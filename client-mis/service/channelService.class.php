<?php
class channelService{
	public static function addChannel($channel){
		
		if(!file_exists(MIS_DATA_PATH."/packageData.php")){
			TbMisView::addMisFeedback("û�а汾�����ļ�");
			return false;
		}
		
		include(MIS_DATA_PATH."/packageData.php");
		
		if(!isset($packageData[$channel['ptype']])){
			TbMisView::addMisFeedback("û��������͵Ŀͻ���");
			return false;
		}
		
		if(!isset($packageData[$channel['ptype']][$channel['pversion']])){
			TbMisView::addMisFeedback("û������汾");
			return false;
		}
		
		if(isset($packageData[$channel['ptype']][$channel['pversion']][$channel['cname']])){
			TbMisView::addMisFeedback("�������ظ�");
			return false;
		}
		$packageData[$channel['ptype']][$channel['pversion']][$channel['cname']]=$channel['cnum'];
		self::_updatePackageData($packageData);
		
		return true;
	}
	public function _updatePackageData($packageData){
        	//�ò�����������data����д��data�ļ�
        	$file = fopen(MIS_DATA_PATH."/packageData.php","w");
        	$strContent = "<?php\n";
        	$strContent .= '$packageData = ';
        	$strContent .= var_export($packageData,true);
        	$strContent .= ";";
        	fwrite($file,$strContent);
        	fclose($file);
        }
}