struct rankInfo
{
uint32_t returncount;//当前返回的数据的个数，为0，未获取到有效的题
rankInfoIdl returnData[];//具体的提交状态信息
};
struct rankInfoIdl
{
string user_name; //用户名称
string code_run_time; //代码运行时间
};
service rank
{
/**
* @brief : 获取排名信息
* @param [in] question_id : uint32_t : 问题id
* @param [in] offset : uint32_t : 偏移量
* @param [in] iCount : uint32_t : 获取排名个数
* @param [in] bIsMidStep : uint32_t : 是否中期排名
* @param [in] pageNo : uint32_t : 当前页号
* @param [out] arrData[] : rankInfo : 输出信息，是一个结构体数组，包括用户名，代码运行时间
**/
void getRankInfo(uint32_t question_id, uint32_t offset, uint32_t iCount, uint32_t bIsMidStep, uint32_t pageNo, out rankInfo arrData[]);

struct commitStatusInfo
{
uint32_t returncount;//当前返回的数据的个数，为0，未获取到有效的题
commitStatusInfoIdl returnData[];//具体的提交状态信息
};
struct commitStatusInfoIdl
{
uint32_t commit_status;//表示提交状态 1：待运行， 2：正在处理，3：编译错误，4：运行错误，5：超时，6：其它   7：运行成功，8：格式错误 9：输出超过限制 10:答案错误
string commit_time;////用户提交代码时间
string commit_error_info;////表示错误的其它信息 
};
/**
* @brief : 获取题目提交状态列表
* @param [in] question_id : uint32_t : 问题id
* @param [in] offset : uint32_t : 偏移量
* @param [in] iCount : uint32_t : 获取排名个数
* @param [in] pageNo : uint32_t : 当前所处的页号
* @param [out] arrData[] : RankInfo : 输出信息，是一个结构体数组，包括用户名，代码运行时间
**/
void getCommitStatus(uint32_t question_id, uint32_t offset, uint32_t iCount, uint32_t pageNo, out commitStatusInfo arrData[]);

struct uploadoutput
{
};
/**
* @brief : 用户提交代码
* @param [in] question_id : uint32_t : 问题id
* @param [in] user_id : uint32_t : 当前答题用户id
* @param [in] code_type : string : 用户提交的代码类型 可选项：c/c++/php 
* @param [out] data[] : uploadoutput : 无
**/
void uploadCode(uint32_t question_id, uint32_t user_id, string code_type,out uploadoutput data[]);


struct codeInfo
{
string code_zip;//用户上传的源码文件
uint32_t commit_id;//用户每次提交生成的数据库表主键值
uint32_t question_id;//问题id
string code_type;//用户提交的代码类型 可选项：c,c++,php 
uint32_t question_max_run_time;//用户代码可运行最长时间
};
/**
* @brief : 评测服务器向数据库获取代码信息
* @param [out] data[] : codeInfo : 用户提交代码相关信息
**/
void getUserCommitCode(out codeInfo data[]);


struct setoutput
{
};
/**
* @brief : 评测服务器向数据写回评测结果
* @param [in] commit_id : uint32_t : 用户每次提交生成的数据库表主键值
* @param [in] commit_status : uint32_t : 表示提交状态 1：待运行， 2：正在处理，3：编译错误，4：运行错误，5：超时，6：其它   7：运行成功，8：格式错误 9：输出超过限制 10:答案错误
* @param [in] code_run_time : uint32_t : //代码运行时间 
* @param [out] data[] : setoutput : 无
**/
void setRunStatus(uint32_t commit_id, uint32_t commit_status, uint32_t code_run_time,out setoutput data[]);

};
