<?php
/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file test.php
 * <AUTHOR>
 * @date 2017/04/07 14:37:50
 * @brief 
 *  
 **/



$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://jiao.service.tieba.otp.baidu.com/api/v1/search/forum?word=les&pn=1&rn=100');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_HEADER, 0);

$arrRet = curl_exec($ch);
var_dump(curl_errno( $ch ));
var_dump(curl_getinfo( $ch ));
curl_close($ch);
var_dump($arrRet);

var_dump(mc_pack_pack2array($arrRet));


/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
