<?php
final class getUserRankAction extends Bingo_Action_Abstract {
	const PORTRAIT_URL = 'http://tb.himg.baidu.com/sys/portrait/item/';
    public function init()
    {
        return true;
    }
	private static function _errRet($errno)
	{
		return array(
				'error_code' => $errno,
				'error_msg' => Tieba_Error::getErrmsg($errno),
		);
	}
    public function execute()
    {
        $game_id = intval(Bingo_Http_Request::get('game_id', ''));  //��Ϸid
        Tieba_Stlog::addNode('uid', Bingo_Http_Request::get('user',0));		
		Tieba_Stlog::addNode('appid', Bingo_Http_Request::get('appid',0));
        if(empty($game_id)||$game_id<=0)
        {
            Bingo_Log::warning("invalid params : game_id:$game_id");
            $ret = self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);     
            print json_encode($ret);
            return  false;
        }       
       
		$dataOut['error_code']=Util_Errcode::SUCCESS;
		$dataOut['error_msg']='success';
      	$dataOut['output']=array();
		$arrInput = array(
            'game_id'=> $game_id,
        );   
    	$bolNew = false;
		//if ($game_id == 141153567120){
			$type = strval(Bingo_Http_Request::get('type', '')); 
			if (!empty($type)){
				if ($type != 'total' && $type != 'day' && $type != 'week'){
					$ret = self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);     
		            print json_encode($ret);
		            return  false;
				}
				$bolNew = true;
				$arrReq = array(
		            'game_id' => $game_id,
		            'type' => $type,
		            'top_size' => 20,
					'keep_user_id' => 1,
		        );				
				$userRankOut = Tieba_Service::call('game','getTopPlayers',$arrReq, null, null, 'post', 'php', 'utf-8');				
				if (false == $userRankOut || $userRankOut['errno'] != 0 || empty($userRankOut['data'])){
					Bingo_Log::warning('service_game getTopPlayers failed!');
		        	print json_encode(self::_errRet(Tieba_Errcode::ERR_DL_DATA));
		        	return false;
				}
				$dataOut['output'] = $this->build($userRankOut['data']);
				Tieba_Stlog::addNode('errno', 0);	
        		print json_encode($dataOut);
        		return true;
			}
		//}
		if (!$bolNew){
        	$userRankOut = Tieba_Service::call('game','getUserRank',$arrInput, null, null, 'post', 'php', 'utf-8');
		}
        if(($userRankOut===false)||(empty($userRankOut)))
        {
            Bingo_Log::warning('service_user getUserRank failed!');
        	print json_encode(self::_errRet(Tieba_Errcode::ERR_UNKOWN));
        	return false;
        } 
        else if($userRankOut['errno']!==0)
        {       
            $dataOut['error_code']=$userRankOut['errno'];
            $dataOut['error_msg']=$userRankOut['errmsg'];
            Bingo_Log::warning('service_user getUserRank failed, errno:'.$userRankOut['errno'].$userRankOut['errmsg']);
            print json_encode($dataOut);
            return false;
        }      
        if(empty($userRankOut['output']))
        {
        	print  json_encode($dataOut);
            return true;
        }
        $arrInfoInput['forum_id'] = array();
        $arrUserInput['user_id'] = array();
        foreach ($userRankOut['output'] as $value){
            $arrTmp = array();           
            $arrTmp['user_id'] = $value['member']['user_id'];
       		$arrTmp['forum_id'] = $value['member']['forum_id'];
            $arrTmp['score'] = $value['score'];
            $dataOut['output'][] = $arrTmp;
            $arrUserInput['user_id'][] = $value['member']['user_id'];
            $arrInfoInput['forum_id'][] = $value['member']['forum_id'];
        } 
        if (!empty($arrInfoInput['forum_id'])){      
	        $getBtInfoOut = Tieba_Service::call('forum','getFnameByFid',$arrInfoInput, null, null, 'post', 'php', 'utf-8');
	        if(($getBtInfoOut['errno']!==0)||(false === $getBtInfoOut)||(empty($getBtInfoOut))){
	            Bingo_Log::warning('service_forum getFnameByFid failed, errno:'.$getBtInfoOut['errno'].$getBtInfoOut['errmsg']);
	           	print json_encode(self::_errRet(Tieba_Errcode::ERR_FORUM_NEW_FNAME_INVALID));
	            return false;
	        }
		    foreach ($dataOut['output'] as &$value){
	            $forum_id = $value['forum_id'];
	            $value['forum_name'] = $getBtInfoOut['forum_name'][$forum_id]['forum_name'];
	        }
        }
        if (!empty($arrUserInput['user_id'])){
        	$mgetUserDataOut = Tieba_Service::call('user','mgetUserDataEx',$arrUserInput, null, null, 'post', 'php', 'utf-8');
            if($mgetUserDataOut===false || $mgetUserDataOut['errno'] !== 0 ){
        		Bingo_Log::warning('service_user mgetUserDataEx failed!'.serialize($mgetUserDataOut));
        		print json_encode(self::_errRet(Tieba_Errcode::ERR_USER_NOT_ONLINE));
        		return false;
        	}
            foreach ($dataOut['output'] as &$value){
				$intUserId = $value['user_id'];
				$value['user_name'] = $mgetUserDataOut['user_info'][$intUserId]['user_name'];
                $user_portrait = self::PORTRAIT_URL.Tieba_Ucrypt::encode($mgetUserDataOut['user_info'][$intUserId]['user_id'],$mgetUserDataOut['user_info'][$intUserId]['user_name']);
                $value['user_portrait'] = $user_portrait;
            }        	
        }
        Tieba_Stlog::addNode('errno', 0);	
        print json_encode($dataOut);
    }
    private function build($arrInput){
    	$arrOut = array();
    	foreach ($arrInput as $arrTmp){
    		$arrItem = array();
    		$arrItem['user_id'] = $arrTmp['user_id'];
    		$arrItem['user_name'] = $arrTmp['user_name'];
    		$arrItem['user_portrait'] = self::PORTRAIT_URL.$arrTmp['portrait'];
    		$arrItem['score'] = $arrTmp['score'];
    		$arrOut[] = $arrItem;
    	}
    	return $arrOut;
    }
}