<?php
/**
 * created by xiaofeng.
 * email: <EMAIL>
 * file name: synAtAction.php
 * create time: 2019-11-05 14:09:37
 * describe:
 */

class synAtAction extends Bingo_Action_Abstract {

    /**
     * init
     * @return boolean
     */
    public function init() {
        return true;
    }

    public function execute() {

        Bingo_Log::warning(__CLASS__.' post all  params:'.print_r(Bingo_Http_Request::getPostAllNoXssSafe(), 1));

        return $this->_handleError(0);
    }


    /**
     * _handleError
     * @param int
     * @param stringsh dev
     * @return array
     */
    private function _handleError($errNum, $errMsg = '', $data = array()){
        $ret = array(
            'error_code' => $errNum,
            'error_msg'  => Tieba_Error::getErrmsg($errNum),
            'data'       => $data,
        );
        if(0 !== strlen($errMsg)){
            $ret['error_msg'] = $errMsg;
        }
        echo json_encode($ret);
        return;
    }

}