<?php
/**
 * Created by PhpStorm.
 * User: yanglei
 * Date: 2018/3/2
 * Time: 下午1:36
 */

define('IS_ORP_RUNTIME', true);
define('MODULE_NAME', 'API');

final class delUserAttrAction extends Bingo_Action_Abstract
{
    public $arrInput = array();

    private static $_primary_key = array(
        'help_mis' => 'tiebaopenApi&@#*%369'
    );

    private static $dealType = array(
        "delavatar" => "亲爱的吧友，您的头像涉嫌违规被管理员重置，请注意不要设置带有违法、低俗色情、联系方式、辱骂攻击等信息的头像！",
        "delsign" => "亲爱的吧友，您的简介涉嫌违规被管理员重置，请注意不要设置带有违法、低俗色情、联系方式、辱骂攻击等信息的简介！",
        "delnickname" => "亲爱的吧友，您的昵称涉嫌违规被管理员重置，请注意不要设置带有违法、低俗色情、联系方式、辱骂攻击等信息的昵称！",
    );

    /**
     * @param
     * @return [type] [description]
     */
    public function init()
    {
        return true;
    }

    public static function deleteHeadphoto($userId)
    {
        $logId = Bd_Log::genLogID();
        ral_set_header("Host: passport.baidu.com");
        ral_set_pathinfo('/sys/delete');
        ral_set_querystring('');

        $arrInput = array('user_id' => $userId);
        $retStr = ral('headphotoedit', 'post', $arrInput, $logId);

        if (intval(ral_get_errno()) !== 0 || $retStr === false) {
            Bingo_Log::warning(__FUNCTION__ . ' call service failed : headphotoedit, ral_get_error = ' . ral_get_error());
            return false;
        }
        $ret = Bingo_String::json2array($retStr);
        if (isset($ret['errno']) && intval($ret['errno']) !== 0) {
            Bingo_Log::warning(__FUNCTION__ . " call service failed : headphotoedit, UserId : $userId, retStr : $retStr");
            return false;
        }
        Bingo_Log::notice(__FUNCTION__ . " call service finished : headphotoedit, UserId : $userId, retStr : $retStr");
        return true;
    }


    /**
     * 重置昵称
     * @param $item
     * @return bool
     */
    public static function resetNickname($uid)
    {
        $arrInput = array(
            'user_id' => $uid,
            'attr_name' => 'user_nickname',
            'attr_value' => '',
        );
        $arrOutput = Tieba_Service::call('user', 'setUserAttr', $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($arrOutput == false || (isset($arrOutput['errno']) && $arrOutput['errno'] > 0)) {
            Bingo_Log::warning("resetNickname, call service setUserAttr failed, uid=$uid"
                . ", req=" . json_encode($arrInput) . ", ret=" . json_encode($arrOutput));
            return false;
        }
        return true;
    }

    /**
     * 执行函数
     * @param null
     * @return null
     */
    public function execute()
    {
        $this->arrInput = Bingo_Http_Request::getGetAll();
        Bingo_Log::notice('execute. input=' . serialize($this->arrInput));

        $user_id = $this->arrInput['user_id'];
        $op_uid = $this->arrInput['op_uid'];
        $call_from = $this->arrInput['call_from'];
        $sign = $this->arrInput['sign'];
        $op = $this->arrInput['op'];

        if (isset(self::$_primary_key[$call_from])) {
            $pk = self::$_primary_key[$call_from];
        }

        if (!$call_from || !$sign || !$pk || !in_array($op, array_keys(self::$dealType))) {
            Bingo_Log::warning('ERR[' . Tieba_Errcode::ERR_NO_RIGHT . '] IN[' . __FUNCTION__ . '] REQ[' . serialize($this->arrInput) . ']');
            return $this->_retJson(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $check_str = md5("user_id=$user_id,op_uid=$op_uid,call_from=$call_from,pk=$pk");
        if ($sign != $check_str) {
            //echo $check_str;exit;
            Bingo_Log::warning("sign err : input=" . json_encode($this->arrInput));
            return self::_retJson(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if ($op == "delavatar") {
            $res = self::deleteHeadphoto($user_id);
        } elseif ($op == "delnickname") {
            $res = self::resetNickname($user_id);
        } elseif ($op == "delsign") {
            $res = self::delUserBrief($user_id);
        }
        // 处置成功发送消息通知
        if ($res) {
//            self::pushMsg($user_id, self::$dealType[$op]);
            self::pushMsgNew(array(
                'user_id'       => $user_id,
                'category_id'   => 3222425470,
                'category_name' => '系统消息',
                'title'         => '',
                'content'       => self::$dealType[$op],
            ));
        }

        $error = 0;
        if (!$res) {
            $error = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
        }
        return self::_retJson($error);
    }

    /**
     * 推送消息
     * @param $uid
     * @return bool
     */
    public static function pushMsg($uid, $content)
    {
        // 给用户发送通知->系统消息
        $msgInput = array(
            'user_id' => 3222425470,
            'to_user_id' => intval($uid),
            'msg_type' => 1,
            'duration' => 0,
            'content' => $content,
            'user_type' => 4,
            'record_id' => -1,
            'is_preview' => 0,
        );

        $ret = Tieba_Service::call('msgpush', 'sendMuticastPersonalMsg', $msgInput, null, null, 'post', 'php', 'utf-8');
        if ($ret == false && $ret['errno'] > 0) {
            Bingo_Log::warning('fail msgpush::sendMuticastPersonalMsg, input=' . serialize($msgInput) . ', ret=' . serialize($ret));
        }
    }

    /**
     * 删除个性签名
     * @param array
     * @return bool
     */
    public static function delUserBrief($uid)
    {
        $arrInput = array(
            'user_id' => intval($uid),
            'op_name' => 'ueg',
        );
        $res = Tieba_Service::call('common', 'delUserBrief', $arrInput);
        if ($res && $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::notice('userBriefPost: common -> delUserBrief ok, input: ' . serialize($arrInput) . ', output: ' . serialize($res));
            return false;
        } else {
            return true;
        }
    }


    /**
     * 返回值内容
     * @param $error
     */
    private static function _retJson($error, $res = '')
    {
        $ret = array(
            'errcode' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error)
        );
        if ($res) {
            $ret["res"] = $res;
        }
        echo Bingo_String::array2json($ret);
        exit;
    }

    /***
     * 调用push发送贴吧app端内消息
     * @param $arrInput
     * @return bool
     */
    private static function pushMsgNew($arrInput)
    {
        Bingo_Log::notice("call ".__METHOD__." input = ".serialize($arrInput));
        if(!isset($arrInput['user_id']) || !intval($arrInput['user_id']))
        {
            Bingo_Log::warning("call ".__METHOD__." param error [user_id not exist or empty]");
            return false;
        }
        if(!isset($arrInput['category_id']) || !intval($arrInput['category_id']))
        {
            Bingo_Log::warning("call ".__METHOD__." param error [category_id not exist or empty]");
            return false;
        }
        if(!isset($arrInput['category_name']) || !$arrInput['category_name'])
        {
            Bingo_Log::warning("call ".__METHOD__." param error [category_name not exist or empty]");
            return false;
        }

        // format param
        $userId       = intval($arrInput['user_id']);
        $categoryId   = intval($arrInput['category_id']); // 3222425470
        $categoryName = trim($arrInput['category_name']); // 系统消息
        $serviceId    = 109; // 分配给ueg使用
        if(isset($arrInput['service_id']) && $arrInput['service_id'])
        {
            $serviceId = strval($arrInput['service_id']);
        }
        $title = '';
        if(isset($arrInput['title']) && $arrInput['title'])
        {
            $title = trim($arrInput['title']);
        }
        $content = '';
        if(isset($arrInput['content']) && $arrInput['content'])
        {
            $content = strval($arrInput['content']);
        }

        // 通过user_id获取发送消息的group_id
        $groupInput  = array(
            'user_ids' => array(intval($userId)),
        );
        $groupOutput = Tieba_Service::call('im', 'queryPlatformGroupByUid', $groupInput, null, null, 'post', 'php', 'utf-8');
        Bingo_Log::notice('call im::queryPlatformGroupByUid, input:[' . serialize($groupInput) . '],output:[' . serialize($groupOutput) . ']');

        //获得group_id失败，不发端内消息
        if (!$groupOutput || $groupOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS || !isset($groupOutput['groups'][0]['group_id']))
        {
            Bingo_Log::warning('call '.__METHOD__.' im::queryPlatformGroupByUid fail, input:[' . serialize($groupInput) . '],output:[' . serialize($groupOutput) . ']');
            return false;
        };
        $intGroupId = intval($groupOutput['groups'][0]['group_id']);
        Bingo_Log::notice("call ".__METHOD__." getGroupIdByUserId [{$userId}] = ".serialize($groupOutput));

        //调用端内消息
        $msgInput = array(
            "task_id"    => "000111",
            "service_id" => strval($serviceId), // 专门ueg使用的service_id
            "title"      => $title,
            "text"       => $content,
            "group_id"   => array(strval($intGroupId)),
            "user_id"    => strval($categoryId),
            "user_name"  => $categoryName,
            "user_type"  => "4",
            "msg_type"   => "1",
        );
        $msgRes = Tieba_Service::call('tieba_push', 'pushMsgLogicForPhp', $msgInput, null, null, 'post', 'json', 'utf-8');
        if ($msgRes === false || (isset($msgRes['errno']) && intval($msgRes['errno']) !== Tieba_Errcode::ERR_SUCCESS))
        {
            Bingo_Log::warning(__FUNCTION__." call service failed : sendSysmsg, input =".serialize($arrInput)." output =".serialize($msgRes));
            return false;
        }

        Bingo_Log::notice("call ".__METHOD__." push success");
        return true;
    }
}