<?php 
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-10-15 11:45:58
 * @version
 */


final class getAttrAction extends Bingo_Action_Abstract
{
    private static $_md5Key = "tb^sjq123.";
    private static $_userstr;
    private static $_sign;
    private static $_attrname;
    private static $_error_code;
    private static $_error_msg;
    
	public function init()
	{
		return true;
	}

	public function execute()
	{

		//��ȡ����
		self::$_userstr = Bingo_Http_Request::get('uid', 0);
		self::$_sign  = strval(Bingo_Http_Request::get('sign', ""));
		self::$_attrname = strval(Bingo_Http_Request::get('attrname', ""));

	    //��������
		if(empty(self::$_userstr) || empty(self::$_attrname) || empty(self::$_sign) ){
			Bingo_Log::warning("invalid params : uids:".self::$_userstr.". attrname:".self::$_attrname.". sign:".self::$_sign);
			self::output(Util_Errcode::ERR_PARAM);
			return;
		}
		/*��װ��openapi��ȥ��signУ�� sigУ��
	    if (empty(self::$_sign) || !self::checksign(self::$_sign,self::$_userstr)){
		    Bingo_Log::warning("invalid params : uids:".self::$_userstr.". attrname:".self::$_attrname.". sign:".self::$_sign);
		    self::output(Util_Errcode::ERR_SIGN);
		    return;
		}*/
		
		$arrUids=explode(',', self::$_userstr);
		$user_infos=self::mgetUserData($arrUids);
	    
		if (isset($user_infos['errno']) || $user_infos['errno'] != Tieba_Errcode::ERR_SUCCESS){
		    self::output(Util_Errcode::ERR_CALL);
		    return;
		}
		        
		
	    $output = array();
		foreach ($user_infos as $uid => $info){
		    if(isset($info[self::$_attrname])){
		        $output[$uid][self::$_attrname] = $info[self::$_attrname];
		    }else{
		        $output[$uid][self::$_attrname] = 0;//Ĭ��Ϊ0���˴����ܻ�������
		    }
		} 
		
		
		
		
		self::output(Util_Errcode::SUCCESS,$output);
		return;
	}
	/*
	 * ��usercenter�л�ȡ�û���Ϣ
	 */
	public static function mgetUserData($arrUids) {
		$arrInput = array(
				'user_id'   => $arrUids,
		);
		Bingo_Timer::start("service_user_mgetUserData");
		$arrOut = Tieba_Service::call('user', 'mgetUserData', $arrInput);
		Bingo_Timer::end("service_user_mgetUserData");
		if (Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']) {
			Bingo_Log::warning('call service user::mgetUserData failed,errno[' . $arrOut['errno'] . '],errmsg[' . $arrOut['errmsg'] . ']');
			return $arrOut;	
		}
		$arrUserInfo = $arrOut['user_info'];
		return $arrUserInfo;
	}
	/*
	 * ��֤ǩ��
	 */
	private static function checksign($sign,$uids) {
	    $str = self::$_md5Key.$uids;
	    $md5str = md5($str);
	    Bingo_Log::warning("input_sign:$sign. check_sign:$md5str str:$str");
	    if ($md5str === $sign){
	        return true;
	    }
	    return false;
	}
	/*
	 * ���ݷ�����־��¼
	 */
	private static function output($error_code,$output = array()){
	    Bingo_Log::pushNotice('uids',self::$_userstr);
	    Bingo_Log::pushNotice('sign',self::$_sign);
	    Bingo_Log::pushNotice('attrname',self::$_attrname);
	    Bingo_Log::pushNotice('error_code',Util_Errcode::SUCCESS);
	    //openƽ̨Ҫ����ȷ������err_code��error_msg
        if ($error_code == Util_Errcode::SUCCESS){
            $ret = array('user_infos'=> $output,);
        }else
        {
	        $ret = array(
	            'error_code'=>$error_code,
	            'error_msg'=>Util_Errcode::$ERR_MSG[$error_code],
	            'user_infos'=> $output,
	        );
        }
	    echo Bingo_String::array2json($ret, Bingo_Encode::ENCODE_UTF8);
	}
}
?>
