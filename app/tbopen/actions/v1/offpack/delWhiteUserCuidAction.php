<?php
/*
 * 
 * @author:he<PERSON><PERSON><PERSON>
 * @time:20180727.
 */
class delWhiteUserCuidAction extends Bingo_Action_Abstract{
    
    private $_strCuid        = '';
    private $_intIsDeleteAll = 0;
    
    public function init(){
   
        $arrRawInput = json_decode(file_get_contents('php://input'), true);
        $strCuid = $arrRawInput['cuid'];
        $intIsDeleteAll = $arrRawInput['delete_all'];
        
        //$strCuid = $_GET['cuid']; //@todo
        if(empty($strCuid) && empty($intIsDeleteAll)){
            Bingo_Log::warning('delWhiteUserCuid,param error!! cuid and delete_all is null,cuid='.$strCuid);
            $this->_jsonRet(array(), Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
            return false;
        }
       
        $this->_strCuid = strval($strCuid);
        $this->_intIsDeleteAll = intval($intIsDeleteAll);
        return true;
    }
    
    public function execute(){
        if($this->_intIsDeleteAll){
            if(!$this->_deleteAllProcess()){
                return false;
            };
        }
        else{
            if(!$this->_process()){
                return false;
            };
        }
        
        return $this->_jsonRet(array(), Tieba_Errcode::ERR_SUCCESS, 'success');
        
    }

    private function _deleteAllProcess(){
        if(!$this->_intIsDeleteAll){
            Bingo_Log::warning(sprintf('_intIsDeleteAll is false, should not call %s', __METHOD__));
            $this->_jsonRet(array(), Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
            return false;
        }

        $arrInput = array(
            'delete_all' => 1,
        );
        $arrOutput = Tieba_Service::call('common', 'deleteAllWhiteList', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(!$this->_checkOutput($arrOutput)){
            Bingo_Log::warning(sprintf('%s call common::deleteAllWhiteList fail! input[%s], output[%s]', 
                __METHOD__, json_encode($arrInput), json_encode($arrOutput))
            );
            return false;
        }

        return true;
    }

    private function _process(){
        $arrInput = array(
            'cuid'        => $this->_strCuid,
        );
        
        //判断cuid白名单是否存在
        $Out = Tieba_Service::call('common', 'getOneWhiteListInfoByCuidAndStatus', $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($Out === false || $Out['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call common::getOneWhiteListInfoByCuidAndStatus failed. input:'.serialize($arrInput).'.output:'.serialize($Out));
            $this->_jsonRet(array(),Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service failed!');
            return false;
        }
        if(empty($Out['data'])){
            Bingo_Log::warning('cuid white list is not exited. input:'.serialize($arrInput).'.output:'.serialize($Out));
            $this->_jsonRet(array(),Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'this cuid white list is not exit!');
            return false;
        }
        $Outret = Tieba_Service::call('common', 'delOneWhiteListByCuid', $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($Outret === false || $Outret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call common::delOneWhiteListByCuid failed. input:'.serialize($arrInput).'.output:'.serialize($Outret));
            $this->_jsonRet(array(),Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service failed!');
            return false;
        }
        
        return true;
    }

    /**
     * 通用返回检查
     */
    private function _checkOutput($arrOutput){
        if(empty($arrOutput) || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS){
            return false;
        }
        return true;
    }
    
    protected function _jsonRet($arrOutput, $intErrno, $strErrmsg){
        $arrRet = array(
            'errno'  => $intErrno,
            'errmsg' => $strErrmsg,
            'data'   => $arrOutput,
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, 'utf-8');
        return true;
    }
    
}