<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>  <EMAIL>
 * @date 2012-3-12
 * @version 
 */
class FloorPbService{
    private static $arrForumDirMap = array(
        
    );
    public static function isClientVersionSupportFloorPb(){
        $strClientVersion = CapiRequest::$strClientVersion;
		if (CapiRequest::$intClientType == CapiDef::CLIENT_TYPE_IPAD) {//ipad不涉及版本，都支持楼中楼
	    	return true;
		}
        if (Molib_Util_Version::compare($strClientVersion, '1.0.0') == 0 || Molib_Util_Version::compare($strClientVersion, '1.0.1') == 0){
            //旧版本永远看不到楼中楼
            return false;
        }        
        return true;
    }
    //是否开启楼中楼
    public static function isOpenFloorPb($strFname,$strForumDir = '',$strForumSecDir = '',$intForumid = 0){        
        if(TbClient::$intFloorAllOpen == 1){
            //楼中楼全流量
            return true;
        }  
        
        // modify by lixin begin
        if (!empty($strForumDir)) {
        	if($strForumDir == '文学艺术' || $strForumDir == '文学'){
        		return true;
        	}
        }
        
        if (!empty($strForumDir) && !empty($strForumSecDir)) {
        	if(($strForumDir == '人文自然' && $strForumSecDir == '艺术') ||
              ($strForumDir == '当代人物' && $strForumSecDir == '当代艺术人物')){
        		return true;
        	}
        }
        // modify by lixin end
        
        //首先判断目录
        if (!empty($strForumDir)){
            if (Bingo_Array::in_array(strval($strForumDir), TbClient::$arrFloorOpenDir)){
                return true;
            }
        }
        //再判断吧名
        $strFile = CSAPI_SERVER_DATA_PATH .'/forum/open_floor_forum_data.php';
        $arrForumData = array();
        include($strFile);
        if (isset($arrForumData[$strFname])){
            return true;            
        }
        
        //再根据吧名取目录
        if (isset(self::$arrForumDirMap[$strFname])){
            $strForumDir = self::$arrForumDirMap[$strFname];      
        }else{
        	$strFnameGBK = Bingo_Encode::convert($strFname,Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
            if ($intForumid == 0){
                $intForumid = self::getForumIdByName($strFnameGBK);
            }    	   	
    	   	if (empty($intForumid)){
    	   	    return false;
    	   	}
    	    $arrForumDir = self::getForumArrDir($intForumid,$strFnameGBK);
    	    if (empty($arrForumDir[0])){
    	        return false;
    	    }    	    
    	    $strFirstDir = $arrForumDir[0];
    	    $strSecondDir = '';
    	    $strFirstDir = Bingo_Encode::convert($strFirstDir,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
    	    if (isset($arrForumDir[1]) && !empty($arrForumDir[1])){
    	    	$strSecondDir = $arrForumDir[1];
    	    	$strSecondDir = Bingo_Encode::convert($strSecondDir,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
    	    } 
        }
        
        if (!empty($strFirstDir)) {
        	if($strFirstDir == '文学艺术' || $strFirstDir == '文学'){
        		return true;
        	}
        }
        
        if (!empty($strFirstDir) && !empty($strSecondDir)) {
        	if(($strFirstDir == '人文自然' && $strSecondDir == '艺术') ||
              ($strFirstDir == '当代人物' && $strSecondDir == '当代艺术人物')){
        		return true;
        	}
        }
        if (Bingo_Array::in_array(strval($strForumDir), TbClient::$arrFloorOpenDir)){
            return true;
        }             
        return false;
    }
	public static function getForumIdByName($strForumName){
		if(!is_string($strForumName)){ //输入不合法
			return false;
		}
		$arrRet = Rpc_Fgate::getFidByFnames(array($strForumName));
		if(1 == $arrRet[0]['has_forum_id']){
			return $arrRet[0]['forum_res']['forumid'];
		}else{
		    Bingo_Log::warning('get fid from fage error'.$strForumName);
			return false;
		}
	}
	public static function getForumIndex($intForumId, $strForumName){
	    $intForumId = intval($intForumId);
	    $strForumName = strval($strForumName);
		$ret = Rpc_Fdir::getDirInfo($intForumId, $strForumName);
		if(!$ret || $ret['level_1_name'] == ""){
			return '';
		}else{
			return $ret['level_1_name'];
		}
	}    
	public static function getForumArrDir($intForumId, $strForumName){
	    $intForumId = intval($intForumId);
	    $strForumName = strval($strForumName);
		$ret = Rpc_Fdir::getDirInfo($intForumId, $strForumName);
		if(!$ret || $ret['level_1_name'] == ""){
			return '';
		}else{
			$arrDir = array();
			$arrDir[0] = $ret['level_1_name'];
			if (!empty($ret['level_2_name'])){
				$arrDir[1] = $ret['level_2_name'];
			}
			return $arrDir;
		}
	}    
}
