<?php

class Zeus_Vote {
    public static function getVoteInfo($arrInput){
        $strVoteCrypt = $arrInput['vote_crypt'];
        $strUrl = '/vote/data/detail_vote/'.$strVoteCrypt.'?alt=json';
        $rpc = new Zeus_Tiebahttp('vote_info');
        $strOut = $rpc->call($strUrl,CapiRequest::$cookie);
        if ($strOut === false){
            Bingo_Log::warning('call vote info error');
            return false;
        }
        $arrOut = Bingo_String::json2array($strOut);
        if ($arrOut === false){
            Bingo_Log::warning('json to array error');
            return false;
        }
        return $arrOut;        
    }
    
    public static function getCanVote($arrInput){
        $intFid = $arrInput['fid'];
        $strFname = $arrInput['kw'];
        $strForumPower = $arrInput['forum_power'];
        $strVoteCrypt = $arrInput['vote_crypt'];
        $strVoteSign = $arrInput['vote_sign'];
        
        $strUrl = "/f?ct=486539264&cm=59300&lm={$intFid}&word={$strFname}&tn=ajaxCanVote&rs11={$strForumPower}&str1={$strVoteCrypt}&str2={$strVoteSign}";
        $rpc = new Zeus_Tiebahttp('can_vote_info');
        $strOut = $rpc->call($strUrl,CapiRequest::$cookie);
        if ($strOut === false){
            Bingo_Log::warning('call can vote info error');
            return false;
        }
        $arrOut = Bingo_String::json2array($strOut);
        if ($arrOut === false){
            Bingo_Log::warning('json to array error');
            return false;
        }
        return $arrOut;           
    }
    

}