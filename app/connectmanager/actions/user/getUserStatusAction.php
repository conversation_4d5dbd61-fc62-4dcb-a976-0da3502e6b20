<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-11-24 13:46:34
 * @comment 后端业务同LCS交互ui
 * @version
 */
require_once 'pb_proto_getUserStatusReqIdl.php';
require_once 'pb_proto_getUserStatusResIdl.php';

class getUserStatusAction extends Util_Base{
	
	protected function _process(){
		//校验appid是否符合标准，目前还没有做
		$appid = $this->arrInput['appid'];
		if( !$appid ){
			Bingo_Log::warning("appid must not be null");
			$this->_error(Tieba_Errcode::ERR_INPUT_PARAM);
			return false;
		}

		//校验uid数据是否为空
		if(!isset($this->arrInput['uids']) || !is_array($this->arrInput['uids']) || empty($this->arrInput['uids'])){
			Bingo_Log::warning("getUserStatus input params is wrong. arrInput[".serialize($this->arrInput)."]");
			$this->_error(Tieba_Errcode::ERR_INPUT_PARAM, "uid must not be empty");
			return false;
		}
		$arrUids = $this->arrInput['uids'];
		$arrUids = array_unique($arrUids);

		$arrReq = array(
			'uids' => $arrUids,
			'appid' => $this->arrInput['appid'],
			'connection_id' => $this->arrInput['connection_id'],
			'device_types' => array(0,),
		);

		//调用后端service
		$arrRes = Tieba_Service::call('connectmanager', 'getUserStatus', $arrReq);
		if( !$arrRes || !isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning("call service getUserStatus failed. req[".serialize($arrReq)."] res[".serialize($arrRes)."]");
			if(isset($arrRes['errno'])){
				$this->_error($arrRes['errno']);
			}
			else{
				$this->_error(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
			}
			return false;
		}

		$this->arrData['errno'] = $arrRes['errno'];
		$this->arrData['result'] = $arrRes['result'];

		return true;
	}

	protected function _loadData($rawInput){	
		//获取php的解析protobuf类
		try{
			$userStatusReq = new GetUserStatusReqIdl();
			$userStatusReq->parseFromString($rawInput);
		} catch(Exception $ex) {
			$this->_error(Tieba_Errcode::ERR_INPUT_PARAM); //设置错误类型
			return false;
		}
		$this->arrInput  = array(
			'appid' => $userStatusReq->getAppid(),
			'uids' => $userStatusReq->getUids(),
			'connection_id' => $userStatusReq->getConnectionId(),
		);


	
		/*
		$this->arrInput = array(
			'appid' => "tieba",
			'uids' => array(1,2),
			'connection_id' => 'aa',
		);
		*/

		return true;
	}

	protected function _buildData(){
		//return json_encode($this->arrData);
		$outProto = new GetUserStatusResIdl();
		foreach($this->arrData['result'] as $uid => $arrChannels){
			$user = new User();
			$userStatus = new UserStatus();
			$user->setUid(intval($uid));
			foreach($arrChannels as $arrChannel){
				$channel = new Channel();
				$channel->setUid(intval($arrChannel['uid']));
				$channel->setDeviceType(intval($arrChannel['device_type']));
				$channel->setChannelId(strval($arrChannel['channel_id']));
				$channel->setStatus(intval($arrChannel['status']));
				$channel->setConnectionId(strval($arrChannel['connection_id']));
				$user->appendChannels($channel);
			}
			$userStatus->appendUsers($user);
			$outProto->appendData($userStatus);
		}
		$outProto->setErrNo(intval($this->arrData['errno']));
		return $outProto->serializeToString();
	}

	protected function _buildError(){
		$outProto = new GetUserStatusResIdl();
		$outProto->setErrNo(intval($this->arrError['errno']));
		$outProto->appendData($this->_buildErrUser);
		return $outProto->serializeToString();
	}

	/**
	 * build user error for error response in proto
	 */
	private function _buildErrUser(){
		$user = new User();
		$userStatus = new UserStatus();
		$user->setUid(intval(0));
		$channel = new Channel();
		$channel->setUid(0);
		$channel->setDeviceType(0);
		$channel->setChannelId("NULL");
		$channel->setStatus(0);
		$channel->setConnectionId("NULL");
		$user->appendChannels($channel);
		$userStatus->appendUsers($user);
		return $userStatus;
	}
}

?>
