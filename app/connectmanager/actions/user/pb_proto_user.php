<?php
/**
 * Auto generated from user.proto at 2014-12-16 13:54:08
 */

/**
 * User message
 */
class User extends ProtobufMessage
{
    /* Field index constants */
    const UID = 1;
    const CHANNELS = 2;

    /* @var array Field descriptors */
    protected static $fields = array(
        self::UID => array(
            'name' => 'uid',
            'required' => true,
            'type' => 5,
        ),
        self::CHANNELS => array(
            'name' => 'channels',
            'repeated' => true,
            'type' => 'Channel'
        ),
    );

    /**
     * Constructs new message container and clears its internal state
     *
     * @return null
     */
    public function __construct()
    {
        $this->reset();
    }

    /**
     * Clears message values and sets default ones
     *
     * @return null
     */
    public function reset()
    {
        $this->values[self::UID] = null;
        $this->values[self::CHANNELS] = array();
    }

    /**
     * Returns field descriptors
     *
     * @return array
     */
    public function fields()
    {
        return self::$fields;
    }

    /**
     * Sets value of 'uid' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setUid($value)
    {
        return $this->set(self::UID, $value);
    }

    /**
     * Returns value of 'uid' property
     *
     * @return int
     */
    public function getUid()
    {
        return $this->get(self::UID);
    }

    /**
     * Appends value to 'channels' list
     *
     * @param Channel $value Value to append
     *
     * @return null
     */
    public function appendChannels(Channel $value)
    {
        return $this->append(self::CHANNELS, $value);
    }

    /**
     * Clears 'channels' list
     *
     * @return null
     */
    public function clearChannels()
    {
        return $this->clear(self::CHANNELS);
    }

    /**
     * Returns 'channels' list
     *
     * @return Channel[]
     */
    public function getChannels()
    {
        return $this->get(self::CHANNELS);
    }

    /**
     * Returns 'channels' iterator
     *
     * @return ArrayIterator
     */
    public function getChannelsIterator()
    {
        return new ArrayIterator($this->get(self::CHANNELS));
    }

    /**
     * Returns element from 'channels' list at given offset
     *
     * @param int $offset Position in list
     *
     * @return Channel
     */
    public function getChannelsAt($offset)
    {
        return $this->get(self::CHANNELS, $offset);
    }

    /**
     * Returns count of 'channels' list
     *
     * @return int
     */
    public function getChannelsCount()
    {
        return $this->count(self::CHANNELS);
    }
}
require_once 'pb_proto_channel.php';