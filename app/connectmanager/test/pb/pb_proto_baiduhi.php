<?php
/**
 * Auto generated from baiduhi.proto at 2014-12-22 17:40:58
 */

/**
 * GeneralContent message
 */
class GeneralContent extends ProtobufMessage
{
    /* Field index constants */
    const APPID = 1;
    const CHANNEL_ID = 2;
    const IP = 3;
    const CUID = 4;
    const PORT = 5;
    const LOG_ID = 6;
    const CMD = 7;
    const UID = 8;
    const DEVICE_TYPE = 9;
    const CONNECTION_ID = 10;

    /* @var array Field descriptors */
    protected static $fields = array(
        self::APPID => array(
            'name' => 'appid',
            'required' => true,
            'type' => 7,
        ),
        self::CHANNEL_ID => array(
            'name' => 'channel_id',
            'required' => true,
            'type' => 7,
        ),
        self::IP => array(
            'name' => 'ip',
            'required' => true,
            'type' => 7,
        ),
        self::CUID => array(
            'name' => 'cuid',
            'required' => true,
            'type' => 7,
        ),
        self::PORT => array(
            'name' => 'port',
            'required' => true,
            'type' => 5,
        ),
        self::LOG_ID => array(
            'name' => 'log_id',
            'required' => true,
            'type' => 5,
        ),
        self::CMD => array(
            'name' => 'cmd',
            'required' => true,
            'type' => 5,
        ),
        self::UID => array(
            'name' => 'uid',
            'required' => true,
            'type' => 5,
        ),
        self::DEVICE_TYPE => array(
            'name' => 'device_type',
            'required' => true,
            'type' => 5,
        ),
        self::CONNECTION_ID => array(
            'name' => 'connection_id',
            'required' => true,
            'type' => 7,
        ),
    );

    /**
     * Constructs new message container and clears its internal state
     *
     * @return null
     */
    public function __construct()
    {
        $this->reset();
    }

    /**
     * Clears message values and sets default ones
     *
     * @return null
     */
    public function reset()
    {
        $this->values[self::APPID] = null;
        $this->values[self::CHANNEL_ID] = null;
        $this->values[self::IP] = null;
        $this->values[self::CUID] = null;
        $this->values[self::PORT] = null;
        $this->values[self::LOG_ID] = null;
        $this->values[self::CMD] = null;
        $this->values[self::UID] = null;
        $this->values[self::DEVICE_TYPE] = null;
        $this->values[self::CONNECTION_ID] = null;
    }

    /**
     * Returns field descriptors
     *
     * @return array
     */
    public function fields()
    {
        return self::$fields;
    }

    /**
     * Sets value of 'appid' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setAppid($value)
    {
        return $this->set(self::APPID, $value);
    }

    /**
     * Returns value of 'appid' property
     *
     * @return string
     */
    public function getAppid()
    {
        return $this->get(self::APPID);
    }

    /**
     * Sets value of 'channel_id' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setChannelId($value)
    {
        return $this->set(self::CHANNEL_ID, $value);
    }

    /**
     * Returns value of 'channel_id' property
     *
     * @return string
     */
    public function getChannelId()
    {
        return $this->get(self::CHANNEL_ID);
    }

    /**
     * Sets value of 'ip' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setIp($value)
    {
        return $this->set(self::IP, $value);
    }

    /**
     * Returns value of 'ip' property
     *
     * @return string
     */
    public function getIp()
    {
        return $this->get(self::IP);
    }

    /**
     * Sets value of 'cuid' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setCuid($value)
    {
        return $this->set(self::CUID, $value);
    }

    /**
     * Returns value of 'cuid' property
     *
     * @return string
     */
    public function getCuid()
    {
        return $this->get(self::CUID);
    }

    /**
     * Sets value of 'port' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setPort($value)
    {
        return $this->set(self::PORT, $value);
    }

    /**
     * Returns value of 'port' property
     *
     * @return int
     */
    public function getPort()
    {
        return $this->get(self::PORT);
    }

    /**
     * Sets value of 'log_id' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setLogId($value)
    {
        return $this->set(self::LOG_ID, $value);
    }

    /**
     * Returns value of 'log_id' property
     *
     * @return int
     */
    public function getLogId()
    {
        return $this->get(self::LOG_ID);
    }

    /**
     * Sets value of 'cmd' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setCmd($value)
    {
        return $this->set(self::CMD, $value);
    }

    /**
     * Returns value of 'cmd' property
     *
     * @return int
     */
    public function getCmd()
    {
        return $this->get(self::CMD);
    }

    /**
     * Sets value of 'uid' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setUid($value)
    {
        return $this->set(self::UID, $value);
    }

    /**
     * Returns value of 'uid' property
     *
     * @return int
     */
    public function getUid()
    {
        return $this->get(self::UID);
    }

    /**
     * Sets value of 'device_type' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setDeviceType($value)
    {
        return $this->set(self::DEVICE_TYPE, $value);
    }

    /**
     * Returns value of 'device_type' property
     *
     * @return int
     */
    public function getDeviceType()
    {
        return $this->get(self::DEVICE_TYPE);
    }

    /**
     * Sets value of 'connection_id' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setConnectionId($value)
    {
        return $this->set(self::CONNECTION_ID, $value);
    }

    /**
     * Returns value of 'connection_id' property
     *
     * @return string
     */
    public function getConnectionId()
    {
        return $this->get(self::CONNECTION_ID);
    }
}

/**
 * SendMessageBackendReq message
 */
class SendMessageBackendReq extends ProtobufMessage
{
    /* Field index constants */
    const GENERAL = 1;
    const PAYLOAD = 2;

    /* @var array Field descriptors */
    protected static $fields = array(
        self::GENERAL => array(
            'name' => 'general',
            'required' => true,
            'type' => 'GeneralContent'
        ),
        self::PAYLOAD => array(
            'name' => 'payload',
            'required' => true,
            'type' => 7,
        ),
    );

    /**
     * Constructs new message container and clears its internal state
     *
     * @return null
     */
    public function __construct()
    {
        $this->reset();
    }

    /**
     * Clears message values and sets default ones
     *
     * @return null
     */
    public function reset()
    {
        $this->values[self::GENERAL] = null;
        $this->values[self::PAYLOAD] = null;
    }

    /**
     * Returns field descriptors
     *
     * @return array
     */
    public function fields()
    {
        return self::$fields;
    }

    /**
     * Sets value of 'general' property
     *
     * @param GeneralContent $value Property value
     *
     * @return null
     */
    public function setGeneral(GeneralContent $value)
    {
        return $this->set(self::GENERAL, $value);
    }

    /**
     * Returns value of 'general' property
     *
     * @return GeneralContent
     */
    public function getGeneral()
    {
        return $this->get(self::GENERAL);
    }

    /**
     * Sets value of 'payload' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setPayload($value)
    {
        return $this->set(self::PAYLOAD, $value);
    }

    /**
     * Returns value of 'payload' property
     *
     * @return string
     */
    public function getPayload()
    {
        return $this->get(self::PAYLOAD);
    }
}

/**
 * SendMessageBackendResp message
 */
class SendMessageBackendResp extends ProtobufMessage
{
    /* Field index constants */
    const PAYLOAD = 1;

    /* @var array Field descriptors */
    protected static $fields = array(
        self::PAYLOAD => array(
            'name' => 'payload',
            'required' => true,
            'type' => 7,
        ),
    );

    /**
     * Constructs new message container and clears its internal state
     *
     * @return null
     */
    public function __construct()
    {
        $this->reset();
    }

    /**
     * Clears message values and sets default ones
     *
     * @return null
     */
    public function reset()
    {
        $this->values[self::PAYLOAD] = null;
    }

    /**
     * Returns field descriptors
     *
     * @return array
     */
    public function fields()
    {
        return self::$fields;
    }

    /**
     * Sets value of 'payload' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setPayload($value)
    {
        return $this->set(self::PAYLOAD, $value);
    }

    /**
     * Returns value of 'payload' property
     *
     * @return string
     */
    public function getPayload()
    {
        return $this->get(self::PAYLOAD);
    }
}

/**
 * PushMsgToChannelReq message
 */
class PushMsgToChannelReq extends ProtobufMessage
{
    /* Field index constants */
    const CHANNEL_IDS = 1;
    const MSG = 2;
    const APPID = 3;
    const TIMEOUT = 4;
    const LOG_ID = 5;

    /* @var array Field descriptors */
    protected static $fields = array(
        self::CHANNEL_IDS => array(
            'name' => 'channel_ids',
            'repeated' => true,
            'type' => 7,
        ),
        self::MSG => array(
            'name' => 'msg',
            'required' => true,
            'type' => 7,
        ),
        self::APPID => array(
            'name' => 'appid',
            'required' => true,
            'type' => 7,
        ),
        self::TIMEOUT => array(
            'name' => 'timeout',
            'required' => true,
            'type' => 5,
        ),
        self::LOG_ID => array(
            'name' => 'log_id',
            'required' => false,
            'type' => 5,
        ),
    );

    /**
     * Constructs new message container and clears its internal state
     *
     * @return null
     */
    public function __construct()
    {
        $this->reset();
    }

    /**
     * Clears message values and sets default ones
     *
     * @return null
     */
    public function reset()
    {
        $this->values[self::CHANNEL_IDS] = array();
        $this->values[self::MSG] = null;
        $this->values[self::APPID] = null;
        $this->values[self::TIMEOUT] = null;
        $this->values[self::LOG_ID] = null;
    }

    /**
     * Returns field descriptors
     *
     * @return array
     */
    public function fields()
    {
        return self::$fields;
    }

    /**
     * Appends value to 'channel_ids' list
     *
     * @param string $value Value to append
     *
     * @return null
     */
    public function appendChannelIds($value)
    {
        return $this->append(self::CHANNEL_IDS, $value);
    }

    /**
     * Clears 'channel_ids' list
     *
     * @return null
     */
    public function clearChannelIds()
    {
        return $this->clear(self::CHANNEL_IDS);
    }

    /**
     * Returns 'channel_ids' list
     *
     * @return string[]
     */
    public function getChannelIds()
    {
        return $this->get(self::CHANNEL_IDS);
    }

    /**
     * Returns 'channel_ids' iterator
     *
     * @return ArrayIterator
     */
    public function getChannelIdsIterator()
    {
        return new ArrayIterator($this->get(self::CHANNEL_IDS));
    }

    /**
     * Returns element from 'channel_ids' list at given offset
     *
     * @param int $offset Position in list
     *
     * @return string
     */
    public function getChannelIdsAt($offset)
    {
        return $this->get(self::CHANNEL_IDS, $offset);
    }

    /**
     * Returns count of 'channel_ids' list
     *
     * @return int
     */
    public function getChannelIdsCount()
    {
        return $this->count(self::CHANNEL_IDS);
    }

    /**
     * Sets value of 'msg' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setMsg($value)
    {
        return $this->set(self::MSG, $value);
    }

    /**
     * Returns value of 'msg' property
     *
     * @return string
     */
    public function getMsg()
    {
        return $this->get(self::MSG);
    }

    /**
     * Sets value of 'appid' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setAppid($value)
    {
        return $this->set(self::APPID, $value);
    }

    /**
     * Returns value of 'appid' property
     *
     * @return string
     */
    public function getAppid()
    {
        return $this->get(self::APPID);
    }

    /**
     * Sets value of 'timeout' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setTimeout($value)
    {
        return $this->set(self::TIMEOUT, $value);
    }

    /**
     * Returns value of 'timeout' property
     *
     * @return int
     */
    public function getTimeout()
    {
        return $this->get(self::TIMEOUT);
    }

    /**
     * Sets value of 'log_id' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setLogId($value)
    {
        return $this->set(self::LOG_ID, $value);
    }

    /**
     * Returns value of 'log_id' property
     *
     * @return int
     */
    public function getLogId()
    {
        return $this->get(self::LOG_ID);
    }
}

/**
 * PushMsgToChannelResp message
 */
class PushMsgToChannelResp extends ProtobufMessage
{
    /* Field index constants */
    const ERR_NO = 1;
    const LOG_ID = 2;
    const OFFLINE_CHANNEL_IDS = 3;

    /* @var array Field descriptors */
    protected static $fields = array(
        self::ERR_NO => array(
            'name' => 'err_no',
            'required' => true,
            'type' => 5,
        ),
        self::LOG_ID => array(
            'name' => 'log_id',
            'required' => true,
            'type' => 5,
        ),
        self::OFFLINE_CHANNEL_IDS => array(
            'name' => 'offline_channel_ids',
            'repeated' => true,
            'type' => 7,
        ),
    );

    /**
     * Constructs new message container and clears its internal state
     *
     * @return null
     */
    public function __construct()
    {
        $this->reset();
    }

    /**
     * Clears message values and sets default ones
     *
     * @return null
     */
    public function reset()
    {
        $this->values[self::ERR_NO] = null;
        $this->values[self::LOG_ID] = null;
        $this->values[self::OFFLINE_CHANNEL_IDS] = array();
    }

    /**
     * Returns field descriptors
     *
     * @return array
     */
    public function fields()
    {
        return self::$fields;
    }

    /**
     * Sets value of 'err_no' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setErrNo($value)
    {
        return $this->set(self::ERR_NO, $value);
    }

    /**
     * Returns value of 'err_no' property
     *
     * @return int
     */
    public function getErrNo()
    {
        return $this->get(self::ERR_NO);
    }

    /**
     * Sets value of 'log_id' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setLogId($value)
    {
        return $this->set(self::LOG_ID, $value);
    }

    /**
     * Returns value of 'log_id' property
     *
     * @return int
     */
    public function getLogId()
    {
        return $this->get(self::LOG_ID);
    }

    /**
     * Appends value to 'offline_channel_ids' list
     *
     * @param string $value Value to append
     *
     * @return null
     */
    public function appendOfflineChannelIds($value)
    {
        return $this->append(self::OFFLINE_CHANNEL_IDS, $value);
    }

    /**
     * Clears 'offline_channel_ids' list
     *
     * @return null
     */
    public function clearOfflineChannelIds()
    {
        return $this->clear(self::OFFLINE_CHANNEL_IDS);
    }

    /**
     * Returns 'offline_channel_ids' list
     *
     * @return string[]
     */
    public function getOfflineChannelIds()
    {
        return $this->get(self::OFFLINE_CHANNEL_IDS);
    }

    /**
     * Returns 'offline_channel_ids' iterator
     *
     * @return ArrayIterator
     */
    public function getOfflineChannelIdsIterator()
    {
        return new ArrayIterator($this->get(self::OFFLINE_CHANNEL_IDS));
    }

    /**
     * Returns element from 'offline_channel_ids' list at given offset
     *
     * @param int $offset Position in list
     *
     * @return string
     */
    public function getOfflineChannelIdsAt($offset)
    {
        return $this->get(self::OFFLINE_CHANNEL_IDS, $offset);
    }

    /**
     * Returns count of 'offline_channel_ids' list
     *
     * @return int
     */
    public function getOfflineChannelIdsCount()
    {
        return $this->count(self::OFFLINE_CHANNEL_IDS);
    }
}

/**
 * BroadcastMsgReq message
 */
class BroadcastMsgReq extends ProtobufMessage
{
    /* Field index constants */
    const DEVICE_TYPES = 1;
    const MSG = 2;
    const APPID = 3;
    const TIMEOUT = 4;
    const LOG_ID = 5;
    const CONNECTION_ID = 6;

    /* @var array Field descriptors */
    protected static $fields = array(
        self::DEVICE_TYPES => array(
            'name' => 'device_types',
            'repeated' => true,
            'type' => 5,
        ),
        self::MSG => array(
            'name' => 'msg',
            'required' => true,
            'type' => 7,
        ),
        self::APPID => array(
            'name' => 'appid',
            'required' => true,
            'type' => 7,
        ),
        self::TIMEOUT => array(
            'name' => 'timeout',
            'required' => true,
            'type' => 5,
        ),
        self::LOG_ID => array(
            'name' => 'log_id',
            'required' => false,
            'type' => 5,
        ),
        self::CONNECTION_ID => array(
            'name' => 'connection_id',
            'required' => false,
            'type' => 7,
        ),
    );

    /**
     * Constructs new message container and clears its internal state
     *
     * @return null
     */
    public function __construct()
    {
        $this->reset();
    }

    /**
     * Clears message values and sets default ones
     *
     * @return null
     */
    public function reset()
    {
        $this->values[self::DEVICE_TYPES] = array();
        $this->values[self::MSG] = null;
        $this->values[self::APPID] = null;
        $this->values[self::TIMEOUT] = null;
        $this->values[self::LOG_ID] = null;
        $this->values[self::CONNECTION_ID] = null;
    }

    /**
     * Returns field descriptors
     *
     * @return array
     */
    public function fields()
    {
        return self::$fields;
    }

    /**
     * Appends value to 'device_types' list
     *
     * @param int $value Value to append
     *
     * @return null
     */
    public function appendDeviceTypes($value)
    {
        return $this->append(self::DEVICE_TYPES, $value);
    }

    /**
     * Clears 'device_types' list
     *
     * @return null
     */
    public function clearDeviceTypes()
    {
        return $this->clear(self::DEVICE_TYPES);
    }

    /**
     * Returns 'device_types' list
     *
     * @return int[]
     */
    public function getDeviceTypes()
    {
        return $this->get(self::DEVICE_TYPES);
    }

    /**
     * Returns 'device_types' iterator
     *
     * @return ArrayIterator
     */
    public function getDeviceTypesIterator()
    {
        return new ArrayIterator($this->get(self::DEVICE_TYPES));
    }

    /**
     * Returns element from 'device_types' list at given offset
     *
     * @param int $offset Position in list
     *
     * @return int
     */
    public function getDeviceTypesAt($offset)
    {
        return $this->get(self::DEVICE_TYPES, $offset);
    }

    /**
     * Returns count of 'device_types' list
     *
     * @return int
     */
    public function getDeviceTypesCount()
    {
        return $this->count(self::DEVICE_TYPES);
    }

    /**
     * Sets value of 'msg' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setMsg($value)
    {
        return $this->set(self::MSG, $value);
    }

    /**
     * Returns value of 'msg' property
     *
     * @return string
     */
    public function getMsg()
    {
        return $this->get(self::MSG);
    }

    /**
     * Sets value of 'appid' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setAppid($value)
    {
        return $this->set(self::APPID, $value);
    }

    /**
     * Returns value of 'appid' property
     *
     * @return string
     */
    public function getAppid()
    {
        return $this->get(self::APPID);
    }

    /**
     * Sets value of 'timeout' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setTimeout($value)
    {
        return $this->set(self::TIMEOUT, $value);
    }

    /**
     * Returns value of 'timeout' property
     *
     * @return int
     */
    public function getTimeout()
    {
        return $this->get(self::TIMEOUT);
    }

    /**
     * Sets value of 'log_id' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setLogId($value)
    {
        return $this->set(self::LOG_ID, $value);
    }

    /**
     * Returns value of 'log_id' property
     *
     * @return int
     */
    public function getLogId()
    {
        return $this->get(self::LOG_ID);
    }

    /**
     * Sets value of 'connection_id' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setConnectionId($value)
    {
        return $this->set(self::CONNECTION_ID, $value);
    }

    /**
     * Returns value of 'connection_id' property
     *
     * @return string
     */
    public function getConnectionId()
    {
        return $this->get(self::CONNECTION_ID);
    }
}

/**
 * BroadcastMsgResp message
 */
class BroadcastMsgResp extends ProtobufMessage
{
    /* Field index constants */
    const ERR_NO = 1;
    const LOG_ID = 2;

    /* @var array Field descriptors */
    protected static $fields = array(
        self::ERR_NO => array(
            'name' => 'err_no',
            'required' => true,
            'type' => 5,
        ),
        self::LOG_ID => array(
            'name' => 'log_id',
            'required' => true,
            'type' => 5,
        ),
    );

    /**
     * Constructs new message container and clears its internal state
     *
     * @return null
     */
    public function __construct()
    {
        $this->reset();
    }

    /**
     * Clears message values and sets default ones
     *
     * @return null
     */
    public function reset()
    {
        $this->values[self::ERR_NO] = null;
        $this->values[self::LOG_ID] = null;
    }

    /**
     * Returns field descriptors
     *
     * @return array
     */
    public function fields()
    {
        return self::$fields;
    }

    /**
     * Sets value of 'err_no' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setErrNo($value)
    {
        return $this->set(self::ERR_NO, $value);
    }

    /**
     * Returns value of 'err_no' property
     *
     * @return int
     */
    public function getErrNo()
    {
        return $this->get(self::ERR_NO);
    }

    /**
     * Sets value of 'log_id' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setLogId($value)
    {
        return $this->set(self::LOG_ID, $value);
    }

    /**
     * Returns value of 'log_id' property
     *
     * @return int
     */
    public function getLogId()
    {
        return $this->get(self::LOG_ID);
    }
}

/**
 * LoginReq message
 */
class LoginReq extends ProtobufMessage
{
    /* Field index constants */
    const GENERAL = 1;
    const APPID = 2;
    const CHANNEL_ID = 3;
    const UID = 4;
    const PASSPORT = 5;
    const DEVICE_TYPE = 6;

    /* @var array Field descriptors */
    protected static $fields = array(
        self::GENERAL => array(
            'name' => 'general',
            'required' => true,
            'type' => 'GeneralContent'
        ),
        self::APPID => array(
            'name' => 'appid',
            'required' => true,
            'type' => 7,
        ),
        self::CHANNEL_ID => array(
            'name' => 'channel_id',
            'required' => true,
            'type' => 7,
        ),
        self::UID => array(
            'name' => 'uid',
            'required' => true,
            'type' => 5,
        ),
        self::PASSPORT => array(
            'name' => 'passport',
            'required' => true,
            'type' => 7,
        ),
        self::DEVICE_TYPE => array(
            'name' => 'device_type',
            'required' => true,
            'type' => 5,
        ),
    );

    /**
     * Constructs new message container and clears its internal state
     *
     * @return null
     */
    public function __construct()
    {
        $this->reset();
    }

    /**
     * Clears message values and sets default ones
     *
     * @return null
     */
    public function reset()
    {
        $this->values[self::GENERAL] = null;
        $this->values[self::APPID] = null;
        $this->values[self::CHANNEL_ID] = null;
        $this->values[self::UID] = null;
        $this->values[self::PASSPORT] = null;
        $this->values[self::DEVICE_TYPE] = null;
    }

    /**
     * Returns field descriptors
     *
     * @return array
     */
    public function fields()
    {
        return self::$fields;
    }

    /**
     * Sets value of 'general' property
     *
     * @param GeneralContent $value Property value
     *
     * @return null
     */
    public function setGeneral(GeneralContent $value)
    {
        return $this->set(self::GENERAL, $value);
    }

    /**
     * Returns value of 'general' property
     *
     * @return GeneralContent
     */
    public function getGeneral()
    {
        return $this->get(self::GENERAL);
    }

    /**
     * Sets value of 'appid' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setAppid($value)
    {
        return $this->set(self::APPID, $value);
    }

    /**
     * Returns value of 'appid' property
     *
     * @return string
     */
    public function getAppid()
    {
        return $this->get(self::APPID);
    }

    /**
     * Sets value of 'channel_id' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setChannelId($value)
    {
        return $this->set(self::CHANNEL_ID, $value);
    }

    /**
     * Returns value of 'channel_id' property
     *
     * @return string
     */
    public function getChannelId()
    {
        return $this->get(self::CHANNEL_ID);
    }

    /**
     * Sets value of 'uid' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setUid($value)
    {
        return $this->set(self::UID, $value);
    }

    /**
     * Returns value of 'uid' property
     *
     * @return int
     */
    public function getUid()
    {
        return $this->get(self::UID);
    }

    /**
     * Sets value of 'passport' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setPassport($value)
    {
        return $this->set(self::PASSPORT, $value);
    }

    /**
     * Returns value of 'passport' property
     *
     * @return string
     */
    public function getPassport()
    {
        return $this->get(self::PASSPORT);
    }

    /**
     * Sets value of 'device_type' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setDeviceType($value)
    {
        return $this->set(self::DEVICE_TYPE, $value);
    }

    /**
     * Returns value of 'device_type' property
     *
     * @return int
     */
    public function getDeviceType()
    {
        return $this->get(self::DEVICE_TYPE);
    }
}

/**
 * LoginResp message
 */
class LoginResp extends ProtobufMessage
{
    /* Field index constants */
    const ERR_NO = 1;

    /* @var array Field descriptors */
    protected static $fields = array(
        self::ERR_NO => array(
            'name' => 'err_no',
            'required' => true,
            'type' => 5,
        ),
    );

    /**
     * Constructs new message container and clears its internal state
     *
     * @return null
     */
    public function __construct()
    {
        $this->reset();
    }

    /**
     * Clears message values and sets default ones
     *
     * @return null
     */
    public function reset()
    {
        $this->values[self::ERR_NO] = null;
    }

    /**
     * Returns field descriptors
     *
     * @return array
     */
    public function fields()
    {
        return self::$fields;
    }

    /**
     * Sets value of 'err_no' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setErrNo($value)
    {
        return $this->set(self::ERR_NO, $value);
    }

    /**
     * Returns value of 'err_no' property
     *
     * @return int
     */
    public function getErrNo()
    {
        return $this->get(self::ERR_NO);
    }
}

/**
 * DisconnectReq message
 */
class DisconnectReq extends ProtobufMessage
{
    /* Field index constants */
    const GENERAL = 1;
    const APPID = 2;
    const CHANNEL_ID = 3;
    const UID = 4;
    const DEVICE_TYPE = 5;

    /* @var array Field descriptors */
    protected static $fields = array(
        self::GENERAL => array(
            'name' => 'general',
            'required' => true,
            'type' => 'GeneralContent'
        ),
        self::APPID => array(
            'name' => 'appid',
            'required' => true,
            'type' => 7,
        ),
        self::CHANNEL_ID => array(
            'name' => 'channel_id',
            'required' => true,
            'type' => 7,
        ),
        self::UID => array(
            'name' => 'uid',
            'required' => true,
            'type' => 5,
        ),
        self::DEVICE_TYPE => array(
            'name' => 'device_type',
            'required' => true,
            'type' => 5,
        ),
    );

    /**
     * Constructs new message container and clears its internal state
     *
     * @return null
     */
    public function __construct()
    {
        $this->reset();
    }

    /**
     * Clears message values and sets default ones
     *
     * @return null
     */
    public function reset()
    {
        $this->values[self::GENERAL] = null;
        $this->values[self::APPID] = null;
        $this->values[self::CHANNEL_ID] = null;
        $this->values[self::UID] = null;
        $this->values[self::DEVICE_TYPE] = null;
    }

    /**
     * Returns field descriptors
     *
     * @return array
     */
    public function fields()
    {
        return self::$fields;
    }

    /**
     * Sets value of 'general' property
     *
     * @param GeneralContent $value Property value
     *
     * @return null
     */
    public function setGeneral(GeneralContent $value)
    {
        return $this->set(self::GENERAL, $value);
    }

    /**
     * Returns value of 'general' property
     *
     * @return GeneralContent
     */
    public function getGeneral()
    {
        return $this->get(self::GENERAL);
    }

    /**
     * Sets value of 'appid' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setAppid($value)
    {
        return $this->set(self::APPID, $value);
    }

    /**
     * Returns value of 'appid' property
     *
     * @return string
     */
    public function getAppid()
    {
        return $this->get(self::APPID);
    }

    /**
     * Sets value of 'channel_id' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setChannelId($value)
    {
        return $this->set(self::CHANNEL_ID, $value);
    }

    /**
     * Returns value of 'channel_id' property
     *
     * @return string
     */
    public function getChannelId()
    {
        return $this->get(self::CHANNEL_ID);
    }

    /**
     * Sets value of 'uid' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setUid($value)
    {
        return $this->set(self::UID, $value);
    }

    /**
     * Returns value of 'uid' property
     *
     * @return int
     */
    public function getUid()
    {
        return $this->get(self::UID);
    }

    /**
     * Sets value of 'device_type' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setDeviceType($value)
    {
        return $this->set(self::DEVICE_TYPE, $value);
    }

    /**
     * Returns value of 'device_type' property
     *
     * @return int
     */
    public function getDeviceType()
    {
        return $this->get(self::DEVICE_TYPE);
    }
}

/**
 * DisconnectResp message
 */
class DisconnectResp extends ProtobufMessage
{
    /* Field index constants */
    const ERR_NO = 1;

    /* @var array Field descriptors */
    protected static $fields = array(
        self::ERR_NO => array(
            'name' => 'err_no',
            'required' => true,
            'type' => 5,
        ),
    );

    /**
     * Constructs new message container and clears its internal state
     *
     * @return null
     */
    public function __construct()
    {
        $this->reset();
    }

    /**
     * Clears message values and sets default ones
     *
     * @return null
     */
    public function reset()
    {
        $this->values[self::ERR_NO] = null;
    }

    /**
     * Returns field descriptors
     *
     * @return array
     */
    public function fields()
    {
        return self::$fields;
    }

    /**
     * Sets value of 'err_no' property
     *
     * @param int $value Property value
     *
     * @return null
     */
    public function setErrNo($value)
    {
        return $this->set(self::ERR_NO, $value);
    }

    /**
     * Returns value of 'err_no' property
     *
     * @return int
     */
    public function getErrNo()
    {
        return $this->get(self::ERR_NO);
    }
}

/**
 * EchoRequest message
 */
class EchoRequest extends ProtobufMessage
{
    /* Field index constants */
    const MESSAGE = 1;

    /* @var array Field descriptors */
    protected static $fields = array(
        self::MESSAGE => array(
            'name' => 'message',
            'required' => true,
            'type' => 7,
        ),
    );

    /**
     * Constructs new message container and clears its internal state
     *
     * @return null
     */
    public function __construct()
    {
        $this->reset();
    }

    /**
     * Clears message values and sets default ones
     *
     * @return null
     */
    public function reset()
    {
        $this->values[self::MESSAGE] = null;
    }

    /**
     * Returns field descriptors
     *
     * @return array
     */
    public function fields()
    {
        return self::$fields;
    }

    /**
     * Sets value of 'message' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setMessage($value)
    {
        return $this->set(self::MESSAGE, $value);
    }

    /**
     * Returns value of 'message' property
     *
     * @return string
     */
    public function getMessage()
    {
        return $this->get(self::MESSAGE);
    }
}

/**
 * EchoResponse message
 */
class EchoResponse extends ProtobufMessage
{
    /* Field index constants */
    const MESSAGE = 1;

    /* @var array Field descriptors */
    protected static $fields = array(
        self::MESSAGE => array(
            'name' => 'message',
            'required' => true,
            'type' => 7,
        ),
    );

    /**
     * Constructs new message container and clears its internal state
     *
     * @return null
     */
    public function __construct()
    {
        $this->reset();
    }

    /**
     * Clears message values and sets default ones
     *
     * @return null
     */
    public function reset()
    {
        $this->values[self::MESSAGE] = null;
    }

    /**
     * Returns field descriptors
     *
     * @return array
     */
    public function fields()
    {
        return self::$fields;
    }

    /**
     * Sets value of 'message' property
     *
     * @param string $value Property value
     *
     * @return null
     */
    public function setMessage($value)
    {
        return $this->set(self::MESSAGE, $value);
    }

    /**
     * Returns value of 'message' property
     *
     * @return string
     */
    public function getMessage()
    {
        return $this->get(self::MESSAGE);
    }
}
