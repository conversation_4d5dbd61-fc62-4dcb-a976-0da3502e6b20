<?php
/**
 * <AUTHOR>
 */
class Util_Common {

    /**
     *
     * @param unknown $intUserId
     * @return boolean
     */
    public static function getUserBawuPerm($intUserId){
        $arrInput = array(
            'user_id'    => $intUserId,
            'role_names' => array(
                'manager',
                'profession_manager',
                'fourth_manager',
                'assist',
                'publication_editor',//http://agroup.baidu.com/bazhu/md/article/47283
                'publication',
                'picadmin',
                'voiceadmin',
                'videoadmin',
                'broadcast_admin',
            ),
        );
        $arrOutput = Tieba_Service::call('perm', 'getUserBawuForum', $arrInput);
        if(false == $arrOutput || Tieba_Errcode::ERR_SUCCESS!=$arrOutput['errno'] || !isset($arrOutput['forum_user_roles']) ){
            $strMsg = sprintf('call perm getUserBawuForum failed! [%s] input[%s] output[%s]', 'perm::getUserBawuForum', serialize($arrInput),serialize($arrOutput));
            Bingo_Log::warning($strMsg);
        }
        return $arrOutput;
    }

    /**
     * [_getUidByUname description]
     * @param  [type] $strUname [description]
     * @return [type]           [description]
     */
    public static function getUidByUname($arrUname){
        if(empty($arrUname)){
            return array();
        }
        $arrUname = array_unique($arrUname);
        $arrInput = array(
            'user_name' => $arrUname,
        );
        $arrOut = Tieba_Service::call('user', 'getUidByUnames', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(false === $arrOut || Tieba_Errcode::ERR_SUCCESS!==$arrOut['errno']){
            $strReason = 'call service failed';
            Bingo_Log::warning(sprintf("%s input[%s] output[%s]",$strReason, serialize($arrInput), serialize($arrOut)));
            //$errno = (isset($arrOut['errno']) ? $arrOut['errno']: Tieba_Errcode::ERR_DB_QUERY_FAIL);
            return array();
        }

        if(!isset($arrOut['output']) || !isset($arrOut['output']['uids']) || empty($arrOut['output']['uids'])){
            return array();
        }
        $arrUserMap = array();
        foreach($arrOut['output']['uids'] as $key => $val){
            $intUid = (int)$val['user_id'];
            $strUname = $val['user_name'];
            $arrUserMap[$strUname] = $intUid;
        }
        return $arrUserMap;
    }
    /**
     * [getUnameByUid description]
     * @param  [type] $arrUid [description]
     * @return [type]         [description]
     */
    public static function getUnameByUid($arrUid){
        if(empty($arrUid)){
            return array();
        }
        $arrUid = array_unique($arrUid);
        $arrInput = array(
            'user_id' => $arrUid,
        );
        $arrOut = Tieba_Service::call('user', 'getUnameByUids', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(false === $arrOut || Tieba_Errcode::ERR_SUCCESS!==$arrOut['errno']){
            $strReason = 'call service failed';
            Bingo_Log::warning(sprintf("%s input[%s] output[%s]",$strReason, serialize($arrInput), serialize($arrOut)));
            //$errno = (isset($arrOut['errno']) ? $arrOut['errno']: Tieba_Errcode::ERR_DB_QUERY_FAIL);
            return array();
        }
        if(!isset($arrOut['output']) || !isset($arrOut['output']['unames']) || empty($arrOut['output']['unames'])){
            return array();
        }
        $arrUserMap = array();
        foreach($arrOut['output']['unames'] as $key => $val){
            $intUid = (int)$val['user_id'];
            $strUname = $val['user_name'];
            $arrUserMap[$intUid] = $strUname;
        }
        return $arrUserMap;
    }
    /**
     * [getUserMap description]
     * @param  [type] $arrUserIds [description]
     * @return [type]             [description]
     */
    public static function getUserMap($arrUserIds){
        if(empty($arrUserIds)){
            return array();
        }
        $arrInput = array(
            'user_id' => $arrUserIds,
        );
        $arrOutput = Tieba_Service::call('user', 'getUnameByUids', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            $strReason = 'call user::getUnameByUids failed';
            Bingo_Log::warning(sprintf("%s input[%s] output[%s]", $strReason, serialize($arrInput), serialize($arrOutput)));
            return array();
        }

        $arrUmap = array();
        foreach($arrOutput['output']['unames'] as $key => $val){
            $intUid = (int)$val['user_id'];
            $strUname = $val['user_name'];
            $arrUmap[$intUid] = array(
                'user_id'   => $intUid,
                'user_name' => $strUname,
                'portrait'  => strval(Tieba_Ucrypt::encode($intUid, $strUname)),
            );
        }
        return $arrUmap;
    }
}


?>
