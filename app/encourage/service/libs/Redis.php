<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2016/5/24
 * Time: 18:11
 */
class Service_Libs_Redis{
	
	const REDIS_NAME = 'show';
	protected static $_redis = array();
	const COMMON_KEY_PREFIX = 'Tbread_';

	/**
	 * @brief get redis obj.
     * @param string $name
	 * @return: obj of Bingo_Cache_Redis, or null if connect fail.
	 **/
	public static function _getRedis($name = null){
		if(is_null($name)){
			$name = self::REDIS_NAME;
		}
		if(isset(self::$_redis[$name])){
			return self::$_redis[$name];
		}
		Bingo_Timer::start('redis_init');
		$redis = new Bingo_Cache_Redis($name);
		Bingo_Timer::end('redis_init');
	
		if(!$redis || !$redis->isEnable()){
			Bingo_Log::warning("init redis fail.");
			return null;
		}
		self::$_redis[$name] = $redis;
		return self::$_redis[$name];
	}

    /**
     * @param $arrInput
     * @return array|null
     */
	public static function hgetAllFromRedis($arrInput){
		if(!isset($arrInput['key'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return false;
		}
		$key = $arrInput['key'];
        $unserial = isset($arrInput['unserial']) ? intval($arrInput['unserial']) : 1;
	
		$redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
		$redis = self::_getRedis($redisName);
		$arrParams = array(
            'key' => $key,
		);
		$arrRet = $redis->HGETALL($arrParams);
		//Bingo_Log::debug("get redis data.[key=$key][ret=".serialize($arrRet)."]");
		if($arrRet===false || $arrRet['err_no'] !== 0){
			Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
		}
		$data = null;
		if(!is_null($arrRet['ret'][$key])){
			Bingo_Log::pushNotice("_hitRedis_hashkey:$key",1);
			//$data = $arrRet['ret'][$key];
			foreach($arrRet['ret'][$key] as $item) {
                $data[] = array(
                    'field' => $item['field'],
                    'value' => $unserial ? unserialize($item['value']) : $item['value'],
                );
            }
		}else{
			Bingo_Log::pushNotice("_hitRedis_hashkey:$key",0);
		}
		return $data;
	}

    /**
     * @param $arrInput
     * @return mixed|null
     */
    public static function hgetFromRedis($arrInput){
        if(!isset($arrInput['key']) || !isset($arrInput['field'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $field = $arrInput['field'];
        $unserial = isset($arrInput['unserial']) ? intval($arrInput['unserial']) : 0;

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if($redis == false){
            Bingo_Log::warning('redis init fail');
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'field' => $field,
        );
        $arrRet = $redis->HGET($arrParams);
        //Bingo_Log::debug("get redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = null;
        if(!is_null($arrRet['ret'][$key])){
            Bingo_Log::pushNotice("_hithRedis_key:$key"."field:$field",1);
            $data = $unserial ? unserialize($arrRet['ret'][$key]) : $arrRet['ret'][$key];
        }else{
            Bingo_Log::pushNotice("_hithRedis_key:$key"."field:$field",0);
        }
        return $data;
    }

    /**
     * @param $arrInput
     * @return bool
     */
	public static function hsetToRedis($arrInput){
	    if(!isset($arrInput['key']) || !isset($arrInput['field'])
	            || !isset($arrInput['value'])){
	        Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	        return false;
	    }
	    $key = $arrInput['key'];
	    $field = $arrInput['field'];
	    $value = $arrInput['value'];
	    $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;
	    $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $serial = isset($arrInput['serial']) ? intval($arrInput['serial']) : 1;
	    
	    $redis = self::_getRedis($redisName);
	    if($redis == false){
	        Bingo_Log::warning('redis init fail');
	        return false;
	    }
	    $arrParams = array(
            'key' => $key,
            'field' => $field,
            'value' => $serial ? serialize($value) : $value,
	    );
	    $arrRet = $redis->HSET($arrParams);
		//Bingo_Log::debug("set redis data.[key=$key][ret=".serialize($arrRet)."]");
		if($arrRet===false || $arrRet['err_no'] !== 0){
			Bingo_Log::pushNotice("_addRedis_key:$key",0);
			Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
			return false;
		}else if($expire > 0){
			Bingo_Log::pushNotice("_addRedis_key:$key",1);
			$arrParams = array(
				'key' => $key,
				'seconds' => intval($expire),
			);
			$arrRet = $redis->EXPIRE($arrParams);
		}
		return true;
	}

    /**
     * @param $arrInput
     * @return bool
     */
    public static function hsetToRedisNx($arrInput){
        if(!isset($arrInput['key']) || !isset($arrInput['field'])
            || !isset($arrInput['value'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $field = $arrInput['field'];
        $value = $arrInput['value'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;
        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $serial = isset($arrInput['serial']) ? intval($arrInput['serial']) : 1;

        $redis = self::_getRedis($redisName);
        if($redis == false){
            Bingo_Log::warning('redis init fail');
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'field' => $field,
            'value' => $serial ? serialize($value) : $value,
        );
        $arrRet = $redis->HSETNX($arrParams);
        //Bingo_Log::debug("set redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::pushNotice("_addRedis_key:$key",0);
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }else if($expire > 0){
            Bingo_Log::pushNotice("_addRedis_key:$key",1);
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $arrRet = $redis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function hincrBy($arrInput){
        if(!isset($arrInput['key']) || !isset($arrInput['field'])
            || !isset($arrInput['step'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $field = $arrInput['field'];
        $step = $arrInput['step'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;
        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;

        $redis = self::_getRedis($redisName);
        if($redis == false){
            Bingo_Log::warning('redis init fail');
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'field' => $field,
            'step' => $step,
        );
        $arrRet = $redis->HINCRBY($arrParams);
        //Bingo_Log::debug("set redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::pushNotice("_addRedis_key:$key",0);
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }else if($expire > 0){
            Bingo_Log::pushNotice("_addRedis_key:$key",1);
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $arrRet = $redis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function hmincrBy($arrInput){
        if(!isset($arrInput['key']) || !isset($arrInput['fields'])
            || !isset($arrInput['step'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $fields = $arrInput['fields'];
        if(empty($fields)) {
            return true;
        }
        $step = $arrInput['step'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;
        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;

        $redis = self::_getRedis($redisName);
        if($redis == false){
            Bingo_Log::warning('redis init fail');
            return false;
        }
        $arrParams = array();
        foreach($fields as $field) {
            $arrParams['reqs'][] = array(
                'key' => $key,
                'field' => $field,
                'step' => $step,
            );
        }
        $arrRet = $redis->HINCRBY($arrParams);
        //Bingo_Log::debug("set redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::pushNotice("_addRedis_key:$key",0);
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }else if($expire > 0){
            Bingo_Log::pushNotice("_addRedis_key:$key",1);
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $arrRet = $redis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function mhincrBy($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['reqs'])) {
            return true;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if(is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $arrParam = array(
            'reqs' => array(),
        );
        foreach($arrInput['reqs'] as $v) {
            if(!isset($v['key']) || !isset($v['field']) || !isset($v['step'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrParam['reqs'][] = array(
                'key' => $v['key'],
                'field' => $v['field'],
                'step' => $v['step'],
            );
        }

        $arrRet = $redis->HINCRBY($arrParam);
        //Bingo_Log::debug("set redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            //Bingo_Log::pushNotice("_addRedis_key:$key",0);
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }

        $arrParam = array(
            'reqs' => array(),
        );
        foreach($arrInput['reqs'] as $v) {
            if(!isset($v['expire'])) {
                continue;
            }
            $arrParam['reqs'][] = array(
                'key' => $v['key'],
                'seconds' => intval($v['expire']),
            );
        }
        if(!empty($arrParam['reqs'])) {
            $redis->EXPIRE($arrParam);
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function hclearFromRedis($arrInput){
        if(!isset($arrInput['key']) || !isset($arrInput['field'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $field = $arrInput['field'];
        //$data = 0;

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        $arrParams = array(
            'key' => strval($key),
            'field' => strval($field),
        );
        $arrRet = $redis->HDEL($arrParams);
        //Bingo_Log::debug("clear redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::pushNotice("clearRedis_key:$key",0);
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }else{
            //$data = 1;
            Bingo_Log::pushNotice("clearRedis_key:$key",1);
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return mixed|null
     */
    public static function hmgetFromRedis($arrInput){
        if(!isset($arrInput['key']) || !isset($arrInput['fields'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $fields = $arrInput['fields'];
        if(empty($fields)) {
            return array();
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $unserial = isset($arrInput['unserial']) ? intval($arrInput['unserial']) : 1;
        $redis = self::_getRedis($redisName);
        if($redis == false){
            Bingo_Log::warning('redis init fail');
            return false;
        }
        $arrParams = array(
            'key' => $key,
            'field' => $fields,
        );
        $arrRet = $redis->HMGET($arrParams);
        //Bingo_Log::debug("get redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = array();
        if(!is_null($arrRet['ret'][$key])){
            //Bingo_Log::pushNotice("_hithRedis_key:$key"."field:$field",1);
            //Bingo_Log::warning(var_export($arrRet['ret'][$key]), 1);
            foreach($arrRet['ret'][$key] as $k => $v) {
                if(!is_null($v) && (!$unserial || ($v = unserialize($v)) !== false)) {
                    $data[$fields[$k]] = $v;
                }
            }
        }else{
            //Bingo_Log::pushNotice("_hithRedis_key:$key"."field:$field",0);
        }
        return $data;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function hmsetToRedis($arrInput){
        if(!isset($arrInput['key']) || !isset($arrInput['fields'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $fields = $arrInput['fields'];
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;
        $serial = isset($arrInput['serial']) ? intval($arrInput['serial']) : 1;

        $arrFieldValues = array();
        foreach($fields as $k=>$v) {
            $arrFieldValues[] = array(
                'field' => $k,
                'value' => $serial ? serialize($v) : $v,
            );
        }
        if(empty($arrFieldValues)) {
            return true;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if($redis == false){
            Bingo_Log::warning('redis init fail');
            return false;
        }

        $arrParams = array(
            'key' => $key,
            'fields' => $arrFieldValues,
        );
        $arrRet = $redis->HMSET($arrParams);
        //Bingo_Log::debug("get redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }

        if($expire) {
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $redis->EXPIRE($arrParams);
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function hmclearFromRedis($arrInput){
        if(!isset($arrInput['key']) || !isset($arrInput['fields'])
            || !is_array($arrInput['fields']) || empty($arrInput['fields'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $key = $arrInput['key'];
        $fields = $arrInput['fields'];
        $data = 0;

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        $arrParams = array();
        foreach($fields as $field) {
            $arrParams['reqs'][] = array(
                'key' => strval($key),
                'field' => strval($field),
            );
        }
        $arrRet = $redis->HDEL($arrParams);
        //Bingo_Log::debug("clear redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::pushNotice("clearRedis_key:$key",0);
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }else{
            $data = 1;
            Bingo_Log::pushNotice("clearRedis_key:$key",1);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @param int $num
     * @return bool
     */
    public static function redisCall($arrInput, $num=1){
        if(!isset($arrInput['method']) || !isset($arrInput['param'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $method = $arrInput['method'];
        $param = $arrInput['param'];
        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        $key =  $arrInput['param']['key'];

        $redis = self::_getRedis($redisName);
        if($redis == false){
            Bingo_Log::warning('redis init fail');
            return false;
        }
        $arrParams = $param;

        while($num > 0) {
            $arrRet = $redis->$method($arrParams);
            if($arrRet===false || $arrRet['err_no'] !== 0){
                Bingo_Log::pushNotice("_addRedis_key:$key",0);
                Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
                $num = $num - 1;
            }
            else {
                break;
            }
        }
        //Bingo_Log::debug("set redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            return false;
        }else if($expire > 0){
            Bingo_Log::pushNotice("_addRedis_key:$key",1);
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $arrRet = $redis->EXPIRE($arrParams);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return mixed|null
     */
	public static function getFromRedis($arrInput){
		if(!isset($arrInput['key'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
		}
		$key = $arrInput['key'];
	
		$redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $unserial = isset($arrInput['unserial']) ? intval($arrInput['unserial']) : 1;
		$redis = self::_getRedis($redisName);
        if($redis == false){
            Bingo_Log::warning('redis init fail');
            return false;
        }
		$arrParams = array(
            'key' => $key,
		);
		$arrRet = $redis->GET($arrParams);
		//Bingo_Log::debug("get redis data.[key=$key][ret=".serialize($arrRet)."]");
		if($arrRet===false || $arrRet['err_no'] !== 0){
			Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
		}
		$data = null;
		if(!is_null($arrRet['ret'][$key])){
			Bingo_Log::pushNotice("_hitRedis_key:$key",1);
			$data = $unserial ? unserialize($arrRet['ret'][$key]) : $arrRet['ret'][$key];
		}else{
			Bingo_Log::pushNotice("_hitRedis_key:$key",0);
		}
		return $data;
	}

    /**
     * @param $arrInput
     * @return bool
     */
	public static function setToRedis($arrInput){
		if(!isset($arrInput['key']) || !isset($arrInput['value'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return false;
		}
		$key = $arrInput['key'];
		$value = $arrInput['value'];
		$setnx = isset($arrInput['setnx'])?intval($arrInput['setnx']):0;
		$expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;
		$redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $serial = isset($arrInput['unserial']) ? intval($arrInput['serial']) : 1;

		$redis = self::_getRedis($redisName);
		if($redis == false){
			Bingo_Log::warning('redis init fail');
			return false;
		}
		$arrParams = array(
			'key' => $key,
			'value' => $serial ? serialize($value) : $value,
		);
		if($setnx){
			Bingo_Log::pushNotice("_addRedis_is_setnx_key:$key",1);
			$arrRet = $redis->SETNX($arrParams);
		}else{
			$arrRet = $redis->SET($arrParams);
		}
		//Bingo_Log::debug("set redis data.[key=$key][ret=".serialize($arrRet)."]");
		if($arrRet===false || $arrRet['err_no'] !== 0){
			Bingo_Log::pushNotice("_addRedis_key:$key",0);
			Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
			return false;
		}else if($expire > 0){
			Bingo_Log::pushNotice("_addRedis_key:$key",1);
			$arrParams = array(
				'key' => $key,
				'seconds' => intval($expire),
			);
			$arrRet = $redis->EXPIRE($arrParams);
		}
		return true;
	}

    /**
     * @param $arrInput
     * @return bool
     */
	public static function clearFromRedis($arrInput){
		if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $data = 0;

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        $arrParams = array(
            'key' => strval($key),
        );
        $arrRet = $redis->DEL($arrParams);
        //Bingo_Log::debug("clear redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::pushNotice("clearRedis_key:$key",0);
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }else{
            $data = 1;
            Bingo_Log::pushNotice("clearRedis_key:$key",1);
        }

        return true;
	}

    /**
     * @param $arrInput
     * @return bool
     */
    public static function mclearFromRedis($arrInput){
        if(!is_array($arrInput['keys']) || count($arrInput['keys']) <= 0){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $keys = $arrInput['keys'];

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        $arrParams = array();
        foreach($keys as $key) {
            $arrParams['reqs'][] = array(
                'key' => strval($key),
            );
        }
        $arrRet = $redis->DEL($arrParams);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::pushNotice("clearRedis_key:".serialize($keys),0);
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }else{
            Bingo_Log::pushNotice("clearRedis_key:".serialize($keys),1);
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function updateRank($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])
            || !isset($arrInput['score'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if(is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $arrParam = array(
            'key' => $arrInput['key'],
            'member' => $arrInput['member'],
            'score' => $arrInput['score'],
        );

        $ret = $redis->ZADD($arrParam);
        if(!$ret || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZADD fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        if(isset($arrInput['expire'])) {
            $arrParam = array(
                'key' => $arrInput['key'],
                'seconds' => intval($arrInput['expire']),
            );
            $redis->EXPIRE($arrParam);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function mupdateRank($arrInput)
    {
        if(!isset($arrInput['key']) || !is_array($arrInput['fields'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['fields'])) {
            return true;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if(is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $arrParam = array(
            'reqs' => array(),
        );
        foreach($arrInput['fields'] as $k => $v) {
            $arrParam['reqs'][] = array(
                'key' => $arrInput['key'],
                'member' => $k,
                'score' => $v,
            );
        }

        $ret = $redis->ZADD($arrParam);
        if(!$ret || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZADD fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        if(isset($arrInput['expire'])) {
            $arrParam = array(
                'key' => $arrInput['key'],
                'seconds' => intval($arrInput['expire']),
            );
            $redis->EXPIRE($arrParam);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function mupdateRankByKeys($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['reqs'])) {
            return true;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if(is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $arrParam = array(
            'reqs' => array(),
        );
        foreach($arrInput['reqs'] as $v) {
            if(!isset($v['key']) || !isset($v['member']) || !isset($v['score'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrParam['reqs'][] = array(
                'key' => $v['key'],
                'member' => $v['member'],
                'score' => $v['score'],
            );
        }

        $ret = $redis->ZADD($arrParam);
        if(!$ret || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZADD fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        $arrParam = array(
            'reqs' => array(),
        );
        foreach($arrInput['reqs'] as $v) {
            if(!isset($v['expire'])) {
                continue;
            }
            $arrParam['reqs'][] = array(
                'key' => $v['key'],
                'seconds' => intval($v['expire']),
            );
        }
        if(!empty($arrParam['reqs'])) {
            $redis->EXPIRE($arrParam);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function incrRank($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])
            || !isset($arrInput['step'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if(is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $arrParam = array(
            'key' => $arrInput['key'],
            'member' => $arrInput['member'],
            'step' => $arrInput['step'],
        );

        $ret = $redis->ZINCRBY($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZINCRBY fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        if(isset($arrInput['expire'])) {
            $arrParam = array(
                'key' => $arrInput['key'],
                'seconds' => intval($arrInput['expire']),
            );
            $redis->EXPIRE($arrParam);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function mincrRank($arrInput)
    {
        if(!isset($arrInput['key']) || !is_array($arrInput['fields'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['fields'])) {
            return true;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if(is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $arrParam = array(
            'reqs' => array(),
        );
        foreach($arrInput['fields'] as $k => $v) {
            $arrParam['reqs'][] = array(
                'key' => $arrInput['key'],
                'member' => $k,
                'step' => $v,
            );
        }

        $ret = $redis->ZINCRBY($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZINCRBY fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        if(isset($arrInput['expire'])) {
            $arrParam = array(
                'key' => $arrInput['key'],
                'seconds' => intval($arrInput['expire']),
            );
            $redis->EXPIRE($arrParam);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function mincrRankByKeys($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['reqs'])) {
            return true;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if(is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $arrParam = array(
            'reqs' => array(),
        );
        foreach($arrInput['reqs'] as $v) {
            if(!isset($v['key']) || !isset($v['member']) || !isset($v['step'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrParam['reqs'][] = array(
                'key' => $v['key'],
                'member' => $v['member'],
                'score' => $v['step'],
            );
        }

        $ret = $redis->ZINCRBY($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZINCRBY fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        $arrParam = array(
            'reqs' => array(),
        );
        foreach($arrInput['reqs'] as $v) {
            if(!isset($v['expire'])) {
                continue;
            }
            $arrParam['reqs'][] = array(
                'key' => $v['key'],
                'seconds' => intval($v['expire']),
            );
        }
        if(!empty($arrParam['reqs'])) {
            $redis->EXPIRE($arrParam);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function remRank($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if(is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $arrParam = array(
            'key' => $arrInput['key'],
            'member' => $arrInput['member'],
        );

        $ret = $redis->ZREM($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZREM fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function mremRank($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['members'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['members'])) {
            return true;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if(is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $arrParam = array(
            'reqs' => array(),
        );
        foreach($arrInput['members'] as $k) {
            $arrParam['reqs'][] = array(
                'key' => $arrInput['key'],
                'member' => $k,
            );
        }

        $ret = $redis->ZREM($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZREM fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function mremRankByKeys($arrInput)
    {
        if(!isset($arrInput['reqs'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['reqs'])) {
            return true;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if(is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $arrParam = array(
            'reqs' => array(),
        );
        foreach($arrInput['reqs'] as $v) {
            if(!isset($v['key']) || !isset($v['member'])) {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                return false;
            }
            $arrParam['reqs'][] = array(
                'key' => $v['key'],
                'member' => $v['member'],
            );
        }

        $ret = $redis->ZREM($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZREM fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        return true;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getRankCount($arrInput)
    {
        if(!isset($arrInput['key'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if(is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $strKey = $arrInput['key'];
        $arrParam = array(
            'key' => $strKey,
        );
        $ret = $redis->ZCARD($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZCARD fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        return isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : 0;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function mgetRankCount($arrInput)
    {
        if(!isset($arrInput['keys'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['keys'])) {
            return array();
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if(is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $arrParam = array();
        foreach($arrInput['keys'] as $strKey) {
            $arrParam['reqs'][] = array(
                'key' => $strKey,
            );
        }

        $ret = $redis->ZCARD($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZCARD fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        $arrOutput = array();
        foreach($arrInput['keys'] as $strKey) {
            $arrOutput[$strKey] = isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : 0;
        }

        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getRank($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if(is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $strKey = $arrInput['key'];
        $arrParam = array(
            'key' => $strKey,
            'member' => $arrInput['member'],
        );

        $ret = $redis->ZREVRANK($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZREVRANK fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        return isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : null;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function mgetRank($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['members'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['members'])) {
            return true;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if(is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $strKey = $arrInput['key'];
        $arrMembers = $arrInput['members'];
        $arrParam = array(
            'reqs' => array(),
        );
        foreach($arrMembers as $k) {
            $arrParam['reqs'][] = array(
                'key' => $strKey,
                'member' => $k,
            );
        }

        $ret = $redis->ZREVRANK($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZREVRANK fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        if(count($arrMembers) > 1) {
            $arrOutput = array();
            foreach($arrMembers as $k) {
                $arrOutput[$k] = isset($ret['ret'][$strKey][$k]) ? intval($ret['ret'][$strKey][$k]) : null;
            }
        } else {
            $k = $arrMembers[0];
            $arrOutput[$k] = isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : null;
        }

        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getScore($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['member'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if(is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $strKey = $arrInput['key'];
        $arrParam = array(
            'key' => $strKey,
            'member' => $arrInput['member'],
        );

        $ret = $redis->ZSCORE($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZSCORE fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        return isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : null;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function mgetScore($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['members'])) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        if(empty($arrInput['members'])) {
            return true;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if(is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $strKey = $arrInput['key'];
        $arrMembers = $arrInput['members'];
        $arrParam = array(
            'reqs' => array(),
        );
        foreach($arrMembers as $k) {
            $arrParam['reqs'][] = array(
                'key' => $strKey,
                'member' => $k,
            );
        }

        $ret = $redis->ZSCORE($arrParam);
        if($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZSCORE fail, params='.serialize($arrParam).',ret='.serialize($ret));
            return false;
        }

        if(count($arrMembers) > 1) {
            $arrOutput = array();
            foreach($arrMembers as $k) {
                $arrOutput[$k] = isset($ret['ret'][$strKey][$k]) ? intval($ret['ret'][$strKey][$k]) : null;
            }
        } else {
            $k = $arrMembers[0];
            $arrOutput[$k] = isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : null;
        }

        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getRankList($arrInput)
    {
        if(!isset($arrInput['key']) || !isset($arrInput['offset']) || !isset($arrInput['count']) ) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if(is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $strKey = $arrInput['key'];
        $intOrder = isset($arrInput['order']) ? intval($arrInput['order']) : 0;
        if($intOrder > 0) {
            $mixMax = isset($arrInput['max']) ? $arrInput['max'] : '+inf';
            $mixMin = isset($arrInput['min']) ? $arrInput['min'] : '-inf';
        } else {
            $mixMax = isset($arrInput['max']) ? $arrInput['max'] : '-inf';
            $mixMin = isset($arrInput['min']) ? $arrInput['min'] : '+inf';
        }
        $intOffset = intval($arrInput['offset']);
        $intCount = intval($arrInput['count']);
        $arrParam = array(
            'key' => $strKey,
            'max' => $mixMax,
            'min' => $mixMin,
            'offset' => $intOffset,
            'count' => $intCount,
        );

        if($intOrder > 0) {
            $ret = $redis->ZRANGEBYSCOREWITHSCORES($arrParam);
            if($ret === false || $ret['err_no'] != 0) {
                Bingo_Log::warning('ZRANGEBYSCOREWITHSCORES fail, params='.serialize($arrParam).',ret='.serialize($ret));
                return false;
            }
        } else {
            $ret = $redis->ZREVRANGEBYSCOREWITHSCORES($arrParam);
            if($ret === false || $ret['err_no'] != 0) {
                Bingo_Log::warning('ZREVRANGEBYSCOREWITHSCORES fail, params='.serialize($arrParam).',ret='.serialize($ret));
                return false;
            }
        }

        return isset($ret['ret'][$strKey]) ? $ret['ret'][$strKey] : array();
    }

    /**
     * @param $arrInput
     * @return bool|int
     */
    public static function getRankCountByCond($arrInput)
    {
        if (!isset($arrInput['key']) || (!isset($arrInput['max']) && !isset($arrInput['min']))) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return false;
        }

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        if (is_null($redis)) {
            Bingo_Log::warning("get redis fail");
            return false;
        }

        $strKey = $arrInput['key'];
        $mixMax = isset($arrInput['max']) ? $arrInput['max'] : '+inf';
        $mixMin = isset($arrInput['min']) ? $arrInput['min'] : '-inf';
        $arrParam = array(
            'key' => $strKey,
            'max' => $mixMax,
            'min' => $mixMin,
        );

        $ret = $redis->ZCOUNT($arrParam);
        if ($ret === false || $ret['err_no'] != 0) {
            Bingo_Log::warning('ZCOUNT fail, params=' . serialize($arrParam) . ',ret=' . serialize($ret));
            return false;
        }

        return isset($ret['ret'][$strKey]) ? intval($ret['ret'][$strKey]) : 0;
    }
}
/* vim: set noexpandtab ts=4 sw=4 sts=4 tw=100: */
