<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013:07:11 15:55:03
 * @version 
 * @structs & methods(copied from idl.)
*/



define("MODULE","encourage_dl");
class Dl_Meizhi_Meizhi{

const SERVICE_NAME = "Dl_Meizhi_Meizhi";
protected static $_conf = null;
protected static $_redis = null;
//protected static $_db = null;
protected static $_use_split_db = false;
//const DB_RAL_SERVICE_NAME = "forum_meizhi";
const DATABASE_NAME = 'forum_meizhi';
const REDIS_MEIZHI_VOTE_PREFIX = "meizhi_thread_";
const REDIS_MEIZHI_DAILY_EXP_PREFIX = "meizhi_daily_exp_";

const MEIZHI_THREAD_TYPE = 9;

const MEIZHI_STATUS_IS_DEL = 1;

//fourm_info和user_info表中使用的status位信息
const MEIZHI_STATUS_IS_FROZEN = 2;
const MEIZHI_STATUS_IS_REMOVE_FROM_RANKING = 4;
/**
 * @brief get mysql obj.
 * @param
 * @return: obj of Bd_DB, or null if connect fail.
**/
private static function _getDB(){
    $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
    if($objTbMysql && $objTbMysql->isConnected()) {
        return $objTbMysql;
    } else {
            Bingo_Log::warning("db connect fail.");
            return null;
    }
}
/**
 * @brief
 * @param
 * @return
 */
private static function _getRedis(){

	if(self::$_redis){
		return self::$_redis ;
	}
	Bingo_Timer::start('redisinit');

	//$redisConf = Bd_Conf::getConf('cache/redis_service_encourage');
	//if(!$redisConf){
	//	Bingo_Log::warning("get redis config fail.[cache/redis_service_encourage]");
	//	return null;
	//}

	//self::$_redis = Bd_RalRpc::create('Redis', array('pid'=>$redisConf['Pid'], 'tk'=>$redisConf['Tk'], 'app'=>$redisConf['App']));
	self::$_redis = new Bingo_Cache_Redis('ala_new');

	Bingo_Timer::end('redisinit');
	if(!self::$_redis){
		Bingo_Log::warning("init redis fail.");
		self::$_redis = null;
		return null;
	}
	return self::$_redis;
}
	
/**
 * @brief init
 * @param
 * @return: true if success. false if fail.
**/		
private static function _init(){
	
	//add init code here. init will be called at every public function beginning.
	//not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
	//init should be recalled for many times.
	
	if(self::$_conf == null){	
		self::$_conf = Bd_Conf::getConf("/app/encourage/dl_meizhi_meizhi");
		if(self::$_conf == false){
			Bingo_Log::warning("init get conf fail.");
			return false;
		}
		
	}
	return true; 
}


/**
 * @brief
 * @param
 * @return
 */
private static function _errRet($errno){
    return array(
        'errno' => $errno,
        'errmsg' => Tieba_Error::getErrmsg($errno),
    );
}
/**
 * @brief
 * @param
 * @return
 */
public static function preCall($arrInput){
    // pre-call hook
}
/**
 * @brief
 * @param
 * @return
 */
public static function postCall($arrInput){
    // post-call hook
}
/**
 * @brief
 * @param
 * @return
 */
public static function test($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['uid'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$uid = intval($arrInput['uid']);

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.

	//your code here......



	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
	);
	return $arrOutput;
}
/**
 * @brief
 * @param
 * @return
 */
private static function _getTableName($forum_id){
	return 'forum_info_'.strval($forum_id % 16);
}


	const MEIZHI_CONFIG = 'meizhi_config';	//配置cache的key
	/**
	 * @brief 获取配置
	 * @param arrInput:
	 * @return: conf_info data
	 * struct conf_info
	 * {
	 * 	   uint32_t up_score;
	 *     uint32_t down_score;
	 *     uint32_t up_meizhi_count;
	 * };
	 **/
	public static function getConf(){
		
		$strKey = self::MEIZHI_CONFIG;
		$arrData = array();
		//input params check;
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}    
	
		//get conf_info from redis cache
//		$redis = self::_getRedis();
//
//		$res = $redis->GET(array('key'=> $strKey));
	
//		if( $res['err_no'] != Tieba_Errcode::ERR_SUCCESS ){
//			Bingo_Log::warning("call getConf get redis failed");
//		}
		//cache has conf_info 
//		if(count($res['ret'][$strKey]) > 0) {
//			$arrData = unserialize($res['ret'][$strKey]);
//		}
//		else {
//			//get data from db
//			$mixDb = self::_getDB();
//			if( is_null($mixDb) ) {
//				return false;
//			}
//
//			$strSql = "select level,level_info from config";
//
//			$mixRes = $mixDb->query($strSql);
//			if(false === $mixRes) {
//				Bingo_Log::warning("getConf db query fail. error[".serialize($mixDb->error())."]");
//				return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
//			}
//			$arrData = $mixRes;
//			//set conf_info into redis cache
//			$mixRes = $redis->SET(array('key'=> $strKey, 'value' => serialize($arrData)));
//		}
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
			'data' => $arrData,
		);
		return $arrOutput;
	}
	
	//修改配置
	/**
	 * @brief
	 * @param
	 * @return
	 */
	public static function setConf($arrInput){
		
		$strKey = self::MEIZHI_CONFIG;
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}    
	
		$mixDb = self::_getDB();
		if( is_null($mixDb) ) {
			return false;
		}
		//set db
		switch ($arrInput['type']) {
			case 'update':
				$strSql = "update config set level_info='". $arrInput['level_info'] .
					"' where level=".$arrInput['level'];
				break;
			case 'insert':
				$strSql = "insert into config(level,level_info) values(".$arrInput['level'].
					",'". $arrInput['level_info'] ."')";
				break;
			default:
				Bingo_Log::warning("type not update or insert. input[".serialize($arrInput)."]");
				return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
		}
		
		$mixRes = $mixDb->query($strSql);
		if(false === $mixRes) {
			Bingo_Log::warning("sql[$strSql] fail. error[".serialize($mixDb->error())."]");
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		
//		//get conf_info from redis cache
//		$redis = self::_getRedis();
//
//		//del cache
//		$mixRes = $redis->DEL(array('key'=> $strKey));
//		//set cache
//		$mixRes = $redis->SET(array('key'=> $strKey, 'value' => serialize($arrInput['data'])));
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrData = array();
		$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
			'data' => $arrData,
		);
		return $arrOutput;
		
	}
	
	/**
	 * @brief 获取所有距上回升/降级>=time且等级>=level的用户
	 * @param arrInput:
	 * uint32_t time
	 * uint32_t level
	 * @return: 
	 * uint32_t user_id
	 * uint32_t new_exp
	 * 
	 **/
	public static function getUserAfterTimeAndLevel($arrInput){
		$mixDb = self::_getDB();
		if( is_null($mixDb) ) {
			return false;
		}
		
		$intWeekTime = $arrInput['time'] - 86400 * 7;
		$strSql = "select user_id, new_exp,level,thread_id from user_info where last_time<=".
				$arrInput['time'] ." and last_time>".$intWeekTime.
				" and level>=".$arrInput['level'];
		
		$arrData = $mixDb->query($strSql);
		if(false === $arrData) {
			Bingo_Log::warning("sql[$strSql] fail. error[".serialize($mixDb->error())."]");
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
			'data' => $arrData,
		);
		return $arrOutput;
	}
	
	/**
	 * @brief 批量对用户降级
	 * @param arrInput[]:
	 * uint32_t user_id
	 * @return: true/false
	 * 
	 **/
	public static function mdownLevel($arrInput){
		
		$arrUids = array();
		foreach ($arrInput['users'] as $arrUser) {
			$arrUids[] = $arrUser['user_id'];
		}
		$strUids = implode(',', $arrUids);
	
		$mixDb = self::_getDB();
		if( is_null($mixDb) ) {
			return false;
		}
		$intCurTime = time();
		$strSql = "update user_info set level=level-1, new_exp=0,last_time=".
			$intCurTime." where user_id in (".$strUids.")";
		$arrData = $mixDb->query($strSql);
		if(false === $arrData) {
			Bingo_Log::warning("sql[$strSql] fail. error[".serialize($mixDb->error())."]");
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}		
		
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
			'data' => array(),
		);
		
		return $arrOutput;
	}

	/**
	 * @brief
	 * @param arrInput:
	 * 	uint32_t forum_id
	 * 	uint32_t thread_id
	 * 	string vote_type
	 * @return: $arrOutput
	 * 	vote_result data
	 **/
	public static function incrMeizhiVotes($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['forum_id']) || !isset($arrInput['thread_id']) || !isset($arrInput['vote_type'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$forum_id = intval($arrInput['forum_id']);
		$thread_id = intval($arrInput['thread_id']);
		$vote_type = $arrInput['vote_type'];
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
	
		//your code here......
		if(empty(self::$_conf['meizhi_vote_type'])){
			Bingo_Log::warning("dl conf loss var.[var=meizhi_vote_type]");
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}

		if(!isset(self::$_conf['meizhi_vote_type'][$vote_type])){
			Bingo_Log::warning("vote_type invalid. [vote_type=".$vote_type."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
//		$key = self::REDIS_MEIZHI_VOTE_PREFIX.$forum_id."_".$thread_id;
//		$arrParams = array(
//				'key' => $key,
//				'field' => $vote_type,
//				'step' => 1,
//		);
//		$redis = self::_getRedis();
//		if($redis === NULL){
//			Bingo_Log::warning("redis init error");
//			return self::_errRet(Tieba_Errcode::ERR_CACHE_CONN_FAIL);
//		}
//		$arrRet = $redis->HINCRBY($arrParams);
//		//Bingo_Log::debug("redis ret=".print_r($arrRet,true));
//		if($arrRet['err_no'] !== 0){
//			return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
//		}
	
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
		);
		return $arrOutput;
	}
	/**
	 * @brief
	 * @param arrInput:
	 * 	uint32_t forum_id
	 * 	uint32_t thread_id
	 * @return: $arrOutput
	 * 	meizhi_votes data
	 **/
	public static function getMeizhiVotes($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['forum_id'])||!isset($arrInput['thread_id'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$thread_id = intval($arrInput['thread_id']);
		$forum_id = intval($arrInput['forum_id']);
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
		$data = array();
	
		//your code here......
		if(empty(self::$_conf['meizhi_vote_type'])){
			Bingo_Log::warning("dl conf loss var.[var=meizhi_vote_type]");
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		$arrMeizhiVoteType = array_keys(self::$_conf['meizhi_vote_type']);
	
//		$key = self::REDIS_MEIZHI_VOTE_PREFIX.$forum_id."_".$thread_id;
//		$arrParams = array(
//				'key' => $key,
//				'field' => $arrMeizhiVoteType,
//		);
//		$redis = self::_getRedis();
//		$arrRet = $redis->HMGET($arrParams);
//		//Bingo_Log::debug("redis ret=".print_r($arrRet,true));
//		if($arrRet===false || $arrRet['err_no'] !== 0){
//			return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
//		}
//
//		foreach($arrRet['ret'][$key] as $index=>$value){
//			$data[$arrMeizhiVoteType[$index]] = intval($value);
//		}
//
		foreach($arrMeizhiVoteType as $item){
			if(!isset($data[$item])){
				$data[$item] = 0;
			}
		}
	
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'data' => $data,
		);
		return $arrOutput;
	}
	/**
	 * @brief
	 * @param arrInput:
	 * 	uint32_t forum_id
	 * 	uint32_t thread_id
	 *  string vote_type
	 *  uint32_t vote_num
	 * @return: $arrOutput
	 **/
	public static function setMeizhiVotes($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['forum_id'])||!isset($arrInput['thread_id'])||!isset($arrInput['vote_type'])||!isset($arrInput['vote_num'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$thread_id = intval($arrInput['thread_id']);
		$forum_id = intval($arrInput['forum_id']);
		$vote_type = $arrInput['vote_type'];
		$vote_num = intval($arrInput['vote_num']);
		
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
		$data = array();
	
		//your code here......
		if(empty(self::$_conf['meizhi_vote_type'])){
			Bingo_Log::warning("dl conf loss var.[var=meizhi_vote_type]");
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}

		if(!isset(self::$_conf['meizhi_vote_type'][$vote_type])){
			Bingo_Log::warning("vote_type invalid. [vote_type=".$vote_type."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		
		
//		$key = self::REDIS_MEIZHI_VOTE_PREFIX.$forum_id."_".$thread_id;
//		$arrParams = array(
//				'key' => $key,
//				'field' => $vote_type,
//				'value' => $vote_num,
//		);
//		$redis = self::_getRedis();
//		$arrRet = $redis->HSET($arrParams);
//		//Bingo_Log::debug("redis ret=".print_r($arrRet,true));
//		if($arrRet===false || $arrRet['err_no'] !== 0){
//			return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
//		}
	
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
		);
		return $arrOutput;
	}
	/**
	* @brief
	* @param arrInput:
	* 	uint32_t user_id
	* 	string   exp_field
	*  uint32_t exp_value
	* @return: $arrOutput
	**/
	public static function saveMeizhiDailyExp($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['user_id'])||!isset($arrInput['exp_field'])||!isset($arrInput['exp_value'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$user_id = intval($arrInput['user_id']);
		$exp_field = $arrInput['exp_field'];
		$exp_value = intval($arrInput['exp_value']);
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
		$data = array();
	
		//your code here......
//		$key = self::REDIS_MEIZHI_DAILY_EXP_PREFIX.$user_id;
//		$arrParams = array(
//				'key' => $key,
//				'field' => $exp_field,
//				'value' => $exp_value,
//		);
//		$redis = self::_getRedis();
//		$arrRet = $redis->HSET($arrParams);
//
//		//Bingo_Log::debug("redis ret=".print_r($arrRet,true));
//		if($arrRet===false || $arrRet['err_no'] !== 0){
//			return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
//		}
//
//		$timestamp = mktime(0,0,0,date("n"),date("j")+1,date("Y"));
//		$arrParams = array(
//				'key' => $key,
//				'timestamp' => $timestamp,
//		);
//		$arrRet = $redis->EXPIREAT($arrParams);
//
//		//Bingo_Log::debug("redis ret=".print_r($arrRet,true));
//		if($arrRet===false || $arrRet['err_no'] !== 0){
//			return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
//		}
//
//		Bingo_Log::pushNotice("meizhiDailyExp",date("Y-m-d",$timestamp));
	
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
		);
		return $arrOutput;
	}
	
	/**
	 * @brief
	 * @param arrInput:
	 * 	uint32_t user_id
	 * @return: $arrOutput
	 * 	meizhi_votes data
	 **/
	public static function getMeizhiDailyExpInfo($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['user_id'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$user_id = intval($arrInput['user_id']);
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
		$data = array();
	
		//your code here......
//		$key = self::REDIS_MEIZHI_DAILY_EXP_PREFIX.$user_id;
//		$arrParams = array(
//				'key' => $key,
//		);
//		$redis = self::_getRedis();
//		$arrRet = $redis->HGETALL($arrParams);
//		//Bingo_Log::debug("redis ret=".print_r($arrRet,true));
//		if($arrRet===false || $arrRet['err_no'] !== 0){
//			return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
//		}
//
//		foreach($arrRet['ret'][$key] as $item){
//			$data[$item['field']] = intval($item['value']);
//		}
//
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'data' => $data,
		);
		return $arrOutput;
	}
	/**
	 * @brief
	 * @param arrInput:
	 * 	uint32_t user_id
	 * @return: $arrOutput
	 * 	meizhi_votes data
	 **/
	public static function incrMeizhiDailyExpInfo($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['user_id'])||!isset($arrInput['field'])||!isset($arrInput['step'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$user_id = intval($arrInput['user_id']);
		$field = $arrInput['field'];
		$step = intval($arrInput['step']);
		$timestamp = mktime(0,0,0,date("n"),date("j")+1,date("Y"));
		
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
		$data = 0;
	
//		//your code here......
//		$key = self::REDIS_MEIZHI_DAILY_EXP_PREFIX.$user_id;
//		$arrParams = array(
//				'key' => $key,
//				'field' => strval($field),
//				'step' => $step,
//		);
//		$redis = self::_getRedis();
//		$arrRet = $redis->HINCRBY($arrParams);
//		//Bingo_Log::debug("redis ret=".print_r($arrRet,true));
//		if($arrRet===false || $arrRet['err_no'] !== 0){
//			return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
//		}
//		$data = intval($arrRet['ret'][$key]);
//
//		//设置每日零点自动清零
//		$arrParams = array(
//				'key' => $key,
//				'timestamp' => $timestamp,
//		);
//		$arrRet = $redis->EXPIREAT($arrParams);
//		if($arrRet===false || $arrRet['err_no'] !== 0){
//			Bingo_Log::warning("call redis expireat error.[input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
//		}
		
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'data' => $data,
		);
		return $arrOutput;
	}
	/**
	 * @brief
	 * @param arrInput:
	 * 	uint32_t user_id
	 * 	uint32_t exp
	 * @return: $arrOutput
	 **/
	public static function addMeizhiExp($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['user_id']) || !isset($arrInput['exp'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$user_id = intval($arrInput['user_id']);
		$exp = intval($arrInput['exp']);
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
	
		//your code here......
		$time = time();
		$strSql = "update user_info set new_exp=new_exp+$exp where user_id=$user_id";
		Bingo_Log::debug($strSql);
	
		$db = self::_getDB();
		if(is_null($db)){
			Bingo_Log::warning("call _getDB error.");
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$arrRet = array();
		Bingo_Timer::start("dbquery");
		$arrRet = $db->query($strSql);
		Bingo_Timer::end("dbquery");
		//Bingo_Log::debug("db output:".var_export($arrRet,true));
		if($arrRet===false){
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
		);
		return $arrOutput;
	}
	/**
	 * @brief
	 * @param arrInput:
	 * 	uint32_t user_id
	 *  user_info user_info
	 *  uint32_t last_vote_num
	 * @return: $arrOutput
	 **/
	public static function meizhiLevelUp($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['user_id'])||!isset($arrInput['user_info'])||!isset($arrInput['last_vote_num'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		//input params.
		$user_id = intval($arrInput['user_id']);
		$last_vote_num = intval($arrInput['last_vote_num']);
		$user_info = Tieba_Service::getArrayParams($arrInput,'user_info');
	
		$level = intval($user_info['level']);
		$forum_id = intval($user_info['forum_id']);
		$thread_id = intval($user_info['thread_id']);
	
		Bingo_Log::pushNotice("level",$level);
		Bingo_Log::pushNotice("forum_id",$forum_id);
		Bingo_Log::pushNotice("thread_id",$thread_id);
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
	
		//your code here......
		$time = time();
		$strSql = "update user_info set level=level+1,last_time=$time,new_exp=0,last_vote_num=$last_vote_num where user_id=$user_id and level=$level";
		Bingo_Log::debug($strSql);
	
		$db = self::_getDB();
		if(is_null($db)){
			Bingo_Log::warning("call _getDB error.");
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$arrRet = array();
		Bingo_Timer::start("meizhiLevelUp");
		if($level===0){
			$table = self::_getTableName($forum_id);
			$strSql2 = "update $table set thread_type=10 where forum_id=$forum_id and thread_id=$thread_id and thread_type=9";
			Bingo_Log::debug($strSql2);
			$db->startTransaction();
			$arrRet = $db->query($strSql)&&$db->query($strSql2);
			if($arrRet){
				$db->commit();
			}else{
				$db->rollback();
			}
		}else{
			$arrRet = $db->query($strSql);
		}
		Bingo_Timer::end("meizhiLevelUp");
		//Bingo_Log::debug("db output:".var_export($arrRet,true));
		if($arrRet===false){
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
		);
		return $arrOutput;
	}
	/**
	 * @brief
	 * @param arrInput:
	 * 	uint32_t thread_id
	 *  uint32_t forum_id
	 * @return: $arrOutput
	 * 	uint32_t data
	 **/
	public static function getMeizhiThreadStatus($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['thread_id']) || !isset($arrInput['forum_id'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$thread_id = intval($arrInput['thread_id']);
		$forum_id = intval($arrInput['forum_id']);
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
	
		$data = array();
		//your code here......
		$table = self::_getTableName($forum_id);
		$strSql = "select thread_id,thread_type,is_del,status from $table where forum_id=$forum_id and thread_id=$thread_id";
		Bingo_Log::debug($strSql);
	
		$db = self::_getDB();
		if(is_null($db)){
			Bingo_Log::warning("call _getDB error.");
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		Bingo_Timer::start("dbquery");
		$arrRet = $db->query($strSql);
		Bingo_Timer::end("dbquery");
		//Bingo_Log::debug("db output:".var_export($arrRet,true));
	
		if(!empty($arrRet[0])){
			$data = $arrRet[0];
		}
	
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'data' => $data,
		);
		return $arrOutput;
	}
	/**
	 * @brief
	 * @param arrInput:
	 * 	uint32_t user_id
	 * 	uint32_t thread_id
	 * 	uint32_t forum_id
	 *  uint32_t del_time
	 *  uint32_t thread_type
	 * @return: $arrOutput
	 **/
	public static function delMeizhiThread($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['user_id']) || !isset($arrInput['thread_id'])|| !isset($arrInput['thread_type']) || !isset($arrInput['forum_id'])||!isset($arrInput['del_time'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$user_id = intval($arrInput['user_id']);
		$thread_id = intval($arrInput['thread_id']);
		$thread_type = intval($arrInput['thread_type']);
		$forum_id = intval($arrInput['forum_id']);
		$del_time = intval($arrInput['del_time']);
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
		$error = Tieba_Errcode::ERR_SUCCESS;
	
		//your code here......
		$table = self::_getTableName($forum_id);
		$strSql = "update $table set is_del=1 where forum_id=$forum_id and thread_id=$thread_id";
		$strSql2 = "update user_info set is_del=1,del_time=$del_time where user_id=$user_id and thread_id=$thread_id";
		$strSql3 = "delete from $table where forum_id=$forum_id and thread_id=$thread_id";
		$strSql4 = "delete from user_info where user_id=$user_id and thread_id=$thread_id";
		$db = self::_getDB();
		if(is_null($db)){
			Bingo_Log::warning("call _getDB error.");
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		Bingo_Timer::start("dbquery");
		if($thread_type===9){
			Bingo_Log::debug($strSql3);
			Bingo_Log::debug($strSql4);
			$db->startTransaction();
			if($db->query($strSql3)&&$db->query($strSql4)){
				$db->commit();
			}else{
				$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				$db->rollback();
			}
	
		}else if($thread_type===10){
			Bingo_Log::debug($strSql);
			Bingo_Log::debug($strSql2);
			$db->startTransaction();
			if($db->query($strSql)&&$db->query($strSql2)){
				$db->commit();
			}else{
				$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				$db->rollback();
			}
		}else{
			Bingo_Log::debug($strSql);
			if(!$db->query($strSql)){
				$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
			}
		}
		Bingo_Timer::end("dbquery");
	
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
		);
		return $arrOutput;
	}
	/**
	 * @brief
	 * @param arrInput:
	 * 	uint32_t user_id
	 * 	uint32_t thread_id
	 *  uint32_t forum_id
	 * 	uint32_t thread_type
	 * @return: $arrOutput
	 **/
	public static function recoverMeizhiThread($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['user_id']) || !isset($arrInput['thread_id']) || !isset($arrInput['thread_type'])|| !isset($arrInput['forum_id'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$user_id = intval($arrInput['user_id']);
		$thread_id = intval($arrInput['thread_id']);
		$forum_id = intval($arrInput['forum_id']);
		$thread_type = intval($arrInput['thread_type']);
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
	
		//your code here......
		$table = self::_getTableName($forum_id);
		$strSql = "update $table set is_del=0 where forum_id=$forum_id and thread_id=$thread_id";
		$strSql2 = "update user_info set is_del=0 where user_id=$user_id and thread_id=$thread_id";
	
		$db = self::_getDB();
		if(is_null($db)){
			Bingo_Log::warning("call _getDB error.");
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		Bingo_Timer::start("dbquery");
		if($thread_type===10){
			Bingo_Log::debug($strSql);
			Bingo_Log::debug($strSql2);
			$db->startTransaction();
			if($db->query($strSql)&&$db->query($strSql2)){
				$db->commit();
			}else{
				$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				$db->rollback();
			}
		}else if($thread_type!==9){
			Bingo_Log::debug($strSql);
			if(!$db->query($strSql)){
				$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
			}
		}
		Bingo_Timer::end("dbquery");
	
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
		);
		return $arrOutput;
	}
	/**
	 * @brief
	 * @param arrInput:
	 * 	uint32_t user_id
	 * 	uint32_t forum_id
	 * 	uint32_t thread_id
	 * 	uint32_t thread_time
	 * @return: $arrOutput
	 **/
	public static function storeMeizhiThread($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['user_id']) || !isset($arrInput['forum_id']) || !isset($arrInput['thread_id']) || !isset($arrInput['thread_time'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$user_id = intval($arrInput['user_id']);
		$forum_id = intval($arrInput['forum_id']);
		$thread_id = intval($arrInput['thread_id']);
		$thread_time = intval($arrInput['thread_time']);
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
	
		//your code here......
		$table = self::_getTableName($forum_id);
		$strSql = "insert into $table set forum_id=$forum_id,user_id=$user_id,".
				"thread_id=$thread_id,thread_type=0,thread_time=$thread_time,".
				"last_post_time=$thread_time,is_del=0";
		Bingo_Log::debug($strSql);
	
		$db = self::_getDB();
		if(is_null($db)){
			Bingo_Log::warning("call _getDB error.");
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$arrRet = array();
		Bingo_Timer::start("dbquery");
		$arrRet = $db->query($strSql);
		Bingo_Timer::end("dbquery");
		//Bingo_Log::debug("db output:".var_export($arrRet,true));
		if($arrRet===false){
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
	
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
		);
		return $arrOutput;
	}
	/**
	 * @brief
	 * @param arrInput:
	 * 	uint32_t forum_id
	 * 	uint32_t thread_id
	 * 	uint32_t thread_type
	 * 	uint32_t thread_time
	 * @return: $arrOutput
	 **/
	public static function updateMeizhiThreadRepliedTime($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['forum_id']) || !isset($arrInput['thread_id']) || !isset($arrInput['thread_type']) || !isset($arrInput['thread_time'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$forum_id = intval($arrInput['forum_id']);
		$thread_id = intval($arrInput['thread_id']);
		$thread_type = intval($arrInput['thread_type']);
		$thread_time = intval($arrInput['thread_time']);
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
	
		//your code here......
		$table = self::_getTableName($forum_id);
		$strSql = "update $table set last_post_time=$thread_time where forum_id=$forum_id and thread_id=$thread_id";
		Bingo_Log::debug($strSql);
	
		$db = self::_getDB();
		if(is_null($db)){
			Bingo_Log::warning("call _getDB error.");
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$arrRet = array();
		Bingo_Timer::start("dbquery");
		$arrRet = $db->query($strSql);
		Bingo_Timer::end("dbquery");
		//Bingo_Log::debug("db output:".var_export($arrRet,true));
		if($arrRet===false){
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
	
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
		);
		return $arrOutput;
	}
	/**
	 * @brief
	 * @param arrInput:
	 * 	uint32_t user_id
	 *  uint32_t thread_id
	 *  uint32_t forum_id
	 * @return: $arrOutput
	 **/
	public static function frozenMeizhiUser($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['user_id'])||!isset($arrInput['thread_id'])||!isset($arrInput['forum_id'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$user_id = intval($arrInput['user_id']);
		$thread_id = intval($arrInput['thread_id']);
		$forum_id = intval($arrInput['forum_id']);
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
		$error = Tieba_Errcode::ERR_SUCCESS;
	
		//your code here......
		$table = self::_getTableName($forum_id);
		$strSql = "update $table set status=status|".self::MEIZHI_STATUS_IS_FROZEN." where forum_id=$forum_id and thread_id=$thread_id";
		$strSql2 = "update user_info set status=status|".self::MEIZHI_STATUS_IS_FROZEN." where user_id=$user_id";
		$db = self::_getDB();
		if(is_null($db)){
			Bingo_Log::warning("call _getDB error.");
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		Bingo_Timer::start("dbquery");

		Bingo_Log::debug($strSql);
		Bingo_Log::debug($strSql2);
		$db->startTransaction();
		if($db->query($strSql)&&$db->query($strSql2)){
			$db->commit();
		}else{
			$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
			$db->rollback();
		}

		Bingo_Timer::end("dbquery");
	
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
		);
		return $arrOutput;
	}
	/**
	 * @brief
	 * @param arrInput:`
	 * 	uint32_t user_id
	 *  uint32_t thread_id
	 *  uint32_t forum_id
	 * @return: $arrOutput
	 **/
	public static function unfrozenMeizhiUser($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['user_id'])||!isset($arrInput['thread_id'])||!isset($arrInput['forum_id'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$user_id = intval($arrInput['user_id']);
		$thread_id = intval($arrInput['thread_id']);
		$forum_id = intval($arrInput['forum_id']);
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
		$error = Tieba_Errcode::ERR_SUCCESS;
	
		//your code here......
		$table = self::_getTableName($forum_id);
		$strSql = "update $table set status=status&~".self::MEIZHI_STATUS_IS_FROZEN." where forum_id=$forum_id and thread_id=$thread_id";
		$strSql2 = "update user_info set status=status&~".self::MEIZHI_STATUS_IS_FROZEN." where user_id=$user_id";
		$db = self::_getDB();
		if(is_null($db)){
			Bingo_Log::warning("call _getDB error.");
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		Bingo_Timer::start("dbquery");
	
		Bingo_Log::debug($strSql);
		Bingo_Log::debug($strSql2);
		$db->startTransaction();
		if($db->query($strSql)&&$db->query($strSql2)){
			$db->commit();
		}else{
			$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
			$db->rollback();
		}
	
		Bingo_Timer::end("dbquery");
	
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
		);
		return $arrOutput;
	}
	/**
	 * @brief
	 * @param arrInput:
	 * 	uint32_t uid
	 * @return: $arrOutput
	 **/
	public static function insertMeizhiThread($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['req'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$arrInput = Tieba_Service::getArrayParams($arrInput, 'req');
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
		$arrOutput = false;
	
		//your code here......
		$db = self::_getDB();
		if( is_null($db) ){
			Bingo_Log::warning("dl insertMeizhiThread get db fail.");
			return false;
		}
	
		$forum_id = $arrInput['forum_id'];
		$user_id = $arrInput['user_id'];
		$thread_id = $arrInput['thread_id'];
		$now_time = time();
		$table_name=self::_getTableName($forum_id);
		$type = self::MEIZHI_THREAD_TYPE;
	
		$sql = "INSERT INTO $table_name(forum_id, thread_id, thread_time, last_post_time, user_id, is_del,thread_type,status) VALUES($forum_id, $thread_id, $now_time, $now_time, $user_id, 0, $type,0)";
	
		$db->startTransaction();
		$res = $db->query($sql);
		if(false === $res){
			Bingo_Log::warning("insertMeizhiThread db query fail. error[".serialize($db->error())."] sql[".$sql."]");
			$db->rollback();
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
	
		$sql = "INSERT INTO user_info(forum_id, thread_id, last_time, del_time, user_id, is_del, level, new_exp, last_vote_num,status) VALUES($forum_id, $thread_id, $now_time, $now_time, $user_id, 0, 0, 0, 0,0)";
		
		$res = $db->query($sql);
		if(false === $res){
			Bingo_Log::warning("insertMeizhiThread db query fail. error[".serialize($db->error())."] sql[".$sql."]");
			$db->rollback();
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
	
		//向redis中插入图片链表
		$thread_id = $arrInput['thread_id'];
		$arrPicInfo = $arrInput['pic_info'];
		$arrRes = self::insertPicInfo($forum_id, $thread_id, $arrPicInfo);
	
		if( false === $arrRes || $arrRes['err_no'] != Tieba_Errcode::ERR_SUCCESS ){
			Bingo_Log::warning("insertPicInfo . input[$forum_id|$thread_id|".serialize($arrPicInfo)."] output[".$arrRes."]");
			$db->rollback();
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
	
		$db->commit();
	
	
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
		);
		return $arrOutput;
	}

	/**
	 * @brief
	 * @param
	 * @return
	 */
	public static function updateMeizhiThread($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['req'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$arrInput = Tieba_Service::getArrayParams($arrInput, 'req');
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
		$arrOutput = false;
	
		//your code here......
		$db = self::_getDB();
		if( is_null($db) ){
			Bingo_Log::warning("get db fail.");
			return false;
		}
	
		Bingo_Log::warning(serialize($db));
	
		$db->startTransaction();
	
		$forum_id = $arrInput['forum_id'];
		$user_id = $arrInput['user_id'];
		$thread_id = $arrInput['thread_id'];
		$old_thread_id = $arrInput['old_thread_id'];
		$now_time = time();
		$table_name=self::_getTableName($forum_id);
	
		$sql = "UPDATE $table_name SET forum_id = $forum_id, thread_id = $thread_id, thread_time = $now_time, last_post_time = $now_time, user_id = $user_id, is_del = 0 WHERE thread_id = $old_thread_id";
	
		$res = $db->query($sql);
		if(false === $res){
			$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
			Bingo_Log::warning("insertMeizhiThread db query fail. error[".serialize($db->error())."] sql[".$sql."]");
			$arrOutput = array(
					'errno' => $error,
					'errmsg' => Tieba_Error::getErrmsg($error),
					'data' => $res,
			);
	
			$db->rollback();
			return false;
		}
	
		$sql = "UPDATE user_info SET forum_id = $forum_id, thread_id = $thread_id, last_time = $now_time, del_time = $now_time, user_id = $user_id, is_del = 0 WHERE thread_id = $old_thread_id";
		$res = $db->query($sql);
		if(false === $res){
			$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
			Bingo_Log::warning("insertMeizhiThread db query fail. error[".serialize($db->error())."] sql[".$sql."]");
			$arrOutput = array(
					'errno' => $error,
					'errmsg' => Tieba_Error::getErrmsg($error),
					'data' => $res,
			);
			$db->rollback();
			return false;
		}
	
	
		//向redis中插入图片链表
		$arrPicInfo = $arrInput['pic_info'];
	
		$arrRes = self::updatePicInfo($thread_id, $old_thread_id, $arrPicInfo);
	
		if( false === $arrRes ){
			Bingo_Log::warning('call dl updatePicInfo failed.');
			$error = Tieba_Errcode::ERR_DL_CALL_FAIL;
			$arrOutput = array(
					'errno' => $error,
					'errmsg' => Tieba_Error::getErrmsg($error),
					'res' => $arrOutput,
			);
			$db->rollback();
			return $arrOutput;
		}
	
		$db->commit();
	
	
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'data' => $res,
		);
		return $arrOutput;
	}

	/**
	 * @brief
	 * @param
	 * @return
	 */
	private static function insertPicInfo($forum_id, $thread_id, $arrPicInfo){
		return true;
//		$redis = self::_getRedis();
//		$res = $redis->HSET(array('key'=> strval($thread_id), 'field'=>'pic_info', 'value' => serialize($arrPicInfo)));
//		return $res;
	}

	/**
	 * @brief
	 * @param
	 * @return
	 */
	 private static function updatePicInfo($thread_id, $old_thread_id, $arrPicInfo){
		 return true;
//		$redis = self::_getRedis();
//
//		$res = $redis->HGETALL(array('key'=> strval($old_thread_id)));
//		Bingo_Log::warning(serialize($res));
//
//		if( $res['err_no'] != Tieba_Errcode::ERR_SUCCESS ){
//			$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
//			Bingo_Log::warning("Redis HGETALL query fail. error[".serialize($res)."]");
//			return false;
//
//		}
//		// output : a:3:{s:3:"ret";a:1:{i:7326249;a:1:{i:0;a:2:{s:5:"field";s:8:"pic_info";s:5:"value";s:2:"N;";}}}s:6:"err_no";i:0;s:7:"err_msg";s:2:"OK";}
//		//	Bingo_Log::warning(serialize($output));
//		$arrData = $res['ret'][strval($old_thread_id)];
//
//		$flag = 0;
//		$arrDelFields = array();
//		foreach( $arrData as $key => $value ){
//			Bingo_Log::warning(serialize($value));
//			if( 'pic_info' == $value['field'] ){
//				$flag = 1;
//				$value['value'] = serialize($arrPicInfo);
//				$arrData[$key] = $value;
//
//			}
//			$arrDelFields[] = $value['field'];
//		}
//		if( 0 === $flag ){
//			$arrData[] = array( 'field' => 'pic_info', 'value' => serialize($arrPicInfo));
//		}
//
//		$input = array(
//				'key' => strval($thread_id),
//				'fields' => $arrData,
//
//		);
//		//	Bingo_Log::warning(serialize($arrData));
//		$res = $redis->HMSET($input);
//		Bingo_Log::warning(serialize($res));
//
//		if( $res['err_no'] != Tieba_Errcode::ERR_SUCCESS ){
//			$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
//			Bingo_Log::warning("Redis HGETALL query fail. error[".serialize($res)."]");
//			return false;
//
//		}
//
//		Bingo_Log::warning(serialize($arrDelFields));
//		if( !empty($arrDelFields) ){
//			$input = array(
//					'key' => strval($old_thread_id),
//					'field' => $arrDelFields,
//			);
//			Bingo_Log::warning(serialize($input));
//			$res = $redis->HDEL($input);
//			Bingo_Log::warning(serialize($res));
//			if( $res['err_no'] != Tieba_Errcode::ERR_SUCCESS ){
//				$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
//				Bingo_Log::warning("Redis HGETALL query fail. error[".serialize($res)."]");
//				return false;
//			}
//			Bingo_Log::warning(serialize($res));
//		}
//
//		return $res;
	}
	
	/**
	 * @brief
	 * @param arrInput:
	 * 	uint32_t uid
	 * @return: $arrOutput
	 **/
	public static function getUserInfoByUid($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['user_id'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$user_id = intval($arrInput['user_id']);
		$force_master = isset($arrInput['force_master'])?intval($arrInput['force_master']):0;
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
	
		//your code here......
		$db = self::_getDB();
		if( is_null($db) )
			return false;
	
		$sql = "SELECT user_id, forum_id, thread_id, is_del, del_time, level, last_vote_num, new_exp, last_time, status FROM user_info WHERE user_id = $user_id ";
		if($force_master){
			$sql .= " FOR UPDATE";
		}
		$res = $db->query($sql);
	
	
		if( $res !== false ){
			$error = Tieba_Errcode::ERR_SUCCESS;
		}else{
			$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
			Bingo_Log::warning("getUserInfoByUid db query fail. error[".serialize($db->error())."] sql[$sql]");
		}
	
	
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'data' => $res,
		);
		return $arrOutput;
	}	
	
	/**
	 * @brief
	 * @param arrInput:
	 * 	uint32_t uid
	 * @return: $arrOutput
	 **/
	public static function getUserInfoByThreadID($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['thread_id'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$thread_id = intval($arrInput['thread_id']);
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
	
		//your code here......
		$db = self::_getDB();
		if( is_null($db) )
			return false;
	
		$sql = "SELECT * FROM user_info WHERE thread_id = $thread_id";
		Bingo_Log::warning($sql);
		$res = $db->query($sql);
	
	
		if( $res !== false ){
			$error = Tieba_Errcode::ERR_SUCCESS;
		}else{
			$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
			Bingo_Log::warning("getUserInfoByUid db query fail. error[".serialize($db->error())."]");
		}
	
	
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'data' => $res,
		);
	
		return $arrOutput;
	}

	/**
	 * @brief
	 * @param
	 * @return
	 */
	public static function getMeizhiThreadID($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['forum_id']) || !isset($arrInput['thread_type_lower']) ){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$forum_id = intval($arrInput['forum_id']);
		$thread_type_lower = intval($arrInput['thread_type_lower']);
		$thread_type_upper = intval($arrInput['thread_type_upper']);
		$page_no = intval($arrInput['page_no']);
		$page_size = intval($arrInput['page_size']);
		$row_no = ($page_no - 1) * $page_size;
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
	
		//your code here......
		$db = self::_getDB();
		if( is_null($db) )
			return false;
	
		$table_name=self::_getTableName($forum_id);
		
		$sql = "SELECT thread_id FROM $table_name 
					WHERE forum_id = $forum_id AND 
						  (thread_type BETWEEN $thread_type_lower AND $thread_type_upper) AND 
						  is_del = 0 AND 
						  status & ".self::_getStatusMask()." = 0 
					ORDER BY last_post_time DESC 
					LIMIT $row_no, $page_size";
		
		$res = $db->query($sql);
	
	
		if( $res !== false ){
			$error = Tieba_Errcode::ERR_SUCCESS;
		}else{
			$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
			Bingo_Log::warning("getMeizhiThreadID db query fail. error[".serialize($db->error())."]");
		}
	
		$arrRes = array();
		foreach($res as $value){
			$arrRes[] = $value['thread_id'];
		}
	
	
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'data' => $arrRes,
		);
		return $arrOutput;
	}

	/**
	 * @brief
	 * @param
	 * @return
	 */
	public static function getPicInfo($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['thread_ids'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		if( empty($arrInput['thread_ids']) ){
			$error = Tieba_Errcode::ERR_SUCCESS;
			$arrOutput = array(
					'errno' => $error,
					'errmsg' => Tieba_Error::getErrmsg($error),
					'data' => NULL,
			);
			return $arrOutput;
		}
	
		//input params.
		$arrThreadID = $arrInput['thread_ids'];
		$forum_id = $arrInput['forum_id'];
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
	
		//your code here......
//		$redis = self::_getRedis();
//		if($redis === NULL){
//			Bingo_Log::warning("redis init error");
//			return self::_errRet(Tieba_Errcode::ERR_CACHE_CONN_FAIL);
//		}
		
		
		$arrOutput = array();
		$cnt = count($arrThreadID);
		for( $i = 0; $i < $cnt; $i += 2 ){
			$arrInput = array();
			$arrInput[] = array( 'key' => strval($arrThreadID[$i]), 'field' => 'pic_info' );
			if($i + 1 < $cnt){
				$arrInput[] = array( 'key' => strval($arrThreadID[$i + 1]), 'field' => 'pic_info' );
			}
				
			$arrInput = array( 'reqs' => $arrInput );
				
//			$res = $redis->HGET($arrInput);
			$res['err_no'] = Tieba_Errcode::ERR_REDIS_CONN_FAIL;
				
			if( $res['err_no'] != Tieba_Errcode::ERR_SUCCESS ){
				//Bingo_Log::warning("getPicInfo failed. input[".serialize($arrInput)."] output [". serialize($res)."]");
			}
			else{
				$arrOutput[] = $res['ret'];
			}
		}
		
		$arrPicData = array();
		foreach( $arrOutput as $arrPicInfo){
			foreach( $arrPicInfo as $key => $value){
				$pic_info = unserialize($value);
				$arrPicData[$key] = $pic_info;
			}
		}
		
	
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'data' => $arrPicData,
		);
		
		return $arrOutput;
	
	}


	/**
	 * @brief
	 * @param
	 * @return
	 */
	public static function getMeizhiRankingThreadID($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['forum_id']) || !isset($arrInput['thread_type_lower']) ){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$forum_id = intval($arrInput['forum_id']);
		$thread_type_lower = intval($arrInput['thread_type_lower']);
		$thread_type_upper = intval($arrInput['thread_type_upper']);
		$page_no = intval($arrInput['page_no']);
		$page_size = intval($arrInput['page_size']);
		$row_no = ($page_no - 1) * $page_size;
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
	
		//your code here......
		$db = self::_getDB();
		if( is_null($db) )
			return false;
	
		$table_name=self::_getTableName($forum_id);
		$sql = "SELECT user_id, thread_id FROM user_info 
					WHERE forum_id = $forum_id AND 
						  is_del = 0 AND 
						  status&".self::_getStatusMask()." = 0 
					ORDER BY level DESC, last_time DESC 
					LIMIT $row_no, $page_size";
		$res = $db->query($sql);
	
	
		if( $res !== false ){
			$error = Tieba_Errcode::ERR_SUCCESS;
		}else{
			$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
			Bingo_Log::warning("getMeizhiRankingThreadID db query fail. error[".serialize($db->error())."]");
		}
	
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'data' => $res,
		);
		return $arrOutput;
	}

	/**
	 * @brief
	 * @param
	 * @return
	 */
	public static function getMeizhiRecordCount($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['forum_id']) || !isset($arrInput['thread_type_lower']) ){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$forum_id = intval($arrInput['forum_id']);
		$thread_type_lower = intval($arrInput['thread_type_lower']);
		$thread_type_upper = intval($arrInput['thread_type_upper']);
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
	
		//your code here......
		$db = self::_getDB();
		if( is_null($db) )
			return false;
	
		$table_name=self::_getTableName($forum_id);
		$sql = "SELECT COUNT(thread_id) FROM $table_name WHERE forum_id = $forum_id AND (thread_type BETWEEN $thread_type_lower AND $thread_type_upper) AND is_del = 0";
		$res = $db->query($sql);
	
		if( $res !== false ){
			$error = Tieba_Errcode::ERR_SUCCESS;
		}else{
			$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
			Bingo_Log::warning("getMeizhiRecordCount db query fail. error[".serialize($db->error())."]");
		}
	
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'data' => $res[0]['COUNT(thread_id)'],
		);
		return $arrOutput;
	}
	
	/**
	 * @brief
	 * @param arrInput:
	 * 	uint32_t user_id
	 *  uint32_t thread_id
	 *  uint32_t forum_id
	 * @return: $arrOutput
	 **/
	public static function setStatusByBit($arrInput){
	
		// input params check;
		// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
		if(!isset($arrInput['thread_id'])||!isset($arrInput['forum_id'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		//input params.
		$user_id = intval($arrInput['user_id']);
		$thread_id = intval($arrInput['thread_id']);
		$forum_id = intval($arrInput['forum_id']);
		$intStatusValue = intval($arrInput['status']);
		$boolIsUnset = intval($arrInput['is_unset']);
	
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		//output params.
		$error = Tieba_Errcode::ERR_SUCCESS;
		
		if($boolIsUnset){
			$strStatus = " & ~$intStatusValue";
		}
		else{
			$strStatus = " | $intStatusValue"; 	
		}
	
		//your code here......
		$table = self::_getTableName($forum_id);
		$strSql = "update $table set status=status".$strStatus." where forum_id=$forum_id and thread_id=$thread_id";
		$strSql2 = "update user_info set status=status".$strStatus." where thread_id=$thread_id";
		$db = self::_getDB();
		if(is_null($db)){
			Bingo_Log::warning("call _getDB error.");
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		Bingo_Timer::start("dbquery");

		Bingo_Log::debug($strSql);
		Bingo_Log::debug($strSql2);
		$db->startTransaction();
		if($db->query($strSql)&&$db->query($strSql2)){
			$db->commit();
		}else{
			$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
			$db->rollback();
		}

		Bingo_Timer::end("dbquery");
	
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
		);
		return $arrOutput;
		
	}

	/**
	 * @brief
	 * @param
	 * @return
	 */
	private function _getStatusMask(){
		$intStatusMask = 0;
		$arrMeizhiStatusList = self::$_conf['meizhi_status_list'];
		if(empty($arrMeizhiStatusList)){
			Bingo_Log::warning("dl conf loss var.[var=meizhi_status_list]");
			return $intStatusMask;
		}
			
		foreach($arrMeizhiStatusList as $intStatusValue){
			$intStatusMask |= $intStatusValue;	
		}
		
		return $intStatusMask;
	}

}
