<?php
/**
 * User: <EMAIL>
 * Date: 2017-12-03
 * Time: 15:58
 */

class setVideoPrivateAction extends Molib_Client_BaseAction {

    const TOAST_FAIL = '服务器开小差了...';
    /**
     * @brief  get private info
     * @return array
     */
    protected function _getPrivateInfo() {


        $intVideoLogId  = intval($this->_getInput('video_log_id', 0));
        $intThreadId    = intval($this->_getInput('thread_id', 0));

        return array(
            'video_log_id'  => $intVideoLogId,
            'thread_id'     => $intThreadId,
            'check_login'   => true,
            'need_login'    => true,
        );
    }

    /**
     * @brief  check private info
     * @return bool
     */
    protected function _checkPrivate() {
        $intVideoLogId  = (int)$this->_objRequest->getPrivateAttr('video_log_id', 0);
        $intThreadId    = (int)$this->_objRequest->getPrivateAttr('thread_id', 0);
        if($intVideoLogId <= 0 || $intThreadId <= 0){
            $this->_error(Tieba_Errcode::ERR_PARAM_ERROR, Tieba_Error::getErrMsg(Tieba_Errcode::ERR_PARAM_ERROR), array(), false);
            return false;
        }
        return true;
    }


    /**
     * @brief  main stream
     * @return bool
     */
    protected function _execute() {
        
        $intUserId      = (int)$this->_objRequest->getCommonAttr('user_id', 0);
        $intVideoLogId  = (int)$this->_objRequest->getPrivateAttr('video_log_id', 0);
        $intThreadId    = (int)$this->_objRequest->getPrivateAttr('thread_id', 0);

        if($intThreadId >0){
            $arrTheadRet = Libs_Video_Nani::getThread($intThreadId);
            $thread = $arrTheadRet[$intThreadId];
            if(!isset($thread) ||  !isset($thread['video_info'])){
                //$this->_error(Tieba_Errcode::ERR_PARAM_ERROR, Tieba_Error::getErrMsg(Tieba_Errcode::ERR_PARAM_ERROR), array(), false);
                $this->_error(Tieba_Errcode::ERR_PARAM_ERROR, self::TOAST_FAIL, array(), false);
                return false;
            }
            $intPid = (int)$thread['first_post_id'];
        }
        $arrOutput = Libs_Video_Nani::setThreadPrivacy($intThreadId,$intPid,$intUserId,1);
        $strLog = sprintf("tid:%s pid:%s uid:%s output[%s]",$intThreadId,$intPid,$intUserId,serialize($arrOutput));
        $intErrno = Tieba_Errcode::ERR_SUCCESS;
        if ($arrOutput == false){
            Bingo_Log::fatal($strLog);
            $intErrno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
        }elseif($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning($strLog);
            $intErrno = $arrOutput['errno'];
        } 
        
       
        $strErrno = $intErrno == Tieba_Errcode::ERR_SUCCESS ? Tieba_Error::getErrMsg($intErrno) : self::TOAST_FAIL;
        $this->_error($intErrno, $strErrno, array(), false);

        return true;
    }
}

