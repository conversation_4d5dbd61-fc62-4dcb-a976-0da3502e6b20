<?php
    /**
     * Created by PhpStorm.
     * User: he<PERSON><PERSON>ong
     * Date: 2019/3/29
     * Time: 10:19 AM
     */
class getShareImgAction extends Molib_Client_BaseAction {

    private $_intTid = 0;
    private $_intFid = 0;
    private $_strRes = '';
    const HOME_TYPE = 1;
    const FORUM_TYPE = 2;
    const IMAGE_TYPE = 3;
    const VIDEO_TYPE = 4;

    protected function _getPrivateInfo() {
        $intThreadId = Bingo_Http_Request::getNoXssSafe('thread_id', 0);
        $intForumId  = Bingo_Http_Request::getNoXssSafe('forum_id', 0);
        $intType     = Bingo_Http_Request::getNoXssSafe('type', 0);

        return array(
            'check_login'   => false,
            'need_login'    => false,
			'ispv'          => 0,
            'type'          => $intType,
            'thread_id'     => $intThreadId,
            'forum_id'      => $intForumId,
        );
    }

    protected function _checkPrivate() {
        $intType = $this->_objRequest->getPrivateAttr('type');
        $this->_intTid = $this->_objRequest->getPrivateAttr('thread_id');
        $this->_intFid  = $this->_objRequest->getPrivateAttr('forum_id');
        if ($intType == 3 && $this->_intTid == 0) {
            Bingo_Log::warning("empty thread id");
            $error = Tieba_Errcode::ERR_MO_PARAM_INVALID;
            $this->_error($error, 'thread id is empty');
            return false;
        } else if ($intType == 2 && $this->_intFid == 0) {
            Bingo_Log::warning("empty forum id");
            $error = Tieba_Errcode::ERR_MO_PARAM_INVALID;
            $this->_error($error, 'forum id is empty');
            return false;
        }

        return true;
    }

    /**
     * @return bool
     * @throws Exception
     */
    public function _execute()
    {
        if (empty( $this->_objRequest->getPrivateAttr('type'))) {
            Bingo_Log::warning('type is empty');
            $error = Tieba_Errcode::ERR_MO_PARAM_INVALID;
            $this->_error($error, 'type error');
            return false;
        }

        switch ( $this->_objRequest->getPrivateAttr('type')) {
            case self::HOME_TYPE:
                $this->_objResponse->setOutData(array('img_url' => 'http://imgsrc.baidu.com/forum/pic/item/2982a42f070828385b29691fb699a9014d08f196.jpg'));
                return true;
            case self::FORUM_TYPE:
                return $this->_shareForum();
            case self::IMAGE_TYPE:
            case self::VIDEO_TYPE:
                return $this->_shareThread();
            default:
                Bingo_Log::warning('share type is error');
                $this->_error(Tieba_Errcode::ERR_PARAM_ERROR, 'share type error');
                return false;
        }
    }

    /**
     * @param $arrInput
     * @return bool
     */
    private function _pushToRedis($arrInput) {
        // 直接透传给go服务
        $strOutput = Tbapi_Core_Midl_Http::httpcall('service_image_theme', '/image/smallapp/share', $arrInput, 'post');
        $arrOutput = Bingo_String::json2array($strOutput);
        if ($arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call service_image_theme failed. input:'.json_encode($arrInput).' output: '.json_encode($arrOutput));
        }
        $this->_objResponse->setOutData(array('img_url' => $arrOutput['result']['pic_url']));
        return false;
    }
    /**
     * @return bool
     * @throws Exception
     */
    private function _shareForum() {
        $arrInput = array(
            "forum_id" => array(
                $this->_intFid //吧id
            ),
        );
        $ret   = Tieba_Service::call('forum', 'mgetBtxInfoEx', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $ret || $ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call forum::mgetBtxInfoEx failed. input:'.json_encode($arrInput).' output:'.json_encode($ret));
            $this->_error(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, '获取吧信息失败');
            return false;
        }
        $type = self::FORUM_TYPE;

        $arrForum = $ret['output'][$this->_intFid];
        $arrThemeColor = json_decode($arrForum['attrs']['theme_color'], true);
        $themeColor    = empty($arrThemeColor['day']['common_color']) ? '2B80FF' : $arrThemeColor['day']['common_color'];

        $arrInput = array(
            'type'          => strval($type),
            'forum_name'    => $arrForum['forum_name']['forum_name'],
            'forum_introduction'    => strval($arrForum['card']['slogan']),
            // 'member_num'    => strval($arrForum['card']['slogan']),
            'forum_avatar'  => strval($arrForum['card']['avatar']),
            'theme_color'   => $themeColor,
            'forum_id'      => strval($this->_intFid),
            'dir_1'         => $arrForum['dir']['level_1_name'],
        );
        Bingo_Log::warning('call share forum: input'. json_encode($arrInput));
        $this->_pushToRedis($arrInput);
    }

    /**
     * @return bool
     * @throws Exception
     */
    private function _shareThread() {
        $arrInput = array(
            "thread_ids" => array(
                0 => $this->_intTid
            ),
            "need_abstract" => 1,
            "forum_id"      => 0,
            "need_photo_pic" => 1,
            "need_user_data" => 1,
            "icon_size"      => 1,
            "need_forum_name" => 1, //是否获取吧名
            "call_from"      => "pc_frs" //上游模块名
        );
        $ret = Tieba_Service::call('post', 'mgetThread', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $ret || $ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call post::mgetThread failed. input:'.json_encode($arrInput).' output:'.json_encode($ret));
            $this->_error(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, '获取贴子信息失败');
            return false;
        }
        $type = self::IMAGE_TYPE;
        $arrThread = $ret['output']['thread_list'][$this->_intTid];
        $strCover = '';
        if (!empty($arrThread['video_info'])) {
            $type = self::VIDEO_TYPE;
            $strCover = $this->_getPicIdFromUrl($arrThread['video_info']['thumbnail_url']);
        }
        $arrImg = array();
        foreach ($arrThread['media'] as $img) {
            $arrImg[] = $this->_getPicIdFromUrl($img['big_pic']);
        }

        $arrUser  = $ret['output']['thread_user_list'][$arrThread['user_id']];
        $userName = empty($arrUser['user_nickname']) ? $arrUser['user_name'] : $arrUser['user_nickname'];
        $portrait = 'http://tb.himg.baidu.com/sys/portrait/item/'.Tieba_Ucrypt::encode( $arrThread['user_id'], Molib_Util_Encode::convertUTF8ToGBK( $arrUser['user_name'] ), intval( $arrUser['portrait_time'] ) ).'.jpg';

        $arrInput = array(
            'type'          => strval($type),
            'user_name'     => $userName,
            'user_avatar'   => strval($portrait),
            'forum_name'    => !empty($arrThread['forum_name']) ? $arrThread['forum_name'].'吧' : '贴吧',
            'content_text'  => $arrThread['abstract'],
            'content_image' => empty($arrImg) ? '' : json_encode($arrImg),
            'video_cover'   => strval($strCover),
            'thread_id'     => strval($this->_intTid),
            'forum_id'      => strval($arrThread['forum_id']),
        );


        $this->_pushToRedis($arrInput);
    }

    /**
     * @param $strUrl
     * @return mixed
     */
    private function _getPicIdFromUrl($strUrl){
        $arrTmp = explode('/', $strUrl); // http://imgsrc.baidu.com/forum/pic/item/c91373f082025aafd815170ffaedab64024f1aca.jpg
        $intLength = count($arrTmp);
        $strPicName = $arrTmp[$intLength - 1]; // c91373f082025aafd815170ffaedab64024f1aca.jpg
        $arrTmp2 = array_filter(explode('.', $strPicName));
        $picSign = '';
        if (isset($arrTmp2[0])) {
            $picSign = $arrTmp2[0];
        }
        $picId = Space_Urlcrypt::decodePicUrlCrypt($picSign);
        return strval($picId[1]);
    }
}