<?php
/**
 * @file: submitCancelDislikeAction.php
 * @author:
 * @datetime: 2021-01-04 10:45
 * @brief: 拉黑吧取消负反馈提交接口
 */

class submitCancelDislikeAction extends Molib_Client_BaseAction {

    const DISLIKE_TYPE = 1007; //1007代表负反馈类型是屏蔽吧,by songxing<PERSON>an

    /**
     * @param
     * @return bool
     */
    protected function _getPrivateInfo() {
        return array(
            'check_login' => false,
            'need_login' => true,
            'forum_id' => Bingo_Http_Request::getNoXssSafe('forum_id'),
        );
    }
    /**
     * @param
     * @return bool
     */
    protected function _checkPrivate() {
        return true;
    }

    /**
     * @param
     * @return bool
     */
    public function _execute() {
        $uid = $this->_objRequest->getCommonAttr('user_id');
        $cuid = $this->_objRequest->getCommonAttr('cuid');
        $intForumID = $this->_objRequest->getPrivateAttr('forum_id');
        $clientType = $this->_objRequest->getCommonAttr('client_type');
        $clientVersion = strval($this->_objRequest->getCommonAttr('client_version'));

        if(!$uid || !$cuid) {
            Bingo_Log::warning('Cannot get uid or cuid, please check your request params.');
            $error = Tieba_Errcode::ERR_PARAM_ERROR;
            $strMsg = Tieba_Error::getErrmsg($error);
            $this->_error($error, $strMsg );
            return false;
        }
        Bingo_Log::notice('uid: ' . $uid . 'cuid: ' . $cuid .',version: ' . $clientVersion .',type: ' . $clientType . ',forum id:' . $intForumID );
        if(empty($intForumID)) {
            Bingo_Log::warning('forum id should be input. Please check your request params');
            $error = Tieba_Errcode::ERR_PARAM_ERROR;
            $strMsg = Tieba_Error::getErrmsg($error);
            $this->_error($error, $strMsg );
            return false;
        }

        $arrInput = array(
            'user_id' => $uid,
            'cuid' => $cuid,
            'fid' => $intForumID,
            'display_text' => '取消屏蔽吧',
            'dislike_type' => self::DISLIKE_TYPE,
            'click_time' => time(),
            'extra' => '',
        );
        $service = 'recom';
        $ret = Molib_Tieba_Service::call($service, 'cancelDislike', $arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            $error = $ret['errno'];
            $strMsg = $ret['errmsg'];
            $this->_error($error, $strMsg);
            Bingo_Log::fatal('submit cancel dislike fail!service :' . $service . ',method: cancelDislike return error, input : ' . serialize($arrInput). ', output : ' . serialize($ret) );
            $this->_objResponse->setOutData(array('data' => true));
            return true;
        }

        //调用推荐成功，存db
        $arrInput = array(
            'uid' => $uid,
            'fid' => $intForumID,
        );
        $service = 'common';
        $arrRet = Molib_Tieba_Service::call($service, 'delDislikeList', $arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            $error = $arrRet['errno'];
            $strMsg = $arrRet['errmsg'];
            $this->_error($error, $strMsg);
            Bingo_Log::warning('submit cancel dislike fail!service :' . $service . ',method: delDislikeList return error, input : ' . serialize($arrInput). ', output : ' . serialize($arrRet) );
            $this->_objResponse->setOutData(array('data' => true));
            return true;
        }

        $this->_objResponse->setOutData(array('data' => true));
        return true;
    }

}
