<?php
/*
 * @Author: your name
 * @Date: 2020-09-07 20:34:11
 * @LastEditTime: 2020-09-09 16:27:38
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /client-commit-user/app/client-commit/actions/user/uploadOffpackAction.php
 */



class uploadOfflinePackAction extends Molib_Client_BaseAction
{
    protected function _getPrivateInfo()
    {
        return array(
            'offline_pack_version' => Bingo_Http_Request::get('offline_pack_version', ''),
        );
    }

    protected function _checkPrivate()
    {
        $strVersion = $this->_objRequest->getPrivateAttr('offline_pack_version');
        $strCuid    = $this->_objRequest->getCommonAttr('cuid', '');

        if (empty($strVersion) || empty($strCuid)) {
            $error = Tieba_Errcode::ERR_MO_PARAM_INVALID;
            $this->_buildErr($error);
            return false;
        }

        return true;
    }

    private function _buildErr($error)
    {
        $this->_error($error, Molib_Client_Error::getErrMsg($error));
    }

    protected function _execute()
    {
        $strVersion = $this->_objRequest->getPrivateAttr('offline_pack_version');
        $strCuid    = $this->_objRequest->getCommonAttr('cuid', '');
        $strUserId    = $this->_objRequest->getCommonAttr('user_id', '');
        $strClientType    = $this->_objRequest->getCommonAttr('client_type', 0);
        $strClientVersion    = $this->_objRequest->getCommonAttr('client_version', '');
        $strOsVersion    = $this->_objRequest->getCommonAttr('os_version', '');
        $strBrand    = $this->_objRequest->getCommonAttr('brand', '');
        $strModel    = $this->_objRequest->getCommonAttr('model', '');

        if (empty($_FILES['offline_pack_file_stream']) || $_FILES['offline_pack_file_stream']['error'] != 0) {
            Bingo_Log::warning(sprintf('uploadOfflinePack failed! client upload error! errno[%s]', $_FILES['offline_pack_file_stream']['error']));
            $error = Tieba_Errcode::ERR_CHUNK_UPLOAD_FAIL;
            $this->_buildErr($error);
            return false;
        }
        
        $intSize = $_FILES['offline_pack_file_stream']['size'];
        
        if ($intSize >= 2048 * 2048) {
            Bingo_Log::warning(sprintf('uploadOfflinePack failed! file size exceed size[%s]', $intSize));
            $error = Tieba_Errcode::ERR_CHUNK_UPLOAD_FAIL;
            $this->_buildErr($error);
            return false; 
        }

        $arrTaskInput = array(
            'cuid' => $strCuid,
            'status' => 0,
        );

        $arrTaskOutput = Tieba_Service::call('common', 'getOneTaskInfoByCuidAndStatus', $arrTaskInput, null, null, 'post', 'php', 'utf-8');
        if (!$arrTaskOutput || $arrTaskOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf("call common::getOneTaskInfoByCuidAndStatus failed! input[%s] output[%s]", json_encode($arrTaskInput), json_encode($arrTaskOutput)));
            $this->_buildErr(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return false;
        }

        $intTaskId = $arrTaskOutput['data']['0']['id'];
        if ($intTaskId <= 0) {
            Bingo_Log::warning(sprintf("get task id failed! output[%s]", json_encode($arrTaskOutput)));
            $this->_buildErr(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return false;
        }
        $strModName = $arrTaskOutput['data']['0']['mod_name'];

       // $intSize = $_FILES['offline_pack_file_stream']['size'];

        $strPath = $_FILES['offline_pack_file_stream']['tmp_name'];
        $strContent = file_get_contents($strPath);

        if(!empty($strModName)){
            $strPackPostfix = sprintf('/pack_%s.zip', $strModName);
        }
        else{
            $strPackPostfix = '/pack.zip';
        }
        $strFinalPath = $intTaskId . $strPackPostfix;  

        $bolOk = Libs_Logic_Bos::uploadFile($strContent, $strFinalPath);
        if (!$bolOk) {
            Bingo_Log::warning(sprintf("upload offpack failed! cuid[%s] version[%s] final[%s]", $strCuid, $strVersion, $strFinalPath));
            $error = Tieba_Errcode::ERR_CHUNK_UPLOAD_FAIL;
            $this->_buildErr($error);
            return false; 
        }

        $arrUserInfo = array(
            'date' => date("Y-m-d H:i"),
            'app_info' => array(
                'client_version' => $strClientVersion,
                'offpack_version' => $strVersion,
            ),
            'phone_info' => array(
                'brand' => $strBrand,
                'model' => $strModel,
                'client_type' => $strClientType,
                'system_version' => $strOsVersion,
            ),
        );
        $strUserInfo = sprintf("%s", json_encode($arrUserInfo, JSON_PRETTY_PRINT));

        if(!empty($strModName)){
            $strInfoTxtPostfix = sprintf('/info_%s.txt', $strModName);
        }
        else{
            $strInfoTxtPostfix = '/info.txt';
        }
        $strFinalPath = $intTaskId . $strInfoTxtPostfix; 
        $bolOk = Libs_Logic_Bos::uploadFile($strUserInfo, $strFinalPath);
        if (!$bolOk) {
            Bingo_Log::warning(sprintf("upload user info failed! cuid[%s] version[%s] final[%s]", $strCuid, $strVersion, $strFinalPath));
            $error = Tieba_Errcode::ERR_CHUNK_UPLOAD_FAIL;
            $this->_buildErr($error);
            return false; 
        }

        $this->_objResponse->setOutData(array());
        return true;
    }
}
