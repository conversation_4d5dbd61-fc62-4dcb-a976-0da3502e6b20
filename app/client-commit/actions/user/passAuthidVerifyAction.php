<?php
/*
 * @Author: your name
 * @Date: 2020-08-13 14:39:58
 * @LastEditTime: 2020-08-14 16:39:34
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /client-commit-user/app/client-commit/actions/user/passAuthidVerifyAction.php
 */

/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

class passAuthidVerifyAction extends Molib_Client_BaseAction
{
    protected function _getPrivateInfo()
    {
        return array(
            'authid' => Bingo_Http_Request::get('authid', ''),
            'scene'  => Bingo_Http_Request::get('scene', ''), // tieba_auth
        );
    }

    protected function _checkPrivate()
    {
        $strAuthid = $this->_objRequest->getPrivateAttr('authid');
        $strScene  = $this->_objRequest->getPrivateAttr('scene');
        $intUid    = $this->_objRequest->getCommonAttr('user_id', 0);

        if (empty($strAuthid) || empty($strScene) || empty($intUid)) {
            $error = Tieba_Errcode::ERR_MO_PARAM_INVALID;
            $this->_buildErr($error);
            return false;
        }

        return true;
    }

    protected function _execute()
    {
        $arrInput = array(
            'tpl'    => 'tieba',
            'authid' => $this->_objRequest->getPrivateAttr('authid'),
        );

        ral_set_pathinfo('/v3/api/auth/widgetstatus');
        ral_set_logid(Bingo_Log::getLogId());

        $arrOutput = ral('passport_name', 'post', $arrInput, rand());

        $arrOutput = Bingo_String::json2array($arrOutput);

        $arrRet = array(
            'err_no'  => 0,
            'err_msg' => 'success',
            'data'    => array(
                'status' => 'ok',
            ),
        );

        if ($arrOutput['data']['idType'] != 'userid') {
            $error = Tieba_Errcode::ERR_MO_PARAM_INVALID;
            $this->_buildErr($error);
            return false;
        }

        if ($this->_objRequest->getCommonAttr('user_id', 0) != $arrOutput['data']['userid']) {
            $arrRet['data']['status'] = 'fail';
        }

        if ($arrOutput['data']['scene'] != $this->_objRequest->getPrivateAttr('scene') || $arrOutput['data']['status'] != true) {
            $arrRet['data']['status'] = 'fail';
        }

        $this->_objResponse->setOutData($arrRet);
        return true;
    }

    private function _buildErr($error)
    {
        $this->_error($error, Molib_Client_Error::getErrMsg($error));
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
