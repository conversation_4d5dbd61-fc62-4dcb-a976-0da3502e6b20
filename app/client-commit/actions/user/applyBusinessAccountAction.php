<?php

/**
 * 申请商户号
 * Class applyBusinessAccountAction
 */

class applyBusinessAccountAction extends Molib_Client_BaseAction {

    private $_intUserId;

    private $_intFid;

    private $_intStatus;

    private $_objMulti;

    const SUBMIT_SUC = 1;
    const NOT_BUSINESS = -1;
    const OVER_LIMIT = -2;
    const STATUS_DEL = 8;
    const STATUS_SUCC = 7;

    /**
     * 根据请求参数设置私有信息
     * @return array
     */
    public function _getPrivateInfo(){
        $arrPrivate = array(
            'check_login' 		=> true,
            'ispv'				=> false,
            'fid' 	            => intval($this->_getInput('fid', -1)),
            'status'			=> intval($this->_getInput('status', 0)),
            'op_uid'            => strval($this->_getInput('op_uid', '')),
            'id'                => intval($this->_getInput('id', '')),
            'refuse_msg'        => strval($this->_getInput('refuse_msg', '')),
        );
        return $arrPrivate;
    }

    /**
     * 请求参数检查
     * @return bool
     */
    public function _checkPrivate(){
        //tbs校验
        $strTbs = strval($this->_getInput('tbs',''));
        $bolTbs = Tieba_Tbs::check($strTbs,true);
        if(false == $bolTbs)
        {
            $errno = Tieba_Errcode::ERR_MO_INTERNAL_ERROR;
            $this->_error($errno, $this->_getUserMsg($errno));
            Bingo_Log::warning('tbs invalid['.$strTbs.']');
            return false;
        }
        return true;
    }

    /**
     * UI主函数
     * @return bool
     */
    public function _execute(){
        $this->_intUserId = $this->_objRequest->getCommonAttr('user_id');
        $this->_intFid = $this->_objRequest->getPrivateAttr('fid');
        $this->_intStatus = $this->_objRequest->getPrivateAttr('status');
        $refuseMsg = $this->_objRequest->getPrivateAttr('refuse_msg');
        $id = $this->_objRequest->getPrivateAttr('id');
        $opUid = $this->_objRequest->getPrivateAttr('op_uid');
        $dataRes = array();

        if ($id == 0 && $this->_intStatus == 0) {
            if (!$this->_multiCall()) {
                Bingo_Log::warning('applyForumAction : multi call failed');
                return false;
            }
            // 判断用户是不是商户号
            if ( !self::_checkIfBusiness()) {
                Bingo_Log::warning("applyForumAction : the user is not business");
                $dataRes['is_success'] = self::NOT_BUSINESS;
                $dataRes['msg'] = '非商户号不能申请入驻吧';
                $this->_buildRes(Tieba_Errcode::ERR_SUCCESS, $dataRes);
                return true;
            }
            // 判断用户申请入驻的吧是不是已经超过次数了
            if ( !self::_checkIfOverAppy()) {
                Bingo_Log::warning("applyForumAction : the business user has applied over limit times");
                $dataRes['is_success'] = self::OVER_LIMIT;
                $dataRes['msg'] = "您申请入驻的吧已经超出限制";
                $this->_buildRes(Tieba_Errcode::ERR_SUCCESS, $dataRes);
                return true;
            }

            $input = array(
                'user_id'   => $this->_intUserId,
                'forum_id'  => $this->_intFid,
            );
            $addRes = Tieba_Service::call('common', 'addForumBusiness', $input, null, null, 'post', 'php', 'utf-8');
            if (Tieba_Errcode::ERR_SUCCESS != $addRes['errno']) {
                Bingo_Log::warning("applyForum: addForumBusiness failed, input=" . serialize($input) . ", output=" . serialize($addRes));
                $this->_buildRes(Tieba_Errcode::ERR_MO_INTERNAL_ERROR, $addRes);
                return false;
            }
            $dataRes['is_success'] = self::SUBMIT_SUC;
            $dataRes['msg'] = '您的申请已经成功提交审核';
            $this->_buildRes(Tieba_Errcode::ERR_SUCCESS, $dataRes);
            return true;
        } else {
            // 编辑, 进入nmq
            if ($this->_intStatus == self::STATUS_DEL) {
                // 获取数据ID
                $input = array(
                    'status'    => self::STATUS_SUCC,
                    'user_id'   => $this->_intUserId,
                    'forum_id'  => $this->_intFid,
                );

                $res = Tieba_Service::call('common', 'getForumBusinessList', $input, null, null, 'post', 'php', 'utf-8');
                if (false == $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning("applyForum: getForumBusinessList failed, input=" . serialize($input) . ", output=" . serialize($res));
                    $this->_buildRes(Tieba_Errcode::ERR_MO_INTERNAL_ERROR, $res);
                    return false;
                }
                $info = $res['data']['list'][0];
                $id = $info['id'];
            }
            if ($this->_intStatus == 5  || $this->_intStatus == 6) {
                $opUid = $this->_intUserId;
            }
            $input = array(
                'id'            => $id,
                'status'        => $this->_intStatus,
                'refuse_msg'    => $refuseMsg,
                'op_uid'        => $opUid,
            );
            if ( !self::_pushNmq($input)) {
                Bingo_Log::warning("applyForum: push to nmq failed, input=" . serialize($input));
                $this->_buildRes(Tieba_Errcode::ERR_MO_INTERNAL_ERROR, null);
                return false;
            }
            $dataRes['is_success'] = self::SUBMIT_SUC;
            $dataRes['msg'] = '成功提交';
            $this->_buildRes(Tieba_Errcode::ERR_SUCCESS, $dataRes);
            return true;
        }
        return true;
    }

    /**
     * 发送nmq
     * @param $arrInput
     * @return bool
     */
    private static function _pushNmq($arrInput) {
        $strTopic = "tbmall";
        //隶属于topic的命令名，长度必须小于16个字节
        $strCmd = "editBusiness";
        //调用commit库发送nmq
        $arrOutput = Tieba_Commit::commit($strTopic, $strCmd, $arrInput);

        //发送失败，打错误日志
        if(false === $arrOutput) {
            Bingo_Log::warning("apply forum Tieba_Commit::commit nmq error，topic[$strTopic] cmd[$strCmd] input=[".serialize($arrInput)."] output=[".serialize($arrOutput)."]");
            return false;
        }
        return true;
    }

    private function _buildRes($errno, $data) {
        $res = array(
            'no'        => $errno,
            'error'     => Molib_Client_Error::getErrMsg($errno),
            'data'      => $data,
        );
        $this->_objResponse->setOutData($res);
    }

    /**
     * 检查用户是不是商户号
     * @return bool
     */
    private function _checkIfBusiness() {
        $arrUserInfo =  $this->_objMulti->getResult('user:getUserData');
        if (Tieba_Errcode::ERR_SUCCESS != $arrUserInfo['errno']) {
            Bingo_Log::warning('call user:getUserData failed.  [sevice_name:user] [method:getUserData] [uid:' . $this->_intUserId . '] [output:' . serialize($arrUserInfo) . ']');
        }
        if ( !empty($arrUserInfo['user_info']) && isset($arrUserInfo['user_info'][0]['business_account']) && $arrUserInfo['user_info'][0]['business_account']['status'] == 1) {
            return true;
        }
        return false;
    }

    private function _checkIfOverAppy() {
        $limit = self::_getBusinessEntryLimit();
        if ($limit == false) {
            Bingo_Log::warning("get business_entry_forum_limit from redis failed, fid=" . $this->_intForumId . ", and uid=" . $this->_intUserId);
            return false;
        }
        $countRes = $this->_objMulti->getResult('common:getApplyCount');
        if (Tieba_Errcode::ERR_SUCCESS != $countRes['errno']) {
            Bingo_Log::warning('call common:getApplyCount failed.  [sevice_name:common] [method:getApplyCount] [uid:' . $this->_intUserId . '] [output:' . serialize($countRes) . ']');
            return false;
        }
        if ($limit <= intval($countRes['data']['entry_cnt'])) {
            return false;
        }
        return true;
    }

    /**
     * 获取商户号入驻吧的个数限制
     */
    private function _getBusinessEntryLimit() {
        $objWordServer = Wordserver_Wordlist::factory();
        $arrInput  = array(
            'business_entry_forum_limit',
        );
        $redisRes   = $objWordServer->getValueByKeys($arrInput, 'tb_wordlist_redis_BusinessAccount');
        if ( !empty($redisRes['business_entry_forum_limit'])) {
            return intval($redisRes['business_entry_forum_limit']);
        }

        return false;
    }


    /**
     * multi call
     * @return bool
     */
    private function _multiCall() {

        $arrServices = array();
        // 获取商户号信息，看看用户是不是商户号
        $arrServices['user']['getUserData'] = array(
            'user_id'  => $this->_intUserId,
        );
        // 已经入驻的个数
        $arrServices['common']['getApplyCount'] = array(
            'user_id'  => $this->_intUserId,
        );

        $this->_objMulti = new Tieba_Multi("client_apply_forum_multi_call_service");

        foreach ($arrServices as $strService => $arrService) {
            foreach ($arrService as $strMethod => $arrMultiInput) {
                $strKey = $strService . ':' . $strMethod;
                $arrInput = array(
                    'serviceName' => $strService,
                    'method'      => $strMethod,
                    'ie'          => 'utf-8',
                    'input'       => $arrMultiInput,
                );
                $this->_objMulti->register($strKey, new Tieba_Service($strService), $arrInput);
            }
        }

        Bingo_Timer::start('multi_call');
        $this->_objMulti->call();
        Bingo_Timer::end('multi_call');
        return true;
    }
}
