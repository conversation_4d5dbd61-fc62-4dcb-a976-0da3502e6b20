<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file setprivate.php
 * <AUTHOR>
 * @date 2014-05-09 11:35:24
 * @brief 
 *  
 **/

class setprivateAction extends Molib_Client_BaseAction {

	const USER_ATTR_NAME = 'priv_sets';
    private static $_strClientVersion;
    private static $_arrOpts = array(
        'location'  => true,//位置
        'like'      => true,//关注的吧
        'group'     => true,//群组
        'friend'    => true,//好友
        'post'      => true,//贴子
        'live'      => true,//直播
        'reply'     => true,//贴子评论权限
        'bazhu_show_inside'     => true,//吧主认证在吧内展示
        'bazhu_show_outside'     => true,//吧主认证在吧外展示
    );
    private static $_arrVals = array(
        1 => true,//if(key=1) 完全公开
        2 => true,//if(key=2) 好友可见
        3 => true,//if(key=3) 完全隐藏
        4 => true,//if(key=4) 隐藏部分吧的帖子
        5 => true,//if(key=5) 我的粉丝（评论权限）
        6 => true,//if(key=6) 我的关注（评论权限）
        7 => true,//if(key=7) 仅我自己（评论权限）
    );
        
    protected function _getPrivateInfo() {
        return array(
            'check_login'=>true,
            'need_login'=>true,
            'opt' => Bingo_Http_Request::get('opt', array()),
            'val' => Bingo_Http_Request::get('val', array()),
            'tid' => Bingo_Http_Request::get('tid', 0),
        	'ispv' => 0,
            'isuv' => 1,
        );
    }

    protected function _checkPrivate() {
        self::$_strClientVersion = strval($this->_objRequest->getCommonAttr('client_version'));
        //检查参数
        //opt只能在允许的范围内
        $strOpt = $this->_objRequest->getPrivateAttr('opt');
        if(true !== self::$_arrOpts[$strOpt]){
            Bingo_Log::warning("err opt. [opt:".serialize($strOpt)."]");
            $error = Tieba_Errcode::ERR_MO_PARAM_INVALID;
            $this->_error($error, Molib_Client_Error::getErrMsg($error));
           return false;
        }

        //val只能在允许的范围内
        $intVal = $this->_objRequest->getPrivateAttr('val');
        if(true !== self::$_arrVals[$intVal]){
            Bingo_Log::warning("err val. [val:".serialize($intVal)."]");
            $error = Tieba_Errcode::ERR_MO_PARAM_INVALID;
            $this->_error($error, Molib_Client_Error::getErrMsg($error));
           return false;
        }
        if($intVal == 7)
        {
           //V12.4创作者控评，只有白名单用户可以设置7-仅我自己。
            if(Molib_Util_Version::compare(self::$_strClientVersion, '12.4.0') <= 0)
            {
                $if_wu = self::_ifWhiteUidList();
                return $if_wu;  
            } 
        }
        return true;
    }

    protected function _execute() {


        $strOpt = $this->_objRequest->getPrivateAttr('opt');
        $intVal = $this->_objRequest->getPrivateAttr('val');
        $intUserId = $this->_objRequest->getCommonAttr('user_id');
        $intTid = $this->_objRequest->getPrivateAttr('tid');
        //设置用户扩展属性
        //get data
        $arrInputGetUserInfo = array(
                                     'user_id'  => $intUserId,
                                     );
        $arrUserInfo = self::_getUserInfo($arrInputGetUserInfo);

        if(false == $arrUserInfo){
            Bingo_Log::warning("fail getUserInfo. [input:".serialize($arrInputGetUserInfo)."]");
            $error = Tieba_Errcode::ERR_MO_PARAM_INVALID;
            $this->_error($error, Molib_Client_Error::getErrMsg($error));
            return false;
        }
        //非吧主不可设置吧主相关隐私字段
        if (empty($arrUserInfo['bazhu_grade']) && in_array($strOpt, ['bazhu_show_inside', 'bazhu_show_outside'])) {
            Bingo_Log::warning("The current user has no permissions err opt. [val:" . serialize($strOpt) . "]");
            $error = Tieba_Errcode::ERR_MO_PARAM_INVALID;
            $this->_error($error, Molib_Client_Error::getErrMsg($error));
            return false;
        }

        //change data
        $arrPrivSets = $arrUserInfo['priv_sets'];
        //统计日志 之前的值
        Tieba_Stlog::addNode('obj_source',$arrPrivSets[$strOpt]);
        $arrPrivSets[$strOpt] = $intVal;

        //统计日志
        Tieba_Stlog::addNode('obj_name',$strOpt);
        Tieba_Stlog::addNode('obj_to',$intVal);

        //V12.4创作者控评，白名单用户可以设置PB的pbreply_private_flag动态属性
        //Bingo_Log::warning("intTid===========".$intTid);
        //Bingo_Log::warning("intVal===========".$intVal);
        if($intTid>0 && $intVal > 0)
        {
            if(Molib_Util_Version::compare(self::$_strClientVersion, '12.4.0') <= 0) 
            {
                $if_wu = self::_ifWhiteUidList();
                if($if_wu)
                {
                    $setPostReq = array(
                        "input" => array( // 输入参数
                            array(
                                "tid" => $intTid,
                                "fields" => array(
                                    array(
                                        "fkey" => "pbreply_private_flag",
                                        "value" => $intVal,
                                    ),
                                ),
                            ),
                        ),
                    );
                    $setPostRet = Tieba_Service::call('post', 'setKeyInThreadInfo', $setPostReq, null,null, 'post', 'php', 'utf-8');
                    //Bingo_Log::warning("setreplayprivateret===".var_export($setPostRet,1));
                    if (false === $setPostRet || Tieba_Errcode::ERR_SUCCESS != $setPostRet['errno']) 
                    {
                        Bingo_Log::warning('call common setUserPrivStat failed. [input:'.serialize($setPostReq).' ] [output: '.serialize($setPostRet).' ]');
                    }
                }
            }
            return true;
        }

        //set data
        //更新用户扩展属性 -----//
        $arrInputSet = array(
                                'user_id'   => $this->_objRequest->getCommonAttr('user_id'), 
                                'priv_sets'  => $arrPrivSets,
                             );
        $arrOutput = self::_setUserPrivSets($arrInputSet);
        
        //----- 更新用户扩展属性//

        if( Tieba_Errcode::ERR_SUCCESS == $arrOutput['errno']){
            if ($strOpt == 'reply') {
                $arrInput = array(
                    'user_id'   =>  $this->_objRequest->getCommonAttr('user_id'),
                    'opt'       =>  'reply',
                    'val'       =>  $intVal,
                );
                $arrOutput = Tieba_Service::call('common', 'setUserPrivStat', $arrInput, null, null, 'post', 'php', 'utf-8');
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                    Bingo_Log::warning('call common setUserPrivStat failed. [input: '.serialize($arrInput).' ] [output: '.serialize($arrOutput).' ]');
                }
            }
            if($strOpt == 'post' && $intVal!=4){
                $arrInput = array(
                    'user_id' => $this->_objRequest->getCommonAttr('user_id'),
                    'attr_name'=>'forbidden_forumlist',
                    'attr_value'=>'',
                );

                $arrOutput = Tieba_Service::call('user','setUserAttr',$arrInput);
                if(false === $arrOutput || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('call setUserAttr fail input is'.$arrInput.' output is'.$arrOutput);
                }
                $input = array(
                    'user_id' => $this->_objRequest->getCommonAttr('user_id'), 
                );
                $output = Tieba_Service::call('common','delForumByUid',$input);
                if(false===$output || $output['errno'] != Tieba_Errcode::ERR_SUCCESS){
                    Bingo_Log::warning('delete private forum faile input is '.$user_id.'output is'.serialize($output));
                }
            }
            return true;
        }else{
            $error = $arrOutput['errno'];
            $this->_error($error, Molib_Client_Error::getErrMsg($error));
            return false;
        }
    }


    private static function _getImageSign($arrImageUrls){
        if(empty($arrImageUrls)){
            return array();
        }
        //static $SPACE_IMG_SRC = 'bb-space-tests00.vm.baidu.com:8090'; //测试用这个地址
        static $SPACE_IMG_SRC = 'imgsrc.baidu.com';

        $arrCryptIds = array();
        $arrRet = array();
        if(!empty($arrImageUrls)){
            foreach ($arrImageUrls as $imageUrl){
                if(strncasecmp($imageUrl, 'http://'.$SPACE_IMG_SRC, strlen('http://'.$SPACE_IMG_SRC)) === 0){
                    if(preg_match('/^http:\/\/'.$SPACE_IMG_SRC.'\/[^\.]+\.[a-z]{1,4}$/i',$imageUrl) !== 1){
                        Bingo_log::warning('the space imge ulr is illegal, post reject!');
                    }else{
                        $ret = preg_match('/([0-9a-z]+).[a-z]{1,4}$/i', $imageUrl, $match);
                        if($ret === 1){
                            $arrCryptIds[] = $match[1];	    				
                        }
                    }
            }
            }
        }    	

        return $arrCryptIds;
    }


	private static function _setUserPrivSets($arrInput){

		$intUserId      = $arrInput['user_id'];
		$arrPrivSets    = $arrInput['priv_sets'];


		//Tieba_Service:call
		$strServiceName = 'user';
		$strMethod = 'setUserAttrByArray';

		$arrInput = array(
						  'user_id' 	=> $intUserId,
						  'attr_name'	=> self::USER_ATTR_NAME,
						  'attr_value'	=> $arrPrivSets,
						 );

		// 打印日志 记录传递给Service内容
		Bingo_Log::debug(sprintf('talk to servicename:[%s] method:[%s] input:[%s] ',$strServiceName,$strMethod, serialize($arrInput) ) );

		//和service交互
		Bingo_Timer::start("{$strServiceName}_{$strMethod}");
		$arrOutput = Tieba_Service::call($strServiceName,$strMethod, $arrInput,null,null,'post','php','utf-8');
		Bingo_Timer::end("{$strServiceName}_{$strMethod}");

		if (false === $arrOutput) {
			Bingo_Log::Fatal(sprintf('Failed to call servicename:[%s] method:[%s][user_name:%s]',	$strServiceName,$strMethod, serialize($arrInput) ));
			return $arrOutput;
		}

		if ( Tieba_Errcode::ERR_SUCCESS != intval($arrOutput['errno']) ) {
            Bingo_Log::warning(sprintf('Err to call servicename:[%s] method:[%s] [input:%s] [output:%s]',$strServiceName,$strMethod,serialize($arrInput),serialize($arrOutput)));
            return $arrOutput;
		}

        // 处理pc隐私问题 ,
        // 突然有一天 , 文慧说先解绑吧，
        // 设置了这个，用户的关注关系也被限制了，
        // 暂时pm还没想好这块要怎么具体重构，解耦开能让用户多关注几个人
        if (isset($arrPrivSets['post'])) {
             // 除非完全公开，否则字段认为隐藏
            $intUserStatus = (1 == $arrPrivSets['post'] || 4 == $arrPrivSets['post'] ) ? 0 : 1;

            $arrInput = array(
                'user_id'     => $intUserId,
                'attr_name'   => 'user_status',
                'attr_value'  => $intUserStatus,
            );

            $arrOutput = Tieba_Service::call('user', 'setUserAttr', $arrInput, null, null, 'post', 'php', 'utf-8');

            if (false === $arrOutput ) {
                Bingo_Log::warning(sprintf('Failed to call servicename:[%s] method:[%s][user_name:%s]',   $strServiceName,$strMethod, serialize($arrInput) ));
                return $arrOutput;
            }

            if ( Tieba_Errcode::ERR_SUCCESS != intval($arrOutput['errno']) ) {
                Bingo_Log::warning(sprintf('Err to call servicename:[%s] method:[%s] [input:%s] [output:%s]',$strServiceName,$strMethod,serialize($arrInput),serialize($arrOutput)));
                return $arrOutput;
            }
        }

        $arrRet = $arrOutput;
        return $arrOutput;
	}

    private static function _getUserInfo($arrInput){

        $intUserId      = $arrInput['user_id'];

        //Tieba_Service:call
        $strServiceName = 'user';
        $strMethod = 'getUserData';

        $arrInput = array(
                          'user_id' => $intUserId,
                         );

        // 打印日志 记录传递给Service内容
        Bingo_Log::debug(sprintf('talk to servicename:[%s] method:[%s] input:[%s] ',$strServiceName,$strMethod, serialize($arrInput) ) );

        //和service交互
        Bingo_Timer::start("{$strServiceName}_{$strMethod}");
        $arrOutput = Tieba_Service::call($strServiceName,$strMethod, $arrInput,null,null,'post','php','utf-8');
        Bingo_Timer::end("{$strServiceName}_{$strMethod}");

        if (false === $arrOutput ) {
            Bingo_Log::Fatal(sprintf('Failed to call servicename:[%s] method:[%s][user_name:%s]',    $strServiceName,$strMethod, serialize($arrInput) ));
            return false;
        }
        //check err_no
        if ( isset($arrOutput['errno']) && (0 == intval($arrOutput['errno'])) ) {
            //success nothing to do
            $arrRet = $arrOutput;
            return $arrRet['user_info'][0];

        } else {
            //err,print log
            Bingo_Log::warning(sprintf('Err to call servicename:[%s] method:[%s] [input:%s] [output:%s]',$strServiceName,$strMethod,serialize($arrInput),serialize($arrOutput)));
            return false;
        }

    }

    /**
     * V12.4创作者控评-读取白名单
     * @return [type] [description]
     */
    private function _ifWhiteUidList()
    {
        //V12.4创作者控评，只给白名单用户做
        $wuid = $this->_objRequest->getCommonAttr('user_id');
        $objWordServer = Wordserver_Wordlist::factory();
        if(is_null($objWordServer)){
            return false;
        }
        $wlKey = 'wu_'.$wuid;
        $arrKey = array(
            $wlKey,
        );
        $arrInfo = $objWordServer->getValueByKeys($arrKey,'tb_wordlist_redis_creator_config');
        if(!empty($arrInfo[$wlKey]) && $arrInfo[$wlKey] == 1)
        {
            //在白名单
            return true;
        }        
        return false;
    }

}



/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
