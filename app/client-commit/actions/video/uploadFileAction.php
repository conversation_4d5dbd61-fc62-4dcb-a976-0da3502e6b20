<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file uploadBlockAction.php
 * <AUTHOR>
 * @date 2015/10/14
 * @brief /c/c/video/uploadBlock
 * @doc http://agroup.baidu.com/share/md/2d883f6b768341e280a289f2c6acaf67
 **/
require "../../libs/video/VideoBaseAction.php";
class uploadFileAction extends Libs_Video_VideoBaseAction
{
    /**
     * 执行函数
     * */
    protected function _execute()
    {
        $arrParamsRet = $this->checkParams();
        if (false === $arrParamsRet || Tieba_Errcode::ERR_SUCCESS !== $arrParamsRet['errno']) {
            Bingo_Log::warning("client-commit_actons_video_uploadFile param error.[input]" . serialize($arrParamsRet));
            return $this->getOutData($arrParamsRet['errno']);
        }
        $arrInputParams = $arrParamsRet['data'];
        $arrChunkRet = $this->uploadChunk($arrInputParams, false);
        if (false === $arrChunkRet || Tieba_Errcode::ERR_SUCCESS !== $arrChunkRet['errno']) {
            return $this->getOutData($arrChunkRet['errno']);
        }
        //获取user id
        
        $arrInputParams['user_id'] = $this->_arrUserInfo['user_id'];
        
        $strFileName = $this->genFileName(array(
                    'forum_id' => $arrInputParams['forum_id'],
                    'user_id' => $arrInputParams['user_id'],
                    'video_md5' => $arrInputParams['video_md5'],
        ));
        $arrInput = $arrChunkRet['data'];
        $arrInput['file_name'] = $strFileName;
        //获取视频开始上传时间
        $intUploadStartTime =0;
        $arrInputTime = array(
        	"video_md5" => $arrInputParams['video_md5'],
        	"video_size" => $arrInputParams['total_length'],
        );
        $arrOutput   = Tieba_Service::call('livegroup', 'getVideoUploadTime', $arrInputTime, NULL, NULL, 'post', 'php', 'utf-8');
        if (false === $arrOutput || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
        	Bingo_Log::warning("uploadFileAction_call_livegroup_getVideoUploadTime fail; [input]".serialize($arrInputTime) . ";[output]".serialize($arrOutput));
        }else{
        	$intUploadStartTime =$arrOutput['upload_starttime'];
        }
        
        /**
         * 分块上传
         * */
        if ($arrInputParams['block_no'] > 1) {
            //上传分块
            $arrInput['upload_id'] = $arrInputParams['upload_id'];
            $strServiceName = "generateBlock";
            Bingo_Timer::start('video_call_smallvideo_' . $strServiceName);
            // ral_set_idc("nj03");
            $arrOutput = self::mcpackCall('smallvideo', $strServiceName, $arrInput);
            Bingo_Timer::end('video_call_smallvideo_' . $strServiceName);
            $arrInput['(raw)data'] = "(bytes)";
            if (false === $arrOutput || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::fatal("client-commit_actions_video_uploadBlock call smallvideo $strServiceName fail; [input]".serialize($arrInput) . ";[output]".serialize($arrOutput));
                return $this->getOutData(Tieba_Errcode::ERR_MO_POST_FILE_UPLOAD_DATA_ERROR);
            
            }
            //获取url
            $arrInput = array(
                'product' => self::PRODUCT,
                "key" => $arrInputParams['video_md5'],
                "block_num" => $arrInputParams['block_num'],
                "file_name" => $strFileName,
                'upload_id' => $arrInputParams['upload_id'],
            );
            $strServiceName = "generateMultiBlocksUrl";
            Bingo_Timer::start('video_call_smallvideo_'.$strServiceName);
            // ral_set_idc("nj03");
            $arrOutput = self::mcpackCall('smallvideo', $strServiceName, $arrInput);
            Bingo_Timer::end('video_call_smallvideo_'.$strServiceName);
            
        } else if ($arrInputParams['chunk_num'] === 1){
            /**
             * 整个文件上传
             * */
        	$intUploadStartTime = time();
            $strServiceName = "uploadFileByStream";
            // ral_set_idc("nj03");
            Bingo_Timer::start("call_smallvideo_$strServiceName");
            $arrOutput = self::mcpackCall('smallvideo', $strServiceName, $arrInput);
            Bingo_Timer::end("call_smallvideo_$strServiceName");
            
        } else if ($arrInputParams['chunk_num'] > 1){
            /**
             * 只有一块，多片上传，合并文件
             * */
            $arrInput['file_name'] = $strFileName;
            
            $strServiceName = 'generateSingleBlockUrl';
            Bingo_Timer::start('video_call_smallvideo_'.$strServiceName);
            // ral_set_idc("nj03");
            $arrOutput = self::mcpackCall('smallvideo', $strServiceName, $arrInput);
            Bingo_Timer::end('video_call_smallvideo_'.$strServiceName); 
        } 
        if (isset($arrInput['(raw)data'])) {
            $arrInput['(raw)data'] = '(bytes)';
        }
        
        if (false === $arrOutput) {
            Bingo_Log::fatal("client-commit_actions_video_uploadFile call smallvideo $strServiceName fail; [input]".serialize($arrInput) . ";[output]".serialize($arrOutput));
            return $this->getOutData(Tieba_Errcode::ERR_MO_POST_FILE_UPLOAD_DATA_ERROR);
        
        }
        if (Tieba_Errcode::ERR_MO_VIDEO_STORE_FAIL === intval($arrOutput['errno'])) {
            Bingo_Log::fatal("client-commit_actions_video_uploadFile call smallvideo $strServiceName fail; need reupload all chunks. input[".serialize($arrInput). "];[output]".serialize($arrOutput));
            return $this->getOutData(Tieba_Errcode::ERR_MO_VIDEO_STORE_FAIL);
        }
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("client-commit_actions_video_uploadFile call smallvideo $strServiceName fail; [input]".serialize($arrInput) . ";[output]".serialize($arrOutput));
            return $this->getOutData(Tieba_Errcode::ERR_MO_POST_FILE_UPLOAD_DATA_ERROR);
        }
        //计算上传时间
        if($intUploadStartTime > 0){
        	$intUploadTime =time()-$intUploadStartTime;
        	$strIp = Bingo_Http_Ip::getConnectIp('0.0.0.0', true);
        	$intIp = Bingo_Http_Ip::newip2long($strIp);
        	Tieba_Stlog::addNode('video_md5', $arrInputParams['video_md5']);
        	Tieba_Stlog::addNode('file_name', $strFileName);
        	Tieba_Stlog::addNode('video_size', $arrInputParams['total_length']);
        	Tieba_Stlog::addNode('upload_time', $intUploadTime);
        	Tieba_Stlog::addNode('uip', $intIp);
        	Tieba_Stlog::addNode('uid', $this->_arrUserInfo['user_id']);
        }
        
        $arrVideoUrl = explode("?", $arrOutput['data']);
        if(empty($arrVideoUrl) ||empty($arrVideoUrl[0]) ){
            $this->_getOutData(Tieba_Errcode::ERR_MO_VIDEO_STORE_FAIL);
            return false;
        }
        $arrOutputData = array(
            'video_url' => $arrVideoUrl[0],
        );
        return $this->getOutData(Tieba_Errcode::ERR_SUCCESS, $arrOutputData );
    }
    
    /**
     * 获取参数并校验
     * @return {Array} Param
     * */
    protected function checkParams () {
        $arrInputParams = array(
            'forum_id' => 0,
            'tbs' => '',
    
            'video_md5' => '',
            'chunk_no' => 0,
            'chunk_num' => 0,
            'chunk_length' => 0,
            'total_length' => 0,
            'block_no' => 0,
            'block_num' => 0,
            'upload_id' => '',
        );
        foreach ($arrInputParams as $key => &$value) {
            if (is_numeric($value)) {
                $value = intval(Bingo_Http_Request::get($key,0));
            } else {
                $value = strval(trim(Bingo_Http_Request::get($key,'')));
            }
            if ($key === 'upload_id') {
                //第一个块不需要有upload_id，其他块需要（文件同理）
                if ($arrInputParams['block_no'] > 1 && empty($value)) {
                    return $this->_error(Tieba_Errcode::ERR_PARAM_ERROR, $arrInputParams);
                }
            } else if (empty($value)) {
                return $this->_error(Tieba_Errcode::ERR_PARAM_ERROR, $arrInputParams);
            }
        }
        $arrInputParams['video_md5'] = strtolower($arrInputParams['video_md5']);
        $arrInputParams['files'] = &$_FILES;
        return $this->_error(Tieba_Errcode::ERR_SUCCESS, $arrInputParams);
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
    