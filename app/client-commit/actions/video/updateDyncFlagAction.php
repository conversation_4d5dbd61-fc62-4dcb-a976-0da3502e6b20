<?php

/**
 * 设置关注动态已读标识
 * <AUTHOR>
 *
 */
class updateDyncFlagAction extends Molib_Client_BaseAction
{
    private $intUserId = 0;
    
    /**
     * @return array
     */
    protected function _getPrivateInfo()
    {
        $arrPrivateInfo['flag'] = trim(Bingo_Http_Request::get('flag', ''));
        $arrPrivateInfo['isuv'] = 1;
        $arrPrivateInfo['ispv'] = 1;
        
        //访客状态
        $arrPrivateInfo['check_login'] = true;
        $arrPrivateInfo['need_login'] = true;
         
        
        return $arrPrivateInfo;
    }
    
    /**
     * 
     * @return boolean
     */
    protected function _checkPrivate()
    {
        if ('has_read' !== $this->_objRequest->getPrivateAttr('flag')) 
        {
            Bingo_Log::warning("client-commit_actions_video_updateDyncFlag param error! " . $this->_objRequest->getPrivateAttr('flag'));
        
            $this->_error(Tieba_Errcode::ERR_PARAM_ERROR, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_PARAM_ERROR), array(), false);
            return false;
        }
        
        return true;
    }
    
    /**
     * 
     * @return boolean
     */
    protected function _execute()
    {
        $this->intUserId = intval(Tieba_Session_Socket::getLoginUid());
        
        $arrRalInput = array('user_id' => $this->intUserId);
        $arrRalRet = Tieba_Service::call('video', 'setUserReadTime', $arrRalInput, null, null, 'post', 'php', 'utf-8' );
        //var_dump($arrRalRet);
        if (false === $arrRalRet || Tieba_Errcode::ERR_SUCCESS !== $arrRalRet['errno'])
        {
            Bingo_Log::pushNotice('set_read', 0);
            Bingo_Log::warning("call video::setUserReadTime fail, the input is".serialize($arrRalInput).' output is '.serialize($arrRalRet));
            $this->_error(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, Tieba_Error::getUserMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL));
            return false;
        }
        
        Bingo_Log::pushNotice('set_read', 1);
        return true;
    }
}