<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file index.php
 * <AUTHOR>
 * @date 2013/12/25 11:00:37
 * @brief 
 *  
 **/

Tieba_Init::init("client-commit");

define('BINGO_ENCODE_LANG', 'UTF-8');
define('CORE_DATA_PATH', ROOT_PATH.DIRECTORY_SEPARATOR.'/data/app/client-core/');

$arrPatterns = array('/^\/mo_client_frs\//', '/^\/mo_client_pb\//', '/^\/mo_cleint_other\//');
$_SERVER['REQUEST_URI'] = preg_replace($arrPatterns, '', $_SERVER['REQUEST_URI']);

$time = explode(" ", microtime());
$msec = substr($time[0],0,5);
$php_start_time = $time[1] +  $msec;

//Get Module
$strUrl = $_SERVER['PATH_INFO'];
$strUrl = rtrim(strip_tags($strUrl), '/');
if(('/c/f/frs/page' === $strUrl)||('/c/f/frs/threadlist' === $strUrl)) {
    define ('MODULE_NAME', 'clientfrs');
} elseif('/c/f/pb/page' === $strUrl) {
    define ('MODULE_NAME', 'clientpb');
} elseif(('/c/c/thread/add' === $strUrl) || ('/c/c/post/add' === $strUrl)) {
    define ('MODULE_NAME', 'clientpost');
} elseif(('/c/f/forum/forumrecommend' === $strUrl) || ('/c/f/forum/favocommend' === $strUrl)) {
    define ('MODULE_NAME', 'cliententer');
} else {
    define ('MODULE_NAME', 'client');
}
$arrUrl = explode('/', $strUrl);
$strModule = 's';
if (isset($arrUrl[2])){
    $strModule = $arrUrl[2];
}
if (!Bingo_Array::in_array(strval($strModule),array('s','f','u','p','c','m','e'))){
    $strModule = 's';
}

//21 billion
$arrFiled = array ('tid', 'thread_id', 'z', 'kz', 'pid', 'quote_id', 'post_id');
foreach($arrFiled as $strKey){
    $intGetValue = $intPostValue;
    $intPostValue = $_POST[$strKey];
    if($intGetValue < 0 && $intGetValue > (-1) * Molib_Client_Define::SIGNED_INT_MAX) {
        $_GET[$strKey] = $intGetValue + Molib_Client_Define::UNSIGNED_INT_MAX;
        $_GET['has_billion'] = 21;
    }
    if($intPostValue < 0 && $intPostValue > (-1) * Molib_Client_Define::SIGNED_INT_MAX) {
        $_POST[$strKey] = $intPostValue + Molib_Client_Define::UNSIGNED_INT_MAX;
        $_GET['has_billion'] = 21;
    }
}

//Filter Request
Bingo_Http_Request::init();
$intClientType = intval(Bingo_Http_Request::get('_client_type', 1));
$arrBlacklist = Molib_Conf_TbClientBlacklist::$arrBlacklist[$intClientType];
if(is_array($arrBlacklist)) {
    $bolInBlacklist = false;
    $strFrom = Bingo_Http_Request::get('from');
    $strVersion = Bingo_Http_Request::get('_client_version');

    foreach($arrBlacklist as $arrRule) {
        if(isset($arrRule['version']) && $arrRule['version'] !== $strVersion) {
            continue;
        }   
        if(isset($arrRule['from']) && $arrRule['from'] !== $strFrom) {
            continue;
        }   
        $bolInBlacklist = true;
        break;
    }   
    if($bolInBlacklist === true) {
        exit;
    }   
}

//Push Log
Bingo_Log::init(array(
        'UI' => array(
                'file'  => MODULE_LOG_PATH . '/../c/'.$strModule.'.ui.log',
                'level' => 0x7,
        ),
        'RPC' => array(
                'file'  => MODULE_LOG_PATH . '/../c/'.$strModule.'.rpc.log',
                'level' => 0xFF,
        ),
), 'UI');

Tieba_Stlog::setFileName('wap');
if (Bingo_Http_Request::get('subapp_type') == 'nani') {
    Tieba_Stlog::setFileName('tieba_hatch_ui');
}
Tieba_Stlog::addNode('pro', 'client');

Tieba_Stlog::addNode('agent', str_replace(' ', '_', strip_tags(Bingo_Http_Request::getServer('HTTP_USER_AGENT'))));
Tieba_Stlog::addNode('url', Bingo_Http_Request::getServer('REQUEST_URI'));
Tieba_Stlog::addNode('refer', Bingo_Http_Request::getServer('HTTP_REFERER'));
Tieba_Stlog::addNode('ispv',1);
Tieba_Stlog::addNode('errno',0);
Tieba_Stlog::addNode('logid',Bingo_Log::getLogId());
$arrRequest = array_merge(Bingo_Http_Request::getGetNoXssSafe(),Bingo_Http_Request::getPostNoXssSafe());
if(isset($arrRequest['kw'])) {
    Tieba_Stlog::addNode('fname',$arrRequest['kw']);
} elseif(isset($arrRequest['fname'])) {
    Tieba_Stlog::addNode('fname',$arrRequest['fname']);
}
$arrKeyIgnore = array(
    'passwd'    => 1,
    'uid'       => 1,  
    'title'     => 1,
    'content'   => 1,
    'un'        => 1,
    'sign'      => 1,
    'zeus_ip'   => 1,
);
foreach($arrRequest as $strKey => $strValue) {
    if(isset($arrKeyIgnore[$strKey])) {
        continue;
    }
      if($strKey == 'cuid'){
           $arr = explode('|',$strValue);
           Tieba_Stlog::addNode('cuid_tmp',$arr[0].'|'.$arr[1]);       
      }
    if(strlen($strValue) > 60) {
        continue;
    }
    if($strKey == '_client_type' && $strValue == 0) {
        continue;
    }
    $strAppid = Bingo_Http_Request::get('app_id','');
    if($strKey == '_client_version') {
        if(($strAppid == 'version_campus' || $strAppid == 'version_test_0001') && substr($strValue,0,2) != 'v_'){
            Tieba_Stlog::addNode($strKey,'v_'.$strAppid.'_'.$strValue);
            continue;
        }
        if(substr($strValue,0,2) != 'v_' && ($arrRequest['subapp_type'] == 'version_campus' || $arrRequest['subapp_type'] == '
version_test_0001' || $arrRequest['subapp_type'] == 'fanclub')){
            Tieba_Stlog::addNode($strKey,'v_'.$arrRequest['subapp_type'].'_'.$strValue);
            continue;
        }
    }
    Tieba_Stlog::addNode($strKey,$strValue);
}
Tieba_Stlog::addNode('mid', 'api');//mid放在foreach之后，防止foreach被覆盖

if(!empty($arrRequest['st_type'])) {
    $arrStType = explode('|',urldecode($arrRequest['st_type']));
    if(count($arrStType) == 2){
        Tieba_Stlog::addNode('page_from_dim', $arrStType[0]);
        Tieba_Stlog::addNode('page_list_dim', $arrStType[1]);
    } else {
        Tieba_Stlog::addNode('page_from_dim', $arrRequest['st_type']);
    }
}
if(empty($arrRequest['subapp_type'])) {
    if(!empty($arrRequest['app_id'])) {
        Tieba_Stlog::addNode('subapp_type', $arrRequest['app_id']); 
    } else {
        if(Molib_Util_Version::compare($arrRequest['_client_version'],'4.2.7') == 0) {
            Tieba_Stlog::addNode('subapp_type', 'mini');
        } else {
            Tieba_Stlog::addNode('subapp_type', 'tieba');
        }
    }
}
Tieba_Stlog::addNode('php_start_time', $php_start_time);
if(isset($_SERVER['CGI_START_TIME'])) {
    $cgi_start_time = $_SERVER['CGI_START_TIME'];
    Tieba_Stlog::addNode('cgi_start_time', $cgi_start_time);
    Tieba_Stlog::addNode('cgi_wait_time', $php_start_time - $cgi_start_time);
}

//Make router rule
$objFrontController = Bingo_Controller_Front::getInstance(array(
    "actionDir" => MODULE_ACTION_PATH,
    "defaultRouter" => "index",
    "notFoundRouter" => "error",
    "beginRouterIndex" => 2,
)); 

//build log urlkey log for warning/error/fatal
$arrModuleHash = array(
    's' => 'csysapi',
    'f' => 'cforumapi',
    'u' => 'cuserapi',
    'p' => 'cproxyapi',
    'c' => 'ccommitapi',
    'm' => 'cmessage',
);
$strRouter = Bingo_Http_Request::getStrHttpRouter();
$strUiName = $arrModuleHash[$strModule];
if($strRouter === 'default') {
    $strRouter = str_replace("/c/$strModule/", '', $strUrl);
}
$urlkey = $strUiName.'/'.$strRouter;
Bingo_Log::pushNotice('rurlkey', str_replace('/', '-', $urlkey));

//Dispatch
try {
    Bingo_Timer::start("total");
    $objFrontController->dispatch();
    Bingo_Timer::end("total");
} catch(Exception $e) {
    Bingo_Log::warning(sprintf('main process failure!HttpRouter=%s msg[%s]',Bingo_Http_Request::getStrHttpRouter(), $e));
}

//Build log
Tieba_Stlog::addNode('urlkey', $urlkey);
if($strRouter == 'frs/page') {
    Tieba_Stlog::addNode('mid', 'api_frs');
} elseif($strRouter == 'pb/page') {
    Tieba_Stlog::addNode('mid', 'api_feye');
}
if (Bingo_Http_Request::get('subapp_type') == 'nani') {
    Tieba_Stlog::addNode('mid', 'nani');
}
//Build Log
$strTimeLog = Bingo_Timer::toString();
$strTimeLogLocal = '';
if(!empty($strTimeLog)) {
    $strTimeLogLocal = ' time[' . $strTimeLog . ']';
}
$arrTimeLog = explode(' ', $strTimeLog);
if(is_array($arrTimeLog) || !empty($arrTimeLog)) {
    foreach($arrTimeLog as $strTmp) {
        $arrTmp = explode(':', $strTmp);
        Tieba_Stlog::addNode('time_'.$arrTmp[0], $arrTmp[1]);
    }
}

$totalTime = Bingo_Timer::calculate('total');
Tieba_Stlog::addNode('total_time', $totalTime );

$arrLogData = Tieba_Stlog::getLogData();
$intErrno = isset($arrLogData['errno']) ? intval($arrLogData['errno']) : 0;
Bingo_Log::notice('stat'.Tieba_Stlog::notice().''.$strTimeLogLocal, '', '', 0, 0, array(), $intErrno);



/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
