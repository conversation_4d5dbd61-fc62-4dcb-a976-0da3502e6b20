<?php
/**
 * get forum's managers list
 *@since 2012-03-28
 */
class memberAction extends SignActionBase{

	const OWNER			= 'owners';		//����
	const MANAGERS		= 'managers';	//С����
	const PICEDITERS	= 'picediters';	//ͼƬ�༭
	const VIDEOEDITERS	= 'videoediters';//��Ƶ�༭
	
	public function execute()
	{
		$this->setTemplate(self::JSON_TEMPLATE , true);
		
		$strForumName = strval(trim($_REQUEST['kw']));
		
		$this->getForumInfo($strForumName);
		
		$retOwners			= PermServer::getRoleUserList($this->arrForum['forumid'],1,0,0,0,1,1,true);
		$retManagers		= PermServer::getRoleUserList($this->arrForum['forumid'],2,0,0,0,1,1,true);
		$retPicEditers		= PermServer::getRoleUserList($this->arrForum['forumid'],4,0,0,0,1,1,true);
		$retVideoEditers	= PermServer::getRoleUserList($this->arrForum['forumid'],3,0,0,0,1,1,true);
		
		$arrManagerList = array(
								self::OWNER 		=> empty($retOwners['res']) ? array() : $retOwners['res'],
								self::MANAGERS 		=> empty($retManagers['res']) ? array() : $retManagers['res'],
								self::PICEDITERS	=> empty($retPicEditers['res']) ? array() : $retPicEditers['res'],
								self::VIDEOEDITERS	=> empty($retVideoEditers['res']) ? array() : $retVideoEditers['res'],
		);
		
		$this->setParams($arrManagerList,$this->strDataKey);
		
		//set json template...
		$this->setTemplate(self::JSON_TEMPLATE , true);
	}
}