<?php
/***************************************************************************
 * 
 * Copyright (c) 2012 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file loadAdAction.php
 * <AUTHOR>
 * @date 2012/12/28 17:26:30
 * @brief 
 *  
 **/

class loadAdAction extends SignActionBase {
    const CUPID_TYPE = 'sign_ad';
	const CUPID_ID = 234;

	private static $_adConf = array(
		//ad_no  => ad_type
		1 => 1,
		2 => 1,
		3 => 2,
	);

	public function execute() {
		$getForumInfoSucc = $this->getForumInfo();

		if ($getForumInfoSucc === true) {
			$forumName = isset($this->arrForum['forumname']) ? $this->arrForum['forumname'] : '';
			if ($forumName !== '') {
				$adInfo = self::_getAdInfo($forumName);
				if ($adInfo !== false && isset($adInfo['ad_type']) && ($adInfo['ad_type']===0 || isset($adInfo['ad_no'])))
					$this->setParams($adInfo, $this->strDataKey);
				else
					$this->setErrorInfo(CommonMsg::SOMETHING_ERROR);
			} else {
				$this->setErrorInfo(CommonMsg::SOMETHING_ERROR);
			}
		}

		//set json template
		$this->setTemplate(self::JSON_TEMPLATE, true);
	}

	//cupid call
	private static function _getAdInfo($forumName) {
		$query = array(
			'type' => self::CUPID_TYPE,
			'forum_name' => $forumName,
		);
		$res = Tbapi_Midlib_Midl_Cupid::query($query);
		if ($res === false) return false;
		$adInfo['ad_type'] = 0;
		if (isset($res[self::CUPID_ID])) {
			$no = $res[self::CUPID_ID];
			if (isset(self::$_adConf[$no])) {
				$adInfo['ad_no'] = $no;
				$adInfo['ad_type'] = self::$_adConf[$no];
			} else return false;
		}
		return $adInfo;
	}
}
?>
