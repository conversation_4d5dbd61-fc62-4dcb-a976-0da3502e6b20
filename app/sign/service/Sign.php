<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013:04:01 11:38:01
 * @version 
 * @structs & methods(copied from idl.)
*/



define("MODULE","sign_service");
class Service_Sign{

const SERVICE_NAME = "Service_Sign";
protected static $_conf = null;
const MAX_YEAR = 2100;
const MIN_YEAR = 2010;
const MAX_MONTH = 12;
const MIN_MONTH = 1;

const ACTS_CTRL_CMD_NO = 61001;

const ACTS_CTRL_SIGN_TOO_MANG = 34;
const ACTS_CTRL_TOO_SOON_TO_SIGN = 901;
const ACTS_CTRL_TOO_MANG_PEOPLE = 199;

const TYPE_OF_PC = 0;
const TYPE_OF_MOBILE = 1;
const TYPE_OF_BDBRS = 2;
const TYPE_OF_BDBRS_FR_MOBILE = 3;
const TYPE_OF_MOBILE_CLIENT = 4;

const TERM_TYPE_MIN = 0;
const TERM_TYPE_MAX = 4;

const DATABASE_NAME_MON = "forum_sign";
const DATABASE_TIEBA_SIGN_1 = "tieba_sign_1";
const DATABASE_TIEBA_SIGN_2 = "tieba_sign_2";
const DATABASE_TIEBA_SIGN_3 = "tieba_sign_3";
const DATABASE_TIEBA_SIGN_4 = "tieba_sign_4";
const USER_MONTH_SIGN_TABLE = "user_month_sign";
const USER_SIGN_TABLE = "user_sign";
const FORUM_DAILY_SIGN_TABLE = "forum_daily_sign_";
const USER_SIGN_TABLE_NUM = 100;
const USER_MONTH_SIGN_TABLE_NUM = 32;

const SIGN_FORUM = 1;
const SIGN_USER = 2;
const SIGN_USER_MONTH = 3;

const TYPE_OF_RESIGN_10 = 5;
const TYPE_OF_RESIGN_20 = 6;
const TYPE_OF_RESIGN_30 = 7;
const TYPE_OF_RESIGN_40 = 8;

private static $RESIGN_TYPE_TO_SCORE = array(
	self::TYPE_OF_RESIGN_10 => 5,
	self::TYPE_OF_RESIGN_20 => 10,
	self::TYPE_OF_RESIGN_30 => 15,
	self::TYPE_OF_RESIGN_40 => 20,
);

private static $_mysql_record = null;
private static $_mysql_mon = null;

protected static $TERM_TYPE_ARR = array(
	self::TYPE_OF_PC,
	self::TYPE_OF_MOBILE,
	self::TYPE_OF_BDBRS,
	self::TYPE_OF_BDBRS_FR_MOBILE,
	self::TYPE_OF_MOBILE_CLIENT,
);

const GRADE_SIGN_CMD_NO = 3420;
const EX_GRADE_SIGN_CMD_NO = 3430;
const MOBLIE_BDBRS_SIGN_CMD_NO = 3440;
const FOR_SIGN_MIGRATE_CMD = 3450;
const FOR_RESIGN_MIGRATE_CMD = 3460;

const DEFAULT_SIGN_CMD_RETRY = 2;

const MULTI_SIGN_IN_MAX = 100;
const RAL_MULTI_MAX = 32;
//const BLOCK_QUERY_MAX = 20;
const BLOCK_QUERY_MAX = 10;

const MOBILE_BDBRS_SIGN = 'mobileBdbrsSign';
const MOBILESIGN = 'mobileSign';
const GRADESIGNIN = 'gradeSignIn';
const RESIGN = 'reSign';
private static $TERM_TO_METHOD = array(
	self::TYPE_OF_PC=>self::GRADESIGNIN,
	self::TYPE_OF_MOBILE=>self::MOBILESIGN,
	self::TYPE_OF_BDBRS=>self::MOBILESIGN,
	self::TYPE_OF_BDBRS_FR_MOBILE=>self::MOBILE_BDBRS_SIGN,
	self::TYPE_OF_MOBILE_CLIENT=>self::MOBILE_BDBRS_SIGN,
);


private static function _getDB($db_name){
	$objTbMysql = Tieba_Mysql::getDB($db_name);
	if($objTbMysql && $objTbMysql->isConnected()) {
		return $objTbMysql;
	} else {
		Bingo_Log::warning("db connect fail.");
		return null;
	}
}

/**
 * @brief init
 * @return: true if success. false if fail.

**/		
private static function _init(){
	
	//add init code here. init will be called at every public function beginning.
	//not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
	//init should be recalled for many times.
	
	if(self::$_conf == null){	
		self::$_conf = Bd_Conf::getConf("/app/sign/service_sign");
		if(self::$_conf == false){
			Bingo_Log::warning("init get conf fail.");
			return false;
		}
		
	}
	return true; 
}


private static function _errRet($errno){
    return array(
        'errno' => $errno,
        'errmsg' => Tieba_Error::getErrmsg($errno),
    );
}

/**         
 *      * 粒度控制检查
 *           */     
private static function _checkActsCtrl($user_id, $forum_id, $user_ip, $term_type=null, $is_batch=0) {
	if (empty($user_id) || empty($forum_id) || empty($user_ip) || is_null($term_type)) {
		return false;
	}

	$in = array( 
			'cmd_no'    => self::ACTS_CTRL_CMD_NO,
			'forum_id'  => $forum_id,
			'user_id'   => $user_id,
			'user_ip'   => $user_ip,
			'term_type' => $term_type,
			'flag'	    => 1,
			'is_batch'  => $is_batch,
		);
	$out = ral('acts_ctrl', 'queryPost', $in, rand());
	if (false === $out) {
		Bingo_Log::warning("fail to call acts_ctrl queryPost! input : ". serialize($in));
		return false;
	} elseif (isset($out['err_no']) && 0 != $out['err_no']) {
		Bingo_Log::warning("sign accts_ctrl not pass! output : ".serialize($out)." , input : ".serialize($in));
		return intval($out['err_no']);
	}
	Bingo_Log::debug("sign acts_ctrl pass! input : ". print_r($in, true) .", output : ".print_r($out, true));

	return 0;
}

private	static function _updateActsCtrl($user_id, $forum_id, $user_ip, $term_type=null, $flag=null, $is_batch=0) {
	if (empty($user_id) || empty($forum_id) || empty($user_ip) || is_null($term_type) || is_null($flag)) {
		return false;
	}
	$in = array( 
			'cmd_no'    => self::ACTS_CTRL_CMD_NO,
			'forum_id'  => $forum_id,
			'user_id'   => $user_id,
			'user_ip'   => $user_ip,
			'term_type' => $term_type,
			'is_batch'  => $is_batch,
		);
	if(1 === $flag)
	{
		$in['flag'] = 1;
	}
	Bingo_Log::debug("input for acts_ctrl->submitPost : ". print_r($in, true));
	$out = ral('acts_ctrl', 'submitPost', $in, rand());
	Bingo_Log::debug("output for acts_ctrl->submitPost : ". print_r($out, true));
	if(false === $out || 0 != $out['err_no'])
	{
		Bingo_Log::warning("fail to call acts_ctrl ! input : ". serialize($in).", output : ". serialize($out));
	}
}

private static function _sendSignCommand($arrInput, $intRetry = 1)
{
	Bingo_Log::debug("input clubcm::query is : ". print_r($arrInput, true));
	while ($intRetry > 0){
		$intRetry--;
		//$ret = RpcIdlClubcm::call($arrInput);
		$ret = ral('clubcm', 'query', $arrInput, rand());//TODO
		Bingo_Log::debug("output clubcm::query is : ". print_r($ret, true));
		if(isset($ret['error_no']) && 0 == $ret['error_no'])
		{
			return $ret['error_no'];
		}
		Bingo_Log::warning("call clubcm error, <uid:".$arrInput['user_id']."forum_id:".$arrInput['forum_id'].
			">, return [".serialize($ret)."] retry :".$intRetry);
		if($intRetry <= 0) {
			break;
		}
		sleep(1);

	}
	return -1;
}

private static function commitCheck($checkOptions, $extra=array())
{
	//检查是否被封禁
	if(isset($checkOptions['blockCheck']) && true === $checkOptions['blockCheck'])
	{
		if(!isset($extra['forum_id']) || !isset($extra['user_id']) || !isset($extra['user_ip']))
		{
			return self::_errRet(Tieba_Errcode::ERR_PROPS_IS_BLOCK);//TODO
		}
		return self::checkBlock($extra['forum_id'], $extra['user_id'], $extra['user_ip']);
	}
	$user_level = array();
	//检查是否在黑名单中
	if(isset($checkOptions['blackCheck']) && true === $checkOptions['blackCheck'])
	{
		if(!isset($extra['forum_id']) || !isset($extra['user_id']))
		{
			return self::_errRet(Tieba_Errcode::ERR_POST_CT_BLACK_USER);//TODO
		}
		if(!isset($user_level['is_black']))
		{
			$user_level = self::getUserLevel($extra['forum_id'], $extra['user_id']);
		}
		if(1 === $user_level['is_black'])
		{
			return self::_errRet(Tieba_Errcode::ERR_POST_CT_BLACK_USER);//TODO
		}
	}
	//检查是否喜欢了该吧
	if(isset($checkOptions['likeCheck']) && true === $checkOptions['likeCheck'])
	{
		if(!isset($extra['forum_id']) || !isset($extra['user_id']))
		{
			return self::_errRet(Tieba_Errcode::ERR_POST_CT_CHECK_ERROR);//TODO
		}
		if(!isset($user_level['is_like']))
		{
			$user_level = self::getUserLevel($extra['forum_id'], $extra['user_id']);
		}
		if(0 === $user_level['is_like'])
		{
			return self::_errRet(Tieba_Errcode::ERR_POST_CT_CHECK_ERROR);//TODO
		}
	}
	//检查客户终端类型
	if(isset($checkOptions['termtypeCheck']) && true === $checkOptions['termtypeCheck'])
	{
		if(!isset($extra['term_type']) || !in_array($extra['term_type'], self::$TERM_TYPE_ARR, true))
		{
			return self::_errRet(Tieba_Errcode::ERR_POST_CT_CHECK_ERROR);//TODO
		}
	}
	return true;
}
private static function checkBlock($forum_id, $user_id, $user_ip=0) {
	$in = array(
		'req'=>array(
			'forum_id'=>$forum_id,
			'user_id'=>$user_id,
			'ip'=>$user_ip,
		),
	);
	Bingo_Log::debug("input for anti::antiUserBlockQuery is ". print_r($in, true));
	$out = Tieba_Service::call("anti", 'antiUserBlockQuery',$in);
	Bingo_Log::debug("output for anti::antiUserBlockQuery is ". print_r($out, true));

	if(!empty($out) && 0 == $out['errno'] && isset($out['is_block']) && 0 != $out['is_block'])
	{
		Bingo_Log::warning("user[$user_id] is in blocked in forum[$forum_id] ; output : ". serialize($out));
		return self::_errRet(Tieba_Errcode::ERR_PROPS_IS_BLOCK);//TODO
	}

	return true;
}

private static function getUserLevel($forum_id, $user_id) {
	if (empty($forum_id) || empty($user_id))
	{
		return array(
			'is_like'=>0,
			'is_black'=>0,
		);
	}

	$in = array(
		'user_id'=>$user_id,
		'forum_ids'=>array($forum_id),
	);
	Bingo_Log::debug("input for perm::mgetUserLevel is ". print_r($in, true));
	$out = Tieba_Service::Call('perm', 'mgetUserLevel', $in);
	Bingo_Log::debug("output for perm::mgetUserLevel is ". print_r($out, true));

	if(!empty($out) && 0 == $out['errno'] && isset($out['score_info'][0]['is_black']) && isset($out['score_info'][0]['is_like']))
	{
		return array(
			'is_like' =>$out['score_info'][0]['is_like'],
			'is_black'=>$out['score_info'][0]['is_black'],
		);
	}

	Bingo_Log::warning("fail to call perm::mgetUserLevel ; input : ". serialize($in) ." output : ". serialize($out));//TODO
	return array(
		'is_like'=>0,
		'is_black'=>0,
	);
}

public static function signInForMigrate($arrInput)
{
	Bingo_Log::trace("input for signInForMigrate : ". serialize($arrInput));
	$forum_id = intval($arrInput['forum_id']);
	$user_id = intval($arrInput['user_id']);
	$sign_time = intval($arrInput['sign_time']);//TODO
	$cont_sign_num = intval($arrInput['cont_sign_num']);
	$total_sign_num = intval($arrInput['total_sign_num']);
	$sign_count = intval($arrInput['sign_count']);
	$sign_in = array(
		'cmd'=>array(
			'forum_id'=>$forum_id,
			'user_id'=>$user_id,
		)
	);
	//��������������
	$fields = array(
		'flags'=>0,
		'cont_sign_num'=>$cont_sign_num,
		'total_sign_num'=>$total_sign_num,
		'last_up_time'=>$sign_time,
	);
	$existed = false;
	if($total_sign_num>1)
	{
		$existed = true;
	}
	$set_user_sign_ret = self::_setUserInfoForDB($user_id, $forum_id, $fields, $existed);
	if(false === $set_user_sign_ret['succ'])
	{
		Bingo_Log::warning("fail to sync sign info for user[$user_id] forum[$forum_id]");
		return $set_user_sign_ret['info'];
	}

	$set_forum_sign_ret = self::_setForumInfoForDB($forum_id, $sign_count, $sign_time);
	if(false === $set_forum_sign_ret['succ'])
	{
		Bingo_Log::warning("fail to update forum sign info for forum[$forum_id]");
		//return $set_forum_sign_ret['info'];
	}

	$cache = new Bingo_Cache_Memcached('forum_profile');
	$user_sign_key = self::SIGN_USER . ":$forum_id:$user_id";
	$forum_sign_key = self::SIGN_FORUM . ":$forum_id";
	$user_mon_sign_key = self::SIGN_USER_MONTH . ":$forum_id:$user_id";
	$cache->remove($user_sign_key);
	$cache->remove($forum_sign_key);
	$cache->remove($user_mon_sign_key);

	return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
}

private static function _setForumInfoForDB($forum_id, $sign_count, $sign_time)
{
	$year = date('Y', $sign_time);
	$month = date('m', $sign_time);
	$day = date('d', $sign_time);
	$db_name = self::_getUserSignTable($forum_id);
	self::$_mysql_record = self::_getDB($db_name);

	$forum_daily_sign_table = self::FORUM_DAILY_SIGN_TABLE . "$year$month$day";
	//$user_sign_table = self::USER_SIGN_TABLE . "_" . $user_id%self::USER_SIGN_TABLE_NUM;
	
	$forum_sign_query_sql = "select forum_id, sign_num, member_count from $forum_daily_sign_table where forum_id=$forum_id";
	Bingo_Log::debug("forum_sign_query_sql : [$forum_sign_query_sql]");
	$forum_sign_query_out = self::$_mysql_record->query($forum_sign_query_sql);
	Bingo_Log::debug("forum_sign_query_out : ". print_r($forum_sign_query_out, true));
	if(false === $forum_sign_query_out)
	{
		//����
		$create_forum_sign_sql = "CREATE TABLE IF NOT EXISTS `$forum_daily_sign_table` like `table_forum_sign`";
		$create_forum_sign_out = self::$_mysql_record->query($create_forum_sign_sql);
		//���������������
		if(false === $create_forum_sign_out)
		{
			Bingo_Log::warning("fail to create table : $forum_daily_sign_table");
		}
	}

	if(isset($forum_sign_query_out[0]['member_count']))
	{
		$member_count = $forum_sign_query_out[0]['member_count'];
	}
	else
	{
		$member_count = self::_getMemcountFromPerm($forum_id);
	}
	if(is_null($member_count))
	{
		return array(
			'succ'=>false,
			'info'=>self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL),
		);
	}

	if(isset($forum_sign_query_out[0]))
	{
		$forum_sign_update_sql = "update $forum_daily_sign_table set sign_num=$sign_count, member_count=$member_count where forum_id=$forum_id";
		Bingo_Log::debug("forum_sign_update_sql : $forum_sign_update_sql");
		$forum_sign_update_out = self::$_mysql_record->query($forum_sign_update_sql);
		Bingo_Log::debug("forum_sign_update_out : ". print_r($forum_sign_update_out, true));
		if(false === $forum_sign_update_out)
		{
			return array(
				'succ'=>false,
				'info' => self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL),
			);
		}
	}
	else
	{
		$forum_sign_insert_sql = "insert ignore into $forum_daily_sign_table (forum_id, sign_num, member_count) values($forum_id, $sign_count, $member_count)";
		Bingo_Log::debug("forum_sign_insert_sql : [$forum_sign_insert_sql]");
		$forum_sign_insert_out = self::$_mysql_record->query($forum_sign_insert_sql);
		Bingo_Log::debug("forum_sign_insert_out : ". print_r($forum_sign_insert_out, true));
		if(false === $forum_sign_insert_out)
		{
			Bingo_Log::warning("fail to insert sign info, sql[$forum_sign_insert_sql]");
			return array(
				'succ'=>false,
				'info' => self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL),
			);
		}
	}

	return array(
		'succ'=>true,
	);
}

private static function _getMemcountFromPerm($forum_id)
{
	$member_count = null;
	$member_in = array(
		'forum_id'=>$forum_id,
	);   
	$member_out = Tieba_Service::Call('perm', 'getForumMemberInfo', $member_in, null, null, 'post', 'php', 'utf-8');
	Bingo_Log::debug("output of perm::getForumMemberInfo : ". print_r($member_out, true));
	if(!isset($member_out['errno']) || 0 != $member_out['errno'])
	{    
		Bingo_Log::warning("fail to call perm::getForumMemberInfo for forum[$forum_id] output : ". serialize($member_out));
	}    
	else 
	{    
		$member_count = 0; 
		if(isset($member_out['output']['member_count']))
		{    
			$member_count = $member_out['output']['member_count'];
		}    
	}    
	return $member_count;
}

public static function signIn($arrInput){
	if(!isset($arrInput['forum_id']) || !isset($arrInput['user_id']) || !isset($arrInput['user_ip'])) {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}

	$forum_id = intval($arrInput['forum_id']);
	$user_id = intval($arrInput['user_id']);
	$user_ip = intval($arrInput['user_ip']);
	$is_batch = isset($arrInput['is_batch'])?intval($arrInput['is_batch']):0;

	$term_type = isset($arrInput['term_type'])? intval($arrInput['term_type']) : self::TYPE_OF_PC;

	$checkOpts = array(
		'blockCheck'=>false,
		'blackCheck'=>true,
		'likeCheck'=>false,
		'termtypeCheck'=>true,
		);
	$extra = array(
		'forum_id'=>$forum_id,
		'user_id'=>$user_id,
		'user_ip'=>$user_ip,
		'term_type'=>$term_type,
		);
	$checkRet = self::commitCheck($checkOpts, $extra);
	if(true !== $checkRet)
	{
		return $checkRet;
	}

	//���ȿ���
	$actl_ret = self::_checkActsCtrl($user_id, $forum_id, $user_ip, $term_type, $is_batch);
	if (0 !== $actl_ret) {
		if(false === $actl_ret) {
			Bingo_Log::warning("fail to call acts ctrl, acts_ctrl service is invalid");
			//return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		elseif (self::ACTS_CTRL_SIGN_TOO_MANG === $actl_ret){
			Bingo_Log::warning("too many sign : err_no=$actl_ret");
			return self::_errRet(Tieba_Errcode::ERR_MO_FORUM_SIGN_CONCURRENT_OP);
		}
		elseif (self::ACTS_CTRL_TOO_SOON_TO_SIGN === $actl_ret){
			Bingo_Log::warning("too soon to sign : err_no=$actl_ret");
			return self::_errRet(Tieba_Errcode::ERR_MO_FORUM_SIGN_TOO_OFTEN);
		}
		else {
			Bingo_Log::warning("acts ctrl error : err_no=$actl_ret");
			return self::_errRet(Tieba_Errcode::ERR_MO_FORUM_ACTS_CTRL_ERROR);
		}
	}

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}

	$sign_in = array(
		'cmd'=>array(
			'forum_id'=>$forum_id,
			'user_id'=>$user_id,
		)
	);
	Bingo_Log::debug("input for sign_in is : ".print_r($sign_in, true));
	$sign_out = Dl_Sign_Sign::sign_in($sign_in);
	Bingo_Log::debug("output for sign_in is : ".print_r($sign_out, true));

	if(false === $sign_out || !isset($sign_out['res']['err_no']))
	{
		Bingo_Log::warning("call Dl_Sign_Sign::sign_in failed! input: ".serialize($sign_in).";output:".serialize($sign_out));
		return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);

	}

	$res = $sign_out['res'];

	$arrOutput = array(
		'errno'=>$res['err_no'],
		'errmsg'=>isset($res['err_msg'])?$res['err_msg'] : "",
		'user_info'=>isset($res['uinfo'])?$res['uinfo'] : array(),
		'forum_info'=>isset($res['finfo'])?$res['finfo'] : array(),
	);
	if(0 != $res['err_no'])
	{ 
		self::_updateActsCtrl($user_id, $forum_id, $user_ip, $term_type, 0, $is_batch);
		return $arrOutput;
	}
	self::_updateActsCtrl($user_id, $forum_id, $user_ip, $term_type, 1, $is_batch);

	/****************��nmq������***************/

	//����forum_id��ȡforum_name
	$forum_in = array(
		'forum_id'=>array($forum_id),
	);	
	$forum_out = Tieba_Service::Call('forum', 'getFnameByFid', $forum_in);	
	Bingo_Log::debug("output for forum::getFnameByFid [$forum_id] is ". print_r($forum_out, true));
	if(false === $forum_out || 0 != $forum_out['errno'] )
	{
		Bingo_Log::warning("fail to call forum::getFnameByFid for forum_id[$forum_id]! output : ".serialize($forum_out));
		$arrOutput['additional'] = "fail to get fname;";
	}
	$forum_name = "";
	if(isset($forum_out['forum_name'][$forum_id]['forum_name']))
	{
		$forum_name = $forum_out['forum_name'][$forum_id]['forum_name'];
	}

	//����user_id��ȡuser_name
	$user_in = array(
		'user_id'=>array($user_id),
	);
	$user_out = Tieba_Service::Call('user', 'getUnameByUids', $user_in);
	Bingo_Log::debug("output for user::getUnameByUids [$user_id] is ". print_r($user_out, true));
	if(false == $user_out || 0 != $user_out['errno'])
	{
		Bingo_Log::warning("fail to call user::getUnameByUids for user_id[$user_id]! output : ".serialize($user_out));
		$arrOutput['additional'] .= "fail to get uname;";
	}
	$user_name = "";
	if(isset($user_out['output']['unames'][0]['user_id']))
	{
		$user_name = $user_out['output']['unames'][0]['user_name'];
	}

	//��ȡ�û��ڸðɵ���Ϣ
	$like_in = array(
		'forum_id'=>$forum_id,
		'user_id'=>$user_id,
		'user_ip'=>$user_ip,
	);  
	Bingo_Log::debug("input for perm::getPerm is ". print_r($like_in, true));
	$like_out = Tieba_Service::Call('perm', 'getPerm', $like_in);
	Bingo_Log::debug("output for perm::getPerm is ". print_r($like_out, true));

	if(empty($like_out) || 0 != $like_out['errno'] || !isset($like_out['output']['grade']))
	{
		Bingo_Log::warning("fail to call perm::getPerm! input : ". serialize($like_in). "; output : ". serialize($like_out));
		$arrOutput['additional'] .= "fail to get likeinfo;";
	}

	$like_info = array(
		'in_time'=>0,
		'level_id'=>1,
		'is_like'=>0,
		'like_num'=>0,
		'cur_score'=>0,
	);
	if(isset($like_out['output']['grade']))
	{
		$like_info = $like_out['output']['grade'];
	}

	//���ݰ�id��ȡ�ðɵĻ�Ա��
	$member_in = array(
		'forum_id'=>$forum_id,
	);

	$member_out = Tieba_Service::Call('perm', 'getForumMemberInfo', $member_in);
	Bingo_Log::debug("output for perm::getForumMemberInfo [$forum_id] is ". print_r($member_out, true));

	if(empty($member_out) || 0 != $member_out['errno'] || !isset($member_out['output']['member_count']))
	{
		Bingo_Log::warning("fail to call perm::getForumMemberInfo! input : ". serialize($member_in). "; output : ". serialize($member_out));
		$arrOutput['additional'] .= "fail to get member count;";
	}
	$member_count = 0;
	if(isset($member_out['output']['member_count']))
	{
		$member_count = intval($member_out['output']['member_count']);
	}

	$sign_count   = intval($res['finfo']['current_rank_info']['sign_count']);

	if (0 >= $member_count || 0 >= $sign_count) {
		$sign_precent = 0;
	}elseif ($sign_count > $member_count){
		$sign_precent = 100; 
	}else {     
		$sign_precent = intval($sign_count/$member_count*100);
	}     

	$userinfo = $res['uinfo'];

	//����ǩ������
	$arrInput = array(
		'user_id'=>$user_id,
		'forum_id'=>$forum_id,
		'user_name'=>$user_name,
		'forum_name'=>$forum_name,
		'sign_time'=>$userinfo['sign_time'],
		'user_sign_rank'=>$userinfo['user_sign_rank'],
		'cont_sign_num'=>$userinfo['cont_sign_num'],
		'total_sign_num'=>$userinfo['total_sign_num'],
		'term_type'=>$term_type,
		'user_ip'=>$user_ip,
		'like_time'=>$like_info['in_time'],
		'level_id'=>$like_info['level_id'],
		'is_like'=>$like_info['is_like'],
		'like_num'=>$like_info['like_num'],
		'cur_score'=>$like_info['cur_score'],
		'percent'=>$sign_precent,
		'sign_count'=>$sign_count,
		'member_count'=>$member_count,
		'sign_version'=>intval($ret['sign_version']),
		'is_from_nmq'=>1,
		'is_from_signnew'=>0,
	);

	switch($term_type)
	{
	case self::TYPE_OF_PC:
		$arrInput['command_no'] = self::GRADE_SIGN_CMD_NO;
		break;
	case self::TYPE_OF_MOBILE:
	case self::TYPE_OF_BDBRS:
		$arrInput['command_no'] = self::EX_GRADE_SIGN_CMD_NO;
		break;
	case self::TYPE_OF_MOBILE_CLIENT:
	case self::TYPE_OF_BDBRS_FR_MOBILE:
		$arrInput['command_no'] = self::MOBLIE_BDBRS_SIGN_CMD_NO;
		break;
	default:
		$arrInput['command_no'] = self::GRADE_SIGN_CMD_NO;
		break;
	}

	$send_out = self::_sendSignCommand($arrInput, self::DEFAULT_SIGN_CMD_RETRY);
	if(0 !== $send_out)
	{
		$arrOutput['additional'] .= "fail to send sign command;";
	}

	return $arrOutput;
}

private static function mgetUserLevel($arr_forum_id, $user_id) {

	$in = array(
		'user_id'=>$user_id,
		'forum_ids'=>$arr_forum_id,
	);
	Bingo_Log::debug("input for perm::mgetUserLevel is ". print_r($in, true));
	$out = Tieba_Service::Call('perm', 'mgetUserLevel', $in);
	Bingo_Log::debug("output for perm::mgetUserLevel is ". print_r($out, true));

	$ret = array(
		'pass'=>array(),
		'black'=>array(),
	);
	if(empty($out) || 0 != $out['errno'])
	{
		Bingo_Log::warning("fail to call perm::mgetUserLevel ; input : ". serialize($in) ." output : ". serialize($out));
		$like_info_default = array(
			'in_time'=>0,
			'level_id'=>1,
			'is_like'=>0,
			'like_num'=>0,
			'cur_score'=>0,
		);
		foreach($arr_forum_id as $forum_id)
		{
			$ret['pass'][$forum_id] = $like_info_default;
		}
		return $ret;
	}

	foreach($out['score_info'] as $level_info)
	{
		$forum_id = $level_info['forum_id'];
		if(0 == $level_info['is_black'])
		{
			$ret['pass'][$forum_id] = $level_info;
			continue;
		}
		$ret['black'][$forum_id] = $level_info;
	}
	return $ret;
}

public static function msignIn($arrInput){
	if(!isset($arrInput['forum_id']) || !is_array($arrInput['forum_id']) 
		|| count($arrInput['forum_id']) > self::MULTI_SIGN_IN_MAX
		|| count($arrInput['forum_id']) <= 0 || !isset($arrInput['user_id']) 
		|| !isset($arrInput['user_ip'])) {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}

	$user_id = intval($arrInput['user_id']);
	$user_ip = intval($arrInput['user_ip']);
	$arr_forum_id = $arrInput['forum_id'];
	$term_type = isset($arrInput['term_type'])? intval($arrInput['term_type']) : self::TYPE_OF_PC;
	if(self::TERM_TYPE_MIN > $term_type || self::TERM_TYPE_MAX < $term_type)
	{
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}

	//ueg query TODO
	//$ueg_query_in = array(
	//	'req'=>array(
	//		'cmd_no'=>self::ACTS_CTRL_CMD_NO,
	//		'rulegroup'=>array('app'),
	//		'app'       => 'sign',
	//		//'forum_id'  => $forum_id,
	//		'user_id'   => $user_id,
	//		'user_ip'   => $user_ip,
	//		'term_type' => $term_type,
	//		'flag'      => 1,
	//	),
	//);
	//Bingo_Log::debug("input for anti::antiActsctrlQuery : ". print_r($ueg_query_in, true));
	//$ueg_query_out = Tieba_Service::Call('anti', 'antiActsctrlQuery', $ueg_query_in);
	//Bingo_Log::debug("output for anti::antiActsctrlQuery : ". print_r($ueg_query_out, true));
	//if (false === $ueg_query_out) {
	//	Bingo_Log::warning("fail to call acts_ctrl queryPost! input : ". serialize($particle_query_in));
	//}
	//$particle_code = 0;
	//if (isset($ueg_query_out['errno']) && 0 != $ueg_query_out['errno']) {
	//	Bingo_Log::trace("sign accts_ctrl not pass! output : ".serialize($ueg_query_out)." , input : ".serialize($in));
	//}
	//if(self::ACTS_CTRL_TOO_SOON_TO_SIGN === $particle_code) //TODO
	//{
	//	Bingo_Log::trace("too soon to sign : err_no[$particle_code]");
	//	return self::_errRet(Tieba_Errcode::ERR_SIGN_TOO_OFTEN); //TODO
	//}
	//elseif(self::ACTS_CTRL_TOO_MANG_PEOPLE === $particle_code) //TODO
	//{
	//	Bingo_Log::trace("too many sign : err_no[$particle_code]");
	//	return self::_errRet(Tieba_Errcode::ERR_TOO_PEOPLE_SIGN); //TODO
	//}
	//elseif($particle_code>0)
	//{
	//	Bingo_Log::trace("acts ctrl error : err_no[$particle_code]");
	//	return self::_errRet(Tieba_Errcode::ERR_ACTS_CTRL_ERROR); //TODO
	//}

	$arr_like_info = self::mgetUserLevel($arr_forum_id, $user_id);
	$sign_in_ret = self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	$sign_in_ret['fail_num'] = 0; 
	$sign_in_ret['succ_num'] = 0; 
	$sign_in_ret['fail_info'] = array();
	$sign_in_ret['succ_info'] = array();
	if(!empty($arr_like_info['black']))
	{
		$sign_in_ret['fail_num'] = count($arr_like_info['black']);
		$arr_fail_info = array();
		foreach($arr_like_info['black'] as $black_info)
		{
			$forum_id = $black_info['forum_id'];
			$arr_fail_info[$forum_id] = self::_errRet(Tieba_Errcode::ERR_USER_IS_BLACK);
		}
		$sign_in_ret['fail_info'] = $arr_fail_info;
	}

	if(empty($arr_like_info['pass']))
	{
		return $sign_in_ret;
	}

	$arr_sign_in = array();
	foreach($arr_like_info['pass'] as $pass_info)
	{
		$sign_in = array(
			'cmd'=>array(
				'forum_id'=>$pass_info['forum_id'],
				'user_id'=>$user_id,
			),
		);
		$arr_sign_in[$pass_info['forum_id']] = $sign_in;
	}
	Bingo_Log::debug("input for m_sign_in is : ".print_r($sign_in, true));
	$forum_count = count($arr_sign_in);
	$sign_out = array();
	for($i=0; $i<=floor($forum_count/self::RAL_MULTI_MAX); $i++)
	{
		$rel_arr_sign_in = array_slice($arr_sign_in, $i*self::RAL_MULTI_MAX, self::RAL_MULTI_MAX, true);
		if(empty($rel_arr_sign_in))
		{
			break;
		}
		$rel_sign_out = Dl_Sign_Sign::m_sign_in($rel_arr_sign_in);
		if(false === $rel_sign_out)
		{
			Bingo_Log::warning("fail to Dl_Sign_Sign::m_sign_in for input : ". print_r($rel_sign_in, true));
			continue;
			//return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		$sign_out += $rel_sign_out;
	}
	Bingo_Log::debug("output for m_sign_in is : ".print_r($sign_out, true));

	$init_ret = array(
		'err_no'=>0,
		'err_msg'=>'',
		'finfo'=>array(
			'forum_info'=>array(
				//'forum_id'=>$forumid,
				'level_1_dir_name'=>'',
			),
			'current_rank_info'=>array(
				'sign_count'=>1,
			),
		),
		'uinfo'=>array(
			'sign_time'=>time(),
			'user_sign_rank'=>2,
			'cont_sign_num'=>2,
			'cout_total_sing_num'=>2,
		),
		'sign_version'=>1,
		'is_sign_succ'=>0,
	);
	$sign_sync_fail = array();

	foreach($arr_like_info['pass'] as $forum_id=>$pass_info)
	{
		if(isset($sign_out[$forum_id]['err_no']) && 0 == $sign_out[$forum_id]['err_no'])
		{
			$sign_in_ret['succ_info'][$forum_id] = self::_errRet(Tieba_Errcode::ERR_SUCCESS);
			$sign_in_ret['succ_num']++;
			continue;
		}
		if(isset($sign_out[$forum_id]['err_no']) && 1 == $sign_out[$forum_id]['err_no'])//has signed in
		{
			$sign_in_ret['fail_info'][$forum_id] = self::_errRet(Tieba_Errcode::ERR_IS_SIGNED);
		}
		else
		{
			$sign_in_ret['fail_info'][$forum_id] = self::_errRet(Tieba_Errcode::ERR_SIGN_IN_FAIL);
			$sign_out[$forum_id] = $init_ret;
			//$sign_sync_fail[$forum_id] = $arr_like_info['pass'][$forum_id];
		}
		$sign_in_ret['fail_num'] ++;
		//unset($sign_out[$forum_id]);
		//unset($arr_like_info['pass'][$forum_id]);
	}

	if(empty($arr_like_info['pass']))
	{
		$sign_in_ret['succ_num'] = 0;
		return $sign_in_ret;
	}


	//query block info
	$block_in = array();
	foreach($arr_like_info['pass'] as $forum_id=>$pass_info)
	{
		$block_in[$forum_id] = array(
			'service_type'=>'blockid',
			'key'=>$user_id,
			'forum_id'=>$forum_id,
		);
	}
	$count = count($block_in);
	Bingo_Log::debug('all input for userstate::queryUserStates : '. print_r($block_in, true));
	for($i=0; $i<=floor($count/self::BLOCK_QUERY_MAX); $i++)
	{
		$rel_block_in = array_slice($block_in, $i*self::BLOCK_QUERY_MAX, self::BLOCK_QUERY_MAX, true);
		if(empty($rel_block_in))
		{
			break;
		}
		$rel_block_in = array('reqs'=>$rel_block_in);
		Bingo_Log::debug('input for userstate::queryUserStates : '. print_r($rel_block_in, true));
		$rel_block_out = Tieba_Service::Call('userstate', 'queryUserStates', $rel_block_in);
		Bingo_Log::debug('output for userstate::queryUserStates : '. print_r($rel_block_out, true));
		if(false === $rel_block_out || 0 != $rel_block_out['errno'] || empty($rel_block_out['res']))
		{
			Bingo_Log::warning("fail to call userstate::queryUserStates, input:".serialize($rel_sign_in)."  output: ". serialize($rel_sign_out));
			continue;
		}
		$rel_block_info = $rel_block_out['res'];
		foreach($rel_block_info as $forum_id=>$block_info)
		{
			if(false !== $block_info)
			{
				$sign_in_ret['succ_info'][$forum_id] = self::_errRet(Tieba_Errcode::ERR_USER_IS_BLOCK);
				$arr_like_info['pass'][$forum_id]['is_block'] = 1;
			}
		}
	}

	//ueg submit TODO
	//$ueg_submit_in = array(
	//	'cmd_no'    => self::ACTS_CTRL_CMD_NO,
	//	'rulegroup' => array('app'),
	//	'app'       => 'sign',
	//	//'forum_id'  => $forum_id,
	//	'user_id'   => $user_id,
	//	'user_ip'   => $user_ip,
	//	'term_type' => $term_type,
	//	'flag'	    => 1,
	//);
	//$ueg_submit_out = Tieba_Service::Call('anti', 'antiActsctrlSubmit', $ueg_submit_in);
	//if (false === $ueg_submit_out) {
	//	Bingo_Log::warning("fail to call acts_ctrl submitPost! input : ". serialize($ueg_submit_in));
	//}

	$succ_forum_id = array();
	foreach($arr_like_info['pass'] as $pass_info)
	{
		$succ_forum_id[] = $pass_info['forum_id'];
	}
	$forum_in = array(
		'forum_id'=>$succ_forum_id,
	);
	Bingo_Log::debug("input for forum::getFnameByFid is ". print_r($forum_in, true));
	$forum_out = Tieba_Service::Call('forum', 'getFnameByFid', $forum_in);
	Bingo_Log::debug("output for forum::getFnameByFid is ". print_r($forum_out, true));
	if(false === $forum_out || 0 != $forum_out['errno'] )
	{
		Bingo_Log::warning("fail to call forum::getFnameByFid for forum_id[$forum_id]! output : ".serialize($forum_out));
	}
	foreach($arr_like_info['pass'] as &$pass_info_v1)
	{
		$pass_info_v1['forum_name'] = "";
	}
	if(isset($forum_out['errno']) && 0 == $forum_out['errno'] && isset($forum_out['forum_name']))
	{
		foreach($arr_like_info['pass'] as &$pass_info_v2)
		{
			if(isset($forum_out['forum_name'][$pass_info_v2['forum_id']]))
			{
				$pass_info_v2['forum_name'] = $forum_out['forum_name'][$pass_info_v2['forum_id']]['forum_name'];
			}
		}
	}

	$user_in = array(
		'user_id'=>array($user_id),
	);   
	$user_out = Tieba_Service::Call('user', 'getUnameByUids', $user_in);
	Bingo_Log::debug("output for user::getUnameByUids [$user_id] is ". print_r($user_out, true));
	if(false == $user_out || 0 != $user_out['errno'])
	{    
		Bingo_Log::warning("fail to call user::getUnameByUids for user_id[$user_id]! output : ".serialize($user_out));
	}    
	$user_name = "";
	if(isset($user_out['output']['unames'][0]['user_id']))
	{    
		$user_name = $user_out['output']['unames'][0]['user_name'];
	}    

	$command_no = self::GRADE_SIGN_CMD_NO;
	switch($term_type)
	{
	case self::TYPE_OF_PC:
		$command_no = self::GRADE_SIGN_CMD_NO;
		break;
	case self::TYPE_OF_MOBILE:
	case self::TYPE_OF_BDBRS:
		$command_no = self::EX_GRADE_SIGN_CMD_NO;
		break;
	case self::TYPE_OF_MOBILE_CLIENT:
	case self::TYPE_OF_BDBRS_FR_MOBILE:
		$command_no = self::MOBLIE_BDBRS_SIGN_CMD_NO;
		break;
	default:
		$command_no = self::GRADE_SIGN_CMD_NO;
	}
	foreach($arr_like_info['pass'] as $pass_info)
	{
		$forum_id = $pass_info['forum_id'];
		$nmq_input = array(
			'command_no'=>$command_no,
			'user_id'=>$user_id,
			'user_name'=>$user_name,
			'forum_id'=>$pass_info['forum_id'],
			'forum_name'=>$pass_info['forum_name'],
			'sign_time'=>$sign_out[$forum_id]['uinfo']['sign_time'],
			'user_sign_rank'=>$sign_out[$forum_id]['uinfo']['user_sign_rank'],
			'cont_sign_num'=>$sign_out[$forum_id]['uinfo']['cont_sign_num'],
			'total_sign_num'=>$sign_out[$forum_id]['uinfo']['cout_total_sing_num'],
			'term_type'=>$term_type,
			'user_ip'=>$user_ip,
			'like_time'=>isset($pass_info['in_time'])?$pass_info['in_time']:0,
			'level_id'=>$pass_info['level_id'],
			'is_like'=>$pass_info['is_like'],
			'like_num'=>isset($pass_info['like_num'])?$pass_info['like_num']:1,
			'cur_score'=>$pass_info['cur_score'],
			'sign_count'=>$sign_out[$forum_id]['finfo']['current_rank_info']['sign_count'],
			'sign_version'=>1,
			'is_from_nmq'=>1,
			'is_from_signnew'=>0,
			'is_sign_succ'=>isset($sign_out[$forum_id]['is_sign_succ'])?$sign_out[$forum_id]['is_sign_succ']:1,
			//'percent'=>0,
			//'member_count'=>0,
		);
		if(isset($pass_info['is_block']) && 1 == $pass_info['is_block'])
		{
			$nmq_input['add_score'] = 0;
		}
		$nmqClass = self::GRADESIGNIN;
		if(isset(self::$TERM_TO_METHOD[$term_type]))
		{   
			$nmqClass = self::$TERM_TO_METHOD[$term_type];
		}   
		Bingo_Log::debug("input for nmq is : ". print_r($nmq_input, true));
		$nmq_out = Tieba_Commit::commit('sign', $nmqClass, $nmq_input);
		Bingo_Log::debug("output for nmq is : ". print_r($nmq_out, true));
		if(!isset($nmq_out['error_no']) || 0 != $nmq_out['error_no'])
		{   
			Bingo_Log::warning("send cmd fail, <uid:$user_id forum_id:$forum_id>, return [".serialize($nmq_out)."]");
			$r_nmq_out = Tieba_Commit::commit('sign', $nmqClass, $nmq_input);
			if(!isset($r_nmq_out['error_no']) || 0 != $r_nmq_out['error_no'])
			{
				Bingo_Log::warning("retry to send cmd fail, <uid:$user_id forum_id:$forum_id>, return [".serialize($r_nmq_out)."]");
			}
		}   
	}

	return $sign_in_ret;
}

/**
 * @brief
 * @arrInput:
 * 	uint32_t forum_id
 * 	uint32_t user_id
 * @return: $arrOutput
 * 	UserSignInfo user_info
 * 	ForumSignInfo forum_info
**/
public static function getUserSignInfo($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['forum_id']) || !isset($arrInput['user_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$forum_id = intval($arrInput['forum_id']);
	$user_id = intval($arrInput['user_id']);

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$user_info = false;
	$forum_info = false;
	//ǩ�������Ϣ
	$forum=array
		(
		 'forum_id' => $forum_id,
		);

	$req_ex=array
		(
		 'need_current_sign_info' => 1,
		 'need_yesterday_sign_info' => 1,
		 'need_weekly_sign_info' => 1,
		 'need_monthly_sign_info' => 1,
		 'need_user_sign_info' => 1,
		 'need_loyalty_info' => 1,
		 'need_org_info' => 1,
		 'need_tip_info' => 1,
		);
	$sign_in = array(
		'forum' => $forum,
		'user_id' => $user_id,
		'req_ex' => $req_ex,
	);
	$sign_out = Dl_Sign_Sign::get_user_sign_info($sign_in);

	if ( !isset($sign_out['user_info']) || !isset($sign_out['forum_info']) )
	{
		Bingo_Log::warning("call Dl_Sign_Sign::get_user_sign_info failed! input: ".serialize($sign_in)." output: ".serialize($sign_out));
		return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
	}
	
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'user_info' => $sign_out['user_info'],
		'forum_info' => $sign_out['forum_info'],
	);
	return $arrOutput;
}

/**
 * @brief
 * @arrInput:
 * 	uint32_t forum_id
 * 	uint32_t user_id[]
 * @return: $arrOutput
 * 	UserSignInfo user_info[]
 * 	ForumSignInfo forum_info
**/
public static function mgetUserSignInfo($arrInput){

        if(!isset($arrInput['forum_id']) || !isset($arrInput['user_id'])){      
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $forum_id = intval($arrInput['forum_id']);
        $arr_user_ids = Tieba_Service::getArrayParams($arrInput, 'user_id');

        if(!isset($arr_user_ids[0]))
        {
                Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if(!self::_init()){
                return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $forum=array(
                'forum_id' => $forum_id,
        );

	$req_ex=array(
		'need_current_sign_info' => 1,
		'need_yesterday_sign_info' => 1,
		'need_weekly_sign_info' => 1,
		'need_monthly_sign_info' => 1,
		'need_user_sign_info' => 1,
		'need_loyalty_info' => 1,
		'need_org_info' => 1,
		'need_tip_info' => 1,
	);
	$sign_in = array(
		'forum'=>$forum,
		'arr_user_ids'=>$arr_user_ids,
		'req_ex'=>$req_ex,
	);
	$sign_out = Dl_Sign_Sign::mget_user_sign_info($sign_in);

	if ( !isset($sign_out['arr_user_info']) || !isset($sign_out['forum_info']) )
	{
		Bingo_Log::warning("call Dl_Sign_Sign::mget_user_sign_info failed! input: ".serialize($sign_in)." output: ".serialize($sign_out));
		return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
	}
	
	$arr_user_info = array();
	foreach($sign_out['arr_user_info'] as $user_info)
	{
		$arr_user_info[$user_info['user_id']] = $user_info;
	}
	
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'arr_user_info' => $arr_user_info,
		'forum_info' => $sign_out['forum_info'],
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t forum_id
 * 	uint32_t user_id
 * @return: $arrOutput
 * 	UserSignInfo user_info
 * 	ForumSignInfo forum_info
**/
public static function getSignRankListByDir($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['level_1_name']) || !isset($arrInput['level_2_name']) || !isset($arrInput['page_type']) || !isset($arrInput['page_no']) || !isset($arrInput['page_size'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}

    if( is_null($arrInput['level_1_name']) || is_null($arrInput['level_2_name']) || intval($arrInput['page_no']) <=0 )
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }

    //input params.
    $level_1_name = $arrInput['level_1_name'];
    $level_2_name = $arrInput['level_2_name'];
    $page_type = intval($arrInput['page_type']);
    $page_no = intval($arrInput['page_no']);
    $page_size = intval($arrInput['page_size']);

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = false;
    
    $level_1_dir_name = $level_1_name.'@'.$level_2_name;   //���̫ɽկ��û�취 

    $forum=array
		(
		 'level_1_dir_name' => $level_1_dir_name,
		);

	$req_ex=array
		(
            'page_type' => $page_type,
            'page_no' => $page_no,
            'page_size' => $page_size,   
        );
	$sign_in = array(
		'req' => $forum,
		'req_ex' => $req_ex,
	);
	$sign_out = Dl_Sign_Sign::get_sign_rank_list($sign_in);

	if ( !is_array($sign_out['res']) )
	{
		Bingo_Log::warning("call Dl_Sign_Sign::get_user_sign_info failed! input: ".serialize($sign_in)." output: ".serialize($sign_out));
		return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
	}

   $output = $sign_out['res']; 

	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t forum_id
 * 	uint32_t user_id
 * @return: $arrOutput
 * 	UserSignInfo user_info
 * 	ForumSignInfo forum_info
**/
public static function getSignRankListByFid($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['forum_id']) || !isset($arrInput['page_type']) || !isset($arrInput['page_no']) || !isset($arrInput['page_size'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}

    if( is_null($arrInput['forum_id']) || intval($arrInput['page_no']) <0 )
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }

    //input params.
    $forum_id = intval($arrInput['forum_id']);
    $page_type = intval($arrInput['page_type']);
    $page_no = intval($arrInput['page_no']);
    $page_size = intval($arrInput['page_size']);

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = false;
    

    $forum=array
		(
		 'forum_id' => $forum_id,
		);

	$req_ex=array
		(
            'page_type' => $page_type,
            'page_no' => $page_no,
            'page_size' => $page_size,   
        );
	$sign_in = array(
		'req' => $forum,
		'req_ex' => $req_ex,
	);
	$sign_out = Dl_Sign_Sign::get_sign_rank_list($sign_in);

	if ( !is_array($sign_out['res']) )
	{
		Bingo_Log::warning("call Dl_Sign_Sign::get_user_sign_info failed! input: ".serialize($sign_in)." output: ".serialize($sign_out));
		return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
	}

   $output = $sign_out['res']; 

	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t forum_id[]
 * 	uint32_t user_id
 * @return: $arrOutput
 * 	UserSignInfo arr_user_info[]
**/
public static function getUserSignForums($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['forum_id']) || !isset($arrInput['forum_id']) || !isset($arrInput['user_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$forum_id = Tieba_Service::getArrayParams($arrInput, 'forum_id');
	$user_id = intval($arrInput['user_id']);
	if(!isset($forum_id[0]))
	{
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$arr_user_info = false;

	//ǩ�������Ϣ
	$forum=array();
	foreach($forum_id as $key => $fid)
	{
		$forum[$key]=array('forum_id'=>intval($fid));
	}

	$req_ex=array
		(
		 'need_current_sign_info' => 1,
		 'need_yesterday_sign_info' => 1,
		 'need_weekly_sign_info' => 1,
		 'need_monthly_sign_info' => 1,
		 'need_user_sign_info' => 1,
		 'need_loyalty_info' => 1,
		 'need_org_info' => 1,
		 'need_tip_info' => 1,
		);
	$sign_in = array(
		'forum' => $forum,
		'user_id' => $user_id,
		'req_ex' => $req_ex,
	);
	$sign_out = Dl_Sign_Sign::get_user_sign_forums($sign_in);

	if(!is_array($sign_out['arr_user_info']) || count($sign_out['arr_user_info']) != count($forum_id) )
	{
		Bingo_Log::warning("call Dl_Sign_Sign::get_user_sign_info failed! input: ".serialize($sign_in)." output: ".serialize($sign_out));
		return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
	}
	
	$item_num = count($sign_out['arr_user_info']);
	$arr_user_info = array();
	for($i=0; $i<$item_num; $i++)
	{
		$fid = intval($forum_id[$i]);
		$arr_user_info[$fid] = $sign_out['arr_user_info'][$i];
	}
	
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'arr_user_info' => $arr_user_info,
	);
	return $arrOutput;
}

/**
 * @brief
 * @arrInput:
 * 	uint32_t forum_id
 * 	uint32_t user_id
 * @return: $arrOutput
 * 	UserSignInfo user_info
 * 	UserDaySignInfo his_info[]
**/
public static function getUserMonSignInfo($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['forum_id']) || !isset($arrInput['user_id']) || !isset($arrInput['year']) || !isset($arrInput['month'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$forum_id = intval($arrInput['forum_id']);
	$user_id = intval($arrInput['user_id']);
	$year = intval($arrInput['year']);
	$month = intval($arrInput['month']);
    if($year>self::MAX_YEAR || $year<self::MIN_YEAR)
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    } 
    if($month>self::MAX_MONTH || $month<self::MIN_MONTH)
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    } 


	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//ǩ�������Ϣ
	$forum=array(
		 'forum_id' => $forum_id,
		);

	$time_req=array(
            'year' => $year,
            'month' => $month,
        );
    $cc_req=array(
        'need_user_sign_info' => 1,
        'need_month_sign_info' => 1, 
            );

	$sign_in = array(
		'forum' => $forum,
		'user_id' => $user_id,
		'time_req' => $time_req,
		'cc_req' => $cc_req,
    );

	$sign_out = Dl_Sign_Sign::get_user_mon_sign_info($sign_in);

	if ( !isset($sign_out['user_info']) || !isset($sign_out['his_info']) )
	{
		Bingo_Log::warning("call Dl_Sign_Sign::get_user_mon_sign_info failed! input: ".serialize($sign_in)." output: ".serialize($sign_out));
		return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
	}
	
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'user_info' => $sign_out['user_info'],
		'his_info' => $sign_out['his_info'],
	);
	return $arrOutput;
}

public static function signInForSync($arrInput)
{
	if (!isset($arrInput ['format']))
       	{
		$arrInput ['format'] = 'mcpack';
	}
	$strFormat = strtolower ($arrInput ['format']);
	if ($strFormat !== 'mcpack' && $strFormat !== 'json' && $strFormat !== 'mcpackraw' ) {
		Bingo_Log::warning ("input params wrong format:$strFormat.");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}
	$arrInput = Tieba_Service::getArrayParams($arrInput, 'data');
	$is_from_nmq = intval($arrInput['is_from_nmq']);
	$is_from_signnew = intval($arrInput['is_from_signnew']);
	if(0 === $is_from_signnew || 1 === $is_from_nmq)
	{
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}

	$comman_no = intval($arrInput['command_no']);
	if(self::FOR_RESIGN_MIGRATE_CMD === $comman_no)
	{
		$ret = self::reSignInForMigrate($arrInput);
	}
	elseif(self::GRADE_SIGN_CMD_NO === $comman_no
		||self::EX_GRADE_SIGN_CMD_NO === $comman_no
		||self::MOBLIE_BDBRS_SIGN_CMD_NO === $comman_no)
	{
		$ret = self::signInForMigrate($arrInput);
	}
	else
	{
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	if(0 !== $ret['errno'])
	{
		header('HTTP/1.1 503 Service Temporarily Unavailable', true, 503);
		return false;
	}
	return $ret;
}

private static function reSignInForMigrate($arrInput)
{
	$forum_id = intval($arrInput['forum_id']);
	$user_id = intval($arrInput['user_id']);
	$cont_sign_num = intval($arrInput['cont_sign_num']);
	$total_sign_num = intval($arrInput['total_sign_num']);

	$db_name = self::_getUserSignTable($forum_id);
	self::$_mysql_record = self::_getDB($db_name);
	if(empty(self::$_mysql_record))
	{
		Bingo_Log::warning("fail to sync resign info for user[$user_id] forum[$forum_id]");
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}

	//��������������
	$fields = array(
		'cont_sign_num'=>$cont_sign_num,
		'total_sign_num'=>$total_sign_num,
	);
	$set_user_sign_ret = self::_setUserInfoForDB($user_id, $forum_id, $fields);
	if(false === $set_user_sign_ret['succ'])
	{
		Bingo_Log::warning("fail to sync resign info for user[$user_id] forum[$forum_id]");
		return $set_user_sign_ret['info'];
	}

	$cache = new Bingo_Cache_Memcached('forum_profile');
	$user_sign_key = self::SIGN_USER . ":$forum_id:$user_id";
	$user_mon_sign_key = self::SIGN_USER_MONTH . ":$forum_id:$user_id";
	$cache->remove($user_sign_key);
	$cache->remove($user_mon_sign_key);

	return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
}

public static function reSignInForScript($arrInput)
{
	if(!isset($arrInput['forum_id']) || !isset($arrInput['user_id']) ||!isset($arrInput['year']) || !isset($arrInput['month']) || !isset($arrInput['day']))
	{    
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}  

	//input params.
	$forum_id = intval($arrInput['forum_id']);
	$user_id = intval($arrInput['user_id']);
	$year = sprintf("%04d", $arrInput['year']);
	$month = sprintf("%02d", $arrInput['month']);
	$day = sprintf("%02d", $arrInput['day']);
	$day_begin_time = mktime(0, 0, 0, $month, $day, $year);

	$cur_time = time();
	$cur_year = date('Y',$cur_time);
	$cur_month = date('m',$cur_time);
	$cur_day = date('d',$cur_time);
	$today_begin_time = mktime(0, 0, 0, $cur_month, $cur_day, $cur_year);

	$last_year = $cur_year;
	$last_month = $cur_month-1;
	if($last_month <= 0)
	{
		$last_year --;
		$last_month = 12;
	}
	$last_month = sprintf('%02d',$last_month);
	$last_mon_begin_time = mktime(0, 0, 0, $last_month, 1, $last_year);

	$last2_year = $last_year;
	$last2_month = $last_month-1;
	if($last2_month <= 0)
	{
		$last2_year --;
		$last2_month = 12;
	}
	$last2_month = sprintf('%02d',$last2_month);
	$last2_mon_begin_time = mktime(0, 0, 0, $last2_month, 1, $last2_year);

	$last3_year = $last2_year;
	$last3_month = $last2_month-1;
	if($last3_month <= 0)
	{
		$last3_year --;
		$last3_month = 12;
	}
	$last3_month = sprintf('%02d',$last3_month);
	$last3_mon_begin_time = mktime(0, 0, 0, $last3_month, 1, $last3_year);
	

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//your code here......

	if($day_begin_time >= $today_begin_time)
	{
		Bingo_Log::trace("forbiden to resign in for today");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}

	$this_mon_ret = self::_getSignMonDetailInfo($forum_id, $user_id, $cur_year, $cur_month);
	$last_mon_ret = self::_getSignMonDetailInfo($forum_id, $user_id, $last_year, $last_month);
	$last2_mon_ret = self::_getSignMonDetailInfo($forum_id, $user_id, $last2_year, $last2_month);
	$last3_mon_ret = self::_getSignMonDetailInfo($forum_id, $user_id, $last3_year, $last3_month);
	if(false === $this_mon_ret['succ']
		|| false === $last_mon_ret['succ']
		|| false === $last2_mon_ret['succ']
		|| false === $last3_mon_ret['succ'])
	{
		Bingo_Log::warning("fail to get user month sign");//TODO
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	}
	$this_mon_record = $this_mon_ret['info'];
	$last_mon_record = $last_mon_ret['info'];
	$last2_mon_record = $last2_mon_ret['info'];
	$last3_mon_record = $last3_mon_ret['info'];

	$int_day = intval($day);
	if($cur_year==$year&&$cur_month==$month)
	{
		$this_mon_record[$int_day] = 1;
	}
	elseif($last_year==$year&&$last_month==$month)
	{
		$last_mon_record[$int_day] = 1;
	}
	elseif($last2_year==$year&&$last2_month==$month)
	{
		$last2_mon_record[$int_day] = 1;
	}
	elseif($last3_year==$year&&$last3_month=$month)
	{
		$last3_mon_record[$int_day] = 1;
	}

	//transfer to bitmap
	$bit_map = array();
	$new_cont_sign_num = 0;	
	$i = $ii = $iii = $iiii = 1;
	$count = 1;
	for($i=$cur_day; $i>=1; $i--)
	{
		if(isset($this_mon_record[$i]))
		{
			$bit_map[$count++] = 1;
			continue;
		}
		$bit_map[$count++] = 0;
	}
	$last_day_count = date('t', $last_mon_begin_time);
	for($ii=$last_day_count; $ii>=1; $ii--)
	{
		if(isset($last_mon_record[$ii]))
		{
			$bit_map[$count++] = 1;
			continue;
		}
		$bit_map[$count++] = 0;
	}
	$last2_day_count = date('t', $last2_mon_begin_time);
	for($iii=$last2_day_count; $iii>=1; $iii--)
	{
		if(isset($last2_mon_record[$iii]))
		{
			$bit_map[$count++] = 1;
			continue;
		}
		$bit_map[$count++] = 0;
	}
	$last3_day_count = date('t', $last3_mon_begin_time);
	for($iiii=$last3_day_count; $iiii>=1; $iiii--)
	{
		if(isset($last3_mon_record[$iiii]))
		{
			$bit_map[$count++] = 1;
			continue;
		}
		$bit_map[$count++] = 0;
	}
	// if not sign in today , will cannot resign
	//TODO
	//$new_cont_sign_num = min($new_cont_sign_num, 60);
	


	$db_name = self::_getUserSignTable($forum_id);
	self::$_mysql_record = self::_getDB($db_name);
	if(empty(self::$_mysql_record))
	{
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	self::$_mysql_record->startTransaction();
	$sign_record_info = self::_getUserInfoForUpdate($user_id, $forum_id);
	if(false === $sign_record_info['succ'])
	{
		self::$_mysql_record->rollback();
		return $sign_record_info['info'];
	}
	$user_sign_info = $sign_record_info['info'];
	$old_cont_sign_num = intval($user_sign_info['cont_sign_num']);
	$old_total_sign_num = intval($user_sign_info['total_sign_num']);
	$last_up_time = intval($user_sign_info['last_up_time']);

	$new_cont_sign_num = 0;
	$fields = array();
	$yes_begin_time = $today_begin_time - 86400;
	//has signed today
	if($last_up_time >= $today_begin_time)
	{
		//reset bitmap
		for($j=1; $j<=$old_cont_sign_num; $j++)
		{
			$bit_map[$j] = 1;
		}

		//get new continue sign num
		$d_num = count($bit_map);
		for($k=1; $k<=$d_num; $k++)
		{
			if(1 === $bit_map[$k])
			{
				$new_cont_sign_num++;
				continue;
			}
			break;
		}
		if($new_cont_sign_num < $old_cont_sign_num)
		{
			$new_cont_sign_num = $old_cont_sign_num;
		}
	}
	//has signed in this two days
	elseif($last_up_time >= $yes_begin_time)
	{
		//get new continue sign num
		$d_num = count($bit_map);
		for($k=2; $k<=$d_num; $k++)
		{
			if(1 === $bit_map[$k])
			{
				$new_cont_sign_num++;
				continue;
			}
			break;
		}
		if($new_cont_sign_num < $old_cont_sign_num)
		{
			$new_cont_sign_num = $old_cont_sign_num;
		}
	}
	//hasn't signed yesterday
	else
	{
		//impossible
		self::$_mysql_record->rollback();
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	$fields['cont_sign_num'] = $new_cont_sign_num;
	$set_user_sign_ret = self::_setUserInfoForDB($user_id, $forum_id, $fields, true);
	if(false === $set_user_sign_ret['succ'])
	{
		self::$_mysql_record->rollback();
		return $set_user_sign_ret['info'];
	}
	self::$_mysql_record->commit();

	$cache = new Bingo_Cache_Memcached('forum_profile');
	$user_sign_key = self::SIGN_USER . ":$forum_id:$user_id";
	$user_mon_sign_key = self::SIGN_USER_MONTH . ":$forum_id:$user_id";
	$cache->remove($user_sign_key);
	$cache->remove($user_mon_sign_key);
	
	return self::_errRet(Tieba_Errcode::ERR_SUCCESS);

}

public static function reSignIn($arrInput)
{
	if(!isset($arrInput['forum_id']) || !isset($arrInput['user_id']) ||!isset($arrInput['year']) || !isset($arrInput['month']) || !isset($arrInput['day']) || !isset($arrInput['term_type']) || !isset($arrInput['user_ip']))
	{    
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}  

	//input params.
	$forum_id = intval($arrInput['forum_id']);
	$user_id = intval($arrInput['user_id']);
	$year = sprintf("%04d", $arrInput['year']);
	$month = sprintf("%02d", $arrInput['month']);
	$day = sprintf("%02d", $arrInput['day']);
	$term_type = intval($arrInput['term_type']);
	$user_ip = intval($arrInput['user_ip']);
	$day_begin_time = mktime(0, 0, 0, $month, $day, $year);

	$cur_time = time();
	$cur_year = date('Y',$cur_time);
	$cur_month = date('m',$cur_time);
	$cur_day = date('d',$cur_time);
	$today_begin_time = mktime(0, 0, 0, $cur_month, $cur_day, $cur_year);

	$last_year = $cur_year;
	$last_month = $cur_month-1;
	if($last_month <= 0)
	{
		$last_year --;
		$last_month = 12;
	}
    $last_month = sprintf('%02d',$last_month);
	$last_mon_begin_time = mktime(0, 0, 0, $last_month, 1, $last_year);

	$last2_year = $last_year;
	$last2_month = $last_month-1;
	if($last2_month <= 0)
	{
		$last2_year --;
		$last2_month = 12;
	}
    $last2_month = sprintf('%02d',$last2_month);
	$last2_mon_begin_time = mktime(0, 0, 0, $last2_month, 1, $last2_year);
	

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//your code here......
	//get like info
	$grade_info = self::_getLikeInfo($user_id, $forum_id);

	//check is black
	if(isset($grade_info['is_black']) && 0 != $grade_info['is_black'])
	{
		Bingo_Log::warning("user[$user_id] is in blacklist of forum[$forum_id]");
		return self::_errRet(Tieba_Errcode::ERR_USER_IS_BLACK);
	}

	//check is block
	$is_block = self::_getBlockInfo($user_id, $forum_id, $user_ip);

	$day60_begin_time = $today_begin_time-60*86400;
	if($day60_begin_time>$day_begin_time)
	{
		Bingo_Log::trace("forbiden to resign in for the day before 60d");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}
	
	if($day_begin_time >= $today_begin_time)
	{
		Bingo_Log::trace("forbiden to resign in for today");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}

	$this_mon_ret = self::_getSignMonDetailInfo($forum_id, $user_id, $cur_year, $cur_month);
	$last_mon_ret = self::_getSignMonDetailInfo($forum_id, $user_id, $last_year, $last_month);
	$last2_mon_ret = self::_getSignMonDetailInfo($forum_id, $user_id, $last2_year, $last2_month);
	if(false === $this_mon_ret['succ']
		|| false === $last_mon_ret['succ']
		|| false === $last2_mon_ret['succ'])
	{
		Bingo_Log::warning("fail to get user month sign");//TODO
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	}
	$this_mon_record = $this_mon_ret['info'];
	$last_mon_record = $last_mon_ret['info'];
	$last2_mon_record = $last2_mon_ret['info'];

	$int_day = intval($day);
	if($cur_year==$year&&$cur_month==$month)
	{
		if(isset($this_mon_record[$int_day]))
		{
			Bingo_Log::warning("user[$user_id] has signed at forum[$forum_id] before for time[$day_begin_time]");
			return self::_errRet(Tieba_Errcode::ERR_IS_SIGNED); 
		}
		$this_mon_record[$int_day] = 1;
	}
	elseif($last_year==$year&&$last_month==$month)
	{
		if(isset($last_mon_record[$int_day]))
		{
			Bingo_Log::warning("user[$user_id] has signed at forum[$forum_id] before for time[$day_begin_time]");
			return self::_errRet(Tieba_Errcode::ERR_IS_SIGNED); 
		}
		$last_mon_record[$int_day] = 1;
	}
	elseif($last2_year==$year&&$last2_month==$month)
	{
		if(isset($last2_mon_record[$int_day]))
		{
			Bingo_Log::warning("user[$user_id] has signed at forum[$forum_id] before for time[$day_begin_time]");
			return self::_errRet(Tieba_Errcode::ERR_IS_SIGNED); 
		}
		$last2_mon_record[$int_day] = 1;
	}

	//transfer to bitmap
	$bit_map = array();
	$new_cont_sign_num = 0;	
	$i = $ii = $iii = 1;
	$count = 1;
	for($i=$cur_day; $i>=1; $i--)
	{
		if(isset($this_mon_record[$i]))
		{
			$bit_map[$count++] = 1;
			continue;
		}
		$bit_map[$count++] = 0;
	}
	$last_day_count = date('t', $last_mon_begin_time);
	for($ii=$last_day_count; $ii>=1; $ii--)
	{
		if(isset($last_mon_record[$ii]))
		{
			$bit_map[$count++] = 1;
			continue;
		}
		$bit_map[$count++] = 0;
	}
	$last2_day_count = date('t', $last2_mon_begin_time);
	for($iii=$last2_day_count; $iii>=1; $iii--)
	{
		if(isset($last2_mon_record[$iii]))
		{
			$bit_map[$count++] = 1;
			continue;
		}
		$bit_map[$count++] = 0;
	}
	// if not sign in today , will cannot resign
	//TODO
	//$new_cont_sign_num = min($new_cont_sign_num, 60);
	


	$db_name = self::_getUserSignTable($forum_id);
	self::$_mysql_record = self::_getDB($db_name);
	if(empty(self::$_mysql_record))
	{
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	self::$_mysql_record->startTransaction();
	$sign_record_info = self::_getUserInfoForUpdate($user_id, $forum_id);
	if(false === $sign_record_info['succ'])
	{
		self::$_mysql_record->rollback();
		return $sign_record_info['info'];
	}
	$user_sign_info = $sign_record_info['info'];
	$old_cont_sign_num = intval($user_sign_info['cont_sign_num']);
	$old_total_sign_num = intval($user_sign_info['total_sign_num']);
	$last_up_time = intval($user_sign_info['last_up_time']);

	//has not signed
	if($last_up_time < $today_begin_time)
	{
		self::$_mysql_record->rollback();
		Bingo_Log::warning("user[$user_id] has not signed at forum[$forum_id]");
		return self::_errRet(Tieba_Errcode::ERR_NOT_SIGN_IN); 
	}

	//has signed
	$day_count = ($today_begin_time-$day_begin_time)/86400;
	if($day_count < $old_cont_sign_num)
	{
		self::$_mysql_record->rollback();
		Bingo_Log::warning("user[$user_id] has signed at forum[$forum_id] before for time[$day_begin_time]");
		return self::_errRet(Tieba_Errcode::ERR_IS_SIGNED); 
	}

	//reset bitmap
	for($j=1; $j<=$old_cont_sign_num; $j++)
	{
		$bit_map[$j] = 1;
	}

	//get new continue sign num
	$new_cont_sign_num = 0;
	$d_num = count($bit_map);
	for($k=1; $k<=$d_num; $k++)
	{
		if(1 === $bit_map[$k])
		{
			$new_cont_sign_num++;
			continue;
		}
		break;
	}

	$existed = true;
	if(0 === $old_total_sign_num)
	{
		$existed = false;
	}
	$new_total_sign_num = $old_total_sign_num + 1;
	$fields = array(
		'cont_sign_num'=>$new_cont_sign_num,
		'total_sign_num'=>$new_total_sign_num,
	);
	$set_user_sign_ret = self::_setUserInfoForDB($user_id, $forum_id, $fields, $existed);
	if(false === $set_user_sign_ret['succ'])
	{
		self::$_mysql_record->rollback();
		return $set_user_sign_ret['info'];
	}
	self::$_mysql_record->commit();

	$set_mon_ret = self::_setUserMonSignInfo($forum_id, $user_id, 0, $day_begin_time /*$cur_time*/, $term_type, $year, $month, $day);
	if(false === $set_mon_ret['succ'])
	{
		Bingo_Log::warning("fail to set month sign info for user[$user_id] in forum[$forum_id]");//TODO
		//return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	}

	$cache = new Bingo_Cache_Memcached('forum_profile');
	$user_sign_key = self::SIGN_USER . ":$forum_id:$user_id";
	$user_mon_sign_key = self::SIGN_USER_MONTH . ":$forum_id:$user_id";
	$cache->remove($user_sign_key);
	$cache->remove($user_mon_sign_key);
	
	if(false === $is_block)
	{
		$add_score_in = array(
			'forum_id'=>$forum_id,
			'user_id'=>$user_id,
			'op_type'=>1,
			'op_score'=>self::$RESIGN_TYPE_TO_SCORE[$term_type],
			'op_user_id'=>$user_id,
		);
		Bingo_Log::debug("input for perm->setUserForumScore : ". print_r($add_score_in, true));
		$add_score_out = Tieba_Service::Call('perm', 'setUserForumScore', $add_score_in, null, null, 'post', 'php', 'utf-8');
		Bingo_Log::debug("output for perm->setUserForumScore : ". print_r($add_score_out, true));
		if(!isset($add_score_out['errno']) || 0 !== $add_score_out['errno'])
		{
			Bingo_Log::warning("fail to add score for user_id[$user_id] forum_id[$forum_id] : ". print_r($add_score_out, true));
		}
	}

	$arrInput = array(
		'user_id'=>$user_id,
		'forum_id'=>$forum_id,
		'year'=>$year,
		'month'=>$month,
		'day'=>$day,
		'term_type'=>$term_type,
		'cont_sign_num'=>$new_cont_sign_num,
		'total_sign_num'=>$new_total_sign_num,
		'command_no'=>self::FOR_RESIGN_MIGRATE_CMD,
		'is_from_signnew'=>0,
		'is_from_nmq'=>1,
	);
	//self::_sendNmqCmd($arrInput);
	$nmqClass = self::RESIGN;
	Bingo_Log::debug("input for nmq is : ". print_r($arrInput, true));
	$nmq_out = Tieba_Commit::commit('sign', $nmqClass, $arrInput);
	Bingo_Log::debug("output for nmq is : ". print_r($nmq_out, true));
	if(!isset($nmq_out['error_no']) || 0 != $nmq_out['error_no'])
	{
		Bingo_Log::warning("send cmd fail, user[$user_id] forum_id[$forum_id], output : ".serialize($nmq_out));
		$r_nmq_out = Tieba_Commit::commit('sign', $nmqClass, $arrInput);
		if(!isset($r_nmq_out['error_no']) || 0 != $r_nmq_out['error_no'])
		{
			Bingo_Log::warning("retry to send cmd fail, <uid:$user_id forum_id:$forum_id>, return [".serialize($r_nmq_out)."]");
		}
	}

	return self::_errRet(Tieba_Errcode::ERR_SUCCESS);

}

public function setUserSignRecordForScript($arrInput)
{
	if(!isset($arrInput['forum_id']) || !isset($arrInput['user_id']) ||!isset($arrInput['year']) || !isset($arrInput['month']) || !isset($arrInput['day']) || !isset($arrInput['term_type']))
	{    
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}  

	//input params.

	$user_id = intval($arrInput['user_id']);
	$forum_id = intval($arrInput['forum_id']);
	$term_type = intval($arrInput['term_type']);
	$year = intval($arrInput['year']);
	$month = intval($arrInput['month']);
	$day = intval($arrInput['day']);
	$year_str = sprintf("%04d", $year);
	$month_str = sprintf("%02d", $month);
	$day_str = sprintf("%02d", $day);
	$day_begin_time = mktime(0, 0, 0, $month, $day, $year);
	$set_mon_ret = self::_setUserMonSignInfo($forum_id, $user_id, 0, $day_begin_time, $term_type, $year_str, $month_str, $day_str);
	if(false === $set_mon_ret['succ'] || false === $set_mon_ret['info'])
	{
		Bingo_Log::warning("fail to set month sign info for user[$user_id] in forum[$forum_id]");//TODO
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	}
	return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
}

private static function _getUserInfoForUpdate($user_id, $forum_id)
{
	$user_sign_table = self::USER_SIGN_TABLE . "_" . $user_id%self::USER_SIGN_TABLE_NUM;
	$user_sign_query_sql = "select cont_sign_num, total_sign_num, last_up_time from $user_sign_table where user_id=$user_id and forum_id=$forum_id for update";
	$user_sign_query_out = self::$_mysql_record->query($user_sign_query_sql);
	if(false === $user_sign_query_out)
	{
		Bingo_Log::warning("fail to get sign info, sql[$user_sign_query_sql]");
		$create_table_sql = "create table $user_sign_table like " . self::USER_SIGN_TABLE . ";"; 
		Bingo_Log::debug("user_month_sign_sql[$create_table_sql]");
		$create_table_out = self::$_mysql_record->query($create_table_sql);
		Bingo_Log::debug("result of create table $user_sign_table : ". print_r($create_table_out, true));
		if(false === $create_table_out)
		{            
			Bingo_Log::warning("fail to create table[$user_sign_table]");
		}            
		else 
		{            
			Bingo_Log::trace("succ to create table[$user_sign_table]");
			$user_sign_query_out = self::$_mysql_record->query($user_sign_query_sql);
			Bingo_Log::debug("result of query user sign record : ". print_r($user_sign_query_out, true)); 
			if(false === $user_sign_query_out)
			{            
				Bingo_Log::warning("fail to query user sign info , sql[$user_sign_query_sql]");
				//return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
				return array(
					'succ'=>false,
					'info'=>self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL),
				);
			}
		}
	}
	Bingo_Log::debug("sign info for user[$user_id] forum[$forum_id] : ". print_r($user_sign_query_out, true));
	$user_sign_out = array(
		'cont_sign_num'=>0,
		'total_sign_num'=>0,
	);
	if(isset($user_sign_query_out[0]))
	{
		$user_sign_out = $user_sign_query_out[0];
	}
	return array(
		'succ'=>true,
		'info'=>$user_sign_out,
	);
}

private static function _setUserInfoForDB($user_id, $forum_id, $fields, $existed=true)
{
	$db_name = self::_getUserSignTable($forum_id);
	self::$_mysql_record = self::_getDB($db_name);

	$user_sign_table = self::USER_SIGN_TABLE . "_" . $user_id%self::USER_SIGN_TABLE_NUM;

	if(false === $existed)
	{
		$col_names = array(
			'user_id',
			'forum_id',
		);
		$col_values = array(
			$user_id,
			$forum_id,
		);
		foreach($fields as $field=>$value)
		{
			$col_names[] = $field;
			$col_values[] = $value;
		}
		$col_name_str = implode(',', $col_names);
		$col_value_str = implode(',', $col_values);
		$user_sign_insert_sql = "insert ignore into $user_sign_table ($col_name_str) values($col_value_str)";
		Bingo_Log::debug("user_sign_insert_sql : [$user_sign_insert_sql]");
		$user_sign_insert_out = self::$_mysql_record->query($user_sign_insert_sql);
		Bingo_Log::debug("result of adding new user sign info : ". print_r($user_sign_insert_out, true));
		if(false === $user_sign_insert_out)
		{
			Bingo_Log::warning("fail to insert sign info, sql[$user_sign_insert_sql]");
			return array(
				'succ'=>false,
				'info' => self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL),
			);
		}
	}
	else
	{
		$col_name2value_arr = array();
		foreach($fields as $field=>$value)
		{
			$col_name2value[] = "$field=$value";
		}
		$col_name2value_str = implode(',', $col_name2value);
		$user_sign_update_sql = "update $user_sign_table set $col_name2value_str where user_id=$user_id and forum_id=$forum_id";
		Bingo_Log::debug("user_sign_update_sql : [$user_sign_update_sql]");
		$user_sign_update_out = self::$_mysql_record->query($user_sign_update_sql);
		Bingo_Log::debug("result of updating user sign info : ". print_r($user_sign_update_out, true));
		if(false === $user_sign_update_out)
		{
			Bingo_Log::warning("fail to update sign info, sql[$user_sign_update_sql]");
			return array(
				'succ'=>false,
				'info' => self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL),
			);
		}
	}

	return array(
		'succ'=>true,
	);
}

private static function _setUserMonSignInfo($forum_id, $user_id, $user_sign_rank, $sign_time, $term_type, $sign_year, $sign_month, $sign_day)
{
	self::$_mysql_mon = self::_getDB(self::DATABASE_NAME_MON);
	if(empty(self::$_mysql_mon))
	{
		//return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		return array(
			'succ'=>false,
			'info'=>self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL),
		);
	}
	$sign_day = intval($sign_day);
	$user_month_sign_table = self::USER_MONTH_SIGN_TABLE . "_" . $user_id % self::USER_MONTH_SIGN_TABLE_NUM . "_$sign_year$sign_month";
	$user_month_sign_sql = "insert ignore into $user_month_sign_table (user_id, forum_id, day, sign_rank, sign_time, term_type, forum_signature) values($user_id, $forum_id, $sign_day, $user_sign_rank, $sign_time, $term_type, 0);";
	Bingo_Log::debug("user_month_sign_sql[$user_month_sign_sql]");
	$user_month_sign_out = self::$_mysql_mon->query($user_month_sign_sql);
	Bingo_Log::debug("result of insert user month sign: ". print_r($user_month_sign_out, true)); 
	$set_ret = true;
	if(false === $user_month_sign_out)
	{
		Bingo_Log::warning("fail to insert sign_month_sign again, sql[$user_month_sign_sql]");
		$set_ret = false;
	}
	return array(
		'succ'=>true,
		'info'=>$set_ret,
	);
}


private static function _getSignMonDetailInfo($forum_id, $user_id, $year, $month)
{
	//self::$_mysql_mon = self::_getDB_MON();
	self::$_mysql_mon = self::_getDB(self::DATABASE_NAME_MON);
	if(empty(self::$_mysql_mon))
	{
		Bingo_Log::warning("fail to get mysql instance.");
		return array(
			'succ'=>false,
			'info'=>self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL),
		);
	}
	$mon_sign_table = self::USER_MONTH_SIGN_TABLE . "_" . $user_id%self::USER_MONTH_SIGN_TABLE_NUM . "_$year$month";
	$sign_mon_detail_query_sql = "select day from $mon_sign_table where user_id=$user_id and forum_id=$forum_id order by day asc;";//TODO
	$sign_mon_detail_query_out = self::$_mysql_mon->query($sign_mon_detail_query_sql);
	if(false === $sign_mon_detail_query_out)
	{
		Bingo_Log::warning("fail to get mon sign info, sql[$sign_mon_detail_query_sql]");
		$create_table_sql = "create table $mon_sign_table like " . self::USER_MONTH_SIGN_TABLE . ";"; 
		Bingo_Log::debug("user_month_sign_sql[$create_table_sql]");
		$create_table_out = self::$_mysql_mon->query($create_table_sql);
		Bingo_Log::debug("result of create table $mon_sign_table : ". print_r($create_table_out, true));
		if(false === $create_table_out)
		{            
			Bingo_Log::warning("fail to create table[$mon_sign_table]");
		}            
		else 
		{            
			Bingo_Log::trace("succ to create table[$mon_sign_table]");
			$user_sign_query_out = self::$_mysql_mon->query($sign_mon_detail_query_sql);
			Bingo_Log::debug("result of query user mon sign : ". print_r($user_sign_query_out, true)); 
			if(false === $user_sign_query_out)
			{            
				Bingo_Log::warning("fail to get sign mon sign info, sql[$sign_mon_detail_query_sql]");
				return array(
					'succ'=>false,
					'info'=>self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL),
				);
			}
		}
	}
	Bingo_Log::debug("sign month detail for user[$user_id] forum[$forum_id] : ". print_r($sign_mon_detail_query_out, true));
	$sign_record = array();
	foreach($sign_mon_detail_query_out as $v)
	{
		$day = $v['day'];
		$sign_record[$day] = 1;
	}
	return array(
		'succ'=>true,
		'info'=>$sign_record,
	);
}

private static function _getUserSignTable($mod_id)
{
	$mod = $mod_id%4;
	if(0 === $mod)
	{
		$db_name = self::DATABASE_TIEBA_SIGN_1;
	}
	elseif(1 === $mod)
	{
		$db_name = self::DATABASE_TIEBA_SIGN_2;
	}
	elseif(2 === $mod)
	{
		$db_name = self::DATABASE_TIEBA_SIGN_3;
	}
	elseif(3 === $mod)
	{
		$db_name = self::DATABASE_TIEBA_SIGN_4;
	}
	return $db_name;
}

private static function _getBlockInfo($user_id, $forum_id, $user_ip)
{
	$anti_in = array(
		'req'=>array(
			'forum_id'=>$forum_id,
			'user_id'=>$user_id,
			'ip'=>$user_ip,
		),
	);
	Bingo_Log::debug("input for anti::antiUserBlockQuery is ". print_r($anti_in, true));
	$anti_out = Tieba_Service::call("anti", 'antiUserBlockQuery',$anti_in, null, null, 'post', 'php', 'utf-8');
	Bingo_Log::debug("output for anti::antiUserBlockQuery is ". print_r($anti_out, true));

	if(empty($anti_out) || 0 != $anti_out['errno'])
	{
		Bingo_Log::warning("fail to call anti::antiUserBlockQuery, output : ". serialize($anti_out));
	}

	$is_block = false;
	if(isset($anti_out['is_block']) && 0 != $anti_out['is_block'])
	{
		Bingo_Log::trace("user[$user_id] is in blocked in forum[$forum_id] ; output : ". serialize($out));
		$is_block = true;
	}
	return $is_block;
}

private static function _getLikeInfo($user_id, $forum_id)
{
	$perm_in = array(
		'user_id'=>$user_id,
		'forum_ids'=>array($forum_id),
	);
	Bingo_Log::debug("input for perm::mgetUserLevel is ". print_r($perm_in, true));
	$perm_out = Tieba_Service::Call('perm', 'mgetUserLevel', $perm_in, null, null, 'post', 'php', 'utf-8');
	Bingo_Log::debug("output for perm::mgetUserLevel is ". print_r($perm_out, true));

	if(empty($perm_out) || 0 !== $perm_out['errno'] || !isset($perm_out['score_info'][0]))
	{
		Bingo_Log::warning("fail to get user level : ". serialize($perm_out));
	}

	$grade_info = array(
		'is_like'=>0,
		'is_black'=>0,
		'like_num'=>0,
		'in_time'=>0,
		'cur_score'=>0,
	);
	if(isset($perm_out['score_info'][0]))
	{
		$grade_info = $perm_out['score_info'][0];
	}
	return $grade_info;
}

}
