<?php
/**
 * @brief common config
 */
class CommonConf{
	
	const PAGE_TYPE_TODAY	= 0 ;// ��������
	const PAGE_TYPE_YESTODAY= 1 ;// ��������
	const PAGE_TYPE_WEEK 	= 2 ;// ������
	const PAGE_TYPE_MONTH	= 3 ;// ������
	
	const PAGE_SIZE			= 20;
		
	const TYPE_OF_PC = 0;	//��pcǩ��	add by hlx 2012-10-31
	const TYPE_OF_MOBILE = 1;	//������ǩ��	add by hlx 2012-10-31
	const TYPE_OF_BDBRS = 2;  //�Ӱٶ������ǩ�� added by keyang 2012-12-24
	const TYPE_OF_BDBRS_FR_MOBILE = 3;  //�����߰ٶ������ǩ�� added by keyang 2013-1-10
	const TYPE_OF_MOBILE_CLIENT = 4;  //�����߿ͻ���ǩ�� added by gaozhanhua 2013-5-21
	const WAP_OPEN_ID = 'wap';	//��ͨ�����ܰ��openid	add by hlx 2012-11-1
	const TBCLIENT_OPEN_ID = 'tbclient';	//�ͻ��ˡ���˹��openid	add by hlx 2012-11-1
	const IPADWEB_OPEN_ID  = 'webipad';	//webipad  add by zcy 2013-04-22
	
	public static $arrPageTypeList = array(
											self::PAGE_TYPE_MONTH ,
											self::PAGE_TYPE_TODAY ,
											self::PAGE_TYPE_YESTODAY ,
											self::PAGE_TYPE_WEEK ,
											);
												

	//�豸��list
	public static $arrTermNameList = array(
		'pc',
		'mobile',
		'brs',
		'mobile_brs',
		'mobile_client',
	);	
	//�豸��list
	public static $arrSignInfo = array(
		'ǩ������+2������ǩ��+4',
		'�ֻ�ǩ������+3������ǩ��+5',
		'�ٶ������ǩ������+3������ǩ��+5',
		'�ٶ��ֻ������ǩ������+4������ǩ��+6',
		'�ֻ��ͻ���ǩ������+4������ǩ��+6',
	);
}
?>
