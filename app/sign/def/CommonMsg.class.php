<?php
/**
 * CommonMsg
 * <AUTHOR> <<EMAIL>>
 * @since 2012-03-28
 *
 */
class CommonMsg
{
	const CommonErrnoKey	= 'no';		// ������key
	const CommonErrmsgKey	= 'error';	// ������Ϣkey
	const CommonInfoKey		= 'data';	// ������Ϣkey
	/*
	 * ����Ŷ���
	 */
	const SUCCESS = 0;	// ����

	const GET_GOODS_ERROR = 1001;	
	
	const READ_MYSQL_ERROR = 1002;
	
	const SOMETHING_ERROR = 1003;
	
	const USER_IS_LOGOUT = 4;
	
	const MEMBER_NAME_ERROR = 1006;
	
	const ACTS_CTRL_ERROR = 1007;
	
	const GET_FORUM_ID_ERROR = 1010;
	
	const USER_IS_NOT_MEMBER = 1011;
	
	const COMMIT_CHECK_INVALID = 1012;
	
	const USER_IS_BLACK = 1023; //�û��ں�����
	
	const USER_IS_BLOCK = 9; //�û������
	
	const NOT_OPEN = 1027; //�ݲ�����
	
	const USER_WITHOUT_NAME = 9000; //���û���
	
	const TOO_MANY_SIGN		= 1100; //ǩ������̫��
	
	const ALREADY_SIGN		= 1101; //�Ѿ�ǩ������
	
	const TOO_SOON_TO_SIGN		= 1102; //�Ѿ�ǩ������
	
	const TERM_CHECK_INVALID		= 1103; //ǩ���ն���֤ʧ��	add by hlx 2012-10-31

	const BLOCK_SIGN_NOT_ADD_SCORE		= 1104; //����û�����ǩ�����ܼӾ���	add by ZCY 2013-10-24
	
	/*
	 * ������Ϣ����
	 */
	const DEFAULT_ERRMSG = '�������ˣ�������һ�°�';
	
	public static $ERR_MSG = array(
		CommonMsg::SUCCESS				=> '',
		CommonMsg::GET_GOODS_ERROR 		=> self::DEFAULT_ERRMSG,
		CommonMsg::READ_MYSQL_ERROR 	=> '��������С���ˣ���ǩһ������~',//'��������С���ˣ�����һ�ΰ�',
		CommonMsg::SOMETHING_ERROR	 	=> '�����������ˣ���ǩһ�ν�����',//'�����������ˣ�����һ�ν�����',
		CommonMsg::USER_IS_LOGOUT 		=> '�����˳���¼�������µ�¼��',
		CommonMsg::COMMIT_CHECK_INVALID	=> '���������˯�ˣ���ǩһ��������',//'���������˯�ˣ�����һ��������',
		CommonMsg::MEMBER_NAME_ERROR 	=> '�û�����Ļ�Ա���ƴ���',
		CommonMsg::ACTS_CTRL_ERROR 		=> 'ǩ��̫Ƶ���˵㣬��ϢƬ��������:)',//'ǩ��̫Ƶ���˵㣬��ϢƬ��������',
		CommonMsg::GET_FORUM_ID_ERROR 	=> '����Ŀ¼�����������뵽����ǩ���ɷ���',//
		CommonMsg::USER_IS_NOT_MEMBER 	=> '����δ����˰ɻ�ȼ�����',
		CommonMsg::USER_IS_BLACK		=> '�����ں������У����ܲ�����',
		CommonMsg::USER_IS_BLOCK		=> '�����ڷ�������У����ܲ�����',
		CommonMsg::NOT_OPEN 			=> '�ݲ����š�',
		CommonMsg::USER_WITHOUT_NAME	=> '�û��û���Ϊ�ա�',
		CommonMsg::TOO_MANY_SIGN		=> '���ʱ�֣�����һ����ʼǩ�����˺ö࣬��Ҫ���ȼ���������ǩ��~',//'��Ҷ�������1���������˵㣬��ǩһ������~',
		CommonMsg::ALREADY_SIGN			=> '�ף���֮ǰ�Ѿ�ǩ����',//'�ף��Ѿ��ɹ�ǩ����Ŷ~',
		CommonMsg::TOO_SOON_TO_SIGN 	=> '��ǩ��̫���� ���ȿ�����������ǩ��:)',//'��ǩ��̫���� �����Ժ�����ǩŶ�������Թ�䱾�ɣ������������Ȥ������Ŷ~',
		CommonMsg::TERM_CHECK_INVALID	=> '����������Ӧ��������һ��',
		CommonMsg::BLOCK_SIGN_NOT_ADD_SCORE	=> '�ף����ѱ������ǩ���ɹ������޷��Ǿ���Ŷ~',
	);
	
	/*
	 * ���ݴ���Ż�ȡ������Ϣ����
	 */
	public static function getErrMsg($intErrno) {
		if (! array_key_exists($intErrno, CommonMsg::$ERR_MSG)) {
			return CommonMsg::DEFAULT_ERRMSG;
		}
		return CommonMsg::$ERR_MSG[$intErrno];
	}
}
