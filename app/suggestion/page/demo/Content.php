<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-04-11 19:06:26
 * @version
 */
class Page_Demo_Content extends Page_Base {

    public static function build($pageInput){

        $uiConf = Util_Conf::getUiConf();

        $serviceInput = array(
            'pn' => $pageInput['pn'],
            'forum_id' => $pageInput['forum_id'],
            'post_per_page' => $uiConf['post_per_page']
        );

        $ret = Tieba_Service::call('post','getList',$serviceInput);
        if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']){
            Bingo_Log::warning('call service thread::getList failed,errno[' . $ret['errno'] . '],errmsg[' . $ret['errmsg'] . ']');
            return false;
        }
        Bingo_Page::assign('content', $ret['content']);
        return true;
    }
}
?>