//@description: 主要提供搜吧相关服务（自动提示、喜欢的吧、搜吧结果）
//@author:  yunting0000000
struct forum_intro{
	string forum_name; 					//吧名称
	string forum_id; 					//吧id
	string avastar = optional();		//吧头像
	string slogan = optional();			//吧标语
	string dir = optional(); 			//吧目录
	uint32_t member_num=optional();		//吧成员个数
	uint32_t thread_num=optional();		//吧帖子数
	uint32_t sug_type;					//吧类型 0:匹配吧  1：tag关联吧  2：关联吧 3：人工干预关联吧 4：sug为空推荐吧 5:主题词匹配吧
};

struct ret_info {
	forum_intro intro[]; 
};

struct sug_ex_input
{
	string keyword;		//搜索关键词
	uint32_t limit;		//显示多少个
	uint32_t offset;	//从哪里开始
	uint32_t width;		//吧图像宽度
	uint32_t height;	//吧图像高度
	uint32_t flag;		//是否需要吧详细信息   0:需要详细信息(一般不用传，默认为0)
};

service suggestion{
	/**
	*@brief : 获取搜吧自动提示接口
	*@param [in] 	: keyword   :  string    :    吧名称
	*@param [out]	: ret	    :  ret_info    :  返回的相关信息
	**/
	void getSug(string keyword,out ret_info ret);
	/**
	*@brief : 获取用户喜欢的吧接口
	*@param [in] 	: user_id  : uint64_t   :  吧名称
	*@param [out]	: ret	   : ret_info     :  返回的相关信息
	**/
	void getFavoSug(uint64_t user_id,out ret_info ret);
	/**
	*@brief : 搜吧结果接口
	*@param [in] 	: input   :   sug_ex_input  : 搜吧初始化参数
	*@param [out]	: ret	  :  ret_info         : 返回的相关信息
	**/
	void getSugEx(sug_ex_input input,out ret_info ret);	
};
