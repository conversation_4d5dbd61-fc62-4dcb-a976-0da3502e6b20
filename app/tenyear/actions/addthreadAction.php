<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-10-22 11:20:20
 * @comment index
 * @version
 */
class addthreadAction extends Util_Base {

    public function execute(){

    	try {
    		
	    	if ( Tieba_Session_Socket::isLogin() !== true ){
	
				Bingo_Log::warning('checkLogin failure!the user not logined!');
				throw new Exception(Tieba_Errcode::ERR_POST_CT_USER_NOT_LOGIN );
	
			}
			self::_checkTbs();
			
    		$arrParamsList = array();
    		$arrParamsList["user_id"] = Tieba_Session_Socket::getLoginUid();
    		
			$strUserName = Tieba_Session_Socket::getLoginUname();
			if ( is_gbk($strUserName) ){
				$arrParamsList["user_name"] = gbk_to_utf8($strUserName);
				$arrParamsList["op_uname"] = gbk_to_utf8($strUserName);
			}else{
				$arrParamsList["user_name"] = trim($strUserName);
				$arrParamsList["op_uname"] = trim($strUserName);
				
			}
			$arrParamsList["baiduuid"] = Tieba_Session_Socket::getBaiduid();
			
			$arrParamsList["cookie"] = Tieba_Session_Socket::getBDUSS();
			
			$arrParamsList["email"] = Tieba_Session_Socket::getEmail();
			$arrParamsList["mobilephone"] =  Tieba_Session_Socket::getMobilephone();
			$arrParamsList["no_un"] = Tieba_Session_Socket::getNo_un();
			$arrParamsList["user_ip"] =Bingo_Http_Ip::ip2long(Bingo_Http_Ip::getConnectIp());
			
	
			$arrParamsList["op_uid"] = Tieba_Session_Socket::getLoginUid() ;
			$arrParamsList["op_ip"] = Bingo_Http_Ip::ip2long(Bingo_Http_Ip::getConnectIp());
			
			
            $arrParamsList['title']     = Bingo_Http_Request::getPostRaw ( 'title', '' );
			$arrParamsList['prefix']     = Bingo_Http_Request::getPostRaw ( 'prefix', '' );
			$arrParamsList['content']   = Bingo_Http_Request::getPostRaw ( 'content', '' );
			
			
			$check_content = '����ʮ�꣬ʱ��Ҵң��ٻ��ף��׾Թ�϶һ���ӡ���Щ��ɫ���ഺ�Σ���ͨ�ò�������ͨ�������ɵĶ������־�����׷������¥��������磬�㼣�����ա�';
			$check_content = gbk_to_utf8($check_content);
			$length_check = strlen($check_content);
			$need_check_content = str_replace("&nbsp;","",$arrParamsList['content']);;
			if(substr($need_check_content,0,$length_check) != $check_content)
			{
				
				Bingo_Log::warning('commit content not allowed!'.$arrParamsList['content']);
				throw new Exception(Tieba_Errcode::ERR_PARAM_ERROR );
			}
			else
			{
				$check_other_content = substr($need_check_content,$length_check);
				
				if(!preg_match("/\<br\/\><a[^\>]*?\>[^\<]+\<\/a\>\<br\/\>\<img[^\>]*?\>/",$check_other_content,$match))
				{
					Bingo_Log::warning('commit content not allowed!'.$arrParamsList['content']);
					throw new Exception(Tieba_Errcode::ERR_POST_CT_USER_NOT_LOGIN );
					
				}
				
			}
			$arrParamsList['rich_text'] = ( bool ) (Bingo_Http_Request::getPostRaw ( 'rich_text', 1 ));
			$arrParamsList['ie'] = trim(Bingo_Http_Request::getPostRaw ( 'ie', '' ));
	
			//��������
			$arrParamsList['anonymous'] = ( bool ) (Bingo_Http_Request::getPostRaw ( 'anonymous', 0 ));
			$arrParamsList['anonymous'] = ( bool ) (Tieba_Session_Socket::getNo_un () === '1') ? 1 : $arrParamsList['anonymous'];
			$arrParamsList['sign_id']   = Bingo_Http_Request::getPostRaw ( 'sign_id', '' );
			$arrParamsList['cid']   = Bingo_Http_Request::getPostRaw ( 'cid', 0 );
			$arrParamsList['call_from']   = Bingo_Http_Request::getPostRaw ( 'call_from', 'pc_bawu' );
			$arrParamsList['isBan']   = Bingo_Http_Request::getPostRaw ( 'isBan',0 );
	
			$arrParamsList['vcode']     = Bingo_Http_Request::getPostRaw ( 'vcode', '' );
			$arrParamsList['vcode_md5'] = Bingo_Http_Request::getPostRaw ( 'vcode_md5', '' );
			$arrParamsList['tforum']    = Bingo_Http_Request::getPostRaw ( 'tforum', '' );
			$arrParamsList['forum_id']        = Bingo_Http_Request::getPostRaw ( 'fid', 0 );
			$arrParamsList['thread_id']       = Bingo_Http_Request::getPostRaw ( 'tid', 0 );
			$arrParamsList['post_id']         = Bingo_Http_Request::getPostRaw ( 'pid',0 );
			$arrParamsList['floor_num'] = Bingo_Http_Request::getPostRaw ( 'floor_num', 0 );
			$arrParamsList['user_type'] = Bingo_Http_Request::getPostRaw ( 'user_type', '' );
	
			//���켣����
			$arrParamsList['mouse_pwd'] = Bingo_Http_Request::getPostRaw ( 'mouse_pwd', '' );
			$arrParamsList['mouse_pwd_t'] = Bingo_Http_Request::getPostRaw ( 'mouse_pwd_t', '' );
			$arrParamsList['mouse_pwd_isclick'] = Bingo_Http_Request::getPostRaw ( 'mouse_pwd_isclick', '' );
	
			//ֱ�����ͻ�������ʾ
			//lp_type
			//˵   �� : lpost_type: 1 ��ͨ����   2 ������
			//������: lpost_sub_type: 1 ֱ����  2 ���ֻ���  3 ͼƬ����
			$arrParamsList['lp_type']     = Bingo_Http_Request::getPostRaw ( 'lp_type', 0 );
			$arrParamsList['lp_sub_type'] = Bingo_Http_Request::getPostRaw ( 'lp_sub_type', 0 );
	
			$arrParamsList['tfrom'] = Bingo_Http_Request::getPostRaw ( 'tfrom', 0 );
			$arrParamsList['maxContentLen'] = Bingo_Http_Request::getPostRaw ( 'maxContentLen', '' );
			$arrParamsList['vcode_flag']  = Bingo_Http_Request::getPostRaw ( 'vcode_flag', '' );
			$arrParamsList['postContent'] = Bingo_Http_Request::getPostRaw ( 'postContent', '' );
	
			//��̸���� postid�ͷ�̸������
			$arrParamsList['repostid']   = Bingo_Http_Request::getPostRaw ( 'repostid', 0 );
			$arrParamsList['talk_type']  = Bingo_Http_Request::getPostRaw ( 'talk_type', '' );
	
	
			$arrParamsList['quote_id']   = Bingo_Http_Request::getPostRaw ( 'quote_id', 0 );
			$arrParamsList['qpost']      = Bingo_Http_Request::getPostRaw ( 'qpost', '' );
			$arrParamsList['tbs']= Bingo_Http_Request::getPostRaw ( 'tbs', '' );
	
			$strForumName = Bingo_Http_Request::getPostRaw ( 'kw', '' );
			$arrParamsList['forum_name'] = empty( $strForumName ) ? Bingo_Http_Request::getPostRaw ( 'word', '' ) : $strForumName ;
	
			#$arrParamsList['thread_type']= Bingo_Http_Request::getPostRaw ( 'thread_type', 0 );
			$arrParamsList['thread_type']= 0;
	
			$arrParamsList['open_id']= Bingo_Http_Request::getPostRaw ( '_open_id', 'tieba' );
			$arrParamsList['phone_type'] = Bingo_Http_Request::getPostRaw ( 'phone_type', '' );
			$arrParamsList['client_ip'] = Bingo_Http_Request::getPostRaw ( '_user_client_ip', 0 );
			$arrParamsList['product_client_type'] = Bingo_Http_Request::getPostRaw ( '_data_client_type', '' );
			$arrParamsList['_inner_type'] = Bingo_Http_Request::getPostRaw ( '_inner_type', '' );
			$arrParamsList['new_vcode'] = Bingo_Http_Request::getPostRaw ( 'new_vcode', 0 );
			$arrParamsList['files']     = Bingo_Http_Request::getPostRaw ( 'files', '' );
	
			$arrParamsList['vid_md5'] = Bingo_Http_Request::getPostRaw ( 'vid_md5', '' );
	
			$arrParamsList['cookiebduss']= Tieba_Session_Socket::getBDUSS(); 
			$arrParamsList['cookiebduid']= Tieba_Session_Socket::getBaiduid();
			
			
			$_anti_app = Bingo_Http_Request::getPostRaw ( 'anti_app', '' );
	 		if($_anti_app !== '') $arrParamsList['anti_app'] = $_anti_app;   
			
	
			//���ݲ���
			//lbs info 
			/* 
			$arrParamsList['lbs_info'] = array(
					'lon'  => Bingo_Http_Request::getPostRaw ( 'lon',''),
					'lat'  => Bingo_Http_Request::getPostRaw ( 'lat',''),
					'town' => Bingo_Http_Request::getPostRaw ( 'town',''),
					);
			*/
	
			// tag
			$arrParamsList['tag'] = Bingo_Http_Request::getPostRaw ( 'tag',0);
	
			//user_agent_sp 
			$strUserAgentSp = trim($_SERVER['HTTP_BAIDU_USER_AGENT']);	
			$arrParamsList['user_agent_sp'] = 0;
			
			//�ٶ�Ӱ�������
			$strUserAgent = trim($_SERVER['HTTP_USER_AGENT']);//ע�⣬����  BAIDU_USER_AGENT
			
			$arrParamsList['product_private_key'] = Bingo_Http_Request::getPostRaw ( 'product_private_key', 'tieba_pc' );
			
			//delete 
			$arrParamsList['commit_fr'] = Bingo_Http_Request::getPostRaw ( 'commit_fr', '' );
			$arrParamsList['is_finf']   = Bingo_Http_Request::getPostRaw ( 'is_finf', false );
			$arrParamsList['is_vipdel'] = Bingo_Http_Request::getPostRaw ( 'is_vipdel',0 );
			$arrParamsList['ie'] = Bingo_Http_Request::getPostRaw ( 'ie','utf-8' );
			$arrParamsList['is_tenyear_zuji'] = 1;
			
			$arrOutput = self::_callCommitService( $arrParamsList ,"post", "addThread" );
			
			self::_displayResult($arrOutput);	
			
        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            Bingo_Page::setTpl("index");
            //Ҳ������ת302
            //Tieba_Error::fetalError302();
            
        }
    }
    
    
   	protected function _callCommitService ($arrInput,$strServiceName,$strMethod ,$flag=0 ) {

		if ( $flag == 0 ){	 
			$arrInput = array('req' => $arrInput);
		}

		$arrOutput = array();
		if ( empty($arrInput) ){
			Bingo_Log::warning(sprintf('parameters is empty.'));			
			throw new Exception(Tieba_Errcode::ERR_PARAM_ERROR );
		}

		// ��ӡ��־ ��¼���ݸ�Service����
		Bingo_Log::debug(sprintf('talk to servicename:[%s] method:[%s] input:[%s] ',$strServiceName,$strMethod, serialize($arrInput) ) );

		Bingo_Timer::start($strServiceName);

        // mod by hjf begin trick
        if ($arrInput['req']['ie'] === 'utf-8' || $arrInput['req']['ie'] === 'utf8')
        {
            $ie = 'utf-8';
        } 
        else
        {
            $ie = 'gbk';
        }
        
        if ($allData['ie'] === 'utf-8' || $allData['ie'] === 'utf8')
        {
            $ie = 'utf-8';
        }
        $arrOutput = Tieba_Service::call($strServiceName, $strMethod, $arrInput,null ,'service_commit', 'post', 'php', $ie );

        $need_ret_ie = 'gbk'; //or utf-8

        if ( $arrOutput['ie'] === 'utf-8' && $need_ret_ie ==='gbk' )    
        {
            $arrOutput = Bingo_Encode::convert($arrOutput,Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
        }    
        if ( $arrOutput['ie'] === 'gbk' && $need_ret_ie ==='utf-8' )    
        {
            $arrOutput = Bingo_Encode::convert($arrOutput,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
        }    
        
//		$arrOutput = Tieba_Service::call($strServiceName, $strMethod, $arrInput);

        // mod by hjf end trick

        Bingo_Timer::end($strServiceName);

		if ($arrOutput === false ) {
			Bingo_Log::warning( sprintf('Failed to call servicename:[%s] method:[%s] input:[%s] ',$strServiceName,$strMethod,serialize($arrInput)));
			Tieba_Error::fetalError302();
			#throw new Exception( Tieba_Errcode::ERR_POST_CT_FAILED_TO_CALL_POSTCM);
		}

		//check err_no
		if ( isset($arrOutput['errno']) ) {
			if ( (intval($arrOutput['errno']) == Tieba_Errcode::ERR_SUCCESS) ){
				//succ noting to do 

			}else{
				Bingo_Log::warning(sprintf('Failed to call servicename:[%s] method:[%s] input:[%s] ',$strServiceName,$strMethod,serialize($arrOutput)));

			}
			return $arrOutput;
		} else {
			//failure
			Bingo_Log::warning(sprintf('Failed to call servicename:[%s] method:[%s] input:[%s] ',$strServiceName,$strMethod,serialize($arrInput),serialize($arrOutput)));
			Tieba_Error::fetalError302();
			#throw new Exception( Tieba_Errcode::ERR_UNKOWN);
		}		

	}
    

	protected static function _checkTbs(){
	
		$intActiveTime = 0;
		$strTbs = Bingo_Http_Request::getPostRaw("tbs", '');
		if (empty($strTbs)) {
			Bingo_Log::warning('access denied because of empty tbs!');
			throw new Exception(Tieba_Errcode::ERR_POST_CT_POWER_NOT_ENOUGH);
		}

		if (false === Tieba_Tbs::check($strTbs, Tieba_Session_Socket::isLogin(), $intActiveTime)) {
			Bingo_Log::warning('access denied because of invalid tbs!tbs=' . $strTbs);
			throw new Exception(Tieba_Errcode::ERR_POST_CT_POWER_NOT_ENOUGH);
		}
		return Tieba_Errcode::ERR_SUCCESS;
	}
	
	
	
	protected function _displayResult($arrResult,$strFlag=''){		

		if (isset($arrResult['res']) && !empty($arrResult['res'])){
			$arrSubResult = $arrResult['res'];
			$arrOutput = array(
					'autoMsg'  => trim ($arrSubResult['errmsg']),
					'fid'      => intval($arrSubResult['forum_id']),
					'fname'    => trim($arrSubResult['forum_name']),
					'tid'      => intval($arrSubResult['thread_id']),
					'is_login' => intval(Tieba_Session_Socket::isLogin()),
					'thread_id'=> intval($arrSubResult['thread_id']),
					'post_id'  => intval($arrSubResult['post_id']),
					'content'  => trim($arrSubResult['content']),
					);

			if (trim($strFlag) == 'add_vote'){
				$arrOutput['vid'] = trim($arrSubResult['vid']);
				$arrOutput['sign'] = trim($arrSubResult['sign']);
			}  

			$arrOutput['vcode'] = array(
					'need_vcode'  => intval($arrSubResult['need_vcode']),
					'str_reason'  => trim($arrSubResult['str_reason']),
					'captcha_vcode_str'  => trim($arrSubResult['captcha_vcode_str']),
					'captcha_code_type'  => intval($arrSubResult['captcha_code_type']),
					'userstatevcode'     => intval($arrSubResult['userstatevcode']),
					);

		}else{
			$arrOutput = array();
		}

		unset($arrOutput['thread_id']);
		unset($arrOutput['post_id']);

		//��ʱ������Ϊ����FEǰ�˴����ţ���serviceͨ��6λ�����ţ����4λ������
		$intErrno = ( intval($arrResult['errno']) == 0 )?0: intval( substr( trim(10000 + $arrResult['errno']) ,-4 ) ) ;
		$intErrno_new = ( intval($arrResult['errno']) == 0 )?0: intval( $arrResult['errno']) ;

	
		$arrRes = array(
				'no'          => $intErrno,
				'err_code'    => $intErrno_new,
				'error'       => "",
				'data'        => $arrOutput,
				);

		echo Bingo_String::array2json( $arrRes );

	}
	
	
}
?>