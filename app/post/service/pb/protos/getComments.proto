import "common.proto";

message GetCommentsReq { 
  optional uint64 thread_id = 1;
  optional uint64 post_id = 2;
  optional uint64 offset = 3;
  optional uint32 res_num = 4;
  optional uint32 status = 5;
  optional uint32 has_mask = 6;
  optional uint64 comment_id = 7;
  optional uint32 structured_content = 8;
  optional uint32 is_comm_reverse = 9;
  optional bytes uids = 10;
}

message GetCommentsRes { 
  optional uint64 errno = 1;
  optional string errmsg = 2;
  optional bytes output = 3;
}







