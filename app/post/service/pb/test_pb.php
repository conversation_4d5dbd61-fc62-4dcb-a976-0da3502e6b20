<?php
/***************************************************************************
 * i
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file test.php
 * <AUTHOR>
 * @date 2013/03/20 16:00:32
 * @brief 
 *  
 **/

//�����Զ�����
function __autoload($strClassName)
{
        require_once str_replace('_', '/', $strClassName) .'.php';
}

function test_getPostInfo()
{
    $arrInput = array(
         'post_ids' => array(
                  1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20
                   ),
     );
    $ret = Tieba_Service::call("post","getPostInfo",$arrInput);
    var_dump($ret);
}
function test_getPostInfoEx()
{
    $arrInput = array(
         'post_ids' => array(
                  1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20, 30919343935,13943759939
                   ),
     );
    $ret = Tieba_Service::call("post","getPostInfoEx",$arrInput);
    var_dump($ret);
}



function test_getPostsByThreadId()
{
    $arrInput = array(
         #'input' => array(
             "thread_id" => 2021590574,
             "offset" => 30,
             "res_num" => 30,
             "see_author" => 1,
             "has_comment" =>1
         #  ),
     );
    $ret = Tieba_Service::call("post","getPostsByThreadId",$arrInput);
    var_dump($ret);
}

function test_getSrchPostsByThreadId()
{
    $arrInput = array(
        #'input' => array(
            "thread_id" => 2021590574,
            "post_id" => 26893517324, 
            "res_num" => 30, 
            "see_author" => 1,
            "has_comment" =>1 
        #               ),  
                            );  
    $ret = Tieba_Service::call("post","getSrchPostsByThreadId",$arrInput);
    var_dump($ret);
}

function test_getReversePostsByThreadId()
{
    $arrInput = array(
        #'input' => array(
            "thread_id" => 2021590574,
            "post_id" => 126893517324, 
            "res_num" => 30, 
            "see_author" => 1,
            "reverse" =>0,
            "has_comment" =>1 
        #               ),  
                            );  
    $ret = Tieba_Service::call("post","getReversePostsByThreadId",$arrInput);
    var_dump($ret);
}

function test_getFullPostsByThreadId()
{
    $arrInput = array(
        #'input' => array(
            "thread_id" => 2021590574,
           # "post_id" => 26893517324, 
            "res_num" => 30, 
            "see_author" => 1,
            "reverse" =>0,
            "has_comment" =>1 
        #               ),  
                            );  
    $ret = Tieba_Service::call("post","getFullPostsByThreadId",$arrInput);
    var_dump($ret);
}


function test_getComments()
{
    $arrInput = array(
        #'input' => array(
            "thread_id" => 2091248536,
            "post_id" => 28063008306, 
            "res_num" => 5, 
            "offset" =>10,
            "status" => 2,
            "comment_id" => 28084617293,

        #               )  
                            );  
    $ret = Tieba_Service::call("post","getComments",$arrInput);
    var_dump($ret);
}

function test_getCommentList()
{
    $arrInput = array(
        #'input' => array(
            "thread_id" => 2091248536,
            "post_id" => 28063008306, 
            "res_num" => 5, 
            "offset" =>10,
            "status" => 2,
            "comment_id" => 28084617293,

        #               )  
                            );  
    $ret = Tieba_Service::call("post","getCommentList",$arrInput);
    var_dump($ret);
}

function test_getPostList()
{
    $arrInput = array(
        'thread_id' => 1,
        "offset"  => 0,
        "res_num" => 10000,
                            );  
    $ret = Tieba_Service::call("post","getPostList",$arrInput);
    var_dump($ret);
}

function test_checkPostInfo()
{
    $arrInput = array(
        'input' => array(
            0 => array(
            "thread_id" => 2091248536,
            "post_id" => 28063008306, 
                ),
            1 => array(
            "thread_id" => 2091248536,
            "post_id" => 280638306, 
                )
           )  
        );  
    $ret = Tieba_Service::call("post","checkPostInfo",$arrInput);
    var_dump($ret);
}

function test_getMaskInfo()
{
    $arrInput = array(
        'input' => array(
            "thread_ids" => array(1,2,3,4),
            "post_ids" => array(1,2,3,4),
           )  
        );  
    $ret = Tieba_Service::call("post","getMaskInfo",$arrInput);
    var_dump($ret);
}

function test_getMaxMinPostId()
{
    $arrInput = array(
        'input' => array(
            0 => array(
            "thread_id" => 2091248536,
            "post_id" => 28063008306, 
                ),
            1 => array(
            "thread_id" => 2091248536,
            "post_id" => 280638306, 
                )
           )  
        );  
    $ret = Tieba_Service::call("post","getMaxMinPostId",$arrInput);
    var_dump($ret);
}





//test_getPostInfo();
//test_getPostInfoEx();
test_getPostsByThreadId();
//test_getSrchPostsByThreadId();
//test_getReversePostsByThreadId();
//test_getFullPostsByThreadId();
//test_getComments();
//test_getCommentList();
//test_getPostList();
//test_checkPostInfo();
//test_getMaxMinPostId();
//test_getMaskInfo();


/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
