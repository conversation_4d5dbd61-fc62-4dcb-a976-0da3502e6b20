import "common.proto";


message GetUFrsReq { 
  optional string forum_name = 1;
  optional uint32 forum_id = 2;
  optional uint32 need_abstract = 3;
  optional uint32 offset = 4;
  optional uint32 res_num = 5;
  optional uint32 need_photo_pic = 6;
  optional uint32 icon_size = 7;
  optional string call_from = 8;
  optional string only_forum_num = 9;
  optional string user_data_tpl = 10;
  optional string need_media = 11;
  optional string usercenter = 12;
  optional string ka = 13;
  optional string local_call = 14;
  optional uint32 thread_info_num = 15;
  optional bytes uids = 16;
  optional uint64 user_id = 17;
}

message GetUFrsRes { 
  optional uint64 errno = 1;
  optional string errmsg = 2;
  optional bytes output = 3;
}





