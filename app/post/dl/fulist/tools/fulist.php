<?php
class Fulist {
	const DATABASE_FULIST_NAME = 'forum_fulist';
	const CACHE_FULIST_NAME = 'forum_fulist';
	const DB_TABLE_THREAD_LIST = 'thread_list';
	const DB_TABLE_FORUM_IDX = 'forum_idx';
	
	const FULIST_REMAIN_MONTHS = 1;
	const FULIST_BATCH_NUM = 1000;
	
	protected static $_db = null;
	
	private static function _getDB() {
		if (self::$_db) {
			return self::$_db;
		}
		
		Bingo_Timer::start ( 'fulistconn' );
		self::$_db = Tieba_Mysql::getDB (self::DATABASE_FULIST_NAME);
		Bingo_Timer::end ( 'fulistconn' );
		
		if (! self::$_db) {
			Bingo_Log::warning ( "get db resource fail." );
			self::$_db = null;
			return null;
		}
		self::$_db->query ( 'set names utf8' );
		return self::$_db;
	}
	private static function _queryDB($sql) {
		$db = self::_getDB ();
		if ($db == false) {
			return false;
		}
	
		Bingo_Timer::start ( "dbquery" );
		$ret = $db->query ( $sql );
		Bingo_Timer::end ( "dbquery" );
		return $ret;
	}

	private static function _initBingoLog() {
		require_once 'Bingo/Log.php';
		require_once 'Bingo/Timer.php';
		
		ob_start ();
		Bingo_Timer::start ( 'total' );
		
		$scriptPath = dirname ( __FILE__ );
		$rootPath = substr ( $scriptPath, 0, strrpos ( $scriptPath, 'app/post/dl/fulist/tools' ) );
		$moduleLogPath = $rootPath . 'log/post/';
		
		// 日志相关定义
		define ( 'LOG_SERVICE', 'SERVICE' );
		$logLevel = Bingo_Log::LOG_FATAL | Bingo_Log::LOG_WARNING | Bingo_Log::LOG_NOTICE;
		
		Bingo_Log::init ( array (
				LOG_SERVICE => array (
						'file' => $moduleLogPath . "/fulist_clear.log",
						'level' => $logLevel 
				) 
		), LOG_SERVICE );
		return true;
	}
	
	private static function _endProcess() {
		Bingo_Timer::end ( 'total' );
		
		Bingo_Timer::calculate ();
		$arrTimer = Bingo_Timer::getTimes ();
		foreach ( $arrTimer as $k => $v ) {
			Bingo_Log::pushNotice ( "TIMER_" . $k, $v );
		}
		Bingo_Log::buildNotice ();
	}
	
	public static function clearFuList($arrInput) {
		self::_initBingoLog ();
		
		if (!isset($arrInput['start']) || !isset($arrInput['end'])) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return false;
		}
		$start = intval($arrInput['start']);
		$end = intval($arrInput['end']);
		
		if (!($start > 0) || !($end > 0)  || !($end >= $start) ) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return false;
		}
		
		$thread_list = self::DB_TABLE_THREAD_LIST;
		$forum_idx = self::DB_TABLE_FORUM_IDX;
		$strtime = strtotime(self::FULIST_REMAIN_MONTHS . ' months ago');
		$limit = self::FULIST_BATCH_NUM;
		
		for ($forum_id =$start;$forum_id <= $end;$forum_id++){
			$continue = true;
		
			while ( $continue ) {
				// select
				$selectSql = "SELECT thread_id FROM $thread_list WHERE forum_id=$forum_id AND post_time < $strtime LIMIT 0,$limit;";
				Bingo_Timer::start ( "dbquery" );
				$retQuery = self::_queryDB ( $selectSql );
				Bingo_Timer::end ( "dbquery" );
		
				if ($retQuery === false) {
					Bingo_Log::warning ( 'query failed, retry.' );
					continue;
				}
		
				$retNum = count ( $retQuery );
				if ( $retNum<= 0) {
					$continue = false;
					break;
				}
		
				//delete
				foreach ($retQuery as $key =>$row){
					$thread_id = intval($row['thread_id']);
					$db = self::_getDB ();
					if (! $db) {
						Bingo_Log::warning ( 'query failed, pass.' );
						continue;
					}
					$db->startTransaction ();
					$strSql = "DELETE FROM $thread_list WHERE forum_id=$forum_id AND thread_id=$thread_id;";
		
					Bingo_Timer::start ( "thread_delete" );
					$retSql = $db->query ( $strSql );
					Bingo_Timer::end ( "thread_delete" );
					if ($retSql === false) {
						$intError = intval($db->errno ());
						Bingo_Log::warning ( "query db failed.errno:$intError,strSql:$strSql");
						$db->rollback ();
						continue;
					}
		
					$idxUpdateSql = "UPDATE $forum_idx SET cur_idx =cur_idx-1 WHERE forum_id = $forum_id and cur_idx>0;";
					Bingo_Timer::start ( "idx_update" );
					$retUpdate = $db->query ( $idxUpdateSql );
					Bingo_Timer::end ( "idx_update" );
					if ($retUpdate === false) {
						$intError = intval($db->errno ());
						Bingo_Log::warning ( "update forum_idx failed.errno:$intError,sql: $idxUpdateSql" );
						$db->rollback ();
						continue;
					}
					$db->commit ();
				}
			}
		}
		self::_endProcess ();
		
		return true;
	}
}

/* main process */
if (intval($_SERVER ['argc']) == 3) {
	$start = intval($_SERVER['argv'][1]);
	$end = intval($_SERVER['argv'][2]);
	$arrInput = array('start'=>$start,'end'=>$end);
	Fulist::clearFuList($arrInput);
} 


?>
