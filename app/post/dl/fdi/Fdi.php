<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013:10:16 15:15:24
 * @version 
 * @structs & methods(copied from idl.)
*/

//*************************************************************************************
// IDL CONTENT BEGIN: 
//*************************************************************************************
// 	#ifndef __FDI_IDL__
// 	#define __FDI_IDL__
// 	
// 	
// 	// tieba transfer cmd.
// 	struct trans_cmd {
// 		uint32_t	command_no;         		// 命令号
// 		uint32_t 	ip = optional();							// 请求用户IP
// 		string 		word = optional(); 		// 讨论区关键词 ,可能包含其他功能的词
// 		uint32_t 	forum_id = optional(); 					// 讨论区ID（0表示是新讨论区）
// 		uint32_t	thread_id = optional();  				// 线索编号	(0表示是一个新的线索)
// 		int64_t 	post_id = optional();                    // Post 编号     （0表示新提交Post或者修改thread）
// 		string		title = optional(); 		// 标题
// 		int32_t		pack_sum = optional();					// 成批发送的数据的个数
// 		uint32_t	anonymous_flag = optional();			// 发贴时用户点击匿名发贴，此位置1，否则置0 
// 		string 		username = optional();	// 用户名/屏蔽、登录、注册
// 		uint32_t	user_id = optional();					//用户ID
// 	 	uint32_t	is_vote = optional();    			    //是否是投票贴，1：是；0：否
// 		uint32_t	vote_id = optional();					//发贴时标记投票id
// 		string		op_uname = optional();
// 		int32_t		ftype = optional();  			//改贴中包含何种过滤词 
// 		int32_t		now_time = optional();			//此条数据的写盘时间
// 		uint64_t	trans_id = optional(); 			//该数据是有记载以来的第几条帖子。
// 		string		cookie_username = optional();	// cookie中的用户名，即发贴或管理员名字
// 		uint32_t	detail_len = optional();					// 变长内容（可能有其他功能）长度
// 		uint32_t 	op_uid = optional();
// 		uint32_t    use_zan_func = optional(); //是否启用赞功能
// 		binary 		rawdata = optional();
// 		
// 		uint32_t 	is_bakan = optional();   
// 		uint32_t 	is_protal = optional();
// 		uint32_t 	is_notitle = optional();  //用新版的frs页发贴
// 		uint32_t 	is_hastitle = optional(); //无标题发贴，用户自己选择输入标题
// 	    uint32_t    othertypes = optional(); //added by hanjunfang 主题类型扩展用，每个bit代表一个类型
// 	    uint32_t    from_thread_id = optional();
// 	
// 	    string image_urls[] = optional();
// 	    string video_urls[] = optional();
// 	    string music_urls[] = optional();
// 	    string face_urls[] = optional();
// 	
// 		string thread_ext_attr_name = optional();
// 		string thread_ext_attr_value = optional();
// 	
// 		uint64_t version = optional();
// 	};
// 	
// 	
// 	struct fdi_req_info
// 	{
// 		uint64_t thread_ids[];
// 		string abstract_type = optional();
// 	};
// 	
// 	//摘要的富文本返回信息定义
// 	struct abstract_media_idl
// 	{
// 		uint32_t media_type;
// 		string	 media_class;
// 		string   media_src;
// 		string   media_vsrc;
// 		string   media_vhsrc;
// 		string   media_vpic;
// 		string	 media_bdwater;
// 	};
// 	
// 	struct abstract_medias
// 	{
// 		abstract_media_idl image[] = optional();
// 		abstract_media_idl face[]  = optional();
// 		abstract_media_idl music[] = optional();
// 		abstract_media_idl embed[] = optional();
// 	};
// 	
// 	//摘要信息
// 	struct thread_abstract_idl 
// 	{
// 		uint32_t version = optional();
// 		string 	   abstract_info  = optional();	
// 		uint32_t   is_abstract_cut = optional();
// 		abstract_medias media  = optional();
// 	};
// 	
// 	struct thread_ext_info
// 	{
// 		string key;
// 		string value;
// 	};
// 	
// 	struct thread_info_idl 
// 	{
// 		// static
// 		uint32_t	forum_id;
// 		uint64_t	thread_id;
// 		string		title;
// 		uint32_t	user_id = optional();
// 		uint32_t	user_ip = optional();   
// 		string		title_prefix = optional();
// 		uint32_t	vote_id = optional();
// 		uint32_t	from_thread_id = optional();
// 	
// 		//dynamic
// 		uint32_t	post_num = optional();
// 		uint32_t	last_modified_time = optional();	
// 		uint32_t	last_user_id = optional();
// 		uint32_t	last_user_ip = optional();   
// 		uint32_t	last_post_deleted = optional();
// 		uint64_t	good_types = optional();
// 		uint64_t	top_types = optional();
// 		uint64_t	thread_types = optional(); // consist static and dyamic
// 		uint64_t	thread_classes = optional(); // consist static and dyamic
// 		uint64_t	last_post_id = optional();
// 	
// 	
// 		uint32_t is_deleted = optional();
// 	
// 		//--以上和之前的frsdi兼容，去除name部分的信息，包括forum name 和 user name
// 		//还有浏览计数
// 		
// 		//frsattr info
// 	    uint64_t first_post_id = optional();
// 		thread_abstract_idl post_abstract = optional();
// 	
// 		//扩展的属性信息
// 		thread_ext_info ext_info[] = optional();
// 	
// 		string post_content = optional();
// 	
// 		int32_t  err = optional();
// 	};
// 	
// 	service fdi
// 	{
// 		void query(fdi_req_info req, out thread_info_idl thread_infos[]);
// 	
// 		void itieba_commit(trans_cmd cmd);
// 	};
// 	
// 	
// 	#endif
// 	
//*************************************************************************************
// IDL CONTENT END. 
//*************************************************************************************


class Dl_Fdi_Fdi{

const SERVER_NAME = 'fdi';
const RAL_SERVICE_NAME = 'fdi';
public static $dl_ie = "utf-8";

public static function _call($arrInput,$strMethod,$intBalanceKey){
    $strTimer = 'ral_call_'.self::SERVER_NAME.'_'.$strMethod;
    Bingo_Timer::start($strTimer);
    //Bingo_Log::debug("ral call [method:{$strMethod}] [input".serialize($arrInput)."]");
	$out = camel(self::RAL_SERVICE_NAME,'query',$arrInput,$intBalanceKey);
    Bingo_Timer::end($strTimer);
    if (!$out || !is_array($out) || !isset($out['content'][0]['result_params'])){
        Bingo_Log::warning("ral call fdi error[method:{$strMethod}] [input".serialize($arrInput)."]");
        return false;
    }
    return $out['content'][0]['result_params'];
}
/**
 * @brief
 * @params: $arrInput:
 * 	fdi_req_info req
 * @return: $arrOutput:
 * 	thread_info_idl thread_infos[]
**/
public static function query($arrInput, $intBalanceKey = NULL){
	if(!$intBalanceKey){
		$intBalanceKey = rand();
	}
	$ralInput = Tbapi_Core_Util_Rpc::packMcpackRpcInput();
	$ralInput['content'][0]['method'] = 'query';
	$ralInput['content'][0]['service_name'] = self::SERVER_NAME;
	$ralInput['content'][0]['params'] = array(
		'req' => $arrInput['req'],
	);
	$arrOut = self::_call($ralInput,'query', $intBalanceKey);
	return $arrOut;
}
/**
 * @brief
 * @params: $arrInput:
 * 	fdi_req_info req
 * @return: $arrOutput:
 * 	thread_info_idl thread_infos[]
 **/
public static function get_content_abstract($arrInput, $intBalanceKey = NULL){
	if(!$intBalanceKey){
		$intBalanceKey = rand();
	}
	$ralInput = Tbapi_Core_Util_Rpc::packMcpackRpcInput();
	$ralInput['content'][0]['method'] = 'get_content_abstract';
	$ralInput['content'][0]['service_name'] = self::SERVER_NAME;
	$ralInput['content'][0]['params'] = array(
			'req' => $arrInput['req'],
	);
	$arrOut = self::_call($ralInput,'get_content_abstract', $intBalanceKey);
	return $arrOut;
}
/**
 * @brief
 * @params: $arrInput:
 * 	trans_cmd cmd
 * @return: $arrOutput:
**/
public static function itieba_commit($arrInput, $intBalanceKey = NULL){
	if(!$intBalanceKey){
		$intBalanceKey = rand();
	}
	$ralInput = Tbapi_Core_Util_Rpc::packMcpackRpcInput();
	$ralInput['content'][0]['method'] = 'itieba_commit';
	$ralInput['content'][0]['service_name'] = self::SERVER_NAME;
	$ralInput['content'][0]['params'] = array(
		'cmd' => $arrInput['cmd'],
	);
	$arrOut = self::_call($ralInput,'itieba_commit', $intBalanceKey);
	return $arrOut;
}
}
