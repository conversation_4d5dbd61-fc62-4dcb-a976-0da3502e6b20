{"page_id": "1769", "topic_name": "贴吧应用推荐", "product_type": "wise", "page_num": "2", "content": "<div class=\"main_body\">\n            \n            \n            <a href=\"http://rj.m.taobao.com/wap/appmark/outSideDownLoad.htm?key=cp_baidutieba\" class=\"wise_list_item\" editor-go-href=\"1\" editor-del-data=\"1\" editor-add-data=\"1\">\n                <img editor-image-data-src=\"1\" src=\"http://hiphotos.baidu.com/baidu/pic/item/e4dde71190ef76c6cfccef249f16fdfaaf5167a6.png\">\n                <div class=\"item_button\" editor-del-data=\"1\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://rj.m.taobao.com/wap/appmark/outSideDownLoad.htm?key=cp_baidutieba\">下载</div>\n                <div class=\"item_info\" editor-font-data=\"1\">淘宝手机助手</div>\n                <div class=\"item_info_text\" editor-font-data=\"1\">会赚钱的软件市场，下应用100%得集分宝</div>\n            </a>\n            \n</div>", "layout_name": "贴吧应用推荐", "create_user": "wangzhihong01", "create_time": "2013-12-06 15:57:33", "last_user": "wangzhihong01", "last_time": "2013-12-06 16:03:27", "before_content": "", "after_content": "", "publish_user": null, "publish_time": "0000-00-00 00:00:00", "page_pc_url": "", "page_title": "贴吧应用推荐", "page_css": "body{\n    background: #F8F8FF;\n    padding-right:10px;    \n}\n.main_body{\n    overflow: hidden;\n}\n.wise_list_item{\n    border-bottom: 1px solid #dddddd;\n    overflow: hidden;\n    display: block;\n    float: left;\n    width: 100%;\n    padding: 10px 0px;\n}\n.wise_list_item img{\n    width: 18%;\n    float: left;\n    margin-left: 15px;\n}\n.wise_list_item .item_info, .wise_list_item .item_info_text{\n    padding-left: 18%;\n    margin-left: 30px;\n    word-break: break-all;\n    word-wrap: break-word;\n}\n.wise_list_item .item_info{\n    color: #000000;\n    font-size: 16px;\n    line-height: 28px;\n}\n.wise_list_item .item_info_text{\n    color: #444444;\n    font-size: 12px;\n    line-height: 18px;\n}\n.wise_list_item .item_button{\n    float: right;\n    font-size: 16px;\n    padding: 5px 15px;\n    background:-webkit-gradient(linear, 0 30%, 0 100%, from(#ffffff), to(#dedede));\n    color: #009FCC;\n    border-radius: 3px;\n    margin: 15px 5px 10px 5px;\n    border: 1px solid #009FCC;\n}", "page_js": ""}