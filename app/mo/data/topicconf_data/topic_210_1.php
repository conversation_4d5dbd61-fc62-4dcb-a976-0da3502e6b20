{"page_id": "2398", "topic_name": "贴吧直播0111", "product_type": "wise", "page_num": "1", "content": "<div class=\"bodywrap\">\n     <div class=\"header\">\n         <div class=\"top_logo\">\n              <span editor-font-data=\"1\">1月11日</span>\n              <span class=\"right_corner\"></span>\n         </div>\n         <div class=\"bottom-font\">\n               <div class=\"head-title\" editor-font-data=\"1\">快乐大本营：郭涛一家+快男三强</div>\n               <div class=\"header-select\" editor-font-data=\"1\"></div>\n         </div>\n         <img editor-image-data-src=\"1\" src=\"http://tb1.bdstatic.com/tb/r/image/2014-01-11/78f4a9291be8ff6d456f8a4076c3bfc8.jpg\">\n     </div>\n     <div class=\"header_nav\">\n                <a class=\"j_button_join button-join button\" href=\"http://tieba.baidu.com/mo/q/topic_page/209_1\"><span class=\"join_friend\"></span> <span editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://tieba.baidu.com/mo/q/topic_page/209_1\">上期精彩</span></a>\n         <a class=\"j_button_share button-share button\" editor-wise-string=\"1\" href=\"#\"><span class=\"share_front\"></span><span editor-font-data=\"1\">分享好友</span></a>\n     </div>\n\n<div class=\"interview_user_info\" editor-del-data=\"1\">\n     <span class=\"hot_talk\" editor-font-data=\"1\">热议(23687)：</span>\n     <div class=\"interview_user_content\">\n     <ul class=\"interview_content\" editor-attr-keys-name=\"data-lis\" editor-attr-keys-text=\"编辑滚动文字，每行必须用竖线开头，需提交修改后刷新才生效\" data-lis=\"|这期有石头哇\n|哈哈！花花要来！！\n|这期的嘉宾我都好喜欢好喜欢。。。一定要看。\n|呀。。。必看必看。\n|真的有快男吗？\" style=\"margin-top: -40px; -webkit-transition: 0.8s linear; transition: 0.8s linear;\"><li>这期有石头哇\n</li><li>哈哈！花花要来！！\n</li><li>这期的嘉宾我都好喜欢好喜欢。。。一定要看。\n</li><li>呀。。。必看必看。\n</li><li>真的有快男吗？</li></ul>   \n     </div>  \n</div>\n\n     <div class=\"content_wrap\">\n         <div class=\"tagList \" editor-add-data=\"1\" editor-del-data=\"1\" editor-up-data=\"1\" editor-down-data=\"1\">\n             <a class=\"barName\">\n                 <span class=\"barNameSpan\">\n                    <span class=\"user_name_span\" editor-font-data=\"1\">热门综艺</span>\n                     <span class=\"time_count\"><span editor-font-data=\"1\" style=\"font-family:arial\">1月11日</span>\n                     </span>\n                     <span class=\"memberCount subBar\" editor-font-data=\"1\">1298</span>\n                 </span>\n                 </a><a class=\"author_pic pic\"><span editor-image-data-bg=\"1\" class=\"picOuter\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2013-12-14/a0a1fb706b0decf7957f7e4b549447ff.jpg);\"></span>\n                     <span class=\"pic_border_right\" style=\"display: block;\"></span>\n                 </a>\n             \n             <a class=\"j_titleWrap titleWrap  \" editor-go-href=\"1\" href=\"http://wapp.baidu.com/f?kz=2805931606&amp;jump_tieba_native=1\">\n                 <span class=\"title\" editor-image-data-bg=\"1\" editor-font-data=\"1\">【20:10】快乐大本营：郭涛一家+快男三强</span>\n  <span class=\"sub_pic\" editor-del-data=\"1\">\n       <span class=\"sub_span\"> <span class=\"sub_pic_left\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2014-01-10/d2bf39777ba88d0dd525e74f057ecfe5.jpg);\"></span></span>\n       <span class=\"sub_span\"> <span class=\"sub_pic_center\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2014-01-10/59958acfd3a7c1b07e35a17b1bbc4188.jpg);\"></span></span>\n       <span class=\"sub_span\"> <span class=\"sub_pic_right \" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2014-01-10/4a5297d649d22890fc33c9539a729252.jpg);\"></span></span>\n  </span>\n<span class=\"subtitle\" editor-font-data=\"1\">2014叔儿和石头妈的闯世界第一站—马栏山站！据说石头妈有爆料私底下的郭涛和石头哦！2013快乐男声全国三强华晨宇、欧豪、白举纲蜕变少年翩翩而来！卖萌帅酷招招必杀！往日雷照大公开比比谁更杀马特！</span>\n             </a>\n             <span class=\"authorList j_authorList\">\n                 <a class=\"picList j_picList\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=快乐大本营&amp;jump_tieba_native=1\">\n                     <span class=\"barOwner\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=快乐大本营&amp;jump_tieba_native=1\">快乐大本营吧</span><span class=\"picFont\"></span>\n                 </a>\n\n             </span>\n         </div><div class=\"tagList \" editor-add-data=\"1\" editor-del-data=\"1\" editor-up-data=\"1\" editor-down-data=\"1\">\n             <a class=\"barName\">\n                 <span class=\"barNameSpan\">\n                    <span class=\"user_name_span\" editor-font-data=\"1\">热门综艺</span>\n                     <span class=\"time_count\"><span editor-font-data=\"1\" style=\"font-family:arial\">1月11日</span>\n                     </span>\n                     <span class=\"memberCount subBar\" editor-font-data=\"1\">775</span>\n                 </span>\n                 </a><a class=\"author_pic pic\"><span editor-image-data-bg=\"1\" class=\"picOuter\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2013-12-14/a0a1fb706b0decf7957f7e4b549447ff.jpg);\"></span>\n                     <span class=\"pic_border_right\" style=\"display: block;\"></span>\n                 </a>\n             \n             <a class=\"j_titleWrap titleWrap  \" editor-go-href=\"1\" href=\"http://wapp.baidu.com/f?kz=2808429512&amp;jump_tieba_native=1\">\n                 <span class=\"title\" editor-image-data-bg=\"1\" editor-font-data=\"1\">【19:30】CCTV体坛风云人物年度评选颁奖盛典</span>\n  <span class=\"sub_pic\" editor-del-data=\"1\">\n       <span class=\"sub_span\"> <span class=\"sub_pic_left\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2014-01-11/28e8efdc81dc2a924d16f09b81a460bd.jpg);\"></span></span>\n       <span class=\"sub_span\"> <span class=\"sub_pic_center\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2014-01-11/20412ce1e5b7dfcfc9bb13ba3d4420a5.jpg);\"></span></span>\n       <span class=\"sub_span\"> <span class=\"sub_pic_right \" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2014-01-11/1563a2175a499d81c87afbc760987c9a.jpg);\"></span></span>\n  </span>\n<span class=\"subtitle\" editor-font-data=\"1\">　一年一度的体坛盛会再次到来，人们又有机会能看到体坛明星的另一面了，那些平日穿着运动服在赛场上奔跑的运动员们，将穿上最华丽的衣装从红毯上走过。今晚7点半体坛风云人物将用十一个奖项，给予这一年恰当的总结</span>\n             </a>\n             <span class=\"authorList j_authorList\">\n                 <a class=\"picList j_picList\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=广州fc&amp;jump_tieba_native=1\">\n                     <span class=\"barOwner\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=广州fc&amp;jump_tieba_native=1\">广州fc吧</span><span class=\"picFont\"></span>\n                 </a>\n\n             </span>\n         </div><div class=\"tagList \" editor-add-data=\"1\" editor-del-data=\"1\" editor-up-data=\"1\" editor-down-data=\"1\">\n             <a class=\"barName\">\n                 <span class=\"barNameSpan\">\n                    <span class=\"user_name_span\" editor-font-data=\"1\">NBA赛事</span>\n                     <span class=\"time_count\"><span editor-font-data=\"1\" style=\"font-family:arial\">1月11日</span>\n                     </span>\n                     <span class=\"memberCount subBar\" editor-font-data=\"1\">987</span>\n                 </span>\n                 </a><a class=\"author_pic pic\"><span editor-image-data-bg=\"1\" class=\"picOuter\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2013-12-14/a0a1fb706b0decf7957f7e4b549447ff.jpg);\"></span>\n                     <span class=\"pic_border_right\" style=\"display: block;\"></span>\n                 </a>\n             \n             <a class=\"j_titleWrap titleWrap  \" editor-go-href=\"1\" href=\"http://wapp.baidu.com/f?kz=2807031270&amp;jump_tieba_native=1\">\n                 <span class=\"title\" editor-image-data-bg=\"1\" editor-font-data=\"1\">【8:30】NBA:火箭VS老鹰</span>\n  <span class=\"sub_pic\" editor-del-data=\"1\">\n       <span class=\"sub_span\"> <span class=\"sub_pic_left\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2014-01-10/034d52a96c8afb88f4762f5abdc4aad4.jpg);\"></span></span>\n       <span class=\"sub_span\"> <span class=\"sub_pic_center\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2014-01-10/b469a5ab03ad693f6660b629a055b4fd.jpg);\"></span></span>\n       <span class=\"sub_span\"> </span>\n  </span>\n<span class=\"subtitle\" editor-font-data=\"1\">明天休斯顿火箭将迎来背靠背的首战，客场对阵亚特兰大老鹰。这是两队本赛季第二次交锋。首回合火箭在缺少詹姆斯-哈登的情况下，依然主场获胜。哈登会否延续近期的出色状态，火箭能否完成横扫？</span>\n             </a>\n             <span class=\"authorList j_authorList\">\n                 <a class=\"picList j_picList\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=火箭&amp;jump_tieba_native=1\">\n                     <span class=\"barOwner\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=火箭&amp;jump_tieba_native=1\">火箭吧</span><span class=\"picFont\"></span>\n                 </a>\n\n             </span>\n         </div><div class=\"tagList \" editor-add-data=\"1\" editor-del-data=\"1\" editor-up-data=\"1\" editor-down-data=\"1\">\n             <a class=\"barName\">\n                 <span class=\"barNameSpan\">\n                    <span class=\"user_name_span\" editor-font-data=\"1\">热门综艺</span>\n                     <span class=\"time_count\"><span editor-font-data=\"1\" style=\"font-family:arial\">1月11日</span>\n                     </span>\n                     <span class=\"memberCount subBar\" editor-font-data=\"1\">2013</span>\n                 </span>\n                 </a><a class=\"author_pic pic\"><span editor-image-data-bg=\"1\" class=\"picOuter\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2013-12-14/a0a1fb706b0decf7957f7e4b549447ff.jpg);\"></span>\n                     <span class=\"pic_border_right\" style=\"display: block;\"></span>\n                 </a>\n             \n             <a class=\"j_titleWrap titleWrap  \" editor-go-href=\"1\" href=\"http://wapp.baidu.com/f?kz=2807099703&amp;jump_tieba_native=1\">\n                 <span class=\"title\" editor-image-data-bg=\"1\" editor-font-data=\"1\">【11:30】NBA：湖人VS快船</span>\n  <span class=\"sub_pic\" editor-del-data=\"1\">\n       <span class=\"sub_span\"> <span class=\"sub_pic_left\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2014-01-10/ffe1427ec5a5f69f0af15887be1ae267.jpg);\"></span></span>\n       <span class=\"sub_span\"> <span class=\"sub_pic_center\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2014-01-10/57fbf2d1f69c0dff24179e4139ced2de.jpg);\"></span></span>\n       <span class=\"sub_span\"> </span>\n  </span>\n<span class=\"subtitle\" editor-font-data=\"1\">过去两个赛季洛城德比总是受人瞩目，可是这一次的德比战却大打折扣，因为两位最耀眼的超级明星科比-布莱恩特和克里斯-保罗都受伤缺席。三连败的湖人急需胜利反弹，而两连胜的快船可能迎来雷迪克复出，他们要在主场复仇湖人。</span>\n             </a>\n             <span class=\"authorList j_authorList\">\n                 <a class=\"picList j_picList\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=洛杉矶快船&amp;jump_tieba_native=1\">\n                     <span class=\"barOwner\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=洛杉矶快船&amp;jump_tieba_native=1\">洛杉矶快船吧</span><span class=\"picFont\"></span>\n                 </a>\n\n             </span>\n         </div><div class=\"tagList \" editor-add-data=\"1\" editor-del-data=\"1\" editor-up-data=\"1\" editor-down-data=\"1\">\n             <a class=\"barName\">\n                 <span class=\"barNameSpan\">\n                    <span class=\"user_name_span\" editor-font-data=\"1\">热门综艺</span>\n                     <span class=\"time_count\"><span editor-font-data=\"1\" style=\"font-family:arial\">1月11日</span>\n                     </span>\n                     <span class=\"memberCount subBar\" editor-font-data=\"1\">897</span>\n                 </span>\n                 </a><a class=\"author_pic pic\"><span editor-image-data-bg=\"1\" class=\"picOuter\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2013-12-14/a0a1fb706b0decf7957f7e4b549447ff.jpg);\"></span>\n                     <span class=\"pic_border_right\" style=\"display: block;\"></span>\n                 </a>\n             \n             <a class=\"j_titleWrap titleWrap  \" editor-go-href=\"1\" href=\"http://wapp.baidu.com/f?kz=2806446189&amp;jump_tieba_native=1\">\n                 <span class=\"title\" editor-image-data-bg=\"1\" editor-font-data=\"1\">【21:00】非诚勿扰：单亲妈妈迎专属</span>\n  <span class=\"sub_pic\" editor-del-data=\"1\">\n       <span class=\"sub_span\"> <span class=\"sub_pic_left\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2014-01-04/558984eb3643e2101cbda7b48faa5e66.jpg);\"></span></span>\n       <span class=\"sub_span\"> <span class=\"sub_pic_center\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2014-01-10/95e36af5659dc4b44a6d35ede5bd0e42.jpg);\"></span></span>\n       <span class=\"sub_span\"> <span class=\"sub_pic_right \" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2014-01-10/d37071415d4a2199edaa8d2eb2b5b903.jpg);\"></span></span>\n  </span>\n<span class=\"subtitle\" editor-font-data=\"1\">在本周六的节目当中，来自安徽的单亲妈妈吴琼，将迎来自己的一次专属心动，并为男嘉宾的一片痴情动心落泪。这个故事的结局将会是怎样的？让我们一起去寻找答案。</span>\n             </a>\n             <span class=\"authorList j_authorList\">\n                 <a class=\"picList j_picList\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=非诚勿扰&amp;jump_tieba_native=1\">\n                     <span class=\"barOwner\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=非诚勿扰&amp;jump_tieba_native=1\">非诚勿扰吧</span><span class=\"picFont\"></span>\n                 </a>\n\n             </span>\n         </div><div class=\"tagList \" editor-add-data=\"1\" editor-del-data=\"1\" editor-up-data=\"1\" editor-down-data=\"1\">\n             <a class=\"barName\">\n                 <span class=\"barNameSpan\">\n                    <span class=\"user_name_span\" editor-font-data=\"1\">英超赛事</span>\n                     <span class=\"time_count\"><span editor-font-data=\"1\" style=\"font-family:arial\">1月11日</span>\n                     </span>\n                     <span class=\"memberCount subBar\" editor-font-data=\"1\">1328</span>\n                 </span>\n                 </a><a class=\"author_pic pic\"><span editor-image-data-bg=\"1\" class=\"picOuter\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2013-12-14/a0a1fb706b0decf7957f7e4b549447ff.jpg);\"></span>\n                     <span class=\"pic_border_right\" style=\"display: block;\"></span>\n                 </a>\n             \n             <a class=\"j_titleWrap titleWrap  \" editor-go-href=\"1\" href=\"http://wapp.baidu.com/f?kz=2807136453&amp;jump_tieba_native=1\">\n                 <span class=\"title\" editor-image-data-bg=\"1\" editor-font-data=\"1\">【20:45】英超第21轮：胡尔城VS切尔西</span>\n  <span class=\"sub_pic\" editor-del-data=\"1\">\n       <span class=\"sub_span\"> <span class=\"sub_pic_left\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2014-01-10/a2434e3d5664d9092e8b0c645755de2c.jpg);\"></span></span>\n       <span class=\"sub_span\"> <span class=\"sub_pic_center\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2014-01-10/cf96fe592c1f346e454d3bae1e2696d4.jpg);\"></span></span>\n       <span class=\"sub_span\"> </span>\n  </span>\n<span class=\"subtitle\" editor-font-data=\"1\">双方历史上正式比赛中交锋41场，切尔西27胜10平4负。最近10次交锋切尔西7胜2平1负，进22球&nbsp;失7球。两队上一次交锋是在2013年8月18日斯坦福桥球场，切尔西主场2比0取胜胡尔城。</span>\n             </a>\n             <span class=\"authorList j_authorList\">\n                 <a class=\"picList j_picList\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=切尔西&amp;jump_tieba_native=1\">\n                     <span class=\"barOwner\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=切尔西&amp;jump_tieba_native=1\">切尔西吧</span><span class=\"picFont\"></span>\n                 </a>\n\n             </span>\n         </div><div class=\"tagList \" editor-add-data=\"1\" editor-del-data=\"1\" editor-up-data=\"1\" editor-down-data=\"1\">\n             <a class=\"barName\">\n                 <span class=\"barNameSpan\">\n                    <span class=\"user_name_span\" editor-font-data=\"1\">热贴盘点</span>\n                     <span class=\"time_count\"><span editor-font-data=\"1\" style=\"font-family:arial\">1月11日</span>\n                     </span>\n                     <span class=\"memberCount subBar\" editor-font-data=\"1\">2638</span>\n                 </span>\n                 </a><a class=\"author_pic pic\"><span editor-image-data-bg=\"1\" class=\"picOuter\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2013-12-14/a0a1fb706b0decf7957f7e4b549447ff.jpg);\"></span>\n                     <span class=\"pic_border_right\" style=\"display: block;\"></span>\n                 </a>\n             \n             <a class=\"j_titleWrap titleWrap  \" editor-go-href=\"1\" href=\"http://wapp.baidu.com/f?kz=2798417193&amp;jump_tieba_native=1\">\n                 <span class=\"title\" editor-image-data-bg=\"1\" editor-font-data=\"1\">【盘点】2014年即将播出的电视剧，哪些是你期待已久的？</span>\n  <span class=\"sub_pic\" editor-del-data=\"1\">\n       <span class=\"sub_span\"> <span class=\"sub_pic_left\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2014-01-11/db43ec4ba8eeafb435c09decc75a9fb5.jpg);\"></span></span>\n       <span class=\"sub_span\"> <span class=\"sub_pic_center\" editor-image-data-bg=\"1\" editor-del-data=\"1\" style=\"background-image:url(http://tb1.bdstatic.com/tb/r/image/2014-01-11/5fc1e3a7b08f4aa6cc1a0714e6c8a82b.jpg);\"></span></span>\n       <span class=\"sub_span\"> </span>\n  </span>\n<span class=\"subtitle\" editor-font-data=\"1\">大漠谣、步步惊情、爱情公寓4...一大波好看的电视剧即将霸占荧屏。2014要上映的剧集中，那部最让你期待？</span>\n             </a>\n             <span class=\"authorList j_authorList\">\n                 <a class=\"picList j_picList\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=娱乐圈&amp;jump_tieba_native=1\">\n                     <span class=\"barOwner\" editor-font-data=\"1\" editor-go-href=\"1\" href=\"http://wapp.baidu.com/mo/q?kw=娱乐圈&amp;jump_tieba_native=1\">娱乐圈吧</span><span class=\"picFont\"></span>\n                 </a>\n\n             </span>\n         </div>\n     </div>\n </div>\n<script>\n\n(function initScrollList(){\n\n   function createList(){\n     var list = $('.interview_user_content').find('ul:first-child');\n     list.attr('style', null);\n     var lis = list.attr('data-lis').split('|');\n     var str = '';\n     for(var i=0; i<lis.length; i++){\n       if(lis[i]){\n         str += '<li>' + lis[i].replace(/>/gi,'&gt;').replace(/</gi,'&lt;')+ '</li>';\n       }\n     }\n     list.html(str);\n   }\n   function autoScroll() {\n            var first_list = $('.interview_user_content').find('ul:first-child');\n            first_list.animate({\n                marginTop: \"-40px\"\n            }, 800, 'linear', function() {\n                first_list.css({\n                    marginTop: \"0px\"\n                }).find(\"li:first-child\").appendTo(first_list);\n            }); \n        };\n\n    createList();\n\n    window.setInterval(function() {\n        autoScroll();\n    }, 2000);\n\n})();\n\n\n$.track = function(opt){\n            opt = opt || {}; \n            var img = new Image();\n            var trackUrl = \"http://static.tieba.baidu.com/tb/img/track.gif?\";\n            var paramArr = [ \n                \"client_type=wap_smart\",\n                \"url=\" + encodeURIComponent(document.location.href),\n                \"refer=\" + encodeURIComponent(document.referrer),\n                \"uname=\" + encodeURIComponent(opt.uname || \"\"),\n                \"task=\" + (opt.task && encodeURIComponent(opt.task) || \"\"),\n                \"page=\" + (opt.page && encodeURIComponent(opt.page) || \"\"),\n                \"locate=\" + (opt.locate && encodeURIComponent(opt.locate)|| \"\"),\n                \"type=\" + (opt.type || \"click\"),\n                \"fname=\" + encodeURIComponent(opt.fname || \"\"),\n                \"fid=\" + (opt.fid || \"\"),\n                \"tid=\" + (opt.tid || \"\"),\n                \"pid=\" + (opt.pid || \"\"),\n                \"is_new_user=\" + (opt.isNewUser || \"\"),\n                \"_t=\" + (new Date()) * 1000\n            ];  \n            trackUrl += paramArr.join(\"&\");\n            img.src = trackUrl;\n            img = null;\n    };\n\n$.track({\n    task: '运营专题通用平台',\n    page: document.title,\n    locate: '主题墙',\n    type: 'view'\n});\n$('body').delegate('a', 'click', function(){\n  $.track({\n    task: '运营专题通用平台',\n    page: document.title,\n    locate: '主题墙',\n    type: 'click'\n  });\n});\n</script>", "layout_name": "主题墙样式", "create_user": "zhaodan05", "create_time": "2014-01-10 19:42:02", "last_user": "zhaodan05", "last_time": "2014-01-11 12:21:13", "before_content": "贴吧直播：郭涛、石头纯爷们父子大闹大本营", "after_content": "http://tb1.bdstatic.com/tb/r/image/2014-01-10/1b661c81efabbe34467a8186f4db1ffe.png", "publish_user": null, "publish_time": "0000-00-00 00:00:00", "page_pc_url": "", "page_title": "贴吧直播：郭涛、石头纯爷们父子大闹大本营", "page_css": "html,body{         background-image:-webkit-gradient(linear, left top, right top, color-stop(0.40, #cccccc), color-stop(0.5, #fdfdfd), color-stop(0.60, #fefefe));         -webkit-background-size: 2px 2px;         background-size: 2px 2px;     \n}  \nbody{\n  padding-bottom:10px;   \n  word-wrap: break-word;\n  word-break: break-all;\n}    .header{         display: block;         position: relative;     }     .header img{         width: 100%;         display: block;     }     .top_logo{         padding: 5px 12px 5px 12px;         font-size: 15px;         font-weight: bold;         color: #fff;         position: absolute;         background: #dd6240;         opacity: .8;         top: 50%; margin-top:-9px; left: 0;     }     .right_corner{         position: absolute;         right: -17px;         top: 0;         background: url(http://tb2.bdstatic.com/tb/mobile/syunying/img/title-right-conner_45b7dce5.png) right top no-repeat;         height: 28px;         display: block;         width: 21px;     }     .button{         display: inline-block;         background: -webkit-gradient(linear,left top,left bottom,from(#3f8be8),to(#2475d9));         height: 25px;         padding-right: 5px;         border-radius: 3px;         line-height: 25px;         vertical-align: middle;         margin-top: 7px;         margin-bottom: 8px;         color: #ffffff;         font-weight: bold;         text-align: center;         padding-left: 35px;     }     .button-share{         float: right;         margin-right: 5px;         position: relative;     }     .share_front{         position: absolute;         left:5px;         top:50%;         display: block;         width: 20px;         height: 20px;         margin-top: -10px;         background-size: 20px 20px;         background-position: center center;         background-repeat: no-repeat;         background-color: #000;         background-image:url(http://tieba.baidu.com/tb/r/image/2013-08-07/f88d435d0fc71a63a29dcabba81e27ae.png);     }     .button-join{         float: right;         margin-right: 5px;         position: relative;     }     .join_friend{         position: absolute;         left:5px;         top:50%;         display: block;         width: 20px;         height: 20px;         margin-top: -10px;         background-size: 20px 20px;         background-color: #000;         background-position: center center;         background-repeat: no-repeat;         background-image: url(http://tieba.baidu.com/tb/r/image/2013-08-07/3faccaed3dc5d4cf8bec9102c8fa67f7.png);     }     .header_nav{         height: 40px;         background: -webkit-gradient(linear,left top,left bottom,from(#f7f8f9),to(#e7e9ea));         box-shadow: 0 1px 3px 0 rgba(0,0,0,.3);     }     .head-title{         color:#fff;         font-size: 18px;         line-height: 35px;         padding-left: 10px;         font-weight: bold;     }     .header-select{         padding-left: 10px;         color: #dcdcdc;         padding-bottom: 10px;         font-size: 14px;     }     .bottom-font{         position: absolute;         bottom: 0px;         left: 0px;   background:#333;opacity:0.8; display:block;width:100%;  }    .tag_feed_list_item{        margin-left: 6px;        margin-right: 6px;        margin-top: 18px;        background: #fcfcfd;        border-radius: 5px;        border: 1px solid #bdbfc3;        border-left: 0px;        border-bottom: 0px;        position: relative;    }    .tagList{        margin-left:6px;        margin-right:6px;        margin-top:18px;        background:#fcfcfd;        border-radius:5px;        border:1px solid #bdbfc3;        border-left:0px;        border-bottom:0px;        position:relative;    }    .firstDivClass{        margin-top: 0px;    }    .tagView{        padding-left:16px;        height:32px;        -webkit-background-size: 14px 15px;        background-size: 14px 15px;        background-repeat:no-repeat;        background-position:left center;        position:absolute;        top:0px;        right:8px;        display:block;        font-size:12px;        font-weight:bold;        line-height:32px;        text-align:center;        color:#fefefe;        background-image:url(http://tieba.baidu.com/tb/cms/client/tagBack.png);    }    .tagViewInner{        display:inline-block;        padding-left:16px;        height:32px;        left:0px;        top:1px;        position:absolute;        color:#93a6b8;    }    .tagred{        background-image:url(http://tieba.baidu.com/tb/cms/client/red-top.png);    }    .tagyellow{        background-image:url(http://tieba.baidu.com/tb/cms/client/yellow-top.png);    }    .tagpurple{        background-image:url(http://tieba.baidu.com/tb/cms/client/purple-top.png);    }    .tagblue{        background-image:url(http://tieba.baidu.com/tb/cms/client/blue-top.png);    }    .taggree{        background-image:url(http://tieba.baidu.com/tb/cms/client/gree-top.png);    }    .barName{        display:block;        padding:8px;        padding-top:8px;        text-align:left;        padding-bottom:3px;        background: #fcfcfc;        border-top-right-radius:5px;        border-top-left-radius:5px;        position:relative;    }    .barNameSpan{        display:block;        color:#777777;        line-height:22px;        font-size:14px;        font-weight:normal;        height:26px;        padding-left:54px;    }    .time_count{        color:#b2b6ba;        font-size:10px;        line-height:18px;        display:block;        line-height:15px;    }    .subBar{        color:#93a6b8;        font-size:12px;        padding-left:16px;        line-height:20px;        -webkit-background-size: 14px 14px;        background-size: 14px 14px;        background-repeat:no-repeat;        background-position:left center;        margin-left:5px;        display:inline-block;        position:absolute;        right:8px;        top:5px;    }    .memberCount{        background-image:url(http://tieba.baidu.com/tb/cms/client/reply_icon_new.png);    }    .user_name_span{        display:block;        color:#666666;        font-size:12px;        line-height:15px;    }    .articleCount{        background-image:url(http://tieba.baidu.com/tb/cms/client/acticle.png);    }    .titleWrap{        padding:10px;        padding-top:0px;        display:block;        padding-bottom:12px;        background:#fcfcfd;    }    .title{        display:block;        line-height:30px;        color:#262626;        text-align:left;        font-size:16px;        font-weight:normal;        padding-top:10px;        line-height:20px;    }    .classTitle{        padding-top:10px;    }    .subtitle{        text-align:left;        color:#777;        font-size:14px;        display:block;        padding-bottom:0px;        padding-top:7px;    }    .titlePic{        display:block;    }    .titlePicMore{        display:-webkit-box;        margin-top:8px;    }    .titlePic img{        width:100%;    }    .titlePicBox{        display:block;        -webkit-box-flex:1;        width:100%;    }    .titlePicBoxInner{        display:block;        margin-right:5px;        max-height:240px;        overflow : hidden;        text-align:left;        line-height:100%;        font-size:0px;    }    .last_titlePicBoxInner{        display:block;        margin-right:0px;        max-height:240px;        overflow : hidden;        text-align:left;    }    .titlePicBoxInner img{        width:100%;    }    .authorList{        display:block;        height:32px;        margin-top:-2px;        border-top:1px solid #e7e9ec;        border-bottom-right-radius:5px;        border-bottom-left-radius:5px;        text-align:left;        padding-left:10px;        padding-right:10px;        position:relative;        background:#F0F2F4;        -webkit-box-shadow: 0px 2px 2px #bdbfc3;        box-shadow: 0px 2px 2px #bdbfc3;    }    .authorListConect{        display:block;        background:#fcfcfd;        height:37px;        border-top:1px solid #f2f2f7;        margin-top:-34px;        text-align:left;        padding-left:10px;        padding-right:10px;        position:relative;        border-top:0px;    }    .picFont{        color:#93a6b8;        font-size:12px;        display:inline-block;        line-height:32px;        font-weight:bold;        margin-left:10px;        text-shadow:0px 1px 1px #fff;    }    .picFontPoint{        line-height:30px;    }    .pic{        display:inline-block;        width:50px;        height:50px;;        vertical-align:middle;        -webkit-background-size: 50px 50px;        background-size: 50px 50px;        position:absolute;        left:-1px;        top:-12px;    }    .picOuter{        display:block;        margin:4px;        -webkit-background-size: 46px 46px;        background-size: 46px 46px;        height:42px;        -webkit-box-shadow: 1px -1px 2px #fff;        box-shadow: 1px -1px 2px #fff;    }    .pic_border_right{        width:50px;        height:50px;        position:absolute;        left:0px;        top:0px;        display:block;        -webkit-background-size: 50px 50px;        background-size: 50px 50px;        background-image:url(http://tieba.baidu.com/tb/cms/client/first_page_outer.png);    }    .picFirst{        margin-left:0px;    }    .barOwner{        color:#93a6b8;        font-size:12px;        display:inline-block;        font-weight:bold;        text-shadow:0px 1px 1px #fff;        padding-left:18px;        line-height:32px;        -webkit-background-size: 14px 14px;        background-size: 14px 14px;        background-position:left center;        background-repeat:no-repeat;        background-image:url(http://tieba.baidu.com/tb/cms/client/bar_icon.png);        text-shadow:0px 1px 1px #fff;    }\n.sub_pic{\n  display: -webkit-box;\n  padding-top:5px;\n}\n.sub_span{\n  display:block;\n  -webkit-box-flex:1;\n  height:90px;\n  overflow:hidden;\n  text-align:left;\n  \n  \n}\n.sub_pic_left{\n background-size:cover;\n display:block;\n height:90px;\n margin-right:5px;\n background-image:url(http://imgstatic.baidu.com/img/image/shouye/xiaohuashouyelunbo0808.jpg);\n}\n.sub_pic_center{\n  background-size:cover;\n   display:block;\n height:90px;\n margin-right:5px;\n   background-image:url(http://imgstatic.baidu.com/img/image/shouye/tongliya0808.jpg);\n}\n.sub_pic_right{\n background-size:cover;\n display:block;\n height:90px;\n background-image:url(http://imgstatic.baidu.com/img/image/shouye/huoyingrenzhe0808.jpg);\n}\n\n\n.interview_user_info{\nborder: 1px solid #d2d2d2;\nheight: 40px;\nline-height: 40px;\npadding: 0 10px;\ncolor: #666;\nbackground-color: #f5f6f7;\noverflow: hidden;\n}\n.hot_talk {\nfloat: left;\ncolor: #1665be;\n}\n\n.interview_content{\n  margin-left: 104px;\n}\n.interview_user_info li{\n  height: 40px;\n}", "page_js": ""}