{"page_id": "1502", "topic_name": "紫色嗷嗷嗷", "product_type": "pc", "page_num": "1", "content": "<div class=\"page_body_panel edit_page_body_panel\" edit-block-name=\"页面背景\" edit-block-type=\"页面\" edit-block-datas=\"[]\" style=\"background-color: rgba(0, 0, 0, 0);\"><style class=\"edit-css-handle\" plugin_class_name=\"puzzle03\">.puzzle03{\n    width:960px;\n}\n\n.puzzle03 .content{\n    width: 960px;\n    height:345px;\n    position:relative;\n}\n\n*+html .puzzle03 .content{\n    height:340px;\n}\n\n*html .puzzle03 .content{\n    height:340px;\n}\n\n.puzzle03 .content .big_img{\n    width:330px;\n    height:320px;\n    overflow: hidden;\n    position:absolute;\n    top:10px;\n    left:10px;\n}\n\n.puzzle03 .content .middle_img{\n    height:320px;\n    width:245px;\n    position:absolute;\n    top:10px;\n    left:348px;\n    overflow:hidden;\n}\n\n.puzzle03 .content .middle_img img{\n    width:100%;\n    height:167px;\n    margin-bottom:5px;\n}\n\n.puzzle03 .content .little_big_img{\n    height:320px;\n    width:175px;\n    overflow:hidden;\n    position:absolute;\n    top:10px;\n    left:603px;\n}\n\n.puzzle03 .content .little_img{\n    height:320px;\n    width:160px;\n    position:absolute;\n    top:10px;\n    right:10px;\n    overflow: hidden;\n}\n\n.puzzle03 .content .little_img img{\n    width:160px;\n    height:157px;\n    margin-bottom:5px;\n}</style><style class=\"edit-css-handle\" plugin_class_name=\"puzzle01\">.puzzle01{\n    width:960px;\n}\n\n.puzzle01 .content{\n    width:100%;\n    height:396px;\n}\n\n*html .puzzle01 .content{\n    height:396px;\n}\n\n.puzzle01 .content ul{\n    list-style-type: none;\n    width:100%;\n    height:100%;\n    font-family:STHeiti,'微软雅黑','Microsoft Yahei',Arial,sans-serif;\n}\n.puzzle01 .content ul li{\n    float:left;\n    margin-left:14px;\n    margin-top:10px;\n}\n\n*html .puzzle01 .content ul li{\n    margin-left:10px;\n    margin-top:10px;\n}\n\n.puzzle01 .content ul li img{\n    width:229px;\n    height:180px;\n    float:left;\n}\n.puzzle01 .content ul li a{\n    text-decoration:none;\n}\n.puzzle01 .content ul li div{\n    width:229px;\n    height:180px;\n    float:left;\n    background:#5E66C4;\n    cursor:pointer;\n}\n\n.puzzle01 .content ul li div .title{\n    font-size:1.25em;\n    color:#FFF;\n    margin-top:40px;\n    margin-left:20px;\n    display:inline-block;\n}\n.puzzle01 .content ul li div .word{\n    display:inline-block;\n    margin-left:20px;\n    margin-top:5px;\n    color:#FFF;\n    width:185px;\n    line-height:25px;\n}</style>\n    <div class=\"page_content_header\" edit-block-name=\"顶部背景\" edit-block-type=\"布局\">\n         <div class=\"main_header\" edit-block-name=\"顶部\" edit-block-type=\"布局\"><a href=\"http://tieba.baidu.com\" target=\"_blank\" class=\"logo\"></a>\n\n<div id=\"bdshare\" class=\"edit_block_share1 edit_block edit_plugin bdshare_t bds_tools get-codes-bdshare\" edit-block-name=\"分享组件_风格1\" edit-block-type=\"分享组件_风格1模块\" edit-plugin-class-name=\"share1\" edit-block-data-keys=\"text,pic,url\" edit-block-data-key-texts=\"文案,图片,链接\" data=\"{'text':'贴吧十周年宝贝魅力征集！2013年12月3日，贴吧即将迎来10周年生日！！&amp;#160;贴吧作为最大的中文兴趣社区，10年来创建了超过800万个兴趣吧，集结了千万同好吧友！&amp;#160;有兴趣爱好的妹子都是好姑娘，贴吧的妹子们，集青春、美丽、才华、有爱于一身。她们有着自己的个性与主见，她们或是手工达人、或是cosplay萌妹，又或者她们是终极粉丝、小说作家、声音达人，更有一呼百应的资深吧主。&amp;#160;“十周年宝贝”魅力征集，将从众多贴吧的优秀妹子中，甄选出气质/形象最佳的5位，为贴吧10周年代言！','pic':'http://tb1.bdstatic.com/tb/r/image/2013-11-16/2fba22cd4bc179afeedfe40ab6671764.png','url':'http://tieba.baidu.com/mo/q/topic_page/145_1'}\" edit-plugin-auto-id=\"id_1384333672987\" edit-block-datas=\"[{&quot;text&quot;:&quot;贴吧十周年宝贝魅力征集！2013年12月3日，贴吧即将迎来10周年生日！！&amp;#160;贴吧作为最大的中文兴趣社区，10年来创建了超过800万个兴趣吧，集结了千万同好吧友！&amp;#160;有兴趣爱好的妹子都是好姑娘，贴吧的妹子们，集青春、美丽、才华、有爱于一身。她们有着自己的个性与主见，她们或是手工达人、或是cosplay萌妹，又或者她们是终极粉丝、小说作家、声音达人，更有一呼百应的资深吧主。&amp;#160;“十周年宝贝”魅力征集，将从众多贴吧的优秀妹子中，甄选出气质/形象最佳的5位，为贴吧10周年代言！&quot;,&quot;pic&quot;:&quot;http://tb1.bdstatic.com/tb/r/image/2013-11-16/2fba22cd4bc179afeedfe40ab6671764.png&quot;,&quot;url&quot;:&quot;http://tieba.baidu.com/mo/q/topic_page/145_1&quot;}]\" style=\"left: 786px; top: 2px;\">\n\n<span class=\"bds_more\"></span>\n<a class=\"bds_qzone\" title=\"分享到QQ空间\" href=\"#\"></a>\n<a class=\"bds_tsina\" title=\"分享到新浪微博\" href=\"#\"></a>\n<a class=\"bds_tqq\" title=\"分享到腾讯微博\" href=\"#\"></a>\n<a class=\"bds_renren\" title=\"分享到人人网\" href=\"#\"></a>\n<a class=\"bds_t163\" title=\"分享到网易微博\" href=\"#\"></a>\n<a class=\"shareCount\" href=\"#\" title=\"累计分享0次\">0</a>\n\n</div>\n\n         </div>\n    </div>\n    <div class=\"page_header_image_bg\" edit-block-name=\"头图背景\" edit-block-type=\"布局\" style=\"height: 680px;background:rgb(0, 102, 119) url(http://tieba.baidu.com/tb/zt/greenxingdong/images/ls_logo.jpg) no-repeat no-repeat 100% 0%;\">\n         <div class=\"page_header_image\" edit-block-name=\"头图\" edit-block-type=\"布局\" style=\"height: 660px;\">\n         </div>\n    </div>\n    <div class=\"page_content_body_bg clearfix\" edit-block-name=\"内容背景\" edit-block-type=\"布局\" style=\"background-color: rgba(0, 0, 0, 0);\">\n        <div class=\"page_content_body edit_append_layout_panel edit_default_insert_layout_panel clearfix\" edit-block-name=\"内容\" edit-block-type=\"布局\" style=\"height: 880px;\">\n        <div class=\"puzzle01 edit_block_puzzle01  edit_block edit_plugin\" edit-block-name=\"拼图_01\" edit-block-type=\"拼图_01模块\" edit-plugin-class-name=\"puzzle01\" edit-block-data-keys=\"bigTitle,big_link,title,src,image_link,word,word_link\" edit-block-data-key-texts=\"大标题,大标题链接,文字标题,图片地址,图片链接,文字,文字链接\" edit-plugin-auto-id=\"id_1384507226843\" edit-block-datas=\"[{&quot;bigTitle&quot;:&quot;10周年宝贝专访|宝贝说&quot;,&quot;big_link&quot;:&quot;http://tieba.baidu.com/f?kw=%CC%F9%B0%C910%D6%DC%C4%EA&amp;amp;tp=0&quot;,&quot;title&quot;:&quot;第1期:千梦少年|女汉子到女神&quot;,&quot;src&quot;:&quot;http://tb1.bdstatic.com/tb/r/image/2013-11-15/e08125d675ab4db7f3ddc9d91ad22c51.png&quot;,&quot;image_link&quot;:&quot;http://tieba.baidu.com/p/2703408241&quot;,&quot;word&quot;:&quot;我希望在我老去的时候会有三本书：一本游记，一本岁月，一本摄影集。如果你也有梦想，请你坚持下去&amp;#160;！【详细】&quot;,&quot;word_link&quot;:&quot;http://tieba.baidu.com/p/2703408241&quot;},{&quot;bigTitle&quot;:&quot;不填&quot;,&quot;big_link&quot;:&quot;&quot;,&quot;title&quot;:&quot;第2期:周若雪|为爱远走丹麦&quot;,&quot;src&quot;:&quot;http://tb1.bdstatic.com/tb/r/image/2013-11-15/3e0d2b0697f4ddfe32ef9323e0dca14e.png&quot;,&quot;image_link&quot;:&quot;http://tieba.baidu.com/p/2706500160&quot;,&quot;word&quot;:&quot;爱旅行，爱搭配，沿途的风景记载了她在路上的美丽身影；喜欢用微笑对待生活，靠自己的努力实现愿望。【详细】&quot;,&quot;word_link&quot;:&quot;http://tieba.baidu.com/p/2706500160&quot;},{&quot;bigTitle&quot;:&quot;不填&quot;,&quot;big_link&quot;:&quot;&quot;,&quot;title&quot;:&quot;第3期:颜玖|有梦想的coser&quot;,&quot;src&quot;:&quot;http://tb1.bdstatic.com/tb/r/image/2013-11-15/ca56b8f2e76ae4da997ae1a66ccfb4c8.png&quot;,&quot;image_link&quot;:&quot;http://tieba.baidu.com/p/2707635185&quot;,&quot;word&quot;:&quot;喜欢唱歌画画写作cos的二次元萌妹，梦想成为一个漫画家，她想证明好动漫也可以中国造。【详细】&quot;,&quot;word_link&quot;:&quot;http://tieba.baidu.com/p/2707635185&quot;},{&quot;bigTitle&quot;:&quot;不填&quot;,&quot;big_link&quot;:&quot;&quot;,&quot;title&quot;:&quot;预告：动漫吧美女吧主菥似荩&quot;,&quot;src&quot;:&quot;http://tb1.bdstatic.com/tb/r/image/2013-11-15/5ed8b1208ddb64e7ab014af89c68aa75.png&quot;,&quot;image_link&quot;:&quot;http://tieba.baidu.com/p/2706538338&quot;,&quot;word&quot;:&quot;来自动漫吧，2005年成为动漫吧吧主，吧里的人一批又一批，现在她依然在这里【详细】&quot;,&quot;word_link&quot;:&quot;http://tieba.baidu.com/p/2706538338&quot;}]\" style=\"top: 0px; left: 1px;\">\n        <p class=\"header_up\">\n            <a class=\"header_color\" target=\"_blank\" href=\"http://tieba.baidu.com/f?kw=%CC%F9%B0%C910%D6%DC%C4%EA&amp;amp;tp=0\">10周年宝贝专访|宝贝说</a>\n        </p>\n        <span class=\"header_down header_background\"></span>\n        <div class=\"content content_background content_color\">\n            <ul><li><a href=\"http://tieba.baidu.com/p/2703408241\" target=\"_blank\"><img src=\"http://tb1.bdstatic.com/tb/r/image/2013-11-15/e08125d675ab4db7f3ddc9d91ad22c51.png\"></a><a href=\"http://tieba.baidu.com/p/2703408241\" target=\"_blank\"><div><span class=\"title\">第1期:千梦少年|女汉子到女神</span><span class=\"word\">我希望在我老去的时候会有三本书：一本游记，一本岁月，一本摄影集。如果你也有梦想，请你坚持下去&nbsp;！【详细】</span></div></a></li><li><a href=\"http://tieba.baidu.com/p/2706500160\" target=\"_blank\"><img src=\"http://tb1.bdstatic.com/tb/r/image/2013-11-15/3e0d2b0697f4ddfe32ef9323e0dca14e.png\"></a><a href=\"http://tieba.baidu.com/p/2706500160\" target=\"_blank\"><div><span class=\"title\">第2期:周若雪|为爱远走丹麦</span><span class=\"word\">爱旅行，爱搭配，沿途的风景记载了她在路上的美丽身影；喜欢用微笑对待生活，靠自己的努力实现愿望。【详细】</span></div></a></li><li><a href=\"http://tieba.baidu.com/p/2707635185\" target=\"_blank\"><img src=\"http://tb1.bdstatic.com/tb/r/image/2013-11-15/ca56b8f2e76ae4da997ae1a66ccfb4c8.png\"></a><a href=\"http://tieba.baidu.com/p/2707635185\" target=\"_blank\"><div><span class=\"title\">第3期:颜玖|有梦想的coser</span><span class=\"word\">喜欢唱歌画画写作cos的二次元萌妹，梦想成为一个漫画家，她想证明好动漫也可以中国造。【详细】</span></div></a></li><li><a href=\"http://tieba.baidu.com/p/2706538338\" target=\"_blank\"><img src=\"http://tb1.bdstatic.com/tb/r/image/2013-11-15/5ed8b1208ddb64e7ab014af89c68aa75.png\"></a><a href=\"http://tieba.baidu.com/p/2706538338\" target=\"_blank\"><div><span class=\"title\">预告：动漫吧美女吧主菥似荩</span><span class=\"word\">来自动漫吧，2005年成为动漫吧吧主，吧里的人一批又一批，现在她依然在这里【详细】</span></div></a></li></ul>\n        </div>\n    </div><div class=\"puzzle03 edit_block_puzzle03  edit_block edit_plugin\" edit-block-name=\"拼图_03\" edit-block-type=\"拼图_03模块\" edit-plugin-class-name=\"puzzle03\" edit-block-data-keys=\"title,src,link\" edit-block-data-key-texts=\"标题,图片路径,图片链接\" edit-plugin-auto-id=\"id_1384519048003\" edit-block-datas=\"[{&quot;title&quot;:&quot;更多10周年宝贝&quot;,&quot;src&quot;:&quot;http://tb1.bdstatic.com/tb/r/image/2013-11-15/b9bb0da88a2a95ae5613e0b61a0764cf.png&quot;,&quot;link&quot;:&quot;http://tieba.baidu.com/p/2705693275&quot;},{&quot;title&quot;:&quot;标题标题标题&quot;,&quot;src&quot;:&quot;http://tb1.bdstatic.com/tb/r/image/2013-11-15/5c430cbf5c03d33164089cc5c0ff7677.png&quot;,&quot;link&quot;:&quot;http://tieba.baidu.com/p/2706376584&quot;},{&quot;title&quot;:&quot;标题标题标题&quot;,&quot;src&quot;:&quot;http://tb1.bdstatic.com/tb/r/image/2013-11-15/c339a562fbd22b184f48a6b5e2c9aaf0.png&quot;,&quot;link&quot;:&quot;http://tieba.baidu.com/p/2704956039&quot;},{&quot;title&quot;:&quot;标题标题标题&quot;,&quot;src&quot;:&quot;http://tb1.bdstatic.com/tb/r/image/2013-11-16/93dbb82d422d20b93266b2c3278800b3.png&quot;,&quot;link&quot;:&quot;http://tieba.baidu.com/p/2693062459&quot;},{&quot;title&quot;:&quot;标题标题标题&quot;,&quot;src&quot;:&quot;http://tb1.bdstatic.com/tb/r/image/2013-11-15/d59bfc731e65e2ab0e27f606453a58cd.png&quot;,&quot;link&quot;:&quot;http://tieba.baidu.com/p/2693549833&quot;},{&quot;title&quot;:&quot;标题标题标题&quot;,&quot;src&quot;:&quot;http://tb1.bdstatic.com/tb/r/image/2013-11-15/5b56c1598bb403981ffe89319284925b.png&quot;,&quot;link&quot;:&quot;http://tieba.baidu.com/p/2691206951&quot;}]\" style=\"top: 457px; left: 0px;\">\n        <p class=\"header_up\">\n            <span class=\"header_color\">更多10周年宝贝</span>\n        </p>\n        <span class=\"header_down header_background\"></span>\n        <div class=\"content content_background content_color\">\n            <div class=\"big_img\"><a href=\"http://tieba.baidu.com/p/2705693275\" target=\"_blank\"><img src=\"http://tb1.bdstatic.com/tb/r/image/2013-11-15/b9bb0da88a2a95ae5613e0b61a0764cf.png\"></a></div>\n            <div class=\"middle_img\"><a href=\"http://tieba.baidu.com/p/2706376584\" target=\"_blank\"><img src=\"http://tb1.bdstatic.com/tb/r/image/2013-11-15/5c430cbf5c03d33164089cc5c0ff7677.png\"></a><a href=\"http://tieba.baidu.com/p/2704956039\" target=\"_blank\"><img src=\"http://tb1.bdstatic.com/tb/r/image/2013-11-15/c339a562fbd22b184f48a6b5e2c9aaf0.png\"></a></div>\n            <div class=\"little_big_img\"><a href=\"http://tieba.baidu.com/p/2693062459\" target=\"_blank\"><img src=\"http://tb1.bdstatic.com/tb/r/image/2013-11-16/93dbb82d422d20b93266b2c3278800b3.png\"></a></div>\n            <div class=\"little_img\"><a href=\"http://tieba.baidu.com/p/2693549833\" target=\"_blank\"><img src=\"http://tb1.bdstatic.com/tb/r/image/2013-11-15/d59bfc731e65e2ab0e27f606453a58cd.png\"></a><a href=\"http://tieba.baidu.com/p/2691206951\" target=\"_blank\"><img src=\"http://tb1.bdstatic.com/tb/r/image/2013-11-15/5b56c1598bb403981ffe89319284925b.png\"></a></div>\n        </div>\n    </div><a class=\"edit_block\" href=\"http://tieba.baidu.com/p/2692427399\" edit-block-name=\"普通无字链接\" edit-block-type=\"链接\" style=\"width: 124px; height: 122px; text-decoration: none; left: 690px; top: -149px;\" target=\"_blank\"></a><a class=\"edit_block\" href=\"http://tieba.baidu.com/p/2704701841\" edit-block-name=\"普通无字链接\" edit-block-type=\"链接\" style=\"width: 121px; height: 123px; text-decoration: none; left: 836px; top: -150px;\" target=\"_blank\"></a><a class=\"edit_block\" href=\"http://tieba.baidu.com/f?kw=%CC%F9%B0%C910%D6%DC%C4%EA&amp;tp=0\" edit-block-name=\"普通无字链接\" edit-block-type=\"链接\" style=\"width: 150px; height: 50px; text-decoration: none; left: 1px; top: 455px;\" target=\"_blank\"></a></div>\n    </div>\n    <div class=\"page_content_footer\" edit-block-name=\"尾部\" edit-block-type=\"布局\">\n     <span class=\"copy_right\">&copy;2013 Baidu</span> <a href=\"http://static.tieba.baidu.com/tb/eula.html\" target=\"_blank\">贴吧协议</a><span>|</span><a href=\"http://tieba.baidu.com/tb/system.html\" target=\"_blank\">吧主制度</a><span>|</span><a href=\"http://tieba.baidu.com/f?ie=utf-8&amp;kw=%E6%84%8F%E8%A7%81%E5%8F%8D%E9%A6%88%E5%90%A7\" target=\"_blank\">意见反馈</a><span>|</span><a href=\"http://static.tieba.baidu.com/tb/zt/declare/\" target=\"_blank\">收费删贴警示</a>\n    </div>\n\n<script plugin_class_name=\"share1\" class=\"edit-js-handle\">(function($){\n            $.BlockPlugins.share1= function(){};\n            $.BlockPlugins.share1.prototype = {\n                $panel: null,\n                default_datas: [{\"text\":\"\",\"pic\":\"\",\"url\":\"\"}],\n                datas: null,\n                scripts:[],\n                init: function($panel){\n                    var my = this;\n                    this.$panel = $panel;\n\n                    var datas = this.$panel.attr('edit-block-datas');\n                    this.setDatas(datas || this.default_datas);\n                    this.loadScript();\n\n                    this.$panel.bind(\"plugin:change:data\", function(event, data){\n                        my.setDatas(data);\n                        my.build();\n                    });\n                    this.$panel.bind(\"plugin:remove\", function(event, data){\n                        my.destory();\n                    });\n                    this.$panel.bind(\"plugin:reset\", function(event, data){\n                        my.reset();\n                    });\n                    this.$panel.bind(\"plugin:build\", function(event, data){\n                        my.build();\n                    });\n                },\n                setDatas: function(datas){\n                    this.datas = typeof(datas)==='string' ? JSON.parse(datas) : datas;\n                    this.$panel.attr('edit-block-datas', JSON.stringify(this.datas));\n                },\n                build: function(){\n                    var _self = this,\n                        _data = \"{'text':'\"+_self.datas[0].text+\"',\" +\n                                \"'pic':'\"+_self.datas[0].pic+\"',\" +\n                                \"'url':'\"+_self.datas[0].url+\"'}\";\n                    _self.$panel.attr(\"data\",_data);\n                },\n                loadScript:function(){\n                    var _self = this;\n                    var _d = document,\n                        _s1 = _d.createElement(\"script\"),\n                        _s2 = _d.createElement(\"script\");\n\n                    _s1.type = \"text/javascript\";\n                    _s1.id = \"bdshare_js\";\n                    _s1.setAttribute(\"data\",\"type=tools&amp;uid=10006&amp;mini=1\");\n\n                    _s2.type = \"text/javascript\";\n                    _s2.id = \"bdshell_js\";\n\n                    document.body.appendChild(_s1);\n                    document.body.appendChild(_s2);\n\n                    _self.scripts.push(_s1);\n                    _self.scripts.push(_s2);\n\n                    document.getElementById(\"bdshell_js\").src = \"http://share.baidu.com/static/js/shell_v2.js?t=\" + new Date().getHours();\n                },\n                destory: function(){\n                    var _self = this,\n                        _d = document;\n                    for(var i = 0,_length = _self.scripts.length ;i < _length ;i++){\n                        try{\n                            var _script = _self.scripts.shift();\n                            _d.body.removeChild(_script);\n                        }catch(e){}\n                    }\n                },\n                reset: function(){}\n            };\n        })($);</script>\n\n<script plugin_class_name=\"puzzle01\" class=\"edit-js-handle\">/**\n * Created with JetBrains PhpStorm.\n * User: zhaoyang07\n * Date: 13-10-27\n * Time: 上午9:16\n * To change this template use File | Settings | File Templates.\n */\n(function($){\n    $.BlockPlugins.puzzle01 = function(){};\n    $.BlockPlugins.puzzle01.prototype = {\n        $panel: null,\n        default_datas: [\n            {\"bigTitle\":\"标题一标题一\",\"word_link\":\"\",\"big_link\":\"\",\"image_link\":\"\",\"title\":\"马伊琍挺大肚独自\",\"src\":\"http://tb1.bdstatic.com/tb/cms/topicconf/sample_03.png\",\"word\":\" 16日，马伊琍挺大肚独自现身北京东四环。她身穿黑裙却难遮隆起的大肚孕相明显。\"},\n            {\"bigTitle\":\"不填\",\"big_link\":\"\",\"image_link\":\"\",\"title\":\"马伊琍挺大肚独自\",\"src\":\"http://tb1.bdstatic.com/tb/cms/topicconf/sample_03.png\",\"word\":\" 16日，马伊琍挺大肚独自现身北京东四环。她身穿黑裙却难遮隆起的大肚孕相明显。\"},\n            {\"bigTitle\":\"不填\",\"big_link\":\"\",\"image_link\":\"\",\"title\":\"马伊琍挺大肚独自\",\"src\":\"http://tb1.bdstatic.com/tb/cms/topicconf/sample_03.png\",\"word\":\" 16日，马伊琍挺大肚独自现身北京东四环。她身穿黑裙却难遮隆起的大肚孕相明显。\"},\n            {\"bigTitle\":\"不填\",\"big_link\":\"\",\"image_link\":\"\",\"title\":\"马伊琍挺大肚独自\",\"src\":\"http://tb1.bdstatic.com/tb/cms/topicconf/sample_03.png\",\"word\":\" 16日，马伊琍挺大肚独自现身北京东四环。她身穿黑裙却难遮隆起的大肚孕相明显。\"}\n        ],\n        datas: null,\n        init: function($panel){\n            var my = this;\n            this.$panel = $panel;\n\n            var datas = this.$panel.attr('edit-block-datas');\n            this.setDatas(datas || this.default_datas);\n            this.build();\n\n            this.$panel.bind(\"plugin:change:data\", function(event, data){\n                my.setDatas(data);\n                my.build();\n            });\n            this.$panel.bind(\"plugin:remove\", function(event, data){\n                my.destory();\n            });\n            this.$panel.bind(\"plugin:reset\", function(event, data){\n                my.reset();\n            });\n            this.$panel.bind(\"plugin:build\", function(event, data){\n                my.build();\n            });\n        },\n        setDatas: function(datas){\n            this.datas = typeof(datas)==='string' ? JSON.parse(datas) : datas;\n            this.$panel.attr('edit-block-datas', JSON.stringify(this.datas));\n        },\n        build: function(){\n            var self = this,\n                datas = self.datas,\n                title = self.$panel.find(\".header_up > a\"),\n                ul = self.$panel.find(\".content > ul\");\n            var str = '';\n\n            for(var i = 0 ,length = datas.length; i <length ; i++){\n                str += '<li><a href=\"'+datas[i].image_link+'\" target=\"_blank\"><img src=\"'+datas[i].src+'\"/></a><a href=\"'+datas[i].word_link+'\" target=\"_blank\"><div><span class=\"title\">'+datas[i].title+'</span><span class=\"word\">'+datas[i].word+'</span></div></a></li>';\n                title.html(datas[0].bigTitle);\n                title.attr('href', datas[0].big_link);\n            }\n\n            ul.html(str);\n        },\n        destory: function(){},\n        reset: function(){}\n    };\n})($);</script><script plugin_class_name=\"puzzle03\" class=\"edit-js-handle\">/**\n * Created with JetBrains PhpStorm.\n * User: zhaoyang07\n * Date: 13-10-27\n * Time: 上午9:16\n * To change this template use File | Settings | File Templates.\n */\n(function($){\n    $.BlockPlugins.puzzle03 = function(){};\n    $.BlockPlugins.puzzle03.prototype = {\n        $panel: null,\n        default_datas: [\n            {\"title\":\"标题标题标题\",\"src\":\"http://tb1.bdstatic.com/tb/cms/topicconf/sample3_03.png\"},\n            {\"title\":\"标题标题标题\",\"src\":\"http://tb1.bdstatic.com/tb/cms/topicconf/sample4_06.png\"},\n            {\"title\":\"标题标题标题\",\"src\":\"http://tb1.bdstatic.com/tb/cms/topicconf/sample4_06.png\"},\n            {\"title\":\"标题标题标题\",\"src\":\"http://tb1.bdstatic.com/tb/cms/topicconf/sample5_09.png\"},\n            {\"title\":\"标题标题标题\",\"src\":\"http://tb1.bdstatic.com/tb/cms/topicconf/sample6_13.png\"},\n            {\"title\":\"标题标题标题\",\"src\":\"http://tb1.bdstatic.com/tb/cms/topicconf/sample6_13.png\"}\n        ],\n        datas: null,\n        init: function($panel){\n            var my = this;\n            this.$panel = $panel;\n\n            var datas = this.$panel.attr('edit-block-datas');\n            this.setDatas(datas || this.default_datas);\n            this.build();\n\n            this.$panel.bind(\"plugin:change:data\", function(event, data){\n                my.setDatas(data);\n                my.build();\n            });\n            this.$panel.bind(\"plugin:remove\", function(event, data){\n                my.destory();\n            });\n            this.$panel.bind(\"plugin:reset\", function(event, data){\n                my.reset();\n            });\n            this.$panel.bind(\"plugin:build\", function(event, data){\n                my.build();\n            });\n        },\n        setDatas: function(datas){\n            this.datas = typeof(datas)==='string' ? JSON.parse(datas) : datas;\n            this.$panel.attr('edit-block-datas', JSON.stringify(this.datas));\n        },\n        build: function(){\n            var self = this,\n                datas = self.datas,\n                title = self.$panel.find(\".header_up span\"),\n                bigImg = self.$panel.find(\".content > .big_img\"),\n                middleImg = self.$panel.find(\".content > .middle_img\"),\n                littleBigImg = self.$panel.find(\".content > .little_big_img\"),\n                littleImg = self.$panel.find(\".content > .little_img\");\n\n            var str1 = '',\n                str2 =  '',\n                str3 = '',\n                str4 = '';\n\n            for(var i = 0 , _length = datas.length ; i < _length ; i++){\n                if(i < 1){\n                    str1 += '<a href=\"'+datas[i].link+'\" target=\"_blank\"><img src=\"'+datas[i].src+'\"/></a>';\n                }else if(i < 3){\n                    str2 += '<a href=\"'+datas[i].link+'\" target=\"_blank\"><img src=\"'+datas[i].src+'\"/></a>';\n                }else if(i < 4){\n                    str3 += '<a href=\"'+datas[i].link+'\" target=\"_blank\"><img src=\"'+datas[i].src+'\"/></a>';\n                }else if(i < 6){\n                    str4 += '<a href=\"'+datas[i].link+'\" target=\"_blank\"><img src=\"'+datas[i].src+'\"/></a>';\n                }\n\n                title.html(datas[0].title);\n            }\n\n            bigImg.html(str1);\n            middleImg.html(str2);\n            littleBigImg.html(str3);\n            littleImg.html(str4);\n\n        },\n        destory: function(){},\n        reset: function(){}\n    };\n})($);</script></div>", "layout_name": "居中专题页面_960_紫色", "create_user": "yang<PERSON><PERSON>g", "create_time": "2013-11-16 11:41:39", "last_user": "yang<PERSON><PERSON>g", "last_time": "2013-11-17 14:02:40", "before_content": "", "after_content": "", "publish_user": null, "publish_time": "0000-00-00 00:00:00", "page_pc_url": "", "page_title": "贴吧10周年宝贝魅力征集|女神来着哪个吧？", "page_css": "/* 色系 */\n.header_background{background:#B5BBF3;}\n.header_color{color:#FFF;}\n.content_background{background:#7980D3;}\n.content_color{color:#FFF;}\n\n/*布局页面*/\n.page_body_panel{\n    font-size: 14px;\n    line-height: 24px;\n}\n.page_content_header{\n    background:#ffffff;\n}\n.page_content_header .logo {\n    width: 92px;\n    height: 30px;\n    display: inline;\n    float: left;\n    margin-top: 5px;\n    background: url(http://tieba.baidu.com/tb/zt/greenxingdong/images/ls_logo.jpg) no-repeat;\n}\n\n.page_content_header .main_header{\n    position: relative;\n    width: 960px;\n    height: 40px;\n    margin: auto;\n}\n.page_header_image_bg{\n}\n.page_header_image{\n    width: 1060px;\n    height: 300px;\n    margin: auto;\n}\n.page_body_panel .page_content_body_bg{\n}\n.page_body_panel .page_content_body{\n    position: relative;\n    width: 960px;\n    height: 800px;\n    margin: auto;\n    top: -100px;\n}\n.page_body_panel .page_content_footer{\n    position: relative;\n    color: #515151;\n    background: #ffffff;\n    text-align: center;\n    height: 100px;\n    line-height: 100px;\n    top: -100px;\n}\n.page_content_footer a, .page_content_footer a:hover{ color:#515151;}\n.page_content_footer span{ margin:0 5px;}\n\n/*布局模块*/\n.edit_block{\n   position: absolute;\n   _padding-top: 12px;\n}\n\n.edit_block .header_down{\n    height: 31px;\n    display:block;\n    margin-top: 12px;\n    border-bottom: 1px solid #B5BBF3;\n}\n.edit_block .header_up{\n    width:242px;\n    height:44px;\n    position:absolute;\n    _margin-top: -12px;\n    left:0px;\n    background:url(http://tb1.bdstatic.com/tb/cms/topicconf/header-nav_03.png) no-repeat;\n    text-align: left;\n}\n.edit_block .header_up span, .edit_block .header_up a{\n    font-size:1.25em;\n    text-decoration:none;\n    font-family:STHeiti,'微软雅黑','Microsoft Yahei',Arial,sans-serif;\n    display:inline-block;\n    margin-top:8px;\n    margin-left:12px;\n}\n.edit_block .content{\n\n    font-size:0.875em;\n    line-height:25px;\n    text-align: justify;\n    text-justify:inter-ideograph;\n}\n\n/*通用样式*/\n.clearfix:after{display:block;clear:both;content:\"\";visibility:hidden;height:0}\n.clearfix{zoom:1}", "page_js": ""}