<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 *  
 **/


class setInfoAction extends Util_Base {
    private $_head_id = 0;
    private $_lucky_id = 0;
    private $_user_info = array();
    private $_name;
    private $_phone;
    private $_address;
    

    private function _input() {
        $this->_head_id = intval(Bingo_Http_Request::get('id', 0));
        $this->_lucky_id = intval(Bingo_Http_Request::get('pack_type', 0));
        
        $this->_name    = trim(Bingo_Http_Request::get('name', ''));
        $this->_phone   = trim(Bingo_Http_Request::get('phone', ''));
        $this->_address = trim(Bingo_Http_Request::get('address', ''));
        
        if (empty($this->_head_id) || empty($this->_lucky_id) || empty($this->_name)|| empty($this->_phone)|| empty($this->_address)) {
            return false;
        }
        if (!$this->_arrUserInfo['is_login']) {
            return false;
        }
        return true;
    }

    public function execute() {
        if (!$this->_input()) {
            $error = Tieba_Errcode::ERR_PARAM_ERROR;
            $this->_jsonRet($error, Tieba_Error::getErrmsg($error));
            return false;
        }
        $strUserAddrKey = Actions_Jd_JingDong::JINGDONG_DALIBAO_ADDR_PRE.'_'.$this->_arrUserInfo['user_id'];
        
        $objRedis = Util_Redis::initCache();
        if (empty($objRedis)) {
            Bingo_Log::warning("init redis error");
            $error = Tieba_Errcode::ERR_SUCCESS;
            $this->_jsonRet($error, Tieba_Error::getErrmsg($error));
            return false;
        }
        $strAddr = Bingo_String::array2json(
                    array(
                		'name' => $this->_name,
                		'phone' => $this->_phone,
                    	'address' => $this->_address,
                    )
        );
        $arrInput = array(
            'key' => $strUserAddrKey,
            'value' => $strAddr,
        );
        $arrRes = $objRedis->SET($arrInput);
        if ($arrRes['err_no'] !== 0) {
            Bingo_Log::warning('call redis error! '.serialize($arrRes).'_'.serialize($arrInput));  
            $error = Tieba_Errcode::ERR_PARAM_ERROR;
            $this->_jsonRet($error, Tieba_Error::getErrmsg($error));     
            return false;     
        }
        $error = Tieba_Errcode::ERR_SUCCESS;
        $this->_jsonRet($error, Tieba_Error::getErrmsg($error));
        return true;
    }
}



/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
