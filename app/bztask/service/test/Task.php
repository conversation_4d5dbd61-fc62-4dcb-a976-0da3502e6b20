<?php

/*==================================
*   Copyright (C) 2016 Baidu.com, Inc. All rights reserved.
*   
*   filename	:	Task.php
*   author		:	zhouping01
*   create_time	:	2016-02-19
*   desc		:	
*
===================================*/

$arrInput = array(
	'taskid' => 14,// int
	'status'      => 2, //{int} 页码
	'opuname'	=> 'zhouping01',
);
$arrOutput = Tieba_Service::call('bztask', 'updateTaskStatus', $arrInput);
var_dump($arrOutput,__FILE__,__LINE__);
exit();


$arrInput = array(
	'user_id' => 50015150,// int
	'pn'      => 0, //{int} 页码
	'rn'      => 10, // {int} 每页task_id' => 0，// 上一次拉取的最后一个 task_id 
);

$arrOutput = Tieba_Service::call('bztask', 'getAllTaskList', $arrInput);

var_dump($arrOutput,__FILE__,__LINE__);
exit();
$arrInput = array(
	'taskid' => 43,// int
	'pushstatus'      => 2, //{int} 页码
	'opuname'	=> 'zhouping01',
);
$arrOutput = Tieba_Service::call('bztask', 'updatePushStatus', $arrInput);
var_dump($arrOutput,__FILE__,__LINE__);



////getInfo
$arrInput = array(
	"taskid" => 43,//任务摘要
);


$ret = Tieba_Service::call('bztask', 'getSurveyTask', $arrInput, null, null, 'post', 'php', 'utf-8', null);
var_dump($ret);
exit();


