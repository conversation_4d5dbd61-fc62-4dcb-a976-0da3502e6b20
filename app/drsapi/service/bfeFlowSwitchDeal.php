<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file bfeFlowSwitchDeal.php
 * <AUTHOR>
 * @date 2015/1/7 15:00:00
 * @brief 
 *  
 **/

class bfeFlowSwitchDeal
{
	public static function bfePostData($idc,$arrayInput){
	
	switch($idc){
		case "hz":
        	foreach($arrayInput as $key => $value){
				if($key == "bfe-hz.BFE.hz"){
                		foreach($value as $bnsNode => $percent){
                        		if($bnsNode == "router.orp.tc"){
									$arrayInput[$key][$bnsNode] = 50;
								}
								if($bnsNode == "router.orp.jx"){
									$arrayInput[$key][$bnsNode] = 50;
								}	
			
                		}
        		}
		}
		return $arrayInput;
        case "jx":
                foreach($arrayInput as $key => $value){
                        if($key == "bfe-yf.BFE.yf"){
                                foreach($value as $bnsNode => $percent){
                                        if($bnsNode == "router.orp.tc"){
                                                $arrayInput[$key][$bnsNode] = 50;
                                        }
                                        if($bnsNode == "router.orp.jx"){
                                                $arrayInput[$key][$bnsNode] = 50;
                                        }
                        
                                }
                        }
                }
                return $arrayInput;

        case "tc":
                foreach($arrayInput as $key => $value){
                        if($key == "bfe-tc.BFE.tc"){
                                foreach($value as $bnsNode => $percent){
                                        if($bnsNode == "router.orp.tc"){
                                                $arrayInput[$key][$bnsNode] = 50;
                                        }
                                        if($bnsNode == "router.orp.jx"){
                                                $arrayInput[$key][$bnsNode] = 50;
                                        }

                                }
                        }
                }
                return $arrayInput;

        case "sh":
                foreach($arrayInput as $key => $value){
                        if($key == "bfe-sh.BFE.sh"){
                                foreach($value as $bnsNode => $percent){
                                        if($bnsNode == "router.orp.tc"){
                                                $arrayInput[$key][$bnsNode] = 50;
                                        }
                                        if($bnsNode == "router.orp.jx"){
                                                $arrayInput[$key][$bnsNode] = 50;
                                        }

                                }
                        }
                }
                return $arrayInput;

	}
	
    public static function execute()
    {
        $cur_time = time();
        $commonObj = new commonApi();
		$initBfeJson = '{"bfe-hz.BFE.hz":{"router.orp.tc":100,"GSLB_BLACKHOLE":0,"router.orp.jx": 0},"bfe-yf.BFE.yf": {"router.orp.tc": 0,"GSLB_BLACKHOLE": 0,"router.orp.jx": 100},"bfe-tc.BFE.tc": {"router.orp.tc": 100,"GSLB_BLACKHOLE": 0,"router.orp.jx": 0},"bfe-sh.BFE.sh": {"router.orp.tc": 100,"GSLB_BLACKHOLE": 0,"router.orp.jx": 0}}';
		
		// 核心模块ui列表
		$core0 = array( "module1" => "ui_mo_client_frs",
                        "module2" => "ui_mo_client_pb",
                        "module3" => "ui_pc_frsui",
                        "module4" => "ui_pc_pbui");   
						
		// 格式化数组
		$initBfeArray = json_decode($initBfeJson,TRUE);

		// 核心故障模块计数器
		$count = 0;
		
        $arrInput = array(
            'module'=>array(),
            'title'=>array(
                'stable_rate_lt_90'
            ),
            'period'=>600,	//3min
        );

        $arrOutput = $commonObj->getAlarmInfoAndUpdate($arrInput);
        if(false === $arrOutput)
        {
            Bingo_Log::warning("fail to get alarm info");
            return false;
        }

        if(empty($arrOutput))
        {
            return true;
        }

        $arrInput = array();
        foreach($arrOutput as $module=>$alarm_info)
        {
            foreach($alarm_info as $k=>$info)
            {
                $related_id = $info['id'];
                $title = $info['title'];
                $errtag_tmp = $info['errtag'];
                $errtag_tmp_arr = explode("@", $errtag_tmp);
                $errtag = $errtag_tmp_arr[1];
				$idc = $errtag_tmp_arr[2];
                $occ_time = $info['occ_time'];
                $cont_time = $info['cont_time'];
                $str_cont_time = self::_convTimeFormat($cont_time);
                $summary = "$errtag"."稳定性低于90%，持续"."$str_cont_time";
                $level = 2;
                if('err_rate_ge_90' == $title)
                {
                    $level = 1;
                    $summary = "$errtag"."稳定性低于90%，持续"."$str_cont_time";
                }
                $arrInput[] = array(
                    'module'=>$module,
                    'title'=>$title, 
					'errtag'=>$errtag_tmp,
					'occ_time'=>$occ_time,
                    'cont_time'=>$cont_time,
                    'summary'=>$summary,
                    'level'=>$level,
					'status'=>0,
                    'related_id'=>$related_id,
					'moduleUi'=>$errtag,
					'idc'=>$idc,
                );
            }
        }

        $arrOutput = $commonObj->msendAlarm($arrInput);
		
		foreach($arrInput as $input){
		// 检查数组是否为空
		if(!isset($input['module']) || !isset($input['title']) || !isset($input['idc'] ||!isset($input['moduleUi']){
			Bingo_Log::warning("err param : ". print_r($input, true));
			return false;
		}
		
		// 故障idc数组初始化
		$idcArray = array();
		
		// 检查故障module是否存在核心ui模块
		if(array_search($input['moduleUi'],$core0,TURE)){
			$count++;	
			$idcArray = $input['idc'];
		}
		$idc = $idcArray[0];
		
		// 检查故障module是否在同一IDC
		foreach($idcArray as $idcerr){
			if("$idc" != "$idcerr"){
				Bingo_Log::warning("err occ not in the same idc");
                        	return false;				
			}
		}	
		if($count > 1){
			// 根据idc初始化post json array
			$bfeArrayData = bfePostData($idc,$initBfeArray);
    	
			$service = "tieba";
			$arrayInput = array(
    				'change_data' => $bfeArrayData,
    				'service' => $service);
					
			// BFE 切换
			$result = Tieba_Service::call('drsapi','bfeSwitchFlow',$arrayInput);
			if($result["errno"] == "0") {
				return true;
			}
		}
		
		
        return false;
    }

    private static function _convTimeFormat($time)
    {   
        if(0 >= $time)
        {   
            return "0s";
        }   
        //秒钟
        $second = $time%60;
        //分钟
        $minute_count = floor($time/60);
        if(0 == $minute_count)
        {   
            return "$second"."s";
        }   
        $minute = $minute_count%60;
        $hour_count = floor($minute_count/60);
        if(0 == $hour_count)
        {   
            return "$minute"."m"."$second"."s";
        }   
        $hour = $hour_count%24;
        $day = floor($hour_count/24);
        if(0 == $day)
        {   
            return "$hour"."h"."$minute"."m"."$second"."s";
        }   
        return "$day"."d"."$hour"."h"."$minute"."m"."$second"."s";
    } 
}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
