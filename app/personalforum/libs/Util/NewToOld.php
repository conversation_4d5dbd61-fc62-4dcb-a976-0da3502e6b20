<?php
/***************************************************************************
 *
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file NewToOld.php
 * <AUTHOR>
 * @date 2013/08/19 18:45:52
 * @brief Transfer new template vars to old ones
 *
 **/
class Util_NewToOld {

    const CAN_PICASSO = 6;
    private static $_arrOldExtField = array('photo', 'card', 'grade', 'usermask', 'brandzone', 'vcode', 'bakan', 'notice', 'postprefix', 'ziyuan', 'customize', 'medal', 'goodalbum', 'zhibo', 'findpeople', 'difang');
    private static $_arrWhiteList = array(
        'http://www.tudou.com/',
        'http://v.blog.sohu.com/',
        'http://tv.sohu.com/',
        'http://share.vrs.sohu.com/',
        'http://my.tv.sohu.com/',
        'http://player.56.com/',
        'http://www.56.com/',
        'http://kankanews.com/',
        'http://video6.smgbb.cn/',
        'http://www.youku.com/',
        'http://player.youku.com/',
        'http://static.youku.com/',
        'http://www.ku6.com/',
        'http://player.ku6.com/',
        'http://video.sina.com.cn/',
        'http://vhead.blog.sina.com.cn/',
        'http://you.video.sina.com.cn/',
        'http://video.qq.com/',
        'http://www.baidu.com/',
        'http://box.baidu.com/',
        'http://hi.baidu.com/',
        'http://mv.baidu.com/',
        'http://mvimg.baidu.com/',
        'http://mvideo.baidu.com/',
        'http://player.cntv.cn/',
        'http://player.xiyou.cntv.cn/',
        'http://www.yinyuetai.com/',
        'http://player.yinyuetai.com/',
        'http://www.aipai.com/',
        'http://www.cutv.com/',
        'http://player.cutv.com/',
        'http://www.pptv.com/',
        'http://v.pptv.com/',
        'http://www.letv.com/',
        'http://www.iqiyi.com/',
        'http://yule.iqiyi.com/',
        'http://player.video.qiyi.com/',
        'http://www.ifeng.com/',
        'http://s.v.ifeng.com/',
        'http://v.ifeng.com/',
        'http://www.m1905.com/',
        'http://www.joy.cn/',
        'http://client.joy.cn/',
        'http://www.molihe.com/',
        'http://mv.molihe.com/',
        'http://swf.molihe.com/',
        'http://www.baomihua.com/',
        'http://video.baomihua.com/',
        'http://www.ouou.com/',
        'http://flash.ouou.com/',
        'http://dv.ouou.com/',
        'http://misc.home.news.cn/',
        'http://www.news.cn/',
        'http://www.wasu.cn/',
        'http://play1.wasu.cn/',
        'http://play.wasu.cn/',
        'http://v.iask.com/',
        'http://i7.imgs.letv.com/',
        'http://static.video.qq.com/',
        'http://player.pptv.com/',
        'http://v.pps.tv/',
        'http://ipd.pps.tv/',
        'http://www.funshion.com/',
        'http://player.pps.tv/',
        'http://api.funshion.com/',
        'http://www.hunantv.com/',
        'http://www.meipai.com/',
        'http://www.bilibili.com/',
        'http://share.acg.tv/',
        'http://static.hdslb.com/',
        'http://bangumi.bilibili.com/',
    );

    public static function adjustTplVar($arrCoreTplData, $arrExtTplData, $arrRightTplData) {
        $arrDestData = array();
        $arrDestForum = self::_getDestForum($arrCoreTplData, $arrExtTplData);
        $arrDestUser = self::_getDestUser($arrCoreTplData, $arrExtTplData);
        $arrDestThreadList = self::_getDestThreadList($arrCoreTplData, $arrExtTplData);
        $arrDestForumuser = self::_getDestForumuser($arrCoreTplData, $arrExtTplData);
        $arrDestForumex = self::_getDestForumex($arrCoreTplData, $arrExtTplData);
        $arrDestOther = self::_getDestOther($arrCoreTplData, $arrExtTplData);

        $arrDestData = array(
            'forum' => $arrDestForum,
            'forumex' => $arrDestForumex,
            'user' => $arrDestUser,
            'thread_list' => $arrDestThreadList,
            'forumuser' => $arrDestForumuser,
            'personal' => $arrRightTplData,
        );
        $arrDestData = array_merge($arrDestData, $arrDestOther);
        return $arrDestData;
    }

    private static function _getDestForum($arrCoreTplData, $arrExtTplData) {
        $arrDestForum = array(
            'from_redirect' => $arrCoreTplData['request']['forum']['from_redirect'],
            'is_exists' => $arrCoreTplData['forum']['base']['is_exists'],
            'is_forbidden' => $arrCoreTplData['forum']['base']['is_forbidden'],
            'forum_id' => $arrCoreTplData['forum']['base']['forum_id'],
            'forum_name' => $arrCoreTplData['forum']['base']['forum_name'],
            'first_class' => $arrCoreTplData['forum']['dir']['level_1_name'],
            'second_class' => $arrCoreTplData['forum']['dir']['level_2_name'],
            //'fstyle'            => $arrCoreTplData['forum']['style'],
            'dir_style' => $arrCoreTplData['forum']['dir_style'],
            'fcard' => $arrExtTplData['card'],
            'forum_grade' => $arrExtTplData['grade']['grade'],
            'forum_perm' => $arrExtTplData['grade']['perm'],
            'thread_num' => $arrCoreTplData['post']['base']['thread_num'],
            'post_num' => $arrCoreTplData['post']['base']['post_num'],
            'goodclass_show' => $arrCoreTplData['forum']['goodclass'],
            'cut_blank_exist' => $arrCoreTplData['forum']['base']['cut_blank_exist'],
            //'comment_threshold' => 0,
            'shield_post' => 1,
            'group_tab' => 1,
            'album_forum' => $arrExtTplData['photo']['album_forum'],
            //'simple_mode'       => $arrCoreTplData['forum']['simple_mode'],
            'global_attr' => $arrCoreTplData['forum']['global_attr'],
        );

        //stock forum
        if (isset($arrExtTplData['Stock']['stock_frs'])) {
            $arrDestForum['thread_num'] = $arrExtTplData['Stock']['stock_frs']['total'];
        }

        return $arrDestForum;
    }

    private static function _getDestUser($arrCoreTplData, $arrExtTplData) {
        $arrDestUser = array(
            // 'need_black_pop'    => false,
            // 'black_pop_level'   => 1,
            'is_login' => $arrCoreTplData['user']['base']['is_login'],
            'id' => $arrCoreTplData['user']['base']['user_id'],
            'name' => $arrCoreTplData['user']['base']['user_name'],
            'portrait' => Tieba_Ucrypt::encode($arrCoreTplData['user']['base']['user_id'], $arrCoreTplData['user']['base']['user_name']),
            'email' => Tieba_Util::maskEmail($arrCoreTplData['user']['base']['email']),
            'mobilephone' => Tieba_Util::maskPhone($arrCoreTplData['user']['base']['mobile']),
            'no_un' => intval($arrCoreTplData['user']['base']['no_un']),
            'name_show' => $arrCoreTplData['user']['base']['user_name'],
            'identity' => self::_transToIdentity($arrCoreTplData['user']['role']),
            'itieba_id' => $arrCoreTplData['user']['base']['outer_id'],
            'like_forums' => array(),
            'meizhi_level' => intval($arrCoreTplData['user']['base']['meizhi_level']) - 1,
            'is_new' => $arrCoreTplData['user']['base']['is_new'],
            //'sid'               => substr($arrCoreTplData['request']['cookie']['BDUSS'], 0, 64),
            'bduid' => $arrCoreTplData['request']['cookie']['baiduid'],
            'userhide' => $arrExtTplData['usermask']['self_hide'],
        );

        if ($arrCoreTplData['user']['base'] && is_array($arrCoreTplData['user']['base'])) $arrDestUser = array_merge($arrCoreTplData['user']['base'], $arrDestUser);

        //�û�˽����Ϣ����
        unset($arrDestUser["ip"]);
        $arrDestUser["mobile"] = Tieba_Util::maskPhone($arrDestUser["mobile"]);

        foreach ($arrCoreTplData['user']['favor_forum']['concern'] as $arrEach) {
            $arrDestUser['user_forum_list']['info'][] = array(
                'forum_name' => $arrEach['forum_name'],
                'user_level' => $arrEach['level_id'],
                'user_exp' => $arrEach['cur_score'],
                'id' => $arrEach['forum_id'],
                'is_like' => true,
                'favo_type' => $arrEach['forum_type'],
            );
        }
        foreach ($arrCoreTplData['user']['favor_forum']['recommand'] as $arrEach) {
            $arrDestUser['user_forum_list']['info'][] = array(
                'forum_name' => $arrEach['forum_name'],
                'user_level' => '',
                'user_exp' => '',
                'id' => $arrEach['forum_id'],
                'is_like' => false,
                'favo_type' => 0,
            );
        }

        return $arrDestUser;
    }

    private static function _getDestForumuser($arrCoreTplData, $arrExtTplData) {
        $arrUserGrade = self::_procUserGrade($arrCoreTplData['user']['grade'], $arrCoreTplData['user']['base']['is_login']);
        $arrUserBlock = self::_procUserBlock($arrCoreTplData['user']['block'], $arrCoreTplData['user']['base']['is_login']);

        //个人吧只能吧主发帖 begin
        $isLogin = $arrCoreTplData['user']['base']['is_login'];
        $isManager = $arrCoreTplData['user'] ? $arrCoreTplData['user'] : null;

        $canPostFrs = false;
        if ($isLogin && $isManager['role']['is_forum_manager']) {
            $canPostFrs = true;
        }
        //个人吧只能吧主发帖 end

        $isHost = false;
        if($canPostFrs) {
            $isHost = true;
        }

        if ($arrCoreTplData['user']['block']['is_block']) {
            $arrParams = array(
                'user_id' => intval($arrCoreTplData['user']['sign']['user_info']['user_id']),
                'forum_id' => intval($arrCoreTplData['user']['block']['block_info']['forum_id']),
            );
            $strBlockReason = self::_getBlockReason($arrParams);
            $arrUserBlock['block_reason'] = $strBlockReason;
        }

        $arrBalv = array_merge($arrUserGrade, $arrUserBlock);
        $arrDestForumuser = array(
            'ret' => '',    //TODO
            'balv' => $arrBalv,
            'rank' => array(
                'index' => $arrCoreTplData['user']['rank']['rank'],
                'num' => $arrCoreTplData['user']['rank']['change'],
                'status' => $arrCoreTplData['user']['rank']['status'],
            ),
            'lv_forum_name_show' => NULL,
            'sign_in_info' => array(
                'user_info' => array(
                    'user_id' => $arrCoreTplData['user']['sign']['user_info']['user_id'],
                    'is_sign_in' => $arrCoreTplData['user']['sign']['user_info']['is_sign_in'],
                    'user_sign_rank' => $arrCoreTplData['user']['sign']['user_info']['user_sign_rank'],
                    'sign_time' => $arrCoreTplData['user']['sign']['user_info']['sign_time'],
                    'cont_sign_num' => $arrCoreTplData['user']['sign']['user_info']['cont_sign_num'],
                    'cout_total_sing_num' => $arrCoreTplData['user']['sign']['user_info']['cout_total_sing_num'],
                    'is_org_disabled' => $arrCoreTplData['user']['sign']['user_info']['is_org_disabled'],
                    'c_sign_num' => $arrCoreTplData['user']['sign']['user_info']['cont_sign_num'],
                    'cm_sign_num' => $arrCoreTplData['user']['sign']['user_info']['cont_sign_num'],
                    'hun_sign_num' => isset($arrCoreTplData['user']['sign']['user_info']['hun_sign_num']) ? $arrCoreTplData['user']['sign']['user_info']['hun_sign_num'] : 0,
                    'total_resign_num' => isset($arrCoreTplData['user']['sign']['user_info']['total_resign_num']) ? $arrCoreTplData['user']['sign']['user_info']['total_resign_num'] : 0,
                ),
                'forum_info' => array(
                    'is_on' => $arrCoreTplData['user']['sign']['forum_info']['is_on'],
                    'is_filter' => $arrCoreTplData['user']['sign']['forum_info']['is_filter'],
                    'forum_info' => array(
                        'forum_id' => $arrCoreTplData['forum']['base']['forum_id'],
                        'level_1_dir_name' => $arrCoreTplData['forum']['dir']['level_1_name'],
                    ),
                    'current_rank_info' => $arrCoreTplData['user']['sign']['forum_info']['current_rank_info'],
                    'yesterday_rank_info' => $arrCoreTplData['user']['sign']['forum_info']['yesterday_rank_info'],
                    'weekly_rank_info' => $arrCoreTplData['user']['sign']['forum_info']['weekly_rank_info'],
                    'monthly_rank_info' => $arrCoreTplData['user']['sign']['forum_info']['monthly_rank_info'],
                    'level_1_dir_name' => $arrCoreTplData['forum']['dir']['level_1_name'],
                    'level_2_dir_name' => $arrCoreTplData['forum']['dir']['level_2_name'],
                ),
            ),
            'has_member_apply' => 0,
            'power' => self::_transToOldPerm($arrCoreTplData['user']['perm']),
            //'forbid_flag'       => $arrCoreTplData['user']['block']['block_type'],  //TODO check right
            'forbid_flag' => empty($arrCoreTplData['user']['block']['block_type']) ? 0 : $arrCoreTplData['user']['block']['block_type'],
            'user_role' => self::_procRole($arrCoreTplData['user']['role']), //TODO diff
            'user_perm' => self::_procPerm($arrCoreTplData['user']['perm'], $arrCoreTplData), //TODO diff
            'post_perm' => array(
                'can_post' => isset($arrCoreTplData['user']['perm']['can_post']) ? $arrCoreTplData['user']['perm']['can_post'] : true,
                'can_post_frs' => $canPostFrs,
                'can_post_pb' => isset($arrCoreTplData['user']['perm']['can_post_pb']) ? $arrCoreTplData['user']['perm']['can_post_pb'] : true,
                'block_type' => $arrCoreTplData['user']['block']['block_type'],
                'post_level_thred' => array('all' => 4, 'frs' => 4, 'pb' => 4,), //TODO
            ),
            'user_can' => array(
                'is_assist' => intval($arrCoreTplData['user']['role']['is_forum_assist']) || intval($arrCoreTplData['user']['role']['is_forum_voiceadmin']),
                'protal_perm' => intval($arrCoreTplData['user']['perm']['can_edit_daquan']),
                'zyq_perm' => 0,
            ),
            'can_pop' => 0,   //TODO Ext
            'is_host' => $isHost,
        );
        if ($arrExtTplData['filepost']['open'] === true) {
            $arrDestForumuser['power'] += 16;
        }
        return $arrDestForumuser;
    }

    private static function _getDestThreadList($arrCoreTplData, $arrExtTplData) {
        $arrDestThreadList = $arrCoreTplData['post']['thread_list'];
        return $arrDestThreadList;
    }

    private static function _getDestOther($arrCoreTplData, $arrExtTplData) {
        $arrDestPage = array(
            'redirect_query' => $arrCoreTplData['request']['querystring']['pre_query'],
            'cur_page' => $arrCoreTplData['request']['page']['cur_page'],
            'kw' => $arrCoreTplData['request']['forum']['name'],
            'total_page' => $arrCoreTplData['request']['page']['total_page'],
            'frs_page' => $arrCoreTplData['request']['url']['type'],
            'cur_good_id' => is_null($arrCoreTplData['request']['querystring']['class_id']) ? 0 : $arrCoreTplData['request']['querystring']['class_id'],
            'is_ipad' => $arrCoreTplData['request']['page']['is_pad'],
            'post_params' => $arrCoreTplData['request']['page']['post_params'],
            'add_favor_params' => $arrCoreTplData['request']['page']['add_favor_params'],
            'referer' => $arrCoreTplData['request']['server']['referer'],
        );
        if (isset($arrExtTplData['photo']['photo_frs'])) {
            $arrDestPage['photo_frs'] = $arrExtTplData['photo']['photo_frs'];
            $arrDestPage['frs_page'] = 16;
        }

        //stock forum
        if (isset($arrExtTplData['Stock']['stock_frs'])) {
            $arrDestPage['cur_page'] = $arrExtTplData['Stock']['stock_frs']['cur_page'] - 1;
            $arrDestPage['total_page'] = $arrExtTplData['Stock']['stock_frs']['total_page'];
            $arrDestPage['frs_page'] = $arrExtTplData['Stock']['stock_frs']['frs_page'];
        }

        $arrDestConf = array(
            'img_num' => 100, //Fixed
            'video_num' => 1, //Fixed
            'smiley_num' => 100, //Fixed
            'white_list' => self::$_arrWhiteList,    //Fixed
            'is_readonly' => 0,   //Fixed
        );
        $arrDestFrsstar = $arrExtTplData['Star']['base']['data'];
        $arrDestSum = array(
            'frsstar' => $arrDestFrsstar,
            'page' => $arrDestPage,
            'conf' => $arrDestConf,
            'frsgcon' => $arrExtTplData['Platform']['gconforum'],
        );

        foreach (self::$_arrOldExtField as $strEach) {
            unset($arrExtTplData[$strEach]);
        }
        $arrDestSum = array_merge($arrDestSum, $arrExtTplData);


        return $arrDestSum;
    }

    private static function _getDestForumex($arrCoreTplData, $arrExtTplData) {
        $arrRet = array(
            'background' => $arrCoreTplData['forum']['background'],
            'version' => $arrCoreTplData['request']['version'],
            'tidinfo' => $arrExtTplData['usermask']['tidinfo'],
            'tb_disk' => array('is_open_disk' => false,),
            'tid_hidden_num' => $arrExtTplData['usermask']['tid_hidden_num'],
            'is_ad' => $arrExtTplData['brandzone']['is_ad'],
            'show_adv_list' => $arrExtTplData['brandzone']['show_adv_list'],
            'vcode_grid' => $arrExtTplData['vcode']['vcode_grid'],
            'zyq' => '',
            'bakan' => $arrExtTplData['bakan'],
            //'protal'                    => array('has_open' => false,), 
            'memberinfo' => $arrCoreTplData['forum']['member'],
            'managers' => $arrCoreTplData['forum']['bawu']['manager'],
            'profession_managers' => $arrCoreTplData['forum']['bawu']['profession_manager'],
            'fourth_managers' => $arrCoreTplData['forum']['bawu']['fourthmanager'],
            'is_assist_num_max' => $arrCoreTplData['forum']['bawu']['is_assist_num_max'],
            'assist_num' => count($arrCoreTplData['forum']['bawu']['assist']),
            'fstyle' => $arrCoreTplData['forum']['style'],
            'duoyi' => $arrCoreTplData['request']['forum']['duoyi'],
            'thread_notice' => isset($arrExtTplData['notice']) ? array($arrExtTplData['notice']) : NULL,
            'avatar' => isset($arrExtTplData['card']['avatar']) ? $arrExtTplData['card']['avatar'] : 'http://imgsrc.baidu.com/forum/abpic/item/fc1844e736d12f2efdbf3c284ec2d56284356830.jpg',
            'slogan' => $arrExtTplData['card']['slogan'],
            'has_postpre' => $arrExtTplData['postprefix']['open'],
            'post_prefix' => $arrExtTplData['postprefix']['prefix'],
            'has_define' => $arrExtTplData['ziyuan']['has_define'],
            'define' => $arrExtTplData['ziyuan']['define'],
            'define_title' => $arrExtTplData['ziyuan']['define_title'],
            'friend' => $arrExtTplData['ziyuan']['friend'],
            'comforum' => $arrExtTplData['Business']['comforum'],
            'is_customize_open' => $arrExtTplData['customize']['open'],
            'customize_logo' => $arrExtTplData['customize']['log'],
            'has_forum_medal' => $arrExtTplData['medal']['has_forum_medal'],
            'forum_medal' => $arrExtTplData['medal']['data'],
            'has_wallet' => $arrExtTplData['Business']['wallet']['has_wallet'],
            'wallet' => $arrExtTplData['Business']['wallet']['data'],
            'is_guanzhu' => true,
            'forum_encourage' => isset($arrCoreTplData['cupid'][285]) ? true : false,
            'album_forum' => $arrExtTplData['photo']['album_forum'],
            // 'has_forum_light'           => false,
            'can_local_upload' => isset($arrCoreTplData['cupid'][255]) ? 1 : 0,
            'has_filepost' => true,
            'has_new_bawu' => isset($arrCoreTplData['cupid'][265]) ? true : false,
            'has_gov_open' => isset($arrCoreTplData['cupid'][275]) ? true : false,
            'album_good_smallflow' => $arrExtTplData['photo']['album_good_smallflow'],
            'album_open_photo_frs' => $arrExtTplData['photo']['album_open_photo_frs'],

            // 'stats' => array (
            //     'newfrs_stats_bigrange' => isset($arrCoreTplData['cupid'][191]) ? 1 : 0,
            // ),
            'ban_pic_topic' => isset($arrCoreTplData['cupid'][226]) ? 1 : 0,
            //'open_forum_value'          => 0,    //°ÉµÈ¼¶¾É(ÏÂ)
            'has_pic_recommend' => isset($arrCoreTplData['cupid'][221]) ? 1 : 0,
            'userinvite_no_open' => isset($arrCoreTplData['cupid'][228]) ? 0 : 1,
            // 'nineyear'                  => array (
            //     'type' => 'god',
            // ),  //¾ÅÖÜÄê(ÏÂ)
            'has_postaudit' => (isset($arrCoreTplData['cupid'][238]) || isset($arrExtTplData['grade']['perm'][106])) ? true : false,
            //'frs_recommend'             => false,   //frs¾«»ªÍÆ¼ö(ÏÂ)
            'timeunit' => $arrExtTplData['Star']['timeunit'],
            // 'ad_big_tower'              => NULL, //ÏÂ
            // 'ad_small_tower'            => NULL,   //ÏÂ
            // 'ad_banner'                 => NULL,    //ÏÂ
            // 'app_tower'                 => '',  //ÏÂ
            // 'leju'                      => 
            //     array (
            //         'error'         => 0,
            //         'info'          => '',
            //         'pic_list'      => '',
            //         'news_list'     => '',
            //         'news_more'     => '',
            //         'extend_list'   => '',
            //     ),  //ÏÂ
            'is_video_v1' => $arrCoreTplData['forum']['type']['is_video'],
            'is_star_frs' => $arrExtTplData['Star']['base']['is_star_frs'],
            'starRegStatus' => $arrExtTplData['Star']['base']['starRegStatus'],
            'is_star_reg' => $arrExtTplData['Star']['base']['is_star_reg'],
            'starRegInfo' => $arrExtTplData['Star']['base']['starRegInfo'],
            'is_star_preview' => $arrExtTplData['Star']['base']['is_star_preview'],
            'star_preview' => $arrExtTplData['Star']['base']['star_preview'],
            'tbgame' => $arrExtTplData['Business']['tbgame'],
            'zhibo' => $arrExtTplData['zhibo'],
            'topinternalad' => $arrExtTplData['difang'],
            'has_paper' => $arrExtTplData['Encourage']['paper']['has_paper'],
        );
        if (isset($arrExtTplData['Business']['zhibai'])) {
            $arrRet['offical'] = $arrExtTplData['Business']['zhibai'];
        }
        if (isset($arrExtTplData['goodalbum'])) {
            $arrRet['entry_frs_discuss_data'] = $arrExtTplData['goodalbum'];
        }
        if (isset($arrExtTplData['customize']['is_customize_pm'])) {
            $arrRet['is_customize_pm'] = $arrExtTplData['customize']['is_customize_pm'];
        }
        if (isset($arrExtTplData['findpeople'])) {
            $arrRet['have_open_xunren'] = $arrExtTplData['findpeople']['have_open_xunren'];
            $arrRet['yaan'] = $arrExtTplData['findpeople']['yaan'];
        }
        if (isset($arrExtTplData['superboy'])) {
            $arrRet = array_merge($arrExtTplData['superboy'], $arrRet);
        }
        if ($arrExtTplData['Encourage']['paper']['has_paper'] === 1) {
            $arrRet = array_merge(array('paper' => $arrExtTplData['Encourage']['paper']), $arrRet);
        }
        if (isset($arrExtTplData['Business']['lecai_lottery'])) {
            $arrRet['lecai_lottery'] = $arrExtTplData['Business']['lecai_lottery'];
        }
        if (isset($arrExtTplData['Business']['ssq_lottery'])) {
            $arrRet['ssq_lottery'] = $arrExtTplData['Business']['ssq_lottery'];
        }
        if (isset($arrExtTplData['Business']['lucky_lottery'])) {
            $arrRet['lucky_lottery'] = $arrExtTplData['Business']['lucky_lottery'];
        }
        if (isset($arrRet['managers']) && count($arrRet['managers'])) {
            $arrManagers = array();
            foreach ($arrRet['managers'] as $arrManager) {
                $arrManager['user']['portrait'] =
                    Tieba_Ucrypt::encode($arrManager['user']['user_id'], $arrManager['user']['user_name']);
                $arrManagers[] = $arrManager;
            }
            $arrRet['managers'] = $arrManagers;
        }
        if (isset($arrRet['profession_managers']) && count($arrRet['profession_managers'])) {
            $arrManagers = array();
            foreach ($arrRet['profession_managers'] as $arrManager) {
                $arrManager['user']['portrait'] =
                    Tieba_Ucrypt::encode($arrManager['user']['user_id'], $arrManager['user']['user_name']);
                $arrManagers[] = $arrManager;
            }
            $arrRet['profession_managers'] = $arrManagers;
        }
        return $arrRet;
    }

    private static function _transToOldPerm($arrPerm) {
        $intPower = 0;
        if (isset($arrPerm['can_edit_bakan']) && $arrPerm['can_edit_bakan'] === true) {
            $intPower = $intPower | 0x8;
        }
        if (isset($arrPerm['can_post']) && $arrPerm['can_post'] === true) {
            $intPower = $intPower | 0x1;
        }
        //TODO
        if (isset($arrPerm['can_bws_bawu_info']) && $arrPerm['can_bws_bawu_info'] === true) {
            $intPower = $intPower | 0x2;
        }
        if (isset($arrPerm['can_vote']) && $arrPerm['can_vote'] === true) {
            $intPower = $intPower | 0x4;
        }
        return $intPower;
    }

    private static function _transToIdentity($arrRole = array()) {
        $intIdentity = 0;
        $intIdentity = $intIdentity | (isset($arrRole['is_forum_pm']) && $arrRole['is_forum_pm'] === true ? 0x40 : 0);
        $intIdentity = $intIdentity | (isset($arrRole['is_forum_member']) && $arrRole['is_forum_member'] === true ? 0x1 : 0);
        //$intIdentity = $intIdentity | (isset($arrRole['member_apply']) && $arrRole['member_apply'] === true ? 0x2 : 0);   //TODO
        $intIdentity = $intIdentity | (isset($arrRole['is_forum_manager']) && $arrRole['is_forum_manager'] === true ? 0x80 : 0);
        return $intIdentity;
    }

    private static function _procUserGrade($arrGrade, $bolIsLogin) {
        $arrRet = array(
            'is_firstlike' => 0,
            'has_award' => 0,
            'award_code' => 0,
            'award_inf1o' => '',
            'award_value' => 0,
            'is_lvup' => 0,
            'rights' => '',
            'has_trans_info' => 0,
            'trans_info' => '',
            'has_mall_expire_info' => 0,
            'mall_expire_info' => '',
            'has_liked' => 0,
            'is_like' => 0,
            'level_id' => 1,
            'cur_score' => 0,
            'score_left' => 0,
            'levelup_score' => 0,
            'level_name' => '',
            'picasso' => false,
            'is_black' => 0,
        );
        if ($bolIsLogin) {
            $arrRet['has_liked'] = $arrGrade['base']['is_like'];
            $arrRet['is_liked'] = $arrGrade['base']['is_like'];
            $arrRet['level_id'] = $arrGrade['base']['level_id'];
            $arrRet['cur_score'] = $arrGrade['base']['cur_score'];
            $arrRet['score_left'] = $arrGrade['base']['score_left'];
            $arrRet['levelup_score'] = $arrRet['cur_score'] + $arrRet['score_left'];
            $arrRet['level_name'] = $arrGrade['base']['level_name'];
            if ($arrRet['level_id'] >= self::CAN_PICASSO) {
                $arrRet['picasso'] = true;
            } else {
                $arrRet['picasso'] = false;
            }
            $arrRet['is_black'] = $arrGrade['base']['is_black'];
            $intTidId = $arrGrade['tip']['tip_id'];
            switch ($intTidId) {
                case 1:
                    $arrRet['is_firstlike'] = 1;
                    break;
                case 2:
                    $arrRet['is_lvup'] = 1;
                    $arrRet['rights'] = $arrGrade['tip']['tip_info'];
                    break;
                case 3:
                    $arrRet['has_trans_info'] = 1;
                    $arrRet['trans_info'] = $arrGrade['tip']['tip_info'];
                    break;
                case 4:
                    $arrRet['has_award'] = 1;
                    $arrRet['award_code'] = 33;
                    $arrRet['award_info'] = $arrGrade['tip']['tip_info'];
                    $arrRet['award_value'] = $arrGrade['tip']['extra_exp'];
                    $arrRet['award_reason_title'] = Bingo_String::truncate($arrGrade['tip']['title'], 30, '...', 'GBK');
                    $arrUserLike['award_reason_url'] = "http://tieba.baidu.com/f?kz=" . $arrGrade['tip']['thread_id'];
                    break;
                case 100:
                    $arrUserLike['has_mall_expire_info'] = 1;
                    $arrUserLike['mall_expire_info'] = $arrGrade['tip']['tip_info'];
            }
        }
        return $arrRet;
    }

    private static function _procUserBlock($arrBlock, $bolIsLogin) {
        $arrRet = array(
            'is_by_pm' => 0,
            'is_block' => 0,
            'days_tofree' => 0,
        );
        if ($bolIsLogin) {
            $arrRet['is_block'] = $arrBlock['is_block'];
            if ($arrBlock['block_info']['block_type'] == 4 || $arrBlock['block_info']['block_type'] == 1) {
                $intTime = intval($arrBlock['block_info']['end_time']) - time();
                $intTime = intval($intTime / 86400) + 1;
                $arrRet['days_tofree'] = $intTime;
            }
            $arrRet['is_by_pm'] = $arrBlock['block_info']['is_by_pm'];
            if (isset($arrBlock['block_info']['opgroup']) &&
                strlen($arrBlock['block_info']['opgroup'])
            ) {
                $arrRet['opgroup'] = $arrBlock['block_info']['opgroup'];
            }
        }
        return $arrRet;
    }

    private static function _procPerm($arrPerm, $arrCoreTplData = null) {


        //个人吧只能吧主发帖 begin
        $isLogin = $arrCoreTplData['user']['base']['is_login'];
        $isManager = $arrCoreTplData['user'] ? $arrCoreTplData['user'] : null;

        $canPostFrs = false;
        if ($isLogin && $isManager['role']['is_forum_manager']) {
            $canPostFrs = true;
        }
        //个人吧只能吧主发帖 end
        $arrPerm['can_post_frs'] = $canPostFrs;
        //$arrPerm['can_op_bakan'] = $arrPerm['can_edit_bakan'];
        //$arrPerm['can_op_daquan'] = $arrPerm['can_edit_daquan'];
        //$arrPerm['can_show_bawu'] = $arrPerm['can_bws_bawu_info'];  //Check TODO
        $arrPerm['can_show_bawu'] = $canPostFrs;

        return $arrPerm;
    }

    private static function _procRole($arrRole) {
        $arrRole['manager'] = $arrRole['is_forum_manager'];
        $arrRole['member'] = $arrRole['is_forum_member'];
        $arrRole['pm'] = $arrRole['is_forum_pm'];
        return $arrRole;
    }

    protected static function _getBlockReason($arrParam) {
        $res = Tieba_Service::call('anti', 'antiGetBlockReason', $arrParam, NULL, NULL, 'post', 'php', 'utf-8');
        if (false === $res || Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            Bingo_Log::warning("anti::antiGetBlockReason call fails");
            return '';
        }
        return $res['res']['remarks'];
    }

}
