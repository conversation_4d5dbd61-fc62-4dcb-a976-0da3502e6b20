<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-02-24 17:39
 * @version
 */

class Util_Session {

    private static $_arr_session = null;

    public static function getSession(){
        if(self::$_arr_session){
            return true;
        }

        self::$_arr_session['bolLogin']  = (boolean)Tieba_Session_Socket::isLogin();
        self::$_arr_session['intUid']    = intval(Tieba_Session_Socket::getLoginUid());
        self::$_arr_session['strUname']  = strval(Tieba_Session_Socket::getLoginUname());
        self::$_arr_session['intUip']    = intval(Bingo_Http_Ip::ip2long(Bingo_Http_Ip::getConnectIp()));
        self::$_arr_session['bolNoname'] = (boolean)Tieba_Session_Socket::getNo_un();
        self::$_arr_session['strMobile'] = strval(Tieba_Session_Socket::getMobilephone());
        self::$_arr_session['strEmail']  = strval(Tieba_Session_Socket::getEmail());
        self::$_arr_session['strBaiduid'] = trim(Tieba_Session_Socket::getBaiduid(),'"');


 //       self::$_arr_session['intUid'] = 1436810965; //todo zhaoshiya  to be deleted
 //       self::$_arr_session['strUname'] = '拾光漫步V'; // todo zhaoshiya  to be deleted

        if( empty(self::$_arr_session) ){
            Bingo_Log::warning("get session fail");
            return false;
        }
        return true;
    }

    public static function get($key){
        $value = isset(self::$_arr_session[$key]) ? self::$_arr_session[$key] : false;
        return $value;
    }
}
