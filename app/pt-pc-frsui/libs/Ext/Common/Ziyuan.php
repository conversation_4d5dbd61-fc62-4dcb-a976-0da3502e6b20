<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/


/**
 * @file Ziyuan.php
 * <AUTHOR>
 * @date 2013/08/25 18:12:49
 * @brief 
 *  
 **/

class Ext_Common_Ziyuan implements Util_MultiBase {

    const MODULE_ID_DEFINE_LINK = 1002;
    const MODULE_NAME_DEFINE_LINK = 'customlink_p1';

    const ZYQ_FRIEND  = 145;
    const ZYQ_FRIEND_NAME  = 'zyqfriend';

    const ZYQ_DEFINE  = 146;
    const ZYQ_DEFINE_NAME  = 'zyqdefine';

    const ZYQ_TITLE   = 147;
    const ZYQ_TITLE_NAME   = 'zyqtitle';

    // 友情贴吧添加头像
    //const CACHE_PRE = 'ziyuan_';      // cache prefix
    const CACHE_PRE = 'ziyuan_utf8_';
    const CACHE_EXPIRE_TIME = 3600;  // default expire time

    const DEFAULT_FRIEND_AVATAR = 'http://m.tiebaimg.com/timg?wapp&quality=80&size=b150_150&subsize=20480&cut_x=0&cut_w=0&cut_y=0&cut_h=0&sec=1369815402&srctrace&di=041f99acc41ef943ddd5096f9d0f42ae&wh_rate=null&src=http%3A%2F%2Fimgsrc.baidu.com%2Fforum%2Fabpic%2Fitem%2Fbc338f3df8dcd1003c3d6b69738b4710bb122fcd.jpg';

    private static $_arrZiyuan = array();

    /**
        * @brief 
        *
        * @param $objCoreData
        *
        * @return 
     */
    public static function getMultiInput($objCoreData) {
        $arrRet = array();
        $arrForumBase = $objCoreData->getForum('base');
        $arrForumStyle = $objCoreData->getForum('style');
        $bolOpenDefineLink = isset($arrForumStyle[self::MODULE_NAME_DEFINE_LINK]) ? true : false;
        self::$_arrZiyuan['has_define'] = $bolOpenDefineLink;

        if(true === $bolOpenDefineLink) {
            if(isset($arrForumStyle[self::ZYQ_DEFINE_NAME])) {
                self::$_arrZiyuan['define'] = $arrForumStyle[self::ZYQ_DEFINE_NAME];
            }
        }
        if(isset($arrForumStyle[self::ZYQ_TITLE_NAME])) {
            self::$_arrZiyuan['define_title'] = $arrForumStyle[self::ZYQ_TITLE_NAME];

        }

        if(isset($arrForumStyle[self::ZYQ_FRIEND_NAME])) {
            // build multi input
            $arrRet['call'][0] = array(
                'service_name' => 'forum',
                'method' => 'getFidByFname',
                'input' => array(),
            );
            foreach($arrForumStyle[self::ZYQ_FRIEND_NAME] as $strFname) {
                $arrRet['call'][0]['input']['query_words'][$strFname] = $strFname;
            };
        } else {
            $arrRet['out']['ziyuan']['tpl_var'] = self::$_arrZiyuan;
        }
        return $arrRet;
    }

    /**
        * @brief 
        *
        * @param $objCoreData
        * @param $arrMultiOut
        *
        * @return 
     */
    public static function execute($objCoreData, $arrMultiOut) {
        $arrRet = array();
        if (false === $arrMultiOut[0] || 0 !== $arrMultiOut[0]['errno']) {
            Bingo_Log::warning("fail to get ziyuan data!");
            return false;
        }
        $arrGetBtxInput = array();
        foreach($arrMultiOut[0]['forum_id'] as $v) {
            $is_forbidden = (int)$v['is_forbidden'];
            $is_exist = (int)$v['is_exist'];
            if (0 === $is_forbidden && 0 < $is_exist) {
                $forum_id = $v['forum_id'];
                $arrGetBtxInput['forum_id'][$forum_id] = $forum_id;
            } else {
                continue;
            }
        }
        Bingo_Timer::start("Ziyuan_getBtxInfo");
        $arrGetBtxOutput = Tieba_Service::call("forum", 'mgetBtxInfo', $arrGetBtxInput, null, null, 'post', 'php', 'utf-8');
        Bingo_Timer::end("Ziyuan_getBtxInfo");
        if(false === $arrGetBtxOutput || 0 !== $arrGetBtxOutput['errno']) {
            Bingo_Log::warning("fail to get ziyuan data, call mgetBtxInfo");
            return false;
        }
        foreach($arrGetBtxOutput['output'] as $forum_id => $v) {
            $forum_name = $v['forum_name']['forum_name'];
            $avatar = isset($v['card']['avatar']) ? $v['card']['avatar'] : self::DEFAULT_FRIEND_AVATAR;
            self::$_arrZiyuan['friend'][] = array(
                'forum_name' => $forum_name,
                'avatar' => $avatar,
            );
        }
        $arrRet['ziyuan']['tpl_var'] = self::$_arrZiyuan;
        return $arrRet;
    }

    /*
    public static function execute($objCoreData, $arrMultiOut) {
        $arrForumBase = $objCoreData->getForum('base');
        $arrForumStyle = $objCoreData->getForum('style');
        $arrNzyq = array();
        $bolOpenDefineLink = isset($arrForumStyle[self::MODULE_NAME_DEFINE_LINK]) ? true : false;
        $arrNzyq['has_define'] = $bolOpenDefineLink;
        if(isset($arrForumStyle[self::ZYQ_FRIEND_NAME]))
        {

            //$arrNzyq['friend'] = $arrForumStyle[self::ZYQ_FRIEND_NAME];
            $arrNzyq['friend'] = self::_buildFriend($objCoreData,$arrForumStyle[self::ZYQ_FRIEND_NAME]);
        }

        if(isset($arrForumStyle[self::ZYQ_TITLE_NAME]))
        {

            $arrNzyq['define_title'] = $arrForumStyle[self::ZYQ_TITLE_NAME];

        }
     */
  /*      if(Tieba_Errcode::ERR_SUCCESS == $arrMultiOut['errno']){
            if(true === $bolOpenDefineLink) {
                if(isset($arrMultiOut['data']['define'])) {
                    $arrNzyq['define'] = $arrMultiOut['data']['define'];
                }
            }
            if(isset($arrMultiOut['data']['define_title'])) {
                $arrNzyq['define_title'] = $arrMultiOut['data']['define_title'];
            }
        }

        if(isset($arrMultiOut['data']['friend'])) {
            $arrNzyq['friend'] = $arrMultiOut['data']['friend'];
        }
   */
    /*
        $arrRet['ziyuan']['tpl_var'] = $arrNzyq;

        //var_export($arrRet);
        return $arrRet;
    }
     */

    protected static function _getCacheKey($strForumName){
        if (!strlen($strForumName)){
            return false;
        }

        return self::CACHE_PRE.$strForumName;
    }

    protected static function _buildFriend($objCoreData,$arrFriends){
        $arrRet = array();
        foreach ($arrFriends as $strForumName){
            // set default value 
            $arrNew  = array(
                'forum_name'  => $strForumName,
                'avatar'      =>
                'http://gss3.bdstatic.com/84oSdTum2Q5BphGlnYG/timg?wapp&quality=80&size=b150_150&subsize=20480&cut_x=0&cut_w=0&cut_y=0&cut_h=0&sec=1369815402&srctrace&di=041f99acc41ef943ddd5096f9d0f42ae&wh_rate=null&src=http%3A%2F%2Fimgsrc.baidu.com%2Fforum%2Fabpic%2Fitem%2Fbc338f3df8dcd1003c3d6b69738b4710bb122fcd.jpg',
            );      

            // get value from cache 
            $strKey = self::_getCacheKey($strForumName);
            if (false !== $strKey){
                $strData = $objCoreData->getCache($strKey);
                $arrData = unserialize($strData);
                if (!empty($arrData)){
                    $arrNew = $arrData;
                    $arrRet[] = $arrNew;
                    continue;
                }
            }

            // not hit cache, get value using Tieba_Service::call
            $arrParam = array(
                'forum_name'   => $strForumName,
            );      
            $arrOut = Tieba_Service::call('forum','getBtxInfoByName',$arrParam, NULL, NULL, 'post', 'php', 'utf-8');
            if (false === $arrOut || 0 === (int)$arrOut['forum_id']['forum_id']) {
                //filter the forbidden forum
                continue;
            }

            if (isset($arrOut['errno']) && Tieba_Errcode::ERR_SUCCESS === $arrOut['errno']){
                if (isset($arrOut['card']['avatar']) && strlen($arrOut['card']['avatar'])){
                    $arrNew['avatar'] = $arrOut['card']['avatar'];
                }
            }       
            $arrRet[] = $arrNew;

            // add cache 
            if (false !== $strKey){
                $strData = serialize($arrNew);
                $objCoreData->setCache($strKey,$strData,self::CACHE_EXPIRE_TIME);
            }
        }       

        return $arrRet;
    }

    public static function getCacheKey($objCoreData) {
        //��Դ��û�е��ýӿڣ�����Ҫʹ��cache,����������ӳ����ӳ�bug
        return false;
        /*$arrForumBase = $objCoreData->getForum('base');
        $strKey = $arrForumBase['forum_id'];
        return $strKey;*/
    }  
}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
