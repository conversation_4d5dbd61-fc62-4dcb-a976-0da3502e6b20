<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Notice.php
 * <AUTHOR>
 * @date 2013/08/22 22:30:49
 * @brief 
 *  
 **/

class Ext_Common_Notice implements Util_CommonBase {

    const PC_NOTICE_MODULE_ID = 132;
    const PC_NOTICE_MODULE_NAME = "noticepc_p1";
    
    const MO_NOTICE_MODULE_ID = 133;
    const MO_NOTICE_MODULE_NAME = 'noticemo_p1';
	
    public static function execute($objCoreData) {

        $arrNotice = array();

        $arrForumStyle = $objCoreData->getForum('style');
        $arrDirStyle   = $objCoreData->getForum('dir_style');
        $strType       = 'pc';

        // get notice for forum
        $arrForumNotice = self::getNoticeFromForumStyle($arrForumStyle, $strType);
        if(self::checkStatus($arrForumNotice)) {
            $arrNotice[] = $arrForumNotice;
        }

        // get notice for dir1/dir2/no_dir/global
        $arrDirNotice = self::getNoticeFromDirStyle($arrDirStyle, $strType);
        foreach($arrDirNotice as $arrEachNotice) {
            if(self::checkStatus($arrEachNotice)) {
                $arrNotice[] = $arrEachNotice;
            }
        }

        $arrRetNotice  = array();
        $intNewestTime = 0;
        foreach($arrNotice as $arrEachNotice) {
            if(intval($arrEachNotice['online_time']) > $intNewestTime) {
                $intNewestTime = intval($arrEachNotice['online_time']);
                $arrRetNotice  = $arrEachNotice;
            }
        }
        
        if(empty($arrRetNotice)) {
            return null;
        }

        $arrRet['notice']['tpl_var'] = array(
                'title'      => $arrRetNotice['title'],
                'title_link' => $arrRetNotice['link'],
                'author'     => '���ɹ���',
                'id'         => 0,
        );
        //var_export($arrRet);
        //$arrRet = Bingo_Encode::convert($arrRet,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
        return $arrRet;
    }

    private static function getNoticeFromForumStyle($arrForumStyle, $strType = 'pc') {
        $arrNotice = array();
        if(isset($arrForumStyle[self::typeName($strType)])) {  
            $arrNoticeStyle = $arrForumStyle[self::typeName($strType)];
			$arrNoticeStyle = Bingo_Encode::convert($arrNoticeStyle,Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
            $arrForumNotice = unserialize(strval($arrNoticeStyle['style_name']));
            if($arrForumNotice === false) {
                Bingo_Log::warning('serialized string conversion error.');
            }else{
				$arrForumNotice = Bingo_Encode::convert($arrForumNotice,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
			}
            $arrNotice = $arrForumNotice;
        }
        return $arrNotice;
    }

    private static function getNoticeFromDirStyle($arrDirStyle, $strType = 'pc') {
        $arrNotice = array();
        if(isset($arrDirStyle[self::typeId($strType)])) {
            $arrNoticeStyle = $arrDirStyle[self::typeId($strType)];
			$arrNoticeStyle = Bingo_Encode::convert($arrNoticeStyle,Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
            foreach($arrNoticeStyle as $arrStyle) { 
                $arrEachNotice = unserialize(strval($arrStyle['style_name']));
                if($arrEachNotice === false) {
                    Bingo_Log::warning('serialize string conversion error.');
                }else{
					$arrEachNotice = Bingo_Encode::convert($arrEachNotice,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
				}
                $arrNotice[] = $arrEachNotice;
            }
        }
        return $arrNotice;
    }

    private static function checkStatus($arrNotice = array()) {
        if(empty($arrNotice)) {
            return false;
        }
        // all time is UNIX Timestamp
        // current time
        $intCurTime = time();

        // online time and offline time
        $intOnlineTime  = intval($arrNotice['online_time']);
        $intOfflineTime = intval($arrNotice['offline_time']);
        
        // if online time is later than offline time, then false
        if($intOnlineTime > $intOfflineTime) {
            Bingo_Log::warning('online time should be earlier than offline time');
            return false;
        }

        // current time is not between online time and offline time, this notice is either not online or already offline
        if($intCurTime < $intOnlineTime || $intCurTime > $intOfflineTime) {
            return false;
        }
        return true;
    }

    private static function typeId($strType = 'pc') {
        if($strType === 'pc') {
            return self::PC_NOTICE_MODULE_ID;
        }
        if($strType === 'mo') {
            return self::MO_NOTICE_MODULE_ID;
        }
    }
    
	private static function typeName($strType = 'pc') {
        if($strType === 'pc') {
            return self::PC_NOTICE_MODULE_NAME;
        }
        if($strType === 'mo') {
            return self::MO_NOTICE_MODULE_NAME;
        }
    }
    
    
    public static function getCacheKey($objCoreData) {
        $arrForumBase = $objCoreData->getForum('base');
        $strKey = $arrForumBase['forum_id'];
        return $strKey;
    }  
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
