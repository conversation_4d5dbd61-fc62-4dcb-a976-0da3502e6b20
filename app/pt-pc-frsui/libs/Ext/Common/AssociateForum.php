<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file AssociateForum.php
 * <AUTHOR>
 * @date 2013/09/04 20:54:24
 * @brief 
 *  
 **/

class Ext_Common_AssociateForum implements Util_MultiBase {

    private static $_arrContent     = array();
    private static $_arrOfficialAttr=array();
    private static $_intType        = 0;
    private static $_intOfficialId  = 0;
    private static $_strOfficialName= 0;
    private static $_arrContentFlag = array();

    const FORUM_ASSOCIATE_ATTR      = 'associate_forum';
    const FORUM_STYLE_STAR_OFFICIAL = 'official';
    const STYLE_STAR                = 1;
    const ACTIVITY_NUM              = 5;
    const SCHEDULE_NUM              = 5;
    const NEWS_NUM                  = 5;
    const TODAY_NUM                 = 4;
    const TYPE_ACTIVITY             = 1;
    const TYPE_SCHEDULE             = 2;
    const TYPE_OTHER                = 3;
    const DEFAULT_PIC_URL           = 'http://tb1.bdstatic.com/tb/star_platform_info_default.jpg';

    public static function getMultiInput($objCoreData) {
        $arrRet       = array();
        $arrForumBase = $objCoreData->getForum('base');
        $arrStyle     = $objCoreData->getForum('style');
        if (isset($arrStyle[self::FORUM_ASSOCIATE_ATTR])){
            self::$_intType         = $arrStyle[self::FORUM_ASSOCIATE_ATTR]['type'];
            self::$_intOfficialId   = $arrStyle[self::FORUM_ASSOCIATE_ATTR]['forum_id'];
            self::$_strOfficialName = $arrStyle[self::FORUM_ASSOCIATE_ATTR]['forum_name'];
            self::$_arrContentFlag  = $arrStyle[self::FORUM_ASSOCIATE_ATTR]['associate_content'];
            
            $arrMultiInput = array();

            // 明星相关配置
            if (self::$_intType == self::STYLE_STAR){
                $arrRet = self::_getStarMultiInput(self::$_intOfficialId,$arrMultiInput);
                //Bingo_Log::warning(print_r($arrRet,true));
                $arrMultiInput = array_merge($arrMultiInput,$arrRet);
            }

            // 通用配置
            $arrMultiInput = self::_getCommonMultiInput(self::$_intOfficialId,$arrMultiInput);
            //Bingo_Log::warning(print_r($arrMultiInput,true));

            return $arrMultiInput;
        }

        return $arrMultiInput;
    }

   

    public static function execute($objCoreData, $arrMultiOut) {
        //Bingo_Log::warning(print_r($arrMultiOut,true));
        if (self::$_intType == self::STYLE_STAR){
            self::_starExecute($arrMultiOut);
        }

        $arrOut = $arrMultiOut[4];
        //Bingo_Log::warning(print_r($arrOut,true));
        if (isset($arrOut['errno']) && Tieba_Errcode::ERR_SUCCESS === $arrOut['errno']){
            $arrData['base']               = $arrOut['data']['base_info']['material'];
            $arrData['base']['star_name']  = self::$_strOfficialName;
            $arrData['ext']                = $arrOut['data']['style'];
            self::$_arrOfficialAttr        = $arrData;
            self::$_arrContent['official'] = $arrData;
            //Bingo_Log::warning(print_r(self::$_arrContent['official'],true));
        }
        return self::_buildTpl($objCoreData);
    }

    /*
    * @desc 通用配置放在这里
    */
    protected static function _getCommonMultiInput($intForumId,$arrMultiInput){
        // 获取官方吧的吧属性
        $arrInput = array(
                        'forum_id'     => self::$_intOfficialId,
                        );
        $arrMultiInput['call']['official'] = array(
                                                        'service_name'     => 'official',
                                                        'method'           => 'getOfficialStyle',
                                                        'input'            => $arrInput,
                                                        );
       return $arrMultiInput; 
    }
    
    protected static function _buildTpl($objCoreData){
        $arrRet = array();
        if (self::$_intType == self::STYLE_STAR){
            $ret = self::_buildStarTpl($objCoreData);
            $arrRet = array_merge($arrRet,$ret);
        }

        $arrRet['official'] = self::$_arrOfficialAttr;
        return $arrRet;
    }

    public static function getCacheKey($objCoreData) {
        return false;
    }  

    // ********************************************************
    // 明星相关的处理

    /*
    * @desc 明星需要用到的service调用
    */
    protected static function _getStarMultiInput($intForumId,$arrMultiInput){
        // 配置新闻
        //if (isset(self::$_arrContentFlag['star_news']) 
        //    && self::$_arrContentFlag['star_news']){
            $arrInput = array(
                            'official_id'    => $intForumId,
                            );
            $arrMultiInput['call']['star_news'] = array(
                                            'service_name'    => 'star',
                                            'method'          => 'getNews',
                                            'input'           => $arrInput,
                                            );
        //}

        // 配置官方活动
        //if (isset(self::$_arrContentFlag['star_activity'])
         //   && self::$_arrContentFlag['star_activity']){
            /*
            $arrInput = array(
                            'forum_id'         => $intForumId,
                            'pn'               => 0,
                            'rn'               => self::ACTIVITY_NUM,
                            'op_type'          => 1,
                            );
            $arrMultiInput['call']['star_activity'] = array(
                                            'service_name'     => 'actstage',
                                            'method'           => 'bwActivityById',
                                            'input'            => $arrInput,
                                            );
             */

        //}

        // 配置官方日程
        //if (isset(self::$_arrContentFlag['star_daily'])
         //   && self::$_arrContentFlag['star_daily']){
            $arrInput = array(
                            'official_id'        => $intForumId,
                            'pn'                 => 1,
                            'sz'                 => self::SCHEDULE_NUM,
                            );
            $arrMultiInput['call']['star_daily_1'] = array(
                                            'service_name'   => 'star',
                                            'method'         => 'getLatestSchedule',
                                            'input'          => $arrInput,
                                            );
            $arrInput = array(
                            'official_id'        => $intForumId,
                            'day'                => date('Ymd',time()),
                            );
            $arrMultiInput['call']['star_daily_2'] = array(
                                            'service_name'    => 'star',
                                            'method'          => 'getDaySchedule',
                                            'input'           => $arrInput,
                                            );
        //}

        return $arrMultiInput;

    }

    /*
    * @desc 明星service调用后的后续处理
    */
    protected static function _starExecute($arrMultiOut){

        //if (self::$_arrContentFlag['star_news']){
            $arrOut = $arrMultiOut[0];
            if (isset($arrOut['errno']) && Tieba_Errcode::ERR_SUCCESS === $arrOut['errno']){
                self::$_arrContent['star_news'] = array_slice($arrOut['data'],0,self::NEWS_NUM);
                foreach(self::$_arrContent['star_news'] as $key => $arrItem) {
                    self::$_arrContent['star_news'][$key]['title'] = Bingo_Encode::convert($arrItem['title'], Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
                    self::$_arrContent['star_news'][$key]['desc'] = Bingo_Encode::convert($arrItem['desc'], Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
                    self::$_arrContent['star_news'][$key]['good_class'] = Bingo_Encode::convert($arrItem['good_class'], Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
                }
            }
        //}

        //if (self::$_arrContentFlag['star_activity']){
            /*
            $arrOut = $arrMultiOut[1];
            if (isset($arrOut['errno']) && Tieba_Errcode::ERR_SUCCESS === $arrOut['errno']){
                foreach ($arrOut['data']['list'] as $arrItem){
                    if ($arrItem['act_info']['status'] == 1){
                        self::$_arrContent['star_activity'][] = $arrItem['act_info'];
                    }
                }
            }
             */
        //}

        //if (self::$_arrContentFlag['star_daily']){
            $arrOut = $arrMultiOut[1];
            if (isset($arrOut['errno']) && Tieba_Errcode::ERR_SUCCESS === $arrOut['errno']){
                self::$_arrContent['star_daily'] = $arrOut['data']['schedule_list'];
            }

            $arrOut = $arrMultiOut[2];
            if (isset($arrOut['errno']) && Tieba_Errcode::ERR_SUCCESS === $arrOut['errno']){
                self::$_arrContent['star_daily_2'] = $arrOut['data'];
            }
        //}

        self::_getToday();
    }

    /*
    * @desc 明星今日，包括：行程（今天发布的和今天的行程）、新闻、staff日志、留言、发布活动
    */
    protected static function _getToday(){
        $arrOut = array();
        $intStartTime = strtotime(date('Ymd',time()));
        $intEndTime   = $intStartTime + 86400;
        //Bingo_Log::warning(print_r($intStartTime,true));
        //Bingo_Log::warning(print_r($intEndTime,true));
        // 今日新闻类消息有新更新
        if (!empty(self::$_arrContent['star_news'])){
            foreach (self::$_arrContent['star_news'] as $arrItem){
                if ($arrItem['create_time'] >= $intStartTime 
                    && $arrItem['create_time'] < $intEndTime){
                    $arrNew = array(
                                    'official_id'    => self::$_intOfficialId,
                                    'official_name'  => self::$_strOfficialName,
                                    'thread_id'      => $arrItem['thread_id'],
                                    'title'          => $arrItem['title'],
                                    'content'        => $arrItem['desc'],
                                    'start_time'     => 0,
                                    'end_time'       => 0,
                                    'type'           => $arrItem['type'],
                                    'status'         => 0,
                                    'pic_url'        => empty($arrItem['pic_url']) ? self::DEFAULT_PIC_URL : $arrItem['pic_url'],
                                    'info_type'      => self::TYPE_OTHER,
                                    'create_time'    => $arrItem['create_time'],
                                    );
                    $arrOut[] = $arrNew; 
                }
            }
        }

        //Bingo_Log::warning(print_r(self::$_arrContent['star_activity'],true));

        if (!empty(self::$_arrContent['star_activity'])){
            foreach (self::$_arrContent['star_activity'] as $arrItem){
                if (true || ($arrItem['begin_time'] <= $intStartTime 
                    && $arrItem['end_time'] > $intEndTime) ){
                        $arrNew   = array(
                                        'official_id'    => $arrItem['forum_id'],
                                        'official_name'  => self::$_strOfficialName,
                                        'thread_id'      => $arrItem['thread_id'],
                                        'title'          => $arrItem['activity_title'],
                                        'content'        => $arrItem['activity_desc'],
                                        'start_time'     => $arrItem['begin_time'],
                                        'end_time'       => $arrItem['end_time'],
                                        'type'           => $arrItem['activity_type'],
                                        'status'         => 0,
                                        'pic_url'        => empty($arrItem['activity_icon']) ? self::DEFAULT_PIC_URL : $arrItem['activity_icon'],
                                        //'pic_url'        => $arrItem['activity_icon'],
                                        'info_type'      => self::TYPE_ACTIVITY,
                                        'create_time'    => $arrItem['create_time'],
                                        );
                        $arrOut[] = $arrNew;
                }
            }
        }

        $arrTmp = array();
        $arrIds = array();
        if (!empty(self::$_arrContent['star_daily_2'])){
           //$arrTmp = array_merge($arrTmp,self::$_arrContent['star_daily_2']);
            foreach (self::$_arrContent['star_daily_2'] as $arrItem){
                $arrTmp[] = $arrItem;
                $arrIds[$arrItem['id']] = true;
            }
        }

        if (!empty(self::$_arrContent['star_daily'])){
            foreach (self::$_arrContent['star_daily'] as $arrItem){
                if (isset($arrIds[$arrItem['id']])){
                    continue;
                }
                if ($arrItem['create_time'] >= $intStartTime 
                    && $arrItem['create_time'] < $intEndTime){
                        $arrTmp[] = $arrItem;
                }
            }
        }
    
        //Bingo_Log::warning(print_r($arrTmp,true));
        if (!empty($arrTmp)){
            foreach ($arrTmp as $arrItem){
                $arrNew = array(
                                'official_id'    => $arrItem['official_id'],
                                'official_name'  => self::$_strOfficialName,
                                'thread_id'      => 0,
                                'title'          => $arrItem['title'],
                                'content'        => $arrItem['content'],
                                'start_time'     => $arrItem['start_time'],
                                'end_time'       => 0,
                                'type'           => $arrItem['type'],
                                'status'         => $arrItem['status'],
                                'pic_url'        => empty($arrItem['pic_url']) ? self::DEFAULT_PIC_URL : $arrItem['pic_url'],
                                //'pic_url'        => null,
                                'pic_url'        => null,
                                'info_type'      => self::TYPE_SCHEDULE,
                                'create_time'    => $arrItem['create_time'],
                                );
                $arrOut[] = $arrNew;
            }
        }
        usort($arrOut,'Ext_Common_AssociateForum::sortTodayItem');

        $arrOut = array_slice($arrOut,0,self::TODAY_NUM);

        //Bingo_Log::warning(print_r($arrOut,true));
        self::$_arrContent['star_today'] = $arrOut;
    }

    /*
    * @desc 将明星今日的数据按照时间排序
    */
    public static function sortTodayItem($arrSort1,$arrSort2){
        if ($arrSort1['create_time'] == $arrSort2['create_time']) {
            return 0;
        }
        return $arrSort1['create_time'] < $arrSort2['create_time'] ? 1 : -1;
    }

    /*
    * @desc 明星模板返回
    */
    protected static function _buildStarTpl($objCoreData){
        $arrFans = array(
                        'name'    => self::$_strOfficialName,
                        'pic'     => self::$_arrOfficialAttr['base']['member_pic'],
                        );
        $arrRet['associate_content']['tpl_var'] = array(
                                                    'type'         => self::$_intType,
                                                    'official'     => self::$_arrContent['official'],
                                                    'star_fans'    => $arrFans,
                                                    'star_daily'   => self::$_arrContent['star_daily'],
                                                    'star_news'    => self::$_arrContent['star_news'],
                                                    'star_activity'=> self::$_arrContent['star_activity'],
                                                    'star_today'   => self::$_arrContent['star_today'],
                                                    );
        $arrRet['associate_content']['tpl_var']['with_today'] =
        empty(self::$_arrContent['star_today']) ? false : true;

        //Bingo_Log::warning(print_r($arrRet,true));
        return $arrRet;

    }
}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
