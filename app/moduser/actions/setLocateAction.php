<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-08
 * @version
 */
class setLocateAction extends Difang_Action_Base {
    protected $bolNeedUserLogin = true;
    protected $bolCheckUserLogin = true;

    private $_intUserId = 0;
    private $_intSwitch = 0;

    protected function _input() {
        // 0开启 1关闭
        $this->_intUserId = intval(Bingo_Http_Request::get('user_id', 0));
        $this->_intSwitch = intval(Bingo_Http_Request::get('switch', 0));
        return true;
    }

    protected function _process() {
        if ($this->_intUserId < 1) {
            $this->intUserId = CapiRequest::$intUid;
        }
        if($this->_intUserId < 1) {
            $this->_error(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }

        $arrRequest = array(
            'user_id' => $this->_intUserId,
            'stat' => $this->_intSwitch,
        );
        $ret = Tieba_Service::call('difang', 'setLocateStatByUid', $arrRequest);
        if($ret==false || $ret['errno']!=0) {
            Bingo_Log::warning('setLocateAction call difang::setLocateStatByUid fail, ret:' . serialize($ret));
            $this->_error(Tieba_Errcode::ERR_RPC_CALL_FAIL);
            return false;
        }
        return true;
    }

    protected function _build() {
        return true;
    }
}
