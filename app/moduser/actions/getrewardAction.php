<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-07-04
 * @获取用户互动奖励列表
 */
class getrewardAction extends Difang_Action_Base {
    private $_intUid = 0;
    private $_arrRewards = array();
    private $_intUserCount = 0;                //献花人数
    private $_intFlowerCount = 0;            //献花总数

    protected function _input() {
        $this->_intUid = intval(Bingo_Http_Request::get('user_id', 0));
        if($this->_intUid < 1) {
            $this->_error(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        $this->arrInput = array(
            'user_id' => $this->_intUid,
        );
        return true;
    }

    protected function _process() {
        $ret = Tieba_Service::call('difang', 'getReward', $this->arrInput);
        if($ret === false) {
            Bingo_Log::warning('getrewardAction call difang::getReward return false');
            $this->_error(Tieba_Errcode::ERR_RPC_CALL_FAIL);
            return false;
        } else if($ret['errno'] !== 0) {
            Bingo_Log::warning('getrewardAction call difang::getReward fail, ret:' . serialize($ret));
            $this->_error(intval($ret['errno']));
            return false;
        }

        $this->_arrRewards = $ret['rewards'];
        $this->_intUserCount = intval($ret['user_count']);
        $this->_intFlowerCount = intval($ret['flower_count']);

        return true;
    }

    protected function _build() {
        $arrRewards = array();
        foreach($this->_arrRewards as $reward) {
            $intUid = $reward['user_id'];
            $strUname = $reward['user_name'];
            $strPortrait = Tieba_Ucrypt::encode($intUid, $strUname);
            $arrRewards[] = array(
                'user' => array(
                    'id' => $intUid,
                    'name' => $strUname,
                    'portrait' => $strPortrait,
                ),
                'flower_count' => $reward['flower_count'],
            );
        }
        $this->arrData['reward_list'] = $arrRewards;
        $this->arrData['user_count'] = $this->_intUserCount;
        $this->arrData['flower_count'] = $this->_intFlowerCount;
    }
}
?>
