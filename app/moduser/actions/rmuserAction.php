<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-03-29 13:23:26
 * @version 1.0
 */
class rmuserAction extends Difang_Action_Base {
	protected $bolNeedUserLogin = true;
	protected $bolCheckUserLogin = true;
	protected $bolNeedCheckSign = false;
	protected $bolNeedCheckNoUn = false; 
	protected $bolIsPv = 0;

	protected function _check() {
		//if (! Bingo_Http_Request::isPost()) {
		//	$this->_error(CapiErrno::METHOD_MUST_POST, CapiError::METHOD_MUST_POST);
		//	return false;
		//}
		return parent::_check();
	}

    protected function _input() {
		$this->arrInput['op_user_id'] = CapiRequest::$intUid;
		$this->arrInput['user_id'] = intval(Bingo_Http_Request::get('uid',0));
		$this->arrInput['group_id'] = intval(Bingo_Http_Request::get('gid',0));
		//var_dump($this->arrInput);
        return true;
    }

    protected function _process() {
        //// add group
        $ret = Tieba_Service::call('difang', 'rmUserGroup', $this->arrInput);
        if($ret==false || $ret['errno']!=0) {
			$this->_error($ret['errno']);
            return false;
        }
		//$arrGroup = $ret['group_list'];

        return true;
    }

    protected function _build() {
        return true;
    }
}
?>
