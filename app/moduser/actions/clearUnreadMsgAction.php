<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-07-03
 * @增加用户互动奖励
 */
class clearUnreadMsgAction extends Difang_Action_Base {
    protected $bolNeedUserLogin = true;
    protected $bolCheckUserLogin = true;

    protected function _input() {
        return true;
    }

    protected function _process() {
        $ret = Tieba_Service::call('difang', 'clearUnreadMsg', array(
			'user_id' => CapiRequest::$intUid,
		));
        if($ret === false) {
            Bingo_Log::warning('call difang::clearUnreadMsg return false');
            $this->_error(Tieba_Errcode::ERR_RPC_CALL_FAIL);
            return false;
        } else if($ret['errno'] !== 0) {
            Bingo_Log::warning('call difang::clearUnreadMsg fail, ret:' . serialize($ret));
            $this->_error(intval($ret['errno']));
            return false;
        }

        return true;
    }
}
?>
