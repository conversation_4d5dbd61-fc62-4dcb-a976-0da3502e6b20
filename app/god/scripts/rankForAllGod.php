<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file rankForAllGod.php
 * <AUTHOR>
 * @date 2017/7/14 16:59:59
 * @brie 全吧大神积分排行榜，积分：3个月内获得的回复数
 **/

ini_set ( "memory_limit", "-1" );
define('MODULE_NAME', 'god');
date_default_timezone_set ( "Asia/Chongqing" );
// 定义相关路径信息
define ( 'APP_NAME', 'god' );
define ( 'SCRIPT_NAME', 'rankForAllGod' );
define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../..' );
define ( 'SCRIPT_ROOT_PATH', ROOT_PATH . '/app/' . APP_NAME . '/script' );
define ( 'SCRIPT_LOG_PATH', ROOT_PATH . '/log/' . APP_NAME . '/script' );
define ( 'SCRIPT_CONF_PATH', ROOT_PATH . '/conf/app/' . APP_NAME );

// 自动加载
/**
 * @param $arrThreadInfo
 * @return
 */
function __autoload($strClassName) {
    $strNewClassName = str_replace ( '_', '/', $strClassName . ".php" );
    $arrClass = explode ( '/', $strNewClassName );
    $intPathLen = count ( $arrClass );
    $strLastName = $arrClass [$intPathLen - 1];
    $strTmp = strtolower ( $strNewClassName );
    $intPreLen = strlen ( $strTmp ) - strlen ( $strLastName );
    $strNewClassName = substr ( $strTmp, 0, $intPreLen ) . $strLastName;
    $strClassPath = ROOT_PATH . '/app/' . APP_NAME . '/' . $strNewClassName;
    require_once $strClassPath;
}
spl_autoload_register ( '__autoload' );
// 设置logid
if (! defined ( 'REQUEST_ID' )) {
    $requestTime = gettimeofday ();
    define ( 'REQUEST_ID', (intval ( $requestTime ['sec'] * 100000 + $requestTime ['usec'] / 10 ) & 0x7FFFFFFF) );
}

if (function_exists ( 'camel_set_logid' )) {
    camel_set_logid ( REQUEST_ID );
}

Bingo_Log::init ( array (
    LOG_SCRIPT => array (
        'file' => SCRIPT_LOG_PATH . '/' . SCRIPT_NAME . '.log',
        'level' => 0x01 | 0x02 | 0x04 | 0x08,
    ),
), LOG_SCRIPT );

//取出所有全吧大神id
$arrInput = array();
$ret = Tieba_Service::call('god', 'getAllGodUserIdList', $arrInput, null, null, 'post', 'php', 'utf-8');
if (false === $ret || Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
	Bingo_Log::fatal('god:getAllGodUserIdList call error. input:' . serialize($arrInput) . 'output:' . serialize($ret));
	exit(-1);
}
$uidList = $ret['data']['uid_list'];
$count = count($uidList);

//取每个人3个月内帖子回复总数
$createTime = strtotime('-90 day');
$temp = array_chunk($uidList, 32);
$arrUid = array_chunk($temp, 32);


$strService = "god";
$strMethod = "mgetPostNumSumByUids";
$arrMultiInput = array();
$arrSum = array();

foreach ( $arrUid as $k => $arrSubUid){
	$objMultiCall = new Molib_Tieba_Multi('god_thread_info_mgetPostNumSumByUids_forall'.$k);
	if (!is_object($objMultiCall)) {
		Bingo_Log::warning('init Molib_Tieba_Multi service object failure');
		continue;
	}
	foreach ( $arrSubUid as $subK => $uids){
		$key = $strMethod.$k.$subK;
		$arrMultiInput = array(
				'uids'			=> implode(',', $uids),
				'create_time'	=> $createTime,
		);
		$arrInput = array(
				'serviceName' => $strService,
				'method' => $strMethod,
				'ie' => 'utf-8',
				'input' => $arrMultiInput,
		);
		$objMultiCall->register($key, $arrInput);
	}

	$objMultiCall->call();
	
	foreach ( $arrSubUid as $subK => $uids){
		$key = $strMethod.$k.$subK;
		$ret = $objMultiCall->getResult($key);
		if (false === $ret || Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
			Bingo_Log::warning('Fail to call service god:mgetPostNumSumByUids.  input:' . serialize($uids) . 'output:' . serialize($ret));
			continue;
		}
		$arrSum = $arrSum + $ret['data']['post_num_sum_list'];
	}
}

//推送给排行榜，一个请求200条
$temp = array_chunk($arrSum, 200, true);
$arrPush = array_chunk($temp, 32, true);

$strService = "rank";
$strMethod = "msetScoresByMember";

foreach ( $arrPush as $k => $arrSubPush){
	$objMultiCall = new Molib_Tieba_Multi('god_thread_info_msetScoresByMember_forall'.$k);
	if (!is_object($objMultiCall)) {
		Bingo_Log::warning('init Molib_Tieba_Multi service object failure');
		continue;
	}
	foreach ( $arrSubPush as $subK => $push){
		$key = $strMethod.$k.$subK;
		$arrMultiInput = array();
		foreach ($push as $uid => $sum){
			$item = array (
					'prev' => 'god_allforum_postnum_sum', // 前缀
					'member' => $uid, // 排名项
					'score' => $sum,  // 排名分数
			);
			$arrMultiInput['reqs'][] = $item;
		}
		$arrInput = array(
				'serviceName' 	=> $strService,
				'method' 		=> $strMethod,
				'ie' 			=> 'utf-8',
				'input'			=> $arrMultiInput,
		);
		$objMultiCall->register($key, $arrInput);
	}
	$objMultiCall->call();
}
		



echo 'import script is all done~'.$count;