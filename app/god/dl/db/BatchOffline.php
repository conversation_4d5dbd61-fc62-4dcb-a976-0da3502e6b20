<?php
/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file BatchOffline.php
 * <AUTHOR>
 * @date 2017/10/20 18:14:44
 * @brief 
 *  
 **/

/*
+----------------+---------------------+------+-----+---------+----------------+
| Field          | Type                | Null | Key | Default | Extra          |
+----------------+---------------------+------+-----+---------+----------------+
| id             | bigint(20)          | NO   | PRI | NULL    | auto_increment |
| uid            | bigint(20) unsigned | NO   | UNI | NULL    |                |
| err_no         | int(10)             | NO   | MUL | 0       |                |
| op_uname       | varchar(30)         | NO   | MUL |         |                |
| create_time    | int(10) unsigned    | NO   |     | NULL    |                |
| update_time    | int(10) unsigned    | NO   |     | NULL    |                |
+----------------+---------------------+------+-----+---------+----------------+
 */

define('BINGO_ENCODE_LANG', 'UTF-8');
define("MODULE","God_dl");
class Dl_Db_BatchOffline {
    const SERVICE_NAME = "dl_db_batchoffline";
    const MODULE_NAME = 'god';
    const DATABASE_NAME = "forum_god"; //forum_god
    const DB_CHARSET = "utf8";

    protected static $_db = null;
    protected static $_conf = null;
    protected static $_use_split_db = false;

    /**
     * @brief get mysql obj.
     * @return: obj of Bd_DB, or null if connect fail.

     **/
    private static function _getDB() {
        if(self::$_db) {
            return self::$_db;
        }
        self::$_db = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if(self::$_db == null || !self::$_db->isConnected()) {
            Bingo_Log::warning('db connect fail.');
            return null;
        }
        return self::$_db;
    }

    /**
     * @brief init
     * @return: true if success. false if fail.

     **/
    private static function _init() {
        if(self::_getDB() == null) {
            Bingo_Log::warning("init db fail.");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        if(self::$_conf == null) {
            $dlConfFile = '/app/' . self::MODULE_NAME . '/'. strtolower(self::SERVICE_NAME);
            self::$_conf = Bd_Conf::getConf($dlConfFile);
            if(self::$_conf == false) {
                Bingo_Log::warning('init get conf fail.' . $dlConfFile);
                return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
            }
        }
        return true;
    }

    /**
     * 错误信息
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    private static function _errRet($errno){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * exec sql method
     * @param $arrInput
     * @return output
     */
    public static function execSql($arrInput) {
        if(!isset($arrInput['function'])) {
            Bingo_Log::warning('input params invalid: function is empty. [' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $ret = self::_init();
        if($ret !== true) {
            return $ret;
        }
        Bingo_Timer::start('initlib');
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end('initlib');
        if($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $arrOut = $mdb->execSql($arrInput);
        return $arrOut;
    }
}
?>
