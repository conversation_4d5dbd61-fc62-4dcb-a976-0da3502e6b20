<?php
/*==================================
*   Copyright (C) 2014 Baidu.com, Inc. All rights reserved.
*   
*   filename	:	cksignAction.php
*   author		:	zhouping01
*   create_time	:	2014-10-08 23:02:31
*   desc		:
*
===================================*/

class cksignAction extends Molib_Client_BaseAction {
		const CK_SUCCESS = 200;
		private static $_iosScrKeyTxt = '97dd648ea033fbe5271eba02bb4159bf';

		public function _getPrivateInfo(){
			$arrPrivateInfo = array();
			$arrPrivateInfo['check_login'] = false;
			$arrPrivateInfo['need_login'] = false;
			$this->_objRequest->addStrategy('check_sign', false);
			$arrPrivateInfo['apiKey'] = strval($this->_getInput('apiKey', ""));
			$arrPrivateInfo['ispv'] = 0;
			$arrPrivateInfo['appName'] = strval($this->_getInput('appName', ""));
			$arrPrivateInfo['signKey'] = strval($this->_getInput('signKey', ""));
			$arrPrivateInfo['scrKey'] = strval($this->_getInput('scrKey', ""));
			$arrPrivateInfo['scrT'] = intval($this->_getInput('scrT', 0));
			return $arrPrivateInfo;
		}

		protected function _checkPrivate() {
			
			$strApiKey = $this->_objRequest->getPrivateAttr("apiKey");
			if(strlen($strApiKey) <= 0){
				$arrRet = $this->_error(Tieba_Errcode::ERR_PARAM_ERROR, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_PARAM_ERROR));
				Bingo_Log::warning("err apiKey. [apiKey:".serialize($strApiKey)."]");
				return false;
			}

			return true;
		}


	protected function _execute() {

		return $this->_logic();

	}

    private function _returnErr($intErrno,$strErrmsg,$arrRet=array()){
        $this->_error($intErrno,$strErrmsg);
        $this->_objResponse->setOutData($arrRet);
        return false;
    }

	//method _errRet
	protected static function _errRet($errno,$errmsg=''){
		if(!empty($errmsg)){
			return array(       
				'error_code' => $errno,
				'error_msg' => $errmsg,
			);
		}else{
			return array(       
				'error_code' => $errno,
				'error_msg' => Tieba_Error::getErrmsg($errno),
			);
		}
	}

	private function _logic(){


		$intUserId 		= $this->_objRequest->getCommonAttr('user_id');

		//valid share params
		/////////////////////////////////////////////////////////////////////////////////
		$strApiKey = strval($this->_objRequest->getPrivateAttr('apiKey'));
		$strAppName = strval($this->_objRequest->getPrivateAttr('appName'));
		$strSignKey = strval($this->_objRequest->getPrivateAttr('signKey'));
		$strScrKey = strval($this->_objRequest->getPrivateAttr('scrKey'));
		$intScrT = intval($this->_objRequest->getPrivateAttr('scrT'));


		$intClientType    = intval($this->_objRequest->getCommonAttr('client_type'));
		$strClientVersion = strval($this->_objRequest->getCommonAttr('client_version'));
		$arrValid = array(
			'scrT'		=> $intScrT,
			'scrKey'	=> $strScrKey,
			'signKey'	=> $strSignKey,
			'appName'	=> $strAppName,
			'apiKey'	=> $strApiKey,
			'client_type'	=> $intClientType,
			'client_version'	=> $strClientVersion,
		);
		$arrRetValid = Molib_Sdk_Share::validByApiKey($arrValid);
		if(Tieba_Errcode::ERR_SUCCESS != $arrRetValid['errno']){
			Bingo_Log::warning("err valid apiKey. [input:".serialize($arrValid)."] [output:".serialize($arrRetValid)."]");
			return $this->_returnErr($arrRetValid['errno'], Molib_Client_Error::getErrMsg($arrRetValid['errno']));
		}
		/////////////////////////////////////////////////////////////////////////////////

		$arrRet = array(
			'appName'	=> $arrRetValid['res']['app_name'],
		);
		return $this->_returnErr(Tieba_Errcode::ERR_SUCCESS,Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_SUCCESS),$arrRet);//todo

	}


	private static function _fetchUrl($strUrl,$arrParams=array(),$strMethod='get'){
	
		$url = $strUrl;
		$params = $arrParams;
	
		$httpproxy = Orp_FetchUrl::getInstance(array('timeout' =>30000,'conn_timeout' =>10000,'max_response_size'=> 1024000));
		if('post' == $strMethod){
			$res = $httpproxy->post($url,$params);
		}else{
			$res = $httpproxy->get($url);
		}
		$http_code = $httpproxy->http_code();
		if(200 == $http_code){//正常请求
			return $res;
		}else{
			$err = $httpproxy->errmsg();
			Bingo_Log::warning("fetch_url_err. [url:$url] [http_code:$http_code] [err:$err]");
			return false;
		}
	}


	//form docs
	private static function _generateSign($params, $secret) {
		$str = '';		//签名字符串
		//先将参数以其参数名的字母升序进行排序
		ksort($params);
		//遍历排序后的参数数组中的每一个key/value对
		foreach( $params as $k => $v ) {
			//为key/value对生成一个key=value格式的字符串，并拼接到签名字符串后面
			if ($k != 'sign') {
				$str .= "$k=$v";
			}
		}
		//将签名密钥拼接到签名字符串最后面
		$str .= $secret;
		//返回待签名字符串的md5签名值
		return md5($str);
	}


	private static function _fetchUrlWithRalAndGet($strRalServiceName,$strUrl,$arrInput=array(),$arrExtra=array()){

		$strDataQuery = Tbapi_Core_Util_Http::arrayToQueryString($arrInput);      

		if (strpos($strUrl,'?')){                                                
			$strUrl.='&'.$strDataQuery;                                          
		}else {                                                                  
			$strUrl.='?'.$strDataQuery;                                          
		}                                                                        

		$strOut = camel($strRalServiceName,$strUrl,$arrInput,$arrExtra); 
		if ($strOut != false ) {
			return $strOut;
		}else{
			Bingo_Log::warning("httpcall error[name:".$strRalServiceName."] [url:".$strUrl."] [input:".serialize($arrInput)."] [mehtod:".$strMethod."] [extra:".serialize($arrExtra)."]");
			return false;
		}
	}
}

