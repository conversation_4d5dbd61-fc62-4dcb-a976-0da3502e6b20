<?php
/**
 * Desc: Video
 * User: <EMAIL>
 * Date: 15/03/2017
 * Time: 18:25
 */

class Service_Video_Video extends Service_Libs_Base {

    const USER_STATE_IDLE       = 1;
    const USER_STATE_MATCHING   = 2;
    const USER_STATE_MATCHED    = 3;
    const USER_STATE_CONNECTING = 4;
    const USER_STATE_CALLING    = 5;

    const VIDEO_MSG_TYPE = 102;

    /**
     * @brief  上报连接成功的状态
     * @param  $arrInput
     * @return array
     */
    public static function uploadConnectSuccess($arrInput) {
        // get the parameter
        $intInitiatorId = intval($arrInput['initiator']);
        $intReceiverId  = intval($arrInput['receiver']);
        $intMatchTime   = intval($arrInput['match_time']);

        //check the parameter
        if (($intInitiatorId <= 0) || ($intReceiverId <= 0) || ($intMatchTime <= 0)) {
            Bingo_Log::warning(sprintf("param error. [input = %s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // update the match record
        $arrParam = array(
            'field' => array(
                'connected_time' => time(),
            ),
            'cond' => " (user_id = $intInitiatorId and match_time = $intMatchTime) or (user_id = $intReceiverId and match_time = $intMatchTime)",
        );
        $arrRet = Dl_Db_UserRelation::update($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("update the match status failed. [input = %s][sql = %s]",
                serialize($arrParam), Dl_Db_UserRelation::getLastSQL()));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        // update the status of user state
        $arrParam = array(
            'user_ids' => array(
                $intInitiatorId,
                $intReceiverId,
            ),
            'status' => self::USER_STATE_CALLING,
        );
        $arrRet = Service_Match_UserState::mUpdateUserStateStatus($arrParam);
        return self::_errRet($arrRet['errno']);
    }

    /**
     * @brief  上报挂断成功的状态：
     * @param  $arrInput
     * @return array
     */
    public static function uploadHangUpSuccess($arrInput) {
        // get the parameter
        $intInitiatorId = intval($arrInput['initiator']);
        $intReceiverId  = intval($arrInput['receiver']);
        $intMatchTime   = intval($arrInput['match_time']);

        //check the parameter
        if (($intInitiatorId <= 0) || ($intReceiverId <= 0) || ($intMatchTime <= 0)) {
            Bingo_Log::warning(sprintf("param error. [input = %s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // update the match record
        $arrParam = array(
            'field' => array(
                'hangup_time' => time(),
            ),
            'cond' => " (user_id = $intInitiatorId and match_time = $intMatchTime) or (user_id = $intReceiverId and match_time = $intMatchTime)",
        );
        $arrRet = Dl_Db_UserRelation::update($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("update the match status failed. [input = %s][sql = %s]",
                serialize($arrParam), Dl_Db_UserRelation::getLastSQL()));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        // update the status of user state
        $arrParam = array(
            'user_ids' => array(
                $intInitiatorId,
                $intReceiverId,
            ),
            'status' => self::USER_STATE_IDLE,
        );
        $arrRet = Service_Match_UserState::mUpdateUserStateStatus($arrParam);
        return self::_errRet($arrRet['errno']);
    }

    /**
     * @brief  cancelConnect
     * @param  $arrInput
     * @return array
     */
    public static function cancelConnect($arrInput) {
        // 参数获取
        $intInitiatorId = intval($arrInput['initiator']);
        $intReceiverId  = intval($arrInput['receiver']);

        // 参数检查
        if (($intInitiatorId <= 0) || ($intReceiverId <= 0)) {
            Bingo_Log::warning(sprintf("param error. [input = %s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 将发起取消者的状态置为idle（空闲）
        $arrParam = array(
            'user_id' => $intInitiatorId,
            'status'  => self::USER_STATE_IDLE,
        );
        $arrRet = Service_Match_UserState::updateUserStateStatus($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            return $arrRet['errno'];
        }

        // set the other to matching status
        $arrParam = array(
            'user_id' => $intReceiverId,
            'status'  => self::USER_STATE_MATCHING,
        );
        $arrRet = Service_Match_UserState::updateUserStateStatus($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            return $arrRet['errno'];
        }

        // 发送IM消息通知对方取消了
        $arrParam = array(
            'user_id' => $intReceiverId ,
            'msg' => array(
                'from'      => $intInitiatorId,
                'to'        => $intReceiverId,
                'type'      => 'remoteCancel',
                'content'   => ''
            ),
        );
        $arrRet = Tieba_Service::call('salaim', 'pushVideoMsgByUid', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call salaim pushVideoMsgByUid failed. [input = %s] [output = %s]",
                serialize($arrParam), serialize($arrRet)));
        }

        // calculate the weight for receiver and add him into the queue
        /*$arrRet = Service_Strategy_Strategy::genUserScoreAndType($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            return self::_errRet($arrRet['errno']);
        }
        $arrParam = array(
            'weight'  => $arrRet['data']['weight'],
            'type'    => $arrRet['data']['type'],
            'user_id' => $intReceiverId,
        );
        $arrRet = Service_Match_Queue::addUserIntoMatchQueue($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            return $arrRet['errno'];
        }*/
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief  当客户端进行P2P连接失败时，上报给服务端，将其置为匹配中状态
     * @param  $arrInput
     * @return array
     */
    public static function uploadConnectFail($arrInput) {
        // 参数获取
        $intInitiatorId = intval($arrInput['initiator']);
        $intReceiverId  = intval($arrInput['receiver']);
        $strTypeSignal  = strval($arrInput['type']);

        // 参数检查
        if (($intInitiatorId <= 0) || ($intReceiverId <= 0)) {
            Bingo_Log::warning(sprintf("param error. [input = %s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!empty($strTypeSignal) && $strTypeSignal !== 'signal') {
            Bingo_Log::warning(sprintf("param error. signal type is not illegal. [input = %s",
                serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 首先将这两个用户状态改为匹配中
        /*$arrParam = array(
            'user_ids' => array(
                $intInitiatorId,
                $intReceiverId,
            ),
            'status'  => self::USER_STATE_MATCHING,
        );
        $arrRet = Service_Match_UserState::mUpdateUserStateStatus($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            return self::_errRet($arrRet['errno']);
        }

        // 给用户计算权值和分类，并分别加入到匹配队列
        $arrParam = array(
            'user_id' => $intInitiatorId,
        );
        $arrRet = Service_Strategy_Strategy::genUserScoreAndType($arrParam);
        $arrParam['weight']  = $arrRet['data']['weight'];
        $arrParam['type']    = $arrRet['data']['type'];
        $arrRet = Service_Match_Queue::addUserIntoMatchQueue($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            return self::_errRet($arrRet['errno']);
        }

        $arrParam = array(
            'user_id' => $intReceiverId,
        );
        $arrRet = Service_Strategy_Strategy::genUserScoreAndType($arrParam);
        $arrParam['weight']  = $arrRet['data']['weight'];
        $arrParam['type']    = $arrRet['data']['type'];
        $arrRet = Service_Match_Queue::addUserIntoMatchQueue($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            return self::_errRet($arrRet['errno']);
        }*/

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief  发起匹配
     * @param  $arrInput
     * @return array
     */
    public static function initiateMatch($arrInput) {
        // 获取并检查参数
        $intUserId     = intval($arrInput['user_id']);
        $strTypeSignal = strval($arrInput['type']);
        $strSignalRoom = strval($arrInput['signal_room']);

        // 参数检查
        if ($intUserId <= 0) {
            Bingo_Log::warning(sprintf("param error. user id is not illegal. [input = %s",
                serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!empty($strTypeSignal)) { //如果是暗号，检查暗号是否有效
            if ($strTypeSignal !== 'signal' || empty($strSignalRoom)) {
                Bingo_Log::warning(sprintf("param error. signal type or room is not illegal. [input = %s",
                    serialize($arrInput)));
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            // 查看暗号是否有效
            $arrParam = array(
                'name' => $strSignalRoom,
            );
            $arrRet = Service_Signal_Signal::getSignalByName($arrParam);
            if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                Bingo_Log::warning(sprintf("get signal info by name failed. [input = %s][output = %s]",
                    serialize($arrParam), serialize($arrRet)));
                return self::_errRet($arrRet['errno']);
            }
            if (empty($arrRet['data'])) {
                return self::_errRet(Tieba_Errcode::VIDEO_ROOM_INVALID);
            }
            $intSignalId = intval($arrRet['data']['signal_id']);
        }

        // 更新状态机状态
        $arrParam = array(
            'user_id' => $intUserId,
            'status'  => self::USER_STATE_MATCHING,
        );
        $arrRet = Service_Match_UserState::updateUserStateStatus($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            return self::_errRet($arrRet['errno']);
        }
        // 计算权值
        $arrRet = Service_Strategy_Strategy::genUserScoreAndType($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            return self::_errRet($arrRet['errno']);
        }

        // 加入到匹配队列
        $arrParam = array(
            'user_id' => $intUserId,
            'type'    => $arrRet['data']['type'],
            'weight'  => $arrRet['data']['weight'],
        );
        if (empty($strTypeSignal)) {
            $arrRet = Service_Match_Queue::addUserIntoMatchQueue($arrParam);
            if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                return self::_errRet($arrRet['errno']);
            }
        }
        else {
            $arrParam = array(
                'signal_id' => $intSignalId,
                'user_id'   => $intUserId,
                'user_sex'  => $arrRet['data']['sex'],
            );
            $arrRet = Service_Signal_Signal::enterSignalById($arrParam);
            if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                return self::_errRet($arrRet['errno']);
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief  取消匹配
     * @param  $arrInput
     * @return array
     */
    public static function cancelMatch($arrInput) {
        // 获取并检查参数
        $intUserId = intval($arrInput['user_id']);
        $strTypeSignal = strval($arrInput['type']);

        if ($intUserId <= 0) {
            Bingo_Log::warning(sprintf("param error. user id is not illegal. [input = %s",
                serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!empty($strTypeSignal) && $strTypeSignal !== 'signal') {
            Bingo_Log::warning(sprintf("param error. signal type is not illegal. [input = %s",
                serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 从匹配队列里清楚用户信息
        $arrParam = array(
            'user_ids' => array(
                $intUserId,
            ),
        );
        if (empty($strTypeSignal)) {
            $arrRet = Service_Match_Queue::clearUsersFromQueue($arrParam);
            if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                return self::_errRet($arrRet['errno']);
            }
        }
        else {
            $arrRet = Service_Signal_Signal::mLeaveSignal($arrParam);
            if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                return self::_errRet($arrRet['errno']);
            }
        }

        // 更改用户状态机中的状态
        $arrParam = array(
            'user_id' => $intUserId,
            'status'  => self::USER_STATE_IDLE,
        );
        $arrRet = Service_Match_UserState::updateUserStateStatus($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            return self::_errRet($arrRet['errno']);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief  长连接上线时更新用户状态机中的信息
     * @param  $arrInput
     * @return array
     */
    public static function socketOnline($arrInput) {
        // 参数检查
        $intUserId = intval($arrInput['user_id']);
        if ($intUserId <= 0) {
            Bingo_Log::warning(sprintf("param error. user id is not illegal. [input = %s]",
                serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 获取用户信息
        $arrParam = array(
            'user_id' => $intUserId,
        );
        $arrRet = Tieba_Service::call('ntuser', 'getUserById', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call ntuser::getUserById failed. [input = %s] [output = %s]", serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        // 更新user profile
        $arrParam = array(
            'user_id'      => $intUserId,
            'status'       => self::USER_STATE_IDLE,
            'online_time'  => time(),
            'match_num'    => 0,
            'opposite_num' => 0,
            'sex'          => $arrRet['data']['user']['gender'] == 0 ? 2 : 1,
            'part_time'    => empty($arrRet['data']['user']['part_time']) ? 0 : 1,
        );
        $class = new ReflectionMethod('Service_Match_UserState', 'updateUserStateProfile');
        $arrRet = $class->invoke(null, $arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            return self::_errRet($arrRet['errno']);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief  长连接上线时更新用户状态机中的信息
     * @param  $arrInput
     * @return array
     */
    public static function socketOffline($arrInput) {
        // 参数检查
        $intUserId = intval($arrInput['user_id']);
        if ($intUserId <= 0) {
            Bingo_Log::warning(sprintf("param error. user id is not illegal. [input = %s]",
                serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 更新user profile
        $arrParam = array(
            'user_id'      => $intUserId,
            'status'       => self::USER_STATE_IDLE,
        );
        $class = new ReflectionMethod('Service_Match_UserState', 'updateUserStateStatus');
        $arrRet = $class->invoke(null, $arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            return self::_errRet($arrRet['errno']);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief  发送IM通知匹配结果
     * @param  $arrInput
     * @return array
     */
    public static function notifyMatchResult($arrInput) {
        // 参数检查
        $arrUserIds   = $arrInput['user_ids'];

        if (empty($arrUserIds)) {
            Bingo_Log::warning(sprintf("param error. [input = %s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $objMulti = new Tieba_Multi('notify_match_users');

        // multi_call
        foreach ($arrUserIds as $key => $arrMatchedUser) {
            if (intval($arrMatchedUser['user_id']) > 0 && intval($arrMatchedUser['match_user_id']) > 0 && intval($arrMatchedUser['match_time'])) {
                $arrMultiInput = array(
                    'serviceName' => 'salamatch',
                    'method'      => 'notifyEachMatchGroup',
                    'input'       => array(
                        'initiator'  => intval($arrMatchedUser['user_id']),
                        'receiver'   => intval($arrMatchedUser['match_user_id']),
                        'match_time' => intval($arrMatchedUser['match_time']),
                        'session_id' => intval($arrMatchedUser['global_match_id']),
                    ),
                );
                $objMulti->register($key, new Tieba_Service('salaim'), $arrMultiInput);
            }
        }

        $objMulti->call();
        $arrOutput = array();
        foreach($arrUserIds as $key => $arrMatchedUser) {
            $arrRet = $objMulti->getResult($key);
            if(false == $arrRet) {
                Bingo_Log::warning("call service[$key: {salamatch}::{notifyEachMatchGroup}] fail");
                $arrOutput[$key] = self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            } else {
                $arrOutput[$key] = $arrRet;
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief  给每一组用户发送匹配结果通知
     * @param  $arrInput
     * @return array
     */
    public static function notifyEachMatchGroup($arrInput) {

        // 参数检查
        $intInitiatorId = intval($arrInput['initiator']);
        $intReceiverId  = intval($arrInput['receiver']);
        $intMatchTime   = intval($arrInput['match_time']);
        $intSessionId   = intval($arrInput['session_id']);

        if ($intInitiatorId <= 0 || $intReceiverId <= 0 || $intMatchTime <= 0) {
            Bingo_Log::warning(sprintf("param error. [input = %s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 获取用户信息
        $arrParam = array(
            'user_ids' => array(
                $intInitiatorId,
                $intReceiverId,
            ),
        );
        $arrRet = Tieba_Service::call('ntuser', 'mgetSalaUser', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("mget sala user info failed. [input = %s] [output = %s]", serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrParam = array(
            'user_id'  => $intInitiatorId,
            'find_uid' => $intReceiverId,
        );
        $arrRes = Tieba_Service::call('salaim', 'getRelationByUid', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call salaim getRelationByUid failed. [input = %s] [output = %s]", serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        // 发送IM消息通知匹配结果
        $arrParam = array(
            'user_id' => $intReceiverId ,
            'msg' => array(
                'from'      => $intInitiatorId,
                'to'        => $intReceiverId,
                'type'      => 'matchResult',
                'content'   => json_encode(
                    array(
                        'initiator' => array(
                            'user_id'   => $intInitiatorId,
                            'location'  => strval($arrRet['data'][$intInitiatorId]['location']),
                            'gender'    => intval($arrRet['data'][$intInitiatorId]['gender']) === 0 ? 2 : 1,
                            'star'      => strval($arrRet['data'][$intInitiatorId]['star']),
                            'avatar'    => strval($arrRet['data'][$intInitiatorId]['avatar']),
                            'user_name' => strval($arrRet['data'][$intInitiatorId]['nickname']),
                            'age'       => intval($arrRet['data'][$intInitiatorId]['age']),
                        ),
                        'receiver' => array(
                            'user_id'   => $intReceiverId,
                            'location'  => strval($arrRet['data'][$intReceiverId]['location']),
                            'gender'    => intval($arrRet['data'][$intReceiverId]['gender']) === 0 ? 2 : 1,
                            'star'      => strval($arrRet['data'][$intReceiverId]['star']),
                            'avatar'    => strval($arrRet['data'][$intReceiverId]['avatar']),
                            'user_name' => strval($arrRet['data'][$intReceiverId]['nickname']),
                            'age'       => intval($arrRet['data'][$intReceiverId]['age']),
                        ),
                        'match_time' => $intMatchTime,
                        'friend'     => (intval($arrRes['data']['status']) === 1 ? 1 : 0),
                        'session_id' => $intSessionId,
                    )
                ),
            ),
        );

        $arrRet = Tieba_Service::call('salaim', 'pushVideoMsgByUid', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call salaim pushVideoMsgByUid failed. [input = %s] [output = %s]",
                serialize($arrParam), serialize($arrRet)));
            return self::cancelMatch($arrParam);
        }

        $arrParam['user_id'] = $intInitiatorId;
        $arrRet = Tieba_Service::call('salaim', 'pushVideoMsgByUid', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call salaim pushVideoMsgByUid failed. [input = %s] [output = %s]",
                serialize($arrParam), serialize($arrRet)));
            return self::cancelMatch($arrParam);
        }

        // 更新用户状态至连接中
        $arrParam = array(
            'user_ids' => array(
                $intInitiatorId,
                $intReceiverId,
            ),
            'status' => self::USER_STATE_CONNECTING,
        );
        return Service_Match_UserState::mUpdateUserStateStatus($arrParam);
    }

    /**
     * @brief  增加聊天时长
     * @param  $arrInput
     * @return array
     */
    public static function addVideoTime($arrInput)
    {
        $intFromUserId = intval($arrInput['from_user_id']);
        $intToUserId   = intval($arrInput['to_user_id']);
        $intAddTime    = intval($arrInput['addTime']);

        if ($intFromUserId <= 0 || $intToUserId <= 0 || $intAddTime <= 0) {
            Bingo_Log::warning(sprintf("param error. [input = %s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 发送IM消息通知匹配结果
        $arrParam = array(
            'user_id' => $intToUserId ,
            'msg' => array(
                'from'      => $intFromUserId,
                'to'        => $intToUserId,
                'type'      => 'addTime',
                'content'   => json_encode(
                    array(
                        'addTime' => $intAddTime,
                    )
                ),
            ),
        );
        $arrRet = Tieba_Service::call('salaim', 'pushVideoMsgByUid', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call salaim pushVideoMsgByUid failed. [input = %s] [output = %s]",
                serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief  清空匹配队列
     * @return array
     */
    public static function delQueue() {
        $arrRet = Dl_Db_Queue::deleteRecord(array());
        return self::_errRet($arrRet['errno']);
    }
}