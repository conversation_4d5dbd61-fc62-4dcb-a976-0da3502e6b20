<?php
class Action_IncomeAsyn extends Exp_Common_UIActionBase{
	public function process(){
		$objAsynPageService = Bd_LayerProxy::getProxy('Service_Page_IncomeAsyn');
    	$arrInputData = $this->arrReqData['request_param'];
        
        if(empty($arrInputData['act'])){
        	$arrInputData['act'] = "default";
        }

        $this->arrOutputData = $objAsynPageService->execute($arrInputData);
	}

}
