<?php
class Action_Nuc extends Exp_Common_UIActionBase{
	
    public function process(){
    	$objNucPageService = Bd_LayerProxy::getProxy('Service_Page_Nuc');
    	$arrInputData = $this->arrReqData['request_param'];
    	$arrInputData['page'] = strip_tags($arrInputData['page']);
        if(empty($arrInputData['page'])){
        	$arrInputData['page'] = "index";
        }
		$this->arrOutputData = $objNucPageService->execute($arrInputData);

		if($this->arrOutputData['errno'] === 0 || 
		$this->arrOutputData['errno'] === Exp_Data_Errno::NEED_LOGIN_ERR){
			$arrConf = Bd_Conf::getConf('/app/user/user/');
			$this->setTemplatePath($arrConf['template_path'][$arrInputData['page']]);
		}else{
			 header('Location: '.$arrConf['error_page']);
		}
    }
}
