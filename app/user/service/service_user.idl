#ifndef __USER_IDL__
#define __USER_IDL__

struct itieba_info
{
    uint32_t itieba_id;
    uint32_t user_type;// 0    1 vip
    uint32_t outer_id;
    uint32_t user_id;
    string user_name;
    string user_url;
    string user_nickname;
    uint32_t user_status;
    uint32_t follow_count;
    uint32_t followed_count;
	uint32_t is_followed;
};

struct signature {
	uint64_t sid;
	uint32_t user_id;
	int32_t status;
	int32_t inner_sid;
	int32_t is_default;
	int32_t simg_width;
	int32_t simg_height;
	string stitle;
	string scontent;
	string surl;
};

struct sign_info {
	uint32_t	is_force_hide;
	signature signatures[];
};

struct fucenter_user_info {
	uint32_t user_id;
	uint32_t lastlogin_time;
	uint32_t lastlogin_ip;
	string user_name;
};

struct uname_uid {
	uint32_t user_id;
	string user_name;
};

struct get_uid_by_unames_output {
	uname_uid uids[];
};

service user{

	void testUser(string word,out string word);

	// ��ȡ�û���Ϣ
	void getUserinfo(uint32_t user_id, out itieba_info user_info, out sign_info sign_info);

	// ��ȡ�û�I������Ϣ
	void getItiebaInfoByUIds(uint32_t user_id[], out itieba_info user_info[]);
	void getItiebaInfoByItbIds(uint32_t itieba_id[], out itieba_info user_info[]);
	void getItiebaInfoByOuterIds(uint32_t outer_id[], out itieba_info user_info[]);
    void getItiebaInfoByUnames(string user_name[], out itieba_info user_info[]);
	
	//��ȡ�û�ǩ���� 
	void getSignByUid(uint32_t user_id,out sign_info output);
	
	//��ȡ�û����͵�½ʱ�䣨������
	void getUserInfoByUids(uint32_t user_id[],out fucenter_user_info output[]);
	
	//��ȡ�û����͵�½ʱ��
	void getUserInfoByUname(string user_name,out fucenter_user_info output);
	
	//�û���ת�û�id��������
	void getUidByUnames(string user_name[],out get_uid_by_unames_output output);


};


#endif
