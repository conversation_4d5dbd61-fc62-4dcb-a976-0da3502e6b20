import "common.proto";

//getFollowAndFollowedByUid input
message GetFollowAndFollowedByUidReq
{
	optional int64 user_id = 1;
	optional uint32 type = 2;
	optional int64 offset = 3;
	optional int64 limit = 4;
	optional uint32 order_time = 5;
	optional uint64 only_user_id = 6;
}

//getFollowAndFollowedByUid output
message GetFollowAndFollowedByUidRes
{
	required uint32 errno = 1;
	required string errmsg = 2;
	optional bytes fans = 3;
	optional bytes concern = 4;
}
