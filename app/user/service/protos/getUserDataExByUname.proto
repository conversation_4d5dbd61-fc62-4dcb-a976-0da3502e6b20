import "common.proto";

//getUserDataExByUname input
message GetUserDataExByUnameReq
{
	optional string user_name = 1;
	optional string data_tpl = 2;
	optional uint32 get_icon = 3;
	optional uint32 need_follow_info = 4;
	optional uint32 need_login_info = 5;
	optional uint32 need_pass_info = 6;
	optional string call_from = 7;
    optional bytes extra_fields = 8;
}

//getUserDataExByUname output
message GetUserDataExByUnameRes
{
	required uint32 errno = 1;
	required string errmsg = 2;
	optional bytes user_info = 3;
}