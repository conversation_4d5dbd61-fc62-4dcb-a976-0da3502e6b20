import "common.proto";

//getUserFollowedListFromUF input
message GetUserFollowedListFromUFReq
{
	optional int64 user_id = 1;
    optional int64 offset = 2;
    optional int64 limit = 3;
    optional int64 only_user_id = 6;
}

//getUserFollowedListFromUF output
message GetUserFollowedListFromUFRes
{
	required uint32 errno = 1;
	required string errmsg = 2;
	optional bytes page = 3;  
	optional bytes user_infos = 4;
}
