
message UserFollowInfo
{
	optional int64 itieba_id = 1;  
	optional int64 user_type = 2;  
	optional int64 outer_id = 3;  
	optional int64 user_id = 4;  
	optional string user_name = 5;  
	optional string user_url = 6;  
	optional string user_nickname = 7;  
	optional uint32 user_status = 8;  
	optional int64 follow_count = 9;  
	optional int64 followed_count = 10;  
	optional uint32 is_followed = 11;
	optional uint32 is_follow = 12;
}

message TiebaInfo
{
	optional int64 itieba_id = 1;
	optional int64 user_type = 2;
	optional int64 outer_id = 3;
	optional int64 user_id = 4;
	optional string user_name = 5;
	optional string user_url =  6;
	optional string user_nickname = 7;
	optional int64 user_status = 8;
	optional int64 follow_count = 9;
	optional int64 followed_count = 10;
	optional uint32 is_followed = 11;
	optional uint32 meizhi_level = 12;
	optional string superboy = 13;
	optional bytes puserinfo = 14;
}

message UidsUnameInfo
{
	optional int64 user_id = 1;
	optional string user_name = 2;
}