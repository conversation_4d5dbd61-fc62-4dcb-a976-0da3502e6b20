#ifndef __TRANSFORM_IDL__
#define __TRANSFORM_IDL__

struct single_eid_reset{
		uint32_t itieba_id;
		uint32_t forum_id; // �����0������¸��û���ע�����аɡ�
		uint64_t max_eid;
};


struct itbtrans_cmd
{
    uint32_t command_no;
    string user_name = optional(); 
    uint32_t user_id = optional(); 
    string nick_name = optional();
    uint32_t user_itieba_id = optional();

    uint32_t itieba_id = optional(); 
    uint32_t itieba_outer_id = optional();
    uint32_t level_id = optional();

    uint32_t thread_id = optional(); 
    int64_t post_id = optional(); 

    string op_uname = optional(); 
    uint32_t op_uid = optional(); 

    string title = optional(); 

    uint32_t followed_itieba_ids[] = optional();
    uint32_t black_itieba_ids[] = optional();

    uint32_t product_id = optional(); 

    uint32_t now_time = optional();
	string forum_name = optional();
	uint32_t thread_user_id = optional();
	string url = optional();
	uint32_t status = optional();

	uint32_t forum_id = optional();

	single_eid_reset  single_cmds[] = optional();
};

#endif
