<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013:03:13 17:27:29
 * @version 
 * @structs & methods(copied from idl.)
*/

//modify by huangyupeng @2018-01-20 18:35:30

class Service_Tool{
	public static function getUinfoByUidsFromPassgate($arrUids, $arrReqFields=array('sex'), $encode='gbk'){
	        $arrExtra = array(
                'retry' => 0,
                'rtimeout' => 100,
            );
            return Passport_Passgate_Tool::getUinfoByUidsFromPassgate($arrUids, $arrReqFields, $encode, $arrExtra);
	}
	
	public static function getUinfoByUidsFromPassgateByUnames($arrUnames, $arrReqFields=array('sex'), $encode='gbk'){
        $arrExtra = array(
            'retry' => 0,
            'rtimeout' => 200,
        );
            return Passport_Passgate_Tool::getUinfoByUnamesFromPassgate($arrUnames, $arrReqFields , $encode, $arrExtra) ;
    }
	/*
	 * 2015-11-13 add by jiangcan 
	 * use replace Passport_Passgate_Server function 
	 * support utf8 encode
	 */
    public static function getUidsByUnamesFromPassgate($arrUnames, $encode='gbk'){
        $arrExtra = array(
            'retry' => 0,
            'rtimeout' => 200,
        );
        return Passport_Passgate_Tool::getUidsByUnamesFromPassgate($arrUnames,$encode, $arrExtra);
    }
}
