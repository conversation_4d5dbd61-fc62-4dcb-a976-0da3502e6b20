<?php
/**
 * @name Service_Page_Nuc
 * <AUTHOR>
 */
class Service_Page_Nuc {
    private $_arrConf;
    private $_arrLoginUser;

    protected $arrCriticalIds;
    protected $arrFeedList;
    protected $intIsEnd;
    protected $intUid;
    protected $intPn;
    protected $arrResult;
    
    public function __construct(){
		$this->_arrConf = Bd_Conf::getConf('/app/user/user/');
	    $this->_arrLoginUser = Saf_SmartMain::getUserInfo();
	    $this->arrCriticalIds = array('start'=>0,'id1'=>0,'id2'=>0,'id3'=>0,'time'=>0);
	    $this->arrFeedList = array();
	    $this->intIsEnd = 0;
	    $this->intUid = $this->_arrLoginUser['uid'];
	    $this->intPn = 0;
        $this->arrResult = array(
        	'errno' => 0,
            'data'   => array(),
		);
    }
    
    public function __set($property,$value){
    	$this->$property = $value;
    }
    
    public function execute($arrInput){
		$strPage = strip_tags($arrInput['page']);
		$intType = intval($arrInput['type']);
		$intState = intval($arrInput['state']);
		$this->intPn = intval($arrInput['pn']);
		if ($this->intPn < 0) $this->intPn = 0;
		if(empty($this->_arrLoginUser)){
			$this->arrResult['errno'] = Exp_Data_Errno::NEED_LOGIN_ERR;
			return $this->arrResult;
		}
		$objUserInfoServ = Bd_LayerProxy::getProxy('Exp_Service_Data_UserInfo');
		$this->arrUserDetail = $objUserInfoServ->getUserDetailInfo($this->intUid);
    	if (empty($this->arrUserDetail)) {
            $this->arrUserDetail['uname'] = $this->_arrLoginUser['uname'];
            $this->arrUserDetail['portrait'] = $objUserInfoServ->getUserPassportPic($this->intUid,$this->arrUser['userName']);
        }
        $objAuthorServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Author');
		$this->arrUserDetail['isPayAuthor'] = $objAuthorServ->checkAuthorState($this->intUid);
		$objVoteServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Vote');
		$this->arrUserDetail['hasVoted'] = $objVoteServ->checkTodayVote($this->intUid);
		$this->arrUserDetail['isMyself'] = 1;//Fe Fis 重构引入变量。

        //判断是否加入分成计划
        $objIncomeServ = Bd_LayerProxy::getProxy('Service_Data_Income');
        $arrIncomeUserInfo = $objIncomeServ->getUserProfileByUid($this->intUid);
        if(!empty($arrIncomeUserInfo) && intval($arrIncomeUserInfo['status'])>0){
            $this->arrUserDetail['isIncomeUser'] = 1; 
        }else{
            $this->arrUserDetail['isIncomeUser'] = 0; 
        }

		//判断绑定第三方网站与否
		$objSnsServ = Exp_Common_RalRpcFactory::getRalRpc('Sns');
		$resBind = $objSnsServ->getBindList(intval($this->intUid));
		if(array_key_exists(1,$resBind)){
			$this->arrUserDetail['isBindRenRen'] = 1;
		}else{
			$this->arrUserDetail['isBindRenRen'] = 0;
		}
		if(array_key_exists(2,$resBind)){
			$this->arrUserDetail['isBindSina'] = 1;
		}else{
			$this->arrUserDetail['isBindSina'] = 0;
		}
        $objUserCenterServ = Bd_LayerProxy::getProxy('Service_Data_UserCenter');
        $strHotPersonHtml = $objUserCenterServ->getHotPersonHtml();
        if ($strHotPersonHtml == false) {
            Bd_Log::warning('get empty data: hot person html');
            $strHotPersonHtml = ''; 
        }
        $strHotActiveHtml = $objUserCenterServ->getHotActiveHtml();
    	if ($strHotActiveHtml == false) {
            Bd_Log::warning('get empty data: hot Active html');
            $strHotActiveHtml = ''; 
        }
        $arrLatestExpList = $objUserCenterServ->getLatestExp();
        
        $arrUser['uid'] = $this->intUid;
        $this->arrResult['data']['UserInfo'] = $arrUser;
        $this->arrResult['data']['hotPersonHTML'] = $strHotPersonHtml;
        $this->arrResult['data']['hotActiveHTML'] = $strHotActiveHtml;
        $this->arrResult['data']['LatestExpList'] = $arrLatestExpList;
        $this->arrResult['data']['UserDetail'] = $this->arrUserDetail;


        if($strPage === ''){
           $strPage = 'index';
        
        }
        switch($strPage) {
        case 'index':
        	$objFollowServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Follow');
        	$objFeedServ = Exp_Common_RalRpcFactory::getRalRpc('FeedUi');
            $res = $objFollowServ->getAllFollowUid($this->intUid);
            $con = "";
            foreach($res as $val){
                $arrReq['uids'] .= $con.$val['follow_uid'];
                $con = ",";
            }
            $arrReq['types'] = $this->_arrConf['feed']['type'];
            $arrReq['start'] = 0;
            $arrReq['count'] = min($this->_arrConf['feed']['perpage'],$this->_arrConf['feed']['maxnum']);
            $arrRet = $objFeedServ->getFeed($arrReq);
            if (empty($arrRet) || !isset($arrRet['list']) || empty($arrRet['list'])) {
            	$this->arrResult['data']['FeedList'] = array();
            	$this->arrResult['data']['CriticalIds'] = $this->arrCriticalIds;
                $this->arrResult['data']['isEnd'] = 1;
                break;
            }
            foreach($arrRet['list'] as $item) {
                $item = $item[0];
                $arrFeed = array();
                $arrOther = $item['other'];
                switch(intval($item['id1'])) {
                case $this->_arrConf['feed']['id1_exp']:
                    if ($arrOther['isVersion'] === 1) {
                        $arrFeed['type'] = $this->_arrConf['feedtype']['version'];
                    } else {
                        $arrFeed['type'] = $this->_arrConf['feedtype']['exp'];
                    }
                    $arrFeed['un'] = $arrOther['un'];
                    $arrFeed['cid'] = $arrOther['cid'];
                    $arrFeed['cname'] = $arrOther['cname'];
                    $arrFeed['createTime'] = $arrOther['createTime'];
                    $arrFeed['eidEnc'] = $arrOther['eidEnc'];
                    $arrFeed['title'] = $arrOther['title'];
                    $arrFeed['picEnc'] = $arrOther['picEnc'];
                    $arrFeed['summary'] = $arrOther['abstract'];
                    break;
                case $this->_arrConf['feed']['id1_comment']:
                    $arrFeed['type'] = $this->_arrConf['feedtype']['comment'];
                    $arrFeed['un'] = $arrOther['un'];
                    $arrFeed['eidEnc'] = $arrOther['eidEnc'];
                    $arrFeed['title'] = $arrOther['title'];
                    $arrFeed['createTime'] = $arrOther['createTime'];
                    $arrFeed['summary'] = $arrOther['abstract'];
                    break;
                case $this->_arrConf['feed']['id1_follow']:
                    $arrFeed['type'] = $this->_arrConf['feedtype']['follow'];
                    $arrFeed['un'] = $arrOther['un'];
                    $arrFeed['followUn'] = $arrOther['followUn'];
                    $arrFeed['intro'] = $arrOther['intro'];
                    $arrFeed['createTime'] = $arrOther['createTime'];
                    $arrFeed['portrait'] = $arrOther['portrait'];
                    break;
                }
                if($arrFeed['un']){
                    $this->arrFeedList[] = $arrFeed;
                }
            }
            $arrCriticalIds = array();
            $intCount = count($arrRet['list']);
            if ($intCount > 0) {
                $arrLast = $arrRet['list'][$intCount-1][0];
                $this->arrCriticalIds['start'] = $intCount;
                $this->arrCriticalIds['id1'] = intval($arrLast['id1']);
                $this->arrCriticalIds['id2'] = intval($arrLast['id2']);
                $this->arrCriticalIds['id3'] = intval($arrLast['id3']);
                $this->arrCriticalIds['time'] = intval($arrLast['time']);
            }
            if ($intCount < min($this->_arrConf['feed']['perpage'],$this->_arrConf['feed']['maxnum'])) {
                $this->intIsEnd = 1;
            }
            $this->arrResult['data']['FeedList'] = $this->arrFeedList;
            $this->arrResult['data']['CriticalIds'] = $this->arrCriticalIds;
            $this->arrResult['data']['isEnd'] = $this->intIsEnd;
            break;

        case 'vote':
            $objVoteServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Vote');
            $objUserServ = Bd_LayerProxy::getProxy('Exp_Service_Data_UserInfo');
            $ret = $objVoteServ->getVoterListByUid($this->intUid, $this->intPn);
	        foreach ($ret as $nouse => $vote ) {
		        $arrVoter[$vote['vote_date']][] = array(
		            'portrait' => $objUserServ->getUserPassportPic($vote['voter_uid'],$vote['uname']),
		            'uname' => $vote['uname'],
		            'votedTime' => $vote['create_time'],
		        );
            }
            $arrVoteList['data'] = $arrVoter;
            $arrVoteList['currentPn'] = $this->intPn;
            $arrVoteList['total'] = $this->arrUserDetail['mVotedNum'];
            $this->arrResult['data']['VoteList'] = $arrVoteList;

            break;

        case 'following':
            $objFollowServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Follow');
            $objAuthWriter = Bd_LayerProxy::getProxy('Exp_Service_Data_AuthWriter');
            $objMyExpServ = Bd_LayerProxy::getProxy('Exp_Service_Data_MyExp');
            $arrRet = $objFollowServ->getFollowListByUid($this->intUid, $this->intPn);
            foreach ($arrRet as $followUid) {
                $followPerson = array();
                $ret = $objUserInfoServ->getUserDetailInfo($followUid['follow_uid']);
                if (empty($ret)) {
                    $followPerson['uname'] = "";
                    $followPerson['followedNum'] = "";
                    $followPerson['introduction'] = "";
                    $followPerson['portrait'] = "";
                }
                $followPerson['uname'] = $ret['uname'];
                $followPerson['followedNum'] = $ret['followed_num'];
                $followPerson['introduction'] = $ret['introduction'];
                $followPerson['portrait'] = $ret['portrait'];
                $authWriter = $objAuthWriter->getAuthWriterInfoByUid($followUid['follow_uid']);
                $followPerson['title'] = empty($authWriter) ? '' : $authWriter['title'];
                $ret1 = $objMyExpServ->getNewExpByUid($followUid['follow_uid']);
                if (empty($ret1[0])) {
                    $followPerson['latestExpTitle'] = '';
                    $followPerson['latestExpEidEnc'] = '';
                } else {
                    $objFcrypt = new Exp_Common_Fcrypt;
                    $followPerson['latestExpEidEnc'] = $objFcrypt->getEncodeStrById($ret1[0]['eid']);
                    $followPerson['latestExpTitle'] = $ret1[0]['title'];
                }
                $arrFollowList[] = $followPerson;
            }
            $arrFollowList['data'] = array_slice($arrFollowList,0,Exp_Data_Constant::USERINFO_FOLLOWED_PERPAGE);
            $arrFollowList['currentPn'] = $this->intPn;
            $arrFollowList['total'] = $this->arrUserDetail['followNum'];
            $this->arrResult['data']['followList'] = $arrFollowList;
            break;

        case 'follower':
        	$objFollowServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Follow');
            $objAuthWriter = Bd_LayerProxy::getProxy('Exp_Service_Data_AuthWriter');
            $objMyExpServ = Bd_LayerProxy::getProxy('Exp_Service_Data_MyExp');
            $arrRet = $objFollowServ->getFollowedListByUid($this->intUid, $this->intPn);
            foreach ($arrRet as $followUid) {
                $followPerson = array();
                $ret = $objUserInfoServ->getUserDetailInfo($followUid['followed_uid']);
                if (empty($ret)) {
                    $followPerson['uname'] = "";
                    $followPerson['followedNum'] = "";
                    $followPerson['introduction'] = "";
                    $followPerson['portrait'] = "";
                }
                $followPerson['uname'] = $ret['uname'];
                $followPerson['followedNum'] = $ret['followed_num'];
                $followPerson['introduction'] = $ret['introduction'];
                $followPerson['portrait'] = $ret['portrait'];
                $authWriter = $objAuthWriter->getAuthWriterInfoByUid($followUid['followed_uid']);
                $followPerson['title'] = empty($authWriter) ? '' : $authWriter['title'];
                $ret1 = $objMyExpServ->getNewExpByUid($followUid['followed_uid']);
                if (empty($ret1[0])) {
                    $followPerson['latestExpTitle'] = '';
                    $followPerson['latestExpEidEnc'] = '';
                } else {
                    $objFcrypt = new Exp_Common_Fcrypt;
                    $followPerson['latestExpEidEnc'] = $objFcrypt->getEncodeStrById($ret1[0]['eid']);
                    $followPerson['latestExpTitle'] = $ret1[0]['title'];
                }
                $arrFollowList[] = $followPerson;
            }
            $arrFollowList['data'] = array_slice($arrFollowList,0,Exp_Data_Constant::USERINFO_FOLLOWED_PERPAGE);
            $arrFollowList['currentPn'] = $this->intPn;
            $arrFollowList['total'] = $this->arrUserDetail['followedNum'];
            $this->arrResult['data']['followedList'] = $arrFollowList;
			break;
        case 'favorclass':
            $objFavorClassServ = Bd_LayerProxy::getProxy('Service_Data_FavorClass');
            $arrFavorClassList = $objFavorClassServ->getFavorClassDetail($this->intUid);
            $this->arrResult['data']['InterestList'] = $arrFavorClassList;
            break;
            
        case 'expList':
            if(!$intType){
                 $intType = 1;
            }
            if ( $intType > Exp_Data_Constant::USERINFO_TAB_EXP_MAX || $intType < Exp_Data_Constant::USERINFO_TAB_EXP_MIN) {
                $this->intErrNo = Exp_Data_Errno::INVALID_REQUEST_PARAM_ERR;
                Bd_Log::warning('redirect to other page because of invalid tab num.');
                Exp_Common_Tools::redirect(Exp_Data_Constant::$REDIRECT_URL_USER_UI);
                return false;
            }

            $objMyExpServ = Bd_LayerProxy::getProxy('Service_Data_MyExp');
            $objUScoreServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Uscore');
            $arrUScore = $objUScoreServ->getUscoreAttr($this->intUid);

            $arrMyExpList = array();
            if (empty($arrUScore)){
                $this->arrResult['data']['expList'] = $arrMyExpList;
                break;
            }

            $arrMyExpList['classNum'] = array(
                'passed' => $arrUScore['passed'],
                'submitting' => $arrUScore['submitting'],
                'failed' => $arrUScore['failed'],
                'draft' => $arrUScore['draft'],
            );
            if(!$intType){
                 $intType = 1;
            }
		    switch ($intType) {
    		    case Exp_Data_Constant::USERINFO_TAB_PASSED:
                    $arrMyExpList['data'] = $objMyExpServ->getMyExpPassedByUid($this->intUid,$this->intPn);
                    $arrMyExpList['expNum'] = $arrMyExpList['classNum']['passed'];
                    $arrMyExpList['type'] = $intType;
                    $arrMyExpList['currentPn'] = $this->intPn;
                    $arrMyExpList['total'] = $arrUScore['passed'];
	            	break;
	    	    case Exp_Data_Constant::USERINFO_TAB_CHECKING:
                    $arrMyExpList['data'] = $objMyExpServ->getMyExpCheckingByUid($this->intUid,$this->intPn);
                    $arrMyExpList['expNum'] = $arrMyExpList['classNum']['submitting'];
                    $arrMyExpList['type'] = $intType;
                    $arrMyExpList['currentPn'] = $this->intPn;
                    $arrMyExpList['total'] = $arrUScore['submitting'];
        	    	break;
		        case Exp_Data_Constant::USERINFO_TAB_UNPASSED:
                    $arrMyExpList['data'] = $objMyExpServ->getMyExpUnPassedByUid($this->intUid,$this->intPn);
                    $arrMyExpList['expNum'] = $arrMyExpList['classNum']['failed'];
                    $arrMyExpList['type'] = $intType;
                    $arrMyExpList['currentPn'] = $this->intPn;
                    $arrMyExpList['total'] = $arrUScore['failed'];
	            	break;
	        	case Exp_Data_Constant::USERINFO_TAB_DRAFT:
	        	    $objDraftServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Draft');
	           	    $arrMyExpList['data'] = $objDraftServ->getDraftNoContentByUid($this->intUid,$this->intPn);
                    $arrMyExpList['expNum'] = $arrMyExpList['classNum']['draft'];
                    $arrMyExpList['type'] = $intType;
                    $arrMyExpList['currentPn'] = $this->intPn;
                    $arrMyExpList['total'] = $arrUScore['draft'];
                    break;
                case Exp_Data_Constant::USERINFO_TAB_SEASONEXP:
	        	    $objSeasonServ = Bd_LayerProxy::getProxy('Exp_Service_Data_SeasonApi');
	           	    $arrMyExpList['data'] = $objSeasonServ->getSeason("getUserSeasonList",$this->intUid,$this->intPn);
                    $arrMyExpList['expNum'] = $arrMyExpList['classNum']['draft'];
                    $arrMyExpList['type'] = $intType;
                    $arrMyExpList['currentPn'] = $this->intPn;
                    $arrMyExpList['total'] = $objSeasonServ->getSeason("getUserSeasonTotal",$this->intUid);
                    break;
             }
             $this->arrResult['data']['expList'] = $arrMyExpList;
             break;
            
         case 'obcomment':
             $objCommentServ = Exp_Common_RalRpcFactory::getRalRpc('Comment');
             $arrComment = $objCommentServ->getReceivedCommentList($this->intUid, Exp_Data_Constant::COMMENT_LIST_LIMIT, $this->intPn);
             $commentList = array();
             //$objUcrypt = Bd_LayerProxy::getProxy('Exp_Common_Ucrypt');
             $obComment['total'] = intval($arrComment['total_count']);
             $objFcrypt = new Exp_Common_Fcrypt();
             foreach ($arrComment['data'] as $item) {
             	$objUserServ = new Exp_Common_UserUtils;
             	$strUn = iconv('gbk','utf-8',$item['replyer_name']);
                $replyerId = $objUserServ->getUidByUname($strUn);
                $arrOne = array();
                $arrContent = unserialize($item['content']);
                $arrOne['uname'] = iconv('gbk','utf-8',$item['replyer_name']);
                $arrOne['cmtId'] = intval($item['reply_id']);
                $intUid = $replyerId;
                $objAuthWriterServ = Bd_LayerProxy::getProxy('Exp_Service_Data_AuthWriter');
                $arrAuthWriter = $objAuthWriterServ->getAuthWriterInfoByUid($intUid);
                if (empty($arrAuthWriter)) {
                    $strPortrait = $objUserInfoServ->getUserPassportPic($intUid,$item['replyer_name']);
                }else {
                    $strPortrait = $arrAuthWriter['picurl'];
                }
                $arrOne['portrait'] = $strPortrait;
                $arrOne['releaseTime'] = intval($item['create_time']);
                $arrOne['replyContent'] = $arrContent['content'];
                $arrOne['picEnc'] = $arrContent['picEnc'];
                $arrOne['thread_id'] = intval($item['thread_id']);
                $arrOne['parent_id'] = intval($item['parent_id']);
                $arrOne['eidEnc'] = $objFcrypt->getEncodeStrById($arrOne['thread_id']);

                if ($arrOne['parent_id'] === 0){
                    $arrOne['type'] =1;
                    $objExpServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Exp');
                    $expDetail = $objExpServ->getExpDetailByEid($arrOne['thread_id']);
                    $arrOne['expTitle'] = $expDetail['title'];
                }
                else{
                    $arrOne['type'] =2;
                    $commentDetail = $objCommentServ->getOneComment($arrOne['thread_id'],$arrOne['parent_id']);
                    $arrContent = unserialize($commentDetail['data'][0]['content']);
                    $arrOne['content']['content'] = $arrContent['content'];
                    $arrOne['content']['cmtId'] = $commentDetail['data'][0]['reply_id'];
                } 
                $commentList[] = $arrOne;
            }
            $obComment['currentPn'] = $this->intPn;
            $obComment['commentList'] = $commentList;
            $this->arrResult['data']['obComment'] = $obComment;
            
            break;
            
        case 'mytask':
			$arrUserTask = array();
			$objMissionApi = Bd_LayerProxy::getProxy('Exp_Service_Data_MissionApi');
			$objTaskApi = Bd_LayerProxy::getProxy('Exp_Service_Data_TaskApi');
			$arrUserMissionGrant = $objMissionApi->getMission("getUserGrant",0,$this->intUid,0,0,0,0,0);
			$arrMissionConf = Bd_Conf::getConf('/app/task/mission/');
			if(!empty($arrUserMissionGrant)){
				$this->arrResult['data']['grant'] = $arrUserMissionGrant['grant'][0]['grant_group'];
			}
			if($intState==0){
				$intState = 1;
			}
			switch ($intState) {
			case Exp_Data_Constant::USERINFO_DOING_MISSION:
				$arrTaskList = $objTaskApi->getTaskInfo("getUserTasks",$this->intUid,0,0,0,0,
					Exp_Data_Constant::TASK_STATUS_RUNNING,Exp_Data_Constant::TASK_TYPE_QUERY);
				if(empty($arrTaskList)){
					$arrTaskList = $objTaskApi->getTaskInfo("getUserTasks",$this->intUid,0,0,0,0,
                                        Exp_Data_Constant::TASK_STATUS_AUDITING,Exp_Data_Constant::TASK_TYPE_QUERY)
;
				}
				$arrMission = $objMissionApi->getMission('getMissionList',0,$this->intUid,
					Exp_Data_Constant::MISSION_TYPE_MONTH,Exp_Data_Constant::USERINFO_DOING_MISSION);
				$arrMissionTasks = $objTaskApi->getTaskInfo("getUserTasks",$this->intUid,0,0,0,$this->intPn,
					Exp_Data_Constant::TASK_STATUS_RUNNING,Exp_Data_Constant::TASK_TYPE_TITLE);
				$arrUserTask = $this->handleTaskData($arrTaskList);
				$arrMissionTasks = $this->handleTaskData($arrMissionTasks);
				$this->arrResult['data']['taskList'] = $arrUserTask;
				$this->arrResult['data']['missionList'] = $arrMission;
				$this->arrResult['data']['missionTaskList'] = $arrMissionTasks;
				$this->arrResult['data']['listScale'] = $arrMissionConf['mission_uc_receive_title_list_num'];
				$this->arrResult['data']['total'] = $objTaskApi->getTaskInfo("getUserTaskNum",$this->intUid,
					0,0,0,0,Exp_Data_Constant::TASK_STATUS_RUNNING,Exp_Data_Constant::TASK_TYPE_TITLE);
				break;
			case Exp_Data_Constant::USERINFO_RECEIVE_MISSION:
				if($intType == Exp_Data_Constant::MISSION_TYPE_DAILY){//日常任务
					$objFavorClassServ = Bd_LayerProxy::getProxy('Service_Data_FavorClass');
					$arrFavorClass = $objFavorClassServ->getFavorClassDetail($this->intUid);
					$arrTaskCommend = array();
					if(empty($arrFavorClass)){
						$objExpServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Exp');
						$arrTopClass = $objExpServ->getUserTopClass($this->intUid);
						if(!empty($arrTopClass)){
							foreach ($arrTopClass as $class){
								if(count($arrTaskCommend)>=Exp_Data_Constant::USERINFO_TASK_COMMEND){
									break;
								}
								$arrTask = $objTaskApi->getTaskSearch("",$class['cid'],0);
								foreach($arrTask['task'] as $task){
									$arrTaskCommend[] = task;
									if(count($arrTaskCommend)>=Exp_Data_Constant::USERINFO_TASK_COMMEND){
										break;
									}
								}
							}
						}
					}else{
						foreach ($arrFavorClass as $class){
							if(count($arrTaskCommend)>=Exp_Data_Constant::USERINFO_TASK_COMMEND){
								break;
							}
							$arrTask = $objTaskApi->getTaskSearch("",$class['cid'],0);
							foreach($arrTask['task'] as $task){
								$arrTaskCommend[] = $task;
								if(count($arrTaskCommend)>=Exp_Data_Constant::USERINFO_TASK_COMMEND){
									break;
								}
							}
						}
					}
					if(count($arrTaskCommend)==0){
						$arrTask = $objTaskApi->getTaskSearch("",0,0);
						foreach($arrTask['task'] as $task){
							$arrTaskCommend[] = $task;
							if(count($arrTaskCommend)>=Exp_Data_Constant::USERINFO_TASK_COMMEND){ 
								break;
							}
						}
					}
					$this->arrResult['data']['taskList'] = $arrTaskCommend;
					$this->arrResult['data']['listScale'] = Exp_Data_Constant::USERINFO_TASK_COMMEND;
					$this->arrResult['data']['total'] = Exp_Data_Constant::USERINFO_TASK_COMMEND;
				}elseif($intType == Exp_Data_Constant::MISSION_TYPE_MONTH){//月度任务
					$arrReceiveMission = $objMissionApi->getMission('getMissionByType',0,0,
						Exp_Data_Constant::MISSION_TYPE_MONTH);
					$this->arrResult['data']['missionList'] = $arrReceiveMission;
				}else{
					Exp_Common_Tools::redirect(Exp_Data_Constant::$REDIRECT_URL_USER_UI);
				}
				break;
			case Exp_Data_Constant::USERINFO_END_MISSION:
				if($intType == Exp_Data_Constant::MISSION_TYPE_DAILY){//日常任务
					$arrTaskList = $objTaskApi->getTaskInfo("getUserTasks",$this->intUid,0,0,0,$this->intPn,
						Exp_Data_Constant::TASK_STATUS_FINISHED,Exp_Data_Constant::TASK_TYPE_QUERY);
					$this->arrResult['data']['taskList'] = $arrTaskList;
					$this->arrResult['data']['listScale'] = Exp_Data_Constant::TASK_NUCLIST_NUM_PERPAGE;
					$this->arrResult['data']['total'] = $objTaskApi->getTaskInfo("getUserTaskNum",$this->intUid,
						0,0,0,Exp_Data_Constant::TASK_STATUS_FINISHED,Exp_Data_Constant::TASK_TYPE_QUERY);
				}elseif($intType == Exp_Data_Constant::MISSION_TYPE_MONTH){//月度任务{
					$arrEndMission = $objMissionApi->getMission('getMissionList',0,$this->intUid,
						Exp_Data_Constant::MISSION_TYPE_MONTH,Exp_Data_Constant::USERINFO_END_MISSION,$this->intPn);
					$this->arrResult['data']['missionList'] = $arrEndMission;
					$this->arrResult['data']['listScale'] = $arrMissionConf['mission_uc_end_mission_list_num'];
					
					$this->arrResult['data']['total'] = $objMissionApi->getMission('getMissionListNum',0,$this->intUid,
						Exp_Data_Constant::MISSION_TYPE_MONTH,Exp_Data_Constant::USERINFO_END_MISSION);
				}else{
					Exp_Common_Tools::redirect(Exp_Data_Constant::$REDIRECT_URL_USER_UI);
				}
				break;
			default:
				Exp_Common_Tools::redirect(Exp_Data_Constant::$REDIRECT_URL_USER_UI);
				return false;
				break;
			}
			$this->arrResult['data']['type'] = $intType;
			$this->arrResult['data']['state'] = $intState;
			$this->arrResult['data']['currentPn'] = $this->intPn;
			break;
        case 'favorexp':
        	$arrFavorTmpList = array();
            $favor_num = 0;
            $total = 0;

            $objUscoreServ = Bd_LayerProxy::getProxy("Exp_Service_Data_Uscore");
            $objFavorServ = Bd_LayerProxy::getProxy("Exp_Service_Data_Favor");
            $objFcrypt = new Exp_Common_Fcrypt;
            $arrUScore = $objUscoreServ->getUscoreAttr($this->intUid);
            
            if( ($favor_num = $arrUScore['favor_num']) !== 0 ){
                $arrFavorTmpList = $objFavorServ->getFavorExpByUid($this->intUid,$this->intPn);
				$arrMyFavorList = array();
                foreach($arrFavorTmpList as $arr){
                    $arr['eidEnc'] = $objFcrypt->getEncodeStrById(intval($arr['eid']));
                    $arr['uid'] = $arr['uid'];
                    if(intval($arr['main_status']) === 2){
                        $arr['isDelete'] = 0;
                    }else{
                        $arr['isDelete'] = 1;
                    }
                    $arr['uname'] = $arr['uname'];
                    $arr['title'] = $arr['title'];
                    $arr['picEnc'] = $arr['firstimg'];
                    $arrJson = json_decode($arr["abstract"], true);
                    if(!is_array($arrJson)){
                        return '';
                    }
                    $strPlaintext = '';
                    foreach($arrJson as $dkey => $ditem){
                        foreach($ditem['items'] as $ikey => $item ){
                            $strPlaintext .= $item['text'];
                        }
                    }
                    $abstract = html_entity_decode(trim(strip_tags($strPlaintext)), ENT_QUOTES, 'UTF-8');
                    $abstract = mb_strcut($abstract, 0, 190, 'utf-8');
                    str_replace(iconv("GBK","UTF-8//IGNORE",' '),'',$abstract);      
                    $arr['summary'] = trim($abstract);

                    $arr['favorTime'] = $arr['favor_time'];
                    $arr['createTime'] = $arr['audit_time'];
                
                    $arrMyFavorList[] = $arr;
                }
            }
            $this->arrResult['data']['listScale'] = Exp_Data_Constant::USERINFO_FAVOR_PERPAGE;
	        $this->arrResult['data']['currentPn'] = $this->intPn;
	        $this->arrResult['data']['total'] = $favor_num;
	        $this->arrResult['data']['favorNum'] = $favor_num;
	        $this->arrResult['data']['favorList'] = $arrMyFavorList;
	        break;
	        
	    case 'author':
            if($intType==0){
                $intType = 1;
            }
            $objAuthorServ = Bd_LayerProxy::getProxy("Service_Data_AuthorApi");
            $resParam = array(
                'uid' => $this->intUid,
            );
            $arrAuthor = $objAuthorServ->getAuthorApi('getAuthorInfoByUid',$resParam);
            if(empty($arrAuthor)){
                $this->intErrNo = Exp_Data_Errno::INVALID_REQUEST_PARAM_ERR;
                Bd_Log::Warning($this->intUid.' is not an pay author');
                Exp_Common_Tools::redirect(Exp_Data_Constant::$REDIRECT_URL_USER_UI);
                return false;
            }
            if ($intType > Exp_Data_Constant::USERINFO_TAB_EXP_MAX || 
                $intType < Exp_Data_Constant::USERINFO_TAB_EXP_MIN) {
                 Bd_Log::warning('redirect to other page because of invalid tab num.');
                 Exp_Common_Tools::redirect(Exp_Data_Constant::$REDIRECT_URL_USER_UI);
                 return false;
            }

            $objExpServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Exp');
            $arrResult = array();
            switch ($intType) {
                case Exp_Data_Constant::USERINFO_TAB_PAYEXPLIST:
                	$resParam = array(
                        'uid' => $this->intUid,
                	    'pn'  => $this->intPn
                    );
                    $res = $objAuthorServ->getPayExpApi('getPayExpListByUid',$resParam);
                    $objClassModel = new Exp_Service_Data_Class();
                    $arrRes = array();
                    if(!empty($res)){
                        foreach($res as $arr){
                            $arrTemp = $arr;
		                    $arrClassInof = $objClassModel->getCatInfoByCid($arr['cid']);
		                    $arrTemp['cname'] = $arrClassInof['cname'];
                            $arrRes[] = $arrTemp;
		                }
                    }
                    $arrResult = $arrRes;
                    break;
                case Exp_Data_Constant::USERINFO_TAB_PAYREC:
                	$resParam = array(
                        'uid' => $this->intUid,
                	    'pn'  => $this->intPn
                    );
                    $arrResult = $objAuthorServ->getPayExpApi('getPayRecListByUid',$resParam);
                    break;
                case Exp_Data_Constant::USERINFO_TAB_AUTHOR:
                    $arrResult = $arrAuthor[0];
                    break;
                case Exp_Data_Constant::USERINFO_TAB_BONUS;
                    $resParam = array(
                        "uid" => $this->intUid,
                    );
                    $levelRes = $objAuthorServ->getAuthorApi('getAuthorLevel',$resParam);
                    $resParam = array(
                        "uid" => $this->intUid,
                        "pn" => $levelRes['active_time']
                    );
                    $tempResult = $objAuthorServ->getPayExpApi('getBonusExpNumByUid',$resParam);
                    $resParam = array(
                        "uid" => $this->intUid,
                    	"pn" => 1
                    );
                    $arrResult['level']    = $levelRes['level'];
                    $arrResult['perBonus'] = $levelRes['perBonus'];
                    $arrResult['original'] = $tempResult['original'];
                    $arrResult['task']     = $tempResult['task'];
                    $payexp = $objAuthorServ->getPayExpApi('getPayExpNumByUid',$resParam);
                    $arrResult['payexp'] = intval($payexp);
                    break;
            }
            $resParam = array(
                "uid" => $this->intUid,
            );
            $this->arrResult['data']['payExpTotal'] = $objAuthorServ->getPayExpApi('getPayExpNumByUid',$resParam);;
            $this->arrResult['data']['payRecordTotal'] = $objAuthorServ->getPayExpApi('getPaidRecSumByUid',$resParam);
            $this->arrResult['data']['listScale'] = Exp_Data_Constant::USERINFO_PAY_EXP_PERPAGE;
            $this->arrResult['data']['currentPn'] = $this->intPn;
            $this->arrResult['data']['type'] = $intType;
            $this->arrResult['data']['resultList'] = $arrResult;
            break;
	    case 'notice':
	     	$intMainType = intval($arrInput['maintype']);
	     	$intSubType = intval($arrInput['subtype']);
	     	$intOffset = intval($arrInput['pn']);
	     	$intRn = intval($arrInput['rn']);
	     	if(empty($intRn)){
	     		$intRn = 25;
	     	}
	     	$objNoticeApi = Bd_LayerProxy::getProxy("Service_Data_NoticeApi");
	     	$arrNotices = $objNoticeApi->getSystemNotice($this->intUid,$intMainType,$intSubType,$intOffset,$intRn);
	     	$this->arrResult['data']['notices']['maintype'] = $intMainType;
	     	$this->arrResult['data']['notices']['currentPn'] = $intOffset;
	     	$this->arrResult['data']['notices']['total'] = $arrNotices['counts'][$intMainType]['total_count'];
	     	$this->arrResult['data']['notices']['data'] = $arrNotices['notices'];
	     	$this->arrResult['data']['notices']['typeNum']['all'] = $arrNotices['counts'][0]['total_count'];
    		$this->arrResult['data']['notices']['typeNum']['notice'] = $arrNotices['counts'][5]['total_count'];
    		$this->arrResult['data']['notices']['typeNum']['author'] = $arrNotices['counts'][7]['total_count'];
    		$this->arrResult['data']['notices']['typeNum']['audit'] =  $arrNotices['counts'][4]['total_count'];
    		$this->arrResult['data']['notices']['typeNum']['good'] = $arrNotices['counts'][6]['total_count'];
	     	break;
	    case 'resume':
            break;
        case 'income':
            if(!$intType){
                 $intType = 1;
            }
            $objIncomeServ = Bd_LayerProxy::getProxy('Service_Data_Income');
            $intUserId = $this->intUid;//759108415;
            $arrOutput = array();
            $intNumPerPage = Exp_Data_Constant::USERINFO_INCOME_NUM_PERPAGE;
            switch ($intType) {
                case Exp_Data_Constant::USERINFO_TAB_INCOME_OVERVIEW:
                    //获取用户收入概况

                    //查询用户信息
                    $arrUserInfo = $objIncomeServ->getUserProfileByUid($intUserId);
                    //查询用户现金信息
                    $arrCashInfo = $objIncomeServ->getUserCashByUid($intUserId);


                    //用户名
                    $arrOutput['incomeOverview']['uname'] = $arrUserInfo['uname'];
                    //真实姓名
                    $arrOutput['incomeOverview']['realName'] = $arrUserInfo['real_name'];
                    //用户付费经验篇数
                    $arrOutput['incomeOverview']['payExpNum'] = $objIncomeServ->getUserPayExpNum($intUserId);//$arrUserInfo['exp_count'];
                    //用户当前状态: 0 没有加入分成计划 1 加入了分成计划 2 提交审核 3 资料审核完成 
                    $arrOutput['incomeOverview']['status'] = $arrUserInfo['status'];
                    //用户是否冻结
                    $arrOutput['incomeOverview']['isFreeze'] = $arrUserInfo['is_freeze'];
                    //当前总收入
                    $arrOutput['incomeOverview']['totalIncome'] = sprintf('%.3f', $arrCashInfo['amount']);

                    //历史总收入
                    $arrOutput['incomeOverview']['hisTotalIncome'] = sprintf('%.3f', $arrCashInfo['amount_all']);
                    //历史总支取
                    $arrOutput['incomeOverview']['hisTotalDrawup'] = sprintf('%.3f', $arrCashInfo['amount_drawup']);

                    //银行
                    $arrOutput['incomeOverview']['bank'] = $arrUserInfo['bank'];
                    $arrOutput['incomeOverview']['subBank'] = $arrUserInfo['sub_bank'];
                    $arrOutput['incomeOverview']['bankCardNo'] = $arrUserInfo['bank_card_no'];


                    //是否等待支取, 判断是否有支取申请
                    
                    //支取申请记录
                    $arrWithdrawApply = $objIncomeServ->getUserWithdrawApplyByUid($intUserId, 0);
                    if(count($arrWithdrawApply) > 0){
                        $arrOutput['incomeOverview']['isWaitingDraw'] = 1;
                        //本次提取金额(如果在提取等待中)
                        $arrOutput['incomeOverview']['lastDrawupApplyAmount'] = $arrWithdrawApply[0]['amount'];
                    }else{
                        $arrOutput['incomeOverview']['isWaitingDraw'] = 0;
                        $arrOutput['incomeOverview']['lastDrawupApplyAmount'] = 0;
                    }
                    //邀请码信息
                    $invitationCodeInfo = $objIncomeServ->getUserInvitationQuota($intUserId);
                    foreach($invitationCodeInfo as $arrItem){
                        $arrOutput['invitationCodeInfo'][] = array(
                            'invitationType' => $arrItem['invitation_type'],
                            'amount' => $arrItem['amount'],
                            'amountLeft' => $arrItem['amount_left'],
                            'isFreeze' => $arrItem['is_freeze'],
                            'cpm' => $this->_arrConf['cpm']['vip'.$arrItem['invitation_type']],
                            'createTime' => date('Y-m-d', $arrItem['create_time'])
                        );
                    }
                    break;
                case Exp_Data_Constant::USERINFO_TAB_INCOME_DAILYLIST:
                    //历史总收入 
                    $arrCashInfo = $objIncomeServ->getUserCashByUid($intUserId);
                    $arrOutput['hisTotalIncome'] = sprintf('%.3f', $arrCashInfo['amount']);
                    
                    //用户每日收入列表
                    $arrUserIncomeLog = $objIncomeServ->getUserIncomeLogByUid($intUserId, $this->intPn, $intNumPerPage);
                    //每日明细数组
                    foreach($arrUserIncomeLog as $arrItem){
                        $arrOutput['dailyIncome'][] = array(
                            'amount' => sprintf('%.3f', $arrItem['income']),
                            'createTime' => date('Y-m-d', $arrItem['date']),
                            'type'   => intval($arrItem['type']),    
                        );
                    }

                    //当前页
                    $arrOutput['currentPn'] = $this->intPn;
                    //总条数
                    $arrOutput['total'] = $objIncomeServ->getUserIncomeLogNum($intUserId);
                    //每页显示条数
                    $arrOutput['rn'] = $intNumPerPage;
                    break;
                case Exp_Data_Constant::USERINFO_TAB_INCOME_EXPLIST:

                    $objFcrypt = new Exp_Common_Fcrypt();
                    $objExpServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Exp');

                    //付费经验收入列表
                    $arrExpIncome = $objIncomeServ->getExpIncomeByUid($intUserId, $this->intPn, $intNumPerPage);
                    foreach($arrExpIncome as $arrItem){
                        $expDetail = $objExpServ->getExpDetailByEid($arrItem['eid']);
                        $arrOutput['payexpList'][] = array(
                            'title' => $expDetail['title'],
                            'createTime' => date('Y-m-d', $expDetail['create_time']),
                            'eidEnc' => $objFcrypt->getEncodeStrById(intval($arrItem['eid'])),
                            'income' => sprintf('%.3f', $arrItem['income']),
                            'lastIncome' => sprintf('%.3f', $arrItem['last_income'])
                        );
                    }
                    //$arrOutput['payexpList'] = $arrExpIncome;

                     //当前页
                    $arrOutput['currentPn'] = $this->intPn;
                    
                    //查询用户信息
                    $arrUserInfo = $objIncomeServ->getUserProfileByUid($intUserId);
                    //总条数
                    $arrOutput['total'] = $objIncomeServ->getUserPayExpNum($intUserId);//$arrUserInfo['exp_count'];
                    //每页显示条数
                    $arrOutput['rn'] = $intNumPerPage;
                    break;
                case Exp_Data_Constant::USERINFO_TAB_INCOME_WITHDRAWALS:
                    //提现记录列表

                    $arrWithdrawLog = $objIncomeServ->getUserWithdrawListByUid($intUserId, $this->intPn, $intNumPerPage);

                    foreach($arrWithdrawLog as $arrItem){
                        $arrOutput['withdrawalsList'][] = array(
                            'amount' => sprintf('%.3f', $arrItem['amount']),
                            'amount_after' => sprintf('%.3f', $arrItem['amount_after']),
                            'createTime' => date('Y-m-d', $arrItem['create_time'])
                        );
                    }
                    //当前页
                    $arrOutput['currentPn'] = $this->intPn;
                    //总条数
                    $arrOutput['total'] = $objIncomeServ->getUserWithdrawLogNum($intUserId);
                    //每页显示条数
                    $arrOutput['rn'] = $intNumPerPage;

                    break;
                case Exp_Data_Constant::USERINFO_TAB_INCOME_ACCOUNT:
                    //查询用户信息
                    $arrUserInfo = $objIncomeServ->getUserProfileByUid($intUserId);
                    $arrOutput['accountInfo']['uname'] = $arrUserInfo['uname'];
                    $arrOutput['accountInfo']['realName'] = $arrUserInfo['real_name'];
                    //用户付费经验篇数
                    $arrOutput['accountInfo']['payExpNum'] = $arrUserInfo['exp_count'];
                    //用户当前状态: 0 没有加入分成计划 1 加入了分成计划 2 提交审核 3 资料审核完成 
                    $arrOutput['accountInfo']['status'] = $arrUserInfo['status'];
                    //用户是否冻结
                    $arrOutput['accountInfo']['isFreeze'] = $arrUserInfo['is_freeze'];
                    $arrOutput['accountInfo']['idCard'] = $arrUserInfo['id_card'];
                    $arrOutput['accountInfo']['idCardImg1'] = $arrUserInfo['id_card_img1'] ? $arrUserInfo['id_card_img1'] : '';
                    $arrOutput['accountInfo']['idCardImg2'] = $arrUserInfo['id_card_img2'] ? $arrUserInfo['id_card_img2'] : '';
                    $arrOutput['accountInfo']['bank'] = $arrUserInfo['bank'];
                    $arrOutput['accountInfo']['subBank'] = $arrUserInfo['sub_bank'];
                    $arrOutput['accountInfo']['bankCardNo'] = $arrUserInfo['bank_card_no'];
                    //查询用户现金信息
                    $arrCashInfo = $objIncomeServ->getUserCashByUid($intUserId);
                    $arrOutput['accountInfo']['totalIncome'] = $arrCashInfo['amount'];
                    //判断用户资质审核是否通过，否，展示其原因
                    $arrOutput['accountInfo']['reason'] = '';
                    if(intval($arrUserInfo['status']) === 4){
                       $arrReason = $objIncomeServ->getAdminReason($intUserId, 1);
                       $arrOutput['accountInfo']['reason'] = $arrReason['desc'];
                    }
                    break;
            }
            $arrOutput['tabType'] = $intType;
            $this->arrResult['data']['income'] =$arrOutput;
            /*
            echo "<textarea rows='20' cols='150'>";
            echo json_encode($arrOutput);
            //echo json_encode($this->arrResult);
            echo "</textarea>";
             */

            break;
         
        default:
            Exp_Common_Tools::redirect(Exp_Data_Constant::$REDIRECT_URL_USER_UI);
            return false;
            break;
        }
		return $this->arrResult;
    }
	
	public function handleTaskData($arrTaskList){
		if(!empty($arrTaskList)){
			$objTaskApi = $objTaskApi = Bd_LayerProxy::getProxy('Exp_Service_Data_TaskApi');
			foreach ($arrTaskList as $arr){
				$intDraftid = $objTaskApi->getTaskInfo("getDidByTid",$this->intUid,$arr['tid']);
				if($intDraftid){
					$arr['did'] = $intDraftid;
				}
				if($arr['eid']){
					$objFcrypt = new Exp_Common_Fcrypt;
                    $arr['eid'] = $objFcrypt->getEncodeStrById(intval($arr['eid']));
				}
				if($arr['state'] == 2 && !$arr['eid']){
					$arr['finish_time'] = intval((Exp_Data_Constant::TASK_TIMEOUT-time()+$arr['receive_time'])/3600) + 1;
					if($arr['finish_time']<0){
						$arr['finish_time'] = 0;
					}
				}elseif($arr['state'] == 2 && $arr['eid']){
					$arr['finish_time'] = intval((Exp_Data_Constant::TASK_FAILURE-time()+$arr['receive_time'])/3600) + 1;
					if($arr['finish_time']<0){
						$arr['finish_time'] = 0;
					}
				}
				$arrUserTask[] = $arr;
			}
		}else{
			return array();
		}
		return $arrUserTask;
	}
}
?>
