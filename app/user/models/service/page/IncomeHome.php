<?php
/**
 * @name Service_Page_IncomeHome
 * <AUTHOR>
 */
class Service_Page_IncomeHome {
    private $_arrConf;
    private $_arrLoginUser;

    protected $intUid;
    protected $arrResult;
    
    public function __construct(){
		$this->_arrConf = Bd_Conf::getConf('/app/user/user/');
	    $this->_arrLoginUser = Saf_SmartMain::getUserInfo();
        $this->objIncomeServ = Bd_LayerProxy::getProxy('Service_Data_Income');
        if($this->_arrLoginUser['uid']){
            $this->intUid = $this->_arrLoginUser['uid'];
        }
        $this->arrResult = array(
        	'errno' => 0,
            'data'   => array(),
		);
    }
    
    public function execute($arrInput){
        $this->arrResult['data']['UserInfo'] = $this->_arrLoginUser;

        if($this->intUid>0){
            $arrUserProfile = $this->objIncomeServ->getUserProfileByUid($this->intUid);
            if(empty($arrUserProfile)){
                $arrUserProfile = array(
                    'status' => 0
                );
            }
            $this->arrResult['data']['UserProfile'] = $arrUserProfile;

            //是否是签约作者
            $objAuthorServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Author');
            $boolIsPayAuthor = $objAuthorServ->checkAuthorState($this->intUid);
            $this->arrResult['data']['isPayAuthor'] = $boolIsPayAuthor ? 1 : 0;
        }
        /*
        echo "<textarea rows='40' cols='150'>";                                                                                                          
        echo @json_encode($this->arrResult);
        echo "</textarea>";
         */
		return $this->arrResult;
    }
}
?>
