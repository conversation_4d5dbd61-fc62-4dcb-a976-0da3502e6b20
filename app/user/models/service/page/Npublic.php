<?php
/**
 * @name Service_Page_NPublic
 * <AUTHOR>
 */
class Service_Page_Npublic {
    private $_arrConf;
    private $_arrLoginUser;

    protected $intUid;
    protected $intType;
    protected $intPn;
    protected $arrResult;
    
    public function __construct(){
		$this->_arrConf = Bd_Conf::getConf('/app/user/user/');
	    $this->_arrLoginUser = Saf_SmartMain::getUserInfo();

	    $this->intUid = 0;
	    $this->intType = 0;
	    $this->intPn = 0;
        $this->arrResult = array(
        	'errno' => 0,
            'data'   => array(),
		);

		$this->objUserInfoServ = Bd_LayerProxy::getProxy('Exp_Service_Data_UserInfo');
        $this->objExp = Bd_LayerProxy::getProxy('Exp_Service_Data_Exp');
        $this->objFcrypt = new Exp_Common_Fcrypt();
        $this->objFavor = Bd_LayerProxy::getProxy('Exp_Service_Data_Favor');
    }
    
    public function execute($arrInput){
		$strPage = strip_tags($arrInput['page']);
		$strUn = strip_tags($arrInput['un']);
		$this->intType = intval($arrInput['type']);
		$this->intPn = intval($arrInput['pn']);
		if ($this->intPn < 0) $this->intPn = 0;
		
		//检查输入用户名是否合法
		$objUserUtils = new Exp_Common_UserUtils();
		$res = $objUserUtils->getUidByUname($strUn);
		if($res <= 0){
			Bd_Log::warning('redirect to other page because can\'t get user info for un: ' .$strUn);
			Exp_Common_Tools::redirect(Exp_Data_Constant::$REDIRECT_URL_USER_UI);
			return false;	
		}
		$this->intUid = $res;

		if (!$intType) $intType = '1';
		//获取用户信息
		//$objUserInfoServ = Bd_LayerProxy::getProxy('Exp_Service_Data_UserInfo');
		$this->arrUserDetail = $this->objUserInfoServ->getUserDetailInfo($this->intUid);
    	if (empty($this->arrUserDetail)) {
            $this->arrUserDetail['uname'] = $this->_arrLoginUser['uname'];
            $this->arrUserDetail['portrait'] = $this->objUserInfoServ->getUserPassportPic($this->intUid,$this->arrUser['userName']);
        }
        //屏蔽恶意用户
        $arrSiteBlack = Exp_Data_PublicBlack::$userInfoSite;
		$arrContentBlack = Exp_Data_PublicBlack::$userInfoContent;
        foreach($arrSiteBlack as $black){
            if(preg_match("/".$black."/i",$this->arrUserDetail['site'])){
                Exp_Common_Tools::redirect(Exp_Data_Constant::$REDIRECT_URL_USER_UI);
                return false;
		    }
		}
		foreach($arrContentBlack as $black){
		    if(preg_match("/".$black."/i",$this->arrUserDetail['content'])){
		        Exp_Common_Tools::redirect(Exp_Data_Constant::$REDIRECT_URL_USER_UI);
		        return false;
		    }
		}
		$objUcrypt = new Exp_Common_Ucrypt();
        $this->arrUserDetail['userEnc'] = $objUcrypt->ucrypt_encode($this->intUid,$strUn);
        //Fe Fis 重构引入变量，判断名片页是否是用户本人的。
        if($this->intUid == $this->_arrLoginUser['uid']){
        	$this->arrUserDetail['isMyself'] = 1;
        }else{
        	$this->arrUserDetail['isMyself'] = 0;
        }
        //获取右侧数据
        //获取热门人物cms
        $objUserCenterServ = Bd_LayerProxy::getProxy('Service_Data_UserCenter');
        $strHotPersonHtml = $objUserCenterServ->getHotPersonHtml();
        if ($strHotPersonHtml == false) {
            Bd_Log::warning('get empty data: hot person html');
            $strHotPersonHtml = ''; 
        }
        //获取热门活动cms
        $strHotActiveHtml = $objUserCenterServ->getHotActiveHtml();
    	if ($strHotActiveHtml == false) {
            Bd_Log::warning('get empty data: hot Active html');
            $strHotActiveHtml = ''; 
        }
        //获取感兴趣分类最新经验列表cms
        $arrLatestExpList = $objUserCenterServ->getLatestExp();
        $this->arrResult['data']['hotPersonHTML'] = $strHotPersonHtml;
        $this->arrResult['data']['hotActiveHTML'] = $strHotActiveHtml;
        $this->arrResult['data']['LatestExpList'] = $arrLatestExpList;
        
        if($strPage === ''){
           $strPage = 'index';
        
        }
        /*
         *个人中心改版添加， 目前只有首页与经验列表页用到，
         *后续全部改版完，放开限制
         */
        if($strPage == 'index' 
            || $strPage == 'expList') {

            $this->objFollow = Bd_LayerProxy::getProxy('Exp_Service_Data_Follow');
            //获取关注的用户列表
            $this->arrResult['data']['followingList'] = $this->getFollowingList();
            //获取粉丝列表
            $this->arrResult['data']['followerList'] = $this->getFollowerList();
            //登录用户是否关注此作者
            if($this->_arrLoginUser['uid']){
                $this->arrUserDetail['isFollowed'] = $this->objFollow->hasUserFollowUser($this->_arrLoginUser['uid'], $this->intUid); 
            }else{
                $this->arrUserDetail['isFollowed'] = 0;
            }
        }

        switch($strPage) {

        case 'index':
            /*
             * modified by wkeke
             * for:
             * 个人中心改版,
             * 摒弃按分类出feed。
             *
             */
        	$objFeedServ = Exp_Common_RalRpcFactory::getRalRpc('FeedUi');

            $arrReq['uids'] = $this->intUid;
            $arrReq['types'] = $this->_arrConf['feed']['type'];//1;//$strTypes;
            $arrReq['start'] = $this->intPn;//0;
            $arrReq['count'] = min($this->_arrConf['feed']['perpage'],$this->_arrConf['feed']['maxnum']);
            $arrRet = $objFeedServ->getFeed($arrReq);
            if (empty($arrRet) || !isset($arrRet['list']) || empty($arrRet['list'])) {
            	$this->arrResult['data']['FeedList'] = array();
                break;
            }

            foreach ($arrRet['list'] as $item) {
                $item = $item[0];
                $arrFeed = array();
                $arrOther = $item['other'];

                if($arrOther['eidEnc']){
                    //获取eid
                    $intEid = 0;
                    $this->objFcrypt->getIdByEncodeStr($arrOther['eidEnc'],&$intEid);
                    //获取经验详情
                    $arrExpAttr = $this->objExp->getExpAttrByEid($intEid); 

                }
                /*
                 * 根据item['id1'] 区分feed类别：
                 * {
                 *   id1_exp  : 1, 
                 *   id1_comment : 2, 
                 *   id1_follow : 3
                 * }
                 */
                switch(intval($item['id1'])){
                case $this->_arrConf['feed']['id1_exp'] : 
                    #exp
                    if ($arrOther['isVersion'] === 1) {
                        $arrFeed['type'] = $this->_arrConf['feedtype']['version'];
                    } else {
                        $arrFeed['type'] = $this->_arrConf['feedtype']['exp'];
                    }
                    $arrFeed['un'] = $arrOther['un'];
                    //$arrFeed['cid'] = $arrOther['cid'];
                    $arrFeed['cname'] = $arrOther['cname'];
                    $arrFeed['createTime'] = $arrOther['createTime'];
                    $arrFeed['eidEnc'] = $arrOther['eidEnc'];
                    $arrFeed['title'] = $arrOther['title'];
                    $arrFeed['picEnc'] = $arrOther['picEnc'];
                    $arrFeed['summary'] = $arrOther['abstract'];
                    break;
                case $this->_arrConf['feed']['id1_comment'] :
                    #comment
                    $arrFeed['type'] = $this->_arrConf['feedtype']['comment'];
                    $arrFeed['un'] = $arrOther['un'];
                    $arrFeed['eidEnc'] = $arrOther['eidEnc'];
                    $arrFeed['title'] = $arrOther['title'];
                    $arrFeed['createTime'] = $arrOther['createTime'];
                    $arrFeed['commentContent'] = $arrOther['abstract'];
                    //获取该经验的作者
                    $arrFeed['expAuthor'] = $arrExpAttr['uname'];     
                    //获取该经验创建时间
                    $arrFeed['expCreateTime'] = $arrExpAttr['create_time'];

                    //获取该经验的摘要summary
                    $arrContent = $this->objExp->getEContentAttrByEid($intEid);
                    $strSummary = $this->getPlainTextFromJsonContent($arrContent['content']);
                    $strSummary = strip_tags($strSummary);
                    $arrHtmlCharacter = array(
                        '&nbsp;' => '', 
                        '&lt;'   => '', 
                        '&gt;'   => '', 
                        '&amp;'  => '', 
                    );  
                    $strSummary = strtr($strSummary,$arrHtmlCharacter);
                    $strSummary = str_replace(iconv("GBK","UTF-8//IGNORE",'　'),'',$strSummary);//这里删除的是全角空格
                    $arrFeed['summary'] = $strSummary;

                    break;
                case $this->_arrConf['feed']['id1_follow'] : 
                    #follow
                    $arrFeed['type'] = $this->_arrConf['feedtype']['follow'];
                    $arrFeed['un'] = $arrOther['un'];
                    $arrFeed['followUn'] = $arrOther['followUn'];
                    //$arrFeed['intro'] = $arrOther['intro'];
                    $arrFeed['createTime'] = $arrOther['createTime'];
                    //$arrFeed['portrait'] = $arrOther['portrait'];
                    break;
                }
                if($arrFeed['un']){
                    if($intEid){
                        //好评数据
                        $arrFeed['feedbackSuc'] = $arrExpAttr['feedbackSuc'];     
                        //获取经验被多少人收藏
                        $arrFeed['expFavorNum'] = $arrExpAttr['expFavorNum'];     
                        //判断登录用户是否收藏该篇经验；
                        $arrFeed['isFavorited'] = $this->objFavor->hasUserFavorExp($this->_arrLoginUser['uid'], $intEid); 
                    }
                    $this->arrFeedList[] = $arrFeed;
                }
            }// foreach end
            $arrFeedData['data'] = $this->arrFeedList;
            $arrFeedData['currentPn'] = $this->intPn;
            $arrFeedData['total'] = $arrRet['total'];
            $this->arrResult['data']['FeedList'] = $arrFeedData;

            break;

        case 'vote':
        	$objVoteServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Vote');
        	$objUserServ = Bd_LayerProxy::getProxy('Exp_Service_Data_UserInfo');
            $ret = $objVoteServ->getVoterListByUid($this->intUid, $this->intPn);
            foreach ($ret as $nouse => $vote ) {
		        $arrVoter[$vote['vote_date']][] = array(
		            'portrait' => $objUserServ->getUserPassportPic($vote['voter_uid'],$vote['uname']),
		            'uname' => $vote['uname'],
		            'votedTime' => $vote['create_time'],
		        );
            }
            $arrVoteList['data'] = $arrVoter;
            $arrVoteList['currentPn'] = $this->intPn;
            $arrVoteList['total'] = $this->arrUserDetail['mVotedNum'];
            $this->arrResult['data']['VoteList'] = $arrVoteList;
            break;

        case 'following':
            $objFollowServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Follow');
            $objAuthWriter = Bd_LayerProxy::getProxy('Exp_Service_Data_AuthWriter');
            $objMyExpServ = Bd_LayerProxy::getProxy('Exp_Service_Data_MyExp');
            $arrRet = $objFollowServ->getFollowListByUid($this->intUid, $this->intPn);
            foreach ($arrRet as $followUid) {
                $followPerson = array();
                $ret = $this->objUserInfoServ->getUserDetailInfo($followUid['follow_uid']);
                if (empty($ret)) {
                    $followPerson['uname'] = "";
                    $followPerson['followedNum'] = "";
                    $followPerson['introduction'] = "";
                    $followPerson['portrait'] = "";
                }
                $followPerson['uname'] = $ret['uname'];
                $followPerson['followedNum'] = $ret['followed_num'];
                $followPerson['introduction'] = $ret['introduction'];
                $followPerson['portrait'] = $ret['portrait'];
                $authWriter = $objAuthWriter->getAuthWriterInfoByUid($followUid['follow_uid']);
                $followPerson['title'] = empty($authWriter) ? '' : $authWriter['title'];
                $ret1 = $objMyExpServ->getNewExpByUid($followUid['follow_uid']);
                if (empty($ret1[0])) {
                    $followPerson['latestExpTitle'] = '';
                    $followPerson['latestExpEidEnc'] = '';
                } else {
                    $objFcrypt = new Exp_Common_Fcrypt;
                    $followPerson['latestExpEidEnc'] = $objFcrypt->getEncodeStrById($ret1[0]['eid']);
                    $followPerson['latestExpTitle'] = $ret1[0]['title'];
                }
                $arrFollowList[] = $followPerson;
            }
            $arrFollowList['data'] = array_slice($arrFollowList,0,Exp_Data_Constant::USERINFO_FOLLOWED_PERPAGE);
            $arrFollowList['currentPn'] = $this->intPn;
            $arrFollowList['total'] = $this->arrUserDetail['followNum'];
            $this->arrResult['data']['followList'] = $arrFollowList;
            break;

        case 'follower':
            $objFollowServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Follow');
            $objAuthWriter = Bd_LayerProxy::getProxy('Exp_Service_Data_AuthWriter');
            $objMyExpServ = Bd_LayerProxy::getProxy('Exp_Service_Data_MyExp');
            $arrRet = $objFollowServ->getFollowedListByUid($this->intUid, $this->intPn);
            foreach ($arrRet as $followUid) {
                $followPerson = array();
                $ret = $this->objUserInfoServ->getUserDetailInfo($followUid['followed_uid']);
                if (empty($ret)) {
                    $followPerson['uname'] = "";
                    $followPerson['followedNum'] = "";
                    $followPerson['introduction'] = "";
                    $followPerson['portrait'] = "";
                }
				//如果用户名为空，从pass获取
				if(!empty($ret['uname'])){
					$followPerson['uname'] = $ret['uname'];
				}else{
					$objUserServ = new Exp_Common_UserUtils;
					$arrUserPassInfo = $objUserServ->getInfoByuid(
						intval($followUid['followed_uid']),
						array("username")
					);
					$followPerson['uname'] = iconv('gbk', 'utf-8', $arrUserPassInfo['username']);
				}

                $followPerson['followedNum'] = $ret['followed_num'];
                $followPerson['introduction'] = $ret['introduction'];
                $followPerson['portrait'] = $ret['portrait'];
                $authWriter = $objAuthWriter->getAuthWriterInfoByUid($followUid['followed_uid']);
                $followPerson['title'] = empty($authWriter) ? '' : $authWriter['title'];
                $ret1 = $objMyExpServ->getNewExpByUid($followUid['followed_uid']);
                if (empty($ret1[0])) {
                    $followPerson['latestExpTitle'] = '';
                    $followPerson['latestExpEidEnc'] = '';
                } else {
                    $objFcrypt = new Exp_Common_Fcrypt;
                    $followPerson['latestExpEidEnc'] = $objFcrypt->getEncodeStrById($ret1[0]['eid']);
                    $followPerson['latestExpTitle'] = $ret1[0]['title'];
                }
                $arrFollowList[] = $followPerson;
            }
            $arrFollowList['data'] = array_slice($arrFollowList,0,Exp_Data_Constant::USERINFO_FOLLOWED_PERPAGE);
            $arrFollowList['currentPn'] = $this->intPn;
            $arrFollowList['total'] = $this->arrUserDetail['followedNum'];
            $this->arrResult['data']['followedList'] = $arrFollowList;
            break;
            
        case 'expList':
            $objMyExpServ = Bd_LayerProxy::getProxy('Service_Data_MyExp');
            $arrExpList = $objMyExpServ->getMyExpPassedDetailByUid($this->intUid, $this->intPn);

            foreach ($arrExpList as &$item) {
                $arrExpAttr = $this->objExp->getExpAttrByEid($item['eid']); 
                //好评数据
                $item['feedbackSuc'] = $arrExpAttr['feedbackSuc'];     
                //经验被收藏数
                $item['expFavorNum'] = $arrExpAttr['expFavorNum'];
                //判断登录用户是否收藏该篇经验
                $item['isFavorited'] = $this->objFavor->hasUserFavorExp($this->_arrLoginUser['uid'], $item['eid']); 

            }
            $arrMyExpList['data'] = $arrExpList;
            $arrMyExpList['currentPn'] = $this->intPn;
            $arrMyExpList['total'] = $this->arrUserDetail['expNum'];
            $this->arrResult['data']['ExpList'] = $arrMyExpList;

            break;

        default:
            Exp_Common_Tools::redirect(Exp_Data_Constant::$REDIRECT_URL_USER_UI);
            return false;
            break;
        }
        $this->arrResult['data']['UserDetail'] = $this->arrUserDetail;
        return $this->arrResult;
    }
    /**
     * 获取该用户的关注列表
     * 个人中心改版添加
     *
     * <AUTHOR>
     * @param 
     * @return array following list
     * 
     */
    public function getFollowingList(){
        $objFollowServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Follow');
        $arrRet = $objFollowServ->getFollowListByUid($this->intUid, 0);
        $arrFollowingList = array();
        $arrTmp = array();
        $intListNum = 6;

        if(count($arrRet)>$intListNum){
            $arrRet = array_slice($arrRet, 0, $intListNum);
        }

        foreach ($arrRet as $followUid) {
            $followPerson = array();
            $ret = $this->objUserInfoServ->getUserDetailInfo($followUid['follow_uid']);
            if (empty($ret)) {
                $followPerson['uid'] = '';
                $followPerson['uname'] = '';
                $followPerson['passed'] = '';
                $followPerson['portrait'] = '';
            }
            $followPerson['uid'] = $ret['uid'];
            $followPerson['uname'] = $ret['uname'];
            $followPerson['passed'] = $ret['passed'];
            $followPerson['portrait'] = $ret['portrait'];

            //登录用户是否关注此用户
            if($this->_arrLoginUser['uid']){
                $followPerson['isFollowed'] = $this->objFollow->hasUserFollowUser($this->_arrLoginUser['uid'], $ret['uid']); 
            }else{
                $followPerson['isFollowed'] = 0;
            }


            $arrTmp[] = $followPerson;
        }
        $arrFollowingList['data'] = $arrTmp;//array_slice($arrTmp,0,$intListNum);
        //$arrFollowingList['currentPn'] = $this->intPn;
        //$arrFollowingList['total'] = $this->arrUserDetail['followNum'];

        return  $arrFollowingList;
    }
    /**
     * 获取该用户的粉丝列表
     * 个人中心改版添加
     *
     * <AUTHOR>
     * @param 
     * @return array follower list
     * 
     */
    public function getFollowerList(){
        $objFollowServ = Bd_LayerProxy::getProxy('Exp_Service_Data_Follow');
        $arrRet = $objFollowServ->getFollowedListByUid($this->intUid, 0);
        $arrFollowerList = array();
        $arrTmp = array();
        $intListNum = 8;

        if(count($arrRet)>$intListNum){
            $arrRet = array_slice($arrRet, 0, $intListNum);
        }

        foreach ($arrRet as $followUid) {
            $followPerson = array();
            $ret = $this->objUserInfoServ->getUserDetailInfo($followUid['followed_uid']);
            if (empty($ret)) {
                $followPerson['uid'] = '';
                $followPerson['uname'] = '';
                $followPerson['portrait'] = '';
            }
            $followPerson['uid'] = $ret['uid'];
            $followPerson['portrait'] = $ret['portrait'];
			if(!empty($ret['uname'])){
				$followPerson['uname'] = $ret['uname'];
			}else{
				$objUserServ = new Exp_Common_UserUtils;
				$arrUserPassInfo = $objUserServ->getInfoByuid(
					intval($followUid['followed_uid']),
					array("username")
				);
				$followPerson['uname'] = iconv('gbk', 'utf-8', $arrUserPassInfo['username']);
			}

            $arrTmp[] = $followPerson;
        }
        $arrFollowerList['data'] = $arrTmp;//array_slice($arrFollowList, 0, $intListNum);
        //$arrFollowerList['currentPn'] = 0;//$this->intPn;
        //$arrFollowerList['total'] = $this->arrUserDetail['followedNum'];
        return $arrFollowerList;
    }
    /**
     * 获取一篇经验的摘要
     * 个人中心改版添加
     *
     * <AUTHOR>
     * @param 
     * @return array follower list
     * 
     */
    private function getPlainTextFromJsonContent($strJsonContent){
        $arrJson = json_decode($strJsonContent,true);
        if(!is_array($arrJson)){
            return '';
        }
        $strPlaintext = ''; //过滤后的文本串
        foreach($arrJson as $dkey => $ditem){
            foreach($ditem['items'] as $ikey => $item ){
                $strPlaintext .= $item['text'];
            }
        }
        return $strPlaintext;
    } 

}
?>
