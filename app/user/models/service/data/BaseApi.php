<?php
/**
 * @name Service_Data_AuthorRpc
 * <AUTHOR>
 */
class Service_Data_BaseApi {
	
	public function _baseCall($strService, $strMethod, $arrParams, $arrFilter = null, $extra = null){
		$arrRet = Saf_Api_Server::call($strService,$strMethod,$arrParams,$arrFilter,$extra);
		if(false === $arrRet) {
            $arrErrorCodes = Saf_Api_Server::getLastError();
            $arrErrNo = array_keys($arrErrorCodes);
            $intErrNo  = $arrErrNo[0];
            $strErrMsg = $arrErrorCodes[$intErrNo];
            if($intErrNo == Saf_Api_Server:: METHOD_FAILED){
                $intErrNo = Saf_Api_Server:: getServiceError();
            }
            Bd_Log::warning(sprintf("saf_api_server_call error [errno:%d][errmsg:%s][errorcodes:%s]",$intErrNo,$strErrMsg,var_export($arrErrorCodes,true)));
            return false;
        }
        return $arrRet['data'];
    }

}
?>
