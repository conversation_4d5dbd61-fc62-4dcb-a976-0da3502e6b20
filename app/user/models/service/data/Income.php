<?php
/**
 * @name Service_Data_Income
 * <AUTHOR>
 */
class Service_Data_Income {
	private $_objDao;
	private $_arrConf;


	public function __construct(){
		$this->_objDao = Bd_LayerProxy::getProxy('Dao_Income');
		$this->_arrConf = Bd_Conf::getConf('/app/user/user/');
	}

    /** 
    * 获取用户信息
    *
    * @access public
    * @param int $intUid 用户id                                                                                                                                                    
    */
    public function getUserProfileByUid($intUid){
        $arrUserProfile = $this->_objDao->getUserProfileByUid($intUid);
        return $arrUserProfile;
    }
    
    /**
    * 获取管理员操作原因
    * @access public
    * @param int $intUid 用户id
    * @param int $type   管理员操作原因
    */
    public function getAdminReason($intUid, $type){
        $arrReason = $this->_objDao->getAdminReason($intUid, $type);
        return $arrReason;
    }
    
    /** 
    * 更新 用户信息 的用户状态、用户类型
    *
    * @access public
    * @param int $intUid 用户id
    * @param int $intUserType 用户类型，与cpm对应
    */
    public function updateUserProfileStatus($intUid, $intUserStatus, $intUserType){
        return $this->_objDao->updateTable(
            'user_profile',
            array(
                'status='.$intUserStatus,
                'user_type='.$intUserType
            ),
            array(
                'uid=' => $intUid
            )
        );
    }
    /** 
    * 更新 用户信息
    *
    * @access public
    * @param int $intUid 用户id
    * @param int $intUserType 用户类型，与cpm对应
    */
    public function updateUserProfile($intUid, $arrAttr){
        return $this->_objDao->updateTable(
            'user_profile',
            $arrAttr,
            array(
                'uid=' => $intUid
            )
        );
    }
    /** 
    * 获取用户收入信息
    *
    * @access public
    * @param int $intUid 用户id                                                                                                                                                    
    */
    public function getUserCashByUid($intUid){
        $arrUserCash = $this->_objDao->getUserCashByUid($intUid);
        return $arrUserCash;
    }

    /** 
    * 获取用户收入明细记录
    *
    * @access public
    * @param int $intUid 用户id 
    * @param int $intPn 起始指针 
    * @param int $intRn 获取记录数目
    */
    public function getUserIncomeLogByUid($intUid, $intPn, $intRn){
        $arrCols = array('income', 'create_time', 'date', 'type');
        $strCond = sprintf("order by create_time desc limit %d, %d", $intPn, $intRn);
        $arrUserCash = $this->_objDao->getUserIncomeLogByUid($intUid, $arrCols, $strCond);
        return $arrUserCash;
    }

    /** 
    * 获取用户收入明细记录数目
    *
    * @access public
    * @param int $intUid 用户id 
    */
    public function getUserIncomeLogNum($intUid){
        // 0为用户收入， 1为取现， 2为罚扣 
        $intLogType = 0;
        return $this->_objDao->getUserCashLogNum($intUid, $intLogType);

    }

    /** 
    * 获取用户付费经验数目
    *
    * @access public
    * @param int $intUid 用户id 
    */
    public function getUserPayExpNum($intUid){
        return $this->_objDao->getUserPayExpNum($intUid);

    }

    /** 
    * 获取用户经验收入列表
    *
    * @access public
    * @param int $intUid 用户id 
    * @param int $intPn 起始指针 
    * @param int $intRn 获取记录数目
    */
    public function getExpIncomeByUid($intUid, $intPn, $intRn){
        $arrCols = array('eid', 'income', 'last_income', 'create_time');
        $strCond = sprintf("order by last_income desc limit %d,%d", $intPn, $intRn);
        $arrExpIncome = $this->_objDao->getExpIncomeByUid($intUid, $arrCols, $strCond);
        return $arrExpIncome;
    }

    /** 
    * 获取用户提现记录
    *
    * @access public
    * @param int $intUid 用户id 
    * @param int $intPn 起始指针 
    * @param int $intRn 获取记录数目
    */
    public function getUserWithdrawListByUid($intUid, $intPn, $intRn){
        /*
        $arrCols = array('amount', 'amount_after', 'create_time');
        $strCond = sprintf("order by create_time desc limit %d,%d", $intPn, $intRn);
        $arrWithdrawLog = $this->_objDao->getUserWithdrawLogByUid($intUid, $arrCols, $strCond);
        return $arrWithdrawLog;
         */
        $strCond = sprintf("order by create_time desc limit %d,%d", $intPn, $intRn);
        return $this->_objDao->getUserWithdrawApplyByUid($intUid, 1, $strCond);
    }

    /** 
    * 获取用户提现记录数目
    *
    * @access public
    * @param int $intUid 用户id 
    */
    public function getUserWithdrawLogNum($intUid){
        //  0为待审合， 1通过， 2拒绝
        $intLogType = 1;
        return $this->_objDao->getUserWithdrawApplyNum($intUid, $intLogType);
    }

    /** 
    * 获取某条经验的收入记录
    *
    * @access public
    * @param int $intEid 经验id 
    * @param int $intPn 起始指针 
    * @param int $intRn 获取记录数目
    */
    public function getExpIncomeLogByEid($intEid, $intPn, $intRn){
        $arrCols = array('income', 'create_time', 'date');
        $strCond = sprintf("order by create_time desc limit %d,%d", $intPn, $intRn);
        $arrExpIncomeLog = $this->_objDao->getExpIncomeLogByEid($intEid, $arrCols, $strCond);
        $arrOutput = array();
        foreach($arrExpIncomeLog as $arrItem){
            $arrOutput[] = array(
                'income' => sprintf('%.3f', $arrItem['income']),
                'createTime' => date("Y-m-d", $arrItem['date'])
            );
        }
        return $arrOutput;
    }

    /** 
    * 获取某条经验的收入记录数目
    *
    * @access public
    * @param int $intEid 经验id 
    */
    public function getExpIncomeLogNum($intEid){
        return $this->_objDao->getExpIncomeLogNum($intEid);
    }

    /** 
    * 获取支取申请记录
    *
    * @access public
    * @param int $intUid 用户id 
    * @param bool $intApplyType 申请记录的状态:0：待审核，1：已通过，2：已拒绝 
    */
    public function getUserWithdrawApplyByUid($intUid, $intApplyType=0){
        $strCond = sprintf("order by create_time desc limit 1");
        return $this->_objDao->getUserWithdrawApplyByUid($intUid, $intApplyType, $strCond);
    }

    /** 
    * 获取用户的邀请配额信息
    *
    * @access public
    * @param int $intUid 用户id 
    * @param int $intInvitationType 邀请码类型, 0: vip0 , 1: vip1, 2:vip2,分别对应相应的cpm等级
    */
    public function getUserInvitationQuota($intUid, $intInvitationType=NULL){
        return $this->_objDao->getUserInvitationQuota($intUid, $intInvitationType);
    }

    /** 
    * 更新配额，配额减一。 
    *
    * @access public
    * @param int $intUid 用户id 
    * @param int $intInvitationType 邀请码类型, 0: vip0 , 1: vip1, 2:vip2,分别对应相应的cpm等级
    */
    public function reduceUserInvitationQuota($intUid, $intInvitationType){
        return $this->_objDao->updateUserInvitationQuota(
            array(
                'amount_left=amount_left-1'
            ),
            array(
                'uid=' => $intUid,
                'invitation_type=' => $intInvitationType,
                'amount_left>' => 0,
            )
        );
    }

    /** 
    * 邀请码入库。 
    *
    * @access public
    * @param int $intUid 用户id 
    * @param str $strInvitationCode 用户邀请码
    * @param int $intInvitationType 邀请码类型, 0: vip0 , 1: vip1, 2:vip2,分别对应相应的cpm等级
    */
    public function insertInvitation($intUid, $strInvitationCode, $intInvitationType){
        return $this->_objDao->insertTable(
            'invitation_code',
            array(
                'code' => $strInvitationCode,
                'creator_uid' => $intUid,
                'invitation_type' => $intInvitationType,
                'create_time' => time()
            )
        );
    }

    /**
    * 获取用户已经生成的邀请码列表
    *
    * @access public
    * @param int $intUid 用户ID
    */
    public function getInvitationListByUid($intUid, $intCodeType){
        $arrCode = $this->_objDao->getInvitationListByUid($intUid, $intCodeType);
        return $arrCode;
    }

    /**
    * 获取邀请码信息
    *
    * @access public
    * @param str $strCode 邀请码
    */
    public function getInvitationInfo($strCode){
        return  $this->_objDao->getInvitationByCode($strCode);
    }
    /** 
    * 更新邀请码信息, 激活。 
    *
    * @access public
    * @param str $strCode 邀请码
    * @param int $intUid 受邀用户ID
    */
    public function activeInvitationCode($strCode, $intUid){
        return $this->_objDao->updateInvitationCode(
            array(
                'is_used=1',
                'invitee_uid='.$intUid
            ),
            array(
                'code=' => $strCode,
                'is_used=' => 0
            )
        );
    }

    /**
    * 获取当日主动加入配额信息
    *
    * @access public
    * @param int $intDate 日期(20130306)
    */
    public function getDailyQuotaInfo($intDate){
        $arrRes = $this->_objDao->getDailyQuotaInfo($intDate);
        if(empty($arrRes)){
            return array();
        }else{
            return array(
                'amountAll' => $arrRes['amount_all'],
                'amountLeft' => $arrRes['amount_left'],
            );
        }
    }

    /**
    * 添加当日主动加入配额信息
    *
    * @access public
    * @param int $intDate 日期(20130306)
    * @param int $intDailyQuota  配额数量
    */
    public function insertDailyQuota($intDate, $intDailyQuota){
        return $this->_objDao->insertTable(
            'daily_join_quota',
            array(
                'join_date' => $intDate,
                'amount_all' => $intDailyQuota,
                'amount_left' => $intDailyQuota,
                'create_time' => time()
            )
        );
    }

    /** 
    * 更新当日主动加入配额信息
    *
    * @access public
    * @param int $intDate 日期(20130306)
    */
    public function updateDailyQuotaInfo($intDate){
        return $this->_objDao->updateTable(
            'daily_join_quota',
            array(
                'amount_left=amount_left-1'
            ),
            array(
                'join_date=' => $intDate
            )
        );
    }
    /**
    * 添加提现申请
    *
    * @access public
    * @param int $intUid 提现用户ID
    */
    public function insertWithDrawApply($intUid, $intAmount){
        return $this->_objDao->insertTable(
            'withdraw_apply',
            array(
                'uid' => $intUid,
                'amount' => $intAmount,
                'create_time' => time()
            )
        );
    }
    /**
    * 添加用户信息
    *
    * @access public
    * @param int $intUid 提现用户ID
    */
    public function insertUserProfile($intUid, $arrAttr){
        return $this->_objDao->insertTable(
            'user_profile',
            $arrAttr
        );
    }

    public function checkActWriterState($intUid){
    	$strFilePath = DATA_PATH."/".$this->_arrConf['data_file']['uidNewWriteFirst'];
	if (!file_exists($strFilePath)){
		Bd_Log::warning("data file[$strFilePath] doesn\'t exist");
		return false;
	}else {
		require_once($strFilePath);
	}

	$arrUid = UidNewWriteFirst::$NewUidList;
	if (in_array($intUid,$arrUid)) {
		return true;
	} else {
		return false;
	}
    }
}
?>
