<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Attrnamemap.php
 * <AUTHOR>
 * @date 2013/08/14 23:43:43
 * @brief 
 *  
 **/


class Dl_Usercenter_Attrnamemap
{
    
    public static $attrname2id = array(
          'user_name' => 0,
          'user_type' => 1,
          'user_pow' => 2,
          'user_nickname' => 3,
          'nani_nickname'=>772,
          'user_status' => 4,
          'meizhi_level' => 5,
          'superboy' => 6,
          'is_coreuser' => 7,
          'is_interestman' => 8,
          'user_sex' => 9,
          'paper' => 13,
          'bg_id' => 14,
          'card' => 15,
          'is_shengyou' => 16,
          'is_hardworking' => 17,
          'billboard' => 18,
          'Parr_props' => 19,
          'portrait_time' => 20,
          'mParr_props' => 21,
          'is_member' => 100,
          'is_verify' => 101,
          'is_tenyear' => 102,
          'is_group_owner' => 103,
          'is_anchor' => 104,
        );
    public static $id2attrname = array(
          0 => 'user_name',
          1 => 'user_type',
          2 => 'user_pow',
          3 => 'user_nickname',
          4 => 'user_status',
          5 => 'meizhi_level',
          6 => 'superboy',
          7 => 'is_coreuser',
          8 => 'is_interestman',
          9 => 'user_sex',
          13 => 'paper',
          14 => 'bg_id',
          15 => 'card',
          16 => 'is_shengyou',
          17 => 'is_hardworking',
          18 => 'billboard',
          19 => 'Parr_props',
          20 => 'portrait_time',
          21 => 'mParr_props',
          772 => 'nani_nickname',
          100 => 'is_member',
          101 => 'is_verify',
          102 => 'is_tenyear',
          103 => 'is_group_owner',
          104 => 'is_anchor',
        );

    public static $attr_info = array (
        "Parr_scores" => array (
            "redis" => 1,
        ),
    	"lbs" => array (
    		"redis" => 1,
    	),
        "user_nickname" => array (
            "base64" => 1,
            "nickname"=> 1,
            "from_app" => 0,
        ),
        "nani_nickname" => array(
            "base64" => 1,
            "nickname" => 1,
            "from_app" => 1,
        )
    );

    public static function isRedisAttr($attr_name)
    {
        return isset( self::$attr_info[$attr_name]["redis"]);
    }
    public static function isBase64Attr($attr_name){
        return isset( self::$attr_info[$attr_name]['base64']);
    }

    public static function getAttrIdByName($attr_name)
    {
        return self::$attrname2id[$attr_name];
    }

    public static function getAttrNameById($attr_id)
    {
        return self::$id2attrname[$attr_id];
    }
    
    public static function isNickNameList($attr_name)
    {
        return isset(self::$attr_info[$attr_name]["nickname"]);
    }   
   public static function getFromAppByName($attr_name)
    {
        return self::$attr_info[$attr_name]["from_app"];
    }
}


/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
