<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013:08:11 20:25:40
 * @version 
 * @structs & methods(copied from idl.)
*/



define("MODULE","user_center_dl");

class Dl_Usercenter_Usercenter
{

    const SERVICE_NAME = "Dl_Usercenter_Usercenter";
    protected static $_conf = null;
    protected static $_use_split_db = false;
    const CACHE_NAME = "forum_usercenter";
    const REDIS_NAME = "bdrpusercenter";
    protected static $_cache = null;
    protected static $_redis = null;
    protected static $_get_attr_file = null;
    protected static $_extattr_conf = null;
    protected static $_nickname_cache_key = array(
        0=>"nn_",
        1=>"naninn_",
    );
    //清除go缓存
    protected static $_nickname_cache_key_go = array(
        0=>"nn_go_",
        1=>"naninn_go_",
    );

    const DB_TABLE_NAME = "user_info";
    const DB_TABLE_USERNICKNAME = "user_nickname_info";
    const DB_TABLE_NUM = 10;
    const NANINICKNAMEID = 772;

	const REDIS_KEY_PREFIX = "tb:";
    const UID_UNAME_EXPIRE_TIME = 86400;
    const UNICKNAME_UID_EXPIRE_TIME = 128800;
    const US_UID_INFO_EXPIRE_TIME = 86400;
    const UNAME_ATTR_ID = 0;
    const USEX_ATTR_ID = 9;
    const DISPLAYNAME_ATTR_ID = 836;
    const USERSTATE_ATTR_ID = 855;

    const MAX_PASS_MGET_NUM = 32;
    public static $dl_ie = 'utf-8';

    const DATABASE_NAME_UTF = "forum_uusercenter";
    const CACHE_NAME_UTF = 'forum_uusercenter';
    const UTF_US_UID_INFO_PREFIX = "uus_v1:";
    const UTF_UID_UNAME_CACHE_PREFIX = "uuid_v0:";

    const UTF_US_UID_INFO_PREFIX_GO = "uus_v2:";
    const UTF_UID_UNAME_CACHE_PREFIX_GO = "uuid_v2:";

	const UC_NMQ_TOPIC = "msg";
	const UC_NMQ_CMD = "";


    /**
     * @brief get cache obj.
     * @return: obj of Bingo_Cache_Memcached, or null if connect fail.
    **/
    private static function _getCache()
    {
        if(self::$_cache){
            return self::$_cache ;
        }
        Bingo_Timer::start('cacheinit');

        /*
         *$cacheConf = Bd_Conf::getConf('/cache/cache_dl_usercenter_usercenter');
         *if(!$cacheConf)
         *{
         *    Bingo_Log::warning("get cache config fail.[/cache/cache_dl_usercenter_usercenter]");
         *    return null;
         *}
         *self::$_cache = new Bingo_Cache_Memcached(array(
         *            'conf' => $cacheConf,
         *    ));
         */


        self::$_cache = new Bingo_Cache_Memcached(self::CACHE_NAME);

        Bingo_Timer::end('cacheinit');

        if(!self::$_cache || !self::$_cache->isEnable())
        {
            Bingo_Log::warning("init cache fail.");
            self::$_cache = null;
            return null;
        }
        return self::$_cache;
    }

    private static function _getRedis()
    {
        if(self::$_redis)
        {
            return self::$_redis ;
        }

        Bingo_Timer::start('redisinit');

        self::$_redis = new Bingo_Cache_Redis(self::REDIS_NAME);

        Bingo_Timer::end('redisinit');

        if(!self::$_redis)
        {
            Bingo_Log::warning("init redis fail.");
            Bingo_Log::pushNotice("redis_err_init",1);
            self::$_redis = null;
            return null;
        }
        return self::$_redis;
    }

    private static function _use_redis()
    {
        return true;
    }

    private static function _redis_mgetUserAttr($uid_array)
    {
        $redis_proxy = self::_getRedis();
        if(! $redis_proxy)
        {
            Bingo_Log::warning("get redis instance failed!");
            return null;
        }

        $redis_input = null;
        foreach($uid_array as $uid)
        {
            $temp_key = self::REDIS_KEY_PREFIX . $uid;
            $temp_input = array (
                "key" => $temp_key,
                );

            $redis_input["reqs"][] = $temp_input;
        }
        Bingo_Timer::start('get_redis_cache');
        $redis_output = $redis_proxy->HGETALL($redis_input);
        Bingo_Timer::end('get_redis_cache');
        if(is_null($redis_output)){
            Bingo_Log::pushNotice("redis_err_hgetall",1);
        }

        //Bingo_Log::warning("redis output: " . Bingo_String::array2json($redis_output));

        return $redis_output;

    }

    private static function _redis_setUserAttr($user_id, $attr_name, $attr_value)
    {
        $redis_proxy = self::_getRedis();
        if(! $redis_proxy)
        {
            Bingo_Log::warning("get redis instance failed!");
            return self::_errRet(Tieba_Errcode::ERR_CACHE_CONN_FAIL);
        }

        $redis_key = self::REDIS_KEY_PREFIX . $user_id;
        $redis_input = array(
            "key" => $redis_key,
            "field" => $attr_name,
            "value" => $attr_value,
            );

        $redis_output = $redis_proxy->HSET($redis_input);

        if(false == $redis_output)
        {
            Bingo_Log::warning("hset user info failed! user_id: " . $user_id . ", attr_name: " . $attr_name . ", attr_value: " . $attr_value);
            Bingo_Log::pushNotice("redis_err_hset",1);
            return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
        }

        self::_writeMonitorLog($user_id, $attr_name, $attr_value);

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * @brief init
     * @return: true if success. false if fail.

    **/
    private static function _init()
    {

        //add init code here. init will be called at every public function beginning.
        //not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
        //init should be recalled for many times.

        if(self::$_conf == null)
        {
            self::$_conf = Bd_Conf::getConf("/app/user_center/dl_usercenter_usercenter");
            if(self::$_conf == false){
                Bingo_Log::warning("init get conf fail.");
                return false;
            }
        }
        //增加配置文件初始化操作 by sjq 20131030
        if (self::$_get_attr_file == null){
            include dirname(__FILE__)."/../../../../data/app/user_center/Attrnamemap.php";
            if (class_exists('Attrnamemap_DB') && count(Attrnamemap_DB::$attrname2id) > 15
                    && count(Attrnamemap_DB::$attrname2id) === count(Attrnamemap_DB::$id2attrname)){
                self::$_get_attr_file = 1;
                Bingo_Log::pushNotice("Attrnamemap_DB",1);

            }else{
                self::$_get_attr_file = 2;
                Bingo_Log::pushNotice("Attrnamemap_DB",0);
                Bingo_Log::warning ( "use DB file error");
                return false;
            }
        }

        //增加新扩展属性文件初始化操作 by gongwen 20140622
    /* 	if(self::$_extattr_conf == null){
            $curDir=dirname(__FILE__);
            $pos = strrpos($curDir, '/app/user/dl/usercenter');
            $confPath = substr($curDir, 0, $pos).'/data';
            $arrConf = array();
            $arrConf = config_load($confPath, 'ext_attr.conf');
            if(empty($arrConf)){
                Bingo_Log::warning("init ext attr fail");
                return false;
            }else{
                self::$_extattr_conf = $arrConf;
            }
        } */

        if(self::$_extattr_conf == null){
            Bingo_Timer::start('ext_attr_conf_dl');
            self::$_extattr_conf = Bd_Conf::getConf("/tieba/ext_attr");
            Bingo_Timer::end('ext_attr_conf_dl');
            if(self::$_extattr_conf == false){
                Bingo_Log::warning("init ext attr fail");
                return false;
            }
        }

        return true;
    }


    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    private static function _writeMonitorLog($intUid, $strAttrName, $strAttrValue) {

        if (!isset(self::$_extattr_conf['user']['ext_attr'][$strAttrName]['Type'])) {
            Bingo_Log::warning("invalid attr: $strAttrName");
            return false;
        }

        $intAttrLen = (intval(self::$_extattr_conf['user']['ext_attr'][$strAttrName]['Type'])) === 0 ? 4 : strlen($strAttrValue);

        $arrLogData = array(
            'service' => 'user',
            'attrs' => array(
                array(
                    'related_id' => $intUid,
                    'attr_name' => $strAttrName,
                    'attr_len' => $intAttrLen,
                    'attr_val' => $strAttrValue,
                ),
            ),
        );
        Tieba_Service_ExtAttrLog::writeCommitLog($arrLogData);
        return true;
    }

    public static function preCall($arrInput)
    {
        // pre-call hook
    }

    public static function postCall($arrInput)
    {
        // post-call hook
    }


    /**
     * @brief init
     * @return: true if success. false if fail.
     *  getPassByUids
     **/
    public static function getPassByUids($arrInput)
    {
        //try to get from cache
        $arrOutput = null;
        $cache_instance  = self::_getCache();
        $hit_cache_uids = array();
        $user_ids = $arrInput;

        /*判断是否为线下环境，线下环境时需要对不在pass中的uname进行默认赋值*/
        $offConf = Bd_Conf::getConf('tieba/env');
        $environment = 0;
        if(isset($offConf['environment']))
        {
            $environment = intval($offConf['environment']);
        }

        if(null === $cache_instance)
        {
            Bingo_Log::warning("connect cache failed!");
        }
        else
        {
            $cache_keys = array();
            $utf_cache_keys = array();

            foreach($user_ids as $user_id)
            {
                $utf_cache_keys[] = self::UTF_UID_UNAME_CACHE_PREFIX . $user_id;
            }
            //新的utf-8编码缓存
            $utf_cache_res = $cache_instance->multipleGet($utf_cache_keys);

            if(null != $utf_cache_res)
            {
                foreach($utf_cache_res as $key => $item)
                {
                    $valarr = unserialize($item);
                    //去除老缓存
                    if (!isset($valarr['username']) || !isset($valarr['displayname'])){
                        self::delCacheByKey($key);
                        Bingo_Log::warning("delete old cache!");
                        continue;
                    }
                    $key_uid = intval (substr($key,strlen(self::UTF_UID_UNAME_CACHE_PREFIX)));
                    $hit_cache_uids[] = $key_uid;
                    $arrOutput[$key_uid] = $valarr;
                }
            }
        }
        
        $total_num = count($user_ids);
        $hit_cache_num = count($hit_cache_uids);

        Bingo_Log::pushNotice("pass_query",$total_num);
        Bingo_Log::pushNotice("pass_cache",$hit_cache_num);

        if($total_num <= $hit_cache_num)
        {
            return $arrOutput;
        }

        $temp_pass_req_ids = array_diff($user_ids,$hit_cache_uids);
        $pass_req_ids = array_values($temp_pass_req_ids);
        $pass_req_count = count($pass_req_ids);
        if($pass_req_count <= 0)
        {
            return $arrOutput;
        }
        
        
        $arrPassDlOutput = array();
        //增加性别获取
		$pass_req_fields = array(
			 'username',
             'userdetail',
             'sex',
             'birthday_year',
             'birthday_month',
             'birthday_day',
             'province',
             'address',
             'taginfo',
             'regtime',
             'constellation',
             'age',
             'displayname',
             'userstate',
         );


        $temp_offset = 0;
        $temp_req_num = 0;
        $pass_talk_num = 0;
        while(true)
        {
            $temp_req_uids = array_slice($pass_req_ids,$temp_offset,self::MAX_PASS_MGET_NUM,TRUE);
            $temp_req_num = count($temp_req_uids);
            $temp_offset = $temp_offset + $temp_req_num;
           
            if(0 === intval($temp_req_num))
                break;

            $real_req_uids = array_values($temp_req_uids);
           
            $temp_pass_output =  Service_Tool::getUinfoByUidsFromPassgate($real_req_uids,$pass_req_fields,self::$dl_ie);

            
            /*线下环境时将不在pass中的用户的uname进行默认赋值*/
            if(1 === intval($environment)){
                foreach($real_req_uids as $uid){
                    if (!isset($temp_pass_output[$uid])) {
                        $temp_pass_output[$uid]['sex'] = 1;
                        $temp_pass_output[$uid]['username'] = "forum";
                    }
                }
            }
            ++ $pass_talk_num;
            if(false === $temp_pass_output)
            {
                Bingo_Log::warning("get info from pass failed!user_ids: " . serialize($real_req_uids));
                return false;
            }

            $arrPassDlOutput = $arrPassDlOutput + $temp_pass_output;


            if($temp_req_num < MAX_PASS_MGET_NUM)
                break;

        }
        Bingo_Log::pushNotice("pass_talk_num",$pass_talk_num);

        /*
         *$arrPassDlOutput = Service_Tool::getUinfoByUidsFromPassgate($pass_req_ids,$pass_req_fields);
         *if(false === $arrPassDlOutput)
         *{
         *    Bingo_Log::warning("get info from pass failed!user_ids: " . serialize($pass_req_ids));
         *    return false;
         *}
         */
        /*
        $pass_null = 0;
        foreach($arrPassDlOutput as $uid => $pass)
        {
            if(true === isset($pass['username']))
            {
                $arrOutput[$uid] = $pass;
                //判断$uname['username'][0]，用户名为空不存cache add by sjq 20130927
                if(null != $cache_instance && isset($pass['username'][0]))
                {
                    $cache_key = self::UTF_UID_UNAME_CACHE_PREFIX . $uid;
                    $add_res = $cache_instance->add($cache_key,serialize($pass),self::UID_UNAME_EXPIRE_TIME);
                }
                else {
                    ++ $pass_null;
                }
            }
        }
        */
        $multiCache = array();
        $pass_null = 0;
        foreach($arrPassDlOutput as $uid => $pass)
        {
                $arrOutput[$uid] = $pass;
                //判断$uname['username'][0]，用户名为空不存cache add by sjq 20130927
                if(null != $cache_instance && isset($pass['username'][0]))
                {
                    $multiCache[] = array(
                        'key' => self::UTF_UID_UNAME_CACHE_PREFIX . $uid,
                        'value' => serialize($pass),
                        'expire' => self::UID_UNAME_EXPIRE_TIME,
                    );
                }
                else {
                    ++ $pass_null;
                }
        }
        $addCacheCount = count($multiCache);
        if(null != $cache_instance && $addCacheCount !== 0 && isset($multiCache[0]['key']))
        {
            Bingo_Timer::start('add_pass_cache');
            if($addCacheCount === 1){
                $ret = $cache_instance->add($multiCache[0]['key'], $multiCache[0]['value'], self::UID_UNAME_EXPIRE_TIME);
            }else{
                $ret = $cache_instance->multipleAdd($multiCache);
            }
            Bingo_Timer::end('add_pass_cache');
            if ($ret === false || $ret === null){
                Bingo_Log::warning("multipleAdd miss uid cache failed!");
            }
        }

        Bingo_Log::pushNotice("pass_null",$pass_null);
        Bingo_Log::pushNotice("pass_count",count($arrPassDlOutput));

        return $arrOutput;
    }

    /**
     * @brief
     * @arrInput:
     * 	uint32_t user_id
     * @return: $arrOutput
     * 	user_center_info info
    **/
    public static function getUserInfo($arrInput)
    {
        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['user_id']))
        {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }


        //input params.
        $user_id = intval($arrInput['user_id']);
        $hit_cache = 0;

        if(!self::_init())
        {
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }


        //output params.
        $info = false;
        $utf_arr_ids = array();
        $utf_cache_key = self::UTF_US_UID_INFO_PREFIX . $user_id ;
        if(0 === $user_id)
        {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $cache = self::_getCache();
        if(null === $cache)
        {
            Bingo_Log::warning("connect cache failed!");
        }
        else
        {
            $cache_res = $cache->get($utf_cache_key);
            if(null != $cache_res)
            {
                $info = unserialize($cache_res);
                foreach($info as & $info_item)
                {
                    $attr_name = self::getAttrNameById($info_item['attr_id']);
                    if(null != $attr_name)
                    {
                        $info_item['attr_name'] = $attr_name;
                    }
                }
                $hit_cache = 1;
            }
        }

        if(false === $info)
        {
            $db_instance = self::_getUtfDB();

            if(null === $db_instance)
            {
                Bingo_Log::warning("connect with db failed!");
                return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
            }

            $tb_name = self::DB_TABLE_NAME ;
            $query_sql = "SELECT attr_id, attr_value FROM " . $tb_name . " WHERE user_id = " . $user_id;
            //Bingo_Log::debug("exec sql: " . $query_sql );
            $db_res = $db_instance->query($query_sql);
            if(false === $db_res)
            {
                $log_info = "query sql:(" . $query_sql . " )failed!";
                Bingo_Log::warning($log_info);
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            else
            {
                $info = $db_res;

                if(null != $cache)
                {
                    $info_add = serialize($db_res);
                    $add_res = $cache->add($utf_cache_key, $info_add, self::US_UID_INFO_EXPIRE_TIME);
                    //$add_res = $cache->add("1234", $info_add);
                    if(CACHE_OK  === $add_res)
                    {
                        //Bingo_Log::debug("add key to cache success! key: " . $cache_key);
                    }
                    else
                    {
                        //Bingo_Log::warning("add cache failed! key: " . $cache_key);
                    }
                }

                foreach($info as & $info_item)
                {
                    $attr_name = self::getAttrNameById($info_item['attr_id']);
                    if(null != $attr_name)
                    {
                        $info_item['attr_name'] = $attr_name;
                    }
                }
            }
        }

    //    $arrNicknames = array();

        //decode user_nickname
        foreach($info as & $info_item){
            if(isset($info_item['attr_value']) && isset($info_item['attr_name']) && isset($info_item['attr_id'])
                && Dl_Usercenter_Attrnamemap::isBase64Attr($info_item['attr_name']) && $info_item['attr_id'] == 3){
                $user_nickname_base64 = $info_item['attr_value'];
      //          $arrNicknames[] = $user_nickname_base64;
                $user_nickname = base64_decode($user_nickname_base64, true);
                if($user_nickname_base64 == base64_encode($user_nickname)){//判断存储的是否是base64字符串，但不完全准确
                    $info_item['attr_value'] = $user_nickname;
                }
            }
        }

		/*
        //search nickname update time
        if(count($arrNicknames) != 0){
            $tb_name = self::DB_TABLE_USERNICKNAME ;
            foreach($arrNicknames as $index => $nickname){
                $arrNicknames[$index] = "'".$nickname."'";
            }

            $query_sql = "select * from " . $tb_name . " where nickname in (" . implode(",", $arrNicknames) . ")";

            //Bingo_Log::warning("exec sql: " . $query_sql);

            $db_instance = self::_getUtfDB();
            if(null === $db_instance)
            {
                Bingo_Log::warning("connect with db failed!");
                return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
            }
            $db_res = $db_instance->query($query_sql);
            $dlOutput = array();

            if(false === $db_res)
            {
                Bingo_Log::warning("query sql " . $query_sql . "failed!");
            }else{
                //Bingo_Log::warning(Bingo_String::array2json($db_res));
                foreach($db_res as $info){
                    $dlOutput[$info['user_id']] = $info['update_time'];
                }
            }

        }
	*/

        $notice_info = sprintf("getUserInfo success, user_id:%d, hit_cache:%d",$user_id,$hit_cache);
        //Bingo_Log::notice($notice_info);

        $ret_info = array (
            'user_id' => $user_id,
            'attrs' => $info,
     //       'nickname_update_time' => isset($dlOutput[$user_id]) ? $dlOutput[$user_id] : 0,
            );

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'info' => $ret_info,
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @arrInput:
     * 	uint32_t user_ids[]
     * @return: $arrOutput
     * 	user_center_info infos[]
    **/
    public static function mgetUserInfo($arrInput)
    {
        
        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['user_ids']) || !isset($arrInput['user_ids']))
        {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //是否 不需要命中命中缓存
        if (isset($arrInput['no_cache']) && $arrInput['no_cache'] == 1) {
            $no_cache = 1;
        } else {
            $no_cache = 0;
        }
        
        //input params.
        $user_ids = Tieba_Service::getArrayParams($arrInput, 'user_ids');
        $all_num = count($user_ids);
        if(0 === $all_num)
        {
            Bingo_Log::warning("input params invalid. no uid [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //过滤uids 为空 为0 add by sjq 20131010
        $arrBad = array("0","");
        $user_ids = array_diff($user_ids,$arrBad);
        $user_ids = array_values($user_ids);
        $user_ids = array_unique($user_ids);
        $total_num = count($user_ids);
        Bingo_Log::pushNotice("bad_num",$all_num - $total_num);
        Bingo_Log::pushNotice("valid_num",$total_num);
        
        if(0 === $total_num)
        {
            Bingo_Log::warning("input params invalid. no valid uid [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if(!self::_init())
        {
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        //output params.
        $infos = false;
        $cache_hit_num = 0;
        $cache_hit_uids = array();
        $ret_infos = array();

        $cache_instance  = self::_getCache();
        if(null === $cache_instance)
        {
            Bingo_Log::warning("connect cache failed!");
        }
        else
        {
            $utf_cache_keys = array();
            $cache_keys = array();
            $check_uids = array();//取不到，去取gbk和数据库
            foreach($user_ids as $user_id)
			{
				if($user_id <= 0){
					Bingo_Log::warning("input params invalid. illegal uid [$user_id] request[".serialize($arrInput)."]");
					return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
				}
                $utf_cache_keys[] = self::UTF_US_UID_INFO_PREFIX . $user_id;
            }
            Bingo_Timer::start('getcache');
            $utf_cache_res = $cache_instance->multipleGet($utf_cache_keys);
            Bingo_Timer::end('getcache');
    
            if(null != $utf_cache_res && $no_cache == 0)
            {
                foreach($utf_cache_res as $key => $item)
                {
                    $key_uid = intval (substr($key,strlen(self::UTF_US_UID_INFO_PREFIX)));
                    $item_raw = unserialize($item);
                    foreach($item_raw as & $info_item)
                    {
                        $attr_name = self::getAttrNameById($info_item['attr_id']);
                        if(null != $attr_name)
                        {
                            $info_item['attr_name'] = $attr_name;
                        }
                        //去掉redis里面的属性（invalid后面会被模板过滤掉）
                        if (true == Dl_Usercenter_Attrnamemap::isRedisAttr($attr_name)) {
                            $info_item['attr_name'] = 'invalid';
                            $info_item['attr_id'] = -1;
                            $info_item['attr_value'] = 'invalid';
                        }
                    }
                    $ret_infos[] = array(
                        'user_id' => $key_uid,
                        'attrs' => $item_raw,
                    );

                    ++ $cache_hit_num;
                    $check_uids[$key_uid] = $key_uid;
                    $cache_hit_uids[] = $key_uid;
                }
            }
        }
        
        Bingo_Log::pushNotice("cache_hit",$cache_hit_num);
        
        if($cache_hit_num < $total_num)
        { 
            $temp_query_db_uids = array_diff($user_ids,$cache_hit_uids);
            $query_db_uids = array_values($temp_query_db_uids);

            if(count($query_db_uids) > 0)
            {
                $hit_db_uids = array();
                $miss_db_uids = array();
                
                
                //first, get pass info from cache or pass
                Bingo_Timer::start('getpass');
                $pass_res = self::getPassByUids($query_db_uids); 
                Bingo_Timer::end('getpass');
                
                if(false === $pass_res)
                {
                    //记录日志，不退出
                    //return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                    Bingo_Log::warning("getPassByUids error [".serialize($query_db_uids)."]");
                }

                $sql_in_part = "(";
                $query_db_num = count($query_db_uids);
                $i = 0;
                $j = 0;
                $db_instance = self::_getUtfDB();
                if(null === $db_instance)
                {
                    Bingo_Log::warning("connect with db failed!");
                    return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
                }
                $merge_uid_info = array();
                //为缓解db压力采取顺序访问db的降级策略
                foreach($query_db_uids as $uid)
                {
                    ++ $i;
                    ++ $j;
                    if($j == 20 || $i == $query_db_num )
                    {
                        $sql_in_part = $sql_in_part . $uid . ")";
                        $j = 0;
                    }
                    else
                    {
                        $sql_in_part = $sql_in_part . $uid . ",";
                        continue;
                    }


                    $query_sql = "SELECT user_id,attr_id,attr_value FROM " . self::DB_TABLE_NAME . " WHERE user_id IN " . $sql_in_part;
                    //Bingo_Log::debug("exec sql: " . $query_sql);



                    Bingo_Timer::start('dbquery');
                    $db_res = $db_instance->query($query_sql);
                    Bingo_Timer::end('dbquery');

                    $sql_in_part = "(";
                    if(false === $db_res)
                    {
                        $log_info = "query sql:(" . $query_sql . " )failed!";
                        Bingo_Log::warning($log_info);
                        return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                    }
                    else
                    {
                        foreach($db_res as $db_res_item)
                        {
                            //Bingo_Log::warning("check user_id " . Bingo_String::array2json($db_res_item));
                            if(false === isset($pass_res[$db_res_item['user_id']]))
                            {
                                Bingo_Log::warning("miss get username on user_id: " . $db_res_item['user_id']);
                                //无pass不算重要错误，往下执行 modify by sjq 20131010
                                //continue;

                            }
                            else
                            {
                                $merge_uid_info[$db_res_item['user_id']][] = array(
                                    'attr_id' => self::UNAME_ATTR_ID,
                                    'attr_value' => $pass_res[$db_res_item['user_id']]['username'],
                                );
                                //新加入性别信息 add by sjq 20131023
                                $merge_uid_info[$db_res_item['user_id']][] = array(
                                    'attr_id' => self::USEX_ATTR_ID,
                                    'attr_value' => $pass_res[$db_res_item['user_id']]['sex'],
                                );
                                //新添加的displayname
                                $merge_uid_info[$db_res_item['user_id']][] = array(
                                    'attr_id' => self::DISPLAYNAME_ATTR_ID,
                                    'attr_value' => $pass_res[$db_res_item['user_id']]['displayname'],
                                );
                                //新添加的userstate
                                $merge_uid_info[$db_res_item['user_id']][] = array(
                                    'attr_id' => self::USERSTATE_ATTR_ID,
                                    'attr_value' => $pass_res[$db_res_item['user_id']]['userstate'],
                                );
                            }


                            $hit_db_uids[] = $db_res_item['user_id'];
                            /*
                            *if(false == isset($merge_uid_info[$db_res_item['user_id']]))
                            *{
                            *}
                            */
                            /*
                            if(self::IS_OPEN_UTF_DB === 1){
                                $db_res_item['attr_value'] = self::toGbkEncodeAndSerialize($db_res_item['attr_value'],'gbk');
                            }
                            */
                            $merge_uid_info[$db_res_item['user_id']][] = array(
                                'attr_id' => $db_res_item['attr_id'],
                                'attr_value' => $db_res_item['attr_value'],
                            );

                        }
                    }
                }

                $temp_uniq_array = array_unique($hit_db_uids);
                $hit_db_uids = $temp_uniq_array;
                $remote_info = Bingo_Http_Request::getRaw("ngscfr");
                Bingo_Log::pushNotice("remote_info",$remote_info);
                Bingo_Log::pushNotice("hit_db_num", count($hit_db_uids));
                
                if(count($hit_db_uids) <= count($query_db_uids))
                {
                    $miss_db_uids = array_diff($query_db_uids,$hit_db_uids);
                    foreach($miss_db_uids as $uid)
                    {
                        if(false === isset($pass_res[$uid]))
                        {
                            Bingo_Log::warning("miss get username on user_id: " . $uid );
                            continue;
                        }
                        $temp_attrs = array();
                        if(true === isset($pass_res[$uid]))
                        {
                            $temp_attrs[] = array(
                                'attr_id' => self::UNAME_ATTR_ID,
                                'attr_value' => $pass_res[$uid]['username'],
                            );
                            $temp_attrs[] = array(
                                'attr_id' => self::USEX_ATTR_ID,
                                'attr_value' => $pass_res[$uid]['sex'],
                            );
                            $temp_attrs[] = array(
                                'attr_id' => self::DISPLAYNAME_ATTR_ID,
                                'attr_value' => $pass_res[$uid]['displayname'],
                            );
                            $temp_attrs[] = array(
                                'attr_id' => self::USERSTATE_ATTR_ID,
                                'attr_value' => $pass_res[$uid]['userstate'],
                            );

                        }

                        $merge_uid_info[$uid] = $temp_attrs;

                    }
                }
                $multipUids = array();
                foreach($merge_uid_info as $key_uid => $cache_value)
                {
                    if(null != $cache_instance && isset($pass_res[$key_uid]['username'][0]))
                    {
                        $utf_cache_key = self::UTF_US_UID_INFO_PREFIX . $key_uid;
                        $multipUids[] = array(
                            'key' => $utf_cache_key,
                            'value' => serialize($cache_value),
                            'expire' => self::US_UID_INFO_EXPIRE_TIME,
                        );
                    }

                    foreach($cache_value as & $info_item)
                    {
                        $attr_name = self::getAttrNameById($info_item['attr_id']);
                        if(null != $attr_name)
                        {
                            $info_item['attr_name'] = $attr_name;
                        }
                        //去掉redis里面的属性（invalid后面会被模板过滤掉）
                        if (true == Dl_Usercenter_Attrnamemap::isRedisAttr($attr_name)) {
                            $info_item['attr_name'] = 'invalid';
                            $info_item['attr_id'] = -1;
                            $info_item['attr_value'] = 'invalid';
                        }

                    }

                    $ret_infos[] = array(
                        'user_id' => $key_uid,
                        'attrs' => $cache_value,
                        );
                }
                $cacheCount = count($multipUids);
                if(null != $cache_instance && $cacheCount !== 0 && isset($multipUids[0]['key']))
                {
                    Bingo_Timer::start('add_user_cache');
                    if($cacheCount ===1){
                        $ret = $cache_instance->add($multipUids[0]['key'], $multipUids[0]['value'], self::US_UID_INFO_EXPIRE_TIME);

                    }else{
                        $ret = $cache_instance->multipleAdd($multipUids);
                    }
                    Bingo_Timer::end('add_user_cache');
                    if ($ret === false || $ret === null){
                        Bingo_Log::warning("multipleAdd miss uid cache failed!");
                    }
                    Bingo_Log::pushNotice("add_cache_count",$cacheCount);
                }

            }
        }
        
        if(true === self::_use_redis()/* && ( !isset($arrInput['multi']) || $arrInput['multi'] !== 1 )*/)
        {
            $redis_info = self::_redis_mgetUserAttr($user_ids);
            if(false == isset($redis_info['err_no']) || 0 != intval($redis_info['err_no']))
            {
                Bingo_Log::warning("get info from reids failed![info: ]" . Bingo_String::array2json($user_ids));
            }
            else
            {
                $redis_ret_info = $redis_info['ret'];
                foreach($redis_ret_info as $uid_key => $uid_info)
                {


                    if('utf-8' === self::$dl_ie) {//由于需要返回redis数据，所以需要对redis数据进行转换（redis始终存gbk数据）
                        foreach ($uid_info as &$oneValue) {
                            if (!empty($oneValue['value']) && is_string($oneValue['value'])) {
                                $oneValue['value']= self::toUtfEncodeAndSerialize($oneValue['value'],'utf-8');
                            }
                        }
                        $redis_info['ret'][$uid_key]=$uid_info;
                    }
                    $uid = intval (substr($uid_key,strlen(self::REDIS_KEY_PREFIX)));


                    foreach($ret_infos as & $test_item)
                    {
                            if(intval($test_item['user_id']) === intval($uid))
                            {
                                    //Bingo_Log::warning("hit info: before" . Bingo_String::array2json($test_item));
                                    foreach($uid_info as $item)
                                    {
                                        $test_item['attrs'][] = array(
                                                        'attr_name' => $item['field'],
                                                        'attr_value' => $item['value'],
                                                        );

                                    }
                                    //Bingo_Log::warning("hit info: after" . Bingo_String::array2json($test_item));
                            }
                    }
                }
            }
        }

        $error = Tieba_Errcode::ERR_SUCCESS;

        //Bingo_Log::warning(Bingo_String::array2json($ret_infos));

      //  $arrNicknames = array();

        //user_nickname decode
        foreach($ret_infos as $index => $ret_info){
            $user_id = $ret_info['user_id'];
            $attrs = $ret_info['attrs'];
            foreach($attrs as & $info_item){
                if(isset($info_item['attr_value']) && isset($info_item['attr_name']) && isset($info_item['attr_id'])
                    && Dl_Usercenter_Attrnamemap::isBase64Attr($info_item['attr_name']) && in_array($info_item['attr_id'],array(3,772))){
                    $user_nickname_base64 = $info_item['attr_value'];
        //            $arrNicknames[] = $user_nickname_base64;
                    $user_nickname = base64_decode($user_nickname_base64, true);
                    if($user_nickname_base64 == base64_encode($user_nickname)){//判断存储的是否是base64字符串，但不完全准确
                        $info_item['attr_value'] = $user_nickname;
                    }
                }
            }
            $ret_infos[$index] = array(
                'user_id' => $user_id,
                'attrs' => $attrs
            );
        }
       
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'infos' => $ret_infos,
            'redis_info' => $redis_info,
        );
        return $arrOutput;
    }
   
	/**
     * @brief
     * @arrInput:
     * 	uint32_t user_id
     * 	string attr_name
     * 	string attr_value
     * @return: $arrOutput
    **/
    public static function setUserInfo($arrInput)
    {
        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
            if(!isset($arrInput['user_id']) || !isset($arrInput['attr_name']) || !isset($arrInput['attr_value'])|| empty($arrInput['user_id']))
            {
                    Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }

            //input params.
            $user_id = intval($arrInput['user_id']);
            $attr_name = $arrInput['attr_name'];
            $attr_value = $arrInput['attr_value'];

            if(!self::_init())
            {
                    return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
            }

            Bingo_Log::pushNotice('attr_name',$attr_name);
            Bingo_Log::pushNotice('attr_value',$attr_value);

            if(true == self::_use_redis())
            {
                    if(true == Dl_Usercenter_Attrnamemap::isRedisAttr($attr_name))
                    {
                            $attr_value = self::toGbkEncodeAndSerialize($attr_value,'utf-8');

                            Bingo_Log::pushNotice("use_redis", 1);
                            return self::_redis_setUserAttr($user_id, $attr_name, $attr_value);
                    }
            }



            $attr_id = self::getAttrIdByName($attr_name);
            if(null === $attr_id)
            {
                    Bingo_Log::warning("get attr_id by name failed!attr_name:" . $attr_name);
                    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            Bingo_Log::pushNotice('attr_id',$attr_id);

            $db_instance = self::_getUtfDB();
            if(true === is_null($db_instance))
            {
                    Bingo_Log::warning("connect with db failed on try add user info");
                    return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
            }

            $tb_name = self::DB_TABLE_NAME ;
            //各端nickname信息存储改造 by xiaojianxin
            if(Dl_Usercenter_Attrnamemap::isNickNameList($attr_name)){
                    $origin = $attr_value;
                    $attr_value = base64_encode($attr_value);
                    //nani_nickname逻辑 by xiaojianxin
                    $req = array(
                                    "originNickName"=>$origin,
                                    "base64NickName"=>$attr_value,
                                    "attr_id" => $attr_id,
                                    "user_id" => $user_id,
                                    "attr_name" => $attr_name,
                                    "from_app" => Dl_Usercenter_Attrnamemap::getFromAppByName($attr_name),
                                );
                    $res = self::setNickName($req);
                    if (false === $res){
                            Bingo_Log::warning("set NickName fail params:".serialize($req));
                            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                    }
                    if($res["errno"] != Tieba_Errcode::ERR_SUCCESS) {
                            return $res;
                    }  
                    if(empty(trim($origin))){
                            $error = Tieba_Errcode::ERR_SUCCESS;
                            $arrOutput = array(
                                            'errno' => $error,
                                            'errmsg' => Tieba_Error::getErrmsg($error),
                                            );
                            return $arrOutput;
                    }
                    //todo: 加入检索库
					if ($from_app == 0){
                            $arrPassInfo = self::getPassByUids(array($user_id));
                            $username = isset($arrPassInfo[$user_id]['username'])? $arrPassInfo[$user_id]['username'] : "";
                            $timeStr = sprintf("%sT%sZ", date("Y-m-d"), date("h:i:s"));
                            $arr = array(
                                            "uid" => $user_id,
                                            "uname" => $username,
                                            "uname_new" => $username,
                                            "nickname" => $user_nickname,
                                            "create_time" => $timeStr,
                                        );
                            $input = array(
                                            'dataType' => "tieba_person",
                                            'action' => "add",
                                            'id' => $user_id,
                                            'fields' => json_encode($arr)
                                          );
							$arrNMQReq['list'] = $input;
							$strTopic = self::UC_NMQ_TOPIC;
							$strCmd = self::UC_NMQ_CMD;

							$arrNMQRes = Tieba_Commit::commit($strTopic, $strCmd, $arrNMQReq);
							if ( $arrNMQRes === false || !isset($arrNMQRes['err_no']) || $arrNMQRes['err_no'] !=0 ) {
								Bingo_Log::warning( sprintf('Failed to call topic:[%s] cmd:[%s] [%s][%s]',$strTopic,$strCmd,serialize($arrNMQReq),serialize($arrNMQRes)));
							}
                    }

            }

            $escape_value = $db_instance->escapeString($attr_value);
            $query_sql = sprintf("REPLACE INTO %s (user_id,attr_id,attr_value) VALUES (%d,%d,'%s')",
                            $tb_name,$user_id,$attr_id,$escape_value);

            //Bingo_Log::debug("exec sql: " . $query_sql);

            $db_res = $db_instance->query($query_sql);
            if(false === $db_res)
            {
                    Bingo_Log::warning("query sql " . $query_sql . "failed!");
                    return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }

            $cache_instance = self::_getCache();
            $utf_cache_key = self::UTF_US_UID_INFO_PREFIX . $user_id;
            $utf_cache_key_go = self::UTF_US_UID_INFO_PREFIX_GO . $user_id;
            if(null != $cache_instance)
            {
                    $cache_instance->remove($utf_cache_key);
                    $cache_instance->remove($utf_cache_key_go);
                    //TODO err log here
            }

            self::_writeMonitorLog($user_id, $attr_name, $escape_value);

            $error = Tieba_Errcode::ERR_SUCCESS;
            $arrOutput = array(
                            'errno' => $error,
                            'errmsg' => Tieba_Error::getErrmsg($error),
                            );
            return $arrOutput;

    }
    /**
     * @brief
     * @arrInput:
     * 	uint32_t user_id
     * 	string attr_name
     * @return: $arrOutput
    **/ 
    public function setNickName($arrInput){

            $originNickName = $arrInput["originNickName"];
            $base64NickName = $arrInput["base64NickName"];
            $user_id = $arrInput["user_id"];
            $attr_name = $arrInput["attr_name"];
            $attr_id = $arrInput["attr_id"];
            $from_app = $arrInput["from_app"]; 
            //修改昵称之前检查昵称是否存在相同的用户名

            $db_instance = self::_getUtfDB();

            $base64NickName = $db_instance->escapeString($base64NickName);
            $tb_name = self::DB_TABLE_NAME ;
            $tb_user_nickname = self::DB_TABLE_USERNICKNAME;

            if($from_app == 0){
                    $passReq = array($originNickName);
                    $passRes = Service_Tool::getUidsByUnamesFromPassgate($passReq,"utf-8");
                    if($passRes[$originNickName]['userid'] != 0 && $user_id != $passRes[$originNickName]['userid']){
                            Bingo_Log::warning("there is a same name in passgate: user_nickname=" . $originNickName." uid=".$passRes[$originNickName]['user_id']);

                            return self::_errRet(Tieba_Errcode::ERR_MO_FORUM_USER_NICKNAME_EXIST);
                    }
            }

            $query_sql = sprintf("select attr_value from %s where user_id = %d and attr_id =%d limit 1", $tb_name, $user_id,$attr_id);
            $db_res = $db_instance->query($query_sql);
            if(false === $db_res){
                    Bingo_Log::warning("query sql " . $query_sql . "failed!");
                    return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }else if(isset($db_res[0]) && isset($db_res[0]['attr_value'])){
                    $dbOriginNickName = $db_res[0]['attr_value'];
                    if(trim($originNickName) == ''){
                            $param = array(
                                'user_id' => $user_id, 
                                'attr_name' => $attr_name, 
                                'attr_value' => $dbOriginNickName
                            );
                            if($from_app == 0) {
                                return self::removeUserInfo($param);
                              
                            }else {
                                $param["from_app"] = $from_app;
                                return self::removeNewNickUserInfo($param);
                            }

                    }else if($base64NickName === $dbOriginNickName){
                            $error = Tieba_Errcode::ERR_SUCCESS;
                            $arrOutput = array(
                                            'errno' => $error,
                                            'errmsg' => Tieba_Error::getErrmsg($error),
                                            );
                            return $arrOutput;
                    }
                    $query_sql = sprintf("select 1 from %s where nickname = '%s' and user_id != %d and from_app=%d limit 1", $tb_user_nickname, $base64NickName, $user_id,$from_app);
                    $db_res = $db_instance->query($query_sql);
                    if(false === $db_res){
                            Bingo_Log::warning("query sql " . $query_sql . "failed!");
                            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);

                    }else if(isset($db_res[0]) && isset($db_res[0]['1'])){ //user nickname exist
                            return self::_errRet(Tieba_Errcode::ERR_MO_FORUM_USER_NICKNAME_EXIST);
                    }

                    //delete nickname cache
                    $cacheInstance = self::_getCache();
                    $nickNameCacheKey = self::$_nickname_cache_key[$from_app].$dbOriginNickName;
                    $ret = $cacheInstance->remove($nickNameCacheKey);
                    if($ret === false){
                            return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
                    }
                    //清除go缓存
                    $nickNameCacheKeyGo = self::$_nickname_cache_key_go[$from_app].$dbOriginNickName;
                    $ret = $cacheInstance->remove($nickNameCacheKeyGo);
                    if($ret === false){
                            return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
                    }




                    $query_sql = "delete from $tb_user_nickname where nickname='".$dbOriginNickName."' and from_app=".$from_app;
                    $db_instance->query($query_sql);
                    $query_sql = sprintf("INSERT INTO %s (user_id,nickname,update_time,from_app) VALUES (%d,'%s',%d,%d)",$tb_user_nickname,$user_id,$base64NickName,time(),$from_app);
                    $db_instance->query($query_sql);

            }else{
                    if(trim($originNickName) == ''){
                            $error = Tieba_Errcode::ERR_SUCCESS;
                            $arrOutput = array(
                                            'errno' => $error,
                                            'errmsg' => Tieba_Error::getErrmsg($error),
                                            );
                            return $arrOutput;
                    }
                    $query_sql = sprintf("select 1 from %s where nickname = '%s' and user_id != %d and from_app=%d limit 1", $tb_user_nickname, $base64NickName, $user_id,$from_app);
                    $db_res = $db_instance->query($query_sql);
                    if(false === $db_res){
                            Bingo_Log::warning("query sql " . $query_sql . "failed!");
                            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                    }else if(isset($db_res[0]) && isset($db_res[0]['1'])){ //user nickname exist
                            return self::_errRet(Tieba_Errcode::ERR_MO_FORUM_USER_NICKNAME_EXIST);
                    }

                    $query_sql = sprintf("INSERT INTO %s (user_id,nickname,update_time,from_app) VALUES (%d,'%s',%d,%d)",$tb_user_nickname,$user_id,$base64NickName,time(),$from_app);
                    $db_instance->query($query_sql);
            } 

            $error = Tieba_Errcode::ERR_SUCCESS;
            $arrOutput = array(
                            'errno' => $error,
                            'errmsg' => Tieba_Error::getErrmsg($error),
                            );
            return $arrOutput;

    }
    /**
     * @brief
     * @arrInput:
     * 	uint32_t user_id
     * 	string attr_name
     * @return: $arrOutput
    **/
    public static function removeNewNickUserInfo($arrInput){

            // input params check;
            // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
            if(!isset($arrInput['user_id']) || !isset($arrInput['attr_name'])){
                    Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }

            //input params.
            $user_id = intval($arrInput['user_id']);
            $attr_name = $arrInput['attr_name'];
            $from_app = $arrInput['from_app'];

            if(!self::_init()){
                    return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
            }
            //output params.

            //your code here......
            $attr_id = self::getAttrIdByName($attr_name);
            if($attr_id == null){
                    Bingo_Log::warning("get attr_id by name failed!attr_name:" . $attr_name);
                    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }

            $db_instance = self::_getUtfDB();
            if(true === is_null($db_instance))
            {
                    Bingo_Log::warning("connect with db failed on try remove user info");
                    return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
            }

            $tb_name = self::DB_TABLE_NAME ;

            $query_sql = sprintf("DELETE FROM %s WHERE user_id = %s and attr_id = %d",
                            $tb_name, $user_id, $attr_id);

            //Bingo_Log::debug("exec sql: " . $query_sql);

            $db_res = $db_instance->query($query_sql);
            if(false === $db_res)
            {
                    Bingo_Log::warning("query sql " . $query_sql . "failed!");
                    return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }

            $cache_instance = self::_getCache();
            $utf_cache_key = self::UTF_US_UID_INFO_PREFIX . $user_id;
            $utf_cache_key_go = self::UTF_US_UID_INFO_PREFIX_GO . $user_id;
            if(null != $cache_instance)
            {
                    $remove_ret = $cache_instance->remove($utf_cache_key);
                    $remove_ret = $cache_instance->remove($utf_cache_key_go);
                    //TODO err log here
            }

            Bingo_Log::pushNotice('attr_name',$attr_name);
            Bingo_Log::pushNotice('attr_id',$attr_id);

            if($arrInput['attr_value']){
                    $tb_user_nickname = self::DB_TABLE_USERNICKNAME;
                    $nickname_base64 = $arrInput['attr_value'];
                    //delete nickname cache
                    $cache_instance = self::_getCache();
                    $nickname_cache_key = self::$_nickname_cache_key[$from_app].$nickname_base64;
                    $ret = $cache_instance->remove($nickname_cache_key);
                    if($ret === false){
                            return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
                    }
                    //清除go缓存
                    $nickname_cache_key_go = self::$_nickname_cache_key_go[$from_app].$nickname_base64;
                    $ret = $cache_instance->remove($nickname_cache_key_go);
                    if($ret === false){
                            return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
                    }

                    $query_sql = "delete from $tb_user_nickname where nickname='".$nickname_base64."' and from_app=".$from_app;
                    $db_res = $db_instance->query($query_sql);
                    if(false === $db_res)
                    {
                            Bingo_Log::warning("query sql " . $query_sql . "failed!");
                            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                    }

            }

            $error = Tieba_Errcode::ERR_SUCCESS;
            $arrOutput = array(
                            'errno' => $error,
                            'errmsg' => Tieba_Error::getErrmsg($error),
                            );
            return $arrOutput;
    }


    /**
     * @brief
     * @arrInput:
     * 	uint32_t user_id
     * 	string attr_name
     * @return: $arrOutput
    **/
    public static function removeUserInfo($arrInput){

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['user_id']) || !isset($arrInput['attr_name'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input params.
        $user_id = intval($arrInput['user_id']);
        $attr_name = $arrInput['attr_name'];

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.

        //your code here......
        $attr_id = self::getAttrIdByName($attr_name);
        if($attr_id == null){
            Bingo_Log::warning("get attr_id by name failed!attr_name:" . $attr_name);
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $db_instance = self::_getUtfDB();
        if(true === is_null($db_instance))
        {
            Bingo_Log::warning("connect with db failed on try remove user info");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $tb_name = self::DB_TABLE_NAME ;

        $query_sql = sprintf("DELETE FROM %s WHERE user_id = %s and attr_id = %d",
            $tb_name, $user_id, $attr_id);

        //Bingo_Log::debug("exec sql: " . $query_sql);

        $db_res = $db_instance->query($query_sql);
        if(false === $db_res)
        {
            Bingo_Log::warning("query sql " . $query_sql . "failed!");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $cache_instance = self::_getCache();
        $utf_cache_key = self::UTF_US_UID_INFO_PREFIX . $user_id;
        $utf_cache_key_go = self::UTF_US_UID_INFO_PREFIX_GO . $user_id;
        if(null != $cache_instance)
        {
            $remove_ret = $cache_instance->remove($utf_cache_key);
            $remove_ret = $cache_instance->remove($utf_cache_key_go);
            //TODO err log here
        }

        Bingo_Log::pushNotice('attr_name',$attr_name);
        Bingo_Log::pushNotice('attr_id',$attr_id);

        //删除昵称的时候需要额外把user_nickname_info和检索库的记录删除
        if($attr_id == 3 && $arrInput['attr_value']){
            $tb_user_nickname = self::DB_TABLE_USERNICKNAME;
            $nickname_base64 = $arrInput['attr_value'];
            //delete nickname cache
            $cache_instance = self::_getCache();
            $nickname_cache_key = self::$_nickname_cache_key[0].$nickname_base64;
            $ret = $cache_instance->remove($nickname_cache_key);
            if($ret === false){
                    return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
            }
            //清除go 缓存
            $nickname_cache_key_go = self::$_nickname_cache_key_go[0].$nickname_base64;
            $ret = $cache_instance->remove($nickname_cache_key_go);
            if($ret === false){
                    return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
            }


            $query_sql = "delete from $tb_user_nickname where nickname='".$nickname_base64."' and from_app=0";
            $db_res = $db_instance->query($query_sql);
            if(false === $db_res)
            {
                Bingo_Log::warning("query sql " . $query_sql . "failed!");
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            
            $arrPassInfo = self::getPassByUids(array($user_id));
            $username = isset($arrPassInfo[$user_id]['username'])? $arrPassInfo[$user_id]['username'] : "";
            $timeStr = sprintf("%sT%sZ", date("Y-m-d"), date("h:i:s"));
            $arr = array(
                "uid" => $user_id,
                "uname" => $username,
                "uname_new" => $username,
                "nickname" => '',
                "create_time" => $timeStr,
            );


            $input = array(
                'dataType' => "tieba_person",
                'action' => "add",
                'id' => $user_id,
                'fields' => json_encode($arr)
            );
            $res   = Tieba_Service::call('search', 'updateSc', $input, null, null, 'post', 'php', 'utf-8');
            if(!$res || $res['errno'] != 0){
                Bingo_Log::warning("insert into search respository failed, error: ".json_encode($res));
            }
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
    }

    /**
     * @param $Unicknames
     *
     * @return [unickname => uid, ...]
     */
    public static function getUidByUserNicknames($arrInput){
        
        if (empty($arrInput["from_app"])){
            $from_app = 0;
        }else {
            $from_app = $arrInput["from_app"];
        }
        $Unicknames = $arrInput["Unicknames"];
        $dlOutput = array();
        $total_num = count($Unicknames);
        if($total_num > 30)
        {
            return false;
        }
        if(is_array($Unicknames)){
            $cache = self::_getCache();
            if(null === $cache)
            {
                Bingo_Log::warning("connect cache failed!");
            }
            $nickname_cache_keys = array();
            $hit_cache_nicknames = array();
            foreach($Unicknames as $key => $nickname)
            {
                $nickname = base64_encode($nickname);
              
                $nickname_cache_keys[] = self::$_nickname_cache_key[$from_app].$nickname;
            }
            Bingo_Timer::start('getcache');
            $cache_res = $cache->multipleGet($nickname_cache_keys);
            Bingo_Timer::end('getcache');
            if(null != $cache_res )
            {
                foreach($cache_res as $key => $item)
                {
                    $nickname_key = explode('_',$key);
                    $nickname = base64_decode($nickname_key[1]);
                    $user_attr = explode('_',$item);
                    $user_id = $user_attr[0];
                    $update_time = $user_attr[1];
                    $dlOutput[$nickname] = array(
                        'user_id' => $user_id,
                        'update_time' => $update_time,
                    );
                    $hit_cache_nicknames[] = $nickename;
                    $cache_hit_num ++;
                }
            }

            Bingo_Log::pushNotice("cache_hit",$cache_hit_num);
            Bingo_Log::pushNotice("cache_not_hit",$total_num-$cache_hit_num);
            if($total_num == $cache_hit_num)
            {
                return array('data' => $dlOutput);
            }

            $Unicknames = array_diff($Unicknames,$hit_cache_nicknames);
            $db_instance = self::_getUtfDB();
            if(true === is_null($db_instance))
            {
                Bingo_Log::warning("connect with db failed on try get uid by nicknames");
                return false;
            }

            $tb_name = self::DB_TABLE_USERNICKNAME ;
            foreach($Unicknames as $index => $nickname){
                $Unicknames[$index] = "'".base64_encode($nickname)."'";
            }

            $query_sql = "select * from " . $tb_name . " where from_app=".$from_app." and nickname in (" . implode(",", $Unicknames) . ")";


            $db_res = $db_instance->query($query_sql);
            $db_hit_num = 0;
            if(false === $db_res)
            {
                Bingo_Log::warning("query sql " . $query_sql . "failed!");
                return array('data' => $dlOutput);
            }else{
                foreach($db_res as $info){
					$dlOutput[base64_decode($info['nickname'])] = array(
						"user_id" => $info['user_id'] ,
						"update_time" => $info['update_time'] ,
                                            );
                                        $db_hit_num++;
                                        $ret = $cache->add(self::$_nickname_cache_key[$from_app].$info['nickname'],$info['user_id']."_".$info['update_time'],self::UNICKNAME_UID_EXPIRE_TIME);
                                        if ($ret === false || $ret === null){
                                            Bingo_Log::warning("Add miss nickname cache failed!");
                                        } 
                }

                Bingo_Log::pushNotice("db_hit_num",$db_hit_num);
                return array('data' => $dlOutput);
            }
        }else{
            return false;
        }
    }


    public static function delCacheByKey($cache_key){

        Bingo_Log::pushNotice('delcache',$cache_key);
        $remove_ret = -1;
        $cache_instance = self::_getCache();
        if(null != $cache_instance && !empty($cache_key))
        {
            $remove_ret = $cache_instance->remove($cache_key);
            //TODO err log here
        }

        if ($remove_ret === 0){
            $error = Tieba_Errcode::ERR_SUCCESS;
        }
        else {
            $error = Tieba_Errcode::ERR_UNKOWN;
        }
        $arrOutput = array(
                'errno' => $error,
                'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;

    }

    public static function getCacheByKey($cache_keys){
        Bingo_Log::pushNotice('getcache',1);
        $cache_res = -1;
        $cache_instance = self::_getCache();
        if(null != $cache_instance && is_array($cache_keys)){
            $cache_res = $cache_instance->multipleGet($cache_keys);
        }


        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
                'errno' => $error,
                'errmsg' => Tieba_Error::getErrmsg($error),
                'data' => $cache_res,
        );
        return $arrOutput;
    }

    //modify by gongwen 20140622
    private static function getAttrIdByName($attr_name){
        if (self::$_extattr_conf == null){
            self::_init();
        }

        if (isset(self::$_extattr_conf['user']['ext_attr'][$attr_name])){
            $attr_fields = self::$_extattr_conf['user']['ext_attr'][$attr_name];
            $attr_id = intval($attr_fields['Id']);
            return $attr_id;
        }else{
            Bingo_Log::warning("field is illegal, key: $attr_name" );
            return null;
        }
    }

    //modify by gongwen 20140622
    private static function getAttrNameById($attr_id){
        if (self::$_extattr_conf == null){
            self::_init();
        }

        if (isset(self::$_extattr_conf['user']['ext_attr'])){
            $user_attrs = self::$_extattr_conf['user']['ext_attr'];
            $attrs = array_keys($user_attrs);
             foreach ($attrs as $attr){
                $user_attr = $user_attrs[$attr];
                if(intval($user_attr['Id']) == intval($attr_id)){
                          return $attr;
                    }
             }
        }
        Bingo_Log::warning("can not find attr_name with attr_id: $attr_id" );
        return null;
    }
    function is_array_serialized_tostring( $str )
    {
        $data = trim($str);
        if ( preg_match( '/^a:[0-9]+:{.*}$/s', $data ) )
            return true;
        return false;
    }


    private static function _getUtfDB()
    {
        Bingo_Timer::start('dbinit');
        $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME_UTF);
        Bingo_Timer::end('dbinit');
        if($objTbMysql && $objTbMysql->isConnected())
        {
            $objTbMysql->charset('utf8');
            return $objTbMysql;
        }
        Bingo_Log::warning("utf8 db connect fail.");
        return null;
    }

    /*
     * 主要是处理GBK序列化后的数据转码成UTF8序列化后数据
     */
    private static function toUtfEncodeAndSerialize($attrValue,$encode='utf-8'){
        //什么编码的情况下进行转码
        if($encode === self::$dl_ie && !is_numeric($attrValue)){
            if(self::is_array_serialized_tostring($attrValue)){//判断该属性是否是序列化后数据

                $attrValueTmp=unserialize($attrValue);//反序列化后进行转码然后再进行序列化，防止字符长度错误
                if($attrValueTmp !== false){
                    $attrValue = Bingo_Encode::convert($attrValueTmp,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
                    $attrValue = serialize($attrValue);
                }else{
                    $attrValue = Bingo_Encode::convert($attrValue,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
                }

            }else{
                $attrValue = Bingo_Encode::convert($attrValue,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
            }
        }
        return $attrValue;
    }

    /*
     * 主要是处理utf8序列化后的数据转码成gbk序列化后数据
     */
    private static function toGbkEncodeAndSerialize($attrValue,$encode='utf-8'){
        //什么编码的情况下进行转码
        if($encode === self::$dl_ie  && !is_numeric($attrValue)){
            if(self::is_array_serialized_tostring($attrValue)){//判断该属性是否是序列化后数据
                $attrValueTmp=unserialize($attrValue);//反序列化后进行转码然后再进行序列化，防止字符长度错误
                if($attrValueTmp !== false){
                    $attrValue = Bingo_Encode::convert($attrValueTmp,Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
                    $attrValue = serialize($attrValue);
                }else{
                    $attrValue = Bingo_Encode::convert($attrValue,Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
                }

            }else{
                $attrValue = Bingo_Encode::convert($attrValue,Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
            }
        }
        return $attrValue;
    }

}

