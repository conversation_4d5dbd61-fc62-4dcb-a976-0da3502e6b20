<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013:04:07 17:52:10
 * @version 
 * @structs & methods(copied from idl.)
*/

//*************************************************************************************
// IDL CONTENT BEGIN: 
//*************************************************************************************
// 	#ifndef __FAVO_FORUM_IDL__
// 	#define __FAVO_FORUM_IDL__
// 	
// 	struct update_forums
// 	{
// 		uint32_t command_no = optional();
// 		uint64_t trans_id = optional();
// 		uint32_t uid;
// 		uint32_t forum_id = optional();
// 		string  forum_name = optional();
// 		string fnames[] = optional();
// 	
// 		uint32_t forum_type = optional(); //used for deleted forums of like or others(not include user forum and favo forum)
// 	};
// 	
// 	struct favos
// 	{
// 		uint32_t type;
// 		string forum_name;
// 	};
// 	
// 	service favo_forum{
// 	
// 		void user_query(
// 				uint32_t user_id,
// 				uint32_t offset,
// 				uint32_t limit,
// 				out string forums[],
// 				out uint32_t has_more
// 	
// 				);
// 		
// 		void user_query_favo(
// 				uint32_t user_id,
// 				uint32_t offset,
// 				uint32_t limit,
// 				out favos forums[],
// 				out uint32_t has_more
// 	
// 				);
// 	
// 		void itieba_commit(update_forums cmd);
// 	
// 		void get_mask_forums(uint32_t user_id,					
// 				out string forums[]); //used for get deleted favo forums
// 	
// 		void get_deleted_forums(uint32_t user_id,
// 				out favos forums[]); //used for get deleted like forums
// 	};
// 	
// 	
// 	#endif
// 	
// 	
// 	
// 	
//*************************************************************************************
// IDL CONTENT END. 
//*************************************************************************************


class Dl_Favoforum_Favoforum{

const SERVER_NAME = 'favo_forum';
const RAL_SERVICE_NAME = 'favo-forum';
public static $dl_ie = 'gbk';

public static function _call($arrInput,$strMethod,$intBalanceKey){
    $strTimer = 'ral_call_'.self::SERVER_NAME.'_'.$strMethod;
    Bingo_Timer::start($strTimer);
    Bingo_Log::debug("ral call [method:{$strMethod}] [input".serialize($arrInput)."]");
    $out = camel(self::RAL_SERVICE_NAME,'query',$arrInput,$intBalanceKey);
    Bingo_Timer::end($strTimer);
    if (!$out || !is_array($out) || !isset($out['content'][0]['result_params'])){
        Bingo_Log::warning("ral call error[method:{$strMethod}] [input".serialize($arrInput)."]");
        return false;
    }
    return $out['content'][0]['result_params'];
}
/**
 * @brief
 * @params: $arrInput:
 * 	uint32_t user_id
 * 	uint32_t offset
 * 	uint32_t limit
 * @return: $arrOutput:
 * 	string forums[]
 * 	uint32_t has_more
**/
public static function user_query($arrInput, $intBalanceKey = NULL){
	if(!$intBalanceKey){
		$intBalanceKey = rand();
	}
	$ralInput = Tbapi_Core_Util_Rpc::packMcpackRpcInput();
	$ralInput['content'][0]['method'] = 'user_query';
	$ralInput['content'][0]['service_name'] = self::SERVER_NAME;
	$ralInput['content'][0]['params'] = array(
		'user_id' => $arrInput['user_id'],
		'offset' => $arrInput['offset'],
		'limit' => $arrInput['limit'],
	);
	$arrOut = self::_call($ralInput,'user_query', $intBalanceKey);
	return $arrOut;
}
/**
 * @brief
 * @params: $arrInput:
 * 	uint32_t user_id
 * 	uint32_t offset
 * 	uint32_t limit
 * @return: $arrOutput:
 * 	favos forums[]
 * 	uint32_t has_more
**/
public static function user_query_favo($arrInput){
	
	if(isset($arrInput['balance_key'])){
		$intBalanceKey = $arrInput['balance_key'];
	} else {
		$intBalanceKey = rand();
	}
	
	$ralInput = Tbapi_Core_Util_Rpc::packMcpackRpcInput();
	$ralInput['content'][0]['method'] = 'user_query_favo';
	$ralInput['content'][0]['service_name'] = self::SERVER_NAME;
	$ralInput['content'][0]['params'] = array(
		'user_id' => $arrInput['user_id'],
		'offset' => $arrInput['offset'],
		'limit' => $arrInput['limit'],
	);
	$arrOut = self::_call($ralInput,'user_query_favo', $intBalanceKey);
	return $arrOut;
}
/**
 * @brief
 * @params: $arrInput:
 * 	update_forums cmd
 * @return: $arrOutput:
**/
public static function itieba_commit($arrInput, $intBalanceKey = NULL){
	if(!$intBalanceKey){
		$intBalanceKey = rand();
	}
	$ralInput = Tbapi_Core_Util_Rpc::packMcpackRpcInput();
	$ralInput['content'][0]['method'] = 'itieba_commit';
	$ralInput['content'][0]['service_name'] = self::SERVER_NAME;
	$ralInput['content'][0]['params'] = array(
		'cmd' => $arrInput['cmd'],
	);
	$arrOut = self::_call($ralInput,'itieba_commit', $intBalanceKey);
	return $arrOut;
}
/**
 * @brief
 * @params: $arrInput:
 * 	uint32_t user_id
 * @return: $arrOutput:
 * 	string forums[]
**/
public static function get_mask_forums($arrInput, $intBalanceKey = NULL){
	if(!$intBalanceKey){
		$intBalanceKey = rand();
	}
	$ralInput = Tbapi_Core_Util_Rpc::packMcpackRpcInput();
	$ralInput['content'][0]['method'] = 'get_mask_forums';
	$ralInput['content'][0]['service_name'] = self::SERVER_NAME;
	$ralInput['content'][0]['params'] = array(
		'user_id' => $arrInput['user_id'],
	);
	$arrOut = self::_call($ralInput,'get_mask_forums', $intBalanceKey);
	return $arrOut;
}
/**
 * @brief
 * @params: $arrInput:
 * 	uint32_t user_id
 * @return: $arrOutput:
 * 	favos forums[]
**/
public static function get_deleted_forums($arrInput){
	
	if(isset($arrInput['balance_key'])){
		$intBalanceKey = $arrInput['balance_key'];
	} else {
		$intBalanceKey = rand();
	}
	
	$ralInput = Tbapi_Core_Util_Rpc::packMcpackRpcInput();
	$ralInput['content'][0]['method'] = 'get_deleted_forums';
	$ralInput['content'][0]['service_name'] = self::SERVER_NAME;
	$ralInput['content'][0]['params'] = array(
		'user_id' => $arrInput['user_id'],
	);
	$arrOut = self::_call($ralInput,'get_deleted_forums', $intBalanceKey);
	return $arrOut;
}
}
