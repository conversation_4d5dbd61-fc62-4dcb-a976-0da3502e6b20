<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013:01:03 15:05:01
 * @version 
 * @structs & methods(copied from idl.)
 * 	#ifndef __FUCENTER_IDL__
 * 	#define __FUCENTER_IDL__
 * 	
 * 	struct signature {
 * 		uint64_t sid;
 * 		uint32_t user_id;
 * 		int32_t status;
 * 		int32_t inner_sid;
 * 		int32_t is_default;
 * 		int32_t simg_width;
 * 		int32_t simg_height;
 * 		string stitle;
 * 		string scontent;
 * 		string surl;
 * 	};
 * 	
 * 	struct get_sign_by_uid_output {
 * 		uint32_t	is_force_hide;
 * 		signature signatures[];
 * 	};
 * 	
 * 	service fucenter{
 * 		
 * 		// ��ȡ�û�ǩ����
 * 		void getSignByUid(uint32_t user_id,out get_sign_by_uid_output output);
 * 	
 * 	
 * 	};
 * 	
 * 	
 * 	#endif
**/
class Dl_Fucenter_Fucenter{

const SERVER_NAME = 'fucenter';
const RAL_SERVICE_NAME = 'fucenter';

const UserInfoBrowser = 400;
const GetValidSignByUid = 9999;
const GetIdByUnames = 9994;
const ForumUserManager = 6000;
const GetSignBySid = 9997;

public static function _call($arrInput,$strMethod,$intBalanceKey){
    $strTimer = 'ral_call_'.self::SERVER_NAME.'_'.$strMethod;
    Bingo_Timer::start($strTimer);
    Bingo_Log::debug("ral call [method:{$strMethod}] [input".serialize($arrInput)."]");
    $out = camel(self::RAL_SERVICE_NAME,'query',$arrInput,$intBalanceKey);
    Bingo_Timer::end($strTimer);
    if (!$out || !is_array($out)){
        Bingo_Log::warning("ral call error[method:{$strMethod}] [input".serialize($arrInput)."]");
        return false;
    }
    return $out;
}
/**
 * @brief
 * @params: $arrInput:
 * 	uint32_t user_id
 * @return: $arrOutput:
 * 	get_sign_by_uid_output output
**/
public static function getSignByUid($arrInput, $intBalanceKey = NULL) {
	if (! $intBalanceKey) {
		$intBalanceKey = rand();
	}
	$ralInput = array (
			'command_no' => self::GetValidSignByUid,
			'user_id' => intval($arrInput ['user_id']), 
	);
	$arrOut = self::_call ( $ralInput, 'getSignByUid', $intBalanceKey );
	if ($arrOut === false) {
		return false;
	}
	$arrDlOutput = array();
	if (isset ( $arrOut['is_force_hide'] )) {
		$arrDlOutput['output']['is_force_hide'] = intval ( $arrOut['is_force_hide'] );
	}
	foreach ( $arrOut ['signatures'] as $value ) {
		$arrSignature = array();
		if (isset ( $value['sid'] )) {
			$arrSignature['sid'] = intval( $value['sid'] );
		}
		if (isset ( $value['uid'] )) {
			$arrSignature ['user_id'] = intval( $value['uid'] );
		}
		if (isset ( $value['status'] )) {
			$arrSignature ['status'] = intval( $value['status'] );
		}
		if (isset ( $value['inner_sid'] )) {
			$arrSignature ['inner_sid'] = intval( $value['inner_sid'] );
		}
		if (isset ( $value['is_default'] )) {
			$arrSignature['is_default'] = intval( $value['is_default'] );
		}
		if (isset ( $value['simg_width'] )) {
			$arrSignature['simg_width'] = intval( $value['simg_width'] );
		}
		if (isset ( $value['simg_height'] )) {
			$arrSignature['simg_height'] = intval( $value['simg_height'] );
		}
		if (isset ( $value['stitle'] )) {
			$arrSignature['stitle'] = $value['stitle'];
		}
		if (isset ( $value['scontent'] )) {
			$arrSignature['scontent'] = $value['scontent'];
		}
		if (isset ( $value['surl'] )) {
			$arrSignature['surl'] = $value['surl'];
		}
		$arrDlOutput['output']['signatures'][] = $arrSignature;
	}
	return $arrDlOutput;
}

/**
 * @brief
 * @params: $arrInput:
 *      uint32_t user_id
 * @return: $arrOutput:
 *      get_sign_by_uid_output output
**/
public static function getUserInfoByUids($arrInput, $intBalanceKey = NULL) {
	if (! $intBalanceKey) {
		$intBalanceKey = rand();
	}
	$ralInput = array (
			'command_no' => self::ForumUserManager,
			'uids' => $arrInput ['user_id'] 
	);
	$arrOut = self::_call ( $ralInput, 'getUserInfoByUids', $intBalanceKey );
	if ($arrOut === false) {
		return false;
	}
	$arrDlOutput = array();
	foreach ( $arrOut ['uinfos'] as $value ) {
		$arrInfo = array();
		if (isset ( $value['uid'] )) {
			$arrInfo['user_id'] = intval( $value ['uid'] );
		}
		if (isset ( $value['lastlogin_time'] )) {
			$arrInfo['lastlogin_time'] = intval( $value['lastlogin_time'] );
		}
		if (isset ( $value['lastlogin_ip'] )) {
			$arrInfo['lastlogin_ip'] = intval( $value['lastlogin_ip'] );
		}
		if (isset ( $value ['username'] )) {
			$arrInfo['user_name'] = $value['username'];
		}
		$arrDlOutput['output'][] = $arrInfo;
	}
	
	return $arrDlOutput;
}

public static function getUserInfoByUname($arrInput, $intBalanceKey = NULL) {
	if (! $intBalanceKey) {
		$intBalanceKey = rand();
	}
	$ralInput = array (
			'command_no' => self::UserInfoBrowser,
			'username' => $arrInput['user_name'] 
	);
	$arrOut = self::_call( $ralInput, 'getUserInfoByUname', $intBalanceKey );
	if ($arrOut === false) {
		return false;
	}
	$arrDlOutput = array();
	if (isset ( $arrOut['uinfos'][0]['uid'] )) {
		$arrDlOutput ['output']['user_id'] = intval( $arrOut['uinfos'][0]['uid'] );
	}
	if (isset ( $arrOut['uinfos'][0]['lastlogin_time'] )) {
		$arrDlOutput ['output'] ['lastlogin_time'] = intval( $arrOut['uinfos'][0]['lastlogin_time'] );
	}
	if (isset ( $arrOut['uinfos'][0]['lastlogin_ip'] )) {
		$arrDlOutput ['output'] ['lastlogin_ip'] = intval( $arrOut['uinfos'][0]['lastlogin_ip'] );
	}
	if (isset ( $arrOut['uinfos'][0]['username'] )) {
		$arrDlOutput['output']['user_name'] = $arrOut['uinfos'][0]['username'];
	}
	return $arrDlOutput;
}

public static function getUidByUnames($arrInput, $intBalanceKey = NULL){
	if (! $intBalanceKey) {
		$intBalanceKey = rand();
	}
	$ralInput = array (
			'command_no' => self::GetIdByUnames,
			'unames' => $arrInput ['user_name'] 
	);
	$arrOut = self::_call ( $ralInput, 'getUidByUnames', $intBalanceKey );
	if ($arrOut === false) {
		return false;
	}
	$arrDlOutput = array();
	foreach ( $arrOut['uinfos'] as $value ) {
		$arrInfo = array();
		if (isset ( $value['uid'] )) {
			$arrInfo['user_id'] = intval( $value['uid'] );
		}
		if (isset ( $value['username'] )) {
			$arrInfo['user_name'] = $value['username'];
		}
		$arrDlOutput['output']['uids'][] = $arrInfo;
	}
	
	return $arrDlOutput;
}

public static function getSignBySignIds($arrInput, $intBalanceKey = NULL){
	if (! $intBalanceKey) {
		$intBalanceKey = rand();
	}
	$ralInput = array (
			'command_no' => self::GetSignBySid,
			'sids' => $arrInput ['sign_id']
	);
	$arrOut = self::_call ( $ralInput, 'getSignBySignIds', $intBalanceKey );
	if ($arrOut === false) {
		return false;
	}
	
	$arrDlOutput = array();
	if (isset ( $arrOut['is_force_hide'] )) {
		$arrDlOutput['output']['is_force_hide'] = intval ( $arrOut['is_force_hide'] );
	}
	foreach ( $arrOut ['signatures'] as $value ) {
		$arrSignature = array();
		if (isset ( $value['sid'] )) {
			$arrSignature['sid'] = intval( $value['sid'] );
		}
		if (isset ( $value['uid'] )) {
			$arrSignature ['user_id'] = intval( $value['uid'] );
		}
		if (isset ( $value['status'] )) {
			$arrSignature ['status'] = intval( $value['status'] );
		}
		if (isset ( $value['inner_sid'] )) {
			$arrSignature ['inner_sid'] = intval( $value['inner_sid'] );
		}
		if (isset ( $value['is_default'] )) {
			$arrSignature['is_default'] = intval( $value['is_default'] );
		}
		if (isset ( $value['simg_width'] )) {
			$arrSignature['simg_width'] = intval( $value['simg_width'] );
		}
		if (isset ( $value['simg_height'] )) {
			$arrSignature['simg_height'] = intval( $value['simg_height'] );
		}
		if (isset ( $value['stitle'] )) {
			$arrSignature['stitle'] = $value['stitle'];
		}
		if (isset ( $value['scontent'] )) {
			$arrSignature['scontent'] = $value['scontent'];
		}
		if (isset ( $value['surl'] )) {
			$arrSignature['surl'] = $value['surl'];
		}
		$arrDlOutput['output']['signatures'][] = $arrSignature;
	}
	return $arrDlOutput;
}

}
