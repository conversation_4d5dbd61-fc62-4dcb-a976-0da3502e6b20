<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2016:11:01 12:12:42
 * @version
 * @structs & methods(copied from idl.)
**/
class Dl_Userfollow_Userfollow{
    // db
    private static $_db         = null;
    const DATABASE_NAME         = "user_relation";
    const DATABASE_CHARSET      = "utf8";
    // tables
    const USER_INFO_TABLE       = 'user_info';       //用户基本信息表，uid与itieba_id对应关系
    const BLACK_LIST_TABLE      = 'black_list';      //黑名单表
    const USER_FOLLOW_TABLE     = 'user_follow';     //关注表
    const USER_FOLLOWED_TABLE   = "user_followed";   //粉丝表
    const USER_COUNT_INFO_TABLE = "user_count_info"; //用户关注情况表
    // dl ie
    public static $dl_ie = 'utf-8';
    // nmq cmd no
    const iSetFollower    = 30034; // 关注用户
    const iCancelFollower = 30035; // 取消关注用户
	const iCancelUserFollowed = 30036;

    const followByBdApp = 30037;// 来自百度app的关注请求
    const iAddBlackList   = 30081; // 添加用户黑名单
    const iCanclBlackList = 30082; // 取消用户黑名单
    // cache
    const PREFIX_USER_FOLLOW_CONUT_INFO = 'uc_'; // 用户关注和被关注数目 user_follow_count_info_
    const PREFIX_USER_FOLLOW_LIST       = 'fl_';       // 用户关注列表 user_follow_list_
    const PREFIX_USER_FOLLOWED_LIST     = 'fdl_';     // 用户粉丝列表 user_followed_list_
    const CACHE_EXPIRE_TIME = 3600; // cache失效时间，测试使用，后期改成1小时
    const CACHE_PID = "forum_userrelation"; // 后期改成申请的pid
    private static $_cache = null;

    const PREFIX_USER_FOLLOW_CONUT_INFO_GO = 'ufc_v2:'; // 用户关注和被关注数目 user_follow_count_info_
    const PREFIX_USER_FOLLOW_LIST_GO       = 'ufl_v2:'; // 用户关注列表 user_follow_list_
    const PREFIX_USER_FOLLOWED_LIST_GO     = 'ufdl_v2:';// 用户粉丝列表 user_followed_list_

    //
    const MAX_USER_FOLLOW_COUNT = 1000; // 用户关注人数上线
    const MAX_USER_FOLLOWED_CACHE_COUNT = 1000; // cache中存的被关注信息数目上限
    const DEFAULT_OFFSET = 0;
    const DEFAULT_LIMIT = 20;

    /**
     * @brief
    **/
    private static function _errRet($intErrno = 0, $data = array(), $self = array())
    {
        $strErrMsg = Tieba_Error::getErrmsg($intErrno);
        $arrOutput = array(
            'errno' => $intErrno,
            'errmsg' => $strErrMsg,
            'user_infos' => $data,
        );
        if (!empty($self)) {
            $arrOutput['self'] = $self;
        }
        return $arrOutput;
    }
    /**
     * @brief 初始化cache
     */
    private static function _initCache() {
        if (!is_null(self::$_cache)) {
            return self::$_cache ;
        }
        Bingo_Timer::start('memcached_init');
        self::$_cache = new Bingo_Cache_Memcached(self::CACHE_PID);
        Bingo_Timer::end('memcached_init');
        if(!self::$_cache || !self::$_cache->isEnable()){
            Bingo_Log::warning("init cache fail.");
            self::$_cache = null;
            return null;
        }
        return self::$_cache;
    }

    /*
     * @brief: 参数检查
     */
    private static function _check_multi_input($needCheck, $data) {
        foreach ($needCheck as $val) {
            if (!isset($data[$val])) {
                Bingo_Log::warning("err input params. need key: ".$val);
                return false;
            }
        }
        return true;
    }
    /**
     * @brief 拉黑
    **/
    private static function _iAddBlackList($arrInput = array()){
        // 检验参数
        $arrNeedFields = array('user_id', 'black_user_ids',);
        $bolRes = self::_check_multi_input($arrNeedFields, $arrInput);
        if (false === $bolRes || !is_array($arrInput['black_user_ids'])) {
            Bingo_Log::warning("sunhua input params: ".serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrRet['retry'] = 1;
        $intUserId = intval($arrInput['user_id']);
        $arrBlackUidMap = array();
        $intTime = isset($arrInput['now_time']) ? intval($arrInput['now_time']) : time();
        foreach($arrInput['black_user_ids'] as $val) {
            $intBlackUid = intval($val);
            if ($intBlackUid == 0 || $intBlackUid == $intUserId) {
                Bingo_Log::warning("error user_id: ".$intUserId. " black_user_id: ".$intBlackUid);
                continue;
            }
            $arrBlackUidMap[$intBlackUid] = 1;
        }
        if (0 == $intUserId || empty($arrBlackUidMap)) {
            Bingo_Log::warning("no user need to add black list");
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        // 被拉黑的user_ids
        $arrBlackUids = array_keys($arrBlackUidMap);
        $db = Dl_Userfollow_Db::getDB();
        if (is_null($db)) {
            Bingo_Log::warning("get db failed");
            return $arrRet;
        }
        // 取消关注
        if (!empty($arrBlackUids)) {
            //查找哪些人关注了发起添加黑名单操作的人
            $db->startTransaction();
            $arrGetInput = array(
                'db' => $db,
                'table' => self::USER_FOLLOWED_TABLE,
                'field' => array('from_id'),
                'cond' => array(
                    'from_id' => $arrBlackUids,
                    'to_id=' => $intUserId,
                ),
            );
            $arrGetRes = Dl_Userfollow_Db::select($arrGetInput);
            if (false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']) {
                Bingo_Log::warning("get user followed info fail param:". serialize($arrGetInput)."_output:".serialize($arrGetRes));
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback user followed info get failed.");
                }
                return $arrRet;
            }
            if (false === $db->commit()) {
                Bingo_Log::warning("iAddBlackList Get fail param:". serialize($arrInput));
            }
            $arrFollowerUids = array();
            foreach($arrGetRes['data'] as $val) {
                $arrFollowerUids[] = $val['from_id'];
            }
            Bingo_Timer::start('loop_cancel_user_follow');
            Bingo_Log::pushNotice('follow_count', count($arrFollowerUids));
            // 取消关注
            foreach ($arrFollowerUids as $intFollowerUid) {
                $db->startTransaction();
                // 删除关注信息
                $strSql = sprintf("delete from %s where from_id = %d and to_id = %d;", self::USER_FOLLOW_TABLE, $intFollowerUid, $intUserId);
                $bolUpdateRes = $db->query($strSql);
                $intAffectedRows = intval($db->getAffectedRows()); // 被影响的Rows，做检验用
                if (false === $bolUpdateRes) {
                    Bingo_Log::warning('delete info from user_follow failed. sql:'.$strSql);
                    if (false === $db->rollback()) {
                        Bingo_Log::fatal("rollback user_follow submit failed.");
                    }
                    return $arrRet;
                }
                if (1 == $intAffectedRows) {
                    // 更新关注数
                    $strSql = sprintf("update %s set follow_count = follow_count - 1 where user_id = %d and follow_count > 0;", self::USER_COUNT_INFO_TABLE, $intFollowerUid);
                    $bolRes = $db->query($strSql);
                    if (false === $bolRes) {
                        Bingo_Log::warning('update user_count_info failed. sql:'.$strSql);
                        if (false === $db->rollback()) {
                            Bingo_Log::fatal("rollback user_count_info submit failed.");
                        }
                        return $arrRet;
                    }
                }
                // 提交
                if (false === $db->commit()) {
                    Bingo_Log::warning("iAddBlackList Submit fail param:". serialize($arrInput));
                    if (false === $db->rollback()) {
                        Bingo_Log::fatal("rollback iAddBlackList Submit fail");
                    }
                    return $arrRet;
                }
                // 清除cache
                $arrKeys = array(
                    self::PREFIX_USER_FOLLOW_LIST.$intFollowerUid,
                    self::PREFIX_USER_FOLLOW_CONUT_INFO.$intFollowerUid,


                    self::PREFIX_USER_FOLLOW_LIST_GO.$intFollowerUid,
                    self::PREFIX_USER_FOLLOW_CONUT_INFO_GO.$intFollowerUid,
                );
                self::_initCache();
                if (is_null(self::$_cache)) {
                    Bingo_Log::warning("init cache fail");
                } else {
                    $arrDelRes = self::$_cache->multipleRemove($arrKeys);
                    if (false === $arrDelRes) {
                        Bingo_Log::warning("call cache del failed. keys:".serialize($arrKeys));
                    }
                }
                $db->startTransaction();
                // 删除被关注信息
                $strSql = sprintf("delete from %s where from_id = %d and to_id = %d;", self::USER_FOLLOWED_TABLE, $intFollowerUid, $intUserId);
                $bolUpdateRes = $db->query($strSql);
                $intAffectedRows = intval($db->getAffectedRows()); // 被影响的Rows，做检验用
                if (false === $bolUpdateRes) {
                    Bingo_Log::warning('delete info from user_follow failed. sql:'.$strSql);
                    if (false === $db->rollback()) {
                        Bingo_Log::fatal("rollback user_follow submit failed.");
                    }
                    return $arrRet;
                }
                if (1 == $intAffectedRows) {
                    // 更新被关注数
                    $strSql = sprintf("update %s set followed_count = followed_count - 1 where user_id = %d and followed_count > 0;", self::USER_COUNT_INFO_TABLE, $intUserId);
                    $bolRes = $db->query($strSql);
                    if (false === $bolRes) {
                        Bingo_Log::warning('update user_count_info failed. sql:'.$strSql);
                        if (false === $db->rollback()) {
                            Bingo_Log::fatal("rollback user_count_info submit failed.");
                        }
                        return $arrRet;
                    }
                }
                // 提交
                if (false === $db->commit()) {
                    Bingo_Log::warning("iAddBlackList Submit fail param:". serialize($arrInput));
                    if (false === $db->rollback()) {
                        Bingo_Log::fatal("rollback iAddBlackList Submit fail");
                    }
                    return $arrRet;
                }
                // 清除cache
                $arrKeys = array(
                    self::PREFIX_USER_FOLLOWED_LIST.$intUserId,
                    self::PREFIX_USER_FOLLOW_CONUT_INFO.$intUserId,

                    self::PREFIX_USER_FOLLOWED_LIST_GO.$intUserId,
                    self::PREFIX_USER_FOLLOW_CONUT_INFO_GO.$intUserId,
                );
                self::_initCache();
                if (is_null(self::$_cache)) {
                    Bingo_Log::warning("init cache fail");
                } else {
                    $arrDelRes = self::$_cache->multipleRemove($arrKeys);
                    if (false === $arrDelRes) {
                        Bingo_Log::warning("call cache del failed. keys:".serialize($arrKeys));
                    }
                }
            }
            Bingo_Timer::end('loop_cancel_user_follow');
            Bingo_Timer::start('add_black_list');
            //查找哪些不在黑名单列表中
            $db->startTransaction();
            $arrGetInput = array(
                'db' => $db,
                'table' => self::BLACK_LIST_TABLE,
                'field' => array('black_id'),
                'cond' => array(
                    'user_id=' => $intUserId,
                    'black_id' => $arrBlackUids,
                ),
            );
            $arrGetRes = Dl_Userfollow_Db::select($arrGetInput);
            if (false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']) {
                Bingo_Log::warning("get user blacklist info fail param:". serialize($arrGetInput)."_output:".serialize($arrGetRes));
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback user_blacklist_info get failed.");
                }
                return $arrRet;
            }

            // 过滤已存在的黑名单记录
            foreach ($arrGetRes['data'] as $val) {
                $intTmpUid = intval($val['black_id']);
                unset($arrBlackUidMap[$intTmpUid]);
            }
            Bingo_Log::pushNotice('black_count', count($arrBlackUidMap));
            $arrMultiInsert = array();
            if (!empty($arrBlackUidMap)) {
                foreach ($arrBlackUidMap as $key => $val) {
                    $arrMultiInsert[] = array(
                        $intUserId,
                        intval($key),
                        $intTime,
                    );
                }
                $arrInsertInput = array(
                    'db' => $db,
                    'table' => self::BLACK_LIST_TABLE,
                    'field' => array(
                        'user_id',
                        'black_id',
                        'time',
                    ),
                    'data' => $arrMultiInsert,
                );
                $arrInsertRes = Dl_Userfollow_Db::multiInsert($arrInsertInput);
                if (false === $arrInsertRes || Tieba_Errcode::ERR_SUCCESS !== $arrInsertRes['errno']) {
                    Bingo_Log::warning("insert data into black_list table failed. input:".serialize($arrInsertInput)." output".serialize($arrInsertRes));
                    if (false === $db->rollback()) {
                        Bingo_Log::fatal("rollback user_blacklist_info get failed.");
                    }
                    return $arrRet;
                }
                // debug
                $strLog = sprintf("output for debug iAddBlackList. from_id=%d to_id=%s", $intUserId, serialize($arrBlackUidMap));
                Bingo_Log::warning($strLog);
            }
            if (false === $db->commit()) {
                Bingo_Log::warning("iAddBlackList Get fail param:". serialize($arrInput));
            }
            Bingo_Timer::end('add_black_list');
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrBlackUidMap);
    }
    /**
     * @brief 取消用户黑名单
    **/
    private static function _iCanclBlackList($arrInput){
        // 检验参数
        $arrNeedFields = array('user_id', 'black_user_ids',);
        $bolRes = self::_check_multi_input($arrNeedFields, $arrInput);
        if (false === $bolRes || !is_array($arrInput['black_user_ids'])) {
            Bingo_Log::warning("sunhua input params: ".serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrRet['retry'] = 1;
        $intUserId = intval($arrInput['user_id']);
        $arrBlackUids = array();
        foreach($arrInput['black_user_ids'] as $val) {
            $intBlackUid = intval($val);
            if ($intBlackUid == 0 || $intBlackUid == $intUserId) {
                Bingo_Log::warning("error black_id: ".$intBlackUid. " user_id: ".$intUserId);
                continue;
            }
            $arrBlackUids[$intBlackUid] = 1;
        }
        Bingo_Timer::start('cancel_black_list');
        if (0 == $intUserId || empty($arrBlackUids)) {
            Bingo_Log::warning("no user need to cancel from black list");
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $arrBlackUids = array_keys($arrBlackUids);
        Bingo_Log::pushNotice('black_count', count($arrBlackUids));
        $arrDelInput = array(
            'table' => self::BLACK_LIST_TABLE,
            'cond' => array(
                'user_id=' => $intUserId,
                'black_id' => $arrBlackUids,
            ),
        );
        $arrDelRes = Dl_Userfollow_Db::delete($arrDelInput);
        if (false === $arrDelRes || Tieba_Errcode::ERR_SUCCESS !== $arrDelRes['errno']) {
            Bingo_Log::warning("cancel user black info fail param:". serialize($arrDelInput)." output:".serialize($arrDelRes));
            return $arrRet;
        }
        Bingo_Timer::end('cancel_black_list');
        // debug
        $strLog = sprintf("output for debug iCanclBlackList. from_id=%d to_id=%d", $intUserId, $arrBlackUids);
        Bingo_Log::warning($strLog);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrBlackUids);
    }
    /*
     * @brief: 获取用户关注和被关注数目
     */
    public static function get_users_count_info_by_uids($arrInput = array())
    {
        $output = array(
            'retry' => 0,  // 1需要重试, 0不需要重试
            'data' => array(),
        );
        if (!isset($arrInput['user_ids']) || empty($arrInput['user_ids'])) {
            Bingo_Log::warning("no user is input.");
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $output);
        }
        $arrUserInfo = array();    // 用户信息
        // get from cache
        $arrKeys = array();
        $arrFilterUids = array();
        foreach ($arrInput['user_ids'] as $val) {
            $intUid = intval($val);
            if ($intUid <= 0) {
                continue;
            }
            $arrFilterUids[$intUid] = 1;
            $arrKeys[$intUid] = self::PREFIX_USER_FOLLOW_CONUT_INFO.$intUid;
        }
        $arrKeys = array_values($arrKeys);
        if (empty($arrKeys)) {
            Bingo_Log::warning("no user is input.");
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $output);
        }
        // 初始化cache
        self::_initCache();
        if (is_null(self::$_cache)) {
            $output['retry'] = 1;
            return self::_errRet(Tieba_Errcode::ERR_CACHE_CONN_FAIL, $output);
        }
        $arrCacheInfo = self::$_cache->multipleGet($arrKeys);
        if (false === $arrCacheInfo) {  // 读cache失败，需要重试
            Bingo_Log::warning("get user followed info from cache failed.");
            $output['retry'] = 1;
            return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL, $output);
        }
        $arrMisUids = array();     // 未命中cache的
        foreach($arrFilterUids as $intUid => $intVal) {
            $strKey = self::PREFIX_USER_FOLLOW_CONUT_INFO.$intUid;
            if (isset($arrCacheInfo[$strKey])) {
                $arrUserInfo[$intUid] = unserialize($arrCacheInfo[$strKey]);
            } else {
                $arrMisUids[] = $intUid;
            }
        }
        Bingo_Log::pushNotice('hit_cache', count($arrUserInfo));
        // 全部命中cache，则直接返回
        if (empty($arrMisUids)) {
            $output['data'] = $arrUserInfo;
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $output);
        }
        // 从db中取数据
        $arrGetInput = array(
            'table' => self::USER_COUNT_INFO_TABLE,
            'field' => array('user_id', 'follow_count', 'followed_count'),
            'cond' => array(
                'user_id' => $arrMisUids,
            ),
        );
        $arrGetRes = Dl_Userfollow_Db::select($arrGetInput);
        if (false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']) {
            Bingo_Log::warning("get user followed count info fail param:". serialize($arrGetInput));
            $output['retry'] = 1;
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, $output);
        }
        $intHitDb = 0;
        $arrCacheAddInfo = array();
        foreach ($arrGetRes['data'] as $arrVal) {
            $intHitDb ++;
            $intUid = $arrVal['user_id'];
            $arrUserInfo[$intUid] = $arrVal;
            $strKey = self::PREFIX_USER_FOLLOW_CONUT_INFO.$intUid;
            $strValue = serialize($arrVal);
            $arrCacheAddInfo[] = array(
                'key'    => $strKey,
                'value'  => $strValue,
                'expire' => self::CACHE_EXPIRE_TIME,
            );
            // add cache
            //$arrAddRes = self::$_cache->add($strKey, $strValue, );
            //if (false === $arrAddRes) {
            //    Bingo_Log::warning('add cache failed. key: '.$strKey." value: ".$strValue);
            //}
        }
        if (!empty($arrCacheAddInfo)) {
            $arrAddRes = self::$_cache->multipleAdd($arrCacheAddInfo);
            if (false === $arrAddRes) {
                Bingo_Log::warning('multi add cache failed. input: '.serialize($arrAddRes));
            }
        }
        Bingo_Log::pushNotice('hit_db', $intHitDb);
        //output
        $output['data'] = $arrUserInfo;
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $output);
    }
    /*
     * @brief: 获取用户关注的全部人
     */
    public static function get_user_followslist($arrInput = array())
    {
        $intUid = intval($arrInput['user_id']);
        if ($intUid <= 0) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        // get from cache
        $strKey = self::PREFIX_USER_FOLLOW_LIST.$intUid;
        // 初始化cache
        self::_initCache();
        if (is_null(self::$_cache)) {
            return self::_errRet(Tieba_Errcode::ERR_CACHE_CONN_FAIL);
        }
        $arrCacheInfo = self::$_cache->get($strKey);
        if (false === $arrCacheInfo) {  // 读cache失败，需要重试
            Bingo_Log::warning("get user follow list from cache failed.");
            return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
        }
        if (!is_null($arrCacheInfo)) {
            Bingo_Log::pushNotice('hit_cache', 1);
            $output = unserialize($arrCacheInfo);
            // 添加时间
            $arrUserInfo = array();
            if (isset($output['user_ids'])) {
                $arrUserInfo = $output;
            } else {
                foreach ($output as $key => $value) {
                    $arrUserInfo['user_ids'][] = $value;
                    $arrUserInfo['user_infos'][$value] = array();
                }
            }
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrUserInfo);
        }
        Bingo_Log::pushNotice('hit_cache', 0);
        // 从db中取数据
        $arrGetInput = array(
            'table' => self::USER_FOLLOW_TABLE,
            'field' => array('to_id, time'),
            'cond' => array(
                'from_id=' => $intUid,
            ),
            'append' => array(
                'order by time desc',
            ),
        );
        $arrGetRes = Dl_Userfollow_Db::select($arrGetInput);
        if (false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']) {
            Bingo_Log::warning("get user follow list info fail param:". serialize($arrGetInput));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrUserFollowsInfo = array();
        foreach ($arrGetRes['data'] as $key => $val) {
            $intToUid = intval($val['to_id']);
            $arrUserFollowsInfo['user_ids'][] = $intToUid;
            $arrUserFollowsInfo['user_infos'][$intToUid] = $val;
        }
        // add cache
        $strValue = serialize($arrUserFollowsInfo);

        $arrAddRes = self::$_cache->add($strKey, $strValue, self::CACHE_EXPIRE_TIME);
        if (false === $arrAddRes) {
            Bingo_Log::warning('add cache failed. key: '.$strKey." value: ".$strValue);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrUserFollowsInfo);
    }

    /**
     * 关注人数 与关注列表中的不一致，通过此接口 进行校正
     */
    public static function reportFollowError($arrInput) 
    {
        $intUserId = intval($arrInput['user_id']);
        if ($intUserId <= 0) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrDlInput = array(
            'user_id' => $intUserId,
        );

        $followlist = self::get_user_followslist($arrDlInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $followlist['errno']) {
            Bingo_Log::warning("call get_users_count_info_by_uids failed. input:".serialize($arrDlInput)."_output:".serialize($followlist));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $followCount = count($followlist['user_infos']['user_ids']);

        $db = Dl_Userfollow_Db::getDB();
        if (is_null($db)) {
            Bingo_Log::warning("get db failed");
            return $followlist;
        }

        // 修改关注数目表
        $strSql = sprintf('update %s set follow_count = %d where user_id = %d;', self::USER_COUNT_INFO_TABLE, $followCount, $intUserId);
    
        $bolRes = $db->query($strSql); 
        if (false === $bolRes) {
            Bingo_Log::warning('update user_count_info failed. sql:'.$strSql);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, $followlist);
        }

        // 清除cache
        $arrKeys = array(
            self::PREFIX_USER_FOLLOW_LIST.$intUserId,
            self::PREFIX_USER_FOLLOW_CONUT_INFO.$intUserId,

            self::PREFIX_USER_FOLLOW_LIST_GO.$intUserId,
            self::PREFIX_USER_FOLLOW_CONUT_INFO_GO.$intUserId,
        );
        self::_initCache();
        if (is_null(self::$_cache)) {
            Bingo_Log::warning("init cache fail");
        } else {
            $arrDelRes = self::$_cache->multipleRemove($arrKeys);
            if (false === $arrDelRes) {
                Bingo_Log::warning("call cache del failed. keys:".serialize($arrKeys));
            }
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $followlist);
        


    }


    /*
     * @brief: 获取用户粉丝信息，cache最多存1000条
     * @param user_id
     * @param offset
     * @param limit
     */
    public static function get_user_followedlist($arrInput = array())
    {
        $output = array(
            'total' => 0,
            'user_ids' => array(),
            'user_infos' => array(),
        );
        $intUid = intval($arrInput['user_id']);
        $intOffset = isset($arrInput['offset']) ? intval($arrInput['offset']) : self::DEFAULT_OFFSET;
        $intLimit = isset($arrInput['limit']) ? intval($arrInput['limit']) : self::DEFAULT_LIMIT;
        if ($intUid <= 0 || $intOffset < 0 || $intLimit <= 0) {
            Bingo_Log::warning('input param error. input:'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        // get user_count_info
        $arrGetInput = array(
            'user_ids' => array($intUid),
        );
        $arrCountInfo = self::get_users_count_info_by_uids($arrGetInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrCountInfo['errno']) {
            Bingo_Log::warning("call get_users_count_info_by_uids failed. input:".serialize($arrGetInput)."_output:".serialize($arrCountInfo));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        if (isset($arrCountInfo['user_infos']['data'][$intUid])) {
            $output['total'] = intval($arrCountInfo['user_infos']['data'][$intUid]['followed_count']);
        }
        // 偏移量太大，不用找了
        if ($intOffset >= $output['total']) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $output);
        }
        // get from cache
        self::_initCache();
        if (is_null(self::$_cache)) {
            return self::_errRet(Tieba_Errcode::ERR_CACHE_CONN_FAIL);
        }
        $strKey = self::PREFIX_USER_FOLLOWED_LIST.$intUid;
        $arrCacheInfo = self::$_cache->get($strKey);
        if (false === $arrCacheInfo) {  // 读cache失败，需要重试
            Bingo_Log::warning("get user followed list from cache failed.");
            // return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
        }
        // 命中cache
        $intHitCache = 0;
        $intHitDb = 0;
        if (!is_null($arrCacheInfo)) {
            $arrCacheInfo = unserialize($arrCacheInfo);
            $intCacheCount = isset($arrCacheInfo['user_ids']) ? count($arrCacheInfo['user_ids']) : count($arrCacheInfo);
            if ($intOffset + $intLimit <= $intCacheCount) { // cache满足需求
                for ($i = 0; $i < $intLimit; $i++) {
                    $index = $intOffset + $i;
                    // 兼容旧cache
                    if (isset($arrCacheInfo['user_ids'])) {
                        $intTmpUid = intval($arrCacheInfo['user_ids'][$index]);
                        $output['user_ids'][] = $intTmpUid;
                        $output['user_infos'][$intTmpUid] = $arrCacheInfo['user_infos'][$intTmpUid];
                    } else {
                        $intTmpUid = intval($arrCacheInfo[$index]);
                        $output['user_ids'][] = $intTmpUid;
                        $output['user_infos'][$intTmpUid] = array();
                    }
                    $intHitCache ++;
                }
            } else {
                // 从cache中取一部分
                for (;$intOffset < $intCacheCount; $intOffset++,$intLimit--) {
                    if (isset($arrCacheInfo['user_ids'])) {
                        $intTmpUid = intval($arrCacheInfo['user_ids'][$intOffset]);
                        $output['user_ids'][] = $intTmpUid;
                        $output['user_infos'][$intTmpUid] = $arrCacheInfo['user_infos'][$intTmpUid];
                    } else {
                        $intTmpUid = intval($arrCacheInfo[$intOffset]);
                        $output['user_ids'][] = $intTmpUid;
                        $output['user_infos'][$intTmpUid] = array();
                    }
                    $intHitCache ++;
                }
                if ($intCacheCount == self::MAX_USER_FOLLOWED_CACHE_COUNT) { // cache里面有超过1000个元素，说明总粉丝数大于1000，需要读ddbs
                    // 从db中取另外一部分
                    $strAppend = "order by time desc limit ".$intOffset. ", ".$intLimit;
                    $arrGetInput = array(
                        'table' => self::USER_FOLLOWED_TABLE,
                        'field' => array('from_id', 'time'),
                        'cond' => array(
                            'to_id=' => $intUid,
                        ),
                        'append' => array(
                            $strAppend,
                        ),
                    );
                    $arrGetRes = Dl_Userfollow_Db::select($arrGetInput);
                    if (false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']) {
                        Bingo_Log::warning("get user followed list info fail param:". serialize($arrGetInput));
                        return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                    }
                    foreach ($arrGetRes['data'] as $val) {
                        $intTmpUid = intval($val['from_id']);
                        $output['user_ids'][] = $intTmpUid;
                        $output['user_infos'][$intTmpUid] = $val;
                        $intHitDb ++;
                    }
                }
            }
            Bingo_Log::pushNotice('hit_cache', $intHitCache);
            Bingo_Log::pushNotice('hit_db', $intHitDb);

            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $output);
        }

        // 从db中取数据
        if ($intOffset + $intLimit <= self::MAX_USER_FOLLOWED_CACHE_COUNT) { // 需要加cache
            $strAppend = "order by time desc limit 0, ".self::MAX_USER_FOLLOWED_CACHE_COUNT;
            $arrGetInput = array(
                'table' => self::USER_FOLLOWED_TABLE,
                'field' => array('from_id', 'time'),
                'cond' => array(
                    'to_id=' => $intUid,
                ),
                'append' => array(
                    $strAppend,
                ),
            );
            $arrGetRes = Dl_Userfollow_Db::select($arrGetInput);
            if (false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']) {
                Bingo_Log::warning("get user followed list info fail param:". serialize($arrGetInput));
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            // add cache
            $arrAddCacheInfo = array();
            foreach ($arrGetRes['data'] as $val) {
                $intTmpUid = intval($val['from_id']);
                $arrAddCacheInfo['user_ids'][] = $intTmpUid;
                $arrAddCacheInfo['user_infos'][$intTmpUid] = $val;
                $intHitDb ++;
            }
            $strValue = serialize($arrAddCacheInfo);
            $intAddRes = self::$_cache->add($strKey, $strValue, self::CACHE_EXPIRE_TIME);
            if (CACHE_OK !== $intAddRes) {
                Bingo_Log::warning(sprintf('add cache failed. key: %s, value: %s, return: %d', $strKey, $strValue, $intAddRes));
            }
            $intGetMax = count($arrGetRes['data']);
            $intNeedMax = $intOffset + $intLimit;
            $intMinIndex = $intGetMax > $intNeedMax ? $intNeedMax : $intGetMax;
            for ($i = $intOffset; $i < $intMinIndex; $i++) {
                $intTmpUid = intval($arrGetRes['data'][$i]['from_id']);
                $output['user_ids'][] = $intTmpUid;
                $output['user_infos'][$intTmpUid] = $arrGetRes['data'][$i];
            }
        } else {
            $strAppend = "order by time desc limit ".$intOffset.", ".$intLimit;
            $arrGetInput = array(
                'table' => self::USER_FOLLOWED_TABLE,
                'field' => array('from_id', 'time'),
                'cond' => array(
                    'to_id=' => $intUid,
                ),
                'append' => array(
                    $strAppend,
                ),
            );
            $arrGetRes = Dl_Userfollow_Db::select($arrGetInput);
            if (false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']) {
                Bingo_Log::warning("get user followed list info fail param:". serialize($arrGetInput));
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            foreach ($arrGetRes['data'] as $val) {
                $output['data'][] = $val;
                $intTmpUid = intval($val['from_id']);
                $output['user_ids'][] = $intTmpUid;
                $output['user_infos'][$intTmpUid] = $val;
                $intHitDb ++;
            }
        }
        Bingo_Log::pushNotice('hit_cache', $intHitCache);
        Bingo_Log::pushNotice('hit_db', $intHitDb);

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $output);
    }
    /*
     * @brief: 根据user_id判断是否在黑名单
     */
    public static function get_black_info_by_user_id($arrInput = array())
    {
        $output = array();
        if (!isset($arrInput['user_id']) || !isset($arrInput['black_ids']) || empty($arrInput['black_ids'])) {
            Bingo_Log::warning("no user is input.");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUserId = intval($arrInput['user_id']);
        $arrBlackInfo = array();
        foreach ($arrInput['black_ids'] as $val) {
            $intBlackUid = intval($val);
            if ($intBlackUid <= 0) {
                continue;
            }
            $arrBlackInfo[$intBlackUid] = array(
                'black_id' => $intBlackUid,
                'is_black' => 0,
            );
        }
        // 没有，则直接返回
        if (empty($arrBlackInfo)) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $output);
        }
        $arrBlackUids = array_keys($arrBlackInfo);
        // 从db中取数据
        $arrGetInput = array(
            'table' => self::BLACK_LIST_TABLE,
            'field' => array('black_id'),
            'cond' => array(
                'user_id=' => $intUserId,
                'black_id' => $arrBlackUids,
            ),
        );
        $arrGetRes = Dl_Userfollow_Db::select($arrGetInput);
        if (false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']) {
            Bingo_Log::warning("get user black info fail param:". serialize($arrGetInput)." output:".serialize($arrGetRes));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        foreach($arrGetRes['data'] as $val) {
            $intTmpUid = intval($val['black_id']);
            $arrBlackInfo[$intTmpUid]['is_black'] = 1;
        }
        //output
        $output = array_values($arrBlackInfo);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $output);
    }
    /*
     * @brief: 根据user_id获取黑名单列表
     */
    public static function get_blacklist_by_user_id($arrInput = array())
    {
        $output = array(
            'total' => 0,
            'black_infos' => array(),
        );
        $intUserId = intval($arrInput['user_id']);
        $intOffset = isset($arrInput['offset']) ? intval($arrInput['offset']) : self::DEFAULT_OFFSET;
        $intLimit = isset($arrInput['limit']) ? intval($arrInput['limit']) : self::DEFAULT_LIMIT;
        if ($intUserId <= 0 || $intOffset < 0 || $intLimit <= 0) {
            Bingo_Log::warning('input param error. input:'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $db = Dl_Userfollow_Db::getDB();
        if (is_null($db)) {
            Bingo_Log::warning('init db failed');
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $strSql = "select count(user_id) as sum from ".self::BLACK_LIST_TABLE. " where user_id = ".$intUserId.";";
        $arrSqlRes = $db->query($strSql);
        if (false === $arrSqlRes) {
            Bingo_Log::warning('get info from black_list failed. sql:'.$strSql);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $intTotalNum = intval($arrSqlRes[0]['sum']);
        $output['total'] = $intTotalNum;
        if ($intTotalNum > 0 && $intTotalNum > $intOffset) {
            $strSql = "select black_id from ".self::BLACK_LIST_TABLE." where user_id = ". $intUserId. " order by time desc limit ".$intOffset.", ".$intLimit.";";
            $arrSqlRes = $db->query($strSql);
            if (false === $arrSqlRes) {
                Bingo_Log::warning('get info from black_list failed. sql:'.$strSql);
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            foreach ($arrSqlRes as $val) {
                $output['black_infos'][] = intval($val['black_id']);
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $output);
    }
    /*
     * @brief: 关注和被关注信息
     * @param user_id
     * @param request_infos
     */
    public static function get_users_relation_by_uid($arrInput = array())
    {
        $output = array();
        $self = array();
        $intUid = intval($arrInput['user_id']);
        if ($intUid <= 0 || !is_array($arrInput['request_infos']) || empty($arrInput['request_infos'])) {
            Bingo_Log::warning('input param error. input:'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        foreach ($arrInput['request_infos'] as $val) {
            $intTmpUid = intval($val);
            if ($intTmpUid <= 0 || $intTmpUid == $intUid) {
                continue;
            }
            $output[$intTmpUid] = array(
                'user_id' => $intTmpUid,
                'is_follow' => 0,
                'is_followed' => 0,
            );
        }
        if (empty($output)) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $output);
        }
        $arrKeys[] = self::PREFIX_USER_FOLLOW_LIST.$intUid;
        foreach ($output as $key => $val) {
            $arrKeys[] = self::PREFIX_USER_FOLLOW_LIST.$key;
        }
        // 初始化cache
        self::_initCache();
        if (is_null(self::$_cache)) {
            Bingo_Log::warning('init cache failed');
            return self::_errRet(Tieba_Errcode::ERR_CACHE_CONN_FAIL);
        }
        $arrCacheInfo = self::$_cache->multipleGet($arrKeys);
        if (false === $arrCacheInfo) {  // 读cache失败，需要重试
            Bingo_Log::warning("get user follow list from cache failed.");
           // return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
        }
        // 是否关注
        $intHitCache = 0;
        $intHitDb = 0;
        $strKey = self::PREFIX_USER_FOLLOW_LIST.$intUid;
        if (isset($arrCacheInfo[$strKey])) {
            $arrUserFollowTmp = unserialize($arrCacheInfo[$strKey]);
            // 兼容cache
            $arrUserFollow = isset($arrUserFollowTmp['user_ids']) ? $arrUserFollowTmp['user_ids'] : $arrUserFollowTmp;
            $intHitCache ++;
        } else {
            $arrGetInput = array(
                'table' => self::USER_FOLLOW_TABLE,
                'field' => array('to_id', 'time'),
                'cond' => array(
                    'from_id=' => $intUid,
                    'to_id' => array_keys($output),
                ),
            );
            $arrGetRes = Dl_Userfollow_Db::select($arrGetInput);
            if (false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']) {
                Bingo_Log::warning("get user followed list info fail param:". serialize($arrGetInput));
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            $arrUserFollow = array();
            foreach ($arrGetRes['data'] as $val) {
                $arrUserFollow[] = intval($val['to_id']);
            }
            $intHitDb ++; // 每个人记一次
        }
        foreach ($output as $key => $val) {
            if (!empty($arrUserFollow) && in_array($key, $arrUserFollow)) {
                $output[$key]['is_followed'] = 1;
            }
        }
        // 是否被关注
        $arrMisUids = array();
        foreach ($output as $key => $val) {
            $intTmpUid = $key;
            $strKey = self::PREFIX_USER_FOLLOW_LIST.$intTmpUid;
            if (isset($arrCacheInfo[$strKey])) {
                $arrUserFollowTmp = unserialize($arrCacheInfo[$strKey]);
                // 兼容cache
                $arrUserFollow = isset($arrUserFollowTmp['user_ids']) ? $arrUserFollowTmp['user_ids'] : $arrUserFollowTmp;
                if (!empty($arrUserFollow) && in_array($intUid, $arrUserFollow)) {
                    $output[$intTmpUid]['is_follow'] = 1;
                }
                $intHitCache ++;
            } else {
                $arrMisUids[] = $intTmpUid;
                $intHitDb ++;
            }
        }
        // 读db
        if(!empty($arrMisUids)) {
            $arrGetInput = array(
                'table' => self::USER_FOLLOWED_TABLE,
                'field' => array('from_id', 'time'),
                'cond' => array(
                    'to_id=' => $intUid,
                    'from_id' => $arrMisUids,
                ),
            );
            $arrGetRes = Dl_Userfollow_Db::select($arrGetInput);
            if (false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']) {
                Bingo_Log::warning("get user followed list info fail param:". serialize($arrGetInput));
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            foreach ($arrGetRes['data'] as $val) {
                $intTmpUid = intval($val['from_id']);
                $output[$intTmpUid]['is_follow'] = 1;
            }
        }
        // 获取关注和被关注数
        /*$arrUids = array_keys($output);
        $arrUids[] = $intUid;
        $arrCountInfo = self::get_users_count_info_by_uids(array('user_ids' => $arrUids));
        if (Tieba_Errcode::ERR_SUCCESS !== $arrCountInfo['errno']) {
            Bingo_Log::warning("call get_users_count_info_by_uids failed. input:".serialize($arrUids)."_output:".serialize($arrCountInfo));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $self = array(
            'user_id' => $intUid,
            'follow_count' => 0,
            'followed_count' => 0,
        );
        foreach ($arrCountInfo['user_infos']['data'] as $key => $val) {
            $intTmpUid = $val['user_id'];
            if ($intTmpUid != $intUid) {
                $output[$intTmpUid]['follow_count'] = intval($val['follow_count']);
                $output[$intTmpUid]['followed_count'] = intval($val['followed_count']);
            } else {
                $self['follow_count'] = intval($val['follow_count']);
                $self['followed_count'] = intval($val['followed_count']);
            }
        }*/
        Bingo_Log::pushNotice('hit_cache', $intHitCache);
        Bingo_Log::pushNotice('hit_db', $intHitDb);
        $output = array_values($output);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $output, $self);
    }
    /*
     * @brief: 关注信息
     * @param user_id
     * @param request_infos
     */
    public static function get_user_follow_info_by_uid($arrInput = array())
    {
        $output = array();
        $self = array();
        $intUid = intval($arrInput['user_id']);
        if ($intUid <= 0 || !is_array($arrInput['request_infos']) || empty($arrInput['request_infos'])) {
            Bingo_Log::warning('input param error. input:'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        foreach ($arrInput['request_infos'] as $val) {
            $intTmpUid = intval($val);
            if ($intTmpUid <= 0 || $intTmpUid == $intUid) {
                continue;
            }
            $output[$intTmpUid] = array(
                'user_id' => $intTmpUid,
                'is_followed' => 0,
                'follow_count' => 0,
                'followed_count' => 0,
            );
        }
        if (empty($output)) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $output);
        }
        $arrKeys[] = self::PREFIX_USER_FOLLOW_LIST.$intUid;
        foreach ($output as $key => $val) {
            $arrKeys[] = self::PREFIX_USER_FOLLOW_LIST.$key;
        }
        // 初始化cache
        self::_initCache();
        if (is_null(self::$_cache)) {
            Bingo_Log::warning('init cache failed');
            return self::_errRet(Tieba_Errcode::ERR_CACHE_CONN_FAIL);
        }
        $arrCacheInfo = self::$_cache->multipleGet($arrKeys);
        if (false === $arrCacheInfo) {  // 读cache失败，需要重试
            Bingo_Log::warning("get user follow list from cache failed.");
            //return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
        }
        // 是否关注
        $intHitCache = 0;
        $intHitDb = 0;
        $strKey = self::PREFIX_USER_FOLLOW_LIST.$intUid;
        if (isset($arrCacheInfo[$strKey])) {
            $arrUserFollowTmp = unserialize($arrCacheInfo[$strKey]);
            $arrUserFollow = isset($arrUserFollowTmp['user_ids']) ? $arrUserFollowTmp['user_ids'] : $arrUserFollowTmp;
            $intHitCache ++;
        } else {
            $arrUserFollowTmp = self::get_user_followslist(array('user_id' => $intUid));
            if (false === $arrUserFollowTmp || Tieba_Errcode::ERR_SUCCESS !== $arrUserFollowTmp['errno']) {
                Bingo_Log::warning("call get_user_followslist failed. uid:".$intUid." ret:".serialize($arrUserFollowTmp));
                return $arrUserFollowTmp;
            }
            $arrUserFollow = isset($arrUserFollowTmp['user_infos']['user_ids']) ? $arrUserFollowTmp['user_infos']['user_ids'] : array();
        }
        foreach ($output as $key => $val) {
            if (!empty($arrUserFollow) && in_array($key, $arrUserFollow)) {
                $output[$key]['is_followed'] = 1;
            }
        }  
        // 获取关注和被关注数
        $arrUids = array_keys($output);
        $arrUids[] = $intUid;
        $arrCountInfo = self::get_users_count_info_by_uids(array('user_ids' => $arrUids));
        if (Tieba_Errcode::ERR_SUCCESS !== $arrCountInfo['errno']) {
            Bingo_Log::warning("call get_users_count_info_by_uids failed. input:".serialize($arrUids)."_output:".serialize($arrCountInfo));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $self = array(
            'user_id' => $intUid,
            'follow_count' => 0,
            'followed_count' => 0,
        );
        foreach ($arrCountInfo['user_infos']['data'] as $key => $val) {
            $intTmpUid = $val['user_id'];
            if ($intTmpUid != $intUid) {
                if (isset($output[$intTmpUid])) {
                    $output[$intTmpUid]['follow_count'] = intval($val['follow_count']);
                    $output[$intTmpUid]['followed_count'] = intval($val['followed_count']);
                }
            } else {
                $self['follow_count'] = intval($val['follow_count']);
                $self['followed_count'] = intval($val['followed_count']);
            }
        }
        Bingo_Log::pushNotice('hit_cache', $intHitCache);
        Bingo_Log::pushNotice('hit_db', $intHitDb);
        $output = array_values($output);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $output, $self);
    }
    /*
     * @brief: 被关注信息
     * @param user_id
     * @param request_infos
     */
    public static function get_user_followed_info_by_uid($arrInput = array())
    {
        $output = array();
        $self = array();
        $intUid = intval($arrInput['user_id']);
        if ($intUid <= 0 || !is_array($arrInput['request_infos']) || empty($arrInput['request_infos'])) {
            Bingo_Log::warning('input param error. input:'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        foreach ($arrInput['request_infos'] as $val) {
            $intTmpUid = intval($val);
            if ($intTmpUid <= 0 || $intTmpUid == $intUid) {
                continue;
            }
            $output[$intTmpUid] = array(
                'user_id' => $intTmpUid,
                'is_follow' => 0,
                'follow_count' => 0,
                'followed_count' => 0,
            );
        }
        if (empty($output)) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $output);
        }
        foreach ($output as $key => $val) {
            $arrKeys[] = self::PREFIX_USER_FOLLOW_LIST.$key;
        }
        $intHitCache = 0;
        $intHitDb = 0;
        // 初始化cache
        self::_initCache();
        if (is_null(self::$_cache)) {
            Bingo_Log::warning('init cache failed');
            return self::_errRet(Tieba_Errcode::ERR_CACHE_CONN_FAIL);
        }
        $arrCacheInfo = self::$_cache->multipleGet($arrKeys);
        if (false === $arrCacheInfo) {  // 读cache失败，需要重试
            Bingo_Log::warning("get user follow list from cache failed.");
            //return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
        }
        // 是否被关注
        $arrMisUids = array();
        foreach ($output as $key => $val) {
            $intTmpUid = $key;
            $strKey = self::PREFIX_USER_FOLLOW_LIST.$intTmpUid;
            if (isset($arrCacheInfo[$strKey])) {
                $arrUserFollowTmp = unserialize($arrCacheInfo[$strKey]);
                $arrUserFollow = isset($arrUserFollowTmp['user_ids']) ? $arrUserFollowTmp['user_ids'] : $arrUserFollowTmp;
                if (!empty($arrUserFollow) && in_array($intUid, $arrUserFollow)) {
                    $output[$intTmpUid]['is_follow'] = 1;
                }
                $intHitCache ++;
            } else {
                $arrMisUids[] = $intTmpUid;
                $intHitDb ++;
            }
        }
        // 读db
        if(!empty($arrMisUids)) {
            $arrGetInput = array(
                'table' => self::USER_FOLLOWED_TABLE,
                'field' => array('from_id', 'time'),
                'cond' => array(
                    'to_id=' => $intUid,
                    'from_id' => $arrMisUids,
                ),
            );
            $arrGetRes = Dl_Userfollow_Db::select($arrGetInput);
            if (false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']) {
                Bingo_Log::warning("get user followed list info fail param:". serialize($arrGetInput));
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            foreach ($arrGetRes['data'] as $val) {
                $intTmpUid = intval($val['from_id']);
                $output[$intTmpUid]['is_follow'] = 1;
            }
        }
        // 获取关注和被关注数
        $arrUids = array_keys($output);
        $arrUids[] = $intUid;
        $arrCountInfo = self::get_users_count_info_by_uids(array('user_ids' => $arrUids));
        if (Tieba_Errcode::ERR_SUCCESS !== $arrCountInfo['errno']) {
            Bingo_Log::warning("call get_users_count_info_by_uids failed. input:".serialize($arrUids)."_output:".serialize($arrCountInfo));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $self = array(
            'user_id' => $intUid,
            'follow_count' => 0,
            'followed_count' => 0,
        );
        foreach ($arrCountInfo['user_infos']['data'] as $key => $val) {
            $intTmpUid = $val['user_id'];
            if ($intTmpUid != $intUid) {
                $output[$intTmpUid]['follow_count'] = intval($val['follow_count']);
                $output[$intTmpUid]['followed_count'] = intval($val['followed_count']);
            } else {
                $self['follow_count'] = intval($val['follow_count']);
                $self['followed_count'] = intval($val['followed_count']);
            }
        }
        Bingo_Log::pushNotice('hit_cache', $intHitCache);
        Bingo_Log::pushNotice('hit_db', $intHitDb);
        $output = array_values($output);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $output, $self);
    }
    /*
     * @brief: 双向关注信息
     * @param user_id
     * @param request_infos
     */
    public static function get_user_double_follows($arrInput = array())
    {
        $intUid = intval($arrInput['user_id']);
        $intOffset = intval($arrInput['offset']);
        $intLimit = intval($arrInput['limit']);
        if ($intUid <= 0 || $intOffset < 0 || $intLimit < 0) {
            Bingo_Log::warning('input param error. input:'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrFollowsRes = self::get_user_followslist(array('user_id' => $intUid));
        if (false === $arrFollowsRes || Tieba_Errcode::ERR_SUCCESS !== $arrFollowsRes['errno']) {
            Bingo_Log::warning("call get_user_followslist failed. input: user_id = ".$intUid."_output:".serialize($arrFollowsRes));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrFollowsUids = isset($arrFollowsRes['user_infos']['user_ids']) ? $arrFollowsRes['user_infos']['user_ids'] : array();
        // 返回值
        $arrDoubleFollows = array();

        $intHitCache = 0;
        $intHitDb = 0; 
        $intMaxNum = $intOffset + $intLimit;
        
        // 判断有没有被关注
        for ($i = 0; $i < count($arrFollowsUids); $i += 100) { // 每次取100个，查cache
            $arrCheckUids = array();
            for ($j = 0;$i + $j < count($arrFollowsUids) && $j < 100;$j++) {
                $index = $i + $j;
                $intTmpUid = intval($arrFollowsUids[$index]);
                $arrCheckUids[$intTmpUid] = 0;
            }
            $arrMissCacheUids = array(); // 读cache失败
            $arrCacheKeys = array();
            foreach ($arrCheckUids as $key => $val) {
                $intTmpUid = intval($key);
                $arrCacheKeys[] = self::PREFIX_USER_FOLLOW_LIST.$intTmpUid;
            }
            // 读cache
            self::_initCache();
            if (is_null(self::$_cache)) {
                Bingo_Log::warning('init cache failed');
                return self::_errRet(Tieba_Errcode::ERR_CACHE_CONN_FAIL);
            }
            $arrCacheInfo = self::$_cache->multipleGet($arrCacheKeys);
            if (false === $arrCacheInfo) {  // 读cache失败
                Bingo_Log::warning("get user follow list from cache failed.");
                //return self::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
            }
            foreach ($arrCheckUids as $key => $val) {
                $intTmpUid = intval($key);
                $strKey = self::PREFIX_USER_FOLLOW_LIST.$intTmpUid;
                if (isset($arrCacheInfo[$strKey])) {
                    $intHitCache ++;
                    $arrCacheUserInfo = unserialize($arrCacheInfo[$strKey]);

                    $arrUids = isset($arrCacheUserInfo['user_ids']) ? $arrCacheUserInfo['user_ids'] : $arrCacheUserInfo;
                    if (in_array($intUid, $arrUids)) {
                        $arrCheckUids[$key] = 1;
                    }
                } else {
                    $arrMissCacheUids[$intTmpUid] = $intTmpUid;
                    $intHitDb ++;
                }
            }
            if (!empty($arrMissCacheUids)) {
                // 读db
                $arrGetInput = array(
                    'table' => self::USER_FOLLOWED_TABLE,
                    'field' => array('from_id', 'time'),
                    'cond' => array(
                        'from_id' => array_values($arrMissCacheUids),
                        'to_id=' => $intUid,
                    ),
                );
                $arrGetRes = Dl_Userfollow_Db::select($arrGetInput);
                if (false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']) {
                    Bingo_Log::warning("get user followed info fail param:". serialize($arrGetInput));
                    return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                }
                foreach ($arrGetRes['data'] as $val) {
                    $intTmpUid = intval($val['from_id']);
                    $arrCheckUids[$intTmpUid] = 1;
                }
            }
            foreach ($arrCheckUids as $key => $val) {
                $intVal = intval($val);
                $intTmpUid = intval($key);
                if ($intVal == 1) {
                    $arrDoubleFollows[] = $intTmpUid;
                }
            }
        }
        
        Bingo_Log::pushNotice('hit_cache', $intHitCache);
        Bingo_Log::pushNotice('hit_db', $intHitDb);
        $arrOutput = array(
            'total' => count($arrDoubleFollows),
            'user_ids' => array_splice($arrDoubleFollows, $intOffset, $intLimit, true),
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }
    /**
     * @brief 关注用户
    **/
    private static function _iSetFollower($arrInput = array()){
        // 检验参数
        $arrNeedFields = array('follow_uid', 'followed_uids',);
        $bolRes = self::_check_multi_input($arrNeedFields, $arrInput);
        if (false === $bolRes || !is_array($arrInput['followed_uids'])) {
            Bingo_Log::warning("sunhua input params: ".serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrRet['retry'] = 1;
        $intUserId = intval($arrInput['follow_uid']);
        $arrFollowedUidMap = array();
        $intTime = isset($arrInput['now_time']) ? intval($arrInput['now_time']) : time();
        // 过滤被关注user_ids
        foreach($arrInput['followed_uids'] as $val) {
            $intFollowedUid = intval($val);
            if ($intFollowedUid == 0 || $intFollowedUid == $intUserId) {
                Bingo_Log::warning("error user_id: ".$intUserId. " followed_user_id: ".$intFollowedUid);
                continue;
            }
            $arrFollowedUidMap[$intFollowedUid] = 1;
        }
        if (0 == $intUserId || empty($arrFollowedUidMap)) {
            Bingo_Log::warning("no user need to set follower");
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $db = Dl_Userfollow_Db::getDB();
        if (is_null($db)) {
            Bingo_Log::warning("get db failed");
            return $arrRet;
        }
        // 被关注的user_ids
        $arrFollowedUids = array_keys($arrFollowedUidMap);
        // 强制读主库, 获取关注数目信息
        $db->startTransaction();
        $arrGetInput = array(
            'db'    => $db,
            'table' => self::USER_COUNT_INFO_TABLE,
            'field' => array('user_id', 'follow_count', 'followed_count'),
            'cond' => array(
                'user_id=' => $intUserId,
            ),
        );
        $arrGetRes = Dl_Userfollow_Db::select($arrGetInput);
        if (false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']) {
            Bingo_Log::warning("get user followed count info fail param:". serialize($arrGetInput));
            $output['retry'] = 1;
            if (false === $db->rollback()) {
                Bingo_Log::fatal("rollback user_follow_info get failed.");
            }
            return $arrRet;
        }
        $intUserFollowCount = isset($arrGetRes['data'][0]['follow_count']) ? intval($arrGetRes['data'][0]['follow_count']) : 0; // 关注数
        // 关注人数已达上限
        if ($intUserFollowCount >= self::MAX_USER_FOLLOW_COUNT) {
            Bingo_Log::warning("follow count exceed limit.[".$intUserId."][".$intUserFollowCount."]");
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        // 获取关注信息，判断是否已经关注过
        $arrGetInput = array(
            'db' => $db,
            'table' => self::USER_FOLLOW_TABLE,
            'field' => array('to_id'),
            'cond' => array(
                'from_id=' => $intUserId,
                'to_id' => $arrFollowedUids,
            ),
        );
        $arrGetRes = Dl_Userfollow_Db::select($arrGetInput);
        if (false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']) {
            Bingo_Log::warning("get user follow info info fail param:". serialize($arrGetInput));
            $output['retry'] = 1;
            if (false === $db->rollback()) {
                Bingo_Log::fatal("rollback user_follow_info get failed.");
            }
            return $arrRet;
        }
        if (false === $db->commit()) {
            Bingo_Log::warning("iSetFollower Get fail param:". serialize($arrInput));
        }
        foreach ($arrGetRes['data'] as $val) {
            $intTmpUid = intval($val['to_id']);
            unset($arrFollowedUidMap[$intTmpUid]);
        }
        // 需要加关注的user_ids
        $arrSetFollowedUids = array_keys($arrFollowedUidMap);
        $intMaxNum = self::MAX_USER_FOLLOW_COUNT - $intUserFollowCount;
        $arrSetFollowedUids = array_slice($arrSetFollowedUids, 0, $intMaxNum); // 添加后上限不能超过1000
        //Bingo_Log::warning(print_r($arrSetFollowedUids, 1));
        Bingo_Log::pushNotice('follow_count', count($arrSetFollowedUids));
        Bingo_Timer::start('loop_set_user_follow');
        foreach ($arrSetFollowedUids as $intFollowedUid) {
            $db->startTransaction();
            // 添加关注详细信息表
            $strSql = sprintf("insert into %s(`from_id`,`to_id`,`time`) values (%d, %d, %d);", self::USER_FOLLOW_TABLE, $intUserId, $intFollowedUid, $intTime);
            $bolRes = $db->query($strSql);
            $intAffectedRows = intval($db->getAffectedRows()); // 被影响的Rows，做检验用
            if (false === $bolRes || 0 == $intAffectedRows) {
                Bingo_Log::warning('insert user_follow_info failed. sql:'.$strSql);
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback user_follow_info submit failed.");
                }
                return $arrRet;
            }
            // 修改关注数目
            // Bingo_Log::warning(print_r($arrUserCountExist, 1));
            $strSql = "insert into ".self::USER_COUNT_INFO_TABLE."(`user_id`,`follow_count`,`followed_count`) values(".$intUserId.", 1, 0) on duplicate key update follow_count = follow_count + 1;";
            $bolUpdateRes = $db->query($strSql);
            $intAffectedRows = intval($db->getAffectedRows()); // 被影响的Rows，做检验用
            if (false === $bolUpdateRes || 0 == $intAffectedRows) {
                Bingo_Log::warning('update user_count_info failed. sql:'.$strSql);
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback user_count_info submit failed.");
                }
                return $arrRet;
            }
            // 提交
            if (false === $db->commit()) {
                Bingo_Log::warning("iSetFollower Submit fail param:". serialize($arrInput));
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback iSetFollower Submit fail");
                }
                return $arrRet;
            }
            // 清除cache
            $arrKeys = array(
                self::PREFIX_USER_FOLLOW_LIST.$intUserId,
                self::PREFIX_USER_FOLLOW_CONUT_INFO.$intUserId,

                self::PREFIX_USER_FOLLOW_LIST_GO.$intUserId,
                self::PREFIX_USER_FOLLOW_CONUT_INFO_GO.$intUserId,
            );
            self::_initCache();
            if (is_null(self::$_cache)) {
                Bingo_Log::warning("init cache fail");
            } else {
                $arrDelRes = self::$_cache->multipleRemove($arrKeys);
                if (false === $arrDelRes) {
                    Bingo_Log::warning("call cache del failed. keys:".serialize($arrKeys));
                }
            }
            $db->startTransaction();
            // 添加被关注信息表
            $strSql = sprintf("insert into %s(`from_id`,`to_id`,`time`) values (%d, %d, %d);", self::USER_FOLLOWED_TABLE, $intUserId, $intFollowedUid, $intTime);
            $bolRes = $db->query($strSql);
            $intAffectedRows = intval($db->getAffectedRows()); // 被影响的Rows，做检验用
            if (false === $bolRes || 0 == $intAffectedRows) {
                Bingo_Log::warning('insert user_followed_info failed. sql:'.$strSql);
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback user_followed_info submit failed.");
                }
                return $arrRet;
            }
            // 修改被关注数目
            $strSql = "insert into ".self::USER_COUNT_INFO_TABLE."(`user_id`,`follow_count`,`followed_count`) values(".$intFollowedUid.", 0, 1) on duplicate key update followed_count = followed_count + 1;";
            $bolUpdateRes = $db->query($strSql);
            $intAffectedRows = intval($db->getAffectedRows()); // 被影响的Rows，做检验用
            if (false === $bolUpdateRes || 0 == $intAffectedRows) {
                Bingo_Log::warning('update user_count_info failed. sql:'.$strSql);
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback user_count_info submit failed.");
                }
                return $arrRet;
            }
            // 提交
            if (false === $db->commit()) {
                Bingo_Log::warning("iSetFollower Submit fail param:". serialize($arrInput));
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback iSetFollower Submit fail");
                }
                return $arrRet;
            }
            // debug
            $strLog = sprintf("output for debug iSetFollower. from_id=%d to_id=%d", $intUserId, $intFollowedUid);
            Bingo_Log::warning($strLog);
            // 清除cache
            $arrKeys = array(
                self::PREFIX_USER_FOLLOWED_LIST.$intFollowedUid,
                self::PREFIX_USER_FOLLOW_CONUT_INFO.$intFollowedUid,

                self::PREFIX_USER_FOLLOWED_LIST_GO.$intFollowedUid,
                self::PREFIX_USER_FOLLOW_CONUT_INFO_GO.$intFollowedUid,
            );
            self::_initCache();
            if (is_null(self::$_cache)) {
                Bingo_Log::warning("init cache fail");
            } else {
                $arrDelRes = self::$_cache->multipleRemove($arrKeys);
                if (false === $arrDelRes) {
                    Bingo_Log::warning("call cache del failed. keys:".serialize($arrKeys));
                }
            }
        }
        Bingo_Timer::end('loop_set_user_follow');
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrSetFollowedUids);
    }
    /**
     * @brief 取消关注
    **/
    private static function _iCancelFollower($arrInput = array()){
        // 检验参数
        $arrNeedFields = array('follow_uid', 'followed_uids',);
        $bolRes = self::_check_multi_input($arrNeedFields, $arrInput);
        if (false === $bolRes || !is_array($arrInput['followed_uids'])) {
            Bingo_Log::warning("sunhua input params: ".serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrRet['retry'] = 1;
        $intUserId = intval($arrInput['follow_uid']);
        $arrFollowedUids = array();
        foreach($arrInput['followed_uids'] as $val) {
            $intFollowedUid = intval($val);
            if ($intFollowedUid == 0 || $intFollowedUid == $intItiebaId) {
                Bingo_Log::warning("error user_id: ".$intUserId. " followed_user_id: ".$intFollowedUid);
                continue;
            }
            $arrFollowedUids[$intFollowedUid] = 1;
        }
        if (0 == $intUserId || empty($arrFollowedUids)) {
            Bingo_Log::warning("no user need to cancel follower");
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $db = Dl_Userfollow_Db::getDB();
        if (is_null($db)) {
            Bingo_Log::warning("get db failed");
            return $arrRet;
        }
        // 获取关注信息，判断是否已经关注过
        $arrFollowedUids = array_keys($arrFollowedUids);
        // 读主库
        $db->startTransaction();
        $arrGetInput = array(
            'db'    => $db,
            'table' => self::USER_FOLLOW_TABLE,
            'field' => array('to_id'),
            'cond' => array(
                'from_id=' => $intUserId,
                'to_id' => $arrFollowedUids,
            ),
        );
        $arrGetRes = Dl_Userfollow_Db::select($arrGetInput);
        if (false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']) {
            Bingo_Log::warning("get user follow info info fail param:". serialize($arrGetInput));
            if (false === $db->rollback()) {
                Bingo_Log::fatal("rollback user_follow_info get failed.");
            }
            return $arrRet;
        }
        if (false === $db->commit()) {
            Bingo_Log::warning("iCancelFollower Get fail param:". serialize($arrInput));
        }
        $arrFollowInfos = $arrGetRes['data'];
        // 选取需要取消关注的用户id
        $arrExistedUids = array();
        foreach ($arrFollowInfos as $val) {
            $intFollowedUid = intval($val['to_id']);
            $arrExistedUids[] = $intFollowedUid;
        }
        //Bingo_Log::warning(print_r($arrExistedUids,1));
        Bingo_Log::pushNotice('follow_count', count($arrExistedUids));
        Bingo_Timer::start('loop_cancel_user_follow');
        // 取消关注
        foreach($arrExistedUids as $intFollowedUid) {
            $db->startTransaction();
            // 取消关注详细信息表
            $strSql = sprintf('delete from %s where from_id = %d and to_id = %d;', self::USER_FOLLOW_TABLE, $intUserId, $intFollowedUid);
            $bolRes = $db->query($strSql);
            $intAffectedRows = intval($db->getAffectedRows()); // 被影响的Rows，做检验用
            if (false === $bolRes) {
                Bingo_Log::warning('delete user_follow failed. sql:'.$strSql);
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback user_follow submit failed.");
                }
                return $arrRet;
            }
            if ($intAffectedRows == 1) {
                // 修改关注数目表
                $strSql = sprintf('update %s set follow_count = follow_count - 1 where user_id = %d and follow_count > 0;', self::USER_COUNT_INFO_TABLE, $intUserId);
                $bolRes = $db->query($strSql);
                // $intAffectedRows = intval($db->getAffectedRows()); // 被影响的Rows，做检验用
                if (false === $bolRes) {
                    Bingo_Log::warning('update user_count_info failed. sql:'.$strSql);
                    if (false === $db->rollback()) {
                        Bingo_Log::fatal("rollback user_count_info submit failed.");
                    }
                    return $arrRet;
                }
            }
            // 提交
            if (false === $db->commit()) {
                Bingo_Log::warning("iCancelFollower Submit fail param:". serialize($arrInput));
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback iCancelFollower Submit fail");
                }
                return $arrRet;
            }
            // 清除cache
            $arrKeys = array(
                self::PREFIX_USER_FOLLOW_LIST.$intUserId,
                self::PREFIX_USER_FOLLOW_CONUT_INFO.$intUserId,

                self::PREFIX_USER_FOLLOW_LIST_GO.$intUserId,
                self::PREFIX_USER_FOLLOW_CONUT_INFO_GO.$intUserId,
            );
            self::_initCache();
            if (is_null(self::$_cache)) {
                Bingo_Log::warning("init cache fail");
            } else {
                $arrDelRes = self::$_cache->multipleRemove($arrKeys);
                if (false === $arrDelRes) {
                    Bingo_Log::warning("call cache del failed. keys:".serialize($arrKeys));
                }
            }
            $db->startTransaction();
            // 取消被关注详细信息
            $strSql = sprintf('delete from %s where from_id = %d and to_id = %d;', self::USER_FOLLOWED_TABLE, $intUserId, $intFollowedUid);
            $bolRes = $db->query($strSql);
            $intAffectedRows = intval($db->getAffectedRows()); // 被影响的Rows，做检验用
            if (false === $bolRes) {
                Bingo_Log::warning('delete user_followed failed. sql:'.$strSql);
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback user_followed submit failed.");
                }
                return $arrRet;
            }
            if ($intAffectedRows == 1) {
                // 修改被关注数目表
                $strSql = sprintf('update %s set followed_count = followed_count - 1 where user_id = %d and followed_count > 0;', self::USER_COUNT_INFO_TABLE, $intFollowedUid);
                $bolRes = $db->query($strSql);
                //$intAffectedRows = intval($db->getAffectedRows()); // 被影响的Rows，做检验用
                if (false === $bolRes) {
                    Bingo_Log::warning('update user_count_info failed. sql:'.$strSql);
                    if (false === $db->rollback()) {
                        Bingo_Log::fatal("rollback user_count_info submit failed.");
                    }
                    return $arrRet;
                }
            }
            // 提交
            if (false === $db->commit()) {
                Bingo_Log::warning("iCancelFollower Submit fail param:". serialize($arrInput));
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback iCancelFollower Submit fail");
                }
                return $arrRet;
            }
            // 清除cache
            $arrKeys = array(
                self::PREFIX_USER_FOLLOWED_LIST.$intFollowedUid,
                self::PREFIX_USER_FOLLOW_CONUT_INFO.$intFollowedUid,

                self::PREFIX_USER_FOLLOWED_LIST_GO.$intFollowedUid,
                self::PREFIX_USER_FOLLOW_CONUT_INFO_GO.$intFollowedUid,
            );
            self::_initCache();
            if (is_null(self::$_cache)) {
                Bingo_Log::warning("init cache fail");
            } else {
                $arrDelRes = self::$_cache->multipleRemove($arrKeys);
                if (false === $arrDelRes) {
                    Bingo_Log::warning("call cache del failed. keys:".serialize($arrKeys));
                }
            }
            // debug
            $strLog = sprintf("output for debug iCancelFollower. from_id=%d to_id=%d", $intUserId, $intFollowedUid);
            Bingo_Log::warning($strLog);
        }
        Bingo_Timer::end('loop_cancel_user_follow');
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrExistedUids);
    }
    /**
     * @brief nmq提交请求处理入口
    **/
    public static function ct_commit($arrInput){
        $mcpack = file_get_contents('php://input');
        $arrParams = mc_pack_pack2array($mcpack);
		//临时屏蔽用户721860822 by xiaojianxin
		if($arrParams["user_id"] == "721860822"){
			return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
		}
        Bingo_Log::warning(print_r($arrParams, 1));  // log
        $intCmdNo = intval($arrParams['command_no']);
        Bingo_Timer::start('function_ct_commit');
        switch ($intCmdNo) {
            case self::iAddBlackList:
                $arrRet = self::_iAddBlackList($arrParams); // 添加用户黑名单
                break;
            case self::iCanclBlackList:
                $arrRet = self::_iCanclBlackList($arrParams); // 取消用户黑名单
                break;
            case self::iSetFollower:
                $arrRet = self::_iSetFollower($arrParams); // 关注用户
                break;
            case self::iCancelFollower:
                $arrRet = self::_iCancelFollower($arrParams); // 取消关注用户
                break;
			case self::iCancelUserFollowed:
				$arrRet = self::_iCancelUserFollowed($arrParams);
                break;
            case self::followByBdApp:
                $arrRet = self::_followByBdApp($arrParams);
                break;
            default:
                Bingo_Log::warning('other cmd received.['.$intCmdNo.']');
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        Bingo_Timer::end('function_ct_commit');
        if(isset($arrRet['retry']) && 1===$arrRet['retry'])
        {
            //需重试
            header('HTTP/1.1 503 Service Temporarily Unavailable', true, 503);
            $arrRes = false;
        }
        return $arrRet;
    }
    /*
     * @brief 提交相关操作的测试用接口
     */
    public static function iSetFollower($arrInput = array()) {
        return self::_iSetFollower($arrInput);
    }
    public static function iCancelFollower($arrInput = array()) {
        return self::_iCancelFollower($arrInput);
    }
    public static function iAddBlackList($arrInput = array()) {
        return self::_iAddBlackList($arrInput);
    }
    public static function iCanclBlackList($arrInput = array()) {
        return self::_iCanclBlackList($arrInput);
	}
	public static function iCancelUserFollowed($arrInput = array()) {
		return self::_iCancelUserFollowed($arrInput);
	}
    
    
    /**
     * @param
     * @return
     **/
    public static function insertData($arrInput = array()){
        $strSql = trim($arrInput['sql']) ;
        $db = Dl_Userfollow_Db::getDB();
        $bolRes = $db->query($strSql);
        $arrData = array(
            'error' => 0 ,
            'sql' => '' ,
        );
        if (false == $bolRes) {
            $arrData['error'] = 1 ;
            $arrData['sql'] = $strSql ;
        }
        return $arrData ;
    }

	/**
     * @brief 取消粉丝关注
    **/
    private static function _iCancelUserFollowed($arrInput = array()){
        // 检验参数
        $arrNeedFields = array('user_id', 'need_cancel_followed_uids',);
        $bolRes = self::_check_multi_input($arrNeedFields, $arrInput);
        if (false === $bolRes || !is_array($arrInput['need_cancel_followed_uids'])) {
            Bingo_Log::warning("input params: ".serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
  //      $arrRet['retry'] = 1;
        $intUserId = intval($arrInput['user_id']);
        $arrFollowedUidMap = array();
        $intTime = isset($arrInput['now_time']) ? intval($arrInput['now_time']) : time();
        // 过滤需要取消关注的已关注我的user_ids
        foreach($arrInput['need_cancel_followed_uids'] as $val) {
            $intFollowedUid = intval($val);
            if ($intFollowedUid == 0 || $intFollowedUid == $intUserId) {
                Bingo_Log::warning("error user_id: ".$intUserId. " followed_user_id: ".$intFollowedUid);
                continue;
            }
            $arrFollowedUidMap[$intFollowedUid] = 1;
        }
        if (0 == $intUserId || empty($arrFollowedUidMap)) {
            Bingo_Log::warning("no user need to set follower");
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $db = Dl_Userfollow_Db::getDB();
        if (is_null($db)) {
            Bingo_Log::warning("get db failed");
            return $arrRet;
        }
        // 粉丝uids
        $arrFollowedUids = array_keys($arrFollowedUidMap);
        // 强制读主库, 获取关注数目信息
        $db->startTransaction();
        // 获取关注信息，判断是否已经关注过
        $arrGetInput = array(
            'db' => $db,
            'table' => self::USER_FOLLOWED_TABLE,
            'field' => array('from_id'),
            'cond' => array(
                'to_id=' => $intUserId,
                'from_id' => $arrFollowedUids,
            ),
        );
        $arrGetRes = Dl_Userfollow_Db::select($arrGetInput);
        if (false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']) {
            Bingo_Log::warning("get user follow info info fail param:". serialize($arrGetInput));
            $output['retry'] = 1;
            if (false === $db->rollback()) {
                Bingo_Log::fatal("rollback user_follow_info get failed.");
            }
            return $arrRet;
        }
        if (false === $db->commit()) {
            Bingo_Log::warning("iCancelUserFollowed Get fail param:". serialize($arrInput));
        }

		$arrFollowedUidMap = array();
        foreach ($arrGetRes['data'] as $val) {
            $intTmpUid = intval($val['from_id']);
            if ($intTmpUid !== 0) {
                $arrFollowedUidMap[$intTmpUid] = 1;
            }
        }
        // 需要取消关注的user_ids
        $arrCancelUserFollowUids = array_keys($arrFollowedUidMap);
       // $intMaxNum = self::MAX_USER_FOLLOW_COUNT - $intUserFollowCount;
       // $arrSetFollowedUids = array_slice($arrSetFollowedUids, 0, $intMaxNum); // 添加后上限不能超过1000
        //Bingo_Log::warning(print_r($arrSetFollowedUids, 1));
        Bingo_Log::pushNotice('cancel_follow_count', count($arrCancelUserFollowUids));
        Bingo_Timer::start('loop_cancel_user_followed');
        foreach ($arrCancelUserFollowUids as $intFollowedUid) {
            $db->startTransaction();
            // 删除关注详细信息表
            //$strSql = sprintf("delete from %s(`from_id`,`to_id`,`time`) values (%d, %d, %d);", self::USER_FOLLOW_TABLE, $intUserId, $intFollowedUid, $intTime);
            $strSql = sprintf("delete from %s where from_id=%d and to_id=%d;", self::USER_FOLLOW_TABLE, $intFollowedUid, $intUserId);
            $bolRes = $db->query($strSql);
            $intAffectedRows = intval($db->getAffectedRows()); // 被影响的Rows，做检验用
            if (false === $bolRes || 0 == $intAffectedRows) {
                Bingo_Log::warning('delete user_follow_info failed. sql:'.$strSql);
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback user_follow_info submit failed.");
                }
                return $arrRet;
            }
            // 修改被关注数目
            // Bingo_Log::warning(print_r($arrUserCountExist, 1));
            $strSql = "update ".self::USER_COUNT_INFO_TABLE." set followed_count = followed_count - 1 where user_id=".$intUserId.";";
            $bolUpdateRes = $db->query($strSql);
            $intAffectedRows = intval($db->getAffectedRows()); // 被影响的Rows，做检验用
            if (false === $bolUpdateRes || 0 == $intAffectedRows) {
                Bingo_Log::warning('update user_count_info failed. sql:'.$strSql);
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback user_count_info submit failed.");
                }
                return $arrRet;
            }
            // 提交
            if (false === $db->commit()) {
                Bingo_Log::warning("iSetFollower Submit fail param:". serialize($arrInput));
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback iSetFollower Submit fail");
                }
                return $arrRet;
            }
            // 清除cache
            $arrKeys = array(
                self::PREFIX_USER_FOLLOW_LIST.$intFollowedUid,
                self::PREFIX_USER_FOLLOW_CONUT_INFO.$intFollowedUid,

                self::PREFIX_USER_FOLLOW_LIST_GO.$intFollowedUid,
                self::PREFIX_USER_FOLLOW_CONUT_INFO_GO.$intFollowedUid,
            );
            self::_initCache();
            if (is_null(self::$_cache)) {
                Bingo_Log::warning("init cache fail");
            } else {
                $arrDelRes = self::$_cache->multipleRemove($arrKeys);
                if (false === $arrDelRes) {
                    Bingo_Log::warning("call cache del failed. keys:".serialize($arrKeys));
                }
            }
            $db->startTransaction();
            // 修改被关注信息表
            $strSql = sprintf("delete from  %s where to_id=%d and from_id=%d", self::USER_FOLLOWED_TABLE, $intUserId, $intFollowedUid);
            $bolRes = $db->query($strSql);
            $intAffectedRows = intval($db->getAffectedRows()); // 被影响的Rows，做检验用
            if (false === $bolRes || 0 == $intAffectedRows) {
                Bingo_Log::warning('insert user_followed_info failed. sql:'.$strSql);
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback user_followed_info submit failed.");
                }
                return $arrRet;
            }
            // 修改被关注数目
            $strSql = "update ".self::USER_COUNT_INFO_TABLE." set follow_count = follow_count - 1 where user_id=".$intFollowedUid;
            $bolUpdateRes = $db->query($strSql);
            $intAffectedRows = intval($db->getAffectedRows()); // 被影响的Rows，做检验用
            if (false === $bolUpdateRes || 0 == $intAffectedRows) {
                Bingo_Log::warning('update user_count_info failed. sql:'.$strSql);
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback user_count_info submit failed.");
                }
                return $arrRet;
            }
            // 提交
            if (false === $db->commit()) {
                Bingo_Log::warning("iSetFollower Submit fail param:". serialize($arrInput));
                if (false === $db->rollback()) {
                    Bingo_Log::fatal("rollback iSetFollower Submit fail");
                }
                return $arrRet;
            }
            // debug
            $strLog = sprintf("output for debug cancelUserFollowed. from_id=%d to_id=%d", $intFollowedUid, $intUserId);
            Bingo_Log::warning($strLog);
            // 清除cache
            $arrKeys = array(
                self::PREFIX_USER_FOLLOWED_LIST.$intUserId,
                self::PREFIX_USER_FOLLOW_CONUT_INFO.$intUserId,

                self::PREFIX_USER_FOLLOWED_LIST_GO.$intUserId,
                self::PREFIX_USER_FOLLOW_CONUT_INFO_GO.$intUserId,
            );
            self::_initCache();
            if (is_null(self::$_cache)) {
                Bingo_Log::warning("init cache fail");
            } else {
                $arrDelRes = self::$_cache->multipleRemove($arrKeys);
                if (false === $arrDelRes) {
                    Bingo_Log::warning("call cache del failed. keys:".serialize($arrKeys));
                }
            }
        }
        Bingo_Timer::end('loop_cancel_user_followed');
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrCancelUserFollowUids);
    }


    /**
     * 批量导入关注关系新增接口
     * @param $arrInput
     * @return array|bool
     */
    public static function processFollowUserBatch($arrInput) {
        //input params.
        if (!isset($arrInput['follow_uid']) || !isset($arrInput['followed_uids']) || intval($arrInput['follow_uid']) < 1
            || !is_array($arrInput['followed_uids']) || !in_array($arrInput['cmd'], array(self::iSetFollower, self::iCancelFollower))) {
            $error = Tieba_Errcode::ERR_PARAM_ERROR;
            $arrOutput = array(
                'errno' => $error,
                'errmsg' => Tieba_Error::getErrmsg($error),
            );
            Bingo_Log::warning("param error:" . json_encode($arrInput));
            return $arrOutput;
        }
        Bingo_Log::pushNotice("input", json_encode($arrInput));
        if (self::iCancelFollower == $arrInput['cmd']) {
            $ret = self::_iCancelFollower($arrInput);
        } else {
            $ret = self::_iSetFollower($arrInput);
        }
        Bingo_Log::pushNotice('ret:' . json_encode($ret));
        return $ret;
    }

    /**
     * 处理来自手百的关注请求
     * @param $arrInput
     * @return array
     */
    private static function _followByBdApp($arrInput) {
        //input params.
        if (!isset($arrInput['follow_uid']) || !isset($arrInput['followed_uid']) || intval($arrInput['follow_uid']) < 1
            || intval($arrInput['followed_uid']) < 1|| !in_array($arrInput['follow_cmd'], array(self::iSetFollower, self::iCancelFollower))) {
            $error = Tieba_Errcode::ERR_PARAM_ERROR;
            $arrOutput = array(
                'errno' => $error,
                'errmsg' => Tieba_Error::getErrmsg($error),
            );
            Bingo_Log::warning("param error:" . json_encode($arrInput));
            return $arrOutput;
        }
        Bingo_Log::pushNotice("input", json_encode($arrInput));
        //参数变更
        $arrInput['followed_uids'] = array($arrInput['followed_uid']);
        unset($arrInput['followed_uid']);

        if (self::iCancelFollower == $arrInput['follow_cmd']) {
            $ret = self::_iCancelFollower($arrInput);
        } else {
            $ret = self::_iSetFollower($arrInput);
        }
        Bingo_Log::pushNotice('ret:' . json_encode($ret));
        return $ret;

    }

}
