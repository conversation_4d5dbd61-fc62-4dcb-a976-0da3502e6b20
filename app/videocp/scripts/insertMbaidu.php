<?php
/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file insertMbaidu.php
 * <AUTHOR>
 * @date 2017/10/18 
 * @brief 
 *  
 **/
define('NAME_SCRIPT' , 'insertMbaidu');
require_once dirname(__FILE__) . "/BaseScript.php";

class insertMbaidu extends BaseScript{
	const NEED_RETRY_TIMES = 3;
    
    private static $_retData = array();
    private static $_currDay;
	private static $_currVideoSiteId = 0;
	private static $_currRedisKey;
	private static $_searchTimeKey = "videocp_seartime";
	private static $strMailContent = '';
	//public static $STR_DEFAULT_TITLE = "我发了一个视频贴,快来围观吧";
	public static $arrCp = array();
	private static $previewToMail = array(
        '<EMAIL>',
    );

	/**
     * [_buildAppend description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
	public static function execute(){
		$arrInput = array(
			'conds' => array(
				'status' => 1,
			),
		);
        self::_getMbaiduData();
    }    
    /**
     * @brief 拼邮件的格式
     * @param $strContent
     * @return bool
     */
    private static function _createMailContent(){
        self::$strMailContent = "=============视频拉取数据 begin\n";
        foreach(self::$arrCp as $key => $value){
            if(isset($value['is_fetch_url_succ']) && $value['is_fetch_url_succ'] == 0){
                self::$strMailContent .= $key . "拉取视频数据失败!\n";
            }else{
                self::$strMailContent .= $key . "拉取了" . intval($value['fetchNum']) . "条视频数据. 成功入库了" . intval($value['succNum']) . "条视频数据" . "\n"; 
            }
        }
        self::$strMailContent .= "视频拉取数据 end===============\n";
    }

    /**
     * @param string $title
     * @param $strContent
     * @return bool
     */
    public static function sendMail($title = "") {
		self::_createMailContent();
        $boundary = '----=' . uniqid();

        $to = implode(',', self::$previewToMail);
        $content = self::$strMailContent;
        $title = date('Y-m-d-H') . "视频抓取数据及入库情况";

        $headers = "MIME-Version: 1.0\r\n";
        $headers .= "Content-Transfer-Encoding: 8bit\r\n";
        $headers .= "From: <EMAIL>\r\n";
        //$headers .= "Content-type: multipart/mixed; boundary=\"$boundary\""  . "\r\n";
        $headers .= 'Content-type: text/plain; charset=utf-8' . "\r\n";
        //echo $to;
        $subject = $title;
        $subject = "=?UTF-8?B?" . base64_encode($subject) . "?=";

        $ret = mail($to, $subject, $content, $headers);
        return $ret;
    }
    /**
     * getMbaiduData
     * @param [type] $arrMatchMap     [description]
     * @return
     */
    private static function _getMbaiduData(){
        $url = 'http://cp01-game-dudai-0511.cp01:8086/test';
        $arrOutput = file_get_contents($url);
        
        $arrOutput = json_decode($arrOutput,true);
        if(!empty($arrOutput['data'])){
            foreach($arrOutput['data'] as $item){
                $single['video_url'] = $item['video_url'];
                $single['title'] = self::decodeUnicode($item['title']);
                $single['video_duration'] = $item['videoDur'];
                $single['play_count'] = $item['playCount'];
                $single['commented'] = $item['commitCount'];
                $single['video_cover_url'] = $item['video_cover_url'];
                $single['video_type'] = 206;
                $single['video_id'] = md5($item['video_url']);
                $single['video_site_id'] =  $single['video_type'] -100;
                $single['top_category'] = '搞笑';
                $single['tags'] = "搞笑"; 
                $single['video_from'] = "手机百度";
                $single['video_format'] = 'mp4';
                $single['video_width'] = 660;
                $single['video_height'] = 370;
                $single['video_cover_width'] = 660;
                $single['video_cover_height'] = 370;
                self::_setVideoInfo($single);
            }
        }

    }
    /**
     * getMbaiduData
     * @param [type] $arrMatchMap     [description]
     * @return
     */

    public function decodeUnicode($str) {
        return preg_replace_callback('/\\\\u([0-9a-f]{4})/i',
            create_function(
            '$matches',
            'return mb_convert_encoding(pack("H*", $matches[1]), "UTF-8", "UCS-2BE");'
        ),
        $str);
    }
	/**
     * 根据把数据插入video_info表中
     * @param [type] $arrMatchMap     [description]
     * @return 
     */
	private static function _setVideoInfo($arrInput){
        if(!isset($arrInput['pubDate'])||$arrInput['pubDate'] > time()){
            $arrInput['pubDate'] = time();
        }
		$arrVideoInfo = array(
			'video_type' => intval($arrInput['video_type']),
			'video_url' => strval($arrInput['video_url']),
            'tb_video_url' => '',
			'video_format' => strval($arrInput['video_format']),
			'video_width' => intval($arrInput['video_width']),
			'video_height' => intval($arrInput['video_height']),
			'video_id' => strval($arrInput['video_id']),
			'video_duration' => intval($arrInput['video_duration']),
			'video_size' => intval($arrInput['video_size']),
			'pubdate' => intval($arrInput['pubDate']),
			'create_time' => time(),
			'title' => isset($arrInput['title']) ? strval($arrInput['title']) : '',
			'video_cover_url' => isset($arrInput['video_cover_url']) ? strval($arrInput['video_cover_url']) : '',
            'tb_video_cover_url' => '',
			'video_cover_width' => isset($arrInput['video_cover_url']) ? intval($arrInput['video_cover_width']) : 0,
			'video_cover_height' => isset($arrInput['video_cover_url']) ? intval($arrInput['video_cover_height']) : 0,
			'top_category' => isset($arrInput['top_category']) ? strval($arrInput['top_category']) : '',
			'sub_category' => isset($arrInput['sub_category']) ? strval($arrInput['sub_category']) : '',
			'tags' => isset($arrInput['tags']) ? strval($arrInput['tags']) : '',
			'play_count' => isset($arrInput['play_count']) ? intval($arrInput['play_count']) : 0,
			'description' => isset($arrInput['description']) ? strval($arrInput['description']) : '',
			'fav_count' => isset($arrInput['fav_count']) ? intval($arrInput['fav_count']) : 0,
			'like_count' => isset($arrInput['liked']) ? intval($arrInput['liked']) : 0,
			'commented_count' => isset($arrInput['commented']) ? intval($arrInput['commented']) : 0,
			'share_count' => isset($arrInput['share']) ? intval($arrInput['share']) : 0,
			'passname' => isset($arrInput['passname']) ? intval($arrInput['passname']) : 0,
			'video_from' => strval($arrInput['video_from']),
			'video_site_id' => intval($arrInput['video_site_id']),
			'thread_id' => 0,
            'threaddate' => 0,
			'status' => 0,
		); 
		if(strlen(trim($arrVideoInfo['title'])) == 0){
			$arrVideoInfo['title'] = self::$STR_DEFAULT_TITLE;
		}
		$arrDBInput = array(
			'field' => $arrVideoInfo,
            'options' => 'ignore ',
		);
		$arrOutput = Tieba_Service::call('videocp', 'setVideoInfo', $arrDBInput, null, null, 'post', 'php', 'utf-8');
        if($arrOutput === false || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning("insert into video_info fail. input: " . serialize($arrDBInput) . "output:" . serialize($arrOutput));
			return false;
		}
		Bingo_Log::notice("insert into video_info succ and video_id = " . $arrVideoInfo['video_id'] . "and video_site_id = " . $arrVideoInfo['video_site_id']);
		return true;
		//var_dump($strSql);
		//self::_queryDBHard($strSql); 
	} 
}

Bingo_Timer::start('total');
insertMbaidu::execute();
Bingo_Timer::end('total');
echo 'total cost: ' . Bingo_Timer::calculate('total') . ' us', PHP_EOL;
echo 'memory usage: ' . memory_get_usage() . ' bytes', PHP_EOL;
echo 'memory peak usage: ' . memory_get_peak_usage() . ' bytes', PHP_EOL;


/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
