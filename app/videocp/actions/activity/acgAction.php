<?php 
/**
 * <AUTHOR>
 * @date 20170626
 */
class acgAction extends Util_BaseNoLogin{
	const ACT_ID = 17;
	const STATUS_ONLINE = 1;
	const TYPE_FIXED = 1;
	const TYPE_WEIGHT = 2;
	const TYPE_NORMAL = 3;
	const MAX_RESULT_NUM = 20;
	private static $_arrThreadSetType = array(
		self::TYPE_FIXED,
		self::TYPE_WEIGHT,
	);
	const CACHE_KEY = 'video_act_acg';
	const CACHE_LIFE_TIME = 300;
	/**
	 * @param
	 * @return [type] [description]
	 */
	public function _execute(){

		$intPn = intval(Bingo_Http_Request::getNoXssSafe('pn', 1));
		$intRn = intval(Bingo_Http_Request::getNoXssSafe('rn', 20));
		$intUid = intval(Tieba_Session_Socket::getLoginUid());
		$strCacheKey = self::CACHE_KEY . self::ACT_ID;
		$strCacheOut = Util_Memcached::getCache($strCacheKey);
		if($strCacheOut){
			Bingo_Log::pushNotice('hit_cache', 1);
			$arrList = unserialize($strCacheOut);
		}else{
			Bingo_Log::pushNotice('hit_cache', 0);
			$arrInput = array(
				'activity_id' => self::ACT_ID,
				'thread_set_type' => self::$_arrThreadSetType,
				'orderby' => array(
					'field' => 'play_count',
					'sort'  => 'DESC',
				),
				'need_has_more' => 1,
				'pn' => $intPn,
				'rn' => $intRn,
			);
			$arrOutput = Tieba_Service::call('videocp', 'getSquareVideoList', $arrInput);
			if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
				Bingo_Log::warning(sprintf("call video::getSquareVideoList failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
				return $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL));
			}
			$arrList = $arrOutput['data']['list'];
			$intHasMore = $arrOutput['data']['has_more'];
			if($intPn * $intRn >= self::MAX_RESULT_NUM){
				$intHasMore = 0;
			}
			$arrList = self::joinThread($arrList, $intUid);
			$strCacheOut = serialize($arrList);
			$ret = Util_Memcached::addCache($strCacheKey, $strCacheOut, self::CACHE_LIFE_TIME);
			if($ret){
				Bingo_Log::pushNotice('add_cache',1);
			}
		}
		$arrList = self::joinAgree($arrList, $intUid);
		$arrRet = array(
			'list' => $arrList,
			'page' => array(
				'has_more' => $intHasMore,
				'pn' => $intPn,
			),
		);
		return $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_SUCCESS), $arrRet);
		
	}
	/**
	 * [joinMore description]
	 * @param  [type] $arrList [description]
	 * @return [type]          [description]
	 */
	private static function joinThread($arrList, $intUid = 0){
		if(empty($arrList)){
			return array();
		}

		$arrTids = array();
		foreach($arrList as $k => $item){
			$intThreadId = (int)$item['thread_id'];
			$arrTids []= $intThreadId;
		}
		$arrTids = array_unique($arrTids);
		$arrInput = array(
			"thread_ids" => $arrTids,
		    "need_abstract" => 0,
		    "forum_id" => 0,
		    "need_photo_pic" => 0,
		    "need_user_data" => 1,
		    "icon_size" => 0,
		    "need_forum_name" => 1, //是否获取吧名
		    "call_from" => "pc_frs" //上游模块名
		);
		$arrOutput = Tieba_Service::call('post', 'mgetThread', $arrInput, null, null, 'post', 'php', 'utf-8');
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
			Bingo_Log::warning(sprintf("call post::mgetThread failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
			return $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL));
			
			//return array();
		}
		$arrThreadList = $arrOutput['output']['thread_list'];
		$arrUserMap = $arrOutput['output']['thread_user_list'];
		$arrNewList = array();
		foreach($arrThreadList as $k => $thread){
			$intUid = (int)$thread['user_id'];
			$intPid = (int)$thread['first_post_id'];
			$intTid = (int)$thread['thread_id'];
			$user = $arrUserMap[$intUid];
			$thread['author'] = array(
				'user_id'   => $user['user_id'],
				'user_name' => $user['user_name'],
				'user_nickname' => $user['user_nickname'],
				'portrait'  => 'https://gss0.bdstatic.com/6LZ1dD3d1sgCo2Kml5_Y_D3/sys/portrait/item/'.Tieba_Ucrypt::encode($user['user_id'], $user['user_name']),
			);
			//$intThumbPicId = $thread['video_info']['thumbnail_picid'];
			$strThumbPic = $thread['video_info']['thumbnail_url'];
			$arrThumbPic = Util_Image::getThumb($strThumbPic);
			if($arrThumbPic[0]&&''!==$arrThumbPic[0]){
				$thread['video_info']['thumbnail_url'] = $arrThumbPic[0];

			}
			$arrNewList []= $thread;
		}
		
		return $arrNewList;
	}
	/**
	 * [joinMore description]
	 * @param  [type] $arrList [description]
	 * @return [type]          [description]
	 */
	private static function joinAgree($arrList, $intUid = 0){
		if(empty($arrList)){
			return $arrList;
		}

		$arrAgreeInputs = array();
		foreach($arrList as $k => $item){
			$intThreadId = (int)$item['thread_id'];
			//$arrTids []= $intThreadId;
			$arrAgreeInputs []= array(
				'thread_id' => $intThreadId,
				'user_id' => $intUid,
			);
		}
		//$arrTids = array_unique($arrTids);
		if(0 !== $intUid){
			$arrTmap = self::getAgreeInfo($arrAgreeInputs);
		}
		if(!$arrTmap){
			return $arrList;
		}
		
		foreach($arrList as $k => $thread){
			$intUid = (int)$thread['user_id'];
			$intPid = (int)$thread['first_post_id'];
			$intTid = (int)$thread['thread_id'];
			if(isset($arrTmap[$intTid])&&$arrTmap[$intTid]){
				$thread['user_has_agreed'] = 1;
			}else{
				$thread['user_has_agreed'] = 0;
			}
			$arrNewList []= $thread;
		}
		
		return $arrNewList;
	}
	/**
	 * [getAgreeInfo description]
	 * @param  [type] $arrInputs [description]
	 * @return [type]            [description]
	 */
	private static function getAgreeInfo($arrInputs){
		$arrMap = array();
		$objRalMulti = new Tieba_Multi();
		foreach($arrInputs as $k => $arrInput){
			$arrMultiInput = array(
                'serviceName' => 'agree',
                'method'      => 'getAgreeByUserIdAndThreadId',
                'input'       => $arrInput,
                'ie'          => 'gbk',//'utf8'
            );
            $objRalMulti->register('acg_'.$k, new Tieba_Service('agree'), $arrMultiInput);

		}
		$arrMultiOutput = $objRalMulti -> call();
		foreach($arrInputs as $k => $arrInput){
			$intTid = (int)$arrInput['thread_id'];
			$arrOutput = $arrMultiOutput['acg_'.$k];
			if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
				Bingo_Log::warning(sprintf("call agree::getAgreeByUserIdAndThreadId failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
				$arrMap[$intTid] = 0;
				continue;
			}
			if(isset($arrOutput['data']['list'][0])){
				$arrMap[$intTid] = 1;
			}else{
				$arrMap[$intTid] = 0;
			}
		}
		return $arrMap;
	}
}
