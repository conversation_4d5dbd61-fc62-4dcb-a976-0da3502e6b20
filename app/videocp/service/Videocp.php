<?php

/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

define('BINGO_ENCODE_LANG','UTF-8');
define("MODULE","Videocp_service");
class Service_Videocp{
    CONST SERVICE_NAME = "Service_Videocp";

    public static $service_ie = 'utf-8';
    protected static $_conf = null;
    
    /**
     * @param:
     * @return:
     **/
    public static function getIE($methodName){
        return self::$service_ie;
    }
    
    /**
     * @brief init
     * @param:
     * @return: true if success. false if fail.
    **/
    private static function _init(){
        //add init code here. init will be called at every public function beginning.
        //not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
        //init should be recalled for many times.

        if(self::$_conf == null){
            self::$_conf = Bd_Conf::getConf("/app/videocp/service_videocp");
            if(self::$_conf == false){
                Bingo_Log::warning("init get conf fail.");
                return false;
            }
        }
        return true;
    }
    
    /**
     * @param:
     * @return:
     **/
    private static function _errRet($errno){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }
    
    /**
     * @param:
     * @return:
     **/
    public static function preCall($arrInput){
        // pre-call hook
    }
    
    /**
     * @param:
     * @return:
     **/
    public static function postCall($arrInput){
        // post-call hook
    }
    
    /**
     * @brief: 统一接入函数
     * @param:
     * @return:
     **/
    public static function call($methodName, $arrInput){
        $serviceName = Libs_Router::getServiceName($methodName);
        
        if( empty($serviceName) ){
            Bingo_Log::warning(__FUNCTION__.' method not found. [method='.$methodName.']');
            return self::_errRet(Tieba_Errcode::ERR_METHOD_NOT_FOUND);
        }
        
        $timer_key = 'service_'.$serviceName.'_'.$methodName;
        Bingo_Timer::start($timer_key);
        $res = call_user_func_array(array($serviceName, $methodName), array($arrInput));
        Bingo_Timer::end($timer_key);
        
        return $res;
    }
    /**
     * @brief: redis get set
     * @param:
     * @return:
     **/
    public static function queryRedis($arrInput){
        if($arrInput['type'] === 'hget'){
            $arrRedisInput = array(
                'key' => $arrInput['key'],
                'field' => $arrInput['field'],
            );
            $arrRedisOut = Util_Redis::HGET($arrRedisInput);
        }else if($arrInput['type'] === 'hset'){
            $arrRedisInput = array(
                'key' => $arrInput['key'],
                'field' => $arrInput['field'],
                'value' => $arrInput['value'],
            );
            $arrRedisOut = Util_Redis::HSET($arrRedisInput);
        }
        return $arrRedisOut;
    }

    /**
     * @brief nmq处理
     * @param
     * @return
     **/
    public static function nmqCallback($arrInput){
        //input params.
        $data = Tieba_Service::getArrayParams($arrInput, 'data');
        
        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.

        //your code here......
        if (!isset($data['command_no'])) {
            Bingo_Log::warning ( "command_no loss." );
            return array ('status' => 0 );
        }
        $intCmd = intval($data['command_no']);
        
        $intTransId = 0;
        if(isset($data ['trans_id'])) {
            $intTransId = intval($data['trans_id']);
        }
        Bingo_Log::pushNotice("command_no",$intCmd);
        Bingo_Log::pushNotice("trans_id",$intTransId);

        $bolHasErr = false;
        $res = self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        switch ($intCmd) {
            case self::NMQ_COMMAND_PUSH_ECOM_ACTION:
                $res = Util_Asyncaction::doAction($data);
                Bingo_Log::pushNotice("errno",$res['errno']);
                break;
            case self::NMQ_COMMAND_POST_THREAD_COMMIT :
                $res = Service_Thread_Thread::addThread($data);
                Bingo_Log::pushNotice('THREAD_COMMIT', 1);
                Bingo_Log::pushNotice('thread_id', $data['thread_id']);
                Bingo_Log::pushNotice('user_id', $data['user_id']);
                Bingo_Log::pushNotice('forum_id', $data['forum_id']);
                break;
            case self::NMQ_COMMAND_POST_DEL_POST:
                $res = Service_Thread_Thread::delThread($data);
                Bingo_Log::pushNotice('POST_DEL', 1);
                Bingo_Log::pushNotice('thread_id', $data['thread_id']);
                Bingo_Log::pushNotice('forum_id', $data['forum_id']);
                Bingo_Log::pushNotice('user_id', $data['user_id']);
                break;
            case self::NMQ_COMMAND_POST_RECOVER_POST:
                $res = Service_Thread_Thread::recoverThread($data);
                Bingo_Log::pushNotice('POST_RECOVER', 1);
                Bingo_Log::pushNotice('thread_id', $data['thread_id']);
                Bingo_Log::pushNotice('forum_id', $data['forum_id']);
                Bingo_Log::pushNotice('user_id', $data['user_id']);
                break;
        }
        if(Tieba_Errcode::ERR_SUCCESS !== $res['errno']){
            Bingo_Log::warning("nmq err. [errno=".$res['errno']."]");
        }
        
        return $res;
    }
}