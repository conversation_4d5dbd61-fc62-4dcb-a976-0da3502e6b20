<?php
/***************************************************************************
 *
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file secondHotStarPageAction.php
 * <AUTHOR>
 * @date: 2016/03/11 14:45
 * @brief:
 *
 */

class secondHotStarPageAction extends Lib_Lego_Action_Base {
    /**
     * @param: null
     * @return: array
     */
    protected function getPageInfo() {
        $arrPageInfo = array();

        $arrTabs = array();

        $arrPageInfo['title'] = array(
            'name'      => '明星热度榜(周榜)',
            'url'       => '',
            'urlNight'  => '',
        );

        $arrPageInfo['tab'] = array();

        return $arrPageInfo;
    }

    /**
     * @param: null
     * @return: array
     */
    protected function getCards() {

        $arrCards = array();
        $arrInputs = $this->_getInputs0();
        $objMulti = new Molib_Tieba_Multi('lego_entertainment_secondhotstar');
        foreach ($arrInputs as $key => $input) {
            $objMulti->register($key, $input);
        }
        $objMulti->call();
        $arrData = $objMulti->getAllResult();

        $arrCards = $this->packSecondStarData($arrData['getHotStarList']['data']);

        $this->setHasMore(0);

        return $arrCards;
    }

    /**
     * @param: null
     * @return: array
     */
    private function _getInputs0() {
        $arrInputs = array();
        if (empty($this->flipId)) {
            $arrInputs['getHotStarList'] = array(
                'serviceName' => 'beyond',
                'method'      => 'getStarList',
                'ie'          => 'utf-8',
                'input'       => array(
                    'list_type'    => 1,
                ),
            );
        }

        return $arrInputs;
    }
    /**
     * @param 封装为lego的二级目录的明星榜卡片
     * @return
     */
    private function packSecondStarData($arrInput, $type = 1) {
        $arrCardsData = array();
        $i = 1;
        $picTrend = 1;
        foreach ($arrInput as $key => $item) {
            if ($item['order_dt'] > 0) {
                $picTrend = 1;
            } else if ($item['order_dt'] == 0) {
                $picTrend = 2;
            } else if ($item['order_dt'] < 0) {
                $picTrend = 3;
            }
            $arrData = array(
                'item_id'     => $item['forum_id'],
                'scheme'     => 'tieba://frs?kw='. urlencode($item['forum_name']),
                'statistics' => 'lego_entertainment_click',
                'rank'       => $i++,
                'title'      => $item['forum_name'],
                'ratio'      => 1,
                'picUrl'     => Lib_Platform_Entertainment_Common::processPicURL($item['avatar'], 3, 1, $this->intScrW, $this->intScrH, $this->intScrDip),
                'picTrendType'    => $picTrend,
            );
            $arrCardsData[] = $this->legoCardCall(7, $arrData);
        }
        return $arrCardsData;
    }

}