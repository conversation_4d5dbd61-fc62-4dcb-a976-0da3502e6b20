<?php
/***************************************************************************
 *
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file PostCard.php
 * <AUTHOR>
 * @date: 2016/03/15 20:25
 * @brief:
 *
 */

class Lib_Lego_Cards_PostCard extends Lib_Lego_Cards_Base {
    /**
     * @param
     * @return
     */
    public static function getCard($arrInput) {
        if (empty($arrInput)) {
            return array();
        }
        $defaultField = array(
            'card_type'         => 12,
            'showCover'         => 0,
            'showLine'          => 0,
        );
        $legoRes = array_merge($defaultField, $arrInput);
        return $legoRes;
    }
}