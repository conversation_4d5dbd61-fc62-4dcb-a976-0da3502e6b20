<?php
/**
 * ===========================================
 * @desc: 
 * @author: fengzhen
 * @date: 2016-7-6
 * ===========================================
 * @version 1.0.0
 * @copyright Copyright (c) www.baidu.com
 */
class Lib_Recommend_Hottopic_Common {
	
	public static function processPicURL($arrMedia,$arrSpec,$intClientType){
		require_once MODULE_ORP_BASE_PATH.'/php/phplib/tb/Molib/Util/ImgCDN/Frs.php';
		if(empty($arrMedia)){
			return array();
		}
		$objFrsImgStrategy = new FrsImgLogic_Stragty_Vitality($intClientType);
		$intMediaCount = count($arrMedia);
		$intCurMediaNum = 0;
		foreach($arrMedia as &$media){
			$intCurMediaNum += 1;
            $strOriginalUrl = $media['big_pic'];
            $intPos = strpos($strOriginalUrl, 'forum');
            //非cdn图片采用旧url
            if ($intPos === false) {
            	$useCdnUrl = false;
            }
			else{
				$useCdnUrl = true;
			}
				
			$strPicSpec = $objFrsImgStrategy->getFrsPicStrategy($intMediaCount, $useCdnUrl, $arrSpec, $intCurMediaNum);
			$strUrl = Molib_Util_ClientImgLogic::generateNewUrl($strOriginalUrl, $useCdnUrl, $strPicSpec);
			if(!empty($strUrl)){
				$media['dynamic_pic'] = $strUrl;
			}
		}
		return $arrMedia;
	}
}