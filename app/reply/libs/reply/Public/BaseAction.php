<?php
abstract class reply_Public_BaseAction extends Bingo_Action
{
	//�����������
	private static $_userCheckFunc = array();

	//action��������
	protected   $_actionConf = array();

	//���������
	private  $_arrNeedParams = array();

	//Ĭ�ϲ���
	private $_arrDefaultParams = array();

	//����У��
	private $_arrCheckParams  = array();
	
	private $_arrUserRole =  array();

	//ģ��
	private $_alt              = false;//���ݸ�ʽ
	private $_strTemplete      = false;//ģ�������
	
	private $autoCommit      = true; //�Ƿ��Զ��ύ:Ĭ���Զ��ύ

	protected $_arrTplVar      = array();
	
	protected $arrInputParam =array();

	//�������
	public  $_intErrorNo  = 0;
	public  $_strErrorMsg = "sucess";

	protected  $_jsonData = array(
			'no'    => 0,
			'data'  => array(),
			'error' => 'sucess',
	);

	/**
	 *
	 * ��ʼ�������ļ�������check�����Ƿ��������������ݣ���Ҫ����������Ч��
	 * @param
	 * @return
	 *     �ɹ���
	 *         true
	 *     ʧ�ܣ�
	 *         false
	 */
	public function initConf(){
		return true;
	}

	/**
	 * ��ʼ��
	 * @see Bingo_Action_Abstract::init()
	 */
	public function init()
	{
		//$this->autoCommit = $autoCommit; //this param is deprecated as none of actions use it
		//��ȡ���ò���
		if(self::initConf() === false ){
			$this->_intErrorNo = conf_ErrorNo::errConfig;
			$this->_strErrorMsg = 'init conf error!';
			Bingo_Log::warning($this->_strErrorMsg);
			return false;
		}
		
		//��ʼ��dalcache
        reply_Public_DalCache::init(array(
        	                               'CacheDataConf'         => conf_CacheConf::$CacheDataConf,
        	                               'CacheStrage'           => conf_CacheConf::$CacheStrage,
        	                               'default_inactive_time' => conf_CacheConf::DEFUALT_INACTIVE_TIME,
        	                               'inactive_type'         => 1,
        	                               'cacheIsOpen'           => false,
        	                            )
        	                         );
        
		//��ʼ��DB����������
        if(dal_reply::init($this->autoCommit) === false ){
            $this->_intErrorNo = conf_ErrorNo::errInitFaile;
            $this->_strErrorMsg = 'db init faile!';
            Bingo_Log::warning($this->_strErrorMsg);
            return false;
        }
		
		//��ʼ��action����Ρ�ģ��Ȳ���
		if(isset($this->_actionConf['needInput'])){
			$this->_arrNeedParams    = $this->_actionConf['needInput'];
		}
		if(isset($this->_actionConf['defaultValue'])){
			$this->_arrDefaultParams = $this->_actionConf['defaultValue'];
		}
		if(isset($this->_actionConf['checkValue'])){
			$this->_arrCheckParams   = $this->_actionConf['checkValue'];
		}
		//��ȡ�������ݸ�ʽ����
		$defaultType = isset($this->_actionConf['tpl']['default'])?$this->_actionConf['tpl']['default']:'';
		$this->_alt = strtolower((trim(Bingo_Http_Request::get('alt', $defaultType))));
		if(isset($this->_actionConf['tpl']['type'][$this->_alt])){
			$this->_strTemplete = $this->_actionConf['tpl']['type'][$this->_alt];
		}else{
			$this->_strTemplete = false;
		}

		if($this->_strTemplete !== false) {
			//��ʼ��view
			Bingo_Page::init(array(
					'baseDir' => MODULE_VIEW_PATH,
					'outputType' => '.',
					'isXssSafe' => true,
			));
			Bingo_Page::setTpl($this->_strTemplete);
			Bingo_Page::setXssSafe(true);
		}

		//��ȡ����
		$ret = $this->_getInputParams();
		if ($ret === false){
			$this->_intErrorNo = conf_ErrorNo::errMissingParams;
			$this->_strErrorMsg = 'params is missing';
			Bingo_Log::warning($this->_strErrorMsg);
			return false;
		}
		$this->arrInputParam = $ret;

		Tieba_Stlog::setFileName('feye-stat');
		return true;
	}

	public function execute(){
		if($this->_intErrorNo === 0 ){
			$this->process();
		}
		$this->Log();
		$this->destroy();
	}

	public function destroy(){
		$this->buildPage();
		if($this->_intErrorNo !== 0 ){
			//ʧ�ܣ����ӡʧ�ܵĲ���
			Bingo_Log::warning("[deal error][".$this->_intErrorNo."|".$this->_strErrorMsg."]");
			if($this->autoCommit === false ){
			    dal_reply::rollback();
			}
		}
		else{
			if($this->autoCommit === false ){
				dal_reply::commit();
			}
			reply_Public_DalCache::inactiveAllCache();
		}
	}

	protected  function buildPage(){
		if($this->_strTemplete !== false){
			Bingo_Page::assign('err_no',$this->_intErrorNo);
			if($this->_intErrorNo != 0 ){
				Bingo_Page::assign('err_text','failed!');
			}else{
				Bingo_Page::assign('err_text',$this->_strErrorMsg);
			}
			Bingo_Page::assign($this->_arrTplVar);
			Bingo_Page::buildPage();
		}else if($this->_alt == 'json' ){  //json ����
			if($this->_intErrorNo != 0 ){
				$this->_jsonData['no']    = $this->_intErrorNo;
				$this->_jsonData['error'] = 'failed!';
				$this->_jsonData['data'] = array();
			}else{
				$this->_jsonData['data'] = $this->_arrTplVar;
			}
			echo(Bingo_String::array2json($this->_jsonData));
		}else{
			//...nothing todo
			Bingo_Log::debug("not match output type!alt=[".$this->_alt."]");
		}
	}

	protected function Log(){
	    if(empty($_SERVER['HTTP_USER_AGENT'])){
	        Tieba_Stlog::addNode('agent','none');
	    }
	    
	    Tieba_Stlog::addNode('email',      Tieba_Session_Socket::getEmail() );
	    Tieba_Stlog::addNode('mobilephone',Tieba_Session_Socket::getMobilephone());
        Tieba_Stlog::addNode('no_un',      intval(Tieba_Session_Socket::getNo_un()) );
        
		Tieba_Stlog::addNode('url', $_SERVER['PATH_INFO']);
		Tieba_Stlog::addNode('mid', MODULE);
		Tieba_Stlog::addNode('ref', isset($_SERVER['HTTP_REFERER'])?$_SERVER['HTTP_REFERER']:'');
		Tieba_Stlog::addNode('errno', $this->_intErrorNo);
		Tieba_Stlog::addNode('ErrMsg', $this->_strErrorMsg);
		Tieba_Stlog::addNode('urlkey', Bingo_Controller_Front::getInstance()->getDispatchRouter());
	}


	/**
	 *  templet & array
	 *
	 */
	protected function _arrayCheck($arrTemplet,&$arrayParam){
		if(!is_array($arrayParam)){
			Bingo_Log::warning("array check faile[".serialize($arrayParam)."][".serialize($arrTemplet)."]");
			return false;
		}

		//����
		if(count($arrTemplet) === 1 && isset($arrTemplet[0])){
			if(count($arrayParam) === 0 ){
				Bingo_Log::warning("input array empty!");
				return false;
			}
			foreach($arrayParam as &$value){
				$ret = $this->_arrayCheck($arrTemplet[0],$value);
				if($ret === false ){
					return false;
				}
			}
			return true;
		}else{//������
			foreach($arrTemplet as $tplKey => $tplValue ){
				if(!isset($arrayParam[$tplKey])){
					$arrayParam[$tplKey] =null;//�Զ�ȡĬ��ֵ
					//Bingo_Log::debug("[$tplKey]is empty".print_r( $tplValue,true));
				}

				if(is_array($tplValue)){
					$ret = $this->_arrayCheck($tplValue,$arrayParam[$tplKey]);
					Bingo_Log::warning("param check faile![$tplKey]");
					if($ret === false ){
						return false;
					}
				}else{
					$ret = $this->_checkParams($tplKey,$arrayParam[$tplKey],$arrTemplet);
					if($ret === false ){
						Bingo_Log::warning("param check faile![$tplKey][".serialize($arrayParam)."][".serialize($arrTemplet)."]");
						return false;
					}
				}
			}
		}
		return true;
	}

	/*
	 * У�����
	*/
	protected function _checkParams($key,&$value,$paramConf){
		$type = TypeConf::STRING_TYPE;
		$jsonTemplete = array();
		if(isset($paramConf[$key])){
			if(is_array($paramConf[$key])){//��������飬����json����
				$type = TypeConf::JSON_TYPE;
				$jsonTemplete = $paramConf[$key];
			}else{
				$type = $paramConf[$key];
			}
		}

		$checkRet = true;
		$resValue = $value;
		if($resValue !== null )
		{
			switch ($type){
				case TypeConf::STRING_TYPE:
					if($resValue === false ){
						$resValue = null;
						Bingo_Log::warning("[$key]string convert error![$value][".Bingo_Encode::ENCODE_GBK."][".Upload_Public_Request::$ie."]");
						$checkRet =  false;
					}
					break;
				case TypeConf::INT_TYPE:
					if(!is_numeric($value) && $value != false ){
						Bingo_Log::warning("[$key]param is not number![$value]");
						$checkRet =  false;
					}
					$resValue = intval($value);
					break;
				case TypeConf::JSON_TYPE:
					if(strlen($value) > TypeConf::MAX_JSON_TYPE_LEN){
						Bingo_Log::warning('json type data is too long!'.strlen($value));
						$checkRet =  false;
					}
					//��Ҫת��Ϊutf-8���룬����json2array��ʧ��!
					$value = Bingo_Encode::convert($value, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_TYPE_MB_STRING);
					$resValue = Bingo_String::json2array($value);
					//Bingo_Log::debug("json type![$key]".serialize($resValue));
					if( $resValue === false || !($this->_arrayCheck($jsonTemplete,$resValue)) ){
						Bingo_Log::warning("[$key]json type check faile!|[$resValue]".serialize($value));
						$resValue = null;
						$checkRet =  false;
					}
					break;
				default:
					if($resValue === false ){
					$resValue = null;
					Bingo_Log::warning("[$key]string convert error![$value][".Bingo_Encode::ENCODE_GBK."][".Upload_Public_Request::$ie."]");
					$checkRet =  false;
				}
				break;
			}

			//ִ�в���У��

			if($checkRet == true && isset($this->_arrCheckParams[$key]) && strlen(trim($this->_arrCheckParams[$key])) > 0 ){
				if( !isset(self::$_userCheckFunc[$this->_arrCheckParams[$key]])){
					self::$_userCheckFunc[$this->_arrCheckParams[$key]] =
					create_function('&$'.$key,'return '.$this->_arrCheckParams[$key].';');
				}
				$func = self::$_userCheckFunc[$this->_arrCheckParams[$key]];
				$checkRet = $func($resValue);
				//Bingo_Log::debug("[$key][$resValue]".'function (&$'.$key.'){return '.$this->_arrCheckParams[$key].';}');
				if($checkRet === false ){
					Bingo_Log::debug('check func return false');
				}
			}

		}

		if ($resValue === null || $checkRet === false ){
			//ȡĬ��ֵ
			//Bingo_Log::warning(print_r($this->_arrDefaultParams,true));
			if(!isset($this->_arrDefaultParams[$key])){
				Bingo_Log::warning("[$key] params is missing or not right!");
				return false;
			}
			else{
				$resValue = $this->_arrDefaultParams[$key];
			}
		}

		$value = $resValue;
		return true;
	}

	/*
	 * ��ȡ������������
	* ��ȡ����������
	*     ��ȡ��Ҫ�Ĳ��������ȡ������ϡ�����������������ڻ��߲���У��ʧ�ܣ�
	*     ��᳢�Ի�ȡĬ�ϲ��������Ĭ�ϲ��������ڣ��򷵻ش���
	*/
	protected function _getInputParams(){
		if (empty($this->_arrNeedParams)){
			return array();
		}
		$arrParams = array();
		foreach ($this->_arrNeedParams as $key => $type){
			$resValue = Bingo_Http_Request::get($key,null);
			//Bingo_Log::debug("[$key][$resValue]");

			$checkRet = $this->_checkParams($key,$resValue,$this->_arrNeedParams);
			if($checkRet === false ){
				Bingo_Log::warning("[$key] get param faile!");
				return false;
			}
			$arrParams[$key] = $resValue;
			if(!is_array($resValue)){
			    if($key != 'postContent'){
				    Tieba_Stlog::addNode($key,$resValue);//�����������log
				}
			}
		}
		//Bingo_Log::debug(print_r($arrParams,true));
		return $arrParams;
	}
	
	
	
	public function gradeLevel($fid,$uid)
	{
		if( $uid!= 0 ){
			$levelRes = RpcIdlFulike::getUserLevel($fid,$uid);
			if( $levelRes === false )
			{
				Bingo_Log::warning('get level  from RpcIdlFulike::getUserLevel failed');
			}
			if(isset($levelRes['is_like']) && $levelRes['is_like'] > 0 && isset($levelRes['level_id']) && $levelRes['level_id'] >=0 )
			{
				//$userGrade = $levelRes['level_id'];
				return  intval($levelRes['level_id']);
			}
			
		}
	}
	
	
	
	protected function _getPerm($fid,$uid){
		if ($fid == 0){
			$this->_arrUserRole['perm_flag'] = 0;
			return 0;
		}
		Bingo_Timer::start('perm1');
		$arrRet = Rpc_PermServer::getUserRole($fid, $uid);
		Bingo_Timer::end('perm1');
		if ($arrRet === false){
			Bingo_Log::warning('get User Role failed');
			return false;
		}
		$arrInput = array();
		foreach ($arrRet as $role){
			$arrInput[] = $role['role_id'];
		}
		$permflag = 0;
		Bingo_Timer::start('perm2');
		$permflag = Rpc_PermServer::getPermFlagByNewRole($arrInput);
		Bingo_Timer::start('perm2');
		if ($permflag === false){
			Bingo_Log::warning('get perm flag failed');
			return false;
		}
		$this->_arrUserRole['perm_flag'] = intval($permflag);
		return true; 
	}
	
	protected function _getErrNo($intNo){
		$intErr=conf_ErrorNo::errUnknown;
	if($intNo==40)	
	{
		$intErr=conf_ErrorNo::errVcode;
		
	}
	else if($intNo==38)
	{
		$intErr=conf_ErrorNo::errVcodeOvertime;
	}
	else if($intNo==32)
	{
		$intErr=conf_ErrorNo::errActionControl;
	}
		return $intErr;
	}
	

}

class TypeConf
{

	const MAX_JSON_TYPE_LEN = 3072000;  //���json���ݳ��ȣ�3M

	const  STRING_TYPE = 0;  // �ַ�����������
	const  INT_TYPE    = 1;  // ������������
	const  JSON_TYPE   = 2;  // json��������
}
?>
