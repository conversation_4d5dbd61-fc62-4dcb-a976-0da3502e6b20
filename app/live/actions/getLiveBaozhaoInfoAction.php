<?php
/**
 * ===========================================
 * @desc: 获取爆照的弹幕、图片数据
 * @author: fengzhen
 * @date: 2015-8-12
 * ===========================================
 * @version 1.0.0
 * @copyright Copyright (c) www.baidu.com
 */
class getLiveBaozhaoInfoAction extends Util_Base
{

    // 参数
    const LIVE_BAOZHAO = 6;//爆照直播贴
    const LIVE_DITU = 7;//地图全景直播
    const DEFAULT_NUM = 30;
    const DEFAULT_DISPLAY_NUM = 13;
    const DEFAULT_DISPLAY_COEFFICOCIENT = '3.14*85';
    const LIVE_UI_CACHE_PREFIX = 'live_ui_baozhao_';
    const EXPIRE_TIME = 10;
    const MAX_GET_POST_NUM = 100;
    protected $_intTid = 0;
    protected $_intPid = 0;
    protected $_intFid = 0;
    protected $_intTaskId = 0;
    protected $_intUid = 0;
    protected $_strUname = '';
    protected $_intRn = 0;
    protected $_intType;
    protected $_coefficient;
    protected $_filterPrex = '';//【我的七夕宣言】
    //多媒体替换策略
    protected static $_arrDiscussReplacement = array(
        'picture' => '[图片]',
        'video' => '[视频]',
        'music' => '[音乐]',
        'face' => '[表情]',
    );


    public function init() {
        return parent::init();
    }

    // 业务逻辑实现
    public function execute() {
        // 返回值初始化
        $arrOut = array();
        $this->_initParams();
        $intType = $this->_intType;
        if(!$this->_checkParams()){
        	$arrOutPut = $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR, "param error");
        	echo Bingo_String::array2json($arrOutPut);
        	return true;
        }
        $strCachekey = self::_getUiCacheKey();
        $arrCacheRes = Util_Memcached::getCache($strCachekey);
        if ($arrCacheRes) {
            $arrResult = unserialize($arrCacheRes);
            echo Bingo_String::array2json($arrResult);
            return true;
        }
        $coefficient = $this->_coefficient;
        
        $arrRes = self::_getLiveBaozhaoList();
        if (!empty($arrRes)) {
            $arrOut['post_list'] = $arrRes['discuss_list'];
            $arrOut['pic_list'] = $arrRes['pic_list'];
            $arrOut['server_time'] = time();
            $arrOut['reply_num'] = $arrRes['reply_num'];
            $intPageView = intval($arrRes['thread_info']['freq_num']);
            $arrOut['display_num'] = self::_getDisplayNum($intPageView,$coefficient,self::DEFAULT_DISPLAY_NUM);
        }
        $arrOutPut = $this->_errRet(0, "success", $arrOut);
        $this->_setNginxCache($arrOutPut);
        $this->_setUiCache($arrOutPut);
        echo Bingo_String::array2json($arrOutPut);
        return true;
    }

    // 参数初始化
    protected function _initParams() {
        $this->_intTid = intval(Bingo_Http_Request::get('threadId', 0));
        $this->_intTaskId = intval(Bingo_Http_Request::get('taskId', 0));
        $this->_intRn = intval(Bingo_Http_Request::get('rn', 50));
        $this->_intUid = $this->_arrUserInfo['user_id'];
        $this->_strUname = $this->_arrUserInfo['user_name'];
        $this->_intType = intval(Bingo_Http_Request::get('type', 0));
        $this->_coefficient = Bingo_Http_Request::get('coefficient');
        $this->_intPid = intval(Bingo_Http_Request::get('post_id'));
        $this->_intFid = intval(Bingo_Http_Request::get('forum_id'));
        $this->_filterPrex = Bingo_Http_Request::get('filterPrex','');
        $this->_filterPrex = Bingo_Encode::convert($this->_filterPrex,Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
    }
    
    protected function _checkParams(){
    	if($this->_intType !== self::LIVE_BAOZHAO && $this->_intType !== self::LIVE_DITU){
        	Bingo_Log::warning($this->_intTid . "live_type is invalid." . $this->_intType);
			return false;
        }
        if(empty($this->_intTid) || empty($this->_intTaskId)){
        	Bingo_Log::warning($this->_intTid . " tid or taskid is invalid." . $this->_intTaskId);
			return false;
        }
        return true;
    }

    // 获取访谈直播的信息
    protected function _getLiveTaskInfo() {
        $arrOut = array();
        $arrInput = array(
            'id' => $this->_intTaskId,
        );
        $arrRes = Tieba_Service::call('live', 'getLiveTaskInfo', $arrInput);
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning("call live getLiveTaskInfoByTid fail input[" . serialize($arrInput) . "]");
            return $arrOut;
        }
        if (!empty($arrRes['data'])) {
            $arrOut = $arrRes['data'];
        }
        return $arrOut;
    }

    protected function  _getLiveBaozhaoList() {
        //获取直播贴内容
        $arrOut = array();
        $intPid = $this->_intPid;
        $arrPostsInfo = array();
        $arrThreadInfo = array();
        //并行调用两个serivice
        $objRalMulti = new Tieba_Multi('post_data');
        $arrMultiInput = array(
            'serviceName' => 'post',
            'method' => 'getInvertPostsByThreadId',
            'input' => array(
                'thread_id' => $this->_intTid,
                'offset' => 0,
                'res_num' => self::MAX_GET_POST_NUM,
                'see_author' => 0,
                'has_comment' => 0,
                'has_mask' => 0,
                'uids' => array(
                    $this->_intUid,
                ),
            ),
        );
        $objRalMulti->register('post', new Tieba_Service('post'), $arrMultiInput);
        $arrMultiInput = array(
            'serviceName' => 'post',
            'method' => 'mgetThread',
            'input' => array(
                'thread_ids' => (array)$this->_intTid,
                'need_abstract' => 1,
                'need_photo_pic' => 0,
                'forum_id' => $this->_intFid,
                'need_user_data' => 1,
                'icon_size' => 3,
                'need_forum_name' => 0,
                'call_from' => 'pc_frs',
            ),
        );
        $objRalMulti->register('thread', new Tieba_Service('post'), $arrMultiInput);
        $arrMultiOut = $objRalMulti->call();
        foreach ($arrMultiOut as $key => $arrServiceOut) {
            if ($key == 'post') {
                if ($arrServiceOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning($this->_intTid . "call service {$key} error. output = [" . serialize($arrServiceOut) . "]");
                }
                $arrPostListInfo = $arrServiceOut['output'];
            }
            if ($key == 'thread') {
                if ($arrServiceOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning($this->_intTid . "call service {$key} error. output = [" . serialize($arrServiceOut) . "]");
                }
                $arrThreadInfo = $arrServiceOut['output'];
            }

        }
        if (!empty($arrPostListInfo)) {
            $intReplyNum = $arrPostListInfo['output'][0]['valid_post_num'];
            $arrPostsInfo = $arrPostListInfo['output'][0]['post_infos'];
            $arrPostsInfo = $this->_filterPosts($arrPostsInfo, $intPid);
            $arrUids = $this->_getUids($arrPostsInfo, array());
            $arrItbInfo = Util_Live::getItbInfo($arrUids);
            $arrPostsInfo = $this->_filterUserMaskPosts($arrPostsInfo, $arrUids);
            $arrPicPosts = Util_Live::filterNeedPicPosts($arrPostsInfo,$this->_filterPrex,$this->_intRn);
            $arrOut['pic_list'] = $arrPicPosts['pic_list'];
            $arrDiscussPostsInfo = Util_Live::buildPosts($arrPicPosts['bar_list'], $arrItbInfo,self::$_arrDiscussReplacement);
            $arrOut['discuss_list'] = $arrDiscussPostsInfo;
            $arrOut['reply_num'] = $intReplyNum;

        }
        if (!empty($arrThreadInfo)) {
            $arrOut['thread_info'] = array(
                'abstract' => strval($arrThreadInfo['thread_list'][$this->_intTid]['abstract']),
                'freq_num' => intval($arrThreadInfo['thread_list'][$this->_intTid]['freq_num']),
            );
        }
        return $arrOut;
    }

    protected function _getDisplayNum($base,$coefficient,$default) {
        if ($coefficient == 0) {
            $coefficient = 1;
        }
        $resNum = intval($base * $coefficient + $default);
        return $resNum;
    }

    protected  function _setNginxCache($arrResult) {
        $arrResult = Bingo_String::array2json($arrResult);
        $hostName = $_SERVER['x_bd_routerip'];
        $strKey = $hostName . ":" . "baozhao:" . $this->_intTaskId . ":" . $this->_intTid . ":" . $this->_intRn;
        $arrRes = Util_Cache::addCache($strKey, $arrResult);
        if($arrRes === false) {
            Bingo_Log::warning("add nginx cache error with baozhao list with[task_id =" . $this->_intTaskId ."]");
        }
        return true;
    }

    protected function  _setUiCache($arrResult) {
        $strKey = self::_getUiCacheKey();
        $addRes = Util_Memcached::addCache($strKey, serialize($arrResult), self::EXPIRE_TIME);
        if (false === $addRes) {
            Bingo_Log::warning('add live baozhao list cache fail param:' . serialize($arrResult) . ' out:' . serialize($addRes));
        }
        return true;
    }

    protected function _getUiCacheKey() {
        $strKey = self::LIVE_UI_CACHE_PREFIX.$this->_intTaskId . "_" . $this->_intTid . "_" . $this->_intRn;
        return $strKey;
    }
}
