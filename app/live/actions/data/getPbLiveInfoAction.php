<?php

/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/1/4
 * Time: 11:27
 */
class getPbLiveInfoAction extends Util_Base
{
    // 参数
    const DEFAULT_DISPLAY_NUM = 13;
    const DEFAULT_NUM = 30;
    const ON_LINE = 1;
    const OFF_LINE = 2;
    //访谈区的多媒体替换策略
    protected static $_arrInterViewReplacement = array(
        'picture' => '',
        'video' => '[视频]',
        'music' => '[音乐]',
        'face' => '[表情]',
    );
    //讨论区的多媒体替换策略
    protected static $_arrDiscussReplacement = array(
        'picture' => '[图片]',
        'video' => '[视频]',
        'music' => '[音乐]',
        'face' => '[表情]',
    );
    protected $_intTid = 0;
    protected $_intFid = 0;
    protected $_intTaskId = 0;
    protected $_intUid = 0;
    protected $_strUname = '';
    protected $_isCompere = false;
    protected $_isGuest = false;
    protected $_status = 0;//当前直播的任务是进行中还是已经结束
    protected $_intRn = 0;

    public function init() {
        return parent::init();
    }

    // 业务逻辑实现
    public function execute() {
        // 返回值初始化
        $arrOut = array();
        $this->_initParams();
        $arrTaskInfo = $this->_getLiveTaskInfo();
        if (!empty($arrTaskInfo)) {
            $intType = $arrTaskInfo['type'];
            switch ($intType) {
                case Util_Const::LIVE_INTERVIEW_TEXT:
                    self::_processLiveInterviewText($arrTaskInfo, $bolIsOnline = false);
                    break;
                case Util_Const::LIVE_INTERVIEW_VEDIO:
                    break;
                case Util_Const::LIVE_INTERVIEW_PK:
                    break;
                case Util_Const::LIVE_TOPIC_TEXT:
                    break;
                case Util_Const::LIVE_TOPIC_PIC:
                    break;
                default:
                    break;
            }
        }
        return true;
    }

    // 参数初始化
    protected function _initParams() {
        $this->_intTid = intval(Bingo_Http_Request::get('thread_id', 0));
        $this->_intUid = intval(Bingo_Http_Request::get('user_id', 0));
        $this->_strUname = strval(Bingo_Http_Request::get('user_name', 0));
        $this->_intTaskId = intval(Bingo_Http_Request::get('task_id', 0));
        $this->_status = (bool)(Bingo_Http_Request::get('status', 0));
        $this->_intRn = intval(Bingo_Http_Request::get('rn', self::DEFAULT_NUM));
    }

    // 获取访谈直播的信息
    protected function _getLiveTaskInfo() {
        $arrOut = array();
        $arrInput = array(
            'id' => $this->_intTaskId,
        );
        $arrRes = Tieba_Service::call('live', 'getLiveTaskInfo', $arrInput);
        if (fasle === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning("call live getLiveTaskInfoByTid fail input[" . serialize($arrInput) . "]");
            return $arrOut;
        }
        if (!empty($arrRes['data'])) {
            $arrOut = $arrRes['data'];
        }
        return $arrOut;
    }

    protected function _processLiveInterviewText($arrTaskInfo, $status) {
        $arrOutPut = array();
        $strKey = self::_getMemcacheKey($this->_intTid, $arrTaskInfo['id']);
        if ($status == self::OFF_LINE) {
            $arrCacheRes = Util_Memcached::getCache($strKey);
            if ($arrCacheRes) {
                $arrOutPut = unserialize($arrCacheRes);
                $arrResult = $this->_errRet(0, "success", $arrOutPut);
                echo Bingo_String::array2json($arrResult);
                return true;
            } else {
                $this->_intRn = 0; //如果直播已经过期，则取出所有的问答列表，否则取默认条数,rn =0 表示所有条数
            }
        }
        $arrOutPut['task_info'] = $arrTaskInfo;
        $intType = $arrTaskInfo['type'];
        $coefficient = $arrTaskInfo['coefficient'];
        $arrOutPut['forum_id'] = $arrTaskInfo['content']['forum_id'];
        $arrOutPut['forum_name'] = $arrTaskInfo['content']['forum_name'];
        $arrOutPut['thread_id'] = $this->_intTid;
        $arrOutPut['live_type'] = $intType;
        $arrRes = self::_getLiveInterviewList($arrTaskInfo,self::$_arrInterViewReplacement,true);
        if (!empty($arrRes)) {
            $arrOutPut['interview_list'] = $arrRes['interview_list'];
            $arrOutPut['thread_info'] = $arrRes['thread_info'];
            $intPageView = intval($arrRes['thread_info']['freq_num']);
            $arrOutPut['display_num'] = self::_getDisplayNum($intPageView, $coefficient, self::DEFAULT_DISPLAY_NUM);
        }
        if ($status == self::OFF_LINE) {
            $addRes = Util_Memcached::addCache($strKey, serialize($arrOutPut), 0);
            if (false === $addRes) {
                Bingo_Log::warning('add Live pb info cache fail param:' . serialize($arrOutPut) . ' out:' . serialize($addRes));
            }
        }
        $arrResult = $this->_errRet(0, "success", $arrOutPut);
        echo Bingo_String::array2json($arrResult);
        return true;
    }

    protected function _processLiveInterviewVideo($arrTaskInfo, $status) {
        $arrOutPut = array();
        $strKey = self::_getMemcacheKey($this->_intTid, $arrTaskInfo['id']);
        if ($status == self::OFF_LINE) {
            $arrCacheRes = Util_Memcached::getCache($strKey);
            if ($arrCacheRes) {
                $arrOutPut = unserialize($arrCacheRes);
                $arrResult = $this->_errRet(0, "success", $arrOutPut);
                echo Bingo_String::array2json($arrResult);
                return true;
            } else {
                $this->_intRn = 0; //如果直播已经过期，则取出所有的问答列表，否则取默认条数,rn =0 表示所有条数
            }
        }
        $arrOutPut['task_info'] = $arrTaskInfo;
        $intType = $arrTaskInfo['type'];
        $coefficient = $arrTaskInfo['coefficient'];
        $arrOutPut['forum_id'] = $arrTaskInfo['content']['forum_id'];
        $arrOutPut['forum_name'] = $arrTaskInfo['content']['forum_name'];
        $arrOutPut['thread_id'] = $this->_intTid;
        $arrOutPut['live_type'] = $intType;
        $arrRes = self::_getLiveInterviewList($arrTaskInfo);
        if (!empty($arrRes)) {
            $arrOutPut['interview_list'] = $arrRes['interview_list'];
            $arrOutPut['thread_info'] = $arrRes['thread_info'];
            $intPageView = intval($arrRes['thread_info']['freq_num']);
            $arrOutPut['display_num'] = self::_getDisplayNum($intPageView, $coefficient, self::DEFAULT_DISPLAY_NUM);
        }
        if ($status == self::OFF_LINE) {
            $addRes = Util_Memcached::addCache($strKey, serialize($arrOutPut), 0);
            if (false === $addRes) {
                Bingo_Log::warning('add Live pb info cache fail param:' . serialize($arrOutPut) . ' out:' . serialize($addRes));
            }
        }
        $arrResult = $this->_errRet(0, "success", $arrOutPut);
        echo Bingo_String::array2json($arrResult);
        return true;
    }

    protected function  _processLiveInterviewPk($arrTaskInfo, $status) {
        $arrOutPut = array();
        $strKey = self::_getMemcacheKey($this->_intTid, $arrTaskInfo['id']);
        if ($status == self::OFF_LINE) {
            $arrCacheRes = Util_Memcached::getCache($strKey);
            if ($arrCacheRes) {
                $arrOutPut = unserialize($arrCacheRes);
                $arrResult = $this->_errRet(0, "success", $arrOutPut);
                echo Bingo_String::array2json($arrResult);
                return true;
            } else {
                $this->_intRn = 0; //如果直播已经过期，则取出所有的问答列表，否则取默认条数,rn =0 表示所有条数
            }
        }
        $arrOutPut['task_info'] = $arrTaskInfo;
        $intType = $arrTaskInfo['type'];
        $coefficient = $arrTaskInfo['coefficient'];
        $arrOutPut['forum_id'] = $arrTaskInfo['content']['forum_id'];
        $arrOutPut['forum_name'] = $arrTaskInfo['content']['forum_name'];
        $arrOutPut['thread_id'] = $this->_intTid;
        $arrOutPut['live_type'] = $intType;
        $arrRes = self::_getLivePkInterviewList($arrTaskInfo);
        if (!empty($arrRes)) {
            $arrOutPut['interview_list'] = $arrRes['interview_list'];
            $arrOutPut['pk_thread'] = $arrRes['pk_info'];
            $arrOutPut['thread_info'] = $arrRes['thread_info'];
            $intPageView = intval($arrRes['thread_info']['freq_num']);
            $arrOutPut['display_num'] = self::_getDisplayNum($intPageView, $coefficient, self::DEFAULT_DISPLAY_NUM);
        }
        if ($status == self::OFF_LINE) {
            $addRes = Util_Memcached::addCache($strKey, serialize($arrOutPut), 0);
            if (false === $addRes) {
                Bingo_Log::warning('add Live pb info cache fail param:' . serialize($arrOutPut) . ' out:' . serialize($addRes));
            }
        }
        $arrResult = $this->_errRet(0, "success", $arrOutPut);
        echo Bingo_String::array2json($arrResult);
        return true;
    }

    protected function  _processLiveTopicText($arrTaskInfo) {

    }

    protected function  _processLiveTopicPic($arrTaskInfo) {

    }

    protected function _getLiveInterviewList($arrTaskInfo,$arrInterviewReplacement = array(),$bolMedia = false) {
        $arrOut = array();
        $arrPostsInfo = array();
        $arrThreadInfo = array();
        $arrInterviewInfo = array();
        $arrComperes = $arrTaskInfo['content']['comperes'];
        $arrGuests = $arrTaskInfo['content']['guests'];
        $intTaskId = intval($arrTaskInfo['id']);
        $this->_isCompere = Util_Live::isCompere($this->_intUid, $arrComperes);
        $this->_isGuest = Util_Live::isGuest($this->_intUid, $arrGuests);
        //获取访谈问答模块内容
        $bolIsSpecial = $this->_isCompere || $this->_isGuest;
        //并行调用三个serivice
        $objRalMulti = new Tieba_Multi('live_data');
        $arrMultiInput = array(
            'serviceName' => 'live',
            'method' => 'getLiveInterviewInfoByTidAndTaskId',
            'input' => array(
                'task_id' => $intTaskId,
                'thread_id' => $this->_intTid,
                'bolIsSpecial' => true,
                'rn' => $this->_intRn,
            ),
        );
        $objRalMulti->register('live', new Tieba_Service('live'), $arrMultiInput);
        $arrMultiInput = array(
            'serviceName' => 'post',
            'method' => 'mgetThread',
            'input' => array(
                'thread_ids' => (array)$this->_intTid,
                'need_abstract' => 1,
                'need_photo_pic' => 0,
                'forum_id' => $arrTaskInfo['content']['forum_id'],
                'need_user_data' => 1,
                'icon_size' => 3,
                'need_forum_name' => 0,
                'call_from' => 'pc_frs',
            ),
        );
        $objRalMulti->register('thread', new Tieba_Service('post'), $arrMultiInput);
        $arrMultiOut = $objRalMulti->call();
        foreach ($arrMultiOut as $key => $arrServiceOut) {
            if ($key == 'live') {
                if ($arrServiceOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning("call service {$key} error. output = [" . serialize($arrServiceOut) . "]");
                }
                $arrInterviewInfo = $arrServiceOut['data'];
            }
            if ($key == 'thread') {
                if ($arrServiceOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning("call service {$key} error. output = [" . serialize($arrServiceOut) . "]");
                }
                $arrThreadInfo = $arrServiceOut['output'];
            }
        }
        if (!empty($arrInterviewInfo)) {
            $arrInterviewInfo = array_reverse($arrInterviewInfo);
            $arrUids = $this->_getUids(array(), $arrInterviewInfo);
            //直播贴的用户id
            $intThreadToUserId = $arrTaskInfo['content']['user_id'];
            if (!empty($intThreadToUserId)) {
                array_push($arrUids, $intThreadToUserId);
            }
            $arrItbInfo = Util_Live::getItbInfo($arrUids);
            //获取用户信息
            $arrThreadUserInfo = !empty($arrItbInfo[$intThreadToUserId]) ? $arrItbInfo[$intThreadToUserId] : array();
            $arrInterviewPostsInfo = Util_Live::buildInterviewQA($arrInterviewInfo, $arrItbInfo, $arrComperes, $arrGuests,$arrInterviewReplacement,$bolMedia);
            $arrOut['interview_list'] = $arrInterviewPostsInfo;
        }
        if (!empty($arrThreadInfo)) {
            $arrOut['thread_info'] = array(
                'abstract' => strval($arrThreadInfo['thread_list'][$this->_intTid]['abstract']),
                'freq_num' => intval($arrThreadInfo['thread_list'][$this->_intTid]['freq_num']),
                'author' => array(
                    'user_name' => strval($arrThreadInfo['thread_user_list'][$intThreadToUserId]['user_name']),
                    'user_nickname' => strval($arrThreadUserInfo['nick']),
                    'is_verify' => intval($arrThreadUserInfo['is_verify']),
                ),
            );
        }
        return $arrOut;
    }

    protected function _getLivePkInterviewList($arrTaskInfo) {
        $arrOut = array();
        $arrPostsInfo = array();
        $arrThreadInfo = array();
        $arrInterviewInfo = array();
        $arrPkInfo = array();
        //嘉宾主持人信息
        $arrComperes = $arrTaskInfo['content']['comperes'];
        $arrGuests = $arrTaskInfo['content']['guests'];
        $intTaskId = intval($arrTaskInfo['id']);
        $this->_isCompere = Util_Live::isCompere($this->_intUid, $arrComperes);
        $this->_isGuest = Util_Live::isGuest($this->_intUid, $arrGuests);
        $bolIsSpecial = $this->_isCompere || $this->_isGuest;
        //pk信息
        $strRedViewpoint = $arrTaskInfo['content']['red_viewpoint'];
        $strBlueViewpoint = $arrTaskInfo['content']['blue_viewpoint'];
        ////并行调用四个serivice
        $objRalMulti = new Tieba_Multi('live_data');
        $arrMultiInput = array(
            'serviceName' => 'live',
            'method' => 'getLiveInterviewInfoByTidAndTaskId',
            'input' => array(
                'task_id' => $intTaskId,
                'thread_id' => $this->_intTid,
                'bolIsSpecial' => $bolIsSpecial,
                'rn' => $this->_intRn,
            ),
        );
        $objRalMulti->register('interview', new Tieba_Service('live'), $arrMultiInput);
        $arrMultiInput = array(
            'serviceName' => 'live',
            'method' => 'getPkLiveInfo',
            'input' => array(
                'thread_id' => $this->_intTid,
            ),
        );
        $objRalMulti->register('pk', new Tieba_Service('live'), $arrMultiInput);
        $arrMultiInput = array(
            'serviceName' => 'post',
            'method' => 'getInvertPostsByThreadId',
            'input' => array(
                'thread_id' => $this->_intTid,
                'offset' => 0,
                'res_num' => $this->_intRn,
                'see_author' => 0,
                'has_comment' => 0,
                'has_mask' => 0,
                'uids' => array(
                    $this->_intUid,
                ),
            ),
        );
        $objRalMulti->register('thread', new Tieba_Service('post'), $arrMultiInput);
        $arrMultiOut = $objRalMulti->call();
        foreach ($arrMultiOut as $key => $arrServiceOut) {
            if ($key == 'interview') {
                if ($arrServiceOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning("call service {$key} error. output = [" . serialize($arrServiceOut) . "]");
                }
                $arrInterviewInfo = $arrServiceOut['data'];

            }
            if ($key == 'pk') {
                if ($arrServiceOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning("call service {$key} error. output = [" . serialize($arrServiceOut) . "]");
                }
                $arrPkInfo = $arrServiceOut['data'];
            }
            if ($key == 'thread') {
                if ($arrServiceOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning("call service {$key} error. output = [" . serialize($arrServiceOut) . "]");
                }
                $arrThreadInfo = $arrServiceOut['output']['thread_list'][$this->_intTid];
            }
        }
        if (!empty($arrInterviewInfo)) {
            $arrInterviewInfo = array_reverse($arrInterviewInfo);
            $arrUids = $this->_getUids(array(), $arrInterviewInfo);
            //直播贴用户id
            $intThreadToUserId = $arrTaskInfo['content']['user_id'];
            if (!empty($intThreadToUserId)) {
                array_push($arrUids, $intThreadToUserId);
            }
            $arrItbInfo = Util_Live::getItbInfo($arrUids);
            //获取用户信息
            $arrThreadUserInfo = !empty($arrItbInfo[$intThreadToUserId]) ? $arrItbInfo[$intThreadToUserId] : array();
            $arrInterviewPostsInfo = Util_Live::buildInterviewQA($arrInterviewInfo, $arrItbInfo, $arrComperes, $arrGuests,self::_arrInterViewReplacement );
            $arrOut['interview_list'] = $arrInterviewPostsInfo;
        }
        if (isset($arrPkInfo)) {
            $arrVoteRed = array(
                'viewpoint' => $strRedViewpoint,
                'vote_num' => intval($arrPkInfo['vote_red_num']),
            );
            $arrVoteBlue = array(
                'viewpoint' => $strBlueViewpoint,
                'vote_num' => intval($arrPkInfo['vote_blue_num']),
            );

            $arrOut['pk_info'] = array(
                'vote_red' => $arrVoteRed,
                'vote_blue' => $arrVoteBlue,
            );
        }

        if (!empty($arrThreadInfo)) {
            $arrOut['thread_info'] = array(
                'abstract' => strval($arrThreadInfo['thread_list'][$this->_intTid]['abstract']),
                'freq_num' => intval($arrThreadInfo['thread_list'][$this->_intTid]['freq_num']),
                'author' => array(
                    'user_name' => strval($arrThreadInfo['thread_user_list'][$intThreadToUserId]['user_name']),
                    'user_nickname' => strval($arrThreadUserInfo['nick']),
                    'is_verify' => intval($arrThreadUserInfo['is_verify']),
                ),
            );
        }

        return $arrOut;
    }

    public function _getMemcacheKey($intThreadId, $intTaskId) {
        return $intThreadId . "_" . $intTaskId;
    }

    protected function _getDisplayNum($base, $coefficient, $default) {
        if ($coefficient == 0) {
            $coefficient = 1;
        }
        $resNum = intval($base * $coefficient + $default);
        return $resNum;
    }

}
