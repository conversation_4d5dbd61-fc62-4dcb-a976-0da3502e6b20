<?php

/**
 * @copyright Copyright (c) www.baidu.com
* <AUTHOR>
* @date 2016-01-11 11:48:00
* @comment 用户关注列表
* @version 1.0
*/

class userfollowsAction extends Libs_BaseAction {

    const NUM_FOLLOWS_PER_PAGE = 16;

    /**
     * 获取自定义参数
     *
     * @param
     * @return
     */
	protected function _getPrivateInfo() {
		return array (
            'user_id'     => intval($this->_getInput('user_id', 0)),
            'pn'          => intval($this->_getInput('pn', 1)),
			'check_login' => false,
			'need_login'  => true, 
		);
	}
	
    /**
     * @param
     * @return
     */
	protected function _checkPrivate() {
		return true;
	}
	
    /**
     * @param
     * @return
     */
	protected function _execute() {
        $pn = intval($this->_objRequest->getPrivateAttr('pn'));
        $pn = $pn <= 0 ? 1 : $pn;
        $offset = self::NUM_FOLLOWS_PER_PAGE * ($pn - 1);
        $limit  = self::NUM_FOLLOWS_PER_PAGE;

		$arrInput = array(
            'user_id'  => $this->_objRequest->getPrivateAttr('user_id'),
            'myId'     => $this->_objRequest->getCommonAttr('user_id'),
            'offset'   => $offset,
            'limit'    => $limit,
		);
        $arrRet = Tieba_Service::call('tbhigh','userFollowsPage',
            $arrInput, null, null, 'post', 'php', 'utf-8');
        if($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("get user follows page failed. errno[{$arrRet['errno']}]");
			$this->_error($arrRet['errno'], Tieba_Error::getErrmsg($arrRet['errno']));
            return false;
        }
        $arrOutput['data'] = $arrRet['data'];
		$this->_objResponse->setOutData($arrOutput);
	}
}
