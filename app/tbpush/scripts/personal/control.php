<?php

/**
 * @file control.php
 * @date 2019/06/03 22:40:49
 * @brief 总控脚本，主要针对个性化涉及的几个脚本做控制（部分逻辑在启动脚本前做比较方便）
 *        1.拉取用户数据文件脚本——getUserData.php
 *           该脚本没有特别需要注意的，仅仅注意下脚本启动的时间即刻，目前考虑0~1点之间完成该数据拉取工作
 *        2.拉取帖子数据文件脚本——getPersonalData.php
 *           该脚本需要从推荐那边拉取数据，但是推荐的qps是有限制的，目前是7~24点不超过100，1~7点不超过500
 *        3.推送数据脚本——pushPersonalData.php
 *           该脚本暂时不使用，后续看情况是否需要
 *
 **/

set_time_limit(0);
ini_set('memory_limit', '2048M');
date_default_timezone_set("Asia/Chongqing");


define('SCRIPTNAME', basename(__FILE__, ".php"));   //定义脚本名
define('BASEPATH', dirname(__FILE__));

require_once(BASEPATH . "/../../util/Base.php");
require_once(BASEPATH . "/../../util/Define.php");

$intZeroDate = date('Ymd', strtotime('0 day'));
$intNowTime  = time();
$intNowTour = date('H', $intNowTime);

// 清除进程
$strCmd = "ps aux | grep getPersonalData.php | awk -F' ' '{print $2}' | xargs kill -9";
exec($strCmd);
$strCmd = "";
sleep(5);

// 拉取帖子数据文件脚本
if (true) {
    $strDir     = DATAPATH_TEMP . '/uids_' . date('Ymd', strtotime('-2 day')) . '/' . $intDate;
    $arrFList   = scandir($strDir);
    foreach ($arrFList as $key => $value) {
        if ($value == '.' || $value == '..') {
            unset($arrFList[$key]);
        }
    }
    $strLog = 'need to deal file count[' . count($arrFList) . ']';
    Util_Base::_log($strLog, __LINE__);

    $intRunLimit = 100;
    $intRunCount = 0;
    foreach ($arrFList as $key => $filename) {
        if ($intRunCount++ >= $intRunLimit) {
            break;
        }

        $strCmd = "/home/<USER>/odp/bin/php /home/<USER>/odp/app/tbpush/scripts/personal/getPersonalData.php " . $filename . " > /home/<USER>/Data/tieba_personal/logs/op.log &";
        exec($strCmd);
        $strLog = "file[" . $filename . "], run_limit[" . $intRunLimit . "], run_count[" . $intRunCount . "] has been running! cmd[" . $strCmd . "]";
        Util_Base::_log($strLog, __LINE__);

        sleep(2);
    }
}


$strLog = "control running!";
Util_Base::_log($strLog, __LINE__);
