<?php
/**
 * @file Base.php
 * <AUTHOR>
 * @date 2019/05/06 10:23:20
 * @brief
 **/

class Util_Base {

    /**
     * 规范化返回结果
     * @param
     * @return
     */
    public static function errRet($errno, $data = array()) {
        $arrRet = array(
            'errno'     => $errno,
            'errmsg'    => Tieba_Error::getErrmsg($errno),
        );
        if (isset($data)) {
            $arrRet['data'] = $data;
        }
        return $arrRet;
    }
}
