<?php
class Service_Caster_Zan {

	const REDIS_NAME = 'tbscore';
	const PRIFEX_USER_TOTAL_ZAN_RANK = 'user_total_zan_rank_';
	const PRIFEX_POS_DAILY_RANK      = 'position_daily_rank_';
	const PRIFEX_POS_TOTAL_ZAN       = 'position_total_zan_';

	/**
	 * @brief 点赞的service接口
	 * @param post_id[int]
	 * @param user_id[int]
	 * @return array
	 */
	public static function userZan($arrInput) {
		$postId = $arrInput['post_id'];
		$userId = $arrInput['user_id'];
        $userIp = $arrInput['user_ip'];
		try {
			/**
			 * 获取贴子扩展属性
			 */
            $postInfo = Service_Caster_Post::getPostAttr($postId);
            $caster_attr = $postInfo[Define_Caster::POST_ATTR_NAME];

            /**
             * 过期的帖子不能点赞了
             */
            if ($caster_attr['source']['time'] + Define_Caster::CASTER_DURATION < time()) {
                /**
                 * 复用ueg的错误号
                 */
                throw new Exception("post time out, can't zan any more", 65534);
            }

			/**
			 * ueg策略
			 */
			Service_Caster_Ueg::canAddZan($userId, $userIp);
			/**
			 * 加赞数
			 */
			$param = array(
				'user_id'  => $caster_attr['source']['user_id'],
				'forum_id' => $caster_attr['source']['forum_id'],
				'position' => $caster_attr['source']['position'],
                'date'     => $caster_attr['source']['time'],
			);
			$ret = self::addZan($param);
			if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				throw new Exception("add zan failed", $ret['errno']);
			}
            /**
             * 点赞debug日志
             */
            Bingo_Log::warning("[USERZAN] {$param['user_id']}-{$userId}-{$userIp}-{$postId}");
			$errno = Tieba_Errcode::ERR_SUCCESS;
            $error = 'success';
		}catch(Exception $e) {
			$errno = $e->getCode();
			$error = $e->getMessage();
			Bingo_Log::warning("user add zan failed. errno[$errno] error[$error]");
		}
        if ($errno === Tieba_Errcode::ERR_SUCCESS) {
            /**
             * 成功赞，提交ueg
             */
            /**
             * 解决跨机房ueg跨机房问题，前面query直接提交
             */
            //Service_Caster_Ueg::submitZan2Ueg($arrInput);
        }
		return array(
			'errno' => $errno,
            'error' => $error,
		);
	}

	/**
	 * @brief 用户点赞接口
	 * @param $arrInput 扩展属性
	 * @return array
	 */
	public static function addZan($arrInput) {

		if(Tieba_Errcode::ERR_SUCCESS !== self::_addZanToRank($arrInput)) {
			return array(
				'errno' => Tieba_Errcode::ERR_REDIS_CALL_FAIL,
			);
		}
		if(Tieba_Errcode::ERR_SUCCESS !== self::_addZanforEachPosition($arrInput)) {
			return array(
				'errno' => Tieba_Errcode::ERR_REDIS_CALL_FAIL,
			);
		}
		return array(
			'errno' => Tieba_Errcode::ERR_SUCCESS,
		);
	}

	/**
	 * @brief 参与用户的总排行榜为该用户加1，即对参与用户的总排行版加1
	 * @brief 工会的日赞数排行榜加1，即该工会今日该用户的赞数加1
	 * @brief 以上两种操作均采用sorted sets结构，进行批量操作
	 * @param $arrInput
	 * @return
	 */
	private static function _addZanToRank($arrInput){

		$forumId  = $arrInput['forum_id'];
		$userId   = $arrInput['user_id'];
		$position = $arrInput['position'];
		$date     = date('20ymd', $arrInput['date']);
		$userTotalRankKey = self::PRIFEX_USER_TOTAL_ZAN_RANK.$forumId;
		$positionRankKey  = self::PRIFEX_POS_DAILY_RANK.$forumId.'_'.$position.'_'.$date;
		$arrParams = array(
			'reqs' => array(
				0 => array(
					'key'    => $userTotalRankKey,
					'member' => $userId,
					'step'   => 1,
				),
				1 => array(
					'key'    => $positionRankKey,
					'member' => $userId,
					'step'   => 1,
				),
			),
		);
		$res = Util_Redis::redisQuery(self::REDIS_NAME, 'ZINCRBY', $arrParams);
		if((false === $res) || (Tieba_Errcode::ERR_SUCCESS !== $res['err_no'])) {
			Bingo_Log::warning('call redis error!.[input='.serialize($arrInput).'][output='.serialize($res).']');
			return  Tieba_Errcode::ERR_FAIL_TALK_WITH_REDIS;
		}
		return Tieba_Errcode::ERR_SUCCESS;
	}

	/**
	 * @brief  工会的每天点赞数加1
	 * @param  forum_id : 吧id
	 * @param  postion  : 工会标识
	 * @param  postion  : 日期
	 * @return array
	 */
	private static function _addZanforEachPosition($arrInput) {

		$forumId  = $arrInput['forum_id'];
		$position = $arrInput['position'];
		$date     = date('20ymd', $arrInput['date']);

		$arrParams = array(
			'key'  => self::PRIFEX_POS_TOTAL_ZAN.$forumId.'_'.$position.'_'.$date,
			'step' => 1,
		);

		$res = Util_Redis::redisQuery(self::REDIS_NAME, 'INCRBY', $arrParams);
		if((false === $res) || (Tieba_Errcode::ERR_SUCCESS !== $res['err_no'])) {
			Bingo_Log::warning('call redis error!.[input='.serialize($arrInput).'][output='.serialize($res).']');
			return Tieba_Errcode::ERR_REDIS_CALL_FAIL;
		}
		return Tieba_Errcode::ERR_SUCCESS;
	}

	/**
	 * @brief  获取工会的日排行榜(date传时间戳即可)
	 * @param  $arrInput forum_id + type + postion
	 * @return array
	 */
	public static function getDailyRankEachPosition($arrInput) {
		$forum_id = $arrInput['forum_id'];
		$position = $arrInput['position'];
		$top      = $arrInput['top'];
		$date     = $arrInput['dates'];

		// 可批量获取榜单
		$arrReqs = array();
		foreach($date as $eachDate) {
			$arrParamItem = array(
				'key'   => self::PRIFEX_POS_DAILY_RANK.$forum_id.'_'.$position.'_'.date('20ymd', $eachDate),
				'start' => 0,
				'stop'  => $top,
			);
			array_push($arrReqs, $arrParamItem);
		}
		$arrParams = array(
			'reqs' => $arrReqs,
		);

		$res = Util_Redis::redisQuery(self::REDIS_NAME, 'ZREVRANGEWITHSCORES', $arrParams);
		if((false === $res) || (Tieba_Errcode::ERR_SUCCESS !== $res['err_no'])) {
			Bingo_Log::warning('call redis error!.[input='.serialize($arrInput).'][output='.serialize($res).']');
			return array(
				Tieba_Errcode::ERR_REDIS_CALL_FAIL,
			);
		}
		$data = array();
		foreach($date as $eachDate) {
			$data[$eachDate] = $res['ret'][self::PRIFEX_POS_DAILY_RANK.$forum_id.'_'.$position.'_'.date('20ymd', $eachDate)];
		}
		return array(
			'errno' => Tieba_Errcode::ERR_SUCCESS,
			'data'  => $data,
		);
	}

	/**
	 * @brief 获取用户赞排行榜
	 * @param $arrInput forum_id + user_id
	 * @return array
	 */
	public static function getUserTotalZanRank($arrInput){
		$forum_id = $arrInput['forum_id'];
		$top      = $arrInput['top'];
		$arrParams = array(
			'key'   => self::PRIFEX_USER_TOTAL_ZAN_RANK.$forum_id,
			'start' => 0,
			'stop'  => $top,
		);
		$res = Util_Redis::redisQuery(self::REDIS_NAME, 'ZREVRANGEWITHSCORES', $arrParams);
		if((false == $res) || (Tieba_Errcode::ERR_SUCCESS !== $res['err_no'])) {
			Bingo_Log::warning('call redis error!.[input='.serialize($arrInput).'][output='.serialize($res).']');
			return Tieba_Errcode::ERR_REDIS_CALL_FAIL;
		}
		return array(
			'errno' => Tieba_Errcode::ERR_SUCCESS,
			'data'  => $res['ret'][self::PRIFEX_USER_TOTAL_ZAN_RANK.$forum_id],
		);
	}

	/**
	 * @brief 获取用户特定日期的赞数（对用户可传递数组，进行批量操作）
	 * @param $arrInput date为时间戳
	 * @return array 若key所对应的member值不存在data字段0
	 */
	public static function getUserZanOfTheday($arrInput) {
		$forum_id  = $arrInput['forum_id'];
		$position  = $arrInput['position'];
		$date      = date('20ymd', intval($arrInput['date']));
		$user_ids  = $arrInput['user_id'];

		$arrParamItem = array();

		foreach($user_ids as $userId) {
			array_push($arrParamItem, array(
				'key'    => self::PRIFEX_POS_DAILY_RANK.$forum_id.'_'.$position.'_'.$date,
				'member' => $userId,
			));
		}

		$arrParams = array(
			'reqs' => $arrParamItem,
		);

		$res = Util_Redis::redisQuery(self::REDIS_NAME, 'ZSCORE', $arrParams);
		if((false === $res) || (Tieba_Errcode::ERR_SUCCESS !== $res['err_no'])) {
			Bingo_Log::warning('call redis error!.[input='.serialize($arrInput).'][output='.serialize($res).']');
			return array(
				'errno' => Tieba_Errcode::ERR_REDIS_CALL_FAIL,
			);
		}
		if (count($user_ids) > 1) {
			return array(
				'errno' => $res['err_no'],
				'data'  => $res['ret'][self::PRIFEX_POS_DAILY_RANK.$forum_id.'_'.$position.'_'.$date],
			);
		}
		else {
			return array(
				'errno' => $res['err_no'],
				'data'  => array(
					"$user_ids[0]" => $res['ret'][self::PRIFEX_POS_DAILY_RANK.$forum_id.'_'.$position.'_'.$date],
				),
			);
		}
	}

	/**
	 * @brief 获取工会每天的总赞数
	 * @param $arrInput
	 * @return array
	 */
	public static function getDailyTotalZan($arrInput) {
		$forumId  = $arrInput['forum_id'];
		$position = $arrInput['position'];
		$date     = date('20ymd', $arrInput['date']);
		$arrParams = array();
		foreach ($position as $eachPos) {
			array_push($arrParams, array(
				'key'  => self::PRIFEX_POS_TOTAL_ZAN.$forumId.'_'.$eachPos.'_'.$date,
			));
		}
		$arrParams = array(
			'reqs' => $arrParams,
		);

		$res = Util_Redis::redisQuery(self::REDIS_NAME, 'GET', $arrParams);
		if((false === $res) || (Tieba_Errcode::ERR_SUCCESS !== $res['err_no'])) {
			Bingo_Log::warning('call redis error!.[input='.serialize($arrInput).'][output='.serialize($res).']');
			return array(
				'errno' => Tieba_Errcode::ERR_REDIS_CALL_FAIL,
			);
		}

		$data = array();
		foreach ($position as $eachPos) {
			$data[$eachPos] = $res['ret'][self::PRIFEX_POS_TOTAL_ZAN.$forumId.'_'.$eachPos.'_'.$date];
		}
		return array(
			'errno' => $res['err_no'],
			'data'  => $data,
		);
	}
}

