<?php
/**
 * Created by PhpStorm.
 * User: wangchao25
 * Date: 2014/12/18
 * Time: 16:45
 */

if ($argc < 5) {
    echo "Usage: php reduceThirdPool.php uid tdou_num scene_id auth_key";
}

$uid      = $argv[1];
$tdou_num = $argv[2];
$scene_id = $argv[3];
$auth_key = $argv[4];

$time = time();
$open_id = Tieba_Openapi::api_encode_uid($uid);
$order_id = Tbmall_Open_Order::genPropsOrderId($uid,$scene_id);
$md5str = "open_id=$open_id&order_id=$order_id&scene_id=$scene_id&tb_timestamp=$time&tdou_num=$tdou_num&$auth_key";
$arrInput = array(
    'user_id' => $uid,
    'open_id' => $open_id,
    'tdou_num' => $tdou_num,   //扣减用户T豆数
    'scene_id' => $scene_id,   // 业务ID
    'appid'    => 100000,      // 内部使用写 100000
    'props_name' => 'christmas',    //业务名称
    'order_id' => $order_id,              //订单ID ： 基础库生成  Tbmall_Open_Order::genPropsOrderId($user_id,$scene_id)
    'tb_timestamp' => $time,          //时间戳 10位unix时间戳，精确到秒
    //'tb_sign' => md5("scene_id=$scene_id&tb_timestamp=$time&tdou_num=$score&user_id=$uid&$auth_key"),               //签名  MD5（scene_id=$scene_id&tb_timestamp=$tb_timestamp&tdou_num=$tdou_num&user_id=$user_id&$auth_key） $auth_key:分配scene_id时附带
    'tb_sign' => md5($md5str),
);

//open_id=3791936980&order_id=2000089379193698053017062941418728868&scene_id=2000089&tb_timestamp=1418728868&tdou_num=100&66a35469dbe0c0ea

$ret = Tieba_Service::call('tbmall', 'addScoresFromPool', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');

var_export($ret);
