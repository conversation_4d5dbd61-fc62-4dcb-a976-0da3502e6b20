<?php
/**
 * @abstract 用户反馈中心配置
 * <AUTHOR>
 * @copyright
 * @date 2015-4-9
 */

class Util_Conf {
	
	//反馈来源，0-pc反馈表单，1-客户端
	const FB_SOURCE_PC = 0;
	CONST FB_SOURCE_CLIENT = 1;
	
	//反馈类型，1-功能失灵，2-账号问题，3-违规内容，4-体验反馈，5-会员服务，6-付费业务，255-其他
	CONST FB_TYPE_FUNCTION = 1;
	CONST FB_TYPE_ACCOUNT = 2;
	CONST FB_TYPE_ILLEGAL = 3;
	CONST FB_TYPE_UE = 4;
	CONST FB_TYPE_MEMBER = 5;
	CONST FB_TYPE_PAY = 6;
	CONST FB_TYPE_ELSE = 255;
	
	//问题类型，1-封禁，2-盗号
	CONST QUES_TYPE_BLOCK = 1;
	CONST QUES_TYPE_HACK = 2;
	//客户端问题类型
	const CLIENT_QUES_TYPE_LOAD_SLOW = 101;
	CONST CLIENT_QUES_TYPE_QUIT = 102;
	CONST CLIENT_QUES_TYPE_IM_CANNOT_USE = 103;
	CONST CLIENT_QUES_TYPE_IM_FAILED = 104;
	CONST CLIENT_QUES_TYPE_COST_POWER = 105;
	CONST CLIENT_QUES_TYPE_SOME_WRONG = 106;
	CONST CLIENT_QUES_TYPE_USE_SLOW = 107;
	CONST CLIENT_QUES_TYPE_ELSE = 108;
	
	const MAX_PICS = 5;
	const MAX_PIC_URL_LENTH = 1024;
	const MAX_CONTENT_LENTH = 1000;
	
	const REPLY_TYPE_USER = 0;
	CONST REPLY_TYPE_OP = 1;
	
	const FB_OP_STATUS_UNDO = 0;
	CONST FB_OP_STATUS_DOING = 1;
	CONST FB_OP_STATUS_DONE = 255;
	
	//出现频率，1-偶尔，2-必现
	const OCCUR_SOMETIME = 1;
	CONST OCCUR_EVERYTIME = 2;
	
	
	public static $feedbackCmd = 'feedback';
	
	public static $occur = array(
		self::OCCUR_SOMETIME => '否',
		self::OCCUR_EVERYTIME => '是',
	);
	public static $fbType = array(
		'func'    => self::FB_TYPE_FUNCTION,
		'account' => self::FB_TYPE_ACCOUNT,
		'illegal' => self::FB_TYPE_ILLEGAL,
		'ue'      => self::FB_TYPE_UE,
		'mem'	  => self::FB_TYPE_MEMBER,
		'pay'	  => self::FB_TYPE_PAY,
		'other'	  => self::FB_TYPE_ELSE,
	);
	
	public static $fbTypeToChs = array(
		self::FB_TYPE_FUNCTION => '功能失灵',
		self::FB_TYPE_ACCOUNT => '账号问题',
		self::FB_TYPE_ILLEGAL => '违规内容',
		self::FB_TYPE_UE => '体验反馈',
		self::FB_TYPE_MEMBER => '会员服务',
		self::FB_TYPE_PAY => '付费业务',
		self::FB_TYPE_ELSE => '其它',
	);
	
	public static $quesTypeToChs = array(
		self::QUES_TYPE_BLOCK => '封禁',
		self::QUES_TYPE_HACK => '盗号',
		self::CLIENT_QUES_TYPE_LOAD_SLOW => '使用慢，一直加载',
		self::CLIENT_QUES_TYPE_QUIT => '闪退',
		self::CLIENT_QUES_TYPE_IM_CANNOT_USE => '私聊群聊不能用',
		self::CLIENT_QUES_TYPE_IM_FAILED => '私聊群聊遇到异常',
		self::CLIENT_QUES_TYPE_COST_POWER => '使用很耗电',
		self::CLIENT_QUES_TYPE_SOME_WRONG => '部分功能无法使用',
		self::CLIENT_QUES_TYPE_USE_SLOW => '不流畅，反应慢',
		self::CLIENT_QUES_TYPE_ELSE => '其他',
	);
	
	public static $quesType = array(
		'block' => self::QUES_TYPE_BLOCK,
		'hack'  => self::QUES_TYPE_HACK,
	);
	
	public static $checkParam = array(
		self::FB_TYPE_FUNCTION => array(
			'occur_site',
			'occur_time',
		),
		self::FB_TYPE_ACCOUNT => array(
			'ques_type',
			'fb_uname',
			'last_action',
		),
		self::FB_TYPE_MEMBER => array(
			'occur_time',
		),
		self::FB_TYPE_PAY => array(
			'occur_time',
		),
	);
	
	public static $occurSite = array(
		self::FB_SOURCE_PC => array(
			'贴吧首页',
			'贴子列表页',
			'贴子详情页',
			'i贴吧',
			'私信页',
			'个人中心',
			'吧务后台',
			'服务中心',
			'其它',
		),
		self::FB_SOURCE_CLIENT => array(
			'贴子列表页',
			'贴子详情页',
			'看贴功能',
			'个人中心',
			'私聊',
			'群聊',
			'其它',
		),
	);
	
}