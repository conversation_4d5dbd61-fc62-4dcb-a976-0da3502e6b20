<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2015-06-25
 *        
 */
class commitReplyAction extends Util_BaseAction {
	
	public function init() {
		if (! parent::init ()) {
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_UNKOWN;
			$this->_renderJson ();
			return false;
		}
		return true;
	}
	
	protected function _process() {
		if (! $this->_bolUserLogin) {
			Bingo_Log::warning ( 'the use do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
			$this->_renderJson ();
			return false;
		}
		$tbs = Bingo_Http_Request::get ( 'tbs', '' );
		if (! Tieba_Tbs::check ( $tbs, true )) {
			Bingo_Log::warning (  __CLASS__ . " tbs check fail.".$this->_intUid );
			$this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
			$this->_renderJson();
			return false;
		}
		$fbId = (int)Bingo_Http_Request::get ( "fb_id",0 );
		$replyContent = Bingo_Http_Request::get ( "reply_content",'' );
		$replyContent = Bingo_String::xssDecode($replyContent);
		
		
		$arrInput = array(
			'user_id' => $this->_intUid,
			'fb_id' => $fbId,
			'reply_content' => $replyContent,
			'reply_uid' => $this->_intUid,
			'create_time' => time(),
			'reply_type' => Util_Conf::REPLY_TYPE_USER,
		);
		$acts = Util_Anti::actsctrlQueryCommon($this->_intUid, Util_Conf::$feedbackCmd, 'pmc');
		if(false === $acts){
			Bingo_Log::warning("Util_Anti::actsctrlQueryCommon failed:".serialize($arrInput));
		}
		if(isset ($acts['errno']) && $acts['errno'] > 0){//�ﵽ��������
			Bingo_Log::notice( __FUNCTION__ . ": the user hit Acts_Ctrl , userid=" . $this->_intUid . " output =".serialize($acts));
			$this->_intErrorNo = Util_Errno::$hitFeedbackActctrl;
			$this->_renderJson ();
			return false;
		}
		
		$arrParams = $this->_checkParams($arrInput);
		if (!is_array($arrParams)) {		
			$this->_intErrorNo = $arrParams;
			$this->_renderJson ();
			return false;
		}
//		$checkInput = array(
//			'user_id' => $this->_intUid,
//			'fb_id' => $fbId,
//		);
//		$checkFdStatus = Service_Feedback_Feedback::checkFbStatus($checkInput);
//		if(false === $checkFdStatus){
//			$this->_intErrorNo = Util_Errno::$feedbackIsDone;
//			$this->_renderJson ();
//			return false;
//		}
		$srvRet = Service_Hermes::addReply($arrParams);
		if($srvRet ['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			$this->_intErrorNo = Util_Errno::$commitFeedbackFailed;
			$this->_renderJson ();
			return false;
		}
		$resActs = Util_Anti::actsctrlSubmitCommon($this->_intUid, Util_Conf::$feedbackCmd, 'pmc');
		Bingo_Log::notice("userid=" . $this->_intUid . " output =".serialize($resActs));
		$this->_intErrorNo = Tieba_Errcode::ERR_SUCCESS;
		$this->_renderJson ();
		return true;
	}

	protected function _checkParams($arrInput) {
		if(empty($arrInput['reply_content']) || empty($arrInput['fb_id'])){
			Bingo_Log::warning ( "fb_desc or fb_id is empty:".serialize($arrInput) );
			return Tieba_Errcode::ERR_PARAM_ERROR;
		}
		//ui�õ�����gbk����
		//$arrInput = Bingo_Encode::convert ( $arrInput, Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK );
		if(mb_strlen($arrInput['reply_content'],'UTF8') > Util_Conf::MAX_CONTENT_LENTH){
			Bingo_Log::warning ( "reply_content length is overflow:".serialize($arrInput) );
			return Util_Errno::$contentLenthOverflow;
		}
		return $arrInput;
	}
}
?>