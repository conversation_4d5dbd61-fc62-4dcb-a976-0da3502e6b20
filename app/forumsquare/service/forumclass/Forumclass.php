<?php

class Service_Forumclass_Forumclass
{


    /**
     * @param $errno
     * @return array
     */
    protected static function errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @param $ret
     * @return array
     */
    protected static function succRet($ret)
    {
        $errno = Tieba_Errcode::ERR_SUCCESS;
        $retArr = self::errRet($errno);
        $retArr['ret'] = $ret;
        return $retArr;
    }

    /**
     * 增加吧单
     * @param $arrInput
    $arrInput = array(
     * 'title' => 'test', //必须
     * 'image_url' => 'url', //必须
     * 'op_uid' => 1234 , //必须
     * 'abstract' => '',
     * 'class_type' => '',
     * 'class_date' => '',
     * 'class_rank' => '',
     * 'op_status' => '',
     * 'op_name' => '',
     * 'publish_time' => '', //发布时间
     * 'extra' => '',
     * );
     * @return array|mixed
     */
    public static function addForumClass($arrInput)
    {
        return Dl_Forumclass_Forumclass::addForumClass($arrInput);
    }

    /**
     * 删除吧单
     * @param $arrInput
     *      $arrInput = array(
     *      'forum_class_id' => 1, //必填
     *      );
     * @return array|mixed
     */
    public static function deleteForumClass($arrInput)
    {
        $ret = Service_Editor_Editor::deleteEditorRecommend($arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning("Failed to deleteEditorRecommend");
            return self::errRet($ret['errno']);
        }
        return Dl_Forumclass_Forumclass::deleteForumClass($arrInput);
    }

    /**
     * 更改吧单
     * @param $arrInput
    $arrInput = array(
     * 'forum_class_id' => 1, //必填，其他选填
     * 'title',
     * 'abstract',
     * 'image_url',
     * 'class_type',
     * 'class_date',
     * 'class_rank',
     * 'op_status',
     * 'op_time',
     * 'op_uid',
     * 'op_name',
     * 'publish_time',
     * 'extra',
     * );
     * @return array|mixed
     */
    public static function updateForumClass($arrInput)
    {
        return Dl_Forumclass_Forumclass::updateForumClass($arrInput);
    }

    /**
     * 获取吧单
     * @param $arrInput
     *         $arrInput = array(
     *         'limit' => 10, //一共取多少条
     *         'offset' => 0,
     *         'orderby' => 'op_time',//排序字段
     *         'desc'=> 1, // 是否逆序，只有传了orderby的时候才有用
     *         'class_type' => 1, //吧单类型 0: 普通榜单, 1: 本周热榜, 2: 新榜'
     *         'op_status' => 1, //状态 '吧单状态，0： 线下, 1： 待发布, 255：线上',
     *         );
     * @return array|mixed
     */
    public static function getForumClass($arrInput)
    {
        $mayOrderFields = array(
            'op_time',
            'class_rank',
        );
        if (isset($arrInput['orderby'])) {
            if (!in_array($arrInput['orderby'], $mayOrderFields)) {
                unset($arrInput['orderby']);
            }
        }

        return Dl_Forumclass_Forumclass::getForumClassList($arrInput);
    }


    /**
     * @param array $arrInput
     * @return array|mixed
     */
    public static function getWaitingList($arrInput = array())
    {
        return Dl_Forumclass_Forumclass::getWaitingList($arrInput);
    }


    /**
     * 发布一个吧单
     * @param $arrInput
     *      $arrInput = array(
     *      'forum_class_id' => 123, //吧单ID, 必选
     *      'op_uid' => 1234, //操作人ID, 必选
     *      'op_name' => "abc", // 操作人的name 可选
     *      );
     * @return array|mixed
     */
    public static function publishForumClass($arrInput)
    {
        if (!isset($arrInput['op_uid']) || !isset($arrInput['forum_class_id'])
            || empty($arrInput['op_uid']) || !isset($arrInput['forum_class_id'])
        ) {
            Bingo_Log::warning("param op_uid and forum_class_id is needed." . __FUNCTION__);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $dlInput = array(
            'forum_class_id' => $arrInput['forum_class_id'],
            'op_uid' => $arrInput['op_uid'],
            'op_status' => Util_Const::FORUM_CLASS_STATUS_ONLINE,
            'publish_time' => time(),
            'op_time' => time(),
        );
        if (isset($arrInput['op_name'])) {
            $dlInput['op_name'] = $arrInput['op_name'];
        }
        return Dl_Forumclass_Forumclass::updateForumClass($dlInput);
    }


    /**
     * 下线一个吧单
     * @param $arrInput
     * 入参同publishForumClass
     * @return array|mixed
     */
    public static function unPublishForumClass($arrInput)
    {
        if (!isset($arrInput['op_uid']) || !isset($arrInput['forum_class_id'])
            || empty($arrInput['op_uid']) || !isset($arrInput['forum_class_id'])
        ) {
            Bingo_Log::warning("param op_uid and forum_class_id is needed." . __FUNCTION__);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $dlInput = array(
            'forum_class_id' => $arrInput['forum_class_id'],
            'op_uid' => $arrInput['op_uid'],
            'op_status' => Util_Const::FORUM_CLASS_STATUS_OFFLINE,
            'publish_time' => 0,
            'op_time' => time(),
        );
        if (isset($arrInput['op_name'])) {
            $dlInput['op_name'] = $arrInput['op_name'];
        }
        $ret = Service_Editor_Editor::unPublishEditorRecommend($arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning("Failed to unPublishEditorRecommend");
            return self::errRet($ret['errno']);
        }
        return Dl_Forumclass_Forumclass::updateForumClass($dlInput);
    }

    /**
     * 获取一个吧单的内容
     * @param $arrInput
     * @return array|mixed
     */
    public static function getOneForumClass($arrInput)
    {
        if (!isset($arrInput['forum_class_id']) || empty($arrInput['forum_class_id'])) {
            Bingo_Log::warning("forum_class_id is needed.");
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $ret = Dl_Forumclass_Forumclass::getOneForumClass($arrInput);
        if(false === $ret || Tieba_Errcode::ERR_SUCCESS !== $ret['errno'] ) {
            Bingo_Log::warning("failed to call dl getOneForumClass");
            return self::errRet(Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL);
        }
        if($ret['ret'][0]['extra']) {
            $extra = $ret['ret'][0]['extra'];
            $extra = unserialize($extra);
            if(is_array($extra)) {
                $ret['ret'][0] = array_merge($ret['ret'][0], $extra);
            }
        }
        unset($ret['ret'][0]['extra']);
        return self::succRet($ret['ret']);
    }

    /**
     * 根据类型获取forum_class_id，type : 1 热榜 2 新榜
     * @param $arrInput
     * @return array
     */
    public static function getForumClassIdByType($arrInput)
    {
        if (!isset($arrInput['type']) || empty($arrInput['type'])) {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        return Dl_Forumclass_Forumclass::getForumClassIdByType($arrInput);
    }


    /**
     * 根据类型获取最近创建的吧单forum_class_id，type : 1 热榜 2 新榜
     * @param $arrInput
     * @return array|mixed
     */
    public static function getNewestForumClassByType($arrInput)
    {
        if (!isset($arrInput['type']) || empty($arrInput['type'])) {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        return Dl_Forumclass_Forumclass::getNewestForumClassByType($arrInput);
    }

    /**
     * 获取自动更新的开关状态
     * @param array $arrInput
     * @return array|mixed
     */
    public static function getSwitch($arrInput = array())
    {
        if (!$arrInput['id']) {
            $arrInput['id'] = Util_Const::SWITCH_ID;
        }
        return Dl_Forumclass_Forumclass::getSwitch($arrInput);
    }

    /**
     * 改变开关的状态
     * @param $arrInput
     * @return array|mixed
     */
    public static function changeSwitchStatus($arrInput)
    {
        if (!$arrInput['id']) {
            $arrInput['id'] = Util_Const::SWITCH_ID;
        }
        return Dl_Forumclass_Forumclass::changeSwitchStatus($arrInput);
    }

    /**
     * 添加一个开关
     * @param $arrInput
     * @return array|mixed
     */
    public static function addOneSwitch($arrInput = array())
    {
        return Dl_Forumclass_Forumclass::addOneSwitch();
    }
}
