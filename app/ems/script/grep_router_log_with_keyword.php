<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file grep_php_log_with_field.php
 * <AUTHOR>
 * @date 2014/07/22 11:41:39
 * @brief 
 *  
 **/


require_once "/home/<USER>/ems/script/script_conf.php";
require_once "/home/<USER>/ems/script/script_db.php";
require_once "/home/<USER>/ems/script/script_lib.php";

$task_id = $_SERVER['argv'][1];
//$type = $_SERVER['argv'][2];
$query_info = rawurldecode($_SERVER['argv'][3]);
$query_info = rawurldecode($query_info);
$starttime = time();

$query_info = json_decode($query_info, BINGO_ENCODE_LANG);
var_dump("lineno:".__LINE__.". ");
var_dump($query_info);

$is_conn = script_db::connDB();
if(!$is_conn)
{
    var_dump("lineno:".__LINE__.". oh! no! connect mysql fail!");
    exit;
}
//update task status to executing
$sql_str = "update ems_task_info set status=2, start_time=$starttime where task_id=$task_id and status=1";
$res = script_db::update($sql_str);
if(false == $res)
{
    var_dump("lineno:".__LINE__.". oh! no! update mysql fail!");
    script_db::closeDB();
    exit;
}
if($res <= 0)
{
    var_dump("lineno:".__LINE__.". status of task_id[$task_id] is not 'waiting'!");
    script_db::closeDB();
    exit;
}

$sql_str = "delete from ems_log_detail where task_id=$task_id";
script_db::delete($sql_str);

$conditions = array();
foreach($query_info as $cond)
{
    $k = $cond[0];
    $conditions[$k] = $cond[1];
}
$keyword = $conditions['keyword'];
$router_type = "router";
if(isset($conditions['type']) && in_array($conditions['type'],array('router', 'inrouter')))
{
    $router_type = $conditions['type'];
}
$stime = strtotime($conditions['@@stime']);
$stime_h = date('d/M/Y:H', $stime);
$stime_i = intval(date('i', $stime)/10);//取小时的十位数
$etime = strtotime($conditions['@@etime']);
$etime_h = date('d/M/Y:H', $etime);
$etime_i = intval(date('i', $etime)/10);
if($stime_h != $etime_h || $stime_i > $etime_i)//just for the same hour
{
    var_dump("lineno:".__LINE__.". oh! no! just for the same hour!");
    updateTaskStatus($task_id, 2, 4, "o! shit! the time period which you provide is error! just for the same hour");
    script_db::closeDB();
    exit;
}
$log_files = array();
$orcp_log_files = array();

$path=script_conf::getLogInfoByType($router_type);
$orcp_router_access_log_file = $path['orcp_notice'];
$log_file_pre = $path['orp_notice'];

//$orcp_router_access_log_file = script_conf::$orpc_log_files['router_notice'];
//$tmptime = $etime;
//$nowtime = date('YmdH');
//$log_file_pre = script_conf::$orp_log_files['router_notice'];
//$stime_tmp = strtotime(date('Y-m-d H:00:00', $stime));
$cur_time = time();
$int_nowtime = $cur_time - $cur_time%(10*60);
$nowtime = date('YmdHi', $int_nowtime);
for($a=$stime; $a<$etime; $a+=10*60)
{
    var_dump($a);
    $int_time = $a - $a%(10*60);
    var_dump($int_time);
    $time_suffix = date('YmdHi', $int_time);
    var_dump($time_suffix);
    if($time_suffix == $nowtime)
    {
        $log_files[] = $log_file_pre;
        $orcp_log_files[] = $orcp_router_access_log_file;
        continue;
    }
    $log_files[] = $log_file_pre.".$time_suffix";
    $orcp_log_files[] = $orcp_router_access_log_file.".$time_suffix";
}
var_dump($log_files);
var_dump($orcp_log_files);
//while($tmptime >= $stime_tmp)
//{
//    $time_suffix = date('YmdH', $tmptime);
//    $tmptime -= 3600;
//    if($time_suffix == $nowtime)
//    {
//        $log_files[] = $log_file_pre;
//        $log_files[] = $log_file_pre.".$time_suffix*";//TODO
//        continue;
//    }
//    $log_files[] = $log_file_pre.".$time_suffix*";//TODO
//}
if(empty($log_files))
{
    var_dump("lineno:".__LINE__.". oh! no! log_files is empty!");
    updateTaskStatus($task_id, 2, 4, "o! shit! the time period which you provide is error");
    script_db::closeDB();
    exit;
}
$grep_other = " | grep '$stime_h:[$stime_i-$etime_i]' ";
$log_files = array_slice($log_files, 0, 6);
$files_str = implode(' ', $log_files);
$orcp_log_str = implode(' ', $orcp_log_files);
//$get_log_cmd = "/usr/bin/get_instance_by_service -a router.orp.all | awk -F' ' '{print $1}' | /usr/local/otools/bin/pdo2 -y -q -t 5m -r 40 \"grep '$keyword' $files_str a $grep_other | head -n 20 \"";
$keyword = str_replace('"', '\\"', $keyword);
$get_log_cmd = "baas login --baas_user=wangqiang21 --baas_role=baas_all_privilege --baas_group=baas_default_group && a=`get_instance_by_service -a router-tieba.orp.jx;get_instance_by_service -a router-tieba.orp.nj;get_instance_by_service -a router-tieba.orp.nj03;`;echo \"\$a\" | awk -F' ' '{print $1}' | /usr/local/otools/bin/pdo2 -y -q -t 5m -r 40 \"grep '$keyword' $files_str a $grep_other | head -n 20 \" && a=`get_instance_by_service router-tieba.orp.tc -a;get_instance_by_service  router-tieba.orp.gz -a;`;echo \"\$a\" | awk '{print \$1\" \"\$9}'| /usr/local/otools/bin/pdo2 -y -q -t 5m -r 40 \"grep '$keyword' $orcp_log_str a $grep_other | head -n 20 \"";

var_dump("lineno:".__LINE__.". $get_log_cmd");
$result = exec($get_log_cmd, $output, $return);
if(0 != $return)
{
    var_dump("lineno:".__LINE__.". oh! no! execute get_log_cmd fail!");
    //update task status to failed
    updateTaskStatus($task_id, 2, 4, "o! shit! fail to exec cmd of getting log!errno[$return]");
    script_db::closeDB();
    exit;
}

//var_dump($result);
//var_dump($output);
//var_dump($return);

$log_info_arr = array();
$count = 0;
foreach($output as $log_msg)
{
    if($count >=200)
    {
        break;
    }

    $extra = array(
        'type'=>$router_type,
    );
    $log_info = analyseLog($log_msg, $extra);
    if(empty($log_info))
    {
        continue;
    }
    $log_info_arr[] = $log_info;
    $count ++;
}
$log_count = count($log_info_arr);

$log_count_tmp = 0;
if($log_count >=20)
{
    $get_log_count_cmd = "/usr/bin/get_instance_by_service -a router.orp.all | awk -F' ' '{print $1}' | /usr/local/otools/bin/pdo2 -y -q -t 5m -show row -r 40 \"grep '$keyword' $files_str a $grep_other | wc -l \"";
    $get_log_count_cmd = "baas login --baas_user=wangqiang21 --baas_role=baas_all_privilege --baas_group=baas_default_group && a=`get_instance_by_service -a router-tieba.orp.jx;get_instance_by_service -a router-tieba.orp.nj;get_instance_by_service -a router-tieba.orp.nj03;`;echo \"\$a\" | awk -F' ' '{print $1}' | /usr/local/otools/bin/pdo2 -y -q -t 5m -show row -r 40 \"grep '$keyword' $files_str a $grep_other | wc -l \" && a=`get_instance_by_service router-tieba.orp.tc -a;get_instance_by_service  router-tieba.orp.gz -a;`;echo \"\$a\" | awk '{print \$1\" \"\$9}'| /usr/local/otools/bin/pdo2 -y -q -t 5m -show row -r 40 \"grep '$keyword' $orcp_log_str a $grep_other | wc -l \"";

    var_dump("lineno:".__LINE__.". $get_log_count_cmd");
    $result = exec($get_log_count_cmd, $count_output, $return);
    if(0 != $return)
    {
        var_dump("lineno:".__LINE__.". oh! no! execute get_log_count_cmd fail!");
        $log_count_tmp = $log_count;
    }
    if(0 == $return)
    {
        foreach($count_output as $item)
        {
            $arr_item = explode('>>', $item);
            $log_count_tmp += intval($arr_item[2]);
        }
    }
}

$log_count = $log_count_tmp;
$task_status = 3;
$task_errmsg = "";
if(empty($log_info_arr))
{
    $task_status = 5;
    $task_errmsg = "no log!";
}

$out = saveLog($log_info_arr, $task_id);
if(false == $out)
{
    var_dump("lineno:".__LINE__.". oh! no! fail to save log");
    //update task status to failed
    updateTaskStatus($task_id, 2, 4, "oh! no! fail to save log");
    script_db::closeDB();
    exit;
}

$endtime = time();
$sql_str = "update ems_task_info set status=$task_status, remarks=\"$task_errmsg\", end_time=$endtime, log_count=$log_count where task_id=$task_id and status=2";
var_dump($sql_str);
$res_6 = script_db::update($sql_str);
if(false == $res_6)
{
    var_dump("lineno:".__LINE__.". oh! no! update mysql fail!");
    script_db::closeDB();
    exit;
}
script_db::closeDB();

var_dump("succ!");




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
