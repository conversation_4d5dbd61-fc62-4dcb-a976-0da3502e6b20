<?php 

/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file stableInterfaceDataByModAction.php
 * <AUTHOR>
 * @date 2016/9/01 17:35:21
 * @brief
 *
 **/


class getDutysByModInterfaceAction extends Util_Base{
    
    /**
     * 主入口
     * @return boolean 返回结果
     */
    public function execute(){
    	$module = Bingo_Http_Request::getNoXssSafe('module', '');
    	$interface = Bingo_Http_Request::getNoXssSafe('interface','');
    	
    	$arrInput = array(
    			'module'=>$module,
    			'interface'=>$interface
    	);
    	
        $arrOut = Tieba_Service::call('ems', 'getDutysByModInterface', $arrInput, null, null, 'post', 'php', 'gbk', 'local');
        if(false == $arrOut || 0 != $arrOut['errno']){
            Bingo_Log::fatal('call ems getDutysByModInterface failed. arrInput:['.serialize($arrInput).'] arrOut:['.serialize($arrOut).']');
            echo "NOTFOUND";
            return false;
        }
      	
         echo $arrOut[0];
        return true;
    }
}










?>

