<?php
/**
 * 接口级别的趋势图
 * <AUTHOR>
 *
 */

class stableInterfaceTrendAction extends Util_Base
{
    public function execute(){
        $startDate = Bingo_Http_Request::getNoXssSafe('startDate', date('Y-m-d',strtotime('-14 days')));
        $endDate = Bingo_Http_Request::getNoXssSafe('endDate', date('Y-m-d'));
        $interface = Bingo_Http_Request::getNoXssSafe('interface', '');
        $module = Bingo_Http_Request::getNoXssSafe('module', '');
        $subsys = Bingo_Http_Request::getNoXssSafe('subsys', '');
        $idcs = Bingo_Http_Request::getNoXssSafe('idcs', '');
        $strCallBack = Bingo_Http_Request::getNoXssSafe('callback', 'test_aaa');
        $format = Bingo_Http_Request::getNoXssSafe('format', 'jsonp');
		if($startDate == ""){
			$startDate = date('Y-m-d',strtotime('-14 days'));
		}
		if($endDate == ""){
			$endDate = date('Y-m-d');
		}
		$date_arr1 = explode('-',$startDate);
        $date_arr2 = explode('-',$endDate);
        if(count($date_arr1) != 3 || count($date_arr2) !=3 ){
            Bingo_Log::warning('stableInterfaceTrendAction input param invalid.arrInput:['.$startDate.$endDate.$interface.$idcs.']');
            $this->_jsonPRet(Tieba_Errcode::ERR_PARAM_ERROR, Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR), $strCallBack, null);
            return false;
        }
        $start_time = strtotime($startDate);
        $end_time = strtotime($endDate);
        
        $start_year = $date_arr1[0];
        $start_month = $date_arr1[1];
        $start_day = $date_arr1[2];
        $end_year = $date_arr2[0];
        $end_month = $date_arr2[1];
        $end_day = $date_arr2[2];
        
        $arrInput = array(
            'startDate' => strtotime($startDate),
            'endDate' => strtotime($endDate),
            'idcs' => $idcs,    //支持多个idc，以,隔开，如：all,jx,nj
            'module' => $module,
            'subsys' => $subsys,
            'interface' => $interface,
        );
        $arrOut = array();
        $arrOut = Tieba_Service::call('ems', 'getStableDataForInterface', $arrInput, NULL, NULL, 'post', 'php', 'utf-8', 'local');
        if(false == $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']){
            Bingo_Log::fatal('stableInterfaceTrendAction call ems getStableDataForInterface failed. arrInput:['.serialize($arrInput).'] arrOut:['.serialize($arrOut).']');
            $this->_jsonPRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, Tieba_Error::getErrmsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL), $strCallBack, null);
            return false;
        }
        if(empty($arrOut['data'])){
            Bingo_Log::fatal('stableInterfaceTrendAction call ems getStableDataForInterface data is empty. arrInput:['.serialize($arrInput).'] arrOut:['.serialize($arrOut).']');
            $this->_jsonPRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, Tieba_Error::getErrmsg(Tieba_Errcode::ERR_DB_QUERY_FAIL), $strCallBack, null);
            return false;
        }
        $arrRowByIdc = array();
        $categories = array();
        $series = array();
        foreach ($arrOut['data'] as $row){
            $arrRowByIdc[$row['idc']][] = $row;
            $strDate = date('Y-m-d', $row['stat_time']);
            if(!in_array($strDate, $categories)){
                $categories[] = $strDate;
            }
        }
        foreach ($arrRowByIdc as $idc => $rows){
            $arrStable = array();
            foreach ($rows as $row){
                $arrStable[] = round($row['stable']/1000000, 6);
            }
            $series[] = array(
                'name' => $interface,
                'idc' => $idc,
                'data' => $arrStable,
            );
        }
        /* build the return data */
        $returnData = array(
            'categories'=>$categories,
            'series'=>$series,
		);
		if($format == "json") {
			$ret = array();
			$ret['title'] = array(
				"text" => mb_convert_encoding("稳定性趋势图", "GBK", "UTF-8"),
			);
			$ret['legend'] = array(
				"data" => array("nj", "nj03", "tc", "jx", "gz", "all")
			);	
			$ret['xAxis'] = array(
				"type" => "category",
				"boundaryGap" => false,
				"data" => $returnData['categories'],
			);
			$ret['yAxis'] = array(
				"type" => "value",
				"scale" => true,
			);
			$ret['tooltip'] = array(
				"trigger" => "axis"	,
			);
			$series = array();
			foreach($returnData['series'] as $k => $v) {
				$series[$k]['name'] = $v['idc'];
				$series[$k]['type'] = "line";
				$series[$k]['smooth'] = false;
				$series[$k]['data'] = $v['data'];
			};		
			$ret['series'] = $series;	
			$this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS), $ret);
		}else{
        	$this->_jsonPRet(Tieba_Errcode::ERR_SUCCESS, Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS), $strCallBack, $returnData);
		}	
		return true;
    }
}

?>
