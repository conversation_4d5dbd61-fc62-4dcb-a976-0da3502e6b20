<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file getRelationsStatusAction.php
 * <AUTHOR>
 * @date 2014/06/25 17:01:24
 * @brief 
 *  
 **/

class getRelationsStatusAction extends Util_Base
{
    
    /**
     * 执行入口
     * @return [type] [description]
     */
    public function execute() {
        $strCallBack    = Bingo_Http_Request::getNoXssSafe('callback', 'test_aaa');
        $starttime = Bingo_Http_Request::getNoXssSafe('starttime', '');
        $endtime = Bingo_Http_Request::getNoXssSafe('endtime', '');
        $subsys = Bingo_Http_Request::getNoXssSafe('subsys', '');
        $module = Bingo_Http_Request::getNoXssSafe('module', '');
        $interface = Bingo_Http_Request::getNoXssSafe('interface', '');
        $idc = Bingo_Http_Request::getNoXssSafe('idc', 'all');

        if (empty($subsys) || empty($module) || empty($starttime) || empty($endtime)) {
            Bingo_Log::warning("getModuleRelationDataAction call failed, input param invalid. arrInput:[$subsys] [$module] [$starttime] [$endtime]");
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR));
            return ;
        }

        $stime = strtotime($starttime);
        $etime = strtotime($endtime);
        if ($etime <= $stime) {
            Bingo_Log::warning("getModuleRelationDataAction call failed, input param invalid. arrInput: [$starttime] [$endtime]");
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR));
            return ;
        }

        $arrInput = array(
            'stime' => $stime,
            'etime' => $etime,
            'subsys' => $subsys,
            'module' => $module,
            'interface' => $interface,
            'idc' => $idc,
        );
        $arrOutput = Tieba_Service::call('ems', 'getRelationsStatus', $arrInput,null,null,'post','php','utf-8');
        $this->_jsonPRet(0, "", $strCallBack, $arrOutput['data']);
        return true;
    }

}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
