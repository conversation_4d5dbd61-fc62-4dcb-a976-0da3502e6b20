<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file getRouterNginxStatusByChartAction.php
 * <AUTHOR>
 * @date 2014/09/18 15:18:37
 * @brief 
 *  
 **/


class getRouterNginxStatusByChartAction extends Util_Base
{
    const RETRY = 3;
    public function execute()
    {
        $chart = Bingo_Http_Request::getNoXssSafe('chart','qps');
        $key = Bingo_Http_Request::getNoXssSafe('key','tieba');
        $subsys = Bingo_Http_Request::getNoXssSafe('subsys','orp_router|router_nginx');
        $stime = Bingo_Http_Request::getNoXssSafe('stime','');
        $etime = Bingo_Http_Request::getNoXssSafe('etime','');
        $strCallBack = Bingo_Http_Request::getNoXssSafe('callback','test_aaa');
        $module = "tieba|$subsys";
        $endtime = time();
        $starttime = $endtime - 3600;
        if(!empty($stime) && !empty($etime))
        {
            $starttime = strtotime($stime);
            $endtime = strtotime($etime);
        }

        $exp = explode('|', $key);

        //if(count($exp) == 1)
        //{
	        $uri = "/ompui/api/commondata";
	        $querystring = "method=get_stats&module=$module&type=10s&key=$key&value=$chart&stime=$starttime&etime=$endtime";
	        Bingo_Log::debug("input for query : uri[$uri] querystring[$querystring]");
	        //$arrOutput = $this->httpcall($uri, $querystring);
	        $arrOutput = $this->stablecall($uri, $querystring);
	        Bingo_Log::debug("output for query : ". print_r($arrOutput, true));
	        if(!isset($arrOutput['errno']) || 0 != $arrOutput['errno'])
	        {
	            //TODo
	            Bingo_Log::warning("fail to get stats info! input[uri:$uri ; querystring:$querystring] output[".serialize($arrOutput)."]");
	            return 0;
	        }
            $retData = $arrOutput['data']["$key:$chart"];
        //}
        //else
        //{
        //    $exp2 = explode('@',$exp[0]);
        //    $exp3 = explode('@',$exp[1]);
        //    $appname = $exp2[1];
        //    $idc = $exp3[1];
        //    $uri = "/ompui/api/commondata";                                         
        //    $querystring = "method=get_tree&module=tieba|orp_router|router_nginx&key=tieba@$appname";
        //    $arrOutput = $this->stablecall($uri, $querystring);
        //    Bingo_Log::debug("input: [$querystring]  output : ". print_r($arrOutput, true));
        //    if(false === $arrOutput || 0 != $arrOutput['errno'])
        //    {
        //        Bingo_Log::warning("fail to get tree! input[querystring:$querystring] output[".serialize($arrOutput)."]");
        //        $this->_jsonPRet(0, "", $strCallBack, array());//TODO
        //        return true;
        //    }

        //    /* get the aim node */
        //    $list = $arrOutput['data']['tieba'][$appname]["server_ip"];
        //    $list = array_keys($list);
        //    $list = $this->get_idcserver($list,$idc);
        //    //var_dump($search_out);
        //    //exit;
        //  
        //    if(empty($list))
        //    {
        //        $retData = array();
        //        for($time = $starttime; $time<=$endtime; $time+=10)
        //        {
        //            $retData[] = array(
        //                $time,
        //                0,
        //            );
        //        }
        //        $this->_jsonPRet(0, "", $strCallBack, $retData);
        //        return true;
        //    }
        //    //var_dump($total_data);
        //    //exit; 
        //    if($chart == "STABLECODE_0_RATE" || $chart == "HTTP_200_RATE" || $chart == "cost")
        //    {
        //        if($chart == "STABLECODE_0_RATE")
        //            $chart = "stable";
        //        else if($chart == "HTTP_200_RATE")
        //            $chart = "HTTP_200_COUNT";
        //        else if($chart == "cost")
        //            $chart = "cost_sum";
        //        if(($search_qps = $this->multi_server_data($list, $appname, "qps", $starttime,$endtime))==false)
        //        {
        //            Bingo_Log::warning("fail to get multi_server qps data ".print_r($list, true));
        //            //exit;
        //        }
        //        if(($search_out = $this->multi_server_data($list, $appname, $chart, $starttime, $endtime))==false)
        //        {
        //            Bingo_Log::warning("fail to get multi_server data".print_r($list,true));
        //            //exit;
        //        }
        //        $total_data = array();
        //        $total_qps_data = array();
        //        foreach($search_qps as $server=>$raw_data)
        //        {
        //            if($raw_data == NULL || $search_out[$server] == NULL )
        //                continue;
        //            foreach($raw_data['data']["tieba@$appname|server_ip@$server:qps"] as $k=>$data)
        //            {
        //                $total_qps_data[$k][1] += $data[1];
        //                $total_qps_data[$k][0] = $data[0];
        //            }
        //            foreach($search_out[$server]['data']["tieba@$appname|server_ip@$server:$chart"] as $k=>$data)
        //            {
        //                $total_data[$k][1] += $data[1];
        //                $total_data[$k][0] = $data[0];
        //            }
        //        }
        //        $size = count($total_qps_data);
        //        for($i = 0;$i<$size;$i++)
        //            $total_data[$i][1] = ($total_qps_data[$i][1] == 0)?0:($total_data[$i][1]/$total_qps_data[$i][1]);
        //    }
        //    else
        //    {
        //        if(($search_out = $this->multi_server_data($list, $appname, $chart, $starttime, $endtime))==false)
        //        {
        //            Bingo_Log::warning("fail to get multi_server data".print_r($list,true));
        //            //exit;
        //        }
        //        $total_data = array();
	    //        foreach($search_out as $server=>$raw_data)
	    //        {
	    //            if($raw_data == NULL)
	    //            {
	    //                continue;
	    //            }
	    //            foreach($raw_data['data']["tieba@$appname|server_ip@$server:$chart"] as $k=>$data)
	    //            {
	    //                $total_data[$k][1] += $data[1];
	    //                $total_data[$k][0] = $data[0];
	    //            }
	    //        }

        //    }

        //    $retData = array();
        //    foreach($total_data as $data)
        //    {
        //        $retData[] = array(
        //            $data[0],
        //            $data[1],
        //        );
        //    }
        //}
        
        $extra = array();
        $timeArr = array();
        $valueArr = array();
        foreach ($retData as $key=>$value){
        	$timeArr[$key] = $value[0];
        	$valueArr[$key] = $value[1];
        }
        $sortData = $retData;
        array_multisort($valueArr,$timeArr, $sortData);
        $min = $sortData[0];
        $max = $sortData[count($sortData)-1];
        if ($chart == 'STABLECODE_0_RATE' && $min[1]<0.999){
            $extra['minefield']['happen_time'] = $min[0];
        	$extra['minefield']['describe'] = iconv(Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK, '稳定性偏低');   	
        }elseif ($chart == 'qps' ){
            $extra[ 'minefield']['happen_time' ] = $max[0];
            $extra[ 'minefield']['describe' ] = iconv(Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK, '流量偏高');               
        } elseif ($chart == 'cost' ){
            $extra[ 'minefield']['happen_time' ] = $max[0];
            $extra[ 'minefield']['describe' ] = iconv(Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK, '耗时偏大');         
        } elseif ($chart == 'HTTP_200_RATE' && $min[1]<0.999){
            $extra[ 'minefield']['happen_time' ] = $min[0];
            $extra[ 'minefield']['describe' ] = iconv(Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK, 'http200成功率偏低');         
        } elseif($chart == 'HTTP_302_COUNT' ){
            $extra[ 'minefield']['happen_time' ] = $max[0];
            $extra[ 'minefield']['describe' ] = iconv(Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK, '302偏高');  
        } elseif ($chart == 'HTTP_499_COUNT' ){
            $extra[ 'minefield']['happen_time' ] = $max[0];
            $extra[ 'minefield']['describe' ] = iconv(Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK, '499偏多');  
        } elseif ($chart == 'HTTP_500_COUNT' ){
            $extra[ 'minefield']['happen_time' ] = $max[0];
            $extra[ 'minefield']['describe' ] = iconv(Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK, '500偏多');  
        } elseif ($chart == 'HTTP_502_COUNT' ){
            $extra[ 'minefield']['happen_time' ] = $max[0];
            $extra[ 'minefield']['describe' ] = iconv(Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK, '502偏多');  
        } elseif ($chart == 'HTTP_503_COUNT' ){
            $extra[ 'minefield']['happen_time' ] = $max[0];
            $extra[ 'minefield']['describe' ] = iconv(Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK, '503偏多');  
        } elseif ($chart == 'HTTP_504_COUNT' ){
            $extra[ 'minefield']['happen_time' ] = $max[0];
            $extra[ 'minefield']['describe' ] = iconv(Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK, '504偏多');  
        }
        

        $sumData['no'] = 0;
        $sumData['error'] = "";
        $sumData['data'] = $retData;
        $sumData['extra'] = $extra;
        //$this->_jsonPRet(0, "", $strCallBack, $sumData);
        echo "$strCallBack(".Bingo_String::array2json($sumData).")";
        return true;
        //$this->_jsonPRet(0, "", $strCallBack, $retData);
        //return true;
        
    }

    private function multi_server_data($list, $appname, $chart, $starttime, $endtime)
    {
            if(!isset($list) || empty($list) ||!isset($appname) ||!isset($chart) || !isset($starttime) || !isset($endtime))
            {
                Bingo_Log::warning("param is wrong!");
                return false;
            }

            /* get data eachfully */
            $uri = "/ompui/api/commondata";
            $result = array();

            $para_num = 32;
            $multi_search = array();
            $multi_search_all = array();
            
            /* multi_search */
            foreach($list as $server)
            {
                $querystring="method=get_stats&module=tieba|orp_router|router_nginx&key=tieba@$appname|server_ip@$server&value=$chart&stime=$starttime&etime=$endtime";
                $multi_search[$server] = array(
                    'omp_online_http',
                    'post',
                    json_encode(array()),
                    rand(),
                    array(
                        "pathinfo"=>$uri,
                        "querystring"=>$querystring,
                    ),
                );
                if(--$para_num == 0)
                {
                    $multi_search_all[] = $multi_search;
                    $multi_search = array();
                    $para_num = 32;
                }
            }
            if($para_num != 32)
                $multi_search_all[] = $multi_search;

            if(empty($multi_search_all))
            {
                Bingo_Log::warning("NULL result! that's impossible!");
                return false;
            }

            $ret_search = array();
            foreach($multi_search_all as $multi_search)
            {
                if(($ret_data = ral_multi($multi_search)) == false)
                {
	                Bingo_Log::warning("input for query : uri[$uri] querystrings[".var_dump($multi_search)."]");
	                return 0;
                }
                $ret_search = array_merge($ret_search, $ret_data);
            }
            //var_dump($ret_search);
            //exit;
            /* json decode */
            foreach($ret_search as $k=>$v)
                $result[$k] = json_decode($v,true);
            return $result;
    }
    private function get_idcserver($list, $idc)
    {
        static $idc_map = array('yf01'=>'jx','cq01'=>'jx','dbl01'=>'jx','ai01'=>'jx','jx'=>'jx','cp01'=>'jx',
                        'cq02'=>'tc','tc'=>'tc','m1'=>'tc','db01'=>'tc','st01'=>'tc',
                        'nj02'=>'nj');
        $to_test = $idc_map[$idc];

        $result = array();
        foreach($list as $server)
        {
            $exp = explode('.', $server);
            if($idc_map[$exp[1]] == $idc)
            {
                $result[] = $server;
            }
        }
        return $result;

    }
    private function httpcall($uri, $querystring, $params=array())
    {   
        $input = json_encode($params);
        ral_set_pathinfo($uri);
        ral_set_querystring($querystring);
        $retry = self::RETRY;
        while($retry -- )
        {
            $out = ral("omp_online_http", "post", $input, rand());
            if(false != $out)
            {
                break;
            }
            Bingo_Log::warning("fail to get omp data for req[".($querystring)."]");
            if(0 == $retry)
            {
                return false;
            }
            sleep(1);
        }
        return json_decode($out, true);
    }
	private function stablecall($uri, $querystring, $input=array())
	{
	    $url = "http://omp.baidu.com".$uri."?".$querystring;
	    $ch = curl_init();
	    curl_setopt($ch, CURLOPT_URL, $url );
	    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	    curl_setopt($ch, CURLOPT_HEADER, 0);
	    $retry = self::RETRY;
	    while($retry -- )
	    {
	        $output = curl_exec($ch);
	        if(false != $output)
	        {
	            break;
	        }
            Bingo_Log::warning("fail to get coreUI data for req[".serialize($input)."]");
	        if(0 == $retry)
	        {
	            return false;
	        }
	        sleep(1);
	    }
	    curl_close($ch);
	    $out = json_decode($output,true);
	    return $out;    
	
	}

 }




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
