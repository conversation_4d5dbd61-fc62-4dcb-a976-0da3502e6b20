<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file summaryDataAction.php
 * <AUTHOR>
 * @date 2014/06/23 14:03:57
 * @brief 
 *  
 **/

class summaryDataAction extends Util_Base
{
    public function execute()
    {
        $date = Bingo_Http_Request::getNoXssSafe('date',date('Y-m-d'));
        $strCallBack = Bingo_Http_Request::getNoXssSafe('callback','test_aaa');

        $date_arr = explode('-', $date);
        if(count($date_arr) !== 3)
        {
            //TODO
            return;
        }
        $today_year = $date_arr[0];
        $today_month = $date_arr[1];
        $today_day = $date_arr[2];
        $today_begin_time = strtotime($date);
        $yes_begin_time = $today_begin_time - 86400;
        $yes_year = date('Y', $yes_begin_time);
        $yes_month = date('m', $yes_begin_time);
        $yes_day = date('d', $yes_begin_time);
        $lw_begin_time = $today_begin_time - 86400*7;
        $lw_year = date('Y', $yes_begin_time);
        $lw_month = date('m', $yes_begin_time);
        $lw_day = date('d', $yes_begin_time);
        $today_input = array(
            'type'=>1,
            'year'=>$today_year,
            'month'=>$today_month,
            'day'=>$today_day,
        );
        Bingo_Log::debug("input for getting today stat data : ". print_r($today_input, true));
        $today_output = Tieba_Service::call('ems', 'getStatDatasForLevelN', $today_input, NULL, NULL, 'post', 'php', 'utf-8', 'local');
        Bingo_Log::debug("today stat data : ". print_r($today_output, true));
        if(false == $today_output || 0 !== $today_output['errno'])
        {
            Bingo_Log::warning("fail to get today stat data! input[".serialize($today_input)."] output[".serialize($today_output)."]");
        }
        $yes_input = array(
            'type'=>1,
            'year'=>$yes_year,
            'month'=>$yes_month,
            'day'=>$yes_day,
        );
        $yes_output = Tieba_Service::call('ems', 'getStatDatasForLevelN', $yes_input, NULL, NULL, 'post', 'php', 'utf-8', 'local');
        Bingo_Log::debug("yesterday stat data : ". print_r($yes_output, true));
        if(false == $yes_output || 0 !== $yes_output['errno'])
        {
            Bingo_Log::warning("fail to get yesterday stat data! input[".serialize($yes_input)."] output[".serialize($yes_output)."]");
        }
        $lw_input = array(
            'type'=>1,
            'year'=>$lw_year,
            'month'=>$lw_month,
            'day'=>$lw_day,
        );
        $lw_output = Tieba_Service::call('ems', 'getStatDatasForLevelN', $lw_input, NULL, NULL, 'post', 'php', 'utf-8', 'local');
        Bingo_Log::debug("last week stat data : ". print_r($lw_output, true));
        if(false == $lw_output || 0 !== $lw_output['errno'])
        {
            Bingo_Log::warning("fail to get last week stat data! input[".serialize($lw_input)."] output[".serialize($lw_output)."]");
        }

        $this->today_data = isset($today_output['data']['tieba'])?$today_output['data']['tieba']:$this->getDefaultStatData();
        $this->yes_data = isset($yes_output['data']['tieba'])?$yes_output['data']['tieba']:$this->getDefaultStatData();
        $this->lw_data = isset($lw_output['data']['tieba'])?$lw_output['data']['tieba']:$this->getDefaultStatData();
        $retData = array();
        $retData[] = $this->dealOneLineData("总流量", "flow", true);
        $retData[] = $this->dealOneLineData("fatal请求数", "fatal_reqs");
        $retData[] = $this->dealOneLineData("error请求数", "err_reqs");
        //$retData[] = $this->dealOneLineData("warning请求数", "warn_reqs");
        $retData[] = $this->dealOneLineData("fatal总数", "fatal_num");
        $retData[] = $this->dealOneLineData("error总数", "err_num");
        $retData[] = $this->dealOneLineData("warning总数", "warn_num");
        $this->_jsonPRet(0, "", $strCallBack, $retData);
        return true;
    }

    private function getDefaultStatData()
    {
        return array(
            'flow'=>0,
            'fatal_reqs'=>0,
            'err_reqs'=>0,
            'warn_reqs'=>0,
            'fatal_num'=>0,
            'err_num'=>0,
            'warn_num'=>0,
        );
    }

    private function dealOneLineData($name, $field, $is_flow=false)
    {
        return array(
            'name'=>iconv(Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK, $name),
            'today'=>intval($this->today_data[$field]),
            'rate'=>$is_flow ? "-" : $this->getRate($this->today_data[$field], 0, $this->today_data['flow']),
            'rate_level'=>$is_flow ? '' : $this->getErrRateLevel($this->today_data[$field], $this->today_data['flow']),
            'yesterday'=>intval($this->yes_data[$field]),
            'yesterday_rate'=>$this->getRate($this->today_data[$field], $this->yes_data[$field], $this->yes_data[$field]),
            'yesterday_rate_level'=>$is_flow ? '' : $this->getRLevel($this->today_data[$field], $this->yes_data[$field], $this->yes_data[$field]),
            'week'=>intval($this->lw_data[$field]),
            'week_rate'=>$this->getRate($this->today_data[$field], $this->lw_data[$field], $this->lw_data[$field]),
            'week_rate_level'=>$is_flow ? '' : $this->getRLevel($this->today_data[$field], $this->lw_data[$field], $this->lw_data[$field]),
        );
    }

    private function getRate($A, $B, $C)
    {
        if($C <= 0)
        {
            return 0;
        }
        return round(($A-$B)/$C, 6);
    }

    private function getRLevel($A, $B, $C)
    {
        if($C <= 0)
        {
            return "";
        }
        $rate = round(($A-$B)/$C, 6) * 100;
        if($rate > 5)
        {
            return "red";
        }
        elseif($rate < -5)
        {
            return "green";
        }
        else
        {
            return "";
        }
    }

    private function getErrRateLevel($A, $B)
    {
        if($C <= 0)
        {
            return "";
        }
        $rate = round(($A-$B)/$C, 6) * 100;
        if($rate > 5)
        {
            return "red";
        }
        else
        {
            return "";
        }
    }

}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
