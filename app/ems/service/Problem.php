<?php
class Service_Problem
{
    //添加模块问题
    public static function addProblem($arrInput)
    {
        if (!isset($arrInput['module_name']) || !isset($arrInput['title']))
        {
            Bingo_Log::warning("param error! ".serialize($arrInput));
            return Service_Libs_Util::buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (Service_Libs_DB::init() === false)
        {
            Bingo_Log::init("init db error!");
            return Service_Libs_Util::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrInput =  array(
            'alarm_id'    => isset($arrInput['alarm_id'])?intval($arrInput['alarm_id']):0,
            'module_name' => $arrInput['module_name'],
            'title'       => $arrInput['title'],
            'create_time' => time(),
            'happen_time' => isset($arrInput['happen_time'])?intval($arrInput['happen_time']):0,
            'status'      => 0,
            'class'       => 0,
            'type'        => '',
            'analysis'    => '',
            'solution'    => '',
            'ext_info'    => '',
            'is_false_alarm' => 3,
            'is_pt_help'     => 3,
        );
        $arrOut = Service_Libs_DB::insert('problem_info', $arrInput);
        if ($arrOut === false)
        {
            Service_Libs_DB::getErrInfo();
            Bingo_Log::warning("insert problem_info error! ".serialize($arrInput));
            return Service_Libs_Util::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return Service_Libs_Util::buildReturn();       
    }

    //更新问题信息
    public static function updateProblem($arrInput)
    {
        if (!isset($arrInput['problem_id']))
        {
            Bingo_Log::warning("param error! ".serialize($arrInput));
            return Service_Libs_Util::buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (Service_Libs_DB::init() === false)
        {
            Bingo_Log::init("init db error!");
            return Service_Libs_Util::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrInput =  array(
            'problem_id'  => $arrInput['problem_id'],
            //'alarm_id'    => isset($arrInput['alarm_id'])?intval($arrInput['alarm_id']):0,
            'title'       => $arrInput['title'],
            //'happen_time' => isset($arrInput['happen_time'])?intval($arrInput['happen_time']):0,
            'status'      => isset($arrInput['status'])?intval($arrInput['status']):0,
            'class'       => isset($arrInput['class'])?intval($arrInput['class']):0,
            'type'        => isset($arrInput['type'])?strval($arrInput['type']):'',
            'analysis'    => isset($arrInput['analysis'])?strval($arrInput['analysis']):'',
            'solution'    => isset($arrInput['solution'])?strval($arrInput['solution']):'',
            //'ext_info'    => isset($arrInput['ext_info'])?strval($arrInput['ext_info']):'',
            'is_false_alarm' => isset($arrInput['is_false_alarm'])?intval($arrInput['is_false_alarm']):0,
            'is_pt_help'     => isset($arrInput['is_pt_help'])?intval($arrInput['is_pt_help']):0,
        );
        $arrCond = array(
            'problem_id='  => $arrInput['problem_id'],
        );
        $arrOut = Service_Libs_DB::update('problem_info', $arrInput, $arrCond);
        if ($arrOut === false)
        {
            Service_Libs_DB::getErrInfo();
            Bingo_Log::warning("insert problem_info error! ".serialize($arrInput));
            return Service_Libs_Util::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return Service_Libs_Util::buildReturn();   
    }

    //获取指定模块的问题列表
    public static function getProblemList($arrInput)
    {
        if (!isset($arrInput['module_name']))
        {
            Bingo_Log::warning("param error! ".serialize($arrInput));
            return Service_Libs_Util::buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (Service_Libs_DB::init() === false)
        {
            Bingo_Log::init("init db error!");
            return Service_Libs_Util::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

       // $strModName = strval($arrInput['module_name']);
        $modules = $arrInput['module_name'];
	foreach($modules as $k=>$module)
	{
	      $modules[$k] = "'$module'";
	}
	$strMods = implode(',', $modules);
        $intOffset  = intval($arrInput['offset']);
        $intLimit   = intval($arrInput['limit']);
        $intStartTime = intval($arrInput['start_time']);
        $intEndTime = intval($arrInput['end_time']);
        $intStatus  = -1;
        if (isset($arrInput['status']))
        {
            $intStatus  = intval($arrInput['status']);
        }
        $strSql   = " WHERE module_name in ($strMods)";
        if ($intStartTime > 0)
        {
            $strSql .= " AND  create_time >= ".$intStartTime;
        }
        if ($intEndTime > 0)
        {
            $strSql .= " AND  create_time <= ".$intEndTime;
        }        
        if ($intStatus !== -1)
        {
            $strSql .= " AND status=".$intStatus;
        }
        $strCountSql = "SELECT count(problem_id) as total FROM problem_info ".$strSql;
        $arrOut = Service_Libs_DB::query($strCountSql, array());
        $intTotal = $arrOut[0]['total'];
        if ($arrOut === false)
        {
            Service_Libs_DB::getErrInfo();
            Bingo_Log::warning("get problem_info list error! ".serialize($arrInput));
            return Service_Libs_Util::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        if ($intTotal <= 0)
        {
            $arrOutput = array(
                'total' => $intTotal,
                'problem_list' => array(),
            );
            return Service_Libs_Util::buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
        }
        $strContentSql = "SELECT * FROM problem_info ".$strSql." order by create_time desc LIMIT ".$intOffset.",".$intLimit;
        $arrOut = Service_Libs_DB::query($strContentSql, array());        
        if ($arrOut === false)
        {
            Service_Libs_DB::getErrInfo();
            Bingo_Log::warning("get problem_info list error! ".serialize($arrInput));
            return Service_Libs_Util::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }        
        $arrOutput = array(
            'total' => $intTotal,
            'problem_list' => $arrOut,
        );
        return Service_Libs_Util::buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrOutput);          
    }

    //获取问题信息
    public static function getProblem($arrInput)
    {
        if (!isset($arrInput['problem_id']))
        {
            Bingo_Log::warning("param error! ".serialize($arrInput));
            return Service_Libs_Util::buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (Service_Libs_DB::init() === false)
        {
            Bingo_Log::init("init db error!");
            return Service_Libs_Util::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $intProId = intval($arrInput['problem_id']);
        $strSql   = "SELECT * FROM problem_info WHERE problem_id = ".$intProId;
        $arrOut = Service_Libs_DB::query($strSql, array());
        if ($arrOut === false)
        {
            Service_Libs_DB::getErrInfo();
            Bingo_Log::warning("get problem_info error! ".serialize($arrInput));
            return Service_Libs_Util::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return Service_Libs_Util::buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrOut[0]);  
    }
}
