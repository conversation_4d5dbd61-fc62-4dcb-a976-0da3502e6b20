<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2016-05-12 11:22:53
 * @version
 */

 /*
    注意这里涉及到rd与fe的模块名，如果提前已经协商好统一模块名例如都叫 messagepool，那么这里就不要传第二个参数，默认即可：
        Tieba_Init::init("messagepool");
    但如果没协商，比如rd的模块名叫 messagepool，fe的模块名叫 messagepool_fe，那么这里就应当是（fe这个模块名用于 ROOT_PATH/template/ 下的文件夹名）
        Tieba_Init::init("messagepool","messagepool_fe");
    同理，也可以自定义omp模块名，默认同样使用 messagepool：
        Tieba_Init::init("messagepool",null,"messagepool_omp");
 */

 Tieba_Init::init("messagepool");

 $objFrontController = Bingo_Controller_Front::getInstance(array(
     "actionDir" => MODULE_ACTION_PATH,
     "defaultRouter" => "index",
     "notFoundRouter" => "error",
     "beginRouterIndex" => 1,
 ));

 Bingo_Timer::start('total');
/*
 Bingo_Page::init(array(
     "baseDir" => MODULE_VIEW_PATH,
     "debug" => false,
     "outputType" => ".",
     "isXssSafe" => true,
     "module" => "messagepool",
     "useTbView" => true,
     "viewRootpath" => MODULE_VIEW_PATH . "/../",
    //"catchPath" => "../../data/app/messagepool",
 ));
*/

try{
    $objFrontController->dispatch();
}catch(Exception $e){
    //出错处理，直接转向到错误页面
    Bingo_Log::warning(sprintf('main process failure!HttpRouter=%s,error[%s]file[%s]line[%s]',
        Bingo_Http_Request::getStrHttpRouter(), $e->getMessage(), $e->getFile(), $e->getLine()));
    Bingo_Page::setErrno($e->getCode());
    header('location:http://static.tieba.baidu.com/tb/error.html');
}

/*
 Bingo_Timer::start('build_page');
 Bingo_Page::buildPage();
 Bingo_Timer::end('build_page');
*/
 Bingo_Timer::end('total');
 
 $strTimeLog = Bingo_Timer::toString();
 Bingo_Log::pushNotice('Timer','['.$strTimeLog.']');
 Bingo_Log::buildNotice();           

?>
