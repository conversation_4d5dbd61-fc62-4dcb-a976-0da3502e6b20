<?php
/**
 * ui action的基类
 **/
abstract class Util_Base extends Bingo_Action_Abstract {

    // 错误号
    protected $_intErrno = 0;
    const BINGO_ENCODE_LANG = "GBK";

    // 错误信息
    protected $_strError = 'success';
    protected $_arrUserInfo = array();

	/**
		*
		*
		* 	@param
		* 	@return
		*
	 */
    public function init(){
    
    	//获取路由名，并打到ui日志里
        $strRouter = Bingo_Http_Request::getStrHttpRouter();
    	Bingo_Log::pushNotice("urlkey", $strRouter);
    	
    	//获取当前用户信息，并打到ui日志里
    	$this->_getUserInfo();
    	foreach($this->_arrUserInfo as $strKey => $mixVal) {
    		Bingo_Log::pushNotice($strKey, $mixVal);
    	}

		//默认对POST请求做tbs校验，如不需要自行去掉此模块
//        if(Bingo_Http_Request::isPost()){
//        	$strTbs = strval(Bingo_Http_Request::get('tbs',''));
//            if ( false === $this->_tbsCheck($strTbs) ) {
//            	throw new Util_Exception("tbs check error!",Tieba_Errcode::ERR_INVALID_SIGN);
//            }
//        }
        return true;
    }

	/**
		*
		*
		* 	@param
		* 	@return
		*
	 */
    protected function _tbsCheck($strTbs) {        
    	if(!Tieba_Tbs::check($strTbs, true)){
    	 	return false;
		}
		return true;
    }
    
	/**
		*
		*
		* 	@param
		* 	@return
		*
	 */
    protected function _getUserInfo() {
        if (!empty($this->_arrUserInfo)) {
            return $this->_arrUserInfo;
        }

        $bolLogin   = (boolean)Tieba_Session_Socket::isLogin();
        $intUserId  = intval(Tieba_Session_Socket::getLoginUid());
        $strUserName    = strval(Tieba_Session_Socket::getLoginUname());
        $intUserIp      = intval(Bingo_Http_Ip::ip2long(Bingo_Http_Ip::getConnectIp()));
        $bolNoUname     = (boolean)Tieba_Session_Socket::getNo_un();
        $strMobile      = strval(Tieba_Session_Socket::getMobilephone());
        $strEmail       = strval(Tieba_Session_Socket::getEmail());

        $arrUserSInfo   = array(
            'is_login'  => $bolLogin,
            'user_id'   => $intUserId,
            'user_name' => $strUserName,
            'user_ip'    => $intUserIp,
            'is_noname' => $bolNoUname,
            'mobile' => Tieba_Util::maskPhone($strMobile),
            'email'  => Tieba_Util::maskEmail($strEmail),
        );
        $this->_arrUserInfo = $arrUserSInfo;
        return $this->_arrUserInfo;
    }


	/**
		*
		*
		* 	@param
		* 	@return
		*
	 */
    protected function _jsonRet($errno,$errmsg,array $arrExtData=array(),$strCallback = ""){
        $arrRet = array(
            'no'=>intval($errno),
            'error'=>strval($errmsg),
            'data'=>$arrExtData,
        );

        if($strCallback !== ""){
            $strJsonData = Bingo_String::array2json($arrRet, self::BINGO_ENCODE_LANG);
            echo "$strCallback($strJsonData)";
            return;
        }

        foreach($arrRet as $k=>$v){
            Bingo_Page::assign($k,$v);
        }
        Bingo_Page::setOnlyDataType("json");
        Bingo_Http_Response::contextType('application/json');
    }

	/**
		*
		*
		* 	@param
		* 	@return
		*
	 */
    protected function _serialRet($errno,$errmsg,array $arrExtData=array()){
        $arrRet = array(
            'no'=>intval($errno),
            'error'=>strval($errmsg),
            'data'=>$arrExtData,
        );
        foreach($arrRet as $k=>$v){
            Bingo_Page::assign($k,$v);
        }
        Bingo_Page::setOnlyDataType("serial");
        Bingo_Http_Response::contextType('application/json');
    }

}
