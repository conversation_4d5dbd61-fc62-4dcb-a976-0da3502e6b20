<?php
class getDeliveryInfoAction extends Common_Base {

	private $intPn      = 0;
	private $intRn      = 0;
	private $intTeamId  = 0;
    private $intBDate   = 0;
    private $intEDate   = 0;
	
	/**
	 * 校验非空、非0
	 * @param unknown $arrOut
	 * @return string $key
	 */
	public function init() {
		parent::init();
		$this->_getUserInfo();
		return true;
	}
	
	/**
	 * 校验非空、非0
	 * @param unknown $arrOut
	 * @return string $key
	 */
	public function execute() {
		try {
			if (!$this->initParam()) {
				$this->_jsonRet(Tieba_Errcode::ERR_NVOTE_INVALID_PARAMS, Tieba_Error::getErrmsg(Tieba_Errcode::ERR_NVOTE_INVALID_PARAMS));
			}
            $errno = Tieba_Errcode::ERR_SUCCESS;

			// 获取团队名
			$arrTeamInput = array (
				'team_id' => $this->intTeamId,
			);
            $arrTeamOutput = Tieba_Service::call('common', 'getTeamInfoByTeamId', $arrTeamInput, null, null, 'post', null);
			if (false == $arrTeamOutput || Tieba_Errcode::ERR_SUCCESS !== $arrTeamOutput['errno']) {
				Bingo_Log::warning ( "call getTeamInfoByTeamId fail!input[" . serialize($arrTeamInput) . "],output[" . serialize($arrTeamOutput) . "]");
				$erron = empty($arrTeamOutput['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrTeamOutput['errno'] ;
				$this->_jsonRet($erron, Tieba_Error::getErrmsg($erron));
				return ;
			}
			$strTeamName = $arrTeamOutput['data']['team_name'];

			// 获取渠道信息数据
            $intOffset  = ($this->intPn - 1) * $this->intRn;
            $intLimit   = $this->intRn;
            $arrDeliverInput = array(
                'team_id'       => $this->intTeamId,
                'begin_date'    => $this->intBDate,
                'end_date'      => $this->intEDate,
                'offset'        => $intOffset,
                'limit'         => $intLimit,
            );
            $arrDeliverOutput = Tieba_Service::call('common', 'getDeliveryData', $arrDeliverInput, null, null, 'post', null);
            if (false == $arrDeliverOutput || Tieba_Errcode::ERR_SUCCESS !== $arrDeliverOutput['errno']) {
                Bingo_Log::warning ( "call getDeliveryData fail!input[" . serialize($arrDeliverInput) . "],output[" . serialize($arrDeliverOutput) . "]");
                $erron = empty($arrDeliverOutput['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrDeliverOutput['errno'] ;
                $this->_jsonRet($erron, Tieba_Error::getErrmsg($erron));
                return ;
            }

            $arrOutput = array();
            $arrDeliverInfo = array();
			foreach ($arrDeliverOutput['data'] as $key => $value) {
                // 获取渠道名
                $arrAccountInput = array (
                    'account_id' => intval($value['account_id']),
                );
                $arrAccountOutput = Tieba_Service::call('common', 'getAccountInfoByAccountId', $arrAccountInput, null, null, 'post', null);
                if (false == $arrAccountOutput || Tieba_Errcode::ERR_SUCCESS !== $arrAccountOutput['errno']) {
                    Bingo_Log::warning ( "call getAccountInfoByAccountId fail!input[" . serialize($arrAccountInput) . "],output[" . serialize($arrAccountOutput) . "]");
                    $erron = empty($arrAccountOutput['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrAccountOutput['errno'] ;
                    $this->_jsonRet($erron, Tieba_Error::getErrmsg($erron));
                    return ;
                }
                $strAccountName = $arrAccountOutput['data']['account_name'];

                $arrDeliverInfo[intval($value['stat_date'])][] = array(
                    'delivery_sum'      => $value['delivery_sum'],
                    'account_name'      => $strAccountName,
                );
                if (isset($arrOutput[intval($value['stat_date'])])) {
                    continue;
                }
                $arrOutput[intval($value['stat_date'])] = array(
                    'team_name'         => $strTeamName,
                    'stat_date'         => $value['stat_date'],
                    'public_source_sum' => $value['public_source_sum'],
                    'private_source_sum'=> $value['private_source_sum'],
                    'channel_name'      => $value['channel_name'],
                );
			}
            ksort($arrOutput);
            foreach ($arrOutput as $key => $value) {
                $arrOutput[$key]['delivery_info'] = $arrDeliverInfo[$key];
			}

            $arrOut = array();
            $arrOut['rows'] = array_values($arrOutput);
            $arrOut['total'] = count($arrDeliverOutput['data']);

			// 返回结果
			$this->_jsonRet($errno, Tieba_Error::getErrmsg($errno), $arrOut);
		} catch (Common_Exception $e) {
			$this->_jsonRet($e->getCode(), $e->getMessage());
			return;
		}
	}
	
	/**
	 * 校验非空、非0
	 * @param unknown $arrOut
	 * @return string $key
	 */
	public function initParam() {
		$this->intPn        = intval(Bingo_Http_Request::getNoXssSafe('pn', 1));
		$this->intRn        = intval(Bingo_Http_Request::getNoXssSafe('rn', 30));
		$this->intTeamId    = intval(Bingo_Http_Request::getNoXssSafe('team_id', 0));
        $intBeginTime       = intval(Bingo_Http_Request::getNoXssSafe('begin_date', 0));
        $intEndTime         = intval(Bingo_Http_Request::getNoXssSafe('end_date', 0));
		if (empty($this->intTeamId) || $intBeginTime > $intEndTime || $intEndTime - $intBeginTime > 86400 * 30 || $intBeginTime < 0) {
			return false;
		}
		$intDate = date("Ymd", strtotime("-1 day"));
        $this->intBDate = ($intBeginTime > 0) ? date("Ymd", $intBeginTime) : $intDate;
        $this->intEDate = ($intEndTime > 0) ? date("Ymd", $intEndTime) : $intDate;

        return true;
	}
}

