-- 贴吧2.0 回复模块数据库设计
-- DDBS表
-- 以`reply_id`作为分片ID

CREATE TABLE `ntreply` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id', 
  `reply_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT 'reply ID',
  `topic_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT 'topic ID',
  `rank` int(11) unsigned NOT NULL DEFAULT 0 COMMENT 'rank',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT 'user ID',
  `user_name` varchar(500) NOT NULL DEFAULT '' COMMENT 'user name',
  `nickname` varchar(500) NOT NULL DEFAULT '' COMMENT 'nickname',
  `is_anonymous` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT 'is anonymous',
  `comment_num` int(11) unsigned NOT NULL DEFAULT 0 COMMENT 'comment num',
  `forward_num` int(11) unsigned NOT NULL DEFAULT 0 COMMENT 'foward num',
  `zan_num` int(11) unsigned NOT NULL DEFAULT 0 COMMENT 'zan num',
  `cai_num` int(11) unsigned NOT NULL DEFAULT 0 COMMENT 'cai num',
  `title` varchar(1000) NOT NULL DEFAULT '' COMMENT 'topic title',
  `content` varchar(16384) NOT NULL DEFAULT '' NOT NULL COMMENT 'content',
  `digest` varchar(1000) NOT NULL DEFAULT '' COMMENT 'digest',
  `pic` varchar(1000) NOT NULL DEFAULT '' COMMENT 'pic',
  `audio` varchar(1000) NOT NULL DEFAULT '' COMMENT 'audio',
  `video` varchar(1000) NOT NULL DEFAULT '' COMMENT 'video',
  `link` varchar(1000) NOT NULL DEFAULT '' COMMENT 'link',
  `resource` varchar(1000) NOT NULL DEFAULT '' COMMENT 'resource eg.yun.baidu',
  `pic_num` int(11) unsigned NOT NULL DEFAULT 0 COMMENT 'pic num',
  `audio_num` int(11) unsigned NOT NULL DEFAULT 0 COMMENT 'audio num',
  `video_num` int(11) unsigned NOT NULL DEFAULT 0 COMMENT 'video num',
  `resource_num` int(11) unsigned NOT NULL DEFAULT 0 COMMENT 'resource num',
  `reply_type` int(11) unsigned NOT NULL DEFAULT 0 COMMENT 'reply type',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT 'status',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'update time',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'create time',
  `ext1` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'ext1',
  `ext2` varchar(1000) NOT NULL DEFAULT '' COMMENT 'ext2',
  PRIMARY KEY (`id`),
  UNIQUE KEY `reply_id`(`reply_id`),
  KEY `topic_id`(`topic_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT 'userReply';