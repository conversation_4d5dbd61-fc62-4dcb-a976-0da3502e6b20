<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * @brief     : 回复
 * <AUTHOR>
 * @date      2016-07-17
 * @version
 * @structs & methods(copied from idl.)
 */
class Service_Ntreply_Ntreply extends Service_Libs_Base {
    CONST NTREPLY_NMQ_TOPIC = 'ntreply';
    CONST NTREPLY_NMQ_ADD_REPLY_COMMAND = 'addReply';                 //发送回复NMQ命令
    CONST NTREPLY_NMQ_DEL_REPLY_COMMAND = 'delReply';                 //删除回复命令
    CONST NTREPLY_NMQ_ZAN_COMMAND = 'addZan';                         //用户赞NMQ命令
    CONST NTREPLY_NMQ_CAI_COMMAND = 'addCai';                         //用户踩NMQ命令
    CONST NTREPLY_NMQ_LOAD_DATA = 'loadData';


    CONST REDIS_HASH_USER_ZANCAI_PREFIX_KEY = 'user_zancai_';
    CONST REDIS_LIST_REPLY_ZAN_PREFIX_KEY = 'reply_zan_list_';
    CONST REDIS_LIST_REPLY_CAI_PREFIX_KEY = 'reply_cai_list_';
    CONST REDIS_HASH_WEFAN_VIRTUAL_USER_KEY = 'wefan_virtual_user';
    CONST REDIS_LOAD_DATA_LIST = 'reply_load_data_list_';
    CONST REDIS_THREAD_ID_LIST = 'wefan_thread_id_';

    const FLOOR_NUM = 5; // 默认返回的楼层数

    /**
     * @brief: 发布新贴
     * @param
     * @return
     * @version: 2.0
     **/
    public static function addThread($arrInput){
        if( !isset($arrInput['user_id']) || !isset($arrInput['content']) || !isset($arrInput['title'])
            || !isset($arrInput['tags']) || !isset($arrInput['reply_type']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //add topic
        $arrInput['private'] = isset($arrInput['private']) ? intval($arrInput['private']) : 0;
        if( $arrInput['private'] != 0 && $arrInput['private'] != 1 ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        
        if( !empty($arrInput['tags']) ){
            $arrInput['tags'] = json_decode($arrInput['tags'], true);
        }
        if( !empty($arrInput['cover']) ){
            $arrInput['cover'] = trim(urldecode($arrInput['cover']));
            $arrInput['cover'] = json_decode($arrInput['cover'], true);
            $arrInput['cover'] = array(
                'big_url' => strval($arrInput['cover']['bigPicURL']),
                'small_url' => strval($arrInput['cover']['smlPicURL']),
                'width' => intval($arrInput['cover']['width']),
                'height' => intval($arrInput['cover']['height']),
                'picId' => strval($arrInput['cover']['picID']),
                'type' => strval($arrInput['cover']['picType']),
            );
        }

        $arrRet = Tieba_Service::call('nttopic', 'addTopic', $arrInput, null, null, 'post', 'php', 'utf-8');
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] || intval($arrRet['data']['topic_id']) <= 0 ){
            Bingo_Log::warning(__FUNCTION__.' service nttopic.addTopic error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet['errno'];
        }
        $topic_id = intval($arrRet['data']['topic_id']);

        //add reply
        $arrInput['topic_id'] = $topic_id;
        $arrRet = self::addReply($arrInput);
        if(false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
            Bingo_Log::warning(__FUNCTION__.' thread.addReply error. [input='.serialize($arrInput).'][out='.serialize($arrRet).']');
            return $arrRet['errno'];
        }

        $data = array(
            'topic_id' => $topic_id,
            'reply_id' => intval($arrRet['data']['reply_id']),
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $data);
    }

    /**
     * @brief: 追更
     * @param
     * @return
     * @version: 2.0
     **/
    public static function addFloor($arrInput){
        return self::addReply($arrInput);
    }

    /**
     * @brief: 删除楼层
     * @param:
     * @return
     * @version: 2.0
     **/
    public static function delFloor($arrInput){
        return self::delReply($arrInput);
    }

    /**
     * @brief: 获取楼层信息
     * @param
     * @return
     * @version: 2.0
     **/
    public static function getFloorInfo($arrInput){
        if( !isset($arrInput['direc']) || !isset($arrInput['is_pv']) || !isset($arrInput['reply_id'])
            || !isset($arrInput['topic_id']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $direc = intval($arrInput['direc']);
        $is_pv = intval($arrInput['is_pv']);
        $reply_id = intval($arrInput['reply_id']);
        $topic_id = intval($arrInput['topic_id']);
        $user_id = intval($arrInput['user_id']);
        $is_push = intval($arrInput['is_push']);

        $res = array();
        $topic = array();
        $replys = array();
        $users = array();
        $userIdsArr = array();

        if( $direc < 0 || $is_pv < 0 || $reply_id < 0 || $topic_id <= 0 || $user_id < 0 ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //获取topic信息和用户话题关系
        $objRalMulti = new Tieba_Multi('reply_service_getFloorInfo');

        //获取话题详情
        $arrMultiInput = array(
            'serviceName' => 'nttopic',
            'method' => 'getTopicReplyList',
            'input' => array(
                'is_pv' => $is_pv,
                'topic_id' => $topic_id,
                'reply_id' => $reply_id,
                'order' => $direc, // order 1表示递增，0表示递减
                'rn' => self::FLOOR_NUM + 1, // 包括自己
            ),
            'ie' => 'utf-8',
        );
//        echo json_encode($arrMultiInput);die;
        $objRalMulti->register('getTopicReplyList', new Tieba_Service('nttopic'), $arrMultiInput);

        //获取用户话题关系
        if( $user_id > 0 ){
            $arrMultiInput = array(
                'serviceName' => 'ntuser',
                'method' => 'pushTopicStatus',
                'input' => array(
                    'user_id' => $user_id,
                    'topic_id' => $topic_id,
                ),
                'ie' => 'utf-8',
            );
            $objRalMulti->register('pushTopicStatus', new Tieba_Service('ntuser'), $arrMultiInput);
        }

        Bingo_Timer::start('multi_call');
        $arrMultiOut = $objRalMulti->call();
        Bingo_Timer::end('multi_call');

        $topic = array();
        $replyList = array();
//        echo json_encode($arrMultiOut);die;
        foreach( $arrMultiOut AS $key => $arrServiceOut ){
            if( false === $arrServiceOut || $arrServiceOut['errno'] != Tieba_Errcode::ERR_SUCCESS ){
                Bingo_Log::warning(__FUNCTION__.'service '.$key.' error. [topic_id='.$topic_id.', user_id='.$user_id.'][out='.serialize($arrServiceOut).']');
                continue;
            }

            //话题详情
            if( $key == 'getTopicReplyList' ){
                $topic = $arrServiceOut['data']['topic_info'];
                $topic['is_collected'] = 0;
                $topic['is_pushed'] = 0;
                if ($topic['status'] == 2 || $topic['status'] == 3 ){
                    // 已删除
                    $topic['is_del'] = 1;
                }
                $replyList = $arrServiceOut['data']['list'];
//                echo json_encode($replyList);die;
            }

            //用户话题关系
            if( $key == 'pushTopicStatus' && $user_id > 0 ){
                $data = $arrServiceOut['data'];
                $topic['is_collected'] = intval($data['is_collected']);
                $topic['is_pushed'] = intval($data['is_pushed']);
            }
        }

        /*
        $arrParams = array(
            'is_pv' => $is_pv,
            'topic_id' => $topic_id,
        );
        $arrRet = Tieba_Service::call('nttopic', 'getTopicReplyList', $arrParams, null, null, 'post', 'php', 'utf-8');
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' service nttopic.getTopicReplyList error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $data = $arrRet['data'];
        $replyList = $data['list'];
        $topic = $data['topic_info'];*/

        $userIdsArr[] = intval($topic['user_id']);
        if( empty($replyList) ){
            $res = [
                'topic' => [
                    'topic_id' => $topic_id,
                    'is_del' => 1,
                ],
            ];
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
        }
        /*
        if( intval($topic['private']) === 1 && $user_id !== intval($topic['user_id']) ){
            $res = [
                'topic' => $topic,
            ];
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
        }*/
        
        $count = count($replyList);
        $topic['top_reply_id'] = $replyList[0];
        $topic['bottom_reply_id'] = $replyList[$count-1];

        //获取reply信息
        $replyIds = array();
        do{
            $start = -1;
            $is_del = 0;

            if( $is_push > 0 ){
                $replyIds[] = $topic['bottom_reply_id'];
                break;
            }

            if( $reply_id > 0 ){
                foreach( $replyList AS $key => $rid ){
                    if( $rid == $reply_id ){
                        $start = $key;
                        break;
                    }
                }
            }else{
                //reply_id = 0 and is_pv=1 获取首楼数据
                $is_pv = 1;

            }

            if( $is_pv == 1 ){
                //由卡片进入回复详情页
                $is_del = ($start === -1) ? 1 : 0;
                $start = $start > -1 ? $start : 0;
                for(;$start<$count;$start++ ){
                    if( count($replyIds) === self::FLOOR_NUM ){
                        break;
                    }
                    $replyIds[] = $replyList[$start];
                }
            }else{
                if( $start === -1 ){
                    // 该楼层已经被删除，取倒数第2楼开始
                    if ($direc === 0){
                        //下拉，倒序
                        $start = $count - 1; // 最后一层
                        if ($start <= 0) {
                            $start = 1;
                        }
                    }
                    else {
                        // 要看的这一楼已经被删除，[0] 是第一楼 [$count-1]是最后一楼
                        // 上拉，升序
                        $start = 0;
                        if ($start >= $count) {
                            $start = -1;
                        }
                    }
                }
                // 进行到这，start代表是传入的reply_id的位置
                if( $direc === 0 ){
                    //下拉，逆向
                    $start -= 1;
                    if( !isset($replyList[$start]) ){
                        break;
                    }
                    for(;$start>=0;$start--){
                        if( count($replyIds) === self::FLOOR_NUM ){
                            break;
                        }
                        $replyIds[] = $replyList[$start];
                    }
                    $replyIds = array_reverse($replyIds);
                }else{
                    //上滑，正向
                    $start += 1;
                    if( !isset($replyList[$start]) ){
                        break;
                    }
//                    var_dump($start);die;
                    for(;$start<$count;$start++){
                        if( count($replyIds) === self::FLOOR_NUM ){
                            break;
                        }
                        $replyIds[] = $replyList[$start];
                    }
                }
            }
        }while(0);

        if( !empty($replyIds) ){
            $arrParams = array(
                'reply_ids' => $replyIds,
                'user_id' => $user_id,
            );
            $arrRet = self::mgetReplyInfo($arrParams);
            if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                Bingo_Log::warning(__FUNCTION__.' mgetReplyInfo error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                return self::_errRet($arrRet['errno']);
            }
            $replyInfos = $arrRet['data'];
        }

        //获取评论
        $commentInfos = array();
        $bottom_reply_id = $topic['bottom_reply_id'];
        if( !empty($replyInfos) ){
            $req = array();
            foreach($replyInfos AS $key=>$item){
                $only_echo = ($key == $bottom_reply_id) ? 0 : 1;
                $rn = ($only_echo == 0) ? 30 : 3;
                $req[] = array(
                    'only_echo' => $only_echo,
                    'pn' => 1,
                    'reply_id' => $key,
                    'rn' => $rn,
                );
            }
            if( !empty($req) ){
                $arrParams = array(
                    'req' => $req,
                    'user_id' => $user_id,
                );
                $arrRet = Tieba_Service::call('ntcomment', 'sgetCommentList', $arrParams, null, null, 'post', 'php', 'utf-8');
                if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                    Bingo_Log::warning(__FUNCTION__.' service ntcomment.sgetMyCommentList error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                }
                $commentInfos = !empty($arrRet['data']) ? $arrRet['data'] : array();
            }
        }

        if( !empty($replyInfos) ){
            foreach( $replyIds AS $rid ){
                $item = $replyInfos[$rid];
                if( empty($item) ){
                    continue;
                }
                $item['is_del'] = 0;
                $comment = array(
                    'list' => array(),
                    'page' => array(
                        'has_more' => 0,
                        'total_count' => 0,
                        'total_page' => 0,
                    ),
                );
                $comment = isset($commentInfos[$rid]) ? $commentInfos[$rid] : $comment;
                $item['comments'] = $comment;
                $replys[] = $item;

                if( !empty($comment['list']) ){
                    foreach($comment['list'] AS $i ){
                        $uid = intval($i['user_id']);
                        if( $uid <= 0 || in_array($uid, $userIdsArr) ){
                            continue;
                        }
                        $userIdsArr[] = $uid;
                    }
                }
            }
        }
        if( $is_pv && $is_del && $reply_id!=0){
            $replys[] = array(
                'reply_id' => $reply_id,
                'is_del' => 1,
            );
        }

        //批量获取用户信息
        if( !empty($userIdsArr) ){
            $arrParams = array(
                'from_uid' => $user_id,
                'user_ids' => $userIdsArr,
                'need_ext' => 1,
            );
            $arrRet = Tieba_Service::call('ntuser', 'getUserInfosAndRelations', $arrParams, null, null, 'post', 'php', 'utf-8');
            if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                Bingo_Log::warning(__FUNCTION__.' service ntuser.getUserInfosAndRelations error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            }else{
                $users = $arrRet['data']['user_infos'];
            }
        }

        $res = array(
            'replys' => $replys,
            'topic' => $topic,
            'users' => $users,
        );

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
    }

    /**
     * @brief  为分享页获取楼层信息
     * @param  $arrInput
     * @return array
     */
    public static function getFloorInfoForSharePage($arrInput){
        if( !isset($arrInput['direc']) || !isset($arrInput['is_pv']) || !isset($arrInput['reply_id'])
            || !isset($arrInput['topic_id']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $direc    = intval($arrInput['direc']);
        $is_pv    = intval($arrInput['is_pv']);
        $reply_id = intval($arrInput['reply_id']);
        $topic_id = intval($arrInput['topic_id']);
        $user_id  = intval($arrInput['user_id']);
        $is_push  = intval($arrInput['is_push']);

        $res = array();
        $topic = array();
        $replys = array();
        $users = array();
        $userIdsArr = array();

        if( $direc < 0 || $is_pv < 0 || $reply_id < 0 || $topic_id <= 0 || $user_id < 0 ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //获取topic信息和用户话题关系
        $objRalMulti = new Tieba_Multi('reply_service_getFloorInfo');

        //获取话题详情
        $arrMultiInput = array(
            'serviceName' => 'nttopic',
            'method' => 'getTopicReplyList',
            'input' => array(
                'is_pv' => $is_pv,
                'topic_id' => $topic_id,
                'reply_id' => $reply_id,
                'order' => $direc, // order 1表示递增，0表示递减
                'rn' => self::FLOOR_NUM + 1, // 包括自己
            ),
            'ie' => 'utf-8',
        );
        $objRalMulti->register('getTopicReplyList', new Tieba_Service('nttopic'), $arrMultiInput);

        //获取用户话题关系
        if( $user_id > 0 ){
            $arrMultiInput = array(
                'serviceName' => 'ntuser',
                'method' => 'pushTopicStatus',
                'input' => array(
                    'user_id' => $user_id,
                    'topic_id' => $topic_id,
                ),
                'ie' => 'utf-8',
            );
            $objRalMulti->register('pushTopicStatus', new Tieba_Service('ntuser'), $arrMultiInput);
        }

        Bingo_Timer::start('multi_call');
        $arrMultiOut = $objRalMulti->call();
        Bingo_Timer::end('multi_call');

        $topic = array();
        $replyList = array();
        foreach( $arrMultiOut AS $key => $arrServiceOut ){
            if( false === $arrServiceOut || $arrServiceOut['errno'] != Tieba_Errcode::ERR_SUCCESS ){
                Bingo_Log::warning(__FUNCTION__.'service '.$key.' error. [topic_id='.$topic_id.', user_id='.$user_id.'][out='.serialize($arrServiceOut).']');
                continue;
            }

            //话题详情
            if( $key == 'getTopicReplyList' ){
                $topic = $arrServiceOut['data']['topic_info'];
                $topic['is_collected'] = 0;
                $topic['is_pushed'] = 0;
                if ($topic['status'] == 2 || $topic['status'] == 3 ){
                    // 已删除
                    $topic['is_del'] = 1;
                }
                $replyList = $arrServiceOut['data']['list'];
//                echo json_encode($replyList);die;
            }

            //用户话题关系
            if( $key == 'pushTopicStatus' && $user_id > 0 ){
                $data = $arrServiceOut['data'];
                $topic['is_collected'] = intval($data['is_collected']);
                $topic['is_pushed'] = intval($data['is_pushed']);
            }
        }

        $userIdsArr[] = intval($topic['user_id']);
        if( empty($replyList) ){
            $res = [
                'topic' => [
                    'topic_id' => $topic_id,
                    'is_del' => 1,
                ],
            ];
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
        }
        $count = count($replyList);
        $topic['top_reply_id'] = $replyList[0];
        $topic['bottom_reply_id'] = $replyList[$count-1];

        //获取reply信息
        $replyIds = array();
        do{
            $start = -1;
            $is_del = 0;

            if( $is_push > 0 ){
                $replyIds[] = $topic['bottom_reply_id'];
                break;
            }

            if( $reply_id > 0 ){
                foreach( $replyList AS $key => $rid ){
                    if( $rid == $reply_id ){
                        $start = $key;
                        break;
                    }
                }
            }

            if( $is_pv == 1 ){
                //由卡片进入回复详情页
                $is_del = ($start === -1) ? 1 : 0;
                $start = $start > -1 ? $start : 0;
                for(;$start<$count;$start++ ){
                    if( count($replyIds) === self::FLOOR_NUM ){
                        break;
                    }
                    $replyIds[] = $replyList[$start];
                }
            }else{
                if( $start === -1 ){
                    // 该楼层已经被删除，取倒数第2楼开始
                    if ($direc === 0){
                        //下拉，倒序
                        $start = $count - 2;
                        if ($start < 0) {
                            $start = 1;
                        }
                    }
                    else {
                        // 上拉，升序
                        $start = 1;
                        if ($start > $count) {
                            $start = -1;
                        }
                    }
                }
                if( $direc === 0 ){
                    //下拉，逆向
                    $start -= 1;
                    if( !isset($replyList[$start]) ){
                        break;
                    }
                    for(;$start>=0;$start--){
                        if( count($replyIds) === self::FLOOR_NUM ){
                            break;
                        }
                        $replyIds[] = $replyList[$start];
                    }
                    $replyIds = array_reverse($replyIds);
                }else{
                    //上滑，正向
                    $start += 1;
                    if( !isset($replyList[$start]) ){
                        break;
                    }
                    for(;$start<$count;$start++){
                        if( count($replyIds) === self::FLOOR_NUM ){
                            break;
                        }
                        $replyIds[] = $replyList[$start];
                    }
                }
            }
        }while(0);

        if( !empty($replyIds) ){
            $arrParams = array(
                'reply_ids' => $replyIds,
                'user_id' => $user_id,
            );
            $arrRet = self::mgetReplyInfo($arrParams);
            if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                Bingo_Log::warning(__FUNCTION__.' mgetReplyInfo error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                return self::_errRet($arrRet['errno']);
            }
            $replyInfos = $arrRet['data'];
        }

        //获取评论
        $commentInfos = array();
        $bottom_reply_id = $topic['bottom_reply_id'];
        if( !empty($replyInfos) ){
            $req = array();
            foreach($replyInfos AS $key=>$item){
                $req[] = array(
                    'only_echo' => 1,
                    'pn' => 1,
                    'reply_id' => $key,
                    'rn' => 3,
                );
            }
            if( !empty($req) ){
                $arrParams = array(
                    'req' => $req,
                    'user_id' => $user_id,
                );
                $arrRet = Tieba_Service::call('ntcomment', 'sgetCommentList', $arrParams, null, null, 'post', 'php', 'utf-8');
                if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                    Bingo_Log::warning(__FUNCTION__.' service ntcomment.sgetMyCommentList error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                }
                $commentInfos = !empty($arrRet['data']) ? $arrRet['data'] : array();
            }
        }

        if( !empty($replyInfos) ){
            foreach( $replyIds AS $rid ){
                $item = $replyInfos[$rid];
                if( empty($item) ){
                    continue;
                }
                $item['is_del'] = 0;
                $comment = array(
                    'list' => array(),
                    'page' => array(
                        'has_more' => 0,
                        'total_count' => 0,
                        'total_page' => 0,
                    ),
                );
                $comment = isset($commentInfos[$rid]) ? $commentInfos[$rid] : $comment;

                if (!empty($comment['list'])) {
                    $comment['list'] = array_splice($comment['list'], 0, 3);
                }

                $item['comments'] = $comment;

                $replys[] = $item;

                if( !empty($comment['list']) ){
                    foreach($comment['list'] AS $i ){
                        $uid = intval($i['user_id']);
                        if( $uid <= 0 || in_array($uid, $userIdsArr) ){
                            continue;
                        }
                        $userIdsArr[] = $uid;
                    }
                }
            }
        }
        if( $is_pv && $is_del && $reply_id!=0){
            $replys[] = array(
                'reply_id' => $reply_id,
                'is_del' => 1,
            );
        }

        //批量获取用户信息
        if( !empty($userIdsArr) ){
            $arrParams = array(
                'from_uid' => $user_id,
                'user_ids' => $userIdsArr,
                'need_ext' => 1,
            );
            $arrRet = Tieba_Service::call('ntuser', 'getUserInfosAndRelations', $arrParams, null, null, 'post', 'php', 'utf-8');
            if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                Bingo_Log::warning(__FUNCTION__.' service ntuser.getUserInfosAndRelations error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            }else{
                $users = $arrRet['data']['user_infos'];
            }
        }

        $res = array(
            'replys' => $replys,
            'topic' => $topic,
            'users' => $users,
        );

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
    }

    /**
     * @brief: 获取内容发布平台登录用户的马甲账户列表
     * @param
     * @return
     **/
    public static function getVirtualUserList($arrInput){
        if( !isset($arrInput['user_id']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $user_id = intval($arrInput['user_id']);
        $res = array();

        if( $user_id <= 0 ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //获取用户信息
        $arrParams = array(
            'user_id' => $user_id,
        );
        $arrRet = Tieba_Service::call('ntuser', 'getUserInfo', $arrParams, null, null, 'post', 'php', 'utf-8');
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] || empty($arrRet['data']) ){
            Bingo_Log::warning(__FUNCTION__.' service ntuser.getUserInfo error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $userInfo = $arrRet['data']['user_info'];
        if( intval($userInfo['user_id']) != $user_id ){
            Bingo_Log::warning(__FUNCTION__.' user is not exist. [user_id='.$user_id.']');
            return self::_errRet(Tieba_Errcode::ERR_DIFANG_USER_NOT_EXIST);
        }

        $arrParams = array(
            'key' => self::REDIS_HASH_WEFAN_VIRTUAL_USER_KEY,
            'field' => $user_id,
        );
        $ret = Util_Redis::HGET($arrParams);
        if( false === $ret ){
            Bingo_Log::warning(__FUNCTION__.' redis call fail');
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        if( $ret !== null ){
            $res[$user_id] = json_decode($ret, true);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
    }

    /**
     * @brief: 添加发布平台的白名单用户，及马甲账号
     * @param
     * @return
     **/
    public static function addVirtualUser($arrInput){
        if( !isset($arrInput['user_id']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $user_id = intval($arrInput['user_id']);
        $virtual_user = trim($arrInput['virtural_user']);
        $virUserArr = array();
        $val = array();

        if( $user_id <= 0 ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        if( !empty($virtual_user) ){
            $virtual_user = trim($virtual_user, ',');
            $virUserArr = explode(',', $virtual_user);
        }

        //获取用户信息
        $arrParams = array(
            'user_id' => $user_id,
        );
        $arrRet = Tieba_Service::call('ntuser', 'getUserInfo', $arrParams, null, null, 'post', 'php', 'utf-8');
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] || empty($arrRet['data']) ){
            Bingo_Log::warning(__FUNCTION__.' service ntuser.getUserInfo error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $userInfo = $arrRet['data']['user_info'];
        if( intval($userInfo['user_id']) != $user_id ){
            Bingo_Log::warning(__FUNCTION__.' user is not exist. [user_id='.$user_id.']');
            return self::_errRet(Tieba_Errcode::ERR_DIFANG_USER_NOT_EXIST);
        }

        $arrParams = array(
            'key' => self::REDIS_HASH_WEFAN_VIRTUAL_USER_KEY,
            'field' => $user_id,
        );
        $ret = Util_Redis::HGET($arrParams);
        if( false === $ret ){
            Bingo_Log::warning(__FUNCTION__.' redis call fail');
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        if( $ret !== null && empty($virUserArr) ){
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $val = ($ret === null) ? array() : json_decode($ret, true);

        //去重
        if( !empty($virUserArr) && !empty($val) ){
            foreach( $virUserArr AS $key => $uid ){
                if( isset($val[$uid]) ){
                    unset($virUserArr[$key]);
                }
            }
        }

        if( !empty($virUserArr) ){
            $arrParams = array(
                'user_ids' => $virUserArr,
            );
            $arrRet = Tieba_Service::call('ntuser', 'getUserInfos', $arrParams, null, null, 'post', 'php', 'utf-8');
            if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                Bingo_Log::warning(__FUNCTION__.' service ntuser.getUserInfos error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                return $arrRet;
            }
            $userInfos = $arrRet['data']['user_infos'];

            foreach( $userInfos AS $key => $item ){
                $nickName = trim($item['nickname']);
                $val[$key] = $nickName;
            }
        }

        $val = json_encode($val);
        $arrParams = array(
            'key' => self::REDIS_HASH_WEFAN_VIRTUAL_USER_KEY,
            'field' => $user_id,
            'value' => $val,
        );
        $ret = Util_Redis::HSET($arrParams);
        if( false === $ret ){
            Bingo_Log::warning(__FUNCTION__.' redis HSET fail.');
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * @brief: 设置发布平台的白名单用户，及马甲账号
     * @param
     * @return
     **/
    public static function setVirtualUser($arrInput){
        if( !isset($arrInput['user_id']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $user_id = intval($arrInput['user_id']);
        $virtual_user = intval($arrInput['virtural_user']);
        $virUserArr = array();
        $val = array();

        if( $user_id <= 0 || $virtual_user<=0){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //获取用户信息
        $arrParams = array(
            'user_id' => $user_id,
        );
        $arrRet = Tieba_Service::call('ntuser', 'getUserInfo', $arrParams, null, null, 'post', 'php', 'utf-8');
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] || empty($arrRet['data']) ){
            Bingo_Log::warning(__FUNCTION__.' service ntuser.getUserInfo error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $userInfo = $arrRet['data']['user_info'];
        if( intval($userInfo['user_id']) != $user_id ){
            Bingo_Log::warning(__FUNCTION__.' user is not exist. [user_id='.$user_id.']');
            return self::_errRet(Tieba_Errcode::ERR_DIFANG_USER_NOT_EXIST);
        }


        //获取用户信息
        $arrParams = array(
            'user_id' => $virtual_user,
        );
        $arrRet = Tieba_Service::call('ntuser', 'getUserInfo', $arrParams, null, null, 'post', 'php', 'utf-8');
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] || empty($arrRet['data']) ){
            Bingo_Log::warning(__FUNCTION__.' service ntuser.getUserInfo error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $userInfo = $arrRet['data']['user_info'];


        $val[$userInfo['user_id']] = $userInfo['nickname'];

        $val = json_encode($val);
        $arrParams = array(
            'key' => self::REDIS_HASH_WEFAN_VIRTUAL_USER_KEY,
            'field' => $user_id,
            'value' => $val,
        );
        $ret = Util_Redis::HSET($arrParams);
        if( false === $ret ){
            Bingo_Log::warning(__FUNCTION__.' redis HSET fail.');
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $str
     *
     * @return mixed
     */
    protected static function filterEmoji($str)
    {
        $str = preg_replace_callback(
            '/./u',
            function (array $match) {
                return strlen($match[0]) >= 4 ? '' : $match[0];
            },
            $str);

        return $str;
    }

    /**
     * @brief: 添加回复内容
     * @param
     * @return
     **/
    public static function addReply($arrInput){
        if( !isset($arrInput['user_id']) || !isset($arrInput['topic_id']) || !isset($arrInput['reply_type'])
            || !isset($arrInput['content']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $is_daoliu = intval($arrInput['is_daoliu']);
        //user
        $user_id = intval($arrInput['user_id']);
        $user_ip = intval($arrInput['user_ip']);
        $user_name = strval($arrInput['user_name']);
        $nickname = strval($arrInput['nickname']);
        $avatar = strval($arrInput['avatar']);
        $user_type = intval($arrInput['user_type']);
        $user_url = strval($arrInput['user_url']);
        //topic
        $topic_id = intval($arrInput['topic_id']);
        $title = strval($arrInput['title']);
        $tags = $arrInput['tags'];
        $private = intval($arrInput['private']);
        //reply
        $reply_type = intval($arrInput['reply_type']);
        $create_time = intval($arrInput['create_time']);
        $create_time = $create_time > 0 ? $create_time : time();
        $content = strval($arrInput['content']);
        $content = urldecode($content);
        $pic = strval($arrInput['pic']);
        $pic = urldecode($pic);
        $pic = Util_Util::formatReplyPic($pic);
        $picOri = $pic;
        $video = strval($arrInput['video']);
        $video = urldecode($video);
        $audio = strval($arrInput['audio']);
        $audio = urldecode($audio);
        $link = strval($arrInput['link']);
        $link = urldecode($link);
        $resource = strval($arrInput['resource']);
        $resource = urldecode($resource);
        $floor_num = isset($arrInput['floor_num']) ? intval($arrInput['floor_num']) : 1;
        $pic_num = 0;
        $audio_num = 0;
        $video_num = 0;
        $resource_num = 0;
        $res = array();

        if( $user_id <= 0 || $topic_id <= 0 || !in_array($reply_type, Model_Ntreply_Ntreply::$_validReplyType) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        
        if( $private != 0 && $private != 1 ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        if( strlen($content) > Model_Ntreply_Ntreply::REPLY_MAX_CONTENT ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        Bingo_Log::pushNotice('uip', $user_ip);

        //计算用户上传富媒体数量
        if( !empty($pic) ){
            $picArr = json_decode($pic, true);
            $pic_num = count($picArr);
        }
        if( !empty($audio) ){
            $audioArr = json_decode($audio, true);
            $audio_num = count($audioArr);
        }
        if( !empty($video) ){
            $videoArr = json_decode($video, true);
            $video_num = count($videoArr);
        }
        if( !empty($resource) ){
            $resourceArr = json_decode($resource, true);
            $resource_num = count($resourceArr);
        }

        //获取回复ID
        $reply_id = Util_Util::getReplyId();
        if( false === $reply_id ){
            Bingo_Log::warning(__FUNCTION__.' get reply id error.');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        // 去除 Emoji 表情
        if( $reply_type == Model_Ntreply_Ntreply::REPLY_TYPE_DEFAULT ){
            $content = trim($content);
            $contentArr = json_decode($content, true);
            if (is_array($contentArr)){
                $newContent = [];
                foreach( $contentArr AS $item ){
                    if( $item['type'] == 'unstyled' ){
                        $text = trim($item['text']);
                        if( !empty($text) ){
                            $item['text'] = self::filterEmoji($text);
                        }else {
                            $item['text'] = $text;
                        }
                    }
                    $newContent[] = $item;
                }
                $content = json_encode($newContent);
            }
        }

        //获取内容摘要
        $digest = Util_Util::getDigest($content, $pic, $video, $resource, $reply_type);
        $digest = trim($digest);

        //根据话题ID获取话题信息
        if( !$is_daoliu ){
            $topicInfo = self::getTopicInfo($topic_id);
            if( $user_id != intval($topicInfo['user_id']) ){
                Bingo_Log::warning(__FUNCTION__.' user can not reply this topic');
                return self::_errRet(Tieba_Errcode::ERR_INTERESTMAN_USER_NOT_POWER);
            }
            if( $private != intval($topicInfo['private']) ){
                $arrParams = array(
                    'user_id' => $user_id,
                    'topic_id' => $topic_id,
                    'private' => $private,
                );
                $arrRet = Tieba_Service::call('nttopic', 'setPrivate', $arrParams, null, null, 'post', 'php', 'utf-8');
                if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] || empty($arrRet['data']) ){
                    Bingo_Log::warning(__FUNCTION__.' service nttopic.setPrivate error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                }
            }
            $floor_num = intval($topicInfo['floor_num']) + 1;
            $title = strval($topicInfo['title']);
            $tags = $topicInfo['tags'];

            //根据用户ID获取用户信息
            $arrParams = array(
                'user_id' => $user_id,
            );
            $arrRet = Tieba_Service::call('ntuser', 'getUserInfo', $arrParams, null, null, 'post', 'php', 'utf-8');
            if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] || empty($arrRet['data']) ){
                Bingo_Log::warning(__FUNCTION__.' service ntuser.getUserInfo error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                return $arrRet;
            }
            $userInfo = $arrRet['data']['user_info'];
            if( intval($userInfo['user_id']) != $user_id ){
                Bingo_Log::warning(__FUNCTION__.' user is not exist. [user_id='.$user_id.']');
                return self::_errRet(Tieba_Errcode::ERR_DIFANG_USER_NOT_EXIST);
            }
            $user_name = strval($userInfo['user_name']);
            $nickname = strval($userInfo['nickname']);
            $avatar = $userInfo['avatar'];
            $user_type = $userInfo['user_type'];
            $user_url = $userInfo['user_url'];

            //UEG异步策略
            $arrParams = array(
                'user_id' => $user_id,
                'user_name' => $user_name,
                'content' => $content,
                'pic' => $picOri,
                'video' => $video,
                'reply_id' => $reply_id,
                'user_ip' => $user_ip,
            );
            $res = self::_asynUEG($arrParams);
            Bingo_Log::pushNotice('asynUEG', $res);
        }

        //图文回复类型，pic只保存5张图片
        /*
        if( $reply_type == Model_Ntreply_Ntreply::REPLY_TYPE_DEFAULT && $pic_num > 5 ){
            $tmpArr = array();
            foreach ( $picArr AS $key => $item ){
                if( $key > 4 ){
                    break;
                }
                $tmpArr[] = $item;
            }
            $pic = json_encode($tmpArr);
        }*/

        $arrParams = array(
            'field' => array(
                'reply_id' => $reply_id,
                'topic_id' => $topic_id,
                'user_id' => $user_id,
                'user_name' => $user_name,
                'nickname' => $nickname,
                'title' => $title,
                'content' => $content,
                'digest' => $digest,
                'pic' => $pic,
                'audio' => $audio,
                'video' => $video,
                'link' => $link,
                'resource' => $resource,
                'pic_num' => $pic_num,
                'audio_num' => $audio_num,
                'video_num' => $video_num,
                'resource_num' => $resource_num,
                'reply_type' => $reply_type,
                'floor_num' => $floor_num,
                'update_time' => $create_time,
                'create_time' => $create_time,
            ),
        );
        $arrRet = Model_Ntreply_Ntreply::insert($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' Ntreply insert sql error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $replyInfo = $arrParams['field'];
        $replyInfo['avatar'] = strval($avatar);
        $replyInfo['user_type'] = intval($user_type);
        $replyInfo['user_url'] = strval($user_url);
        $replyInfo['create_time'] = $create_time;
        $replyInfo['has_resource'] = !empty($resource) ? 1 : 0;
        $replyInfo['tags'] = $tags;
        $replyInfo['pic'] = $picOri;
        $replyInfo = Util_Util::formatReplyOut($replyInfo);
        $replyInfo['is_daoliu'] = $is_daoliu;

        //发送NMQ
        $arrParams = array(
            'command' => self::NTREPLY_NMQ_ADD_REPLY_COMMAND,
            'reqs' => $replyInfo,
        );
        $sendNmq = self::_sendNMQ($arrParams);
        if( !$sendNmq ){
            Bingo_Log::warning(__FUNCTION__.' send nmq error. [input='.serialize($arrParams).']');
        }

        //添加User feed流
        /*
        $data = array(
            'topic_id' => $topic_id,
            'reply_id' => $reply_id,
            'reply_type' => $reply_type,
        );
        $arrParams = array(
            'owner_id' => $user_id,
            'owner_type' => 'user',
            'subject_id'  => $user_id,
            'subject_type'  => 'user',
            'action'  => 'add',
            'object_id'  => $reply_id,
            'happen_at' => time(),
            'object_type' => 'reply',
            'data' => json_encode($data),
        );
        $arrRet = Tieba_Service::call('ntfeed', 'addFeed', $arrParams, null, null, 'post', 'php', 'utf-8');
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' service ntfeed.addFeed error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
        }*/

        //添加topic Feed流
        /*
        $data = array(
            'msg_type' => 8,
            'from_uid' => $user_id,
            'topic_id' => $topic_id,
            'reply_id' => $reply_id,
            'content' => strval($nickname).'发布了回复',
        );
        $arrParams = array(
            'owner_id' => $topic_id,
            'owner_type' => 'topic',
            'subject_id'  => $user_id,
            'subject_type'  => 'user',
            'action'  => 'add',
            'object_id'  => $reply_id,
            'happen_at' => time(),
            'object_type' => 'reply',
            'data' => json_encode($data),
        );
        $arrRet = Tieba_Service::call('ntfeed', 'addFeed', $arrParams, null, null, 'post', 'php', 'utf-8');
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' service ntfeed.addFeed error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
        }*/

        //发送IM
        /*
        if( $user_id != $topicInfo['user_id'] ){
            $arrParams = array(
                'user_id' => intval($topicInfo['user_id']),
                'msg' => array(
                    'msg_type' => 6,
                    'user_id' => intval($topicInfo['user_id']),
                    'from_uid' => $user_id,
                    'reply_id' => $reply_id,
                    'topic_id' => $topic_id,
                    'content' => strval($userInfo['nickname']).'发布了回复',
                ),
            );
            $arrRet = Tieba_Service::call('ntgroup', 'sendUserMsg', $arrParams, null, null, 'post', 'php', 'utf-8');
            if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                Bingo_Log::warning(__FUNCTION__.' service nrgroup.sendUserMsg error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            }
        }*/

        $replyInfo['user'] = array(
            'user_id' => $replyInfo['user_id'],
            'user_name' => $replyInfo['user_name'],
            'user_type' => $replyInfo['user_type'],
            'user_url' => $replyInfo['user_url'],
            'nickname' => $replyInfo['nickname'],
            'avatar' => $replyInfo['avatar'],
        );

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $replyInfo);
    }

    /**
     * @brief: 点赞
     * @param
     * @return
     * @version: 2.0
     **/
    public static function addZan($arrInput){
        if( !isset($arrInput['user_id']) || !isset($arrInput['reply_id']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $user_id = intval($arrInput['user_id']);
        $reply_id = intval($arrInput['reply_id']);

        if( $user_id <= 0 || $reply_id <= 0 ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //根据用户ID获取用户信息
        $arrParams = array(
            'user_id' => $user_id,
        );
        $arrRet = Tieba_Service::call('ntuser', 'getUserInfo', $arrParams, null, null, 'post', 'php', 'utf-8');
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] || empty($arrRet['data']) ){
            Bingo_Log::warning(__FUNCTION__.' service ntuser.getUserInfo error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $userInfo = $arrRet['data']['user_info'];

        //根据reply_id获取回复信息
        $arrParams = array(
            'reply_id' => $reply_id,
        );
        $arrRet = self::getReplyInfoByReplyId($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' getReplyInfoByReplyId error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $replyInfo = $arrRet['data'];
        if( empty($replyInfo) || $replyInfo['status'] == Model_Ntreply_Ntreply::REPLY_STATUS_DEL ){
            Bingo_Log::warning(__FUNCTION__.' reply id is invalid. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return self::_errRet(Tieba_Errcode::ERR_ANTI_POST_LOCKED);
        }
        $to_uid = intval($replyInfo['user_id']);

        $errno = Tieba_Errcode::ERR_SUCCESS;
        do{
            //验证用户是否可以踩|赞
            $canZan = self::_canZanCai($user_id, $reply_id);
            if( !$canZan ){
                Bingo_Log::warning(__FUNCTION__.' user already zan. [user_id='.$user_id.', reply_id='.$reply_id.']');
                break;
                //return self::_errRet(Tieba_Errcode::ERR_ZAN_HAD_LIKED);
            }

            //更新用户赞踩列表
            $res = self::_updateUserZanCai($user_id, $reply_id, 1);
            if( false === $res ){
                Bingo_Log::warning(__FUNCTION__.' update user zan error.');
                $errno = Tieba_Errcode::ERR_REDIS_CALL_FAIL;
                break;
            }

            //更新回复的zan_num
            $arrParams = array(
                'reply_id' => $reply_id,
                'value' => 1,
            );
            $arrRet = self::updateZanCaiNum($arrParams);
            if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                Bingo_Log::warning(__FUNCTION__.' updateZanCaiNum zan num error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                $errno = $arrRet['errno'];
                break;
            }

            //更新回复的赞列表
            $res = self::_updateReplyZanCaiList($reply_id, $user_id, 1);
            if( false === $res ){
                Bingo_Log::warning(__FUNCTION__.' update reply zancai list error. [reply_id='.$reply_id.', user_id='.$user_id.']');
            }

            //发送NMQ
            $arrParams = array(
                'command' => self::NTREPLY_NMQ_ZAN_COMMAND,
                'reqs' => array(
                    'action' => 'addReplyZan',
                    'user_id' => $user_id,
                    'to_uid' => $to_uid,
                    'topic_id' => $replyInfo['topic_id'],
                    'reply_id' => $reply_id,
                    'value' => 1,
                    'time' => time(),
                ),
            );
            $arrRet = self::_sendNMQ($arrParams);
            if( false === $arrRet ){
                Bingo_Log::warning(__FUNCTION__.' _sendNMQ error.');
            }

            //发IM消息
            if( $user_id != $to_uid && false == Dl_ReplyCache::getAddReply($replyInfo['topic_id'], $user_id)){
                $arrParams = array(
                    'user_id' => $to_uid,
                    'msg' => array(
                        'msg_type' => 5,
                        'user_id' => $to_uid,
                        'from_uid' => $user_id,
                        'reply_id' => $reply_id,
                        'topic_id' => $replyInfo['topic_id'],
                        'content' => strval($userInfo['nickname']).'赞了你的文章',
                    ),
                );
                $arrRet = Tieba_Service::call('ntgroup', 'sendUserMsg', $arrParams, null, null, 'post', 'php', 'utf-8');
                if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                    Bingo_Log::warning(__FUNCTION__.' service nrgroup.sendUserMsg error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                }
                if(false == Dl_ReplyCache::addAddReply($replyInfo['topic_id'], $user_id)){
                    Bingo_Log::warning("Dl_ReplyCache::addAddReply $user_id failed ".$replyInfo['topic_id']);
                }
            }
        }while(0);

        if( $errno !== Tieba_Errcode::ERR_SUCCESS ){
            return self::_errRet($errno);
        }

        //获取当前回复的总赞数
        $num = self::_getReplyZanCaiNum($reply_id, 1);

        $res = array(
            'zan_num' => $num,
            'topic_id' => $replyInfo['topic_id'],
        );

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
    }

    /**
     * @brief: 取消点赞
     * @param
     * @return
     * @version: 2.0
     **/
    public static function cancelZan($arrInput){
        if( !isset($arrInput['user_id']) || !isset($arrInput['reply_id']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $user_id = intval($arrInput['user_id']);
        $reply_id = intval($arrInput['reply_id']);

        if( $user_id <= 0 || $reply_id <= 0 ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //根据用户ID获取用户信息
        $arrParams = array(
            'user_id' => $user_id,
        );
        $arrRet = Tieba_Service::call('ntuser', 'getUserInfo', $arrParams, null, null, 'post', 'php', 'utf-8');
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] || empty($arrRet['data']) ){
            Bingo_Log::warning(__FUNCTION__.' service ntuser.getUserInfo error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $userInfo = $arrRet['data']['user_info'];

        //根据reply_id获取回复信息
        $arrParams = array(
            'reply_id' => $reply_id,
        );
        $arrRet = self::getReplyInfoByReplyId($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' getReplyInfoByReplyId error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $replyInfo = $arrRet['data'];
        if( empty($replyInfo) || $replyInfo['status'] == Model_Ntreply_Ntreply::REPLY_STATUS_DEL ){
            Bingo_Log::warning(__FUNCTION__.' reply id is invalid. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return self::_errRet(Tieba_Errcode::ERR_ANTI_POST_LOCKED);
        }
        $to_uid = intval($replyInfo['user_id']);

        $errno = Tieba_Errcode::ERR_SUCCESS;
        do{
            //验证用户是否点赞过
            $canZan = self::_canZanCai($user_id, $reply_id);
            if( $canZan ){
                Bingo_Log::warning(__FUNCTION__.' user do not zan this reply. [user_id='.$user_id.', reply_id='.$reply_id.']');
                break;
                //return self::_errRet(Tieba_Errcode::ERR_ZAN_HAD_LIKED);
            }

            //删除用户赞数据
            $res = self::_delUserZan($user_id, $reply_id);
            if( false === $res ){
                Bingo_Log::warning(__FUNCTION__.' update user zan error.');
                $errno = Tieba_Errcode::ERR_REDIS_CALL_FAIL;
                break;
            }

            //更新回复的zan_num
            if( intval($replyInfo['zan_num']) >= 1 ){
                $arrParams = array(
                    'reply_id' => $reply_id,
                    'value' => 1,
                    'num' => -1,
                );
                $arrRet = self::updateZanCaiNum($arrParams);
                if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                    Bingo_Log::warning(__FUNCTION__.' updateZanCaiNum zan num error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                    $errno = $arrRet['errno'];
                    break;
                }
            }

            //更新回复的赞列表
            $res = self::_updateReplyZanCaiList($reply_id, $user_id, 1, 'del');
            if( false === $res ){
                Bingo_Log::warning(__FUNCTION__.' update reply zancai list error. [reply_id='.$reply_id.', user_id='.$user_id.']');
            }

            //发送NMQ
            $arrParams = array(
                'command' => self::NTREPLY_NMQ_ZAN_COMMAND,
                'reqs' => array(
                    'action' => 'cancelReplyZan',
                    'user_id' => $user_id,
                    'to_uid' => $to_uid,
                    'topic_id' => $replyInfo['topic_id'],
                    'reply_id' => $reply_id,
                    'value' => 0,
                    'time' => time(),
                ),
            );
            $arrRet = self::_sendNMQ($arrParams);
            if( false === $arrRet ){
                Bingo_Log::warning(__FUNCTION__.' _sendNMQ error.');
            }

            //发IM消息
            /*
            if( $user_id != $to_uid ){
                $arrParams = array(
                    'user_id' => $to_uid,
                    'msg' => array(
                        'msg_type' => 5,
                        'user_id' => $to_uid,
                        'from_uid' => $user_id,
                        'reply_id' => $reply_id,
                        'topic_id' => $replyInfo['topic_id'],
                        'content' => strval($userInfo['nickname']).'赞了你的回复',
                    ),
                );
                $arrRet = Tieba_Service::call('ntgroup', 'sendUserMsg', $arrParams, null, null, 'post', 'php', 'utf-8');
                if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                    Bingo_Log::warning(__FUNCTION__.' service nrgroup.sendUserMsg error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                }
            }*/
        }while(0);

        if( $errno !== Tieba_Errcode::ERR_SUCCESS ){
            return self::_errRet($errno);
        }

        //获取当前回复的总赞数
        $num = self::_getReplyZanCaiNum($reply_id, 1);

        $res = array(
            'zan_num' => $num,
            'topic_id' => $replyInfo['topic_id'],
        );

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
    }

    /**
     * @brief: 点踩
     * @param
     * @return
     **/
    public static function addCai($arrInput){
        if( !isset($arrInput['user_id']) || !isset($arrInput['reply_id']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $user_id = intval($arrInput['user_id']);
        $reply_id = intval($arrInput['reply_id']);

        if( $user_id <= 0 || $reply_id <= 0 ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //根据reply_id获取回复信息
        $arrParams = array(
            'reply_id' => $reply_id,
        );
        $arrRet = self::getReplyInfoByReplyId($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' getReplyInfoByReplyId error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $replyInfo = $arrRet['data'];
        if( empty($replyInfo) || $replyInfo['status'] == Model_Ntreply_Ntreply::REPLY_STATUS_DEL ){
            Bingo_Log::warning(__FUNCTION__.' reply id is invalid. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return self::_errRet(Tieba_Errcode::ERR_ANTI_POST_LOCKED);
        }
        $to_uid = intval($replyInfo['user_id']);

        //验证用户是否可以踩|赞
        $canZan = self::_canZanCai($user_id, $reply_id);
        if( !$canZan ){
            Bingo_Log::warning(__FUNCTION__.' user already zan. [user_id='.$user_id.', reply_id='.$reply_id.']');
            return self::_errRet(Tieba_Errcode::ERR_ZAN_HAD_LIKED);
        }

        //更新用户赞踩列表
        $res = self::_updateUserZanCai($user_id, $reply_id, 0);
        if( false === $res ){
            Bingo_Log::warning(__FUNCTION__.' update user zan error.');
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        //更新回复的cai_num
        $arrParams = array(
            'reply_id' => $reply_id,
            'value' => 0,
        );
        $arrRet = self::updateZanCaiNum($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' updateZanCaiNum cai num error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }

        //更新回复的踩LIST
        $res = self::_updateReplyZanCaiList($reply_id, $user_id, 0);
        if( false === $res ){
            Bingo_Log::warning(__FUNCTION__.' update reply zancai list error. [reply_id='.$reply_id.', user_id='.$user_id.']');
        }

        //发送NMQ
        $arrParams = array(
            'command' => self::NTREPLY_NMQ_CAI_COMMAND,
            'reqs' => array(
                'user_id' => $user_id,
                'to_uid' => $to_uid,
                'topic_id' => $replyInfo['topic_id'],
                'reply_id' => $reply_id,
                'value' => 0,
                'time' => time(),
            ),
        );
        $arrRet = self::_sendNMQ($arrParams);
        if( false === $arrRet ){
            Bingo_Log::warning(__FUNCTION__.' _sendNMQ error.');
        }

        //获取当前回复的总踩数
        $num = self::_getReplyZanCaiNum($reply_id, 0);

        $res = array(
            'cai_num' => $num,
            'topic_id' => $replyInfo['topic_id'],
        );

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
    }

    /**
     * @brief: 获取点赞列表
     * @param
     * @return
     **/
    public static function getZanList($arrInput){
        if( !isset($arrInput['reply_id']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $reply_id = intval($arrInput['reply_id']);
        $pre_uid = intval($arrInput['pre_uid']);
        $rn = isset($arrInput['rn']) ? intval($arrInput['rn']) : 20;
        $start = 0;
        $stop = 0;
        $key = self::REDIS_LIST_REPLY_ZAN_PREFIX_KEY.$reply_id;
        $zan_num = 0;
        $list = array();
        $uids = array();

        if( $reply_id <= 0 || $pre_uid < 0 || $rn <= 0 ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //计算起始index
        if( $pre_uid > 0 ){
            $arrParams = array(
                'key' => $key,
                'member' => $pre_uid,
            );
            $ret = Util_Redis::ZREVRANK($arrParams);
            if( false === $ret ){
                Bingo_Log::warning(__FUNCTION__.' redis ZREVRANK error. [input='.serialize($arrParams).']');
                return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
            }
            if( $ret[$key] !== null ){
                $start = intval($ret[$key]) + 1;
            }
        }

        //获取点赞总数
        $zan_num = self::_getReplyZanCaiNum($reply_id, 1);

        //获取点赞列表
        $stop = $start + $rn - 1;
        $arrParams = array(
            'key' => $key,
            'start' => $start,
            'stop' => $stop,
        );
        $ret = Util_Redis::ZREVRANGEWITHSCORES($arrParams);
        if( false === $ret ){
            Bingo_Log::warning(__FUNCTION__.' redis ZREVRANGEWITHSCORES error. [input='.serialize($arrParams).']');
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        $data = $ret[$key];

        if( !empty($data) ){
            foreach( $data AS $item ){
                $uid = intval($item['member']);
                $time = intval($item['score']);

                if( $uid <= 0 ){
                    continue;
                }
                $uids[] = $uid;
                $list[] = array(
                    'user_id' => $uid,
                    'time' => $time,
                );
            }
        }

        //批量获取用户信息
        $userInfos = array();
        if( !empty($uids) ){
            $arrParams = array(
                'user_ids' => $uids,
            );
            $arrRet = Tieba_Service::call('ntuser', 'getUserInfos', $arrParams, null, null, 'post', 'php', 'utf-8');
            if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                Bingo_Log::warning(__FUNCTION__.' service ntuser.getUserInfos error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                return $arrRet;
            }
            $userInfos = $arrRet['data']['user_infos'];
        }

        foreach( $list AS $key => $item ){
            $uid = intval($item['user_id']);

            if( !isset($userInfos[$uid]) ){
                continue;
            }

            $list[$key]['nickname'] = strval($userInfos[$uid]['nickname']);
            $list[$key]['avatar'] = strval($userInfos[$uid]['avatar']);
            $list[$key]['mark'] = strval($userInfos[$uid]['mark']);
            $list[$key]['intro'] = strval($userInfos[$uid]['intro']);
            $list[$key]['gender'] = intval($userInfos[$uid]['gender']);
            $list[$key]['user_type'] = intval($userInfos[$uid]['user_type']);
            $list[$key]['user_url'] = strval($userInfos[$uid]['user_url']);
        }

        $res = array(
            'num' => $zan_num,
            'list' => $list,
        );

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
    }

    /**
     * @brief: 获取点踩列表
     * @param
     * @return
     **/
    public static function getCaiList($arrInput){
        if( !isset($arrInput['reply_id']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $reply_id = intval($arrInput['reply_id']);
        $pre_uid = intval($arrInput['pre_uid']);
        $rn = isset($arrInput['rn']) ? intval($arrInput['rn']) : 20;
        $start = 0;
        $stop = 0;
        $key = self::REDIS_LIST_REPLY_CAI_PREFIX_KEY.$reply_id;
        $cai_num = 0;
        $list = array();
        $uids = array();

        if( $reply_id <= 0 || $pre_uid < 0 || $rn <= 0 ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //计算起始index
        if( $pre_uid > 0 ){
            $arrParams = array(
                'key' => $key,
                'member' => $pre_uid,
            );
            $ret = Util_Redis::ZREVRANK($arrParams);
            if( false === $ret ){
                Bingo_Log::warning(__FUNCTION__.' redis ZREVRANK error. [input='.serialize($arrParams).']');
                return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
            }
            if( $ret[$key] !== null ){
                $start = intval($ret[$key]) + 1;
            }
        }

        //获取点踩总数
        $cai_num = self::_getReplyZanCaiNum($reply_id, 0);

        //获取点踩列表
        $stop = $start + $rn - 1;
        $arrParams = array(
            'key' => $key,
            'start' => $start,
            'stop' => $stop,
        );
        $ret = Util_Redis::ZREVRANGEWITHSCORES($arrParams);
        if( false === $ret ){
            Bingo_Log::warning(__FUNCTION__.' redis ZREVRANGEWITHSCORES error. [input='.serialize($arrParams).']');
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        $data = $ret[$key];
        if( !empty($data) ){
            foreach( $data AS $item ){
                $uid = intval($item['member']);
                $time = intval($item['score']);

                if( $uid <= 0 ){
                    continue;
                }
                $uids[] = $uid;
                $list[] = array(
                    'user_id' => $uid,
                    'time' => $time,
                );
            }
        }

        //批量获取用户信息
        $userInfos = array();
        if( !empty($uids) ){
            $arrParams = array(
                'user_ids' => $uids,
            );
            $arrRet = Tieba_Service::call('ntuser', 'getUserInfos', $arrParams, null, null, 'post', 'php', 'utf-8');
            if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                Bingo_Log::warning(__FUNCTION__.' service ntuser.getUserInfos error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                return $arrRet;
            }
            $userInfos = $arrRet['data']['user_infos'];
        }

        foreach( $list AS $key => $item ){
            $uid = intval($item['user_id']);

            if( !isset($userInfos[$uid]) ){
                continue;
            }

            $list[$key]['nickname'] = strval($userInfos[$uid]['nickname']);
            $list[$key]['avatar'] = strval($userInfos[$uid]['avatar']);
            $list[$key]['mark'] = strval($userInfos[$uid]['mark']);
            $list[$key]['intro'] = strval($userInfos[$uid]['intro']);
            $list[$key]['gender'] = intval($userInfos[$uid]['gender']);
            $list[$key]['user_type'] = intval($userInfos[$uid]['user_type']);
            $list[$key]['user_url'] = strval($userInfos[$uid]['user_url']);
        }

        $res = array(
            'num' => $cai_num,
            'list' => $list,
        );

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
    }

    /**
     * @brief: 删除回复
     * @param
     * @return
     **/
    public static function delReply($arrInput){
        if( !isset($arrInput['user_id']) || !isset($arrInput['reply_id']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $user_id = intval($arrInput['user_id']);
        $reply_id = intval($arrInput['reply_id']);

        if( $user_id <= 0 || $reply_id <= 0 ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //获取回复信息
        $arrParams = array(
            'reply_id' => $reply_id,
        );
        $arrRet = self::getReplyInfoByReplyId($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' getReplyInfoByReplyId error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $replyInfo = $arrRet['data'];

        if( $user_id !== intval($replyInfo['user_id']) && $user_id !== 1 ){
            Bingo_Log::warning(__FUNCTION__.' user only can del own reply.[user_id='.$user_id.', reply_id='.$reply_id.']');
            return self::_errRet(Tieba_Errcode::ERR_INTERESTMAN_USER_NOT_POWER);
        }

        //删除回复
        $arrParams = array(
            'field' => array(
                'status' => Model_Ntreply_Ntreply::REPLY_STATUS_DEL,
            ),
            'cond' => array(
                'reply_id' => $reply_id,
            ),
        );
        $arrRet = Model_Ntreply_Ntreply::update($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' Ntreply sql update error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        //send NMQ
        $reqs = array(
            'user_id' => $replyInfo['user_id'],
            'topic_id' => intval($replyInfo['topic_id']),
            'reply_id' => $reply_id,
            'reply_type' => intval($replyInfo['reply_type']),
            'time' => time(),
        );
        $arrParams = array(
            'command' => self::NTREPLY_NMQ_DEL_REPLY_COMMAND,
            'reqs' => $reqs,
        );
        $sendNmq = self::_sendNMQ($arrParams);
        if( !$sendNmq ){
            Bingo_Log::warning(__FUNCTION__.' send nmq error. [input='.serialize($arrParams).']');
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief: 恢复删除内容
     * @param
     * @return
     **/
    public static function recoverReply($arrInput){
        if( !isset($arrInput['user_id']) || !isset($arrInput['reply_id']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $user_id = intval($arrInput['user_id']);
        $reply_id = intval($arrInput['reply_id']);

        if( $user_id <= 0 || $reply_id <= 0 ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //获取回复信息
        $arrParams = array(
            'reply_id' => $reply_id,
        );
        $arrRet = self::getReplyInfoByReplyId($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' getReplyInfoByReplyId error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $replyInfo = $arrRet['data'];

        if( $user_id !== intval($replyInfo['user_id']) && $user_id !== 1 ){
            Bingo_Log::warning(__FUNCTION__.' user only can recover own reply.[user_id='.$user_id.', reply_id='.$reply_id.']');
            return self::_errRet(Tieba_Errcode::ERR_INTERESTMAN_USER_NOT_POWER);
        }

        if( $replyInfo['status'] == Model_Ntreply_Ntreply::REPLY_STATUS_OK ){
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        //恢复
        $arrParams = array(
            'field' => array(
                'status' => Model_Ntreply_Ntreply::REPLY_STATUS_OK,
            ),
            'cond' => array(
                'reply_id' => $reply_id,
            ),
        );
        $arrRet = Model_Ntreply_Ntreply::update($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' Ntreply sql update error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        //恢复topic端记录
        $has_rescource = intval($replyInfo['resource_num']) > 0 ? 1 : 0;
        $arrParams = array(
            'topic_id' => intval($replyInfo['topic_id']),
            'reply_id' => $reply_id,
            //'time' => intval($replyInfo['time']),
            //'has_resource' => $has_rescource,
        );
        $arrRet = Tieba_Service::call('nttopic', 'recoverTopicReply', $arrParams, null, null, 'post', 'php', 'utf-8');
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' service nttopic.recoverTopicReply error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
        }

        //send NMQ
        /*
        $reqs = array(
            'user_id' => $replyInfo['user_id'],
            'topic_id' => intval($replyInfo['topic_id']),
            'reply_id' => $reply_id,
            'reply_type' => intval($replyInfo['reply_type']),
            'time' => time(),
        );
        $arrParams = array(
            'command' => self::NTREPLY_NMQ_DEL_REPLY_COMMAND,
            'reqs' => $reqs,
        );
        $sendNmq = self::_sendNMQ($arrParams);
        if( !$sendNmq ){
            Bingo_Log::warning(__FUNCTION__.' send nmq error. [input='.serialize($arrParams).']');
        }*/

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief: 获取回复信息
     * @param
     * @return
     **/
    public static function getReplyInfo($arrInput){
        if( !isset($arrInput['topic_id']) || !isset($arrInput['reply_id']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $user_id = intval($arrInput['user_id']);
        $topic_id = intval($arrInput['topic_id']);
        $reply_id = intval($arrInput['reply_id']);
        $need_comment = isset($arrInput['need_comment']) ? intval($arrInput['need_comment']) : 1;
        $users = array();
        $userRelations = array();
        $topic = array();
        $reply = array();
        $comment = array();
        $action = 0;
        $group_id = 0;

        if( $topic_id <= 0 || $reply_id <= 0 ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //获取回复详情
        $arrParams = array(
            'reply_id' => $reply_id,
        );
        $arrRet = self::getReplyInfoByReplyId($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' getReplyInfoByReplyId error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $reply = $arrRet['data'];

        if( $topic_id !== intval($reply['topic_id']) ){
            Bingo_Log::warning(__FUNCTION__.' topic_id is not match reply_id. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        if( $reply['status'] != Model_Ntreply_Ntreply::REPLY_STATUS_OK ){
            Bingo_Log::warning(__FUNCTION__.' reply is del.[reply_id='.$reply_id.']');
            return self::_errRet(Tieba_Errcode::ERR_ANTI_POST_LOCKED);
        }

        //获取当前用户针对该回复的操作
        Bingo_Log::pushNotice('uid', $user_id);
        if( $user_id > 0 ){
            $action = self::_getUserReplyAction($user_id, $reply_id);
            Bingo_Log::pushNotice('action', $action);
            $reply['action'] = $action;
        }

        //获取回复的评论信息&获取话题信息
        $objRalMulti = new Tieba_Multi('reply_service_getReplyInfo');

        //获取话题详情
        $arrMultiInput = array(
            'serviceName' => 'nttopic',
            'method' => 'getTopicInfo',
            'input' => array(
                'topic_id' => $topic_id,
            ),
            'ie' => 'utf-8',
        );
        $objRalMulti->register('getTopicInfo', new Tieba_Service('nttopic'), $arrMultiInput);

        //获取评论信息
        if( $need_comment === 1 ){
            $arrMultiInput = array(
                'serviceName' => 'ntcomment',
                'method' => 'getCommentsByReplyId',
                'input' => array(
                    'reply_id' => $reply_id,
                    'comment_id' => 0,
                    'rn' => 20,
                ),
                'ie' => 'utf-8',
            );
            $objRalMulti->register('getCommentList', new Tieba_Service('ntcomment'), $arrMultiInput);
        }

        Bingo_Timer::start('multi_call');
        $arrMultiOut = $objRalMulti->call();
        Bingo_Timer::end('multi_call');

        foreach( $arrMultiOut AS $key => $arrServiceOut ){
            if( false === $arrServiceOut || $arrServiceOut['errno'] != Tieba_Errcode::ERR_SUCCESS ){
                Bingo_Log::warning(__FUNCTION__.'service '.$key.' error. [topic_id='.$topic_id.', reply_id='.$reply_id.'][out='.serialize($arrServiceOut).']');
                continue;
            }

            //话题详情
            if( $key == 'getTopicInfo' ){
                $topic = $arrServiceOut['data'][$topic_id];
            }

            //评论详情
            if( $key == 'getCommentList' ){
                $comment = $arrServiceOut['data'];
                $comment['page']['has_more'] = intval($comment['count']) > count($comment['list']) ? 1 : 0;
            }
        }

        //判定话题是否已被删除或锁定
        /*
        if( $topic['status'] != 1 || empty($topic) ){
            Bingo_Log::warning(__FUNCTION__.' topic is invalid. [topic_id='.$topic_id.']');
            return self::_errRet(Tieba_Errcode::ERR_POST_CT_POST_NOT_EXIST);
        }*/

        //批量获取用户信息
        $userIdsArr = array();
        $userIdsArr[] = $topic['user_id'];
        $userIdsArr[] = $reply['user_id'];
        $commentList = $comment['list'];

        if( !empty($commentList) && is_array($commentList) ){
            foreach( $commentList AS $item ){
                $uid = intval($item['user_id']);
                $userIdsArr[] = $uid;
            }
        }
        $userIdsArr = array_unique($userIdsArr);

        //start multi call
        //获取用户信息以及用户与当前用户关系
        $objRalMulti = null;
        $objRalMulti = new Tieba_Multi('reply_service_getUserInfo');

        //批量获取用户信息
        $arrMultiInput = array(
            'serviceName' => 'ntuser',
            'method' => 'getUserInfosAndRelations',
            'ie' => 'utf-8',
            'input' => array(
                'from_uid' => $user_id,
                'user_ids' => $userIdsArr,
                'need_ext' => 1,
            ),
        );
        $objRalMulti->register('getUserInfosAndRelations', new Tieba_Service('ntuser'), $arrMultiInput);

        //获取回复uid的group_id
        $arrMultiInput = array(
            'serviceName' => 'ntuser',
            'method' => 'getGroupIdByUid',
            'ie' => 'utf-8',
            'input' => array(
                'from_uid' => $user_id,
                'to_uid' => $reply['user_id'],
            ),
        );
        $objRalMulti->register('getGroupIdByUid', new Tieba_Service('ntuser'), $arrMultiInput);

        /*
        $arrMultiInput = array(
            'serviceName' => 'ntuser',
            'method' => 'getUserInfos',
            'input' => array(
                'user_ids' => $userIdsArr,
                'need_ext' => 1,
            ),
            'ie' => 'utf-8',
        );
        $objRalMulti->register('getUserInfos', new Tieba_Service('ntuser'), $arrMultiInput);

        //获取当前用户与批量用户的关系
        if( $user_id > 0 ){
            $arrMultiInput = array(
                'serviceName' => 'ntuser',
                'method' => 'getUserRelations',
                'input' => array(
                    'from_uid' => $user_id,
                    'user_ids' => $userIdsArr,
                ),
                'ie' => 'utf-8',
            );
            $objRalMulti->register('getUserRelations', new Tieba_Service('ntuser'), $arrMultiInput);
        }
        */

        Bingo_Timer::start('multi_call');
        $arrMultiOut = $objRalMulti->call();
        Bingo_Timer::end('multi_call');

        foreach( $arrMultiOut AS $key => $arrRet ){
            if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                Bingo_Log::warning(__FUNCTION__.' service '.$key.' error.');
                continue;
            }

            //批量获取用户信息
            if( $key == 'getUserInfosAndRelations' ){
                $users = $arrRet['data']['user_infos'];
            }

            //获取回复uid的group_id
            if( $key == 'getGroupIdByUid' ){
                $group_id = intval($arrRet['data']['group_id']);
            }
        }

        $res = array(
            'users' => $users,
            'topic' => $topic,
            'reply' => $reply,
            'comment' => $comment,
            'group_id' => $group_id,
        );

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
    }

    /**
     * @brief: 批量获取回复信息
     * @param
     * @return
     **/
    public static function mgetReplyInfo($arrInput){
        if( !isset($arrInput['reply_ids']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $reply_ids = $arrInput['reply_ids'];
        $user_id = intval($arrInput['user_id']);
        $get_del = intval($arrInput['get_del']);
        $res = array();
        $valStr = '';

        if( empty($reply_ids) || !is_array($reply_ids) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        $replyIdsArr = array_unique($reply_ids);
        foreach(  $replyIdsArr AS $value ){
            $valStr .= $value.',';
        }
        $valStr = '('.substr($valStr, 0, strlen($valStr)-1).')';
        $arrParams = array(
            'field' => array(
                'reply_id',
                'topic_id',
                'user_id',
                'comment_num',
                'forward_num',
                'zan_num',
                'cai_num',
                'title',
                'content',
                'digest',
                'floor_num',
                'pic',
                'audio',
                'video',
                'link',
                'resource',
                'pic_num',
                'reply_type',
                'status',
                'update_time',
                'create_time',
            ),
            'cond' => array(
                'reply_id' => array(
                    'opt' => 'in',
                    'val' => $valStr,
                    'quotes' => 0,
                ),
            ),
        );
        $arrRet = Model_Ntreply_Ntreply::select($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' Ntreply sql select error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $data = $arrRet['data'];
        if( !empty($data) ){
            foreach( $data AS $item ){
                $reply_id = intval($item['reply_id']);
                $status = intval($item['status']);

                if( $get_del == 0 && $status !== Model_Ntreply_Ntreply::REPLY_STATUS_OK ){
                    continue;
                }
                $res[$reply_id] = Util_Util::formatReplyOut($item);
            }
        }

        //获取回复列表用户的赞|踩操作
        $actionArr = array();
        if( $user_id > 0 ){
            $actionArr = self::_mgetUserReplyAction($user_id, $replyIdsArr);
        }
        if( !empty($actionArr) && !empty($res) ){
            foreach( $res AS $key => $item ){
                $res[$key]['action'] = intval($actionArr[$key]);
            }
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
    }

    /**
     * @brief: 用户是否可以点赞|踩
     * @param
     * @return
     **/
    private static function _canZanCai($user_id, $reply_id){
        if( $user_id <= 0 || $reply_id <= 0 ){
            return false;
        }

        $key = self::REDIS_HASH_USER_ZANCAI_PREFIX_KEY.$user_id;

        $arrParams = array(
            'key' => $key,
            'field' => $reply_id,
        );
        $res = Util_Redis::HGET($arrParams);
        if( false === $res ){
            Bingo_Log::warning(__FUNCTION__.' redis_HGET error. [input='.serialize($arrParams).']');
            return false;
        }
        if( $res === null ){
            return true;
        }

        return false;
    }

    /**
     * @brief: 获取用户的点赞|点踩操作
     * #未操作返回0
     * #点赞返回1
     * #点踩返回2
     * @param
     * @return
     **/
    private static function _getUserReplyAction($user_id, $reply_id){
        $value = 0;

        if( $user_id <= 0 || $reply_id <= 0 ){
            return $value;
        }

        $key = self::REDIS_HASH_USER_ZANCAI_PREFIX_KEY.$user_id;

        $arrParams = array(
            'key' => $key,
            'field' => $reply_id,
        );
        $res = Util_Redis::HGET($arrParams);

        if( false === $res ){
            Bingo_Log::warning(__FUNCTION__.' redis_HGET error. [input='.serialize($arrParams).']');
            return $value;
        }
        if( $res === null ){
            return $value;
        }
        $value = ($res == 1) ? 1 : 2;
        Bingo_Log::pushNotice('val', $value);
        return $value;
    }

    /**
     * @brief: 批量获取用户的点赞|点踩操作
     * #未操作返回0
     * #点赞返回1
     * #点踩返回2
     * @param
     * @return
     **/
    private static function _mgetUserReplyAction($user_id, $reply_ids){
        $res = array();
        $input = array();
        $key = self::REDIS_HASH_USER_ZANCAI_PREFIX_KEY.$user_id;

        if( $user_id <= 0 || empty($reply_ids) || !is_array($reply_ids) ){
            Bingo_Log::warning(__FUNCTION__.' params error.');
            return $res;
        }

        $input['key'] = $key;
        foreach( $reply_ids AS $rid ){
            $input['field'][] = $rid;
        }
        $ret = Util_Redis::HMGET($input);
        if( $ret === false || empty($ret) ){
            return $res;
        }

        foreach( $reply_ids AS $key => $rid ){
            $action = $ret[$key];
            if( $action === null ){
                $action = 0;
            }else if( $action == 0 ){
                $action = 2;
            }else{
                $action = intval($action);
            }

            $res[$rid] = $action;
        }

        return $res;
    }

    /**
     * @brief: 获取回复的赞|踩总数
     * @param
     * @return
     **/
    private static function _getReplyZanCaiNum($reply_id, $value){
        $num = 0;
        if( $reply_id <= 0 || ( $value !== 0 && $value !== 1 ) ){
            return $num;
        }

        $key = ($value === 1) ? self::REDIS_LIST_REPLY_ZAN_PREFIX_KEY : self::REDIS_LIST_REPLY_CAI_PREFIX_KEY;
        $key .= $reply_id;

        $arrParams = array(
            'key' => $key,
        );
        $ret = Util_Redis::ZCARD($arrParams);
        if( false === $ret ){
            Bingo_Log::warning(__FUNCTION__.' redis_LLEN error. [key='.$key.']');
            return $num;
        }

        $num = intval($ret[$key]);

        return $num;
    }

    /**
     * @brief: 更新回复的赞踩列表
     * @param
     * @return
     **/
    private static function _updateReplyZanCaiList($reply_id, $user_id, $value, $action='add'){
        if( $reply_id <= 0 || $user_id <= 0 || ($value !== 0 && $value !== 1) ){
            return false;
        }

        $key = ($value === 1) ? self::REDIS_LIST_REPLY_ZAN_PREFIX_KEY : self::REDIS_LIST_REPLY_CAI_PREFIX_KEY;
        $key .= $reply_id;

        if( $action == 'add' ){
            $arrParams = array(
                'key' => $key,
                'member' => $user_id,
                'score' => time(),
            );
            $ret = Util_Redis::ZADD($arrParams);
            if( false === $ret ){
                Bingo_Log::warning(__FUNCTION__.' redis_RPUSH error. [input='.serialize($arrParams).']');
                return false;
            }
        }else{
            $arrParams = array(
                'key' => $key,
                'member' => $user_id,
            );
            $ret = Util_Redis::ZREM($arrParams);
            if( $ret === false ){
                Bingo_Log::warning(__FUNCTION__.' redis_RZEN error. [input='.serialize($arrParams).']');
                return false;
            }
        }

        return true;
    }

    /**
     * @brief: 删除用户点赞数据
     * @param
     * @return
     * @version: 2.0
     **/
    private static function _delUserZan($user_id, $reply_id){
        if( $user_id <= 0 || $reply_id <= 0){
            return false;
        }
        $key = self::REDIS_HASH_USER_ZANCAI_PREFIX_KEY.$user_id;

        $arrParams = array(
            'key' => $key,
            'field' => $reply_id,
        );
        $res = Util_Redis::HDEL($arrParams);
        if( false === $res ){
            Bingo_Log::warning(__FUNCTION__.' redis_HDEL error. [input='.serialize($arrParams).']');
            return false;
        }

        return true;
    }

    /**
     * @brief: 更新用户点赞|踩数据
     * @param
     * @return
     **/
    private static function _updateUserZanCai($user_id, $reply_id, $value){
        if( $user_id <= 0 || $reply_id <= 0 || $value < 0  ){
            return false;
        }
        $key = self::REDIS_HASH_USER_ZANCAI_PREFIX_KEY.$user_id;

        $arrParams = array(
            'key' => $key,
            'field' => $reply_id,
            'value' => $value,
        );
        $res = Util_Redis::HSET($arrParams);
        if( false === $res ){
            Bingo_Log::warning(__FUNCTION__.' redis_HSET error. [input='.serialize($arrParams).']');
            return false;
        }

        return true;
    }

    /**
     * @brief: 更新回复的赞|踩数
     * @param
     * @return
     **/
    public static function updateZanCaiNum($arrInput){
        if( !isset($arrInput['reply_id']) || !isset($arrInput['value']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $field = array();
        $reply_id = intval($arrInput['reply_id']);
        $value = intval($arrInput['value']);
        $num = isset($arrInput['num']) ? intval($arrInput['num']) : 1;

        if( $value ){
            $field = array(
                'zan_num' => array(
                    'val' => "zan_num + $num",
                    'quotes' => 0,
                ),
            );
        }else{
            $field = array(
                'cai_num' => array(
                    'val' => "cai_num + $num",
                    'quotes' => 0,
                ),
            );
        }

        $arrParams = array(
            'field' => $field,
            'cond' => array(
                'reply_id' => $reply_id,
            ),
        );
        $arrRet = Model_Ntreply_Ntreply::update($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' sql update error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
        }

        return $arrRet;
    }

    /**
     * @brief: 更新回复的评论数
     * @param
     * @return
     **/
    public static function updateCommentNum($arrInput){
        if( !isset($arrInput['reply_id']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $reply_id = intval($arrInput['reply_id']);
        $num = isset($arrInput['num']) ? intval($arrInput['num']) : 1;

        if( $reply_id <= 0 ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        if( $num < 0 ){
            //获取回复信息
            $arrParams = array(
                'reply_id' => $reply_id,
            );
            $arrRet = self::getReplyInfoByReplyId($arrParams);
            if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                Bingo_Log::warning(__FUNCTION__.' getReplyInfoByReplyId error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                return $arrRet;
            }
            $replyInfo = $arrRet['data'];
            $comment_num = intval($replyInfo['comment_num']);
            if( $comment_num <= 0 ){
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
            }
            $num = ($comment_num + $num) >= 0 ? $num : -$comment_num;
        }

        $arrParams = array(
            'field' => array(
                'comment_num' => array(
                    'val' => "comment_num + $num",
                    'quotes' => 0,
                ),
            ),
            'cond' => array(
                'reply_id' => $reply_id,
            ),
        );
        $arrRet = Model_Ntreply_Ntreply::update($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' Ntreply sql update error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
        }

        return $arrRet;
    }

    /**
     * @brief: 更新回复的转发数
     * @param
     * @return
     **/
    public static function updateForwardNum($arrInput){
        if( !isset($arrInput['reply_id']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $reply_id = intval($arrInput['reply_id']);
        $num = isset($arrInput['num']) ? intval($arrInput['num']) : 1;

        if( $reply_id <= 0 ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        $arrParams = array(
            'field' => array(
                'forward_num' => array(
                    'val' => "forward_num + $num",
                    'quotes' => 0,
                ),
            ),
            'cond' => array(
                'reply_id' => $reply_id,
            ),
        );
        $arrRet = Model_Ntreply_Ntreply::update($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' Ntreply sql update error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
        }

        return $arrRet;
    }

    /**
     * @brief: 根据话题ID获取话题信息
     * @param
     * @return
     **/
    public static function getTopicInfo($topic_id){
        $topic_id = intval($topic_id);
        $res = array();

        if( $topic_id <= 0 ){
            return $res;
        }

        $arrParams = array(
            'topic_id' => $topic_id,
        );
        $arrRet = Tieba_Service::call('nttopic', 'getTopicReplyList', $arrParams, null, null, 'post', 'php', 'utf-8');
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' service nttopic.getTopicInfo error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $res;
        }
        $res = $arrRet['data']['topic_info'];
        $res['list'] = $arrRet['data']['list'];

        return $res;
    }

    /**
     * @brief: 根据回复ID获取回复信息
     * @param
     * @return
     **/
    public static function getReplyInfoByReplyId($arrInput){
        if( !isset($arrInput['reply_id']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $res = array();
        $reply_id = intval($arrInput['reply_id']);

        if( $reply_id <= 0 ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        $arrParams = array(
            'cond' => array(
                'reply_id' => $reply_id,
            ),
        );
        $arrRet = Model_Ntreply_Ntreply::select($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' Ntreply sql select error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $replyInfo = $arrRet['data'][0];
        if( empty($replyInfo) ){
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
        }

        $user_id = intval($replyInfo['user_id']);
        $arrParams = array(
            'user_id' => $user_id,
        );
        $arrRet = Tieba_Service::call('ntuser', 'getUserInfo', $arrParams, null, null, 'post', 'php', 'utf-8');
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' service ntuser.getUserInfo error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $userInfo = $arrRet['data']['user_info'];

        $replyInfo['user_name'] = strval($userInfo['user_name']);
        $replyInfo['nickname'] = strval($userInfo['nickname']);
        $replyInfo['can_comment'] = ($replyInfo['status'] == Model_Ntreply_Ntreply::REPLY_STATUS_OK) ? 1 : 0;
        $replyInfo = Util_Util::formatReplyOut($replyInfo);

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $replyInfo);
    }

    /**
     * @brief: 发送NMQ
     * @param
     * @return
     **/
    private static function _sendNMQ($arrInput){
        if( !isset($arrInput['command']) || !isset($arrInput['reqs']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return false;
        }

        $nmq_retry_num = 2;
        $arrRet = null;
        $topic = null;
        $method = null;
        $reqs = null;

        $topic = isset($arrInput['topic']) ? $arrInput['topic'] : self::NTREPLY_NMQ_TOPIC;
        $command = $arrInput['command'];
        $reqs = $arrInput['reqs'];

        if( empty($topic) || empty($command) || empty($reqs) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return false;
        }

        //发送NMQ
        $num = $nmq_retry_num;
        do{
            $arrRet = Tieba_Commit::commit($topic, $command, $reqs);
            if( $arrRet['err_no'] == Tieba_Errcode::ERR_SUCCESS ){
                break;
            }
            $num--;
        }while($num > 0);
        if( $arrRet === false || !isset($arrRet['err_no']) || $arrRet['err_no'] != 0 ){
            Bingo_Log::warning('Tieba_Commit:'.$topic.'_'.$command.' error.[input='.serialize($arrInput).']');
            return false;
        }

        return true;
    }

    /**
     * @brief: UEG异步策略
     * @param
     * @return
     **/
    private static function _asynUEG($arrInput){
        $user_id = intval($arrInput['user_id']);
        $user_name = strval($arrInput['user_name']);
        $user_ip = intval($arrInput['user_ip']);
        $reply_id = intval($arrInput['reply_id']);
        $content = strval($arrInput['content']);
        $pic = strval($arrInput['pic']);
        $picInput = '';
        $video = strval($arrInput['video']);
        $videoInput = '';

        if( $user_id <= 0 || empty($user_name) || $reply_id <= 0 ){
            return false;
        }

        $picArr = json_decode($pic, true);
        $videoArr = json_decode($video, true);

        if( !empty($picArr) && is_array($picArr) ){
            foreach( $picArr AS $item ){
                $picInput[] = $item['big_url'];
            }
        }
        if( !empty($videoArr) && is_array($videoArr) ){
            $videoInput = trim($videoArr[0]['source_url']);
        }

        $arrParams = array(
            'anti_cmd'=>'recallApp',
            "req" => array(
                "app_key" => 23,                    //专为微粉业务指定
                "ugc_type" => 2,                    //1话题，2回复，3评论，4聊天室聊天
                "user_id" => $user_id,              //用户id
                "user_name" => $user_name,          //用户名
                "content" => $content,              //文字内容
                "image" => $picInput,               //图片url
                "video" => $videoInput,             //url
                //"thread_id" => $thread_id,        //复用主题id，话题id,聊天室聊天内容的id也请服用此字段
                "post_id" => $reply_id,             //复用回复id
                //"quote_id" => $quote_id,          //复用楼中楼id，评论id
                "ip" => $user_ip,                   //用户本次操作ip
            ),
        );

        $arrRet = Tieba_Service::call("anti","antiCommit", $arrParams, null, null, 'post', 'php', 'utf-8');
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' service anti.antiCommit error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return false;
        }

        return true;
    }

    /**
     * @brief
     * @param:
     * @return:
     **/
    public static function sendNMQ($arrInput){
        if( !isset($arrInput['command']) || !isset($arrInput['reqs']) ){
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params
        $method = $arrInput['command'];
        $reqs = $arrInput['reqs'];

        $res = self::_sendNMQ($arrInput);
        if( $res === false ){
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief  增加话题的PV(从消息推送)
     * @param  $arrInput
     * @return mixed
     */
    public static function incTopicPVFromPush($arrInput) {
        $intTopicId = intval($arrInput['topic_id']);
        $arrParams = array(
            'key' => 'topic_recommend_push_' . $intTopicId,
        );
        $arrRet = Util_Redis::INCR($arrParams);
        if( false === $arrRet ){
            Bingo_Log::warning(__FUNCTION__.' redis call fail');
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief  获取话题的PV(从消息推送)
     * @param  $arrInput
     * @return mixed
     */
    public static function getTopicPVFromPush($arrInput) {
        $arrTopicIds = $arrInput['topic_ids'];
        if (empty($arrTopicIds)) {
            Bingo_Log::warning(sprintf("param error. topic ids is empty"));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrParams = array();
        foreach ($arrTopicIds as $intTopicId) {
            $arrParams['reqs'][] = array(
                'key' => 'topic_recommend_push_' . $intTopicId,
            );
        }
        $arrRet = Util_Redis::redisQuery(null, 'GET', $arrParams);
        Bingo_Log::warning(print_r($arrRet, 1));

        if(Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no'] ){
            Bingo_Log::warning(__FUNCTION__.' redis call fail');
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        $arrOutput = array();
        foreach ($arrRet['ret'] as $key => $item){
            $intKeyLen = strlen($key);
            $strKey = substr($key, 21, $intKeyLen - 21);
            $arrOutput[$strKey] = intval($item);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }
}