<?php

/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

define('BINGO_ENCODE_LANG','UTF-8');
define("MODULE","Ntreply_service");
class Service_Ntreply{
    CONST SERVICE_NAME = "Service_Ntreply";
    
    CONST NMQ_COMMAND_ADD_REPLY   = 601;
    CONST NMQ_COMMAND_DEL_REPLY   = 602;
    CONST NMQ_COMMAND_ADD_COMMENT = 603;
    CONST NMQ_COMMAND_DEL_COMMENT = 604;
    CONST NMQ_COMMAND_ZAN_REPLY   = 605;
    CONST NMQ_COMMAND_CAI_REPLY   = 606;
    CONST NMQ_COMMAND_LOAD_DATA   = 607;

    public static $service_ie = 'utf-8';
    protected static $_conf = null;
    
    /**
     * @param:
     * @return:
     **/
    public static function getIE($methodName){
        return self::$service_ie;
    }
    
    /**
     * @brief init
     * @param:
     * @return: true if success. false if fail.
    **/
    private static function _init(){
        //add init code here. init will be called at every public function beginning.
        //not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
        //init should be recalled for many times.

        if(self::$_conf == null){
            self::$_conf = Bd_Conf::getConf("/app/ntreply/service_ntreply");
            if(self::$_conf == false){
                Bingo_Log::warning("init get conf fail.");
                return false;
            }
        }
        return true;
    }
    
    /**
     * @param:
     * @return:
     **/
    private static function _errRet($errno){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }
    
    /**
     * @param:
     * @return:
     **/
    public static function preCall($arrInput){
        // pre-call hook
    }
    
    /**
     * @param:
     * @return:
     **/
    public static function postCall($arrInput){
        // post-call hook
    }
    
    /**
     * @brief: 统一接入函数
     * @param:
     * @return:
     **/
    public static function call($methodName, $arrInput){
        $serviceName = Libs_Router::getServiceName($methodName);
        
        if( empty($serviceName) ){
            Bingo_Log::warning(__FUNCTION__.' method not found. [method='.$methodName.']');
            return self::_errRet(Tieba_Errcode::ERR_METHOD_NOT_FOUND);
        }
        
        $timer_key = 'service_'.$serviceName.'_'.$methodName;
        Bingo_Timer::start($timer_key);
        $res = call_user_func_array(array($serviceName, $methodName), array($arrInput));
        Bingo_Timer::end($timer_key);
        
        return $res;
    }

    /**
     * @brief nmq处理
     * @param
     * @return
     **/
    public static function nmqCallback($arrInput){
        //input params.
        $data = Tieba_Service::getArrayParams($arrInput, 'data');
        
        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.

        //your code here......
        if (!isset($data['command_no'])) {
            Bingo_Log::warning ( "command_no loss." );
            return array ('status' => 0 );
        }
        $intCmd = intval($data['command_no']);
        
        $intTransId = 0;
        if(isset($data ['trans_id'])) {
            $intTransId = intval($data['trans_id']);
        }
        Bingo_Log::pushNotice("command_no",$intCmd);
        Bingo_Log::pushNotice("trans_id",$intTransId);

        $bolHasErr = false;
        $res = self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        switch ($intCmd) {
            case self::NMQ_COMMAND_ADD_COMMENT:
                $res = Service_Ntreply_Ntreply::updateCommentNum($data);
                Bingo_Log::pushNotice('ADD_COMMENT', 1);
                Bingo_Log::pushNotice('reply_id', $data['reply_id']);
                Bingo_Log::pushNotice("errno",$res['errno']);
                break;
            case self::NMQ_COMMAND_DEL_COMMENT:
                $data['num'] = -1;
                $res = Service_Ntreply_Ntreply::updateCommentNum($data);
                Bingo_Log::pushNotice('DEL_COMMENT', 1);
                Bingo_Log::pushNotice('reply_id', $data['reply_id']);
                Bingo_Log::pushNotice("errno",$res['errno']);
                break;
            case self::NMQ_COMMAND_LOAD_DATA:
                $res = Service_Ntreply_Ntreply::addReply($data);
                Bingo_Log::pushNotice('LOAD_DATA', 1);
                Bingo_Log::pushNotice('errno', $res['errno']);
                break;
        }
        if(Tieba_Errcode::ERR_SUCCESS !== $res['errno']){
            Bingo_Log::warning("nmq err. [errno=".$res['errno']."]");
        }
        
        return $res;
    }
    
    /**
     * @brief:联调使用 上线后删除
     * @param
     * @return
     **/
    public static function querySql($arrInput){
        $sql = $arrInput['sql'];
        $sql = trim($sql);
        
        $db = Tieba_Mysql::getDB('forum_ecom');
        if($db && $db->isConnected()) {
			$db->charset('utf8');
		} else {
			Bingo_Log::warning("db connect fail.");
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
        $data = $db->query($sql);
        
        return array(
            'errno' => Tieba_Errcode::ERR_SUCCESS,
            'errmsg' => '',
            'data' => $data,
        );
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function addTimeoutReply($arrInput){
        $ret = Dl_ReplyCache::addAddReply($arrInput['topic_id'], $arrInput['user_id']);
        return array(
            "ret" => $ret,
        );
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function getTimeoutReply($arrInput){
        $ret = Dl_ReplyCache::getAddReply($arrInput['topic_id'], $arrInput['user_id']);
        return array(
            "ret" => $ret,
        );
    }
}