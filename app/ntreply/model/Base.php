<?php

/**
 * 领域模型：主要用来提供一些通用的领域逻辑
 * Class Model_Base
 */
class Model_Base {

    protected $key = 'id';

    protected $attributes = array();

    protected $errno  = 0;
    protected $errmsg = null;

    public function __construct( array $attributes = array() ) {

        $this->fill( $attributes );
    }

    /**
     * @param array $attributes
     * @return static
     */
    public static function newInstance( array $attributes = array() ) {
        return new static( $attributes );
    }

    public function fill( array $attributes ) {

        foreach ( $attributes as $key => $value ) {
            $this->$key = $value;
        }

    }

    public function __unset( $key ) {

        unset( $this->attributes[ $key ] );
    }

    public function __isset( $key ) {

        return isset( $this->attributes[ $key ] );
    }

    public function __get( $key ) {

        return $this->getAttribute( $key );
    }

    public function __set( $key, $value ) {

        $this->setAttribute( $key, $value );
    }

    public function getAttribute( $key ) {

        if ( array_key_exists( $key, $this->attributes ) ) {
            return $this->getPlainAttribute( $key );
        }

        return null;
    }

    protected function getPlainAttribute( $key ) {

        $value = $this->attributes[ $key ];

        if ( $this->hasGetMutator( $key ) ) {
            $method = 'get' . $this->camelCase( $key );

            return $this->$method( $value );
        }

        return $value;

    }

    protected function hasGetMutator( $key ) {

        return method_exists( $this, 'get' . $this->camelCase( $key ) );

    }

    protected function camelCase( $key ) {

        $value = ucwords( str_replace( '_', ' ', $key ) );

        return str_replace( ' ', '', $value );
    }

    public function setAttribute( $key, $value ) {

        if ( $this->hasSetMutator( $key ) ) {
            $method                   = 'set' . $this->camelCase( $key );
            $this->attributes[ $key ] = $this->$method( $value );

            return;
        }
        $this->attributes[ $key ] = $value;

    }

    protected function hasSetMutator( $key ) {

        return method_exists( $this, 'set' . $this->camelCase( $key ) );
    }

    public function getAttributes() {

        return $this->attributes;
    }

    public function setAttributes( array $attributes ) {

        $this->attributes = $attributes;
    }

    public function getKeyName() {

        return $this->key;
    }

    public function getKey() {

        return $this->getAttribute( $this->getKeyName() );
    }

    public function toArray() {
        return $this->attributes;
    }

    /**
     * @return string
     */
    public function toJson() {
        return json_encode( $this->attributes );
    }

    /**
     * @param      $errno
     * @param null $errmsg
     * @return $this
     */
    public function error( $errno, $errmsg = null ) {

        $this->errno  = $errno;
        $this->errmsg = $errmsg;
        if ( is_null( $this->errmsg ) ) {
            $this->errmsg = Tieba_Error::getErrmsg( $this->errno );
        }
        return $this;
    }

    /**
     * @return int
     */
    public function getErrno() {
        return $this->errno;
    }

    /**
     * @return string
     */
    public function getErrmsg() {
        return $this->errmsg;
    }

}