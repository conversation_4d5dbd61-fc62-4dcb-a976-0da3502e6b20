<?php

class Model_Ntreply_Ntreply extends Model_Base {
    CONST REPLY_STATUS_DEL = 0;                     //被删除的回复
    CONST REPLY_STATUS_OK = 1;                      //正常回复
    CONST REPLY_STATUS_AUDIT = 2;                   //待审核的回复
    
    CONST REPLY_TYPE_DEFAULT = 1;                   //回复内容类型——图文
    CONST REPLY_TYPE_VIDEO = 2;                     //回复内容类型——视频
    CONST REPLY_TYPE_PIC = 3;                       //回复内容类型——图片
    
    CONST REPLY_MAX_CONTENT = 20000;                //最大回复内容长度
    
    private static $_model = null;
    
    public static $_validReplyType = array(
        self::REPLY_TYPE_DEFAULT,
        self::REPLY_TYPE_VIDEO,
        self::REPLY_TYPE_PIC,
    );
    
    
    /**
     * @brief:
     * @param:
     * @return:
     **/
    public static function getModel(){
        if(is_null(self::$_model) ){
            self::$_model = new Dl_Db_Ntreply();
        }
        
        return self::$_model;
    }
    
    /**
     * @brief:查询回复信息
     * @param:
     * @return:
     **/
    public static function select($arrInput){
        //input params
        $cond = $arrInput['cond'];
        $model = self::getModel();
        
        if( empty($cond) || !is_array($cond) ){
            return false;
        }
        
        $arrRet = $model->select($arrInput);
        
        return $arrRet;
    }
    
    /**
     * @brief: 插入新的回复
     * @param:
     * @return:
     **/
    public static function insert($arrInput){
        if( !isset($arrInput['field']) ){
            return false;
        }
        
        //input params
        $field = $arrInput['field'];
        $now = time();
        $model = self::getModel();
        
        if( empty($field) || !is_array($field) ){
            return false;
        }
        
        $field['status'] = isset($field['status']) ? $field['status'] : self::REPLY_STATUS_OK;
        $field['update_time'] = isset($field['update_time']) ? $field['update_time'] : $now;
        $field['create_time'] = isset($field['create_time']) ? $field['create_time'] : $now;
        
        $arrParams = array(
            'field' => $field,
        );
        $arrRet = $model->insert($arrParams);
        
        return $arrRet;
    }
    
    /**
     * @brief: 更新记录
     * @param:
     * @return:
     **/
    public static function update($arrInput){
        if( !isset($arrInput['field']) || !isset($arrInput['cond']) ){
            return false;
        }
        
        //input params
        $field = $arrInput['field'];
        $cond = $arrInput['cond'];
        $model = self::getModel();
        
        if( empty($field) || !is_array($field) || empty($cond) || !is_array($cond) ){
            return false;
        }
        
        $field['update_time'] = time();
        
        $arrParams = array(
            'field' => $field,
            'cond' => $cond,
        );
        $arrRet = $model->update($arrParams);
        
        return $arrRet;
    }
    
}