<?php


class Libs_Fsv_Rules_RequiredRule implements Libs_Fsv_Contracts_RuleContract
{
    public function run($value, $input, $args)
    {
        $value = preg_replace('/^[\pZ\pC]+|[\pZ\pC]+$/u', '', $value);

        return is_numeric($value) || !empty($value);
    }

    public function error()
    {
        return '{field} is required.';
    }

    public function canSkip()
    {
        return false;
    }
}
