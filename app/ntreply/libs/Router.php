<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * @brief: 模块路由信息
 * <AUTHOR>
 * @date 2016-04-05
 * @version
 * @structs & methods(copied from idl.)
*/
define('BINGO_ENCODE_LANG','UTF-8');
class Libs_Router{

    private static $_allRouter = array(
        //回复模块
        //微粉2.0 接口
        'insertWeFanTBPosts'   =>  'Service_Dataload_DataLoad',   //将贴吧数据导入微粉
        'updateWeFanTBPosts'   =>  'Service_Dataload_DataLoad',   //追更微粉的帖子
        'getTBPostsByThreadId' =>  'Service_Dataload_DataLoad',   //抓取贴吧数据

        'addThread'         =>  'Service_Ntreply_Ntreply',      //添加新贴
        'addFloor'          =>  'Service_Ntreply_Ntreply',      //添加楼层
        'addZan'            =>  'Service_Ntreply_Ntreply',      //点赞
        'cancelZan'         =>  'Service_Ntreply_Ntreply',      //取消点赞
        'delFloor'          =>  'Service_Ntreply_Ntreply',      //删除楼层
        'getFloorInfo'      =>  'Service_Ntreply_Ntreply',      //获取楼层信息

        'sendNMQ'           =>  'Service_Ntreply_Ntreply',

        //微粉1.0接口
        'addReply'          =>  'Service_Ntreply_Ntreply',      //添加回复内容
        'addCai'            =>  'Service_Ntreply_Ntreply',      //点踩
        'getZanList'        =>  'Service_Ntreply_Ntreply',      //获取点赞列表
        'getCaiList'        =>  'Service_Ntreply_Ntreply',      //获取点踩列表
        'delReply'          =>  'Service_Ntreply_Ntreply',      //删除回复
        'recoverReply'      =>  'Service_Ntreply_Ntreply',      //恢复回复
        'getReplyInfo'      =>  'Service_Ntreply_Ntreply',      //获取回复信息
        'mgetReplyInfo'     =>  'Service_Ntreply_Ntreply',      //批量获取回复信息
        'updateForwardNum'  =>  'Service_Ntreply_Ntreply',      //更新回复的分享数
        'updateCommentNum'  =>  'Service_Ntreply_Ntreply',      //更新回复的评论数
        'getZanList'        =>  'Service_Ntreply_Ntreply',      //获取点赞列表
        'getCaiList'        =>  'Service_Ntreply_Ntreply',      //获取点踩列表
        'getReplyInfoByReplyId' => 'Service_Ntreply_Ntreply',
        'getFloorInfoForSharePage'      =>  'Service_Ntreply_Ntreply',      //获取回复信息（分享页使用）

        'getVirtualUserList' => 'Service_Ntreply_Ntreply',   //获取内容发布平台登录用户的马甲账户列表
        'addVirtualUser'     => 'Service_Ntreply_Ntreply',  //添加发布平台的白名单用户，及马甲账号
        'getTopicPVFromPush' => 'Service_Ntreply_Ntreply',
        'incTopicPVFromPush' => 'Service_Ntreply_Ntreply',
        'setVirtualUser'     => 'Service_Ntreply_Ntreply',  //添加发布平台的白名单用户，及马甲账号
    );

    /**
     * @brief: 获取路由信息
     * @param:
     * @return:
     **/
    public static function getServiceName($methodName){
        if( isset(self::$_allRouter[$methodName]) ){
            return self::$_allRouter[$methodName];
        }
        return '';
    }
}