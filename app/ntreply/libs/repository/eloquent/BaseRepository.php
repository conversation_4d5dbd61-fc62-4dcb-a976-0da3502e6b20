<?php

/**
 * 本类主要是用来实现Libs_Repository_Contracts_RepositoryInterface，抽取抽象逻辑，
 * 但是由于时间问题，暂时没有抽取，还是由各个类自己实现接口
 * Class Libs_Repository_Contracts_BaseRepository
 */
abstract class Libs_Repository_Contracts_BaseRepository implements Libs_Repository_Contracts_RepositoryInterface {

    /**
     * @var Model_Base
     */
    private $model;

    /**
     * Libs_Repository_Contracts_BaseRepository constructor.
     */
    public function __construct() {
        $this->makeModel();
    }

    public function all( $columns = array( '*' ) ) {

    }

    public function paginate( $perPage = 15, $columns = array( '*' ) ) {
        // TODO: Implement paginate() method.
    }

    public function create( array $data ) {
        //return $this->model->create($data);
    }

    public function update( array $data, $id ) {
        // TODO: Implement update() method.
    }

    public function delete( $id ) {
        // TODO: Implement delete() method.
    }

    public function find( $id, $columns = array( '*' ) ) {
        // TODO: Implement find() method.
    }

    public function findBy( $conditions, $columns = array( '*' ) ) {
        // TODO: Implement findBy() method.
    }

    /**
     * Specify Model class name
     *
     * @return string
     */
    abstract public function model();

    /**
     * 根据自子类的model名字，创建出model
     * @return Model_Base
     */
    private function makeModel() {
        $modelName = "Model_" . ucfirst(strtolower($this->model()));
        $model = null;
        if(class_exists($modelName)) {
            $model = new $modelName;
        }
        else {
            Bingo_Log::warning("class $modelName not exist");
        }
        return $this->model = $model;
    }

}