<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2016-08-16 15:16:31
 * @version
 * @structs & methods(copied from idl.)
 * @brief 内容发布平台
*/

class publishAction extends Util_Base{
    protected static $_ui_ie = 'utf-8';
    protected static $_virtualUsers = null;

    public function _execute(){

        $this->_getUserInfo();
        $user_id = empty($this->_arrUserInfo['user_id'])? 0 : $this->_arrUserInfo['user_id'];
        Bingo_Log::pushNotice("ispv", 1);


        if(0 >= $user_id){
            Bingo_Log::warning('user is not login');
            $sever = Bingo_Http_Request::getServer();
            $strRedirectUrl = urlencode('http://' . $sever['HTTP_HOST'] . '/ntreply/publish');
            $passUrl = 'https://passport.baidu.com/v2/?login&u=' . $strRedirectUrl . '&regtype=1&tpl=tb';
            Bingo_Http_Response::redirect($passUrl);
            return;
        }

        //getVirtualUsers
        $virtualUsers = $this->getVirtualUsers($user_id);
        if( empty($virtualUsers) ){
            //用户不是白名单用户，无权访问该页面
            Bingo_Http_Response::redirect('http://wefan.baidu.com/ntspread/index');
            return;
        }


        Bingo_Page::assign('tbs', Tieba_Tbs::gene($this->_arrUserInfo['is_login']));
        Bingo_Page::assign('user', $this->_arrUserInfo);
        Bingo_Page::assign('virturalUsers', $virtualUsers);


        Bingo_Page::setTpl("wefan_topic_fill.php");
    }

}