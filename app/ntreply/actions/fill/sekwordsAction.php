<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/


/**
 * @file getCommentListAction.php
 * <AUTHOR>
 * @date 2016-07-24
 * @brief 异步拉评论
 *  
 **/
class sekwordsAction extends Util_Base {
    protected static $_ui_ie = 'utf-8';
    
	const TOPIC_SE_INDEX = 1;
	const TAG_SE_INDEX = 2;
	const USER_SE_INDEX = 3;
	
	private $_selectReq = array();
	private $_selectRes = array();

    private $_pn = 1;
    private $_rn = 10;
    private $_kw = "";
	private $_type = self::TOPIC_SE_INDEX;

    private static $_se_app = array(
    	1 => 'topic',
    	2 => 'tag',
    	3 => 'user',
    );
    
    private static $_se_index_word = array(
    	1 => 'topic_title',
    	2 => 'tag_name',
    	3 => 'user_nickname',
    );
    
    /**
     * @param
     * @return
     **/
    public function _execute(){
        try{
            //参数获取
            $user_id = intval($this->_arrUserInfo['user_id']);
            $this->_kw = strval(Bingo_Http_Request::getNoXssSafe('kw', ''));
            $this->_rn = intval(Bingo_Http_Request::getNoXssSafe('rn',10));
            $this->_pn = intval(Bingo_Http_Request::getNoXssSafe('pn', 1));
            if($this->_kw === ''){
                //参数校验
                throw new Util_Exception('params error!', Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $this->_type = intval(Bingo_Http_Request::getNoXssSafe('type', 1));

            $this->_buildParams();
            //UEG
            
            //OTHER策略
            
            $this->_select();
            
            $this->_buildOutput();
            $has_more = 1;
            $all_num = isset($this->_selectRes['all_num']) ? intval($this->_selectRes['all_num']) : 0; 
            if ($all_num <= $this->_pn * $this->_rn) {
            	$has_more = 0;
            }
            $data = array(
            	'list' => isset($this->_selectRes['rows']) ? $this->_selectRes['rows'] : array(),
            	'page' => array(
            		'has_more'   => $has_more,
            		'current_pn' => $this->_pn,
            	),
            );
                        
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_SUCCESS), $data);
            
        } catch(Util_Exception $e) {
            $errno = $e->getCode();
            $errmsg = $e->getMessage();
            Bingo_Log::warning('errno='.$e->getCode().' msg='.$e->getMessage());

            $this->_jsonRet($errno, $errmsg);
        }
    }
    
	/**
     * @param $arrList
     * @param $arrUsers
     * @return
     * @throws Util_Exception
     */
    private function _getUserDetailInfos($arrList)
    {
        if(empty($arrList)) {
            return array();
        }

        $arrUserIds = array_unique(array_column($arrList, 'user_id'));
        $arrParams = array(
            'user_ids' => $arrUserIds,
        );
        $arrRet = Tieba_Service::call('ntuser', 'getUserInfos', $arrParams, null, null, 'get', 'php', 'utf-8');
        if($arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call ntuser::getUserInfos fail, input=" . serialize($arrParams) . ',ret='.serialize($arrRet));
            throw new Util_Exception('user service error', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return $arrRet['data']['user_infos'];
    }
    /**
     * @param 
     * @return
     * @throws Util_Exception
     */
    private function _buildParams() {
    	$this->_selectReq = array(
    		self::$_se_index_word[$this->_type] => strval($this->_kw),
    		'page_no'  => intval($this->_pn)-1,
    		'list_num' => intval($this->_rn),
    	);
        /*if ($this->_type == self::TAG_SE_INDEX && $this->_pn != 1) {
            $this->_selectReq['list_num'] += 1;
        }*/
    }
    /**
     * @param 
     * @return
     * @throws Util_Exception
     */
    private function _select() {
    	
    	$se = Bd_RalRpc::create('Se', array('pid'=>'weifans', 'app'=> self::$_se_app[$this->_type]));
		if (!empty($this->_selectReq)) {
			$res = $se->select($this->_selectReq);
			if (null == $res || $res['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
			    Bingo_Log::warning("insert error,app:[tag],input:" . serialize($this->_selectReq) . " error_info: " . serialize($res) . "---" .  $se->get_error());
			    return false;
			}
			$this->_selectRes = $res;
		}
		return true;
    }
    /**
     * @param 
     * @return
     * @throws Util_Exception
     */
	private function _buildOutput() {
    	if ($this->_type == self::TAG_SE_INDEX) {
    		//Tag ,拉回复数，关注数
    		$this->_buildTagOutput();
    		
    	} elseif ($this->_type == self::USER_SE_INDEX) {
    		//拉用户粉丝数
    		$this->_buildUserOutput();
    	} else {
    		//拉回复数，关注数
    		$this->_buildTopicOutput();
    	}
    }
    /**
     * @param 
     * @return
     * @throws Util_Exception
     */
    private function _getTagFollowNum() {
    	$arrRet = array();
    	$arrTagIds = array_unique(array_column($this->_selectRes['rows'], 'tag_id'));
        $arrTagIds = array(
            'tag_ids' => $arrTagIds,
        );
    	if (!empty($arrTagIds)) {
	    	$arrRet = Tieba_Service::call('nttag', 'mgetTagsAttrs', $arrTagIds, null, null, 'post', 'php', 'utf-8');
	        if($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
	            Bingo_Log::warning("call mgetTagsAttrs fail, input=" . serialize($arrTagIds) . ',ret='.serialize($arrRet));
	            return false;
	        }
    		
    	}
    	return $arrRet;
    }
    /**
     * @param 
     * @return
     * @throws Util_Exception
     */
    private function _getTopicFollowNum() {
    	$arrRet = array();
    	$arrTopicIds = array_unique(array_column($this->_selectRes['rows'], 'topic_id'));
        $arrTopicIds = array(
            'topic_ids' => $arrTopicIds,
        );

    	if (!empty($arrTopicIds)) {
    		$arrRet = Tieba_Service::call('nttopic', 'mgetTopicInfo', $arrTopicIds, null, null, 'post', 'php', 'utf-8');
	        if($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
	            Bingo_Log::warning("call mgetTopicInfo fail, input=" . serialize($arrTopicIds) . ',ret='.serialize($arrRet));
	            return false;
	        }
    	}
    	return $arrRet;
    }
    /**
     * @param 
     * @return
     * @throws Util_Exception
     */
	private function _getUserFollowNum() {
    	$arrRet = array();
    	$arrUserIds = array_unique(array_column($this->_selectRes['rows'], 'user_id'));
        $arrUserIds = array(
            'user_ids' => $arrUserIds,
            'need_ext' => 1,
        );
    	if (!empty($arrUserIds)) {
    		$arrRet = Tieba_Service::call('ntuser', 'getUserInfos', $arrUserIds, null, null, 'post', 'php', 'utf-8');
	        if($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
	            Bingo_Log::warning("call getUserInfos fail, input=" . serialize($arrUserIds) . ',ret='.serialize($arrRet));
	            return false;
	        }
    	}
    	return $arrRet;
    }
    /**
     * @param 
     * @return
     * @throws Util_Exception
     */
    private function _buildTopicOutput() {
    	$arrRes = $this->_getTopicFollowNum();
    	$topics_follow_info = isset($arrRes['data']) ?  $arrRes['data'] : array();
    	if (!empty($topics_follow_info) && isset($this->_selectRes['rows'])) {
    			
    		foreach ($this->_selectRes['rows'] as $key => $value) {
    			$topic_id = intval($value['topic_id']);
    			if ($topic_id <= 0) {
    				continue;
    			}
   				if (isset($topics_follow_info[$topic_id])) {
    				$this->_selectRes['rows'][$key]['topic_reply_num']  = intval($topics_follow_info[$topic_id]['reply_num']);
    				$this->_selectRes['rows'][$key]['topic_follow_num'] = intval($topics_follow_info[$topic_id]['follow_num']);
    			}
    		}
    	}
    		
    	return true;
    }
    /**
     * @param 
     * @return
     * @throws Util_Exception
     */
    private function _buildTagOutput() {
        $tagFirst = array();
        if ($this->_pn === 1 ) {
            $arrParams = array(
                'tag_name'     => $this->_kw,
                'get_ext_info' => 1,
            );
            $arrOpt = Tieba_Service::call('nttag', 'getTagInfoByName', $arrParams, null, null, 'post', 'php', 'utf-8');
            $arrTag = isset($arrOpt['data']['tag']) ? $arrOpt['data']['tag'] : array();
            if (!empty($arrTag)) {
                $tagFirst = array(
                    array(
                        'tag_name' => $arrTag['tag_name'],
                        'tag_id'   => $arrTag['tag_id'],
                        'tag_intro'=> $arrTag['tag_intro'],
                        'tag_icon' => $arrTag['tag_icon'],
                        'tag_reply_num' => $arrTag['reply_num'],
                        'tag_topic_num' => $arrTag['topic_num'],
                        'tag_follow_num'=> $arrTag['follow_num'],
                    ),
                );     
            }       
        }
    	$arrRes = $this->_getTagFollowNum();
    	$tags_follow_info = isset($arrRes['data']['tags']) ?  $arrRes['data']['tags'] : array();
    	if (!empty($tags_follow_info) && isset($this->_selectRes['rows'])) {
    		
    		foreach ($this->_selectRes['rows'] as $key => $value) {
    			$tag_id = intval($value['tag_id']);
    			if ($tag_id <= 0) {
    				continue;
    			}
                if ($value['tag_name'] == $this->_kw) {
                    unset($this->_selectRes['rows'][$key]);
                    continue;
                }
    			if (isset($tags_follow_info[$tag_id])) {
    				$this->_selectRes['rows'][$key]['tag_reply_num']  = intval($tags_follow_info[$tag_id]['reply_num']);
    				$this->_selectRes['rows'][$key]['tag_topic_num']  = intval($tags_follow_info[$tag_id]['topic_num']);
    				$this->_selectRes['rows'][$key]['tag_follow_num'] = intval($tags_follow_info[$tag_id]['follow_num']);
    			}
    		}
    	}
        $this->_selectRes['rows'] = array_merge($tagFirst, $this->_selectRes['rows']);
        //array_pop($this->_selectRes['rows']);
        
    	return true;
    }
    
	/**
     * @param 
     * @return
     * @throws Util_Exception
     */
    private function _buildUserOutput() {
    	$arrRes = $this->_getUserFollowNum();
    	$users_follow_info = isset($arrRes['data']['user_infos']) ?  $arrRes['data']['user_infos'] : array();
    	if (!empty($users_follow_info) && isset($this->_selectRes['rows'])) {
    		
    		foreach ($this->_selectRes['rows'] as $key => $value) {
    			$user_id = intval($value['user_id']);
    			if ($user_id <= 0) {
    				continue;
    			}
    			if (isset($users_follow_info[$user_id])) {
    				$this->_selectRes['rows'][$key]['user_fans']   = intval($users_follow_info[$user_id]['fan_num']);
                    $this->_selectRes['rows'][$key]['user_intro']  = $users_follow_info[$user_id]['intro'];
                    $this->_selectRes['rows'][$key]['user_gender'] = intval($users_follow_info[$user_id]['gender']);
                    $this->_selectRes['rows'][$key]['user_mark']   = $users_follow_info[$user_id]['mark'];
                    $this->_selectRes['rows'][$key]['user_avatar'] = $users_follow_info[$user_id]['avatar'];
                }
    			
    		}
    	}
    	return true;
    }

    
}
