<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2016/7/24
 * Time: 15:17
 */
class getHotTagsAction extends Util_Base {
    protected static $_ui_ie = 'utf-8';

    const DEF_TAG_NUM = 10;
    const MAX_TAG_NUM = 30;
    const MAX_USER_TAG_NUM = 100;

    /**
     * @param
     * @return
     **/
    public function _execute()
    {
        try {
            $intRn = intval(Bingo_Http_Request::getNoXssSafe('rn', self::DEF_TAG_NUM));
            $intUserId = intval($this->_arrUserInfo['user_id']);

            if($intRn <= 0 || $intRn > self::MAX_TAG_NUM) {
                Bingo_Log::warning("param error, rn=$intRn, user_id=$intUserId");
                throw new Util_Exception('param error', Tieba_Errcode::ERR_PARAM_ERROR);
            }

            //获得用户关注所有标签和后台推荐所有标签ID
            $this->_getUserFollowAndRecommendTagIds($intUserId, $arrUserFollowTagIds, $arrRecommendTagIds);

            //获得去除重复的推荐标签ID
            $this->_getDifferentRecommendTagIds($intRn, $arrUserFollowTagIds, $arrRecommendTagIds, $arrTagIds);

            //获得标签详情
            $this->_getTagDetailInfos($arrTagIds, $arrTagList);

            $arrOutput = array('hot_tags'=>$arrTagList);
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, 'success', $arrOutput);
        } catch (Util_Exception $e) {
            $this->_jsonRet($e->getCode(), $e->getMessage());
        }
    }

    /**
     * @param $arrTagIds
     * @param $arrTagList
     * @throws Libs_Exception
     */
    private function _getTagDetailInfos($arrTagIds, &$arrTagList)
    {
        if (empty($arrTagIds)) {
            $arrTagList = array();
            return;
        }

        $arrParams = array(
            'tag_ids' => $arrTagIds,
            'get_ext_info' => 0,
        );
        $arrRet = Libs_Rpc::callService('nttag', 'mgetTagInfosByIds', $arrParams, 'get');
        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            throw new Util_Exception('tag service error', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        foreach ($arrTagIds as $intTagId) {
            if(!isset($arrRet['data']['tags'][$intTagId])) {
                continue;
            }
            $arrTagList[] = $arrRet['data']['tags'][$intTagId];
        }
    }

    /**
     * @param $intRn
     * @param $arrUserFollowTagIds
     * @param $arrRecommendTagIds
     * @param $arrTagIds
     */
    private function _getDifferentRecommendTagIds($intRn, $arrUserFollowTagIds, $arrRecommendTagIds, &$arrTagIds)
    {
        $arrCandidateTagIds = array_diff($arrRecommendTagIds, $arrUserFollowTagIds);
        shuffle($arrCandidateTagIds);
        if(count($arrCandidateTagIds) >= $intRn) {
            $arrTagIds = array_slice($arrCandidateTagIds, 0, $intRn);
        } else {
            $arrCandidatePaddingTagIds = array_diff($arrRecommendTagIds, $arrCandidateTagIds);
            shuffle($arrCandidatePaddingTagIds);
            $arrPaddingTagIds = array_slice($arrCandidatePaddingTagIds, 0, $intRn - count($arrCandidateTagIds));
            $arrTagIds = array_merge($arrCandidateTagIds, $arrPaddingTagIds);
        }
    }

    /**
     * @param $intUserId
     * @param $arrUserFollowTagIds
     * @param $arrRecommendTagIds
     * @throws Libs_Exception
     */
    private function _getUserFollowAndRecommendTagIds($intUserId, &$arrUserFollowTagIds, &$arrRecommendTagIds)
    {
        if($intUserId > 0) {
            $arrReqs['get_user_follow_tag_ids'] = array(
                'serviceName' => 'ntuser',
                'method' => 'getFollowBySceneId',
                'input' => array(
                    'scene_id' => 101001,
                    'user_id' => $intUserId,
                    'pn' => 1,
                    'rn' => self::MAX_USER_TAG_NUM,
                ),
                'httpMethod' => 'get',
            );
        }
        $arrReqs['get_recommend_tags'] = array(
            'serviceName' => 'nttag',
            'method' => 'getRecommendTags',
            'input' => array(
                'rn' => 0,
                'get_full_info' => 0,
            ),
            'httpMethod' => 'get',
        );

        $arrMultiOutputs = Libs_Rpc::mcallServices(__METHOD__, $arrReqs);

        if($intUserId > 0) {
            $this->_processGetUserFollowTagIdsResult($arrMultiOutputs['get_user_follow_tag_ids'], $arrUserFollowTagIds);
        } else {
            $arrUserFollowTagIds = array();
        }

        $this->_processGetRecommendTagsResult($arrMultiOutputs['get_recommend_tags'], $arrRecommendTagIds);

    }

    /**
     * @param $arrRet
     * @param $arrUserFollowTagIds
     */
    private function _processGetUserFollowTagIdsResult($arrRet, &$arrUserFollowTagIds)
    {
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('get user follow tags fail, ret='.serialize($arrRet));
            $arrUserFollowTagIds = array();
            return;
        }

        $arrUserFollowTagIds = !empty($arrRet['data']['follow_ids']) ? $arrRet['data']['follow_ids']:array();
    }

    /**
     * @param $arrRet
     * @param $arrRecommendTagIds
     * @throws Libs_Exception
     */
    private function _processGetRecommendTagsResult($arrRet, &$arrRecommendTagIds)
    {
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            throw new Util_Exception('tag service error', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrRecommendTagIds = array_column($arrRet['data']['list'], 'tag_id');
    }
}
