<?php
class addTopicAction extends Util_Base {
    protected static $_ui_ie = 'utf-8';
    
    /**
     * @param
     * @return array
     **/
    public function _execute()
    {
        //参数获取
        $user_id = intval($this->_arrUserInfo['user_id']);
        $virtual_uid = intval(Bingo_Http_Request::getNoXssSafe('virtual_uid', 0));
        $title = strval(Bingo_Http_Request::getNoXssSafe('title', ''));
        $tags = Bingo_Http_Request::getNoXssSafe('tags', '');
        $description = strval(Bingo_Http_Request::getNoXssSafe('description', ''));
        
        if( 0 >= $user_id ){
            Bingo_Log::warning('user is not login');
            return $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN, Tieba_Error::getErrmsg(Tieba_Errcode::ERR_USER_NOT_LOGIN));
        }
        
        if(!$title || !$tags) {
            Bingo_Log::warning('params error');
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR,'参数错误');
            return false;
        }
        
        Bingo_Log::pushNotice('from', 'PC');
        
        //验证用户或是马甲账户是否有权限发布内容
        $virtualUsers = $this->getVirtualUsers($user_id);
        if( empty($virtualUsers) ){
            Bingo_Log::warning('user is not allow to addReply. [uid='.$user_id.']');
            $this->_jsonRet(Tieba_Errcode::VIDEO_USER_AUTH_LIMIT,'您无权限发布内容！');
            return false;
        }
        if( $virtual_uid > 0 && !isset($virtualUsers[$user_id][$virtual_uid]) ){
            Bingo_Log::warning('virtual user is not allow to addReply. [virtual_uid='.$virtual_uid.']');
            $this->_jsonRet(Tieba_Errcode::VIDEO_USER_AUTH_LIMIT,'您无权限发布内容！');
            return false;
        }
        $user_id = $virtual_uid > 0 ? $virtual_uid : $user_id;

        // ueg策略
        if (!$this->antiCheck('topic',$title) || !$this->antiCheck('description',$description)){
            Bingo_Log::warning("antiCheck error");
            return $this->_jsonRet( Tieba_Errcode::ERR_UEG_CHECK_FAILED, '对不起，您提交的内容不符合相关法律法规！' );
        }
        
        $ret = Libs_Rpc::callService('nttopic','addTopic',compact('user_id','title','description','tags'),'post','php','utf-8','local');
        if($ret['errno'] === Tieba_Errcode::ERR_PARAM_ERROR){
            Bingo_Log::warning(sprintf('call addTopic error [output:%s]',serialize($ret)));
            return $this->_jsonRet( Tieba_Errcode::ERR_PARAM_ERROR, '参数错误' );
        }
        
        if(false === $ret || Tieba_Errcode::ERR_SUCCESS !== $ret['errno'] ){
            Bingo_Log::warning(sprintf('call addTopic error [output:%s]',serialize($ret)));
            return $this->_jsonRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
        }
        
        //add log
        Tieba_Stlog::addNode('topic_id', intval($ret['data']['topic_id']));
        Tieba_Stlog::addNode('tag_id', implode('_', $tags));
        
        return $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,'',$ret['data']);
    }
}