<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date      2016-08-16 15:16:31
 * @version
 * @structs & methods(copied from idl.)
 * @brief     内容发布平台
 */
class getPublicTopics extends Util_Base {

    protected static $_ui_ie        = 'utf-8';
    protected static $_virtualUsers = null;

    public function _execute()
    {
        $this->_getUserInfo();
        $user_id = empty( $this->_arrUserInfo['user_id'] ) ? 0 : $this->_arrUserInfo['user_id'];
        //        Bingo_Log::pushNotice("ispv", 1);
        $pn = Bingo_Http_Request::get( 'pn', 1 );
        $rn = Bingo_Http_Request::get( 'rn', 10 );
        if ( $user_id <= 0 ) {
            Bingo_Log::warning( 'user is not login' );
            $this->_jsonRet( Tieba_Errcode::ERR_USER_NOT_LOGIN,
                             Tieba_Error::getErrmsg( Tieba_Errcode::ERR_USER_NOT_LOGIN ) );
            return;
        }
        //getVirtualUsers
        $virtualUsers = $this->getVirtualUsers( $user_id );
        if ( empty( $virtualUsers ) ) {
            //用户不是白名单用户，无权访问该页面
            Bingo_Log::warning( 'user has no access' );
            $this->_jsonRet( Tieba_Errcode::ERR_NO_RIGHT, Tieba_Error::getErrmsg( Tieba_Errcode::ERR_NO_RIGHT ) );
            return;
        }
        $virtualUsers = $virtualUsers[ $user_id ]; // 获取第一个不是自己的user_id
        $public_id    = 0;
        foreach ( $virtualUsers as $virtual_id => $user_name ) {
            if ( $user_id != $virtual_id ) {
                $public_id = $virtual_id;
                break;
            }
        }
        if ( $public_id == 0 ) {
            //用户没有关联的公众号
            Bingo_Log::warning( 'user has no public id' );
            $this->_jsonRet( Tieba_Errcode::ERR_NO_RECORD, Tieba_Error::getErrmsg( Tieba_Errcode::ERR_NO_RECORD ) );
            return;
        }
        // 以这个公众要id来后去资料
        $arrParams = [
            'user_id' => $public_id,
            'pn'      => $pn,
            'rn'      => $rn,
        ];
        $arrRet    = Tieba_Service::call( 'ntuser', 'getMyTopics', $arrParams, null, null, 'post', 'php', 'utf-8' );
        if ( Tieba_Errcode::ERR_SUCCESS != $arrRet['errno'] ) {
            Bingo_Log::warning( "call ntuser::getMyTopics fail, input=" . serialize( $arrParams )
                                . ',ret=' . serialize( $arrRet ) );
            $this->_jsonRet( Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'ntuer service error' );
            return;
        }
        $topic_ids = $arrRet['data']['follow_ids'];
        $page      = $arrRet['data']['page'];
        $arrTopicInput = [
            'topic_ids' => $topic_ids,
        ];
        $arrTopicInfo  = Tieba_Service::call( 'nttopic', 'mgetTopicInfo', $arrTopicInput, null, null, 'post', 'php',
                                              'utf-8' );
        if ( Tieba_Errcode::ERR_SUCCESS != $arrRet['errno'] ) {
            Bingo_Log::warning( sprintf( "call mgetTopicInfo error [input:%s] [output:%s]",
                                         serialize( $arrTopicInput ), serialize( $arrTopicInfo ) ) );
            $this->_jsonRet( $arrTopicInfo['errno'], 'nttopic service error' );
            return;
        }
        $topicList = $arrTopicInfo['data'];
        //        $arrList['page']  =  $topicList['data']['page'];
        //顺序格式化
        //        foreach($topic_ids as $value){
        //            $arrList['list'][] = $topicList[$value];
        //        }
        $ret = [
            'list' => $topicList,
            'page' => $page,
        ];
        $this->_jsonRet( 0, 'success', $ret );
        return;
    }
}