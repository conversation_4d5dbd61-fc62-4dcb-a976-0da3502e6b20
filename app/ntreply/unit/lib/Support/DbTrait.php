<?php namespace TiebaTest\Support;

use TiebaTest\Db\Medoo;

trait DbTrait {

    public function haveInDatabase( $table, array $data, $primarys = [ ] ) {
        $db = new Medoo($this->connection);
        try {
            $insert = $db->insert( $table, $data );
            $this->assertTrue( $insert == 0 );
        } catch ( \Exception $e ) {
            if ( $e->getCode() == 1062 ) { // 重复键
                // update
                $condition = [ ];
                if ( !is_array( $primarys ) ) {
                    $primarys = (array)$primarys;
                }
                $primary_data = $data;
                foreach ( $primarys as $primary ) {
                    $condition[ $primary ] = $data[ $primary ];
                    unset( $data[ $primary ] );
                }
                if ( !empty( $condition ) && !empty( $data ) ) {
                    $where = [
                        'and' => $condition
                    ];
                    $sql = $db->delete( $table, $where );
                    //dd($sql);
                    $this->assertTrue($sql>0);
                    //$this->haveInDatabase()
                    $insert = $db->insert( $table, $primary_data );
                    //dd($sql, $insert);
                    //$this->assertTrue( $insert > 0 );
                }
            }
        }
    }

    public function seeInDatabase( $table, $criteria = [ ] ) {
        $db    = new Medoo($this->connection);
        $where = [
            'and' => $criteria,
        ];
        $data  = $db->select( $table, '*', $where );
        $this->assertTrue( count( $data ) > 0 );
    }

    public function dontSeeInDatabase( $table, $criteria = [ ] ) {
        $db    = new Medoo($this->connection);
        $where = [
            'and' => $criteria,
        ];
        $data  = $db->select( $table, '*', $where );

        $this->assertTrue( count( $data ) == 0 );
    }

    public function dontHaveInDatabase( $table, $criteria = [ ], $connection = null ) {
        if($connection) {
            $db = new Medoo($connection);
        }
        else {
            $db     = new Medoo($this->connection);
        }
        $where  = [
            'and' => $criteria,
        ];
        //$delete = $db->debug()->delete( $table, $where );
        $delete = $db->delete( $table, $where );
        //dd($delete);
        $this->assertTrue( count( $delete ) <= 1 );
    }

}