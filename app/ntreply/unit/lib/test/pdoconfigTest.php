<?php
/**
 * Created by PhpStorm.
 * User: zhuanxu
 * Date: 2/9/15
 * Time: 9:18 PM
 */

require_once "../Db/PdoConfig.php";

//use TiebaTest\Db;

class pdoconfigTest extends PHPUnit_Framework_TestCase {
    function testPdo() {
        $pdo = new \TiebaTest\Db\PdoConfig("forum_tbmall");
        $query     = 'SHOW TABLES';
        $statement = $pdo->prepare($query);
        $statement->execute();
        $tableNames = array();
        while (($tableName = $statement->fetchColumn(0))) {
            $tableNames[] = $tableName;
        }
        var_dump($tableNames);
    }

}