<?php
/**
 * Created by PhpStorm.
 * User: zhuanxu
 * Date: 2/3/15
 * Time: 11:05 PM
 */
namespace TiebaTest\Db;

class DbFunction{
    public static function truncate($db, $table, $charset='utf8') {
        $objTbMysql = \Tieba_Mysql::getDB($db);
        if($objTbMysql && $objTbMysql->isConnected()) {
            $objTbMysql->charset($charset);
        }
        else {
            return false;
        }

        $sql = sprintf("delete from %s",$table);

        $result = $objTbMysql->query($sql);
        //var_dump($result);
        return $result;
    }
}