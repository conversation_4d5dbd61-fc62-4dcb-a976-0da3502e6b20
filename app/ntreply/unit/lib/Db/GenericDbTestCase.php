<?php
/**
 * Created by PhpStorm.
 * User: zhuanxu
 * Date: 2/2/15
 * Time: 4:42 PM
 */
namespace TiebaTest\Db;

abstract class GenericDbTestCase extends PHPUnit_Extensions_Database_TestCase {
    private $pdo = null;
    /**
     * Returns the test database connection.
     *
     * @return PHPUnit_Extensions_Database_DB_IDatabaseConnection
     */
    protected function getConnection()
    {
        if ($this->pdo === null) {
            $pdo = new PDOConfig('forum_tbguess');
            $this->pdo = $this->createDefaultDBConnection($pdo);
        }
        return $this->pdo;
    }
}
