<?php

require_once "../../vendor/autoload.php";
require_once "../../setUp.php";

//use Mockery as m;

class refundTest extends PHPUnit_Framework_TestCase {

    public function testRefund()
    {
        $order_id = 116050433986846;
        $dealOrder = Model_Deal_DealOrderRepository::find_by_order_id($order_id);
        $payOrder = Model_Payment_PaymentOrderRepository::find_by_pay_id($dealOrder->pay_id);
//        $ret = Service_Deal_Deal::walletPasswordNotify(
//            [
//                'order_id' => $order_id,
//                'type' => 2,
//                'nmq' => 0,
//            ]
//        );
//        $this->assertTrue(Service_Libs_Base::isSuccess($ret));
        
        $ret = Service_Pay_Refund::refund(
            [
                'pay_id' => $payOrder->pay_id,
                'order_id' => $payOrder->order_id,
                'pay_type' => $payOrder->pay_type,
                'refund_type' => Model_Refund_RefundOrder::REFUND_TYPE_SELLER,
            ]
        );
        dd($ret);
    }
}