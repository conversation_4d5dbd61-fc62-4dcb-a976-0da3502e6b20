<?php
require_once "../../vendor/autoload.php";
require_once "../../setUp.php";

//use Mockery as m;

class confirmTest extends PHPUnit_Framework_TestCase {

    public function testConfirmTrans()
    {
        $order_id = 116050425988941;
        $dealOrder = Model_Deal_DealOrderRepository::find_by_order_id($order_id);

//        $ret = Service_Deal_Deal::walletNotifyConfirmTrans(
//            $order_id
//        );
        Service_Pay_Pay::confirmPayTrans(
            [
                'order_id' => $order_id, 
                'pay_id' => $dealOrder->pay_id,
            ]
        );
    }
}