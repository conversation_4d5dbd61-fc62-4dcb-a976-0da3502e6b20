<?php

require_once "../../vendor/autoload.php";
require_once "../../setUp.php";

//use Mockery as m;

class productTest extends PHPUnit_Framework_TestCase {


    const USER_ID = 778771;

    public function testProductList()
    {
        $list = Service_Product_Product::listProduct(
            [
                'user_id' => self::USER_ID,
//                'is_guest' => 1,
                'size' => 1,
            ]
        );
        $this->assertTrue(Service_Libs_Base::isSuccess($list));
    }
    public function testGetDisplayWindowInfo()
    {
        $list = Service_Product_Product::getDisplayWindowInfo(
            [
                'user_id' => self::USER_ID,
                //                'is_guest' => 1,
                'size' => 1,
            ]
        );
        $this->assertTrue(Service_Libs_Base::isSuccess($list));
    }
    public function testDetailProduct()
    {
        $list = Service_Product_Product::detailProduct(
            [
                'product_id' => 17,
            ]
        );
        $this->assertTrue(Service_Libs_Base::isSuccess($list));
    }

    public static function testDisplayWindow( )
    {
        $info = Service_Product_Product::displayWindow(
            [
                'user_id' => self::USER_ID,
                'product_ids' => [17],
            ]
        );
        dd($info);
    }

}