<?php
require_once "../../vendor/autoload.php";
require_once "../../setUp.php";

//use Mockery as m;

class buyerTest extends PHPUnit_Framework_TestCase {

    /**
     * @type string
     */
    protected $connection = 'forum_ecom';

    use \TiebaTest\Support\DbTrait;




    const USER_ID = 1496;

    public function testSetDefaultAddress() {
        $ret = Service_Buyer_Buyer::setDefaultAddress(
            [
                'user_id' => 1496,
                'delivery_id' => 12,
            ]
        );
//        var_dump(__LINE__);
        dd($ret);
    }

    public function testGetDefaultAddress() {

        $ret = Service_Buyer_Buyer::getDefaultAddress(
            [
                'user_id' => 1496,
            ]
        );
        var_dump(__LINE__);
        dd($ret);
    }


}