<?php
require_once "../../vendor/autoload.php";
require_once "../../setUp.php";

//use Mockery as m;

class sellerTest extends PHPUnit_Framework_TestCase {

    /**
     * @type string
     */
    protected $connection = 'forum_ecom';

    use \TiebaTest\Support\DbTrait;




    const USER_ID = 1496;

    public function testExpire() {
        $arrParams = [
            'key'     => 'aad',
            'seconds' => 1
        ];
        $redis_out = Util_Redis::redisQuery( Util_Redis::REDIS_NAME, "EXPIRE", $arrParams );
        dd($redis_out);
    }

    public function testGetCode() {
        $ret = Service_Seller_Seller::phoneCode(
            [
                'user_id' => 1496,
                'phone' => 18310273231,
            ]
        );
        dd($ret);
    }

    public function testVerifyCode() {
        $ret = Service_Seller_Seller::phoneVerify(
            [
                'user_id' => 1496,
                'phone' => 18310273231,
                'code' => 376006
            ]
        );
        dd($ret);
    }
}