<?php
require_once "../../vendor/autoload.php";
require_once "../../setUp.php";

//use Mockery as m;

class dealTest extends PHPUnit_Framework_TestCase {

    /**
     * @type string
     */
    protected $connection = 'forum_ecom';

    use \TiebaTest\Support\DbTrait;

    const USER_ID = 1496;
    const SELLER_ID = 778771;
    /**
     * 测试创建订单
     */
    public function testCreateOrder() {
        $ret = $this->createOrder();
        $this->assertTrue(Service_Libs_Base::isSuccess($ret));
    }

    public function testModifyFee() {
        $ret = $this->createOrder();
        $this->assertTrue(Service_Libs_Base::isSuccess($ret));

        $order_id = $ret['data']['order_id'];
        $ret = Service_Deal_Deal::modifyShipFee(
            [
                'order_id' => $order_id,
                'user_id' => self::SELLER_ID,
                'ship_fee' => 40,
            ]
        );
        $this->assertTrue(Service_Libs_Base::isSuccess($ret));
    }

    public function testCancelOrder(  ) {
        $ret = $this->createOrder();
        $this->assertTrue(Service_Libs_Base::isSuccess($ret));
        $order_id = $ret['data']['order_id'];
        $ret = Service_Deal_Deal::cancelOrder(
            [
                'order_id' => $order_id,
                'user_id' => self::USER_ID,
            ]
        );
        $this->assertTrue(Service_Libs_Base::isSuccess($ret));
    }

    /**
     * @group pay_delivery
     * @group pay_refund
     * @return int 
     */
    public function testPayOrder() {
        $ret = $this->createOrder();
        $this->assertTrue(Service_Libs_Base::isSuccess($ret));
        $order_id = $ret['data']['order_id'];

        $ret = Service_Deal_Deal::detailOrder(
            [
                'order_id' => $order_id,
                'user_id' => self::USER_ID,
            ]
        );
        $this->assertTrue(Service_Libs_Base::isSuccess($ret));
        $ret = Service_Deal_Deal::payOrder(
            [
                'order_id' => $order_id,
                'user_id' => self::USER_ID,
                'total_price' => $ret['data']['total_price'],
            ]
        );
        $this->assertTrue(Service_Libs_Base::isSuccess($ret));

        $pay_id = json_decode($ret['data']['pay_url']['extra']);

        $ret = Service_Deal_Deal::confirmDealOrder(
            [
                'order_id' => $order_id,
                'pay_id' => $pay_id,
                'bfb_order_no' => rand(1000000,9999999),
                'total_real_price' => 1000,
            ]
        );
        $this->assertTrue(Service_Libs_Base::isSuccess($ret));
        return $order_id;
    }


    /**
     * @group pay_delivery
     * @depends testPayOrder
     * @param $ordr_id
     */
    public function testDeliveryOrder($ordr_id) {
        // delivery_order
        $ret = Service_Deal_Deal::deliveryOrder(
            [
                'order_id' => $ordr_id,
                'user_id' => self::SELLER_ID,
                'company' => 'zhongtong',
                'delivery_no'=>379015130226,
            ]
        );
        $this->assertTrue(Service_Libs_Base::isSuccess($ret));
    }

    public function testListOrder() {
        $order = Service_Deal_Deal::listOrder(
            [
                'role' => 'seller',
                'user_id'=> self::SELLER_ID,
                'pageRequest' => [],
            ]
        );
        $this->assertTrue(Service_Libs_Base::isSuccess($order));
        $order = Service_Deal_Deal::listOrder(
            [
                'role' => 'buyer',
                'user_id'=> self::USER_ID,
                'pageRequest' => [],
            ]
        );
        $this->assertTrue(Service_Libs_Base::isSuccess($order));

    }

    public function testOrderDetail() {
        $ret = $this->createOrder();
        $this->assertTrue(Service_Libs_Base::isSuccess($ret));
        $order_id = $ret['data']['order_id'];
        $ret = Service_Deal_Deal::detailOrder(
            [
                'order_id' => $order_id,
                'user_id' => self::USER_ID,
            ]
        );
        $this->assertTrue(Service_Libs_Base::isSuccess($ret));
    }


    /**
     * @group   pay_refund
     * @depends testPayOrder
     * @param $order_id
     */
    public function testRefundOrder($order_id) {
        
        $ret = Service_Deal_Deal::refundOrder(
            [
                'order_id' => $order_id,
                'user_id' => self::SELLER_ID,
            ]
        );
        dd($ret);
    }

    /**
     * @param $order_id
     */
    public function testConfirmTransOrder($order_id) {

        $this->markTestSkipped();


        //confirmPayOrder
        $ret = Service_Deal_Deal::confirmDealOrder(
            [
                'order_id' => $order_id,
                'pay_id' => 1,
                'total_real_price' => 1,
            ]
        );
        var_dump($ret);
        // delivery_order
        $ret = Service_Deal_Deal::deliveryOrder(
            [
                'order_id' => $order_id,
                'user_id' => 1,
                'delivery_no' => 1,
            ]
        );
        var_dump($ret);

        $ret = Service_Deal_Deal::confirmTransOrder(
            [
                'order_id' => $order_id,
                'user_id' => self::USER_ID,
            ]
        );
        var_dump($ret);
        //confirmPayOrder
        $ret = Service_Deal_Deal::confirmDealOrder(
            [
                'order_id' => $order_id,
            ]
        );
        var_dump($ret);
    }

    /**
     * @return array
     */
    protected function createOrder()
    {
        $ret = Service_Deal_Deal::createOrder(
            [
                'user_id'          => self::USER_ID,
                'product_id'       => 1,
                'thread_id'        => 3900040848, // 数据库中存在
                'delivery_address' => "凤虎街道",
                'delivery_name'    => '凤虎',
                'buyer_name'       => 'abc',
                'buyer_phone'      => '18610664376',
                'zip_code'         => 0,
                'unit_count'       => 1,
                'order_msg'        => "我只要最好的",
            ]
        );

        return $ret;
    }

}