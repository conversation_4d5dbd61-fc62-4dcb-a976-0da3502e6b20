<?php
require_once "../../vendor/autoload.php";
require_once "../../setUp.php";

//use Mockery as m;

class dealTest extends PHPUnit_Framework_TestCase {

    /**
     * @type string
     */
    protected $connection = 'forum_ecom';

    use \TiebaTest\Support\DbTrait;

    const USER_ID   = 1496;

    const SELLER_ID = 778771;

    public function testSetUserAttribute()
    {
        Service_Deal_Buyer::asyncUpdateBuyerInfo( self::USER_ID, 'buyer_order_stamp', time() );
    }

}