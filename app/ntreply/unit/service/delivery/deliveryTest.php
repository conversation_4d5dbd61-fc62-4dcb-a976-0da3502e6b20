<?php
require_once "../../vendor/autoload.php";
require_once "../../setUp.php";

//use Mockery as m;

class deliveryTest extends PHPUnit_Framework_TestCase {

    /**
     * @type string
     */
    protected $connection = 'forum_ecom';

    use \TiebaTest\Support\DbTrait;

    
    
      
    const USER_ID = 1496;


    public function testSetDeliveryInfo() {
//        $order_id = Service_Deal_Orderid::genOrderId();
//        Service_Delivery_Delivery::setDeliveryInfoToRedis("adsdad",$order_id);
//        $ret = Service_Delivery_Delivery::getDeliveryInfoFromRedis($order_id);
//        dd($ret);
        $ret = Service_Delivery_Delivery::pollDeliveryInfo(
            [
                'company' => 'zhongtong',
                'delivery_no'=>379015130226,
                'order_id'=> 1,
            ]
        );
        $ret = Service_Delivery_Delivery::getGuessCompany(
            [
                'delivery_no'=>379015130226,
            ]
        );
        var_dump($ret);
    }

    public function testGetDeliveryInfo() {
        $ret = Service_Delivery_Delivery::getDeliveryInfo(
            [
                'delivery_no'=>379015130226,
                'order_id'=> 1,
            ]
        );
        dd($ret);
    }
}