<?php
/**
 * Created by PhpStorm.
 * User: 
 * Date: 7/17/16
 * Time: 16:52 PM
 */

class Dl_Db_Ntreply extends Dl_Db_Model{
    public static $_db_table = 'ntreply';
    public static $_db_name = 'forum_ntreply';
    private static $_arrDB;
    private static $_instance;
    
    public static $fields = array(
        'reply_id',
        'topic_id',
        'rank',
        'user_id',
        'user_name',
        'nickname',
        'is_anonymous',
        'floor_num',
        'comment_num',
        'forward_num',
        'zan_num',
        'cai_num',
        'title',
        'content',
        'digest',
        'pic',
        'audio',
        'video',
        'link',
        'resource',
        'pic_num',
        'audio_num',
        'video_num',
        'resource_num',
        'reply_type',
        'status',
        'update_time',
        'create_time',
        'ext1',
        'ext2',
    );
    
    /**
     * @brief:
     * @param:
     * @return:
     **/
    public function getModel() {
        return self::$_db_table;
    }
    
    /**
     * @brief:
     * @param:
     * @return:
     **/
    public function getFields() {
        return self::$fields;
    }
    
    /**
     * @brief:
     * @param:
     * @return:
     **/
    public function getDbname() {
        return self::$_db_name;
    }
    
    
}
