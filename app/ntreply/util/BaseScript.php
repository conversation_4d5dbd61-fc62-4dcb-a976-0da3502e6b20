<?php

define ( 'MODULE_NAME', 'ntreply');
define ( 'ROOT_PATH', dirname ( __FILE__ ) . "/../../.." );

define ( 'HOME_PHP_PATH', realpath ( ROOT_PATH . '/php/bin/php' ) );
define ( 'IS_ORP_RUNTIME', true );

if (! defined ( 'REQUEST_ID' )) {
    define ( 'REQUEST_ID', Bingo_Log::getLogId () );
}

if (function_exists ( 'camel_set_logid' )) {
    camel_set_logid ( REQUEST_ID );
}

Tieba_Init::init ( "ntreply" );

abstract class Util_BaseScript {
    function __construct() { //һЩ0mһЩ0m
        Bingo_Log::pushNotice ( 'script_module', 'ntreply');
        Bingo_Timer::start ( 'total' );
        return true;
    }
    function __destruct() { //TICE0mCE0m
        Bingo_Timer::end ( 'total' );
        Bingo_Log::pushNotice ( "script_name", $_SERVER ['PHP_SELF'] );
        $strTimeLog = Bingo_Timer::toString ();
        if (! empty ( $strTimeLog )) {
            $strTimeLog = substr ( $strTimeLog, 0, 100 ); //SCRIPT ֻ ̫ˣݼ[0m[0m
            Bingo_Log::pushNotice ( "Timer", $strTimeLog );
        }
        Bingo_Log::buildNotice ();
    }
    protected function _process() {
        echo "It's a script, please use public function process!";
    }

    /*
     * 发送短信
     */
    public function send_sms($mobiles,$content,$sub_code= ''){

        $url = 'http://emsg.baidu.com/service/sendSms.json';
        $username='tiebamall';
        $password='8E7Vdu3W';
        $msgDest=$mobiles;
        $msgContent=$content;
        $businessCode='tiebamall';
        $signature=md5($username.$password.$msgDest.$msgContent.$businessCode.$sub_code);
        //echo 'signature='.$signature."\n";
        $curlPost='username='.$username.'&businessCode='.$businessCode.'&msgContent='.urlencode($msgContent).'&msgDest='.$msgDest.'&signature='.$signature;
        if ($sub_code !== ''){
            $curlPost .= '&extId='.$sub_code;
        }
        //echo "curlPost=$curlPost\n";

        $ch = curl_init();
        $this_header = array(
            "content-type: application/x-www-form-urlencoded; charset=UTF-8"
        );

        curl_setopt($ch,CURLOPT_HTTPHEADER,$this_header);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HEADER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $curlPost);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_VERBOSE, true);

        $result = curl_exec($ch);
        if($result)
            return $result;
        curl_close($ch);
    }


    abstract public function process();
}