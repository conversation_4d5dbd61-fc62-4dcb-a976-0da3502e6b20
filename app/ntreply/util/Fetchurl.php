<?php

class Util_Fetchurl {

    /**
     * @param       $url
     * @param array $header
     * @param array $cookie
     * @param array $options
     * @param int   $intDefaultRetryTimes
     * @return array|bool
     */
    public static function call( $url,
                                 array $options = array(),
                                 array $header = array(),
                                 array $cookie = array(),
                                 $intDefaultRetryTimes = 1 ) {
        $intRetryTimes = $intDefaultRetryTimes;
        do {
            $httpproxy = Orp_FetchUrl::getInstance( $options );
            Bingo_Timer::start( "fetchurl" );
            $ret = $httpproxy->get( $url, $header, $cookie );
            Bingo_Timer::end( "fetchurl" );
            $errno  = $httpproxy->errno();
            $errmsg = $httpproxy->errmsg();

            Bingo_Log::pushNotice( "fetch_url_url", $url );
            Bingo_Log::pushNotice( "fetch_url_errno_$intRetryTimes", $errno );
            Bingo_Log::pushNotice( "fetch_url_msg_$intRetryTimes", $errmsg );
            Bingo_Log::pushNotice( "fetch_url_ret_$intRetryTimes", $ret );

            if ( $ret === false ) {
                Bingo_Log::warning( "fetchurl return false." );
                if ( intval( $errno ) === 0 ) {
                    Bingo_Log::warning( "fetchurl return false but errno=0." );

                    return false;
                }
            } else {
                break;
            }
        } while ( $intRetryTimes-- > 0 );
        Bingo_Log::pushNotice( "fetch_url_retry_num", $intDefaultRetryTimes - $intRetryTimes );

        return Util_Function::errRet( $errno, $ret, $errmsg );
    }

    /**
     * @param       $url
     * @param array $params
     * @param array $header
     * @param array $cookie
     * @param array $options
     * @param int   $intDefaultRetryTimes
     * @return array|bool
     */
    public static function post( $url,
                                 array $params = array(),
                                 array $options = array(),
                                 array $header = array(),
                                 array $cookie = array(),
                                 $intDefaultRetryTimes = 1 ) {
        $intRetryTimes = $intDefaultRetryTimes;
        do {
            $httpproxy = Orp_FetchUrl::getInstance( $options );
            Bingo_Timer::start( "fetchurl" );
            $ret = $httpproxy->post( $url, $params, $header, $cookie );
            Bingo_Timer::end( "fetchurl" );
            $errno  = $httpproxy->errno();
            $errmsg = $httpproxy->errmsg();

            Bingo_Log::pushNotice( "fetch_url_url", $url );
            Bingo_Log::pushNotice( "fetch_url_errno_$intRetryTimes", $errno );
            Bingo_Log::pushNotice( "fetch_url_msg_$intRetryTimes", $errmsg );
            Bingo_Log::pushNotice( "fetch_url_ret_$intRetryTimes", $ret );

            if ( $ret === false ) {
                Bingo_Log::warning( "fetchurl return false." );
                if ( intval( $errno ) === 0 ) {
                    Bingo_Log::warning( "fetchurl return false but errno=0." );

                    return false;
                }
            } else {
                break;
            }
        } while ( $intRetryTimes-- > 0 );
        Bingo_Log::pushNotice( "fetch_url_retry_num", $intDefaultRetryTimes - $intRetryTimes );

        return Util_Function::errRet( $errno, $ret, $errmsg );
    }
}
