<?php
/**
 *  **/
class Util_Redis{

    protected static $_redis = array();
    /**
     * 个数
     * @type int
     */
    protected static $_redisQueryCounter = 0;



    const REDIS_NAME = "tbnt";
    //const REDIS_NAME = "tbguess";
    const REDIS_KEY  = "product_recommend";

    /**
     * @brief get redis obj.
     * @param null $name
     * @return mixed|null : obj of Bingo_Cache_Redis, or null if connect fail.
     */
    public static function _getRedis($name = null){
        if(is_null($name)){
            $name = self::REDIS_NAME;
        }
        if(isset(self::$_redis[$name])){
            return self::$_redis[$name];
        }
        Bingo_Timer::start('redis_init');
        $redis = new Bingo_Cache_Redis($name);
        Bingo_Timer::end('redis_init');

        if(!$redis || !$redis->isEnable()){
            Bingo_Log::warning("init redis fail.");
            return null;
        }
        self::$_redis[$name] = $redis;
        return self::$_redis[$name];
    }
    
    /**
     * @brief: LIST RPUSH
     * @param
     * @return
     **/
    public static function RPUSH($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->RPUSH($arrInput);
        if($arrRet === false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        
        return true;
    }
    
    /**
     * @brief: LIST LLEN
     * @param
     * @return
     **/
    public static function LLEN($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->LLEN($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'][$arrInput['key']];
        return $data;
    }
    
    /**
     * @brief: LIST LRANGE
     * @param
     * @return
     **/
    public static function LRANGE($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->LRANGE($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'][$arrInput['key']];
        return $data;
    }
	
    public static function GET($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->GET($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'][$arrInput['key']];
        return $data;
    }
    
    public static function SET($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->SET($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        
        return true;
    }
	
    public static function INCR($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->INCR($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'][$arrInput['key']];
        return $data;
    }

    public static function DECR($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->DECR($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'][$arrInput['key']];
        return $data;
    }

    public static function INCRBY($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->INCRBY($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'][$arrInput['key']];
        return $data;
    }

    public static function DECRBY($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->DECRBY($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'][$arrInput['key']];
        return $data;
    }
	
    public static function EXPIRE($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->EXPIRE($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function mDEL($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->DEL(array('reqs' => $arrInput));
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function ZSCORE($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZSCORE($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'][$arrInput['key']]; //直接取出数值
        return $data;
    }

    public static function ZADD($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZADD($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }
	
	public static function ZCARD($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZCARD($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

	public static function ZINCRBY($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
		}
		$arrRet = $redis->ZINCRBY($arrInput);
		if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
	}

	public static function ZREVRANK($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
		$arrRet = $redis->ZREVRANK($arrInput);
		if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
	}

    public static function mZADD($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZADD(array('reqs' => $arrInput));
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function mZINCRBY($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZINCRBY( array('reqs' => $arrInput) );
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function mZREMRANGEBYRANK($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZREMRANGEBYRANK( array('reqs' => $arrInput) );
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function mZRANGEWITHSCORES($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZRANGEWITHSCORES( array('reqs' => $arrInput) );
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function ZREVRANGEWITHSCORES($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZREVRANGEWITHSCORES($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function mZREVRANGEWITHSCORES($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZREVRANGEWITHSCORES( array('reqs' => $arrInput) );
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function mHSET($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HSET( array('reqs' => $arrInput));
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function mHDEL($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HDEL( array('reqs' => $arrInput));
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function mHMGET($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HMGET( array('reqs' => $arrInput));
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function HGET($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HGET($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $key = $arrInput['key'];
        $data = $arrRet['ret'][$key];
        return $data;
    }
    
    /**
     * @param: array
     * @return: int
     **/
    public static function HLEN($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HLEN($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $key = $arrInput['key'];
        $data = $arrRet['ret'][$key];
        return $data;
    }

    public static function HSET($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HSET($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $key = $arrInput['key'];
        $data = $arrRet['ret'][$key];
        return $data;
    }
    
    public static function HDEL($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HDEL($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        
        return true;
    }
    
    public static function HMSET($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HMSET($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $key = $arrInput['key'];
        $data = $arrRet['ret'][$key];
        return $data;
    }
    
    public static function HMGET($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HMGET($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $key = $arrInput['key'];
        $data = $arrRet['ret'][$key];
        return $data;
    }

    public static function HINCRBY($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HINCRBY($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $key = $arrInput['key'];
        $data = $arrRet['ret'][$key];
        return $data;
    }

    public static function HGETALL($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HGETALL($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $key = $arrInput['key'];
        $data = $arrRet['ret'][$key];
        return $data;
    }

    public static function ZREM($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZREM($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function ZRANGE($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZRANGE($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $key = $arrInput['key'];
        $data = $arrRet['ret'][$key];
        return $data;
    }

    public static function ZREVRANGE($arrInput){
        if(null == ($redis = self::_getRedis())){
            Bingo_Log::warning('get redis err!');
            return false;
        }
        $arrRet = $redis->ZREVRANGE($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $key = $arrInput['key'];
        $data = $arrRet['ret'][$key];
        return $data;
    }

    public static function getHashFromRedis($arrInput){
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            $arrOutput = array(
                'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
                'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR),
                'data' => null,
            );
            return $arrOutput;
        }
        $key = $arrInput['key'];

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        $arrParams = array(
            'key' => $key,
        );
        $arrRet = $redis->HGETALL($arrParams);
        //Bingo_Log::debug("get redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
        }
        $data = null;
        if(!is_null($arrRet['ret'][$key])){
            Bingo_Log::pushNotice("_hitRedis_hashkey:$key",1);
            $data = $arrRet['ret'][$key];
        }else{
            Bingo_Log::pushNotice("_hitRedis_hashkey:$key",0);
        }
        return $data;
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;
    }
    //��redis�ж�ȡcache
    public static function getFromRedis($arrInput){
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            $arrOutput = array(
                'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
                'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR),
                'data' => null,
            );
            return $arrOutput;
        }
        $key = $arrInput['key'];

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        $arrParams = array(
            'key' => $key,
        );
        $arrRet = $redis->GET($arrParams);
        //Bingo_Log::debug("get redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
        }
        $data = null;
        if(!is_null($arrRet['ret'][$key])){
            Bingo_Log::pushNotice("_hitRedis_key:$key",1);
            $data = unserialize($arrRet['ret'][$key]);
        }else{
            Bingo_Log::pushNotice("_hitRedis_key:$key",0);
        }
        return $data;
    }
    //��redis�����cache
    public static function setToRedis($arrInput){
        if(!isset($arrInput['key']) || !isset($arrInput['value'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $key = $arrInput['key'];
        $value = $arrInput['value'];
        $setnx = isset($arrInput['setnx'])?intval($arrInput['setnx']):0;
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        $redis = self::_getRedis();
        $arrParams = array(
            'key' => $key,
            'value' => serialize($value),
        );


        if($setnx){
            Bingo_Log::pushNotice("_addRedis_is_setnx_key:$key",1);
            $arrRet = $redis->SETNX($arrParams);
        }else{
            $arrRet = $redis->SET($arrParams);
        }
        //Bingo_Log::debug("set redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::pushNotice("_addRedis_key:$key",0);
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
			return false;
        }else if($expire > 0){
            Bingo_Log::pushNotice("_addRedis_key:$key",1);
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $arrRet = $redis->EXPIRE($arrParams);
            //Bingo_Log::debug("set redis key expire.[key=$key][ret=".serialize($arrRet)."]");
        }
		return true;
    }

    public static function clearFromRedis($arrInput){
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $key = $arrInput['key'];
        $data = 0;

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        $arrParams = array(
            'key' => strval($key),
        );
        $arrRet = $redis->DEL($arrParams);
        //Bingo_Log::debug("clear redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::pushNotice("clearRedis_key:$key",0);
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
        }else{
            $data = 1;
            Bingo_Log::pushNotice("clearRedis_key:$key",1);
        }
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;

    }

	//添加排行榜元素
	public static function addRankItem($arrInput){

	}

    /**
     * redis 操作
     * @param       $strRedisName
     * @param       $strFunc
     * @param array $arrParams
     * @param int   $intRetryTimes
     * @return bool
     */
    public static function redisQuery($strRedisName,$strFunc,array $arrParams,$intRetryTimes = 0){

        $intRetryTimes = intval($intRetryTimes);
        $mixRedis = self::_getRedis($strRedisName);
        if(!$mixRedis){
            return false ;
        }

        self::$_redisQueryCounter++;
        $strTimerKey = "redisquery_".$strRedisName."_".$strFunc."_".self::$_redisQueryCounter;

        do {
            Bingo_Timer::start($strTimerKey);
            $arrRet = $mixRedis->$strFunc($arrParams);
            Bingo_Timer::end($strTimerKey);

            Bingo_Log::debug("$strTimerKey ret=".var_export($arrRet,true));

            if($arrRet['err_no'] !== 0 ){
                Bingo_Log::warning("call redis query error.[redisname=$strRedisName][func=$strFunc][input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
            }else{
                break;
            }
        }while($intRetryTimes-- > 0);

        return $arrRet;
    }

}
/* vim: set noexpandtab ts=4 sw=4 sts=4 tw=100: */
