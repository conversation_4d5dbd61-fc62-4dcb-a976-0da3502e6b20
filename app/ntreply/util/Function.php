<?php
class Util_Function{
	public static function pickSUM($num,array $pool){
		$ret = array();
		$pool_total = array_sum($pool);
		foreach($pool as $key=>$value){
			$ret[$key] = intval($num*$value/$pool_total);
		}
		$ret_total = array_sum($ret);
		if($ret_total < $num){
			$ret_left = $num - $ret_total;
			foreach($pool as $key=>$value){
				if($ret_left <= 0){
					break;
				}
				if($value - $ret[$key] >= $ret_left){
					$ret[$key] += $ret_left;
					break;
				}else if($value > $ret[$key]){
					$sub = $value - $ret[$key];
					$ret[$key] += $sub;
					$ret_left -= $sub;
				}
			}
		}
		return $ret;
	}
	public static function arraySumByKey(array $array1,array $array2){
		$ret = array();
		foreach($array1 as $key=>$value){
			$ret[$key] = $value;
			if(is_numeric($array2[$key])){
				$ret[$key] += $array2[$key];
			}
		}
		return $ret;
	}
	public static function arraySubByKey(array $array1,array $array2){
		$ret = array();
		foreach($array1 as $key=>$value){
			$ret[$key] = $value;
			if(is_numeric($array2[$key])){
				$ret[$key] -= $array2[$key];
			}
		}
		return $ret;
	}
	public static function pickArray(array $array,$index,$unique=true){
		$ret = array();
		foreach($array as $item){
			if(isset($item[$index])){
				$ret[] = $item[$index];
			}
		}
		if($unique && count($ret)>0){
			$ret = array_values(array_unique($ret));
		}
		return $ret;
	}
	//近似平均随机分配蛋糕
	public static function randomAvg($total_num,$left_num,$total_times,$left_times){
		$ret = 0;
		if($left_times <= 1){
			$ret = $left_num;
		}else{
			$arv = intval($total_num/$total_times);
			$low = -($arv-1);
			$high = $arv-1 + $arv*($total_times-$left_times) - ($total_num-$left_num) ;
			$add = rand($low,$high);
			$ret = $arv + $add;
		}
		return $ret;
	}
	public static function millitime(){
		$rate = 1000;
		list($usec, $sec) = explode(" ", microtime());
		return (intval($usec*$rate) + intval($sec*$rate));		
	}
	
	public static function parseCategory($intPropsId){
		return intval(substr($intPropsId,0,3));
	}
	public static function isUper($char){
		$asc = ord($char);
		if($asc >= 65 && $asc <= 90){
			return true;
		}
		return false;
	}
	public static function array2Index(array $array,$index){
		$ret = array();
		foreach($array as $item){
			if(isset($item[$index])){
				$ret[$item[$index]] = $item;
			}
		}
		return $ret;
	}
	public static function array_sum(array $array,$index = null){
		$ret = 0;
		if($index === null){
			$ret = array_sum($array);
		}else{
			foreach($array as $item){
				if(isset($item[$index])){
					$ret += intval($item[$index]);
				}
			}
		}
		return $ret;
	}
	public static function errRet($errno,$data = "",$errmsg=null){
		if(is_null($errmsg)){
			$errmsg = Tieba_Error::getErrmsg($errno);
		}
		$arrRet = array(
				'errno' => $errno,
				'errmsg' => $errmsg,
		);
		if($data !== ""){
			$arrRet['data'] = $data;
		}
		Bingo_Log::pushNotice("errno",$errno);
		return $arrRet;
	}
	
	public static function isSmallFlow($user_id,$key,$value){
		$arrParams = array(
				"user_id" => $user_id,
		);
		$arrRet = Tieba_Service::call("user","getUserData",$arrParams,NULL, NULL, 'post', 'php', 'utf-8');
		if($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning("call getUserData error.[input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
			return false;
		}
		$user_data = $arrRet['user_info'][0];
		if(isset($user_data[$key])&&$user_data[$key] == $value){
			return true;
		}
		return false;
	}
	
	
	public static function jsonRetUi($errno,$errmsg,array $arrExtData=array(), $default_ie = false){
	    $arrRet = array(
	            'no'=>intval($errno),
	            'error'=>strval($errmsg),
	            'data'=>$arrExtData,
	    );
	
	    Bingo_Log::pushNotice("errno",$errno);
	    Bingo_Http_Response::contextType('application/json');
	    if($default_ie){
	        echo Bingo_String::array2json($arrRet);
	    }
	    else{
	        echo Bingo_String::array2json($arrRet, 'utf-8');
	    }
	}
	
	/*
	public static function aaa($array,$key){
		usort($iconInfo, 'Util_Function::_cmp');
	}
	public static function _cmp($a, $b) {
		if (intval($a) === intval($b)) {
			return 0;
		}
		return ($a > $b) ? -1 : 1;
	}
	*/
	
	/* 生成签名 */
    public static function genSign($arrInput, $auth_key){
        if ( empty($auth_key) ){
            return false;
        }
        ksort($arrInput);
        $md5Str = '';
        foreach ($arrInput as $key => $value) {
            $md5Str .= "$key=$value&";
        }
        $md5Str .= $auth_key;
        $md5Sign = md5($md5Str);
		$md5Sign = strtolower($md5Sign);
		return $md5Sign;
    }
}
