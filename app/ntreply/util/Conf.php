<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-04-03 11:10:20
 * @version
 */

class Util_Conf {
	const UI_CONF = "/app/ntreply/ui_ntreply";
	const SERVICE_CONF = "/app/ntreply/service_ntreply";
	
    private static $_conf = array();

    public static function getUiConf(){
        return self::_getConf(self::UI_CONF);
    }
    public static function getServiceConf(){
  		return self::_getConf(self::SERVICE_CONF);
    }
    private static function _getConf($conf){
    	if(self::$_conf[$conf]){
    		return self::$_conf[$conf];
    	}
    	$tmp_conf = Bd_Conf::getConf($conf);
    	
    	if(false === $tmp_conf){
    		Bingo_Log::warning("read conf fail");
    		return null;
    	}
    	self::$_conf[$conf] = $tmp_conf;
    	
    	return $tmp_conf;
    }
}
?>