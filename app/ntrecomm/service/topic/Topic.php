<?php
/**
 * 
 * @breif 贴吧2.0 热门话题
 *
 */
class Service_Topic_Topic{
	
	public    static $service_ie = 'utf-8';
	protected static $_conf = null;
	
	const REDIS_HOT_TOPIC_PREFIX = 'nt_tag_topic_';
	/**
	 * @param
	 * @return boolean
	 */
	public static function init(){
		if(self::$_conf == null){	
			self::$_conf = Bd_Conf::getConf("/app/ntrecomm/service_ntrecomm");
			if(self::$_conf == false){
				Bingo_Log::warning("init get conf fail.");
				return false;
			}
		}
		return true; 
	}
	/**
	 * @param
	 * @return 
	 */
	private static function errRet($errno){
		return array(
			'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
		);
	}
	/**
	 * 正确的返回
	 * @param $ret
	 * @return array
	 */
	private static function succRet($ret) {
		$errCode = Tieba_Errcode::ERR_SUCCESS;
		$errMsg  = Tieba_Error::getErrmsg($errCode);
		return array(
			'errno'  => $errCode,
			'errmsg' => $errMsg,
			'data'   => $ret,
		);
	}

	/**
	 * @brife 贴吧2.0获取标签下热门话题
	 * @param array $arrInput 
	 * array {
	 * 		tag_id: 1,
	 *  	tag_name:体育， //保留字段
	 *      offset: 0,
	 *      res_num:20,
	 * }
	 * @return array(
	 * 		'errno'  => 0,
	 *      'errmsg' => 'success',
	 * 		'data' => array(
	 * 			'tag_id' => 1,
	 * 			'total'  => 100,
	 * 			'topic_ids' => array(1,2,3,4),
	 * 		),
	 * )
	 */
	public static function getHottopicByTag($arrInput) {
		if(!isset($arrInput['tag_id']) || !isset($arrInput['offset']) || !isset($arrInput['res_num'])) {
			Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
			return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$tagId  = intval($arrInput['tag_id']);
		$offset = intval($arrInput['offset']);
		$resNum = intval($arrInput['res_num']);
		if($tagId <=0 || $offset < 0 || $resNum <=0){
			Bingo_Log::warning("param is invaliad . input=" . serialize($arrInput));
			return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$key = self::REDIS_HOT_TOPIC_PREFIX . $tagId;
		$redisVal = Libs_Redis::getKv($key);
		if($redisVal === false){
			Bingo_Log::warning(__FUNCTION__. " get redis failed. input=" . serialize($arrInput));
			return self::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
		}
		$arrTids = array(); 
		$total   = 0;
		if(strlen($redisVal) > 0){
			$tmpArr  = explode(',', $redisVal);
			$total   = count($tmpArr);
			$arrItem = array_slice($tmpArr, $offset, $resNum);
			foreach ($arrItem as $strTids){
				$tmpArr = explode(':', $strTids);
				if(isset($tmpArr[0])){
					$arrTids[] = intval($tmpArr[0]);
				}
			}			
		}	
		$return = array(
			'tag_id'     => $tagId,
			'topic_ids'  => $arrTids,
			'total'      => $total,
		);
		return self::succRet($return);
	}
}
