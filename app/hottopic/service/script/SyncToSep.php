<?php
/**
 * ===========================================
 * @desc: 
 * @author: fengzhen
 * @date: 2015-9-8
 * ===========================================
 * @version 1.0.0
 * @copyright Copyright (c) www.baidu.com
 */
@set_time_limit(0);
@ini_set("memory_limit", "2048M");
Tieba_Init::init("hottopic");
define ( 'ROOT_PATH', dirname ( __FILE__ ) . "/../../.." );
define ( 'IS_ORP_RUNTIME', true );
require_once dirname ( __FILE__ ) . '/../logic/TopicLogic.php';
require_once dirname ( __FILE__ ) . '/../util/Common.php';
require_once dirname ( __FILE__ ) . '/../util/Const.php';
require_once dirname ( __FILE__ ) . '/../../dl/Hottopic.php';
require_once dirname ( __FILE__ ) . '/../util/SendMail.php';

if (! defined ( 'REQUEST_ID' )) {
        define ( 'REQUEST_ID', Bingo_Log::getLogId () );
}
class Service_Script_SyncToSep {
	public static $dataKey = "b76b76bc53544bfa8aa5fc95857ef325";
	public $sendUser = 'fengzhen';
	public $topicList = "";
	public function __construct($sendUser){
		if(!empty($sendUser)){
			$this->sendUser = $sendUser;
		}
	}
	public static function getOnlineTopic(){
		$onlineTopic = array();
		$recommend = array();
		foreach(Service_Util_Const::$recommendConf as $key => $value){
			$arrInput = array(
				're_locate' => $key,
				'num' => $value['num'],
				'need_topic_info' => 1,
			);
			$res = Service_Logic_TopicLogic::getSearchRecommend($arrInput);
			if($res['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				var_dump("getSearchRecommend fail:".serialize($res));
				Bingo_Log::warning("getSearchRecommend fail:".serialize($res));
				continue;
			}
			if(empty($res['ret'])){
				continue;
			}
			$recommend = array_merge($recommend,$res['ret']);
		}
		foreach($recommend as $value){
			if(intval($value['re_type']) === Service_Util_Const::RECOMMEND_TYPE_FORUM || $value['op_status'] != Service_Util_Const::OP_STATUS_TOPIC_IMPORTDATA){
				continue;
			}
			$onlineTopic[$value['mul_id']] = $value['topic_info'];
		}
		return $onlineTopic;
	}
	
	public static function getFreshTopic($arrInput){
		$result = array();
		$input = array(
			'num' => $arrInput['num'],
			'offset' => $arrInput['offset'],
			'op_status' => Service_Util_Const::OP_STATUS_TOPIC_IMPORTDATA,
			'start_time' => $arrInput['start_time'],
			'end_time' => $arrInput['end_time'],
		);
		$res = Service_Logic_TopicLogic::getTopicList($input);
		if($res['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			var_dump("getTopicList fail:out".serialize($res));
			Bingo_Log::warning("getTopicList fail:out".serialize($res));
		}
		return $res['ret'];
	}
	
	public static function getNeedSyncTopic(){
		$totalTopic = array();
		$onlineTopic = self::getOnlineTopic();
		$arrInput = array(
			'num' => Service_Util_Const::MAX_FRESH_TOPIC_NUM,
			'offset' => 0,
			'start_time' => strtotime("-1 day"),
			'end_time' => time(),
		);
		$totalTopic = $onlineTopic;
		$freshTopic = self::getFreshTopic($arrInput);
		if(empty($freshTopic)){
			return $totalTopic;
		}
		foreach($freshTopic as $topic){
			if(!isset($totalTopic[$topic['topic_id']])){
				$totalTopic[$topic['topic_id']] = $topic;
			}
		}
		return $totalTopic;
	}
	
	public static function sendFile($file){
		$storage = new Orp_Storage("yKz3hBQv8DhtpGRFj4v30BNRc8wLBSpv",'orp');
		$realPath = $storage->getLocalRealPath(__FILE__);
		$realDataPath = dirname( $realPath ) . '/../../../../app/hottopic/service/script';
		$realDataPath = '/home/<USER>/../../' . $realDataPath ; // for noah bscp work@xxxx  , avoid matrix@xxx 
		$dataKey = self::$dataKey;
		$host = system("hostname");
		$strCmd = "bscp --setinfo ftp://$host:$realDataPath/$file data://data/$dataKey";
		Bingo_Log::warning("do cmd: [$strCmd]");
		system ( $strCmd, $intRet );
		if ( $intRet ) {
			$strMsg = '拷贝文件到远程失败：[' . $strCmd . ']';
			var_dump($strMsg);
			Bingo_Log::warning($strMsg);
			return false;
		}
		else {
			$strMsg = '拷贝文件到远程成功：[' . $strCmd . ']';
			var_dump($strMsg);
			Bingo_Log::warning($strMsg);
			return true;
		}
	}
	
	public function sendEmail($file,$bolSuccess){
		$mail = new Service_Util_SendMail();
		$sendUserAddress = $this->sendUser . "@baidu.com";
        $mail->setTo(array(
            '<EMAIL>',
        	'<EMAIL>',
        	'<EMAIL>',
        	$sendUserAddress,
        ));
        if($bolSuccess){
        	$title = "【添加配送任务成功】配置文件配送到Sep机器";
        }else{
        	$title = "【添加配送任务失败】配置文件配送到Sep机器";
        }
        $mail->setFrom('<EMAIL>');
        $mail->setCharset("utf-8");
        $mail->setSubject($title . " " . date("Y-m-d"));
        $host = system("hostname");
        $content = "机器：$host<br>"."配送话题列表：<br>".$this->topicList;
        $mail->setHTML($content);
        $mail->setText($content);
        return $mail->send();
	}
	public function process(){
		$totalTopic = self::getNeedSyncTopic();
		if(empty($totalTopic)){
			var_dump("no topic need sync!");
			Bingo_Log::warning("no topic need sync!");
			return true;
		}
		var_dump("topic:[".serialize(array_keys($totalTopic))."] need sync...");
		Bingo_Log::warning("topic:[".serialize(array_keys($totalTopic))."] need sync...");
		$file = Service_Util_Const::$sendToSepFile;
		if(file_exists($file)){
			exec("mv -f $file $file.bak");
		}		
		foreach($totalTopic as $key => $topic){
			$topicId = $topic['topic_id'];
			$topicName = $topic['topic_name'];
			$relateForum = $topic['send_sep_forums'];
			$keywords = $topic['send_sep_keywords'];
			$str = "$topicId\t$topicName\t$relateForum\t$keywords\n";
			file_put_contents ( $file, $str, FILE_APPEND );
			$this->topicList .= $topicName . "<br>";
		}
		$res = self::sendFile($file);
		self::sendEmail($file,$res);
		return true;
	}
}
$sync = new Service_Script_SyncToSep($argv [1]);
$sync->process();