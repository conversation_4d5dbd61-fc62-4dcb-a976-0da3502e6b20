<?php
/**
 * @file: autoOnlineTopic.php
 * @author: <EMAIL>
 * @datetime: 2021-08-18 16:30
 * @brief:自动更改可以上线的话题的状态
 */

require_once dirname(__FILE__) . '/../util/Const.php';
require_once dirname(__FILE__) . '/../../dl/Hottopic.php';

date_default_timezone_set ( "Asia/Chongqing" );
$requestTime = date('Ymd',time());
define ( 'APP_NAME', 'hottopic' );
define ( 'SCRIPT_NAME', 'autoOnlineTopic' );
define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../../..' );
define ( 'SCRIPT_LOG_PATH', ROOT_PATH . '/log/' . APP_NAME );
Bingo_Log::init(array(
    LOG_SCRIPT => array(
        'file' => SCRIPT_LOG_PATH . '/' . SCRIPT_NAME . '/' . SCRIPT_NAME . "_$requestTime.log",
        'level' => 0x01 | 0x02 | 0x04 | 0x08,
    ),
), LOG_SCRIPT );

class autoOnlineTopic
{
    public function process()
    {
        $nowTime = time();
        $input = array(
            'op_status' => Service_Util_Const::OP_STATUS_PRE_ONLINE,//待上线
        );
        $notOnlineTopics = Dl_Hottopic::getRecommendConf($input);
        if(empty($notOnlineTopics)){
            return true;
        }
        $needOnlineTopics = array();
        foreach ($notOnlineTopics['ret'] as $data) {
            if ($data['effective_time'] <= $nowTime) {//到达上线时间
                $needOnlineTopics[] = $data;
            }
        }
        
        foreach($needOnlineTopics as $deleteTopic){
            //删除其他在该推荐位置上的相同话题
            $search = array(
                'mul_id' => $deleteTopic['mul_id'],
                'op_status' => Service_Util_Const::OP_STATUS_EXIST,
            	're_locate' => Service_Util_Const::RECOMMEND_LOCATE_TOTAL_BANG,
            );
            $sea = Dl_Hottopic::getRecommendConf($search);
            if ($sea['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('Get same topic Fail!');
                return false;
            }
            $delReId = array();
            foreach ($sea['ret'] as $data) {
                if($data['re_id'] != $deleteTopic['re_id']){
                    $delReId[] = $data['re_id'];
                }
            }
            if (!empty($delReId)) {
                $del = array(
                    're_id' => $delReId,
                    'op_status' => Service_Util_Const::OP_STATUS_DELETE,
                );
                $res = Dl_Hottopic::updateRecommendConf($del);
                if ($res['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('Delete same topic Fail!');
                    return false;
                }
            }
        }
        $input = array(
            're_locate' => Service_Util_Const::RECOMMEND_LOCATE_TOTAL_BANG,
            'limit' => Service_Util_Const::TOPIC_BANG_NUM + 1,
            'op_status' => Service_Util_Const::OP_STATUS_EXIST,
        );
        $res = Dl_Hottopic::getRecommendConf($input);
        if ($res['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('GET ALL RecommendConf In TOTAL_BANG Fail!');
            return false;
        }
        $arrManualTopicId = array();
        $topicReIds = array();
        foreach ($res['ret'] as $conf) {
            $arrManualTopicId[] = $conf['mul_id'];
            $topicReIds[$conf['mul_id']] = $conf['re_id'];
        }
        $input = array(
            'topic_id' => $arrManualTopicId,
        );
        $topicList = Dl_Hottopic::getTopicList($input);//所有话题info
        if ($topicList['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('GET All Topics Fail!');
            return false;
        }
        
        $input = array(
            'topic_id' => array_column($needOnlineTopics, 'mul_id'),
        );
        $needToOnlineTopics = Dl_Hottopic::getTopicList($input);//本次插入的话题info
        if ($needToOnlineTopics['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('GET RecommendConf for PREONLINE Fail!');
            return false;
        }
        $arrAddIdx = array();

        foreach ($needToOnlineTopics['ret'] as $topic) {
            $arrAddIdx[$topic['idx_bang']] = array(
                'topic_id' => $topic['topic_id'],
                'is_manual' => 1,
                'num' => $topic['discuss_num'],
            );
        }

        foreach ($topicList['ret'] as $topic) {
            $arrManualIdx[$topic['idx_bang']] = array(
                'topic_id' => $topic['topic_id'],
                'is_manual' => 1,
                'num' => $topic['discuss_num'],
            );
        }
        $newTopicList = array();
        $delRecommendTopic = array();
        for ($i = 1; $i < Service_Util_Const::TOPIC_BANG_NUM+2; $i++) {
            if(isset($arrAddIdx[$i])){
                $newTopicList[$i] = $arrAddIdx[$i];
                if (isset($arrManualIdx[$i])) {
                    $delRecommendTopic[] = $topicReIds[$arrManualIdx[$i]['topic_id']];
                }
                continue;
            }
            if (isset($arrManualIdx[$i])) {
                $newTopicList[$i] = $arrManualIdx[$i];
            }
            else{
                $newTopicList[$i] = array(
                    'topic_id' => 0,
                    'is_manual' => 0,
                    'num' => 0,
                );
            }
        }
        if(!empty($delRecommendTopic)){
            $deleteTopics = array(
                're_id' => $delRecommendTopic,
                'op_status' => Service_Util_Const::OP_STATUS_DELETE,
            ); 
            $res = Dl_Hottopic::updateRecommendConf($deleteTopics);
            if ($res['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('del RecommendConf Fail!');
                return false;
            }
        }
        $tempList = $newTopicList;
        foreach ($newTopicList as $inx => $value) {
            $updateInput = array(
                'topic_id' => $value['topic_id'],
                'idx_bang' => $inx,
            );
            $start = $inx + 1;
            while ($start <= Service_Util_Const::TOPIC_BANG_NUM+1) {
                if ($tempList[$start]['is_manual']) {
                    $start++;
                    continue;
                }
                $minNum = $tempList[$start]['num'];
                break;
            }
            if ($inx == 1) {
                $curTopicDisNum = $minNum + rand(50000, 100000)*($start-$inx);
            } else {
                $allLast = $newTopicList[$inx - 1]['num'] - $minNum;
                $riseNumPer = intval($allLast/($start-$inx));
                $startNum = intval($riseNumPer*0.7);
                $curTopicDisNum = $minNum + rand($startNum,$riseNumPer-1)*($start-$inx);
            }
            $newTopicList[$inx]['num'] = $curTopicDisNum;
            $updateInput['discuss_num'] = $curTopicDisNum;
            $updateInput['hot_value'] = $curTopicDisNum;
            if($updateInput['topic_id'] != 0){
                $ret = Dl_Hottopic::updateTopic($updateInput);
                if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('Update RecommendConf Fail!');
                    return false;
                }
            }
        }
        if (!empty($needOnlineTopics)) {
            $updateTopics = array(
                're_id' => array_column($needOnlineTopics, 're_id'),
                'op_status' => Service_Util_Const::OP_STATUS_EXIST,
            ); 
            $res = Dl_Hottopic::updateRecommendConf($updateTopics);
            if ($res['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('Update RecommendConf Fail!');
                return false;
            }
        }
        return true;
    }
}
$obj = new autoOnlineTopic();
for($i = 0;$i < 3;$i++){
    $res = $obj->process();
    if($res){
        break;
    }
}
