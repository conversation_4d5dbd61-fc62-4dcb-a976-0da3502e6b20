<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file getFrsLiveListAction.php
 * <AUTHOR>
 * @date 2015/06/09 14:50:09
 * @brief 
 *  
 **/
class getFrsLiveListAction extends Util_Base{
    public function execute(){
        $arrRet = Tieba_Service::call('tbliveact','getFrsLiveList',array(),null,null,'post','php','gbk','local');
        if($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('get getFrsLiveList fail,the output is[' . serialize($arrRet) .']');
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL,'service call fail');
        }else{
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,'success',$arrRet['data']);
        }
    }
}






/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
