<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file libs/UserPerm.php
 * <AUTHOR>
 * @date 2013/08/12 14:32:25
 * @brief 
 *  
 **/

class Service_Libs_Lcs {


	public static function sendMsgToLCS($content, $type , $room_id = 0, $forum_id = 0) {
		
		if ($type === 1){ //推送本房间
			$arrInput = array(
					'msgType' => 'msgcenter',
					'groundIds' =>  array($room_id),
					'userId' => 0, //填无效值
					'msgBody' => json_encode($content), //消息体JSON串
			);
		}else if ($type ===2 ){ //推送全吧
			//获取吧对应的房价列表。
			$arr_room_ids = array();
			$arrInput = array(
					'msgType' => 'msgcenter',
					'groundIds' =>  $arr_room_ids,
					'userId' => 0, //填无效值
					'msgBody' => json_encode($content), //消息体JSON串
			);
		}
		

		
		return true;
	}

}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>