<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-05-06 10:02:29
 * @version
 */

class Util_Redis {

    private static $_cache;
        
    //cache的pid
    const CACHE_PID = '';
    
    //cache配置总开关，方便测试
	const SWITCH_OF_CACHE = true;
	
	//所有cache的key的前缀，修改前缀即可失效现有所有cache
	const PREFIX_ALL_KEY = 'tbliveact_';

    //初始化cache
    public static function initCache(){
    	if(false === self::SWITCH_OF_CACHE) {
    		return null;
    	}
        if(self::$_cache){
            return self::$_cache ;
        }
        
        Bingo_Timer::start('redis_init');
        self::$_cache = new Bingo_Cache_Redis(self::CACHE_PID);
        Bingo_Timer::end('redis_init');

        if(!self::$_cache || !self::$_cache->isEnable()){
            Bingo_Log::warning("init cache fail.");
            self::$_cache = null;
            return null;
        }
        return self::$_cache;
    }
    //获取单个cache
	public static function getCache($strKey){
    	if(false === self::SWITCH_OF_CACHE) {
    		return null;
    	}
		//add your code
    	
	}
    //批量获取cache
	public static function mgetCache($arrKey){
    	if(false === self::SWITCH_OF_CACHE) {
    		return null;
    	}
		//add your code
	}	
	//删除cache
	public static function removeCache($strKey){
    	if(false === self::SWITCH_OF_CACHE) {
    		return true;
    	}
		//add your code
	}
	//设置cache
	public static function addCache($strKey, $mixValue, $intLifeTime){
    	if(false === self::SWITCH_OF_CACHE) {
    		return true;
    	}
		//add your code
	}
}
?>