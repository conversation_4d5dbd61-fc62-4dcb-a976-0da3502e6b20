<?php
/***************************************************************************
 * 
 * Copyright (c) 2010 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
class DataRelated {
	
	/**
	 * 批量获取主题信息
	 * @param array $arrIn  输入thread_id列表
	 * @param array $arrOut 输出对应主题信息(默认GBK编码);
	 * 				array (
	 * 					'123' =>  // 主题ID 
	 * 						array(
	 * 							'id'    => 123, 	// 主题ID 
	 * 							'title' => 'xxx',   // 标题
	 * 							'reply_num' => 5,   // 回复数
	 * 						),
	 * 				);
	 * @return bool
	 */
	public static function getThreadsInfo ($arrIn, & $arrOut, $strOutEncode = Bingo_Encode::ENCODE_GBK) {
		$arrOut = array();
		$arrOutTmp = array();
		if (! is_array($arrIn)) {
			return false;
		}
		Bingo_Timer::start("frsdi");
		$arrChunk = array_chunk($arrIn, 100);	// 每次最多取100条
		foreach ($arrChunk as $arrTmp) {
			$arrList = Tbapi_Frs_Midl_Frsdi::query($arrTmp);
			/* $arrList
			 * array(1) {
				  ["thread_infos"]=>
				  array(1) {
				    [0]=>
				    array(22) {
				      ["forum_id"]=>
				      int(898666)
				      ["thread_id"]=>
				      int(2040923329)
				      ["forum_name"]=>
				      string(12) "贴吧意见反馈"
				      ["title"]=>
				      string(44) "【招募】贴吧官方微博答疑&#x2022;志愿者大招募"
				      ["user_id"]=>
				      int(690587889)
				      ["user_name"]=>
				      string(14) "TB意见反馈小组"
				      ["user_ip"]=>
				      int(1353287485)
				      ["title_prefix"]=>
				      string(0) ""
				      ["vote_id"]=>
				      int(0)
				      ["post_num"]=>
				      int(98)
				      ["freq_num"]=>
				      int(2564)
				      ["last_modified_time"]=>
				      int(1355386957)
				      ["last_user_id"]=>
				      int(98938681)
				      ["last_user_name"]=>
				      string(13) "ァ芜鍅_莣尐伱"
				      ["last_user_ip"]=>
				      int(3827030387)
				      ["last_post_deleted"]=>
				      int(0)
				      ["good_types"]=>
				      string(1) "1"
				      ["top_types"]=>
				      string(1) "1"
				      ["thread_types"]=>
				      string(4) "1027"
				      ["thread_classes"]=>
				      string(1) "1"
				      ["last_post_id"]=>
				      string(11) "27201811304"
				      ["version"]=>
				      string(11) "19212257979"
				    }
				  }
				}
			 */
			if ($arrList === false) {
				continue;
			}
	        foreach ($arrList['thread_infos'] as $v) {
	        	if (! isset($v['thread_id'])) {
	        		continue;
	        	}
	        	$id = intval($v['thread_id']);
	        	$title = strval($v['title']);
	        	$arrOutTmp[$id] = array(
	        		'id' => $id,
	        		'title' => strval($title),
	        		'reply_num' => intval($v['post_num']),
	        	);
	        }	
		}
        Bingo_Timer::end("frsdi");
		
        if ($strOutEncode == Bingo_Encode::ENCODE_UTF8) {
        	$arrOutTmp = Bingo_Encode::convert($arrOutTmp,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
        }
        $arrOut = $arrOutTmp;
        
        return true;
	}
	
	/**
	 * 根据经纬度坐标获取行政区信息
	 * @param string $strLng  经度
	 * @param string $strLat  纬度
	 * @param string $arrOut  行政区信息
	 * 				array(
	 * 					'province' => '省',
	 * 					'city'     => '市',
	 * 					'district' => '县/区',
	 * 					'street'   => '乡镇/街道',
	 * 				);
	 * @return bool
	 */
	public static function getDistrictFromCoordinate($strLng, $strLat, & $arrOut) {
		$arrOut = array();
		if ($strLng == '' || $strLat == '') {
			return false;
		}
		Bingo_Timer::start("map_geocoder");
		$strLocation = "$strLat,$strLng";
		$arrParam = array(
			'location' => $strLocation,
			'key' => 'da9fdbf328c84009686ab18a2c1bae24',
			'output' => 'json',
		);
		//http://api.map.baidu.com/geocoder?&key=da9fdbf328c84009686ab18a2c1bae24&location=30.957756,116.39076&output=json
		
		$strUrl = '/geocoder';
        //$strOut = Tbapi_Core_Midl_Http::httpcall('map_api',$strUrl,$arrParam);
        $obj = orp_FetchUrl::getInstance();
        $strOut = $obj->get('http://api.map.baidu.com/geocoder?'.http_build_query($arrParam));
        /*   地图接口返回数据结构(UTF-8编码)
             {status: '字符串状态常量', 取值如下：
			 //OK 成功
			 INVILID_KEY 非法密钥   
			 INVALID_PARAMETERS 非法参数，参数错误时候给出。
			 result: {    
			 location: {
			 lat: 纬度：数值，
			 lng: 经度：数值
			 },
			 formatted_address: ‘详细地址描述’,
			 business: '周围商圈',
			 addressComponent:{
			 streetNumber: '门牌号码',
			 street: ‘街道名称’,
			 district: ‘区县名称’,
			 city:’城市名称’,
			 province:’省份名称’
			 }
			 },
			 }
         */
        Bingo_Timer::end("map_geocoder");
        
        // 打印日志使用
        $strTmp = str_replace(array("\n"," ","\t","\r"),'',$strOut);
  		$strTmp = Bingo_Encode::convert($strTmp,Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
        Bingo_Log::debug('map geocoder ' . serialize($arrParam) . ' Out ' . $strTmp);
        
        if (empty($strOut)) {
        	return false;
        }
        
        $arrOutTmp = json_decode($strOut, true);
        $arrOutTmp = Bingo_Encode::convert($arrOutTmp,Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
        $status = strval($arrOutTmp['status']);
        if ($status != 'OK') {
        	return false;
        }
        $arrOut['province'] = strval($arrOutTmp['result']['addressComponent']['province']);
        $arrOut['city'] = strval($arrOutTmp['result']['addressComponent']['city']);
        $arrOut['district'] = strval($arrOutTmp['result']['addressComponent']['district']);
        $arrOut['street'] = strval($arrOutTmp['result']['addressComponent']['street']);
        
		return true;
	}
	
	
	/**
	 * 批量获取吧名对应的吧ID（同时返回可能的主题词）
	 * @param array $arrIn  吧名列表, GBK编码
	 * @param array $arrOut 对应的结果集(默认GBK编码)
	 * 				array(
	 * 					'test' => // 吧名(query)
	 * 						 array(
	 * 							'exist' => 1, 	// 吧是否存在,0-否,1-是
	 * 							'forum_name' => 'test', //吧名, 如果是qury = 'wow', 那么这里就是'魔兽世界'
	 * 							'forum_id' => 35, 		// 对应的吧ID(与forum_name对应的吧的吧ID)
	 * 							'forum_host_level' => 3,    // 吧热度 (0-5数字)
	 * 						 ),
	 * 				);
	 * @return bool
	 */
	public static function getForumByNames ($arrIn, & $arrOut, $strOutEncode = Bingo_Encode::ENCODE_GBK) {
		$arrOut = array();
		$arrOutTmp = array();
		$arrFids = array();
		$arrFid2name = array();
		if (! is_array($arrIn)) {
			return false;
		}
		Bingo_Timer::start("fgate");
		$arrChunk = array_chunk($arrIn, 20);	// 每次最多取20条
		foreach ($arrChunk as $arrTmp) {
			$arrList = Rpc_Fgate::getFidByFnames($arrTmp);
			Bingo_Log::debug('getFidByFnames input[' . serialize($arrIn) . '] output[' . serialize($arrList) . ']');
			if ($arrList === false) {
				continue;
			}
	        foreach ($arrList as $v) {
	        	$qword = strval($v['qword']);
	        	$arrTmp = array();
	        	$arrTmp['exist'] = 1;
	        	if ($v['has_forum_id'] != 1) {
	        		$arrTmp['exist'] = 0;
	        	} else {
	        		$arrTmp['forum_name'] = strval($v['forum_res']['forumname']); // 后端返回数据为GBK，无需转码
	        		$arrTmp['forum_id'] = intval($v['forum_res']['forumid']);
	        		$arrTmp['forum_host_level'] = 0;   // 初始为0, 后面查询到值再修改
	        		// 吧ID加入数组
	        		$arrFids[] = $arrTmp['forum_id'];
	        		$arrFid2name[$arrTmp['forum_id']] = $qword;
	        	}
	        	$arrOutTmp[$qword] = $arrTmp;
	        }	
		}
		Bingo_Timer::end("fgate");
		
		// 获取吧热度
		if (! empty($arrFids)) {
			$ret = self::getForumHotvalueByIds($arrFids, $arrForumHot);
			if ($ret !== false) {
				foreach ($arrForumHot as $k => $v) {
					$qword = $arrFid2name[$k];
					$arrOutTmp[$qword]['forum_host_level'] = $v; 
				}
			}
		}
		
        if ($strOutEncode == Bingo_Encode::ENCODE_UTF8) {
        	$arrOutTmp = Bingo_Encode::convert($arrOutTmp,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
        }
        $arrOut = $arrOutTmp;
        return true;
	}
	
	/**
	 * 批量获取吧热度数据 
	 * @param array $arrIn 吧ID列表
	 * @param array $arrOut 对应吧热度数据
	 * 					array(
	 * 						'35' => 2,
	 * 					);
	 * @return bool
	 */
	public static function getForumHotvalueByIds ($arrIn, & $arrOut) {
		$arrOut = array();
		foreach($arrIn as $fid) {
			$arrOut[$fid] = 3;
		}
		return true;
	}
}
