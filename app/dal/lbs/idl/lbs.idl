struct Area
{
	string provice;
	string city;
	string district; 
	string street;
	string name;
};

struct MapReq
{
	string lng;
	string lat;
	uint32_t thread_num;
	uint32_t forum_num;
};

struct ListReq
{
	string lng;
	string lat;
};

struct ForumInfo
{
	string name;
	uint32_t id;
	uint32_t type;	//0表示地区吧，1表示特定位置打点的吧
	uint32_t distance;		//距离我的距离，地区吧无意义
	uint32_t level;	//吧是几星的
	uint32_t is_like;		//是否喜欢
};

struct ThreadInfo
{
	uint64_t tid;
	uint32_t fid;
	uint32_t type;	//贴子类型,0是普通帖，1是图片贴，2是视频音频，3是投票
	string title;
	string fname;		//吧名
	string pic_url;			//富文本，图片url
	uint32_t reply_num;		//回复数
	uint32_t time; 	//发帖时间
	uint32_t distance;		//距离
};

struct Point
{
	uint32_t type;	//0是帖子点，1是吧
	ForumInfo forum;
	ThreadInfo threads[];
};

struct Zone
{
	Point points[];
	uint32_t count;
	uint32_t distance;
};

struct MapInfo
{
	uint32_t errno;
	uint32_t errmsg;
	Zone zlist[];  
	Area area;
};

struct ListInfo
{
	uint32_t errno;
	uint32_t errmsg;
	ForumInfo forums[];
	ThreadInfo threads[];
	Area area;
};

//lng 经度，lat 纬度
service lbs
{
	void get_thread_map(
		MapReq req,
		out MapInfo data
	);
	
	void get_thread_list(
		ListReq req,
		out ListInfo data
	);
	
};
