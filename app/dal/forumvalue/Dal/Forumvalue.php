<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2012:10:14 14:53:45
 * @version 
 * @structs & methods(copied from idl.)
 * 	struct forum_info
 * 	{
 * 			uint32_t forum_id;
 * 	    uint32_t forum_member;
 * 	    uint32_t forum_new_member;
 * 			uint32_t forum_thread_member;
 * 	    uint32_t forum_sign_member;
 * 	    uint32_t forum_wap_member;
 * 			uint32_t forum_thread_large;
 * 	    uint32_t forum_image_member;
 * 	    double	 forum_value_day;
 *			double	 forum_value_total;
 *			uint32_t forum_rank;
 *			uint32_t forum_grade;
 *			double	 forum_cheat_value;
 *			uint32_t day;
 * 	};
 * 	
 * 	service Forumvalue
 * 	{
 * 	    void get_forum_value
 * 	    (
 * 	        uint32_t forum_id, 
 * 	        out forum_info forum_value,
 * 	    );
 * 	    
 * 	    void get_batch_forum_value
 * 	    (
 * 	        uint32_t[] forum_id, 
 * 	        out forum_info forum_value,
 * 	    );
 * 	    
 * 	    void get_his_forum_value
 * 	    (
 * 	        uint32_t forum_id, 
 * 					uint32_t day,
 * 	        out forum_info forum_value,
 * 	    );
 * 	
 * 			void update_forum_value
 * 		 (	
 * 					forum_info forum_value,
 * 			 		out int32_t status
 * 			);
 * 	
 * 		  void update_forum_rank_value
 * 		  (
 * 					uint32_t forum_id,
 *					uint32_t forum_rank,
 * 					out int32_t status
 * 			);
 * 			void update_forum_cheat_value
 *			(
 * 				uint32_t forum_id,
 * 				int32_t cheat_value,
 * 				out int32_t status
 * 			);
 * 		
 * 		void itieba_commit(
 * 			trans_cmd cmd
 * 		);
 * 	
 * 	};
 * 	
 * 	
**/

//require_once (dirname(__FILE__).'/../conf/Dal_Forumvalue_Config.php');
require_once (dirname(__FILE__).'/../../../../conf/app/dal/forumvalue/Dal_Forumvalue_Config.php');

class Dal_Forumvalue{
	
const SERVICE_NAME = "Dal_Forumvalue";
const CACHE_TYPE_USED='memcached';

const		 RETRY_NUMBER = 2;
const    UPDATE_SUCCESS = 1;
const    UPDATE_NORMAL_ERR = -1; 
const    UPDATE_MYSQL_ERR = -2;
const    UPDATE_MONEY_NOT_ENOUGH = -3;

const		PATCH_NUM = 30;

protected static $_db = null;
private static $_cache = null;

private static function __getDB($balance = false){
	if(self:: $_db){
		return self:: $_db ;
	}
	Bingo_Timer::start('dbconn');
       self:: $_db = Tieba_Mysql::getDB('forum_valueinfo');
/*	self:: $_db = Tieba_DB::getSelfDB(self::SERVICE_NAME, Dal_Forumvalue_Config::$arrDbConfig, $balance);
*/	
	Bingo_Timer::end('dbconn');
	
	if(!self:: $_db){
		Bingo_Log::warning("get db resource fail.");
		return false;
	}
	return self:: $_db;
}

public static function init(){
	if(self::$_db || self:: __getDB()){
		return true;
	}else{
		return false;
	}
}

private static function  _removeCache($strKey){	
	if(!Dal_Forumvalue_Config::$boolCacheOpen)
		return false;	
	if(!self::$_cache){
		//self::$_cache = Bingo_Cache::factory(self::CACHE_TYPE_USED,array(
		//	'confPath' =>dirname(__FILE__).'/../conf/',
		//	'confFileName' => Dal_Forumvalue_Config::$strCacheConfigFile,
		//	));
		self::$_cache = new Bingo_Cache_Memcached("forum_numerical");
	}	
	
	$ret = self::$_cache->remove(strval($strKey));
	if($ret === CACHE_OK){
		return true;
	}else{
		return false;
	}
}
private static function  _addCache($strKey, $mixValue, $intLifeTime=0){
	if(!Dal_Forumvalue_Config::$boolCacheOpen)
		return false;	
	if(!self::$_cache){
		//self::$_cache = Bingo_Cache::factory(self::CACHE_TYPE_USED,array(
		//	'confPath' =>dirname(__FILE__).'/../conf/',
		//	'confFileName' => Dal_Forumvalue_Config::$strCacheConfigFile,
		//	));
		self::$_cache = new Bingo_Cache_Memcached("forum_numerical");
	}	
	
	$ret = self::$_cache->add(strval($strKey), $mixValue, $intLifeTime);
	if($ret === CACHE_OK){
		return true;
	}else{
		Bingo_Log::warning('add cache err no : '.$ret);
		return false;
	}	
}
private static function  _getCache($strKey){
	if(!Dal_Forumvalue_Config::$boolCacheOpen)
		return false;
	if(!self::$_cache){
		Bingo_Timer::start('cacheinit');
		//self::$_cache = Bingo_Cache::factory(self::CACHE_TYPE_USED,array(
		//	'confPath' =>dirname(__FILE__).'/../conf/',
		//	'confFileName' => Dal_Forumvalue_Config::$strCacheConfigFile,
		//	));
		self::$_cache = new Bingo_Cache_Memcached("forum_numerical");
		Bingo_Timer::end('cacheinit');
	}		
	Bingo_Timer::start('cacheget');
	$res = self::$_cache->get($strKey);
	Bingo_Timer::end('cacheget');
	return $res;
}

/**
 * @brief
 * @arrInput:
 * 	uint32_t forum_id
 * @return: $arrOutput
 * 	forum_info forum
**/
public static function get_forum_value($arrInput){
	if(!isset($arrInput['forum_id']) || !isset($arrInput['day'])){
		Bingo_Log::warning('parm forum_id or day is not set.');
		return false;
	}
	
	$intForumId = intval($arrInput['forum_id']);
	if($intForumId < 0){
		Bingo_Log::warning('invalid forum_id got. forum_id:'.$intForumId);
		return false;
	}
	Bingo_Log::pushNotice('forum_id', $intForumId);
	
	//deal cache.
	$strCacheKey = 'forum_value'.$intForumId;
	$arrCacheRes = self::_getCache($strCacheKey);
	if($arrCacheRes){
		Bingo_Log::debug('get forum value hit cache. uid:'.$intForumId);
		Bingo_Log::pushNotice('hitcache','1');
		//var_dump($arrCacheRes);
		
		return $arrCacheRes;
	} 
	
	Bingo_Log::pushNotice('hitcache','0');
	
	if(!self::init()){
		return false;
	}
	
	//get current day	
	$intDay = intval($arrInput['day']);
	$intTableIndex = intval($intForumId % Dal_Forumvalue_Config::$intTableDiv);
	
	$retry = 0;	
	while($retry<self::RETRY_NUMBER){		
		$intDayValue = self::_get_day($intDay);
		
		//��ȡ����ֵ	
		$strSql = 'select forum_value_total, forum_rank, forum_grade, forum_change_value from forum_value'.$intTableIndex.' where forum_id = '.$intForumId .' and day = '.$intDayValue.';';	
		Bingo_Timer::start('dbquery');
		$res = self::$_db ->query($strSql);
		Bingo_Timer::end('dbquery');				
		if($res === false){
				Bingo_Log::warning('query failed. errno:['.self::$_db->errno().']['.self::$_db->error().']sqlStr:'.$strSql);
		}
		if(!empty($res))
			break;	
		$intDay = $intDay - 24*3600;
		$retry ++;
	}
	
	 //��ȡ��ߵȼ��ķ��ٶ�			
		$strSql = 'select totalNumber from forum_compute_info where intday = '.$intDayValue.';';	
		Bingo_Timer::start('dbqueryTotalNumber');
		$resStatus = self::$_db ->query($strSql);
		Bingo_Timer::end('dbqueryTotalNumber');				
		if($resStatus === false){
				Bingo_Log::warning('query failed. errno:['.self::$_db->errno().']['.self::$_db->error().']sqlStr:'.$strSql);
				return false;
		}	
				
		$arrOutput = array();	
		if(count($res) > 0 && count($resStatus) > 0 ){
			$arrOutput['forum_value'] = array(
				'forum_id' => $intForumId,
				'day_info' => array(
					'forum_value_total'	=>	$res[0]['forum_value_total'],
					'forum_rank' 			 	=>  $res[0]['forum_rank'],
					'forum_grade'			 	=>  $res[0]['forum_grade'],
					'forum_grade_title'	=> 	Dal_Forumvalue_Config::$arrForumGrade[$res[0]['forum_grade']],
					'forum_change_value' => $res[0]['forum_change_value'],
					'forum_top_value'   =>  $resStatus[0]['totalNumber']),							
		);
		}else{
			Bingo_Log::warning('fail to get result. sqlStr:'.$strSql);
			return false;		
		}		
	
	//get his day
	$intHisDayVale = self::_get_day($intDay, 30);
	$strSqlHis = 'select forum_value_day, day'
			.' from forum_value'.$intTableIndex.' where forum_id = '.$intForumId
			.' and day >= '. $intHisDayVale .' and day <= '.$intDayValue .' order by day;';
	
	Bingo_Timer::start('dbqueryHis');
	$arrRes = self::$_db ->query($strSqlHis);
	Bingo_Timer::end('dbqueryHis');
	if($arrRes === false){
			Bingo_Log::warning('query failed. errno:['.self::$_db->errno().']['.self::$_db->error().']sqlStr:'.$strSqlHis);
			return false;
	}
	
	$arrHisInfo = array();
	foreach($arrRes as $key => $value){
		$arrTempInfo = array();
		//$arrTempInfo['forum_member'] = $value['forum_member'];
		//$arrTempInfo['forum_new_member'] = $value['forum_new_member'];
		//$arrTempInfo['forum_thread_member'] = $value['forum_thread_member'];
		$arrTempInfo['forum_value_day'] = $value['forum_value_day'];
		//$arrTempInfo['forum_sign_member'] = $value['forum_sign_member'];
		//$arrTempInfo['forum_wap_member'] = $value['forum_wap_member'];
		//$arrTempInfo['forum_thread_large'] = $value['forum_thread_large'];
		$arrTempInfo['day'] = $value['day'];
		$arrHisInfo[] = $arrTempInfo;		
  }
	//var_dump($arrHisInfo);
	$arrOutput['History_info'] = $arrHisInfo;
	//add cache
	if(!self::_addCache($strCacheKey,$arrOutput)){
		Bingo_Log::warning('add cache fail.intForumId:'.$intForumId);
	}

	//var_dump($arrOutput);	
	return $arrOutput;
}

public static function update_forum_value($arrInput){
		if(!self::_validate_params($arrInput)){
				return self::UPDATE_NORMAL_ERR;
		}
		
		if(!self::init()){
			return false;
		}
		
		$intForumId = $arrInput['forum_id'];
		$intDay = self::_get_day($arrInput['day']);
		$intTableIndex = intval($intForumId % Dal_Forumvalue_Config::$intTableDiv);	

		$strInsertsql = sprintf('insert into forum_value%d (forum_id, forum_member, forum_new_member, forum_thread_member, forum_sign_member, forum_wap_member, forum_thread_large,
		 		forum_value_day, forum_value_total, forum_grade, forum_change_value, forum_rank, day) values(%d, %d, %d, %d, %d, %d, %d, %f, %f, %d, %f, %d, %d)', $intTableIndex,$arrInput['forum_id'],
		 		$arrInput['forum_member'],$arrInput['forum_new_member'],$arrInput['forum_thread_member'],$arrInput['forum_sign_member'],$arrInput['forum_wap_member'],$arrInput['forum_thread_large'],
		 		$arrInput['forum_value_day'],$arrInput['forum_value_total'],$arrInput['forum_grade'],$arrInput['forum_change_value'], $arrInput['forum_rank'],$intDay);					
		
		$strUpdatesql =sprintf(' on duplicate key update forum_member = %d, forum_new_member = %d, forum_thread_member = %d, forum_sign_member = %d, forum_wap_member = %d, 
				forum_thread_large = %d, forum_value_day = %f, forum_value_total = %f, forum_grade = %d, forum_change_value = %f, forum_rank = %d;',
				$arrInput['forum_member'],$arrInput['forum_new_member'], $arrInput['forum_thread_member'], $arrInput['forum_sign_member'],$arrInput['forum_wap_member'],$arrInput['forum_thread_large'],
		 		$arrInput['forum_value_day'],$arrInput['forum_value_total'],$arrInput['forum_grade'],$arrInput['forum_change_value'], $arrInput['forum_rank']);
		
		$strsql = $strInsertsql . $strUpdatesql;
		
		//var_dump($strsql);
		Bingo_Timer::start('dbquery');
		$res = self::$_db ->query($strsql);
		Bingo_Timer::end('dbquery');
		
		if($res === false){
			Bingo_Log::warning('insert failed. errno:['.self::$_db->errno().']['.self::$_db->error().']sqlStr:'.$strsql);
			return self::UPDATE_MYSQL_ERR;
		}
/*
		$strCacheKey = 'forum_value'.$intForumId;
		self::_removeCache($strCacheKey);	
*/
		return self::UPDATE_SUCCESS;
		
}

//
public static function update_forum_rank($arrInput){
		if(!isset($arrInput['forum_id']) || !isset($arrInput['rank']) || !isset($arrInput['day'])){
				Bingo_Log::warning('parm forum_id, rank or day is not set.');
				return false;			
		}

		if(!self::init()){
			return false;
		}		
		
		$intForumId = $arrInput['forum_id'];
		$intDay = self::_get_day($arrInput['day']);
		$intTableIndex = intval($intForumId % Dal_Forumvalue_Config::$intTableDiv);
		
		$strsql = 'update forum_value'.$intTableIndex.' set forum_rank = '.$arrInput['rank'].' where forum_id = '.
							$intForumId.' and day = '.$intDay.';';	
		
		Bingo_Timer::start('dbquery');
		$res = self::$_db -> query($strsql);
		Bingo_Timer::end('dbquery');
		
		if($res === false){
			Bingo_Log::warning('insert failed. errno:['.self::$_db->errno().']['.self::$_db->error().'] sqlStr:'.$strsql);
			return self::UPDATE_MYSQL_ERR;
		}
		
		$strCacheKey = 'forum_value'.$intForumId;
		self::_removeCache($strCacheKey);				
		
		return self::UPDATE_SUCCESS;		
}

//������ʷ�������ݣ��´ν����ʱ����Ч
public static function update_forum_cheat($arrInput){
		if(!isset($arrInput['forum_id']) || !isset($arrInput['cheat']) || !isset($arrInput['day'])){
				Bingo_Log::warning('parm forum_id, rank or day is not set.');
				return false;			
		}
		if(!self::init()){
			return false;
		}
				
		$intForumId = $arrInput['forum_id'];
		$intDay = self::_get_day($arrInput['day'], 0);
		$intTableIndex = intval($intForumId % Dal_Forumvalue_Config::$intTableDiv);
		
		//confirm the record
		$strsql = 'select forum_value_day, forum_cheat_value, forum_change_value from forum_value'.$intTableIndex.' where forum_id = '.$intForumId.' and day = '.$intDay.';';
		
		Bingo_Timer::start('dbquery');
		$res = self::$_db -> query($strsql);
		Bingo_Timer::end('dbquery');
		
		if($res === false){
			Bingo_Log::warning('insert failed. errno:['.self::$_db->errno().']['.self::$_db->error().'] sqlStr:'.$strsql);
			return self::UPDATE_MYSQL_ERR;
		}
			
		//
		if(!empty($res)){
				$intCheatValue = $res[0]['forum_cheat_value'] + $arrInput['cheat'];
				$intForumValue = $res[0]['forum_value_day'] - $arrInput['cheat'];
				if($intForumValue<0)
					$intForumValue = 0;
				$intForumChangeValue = $res[0]['forum_change_value'] - $arrInput['cheat'];
				$strsql = 'update forum_value'.$intTableIndex.' set forum_cheat_value = '.$intCheatValue.
									', forum_value_day = '.$intForumValue.', forum_change_value = '.$intForumChangeValue.' where forum_id = '.
							$intForumId.' and day = '.$intDay.';';				
		}else{
			Bingo_Log::warning($intDay.' data is not ready!');
			return false;	
		}
		
		//var_dump($strsql);		
		Bingo_Timer::start('dbquery');
		$res = self::$_db -> query($strsql);
		Bingo_Timer::end('dbquery');
		
		if($res === false){
			Bingo_Log::warning('insert failed. errno:['.self::$_db->errno().']['.self::$_db->error().'] sqlStr:'.$strsql);
			return self::UPDATE_MYSQL_ERR;
		}			
		
		return self::UPDATE_SUCCESS;		
}

public static function get_history_value($arrInput){		
		if(!isset($arrInput['forum_id']) || !isset($arrInput['day'])){
				Bingo_Log::warning('parm forum_id or day is not set.');
				return false;			
		}
		if(!self::init()){
			return false;
		}

		$intForumId = $arrInput['forum_id'];
		$intDay = $arrInput['day'];
		
		$intCurrentDay = self::_get_day($intDay);//intval(date("Ymd",$intDay));
		$intHisDay = self::_get_day($intDay, 30);//intval(date("Ymd",$intDay-30*24*3600));
		
		$intTableIndex = intval($intForumId % Dal_Forumvalue_Config::$intTableDiv);
		$strsql = "select sum(forum_value_day) as totalvalue from forum_value". $intTableIndex . " where forum_id = ".$intForumId.
							" and day >= ".$intHisDay." and day < " .$intCurrentDay." ;";
		
		Bingo_Timer::start('dbquery');
		$res = self::$_db -> query($strsql);
		Bingo_Timer::end('dbquery');
		
		if($res === false){
			Bingo_Log::warning('insert failed. errno:['.self::$_db->errno().']['.self::$_db->error().'] sqlStr:'.$strsql);
			return false;
		}
								
		return $res;					
}

public static function get_cheat_value($arrInput){
		if(!isset($arrInput['forum_id']) || !isset($arrInput['day'])){
				Bingo_Log::warning('parm forum_id or day is not set.');
				return false;			
		}
		if(!self::init()){
			return false;
		}
		
		$intForumId = $arrInput['forum_id'];			
		$intDay = $arrInput['day'];
		$intCurrenDay = self::_get_day($intDay);
		$intTableIndex = intval($intForumId % Dal_Forumvalue_Config::$intTableDiv);		
		
		$strsql = "select forum_cheat_value from forum_value". $intTableIndex . " where forum_id = ".$intForumId.
							" and day = ".$intCurrenDay." ;";
		
		Bingo_Timer::start('dbquery');
		$res = self::$_db -> query($strsql);
		Bingo_Timer::end('dbquery');
		
		if($res === false){
			Bingo_Log::warning('insert failed. errno:['.self::$_db->errno().']['.self::$_db->error().'] sqlStr:'.$strsql);
			return false;
		}
								
		return $res;		
}

public static function get_yesterday_value($arrInput){
		if(!isset($arrInput['forum_id']) || !isset($arrInput['day'])){
				Bingo_Log::warning('parm forum_id or day is not set.');
				return false;			
		}
		if(!self::init()){
			return false;
		}
		
		$intForumId = $arrInput['forum_id'];			
		$intDay = $arrInput['day'];
		$intCurrenDay = self::_get_day($intDay, 2);
		$intTableIndex = intval($intForumId % Dal_Forumvalue_Config::$intTableDiv);		
		
		$strsql = "select forum_value_total from forum_value". $intTableIndex . " where forum_id = ".$intForumId.
							" and day = ".$intCurrenDay." ;";
		
		Bingo_Timer::start('dbquery');
		$res = self::$_db -> query($strsql);
		Bingo_Timer::end('dbquery');
		
		if($res === false){
			Bingo_Log::warning('insert failed. errno:['.self::$_db->errno().']['.self::$_db->error().'] sqlStr:'.$strsql);
			return false;
		}
								
		return $res;		
}

public static function insert_forum_status($arrInput){
		if(!isset($arrInput['day']) || !isset($arrInput['begin_time']) || !isset($arrInput['status']) ){
				Bingo_Log::warning('parm day , begin_time, status is not set.');
				return false;			
		}
		if(!self::init()){
			return false;
		}
		
		$intCurrentDay = self::_get_day($arrInput['day']);
		$strSql = sprintf('insert into forum_compute_info (begin_time, intday, status) values (%d, %d, %d) on duplicate key update begin_time = %d, intday = %d, status = %d;',$arrInput['begin_time'], $intCurrentDay, $arrInput['status'],
										$arrInput['begin_time'], $intCurrentDay, $arrInput['status']);
		
		Bingo_Timer::start('dbquery');
		$res = self::$_db -> query($strSql);
		Bingo_Timer::end('dbquery');
		
		if($res === false){
			Bingo_Log::warning('insert failed. errno:['.self::$_db->errno().']['.self::$_db->error().'] sqlStr:'.$strSql);
			return self::UPDATE_MYSQL_ERR;
		}			
		
		return self::UPDATE_SUCCESS;															
}

public static function updata_forum_status($arrInput){
		if(!isset($arrInput['day']) || !isset($arrInput['end_time']) || !isset($arrInput['status']) || !isset($arrInput['totalnumber'])){
				Bingo_Log::warning('parm day , begin_time, status , totalnumber is not set.');
				return false;			
		}
		if(!self::init()){
			return false;
		}
		
		$intCurrentDay = self::_get_day($arrInput['day']);
		$strSql = sprintf('update forum_compute_info set end_time = %d, status = %d, totalnumber = %f where intday = %d;',
										$arrInput['end_time'], $arrInput['status'], $arrInput['totalnumber'], $intCurrentDay);
		
		Bingo_Timer::start('dbquery');
		$res = self::$_db -> query($strSql);
		Bingo_Timer::end('dbquery');
		
		if($res === false){
			Bingo_Log::warning('insert failed. errno:['.self::$_db->errno().']['.self::$_db->error().'] sqlStr:'.$strSql);
			return self::UPDATE_MYSQL_ERR;
		}			
		
		return self::UPDATE_SUCCESS;															
}

//for mis
public static function get_Forum_Info_ByGrade($arrInput){
		if(!isset($arrInput['day']) || !isset($arrInput['grade'])){
				Bingo_Log::warning('parm day , grade is not set.');
				return false;			
		}
		if(!self::init()){
			return false;
		}
		
		$intCurrentDay = self::_get_day($arrInput['day']);
		
		//�жϵ����Ƿ�������
		$strSql = 'select status from forum_compute_info where intday = '.$intCurrentDay.";"; 
		Bingo_Timer::start('dbquery');
		$resStatus = self::$_db ->query($strSql);
		Bingo_Timer::end('dbquery');				
		if($resStatus === false){
			Bingo_Log::warning('query failed. errno:['.self::$_db->errno().']['.self::$_db->error().']sqlStr:'.$strSql);
			return false;
		}		
		if(isset($resStatus[0]['status']) && $resStatus[0]['status']==1){
			$strSql = 'select count(1) as forum_number,sum(forum_value_total) as total_forum_value ,sum(forum_change_value) as total_forum_change'.
								', sum(forum_member) as total_forum_member, sum(forum_new_member) as total_forum_new_member'.
								',sum(forum_thread_member) as total_forum_thread_member, sum(forum_sign_member) as total_forum_sign_member'.
								', sum(forum_wap_member) as total_forum_wap_member, sum(forum_thread_large) as total_forum_thread_large '.
								'from forum_value where day = '.$intCurrentDay.' and forum_grade = '.$arrInput['grade'].';'; 
			Bingo_Timer::start('dbquery');
			$ret = self::$_db ->query($strSql);
			Bingo_Timer::end('dbquery');
			
			if($ret === false){
				Bingo_Log::warning('query failed. errno:['.self::$_db->errno().']['.self::$_db->error().']sqlStr:'.$strSql);
				return false;
			}							
		}else{
			Bingo_Log::warning('query result is empty. sqlStr:'.$strSql);
			return false;				
		}
			
		return $ret;		
}


public static function removeCache($strCacheKey){
		self::_removeCache($strCacheKey);	
} 

private static function _get_day($intDay, $intStep = 1){
		return intval(date("Ymd",$intDay - $intStep*24*3600));
}

private static function _validate_params($arrInput){	
	if(!isset($arrInput['forum_id']) || !is_numeric($arrInput['forum_id'])){
		Bingo_Log::warning('parm forum_id not invalid.');
		return false;
	}
	if(!isset($arrInput['forum_member']) || !is_numeric($arrInput['forum_member'])){
		Bingo_Log::warning('parm forum_member not invalid.');
		return false;
	}
	if(!isset($arrInput['forum_thread_member']) || !is_numeric($arrInput['forum_thread_member'])){
		Bingo_Log::warning('parm forum_thread_member not invalid.');
		return false;
	}
	if(!isset($arrInput['forum_sign_member']) || !is_numeric($arrInput['forum_sign_member'])){
		Bingo_Log::warning('parm forum_sign_member not invalid.');
		return false;
	}	
	if(!isset($arrInput['forum_wap_member']) || !is_numeric($arrInput['forum_wap_member'])){
		Bingo_Log::warning('parm forum_wap_member not invalid.');
		return false;
	}
	if(!isset($arrInput['forum_thread_large']) || !is_numeric($arrInput['forum_thread_large'])){
		Bingo_Log::warning('parm forum_thread_large not invalid.');
		return false;
	}	
	if(!isset($arrInput['forum_value_day']) || !is_numeric($arrInput['forum_value_day'])){
		Bingo_Log::warning('parm forum_value_day not invalid.');
		return false;
	}
	if(!isset($arrInput['forum_value_total']) || !is_numeric($arrInput['forum_value_total'])){
		Bingo_Log::warning('parm forum_value_total not invalid.');
		return false;
	}
	if(!isset($arrInput['forum_grade']) || !is_numeric($arrInput['forum_grade'])){
		Bingo_Log::warning('parm forum_grade not invalid.');
		return false;
	}
	if(!isset($arrInput['forum_change_value']) || !is_numeric($arrInput['forum_change_value'])){
		Bingo_Log::warning('parm forum_change_value not invalid.');
		return false;
	}
	if(!isset($arrInput['day']) || !is_numeric($arrInput['day'])){
		Bingo_Log::warning('parm day not invalid.');
		return false;
	}								
	return true;
}

/*
public static function http_query($intUid){
    $objHttp = new Bd_Rpc_Http('test', 
        array(
            array(
            'host' => '127.0.0.1',
            'port' => '8666',
            ),  
        )   
    
    );  
    $arrOpt = array(
        'connect_timeout' => 1000,
        'read_timeout' => 1000,
    );  
    $objHttp -> setOptions($arrOpt);

    $arrInfo = array(
    	'trade_id' => 124,
    	'user_id' => 1,
    	'user_name' => 'bob',
    	'status' => 0,
    	'goods_id' => 11112,
    	'goods_num' => 22,
    	'trade_money' => 33,
    	'trade_time' => 33334,

    );	
    
    $arrInput = array(
        'method' => 'post',
        'url' => '/data/money',
        'post_vars' => array(
    		'method' => 'do_consume',
    		'format' =>'json',
    		'info' => json_encode($arrInfo),	
    	
    	),
    
    );  

    $strOut = $objHttp -> call($arrInput);
    var_dump($strOut);
    $arrOut = json_decode($strOut, true);
    //var_dump($arrOut);
    return $arrOut;
}


private static function  do_consume_http($info){
	
	$strUrl = '/data/money';
	$camelServiceName = 'money_http';
	if(is_array($info)){
		$strInfo = json_encode($info);
	}
	$arrData = array(
		'format' => 'json',
		'method' => 'do_consume',
		'info' => $strInfo,
		'ie' => 'utf-8',
	
	);
	var_dump($arrData);
	
	$strOut = Tbapi_Core_Midl_Http::httpcall($camelServiceName,$strUrl,$arrData);
	
	var_dump($strOut);

	return $strOut;
}

*/
public static function test($arrInput){
	$arrInfo = array(
    	'forum_id' => $arrInput['forum_id'],
    	'forum_member' => 1,
    	'forum_new_member' => 1111,
    	'status' => 0,
    	'forum_thread_member' => 11112,
    	'forum_sign_member' => -22,
    	'forum_wap_member' => 1,
    	'forum_thread_large' => 33334,
    	'forum_image_member' => 100,
    	'forum_value_day' => 50,
    	'forum_value_total' => 2800,
    	'forum_rank' => 3,
    	'forum_grade' => 8,
    	'forum_change_value' => 200,    	
			'day' => time(),
    );
/*    
    $strFile = dirname(__FILE__).'/../../Money.midl.php';
    require_once $strFile;
		$res  = Tbapi_Mall_Midl_Money2::doConsume($arrInfo);
*/
		$day = $arrInfo['day'];
		$i = 0;
		while($i <= 30){
			$arrInfo['day'] = $day - $i*24*3600;
			$arrInfo['forum_value_day'] = rand(1,100);
			var_dump($arrInfo['day']);
			$res = self::update_forum_value($arrInfo);
			$i++;
			var_dump($res);
		}
		
//		$res = self::update_forum_value($arrInfo);
//		var_dump($res);		
		return $res;
}

public static function testCache($arrInput){
		if(!isset($arrInput['forum_id'])){
			return false;	
		}
		$intForumId = $arrInput['forum_id'];
		$strCacheKey = 'forum_value'.$intForumId;
		self::_removeCache($strCacheKey);		
}

public static function test2($arrInput){
	echo 'in test2 now.';
	var_dump($arrInput);	
	echo 'end test2.';
	return array('a'=>'b');
}

/**
 * @brief
 * @arrInput:
 * 	uint32_t forum_id
 * @return: $arrOutput
 * 	forum_info forum
**/
public static function get_patch_forum_value($arrInput){
		if(!isset($arrInput['forum_id']) || !isset($arrInput['day'])){
			Bingo_Log::warning('parameter forum_id or day is not set.');
			return false;
		}
		if(!self::init()){
			return false;
		}
		
		$strForumId = strval($arrInput['forum_id']);
		$arrForumId = explode("_", $strForumId);
		if(is_array($arrForumId) && count($arrForumId) <= self::PATCH_NUM){
			$intDay = intval($arrInput['day']);
			//��ȡ����ļ����Ƿ����		
			while($retry<self::RETRY_NUMBER){
				$intDayValue = self::_get_day($intDay);
				
				//��ȡ������ɵ�ʱ��
				$strSql = 'select status from forum_compute_info where intday = '.$intDayValue.';';	
				Bingo_Timer::start('dbquery');
				$res = self::$_db ->query($strSql);
				Bingo_Timer::end('dbquery');				
				if($res === false){
						Bingo_Log::warning('query failed. errno:['.self::$_db->errno().']['.self::$_db->error().']sqlStr:'.$strSql);
				}
				if(!empty($res) && $res[0]['status'] == 1)
					break;	
				$intDay = $intDay - 24*3600;
				$retry ++;					
			}
			if(empty($res) || $res[0]['status'] != 1){
				$arrOutput = array(
					'no'		=> 1,
					'data'	=> "",
				);
				return $arrOutput;	
			}
			$arrTempInput = array(
					'day' => $intDay);					
			$arrOutput = array(
				"no"	=> 0,
			);
			$arrTempOutput = array();		
			foreach($arrForumId as $key => $value){
				$arrTempInput['forum_id'] = $value;
				$ret = self::get_single_forum_value($arrTempInput);
				if($ret === false){
					Bingo_Log::warning("Fail to get forumvalue. input : ".serialize($arrTempInput));
					$arrTempOutput[$value] = 0;	
				}else{
					$arrTempOutput[$value] = $ret;	
				}				
			}
			$arrOutput['data'] = $arrTempOutput;
		}
		return $arrOutput;	
	}
	
public static function get_single_forum_value($arrInput){
		if(!isset($arrInput['forum_id']) || !isset($arrInput['day'])){
			Bingo_Log::warning('parameter forum_id or day is not set.');
			return false;
		}
	
	//get current day	
	$intForumId = intval($arrInput['forum_id']);
	$intDay = intval($arrInput['day']);
	$intTableIndex = intval($intForumId % Dal_Forumvalue_Config::$intTableDiv);
	
	$retry = 0;	
	$intDayValue = self::_get_day($intDay);
		
	//��ȡ����ֵ	
	$strSql = 'select forum_value_day from forum_value'.$intTableIndex.' where forum_id = '.$intForumId .' and day = '.$intDayValue.';';	
	Bingo_Timer::start('dbquery');
	$res = self::$_db ->query($strSql);
	Bingo_Timer::end('dbquery');				
	if($res === false || empty($res[0]['forum_value_day'])){
		Bingo_Log::warning('query failed. errno:['.self::$_db->errno().']['.self::$_db->error().']sqlStr:'.$strSql);
		return false;
	}
		
	return $res[0]['forum_value_day'];	
		
	}	
	
}
