<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file getAmazon.php
 * <AUTHOR>
 * @date 2015/11/19 12:36:54
 * @brief 
 *  
 **/


$start_time = (int)strtotime($argv[1]);
$end_time = (int)strtotime($argv[2]);
$forum_name = trim($argv[3]);

if (0 < strlen($forum_name)) {
    if ($forum_name === iconv('UTF-8', 'UTF-8//IGNORE', $forum_name)) {
    } else {
        $forum_name = iconv('GBK', 'UTF-8//IGNORE', $forum_name);
    }
    define("FORUM_NAME", $forum_name);
} else {
    define("FORUM_NAME", '贴吧触点推广');
}

if (0 < $start_time) {
} else {
    $start_time = strtotime(date('Y-m-d', time()))-86400;
}
define('START_DATE', date("Y-m-d", $start_time));

if (0 < $end_time) {
    $end_time = date('Y-m-d', $end_time);
} else {
}

define("END_DATE", $end_time);

define("MODULE_NAME", "get_tpoint_data");
require_once('base.php');

echo sprintf("start date : %s, end date : %s, forum_name : %s\n", START_DATE, END_DATE, FORUM_NAME);

class Process {
    private static $_arrWordFileHead = array(
        "模板ID",
        '模板标题',
        '关键词',
        '图片点击pv',
        '图片点击uv',
        '展示pv',
        '展示uv',
        '按钮点击pv',
        '按钮点击uv',
        '点击pv',
        '点击uv',
        '请求pv',
        '请求uv',
        '回复量',
        '客户端类型',
    );

    private static $_arrTplHead = array(
        '模板ID',
        '关键词',
        '触发量',
        '删除量',
        '实际量',
    );

    private static $_maskField = array(
        'template_content',
        'template_images',
        'template_land_mark',
        'status',
        'monitor_city',
        'monitor_id',
        'monitor_forums',
        'op_user',
        'op_reason',
        'create_time',
        'update_time',
        'monitor_forums',
        'version_show',
        'owner_forum_id',
        'is_client',
    );

    const SECOND_ONE_DAY = 86400;
    const DEFAULT_PAGE_SIZE = 44;
    const TXT_SEP = "\t";

    /**
     * @brief 
     *
     * @return 
     */
    public static function getDate() {
        $today = strtotime(date('Y-m-d', time()));
        $yesterday = $today - self::SECOND_ONE_DAY;
        $start_time = $yesterday;
        $end_time = $today+self::SECOND_ONE_DAY-1;
        if (0 < START_DATE) {
            $start_time = strtotime(START_DATE);
        }
        if (0 < END_DATE) {
            $end_time = strtotime(END_DATE);
        }
        $arrRet = array(
            'start_time' => $start_time,
            'end_time' => $end_time,
        );
        return $arrRet;
    }

    /**
     * @brief 
     *
     * @param $forum_name
     *
     * @return 
     */
    public static function getFidByFname($forum_name) {
        $arrInput = array(
            'query_words' => array(
                $forum_name,
            ),
        );
        $arrOutput = MyCall::call('forum','getFidByFname', $arrInput, 'utf-8', 20);
        if (false === $arrOutput) {
        }
        return (int)$arrOutput['forum_id'][0]['forum_id'];
    }

    /**
     * @brief 
     *
     * @param $owner_forum_id
     * @param $owner_type
     * @param $start_time
     * @param $end_time
     *
     * @return 
     */
    public static function getWordData($owner_forum_id, $owner_type, $start_time, $end_time) {
        echo "get word data ...\n";
        $arrInput = array(
            'req' => array(
                'owner_forum_id' => $owner_forum_id,
                'owner_type' => $owner_type,
                'start_time' => $start_time,
                'end_time' => $end_time,
                'res_num' => self::DEFAULT_PAGE_SIZE,
            ),
        );
        $intCount = 0;
        $intOffset = 0;
        $arrData = array();
        do {
            $arrInput['req']['offset'] = $intOffset;
            $arrOutput = MyCall::call('opmonitor','getAutoreplyPostData', $arrInput, 'utf-8', 20);
            foreach($arrOutput['res'] as $v) {
                foreach(self::$_maskField as $field) {
                    unset($v[$field]);
                }
                $arrData [] = $v;
            }
            $intCount = count($arrOutput['res']);
            $intOffset += self::DEFAULT_PAGE_SIZE;
        } while($intCount >= self::DEFAULT_PAGE_SIZE);
        return $arrData;
    }

    /**
     * @brief 
     *
     * @param $start_time
     * @param $end_time
     * @param $template_id
     *
     * @return 
     */
    public static function getTplData($start_time, $end_time, $template_id) {
        echo "get tpl data ...\n";
        $arrTmp = array();
        $i=1;
        $split_size = 10;
        $arrTotalInput = array();
        $arrSplitTplId = array();
        foreach($template_id as $tplid) {
            $arrSplitTplId[]= $tplid;
            if ( $i%$split_size ===0) {
                $arrTotalInput[] = $arrSplitTplId;
                $arrSplitTplId = array();
                $i = 1;
            } else {
                $i++;
            }
        }
        if (!empty($arrSplitTplId)) {
            $arrTotalInput[] = $arrSplitTplId;
        }

        $arrData = array();
        foreach($arrTotalInput as $single_template_id) {
            $arrInput = array(
                'req' => array(
                    'template_ids' => $single_template_id,
                    'start_time' => $start_time,
                    'end_time' => $end_time,
                ),
            );
            $arrOutput = MyCall::call('opmonitor','downloadAutoreplyPostData', $arrInput, 'utf-8', 20);
            foreach($arrOutput['res'] as $v) {
                $tplid = $v['template_id'];
                $tpoint_post = (int)$v['tpoint_post'];
                $tpoint_delete = $v['tpoint_delete'];
                $arrData[$tplid]['word'] = $v['word'][0];
                if (isset($v[$tplid])) {
                    $arrData[$tplid]['tpoint_post'] += $tpoint_post;
                    $arrData[$tplid]['tpoint_delete'] += $tpoint_delete;
                } else {
                    $arrData[$tplid]['tpoint_post'] = $tpoint_post;
                    $arrData[$tplid]['tpoint_delete'] = $tpoint_delete;
                }
            }
        }
        return $arrData;
    }

    /**
     * @brief 
     *
     * @return 
     */
    public static function main() {
        try {
            $owner_forum_id = self::getFidByFname(FORUM_NAME);
            $owner_type = 0;
            $arrTmp = self::getDate();
            $start_time = $arrTmp['start_time'];
            $end_time = $arrTmp['end_time'];

            $filename_suffix = "_".date("Y-m-d", $start_time)."_".date("Y-m-d", $end_time)."_".FORUM_NAME.".xls";

            $arrWordData = self::getWordData($owner_forum_id, $owner_type, $start_time, $end_time);
            $word_file = new MyFile('word'.$filename_suffix, MyFile::TYPE_WRITE);
            $strWriteMsg = "";
            foreach($arrWordData as $data) {
                $strTmpMsg = "<tr>";
                foreach($data as $k => $v) {
                    $strTmpMsg .= '<td style="border:1px solid #ddd">'.$v.'</td>';
                }
                $strTmpMsg .= '</tr>';
                $strWriteMsg.=$strTmpMsg;
            }
            if(0 < strlen($strWriteMsg)) {
                $strHeader = implode('</td><td style="border:1px solid #ddd">', self::$_arrWordFileHead);
                $strHeader = '<tr><td style="border:1px solid #ddd">'.$strHeader."</td></tr>";
                $strTitle = '<tr><td style="border:1px solid #ddd">'."词维度统计, 日期:".date('Y.m.d', $start_time)."-".date('Y.m.d', $end_time).", 吧名:".FORUM_NAME."</td></tr>";
                //$strTitle = '<tr>'."词维度统计, 日期:".date('Y.m.d', $start_time)."-".date('Y.m.d', $end_time).", 吧名".FORUM_NAME."</tr>";
                $word_file->write("<table>".$strTitle.$strHeader.$strWriteMsg."</table>");
            }
            $template_id = array();
            foreach($arrWordData as $v) {
                $tplid = (int)$v['template_id'];
                if(!in_array($tplid, $template_id)) {
                    $template_id[] = $tplid;
                }
            }
            $strWriteMsg = "";
            $tpl_file = new MyFile('tpl'.$filename_suffix, MyFile::TYPE_WRITE);
            $arrTplData = self::getTplData($start_time, $end_time, $template_id);
            foreach($arrTplData as $tplid => $data) {
                $strTmpMsg = "<tr>";
                $strTmpMsg .= '<td style="border:1px solid #ddd">'.$tplid.'</td>';
                foreach($data as $k => $v) {
                    $strTmpMsg .= '<td style="border:1px solid #ddd">'.$v.'</td>';
                }
                $strTmpMsg = $strTmpMsg.'<td style="border:1px solid #ddd">'.($data['tpoint_post']-$data['tpoint_delete']).'</td>';
                $strTmpMsg .= '</tr>';
                $strWriteMsg.=$strTmpMsg;
            }

            if(0 < strlen($strWriteMsg)) {
                $strHeader = implode('</td><td style="border:1px solid #ddd">', self::$_arrTplHead);
                $strHeader = '<tr><td style="border:1px solid #ddd">'.$strHeader."</td></tr>";
                $strTitle = '<tr><td style="border:1px solid #ddd">'."模板维度统计, 日期:".date('Y.m.d', $start_time)."-".date('Y.m.d', $end_time).", 吧名:".FORUM_NAME."</td></tr>";
                $tpl_file->write("<table>".$strTitle.$strHeader.$strWriteMsg."</table>");
            }
        } catch(Exception $e) {
        }
    }
}

Process::main();

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
