<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>  <EMAIL>
 * @date 2012-6-20
 * @version 
 */
class getsmscodeAction extends CapiAction {
    protected $bolNeedUserLogin = false; //是否需要进行用户登录
    protected $bolCheckUserLogin = false; //是否要检查用户是否登录

    protected function _execute() {
    	$strPhonenum = Bingo_Http_Request::get('phonenum','');
    	if (empty($strPhonenum)){
    		  $this->_error(CapiErrno::PARAM_NOT_ENOUGH,CapiError::PARAM_NOT_ENOUGH);
            return false;
    	}
        $strVcode = Bingo_Http_Request::get('vcode','');
        $strVcodeMd5 = Bingo_Http_Request::get('vcode_md5','');

        $ip = CapiRequest::$strIp;
        $bolIsIpv6 = (Bingo_Http_Ip::IP_V6 == Bingo_Http_Ip::getIpVersion($ip));
    	$arrInput = array(
            'phonenum' => $strPhonenum, //不加密
            'clientip' => $bolIsIpv6 ? '' : $ip,
            'clientip6' => $bolIsIpv6 ? $ip : '',
            'clientid' => CapiRequest::$strClientId,        
        );    
        if (!empty($strVcode)){
            $arrInput['verifycode'] = $strVcode;
        }
        if (!empty($strVcodeMd5)){
            $arrInput['vcodestr'] =$strVcodeMd5;
        } 
        $arrOut = Passport_Sapi_Reg::getSmsCode($arrInput);
        if ($arrOut['errno'] != 110000) {
        	;if ($arrOut['needvcode']){
                    $this->arrData['anti'] = array(
                        'need_vcode' => true,
                        'vcode_md5' => $arrOut['vcodestr'],
                        'vcode_pic_url' => Conf::get('passport_vcode_host','').$arrOut['vcodestr'],
                    );                
                    $this->_error(CapiErrno::NEED_VCODE,CapiError::NEED_VCODE);
                }else{
                    $this->_error(CapiErrno::INTERNAL_ERROR,CapiError::INTERNAL_ERROR);
                }                                              
        }else {
        	return $arrOut;
        }
    }
   
}
