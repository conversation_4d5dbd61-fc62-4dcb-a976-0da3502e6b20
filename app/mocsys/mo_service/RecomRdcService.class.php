<?php

/**
 * @file: RecomRdcService.class.php
 * @author: song<PERSON><PERSON><PERSON>@baidu.com
 * @datetime: 2019-07-24 19:18
 * @brief: 贴吧首页行为日志迁移RDC中台化
 * @wiki: http://wiki.baidu.com/pages/viewpage.action?pageId=710251026
 */
class RecomRdcService
{
    /**
     * 视频点击：'c10708', 'c10734', 'c10735', 'c10756', 'c11929'
     * 视频曝光：c10709
     * 视频播放时长：c11244
     * 图文点击：c10704', 'c10730', 'c10731',  'c10755'
     * 图文曝光：c10705
     * 负反馈：c11693
     * 落地页时长：stime
     * 互动日志 点赞：batch/c12003  点踩：batch/c13271  回复：batch/c12942  分享：batch/c12386
     * news（图文），sv（短视频），mv（小视频）
     * @var array
     */
    private static $arrUrlKeyType = array(
        'batch/c10708' => 'sv',
        'batch/c10734' => 'sv',
        'batch/c10735' => 'sv',
        'batch/c10756' => 'sv',
        'batch/c11929' => 'sv',
        'batch/c10709' => 'sv',
        'batch/c11244' => 'sv',
        'batch/c10704' => 'news',
        'batch/c10730' => 'news',
        'batch/c10731' => 'news',
        'batch/c10755' => 'news',
        'batch/c10705' => 'news',
        'batch/c11693' => '',//负反馈，单独判断
        'batch/stime'  => 'news', //落地页时长
        'batch/c12003' => '', //点赞
        'batch/c13271' => 'sv', //点踩 无法区分贴子类型,全部使用sv
        'batch/c12942' => '', //回复(列表页点击回复按钮)
        'batch/c12386' => '', //分享
        'batch/c13570' => 'sv', //关注作者,无法区分贴子类型,全部使用sv
        'batch/c12507' => 'sv', //关注作者 pb页. 无法区分贴子类型,全部使用sv
        'batch/c12590' => 'sv', //视频中间页展现日志
        'batch/c13399' => 'news', //无法区分资源类型 默认图文
        'batch/c13401' => 'news', //pb顶部点击进吧，PM:钟睿
        'batch/c13563' => 'news',
	    'batch/c13562'  => 'news',
        'batch/c13693' => 'news', //首页进吧，PM:钟睿
        'batch/c14085' => 'news',
        'batch/c11823' => 'sv',//首页feed推荐直播展现 pm:<EMAIL>
        'batch/c11824' => 'sv',//首页feed推荐直播点击 pm:<EMAIL>
        'batch/c14179' => 'sv', // 视频点赞
        'batch/c14185' => 'sv', // 视频关注
        'batch/c14182' => 'sv', // 视频收藏
    );


    /**
     * @var array 通过指定字段区分类型
     */
    protected static $arrUrlKeyTypeOption = array(
        'batch/c11693' => array(
            'option_key' => 'obj_param2',
            '1' =>  'sv',
            '2' =>  'news',
        ),
        'batch/c12386' => array(
            'option_key' => 'obj_type',
            '1' =>  'news',
            '2' =>  'sv',
        ),
        'batch/c12003' => array(
            'option_key' => 'obj_param1',
            '1' =>  'news',
            '2' =>  'sv',
        ),
        'batch/c12942' => array(
            'option_key' => 'obj_type',
            '1' =>  'sv',
            '2' =>  'news',
        ),
    );



    /**
     * 3: 点击行为
     * 9: dislike
     * 11: 下发日志
     * 12: 展现日志
     * 101: 视频播放时长
     * 346: 落地页停留时长
     * 其他可以自己定义，rdc做透传。
     * @var array
     */
    private static $arrUrlkeyCmdid = array(
        'batch/c10708' => '3',
        'batch/c10734' => '3',
        'batch/c10735' => '3',
        'batch/c10756' => '3',
        'batch/c11929' => '3',
        'batch/c10709' => '12',
        'batch/c11244' => '101',
        'batch/c10704' => '3',
        'batch/c10730' => '3',
        'batch/c10731' => '3',
        'batch/c10755' => '3',
        'batch/c10705' => '12',
        'batch/c11693' => '9',
        'batch/stime'  => '346',
        'batch/c12003' => '13', //点赞
        'batch/c13271' => '19', //点踩
        'batch/c12942' => '21', //回复(列表页点击回复按钮)
        'batch/c12386' => '16', //分享
        'batch/c13570' => '28', //关注作者
        'batch/c12507' => '28', //关注作者
        'batch/c12590' => '12', //视频中间页展现日志
        'batch/c13399' => '120', //pb点击进吧
        'batch/c13401' => '120', //pb顶部点击进吧，PM:钟睿
	    'batch/c13563' => '41',
	    'batch/c13562'  => '15',
        'batch/c13693' => '121', //首页进吧，PM:钟睿
        'batch/c14085' => '49',
        'batch/c11823' => '44',
        'batch/c11824' => '45',
        'batch/c14179' => '',
        'batch/c14185' => '',
        'batch/c14182' => '',
    );

    /**
     * @var array 通过指定字段区分cmdid pm@gongxin04
     */
    private static $arrUrlKeyCmdidOption = array(
        'batch/c14179' => array(
            'option_key' => 'obj_locate',
            '1' =>  '103',
            '2' =>  '104',
        ),
        'batch/c14185' => array(
            'option_key' => 'obj_locate',
            '1' =>  '105',
            '2' =>  '106',
        ),
        'batch/c14182' => array(
            'option_key' => 'obj_locate',
            '1' =>  '107',
            '2' =>  '108',
        ),
    );

    /**
     * 视频播放url key
     * @var string
     */
    private static $videoPlayUrlKey = 'batch/c11244';

    /**
     * 负反馈url key
     * @var string
     */
    private static $dislikeUrlKey = 'batch/c11693';

    /**
     * 页面停留时长日志
     * @var string
     */
    private static $pageDurationKey = 'batch/stime';
    private static $postDurationKey = 'batch/c14085';
    /**
     * @var array
     */
    private static $arrRequiredKey = array(
        'logid',
        'cuid',
        'uid',
        '_client_type',
        't',
        'tid'
    );

    /**
     * 基础中的可选字段
     * @var array
     */
    private static $arrOptionKey = array(
        'sv' => '_client_version',
        'os_ver' => 'os_version',
        'imei' => '_phone_imei',
        'osbranch' => 'subapp',
        'brand' => 'brand_type',
    );

    /**
     * server 透传的key
     * @var array
     */
    private static $arrExtKey = array(
        'fid',
        'tid',
        'uid',
        'urlkey',
        'ab_tag',
        'obj_param1',
        'obj_param2',
        'obj_param3',
        'obj_param4',
        'obj_param5',
        'obj_param6',
        'obj_source',
        'obj_locate',
        'obj_id',
        'obj_floor',
        'obj_type',
        'is_vertical',
        'obj_name',
        'hdid',
        'yysid',
        'yyssid',
        'yyuid',
        'yyliveid',
        'is_finish', // 视频是否完播，pm@gongxin04
    );

    /**
     * @var array
     */
    private static $arrUrkEncodeKey = array(
        'ab_tag' => 1,
        'obj_param1' => 1,
        'obj_id' => 1,
        'obj_name' => 1
    );

    /**
     * @var array
     */
    private static $arrOsMap = array(
        '1' => 'ios',
        '2' => 'android'
    );

    /**
     * @var array
     * 1: wifi、2: 2G、3: 3G、4: 4G
     */
    private static $arrNetTypeMap = array(
        '1' => 'wifi',
        '2' => '2G',
        '3' => '3G',
        '4' => '4G'
    );

    /**
     * @param $oneInput
     * @param $statLog
     * @return bool
     */
    public static function buildRdcInput(&$oneInput, $statLog)
    {
        if (!isset($statLog['urlkey'])) {
            return false;
        }
        $urlkey = $statLog['urlkey'];
        if (!isset(self::$arrUrlKeyType[$urlkey])) {
            return false;
        }
        foreach (self::$arrRequiredKey as $k) {
            if (!isset($statLog[$k])) {
                return false;
            }
        }
        $oneInput['logid'] = intval($statLog['logid']);
        $oneInput['command_no'] = 420;
        $arrData = array();
        $arrData['cuid'] = strval($statLog['cuid']);
        $arrData['device_id'] = strval($statLog['cuid']);
        $arrData['passport_uid'] = strval($statLog['uid']);
        //for sample
        if (empty($arrData['passport_uid']) && false !== strpos($statLog['obj_param1'], "virtual_uid")) {
            $objParam1 = json_decode(urldecode($statLog['obj_param1']), true);
            if ($objParam1 && isset($objParam1['virtual_uid'])) {
                $arrData['passport_uid'] = strval($objParam1['virtual_uid']);
            }
        }
        $arrData['tm'] = strlen($statLog['t']) > 10 ? strval($statLog['t']) : strval($statLog['t'] * 1000);;
        $cmdid = self::getCmdidByOptionKey($urlkey, $statLog);
        if (empty($cmdid)) {
            return false;
        }
        $arrData['cmdid'] = $cmdid;
        $arrData['log_src'] = 'tieba_app';
        $arrData['pd'] = 'tieba_app';
        $arrData['brand'] = '';
        $arrData['net_type'] = self::$arrNetTypeMap[$statLog['net_type']];
        $arrData['idfa'] = '';
        foreach (self::$arrOptionKey as $k => $v) {
            if (isset($statLog[$v])) {
                $arrData[$k] = $statLog[$v];
            }
        }
        if (isset($statLog['sample_id']) && !empty($statLog['sample_id'])){
            $sample_id=$statLog['sample_id'];
            $sample=explode("-",$sample_id);
            $swfm=array();
            foreach ( $sample as $index => $onesample){
                $onetemp=explode("_",$onesample);
                if(count($onetemp) < 2){
                    Bingo_Log::warning("onetemp count=".count($onetemp));
                    continue;
                }
                $smfw[$onetemp[0]]=$onetemp[1];
            }
            $arrData['smfw']=$smfw;
        }
        $arrData['os'] = self::$arrOsMap[$statLog['_client_type']];
        if (isset($statLog['uip']) && !empty($statLog['uip'])) {
            $arrData['cip'] = Bingo_Http_Ip::long2ip($statLog['uip']);
        }
        if (isset($statLog['uip6']) && !empty($statLog['uip6'])) {
            $arrData['ipv6'] = Bingo_Http_Ip::long2ip($statLog['uip6']);
        }
        /**
         * 小流量信息暂时不适配
         */
        $arrDataSmfw = array();

        $arrDataData = array();
        $type = self::$arrUrlKeyType[$urlkey];
        /*
        if (self::$dislikeUrlKey == $urlkey) {
            $type = self::getDislikeThreadType($statLog);
        }
        */
        if(isset(self::$arrUrlKeyTypeOption[$urlkey])){
            $optionKey = self::$arrUrlKeyTypeOption[$urlkey]['option_key'];
            if (!empty($statLog[$optionKey])) {
                $optionVal = $statLog[$optionKey];
                if(isset(self::$arrUrlKeyTypeOption[$urlkey][$optionVal])){
                    $type = self::$arrUrlKeyTypeOption[$urlkey][$optionVal];
                }
            }
        }
        if (!empty($type)) {
            $nid = getSignByTid($statLog['tid']);

            //直播卡片点展要上报直播资源的nid 不是帖子的nid pm:<EMAIL>
            $changeNidArr = array('batch/c11823', 'batch/c11824');
            if(in_array($urlkey, $changeNidArr) && !empty($statLog['obj_param1'])){
                $liveObjParam1 = json_decode(urldecode($statLog['obj_param1']), true);
                if ($liveObjParam1 && isset($liveObjParam1['extra'])) {
                    $liveObjParam1ExtraArr = explode(';', $liveObjParam1['extra']);
                    foreach($liveObjParam1ExtraArr as $vv){
                        $vvTemp = explode(':', $vv);
                        if($vvTemp[0] == 'feed_id'){
                            $nid = $vvTemp[1];
                        }
                    }
                }
            }

            if (empty($nid)) {
                return false;
            }
            $arrDataData['id'][] = $type . "_" . $nid;
        } else {
            return false;
        }
        $arrDataDataExt = array();
        $urlKeyMap =array(
            'batch/c13563' => 1,
            'batch/c12003' => 1,
            'batch/c12386' => 1,
            'batch/c13271' => 1,
            'batch/c13562' => 1,);
        $logFromMap =array(
            'a002' => 1,
            'a008' => 1,
            'a023' => 1);
        if(isset($urlKeyMap[$urlkey])){
            if( isset($logFromMap[$statLog['obj_tab']]) || isset($logFromMap[$statLog['obj_page']]) ){
                $arrDataDataExt['log_from'] = '0';
            }else{
                $arrDataDataExt['log_from'] = '1';
            }
        }
        foreach (self::$arrExtKey as $k) {
            if (isset($statLog[$k])) {
                $arrDataDataExt[$k] = $statLog[$k];
            }
        }
        foreach ($arrDataDataExt as $k => $v) {
            if (isset(self::$arrUrkEncodeKey[$k])) {
                $arrDataDataExt[$k] = urldecode($v);
            }
        }

        //for trace
        if(isset($arrDataDataExt['obj_id'])){
            $obj = json_decode($arrDataDataExt['obj_id'], true);
            if($obj){
                if(isset($obj['loc'])){
                    $arrDataDataExt['pos'] = $obj['loc'];
                }
                if(isset($obj['sid'])){
                    $arrDataDataExt['log_id'] = $obj['sid'];
                }
                if(isset($obj['extra'])){
                    $arrDataDataExt['extra'] = $obj['extra'];
                }
            }
        }
        //视频播放时长
        if ($urlkey == self::$videoPlayUrlKey) {
            $arrDataDataExt['client_option'] = array(
                'duration' => !empty($statLog['obj_duration']) ? floatval($statLog['obj_duration']) / 1000 : 0,  //播放时长，float类型，秒单位
                'video_duration' => isset($statLog['playduration']) ? floatval($statLog['playduration']) / 1000 : 0,// 视频自身时长，float类型，秒单位
            );
            if (true == self::isMiddlePagePlayLog($statLog)) {
                if (isset($statLog['obj_id']) && !empty($statLog['obj_id'])) {
                    $fromNid = getSignByTid($statLog['obj_id']);
                    if (empty($fromNid)) {
                        return false;
                    }
                    $arrDataDataExt['from_nid'] = $type . "_" . $fromNid;;
                }
            }
        }
        //落地页时长
        if ($urlkey == self::$pageDurationKey || $urlkey == self::$postDurationKey) {
            if (self::isIndexPageDurationLog($statLog))  {
                $arrDataDataExt['client_option']['duration'] = floatval($statLog['obj_duration']) / 1000;
            } else {
                return false;
            }
        }

        // $arrDataDataExt 加入一些server透传的字段
        // 注意，ext是一个list ！！
        $arrDataData['ext'][] = $arrDataDataExt;
        $arrData['data'] = $arrDataData;
        $oneInput['data'] = $arrData;
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function multiCallRdc($arrInput)
    {
        $header = array(
            'provider' => "tieba_app",
        );
        $service = 'service_index_recom_rdc';
        $method = 'post';
        $extra = array();
        $multi_req = array();
        foreach ($arrInput as $k => $oneInput) {
            $header['logid'] = $oneInput['logid'];
            $oneInput = json_encode($oneInput);
            Bingo_Log::warning($oneInput);
            $req = array($service, $method, $oneInput, $extra, $header);
            $multi_req['req_' . $k] = $req;
        }
        $ret = ral_multi($multi_req);
        return true;
    }

    /*
     * 视频播放日志是首页的条件
     */
    private static function isIndexPlayLog($statLog)
    {
        if ($statLog['obj_locate'] == 1) {
            return true;
        } elseif ($statLog['obj_locate'] == 13) {
            if (!empty($statLog['obj_id']) && is_numeric($statLog['obj_id']) && $statLog['obj_id'] != $statLog['tid']) {
                return true;
            }
        } elseif ($statLog['obj_locate'] == 6) {
            if ($statLog['obj_param3'] == 'index') {
                return true;
            }
        }
        return false;
    }

    /**
     * 视频播放日志是中间页的条件
     * @param $statLog
     * @return bool
     */
    private static function isMiddlePagePlayLog($statLog)
    {
        if ($statLog['obj_locate'] == 13) {
            if (!empty($statLog['obj_id']) && is_numeric($statLog['obj_id']) && $statLog['obj_id'] != $statLog['tid']) {
                return true;
            }
        }
        return false;
    }


    /**
     * 10.1版本新增：区分贴子类型, 1-图文,2-视频, 3-直播
     * @param $statLog
     * @return string
     */
    private static function getDislikeThreadType($statLog)
    {
        if (!isset($statLog['obj_param2']) || empty($statLog['obj_param2'])) {
            return "";
        }
        $type = "";
        $objParam2 = $statLog['obj_param2'];
        if (1 == $objParam2) {
            $type = 'news';
        }
        if (2 == $objParam2) {
            $type = "sv";
        }
        /**
         * 直播暂时不要了
         */
        if (3 == $objParam2) {
            $type = "";
        }
        return $type;
    }


    /**
     * @wiki http://agroup.baidu.com/recommendation/md/article/856980
     * @param $statLog
     * @return bool
     */
    private static function isIndexPageDurationLog($statLog)
    {
        if (!isset($statLog['obj_source']) || empty($statLog['obj_source'])) {
            return false;
        }

        if (!isset($statLog['obj_type']) || empty($statLog['obj_type'])) {
            return false;
        }

        if (!isset($statLog['obj_duration']) || empty($statLog['obj_duration'])) {
            return false;
        }
        $arrObjTypeRange = array(
            'a005' => 1,
            'a007' => 1,
            'a008' => 1,
            'a020' => 1,
            'a023' => 1,
        );
        $objSource = $statLog['obj_source'];
        $objType = $statLog['obj_type'];
        $objDuration = $statLog['obj_duration'];
        if (!isset($arrObjTypeRange[$objType])) {
            return false;
        }
        if ($objDuration <= 0 || $objDuration > 86400000) {
            return false;
        }
        $isIndex = false;
        if ($objType == 'a023') {
            if ($objSource == 'a002') {
                $isIndex = true;
            } else {
                return false;
            }
        } elseif ($objType == 'a020') {
            $isIndex = true;
        } else {
            if (false == strstr($objSource, 'a002')) {
                return false;
            } else {
                if (strlen($objSource) > 9) {
                    return false;
                }
                $isIndex = true;
            }
        }
        return $isIndex;
    }

    /**
     * 根据指定字段决定cmdid
     */
    private static function getCmdidByOptionKey($urlkey, $statLog) {
        $cmdid = self::$arrUrlkeyCmdid[$urlkey];
        if (isset(self::$arrUrlKeyCmdidOption[$urlkey])) {
            $optionKey = self::$arrUrlKeyCmdidOption[$urlkey]['option_key'];
            if (!empty($statLog[$optionKey])) {
                $optionVal = $statLog[$optionKey];
                if (isset(self::$arrUrlKeyCmdidOption[$urlkey][$optionVal])) {
                    $cmdid = self::$arrUrlKeyCmdidOption[$urlkey][$optionVal];
                }
            }
        }
        return $cmdid;
    }
}