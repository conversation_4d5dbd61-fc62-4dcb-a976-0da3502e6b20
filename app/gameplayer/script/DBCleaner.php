<?php
/**
 * 删除数据库过期数据，凌晨1-6点间每小时执行一次
 * ！！根据下面的时段设计，运行时间必须是1-6点
 * 
 * @authors tanxinyun
 * @date    2015-04-17 16:20:52
 */

class DBCleaner {

	// 分了64张表
	const TABLE_NUM = 64;

	/**
	 * 输入日志
	 * @param  [string] $msg [description]
	 * @return [string]      [description]
	 */
    public function log($msg) {
        $line = date('Y-m-d H:i:s').' '.$msg."\r\n";
        echo "$line";

        return $line;
    }

    /**
     * 主函数
     * @return [type] [description]
     */
    public function run() {
    	$this->log('job starting...');

    	$hour = intval(date('H'));

    	// 只在1-6点间进行删除操作
    	if ($hour < 1 || $hour > 6) {
            $this->log('hour not in [1, 6]');
    		return;
    	}

    	$tablePres = array('score_log_', 'day_score_');

    	foreach ($tablePres as $pre) {
    		for ($i = 0; $i < self::TABLE_NUM; $i++) {

    			$table = $pre.$i;
    			$time = $this->getTime();

    			$ret = $this->doSingleCall($table, $time);

    			if (empty($ret) || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
    				$status = json_encode($ret);
    			} else {
                    $status = 'OK';
                }

                $t = date('Ymd H:i:s', $time);
                $this->log("$table - $t: $status");

                sleep(3);
    		}
    	}

    	$this->log('job finished');
    }

    /**
     * 计算删除截止时间点
     * @return [int] [description]
     */
    public function getTime() {
        $h = intval(date('H'));
    	$t = mktime(0, 0, 0, date('m'), date('d') - 45, date('Y'));

    	return $t + $h * 4 * 3600;
    }

    /**
     * 系统调用，删除更新时间小于指定时间前的库表行
     * @param  [string] $table [description]
     * @param  [int] $time  [description]
     * @return [array]        [description]
     */
    public function doSingleCall($table, $time) {
    	$input = array(
    		'table' => $table,
    		'update_time' => $time,
    	);

    	return Tieba_Service::call('gameplayer', 'delRow', $input);
    }
}

$cleaner = new DBCleaner();
$cleaner->run();
