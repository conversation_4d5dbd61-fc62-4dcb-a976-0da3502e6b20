<?php
/**
 * 获取手游玩家的积分相关信息
 * @authors tanxinyun
 * @date    2015-03-25 15:43:16
 * @version $Id$
 */

class playerDetailAction extends Util_ActionBase {
    
    /**
     * 主逻辑
     * @return [null] [description]
     */
    public function execute() {
        if (!$this->arrUserInfo['is_login']) {
            return Util_Ret::jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN);
        }

        $arrInput = array(
            'user_id' => $this->arrUserInfo['user_id'],
        );

        $ret = Tieba_Service::call('gameplayer', 'getPlayerDetail', $arrInput, null, null, 'post', 'php', 'utf-8');

        if (empty($ret) || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('getPlayerDetail failure, ret: '.serialize($ret));
            return Util_Ret::jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return Util_Ret::jsonRet(0, 'success', $ret['data']);
    }
}