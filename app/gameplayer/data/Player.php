<?php
/**
 * 玩家信息的相关接口
 * @authors tanxinyun
 * @date    2015-03-25 16:04:02
 * @version $Id$
 */

class Data_Player {

	const ICON_NAME = 'starmaster';
	const LEVEL_UP_FLAG = 'player_level_up';

	/**
	 * 处理签到触发的加分逻辑
	 * @param  [array] $arrInput [输入参数]
	 * @return [array]           [结果结构体]
	 */
	public static function signIn($arrInput) {
		if (empty($arrInput['user_id']) || empty($arrInput['forum_id']) || !isset($arrInput['term_type'])) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'user_id/forum_id/term_type');
		}

		$forumId = intval($arrInput['forum_id']);
		$userId = intval($arrInput['user_id']);
		$termType = intval($arrInput['term_type']);

		$ret = Data_Mis::getScoreConfig();

		if ($ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'failed to get score config');
		}

		$scoreConfig = $ret['data'];

		if (empty($scoreConfig['sign_is_on'])) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_SUCCESS, 'task aborted');
		}
		
		// 达到最大签到吧数量后就不再加分
		if (Data_Limit::hitMaxSignNum($userId, $scoreConfig)) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_SUCCESS, 'hit max sign num');
		}

		$scores = Data_Score::getSignScore($userId, $termType, $scoreConfig);

		$input = array(
			'user_id' => $userId,
			'item_type' => $scores['item_type'],
			'item' => $forumId,
			'boost_factor' => $scores['boost_factor'],
			'score' => round($scores['boost_factor'] * $scores['base_score'], 1),
		);

		return self::save($input);
	}

	/**
	 * 批量签到数据保存接口
	 * @param  [array] $arrInput [sign_forums: {user_id, forum_id, term_type, update_time}]
	 * @return [array]           [description]
	 */
	public static function mSignIn($arrInput) {
		if (empty($arrInput) || empty($arrInput['sign_forums'])) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'sign_forums');
		}

		foreach ($arrInput['sign_forums'] as $forum) {
			if (empty($forum['user_id']) || empty($forum['forum_id']) 
				|| empty($forum['update_time']) || !isset($forum['term_type'])) {
				return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'user_id/forum_id/term_type/update_time');
			}
		}

		$ret = Data_Mis::getScoreConfig();

		if ($ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'failed to get score config');
		}

		$scoreConfig = $ret['data'];

		if (empty($scoreConfig['sign_is_on'])) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_SUCCESS, 'task aborted');
		}

		$userId = $arrInput['sign_forums'][0]['user_id'];
		$availNum = Data_Limit::getAvailSignNum($userId, $scoreConfig);
		
		// 达到最大签到吧数量后就不再加分
		if ($availNum <= 0) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_SUCCESS, 'hit max sign num');
		}

		$signForums = array_slice($arrInput['sign_forums'], 0, $availNum);

		$rec = self::getMultiSignRows($userId, $signForums, $scoreConfig);
		$sql = self::getMultiSignSql($userId, $rec['rows']);

		$input = array(
			'user_id' => $userId,
			'score' => $rec['score'],
			'sql' => $sql,
		);

		return self::save($input);		
	}

	/**
	 * 计算多条签到消息的得分，返回数据库行数组
	 * @param  [array] $signForums  [description]
	 * @param  [array] $scoreConfig [description]
	 * @return [array]              [rows]
	 */
	private static function getMultiSignRows($userId, $signForums, $scoreConfig) {
		$rows = array();
		$totalScore = 0;

		$factor = Data_Score::getMaxBoostFactor($userId, $scoreConfig);

		foreach ($signForums as $forum) {
			$item = Data_Score::getItemInfo($forum['term_type'], $scoreConfig);
			$score = round($item['base_score'] * $factor, 1);

			$rows[] = array(
				'update_time' => $forum['update_time'],
				'user_id' => $userId,
				'item_type' => $item['item_type'],
				'item' => $forum['forum_id'],
				'boost_factor' => $factor,
				'score' => $score,
			);

			$totalScore += $score;
		}

		return array(
			'rows' => $rows,
			'score' => $totalScore,
		);
	}

	/**
	 * 将多条签到信息数据转为一条sql
	 * @param  [int] $userId [userId]
	 * @param  [array] $rows   [rows]
	 * @return [string]         [sql]
	 */
	private static function getMultiSignSql($userId, $rows) {
		$table = Dl_DB::getScoreLogTable($userId);
		$sql = "INSERT INTO $table(id, update_time, user_id, item_type, item, boost_factor, score) values";
	
		$strs = array();

		foreach ($rows as $row) {
			$strs[] = self::rowToString($row);
		}

		return $sql.implode(',', $strs);
	}

	/**
	 * 行数据转为字符串
	 * @param  [array] $row [row]
	 * @return [string]      [sql]
	 */
	private static function rowToString($row) {
		$time = $row['update_time'];
		$uid = $row['user_id'];
		$type = $row['item_type'];
		$item = $row['item'];
		$factor = $row['boost_factor'];
		$score = $row['score'];

		return "(NULL, $time, $uid, $type, $item, $factor, $score)";
	}

	/**
	 * 活跃游戏触发的加分逻辑
	 * @param  [array] $arrInput [输入参数]
	 * @return [array]           [结果结构体]
	 */
	public static function playGame($arrInput) {
		if (empty($arrInput['user_id']) || empty($arrInput['game_name'])) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'user_id/game_name');
		}

		$userId = intval($arrInput['user_id']);
		$gameName = $arrInput['game_name'];
		$updateTime = intval($arrInput['update_time']);

		$ret = Data_Mis::getScoreConfig();

		if ($ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'failed to get score config');
		}

		$scoreConfig = $ret['data'];

		if (empty($scoreConfig['duokugame_is_on'])) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_SUCCESS, 'task aborted');
		}

		// 达到最大签到吧数量后就不再加分
		if (Data_Limit::hitMaxGamePlayNum($userId, $scoreConfig)) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_SUCCESS, 'hit max game play num');
		}

		$input = array(
			'user_id' => $userId,
			'item_type' => Util_Define::ITEM_TYPE_GAME_PLAY,
			'item' => $gameName,
			'boost_factor' => 1.0,
			'score' => $scoreConfig['game_play_score'],
			'update_time' => $updateTime,
		);

		return self::save($input);
	}

	/**
	 * 保存得分记录
	 * @param  [array] $arrInput  [输入参数]
	 * @param  [array] $arrConfig [mis配置信息]
	 * @return [array]            [结果结构体]
	 */
	public static function save($arrInput) {
		if (empty($arrInput['sql'])) {
			$ret = Data_Score::insertLog($arrInput);
		} else {
			$ret = Data_Score::insertLogBySql($arrInput);
		}
		
		if ($ret['errno'] == Tieba_Errcode::ERR_PARAM_ERROR) {
			return $ret;
		}

		// 日志记录插入失败并不关键，只要下面得分记录OK就行
		if ($ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('failed to insert score log, input: '.serialize($arrInput).', ret: '.serialize($ret));
		}

		$ret = Data_Score::updateDayScore($arrInput);

		if ($ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('updateDayScore failure, input: '.serialize($arrInput).', ret: '.serialize($ret));
			//return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, 'updateDayScore');
		}

		// 更新db里的用户总分
		$ret = Data_Score::updateTotalScore($arrInput);

		if ($ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('failed to update total score, input: '.serialize($arrInput).', ret: '.serialize($ret));
			return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, 'updateTotalScore');
		}

		$arrInput['prev_score'] = $ret['data']['prev_score'];
		$arrInput['score'] = $ret['data']['score'];

		// 数据库更新完成后，处理后续逻辑
		return self::afterUpdate($arrInput);
	}

	/**
	 * 更新完总分后的后续操作
	 * @param  [array] $arrInput  [输入数组]
	 * @param  [array] $arrConfig [mis配置数据]
	 * @return [array]           [结果结构体]
	 */
	private static function afterUpdate($arrInput) {
		// 更新用户得分排行榜
		$rankRet = Data_Rank::update($arrInput);

		// 更新排行榜非核心业务，失败时仍可继续后续逻辑
		// 在用户投诉时，再手工修正即可
		if ($rankRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('failed to update player rank, input: '.serialize($arrInput).', ret: '.serialize($rankRet));
		}

		// 检查用户是否等级提升
		$levelRet = self::isLevelUp($arrInput);

		if ($levelRet['level_up'] !== true) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_SUCCESS, $levelRet);
		}

		$arrInput['level'] = $levelRet['level'];

		return self::levelUpAct($arrInput);
	}

	/**
	 * 检查用户在本次加分后是否等级提升
	 * @param  [array]  $arrInput  [user_id，prev_level，score]
	 * @return [array]  $ret       [level_up, level]
	 */
	private static function isLevelUp($arrInput) {
		$prevLevel = Data_Score::calUserLevel(intval($arrInput['prev_score']));
		$curLevel = Data_Score::calUserLevel(intval($arrInput['score']));

		$ret = array(
			'level_up' => $curLevel > $prevLevel,
			'level' => $curLevel,
		);

		return $ret;
	}

	/**
	 * 等级提升时的处理逻辑
	 * @param  [array] $arrInput [输入参数数组]
	 * @return [array]           [结果结构体]
	 */
	private static function levelUpAct($arrInput) {
		$arrInput['val'] = 1;
		
		$failedCalls = array();

		// 发放印记，设置升级标志
		$calls = array(
			'Data_Player::grantIcon',
			'Data_Player::setLevelUpFlag',
		);

		foreach ($calls as $call) {
			$ret = call_user_func($call, $arrInput);
			if (empty($ret) || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
				$failedCalls[] = $call;
				Bingo_Log::warning($call.' failure, input: '.serialize($arrInput).', ret: '.serialize($ret));
			}
		}

		if (empty($failedCalls)) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_SUCCESS);
		} else {
			return Util_Ret::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, $failedCalls);
		}
	}

	/**
	 * 为用户发放印记
	 *
	 * icon等级不能是0，只能从1开始，PM用了VIP0，当初未留意到，给自己留了个坑，sigh
	 * 
	 * @param  [array] $arrInput [user_id，level]
	 * @return [array]           [结果结构体]
	 */
	public static function grantIcon($arrInput) {
		if (!isset($arrInput['user_id']) || !isset($arrInput['level'])) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'user_id/level');
		}

		$level = intval($arrInput['level']);

		$input = array(
			'user_id' => $arrInput['user_id'],
			'name' => self::ICON_NAME,
			'level' => $level + 1,
			'cnt' => 1,
		);
		
		return Tieba_Service::call('icon', 'setUserIcon', $input);
	}

	/**
	 * 设置等级是否提升的用户属性字段
	 * @param [array] $arrInput [user_id]
	 * @param string $val      [属性值]
	 * @return [array]           [结果结构体]
	 */
	public static function setLevelUpFlag($arrInput) {
		if (!isset($arrInput['user_id']) || !isset($arrInput['val'])) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'user_id/val');
		}

		$userId = intval($arrInput['user_id']);
		$val = strval($arrInput['val']);

		$input = array(
			'user_id' => $userId,
			'attr_name' => self::LEVEL_UP_FLAG,
			'attr_value' => $val,
		);
		
		return Tieba_Service::call('user', 'setUserAttr', $input);
	}

	/**
	 * 获取用户的积分，得分项目，星玩家等级等
	 * @param  [array] $arrInput [description]
	 * @return [array]           [结果结构体]
	 */
	public static function detail($arrInput) {
		if (empty($arrInput) || empty($arrInput['user_id'])) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'user_id');
		}

		$ret = Data_Mis::getScoreConfig();
		if ($ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			return $ret;
		}
		$configs = $ret['data'];

		$begin = Util_Date::todayBegin();
		$end = Util_Date::todayEnd();
		$userId = intval($arrInput['user_id']);

		$items = self::getItems($begin, $end, $userId);

		$totalScore = self::getTotalScore($userId);

		if ($items === false || $totalScore === false) {
			Bingo_Log::warning('get items/totalScore failure');
			return Util_Ret::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}

		$ret = Data_Duoku::queryStarPlayerLevel($userId);

		$starLevel = array(
			'star_level' => $ret['data']['level'],
		);

		$detail = array_merge($items, $totalScore, $starLevel);

		self::fixScore($detail, $configs);

		return Util_Ret::errRet(Tieba_Errcode::ERR_SUCCESS, $detail);
	}

	/**
	 * 返回一个可能虚假的分数
	 * @param  [array] $detail  [description]
	 * @param  [array] $configs [description]
	 * @return [array]          [description]
	 */
	private static function fixScore(&$detail, $configs) {
		$detail['total_score'] -= $detail['today_score'];

		if ($configs['sign_is_on']) {
			$pcScore = $configs['pc_sign_score'];
			$appScore = $configs['app_sign_score'];
		} else {
			$pcScore = 0;
			$appScore = 0;
		}

		$detail['pc_sign']['score'] = count($detail['pc_sign']['items']) * $pcScore;
		$detail['app_sign']['score'] = count($detail['app_sign']['items']) * $appScore;

		$starLevel = $detail['star_level'];
		$factor = empty(Util_Define::$DUOKU_FACTORS[$starLevel]) ? 1 : Util_Define::$DUOKU_FACTORS[$starLevel];
		
		$detail['today_score'] = $factor * ($detail['pc_sign']['score'] + $detail['app_sign']['score']) + $detail['game_play']['score'];
		
		$detail['total_score'] += $detail['today_score'];

		return $detail;
	}

	/**
	 *  返回当天得分记录情况
	 * @param  [int] $begin  [开始时间]
	 * @param  [int] $end    [结束时间]
	 * @param  [int] $userId [用户ID]
	 * @return [mixed]         [FALSE / 结果]
	 */
	private static function getItems($begin, $end, $userId) {
		$logs = Data_Score::queryLog($begin, $end, $userId);

		if ($logs === false) {
			return false;
		}

		$today = date('Y-m-d');

		if (empty($logs) || empty($logs[$today])) {
			$item = array(
				'items' => array(),
				'score' => 0,
				'boost_factors' => array(),
			);
			$rec = array(
				'today_score' => 0.0,
				Util_Define::$ITEM_KEYS[Util_Define::ITEM_TYPE_PC_SIGN] => $item,
				Util_Define::$ITEM_KEYS[Util_Define::ITEM_TYPE_APP_SIGN] => $item,
				Util_Define::$ITEM_KEYS[Util_Define::ITEM_TYPE_GAME_PLAY] => $item,
			);
		} else {
			$rec = $logs[$today];
			$rec['today_score'] = 0.0;

			foreach ($rec as $type => $v) {
				$rec['today_score'] += floatval($v['score']);
			}

			$misAdminKey = Util_Define::$ITEM_KEYS[Util_Define::ITEM_TYPE_MIS_ADMIN];
			unset($rec[$misAdminKey]);
		}

		return $rec;
	}

	/**
	 *  返回截止到当天的总积分
	 * @param  [int] $begin  [开始时间]
	 * @param  [int] $end    [结束时间]
	 * @param  [int] $userId [用户ID]
	 * @return [mixed]         [FALSE / 结果]
	 */
	private static function getTotalScore($userId) {
		$scores = Data_Score::queryTotalScore($userId);

		if ($scores === false) {
			return false;
		}

		if (empty($scores)) {
			$rec = array(
				'total_score' => 0.0,
				'user_level' => -1,
			);
		} else {
			$rec = array(
				'total_score' => $scores[0]['total_score'],
				'user_level' => Data_Score::calUserLevel($scores[0]['total_score']),
			);
		}

		return $rec;
	}

	/**
	 * 用户历史记录
	 * @param  [array] $arrInput [输入参数]
	 * @return [array]           [结果结构体]
	 */
	public static function actHistory($arrInput) {
		if (empty($arrInput['user_id'])) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'user_id');
		}

		$result = array(
			'forums' => array(),
			'games' => array(),
		);

		// 查询10天内数据
		$begin = Util_Date::todayBegin() - 9 * 86400;
		$end = Util_Date::todayEnd();
		$userId = $arrInput['user_id'];

		$logs = Data_Score::queryLog($begin, $end, $userId);
		
		if (empty($logs)) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_SUCCESS, $result);
		}

		$forumIds = array();
		$games = array();

		foreach ($logs as $day => $log) {
			$forumIds = array_merge($forumIds, $log['pc_sign']['items']);
			$forumIds = array_merge($forumIds, $log['app_sign']['items']);
			$games = array_merge($games, $log['game_play']['items']);
		}

		$forumIds = array_unique($forumIds);
		foreach ($forumIds as &$forumId) {
			$forumId = intval($forumId);
		}

		$result['forums'] = array_unique(self::getForumNames($forumIds));
		$result['games'] = array_values(array_unique($games));

		return Util_Ret::errRet(Tieba_Errcode::ERR_SUCCESS, $result);
	}

	/**
	 * 查询吧名
	 * @param  [array] $forumIds [吧id]
	 * @return [array]           [吧名]
	 */
	private static function getForumNames($forumIds) {
		$forumNames = array();

		if (empty($forumIds)) {
			return $forumNames;
		}

		$arrInput = array(
			'forum_id' => $forumIds,
		);

		$ret = Tieba_Service::call('forum', 'getFnameByFid', $arrInput, null, null, 'post', 'php', 'utf-8');

		if ($ret && $ret['errno'] == Tieba_Errcode::ERR_SUCCESS) {
			foreach ($ret['forum_name'] as $rec) {
				if ($rec['exist']) {
					$forumNames[] = $rec['forum_name'];
				}
			}
		}

		return $forumNames;
	}
}
