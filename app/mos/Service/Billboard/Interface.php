<?php
interface Service_Antispam_Interface{
    public function need_vcode(Service_Antispam_Entity_ReqNeedVcode $req,Service_Antispam_Entity_ResNeedVcode $res);
    public function get_vcode(Service_Antispam_Entity_ReqGetVcode $req,Service_Antispam_Entity_ResGetVcode $res);
    public function get_vcode_ex(Service_Antispam_Entity_ReqGetVcodeEx $req,Service_Antispam_Entity_ResGetVcodeEx $res);
    public function check_vcode(Service_Antispam_Entity_ReqCheckVcode $req,Service_Antispam_Entity_ResCheckVcode $res);
    public function prison(Service_Antispam_Entity_ReqPrison $req,Service_Antispam_Entity_ResPrison $res);
    public function tousu(Service_Antispam_Entity_ReqTousu $req,Service_Antispam_Entity_ResTousu $res);
    public function find_people_vcode(Service_Antispam_Entity_ReqFindPeopleVcode $req,Service_Antispam_Entity_ResFindPeopleVcode $res);
}

class Service_Antispam_Entity_Error extends Tbapi_Core_Entity{
    public $errno = 0;
    public $errmsg = '';
    public $usermsg = '';
    public function __construct(){
    }
}
class Service_Antispam_Entity_Anti extends Tbapi_Core_Entity{
    public $need_vcode = 0;
    public $open_shenshou = 0;
    public $vcode_md5 = '';
    public $vcode_type = 0;
    public function __construct(){
    }
}
class Service_Antispam_Entity_ReqNeedVcode extends Tbapi_Core_Entity{
    public $forum_id = 0;
    public $forum_name = '';
    public $anonymous = 0;
    public $pub_type = 0;
    public $thread_id = 0;
    public function __construct(){
    }
}
class Service_Antispam_Entity_ResNeedVcode extends Tbapi_Core_Entity{
    /**
     *
     * @var Service_Antispam_Entity_Anti
     */
    public $anti = null ;
    public function __construct(){
        $this->anti = new Service_Antispam_Entity_Anti();
    }
}
class Service_Antispam_Entity_ReqGetVcode extends Tbapi_Core_Entity{
    public $fid = 0;
    public $kw = '';
    public $anonymous = 0;
    public $pubtype = 0;
    public $tid = 0;
    public $_data_client_type = '';
    public $phone_type = '';
    public $tag = 0;
    public function __construct(){
    }
}
class Service_Antispam_Entity_ResGetVcode extends Tbapi_Core_Entity{
    /**
     *
     * @var Service_Antispam_Entity_Error
     */
    public $error = null ;
    /**
     *
     * @var Service_Antispam_Entity_Anti
     */
    public $anti = null ;
    public function __construct(){
        $this->error = new Service_Antispam_Entity_Error();
        $this->anti = new Service_Antispam_Entity_Anti();
    }
}
class Service_Antispam_Entity_ReqGetVcodeEx extends Tbapi_Core_Entity{
    public $fid = 0;
    public $kw = '';
    public $anonymous = 0;
    public $pubtype = 0;
    public $tid = 0;
    public $_data_client_type = '';
    public $phone_type = '';
    public $tag = 0;
    public $title = '';
    public $content = '';
    public function __construct(){
    }
}
class Service_Antispam_Entity_ResGetVcodeEx extends Tbapi_Core_Entity{
    /**
     *
     * @var Service_Antispam_Entity_Error
     */
    public $error = null ;
    /**
     *
     * @var Service_Antispam_Entity_Anti
     */
    public $anti = null ;
    public function __construct(){
        $this->error = new Service_Antispam_Entity_Error();
        $this->anti = new Service_Antispam_Entity_Anti();
    }
}
class Service_Antispam_Entity_ReqCheckVcode extends Tbapi_Core_Entity{
    public $vcode_md5 = '';
    public $vcode_tag = 0;
    public $vcode = '';
    public $forum_id = 0;
    public function __construct(){
    }
}
class Service_Antispam_Entity_ResCheckVcode extends Tbapi_Core_Entity{
    /**
     *
     * @var Service_Antispam_Entity_Error
     */
    public $error = null ;
    public function __construct(){
        $this->error = new Service_Antispam_Entity_Error();
    }
}
class Service_Antispam_Entity_ReqPrison extends Tbapi_Core_Entity{
    public $Ntn = '';
    public $filter_day = 0;
    public $tbs = '';
    public $login = false;
    public $filter_uname = '';
    public $uname = '';
    public $forum_name = '';
    public $forum_id = 0;
    public $thread_id = 0;
    public function __construct(){
    }
}
class Service_Antispam_Entity_ResPrison extends Tbapi_Core_Entity{
    /**
     *
     * @var Service_Antispam_Entity_Error
     */
    public $error = null ;
    public $manage = '';
    public function __construct(){
        $this->error = new Service_Antispam_Entity_Error();
    }
}
class Service_Antispam_Entity_ReqTousu extends Tbapi_Core_Entity{
    public $act = '';
    public $title = '';
    public $content = '';
    public $user_name = '';
    public $user_ip = '';
    public function __construct(){
    }
}
class Service_Antispam_Entity_ResTousu extends Tbapi_Core_Entity{
    /**
     *
     * @var Service_Antispam_Entity_Error
     */
    public $error = null ;
    public $data = '';
    public function __construct(){
        $this->error = new Service_Antispam_Entity_Error();
    }
}
class Service_Antispam_Entity_ReqFindPeopleVcode extends Tbapi_Core_Entity{
    public $fid = 0;
    public $type = 0;
    public function __construct(){
    }
}
class Service_Antispam_Entity_ResFindPeopleVcode extends Tbapi_Core_Entity{
    /**
     *
     * @var Service_Antispam_Entity_Error
     */
    public $error = null ;
    /**
     *
     * @var Service_Antispam_Entity_Anti
     */
    public $anti = null ;
    public function __construct(){
        $this->error = new Service_Antispam_Entity_Error();
        $this->anti = new Service_Antispam_Entity_Anti();
    }
}
