<?php
class Service_BillBoard_Logic_FetchProps extends Molib_Core_Logic{
    private $intUid = 0;
    private $intFid = 0;   
     
    protected function _check() {
        $this->intUid = intval($this->_reqData['uid']);
        $this->intFid = strval($this->_reqData['fid']);
        //校验tbs 
        if (empty($this->intUid) || empty($this->intFid) || !Tieba_Tbs::check($this->_reqData['tbs'], true)){
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID, $this->_reqData);
            return false;
        }
        
        return true;
    }
    protected function _execute() {
        $arrInput = array(
            'uid' => $this->intUid,
            'fid' => $this->intFid, 
        	'client_type' => 'wap',    //统计标识 	 
        );
        $arrOut = Molib_Tieba_Service::call('billboard', 'fetchProps', $arrInput);
		if (false === $arrOut) {
            $this->_error(Tieba_Errcode::FETCH_PROPS_FAILED, $this->_reqData);             
            return false;
        }   
        if(isset($arrOut['errno']) && $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
        	$this->_error($arrOut['errno'], $this->_reqData); 
        	return false;           
        }     
        $this->_resData['error']['errno'] = $arrOut['errno']; 
        $this->_resData['error']['usermsg'] = $arrOut['errmsg']; 
        $this->_resData['has_fetch']  = $arrOut['got'];//0未抢到 1 抢到
        $this->_resData['data']  = $arrOut['data']; 
        
        return true;
    }
    protected function _transform() {    	
        return true;
    }
}
