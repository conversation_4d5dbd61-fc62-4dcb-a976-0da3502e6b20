<?php
class Service_Forum_Logic_ForumManagers extends Molib_Core_Logic {
    private $intForumId = 0;
    private $arrManagers = array(
    	'managers' 			=> '',
    	'assist_managers' 	=> '',
    );
    
    protected function _check() {
        $this->intForumId = $this->_reqData['forum_id'];
        if (empty($this->intForumId)) {
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID, $this->_reqData);
            return false;
        }
        return true;
    }
    protected function _execute() {
        $arrOut = Tbapi_Core_Server::apicall('userforum','managers_info', array('forum_id' => $this->intForumId));
        if (false === $arrOut || !is_array($arrOut)) {
            $this->_error(Tieba_Errcode::ERR_MO_FORUM_GET_MANAGERS_FAIL, $this->_reqData);
            Molib_Util_Log::apiwarning('userforum','managers_info', false, $this->intForumId);
            return false;
        }
        $this->arrManagers = $arrOut;        
        return true;
    }
    protected function _transform() {
        $this->_resData['managers'] = $this->arrManagers['managers'];
        $this->_resData['assist_managers'] = $this->arrManagers['assist_managers'];
        return true;
    }
}