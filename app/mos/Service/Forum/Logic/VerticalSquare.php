<?php



class Service_Forum_Logic_VerticalSquare extends Molib_Core_Logic{
	private $intUserId = 0;
    protected function _check(){
        if (empty($this->_reqData['app_id'])){
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID, $this->_reqData);
            return false;
        }
        $this->intUserId = intval($this->_reqData['user_id']); 
        if($this->intUserId <= 0){
            $this->intUserId = 0;
        }
    }
    protected function _execute(){
         $arrInput['req'] = array(
            'app_id' => $this->_reqData['app_id'],
            'user_id' => $this->intUserId,
         );
         $arrOutput = Tieba_Service::call('verticalapp','forum_square', $arrInput,null ,null, 'post', 'php', 'utf-8');
         if (false === $arrOut || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            $intErrNo = (isset($arrOut['errno'])) ? $arrOut['errno'] : -1;
            Molib_Util_Log::apiwarning('verticalapp', 'forum_square', $intErrNo, $arrInput);
            return false;
        }
         $this->_resData['recommand_dir_name'] =  $arrOutput['res']['recommand_dir_name'];
         $this->_resData['recommend_info'] = $arrOutput['res']['recommend_info'];
         $this->_resData['menu_list'] = $arrOutput['res']['menu_list'];
    }
    protected function _transform(){
        
    }


}


?>