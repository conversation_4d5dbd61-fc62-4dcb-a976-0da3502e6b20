<?php

class Service_Forum_Logic_MixSearch extends Molib_Core_Logic {
	private $strQuery ;
	private $strSearchType = '';
	private $intPage = 1;
	private $intSortType = 0;  //0按时间顺序，1按时间倒序 2按相关性排序
	private $intPageSize = 10;
	private $intWireless = 65536;

	protected function _check() {
		$this->arrQuery = /*Molib_Util_Encode::convertGBKToUTF8*/urldecode($this->_reqData['query']);
	    if('' == $this->arrQuery)
		{
			return false;
		}
		$this->strSearchType = $this->_reqData['search_type'];
		if('' == $this->strSearchType)
		{
			return false;
		}
		$this->intPage = intval($this->_reqData['page']);
		if($this->intPage <0)
		{
			$this->intPage=1;
		}
		$this->intSortType = intval($this->_reqData['sort_type']);
		if( $this->intSortType=='' ||!in_array($this->intSortType, array(0,1,2)))
		{
			$this->intSortType=0;
		}
		$this->intPageSize = intval($this->_reqData['page_size']);
		if($this->intPageSize==0)
		{
			$this->intPageSize=10;
		}

		return true;
	}

	protected function _execute() {

		switch($this->strSearchType)
		{
			case 'forum': //搜吧
				{
					$strUrl = "/f/search/wfm?ie=utf-8&word=".$this->arrQuery."&qw=".$this->arrQuery."&sm=".$this->intSortType."&rn=".$this->intPageSize."&pn=".$this->intPage."&lm=".$this->intWireless;
                    $arrOut = Tbapi_Core_Midl_Http::httpcall('pbui_http',$strUrl,array());
                    if (false == $arrOut)
                    {
                       $this->_error(Tieba_Errcode::ERR_CALL_SERVICE_FAIL,array('arrayInput'=>$strUrl));
                       return false;
                    }
                    $arrOut  = Bingo_String::json2array($arrOut);
                    if($arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS)
                    {
                   	   $this->_error($arrOut['errno'],array('arrayInput'=>$strUrl,'output'=>$arrOut));
                       return false;
                    }
                    $this->_resData['pageData']=$arrOut['data'];
                    break;
				}
			case 'composite':  //综合搜索
				{
					$arrInput = array(
							'query_type'    => 0,
							'page_num'      => ($this->intPage),
							'res_num'       => $this->intPageSize,
							'post_category' => 0,
							'sort_mode'     => $this->intSortType,
							'cluster_mode'  => 0,
							'forum_name'    => "",
							'username'      => '',
							'query_word'    => $this->arrQuery,
							'imageurl'      => 0,
							'videourl'      => 0,
							'tbt_min'       => 0,
							'tbt_max'       => 0,
							'uip'			=> Mo_Request::$strIp,
							'baiduid'		=> Mo_Request::getCookie('BAIDUID',''),
							'baiduverify'	=> Mo_Request::getCookie('BAIDUVERIFY',''),
							'search_on'     => strval($this->_reqData['search_on']), // 搜贴流量来源端
					);
					$arrOutput = Molib_Tieba_Service::call('search','searchPost',$arrInput);
					if ($arrOutput === false||Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
						Bingo_Log::fatal('call searchps call failed! [service_name:searchps] '.serialize($arrOutput));
						$this->_error(Tieba_Errcode::ERR_MO_INTERNAL_ERROR, array('arrInput'=>$arrInput, 'output'=> $arrOutput));
						return false;
					}else{
						$arrOutput = $arrOutput['data'];
					}

					$arrOutData = $this->_buidReturnData($arrOutput);

					$this->_resData['pageData'] = $arrOutData;
                    break;
				}
		}

	}
	private function _buidReturnData($arrOutData){
		$arrReturnData = array();
		$arrReturnData['illegal'] = false;
		$arrReturnData['page'] = $this->_buildPage($arrOutData);
		$pn = $this->intPage;
		if($pn === 1 ){
			$ret = $this->searchForumData($this->arrQuery);
			$arrReturnData['forum'] = $ret;
		}

		$arrReturnData['search_what'] = 'composite';

		$arrReturnData['post'] = $this->getPostRes($arrOutData);

		$arrReturnData['reg_num'] = isset($arrReturnData['post'])? count($arrReturnData['post']) : 0;
		$arrReturnData['search_key'] = $this->arrQuery;
		$arrReturnData['search_turn'] = '';
		$arrReturnData['tbs'] = '';
		$arrReturnData['total_num'] = $arrOutData['dispNum'];
		$arrReturnData['kw'] = '';
		return $arrReturnData;
	}

	protected function getPostRes($frasOutput){
		// ��֯����?
		$arrPostRes = array();
		if(isset($frasOutput['post_list']) && is_array($frasOutput['post_list'])){
			foreach($frasOutput['post_list'] as $result){
				if($result['type'] == "SP"){
					continue;
				}
				$arrItem = array();
				$arrItem['tid'] = $result['tid'];
				$arrItem['pid'] = $result['pid'];
				$arrItem['title'] = $result['title_h'];
				$arrItem['brief'] = $result['content_h'];
				$arrItem['time'] = $result['time'];
				$arrItem['url'] = $result['url'];
				$arrItem['forum'] = $result['forum'];
				$arrItem['is_replay'] = strpos($arrItem['title'], "回复:")===0 ? true : false;
				// �û���Ϣ
				/*$ret = $this->getUserInfo($result['username']);
				if($ret === false){
					Bingo_Log::warning("get user info faile. param:[".serialize($result['username'])."]");
				}*/
				$arrItem['author'] = array(
						'name' => $result['username'],
						//'id' => $ret['user_id'],
				);
				// �ظ���Ϣ
				/*
				$arrInput = array(
						'input' => array(
								0 => array(
										'thread_id' => $result['tid'],
										'post_id' => $result['pid'],
								),
						),
				); */
                $arrInput = array(
                    'thread_id' => $result['tid'],
                    'offset' => '0',
                    'res_num' => '10',
                    'see_author' => '0',
                    'has_comment' => '1',
                    'has_mask' => '1',
                    'has_ext' => '1',
                    'need_set_pv' => '0',
                    'structured_content' => 0,
                );
				$ret = $this->checkPostInfo($arrInput);
				if($ret === false){
					Bingo_Log::warning("get user info faile. param:[".serialize($arrInput)."]");
				}
				$arrItem['replay'] = $ret['total_post_num'];
				// ͼƬ ��Ƶ ��Ƶ��Ϣ
				$arrItem['video'] = array();
				$arrItem['audio'] = array();
				$arrItem['img'] = array();

				if (isset($result['media']) && !empty($result['media'])) {
					foreach ($result['media'] as $k => $media) {
						if ($media['type'] === 'video') {
							$arrItem['video'][] = $media['url'];
						} elseif($media['type'] === 'music') {
							$arrItem['audio'][] = $media['url'];
						} elseif($media['type'] === 'image') {
							$arrItem['img'][] = $media['url'];
						}
					}
				}

				$arrPostRes[] = $arrItem;
			}
		}

		return $arrPostRes;
	}

	protected function checkPostInfo($arrInput){
		// $res = Tieba_Service::call("post", "checkPostInfo", $arrInput);
		$res = Tieba_Service::call("post", "getPostsByThreadId", $arrInput);
		if(empty($res) ||$res['errno'] != 0){
			Bingo_Log::warning("Failed to call Service:[user], method:[checkPostInfo], param:[".serialize($arrInput)."]");
			return false;
		}
	    return $res['output']["output"][0];
	}

	protected function searchForumData($strFname){
		if(empty($strFname)){
			Bingo_Log::warning("forum name is empty");
			return array();
		}
		$arrForumInfo = array();
		// ���� ��id ��ͼ��
		$arrInput = array(
				'query_words' => array($strFname)
		);
		$res = Tieba_Service::call('forum', 'getFidByFname', $arrInput);
		if($res===false || !isset($res) ||$res['errno'] != 0){
			Bingo_Log::warning("Failed to call Service:[forum], method:[getFidByFname], param:[".serialize($arrInput)."]");
			return false;
		}
		$forumId = $res['forum_id'][0]['forum_id'];
		$arrInput = array('forum_id' => array($res['forum_id'][0]['forum_id']));
		$res = Tieba_Service::call('forum', 'mgetBtxInfoEx', $arrInput);
		if($res===false || !isset($res) || $res['errno'] != 0){
			Bingo_Log::warning("Failed to call Service:[forum], method:[mgetForumInfoEx], param:[".serialize($arrInput)."]");
			return false;
		}
		$forumInfo = array();
		$forumInfo['brief'] = $res['output'][$forumId]['card']['slogan'];
		$forumInfo['img'] = $res['output'][$forumId]['card']['avatar'];
		$forumInfo['id'] = $res['output'][$forumId]['forum_name']['forum_id'];
		$forumInfo['name'] = $res['output'][$forumId]['forum_name']['forum_name'];
		$forumInfo['post'] = $res['output'][$forumId]['statistics']['thread_num'];
		$forumInfo['member'] = $res['output'][$forumId]['statistics']['member_count'];
		$forumInfo['hot_value'] = $res['output'][$forumId]['dir']['hot_value'];
		$forumInfo['dir'] = array(
				'level_1_name' => $res['output'][$forumId]['dir']['level_1_name'],
				'level_2_name' => $res['output'][$forumId]['dir']['level_2_name'],
		);
		// ��ǩ��Ϣ
		$arrInput = array(
				'forum_ids' => array($forumInfo['forum_id']),
		);
		$res = Tieba_Service::call('consume', 'getTagsByFids', $arrInput);
		if($res===false || !isset($res) || $res['errno'] != 0){
			Bingo_Log::warning("Failed to call Service:[consume], method:[getTagsByFids], param:[".serialize($arrInput)."]");
		}
		$forumInfo['tags'] = $res['ret']['tags'];

		// �û��Ƿ�like
		if($this->_intUid > 0){
			$arrInput = array(
					'forum_id' => $forumInfo['id'],
					'user_id' => $this->_intUid,
					'user_ip' => 0,
			);
			$res = Tieba_Service::call("perm","getPerm",$arrInput);
			if(!isset($res) ||$res['errno'] != 0){
				Bingo_Log::warning("Failed to call Service:[perm], method:[getPerm], param:[".serialize($arrInput)."]");
			}
			$forumInfo['is_like'] = $res['output']['grade']['is_like'];
		}
		return $forumInfo;
	}

	private function _buildPage($arrOutput)
	{
		$total_page = intval(ceil($arrOutput['listNum']/$this->intPageSize));
		$page = array(
				'cur_page' => $this->intPage,
				'total_page' => $total_page,
				'total_num' => $arrOutput['listNum'],
				'page_size' => $this->intPageSize,
		);
		return $page;
	}

    protected function _transform() {
    }

}

?>
