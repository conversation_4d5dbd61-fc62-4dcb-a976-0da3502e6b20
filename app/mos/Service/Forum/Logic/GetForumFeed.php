<?php
class Service_Forum_Logic_GetForumFeed extends Molib_Core_Logic {
	
	const VOICE_SHOW_NONE = 0; // 不显示语音
    const VOICE_SHOW_BUTTON = 1; // 显示语音为按钮
    const VOICE_SHOW_TEXT = 2; // 显示语音为文本
    const USER_ICON_SIZE = 2;
     
	//请求数据
	private $intUserId = 0;
	private $intLimit = 0;
	private $intOffset = 0;
	private $intVoiceShow = 0; // 

 	private $_bolUseNewUrl = false; //是否返回cdn图片url
 	private $_intPicStrategyType = 0;
 	private $_intScreenWidth = 0;
 	private $_intScreenHeight = 0;
 	private $_intScreenDip = 0;
 	private $_intQualityType = 0;
	
	//内部数据
	private $intTotal = 0; 
	private $intHasMore = false; 
	private $arrFeedThreadList = array(); 
	private $arrVoice = array();

	protected function _check() {
		$this->intUserId = $this->_reqData['user_id']; 
		if (empty($this->intUserId)) {
			$this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID, $this->_reqData);
			return false;
		}
		$this->intLimit = isset($this->_reqData['rn']) ? intval($this->_reqData['rn']) : 20; 
		if ($this->intLimit <= 0) $this->intLimit = 20;
		$intPage = isset($this->_reqData['pn']) ? intval($this->_reqData['pn']) : 1;
		if ($intPage <= 0) $intPage = 1;
		$this->intOffset = ($intPage - 1) * $this->intLimit;

		$this->intVoiceShow = isset($this->_reqData['voice_style']) ? intval($this->_reqData['voice_style']) : 0;
	    if ($this->intVoiceShow != self::VOICE_SHOW_BUTTON && $this->intVoiceShow != self::VOICE_SHOW_TEXT) {
			$this->intVoiceShow = self::VOICE_SHOW_NONE;
		}
        if (!empty($this->_reqData['show_user_icon'])) {
            $this->_bolShowUserIcon = true;
        }
        if (!empty($this->_reqData['user_icon_num'])) {
            $this->_intUserIconNum = intval($this->_reqData['user_icon_num']);
        }
        if (!empty($this->_reqData['user_icon_size'])) {
            $this->_intUserIconSize = intval($this->_reqData['user_icon_size']);
        }
        
		//是否返回cdn 图片url
		$this->_bolUseNewUrl = isset($this->_reqData['is_new_url']) && is_bool($this->_reqData['is_new_url']) ? $this->_reqData['is_new_url'] : false;        
		if ($this->_bolUseNewUrl) {
            //图片参数保护, 会用到这些参数
            $this->_intScreenWidth = ($this->_reqData['scr_w'] > 0) ? $this->_reqData['scr_w'] : 320;
            $this->_intScreenHeight = ($this->_reqData['scr_h'] > 0) ? $this->_reqData['scr_h'] : 320;
            $this->_intScreenDip = ($this->_reqData['scr_dip'] > 0) ? $this->_reqData['scr_dip'] : 1;
            $this->_intQualityType = ($this->_reqData['q_type'] > 0) ? $this->_reqData['q_type'] : 1;
            $this->_intPicStrategyType = ($this->_reqData['pic_strategy_type'] > 0) ? $this->_reqData['pic_strategy_type'] : 1;
		}
		return true;
	}

	protected function _execute() {
		$arrInput = array(
			'user_id' => $this->intUserId,
			'limit' => $this->intLimit,
			'offset' => $this->intOffset,
		);
		$serviceKey = 'getFeedThreadOfLikeForum';
		//Bingo_Timer::start($serviceKey);
		$arrOut = Tieba_Service::call('consume', 'getFeedThreadListOfLikeForum', $arrInput, NULL,NULL,'post','php','utf-8');
		//Bingo_Timer::end($serviceKey);
		if(false === $arrOut || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
			$this->_error(Tieba_Errcode::ERR_MO_INTERNAL_ERROR, $arrOut);
			Molib_Util_Log::apiwarning('consume', 'getFeedThreadListOfLikeForum', false, $arrInput);
			return false;
		}
		$this->intTotal = $arrOut['ret']['total'];
		$this->intHasMore = $arrOut['ret']['has_more'];
		if (isset($arrOut['ret']['goodthreadfeed']) && !empty($arrOut['ret']['goodthreadfeed'])) {			
				$this->arrFeedThreadList = $arrOut['ret']['goodthreadfeed'];
		} 
		else {
			$this->arrFeedThreadList = array();
		}
        $arrUserData = array();
        if ($this->_bolShowUserIcon) {
            foreach ($this->arrFeedThreadList as $arrThread) {
                $arrUserId[] = $arrThread['user_id'];
            }       
            if (!is_array($arrUserId)) {
                return array();
            }       
            if ($this->_intUserIconSize) {
                $intUserIconSize = $this->_intUserIconSize;
            } else {
                $intUserIconSize = self::USER_ICON_SIZE;
            }
            $arrInput = array(
                    'user_id' => $arrUserId,
                    'get_icon' => $intUserIconSize,
                    );      
            $arrUserData = Tieba_Service::call('user', 'mgetUserData', $arrInput, NULL,NULL,'post','php','utf-8');
        }
        foreach ($this->arrFeedThreadList as $intKey => $arrThread) {
            if ($arrUserData['user_info'][$arrThread['user_id']]['iconinfo']) {
                $this->arrFeedThreadList[$intKey]['userinfo']['iconinfo'] = $arrUserData['user_info'][$arrThread['user_id']]['iconinfo'];
            } else {
                $this->arrFeedThreadList[$intKey]['userinfo']['iconinfo'] = array();
            }
			
			/*
			T秀勋章
			*/
			$mParr_props = $arrUserData['user_info'][$arrThread['user_id']]['mParr_props'];
			$this->arrFeedThreadList[$intKey]['userinfo']['mParr_props'] = $mParr_props;
			$this->arrFeedThreadList[$intKey]['userinfo']['tshow_icon'] = Molib_Util_TshowInfo::convertTshowIcon($mParr_props);
        }
		if ($this->intVoiceShow == self::VOICE_SHOW_BUTTON) {
			$arrTids = array();
			foreach ($this->arrFeedThreadList as &$arrThread) {
				if ($arrThread['is_voice']) {
					$arrTids[] = $arrThread['thread_id'];
				}
			}
			if (empty($arrTids)){
			    return true;
			}
			///*******************  获取语音
			$ret = Tbapi_Core_Server::mocall('voice', 'get_thread_voice_infos_by_tids', array('tids' => $arrTids));
			if (is_array($ret['voice_list'])) {
				$this->arrVoice = $ret['voice_list'];
			}
		}

		return true;
	}
	protected function _transform() {
	    $arrThreadList = array();
		foreach ($this->arrFeedThreadList as $arrTmp) {
			$arrThread = array();
            $arrThread['forum_id'] = $arrTmp['forum_id'];
            $arrThread['thread_id'] = $arrTmp['thread_id'];
            $arrThread['forum_name'] = $arrTmp['forum_name'];
            $arrThread['user_id'] = $arrTmp['user_id'];
            $arrThread['userinfo'] = $arrTmp['userinfo'];
            $arrThread['user_name'] = $arrTmp['user_name'];
            $arrThread['post_num'] = $arrTmp['post_num'];
            $arrThread['is_good'] = $arrTmp['is_good'];
            $arrThread['is_top'] = $arrTmp['is_top'];
            $arrThread['is_up'] = $arrTmp['is_up'];
            $arrThread['is_manypostnum'] = $arrTmp['is_manypostnum'];
            $arrThread['create_time'] = $arrTmp['create_time'];
            $arrThread['modify_time'] = $arrTmp['modify_time'];
            $arrThread['user_pic_url'] = $arrTmp['user_pic_url'];
            $arrThread['is_voice_thread'] = $arrTmp['is_voice'] ? 1 : 0;
            $arrThread['thread_type'] = $arrTmp['is_voice'] ? Tieba_Type_Thread::VOICE_THREAD : $arrTmp['thread_type'];
            
            $strTitle = $arrTmp['title'];
			$strTitle = html_entity_decode($strTitle, ENT_COMPAT, 'UTF-8');
			$arrThread['title'] = trim($strTitle);
			
			$arrAllMedia = &$arrTmp['media'];
			if (!empty($arrAllMedia)) {
				foreach ($arrAllMedia as &$arrMedia) {
					$arrMedia['type'] = Molib_Util_RichTextParser::$arrTypeMap[$arrMedia['type']];
					$arrMedia['pic_info'] = $arrMedia['picInfo'];
					unset($arrMedia['picInfo']);
				}
			}else {
				$arrAllMedia = array();
			}
            $arrThread['media'] = $arrTmp['media'];
            //处理语音
            if ($arrTmp['is_voice']) {        
				if ($this->intVoiceShow == self::VOICE_SHOW_BUTTON) { // 显示语音图标
				    if (!empty($this->arrVoice) && isset($this->arrVoice[ $arrThread['thread_id']])){
					    $arrThread['voice_info'] = $this->arrVoice[ $arrThread['thread_id'] ];
				    }
				} 
				elseif ($this->intVoiceShow == self::VOICE_SHOW_TEXT) { // 显示语音文字						
					$arrTmp['abstract'] = '[语音]'.$arrTmp['abstract'];
				} else {
                    $arrThread['is_voice_thread'] = 0;
                }
            }
			//处理abstract
			$strAbstract = $arrTmp['abstract'];
			if (!empty($strAbstract)){
			    $strAbstract = html_entity_decode($strAbstract, ENT_COMPAT, 'UTF-8');
				$arrAbstract = array(
					array( 'type'=> 0, 'text'=> $strAbstract, ),
				);
			} 
			else {
				$arrAbstract = array();
			}
			$arrThread['abstract'] = $arrAbstract;
			
			$arrThreadList[] = $arrThread;
		}
        //处理首页frs图片策略
        if (true === $this->_bolUseNewUrl) {
            $arrSpec = array(
                'screen_w' => $this->_intScreenWidth,
                'screen_h' => $this->_intScreenHeight,
                'screen_dip' => $this->_intScreenDip,
                'q_type' => $this->_intQualityType,
            );
            $arrThreadList = Service_Frs_Libs_Page_FrsImgLogic::procNewPicUrl($arrThreadList, $this->_bolUseNewUrl, $arrSpec, $this->_intPicStrategyType);
        }
		$this->_resData['total'] = $this->intTotal;
		$this->_resData['has_more'] = $this->intHasMore;
		$this->_resData['feed_thread_list'] = $arrThreadList;
	}
}
