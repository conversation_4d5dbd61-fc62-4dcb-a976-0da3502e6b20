<?php
//获取和单个用户的历史聊天记录

class Service_Pletter_Logic_GetChatHistory extends Molib_Core_Logic {
	
	private $_intUserId = 0;
	private $_intComId = 0;
	private $_intMsgId = 0;//取<MsgId的数据
	private
	private $_arrMsg = array();
	
	public function _check() {
		$this->_intUserId = $this->_reqData['user_id'];
		$this->_intComId = $this->_reqData['com_id'];
		
        if (empty($this->_intUserId) || empty($this->_intComId)) {
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID);
            Bingo_Log::warning('login user_id or com_id is empty');
            return false;
        }  
        $this->_intMsgId = intval($this->_reqData['msg_id']) < 0 ? 0 : intval($this->_reqData['msg_id']);
    	return true;
    }
	
     protected function _execute()  {
     	 $this->_getChatHistory();     	 
     }
     protected function _transform() {
     	$this->_resData['has_more'] = $this->_arrMsg['has_more'];
     	
        $arrData = $this->_arrMsg['message'];
        if (!is_array($arrData) || count($arrData) == 0) {
     		$this->_resData['message'] = array();
        	return ;
     	}
     	foreach ($arrData as $value) {
     		$tmp = array();
     		$tmp['from'] = ($value['owner_id'] == $this->_intUserId) ? 1 : 0;//1表示这条消息是自己发的，0表示是对方发的
     		$tmp['msg_id'] = $value['message_id'];
     		$tmp['content'] =  Service_Pletter_Libs_Pletter::procRichText($value['content'],intval($this->_reqData['client_type']));
     		$tmp['time'] = $value['time'];
			$this->_resData['message'][] = $tmp;
     	}
     }
     
     private function _getChatHistory() {
     	 $arrInput = array(
                'user_id' => $this->_intUserId,  
     	 		'communicator_id' => $this->_intComId,
     	 		'message_id' => $this->_intMsgId,         
          	);
        $arrOut = Tieba_Service::call('pletter', 'getChatHistory', $arrInput,NULL,NULL,'post','php','utf-8');
		if (false === $arrOut || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Molib_Util_Log::apiwarning('pletter', 'getChatHistory', false, $arrInput);
			$this->_error(Tieba_Errcode::ERR_MO_GETCHATHISTORY_FAIL);
			return false;
		}     
        $this->_arrMsg = $arrOut;
     	$this->_resData['error']['errno'] = $arrOut['errno'];
     	$this->_resData['error']['errmsg'] = $arrOut['errmsg'];
 		return true;
     	
     }
     
}

?>
