<?php
//添加和单个用户的聊天信息

class Service_Pletter_Logic_AddMsg extends Molib_Core_Logic {
	
	private $_intUserId = 0;
	private $_intComId = 0;
	private $_strContent = '';
	private $_intContentType = 0;
	private $_intLastMsgId = null;
	private $_intClientType = 0;
	private $_arrList = array();
	private $_arrRecent = array();
	
	public function _check() {
		$this->_intUserId = intval($this->_reqData['user_id']);
		$this->_intComId = intval($this->_reqData['com_id']);
		$this->_strContent = trim($this->_reqData['content']);
		$this->_intContentType = 0;//目前只有文本，这一种方式
		$this->_intLastMsgId = intval($this->_reqData['last_msg_id']);
		$this->_intClientType = intval($this->_reqData['client_type']);
		
		if (empty($this->_intUserId) || empty($this->_intComId) || strlen($this->_strContent) == 0) {
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID);
            Bingo_Log::warning('login_user_id or com_id or content is empty');
            return false;
        }  
         
    	return true;
    }
	
     protected function _execute()  {
        $req=array(
            'req'=>array(
                'user_id'=>$this->_intUserId,
                'forum_id'=>0,
            ),
        );
        $arrOut = Tieba_Service::call('anti', 'antiUserBlockQuery', $req);
        if (false === $arrOut) {
            Bingo_Log::warning('call antiUserBlockQuery failed: ' . serialize($arrOut));
            Tieba_Stlog::addNode ('call_anti_failed', 1);
            Tieba_Stlog::addNode ('is_block', 1);
			$this->_error(Tieba_Errcode::ERR_ANTI_ID_BLOCKED);
			//return false;
        }
		elseif ($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS && $arrOut['is_block'] == 1) {
			Bingo_Log::warning('antiUserBlockQuery check faild: ' . serialize($arrOut));
			Tieba_Stlog::addNode ('is_block', 1);
			$this->_error(Tieba_Errcode::ERR_ANTI_ID_BLOCKED);
			return false;
		}
		
		$arrOut = $this->_checkUserDialogNum();
		if ($arrOut === false) {
		    Tieba_Stlog::addNode ('chat_too_much', 1);
			$this->_error(Tieba_Errcode::ERR_ANTI_CHAT_TOO_MUCH);
			return false;
		}
		
        $this->_addToImPmsg(); //双写到新私聊
     	//处理content
     	$this->_strContent = Service_Pletter_Libs_Pletter::processContent($this->_strContent,intval($this->_reqData['client_type']));
		
     	$arrOut = $this->_checkNewPost();
		if (true === $arrOut) {
			$this->_error(Tieba_Errcode::ERR_SUCCESS);
			return true;
		}
		
     	$ret = $this->_addMSg();  
     	if (intval($this->_reqData['client_type']) == Service_Pletter_Libs_Pletter::CLIENT_TYPE_ANDROID){
     		$this->_getRecent();
     	} 
     }
     protected function _transform() {
        $this->_resData['message'] = $this->_arrList['message'];
        $this->_resData['message']['user_id'] = $this->_intUserId;
		$this->_resData['message']['com_id'] = $this->_intComId;
		$this->_resData['message']['msg_id'] = $this->_arrList['message']['message_id'];
		$this->_resData['message']['from']=1;
		if ($this->_intClientType == Service_Pletter_Libs_Pletter::CLIENT_TYPE_ANDROID) {//android用户，需要content字段
			
			$this->_resData['message']['content'] = Service_Pletter_Libs_Pletter::procRichText($this->_strContent,$this->_intClientType);;//android使用，为了保持统一，加msg_id字段
		 	$this->_resData['recent'] = array();
		 	if (isset($this->_arrRecent) && is_array($this->_arrRecent)){
				//android 特殊的需求
				$this->_resData['recent']['has_more'] = $this->_arrRecent['has_more'];  
				$arrData = $this->_arrRecent['message'];
        		if (!is_array($arrData) || count($arrData) == 0) {
     				$this->_resData['recent']['message'] = array();
        			return ;
     			}
     			$arrMessage = array();
     			foreach ($arrData as $value) {
     				$tmp = array();
     				$tmp['from'] = ($value['owner_id'] == $this->_intUserId) ? 1 : 0;//1表示这条消息是自己发的，0表示是对方发的
     				$tmp['msg_id'] = $value['message_id'];
					$tmp['content'] =  Service_Pletter_Libs_Pletter::procRichText($value['content'],intval($this->_reqData['client_type']));
     				$tmp['time'] = $value['time'];
     				$arrMessage[] = $tmp;
     			}
     			$this->_resData['recent']['message'] = $arrMessage;
			}	
			else
			{
				$this->_resData['recent']['has_more'] = 0;
				$this->_resData['recent']['message'] = array();
			}
		}
		
    }
     
     private function _addMSg() {
     	 $arrInput = array(
                'from_user_id' => $this->_intUserId, 
     	 		'to_user_id' => $this->_intComId, 
     	 		'content' => $this->_strContent,          
     	 		'type' => $this->_intContentType,          
          	);
        $arrOut = Tieba_Service::call('pletter', 'addMessage', $arrInput,NULL,NULL,'post','php','utf-8');
		if (false === $arrOut) {
			Molib_Util_Log::apiwarning('pletter', 'addMessage', false, $arrInput);
			$this->_error(Tieba_Errcode::ERR_MO_ADDMSG_FAIL);	
			return false;
		}     
		if ($arrOut['errno'] == 220015) {//命中敏感词
			$this->_error(Tieba_Errcode::ADDMSG_HAS_ILLEGAL_WORD);	
			return false;
		}
     	if ($arrOut['errno'] == 220034) {//命中粒度控制
			$this->_error(Tieba_Errcode::ADDMSG_HIT_ANTI);	
			return false;
		}
		if ($arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS) {//提示用户发送失败
			$this->_resData['error']['usermsg'] = '发送失败';
		}
        $this->_arrList = $arrOut;
     	$this->_resData['error']['errno'] = $arrOut['errno'];
        $this->_resData['error']['errmsg'] = $arrOut['errmsg']; 
 		return true;
     	
     }
	 private function _getRecent() {
		$arrInput = array(
                'user_id' => $this->_intUserId,  
     	 		'communicator_id' => $this->_intComId,
     	 		'message_id' => $this->_intLastMsgId,
          	);
		$arrOut = Tieba_Service::call('pletter', 'getRecentChat', $arrInput,NULL,NULL,'post','php','utf-8');
		if (false === $arrOut || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Molib_Util_Log::apiwarning('pletter', 'getRecentChat', false, $arrInput);
			return true;
		}
        $this->_arrRecent = $arrOut;
 		return true;
     	
     }
     
    private function _addToImPmsg() {
        $arrInput = array(
            'group_id' => 0,
            'msg_type' => 1,
            'user_id'  => $this->_intUserId,
        	'to_user_id'  => $this->_intComId,
            'content'  => $this->_strContent,
            'duration' => 0,
            'record_id' => -1,
            'cuid'     => '',
        	'version'  => '5.1.0',
        	'data'     => json_encode(array('client_version' => '5.1.0', 'seq_id' => 0)),
        );
        $arrOut = Tieba_Service::call('im', 'addPersonalMsg', $arrInput, NULL,NULL,'post','php','utf-8');
		if (false === $arrOut || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Molib_Util_Log::apiwarning('im', 'addPersonalMsg', false, $arrInput);
		}
		else {
		    Bingo_Log::notice('_addToImPmsg success'.serialize($arrInput));
		}
		return true;
    }
    
    private function _userBlockQuery() {
        $req=array(
            'req'=>array(
                'user_id'=>$this->_intUserId,
                'forum_id'=>0,
                ),
            );
        $res = Tieba_Service::call('anti','antiUserBlockQuery',$req);
    		if (false !== $res || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Molib_Util_Log::apiwarning('im', 'addPersonalMsg', false, $arrInput);
		}
		else {
		    Bingo_Log::notice('_addToImPmsg success'.serialize($arrInput));
		}
    }
    
    private function _checkNewPost() {
    	$arrAntiReq = array(
    			'captcha_cmd_no'      => 8,
    			'baiduuid'            => 'pmsg',  //伪造，和sunyuqi确认
    			'anti_command_no'     => 50000,
    			'login_flag'          => 2, //默认登录，和sunyuqi确认
    			'word'                => 'pmsg',  //伪造，和sunyuqi确认
    			'forum_name'          => 'pmsg',  //伪造，和sunyuqi确认
    			'forum_id'            => 1385680, //伪造，和sunyuqi确认
    			'fid'            	  => 1385680, //伪造，和sunyuqi确认
    			'user_name'           => 'pmsg',  //伪造，和sunyuqi确认
    			'user_id'             => intval($this->_intUserId),
    			'cookie'              => 'pmsg',   //伪造，和sunyuqi确认
    			'cookie_username'     => 'pmsg',   //伪造，和sunyuqi确认
    			'detail_len'          => strlen(trim($this->_strContent). "\0"),
    			'cookiebduid'         => 'pmsg', //伪造，和sunyuqi确认
    			'title'               => 'pmsg', //伪造，和sunyuqi确认
    			'(raw)rawdata'        => trim($this->_strContent). "\0",
    			'ori_content'         => trim($this->_strContent),
    			'content'             => trim($this->_strContent),
    			'tag'                 => 11,  //默认11,表示支持九宫格验证码，和sunyuqi确认
    			'rulegroup'           => array('app'),
    			'app'           	  => 'im',
    			'cmd'                 => 'postcheck',
    			'thread_id'            => 2527916008,
    			'grade'				  => 10,
    			'op_uid'              =>  intval($this->_intUserId),
    			'op_uname'            => 'pmsg',
    			'op_status'           => 0,
    		  
    	);
    	$req = array(
    			'req' => $arrAntiReq,
    	);
    	$res = Tieba_Service::call('anti','antiNewPostCheck',$req);
    	if(false!== $res && isset($res['errno']) && intval($res['errno']!=Tieba_Errcode::ERR_SUCCESS))
    	{
    		Molib_Util_Log::apiwarning('anti','antiNewPostCheck', false, $res);
    		return true;
    	}
    	return false;
    }
    
    private function _checkUserDialogNum() {
		$srcuid = $this->_intUserId;
        $dstuid = $this->_intComId;
        $maxUid = max($srcuid,$dstuid);
        $minUid = min($srcuid,$dstuid);
        $need_submit = false;
        $req = array(
            'req' => array(
                'reqs' => array(
                    'hasDialog'=>array('rulegroup'=>array('app'),'app'=>'client_msg','cmd'=>'dialog_user_check','srcuid'=>$maxUid,'dstuid'=>$minUid),
                    'srcuser'=>array('rulegroup'=>array('app'),'app'=>'client_msg','cmd'=>'dialog_num_check','uid'=>$srcuid),
                    'dstuser'=>array('rulegroup'=>array('app'),'app'=>'client_msg','cmd'=>'dialog_num_check','uid'=>$dstuid),
                ),  
            ),  
        );
        $arrOut = Tieba_Service::call('anti','antiActsctrlMultiQuery',$req);
        if ($arrOut == false || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            $need_submit = true;
            Tieba_Stlog::addNode ('call_anti_failed', 1);
            Molib_Util_Log::apiwarning('anti', 'antiActsctrlMultiQuery', false, $arrOut);
            //return false;
        }
        else {
            if ($arrOut['res']['hasDialog']['err_no'] === 0) {  //用户之间未建立过会话
                if ($arrOut['res']['srcuser']['err_no'] === 0 && $arrOut['res']['dstuser']['err_no'] === 0) {
                    $need_submit = true;  //两者会话数都未到上限，允许双方建立会话，然后需要submit到ueg，会话计数增加
                }
                else {  //有一方会话数到上限
                    Bingo_Log::warning("one uid match the max dialog num, so need filter " . serialize($arrOut));
                    return false;
                }
            }
            //已建立会话直接放行
        }
        
        if ($need_submit) {
            $arrOut = Tieba_Service::call('anti','antiActsctrlMultiSubmit',$req);
            if ($arrOut == false || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Molib_Util_Log::apiwarning('anti', 'antiActsctrlMultiQuery', false, $arrOut);
            }
        }
        return true;
    }
}