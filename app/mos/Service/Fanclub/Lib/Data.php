<?php
/**
 * @author: ji<PERSON><PERSON><PERSON>
 * @date: 2013-11-07
 * @file: Data.php
 * @description:
 */

class Service_Fanclub_Lib_Data {
//    const OFFICIAL_CLUB_ID = 303336;//�ٷ���id����ʱ�ĳ�test2
    const OFFICIAL_CLUB_ID = 8585575;

    public static function isClubMember($forum_id,$user_id){
        $arrInput = array(
            'forum_id' => intval($forum_id),
            'user_id' => array($user_id)
        );
        if(self::OFFICIAL_CLUB_ID == $forum_id){//����ǹٷ��ɣ�Ĭ�������û����ǻ�Ա
            return true;
        }
        $arrOutput = Tieba_Service::call('user', 'mgetUserForumData', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        if( !isset($arrOutput['errno']) || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno'] ){
            Bingo_Log::warning("call mgetUserForumData error! [input:".serialize($arrInput)."] [output:".serialize($arrOutput)."]");
            return false;
        }
        $intIsMember = intval($arrOutput['user_info'][$user_id]['is_member']);
        if($intIsMember === 0){
            return false;
        }else{
            return true;
        }
    }
    public static function getPreonlineUser(){
           return array(5, 1496, 2605, 69262955/*cinaxi*/, 41520859/*echociel*/, 8181639/*Mikyung������*/,170166752/*xinlei1812*/);
    }
    public static function checkPreonlineUser($intUserId){
        if($intUserId <= 0){
            return false;
        }
        return Bingo_Array::in_array($intUserId,self::getPreonlineUser());
    }
} 
