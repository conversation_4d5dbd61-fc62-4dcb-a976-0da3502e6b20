<?php
/**
 * Author: tangyan
 * Date: 13-9-29
 * Desc: ��ȡ�û���ĳ�����ǰɵ���Ϣ
 */

class Service_Fanclub_Logic_ApplyMembership extends Molib_Core_Logic{
	const CLIENT_PC = 0;
	const CLIENT_APP = 1;
	const CLIENT_WAP = 2;
    protected function _check(){
		$arrInput = $this->_reqData;
		if(!isset($arrInput['forum_id'])
			|| !isset($arrInput['duration'])
			|| !isset($arrInput['from_client'])
			|| !isset($arrInput['from_type'])){
    		$this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID);
            return false;
    	}
		$user_id  = intval($arrInput['user_data']['id']);
		if (!$user_id) {
    		$this->_error(Tieba_Errcode::ERR_USER_NOT_LOGIN);
			return false;
		}
        return true;
    }
    protected function _execute(){
		$arrInput = array (
			'forum_id'     => $this->_reqData['forum_id'],
			'user_id'      => $this->_reqData['user_data']['id'], 
			'duration'     => $this->_reqData['duration'],
			'from_client'  => $this->_reqData['from_client'],
			'from_type'    => $this->_reqData['from_type'],
			'service_name' => $this->_reqData['service_name'],
			'service_desc' => $this->_reqData['service_desc'],
			'service_url'  => $this->_reqData['service_url'],
			'return_url'   => $this->_reqData['return_url'],
			);
		//Bingo_Log::warning(print_r($arrInput,true));
        $arrOutput = Tieba_Service::call('fstar','applyMembership',$arrInput, NULL,'service_fstar2', 'post', 'php', 'utf-8');
        if(!isset($arrOutput['errno']) || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning('apply membership failed: input['.serialize($arrInput).'] output['.serialize($arrOutput).']');
            $this->_resData['error']['errno'] = $arrOutput['errno'];
            return false;
        }
		/*
		$from_client = intval($arrInput['from_client']);
		if ($from_client === self::CLIENT_PC) {
			//��app,��Ҫ��ת��֧��ҳ
			$location = $arrOutput['data']['consume_url'];
			header('Location: '.$location);
			return true;
		}
		//����app,��Ҫ���ض�����
		 */
		$this->_resData['errno'] = Tieba_Errcode::ERR_SUCCESS;
		$this->_resData['data'] = $arrOutput['data'];
        return true;
    }
    protected function _transform(){
        return true;
    }
}
