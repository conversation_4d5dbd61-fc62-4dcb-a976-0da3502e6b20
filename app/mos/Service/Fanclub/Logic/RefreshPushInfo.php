<?php
/**
 * Author: ji<PERSON><PERSON><PERSON>
 * Date: 13-9-25
 * Time: 2013-09-25
 * Desc: 获取用户在粉丝俱乐部的权限相关信息，包括会员，表情等权限
 */

class Service_Fanclub_Logic_RefreshPushInfo extends Molib_Core_Logic{
    protected function _check(){
        if (	!isset($this->_reqData['channel_id']) 	||
            !isset($this->_reqData['y_user_id'])
        ) {
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID);
            return false;
        }
        return true;
    }
    protected function _execute(){
        $this->_reqData['user_id'] = intval($this->_reqData['user_data']['id']);
        $this->_reqData['user_name'] = $this->_reqData['user_data']['name'];
        $arrInput = $this->_reqData;
        $arrOutput = Tieba_Service::call('fstar', 'refreshPushInfo', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        if(!isset($arrOutput['errno']) || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning('call refreshPushInfo error input['.serialize($arrInput).'] output['.serialize($arrOutput).']');
            $this->_resData['error']['errno'] = Tieba_Errcode::ERR_MO_ACTIVITY_ADD_ORDER;
            return false;
        }
        return true;
    }
    protected function _transform(){
        return true;
    }
}