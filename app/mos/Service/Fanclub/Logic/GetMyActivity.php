<?php
/**
 * Author: ji<PERSON><PERSON><PERSON>
 * Date: 13-9-25
 * Time: 2013-09-25
 * Desc: 获取用户在粉丝俱乐部的权限相关信息，包括会员，表情等权限
 */

class Service_Fanclub_Logic_GetMyActivity extends Molib_Core_Logic{
    protected function _check(){
	    if (	!isset($this->_reqData['forum_id']) 	|| 
        		!isset($this->_reqData['pn']) 		||
        		!isset($this->_reqData['limit'])
        	) {
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID);
            return false;
        }
        return true;
    }
    protected function _execute(){
    	$arrInput = $this->_reqData;

    	$arrInput['user_id'] = intval($this->_reqData['user_data']['id']);
        $arrInput['page_no'] = $this->_reqData['pn'];
        $arrInput['page_size'] = $this->_reqData['limit'];

        $arrOutput = Tieba_Service::call('fstar', 'mgetMyActivityList', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        if(!isset($arrOutput['errno']) || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
        	Bingo_Log::warning('call getMyActivityList error input['.serialize($arrInput).'] output['.serialize($arrOutput).']');
        	$this->_resData['error']['errno'] = Tieba_Errcode::ERR_MO_MY_ACTIVITY_LIST;
            return false;
        }
        $this->_resData['data'] = $arrOutput['data']['activity_list'];
        $this->_resData['total_count'] = $arrOutput['data']['total_count'];
        return true;
    }

    protected function _transform(){
        if(!isset($this->_resData['data']) || empty($this->_resData['data'])){
            unset($this->_resData['data']);
        }
        return true;
    }
}