<?php
class Service_Antispam_Lib_Data{
	
	/**
	 * 通用验证码获取接口
	 * @param $intUserId
	 * @param $intType:户该出九宫格还是中文验证码的随机数,1表示中文验证码0表示九宫格
	 */
    public function getCaptcha($intUserId,$intType) {
    	$arrInput = array('req' =>array(
    		'user_id' => $intUserId,
    		'rulegroup' => array('ueg'),//表示用的策略组这里固 定在数组里面放一个'ueg'就行
    		'captcha_rand' => $intType,//用户该出九宫格还是中文验证码的随机数,=0的时候是中文验证码=1是九宫格
    		'cmd' => 'CaptchaTask',//表示操作，这里固定传'CaptchaTask'就行
    	));
        $arrInput['call_from'] = 'mos';
        $arrInput['group'] = 'antiGetCaptcha';
    	$arrRes = Molib_Tieba_Service::call('anti', 'antiGetCaptcha', $arrInput);		
		if(isset($arrRes['errno']) && $arrRes['errno'] == Tieba_Errcode::ERR_SUCCESS) {
			return $arrRes['res'];
		}else {
			$intErrno = isset($arrRes['errno']) ? $arrRes['errno'] : -1;
			Molib_Util_Log::apiwarning('pmc', 'antiActsctrlQuery', $intErrno, $arrInput);
		}	
		return false;
    }
    
	/**
	 * 通用验证码获取接口
	 * @param $intUserId
	 * @param $intType:户该出九宫格还是中文验证码的随机数,1表示中文验证码0表示九宫格
	 */
    public function checkCaptcha($intUserId,$intType,$strVcode,$strInput) {
    	$arrInput = array('req'=>array(
    		'user_id' => $intUserId,
    		'rulegroup' => array('ueg'),//表示用的策略组这里固 定在数组里面放一个'ueg'就行
    		'captcha_rand' => $intType,//用户该出九宫格还是中文验证码的随机数,=0的时候是中文验证码=1是九宫格
    		'cmd' => 'CaptchaTask',//表示操作，这里固定传'CaptchaTask'就行
    		'captcha_vcode_str' => $strVcode,
    		'captcha_input_str' => $strInput,
    	));
    	$arrRes = Molib_Tieba_Service::call('anti', 'antiCheckCaptcha', $arrInput);
		if(!isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			$intErrno = isset($arrRes['errno']) ? intval($arrRes['errno']) : -1;
			Molib_Util_Log::apiwarning('pmc', 'antiCheckCaptcha', $intErrno, $arrInput);
			return $intErrno;
		}
		return $arrRes['res']['captcha_err_no'] == 0 ? true : false;
    }
    /**
     * 验证码任务状态后续处理
     * 1 验证码任务成功： 直接返回，显示用户做任务的时间
     * 2 验证码任务进行中：返回下次等待输入的验证码
     * 3 验证码任务结束：标识是否有下一次机会
     * @param arr $arrTask 验证码任务数组
     * @param int $intSuccessCount
     * @param int $intUserId
     * @param string $strSource
     */
    public function delTaskRes($arrTask,$intSuccessCount,$intUserId,$strSource='client'){
    	if(is_array($arrTask) && $intUserId <= 0 ){
    		return false;
    	}
    	$arrRes = array(
    		'task' => $arrTask,
    	);
	if(empty($arrTask)){
		return $arrTask;
	}
    	//当前任务结束：成功
		if($arrTask['task_status'] == 1){			 
			$arrRes['task']['message'] = self::getMessage($arrTask['elapsed_sec']);//任务成功后，增加文案提示
			return $arrRes;
		}
		//当前任务结束：失败,需要再查询粒度控制，看用户是否有机会重试
		if($arrTask['task_status'] == 2){
			$isHit = self::antiActQuery($intUserId,$strSource);			
			$arrRes['has_chance'] = $isHit ? false : true;
			return $arrRes;
		}
		//当前任务进行中：需要查询验证码返回给前端
		if($arrTask['task_status'] == 0){		
			//计算出九宫格还是中文验证码
			$intType = rand(0,1);//随机
			$arrAnti = self::getCaptcha($intUserId,$intType);
			if($arrAnti != false){
				$arrRes['getcaptcha_res'] = $arrAnti;
				return $arrRes; 
			}			
		}
		return false;
    }
    
    //任务成功后文案提示
	public function getMessage($intSeconds){
		$strMessage = $intSeconds;
		switch ($intSeconds) {
			case $intSeconds <= 20:
				$strMessage .= '秒完成，打败99%的用户！';
				break;
			case $intSeconds > 20 && $intSeconds <= 40:
				$strMessage .= '秒完成，打败90%的用户！';
				break;
			case $intSeconds > 40 && $intSeconds <= 60:
				$strMessage .= '秒完成，打败80%的用户！';
				break;
			case $intSeconds > 60 && $intSeconds <= 80:
				$strMessage .= '秒完成，打败70%的用户！';
				break;
			case $intSeconds > 80 && $intSeconds <= 90:
				$strMessage .= '秒完成，打败60%的用户！';
				break;
			case $intSeconds > 90 && $intSeconds <= 120:
				$strMessage .= '秒完成，打败50%的用户！';
				break;
			case $intSeconds > 120:
				$strMessage = '这个速度也还行~加油';
				break;
		}
		return $strMessage;
	}

	//查询粒度控制
	//true标识命中，false标识未命中
	public function antiActQuery($intUserId,$strSource){
		$arrInput = array('req'=>array(
		 	'user_id' => $intUserId,
		 	'rulegroup' => array('ueg'),//表示用的策略组这里固 定在数组里面放一个'ueg'就行
		 	'source' => $strSource,
		 	'cmd' => 'CaptchaTask',//表示操作，这里固定传'CaptchaTask'就行
		 ),	
		 );
		$arrRes = Molib_Tieba_Service::call('anti', 'antiActsctrlQuery', $arrInput);
		// 返回结果$res['res']['err_no']>0表示命中，一般是34，但是肯能后面会加。但是没有命中肯定是0
		if(isset($arrRes['res']['err_no']) && $arrRes['res']['err_no']>0){
				return true;
		}		 
		return false;
	}	
	 
}
 
