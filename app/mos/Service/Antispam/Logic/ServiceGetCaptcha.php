<?php
/**
 * 
 * 验证码浏览接口
 *
 */
class Service_Antispam_Logic_ServiceGetCaptcha extends  Molib_Core_Logic {

	private $intUserId = 0;	 
	private $intCaptchaType = 0;//用来随机计算出九宫格还是中文验证码
	
	protected function _check() {
		$this->intUserId = intval($this->_reqData['user_id']);
		if($this->intUserId <= 0){
			$this->_error(Tieba_Errcode::ERR_PARAM_ERROR, $this->_reqData);
			return false;
		}		 
		$this->intCaptchaType = $this->_reqData['captcha_code_type'];			 
		return true;
	}

	protected function _execute() {
		//校验验证码
		$intType = $this->intCaptchaType == 4 ? 1 : 0;//=4是九宫格，service中=0的时候是中文验证码=1是九宫格
		$arrAnti = Service_Antispam_Lib_Data::getCaptcha($this->intUserId,$intType);
			if($arrAnti != false){
				$this->_resData['getcaptcha_res'] = $arrAnti;
				return true;
			}			
		return false;		
	}		
	protected function _transform() {
		return true;
	}
}
