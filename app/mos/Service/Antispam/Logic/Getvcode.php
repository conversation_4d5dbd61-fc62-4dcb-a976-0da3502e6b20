<?php
class Service_Antispam_Logic_Getvcode extends Molib_Core_Logic {
    protected function _check() {
        return true;
    }
    protected function _execute() {
        $arrOut = Tbapi_Core_Server::apicall('antispam', 'vcode', $this->_reqData);
        if (false === $arrOut) {
            $this->_error(Tieba_Errcode::ERR_MO_ANTISPAM_GETVCODE_FAIL,$this->_reqData);
            return false;
        }
        $this->_resData = $arrOut;
        return true;
    }
    protected function _transform() {
        
    }
}