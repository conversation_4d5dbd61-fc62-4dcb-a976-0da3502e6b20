<?php
class Service_Antispam_Logic_CheckSpamUrl extends Molib_Core_Logic{
	static public $URLREFER='&urlrefer=tieba1119';
    protected function _check() {
        if (empty($this->_reqData['url'])){
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID, $this->_reqData);
            return false;
        }
        return true;
    }
    protected function _execute() {
        $arrInput=array(
            'req'=>array(
                'topic'=>1,
                'url'=>$this->_reqData['url'],
                'op_name'=>'op',
                'op_id'=>123,
            ),
        );
    //У��checkurl
    $strUrl =$this->_reqData['url'];
    $strUrlRefer =$this->_reqData['urlrefer'];
    if(empty($strUrlRefer)){
    	$server_name = Bingo_Http_Request::getServer('HTTP_REFERER');
    	$client_version = $this->_reqData['_client_version'];
    	$is_tieba = strpos($server_name,'tieba.baidu.com');
    	if(empty($client_version)){
    		//���ܰ���߼�
    		if($is_tieba === false || $is_tieba > 10){
    			Bingo_Http_Response::redirect("http://static.tieba.baidu.com/tb/error.html?tc=02028667270713038346040313", true);
    			exit;
    		}
    	}else {
    		//�ȴ�ueg���м�ҳ�ڼ���ת
    		if($is_tieba === false && $client_version ==''){
    			Bingo_Http_Response::redirect("http://static.tieba.baidu.com/tb/error.html?tc=02028667270713038346040313", true);
    			exit;
    		}
    	}	
    }else {
    	$clientVersion =$this->_reqData['client_version'];
    	if(!empty($clientVersion)){
    		$bduss = Bingo_Http_Request::getCookie('BDUSS');
//     		if(empty($bduss)){
//     			Bingo_Http_Response::redirect("http://static.tieba.baidu.com/tb/error.html?tc=02028667270//713038346040313", true);
//     			exit;
//     		}
    		$stMdsMark =md5($bduss.$strUrl.self::$URLREFER);
    	}
    	if($stMdsMark != $strUrlRefer){
    		Bingo_Http_Response::redirect("http://static.tieba.baidu.com/tb/error.html?tc=02028667270713038346040313", true);
    		exit;
    	}
    }
    
        $this->_res = Tieba_Service::call('anti', 'checkSpamUrl', $arrInput);
        return true;
    }
    protected function _transform() {
        $this->_resData['mark'] = intval($this->_res['res']['mark']);
        return true;
    }
}
