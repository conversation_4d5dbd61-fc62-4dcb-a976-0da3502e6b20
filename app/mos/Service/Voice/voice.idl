//@description：语音贴获取相关接口
//@author:huanglilong

struct ReqGetThreadVoiceInfosByTids{
	uint32_t tids[]; // 查询的主题ID
};

struct VoiceInfo{
	uint32_t type;   // 语音类型  定义为10
    uint32_t durning_time; // 语音时间，毫秒为单位
    string  voice_md5;     // 语音md5
}

struct ResGetThreadVoiceInfosByTids{
    uint32_t errno;  // 错误号
    string errmsg;   // 错误信息
    VoiceInfo  voice_list[]; // 语音数据，以tid为键值
};

struct TidPid{
	uint32_t tid;  // 主题ID
	uint32_t pid;  // 帖子ID
};

struct ReqGetThreadVoiceInfosByPids{
	TidPid pids[]; // 帖子ID
};

struct ResGetThreadVoiceInfosByPids{
    uint32_t errno;  // 错误号
    string errmsg;   // 错误信息
    VoiceInfo  voice_list[]; // 语音数据，以pid为键值
};

struct VoiceStream{
    string voice_md5; // 语音md5
    string content; // 语音数据，返回的二进制内容
}

struct ReqGetVoiceStream{
	string voice_md5; // 语音md5
};

struct ResGetVoiceStream { // 
	uint32_t errno;  // 错误号
    string errmsg;   // 错误信息
    VoiceStream voice;   // 如果没有则为空
};

service voice {
    
    //@url:
    //@description: frs 查询语音信息接口
    //@notice: 
    void get_thread_voice_infos_by_tids(ReqGetThreadVoiceInfosByTids req, ResGetThreadVoiceInfosByTids res);
    
    //@url:
    //@description: pb 查询语音信息接口
    //@notice: 
    void get_thread_voice_infos_by_tids(ReqGetThreadVoiceInfosByPids req, ResGetThreadVoiceInfosByPids res);
    
    //@url:
    //@description: 查询语音内容接口
    //@notice: 
    void get_voice_stream(ReqGetVoiceStream req, ResGetVoiceStream res);
    
};