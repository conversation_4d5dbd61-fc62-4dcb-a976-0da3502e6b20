<?php
class Service_Message_Logic_Check extends Molib_Core_Logic {
    private $_arrOut;
    protected function _check() {
        return true;
    }
    protected function _execute() {
        $arrRequest = array(
                'limit' => 1,
                'os'	=> 'ios',
                );
        $this->_arrOut = Tbapi_Core_Server::apicall('ucommon','get_new_cmessage',$arrRequest,0);
        if ($this->_arrOut === false) {
            Molib_Util_Log::apiwarning('ucommon','get_new_cmessage', false, $arrRequest);
            $this->_error(Tieba_Errcode::ERR_UNKOWN);
            return false;
        }
        return true;      
    }
    protected function _transform() {
        $this->_resData['msg'] = $this->_arrOut['msg'];
        return true;
    }
}
