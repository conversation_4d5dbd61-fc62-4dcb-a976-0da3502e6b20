<?php
//直播贴 嘉宾讨论区和吧友讨论区
//liurihui
//默认30条 逆序
class Service_Livetopic_Logic_Livepost extends Molib_Core_Logic {
    const LIVE_UI_CACHE_PREFIX = 'mos_livetopic_livepost_';
    const EXPIRE_TIME = 10;
    private $intThreadId = 0;
    private $intPostId = 0; //主楼id
    private $intForumId  = 0;
    private $strForumName = '';
    private $strPostType  = '';
    private $intRn = 0;
    private $intTaskId = 0;
    //返回数据
    private $intIsLivePost = 0;
    private $strLivePostType = '';
    private $arrTalkPostList = array();
    private $arrUserPost = array();
    private $arrTaskInfo = array();

    
    protected function _check() {
        $this->intThreadId = intval($this->_reqData['thread_id']);
        $this->intForumId  = intval($this->_reqData['forum_id']);
        $this->strForumName = strval($this->_reqData['forum_name']);
        $this->strPostType  = strval($this->_reqData['post_type']);
        $this->intRn = intval($this->_reqData['rn']);
        $this->intTaskId = intval($this->_reqData['task_id']);
        $this->intUid = intval ( Tieba_Session_Socket::getLoginUid () );
    }
    protected function _execute() {
        //cache
        $strCachekey = $this->_getUiCacheKey();
        $this->arrCacheRes = Service_Livetopic_Libs_Util_Memcache::getCache($strCachekey);
        if ($this->arrCacheRes) {
            return true;
        }

        $this->arrTaskInfo = $arrTaskInfo = $this->_getLiveTaskInfo();
        if (empty($arrTaskInfo)) {
            Bingo_Log::warning("the live task is not exist");
            return false;
        }
        $intThreadType = intval($arrTaskInfo['type']);
        if((Service_Livetopic_Libs_Util_Const::LIVE_INTERVIEW_TEXT != $intThreadType) && (Service_Livetopic_Libs_Util_Const::LIVE_INTERVIEW_VEDIO != $intThreadType) && (Service_Livetopic_Libs_Util_Const::LIVE_TOPIC_TEXT != $intThreadType)){
            return false;
        }
        $this->intIsLivePost = 1;
        //访谈直播贴
        if(Service_Livetopic_Libs_Util_Const::LIVE_INTERVIEW_TEXT == $intThreadType || Service_Livetopic_Libs_Util_Const::LIVE_INTERVIEW_VEDIO == $intThreadType){            
            $this->strLivePostType = Service_Livetopic_Libs_Util_Const::INTERVIEW_THREAD;
            $ret = $this->_getLiveInterviewList($arrTaskInfo, Service_Livetopic_Libs_Util_Const::$arrDiscussReplacement, false);
            if(false === $ret){
                return false;
            }
        }
        //文字话题
        else if(Service_Livetopic_Libs_Util_Const::LIVE_TOPIC_TEXT == $intThreadType) {
            $this->strLivePostType = Service_Livetopic_Libs_Util_Const::TOPIC_TEXT_THREAD;
            $ret = $this->_getUserPost();
            if(false === $ret){
                return false;
            }                   
        }

        return true;                
    }
    protected function _transform() {
        if ($this->arrCacheRes) {
            $this->_reqData = unserialize($this->arrCacheRes);
        }
        else {
            $this->_resData['is_live_post'] = $this->intIsLivePost;
            $this->_resData['live_post_type'] = $this->strLivePostType;
            $this->_resData['talk_post_list'] = $this->arrTalkPostList;
            $this->_resData['user_post'] = $this->arrUserPost;
            $this->_resData['task_info'] = $this->arrTaskInfo; 
            $this->_setNginxCache($this->_resData);
            $this->_setUiCache($this->_resData);
        }
    }
    // 获取访谈直播的信息
    private function _getLiveTaskInfo() {
        $arrOut = array();
        $arrInput = array(
            'id' => $this->intTaskId,
        );

        $arrRes = Tieba_Service::call('live', 'getLiveTaskInfo', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        if (fasle === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning("call live getLiveTaskInfoByTid fail input[" . serialize($arrInput) . "]");
            return $arrOut;
        }
        if (!empty($arrRes['data'])) {
            $arrOut = $arrRes['data'];
        }
        return $arrOut;
    }

    private function _getLiveInterviewList($arrTaskInfo,$arrInterviewReplacement = array(), $bolMedia = false) {
        $arrInterviewInfo = array();
        $arrInterviewTopPostInfo = array();
        $arrComperes = $arrTaskInfo['content']['comperes'];
        $arrGuests = $arrTaskInfo['content']['guests'];
        $intTaskId = intval($arrTaskInfo['id']);
        $this->intPostId = $arrTaskInfo['content']['post_id'];
        $this->_isCompere = Service_Livetopic_Libs_Util_Live::isCompere($this->intUid, $arrComperes);
        $this->_isGuest = Service_Livetopic_Libs_Util_Live::isGuest($this->intUid, $arrGuests);
        //获取访谈问答模块内容
        $bolIsSpecial = $this->_isCompere || $this->_isGuest;
        $arrInput = array(
            'task_id' => $intTaskId,
            'thread_id' => $this->intThreadId,
            'bolIsSpecial' => true,
            'order_id' => 0,
            'direction' => -1,
            'rn' => 30,
        );
    
        $arrOutput = Tieba_Service::call('live', 'getLiveInterviewInfoByTidAndTaskId', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
    
        if ($arrOutput === false || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call service {getLiveInterviewInfoByTidAndTaskId} error. output = [" . serialize($arrOutput) . "]");
            return false;
        }

        $arrInterviewInfo = $arrOutput['data'];
        if (!empty($arrInterviewInfo)) {
            $arrInterviewInfo = array_reverse($arrInterviewInfo);
            $arrUids = Service_Livetopic_Libs_Util_Live::getUids(array(), $arrInterviewInfo);
            $arrItbInfo = Service_Livetopic_Libs_Util_Live::getItbInfo($arrUids);
            $arrInterviewPostsInfo = Service_Livetopic_Libs_Util_Live::buildInterviewQA($arrInterviewInfo, $arrItbInfo, $arrComperes, $arrGuests,$arrInterviewReplacement,$bolMedia);
            $this->arrTalkPostList = $arrInterviewPostsInfo;
        }

        //postlist理论上pb应该已有数据，这里为了兼容q版再取一次。智能版迁移的时候应该不用再取
        //但frs可以配置别的吧的贴子为直播贴，所以必须取
        $this->_getUserPost();
        return true;
    }

    private function _getUserPost() {
        $input = array(
            "thread_id" => $this->intThreadId, //帖子id
            "offset" => 0,
            "res_num" => 30,
            "see_author" => 0,
            "has_comment" => 1,
            "has_mask" => 1,
            "has_ext" => 0,
            "need_set_pv" => 0,
            "structured_content" => 0,
        );  
        $res   = Tieba_Service::call('post', 'getPostsByThreadId', $input, NULL, NULL, 'post', 'php', 'utf-8');
        if ($res === false) {
            $this->_error(Tieba_Errcode::ERR_MO_INTERNAL_ERROR);
            Bingo_Log::warning("call service {getPostsByThreadId} error. output = [" . serialize($res) . "]");
            return false;
        }
        $arrPostListInfo = $res['output']['output'][0];
        $arrOut = array();
        if (!empty($arrPostListInfo)) {
            $intReplyNum = $arrPostListInfo['valid_post_num'];
            $arrPostsInfo = $arrPostListInfo['post_infos'];
            $arrUids = Service_Livetopic_Libs_Util_Live::getUids($arrPostsInfo, array());
            $arrItbInfo = Service_Livetopic_Libs_Util_Live::getItbInfo($arrUids);
            $arrOut['first_post'] = $arrPostsInfo[0];
            $arrOut['first_post']['user'] = Service_Livetopic_Libs_Util_Live::buildPostUser($arrPostsInfo[0], $arrItbInfo);
            // $arrPostsInfo = array_reverse($arrPostsInfo);
            $arrPostsInfo = Service_Livetopic_Libs_Util_Live::filterPosts($arrPostsInfo, $this->intPostId);
            $arrOut['post_list'] = Service_Livetopic_Libs_Util_Live::buildPosts($arrPostsInfo, $arrItbInfo, Service_Livetopic_Libs_Util_Const::$arrDiscussReplacement);
            $arrOut['reply_num'] = $intReplyNum;
        }

        $this->arrUserPost = $arrOut;
        return true;   
    }

    protected  function _setNginxCache($arrResult) {
        $arrResult = Bingo_String::array2json($arrResult);
        $hostName = $_SERVER['x_bd_routerip'];
        $strKey = $hostName . ":" . "livepost:".$this->intThreadId . ":" . $this->intForumId . ":" . $this->strForumName . 
            ":" . $this->strPostType . ":" . $this->intRn . ":" . $this->intTaskId;
        $arrRes = Service_Livetopic_Libs_Util_Cache::addCache($strKey, $arrResult);
        if($arrRes === false) {
            Bingo_Log::warning("add nginx cache error with livepost list with[task_id =" . $this->_intTaskId ."]");
        }
        return true;
    }

    protected function  _setUiCache($arrResult) {
        $strKey = $this->_getUiCacheKey();
        $addRes = Service_Livetopic_Libs_Util_Memcache::addCache($strKey, serialize($arrResult), self::EXPIRE_TIME);

        if (false === $addRes) {
            Bingo_Log::warning('add live livepost list cache fail param:' . serialize($arrResult) . ' out:' . serialize($addRes));
        }
        return true;
    }

    protected function _getUiCacheKey() {
        $strKey = self::LIVE_UI_CACHE_PREFIX.$this->intThreadId . "_" . $this->intForumId . "_" . $this->strForumName . 
            "_" . $this->strPostType . "_" . $this->intRn . "_" . $this->intTaskId;
        return $strKey;
    }
}