<?php
//新年抽奖
class Service_Act_Logic_SetTenthYearIcon extends Molib_Core_Logic {
	
	 
	private $_arrData = array();
	const RECOMD_FORUM_NUM = 8;
	
	public function _check() {
		$this->_intUid = $this->_reqData['user_id'];
	 	if ($this->_intUid <= 0) {
            $this->_error(Tieba_Errcode::ERR_MO_USER_NOT_LOGIN);
            return false;
        }        
    	return true; 
    }
	
     protected function _execute()  {
     $arrInput = array(
	     'user_id'    => $this->_intUid,
	     'attr_name'  => 'is_tenyear',
	     'attr_value' => 2,
	     );
     $arrOut = Tieba_Service::call('user', 'setUserAttr', $arrInput);
      if (false === $arrOut) {
            Bingo_Log::warning("Tieba_Service_call_user_setUserAttr_false");
            Tieba_Stlog::addNode("set_user_icon", 0);
            return false;
        }elseif($arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
        	Bingo_Log::warning("Tieba_Service_call_user_gsetUserAttr_errro_no_not_zero");
        	Tieba_Stlog::addNode("set_user_icon", 0);
        	$this->_error($arrOut['errno'], $arrInput);
            return false;
        }
        Tieba_Stlog::addNode("set_user_icon", 1);
     }
     protected function _transform() {
     	  $this->_resData['error']['errno'] = 0;
     	  $this->_resData['error']['errmsg'] = 'success';
     	  $this->_resData['data'] = array('success');     	 	
     	 
     } 
}

?>