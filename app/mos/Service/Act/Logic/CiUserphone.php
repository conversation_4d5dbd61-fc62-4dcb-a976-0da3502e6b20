<?php
//新年抽奖
class Service_Act_Logic_CiUserphone extends Molib_Core_Logic {
	
	private $_strUname = '';
	private $_strPhone;
	private $_intUid;
	private $_arrPhoneRes = array();
	
	public function _check() {
		$this->_strTbs = $this->_reqData['tbs'];
        if ('' === $this->_strTbs) {
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID);
            Bingo_Log::warning('commit tbs is empty');
            return false;
        }    	
        $this->_intUid = $this->_reqData['user_id'];
	 	if ($this->_intUid <= 0) {
            $this->_error(Tieba_Errcode::ERR_MO_USER_NOT_LOGIN);
            return false;
        }    	 
    	$this->_strPhone = $this->_reqData['phone'];
        if ('' === $this->_strPhone) {
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID);
            Bingo_Log::warning('commit phone is empty');
            return false;
        }    	
    	return true;
    }
	
     protected function _execute()  {
     	 $this->_ciUserInfo();
     }
     protected function _transform() {
     	$this->_resData['error']['errno'] = $this->_arrPhoneRes['no'];
        $this->_resData['error']['errmsg'] = $this->_arrPhoneRes['error'];
     	$this->_resData['data'] = $this->_arrPhoneRes['data'];
     }
     
     private function _ciUserInfo() {
     	 $arrData = array(
                'phone' => $this->_strPhone,   
     	 		'tbs' => $this->_strTbs,                 
          	);
        $strUrl = '/app/choujiang/commit/updatewinnerinfo';
        $strOut = Tbapi_Core_Midl_Http::httpcall('client_choujiang',$strUrl,$arrData,'post');
        if ($strOut === false){
        	Bingo_Log::warning('Tbapi_Core_Midl_Http::httpcall client_choujiang fail'.serialize($arrData));
        	$this->_error(Tieba_Errcode::ERR_UNKOWN);
        	$this->_arrPhoneRes['data'] = array();
            return false;
        }
 
        $arrOut = Bingo_String::json2array($strOut,Bingo_Encode::ENCODE_UTF8);
        if (empty($arrOut) || !is_array($arrOut)){
            Bingo_Log::warning('json to array error');
            $this->_error(Tieba_Errcode::ERR_UNKOWN);
            $this->_arrPhoneRes['data'] = array();
            return false;
        }
        $this->_arrPhoneRes = $arrOut;
 
        return true;
     	
     }
     
}

?>