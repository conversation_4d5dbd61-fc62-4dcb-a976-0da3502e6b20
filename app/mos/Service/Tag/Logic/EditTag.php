<?php
require_once 'Molib/Core/Logic.php';
class Service_Tag_Logic_EditTag extends Molib_Core_Logic {
    protected function _check() {
    }

    protected function _execute() {
        $arrAddTag = array_filter(explode(',', $this->_reqData['add_tag_id_list']));
        $arrDelTag = array_filter(explode(',', $this->_reqData['del_tag_id_list']));

        if (count($arrAddTag)) {
            $this->_reqData['tag_info'] = implode(',', $arrAddTag);
            $arrResponse = Tbapi_Core_Server::mocall('tag', 'add_tag', $this->_reqData);
        }
        if (0 !== $arrResponse['error']['errno']) {
            $this->_error($arrResponse['error']['errno']);
        } else if (null === $arrResponse['error']['errno']) {
            $this->_error(Tieba_Errcode::ERR_MO_TAG_ADD_TAG_ERROR);
        }
        if (count($arrDelTag)) {
            $this->_reqData['tag_info'] = implode(',', $arrDelTag);
            $arrResponse = Tbapi_Core_Server::mocall('tag', 'del_tag', $this->_reqData);
        }
        if (0 !== $arrResponse['error']['errno']) {
            $this->_error($arrResponse['error']['errno']);
        } else if (null === $arrResponse['error']['errno']) {
            $this->_error(Tieba_Errcode::ERR_MO_TAG_DEL_TAG_ERROR);
        }
    }
    protected function _transform() {
    }
}
