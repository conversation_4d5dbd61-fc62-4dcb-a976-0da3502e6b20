<?php
require_once 'Molib/Core/Logic.php';
class Service_Tag_Logic_UserTag extends Molib_Core_Logic {
	protected function _check() {
    }
    protected function _execute() {
        $arrRequest = array(
                'user_id' => Tieba_Ucrypt::decode($this->_reqData['user_data']['portrait']),
                'offset' => 0,
                'limit' => 100,
                );  
        $this->_arrResponse = Tieba_Service::call('consume', 'getUserTag', $arrRequest,NULL,NULL,'post','php','utf-8');
        if (!is_array($this->_arrResponse)) {
            Molib_Util_Log::apiwarning('tag_http', 'usertag', $this->_arrResponse, $arrRequest);
            $this->_error(Tieba_Errcode::ERR_MO_INTERNAL_ERROR);
            return false;
        }   
        if (0 !== $this->_arrResponse['errno']) {
            Molib_Util_Log::apiwarning('tag_http', 'usertag', $this->_arrResponse, $arrRequest);
            $this->_error(Tieba_Errcode::ERR_MO_INTERNAL_ERROR);
            return false;
        } 
    }
    protected function _transform() {
        $intKey = 0;
        $this->_resData['data']['user_tag'] = array();
        foreach ($this->_arrResponse['ret']['user_tag_infos'] as $arrItem) {
            $this->_resData['data']['user_tag'][$intKey]['tag_name'] = $arrItem['tag_name'];
            //$this->_resData['data']['user_tag'][$strKey]['color'] = $arrItem['color'];
            $this->_resData['data']['user_tag'][$intKey]['tag_id'] = $arrItem['tag_id'];
            $intKey++;
        }
    }
}
