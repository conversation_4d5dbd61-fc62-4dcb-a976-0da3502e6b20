/**
 * @file 编译脚本
 * <AUTHOR>
 * @since 2014-6-10
 */

// var deployHost = 'olympic.tieba.otp.baidu.com';
var deployHost = 'xss.tieba.otp.baidu.com';

var deployPath = '/home/<USER>/orp/app/mos/Service/Frs';


deployHost = process.env.HOST || deployHost;
var hostPrefix = deployHost.replace('.tieba.otp.baidu.com', '');

var deployServ = 'http://' + hostPrefix + '.fis.tieba.otp.baidu.com/fis/reciever';
// var deployServ = 'http://' + deployHost + ':8099/fis/reciever';
var deployServ = 'http://xss.fis.tieba.otp.baidu.com/fis/reciever';

console.info('deploy host: ' + deployHost);

fis.config.merge({
    deploy: {
        remote: [
            {
                receiver: deployServ,
                from: '/',
                to: deployPath
                // replace: replaceAddrConf
            }
        ]
    },
    roadmap: {
        path: [
            {
                reg: /\.*\.json$/i,
                release: false
            }
        ]
    }
});

fis.config.set('project.charset', 'utf-8');

fis.config.set('project.exclude', /^\/(output|.*\.(sh|js|json|bat))/i);

var replaceAddrConf = {
    from: /(tieba\.baidu\.com)|(tb[12]\.bdstatic\.com)|(static\.tieba\.baidu\.com)/g,
    to: function (m, $1, $2, $3, $4) {
        if ($1) {
            return 'fe8.tieba.baidu.com';
        } else if ($2 || $3) {
            return 'static.fe8.tieba.baidu.com';
        }
    }
};
