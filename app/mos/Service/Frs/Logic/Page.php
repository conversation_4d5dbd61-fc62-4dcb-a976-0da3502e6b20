<?php
/**
 * Service_Frs_Logic_Page 
 * 
 * @package 
 * @version $Id: 2012-12-25$
 * 参照版本号: 120165
 * @copyright Copyright 2010-2012 baidu.com
 * <AUTHOR> <<EMAIL>> 
 * @license 
 */
class Service_Frs_Logic_Page extends Molib_Core_Logic
{
    private $_strForumName = '';
    private $_intOffset = null;
    private $_intRn = 0;
    private $_intRnNeed = 0;
    private $_showForumTag = false;
    private $_showBawuPower = false;
    private $_showForumEx  = false;
    private $_proc_post_prefix = false;
    private $_bolIsFilterPic = false;

    private $_intWithCal = 0;
    private $_arrFrsPage = array();
    private $_arrAnti;
    private $_arrThreadList;
    private $_arrThreadIdList = array();
    private $_arrUser;
    private $_arrForum;
    private $_arrFrsLivePost = array();
    private $_arrFrsStar = array();
    private $_arrTagInfo = array();

    const FORUM_IS_READABLE = 0;
    const FORUM_IS_NOT_EXISTS = 1;
    const FORUM_IS_FORBIDDEN = 2;
    const COMMENT_THRESHOLD = 100;
    const TEXT_LIVE = 'normal';  //文字直播
    const TEXT_TOPIC = 'text';   //文字话题
    const INTERVIEW_LIVE = 'interview'; //访谈直播
    const SHARE_INNER_THREAD = 8; //吧内转贴
    const PIC_POST_THREAD = 1; //图册精选

    public function _check()
    {
        $this->_strForumName = Molib_Util_Encode::convertGBKToUTF8(
                trim($this->_reqData['forum_name']));
        if ('' === $this->_strForumName) {
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID,$this->_reqData);
            return false;
        }
        $this->_intOffset = intval($this->_reqData['offset']);
        if ($this->_intOffset < 0) {
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID,$this->_reqData);
            return false;
        }
        $this->_intRn = intval($this->_reqData['rn']);
        if ($this->_intRn < 0) {
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID,$this->_reqData);
            return false;
        }
        $this->_intRnNeed = intval($this->_reqData['rn_need']);
        if ($this->_intRnNeed < 0) {
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID,$this->_reqData);
            return false;
        }
        $this->_intIsGood = intval($this->_reqData['is_good']);
        if ($this->_intIsGood < 0) {
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID,$this->_reqData);
            return false;
        }
        if (1 === $this->_intIsGood) {
            $this->_intClassId = intval($this->_reqData['class_id']);
            if ($this->_intClassId < 0) {
                $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID,$this->_reqData);
                return false;
            }
        } else {
            $this->_intClassId = 0;
        }
        $this->_bolIsProcRichText = is_bool($this->_reqData['is_proc_richtext']) ? $this->_reqData['is_proc_richtext'] : false;        
        $this->_bolIsFilterMedia = is_bool($this->_reqData['is_filter_media']) ? $this->_reqData['is_filter_media'] : false;        
        $this->_bolIsProcLive = is_bool($this->_reqData['is_proc_live']) ? $this->_reqData['is_proc_live'] : false;        
        $this->_showForumTag = is_bool($this->_reqData['show_forum_tag']) ? $this->_reqData['show_forum_tag'] : false;        
        $this->_showBawuPower = is_bool($this->_reqData['show_bawu_power']) ? $this->_reqData['show_bawu_power'] : false;      
        $this->_bolIsShowRepost = is_bool($this->_reqData['is_show_repost']) ? $this->_reqData['is_show_repost'] : false;
        
        $this->_showForumEx = $this->_reqData['show_forum_ex'];
        $this->_proc_post_prefix = $this->_reqData['proc_post_prefix'];
        $this->_intWithCal = ( 0 < intval($this->_reqData['with_cal']) ) ? 1 : 0;
        $this->_bolIsFilterPic = is_bool($this->_reqData['is_filter_pic']) ? $this->_reqData['is_filter_pic'] : false;            
        return true;
    }
    protected function _execute()
    {
        //请求主题list
        $this->_getFrsPage();
        if (self::FORUM_IS_READABLE !== $this->_checkForum()) {
            Bingo_Log::debug("[logic:frs][method:_execute()][self::FORUM_IS_READABLE !== _checkForum()]");
            return true;
        }
        //处理签到数据
        $this->_procSignInfo();
        //获取用户角色，是否是吧主
        if ($this->_showBawuPower) {
            $this->_getUserRole();
        }
        if ($this->_showForumEx) {
            $this->_getForumInfoEx();
        }
        //处理直播贴
        if ($this->_bolIsProcLive) {
            $this->_getFrsLivePost();
        }
        //过滤投票贴、吧刊贴、图册贴
        if (true === $this->_bolIsFilterMedia) {
            $this->_filterMedia();
        }
        //过滤吧内转贴 
        if (false === $this->_bolIsShowRepost) {
        	$this->_filterRepost();
        }
        //过滤图片精选的贴子
    	if (true === $this->_bolIsFilterPic) {
        	$this->_filterPicPost();
        }
        //摘要富文本处理
        if (true === $this->_bolIsProcRichText) {
            $this->_procRichText();
        }
        //明星垂直化
        $this->_procFrsStar();
        //获取吧标签
        if (true === $this->_showForumTag) {
            $this->_getForumTagInfo();
        }
        return true;
    }
    protected function _transform()
    {
        $intForumStatus = $this->_checkForum();
        //吧存在且不禁止
        if (self::FORUM_IS_READABLE === $intForumStatus) {
            if($this->_proc_post_prefix && isset($this->_arrForum['post_prefix']) && $this->_arrForum['post_prefix']['mode'] == 1) {
                //发贴前缀复杂模式，替换text中的#time#
                $arrPostPreFix = $this->_arrForum['post_prefix'];
                if(strstr($arrPostPreFix['text'], '#time#')) {
                    $strTime = strftime($arrPostPreFix['time'], time());
                    $arrPostPreFix['text'] = str_replace('#time#', $strTime, $arrPostPreFix['text']);
                }
                $this->_arrForum['post_prefix'] = $arrPostPreFix;
            }
            $this->_resData['forum'] = Molib_Util_Array::fetchArray($this->_arrForum, array('id', 'name', 'first_class','second_class','is_exists', 'is_like', 'user_level', 'level_name', 'good_classify', 'member_num', 'thread_num', 'post_num', 'is_readonly','has_frs_star', 'album_open_photo_frs', 'cur_score', 'levelup_score','has_postpre','post_prefix', 'managers','zhibo','forum_sign_calendar','avatar','slogan','tids'));
            $this->_resData['forum']['level_id'] = $this->_resData['forum']['user_level'];
            $this->_resData['forum']['sign_in_info'] = $this->_arrForum['sign_in_info'];
            $this->_resData['forum']['tag_info'] = $this->_arrTagInfo;
            $arrGoodClassify = $this->_resData['forum']['good_classify'];
            if (!is_array($arrGoodClassify)) {
                $arrGoodClassify = array();
            }
            foreach ($arrGoodClassify as &$arrTmp) {
                $arrTmp['class_name'] = htmlspecialchars_decode($arrTmp['class_name']);
            }
            $this->_resData['forum']['good_classify'] = $arrGoodClassify;
            $this->_resData['frs_star'] = $this->_arrFrsStar;
            
        } //吧不存在
        else if (self::FORUM_IS_NOT_EXISTS === $intForumStatus) {
            $this->_resData['forum']['is_exists'] = false;
            $this->_resData['forum']['is_forbid'] = false;
            Bingo_Log::warning("fname {$this->_strForumName} is not exists");
            $this->_error(Tieba_Errcode::ERR_MO_FORUM_NOT_EXIST);
            return true;
            
        } //吧禁止访问
        else {
            $this->_resData['forum']['is_exists'] = false;
            $this->_resData['forum']['is_forbid'] = true;
			$this->_resData['user'] = Molib_Util_Format::formatUser(Molib_Util_Array::fetchArray($this->_arrUser, array ('is_login', 'id', 'name', 'name_show', 'portrait', 'type')));
            Bingo_Log::warning("fname {$this->_strForumName} is forbidden");
            $this->_error(Tieba_Errcode::ERR_MO_FORUM_IS_FORBIDDEN);
            return true;
        }
        $this->_arrUser['balv'] = Molib_Util_Array::fetchArray($this->_arrUser['balv'], array('is_black','is_block',days_tofree));
        $this->_resData['user'] = Molib_Util_Format::formatUser(Molib_Util_Array::fetchArray($this->_arrUser, array ('is_login', 'id', 'name', 'name_show', 'portrait', 'type','userhide', 'balv', 'rank','no_un', 'is_manager')));
        $this->_resData['forum']['name'] = htmlspecialchars_decode($this->_resData['forum']['name'], ENT_QUOTES);
        $this->_resData['thread_list'] = $this->_arrThreadList;
        $this->_resData['thread_id_list'] = $this->_arrThreadIdList;
        $this->_resData['anti'] = $this->_arrAnti;
        $this->_resData['anti']['forbid_info'] = Molib_Util_ForbidInfo::getForbidInfo($this->_resData['anti']['forbid_flag']);
        $this->_resData['page'] = $this->_arrPage;
    }
    private function _getFrsPage()
    {
        $arrRequest = array('kw' => $this->_strForumName, 'offset' => $this->_intOffset, 'rn' => $this->_intRn,
                'is_good' => $this->_intIsGood, 'cid' => $this->_intClassId);
        $arrOut = Tbapi_Core_Server::apicall('frs', 'frs_page', $arrRequest, 0);
        if ($arrOut === false) {
            Molib_Util_Log::apiwarning('frs', 'frs_page', false, $arrRequest);
            $this->_error(Tieba_Errcode::ERR_MO_FRSPAGE_FAIL);
            return false;
        }
        $this->_arrFrsPage = $arrOut;
        $this->_arrUser = $arrOut['user'];
        $this->_arrForum = $arrOut['forum'];
        $this->_arrThreadList = $arrOut['thread_list'];
        $this->_arrAnti = $arrOut['anti'];
        $this->_arrPage = $arrOut['page'];
        
        //截取上拉加载部分的tid，在设置了rn_need并且返回条数大于展示条数的情况下
        if ($this->_intRnNeed > 0 && count($this->_arrThreadList) > $this->_intRnNeed) {
            $arrLeft = array_slice($this->_arrThreadList, $this->_intRnNeed);
            foreach ($arrLeft as $arrThread) {
                $this->_arrThreadIdList[] = $arrThread['id'];
            }
            $this->_arrThreadList = array_slice($this->_arrThreadList, 0, $this->_intRnNeed);
        }
        $arrTLite = $this->_arrThreadList;
        foreach ($arrTLite as $thread){
        	$strtids .= $thread['thread_id'].',';
        }
        $this->_arrForum['tids'] = $strtids;
    }

    private function _getFrsLivePost() {
        //该吧有直播贴且开放
        if (false === is_array($this->_arrForum['zhibo'])) {
            return false;
        }
        if((1 == $this->_arrForum['zhibo']['has_lpost']) && (1 == $this->_arrForum['zhibo']['lpost_type'])){
    	    $bolTextLive = true; //文字直播      
    	}
    	elseif(((0 == $this->_arrForum['zhibo']['has_lpost']) && (1 == $this->_arrForum['zhibo']['type']))) {
    	    $bolTextTopic = true;//文字话题
    	}
    	elseif((1 == $this->_arrForum['zhibo']['has_lpost']) && (2 == $this->_arrForum['zhibo']['lpost_type'])) {
    	    $bolInterviewLive = true; //访谈直播
    	}
    	else {
    	    return false;  //不是直播贴
    	}
    	if($bolTextLive || $bolTextTopic){
        	//文字话题直播贴       	
	        $arrInput = array(
    	        'kw'  => $this->_arrForum['name'],
    	        'num' => 1,
    	        'tid' => $this->_arrForum['zhibo']['tid'],
	        );	        	
	       	$arrOut = Tbapi_Core_Server::apicall('frs','get_live_topic',$arrInput,0);		
	       	if (false === $arrOut || !isset($arrOut['info']['thread'])){
	       	    Molib_Util_Log::apiwarning('frs', 'get_live_topic', false, $arrInput);
	       	    return false;         
	     	}	       	      		
            if($bolTextLive){ 		   
            	$arrOut['info']['thread']['live_post_type'] = self::TEXT_LIVE;     //文字直播贴
            }
            else{  
            	$arrOut['info']['thread']['live_post_type'] = self::TEXT_TOPIC;  //文字话题贴
            }	       		        
        }
        //访谈直播贴
        elseif($bolInterviewLive) {
        	$arrInput = array(
        	     'fid' => $this->_arrForum['id'],
        	);
        	$arrOut = Tbapi_Core_Server::apicall('frs','get_talk_forum',$arrInput,0);
            if (false === $arrOut || !isset($arrOut['info']['thread'])) {
	       	    Molib_Util_Log::apiwarning('frs', 'get_talk_forum', false, $arrInput);
	       	    return false;
	     	}	     
            $arrOut['info']['thread']['live_post_type'] = self::INTERVIEW_LIVE; 	     	
        } 
        
        $arrOut['info']['thread']['is_livepost'] = 1;
		//如果该吧是直播贴落地的吧，同时也配置了直播贴的显示，则需要从主题列表中去掉直播贴       		
        foreach ($this->_arrThreadList as $key => $value){         
        	if ($value['id'] == $arrOut['info']['thread']['id']){
        		array_splice($this->_arrThreadList, $key, 1);
        		break;
        	}		            			
        } 
        //与frs页做merge  
        $arrLiveThread = array();
		$arrLiveThread []= $arrOut['info']['thread'];     	
        $this->_arrThreadList = array_merge($arrLiveThread, $this->_arrThreadList);         
        return true;
    }
    private function _checkForum()
    {
        if (isset($this->_arrFrsPage['forum']) && $this->_arrFrsPage['forum']['is_exists'] === true) {
            return self::FORUM_IS_READABLE;
        }
        if (isset($this->_arrFrsPage['forum']['is_forbidden']) && $this->_arrFrsPage['forum']['is_forbidden'] == true) {
            //forum forbidden
            return self::FORUM_IS_FORBIDDEN;
        } else {
            //forum not exists
            return self::FORUM_IS_NOT_EXISTS;
        }
    }
    private function _filterMedia()
    {
        foreach ($this->_arrThreadList as $intKey => $arrThread) {
            if ($arrThread['is_vote'] || $arrThread['is_bakan'] || $arrThread['is_protal']) {
                unset($this->_arrThreadList[$intKey]);
            }
        }
    }
    private function _filterRepost()
    {
    	foreach ($this->_arrThreadList as $intKey => $arrThread){
    		if(isset($arrThread['thread_types'])){
    			$thread_types = intval($arrThread['thread_types']);
    			$other_types = $thread_types >> 32;
    			if(self::SHARE_INNER_THREAD === $other_types){
    				unset($this->_arrThreadList[$intKey]);
    			}
    		}
    	}
    }
    private function _procRichText()
    {
        $arrThreadList = array ();
        $intCommentThreshold = self::COMMENT_THRESHOLD;
        foreach ($this->_arrThreadList as $arrThread) {
            /**过滤非贴子的公告贴**/
            if ($this->_reqData['filter_forum_notice']) {
                if ($arrThread['is_notice'] == 1 && !is_numeric($arrThread['id'])){
                    continue;
                } 
            }
            /** frs摘要信息 */
            $arrAllMedia = &$arrThread['media'];
            if (!empty($arrAllMedia)) {
                foreach ($arrAllMedia as &$arrMedia) {
                    if ($arrMedia['type'] == 'pic') {
                        $arrMedia['type'] = Molib_Util_RichTextParser::SLOT_TYPE_IMG;
                    }else if ($arrMedia['type'] == 'abstract'){
                        $arrMedia['type'] = Molib_Util_RichTextParser::SLOT_TYPE_TEXT;
                    }else if(($arrMedia['type'] == 'flash')){
                        $arrMedia['type'] = Molib_Util_RichTextParser::SLOT_TYPE_EMBED;
                    }else if(($arrMedia['type'] == 'music')){
                        $arrMedia['type'] = Molib_Util_RichTextParser::SLOT_TYPE_MUSIC;
                    }
                }
            }else {
                $arrAllMedia = array();
            }
            $parser = new Molib_Util_RichTextParser();
            $srtAbstract = $arrThread['abstract'];
            $condition = new Molib_Util_RichTextParserCondition();
            $condition->intNewLineCount = 1;
            $objResult = $parser->process($condition, $srtAbstract);
            $srtAbstract = $objResult->arrContent;
            if (empty($srtAbstract)){
                $srtAbstract = array();
            }
            $arrThread['abstract'] = $srtAbstract;
            $strTitle = $arrThread['title'];
            $strTitle = html_entity_decode($strTitle, ENT_COMPAT, 'UTF-8');
            $arrThread['title'] = trim($strTitle);
            $arrUserKey = array('name', 'name_show', 'type', 'id', 'portrait');
            $arrThread['author'] = Molib_Util_Array::fetchArray($arrThread['author'], $arrUserKey);
            $arrThread['author'] = Molib_Util_Format::formatUser($arrThread['author']);
            $arrThread['last_replyer'] = Molib_Util_Array::fetchArray($arrThread['last_replyer'], $arrUserKey);
            $arrThread['last_replyer'] = Molib_Util_Format::formatUser($arrThread['last_replyer']);
            //广告计费
            if (isset($arrThread['ad']) && isset($arrThread['ad']['click_url'])){
                $arrThread['click_url'] = $arrThread['ad']['click_url'];
            }
            $arrThread = Molib_Util_Array::fetchArray($arrThread, array('id', 'title', 'reply_num', 'view_num', 'last_time_int','thread_types', 'is_top', 'is_good', 'author', 'last_replyer', 'comment_num', 'show_commented', 'last_time' , 'click_url', 'media', 'abstract','is_notice','title_link'));
            $arrThread['show_commented'] = false;
            if ($arrThread['comment_num'] > $intCommentThreshold) {
                $arrThread['show_commented'] = true;
            }
            $arrThreadList[] = $arrThread;
        }
        unset($this->_arrThreadList);
        $this->_arrThreadList = $arrThreadList;
    }
    private function _procFrsStar()
    {
        if (isset($this->_arrFrsPage['forum']['has_frs_star']) 
                && intval($this->_arrFrsPage['forum']['has_frs_star']) === 1) {
            if (intval($this->_arrFrsPage['forum']['id']) === 6887829) {
                $this->_arrFrsStar['has_frs_star'] = 0;
            } else {
                $this->_arrFrsStar = $this->_getFrsStar($this->_arrFrsPage['forum']['id']);
                if (!empty($this->_arrFrsStar)) {
                    $this->_arrFrsStar['has_frs_star'] = 1;
                } else {
                    $this->_arrFrsStar['has_frs_star'] = 0;
                }
            }
        } else {
            $this->_arrFrsStar['has_frs_star'] = 0;
        }
    }
    private function _getFrsStar($intFid) {
        $arrInput = array(
                'forum_id' => intval($intFid),
                'need_frsinfo' => 1, // 明星相关信息,需要
                'need_rforum' => 0,    // 相关贴吧信息,无线暂时不需要
                );
        $arrResult = Tbapi_Core_Server::apicall('frs', 'frs_star', $arrInput);
        if ($arrResult === false) {
            Molib_Util_Log::apiwarning('frs', 'frs_star', false, $arrInput);
        }
        if (empty($arrResult) || !is_array($arrResult)) {
            return array();
        }
       
        $arrResult['fans']['start_time'] = $arrResult['fans']['open_time'];
        unset($arrResult['fans']['open_time']);
        $intNowTime = time();
        $intLeftTime = $arrResult['fans']['start_time'] - $intNowTime;
        if ($intLeftTime >= 0) {
            $arrResult['fans']['left_time'] = $intLeftTime;
        } else {
            $arrResult['fans']['left_time'] = 0;
        }
        // 其他url暂时没使用,暂不处理
        return $arrResult;
    }
    private function _procSignInfo()
    {
        $forum_id = $this->_arrForum['id'];
        $forum_name = $this->_arrForum['name'];
        //user_id=0 只返回吧相关信息, user_id>0增加返回用户相关信息
        $user_id = 0;
        if ($forum_id == 0 && !isset($forum_name)) {
            Bingo_Log::warning('forum_sign_info forum id is ' . $forum_id . ' forum_name is ' . $forum_name);
            return false;
        }
        //取签到信息
        $arrData = array(
                'forum_id'   => $forum_id,
                'forum_name' => $forum_name,
                'user_id'    => $user_id,
                );
        $arrOut = Tbapi_Core_Server::apicall('userforum', 'forum_sign_info', $arrData, 0);
        if ($arrOut === false) {
            Molib_Util_Log::apiwarning('userforum', 'forum_sign_info', false, $arrData);
            return false;
        }        
        $this->_arrForum['forum_sign_num'] = $arrOut['sign_count'];
        $this->_arrForum['forum_rank'] = $arrOut['sign_rank'];        
		$this->_arrForum['forum_sign_calendar'] = array();
        
        if ( 0 < intval($this->_arrUser['id']) && 1 == $this->_intWithCal ) {
        	$this->_loadSignCalendar();
        }
        return true;
    }
    private function _getForumTagInfo(){
        if (empty($this->_arrForum['id'])) {
            return false;
        }
        $arrOut = Tbapi_Core_Server::mocall('tag', 'tag_byfid', array('fid_list' => $this->_arrForum['id']));
        if (false === $arrOut || $arrOut['error']['errno'] != 0) {
            Molib_Util_Log::apiwarning('tag', 'tag_byfid', false, $arrOut);
            return false;
        }
        if (isset($arrOut['tag_info'][$this->_arrForum['id']])) {
            $this->_arrTagInfo = $arrOut['tag_info'][$this->_arrForum['id']];
        }
        else {
            $this->_arrTagInfo = array(
                'tag_name' => '',
                'tag_id'   => 0,
                'color' => '',
            );
        }
        return $this->_arrTagInfo;
    }

    private function _getUserRole(){
        $arrInput = array(
            'forum_id' => $this->_arrForum['id'],
            'user_id'  => $this->_arrUser['id'],
            'user_ip'  => $this->_reqData['user_ip'],
        );
        $arrOut = Tbapi_Core_Server::mocall('user', 'user_role', $arrInput);
        if (false === $arrOut || $arrOut['error']['errno'] != 0) {
            Molib_Util_Log::apiwarning('tag', 'tag_byfid', false, $arrOut);
            return false;
        }
        $this->_arrUser['is_manager'] = $arrOut['manager_type'];
               
        return true;
    }
    private function _getForumInfoEx(){
        $arrInput = array(
            'forum_ids' => $this->_arrForum['id'],
        );
        $arrOut = Tbapi_Core_Server::mocall('forum', 'forum_style', $arrInput);
        if (false === $arrOut || $arrOut['error']['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Molib_Util_Log::apiwarning('forum', 'forum_style', false, $arrOut);
            return false;
        }
        //5是吧头像和吧简介的样式
        if (isset($arrOut['forums_style'][$this->_arrForum['id']]['attrs']['card_p1'])) {
            $arrStyle = $arrOut['forums_style'][$this->_arrForum['id']]['attrs']['card_p1']['style_name'];
            $this->_arrForum['avatar'] = $arrStyle['avatar'];
            $this->_arrForum['slogan'] = $arrStyle['slogan'];
        }
        else {
            $this->_arrForum['avatar'] = '';
            $this->_arrForum['slogan'] = '';
        }
        return true;                                                                                  
    }
    private function _loadSignCalendar(){
        //load data...
        //$arrForum = array(
	//		'forum_id' => intval($this->_arrForum['id']),
	//	);
		
		//$arrMonth = array(
		//	'year' 	=> intval(date('Y')),
		//	'month' => intval(date('n')),
		//);
		$intUserID = intval($this->_arrUser['id']);
		
		//$arrOutputNeed = array(
		//	'need_user_sign_info' => 1, 
		//	'need_month_sign_info' => 1, 
		//);
		
		////action getting data from module
		//$ret = Tbapi_Userforum_Midl_Sign::getUserMonSignInfo($arrForum, $intUserID,$arrMonth,$arrOutputNeed);
		$in = array(
			'forum_id'=>intval($this->_reqData['forum_id']),
			'user_id'=>$intUserID,
			'year'=>intval($this->_reqData['year']),
			'month'=>intval($this->_reqData['month']),
		);  
		$ret = Tieba_Service::Call('sign', 'getUserMonSignInfo', $in);

		//if (empty($ret)) {
		if (empty($ret) || 0 !== $ret['errno']) {
			Bingo_Log::warning(	'Get forum and user info error ! params: '.serialize($this->_reqData).' result: '.serialize($ret));
			$this->_resData['info'] = array();
		}else {
			$arrMonthInfo = $ret['his_info'];
			$arrUserInfo = $ret['user_info'];
			$arrMonDisplay = array();
			$arrFlags = array();
			$intDays = intval(date('j',$arrUserInfo['sign_time']));
			$intCountSignNum = $arrUserInfo['cont_sign_num'];
	
			foreach($arrMonthInfo as $arrValue){
				$sign_time = $arrValue['sign_time'];
				//后端接口getUserMonSignInfo，有bug，当月没签到，会取上个月的签到信息，在这里做临时处理，如果历史签到信息不是当月的，则返回不处理
				if(date('n',$sign_time) != date('n')) {
					break;
				}
				$arrMonDisplay[] = array(
					'd' => strval(date('j',$sign_time)),
					't' => strval(date('G:i',$sign_time)),
					'rank' => $arrValue['sign_rank'],
					'sign_type' => (1 === $arrValue['term_type'])?'mobile':'',	//add by hlx 2012-10-31
					//'term_type' => $arrValue['term_type'],
				);
				$arrFlags[intval(date('j',$sign_time))] = 1;
			}		
			
			$this->_arrForum['forum_sign_calendar'] = $arrMonDisplay;
		}
    }
    //过滤图册精选的贴子
    private function _filterPicPost(){
        foreach ($this->_arrThreadList as $intKey => $arrThread){
            if(isset($arrThread['thread_types'])){
            	$thread_types = intval($arrThread['thread_types']);
                $other_types = $thread_types >> 32;
                if(self::PIC_POST_THREAD === $other_types){
                	unset($this->_arrThreadList[$intKey]);
                }       
            }       
        }       
    }
}
