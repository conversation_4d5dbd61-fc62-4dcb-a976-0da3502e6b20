<?php
class Service_Post_Logic_VoiceChunkUpload extends Molib_Core_Logic {

    CONST MAX_VOICE_CHUNK_SIZE = 102400;

    protected function _check() {
        $strVoiceKey = 'voice_chunk';
        if (empty($_FILES[$strVoiceKey])) {
            $intErrno = 4;
        } else {
            $intErrno = intval($_FILES[$strVoiceKey]['error']);            
        }        
        if ($intErrno > 0){
            Bingo_Log::trace("post file upload net error, errno=$intErrno");
            $this->_error(Tieba_Errcode::ERR_MO_POST_FILE_UPLOAD_NET_ERROR);
            return false;	
        }
        $intVoiceSize = intval($_FILES[$strVoiceKey]['size']);
        //文件分片不能太大,不能超过50k
        if ($intVoiceSize > self::MAX_VOICE_CHUNK_SIZE) {
            Bingo_Log::trace("post file upload size error, size=$intVoiceSize");
            $this->_error(Tieba_Errcode::ERR_MO_POST_FILE_UPLOAD_SIZE_ERROR);
            return false;            
        }
        $strVoiceData = '';
        $arrData = array();

        //获取上传文件
        if (isset($_FILES[$strVoiceKey]['tmp_name'])){
            $strVoiceData = file_get_contents($_FILES[$strVoiceKey]['tmp_name']);
            $strVoiceMd5 = strtolower(md5($strVoiceData));
        }
        if (empty($strVoiceData)){
            //上传的文件为空
            Bingo_Log::trace("post file upload net error, voice_data=$strVoiceData");
            $this->_error(Tieba_Errcode::ERR_MO_POST_FILE_UPLOAD_NET_ERROR);
            return false;	            
        }
        $this->strChunkVoice = $strVoiceData;
        $arrData['chunk_file_md5'] = strtolower($strVoiceMd5);
        $strChunkMd5 = strtolower($this->_reqData['chunk_md5']);
        $arrData['user_id'] = $this->_reqData['user_data']['id'];
        $arrData['chunk_offset'] = intval($this->_reqData['offset']);
        $arrData['chunk_length'] = intval($this->_reqData['length']);
        $arrData['chunk_no'] = intval($this->_reqData['chunk_no']);
        $arrData['total_length'] = intval($this->_reqData['total_length']);
        $arrData['total_num'] = intval($this->_reqData['total_num']);
        $arrData['is_lightapp'] = $this->_reqData['is_lightapp'];
	    //因轻应用端无法获取文件md5,因此去掉md5校验
        if($arrData['is_lightapp']){
            $arrData['total_file_md5'] = strtolower($strVoiceMd5); 
        }else{
            $arrData['total_file_md5'] = strtolower($this->_reqData['voice_md5']);
            if (strlen($strVoiceData) !== $arrData['chunk_length']){
                Bingo_Log::trace("voice_data, chunk_length not match, voice_data=".strlen($strVoiceData)." chunk_length={$arrData['chunk_length']}");
                $this->_error(Tieba_Errcode::ERR_MO_POST_FILE_UPLOAD_DATA_ERROR);
                return false;              
            }
            if ($arrData['chunk_offset'] > $arrData['total_length'] || 
                    ($arrData['chunk_offset'] + $arrData['chunk_length']) > $arrData['total_length']) {
                Bingo_Log::trace("chunk_offset={$arrData['chunk_offset']} total_length={$arrData['total_length']} chunk_length={$arrData['chunk_length']}");
                $this->_error(Tieba_Errcode::ERR_MO_POST_FILE_UPLOAD_DATA_ERROR);
                return false;            
            }
            if (empty($arrData['total_file_md5']) || $arrData['total_num'] == 0 || $arrData['total_length'] == 0 || $arrData['chunk_length'] == 0){
                Bingo_Log::trace("total_file_md5={$arrData['total_file_md5']} total_num={$arrData['total_num']} total_length={$arrData['total_length']} chunk_length={$arrData['chunk_length']}");
                $this->_error(Tieba_Errcode::ERR_MO_POST_FILE_UPLOAD_DATA_ERROR);
                return false;             
            }
        }    
        $arrData['upload_time'] = Bingo_Timer::getNowTime();
                      
        if ($arrData['chunk_no'] > $arrData['total_num']){
            Bingo_Log::trace("chunk_no > total_num, chunk_no={$arrData['chunk_no']} total_num={$arrData['total_num']}");
            $this->_error(Tieba_Errcode::ERR_MO_POST_FILE_UPLOAD_DATA_ERROR);
            return false;              
        }   
        if ($arrData['chunk_no']  <= 0 ){
            Bingo_Log::trace("chunk_no<=0 chunk_no={$arrData['chunk_no']}");
            $this->_error(Tieba_Errcode::ERR_MO_POST_FILE_UPLOAD_DATA_ERROR);
            return false;              
        }  
        $this->_arrRequest = $arrData;
        return true;
    }
    protected function _execute() {
        $arrInput = array(
                'id' => $this->_arrRequest['total_file_md5'],
                'content' => $this->strChunkVoice,
                'chunk_no' => $this->_arrRequest['chunk_no'],
                );
        $ret = Tieba_Service::call('moyuyin', 'storeChunk', $arrInput);
        if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']){
            Molib_Util_Log::apiwarning('moyuyin', 'storeChunk', $ret['errno'], $arrInput);
            $this->_error(Tieba_Errcode::ERR_MO_POST_FILE_UPLOAD_DATA_ERROR);
            return false;
        }
        return true;
    }
    protected function _transform() {
        $this->_resData = $this->_arrRequest;
    }
}
