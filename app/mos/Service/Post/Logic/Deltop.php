<?php
class Service_Post_Logic_Deltop extends Molib_Core_Logic {
	//请求数据
    private $_strFname = '';
    private $_intFid = 0;
    private $_intTid = 0;
    private $_strTbs = '';
    private $_strUsername = '';
    private $_intUserid = 0;
    private $_intUserip = 0;
	private $_bolIsMemberTop = null;//初始化必须是null

	protected function _check() {
		$this->_strFname = $this->_reqData['forum_name'];
		$this->_intFid = $this->_reqData['forum_id'];
		$this->_intTid = $this->_reqData['thread_id'];
		$this->_strTbs = $this->_reqData['tbs'];
		$this->_intUserid = $this->_reqData['user_id'];
        $this->_strUsername = $this->_reqData['user_name'];
        $this->_intUserip = $this->_reqData['user_ip'];
		if(isset($this->_reqData['is_membertop'])){
	        $this->_bolIsMemberTop = $this->_reqData['is_membertop'];
		}
		
		if(empty($this->_strFname) || empty($this->_intFid) || empty($this->_intTid) || empty($this->_strTbs)){
            Bingo_Log::warning(sprintf('DelTop input is invalid. fname:%s;fid:%s;tid:%s;tbs:%s',
                $this->_strFname,$this->_intFid,$this->_intTid,$this->_strTbs));
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID, $this->_reqData);    
            return false;
        }
		//should check tbs.
    	$arrCookieBak = $_COOKIE;
        $_COOKIE = Tbapi_Core_Server::getCookie();
        $bolLogin = ($this->_intUserid == 0)? false:true;
        $ret = Tieba_Tbs::check($this->_strTbs, $bolLogin);
        $_COOKIE = $arrCookieBak;
        if ($ret === false){ 
            Bingo_Log::warning(sprintf('tbs check fail! tbs:%s',$this->_strTbs));
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID, $this->_reqData);    
            return false;
        }
        return true;
	}
	
	protected function _execute() {		
    	$arrOut = array();
		$arrInput['req'] = array(
        	'user_id'	=>	$this->_intUserid,
			'user_name'	=>	$this->_strUsername,
			'forum_id'	=>	$this->_intFid,
			'forum_name'	=>	$this->_strFname,
			'thread_id'	=>	$this->_intTid,
			'op_uid'	=>	$this->_intUserid,
			'op_uname'	=> 	$this->_strUsername, 
			'op_ip'	=>	$this->_intUserip,
			'from_module'	=>	'mo_bawu',
			'from_function'	=>	'Service_Post_Logic_Addtop',
			'user_ip'	=>	$this->_intUserip,
        );

		if(null != $this->_bolIsMemberTop){
			$arrInput['req']['is_membertop'] = $this->_bolIsMemberTop;
		}
        $arrInput['req'] = Service_Post_Logic_Util::addParamUserIpV4OrV6($arrInput['req'], $this->_intUserip, 'op_ip');

        $arrOut = Tieba_Service::call('bawu', 'cancelTop', $arrInput,NULL,NULL,'post','php','utf-8');
        if ($arrOut['errno'] == Tieba_Errcode::ERR_HAD_OPEN_RED_PACKAGE) {
            $this->_error(Tieba_Errcode::ERR_HAD_OPEN_RED_PACKAGE); 
        } else if (false === $arrOut || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
            $this->_error(Tieba_Errcode::ERR_MO_POST_DELTOP_FAIL); 
            return false;
        }
        
        return true;   
    } 
    
    protected function _transform(){ 	
    	return true;       
    }
    
    
}
