<?php
/*
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @time: 2016-4-1
 */
class indexAction extends Mo_Core_Action{
    //声明要渲染的模板control路径，也可以在_execute中动态赋值
    protected $strTplName = 'tbvip/index.php';
    protected $strTplPath = 'scommerce';

    private $_intParam = 0;
    private $_retData  = array();
    private $_apply_st = -1;   //-1 未申请 0 正在审核 1 审核通过 2 审核未通过 3撤销资格
    private $user = array();
    private $wreq = array();

    //必须实现，其内部逻辑拆分自便
    protected function _execute(){
        $this->user = Mo_Service_Wap::getTplUser(); 
        $this->wreq = Mo_Service_Wap::getTplWreq();
        //未登录跳转
        if ($this->user['is_login']!==true) {
            $sever = Bingo_Http_Request::getServer();
            $strRedirectUrl = urlencode('http://' . $sever['HTTP_HOST'] . '/mo/q/tbvip/index');
            $passUrl = 'http://wappass.baidu.com/passport/?login&u=' . $strRedirectUrl . '&regtype=1&tpl=tb';
            Bingo_Http_Response::redirect($passUrl);
            return;
        }
        //主逻辑
        $this->_process();
        //封装返回值
        $this->_build();
    }
    /**
    * @brief 主流程
    * @param void
    * @return void
    **/
    private function _process(){
        $arrData = array();
        //获取用户
        $this->_apply_st = $this->_getUserStatus($this->user['id']);
        //获取认证分类
        /*
        $arrCateList = $this->_getCategoryList();
        if (!empty($arrCateList) && is_array($arrCateList)) {
            foreach ($arrCateList as $row) {
                $intCategory = intval($row['id']);
                $arrRank = $this->_getRank($intCategory);
                $arrUsers= $this->_fmtUser($arrRank);
                if (!empty($arrUsers)) {
                    $arrData[] = array(
                        'category_id' => $intCategory,
                        'category'    => $row['name'],
                        'user'        => $arrUsers,
                    );
                }
            }
            $this->_retData = $arrData;
        }
        */
    }
    /**
    * @brief 返回值
    * @param void
    * @return void
    **/
    private function _build() {
        Mo_Response::addViewData('tbs',Tieba_Tbs::gene($this->user['is_login']));
        Mo_Response::addViewData('user', $this->user);
        Mo_Response::addViewData('wreq', $this->wreq);
        //Mo_Response::addViewData('data', $this->_retData);
        Mo_Response::addViewData('status', $this->_apply_st);
    }

    /**
    * @brief 获取认证分类列表
    * @param void
    * @return array
    *  -1 未申请 0 正在审核 1 审核通过 2 审核未通过 3撤销资格
    **/
    private function _getCategoryList() {
        $intPn = intval(Mo_request::get('pn'));
        $intRn = intval(Mo_request::get('rn'));
        $intPn = ($intPn <=0 ) ? 1 : $intPn;
        $intRn = ($intRn <=0 ) ? 10 : $intRn;

        $arrInput = array(
            'status' => 1,
            'pn'     => $intPn,
            'rn'     => $intRn,
        );
        $arrRet   = Tieba_Service::call('tbvip','getCategoryList', $arrInput,null,null,'post','php','utf-8');
        if ($arrRet['errno'] === Tieba_Errcode::ERR_SUCCESS) {
            return $arrRet['data']['list'];
        }
        else {
            Bingo_Log::warning('call getCategoryList failed [input]'.serialize($arrInput). '[output]'.serialize($arrRet));
            return array();
        }
    }
    /**
    * @brief 获取用户申请状态
    * @param int user_id
    * @return array
    **/
    private function _getUserStatus($user_id) {
        $arrInput = array(
            'user_id' => intval($user_id),
        );
        $arrRet   = Tieba_Service::call('tbvip','getUser', $arrInput,null,null,'post','php','utf-8');
        if ($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call getCategoryList failed [input]'.serialize($arrInput). '[output]'.serialize($arrRet));
            return -1;
        }

        if (empty($arrRet['data'])) {
            return -1;
        } 
        $intStatus = intval($arrRet['data']['status']);
        return $intStatus;
    }
    /**
    * @brief 跟据认证分类获取大V排名榜
    * @param array
    * @return array
    **/
    private function _getRank($category) {
        $arrInput = array(
            'pn'  => 1,
            'rn'  => 3,
            'category' => intval($category),
        );
        $arrRet   = Tieba_Service::call('tbvip','getRank', $arrInput,null,null,'post','php','utf-8');
        if ($arrRet['errno'] === Tieba_Errcode::ERR_SUCCESS) {
            return $arrRet['data']['list'];
        }
        else {
            Bingo_Log::warning('call getCategoryList failed [input]'.serialize($arrInput). '[output]'.serialize($arrRet));
            return array();
        }
    }
    /**
    * @brief 格式化用户信息
    * @param array
    * @return array
    **/
    private function _fmtUser($arrInput) {
        if (empty($arrInput) || !is_array($arrInput)) {
            return array();
        }

        foreach ($arrInput as $row) {
            $arrUserId[] = intval($row['user_id']);
        }
        $arrUserId = array_unique($arrUserId);
        $arrParam  = array(
            'user_id' => $arrUserId,
        );
        $arrRet = Tieba_Service::call( 'user', 'mgetUserData', $arrParam, null, null, 'POST', 'php', 'utf-8' );
        if ( $arrRet===false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS )
        {
            Bingo_Log::warning( "call user mgetUserData error. [input]" . serialize($arrParam). " [output: ]" . serialize( $arrRet ) );
            return array();
        }
        $arrUserInfo = $arrRet['user_info'];
        //获取关注信息
        $arrParam   = array(
            'user_id' => $this->user['id'],
            'req_user_id' => $arrUserId,
        );
        $arrRet = Tieba_Service::call( 'user', 'getUserFollowInfo', $arrParam, null, null, 'POST', 'php', 'utf-8' );
        if ( $arrRet===false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS )
        {
            Bingo_Log::warning( "call user getUserFollowInfo error. [input]" . serialize($arrParam). " [output: ]" . serialize( $arrRet ) );
            //return array();
        }
        $arrFollow = isset($arrRet['res_user_infos']) ? $arrRet['res_user_infos'] : array();
        $arrFollowId = array();
        if (!empty($arrFollow)) {
            foreach ($arrFollow as $row) {
                $arrFollowId[$row['user_id']] = $row;
            }
        }
        foreach ($arrInput as $key=>$val) {
            //头像时间戳
            $intPortraitTime  = time();
            if (isset($arrUserInfo[$val['user_id']])) {
                $intPortraitTime = $arrUserInfo[$val['user_id']]['portrait_time'];
            } else {
                unset($arrInput[$key]);
                continue;
            }
            $arrInput[$key]['username'] = !empty($arrUserInfo[$val['user_id']]['user_nickname']) ?  $arrUserInfo[$val['user_id']]['user_nickname'] :  $arrUserInfo[$val['user_id']]['user_name'] ;
            $arrInput[$key]['portrait'] = strval(Tieba_Ucrypt::encode($val['user_id'], $arrUserInfo[$val['user_id']]['user_name'], $intPortraitTime));
            $arrInput[$key]['v_url']    = isset($arrUserInfo[$val['user_id']]['tb_vip']['v_url']) ? $arrUserInfo[$val['user_id']]['tb_vip']['v_url'] : '';
            $arrInput[$key]['intro']    = isset($arrUserInfo[$val['user_id']]['tb_vip']['intro']) ? $arrUserInfo[$val['user_id']]['tb_vip']['intro'] : '';
            $arrInput[$key]['is_followed']= isset($arrFollowId[$val['user_id']]) ? $arrFollowId[$val['user_id']]['is_followed'] : 0;
        }
        return $arrInput;
    }

    //必须实现
    protected function _log(){
        //如果数据组没有提需求，这里留空即可，这里我们假设这个请求不算pv，故将log中的ispv字段设为0（默认1）
        Tieba_Stlog::addNode('ispv', 1);
    }
}
?>
