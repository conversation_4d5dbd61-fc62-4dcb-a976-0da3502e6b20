<?php
/**
 * Author: ji<PERSON><PERSON>bin
 * Date: 2013-04-21
 * Time: 上午10:26
 * Description:
 */

class aggregateAction extends Mo_Core_Action {
	protected $strTplName = 'aggregation.php';
	protected $strTplPath = 'swebview';
	private $_arrForumList;
	private $_strForumName;
	private $_intForumId;
	private $_arrForumConf;
	private $_arrTheadConf;

	private $_redis;
    const FORUM_SET = 'aggregate_page_forum_set';
	/**
	 * @return bool
	 */
	protected function _init()
	{
		/*$this->_arrForumList = array(
			"清华大学","中北大学","西安交通大学","长安大学","安徽工程大学","安徽理工大学","安徽职业技术学院","合肥工业大学","济南大学","江苏大学",
			"聊城大学","临沂大学","南京工程学院","南京邮电大学","南通大学","齐鲁工业大学","青岛大学","山东建筑大学","山东科技大学","山东理工大学",
			"苏州大学","烟台大学","中国矿业大学","广东财经大学华商学院","广东轻工职业技术学院","广州番禺职业技术学院","桂林电子科技大学","吉林大学珠海学院","南宁职业技术学院","深圳大学",
			"深圳职业技术学院","郑州大学","河南工业大学","河南大学","河南理工大学","华中科技大学","武汉大学","武汉理工大学","河南科技大学","河南财经政法大学",
			"郑州大学西亚斯国际学院","华北水利水电大学","武汉生物工程学院","九江学院","武汉职业技术学院","湖南工业职业技术学院","长沙民政职业技术学院","中南大学","西华大学","西南大学",
			"西南交通大学","西南科技大学","成都大学","成都理工大学","电子科技大学","四川大学",
		);*/
        $this->_redis = new Bingo_Cache_Redis('push');
        $resForumInfo = $this->_redis->GET(array('key' => self::FORUM_SET));
        $resForumInfo = Bingo_String::json2array($resForumInfo['ret'][self::FORUM_SET],'utf-8');

        //获取吧名列表
        $this->_arrForumList = array();
        foreach ($resForumInfo as $key => $value) {
            $this->_arrForumList[] = $value;
        }
	}
	
	/**
	 * @return
	 * @param
	 */
	protected  function _execute(){
		$this->_init();
		$arrUrl = explode("?", $_SERVER['REQUEST_URI']);
        $arrUrl = explode("&", $arrUrl[0]);
        $arrUrl = explode("=", $arrUrl[0]);
        //兼容旧格式/f/aggregate/forum=安徽理工大学
        //$this->_strForumName = trim(urldecode($arrUrl[1]), '');
        //新格式/t/f/32132,  32132为吧id
        //if(empty($this->_strForumName)){
        $arrUrl = explode("/", $arrUrl[0]);
        $this->_intForumId = trim(urldecode($arrUrl[count($arrUrl)-1]), '');
        //}
		foreach($this->_arrForumList as $index => $forum){
			//把测页展示的其他学校去掉本学校
			if ($forum['forum_id'] == $this->_intForumId){
                $this->_strForumName = $forum['forum_name'];
    			unset($this->_arrForumList[$index]);
    		}
		}
		//$redis = new Bingo_Cache_Redis('push');
		$key = "aggregate_page_".$this->_strForumName;
		$input = array(
			"key" => $key,
		);
		$res = $this->_redis->GET($input);
		$this->_arrForumConf = Bingo_String::json2array($res['ret'][$key],'UTF-8');
		if (empty($this->_arrForumConf)){
			header('location:/f?kw='.$this->_strForumName);
			Mo_Response::$intErrno = Tieba_Errcode::ERR_MIS_FORUM_NAME_NOT_EXIST;
        	Mo_Response::$strError = Tieba_Error::getErrmsg(Tieba_Errcode::ERR_MIS_FORUM_NAME_NOT_EXIST);
			$this->_jsonRet( Tieba_Errcode::ERR_MIS_FORUM_NAME_NOT_EXIST,Tieba_Error::getErrmsg(Tieba_Errcode::ERR_MIS_FORUM_NAME_NOT_EXIST));
        	return false;
		}
		$this->_arrForumConf['tag'] = array_values($this->_arrForumConf['tag']);
		$this->_intForumId = intval($this->_arrForumConf['forum_id']);
		foreach($this->_arrForumConf['tag'] as $arrTag){
			foreach($arrTag['thread_list'] as $thread){
				$this->_arrTheadConf[$thread["thread_id"]] = $thread["thread_id"];
			}
		}
		$arrOutput = $this->_loadData();
		Mo_Response::addViewData('base',Mo_Service_Wap::getTplBase());
		Mo_Response::addViewData('user',Mo_Service_Wap::getTplUser());
		Mo_Response::addViewData('data',$arrOutput);
	}
	
	/**
	 * @return
	 * @param
	 */
	protected function _log() {
		Tieba_Stlog::addNode('ispv', 1);
		Tieba_Stlog::addNode("urlkey", 'f/aggregate/');
		$arrUser = Mo_Service_Wap::getTplUser();
		Tieba_Stlog::addNode("uid", $arrUser['uid']);
		Tieba_Stlog::addNode("mobile", $arrUser['mobilephone']);
		Tieba_Stlog::addNode('uip', intval(Bingo_Http_Ip::ip2long(Bingo_Http_Ip::getConnectIp())));
		Tieba_Stlog::addNode("forum_name", $this->_strForumName);
		Tieba_Stlog::addNode("forum_id", $this->_intForumId);
	}
	
	/**
	 * @return
	 * @param
	 */
	protected function _loadData(){
		$objRalMulti = new Tieba_Multi('aggregate');
		$arrMultiInput = array(
			'serviceName' => 'forum',
			'method' => 'mgetBtxInfoEx',
			'input' => array(
				"forum_id" => array(
					$this->_intForumId,
				),
			),
			'ie' => 'utf-8',
		);
		$objRalMulti->register('mgetBtxInfoEx',new Tieba_Service('aggregate'),$arrMultiInput);
	
		$input = array(
			"thread_ids" => array_values($this->_arrTheadConf),
			"need_abstract" => 1,
			"forum_id" => 0,
			"need_photo_pic" => 0,
			"need_user_data" => 0,
			"icon_size" => 0,
			"need_forum_name" =>0, //是否获取吧名
			"call_from" => "pc_frs" //上游模块名
		);
		$arrMultiInput = array(
			'serviceName' => 'post',
			'method' => 'mgetThread',
			'input' => $input,
			'ie' => 'utf-8',
		);
		$objRalMulti->register('mgetThread',new Tieba_Service('aggregate'),$arrMultiInput);
	
		foreach($this->_arrTheadConf as $tid){
			$input = array(
				"thread_id" => $tid, //帖子id
				"offset" => 0,
				"res_num" => 10,
				"see_author" => 0,
				"has_comment" => 0,
				"has_mask" => 0,
				"has_ext" =>0,
				"need_set_pv" => 0,
				"structured_content" => 1
			);
			$arrMultiInput = array(
				'serviceName' => 'post',
				'method' => 'getPostsByThreadId',
				'input' => $input,
				'ie' => 'utf-8',
			);
			$objRalMulti->register('getPostsByThreadId_'.$tid,new Tieba_Service('aggregate'),$arrMultiInput);
		}
		$objRalMulti->call();
		$arrRet = $objRalMulti->results;
		 
		$arrResult=array();
		$arrUserList = array();
		 
		if ($arrRet['mgetBtxInfoEx']['errno'] == Tieba_Errcode::ERR_SUCCESS){
			$arrResult['forum'] = $arrRet['mgetBtxInfoEx']['output'];
		}
		if ($arrRet['mgetThread']['errno'] == Tieba_Errcode::ERR_SUCCESS){
			foreach ($arrRet['mgetThread']['output']['thread_list'] as $tid => $info){
				$arrResult['thread'][$tid] = $info;
				$arrUserList[$info['user_id']] = $info['user_id'];
				$arrUserList[$info['last_user_id']] = $info['last_user_id'];
			}
		}
		$arrResult['user'] = $this->_getUserList($arrUserList);
		foreach($this->_arrTheadConf as $tid){
			$ret = $arrRet['getPostsByThreadId_'.$tid]['output']['output'][0];
			if ($ret['errno'] == Tieba_Errcode::ERR_SUCCESS && !empty($ret['post_infos'])){
				$arrResult['post'][$tid] = $ret;
			}
		}
		return $this->_buildData($arrResult);
	}
	
	/**
	 * @return
	 * @param
	 */
	protected function _buildData($arrResult){
		foreach($this->_arrTheadConf as $tid){
			$content = "";
			$pic_url = "";
			//获取首楼的文本内容
			foreach($arrResult['post'][$tid]['post_infos'][0]['content'] as $floor){
				if ($floor['tag'] == "plainText"){
					$content .= $floor['value'];
				}
			}
			//如果是视频贴，视频封面作为图片
			if ($arrResult['post'][$tid]['thread_type'] == 40){
				$pic_url = $arrResult['post'][$tid]['video_info']['thumbnail_url'];
			}
			else {
				//如果是非视频贴，则从1-10楼顺序遍历获取第一张非表情图
				foreach($arrResult['post'][$tid]['post_infos'] as $post){
					foreach($post['content'] as $floor){
						if ($floor['tag'] == "img"){
							$pos = strrpos($floor['src'],"/");
							$strImgId = strval(substr($floor['src'],$pos+1,40));
							if (strlen($strImgId) != 40){
								continue;
							}
							$pic_url = $floor['src'];
							break;
						}
					}
					if (!empty($pic_url)){
						break;
					}
				}
			}
			if (substr($pic_url,0,4) == "http" && substr($pic_url,0,5) != "https"){
				$pic_url = str_replace("http","https",$pic_url);
			}
			$arrResult['detail'][$tid] = array(
				'content' => $content,
				'pic_url' => $pic_url,
			);
		}
		return $this->_buildPage($arrResult);
	}
	
	/**
	 * @param
	 * @return
	 */
	protected function _buildPage($arrResult){
		$arrOutput = array(
			'page_title' => $this->_arrForumConf['page_title'],
			'title_tag' => $this->_arrForumConf['title_tag'],
			'description' => $this->_arrForumConf['description'],
			'keywords' => $this->_arrForumConf['keywords'],
		);
		
		$forum_pic = $arrResult['forum'][$this->_intForumId]['card']['avatar'];
		if (substr($forum_pic,0,4) == "http" && substr($forum_pic,0,5) != "https"){
			$forum_pic = str_replace("http","https",$forum_pic);
		}
		$arrOutput['forum'] = array(
			'forum_name' => $this->_arrForumConf['forum_name'],
			'forum_pic' => $forum_pic,
			'like_num' => $arrResult['forum'][$this->_intForumId]['statistics']['member_count'],
			'thread_num' => $arrResult['forum'][$this->_intForumId]['statistics']['thread_num'] ,
		);
		 
		$arrThreadModule = array();
		foreach($this->_arrForumConf['tag'] as $index => $arrTag){
			$arrTagInfo = array(
				'tagname' => $arrTag['tagname'],
			);
			//最后一个tag作为图册,其它作为非图册
			$arrTagInfo['type'] = ($index == count($this->_arrForumConf['tag']) -1) ? "pic_list" : "world_list";
			foreach($arrTag['thread_list'] as $tid => $confThread){
				$arrThread = $arrResult['thread'][$tid];
				if ($arrThread['is_deleted'] == 1){
					continue;
				}
				$content = $arrResult['detail'][$tid]['content'];
				$pic_url = $arrResult['detail'][$tid]['pic_url'];
				 
				//如果图册的贴子没有图片，则不应放到图册里
				if ($arrTagInfo['type'] == "pic_list" && empty($pic_url)){
					continue;
				}
				 
				$content = empty($content) ? $confThread['thread_name'] : $content;
				$arrThreadList = array(
					'thread_name' => $confThread['thread_name'],
					'comment_num' => $arrThread['post_num'],
					'thread_id' => $tid,
					'thread_type' =>  empty($pic_url) ? "word" : "word-pic",
					'content' => "<p>".$content."</p>",
					'last_comment_member' => $arrResult['user'][$arrThread['last_user_id']]['user_name'],
					'last_comment_time' =>  $arrThread['last_modified_time'],
					'author' =>  $arrResult['user'][$arrThread['user_id']]['user_name'],
					'pic_url' => $pic_url,
				);
				$arrTagInfo['list'][] = $arrThreadList;
			}
			//没有贴，不展现该tag
			if (count($arrTagInfo['list']) > 0){
				$arrThreadModule[] = $arrTagInfo;
			}
		}
	
		$total = count($this->_arrForumList)-1;
    	if ($total >= 50){
    		$list = array();
    		while(count($list) < 10){
    			$forum = $this->_arrForumList[rand(0,$total)];
    			if (!empty($forum)){
	    			$list[$forum['forum_id']] = $forum;
    			}
    		}
    		$arrThreadModule[] = array(
    			"tagname" => "其他学校",
    			'type' => 'about_list',
    			'list' => array_values($list),
    		);
    	}
		$arrOutput['thread_module'] = $arrThreadModule;
		return $arrOutput;
	}
	
	
	/**
	 * @param
	 * @return
	 */
	protected function _getUserList($arrUserList){
		$input = array(
			"user_id" => array_values($arrUserList),
			"need_follow_info" => 0,
			"need_pass_info" => 0,
			"get_icon" => 0
		);
		$res = Tieba_Service::call('user', 'mgetUserDataEx', $input, null, null, 'post', 'php', 'utf-8');
		$arrUserResult = $res['user_info'];
		return $arrUserResult;
	}
	
	/**
	 * 	@param
	 * 	@return
	 */
	protected function _jsonRet($errno,$errmsg,array $arrExtData=array()){
		$arrRet = array(
			'no'=>intval($errno),
			'error'=>strval($errmsg),
			'data'=>$arrExtData,
		);
		echo Bingo_String::array2json($arrRet);
	}
}
