<?php
/**
 * Created by PhpStorm.
 * User: huxiaomei
 * Date: 2019/7/11
 * Time: 下午4:28
 */
require_once('../../../util/smallapp/Collectcard.php');

class outsidePageAction extends Mo_Core_Action {
    protected $strTplName = 'cardCollection.php';
    protected $strTplPath = 'tbwiseusergrow';

    protected $intErrno = 0;
    private $_strTbs = '';
    protected $token = '';

    /**
     * 执行函数
     */
    protected function _execute()
    {
        $this->_input();
        $this->_strTbs = Tieba_Tbs::gene(Mo_Request::$bolLogin);//tbs
        $ret = array(
            'token' => $this->token,
            'tbs' => $this->_strTbs
        );
        $this->_build($ret);
    }

    /**
     * 获取参数及校验
     * @return bool
     */
    protected function _input() {
        $this->token = Bingo_Http_Request::getNoXssSafe('token');
        return true;
    }

    /**
     *build 页面数据
     */
    private function _build($ret) {
        Mo_Response::addViewData('token',$ret['token']);
        Mo_Response::addViewData('tbs',$ret['tbs']);
    }

    /**
     * 跳转到错误页
     * @param  [type] $tag [description]
     * @return [type]          [description]
     */
    private function _redirectToErrorPage($errno) {
        $errorPage = 'https://tieba.baidu.com/mo/q/'.Mo_Core_Url::ROUTER_PRIFORUM_ERROR."?errorno=".$errno;
        Bingo_Http_Response::redirect($errorPage);
    }

    /**
     *打点日志
     */
    public function _log() {
        Tieba_Stlog::addNode('ispv', 1);
    }

}