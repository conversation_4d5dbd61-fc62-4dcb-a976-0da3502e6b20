<?php
/**
 * created by xiaof<PERSON>.
 * email: <EMAIL>
 * file name: inrcDrawCardAction.php
 * create time: 2019-06-27 20:14:12
 * describe: 增加抽卡次数UI 每期首次进入赠抽卡两次
 */

require_once('../../../util/smallapp/Collectcard.php');

class inrcDrawCardAction extends Mo_Core_Action {

    protected $intErrno = 0;
    protected $arrRes = array();
    protected $intPeriod = 0;
    protected $intUserId = 0;

    /**
     * 执行函数
     */
    protected function _execute()
    {
        if ($this->_input()) {
            $this->_process();
        }

        $this->_build();
    }

    /**
     * 获取参数及校验
     * @return bool
     */
    protected function _input() {
        $this->intPeriod = intval(Bingo_Http_Request::getNoXssSafe('periodId'));
        $tbs = Bingo_Http_Request::getNoXssSafe('tbs');
        $this->intUserId = intval(Mo_Request::$intUid);
        //$this->intUserId = 375797966;
        if(!Mo_Request::$bolLogin || empty($this->intUserId)){
            Bingo_Log::warning('input user not login return data is empty ');
            $this->intErrno = Tieba_Errcode::ERR_NVOTE_NOT_LOGIN;
            $this->_buildRet($this->intErrno);
            return false;
        }

        if (empty($this->intPeriod) || empty($tbs)) {
            $this->intErrno = Tieba_Errcode::ERR_PARAM_ERROR;
            $this->_buildRet($this->intErrno);
            return false;
        }

        if( !Tieba_Tbs::check($tbs, true) ){
            //tbs验证失败
            $this->intErrno = Tieba_Errcode::ERR_NVOTE_INVALID_TBS;
            $this->_buildRet($this->intErrno);
            return false;
        }
        //提交接口校验签名
        /*
        if (!$this->_checkSign()) {
            $this->_buildRet(Tieba_Errcode::ERR_BUSI_INVALID_SIGN);
            return false;
        }
        */
        return true;
    }

    /**
     * 处理
     * @return bool
     */
    protected function _process() {
        $arrOut = Util_Smallapp_Collectcard::checkActivityPeriod($this->intPeriod);
        if (!$arrOut) {
            Bingo_Log::warning(__METHOD__.'-call-checkActivityPeriod fail period:['.$this->intPeriod.'],output:[' . serialize($arrOut) . ']');
            $this->intErrno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            return false;
        }
        //增加抽卡机会
        $arrReq = array(
            'user_id' => $this->intUserId,
            'period_id' => $this->intPeriod,
        );
        $arrOut = Tieba_Service::call('common', 'userFinishFirstJoin', $arrReq, null, null, 'post', 'php', 'utf-8');
        Bingo_Log::notice(__METHOD__.'-call-userFinishFirstJoin input:['.serialize($arrReq).'],output:[' . serialize($arrOut) . ']');
        if (!$this->_checkReturn($arrOut)) {
            Bingo_Log::warning(__METHOD__.'-call-userFinishFirstJoin fail input:['.serialize($arrReq).'],output:[' . serialize($arrOut) . ']');
            $this->intErrno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            return false;
        }
        return true;
    }

    /**
     * 构建返回结果
     */
    private function _build() {
        $arrOut = $this->arrRes;
        $this->_buildRet($this->intErrno, $arrOut);

    }

    /**
     * 封装返回格式
     * @param $errno
     * @param array $data
     * @return mixed
     */
    private function _buildRet($errno, $data = array()) {
        Mo_Response::$bolAjaxCommit = true;
        Mo_Response::$intErrno     = $errno;
        Mo_Response::$strError     = Tieba_Error::getErrmsg($errno);
        Mo_Response::$arrErrorData = $data;
        return $errno;
    }

    /**
     * 检查返回结果
     * @param $arrRes
     * @return bool
     */
    private function _checkReturn($arrRes) {
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']) {
            return false;
        }
        return true;
    }

    /**
     *
     */
    public function _log() {
    }
}