<?php
    /**
     * Created by PhpStorm.
     * User: he<PERSON><PERSON><PERSON>
     * Date: 2018/11/6
     * Time: 下午2:17
     */
    class getTopAgreePostAction extends Mo_Core_Action {
        const CUPID_PHOTO_BLACK_LIST = 225;//是否在图片贴吧黑名单当中
        const CUPID_ALBUM_PHOTO = 27; //判断是否开启精
        const CUPID_PHOTO_PB_SWITCH = 230;//是否可以从 看图pb页切换到 图片贴吧
        const CUPID_BAN_PIC_TOPIC = 226; //判断是否禁止开启图片话题贴
        const CUPID_DEFAULT_PHOTO_FRS = 205;//判断是否开启图片贴吧
        const CUPID_BANPOSTPIC = 203; //根据吧判定那些吧不能发图片
        const CUPID_FOREVER_BAN_POWER = 216;//永久封禁权限
        const CUPID_BRANDZONE = 201;//查询是否含有广告贴吧列表
        const CUPID_RULE_PAPER = 287;//查询吧是否
        const FLOOR_PAGE_SIZE = 30;

        //memberVip by ding<PERSON>hu
        const NATIVE_MEMBER_CENTER_URL = 'membercenter:';
        //const NATIVE_MEMBER_CENTER_URL = 'http://tieba.baidu.com/mo/q/tbmall/recallScore?from=icon';
        const MEMBER_VIP_SUPPORT_URL = 'http://tieba.baidu.com/mo/q/celebrity/support?forum_id=';
        const MEMBER_VIP_VIP_URL = 'http://tieba.baidu.com/mo/q/celebrity/vip?forum_id=';
        const MEMBER_VIP_ICON_URL = 'http://imgsrc.baidu.com/forum/pic/item/0d7de31190ef76c6aaa443ad9b16fdfaae516766.jpg';
        const H5_MEMBER_PK_URL = "http://tieba.baidu.com/mo/q/member/pk"; //会员PKUrl
        const NATIVE_MEMBER_BUY_URL = "http://tieba.baidu.com/mo/q/member/pk?jumptoforum=memberbuy"; //会员购买


        // request variables
        private $_intThreadId;
        private $_intFloorRn;
        private $_intWithFloor;
        private $_intForumId;
        private $_intUserId;
        private $_arrPostId = array();

        // variables used in methods
        private $_arrPostInfo = array();
        private $_arrVoiceInfo = array();
        private $_arrUserInfo = array();
        private $_arrForumInfo = array();
        private $_arrAgreeInfo = array();
        private $_arrBlockUserId = array();

        private $_intIsCommReverse = 0;

        private $_arrRetPostList = array();

        /**
         * @return bool
         */
        public function _checkPrivate()
        {
            return true;
        }

        /**
         * @return bool
         */
        private function _input()
        {
            $this->_intThreadId = Mo_Request::get ('kz');
            $this->_intWithFloor = Mo_Request::get('with_floor');
            $this->_arrPostId = explode(',', Mo_Request::get('post_id'));
            $this->_intFloorRn = count($this->_arrPostId);
            $this->_intIsCommReverse = Mo_Request::get('is_comm_reverse');
            $this->_intUserId = Mo_Request::$intUid;
            return true;
        }

        /**
         * @return bool
         */
        public function _process() {
            // 获取回复和楼中楼信息
            $this->_getPostComments();
            // 获取用户语音和来源吧信息
            $this->_getUserAndVoiceAndForumAndAgreeInfo();

            // build from forum info
            $this->_buildPostFromForumInfo();
            // build post agree info
            $this->_buildPostAgreeInfo();

            // 判断用户封禁状态
            $this->_buildBlockInfo();

            Bingo_Timer::start('process_posts');
            // $this->_arrRetPostList = Molib_Util_ProcessPostInfo::build($this->_arrPostInfo, $this->_arrVoiceInfo, $this->_arrUserInfo, $this->_objRequest);
            $this->_arrRetPostList = $this->_processPost();
            Bingo_Timer::end('process_posts');
            return true;
        }

        /**
         * @return array
         */
        private function _processPost() {
            $arrVoice = array();
            foreach ($this->_arrVoiceInfo['ret']['postVoiceList'] as $voice) {
                $arrVoice[$voice['post_id']] = $voice;
            }
            $objPbCondition = self::_getPbContentCondition();
            $objProcessor = new Molib_Util_RichText_ParserStructured();
            foreach ($this->_arrPostInfo as $post) {
                $objResult = $objProcessor->process($objPbCondition, $post['content']);
                $objResult = $objResult->arrContent;
                foreach ($post['comment_info'] as $key => $comment) {
                    $objCommentResult= $objProcessor->process($objPbCondition, $comment['content']);
                    $post['comment_info'][$key]['content'] = $objCommentResult->arrContent;

                    if (!empty($arrVoice[$comment['post_id']])) {
                        $post['comment_info'][$key]['content'][] = array(
                            'during_time' => $arrVoice[$comment['post_id']]['during_time'] * 1000,
                            'type'        => 10,
                            'voice_md5'   => $arrVoice[$comment['post_id']]['voice_md5'],
                        );
                    }
                }
                if (!empty($arrVoice[$post['post_id']])) {
                    $objResult[] = array(
                        'during_time' => intval($arrVoice[$post['post_id']]['during_time']) * 1000,
                        'type'        => 10,
                        'voice_md5'   => $arrVoice[$post['post_id']]['voice_md5'],
                    );
                }
                $arrTopPost[] = array(
                    'tid' => $post['thread_id'],
                    'pid' => $post['post_id'],
                    'title'     => $post['title'],
                    'username'  => $post['username'],
                    'user_nickname' => $this->_arrPostInfo[$post['user_id']]['user_nickname'],
                    'user_id'       => $post['user_id'],
                    'forum_name' => $post['word'],
                    'forum_id'   => $post['forum_id'],
                    'agree'      => $post['agree'],
                    'content'    => $objResult,
                    'time'       => $post['now_time'],
                    'portrait'   => Tieba_Ucrypt::encode( $post['user_id'], Molib_Util_Encode::convertUTF8ToGBK( $post['username'] ), $this->_arrUserInfo[$post['user_id']]['portrait_time'] ),
                    'comment_info' => $post['comment_info'],
                    'comment_num'     => $post['comment_num'],
                );
            }
            return $arrTopPost;
        }

        /**
         * @return bool
         */
        public function _execute()
        {
            Mo_Response::$bolAjaxCommit = true; //标记返回值类型为json
            if ($this->_input()) {
                $this->_process();
            }
            $this->_build();
        }

        /**
         * @param $arrRetPostList
         */
        private function _build()
        {
            $arrOutData = array(
                'post_list' => $this->_arrRetPostList,
            );

            Mo_Response::$intErrno = 0;
            Mo_Response::$strError = 'success';
            Mo_Response::$arrErrorData = $arrOutData;
        }

        /**
         * @return bool
         */
        private function _getPostComments()
        {

            $objMulti = new Tieba_Multi('post_comment');
            // register post info
            $arrPostInput = array(
                'post_ids' => $this->_arrPostId,
                'structured_content' => 1, // 结构化数据
            );
            $objMulti->register('post:getPostInfo', new Tieba_Service('post'), array(
                'serviceName' => 'post',
                'method' => 'getPostInfo',
                'input' => $arrPostInput,
                'ie' => 'utf-8',
            ));

            // register mask info
            $arrMaskInput = array(
                'input' => array(
                    'thread_ids' => array(),
                    "post_ids" => $this->_arrPostId,
                    'comment_infos' => array(),
                )
            );
            $objMulti->register('post:getMaskInfo', new Tieba_Service('post'), array(
                'serviceName' => 'post',
                'method' => 'getMaskInfo',
                'input' => $arrMaskInput,
            ));

            // register comments info
            foreach ($this->_arrPostId as $intPostId) {
                $arrCommentInput = array(
                    'thread_id' => $this->_intThreadId, //帖子id
                    'post_id' => $intPostId,
                    'comment_id' => 1,
                    'offset' => 0,
                    'res_num' => $this->_intFloorRn,
                    'status' => 0, //帖子状态
                    'has_mask' => 1,
                    'is_comm_reverse' => $this->_intIsCommReverse,
                    'structured_content' => 1,
                );
                $objMulti->register('post:getComments:' . $intPostId, new Tieba_Service('post'), array(
                    'serviceName' => 'post',
                    'method' => 'getComments',
                    'input' => $arrCommentInput,
                    'ie' => 'utf-8',
                ));
            }

            $objMulti->call();

            // get Post result
            $arrPostResult = $objMulti->getResult('post:getPostInfo');
            if (false === $arrPostResult || Tieba_Errcode::ERR_SUCCESS !== $arrPostResult['errno']
                || empty($arrPostResult['output'])
            ) {
                Bingo_Log::warning('call post:getPostInfo failed! input:[' . serialize($arrPostInput) . ']' . 'output:[' . serialize($arrPostResult) . ']');
                return false;
            }
            $arrPostInfoList = $arrPostResult['output'];

            // get Mask result
            $arrMaskResult = $objMulti->getResult('post:getMaskInfo');
            if (false === $arrMaskResult || Tieba_Errcode::ERR_SUCCESS !== $arrMaskResult['errno']
                || empty($arrMaskResult['output'])
            ) {
                Bingo_Log::warning('call post:getMaskInfo failed! input:[' . serialize($arrMaskInput) . ']' . 'output:[' . serialize($arrMaskResult) . ']');
                return false;
            }
            $arrMaskInfoList = $arrMaskResult['output']['posts_mask_status'];

            // get Comment result
            foreach ($this->_arrPostId as $intPostId) {

                $arrCommentResult = $objMulti->getResult('post:getComments:' . $intPostId);

                if (false === $arrCommentResult || Tieba_Errcode::ERR_SUCCESS !== $arrCommentResult['errno']) {
                    Bingo_Log::warning('call post:getComments failed! input:[' . serialize($arrCommentInput) . ']' . 'output:[' . serialize($arrCommentResult) . ']');
                    continue;
                }

                if (isset($arrCommentResult['output']['post_infos'])) {
                    $arrComments[$intPostId] = $arrCommentResult['output'];
                }

                unset($arrCommentResult);
            }

            $this->_buildPostComments($arrPostInfoList, $arrMaskInfoList, $arrComments);

            return true;
        }

        /**
         * 获取回复的用户/语音/来源吧信息
         * @return bool
         */
        private function _getUserAndVoiceAndForumAndAgreeInfo()
        {
            $arrVoiceParam = array();
            $arrUserParam = array();
            $arrForumParam = array();

            $arrPostUid = array();


            // 拼接参数
            foreach ($this->_arrPostInfo as $arrPost) {
                $arrPostUid[] = $arrPost['user_id'];
                if (intval($arrPost['ptype']) === 1) {
                    $intPid = intval($arrPost['post_id']);
                    $arrVoiceParam[] = array(
                        'thread_id' => $this->_intThreadId,
                        'post_id' => $intPid,
                    );
                }
                //TODO test
//			$arrPost['forum_id_ori'] = 59099;
                // 来源吧字段
                if (isset($arrPost['forum_id_ori'])) {
                    $arrForumParam[] = intval($arrPost['forum_id_ori']);
                }
                $arrUserParam[$arrPost['user_id']] = $arrPost['user_id'];
                if (empty($arrPost['comment_info'])) {
                    continue;
                }
                foreach ($arrPost['comment_info'] as $arrComment) {
                    if (intval($arrComment['ptype']) === 1) {
                        $intSPid = intval($arrComment['comment_id']);
                        $arrVoiceParam[] = array(
                            'thread_id' => $this->_intThreadId,
                            'post_id' => $intSPid,
                        );
                    }
                    $arrUserParam[$arrComment['user_id']] = $arrComment['user_id'];
                }
            }

            $objMulti = new Tieba_Multi('user_voice');

            // 点赞信息
            if ($this->_intUserId != 0) {
                $strAgreeKey = 'agree:getAgreeInfo';
                $arrAgreeReq = array(
                    'thread_id' => $this->_intThreadId,
                    'user_id' => $this->_intUserId,
                    'post_ids' => $this->_arrPostId,
                );
                $objMulti->register($strAgreeKey, new Tieba_Service('agree'), array(
                    'serviceName' => 'agree',
                    'method' => 'getAgreeByUserIdAndPostIds',
                    'input' => $arrAgreeReq,
                ));
            }

            // 语音回复信息
            if (!empty($arrVoiceParam)) {

                $strVoiceKey = 'voice:getVoiceInfo';
                $arrVoiceReq = array(
                    'pids' => $arrVoiceParam,
                );

                $objMulti->register($strVoiceKey, new Tieba_Service('voice'), array(
                    'serviceName' => 'voice',
                    'method' => 'getThreadVoiceInfosByPids',
                    'input' => $arrVoiceReq,
                ));
            }

            // 来源吧信息
            if (!empty($arrForumParam)) {
                $strForumKey = 'forum:getFnameInfo';
                $arrForumReq = array(
                    'forum_id' => $arrForumParam,
                );
                $objMulti->register($strForumKey, new Tieba_Service('forum'), array(
                    'serviceName' => 'forum',
                    'method' => 'getFnameByFid',
                    'input' => $arrForumReq,
                    'ie' => 'utf-8',
                ));
            }

            // 获取用户信息
            $strUserKey = 'user:getUserForumInfo';
            $arrUserReq = array(
                'forum_id' => $this->_intForumId,
                'get_icon' => 0,
                'user_id' => array_values($arrUserParam),
            );

            $objMulti->register($strUserKey, new Tieba_Service('user'), array(
                'serviceName' => 'user',
                'method' => 'mgetUserForumInfo',
                'input' => $arrUserReq,
                'ie' => 'utf-8',
            ));

            // 获取用户屏蔽状态
            $strUserStateKey = 'userstate:mgetUserState';
            $arrBlockReq = array(
                'keys' => $arrPostUid,
                'forum_id' => 0,
                'service_type' => 'blockid',
                "opgroup" => 'system', //操作群组类型
            );
            $objMulti->register($strUserStateKey, new Tieba_Service('userstate'), array(
                'serviceName' => 'userstate',
                'method' => 'mgetUserState',
                'ie' => 'utf-8',
                'input' => $arrBlockReq,
            ));


            $objMulti->call();

            // get Agree result
            if ($this->_intUserId != 0) {
                $arrAgreeResult = $objMulti->getResult($strAgreeKey);
                if (false === $arrAgreeResult || Tieba_Errcode::ERR_SUCCESS !== $arrAgreeResult['errno']) {
                    Bingo_Log::warning('call agree:getAgreeByUserIdAndPostIds failed! input:[' . serialize($arrAgreeReq) . ']' . 'output:[' . serialize($arrAgreeResult) . ']');
                } else {
                    $this->_arrAgreeInfo = $arrAgreeResult['data']['map'];
                }
            }

            // get Voice result
            if (!empty($arrVoiceParam)) {
                $arrVoiceResult = $objMulti->getResult($strVoiceKey);
                if (false === $arrVoiceResult || Tieba_Errcode::ERR_SUCCESS !== $arrVoiceResult['errno']) {
                    Bingo_Log::warning('call voice:getThreadVoiceInfosByPids failed! input:[' . serialize($arrVoiceReq) . ']' . 'output:[' . serialize($arrVoiceResult) . ']');
                } else {
                    $this->_arrVoiceInfo = $arrVoiceResult;
                }

            }

//		Bingo_Log::warning(var_export($this->_arrVoiceInfo, true));

            // get Forum result
            if (!empty($arrForumParam)) {
                $arrForumResult = $objMulti->getResult($strForumKey);
                if (false === $arrForumResult || Tieba_Errcode::ERR_SUCCESS !== $arrForumResult['errno']) {
                    Bingo_Log::warning('call forum:getFnameByFid failed! input:[' . serialize($arrForumReq) . ']' . 'output:[' . serialize($arrVoiceResult) . ']');
                } else {
                    $arrForumData = array();
                    foreach ($arrForumResult['forum_name'] as $intFid => $arrForumInfo) {
                        if ($arrForumInfo['exist'] == 1) {
                            $arrForumData[$intFid] = $arrForumInfo;
                        }
                    }
                    $this->_arrForumInfo = $arrForumData;
                }
            }

            // get User result
            $arrUserResult = $objMulti->getResult($strUserKey);
            if (false === $arrUserResult || Tieba_Errcode::ERR_SUCCESS !== $arrUserResult['errno']) {
                Bingo_Log::warning('call user:mgetUserForumInfo failed! input:[' . serialize($arrUserReq) . ']' . 'output:[' . serialize($arrUserResult) . ']');
            } else {
                foreach ($arrUserResult['user_info'] as $value) {
                    $arrUserData[$value['user_id']] = $value;
                }
                $this->_arrUserInfo = $arrUserData;
            }

            // 回复用户屏蔽状态
            $arrBlockResult = $objMulti->getResult($strUserStateKey);
            if (false == $arrBlockResult || Tieba_Errcode::ERR_SUCCESS !== $arrBlockResult['errno']) {
                Bingo_Log::warning('query userstate_mgetUserState failed! input:[' . serialize($arrBlockReq) . ']' . 'output:[' . serialize($arrBlockResult) . ']');
                return false;
            }

            if (!empty($arrBlockResult['output'])) {
                foreach ($arrBlockResult['output'] as $intUid => $arrBlockInfo) {
                    if (!empty($arrBlockInfo['list'])) {
                        $this->_arrBlockUserId[$intUid] = $intUid;
                    }
                }
            }

            return true;
        }

        /**
         * combine post info and comment list
         * @param $arrPostInfoList
         * @param $arrMaskInfoList
         * @param $arrComments
         * @return bool
         */
        private function _buildPostComments($arrPostInfoList, $arrMaskInfoList, $arrComments)
        {

            // build comments list
            foreach ($arrComments as $intPostId => &$arrCommentList) {
                foreach ($arrCommentList['post_infos'] as &$commentInfo) {
                    unset($commentInfo['quote']);
                    // exchange post_id and comment_id
                    $commentInfo['comment_id'] = $commentInfo['post_id'];
                    $commentInfo['post_id'] = $intPostId;

                    // set forumId
                    $this->_intForumId = $commentInfo['forum_id'];
                }
            }

            $arrOutPostList = array();
            // rebuild post info and comments
            foreach ($arrPostInfoList as $arrPostInfo) {
                $intPostId = $arrPostInfo['post_id'];
                // 回复被删或屏蔽
                if (isset($arrMaskInfoList[$intPostId]) &&
                    (1 == $arrMaskInfoList[$intPostId]['is_key_deleted'] ||
                        1 == $arrMaskInfoList[$intPostId]['is_key_mask'])
                ) {
                    continue;
                }
                // 存在楼中楼
                if (isset($arrComments[$intPostId])) {
                    $arrCommentInfo = $arrComments[$intPostId];
                    $arrPostInfo['comment_info'] = $arrCommentInfo['post_infos'];
                    $arrPostInfo['comment_num'] = $arrCommentInfo['valid_post_num'];
                    $arrPostInfo['comment_offset'] = $arrCommentInfo['offset'];
                    $arrPostInfo['comment_list_num'] = count($arrCommentInfo['post_infos']);
                    unset($arrCommentInfo);
                }
                $arrOutPostList[$intPostId] = $arrPostInfo;

            }

            $this->_arrPostInfo = $arrOutPostList;

            return true;
        }

        /**
         * build post agree info
         * 合并回复的赞字段
         * @return bool
         */
        private function _buildPostAgreeInfo()
        {
            foreach ($this->_arrPostInfo as $intPostId => &$arrPost) {
                // build agree info
                $arrPost['agree']['agree_num'] = isset($arrPost['agree_num']) ? $arrPost['agree_num'] : 0;
                $arrPost['agree']['has_agree'] = isset($this->_arrAgreeInfo[$intPostId]) ? 1 : 0;
            }
            return true;
        }

        /**
         * build from_forum info
         * 合并回复的来源吧字段
         * @return bool
         */
        private function _buildPostFromForumInfo()
        {
            if (empty($this->_arrForumInfo)) {
                return true;
            }
            foreach ($this->_arrPostInfo as &$arrPost) {
                // TODO test
//			$arrPost['forum_id_ori'] = 59099;
                if (isset($arrPost['forum_id_ori']) && isset($this->_arrForumInfo[$arrPost['forum_id_ori']])) {
                    $arrPost['from_forum'] = array(
                        'id' => $this->_arrForumInfo[$arrPost['forum_id_ori']]['forum_id'],
                        'name' => $this->_arrForumInfo[$arrPost['forum_id_ori']]['forum_name'],
                    );
                }
            }
            return true;
        }


        /**
         * build post user block info
         * @return bool
         */
        private function _buildBlockInfo()
        {
            foreach ($this->_arrPostInfo as $intPostId => $arrPost) {
                $intUid = $arrPost['user_id'];
                if (isset($this->_arrBlockUserId[$intUid])) {
                    unset($this->_arrPostInfo[$intPostId]);
                }
            }
            return true;
        }

        /**
         *
         */
        public function _log() {

        }

        /**
         * @return Mo_Util_Richtext_RichTextProcessorCondition
         */
        private function _getPbContentCondition(){
            $objCondition = new Mo_Util_Richtext_RichTextProcessorCondition();
            $objCondition->strSmilePicUrlPre = Conf::get('simle_pic_url_pre','');
            $objCondition->intTextSegLen = Mo_Define::PB_SEGMENT_LENGHTN;
            $objCondition->bolDisplayNewLine = true;
            $objCondition->intNewLineCount = 1;
            $objCondition->bolBlankTrim = true;
            $objCondition->intBigImgWidth = 4000;
            $objCondition->bolTextDisplayOther = true;
            $objCondition->bolShowLoadingTip = true;
            $objCondition->intSmileDisplayCount = 5;
            $objCondition->bolShowEmbedUrl = true;
            $objCondition->bolProcPhoneNum=true;
            $objCondition->strVedioReplaceText = Mo_Define::PB_VEDIO_REPLACE_TEXT;
            $objCondition->intSmallImgWidth = Mo_Request::getCookie('SET_PB_IMAGE_WIDTH', 250);
            $paramPd = Mo_Request::get('pd', 0);

            if( $paramPd == Mo_Define::PD_MUSIC || $paramPd == Mo_Define::PD_LOCAL_FORUM || Mo_Request::get(Mo_Define::MARK_YIPINGTAI, '0') ==1 || $paramPd==Mo_Define::PD_STAR_FANS){
                $objCondition->isLazyLoad = false;
            }else{
                $objCondition->isLazyLoad = true;
            }


            if (Mo_Define::NET_TYPE_WIFI == Mo_Request::$intNetType) {
                $objCondition->intSmallImgQulity = 100;
            }
            else {
                $objCondition->intSmallImgQulity = 80;
            }

            if('iphone' == Mo_Request::$strMobileModel){
                $objCondition->bolVideoM3u8First = true;//ios系统支持m3u8格式视频，而android则不支持
                $objCondition->bolParseBdhd = false;
            }else{
                $objCondition->bolVideoM3u8First = false;
                $objCondition->bolParseBdhd = true; //android 客户端可支持bdhd视频
            }
            $objCondition->intVideoImgWidth = 130;
            $objCondition->intVideoImgHeight = 96;
            $objCondition->intVideoImgQuality = 80;
            return $objCondition;
        }
    }