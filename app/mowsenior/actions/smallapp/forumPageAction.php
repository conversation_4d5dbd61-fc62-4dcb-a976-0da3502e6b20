<?php

/**
 * <AUTHOR> <heliuyong>
 * @version 2018-01-10
 */
require_once MOBILE_UI_PATH . '/../mowsenior/util/smallapp/Redis.php';
class forumPageAction extends Mo_Core_Action
{
    const REDIS_PREFIX = 'smallapp_top_forum_';
    private $_intUserId = 0;
    private $_intPn = 1;
    private $_mCaller = null;
    private $_intErrno = 0;
    private $_strErrmsg = 'success';

    // 用户全部关注吧idx
    private $_arrLikeForumIdx = array();
    // 用户当前页关注吧idx
    private $_arrCurLikeForumIdx = array();
    // 推荐吧idx
    private $_arrRecommendForumIdx = array();


    // 关注吧的list
    private $_arrLikedForum = array();
    // 置顶吧list
    private $_arrTopForum   = array();
    private $_arrTopForumList   = array();
    // 推荐的吧list
    private $_arrRecommendForum = array();
    // 用户登录信息
    private $_arrUserInfo = array();

    // 每页返回关注的吧条数
    const LIKE_FORUM_RN = 100;
    // 分批查询签到吧个数
    const SIGN_PER_CALL = 10;
    // 分批查询吧信息个数
    const FORUM_PER_CALL = 20;

    // 推荐吧请求的吧个数
    const RECOMMEND_REQ_FORUM_CNT = 200;
    // 推荐吧返回的吧个数
    const RECOMMEND_RES_FORUM_CNT = 100;

    /**
     * [_execute description]
     * @return [type] [description]
     */
    protected function _execute()
    {
        if ($this->_input()) {
            $this->_process();
        }
        $this->_build();
    }

    /**
     * [_input description]
     * @return [type] [description]
     */
    private function _input()
    {
        $this->_intUserId = Mo_Request::$intUid;
        $this->_intPn = intval(Mo_Request::get('pn', 1));
        $this->_mCaller = new Tieba_Multi('forum_page');
        return true;
    }

    /**
     * [_process description]
     * @return [type] [description]
     *
     */
    private function _process()
    {
        // 查询用户关注吧list
        if ($this->_intUserId > 0) {
            $this->_getLikeIdxPre(); // 获取关注吧id list
        }

        $this->_mCaller->call();

        if ($this->_intUserId > 0) {
            $this->_getLikeIdxExec();
        }


        $this->_mCaller = new Tieba_Multi('forum_data');
        // 关注吧不为空返回关注吧list
        if (!empty($this->_arrCurLikeForumIdx)) {
            $arrForumId = array_keys($this->_arrCurLikeForumIdx);
            // 置顶吧列表信息
            $this->_getTopForumId();
            if (!empty($this->_arrTopForum)){
                $arrForumId = array_merge($arrForumId, $this->_arrTopForum);
            }
            $this->_getSignForumPre($arrForumId);
            $this->_getForumInfoPre($arrForumId);
            $this->_mCaller->call();

            $arrSignForum = $this->_getSignForumExec($arrForumId);
            $arrForumInfo = $this->_getForumInfoExec($arrForumId);

            // 构造关注吧列表信息
            $this->_buildLikeForum($arrSignForum, $arrForumInfo);
        } else {

            // 获取推荐吧id list
            $this->_getRecommendForumPre();
            $this->_mCaller->call();
            $this->_getRecommendForumExec();

            // 关注吧为空时 构造推荐吧list
            $arrForumId = array_keys($this->_arrRecommendForumIdx);
            $this->_getForumInfoPre($arrForumId);
            $this->_mCaller->call();

            $arrForumInfo = $this->_getForumInfoExec($arrForumId);
//            Bingo_Log::warning(var_export($arrForumInfo, true));
            // 构造推荐吧列表信息
            $this->_buildRecommendForum($arrForumInfo);
        }

        if (Mo_Request::$bolLogin) {
            $this->_arrUserInfo['user_id']  = $this->_intUserId;
            $this->_arrUserInfo['is_login'] = true;
        }
    }

    /**
     * 构造关注吧列表信息
     * @param $arrSignForum
     * @param $arrForumInfo
     */
    private function _buildLikeForum($arrSignForum, $arrForumInfo) {
        $arrLikeForum = $this->_arrCurLikeForumIdx;
        $arrRet = array();
        foreach ($this->_arrTopForum as $item) {
            $forumId[$item] = $item;
        }
        $arrTopForum = array();
        foreach ($arrLikeForum as $arrForum) {
            $arrTmp = array();

            $arrTmp['forum_id'] = $arrForum['forum_id'];
            $arrTmp['forum_name'] = $arrForum['forum_name'];
            if (empty($arrForum['forum_name'])) {
                continue;
            }
            $arrTmp['level_id'] = strval($arrForum['level_id']);
            $arrTmp['is_like'] = intval($arrForum['cur_score']) > 0 ? true : false;
            if (isset($arrSignForum[$arrForum['forum_id']]['is_sign_in'])) {
                $arrTmp['is_sign'] = $arrSignForum[$arrForum['forum_id']]['is_sign_in'];
            }
            $btx = $arrForumInfo[$arrForum['forum_id']];
            $arrStyle = array();
            if (isset($btx['attrs']['card_p1'])) {
                $arrStyle = Bingo_String::json2array($btx['attrs']['card_p1']['style_name']);
            } elseif (isset($btx['card']['avatar'])) {
                $arrStyle['avatar'] = $btx['card']['avatar'];
            }
            $arrTmp['is_brand_forum'] = $btx['attrs']['special_forum']['is_brand_forum'] == 1 ? 1 : 0;
            $arrTmp['forum_avatar'] = isset($arrStyle['avatar']) ? $arrStyle['avatar'] : '';
            $arrTmp['slogan'] = isset($arrStyle['slogan']) ? $arrStyle['slogan'] : '';
            if (isset($forumId[$arrTmp['forum_id']])) {
                $arrTopForum[$arrTmp['forum_id']] = array(
                    'forum_id'      => $arrTmp['forum_id'],
                    'forum_name'    => $arrTmp['forum_name'],
                    'forum_avatar'  => $arrTmp['forum_avatar'],
                    'level_id'      => $arrTmp['level_id'],
                    'is_sign'       => $arrTmp['is_sign'],
                    'is_brand_forum'=> $arrTmp['is_brand_forum'],
                );
                continue;
            }
            $arrRet[] = $arrTmp;
        }

        foreach ($this->_arrTopForum as $item) {
            if (!empty($arrTopForum[$item])) {
                $this->_arrTopForumList[] = $arrTopForum[$item];
            }
        }
        
        foreach ($arrRet as &$arrForum) {
            $arrForum = Molib_Util_Array::fetchArray($arrForum,
                array(
                    'forum_id',
                    'forum_name',
                    'forum_avatar',
                    'level_id',
                    'is_sign',
                    'is_brand_forum',
                )
            );
        }
        $this->_arrLikedForum = array(
            'list' => $arrRet,
            'page' => array(
                'cur_page' => $this->_intPn,
                'total_page' => ceil($this->_intLikeForumCnt / self::LIKE_FORUM_RN),
            )
        );
    }

    /**
     * 构造置顶吧列表信息
     */
    private function _getTopForumId() {
        if (Mo_Request::$bolLogin) {
            $redisInput = array(
                'key'   => self::REDIS_PREFIX . Mo_Request::$intUid,
                'start' => 0,
                'stop'  => -1,
            );
            $ret = Util_Smallapp_Redis::call('ZREVRANGE', $redisInput);
            if ($ret['err_no'] != 0) {
                Bingo_Log::warning('call redis failed');
            } else {
                $this->_arrTopForum = $ret['ret'][self::REDIS_PREFIX . Mo_Request::$intUid];
                Bingo_Log::warning(print_r($this->_arrTopForum, 1));
            }
        }
    }

    /**
     * 构造推荐吧信息
     * @param $arrForumInfo
     */
    private function _buildRecommendForum($arrForumInfo) {
        $arrRet = array();
        foreach ($arrForumInfo as $arrForum) {
            $intForumId = $arrForum['forum_id'];
            if (isset($this->_arrLikeForumIdx[$intForumId])) {
                continue;
            }
            $arrStyle = array();
            if (isset($arrForum['attrs']['card_p1'])) {
                $arrStyle = Bingo_String::json2array($arrForum['attrs']['card_p1']['style_name']);
            } elseif (isset($arrForum['card']['avatar'])) {
                $arrStyle['avatar'] = $arrForum['card']['avatar'];
            }
            $arrRet[] = array(
                'forum_id' => $arrForum['forum_name']['forum_id'],
                'forum_name' => $arrForum['forum_name']['forum_name'],
                'forum_avatar' => isset($arrStyle['avatar']) ? $arrStyle['avatar'] : '',
                'forum_intro' => isset($arrStyle['slogan']) ? $arrStyle['slogan'] : '',
            );
        }

        $this->_arrRecommendForum = $arrRet;
    }


    /**
     * pre查询用户关注的吧id
     */
    private function _getLikeIdxPre() {

        // 获取用户所有关注吧idx  getLikeForumListIndexBySortTime
        $arrInput = array(
            'user_id' => $this->_intUserId,
            'check_forum' => 1
        );
        $this->_mCaller->register('likeIdx', new Tieba_Service('perm'), array(
            'serviceName' => 'perm',
            'method' => 'getLikeForumListIndex',
            'input' => $arrInput,
            'ie' => 'utf-8',
        ));

        $stType = Mo_Request::get('st', 0);
        $method = $stType == 1 ? 'getLikeForumListBySortTime' : 'getLikeForumList';
        // 获取当前页关注吧list
        $arrInput['page_no'] = $this->_intPn;
        $arrInput['page_size'] = self::LIKE_FORUM_RN;
        $this->_mCaller->register('getLike', new Tieba_Service('perm'), array(
            'serviceName' => 'perm',
            'method' => $method,
            'input' => $arrInput,
            'ie' => 'utf-8',
        ));
    }

    /**
     * exec查询用户关注吧id
     * @return bool
     */
    private function _getLikeIdxExec() {
        // 用户所有关注吧idx
        $arrOut = $this->_mCaller->getResult('likeIdx');
        if (false == $arrOut || $arrOut ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('get perm_getLikeForumListIndex return error. uid is: ' . $this->_intUserId. 'output: '. serialize($arrOut));
        }
        if (!empty($arrOut['output']['member_list'])) {
            $this->_arrLikeForumIdx = $arrOut['output']['member_list'];
        }

        // 用户当前页关注吧idx
        $arrOut = $this->_mCaller->getResult('getLike');
        if (false == $arrOut || $arrOut ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('get perm_getLikeForumList return error. uid is: ' . $this->_intUserId. 'output: '. serialize($arrOut));
        }
        if (!empty($arrOut['output']['member_list'])) {
            foreach ($arrOut['output']['member_list'] as $member) {
                $this->_arrCurLikeForumIdx[$member['forum_id']] = $member;
            }
        }

        // 关注吧数量
        $this->_intLikeForumCnt = $arrOut['output']['member_count'];

        return true;
    }

    /**
     * pre查询推荐的吧
     */
    private function _getRecommendForumPre() {
        $arrInput = array(
            'count' => self::RECOMMEND_REQ_FORUM_CNT,
            'check_forum' => 1,
        );
        $this->_mCaller->register('recommend_forum', new Tieba_Service('common'), array(
            'serviceName' => 'common',
            'method' => 'getTopForumByDayThreadCnt',
            'input' => $arrInput,
        ));
    }

    /**
     * exec查询推荐的吧
     */
    private function _getRecommendForumExec() {
        $arrOut = $this->_mCaller->getResult('recommend_forum');
        if (false == $arrOut || $arrOut ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('get common_getTopForumByDayThreadCnt return error. output: '. serialize($arrOut));
        }
        if (!empty($arrOut['data'])) {
            foreach ($arrOut['data'] as $intForumId) {
                $this->_arrRecommendForumIdx[$intForumId] = $intForumId;
            }
        }
    }

    /**
     * pre获取签到吧信息
     * @param $arrForumId
     */
    private function _getSignForumPre($arrForumId) {

        $arrChunkForumId = array_chunk($arrForumId, self::SIGN_PER_CALL);
        foreach ($arrChunkForumId as $i => $arrTmpForumId) {
            $arrInput = array(
                'forum_id' => $arrTmpForumId,
                'user_id' => $this->_intUserId,
            );
            $this->_mCaller->register('sign_' . $i, new Tieba_Service('sign'), array(
                    'serviceName' => 'sign',
                    'method' => 'getUserSignForums',
                    'input' => $arrInput,
            ));
        }
    }

    /**
     * exec获取签到吧信息
     * @param $arrForumId
     * @return array
     */
    private function _getSignForumExec($arrForumId) {
        $arrChunkForumId = array_chunk($arrForumId, self::SIGN_PER_CALL);
        $arrSignForum = array();
        foreach ($arrChunkForumId as $i => $arrTmpForumId) {
            $arrSignOut = $this->_mCaller->getResult('sign_' . $i);
            if (is_array($arrSignOut['arr_user_info'])) {
                $arrSignForum = (array)$arrSignForum + (array)$arrSignOut['arr_user_info'];
            }
        }
        return $arrSignForum;
    }

    /**
     * pre获取吧信息
     * @param $arrForumId
     */
    private function _getForumInfoPre($arrForumId) {
        $arrChunkForumId = array_chunk($arrForumId, self::FORUM_PER_CALL);
        foreach ($arrChunkForumId as $i => $arrTmpForumId) {
            $arrInput = array(
                'forum_id' => $arrTmpForumId,
            );
            $this->_mCaller->register('btx_' . $i, new Tieba_Service('forum'), array(
                'serviceName' => 'forum',
                'method' => 'mgetBtxInfo',
                'input' => $arrInput,
                'ie' => 'utf-8',
            ));
        }

    }

    /**
     * exec获取吧信息
     * @param $arrForumId
     * @return array
     */
    private function _getForumInfoExec($arrForumId) {
        $arrChunkForumId = array_chunk($arrForumId, self::FORUM_PER_CALL);
        $arrForumInfo = array();
        foreach ($arrChunkForumId as $i => $arrTmpForumId) {
            $arrForumOut = $this->_mCaller->getResult('btx_' . $i);
            if (is_array($arrForumOut['output'])) {
                $arrForumInfo = (array)$arrForumInfo + (array)$arrForumOut['output'];
            }
        }
        return $arrForumInfo;
    }


    /**
     * 返回数据
     */
    private function _build()
    {
        $arrOutData = array(
            'liked_forums' => $this->_arrLikedForum,
            'recommend_forums' => $this->_arrRecommendForum,
            'topForum'      => $this->_arrTopForumList,
            'tbs'          => Tieba_Tbs::gene(Mo_Request::$bolLogin),
            'login_user_info' => $this->_arrUserInfo,
        );
        Mo_Response::$bolAjaxCommit = true; //标记返回值类型为json
        Mo_Response::$intErrno = $this->_intErrno;
        Mo_Response::$strError = $this->_strErrmsg;
        Mo_Response::$arrErrorData = $arrOutData;
    }


    /**
     * [_log description]
     * @return [type] [description]
     */
    protected function _log()
    {
//        Tieba_Stlog::add('ispv', 1);
    }
}

