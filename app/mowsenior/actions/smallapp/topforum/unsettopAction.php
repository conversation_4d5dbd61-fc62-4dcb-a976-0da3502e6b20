<?php
    /**
     * Created by PhpStorm.
     * User: he<PERSON><PERSON><PERSON>
     * Date: 2018/10/29
     * Time: 下午3:48
     */
    require_once MOBILE_UI_PATH . '/../mowsenior/util/smallapp/Redis.php';
    class unsettopAction extends Mo_Core_Action
    {
        const REDIS_PREFIX = 'smallapp_top_forum_';
        private $_intFid = 0;
        /**
         * [_execute description]
         * @return [type] [description]
         */
        protected function _execute()
        {
            Mo_Response::$bolAjaxCommit = true; //标记返回值类型为json
            Mo_Response::$intErrno = 0;
            Mo_Response::$strError = 'success';
            if ($this->_input()) {
                $this->_process();
            }
            $this->_build();
        }

        /**
         * [_input description]
         * @return [type] [description]
         */
        private function _input()
        {
            $this->_intFid = Mo_Request::get('fid', 0);
            return true;
        }

        /**
         * [_process description]
         * @return [type] [description]
         *
         */
        private function _process()
        {
            try {
                if (Mo_Request::$bolLogin) {
                    //tbs验证
                    $tbs = strval(Mo_request::get('tbs'));
                    if( empty($tbs) ){
                        throw new Exception("tbs is empty", Tieba_Errcode::ERR_PARAM_ERROR);
                    }
                    if( !Tieba_Tbs::check($tbs, true) ){
                        //tbs验证失败
                        throw new Exception("tbs check fail!", Tieba_Errcode::ERR_NVOTE_INVALID_TBS);
                    }
                    if ($this->_intFid <= 0) {
                        Mo_Response::$intErrno = Tieba_Errcode::ERR_PARAM_ERROR;
                        Mo_Response::$strError = 'param error';
                        return false;
                    }
                    $redisInput = array(
                        'key'   => self::REDIS_PREFIX . Mo_Request::$intUid,
                        'member' => $this->_intFid,
                    );
                    $ret = Util_Smallapp_Redis::call('ZREM', $redisInput);
                    if ($ret['err_no'] != 0) {
                        Bingo_Log::warning('call redis failed');
                        Mo_Response::$intErrno = Tieba_Errcode::ERR_UNKOWN;
                        Mo_Response::$strError = '删除失败';
                        return false;
                    }
                    return true;
                }
            } catch (Exception $e) {
                Bingo_Log::warning( "no=".$e->getCode() ." msg=".$e->getMessage() );
                Mo_Response::$intErrno = $e->getCode();
                Mo_Response::$strError = $e->getMessage();
            }
        }
        /**
         * 组装
         * @return [type] [description]
         */
        private function _build()
        {
            Mo_Response::$arrErrorData = array();
        }

        //必须实现
        /**
         * [_log description]
         * @return [type] [description]
         */
        protected function _log()
        {
        }
    }

