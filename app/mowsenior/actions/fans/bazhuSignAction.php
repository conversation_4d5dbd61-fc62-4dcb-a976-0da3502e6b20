<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-06-10 16:03:40
 * @ 吧报名哦
 * @version
 */
class bazhuSignAction extends Util_Base {
	const LEVEL_1_NAME                 = '娱乐明星';
	const ACT_FORUM_TYPE               = 1;

    protected $_strForumName           = '';
    protected $_intForumId             = 0;
    protected $_arrRegisterInfo        = array();
	protected $_intRegisterNum         = 0;
    protected $_arrBlockDir            = array(
        '娱乐明星话题' => 1,
        '其他粉丝组织' => 1,
    );
    public function execute(){
        try{
            // 不是吧主，还申请个啥
            //$this->_isManagerOrPm();
            $this->_check();
            //$this->_checkCommit();
            $this->_getUserInfo();
            $this->_execute();
            $this->_jsonRet(0,'success', array('count' => $this->_intRegisterNum));
        }catch (Util_Exception $e){
            $this->_jsonRet($e->getCode(),$e->getMessage());
        }
    }

    protected function _check() {
        // 注意防范XSS
        $this->_strForumName = Bingo_Http_Request::get('forum_name', '');
        $this->_arrRegisterInfo = array(
            'name'         => Bingo_Http_Request::get('name', ''),
            'tel'          => Bingo_Http_Request::get('phone', ''),
            'qq'           => Bingo_Http_Request::get('qq', ''),
            'address'      => Bingo_Http_Request::get('address', ''),
            'goods'        => Bingo_Http_Request::get('special_good', ''),
            'show_content' => Bingo_Http_Request::get('show_content', ''),
            'extra'        => Bingo_Http_Request::get('extra', ''),
        );
        if (empty($this->_strForumName) || empty($this->_arrRegisterInfo['name']) 
            || empty($this->_arrRegisterInfo['tel']) || empty($this->_arrRegisterInfo['qq'])
            || empty($this->_arrRegisterInfo['goods']) || empty($this->_arrRegisterInfo['show_content']) ) {
			$strMsg = sprintf('forum_name %s register info : %s', $this->_strForumName, serialize($this->_arrRegisterInfo));
			Bingo_Log::warning($strMsg);
            throw new Util_Exception(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
        }

        // 手机号验证
        if (!Util_Function::checkPhone($this->_arrRegisterInfo['tel'])) {
            throw new Util_Exception(Util_Def::ERR_TEL_ERROR, 'phone number invalid');            
        }

        // qq号验证
        if (!preg_match("/^[1-9][0-9]{4,}$/", $this->_arrRegisterInfo['qq'])) {
            throw new Util_Exception(Util_Def::ERR_QQ_ERROR, 'qq number invalid');
        }

        $arrParam = array(
            'forum_name' => $this->_strForumName,
        );
        $arrRet = Tieba_Service::call('forum', 'getBtxInfoByName', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            $strMsg = sprintf('call forum::getBtxInfoByName fail with input [%s] output [%s]', serialize($arrParam), serialize($arrRet));
            Bingo_Log::warning($strMsg);
            throw new Util_Exception(Util_Def::ERR_NET_REASON_ERROR, 'net reason');
        }
        $this->_intForumId = $arrRet['forum_id']['forum_id'];
        $this->_strLevel1  = $arrRet['dir']['level_1_name'];
        $this->_strLevel2  = $arrRet['dir']['level_2_name'];

		// 查看吧是否有效
        if ($this->_intForumId <= 0) {
            throw new Util_Exception(Util_Def::ERR_FORUM_NOT_EXITS_ERROR, 'forum not exits');
        }

        // 查看是否是娱乐明星目录的吧
        if (self::LEVEL_1_NAME !== $this->_strLevel1) {
            throw new Util_Exception(Util_Def::ERR_NOT_STAR_FORUM_ERROR, 'forum not star forum');
        }

        // status 0: 健康的 1：隐形的 2：死亡的
        if (isset($this->_arrBlockDir[$this->_strLevel2])) {
            $this->_intForumStatus = 1;
        } else {
            $this->_intForumStatus = 0;
        }
        
        // 查看是否已经报名
        $arrParam = array(
            'related_act_type' => self::ACT_FORUM_TYPE,
            'related_act_id'   => $this->_intForumId,
        );
        $arrRet = Tieba_Service::call('header', 'getRecommendHead', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            $strMsg = sprintf('call header::getRecommendHead fail with input [%s] output [%s]', serialize($arrParam), serialize($arrRet));
            Bingo_Log::warning($strMsg);
            throw new Util_Exception(Util_Def::ERR_NET_REASON_ERROR, 'net reason');
        }

        if (!empty($arrRet['data'])) {
            Bingo_Log::pushNotice("{$this->_strForumName}", 'register');
            throw new Util_Exception(Util_Def::ERR_FORUM_HAS_REGISTER_ERROR, 'forum has registered');
        }

		$arrRet = Tieba_Service::call('header', 'getRecommendHeadNum', $arrParam, null, null, 'post', 'php', 'utf-8');
		if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            $strMsg = sprintf('call header::getRecommendHeadNum fail with input [%s] output [%s]', serialize($arrParam), serialize($arrRet));
            Bingo_Log::warning($strMsg);
            throw new Util_Exception(Util_Def::ERR_NET_REASON_ERROR, 'net reason');
        }
		$this->_intRegisterNum = (int)($arrRet['data']['count']);

    }

    protected function _execute() {
        $arrParam                 = $this->_arrRegisterInfo;
        $arrParam['forum_id']     = $this->_intForumId;
        $arrParam['status']       = $this->_intForumStatus;
        $arrParam['op_user_id']   = $this->_arrUserInfo['user_id'];
        $arrParam['op_user_name'] = $this->_arrUserInfo['user_name'];
        $arrRet = Tieba_Service::call('star', 'forumRegister', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            $strMsg = sprintf('call star::forumRegister fail with input [%s] output [%s]', serialize($arrParam), serialize($arrRet));
            Bingo_Log::fatal($strMsg);
            throw new Util_Exception(Util_Def::ERR_NET_REASON_ERROR, 'net reason');
        }

        $this->_strForumName = Bingo_Http_Request::get('forum_name', '');
		$arrParam = array(
			'forum_id'      => $this->_intForumId,
			'forum_name'    => $this->_strForumName,
			'npc_name'      => 'yaokun',
			'npc_picurl'    => 'yaokun',
			'fans_name'     => 'yaokun',
			'npc_intro'     => 'yaokun',
			'proposer_id'   => $this->_arrUserInfo['user_id'],
			'proposer_name' => $this->_arrUserInfo['user_name'],
			'status'        => 2, //直接通过,无需审核
			'type'          => 1, //pt only
		);
		$arrRet = Tieba_Service::call('celebrity', 'addNpcApplication', $arrParam, null, null, 'post', 'php', 'utf-8');
		if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::pushNotice('call_addNpcApplication_failed', '1');
        }
		$this->_intRegisterNum++;
    }
}
?>
