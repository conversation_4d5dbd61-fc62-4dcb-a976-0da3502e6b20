<?php
/**
 * User: dingfenghu
 * Date: 2017-05-17
 * 
 */
class getRankListAction extends Mo_Core_Action{
    const TOP_FORUM_WEEK_AGREE_USER_TYPE = 1;
    const TOP_FORUM_WEEK_AGREE_THREAD_TYPE = 2;
    const TOP_AGREE_USER_TYPE = 3;
    
    public static $retData = array();
    
    /**
     * @param array
     * @return array
     **/
    protected function _execute() {
		Mo_Response::$bolAjaxCommit = true; //标记返回值类型为json
		Mo_Response::$intErrno = 0;
		Mo_Response::$strError = 'success';
		Mo_Response::$arrErrorData = array();
        
        try {
			$this->user = Mo_Service_Wap::getTplUser();
            $this->wreq = Mo_Service_Wap::getTplWreq();
            $user_id = intval($this->user['id']);
            $userInfo = array();
            
            //参数获取
            $type = intval(Mo_request::get('type', 0));
            $pn = intval(Mo_Request::get('pn', 1));
            $rn = intval(Mo_Request::get('rn', 20));
            
            if( $user_id > 0 && $pn == 1 ){
                $arrInput = array(
                    'user_id' => $user_id,
                );
                //获取用户详细信息比如会员，tdou等信息
                $arrOut = Tieba_Service::call('user', 'getUserDataEx', $arrInput, null, null, 'post', 'php', 'utf-8');
                if ( $arrOut === false || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS ){
                    Bingo_Log::warning('service  user.getUserDataEx error. [input='.serialize($arrInput).'][out='.serialize($arrOut).']');
                }else{
                    $this->user = array_merge($this->user, $arrOut['user_info']);
                    if(!array_key_exists( 'Parr_scores', $this->user)){
                        $this->user['Parr_scores'] = array(
                            "scores_total" => 0
                        );
                    }
                    $this->user['avatar'] = $this->user['portrait'];
                    $this->user['portrait'] = Tieba_Ucrypt::encode( $this->user['user_id'], $this->user['user_name']);
                    $userInfo = $this->user; 
                }
            }
            
            if($type == self::TOP_AGREE_USER_TYPE){
                $arrParams = array(
                    'user_id' => $user_id,
                    'pn' => $pn,
                    'ps' => $rn,
                );
                $arrRet = Tieba_Service::call('agree', 'getTopAgreeUser', $arrParams, null, null, 'post', 'php', 'utf-8');
                if( Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                    Bingo_Log::warning(__FUNCTION__.' service agree.getTopAgreeUser error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                    throw new Exception("service call fail!",Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
            }
            
            $errno = isset($arrRet['errno']) ? $arrRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            $data = isset($arrRet['data']) ? $arrRet['data'] : array();
            $data['user'] = isset($data['user']) ? $data['user'] : array();
            $data['user'] = array_merge($data['user'], $userInfo);
            
			Mo_Response::$intErrno = $errno;
			Mo_Response::$strError = Tieba_Error::getUserMsg($errno);
            Mo_Response::$arrErrorData = $data;
        }catch(Exception $e){
            Bingo_Log::warning( "no=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            //$this->_jsonRet($e->getCode(), Tieba_Error::getUserMsg($e->getCode()));
			Mo_Response::$intErrno = $e->getCode();
			Mo_Response::$strError =$e->getMessage();
            Mo_Response::$arrErrorData = self::$retData;
        }

        return true;
    }
	
    /**
     * @brief:
     * @param:null
     * @return:null
     **/
    protected  function _log(){
        Tieba_Stlog::addNode('ispv', 0);
    }
}
