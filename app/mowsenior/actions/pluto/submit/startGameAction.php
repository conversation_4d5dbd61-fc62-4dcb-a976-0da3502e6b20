<?php
/**
 * Created by PhpStorm.
 * User: chenlei24
 * Date: 15-10-20
 * Time: 上午10:14
 */

require_once('../../../util/pluto/Base.php'); 
class startGameAction extends Util_Pluto_Base {
    const PRESENT_COLOR_NUM_PER_FORUM  = 100;
	const OP_TYPE_DESC                 = 1;
	const OP_TYPE_ADD                  = 2;
	const OP_TYPE_PRE_ALLOCATE         = 3;
	
	protected $_conf     = null;      //存放模块的所有配置信息
    protected $_arrData  = array();   //存放需要用到的数据信息
    protected $_arrRet   = array();   //存放返回信息
    protected $_arrParam = array();  //存放FE传进参数
	
	
	/**
     * @desc 主执行程序
     * @param
     * @return
     */
    public function _execute () {
		try{
			Mo_Response::$bolAjaxCommit = true;
			$this->init();
			$this->_input();
            $this->_check();
			$this->_process();
			$this->_log();
			Mo_Response::$intErrno = Tieba_Errcode::ERR_SUCCESS;
            Mo_Response::$strError = 'success';
            Mo_Response::$arrErrorData = $this->_arrRet;
		} catch (Exception $e) {
			Mo_Response::$intErrno = $e->getCode();
            Mo_Response::$strError = $e->getMessage();
		}

		return true;
	}

	/**
	 * @desc 检查活动的有效性与用户的有效性
	 * @param null
	 * @return true/false
	 */
	protected function _check() {
		if (!$this->_checkForumAvail()) {
			return false;
		}
		if (!$this->_checkUserAvail()) {
			return false;
		}
		return true;
	}

	/**
	 * @desc 检查当前活动正在进行巡游
	 * @param null
	 * @return true/false
	 */
	protected function _checkForumAvail() {
		//判断当前活动状态
		$arrParam = array(
            'forum_id' => $this->_arrParam['forum_id'],
		);
        $arrRes = Tieba_Service::call('pluto', 'getTravelInfo', $arrParam);
		if ($arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('call pluto::getTravelInfo fail! '. serialize($arrRes). '_'. serialize($arrParam));
			throw new Exception('call service fail', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
			
			return false;
		}		
		if (empty($arrRes['data'])) {   //当前吧没有巡游活动
			Bingo_Log::warning('no crowd founding activity in current forum! '. serialize($arrRes). '_'. serialize($arrParam));
			throw new Exception('no crowdfunding in current forum', Util_Pluto_Def::ERR_NOT_CROWDFUNDING_FORUM);

			return false;
		}
		$this->_arrData['travel_info'] = $arrRes['data']['travel_info'];
		$this->_arrData['cdinfo']      = $arrRes['data']['cdinfo'];
		$this->_arrData['category']    = $arrRes['data']['category'];
		$intCurrentTime = time();
		$intStartTime = (int)$arrRes['data']['travel_info']['start_time'];
		$intEndTime = (int)$arrRes['data']['travel_info']['end_time'];
		if ($intCurrentTime < $intStartTime) {  //还未巡游至当前吧
			Bingo_Log::warning('crowd founding activity not start!' . serialize($arrRes). '_'. serialize($arrParam));
			throw new Exception('crowd founding not start', Util_Pluto_Def::ERR_CROWDFUNDING_NOT_START);
		
		    return false;
		}
		if ($intCurrentTime > $intEndTime) {   //当前吧已巡游完毕
			Bingo_Log::warning('crowd founding activity ends! '. serialize($arrRes). '_'. serialize($arrParam));
			throw new Exception('crowd funding ends', Util_Pluto_Def::ERR_CROWDFUNDING_ENDS);
		
		    return false;
		}

        return true;
	}

	/**
	 * @desc 检查用户的有效性,即当前用户还有抽奖机会
	 * @param null
	 * @return true/false
	 */
	protected function _checkUserAvail() {
		$strCmd = 'playgame';
		$arrReq = $this->_getUegReq($strCmd);
		if (empty($arrReq)) {
            Bingo_Log::warning('invalid cmd '. $cmd);
			throw new Exception('call service fail', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);

			return false;
		}
		$arrRes = Tieba_Service::call('anti', 'antiActsctrlQuery', $arrReq);
		if ($arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS && $arrRes['errno'] !== Tieba_Errcode::ERR_ANTI_ACTS_REFUSE) {
			Bingo_Log::warning('call anti::antiActsctrlQuery fail! '. serialize($arrRes). '_'. serialize($arrReq));
			throw new Exception('call service fail', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);

			return false;
		}
		if ($arrRes['errno'] === Tieba_Errcode::ERR_ANTI_ACTS_REFUSE) {  //用户剩余抽奖次数小于等于0,返回
            Bingo_Log::warning('user have no chance!'. $intUserId. $intForumId. serialize($arrRes));
			throw new Exception('user have no chance', Util_Pluto_Def::ERR_USER_NO_CHANCE);
		
		    return false;
		}

		return true;
	}

	/**
	 * @desc 接受参数
	 * @param null
	 * @return 
	 */
	protected function _input() {
	    $this->_arrParam['user_id'] = $this->_arrUserInfo['user_id'];   
	    $this->_arrParam['forum_id'] = (int)(Mo_Request::get('forumId', 0));

		if ($this->_arrParam['user_id'] <= 0 || $this->_arrParam['forum_id'] <= 0) {
			Bingo_Log::warning('param error with input %s'. serialize($this->_arrParam));
			throw new Exception('param error', Tieba_Errcode::ERR_PARAM_ERROR);
		}	

		//判断用户是否登陆，脚本刷接口的话，可能是没登陆的用户
		if (!$this->_arrUserInfo['is_login']) {
			Bingo_Log::warning('not login');
			throw new Exception('param error', Tieba_Errcode::ERR_PARAM_ERROR);
		}
	}

	/**
	 * @desc 获取返回给FE的用户最多抽中的颜料数
	 * @param null
	 * @return true/false
	 */
	protected function _getColorNum() {
        //概率性的获取用户最多抽中的颜料数A1
		$arrPdf = array();
		foreach($this->_conf['probability'] as $key => $value) {
			$arrPdf[] = floatval($value);
		}
		$ret = Tbapi_Platform_Star_AliasMethod::rand($arrPdf);
		if ($ret === false) {
			Bingo_Log::warning('call Tbapi_Platform_Star_AliasMethod error! '. serialize($ret). '_'. serialize($arrPdf));
			throw new Exception('call service fail', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);

			return false;
		}
		$intColorNum1 = (int)$ret;
		Tieba_Stlog::addNode('color_num_1', $intColorNum1);

		// 如果分配的概率就是0，后续的数据库操作也就可以省略了
		if (!$intColorNum1) {
			$this->_arrRet['colorNum'] = $intColorNum1;
			return true;
		}

		// 查看下同时在线人数是否超限(50人)，超限的话，直接设置为没中奖
		$intId = (int)($this->_arrData['cdinfo']['id']);
		if (false === $this->_uegCheck($intId, 'charge', $this->_arrUserInfo['user_id'])) {
			$this->_arrRet['colorNum'] = 0;
			Tieba_Stlog::addNode('color_num_2', 0);
			return true;
		}

		// 先计算下本吧最少可以分掉多少颜料
		$intCurForumColorNumLimit = $this->_getCurForumLeftColorLimit($this->_arrData['cdinfo'], $this->_arrData['category'], $this->_arrData['travel_info']);

		Tieba_Stlog::addNode('cur_forum_color_limit_num', $intCurForumColorNumLimit);

		// 去数据库中尝试扣减
		$arrParam = array(
			'id'        => $this->_arrData['cdinfo']['id'],
			'color_num' => $intColorNum1,
			'limit'     => $intCurForumColorNumLimit,
			'op_type'   => self::OP_TYPE_DESC,
		);
		$arrRet = Tieba_Service::call('pluto', 'updatePoolNum', $arrParam, null, null, 'post', 'php', 'utf-8');
		if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
			$strMsg = sprintf('call pluto::updatePoolNum fail with input [%s] output [%s]', serialize($arrParam), serialize($arrRet));
			Bingo_Log::warning($strMsg);
			throw new Exception($strMsg, Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}

		$this->_arrRet['colorNum'] = (int)($arrRet['data']);
		Tieba_Stlog::addNode('color_num_3', $this->_arrRet['colorNum']);

		// 提交UEG策略
		$this->_uegCommit($this->_arrData['cdinfo']['id'], 'charge');

		return true;
	}

	/**
	 * @desc 真正处理的函数
	 * @param null
	 * @return true/false;
	 */
	protected function _process() {
		if(!$this->_getColorNum()) {
			return false;
		}

		//生成当前订单的ID,并保存至全局变量中
		$this->_arrRet['orderId'] = Tbapi_Platform_Star_Order::genOrderId($intUserId, $intForumId);

		Tieba_Stlog::addNode('order_id', $this->_arrRet['orderId']);

		//将返回给FE的颜料数与订单号写入数据表prop_transaction中
		$arrParam = array(
		    'order_id'   => $this->_arrRet['orderId'],
			'user_id'    => $this->_arrParam['user_id'],
			'product_id' => $this->_conf['product']['product_id'],
		    'op_type'    => self::OP_TYPE_PRE_ALLOCATE,
		    'category'   => 1,
			'num'        => $this->_arrRet['colorNum'],
		    'cid'        => intval($this->_arrData['cdinfo']['id']),	
		);
		$arrRet = Tieba_Service::call('pluto', 'writePropTransaction', $arrParam, null, null, 'post', 'php', 'utf-8');
		if ($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('call pluto::writePropTransaction fail! '. serialize($arrRet). '_'. serialize($arrParam));
			throw new Exception('call service fail', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);

			return false;
		}

		//调用UEG接口,将当前用户的抽奖机会减1
		$strCmd = 'playgame';
		$arrReq = $this->_getUegReq($strCmd);
		if (empty($arrReq)) {
            Bingo_Log::warning('invalid cmd '. $cmd);
			throw new Exception('call service fail', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);

			return false;
		}
		$arrRes = Tieba_Service::call('anti', 'antiActsctrlSubmit', $arrReq);
		if ($arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('call anti::antiActsctrlQuery fail! '. serialize($arrRes). '_'. serialize($arrReq));
			throw new Exception('call service fail', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);

			return false;
		}

		return true;
	}
    
	/**
	 * @param $intErrno
	 * @param $strErrMsg
	 * @param $arrData
	 * @return 
	**/
	protected function _jsonRet ($intErrno, $strErrMsg, $arrData) {
		$arrRet = array(
            'errno' => intval($intErrno),
			'errmsg' => strval($strErrMsg),
			'data'   => $arrData,
		);
		Bingo_Http_Response::contextType('application/json');
	}

	/**
	 * @param $cmd 目前仅支持获取与减少两种操作
	 * @param $user_id 用户id
	 * @param $forum_id 吧id
	 * @return $arrOut 调用ueg的输入参数
	*/
	protected function _getUegReq($cmd) {
		$arrCmd = array('playgame', );
		if (!in_array($cmd, $arrCmd)) {
			return array();
		}

		$intCurForumIndex = $this->_getTravelIndexByTravelInfo($this->_arrData['cdinfo'], $this->_arrData['category'], $this->_arrData['travel_info']);

		$arrOut = array(
			'req' => array(
                'rulegroup' => array('app'),
				'app'       => 'starshow',
				'cmd'       => $cmd,
				'act_id'    => intval($this->_arrData['cdinfo']['id']),
				'forum_id'  => intval($this->_arrParam['forum_id']),
				'gid'       => intval($intCurForumIndex),
				'uid'       => intval($this->_arrParam['user_id']),
				'ip'        => $this->_arrUserInfo['user_ip'],
			),
		);
		
		return $arrOut;
	}
	
	/**
     * @desc 看我如何打日志
     * @param 
     * @return
     */
    protected function _log() {
        Tieba_Stlog::addNode('pv', 0); 
	    Tieba_Stlog::addNode('uid', $this->_arrUserInfo['user_id']);
	    Tieba_Stlog::addNode('un', $this->_arrUserInfo['user_name']);
        Tieba_Stlog::addNode('uip', $this->_arrUserInfo['user_uip']);
	    Tieba_Stlog::addNode('fid', $this->_arrParam['forum_id']);     	
	    Tieba_Stlog::addNode('cd_id', $this->_arrData['cdinfo']['id']);     	
	}
}
/* vim: set noexpandtab ts=4 sw=4 sts=4 tw=100: */
