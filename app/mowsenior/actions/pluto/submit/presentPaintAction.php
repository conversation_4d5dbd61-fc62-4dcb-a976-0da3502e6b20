<?php
/**
 * @author: chenlei24 
 * @date: 2015-10-22
 * @file: buyPaintAction.php
 * @description:
 */

require_once('../../../util/pluto/Base.php');
class presentPaintAction extends Util_Pluto_Base {
	const OP_TYPE_DESC                 = 1;
	const OP_TYPE_ADD                  = 2;

    protected $_arrParam = array();
	protected $_conf     = null;
    protected $_arrData  = array();
    protected $_intFid;

	/**
	 * @desc 主执行程序
	 * @param
	 * @return
	 */
    public function _execute() {
        try{
            Mo_Response::$bolAjaxCommit = true;
			$this->init();
            $this->_checkParam();
            $this->_buildData();
            $this->_process();
            $this->_log();
            Mo_Response::$intErrno = Tieba_Errcode::ERR_SUCCESS;
            Mo_Response::$strError = 'success';
            Mo_Response::$arrErrorData = $this->_arrRet;
        }catch (Exception $e){
            Mo_Response::$intErrno = $e->getCode();
            Mo_Response::$strError = $e->getMessage();
        }

    }

	/**
	 * @desc 也是主执行程序
	 * @param
	 * @return
	 */
    protected function _process(){
        //订单号与输入参数的校验
        //获取当前输入订单对应信息
        $arrParam = array(
            'order_id' => $this->_arrParam['order_id'],
            'cid'      => $this->_arrData['cdinfo']['id'],
        );
        $arrRet = Tieba_Service::call('pluto', 'getPropTransaction', $arrParam);
        if ($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call pluto::getPropTransaction fail'. serialize($arrParam). '_'. serialize($arrRet));
            throw new Exception('call service fail!', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        if (empty($arrRet['data'])) {
            Bingo_Log::warning('no corresponding order_id in DB'. serialize($arrParam). '_'. serialize($arrRet));
            throw new Exception('order_id not exist', Util_Pluto_Def::ERR_ORDER_ID_NOT_EXIST);
        }
        //判断订单是否已使用
        $intStatus = intval($arrRet['data']['status']);
        if ($intStatus === 1) {
            Bingo_Log::warning('repeat commit the order_id'. serialize($arrParam));
            throw new Exception('order_id repeat commit', Util_Pluto_Def::ERR_ORDER_ID_REPEAT);
        }
        //判断输入数目不超过库中记录数目
        $this->_intFid = intval($arrRet['data']['id']);
        $intInputProductNum = (int) $this->_arrParam['product_num'];
        $intRecordProductNum = (int) $arrRet['data']['num']; 
        if ($intInputProductNum > $intRecordProductNum) { //用户作弊
            $strMsg = sprintf('input product num greater than records in database, record:%d, input:%d', $intRecordProductNum, $intInputProductNum);
            Bingo_Log::warning($strMsg);
            throw new Exception('product_num not valid', Util_Pluto_Def::ERR_PRODUCT_NUM_NOT_VALID);
        }

        // 需要返回到库存中的颜料数目
        $intColorNum = $intRecordProductNum > $intInputProductNum ? ($intRecordProductNum - $intInputProductNum) : 0;

        $intCurUserId = (int)$this->_arrParam['user_id'];
        $intRecordUserId = (int)$arrRet['data']['user_id'];
        if ($intCurUserId !== $intRecordUserId) { //用户ID与当前order_id不对应
            $strMsg = sprintf('user_id is supposed to be %d, but the input user_id is %d', $intRecordUserId, $intInputUserId);
            Bingo_Log::warning($strMsg);
            throw new Exception('not valid user', Util_Pluto_Def::ERR_USER_NOT_VALID);
        }
        $arrRet = Tieba_Service::call('pluto', 'expirePropTransaction', $arrParam);
        if ($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call pluto::expirePropTransaction fail'. serialize($arrParam). '_'. serialize($arrRet));
            throw new Exception('call service fail!', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        //返回的product_num大于0时,才进行,否则不进行中奖信息入库
        //计算奖品失效时间
        if ($this->_arrParam['product_num'] > 0) {
            $intAvailTime = (int)$this->_conf['avail_time']['default'];   //默认是30天,转换成以秒为单位就是这个值啦
            $intUnvalidTime = (int)$this->_arrData['travel_end_time'] + $intAvailTime;
            //信息入库
            $strOrderId = Tbapi_Platform_Star_Order::genOrderId($arrParam['user_id'], $arrParam['forum_id']);
            $arrParam = array(
                'user_id'          => $this->_arrParam['user_id'],
                'related_id'       => $this->_arrData['cdinfo']['id'],
                'product_id'       => $this->_conf['product']['product_id'],
                'product_num'      => $this->_arrParam['product_num'],
                'order_id'         => $strOrderId,
                'start_time'       => time(),
                'end_time'         => $intUnvalidTime,
                'related_sys_type' => $this->_conf['product']['related_sys_type'],
                'related_sys_id'   => $this->_arrData['cdinfo']['id'],
                'fid'              => intval($this->_intFid),
            );
            $arrRet = Tieba_Service::call('productcenter', 'presentProduct', $arrParam);
            if ($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call productcenter::presentProduct fail!'. serialize($arrParam). '_'. serialize($arrRet));
                throw new Exception('call service fail!', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }
            Tieba_Stlog::addNode('order_id', $strOrderId);
            Tieba_Stlog::addNode('product_num', $this->_arrParam['product_num']);

        // 没有需要放回到库存中的数目，直接返回呗
        if ($intColorNum <= 0) {
            return true;
        }

        $intCurColorNumLimit  = $this->_getCurForumLeftColorLimit($this->_arrData['cdinfo'], $this->_arrData['category'], $this->_arrData['travel_info']);
        $intColorNumPerForum  = $this->_getColorNumPerForum($this->_arrData['cdinfo'], $this->_arrData['category'], $this->_arrData['travel_info']);
        $intPoolTotalColorNum = $this->_getPoolTotalColorNum($this->_arrData['cdinfo'], $this->_arrData['category'], $this->_arrData['travel_info']);

        Tieba_Stlog::addNode('cur_color_num_limit', $intCurColorNumLimit);
        Tieba_Stlog::addNode('color_num_per_forum', $intColorNumPerForum);

        // 更新当前活动的剩余可赠送颜料数
        // 为防止加多了颜料，做一个限制，不能超过颜料库中的初始值
        $arrParam = array(
            'id' => $this->_arrData['cdinfo']['id'],
            'color_num' => $intColorNum,
            'limit'     => $intPoolTotalColorNum,
            'op_type'   => self::OP_TYPE_ADD,
        );
        $arrRet = Tieba_Service::call('pluto', 'updatePoolNum', $arrParam, null, null, 'post', 'php', 'utf-8');
        if ($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call pluto::updatePoolNum fail!'. serialize($arrParam). '_'. serialize($arrRet));
            throw new Exception('call service fail!', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
	}

	/**
	 * @desc 打印必要的网络日志
	 * @param
	 * @return
	 */
	protected function _log() {
        Tieba_Stlog::addNode('pv', 0);                               
            Tieba_Stlog::addNode('uid', $this->_arrUserInfo['user_id']);
            Tieba_Stlog::addNode('un', $this->_arrUserInfo['user_name']);
            Tieba_Stlog::addNode('uip', $this->_arrUserInfo['user_uip']);
            Tieba_Stlog::addNode('fid', $this->_arrParam['forum_id']);    
            Tieba_Stlog::addNode('cd_id', $this->_arrData['cdinfo']['id']);   
    }

	/**
	 * @desc 参数接收和检查
	 * @param 
	 * @return
	 */
    private function _checkParam() {
        //获取参数
        $this->_arrParam['user_id']   = $this->_arrUserInfo['user_id'];
        $this->_arrParam['forum_id']  = intval(Mo_Request::get('forumId',0));
        $this->_arrParam['order_id']  = strval(Mo_Request::get('orderId',''));
        $this->_arrParam['tbs']       = strval(Mo_Request::get('tbs',''));
        $this->_arrParam['product_num'] = intval(Mo_Request::get('productNum',-1));

        //参数校验
		if ($this->_arrParam['user_id'] <= 0 || $this->_arrParam['forum_id'] <= 0 || $this->_arrParam['product_num'] < 0 
			|| empty($this->_arrParam['tbs']) || empty($this->_arrParam['order_id'])) {
            $strMsg = sprintf('param error with input %s', serialize($this->_arrParam));
		    Bingo_Log::warning($strMsg);
			throw new Exception('param error', Tieba_Errcode::ERR_PARAM_ERROR);
		}	

        //校验tbs
		if (!$this->_checkTbs($this->_arrParam['tbs'],true) ) {
            Bingo_Log::warning('tbs_check fail');
            throw new Exception('tbs check fail', Tieba_Errcode::ERR_TBSIG_CHECK_FAIL);
}
    }

	/**
	 * @desc 构建需要的数据 
	 * @param 
	 * @return
     */
    private function _buildData() {
        //获取当前巡游活动信息
        $arrParam = array(
            'forum_id' => $this->_arrParam['forum_id'],                            
        );  
        $arrRes = Tieba_Service::call('pluto', 'getTravelInfo', $arrParam);
        if ($arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call pluto::getTravelInfo fail! '. serialize($arrRes). '_'. serialize($arrParam));
            throw new Exception('param error', Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (empty($arrRes['data'])) {
            Bingo_Log::warning('no crowdfunding in current forum'. serialize($arrRes). '_'. serialize($arrParam));
            throw new Exception('no crowdfunding in current forum', Util_Pluto_Def::ERR_NOT_CROWDFUNDING_FORUM);
        }        
        $this->_arrData['travel_info'] = $arrRes['data']['travel_info'];
        $this->_arrData['cdinfo']      = $arrRes['data']['cdinfo'];
        $this->_arrData['category']    = $arrRes['data']['category']; 

        //计算当前巡游活动结束时间
        $intTravelStartTime = (int)$arrRes['data']['cdinfo']['travel_time'];
        $intTravelInterval  = (int)$arrRes['data']['category']['travel_time'];
        $intTravelForumNum  = (int)$arrRes['data']['category']['travel_num'];
        $intTravelBusNum    = (int)$arrRes['data']['cdinfo']['bus_num'];
        $this->_arrData['travel_end_time'] = $intTravelStartTime + $intTravelInterval * $intTravelForumNum * $intTravelBusNum;
    }
} 
