<?php
/**
 * Created by PhpStorm.
 * User: shiyibo
 * Date: 15-1-8
 * Time: 下午6:09
 */
require_once('../../../util/pluto/Base.php');
class starTravelingAction extends Util_Pluto_Base {
	protected $_arrReq           = array();
	protected $_arrParam         = array();
	protected $_arrData          = array();
	protected $_arrCategory      = array();
	protected $_conf             = null;
	protected $_intTime          = 0;

	// 声明要渲染的模块control路径
	protected $strTplName = 'star_traveling.php';
    protected $strTplPath = 'splatform';

	/**
	 * @desc 主执行程序
	 * @param
	 * @return
	 */
    public function _execute () {
		try{
			$this->init();
			$this->_input();
			$this->_prepareParam();
			$this->_multiCall();
			$this->_buildData();
		}catch (Exception $e) {
			Mo_Response::addViewData('no',$e->getCode());
			Mo_Response::addViewData('error',$e->getMessage());
		}

		return true;
    }

	/**
	 * @desc 接受参数
	 * @param null
	 * @return
	 */
	protected function _input() {
		$this->_arrReq['id']       = (int)(Mo_Request::get('id', 0));
		$this->_arrReq['forum_id'] = (int)(Mo_Request::get('forum_id', 0));
		$this->_intTime            = time();

		if ($this->_arrReq['id'] <= 0 || $this->_arrReq['forum_id'] <= 0) {
			Bingo_Log::warning('param error with input [%s]', serialize($this->_arrReq));
			throw new Exception('param error', Tieba_Errcode::ERR_PARAM_ERROR);
		}

		return true;
	}

	/**
	 * @desc 给平行调用后端贮备参数
	 * @param null
	 * @return
	 */ 
	protected function _prepareParam() {
		$arrParam = array(
			// 应援信息
			'cd_info' => array(
				'serviceName' => 'pluto',
				'method'      => 'getCdInfoByIds',
				'input'       => array(
					'id' => $this->_arrReq['id'],
				),
				'ie'    => 'utf-8',
			),

			// 应援概况
			'oil_num' => array(
				'serviceName' => 'pluto',
				'method'      => 'getCdNumById',
				'input'       => array(
					'id' => $this->_arrReq['id'],
				),
				'ie'    => 'utf-8',
			),

			// 巡游数据
			'travel_list' => array(
				'serviceName' => 'pluto',
				'method'      => 'getTravelListById',
				'input'       => array(
					'id' => $this->_arrReq['id'],
				),
				'ie'    => 'utf-8',
			),
			// 我的颜料 
			'color' => array(
				'serviceName' => 'pluto',
				'method'      => 'getUserProps',
				'input'       => array(
					'user_id'  => $this->_arrUserInfo['user_id'],
				),
				'ie'         => 'utf-8',
			),
			/*'anti' => array(
				'serviceName' => 'anti',
				'method'      => 'antiActsctrlQuery',
				'input'       => array(
					'req' => array(
						'rulegroup' => array('app'),
						'app'       => 'starshow',
						'cmd'       => 'playgame',
						'act_id'    => $this->_arrReq['id'],
						'forum_id'  => $this->_arrReq['forum_id'],
						'uid'       => $this->_arrUserInfo['user_id'],
						'ip'        => $this->_arrUserInfo['user_ip'],
					),
				),
				'output' => 'res',
				'ie'         => 'utf-8',
			),*/
	 		
			// 取库存中的油漆 
			'oil_poll' => array(
				'serviceName' => 'generalmessage',
				'method'      => 'getSingleValue',
				'input'       => array(
					'key'   => $this->_conf['oil_poll']['key'],
					'field' => sprintf('%s_%d', $this->_conf['oil_poll']['field_prex'], $this->_arrReq['id']),
				),
				'ie'    => 'utf-8',
			),
		);

		$this->_arrParam = $arrParam;
	}

	/**
	 * @desc 批量去后端拉取数据
	 * @param 
	 * @return
	 */
	protected function _multiCall() {
		$objRalMulti = new Tieba_Multi('crowdfunding');
		foreach($this->_arrParam as $key => $arrItem) {
			$objRalMulti->register($key, new Tieba_Service($arrItem['serviceName']), $arrItem);
		}
		$objRalMulti->call();
		foreach($this->_arrParam as $key => $arrItem) {
			$arrResult = $objRalMulti->getResult($key);
			if (false === $arrResult || Tieba_Errcode::ERR_SUCCESS !== $arrResult['errno']) {
				$strMsg = sprintf('call %s::%s with input [%s] output [%s]', $arrItem['serviceName'], $arrItem['method'], serialize($arrItem['input']), serialize($arrResult));
				Bingo_Log::warning($strMsg);
				continue;
			} else {
				$this->_arrData[$key] = !isset($arrItem['output']) ? $arrResult['data'] : $arrResult[$arrItem['output']];
			}
		}
	}

	/**
	 * @desc 打一些必要的日志
	 * @param null
	 * @return
	 */
	protected function _log() {
		Tieba_Stlog::addNode('pv', 0);
		Tieba_Stlog::addNode('uid', $this->_arrUserInfo['user_id']);
		Tieba_Stlog::addNode('un', $this->_arrUserInfo['user_name']);
		Tieba_Stlog::addNode('uip', $this->_arrUserInfo['user_uip']);
		Tieba_Stlog::addNode('fid', $this->_arrReq['forum_id']);
	}

	/**
	 * @desc 数据封装
	 * @param null
	 * @return
	 */
	protected function _buildData() {
		$arrCdInfo = $this->_buildCdInfo();
		$arrUser   = $this->_buildUser();
		$arrOil    = $this->_buildOilInfo();

		Mo_Response::addViewData('crowdfunding', $arrCdInfo);
		Mo_Response::addViewData('user', $arrUser);
		Mo_Response::addViewData('oilInfo', $arrOil);
		Mo_Response::addViewData('wreq', $this->_arrData['werq']);
		Mo_Response::addViewData('base', $this->_arrData['base']);
	}

	/**
	 * @desc 计算本机的油漆库存 
	 * @param
	 * @return
	 */
	protected function _buildOilInfo() {
		$intLeftOilNum    = (int)$this->_arrData['oil_poll'];
		$intInterval      = $this->_arrCategory['travel_time'];          // default 7200s
		$intFeedbackRatio = $this->_arrCategory['ratio'];                // default 0.2 
		$intTravelTime    = (int)($this->_arrData['cd_info'][0]['travel_time']);
		$intTravelCount   = (int)($this->_arrCategory['travel_num']);
		$intCurTime       = time();
		$intTravelIndex   = (int)(($intCurTime - $intTravelTime)/$intInterval);
		$intTotal         = $this->_arrData['cd_info'][0]['oil_num'] * $intFeedbackRatio;
		$intNumPerForum   = $intTotal/$intTravelCount;
		$intLeftNum       = $intLeftOilNum - ($intTravelCount - $intTravelIndex - 1) * $intNumPerForum;
		$arrOilInfo       = array(
			'total'   => $intTotal,
			'left'    => $intLeftNum <= 0 ? 0 : $intLeftNum,
		);

		return $arrOilInfo;
	}

	/**
	 * @desc 拉取user数据
	 * @param 
	 * @return
	 */ 
	protected function _buildUser() {
		// 用户没登陆 也就我所谓后续数据了
		$arrUserInfo = $this->_arrUserInfo;
		if (false === $arrUserInfo['is_login']) {
			return $arrUserInfo;
		}

		//  拉取更详细的用户数据
		$arrParam = array(
			'user_id' => $arrUserInfo['user_id'],
		);
		$arrRet = Tieba_Service::call('user', 'getUserData', $arrParam, null, null, 'post', 'php', 'utf-8');
		if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
			$strMsg = sprintf('call user::getUserData fail with input [%s] output [%s]', serialize($arrParam), serialize($arrRet));
			Bingo_Log::warning($strMsg);
		} else {
			$arrData = $arrRet['user_info'][0];
			if (null !== $arrData && null !== $arrUserInfo) {
				$arrUserInfo = array_merge($arrUserInfo, $arrData);
			}
		}
		
		// 查看用户是否有颜料道具
		$arrPropsInfo         = $this->_arrData['color']['props_info'];
		$arrUserInfo['color'] = 0;
		$intColorPropsId      = (int)$this->_conf['product']['product_id'];
		foreach($arrPropsInfo as $arrItem) {
			$intPropsId = (int)($arrItem['product_id']);
			if ($intColorPropsId === $intPropsId) {
				$arrUserInfo['color'] = (int)($arrItem['num']);
			}
		}

		$arrUserInfo['playTimes'] = $this->_getLeftPlayTimes();

		return $arrUserInfo;
	}

	/**
	 * @desc 计算当前用户还可以参与几次小游戏
	 * @param
	 * @return
	 */
	protected function _getLeftPlayTimes() {
		$intId                  = (int)($this->_arrData['cd_info'][0]['id']);
		$intCurForumTravelIndex = $this->_getTravelIndexByTravelList($this->_arrData['travel_list'][$intId]);
		$intLeftTimes           = 0;
		$arrParam = array(
			'req' => array(
				'rulegroup' => array('app'),
				'app'       => 'starshow',
				'cmd'       => 'playgame',
				'act_id'    => $this->_arrReq['id'],
				'forum_id'  => $this->_arrReq['forum_id'],
				'uid'       => $this->_arrUserInfo['user_id'],
				'gid'       => $intCurForumTravelIndex,
				'ip'        => $this->_arrUserInfo['user_ip'],
			),
		);
		$arrRet = Tieba_Service::call('anti', 'antiActsctrlQuery', $arrParam, null, null, 'post', 'php', 'utf-8');
		if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
			$strMsg = sprintf('call anti::antiActsctrlQuery fail with input [%s] output [%s]', serialize($arrParam), serialize($arrRet));
			Bingo_Log::warning($strMsg);
			return $intLeftTimes;
		}

		$intLimitTimes = (int)$arrRet['res']['Anti_limit'];
		$intCurTimes   = (int)$arrRet['res']['Anti_count'];
		$intLeftTimes  = max(0, $intLimitTimes - $intCurTimes);

		Tieba_Stlog::addNode('cur_limit', $intCurTimes);
		Tieba_Stlog::addNode('limit', $intLimitTimes);

		return $intLeftTimes;
	}

	/**
	 * @desc 构造活动数据
	 * @param
	 * @return
	 */
	protected function _buildCdInfo() {
		$arrCdInfo       = $this->_arrData['cd_info'][0];
		$intOilNum       = (int)$this->_arrData['oil_num'][$this->_arrReq['id']]['oil_num'];
		$intTravelTime   = (int)($arrCdInfo['travel_time']);
		$intTargetOilNum = (int)($arrCdInfo['oil_num']);
		$intNow          = time();

		Bingo_Log::pushNotice('travel_time', $intTravelTime);
		Bingo_Log::pushNotice('target_oil', $intTargetOilNum);
		Bingo_Log::pushNotice('cur_oil', $intOilNum);

		// 不满足巡游条件，而进来的，打回原形
		if (!($intNow >= $intTravelTime && $intOilNum >= $intTargetOilNum)) {
			$strHome = "http://tieba.baidu.com/mo/q/";
			Bingo_Log::pushNotice('jump', 0);
			header("Location:".$strHome);
		}

		// 判断当前吧是否在巡游列表里，滥竽充数的话，打回原形
		$intId    = (int)$this->_arrData['cd_info'][0]['id'];
		$bolValid = false;
		$intNow   = time();
		foreach($this->_arrData['travel_list'][$intId] as $arrItem) {
			$intFid       = (int)($arrItem['forum_id']);
			$intStartTime = (int)($arrItem['start_time']);
			$intEndTime   = (int)($arrItem['end_time']);
			if ($intFid === $this->_arrReq['forum_id'] && $intNow >= $intStartTime && $intNow < $intEndTime) {
				$bolValid = true;
			}
		}
		if (false === $bolValid) {
			$strHome = "http://tieba.baidu.com/mo/q/";
			Bingo_Log::pushNotice('jump', 1);
			header("Location:".$strHome);
		}

		// 拉取活动的策略 
		$arrParam = array(
			'type' => array($arrCdInfo['category']),
		);
		$arrRet   = Tieba_Service::call('pluto', 'getCategoryByType', $arrParam, null, null, 'post', 'php', 'utf-8');
		if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
			$strMsg = sprintf('call pluto::getCategoryByType fail with input[%s] output[%s]', serialize($arrParam), serialize($arrRet));
			throw new Exception($strMsg, Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}
		$this->_arrCategory = $arrRet['data'][$arrCdInfo['category']];

		// 巡游只需要这些数据
		$arrOut = array(
			'id'             => $arrCdInfo['id'],
			'forumId'        => $arrCdInfo['forum_id'],
			'forumName'      => $arrCdInfo['forum_name'],
			'title'          => $arrCdInfo['title'],
			'desc'           => $arrCdInfo['desc_text'],
			'url'            => Tbapi_Platform_Star_Function::getThreadIdByThreadUrl($arrCdInfo['url']),
			'targetUrl'      => Tbapi_Platform_Star_Function::getThreadIdByThreadUrl($arrCdInfo['target_url']),
			'travelTime'     => $intTravelTime,
			'starPic'        => $arrCdInfo['background_pic'],
		);
		return $arrOut;
	}
}
/* vim: set noexpandtab ts=4 sw=4 sts=4 tw=100: */
