<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2016/5/18
 * Time: 17:07
 */

class modifyTeamAction extends Mo_Core_Action{

    private $arrInput = array();

    //必须实现，其内部逻辑拆分自便
    protected function _execute(){
        if ($this->_input()) {
            $this->_process();
        }
    }

    /**
     * @brief input
     * @return $this
     */
    private function _input() {
        $this->user = Mo_Service_Wap::getTplUser();
        $this->wreq = Mo_Service_Wap::getTplWreq();

        if ($this->user['is_login'] == false) {
            return false;
        }


        //校验参数，获取信息等
        $this->arrInput = array(
            'id' => intval($this->wreq['third_group']),
            'user_id' => intval($this->user['id']),
            'cinema_id' => intval($this->wreq['cinema_id']),
            'team_type' => intval($this->wreq['team_type']),
        );
        return true;
    }

    private function _process(){
        Mo_Response::$bolAjaxCommit = true; //标记返回值类型为json


        //此方法若逻辑较复杂，请自行拆分
        $ret = Tieba_Service::call("team","modifyGroup",$this->arrInput,null, null, 'post','php', 'utf-8');
        if ($ret != false && $ret['errno'] == Tieba_Errcode::ERR_SUCCESS) {
            Mo_Response::$intErrno = $ret['errno'];
            Mo_Response::$strError = $ret['errmsg'];
            Mo_Response::$arrErrorData = $ret['data'];
            return ;
        } else {
            Mo_Response::$intErrno = $ret['errno'];
            Mo_Response::$strError = $ret['errmsg'];
            Mo_Response::$arrErrorData = array();
            return ;
        }

    }

    //必须实现
    protected function _log(){
        //如果数据组没有提需求，这里留空即可，这里我们假设这个请求不算pv，故将log中的ispv字段设为0（默认1）
        Tieba_Stlog::addNode('ispv', 0);
    }
}