<?php
/**
 * Created by PhpStorm.
 * User: wangjunsheng
 * Date: 2016/5/18
 * Time: 14:25
 */

class getAllCityAction extends Mo_Core_Action{

    private $arrInput = array();

    //必须实现，其内部逻辑拆分自便
    protected function _execute(){
        if ($this->_input()) {
            $this->_process();
        }
    }

    /**
     * @brief input
     * @return $this
     */
    private function _input() {
        return true;
    }

    private function _process(){
        Mo_Response::$bolAjaxCommit = true; //标记返回值类型为json


        //此方法若逻辑较复杂，请自行拆分
        $ret = Tieba_Service::call("team","getAllCity",$this->arrInput,null, null, 'post','php', 'utf-8');
        if ($ret != false && $ret['errno'] == Tieba_Errcode::ERR_SUCCESS) {
            $city = array(
                'city' => $ret['data'],
            );
            Mo_Response::$intErrno = $ret['errno'];
            Mo_Response::$strError = $ret['errmsg'];
            Mo_Response::$arrErrorData = $city;
            return ;
        }

    }

    //必须实现
    protected function _log(){
        //如果数据组没有提需求，这里留空即可，这里我们假设这个请求不算pv，故将log中的ispv字段设为0（默认1）
        Tieba_Stlog::addNode('ispv', 0);
    }
}