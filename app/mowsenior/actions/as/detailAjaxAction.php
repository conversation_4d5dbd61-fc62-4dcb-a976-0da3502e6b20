<?php
/**
 *  as详情接口
 *
 *  <AUTHOR>
 *  @version 2014-6-16 15:04:01
 *  
 */
class detailAjaxAction extends Mo_Core_Action{
    protected $strTplName = 'as_detail_ajax.php';
    protected $strTplPath = 'syunying';
    private $_strAppKey    = ''; //app key
    private $_strPage = '';//页面
    private $_strLocate = '';//页面位置

    protected  function _execute(){
        if($this->_input()) {
            $this->_process();
        }
    }
   
    private function _input() {
        $this->_strAppKey   = Mo_Request::get('app_key');
        $this->_strPage   = Mo_Request::get('page');

        if(empty($this->_strAppKey) || empty($this->_strPage)) {
            Bingo_log::warning('get as detail params error');
            return false;
        }

        return true;
    }

    private function _process() {
        //连接ksarch的Wordserver服务
        //连接的是默认的Wordserver服务, 如果在词表mis中，没有设置过pid,tk,app参数， 则表示该词表使用的是默认的Wordserver服务
        $handleWordServer = Wordserver_Wordlist::factory();

        //根据key获取value -----//
        $strKey1 = 'Content';
        $strTableName = 'tb_wordlist_redis_mowsenior_as_app';//英文表名

        $arrKeys = array($strKey1);
        $arrItemInfo = $handleWordServer->getValueByKeys($arrKeys,$strTableName);

        $value1 = unserialize($arrItemInfo[$strKey1]); //如果在mis后台填的是数组， 这时需要 unserialize() 处理后，转换为数组
        //----- 根据key获取value//
        if(!empty($value1)) {        
            $arrOutput = $value1['detail'][$this->_strAppKey];
        }

        if($arrOutput['error']['errno'] === 0) {
            //字段转换
            $item = array();
            $item['title'] = $arrOutput['data']['app_name'];
            $item['downloadUrl'] = $arrOutput['data']['download_url'];
            $item['introduce'] = $arrOutput['data']['app_detail'];
            $item['previews'] = array();
            foreach ($arrOutput['data']['show_imgs'] as $key => $value) {
                $item['previews'][] = $value['img'];
            }

            $pageData = array();
            $pageData['app'] = array();
            $pageData['app'][] = $item;
            $pageData['page'] = $this->_strPage=='frs' ? 'sfrs' : 'spb';
            $pageData['locate'] = $this->_strLocate;
            Mo_Response::addViewData('pageData', $pageData);
        }
        else {
            Bingo_log::warning('get as detail service error');
        }
    }

    protected function _log(){
        Tieba_Stlog::addNode('ispv', 0);
    }
}
?>