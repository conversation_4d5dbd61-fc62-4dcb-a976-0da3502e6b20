<?php
/**
 * Author: wangzhonghua
 * Date: 2013-07-01
 * Description:search forum
 */

class voiceAction extends Mo_Core_Action{
    private $_strMd5 = '';//语音md5
    private $_arrVoice = array();//语音结果

 	protected $strTplName = '';
    protected $strTplPath = '';
	
    protected  function _execute(){
        if($this->_input()){
        	 $this->_process();
        } 
    }
	
    private function _input(){
        $this->_strMd5 = Mo_Request::get('md5','');
		if(empty($this->_strMd5)){
			return false;
		}
        return true;
    }
	
    private function _process(){
    	if(!method_exists('Ffmpeg_Ffmpeg','audio_format_convert')){
    		echo '';
			return false;
		}
    	$tmpAmrFilePath = dirname(__FILE__).'/voice_'.$this->_strMd5.'.amr';
		$tmpMp3FilePath = dirname(__FILE__).'/voice_'.$this->_strMd5.'.mp3';
        $arrInput = array(
            'voice_md5' => $this->_strMd5
        );
		$arrOut = Mo_Data::get('voice', 'get_voice_stream', $arrInput);
		
		if($arrOut && isset($arrOut['voice'][0]['voice_content'])){
			$video_content = $arrOut['voice'][0]['voice_content'];
			$file = fopen($tmpAmrFilePath, 'w');
            $ret =  fwrite($file, $video_content);
            fclose($file);
            $convertRet = Ffmpeg_Ffmpeg::audio_format_convert($tmpAmrFilePath, $tmpMp3FilePath);
			if($convertRet < 0){
                unlink($tmpAmrFilePath);
                unlink($tmpMp3FilePath);
				echo '';
				return false;
			}else{
	            $content = file_get_contents($tmpMp3FilePath);
				$voice_length = filesize($tmpMp3FilePath);
			}
            header("X-Powered-By: ");
            header("Accept-Ranges: bytes");
            header("Content-Range:bytes 0-".$voice_length."/".$voice_length);
            header("Content-Type:audio/mpeg");
            header("Content-Length:". $voice_length);
            header("content-disposition:attachment; filename=".$this->_strMd5.".mp3");
            header("Content-Transfer-Encoding: binary");
			
			unlink($tmpAmrFilePath);
			unlink($tmpMp3FilePath);
		}else{
			$content = '';
		}
		echo $content;
    }
	
    protected function _log(){
		
    }
}
