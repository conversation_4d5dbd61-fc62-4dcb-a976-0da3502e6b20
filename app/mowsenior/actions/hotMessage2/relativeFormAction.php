<?php
/**
 * 热点聚合 Ajax
 * User: dongtiancheng
 * Date: 2015/9/21
 * Time: 17:11
 */

class relativeFormAction extends Mo_Core_Action {
    protected function _execute()
    {
        //参数获取
        $this->_intUid = intval(Mo_Request::$intUid);
        $this->_strUname = Mo_Request::$strUname;
        $user = Mo_Service_Wap::getTplUser();
        $wreq = Mo_Service_Wap::getTplWreq();

        $topic_id = Mo_request::get('topic_id');
        $num = Mo_request::get('num', 10);

        if (isset($topic_id) && $num) {
            $arrInput = array(
                'req' => array(
                    'topic_id' => $topic_id,
                    'num' => $num,
                ),
            );
            $acts = Tieba_Service::call('hottopic', 'getTopicRelateForum', $arrInput , null, null, 'post', 'php', 'utf-8');

            $formIds = array();

            foreach ($acts['ret'] as $key => $value) {
                array_push($formIds, $value['forum_id']);
            }

            $res = Tieba_Service::call('forum', 'mgetBtxInfoEx', array(
                'forum_id' => $formIds,
            ), null, null, 'post', 'php', 'utf-8');

            $res['user'] = $user;
            $res['wreq'] = $wreq;

            $user_id = $user['id'];
            $param = array(
                'user_id'=>$user_id,
                'check_forum'=>1,
            );

            $user_infor = Tieba_Service::call('perm', 'getLikeForumListIndex', $param, null, null, 'post', 'php', 'utf-8');

            foreach ($res['output'] as $resOutput => $value) {
                if (isset($user_infor['output']['member_list'][$resOutput])) {
                    $res['output'][$resOutput]['hasFocused'] = 1;
                }
                else {
                    $res['output'][$resOutput]['hasFocused'] = 0;
                }
            }

            $res['output'] = array_values($res['output']);

            if (isset($acts['errno']) && $acts['errno'] == 0) {
                Mo_Response::$bolAjaxCommit = true;
                Mo_Response::$intErrno = 1;
                Mo_Response::$strError = '';
                Mo_Response::$arrErrorData = $res;
                return;
            }
        }
    }

    protected function _log() {

    }
}