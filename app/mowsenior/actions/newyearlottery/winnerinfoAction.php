<? 
/**
 * @disc: 新春群乐汇当前用户中奖信息
 */

class winnerinfoAction extends Mo_Core_Action{
    protected $strTplName = 'festerval_user_info.php';
    protected $strTplPath = 'swebview';

    private $arrPrizeInfo = array();
    private $arrWinRecord = array();
    private $arrWinnerInfo = array();

    protected  function _execute(){
        if ($this->_validateInput()){
            $this->_process();
        }
        $this->_build();
    }
    private function _validateInput() {
        preg_match('/^[\d]*/', Mo_Request::get('op_time', ''), $arrMatch);
        Mo_Request::set('op_time', $arrMatch[0]);
        if ('' !== Mo_Request::get('prize_id', '') && '' !== Mo_Request::get('op_time', '')) {
            return true;
        } else {
            return false;
        }
    }
    private function _process(){
        if(Mo_Request::$bolLogin){
            //获取奖品信息
            $arrInput['req'] = array(
                'prize_id'  =>  Mo_Request::get('prize_id')
            );
            $this->arrPrizeInfo = Tieba_Service::call('lottery','getPrize',$arrInput,NULL,NULL,'post','php','utf-8');
            //获取该用户的获奖记录
            $arrInput['req'] = array(
                'user_id'   =>   Mo_Request::$intUid,
            );
            $arrAllWinRecord = Tieba_Service::call('lottery','getWinRecordByUid',$arrInput,NULL,NULL,'post','php','utf-8');
            //Mo_Response::addViewData('all_win_record', $arrAllWinRecord);
            //取出满足中奖时间的记录
            foreach ($arrAllWinRecord['res'] as $value) {
                if ($value['op_time'] == Mo_Request::get('op_time') && $value['prize_id'] == Mo_Request::get('prize_id')) {
                    $this->arrWinRecord = $value;
                }
            }
            //获取中奖者基本信息
            if (!empty($this->arrWinRecord)) {
                $arrInput['req'] = array(
                    'user_id'   =>   Mo_Request::$intUid,
                    'prize_id'  =>  $this->arrWinRecord['prize_id'],
                    'op_time'   =>  $this->arrWinRecord['op_time']
                );
                $this->arrWinnerInfo = Tieba_Service::call('lottery','getWinnerInfo',$arrInput,NULL,NULL,'post','php','utf-8');
            }
        }
    }
    private function _build() {
        Mo_Response::addViewData('prize_info', $this->arrPrizeInfo);
        Mo_Response::addViewData('win_record', $this->arrWinRecord);
        Mo_Response::addViewData('winner_info', $this->arrWinnerInfo);
    }
    protected function _log(){
        Tieba_Stlog::addNode('ispv',0);
    }
}
