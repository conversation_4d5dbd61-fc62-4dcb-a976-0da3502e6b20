<?php
/*
 * @Author: <EMAIL>
 * @LastEditors: <EMAIL>
 * @Date: 2020-08-21 11:23:24
 * @LastEditTime: 2020-09-04 15:25:58
 * @FilePath: /baidu/tieba-server-base/wap-mobile-mowsenior/actions/recommend/getPersonalizedSwitchAction.php
 * @Description: 获取个性化推荐开关状态
 */
class getPersonalizedSwitchAction extends Mo_Core_Action
{
    //
    protected function _execute()
    {
        $intUserId = Mo_Request::$intUid;
        $intClientType = Mo_Request::get('client_type',1);
        if(!in_array($intClientType,array(1,2))){
            return $this->_error(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrInput = array(
            'user_id' => $intUserId,
            'client_type' => $intClientType,
        );
        if (empty($intUserId)){
            return $this->_error(Tieba_Errcode::ERR_USER_NOT_LOGIN);
        }
        $arrOutput = Tieba_Service::call('common', 'getPersonalizedSwitch', $arrInput);
        // var_dump($arrOutput);die;
        if ($arrOutput === false || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            return $this->_error(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        
        $arrData = array(
            'status' => intval($arrOutput['data']['closed']) == 1 ? 0 : 1,
            'client_type' => intval($arrOutput['data']['client_type'])
        );
        return $this->_success($arrData);
    }

    protected function _success($data = array())
    {
        Mo_Response::$bolAjaxCommit = true;
        Mo_Response::$intErrno = Tieba_Errcode::ERR_SUCCESS;
        Mo_Response::$strError = 'success';
        Mo_Response::$arrErrorData = $data;
        return true;
    }

    protected function _error($errno, $msg = '')
    {
        Mo_Response::$bolAjaxCommit = true;
        Mo_Response::$intErrno = $errno;
        Mo_Response::$strError = !empty($msg) ? $msg : Tieba_Error::getErrmsg($errno);
        Mo_Response::$arrErrorData = array();
        return false;
    }

    protected function _log()
    {
    }
}
