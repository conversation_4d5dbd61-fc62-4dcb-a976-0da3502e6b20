<?php
/**
 * Author: liweitao01
 * Date: 14-6-16
 * Description: 新独立消息页的action
 */
class msgPageAction extends Mo_Core_Action{

    protected $strTplPath = 'suser';
    protected $strTplName = 'msg.php';
    protected $strMsgType = 'atme';

    private $arrMessage = array();

    protected  function _execute(){
        $this->_process();
        $this->_build();
    }

    private function _process() {
        $this->arrMessage = Mo_Data::get('message', 'getmessage', array(
            'user_portrait' => Mo_Request::$strPortrait,
        ));
        if(strpos(Mo_Request::$strReqAddr,"atme")===false){
            $this->strMsgType="replyme";
        }
    }

    private function _build() {
        Mo_Response::addViewData('base',Mo_Service_Wap::getTplBase());
        Mo_Response::addViewData('user',Mo_Service_Wap::getTplUser());
        Mo_Response::addViewData('wreq',Mo_Service_Wap::getTplWreq());
        Mo_Response::addViewData('msgtype',$this->strMsgType);
        Mo_Response::addViewData('message',$this->arrMessage);
    }

    protected  function _log(){
        Tieba_Stlog::addNode('ispv',1);
    }
}
