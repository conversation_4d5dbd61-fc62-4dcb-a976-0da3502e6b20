<?php
class tiebaplusCollegeDataAction extends Mo_Core_Action {

    private $_page_no = 1;    //第几页
    private $_page_size = 10;
    private $_has_more = 1; //  是否还有数据

    private function _input(){
        $this->_page_no = Bingo_Http_Request::getNoXssSafe('page_no', 1);
        $this->_page_size = Bingo_Http_Request::getNoXssSafe('page_size', 10);
        return true;
    }

    /**
     * 执行函数-推广首页异步接口
     */
    protected function _execute(){
        Mo_Response::$bolAjaxCommit = true;
        Mo_Response::$intErrno = 0;
        Mo_Response::$strError = 'success';
        if($this->_input()){
            $this->_process();
            return true;
        }
    }

    protected function _process(){
        $intNowTime = time();
        $objMulti = new Tieba_Multi("indexdata");
        //获取banner数据
        $arrBannerInput = array(
            'serviceName' => 'adsense',
            'method' => 'getTiebaplusCollegeBannerList',
            'ie' => 'utf-8',
            'input' => array(
                'fields' => array('id','img_url','jump_url','start_time','end_time'),
                'conds' => array( 'start_time < ' => $intNowTime,
                    'end_time >'  => $intNowTime,
                    'page_size=' =>4,
                    'page_num=' =>1,
                    'is_delete='  => 0),
                'append' => 'order by id asc'
            ),
        );
        $objMulti->register('banner', new Tieba_Service("adsense"), $arrBannerInput);
        //获取tab数据
        $arrTabInput = array(
            'serviceName' => 'adsense',
            'method' => 'getTiebaplusCollegeTabList',
            'ie' => 'utf-8',
            'input' => array(
                'fields' => array('id','pid','name','detail','level','sort'),
                'conds' => array(
                    'is_delete='  => 0,
                    'page_size=' =>1000,
                    'page_num=' =>1),
                'append' => 'order by sort asc'
            ),
        );
        $objMulti->register('tab', new Tieba_Service("adsense"), $arrTabInput);
        //获取feed数据
        $objMulti->call();
        //获取返回值信息
        $arrObjOut =  $objMulti->results;
        $arrData = array();
        //banner数据
        if(false ==$arrObjOut['banner'] || 0!=$arrObjOut['banner']['errno']){
            Bingo_Log::warning("Call adsesne:getTiebaplusCollegeBannerList failed. [input=]'"
                .serialize($arrBannerInput).'[Output=]'.serialize($arrObjOut['banner']));
            Mo_Response::$intErrno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            Mo_Response::$strError = "service call fail";
            return;
        }
        $arrBannerList = isset($arrObjOut['banner']['data']['list'])?$arrObjOut['banner']['data']['list']:array();
        $arrData['bannerList'] = $arrBannerList;
        //tab数据
        if(false ==$arrObjOut['tab'] || 0!=$arrObjOut['tab']['errno']){
            Bingo_Log::warning("Call adsesne:getTiebaplusCollegeTabList failed. [input=]'"
                .serialize($arrTabInput).'[Output=]'.serialize($arrObjOut['tab']));
            Mo_Response::$intErrno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            Mo_Response::$strError = "service call fail";
            return;
        }
        $arrTabList = isset($arrObjOut['tab']['data']['list'])?$arrObjOut['tab']['data']['list']:array();
        $arrData['tabList'] = $this->createTab($arrTabList);
        //feed数据
        $arrData['feedList'] = $this->getTabFeed($arrData['tabList'][0]);
        $arrData['hasMore'] = $this->_has_more;
        Mo_Response::$arrErrorData = $arrData;
        return;
      /*  $active_center = array();
        $active_center['win_title'] = '福利来了';
        $active_center['win_desc'] = '新人专享，7天';
        $active_center['win_jump_time'] = 3;
        $active_center['is_new_window'] = 1;
        $active_center['mission'] = array (
            'active_id' => '101',
            'mission_id' => '9',
            'task_type' => '9',
            'mission' => "关注吧",
            'cleartype' => 1,
            'cleartime' => 86340,
            'total_limit' => '2',
            'final_reward_url' => '',
            'desc'=>"关注吧",
        );
        Mo_Response::$arrErrorData = $active_center;*/
        return;
    }

    private function createTab($arrTabs){
        if(empty($arrTabs)){
            return $arrTabs;
        }
        //return    $arrTabs;
        $pidMap = array();
        foreach ($arrTabs as $tab){
            $pid = $tab['pid'];
                if(!isset($pidMap[$pid])){
                    $pidMap[$pid] = array($tab);
                }else{
                    $pidMap[$pid][] = $tab;
                }
        }

        $level1TabList = $pidMap[0];
        foreach ($level1TabList as &$tab){
            $tabId = $tab['id'];
            if(isset($pidMap[$tabId])){
                $tab['subTabList'] = $pidMap[$tabId];
                //全部数据
                $tabAll = array();
                $tabAll['id']=$tab['id'];
                $tabAll['pid']=$tab['pid'];
                $tabAll['name']='全部';
                $tabAll['detail']=$tab['detail'];
                $tabAll['level']=$tab['level'];
                $tabAll['sort']=$tab['sort'];
                $tempList = array($tabAll);
                $tab['subTabList'] = array_merge($tempList,$tab['subTabList']);
            }
        }
        return  $level1TabList;
    }

    private function getTabFeed($arrTab){
        if(empty($arrTab)){
            return array();
        }
        $intTabId = $arrTab['id'];
        $arrInput = array(
            'fields' => array('id','level1_tab_id','level2_tab_id','name','detail','img_url','jump_url','data_tag','data_tag_join'),
            'conds' => array(
                'is_delete='  => 0,
                'page_size=' =>$this->_page_size,
                'page_num=' =>$this->_page_no,
                'level1_tab_id=' =>$intTabId)
        );

        $arrRet = Tieba_Service::call('adsense', 'getTiebaplusCollegeFeedList', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(false == $arrRet || 0!=$arrRet['errno']){
            Bingo_Log::warning("Call adsesne:getTiebaplusCollegeFeedList failed. [input=]'"
                .serialize($arrInput).'[Output=]'.serialize($arrRet));
            return array();
        }

        if(empty($arrRet['data']['list'])){
            Bingo_Log::warning("Call adsesne:getTiebaplusCollegeFeedList empty. [input=]'"
                .serialize($arrInput).'[Output=]'.serialize($arrRet));
            $this->_has_more = 0;
            return array();
        }

        $threadList = $this->geThreadData($arrRet['data']['list']);
        foreach ($arrRet['data']['list'] as &$item){
            $item['is_video'] = 0;
            if(!isset($item['thread_id'])){
                continue;
            }
            $threadId = $item['thread_id'];
            if(isset($threadList[$threadId])){
                $tidResult = $threadList[$threadId];
                if (isset($tidResult['video_info']) && !empty($tidResult['video_info'])) {
                    $item['is_video'] = 1;
                }
            }
        }

        $this->_has_more = $arrRet['data']['hasMore'];

        return $arrRet['data']['list'];

    }

    private function geThreadData(&$dataList){
        $arrThreadList = array();
        foreach ($dataList as &$item){
            if(isset($item['jump_url'])){
                $jumpUrl = $item['jump_url'];
                if(strpos($jumpUrl,'?') !== false){
                    $arrSplit = explode('?',$jumpUrl);
                    $arrSplit = explode('/',$arrSplit[0]);
                    $count = count($arrSplit);
                    $threadId = $arrSplit[$count-1];
                    if(is_numeric($threadId)){
                        $arrThreadList[$threadId] = $threadId;
                        $item['thread_id'] = $threadId;
                    }
                }else{
                    $arrSplit = explode('/',$jumpUrl);
                    $count = count($arrSplit);
                    $threadId = $arrSplit[$count-1];
                    if(is_numeric($threadId)){
                        $arrThreadList[$threadId] = $threadId;
                        $item['thread_id'] = $threadId;
                    }
                }
            }
        }

        $arrThreadList = array_values($arrThreadList);
        return $this->_getThreadInfo($arrThreadList);
    }

    /**
     * 获取帖子信息
     */
    private function _getThreadInfo($arrTids) {
        $input = array(
            'thread_ids'    => $arrTids,
            'forum_id'          => 0,
            'need_abstract'     => 0,
            'need_photo_pic'    => 0,
            'need_user_data'    => 0,
            'icon_size'         => 0,
            'need_forum_name'   => 0,
            'call_from'         => 'default',
        );
        $output = Tieba_Service::call('post', 'mgetThread', $input, null, null, 'post', 'php', 'utf-8');
        if (!$output || $output['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
            Bingo_Log::warning(sprintf('call post::mgetThread fail, input:[%s], output:[%s]', serialize($input), serialize($output)));
            return false;
        }
        return $output['output']['thread_list'];
    }



    protected function _log()
    {
        Tieba_Stlog::addNode('ispv', 1);
    }
}