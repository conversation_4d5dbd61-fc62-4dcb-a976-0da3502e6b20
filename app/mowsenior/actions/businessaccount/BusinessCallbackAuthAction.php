<?php
/**
 * User: liu<PERSON>hui
 * Date: 2020/8
 * Desc: 商户号--给支付方的回调接口（带着商户ID、订单号、支付状态来通知我）
 */

class BusinessCallbackAuthAction extends Mo_Core_Action {
    
    const AUTH_TIEBA = 19;//19-官方标签
    const STATUS_PASS = 2;//通过
    const STATUS_UNPASS = 4;//拒绝
    const PUSH_UID = 4892865941;
    const PUSH_UNAME = '官方号小助手';
    const TASK_ID = "103";
    const SERVICE_ID = "103";

    private $objMulti;
    /**
     * [_execute description]
     * @return [type] [description]
     */
    protected function _execute() {
        $ret = json_decode($GLOBALS['HTTP_RAW_POST_DATA'],1);
        $ret = json_decode($ret['messageBody'],true);
        if($ret['authtype'] == self::AUTH_TIEBA)
        {
            Bingo_Log::warning("jieshouauthret====".var_export($ret,1));
            $queryByUidReq['shoubai_c_appid'] = $ret['officialid'];
            $queryByUidRes = Tieba_Service::call('common', 'getBusinessData', $queryByUidReq,null, null, 'post', 'php', 'utf-8');

            if($ret['status'] == self::STATUS_PASS)
            {
                if($queryByUidRes['data'][0]['user_id']>0 && $queryByUidRes['data'][0]['audit_status_auth'] != 1)
                {
                    $editInput = array(
                        'update_fields' => array(
                            'modify_time'=> time(),
                            'pass_time' => time(),
                            'audit_status_auth' => 1,
                        ),
                        'condition' => array(
                            'user_id' => $queryByUidRes['data'][0]['user_id'],
                        ),
                    );
                } 
            }
            elseif($ret['status'] == self::STATUS_UNPASS)
            {
                if($queryByUidRes['data'][0]['user_id']>0 && $queryByUidRes['data'][0]['audit_status_auth'] != 2)
                {
                    $editInput = array(
                        'update_fields' => array(
                            'modify_time'=> time(),
                            'auth_last_fail_time' => time(),
                            'audit_status_auth' => 2,
                        ),
                        'condition' => array(
                            'user_id' => $queryByUidRes['data'][0]['user_id'],
                        ),
                    );
                }    
            }
            if($editInput['update_fields']['modify_time']!=null && $editInput['update_fields']['audit_status_auth']!=null)
            {
                $dataRet  = Tieba_Service::call('common', 'editBusinessInfo', $editInput,null, null, 'post', 'php', 'utf-8');
                Bingo_Log::warning("Auth Result editBusinessInfo==".var_export($dataRet,1));
                if(Tieba_Errcode::ERR_SUCCESS !== $dataRet['errno'])
                {
                    Bingo_Log::warning(sprintf("BusinessCallbackAuth editBusinessInfo failed.[input = %s][output = %s]",serialize($editInput), serialize($dataRet)));
                }
                else
                {
                    if($editInput['update_fields']['audit_status_auth'] == 1)
                    {
                        //写DB成功后，标签审核通过的，将商户号身份写入用户属性
                        $arrParams = array(
                            'user_id' => $queryByUidRes['data'][0]['user_id'],
                            'attr_name' => 'business_account',
                            'attr_value' => array(
                                'status' => 1,
                            ),
                        );
                        $arrRes = Tieba_Service::call('user', 'setUserAttrByArray', $arrParams, null, null, 'post', 'php', 'utf-8');
                        if(Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno'])
                        {
                            Bingo_Log::warning(sprintf("setUserAttrByArray failed.[input = %s][output = %s]",serialize($arrParams), serialize($arrRes)));
                            header('HTTP/1.1 503 Service Temporarily Unavailable', true, 503);
                        }
                        else
                        {
                            //用户属性写成功的，把用户的昵称改成提交的name（后面+熊掌emoji）
                            $editNicknameRes = array(
                                'user_id' => $queryByUidRes['data'][0]['user_id'],//**********,
                                'attr_name' => 'user_nickname',
                                'attr_value' => $queryByUidRes['data'][0]['business_nickname']."\xF0\x9F\x90\xBE",
                            );
                            $editNicknameRet = Tieba_Service::call('user', 'setUserAttr', $editNicknameRes, null, null, 'post', 'php', 'utf-8'); 
                            if(Tieba_Errcode::ERR_SUCCESS !== $editNicknameRet['errno'])
                            {
                                Bingo_Log::warning(sprintf("editUserNickname failed.[input = %s][output = %s]",serialize($editNicknameRes), serialize($editNicknameRet)));
                                header('HTTP/1.1 503 Service Temporarily Unavailable', true, 503);
                            }
                            else{
                                //修改昵称成功后，过UEG事后审核
                                $this->_antiCommit($editNicknameRes['user_id'],$editNicknameRes['attr_value']);
                            }
                        }
                    }
                    //标签审核通过/未通过，都给用户发PUSH
                    $text = array(
                        2=>"尊敬的用户，您的商家资质未通过审核，还请修改后重新提交。超过30天未提交修改认证流程将自动关闭。感谢您对贴吧的支持，如有问题请联系 @官方号小助手",
                        1=>"尊敬的用户，恭喜您申请的贴吧官方号认证审核已通过，各项权益均已生效。感谢您对贴吧的支持，如有问题请联系 @官方号小助手",
                    );
                    $sendPushReq = array(
                        'title' => '官方号审核结果通知',
                        'content'   => $text[$editInput['update_fields']['audit_status_auth']],
                        'url'       => 'https://tieba.baidu.com/mo/q/wise-business-merchant/businessIndex',
                        'image_url'     => '',
                        'uid'       => array($queryByUidRes['data'][0]['user_id']),
                        'user_id'   => self::PUSH_UID,
                        'user_name' => self::PUSH_UNAME,
                        'task_id'   => self::TASK_ID,
                        'service_id'    => self::SERVICE_ID,
                        'user_type' => 4,
                        'msg_type'  => 1,
                    );
                    $sendPushRet = Tieba_Service::call('common', 'newPushMsg', $sendPushReq,null, null, 'post', 'php', 'utf-8');
                    if($sendPushRet['errno'] != Tieba_Errcode::ERR_SUCCESS)
                    {
                        Bingo_Log::warning('call newPushMsg failed,input:' .serialize($sendPushReq) . ' output:' . serialize($sendPushRet));
                    } 
                }
            }
        }
        return $this->_errRet(Tieba_Errcode::ERR_SUCCESS, $dataRet);
    }

    /**
     * antiCommit
     * @return boolean
     */
    private function _antiCommit($uid,$nickname)
    {
        $arrParams = array
        (
            'anti_cmd' => 'recallApp',
            'req' => array(
                'user_id' => $uid, //发送请求的用户id
                'user_name' => "",//发送请求的用户的用户名
                'forum_id' => 0,//吧id，没有添0
                'word' => '',//吧名，没有添“”
                'content' => $nickname,//添加好友的验证信息
                'app_key' => 36,//昵称信息指定
                'ip' => '',//用户本次操作ip
                'thread_id' => 0,//主题id,没有添0
                'post_id' => 0,//贴子id，没有添0
                'user_id_ed' => 0,//接受好友请求用户id，为有多用户之间交互的业务预留，没有添0
                'user_name_ed' => '',//接受好友请求用户的用户名，为有多用户之间交互的业务预留，没有添“”
                'client_version' => '',
                'client_type' => '',
                'os_version' => '',
                'brand' => '',
                'brand_type' => '',
            ),
        );
        $arrRet = Tieba_Service::call('anti', 'antiCommit', $arrParams, null, null, 'post', 'php', 'utf-8');
        if (false == $arrRet) {
            Bingo_Log::warning("call anti_antiCommit_false. input[" . serialize($arrParams) . ']');
        }
        return true;
    } 
 
    /**
     * @param $errno
     * @param array $data
     * @return bool
     */
    private function _errRet($errno, $data = array()) {
        $arrData = array(
            'errno' => ($errno == Tieba_Errcode::ERR_SUCCESS) ? 0 : -1,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        );
        echo Bingo_String::array2json($arrData);
        return true;
    }

    /**
     * [_log description]
     * @return [type] [description]
     */
    protected function _log() {
        Tieba_Stlog::addNode('ispv', 0);
    }
}

?>
