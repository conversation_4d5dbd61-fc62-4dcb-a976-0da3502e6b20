<?php
/**
 * User: ding<PERSON>hu
 * Date: 2015-09-28
 * 会员中心做任务接口
 */
class acceptProposeAction extends Mo_Core_Action{
    
    /**
     * @brief:
     * @param:null
     * @return:null
     **/
    protected function _execute() {
		Mo_Response::$bolAjaxCommit = true; //标记返回值类型为json
		Mo_Response::$intErrno = 0;
		Mo_Response::$strError = 'success';
		Mo_Response::$arrErrorData = array();
        
        try {
			$this->user = Mo_Service_Wap::getTplUser();
            $this->wreq = Mo_Service_Wap::getTplWreq();
            
            if ($this->user['is_login'] !== true ){
                //未登陆
                throw new Exception("user need login!",Tieba_Errcode::ERR_USER_NOT_LOGIN);
			}
            
            //参数获取
            $propose_to_user_id = intval(Mo_request::get('propose_to_user_id', 1));
            $aoth= trim(Mo_request::get('oath', 1));

            $user_id = intval($this->user['id']);
			$user_name	= $this->user_id['name'];

            //tbs验证
            $tbs = strval(Mo_request::get('tbs'));
            if( empty($tbs) ){
                throw new Exception("tbs is empty", Tieba_Errcode::ERR_PARAM_ERROR);
            }
            if( !Tieba_Tbs::check($tbs, true) ){
                //tbs验证失败
                throw new Exception("tbs check fail!", Tieba_Errcode::ERR_NVOTE_INVALID_TBS);
            }
            
            if( $user_id <= 0  || $propose_to_user_id <= 0){
                throw new Exception('params error.', Tieba_Errcode::ERR_PARAM_ERROR);
            }


			$arrParam	= array(
				'user_id'	=> $user_id,
				'propose_to_user_id'	=> $propose_to_user_id
			);

			$arrRet = Tieba_Service::call('marriage','newAcceptMarriage',$arrParam,null,null,'post','php','utf-8');
			
			if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
				Bingo_Log::warning("call newProposeMarriage failed.input:[".serialize($arrParam)."].output:[".serialize($arrRet)."].");
                throw new Exception($arrRet['errmsg'],$arrRet['errno']);
			}
            
			$data	= $arrRet['data'];
            
			Mo_Response::$intErrno = $arrRet['errno'];
			Mo_Response::$strError = $arrRet['errmsg'];
            Mo_Response::$arrErrorData = $data;
        }catch(Exception $e){
            Bingo_Log::warning( "no=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            //$this->_jsonRet($e->getCode(), Tieba_Error::getUserMsg($e->getCode()));
			Mo_Response::$intErrno = $e->getCode();
			Mo_Response::$strError =$e->getMessage();
        }

        return true;
    }
	
    /**
     * @biref:
     * @param:null
     * @reutrn:null
     **/
    protected  function _log(){
        Tieba_Stlog::addNode('ispv', 0);
    }
}
