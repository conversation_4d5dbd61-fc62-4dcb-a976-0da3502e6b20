<?php
/**
 * User: ding<PERSON>hu
 * Date: 2015-09-28
 * 会员中心做任务接口
 */
class proposeAction extends Mo_Core_Action{
    
    /**
     * @brief:
     * @param:null
     * @return:null
     **/
    protected function _execute() {
		Mo_Response::$bolAjaxCommit = true; //标记返回值类型为json
		Mo_Response::$intErrno = 0;
		Mo_Response::$strError = 'success';
		Mo_Response::$arrErrorData = array();
        
        try {
			$this->user = Mo_Service_Wap::getTplUser();
            $this->wreq = Mo_Service_Wap::getTplWreq();
            
            if ($this->user['is_login'] !== true ){
                //未登陆
                throw new Exception("user need login!",Tieba_Errcode::ERR_USER_NOT_LOGIN);
			}
            
            //参数获取
            $propose_to_user_name	= Mo_request::get('propose_to_user_name', 1);
            $aoth= trim(Mo_request::get('oath', 1));

            $user_id = intval($this->user['id']);
			$user_name	= $this->user_id['name'];

            //tbs验证
            $tbs = strval(Mo_request::get('tbs'));
            if( empty($tbs) ){
                throw new Exception("tbs is empty", Tieba_Errcode::ERR_PARAM_ERROR);
            }
            if( !Tieba_Tbs::check($tbs, true) ){
                //tbs验证失败
                throw new Exception("tbs check fail!", Tieba_Errcode::ERR_NVOTE_INVALID_TBS);
            }

			//get uid
			$arrParam	= array(
				'user_name'	=> array(
					$propose_to_user_name,
				),
			);
			$arrRet	= Tieba_Service::call('user','getUidByUnames',$arrParam,null,null,'post','php','utf-8');

			if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
				Bingo_Log::warning("call getUidByUnames failed.input:[".serialize($arrParam)."].output:[".serialize($arrRet)."].");
				throw new Exception('get no name',$arrRet['errno']);
			}
			$propose_to_user_id	= intval($arrRet['output']['uids'][0]['user_id']);
            
			$arrParam	= array(
				'user_nickname'	=> array(
					$propose_to_user_name,
				),
			);
			$arrRet	= Tieba_Service::call('user','getUidByUserNicknames',$arrParam,null,null,'post','php','utf-8');
			if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
				Bingo_Log::warning("call getUidByUserNicknames failed.input:[".serialize($arrParam)."].output:[".serialize($arrRet)."].");
			}
			if(intval($arrRet['output'][0]['user_id'])> 0){
				$propose_to_user_id	= intval($arrRet['output'][0]['user_id']);
			}


            if( $user_id <= 0  || $propose_to_user_id <= 0){
                throw new Exception('您输入的心仪对象ID出错，请输入正确的贴吧ID', Tieba_Errcode::ERR_PARAM_ERROR);
            }


			$arrParam	= array(
				'user_id'	=> $user_id,
				'user_name'	=> $user_name,
				'propose_to_user_id'	=> $propose_to_user_id,
				'propose_to_user_name'	=> $propose_to_user_name,
				'aoth'	=> trim($aoth),
			);

			$arrRet = Tieba_Service::call('marriage','newProposeMarriage',$arrParam,null,null,'post','php','utf-8');
			if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
				Bingo_Log::warning("call newProposeMarriage failed.input:[".serialize($arrParam)."].output:[".serialize($arrRet)."].");
				throw new Exception($arrRet['errmsg'],$arrRet['errno']);
			}
			$data	= $arrRet['data'];
            
			Mo_Response::$intErrno = intval($arrRet['errno']);
			Mo_Response::$strError = $arrRet['errmsg'];
            Mo_Response::$arrErrorData = $data;
        }catch(Exception $e){
            Bingo_Log::warning( "no=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            //$this->_jsonRet($e->getCode(), Tieba_Error::getUserMsg($e->getCode()));
			switch($e->getCode()){
				case 2670002:
				Mo_Response::$intErrno = $e->getCode();
				Mo_Response::$strError ="你心仪的TA已经结婚了，祝你早日找到属于自己的另一半！";
				break;
				case 2380009:
				Mo_Response::$intErrno = $e->getCode();
				Mo_Response::$strError ="只有成为妹子团成员才能在贴吧结婚哦，快点加入吧！";
				break;
				default:
				Mo_Response::$intErrno = $e->getCode();
				Mo_Response::$strError =$e->getMessage();
				break;
			}
        }

        return true;
    }
	
    /**
     * @biref:
     * @param:null
     * @reutrn:null
     **/
    protected  function _log(){
        Tieba_Stlog::addNode('ispv', 0);
    }
}
