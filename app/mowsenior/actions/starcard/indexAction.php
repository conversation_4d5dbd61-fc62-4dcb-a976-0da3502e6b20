<?php
/**
 * 娱乐人物组卡聚合页 https ui 接口
 * User: ch<PERSON><PERSON>qiang
 * Date: 20210423
 */
class indexAction extends Mo_Core_Action {
    /**
     * @param
     * @return
     */
    protected function _execute() {
        //参数获取
        Mo_Response::$bolAjaxCommit = true;
        $word = Mo_Request::get('wd', null);
        $pn = Mo_Request::get('pn', 1);

        $arrInput = array(
            'word' => $word,
            'pn' => $pn,
        );

        // $arrOut = Tieba_Service::call('push', 'starcardAggregate', $arrInput, null, null, 'post', 'php', 'utf-8');
        // $arrOut = Tieba_Service::call('push', 'siteSearch', $arrInput, null, null, 'post', 'php', 'gbk');
        $arrOut = Tieba_Service::call('push', 'starcardAggregate', $arrInput, null, null, 'get', 'php', 'gbk');
        // $arrOut = Tieba_Service::call('push', 'siteSearch', $arrInput, null, null, 'get', null, null);

        Mo_Response::$intErrno = 0;
        Mo_Response::$strError = 'success';
        Mo_Response::$arrErrorData = $arrOut;
        return;


    }
    /**
     * [wiseindexdaoliu description]
     * @return [type] [description]
     */
    public function wiseindexdaoliu()
    {
        //wise导流位优化
        $baiduid = Mo_Request::getCookie('BAIDUID');
        //var_dump($baiduid);
        $subbid = base_convert(substr($baiduid,0,8),16,10);
        $subbid = intval(substr($subbid,0,10));//将baiduid里的数字抽取出做小流量用
        $useragent = Bingo_Http_Request::getServer('HTTP_USER_AGENT', '');//取UA判断设备是安卓
        $referer = Mo_Request::get('referrer', '');//referrer从前端得到，并传过来
        //初始化redis
        $redis = new Bingo_Cache_Redis('twlive');
        if (null == $redis){
            Bingo_Log::warning("call redis failed!!");
            return false;
        }
        //IP判断
        $ipno = false;
        $uip = Mo_Request::$strIp;
        $uiptmp = explode(".", $uip);
        if($uiptmp[0] == 10 || ($uiptmp[0] == 192 && $uiptmp[1] == 168) || ($uiptmp[0] == 172 && $uiptmp[1] >= 16 && $uiptmp[1] <= 31))
        {
            //先判断是否内网IP
            //Bingo_Log::warning("uip=".$uip." in Intranet.");
            $ipno = true;
        }
        else
        {
            //如果不是内网IP再查ipcity redis
            $rinput = array(
                'key' => "ipcity".$uiptmp[0].".".$uiptmp[1],
            );
            $redis_ret = self::redis_recall($redis,'GET',$rinput);
            if($redis_ret['ret'][$rinput['key']] != null)
            {
                $ipinterval = unserialize($redis_ret['ret'][$rinput['key']]);
                foreach($ipinterval as $uk => $uv)
                {
                    $ut = explode("|",$uv);
                    if(ip2long($uip)>=$ut[0] && ip2long($uip)<=$ut[1])
                    {
                        //Bingo_Log::warning("uip=".$uip." in bsgs.".$uv);
                        $ipno = true;
                    }
                }
            }
            else
            {
                //非北上广深判断不来自手百才行，来自手百也不做导流
                if(strpos($useragent, "baiduboxapp") != false)
                {   
                    $ipno = true;
                }
            }
        }
        //判断IP、判断referer、判断设备
        if(!$ipno && $referer=='n' && strpos($useragent, "Android") != false)
        {
            //24小时只展示一次
            $rinput24 = array(
                'key' => "wisedaoliu".$baiduid,
            );
            $redis_ret24 = self::redis_recall($redis,'GET',$rinput24);
            $last_show_time = $redis_ret24['ret'][$rinput24['key']];
            if(time()-$last_show_time>86400)
            {
                //命中小流量
                if($subbid % 10 == 1 )
                {
                    //var_dump($subbid);
                    $returnwiseindexdaoliu = true;
                    $rinput24 = array(
                        'key' => "wisedaoliu".$baiduid,
                        'value'=>time(),
                    );
                    $redis_ret24 = self::redis_recall($redis,'SET',$rinput24);
                    //var_dump($redis_ret24);
                    
                    //打点stlog
                    Tieba_Stlog::addNode('wiseindexdaoliuid', $baiduid);
                }
            }
        }
        return $returnwiseindexdaoliu;
    }

    /**
     * @param
     * @return
     */
    protected function _log() {
        Tieba_Stlog::addNode('starcard', 1);
    }
}
