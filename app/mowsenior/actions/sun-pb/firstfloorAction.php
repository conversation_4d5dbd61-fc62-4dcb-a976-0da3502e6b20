<?php
/**
 * User: hubin13
 * Date: 2018/10/29
 * Desc: 获得首楼数据
 */
define( 'SUN_PB_PATH', ROOT_PATH.'/app/mowsenior/actions/sun-pb' );
define( 'SUN_PB_CONF_PATH', SUN_PB_PATH.'/conf' );
define( 'SUN_PB_DATA_PATH', SUN_PB_PATH.'/data' );

class firstfloorAction extends Mo_Core_Action {
    const CONF_NAME_PB = "sun-pb.conf";
    const NORMAL_PB  = 'normal';//直播贴切换参数，直播模式为live，普通模式为normal

    private static $_objCoreData;//核心数据对象
    private static $_objExtData;//扩展数据对象
    
    private $bolVideoThread = false;
    
    /**
     * @param
     * @return
     */
    protected function _execute() {
        set_include_path( get_include_path() . PATH_SEPARATOR . SUN_PB_PATH );
        
        Bingo_Timer::start( 'core_base' );
        //获取参数数据
        $arrRequestData = Core_Request::execute();
        // 2017-3-22 lisa 来源于pbshare 的ios9+ 无app的用户进入 智能版pb页 跳转到 应用宝或者下载中间页
        if ($arrRequestData['query']['strFrom'] == 'wxOpenApp') {
            $downPage = 'http://a.app.qq.com/o/simple.jsp?pkgname=com.baidu.tie'.'ba';
            Bingo_Http_Response::redirect($downPage ,true);
        }else if (strpos($arrRequestData['query']['strFrom'], 'OpenApp')) {
            $downPage = 'https://tieba.baidu.com/mo/q/activityDiversion/download?fr='.$arrRequestData['query']['strFrom'];
            Bingo_Http_Response::redirect($downPage ,true);
        }

        // 分享页直接返回schema,@liyong18
        $open_app_schema = Bingo_Http_Request::getNoXssSafe('_open_app_schema', '');
        if (!empty($open_app_schema) && $this->isRightSchema($open_app_schema)) {
            Bingo_Http_Response::redirect($open_app_schema, true);
            return true;
        }

        //加载配置文件
        $objConf = new Util_Conf( SUN_PB_CONF_PATH, self::CONF_NAME_PB );
        //获取配置信息，主要是一些功能的开关或状态
        $arrOnoff = Core_Strategy::getOnoff( $objConf );
        //获取贴子数据
        //判断来自大搜，然后再读玉伟的服务判断是否百晓生视频帖
        $frombxs = Mo_Request::get( 'frombxs', 0 );
        if($frombxs==1)
        {
            $arrInput_bxs['thread_id'] = $arrRequestData['query']['intThreadId'];
            $arrRes_bxs = Tieba_Service::call( 'aggregate', 'getBxsVideoThreadGanyu', $arrInput_bxs, null, null, 'post', 'php', 'utf-8' );
            if($arrRes_bxs['errno'] != 0){
                Bingo_Log::warning('call service aggregate::getBxsVideoThreadGanyu fail, input:['.serialize($arrInput_bxs).'],output:['.serialize($arrRes_bxs).']');
            }
            elseif($arrRes_bxs['data'][$arrInput_bxs['thread_id']]['is_exist'] == 1)//if(1)
            { 
                $arrRequestData['intReverse']=1;
                Mo_Response::addViewData( 'is_bxspb', 1);
            }
        } 
        $arrThread = Core_Thread::getPbList( $arrRequestData, $arrOnoff );
        //Mo_Response::addViewData($strKey, $mixVar)
        //组建原始基础数据，通过这些数据获取人吧贴信息
        $arrBaseData = array(
            'request' =>  $arrRequestData,
            'thread' =>  $arrThread,
            'onoff'  =>  $arrOnoff,
        );
        //以debug开头的变量都是用来调试的，非调试模式下是不会输出到模板的
        Mo_Response::addViewData( 'debug_arrBaseData', $arrBaseData );
        Bingo_Timer::end( 'core_base' );

        //获取人吧贴核心数据，并对核心数据进行处理
        Bingo_Timer::start( 'core_main' );
        $arrCoreData = Core_Main::execute( $arrBaseData );
        $objCoreData = Util_CoreData::getInstance();
        if($arrRequestData['intReverse'] == 1)
        {
            //如果是百晓生PB页就要干预播放数
            //$arrRes_bxs['data'][$arrInput_bxs['thread_id']]['video_view_num'] = 222;
            //$arrRes_bxs['data'][$arrInput_bxs['thread_id']]['video_agree_num'] = 111;
            $arrCoreData['thread']['thread']['video_play_count'] += $arrRes_bxs['data'][$arrInput_bxs['thread_id']]['video_view_num'];
        }
        $objCoreData->assign( $arrCoreData );
        self::$_objCoreData = $objCoreData;
        Bingo_Timer::end( 'core_main' );
        
        //去重无用扩展 
        Ext_Portal::setSmallExtension('Base_UserHotVedio', 0);
        Ext_Portal::setSmallExtension('Base_UserVedio', 0);
        
        $arrExtData = Core_Extension::execute( $objCoreData );
        $objExtData = Util_ExtData::getInstance();
        $objExtData->assign( $arrExtData );
        self::$_objExtData = $objExtData;
        // Bingo_Log::warning("ext:".var_export($arrExtData,1));
        Bingo_Timer::end( 'core_extension' );
        
        //对核心数据、扩展数据进行整合处理得到模板想要的数据
        Bingo_Timer::start( 'core_integration' );
        Core_Integration::execute( $objCoreData, $objExtData );
        Bingo_Timer::end( 'core_integration' );
        
        //处理数据异常，如非法词吧，不存在的吧等
        self::_handlerException();
        
        //变量输出到模板前做最后的处理，这里是数据处理的最后一层
        Bingo_Timer::start( "core_view" );
        Core_View::execute( $objCoreData, $objExtData );
        Bingo_Timer::end( "core_view" );
        
        //只取首楼数据
        $arrData = Mo_Response::getViewData();
        if(!empty($arrData['post_list'])&&is_array($arrData['post_list'])){
        	Mo_Response::addViewData("post_list", array_slice($arrData["post_list"], 0,1));
        }
        Mo_Response::addViewData("page", array());
        Mo_Response::addViewData("ad_floor", array());
        Mo_Response::addViewData("prison_data", array());
        
        
    }

    /**
     * [isRightSchema description]
     * @param  [type]  $url [description]
     * @return boolean      [description]
     */
    protected function isRightSchema($url) {
        $rSchema = '/^(?:tbfrs|tbpb|tbmaintab|tbphotolive|com\.baidu\.tieba):\/\/(?:jumptoforum|tieba\.baidu\.com|unidispatch)/';
        return preg_match($rSchema, $url);

    }

    /**
     * @param  
     * @return 
     */
    private function _handlerException() {
        $arrThread = self::$_objCoreData->getData( 'thread' );
        $arrPostList = self::$_objCoreData->getData( 'post_list' );
        if ( empty( $arrThread ) || empty( $arrPostList ) ) {
            Bingo_Log::trace( 'wap pb thread not exist' );
            Mo_Response::$intErrno = Mo_Errno::WAP_PB_THREAD_NOT_EXIST;
            Mo_Response::$strError = Mo_Error::WAP_PB_THREAD_NOT_EXIST;
            return false;
        }
    }

    /**
     * [_pbSmallFlow ]
     * @param  [type]  $url [description]
     * @return boolean      [description]
     */
    private function _pbSmallFlow() {
        $pbWhitelist = array(
            '8E6D8E6AE96A193144FF3FD8A30F05EB:FG=1',
            'AC1E316EFBDD4E00A3F6CD013781568B:FG=1',
            '214E60CBDDF90A442874512DE4D0E9B4:FG=1',
            'A94BD2851041AAE2AD528BAA440AF440:FG=1',
            '45569364F9B75BB0F589369B4DA21531:FG=1',
            '94D2A65B9A178E0554E76E763FBA3E25:FG=1',
            '6F4CD9385B1A62D4B458A8B3325B004B:FG=1',
            'E8969524EBE5ECD8286AE12957333C82:FG=1',
            'C1B1A05F41C75FC9ACB44D4C35FC3BEB:FG=1',
            '0AF7BE13C00AED3F194DD895AB7C34E7:FG=1',
            'A1E77856C1D8041939A2B7459FF24BED:FG=1',
        );
        $isNewPb = 0;
        $baiduid = Mo_Request::getCookie('BAIDUID');
        if ($baiduid !== '') {
            $firstOne = substr($baiduid, 0, 1);
            if ($firstOne=='0' || $firstOne=='1' || in_array($baiduid, $pbWhitelist)) {
                $isNewPb = 1;
            }
        }
        Mo_Response::addViewData( 'is_newpb', $isNewPb);
        return $isNewPb;
    }

    protected function _log() {
        $arrForum = self::$_objCoreData->getData( 'forum' );
        $arrThread = self::$_objCoreData->getData( 'thread' );
        $arrRequest = self::$_objCoreData->getData( 'request' );
        $arrQuery = $arrRequest['query'];
        $arrPager = $arrRequest['pager'];
        //智能版pb中增加大搜回流监控
        Bingo_Log::warning("sub-pb referer = ".$_SERVER['HTTP_REFERER']);
        $referer = $_SERVER['HTTP_REFERER'];
        if(strpos($referer, '.baidu.com')!==false)//m.baidu.com
        {
            //增加导流死链日志
            if($arrThread['id']==0)
            {
                Tieba_Stlog::addNode('baiduseo-wappb-badurl', 1);
            }
        }
                
        $isNewPb = $this->_pbSmallFlow();

        Mo_Log::addNode( 'req_pb', 'tid', $arrQuery['intThreadId'] );
        Mo_Log::addNode( 'req_pb', 'pn', $arrPager['intOffset'] );
        Mo_Log::addNode( 'req_pb', 'rn', $arrPager['intRn'] );
        Mo_Log::addNode( 'req_pb', 'sc', $arrQuery['intPostId'] );
        Mo_Log::addNode( 'req_pb', 'lm', Mo_Request::get('lm', 0) );
        Mo_Log::addNode( 'req_pb', 'r', $arrPager['intReverse'] );
        Mo_Log::addNode( 'req_pb', 'pnum', $arrPager['intPNum'] );
        Mo_Log::addNode( 'req_pb', 'tnum', $arrPager['intPNum'] );
        Mo_Log::addNode( 'req_pb', 'last', Mo_Request::get('last',0) );
        Mo_Log::addNode( 'req_pb', 'see_lz', $arrQuery['intSeeLz'] );
        Mo_Log::addNode( 'req_pb', 'has_recommend', 0 );
        Mo_Log::addNode( 'res_pb', 'err', Mo_Response::$intErrno );
        Mo_Log::addNode( 'res_pb', 'fn', $arrForum['name'] );
        Mo_Log::addNode( 'res_pb', 'ispv', 1 );
        Mo_Log::addNode( 'res_pb', 'sunpb', 1 );
        Mo_Log::addNode( 'res_pb', 'isiphnew', 1 );
        Mo_Log::addNode( 'req_pb', 'abstract', Mo_Request::getCookie( 'abstract_mode', 0 ) );
        Mo_Log::addNode( 'req_pb', '_isLivePost', $arrThread['is_livepost'] );
        Mo_Log::addNode( 'req_pb', 'live_post_type', $arrThread['live_post_type'] );
        Mo_Log::addNode( 'res_pb', 'is_newpb', $isNewPb);
        Mo_Log::addNode( 'req', 'saveflow', Mo_Request::getCookie( 'IS_SAVE_FLOW', 0 ) );

    }
    
}

?>
