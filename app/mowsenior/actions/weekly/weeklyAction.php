<?php
/**
 * 大神&吧主周报
 */
class weeklyAction extends Mo_Core_Action{
    protected $strTplName = '';
    protected $strTplPath = 'tbwisebawu';
    const BAZHU_TPL_NAME = 'bazhuweekly.php';
    const GOD_TPL_NAME = 'masterweeklyreport.php';
    const BAZHU_BUSSINESS = 'bazhu';
    const GOD_BUSSINESS = 'god';

    private $base = null;
    private $user = null;
    private $wreq = null;
    private $strBussiness = '';
    private $intUserId = 0;//用户自己id
    private $strShareUname = '';
    private $intShareUid = 0;
    private $intFid = 0;
    private $intWeeklyTime = 0;//周报日期

    private $arrCoreData = array();

    private function _input() {
        $this->user = Mo_Service_Wap::getTplUser();
        $this->base = Mo_Service_Wap::getTplBase();
        $this->wreq = Mo_Service_Wap::getTplWreq();
        $this->strBussiness = Mo_Request::get('business','');
        $this->intUserId = intval($this->user['uid']);
        $this->intFid = Mo_Request::get('fid',0);
        $this->intWeeklyTime = Mo_Request::get('weekly_time',0);
        $strPortrait = Mo_Request::get('portrait','');
        $this->intShareUid = Tieba_Ucrypt::decode($strPortrait);
        return true;
    }
    /**
     * 
     */
    protected  function _execute(){
        $this->_input();
        if (false == $this->_process()){
            $this->_ret(Tieba_Errcode::ERR_FORUM_GET_DELETEINFO);
        }
        $this->_ret(Tieba_Errcode::ERR_SUCCESS,$this->arrCoreData);
        return true;
    }

    /**
     * 吧主周报
     */
    private function _bazhuProcess(){
        $this->strTplName = self::BAZHU_TPL_NAME;
        //获取周报信息
        $arrWeeklyInput = array(
            'monday_time' => $this->intWeeklyTime,
            'fid' => $this->intFid,
        );
        $arrWeeklyOutput = Tieba_Service::call('bawu','getWeeklyInfo',$arrWeeklyInput,null,null,'post',null,'gbk');
        if (false == $arrWeeklyOutput || Tieba_Errcode::ERR_SUCCESS != $arrWeeklyOutput['errno'] || empty($arrWeeklyOutput)){
            Bingo_Log::warning('call getWeeklyInfo failed!! input ['.serialize($arrWeeklyInput).'] output ['.serialize($arrWeeklyOutput).']');
            return false;
        }
        $arrWeekData = $arrWeeklyOutput['data'];
        if (!is_array($arrWeekData) || $arrWeekData['forum_id'] != $this->intFid){
            Bingo_Log::warning('call getWeeklyInfo,get error data input ['.serialize($arrWeeklyInput).'] output ['.serialize($arrWeeklyOutput).']');
            return false;
        }
        //获取吧数据
        $arrForumInput = array(
            'forum_id' => array($this->intFid),
        );
        $arrForumOutput = Tieba_Service::call('forum','mgetBtxInfoEx',$arrForumInput,null,null,'post',null,'utf-8');
        if (false == $arrForumOutput || Tieba_Errcode::ERR_SUCCESS != $arrForumOutput['errno']){
            Bingo_Log::warning('call mgetBtxInfoEx failed!! input ['.serialize($arrForumInput).'] output ['.serialize($arrForumOutput).']');
            return false;
        }
        $arrForumInfo = $arrForumOutput['output'][$this->intFid];
        //组建数据
        $this->arrCoreData['forum_id'] = $arrWeekData['forum_id'];//吧id
        $this->arrCoreData['forum_name'] = $arrWeekData['forum_name'];//吧名
        $this->arrCoreData['forum_head_portrait'] = $arrForumInfo['card']['avatar'];//吧头像
        $this->arrCoreData['field_name'] = $arrWeekData['field'];//领域名
        $this->arrCoreData['field_rank'] = $arrWeekData['rank'];//领域排名
        $this->arrCoreData['pv_field_proportion'] = $arrWeekData['lead_per'];//pv领域占比
        $this->arrCoreData['new_follow_cnt'] = $arrWeekData['atte_cnt'];//新增关注数
        $this->arrCoreData['new_pv'] = $arrWeekData['pv'];//新增访问量
        $this->arrCoreData['new_thread_cnt'] = $arrWeekData['thread_cnt'];//新增发帖
        $this->arrCoreData['day_signin_rate'] = $arrWeekData['day_signin_rate'];//日均签到率
        $this->arrCoreData['per_capita_time'] = $arrWeekData['ave_time'];//人均时长
        $this->arrCoreData['content_quality_rate'] = $arrWeekData['thread_good'];//内容优质率
        $this->arrCoreData['forum_follows'] = $arrForumInfo['statistics']['member_count'];//吧关注数
        return true;
    }

    /**
     * 大神周报
     */
    private function _godProcess(){
        //获取周报信息
        $arrWeeklyInput = array(
            'monday_time' => $this->intWeeklyTime,
            'user_id' => $this->intShareUid,
        );
        $arrWeeklyOutput = Tieba_Service::call('god','getGodWeeklyByUid',$arrWeeklyInput,null,null,'post',null,'utf-8');
        if (false == $arrWeeklyOutput || Tieba_Errcode::ERR_SUCCESS != $arrWeeklyOutput['errno'] || empty($arrWeeklyOutput)){
            Bingo_Log::warning('call getGodWeeklyByUid failed!! input ['.serialize($arrWeeklyInput).'] output ['.serialize($arrWeeklyOutput).']');
            return false;
        }
        $arrWeekData = $arrWeeklyOutput['data'];
        if (!is_array($arrWeekData) || $arrWeekData['user_id'] != $this->intShareUid){
            Bingo_Log::warning('call getGodWeeklyInfo,get error data input ['.serialize($arrWeeklyInput).'] output ['.serialize($arrWeeklyOutput).']');
            return false;
        }
        //获取大神属性
        $arrUserInput = array(
            'user_id' => $this->intShareUid,
            'get_icon' => 1,
        );
        $arrUserOutput = Tieba_Service::call('user','getUserData',$arrUserInput,null,null,'post',null,'utf-8');
        if (false == $arrUserOutput || Tieba_Errcode::ERR_SUCCESS != $arrUserOutput['errno']){
            Bingo_Log::warning('call getUserData failed!! input ['.serialize($arrUserInput).'] output ['.serialize($arrUserOutput).']');
            return false;
        }
        $arrUserInfo = $arrUserOutput['user_info'][0];
        $arrGodInfo = $arrUserInfo['god_info'];
        if (empty($arrGodInfo['field_name'])){
            Bingo_Log::warning('call getUserData,get error data input ['.serialize($arrUserInput).'] output ['.serialize($arrUserOutput).']');
            return false;
        }
        $this->arrCoreData['uname'] = $arrUserInfo['display_name'];//用户名
        $this->arrCoreData['user_head_portrait'] =  sprintf("http://tb.himg.baidu.com/sys/portrait/item/%s.jpg",Tieba_Ucrypt::encode($this->intShareUid));//头像连接
        $this->arrCoreData['portrait'] = Tieba_Ucrypt::encode($this->intShareUid);//
        $this->arrCoreData['field_name'] = $arrGodInfo['field_name'];//领域名
        $this->arrCoreData['type_name'] = empty($arrGodInfo['type_name']) ? '通用' : $arrGodInfo['type_name'];//大神类型
        $this->arrCoreData['new_thread_cnt'] = $arrWeekData['content']['thread_cnt'];//发布内容量
        $this->arrCoreData['recommend_cnt'] = $arrWeekData['content']['ruku_cnt'];//推荐入库数
        $this->arrCoreData['new_pv'] = $arrWeekData['content']['new_pv'];//新增浏览
        $this->arrCoreData['new_zan_cnt'] = $arrWeekData['content']['lc_n_zan_new_cnt'];//新增点赞
        $this->arrCoreData['new_post_cnt'] = $arrWeekData['content']['post_new_cnt'];//新增评论
        $this->arrCoreData['new_fans_cnt'] = $arrWeekData['content']['fans_new_num'];//新增粉丝量

        return true;
    }

    private function _process(){
        $bolSucess = false;
        switch ($this->strBussiness){
            case self::GOD_BUSSINESS:
                $bolSucess = $this->_godProcess();
                break;
            case self::BAZHU_BUSSINESS:
                $bolSucess = $this->_bazhuProcess();
                break;
            default:
                break;
        }
        return $bolSucess;
    }
    /**
     * 
     */
    protected  function _build(){
        Mo_Response::addViewData('wreq',$this->wreq);
        Mo_Response::addViewData('base',$this->base);
        Mo_Response::addViewData('user',$this->user);

        foreach($this->arrCoreData as $keyName => $keyValue){
             Mo_Response::addViewData($keyName, $keyValue);
        }
    }

    private function _ret($errno, $data = array()) {
        Mo_Response::$bolAjaxCommit = true; //标记返回值类型为json
        Mo_Response::$intErrno = $errno;
        Mo_Response::$strError = Tieba_Error::getErrmsg($errno);
        Mo_Response::$arrErrorData = $data;
        Mo_Response::addViewData('base', $this->base);
        Mo_Response::addViewData('user', $this->user);
        Mo_Response::addViewData('wreq', $this->wreq);
    }

    public function _log(){

    }
}