<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2019-04-15
 * @describe 裂变红包实时检索
 */
class staticdatapageAction extends Mo_Core_Action {
	
    protected $strTplName = 'selectData.php';
    protected $strTplPath = 'rainmoney';

    /**
     * 执行入口
     * @param
     * @return
     */
    protected function _execute() {
        if($this->_input()){
            $this->_process();
        }
        $this->_build();        
    }

    /**
     * 输入
     * @param
     * @return
     */
    private function _input(){
        return true;
    }
    
    /**
     * 执行
     * @param
     * @return
     */
    private function _process(){
        return true;
    }
    
    /**
     * 返回
     * @param
     * @return
     */
    private function _build() {
	}
	/**
	 * 日志
	 * @param
	 * @return
	 */
    protected function _log() {
        Tieba_Stlog::addNode('ispv',1);
    }   
}
