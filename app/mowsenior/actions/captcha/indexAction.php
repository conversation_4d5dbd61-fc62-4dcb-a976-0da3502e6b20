<?php
/**
 * 新版验证码
 * <AUTHOR>
 */
class indexAction extends Mo_Core_Action{
    protected $strTplName = 'captcha.php';
    protected $strTplPath = 'captcha';

    protected  function _execute() {
        if ($this->_input()){
            $this->_process();
            $this->_build();
        }
    }
    private function _input() {
        return true;
    }
    private function _process() {

    }

    private function _build() {
        Mo_Response::addViewData('base', Mo_Service_Wap::getTplBase());
        Mo_Response::addViewData('wreq', Mo_Service_Wap::getTplWreq());
    }
    protected function _log() {

    }
}

?>
