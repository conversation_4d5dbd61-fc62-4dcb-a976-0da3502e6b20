<?php
/**
 * 吧回收站h5
 * User: dongtiancheng
 * Date: 2015/10/15
 * Time: 17:11
 */

class dismissSysAction extends Mo_Core_Action {

    protected $strTplName = 'dismiss_sys.php';
    // 模板路径
    protected $strTplPath = 'smanage';

    private $wreq = array();

    const SERVER_URL = 'http://tieba.baidu.com';

    const PASS_URL = 'http://wappass.baidu.com';

    /**
     * 获取数据
     *
     * @param $url
     * @return bool
     */
    public function curlData($url){
        $options = array(
            'timeout' =>30000,
            'conn_timeout' =>10000,
            'max_response_size'=> 1024000,
        );
        $httpproxy = Orp_FetchUrl::getInstance($options);
        Bingo_Timer::start(__FUNCTION__);
        $result = $httpproxy->get($url);
        var_dump($result);
        Bingo_Timer::end(__FUNCTION__);
        $err      = $httpproxy->errmsg();
        $httpCode = $httpproxy -> http_code();
        if (!empty($err) || $httpCode !== 200) {
            Bingo_log::warning(__FUNCTION__ . " call baidu failed, url = $url, http_code = $httpCode, output = ". serialize($err));
            return false;
        }
        $mentions = Bingo_String::json2array($result);
        $mentions = Bingo_Encode::convert($mentions, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        return $mentions;
    }


    protected function _execute(){
        $user = Mo_Service_Wap::getTplUser();
        $this->wreq = Mo_Service_Wap::getTplWreq();
        $this->wreq['tbs'] = Tieba_Tbs::gene(Mo_Request::$bolLogin);

        if (!$user['is_login']) {
            Bingo_Log::warning ( __CLASS__ . ':The user do not login.' );
            Bingo_Http_Response::redirect(self::PASS_URL . '/passport/?login');
        }

        $postId		= intval(Mo_request::get('pid'));
        $threadId	= intval(Mo_request::get("tid"));

        $input = array(
            'pid' => $postId,
            'tid' => $threadId,
            'uid' => $user['id'],
        );

        $res = Tieba_Service::call('pmc', 'checkSystemManual', $input, null, null,'post', 'php', 'utf-8');

        if (isset($res['errno']) && $res['errno'] == 0) {
            Mo_Response::addViewData('data', $res);
            Mo_Response::addViewData('wreq', $this->wreq);
            Mo_Response::addViewData('user', $user);
        }
        else {
            Bingo_Http_Response::redirect('http://tieba.baidu.com/mo/q/pmc');
        }
    }

    protected function _log(){
    }
}
