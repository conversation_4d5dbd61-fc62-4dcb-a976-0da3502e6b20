<?php
/**
 * 吧回收站h5
 * User: dongtiancheng
 * Date: 2015/10/15
 * Time: 17:11
 */

class dismissBawuAction extends Mo_Core_Action {

    protected $strTplName = 'dismiss_bawu.php';
    // 模板路径
    protected $strTplPath = 'smanage';

    private $wreq = array();

    const SERVER_URL = 'http://tieba.baidu.com';

    const PASS_URL = 'http://wappass.baidu.com';

    protected function _execute(){
        $user = Mo_Service_Wap::getTplUser();
        $this->wreq = Mo_Service_Wap::getTplWreq();
        $this->wreq['tbs'] = Tieba_Tbs::gene(Mo_Request::$bolLogin);

        if (!$user['is_login']) {
            Bingo_Log::warning ( __CLASS__ . ':The user do not login.' );
            Bingo_Http_Response::redirect(self::PASS_URL . '/passport/?login');
        }

        $postId		= intval(Mo_request::get('pid'));
        $threadId	= intval(Mo_request::get("tid"));
        $client_version = Mo_Request::get('_client_version');

        $input = array(
            'pid' => $postId,
            'tid' => $threadId,
            'uid' => $user['id'],
        );

        $res = Tieba_Service::call('pmc', 'checkBawuManual', $input, null, null, 'post', 'php', 'utf-8');

        if (isset($res['errno']) && $res['errno'] == 0) {
            Mo_Response::addViewData('data', $res);
            Mo_Response::addViewData('wreq', $this->wreq);
            Mo_Response::addViewData('user', $user);
        }
        else {
            Bingo_Http_Response::redirect(self::SERVER_URL . '/mo/q/pmc?status=1&reason=BawuServiceCheckError' . (isset($client_version) ? ('&_client_version=' . $client_version) : '') . '#bawu');
        }
    }

    protected function _log(){
    }
}
