<?php
/**
 * created by xiaofeng.
 * email: <EMAIL>
 * file name: Unicom.php
 * create time: 2020-09-16 15:23:18
 * describe:
 */

class Util_Unicom_Unicom {
    public $timeout = 2000; //超时时间,单位毫秒

    /**
     * 请求联通联通
     * @param string $params
     * @param string $url
     * @param int $timeout
     * @param string $callFrom
     * @return bool
     */
    public function callUnicom($params, $url, $timeout = 500, $callFrom = '') {
        $headers = array('Content-Type: application/json');
        $options = array(
            'timeout'           => $timeout,    // 单位：毫秒
            'conn_timeout'      => $timeout,    // 单位：毫秒
            'max_response_size' => 4194304000,
        );

        $httpproxy = Orp_FetchUrl::getInstance($options);
        Bingo_Timer::start('call_unicom_'.$callFrom);
        $ret = $httpproxy->post($url, $params, $headers, array());
        Bingo_Timer::end('call_unicom_'.$callFrom);
        $errno = $httpproxy->errno();
        $errmsg = $httpproxy->errmsg();

        if( $errno !== 0 || $ret == false ) {
            Bingo_Log::warning('call unicom service '. $url .' fail:'.json_encode($ret). ' errmsg:'.$errmsg);
            return false;
        }
        $data = Bingo_String::json2array($ret, Bingo_Encode::ENCODE_UTF8);

        return $data;
    }

    /**
     * 构造联通请求参数
     * @param $arrReq 业务基本参数
     * @param $systemParams 系统基本参数
     * @param $secretKey
     * @return string
     */
    public function buildUnicomReqParams($arrReq, $systemParams, $secretKey) {

        //业务参数按key升序排序
        ksort($arrReq);

        //系统按key升序排序
        $systemParams['content'] = json_encode($arrReq);
        ksort($systemParams);

        //参数拼接
        $originSign = $secretKey;
        foreach ($systemParams as $key => $value) {
            $originSign .= $key.$value;
        }
        $originSign .= $secretKey;
        $secret = hex2bin($secretKey);
        $sign = strtoupper(bin2hex(hash_hmac('sha256', $originSign, $secret,true)));
        $systemParams['sign'] = $sign;

        $strParam = '';
        foreach ($systemParams as $key => $value) {
            $strParam .= $key . '=' . $value . '&';
        }
        $strParam = rtrim($strParam, '&');

        return $strParam;
    }

}