<?php
/**
 * <AUTHOR>
 * @date 2017-04-07
 */
require_once('./Def.php');
require_once('./Image.php');
abstract class Util_Campus_Base extends Mo_Core_Action{
	protected $_intErrno = Tieba_Errcode::ERR_SUCCESS;
	protected $_intUIType = Util_Campus_Def::UI_TYPE_BROWSE;
	protected $_arrUserInfo = array();
	protected $_arrData = array();
	/**
	 * [_execute description]
	 * @param
	 * @return [type] [description]
	 */
	protected function _execute(){
		$this->_getUserInfo();
		if(Util_Campus_Def::UI_TYPE_COMMIT === $this->_intUIType && !$this->_checkCommit()){
			return $this->_errRet(Tieba_Errcode::ERR_NOT_POST_METHOD);
		}
		if( $this->input() ){
			$this->process();
		}
		$this->_log();
		if(Tieba_Errcode::ERR_SUCCESS === $this->_intErrno){
			return self::_succRet($this->_arrData, $this->_strSuccMsg);
		}
		return self::_errRet($this->_intErrno);
	}
	abstract protected function process();
	abstract protected function input();
	/**
     * @desc 获取用户信息
     * @param
     * @return
     */
    protected function _getUserInfo() {
        if (!empty($this->_arrUserInfo)) {
            return $this->_arrUserInfo;
        }

        $bolLogin    = (int)Tieba_Session_Socket::isLogin();
        $intUserId   = intval(Tieba_Session_Socket::getLoginUid());
        $strUserName = Tieba_Session_Socket::getLoginUname();
        $intUserIp   = intval(Bingo_Http_Ip::ip2long(Bingo_Http_Ip::getConnectIp()));
        $bolNoUname  = (int)Tieba_Session_Socket::getNo_un();
        $strMobile   = strval(Tieba_Session_Socket::getMobilephone());
        $strEmail    = strval(Tieba_Session_Socket::getEmail());
        $portrait    = strval(Tieba_Ucrypt::encode($intUserId, $strUserName));
        $tbs         = strval(Tieba_Tbs::gene($bolLogin));

        $arrUserSInfo   = array(
            'is_login'  => $bolLogin,
            'id'        => $intUserId,
            'uid'       => $intUserId,
            'name'      => Bingo_Encode::convert($strUserName, "UTF-8", "GBK"),
            'user_ip'   => $intUserIp,
            'is_noname' => $bolNoUname,
            'mobile'    => Tieba_Util::maskPhone($strMobile),
            'email'     => Tieba_Util::maskEmail($strEmail),
            'portrait'  => $portrait,
            'tbs'       => $tbs,
        );
        if(!$bolLogin){
        	$this->_arrUserInfo = $arrUserSInfo;
        	return $this->_arrUserInfo;
        }

        $arrParam = array(
            'user_id' => $arrUserSInfo['user_id'],
        );
        $arrRet = Tieba_Service::call('user', 'getUserData', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS === $arrRet['errno']) {
            $strUserName = $arrRet['user_info'][0]['user_name'];
            if (isset($arrRet['user_info'][0]['profession_manager_nick_name']) && !empty($arrRet['user_info'][0]['profession_manager_nick_name'])) {
                $strUserName = $arrRet['user_info'][0]['profession_manager_nick_name'];
            }
            if (isset($arrRet['user_info'][0]['user_nickname']) && !empty($arrRet['user_info'][0]['user_nickname'])) {
                $strUserName = $arrRet['user_info'][0]['user_nickname'];
            }
            $arrUserSInfo['show_name'] = $strUserName;
            $arrUserSInfo['user_ext'] = $arrRet['user_info'];
        }

        $this->_arrUserInfo = $arrUserSInfo;

        return $this->_arrUserInfo;
    }
    /**
     * @desc 获取还能投票几次 for 2017.4
     * @param
     * @return
     */
    protected function _getVote(){
    	$intLeft = 0;
    	$arrUserInfo = $this->_getUserInfo();
    	$intUid = (int)$arrUserInfo['uid'];
    	if(!$arrUserInfo['is_login']|| 0 >= $arrUserInfo['uid']){
        	Bingo_Log::warning("user not login");
        	//$this->_intErrno = Tieba_Errcode::ERR_MO_USER_NOT_LOGIN;
        	return array('left' => $intLeft, );
        }
    	$arrInput = array(
    		'user_id' => $intUid,
    		'vote_act_id' => Util_Campus_Def::ELECT_ACT_ID,
    	);
    	$arrOutput = Tieba_Service::call('elect', 'getUserChance', $arrInput);
    	//Bingo_Log::warning(print_r($arrOutput,1));
    	if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
    		Bingo_Log::warning(sprintf("call elect::getUserChance failed! input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
    	}else{
            $intLeft = (int)$arrOutput['data']['user_chance']['chance_count'];
    		$intAdded = (int)$arrOutput['data']['user_chance']['add_count'];
    		$intAdd = (int)$arrOutput['data']['user_chance']['remain_add_count'];
    	}
    	return array('left' => $intLeft, );
    }
    /**
     * [_getTodayLeft description]
     * @param  [type] $arrChanceRecord [description]
     * @return [type]                  [description]
     */
    protected function _getTodayLeft($arrChanceRecord){
        $intToday = strtotime('today');
        $intLeft = 0;
        foreach($arrChanceRecord as $k => $record){
            if($record['op_time'] >= $intToday){
                $intLeft += (int)$record['chance_count'];
            }
        }
        return $intLeft;
    }
    /**
     * @desc 获取还能抽奖几次 for 2017.4
     * @param
     * @return
     */
    protected function _getLottery(){
    	$intLeft = 0;
    	$intTodayTime = strtotime('today');
    	$arrUserInfo = $this->_getUserInfo();
    	$intUid = (int)$arrUserInfo['uid'];
    	if(!$arrUserInfo['is_login']|| 0 >= $arrUserInfo['uid']){
        	Bingo_Log::warning("user not login");
        	//$this->_intErrno = Tieba_Errcode::ERR_MO_USER_NOT_LOGIN;
        	return array('left' => $intLeft, );
        }
    	$arrInput = array(
    		'user_id' => $intUid,
    		'award_act_id' => Util_Campus_Def::AWARD_ACT_ID,
            'rec_start_time'       => $intTodayTime,
			'chance_expire_time'   => $intTodayTime,
            'chance_default_count' => Util_Campus_Def::AWARD_INIT_CHANCE, 
    	);
    	$arrOutput = Tieba_Service::call('tyche', 'getUserChance', $arrInput);
    	//Bingo_Log::warning(print_r($arrOutput,1));
    	if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
    		Bingo_Log::warning(sprintf("call elect::getUserChance failed! input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
    	}else{
            $intLeft = self::_getTodayLeft($arrOutput['data']['chance_record']);
    		//$intLeft = (int)$arrOutput['data']['chance_count'];
    	}
    	return array('left' => $intLeft, );

    }
    
	/**
     * @desc 提交方法检测
     * @param
     * @return
     */
    protected function _checkCommit() {
    	//Bingo_Log::warning(print_r($_SERVER['REQUEST_METHOD'],1));
        if(!Bingo_Http_Request::isPost()){
            Bingo_Log::warning('method is not post');
            $this->_intErrno = Tieba_Errcode::ERR_NOT_POST_METHOD;
            return false;
        }
        $strTbs = strval(Bingo_Http_Request::get('tbs',''));
        //Bingo_Log::warning($strTbs);
        if (!Tieba_Tbs::check($strTbs, true) ) {
            Bingo_Log::warning('tbs check error');
            $this->_intErrno = Tieba_Errcode::ERR_INVALID_SIGN;
        	return false;    
        }
    	$arrUserInfo = $this->_getUserInfo();
        
        if(!$arrUserInfo['is_login']|| 0 >= $arrUserInfo['uid']){
        	Bingo_Log::warning("user not login");
        	$this->_intErrno = Tieba_Errcode::ERR_MO_USER_NOT_LOGIN;
        	return false;
        }

        return true;
    }
    /**
     * [_errRet description]
     * @param
     * @return [type] [description]
     */
    protected static function _errRet($errno){
        $arrErrMap = array(
            220034 => "您的操作太频繁了啦 >_< ",
        );
        if(isset($arrErrMap[$errno])){
            $arrRet = array(
                'errno'  => $errno,
                'errmsg' => $arrErrMap[$errno],
                'error'  => Tieba_Error::getErrmsg($errno),
                //'msg'   
            );
        }else{
            $arrRet = array(
                'errno'  => $errno,
                'errmsg' => Bingo_Encode::convert(Tieba_Error::getUserMsg($errno), 'UTF-8', 'GBK'),
                'error'  => Tieba_Error::getErrmsg($errno),
                //'msg'   
            );
        }
    	
    	echo Bingo_String::array2json($arrRet);
    	return true;
    }
    /**
     * [_errRet description]
     * @param
     * @return [type] [description]
     */
    protected static function _succRet($data, $msg = null){
    	$arrRet = array(
    		'errno' => Tieba_Errcode::ERR_SUCCESS,
    		'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
    		'data' => $data,
    	);
    	if(isset($msg)){
    		$arrRet['msg'] = $msg;
    	}
    	echo Bingo_String::array2json($arrRet);
    	return true;

    }
        

}