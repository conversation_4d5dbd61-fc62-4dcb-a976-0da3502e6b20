<?php
/**
 * Created by PhpStorm.
 * User: huxiaomei
 * Date: 2019/5/6
 * Time: 下午10:20
 */
class Util_Newbawurule_Data{
    const  Activity_WORD_TABLE = 'tb_wordlist_redis_Bazhu_Activity';
    const  BAZHU_ACTIVITY_BEGIN_TIME = 'bazhu_activity_begin_time';
    const  BAZHU_ACTIVITY_END_TIME = 'bazhu_activity_end_time';
    const  BAZHU_ACTIVITY_JOIN_END_TIME = 'bazhu_activity_join_end_time';
    const  BAZHU_ACTIVITY_OPEN_AWARD_TIME = 'bazhu_activity_open_award_time';
    const  BAZHU_ACTIVITY_PRIFORUM_UV = 'activity_priforum_uv';

    //奖品信息
    const RECORD_STATUS_JOIN = 1;//参与活动
    const RECORD_STATUS_GIVE_AWARD = 2;//已发奖品
    const RECORD_STATUS_NOGIVE_AWARD = 3;
    const RECORD_STATUS_END = 4;//结束状态
    const RECORD_STATUS_OPEN_AWARD = 5;//已开奖

    const AWARD_TYPE_INIT = 0;//初始化状态
    const AWARD_TYPE_REAL = 1;//实物奖
    const AWARD_TYPE_MEMBER = 2;//会员奖
    const AWARD_TYPE_REAL_MEMBER = 3;//会员+实物奖

    /**
     * 获取吧主节活动词表信息
     * @param  [type]  $intFid [description]
     * @return array         [description]
     */
    public static function getActivityWordList()
    {
        $handleWordServer = Wordserver_Wordlist::factory();
        $wordList = array(self::BAZHU_ACTIVITY_BEGIN_TIME,self::BAZHU_ACTIVITY_END_TIME,self::BAZHU_ACTIVITY_JOIN_END_TIME,self::BAZHU_ACTIVITY_OPEN_AWARD_TIME,self::BAZHU_ACTIVITY_PRIFORUM_UV);
        $arrItemInfo      = $handleWordServer->getValueByKeys(array_values($wordList), self::Activity_WORD_TABLE);
        $bazhuActivityBegintime = !empty($arrItemInfo[self::BAZHU_ACTIVITY_BEGIN_TIME]) && intval($arrItemInfo[self::BAZHU_ACTIVITY_BEGIN_TIME]) > 0 ? intval($arrItemInfo[self::BAZHU_ACTIVITY_BEGIN_TIME]) : 0;
        $bazhuActivityEndtime = !empty($arrItemInfo[self::BAZHU_ACTIVITY_END_TIME]) && intval($arrItemInfo[self::BAZHU_ACTIVITY_END_TIME]) > 0 ? intval($arrItemInfo[self::BAZHU_ACTIVITY_END_TIME]) : 0;
        $bazhuActivityJoinEndtime = !empty($arrItemInfo[self::BAZHU_ACTIVITY_JOIN_END_TIME]) && intval($arrItemInfo[self::BAZHU_ACTIVITY_JOIN_END_TIME]) > 0 ? intval($arrItemInfo[self::BAZHU_ACTIVITY_JOIN_END_TIME]) : 0;
        $bzActivityOpenAwardTime = !empty($arrItemInfo[self::BAZHU_ACTIVITY_OPEN_AWARD_TIME]) && intval($arrItemInfo[self::BAZHU_ACTIVITY_OPEN_AWARD_TIME]) > 0 ? intval($arrItemInfo[self::BAZHU_ACTIVITY_OPEN_AWARD_TIME]) : 0;
        $bazhuActivityPriforumUv = !empty($arrItemInfo[self::BAZHU_ACTIVITY_PRIFORUM_UV]) && intval($arrItemInfo[self::BAZHU_ACTIVITY_PRIFORUM_UV]) > 0 ? intval($arrItemInfo[self::BAZHU_ACTIVITY_PRIFORUM_UV]) : 0;

        $ret = array(
            'begin_time' => $bazhuActivityBegintime,
            'end_time' => $bazhuActivityEndtime,
            'join_end_time' => $bazhuActivityJoinEndtime,
            'open_award_time' => $bzActivityOpenAwardTime,
            'activity_priforum_uv' => $bazhuActivityPriforumUv
        );
        return $ret;
    }

}