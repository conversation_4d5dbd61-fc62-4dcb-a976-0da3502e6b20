<?php
/**
 * Created by PhpStorm.
 * User: huxiaomei
 * Date: 2019/3/28
 * Time: 下午6:37
 */
class Util_Newbawurule_Check{
    /**
     * 是否为私有化吧
     * @param  [type]  $intFid [description]
     * @return array         [description]
     */
    public static function isPrivateForum($intFid=0,$strFname='')
    {
        if (!$intFid && !$strFname){
            return false;
        }else{
            if ($intFid){
                $arrInput = array(
                    'forum_id' => $intFid
                );
                $arrOutput = Tieba_Service::call('forum', 'getForumAttr', $arrInput, null, null, 'post', 'php', 'utf-8');
                $isPrivateForum = isset($arrOutput['output']['is_private_forum']) ? $arrOutput['output']['is_private_forum'] : 0;
            }else if ($strFname){
                $arrInput = array(
                    'forum_name' => $strFname
                );
                $arrOutput = Tieba_Service::call('forum', 'getBtxInfoByName', $arrInput, null, null, 'post', 'php', 'utf-8');
                $isPrivateForum = isset($arrOutput['attrs']['is_private_forum']) ? $arrOutput['attrs']['is_private_forum'] : 0;
            }
            if ($isPrivateForum){
                return true;
            }else{
                return false;
            }
        }
    }

    /**
     * 跳转到错误页
     * @param  [type] $tag [description]
     * @return [type]          [description]
     */
    public static function redirectToErrorPage($errno,$title) {
        $pageTitle = isset($title) && !empty($title) ? $title : '出错啦';
        $errorPage = 'https://tieba.baidu.com/mo/q/'.Mo_Core_Url::ROUTER_NEWBAWURULE_ERROR.'?title='.$pageTitle.'&errorno='.$errno;
        Bingo_Http_Response::redirect($errorPage);
    }

    /**
     * 跳转到客户端下载页
     * @param  [type] $tag [description]
     * @return [type]          [description]
     */
    public static function redirectToDownload() {
        $downloadPage = 'http://tieba.baidu.com/mo/q/activityDiversion/download?fr=bawu';
        Bingo_Http_Response::redirect($downloadPage);
    }

}