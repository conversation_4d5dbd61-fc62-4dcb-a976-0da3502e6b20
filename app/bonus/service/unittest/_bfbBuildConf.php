<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file _bfbBuildConf.php
 * <AUTHOR>
 * @date 2015/01/26 14:35:43
 * @brief 
 *  
 **/

$arrConf = array();
$arrConf['currency'] = 1;  //币种
$arrConf['expire_time'] = date("YmdHis",1425571200);   //过期时间
$arrConf['goods_desc'] = "这是一笔使用百度钱包纯网关支付的订单";   //商品说明
//$arrConf['goods_desc'] = iconv("UTF-8","GBK",$arrConf['goods_desc']);     //转码
$arrConf['goods_name'] = "使用百度钱包支付的商品"; //商品名称
//$arrConf['goods_name'] = iconv("UTF-8","GBK",$arrConf['goods_name']);     //转码
$arrConf['input_charset'] = 1;     //输出char set
$arrConf['order_create_time'] = date("YmdHis",time());     //订单创建时间
$arrConf['order_no'] = 34000000098;       //订单ID
$arrConf['page_url'] = "http://tieba.baidu.com/show";     //前台通知url
$arrConf['pay_type'] = 2;      //支付方式
$arrConf['return_url'] = "http://www.yoursite.com/return_url"; //后台通知url
$arrConf['service_code'] = 1;  //必须为1，服务方式
$arrConf['sign_method'] = 1;   //必须为1，签名方式
$arrConf['sp_no'] = 9000100005;    //商户号
$arrConf['total_amount'] = 1;  //总话费RMB,分为单位
$arrConf['sp_rcs'] = "";   //用户风控信息
$arrConf['version'] = 2;   //服务版本
$arrConf['buyer_sp_username'] = "pangzhanbo";    //商户user_name
//$arrConf['buyer_sp_username'] = iconv("UTF-8","GBK",$arrConf['buyer_sp_username']);   //转码
$arrConf['sp_uno'] = 7518505;    //商户user_id
$arrConf['extra'] = "1496";

$arrInput =array(
    'conf' => $arrConf,
    'url' => "https://www.baifubao.com/api/0/pay/0/pre_direct?",
    'key' => "pSAw3bzfMKYAXML53dgQ3R4LsKp758Ss",
    );
$url = Service_BfbPay::_bfbBuildConf($arrInput);
var_dump($url);






/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
