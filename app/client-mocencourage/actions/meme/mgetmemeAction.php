<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2017/7/31
 * Time: 15:05
 */
class mgetmemeAction extends Molib_Client_BaseAction
{
    protected $userId = 0;
    protected $errno = Tieba_Errcode::ERR_SUCCESS;
    protected $users = array();
    /**
     * @param:null
     * @return:null
     **/
    public function _getPrivateInfo(){
        $arrPrivateInfo = array();
        $arrPrivateInfo['ids'] = json_decode($this->_getInput('ids', ''));

        $arrPrivateInfo['need_login'] = true;
        $arrPrivateInfo['check_login'] = true;
        return $arrPrivateInfo;
    }
    /**
     * @param:null
     * @return:null
     **/
    public function _checkPrivate(){
        $ids = $this->_objRequest->getPrivateAttr('ids');
        if(!is_array($ids)){
            Bingo_Log::warning('params error '.serialize($ids));
            return false;
        }
        return true;
    }

    /**
     * @param:null
     * @return:null
     **/
    public function _execute(){
        $this->userId = $this->_objRequest->getCommonAttr('user_id'); 
        $ids = $this->_objRequest->getPrivateAttr('ids');
        foreach ($ids as $id){
            $tmp = intval($id);
            if($tmp > 0){
                $input[] = $tmp;
            }
        }
        $data = array();

        if(empty($input)){
            Bingo_Log::warning('empty normal input params');
//            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, array());
            $data = self::getPackageByUserId();
        } else {
            $data = self::getPackage($input);
        }

        if($this->errno != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('ui encounter error');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array());
        }
        $arrOut['data']=$data;
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOut);
    }


    /**
     * @param $arrInput
     * @return array
     */
    protected function injectUserName(&$arrInput){
        $input = array(
            "user_id" => $this->users,
        );
        $data = Tieba_Service::call('user', 'getUnameByUids', $input, null, null, 'post', 'php', 'utf-8');
        if(!empty($data) && isset($data['errno']) && Tieba_Errcode::ERR_SUCCESS === $data['errno']){
            $tmp = $data['output']['unames'];
            foreach($tmp as $value){
                $ret[$value['user_id']] = $value['user_name'];
            }
            $arrInput['auther'] = $ret[$arrInput['owner']];
        }
        else{
            Bingo_Log::warning("call getUnameByUids failed,rets: ".serialize($data).' input:'.serialize($input));
            $this->errno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            return array();
        }
    }

    /**
     * @param $ids
     * @return array
     */
    protected function getPackage($ids){
        $input = array(
            'ids' => $ids,
            'user_id' => $this->userId,
        );
        $data = Tieba_Service::call('meme', 'getPackageByIds', $input, null, null, 'post', 'php', 'utf-8');
        if(!empty($data) && isset($data['errno']) && Tieba_Errcode::ERR_SUCCESS === $data['errno']){
            $tmp = $data['data'];
            foreach($tmp as $value){
                $value['avatar'] = 'http://tb.himg.baidu.com/sys/portrait/item/'.Tieba_Ucrypt::encode($value['owner'], $value['author']);
                $ret[] = $value;
//                if(intval($value['status']) != 1 && intval($value['owner']) != $this->userId){
//                    Bingo_Log::warning('package status not 1: '.$value['id']);
////                    continue;
//                }
//                else{
//                    $value['avatar'] = $this->_pid2Url(Tieba_Ucrypt::encode($value['owner'], $value['auther']));
//                    $ret[] = $value;
//                }
            }
        }
        else{
            Bingo_Log::warning("call getPackageDetailById failed, rets: ".serialize($data).' input:'.$input);
            $this->errno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            return array();
        }
        if(empty($ret)){
            $this->errno = Tieba_Errcode::ERR_SUCCESS ;
            return array();
        }
        foreach ($ret as $value){
            $ids[] = intval($value['id']);
        }
        $input = array('ids'=>$ids,);
        $data = Tieba_Service::call('meme', 'getMapByPackages', $input, null, null, 'post', 'php', 'utf-8');
        if(!empty($data) && isset($data['errno']) && Tieba_Errcode::ERR_SUCCESS === $data['errno']) {
            foreach($ret as &$value){
                $value['pics'] = $data['data'][intval($value['id'])];
            }
            return $ret;
        }
        else {
            Bingo_Log::warning("call getMapByPackages failed, rets: ".serialize($data).' input:'.$input);
            $this->errno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            return array();
        }

    }

    /**
     * @param $ids
     * @return array
     */
    protected function getPackageByUserId(){
        $input = array(
            'user_id' => $this->userId,
        );

        $data = Tieba_Service::call('meme', 'getPackageByUserId', $input, null, null, 'post', 'php', 'utf-8');
        if(!empty($data) && isset($data['errno']) && Tieba_Errcode::ERR_SUCCESS === $data['errno']){
            $tmp = $data['data'];
            foreach($tmp as $value){
                $value['avatar'] = 'http://tb.himg.baidu.com/sys/portrait/item/'.Tieba_Ucrypt::encode($value['owner'], $value['author']);
                $ret[] = $value;
//                if(intval($value['status']) != 1 && intval($value['owner']) != $this->userId){
//                    Bingo_Log::warning('package status not 1: '.$value['id']);
////                    continue;
//                }
//                else{
//                    $value['avatar'] = $this->_pid2Url(Tieba_Ucrypt::encode($value['owner'], $value['author']));
//                    $ret[] = $value;
//                }
            }
        } else {
            Bingo_Log::warning("call getPackageDetailById failed, rets: ".serialize($data).' input:'.$input);
            $this->errno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            return array();
        }

        if(empty($ret)){
            $this->errno = Tieba_Errcode::ERR_SUCCESS ;
            return array();
        }

        foreach ($ret as $value){
            $ids[] = intval($value['id']);
        }
        $input = array('ids'=>$ids,);
        $data = Tieba_Service::call('meme', 'getMapByPackages', $input, null, null, 'post', 'php', 'utf-8');
        if(!empty($data) && isset($data['errno']) && Tieba_Errcode::ERR_SUCCESS === $data['errno']) {
            foreach($ret as &$value){
                $value['pics'] = $data['data'][intval($value['id'])];
            }
            return $ret;
        } else {
            Bingo_Log::warning("call getMapByPackages failed, rets: ".serialize($data).' input:'.$input);
            $this->errno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            return array();
        }

    }
    /**
     * @param $intPicId
     * @param string $type
     * @return mixed
     */
    private function _pid2Url($intPicId, $type = 'pic' ){
        $arrParams[] = [
            'pic_id' => $intPicId,
            'product_name' => 'forum',
            'pic_spec' => $type,
            'domain' => 'imgsrc.baidu.com',
        ];
        $arrPicUrls = Bd_Pic::pid2Url($arrParams, false);
        return $arrPicUrls['resps'][0];
    }

    /**
     * @param $errno
     * @param array $data
     * @return bool
     */
    private function _errRet($errno, $data = array()) {
        $msg = Molib_Client_Error::getErrMsg($errno);

        $this->_error($errno, $msg);
        $this->_objResponse->setOutData($data);
        return true;
    }
}
