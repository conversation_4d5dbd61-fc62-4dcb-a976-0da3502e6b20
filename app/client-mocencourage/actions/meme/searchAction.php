<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/



/**
 * @file searchAction.php
 * <AUTHOR>
 * @date 2017-05-15
 * @brief 
 *  
 **/

class searchAction extends Molib_Client_BaseAction
{

    const WORD_LIST_NAME = 'tb_wordlist_redis_forum_meme_filter_text';
    const WORD_CONT_SIGN_NAME = 'tb_wordlist_redis_forum_meme_cont_sign';
    const WORD_CONT_SIGN_KEY = 'meme_cont_sign';
    const QUERY_MAX_LENGTH = 512;
    const ERRNO_MAG_PARAM_ERROR = "参数错误";
    const UEG_STRAGETY_HUANGFAN = "forum_confilter";
    const UEG_STRAGETY_MEME_WORD= "confilter_meme_word";
    const UEG_STRAGETY_AV= "confilter_pornactress";
    const UEG_RESULT_STATUS_PASS = 1;
    const UEG_RESULT_STATUS_UNPASS = 2;

    const WORDS_PASS = "正常";
    const WORDS_UNPASS = "包含敏感字符，请重新输入";
    const UEG_ERRNO_REFUSED_2200015 = 220015;
    const UEG_ERRNO_REFUSED_220009  = 220009;
    const UEG_ERRNO_REFUSED_200008  = 200008;
    const UEG_ERRNO_PASS = 0;


    /**
     * @param:null
     * @return:null
     **/
    public function _getPrivateInfo()
    {
		$arrPrivateInfo = array();
		$arrPrivateInfo['check_login'] = false;
        $arrPrivateInfo['need_login'] = false;
//        $arrPrivateInfo['ispv'] = 1;
//        $arrPrivateInfo['isuv'] = 1;
        $arrPrivateInfo['kw'] = strval($this->_getInput('kw', ''));
        $arrPrivateInfo['pn'] = intval($this->_getInput('pn', 1));
        $arrPrivateInfo['rn'] = intval($this->_getInput('rn', 10));
        return $arrPrivateInfo;
    }
    
    /**
     * @param:null
     * @return:null
     **/
    public function _checkPrivate()
    {


        return true;
    }


    /**
     * @param $query
     * @return bool|int
     */
    public function checkUegStatus($query){
        $strContent = Bingo_Encode::convert($query, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);

        if($strContent == ''){
            Bingo_Log::warning('param error');
            return $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $reqUeg=array(
            'req'=>array(
                'confilter_type'=>self::UEG_STRAGETY_HUANGFAN,
                '(raw)rawdata'=>$strContent.'\0',
                'title'=>' ',
                'detail_len'=>strlen($strContent)+1,
            ),
        );

        $resUeg = Tieba_Service::call('anti','antiConfilter',$reqUeg);
        $status = self::UEG_RESULT_STATUS_PASS;
        if($resUeg === false || $resUeg['errno'] != self::UEG_ERRNO_PASS){
            Bingo_Log::warning('ueg return not success,errno = '.$resUeg['errno']);
        }
        if($resUeg['errno'] == self::UEG_ERRNO_REFUSED_2200015 || $resUeg['errno'] == self::UEG_ERRNO_REFUSED_220009 ||$resUeg['errno'] == self::UEG_ERRNO_REFUSED_200008){
            $status = self::UEG_RESULT_STATUS_UNPASS;
        }

        return $status;
    }

    /**
     * @param $query
     * @return bool|int
     */
    public function checkUegMemeStatus($query){
//        $strContent = Bingo_Encode::convert($query, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
        $strContent = $query;
        if($strContent == ''){
            Bingo_Log::warning('param error');
            return $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $reqUeg=array(
            'req'=>array(
                'confilter_type'=>'Confilter',
                'reqs'=>array(
                    array(
                        'groupid'=>-1,
                        'content'=>$strContent,
                        'return_position'=>'no',
                        'no_normalize'=>'yes',
                        'dictlist'=> self::UEG_STRAGETY_MEME_WORD,
                    ),
                    array(
                        'groupid'=>-1,
                        'content'=>$strContent,
                        'return_position'=>'no',
                        'no_normalize'=>'yes',
                        'dictlist'=> self::UEG_STRAGETY_AV,
                    ),

                    array(
                        'groupid'=>-1,
                        'content'=>$strContent,
                        'return_position'=>'no',
                        'no_normalize'=>'yes',
                        'dictlist'=> 'confilter_Word_key_Leader1',
                    ),
                ),

            )
        );
        $resUeg = Tieba_Service::call('anti','antiConfilter',$reqUeg);

        $status = self::UEG_RESULT_STATUS_PASS;
        if($resUeg === false || $resUeg['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('ueg return not success,errno = '.$resUeg['errno']);
        }

        // 检测是否命中ueg词表
        $arrAns = json_decode(json_encode($resUeg['res']['ans']), true);
        Bingo_Log::notice("search_check_" . $strContent . serialize($arrAns, 1));
        foreach ($arrAns as $ans) {
            if (intval($ans['count']) > 0) {
                $status = self::UEG_RESULT_STATUS_UNPASS;
            }
        }

        return $status;
    }


    /**
     * @param $query
     * @return bool|int
     */
    public function check19daNewWordsStatus($query){
//        $strContent = Bingo_Encode::convert($query, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
        $strContent = $query;
        if($strContent == ''){
            Bingo_Log::warning('param error');
            return $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $reqUeg=array(
            'req'=>array(
                'confilter_type'=>'Confilter',
                'reqs'=>array(
                    array(
                        'groupid'=>-1,
                        'content'=>$strContent,
                        'return_position'=>'no',
                        'no_normalize'=>'yes',
                        'dictlist'=> 'confilter_nickname_filter',
                    ),
                ),

            )
        );
        $resUeg = Tieba_Service::call('anti','antiConfilter',$reqUeg);

        $status = self::UEG_RESULT_STATUS_PASS;
        if($resUeg === false || $resUeg['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('ueg return not success,errno = '.$resUeg['errno']);
        }

        // 检测是否命中ueg词表
        $arrAns = json_decode(json_encode($resUeg['res']['ans']), true);
        Bingo_Log::notice("search_check_" . $strContent . serialize($arrAns, 1));
        foreach ($arrAns as $ans) {
            if (intval($ans['count']) > 0) {
                $status = self::UEG_RESULT_STATUS_UNPASS;
            }
        }

        return $status;
    }
    /**
     * @param $intForumId
     * @return bool
     */
    public function checkSelfMemeWordList($query) {


        $objWordServer = Wordserver_Wordlist::factory();
        $arrKeys = array($query);
        $arrRet = $objWordServer->getValueByKeys($arrKeys, self::WORD_LIST_NAME);
        $status = self::UEG_RESULT_STATUS_PASS;
        if(!empty($arrRet)) {
            foreach($arrKeys as $strKey) {
                if(!is_null($arrRet[$strKey])) {
                    $status = self::UEG_RESULT_STATUS_UNPASS;
                }
            }
        }
        return $status;
    }

    /**
     * @param $intForumId
     * @return bool
     */
    public function getContSignWordList() {
        $objWordServer = Wordserver_Wordlist::factory();
        $arrKeys = array(self::WORD_CONT_SIGN_KEY);
        $arrRet = $objWordServer->getValueByKeys($arrKeys, self::WORD_CONT_SIGN_NAME);
        $arrContSign = unserialize($arrRet[self::WORD_CONT_SIGN_KEY]);
        return $arrContSign;
    }

    /**
     * @param:null
     * @return:null
     **/
    public function _execute()
    {

        $strkw  = $this->_objRequest->getPrivateAttr('kw');
        $intPn          = $this->_objRequest->getPrivateAttr('pn');
        $intRn          = $this->_objRequest->getPrivateAttr('rn');


        $strkw = trim($strkw);
        if(mb_strwidth($strkw,'utf8') >= self::QUERY_MAX_LENGTH){
            Bingo_Log::warning(" invalid  param . [input=".serialize($strkw)."]");
            $strkw = mb_strimwidth($strkw,0,self::QUERY_MAX_LENGTH,'','utf8');
        }

        $boolPass = $this->checkUegStatus($strkw);
        if($boolPass == self::UEG_RESULT_STATUS_UNPASS){
            Bingo_Log::warning("query no pass ueg!!! query is .".$strkw);
            return $this->_errRet(Tieba_Errcode::ERR_NVOTE_CONTENT_UNUSUAL, array());
        }

        $boolMemePass = $this->checkUegMemeStatus($strkw);
        if($boolMemePass == self::UEG_RESULT_STATUS_UNPASS){
            Bingo_Log::warning("query no pass ueg!!! query is .".$strkw);
            return $this->_errRet(Tieba_Errcode::ERR_NVOTE_CONTENT_UNUSUAL, array());
        }

        $bool19daMemePass = $this->check19daNewWordsStatus($strkw);
        if($bool19daMemePass == self::UEG_RESULT_STATUS_UNPASS){
            Bingo_Log::warning("query no pass ueg!!! query is .".$strkw);
            return $this->_errRet(Tieba_Errcode::ERR_NVOTE_CONTENT_UNUSUAL, array());
        }


        $boolMemeSelf = $this->checkSelfMemeWordList($strkw);
        if($boolMemeSelf == self::UEG_RESULT_STATUS_UNPASS){
            Bingo_Log::warning("query no pass ueg!!! query is .".$strkw);
            return $this->_errRet(Tieba_Errcode::ERR_NVOTE_CONTENT_UNUSUAL, array());
        }


        $arrParams = array(
            'kw' => $strkw,
            'pn' => $intPn,
            'rn' => $intRn,
        );

        for($intTryTimes = 0; $intTryTimes < 3; ++$intTryTimes) {
            $arrResult = Tieba_Service::call('meme', 'search', $arrParams, null, null, 'post', 'php', 'utf-8', 'local');
            if ( false !== $arrResult  && $arrResult['errno'] == Tieba_Errcode::ERR_SUCCESS ) {
                break;
            }
        }

        if ( false === $arrResult  || $arrResult['errno'] != Tieba_Errcode::ERR_SUCCESS ) {
            Bingo_Log::warning("failed to call meme.search. [input=".serialize($arrParams)."][out=".serialize($arrResult).']');
            return $this->_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array());
        }

        //get cont_sign

        $arrFilterContSign= $this->getContSignWordList();

        $arrOutSearch = array();
        foreach ($arrResult['data']['list'] as $item){
            if(!in_array($item['cont_sign'],$arrFilterContSign)){
                $arrOutSearch[]=$item;
            }
        }
        $arrOut['list'] = $arrOutSearch;
        $arrOut['page'] = $arrResult['data']['page'];
        return $this->_errRet(Tieba_Errcode::ERR_SUCCESS,$arrOut);
    }

    /**
     * @param $errno
     * @param array $data
     * @return bool
     */
    private function _errRet($errno, $data = array()) {
        $msg = Molib_Client_Error::getErrMsg($errno);
        if($errno == Tieba_Errcode::ERR_PARAM_ERROR){
            $msg = self::ERRNO_MAG_PARAM_ERROR;
        }
        if($errno == Tieba_Errcode::ERR_NVOTE_CONTENT_UNUSUAL){
            $msg = self::WORDS_UNPASS;
        }
        $this->_error($errno, $msg);
        $this->_objResponse->setOutData($data);
        return true;
    }

}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
