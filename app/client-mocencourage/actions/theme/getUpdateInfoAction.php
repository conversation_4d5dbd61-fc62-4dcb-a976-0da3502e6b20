<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/


/**
 * @file getSkinAction.php
 * <AUTHOR>
 * @brief  详情页获取皮肤详细信息
 * @param  common
 **/

class getUpdateInfoAction extends Molib_Client_BaseAction
{

    public function _getPrivateInfo()
    {
        $arrPrivateInfo['ispv'] = 1;
        return $arrPrivateInfo;
    }

    public function _checkPrivate()
    {
        return true;
    }

    public function _execute()
    {
    	$props_version = Bingo_Http_Request::getNoXssSafe('props_version',1);
        $arrRes = $this->_getUpdateProps($props_version);
    	if ($arrRes === false){
            $this->_error(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL));
            $arrRes = array();
        } 
		$arrOut = array();
		$arrOut['props'] = $arrRes;
        //stlog
        //$this->_stlog($arrOut);
        $this->_objResponse->setOutData($arrOut);
    }

    private function _stlog($arrInput){
       
        $this->_objResponse->addLog('theme_dir',$arrInput);
    }
    

   /**
     * get skin list
     *
     * return  array
     */
    private function _getUpdateProps($props_version){
        $intClientType = intval($this->_objRequest->getCommonAttr('client_type'));
        $arrParams = array(
        	'client_type'   => $intClientType,
        	'props_version' => $props_version,
        );
        $arrResult = Tieba_Service::call('theme','getUpdateProps',$arrParams, null, null,'post','php','utf-8');
        if (false === $arrResult ) {
            Bingo_Log::fatal("failed to call theme. input:".serialize($arrParams)." output:".serialize($arrResult));
            return false;
        } else if( $arrResult['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal("failed to call theme. input:".serialize($arrParams)." output:".serialize($arrResult));
            return false;
        }   
        if (isset($arrResult['data']) && !empty($arrResult['data'])){
            return $arrResult['data'];
        } else {
            Bingo_Log::warning("failed to call getPropsListByCategory .input:".serialize($arrParams)." output:".serialize($arrResult));
            return array();
        }
    }
}

?>