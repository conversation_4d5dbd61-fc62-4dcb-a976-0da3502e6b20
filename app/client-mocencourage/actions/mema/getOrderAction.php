<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/


/**
 * @file buypropsAction.php
 * <AUTHOR>
 * @date 2014/07/02 13:59:25
 * @brief  生成钱包订单
 *  
 **/

class getOrderAction extends Molib_Client_BaseAction
{

    const PARAMS_TERMINAL    = "android";
    const	PAY_TYPE_MEMBER    = 1;
    const	PAY_TYPE_TDOU      = 2;

    const VERSION_POPUP_REDIS_TABLE = 'tb_wordlist_redis_pay_version_popup';
    private $popupInfo = null;

    /**
     * 自定义标签配置
     * @var array
     */
    private static $arrTagNames = array(
//        'BAIDU-BAIFUBAO-WISE' => '推荐',
    );

    /**
     * 自定义促销文案配置
     * @var array
     */
    private static $arrPromptTexts = array(
//        'BAIDU-BAIFUBAO-WISE' => '充值立减10元',
    );

    /**
     * 自定义是否默认选中
     * @var array
     */
    private static $arrDefault = array(
//        'BAIDU-BAIFUBAO-WISE' => 1,
    );


    public function _getPrivateInfo()
    {
        $arrPrivateInfo['ispv']        = 0;
        $arrPrivateInfo['check_login'] = true;
        $arrPrivateInfo['need_login']  = true;
        $arrPrivateInfo['check_real_name'] = true;
        //$this->_objRequest->addStrategy('check_sign', false);
        //params
        $arrPrivateInfo['pay_type']  = intval($this->_getInput('pay_type', 0));
        if (4 == $this->_getInput('pay_type', 0)) {
            Bingo_Log::warning('pzbtest'.var_export($this->_objRequest->getPrivateAttr('wares'),true));
            $arrPrivateInfo['pay_type'] = 1;
        }
        $arrPrivateInfo['is_left']   = intval($this->_getInput('is_left', 0));
        $arrPrivateInfo['order_url'] = $this->_getInput('order_url', 0);
        $arrPrivateInfo['mobile'] = $this->_getInput('mobile', 0);
        $arrPrivateInfo['wares'] = $this->_getInput('wares', 0);
        $arrPrivateInfo['is_autopay'] = $this->_getInput('is_autopay', 0);
        $arrPrivateInfo['payment_pos_key']  = $this->_getInput('payment_pos_key',0);//这里注释下，payment_pos_key 后面会变成 payment_pos_id,因为后续会对这个进行加密，这里先这样写~~
        $arrPrivateInfo['payment_pos_id']  = $this->_getInput('payment_pos_key',0);//这里注释下，payment_pos_key 后面会变成 payment_pos_id,因为后续会对这个进行加密，这里先这样写~~
        $arrPrivateInfo['user_id'] = $this->_getInput('user_id',0);
        $arrPrivateInfo['refer_page'] = $this->_getInput('refer_page', '');
        $arrPrivateInfo['click_zone'] = $this->_getInput('click_zone', '');
        $arrPrivateInfo['wallet_sdk_ua'] = $this->_getInput('wallet_sdk_ua', '');

        return $arrPrivateInfo;
    }

    public function _checkPrivate()
    {
        $wares =  $this->_objRequest->getPrivateAttr('wares');
        $strSubAppType = $this->_objRequest->getCommonAttr("subapp_type", "tieba");
        if ($strSubAppType == "mobilebaidu") {
            $wares = json_decode($wares, true);
        }
        
        if ( $this->_objRequest->getPrivateAttr('pay_type') != 1 && $this->_objRequest->getPrivateAttr('pay_type') != 2 ){
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_MO_PARAM_INVALID));
            return false;
        }
        if($this->_objRequest->getPrivateAttr('pay_type') == 1 && $wares['new_props_id'] == 0){
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_MO_PARAM_INVALID));
            return false;
        }
        if($this->_objRequest->getPrivateAttr('pay_type') == 2 && $wares['new_props_id'] == ''){
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_MO_PARAM_INVALID));
            return false;
        }
        if ( $wares['money'] == 0 ){
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_MO_PARAM_INVALID));
            return false;
        }
        return true;
    }

    /**
     * 检查是否需要出升级提示弹窗
     * @return [type] [description]
     */
    private function _checkNewVersion() {
        $intClientType    = intval($this->_objRequest->getCommonAttr('client_type'));
        $strClientVersion = strval($this->_objRequest->getCommonAttr('client_version'));
        
        if ($intClientType != Molib_Client_Define::CLIENT_TYPE_ANDROID) {
            return false;
        }

        $isSupportedVersion = Molib_Util_Version::compare($strClientVersion, '7.3.0') <= 0 ? true : false;

        // 7.3.0以下版本不支持本弹窗
        if (!$isSupportedVersion) {
            return false;
        }

        $popup = $this->_getPopupConfig($strClientVersion);

        if (empty($popup)) {
            return false;
        }

        $this->popupInfo = array(
            'popup' => $popup,
        );

        return true;
    }

    /**
     * 获取大于本次请求app版本号的最小版本号的配置
     * @param  [type] $strClientVersion [description]
     * @return [type]                   [description]
     */
    private function _getPopupConfig($strClientVersion) {
        $affectedVersions = $this->_getRedisArrayItem('affected_version');

        if (empty($affectedVersions)) {
            return false;
        }

        sort($affectedVersions);

        $selectedVer = '';

        foreach ($affectedVersions as $ver) {
            $isAppVerLower = Molib_Util_Version::compare($strClientVersion, $ver) >= 0;

            if ($isAppVerLower) {
                $selectedVer = $ver;
                break;
            }
        }

        if (empty($selectedVer)) {
            return false;
        }

        $conf = $this->_getRedisArrayItem($ver);

        if (empty($conf)) {
            return false;
        }

        $popup = array(
            'popup_id' => intval($conf[0]),
            'popup_times' => intval($conf[1]),
            'popup_type' => intval($conf[2]),
            'hint' => $conf[3],
            'y_btn_text' => $conf[4],
            'y_btn_link' => $conf[5],
            'n_btn_text' => $conf[6],
            'n_btn_link' => $conf[7],
        );

        if ($popup['popup_type'] == 2) {
            $popup['app_version'] = $conf[8];
            $popup['app_size'] = intval($conf[9]);
            $popup['changelogs'] = $conf[10];
            $popup['appendix_text'] = $conf[11];
            $popup['appendix_link'] = $conf[12];
            $popup['appendix_optional'] = intval($conf[13]);
        }

        return $popup;
    }

    /**
     * 根据key从redis里取出数组型的记录
     * @param  [type] $key [description]
     * @return [type]      [description]
     */
    private function _getRedisArrayItem($key) {
        $ws = Wordserver_Wordlist::factory();

        $ret = $ws->getValueByKeys(array($key), self::VERSION_POPUP_REDIS_TABLE);

        if (empty($ret) || empty($ret[$key])) {
            return false;
        }

        $item = unserialize($ret[$key]);

        if (empty($item) || !is_array($item)) {
            return false;
        }

        return $item;
    }

    public function _execute()
    {
        $arrOut              = array();
        $boolLogin           = $this->_objRequest->getCommonAttr('login');
        $arrWares =  $this->_objRequest->getPrivateAttr('wares');
        $strMobile =  $this->_objRequest->getPrivateAttr('mobile');
        $strOrderUrl =  $this->_objRequest->getPrivateAttr('order_url');
        $intIsLeft =  $this->_objRequest->getPrivateAttr('is_left');
        $intPayType =  $this->_objRequest->getPrivateAttr('pay_type');
        $intIsAutopay   = intval($this->_objRequest->getPrivateAttr('is_autopay'));
        $intPaymentPosId    = intval($this->_objRequest->getPrivateAttr('payment_pos_id'));
        $strReferPage  = strval($this->_objRequest->getPrivateAttr('refer_page'));
        $strClickZone  = strval($this->_objRequest->getPrivateAttr('click_zone'));
        $intUserId = intval($this->_objRequest->getPrivateAttr('user_id'));
        $strSubAppType = $this->_objRequest->getCommonAttr("subapp_type", "tieba");
        $strCuid = strval($this->_objRequest->getCommonAttr('cuid'));
        $strWalletUa = strval($this->_objRequest->getPrivateAttr('wallet_sdk_ua'));

        // sdk decode
        if ($strSubAppType == "mobilebaidu") {
            $arrWares = json_decode($arrWares, true);
        } else if($strSubAppType == ""){
            $strSubAppType = "tieba";
        }
        
        Bingo_Log::warning('refer_page:'.var_export($strReferPage,true));
        Bingo_Log::warning('click_zone:'.var_export($strClickZone,true));
        //由于客户端要复用会员充值接口,估icon_id用new_props_id代替,icon_count用props_mon代替
        if($intPayType == self::PAY_TYPE_MEMBER){
            $strProps_id  = 'memberId';
            $strProps_mon = 'memberCount';
        }else if($intPayType == self::PAY_TYPE_TDOU){
            $strProps_id  = 'iconId';
            $strProps_mon = 'iconCount';
        }else{
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_MO_PARAM_INVALID));
            return false;
        }

        $needUpgrade = $this->_checkNewVersion();

        if ($needUpgrade) {
            // 这个错误号是临时拉来充壮丁，不要纠结其含义
            $code = Tieba_Errcode::ERR_PARAM_ERROR;
            $msg = Molib_Client_Error::getErrMsg($code);

            $this->_error($code, $msg);
            $this->_objResponse->setOutData($this->popupInfo);
            
            return false;
        }
        if( $intUserId && $intUserId != intval($this->_objRequest->getCommonAttr('user_id'))){
            Bingo_Log::warning('two uid is not equal! user_id:'.$intUserId .' uid:'.$this->_objRequest->getCommonAttr('user_id'));
            $this->_error(Tieba_Errcode::ERR_MO_PARAM_INVALID, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_MO_PARAM_INVALID));
            return false;
        }
        $arrInput = array(
            'channel'        => '2',
            'payAmount'      => $arrWares['money'],
            'originalAmount' => $arrWares['money'],
            'user_id'        => $this->_objRequest->getCommonAttr('user_id'),
            'title'          => $arrWares['wares_name'],
            'url'            => $strOrderUrl,
            'itemInfo'       => $arrWares['wares_item'],
            'mobile'         => $strMobile,
            'imei'           => $this->_objRequest->getCommonAttr('_phone_imei'),
            'pay_type'       => intval($this->_objRequest->getPrivateAttr('pay_type')),
            'is_left'        => $intIsLeft,
            $strProps_mon    => $arrWares['props_mon'],
            $strProps_id     => $arrWares['new_props_id'],
            'terminal'       => self::PARAMS_TERMINAL,
            'third_order_id' => '',
            'fr'             => 0,	
            'scene_id'       => 0,	
            'refer_page'     => $strReferPage,
            'click_zone'     => $strClickZone,
            'click_pos'      => ($intPayType == self::PAY_TYPE_MEMBER) ? $arrWares['props_id'].'_'.$arrWares['props_mon'] : $arrWares['new_props_id'],
            'cuid'           => $strCuid,
            'wallet_sdk_ua'  => $strWalletUa,
            'subapp_type'    => $strSubAppType,
        );
        $intIsAutopay > 0 && $arrInput['is_autopay'] = $intIsAutopay;
        $intPaymentPosId > 0 && $arrInput['payment_pos_id'] = $intPaymentPosId;
        /*
        //common params
        $this->_intClientType = intval($this->_objRequest->getCommonAttr('client_type'));
        $this->_intScrW       = intval($this->_objRequest->getPrivateAttr('scr_w'));
        $this->_intCuid       = trim($this->_objRequest->getCommonAttr('cuid'));
        $this->_strBduss      = trim($_COOKIE['BDUSS']);
        */
        $arrRes = $this->_getBuyPropsInfo($arrInput);
        
        if ($arrRes === false){
            $this->_error(Tieba_Errcode::ERR_ORDER_INVALID, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_ORDER_INVALID));
            $arrOut = array();
        }else{
            $arrOut = $this->_builderParamsters($arrRes);
        }
        if(1 == $this->_objRequest->getPrivateAttr('is_autopay')){
            $arrOut['pay_url']  = $arrRes['return_url'].'&nomenu=1';
            $arrOut['call_type']    = 'url';
        }

        //stlog
        $this->_stlog($arrOut);
        $this->_objResponse->setOutData($arrOut);
    }

    private function _stlog($arrInput){
       
        $this->_objResponse->addLog('walletOrder',$arrInput);
    }
    private function _builderParamsters($mixRes){
        $arrOutput = array();
        $arrOutput = $mixRes;

        foreach ($arrOutput['pay_channel']['list'] as $k => $v) {
            $strChannelName = $v['channel'];

            if (isset(self::$arrTagNames[$strChannelName])) {
                $arrOutput['pay_channel']['list'][$k]['tag_name'] = self::$arrTagNames[$strChannelName];
            }

            if (isset(self::$arrPromptTexts[$strChannelName])) {
                $arrOutput['pay_channel']['list'][$k]['prompt_text'] = self::$arrPromptTexts[$strChannelName];
            }

            if (isset(self::$arrDefault[$strChannelName])) {
                $arrOutput['pay_channel']['list'][$k]['is_default'] = self::$arrDefault[$strChannelName];
            }
        }

        foreach ($arrOutput['pay_channel']['fold'] as $k => $v) {
            $strChannelName = $v['channel'];

            if (isset(self::$arrTagNames[$strChannelName])) {
                $arrOutput['pay_channel']['fold'][$k]['tag_name'] = self::$arrTagNames[$strChannelName];
            }

            if (isset(self::$arrPromptTexts[$strChannelName])) {
                $arrOutput['pay_channel']['fold'][$k]['prompt_text'] = self::$arrPromptTexts[$strChannelName];
            }

            if (isset(self::$arrDefault[$strChannelName])) {
                $arrOutput['pay_channel']['fold'][$k]['is_default'] = self::$arrDefault[$strChannelName];
            }
        }

        $arrOutput['pay_channel'] = json_encode($arrOutput['pay_channel']);

        return $arrOutput;
    }

   /**
     * get buy props info
     *
     * return  false/true
     */
    private function _getBuyPropsInfo($arrInput){
        //如果是版本大于6.6.7返回2.0，否则1.0
        $strConf          = $this->_objRequest->getStrategy('andorid_buyprop_control');
        $intClientType    = intval($this->_objRequest->getCommonAttr('client_type'));
        $strClientVersion = strval($this->_objRequest->getCommonAttr('client_version'));
        $strCuid          = strval($this->_objRequest->getCommonAttr( 'cuid' ));
        $strVersion       = '1.0';
        if( Molib_Util_VersionMatch::checkClient($strConf, $intClientType, $strClientVersion) ){
            $strVersion   = '2.0';
        }
        $arrInput['version'] = $strVersion;
        $arrInput['cuid']    = $strCuid;
        Bingo_Log::warning ( 'hello_android_input=====>'. serialize($arrInput) . "\n\n" );
        $arrResult = Tieba_Service::call('tbmall','getPayUrl',$arrInput, null, null,'post','php','utf-8');
        Bingo_Log::warning ( 'hello_android_output=====>'. serialize($arrResult) . "\n\n" );

        if(false == $arrResult ) {
            Bingo_Log::warning("failed to call tbmall. input:".serialize($arrInput)." output:".serialize($arrResult));
            return false;
        }else if( $arrResult['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("failed to call tbmall. input:".serialize($arrInput)." output:".serialize($arrResult));
            return false;
        }   
        if (isset($arrResult['data']) && !empty($arrResult['data'])){
            return $arrResult['data'];
        }else{
            Bingo_Log::warning("failed to call get pay info .input:".serialize($arrInput)." output:".serialize($arrResult));
            return array();
        }
    }

}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
