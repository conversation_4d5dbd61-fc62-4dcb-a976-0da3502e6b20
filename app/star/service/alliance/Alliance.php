<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date      2014:09:23 15:58:15
 * @version
 * @structs & methods(copied from idl.)
 */
define("MODULE", "Star_service");

class Service_Alliance_Alliance {

    const DATABASE_NAME  = "forum_star";
    const TABLE_UNION    = "star_union";
    const TABLE_RECORD   = "union_record";
    const MEMCACHED_NAME = "forum_common";
    const SERVICE_NAME   = "Service_Alliance_Alliance";
    const REDIS_NAME     = "forum_star";
	const DEFAULT_STATUS = 0;
	const ALLIANCE_NUM = 15;
	const DEFAULT_PAGE_SIZE = 20;
	const AUDIT_REFUSE = 0;
    const AUDIT_PASS = 1; // 默认状态
    const AUDIT_REMOVE = 2; // FRS 默认需要15张联盟
    const APPLY_JOIN = 0; // 吧务后端一页展示的条数
    const APPLY_REMOVE = 1; // 审核拒绝
    const ACCEPT_JOIN = 2; // 审核通过
    const ACCEPT_REMOVE = 3; // 审核解除
    const CONTENT_JOIN = 0; // 申请加入
    const CONTENT_REMOVE = 1; // 申请解除
    const TYPE_STAR = 1; // 同意加入
    const UNION_AUDIT = 0; // 通用解除
    const UNION_JOIN = 1; // 结成联盟
    const UNION_REMOVE = 2; // 解除联盟
    const SWITCH_NAME = 'associate_forum'; // 明星的联盟
        const FORUM_ATTR_NAME = 'associate_forum'; // 联盟审核
        const APPLY_REF       = 4; // 联盟加入
        const REMOVE_REF      = 5; // 联盟解除
        protected static $_conf = null; // 吧属性字段
    protected static $_use_split_db = false;
    protected static $_redis = null;
    protected static $_cache = null;
    protected static $_arrRecordFields = array (
        'id', // ID
        'official_id', // 官方吧ID
        'official_name', // 官方吧吧名
        'forum_id', // 联盟ID
        'forum_name', // 联盟吧名
        'status', // 0:申请加入 1：申请解除
        'content', // 原先内容
        'apply_user', // 申请者
        'apply_id', // 申请ID
        'apply_time', // 申请时间
        'op_user', // 操作者
        'op_id', // 操作ID
        'op_time', // 操作时间
    );

    protected static $_arrUnionFields = array (
        'id', // ID
        'official_id', // 官方吧ID
        'official_name', // 官方吧吧名
        'official_portrait', // 官方吧头像
        'forum_id', // 联盟吧ID
        'forum_name', // 联盟吧吧名
        'is_union', // 是否是联盟 0：不是 1：是
    );
    static protected $_arrContentFields = array (
        0, // 未结盟
        1, // 已结盟
    );
    static protected $_arrStatusFields = array (
        0, // 待审核
        1, // 已审核
    );
    static protected $_arrUnoinFields = array (
        0, // 未结盟
        1, // 已结盟
    );
    static protected $_arrOpTypeFields = array (
        self::AUDIT_REFUSE,
        self::AUDIT_PASS,
        self::AUDIT_REMOVE,
        self::APPLY_JOIN,
        self::APPLY_REMOVE,
    );

    public static function preCall ($arrInput) {
        // pre-call hook
    }

    public static function postCall ($arrInput) {
        // post-call hook
    }

    /**
     * @brief 申请联盟
     * @param official_id   : uint32_t : 官方吧ID
     * @param official_name : string   : 官方吧吧名
     * @param forum_info    : array    : 申请联盟数组
     * @param user_id       : uint32_t : 申请者ID
     * @param user_name     : string   : 申请者名称
     * @param content       : uint32_t : 申请内容 0：申请加入 1：申请解除
     **/
    public static function applyAlliance ($arrInput) {
        //check the tb sig, if no need, just remove the codes below.
        /*
		if(!Tieba_Service::checkTBSig($arrInput)){
			Bingo_Log::warning("check tb sig fail. invalid request.");
			return self::_errRet(Tieba_Errcode::ERR_TBSIG_CHECK_FAIL);
		}
        */

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        $arrCheckParam = array ('official_id', 'official_name', 'forum_info', 'content', 'apply_id', 'apply_user',);
        if (false === self::_checkParam($arrInput, $arrCheckParam)) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 申请操作只能是这两种
        if (!in_array($arrInput['content'], self::$_arrContentFields)) {
            Bingo_Log::warning("input params content invalid");

            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 申请联盟吧数据
        if (!is_array($arrInput['forum_info']) || empty($arrInput['forum_info'])) {
            Bingo_Log::warning('input param error forum_info empty or null [' . serialize($arrInput) . ']');

            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (!self::_init()) {
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        $db = self::_getDB();
        if (!$db) {
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        // 开启事务
        $ret = $db->startTransaction();
        if ($ret === false) {
            Bingo_Log::warning("insert db error! sql: " . $db->getLastSQL() . ' affect_num:' . $db->getAffectedRows() . " [output:" . serialize($db->error()) . "]");

            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        // 拼URL
        $strUnionSql     = 'REPLACE INTO ' . self::TABLE_UNION . '(official_id,official_name,official_portrait,forum_id,forum_name,is_union,apply_time) VALUES ';
        $strUnionFormat  = "(%d,'%s','%s',%d,'%s',%d,%d)";
        $strRecordSql    = 'INSERT INTO ' . self::TABLE_RECORD . '(official_id,official_name,forum_id,forum_name,apply_id,apply_user,apply_time,op_time,content,status) VALUES ';
        $strRecordFormat = "(%d,'%s',%d,'%s',%d,'%s',%d,%d,%d,%d)";
        $intNow          = time();
        foreach ($arrInput['forum_info'] as $arrItem) {
            $strUnion  = sprintf($strUnionFormat,
                                 intval($arrInput['official_id']), // 官方吧ID
                                 $db->escapeString($arrInput['official_name']), // 官方吧吧名
                                 $db->escapeString($arrInput['official_portrait']), // 官方吧头像
                                 intval($arrItem['forum_id']), // 联盟吧ID
                                 $db->escapeString($arrItem['forum_name']), // 联盟吧吧名
                                 self::UNION_AUDIT, // 申请加入联盟
                                 $intNow
            );
            $strRecord = sprintf($strRecordFormat,
                                 intval($arrInput['official_id']), // 官方吧ID
                                 $db->escapeString($arrInput['official_name']), // 官方吧吧名
                                 intval($arrItem['forum_id']), // 联盟吧ID
                                 $db->escapeString($arrItem['forum_name']), // 联盟吧吧名
                                 intval($arrInput['apply_id']), // 申请者ID
                                 $db->escapeString($arrInput['apply_user']), // 申请者名
                                 $intNow, // 申请时间
                                 $intNow, // 操作时间
                                 intval($arrInput['content']), // 申请内容
                                 0 // 初始状态
            );


            $strUnionSql .= $strUnion . ',';
            $strRecordSql .= $strRecord . ',';

        }

        $strUnionSql  = trim($strUnionSql, ',');
        $strRecordSql = trim($strRecordSql, ',');

        $error = Tieba_Errcode::ERR_SUCCESS;

        // 只有申请加入联盟时，才需要在union表中插入一条记录
        if (self::APPLY_JOIN === intval($arrInput['content'])) {
            $ret = $db->query($strUnionSql);
            if ($ret === false) {
                Bingo_Log::warning("insert db error! sql: " . $db->getLastSQL() . ' affect_num:' . $db->getAffectedRows() . " [output:" . serialize($db->error()) . "]");

                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
        }

        // 插入申请记录
        $ret = $db->query($strRecordSql);
        if ($ret === false) {
            Bingo_Log::warning("insert db error! sql: " . $db->getLastSQL() . ' affect_num:' . $db->getAffectedRows() . " [output:" . serialize($db->error()) . "]");
            $db->rollback();

            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        // 申请解除联盟
        if (self::APPLY_REMOVE === intval($arrInput['content'])) {
            /*$arrParam = array(
                            'from'   => $arrInput['op_id'],
                            'to'     => $arrInput['apply_id'],
                            'content'=> $arrInput['official_name'].'????'.$arrInput['forum_name'].'?????˹?ϵ?Ѿ?????',
                            );
            $ret = Tieba_Service::call('mis','msgSendMsg',$arrParam);
            if (false === $ret || Tieba_Errcode::ERR_SUCCESS !== $ret['errno']){
                Bingo_Log::warning('call mis::msgSendMsg fail with input ['.serialize($arrParam).'] output ['.serialize($ret).']');
                $db->rollback();
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }*/
        }

        // 事务提交
        $ret = $db->commit();

        $arrOutput = array (
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );

        return $arrOutput;
    }

    public static function updateAlliance ($arrInput) {

        //check the tb sig, if no need, just remove the codes below.
        /*
        if(!Tieba_Service::checkTBSig($arrInput)){
        Bingo_Log::warning("check tb sig fail. invalid request.");
        return self::_errRet(Tieba_Errcode::ERR_TBSIG_CHECK_FAIL);
        }
        */

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        $arrCheckParam = array ('id', 'official_id', 'official_name', 'forum_id', 'forum_name', 'content', 'op_type', 'op_user', 'op_id',);
        if (false === self::_checkParam($arrInput, $arrCheckParam)) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //if(!Bingo_Array::in_array($arrInput['op_type'],self::$_arrOpTypeFields)){
        if (!in_array($arrInput['op_type'], self::$_arrOpTypeFields)) {
            Bingo_Log::warning("input params op_type invalid");

            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrRecordUpdate = array (
            'op_user' => $arrInput['op_user'],
            'op_id'   => intval($arrInput['op_id']),
            'op_time' => time(),
        );
        $arrUnionUpdate  = array ();

        /*
        *      状态(content)             操作(op_type)                                  联盟 (is_union)
        *      0：申请加入 1：申请解除   0:审核拒绝 1：审核通过 2：强制解除       0:待审核  1：联盟 2：解除
        */
        switch (intval($arrInput['op_type'])) {
            case self::AUDIT_REFUSE: // 审核拒绝
                $arrRecordUpdate['status'] = 1; // 状态改为已经审核过
                /*
                 *  申请加入联盟：
                 *  审核记录的内容为审核拒绝（APPLY_REF）
                 *  联盟状态为联盟解除
                 * */
                if (self::APPLY_JOIN === intval($arrInput['content'])) {
                    //$arrRecordUpdate['content'] = self::ACCEPT_REMOVE;
                    $arrRecordUpdate['content'] = self::APPLY_REF;
                    $arrUnionUpdate['is_union'] = self::UNION_REMOVE;
                } else {
                    /*
                     * 申请解除联盟
                     * 审核记录的内容为解除拒绝（REMOVE_REF）
                     * 联盟状态保持不变，仍然为结盟
                     * */
                    //$arrRecordUpdate['content'] = self::ACCEPT_JOIN;
                    $arrRecordUpdate['content'] = self::REMOVE_REF;
                }
                //$arrUnionUpdate['is_union'] = $arrInput['content'];
                break;
            /*
             * 审核通过
            */
            case self::AUDIT_PASS: // 审核通过
                $arrRecordUpdate['status'] = 1; // 记录已经审核
                if (self::APPLY_JOIN === intval($arrInput['content'])) {
                    $arrRecordUpdate['content'] = self::ACCEPT_JOIN; // 同意结盟
                    $arrUnionUpdate['is_union'] = self::UNION_JOIN;
                } else {
                    $arrRecordUpdate['content'] = self::ACCEPT_REMOVE; // 同意解除联盟
                    $arrUnionUpdate['is_union'] = self::UNION_REMOVE;
                }
                break;
            /*
             * 强制解除
             * 审核记录内容为同意解除
             *  联盟状态为解除联盟
             *  修改脚本索引和状态（其实解除联盟，粉丝数据不会退，这个改不改没影响）
            * */
            case self::AUDIT_REMOVE: // 强制解除
                $arrRecordUpdate['content']       = self::ACCEPT_REMOVE;
                $arrRecordUpdate['script_status'] = 0; // 更新脚本状态
                $arrRecordUpdate['script_index']  = 0;
                $arrUnionUpdate['is_union']       = self::UNION_REMOVE; // 联盟解除
                break;
            default:
                break;
        }

        $arrRecordConds = array (
            'id=' => $arrInput['id'],
        );
        $arrUnionConds  = array (
            'official_id=' => intval($arrInput['official_id']),
            'forum_id='    => intval($arrInput['forum_id']),
        );
        if (!self::_init()) {
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        $db = self::_getDB();
        if (!$db) {
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        // 开启事务
        $ret = $db->startTransaction();
        if ($ret === false) {
            Bingo_Log::warning("insert db error! sql: " . $db->getLastSQL() . ' affect_num:' . $db->getAffectedRows() . " [output:" . serialize($db->error()) . "]");

            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $ret = $db->update(self::TABLE_RECORD, $arrRecordUpdate, $arrRecordConds, null);
        if ($ret === false) {
            Bingo_Log::warning("update db error! sql: " . $db->getLastSQL() . ' affect_num:' . $db->getAffectedRows() . " [output:" . serialize($db->error()) . "]");

            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        if (!empty($arrUnionUpdate)) {
            $ret = $db->update(self::TABLE_UNION, $arrUnionUpdate, $arrUnionConds, null);
            if ($ret === false) {
                Bingo_Log::warning("update db error! sql: " . $db->getLastSQL() . ' affect_num:' . $db->getAffectedRows() . " [output:" . serialize($db->error()) . "]");
                $db->rollback();

                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
        }
        // 只要不是拒绝，那么就有吧属性操作
		// 提到UI中操作，避免超时
        /*if (self::AUDIT_REFUSE != $arrInput['op_type']) {
            $ret = self::_extProcess($arrInput);
            if (false === $ret) {
                $db->rollback();

                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }*/

        $db->commit();

        $error     = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array (
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );

        return $arrOutput;
    }

    /**
     * @brief
     * @arrInput:
     *    uint32_t schedule_id
     * @return  : $arrOutput
     **/
    public static function delAlliance ($arrInput) {

        //check the tb sig, if no need, just remove the codes below.
        /*
        if(!Tieba_Service::checkTBSig($arrInput)){
            Bingo_Log::warning("check tb sig fail. invalid request.");
            return self::_errRet(Tieba_Errcode::ERR_TBSIG_CHECK_FAIL);
        }
         */
        /*$schedule_id = intval($arrInput['schedule_id']);
        if(!$schedule_id){
            Bingo_Log::warning("delete schedule params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        $db = self::_getDB();
        if (!$db) {
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $strCond = " id = $schedule_id";
        $ret  = $db->delete(self::TABLE_SCHEDULE, $strCond);
        $error = Tieba_Errcode::ERR_SUCCESS;
        if ($ret === false ) {
            Bingo_Log::warning("db delete error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
            return array(
                'errno' => $error,
                'errmsg' => Tieba_Error::getErrmsg($error),
            );
        }
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $arrOutput;
        */
    }

    /**
     * @brief 联盟展示
     * @param official_id : uint32_t : 官方吧ID
     * @param status      : uint32_t :
     **/
    public static function listAlliance ($arrInput) {

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        $arrCheckParam = array ('official_id', 'status',);
        if (false === self::_checkParam($arrInput, $arrCheckParam)) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (isset($arrInput['pn'])) {
            return self::_getAllianceForBawu($arrInput);
        } else {
            return self::_getAllianceForFrs($arrInput);
        }
    }

    public static function checkApplyExists ($arrInput) {
        if (!isset($arrInput['forum_id']) || !isset($arrInput['official_id'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");

            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (!self::_init()) {
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        $db = self::_getDB();
        if (!$db) {
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $arrConds = array (
            'forum_id='    => intval($arrInput['forum_id']),
            'official_id=' => intval($arrInput['official_id']),
            'status='      => 0,
        );
        $ret      = $db->select(self::TABLE_RECORD, self::$_arrRecordFields, $arrConds);
        if (false === $ret) {
            Bingo_Log::warning("update db error! sql: " . $db->getLastSQL() . ' affect_num:' . $db->getAffectedRows() . " [output:" . serialize($db->error()) . "]");

            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $bolExist = count($ret) > 0 ? true : false;

        $error     = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array (
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data'   => $bolExist,
        );

        return $arrOutput;
    }

    /*
    * @brief 检查必要的参数设置
    * @param [in] arrInput : array ：待检测数组
    * @param [in] arrCheckParam : array : 需要检测的字段数组
    * @return true if pass otherwise return false
    */

    public static function listAllianceByForumId ($arrInput) {

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if (!isset($arrInput['forum_ids']) || empty($arrInput['forum_ids'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");

            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (!self::_init()) {
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        $db = self::_getDB();
        if (!$db) {
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        //$strCond = " forum_id in ({$arrInput['forum_ids']}) and is_union=1";
        $strCond = " forum_id in ({$arrInput['forum_ids']})";
        $ret     = $db->select(self::TABLE_UNION, self::$_arrUnionFields, $strCond);
        if (false === $ret) {
            Bingo_Log::warning("update db error! sql: " . $db->getLastSQL() . ' affect_num:' . $db->getAffectedRows() . " [output:" . serialize($db->error()) . "]");

            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        //
        $error     = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array (
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data'   => $ret,
        );

        return $arrOutput;
    }

    public static function getAllianceRecordById ($arrInput) {
        if (!isset($arrInput['id'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");

            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!self::_init()) {
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        $db = self::_getDB();
        if (!$db) {
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $arrConds = array (
            'id=' => intval($arrInput['id']),
        );
        $ret      = $db->select(self::TABLE_RECORD, self::$_arrRecordFields, $arrConds);
        if (false === $ret) {
            Bingo_Log::warning("select db error! sql: " . $db->getLastSQL() . ' affect_num:' . $db->getAffectedRows() . " [output:" . serialize($db->error()) . "]");

            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        //
        $error     = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array (
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data'   => $ret,
        );

        return $arrOutput;

    }

    /*
    * @brief          更新联盟数据
    *
    * @param ids       : string    : [option] : 更新的ID
    * @param forum_ids : string    : [option] : 更新的联盟吧信息
    * @param op_type   : uint32_t  : [must]   : 0:拒绝  1：同意 2：强制解除
    * @official_id     : uint32_t  : [must]   : 官方吧ID
    * @official_name   : string    : [must]   : 官方吧吧名
    * @forum_id        : uint32_t  : [must]   : 联盟ID
    * @forum_name      : string    : [must]	  : 联盟吧名
    * @param op_user   : string    : [must]   : 操作者
    * @param op_id     : uint32_t  : [must]   : 操作者ID
    * @param apply_user: string    : [must]   : 申请人
    * @param apply_id  : uint32_t  : [must]   : 申请人ID
    * @NOTE
    */

    protected static function _checkParam ($arrInput, $arrCheckParam) {
        if (is_array($arrCheckParam) && count($arrCheckParam)) {
            foreach ($arrCheckParam as $strKey) {
                if (!isset($arrInput[$strKey])) {
                    Bingo_Log::warning('input param invalid .[' . serialize($arrInput) . ']' . ' missing ' . $strKey);

                    return false;
                }
            }
        }

        return true;
    }

    /*
   * @desc 设置吧属性
   */

    protected static function _extProcess ($arrInput) {
        // 审核通过的话
        if (self::AUDIT_PASS == $arrInput['op_type']) {
            if (isset($arrInput['content']) && (intval($arrInput['content']) === 1)) {
                $ret = self::_delForumAttr(array ($arrInput['forum_id'],));
                if (false === $ret) {
                    return false;
                }
                $arrInput['op_type'] = 2;
                $ret                 = Tieba_Service::call('official', 'updateAssociateByRelate', $arrInput);
                if (false === $ret || Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
                    return false;
                }

            } else {
                $arrForum = array ($arrInput['forum_id'],);
                $ret      = self::_setForumAttr($arrInput['official_id'], $arrInput['official_name'], $arrForum);
                if (false === $ret || Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
                    return false;
                }

                $ret = self::_setForumSwitch($arrInput['official_id'], $arrInput['op_user'], 1);
                if (false === $ret) {
                    return false;
                }

                $ret = Tieba_Service::call('official', 'updateAssociateByRelate', $arrInput);
                if (false === $ret || Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
                    return false;
                }
            }
        } else {
            // 审核拒绝
            $ret = self::_delForumAttr(array ($arrInput['forum_id'],));
            if (false === $ret) {
                return false;
            }

            $ret = Tieba_Service::call('official', 'updateAssociateByRelate', $arrInput);
            if (false === $ret || Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
                return false;
            }
        }

        return true;
    }

    /*
    * @brief 删除吧属性
    */

    protected static function _getAllianceForFrs ($arrInput) {
        if (!self::_init()) {
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $db = self::_getDB();
        if (!$db) {
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $arrConds  = array (
            'official_id=' => intval($arrInput['official_id']),
            'is_union='    => 1,
        );
        $strAppend = 'order by apply_time desc limit 0,' . self::ALLIANCE_NUM;
        //$strAppend = 'limit 0,'.self::ALLIANCE_NUM;
        $ret = $db->select(self::TABLE_UNION, self::$_arrUnionFields, $arrConds, null, $strAppend);
        if (false === $ret) {
            Bingo_Log::warning("select db error! sql: " . $db->getLastSQL() . ' affect_num:' . $db->getAffectedRows() . " [output:" . serialize($db->error()) . "]");

            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;

        $arrOutput = array (
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data'   => array (
                'list' => $ret,
            ),
        );

        return $arrOutput;
    }

    /*
    *@brief 设置official开关
    *          type = 1 : 明星官方
    *          type = 2 : 企业官方
    */

    protected static function _getAllianceForBawu ($arrInput) {
        $intPn     = intval($arrInput['pn']);
        $intSz     = intval($arrInput['sz']);
        $intStatus = intval($arrInput['status']);

        if ($intPn <= 0) {
            $intPn = 1;
        }
        if ($intSz <= 0) {
            $intSz = self::DEFAULT_PAGE_SIZE;
        }
        $intStart = ($intPn - 1) * $intSz;
        //$strAppend = "order by apply_time  limit $intStart,$intSz";
        $arrConds = array (
            'official_id=' => intval($arrInput['official_id']),
            'status='      => $intStatus,
        );
        if (!$intStatus) {
            $strAppend = "order by apply_time  limit $intStart,$intSz";
        } else {
            $strAppend = "order by apply_time desc  limit $intStart,$intSz";
        }

        if (!self::_init()) {
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        $db = self::_getDB();
        if (!$db) {
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $ret = $db->select(self::TABLE_RECORD, self::$_arrRecordFields, $arrConds, null, $strAppend);

        if (false === $ret) {
            Bingo_Log::warning("select db error! sql: " . $db->getLastSQL() . ' affect_num:' . $db->getAffectedRows() . " [output:" . serialize($db->error()) . "]");

            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRet = $db->select(self::TABLE_RECORD, 'count(id) as count', $arrConds);
        if (false === $arrRet) {
            Bingo_Log::warning("select db error! sql: " . $db->getLastSQL() . ' affect_num:' . $db->getAffectedRows() . " [output:" . serialize($db->error()) . "]");

            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $intTotal = $arrRet[0]['count'];

        $error     = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array (
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data'   => array (
                'list'  => $ret,
                'total' => $intTotal,
            ),
        );

        return $arrOutput;

    }

    /*
    * @brief
    *   后续操作
    */

    /**
     * @brief get mysql obj.
     * @return: obj of Bd_DB, or null if connect fail.
     **/
    private static function _getDB () {
        $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if ($objTbMysql && $objTbMysql->isConnected()) {
            $objTbMysql->charset('utf8');

            return $objTbMysql;
        } else {
            Bingo_Log::warning("db connect fail.");

            return null;
        }
    }

    /**
     * @brief get cache obj.
     * @return: obj of Bingo_Cache_Memcached, or null if connect fail.
     **/
    private static function _getCache () {
        if (self::$_cache) {
            return self::$_cache;
        }
        Bingo_Timer::start('memcached_init');
        self::$_cache = new Bingo_Cache_Memcached(self::MEMCACHED_NAME);
        Bingo_Timer::end('memcached_init');

        if (!self::$_cache || !self::$_cache->isEnable()) {
            Bingo_Log::warning("init cache fail.");
            self::$_cache = null;

            return null;
        }

        return self::$_cache;
    }

    /*
    * @brief ??ȡFRS??????????
    */

    /**
     * @brief get redis obj.
     * @return: obj of Bingo_Cache_Redis, or null if connect fail.
     **/
    private static function _getRedis () {
        if (self::$_redis) {
            return self::$_redis;
        }
        Bingo_Timer::start('redis_init');
        self::$_redis = new Bingo_Cache_Redis(self::REDIS_NAME);
        Bingo_Timer::end('redis_init');

        if (!self::$_redis || !self::$_redis->isEnable()) {
            Bingo_Log::warning("init redis fail.");
            self::$_redis = null;

            return null;
        }

        return self::$_redis;
    }

    /*
    */

    /**
     * @brief init
     * @return: true if success. false if fail.
     **/
    private static function _init () {

        //add init code here. init will be called at every public function beginning.
        //not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
        //init should be recalled for many times.

        if (self::$_conf == null) {
            self::$_conf = Bd_Conf::getConf("/app/star/service_schedule_schedule");
            if (self::$_conf == false) {
                Bingo_Log::warning("init get conf fail.");

                return false;
            }

        }

        return true;
    }

    private static function _errRet ($errno) {
        return array (
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /*
     * @brief 检测联盟吧是否在官方吧中申请过
     * @param forum_id    : uint32_t : 联盟吧ID
     * @param official_id : uint32_t : 官方吧ID
     * */

    protected function _setForumAttr ($intOfficialId, $strOfficialName, $arrForumIds) {
        if (empty($arrForumIds) || empty($intOfficialId)) {
            return true;
        }

        $arrInput = array ();
        foreach ($arrForumIds as $intForumId) {
            $arrNew     = array (
                'forum_id'   => $intForumId,
                'attr_name'  => self::FORUM_ATTR_NAME,
                'attr_value' => array (
                    'forum_id'          => $intOfficialId,
                    'forum_name'        => $strOfficialName,
                    'type'              => self::TYPE_STAR,
                    'associate_content' => array (
                        'star_fans'     => true,
                        'star_news'     => true,
                        'star_activity' => true,
                        'star_daily'    => true,
                    ),
                ),
            );
            $arrInput[] = $arrNew;
        }

        $arrRet = Tieba_Service::call('forum', 'msetForumAttr', array ('input' => $arrInput));
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning('call forum::msetForumAttr fail with input ' . serialize($arrInput) . '
            and output ' . serialize($arrRet));

            //return Util_Star_Common::jsonOutput(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return $this->_errorRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /*
    * @brief 查看给定的吧有加入了多少联盟
    * @param forum_ids : array : 给定的吧ID
    */

    protected function _delForumAttr ($arrForumIds) {
        if (empty($arrForumIds)) {
            return true;
        }

        $objRalMulti = new Tieba_Multi('associate_forum');
        foreach ($arrForumIds as $intForumId) {
            $arrParam      = array (
                'forum_id'  => intval($intForumId),
                'attr_name' => self::FORUM_ATTR_NAME,
            );
            $arrMultiInput = array (
                'serviceName' => 'forum',
                'method'      => 'delForumAttr',
                'input'       => $arrParam,
            );
            $objRalMulti->register($intForumId, new Tieba_Service('forum'), $arrMultiInput);
        }
        $objRalMulti->call();
        $arrRet = $objRalMulti->results;
        if (false === $arrRet) {
            Bingo_Log::warning('call forum::delForumAttr fail');

            return false;
        }

        foreach ($arrRet as $value) {
            if (false === $value || Tieba_Errcode::ERR_SUCCESS !== $value['errno']) {
                return false;
            }
        }

        return true;
    }

    protected function _setForumSwitch ($intOfficialId, $strUserName, $type = 1) {
        $arrInput = array (
            'forum_id'    => $intOfficialId,
            'switch_name' => self::SWITCH_NAME,
            'op_uname'    => $strUserName,
        );
        if (1 == $type) {
            $arrRet = Tieba_Service::call('official', 'addOfficialSwitch', $arrInput);
        } else if (2 == $type) {
            $arrRet = Tieba_Service::call('official', 'delOfficialSwitch', $arrInput);
        }

        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call official::***OfficialSwitch fail with input [' . serialize($arrInput) . '] output [' . serialize($arrRet) . ']');

            return false;
        }

        return true;
    }
}
