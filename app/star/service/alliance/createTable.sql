USE forum_star;
CREATE TABLE star_union(
`id`  INT(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
`official_id` INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '官方吧id',
`official_name` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '官方吧吧名',
`official_portrait` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '官方吧头像',
`forum_id`  INT(10) UNSIGNED  NOT NULL DEFAULT 0 COMMENT '联盟吧吧ID',
`forum_name` VARCHAR(64)  NOT NULL DEFAULT '' COMMENT '联盟吧吧名',
`is_union`      TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0:没加入  1：加入',
`reserved1`     INT(64) UNSIGNED NOT NULL DEFAULT 0 COMMENT '保留1',
`reserved2`     VARCHAR(128) NOT NULL DEFAULT '' COMMENT '保留2',
PRIMARY KEY (`ID`),
UNIQUE idx_official (`official_id`,`forum_id`)
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT '明星官方吧明显联盟数据表';

CREATE TABLE union_record(
`id`  INT(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
`official_id` INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '官方吧id',
`official_name` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '官方吧吧名',
`forum_id`  INT(10) UNSIGNED  NOT NULL DEFAULT 0 COMMENT '联盟吧吧ID',
`forum_name` VARCHAR(64)  NOT NULL DEFAULT '' COMMENT '联盟吧吧名',
`apply_user`   VARCHAR(64) NOT NULL DEFAULT '' COMMENT '申请用户名',
`apply_id`     INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '申请用户ID',
`apply_time`   INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '申请时间，时间戳',
`op_user`       VARCHAR(64) NOT NULL DEFAULT '' COMMENT '审核用户名',
`op_id`         INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核用户ID',
`op_time`       INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核时间，时间戳',
`content`       TINYINT NOT NULL DEFAULT 0 COMMENT '内容 0：申请加入 1：申请退出',
`status`        TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0:待审核 1:审核过',
`script_status` TINYINT NOT NULL DEFAULT 0 COMMENT '脚本运行状态：0:没跑脚本 1:跑脚本中 2：已经处理完',
`script_index`  INT(10) NOT NULL DEFAULT 0 COMMENT '脚本执行时的perm表的ID',
`script_time`   INT(10) NOT NULL DEFAULT 0 COMMENT '脚本执行时的perm表的ID',
`reserved1`     INT(64) UNSIGNED NOT NULL DEFAULT 0 COMMENT '保留1',
`reserved2`     VARCHAR(128) NOT NULL DEFAULT '' COMMENT '保留2',
PRIMARY KEY (`ID`)
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT '明星官方吧明显联盟数据表';

