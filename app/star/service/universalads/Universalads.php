<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date      2015:01:28 16:03:48
 * @version
 * @desc
 *   打造通用的明星广告投递
 * @structs & methods(copied from idl.)
 */


define("MODULE", "Star_service");

class Service_Universalads_Universalads {
    const SERVICE_NAME              = "Service_UniversalAds_UniversalAds";
    const DATABASE_NAME             = "forum_frsstar";
    const CROWDFUNDING_TABLE        = 'crowdfunding';
    const MEMCACHED_NAME            = "forum_common";
    const REDIS_NAME                = "forum_star";

    protected static $_conf         = null;
    protected static $_use_split_db = false;
    protected static $_cache        = null;
    protected static $_redis        = null;
    protected static $_intTime      = 0;

    /*
     * @brief get mysql obj.
     * @return: obj of Bd_DB, or null if connect fail.

    */
    private static function _getDB () {
        $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if ($objTbMysql && $objTbMysql->isConnected()) {
            $objTbMysql->charset('utf8');

            return $objTbMysql;
        } else {
            Bingo_Log::warning("db connect fail.");

            return null;
        }
    }

    /*
     * @brief get cache obj.
     * @return: obj of Bingo_Cache_Memcached, or null if connect fail.

    */
    private static function _getCache () {
        if (self::$_cache) {
            return self::$_cache;
        }
        Bingo_Timer::start('memcached_init');
        self::$_cache = new Bingo_Cache_Memcached(self::MEMCACHED_NAME);
        Bingo_Timer::end('memcached_init');

        if (!self::$_cache || !self::$_cache->isEnable()) {
            Bingo_Log::warning("init cache fail.");
            self::$_cache = null;

            return null;
        }

        return self::$_cache;
    }

    /*
     * @brief get redis obj.
     * @return: obj of Bingo_Cache_Redis, or null if connect fail.
     */
    private static function _getRedis () {
        if (self::$_redis) {
            return self::$_redis;
        }
        Bingo_Timer::start('redis_init');
        self::$_redis = new Bingo_Cache_Redis(self::REDIS_NAME);
        Bingo_Timer::end('redis_init');

        if (!self::$_redis || !self::$_redis->isEnable()) {
            Bingo_Log::warning("init redis fail.");
            self::$_redis = null;

            return null;
        }

        return self::$_redis;
    }


    /*
     * @brief init
     * @return: true if success. false if fail.
     */
    private static function _init () {
        if(self::$_conf == null){
            self::$_conf = Bd_Conf::getConf("/app/star/service_universalads_universalads");
            if(self::$_conf == false){
                Bingo_Log::warning("init get conf fail.");
                return false;
            }

        }

        self::$_intTime = time();
        return true;
    }


    private static function _errRet ($errno, $data = array()) {
        return array (
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data'   => $data,
        );
    }

    public static function preCall ($arrInput) {
        // pre-call hook
    }

    public static function postCall ($arrInput) {
        // post-call hook
    }

    /*
     * @desc 增加计数
     * @param [in] forum_id : uint32_t : 吧ID
     * @param [in] num      : uint32_t : 增加计数的个数
     */
    public static function updateNumByForumId ($arrInput) {
        if (false === self::_init()) {
            return self::_errRet(Tieba_Errcode::ERR_INIT);
        }

        $arrNeedCheck = array('forum_id', 'num');
        if (false === Service_Libs_Tools::checkMustParam($arrInput, $arrNeedCheck)) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $intForumId = (int)($arrInput['forum_id']);
        $intNum     = (int)($arrInput['num']);

        if ($intForumId <= 0 ){
            Bingo_Log::warning('forum_id invalid with input ' . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (0 === $intNum) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $strSql =  sprintf('update %s set oil_num=oil_num + %d where forum_id=%d and status=2', self::CROWDFUNDING_TABLE, $intNum, $intForumId);
        $arrParam = array(
            'database' => self::DATABASE_NAME,
            'sql'      => $strSql,
        );
        $arrRet = Service_Libs_DB::query($arrParam);
        if (false === $arrRet) {
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /*
     * @desc 给定吧ID，返回对应的数据
     * @param [in] forum_id   : uint32_t : 吧ID
     * @param [in] status     : uint32_t : 是否在线
     */
    public static function getCurrentAdsStatusByForumId($arrInput) {
        if (false === self::_init()) {
            return self::_errRet(Tieba_Errcode::ERR_INIT);
        }

        $arrNeedCheck = array('forum_id',);
        if (false === Service_Libs_Tools::checkMustParam($arrInput, $arrNeedCheck)) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if ($arrInput['forum_id'] <= 0) {
            $strMsg = sprintf('param error. with input [%s]', serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrInput['status'] = isset($arrInput['status']) ? $arrInput['status'] : self::STATUS_ONLINE;
        $arrFields = self::_getCrowdfundingFields();
        $arrConds  = array(
            'forum_id' => (int)($arrInput['forum_id']),
            'status'   => (int)($arrInput['status']),
        );
        $arrParam  = array(
            'database' => self::DATABASE_NAME,
            'table'    => self::CROWDFUNDING_TABLE,
            'fields'   => $arrFields,
            'conds'    => $arrConds,
        );
        $arrRet = Service_Libs_DB::select($arrParam);
        if (false === $arrRet) {
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

    /*
     * @desc 拉取展现的广告数据
     * @param [in] forum_id : uint32_t : 吧ID
     * @param [in] type     : uint32_t : 类型
     * @param [in] sub_type : uint32_t : 子类型
     */
    public static function getAdsByForumId($arrInput) {
        if (false === self::_init()) {
            return self::_errRet(Tieba_Errcode::ERR_INIT);
        }

        $arrNeedCheck = array('forum_id', 'type', 'sub_type',);
        if (false === Service_Libs_Tools::checkMustParam($arrInput, $arrNeedCheck) || $arrInput['forum_id'] <= 0) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrRet = Service_Universalads_Display_Stragery::getAdsByForumId($arrInput);
        if (false === $arrRet) {
            $strMsg = sprintf('getAdsByForum fail with input [%s]', serialize($arrInput));
            Bingo_Log::warning($strMsg);
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

    /*
     * @desc 更新用户的计数
     * @param [in] forum_id  : uint32_t : 吧ID
     * @param [in] user_info : array    : 需要增加的信息
     *             user_name : string   : 用户名
     *             add_value : uint32_t : 增加的ID
     *             type      : uint32_t : 类别
     */
    public static function incrUserCount($arrInput) {
        if (false === self::_init()) {
            return self::_errRet(Tieba_Errcode::ERR_INIT);
        }

        $arrNeedCheck = array('forum_id', 'user_info');
        if (false === Service_Libs_Tools::checkMustParam($arrInput, $arrNeedCheck) || $arrInput['forum_id'] <= 0) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (empty($arrInput['user_info'])) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $arrContents = array();
        foreach ($arrInput['user_info'] as $arrUser) {
            $intType = (int)($arrUser['type']);
            if (!isset(self::$_conf['type'][$intType])) {
                Bingo_Log::warning('invalid type ' . $intType);
                continue;
            }
            $arrContents[$intType][] = $arrUser;
        }

        $arrParam = array();
        foreach ($arrContents as $intType => $arrItem) {
            $strKey = self::_genKey($intType, $arrInput['forum_id']);
            foreach ($arrItem as $arrUser) {
                $arrNew = array(
                    'key'    => $strKey,
                    'member' => $arrUser['user_name'],
                    'step'   => (int)($arrUser['add_value']),
                );
                $arrParam[] = $arrNew;
            }
        }

        if (empty($arrParam)) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, 'success');
        }

        $arrRet = Service_Libs_Redis::mZINCRBY($arrParam);
        if (false === $arrRet) {
            $strMsg = sprintf('add redis fail with input [%s]', serialize($arrParam));

            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /*
     * @desc 拉取本活动存放的数据
     * @param [in] forum_id  : uint32_t : 吧ID
     * @param [in] input_info: array    : 拉取数据信息
     *             type      : uint32_t : 类别
     *             num       : uint32_t : 数目
     */
    public static function getUserCount($arrInput) {
        if (false === self::_init()) {
            return self::_errRet(Tieba_Errcode::ERR_INIT);
        }

        $arrNeedCheck = array('forum_id', 'input_info');
        if (false === Service_Libs_Tools::checkMustParam($arrInput, $arrNeedCheck)) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if ($arrInput['forum_id'] <= 0 || empty($arrInput['input_info'])) {
            $strMsg = sprintf('param error with input [%s]', serialize($arrInput));
            Bingo_Log::warning($strMsg);
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrParam = array();
        foreach ($arrInput['input_info'] as $arrItem) {
            $intType = (int)($arrItem['type']);
            if (!isset(self::$_conf['type'][$intType])) {
                Bingo_Log::warning('invalid type ' . $intType);
                continue;
            }
            $strKey = self::_genKey($intType, $arrInput['forum_id']);
            $intNum = (int)($arrItem['num']);
            if ($intNum <= 0) {
                continue;
            }
            $intOffset = $intNum - 1;
            $arrParam[] = array(
                'key' => $strKey,
                'start' => 0,
                'stop'  => $intOffset,
            );
        }

        $arrRet = Service_Libs_Redis::mZREVRANGEWITHSCORES($arrParam);
        if (false === $arrRet) {
            $strMsg = sprintf('add redis fail with input [%s]', serialize($arrParam));

            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrOut = array();
        foreach ($arrInput['input_info'] as $arrItem) {
           $intType = (int)($arrItem['type']);
            if (!isset(self::$_conf['type'][$intType])) {
                Bingo_Log::warning('invalid type ' . $intType);
                continue;
            }
            $strKey = self::_genKey($intType, $arrInput['forum_id']);
            if (isset($arrRet[$strKey])) {
                $arrOut[$intType] = $arrRet[$strKey];
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOut);
    }


    /*
     * @desc 根据业务类型，产生不同的key
     */
    protected static function _genKey($intType, $intForumId) {
        $strPrefix = strtoupper(self::$_conf['type'][$intType]['content']);
        $strKey = sprintf('STAR_ADS_%d_%s', $intForumId, $strPrefix);

        return $strKey;
    }

    protected static function _getCrowdfundingFields() {
        $arrFields = array('id', 'title', 'desc_text', 'url', 'forum_id', 'forum_name', 'start_time', 'end_time', 'travel_time', 'status', 'background_pic', 'bus_num', 'bus_pic', 'oil_num', 'discount_rate', 'reason', 'script_status', 'create_time', 'create_uname', 'audit_time', 'audit_uname', 'update_time', 'update_uname', 'extra');
        return $arrFields;
    }
}
