<?php
/**
 * Author: ji<PERSON><PERSON><PERSON>
 * Date: 14-3-31
 * Desc: 
 */

class Data_Perm {
    public static function getPerm($forum_id,$user_id,$user_ip){
        $arrInput = array(
            'forum_id' => $forum_id,
            'user_id' => $user_id,
            //'user_ip' => $user_ip
        );
        $arrInput = Util_Function::addParamUserIpV4OrV6($arrInput, $user_ip);
        $arrOut = Tieba_Service::call('perm','getPerm',$arrInput);
        if($arrOut == false || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']){
            Bingo_Log::warning('call perm[getPerm] fail,the input is[' . serialize($arrInput) .'],the out is[' . serialize($arrOut).']');
            return false;
        }
        return $arrOut['output'];
    }
} 