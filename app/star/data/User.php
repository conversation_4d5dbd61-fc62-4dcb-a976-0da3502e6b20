<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-03-24 16:03:40
 * @version
 */
class Data_User {

		
	/*
	* @brief : 用户id转用户名（批量）
	* @param [in] user_id[] : string : 用户id
	* @param [out] output : get_uname_by_uids_output : 用户名
	*/
	public function getUnameByUids($arrInput) {
		$arrOutput = Tieba_Service::call('user', 'getUnameByUids', $arrInput);
		if(false === $arrOutput) {
			$strLog = "call user getUnameByUids false. input=".serialize($arrInput).
				" output=".serialize($arrOutput);
			Bingo_Log::warning($strLog);
			return false;
		}
		if($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			$strLog = "call user getUnameByUids errno!=0. input=".serialize($arrInput).
				" output=".serialize($arrOutput);
			Bingo_Log::warning($strLog);
			return false;
		}
		return $arrOutput;
	}	
	/*
	* @brief : 获取用户基本信息，具体返回内容见上文详细说明
	* @param [in] user_id[] : string : 用户id
	* @param [in] user_id : uint32_t : 用户id
	* @param [out] output : get_uname_by_uids_output : 用户名
	* @param [out] user_info : user_data_info : 用户基本信息
	*/
	public function getUserData($arrInput) {
		$arrOutput = Tieba_Service::call('user', 'getUserData', $arrInput);
		if(false === $arrOutput) {
			$strLog = "call user getUserData false. input=".serialize($arrInput).
				" output=".serialize($arrOutput);
			Bingo_Log::warning($strLog);
			return false;
		}
		if($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			$strLog = "call user getUserData errno!=0. input=".serialize($arrInput).
				" output=".serialize($arrOutput);
			Bingo_Log::warning($strLog);
			return false;
		}
		return $arrOutput;
	}
}