<?php

define("MY_SCRIPT_NAME", 'clearceleinfo');
require_once('base.php');
class CeleInfo {
    const TABLE_NAME    = 'npc_vote';
    const DATABASE_NAME = "forum_celebrity";
    const DB_NAME       = '';

    private static function _getDb() {
        $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if($objTbMysql && $objTbMysql->isConnected()) {
            return $objTbMysql;
        } else {
            Bingo_Log::warning("db connect fail.");
            return null;
        }
    }

    /**
     * @brief clear the cele info by type
     *
     * @param $type
     *
     * @return null
     */
    public static function clearCeleInfo($type) {
        $db = self::_getDb();
        $res = $db->query("select * from npc_vote where type = 1");
        //$sql = sprintf("DELETE FROM %s WHERE type = %d", self::TABLE_NAME, $type);
        //$res = $db->query($sql);
        var_dump($res);
    }

}

CeleInfo::clearCeleInfo(1);

?>
