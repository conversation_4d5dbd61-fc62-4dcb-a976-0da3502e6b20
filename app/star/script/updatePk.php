<?php

define("MY_SCRIPT_NAME", 'updatepk');
require_once('base.php');

/**
    * @brief update the pk value by day
 */
class Pk {
    const HEADER_TYPE_FORUM     = 1;
    const STATUS_DEFAULT        = 0;
    const DATABASE_NAME         = "forum_activity_center";
    const TABLE_NAME            = "header_info";
    const PAGENUM_DEFAULT       = 50;
	const RANK_NAME 			= "fan_festival";

    private static function _getDB () {
        $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if($objTbMysql && $objTbMysql->isConnected()) {
            return $objTbMysql;
        } else {
            throw new Exception('db connect failed!');
        }
    }

    public static function getTotalCount() {
        $db = self::_getDB();
        $sql = sprintf("SELECT count(head_id) as cnt FROM %s WHERE related_act_type = %d AND status = %d", self::TABLE_NAME, self::HEADER_TYPE_FORUM, self::STATUS_DEFAULT);
        $res = $db->query($sql);
        if (false === $res) {
            Bingo_Log::warning("fail to getTotalCount");
            return -1;
        }
        $intCount = (int)$res[0]['cnt'];
        return $intCount;
    }

    public static function getAllActForum() {
        $intCount = (int)self::getTotalCount();
        $arrRet = array();
        if (0 >= $intCount) {
            throw new Exception("fail to getAllActForum, or total count is 0");
        }
        $db = self::_getDB();
        for($i=0; $i<=$intCount; $i+=self::PAGENUM_DEFAULT) {
            $sql = sprintf("SELECT related_act_id AS forum_id, head_id FROM %s WHERE related_act_type = %s LIMIT %d, %d", self::TABLE_NAME, self::HEADER_TYPE_FORUM, $i, self::PAGENUM_DEFAULT);
            $res = $db->query($sql);
            if (false === $res) {
                //pass
                continue;
            }
            foreach($res as $info) {
                $arrRet [] = $info['forum_id'];
            }
        }
        return $arrRet;
    }

    public static function main() {
        try {
            $arrFids = self::getAllActForum();

            foreach($arrFids as $fid) {
                if (0 >= $fid) {
                    //dirty data
                    continue;
                }
                $arrInput = array(
                    'req' => array(
                        'forum_id' => $fid,
                    ),
                );
                $count_info = Util::call('star', 'getCountByFid', $arrInput);
                if (false === $count_info) {
                    // process
					Bingo_Log::warning("fail to call getCountByFid");
					continue;
				}
				$arrInput = array(
					'req' => array(
						'rank_name' => self::RANK_NAME,
						'member' 	=> $fid,
					),
				);
				$score_info = Util::call('generalmessage', 'getScore', $arrInput);
				if (false === $score_info) {
					Bingo_Log::warning("fail to getScore");
					continue;
				}
				$today 		= (double)$score_info['data'][0];
                $yesterday  = (double)$count_info['data']['pk_yesterday'];
				if (is_null($yesterday)) {
					$yesterday = 0.0;
                }

                $tmp_msg = "forum[$fid] -> today[$today], yesterday[$yesterday]";
                Bingo_Log::notice($tmp_msg);
                var_dump($tmp_msg);

                $arrInput = array(
                    'req' => array(
                        'forum_id'  => $fid,
                        'field'     => 'pk_beforeyesterday', 
                        'value'     => $yesterday,
                    ),
                );
                $arrOutput = Util::call('star', 'setCountByFid', $arrInput);
                if (false === $arrOutput) {
                    Bingo_Log::warning("fail to setCountByFid -> pk_beforeyesterday, input[$fid]");
                    continue;
                }
                
                $arrInput = array(
                    'req' => array(
                        'forum_id'  => $fid,
                        'field'     => 'pk_yesterday', 
                        'value'     => $today,
                    ),
                );
                $arrOutput = Util::call('star', 'setCountByFid', $arrInput);
                if (false === $arrOutput) {
                    Bingo_Log::warning("fail to setCountByFid -> pk_yesterday, input[$fid]");
                    continue;
                }
            }
            return true;
        } catch (Exception $e) {
            $errmsg = $e->getMessage();
            Bingo_Log::warning($errmsg);
            var_dump($errmsg);
            return false;
        }
    }
}

Pk::main();
Bingo_Log::notice("success");
var_dump("success");
exit(0);
