<?php

/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/8/11
 * Time: 10:28
 */
class MakeSignData
{
    const SIGN_ADD_RECORD = "sign_add_record_";//记录每天签到数的文件
    const FAIL_ADD_RECORD = "fail_add_record_";//记录失败重试之后的数据
    const BACK_UP_ADD = "add_back_up_";//备份当前的累计签到总值
    const REDIS_FORUM_RANK_DAILY_PREFIX = 'dd_';
    const REDIS_NAME = "sign";
    const REDIS_KEY_DELIMITER = '_';
    const REDIS_FORUM_RANK_MONTH_PREFIX = 'dm_';
    const CHEAT_TABLE_PREFIX = "sign_monitor";
    const SIGN_NUM_MEM_NUM_MOD = 1000000000;
    const RANK_FORUM = 10000;
    const PAGE_NUM = 100;
    const MAX_RETRY = 10;
    const REDIS_PID = 'commonb';
    const REDIS_ADD_PRE = 'fbd_star_add_';
    const REDIS_CHEAT_FAIL_PRE = 'fbd_star_add_fail_';
    const STAR_DIR = '娱乐明星';
    const MAX_NUM = 500;
    const BACK_UP_CHEAT = 'cheat_back_up_';
    const STAR_REDIS = 'star';
    const STAR_REDIS_PRE = 'rank_go_';

    /**
     * @return Bingo_Cache_Redis|null
     */
    private static function _getRedisObj() {
        $redis = null;
        for ($i = 0; $i < self::MAX_RETRY; $i++) {
            $redis = new Bingo_Cache_Redis(self::REDIS_PID);
            if (!$redis || !$redis->isEnable()) {
                echo("init redis fail.\n");
                $redis = null;
                $time = $i * 30;
                sleep($time);
            } else {
                return $redis;
            }
        }
        return $redis;
    }

    /**
     * @brief 获取报名粉丝节的吧id
     * @return mixed
     */
    private static function _getRankForum() {
        $arrTemp = array();
        $arrInput = array(
            'req' => array(
                'rank_name' => 'fan_festival',
                'start' => 0,
                'stop' => self::RANK_FORUM,
            ),

        );
        $strService = 'generalmessage';
        $strMethod = 'getRank';
        $arrRes = Tieba_Service::call('generalmessage', 'getRank', $arrInput);
        if ($arrRes === false || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            echo "call the service[" . $strService . "] method=[" . $strMethod . "] failed input =[ " . serialize($arrInput) . "] and out[" . serialize($arrRes) . "]\n";
            $arrRes = self::retryService($strService, $strMethod, $arrInput);
            if ($arrRes === false) {
                return false;
            }
        }
        if (!empty($arrRes['data'])) {
            foreach ($arrRes['data'] as $arrInfo) {
                $arrTemp[] = intval($arrInfo[0]);
            }
        }
        return $arrTemp;
    }

    /**
     * @param $arrFestivalForumIds
     * @param $flag
     * @return bool
     */
    public static function process($arrFestivalForumIds, $flag) {
        if (!is_array($arrFestivalForumIds)) {
            $arrFestivalForumIds = (array)$arrFestivalForumIds;
        }
        $strKey = self::FAIL_ADD_RECORD . date("Ymd", time());
        if ($flag == 0) {
            $strFile = self::SIGN_ADD_RECORD . time();
            $fp = fopen($strFile, "w");
        } else {
            $strFile = self::FAIL_ADD_RECORD . date("Ymd", time());
            $fp = fopen($strFile, "a");
        }

        foreach ($arrFestivalForumIds as $intTempForumId) {
            $arrInput = array(
                'req' => array(
                    'forum_id' => $intTempForumId,
                ),
            );
            $arrRes = Tieba_Service::call('star', 'getCountByFid', $arrInput);
            if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
                $arrRes = self::retryService('star', 'getCountByFid', $arrInput);
                if (false === $arrRes) {
                    while (!self::addListKey($strKey, $intTempForumId)) {

                    }
                    continue;
                }
            }
            $intPreCountSign = intval($arrRes['data']['pre_sign']);
            $intAddSignCount = intval($arrRes['data']['today_sign']);
            $intCurCountSign = $intAddSignCount + $intPreCountSign;
            if ($intCurCountSign <= $intPreCountSign) {
                continue;
            } else {
                //回写当前签到数目和当前数据清零
                $arrInputParam = array(
                    'key' => self::STAR_REDIS_PRE . $intTempForumId,
                    'fields' => array(
                        array(
                            'field' => 'pre_sign',
                            'value' => $intCurCountSign,
                        ),
                        array(
                            'field' => 'today_sign',
                            'value' => 0,
                        ),
                    ),

                );
                $arrRedisInput = array(
                    'redis_name' => self::STAR_REDIS,
                    'redis_method' => 'hmset',
                    'redis_param' => $arrInputParam,
                );
                $arrRes = Tieba_Service::call('starshow', 'callRedis', $arrRedisInput);
                if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
                    $arrRes = self::retryService('starshow', 'callRedis', $arrRedisInput);
                    if (false === $arrRes) {
                        while (!self::addListKey($strKey, $intTempForumId)) {

                        }
                        continue;
                    }
                }
            }
            //记录数据
            fwrite($fp, $intTempForumId . "\t" . $intPreCountSign . "\t" . $intAddSignCount . "\t" . $intCurCountSign . "\n");
        }
        fclose($fp);
        return true;
    }

    /**
     * @param $key
     * @param $value
     * @return bool
     */
    public static function addListKey($key, $value) {
        $arrInput = array(
            'key' => $key,
            'value' => $value,
        );
        $redis = self::_getRedisObj();
        if ($redis) {
            $arrRes = $redis->RPUSH($arrInput);
            if ($arrRes === false || $arrRes['err_no'] != 0) {
                echo "add the key=[" . $key . "] and value=[" . $value . "]\n";
                $arrRes = self::retryRedis("RPUSH", $arrInput, 1);
                if (false === $arrRes) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    /**
     * @param $key
     * @return bool
     */
    public static function removeListKey($key) {
        $arrInput = array(
            'key' => $key,
        );
        $redis = self::_getRedisObj();
        if ($redis) {
            $arrRes = $redis->LPOP($arrInput);
            if ($arrRes === false || $arrRes['err_no'] != 0) {
                return false;
            } else {
                return $arrRes;
            }
        }
        return false;
    }

    /**
     * @param $key
     * @param $start
     * @param $end
     * @return bool
     */
    public static function getListDataByRange($key, $start, $end) {
        $arrInput = array(
            'key' => $key,
            'start' => $start,
            'stop' => $end,
        );
        $redis = self::_getRedisObj();
        if ($redis) {
            $arrRes = $redis->LRANGE($arrInput);
            if ($arrRes === false || $arrRes['err_no'] != 0) {
                echo "get the key=[" . $key . "] and start=[" . $start . "] and end=[ " . $end . "] failed\n";
                return false;
            }
            return $arrRes['ret'][$key];
        }
        return false;
    }

    /**
     * @param $key
     * @return int
     */
    public static function getListLen($key) {
        $arrInput = array(
            'key' => $key,
        );
        $redis = self::_getRedisObj();
        if ($redis) {
            $arrRes = $redis->LLEN($arrInput);
            if ($arrRes === false || $arrRes['err_no'] != 0) {
                $arrRes = self::retryRedis("LLEN", $arrInput, 1);
                if (false === $arrRes) {
                    return 0;
                }
            } else {
                return $arrRes['ret'][$key];
            }
        }
        return 0;
    }

    /**
     * @param $arrForumIds
     * @return bool
     */
    public static function backUpData($arrForumIds) {
        $date = date("Ymd",time());
        $backupFile = self::BACK_UP_ADD . $date;
        $fp = fopen($backupFile,"w");
        foreach ($arrForumIds as $intTempForumId) {
            $arrInput = array(
                'req' => array(
                    'forum_id' => $intTempForumId,
                ),
            );
            $arrRes = Tieba_Service::call('star', 'getCountByFid', $arrInput);
            if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
                $arrRes = self::retryService('star', 'getCountByFid', $arrInput);
                if (false === $arrRes) {

                    continue;
                }
            }
            $intPreCountSign = intval($arrRes['data']['pre_sign']);
            fwrite($fp,$intTempForumId . "\t" . $intPreCountSign . "\n");
        }
        fclose($fp);
        return true;
    }

    /**
     * @return bool
     */
    public static function execute() {
        //获取参加粉丝节的吧的数目
        $arrFestivalForumIds = self::_getRankForum();
        if ($arrFestivalForumIds === false || empty($arrFestivalForumIds)) {
            echo "get the festival forum ids failed \n";
            return true;
        }
        //备份前一天的数据到文件
        self::backUpData($arrFestivalForumIds);
        //先处理报名的粉丝吧的数据
        self::process($arrFestivalForumIds, 0);
        echo "the festival forums is compelet\n";
        self::endProcess();
        return true;
    }

    /**
     * @return bool
     */
    public static function endProcess() {
        $strKey = self::FAIL_ADD_RECORD . date("Ymd", time());
        while (self::getListLen($strKey)) {
            $arrRes = self::removeListKey($strKey);
            $intForumId = $arrRes['ret'][$strKey];
            self::process($intForumId, 1);
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function setRedisFlag($arrInput) {
        $strKey = $arrInput['key'];
        $value = $arrInput['value'];
        $arrInput = array(
            'key' => $strKey,
            'field' => $value,
            'value' => 1,
        );
        while(1) {
            $redis = self::_getRedisObj();
            if ($redis) {
                $arrRes = $redis->HSET($arrInput);
                if ($arrRes === false || $arrRes['err_no'] != 0 || !isset($arrRes['ret'])) {
                    echo " set value failed and key=[" . $strKey . "] \n";
                    $arrRes = self::retryRedis('HSET', $arrInput, 1);
                    if ($arrRes === false) {
                        continue;
                    }
                    return $arrRes;

                } else {
                    return $arrRes;
                }
            }
        }
        return true;
    }

    /**
     * @param $method
     * @param $arrInput
     * @param $flag
     * @return bool
     */
    public static function retryRedis($method, $arrInput, $flag) {
        if ($flag == 0) {
            for ($i = 0; $i < self::MAX_RETRY; $i++) {
                $redis = self::_getRedis();
                $arrRes = $redis->$method($arrInput);
                if ($arrRes === false || $arrRes['err_no'] != 0 || !isset($arrRes['ret'])) {
                    sleep($i);
                    continue;
                }
                echo "execute redis method=[" . $method . "] success after retry times=[" . $i . "]\n";
                return $arrRes;
            }
        }

        if ($flag == 1) {
            for ($i = 0; $i < self::MAX_RETRY; $i++) {
                $redis = self::_getRedisObj();
                $arrRes = $redis->$method($arrInput);
                if ($arrRes === false || $arrRes['err_no'] != 0) {
                    sleep($i);
                    continue;
                }
                echo "execute redis method=[" . $method . "] success after retry times=[" . $i . "]\n";
                return $arrRes;
            }
        }
        return false;
    }

    /**
     * @param $arrInput
     * @return array|bool
     */
    public static function getAllRedisFlag($arrInput) {
        $arrOutPut = array();
        $strKey = $arrInput['key'];
        $arrInput = array(
            'key' => $strKey,
        );
        $redis = self::_getRedisObj();
        if ($redis) {
            $arrRes = $redis->HGETALL($arrInput);
            if ($arrRes === false || $arrRes['err_no'] != 0 || !isset($arrRes['ret'])) {
                echo " get value failed and key=[" . $strKey . "] \n";
                $arrRes = self::retryRedis('HGETALL', $arrInput, 1);
                if ($arrRes === false) {
                    return false;
                }

            }
            $arrTemp = $arrRes['ret'][$strKey];
            foreach ($arrTemp as $arrFlag) {
                $arrOutPut[$arrFlag['field']] = $arrFlag['value'];
            }
        }
        return $arrOutPut;
    }

    /**
     * @param $arrInput
     * @return bool
     */
    public static function delRedisFlag($arrInput) {
        $strKey = $arrInput['key'];
        $value = $arrInput['value'];
        $arrInput = array(
            'key' => $strKey,
            'field' => array(
                $value,
            ),
        );
        $redis = self::_getRedisObj();
        if ($redis) {
            $arrRes = $redis->HDEL($arrInput);
            if ($arrRes === false || $arrRes['err_no'] != 0 || !isset($arrRes['ret'])) {
                echo " del value failed and key=[" . $strKey . "] \n";
                $arrRes = self::retryRedis('HDEL', $arrInput, 1);
                if ($arrRes === false) {
                    return false;
                }
                return $arrRes;

            } else {
                return $arrRes;
            }
        }
        return false;
    }

    /**
     * @param $strService
     * @param $strMethod
     * @param $arrInput
     * @param string $ie
     * @return bool
     */
    public static function retryService($strService, $strMethod, $arrInput, $ie = 'gbk') {
        for ($i = 1; $i < self::MAX_RETRY; $i++) {
            if ($ie == 'utf-8') {
                $arrRes = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
            } else {
                $arrRes = Tieba_Service::call($strService, $strMethod, $arrInput);
            }
            if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
                sleep(5 * $i);
            } else {
                echo "call the service[" . $strService . "] method=[" . $strMethod . "] success after retry[" . $i . "] times\n";
                return $arrRes;
            }
        }
        return false;

    }
}
echo "the start time =[" . date("Ymd H:i:s", time()) . "]\n";
$strDate = date("Ymd", time() - 3600 * 24);
if (!empty($argv[1]) && $argv[1] != 'd') {
    $strDate = intval($argv[1]);
}
$strKey = "star_" . date("Ymd", time());
$strFinishFlag = "star_add_finish_" . date("Ymd", time());
$arrInput = array(
    'key' => $strKey,
);
$arrAllFlag = MakeSignData::getAllRedisFlag($arrInput);
if ($arrAllFlag === false) {
    echo "get the flag fail\n";
    return true;
}
if(!empty($arrAllFlag[$strFinishFlag])) {
    echo "the add star task is success yet\n";
    return true;
}

$bolRet = MakeSignData::execute();
if ($bolRet === true) {
    $arrInput = array(
        'key' => $strKey,
        'value' => $strFinishFlag,
    );
    MakeSignData::setRedisFlag($arrInput);
    echo "execute the star task success at time=[ ." . date("Ymd H:i:s", time()) . "]\n";

} else {
    echo "execute the star task failed at time=[ ." . date("Ymd H:i:s", time()) . "]\n";
}


echo "the end time =[" . date("Ymd H:i:s", time()) . "]\n";
?>