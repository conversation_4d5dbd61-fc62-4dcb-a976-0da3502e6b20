<?php

define("MY_SCRIPT_NAME", 'updateuserattr');
ini_set('memory_limit', '8192M');
require_once('base.php');

/**
 * @brief update the pk value by day
 */
class UserAttr {
    private static $_is_debug   = null;
    private static $_file_path  = null;
    private static $_day_offset = null;

    const USER_ATTR_NAME    = 'pt_star';
    const SLEEP_TIME        = 10;
    const TIME_PERDAY 		= 86400;

    const LOG_STATUS_UNDONE = 1;
    const CUSTOM_TYPE       = 1;

    const STATUS_UNDONE 	= 1;
    const STATUS_DONE	 	= 2;

    //eg: tieba_rpt_prod_plat_fans_file_20150625

    // wordlistkey
    const WORDLIST_KEY = "tb_wordlist_redis_star_fans_party";

    private static function _writeLog($data, $status) {
        return true;
        $arrInput = array(
            'req' => array(
                'user'          => 'yaokun',
                'custom_id'     => (int)$data,  
                'custom_type'   => self::CUSTOM_TYPE,
                'no_dup'        => 1,
                'status'        => (int)$status,
            ),
        );
        $arrOutput = Util::call('generalmessage', 'write', $arrInput);
        if (false === $arrOutput) {
            Bingo_Log::notice("fail to record user_id $data");
            return true;
        }
        return true;
    }

    /**
     * @brief get wordlist
     *
     * @return array or bool
     */
    public static function getWordList() {
        $objWordList = Wordserver_Wordlist::factory();
        $arrInput    = array (
            'table' => self::WORDLIST_KEY,
            'start' => 0,
            'stop'  => -1,
        );
        $arrRet      = $objWordList->getTableContents($arrInput);
        if (null === $arrRet) {
            throw new Exception('fail to get wordlist!');
        }
        return $arrRet;
    }

    public static function getRemainUser() {
        $arrInput = array(
            'req' => array(
                'limit' => 1000,
                'field' => array(
                    'custom_id',
                ),
                'cond' => array(
                    'status' => self::STATUS_UNDONE,
                ),
            ),
        );
        $arrOutput = Util::call('generalmessage', 'read', $arrInput);
        if (false === $arrOutput) {
            Bingo_Log::warning('fail to get total record!');
        }
        $arrUid = array();
        foreach($arrOutput['data'] as $v) {
            $arrUid [] = (int)$v['custom_id'];
        }
        return $arrUid;
    }

    public static function getUser($is_online, $day_offset, $file_prefix) {
        /*
        $base_cmd = "comm -3 ";
        $file1 = "1";
        $file2 = "2";
        if (!file_exists($file1) || !file_exists($file2)) {
            throw new Exception("fail to get user file!");
        }
        $sorted_file1 = "sorted_" . $file1;
        $sorted_file2 = "sorted_" . $file2;
        $sort_cmd1 = "cat $file1 | sort -n > $sorted_file1";
        exec($sort_cmd1);
        $sort_cmd2 = "cat $file2 | sort -n > $sorted_file2";
        exec($sort_cmd2);
        $newfile = "3";
        $cmd = "$base_cmd $sorted_file1 $sorted_file2 > $newfile";
        exec($cmd);
        $diff_file = fopen($newfile, "r+");
         */
        $diff_file_name = "";
        if (!self::$_is_debug) {
            $date = date("Ymd", time()+self::TIME_PERDAY*self::$_day_offset);
            $diff_file_name = self::$_file_path . $date;
        } else {
            $diff_file_name = self::$_file_path;
        }
        var_dump("file name is [$diff_file_name]");
        Bingo_Log::notice("file name is [$diff_file_name]");
        $diff_file = fopen($diff_file_name, "r+");
        if (false === $diff_file) {
            $errmsg = "fail to open file, filename[$diff_file_name]";
            throw new Exception("fail to open file, filename[$diff_file_name]");
        }
        //$arrUid = array();
        while(!feof($diff_file)) {
            $uid = fgets($diff_file);
            $uid = trim($uid);
            $uid = (int)$uid;
            if (0 < $uid) {
                $user_data = self::getUserAttr($uid);
                if (false === $user_data) {
                    continue;
                }
                $value = $user_data[self::USER_ATTR_NAME];
                if (1 === (int)$value['sign_num']) {
                    //todo
                } else {
                    //$cur_mem = memory_get_usage();
                    //var_dump($cur_mem);
                    $ret = self::setUserAttr($uid, $value);
                }
                usleep(self::SLEEP_TIME);
                /*
                $arrUid [] = $uid;
                self::_writeLog($uid, self::STATUS_UNDONE);
                 */
                unset($uid);
            }
        }
        /*
        if (empty($arrUid)) {
            $strNoticeMsg = "now empty from file!";
            Bingo_Log::notice($strNoticeMsg);
            var_dump($strNoticeMsg);
        }
         */
        //return $arrUid;
        return true;
    }

    /**
     * @brief 
     *
     * @param $uid
     * @param $value
     *
     * @return 
     */
    public static function setUserAttr($uid, $value) {
        if (is_null($value)) {
            $value = array(
                'sign_num' => 1,
            );
        } else {
            $value['sign_num'] = 1;
        }
        $arrInput = array(
            'user_id'   => $uid,
            'attr_name' => self::USER_ATTR_NAME,
            'attr_value'=> serialize($value),
        );
        //var_dump($arrInput);
        $arrOutput = Util::call('user', 'setUserAttr', $arrInput);
        if (false === $arrOutput) {
            Bingo_Log::warning("fail to setUserAttr, user_id[$uid]");
            return false;
        }
        return true;
    }

    public static function getUserAttr($uid) {
        $arrInput = array(
            'user_id'   => $uid,
            'get_icon' 	=> 0,
            'data_tpl'  => 'user_pt_single_tpl',
        );
        $arrOutput = Util::call('user', 'getUserData', $arrInput);
        if (false === $arrOutput || 0 !== (int)$arrOutput['errno']) {
            Bingo_Log::warning("fail to getUserData, user_id[$uid]");
            return false;
        }
        return $arrOutput['user_info'][0];
    }

    public static function main() {
        try {
            $arrWordList = self::getWordList();
            $is_start = (int)$arrWordList['ret']['is_start'];
            if (0 === $is_start) {
                // not start
                var_dump("not start!");
                Bingo_Log::notice("not start!");
                return true;
            }

            self::$_file_path   = $arrWordList['ret']['file_path'];
            self::$_is_debug    = (int)$arrWordList['ret']['is_debug'];
            self::$_day_offset  = (int)$arrWordList['ret']['day_offset'];

            //$arrRemainUid 	= self::getRemainUser();
            //$arrRemainUid = array();
            $arrFileUid 	= self::getUser();
            
            /*
            $arrUidHash = array();
            foreach($arrFileUid as $uid) {
                $arrUidHash[$uid] = 1;
            }
            foreach($arrRemainUid as $uid) {
                $arrUidHash[$uid] = 1;
            }
            $arrUid = array();
            foreach($arrUidHash as $uid => $tmp) {
                $arrUid[] = $uid;
            }
            foreach($arrUid as $uid) {
                $user_data = self::getUserAttr($uid);
                if (false === $user_data) {
                    continue;
                }
                $value = $user_data[self::USER_ATTR_NAME];
                if (1 === (int)$value['sign_num']) {
                    self::_writeLog($uid, self::STATUS_DONE);
                } else {
                    $ret = self::setUserAttr($uid, $value);
                    if (true === $ret) {
                        self::_writeLog($uid, self::STATUS_DONE);
                    }
                }
                usleep(self::SLEEP_TIME);
            }
             */
            return true;
        } catch (Exception $e) {
            $errmsg = $e->getMessage();
            Bingo_Log::warning($errmsg);
            var_dump($errmsg);
            return false;
        }
    }
}

if ( UserAttr::main() ) {
    Bingo_Log::notice("success");
    var_dump("success");
} else {
    Bingo_Log::notice("fail");
    var_dump("fail");
}
exit(0);
