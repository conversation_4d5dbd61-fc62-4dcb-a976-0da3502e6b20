MODULE=star
HOST=cq01-testing-forum53.vm.baidu.com
TAR_NAME=star.tar.gz
UI_PATH=/home/<USER>/orp001
cd ../../
rm -rf output
sh build.sh 1>/dev/null 2>/dev/null
cd output
for i in `find . -name '*.tar.gz'`;
do
	TAR_NAME=$i
done
echo "==> moving ${TAR_NAME} to :${HOST}:${UI_PATH}"
scp ${TAR_NAME} forum@$HOST:$UI_PATH

ssh forum@${HOST} "
	cd ${UI_PATH}
	tar xfz ${TAR_NAME} 1>/dev/null 2> /dev/null
	rm ${TAR_NAME} 
"
