<?php
/**
 * Created by PhpStorm.
 * User: NY
 * Date: 14-4-9
 * Time: 锟斤拷锟斤拷9:20
 */

class Search_Common_Data {
    public static function getOfficialList(){
        /*$arrInput = array();
        $arrOut = Tieba_Service::call('star','getOfficialList',$arrInput,NULL,NULL,'POST','php','utf-8');
        if($arrOut === false || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            return false;
        }*/
        $intLimit = 50;
        $intPn    = 1;
        $arrOfficialInfo = array();
        $intResCount = $intLimit;
        $arrKeys = array();
        while ($intResCount === $intLimit) {
            $arrInput = array(
            	'type'  => 1,
            	'pn'    => $intPn,
            	'limit' => $intLimit,
            );
            $arrResOut = Tieba_Service::call('official', 'getOfficialList', $arrInput,NULL,NULL,'POST','php','utf-8'); 
            if (false === $arrResOut || $arrResOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::fatal('call official::getOfficialList failed! '.serialize($arrResOut).'_'.serialize($arrInput));
                return  false;
            }
            $intResCount = count($arrResOut['data']['list']);
            ++$intPn;            
            $arrOfficialID = array();
            $arrTempOfficialInfo = array();
            foreach ($arrResOut['data']['list'] as $value){
                $arrTemp['official_id']   = intval($value['forum_id']);
                $arrTemp['official_name'] = $value['forum_name'];
                $arrMaterial = $value['material'];
                $arrOfficialID[]['forum_id'] = intval($value['forum_id']);
                $arrTemp['star_name']     = isset($arrMaterial['star_name'])?$arrMaterial['star_name']:'';
                $arrTemp['search_pic']    = isset($arrMaterial['search_pic'])?$arrMaterial['search_pic']:'';
                $arrTemp['summary']       = isset($arrMaterial['summary'])?$arrMaterial['summary']:'';
                $arrTempOfficialInfo[intval($value['forum_id'])] = $arrTemp;
            } 
        	$arrInput=array('input'=>$arrOfficialID);
	        $arrAttrOut = Tieba_Service::call('forum','mgetForumAttr',$arrInput,NULL,NULL,'POST','php','utf-8');
            if($arrAttrOut === false || $arrAttrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                return false;
            }
	        foreach($arrAttrOut['output'] as $key=>$value) {
	            if (isset($value['official']['official_special_switch']['official_star_push']) && intval($value['official']['official_special_switch']['official_star_push']) === 1){
		            $arrOfficialInfo[]  = $arrTempOfficialInfo[$key];
		            $arrKeys[]          = intval($key);
	            }
	        }            
        }     
        /*   
        $arrFilterForums=self::getFilterForums();	
	if(!isset($arrFilterForums)||empty($arrFilterForums)) {
		echo "get the wordlist failed";
		return false;
	}*/
	/*
	foreach($arrOut['data'] as $key=>$value) {
		if(in_array($value['official_id'],$arrFilterForums)) {
			unset ($arrOut['data'][$key]);
			continue;
		}
		$arrFids[]=array('forum_id'=>$value['official_id']);
		$arrKeys[]=$value['official_id'];
	}*/
	$arrSearchKeys=self::getSearchKeys($arrKeys);
	if(empty($arrSearchKeys)) {
		echo "get the search keys failed";
		return false;
	}/*
	$arrInput=array('input'=>$arrFids);
	$arrOut = Tieba_Service::call('forum','mgetForumAttr',$arrInput,NULL,NULL,'POST','php','utf-8');
        if($arrOut === false || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            return false;
        }
	foreach($arrOut['output'] as $value) {
	   if(!empty($value['star_official'])){
		$arrOfficialInfo[]=$value['star_official'];
	   }
	}*/
	$arrOut['data']['official']=$arrOfficialInfo;			
	$arrOut['data']['search_keys']=$arrSearchKeys;
        return $arrOut['data'];
    }
    public static function getNewsList($official_id){
        //用一个trick的方法，取前200条然后再判断发帖时间
        $intNum = 100;
        $arrInput = array(
		'forum_id' => $official_id,
		'need_abstract'=>1,
 		'offset'=>0,
 		'res_num'=>$intNum,
 		'icon_size'=>3,
   		'need_photo_pic'=>0,
  		'forum_name'=>'',
        );
        $arrOut = Tieba_Service::call('post','getFrs',$arrInput,NULL,NULL,'POST','php','utf-8');
        if($arrOut === false || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            return false;
        }

	
        $arrThreadList = $arrOut['output']['thread_list'];
        
        if (count($arrThreadList) === $intNum) {
            sleep(1);
            $arrInput = array(
		    	'forum_id' => $official_id,
				'need_abstract'=>1,
 				'offset'=>$intNum,
 				'res_num'=>$intNum,
 				'icon_size'=>3,
   				'need_photo_pic'=>0,
  				'forum_name'=>'',
             );
            $arrOut = Tieba_Service::call('post','getFrs',$arrInput,NULL,NULL,'POST','php','utf-8');
            if($arrOut === false || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                return false;
            }
            $arrThreadList = $arrThreadList+$arrOut['output']['thread_list'];            
        }
        return $arrThreadList;
    }
    public static function getScheduleList($official_id){
        $arrInput = array(
            'official_id' => $official_id,
            'pn' => 1,
            'limit' =>10,
        );
        $arrOut = Tieba_Service::call('star','getLatestSchedule',$arrInput,NULL,NULL,'POST','php','utf-8');
        if($arrOut === false || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            return false;
        }
        return $arrOut['data'];
    }
   public static function getFilterForums() {
        $arrFilterForums=array();
        $handleWordServer = Wordserver_Wordlist::factory();
        $strTableName = 'tb_wordlist_redis_search_filter_forums';
        $arrInput = array(
                  'table' => $strTableName,
                  'start' => 0,
                  'stop' =>  -1,
                 );
        $ret = $handleWordServer->getTableContents($arrInput);
        foreach($ret['ret'] as $val) {
                 $arrFilterForums=unserialize($val);
        }
        return $arrFilterForums;
    }
  public static function getSearchKeys($arrForumIds) {
        $handleWordServer = Wordserver_Wordlist::factory();
        $strTableName = 'tb_wordlist_redis_star_search_keys';
	$arrItemInfo = $handleWordServer->getValueByKeys($arrForumIds,$strTableName);
	foreach($arrForumIds as $value) {
	 	$arrSearchKeys[$value]= unserialize($arrItemInfo[$value]);
	}
	
        return $arrSearchKeys;
    }

} 
