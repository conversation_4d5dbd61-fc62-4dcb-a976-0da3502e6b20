<?
    echo '<?xml version="1.0" encoding="utf-8"?>';
?>

<DOCUMENT>
    <?$specialCharacter=array("&","<",">","'",'"');
      $replaceCharacter=array("&amp;","&lt;","&gt;","&apos;","&quot;");
    ?>
    <?foreach($data_list as $value){?>
	<?$arrKeys=$search_keys[$value['official']['official_id']];?>
	<?$value['official']['official_name']=urlencode($value['official']['official_name']);?>
	<?foreach ($arrKeys as $search_key){?>
        <item>
            <key><?echo $search_key?></key>
            <display>
                <title><?echo $value['official']['star_name'];?>的最新动态</title>
			
			<url><?echo "http://tieba.baidu.com/f?kw=" . $value['official']['official_name'];?> </url>
                <nodeNum><?echo count($value['news_list']);?></nodeNum>
		<?$time_list=$value['time_list'];?>
		<?$count=0;?>
                <?foreach($value['news_list'] as $key=>$item){?>
                    <?
                        $newsLink = 'http://tieba.baidu.com/p/' .$key;
 			$isNew = false;
                        if($count == 0){
                            $isNew = true;
			    $count++;
                        }
			if(!empty($item['media'])&&!empty($item['media'][0])) {
				if($item['media'][0]['type']=='pic'&&!empty($item['media'][0]['small_pic'])) {
				$pic=$item['media'][0]['small_pic'];
				}
			}
					
                    ?>
                    <node>
			<?$item['title']=str_replace($specialCharacter,$replaceCharacter,$item['title']);?>
                        <nodeTitle><?echo $item['title']?></nodeTitle>
                        <nodeTime><?echo date('Y-m-d' ,$time_list[$key]);?></nodeTime>
			<?
                            if($isNew){
                               echo '<nodeType>New</nodeType>';
                            }else{
                               echo '<nodeType>Normal</nodeType>';
                            }
                        ?>
			<?$item['abstract']=str_replace($specialCharacter,$replaceCharacter,$item['abstract']);?>
                        <abstract><?echo $item['abstract'];?></abstract>
                        <abstractUrl><?echo $newsLink;?></abstractUrl>
                        	<resrcNum>1</resrcNum>
                        		<resrc>
					<? if($isNew) {
					   echo '<type>img</type>';
					  }else{
					   echo '<type>news</type>';
					  }
					?>
                            			<resrcTitle>
                                		<text><?echo $item['title']?></text>
                                		<url>
                                   		<?echo $newsLink;?>
                                		</url>
                            			</resrcTitle>
						<?if($isNew&&!empty($pic)){?>
                                		<resrcImg>
                                    		<src><?echo $pic?></src>
                                    		<url><?echo $newsLink;?></url>
                                		</resrcImg>
                            			<?}?>
                            			<resrcTime><?echo date('Y-m-d',$time_list[$key]);?></resrcTime>
                            			<resrcRef>百度贴吧</resrcRef>
                        		</resrc>
                    </node>
                <?}?>
            </display>
        </item>
	<?}?>
    <?}?>
</DOCUMENT>
