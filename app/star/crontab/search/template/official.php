<?
    echo '<?xml version="1.0" encoding="utf-8"?>';
?>

<DOCUMENT>
	<?$specialCharacter=array("&","<",">","'",'"');
      	  $replaceCharacter=array("&amp;","&lt;","&gt;","&apos;","&quot;");
    	?>
    <?foreach($official_list as $value){?>
    <?$arrKeys=$search_keys[$value['official_id']];?>
    <?$value['official_name']=urlencode($value['official_name']);?>
    <?foreach ($arrKeys as $search_key){?>
    <item>
	<?$search_key=str_replace($specialCharacter,$replace<PERSON>haracter,$search_key);?>
       <key><?echo $search_key?></key>
        <display>
            <title><?echo $value['star_name']?>官方粉丝团_<?echo $value['star_name']?>官方粉丝服务平台_百度贴吧</title>
		
            <url>http://tieba.baidu.com/f?kw=<?echo $value['official_name']?></url>
            <photo src="<?echo $value['search_pic'];?>" src135="<?echo $value['search_pic'];?>"/>
	    <?$value['summary']=str_replace($specialCharacter,$replaceCharacter,$value['summary']);?>
            <text><?echo $value['summary'];?></text>
            <part name="新闻公告" link="http://tieba.baidu.com/f/good?kw=<?echo $value['official_name']?>&amp;tab=good&amp;cid=1"/>
            <part name="官方日程" link="http://tieba.baidu.com/f/good?kw=<?echo $value['official_name']?>&amp;tab=schedule"/>
            <part name="staff日志" link="http://tieba.baidu.com/f/good?kw=<?echo $value['official_name']?>&amp;tab=good&amp;cid=2"/>
           <showurl>tieba.baidu.com/</showurl>
            <date><?echo date('Y-m-d',time());?></date>
        </display>
    </item>
    <?}?>
    <?}?>
</DOCUMENT>
