<?php
ini_set("memory_limit", "2G");

define ('ROOT_PATH', dirname(__FILE__) . "/../..");
define('IS_ORP_RUNTIME', true);
if (!defined('REQUEST_ID')) {
    define('REQUEST_ID', Bingo_Log::getLogId());
}

if (function_exists('camel_set_logid')) {
    camel_set_logid(REQUEST_ID);
}

Tieba_Init::init('star');

class threadRcommend{
    public $intTime = '';
    const DB_NAME   = 'forum_activity_center';
    const TABLE_HEADER_INFO = "header_info";
    const RELATED_ACT_TYPE = 3;
    
    public function __construct() {
        $this->intTime = time();
        Bingo_Timer::start('total');
        return true;
    }

    public function __destruct() {
        Bingo_Timer::end('total');
        Bingo_Log::pushNotice("script_name" , $_SERVER['PHP_SELF']);
        $strTimeLog = Bingo_Timer::toString();
        if (!empty($strTimeLog)) {
            $strTimeLog = substr($strTimeLog, 0, 100);
            Bingo_Log::pushNotice("Timer", $strTimeLog);
        }
        Bingo_Log::buildNotice();
    }

    public static function getDb() {
        $objTbMysql = Tieba_Mysql::getDB(self::DB_NAME);
        if ($objTbMysql && $objTbMysql->isConnected()) {
             $objTbMysql->charset('gbk');
            return $objTbMysql;
        } else {
            Bingo_Log::warning("db connect fail.");
            return null;
        }
    }
    public function run(){
        $intOffset = 0;
        $intPerNum = 30;
        $arrFiled = array(
            'head_id',
            'related_act_id',
        );
        while (true) {
            $objDb = self::getDb();
            $strCond = " related_act_type =".self::RELATED_ACT_TYPE;
            $strAppend = " order by num desc limit ".$intOffset.",".$intPerNum;
            $arrRes = $objDb->select(self::TABLE_HEADER_INFO, $arrFiled, $strCond, NULL, $strAppend);
            if ($arrRes === false) {
                Bingo_Log::warning("query daily failed. SQL[". $objDb->getLastSQL()."][output:".serialize($objDb->error())."]");
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            $arrWordKey = array();
            if (!empty($arrRes)) {
                foreach ($arrRes as $arrRow) {
                    $arrTid[] = $arrRow['related_act_id'];
                }
                $arrInput = array(
                    'thread_ids' => $arrTid,
                    'need_abstract' => 0,
                    'forum_id' => 0,
                    'need_photo_pic' => 0,
                    'need_user_data' => 0,
                    'icon_size' => 3,
                    'need_forum_name' => 0,
                    'call_from' => 'star',
                );
                $arrRet = Tieba_Service::call('post', 'mgetThread', $arrInput);
                if (Tieba_Errcode::ERR_SUCCESS!=$arrRet['errno'] || !is_array($arrRet['output']['thread_list'])) {
                    usleep(2000);
                    $arrRet = Tieba_Service::call('post', 'mgetThread', $arrInput);
                }
                if (Tieba_Errcode::ERR_SUCCESS!=$arrRet['errno'] || !is_array($arrRet['output']['thread_list'])) {
                    echo "get mgetThread failed, tids:".serialize($arrTid)."\n";
                    continue;
                }
                //帖子已经被删除不再更新pv
                foreach ($arrRes as $arrRow) {
                    $intTid = intval($arrRow['related_act_id']);
                    $intHeadId = intval($arrRow['head_id']);
                    $arrThreadInfo = $arrRet['output']['thread_list'][$intTid];
                    if (!isset($arrThreadInfo['is_deleted']) || 1===intval($arrThreadInfo['is_deleted'])) {
                        continue;
                    }
                    $arrUpdate = array(
                        'table' => 'header_info',
                        'field' => array(
                            'num=' => intval($arrThreadInfo['freq_num']),
                        ),
                        'cond' => array(
                            'head_id='=> $intHeadId,
                        ),
                    );
                    self::update($arrUpdate);
                    usleep(500);
                }
            }
            if (count($arrRes)<$intPerNum){
                break;
            }
            $intOffset += $intPerNum;
        }
    }
    public static function update($arrInput) {
        if(!isset($arrInput['table']) ||!isset($arrInput['field']) || !is_array($arrInput['field'])) {
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return false;
        }
        $table = $arrInput['table'];
        $strField = null;
        foreach ($arrInput['field'] as $key => $value) {
            if ($strField !== null) {
                $strField .= ",";
            }
            $key = mysql_escape_string(trim($key));
            $value = mysql_escape_string(trim($value));
            $strField .= "$key'$value' ";   
        }
        $strCond = null;
        if (isset($arrInput['cond']) && is_array($arrInput['cond'])) {
            foreach($arrInput['cond'] as $cond => $value) {
                if ($strCond !== null) {
                    $strCond .= " and ";
                }
                if (is_array($value)) {
                    $value = array_map(array("Util_Db", "addQuote"), $value);
                    $strCond .= "$cond in (".implode(",", $value).") ";
                } else {
                    $key = mysql_escape_string(trim($cond));
                    $value = mysql_escape_string(trim($value));
                    $strCond .= "$key '$value' ";
                }
            }
        }
        $append = null;
        if (isset($arrInput['append'])) {
            $append = $arrInput['append'];
        }
        $db = self::getDb();
        if (is_null($db)) {
            return false;
        }
        $ret = $db->update($table, $strField, $strCond, null, $append);
        if ($ret === false) {
            Bingo_Log::warning("[output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            echo "[output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]";
            return false;
        }
        return true;
    }
    
    public function sliceAdvertByWeight($arrAdvert = array()) {
        $arrOutput = array();
        foreach ($arrAdvert as $val) {
            $arrOutput[$val['type']][$val['sub_type']][$val['weight']][] = $val;
        }
        return $arrOutput;
    }
}

$obj = new threadRcommend();
$obj->run();
