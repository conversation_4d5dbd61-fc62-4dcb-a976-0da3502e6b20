<?php
/*
 * @brief ��ͨ���ǰɺ����ǹٷ��ɽ�����˺󣬽���ͨ���ǰɵĻ�Ա�������ǹٷ�����
 *
 * ���ԣ�
 *    1����ȡforum_star����union_record������˹���δ�ܽű��ļ�¼��Ҳ��status=1,script_status=0�ļ�¼
 *    2������1�ж�ȡ�ļ�¼��ȥforum_grade���в�����ͨ�ɵĻ�Ա����
 *    3�����ε���perm::setUserLikeForumʹ��ͨ���ǰɵĻ�Ա��ע���ǹٷ��ɣ�ÿ��ִ��10�Σ��Է�ֹѹ��perm����
 *
 * NOTE��
 *   1�����ݽű�����setUserLikeForum�����ݿ��������һ����Ե���86W��Ա��
 *   2��������һ�����������˵İ��ж��������ӳٿ������¼��㣺
 *       2.1 ������˰ɸ���ΪM��
 *       2.2 ƽ��ÿ����ͨ�ɻ�Ա��ΪN�����λ���򣩣�
 *       2.3 ����ӳ�ʱ����Ϊ M*N/86 (��λ���죩��
 *   3���ű�ִ��ʱ�䣺һ��ִ��3�Σ��Ա�Ͽ쵼���Ա��
 * */
class UnionLike{
	const GRADE_DATABASE  = 'grade_new_read';
	const STAR_DATABASE   = 'forum_star';
	const GRADE_TABLE     = 'user_grade';
	const STAR_TABLE      = 'union_record';
	const GRADE_NUM       = 1000;               // һ�δ�forum_grade�л�ȡ��¼�ĸ���
	const SLEEP_NUM       = 10;                 // һ����ִ��10������
	const TIME_INTERVAL   = 3600;               // �ж���һ�νű��Ƿ���ִ��

	// ��ȡforum_grade���ݿ�
    public function getGradeDB() {
        $objTbGcMysql = Tieba_Mysql::getDB(self::GRADE_DATABASE);
        if($objTbGcMysql && $objTbGcMysql->isConnected()) {
            return $objTbGcMysql;
        } else {
            $objTbGcMysql = Tieba_Mysql::getDB(self::GRADE_DATABASE);
            if($objTbGcMysql && $objTbGcMysql->isConnected()) {
                return $objTbGcMysql;
            }
            Bingo_Log::warning(self::GRADE_DATABASE." db connect fail.");
            return null;
        }
    }

	// ��ȡforum_star���ݿ�
    public function getStarDB() {
        $objTbMysql = Tieba_Mysql::getDB(self::STAR_DATABASE);
        if($objTbMysql && $objTbMysql->isConnected()) {
            return $objTbMysql;
        } else {
            Bingo_Log::warning(self::STAR_DATABASE." db connect fail.");
            return null;
        }
    }

	protected function _selectSql($table, $where, $cols,$conds) {
        if (empty($cols) || (count($cols) == 1) && $cols[0] == '*') {
            $selectStr = "*";
        } else {
            foreach ($cols as $val) {
                $selectStr .= "`$val`,";
            }
            $selectStr = rtrim($selectStr, ',');
        }
        $sql = "select $selectStr from $table  ";
        $whereSql = "where 1 = 1 ";
        if (!empty($where)) {
            foreach ($where as $wKey => $wVal) {
                $whereSql .= " and $wKey '".addslashes($wVal)."'";
            }
        }
        $sql .= $whereSql.' '.$conds;
        return $sql;
    }

	public function selectData($db,$table, $where, $cols,$conds) {
        $sql = $this->_selectSql($table, $where, $cols,$conds);
        //$db = $this->getDB();
        $res = $db->query($sql);
        return $res;
    }

    protected function _updateSql($table, $where, $data) {
        $sql = "update $table set ";
        foreach ($data as $dKey => $dVal) {
            $dataSql .= " $dKey = '$dVal',";
        }
        $dataSql = rtrim($dataSql, ',');
        $sql .= $dataSql;
        $whereSql = " where 1 = 1 ";
        foreach ($where as $wKey => $wVal) {
            $whereSql .= " and $wKey  '$wVal'";
        }
        $sql .= $whereSql;
        return $sql;
    }

	public function updateData($db,$table, $where, $data) {
        $sql = $this->_updateSql($table, $where, $data);
		//var_dump($sql);
        //$db = $this->getDB();
        $row = $db->query($sql);
        if (false == $row) {
            echo "this update fail no:".$db->errno."\r\n";
            Bingo_Log::warning("this update fail no:".$db->errno);
        }
        return $row;
    }

	protected function _insertSql($table, $cols) {
        $sql = "insert into $table ";
        $filedStr = '';
        $valStr   = '';
        foreach ($cols as $key => $val) {
            $filedStr .= "`$key`,";
            $valStr	  .= "'$val',";
        }
        $sql .= " (". rtrim($filedStr, ',') ." )";
        $sql .= " values(". rtrim($valStr, ','). ")";
        return $sql;
    }

	public function insertData($db,$table, $cols) {
        //$db = $this->getDB();
        $sql = $this->_insertSql($table, $cols);
        $res = $db->query($sql);
        return $res;
    }
	
	public function getUnionRecord(){
		$db = $this->getStarDB();
		if (null === $db){
			Bingo_Log::warning('get db fail');
			return false;
		}
		
		// script_status 0: �δ����  1�� ������  2��������
		// ȥ��δ������ļ�¼������һ��ִ����Ҫ��ʱ�ܳ�������ֻȡ10����¼����
		$arrWhere = array(
						//'script_status <>'   => 2,     // δ�����¼
						'script_status ='    => 1,     // δ�����¼
						'status='            => 1,     // �Ѿ��������
						'content='   => 2,
					);
		$strConds = 'limit 0,10';
		$arrCols  = array('id','official_id','forum_id','content','script_status','script_index','script_time');
		
		$arrRet = $this->selectData($db,self::STAR_TABLE,$arrWhere,$arrCols,$strConds);

		$intTime = time();

		if (!empty($arrRet)){
			foreach ($arrRet as $key => $arrItem){
				// ������¼���ص�ʱ��������ڵ�ʱ�����С��һ��Сʱ��������˵���ű���Ч���������쳣��ֹ��
				$intInterval = $intTime - intval($arrItem['script_time']);
				if ($intInterval < self::TIME_INTERVAL){
					unset($arrRet[$key]);
					continue;
				}

				$sql = "select is_union from star_union where official_id=".$arrItem['official_id'].' and forum_id='.$arrItem['forum_id'];
				$res = $db->query($sql);
				//var_dump($arrItem);
				//var_dump($res);
				//$arrRet[$key]['is_union'] = $res[0]['is_union'];

				// ����������ˣ����Ҳ�ǽ������
				// ��ʱ���ӽ�ˮ�ˣ����������״̬����ʵ��ȫû��Ҫ��
				$intContent = intval($arrItem['content']) - 2;
				if (0 == $intContent && $res[0]['is_union']){
					$arrRet[$key]['is_union'] = $res[0]['is_union'];
				}else if (1 == $intContent && 0 == intval($res[0]['is_union'])){
					// ���������ˣ����Ҳ�ǽ���ɹ�
					$arrRet[$key]['is_union'] = $res[0]['is_union'];
				}else {
					unset($arrRet[$key]);
				}
			}
		}

		return $arrRet;
	}

	// ��Ƭ��ȡָ���ɵĻ�Ա����
	public function getForumMemberLimit($intFid = null, $offset = 0, $limit = 1000) {
        $sql = "select user_id from forum_member where forum_id=$intFid and flag = 1 limit $offset, $limit;";
		//var_dump($sql);
        $db = $this->getGradeDB();
        $res = $db->query($sql);
        return $res;
    }

	public function execute(){
		$db = $this->getStarDB();
		if (null == $db){
			Bingo_Log::warning('get db fail');
			return false;
		}

		$arrRet = $this->getUnionRecord();

		if (!empty($arrRet)){
			foreach($arrRet as $arrItem){
				if (1 != $arrItem['script_status']){
					// ��Ǹü�¼�Ѿ������
					$arrWhere = array(
									'id='   => $arrItem['id'],
									);
					$arrCols  = array(
									'script_status'   => 1,
									'script_time'     => time(),
									);
					$ret = $this->updateData($db,self::STAR_TABLE,$arrWhere,$arrCols);
					//var_dump($ret);
					if (!$ret){
						continue;
					}
				}

				$intStart = intval($arrItem['script_index']);
				$bolFinish = false;
				while(!$bolFinish){
					$arrMembers = $this->getForumMemberLimit($arrItem['forum_id'],$intStart,self::GRADE_NUM);
					//var_dump($arrMembers);
					if (count($arrMembers) < self::GRADE_NUM){
						$bolFinish = true;
					}

					$intCnt = 0;
					foreach ($arrMembers as $intUid){
						$intCnt++;
						if ($intCnt && $intCnt%self::SLEEP_NUM == 0){
							sleep(1);
						}

						$arrParam = array(
										'forum_id'    => intval($arrItem['official_id']),
										'user_id'     => intval($intUid['user_id']),
										);
						if (!$arrItem['is_union']){
							$arrParam['type'] = 'unlike';
						}
						$ret = Tieba_Service::call('perm','setUserLikeForum',$arrParam);
						if (false === $ret || Tieba_Errcode::ERR_SUCCESS !== $ret['errno']){
							Bingo_Log::warning('call perm::setUserLikeForum fail with input ['.serialize($arrParam).'] output ['.serialize($ret).']');
						}
					}// END FOREACH

					$intStart += count($arrMembers);

					// ���´����ļ������Է�ִֹ��ͻȻʧ�ܺ����¿�ʼ�ܽű�ʱ��Ҫ���ܺܶ�����
					$arrWhere = array(
									'id='    => $arrItem['id'],
									);
					$arrCols = array(
									'script_index' => $intStart,
									'script_time'  => time(),
									);
					$ret = $this->updateData($db,self::STAR_TABLE,$arrWhere,$arrCols);
				}// END WHILE

				$arrWhere = array(
								'id='    => $arrItem['id'],
								);
				$arrCols  = array(
								'script_status'   => 2,
								'script_time'  => time(),
								);
				$ret = $this->updateData($db,self::STAR_TABLE,$arrWhere,$arrCols);
				if (!$ret){
					continue;
				}
			}// END FOREACH
		}// END If
	}
};

$objUnionLike = new UnionLike();
$objUnionLike->execute();
