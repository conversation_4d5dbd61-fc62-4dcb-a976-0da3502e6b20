<?php

class Util_Def {
    const STRATEGY_WIDE     = 0;
    const STRATEGY_TIGHT    = 1;

    const RANK_GO_PREFIX    = "rank_go_"; //排行榜redis prefix
    const RANK_NAME         = 'fan_festival'; //排行榜rank

    const TOP_CACHE_TIME    = 2; //3600
    const FORUM_CACHE_TIME  = 10; //60
    const CACHE_GO_PREFIX   = "star_go_"; //助威cache prefix

    const MULTI_SINGLE_MAX  = 32;

    const FIELD_ORI_SIGN        = 0;
    const FIELD_CUR_SIGN        = 1;
    const FIELD_VOTEID          = 2;
    const FIELD_PK_YESTERDAY    = 3;
    const FIELD_PK_BFYESTERDAY  = 4;
    const FIELD_VOTE_NUM        = 5;
    const FIELD_PRE_SIGN        = 6;
    const FIELD_TDY_SIGN        = 7;
    const FIELD_TOTAL_SIGN      = 8;
    const FIELD_ANTI_SIGN       = 9;

    const USER_ATTR_NAME        = 'pt_star';

    //wordlist
    const WORDLIST_NAME                 = 'tb_wordlist_redis_star_fans_party';
    const WORDLIST_PK_COEF              = 'pk_coef';
    const WORDLIST_PK_COEF_DEFAULT      = 1;

    //error code area
	const ERR_FORUM_NOT_EXISTS_ERROR    = 1;       // 吧不存在
    const ERR_FORUM_HAS_REGISTER_ERROR  = 2;       // 本吧已经报名 
    const ERR_NOT_STAR_FORUM_ERROR      = 3;       // 不是娱乐明星的吧
	const ERR_NET_REASON_ERROR          = 4;       // 网络原因
    const ERR_TEL_ERROR                 = 5;       // 手机号错误
    const ERR_QQ_ERROR                  = 6;       // qq号错误
    const ERR_NO_MANAGER_ERROR          = 7;
    const ERR_IDENTIFY_ID_ERROR         = 8;
    const ERR_USER_HAS_REGISTER_ERROR   = 9;        // 用户已经报名
    const ERR_NZYQ_INVALID_URL          = 10;       // 帖子url不合法
    const ERR_THREAD_HAS_DELETE_ERROR   = 11;       // 帖子已经被删除
    const ERR_USER_NOT_LOGIN            = 12;       // 没有登录
    const ERR_HIT_IDX_ANTI              = 13;       // 中ueg策略，每个id每天最多推荐3条帖子,或者每个IP每天最多推荐200条帖子，每秒系统能接受的推荐最多为5条帖子
    const ERR_EMPTY_REASON_ERROR        = 15;       // 推荐的理由为空
    const ERR_ADDRESS_ERROR             = 16;       // 地址有误
    const ERR_TBS_ERROR                 = 17;
    const ERR_NOT_POST_ERROR            = 18;
    const ERR_GO_HIT_ANTI               = 14;       // 助威中用户策略,您在本吧已经助威了
    const ERR_GO_NORMAL_MEMBER          = 19;
    const ERR_GO_VIP_MEMBER             = 20;
    const ERR_GO_SUPER_MEMBER           = 21;
    const ERR_GO_ACT_HAS_END            = 22;
    const ERR_GO_IP_VOTE_TOO_MUCH       = 23;
    const ERR_GO_TOTAL                  = 24;       // 用户一天全吧助威超过3次
    const ERR_USER_NO_RIGHT             = 25;       // 粉丝没有达到注册标准
    const ERR_REGISTER_STOP             = 26;       // 报名时间已过

    const ERR_GO_HIT_IDX187             = 187;
    const ERR_GO_HIT_IDX188             = 188;
    const ERR_GO_HIT_IDX189             = 189;
    
    const REASON_TO_IDX187 = 65535;
    const REASON_TO_IDX188 = 65534;
    const REASON_TO_IDX189 = 65533;

    //debug const
    const ANTI_DEBUG                    = true;

    const RANK_FIRST_PER_NUM            = 39;
    const RANK_NOT_FIRST_PER_NUM        = 36;
    const MOBILE_PATTERN                = "#^(1[0-9]{10})|((0[0-9]{2,3}\-)?([2-9][0-9]{6,7})+(\-[0-9]{1,4})?)$#";
    const ID_PATTERN                    = "/^(\d{6})(18|19|20)?(\d{2})([01]\d)([0123]\d)(\d{3})(\d|X|x)?$/";

	// 票数
	const FANS_PARTY_POOL = 'fans_party';
	const FANS_TICKET_NUM = 'ticket_num';

	// 存放邀请个数
	const FORUM_POOL                    = 'forum_list';
	const FORUM_CONTENT                 = 'invite_num';
	const FORUM_LIST_SIZE               = 100;
    const MAX_TOP_LIST_COUNT            = 99;

    // 存放票务码
    const STAR_FESTIVAL_EXKEY_ACT_ID  = 'star_festival_exkey_act_id';
    const EXKEY_ACT_ID                = 'exkey_act_id';
    const EXKEY_LETTER_TITLE          = '恭喜您，成功报名百度贴吧粉丝节';
    const EXKEY_LETTER_CONTENT_FORMAT = '您已经成功报名百度贴吧粉丝节嘉年华，您的票码如下：%s，请您妥善保管。';
    
    // 用户能否报名阈值
    const FANS_VOTE_LIMIT               = 3;

    const CELEBRITY_PT_TYPE             = 1;
}
