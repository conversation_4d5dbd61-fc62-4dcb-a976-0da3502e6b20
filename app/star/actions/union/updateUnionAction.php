<?php
/**
 *Author: shiyi<PERSON>
 *Filename: addUnion.php
 *Date: 2014-09-25
 *Desc: 明星官方吧联盟申请相关操作
 *      1、申请加入明星官方联盟
 *      2、申请解除明星官方联盟
 *      都是由用户在FRS页主动发起，具体的审批在吧务后台进行
 */
class updateUnionAction extends Util_Base{

	const TYPE_REMOVE    = 1;     // 申请加入
	const TYPE_JOIN      = 0;     // 申请解除

	protected $_arrContentFields = array(
									self::TYPE_JOIN,
									self::TYPE_REMOVE,
									);

	protected $_arrParams    = array(
							    	'official_id' => '',       // 官方吧id
									'forum_ids'   => '',       // 联盟吧吧ID，字符串，以逗号分隔
									'type'        => '',       // 操作类型 0: 申请加入  1 ： 申请解除
									);


	public function execute(){
		try{
            $this->check();
            //$this->_isManagerOrPm();
            //$this->_checkCommit();
            $this->_getUserInfo();
            $this->_execute();
            $this->_jsonRet(0,'');
        }catch (Util_Exception $e){
            $this->_jsonRet($e->getCode(),$e->getMessage());
        }
	}
    protected function _execute(){
		$this->_arrParams['apply_id']   = $this->_arrUserInfo['user_id'];
		$this->_arrParams['apply_user'] = Bingo_Encode::convert($this->_arrUserInfo['user_name'],Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
		$arrParam = array(
						'forum_id'     => $this->_arrParams['forum_ids'],
						'official_id'  => $this->_arrParams['official_id'],
						);
		$arrOut = Tieba_Service::call('star','checkApplyExists',$arrParam,null,null,'post','php','utf-8');
		if($arrOut === false || Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']){
			Bingo_Log::fatal("call star::addAlliance failed! ".serialize($arrOut)."_".serialize($this->_arrParams));
            throw new Util_Exception(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

		if (true === $arrOut['data']){
			return true;
		}

		$arrOut = Tieba_Service::call('star','applyAlliance',$this->_arrParams,null,null,'post','php','utf-8');
        if($arrOut === false || Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']){
			Bingo_Log::fatal("call star::addAlliance failed! ".serialize($arrOut)."_".serialize($this->_arrParams));
            throw new Util_Exception(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return true;
    }
	public function check(){
        $this->_arrParams = array(
								'official_id' => intval(Bingo_Http_Request::get('official_id',0)),
            					'forum_ids'   => strval(Bingo_Http_Request::get('forums','')),
            					'content'     => intval(Bingo_Http_Request::get('type',0)),
        						);

		if ($this->_arrParams['official_id'] <= 0 || empty($this->_arrParams['forum_ids'])  
			|| !in_array($this->_arrParams['content'],$this->_arrContentFields)){
			Bingo_Log::warning('param error '.serialize($this->_arrParams));
			throw new Util_Exception(Tieba_Errcode::ERR_PARAM_ERROR);
		}

		// 申请加入，需要获取吧名
		$objRalMulti = new Tieba_Multi('union_btx_info');
		$arrForumIds = explode(',',$this->_arrParams['forum_ids']);
		$arrForumIds[] = $this->_arrParams['official_id'];
		foreach ($arrForumIds as $forum_id){
			$arrParam = array(
							'forum_id'   => $forum_id,
							);
			$arrMultiInput = array(
								'serviceName'    => 'forum',
								'method'         => 'getBtxInfo',
								'input'          => $arrParam,
								);
			$objRalMulti->register($forum_id,new Tieba_Service('forum'),$arrMultiInput);
		}

		array_pop($arrForumIds);
		$objRalMulti->call();
		$arrForumInfo = array();
		foreach ($arrForumIds as $forum_id){
			$arrResult = $objRalMulti->getResult($forum_id);
			if (false === $arrResult || Tieba_Errcode::ERR_SUCCESS !== $arrResult['errno']){
				Bingo_Log::warning('call forum::getBtxInfo fail. input['.$forum_id.'] output '.serialize($arrResult));
				throw new Util_Exception(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
			}

			$arrNew = array(
						'forum_id'    => $forum_id,
						'forum_name'  => Bingo_Encode::convert($arrResult['forum_name']['forum_name'],Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK),
						);
			$arrForumInfo[] = $arrNew;
		}

		$arrResult = $objRalMulti->getResult($this->_arrParams['official_id']);
		if (false === $arrResult || Tieba_Errcode::ERR_SUCCESS !== $arrResult['errno']){
			Bingo_Log::warning('call forum::getBtxInfo fail. input['.$this->_arrParams['official_id'].'] output '.serialize($arrResult));
			throw new Util_Exception(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}

		$this->_arrParams['official_portrait'] = $arrResult['card']['avatar'];
		$this->_arrParams['official_name']     = Bingo_Encode::convert($arrResult['forum_name']['forum_name'],Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
		$this->_arrParams['forum_info']        = $arrForumInfo;
	}
}
