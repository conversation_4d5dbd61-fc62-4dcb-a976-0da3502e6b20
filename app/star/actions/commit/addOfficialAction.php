<?php
/**
 *Author: jiang<PERSON>bin
 *Filename: changeBackground.php
 *Date: 2014-03-27
 *Desc: 
 */
class addOfficialAction extends Util_Base{
	private $_arrParams = array(
		'official_id'     => '',
		'official_name'   => '',
		'background_pic'  => '',
		'logo'            => '',
		'third_logo'      => '',
		'member_pic'      => '',
		'flash_url'       => '',
		'color'           => '',
		'deadline_color'  => '',
		'deadline_pic'    => '',
		'summary'         => '',
		'register_time'   => '',
		'star_name'       => '',
		'search_pic'      => '',
	);

	protected $_strFileName  = "http://storage.orp.baidu.com/openapi/call/storage/get?token=5Pm4xjGMUml9OBFk1EsOsJJra765jUYn&productName=tieba&path=/mis/official.txt";

	public function execute(){
		try{
            $this->check();
            $this->_process();
            $this->_jsonRet(0,'success');
        }catch (Util_Exception $e){
            $this->_jsonRet($e->getCode(),$e->getMessage());
        }
	}

    public function _process(){
		$arrParam = array(
						'req'=> $this->_arrParams,
						);
		$arrOut = Tieba_Service::call('star','addNewOfficialSite',$arrParam);
        if($arrOut == false){
            Bingo_Log::warning('add new official site fail');
            throw new Util_Exception(Tieba_Errcode::ERR_UNKOWN);
        }
        return true;
    }
	public function check(){
		$arrContents = file($this->_strFileName,FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
		$intParamNum = count($this->_arrParams);
		$intParamCnt = count($arrContents);
		if ($intParamNum != $intParamCnt){
			throw new Util_Exception(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrParam = array();
		foreach ($arrContents as $strLine){
			$arrLine = explode(':',trim($strLine),2);
			$arrParam[$arrLine[0]] = $arrLine[1];
		}
		foreach ($this->_arrParams as $key => $value){
			if (isset($arrParam[$key])){
				$this->_arrParams[$key] = $arrParam[$key];
			}	
		}

		// check official_id is valid or not
		if($this->_arrParams['official_id'] <= 0){
            throw new Util_Exception(Tieba_Errcode::ERR_PARAM_ERROR);
        }

		/*$arrInput=array('forum_id'=>$this->_arrParams['official_id']);
		$arrRet=Tieba_Service::call('forum','getForumAttr',$arrInput);
	    if(isset($arrRet['output']['star_official'])) {
			$register_time=intval($arrRet['output']['star_official']['register_time']);
			if(time()<$register_time) {
				Bingo_Log::warning('now can not register');
				throw new Util_Exception(Tieba_Errcode::ERR_PARAM_ERROR);
			}
		}
		else {
			   Bingo_Log::warning('it is not the star_official forum');
			   throw new Util_Exception(Tieba_Errcode::ERR_PARAM_ERROR);
		}*/
	}
}
