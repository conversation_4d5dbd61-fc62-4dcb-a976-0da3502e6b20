<?php
/**
 *Author: shiyibo
 *Filename: maddMember.php
 *Date: 2014-08-07
 *Desc: 
 */
class maddMemberAction extends Util_Base{
	private $_arrParams = array(
		'real_name' => '',
		'address' => '',
		'region' => '',
		'province' => '',
		'birthday' => '',
		'identify' => '',
		'email'    => '',
	);
    private $_official_id = 0;

	protected $_strFileName         = "http://storage.orp.baidu.com/openapi/call/storage/get?token=5Pm4xjGMUml9OBFk1EsOsJJra765jUYn&productName=tieba&path=/mis/official_member.txt";
    const BIRTHDAY_FORMAT_LENGTH = 8;//生日的格式长度，例如19880907
	public function execute(){
		try{
			$arrContents = file($this->_strFileName,FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
			$bolSucc = true;
			foreach ($arrContents as $strLine){
				if (!strlen(trim($strLine))){
					continue;
				}
				$arrLine = explode(':',$strLine);
				$ret = $this->check($arrLine);
				if (false === $ret){
					$bolSucc = false;
					echo $strLine.' is invalid'."\n";
					continue;
				}

				$ret = $this->addMember($arrLine);
				if (false === $ret){
					echo $strLine.' add fails'."\n";
					$bolSucc = false;
					continue;
				}
			}
			if ($bolSucc){
				$this->_jsonRet(0,'success');
			}
        }catch (Util_Exception $e){
            $this->_jsonRet($e->getCode(),$e->getMessage());
        }
	}
    public function addMember($arrInput){
		$intOfficialId = intval(trim($arrInput[0]));
		$intUserId     = intval($arrInput[1]);
		$arrUserInfo   = array(
							'real_name'  => trim(strval($arrInput[2])),
							'address'    => trim(strval($arrInput[8])),
							'region'     => trim(strval($arrInput[7])),
							'province'   => trim(strval($arrInput[7])),
							'birthday'   => trim(strval($arrInput[6])),
							'identify'   => trim(strval($arrInput[3])),
							'email'      => trim(strval($arrInput[4])),
							);
        $arrParam  = array(
            'user_id' => $intUserId,
            'official_id' =>$intOfficialId,
            'user_info' => $arrUserInfo,
        );
        $arrOut = Util_LocalCall::call('addNewOfficialMember',$arrParam);
        if($arrOut == false){
            Bingo_Log::warning('add member fail');
            //throw new Util_Exception(Tieba_Errcode::ERR_UNKOWN);
			return false;
        }
        return true;
    }
	public function check($arrInput){
		$arrParams   = array(
							'real_name'  => trim(strval($arrInput[2])),
							'address'    => trim(strval($arrInput[8])),
							'region'     => trim(strval($arrInput[7])),
							'province'   => trim(strval($arrInput[7])),
							'birthday'   => trim(strval($arrInput[6])),
							'identify'   => trim(strval($arrInput[3])),
							'email'      => trim(strval($arrInput[4])),
							);

        $intOfficialId = intval(trim($arrInput[0]));
		$arrInput=array('forum_id'=>$intOfficialId);
		$arrRet=Tieba_Service::call('forum','getForumAttr',$arrInput);
	    if(isset($arrRet['output']['star_official'])) {
			/*$register_time=intval($arrRet['output']['star_official']['register_time']);
			if(time()<$register_time) {
				Bingo_Log::warning('now can not register');
				throw new Util_Exception(Tieba_Errcode::ERR_PARAM_ERROR);
			}*/
		}
		else {
			   Bingo_Log::warning('it is not the star_official forum');
			   //throw new Util_Exception(Tieba_Errcode::ERR_PARAM_ERROR);
			   //return false;
		}
        foreach($arrParams as $value){
            if($value == ''){
                //throw new Util_Exception(Tieba_Errcode::ERR_PARAM_ERROR);
				return false;
            }
        }
        if($intOfficialId <= 0){
            //throw new Util_Exception(Tieba_Errcode::ERR_PARAM_ERROR);
			return false;
        }
        if(strlen($arrParams['birthday']) != self::BIRTHDAY_FORMAT_LENGTH){
            //throw new Util_Exception(Tieba_Errcode::ERR_PARAM_ERROR);
			return false;
        }
	}
}
