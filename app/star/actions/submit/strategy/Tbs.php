<?php

/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */


class Actions_Submit_Strategy_Tbs implements Actions_Submit_Strategy_Interface {
    /**
        * @brief 
        *
        * @param $arrInput
        *
        * @return 
     */
    public static function execute($arrInput) {
        $tbs = Bingo_Http_Request::get('tbs', '');
        if (0 >= strlen($tbs)) {
            throw new Exception("invalid tbs", Util_Def::ERR_TBS_ERROR);
        }
        if(!Tieba_Tbs::check($tbs, true)) {
            throw new Exception("invalid tbs", Util_Def::ERR_TBS_ERROR);
        }
        return true;
    }
}

