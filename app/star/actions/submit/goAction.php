<?php

class goAction extends Bingo_Action_Abstract {
    const CHECKER_CONF      = 'go';
    private $_arrErrnoMap   = null;

    const ENCOURAGE_ANTI_FORUM      = 65533;
    const ENCOURAGE_ANTI_IP         = 65532;
    const ENCOURAGE_ANTI_TOTAL      = 65534;
    const ENCOURAGE_DL_CALL_FAIL    = 210005;
    const ENCOURAGE_UNKNOWN_ERROR   = 110003;
    
    public function init() {
        $this->_arrErrnoMap = array(
            self::ENCOURAGE_ANTI_FORUM      => Util_Def::ERR_GO_HIT_ANTI,
            self::ENCOURAGE_ANTI_IP         => Util_Def::ERR_GO_IP_VOTE_TOO_MUCH,
            self::ENCOURAGE_ANTI_TOTAL      => Util_Def::ERR_GO_TOTAL,
            self::ENCOURAGE_DL_CALL_FAIL    => Util_Def::ERR_NET_REASON_ERROR,
            self::ENCOURAGE_UNKNOWN_ERROR   => Util_Def::ERR_NET_REASON_ERROR,
        );
        $this->execute();
    }

    /**
        * @brief main
        *
        * @return 
     */
    public function execute() {
        try {
            //1. checker
            $objChecker = new Actions_Submit_Strategy_Main();
            $objChecker->init(self::CHECKER_CONF);
            $objChecker->check();

            //2. init param
            $arrField = array(
                'forum_id' => Util_Param::PARAM_TYPE_INT_NOT_ZERO,
                //'vote_id'  => Util_Param::PARAM_TYPE_INT_NOT_ZERO, 
            );
            $arrParam = Util_Param::getParam($arrField);

            $intFid     = $arrParam['forum_id'];
            //$intVoteId  = $arrParam['vote_id'];
            $arrUserInfo = Util_Function::getUserInfo();
            $intUid = (int)$arrUserInfo['user_id'];
            $intUip = $arrUserInfo['user_ip'];

            if (0 >= $intUid || 0 >= $intUip) {
                Bingo_Log::pushNotice('uid_or_uip_error', 1);
                throw new Exception("invalid uid or uip!");
            }
            Bingo_Log::pushNotice('uid_or_uip_error', 0);

            //d. set rank score
            $strModule      = 'star';
            $strMethod      = 'updateScoreByFid';
            $strIe          = 'gbk';
            $strCallType    = 'remote';
            $intStrategy    = Util_Def::STRATEGY_TIGHT;
            $arrInput = array(
                'req' => array( 
                    'user_id'   => $intUid,
                    'user_ip'   => $intUip,
                    'forum_id'  => $intFid,
                    'from'      => 'pc',
                ),
            );

            // add notice log
            Bingo_Log::pushNotice("forum_id", $intFid);
            Bingo_Log::pushNotice("user_id", $intUid);
            if (Util_Function::IsIpV6($intUip)) {
                Bingo_Log::pushNotice("user_ipv6", $intUip);
            } else {
                Bingo_Log::pushNotice("user_ip", $intUip);
            }

            $arrOutput = Util_Function::call($strModule, $strMethod, $arrInput, $strIe, $strCallType, $intStrategy);
            if (0 < $arrOutput['errno']) {
                throw new Exception("some error unknown, output[" . serialize($arrOutput) . "], ", $arrOutput['errno']);
            }
            $intAddVoteNum = $arrOutput['data']['add_vote_num'];

            //4. return
            Util_Function::jsonReturn($intAddVoteNum, 'success');
            $this->_buildNetLog();
            Bingo_Log::pushNotice('is_hit_ueg', 0);
            return true;
        } catch(Exception $e) {
            $errmsg = $e->getMessage();
            $errno = $e->getCode();
            $this->_checkIsHitUeg($errno);
            $errno = isset($this->_arrErrnoMap[$errno]) ? $this->_arrErrnoMap[$errno] : $errno;
            Bingo_Log::warning($errmsg);
            Util_Function::jsonReturn($errno, $errmsg);
            return false;
        }
    }

    /**
        * @brief check is hit ueg!
        * @param $errno
        * @return 
     */
    protected function _checkIsHitUeg($errno) {
        switch($errno) {
            case self::ENCOURAGE_ANTI_FORUM:
                $is_hit = 1;
                break;
            case self::ENCOURAGE_ANTI_IP:
                $is_hit = 1;
                break;
            case self::ENCOURAGE_ANTI_TOTAL:
                $is_hit = 1;
                break;
            default:
                $is_hit = 0;
                break;
        }
        Bingo_Log::pushNotice('is_hit_ueg', $is_hit);
        return true;
    }

    protected function _buildNetLog() {
        
    }

}

?>
