<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-06-10 16:03:40
 * @version
 */

class rankAction extends Util_Base {
    const TEMPLATE = 'fans_rank.php';

    public function execute(){
        try{
            $intPn = Bingo_Http_Request::get('pn', 1);
            $intRn = 0; 
            if (1 < $intPn) {
                $intRn = Util_Def::RANK_NOT_FIRST_PER_NUM;
            } else {
                $intRn = Util_Def::RANK_FIRST_PER_NUM;
            }
            Util_Common::redirectFeModule('activity');
            $arrMultiInput = array();
            $arrMultiInput[] = array(
                'star',
                'getCount',
                array(
                    'req' => array(
                        'pn' => $intPn,
                        'rn' => $intRn,
                        'from' => 'rank',
                    ),
                ),
                'gbk',
                'getCount',
            );
            $arrMultiOut = Util_Function::multi_call($arrMultiInput);

            $arrData = array();
            $arrFids = array();
            $intTotalCount = 0;
            //process result
            foreach($arrMultiOut as $key => $out) {
                if($key === 'getCount') {
                    if (false === $out || 0 !== $out['errno']) {
                        throw new Exception('call service to get top list failed!', Util_Def::ERR_NET_REASON_ERROR);
                    }
                    foreach($out['data']['list'] as $data) {
                        $item = array();
                        $item['pk_rate'] = $data['info']['pk_deltabyper'];
                        $intSignNum = $data['info']['total_sign']-$data['info']['anti_sign'];
                        if (0 >= $intSignNum) {
                            $intSignNum = 0;
                        }
                        $item['sign_num'] = $intSignNum;
                        //$item['sign_num'] = $data['info']['pre_sign']+$data['info']['today_sign'];
                        $item['cheer_val'] = (int)$data['info']['vote_num'];
                        $item['forum_name'] = $data['info']['forum_name'];
                        $item['portrait'] = $data['info']['portrait'];
                        $arrData[$data['forum_id']] = $item;
                        $arrFids [] = $data['forum_id'];
                    }
                    $intTotalCount = (int)$out['data']['count'];
                }
            }
            foreach($arrFids as $fid) {
                $item = $arrData[$fid];
                $this->_output['rank_list'] [] = $item;
            }
            if (!isset($this->_output['rank_list'])) {
                $this->_output['rank_list'] = array();
            }
            $this->_output['count'] = $intTotalCount;

            $this->_getUserInfo();
            Tieba_Stlog::addNode('urlkey', 'rank');
            Tieba_Stlog::addNode('user_id', $this->_arrUserInfo['user_id']);

            $this->_displayTemplate(self::TEMPLATE);
        }catch (Exception $e) {
            $errno  = $e->getCode();
            $errmsg = $e->getMessage();
            Bingo_Log::warning($errmsg);
            header('Location://tieba.baidu.com');
        }
    }
}

?>
