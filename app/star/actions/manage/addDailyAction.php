<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-03-25 13:35:22
 * @comment 模版接口
 * @version
 */
class addDailyAction extends Util_Base {

	private $_arrParam = array(
		'title' => '',
		'content' => '',
		'abstract' => '',
		'official_id' => 0,
		'type' => '',
		'pic_urls' => array(),
	);

    public function execute(){
    	try {
			self::_checkCommit();
			self::_isManager();

			$this->_arrParam = $this->_getParams();
			$output = Tieba_Service::call('star','addDaily',$this->_arrParam, 
											NULL, NULL, 'post', 'php', 'utf-8', 'local');
			if ($output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				$strLog = 'add daily failed. param['.serialize($this->_arrParam).
					'output ['.serialize($output).']';
				Bingo_Log::warning($strLog);
				self::_jsonRet('call service fail.',Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
				return ;
			}
			self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, 'success');
        }catch(Util_Exception $e){
			$errno = $e->getCode();
			$error = $e->getMessage(); 
            Bingo_Log::warning( "errno=".$errno ." msg=".$error );
			self::_jsonRet($errno, $error);
        }
    }

	private function _getParams() {
		//前端post的都是utf-8，直接获取raw数据
		$strPicUrl = trim(strval(Bingo_Http_Request::getPostRaw('pic_urls',''))); 
		$arrPicUrl = array();
		if (!empty($strPicUrl)) {
			$arrPicUrl = explode(',',$strPicUrl);
			foreach ($arrPicUrl as $index => $picUrl) {
				$arrPicUrl[$index] = trim($picUrl);
			}
		}
		$title    = Bingo_String::xssDecode(Bingo_Http_Request::getPostRaw('title',''));
		$content  = Bingo_String::xssDecode(Bingo_Http_Request::getPostRaw('content',''));
		$abstract = substr($content,0,100);
		$official_id = Bingo_Http_Request::getPostRaw('official_id','');
		$type = Bingo_Http_Request::getPostRaw('type','');
		if (!$title || !$content || !$official_id 
			|| ($type != 'news' && $type != 'staff')) {
			$strLog = "param error. title[$title] content[$content] official_id[$official_id] type[$type]";
			Bingo_Log::warning($strLog);
			throw new Util_Exception('param error', Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrParam = array(
			'title' => $title,
			'content' => $content,
			'abstract' => $abstract,
			'official_id' => $official_id,
			'type' => $type,
			'pic_urls' => $arrPicUrl,
			'op_uid' => $this->_arrUserInfo['user_id'],
			'op_ip' => $this->_arrUserInfo['user_ip'],
			'create_time' => time(),
		);
		return $arrParam;
	}

}
?>
