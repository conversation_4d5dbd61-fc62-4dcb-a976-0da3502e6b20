<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-08-01 13:35:22
 * @comment 更新日程
 * @version
 */
class updateScheduleAction extends Util_Base {

	private $_arrParam = array(
							'id' => '',
							'title' => '',
							'content' => '',
							'start_time' => '',
							'official_id' => 0,
							'type' => '',
							'pic_urls' => array(),
							);

	private static $_type = array(
		'show', 'program', 'confer', 'other',
	);

    public function execute(){
    	try {
			//self::_checkCommit();
			self::_isManagerOrPm();

			$this->_arrParam = $this->_getParams();
			//$output = Util_LocalCall::call('updateSchedule', $this->_arrParam);
			$output = Tieba_Service::call('star','updateSchedule',$this->_arrParam,NULL,NULL,'post','php','utf-8','local');
			if ($output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				$strLog = 'update schedule failed. param['.serialize($this->_arrParam).
					'output ['.serialize($output).']';
				Bingo_Log::warning($strLog);
                self::_jsonRet(Tieba_Errcode::ERR_UNKOWN,'unknow error');
				return ;
			}
			self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, 'success');
        }catch(Util_Exception $e){
			$errno = $e->getCode();
			$error = $e->getMessage(); 
            Bingo_Log::warning( "errno=".$errno ." msg=".$error );
			self::_jsonRet($errno, $error);
            //也可以跳转302
            //Tieba_Error::fetalError302();
        }
    }

	private function _getParams() {
		$id    = Bingo_Http_Request::getPostRaw('id',0);
		$title = Bingo_String::xssDecode(Bingo_Http_Request::getPostRaw('title',''));
		$content = Bingo_String::xssDecode(Bingo_Http_Request::getPostRaw('content',''));
		$start_time = Bingo_Http_Request::getPostRaw('start_time','');
		$official_id = Bingo_Http_Request::getPostRaw('official_id','');
		$type = strval(Bingo_Http_Request::getPostRaw('type',''));
		$debug = intval(Bingo_Http_Request::get('debug',''));
		if ($debug){
			$arrParam = array(
				'id'    => '2',
				'title' => 'title002',
				'content' => 'content002',
				'official_id' => 35, 
				'start_time' => time() + 86400,
				'type' => 'show',
				'op_uid' => $this->_arrUserInfo['user_id'],
				//'create_time' => time(),
			);
		}else {
			if (!$id || !$title || !$content || !$official_id 
				|| !$start_time || !in_array($type,self::$_type, true)) {
				$strLog = "param error. id[$id] title[$title] content[$content] official_id[$official_id] type[$type] start_time[$start_time]";
				Bingo_Log::warning($strLog);
				throw new Util_Exception(Tieba_Errcode::ERR_PARAM_ERROR);
			}
			$arrParam = array(
				'id'    => $id,
				'title' => $title,
				'content' => $content,
				'official_id' => $official_id,
				'start_time' => strtotime($start_time),
				'type' => $type,
				'op_uid' => $this->_arrUserInfo['user_id'],
				//'create_time' => time(),
			);
		}
		return $arrParam;
	}
}
?>
