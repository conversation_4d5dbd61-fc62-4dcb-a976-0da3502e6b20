<?php
/**
 *Author: jiang<PERSON>bin
 *Filename: newsAction.php
 *Date: 2014-03-24
 *Desc: 
 */
class newsAction extends Util_Base{

    const TEMPLATE = '';

    const TYPE_NEWS = 'news';

    const ITEM_PER_PAGE = 5;

    private $_arrParam = array(
        'official_id' => '',
        'type' => '',
        'pn' => 0,
        'limit' => 0,
        'format' => '',
    );

    public function execute(){
        try {
            self::_isManager();

            $this->_arrParam = $this->_getParams();
            //直接从数据库中获取
            $output = Tieba_Service::call('star','getDailyListFromDB',$this->_arrParam,
                NULL, NULL, 'post', 'php', 'gbk', 'local');
            if ($output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                $strLog = 'get news list failed. param['.serialize($this->_arrParam).
                    'output ['.serialize($output).']';
                Bingo_Log::warning($strLog);
                $this->_output = array();
            }
            else {
                $this->_output['news_list'] = $output['daily_list'];
                $this->_output['total'] = $output['total'];
                $this->_output['pn'] = $this->_arrParam['pn'];
                $this->_output['limit'] = $this->_arrParam['limit'];
            }

            if ($this->_arrParam['format'] == 'json') {
                $this->_output = Bingo_Encode::convert($this->_output,Bingo_Encode::ENCODE_UTF8,
                    Bingo_Encode::ENCODE_GBK);
                self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, 'success', $this->_output);
                return ;
            }
            self::_displayTemplate('news.php');
        }catch(Util_Exception $e){
            $errno = $e->getCode();
            $error = $e->getMessage();
			Bingo_Log::warning("errno[$errno] error[$error]");
            //也可以跳转302
            //Tieba_Error::fetalError302();
        }
    }

    private function _getParams() {
        $official_id = intval(Bingo_Http_Request::get('official_id',''));
        $pn = intval(Bingo_Http_Request::get('pn',''));
        if (!$official_id) {
            $strLog = "param error. official_id[$official_id]";
            Bingo_Log::warning($strLog);
            throw new Util_Exception( Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $pn = $pn<=0 ? 1 : $pn;
        $limit = $limit <=0 ? self::ITEM_PER_PAGE : $limit;
        $arrParam = array(
            'official_id' => $official_id,
            'pn' => $pn,
            'limit' => $limit,
            'type' => self::TYPE_NEWS,
            'format' => $format,
        );
        return $arrParam;
    }
}
