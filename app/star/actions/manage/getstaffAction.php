<?php
/**
 *Author: jiang<PERSON>bin
 *Filename: getstaffAction.php
 *Date: 2014-03-24
 *Desc: 
 */
class getstaffAction extends Util_Base{

    const TEMPLATE = 'staff.php';

    const TYPE_STAFF = 'staff';

    const ITEM_PER_PAGE = 5;

    private $_arrParam = array(
        'official_id' => '',
        'type' => '',
        'pn' => 0,
        'limit' => 0,
        'format' => '',
    );

    public function execute(){
        try {
            self::_isManager();

            $this->_arrParam = $this->_getParams();
            //直接从数据库中获取
            $output = Tieba_Service::call('star','getDailyListFromDB',$this->_arrParam,
                NULL, NULL, 'post', 'php', 'gbk', 'local');
            if ($output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                $strLog = 'get news list failed. param['.serialize($this->_arrParam).
                    'output ['.serialize($output).']';
                Bingo_Log::warning($strLog);
                $this->_output = array();
            }
            else {
                $this->_output['staff_list'] = $output['daily_list'];
                $this->_output['total'] = $output['total'];
                $this->_output['pn'] = $this->_arrParam['pn'];
                $this->_output['limit'] = $this->_arrParam['limit'];
            }

            if ($this->_arrParam['format'] == 'json') {
                $this->_output = Bingo_Encode::convert($this->_output,Bingo_Encode::ENCODE_UTF8,
                    Bingo_Encode::ENCODE_GBK);
                self::_jsonRet(Tieba_Errcode::ERR_SUCCESS, 'success', $this->_output);
                return ;
            }
            self::_displayTemplate(self::TEMPLATE);
        }catch(Util_Exception $e){
            $errno = $e->getCode();
            $error = $e->getMessage();
            if($e->getCode() == Tieba_Errcode::ERR_POST_CT_POWER_NOT_ENOUGH){//没有权限查看直接跳转到错误页
                Tieba_Error::fetalError302();
                return false;
            }
            //也可以跳转302
            //Tieba_Error::fetalError302();
        }
    }

    private function _getParams() {
        $official_id = intval(Bingo_Http_Request::get('official_id',''));
        $pn = intval(Bingo_Http_Request::get('pn',''));
        if (!$official_id) {
            $strLog = "param error. official_id[$official_id]";
            Bingo_Log::warning($strLog);
            throw new Util_Exception( Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $pn = $pn<=0 ? 1 : $pn;
        $limit = $limit <=0 ? self::ITEM_PER_PAGE : $limit;
        $arrParam = array(
            'official_id' => $official_id,
            'pn' => $pn,
            'limit' => $limit,
            'type' => self::TYPE_STAFF,
            'format' => $format,
        );
        return $arrParam;
    }
}
