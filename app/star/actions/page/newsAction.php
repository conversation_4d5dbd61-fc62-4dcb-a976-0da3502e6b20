<?php
/**
 *Author: jiang<PERSON>bin
 *Filename: newsAction.php
 *Date: 2014-03-24
 *Desc: 
 */
class newsAction extends Util_Base{

    const TEMPLATE = '';

    const TYPE_NEWS = 'news';

    const ITEM_PER_PAGE = 5;

    private $_arrParam = array(
        'official_id' => '',
        'type' => '',
        'pn' => 0,
        'limit' => 0,
        'format' => '',
    );

    public function execute(){
		try {
			$this->_initParam();
			//获取通用的模板变量
			$this->_getCommonData();

			$arrInput = array(
				'official_id' => $this->_output['official']['official_id'],
				'type' => 'news',
				'pn' => $this->_arrParam['pn'],
				'limit' => $this->_arrParam['limit'],
			);
			$arrNews = Tieba_Service::call('star','getDailyList',$arrInput,
				NULL,NULL,'POST','php','gbk','local');
			if ($arrNews['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				$strLog = 'get news list failed. input['.serialize($arrInput).
					'] output['.serialize($arrNews).']';
				Bingo_Log::warning($strLog);
			}
			$this->_output['news_list'] = $arrNews['daily_list'];
			$this->_output['page'] = Util_Pager::formatPager($arrNews['total'],
				$arrNews['pn'], $arrNews['limit']);
			$this->_displayTemplate('news.php');
		} catch (Util_Exception $e) {
            $errno = $e->getCode();
            $error = $e->getMessage();
			Bingo_Log::warning("errno[$errno] error[$error]");
            //也可以跳转302
            Tieba_Error::fetalError302();
		}
    }

	private function _initParam() {
		$this->_arrParam['official_id'] = intval(Bingo_Http_Request::get('official_id',''));
		$this->_arrParam['pn'] = intval(Bingo_Http_Request::get('pn',1));
		$this->_arrParam['limit'] = intval(Bingo_Http_Request::get('limit',self::ITEM_PER_PAGE));
	}
}
