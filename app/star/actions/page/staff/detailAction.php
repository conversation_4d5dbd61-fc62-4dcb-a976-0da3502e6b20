<?php
/**
 *Author: ji<PERSON><PERSON><PERSON>
 *Filename: staff/detailAction.php
 *Date: 2014-03-26
 *Desc: 
 */
class detailAction extends Util_Base{

    const TEMPLATE = '';

    const TYPE_NEWS = 'news';

    const ITEM_PER_PAGE = 5;

    private $_arrParam = array(
		'official_id' => 0,
        'id' => 0,
        'format' => '',
    );

    public function execute(){
        try {
			$this->_initParam();
			//获取通用的模板变量
			$this->_getCommonData();

			$reqDailyInfo = array(
				'daily_id' => $this->_arrParam['id'],
			);
			$resDailyInfo = Tieba_Service::call('star','getDailyInfoById',$reqDailyInfo,
				NULL,NULL,'POST','php','gbk','local');
			if ($resDailyInfo['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				$strLog = 'get daily info failed. input['.serialize($reqDailyInfo).
					'] output['.serialize($resDailyInfo).']';
				Bingo_Log::warning($strLog);
			}
			$this->_output['detail'] = $resDailyInfo['daily'];
			$this->_displayTemplate('detail/staff.php');
        }catch(Util_Exception $e){
            $errno = $e->getCode();
            $error = $e->getMessage();
			Bingo_Log::warning("errno[$errno] error[$error]");
            //也可以跳转302
            Tieba_Error::fetalError302();
        }
    }

    private function _initParam() {
        $official_id = intval(Bingo_Http_Request::get('official_id',''));
        $id = intval(Bingo_Http_Request::get('id',''));
        $format = strval(Bingo_Http_Request::get('format',''));
        if (!$id) {
            $strLog = "param error. official_id[$official_id] id[$id]";
            Bingo_Log::warning($strLog);
            throw new Util_Exception( Tieba_Errcode::ERR_PARAM_ERROR);
        }
		$this->_arrParam = array(
			'official_id' => $official_id,
			'id' => $id,
			'format' => $format,
		);
    }
}
