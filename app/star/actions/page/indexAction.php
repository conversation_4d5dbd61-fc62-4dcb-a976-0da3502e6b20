<?php
/**
 *Author: jiang<PERSON>bin
 *Filename: indexAction.php
 *Date: 2014-03-24
 *Desc: 
 */
class indexAction extends Util_Base{
    const PAGE_SIZE = 5;
	public function execute(){
        try{
			$this->_initParam();
			//获取通用的模板变量
			$this->_getCommonData();

            $arrInput = array(
                'official_id' => $this->_output['official']['official_id'],
                'pn' => $this->_arrParams['pn']
            );
            //获取首页的feed流
            $arrFeed = Util_LocalCall::call('getFeedList',$arrInput);
            $arrFeedList = array();
            if($arrFeed !== false){
				$arrFeed = $arrFeed['data'];
                $this->_output['feed_list'] = $arrFeed['feed_list'];
				$this->_output['page'] = Util_Pager::formatPager($arrFeed['total'],
					$this->_arrParams['pn'],$arrFeed['limit']);
            }
			$this->_displayTemplate('index.php');
			
        }catch (Util_Exception $e){
            $errno = $e->getCode();
            $error = $e->getMessage();
			Bingo_Log::warning("errno[$errno] error[$error]");
            //也可以跳转302
            Tieba_Error::fetalError302();
        }
	}

	private function _initParam() {
		$this->_arrParams['pn'] = Bingo_Http_Request::get('pn',1);
		$this->_arrParams['official_id'] = Bingo_Http_Request::get('official_id','');
	}
}
