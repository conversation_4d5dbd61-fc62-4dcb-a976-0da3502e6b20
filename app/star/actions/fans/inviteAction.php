<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date      2015-06-10 16:03:40
 * @ 吧邀请
 * @version
 */
class inviteAction extends Util_Base {
    const TITLE          = 'title';
    const CONTENT        = 'content';
    const LEVEL_1_NAME   = '娱乐明星';
    const ACT_FORUM_TYPE = 1;

    protected $_strForumName = ''; // 吧名
    protected $_intForumId = 0; // 吧ID
    protected $_arrManagerList = array (); // 吧主列表
    protected $_intComeFrom = 0; // 0 : 专题页 1： FRS

    public function execute () {
        try {
            $this->_check();
            $this->_checkCommit();
            $this->_getUserInfo();
            $this->_execute();
            $this->_jsonRet(0, '');
        } catch (Util_Exception $e) {
            $this->_jsonRet($e->getCode(), $e->getMessage());
        }
    }

    protected function _check () {
        // 注意防范XSS
        $this->_strForumName = Bingo_String::xssDecode(Bingo_Http_Request::get('forumName', ''));
        $this->_intComeFrom  = Bingo_String::xssDecode(Bingo_Http_Request::get('come_from', 0));

        // 邀请截止时间 30号零点
        $intDeadLine = strtotime('2015-08-30 00:00:00');
        $intNow      = time();
        if ($intNow >= $intDeadLine) {
            Bingo_Log::warning('register stoped');
            throw new Util_Exception(Util_Def::ERR_REGISTER_STOP, 'invite stoped');
        }

        if (empty($this->_strForumName)) {
            throw new Util_Exception(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
        }

        $arrParam = array (
            'forum_name' => $this->_strForumName,
        );
        //$arrParam = Bingo_Encode::convert($arrParam, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        $arrRet = Tieba_Service::call('forum', 'getBtxInfoByName', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            $strMsg = sprintf('call forum::getBtxInfoByName fail with input [%s] output [%s]', serialize($arrParam), serialize($arrRet));
            Bingo_Log::warning($strMsg);
            throw new Util_Exception(Util_Def::ERR_NET_REASON_ERROR, 'net reason');
        }
        $this->_intForumId = (int)$arrRet['forum_id']['forum_id'];
        $this->_strLevel1  = $arrRet['dir']['level_1_name'];
        $strRealFname      = $arrRet['forum_id']['forum_name'];

        // 查看吧是否有效
        if ($this->_intForumId <= 0 || $strRealFname !== $this->_strForumName) {
            throw new Util_Exception(Util_Def::ERR_FORUM_NOT_EXISTS_ERROR, 'forum not exists');
        }

        // 查看是否是娱乐明星目录的吧
        if (self::LEVEL_1_NAME !== $this->_strLevel1) {
            throw new Util_Exception(Util_Def::ERR_NOT_STAR_FORUM_ERROR, 'forum not star forum');
        }

        // 查看是否已经报名
        if (isset($arrRet['attrs']['pt_switch']) && isset($arrRet['attrs']['pt_switch']['fans_festival_register']) && $arrRet['attrs']['pt_switch']['fans_festival_register']) {
            Bingo_Log::pushNotice($this->_intForumId, 'register');
            throw new Util_Exception(Util_Def::ERR_FORUM_HAS_REGISTER_ERROR, 'forum has registered');
        }
        /*$arrParam = array (
            'related_act_type' => self::ACT_FORUM_TYPE,
            'related_act_id'   => $this->_intForumId,
        );
        $arrRet   = Tieba_Service::call('header', 'getRecommendHead', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            $strMsg = sprintf('call star::getRegisterInfoByRelatedActId fail with input [%s] output [%s]', serialize($arrParam), serialize($arrRet));
            Bingo_Log::warning($strMsg);
            throw new Util_Exception(Util_Def::ERR_NET_REASON_ERROR, 'net reason');
        }

        if (!empty($arrRet['data'])) {
            Bingo_Log::pushNotice("{$this->_strForumName}", 'register');
            throw new Util_Exception(Util_Def::ERR_FORUM_HAS_REGISTER_ERROR, 'forum has registered');
        }*/

        // 拉取吧主数据
        $arrParam = array (
            'forum_id' => $this->_intForumId,
        );
        $arrRet   = Tieba_Service::call('perm', 'getManagerList', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            $strMsg = sprintf('call perm::getManagerList fail with input [%s] output [%s]', serialize($arrParam), serialize($arrRet));
            Bingo_Log::warning($strMsg);
            throw new Util_Exception(Util_Def::ERR_NET_REASON_ERROR, 'net reason');
        }

        $this->_arrManagerList = $arrRet['output'];
        if (empty($this->_arrManagerList)) {
            throw new Util_Exception(Util_Def::ERR_NO_MANAGER_ERROR, 'forum without manager');
        }
    }

    protected function _execute () {
        $intTableIndex = $this->_intForumId % Util_Def::FORUM_LIST_SIZE;
        $strTableName  = sprintf('%s_%d', Util_Def::FORUM_POOL, $intTableIndex);
        $strContent    = sprintf('%s_%d', Util_Def::FORUM_CONTENT, $this->_intForumId);
        $arrParam      = array (
            'key'   => $strTableName,
            'field' => $strContent,
            'value' => 1,
        );
        $arrRet        = Tieba_Service::call('generalmessage', 'updateSingleValue', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            $strMsg = sprintf('call generalmessage::updateSingleValue fail with input [%s] output [%s]', serialize($arrParam), serialize($arrRet));
            Bingo_Log::fatal($strMsg);
            throw new Util_Exception(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $this->_buildNetLog();
        /*
		// for testing
		$this->_arrManagerList[] = array(
			'user' => array(
				'user_id' => 2605,
			),
        );
         */
        // for testing over
        // 给吧主发信息
        /*$objRalMulti = new Tieba_Multi('star');
        foreach ($this->_arrManagerList as $key => $arrItem) {
            $arrInput['req'] = array(
                'category_id' => 1,
                'user_id'     => $arrItem['user']['user_id'],
                'title'       => self::TITLE,
                'content'     => self::CONTENT,
                'call_from'   => 'star',
            );
            $arrMultiInput = array(
                'serviceName' => 'sysmsg',
                'method'      => 'sendSysmsg',
                'input'       => $arrInput,
                'ie'          => 'utf-8',
            );
            $objRalMulti->register($arrItem['user_id'], new Tieba_Service('sysmsg'), $arrMultiInput);
        }
        $objRalMulti->call();
        foreach($this->_arrManagerList as $key => $arrItem) {
            $arrResult = $objRalMulti->getResult($arrItem['user_id']);
            if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrResult['errno']) {
                $strMsg = sprintf('call sysmsg::sendSysmsg fail with input user_id %d with output [%s]', $arrItem['user_id'], serialize($arrResult));
                Bingo_Log::warning($strMsg);
            }
		}*/
    }

    protected function _buildNetLog () {
        Tieba_Stlog::addNode('urlkey', 'star_fans_party_invite');
        Tieba_Stlog::addNode('forum_id', $this->_intForumId);
        Tieba_Stlog::addNode('forum_name', $this->_strForumName);
    }
}

?>
