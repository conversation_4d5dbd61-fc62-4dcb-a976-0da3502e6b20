<?php
Tieba_Init::init('tbs');

define('MODULE_UI_PATH',     dirname(__FILE__));
define('MODULE_ACTION_PATH', MODULE_UI_PATH.'/actions');
define('MODULE_LIBS_PATH',   MODULE_UI_PATH.'/libs');

set_include_path(get_include_path() . PATH_SEPARATOR . MODULE_LIBS_PATH);

function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) .'.php';
}
spl_autoload_register('__autoload');

if (!defined('IS_ORP_RUNTIME')){
	define('IS_ORP_RUNTIME', true);
}

$objFrontController = Bingo_Controller_Front::getInstance(array(
    "actionDir" => MODULE_ACTION_PATH,
    "defaultRouter" => "index",
    "notFoundRouter" => "error",
    "beginRouterIndex" => 1
));

$objFrontController->addStaticRouter('get', 'get');

try{
	$objFrontController->dispatch();
}catch(Exception $e){
    Bingo_Log::warning(sprintf('main process failure!HttpRouter=%s,error[%s]file[%s]line[%s]',
        Bingo_Http_Request::getStrHttpRouter(), $e->getMessage(), $e->getFile(), $e->getLine()));
    Bingo_Page::setErrno($e->getCode());
    header('location:http://static.tieba.baidu.com/tb/error.html');
}

$strTimeLog = Bingo_Timer::toString();
if (! empty($strTimeLog)) {
	Bingo_Log::pushNotice("timer", $strTimeLog);
}

Bingo_Log::buildNotice();
?>
