<?php
class dpackAction extends CapiAction
{
	private $_result   = array();
	private $_packinfo = array();

	protected $bolCheckUserLogin = false;
	protected function _execute()
	{
		if ( CapiRequest::$intClientType != CapiDef::CLIENT_TYPE_IPHONE ){
			if (!CapiRequest::$bolLogin) {
				Tieba_Stlog::addNode('errno',CapiErrno::USER_NOT_LOGIN);
				$this->_error(CapiErrno::USER_NOT_LOGIN,CapiError::USER_NOT_LOGIN,array(),false);
				return false;
			}
		}

		Tieba_Stlog::addNode('user_id',CapiRequest::$intUid);
		Tieba_Stlog::addNode('user_name',Encode::convertUTF8ToGBK(CapiRequest::$strUname));

		if ($this->_input() === false){
			return false;
		}
		if ($this->_process() === false){
			return false;
		}
		$this->_build();
	}

	private function _input()
	{
		$this->_req['pid'] = intval(Bingo_Http_Request::get('pid', 0));
		Tieba_Stlog::addNode('pack_id',$this->_req['pid'] );
		if($this->_req['pid'] <= 0) {
			Bingo_Log::warning('param is invalid, pid is ['.$this->_req['pid'].']');
			Tieba_Stlog::addNode('errno',CapiErrno::PARAM_NOT_ENOUGH );
			$this->_error(CapiErrno::PARAM_NOT_ENOUGH, CapiError::PARAM_NOT_ENOUGH);
			#$this->_returnData(Tieba_Errcode::ERR_PARAM_ERROR);
			return false;
		}  
		if( CapiRequest::$intClientType == CapiDef::CLIENT_TYPE_ANDROID ){
			$this->_req['client_type'] = CapiDef::CLIENT_TYPE_ANDROID;
		}
		if( CapiRequest::$intClientType == CapiDef::CLIENT_TYPE_IPHONE || CapiRequest::$intClientType == CapiDef::CLIENT_TYPE_IPAD ){
			$this->_req['client_type'] = CapiDef::CLIENT_TYPE_IPHONE;
			$this->_req['cuid'] = CapiRequest::$strCuid;
		}
		return true;
	}

	private function _process(){

		$intPid      = 0;
		$strEngPname = '';
		$this->_result = array();

		// get packinfo by pid
		$arrPids       = array(intval($this->_req['pid']));
		$bolShowFace   = false;
		$intClientType = intval($this->_req['client_type']);// 1 ios 2 andriod	
		$intSize       = intval(1); //根据分辨率确定大小值

		$arrPacksListInfo = MoFacesService::getPacksListInfo($arrPids,$bolShowFace,$intClientType,$intSize);	
		if ($arrPacksListInfo === false || intval($arrPacksListInfo['errno']) > 0  ){
			Bingo_Log::warning(sprintf("Failed to get pack info by pid,input:[%s]",serialize($arrPacksListInfo)));
			$this->_returnData( Tieba_Errcode::ERR_FACES_GET_PACK_INFO_BY_PID );
			return false;
		}
		$this->_packinfo = $arrPacksListInfo[intval($this->_req['pid'])];

		//get perchase info by user_id
		$arrInput['client_type'] = intval( $this->_req['client_type']);
		$arrInput['user_id']     = intval( CapiRequest::$intUid );
		$arrInput['cuid']        = trim( CapiRequest::$strCuid );
		$arrInput['pid']         = intval(  $this->_req['pid'] );

		$arrDpackInfo = MoFacesService::getOwnerFacesPack($arrInput);

		if ( isset($arrDpackInfo[intval($arrInput['pid'])]['pid']) && $arrDpackInfo[intval($arrInput['pid'])]['pid'] == 0 ){
			Bingo_Log::warning(sprintf("the pack status is not bought,please to buy first,output:[%s]",serialize($arrDpackInfo)));
			$this->_returnData( Tieba_Errcode::ERR_FACES_PACK_NOT_BUY );
			return false;
		}

		foreach( $arrDpackInfo  as $item ){
		    $arrBuyFacesListResult[intval($item['pid'])] = true;
		}           
			
		//fix under carriage
		$bolGetAllFacepack = MoFacesService::checkUserPower();
		if ( ! $bolGetAllFacepack ){
			$intPidTmp = intval($this->_req['pid']);
			if ( isset($arrPacksListInfo[$intPidTmp]['status']) && intval($arrPacksListInfo[$intPidTmp]['status']) == 0 ) {
			    if ( $arrBuyFacesListResult[$intPidTmp] != true ){ 
				Bingo_Log::warning(sprintf("the pid had down carriage,input:[%s]",serialize($arrPacklist)));
				$this->_returnData( Tieba_Errcode::ERR_FACES_PACK_DOWN_CARRIAGE );
				return false;
			    }       
			}elseif( intval($arrPacksListInfo[$intPidTmp]['ios_online'])==0 && intval( $this->_req['client_type'])==1 ){
			    if ( $arrBuyFacesListResult[$intPidTmp] != true ){ 
				Bingo_Log::warning(sprintf("the pid ios version had down carriage,input:[%s]",serialize($arrPacklist)));
				$this->_returnData( Tieba_Errcode::ERR_FACES_PACK_DOWN_CARRIAGE );
				return false;
			    }       
			
			}elseif( intval($arrPacksListInfo[$intPidTmp]['android_online'])==0 && intval( $this->_req['client_type']) ==2 ){
				
			    if ( $arrBuyFacesListResult[$intPidTmp] != true ){ 
				Bingo_Log::warning(sprintf("the pid andriod version had down carriage,input:[%s]",serialize($arrPacklist)));
				$this->_returnData( Tieba_Errcode::ERR_FACES_PACK_DOWN_CARRIAGE );
				return false;
			    }       
			}     
		}

		// add pack download record
		$arrInput['client_type'] = intval( $this->_req['client_type']);
		$arrInput['user_id']     = intval( CapiRequest::$intUid );
		$arrInput['cuid']        = trim( CapiRequest::$strCuid );
		$arrInput['pid']         = intval( $this->_packinfo['pid'] );
		$arrInput['price']       = intval( $this->_packinfo['price'] );

		Tieba_Stlog::addNode('cuid',trim( CapiRequest::$strCuid ) );
		Tieba_Stlog::addNode('price',intval( $this->_packinfo['price'] ));

		$arrRes = MoFacesService::addDpackhis($arrInput);		
		if ($arrRes === false || intval($arrRes['errno']) > 0  ){
			Bingo_Log::warning(sprintf("Failed to add pack history,input:[%s]",serialize($arrInput)));
		}

		$this->_returnData(Tieba_Errcode::ERR_SUCCESS,$this->_packinfo );
		return true;
	}

	private function _build(){
	}

	/**
	 * 返回值统一处理
	 *
	 */
	private function _returnData($intErrno,$arrInput=false){
		// get size by screnn
		$intSize = MoFacesService::getSizeByResolution();
		
		Tieba_Stlog::addNode('size',$intSize );
		Tieba_Stlog::addNode('errno',$intErrno );

		$this->arrData['errno']    = $intErrno;
		$this->arrData['error_code']    = $intErrno;
		$this->arrData['errmsg']   = Tieba_Error::getErrmsg($intErrno);
		$this->arrData['usermsg']  = Encode::convertGBKToUTF8( Tieba_Error::getUserMsg($intErrno));
		if ($arrInput != false){	
			$this->arrData['pack_url']=($intSize>1)? trim($arrInput['pack2x_url']): trim($arrInput['pack_url']) ;
		}else{
			$this->arrData['pack_url'] = '';
		}
	}
}
