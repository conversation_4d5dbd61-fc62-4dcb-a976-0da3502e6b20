<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-02-05 19:25:09
 * @comment 测试调用Kvstorage存取数据
 * @version
 */
class testKvstorageAction extends Util_Base { //@change：改class名、需与文件名相同、并都以Action结尾
    const PROJECT_NAME = 'sample'; //@change：改为自己业务所在的目录名

    /**
     * 调用kvstorage存数据
     * @param $key string
     * @param $value * 任意类型, int, string, array...
     * @return bool | * , false on loose, $value on success
     */
    private function _set($key, $value) {
        $arrInput = array(
            'project'=> PROJECT_NAME,
            'key'=> $key,
            'value'=> $value,
        );
        $arrOutput = Tieba_Service::call(
            'creative', 'kvStorageSet',
            $arrInput,
            NULL, NULL, 'post', 'php', 'gbk',
            'local' //没有service的app、走本地调用
        );

        if($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            $strLog = "call creative kvStorageSet errno!=0. input=".serialize($arrInput).
                " output=".serialize($arrOutput);
            Bingo_Log::warning($strLog);
            return false;
        }

        return $arrOutput['data'];
    }

    /**
     * 调用kvstorage取数据
     * @param $key string
     * @return bool | * false on loose, $value on success
     */
    private function _get($key) {
        $arrInput = array(
            'project'=> PROJECT_NAME,
            'key'=> $key,
        );
        $arrOutput = Tieba_Service::call(
            'creative', 'kvStorageGet',
            $arrInput,
            NULL, NULL, 'post', 'php', 'gbk',
            'local' //没有service的app、走本地调用
        );

        if($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            $strLog = "call creative kvStorageGet errno!=0. input=".serialize($arrInput).
                " output=".serialize($arrOutput);
            Bingo_Log::warning($strLog);
            return false;
        }

        return  $arrOutput['data'];
    }

    public function execute(){
        try {
            // 参数获取
            $key = strval(Bingo_Http_Request::getGet('key',''));
            $value = Bingo_Http_Request::getPost('value');
            if (!isset($value)) {
                $value = Bingo_Http_Request::getGet('value');
            }

            if (!$key) {
                $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, "param error: please input key");
                return;
            }

            //@change change to your own code
            // 存或取值
            if (isset($value)) { //存值
                // 作为示例，将value的类型与其值存入redis
                if (intVal($value)) {
                    $redisValue = array("type" => "int", "value" => intVal($value));
                } else {
                    $redisValue = array("type" => "string", "value" => $value);
                }
                $serviceData = $this->_set($key, $redisValue);
            } else { //取值
                $serviceData = $this->_get($key);
            }

            // service错误处理
            if ($serviceData === false) {
                $this->_jsonRet(Tieba_Errcode::ERR_NVOTE_CALL_SERVICE_ERROR, Tieba_Error::getUserMsg(Tieba_Errcode::ERR_NVOTE_CALL_SERVICE_ERROR));
                return;
            }

            // 正常返回
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,Tieba_Error::getUserMsg(Tieba_Errcode::ERR_SUCCESS),$serviceData);
        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), '未知错误');
        }
    }
}
?>
