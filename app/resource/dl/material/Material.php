<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file Material.php
 * <AUTHOR>
 * @date 2016/03/09 17:49:06
 * @brief material dl
 *  
 **/

define("MODULE", "Resource_dl");
class Dl_Material_Material {
    private static $_db           = null;  
    protected static $_conf       = null;

    const SERVICE_NAME = "Dl_Material_Material";

    const DB_NAME = 'forum_resource';

    //待审核/待上线/已上线/已下线/未过审/已关闭
    const THROW_STATUS_PENDING_AUDIT = 1;
    const THROW_STATUS_BEFORE_ONLINE = 2;
    const THROW_STATUS_ONLINE = 5;
    const THROW_STATUS_OFFLINE = 4;
    const THROW_STATUS_AUDIT_NOT_PASS = 3;
    const THROW_STATUS_CLOSE = 6;
    const THROW_STATUS_CLOSE_ADVERTISER_OFFLINE = 7;


    const SEARCH_TYPE_BY_CREATE_USER_ID = 2;
    const SEARCH_TYPE_BY_AD_NAME = 1;
    const SEARCH_TYPE_BY_ORDER_ITEM_ID = 3;



    /**
     * @brief init
     * @return: true if success. false if fail.
    **/		
    private static function _init() {
        if (self::$_conf == null) {
            self::$_conf = Bd_Conf::getConf("/app/official/dl_test_test");
            if (self::$_conf == false) {
                Bingo_Log::warning("init get conf fail.");
                return false;
            }
        }
        return true; 
    }


    /**
     * @param $errno
     * @param null $data
     * @return array
     */
    private static function _errRet($errno, $data = null) {
        return array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data'   => $data,
        );
    }


    public static function preCall($arrInput) {
        // pre-call hook
    }

    public static function postCall($arrInput) {
        // post-call hook
    }

    
    public static function getDB() {
        $objTbMysql = Tieba_Mysql::getDB(self::DB_NAME);
        if ($objTbMysql && $objTbMysql->isConnected()) {
            return $objTbMysql;
        } else {
            Bingo_Log::warning("db connect fail.");
            return null;
        }
    }

    public function updateThrow($arrInput = array()) {
        $objDb = Util_Schedule_Db::getDB();
        $objDb->startTransaction();
        //update order item status to ORDER_STATUS_ABANDONED
        $arrOrderInput = array(
            'db'            => $objDb,
            'order_item_id' => $arrInput['order_item_id'],
            'status'        => Util_Schedule_Def::ORDER_STATUS_ABANDONED,
            'op_user_id'    => $arrInput['op_user_id'],
        );
        $arrOrderRes = self::updateOrderItemStatus($arrOrderInput);
       
        $arrInput['db'] = $objDb;
        $arrThrowRes = self::createThrow($arrInput);
        $intOrderItemId = $arrInput['order_item_id'];
        if (false === $objDb->commit()) {
            Bingo_Log::warning("commit fail and order_item_id =[" . $intOrderItemId . "]");
            if (false === $objDb->rollback()) {
                Bingo_Log::warning("rollback fail and order_item_id =[" . $intOrderItemId. "]");
            }
            return false;
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrThrowRes['data']);
    }
    
    //material
    public function createThrow($arrInput = array()) {
        if (isset($arrInput['db'])) {
            $objDb = $arrInput['db'];
        } else {
            $objDb = Util_Schedule_Db::getDB();
        }
        $objDb->startTransaction();
        $intCurTime = time();

      /*
        'material_name'  => $objThrow->getMaterialName(),
        'material_info'  => $objThrow->getMaterialInfo(),
        'res_id'         => $objThrow->getResId(),
        'advertiser_id'  => $objThrow->getAdvertiserId(),
        'operator_id'    => $objThrow->getOperatorId(),
        'order_type'     => $objThrow->getOrderType(),
        'create_user_id' => $objThrow->getCreateUserId(),
        'op_user_id'     => $objThrow->getOpUserId(),
     */

        //create quote id
        $arrInsertInput = array(
            'db'    => $objDb,
            'table' => 'resource_price_relation',
            'field' => array(
                'resource_ids' => strval($arrInput['res_id']),
                'op_time'      => $intCurTime,
                //'op_user_name' => $arrInput['op_user_name'],
            ),
        );
        $arrRes = Util_Schedule_Db::insert($arrInsertInput);
        $intQuoteId = $arrRes['data'];


        //create commercial_order
        $arrInsertInput = array(
            'db'    => $objDb,
            'table' => 'commercial_order',
            'field' => array(
                'quote_id'         => $intQuoteId,
                'create_user_id'   => intval($arrInput['create_user_id']),
                //'create_user_name' => $arrInput['op_user_name'],
                'create_time'      => $intCurTime,
                'op_user_id'       => intval($arrInput['op_user_id']),
                //'op_user_name'     => $arrInput['op_user_name'],
                'op_time'          => $intCurTime,
            ),
        );
        $arrRes = Util_Schedule_Db::insert($arrInsertInput);
        $intOrderId = $arrRes['data'];
       
        //create commercial_order_item
        $arrItemInput = array();
        $intCombineId = $intQuoteId * 10000 + intval($arrInput['res_id']);
        $arrItemInput['data'][] = array(
            'res_type'       => intval($arrInput['res_type']),
            'order_id'       => $intOrderId,
            'order_type'     => $arrInput['order_type'],
            'operator_id'    => $arrInput['operator_id'],
            'advertiser_id'  => $arrInput['advertiser_id'],
            'quote_id'       => $intQuoteId,
            'resource_id'    => $arrInput['res_id'],
            'combine_id'     => $intCombineId,
            'status'         => Util_Schedule_Def::ORDER_STATUS_PENDING_AUDIT,
           // 'create_user_name' => strval($arrInput['create_user_name']),
            'create_user_id' => intval($arrInput['create_user_id']),
            'create_time'    => $intCurTime,
           // 'op_user_name' => strval($arrInput['op_user_name']),
            'op_user_id'     => intval($arrInput['op_user_id']),
            'op_time'        => $intCurTime,
            //'apply_user_name' => strval($arrInput['create_user_name']),
            //'apply_user_id' => strval($arrInput['create_user_id']),
           // 'apply_time' => $intCurTime,
        );
        $arrItemInput['db'] = $objDb;
        $arrItemRes = Dl_Dbschedule_Dbschedule::multiCreateOrderItem($arrItemInput);
        
        
        //create material
        $arrMaterialInput = array(
            'db'    => $objDb,
            'table' => 'commercial_material',
            'field' => array(
                'order_item_id' => $intCombineId,
                'res_id'        => intval($arrInput['res_id']),
                'material_name' => $arrInput['material_name'],
                'material_info' => Bingo_String::array2json($arrInput['material_info'], Bingo_Encode::ENCODE_UTF8),
                'preview_url'   => Bingo_String::array2json($arrInput['preview_url'], Bingo_Encode::ENCODE_UTF8),
            ),
        );
        $arrRes = Util_Schedule_Db::insert($arrMaterialInput);

        //create price record total
        //if (!empty($arrInput['data'])) {
        $arrReplaceInfo['field'] = array(
            'quote_id',
            'resource_id',
            'combine_id',
            'forum_id',
            'resource_name',
            'balance_type',
            'discount',
            'evaluation',
            'ctr',
            'start_time',
            'end_time',
            'day',
            'sum',
            'discount_sum',
            'create_time',
            'create_user_name',
            'op_time',
            'op_user_name',
            'res_type',
            'data_type',
            'extra_info',
        );
        $arrReplaceInfo['data'][] = array(
            $intQuoteId,
            $arrInput['res_id'],
            $intCombineId,
            $arrInput['forum_id'][0],
            $arrInput['res_name'],
            0,
            0,
            0,
            0,
            $arrInput['start_time'],
            $arrInput['end_time'],
            0,
            0,
            0,
            $intCurTime,
            '',
            $intCurTime,
            '',
            $arrInput['res_type'],
            1,
            '',
        );
    
        $arrInsertInput = array(
            'db'    => $objDb,
            'table' => 'price_record_total',
            'field' => $arrReplaceInfo['field'],
            'data'  => $arrReplaceInfo['data'],
        );
        $arrInsertRes = Util_Db::replace($arrInsertInput);
        /*if(false === $arrInsertRes || Tieba_Errcode::ERR_SUCCESS !==intval($arrInsertRes['errno'])) {
            Bingo_Log::warning("call replace method  fail input[" .serialize($arrInsertInput) ."] out[" .serialize($arrInsertRes) ."]");
            return false;
        }*/


        //create price_record
        foreach ($arrInput['client_type'] as $intClientType) {
            foreach ($arrInput['page'] as $intPage) {
                foreach ($arrInput['forum_id'] as $intForumId) {
                    $arrPriceInput['data'][] = array(
                        'quote_id'      => $intQuoteId,
                        'resource_id'   => $arrInput['res_id'],
                        'res_type'      => $arrInput['res_type'],
                        'combine_id'    => $intCombineId,
                        'resource_name' => $arrInput['res_name'],
                        'platform'      => $intClientType,
                        'location'      => $intPage,
                        'region'        => 1,
                        'dimension'     => 1,
                        'forum_id'      => $intForumId,
                       // 'forum_name'    => ,
                        'first_dir',
                        'second_dir',
                        'start_time'    => $arrInput['start_time'],
                        'end_time'      => $arrInput['end_time'],
                        'create_time'   => $intCurTime,
                        //'create_user_name',
                        'op_time'       => $intCurTime,
                        //'op_user_name',
                    );
                }
            }
        }
        $arrPriceInput['db'] = $objDb;
        $arrRes = Dl_Price_Price::multiCreatePriceRecord($arrPriceInput);
        if (false === $arrRes) {
            return false;
        }

        if (false === $objDb->commit()) {
            Bingo_Log::warning("commit fail and combine_id =[" . $intCombineId . "]");
            if (false === $objDb->rollback()) {
                Bingo_Log::warning("rollback fail and combine_id =[" . $intCombineId. "]");
            }
            return false;
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $intCombineId);
    }


    //material
    public function createDirectCustomerThrow($arrInput = array()) {
        if (isset($arrInput['db'])) {
            $objDb = $arrInput['db'];
        } else {
            $objDb = Util_Schedule_Db::getDB();
        }
        $objDb->startTransaction();
        $intIsOnlyDimChanged = intval($arrInput['is_only_dim_changed']);

        //material also changed,update status to audit pending
        if (0 === $intIsOnlyDimChanged) {
            /*$arrOrderInput = array(
                'combine_id' => $arrInput['order_item_id'],
            );
            $arrOrderRes = Dl_Dbschedule_Dbschedule::getOrderItemInfo($arrOrderInput);
            if (Tieba_Errcode::ERR_SUCCESS !== $arrOrderRes['errno']) {
                Bingo_Log::warning('call Dl_Dbschedule_Dbschedule::getOrderItemInfo fail' . serialize($arrOrderInput) . '_' . serialize($arrOrderRes));
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            $arrOrderData = $arrOrderRes['data'][0];
            $intCurStatus = intval($arrOrderData['status']);*/
            //if (Util_Schedule_Def::ORDER_STATUS_AUDIT_PASS !== $intCurStatus) {
            $arrUpInput = array(
                'status'         => Util_Schedule_Def::ORDER_STATUS_PENDING_AUDIT,
                'op_user_id'     => $arrInput['op_user_id'],
                'order_item_id'  => $arrInput['order_item_id'],
                'commit_user_id' => $arrInput['op_user_id'],
                'commit_time'    => time(),
            );
            $arrRes = self::updateOrderItemStatus($arrUpInput);
            //}
            
            //delete previous material
            $arrDeleteInput = array(
                'db'    => $objDb,
                'table' => 'commercial_material',
                'cond'  => array(
                    'order_item_id=' => $arrInput['order_item_id'],
                ),
            );
            $arrDeleteRes = Util_Schedule_Db::delete($arrDeleteInput);

            //delete previous material item
            $arrDeleteInput = array(
                'db'    => $objDb,
                'table' => 'commercial_material_item',
                'cond'  => array(
                    'order_item_id=' => $arrInput['order_item_id'],
                    'status'         => array(1, 2),
                ),
            );
            $arrDeleteRes = Util_Schedule_Db::delete($arrDeleteInput);

            //replace material
            $arrField = array(
                'order_item_id',
                'res_id',
                'material_name',
                //'material_info',
                //'preview_url',
                //'op_user_id',
               //'op_time',
                //'status',
                //'update_time',
            );
            //foreach ($arrInput['material_info'] as $value) {
                $arrData[] = array(
                    $arrInput['order_item_id'],
                    $arrInput['res_id'],
                    $arrInput['material_name'],
                   //Bingo_String::array2json($value['material'], Bingo_Encode::ENCODE_UTF8),
                   // Bingo_String::array2json($value['preview_token'], Bingo_Encode::ENCODE_UTF8),
                   // $arrInput['op_user_id'],
                   // time(),
                   // 1,  //待审核
                   // $value['update_time'],
                );
            //}
            $arrMaterialInput = array(
                'db'    => $objDb,
                'table' => 'commercial_material',
                /*'field' => array(
                    'order_item_id' => $arrInput['order_item_id'],
                    'res_id'        => intval($arrInput['res_id']),
                    'material_name' => $arrInput['material_name'],
                    'material_info' => Bingo_String::array2json($arrInput['material_info'], Bingo_Encode::ENCODE_UTF8),
                    'preview_url'   => Bingo_String::array2json($arrInput['preview_url'], Bingo_Encode::ENCODE_UTF8),
                ),*/
                'field' => $arrField,
                'data'  => $arrData,
            );
            $arrRes = Util_Schedule_Db::replace($arrMaterialInput);


            //replace material item
            $arrField = array(
                'order_item_id',
                'res_id',
                'material_name',
                'material_info',
                'preview_url',
                'op_user_id',
                'op_time',
                'status',
                'update_time',
            );
            $arrData = array();
            foreach ($arrInput['material_info'] as $value) {
                $arrData[] = array(
                    $arrInput['order_item_id'],
                    $arrInput['res_id'],
                    $arrInput['material_name'],
                    Bingo_String::array2json($value['material'], Bingo_Encode::ENCODE_UTF8),
                    Bingo_String::array2json($value['preview_token'], Bingo_Encode::ENCODE_UTF8),
                    $arrInput['op_user_id'],
                    time(),
                    1,  //待审核
                    $value['update_time'],
                );
            }
            $arrMaterialInput = array(
                'db'    => $objDb,
                'table' => 'commercial_material_item',
                /*'field' => array(
                    'order_item_id' => $arrInput['order_item_id'],
                    'res_id'        => intval($arrInput['res_id']),
                    'material_name' => $arrInput['material_name'],
                    'material_info' => Bingo_String::array2json($arrInput['material_info'], Bingo_Encode::ENCODE_UTF8),
                    'preview_url'   => Bingo_String::array2json($arrInput['preview_url'], Bingo_Encode::ENCODE_UTF8),
                ),*/
                'field' => $arrField,
                'data'  => $arrData,
            );
            $arrRes = Util_Schedule_Db::replace($arrMaterialInput);

        }
        
        
        
        //get previous dim info
        $arrDimInfo = self::getDimensionInfo(array('combine_id' => array($arrInput['order_item_id'])));
        $intCombineId = $arrInput['order_item_id'];
        $arrId = array();
        foreach ($arrDimInfo['data'] as $value) {
            //range
            $intDimension = intval($value['dimension']);
            //forum
            if (1 === $intDimension) {
                if (!in_array($value['forum_id'], $arrInput['range']['forum'])) {
                    $arrId[] = $value['id'];
                }
            }
            //first dir
            if (2 === $intDimension) {
                if (!in_array($value['first_dir'], $arrInput['range']['first_dir'])) {
                    $arrId[] = $value['id'];
                }

            }
            //second dir
            if (3 === $intDimension) {
                $strSecDir = $value['first_dir'] . '-' . $value['second_dir'];
                if (!in_array($strSecDir, $arrInput['range']['second_dir'])) {
                    $arrId[] = $value['id'];
                }

            }
            //platform
            if (!in_array($value['platform'], $arrInput['client_type'])) {
                $arrId[] = $value['id'];
            }
            //page
            if (!in_array($value['location'], $arrInput['page'])) {
                $arrId[] = $value['id'];
            }

        }
        //update price_record dim info
        $arrRes = Tieba_Service::call('user', 'getUnameByUids', array('user_id' => array($arrInput['op_user_id'])), NULL, NULL, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning('call user getUnameByUids fail_' . serialize($arrPass) . '_' . serialize($arrRes));
        }
        $strOpUserName = $arrRes['output']['unames'][0]['user_name'];
        if (!empty($arrId)) {
            $arrUpdateInput = array(
                'table' => 'price_record',
                'field' => array(
                    'deleted_status=' => 1, //deleted
                    'op_user_name='   => $strOpUserName,
                    'op_time='        => time(),   
                ),
                'cond'  => array(
                    'id' => $arrId,
                ),
            );
            $arrRes = Util_Schedule_Db::update($arrUpdateInput);
        }

        if (false === $objDb->commit()) {
            Bingo_Log::warning("commit fail and combine_id =[" . $intCombineId . "]");
            if (false === $objDb->rollback()) {
                Bingo_Log::warning("rollback fail and combine_id =[" . $intCombineId. "]");
            }
            return false;
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $intCombineId);
    }

    

    private function _combineData($arrDimData) {
        $arrDimInfo = array();
        foreach ($arrDimData as $value) {
            $intOrderItemId = $value['combine_id'];
            $arrDimInfo[$intOrderItemId]['res_id'] = $value['resource_id'];
            $arrDimInfo[$intOrderItemId]['res_name'] = $value['resource_name'];

            if (!in_array($value['location'], $arrDimInfo[$intOrderItemId]['page'])
                && !empty($value['location'])) {
                $arrDimInfo[$intOrderItemId]['page'][] = $value['location'];
            }

            if (!in_array($value['platform'], $arrDimInfo[$intOrderItemId]['client_type'])
                && !empty($value['platform'])) {
                 $arrDimInfo[$intOrderItemId]['client_type'][] = $value['platform'];
            }


            if (!in_array($value['forum_id'], $arrDimInfo[$intOrderItemId]['range']['forum'])
                && !empty($value['forum_id'])) {
                $arrDimInfo[$intOrderItemId]['range']['forum'][] = $value['forum_id'];
            }
           
            if (!in_array($value['first_dir'], $arrDimInfo[$intOrderItemId]['range']['first_dir']) 
                && !empty($value['first_dir'])) {
                if (!in_array($value['second_dir'], $arrDimInfo[$intOrderItemId]['range']['second_dir']) 
                    && !empty($value['second_dir'])) {
                    $arrDimInfo[$intOrderItemId]['range']['second_dir'][] = $value['first_dir'] . '-' . $value['second_dir'];
                }
                $arrDimInfo[$intOrderItemId]['range']['first_dir'][] = $value['first_dir'];
            }
            $arrDimInfo[$intOrderItemId]['time']['start_time'] = $value['start_time'];
            $arrDimInfo[$intOrderItemId]['time']['end_time'] = $value['end_time'];
            $arrDimInfo[$intOrderItemId]['res_type'] = $value['res_type'];
            $arrDimInfo[$intOrderItemId]['combine_id'] = $value['combine_id']; 
            $arrDimInfo[$intOrderItemId]['dimension'] = $value['dimension']; 

            //new 
            $arrDimInfo[$intOrderItemId]['contract_id'] = $value['contract_id'];  
            $arrDimInfo[$intOrderItemId]['apply_user_id'] = $value['apply_user_id'];  
            $arrDimInfo[$intOrderItemId]['apply_user_name'] = $value['apply_user_name'];  
            $arrDimInfo[$intOrderItemId]['apply_time'] = $value['apply_time']; 
            $arrDimInfo[$intOrderItemId]['op_user_name'] = $value['op_user_name']; 
            $arrDimInfo[$intOrderItemId]['op_user_id'] = $value['op_user_id'];
            $arrDimInfo[$intOrderItemId]['res_name'] = $value['resource_name']; 
            $arrDimInfo[$intOrderItemId]['status'] = $value['status'];  
        }
        return $arrDimInfo;
    }




    public function updateOrderItemStatus($arrInput = array()) {
        $arrUpdateInput = array(
            'table' => 'commercial_order_item',
            'field' => array(
                'status='     => $arrInput['status'],
                'op_user_id=' => $arrInput['op_user_id'],
                'op_time='    => time(),   
            ),
            /*'cond'  => array(
                'combine_id=' => intval($arrInput['order_item_id']),
            ),*/
        );
        if (is_array($arrInput['order_item_id'])) {
            $arrUpdateInput['cond']['combine_id'] = $arrInput['order_item_id'];
        } else {
            $arrUpdateInput['cond']['combine_id='] = $arrInput['order_item_id'];
        }
        if (isset($arrInput['db'])) {
            $arrUpdateInput['db'] = $arrInput['db'];
        }
        if (isset($arrInput['ext_info'])) {
            $arrUpdateInput['field']['ext_info='] = $arrInput['ext_info'];
        }
        if ($arrInput['commit_time']) {
            $arrUpdateInput['field']['commit_time='] = $arrInput['commit_time'];
        }
        if ($arrInput['commit_user_id']) {
            $arrUpdateInput['field']['commit_user_id='] = $arrInput['commit_user_id'];
        }
        if ($arrInput['audit_time']) {
            $arrUpdateInput['field']['audit_time='] = $arrInput['audit_time'];
        }
        if ($arrInput['audit_user_id']) {
            $arrUpdateInput['field']['audit_user_id='] = $arrInput['audit_user_id'];
        }
        $arrRes = Util_Schedule_Db::update($arrUpdateInput);
        return $arrRes;
    }

    //set has read
    public function setHasRead($arrInput = array()) {
        $arrUpdateInput = array(
            'table' => 'commercial_order_item',
            'field' => array(
                'has_read='   => $arrInput['has_read'],
            ),
            'cond'  => array(
                'combine_id=' => intval($arrInput['order_item_id']),
            ),
        );
        $arrRes = Util_Schedule_Db::update($arrUpdateInput);
        return $arrRes;
    }

    //set all has read
    public function setAllHasRead($arrInput = array()) {
        if (0 === intval($arrInput['order_type'])) {
            $arrUpdateInput = array(
                'table' => 'commercial_order_item',
                'field' => array(
                    'has_read='   => $arrInput['has_read'],
                ),
                'cond'  => array(
                    'commit_user_id=' => $arrInput['op_user_id'],
                ),
            );
            $arrRes = Util_Schedule_Db::update($arrUpdateInput);
            return $arrRes;
        }
        $arrSelInput = array(
            'table' => 'commercial_order_item',
            'field' => array(
                'combine_id',
                'status',
            ), 
            'cond' => array(
                'has_read='    => 0,
                'order_type='  => 1,
                'operator_id=' => $arrInput['operator_id'],
            ),
        );
       
        $arrRes = Util_Schedule_Db::select($arrSelInput);
        $arrRet = array();
        $arrCombineId = array();
        foreach ($arrRes['data'] as $value) {
            $arrCombineId[] = $value['combine_id'];
        }
        if (empty($arrCombineId)) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $arrSelInput = array(
            'table' => 'price_record_total',
            'field' => array(
                'start_time',
                'end_time',
                'combine_id',
            ), 
            'cond' => array(
                'combine_id' => $arrCombineId,
                'forum_id '  => $arrInput['forum_id'],

            ),
        );
        $arrRes = Util_Schedule_Db::select($arrSelInput);
        $arrCombineId = array();
        foreach ($arrRes['data'] as $value) {
            $arrCombineId[] = $value['combine_id'];
        }
        if (empty($arrCombineId)) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }    

        //update has_read
        $arrUpdateInput = array(
            'table' => 'commercial_order_item',
            'field' => array(
                'has_read='   => $arrInput['has_read'],
            ),
            'cond'  => array(
                'combine_id' => $arrCombineId,
            ),
        );
        $arrRes = Util_Schedule_Db::update($arrUpdateInput);
        return $arrRes;
    }

    //get has read
    public function getHasRead($arrInput = array()) {
        $arrSelInput = array(
            'table' => 'commercial_order_item',
            'field' => array(
                'combine_id',
                'status',
            ), 
            'cond' => array(
                'has_read='    => 0,
                'order_type='  => 1,
                'status='      => Util_Schedule_Def::ORDER_STATUS_AUDIT_NOT_PASS,
                'operator_id=' => $arrInput['operator_id'],
            ),
        );
       
        $arrRes = Util_Schedule_Db::select($arrSelInput);
        $arrRet = array();
        $arrCombineId = array();
        foreach ($arrRes['data'] as $value) {
            $arrCombineId[] = $value['combine_id'];
        }
        if (empty($arrCombineId)) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $arrSelInput = array(
            'table' => 'price_record_total',
            'field' => array(
                'start_time',
                'end_time',
                'combine_id',
            ), 
            'cond' => array(
                'combine_id' => $arrCombineId,
                'forum_id='  => $arrInput['forum_id'],

            ),
        );
        $arrRes = Util_Schedule_Db::select($arrSelInput);
        $arrCombineId = array();
        foreach ($arrRes['data'] as $value) {
            $arrCombineId[] = $value['combine_id'];
        }
        if (empty($arrCombineId)) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        return $arrRes;    
    }



    public function getMaterialByOrderItemId($arrInput = array()) {
         if (0 === intval($arrInput['order_type'])) {
            $strTable = 'commercial_material_item';
            $arrField = array(
                'material_name',
                'material_info',
                'order_item_id',
                'id',
                'preview_url',
                'status',
                'op_user_id',
                'update_time',
                'reason',
            );
        } else {
            $strTable = 'commercial_material';
            $arrField = array(
                'material_name',
                'material_info',
                'order_item_id',
                'id',
                'preview_url',
                //'status',
                //'op_user_id',
                //'update_time',
               // 'reason',
            );
        } 
        $arrSelInput = array(
            'table' => $strTable,
            'field' => $arrField,
            'cond'  => array(
                'order_item_id=' => $arrInput['order_item_id'],
            ),
        );
        $arrSelRes = Util_Schedule_Db::select($arrSelInput);
        return $arrSelRes;
    }

    public function getThrowList($arrInput = array()) {
        $intOperatorId = $arrInput['operator_id'];
        $intForumId = $arrInput['forum_id'];
        $intOffset = $arrInput['offset'];
        $intLimit = $arrInput['limit'];
        $intCurTime = time();
        $intOrderType = isset($arrInput['order_type']) ? intval($arrInput['order_type']) : -1;
        
        //field
        $strField = "
            commercial_order_item.combine_id,
            
            commercial_order_item.res_id, 
            commercial_order_item.create_time, 
            commercial_order_item.op_time, 
            commercial_order_item.create_user_id,
            commercial_order_item.op_user_id,
            commercial_order_item.audit_user_name,
            commercial_order_item.audit_user_id,
            commercial_order_item.audit_time,
            commercial_order_item.commit_user_name,
            commercial_order_item.commit_user_id,
            commercial_order_item.commit_time,
            commercial_order_item.status,
            commercial_order_item.ext_info,
            price_record_total.start_time,
            price_record_total.end_time,
            
            price_record_total.resource_name,
            commercial_material.material_name,
            commercial_material.preview_url";

            //price_record_total.location,
            //price_record_total.platform,
            //price_record_total.forum_id,

        //where 
        if (0 === $intOrderType) {
            $strWhere = " where commercial_order_item.order_type = 0 and commercial_order_item.status in(9,10,11,12,13,15)";
        } else {
            $strWhere = " where commercial_order_item.operator_id = $intOperatorId and price_record_total.forum_id = $intForumId and commercial_order_item.order_type = 1 and commercial_order_item.status in(10,11,12,13,15)";
        }
        if (isset($arrInput['res_id'])) {
            $intResId = $arrInput['res_id'];
            $strWhere .= " and commercial_order_item.res_id = $intResId";
        }

        if (isset($arrInput['has_read'])) {
            $intHasRead = $arrInput['has_read'];
            $strWhere .= " and commercial_order_item.has_read = $intHasRead";
        }

        if (isset($arrInput['commit_user_id'])) {
            $intCommitUserId = $arrInput['commit_user_id'];
            $strWhere .= " and commercial_order_item.commit_user_id = $intCommitUserId";
        }


        if (isset($arrInput['search_type'])) {
            $intSearchType = intval($arrInput['search_type']);
            $strSearchWord = $arrInput['search_word'];
            //search by create_user_id 
            if (self::SEARCH_TYPE_BY_CREATE_USER_ID === $intSearchType) {
                if (0 === $intOrderType) {
                    $strWhere .= " and commercial_order_item.commit_user_id = $strSearchWord";
                } else {
                    $strWhere .= " and commercial_order_item.create_user_id = $strSearchWord";
                }
            }

            //search by ad_name
            if (self::SEARCH_TYPE_BY_AD_NAME === $intSearchType) {
                $strWhere .= " and commercial_material.material_name like '%$strSearchWord%'";
            }
            //search by  order_item_id
            if (self::SEARCH_TYPE_BY_ORDER_ITEM_ID === $intSearchType) {
                $strWhere .= " and commercial_material.order_item_id = $strSearchWord";
            }
            //$strWhere .= " and commercial_order_item.res_id = $intResId";
        }

        if (isset($arrInput['status'])) {
            $arrStatusMap = array(
                self::THROW_STATUS_PENDING_AUDIT  => Util_Schedule_Def::ORDER_STATUS_PENDING_AUDIT,
                self::THROW_STATUS_AUDIT_NOT_PASS => Util_Schedule_Def::ORDER_STATUS_AUDIT_NOT_PASS,
                self::THROW_STATUS_CLOSE          => array(
                    Util_Schedule_Def::ORDER_STATUS_CLOSE,
                    Util_Schedule_Def::ORDER_STATUS_CLOSE_ADVERTISER_OFFLINE,
                ),
            ); 
            $arrMapDirect = array_keys($arrStatusMap);

            $arrOtherStatus = array(
                self::THROW_STATUS_BEFORE_ONLINE,
                self::THROW_STATUS_ONLINE,
                self::THROW_STATUS_OFFLINE,
            );

            $intStatus = intval($arrInput['status']);
            if (in_array($intStatus, $arrMapDirect)) {
                $intMappedStatus = $arrStatusMap[$intStatus];
                if (!is_array($intMappedStatus)) {
                    $strWhere .= " and commercial_order_item.status = $intMappedStatus";
                } else {
                    $strStatus = implode(',', $intMappedStatus);
                    $strWhere .= " and commercial_order_item.status in ($strStatus)";
                }
                
            }
            if (in_array($intStatus, $arrOtherStatus)) {
                $intOrderStatus = Util_Schedule_Def::ORDER_STATUS_AUDIT_PASS;
                $strWhere .= " and commercial_order_item.status = $intOrderStatus";
                if ($intStatus === self::THROW_STATUS_BEFORE_ONLINE) {
                    $strWhere .= " and price_record_total.start_time > $intCurTime";
                }
                if ($intStatus === self::THROW_STATUS_ONLINE) {
                    $strWhere .= " and price_record_total.start_time <= $intCurTime and price_record_total.end_time >= $intCurTime";
                }
                if ($intStatus === self::THROW_STATUS_OFFLINE) {
                    $strWhere .= " and price_record_total.end_time < $intCurTime";
                }
            }
        }

        if (1 === $intOrderType) {
            $strOrder = " order by commercial_order_item.create_time desc";
        } else {
            $strOrder = " order by commercial_order_item.commit_time desc";
        }
        
        $strLimit = "limit $intOffset, $intLimit";
        $strJoin = "price_record_total  left join commercial_material on price_record_total.combine_id = commercial_material.order_item_id";
        //$strSql = "select $strField from commercial_order_item left join price_record on commercial_order_item.combine_id = price_record.combine_id $strWhere $strLimit";
        //$strGroup = "group by commercial_order_item.combine_id";
        $strTotalSql = "select $strField from commercial_order_item left join ($strJoin) on commercial_order_item.combine_id = price_record_total.combine_id $strWhere";

        //total
        $objDb = Util_Schedule_Db::getDB();
        $arrTotalRet = $objDb->query($strTotalSql);
        if ($arrTotalRet === false) {
            Bingo_Log::warning("[output:".serialize($arrTotalRet)."error:".$objDb->error()."sql:".$objDb->getLastSQL()."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $intTotal = count($arrTotalRet);

        //limit
        $strSql = "select $strField from commercial_order_item left join ($strJoin) on commercial_order_item.combine_id = price_record_total.combine_id $strWhere $strOrder $strLimit";
      
        $objDb = Util_Schedule_Db::getDB();
        $arrRet = $objDb->query($strSql);
        if ($arrRet === false) {
            Bingo_Log::warning("[output:".serialize($arrRet)."error:".$objDb->error()."sql:".$objDb->getLastSQL()."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        //$arrScheduleRet = self::_combineData($arrScheduleRet);

        $arrData = array(
            'list'  => $arrRet,
            'total' => $intTotal,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
    }


    //get ThrowList to audit sys
    //create_user_name op_user_name forum_name
    public function getTomsThrowList($arrInput = array()) {
        $intStatus = intval($arrInput['status']);
        $intOrder = intval($arrInput['order']);
        $intOffset = $arrInput['pn'];
        $intLimit = $arrInput['rn'];
        $intOrderType = $arrInput['order_type'];
        
        $intCurTime = time();


        //field
        $strField = "
            commercial_order_item.combine_id,

            commercial_order_item.create_time, 
            commercial_order_item.commit_time, 
            commercial_order_item.create_user_id,
            commercial_order_item.commit_user_id,
            commercial_order_item.op_user_id,
            commercial_order_item.op_time,
            commercial_order_item.status,
            commercial_order_item.order_type,
            commercial_order_item.advertiser_id,
            commercial_order_item.ext_info,
            price_record_total.start_time,
            price_record_total.end_time,
           
            price_record_total.resource_name,
            price_record_total.resource_id,
            commercial_material.material_name,
            commercial_material.preview_url";
            // price_record_total.location,
            //price_record_total.platform,
            //price_record_total.forum_id,

        //where 
        $strWhere = " where commercial_order_item.order_type = $intOrderType and commercial_order_item.status in(10,11,12,13,15)";
        if (isset($arrInput['res_id'])) {
            $intResId = $arrInput['res_id'];
            $strWhere .= " and commercial_order_item.res_id = $intResId";
        }
        //search
        if (isset($arrInput['type'])) {
            $intType = intval($arrInput['type']);
            $arrWord = $arrInput['word'];
            
            if (1 === $intType) {
                $strWhere .= " and commercial_material.material_name = '$arrWord'";
            }
            if (2 === $intType) {
                $intForumId = $arrWord['forum_id'];
                $strFirstDir = $arrWord['first_dir'];
                $strSecondDir = $arrWord['second_dir'];
                $strWhere .= " and (price_record_total.forum_id like '%$intForumId%' or price_record_total.first_dir like '%$strFirstDir%' or price_record_total.second_dir like '%$strSecondDir%')";
            }
            if (3 === $intType) {
                //$strWhere .= " and commercial_order_item.create_user_id = $arrWord";
                if ('' == $arrWord) {
                    $strWhere .= " and commercial_order_item.op_user_id = $arrWord";
                } else {
                    $strWhere .= " and commercial_order_item.op_user_id in ( $arrWord )";
                }
            }
        }
        if (isset($arrInput['status'])) {
            $arrStatusMap = array(
                self::THROW_STATUS_PENDING_AUDIT  => Util_Schedule_Def::ORDER_STATUS_PENDING_AUDIT,
                self::THROW_STATUS_AUDIT_NOT_PASS => Util_Schedule_Def::ORDER_STATUS_AUDIT_NOT_PASS,
                self::THROW_STATUS_CLOSE          => array(
                    Util_Schedule_Def::ORDER_STATUS_CLOSE,
                    Util_Schedule_Def::ORDER_STATUS_CLOSE_ADVERTISER_OFFLINE,
                ),
            ); 
            $arrMapDirect = array_keys($arrStatusMap);

            $arrOtherStatus = array(
                self::THROW_STATUS_BEFORE_ONLINE,
                self::THROW_STATUS_ONLINE,
                self::THROW_STATUS_OFFLINE,
            );

            $intStatus = intval($arrInput['status']);
            if (in_array($intStatus, $arrMapDirect)) {
                $intMappedStatus = $arrStatusMap[$intStatus];
                if (!is_array($intMappedStatus)) {
                    $strWhere .= " and commercial_order_item.status = $intMappedStatus";
                } else {
                    $strStatus = implode(',', $intMappedStatus);
                    $strWhere .= " and commercial_order_item.status in ($strStatus)";
                }
            }
            if (in_array($intStatus, $arrOtherStatus)) {
                $intOrderStatus = Util_Schedule_Def::ORDER_STATUS_AUDIT_PASS;
                $strWhere .= " and commercial_order_item.status = $intOrderStatus";
                $intCurTime;
                if ($intStatus === self::THROW_STATUS_BEFORE_ONLINE) {
                    $strWhere .= " and price_record_total.start_time > $intCurTime";
                }
                if ($intStatus === self::THROW_STATUS_ONLINE) {
                    $strWhere .= " and price_record_total.start_time <= $intCurTime and price_record_total.end_time >= $intCurTime";
                }
                if ($intStatus === self::THROW_STATUS_OFFLINE) {
                    $strWhere .= " and price_record_total.end_time < $intCurTime";
                }
            }
        }

        //order
        $arrStatusMap = array(
            self::THROW_STATUS_PENDING_AUDIT  => Util_Schedule_Def::ORDER_STATUS_PENDING_AUDIT,
            self::THROW_STATUS_AUDIT_NOT_PASS => Util_Schedule_Def::ORDER_STATUS_AUDIT_NOT_PASS,
            self::THROW_STATUS_CLOSE          => Util_Schedule_Def::ORDER_STATUS_CLOSE,
        ); 
        if (self::THROW_STATUS_PENDING_AUDIT === $intStatus) {
            //$strOrder = " order by commercial_order_item.create_time asc";
            if (1 === $intOrderType) {
                $strOrder = " order by commercial_order_item.create_time asc";
            } else {
                $strOrder = " order by commercial_order_item.commit_time asc";
            }
        }

        if (self::THROW_STATUS_BEFORE_ONLINE === $intStatus
            || self::THROW_STATUS_ONLINE === $intStatus
            || self::THROW_STATUS_AUDIT_NOT_PASS === $intStatus
            || self::THROW_STATUS_CLOSE === $intStatus) {
            $strOrder = " order by commercial_order_item.op_time desc";
            
        }

        if (self::THROW_STATUS_OFFLINE === $intStatus) {
            $strOrder = " order by price_record_total.end_time desc";
        }
        

        $strLimit = "limit $intOffset, $intLimit";
        $strJoin = "price_record_total left join commercial_material on price_record_total.combine_id = commercial_material.order_item_id";
        //$strSql = "select $strField from commercial_order_item left join price_record on commercial_order_item.combine_id = price_record.combine_id $strWhere $strLimit";
        $strTotalSql = "select $strField from commercial_order_item left join ($strJoin) on commercial_order_item.combine_id = price_record_total.combine_id $strWhere";

        //total
        $objDb = Util_Schedule_Db::getDB();
        $arrTotalRet = $objDb->query($strTotalSql);
        if ($arrTotalRet === false) {
            Bingo_Log::warning("[output:".serialize($arrTotalRet)."error:".$objDb->error()."sql:".$objDb->getLastSQL()."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $intTotal = count($arrTotalRet);

        //limit
        $strSql = "select $strField from commercial_order_item left join ($strJoin) on commercial_order_item.combine_id = price_record_total.combine_id $strWhere $strOrder $strLimit";
      
        $objDb = Util_Schedule_Db::getDB();
        $arrRet = $objDb->query($strSql);
        if ($arrRet === false) {
            Bingo_Log::warning("[output:".serialize($arrRet)."error:".$objDb->error()."sql:".$objDb->getLastSQL()."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        //$arrScheduleRet = self::_combineData($arrScheduleRet);

        $arrData = array(
            'list'  => $arrRet,
            'total' => $intTotal,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
    }

    /**
     * @brief get dimension info from price_record by order_item_id
     * @return: true if success. false if fail.
    **/     
    public function getDimensionInfo($arrInput = array()) {
        $arrSelInput = array(
            'table' => 'price_record',
            'field' => array(
                'id',
                'res_type',
                'resource_id',
                'resource_name',
                'combine_id',
                'platform',
                'location',
                'region',
                'dimension',
                'forum_id',
                'forum_name',
                'first_dir',
                'second_dir',
                'start_time',
                'end_time',
                'is_mask_official',
                'is_mask_special',
                'mask_forum',
            ),
            'cond' => array(
                'combine_id'     => $arrInput['combine_id'],
                'deleted_status=' => 0,
            ),
        );
        $arrRes = Util_Schedule_Db::select($arrSelInput);
        return $arrRes;
    }

    /**
    * @param : 
    * @return :
    */
    public function getAllThrowList($arrInput) 
    {
        $intOperatorId = intval($arrInput['operator_id']);
        $intForumId    = intval($arrInput['forum_id']);
        
        //field
        $strField = "
            commercial_order_item.combine_id,
            commercial_order_item.res_id, 
            commercial_order_item.create_time, 
            commercial_order_item.op_time, 
            commercial_order_item.create_user_id,
            commercial_order_item.op_user_id,
            commercial_order_item.status,
            commercial_order_item.ext_info,
            price_record_total.start_time,
            price_record_total.end_time,
            
            price_record_total.resource_name,
            commercial_material.material_name,
            commercial_material.preview_url";

        //where 
        $strWhere = " where commercial_order_item.operator_id = $intOperatorId and price_record_total.forum_id = $intForumId and commercial_order_item.order_type = 1";
        if ($intForumId <= 0)
        {
            $strWhere = " where commercial_order_item.operator_id = $intOperatorId and commercial_order_item.order_type = 1";
        }        
        if (isset($arrInput['res_id'])) 
        {
            $intResId = $arrInput['res_id'];
            $strWhere .= " and commercial_order_item.res_id = $intResId";
        }

        if (isset($arrInput['status'])) 
        {
            $arrStatusMap = array(
                self::THROW_STATUS_PENDING_AUDIT  => Util_Schedule_Def::ORDER_STATUS_PENDING_AUDIT,
                self::THROW_STATUS_AUDIT_NOT_PASS => Util_Schedule_Def::ORDER_STATUS_AUDIT_NOT_PASS,
                self::THROW_STATUS_CLOSE          => Util_Schedule_Def::ORDER_STATUS_CLOSE,
            ); 
            $arrMapDirect = array_keys($arrStatusMap);

            $arrOtherStatus = array(
                self::THROW_STATUS_BEFORE_ONLINE,
                self::THROW_STATUS_ONLINE,
                self::THROW_STATUS_OFFLINE,
            );

            $intStatus = intval($arrInput['status']);
            if (in_array($intStatus, $arrMapDirect)) 
            {
                $intMappedStatus = $arrStatusMap[$intStatus];
                $strWhere .= " and commercial_order_item.status = $intMappedStatus";
            }
            if (in_array($intStatus, $arrOtherStatus)) 
            {
                $intOrderStatus = Util_Schedule_Def::ORDER_STATUS_AUDIT_PASS;
                $strWhere .= " and commercial_order_item.status = $intOrderStatus";
                $intCurTime;
                if ($intStatus === self::THROW_STATUS_BEFORE_ONLINE) 
                {
                    $strWhere .= " and price_record_total.start_time > $intCurTime";
                }
                if ($intStatus === self::THROW_STATUS_ONLINE) 
                {
                    $strWhere .= " and price_record_total.start_time <= $intCurTime and price_record_total.end_time >= $intCurTime";
                }
                if ($intStatus === self::THROW_STATUS_OFFLINE) 
                {
                    $strWhere .= " and price_record_total.end_time < $intCurTime";
                }
            }
        }

        if (1 === $intOrderType) {
            $strOrder = " order by commercial_order_item.create_time desc";
        } else {
            $strOrder = " order by commercial_order_item.commit_time desc";
        }
        $strJoin = "price_record_total  left join commercial_material on price_record_total.combine_id = commercial_material.order_item_id";

        //limit
        $strSql = "select $strField from commercial_order_item left join ($strJoin) on commercial_order_item.combine_id = price_record_total.combine_id $strWhere $strOrder $strLimit";
      
        $objDb = Util_Schedule_Db::getDB();
        $arrRet = $objDb->query($strSql);
        if ($arrRet === false) 
        {
            Bingo_Log::warning("[output:".serialize($arrRet)."error:".$objDb->error()."sql:".$objDb->getLastSQL()."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

    public function getOnlineOrderItemByOperator($arrInput = array()) { 
        $arrSelInput = array(
            'table' => 'commercial_order_item',
            'field' => array(
                'combine_id',
                'status',
            ), 
            'cond' => array(
                'status' => array(
                    Util_Schedule_Def::ORDER_STATUS_PENDING_AUDIT,
                    Util_Schedule_Def::ORDER_STATUS_AUDIT_PASS,
                ),
                'order_type=' => 1,
            ),
        );
        if (isset($arrInput['operator_id'])) {
            $arrSelInput['cond']['operator_id='] = $arrInput['operator_id'];
        }
        if (isset($arrInput['advertiser_id'])) {
            $arrSelInput['cond']['advertiser_id='] = $arrInput['advertiser_id'];
        }
        $arrRes = Util_Schedule_Db::select($arrSelInput);
        $arrRet = array();
        $arrCombineId = array();
        foreach ($arrRes['data'] as $value) {
            $arrCombineId[$value['combine_id']] = $value['status'];
            if (Util_Schedule_Def::ORDER_STATUS_PENDING_AUDIT === intval($value['status'])) {
                $arrRet['has_ad_delivery'] = 1;
                //return $arrRet;
            }
        }
        if (empty($arrCombineId)) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
        }

        $arrSelInput = array(
            'table' => 'price_record_total',
            'field' => array(
                'start_time',
                'end_time',
                'combine_id',
            ), 
            'cond' => array(
                'combine_id' => array_keys($arrCombineId),

            ),
        );
        if (isset($arrInput['forum_id'])) {
            $arrSelInput['cond']['forum_id='] = $arrInput['forum_id'];
        }
        $arrRes = Util_Schedule_Db::select($arrSelInput);
        foreach ($arrRes['data'] as $value) {
            if (Util_Schedule_Def::ORDER_STATUS_AUDIT_PASS === intval($arrCombineId[$value['combine_id']])) {
                if ($value['end_time'] > time()) {
                    $arrRet['online_info'][] = $value['combine_id'];
                    $arrRet['has_ad_delivery'] = 1;
                    //return $arrRet;
                }
            }
            if (Util_Schedule_Def::ORDER_STATUS_PENDING_AUDIT === intval($arrCombineId[$value['combine_id']])) {
                $arrRet['online_info'][] = $value['combine_id'];
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
        //return $arrRet;
    }

    public function offlineOrderByAdvertiserId($arrInput = array()) {
        $arrUpdateInput = array(
            'table' => 'commercial_order_item',
            'field' => array(
                'status='     => $arrInput['status'],
                'op_user_id=' => $arrInput['op_user_id'],
                'op_time='    => time(),   
            ),
            'cond'  => array(
                'advertiser_id=' => intval($arrInput['advertiser_id']),
                'order_type='    => 1,
            ),
        );
        
        $arrRes = Util_Schedule_Db::update($arrUpdateInput);
        return $arrRes;
    }



    /*public function getOnlineForumIdBySpId($arrInput = array()) { 
        $arrSelInput = array(
            'table' => 'commercial_order_item',
            'field' => array(
                'combine_id',
                'status',
            ), 
            'cond' => array(
                'status' => array(
                    Util_Schedule_Def::ORDER_STATUS_PENDING_AUDIT,
                    Util_Schedule_Def::ORDER_STATUS_AUDIT_PASS,
                ),
                'order_type=' => 1,
            ),
        );
        if (isset($arrInput['operator_id'])) {
            $arrSelInput['cond']['operator_id='] = $arrInput['operator_id'];
        }
        if (isset($arrInput['advertiser_id'])) {
            $arrSelInput['cond']['advertiser_id='] = $arrInput['advertiser_id'];
        }
        $arrRes = Util_Schedule_Db::select($arrSelInput);
        $arrRet = array();
        $arrCombineId = array();
        foreach ($arrRes['data'] as $value) {
            $arrCombineId[$value['combine_id']] = $value['status'];
            if (Util_Schedule_Def::ORDER_STATUS_PENDING_AUDIT === intval($value['status'])) {
                $arrRet['has_ad_delivery'] = 1;
                //return $arrRet;
            }
        }
        if (empty($arrCombineId)) {
            return $arrRet;
        }

        $arrSelInput = array(
            'table' => 'price_record_total',
            'field' => array(
                'start_time',
                'end_time',
                'combine_id',
            ), 
            'cond' => array(
                'combine_id' => array_keys($arrCombineId),

            ),
        );
        if (isset($arrInput['forum_id'])) {
            $arrSelInput['cond']['forum_id='] = $arrInput['forum_id'];
        }
        $arrRes = Util_Schedule_Db::select($arrSelInput);
        foreach ($arrRes['data'] as $value) {
            if (Util_Schedule_Def::ORDER_STATUS_AUDIT_PASS === intval($arrCombineId[$value['combine_id']])) {
                if ($value['end_time'] > time()) {
                    $arrRet['online_info'][] = $value['combine_id'];
                    $arrRet['has_ad_delivery'] = 1;
                    //return $arrRet;
                }
            }
            if (Util_Schedule_Def::ORDER_STATUS_PENDING_AUDIT === intval($arrCombineId[$value['combine_id']])) {
                $arrRet['online_info'][] = $value['combine_id'];
            }
        }

        return $arrRet;
    }*/



    public function getTotalDimensionInfo($arrInput = array()) {
        $arrSelInput = array(
            'table' => 'price_record_total',
            'field' => array(
                'combine_id',
                'res_type',
                'resource_id',
                'resource_name',
                'forum_id',
               //'forum_name',
                'first_dir',
                'second_dir',
                'start_time',
                'end_time',
                //'is_mask_official',
               // 'is_mask_special',
                //'mask_forum',
            ),
            'cond' => array(
                'combine_id' => $arrInput['combine_id'],
            ),
        );
        $arrRes = Util_Schedule_Db::select($arrSelInput);
        return $arrRes;
    }

    public function updateMaterialStatus($arrInput = array()) {
        //1待审核 2未通过 3已通过
        $arrId = array();
        $intOpUserId = intval($arrInput['op_user_id']);
        $intTime = time();
        $strSql = 'update commercial_material_item SET status = CASE id';
        foreach ($arrInput['resource_info'] as $value) {
            $intStatus = intval($value['status']) + 2;
            $intId = intval($value['id']);
            $arrId[] = $intId;
            $strTmp = " WHEN $intId THEN $intStatus";
            $strSql .= $strTmp;

        }
        $strIds = implode(',', $arrId);
        //$strSql  .= " END, reason = CASE id";
        
        foreach ($arrInput['resource_info'] as $value) {
            $strReason = $value['reason'];
            if (!empty($strReason)) {
                $arrReason[] = $strReason;
            }

        }
        if (!empty($arrReason)) {
            $strSql  .= " END, reason = CASE id";
            foreach ($arrInput['resource_info'] as $value) {
                $strReason = $value['reason'];
                $intId = intval($value['id']);
                if (!empty($strReason)) {
                    $strTmp = " WHEN $intId THEN '$strReason'";
                    $strSql .= $strTmp;
                }

            }
        }

        $strSql  .= " END, op_user_id = $intOpUserId, op_time = $intTime  where id in ($strIds);";
        $objDb = Util_Schedule_Db::getDB();
        $arrRes = $objDb->query($strSql);
        if (false === $arrRes) {
            return false;
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }
}
