<?php

/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/10/19
 * Time: 2:29
 */
class Dl_Data_Data
{
    const OFFICIAL_PV_TABLE = 'pv_official';
    const CONTRACT_FORUM_PV_TABLE = 'pv_contractforum';
    const ALL_FORUM_PV_TABLE = 'pv_all';
    const OPERATION_CENTER_PV_TABLE = 'pv_opcenter';
    const DIFF_PV_TABLE = 'pv_diff';
    const PER_PAGE_NUM = 30;

    /**
     * @param $errno
     * @param $data
     * @return array
     */
    private static function _errRet($errno, $data = array()) {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        );
    }

    /**
     * @return array
     */
    private static function _getOfficialPvTableField() {
        return array(
            'client_type',
            'total_pv_frs',
            'total_pv_pb',
        );
    }

    /**
     * @return array
     */
    private static function _getContractForumPvTableField() {
        return array(
            'client_type',
            'total_pv_frs',
            'total_pv_pb',
        );
    }

    /**
     * @return array
     */
    private static function _getAllForumPvTableField() {
        return array(
            'client_type',
            'total_pv_frs',
            'total_pv_pb',
        );
    }

    /**
     * @return array
     */
    private static function _getOperationCenterPvTableField() {
        return array(
            'client_type',
            'total_pv_frs',
            'total_pv_pb',
        );
    }

    /**
     * @return array
     */
    private static function _getDiffPvTableField() {
        return array(
            'client_type',
            'of_diff_pv_frs',
            'of_diff_pv_pb',
            'oc_diff_pv_frs',
            'oc_diff_pv_pb',
            'ofc_diff_pv_frs',
            'ofc_diff_pv_pb',
            'af_diff_pv_frs',
            'af_diff_pv_pb',
            'ac_diff_pv_frs',
            'ac_diff_pv_pb',
            'afc_diff_pv_frs',
            'afc_diff_pv_pb',
        );
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getOfficialPageView($arrInput) {
        $arrSelectInput = array(
            'table' => self::OFFICIAL_PV_TABLE,
            'field' => self::_getOfficialPvTableField(),
            'cond' => array(
                'date=' => intval($arrInput['date']),
                'first_dir=' => strval($arrInput['first_dir']),
                'second_dir=' => strval($arrInput['second_dir']),
            ),
        );
        $arrSelectRes = Util_Db::select($arrSelectInput);
        return $arrSelectRes;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getContractForumPageView($arrInput) {
        $arrSelectInput = array(
            'table' => self::CONTRACT_FORUM_PV_TABLE,
            'field' => self::_getContractForumPvTableField(),
            'cond' => array(
                'date=' => intval($arrInput['date']),
                'first_dir=' => strval($arrInput['first_dir']),
                'second_dir=' => strval($arrInput['second_dir']),
            ),
        );
        $arrSelectRes = Util_Db::select($arrSelectInput);
        return $arrSelectRes;
    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function getOperationCenterPageView($arrInput) {
        $arrSelectInput = array(
            'table' => self::OPERATION_CENTER_PV_TABLE,
            'field' => self::_getOperationCenterPvTableField(),
            'cond' => array(
                'date=' => intval($arrInput['date']),
                'first_dir=' => strval($arrInput['first_dir']),
                'second_dir=' => strval($arrInput['second_dir']),
            ),
        );
        $arrSelectRes = Util_Db::select($arrSelectInput);
        return $arrSelectRes;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getAllForumPageView($arrInput) {
        $arrSelectInput = array(
            'table' => self::ALL_FORUM_PV_TABLE,
            'field' => self::_getAllForumPvTableField(),
            'cond' => array(
                'date=' => intval($arrInput['date']),
                'first_dir=' => strval($arrInput['first_dir']),
                'second_dir=' => strval($arrInput['second_dir']),
            ),
        );
        $arrSelectRes = Util_Db::select($arrSelectInput);
        return $arrSelectRes;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getDirDiffPageView($arrInput) {
        $arrSelectInput = array(
            'table' => self::DIFF_PV_TABLE,
            'field' => self::_getDiffPvTableField(),
            'cond' => array(
                'date=' => intval($arrInput['date']),
                'first_dir=' => strval($arrInput['first_dir']),
                'second_dir=' => strval($arrInput['second_dir']),
            ),
        );
        $arrSelectRes = Util_Db::select($arrSelectInput);
        return $arrSelectRes;
    }

}