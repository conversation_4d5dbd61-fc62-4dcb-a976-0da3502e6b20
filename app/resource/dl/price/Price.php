<?php

/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/10/9
 * Time: 14:47
 */
class Dl_Price_Price
{
    const PER_PAGE_NUM = 30;
    const RESOURCE_TABLE = 'resource';
    const PRICE_TABLE = 'price';
    const PRICE_RECORD_TABLE = 'price_record';
    const QUOTE_TABLE = 'resource_price_relation';
    const PRICE_RECORD_TOTAL_TABLE = 'price_record_total';
    const QUOTE_RES_COMMERCIAL = 0;


    /**
     * @param $errno
     * @param $data
     * @return array
     */
    private static function _errRet($errno, $data = array()) {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        );
    }

    /**
     * @return array
     */
    private static function _getPriceTableField() {
        return array(
            'id',
            'resource_id',
            'resource_name',
            'resource_classify_id',
            'resource_classify_name',
            'platform',
            'location',
            'region',
            'dimension',
            'ord_dir_price',
            'star_dir_price',
            'local_dir_price',
            'vertical_dir_price',
            'game_dir_price',
            'sport_dir_price',
            'comic_dir_price',
            'is_limit_dir',
            'limit_first_dir',
            'limit_second_dir',
            'balance_type',
            'day_price',
            'discount',
            'min_cycle',
            'ctr',
            'create_time',
            'create_user_name',
            'op_time',
            'op_user_name',
            'is_tiebaplus_resource',
        );
    }

    /**
     * @return array
     */
    private static function _getPriceRecordTableField() {
        return array(
            'id',
            'res_type',
            'quote_id',
            'resource_id',
            'combine_id',
            'resource_name',
            'platform',
            'location',
            'region',
            'dimension',
            'forum_name',
            'forum_id',
            'first_dir',
            'second_dir',
            'start_time',
            'end_time',
            'is_mask_official',
            'is_mask_special',
            'mask_forum',
            'page_view',
            'balance_type',
            'base_price',
            'price',
            'discount',
            'evaluation',
            'sum',
            'discount_sum',
            'min_cycle',
            'ctr',
            'create_time',
            'create_user_name',
            'op_time',
            'op_user_name',
            'deleted_status',
            //贴+非标
            'is_tiebaplus_resource',
            'exclude_days',
            'clash_days',
            'is_all_book_clash',
            'age',
            'sex',
            'province',
            'city',
            'thread_num',
            'purchase_pv',
        );
    }

    /**
     * @return array
     */
    private static function _getPriceTotalRecordTableField() {
        return array(
            'id',
            'quote_id',
            'resource_id',
            'combine_id',
            'resource_name',
            'res_type',
            'balance_type',
            'discount',
            'evaluation',
            'start_time',
            'end_time',
            'day',
            'sum',
            'discount_sum',
            'ctr',
            'create_time',
            'create_user_name',
            'op_time',
            'op_user_name',
            'data_type',
            'extra_info',
            'is_tiebaplus_resource',
            'exclude_days',
            'clash_days',
            'is_all_book_clash',
        );
    }

    /**
     * @param $arrInput
     */
    public static function preCall($arrInput) {
        // pre-call hook
    }

    /**
     * @param $arrInput
     */
    public static function postCall($arrInput) {
        // post-call hook
    }

    /**
     * @param $arrInput
     * @return array|bool
     */
    public static function createResourceQuoteConfig($arrInput) {
        $intCurTime = time();
        $arrInsertInput = array(
            'table' => self::PRICE_TABLE,
            'field' => array(
                'resource_id' => intval($arrInput['resource_id']),
                'resource_name' => strval($arrInput['resource_name']),
                'resource_classify_id' => intval($arrInput['resource_classify_id']),
                'resource_classify_name' => strval($arrInput['resource_classify_name']),
                'platform' => $arrInput['platform'],
                'location' => $arrInput['location'],
                'region' => $arrInput['region'],
                'dimension' => $arrInput['dimension'],
                'ord_dir_price' => floatval($arrInput['ord_dir_price']),
                'star_dir_price' => floatval($arrInput['star_dir_price']),
                'local_dir_price' => floatval($arrInput['local_dir_price']),
                'vertical_dir_price' => floatval($arrInput['vertical_dir_price']),
                'game_dir_price' => floatval($arrInput['game_dir_price']),
                'sport_dir_price' => floatval($arrInput['sport_dir_price']),
                'comic_dir_price' => floatval($arrInput['comic_dir_price']),
                'is_limit_dir' => $arrInput['is_limit_dir'],
                'limit_first_dir' => $arrInput['limit_first_dir'],
                'limit_second_dir' => $arrInput['limit_second_dir'],
                'balance_type' => intval($arrInput['balance_type']),
                'day_price' => floatval($arrInput['day_price']),
                'discount' => floatval($arrInput['discount']),
                'min_cycle' => intval($arrInput['min_cycle']),
                'ctr' => floatval($arrInput['ctr']),
                'create_time' => $intCurTime,
                'create_user_name' => $arrInput['op_user_name'],
                'op_time' => $intCurTime,
                'op_user_name' => $arrInput['op_user_name'],
                'is_tiebaplus_resource' => $arrInput['is_tiebaplus_resource'],
            ),
        );
        $arrRes = Util_Db::insert($arrInsertInput);
        return $arrRes;
    }

    /**
     * @param $arrInput
     * @return array|bool
     */
    public static function updateResourceQuoteConfigById($arrInput) {
        $arrAllField = self::_getPriceTableField();
        $intId = intval($arrInput['id']);
        unset($arrInput['id']);
        foreach ($arrAllField as $strKey) {
            if (isset($arrInput[$strKey])) {
                $arrUpdateField[$strKey . "="] = $arrInput[$strKey];
            }
        }
        if (empty($arrUpdateField)) {
            Bingo_Log::warning("update  input is empty param:" . serialize($arrInput));
            return false;
        }
        $arrUpdateInput = array(
            'table' => self::PRICE_TABLE,
            'field' => $arrUpdateField,
            'cond' => array(
                "id=" => $intId,
            ),
        );
        $arrUpdateRes = Util_Db::update($arrUpdateInput);
        return $arrUpdateRes;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getResourceQuoteConfigByResourceId($arrInput) {
        $arrSelectInput = array(
            'table' => self::PRICE_TABLE,
            'field' => self::_getPriceTableField(),
            'cond' => array(
                'resource_id=' => intval($arrInput['resource_id']),
            ),
        );
        $arrSelectRes = Util_Db::select($arrSelectInput);
        return $arrSelectRes;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getResourceQuoteConfigById($arrInput) {
        $arrSelectInput = array(
            'table' => self::PRICE_TABLE,
            'field' => self::_getPriceTableField(),
            'cond' => array(
                'id=' => intval($arrInput['id']),
            ),
        );
        $arrSelectRes = Util_Db::select($arrSelectInput);
        return $arrSelectRes;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getQuoteRecordByResourceIdAndQuoteId($arrInput) {
        $arrSelectInput = array(
            'table' => self::PRICE_RECORD_TABLE,
            'field' => self::_getPriceRecordTableField(),
            'cond' => array(
                'resource_id=' => intval($arrInput['resource_id']),
                'quote_id=' => intval($arrInput['quote_id']),
            ),
        );
        $arrSelectRes = Util_Db::select($arrSelectInput);
        return $arrSelectRes;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getQuoteRecordByCombineId($arrInput) {
        $arrSelectInput = array(
            'table' => self::PRICE_RECORD_TABLE,
            'field' => self::_getPriceRecordTableField(),
            'cond' => array(
                'combine_id=' => intval($arrInput['combine_id']),
            ),
        );
        $arrSelectRes = Util_Db::select($arrSelectInput);
        return $arrSelectRes;
    }

    /**
     * @param $arrInput
     * @return array|bool
     */
    public static function getQuoteResourceList($arrInput) {
        $strAppendSql = '';
        if (!empty($arrInput['pn'])) {
            $intPn = isset($arrInput['pn']) ? intval($arrInput['pn']) : 1;
            $intPerNum = isset($arrInput['per_num']) ? intval($arrInput['per_num']) : self::PER_PAGE_NUM;
            $intOffset = ($intPn - 1) * $intPerNum;
            $strAppendSql = " limit $intOffset,$intPerNum";
        }

        $arrAllField = self::_getPriceTableField();
        $strAllFields = implode(",", $arrAllField);
        $db = Util_Db::getDB();
        $strTable = self::PRICE_TABLE;
        $strTotalSql = "select count(id) as total_num from $strTable ";
        $arrSelectRes = $db->query($strTotalSql);
        if (false === $arrSelectRes) {
            Bingo_Log::warning("exec sql fail and sql=[" . $strTotalSql . ' out:' . serialize($arrSelectRes));
            return false;
        }
        $strPageSql = "select " . $strAllFields . " from $strTable " . $strAppendSql;
        $arrPageRes = $db->query($strPageSql);
        if (false === $arrPageRes) {
            Bingo_Log::warning("exec sql fail and  sql=[" . $strPageSql . ' out:' . serialize($arrPageRes));
            return false;
        }
        $arrOutPut = array(
            'total_pn' => intval($arrSelectRes[0]['total_num'] / $intPerNum) + 1,
            'list' => $arrPageRes,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutPut);
    }


    /**
     * @param $arrInput
     * @return mixed
     */
    public static function multiCreatePriceRecord($arrInput) {
        if (isset($arrInput['db'])) {
            $db = $arrInput['db'];
        } else {
            $db = Util_Db::getDB();
        }
        $db->startTransaction();
        $intQuoteId = intval($arrInput['quote_id']);
        $intResourceId = intval($arrInput['resource_id']);
        $arrDeleteInput = array(
            'db' => $db,
            'table' => self::PRICE_RECORD_TABLE,
            'cond' => array(
                "quote_id=" => $intQuoteId,
                "resource_id=" => $intResourceId,
            ),
        );
        $arrDeleteRes = Util_Db::delete($arrDeleteInput);
        if (false === $arrDeleteRes || Tieba_Errcode::ERR_SUCCESS !== $arrDeleteRes['errno']) {
            Bingo_Log::warning("create resource fail input[:" . serialize($arrDeleteInput) . "]out[" . serialize($arrDeleteInput) . "]");
            return false;
        }
        if (!empty($arrInput['data'])) {
            $arrReplaceInfo['field'] = array(
                'res_type',
                'quote_id',
                'resource_id',
                'combine_id',
                'resource_name',
                'platform',
                'location',
                'region',
                'dimension',
                'forum_id',
                'forum_name',
                'first_dir',
                'second_dir',
                'start_time',
                'end_time',
                'balance_type',
                'is_mask_official',
                'is_mask_special',
                'mask_forum',
                'page_view',
                'base_price',
                'price',
                'discount',
                'evaluation',
                'sum',
                'discount_sum',
                'ctr',
                'min_cycle',
                'create_time',
                'create_user_name',
                'op_time',
                'op_user_name',
                //贴+非标
                'is_tiebaplus_resource',
                'exclude_days',
                'clash_days',
                'is_all_book_clash',
                'age',
                'sex',
                'province',
                'city',
                'thread_num',
                'purchase_pv',
            );
            foreach ($arrInput['data'] as $arrEach) {
                $arrReplaceInfo['data'][] = array(
                    intval($arrEach['res_type']),
                    intval($arrEach['quote_id']),
                    intval($arrEach['resource_id']),
                    intval($arrEach['combine_id']),
                    strval($arrEach['resource_name']),
                    strval($arrEach['platform']),
                    strval($arrEach['location']),
                    strval($arrEach['region']),
                    intval($arrEach['dimension']),
                    intval($arrEach['forum_id']),
                    strval($arrEach['forum_name']),
                    strval($arrEach['first_dir']),
                    strval($arrEach['second_dir']),
                    intval($arrEach['start_time']),
                    intval($arrEach['end_time']),
                    intval($arrEach['balance_type']),
                    intval($arrEach['is_mask_official']),
                    intval($arrEach['is_mask_special']),
                    strval($arrEach['mask_forum']),
                    intval($arrEach['page_view']),
                    floatval($arrEach['base_price']),
                    floatval($arrEach['price']),
                    floatval($arrEach['discount']),
                    floatval($arrEach['evaluation']),
                    floatval($arrEach['sum']),
                    floatval($arrEach['discount_sum']),
                    floatval($arrEach['ctr']),
                    intval($arrEach['min_cycle']),
                    intval($arrEach['create_time']),
                    strval($arrEach['create_user_name']),
                    intval($arrEach['op_time']),
                    strval($arrEach['op_user_name']),
                    //贴+非标
                    intval($arrEach['is_tiebaplus_resource']),
                    intval($arrEach['exclude_days']),
                    intval($arrEach['clash_days']),
                    intval($arrEach['is_all_book_clash']),
                    strval($arrEach['age']),
                    strval($arrEach['sex']),
                    strval($arrEach['province']),
                    strval($arrEach['city']),
                    intval($arrEach['thread_num']),
                    intval($arrEach['purchase_pv']),
                );
            }
        }
        $arrInsertInput = array(
            'db' => $db,
            'table' => self::PRICE_RECORD_TABLE,
            'field' => $arrReplaceInfo['field'],
            'data' => $arrReplaceInfo['data'],
        );
        $arrInsertRes = Util_Db::replace($arrInsertInput);
        if (false === $arrInsertRes || Tieba_Errcode::ERR_SUCCESS !== $arrInsertRes['errno']) {
            Bingo_Log::warning("create resource fail input[:" . serialize($arrInsertInput) . "]out[" . serialize($arrInsertRes) . "]");
            return false;
        }
        if (false === $db->commit()) {
            Bingo_Log::warning("commit fail and quoted_id =[" . $intQuoteId . "]");
            if (false === $db->rollback()) {
                Bingo_Log::warning("rollback fail and resource_id =[" . $intQuoteId . "]");
            }
            return false;
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return mixed
     */
    public static function multiCreatePriceRecordByMis($arrInput) {
        $intCurTime = time();
        $db = Util_Db::getDB();
        $db->startTransaction();
        $intQuoteId = intval($arrInput['quote_id']);
        $intResourceId = intval($arrInput['resource_id']);
        $intCombineId = intval($arrInput['combine_id']);
        $arrDeleteInput = array(
            'db' => $db,
            'table' => self::PRICE_RECORD_TABLE,
            'cond' => array(
                "quote_id=" => $intQuoteId,
                "resource_id=" => $intResourceId,
            ),
        );
        $arrDeleteRes = Util_Db::delete($arrDeleteInput);
        if (false === $arrDeleteRes || Tieba_Errcode::ERR_SUCCESS !== $arrDeleteRes['errno']) {
            Bingo_Log::warning("create resource fail input[:" . serialize($arrDeleteInput) . "]out[" . serialize($arrDeleteInput) . "]");
            return false;
        }
        //先创建订单
        $arrInputParam = array(
            'quote_id' => $arrInput['quote_id'],
            'create_user_name' => strval($arrInput['create_user_name']),
            'create_user_id' => intval($arrInput['create_user_id']),
            'op_user_name' => strval($arrInput['op_user_name']),
            'op_user_id' => intval($arrInput['op_user_id']),
            'create_time' => $intCurTime,
            'op_time' => $intCurTime,
        );
        $arrRes = Tieba_Service::call('resource', 'createOrder', $arrInputParam);
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== intval($arrRes['errno'])) {
            Bingo_Log::warning("call resource createOrder fail input[" . serialize($arrInputParam) . "]out[" . serialize($arrRes) . "]");
            return false;
        }
        $intOrderId = intval($arrRes['data']);
        //创建子订单
        $arrInputParam = array();
        $arrInputParam['data'][] = array(
            'res_type' => intval($arrInput['res_type']),
            'order_id' => $intOrderId,
            'quote_id' => $intQuoteId,
            'resource_id' => $intResourceId,
            'combine_id' => $intCombineId,
            'status' => 7,
            'create_user_name' => strval($arrInput['create_user_name']),
            'create_user_id' => intval($arrInput['create_user_id']),
            'create_time' => $intCurTime,
            'op_user_name' => strval($arrInput['op_user_name']),
            'op_user_id' => intval($arrInput['op_user_id']),
            'op_time' => $intCurTime,
            'apply_user_name' => strval($arrInput['create_user_name']),
            'apply_user_id' => strval($arrInput['create_user_id']),
            'apply_time' => $intCurTime,
        );
        $arrRes = Tieba_Service::call('resource', 'multiCreateOrderItem', $arrInputParam);
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== intval($arrRes['errno'])) {
            Bingo_Log::warning("call resource multiCreateContract fail input[" . serialize($arrInputParam) . "] out[" . serialize($arrRes) . "]");
            return false;
        }
        if (!empty($arrInput['data'])) {
            $arrReplaceInfo['field'] = array(
                'res_type',
                'quote_id',
                'resource_id',
                'combine_id',
                'resource_name',
                'platform',
                'location',
                'region',
                'dimension',
                'forum_id',
                'forum_name',
                'first_dir',
                'second_dir',
                'start_time',
                'end_time',
                'balance_type',
                'is_mask_official',
                'is_mask_special',
                'mask_forum',
                'page_view',
                'base_price',
                'price',
                'discount',
                'evaluation',
                'sum',
                'discount_sum',
                'ctr',
                'min_cycle',
                'create_time',
                'create_user_name',
                'op_time',
                'op_user_name',
            );
            foreach ($arrInput['data'] as $arrEach) {
                $arrReplaceInfo['data'][] = array(
                    intval($arrEach['res_type']),
                    intval($arrEach['quote_id']),
                    intval($arrEach['resource_id']),
                    intval($arrEach['combine_id']),
                    strval($arrEach['resource_name']),
                    strval($arrEach['platform']),
                    strval($arrEach['location']),
                    strval($arrEach['region']),
                    intval($arrEach['dimension']),
                    intval($arrEach['forum_id']),
                    strval($arrEach['forum_name']),
                    strval($arrEach['first_dir']),
                    strval($arrEach['second_dir']),
                    intval($arrEach['start_time']),
                    intval($arrEach['end_time']),
                    intval($arrEach['balance_type']),
                    intval($arrEach['is_mask_official']),
                    intval($arrEach['is_mask_special']),
                    strval($arrEach['mask_forum']),
                    intval($arrEach['page_view']),
                    floatval($arrEach['base_price']),
                    floatval($arrEach['price']),
                    floatval($arrEach['discount']),
                    floatval($arrEach['evaluation']),
                    floatval($arrEach['sum']),
                    floatval($arrEach['discount_sum']),
                    floatval($arrEach['ctr']),
                    intval($arrEach['min_cycle']),
                    intval($arrEach['create_time']),
                    strval($arrEach['create_user_name']),
                    intval($arrEach['op_time']),
                    strval($arrEach['op_user_name']),
                );
            }
        }
        $arrInsertInput = array(
            'db' => $db,
            'table' => self::PRICE_RECORD_TABLE,
            'field' => $arrReplaceInfo['field'],
            'data' => $arrReplaceInfo['data'],
        );
        $arrInsertRes = Util_Db::replace($arrInsertInput);
        if (false === $arrInsertRes || Tieba_Errcode::ERR_SUCCESS !== $arrInsertRes['errno']) {
            Bingo_Log::warning("create resource fail input[:" . serialize($arrInsertInput) . "]out[" . serialize($arrInsertRes) . "]");
            return false;
        }
        //创建总表
        $arrReplaceInfo = array();
        if (!empty($arrInput['data'])) {
            $arrReplaceInfo['field'] = array(
                'quote_id',
                'resource_id',
                'combine_id',
                'resource_name',
                'balance_type',
                'discount',
                'evaluation',
                'ctr',
                'start_time',
                'end_time',
                'day',
                'sum',
                'discount_sum',
                'create_time',
                'create_user_name',
                'op_time',
                'op_user_name',
                'res_type',
                'data_type',
            );
            foreach ($arrInput['data'] as $arrEach) {
                $arrReplaceInfo['data'][] = array(
                    intval($arrEach['quote_id']),
                    intval($arrEach['resource_id']),
                    intval($arrEach['combine_id']),
                    strval($arrEach['resource_name']),
                    intval($arrEach['balance_type']),
                    floatval($arrEach['discount']),
                    floatval($arrEach['evaluation']),
                    floatval($arrEach['ctr']),
                    intval($arrEach['start_time']),
                    intval($arrEach['end_time']),
                    intval($arrEach['day']),
                    floatval($arrEach['sum']),
                    floatval($arrEach['discount_sum']),
                    intval($arrEach['create_time']),
                    strval($arrEach['create_user_name']),
                    intval($arrEach['op_time']),
                    strval($arrEach['op_user_name']),
                    intval($arrEach['res_type']),
                    intval($arrEach['data_type']),
                );
            }
            $arrInsertInput = array(
                'db' => $db,
                'table' => self::PRICE_RECORD_TOTAL_TABLE,
                'field' => $arrReplaceInfo['field'],
                'data' => $arrReplaceInfo['data'],
            );
            $arrInsertRes = Util_Db::replace($arrInsertInput);
            if (false === $arrInsertRes || Tieba_Errcode::ERR_SUCCESS !== intval($arrInsertRes['errno'])) {
                Bingo_Log::warning("call replace method  fail input[" . serialize($arrInsertInput) . "] out[" . serialize($arrInsertRes) . "]");
                return false;
            }
        }
        if (false === $db->commit()) {
            Bingo_Log::warning("commit fail and quoted_id =[" . $intQuoteId . "]");
            if (false === $db->rollback()) {
                Bingo_Log::warning("rollback fail and resource_id =[" . $intQuoteId . "]");
            }
            return false;
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function multiCreatePriceTotalRecord($arrInput) {
        $intCurTime = time();
        $db = Util_Db::getDB();
        $db->startTransaction();
        //del order
        $arrInputParam = array(
            'quote_id' => intval($arrInput['quote_id']),
        );
        $arrRes = Tieba_Service::call('resource','delOrderByQuoteId',$arrInputParam);
        if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !==intval($arrRes['errno'])) {
            Bingo_Log::warning("call resource delOrderByQuoteId fail input[" .serialize($arrInputParam) ."]out[" .serialize($arrRes) ."]");
            return false;
        }
        //先创建订单
        $arrInputParam = array(
            'quote_id' => $arrInput['quote_id'],
            'create_user_name' => strval($arrInput['create_user_name']),
            'create_user_id' => intval($arrInput['create_user_id']),
            'op_user_name' => strval($arrInput['op_user_name']),
            'op_user_id' => intval($arrInput['op_user_id']),
            'create_time' => $intCurTime,
            'op_time' => $intCurTime,
        );
        $arrRes = Tieba_Service::call('resource', 'createOrder', $arrInputParam);
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== intval($arrRes['errno'])) {
            Bingo_Log::warning("call resource createOrder fail input[" . serialize($arrInputParam) . "]out[" . serialize($arrRes) . "]");
            return false;
        }
        $intOrderId = intval($arrRes['data']);
        //创建子订单
        $arrInputParam = array();
        if (!empty($arrInput['data'])) {
            $arrInputDelete = array(
                'quote_id' => intval($arrInput['quote_id']),
            );
            $arrRes = Tieba_Service::call('resource','deleteOrderItemByQuoteId',$arrInputDelete);
            if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !==intval($arrRes['errno'])) {
                Bingo_Log::warning("call resource deleteOrderItemByQuoteId fail input[" .serialize($arrInputDelete) ."]out[" .serialize($arrRes) ."]");
                return false;
            }
            foreach ($arrInput['data'] as $arrEach) {
                $arrInputParam['data'][] = array(
                    'res_type' => intval($arrEach['res_type']),
                    'order_id' => $intOrderId,
                    'quote_id' => intval($arrEach['quote_id']),
                    'resource_id' => intval($arrEach['resource_id']),
                    'combine_id' => intval($arrEach['combine_id']),
                    'status' => 0,
                    'create_time' => $intCurTime,
                    'create_user_name' => strval($arrEach['create_user_name']),
                    'create_user_id' => intval($arrEach['create_user_id']),
                    'op_time' => $intCurTime,
                    'op_user_name' => strval($arrEach['op_user_name']),
                    'op_user_id' => intval($arrEach['op_user_id']),
                    'apply_time' => $intCurTime,
                    'apply_user_name' => strval($arrEach['op_user_name']),
                    'apply_user_id' => intval($arrEach['op_user_id']),

                );
            }
            $arrRes = Tieba_Service::call('resource', 'multiCreateOrderItem', $arrInputParam);
            if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== intval($arrRes['errno'])) {
                Bingo_Log::warning("call resource multiCreateContract fail input[" . serialize($arrInputParam) . "] out[" . serialize($arrRes) . "]");
                return false;
            }
        }
        //在创建合同
        $arrInputParam = array();
        if (!empty($arrInput['data'])) {
            foreach ($arrInput['data'] as $arrEach) {
                $intResType = intval($arrEach['res_type']);
                //非商业资源报价不走合同流程
                if ($intResType === self::QUOTE_RES_COMMERCIAL) {
                    $arrInputParam['data'][] = array(
                        'order_id' => $intOrderId,
                        'quote_id' => intval($arrEach['quote_id']),
                        'resource_id' => intval($arrEach['resource_id']),
                        'combine_id' => intval($arrEach['combine_id']),
                        'create_user_name' => strval($arrInput['create_user_name']),
                        'op_user_name' => strval($arrInput['op_user_name']),
                        'create_time' => $intCurTime,
                        'op_time' => $intCurTime,
                    );

                }
            }
            if (!empty($arrInputParam)) {
                $arrRes = Tieba_Service::call('resource', 'multiCreateContract', $arrInputParam);
                if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== intval($arrRes['errno'])) {
                    Bingo_Log::warning("call resource multiCreateContract fail input[" . serialize($arrInputParam) . "] out[" . serialize($arrRes) . "]");
                    return false;
                }
            }

        }
        if (!empty($arrInput['data'])) {
            $arrReplaceInfo['field'] = array(
                'quote_id',
                'resource_id',
                'combine_id',
                'resource_name',
                'balance_type',
                'discount',
                'evaluation',
                'ctr',
                'start_time',
                'end_time',
                'day',
                'sum',
                'discount_sum',
                'create_time',
                'create_user_name',
                'op_time',
                'op_user_name',
                'res_type',
                'data_type',
                'extra_info',
                'forum_id',
                'first_dir',
                'second_dir',
                'is_tiebaplus_resource',
                'exclude_days',
                'clash_days',
                'is_all_book_clash',
            );
            foreach ($arrInput['data'] as $arrEach) {
                $arrReplaceInfo['data'][] = array(
                    intval($arrEach['quote_id']),
                    intval($arrEach['resource_id']),
                    intval($arrEach['combine_id']),
                    strval($arrEach['resource_name']),
                    intval($arrEach['balance_type']),
                    floatval($arrEach['discount']),
                    floatval($arrEach['evaluation']),
                    floatval($arrEach['ctr']),
                    intval($arrEach['start_time']),
                    intval($arrEach['end_time']),
                    intval($arrEach['day']),
                    floatval($arrEach['sum']),
                    floatval($arrEach['discount_sum']),
                    intval($arrEach['create_time']),
                    strval($arrEach['create_user_name']),
                    intval($arrEach['op_time']),
                    strval($arrEach['op_user_name']),
                    intval($arrEach['res_type']),
                    intval($arrEach['data_type']),
                    Bingo_String::array2json($arrEach['extra_info']),
                    $arrEach['forum_id'],
                    $arrEach['first_dir'],
                    $arrEach['second_dir'],
                    intval($arrEach['is_tiebaplus_resource']),
                    intval($arrEach['exclude_days']),
                    intval($arrEach['clash_days']),
                    intval($arrEach['is_all_book_clash']),
                );
            }
        }
        $arrInsertInput = array(
            'db' => $db,
            'table' => self::PRICE_RECORD_TOTAL_TABLE,
            'field' => $arrReplaceInfo['field'],
            'data' => $arrReplaceInfo['data'],
        );
        $arrInsertRes = Util_Db::replace($arrInsertInput);
        if (false === $arrInsertRes || Tieba_Errcode::ERR_SUCCESS !== intval($arrInsertRes['errno'])) {
            Bingo_Log::warning("call replace method  fail input[" . serialize($arrInsertInput) . "] out[" . serialize($arrInsertRes) . "]");
            return false;
        }
        if (false === $db->commit()) {
            Bingo_Log::warning("commit fail and quoted_id =[" . $arrInput['quote_id'] . "]");
            if (false === $db->rollback()) {
                Bingo_Log::warning("rollback fail and quote_id =[" . $arrInput['quote_id'] . "]");
            }
            return false;
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);

    }

    /**
     * @param $arrInput
     * @return array|bool
     */
    public static function getQuoteRecordList($arrInput) {
        $intPn = isset($arrInput['pn']) ? intval($arrInput['pn']) : 1;
        $intPerNum = isset($arrInput['per_num']) ? intval($arrInput['per_num']) : self::PER_PAGE_NUM;
        $intOffset = ($intPn - 1) * $intPerNum;
        $intDataType = intval($arrInput['data_type']);
        $arrAllField = self::_getPriceTotalRecordTableField();
        $strAllFields = implode(",", $arrAllField);
        $db = Util_Db::getDB();
        $strTable = self::PRICE_RECORD_TOTAL_TABLE;
        $strCondSql = "";
        if (!empty($arrInput['combine_id'])) {
            $intId = $arrInput['combine_id'];
            $strCondSql = " where combine_id =" . $intId;
        }

        if (!empty($arrInput['op_user_name'])) {
            $strOpUserName = $arrInput['op_user_name'];
            $strCondSql = " where op_user_name ='" . $strOpUserName . "'";
        }
        if (!empty($strCondSql)) {
            $strCondSql .= " and data_type =" . $intDataType;
        } else {
            $strCondSql = " where data_type =" . $intDataType;
        }
        $strTotalSql = "select count(id) as total_num from $strTable " . $strCondSql;
        $arrSelectRes = $db->query($strTotalSql);
        if (false === $arrSelectRes) {
            Bingo_Log::warning("exec sql fail and sql=[" . $strTotalSql . ' out:' . serialize($arrSelectRes));
            return false;
        }
        $strOrderSql = " order by create_time desc ";
        $strAppend = " limit $intOffset, " . $intPerNum;
        $strPageSql = "select " . $strAllFields . " from $strTable " . $strCondSql . $strOrderSql . $strAppend;
        $arrPageRes = $db->query($strPageSql);
        if (false === $arrPageRes) {
            Bingo_Log::warning("exec sql fail and  sql=[" . $strPageSql . ' out:' . serialize($arrPageRes));
            return false;
        }
        foreach ($arrPageRes as &$value) {
            $value['extra_info'] = Bingo_String::json2array($value['extra_info']);
        }
        $arrOutPut = array(
            'total_pn' => intval($arrSelectRes[0]['total_num'] / $intPerNum) + 1,
            'list' => $arrPageRes,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutPut);
    }

    /**
     * @param $arrInput
     * @return array|bool
     */
    public static function getPriceRecordList($arrInput) {
        $intPn = isset($arrInput['pn']) ? intval($arrInput['pn']) : 1;
        $intPerNum = isset($arrInput['per_num']) ? intval($arrInput['per_num']) : self::PER_PAGE_NUM;
        $intOffset = ($intPn - 1) * $intPerNum;
        $arrAllField = self::_getPriceRecordTableField();
        $strAllFields = implode(",", $arrAllField);
        $db = Util_Db::getDB();
        $strTable = self::PRICE_RECORD_TABLE;
        $strCondSql = "";
        if (!empty($arrInput['quote_id'])) {
            $intId = $arrInput['quote_id'];
            $strCondSql = " where quote_id =" . $intId;
        }

        if (!empty($arrInput['op_user_name'])) {
            $strOpUserName = $arrInput['op_user_name'];
            $strCondSql = " where op_user_name ='" . $strOpUserName . "'";
        }
        $strTotalSql = "select count(id) as total_num from $strTable " . $strCondSql;
        $arrSelectRes = $db->query($strTotalSql);
        if (false === $arrSelectRes) {
            Bingo_Log::warning("exec sql fail and sql=[" . $strTotalSql . ' out:' . serialize($arrSelectRes));
            return false;
        }
        $strOrderSql = " order by create_time desc ";
        $strAppend = " limit $intOffset, " . $intPerNum;
        $strPageSql = "select " . $strAllFields . " from $strTable " . $strCondSql . $strOrderSql . $strAppend;
        $arrPageRes = $db->query($strPageSql);
        if (false === $arrPageRes) {
            Bingo_Log::warning("exec sql fail and  sql=[" . $strPageSql . ' out:' . serialize($arrPageRes));
            return false;
        }
        $arrOutPut = array(
            'total_pn' => intval($arrSelectRes[0]['total_num'] / $intPerNum) + 1,
            'list' => $arrPageRes,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutPut);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function delPriceRecordById($arrInput) {
        $intId = intval($arrInput['id']);
        $db = Util_Db::getDB();
        $arrDeleteInput = array(
            'db' => $db,
            'table' => self::PRICE_RECORD_TABLE,
            'cond' => array(
                "id=" => $intId,
            ),
        );
        $arrDeleteRes = Util_Db::delete($arrDeleteInput);
        return $arrDeleteRes;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getPriceTotalRecordById($arrInput) {
        $intId = intval($arrInput['id']);
        $db = Util_Db::getDB();
        $arrSelectInput = array(
            'db' => $db,
            'table' => self::PRICE_RECORD_TOTAL_TABLE,
            'field' => self::_getPriceTotalRecordTableField(),
            'cond' => array(
                "id=" => $intId,
            ),
        );
        $arrSelectRes = Util_Db::select($arrSelectInput);
        return $arrSelectRes;
    }

    
    /**
     * @param array
     * @return array
     */
    public static function getPriceTotalRecordByCombineId($arrInput) {
        $intId = intval($arrInput['combine_id']);
        $db = Util_Db::getDB();
        $arrSelectInput = array(
            'db' => $db,
            'table' => self::PRICE_RECORD_TOTAL_TABLE,
            'field' => self::_getPriceTotalRecordTableField(),
            'cond' => array(
                "combine_id=" => $intId,
            ),
        );
        $arrSelectRes = Util_Db::select($arrSelectInput);
        return $arrSelectRes;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function delQuoteRecordById($arrInput) {
        $intId = intval($arrInput['id']);
        $db = Util_Db::getDB();
        $arrDeleteInput = array(
            'db' => $db,
            'table' => self::PRICE_RECORD_TOTAL_TABLE,
            'cond' => array(
                'id=' => $intId,
            ),
        );
        $arrDeleteRes = Util_Db::delete($arrDeleteInput);
        return $arrDeleteRes;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function createQuoteId($arrInput) {
        $intCurTime = time();
        $arrInsertInput = array(
            'table' => self::QUOTE_TABLE,
            'field' => array(
                'resource_ids' => strval($arrInput['resource_ids']),
                'op_time' => $intCurTime,
                'op_user_name' => $arrInput['op_user_name'],
            ),
        );
        $arrInsertRes = Util_Db::insert($arrInsertInput);
        return $arrInsertRes;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getDistinctQuotIds($arrInput) {
        $intPn = isset($arrInput['pn']) ? intval($arrInput['pn']) : 1;
        $intPerNum = isset($arrInput['per_num']) ? intval($arrInput['per_num']) : self::PER_PAGE_NUM;
        $intOffset = ($intPn - 1) * $intPerNum;
        $db = Util_Db::getDB();
        $strTable = self::PRICE_RECORD_TABLE;
        $strAppend = " limit $intOffset, " . $intPerNum;
        $strSql = "select distinct(quote_id) from $strTable " . $strAppend;
        $arrSelectRes = $db->query($strSql);
        if (false === $arrSelectRes) {
            Bingo_Log::warning("exec sql fail and sql=[" . $strSql . ' out:' . serialize($arrSelectRes));
            return false;
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrSelectRes);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getQuoteDetailInfoByQuoteId($arrInput) {
        $arrSelectInput = array(
            'table' => self::PRICE_RECORD_TABLE,
            'field' => self::_getPriceRecordTableField(),
            'cond' => array(
                'quote_id=' => intval($arrInput['quote_id']),
            ),
        );
        $arrSelectRes = Util_Db::select($arrSelectInput);
        return $arrSelectRes;
    }

}
