<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 

/**
 * @file Def.php
 * <AUTHOR>
 * @date 2016/01/12 13:41:06
 * @brief schedule define
 *  
 **/

class Util_Schedule_Def extends Bingo_Action_Abstract {
    public static $arrRes = array(
        '贴吧顶部高级头图',
        '客户端frs页顶部banner',
        '皮肤',
        '贴吧文字直播贴',
        '贴吧视频直播贴',
        '贴吧独家广告',
        '贴吧灯塔',
        '新高级头图',
        '彩蛋雨',
        '首页右侧浮标',
        'PB页右侧浮标',
    ); 
    const VALIDATE = 1;
    const INVALIDATE = 0;

    const DEFAULT_RN = 20;

    const RES_MAX_COUNT = 10000;

    const CONFLICT_DIMENSION_RANGE_FORUM = 0;
    const CONFLICT_DIMENSION_RANGE_FIRST_DIR = 1;
    const CONFLICT_DIMENSION_RANGE_SEC_DIR= 2;



    //order status
    //0已报价 1已预订 2已锁定 3已取消预订 4已撤销锁定 5已自动锁定 6已自动释放 7已投放 8已过期 9已失效 10待审核 11审核通过 12审核未通过 13已关闭 14已废弃（编辑+运营商下线，下线投放） 15广告主下线，下线投放 16 运营商下线，下线投放
    const ORDER_STATUS_PRICED = 0;
    const ORDER_STATUS_BOOKED_MANUAL = 1;
    const ORDER_STATUS_LOCKED_MANUAL = 2;
    const ORDER_STATUS_CANCEL_BOOKED = 3;
    const ORDER_STATUS_CANCEL_LOCKED = 4;
    const ORDER_STATUS_LOCKED_AUTO = 5;
    const ORDER_STATUS_RELEASED_AUTO = 6;
    const ORDER_STATUS_PUT = 7;
    const ORDER_STATUS_EXPIRED = 8;
    const ORDER_STATUS_INVALIDATE = 9;
    const ORDER_STATUS_PENDING_AUDIT = 10;
    const ORDER_STATUS_AUDIT_PASS = 11;
    const ORDER_STATUS_AUDIT_NOT_PASS = 12;
    const ORDER_STATUS_CLOSE = 13;
    const ORDER_STATUS_ABANDONED = 14;
    const ORDER_STATUS_CLOSE_ADVERTISER_OFFLINE = 15;
    const ORDER_STATUS_CLOSE_OPERATOR_OFFLINE = 16;
    

    //conflict type
    const CONFLICT_TYPE_MUTEX_RES   = 'conflict_mutex_res';
    const CONFLICT_TYPE_PAGE        = 'conflict_page';
    const CONFLICT_TYPE_CLIENT_TYPE = 'conflict_client_type';
    const CONFLICT_TYPE_RANGE       = 'conflict_range';

    //account type
    const ACCOUNT_TYPE_UUAP = 0;
    const ACCOUNT_TYPE_PASSPORT = 1;

    //role
    const ROLE_ID_SUPER_MANAGER = 0;
    const ROLE_NAME_SUPER_MANAGER = 'manager';
    const ROLE_NAME_CUSTOMER = 'customer';

    
    //res type 
    const RES_TYPE_COMMERCIAL = 0;
    const RES_TYPE_INTERNAL = 1;


    //contract audit status 
    //运行 完成 挂起 终止 驳回 作废
    const CONTRACT_AUDIT_STATUS_RUN = 0;
    const CONTRACT_AUDIT_STATUS_COMPLETE = 1;
    const CONTRACT_AUDIT_STATUS_HANG = 2;
    const CONTRACT_AUDIT_STATUS_TERMINATE = 3;
    const CONTRACT_AUDIT_STATUS_REJECT = 4;
    const CONTRACT_AUDIT_STATUS_CANCEL = 5;

    //contract status 
    //未生效 生效 终止 作废
    const CONTRACT_STATUS_NOT_EFFECTIVE = 0;
    const CONTRACT_STATUS_EFFECTIVE = 1;
    const CONTRACT_STATUS_TERMINATE = 2;
    const CONTRACT_STATUS_CANCEL = 3;

    //
    public static $arrOrderStatusMap = array(
        self::ORDER_STATUS_PRICED        => 'ORDER_STATUS_PRICED',
        self::ORDER_STATUS_BOOKED_MANUAL => 'ORDER_STATUS_BOOKED_MANUAL',
        self::ORDER_STATUS_LOCKED_MANUAL => 'ORDER_STATUS_LOCKED_MANUAL',
        self::ORDER_STATUS_CANCEL_BOOKED => 'ORDER_STATUS_CANCEL_BOOKED',
        self::ORDER_STATUS_CANCEL_LOCKED => 'ORDER_STATUS_CANCEL_LOCKED',
        self::ORDER_STATUS_LOCKED_AUTO   => 'ORDER_STATUS_LOCKED_AUTO',
        self::ORDER_STATUS_RELEASED_AUTO => 'ORDER_STATUS_RELEASED_AUTO',
        self::ORDER_STATUS_PUT           => 'ORDER_STATUS_PUT',
       // self::ORDER_STATUS_ENDED         => 'ORDER_STATUS_ENDED',
        self::ORDER_STATUS_EXPIRED       => 'ORDER_STATUS_EXPIRED',
        self::ORDER_STATUS_INVALIDATE    => 'ORDER_STATUS_NVALIDATE',
    );
    
    //
    public static $arrOperationTypeNameMap = array(
        'book'        => self::ORDER_STATUS_BOOKED_MANUAL,
        'cancel_book' => self::ORDER_STATUS_CANCEL_BOOKED,
        'lock'        => self::ORDER_STATUS_LOCKED_MANUAL,
        'cancel_lock' => self::ORDER_STATUS_CANCEL_LOCKED,
    );

    public function execute() {
        
    }
    
}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */    
?>
