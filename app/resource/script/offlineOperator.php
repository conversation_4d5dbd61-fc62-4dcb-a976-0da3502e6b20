<?php
@set_time_limit(0);
@ini_set("memory_limit", "4096M");
define ('ROOT_PATH', dirname(__FILE__) . "/..");
Tieba_Init::init("forum_resource");

class OfflineOperator
{
    /**
    * @param  : 
    * @return :
    */
    public static function getTaskList($arrInput = array())
    {
        $arrInput = array(
            'status_name'  => 'resource_status',
            'status_value' => 0,
        );
        $arrRes = Tieba_Service::call('toms', 'ACgetTaskList', $arrInput);
        if ($arrRes['errno'] !== 0)
        {
            echo "call toms::ACgetTaskList fail! ".serialize($arrRes).'_'.serialize($arrInput)."\n";
            return array();
        }

        return $arrRes['data'];
    }

    /**
    * @param  : 
    * @return :
    */
    public static function executeTask($arrTaskInfo)
    {
        if (!isset($arrTaskInfo['task_id']) || !isset($arrTaskInfo['operator_id']))
        {
            echo "task info error! ".serialize($arrTaskInfo)."\n";
        }

        $intTaskId     = intval($arrTaskInfo['task_id']);
        $intOperatorId = intval($arrTaskInfo['operator_id']);
        $intForumId    = intval($arrTaskInfo['forum_id']);
        $intOpUserId = intval($arrTaskInfo['op_user_id']);

        //set doing status
        $intRetry = 0;
        $bolRes = self::_setStatus($intTaskId, 2);
        while(($intRetry <= 5) && !$bolRes)
        {
            $bolRes = self::_setStatus($intTaskId, 2);
            $intRetry++;
        }
        if ($bolRes === false)
        {
            echo "set status error! task_id: ".$intTaskId.", status: doing\n";
            return false;
        }

        //offline task
        $bolRes = self::_offlineTask($intTaskId, $intOperatorId, $intForumId, $intOpUserId);
        while(($intRetry <= 5) && !$bolRes)
        {
            $bolRes = self::_offlineTask($intTaskId, $intOperatorId, $intForumId, $intOpUserId);
            $intRetry++;
        }

        if ($bolRes === false)
        {
            echo "set offline task error! task_id : ".$intTaskId."\n";
            $intRetry = 0;
            $bolRes = self::_setStatus($intTaskId, 0);
            while(($intRetry <= 5) && !$bolRes)
            {
                $bolRes = self::_setStatus($intTaskId, 0);
                $intRetry++;
            }
            if ($bolRes === false)
            {
                echo "reset status error! task_id: ".$intTaskId.", status: to do\n";
                return false;
            }
            return false;
        }

        //set done status
        $intRetry = 0;
        $bolRes = self::_setStatus($intTaskId, 1);
        while(($intRetry <=5) && !$bolRes)
        {
            $bolRes = self::_setStatus($intTaskId, 1);
            $intRetry++;
        }
        if ($bolRes === false)
        {
            echo "set status error! task_id: ".$intTaskId.", status: done\n";
            return false;            
        }

        return true;
    }

    /**
    * @param  : 
    * @return :
    */
    private static function _setStatus($intTaskId, $intStatus)
    {
        $arrInput = array(
            'task_id'      => $intTaskId,
            'status_name'  => 'resource_status',
            'status_value' => $intStatus, 
        );

        $arrRes = Tieba_Service::call('toms', 'ACupdateOfflineTaskStatus', $arrInput);
        if ($arrRes['errno'] !== 0)
        {
        
            echo "call toms::ACupdateOfflineTaskStatus fail! ".serialize($arrInput).'_'.serialize($arrRes)."\n";
            return false;
        }

        return true;
    }

    /**
    * @param  : 
    * @return :
    */
    private static function _offlineTask($intTaskId, $intOperatorId, $intForumId, $intOpUserId)
    {
        //get online throw
        $arrSelInput = array(
            'operator_id' => $intOperatorId,
            'forum_id'    => $intForumId,
        );
       
        $arrRes = Tieba_Service::call('resource', 'getOnlineOrderItemByOperator', $arrSelInput);
        
        if (0 !== $arrRes['errno']) {
            echo "call resource getOnlineOrderItemByOperator fail " . serialize($arrSelInput) . "_" . serialize($arrRes) . "\n";
            return false;
        }
        if (empty($arrRes['data']['online_info'])) {
            echo "operator_id:$intOperatorId,forum_id:$intForumId has no online throw!\n";
            return true;
        }
        
        //update order item status to ORDER_STATUS_CLOSE_OPERATOR_OFFLINE
        $arrInput = array(
            'id'         => $arrRes['data']['online_info'],
            'op_user_id' => $intOpUserId,
        );
        $arrOfflineRes = Tieba_Service::call('resource', 'operatorOfflineMaterialClose', $arrInput);
        if (0 !== $arrOfflineRes['errno']) {
            echo "call resource operatorOfflineMaterialClose fail " . serialize($arrInput) . "_" . serialize($arrOfflineRes) . "\n";
            return false;
        }
        
        //callback displaysys
        $arrDisplayInput = array(
            'task_id'    => $arrRes['online_info'],
            'op_user_id' => $intOpUserId,
        );
        $arrDisplayRes = Tieba_Service::call('displaysys', 'closeTaskByTid', $arrDisplayInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrDisplayRes['errno']) {
            Bingo_Log::warning('call displaysys closeTaskByTid fail' . serialize($arrDisplayInput). '_' . serialize($arrDisplayRes));
            return false;
        }
        return true;
    }
}

//OfflineOperator::_offlineTask(1, 43,2296654,123 );
//exit;

$arrTaskList = OfflineOperator::getTaskList();
foreach($arrTaskList as $arrTaskInfo)
{
    echo "excute task start : ".$arrTaskInfo['task_id']."\n";
    $bolRes = OfflineOperator::executeTask($arrTaskInfo);
    if ($bolRes === false)
    {
        echo "execute task fail! task_id: ".$intTaskId."\n";
    }
    echo "excute task end : ".$arrTaskInfo['task_id']."\n";
}