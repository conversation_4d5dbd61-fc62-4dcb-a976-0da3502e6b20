/*==============================================================*/
/* 开发体系数据库                                          */
/*==============================================================*/

DROP DATABASE IF EXISTS `forum_developer`;
CREATE DATABASE `forum_developer`;
USE forum_developer;

-- 工具表
DROP TABLE IF EXISTS `tool`;
CREATE TABLE `tool` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT comment '自增主键',
  `name` VARCHAR(64) NOT NULL DEFAULT '' comment '工具名称',
  `url` VARCHAR(1024) NOT NULL DEFAULT '' comment '工具url',
  `icon` VARCHAR(1024) NOT NULL DEFAULT '' comment '图片url',
  `description` VARCHAR(256) NOT NULL DEFAULT '' comment '简介',
  `readme` VARCHAR(1024) NOT NULL DEFAULT '' comment '使用说明',
  `download` VARCHAR(1024) NOT NULL DEFAULT '' comment '附件下载地址',
  `email` VARCHAR(64) NOT NULL DEFAULT '' comment '负责人邮箱前缀',
  `hi` VARCHAR(64) NOT NULL DEFAULT '' comment '负责人hi',
  `op_time` int(11) unsigned NOT NULL comment '修改时间',
  `level1` tinyint unsigned NOT NULL comment '一级属性：0是工具平台、1服务平台、2容灾平台',
  `level2` tinyint unsigned NOT NULL comment '二级属性',
  `level3` tinyint unsigned NOT NULL comment '三级属性',
  `in_index` tinyint unsigned NOT NULL comment '是否首页',
  `position` tinyint unsigned NOT NULL comment '所处的位置：1终端、2ui、3service、4dl、5common、6storage',
  `pv` int(11) unsigned NOT NULL comment '访问量',
  `pv_time` int(11) unsigned NOT NULL comment '上一次访问量自增的时间',
  KEY `idx_all_level`(level1, level2, level3),
  KEY `idx_llp`(level1, level2, pv),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 首页幻灯片表
DROP TABLE IF EXISTS `slide`;
CREATE TABLE `slide` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT comment '自增主键',
  `op_time` int(11) unsigned NOT NULL comment '修改时间',
  `text` VARCHAR(256) NOT NULL DEFAULT '' comment '标题',
  `url` VARCHAR(256) NOT NULL DEFAULT '' comment 'url',
  `front_img` VARCHAR(256) NOT NULL DEFAULT '' comment '前面的图',
  `back_img` VARCHAR(256) NOT NULL DEFAULT '' comment '后面的图',
  index(op_time),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 动态表
DROP TABLE IF EXISTS `news`;
CREATE TABLE `news` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT comment '自增主键',
  `op_time` int(11) unsigned NOT NULL comment '动态时间',
  `content` VARCHAR(256) NOT NULL DEFAULT '' comment '动态内容',
  index(op_time),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 示例代码表
DROP TABLE IF EXISTS `code`;
CREATE TABLE `code` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  -- 示例代码标题，支持检索
  `title` VARCHAR(1024) NOT NULL DEFAULT '',
  -- 示例代码内容
  `code` TEXT NOT NULL,
  -- 负责人hi
  `author` VARCHAR(64) NOT NULL DEFAULT '',
  -- 修改时间
  `op_time` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 全局宏表
DROP TABLE IF EXISTS `macro`;
CREATE TABLE `macro` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  -- 宏名称
  `name` VARCHAR(128) NOT NULL DEFAULT '',
  -- 负责人hi
  `author` VARCHAR(64) NOT NULL DEFAULT '',
  -- 适用范围id：0 自定义（此时use_scope有用）、1 ui、2 service
  `use_scope_id` int(11) unsigned NOT NULL,
  `use_scope` VARCHAR(1024) NOT NULL DEFAULT '',
  -- 修改时间
  `op_time` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 文档平台数据
DROP TABLE IF EXISTS `document`;
CREATE TABLE `document` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT comment '自增主键',
  `service` VARCHAR(20) NOT NULL DEFAULT '' comment 'service名',
  `sub_service` VARCHAR(20) NOT NULL DEFAULT '' comment '子service名',
  `category` VARCHAR(64) NOT NULL DEFAULT '' comment '内容类别',
  `name` VARCHAR(64) NOT NULL DEFAULT '' comment '函数名、结构体名等',
  `section` VARCHAR(64) NOT NULL DEFAULT '' comment '分节',
  `content` VARCHAR(10000) NOT NULL DEFAULT '' comment '内容',
  `last_content` VARCHAR(10000) NOT NULL DEFAULT '' comment '更改前内容',
  `in_time` int(11) unsigned NOT NULL DEFAULT 0 comment '入库时间',
  `is_modify` tinyint unsigned NOT NULL DEFAULT 0 comment '是否自定义',
  `status` tinyint unsigned NOT NULL DEFAULT 0 comment '状态 0:正常 1:已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_ssss` (`service`, `sub_service`, `category`, `name`, `section`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- service接入表
DROP TABLE IF EXISTS `enroll`;
CREATE TABLE `enroll` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT comment '自增id',
  `applicant` varchar(32) NOT NULL default '' comment '申请人',
  `caller_module_name` varchar(32) NOT NULL default '' comment '调用方模块名',
  `caller_module_level` tinyint unsigned NOT NULL comment '调用方模块层级：0是ui、1是service、2是openapi',
  `service` varchar(32) NOT NULL default '' comment 'servie名',
  `sub_service` varchar(32) NOT NULL default '' comment '子servie名',
  `method` varchar(64) NOT NULL default '' comment 'method名',
  `purpose` VARCHAR(1024) NOT NULL DEFAULT '' comment '用途',
  `avg_qps_in_month` int(11) unsigned NOT NULL comment '3个月内平均qps',
  `max_qps_in_month` int(11) unsigned NOT NULL comment '3个月内高峰qps',
  `increase_rate` int(11) unsigned NOT NULL comment '1年内qps增长率',
  `status` tinyint unsigned NOT NULL comment '状态：1待审核；2审核未通过；3审核通过待上线；4已上线',
  `suggestion` VARCHAR(1024) NOT NULL DEFAULT '' comment '不同意后的意见',
  `apply_op_time` int(11) unsigned NOT NULL comment '申请操作时间',
  `audit_op_time` int(11) unsigned NOT NULL comment '审核操作时间',
  `online_op_time` int(11) unsigned NOT NULL comment '上线操作时间',
  `auditor` varchar(32) NOT NULL default '' comment '最近审核人',
  
  PRIMARY KEY (`id`),
  KEY `idx_applytime`(apply_op_time),
  UNIQUE KEY `idx_cssm` (`caller_module_name`,`service`,`sub_service`,`method`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- service授权名单表
DROP TABLE IF EXISTS `authorized`;
CREATE TABLE `authorized` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT comment '自增id',
  `applicant` varchar(32) NOT NULL default '' comment '申请人',
  `caller_module_name` varchar(32) NOT NULL default '' comment '调用方模块名',
  `caller_module_level` tinyint unsigned NOT NULL comment '调用方模块层级：0是ui、1是service、2是openapi',
  `service` varchar(32) NOT NULL default '' comment 'servie名',
  `sub_service` varchar(32) NOT NULL default '' comment '子servie名',
  `method` varchar(64) NOT NULL default '' comment 'method名',
  `purpose` VARCHAR(1024) NOT NULL DEFAULT '' comment '用途',
  `avg_qps_in_month` int(11) unsigned NOT NULL comment '3个月内平均qps',
  `max_qps_in_month` int(11) unsigned NOT NULL comment '3个月内高峰qps',
  `increase_rate` int(11) unsigned NOT NULL comment '1年内qps增长率',
  `status` tinyint unsigned NOT NULL comment '状态：1待审核；2审核未通过；3审核通过待上线；4已上线',
  `suggestion` VARCHAR(1024) NOT NULL DEFAULT '' comment '不同意后的意见',
  `apply_op_time` int(11) unsigned NOT NULL comment '申请操作时间',
  `audit_op_time` int(11) unsigned NOT NULL comment '审核操作时间',
  `online_op_time` int(11) unsigned NOT NULL comment '上线操作时间',
  `auditor` varchar(32) NOT NULL default '' comment '最近审核人',
  
  PRIMARY KEY (`id`),
  KEY `idx_applytime`(apply_op_time),
  UNIQUE KEY `idx_cssm` (`caller_module_name`,`service`,`sub_service`,`method`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8; 

alter table enroll drop column avg_qps_in_year;
alter table enroll drop column max_qps_in_year;
alter table enroll drop column exception;
alter table authorized drop column avg_qps_in_year;
alter table authorized drop column max_qps_in_year;
alter table authorized drop column exception;
DROP TABLE IF EXISTS `enroll_status`;
DROP TABLE IF EXISTS `caller_module`;
-- 接入版本表
DROP TABLE IF EXISTS `enroll_conf_version`;
CREATE TABLE `enroll_conf_version` (
  `version` int(11) unsigned NOT NULL AUTO_INCREMENT comment '自增版本',
  `conf_file` MEDIUMTEXT NOT NULL DEFAULT '' comment '配置文件内容',
  `save_time` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '配置保存时间',  
  PRIMARY KEY (`version`)
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT '接入版本表';

-- 异常监控配置表
DROP TABLE IF EXISTS `enroll_expt_monitor`;
CREATE TABLE `enroll_expt_monitor` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT comment '自增id',
  `monitor` varchar(64) NOT NULL default '' comment '监控项：默认监控是default，method是method:{service}:{method}',
  `conf_type` tinyint unsigned NOT NULL DEFAULT 0 comment '异常事件：0是大于申请qps、1是小于申请qps、2容量',
  `conf_content` varchar(256) NOT NULL default '' comment '事件内容',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_mc` (`monitor`,`conf_type`)
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='异常监控配置表';

-- 异常服务表
DROP TABLE IF EXISTS `enroll_expt_list`;
CREATE TABLE `enroll_expt_list` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT comment '自增id',
  `caller_module_name` varchar(32) NOT NULL default '' comment '调用方模块名',
  `service` varchar(32) NOT NULL default '' comment 'servie名',
  `sub_service` varchar(32) NOT NULL default '' comment '子servie名',
  `method` varchar(64) NOT NULL default '' comment 'method名',
  `expt_count` int(11) unsigned NOT NULL DEFAULT 0 comment '累计异常次数',
  `conf_type` tinyint unsigned NOT NULL DEFAULT 0 comment '异常事件：0是大于申请qps、1是小于申请qps、2容量',
  `modify_time` int(11) unsigned NOT NULL comment '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_modifytime`(modify_time),
  UNIQUE KEY `idx_ccssm` (`conf_type`,`caller_module_name`,`service`,`method`,`sub_service`)
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='异常服务表';
-- 上线状态表
DROP TABLE IF EXISTS `enroll_online_status`;
CREATE TABLE `enroll_online_status` (
  `op_time` int(11) unsigned NOT NULL comment '操作时间',
  `status` tinyint unsigned NOT NULL comment '上线状态：1上线成功,2上线失败',
  `type` tinyint unsigned NOT NULL comment '上线类型',
  
  PRIMARY KEY (`op_time`)  
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='上线状态表';

DROP TABLE IF EXISTS `flevelinfo`;
-- ## flevelinfo 第一级错误号 表
CREATE TABLE `flevelinfo` (
  `flevel_no` varchar(20)  NOT NULL default '0' COMMENT '一级错误号',
  `fdesc` varchar(200) NOT NULL default '0' COMMENT '一级错误号描述',
  PRIMARY KEY  (flevel_no)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `slevelinfo`;
-- ## slevelinfo 第二级错误号 表
CREATE TABLE `slevelinfo` (
  `flevel_no` varchar(20)  NOT NULL default '0' COMMENT '一级错误号',
  `slevel_no` varchar(20)  NOT NULL default '0' COMMENT '二级错误号',
  `sdesc` varchar(200) NOT NULL default '0' COMMENT '二级错误号描述',
   PRIMARY KEY post_pk(flevel_no, slevel_no)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS `tlevelinfo`;
-- ## tlevelinfo 第三级错误号 表
CREATE TABLE `tlevelinfo` (
  `flevel_no` varchar(20)  NOT NULL default '0' COMMENT '一级错误号',
  `slevel_no` varchar(20)  NOT NULL default '0' COMMENT '二级错误号',
  `tlevel_no` varchar(20)  NOT NULL default '0' COMMENT '三级错误号',
  `tdesc` varchar(200) NOT NULL COMMENT '三级错误号描述',
  `const` varchar(200) NOT NULL COMMENT '错误号常量',
  `engdesc` varchar(200) NOT NULL COMMENT '英文描述',
  `chndesc` varchar(200) DEFAULT NULL COMMENT '中文描述',
  `errmod` varchar(200) DEFAULT NULL COMMENT '出错模块',
   PRIMARY KEY post_pk(flevel_no, slevel_no,tlevel_no)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `stability`;
-- ## stability 稳定性错误号表
CREATE TABLE `stability` (
  `err_no` varchar(20) NOT NULL PRIMARY KEY DEFAULT '0' COMMENT '错误号',
  `map_err_no` varchar(20) NOT NULL DEFAULT '0' COMMENT '稳定性错误号',
  UNIQUE INDEX `err_map`(`err_no`,`map_err_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
