<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gbk" />
<title>理想框架检测报告</title>
<style type="text/css">
body {
margin:0;
padding:0 0 0 0;
font-size:12px;
}
html
{
overflow-x:hidden;
overflow-y:auto;
}
html, body, .ppc {
height: 100%;
}
a:link, a:visited, a:active, a:focus {
color:#666;
}
a:hover {
color:#09f;
}
.bt1 {
font-size: 24px;
font-family: "微软雅黑";
}
.bt2 {
font-size: 12px;
color:#999;
}
.getiao {
width: 100%;
height: 46px;
background-image: url(images/pps_04.gif);
overflow: hidden;
}
.getiao_xsd {
float: left;
width: 220px;
display: inline;
}
.getiao_wz {
width: 75px;
float: left;
margin-top: 14px;
margin-left: 0px;
display: inline;
}
.getiao_percent {
width: 30px;
float: left;
margin-top: 13px;
margin-left: 0px;
display: inline;
}
.getiao img {
border: 1px solid #666666;
margin-top: 11px;
float: left;
}
.getiao_an {
float: right;
height: 30px;
width: 400px;
margin-top: 5px;
display: block;text-align: right;

}
.getiao_an a {
float: left;
height: 20px;
padding-top: 7px;
padding-right: 20px;
padding-left: 20px;
overflow: hidden;
background-repeat: repeat-x;
border: 1px solid #939393;
text-decoration: none;
color: #666;
margin-right: 5px;
margin-left: 5px;
}
.getiao_an a:link, .getiao_an a:visited, .getiao_an a:active, .getiao_an a:focus {
background-image: url(images/pps_an.gif);
}
.getiao_an a:hover {
background-image: url(images/pps_anx.gif);
}
.top {
background-color:#E2E2E2;
height: 8px;
margin:0;
padding:0 0 0 0;
}
.bottom {
background-image: url(images/di.gif);
background-repeat: repeat-x;
width: 100%;
display: block;
height: 40px;
bottom: 0px;
vertical-align: bottom;
margin-bottom: 0px;
position:relative;
left: 0px;
overflow: hidden;
}
.left, .right {
background-repeat: repeat-y;
width: 31px;
vertical-align: top;
position: relative;
height: 100%
overflow: hidden;
}
.left {
background-image: url(images/pps_left.gif);
}
.right {
background-image: url(images/pps_right.gif);

}
<!--
user_icon {
color: #FFFFFF;
}
html
{
overflow-x:hidden;
overflow-y:auto;
}
body,td,th {
font-family: "微软雅黑";
font-size: 12px;
}
h1,h2,h3,h4,h5,h6 {
font-family: "宋体";
}
p{
margin-bottom:10px;
}
demo_padding {
line-height: 30px;
}
.zhengwen {
padding-right: 15px;
padding-left: 5px;
padding-bottom:100px;
font-size: 13px;
line-height: 20px;
color: #666666;
}
.zhengwencenter {
padding-right: 15px;
padding-left: 0px;
margin-bottom:10px;
font-size: 13px;
line-height: 20px;
color: #666666;
text-align:center
}
.neikuang {
background-color: #EBEBEB;
border: 1px solid #999999;
padding-right: 10px;
padding-left: 10px;
margin-top:10px;
margin-left:25px;
width:300px;
}
.shubu{
height: 35px;
width: 400px;
margin-left:25px;
margin-top:25px;
background-color: #FFFFFF;
border: 1px solid #999999;
text-align: left;
vertical-align: middle;
display: block;
color: green;
}
a.red:link {color:#FF0000}
a.red:visited {color:#FF0000}
a.red:hover {color:#000000}
a.red:active {color:#000000}

a.orange:link {color:#FF6600}
a.orange:visited {color:#FF6600}
a.orange:hover {color:#000000}
a.orange:active {color:#000000}

a.dark:link {color:#666666}
a.dark:visited {color:#666666}
a.dark:hover {color:#000000}
a.dark:active {color:#000000}

a.pagelink:hover {color:#000000}
a.pagelink:active {color:#000000}

.green{color:#008000}
.gray{color:#666666}
.red{color:#FF0000}
.orange{color:#FF6600}
a{TEXT-DECORATION:none}

-->
</style>
</head>

<body>
<div>
<table width="100%" height="100%" border="0" cellspacing="0" cellpadding="0">
<tr>
<td class="left"><img src="images/pps_02_.gif" width="31" height="198" /></td>
<td valign="top" align="center">

<table width="100%" border="0" cellspacing="0" cellpadding="0">
<tr>
<td class="top"></td>
</tr>
<tr>
<td valign="top" align="center">

<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
<tr>
<td width="10px" height="80"></td>
<td width="182px" height="80" align="left"><a target="_blank" href="http://www.paperpass.org/"><img border="0" /></a></td>
<td align="center">

<table border="0" cellpadding="0" cellspacing="0">
<tr>
<td class="bt1">理想框架check工具</td>
</tr>
<tr>
<td class="bt2">检测时间:time</td>
</tr>
</table>

</td>
<td width="153" align="right">
<div style="color:#666; list-style-type: none; line-height: 20px;margin:10px">

<table width="100px" border="0" cellspacing="0" cellpadding="0">
<tr><td align="left"></td></tr>

<tr><td align="left"></td></tr>

<tr><td align="left"></td></tr>
</table>
</div>
</td>
</tr>
</table>

</td>
</tr>
<tr>
<td valign="top" align="center">

<div class="getiao">

<div style="margin-left: 20px;">
<div class="getiao_xsd">
</div>


</div>

<div class="getiao_an" style="width: 400px; height: 85px">

<a href="./result_report.htm" target='ViewMain'>
详细报告
</a>
<a href="./dir_report.htm" target='ViewMain'>
检查目录
</a>
<a href="./error_report.htm" target='ViewMain'>
错误报告
</a>
<a href="./usage_report.htm" target='ViewMain'>
使用说明
</a>

</div>

</div>

</td>
</tr>
<tr>
<td valign="top" align="left">

<!--替换开始-->
<P style="margin-left:25px" class="green"><font size="3" color="blue">
/**                                                       <br>
 * *******************************************************************************<br>
 * 贴吧理想框架check工具                                                  <br>                               
 * 使用方法usage()                                            <br>
 * (整个系统)/bin/php checkTool.php     frame     all/dl/service/ui   /home/<USER>/orp001/
 * (子系统)  /bin/php checkTool.php   mall|post   all/dl/service/ui   /home/<USER>/orp001/
 * +++++++++++++++++++++++++++++++++++++++++++++++++++++++<br>
 * 第一个参数：模块系统或子系统                                         <br>
 *            frame   ：orp001/app 整体目录全部检查                  <br>
 *            mall    ：orp001/app/mall 目录进行检查           <br>
 * +++++++++++++++++++++++++++++++++++++++++++++++++++++++<br>
 * 第二个参数：模块类型                                             <br>
 *            all     : 子系统中ui|dl|service|ui 等模块全部检测      <br>
 *            dl      : 子系统中dl进行检查                        <br>
 *            service ：子系统中service进行检查                    <br>
 *            ui      ：子系统中ui进行检查                         <br>
 * +++++++++++++++++++++++++++++++++++++++++++++++++++++++<br>
 * 第三个参数：模块路径                                             <br>
 *            path    ：orp001的路径，系统自动寻找路径下的各个模块目录      <br>
 * *******************************************************<br>
 */                                                       <br>
                                                          <br>

</p>
<!--替换结束-->


</td>
</tr>
</table>
</td>
<td class="right"><img src="images/pps_02.gif" width="31" height="198" /></td>
</tr>
</table>

<table width="100%" border="0" cellspacing="0" cellpadding="0">
<tr>
<td align="left" width="50%">
<div class="bottom">
<img src="images/pps_06_.gif" />
</div>
<td>
<td align="right"  width="50%">
<div class="bottom">
<img src="images/pps_06.gif"/>
</div>
<td>
</tr>
</table>

</div>
</body>
</html>
