<?php
/**
 * brief: 调用外部http接口格式化php代码
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-12-26 15:32:03
 * @version
 */
class Util_Phpformater {
	public static function format($code){
		if(empty($code) || strlen($code)<=0){
			return false;
		}
		
		$code = ltrim(rtrim($code));
		
		if(!self::startWith($code, "<?php ")){
			$code = "<?php ".$code;
		}
		
		if(!self::endWith($code, " ?>")){
			$code .= " ?>";
		}
		
		$url = "http://beta.phpformatter.com/Output/";
		
		$headers = array(
			'Host: beta.phpformatter.com',
			'Origin: http://beta.phpformatter.com',
			'Referer: http://beta.phpformatter.com/',
			'User-Agent: Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.95 Safari/537.36',
			'Content-Type: application/x-www-form-urlencoded',
			'Accept-Language: zh-CN,zh;q=0.8,en-US;q=0.6,en;q=0.4',
			'Accept: */*',
			'Connection: keep-alive',
		);
		$time_array = explode(" ", microtime());
		$time = floor(($time_array[0]+$time_array[1]) * 1000);
		$input = array(
				"time"=>$time,
				"spaces_around_map_operator"=>"on",
				"spaces_around_assignment_operators"=>"on",
				"spaces_around_bitwise_operators"=>"on",
				"spaces_around_relational_operators"=>"on",
				"spaces_around_equality_operators"=>"on",
				"spaces_around_logical_operators"=>"on",
				"spaces_around_math_operators"=>"on",
				"space_after_structures"=>"on",
				"align_assignments"=>"on",
				"remove_empty_lines"=>"on",
				"indent_case_default"=>"on",
				"indent_number"=>"4",
				"first_indent_number"=>"0",
				"indent_char"=>" ",
				"indent_style"=>"PEAR",
				"code"=>$code,
		);
		
		Bingo_Timer::start('php_formater');
		$httpproxy = Orp_FetchUrl::getInstance(array('timeout' =>30000,'conn_timeout' =>10000,'max_response_size'=> 1024000));
		$resStr = $httpproxy->post($url,$input,$headers);
		Bingo_Timer::end('php_formater');
		
		$format_res = json_decode($resStr,true);
		if($format_res['error'] === true){
			$msg = sprintf("in line %s , %s",$format_res['line'],$format_res['text']);
			$arrOutput = array(
					'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
					'errmsg' => $msg,
					'res' => false,
			);
			return $arrOutput;
		}
		$code_str = $format_res['plainoutput'];
		if(self::startWith($code_str, "<?php")){
			$code_str = substr($code_str,strlen("<?php"));
		}
		if(self::endWith($code_str, "?>")){
			$code_str = substr($code_str,0,0-(strlen("?>")));
		}
		$code_str = ltrim(rtrim($code_str));
		//$code_str .= "\n";
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'res' => $code_str,
		);
		return $arrOutput;
	}
		
		
	private static function startWith($str, $needle) {
		
		return strpos($str, $needle) === 0;
		
	}
		
		
		
	private static function endWith($haystack, $needle)
	{
		$length = strlen($needle);
		if($length == 0)
		{
			return true;
		}
		return (substr($haystack, -$length) === $needle);
	}
}
?>