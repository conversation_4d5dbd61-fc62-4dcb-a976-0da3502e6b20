<?php
/**
* A brief description for this file
*
* @copyright Copyright (c) www.baidu.com
* @file  Pb.php
* <AUTHOR> <<EMAIL>>
* @date 2015-3-12 上午10:38:20
* @version 
* @structs & methods(copied from idl.)
*/
class Service_Pb_Message {
	protected $name = null;
	protected $itemType = null;
	protected $items = null;
	protected $hasSubMsg = false;
	protected $subMsgs = null;
	protected $order = 0;
	protected $isNULL = false;
	protected static $messageCollection = array ();
	protected static $ENVNEWLINE = "\n";
	const OLDBETTER = 0;
	const NEWBETTER = 1;
	const MERGEBETTER = 2;
	const DIFFERENTITEM = 3;
	const MESSAGE_CLASS = 'Service_Pb_Message';
	const MESSAGE_ITEM_CLASS = 'Service_Pb_MessageItem';
	function __construct() {
		$this->items = array ();
		$this->name = "";
		$this->itemType = "optional";
		$this->hasSubMsg = false;
		$this->subMsgs = array ();
	}
	public static function getCollection() {
		return self::$messageCollection;
	}
	public static function cleanCollection() {
		self::$messageCollection = array ();
	}

	public static function loadFromObject($name, $obj) {
		$message = new Service_Pb_Message ();
		$name = ucfirst ( $name );
		$message->name = strval ( $name );
		$order = 1;
		
		if (gettype ( $obj ) == "object") {
			foreach ( $obj as $key => $value ) {
				$keyValid = self::checkKeyValid($key);
				if(!$keyValid){
					continue;
				}                
				$key = trim ( $key );
				$isnumeric = is_numeric ( $key );
				if (! $isnumeric) {
					if (gettype ( $value ) == "object") {
						$item = self::loadFromObject ( $key, $value );
						$message->hasSubMsg = true;
						$addItem = new Service_Pb_MessageItem ( strval ( $key ), $item->name, 1 );
						$message->items [strval ( $key )] = $addItem;
					} else if (gettype ( $value ) == "array") {
						$item = self::loadArray ( $key, $value );
						if (get_class ( $item ) == self::MESSAGE_CLASS) {
							$message->hasSubMsg = true;
							$item = new Service_Pb_MessageItem ( $item->name, $item->isNULL ? "bytes" : $item->name, 1 );
						}
						$message->items [strval ( $key )] = $item;
					} else {
						$item = new Service_Pb_MessageItem ( strval ( $key ), self::getPBValueType ( $value ), 0 );
						$message->items [strval ( $key )] = $item;
					}
					$message->items [strval ( $key )]->order = $order ++;
				} else {
					/*
					 * $item = new Service_Pb_MessageItem ( strval ( $name ), 'bytes', 0 ); var_dump ( $item ); return $item;
					 */
				}
			}
		} else {
			return null;
		}
		
		if (array_key_exists ( strval ( $name ), self::$messageCollection )) {
			switch (self::checkMessage ( self::$messageCollection [strval ( $name )], $message )) {
				case self::OLDBETTER :
					break;
				case self::NEWBETTER :
					self::$messageCollection [strval ( $name )] = $message;
					break;
				case self::MERGEBETTER :
					break;
				case self::DIFFERENTITEM :
					break;
				default :
					break;
			}
		} else {
			self::$messageCollection [strval ( $name )] = $message;
		}
		return $message;
	}
	private static function loadArray($key, $arr) {
		$retVal = null;
		if (count ( $arr ) == 0) {
			$retVal = new Service_Pb_Message ();
			$retVal->name = $key;
			$retVal->itemType = "repeated";
			$retVal->isNULL = true;
		} else {
			$item = $arr [0];
			if (gettype ( $item ) == "object") {
				$retVal = self::loadFromObject ( $key, $item );
				$retVal->itemType = "repeated";
			} else if (gettype ( $item ) == "array") {
				$retVal = new Service_Pb_MessageItem ( $key, 'bytes', 0 );
			} else {
				$retVal = new Service_Pb_MessageItem ( $key, self::getPBValueType ( $item ), 0 );
				$retVal->itemType = "repeated";
			}
		}
		return $retVal;
	}
	private static function getPBValueType($value) {
		$valueType = gettype ( $value );
		// var_dump("===$valueType====");
		switch ($valueType) {
			case 'integer' :
				$valueType = 'uint64';
				break;
			case 'boolean' :
				$valueType = 'boolean';
				break;
			case 'double' :
				$valueType = 'double';
				break;
			case 'string' :
				$valueType = 'string';
				break;
			default :
				$valueType = 'string';
				break;
		}
		return $valueType;
	}
	private static function checkMessage($oldMessage, $newMessage) {
		if (get_class ( $oldMessage ) != self::MESSAGE_CLASS || get_class ( $newMessage ) != self::MESSAGE_CLASS) {
			return self::DIFFERENTITEM;
		}
		
		if ($newMessage->isNULL) {
			return self::OLDBETTER;
		} else if ($oldMessage->isNULL) {
			return self::NEWBETTER;
		}
		$diffOldItems = array_diff_key ( $oldMessage->items, $newMessage->items );
		$diffNewItems = array_diff_key ( $newMessage->items, $oldMessage->items );
		if (count ( $diffNewItems ) == 0) {
			return self::OLDBETTER;
		} else if (count ( $diffOldItems ) == 0) {
			return self::NEWBETTER;
		} else {
			$mergedItem = self::mergeItem ( $oldMessage, $newMessage );
			if ($mergedItem == null) {
				return self::DIFFERENTITEM;
			}
			return self::MERGEBETTER;
		}
	}
	private static function mergeItem($oldMessage, $newMessage) {
		$diffItems = array_diff_key ( $newMessage->items, $oldMessage->items );
		$firstKeys = array (
				key ( $oldMessage->items ) 
		);
		$secondKeys = array (
				key ( $newMessage->items ) 
		);
		if (count ( $firstKeys ) > 0 && count ( $secondKeys ) > 0) {
			try {
				$commonKeys = array_intersect ( $firstKeys, $secondKeys );
				if (count ( $commonKeys ) > 0) {
					foreach ( $commonKeys as $key ) {
						// 检察相同的对象中,是否存在不同的小对象
						$first = $oldMessage->items [$key];
						$second = $newMessage->items [$key];
						if (get_class ( $first ) == self::MESSAGE_CLASS && get_class ( $second ) == self::MESSAGE_CLASS) {
							continue;
						} else if (get_class ( $first ) == self::MESSAGE_CLASS || get_class ( $second ) == self::MESSAGE_CLASS) {
							return null;
						}
						
						if ($first->itemType != $second->itemType) {
							$first->itemType = 'optional';
							$second->itemType = 'optional';
						}
						
						if ($first->valueType != $second->valueType) {
							if ($first->valueType == 'bytes' || $second->valueType == 'bytes') {
								$first->valueType = 'bytes';
								$second->valueType = 'bytes';
							} else {
								$first->valueType = 'string';
								$second->valueType = 'string';
							}
						}
					}
				}
			} catch ( Exception $ex ) {
			}
		}
		$order = count ( $oldMessage->items ) + 1;
		foreach ( $diffItems as $key => $item ) {
			$item->order = $order ++;
			$oldMessage->items [$key] = $item;
		}
		return $oldMessage->items;
	}
	public static function dumpToFile($leafOnly, $filePath) {
		$file = fopen ( $filePath, "wb" );
		if ($file == false) {
			return false;
		}
		
		foreach ( self::$messageCollection as $item ) {
			if (! $leafOnly || ! $item->hasSubMsg) {
				fwrite ( $file, $item->asProto () );
			}
		}
		fclose ( $file );
		return true;
	}
	public function asProto() {
		$retVal = vsprintf ( "message %s { %s", array (
				$this->name,
				self::$ENVNEWLINE 
		) );
		foreach ( $this->items as $item ) {
			$retVal = vsprintf ( "%s\t%s %s %s=%s;%s", array (
					$retVal,
					$item->itemType,
					(get_class ( $item ) == self::MESSAGE_CLASS) ? $item->name : $item->valueType,
					$item->name,
					$item->order,
					self::$ENVNEWLINE 
			) );
		}
		$retVal = vsprintf ( "%s}%s", array (
				$retVal,
				self::$ENVNEWLINE 
		) );
		return $retVal;
	}
	public static function dumpToArray($name, $obj) {
		self::cleanCollection();
		self::loadFromObject($name, $obj);
		//var_dump ( self::$messageCollection );
		$arr = array();
		foreach ( self::$messageCollection as $item ) {
			//if (! $item->hasSubMsg) {
				$messageArr = $item->asArray ();
				$arr[] = $messageArr;
			//}
		}
      return $arr;
	}
	public function asArray() {
		$messageArr = array ();
		$messageArr ['name'] = strval ( $this->name );
		$messageArr ['items'] = array ();
		foreach ( $this->items as $item ) {
			$itemArr = array ();
			$itemArr ['qualifier'] = $item->itemType;
			$itemArr ['type'] = $item->valueType;
			$itemName = $item->name;
			$itemArr ['name'] = $itemName;
			$itemArr ['id'] = $item->order;
			$itemArr ['status'] = 0;
			$itemArr ['brief'] = '';
			$messageArr ['items'] [$itemName] = $itemArr;
		}		
		return $messageArr;
	}
	
	/**
	 * @param  $service
	 * @param  $method
	 * @param  $arrInput
	 * @return
	 */
	private static function checkKeyValid($key) {
		$arrIgnoreKey = array('BAIDUID','BDUSS','BAIDU_WISE_UID','IS_NEW_USER','TIEBAUID');
		$startLetter = substr($key, 0,1);
		if(in_array($key, $arrIgnoreKey)|| ('_' == $startLetter)){
			return false;
		}
		return true;		
	}
}

?>