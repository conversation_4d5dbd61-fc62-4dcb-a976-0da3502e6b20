<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-02-28
 * @version 1.0
 */

class Service_Document_Idl {

private static $regAuthor = '/\/\/@author[\s]*[:：][\s]*([^\s]*)[\s]*([^\s]*)/i';
private static $regServiceBrief = '/[*]*[\s]*@brief[\s]*[:：]*[\s]*([\S\s]*)/u';
private static $regBrief = '/\/\/@description[\s]*[:：][\s]*([^:：]*)/u';

private static $regStruct = '/struct[\s]+([\w]+)[\s]*[\{]*[\s]*[\/]*[\s]*[\S]*/i';
private static $regStructContent = '/[\s]*([\w]*[\S]*)[\s]*([\w]*[\S]*)[\s]*;[\s\/]*([\S\s]*)/u';
private static $regStructContentOptional = '/[\s]*([\w]*[\S]*)[\s]*([\w]*[\S]*)[\s]*=[\s]*optional\(\)[\s]*;[\s\/]*([\S\s]*)/u';

private static $regServiceIn = '/[*]*[\s]*@param[\s]*[:：]*[\s]*\[in\][\s]*[:：]*([^:：]*)[:：]([^:：]*)[:：](.*)/u';
private static $regServiceOut = '/[*]*[\s]*@param[\s]*[:：]*[\s]*\[out\][\s]*[:：]*([^:：]*)[:：]([^:：]*)[:：](.*)/u';
private static $regServiceVFun = '/^[\s]*void[\s]*([\w]+)[\s]*\((.*)\)/i';
private static $regServiceCFun = '/^[\s]*commit[\s]*void[\s]*([\w]+)[\s]*\((.*)\)/i';
private static $regServiceFun = '/^[\s]*(?:commit)*[\s]*void[\s]*([\w]*)/i';
private static $regFunctionAuthor = '/[*]*[\s]*@author[\s]*[:：]*([^\s]*)[\s]*([^\s]*)/u';

private static $arrAllStruct = array();
private static $_service = '';
private static $_subService = '';

//获取参数的注释
private static function buildFunParamDes($strKey, $intNum = 0, $arrNote) {
    $intNum = intval($intNum);
    return $arrNote[$strKey][$intNum]['brief'];
}

//将(commit) void fun(string input [] , out struct_info output)中的参数提取出来
private static function buildFunParam($strParam, $arrInNote, $arrOutNote) {
    $strParam = trim($strParam);
    $arrParam = array(
        'in' => array(),
        'out' => array(),
    );
    $arrDesRecord = array();
    if(strlen($strParam) <= 0) {
        return $arrParam;
    }
    //对中英文的逗号都支持
    $arrParamStr = preg_split('/[,，]+/i', $strParam);
    self::trimArr($arrParamStr);
    //处理每个参数
    foreach ($arrParamStr as $strItem) {
        $intIsArr = 0;
        //如果包含[和]则表示是一个数组
        if(false!==strpos($strItem, '[') && false!==strpos($strItem, ']')) {
            $intIsArr = 1;
        }
        $arrTmp = preg_split('/[\n\r\t\s]+/i', $strItem);
        //if is out param
        //无法处理输入参数为out input []这种情况，即输入参数类型名为out，这种情况属于idl书写不规范
        if('out' === $arrTmp[0] &&
            count($arrTmp) > 2) {
            $strType = $arrTmp[1];
            $strName = $arrTmp[2];
            $intIsStruct = (array_key_exists($strType, self::$arrAllStruct)) ? 1 : 0;
            $strKey = $strName.'_'.$strType;
            $arrDesRecord['out'][$strKey] = (isset($arrDesRecord['out'][$strKey]))?(++$arrDesRecord['out'][$strKey]):0;
            $strBrief = self::buildFunParamDes($strKey, $arrDesRecord['out'][$strKey], $arrOutNote);
            //去掉[]
            $strName = preg_replace('/\[|\]/', '', $strName);
            $arrParam['out'][] = array(
                'type' => $strType,    //参数类型
                'name' => $strName,    //参数名
                'array' => $intIsArr,    //是否数组
                'struct' => $intIsStruct,    //是否struct
                'brief' => strval($strBrief),    //注释
                'optional' => 0,    //是否可选，第一级参数目前都必选，只有struct里的参数存在optio
            );
        }
        //if is in param
        else {
            $strType = $arrTmp[0];
            $strName = $arrTmp[1];
            $intIsStruct = (array_key_exists($strType, self::$arrAllStruct)) ? 1 : 0;
            $strKey = $strName.'_'.$strType;
            $arrDesRecord['in'][$strKey] = (isset($arrDesRecord['in'][$strKey]))?(++$arrDesRecord['in'][$strKey]):0;
            $strBrief = self::buildFunParamDes($strKey, $arrDesRecord['in'][$strKey], $arrInNote);
            //去掉[]
            $strName = preg_replace('/\[|\]/', '', $strName);
            $arrParam['in'][] = array(
                'type' => $strType,    //参数类型
                'name' => $strName,    //参数名
                'array' => $intIsArr,    //是否数组
                'struct' => $intIsStruct,    //是否struct
                'brief' => strval($strBrief),    //注释
                'optional' => 0,    //是否可选，第一级参数目前都必选，只有struct里的参数存在optio
            );
        }
    }

    return $arrParam;
}

//对arrContent中的从intMin~intMax的元素进行trim
private static function trimArr(&$arrContent, $intMin = 0, $intMax = 0) {
    if($intMax <= 0) {
        $intMax = count($arrContent);
    }
    if($intMin < 0) {
        $intMin = 0;
    }
    for($i = $intMin; $i < $intMax; $i++) {
        $arrContent[$i] = trim($arrContent[$i]);
    }
}

//解析注释信息
private static function parseBrief($strLine, &$arrRet) {
    if( preg_match(self::$regBrief, $strLine, $arrContent) ) {
        $arrRet['service_brief'][''][''] = strval($arrContent[1]);
        return true;
    }
    return false;
}

//解析负责人信息
private static function parseAuthor($strLine, &$arrRet) {
    if( preg_match(self::$regAuthor, $strLine, $arrContent) ) {
        $arrRet['service_author']['']['mail'] = strval($arrContent[1]);
        $arrRet['service_author']['']['hi'] = strval($arrContent[2]);
        return true;
    }
    return false;
}

//解析一个struct信息
private static function parseStruct($strLine, &$mixFileHandle, &$arrRet) {
    $bolRet = false;
    if( preg_match(self::$regStruct, $strLine, $arrContent) ) {
        $strStructName = strval($arrContent[1]);
        if(strlen($strStructName === 0)) {
            return false;
        }
        self::$arrAllStruct[$strStructName] = 1;

        while(!feof($mixFileHandle)) {
            $strLine = preg_replace('/([\s]+)\/\//', '    //', trim(fgets($mixFileHandle)));
            $arrVar = array();
            if( $strLine=='{' || $strLine=='' ) {
                continue;
            } else if( $strLine=='};' || $strLine=='}' ) {
                break;
            }

            if( preg_match(self::$regStructContentOptional, $strLine, $arrContent) ) {
                $strType = strval($arrContent[1]);
                $strName = strval($arrContent[2]);
                $intIsArray = 0;
                //如果包含[和]则表示是一个数组
                if(false!==strpos($strType, '[') && false !== strpos($strType, ']')) {
                    $strType = preg_replace('/\[|\]/', '', $strType);
                    $intIsArray = 1;
                }
                if(false!==strpos($strName, '[') && false !== strpos($strName, ']')) {
                    $strName = preg_replace('/\[|\]/', '', $strName);
                    $intIsArray = 1;
                }
                $arrVar = array(
                    'array' => $intIsArray,
                    'type' => $strType,
                    'name' => $strName,
                    'brief' => strval($arrContent[3]),
                    'optional' => 1,
                );
            } else if( preg_match(self::$regStructContent, $strLine, $arrContent) ) {
                $strType = strval($arrContent[1]);
                $strName = strval($arrContent[2]);
                $intIsArray = 0;
                //如果包含[和]则表示是一个数组
                if(false!==strpos($strType, '[') && false !== strpos($strType, ']')) {
                    $strType = preg_replace('/\[|\]/', '', $strType);
                    $intIsArray = 1;
                }
                if(false!==strpos($strName, '[') && false !== strpos($strName, ']')) {
                    $strName = preg_replace('/\[|\]/', '', $strName);
                    $intIsArray = 1;
                }
                $arrVar = array(
                    'array' => $intIsArray,
                    'type' => $strType,
                    'name' => $strName,
                    'brief' => strval($arrContent[3]),
                    'optional' => 0,
                );
            }

            if(!empty($arrVar)) {
                $arrRet['struct'][$strStructName][''][] = $arrVar;
                $bolRet = true;
            }
        }
    }
    return $bolRet;
}

//解析一个function信息
private static function parseFunction($strLine, &$mixFileHandle, &$arrRet) {
    $bolRet = false;
    $strBrief = '';
    $strAuthorMail = '';
    $strAuthorHi = '';
    $arrInNote = array();
    $arrOutNote = array();
    $strFunName = '';
    $strFunType = '';
    if( preg_match(self::$regServiceBrief, $strLine, $arrContent) ) {
        $strBrief = strval($arrContent[1]);

        while(!feof($mixFileHandle)) {
            $strLine = trim(fgets($mixFileHandle));

            //处理输入参数
            if ( preg_match(self::$regServiceIn, $strLine, $arrContent) ) {
                self::trimArr($arrContent, 1);
                $strKey = $arrContent[1].'_'.$arrContent[2];
                $arrInNote[$strKey][] = array(
                    'name' => strval($arrContent[1]),
                    'type' => strval($arrContent[2]),
                    'brief' => strval($arrContent[3]),
                );
            }
            //处理输出参数
            else if ( preg_match(self::$regServiceOut, $strLine, $arrContent) ) {
                self::trimArr($arrContent, 1);
                $strKey = $arrContent[1].'_'.$arrContent[2];
                $arrOutNote[$strKey][] = array(
                    'name' => strval($arrContent[1]),
                    'type' => strval($arrContent[2]),
                    'brief' => strval($arrContent[3]),
                );
            }
            //处理author
            else if( preg_match(self::$regFunctionAuthor, $strLine, $arrContent) ) {
                $strAuthorMail = trim(strval($arrContent[1]));
                $strAuthorHi = trim(strval($arrContent[2]));
            }
            //处理提交函数
            else if ( preg_match(self::$regServiceCFun, $strLine, $arrContent) ) {
                $strFunName = strval($arrContent[1]);
                $strFunDefine = strval($arrContent[2]);
                $strFunType = '提交';
                break;
            }
            //处理浏览函数
            else if ( preg_match(self::$regServiceVFun, $strLine, $arrContent) ) {
                $strFunName = strval($arrContent[1]);
                $strFunDefine = strval($arrContent[2]);
                $strFunType = '浏览';
                break;
            }
        }
        if(strlen($strFunName) === 0) {
            return false;
        }
        $arrParam = self::buildFunParam($strFunDefine, $arrInNote, $arrOutNote);

        $arrRet['function'][$strFunName]['brief'] = $strBrief;
        $arrRet['function'][$strFunName]['type'] = $strFunType;
        $arrRet['function'][$strFunName]['author_mail'] = $strAuthorMail;
        $arrRet['function'][$strFunName]['author_hi'] = $strAuthorHi;
        $arrRet['function'][$strFunName]['input'] = $arrParam['in'];
        $arrRet['function'][$strFunName]['output'] = $arrParam['out'];
        /*foreach (Service_Document_Document::$functionSections as $section_key => $default_val){
        	if(!isset($arrRet['function'][$strFunName][$section_key])){
        		$arrRet['function'][$strFunName][$section_key] = $default_val;
        	}
        }*/
    }

    return $bolRet;
}

//解析service全局说明
private static function parseServiceNotice($strFileName, &$arrRet) {
    if(!is_file($strFileName)) {
        return false;
    }
    
    if(preg_match("/\/\/CONF_START\n(.*)\n\/\/CONF_END/Ums", file_get_contents($strFileName), $arrContent) ) {
        $arrRet['service_notice'][''][''] = strval($arrContent[1]);
        return true;
    }
    
    return false;
}

public static function parseServiceTree($basedir=null) {
    $arrRet = array();
    $moduleList = array('forum','perm','photo','post','user');
    if(!isset($basedir)){
    	$path = dirname(__FILE__) . '/../../../';
    }else{
    	$path = $basedir;
    }
    $cmd = 'ls '. $path;
    exec($cmd, $module);
    for($i=0 ; $i<count($module) ; ++$i) {
        if(!in_array($module[$i], $moduleList)) {
            array_push($moduleList, $module[$i]);
        }
    }

    for($i=0 ; $i<count($moduleList); ++$i) {
        $tmp = $path . $moduleList[$i] . '/service';
        $file = array();
        $cmd = 'find ' . $tmp . ' -name "*.idl"';
        exec($cmd,$file);
        if(count($file)==0) {
            continue;
        }

        for($j=0; $j<count($file); ++$j) {
            $afile = fopen($file[$j], 'r+');
            $fileName = array_filter(explode('/', $file[$j]));
            $len = sizeof($fileName);
            $dirName = $fileName[$len-1];
            $fileName = $fileName[$len];
            $fileName = array_filter(explode('.', $fileName));
            $fileName = $fileName[0];
            if($fileName!=$moduleList[$i] and $fileName!=$dirName) {
                continue;
            }

            while(!feof($afile)) {
                $str = trim(fgets($afile));
                $content = array();
                if(preg_match(self::$regServiceFun, $str, $content)) {
                    if($fileName==$moduleList[$i]) {
                        $arrRet['/tieba-odp/'. $moduleList[$i]. '/'. $content[1]] = 1;
                    } else {
                        $arrRet['/tieba-odp/'. $moduleList[$i]. '/'. $fileName. '/'. $content[1]] = 1;
                    }
                }
            }
            fclose($afile);
        }
    }
    return $arrRet;
}

public static function getAllService($basedir=null){
	$arrRet = array();
	$moduleList = array('forum','perm','photo','post','user');
	if(!isset($basedir)){
		$path = dirname(__FILE__) . '/../../../';
	}else{
		$path = $basedir;
	}
	$cmd = 'ls '. $path;
	exec($cmd, $module);
	for($i=0 ; $i<count($module) ; ++$i) {
		if(!in_array($module[$i], $moduleList)) {
			array_push($moduleList, $module[$i]);
		}
	}
	
	for($i=0 ; $i<count($moduleList); ++$i) {
		$tmp = $path . $moduleList[$i] . '/service';
		$file = array();
		$cmd = 'find ' . $tmp . ' -name "*.idl"';
		exec($cmd,$file);
		if(count($file)==0) {
			continue;
		}
	
		for($j=0; $j<count($file); ++$j) {
			$afile = fopen($file[$j], 'r+');
			$fileName = array_filter(explode('/', $file[$j]));
			$len = sizeof($fileName);
			$dirName = $fileName[$len-1];
			$fileName = $fileName[$len];
			$fileName = array_filter(explode('.', $fileName));
			$fileName = $fileName[0];
			if($fileName!=$moduleList[$i] and $fileName!=$dirName) {
				continue;
			}
	
			while(!feof($afile)) {
				$str = trim(fgets($afile));
				$content = array();
				if(preg_match(self::$regServiceFun, $str, $content)) {
					if($fileName==$moduleList[$i]) {
						//$arrRet['/tieba-odp/'. $moduleList[$i]. '/'. $content[1]] = 1;
						if(!isset($arrRet[$moduleList[$i]])){
							$arrRet[$moduleList[$i]] = array();
						}
					} else {
						//$arrRet['/tieba-odp/'. $moduleList[$i]. '/'. $fileName. '/'. $content[1]] = 1;
						if(!isset($arrRet[$moduleList[$i]][$fileName])){
							$arrRet[$moduleList[$i]][$fileName] = array();
						}
					}
				}
			}
			fclose($afile);
		}
	}
	return $arrRet;
}

/*
 * 解析idl文件
 * $strServiceName：service名
 * $strSubServiceName：子service名
 * $arrNeedParse：需要解析的项目，包括brief、author、struct、function，0不解析，1解析
 */
public static function parseIdl($strServiceName, $strSubServiceName, $arrNeedParse,$basedir=null) {
    self::$_service = $strServiceName;
    self::$_subService = $strSubServiceName;
    $arrFile = array();
    $arrRet = array();

    if(!isset($basedir)){
    	$strPath = dirname(__FILE__). '/../../../'. $strServiceName. '/service';
    }else{
    	$strPath = $basedir. $strServiceName. '/service';
    }
    //如果不是子service
    if( $strSubServiceName === '' ){
        $strCmd = 'find '. $strPath. ' -name '. '"'. $strServiceName. '.idl"';
    } else{
        $strCmd = 'find '. $strPath. ' -name '. '"'. $strSubServiceName. '.idl"';
    }
    exec($strCmd, $arrFile);
    if(false === ($mixFileHandle = fopen($arrFile[0], 'r+'))) {
        Bingo_Log::warning('cannot open '.$arrFile[0]);
        return false;
    }

    $arrRet['service_brief'][''][''] = '';
    $arrRet['service_notice'][''][''] = '';
    $arrRet['service_author']['']['mail'] = '';
    $arrRet['service_author']['']['hi'] = '';
    while(!feof($mixFileHandle)) {
        $strLine = trim(fgets($mixFileHandle));
        //对每一行进行正则表达式判断
        if($arrNeedParse['struct'] === 1) {
            //解析当前struct
            $ret = self::parseStruct($strLine, $mixFileHandle, $arrRet);
            if($ret) {
                continue;
            }
        }
        if($arrNeedParse['function'] === 1) {
            //解析当前functio
            $ret = self::parseFunction($strLine, $mixFileHandle, $arrRet);
            if($ret) {
                continue;
            }
        }
        if($arrNeedParse['brief'] === 1) {
            $ret = self::parseBrief($strLine, $arrRet);
            if($ret) {
                continue;
            }
        }
        if($arrNeedParse['author'] === 1) {
            $ret = self::parseAuthor($strLine, $arrRet);
            if($ret) {
                continue;
            }
        }
    }
    fclose($mixFileHandle);
    
    //处理struct嵌套
    if(!empty($arrRet['struct'])) {
        foreach($arrRet['struct'] as $strStructName => $arrStruct) {
            foreach($arrStruct[''] as $key => $item) {
                $intIsStruct = (array_key_exists($item['type'], self::$arrAllStruct)) ? 1 : 0;
                $arrRet['struct'][$strStructName][''][$key]['struct'] = $intIsStruct;
            }
        }
    }

    //解析service说明
    if($arrNeedParse['conf'] === 1) {
        $ret = self::parseServiceNotice($arrFile[0], $arrRet);
    }

    return $arrRet;
}
}
