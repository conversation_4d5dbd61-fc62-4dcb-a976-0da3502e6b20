<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-09-25 16:01:52
 * @comment pc����̨
 * @version
 */
class baifubaoAction extends Util_Base {
    
    public static $service_ie = "gbk";

    public function execute(){
        try {
            Bingo_Log::warning(var_export($_REQUEST,true));
            Bingo_Log::pushNotice("REQUEST", json_encode($_REQUEST));
	        $arrInput = array(
			    'req' => 
			        array( 
				        'info' => $_REQUEST,
				        'gateway' => 1,
				)
			);
            $arrOutput = Tieba_Service::call('pay', 'GatewayCallback', $arrInput, NULL, NULL, 'post', 'php', self::$service_ie, 'local');
            if( !isset($arrOutput['errno']) || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                throw new Util_Exception("GatewayCallback errno!",$arrOutput['errno']);
            }
            
            //��־��ӡ
            Bingo_Log::pushNotice("res", $arrOutput['errno']);
            echo $arrOutput['res'];
            // Ĭ�ϳɹ�����ֵ
            //$this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,Tieba_Error::getUserMsg(Tieba_Errcode::ERR_SUCCESS),$outData);
        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            //���ݽӿ�һ������ṩ��������Ϣ���ܶ��Ⱪ¶��Ĭ����'δ֪����'��֮�����������޸�
            $this->_jsonRet($e->getCode(), 'δ֪����');	
        }
    }
}
?>
