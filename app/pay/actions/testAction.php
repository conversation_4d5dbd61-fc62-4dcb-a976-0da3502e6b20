<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-09-25 13:52:19
 * @comment ����ACTION
 * @version
 */
class testAction extends Util_Base {

    public function execute(){
    	try {
            //������ȡ
            $uid = intval(Bingo_Http_Request::get('uid',0));




            // ģ�����
            //ģ��������,��ʼ��ΪĬ��ֵ�������޸�
            Bingo_Page::assign('uid',0);
            Bingo_Page::assign('infoArr',array());

            Bingo_Page::setTpl("index");
        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            Bingo_Page::setTpl("index");
            //Ҳ������ת302
            //Tieba_Error::fetalError302();
            
        }
    }

}
?>