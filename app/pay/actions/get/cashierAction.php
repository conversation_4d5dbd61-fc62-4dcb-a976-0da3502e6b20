<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-09-25 16:01:52
 * @comment pc����̨
 * @version
 */
class cashierAction extends Util_Base {
    
    public static $service_ie = "gbk";

    public function execute(){
        try {
            //������ȡ 
            $id = strval(Bingo_Http_Request::get('id',''));
            if (empty($id)){
                throw new Util_Exception("id is empty!",Tieba_Errcode::ERR_PARAM_ERROR);
            }
            //id��Ҫ����
            $consume_order_id  = intval(Bd_Crypt_Ucrypt::id_decode($id));
            if ($consume_order_id <= 0){
                throw new Util_Exception("id decode errno!",Tieba_Errcode::ERR_PARAM_ERROR);
            }
            
            Bingo_Log::pushNotice("consume_order_id", $consume_order_id);
 
            //��֤��¼��ȡuid
            if ($this->_arrUserInfo['user_id'] === 0){
                //ֱ����ת��½ҳ
                Bingo_Log::warning("user need login!!");
                $jump_url ='http://'.$_SERVER['HOSTNAME'].':'.$_SERVER["SERVER_PORT"].$_SERVER["REQUEST_URI"];
                header('Location: https://passport.baidu.com/v2/?login&u='.$jump_url);
                exit;
            } 
            $user_id = $this->_arrUserInfo['user_id'];

            $arrInput = array( 'consume_order_id' => $consume_order_id );
            $arrOutput = Tieba_Service::call('pay', 'getConsumeOrderInfo', $arrInput, NULL, NULL, 'post', 'php', self::$service_ie, 'local');
            if( !isset($arrOutput['errno']) || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                throw new Util_Exception("getConsumeOrderInfo errno!",$arrOutput['errno']);
            }
            
            $outData = array();
            if (!empty($arrOutput['res'])){
                //�ж϶���uid�Ƿ���ڵ�½uid
                if (strval($arrOutput['res']['user_id']) !== strval($this->_arrUserInfo['user_id'])){
                    Bingo_Log::warning($this->_arrUserInfo['user_id']." ".$arrOutput['res']['user_id']);
                    throw new Util_Exception(Tieba_Error::getUserMsg(Tieba_Errcode::ERR_POST_CT_POWER_NOT_ENOUGH),Tieba_Errcode::ERR_POST_CT_POWER_NOT_ENOUGH);
                }
                //�����ɹ�ֱ�����ɹ�ҳ
                if (intval($outData['res']['status']) === 1) {
                    Bingo_Log::warning("order success,donot pay again!!");
                    header('Location: ' . trim($outData['res']['go_url']));
                    exit;
                }
                
                $outData['id'] = $id;
                $outData['tbs'] = Tieba_Tbs::gene(true);
                $outData['user_name'] = $this->_arrUserInfo['user_name'];
                $outData['service_name'] = $arrOutput['res']['service_name'];
                $outData['service_desc'] = $arrOutput['res']['service_desc'];
                //���ݿ�ĵ�λ�Ƿ֣���Ҫת��Ԫ
                $outData['consume_amount'] = round($arrOutput['res']['consume_amount']/100,2);
            }
            
            //��ģ�����
            Bingo_Page::assign('orderinfo',$outData);
            Bingo_Page::setTpl("index.php");
        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            
            Bingo_Page::assign('errno',$e->getCode());
            Bingo_Page::assign('msg',$e->getMessage());
            Bingo_Page::setTpl("index.php");
            //302������ҳ
            //Tieba_Error::fetalError302();
        }
    }
}
?>
