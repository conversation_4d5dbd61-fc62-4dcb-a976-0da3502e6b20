<?php
/**
 * ui action�Ļ���
 **/
abstract class Util_Base extends Bingo_Action_Abstract {

    // �����
    protected $_intErrno = 0;

    // ������Ϣ
    protected $_strError = 'success';
    protected $_arrUserInfo = array();

    public function init(){
    
    	//��ȡ·����������ui��־��
        $strRouter = Bingo_Http_Request::getStrHttpRouter();
    	Bingo_Log::pushNotice("urlkey", $strRouter);
    	
    	//��ȡ��ǰ�û���Ϣ������ui��־��
    	$this->_getUserInfo();
    	foreach($this->_arrUserInfo as $strKey => $mixVal) {
    		Bingo_Log::pushNotice($strKey, $mixVal);
    	}

		//Ĭ�϶�POST������tbsУ�飬�粻��Ҫ����ȥ����ģ��
        if(Bingo_Http_Request::isPost() && strstr($strRouter,"callback") ===false){
        	$strTbs = strval(Bingo_Http_Request::get('tbs',''));
            if ( false === $this->_tbsCheck($strTbs) ) {
            	throw new Util_Exception("tbs check error!",Tieba_Errcode::ERR_INVALID_SIGN);
            }
        }
        return true;
    }

    protected function _tbsCheck($strTbs) {        
    	if(!Tieba_Tbs::check($strTbs, true)){
    	 	return false;
		}
		return true;
    }
    
    protected function _getUserInfo() {
        if (!empty($this->_arrUserInfo)) {
            return $this->_arrUserInfo;
        }

        $bolLogin   = (boolean)Tieba_Session_Socket::isLogin();
        $intUserId  = intval(Tieba_Session_Socket::getLoginUid());
        $strUserName    = strval(Tieba_Session_Socket::getLoginUname());
        $intUserIp      = intval(Bingo_Http_Ip::ip2long(Bingo_Http_Ip::getConnectIp()));
        $bolNoUname     = (boolean)Tieba_Session_Socket::getNo_un();
        $strMobile      = strval(Tieba_Session_Socket::getMobilephone());
        $strEmail       = strval(Tieba_Session_Socket::getEmail());

        $arrUserSInfo   = array(
            'is_login'  => $bolLogin,
            'user_id'   => $intUserId,
            'user_name' => $strUserName,
            'user_ip'    => $intUserIp,
            'is_noname' => $bolNoUname,
            'mobile' => Tieba_Util::maskPhone($strMobile),
            'email'  => Tieba_Util::maskEmail($strEmail),
        );
        $this->_arrUserInfo = $arrUserSInfo;
        return $this->_arrUserInfo;
    }


    protected function _jsonRet($errno,$errmsg,array $arrExtData=array()){
        $arrRet = array(
            'no'=>intval($errno),
            'error'=>strval($errmsg),
            'data'=>$arrExtData,
        );
        foreach($arrRet as $k=>$v){
            Bingo_Page::assign($k,$v);
        }
        Bingo_Page::setOnlyDataType("json");
        Bingo_Http_Response::contextType('application/json');
    }

    protected function _serialRet($errno,$errmsg,array $arrExtData=array()){
        $arrRet = array(
            'no'=>intval($errno),
            'error'=>strval($errmsg),
            'data'=>$arrExtData,
        );
        foreach($arrRet as $k=>$v){
            Bingo_Page::assign($k,$v);
        }
        Bingo_Page::setOnlyDataType("serial");
        Bingo_Http_Response::contextType('application/json');
    }

}