<?php

class Dl_Uegnaudit_Alarm  extends Dl_Uegnaudit_Base
{
    const MODULE_NAME = 'uegnaudit';
    const SERVICE_NAME = 'Dl_Uegnaudit_Alarm';
    const DB_CHARSET = 'utf8';

    const DATABASE_NAME = 'forum_uegnaudit';
    const TABLE_NAME_ALARM = "alarm";

    private static $_db = null;
    private static $_conf = null;

    /**
     * @param $errno
     * @return array
     */
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @return Bd_DB|null
     */
    private static function _getDB()
    {
        if (self::$_db) {
            return self::$_db;
        }
        self::$_db = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if (self::$_db == null || !self::$_db->isConnected()) {
            Bingo_Log::warning('db connect fail.');
            return null;
        }
        self::$_db->query("set names " . self::DB_CHARSET);
        return self::$_db;
    }

    /**
     * @return array|bool
     */
    private static function _init()
    {
        if (self::_getDB() == null) {
            Bingo_Log::warning("init db fail.");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        if (self::$_conf == null) {
            $dlConfFile = '/app/' . self::MODULE_NAME . '/' . strtolower(self::SERVICE_NAME);
            self::$_conf = Bd_Conf::getConf($dlConfFile);
            if (self::$_conf == false) {
                Bingo_Log::warning('init get conf fail.' . $dlConfFile);
                return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
            }
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return array|bool
     */
    public static function execSql($arrInput)
    {
        if (!isset($arrInput['function'])) {
            Bingo_Log::warning('input params invalid: function is empty. [' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $ret = self::_init();
        if ($ret !== true) {
            return $ret;
        }
        Bingo_Timer::start('initlib');
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end('initlib');
        if ($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $arrOut = $mdb->execSql($arrInput);
        return $arrOut;
    }

    /**
     * @param array
     * @return array
     */
    public static function addAlarm($arrInput) {
        if (empty($arrInput['project_id']) || empty($arrInput['punishment_title']) ||
            empty($arrInput['interval']) || empty($arrInput['threshold']) ||
            (empty($arrInput['phone']) && empty($arrInput['mail'])) || empty($arrInput['update_uname']) ) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }

        $intProjectId = intval($arrInput['project_id']);
        $strPunishmentTitle = strval($arrInput['punishment_title']);
        $intInterval = intval($arrInput['interval']);
        $intThreshold = intval($arrInput['threshold']);
        $arrPhone = $arrInput['phone'];
        $strPhone = implode(',', $arrPhone);
        $arrMail = $arrInput['mail'];
        $strMail = implode(',', $arrMail);
        $strUpdateUname = strval($arrInput['update_uname']);
        $intUpdateTime = time();

        $ret = self::_init();
        if ($ret !== true) {
            return $ret;
        }
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        if ($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $arrParams = array(
            'function' => 'addAlarm',
            'table_name' => self::TABLE_NAME_ALARM,
            'project_id' => $intProjectId,
            'punishment_title' => $strPunishmentTitle,
            'interval' => $intInterval,
            'threshold' => $intThreshold,
            'phone' => $strPhone,
            'mail' => $strMail,
            'update_uname' => $strUpdateUname,
            'update_time' => $intUpdateTime,
        );
        $arrOutput = $mdb->execSql($arrParams);
        Bingo_Log::warning(__FUNCTION__.$mdb->getLastSql());
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call execSql fail. input:[" . serialize($arrParams) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param array
     * @return array
     */
    public static function updateAlarm($arrInput)
    {
        if (empty($arrInput['project_id']) || empty($arrInput['punishment_title']) ||
            empty($arrInput['check_time']) || !is_numeric($arrInput['last_count'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }

        $intProjectId = intval($arrInput['project_id']);
        $strPunishmentTitle = strval($arrInput['punishment_title']);
        $intCheckTime = intval($arrInput['check_time']);
        $intLastCount = intval($arrInput['last_count']);

        $ret = self::_init();
        if ($ret !== true) {
            return $ret;
        }
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        if ($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $arrParams = array(
            'function' => 'updateAlarm',
            'table_name' => self::TABLE_NAME_ALARM,
            'project_id' => $intProjectId,
            'punishment_title' => $strPunishmentTitle,
            'check_time' => $intCheckTime,
            'last_count' => $intLastCount,
        );
        $arrOutput = $mdb->execSql($arrParams);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call execSql fail. input:[" . serialize($arrParams) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param array
     * @return array
     */
    public static function delAlarm($arrInput) {
        if (empty($arrInput['project_id'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }

        $ret = self::_init();
        if ($ret !== true) {
            return $ret;
        }
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        if ($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $intProjectId = intval($arrInput['project_id']);
        $arrParams = array(
            'function' => 'delAlarm',
            'table_name' => self::TABLE_NAME_ALARM,
            'project_id' => $intProjectId,
        );
        $arrOutput = $mdb->execSql($arrParams);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call execSql fail. input:[" . serialize($arrParams) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param array
     * @return array
     */
    public static function getAlarmInfo($arrInput)
    {
        if (empty($arrInput['project_id']) || empty($arrInput['punishment_title'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }

        $intProjectId = intval($arrInput['project_id']);
        $strPunishmentTitle = strval($arrInput['punishment_title']);

        $ret = self::_init();
        if ($ret !== true) {
            return $ret;
        }
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        if ($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $arrParams = array(
            'function' => 'getAlarmInfo',
            'table_name' => self::TABLE_NAME_ALARM,
            'project_id' => $intProjectId,
            'punishment_title' => $strPunishmentTitle,
        );
        $arrOutput = $mdb->execSql($arrParams);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call execSql fail. input:[" . serialize($arrParams) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrAlarmInfo = $arrOutput['results'][0][0];
        if (isset($arrAlarmInfo['phone'])) {
            $arrAlarmInfo['phone'] = explode(',', $arrAlarmInfo['phone']);
        }

        if (isset($arrAlarmInfo['email'])) {
            $arrAlarmInfo['email'] = explode(',', $arrAlarmInfo['email']);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrAlarmInfo);
    }

    /**
     * @param array
     * @return array
     */
    public static function getAlarmList($arrInput)
    {
        $strCond = '';
        if (isset($arrInput['check_time'])) {
            $intCheckTime = intval($arrInput['check_time']);
            $strCond = $strCond . " check_time <". $intCheckTime;
        }

        if (isset($arrInput['project_id']) && !empty($arrInput['project_id'])) {
            $arrProjectId = $arrInput['project_id'];
            foreach ($arrProjectId as &$item) {
                $item = intval($item);
            }
            $strCond = self::_formatCond($strCond);
            $strCond = $strCond . " project_id in (". implode(',', $arrProjectId) . ")";
        }

        if(isset($arrInput['partitionId']) && isset($arrInput['partitionNum'])){
            $strCond = self::_formatCond($strCond);
            $strCond = $strCond . " id % ".$arrInput['partitionNum']." = ".$arrInput['partitionId'];
        }

        if (empty($strCond)) {
            $strCond = 1;
        }

        $offset = (! empty($arrInput["offset"])) ?  intval($arrInput["offset"]) : 0;
        $limit =  (! empty($arrInput["limit"])) ? intval($arrInput["limit"]) : (Libs_Define_Uegnaudit::DEFAULT_PAGE_LIMIT);

        $ret = self::_init();
        if ($ret !== true) {
            return $ret;
        }
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        if ($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $arrParams = array(
            'function' => 'getAlarmList',
            'table_name' => self::TABLE_NAME_ALARM,
            'cond'       => $strCond,
            'offset'     => $offset,
            'limit'      => $limit,
        );
        $arrOutput = $mdb->execSql($arrParams);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call execSql fail. input:[" . serialize($arrParams) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrAlarmList = array();
        foreach ($arrOutput['results'][0] as $arrAlarmInfo) {
            if (isset($arrAlarmInfo['phone'])) {
                $arrAlarmInfo['phone'] = explode(',', $arrAlarmInfo['phone']);
            }

            if (isset($arrAlarmInfo['email'])) {
                $arrAlarmInfo['email'] = explode(',', $arrAlarmInfo['email']);
            }

            $arrAlarmList[] = $arrAlarmInfo;
        }

        $arrData = array(
            "list" => $arrAlarmList,
            "count" => $arrOutput['results'][1][0]['count'],
        );
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
    }

    /**
     * @param $strCond
     * @param string $strCompare
     * @return string
     */
    private static function _formatCond($strCond, $strCompare = '') {
        if ($strCond === $strCompare) {
            return $strCond;
        }
        return $strCond .' and ' ;
    }
}