<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


define("BINGO_ENCODE_LANG", "UTF-8");
define("MODULE","Uegnaudit_service");
class Service_Project_Project extends Service_Base
{


    /**
     * @param $arrInput
     * @return array
     */
    public static function getProjectInfo($arrInput) {
        if (empty($arrInput['id'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }

        $intProjectId = intval($arrInput['id']);

        $arrDlInput = array(
            'id' => $intProjectId,
        );

        $arrOutput = Dl_Uegnaudit_Project::getProjectInfo($arrDlInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::getProjectInfo fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrRetData = (array)$arrOutput['data'];

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function getProjectGroupByPidAndUid($arrInput) {
        if (empty($arrInput['project_id']) || empty($arrInput['user_id'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }

        $intProjectId = intval($arrInput['project_id']);
        $intUserId = intval($arrInput['user_id']);

        $arrDlInput = array(
            'project_id' => $intProjectId,
            'user_id'    => $intUserId,
        );

        $arrOutput = Dl_Uegnaudit_Project::getProjectGroupByPidAndUid($arrDlInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::getProjectGroupByPidAndUid fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrRetData = (array)$arrOutput['data'];

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function addProject($arrInput) {
        if (empty($arrInput['name']) || !isset($arrInput['type']) || empty($arrInput['priority'])
            || empty($arrInput['from_type']) || empty($arrInput['audit_type'])
            || empty($arrInput['create_uname'])
        ) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }

        $strName        = strval($arrInput['name']);
        $intType        = intval($arrInput['type']);
        $intPriority    = intval($arrInput['priority']);
        $intFromType    = intval($arrInput['from_type']);
        $intAuditType   = intval($arrInput['audit_type']);
        $strCreateUName = strval($arrInput['create_uname']);
        $strKeyWord     = !empty($arrInput['keyword']) ? strval($arrInput['keyword']) : "";
        $strWord        = !empty($arrInput['word']) ? strval($arrInput['word']) : "";
        $intCreateTime  = time();

        $strDataFrom       = !empty($arrInput['data_from']) ? strval($arrInput['data_from']) : "";
        $intItemBundleMax  = !empty($arrInput['item_bundle_max']) ? intval($arrInput['item_bundle_max']) : Libs_Define_Project::PROJECT_ITEM_BUNDLE_MAX_DEFAULT;
        $intItemCostMax    = !empty($arrInput['item_cost_max']) ? intval($arrInput['item_cost_max']) : Libs_Define_Project::PROJECT_ITEM_COST_MAX;
        $intItemCostMin    = isset($arrInput['item_cost_min']) ? intval($arrInput['item_cost_min']) : Libs_Define_Project::PROJECT_ITEM_COST_MAX;
        $intTaskItemMax    = isset($arrInput['task_item_max']) ? intval($arrInput['task_item_max']) : Libs_Define_Project::PROJECT_ITEM_COST_MAX;
        $intItemUnauditMax = !empty($arrInput['item_unaudit_max']) ? intval($arrInput['item_unaudit_max']) : Libs_Define_Project::PROJECT_ITEM_UNAUDIT_MAX;

        $arrProjectGroup     = (!empty($arrInput['project_group']) && is_array($arrInput['project_group'])) ? $arrInput['project_group'] : array();
        $arrAuditTrain       = (!empty($arrInput['audit_train']) && is_array($arrInput['audit_train'])) ? $arrInput['audit_train'] : array();
        $arrDisplayField     = (!empty($arrInput['display_field']) && is_array($arrInput['display_field'])) ? $arrInput['display_field'] : array();
        $arrDisplayContField = (!empty($arrInput['display_cont_field']) && is_array($arrInput['display_cont_field'])) ? $arrInput['display_cont_field'] : array();
        //$arrInterfaceId = (!empty($arrInput['interface_id']) && is_array($arrInput['interface_id'])) ? $arrInput['interface_id'] : array();
        $arrPrimaryKey = (!empty($arrInput['primary_key']) && is_array($arrInput['primary_key'])) ? $arrInput['primary_key'] : array();

        $intFilterDeleted = !empty($arrInput['filter_deleted']) ? intval($arrInput['filter_deleted']) : Libs_Define_Project::PROJECT_DO_FILTER_DELETED_OFF;
        $intMultiTag      = !empty($arrInput['multi_tag']) ? intval($arrInput['multi_tag']) : Libs_Define_Project::PROJECT_MULTI_TAG_OFF;
        $intStatus        = !empty($arrInput['status']) ? intval($arrInput['status']) : Libs_Define_Project::PROJECT_STATUS_OFF;
        $strRemark        = strval($arrInput['remark']);
        $intBefAudit      = !empty($arrInput['bef_audit']) ? intval($arrInput['bef_audit']) : Libs_Define_Project::PROJECT_STATUS_OFF;
        $intIsMaster      = !empty($arrInput['is_master']) ? intval($arrInput['is_master']) : 0;
        $intIsMask        = !empty($arrInput['is_mask']) ? intval($arrInput['is_mask']) : 0;
        $intBId           = !empty($arrInput['b_id']) ? intval($arrInput['b_id']) : 0;
        $intCityId        = !empty($arrInput['city_id']) ? intval($arrInput['city_id']) : 0;
        $intWordsStatus   = !empty($arrInput['words_status']) ? intval($arrInput['words_status']) : 0;
        $intProjectEffect = !empty($arrInput['project_effect']) ? intval($arrInput['project_effect']) : 0;
        $intIsHint        = !empty($arrInput['is_hint']) ? intval($arrInput['is_hint']) : 0;
        $strHint          = !empty($arrInput['hint']) ? strval($arrInput['hint']) : '';

        $arrGroupIds = array_filter(array_unique($arrProjectGroup));

        $arrDlInput = array(
            'name'               => $strName,
            'type'               => $intType,
            'priority'           => $intPriority,
            'from_type'          => $intFromType,
            'audit_type'         => $intAuditType,
            'item_bundle_max'    => $intItemBundleMax,
            'item_cost_max'      => $intItemCostMax,
            'item_cost_min'      => $intItemCostMin,
            'task_item_max'      => $intTaskItemMax,
            'item_unaudit_max'   => $intItemUnauditMax,
            'filter_deleted'     => $intFilterDeleted,
            'multi_tag'          => $intMultiTag,
            'status'             => $intStatus,
            'remark'             => $strRemark,
            'create_uname'       => $strCreateUName,
            'create_time'        => $intCreateTime,
            'data_from'          => $strDataFrom,
            'project_group'      => $arrGroupIds,
            'display_field'      => $arrDisplayField,
            'display_cont_field' => $arrDisplayContField,
            //'interface_id' => $arrInterfaceId,
            'primary_key'        => $arrPrimaryKey,
            'bef_audit'          => $intBefAudit,
            'keyword'            => $strKeyWord,
            'audit_train'        => $arrAuditTrain,
            'word'               => $strWord,
            'is_master'          => $intIsMaster,
            'is_mask'            => $intIsMask,
            'b_id'               => $intBId,
            'city_id'            => $intCityId,
            'words_status'       => $intWordsStatus,
            'project_effect'     => $intProjectEffect,
            'is_hint'            => $intIsHint,
            'hint'               => $strHint,
        );

        $arrOutput = Dl_Uegnaudit_Project::addProject($arrDlInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::addProject fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrRetData = $arrOutput['data'];
        //发送邮件
        if($arrRetData){
            $strdata = '';
            if($intFromType == 1){
                $strdata .= '关联策略号';
            }else if($intFromType == 2){
                $strdata .= 'api接口';
            }else{
                $strdata .= '平台流转';
            }
            $strdata .= "【".$strDataFrom."】";
            $arrParams = array(
                'title'     => '审核平台新增项目成功',
                'content'   => '<table border="1"><tr><th colspan="2">基本信息</th></tr><tr><td>项目ID</td><td>'.intval($arrRetData).'</td></tr><tr><td>项目名称</td><td>'.$strName.'</td></tr><tr><td>创建人</td><td>'.$strCreateUName.'</td></tr><tr><td>创建时间</td><td>'.date('Y-m-d  H:i:s',$intCreateTime).'</td></tr><tr><td>数据来源</td><td>'.$strdata.'</td></tr></table>',
            );
            $arrOutput = Service_Dealer_Dealer::SendEmailMessage($arrParams);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Dealer_Dealer::SendEmailMessage fail. input:[" . serialize($arrParams) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function editProject($arrInput)
    {
        if (empty($arrInput['id'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        $intProjectId = $arrInput['id'];

        // getProjectInfo
        $arrDlInput = array(
            'id' => $intProjectId,
        );
        $arrOutput = self::getProjectInfo($arrDlInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call self::getProjectInfo fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrProjectInfo = $arrOutput['data'];
        if (empty($arrProjectInfo)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " project not exist. project_id is:[" . $intProjectId .  "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrDBDisplayFieldTemp = $arrProjectInfo['display_field'];
        $arrDBDisplayField = array();
        $arrDBDisplayContField = array();
        $arrDBInterfaceField = array();
        foreach ($arrDBDisplayFieldTemp as $item) {
            $strField = $item['field'];
            $isShow   = intval($item['display']);
            $isCont   = intval($item['display_cont']);
            //$interfaceId   = intval($item['interface_id']);
            $arrDBDisplayField[$strField] = $isShow;
            $arrDBDisplayContField[$strField] = $isCont;
            //$arrDBInterfaceField[$strField] = $interfaceId;
        }
        $intDBFromType = $arrProjectInfo['from_type'];
        $arrDBStrategyNum = $arrProjectInfo['strategy_num'];
        $arrDBApiNum = $arrProjectInfo['api_num'];
        $arrDBInflow = $arrProjectInfo['project_inflow'];

        $arrStrategyNum = array();
        $arrApiNum = array();
        $arrInflow = array();
        $strDataFrom = !empty($arrInput['data_from']) ? ($arrInput['data_from']) : "";
        if ($intDBFromType == Libs_Define_Project::PROJECT_FROM_TYPE_STRATEGY_NUM) {
            $arrStrategyNum = !empty($strDataFrom) ? explode("," , $strDataFrom) : array();;
        } else if ($intDBFromType == Libs_Define_Project::PROJECT_FROM_TYPE_API_NUM) {
            $arrApiNum = !empty($strDataFrom) ? explode("," , $strDataFrom) : array();;
        } else if ($intDBFromType == Libs_Define_Project::PROJECT_FROM_TYPE_PROJECT_INFLOW) {
            $arrDataFrom = Util_Project_Project::decodeInflowDataFrom($strDataFrom);
            $arrInflow = !empty($arrDataFrom) ? $arrDataFrom : array();
        }

        $strName             = isset($arrInput['name']) ? strval($arrInput['name']) : $arrProjectInfo['name'];
        $intPriority         = isset($arrInput['priority']) ? intval($arrInput['priority']) : $arrProjectInfo['priority'];
        $intItemBundleMax    = !empty($arrInput['item_bundle_max']) ? intval($arrInput['item_bundle_max']) : $arrProjectInfo['item_bundle_max'];
        $intItemCostMax      = !empty($arrInput['item_cost_max']) ? intval($arrInput['item_cost_max']) : $arrProjectInfo['item_cost_max'];
        $intItemCostMin      = isset($arrInput['item_cost_min']) ? intval($arrInput['item_cost_min']) : $arrProjectInfo['item_cost_min'];
        $intTaskItemMax      = isset($arrInput['task_item_max']) ? intval($arrInput['task_item_max']) : $arrProjectInfo['task_item_max'];
        $intItemUnauditMax   = !empty($arrInput['item_unaudit_max']) ? intval($arrInput['item_unaudit_max']) : $arrProjectInfo['item_unaudit_max'];
        $intFilterDeleted    = isset($arrInput['filter_deleted']) ? intval($arrInput['filter_deleted']) : $arrProjectInfo['filter_deleted'];
        $intMultiTag         = isset($arrInput['multi_tag']) ? intval($arrInput['multi_tag']) : $arrProjectInfo['multi_tag'];
        $intStatus           = isset($arrInput['status']) ? intval($arrInput['status']) : $arrProjectInfo['status'];
        $strRemark           = isset($arrInput['remark']) ? strval($arrInput['remark']) : $arrProjectInfo['remark'];
        $strKeyWord          = isset($arrInput['keyword']) ? strval($arrInput['keyword']) : $arrProjectInfo['keyword'];
        $strWord             = isset($arrInput['word']) ? strval($arrInput['word']) : $arrProjectInfo['highlight_word'];
        $intBefAudit         = isset($arrInput['bef_audit']) ? intval($arrInput['bef_audit']) : $arrProjectInfo['bef_audit'];
        $arrProjectGroup     = (isset($arrInput['project_group'])) ? (empty($arrInput['project_group']) ? array() : (array)$arrInput['project_group']) : $arrProjectInfo['project_group'];
        $arrAuditTrain       = (isset($arrInput['audit_train'])) ? (empty($arrInput['audit_train']) ? array() : (array)$arrInput['audit_train']) : $arrProjectInfo['audit_train'];
        $arrDisplayField     = (isset($arrInput['display_field'])) ? (empty($arrInput['display_field']) ? array() : (array)$arrInput['display_field']) : $arrDBDisplayField;
        $arrDisplayContField = (isset($arrInput['display_cont_field'])) ? (empty($arrInput['display_cont_field']) ? array() : (array)$arrInput['display_cont_field']) : $arrDBDisplayContField;
        //$arrInterfaceId = (isset($arrInput['interface_id'])) ? (empty($arrInput['interface_id'])? array() :  (array) $arrInput['interface_id']) : $arrDBInterfaceField;

        $intIsMaster      = !empty($arrInput['is_master']) ? intval($arrInput['is_master']) : 0;
        $intIsMask        = !empty($arrInput['is_mask']) ? intval($arrInput['is_mask']) : 0;
        $intBId           = !empty($arrInput['b_id']) ? intval($arrInput['b_id']) : 0;
        $intCityId        = !empty($arrInput['city_id']) ? intval($arrInput['city_id']) : 0;
        $intWordsStatus   = !empty($arrInput['words_status']) ? intval($arrInput['words_status']) : 0;
        $intProjectEffect = !empty($arrInput['project_effect']) ? intval($arrInput['project_effect']) : 0;
        $intIsHint        = !empty($arrInput['is_hint']) ? intval($arrInput['is_hint']) : 0;
        $strHint          = !empty($arrInput['hint']) ? strval($arrInput['hint']) : '';

        $arrStrategyNum = (!empty($arrStrategyNum)) ? $arrStrategyNum : array();
        $arrApiNum      = (!empty($arrApiNum)) ? $arrApiNum : array();
        $arrInflow      = (!empty($arrInflow)) ? $arrInflow : array();

        $arrGroupIds = array_filter(array_unique($arrProjectGroup));

        $arrDlInput = array(
            'id'                 => $intProjectId,
            'name'               => $strName,
            'priority'           => $intPriority,
            'item_bundle_max'    => $intItemBundleMax,
            'item_cost_max'      => $intItemCostMax,
            'item_cost_min'      => $intItemCostMin,
            'task_item_max'      => $intTaskItemMax,
            'item_unaudit_max'   => $intItemUnauditMax,
            'filter_deleted'     => $intFilterDeleted,
            'multi_tag'          => $intMultiTag,
            'status'             => $intStatus,
            'bef_audit'          => $intBefAudit,
            'remark'             => $strRemark,
            'project_group'      => $arrGroupIds,
            'audit_train'        => $arrAuditTrain,
            'display_field'      => $arrDisplayField,
            'display_cont_field' => $arrDisplayContField,
            //'interface_id' => $arrInterfaceId,
            'strategy_num'       => $arrStrategyNum,
            'api_num'            => $arrApiNum,
            'project_inflow'     => $arrInflow,
            'keyword'            => $strKeyWord,
            'word'               => $strWord,
            'is_master'          => $intIsMaster,
            'is_mask'            => $intIsMask,
            'b_id'               => $intBId,
            'city_id'            => $intCityId,
            'words_status'       => $intWordsStatus,
            'project_effect'     => $intProjectEffect,
            'is_hint'            => $intIsHint,
            'hint'               => $strHint,
        );

        $arrOutput = Dl_Uegnaudit_Project::editProject($arrDlInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::addProject fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrRetData = $arrOutput['data'];

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function getProjectIdByUId($arrInput) {
        if (empty($arrInput['user_id'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        $intUserId = $arrInput['user_id'];

        $arrDlInput = array(
            'user_id' => $intUserId,
        );

        $arrOutput = Dl_Uegnaudit_Project::getProjectIdByUId($arrDlInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::getProjectIdByUId fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrProjectIdsInfo = $arrOutput['data'];
        $arrProjectIds = array();
        foreach ($arrProjectIdsInfo as $item) {
            $arrProjectIds[] = intval($item['project_id']);
        }

        $arrRetData = array_unique($arrProjectIds);
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);

    }



    /**
     * @param $arrInput
     * @return array
     */
    public static function getProjectList($arrInput) {
        $arrDBInput = array();
        if (!empty($arrInput['name'])) {
            $arrDBInput['name'] = ($arrInput['name']);
        }

        if (!empty($arrInput['priority'])) {
            $arrDBInput['priority'] = ($arrInput['priority']);
        }

        if (!empty($arrInput['b_id'])) {
            $arrDBInput['b_id'] = ($arrInput['b_id']);
        }

        if (!empty($arrInput['create_uname'])) {
            $arrDBInput['create_uname'] = ($arrInput['create_uname']);
        }

        if (!empty($arrInput['begin_time']) && isset($arrInput['end_time'])) {
            $arrDBInput['begin_time'] = ($arrInput['begin_time']);
            $arrDBInput['end_time']   = ($arrInput['end_time']);
        }

        if (isset($arrInput['status'])) {
            $arrDBInput['status'] = ($arrInput['status']);
        }

        if (isset($arrInput['audit_type'])) {
            $arrDBInput['audit_type'] = $arrInput['audit_type'];
        }

        if (isset($arrInput['blur_project_id'])) {
            $arrDBInput['blur_project_id'] = $arrInput['blur_project_id'];
        }

        $arrProjectIds1 = array();
        $arrProjectIds2 = array();
        $arrProjectIds3 = array();
        $arrProjectIds4 = array();      //帖子信息查询的项目id

        if (!empty($arrInput['project_id'])) {
            $arrProjectIds1 = array(
                $arrInput['project_id'],
            );
        }
        if (!empty($arrInput['strategy_num'])) {
            $intStrategyNum = $arrInput['strategy_num'];
            $arrDlInput = array(
                'strategy_num' => $intStrategyNum,
            );

            $arrOutput = Dl_Uegnaudit_Project::getProjectIdByStrategyNumAll($arrDlInput);

            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::getProjectIdByStrategyNum fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $arrProjectIdsInfo = $arrOutput['data'];
            foreach ($arrProjectIdsInfo as $item) {
                $arrProjectIds2[] = intval($item['project_id']);
            }

            if (empty($arrProjectIds2)) {
                $arrProjectIds2[] = 0;
            }
        }

        if (isset($arrInput['project_ids'])) {
            $arrProjectIds3 = $arrInput['project_ids'];
            if (empty($arrProjectIds3)) {
                $arrProjectIds3[] = 0;
            }
        }
        $arrESInput = array();
        if (!empty($arrInput['post_id'])) {
            $arrESInput['data_post_id'] = $arrInput['post_id'];
        }
        if (!empty($arrInput['thread_id'])) {
            $arrESInput['data_thread_id'] = $arrInput['thread_id'];
        }
        if (!empty($arrInput['user_id'])) {
            $arrESInput['data_uid'] = $arrInput['user_id'];
        }
        if (!empty($arrInput['feed_id'])) {
            $arrESInput['feed_id'] = $arrInput['feed_id'];
        }
        if($arrESInput){
            $arrOutput = self::searchPostEs($arrESInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__."::".__FUNCTION__." call searchPostEs fail. input:[".serialize($arrESInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            if(count($arrOutput['data']) > 0){
                foreach ($arrOutput['data'] as $item){
                    $arrProjectIds4[] = $item['project_id'];
                }
            }
            if(NOTICE_LOG_HANDLER){
                $strLog = __CLASS__."::".__FUNCTION__." call searchPostEs fail. input:[".serialize($arrESInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::notice($strLog);
            }
        }
        $arrProjectIds = Util_Project_Project::getProjectIds($arrProjectIds1, $arrProjectIds2, $arrProjectIds3,$arrProjectIds4);

        if (!empty($arrProjectIds)) {
            $arrDBInput['project_ids'] = $arrProjectIds;
        }

        //为减少单个脚本实例查询次数
        if(isset($arrInput['partitionNum']) && isset($arrInput['partitionId'])){
            $arrDBInput['partitionNum'] = $arrInput['partitionNum'];
            $arrDBInput['partitionId'] = $arrInput['partitionId'];
        }

        $offset = (! empty($arrInput["offset"])) ?  intval($arrInput["offset"]) : 0;
        $limit =  (! empty($arrInput["limit"])) ? intval($arrInput["limit"]) : (Libs_Define_Uegnaudit::DEFAULT_PAGE_LIMIT);

        $arrDBInput['offset'] = $offset;
        $arrDBInput['limit']  = $limit;

        $arrOutput = Dl_Uegnaudit_Project::getProjectList($arrDBInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call Dl_Uegnaudit_Project::getProjectList fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrProjectList = $arrOutput['data']['list'];
        $intCount    = $arrOutput['data']['count'];

        $arrRetData = array(
            'list' => $arrProjectList,
            'count' => $intCount,
        );

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function removeProject($arrInput) {
        if (empty($arrInput['id'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        $intProjectId = $arrInput['id'];

        $arrDBInput = array(
            'id' => $intProjectId,
        );
        $arrOutput = Dl_Uegnaudit_Project::removeProject($arrDBInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call Dl_Uegnaudit_Project::removeProject fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function clearProject($arrInput) {
        if (empty($arrInput['id'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        $intProjectId = $arrInput['id'];
        $intBeginTime = intval($arrInput['begin_time']);
        $intEndTime   = intval($arrInput['end_time']);

        $arrDBInput = array(
            'id' => $intProjectId,
            'begin_time' => $intBeginTime,
            'end_time'   => $intEndTime,
        );

        $arrOutput = Dl_Uegnaudit_Project::clearProject($arrDBInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call Dl_Uegnaudit_Project::clearProject fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);

    }

    /**
     * 播客 下线栏目帖子清理任务池
     * @param $arrInput
     * @return array
     */
    public static function clearProjectPost($arrInput) {
        if (empty($arrInput['pid']) || empty($arrInput['call_from'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(),'参数错误');
        }

        if(!in_array($arrInput['call_from'],Libs_Define_Project::$arrClearPostCallFrom)){
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param call from error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(),'来源错误');
        }

        $strLog = __CLASS__ . "::" . __FUNCTION__ . " input:[" . serialize($arrInput) . "];";
        Bingo_Log::notice($strLog);

        /*$arrPid = explode(',',$arrInput['pid']);
        foreach ($arrPid as &$item){
            $item = intval($item);
        }*/

        $arrPid = $arrInput['pid'];
        $arrDBInput = array(
            'ids' => $arrPid,
        );

        $arrOutput = Dl_Uegnaudit_Project::clearProjectPost($arrDBInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call Dl_Uegnaudit_Project::clearProject fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return
     */
    public static function updateProjectStatus($arrInput) {
        if (empty($arrInput['id']) || !isset($arrInput['status'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }

        $intProjectId = intval($arrInput['id']);
        $intStatus    = intval($arrInput['status']);

        // getProjectInfo
        $arrDlInput = array(
            'id' => $intProjectId,
        );
        $arrOutput = self::getProjectInfo($arrDlInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call self::getProjectInfo fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrProjectInfo = $arrOutput['data'];
        if (empty($arrProjectInfo)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " project not exist. project_id is:[" . $intProjectId .  "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $intProjectCurrentStatus = intval($arrProjectInfo['status']);
        if ($intProjectCurrentStatus == Libs_Define_Project::PROJECT_STATUS_CLEARING) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " set status error, project is clearing. project_info is:[" . serialize($arrProjectInfo) .  "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrDBInput = array(
            'id' => $intProjectId,
            'status' => $intStatus,
        );

        $arrOutput = Dl_Uegnaudit_Project::updateProjectStatus($arrDBInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call Dl_Uegnaudit_Project::updateProjectStatus fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * @param $arrInput
     * @return
     */
    public static function updateProjectStatusInner($arrInput) {
        if (empty($arrInput['id']) || !isset($arrInput['status'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }

        $intProjectId = intval($arrInput['id']);
        $intStatus    = intval($arrInput['status']);

        $arrDBInput = array(
            'id' => $intProjectId,
            'status' => $intStatus,
        );

        $arrOutput = Dl_Uegnaudit_Project::updateProjectStatus($arrDBInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call Dl_Uegnaudit_Project::updateProjectStatus fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * 单条导入
     * @param $arrInput
     * @return array
     */
    public static function importPostData($arrInput)
    {
        if (empty($arrInput['data']) || !is_array($arrInput['data'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        $arrPostData = $arrInput['data'];

        $intProjectId = intval($arrInput['project_id']);
        $intStrategyNum = intval($arrInput['strategy_num']);

        // 项目ID为空, 策略号存在
        if (empty($intProjectId) && $intStrategyNum) {
            $arrDlInput = array(
                'strategy_num' => $intStrategyNum,
            );

            $arrOutput = Dl_Uegnaudit_Project::getProjectIdByStrategyNum($arrDlInput);

            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::getProjectIdByStrategyNum fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            // 仅仅为了打日志查问题
            if (true) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::getProjectIdByStrategyNum succ. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::notice($strLog);
            }

            $intProjectId = intval($arrOutput['data']['project_id']);
        }

        if (empty($intProjectId)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " project_id is empty.  :[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }

        $arrServiceInput = array(
            'project_id' => $intProjectId,
            'datas' => array($arrPostData),
        );

        $arrOutput = self::mImportPostData($arrServiceInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call self::mImportPostData fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrImportPostInfo = $arrOutput['data'];
        if (isset($arrImportPostInfo['fail']) && !empty($arrImportPostInfo['fail'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call self::mImportPostData fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * nmq 数据入库
     * @param array
     * @return array
     */
    public static function nmqPost($arrInput) {
        $arrData = Tieba_Service::getArrayParams($arrInput, "data");
        Bingo_Log::notice("nmqPost input: ". serialize($arrData));

        return self::mImportPostData($arrData);
    }

    /**
     * nmq 删帖
     */
    public static function nmqDelPost($arrInput)
    {
        $arrData = Tieba_Service::getArrayParams($arrInput, "data");
        Bingo_Log::notice("nmqDelPost data: " . serialize($arrData));

        if (empty($arrData['post_id']) && empty($arrData['thread_id'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrData)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        //控制删帖命令百分比
//        $intlog_id = intval($arrData['log_id']);
//        $handleWordServer = Wordserver_Wordlist::factory();
//        $switchInfo       = $handleWordServer->getValueByKeys(array('uegdeletenum'), 'uegdeletenum');
//        if($switchInfo && $intlog_id){
//            $uegdeletenum = intval($switchInfo['uegdeletenum']);
//            if($intlog_id%$uegdeletenum != 0){
//                return self::errRet(Tieba_Errcode::ERR_SUCCESS);
//            }
//        }
        $arrOutput = self::_formatDelPost($arrData);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call self::_formatDelPost fail. output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            //return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * nmq 屏蔽主题
     */
    public static function nmqMaskUserThr($arrInput)
    {
        $arrData = Tieba_Service::getArrayParams($arrInput, "data");
        Bingo_Log::notice("nmqMaskUserThr data: " . serialize($arrData));
        if (empty($arrData['mask_thread_id']) || !is_array($arrData['mask_thread_id'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrData)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }
//        $intlog_id = intval($arrData['log_id']);
//        $handleWordServer = Wordserver_Wordlist::factory();
//        $switchInfo       = $handleWordServer->getValueByKeys(array('uegdeletenum'), 'uegdeletenum');
//        if($switchInfo && $intlog_id){
//            $uegdeletenum = intval($switchInfo['uegdeletenum']);
//            if($intlog_id%$uegdeletenum != 0){
//                return self::errRet(Tieba_Errcode::ERR_SUCCESS);
//            }
//        }
        $arrThreadList = $arrData['mask_thread_id'];
        //只处理20条，余下的记录线下脚本离线处理。
        $arrThreadData = array_slice($arrThreadList,0,Libs_Define_Project::PROJECT_ITEM_COST_MAX);
        if($arrThreadData){
            $arrPostListData = Util_Thread_Thread::getThreadList($arrThreadData);
            foreach ($arrThreadData as $value){
                $arrDdInput = array(
                    'post_id'   => isset($arrPostListData[$value]) ? intval($arrPostListData[$value]) : 0,
                    'thread_id' => $value,
                    'status' => 'is_mask',
                );
                $arrOutput = self::_formatDelPost($arrDdInput);
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__."::".__FUNCTION__." call self::_formatDelPost fail. output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                    continue;
                }
            }
        }
        if(count($arrThreadList) > Libs_Define_Project::PROJECT_ITEM_COST_MAX){
            $arrThreadData = array_slice($arrThreadList,Libs_Define_Project::PROJECT_ITEM_COST_MAX);
            $strIndex = Libs_Define_Es::ES_INDEX_UEGNAUDIT_THREAD_INFO;
            $arrEsInput = array(
                "index" => $strIndex,
                "type"  => Libs_Define_Es::ES_TYPE_POST,
            );
            $arrPostListData = Util_Thread_Thread::getThreadList($arrThreadData);
            $intNowTime = Bingo_Timer::getNowTime();
            $arrEsInputBody = array();
            foreach ($arrThreadData as $key=>$item) {
                $intThreadId = intval($item);
                $intPostId = isset($arrPostListData[$intThreadId]) ? intval($arrPostListData[$intThreadId]) : 0;
                $strKey = "ueg_".$intPostId;
                $strValue = Util_Redis_Redis::existsKv($strKey);
                if ($strValue == false) {
                    $strLog = "Util_Redis_Redis existsKv fail key=".$strKey;
                    Bingo_Log::warning($strLog);
                    continue ;
                }
                $arrEsInputBody[] = array(
                    "index" => array(
                        "param" => array(
                            'thread_id' => $intThreadId,
                            'post_id'   => $intPostId,
                            'create_time' => intval($intNowTime),
                            'status' => Libs_Define_Item::ITEM_STATUS_NOT_AUDIT,
                        ),
                    ),
                );
            }
            if(!empty($arrEsInputBody)){
                $arrEsInput['body'] = $arrEsInputBody;
                $arrOutput = Dl_Uegnaudit_Es::bulkIndex($arrEsInput);
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::bulkIndex fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                }
            }
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);

    }

    /**
     * nmq 屏蔽回复
     */
    public static function nmqPostMask($arrInput)
    {
        $arrData = Tieba_Service::getArrayParams($arrInput, "data");
        Bingo_Log::notice("nmqPostMask input: " . serialize($arrData));
        if (empty($arrData['mask_post_id']) || !is_array($arrData['mask_post_id']) ||
            empty($arrData['mask_thread_id']) || !is_array($arrData['mask_thread_id'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrData)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }
//        $intlog_id = intval($arrData['log_id']);
//        $handleWordServer = Wordserver_Wordlist::factory();
//        $switchInfo       = $handleWordServer->getValueByKeys(array('uegdeletenum'), 'uegdeletenum');
//        if($switchInfo && $intlog_id){
//            $uegdeletenum = intval($switchInfo['uegdeletenum']);
//            if($intlog_id%$uegdeletenum != 0){
//                return self::errRet(Tieba_Errcode::ERR_SUCCESS);
//            }
//        }
        $arrPostList = $arrData['mask_post_id'];
        $arrThreadList = $arrData['mask_thread_id'];
        //只处理20条，余下的记录线下脚本离线处理。
        $arrThreadData = array_slice($arrThreadList,0,Libs_Define_Project::PROJECT_ITEM_COST_MAX);
        foreach ($arrThreadData as $key=>$item){
            $arrDdInput = array(
                'post_id'   => $arrPostList[$key],
                'thread_id' => $item,
                'status' => 'is_mask',
            );
            $arrOutput = self::_formatDelPost($arrDdInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__."::".__FUNCTION__." call self::_formatDelPost fail. output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                continue;
            }
        }
        if(count($arrThreadList) > Libs_Define_Project::PROJECT_ITEM_COST_MAX){
            $arrThreadData = array_slice($arrThreadList,Libs_Define_Project::PROJECT_ITEM_COST_MAX);
            $arrPostData = array_slice($arrPostList,Libs_Define_Project::PROJECT_ITEM_COST_MAX);
            $strIndex = Libs_Define_Es::ES_INDEX_UEGNAUDIT_THREAD_INFO;
            $arrEsInput = array(
                "index" => $strIndex,
                "type"  => Libs_Define_Es::ES_TYPE_POST,
            );
            $intNowTime = Bingo_Timer::getNowTime();
            $arrEsInputBody = array();
            foreach ($arrThreadData as $key=>$item) {
                $intThreadId = intval($item);
                $intPostId = intval($arrPostData[$key]);
                $strKey = "ueg_".$intPostId;
                $strValue = Util_Redis_Redis::existsKv($strKey);
                if ($strValue == false) {
                    $strLog = "Util_Redis_Redis existsKv fail key=".$strKey;
                    Bingo_Log::warning($strLog);
                    continue ;
                }
                $arrEsInputBody[] = array(
                    "index" => array(
                        "param" => array(
                            'thread_id' => $intThreadId,
                            'post_id'   => $intPostId,
                            'create_time' => intval($intNowTime),
                            'status' => Libs_Define_Item::ITEM_STATUS_NOT_AUDIT,
                        ),
                    ),
                );
            }
            if(!empty($arrEsInputBody)){
                $arrEsInput['body'] = $arrEsInputBody;
                $arrOutput = Dl_Uegnaudit_Es::bulkIndex($arrEsInput);
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::bulkIndex fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                }
            }
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);

    }

    /**
     * 过滤,验证需要被处理的信息
     * @param array
     * @return array
     */
    public static function _formatDelPost($arrInput) {
        if (empty($arrInput['post_id']) && empty($arrInput['thread_id'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return false;
        }
        $intIsMask = isset($arrInput['status']) ? 1 : 0;
        $intPostId = intval($arrInput['post_id']);
        $intThreadId = intval($arrInput['thread_id']);
        if(empty($intPostId)){
            $intPostId = Util_Thread_Thread::getThreadInfo(array($intThreadId));
        }
        if($intPostId == 0 ){
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        //1.redis过滤，不存在的不处理。
        $strKey = "ueg_".$intPostId;
        $strValue = Util_Redis_Redis::existsKv($strKey);
        if ($strValue == false) {
            $strLog = "Util_Redis_Redis existsKv fail key=".$strKey;
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        //2.组装搜索条件，查询ddbs-post_todo_item表数据
        $arrDdInput = array(
            'post_id'   => $intPostId,
            'thread_id' => $intThreadId,
        );
        $arrOutput = Dl_Uegnaudit_PostTodoItem::getPostTodoItemList($arrDdInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = " call Dl_Uegnaudit_PostTodoItem::getPostTodoItemList fail. input:[" . serialize($arrDdInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrResultData = $arrOutput['data']['list'];
        //根据获取到数据删除ddbs——task_todo_item表和更新ES表数据
        if($arrResultData){
            $arrProjectIds = array();
            $arrProjectLists = array();
            $arrCommits = array();
            foreach ($arrResultData as $value){
                $arrProjectIds[] = $value['project_id'];
                $arrProjectLists[$value['project_id']][] = $value;
            }
            $strProjectIds = implode(",",array_unique($arrProjectIds));
            $arrInput = array(
                'ids'=>$strProjectIds,
            );
            $arrOutput = self::getProjectListById($arrInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = " call self::getProjectListById fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return false;
            }
            foreach ($arrOutput['data']['list'] as $v) {
                //项目ID开启过滤删帖,进行过滤，变更帖子状态
                if($intIsMask){
                    if ($v['is_mask']==1 && $arrProjectLists[$v['id']]) {
                        foreach ($arrProjectLists[$v['id']] as $item){
                            $arrCommits[] =  $item;
                        }
                    }
                }else{
                    if ($v['filter_deleted']==1 && $arrProjectLists[$v['id']]) {
                        foreach ($arrProjectLists[$v['id']] as $item){
                            $arrCommits[] =  $item;
                        }
                    }
                }
            }
            if(empty($arrCommits)){
                return self::errRet(Tieba_Errcode::ERR_SUCCESS);
            }
            $arrDdInput['commit'] = $arrCommits;
            $arrDataOutput = self::updatePostSync($arrDdInput);
            if (false === $arrDataOutput || Tieba_Errcode::ERR_SUCCESS != $arrDataOutput["errno"]) {
                $strLog = " call updatePostSync fail. input:[" . serialize($arrDdInput['commit']) . "]; output:[" . serialize($arrDataOutput) . "]";
                Bingo_Log::warning($strLog);
                //return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 更新ES数据状态,删除DDBS对应数据
     * @param array
     * @return array
     */
    public static function updatePostSync($arrData) {
        if (empty($arrData['post_id']) || empty($arrData['commit']) || !is_array($arrData['commit'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrData)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }
        $arrDDBSData  = $arrData['commit'];
        $intPostId = $arrData['post_id'];
        // 1. 拼装数据,更新ES
        $arrTaskTodoItemIds = array();
        $arrEsInputBody = array();
        // 2. 更新成功,删除DDBS-task_todo_item中的数据
        foreach ($arrDDBSData as $item) {
            //$intPostId = intval($item['post_id']);
            $arrDlInput = array(
                'project_id' => intval($item['project_id']),
                'item_index' => strval($item['item_index']),
                'item_id' => strval($item['item_id']),
                'status' => array(0)
            );
            $arrOutputTask = Dl_Uegnaudit_TaskTodoItem::delTaskTodoItemByItemId($arrDlInput);
            if (false === $arrOutputTask || Tieba_Errcode::ERR_SUCCESS != $arrOutputTask["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_PostTodoItem::delTaskTodoItemByItemId fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutputTask) . "]";
                Bingo_Log::warning($strLog);
                continue;
            }
            // 3. 更新成功,删除DDBS-post_todo_item中的数据
            $arrDlPostInput = array(
                'post_id' => $intPostId,
                'item_index' => strval($item['item_index']),
                'item_id' => strval($item['item_id']),
            );
            $arrOutput = Dl_Uegnaudit_PostTodoItem::delPostTodoItem($arrDlPostInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"] || empty($arrOutput['data']) || empty($arrOutputTask['data'])) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_TaskTodoItem::delPostTodoItem fail. input:[" . serialize($arrDlPostInput) . "]; output:[" . serialize($arrOutput) . "]; output2:[" . serialize($arrOutputTask) . "]";
                Bingo_Log::warning($strLog);
                continue;
            }
            $arrTaskTodoItemIds[] = intval($item['id']);
            $strItemIndex = strval($item['item_index']);
            $strItemId    = strval($item['item_id']);
            $arrEsInputBody[] = array(
                "update" => array(
                    "metadata" => array(
                        "_index" => $strItemIndex,
                        "_type"  => Libs_Define_Es::ES_TYPE_POST,
                        "_id"    => $strItemId,
                    ),
                    "param" => array(
                        'is_synced2DDBS' => 3,
                    ),
                ),
            );
        }
        if(empty($arrEsInputBody)){
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $arrEsInput['body'] = $arrEsInputBody;
        $arrOutput = Dl_Uegnaudit_Es::bulkUpdate($arrEsInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::bulkUpdate fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        if (NOTICE_LOG_HANDLER) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::bulkUpdate. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::notice($strLog);
        }
        $arrEsData = $arrOutput['data'];
        if ($arrEsData['fail']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " some es update failed. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            //return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 删帖获取有效项目信息
     * @param array
     * @return array
     */
    private static function getSearchPostEs($arrInput)
    {
        if (empty($arrInput['data_post_id']) && empty($arrInput['data_thread_id'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return false;
        }
        $arrProjectRes = array();
        $arrOutput = self::searchPostEs($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call searchPostEs fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return false;
        }

        $countlist = count($arrOutput['data']);
        if( $countlist > 0 ){
            //获取查询到的所有项目，是否选择过滤删帖
            $arrProjectIds = array();
            $arrProjectLists = array();
            foreach ($arrOutput['data'] as $item){
                $arrProjectIds[] = $item['project_id'];
                $arrProjectLists[$item['project_id']][] = $item;
            }
            $strProjectIds = implode(",",array_unique($arrProjectIds));
            $arrInput = array(
                'ids'=>$strProjectIds,
            );
            $arrOutput = self::getProjectListById($arrInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = " call self::getProjectListById fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return false;
            }
            $arrProjectRes = array();
            foreach ($arrOutput['data']['list'] as $value){
                //项目ID开启过滤删帖,进行过滤，变更帖子状态
                if($value['filter_deleted']){
                    $arrProjectRes = $arrProjectLists[$value['id']];
                    $intPostCount = count($arrProjectRes);
                    if($intPostCount > 0){
                        $intPsOffset = ceil($intPostCount/50);
                        for ($i = 0;$i < $intPsOffset;$i++){
                            $intPsLimit = $i * 50;
                            $arrDlRes =  array_slice($arrProjectRes,$intPsLimit,50);
                            $arrOutput = Dl_Uegnaudit_TaskTodoItem::updateTaskTodoItemByItemId($arrDlRes);
                            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                                $strLog = " call Dl_Uegnaudit_TaskTodoItem::updateTaskTodoItemByItemId fail. input:[" . serialize($arrDlRes) . "]; output:[" . serialize($arrOutput) . "]";
                                Bingo_Log::warning($strLog);
                                continue;
                            }
                            if (NOTICE_LOG_HANDLER) {
                                $strLog = " call Dl_Uegnaudit_TaskTodoItem::updateTaskTodoItemByItemId succ. input:[" . serialize($arrDlRes) . "]; output:[" . serialize($arrOutput) . "]";
                                Bingo_Log::notice($strLog);
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 批量导入
     * @param $arrInput
     * @return array
     */
    public static function mImportPostData($arrInput) {
        Bingo_Log::warning("mImportPostData input :".serialize($arrInput));
        if (empty($arrInput['datas']) || !is_array($arrInput['datas'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        $arrInputPostDatas = $arrInput['datas'];

        $intProjectId    = intval($arrInput['project_id']);
        $intStrategyNum  = intval($arrInput['strategy_num']);
        $intCallfromFlow = intval($arrInput['callfrom_flow']);

        // 优质用户高优审核
        if (isset($arrInput['isFine']) && isset($arrInput['pushType'])) {
            $intIsFine   = intval($arrInput['isFine']);
            $intPushType = intval($arrInput['pushType']);
            if ($intIsFine == 1) { // 命中优质用户
                if ($intPushType == 0) { // 主态推审
                    $intProjectId = 2890;
                } else if ($intPushType == 1) { // 客态推审
                    $intProjectId = 2889;
                } else if ($intPushType == 2) { // 降权推审
                    $intProjectId = 2892;
                }
            }
        }

        // 项目ID为空, 策略号存在
        if (empty($intProjectId) && $intStrategyNum) {
            $arrDlInput = array(
                'strategy_num' => $intStrategyNum,
            );

            $arrOutput = Dl_Uegnaudit_Project::getProjectIdByStrategyNum($arrDlInput);

            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::getProjectIdByStrategyNum fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            $intProjectId = intval($arrOutput['data']['project_id']);
        }

        if (empty($intProjectId)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " project_id is empty.  :[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }

        Bingo_Log::pushNotice("project_id", $intProjectId);
        Bingo_Log::notice("project_id".$intProjectId);

        // 0. getProjectInfo
        $arrDlInput = array(
            'id' => $intProjectId,
        );
        $arrOutput = self::getProjectInfo($arrDlInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call self::getProjectInfo fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrProjectInfo = $arrOutput['data'];
        if (empty($arrProjectInfo)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " project not exist. project_id is:[" . $intProjectId .  "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $bolFilterDeleted = intval($arrProjectInfo['filter_deleted']);
        $bolIsMaster = intval($arrProjectInfo['is_master']);

        // 0.1 过滤已删帖
        $arrPostDatas = $arrInputPostDatas;
        if ($bolFilterDeleted || $bolIsMaster) {
            $arrPostDatas = Util_Project_Project::isMasterPost($bolFilterDeleted,$bolIsMaster,$arrInputPostDatas);

            if ($arrPostDatas === false) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Util_Project_Project::filterDeletedPost fail. input:[" . serialize($arrInputPostDatas);
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            if (empty($arrPostDatas)) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " after Util_Project_Project::filterDeletedPost, post data is empty. post data input:[" . serialize($arrInputPostDatas);
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_SUCCESS);
            }
        }

        // 0.2 去重
        $arrPrimaryKey  = (array)$arrProjectInfo['primary_key'];
        foreach ($arrPrimaryKey as $strPrimaryKey) {
            $arrUtilInput = array(
                'project_id' => $intProjectId,
                'datas' => $arrPostDatas,
                'primary_key' => $strPrimaryKey,
            );
            $arrPostDatas = Util_Project_Project::filterPrimaryKey($arrUtilInput);

            if ($arrPostDatas === false) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Util_Project_Project::filterPrimaryKey fail. input:[" . serialize($arrInputPostDatas);
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            if (empty($arrPostDatas)) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " after Util_Project_Project::filterPrimaryKey, post data is empty. post data input:[" . serialize($arrInputPostDatas);
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_SUCCESS);
            }
        }

        // 0.4 针对昵称项目2044 进行重新获取昵称问题
        if($intProjectId == 2044){
            $arrPostDatas = Util_Project_Project::filterUserNickName($arrPostDatas);
            if ($arrPostDatas === false) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Util_Project_Project::filterUserNickName fail. input:[" . serialize($arrInputPostDatas);
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            if (empty($arrPostDatas)) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " after Util_Project_Project::filterUserNickName, post data is empty. post data input:[" . serialize($arrInputPostDatas);
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_SUCCESS);
            }
        }
        // 1. 同步数据到 ES
        $intNowTime = Bingo_Timer::getNowTime();
        $intMilliNowTime = $intNowTime * 1000;
        $strDate = Util_Es_Es::getEsDate($intNowTime);
        if ($strDate === false) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " getEsData false. now timestamp is :[" . ($intNowTime) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array());
        }
        $strIndex = Libs_Define_Es::ES_INDEX_UEGNAUDIT_POST_INFO . '_' . $strDate;
        $arrEsInput = array(
            "index" => $strIndex,
            "type"  => Libs_Define_Es::ES_TYPE_POST,
        );

        $arrEsInputBody = array();
        $arrKeysInput = array();
        foreach ($arrPostDatas as $key=>$item) {
            $arrKeysInput[] = $key;
            $item['user_id'] = intval($item['user_id']);
            $item['forum_id'] = intval($item['forum_id']);
            $item['thread_id'] = intval($item['thread_id']);
            $item['post_id'] = intval($item['post_id']);

            // 兼容 create_time 字段
            if (isset($item['create_time'])) {
                if (is_numeric($item['create_time'])) {
                    $item['create_time'] = intval($item['create_time']);
                } else {
                    $item['create_time'] = strtotime($item['create_time']);
                }
            }

            $strTitleContent =  $item['title']. $item['content'];

            //处理首帧作弊图片
            if( $intStrategyNum == 515455 && $item['content']){
                $strContent = $item['content'];
                $pattern_src = '/<img[\s\S]*?src\s*=\s*[\"|\'](.*?)[\"|\'][\s\S]*?>/';
                preg_match_all( $pattern_src , $strContent , $results );
                //非图片帖不流入
                $arrImgList = array();
                $arrNewImgList = array();
                if (!empty($results[1])){
                    $strContent = implode(",",$results[0]);
                    foreach ($results[1] as $k=>$v){
                        if( (strpos($v,'//static.tieba.baidu.com') && strrchr($v,'.') == '.png') || strpos($v,'//tb2.bdstatic.com/tb/editor/images')){
                            //包含这个地址并且是.png的图片过滤掉,或者后者都过滤掉
                        }else{
                            $arrImgList[] = $v;
                            $arrNewImgList[] = str_replace(strrchr($v,'.'),'.webp',$v);
                        }
                    }
                    if($arrImgList && $arrNewImgList){
                        $item['frscontent'] = str_replace($arrImgList,$arrNewImgList,$strContent);
                    }
                }
            }

            //flow emoji标签解析
            if($intCallfromFlow){
                $item['title'] = Util_Project_Project::unicodeDecode($item['title']);
                $item['content'] = Util_Project_Project::unicodeDecode($item['content']);
                $item['userdetail'] = Util_Project_Project::unicodeDecode($item['userdetail']);
                $item['userNickName'] = Util_Project_Project::unicodeDecode($item['userNickName']);
            }

            $arrEsInputBody[] = array(
                "index" => array(
                    "param" => array(
                        'project_id' => $intProjectId,
                        'moniter' => $intStrategyNum,                       //新增策略号字段
                        'create_time' => intval($intNowTime),
                        'create_time_ms' => $intMilliNowTime,
                        'status' => Libs_Define_Item::ITEM_STATUS_NOT_AUDIT,
                        'is_wrong' => 0,
                        'is_audited' => 0,
                        'title_content_md5' => md5($strTitleContent),
                        'is_synced2DDBS' => 0,
                        'is_cleared' => 0,

                        'task_id' => 0,
                        'task_op_uid' =>0,
                        'task_op_uname' => "",
                        'task_op_parent_tag' => 0,
                        'task_op_tag' => 0,
                        'task_op_time' => 0,
                        'task_op_time_ms' => 0,

                        'review_id' => 0,
                        'review_op_uid' => 0,
                        'review_op_uname' => "",
                        'review_op_parent_tag' => 0,
                        'review_op_tag' => 0,
                        'review_op_time' => 0,
                        'review_op_time_ms' => 0,
                        'review_extract_ids' => 0,//记录质检抽取任务ID集合，用于优化质检抽取

                        'data' => $item,
                    ),
                ),
            );

        }

        $arrEsInput['body'] = $arrEsInputBody;
        $arrOutput = Dl_Uegnaudit_Es::bulkIndex($arrEsInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::bulkIndex fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrEsRetData = $arrOutput['data'];
        $arrEsRetDataErrors = $arrEsRetData['errors'];
        $arrEsRetDataSucc = $arrEsRetData['succ'];
        $arrEsRetDataFail = $arrEsRetData['fail'];

        if ($arrEsRetDataErrors) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ES bulk some failed. failed info:[" . serialize($arrEsRetDataFail) . "]";
            Bingo_Log::warning($strLog);
        }
        if (!is_array($arrEsRetDataSucc) || count($arrEsRetDataSucc) <= 0) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ES bulk and nothing to do. succ count:[" . count($arrEsRetDataSucc) . "], fail count:[" . count($arrEsRetDataFail) . "]";
            Bingo_Log::notice($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }

        //数据组日志打点(数据成功写入后)
        Tieba_Stlog::setFileName('feye-stat');
        Tieba_Stlog::addNode('uip',intval(Bingo_Http_Ip::ip2long("*************")));
        $urlkey = 'uegnaudit-project-inflow';
        Tieba_Stlog::addNode('urlkey', $urlkey);

        $arrItemIds = array();
        $arrItemDatas = array();
        foreach ($arrEsRetDataSucc as $key=>$item) {
            $strItemId = strval($item['_id']);
            $arrItemIds[] = $strItemId;
            $intPostId = intval($arrPostDatas[$key]['post_id']);
            $intThreadId = intval($arrPostDatas[$key]['thread_id']);
            if($intPostId && $intThreadId){
                $arrItemDatas[$key]['post_id'] = $intPostId;
                $arrItemDatas[$key]['thread_id'] = $intThreadId;
                $arrItemDatas[$key]['item_id'] = $strItemId;
            }
            $intCommandNo = intval($arrPostDatas[$key]['command_no']);
            Tieba_Stlog::addNode('obj_name', $arrProjectInfo['name']);          //项目名称
            Tieba_Stlog::addNode('obj_param1', $intProjectId);                  //项目ID
            Tieba_Stlog::addNode('obj_param2', $arrProjectInfo['city_id']);     //城市ID
            Tieba_Stlog::addNode('obj_type', $intCommandNo);                    //帖子类型
            Tieba_Stlog::addNode('obj_id', $intStrategyNum);                    //策略号
            Tieba_Stlog::notice();
        }
        // 2. 把正确的结果插入到TODO表中
        $arrDlInput = array(
            'project_id' => $intProjectId,
            'item_index' => $strIndex,
            'item_ids'   => $arrItemIds,
            'create_time' => $intNowTime,
        );
        if (empty($arrItemIds)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " itemids is empty. input info:[" . serialize($arrDlInput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }

        $arrOutput = Dl_Uegnaudit_TaskTodoItem::addTaskTodoItemBatch($arrDlInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_TaskTodoItem::addTaskTodoItemBatch fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        //记录帖子信息，二次过滤使用
        if($arrItemDatas && $bolFilterDeleted){
            $arrDlPostInput = array(
                'project_id' => $intProjectId,
                'item_index' => $strIndex,
                'data_ids'   => $arrItemDatas,
                'create_time' => $intNowTime,
            );
            $arrOutput = Dl_Uegnaudit_PostTodoItem::addPostTodoItemBatch($arrDlPostInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_PostTodoItem::addPostTodoItemBatch fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
            }
        }
        //记录帖子流入
        $userId =  isset($arrInput['user_id']) ? $arrInput['user_id']:0;
        $arrDlLogInput = array(
            'project_id' => $intProjectId,
            'user_id'    => $userId,
            'item_index' => Libs_Define_Es::ES_INDEX_UEGNAUDIT_POST_INFO,
            'item_ids'   => $arrItemIds,
            'status'   => 1,//记录帖子导入 类型
        );
        Dl_Uegnaudit_ItemTodoLog::addItemTodoLogBatch($arrDlLogInput);

        $arrRetData = array(
            'fail' => $arrEsRetDataFail,
        );
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }


    public static function mImportPostDataEs($arrInput) {

        if (empty($arrInput['datas']) || !is_array($arrInput['datas'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        $intProjectId = $arrInput['project_id'];
        $arrPostDatas = $arrInput['datas'];

        // 1. 同步数据到 ES
        $intNowTime = Bingo_Timer::getNowTime();
        $intMilliNowTime = $intNowTime * 1000;
        $strDate = Util_Es_Es::getEsDate($intNowTime);
        if ($strDate === false) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " getEsData false. now timestamp is :[" . ($intNowTime) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array());
        }
        $strIndex = Libs_Define_Es::ES_INDEX_UEGNAUDIT_POST_INFO . '_' . $strDate;
        $arrEsInput = array(
            "index" => $strIndex,
            "type"  => Libs_Define_Es::ES_TYPE_POST,
        );
        $arrEsInputBody = array();
        foreach ($arrPostDatas as $item) {
            $item['user_id'] = intval($item['user_id']);
            $item['forum_id'] = intval($item['forum_id']);
            $item['thread_id'] = intval($item['thread_id']);
            $item['post_id'] = intval($item['post_id']);

            // 兼容 create_time 字段
            if (isset($item['create_time'])) {
                if (is_numeric($item['create_time'])) {
                    $item['create_time'] = intval($item['create_time']);
                } else {
                    $item['create_time'] = strtotime($item['create_time']);
                }
            }

            $strTitleContent =  $item['title']. $item['content'];

            $arrEsInputBody[] = array(
                "index" => array(
                    "param" => array(
                        'project_id' => $intProjectId,
                        'create_time' => $intNowTime,
                        'create_time_ms' => $intMilliNowTime,
                        'status' => Libs_Define_Item::ITEM_STATUS_NOT_AUDIT,
                        'is_wrong' => 0,
                        'is_audited' => 0,
                        'title_content_md5' => md5($strTitleContent),
                        'is_synced2DDBS' => 0,
                        'is_cleared' => 0,

                        'task_id' => 0,
                        'task_op_uid' =>0,
                        'task_op_uname' => "",
                        'task_op_parent_tag' => 0,
                        'task_op_tag' => 0,
                        'task_op_time' => 0,
                        'task_op_time_ms' => 0,

                        'review_id' => 0,
                        'review_op_uid' => 0,
                        'review_op_uname' => "",
                        'review_op_parent_tag' => 0,
                        'review_op_tag' => 0,
                        'review_op_time' => 0,
                        'review_op_time_ms' => 0,

                        'data' => $item,
                    ),
                ),
            );
        }
        $arrEsInput['body'] = $arrEsInputBody;
        $arrOutput = Dl_Uegnaudit_Es::bulkIndex($arrEsInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::bulkIndex fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrEsRetData = $arrOutput['data'];
        $arrEsRetDataErrors = $arrEsRetData['errors'];
        $arrEsRetDataSucc = $arrEsRetData['succ'];
        $arrEsRetDataFail = $arrEsRetData['fail'];

        if ($arrEsRetDataErrors) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ES bulk some failed. failed info:[" . serialize($arrEsRetDataFail) . "]";
            echo $strLog."\n";
        }
        if (!is_array($arrEsRetDataSucc) || count($arrEsRetDataSucc) <= 0) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ES bulk and nothing to do. succ count:[" . count($arrEsRetDataSucc) . "], fail count:[" . count($arrEsRetDataFail) . "]";
            echo $strLog."\n";
            return self::errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrEsRetData['fail']);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getProjectItemList($arrInput) {

        if (empty($arrInput['project_id']) || !isset($arrInput['is_audited'])
        ) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }

        $intProjectId     = intval($arrInput['project_id']);
        $intTaskId        = intval($arrInput['task_id']);
        $intIsAudited     = intval($arrInput['is_audited']);
        $strKeyWord       = strval($arrInput['keyword']);
        $strTaskOpUname   = strval($arrInput['task_op_uname']);
        $intTaskOpUid     = intval($arrInput['task_op_uid']);
        $intDataUid       = intval($arrInput['data_uid']);
        $intDataPostId    = intval($arrInput['data_post_id']);
        $intDataForumId   = intval($arrInput['data_forum_id']);
        $intDataThreadId  = intval($arrInput['data_thread_id']);
        $intDataFeedId    = strval($arrInput['data_feed_id']);
        $intDataGroupId   = intval($arrInput['data_group_id']);
        $strDataTag       = strval($arrInput['data_tag']);
        $intStrategyNum   = intval($arrInput['strategy_num']);
        $strDataDisReason = strval($arrInput['data_disposal_reason']);

        $intTaskOpTag = (!empty($arrInput['task_op_tag']) && is_array($arrInput['task_op_tag'])) ? $arrInput['task_op_tag'] : array();
        $intTaskOpParentTag = (!empty($arrInput['task_op_parent_tag']) && is_array($arrInput['task_op_parent_tag'])) ? $arrInput['task_op_parent_tag'] : array();
        $intBeginTime = intval($arrInput['begin_time']);
        $intEndTime   = intval($arrInput['end_time']);

        $from = (! empty($arrInput["from"])) ?  intval($arrInput["from"]) : 0;
        $size =  (! empty($arrInput["size"])) ? intval($arrInput["size"]) : (Libs_Define_Uegnaudit::DEFAULT_PAGE_LIMIT);

        $sortField = strval($arrInput['sort_field']);
        $sortOrder = strval($arrInput['sort_order']);
        $intIsRandom = intval($arrInput['is_random']);

        $arrEsInput = array(
            'project_id' => $intProjectId,
            'from' => $from,
            'size' => $size,
        );
        if($intIsRandom){
            $arrEsInput['is_random'] = $intIsRandom;
        }
        if ($sortField && $sortOrder) {
            $arrEsInput['sort'] = array(
                array(
                    $sortField => array(
                        "order" => $sortOrder,
                    )
                ),
            );
        }

        if (!empty($strKeyWord)) {
            $arrEsInput['should'][] =
                array(
                    "match" => array(
                        "data.title" => $strKeyWord,
                    )
                );
            $arrEsInput['should'][] =
                array(
                    "match" => array(
                        "data.content" => $strKeyWord,
                    )
                );

            unset($arrEsInput['sort']);
        }

        if (!empty($strTaskOpUname)) {
            $arrEsInput['must'][] =
                array(
                    "term" => array(
                        "task_op_uname" => $strTaskOpUname,
                    )
                );
        }

        if (isset($arrInput['is_synced2DDBS'])) {
            $intIsSynced2DDBS = intval($arrInput['is_synced2DDBS']);
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "is_synced2DDBS" => $intIsSynced2DDBS,
                    )
                );
        }else{
            $arrEsInput['filter'][] =
                array(
                    "terms" => array(
                        "is_synced2DDBS" => array(0,1),
                    )
                );
        }

        if (!empty($intTaskOpTag)) {
            $arrEsInput['filter'][] =
                array(
                    "terms" => array(
                        "task_op_tag" => $intTaskOpTag,
                    )
                );
        }

        if (!empty($intTaskOpParentTag)) {
            $arrEsInput['filter'][] =
                array(
                    "terms" => array(
                        "task_op_parent_tag" => $intTaskOpParentTag,
                    )
                );
        }

        if (!empty($intTaskId)) {
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "task_id" => $intTaskId,
                    )
                );
        }
        if (!empty($intTaskOpUid)) {
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "task_op_uid" => $intTaskOpUid,
                    )
                );
        }

        if (!empty($intDataUid)) {
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "data.user_id" => $intDataUid,
                    )
                );
        }

        if (!empty($intDataPostId)) {
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "data.post_id" => $intDataPostId,
                    )
                );
        }

        if (!empty($intDataForumId)) {
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "data.forum_id" => $intDataForumId,
                    )
                );
        }

        if (!empty($intDataThreadId)) {
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "data.thread_id" => $intDataThreadId,
                    )
                );
        }

        if (!empty($intDataFeedId)) {
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "data.feed_id" => $intDataFeedId,
                    )
                );
        }

        if (!empty($intDataGroupId)) {
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "data.group_id" => $intDataGroupId,
                    )
                );
        }

        if (!empty($strDataTag)) {
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "data.tag.keyword" => $strDataTag,
                    ),
                );
        }
        if (!empty($strDataDisReason)) {
            $arrEsInput['must'][] =
                array(
                    "match" => array(
                        "data.disposal_reason" => $strDataDisReason,
                    ),
                );
        }

        if (!empty($intStrategyNum)) {
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "moniter" => $intStrategyNum,
                    )
                );
        }

        if ($intBeginTime && $intEndTime) {
            $strFilterTime = 'task_op_time';
            if ($intIsAudited == Libs_Define_Item::ITEM_STATUS_NOT_AUDIT) {
                $strFilterTime = 'create_time';
            }

            $arrEsInput['filter'][] =
                array(
                    "range" => array(
                        $strFilterTime => array(
                            "gte" => $intBeginTime,
                            "lt"  => $intEndTime,
                        ),
                    )
                );
        }

        if (in_array($intIsAudited, array(Libs_Define_Item::ITEM_STATUS_NOT_AUDIT, Libs_Define_Item::ITEM_STATUS_AUDITED))) {
            // 已审核 未审核
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "is_audited" => $intIsAudited,
                    )
                );

        } else {
            // 错题集
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "is_wrong" => 1,
                    )
                );
        }
        $arrEsInput['filter'][] =
            array(
                "term" => array(
                    "is_cleared" => 0,
                )
            );

        $arrOutput = Dl_Uegnaudit_Es::search($arrEsInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::search fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrEsData = $arrOutput['data'];
        $intTotal  = $arrEsData['total'];
        $arrEsDataHits = $arrEsData['hits'];
        $arrRetDataList = array();
        foreach ($arrEsDataHits as $item) {
            $arrItemTemp = $item['_source'];
            $arrItemTemp['_index'] = $item['_index'];
            $arrItemTemp['_type']  = $item['_type'];
            $arrItemTemp['_id']    = $item['_id'];
            $arrItemTemp['data']['create_time']=date("Y-m-d H:i:s",$arrItemTemp['data']['create_time']);
            if(isset($arrItemTemp['data']['appeal_time'])){
                $arrItemTemp['data']['appeal_time']=date("Y-m-d H:i:s",$arrItemTemp['data']['appeal_time']);
            }
            $arrRetDataList[]  = $arrItemTemp;
        }

        $arrRetData = array(
            'total' => $intTotal,
            'list'  => $arrRetDataList,
        );

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getProjectItemListByMget($arrInput) {
        if (empty($arrInput['docs']) || !is_array($arrInput['docs'])
        ) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }

        $arrDocs = $arrInput['docs'];

        $arrESInput = array(
            'docs' => $arrDocs,
        );
        $arrOutput = Dl_Uegnaudit_Es::mget($arrESInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::mget fail. input:[" . serialize($arrESInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrEsData = $arrOutput['data'];
        $arrSuccTemp = $arrEsData['succ'];
        $arrFail = $arrEsData['fail'];

        $arrSucc = array();
        foreach ($arrSuccTemp as $item) {
            $arrItemTemp = $item['_source'];
            $arrItemTemp['_index'] = $item['_index'];
            $arrItemTemp['_type']  = $item['_type'];
            $arrItemTemp['_id']    = $item['_id'];
            $arrItemTemp['data']['create_time']=date("Y-m-d H:i:s",$arrItemTemp['data']['create_time']);
            if(isset($arrItemTemp['data']['appeal_time'])){
                $arrItemTemp['data']['appeal_time']=date("Y-m-d H:i:s",$arrItemTemp['data']['appeal_time']);
            }
            $arrSucc[]  = $arrItemTemp;
        }

        $arrRetData = array(
            'succ' => $arrSucc,
            'fail' => $arrFail,
        );
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }



    /**
     * 这里不对project、project_task状态进行更新, 仅做clear
     * @param $arrInput
     * @return array
     */
    public static function doProjectClear($arrInput) {
        if (empty($arrInput['project_id'])
            || empty($arrInput['begin_time']) || empty($arrInput['end_time'])
        ) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }

        $intProjectId = intval($arrInput['project_id']);
        $intBeginTime = intval($arrInput['begin_time']);
        $intEndTime   = intval($arrInput['end_time']);

        // 1. 清空ES中的数据
        $i = 0;
        do {

            $arrEsInput = array(
                'project_id' => $intProjectId,
            );

            $arrEsInput['filter'][] =
                array(
                    "range" => array(
                        "create_time" => array(
                            "gte" => $intBeginTime,
                            "lt"  => $intEndTime,
                        ),
                    )
                );
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "status" => Libs_Define_Item::ITEM_STATUS_NOT_AUDIT,
                    )
                );
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "is_cleared" => 0,
                    )
                );

            $arrEsInput['script'] =
                array(
                    "source" => "ctx._source.is_synced2DDBS=1;ctx._source.is_cleared=1;ctx._source.status=".Libs_Define_Item::ITEM_STATUS_CLEARED,
                    "lang" => "painless",
                );
            $arrEsInput['size'] = 300;

            $arrOutput = Dl_Uegnaudit_Es::count($arrEsInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::count fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
//                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $intTotalNum   = $arrOutput['data']['total'];

            $arrOutput = Dl_Uegnaudit_Es::updateByQuery($arrEsInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::updateByQuery fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
//                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            $intDeletedNum = $arrOutput['data']['updated'];

            $intLeftNum = $intTotalNum - $intDeletedNum;

            $i++;
            if ($i == Libs_Define_Es::ES_DO_CLEAR_TIMES) {
                break;
            }

        } while($intLeftNum > 0);

        if ($i == Libs_Define_Es::ES_DO_CLEAR_TIMES) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " do clear already loop :[" . Libs_Define_Es::ES_DO_CLEAR_TIMES . "] times.";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        // 2. 清空DDBS数据
        $arrDlInput = array(
            'project_id' => $intProjectId,
            'status' => array(0),
            'begin_time' => $intBeginTime,
            'end_time'   => $intEndTime,
        );

        //统计删除总量
        $arrDelTaskTodoItemCount = Dl_Uegnaudit_TaskTodoItem::delTaskTodoItemByTimeCount($arrDlInput);
        if (false === $arrDelTaskTodoItemCount || Tieba_Errcode::ERR_SUCCESS != $arrDelTaskTodoItemCount["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_TaskTodoItem::delTaskTodoItemByTimeCount fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrDelTaskTodoItemCount) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        //单次可删除量 5万  删除次数 向上取整
        $intCount = intval($arrDelTaskTodoItemCount['data']['count']);
        $intSingleThroughput = 10000;
        $intProcessingNum = ceil($intCount/$intSingleThroughput);
        $arrDlInput['limit'] = $intSingleThroughput;
        for($i=0;$i<$intProcessingNum;$i++){
            $arrOutput = Dl_Uegnaudit_TaskTodoItem::delTaskTodoItemByTime($arrDlInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_TaskTodoItem::delTaskTodoItemByTime fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            sleep(1);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * 这里只针对清理任务 count超时，只对ddbs clear
     * @param $arrInput
     * @return array
     */
    public static function doProjectClearTaskTodoItemByIdClear($arrInput){

        //待清理帖DDBS映射ID
        $arrDelTaskTodoItem = Dl_Uegnaudit_TaskTodoItem::getTaskTodoItemByIdDel($arrInput);
        if (false === $arrDelTaskTodoItem || Tieba_Errcode::ERR_SUCCESS != $arrDelTaskTodoItem["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_TaskTodoItem::getTaskTodoItemByIdDel fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDelTaskTodoItem) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrList = $arrDelTaskTodoItem['data']['list'];
        $intProjectId = $arrInput['project_id'];
        $intBeginTime = $arrInput['begin_time'];
        $intEndTime = $arrInput['end_time'];

        if(count($arrList)>0){
            $arrIds = array();
            foreach ($arrList as $value){

                $arrIds[] = $value['id'];
            }

            $intCount = 500;
            $intPn = 0;
            $intPs = 500;
            while ($intPs == $intCount){
                $arrSlip = array_slice($arrIds,$intPn * $intPs,$intPs);
                $intCount = count($arrSlip);
                if($intCount>0){
                    $intPn++;
                    $strIds = implode(",",$arrSlip);
                    $arrServiceInput = array(
                        'project_id' => $intProjectId,
                        'ids'=> $strIds,
                        'begin_time' => $intBeginTime,
                        'end_time' => $intEndTime
                    );
                    //Bingo_Log::notice(__FUNCTION__.json_encode($arrServiceInput));

                    $arrOutput = Dl_Uegnaudit_TaskTodoItem::delTaskTodoItemById($arrServiceInput);
                    if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                        $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_ItemTodoLog::delTaskTodoItemById fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                        Bingo_Log::warning($strLog);
                        return false;
                    }
                }
                usleep(500000);
            }
        }else{
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_TaskTodoItem::getTaskTodoItemByIdDel data empty.";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }
    /**
     * 这里就只做一件事情,清空DDBS中的数据
     * @param $arrInput
     * @return array
     */
    public static function doProjectRemove($arrInput) {
        if (empty($arrInput['project_id'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }

        $intProjectId = intval($arrInput['project_id']);
        $intStatus    = Libs_Define_TaskTodoItem::TASK_TODO_ITEM_STATUS_NOT_ALLOCATE;

        // 清空DDBS数据
        $arrDlInput = array(
            'project_id' => $intProjectId,
            'status' => $intStatus,
        );

        //统计删除总量
        $arrOutputStatusItemCount = Dl_Uegnaudit_TaskTodoItem::getTaskStatusItemCount($arrDlInput);
        if (false === $arrOutputStatusItemCount || Tieba_Errcode::ERR_SUCCESS != $arrOutputStatusItemCount["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_TaskTodoItem::delTaskTodoItemByStatus fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutputStatusItemCount) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        //单次可删除量 5万  删除次数 向上取整
        $intCount = intval($arrOutputStatusItemCount['data']['count']);
        $intSingleThroughput = 10000;
        $intProcessingNum = ceil($intCount/$intSingleThroughput);
        $arrDlInput['limit'] = $intSingleThroughput;
        for($i=0;$i<$intProcessingNum;$i++){
            $arrOutput = Dl_Uegnaudit_TaskTodoItem::delTaskTodoItemByStatus($arrDlInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_TaskTodoItem::delTaskTodoItemByStatus fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            sleep(1);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function updateProjectTaskStatus($arrInput) {

        if (empty($arrInput['id']) || !isset($arrInput['status'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }

        $intProjectId = intval($arrInput['id']);
        $intStatus    = intval($arrInput['status']);
        $arrDBInput = array(
            'id' => $intProjectId,
            'status' => $intStatus,
        );

        $arrOutput = Dl_Uegnaudit_Project::updateProjectTaskStatus($arrDBInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call Dl_Uegnaudit_Project::updateProjectTaskStatus fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function updateProjectItemNum($arrInput) {
        if (empty($arrInput['project_id'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }

        $intProjectId = intval($arrInput['project_id']);

        // 1. 获取DDBS中的数据
        $arrDlInput = array(
            'project_id' => $intProjectId,
        );
        $arrOutput = Dl_Uegnaudit_TaskTodoItem::getTaskTodoItemCount($arrDlInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_TaskTodoItem::getTaskTodoItemCount fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $intDDBSUnauditItemCount = $arrOutput['data']['unaudit_count'];
        $intDDBSLockedItemCount  = $arrOutput['data']['allocated_count'];
        $intDDBSAuditedItemCount = $arrOutput['data']['audited_count'];

        // 2. 获取ES中已审核数据
        $arrEsInput = array(
            'project_id' => $intProjectId,
        );

        $arrEsInput['filter'][] =
            array(
                "term" => array(
                    "status" => Libs_Define_Item::ITEM_STATUS_AUDITED,
                )
            );
        $arrEsInput['filter'][] =
            array(
                "term" => array(
                    "is_cleared" => 0,
                )
            );

        $arrOutput = Dl_Uegnaudit_Es::count($arrEsInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::deleteByQuery fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $intESAuditedItemCount = $arrOutput['data']['total'];

        $intUnauditItemCount = $intDDBSUnauditItemCount;
        $intLockedItemCount  = $intDDBSLockedItemCount;
        $intAuditedItemCount = $intDDBSAuditedItemCount + $intESAuditedItemCount;
        $intTotalItemCount   = $intUnauditItemCount + $intLockedItemCount + $intAuditedItemCount;

        // 3. 更新项目数据
        $arrDlInput = array(
            'id' => $intProjectId,
            'item_total' => $intTotalItemCount,
            'item_done'  => $intAuditedItemCount,
            'item_lock'  => $intLockedItemCount,
            'item_unaudit' => $intUnauditItemCount,
        );
        $arrOutput = Dl_Uegnaudit_Project::updateProjectItemNum($arrDlInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_TaskTodoItem::updateProjectItemNum fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function doDDBS2ESSync($arrInput) {
        if (empty($arrInput['project_id']) || empty($arrInput['task_id'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }

        $intProjectId = intval($arrInput['project_id']);
        $intTaskId    = intval($arrInput['task_id']);
        $limit        = intval($arrInput["limit"]);

        // 1. DDBS选取已审核的数据
        $arrDlInput = array(
            'project_id' => $intProjectId,
            'task_id' => $intTaskId,
            'status'  => Libs_Define_TaskTodoItem::TASK_TODO_ITEM_STATUS_AUDITED,
            'limit'   => $limit,
        );
        $arrOutput = Dl_Uegnaudit_TaskTodoItem::getTaskTodoItemList($arrDlInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_TaskTodoItem::getTaskTodoItemList fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrDDBSData = $arrOutput['data']['list'];

        if (empty($arrDDBSData)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ddbs audited data is empty, input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::notice($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }


        $arrTagInfo = Util_Project_Project::getTagInfo(array('project_id' => $intProjectId));
        $arrUserInfo = Util_Project_Project::getUserInfoByProjectId(array('project_id' => $intProjectId));

        // 2. 拼装数据,更新ES
        $arrTaskTodoItemIds = array();
        $arrEsInputBody = array();
        foreach ($arrDDBSData as $item) {
            $arrTaskTodoItemIds[] = intval($item['id']);
            $strItemIndex = strval($item['item_index']);
            $strItemId    = strval($item['item_id']);
            $intTaskOpUid = intval($item['task_op_uid']);
            $intTaskOpUtype = intval($item['task_op_utype']);
            $intTaskOpTime = intval($item['task_op_time']);
            $intMilliTaskOpTime = $intTaskOpTime * 1000;

            $strTaskOpTag = strval($item['task_op_tag']);
            $arrTaskOpTag = explode(", ", $strTaskOpTag);
            $arrTaskOpParentTag = array();
            $intIsNormalTag = 1;
            foreach ($arrTaskOpTag as &$tagId) {
                $tagId = intval($tagId);
                $arrTaskOpParentTag[] = (int) $arrTagInfo[$tagId]['parent_tag_id'];
                //  注意,这里 type = 1 为作弊tag
                if ($arrTagInfo[$tagId]['type']) {
                    $intIsNormalTag = 0;
                }
            }

            $strTaskOpUname = '';
            if (isset($arrUserInfo[$intTaskOpUid])) {
                $strTaskOpUname = $arrUserInfo[$intTaskOpUid]['user_name'];
            }
            if(!empty($intTaskOpUid) && !empty($arrTaskOpTag)){
                $arrEsInputBody[] = array(
                "update" => array(
                    "metadata" => array(
                        "_index" => $strItemIndex,
                        "_type"  => Libs_Define_Es::ES_TYPE_POST,
                        "_id"    => $strItemId,
                    ),
                    "param" => array(
                        'status' => Libs_Define_Item::ITEM_STATUS_AUDITED,
                        'is_audited' => 1,
                        'is_synced2DDBS' => 1,
                        'is_cleared' => 0,
                        'task_id' => $intTaskId,
                        'task_op_uid' => $intTaskOpUid,
                        'task_op_uname' => $strTaskOpUname,
                        'task_op_utype' => $intTaskOpUtype,
                        'task_op_parent_tag' => $arrTaskOpParentTag,
                        'task_op_tag' => $arrTaskOpTag,
                        'task_op_time' => $intTaskOpTime,
                        'task_op_time_ms' => $intMilliTaskOpTime,
                        'is_normal_tag' => $intIsNormalTag,
                    ),
                ),
            );
            }
        }

        $arrEsInput['body'] = $arrEsInputBody;
        $arrOutput = Dl_Uegnaudit_Es::bulkUpdate($arrEsInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::bulkUpdate fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrEsData = $arrOutput['data'];
        if ($arrEsData['fail']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " some es update failed. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        // 4. 更新成功,删除DDBS中的数据
        $arrDlInput = array(
            'project_id' => $intProjectId,
            'ids' => $arrTaskTodoItemIds,
        );
        $arrOutput = Dl_Uegnaudit_TaskTodoItem::delTaskTodoItemBatch($arrDlInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_TaskTodoItem::delTaskTodoItemBatch fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 标题和内容相同,打上相同标签 新
     * @param $arrInput
     * @return array
     */
    public static function doItemAutoCommitWithSameTitleAndContentNew($arrInput) {
        if (empty($arrInput['project_id'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intProjectId = intval($arrInput['project_id']);
        $intMultiTag = intval($arrInput['multi_tag']);

        // 1、项目维度聚合相同内容
        $arrTitleContent = self::aggregationTitleContent($intProjectId);
        if (false === $arrTitleContent || Tieba_Errcode::ERR_SUCCESS != $arrTitleContent["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call aggregationTitleContent fail. output:[" . serialize($arrTitleContent) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrTitleContent = $arrTitleContent['data'];

        if(empty($arrTitleContent)){
            $strLog = __CLASS__ . "::" . __FUNCTION__ ." call aggregationTitleContent identical title_content is empty.project_id:".$intProjectId;
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS,array("Success"));
        }
        

        // 2、项目配置标签树 单标签
        if($intMultiTag == 0){
            $arrTagRelationship = self::sonToFatherRelationship($intProjectId);
            if (false === $arrTagRelationship || Tieba_Errcode::ERR_SUCCESS != $arrTagRelationship["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call sonToFatherRelationship fail. output:[" . serialize($arrTagRelationship) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $arrTagRelationship = $arrTagRelationship['data'];
        }

        foreach ($arrTitleContent as $item) {

            $arrAuditTitleContentAndTag = array();
            $strTitleContent = strval($item['key']);

            // 3、根据项目、TitleContent md5 聚合审核标签最多
            if($intMultiTag == 1){ //复合标签
                $arrOutputTag = self::compositeLabel($intProjectId,array($strTitleContent));
                if (false === $arrOutputTag || Tieba_Errcode::ERR_SUCCESS != $arrOutputTag["errno"]) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call compositeLabel fail. output:[" . serialize($arrOutputTag) . "]";
                    Bingo_Log::warning($strLog);
                    //return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                    sleep(1);
                    continue;
                }
                if(empty($arrOutputTag['data'])){
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call compositeLabel identical title_content audit tags is empty.";
                    Bingo_Log::warning($strLog);
                    continue;
                }
                $arrAuditTitleContentAndTag = $arrOutputTag['data'];
            } else {//单标签
                $arrAggsData = self::aggregationTitleContentAuditTags($intProjectId,array($strTitleContent));
                if(false === $arrAggsData || Tieba_Errcode::ERR_SUCCESS != $arrAggsData["errno"]){
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call aggregationTitleContentAuditTags identical title_content audit tags is empty.";
                    Bingo_Log::warning($strLog);
                    //return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                    sleep(1);
                    continue;
                }

                if(empty($arrAggsData['data'])){
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call aggregationTitleContentAuditTags identical title_content audit tags is empty.";
                    Bingo_Log::warning($strLog);
                    continue;
                }
                $arrAggsData = $arrAggsData['data'];
                $arrAuditTag = $arrAggsData[0]['key'];
                $arrAuditTitleContentAndTag[$strTitleContent]['task_op_parent_tag']= $arrTagRelationship[$arrAuditTag];
                $arrAuditTitleContentAndTag[$strTitleContent]['task_op_tag']= $arrAuditTag;
            }

            if(empty($arrAuditTitleContentAndTag)){
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " identical title_content audited tags is empty.";
                Bingo_Log::warning($strLog);
                //return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                continue;
            }

            // 4、根据项目、TitleContent md5 查询未审核帖
            $arrEsDataHits = self::getProjectAndTitleContentUnaudited($intProjectId,array($strTitleContent));
            if(false === $arrEsDataHits || Tieba_Errcode::ERR_SUCCESS != $arrEsDataHits["errno"]){
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call getProjectAndTitleContentUnaudited identical title_content unaudited is empty.";
                Bingo_Log::warning($strLog);
                //return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                sleep(1);
                continue;
            }
            $arrEsDataHits = $arrEsDataHits['data'];

            if(empty($arrEsDataHits)){
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " identical title_content unaudited post is empty.";
                Bingo_Log::warning($strLog);
                //return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                continue;
            }

            // 5、打标签
            $arrOutput = self::stickOnaLabelNmq($intProjectId,$arrAuditTitleContentAndTag,$arrEsDataHits);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call stickOnaLabelNmq fail. output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                //return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                sleep(1);
                continue;
            }
            //break;
            //sleep(1);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS,array("Success"));
    }

    /**
     * 项目维度聚合相同内容
     * @param $intProjectId
     * @return array
     */
    public static function aggregationTitleContent($intProjectId){

        // 聚合数据 (获取从未审核的信息)
        $arrEsInput = array();
        $arrEsInput['project_id'] = $intProjectId;
        $arrEsInput['filter'][] =
            array(
                "terms" => array(
                    'is_audited' => array(0,1),
                )
            );
        $arrEsInput['filter'][] =
            array(
                "term" => array(
                    'is_cleared' => 0,
                )
            );
        $arrEsInput['filter'][] =
            array(
                "terms" => array(
                    'is_synced2DDBS' => array(0,1),
                )
            );
        $arrEsInput['must_not'][] =
            array(
                "term" => array(
                    'title_content_md5' => "d41d8cd98f00b204e9800998ecf8427e",
                )
            );
        $arrEsInput['must_not'][] =
            array(
                "term" => array(
                    'task_op_uid' => 1642266823,
                )
            );
        $arrEsInput['aggs'] = array(
            'title_content' => array(
                'terms' => array(
                    "field" => 'title_content_md5',
                    'size' => 50,
                ),
                'aggs' => array(
                    'is_audited' => array(
                        'sum' => array(
                            "field" => "is_audited",
                        )
                    ),
                    'is_audited_filter' => array(
                        'bucket_selector' => array(
                            'buckets_path' => array(
                                'count' => '_count',
                                'is_audited' => 'is_audited',
                            ),
                            'script'=>"params.is_audited > 1 && params.count > 2 && params.count > params.is_audited"
                        )
                    ),
                ),
            ),
        );

        $arrEsInput['function'] = 'LastThreeDays';

        $arrOutput = Dl_Uegnaudit_Es::aggs($arrEsInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::aggs fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrEsData = $arrOutput['data'];
        $arrAggsData = (array) $arrEsData['aggs']['title_content']['buckets'];

        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrAggsData);
    }

    /**
     * 项目维度聚合相同内容审核次数最多测标签
     * @param $intProjectId
     * @param $strTitleContent
     * @return array
     */
    public static function aggregationTitleContentAuditTags($intProjectId,$strTitleContent){
        $arrEsInput = array();
        $arrEsInput['project_id'] = $intProjectId;
        $arrEsInput['filter'][] =
            array(
                "terms" => array(
                    'is_audited' => array(1),
                )
            );
        $arrEsInput['filter'][] =
            array(
                "terms" => array(
                    'title_content_md5' => $strTitleContent,
                )
            );
        $arrEsInput['must_not'][] =
            array(
                "term" => array(
                    'task_op_uid' => 1642266823,
                )
            );
        $arrEsInput['aggs'] = array(
            'tags' => array(
                'terms' => array(
                    "field" => 'task_op_tag',
                    'size' => 1,
                ),
                'aggs' => array(
                    'is_tags_filter' => array(
                        'bucket_selector' => array(
                            'buckets_path' => array(
                                'count' => '_count',
                            ),
                            'script'=>"params.count > 1"
                        )
                    ),
                ),
            ),
        );
        $arrEsInput['function'] = 'LastThreeDays';

        $arrOutput = Dl_Uegnaudit_Es::aggs($arrEsInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::aggs fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrEsData = $arrOutput['data'];
        $arrAggsData = (array) $arrEsData['aggs']['tags']['buckets'];

        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrAggsData);
    }

    /**
     * 项目、相同内容 获取未审帖子
     * @param $intProjectId
     * @param $arrPrimaryKeyIds
     * @return array
     */
    public static function getProjectAndTitleContentUnaudited($intProjectId,$arrPrimaryKeyIds){
        $arrESQueryInput = array();
        $arrESQueryInput['project_id'] = $intProjectId;
        $arrESQueryInput['filter'][] =
            array(
                "terms" => array(
                    'is_audited' => array(0),
                )
            );
        $arrESQueryInput['filter'][] =
            array(
                "term" => array(
                    'is_cleared' => 0,
                )
            );
        $arrESQueryInput['filter'][] =
            array(
                "terms" => array(
                    'is_synced2DDBS' => array(0,1),
                )
            );
        $arrESQueryInput['filter'][] =
            array(
                "terms" => array(
                    'title_content_md5' => $arrPrimaryKeyIds,
                )
            );

        $arrESQueryInput['from'] = 0;
        $arrESQueryInput['size'] = 500;
        $arrESQueryInput['function'] = 'LastThreeDays';
        $arrESQueryInput['_source'] = 'title_content_md5,data.post_id';

        $arrOutput = Dl_Uegnaudit_Es::search($arrESQueryInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::search fail. input:[" . serialize($arrESQueryInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrEsData = $arrOutput['data'];
        $arrEsDataHits = $arrEsData['hits'];

        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrEsDataHits);
    }

    /**
     * 以标签ID作为数组key，父标签作为值
     * @param $intProjectId
     * @return array
     */
    public static function sonToFatherRelationship($intProjectId){
        $arrTagInput = array(
            "project_id" => $intProjectId
        );
        $arrTagOutput =  Service_Tag_Tag::getTagTree($arrTagInput);
        if (false === $arrTagOutput || Tieba_Errcode::ERR_SUCCESS != $arrTagOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Service_Tag_Tag::getTagTree fail. input:[" . serialize($arrTagInput) . "]; output:[" . serialize($arrTagOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrTags = $arrTagOutput['data']['list'];
        // 以标签ID作为数组key，父标签作为值
        $arrTagRelationship = array();
        foreach ($arrTags as $item){
            foreach ($item['tag'] as $value){
                $arrTagRelationship[$value['id']] = $value['parent_id'];
            }
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrTagRelationship);
    }

    /**
     * 复合标签
     * @param $intProjectId
     * @param $arrPrimaryKeyIds
     * @return array
     */
    public static function compositeLabel($intProjectId,$arrPrimaryKeyIds){
        $arrESQueryInput = array();
        $arrESQueryInput['project_id'] = $intProjectId;
        $arrESQueryInput['filter'][] =
            array(
                "terms" => array(
                    'is_audited' => array(1),
                )
            );
        $arrESQueryInput['filter'][] =
            array(
                "terms" => array(
                    'title_content_md5' => $arrPrimaryKeyIds,
                )
            );
        $arrESQueryInput['must_not'][] =
            array(
                "term" => array(
                    'task_op_uid' => 1642266823,
                )
            );

        $arrESQueryInput['from'] = 0;
        $arrESQueryInput['size'] = 100;
        $arrESQueryInput['function'] = 'LastThreeDays';
        $arrESQueryInput['_source'] = 'title_content_md5,task_op_tag,task_op_parent_tag';

        $arrOutput = Dl_Uegnaudit_Es::search($arrESQueryInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::search fail. input:[" . serialize($arrESQueryInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrEsData = $arrOutput['data'];
        $arrEsDataHits = $arrEsData['hits'];

        $arrAuditedData = array();
        foreach ($arrEsDataHits as $item) {
            $arrItemTemp = $item['_source'];
            $strTitleContentMd5 = $arrItemTemp['title_content_md5'];
            $arrAuditedData[$strTitleContentMd5][] = $arrItemTemp;
        }

        //计算被标记标签数量
        $arrKeyTags = array();
        foreach ($arrAuditedData as $key=>$item){
            foreach ($item as $tcKey=>$value){
                $intKeyTag = $value['task_op_tag'];
                if(count($intKeyTag)>1){
                    $intKeyTag = md5($intKeyTag);
                }else{
                    $intKeyTag = $intKeyTag[0];
                }

                if($arrKeyTags[$key][$intKeyTag]){
                    $arrKeyTags[$key][$intKeyTag] =  intval($arrKeyTags[$key][$intKeyTag]) + 1;
                }else {
                    $arrKeyTags[$key][$intKeyTag] = 1;
                }
            }
        }

        //排序 得到被标记次数最多的标签
        $arrAuditTags = array();
        foreach ($arrKeyTags as $key=>$item) {
            $arrTagsSort = $arrKeyTags[$key];
            arsort($arrTagsSort);
            foreach($arrTagsSort as $tag=>$num){
                if($num>1){
                    $arrAuditTags[$key][$tag] = $num;
                }
                break;
            }
        }

        if (empty($arrAuditTags)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " dealer Multi tags is empty.";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        //得到被标记次数最多的标签对应的帖子信息
        $arrAuditedTags = array();
        foreach ($arrAuditedData as $key=>$item){
            foreach ($item as $k=>$value){
                $intKeyTag = $value['task_op_tag'];
                if(count($intKeyTag)>1){
                    $intKeyTag = md5($intKeyTag);
                }else{
                    $intKeyTag = $intKeyTag[0];
                }

                if($arrAuditTags[$key][$intKeyTag]){
                    $arrAuditedTags[$key]['task_op_parent_tag'] = $value['task_op_parent_tag'];
                    $arrAuditedTags[$key]['task_op_tag'] = $value['task_op_tag'];
                }
            }
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrAuditedTags);
    }

    /**
     * 贴上标签 推送NMQ
     * @param $intProjectId
     * @param $arrAuditTitleContentAndTag
     * @param $arrEsDataHits
     * @return array
     */
    public static function stickOnaLabelNmq($intProjectId,$arrAuditTitleContentAndTag,$arrEsDataHits){
        $intCount = 20;
        $intPn = 0;
        $intPs = 20;
        while ($intCount == $intPs) {
            $intCurrentTime = time();
            $arrLogInput = array();//机审帖子Item
            $arrCommit = array();
            $arrNMQInputData = array();

            $intIndex = $intPn * $intPs;
            $arrUnaudited =  array_slice($arrEsDataHits,$intIndex,$intPs);
            if(empty($arrUnaudited)){break;}
            foreach ($arrUnaudited as $item) {
                $strTitleContentMd5 = strval($item['_source']['title_content_md5']);
                if (!isset($arrAuditTitleContentAndTag[$strTitleContentMd5])) {
                    continue;
                }
                $arrAuditedDataInfo =  $arrAuditTitleContentAndTag[$strTitleContentMd5];

                $arrTaskOpTag = (array)$arrAuditedDataInfo['task_op_tag'];
                $arrTaskOpParentTag = (array)$arrAuditedDataInfo['task_op_parent_tag'];

                //提交到NMQ
                $arrNMQInputDataItem = array(
                    '_index' => $item['_index'],
                    '_id' => $item['_id'],
                    'post_id' => $item['_source']['data']['post_id'],
                    "postId"    => $item['_source']['data']['post_id'],
                    'task_op_time' => $intCurrentTime,
                    'task_op_tag'=> $arrTaskOpTag,
                    'task_op_parent_tag' => $arrTaskOpParentTag
                );
                $arrNMQInputData[] = $arrNMQInputDataItem;

                //ddbs log
                array_push($arrLogInput,$item['_id']);

                //dealer service param
                $arrCommit[] = array(
                    "_index"    =>$item['_index'],
                    "_id"       =>$item['_id'],
                    "audit_tags"=>$arrTaskOpParentTag,
                    "tags"      =>$arrTaskOpTag,
                    "post_id"   => $item['_source']['data']['post_id'],
                    "postId"    => $item['_source']['data']['post_id']
                );
            }

            // 1、处置
            if(!empty($arrCommit)){
                $arrServiceInput = array(
                    'project_id' => $intProjectId,
                    'task_op_uid' => 1642266823,
                    'commit' => $arrCommit
                );
                Bingo_Log::notice(__FUNCTION__." callDealer commit data:".json_encode($arrServiceInput));

                $strServiceName = "uegnaudit";
                $strServiceMethod = "callDealer";
                $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                    //return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                    continue;
                }
            }

            // 提交NMQ
            if(!empty($arrNMQInputData)){
                $arrNmqInput = array(
                    'project_id' => $intProjectId,
                    'task_op_uid' => 1642266823,
                    'commit' => $arrNMQInputData,
                );
                Bingo_Log::notice(__FUNCTION__." audit nmq commit data:".json_encode($arrNmqInput));

                Bingo_Timer::start("commituegnAuditTask");
                $arrOutput = Tieba_Commit::commit("anti", "uegnAuditTask", $arrNmqInput);
                Bingo_Timer::end("commituegnAuditTask");
                if ($arrOutput === false) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " commit nmq anti:uegnAuditTask fail. input=" . serialize($arrNmqInput);
                    Bingo_Log::warning($strLog);
                    //return self::errRet(Tieba_Errcode::ERR_FORUM_COMMIT_NMQ);
                    continue;
                }
            }

            // 添加帖子机审记录
            if(!empty($arrLogInput)){
                $arrInputItem = array(
                    'project_id' => $intProjectId,
                    'user_id'      =>1642266823,
                    'item_ids'     =>$arrLogInput,
                    'item_index'   => Libs_Define_Es::ES_INDEX_UEGNAUDIT_POST_INFO,
                    'status'        => 5,//审核提交
                );
                Bingo_Log::notice(__FUNCTION__." audit log record data:".json_encode($arrInputItem));
                Dl_Uegnaudit_ItemTodoLog::addItemTodoLogBatch($arrInputItem);
            }
            $intCount = count($arrUnaudited);
            $intPn ++;
            sleep(2);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,array("Success"));
    }

    /**
     * @param $arrInput
     * @return
     */
    public static function checkDataFrom($arrInput) {
        $strDataFrom = !empty($arrInput['data_from']) ? strval($arrInput['data_from']) : "";
        $intProjectId = intval($arrInput['project_id']);

        if (empty($arrInput['from_type']) || empty($strDataFrom)) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }
        $intFromType  = intval($arrInput['from_type']);

        $arrDataFromTemp  = explode("," , $strDataFrom);
        $arrDataFrom = array();
        foreach ($arrDataFromTemp as &$item) {
            $arrDataFrom[] = intval($item);
        }

        $arrDlInput = array(
            'from_type' => $intFromType,
            'data_from' => $arrDataFrom,
        );

        $arrOutput = Dl_Uegnaudit_Project::getDataFrom($arrDlInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::getDataFrom fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrDBData = $arrOutput['data'];

        $arrStrategyNum = array();
        $arrApiNum      = array();
        if ($intFromType == Libs_Define_Project::PROJECT_FROM_TYPE_STRATEGY_NUM) {
            foreach ($arrDBData as $item) {

                $intDBProjectId = intval($item['project_id']);
                if ($intDBProjectId == $intProjectId) {
                    continue;
                }
                $intStrategyNum = $item['strategy_num'];
                $arrStrategyNum[$intStrategyNum]['data_from_num'] = $item['strategy_num'];
                $arrStrategyNum[$intStrategyNum]['project_id'][] = $item['project_id'];
                $arrStrategyNum[$intStrategyNum]['project_name'][] = $item['project_name'];
            }
            $arrRetData = array();

            foreach ($arrDataFrom as $data_from_num) {
                $arrRetDateItem = array();
                if (isset($arrStrategyNum[$data_from_num])) {
                    $arrRetDateItem = $arrStrategyNum[$data_from_num];
                    $arrRetDateItem['exist'] = 1;
                } else {
                    $arrRetDateItem['data_from_num'] = $data_from_num;
                    $arrRetDateItem['exist'] = 0;
                }
                $arrRetData[] =$arrRetDateItem;
            }

        } else if ($intFromType == Libs_Define_Project::PROJECT_FROM_TYPE_API_NUM) {
            foreach ($arrDBData as $item) {

                $intDBProjectId = intval($item['project_id']);
                if ($intDBProjectId == $intProjectId) {
                    continue;
                }

                $intApiNum = $item['api_num'];
                $arrApiNum[$intApiNum]['data_from_num'] = $item['api_num'];
                $arrApiNum[$intApiNum]['project_id'][] = $item['project_id'];
                $arrApiNum[$intApiNum]['project_name'][] = $item['project_name'];
            }

            $arrRetData = array();
            foreach ($arrDataFrom as $data_from_num) {
                $arrRetDateItem = array();
                if (isset($arrApiNum[$data_from_num])) {
                    $arrRetDateItem = $arrApiNum[$data_from_num];
                    $arrRetDateItem['exist'] = 1;
                } else {
                    $arrRetDateItem['data_from_num'] = $data_from_num;
                    $arrRetDateItem['exist'] = 0;
                }
                $arrRetData[] =$arrRetDateItem;
            }
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function getOverFlowProjectList($arrInput) {

        $arrDlInput = array();
        $arrOutput = Dl_Uegnaudit_Project::getOverFlowProjectList($arrDlInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::getOverFlowProjectList fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrDBData = $arrOutput['data'];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrDBData);
    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function getProjectByIds($arrInput) {

        $intMaxIds = 100;

        if (empty($arrInput['project_ids']) || !is_array($arrInput['project_ids'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }
        $arrProjectIds = $arrInput['project_ids'];

        if (count($arrProjectIds) > $intMaxIds) {
            $strLog = __CLASS__."::".__FUNCTION__."  project id need not exceed 100. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }


        $arrDBInput = array(
            'project_ids' => $arrProjectIds,
            'offset' => 0,
            'limit'  => 100,
        );

        $arrOutput = Dl_Uegnaudit_Project::getProjectList($arrDBInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call Dl_Uegnaudit_Project::getProjectList fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrProjectList = $arrOutput['data']['list'];
        $intCount    = $arrOutput['data']['count'];

        $arrRetData = array(
            'list' => $arrProjectList,
            'count' => $intCount,
        );

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }


    /**
     * @param $arrInput
     * @return
     */
    public static function getProjectTaskList($arrInput) {

        $offset = (! empty($arrInput["offset"])) ?  intval($arrInput["offset"]) : 0;
        $limit =  (! empty($arrInput["limit"])) ? intval($arrInput["limit"]) : (Libs_Define_Uegnaudit::DEFAULT_PAGE_LIMIT);

        $arrDlInput = array(
            'offset'     => $offset,
            'limit'      => $limit,
        );
        //为减少单个脚本实例查询次数
        if(isset($arrInput['partitionNum']) && isset($arrInput['partitionId'])){
            $arrDlInput['partitionNum'] = $arrInput['partitionNum'];
            $arrDlInput['partitionId'] = $arrInput['partitionId'];
        }

        $arrOutput = Dl_Uegnaudit_Project::getProjectTaskList($arrDlInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::getOverFlowProjectList fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrDBData = $arrOutput['data'];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrDBData);
    }


    /**
     * check DDBS和ES数据的一致性
     * @param $arrInput
     * @return
     */
    public static function checkDataConsist($arrInput) {
        if (empty($arrInput['project_id'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }

        $intProjectId = intval($arrInput['project_id']);

        $arrServiceInput = array(
            'project_id' => $intProjectId,
            'is_audited' => Libs_Define_Item::ITEM_STATUS_NOT_AUDIT,
            'is_synced2DDBS' => 0,
            'is_cleared' => 0,
            'sort_field' => 'create_time',
            'sort_order' => 'asc',
            'from' => 0,
            'size' => Libs_Define_Uegnaudit::MULTI_CALL_CHUNK_SIZE,
        );

        $arrOutput = self::getProjectItemList($arrServiceInput);

        $arrEsItemList = $arrOutput['data']['list'];
        $arrEsItemInfo = array();
        $arrEsInputBody = array();
        foreach ($arrEsItemList as $item) {
            $arrEsItemInfoOne = array();

            $strItemIndex   = $item['_index'];
            $strItemId      = $item['_id'];
            $strItemIndexId = $strItemIndex . "_" . $strItemId;
            $strItemMd5     = md5($strItemIndexId);

            $arrEsItemInfoOne['item_index'] = $strItemIndex;
            $arrEsItemInfoOne['item_id']    = $strItemId;
            $arrEsItemInfoOne['item_md5']   = $strItemMd5;
            $arrEsItemInfoOne['project_id'] = $intProjectId;
            $arrEsItemInfoOne['create_time'] = $item['create_time'];

            $arrEsItemInfo[]= $arrEsItemInfoOne;


            $arrEsInputBody[] = array(
                "update" => array(
                    "metadata" => array(
                        "_index" => $strItemIndex,
                        "_type"  => Libs_Define_Es::ES_TYPE_POST,
                        "_id"    => $strItemId,
                    ),
                    "param" => array(
                        'is_synced2DDBS' => 1,
                    ),
                ),
            );
        }

        $arrNotExistData = array();
        $arrServiceInputChunk  = array_chunk($arrEsItemInfo, Libs_Define_Uegnaudit::MULTI_CALL_CHUNK_SIZE);
        foreach ($arrServiceInputChunk as $arrChunkItem) {
            $arrServiceInput = array(
                'data' => $arrChunkItem,
            );

            $strServiceName = "uegnaudit";
            $strServiceMethod = "mGetTaskTodoItemByMd5";
            $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            $arrDBData = $arrOutput['data'];
            foreach ($arrChunkItem as $item) {
                $strItemMd5 = $item['item_md5'];
                if (!isset($arrDBData[$strItemMd5])) {
                    $arrNotExistData[] = $item;
                }
            }
        }

        if (empty($arrNotExistData)) {
            $arrEsInput = array();
            $arrEsInput['project_id'] = $intProjectId;
            $arrEsInput['body'] = $arrEsInputBody;
            $arrOutput = Dl_Uegnaudit_Es::bulkUpdate($arrEsInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::bulkUpdate fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            $strLog = __CLASS__ . "::" . __FUNCTION__ . " all datas are in ddbs.";
            Bingo_Log::notice($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $arrServiceInputChunk  = array_chunk($arrNotExistData, Libs_Define_Uegnaudit::MULTI_CALL_CHUNK_SIZE);
        foreach ($arrServiceInputChunk as $arrChunkItem) {
            $arrServiceInput = array(
                'data' => $arrChunkItem,
            );

            $strServiceName = "uegnaudit";
            $strServiceMethod = "mAddTaskTodoItem";
            $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * @param $arrInput
     * @return
     */
    public static function getDataFromByInflowProjectIds($arrInput) {
        if (empty($arrInput['project_ids']) || !is_array($arrInput['project_ids'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }

        $arrProjectId = array();
        foreach ($arrInput['project_ids'] as $projectId) {
            $intProjectId = intval($projectId);
            $arrProjectId[] = $intProjectId;
        }
        $arrProjectId = array_filter($arrProjectId);
        $intFromType = Libs_Define_Project::PROJECT_FROM_TYPE_PROJECT_INFLOW;

        $arrDlInput = array(
            'from_type'     => $intFromType,
            'data_from'      => $arrProjectId,
        );
        $arrOutput = Dl_Uegnaudit_Project::getDataFrom($arrDlInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::getDataFrom fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrDBData = $arrOutput['data'];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrDBData);
    }

    /**
     * @根据帖子信息搜索ES
     * @param $arrInput
     * @return array
     */
    public static function searchPostEs($arrInput) {

        if (empty($arrInput['data_post_id']) && empty($arrInput['data_thread_id']) && empty($arrInput['feed_id']) && empty($arrInput['data_uid'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }

        $intProjectId = intval($arrInput['project_id']);
        $intDataUid   = intval($arrInput['data_uid']);
        $intDataPostId= intval($arrInput['data_post_id']);
        if(is_array($arrInput['data_thread_id'])){
            $arrDataThreadId = $arrInput['data_thread_id'];
        }else{
            $arrDataThreadId = isset($arrInput['data_thread_id'])?array($arrInput['data_thread_id']):0;
        }

        $strFeedId = strval($arrInput['feed_id']);
        $date_list = $arrInput['date_list'];

        $from = (! empty($arrInput["from"])) ?  intval($arrInput["from"]) : 0;
        $size =  (! empty($arrInput["size"])) ? intval($arrInput["size"]) : 100;
        $arrEsInput = array(
            'date_list' => $date_list,
            'from' => $from,
            'size' => $size,
        );
        if(!empty($intProjectId)){
            $arrEsInput['project_id'] = $intProjectId;
        }
        if(isset($arrInput['train'])){
            $arrEsInput['train'] = $arrInput['train'];
        }
        if (!empty($intDataUid)) {
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "data.user_id" => $intDataUid,
                    )
                );
        }
        if (!empty($intDataPostId)) {
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "data.post_id" => $intDataPostId,
                    )
                );
        }
        if (!empty($arrDataThreadId)) {
            $arrEsInput['filter'][] =
                array(
                    "terms" => array(
                        "data.thread_id" => $arrDataThreadId,
                    )
                );
        }
        if (!empty($strFeedId)) {
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "data.feed_id" => $strFeedId,
                    )
                );
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "is_audited" => 0,
                    )
                );
        }
        if($date_list){
            $arrEsInput['filter'][] =
                array(
                    "term" => array(
                        "is_audited" => 0,
                    )
                );
        }else{
            $arrEsInput['must_not'][] =
                array(
                    "term" => array(
                        "is_synced2DDBS" => 3,
                    )
                );
        }
        $arrEsInput['filter'][] =
            array(
                "term" => array(
                    "is_cleared" => 0,
                )
            );

        $arrOutput = Dl_Uegnaudit_Es::searchPostEs($arrEsInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::searchPostEs fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrEsData = $arrOutput['data'];
        $arrEsDataHits = $arrEsData['hits'];
        $arrRetData = array();
        foreach ($arrEsDataHits as $key=>$item) {
            if(isset($item['_source']['project_id'])){
                $arrRetData[$key]['project_id'] = $item['_source']['project_id'];
            }
            if(isset($item['_source']['train_id'])){
                $arrRetData[$key]['train_id'] = $item['_source']['train_id'];
            }
            $arrRetData[$key]['item_id'] = $item['_id'];
            $arrRetData[$key]['item_index'] = $item['_index'];
            if(!empty($intProjectId)){
                $arrRetData[$key]['thread_id'] = $item['_source']['data']['thread_id'];
            }
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }

    /**
     * 根据项目配置导出信息字段
     * @param $arrInput
     * @return array
     */
    public static function exportProjectField($arrInput) {
        if (empty($arrInput['project_id'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), '获取项目配置字段信息时参数错误');
        }
        //项目配置字段
        $arrOutput = Dl_Uegnaudit_Project::getProjectField($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::getProjectField fail. input:[" . serialize($arrInput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '获取项目配置字段数据出错');
        }
        $arrProjectField = $arrOutput['data']['list'];

        //字段管理字段
        $arrOutputField = Dl_Uegnaudit_Field::getAllField();
        if (false === $arrOutputField || Tieba_Errcode::ERR_SUCCESS != $arrOutputField["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Field::getAllField fail.";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '获取字段管理数据出错');
        }
        $arrAllField = $arrOutputField['data']['list'];

        //生成ExcelTableHead
        $arrExportField = array();
        foreach($arrProjectField as $item){
            foreach($arrAllField as $value){
                if($item['field'] == $value['field_name']){
                    $arrExportField[$item['field']] = $value['field_name_cn'];
                }
            }
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrExportField);
    }

    /**
     * @根据帖子信息搜索ES信息(amis专用)
     * @param $arrInput
     * @return array
     */
    public static function searchPostByAmis($arrInput) {
        if (empty($arrInput['post_id'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }

        $arrDataPostId= explode(PHP_EOL,$arrInput['post_id']);
        $from = (! empty($arrInput["from"])) ?  intval($arrInput["from"]) : 0;
        $size =  (! empty($arrInput["size"])) ? intval($arrInput["size"]) : 200;
        $arrDateList = array();
        for ($i=0; $i <= 3; $i++) {
            $intTime = strtotime('-'.$i.' months');
            $arrDateList[] = date('Y.m*', $intTime);
        }
        $arrEsInput = array(
            'date_list' => $arrDateList,
            'from' => $from,
            'size' => $size,
        );
        if (!empty($arrDataPostId)) {
            $arrEsInput['filter'][] =
                array(
                    "terms" => array(
                        "data.post_id" => $arrDataPostId,
                    )
                );
        }

        $arrOutput = Dl_Uegnaudit_Es::searchPostEs($arrEsInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::searchPostEs fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrEsData = $arrOutput['data'];
        $arrEsDataHits = $arrEsData['hits'];

        //权限判断
        $arrProjectIds = array();
        $intUserId = isset($_SERVER['HTTP_PASS_UID'])?intval($_SERVER['HTTP_PASS_UID']):0;
        if($intUserId){
            //验证是否为管理员
            $bolIsSuper = Util_User_User::isSuper($intUserId);
            if(!$bolIsSuper){
                $arrServiceInput = array(
                    'user_id' => $intUserId,
                );
                $strServiceName = "uegnaudit";
                $strServiceMethod = "getProjectIdByUId";
                $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                    return false;
                }
                $arrProjectIds = $arrOutput['data'];
            }
        }

        $arrRetData = array();
        foreach ($arrEsDataHits as $key=>$item) {
            $intProjectId = intval($item['_source']['project_id']);
            $arrTaskOpId = is_array($item['_source']['task_op_tag']) ? $item['_source']['task_op_tag'] : array();
            $arrReviewOpId = is_array($item['_source']['review_op_tag']) ? $item['_source']['review_op_tag'] : array();
//            if($arrProjectIds && !in_array($intProjectId,$arrProjectIds)){
//                continue;
//            }
            $arrRetData[$key]['project_id'] = $intProjectId;
            if($intProjectId){
                $arrResult = Dl_Uegnaudit_Project::getProjectInfoTime(array('id'=>$intProjectId));
                if($arrResult && $arrResult['data']){
                    $arrRetData[$key]['project_name'] = $arrResult['data']['name'];
                }
            }
            $arrRetData[$key]['task_op_uname'] = $item['_source']['task_op_uname'];
            $arrRetData[$key]['task_op_time'] = $item['_source']['task_op_time'];
            $arrRetData[$key]['review_op_uname'] = $item['_source']['review_op_uname'];
            $arrRetData[$key]['review_op_time'] = $item['_source']['review_op_time'];
            $task_op_tag = '';
            $review_op_tag = '';
            if($arrTaskOpId || $arrReviewOpId){
                $arrInput = array(
                    'project_id'=> $intProjectId,
                    'tag_id'    => array_merge($arrTaskOpId,$arrReviewOpId)
                );
                $arrOutput = Tieba_Service::call("uegnaudit", "getTagInfo", $arrInput, null, null, 'post', null, 'utf-8');
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Tag::getTagList fail output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                }
                $arrTagNameList = $arrOutput['data']['list'];
                if($arrTagNameList){
                    foreach ($arrTaskOpId as $value){
                        $task_op_tag .= isset($arrTagNameList[$value]['tag_name'])?$arrTagNameList[$value]['tag_name']:$value;
                    }
                    foreach ($arrReviewOpId as $value){
                        $review_op_tag .= isset($arrTagNameList[$value]['tag_name'])?$arrTagNameList[$value]['tag_name']:$value;
                    }
                }
            }
            $arrRetData[$key]['task_op_tag'] =  rtrim($task_op_tag, ",");
            $arrRetData[$key]['review_op_tag'] = rtrim($review_op_tag, ",");
            $arrRetData[$key]['thread_id'] = $item['_source']['data']['thread_id'];
            $arrRetData[$key]['post_id'] = $item['_source']['data']['post_id'];
            $arrRetData[$key]['command_no'] = $item['_source']['data']['command_no'];
            $arrRetData[$key]['title'] = $item['_source']['data']['title'];
            $arrRetData[$key]['content'] = $item['_source']['data']['content'];
            $arrRetData[$key]['user_id'] = $item['_source']['data']['user_id'];
            $arrRetData[$key]['user_name'] = $item['_source']['data']['user_name'];
            $arrRetData[$key]['forum_id'] = $item['_source']['data']['forum_id'];
            $arrRetData[$key]['forum_name'] = $item['_source']['data']['forum_name'];
            $arrRetData[$key]['user_icon'] = $item['_source']['data']['user_icon'];
            $arrRetData[$key]['userdetail'] = $item['_source']['data']['userdetail'];
            $arrRetData[$key]['tail_content'] = $item['_source']['data']['tail_content'];
            $arrRetData[$key]['userNickName'] = $item['_source']['data']['userNickName'];
            $arrRetData[$key]['create_time'] = $item['_source']['data']['create_time'];
            $arrRetData[$key]['is_audited'] = $item['_source']['is_audited'];
            $arrRetData[$key]['is_cleared'] = $item['_source']['is_cleared'];
            $arrRetData[$key]['is_synced2DDBS'] = $item['_source']['is_synced2DDBS'];
            $arrRetData[$key]['moniter'] = $item['_source']['moniter'];
            $arrRetData[$key]['push_time'] = $item['_source']['create_time'];
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, array_values($arrRetData));
    }

    /**
     * @根据帖子信息搜索ES信息(申诉专用)
     * @param $arrInput
     * @return array
     */
    public static function searchPostAmis($arrInput) {
        $arrProject = array(
            '2613'  => "申诉主态推审接入测试",
            '2642'  => "主态审核专项"
        );
        $arrProjectids = array_keys($arrProject);
        $arrDate = array();
        if (empty($arrInput['post_id'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }

        $arrDataPostId= explode(PHP_EOL,$arrInput['post_id']);
        $from = (! empty($arrInput["from"])) ?  intval($arrInput["from"]) : 0;
        $size =  (! empty($arrInput["size"])) ? intval($arrInput["size"]) : 50;
        $arrEsInput = array(
            'from' => $from,
            'size' => $size,
        );

        if(empty($arrDataPostId)){
            return self::errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }

        $arrEsInput['filter'][] =
            array(
                "terms" => array(
                    "project_id" => $arrProjectids,
                )
            );
        $arrEsInput['filter'][] =
            array(
                "terms" => array(
                    "data.post_id" => $arrDataPostId,
                )
            );
        $arrOutput = Dl_Uegnaudit_Es::searchPostEs($arrEsInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::searchPostEs fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrEsData = $arrOutput['data'];
        $arrEsDataHits = $arrEsData['hits'];

        $arrRetData = array();
        foreach ($arrEsDataHits as $key=>$item) {
            $intProjectId = intval($item['_source']['project_id']);
            $arrTaskOpId = is_array($item['_source']['task_op_tag']) ? $item['_source']['task_op_tag'] : array();
            $arrReviewOpId = is_array($item['_source']['review_op_tag']) ? $item['_source']['review_op_tag'] : array();
            $arrRetData[$key]['project_id'] = $intProjectId;
            $arrRetData[$key]['project_name'] = isset($arrProject[$intProjectId]) ? $arrProject[$intProjectId] : "";
            $arrRetData[$key]['task_op_uname'] = $item['_source']['task_op_uname'];
            $arrRetData[$key]['task_op_time'] = $item['_source']['task_op_time'];
            $arrRetData[$key]['review_op_uname'] = $item['_source']['review_op_uname'];
            $arrRetData[$key]['review_op_time'] = $item['_source']['review_op_time'];
            $task_op_tag = '';
            $review_op_tag = '';
            if($arrTaskOpId || $arrReviewOpId){
                $arrInput = array(
                    'project_id'=> $intProjectId,
                    'tag_id'    => array_merge($arrTaskOpId,$arrReviewOpId)
                );
                $arrOutput = Tieba_Service::call("uegnaudit", "getTagInfo", $arrInput, null, null, 'post', null, 'utf-8');
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Tag::getTagList fail output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                }
                $arrTagNameList = $arrOutput['data']['list'];
                if($arrTagNameList){
                    foreach ($arrTaskOpId as $value){
                        $task_op_tag .= isset($arrTagNameList[$value]['tag_name'])?$arrTagNameList[$value]['tag_name']:$value;
                    }
                    foreach ($arrReviewOpId as $value){
                        $review_op_tag .= isset($arrTagNameList[$value]['tag_name'])?$arrTagNameList[$value]['tag_name']:$value;
                    }
                }
            }
            $arrRetData[$key]['task_op_tag'] =  rtrim($task_op_tag, ",");
            $arrRetData[$key]['review_op_tag'] = rtrim($review_op_tag, ",");
            $arrRetData[$key]['thread_id'] = $item['_source']['data']['thread_id'];
            $arrRetData[$key]['post_id'] = $item['_source']['data']['post_id'];
            $arrRetData[$key]['command_no'] = $item['_source']['data']['command_no'];
            $arrRetData[$key]['title'] = $item['_source']['data']['title'];
            $arrRetData[$key]['content'] = $item['_source']['data']['content'];
            $arrRetData[$key]['user_id'] = $item['_source']['data']['user_id'];
            $arrRetData[$key]['user_name'] = $item['_source']['data']['user_name'];
            $arrRetData[$key]['forum_id'] = $item['_source']['data']['forum_id'];
            $arrRetData[$key]['forum_name'] = $item['_source']['data']['forum_name'];
            $arrRetData[$key]['user_icon'] = $item['_source']['data']['user_icon'];
            $arrRetData[$key]['userdetail'] = $item['_source']['data']['userdetail'];
            $arrRetData[$key]['tail_content'] = $item['_source']['data']['tail_content'];
            $arrRetData[$key]['userNickName'] = $item['_source']['data']['userNickName'];
            $arrRetData[$key]['create_time'] = $item['_source']['data']['create_time'];
            $arrRetData[$key]['is_audited'] = $item['_source']['is_audited'];
            $arrRetData[$key]['is_cleared'] = $item['_source']['is_cleared'];
            $arrRetData[$key]['is_synced2DDBS'] = $item['_source']['is_synced2DDBS'];
            $arrRetData[$key]['moniter'] = $item['_source']['moniter'];
            $arrRetData[$key]['push_time'] = $item['_source']['create_time'];
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, array_values($arrRetData));
    }

    /**
     * @根据帖子信息搜索ES信息导出(amis专用)
     * @param $arrInput
     * @return array
     */
    public static function exportPostAmis($arrInput) {
        if (empty($arrInput['post_id']) || empty($arrInput['keyword'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        //创建excel,展示字段整理
        $arrWordList = explode(',',$arrInput['keyword']);
        $arrAuditList = array(
            'project_id'          => '项目ID',
            'project_name'        => '项目名称',
            'moniter'             => '策略号',
            'push_time'           => '进审时间',
            'task_op_uid'         => '审核人ID',
            'task_op_uname'       => '审核人',
            'task_op_time'        => '审核时间',
            'task_op_tag'         => '审核标签',
            'review_op_uid'       => '质检人ID',
            'review_op_uname'     => '质检人',
            'review_op_tag'       => '质检标签',
            'review_op_time'      => '质检时间',
        );
        $arrOutputField = Dl_Uegnaudit_Field::getAllField();
        if (false === $arrOutputField || Tieba_Errcode::ERR_SUCCESS != $arrOutputField["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Field::getAllField fail.";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '获取项目导出字段失败');
        }
        $arrAllField = $arrOutputField['data']['list'];
        $arrAuditInfo = array();
        foreach ($arrAllField as $key=>$value){
            if(in_array($value['field_name'],$arrWordList)){
                $arrAuditInfo[$value['field_name']] = $value['field_name_cn'];
            }
        }
        foreach ($arrAuditList as $key=>$value){
            if(in_array($key,$arrWordList)){
                $arrAuditInfo[$key] = $value;
            }
        }
        $strExcelName = "PostDetails";
        $arrExcelTableHead = $arrAuditInfo;
        $arrOutput = self::searchPostByAmis($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call searchPostAmis fail. output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrExcelData = $arrOutput['data'];
        $strExcelContent = Util_Excel_Excel::getExcel2($strExcelName, $arrExcelTableHead, $arrExcelData);

        header("Content-type: application/octet-stream;charset=gbk");
        header("Accept-Ranges: bytes");
        header("Accept-Length: " . strlen($strExcelContent));
        header("Content-Disposition: attachment; filename=" . $strExcelName . '.xls');
        echo $strExcelContent;
        exit;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function checkSuspensionReview($arrInput){
        if (empty($arrInput['user_id']) || empty($arrInput['project_id'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        $intUserId    = $arrInput['user_id'];
        $intProjectId = $arrInput['project_id'];
        $arrServiceInput = array(
            'user_id'    =>$intUserId,
            'project_id' =>$intProjectId
        );
        $arrOutput = Dl_Uegnaudit_User::getSuspendedByPIdAndUid($arrServiceInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_User::getSuspendedByPIdAndUid fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $bolIsSuspension = empty($arrOutput['data']['list']) ? false : true;

        if($bolIsSuspension){
            //直系
            $arrServiceInput = array(
                'project_id' =>$intProjectId
            );
            $arrOutput = Dl_Uegnaudit_Project::getTrainByProId($arrServiceInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::getTrainByProId fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $arrTrainAuditRelation = $arrOutput['data']['list'];
            $arrRelation = array();
            foreach ($arrTrainAuditRelation as $item){
                $arrRelation[] = $item['train_id'];
            }
            $strRelation = implode(",", $arrRelation);
            $arrServiceInput = array(
                "ids" => $strRelation
            );
            $arrOutput = Dl_Uegnaudit_Train::getTrainListById($arrServiceInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Train::getTrainListById( fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $arrTrainList = $arrOutput['data']['list'];
            $arrParentProject = array();
            $arrTrainId = array();
            foreach ($arrTrainList as $item){
                if($item['priority'] > 1) {
                    $arrParentProject[] = $item['id'];
                }
                $arrTrainId[$item['id']]= $item['name'];
            }
            if($arrParentProject){
                $arrTrainId = self::auditTrainRelation($arrParentProject,$arrTrainId);
            }
            return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrTrainId);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, array());
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getAuditProjectTrainList($arrInput){
        if (empty($arrInput['project_id'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        $intProjectId = $arrInput['project_id'];
        $arrServiceInput = array(
            'project_id' =>$intProjectId
        );
        $arrOutput = Dl_Uegnaudit_Project::getTrainByProId($arrServiceInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::getTrainByProId fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrTrainAuditRelation = $arrOutput['data']['list'];
        if(empty($arrTrainAuditRelation)){
            Bingo_Log::notice(__CLASS__."::".__FUNCTION__." Not configured train project. project_id:".$intProjectId);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }
        $arrRelation = array();
        foreach ($arrTrainAuditRelation as $item){
            $arrRelation[] = $item['train_id'];
        }
        $strRelation = implode(",", $arrRelation);
        $arrServiceInput = array(
            "ids" => $strRelation
        );
        $arrOutput = Dl_Uegnaudit_Train::getTrainListById($arrServiceInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Train::getTrainListById( fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrTrainList = $arrOutput['data']['list'];
        $arrParentProject = array();
        $arrTrainId = array();
        foreach ($arrTrainList as $item){
            if($item['priority'] > 1) {
                $arrParentProject[] = $item['id'];
            }
            $arrTrainId[$item['id']]= $item['name'];
        }
        if($arrParentProject){
            $arrTrainId = self::auditTrainRelation($arrParentProject,$arrTrainId);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrTrainId);
    }

    /**
     * @param $arrTrainIds
     * @param $arrTrainId
     * @return array
     */
    private static function auditTrainRelation($arrTrainIds,$arrTrainId){
        $arrServiceInput = array(
            "ids" => $arrTrainIds
        );
        $arrOutput = Dl_Uegnaudit_Train::getTrainRelation($arrServiceInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call DDl_Uegnaudit_Project::getTrainByProId fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrTrainAuditRelation = $arrOutput['data']['list'];
        $arrParentGrade = array();
        foreach ($arrTrainAuditRelation as $item){
            if($item['priority'] > 1){
                $arrParentGrade[] = $item['id'];
            }
            $arrTrainId[$item['id']] = $item['name'];
        }
        if($arrParentGrade){
            $arrTrainId =  self::auditTrainRelation($arrParentGrade,$arrTrainId);
        }
        return $arrTrainId;
    }


    public static function searchPost($arrInput) {
        if (empty($arrInput['post_id'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }

        $arrDataPostId= explode(PHP_EOL,$arrInput['post_id']);
        $from = (! empty($arrInput["from"])) ?  intval($arrInput["from"]) : 0;
        $size =  (! empty($arrInput["size"])) ? intval($arrInput["size"]) : 100;
        $arrDateList = array();
        $intNowTime = Bingo_Timer::getNowTime();
        $intStarTime = $intNowTime - 86400*30;
        for ($intTime = $intStarTime; $intTime <= $intNowTime; $intTime += 86400) {
            $arrDateList[] = date('Y.m.d', $intTime);
        }
        $arrEsInput = array(
            'from' => $from,
            'size' => $size,
        );
        if($arrDateList){
            $arrEsInput['date_list'] = $arrDateList;
        }
        if (!empty($arrDataPostId)) {
            $arrEsInput['filter'][] =
                array(
                    "terms" => array(
                        "data.post_id" => $arrDataPostId,
                    )
                );
        }

        $arrOutput = self::searchPostId($arrEsInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::searchPostEs fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrEsData = $arrOutput['data'];
        $arrEsDataHits = $arrEsData['hits'];

        $arrRetData = array();
        foreach ($arrEsDataHits as $key=>$item) {
            $intProjectId = intval($item['_source']['project_id']);
            $arrTaskOpId = is_array($item['_source']['task_op_tag']) ? $item['_source']['task_op_tag'] : array();
            $arrReviewOpId = is_array($item['_source']['review_op_tag']) ? $item['_source']['review_op_tag'] : array();

            $arrRetData[$key]['project_id'] = $intProjectId;
            if($intProjectId){
                $arrResult = Dl_Uegnaudit_Project::getProjectInfoTime(array('id'=>$intProjectId));
                if($arrResult && $arrResult['data']){
                    $arrRetData[$key]['project_name'] = $arrResult['data']['name'];
                }
            }
            $arrRetData[$key]['task_op_uname'] = $item['_source']['task_op_uname'];
            $arrRetData[$key]['task_op_time'] = $item['_source']['task_op_time'];
            $arrRetData[$key]['review_op_uname'] = $item['_source']['review_op_uname'];
            $arrRetData[$key]['review_op_time'] = $item['_source']['review_op_time'];
            $task_op_tag = '';
            $review_op_tag = '';
            if($arrTaskOpId || $arrReviewOpId){
                $arrInput = array(
                    'project_id'=> $intProjectId,
                    'tag_id'    => array_merge($arrTaskOpId,$arrReviewOpId)
                );
                $arrOutput = Tieba_Service::call("uegnaudit", "getTagInfo", $arrInput, null, null, 'post', null, 'utf-8');
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Tag::getTagList fail output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                }
                if($arrOutput['data']){
                    foreach ($arrOutput['data']['list'] as $k=>$val){
                        if(in_array($k,$arrTaskOpId)){
                            $task_op_tag .= $val['tag_name'].",";
                        }else{
                            $task_op_tag = implode(',',$arrTaskOpId);
                        }
                        if(in_array($k,$arrReviewOpId)){
                            $review_op_tag .= $val['tag_name'].",";
                        }else{
                            $review_op_tag .= implode(',',$arrReviewOpId);
                        }
                    }
                }
            }
            $arrRetData[$key]['task_op_tag'] =  rtrim($task_op_tag, ",");
            $arrRetData[$key]['review_op_tag'] = rtrim($review_op_tag, ",");
            $arrRetData[$key]['thread_id'] = $item['_source']['data']['thread_id'];
            $arrRetData[$key]['post_id'] = $item['_source']['data']['post_id'];
            $arrRetData[$key]['command_no'] = $item['_source']['data']['command_no'];
            $arrRetData[$key]['title'] = $item['_source']['data']['title'];
            $arrRetData[$key]['content'] = $item['_source']['data']['content'];
            $arrRetData[$key]['user_id'] = $item['_source']['data']['user_id'];
            $arrRetData[$key]['user_name'] = $item['_source']['data']['user_name'];
            $arrRetData[$key]['forum_id'] = $item['_source']['data']['forum_id'];
            $arrRetData[$key]['forum_name'] = $item['_source']['data']['forum_name'];
            $arrRetData[$key]['user_icon'] = $item['_source']['data']['user_icon'];
            $arrRetData[$key]['userdetail'] = $item['_source']['data']['userdetail'];
            $arrRetData[$key]['tail_content'] = $item['_source']['data']['tail_content'];
            $arrRetData[$key]['userNickName'] = $item['_source']['data']['userNickName'];
            $arrRetData[$key]['create_time'] = $item['_source']['data']['create_time'];
            $arrRetData[$key]['is_audited'] = $item['_source']['is_audited'];
            $arrRetData[$key]['is_cleared'] = $item['_source']['is_cleared'];
            $arrRetData[$key]['is_synced2DDBS'] = $item['_source']['is_synced2DDBS'];
            $arrRetData[$key]['moniter'] = $item['_source']['moniter'];
            $arrRetData[$key]['push_time'] = $item['_source']['create_time'];
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, array_values($arrRetData));
    }

    public static function searchPostId($arrInput) {
        if (empty($arrInput)) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }

        $from = (! empty($arrInput["from"])) ?  intval($arrInput["from"]) : 0;
        $size =  (! empty($arrInput["size"])) ? intval($arrInput["size"]) : (Libs_Define_Uegnaudit::DEFAULT_PAGE_LIMIT);

        $arrEsInput = self::_formatEsInput($arrInput);
        $arrEsInput['from'] = $from;
        $arrEsInput['size'] = $size;
        $strEsInput = json_encode($arrEsInput);

        // 该部分逻辑主要是指定具体的查询es,提高查询效率
        if (isset($arrInput['date_list']) && count($arrInput['date_list']) > 0) {
            $strPathInfo = '';
            foreach ($arrInput['date_list'] as $key => $value) {
                $strPathInfo .= Libs_Define_Es::ES_INDEX_UEGNAUDIT_POST_INFO . '_' . $value . ',';
            }
            $strPathInfo = rtrim($strPathInfo, ',') . '/' . Libs_Define_Es::ES_TYPE_POST . "/_search";
        } else {
            $strPathInfo = Libs_Define_Es::ES_INDEX_UEGNAUDIT_POST_INFO . '_*/' . Libs_Define_Es::ES_TYPE_POST . "/_search";
        }
        $arrHeader = array(
            "pathinfo" => $strPathInfo,
            "content-type" => 'application/json',
        );
        $arrOutput = Tieba_Ral::call("uegnaudites", "post", $strEsInput, rand(), $arrHeader);
        if(NOTICE_LOG_HANDLER){
            $strLog = __CLASS__."::".__FUNCTION__."searchPostEs input:[".($strEsInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::notice($strLog);
        }
        if (empty($arrOutput)) {
            $strLog = __CLASS__."::".__FUNCTION__." Tieba_Ral::call  fail. input:[".($strEsInput)."]; header:[" . serialize($arrHeader) . "]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrOutput = json_decode($arrOutput, true);
        $arrHits = $arrOutput['hits'];
        $intTotal = $arrHits['total'];
        $arrItemHits = $arrHits['hits'];

        $arrRet = array(
            "total" => $intTotal,
            "hits"  => $arrItemHits,
        );
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

    private static function _formatEsInput($arrInput) {

        $intProjectId = intval($arrInput['project_id']);
        $intTrainId = intval($arrInput['train_id']);

        $arrFilter = isset($arrInput['filter']) && is_array($arrInput['filter']) ? $arrInput['filter'] : array();
        $arrMust   = isset($arrInput['must'])   && is_array($arrInput['must'])   ? $arrInput['must']   : array();
        $arrShould = isset($arrInput['should']) && is_array($arrInput['should']) ? $arrInput['should']   : array();
        $arrMustNot = isset($arrInput['must_not']) && is_array($arrInput['must_not']) ? $arrInput['must_not']   : array();

        $intMinimumShouldMatch = 0;
        if (isset($arrInput['minimum_should_match']) && intval($arrInput['minimum_should_match']) == 1) {
            $intMinimumShouldMatch = intval($arrInput['minimum_should_match']);
        }
        $arrProjectFilter = array(
            "term" => array(
                "project_id" => $intProjectId,
            )
        );
        $arrTrainFilter = array(
            "term" => array(
                "train_id" => $intTrainId,
            )
        );
        if($intProjectId){
            $arrFilter[] = $arrProjectFilter;
        }
        if($intTrainId){
            $arrFilter[] = $arrTrainFilter;
        }
        $arrBol = array(
            "bool" =>   array(
                "filter" => $arrFilter,
                "must" => $arrMust,
                "must_not" => $arrMustNot,
                "should" => $arrShould,
                "minimum_should_match" => $intMinimumShouldMatch,
            )
        );

        $arrEsInput = array(
            "query" => $arrBol,
        );

        if (isset($arrInput['sort'])) {
            $arrEsInput['sort'] = $arrInput['sort'];
        }

        if (isset($arrInput['is_random']) && intval($arrInput['is_random']) == 1) {
            $arrEsInput['sort'] = array(
                "_script" => array(
                    "script" => "Math.random()",
                    "type" => "number",
                    "order" => "asc",
                ),
            );
        }

        return $arrEsInput;
    }

    public static function uegnAuditFilterDel($arrInput){
        Bingo_Log::notice("uegnAuditFilterDel input: ". serialize($arrInput));
        if (empty($arrInput)) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }
        // 提交到NMQ
        $arrNmqInput = array(
            'feed_id' => $arrInput['feed_id'],
            'call_from' => $arrInput['call_from'],
        );

        Bingo_Timer::start("uegFilterDel");
        $arrOutput = Tieba_Commit::commit("anti", "uegFilterDel", $arrNmqInput);
        Bingo_Timer::end("uegFilterDel");
        if ($arrOutput === false) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " commit nmq anti:uegFilterDel fail. input=" . serialize($arrNmqInput);
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_NMQ_OPT);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * nmq anti uegFilterDel
     * @param $arrInput
     * @return array
     */
    public static function nmqFilterDelete($arrInput)
    {
        $arrData = Tieba_Service::getArrayParams($arrInput, "data");
        Bingo_Log::notice("nmqFilterDelete data: " . serialize($arrData));

        if (empty($arrData['call_from'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrData)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $strCallFrom = strval($arrData['call_from']);
        if($strCallFrom == 'mis_uegaudit'){
            $ret = self::auditSetKeyInfo($arrData);
        }else{
            $ret = self::nmqFlowDelete($arrData);
        }

        return $ret;
    }

    /**
     * 接flow删帖处理
     * @param $arrInput
     * @return array
     */
    public static function nmqFlowDelete($arrInput)
    {
        Bingo_Log::notice("nmqFlowDelete data: " . serialize($arrInput));
        if (empty($arrInput['feed_id']) || empty($arrInput['call_from'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $arrServiceInput = array(
            'feed_id' => $arrInput['feed_id'],
            'size' => 10,
        );

        $arrOutput = self::searchPostEs($arrServiceInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call self::searchPostEs fail. output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrServiceInput = array();
        if($arrOutput['data']){
            $arrServiceInput['commit'] = $arrOutput['data'];
            $arrProjectIds = array();
            foreach ($arrOutput['data'] as $item){
                $arrProjectIds[] = intval($item['project_id']);
            }
            $arrOutput = self::updateFilterSync($arrServiceInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__."::".__FUNCTION__." call self::searchPostEs fail. output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            //删除对应项目的redis-key

            $strKey = "ueg_".$arrInput['feed_id'];
            $intTtl = Util_Redis_Redis::getTtl($strKey);
            if ($intTtl > 0) {
                $result = Util_Redis_Redis::getKv($strKey);
                $arrRedisIds = explode(',',$result);
                $arrDiff = array_diff($arrRedisIds,$arrProjectIds);
                if($arrDiff){
                    $strDiffIds = implode(',',$arrDiff);
                    $arrRedisInput = array(
                        'key'   => $strKey,
                        'value' => $strDiffIds,
                        'seconds'=> $intTtl,
                    );
                    $res = Util_Redis_Redis::setNxKv($arrRedisInput);
                    if ($res == false) {
                        Bingo_Log::warning("setKv error. input=".serialize($arrRedisInput)." output=".serialize($res)." ");
                    }
                }else{
                    $res = Util_Redis_Redis::delKv($strKey);
                    if ($res == false) {
                        Bingo_Log::warning("delKv error. input=".serialize($strKey)." output=".serialize($res)." ");
                    }
                }
            }
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    public static function updateFilterSync($arrData) {
        if (empty($arrData['commit']) || !is_array($arrData['commit'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrData)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }
        $arrDDBSData  = $arrData['commit'];
        // 1. 拼装数据,更新ES
        $arrEsInputBody = array();
        // 2. 更新成功,删除DDBS-task_todo_item中的数据
        foreach ($arrDDBSData as $item) {
            $arrDlInput = array(
                'project_id' => intval($item['project_id']),
                'item_index' => strval($item['item_index']),
                'item_id' => strval($item['item_id']),
                'status' => array(0)
            );
            $arrOutputTask = Dl_Uegnaudit_TaskTodoItem::delTaskTodoItemByItemId($arrDlInput);
            if (false === $arrOutputTask || Tieba_Errcode::ERR_SUCCESS != $arrOutputTask["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_PostTodoItem::delTaskTodoItemByItemId fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutputTask) . "]";
                Bingo_Log::warning($strLog);
                continue;
            }
            $strItemIndex = strval($item['item_index']);
            $strItemId    = strval($item['item_id']);
            $arrEsInputBody[] = array(
                "update" => array(
                    "metadata" => array(
                        "_index" => $strItemIndex,
                        "_type"  => Libs_Define_Es::ES_TYPE_POST,
                        "_id"    => $strItemId,
                    ),
                    "param" => array(
                        'is_synced2DDBS' => 3,
                    ),
                ),
            );
        }
        if(empty($arrEsInputBody)){
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $arrEsInput['body'] = $arrEsInputBody;
        $arrOutput = Dl_Uegnaudit_Es::bulkUpdate($arrEsInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::bulkUpdate fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        if (NOTICE_LOG_HANDLER) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::bulkUpdate. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::notice($strLog);
        }
        $arrEsData = $arrOutput['data'];
        if ($arrEsData['fail']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " some es update failed. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getProjectIdByGId($arrInput) {
        if (empty($arrInput['group_ids'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        $arrGroupIds = $arrInput['group_ids'];

        $arrDlInput = array(
            'group_ids' => $arrGroupIds,
        );
        $arrOutput = Dl_Uegnaudit_Project::getProjectIdByGId($arrDlInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::getProjectIdByGId fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrProjectIdsInfo = $arrOutput['data'];
        $arrProjectIds = array();
        foreach ($arrProjectIdsInfo as $item) {
            $arrProjectIds[] = intval($item['project_id']);
        }

        $arrRetData = $arrProjectIds;
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);

    }

    /**
     * 推荐出库接口
     * @param $arrInput
     * @return array
     */
    public static function recommendThreadDelete($arrInput) {
        if (empty($arrInput['project_id']) || empty($arrInput['call_from']) || empty($arrInput['thread_id'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        $intProjectId = intval($arrInput['project_id']);
        $arrThreadId  = $arrInput['thread_id'];
        $arrThreadIds = array();
        if($arrThreadId){
            foreach ($arrThreadId as $thread_id){
                $intThreadId = intval($thread_id);
                if(!empty($intThreadId)){
                    $arrThreadIds[] = $intThreadId;
                }
            }
        }
        if(empty($arrThreadIds)){
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        $arrServiceInput = array(
            'project_id' => $intProjectId,
            'data_thread_id' => $arrThreadIds,
        );

        $arrOutput = self::searchPostEs($arrServiceInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call self::searchPostEs fail. output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrDDBSData = $arrOutput['data'];
        if($arrDDBSData) {
            $arrItems = array();
            $arrEsInputBody = array();
            foreach ($arrDDBSData as $item) {
                $strItemIndex = strval($item['item_index']);
                $strItemId    = strval($item['item_id']);
                $intThreadId    = strval($item['thread_id']);
                $arrItems[] = array(
                    '_index' => strval($item['item_index']),
                    '_id'    => strval($item['item_id']),
                );
                $arrEsInputBody[] = array(
                    "update" => array(
                        "metadata" => array(
                            "_index" => $strItemIndex,
                            "_type"  => Libs_Define_Es::ES_TYPE_POST,
                            "_id"    => $strItemId,
                        ),
                        "param" => array(
                            'is_synced2DDBS' => 3,
                        ),
                    ),
                );

                if($intThreadId){
                    $strKey = "ueg_".$intThreadId;
                    $intTtl = Util_Redis_Redis::getTtl($strKey);
                    if ($intTtl > 0) {
                        $result = Util_Redis_Redis::getKv($strKey);
                        $arrRedisIds = explode(',',$result);
                        $arrProjectIds = array($intProjectId);
                        $arrDiff = array_diff($arrRedisIds,$arrProjectIds);
                        if($arrDiff){
                            $strDiffIds = implode(',',$arrDiff);
                            $arrRedisInput = array(
                                'key'   => $strKey,
                                'value' => $strDiffIds,
                                'seconds'=> $intTtl,
                            );
                            $res = Util_Redis_Redis::setNxKv($arrRedisInput);
                            if ($res == false) {
                                Bingo_Log::warning("setKv error. input=".serialize($arrRedisInput)." output=".serialize($res)." ");
                            }
                        }else{
                            $res = Util_Redis_Redis::delKv($strKey);
                            if ($res == false) {
                                Bingo_Log::warning("delKv error. input=".serialize($strKey)." output=".serialize($res)." ");
                            }
                        }
                    }
                }
            }
            if($arrItems){
                $arrServiceInput = array(
                    'project_id' => $intProjectId,
                    'items'      => $arrItems,
                    'status'     => array(1),
                );
                $strServiceName = "uegnaudit";
                $strServiceMethod = "delTaskTodoItemByMd5";
                $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                    return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
            }
            if($arrEsInputBody){
                $arrEsInput['body'] = $arrEsInputBody;
                $arrOutput = Dl_Uegnaudit_Es::bulkUpdate($arrEsInput);
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::bulkUpdate fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                    return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
            }
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 批量获取项目信息
     * @param $arrInput
     * @return array
     */
    public static function getProjectListById($arrInput) {
        return Dl_Uegnaudit_Project::getProjectListById($arrInput);
    }

    /**
     * 接设置主态帖子扩展属性
     * @param $arrInput
     * @return array
     */
    public static function auditSetKeyInfo($arrInput){
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        Bingo_Log::notice("auditSetKeyInfo data: " . serialize($arrInput));
        if (empty($arrInput) || !isset($arrInput['is_thread']) || empty($arrInput['post_id']) || empty($arrInput['thread_id'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $command_no = intval($arrInput['is_thread']);
        $post_id = intval($arrInput['post_id']);
        $thread_id = intval($arrInput['thread_id']);
        if($command_no == 1){           //主题
            $pid = 'tid';
            $tid = $thread_id;
            $method = 'setKeyInThreadInfo';
        }else{
            $pid = 'pid';
            $tid = $post_id;
            $method = 'setKeyInPostInfoEx';
        }
        $setKeyParams = array(
            "input" => array(
                array(
                    "$pid" => $tid,
                    "fields" => array(
                        array(
                            "fkey" => "after_visible_audit",
                            "value" => 0,
                        ),
                    ),
                ),
            ),
        );
        $service = 'post';
        $setKeyRet = Tieba_Service::call($service,$method, $setKeyParams, null, null, 'post', 'php', 'utf-8');
        Bingo_Log::notice(__CLASS__ . "::" . __FUNCTION__ ." call [auditSetKeyInfo] input[".serialize($setKeyParams)."] output[".serialize($setKeyRet)."]");
        if (!$setKeyRet || $setKeyRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            // 重试一次
            $setKeyRet = Tieba_Service::call($service,$method, $setKeyParams, null, null, 'post', 'php', 'utf-8');
            if (!$setKeyRet || $setKeyRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning( $method.'retry fail, output='.serialize($setKeyRet));
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    public static function auditSetKeyInfoNew($arrInput){
        Bingo_Log::notice("auditSetKeyInfo data: " . serialize($arrInput));
        if (empty($arrInput) || !isset($arrInput['is_thread']) || empty($arrInput['post_id']) || empty($arrInput['thread_id'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $command_no = intval($arrInput['is_thread']);
        $post_id = intval($arrInput['post_id']);
        $thread_id = intval($arrInput['thread_id']);

        //增加判断查询 after_visible_audit = 1时，再成功设置为0，且推送anti.resetAuditState
        $intAfterVisibleAudit = 0;
        if($command_no == 1){           //主题
            $pid = 'tid';
            $tid = $thread_id;
            $method = 'setKeyInThreadInfo';
            $res = Service_Dealer_Dealer::_mgetThread(array($thread_id));
            if(!empty($res) && isset($res[$thread_id]['after_visible_post'])){
                $intAfterVisibleAudit = intval($res[$thread_id]['after_visible_post']);
            }
        }else{
            $pid = 'pid';
            $tid = $post_id;
            $method = 'setKeyInPostInfoEx';
            $input = array("post_ids" => array(0 => $post_id));
            $res = Tieba_Service::call('post', 'getPostInfo', $input, null, null, 'post', 'php', 'utf-8');
            if (!empty($res) && Tieba_Errcode::ERR_SUCCESS == $res["errno"]) {
                $postInfo = $res['output']['0'];
                if(isset($postInfo['after_visible_post'])){
                    $intAfterVisibleAudit = intval($postInfo['after_visible_post']);
                }
            }
        }
        if($intAfterVisibleAudit == 1){
            $strLog = __CLASS__."::".__FUNCTION__."  $method set done. input:[".serialize($arrInput)."]  output:[".serialize($res)."];";
            Bingo_Log::notice($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        //设置帖子属性
        $setKeyParams = array(
            "input" => array(
                array(
                    "$pid" => $tid,
                    "fields" => array(
                        array(
                            "fkey" => "after_visible_audit",
                            "value" => 0,
                        ),
                    ),
                ),
            ),
        );
        $service = 'post';
        $setKeyRet = Tieba_Service::call('post',$method, $setKeyParams, null, null, 'post', 'php', 'utf-8');
        Bingo_Log::notice(__CLASS__ . "::" . __FUNCTION__ ." call [auditSetKeyInfo] input[".serialize($setKeyParams)."] output[".serialize($setKeyRet)."]");
        if (!$setKeyRet || $setKeyRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            // 重试一次
            $setKeyRet = Tieba_Service::call('post',$method, $setKeyParams, null, null, 'post', 'php', 'utf-8');
            if (!$setKeyRet || $setKeyRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning( $method.'retry fail, output='.serialize($setKeyRet));
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }
        //推送NMQ  anti.resetAuditState
        $arrNmqInput = array(
            'post_id'   => $post_id,
            'thread_id' => $thread_id,
            'post_type' => $command_no,
            'call_from' => 'mis_uegaudit'
        );

        Bingo_Timer::start("TimerResetAuditState");
        $arrOutput = Tieba_Commit::commit("anti", "resetAuditState", $arrNmqInput);
        Bingo_Timer::end("TimerResetAuditState");
        if ($arrOutput === false) {
            $arrOutput = Tieba_Commit::commit("anti", "resetAuditState", $arrNmqInput);
            if ($arrOutput === false) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " commit nmq anti:resetAuditState retry fail. input=" . serialize($arrNmqInput);
                Bingo_Log::warning($strLog);
            }
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);

    }

    /**
     * 获取词表高亮次
     * @param $arrInput
     * @return array
     */
    public static function getLightFilterWord($arrInput){
        $arrHitWords = array();
        if (empty($arrInput) || empty($arrInput['word']) || empty($arrInput['content'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrHitWords);
        }
        $strWord = strval($arrInput['word']);
        $confilterContent = strval($arrInput['content']);

        $arrDictList = explode(',',$strWord);
        $arrReq = array();
        foreach ($arrDictList as $k=>$dict) {
            $arrReq[$k]['content']  = $confilterContent;
            $arrReq[$k]['wordlist'] = $dict;
        }
        $arrNewInput = array(
            'app_name'      => 'tieba_anti',
            'app_token'     => '9612da98a14f071ea18a7f993bb3c480',
            'reqs'          => $arrReq,
            'hit_continue'  => 1,
        );
        $req = array(
            'req' => json_encode($arrNewInput),
        );
        $arrOutput = Tieba_Service::call('anti', 'antiDictProxy', $req,'',array(),'post', 'json', 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call  fail. output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return false;
        }
        $arrHit = json_decode($arrOutput['data'],true);
        $strFilterWord = strval($arrHit['hit_words_sum']);
        $arrWordLists =  $arrHit['hit_wordlists'];
        if(count($arrWordLists) > 10){
            $arrWordLists = array_slice($arrWordLists,0,10);
        }
        $arrFilterWord = array();
        $arrWordList = array();
        if(strpos($strFilterWord, " ") >= 1){
            $strFilterWord = str_replace(" ", ",", $strFilterWord);
        }
        $arrDataList = explode(',',$strFilterWord);
        foreach ($arrDataList as $item){
            $arrFilterWord[] = strval($item);
        }
        foreach ($arrWordLists as $words){
            if(isset($words['wordlist']) && !empty($words['words'])){
                $arrWordList[$words['wordlist']] = explode(',',$words['words']);
            }
        }
        $arrHitWords = array(
            'wordsum' => array_unique($arrFilterWord),
            'wordlist' => $arrWordList
        );

        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrHitWords);
    }

    /**
     * 统计各项目各策略号下每天推审量
     * @param $arrInput
     * @return array
     */
    public static function getProjectStrategy($arrInput){

        if (empty($arrInput) || empty($arrInput['begin_time']) || empty($arrInput['end_time'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS,array());
        }

        $offset = (! empty($arrInput["offset"])) ?  intval($arrInput["offset"]) : 0;
        $limit =  (! empty($arrInput["limit"])) ? intval($arrInput["limit"]) : 50;

        $arrDBInput['begin_time'] = $arrInput['begin_time'];
        $arrDBInput['end_time'] = $arrInput['end_time'];
        $arrDBInput['offset'] = $offset;
        $arrDBInput['limit']  = $limit;

        $arrOutput = Dl_Uegnaudit_ProjectDataDay::getProjectStrategy($arrDBInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call Dl_Uegnaudit_ProjectDataDay::getProjectStrategy fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrProjectList = $arrOutput['data']['list'];
        $intCount    = $arrOutput['data']['count'];

        $arrRetData = array(
            'list' => $arrProjectList,
            'count' => $intCount,
        );

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);

    }

    /**
     * 统计各项目每天推审量
     * @param $arrInput
     * @return array
     */
    public static function getProjectDataDay($arrInput){

        if (empty($arrInput) || empty($arrInput['begin_time']) || empty($arrInput['end_time'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS,array());
        }

        $offset = (! empty($arrInput["offset"])) ?  intval($arrInput["offset"]) : 0;
        $limit =  (! empty($arrInput["limit"])) ? intval($arrInput["limit"]) : 50;

        $arrDBInput['begin_time'] = $arrInput['begin_time'];
        $arrDBInput['end_time'] = $arrInput['end_time'];
        $arrDBInput['offset'] = $offset;
        $arrDBInput['limit']  = $limit;

        $arrOutput = Dl_Uegnaudit_ProjectDataDay::getProjectDataDay($arrDBInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call Dl_Uegnaudit_ProjectDataDay::getProjectDataDay fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrProjectList = $arrOutput['data']['list'];
        $intCount    = $arrOutput['data']['count'];

        $arrRetData = array(
            'list' => $arrProjectList,
            'count' => $intCount,
        );

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);

    }

    /**
     * @根据帖子信息搜索ES历史信息(amis审核历史记录)
     * @param $arrInput
     * @return array
     */
    public static function searchHistoryAuditByAmis($arrInput) {
        if (empty($arrInput['project_id']) && empty($arrInput['thread_id']) && empty($arrInput['post_id']) && empty($arrInput['user_name']) && empty($arrInput['op_uname'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS, array(),"请填写筛选条件");
        }

        $from = 0;
        $size = 20;
        $intStartTime = intval($arrInput["start_time"]);
        $intEndTime = intval($arrInput["end_time"]);
        $intType = strval($arrInput["type"]);
        $intProjectId = intval($arrInput["project_id"]);
        $intThreadId = intval($arrInput["thread_id"]);
        $intPostId = intval($arrInput["post_id"]);
        $strUserName = strval($arrInput["user_name"]);
        $strOpUname = strval($arrInput["op_uname"]);

        $arrEsInput = array(
            'from' => $from,
            'size' => $size,
        );
        $arrEsInput['filter'][] = array("term" => array("is_audited" => 1));
        $arrEsInput['filter'][] = array("term" => array("is_cleared" => 0));
        if($intProjectId){
            $arrEsInput['project_id'] = $intProjectId;
        }
        if (!empty($intThreadId)) {
            $arrEsInput['filter'][] = array("term" => array("data.thread_id" => $intThreadId,));
        }
        if (!empty($intPostId)) {
            $arrEsInput['filter'][] = array("term" => array("data.post_id" => $intPostId,));
        }
        if (!empty($strUserName)) {
            $arrEsInput['filter'][] = array("term" => array("data.user_name" => $strUserName,));
        }
        if (!empty($strOpUname)) {
            $arrEsInput['filter'][] = array("term" => array("task_op_uname" => $strOpUname,));
        }
        if (!empty($intType) && !empty($intStartTime) && !empty($intEndTime)) {
            $arrEsInput['filter'][] = array("range" => array($intType=>array("gte" => $intStartTime,"lt" => $intEndTime)));
        }

        $arrOutput = Dl_Uegnaudit_Es::searchPostEs($arrEsInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Es::searchPostEs fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrEsData = $arrOutput['data'];
        $arrEsDataHits = $arrEsData['hits'];

        $arrPunishment = array();
        $arrOutput = Tieba_Service::call('uegnaudit', 'getAllPunishment', array(), null, null, 'post', null, 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call uegnaudit getAllPunishment fail. output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        if($arrOutput['data']['list']){
            foreach ($arrOutput['data']['list'] as $datum){
                $arrPunishment[$datum['punishment_id']] = $datum['title'];
            }
        }

        $arrRetData = array();
        foreach ($arrEsDataHits as $key=>$item) {
            $intProjectId = intval($item['_source']['project_id']);
            if($intProjectId){
                $arrResult = Dl_Uegnaudit_Project::getProjectInfoTime(array('id'=>$intProjectId));
                if($arrResult && $arrResult['data']){
                    $arrRetData[$key]['project_name'] = $arrResult['data']['name'];
                }
            }
            $arrRetData[$key]['project_id'] = $intProjectId;
            $arrRetData[$key]['thread_id'] = $item['_source']['data']['thread_id'];
            $arrRetData[$key]['post_id'] = $item['_source']['data']['post_id'];
            $arrRetData[$key]['title'] = $item['_source']['data']['title'];
            $arrRetData[$key]['content'] = $item['_source']['data']['content'];
            $arrRetData[$key]['user_name'] = $item['_source']['data']['user_name'];
            $arrRetData[$key]['user_id'] = $item['_source']['data']['user_id'];
//            $arrRetData[$key]['forum_id'] = $item['_source']['data']['forum_id'];
            $arrRetData[$key]['forum_name'] = $item['_source']['data']['forum_name'];
//            $arrRetData[$key]['user_icon'] = $item['_source']['data']['user_icon'];
//            $arrRetData[$key]['userdetail'] = $item['_source']['data']['userdetail'];
//            $arrRetData[$key]['tail_content'] = $item['_source']['data']['tail_content'];
//            $arrRetData[$key]['userNickName'] = $item['_source']['data']['userNickName'];
            $arrRetData[$key]['create_time'] = $item['_source']['data']['create_time'];
            $arrRetData[$key]['task_op_uname'] = $item['_source']['task_op_uname'];
            $strHistory = strval($item['_source']['history_audit']);
            $strHistoryAudit = "版本|审核人ID|审核人名称|标签ID:名称|处置手段|审核时间";
            $arrHistoryAudit = explode("|",$strHistoryAudit);
            $arrAudit = array();
            $strAudit = '';
            if($strHistory){
                $arrHistory = explode(PHP_EOL,$strHistory);
                foreach ($arrHistory as $value){
                    $arrValue = explode('|',$value);
                    if($value){
                        foreach ($arrValue as $k=>$v){
                            if($v){
                                $arrAudit[$k] = $arrHistoryAudit[$k].":  ".$v;
                                if($k==4){
                                    $arrAudit[$k] = $arrHistoryAudit[$k].":  ".$arrPunishment[$v];
                                }
                                if($k==5){
                                    $arrAudit[$k] = $arrHistoryAudit[$k].":  ".date('Y-m-d H:i:s',$v);
                                }
                            }
                        }
                        $strAudit .= implode(PHP_EOL,$arrAudit).PHP_EOL;
                    }
                }
            }

            $arrRetData[$key]['history'] = $strAudit;
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, array_values($arrRetData));
    }

    /**
     * 统计各策略 每天审核情况
     * @param $arrInput
     * @return array
     */
    public static function getProjectStrategyAudit($arrInput){
        if (empty($arrInput) || empty($arrInput['begin_time']) || empty($arrInput['end_time'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS,array());
        }
        $offset = (! empty($arrInput["offset"])) ?  intval($arrInput["offset"]) : 0;
        $limit =  (! empty($arrInput["limit"])) ? intval($arrInput["limit"]) : 50;

        $arrDBInput['begin_time'] = $arrInput['begin_time'];
        $arrDBInput['end_time'] = $arrInput['end_time'];
        $arrDBInput['offset'] = $offset;
        $arrDBInput['limit']  = $limit;

        $arrOutput = Dl_Uegnaudit_ProjectDataDay::getProjectStrategyAudit($arrDBInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call Dl_Uegnaudit_ProjectDataDay::getProjectStrategyAudit fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrProjectList = $arrOutput['data']['list'];
        $intCount    = $arrOutput['data']['count'];

        $arrRetData = array(
            'list' => $arrProjectList,
            'count' => $intCount,
        );
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }

    /**
     * @根据用户ID查询审核状态(PASS专用)
     * @param $arrInput
     * @return array
     */
    public static function getUserAuditStatus($arrInput) {
        if (empty($arrInput['uid']) || empty($arrInput['timestamp']) || empty($arrInput['apikey']) || empty($arrInput['sign'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }

        $intUserId = intval($arrInput['uid']);
        $intTimestamp = intval($arrInput['timestamp']);
        $strApikey = strval($arrInput['apikey']);
        $strSign = strval($arrInput['sign']);
        $strSKing = Service_Dealer_Dealer::RESETAPI_SKEY;
        //AKSK验证
        $param = array(
            'uid' => $intUserId,
            'timestamp' => $intTimestamp,
            'apikey' => $strApikey
        );
        ksort($param);
        $strGather = "";
        foreach ($param as $k => $v){
            if ($k != 'sign') {
                $strGather .= "$k=$v";
            }
        }
        $strGather .= $strSKing;
        $sign = md5($strGather);

        if ($strSign != $sign) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_COMFORUM_WRONG_TOKEN, array());
        }
        $strIndex = '';
        for ($i=0; $i < 7; $i++) {
            $intTime = strtotime('-'.$i.' day');
            $arrDateList[] = date('Y.m.d', $intTime);
            $strDate = 'uegnaudit_post_'.date('Y.m.d', $intTime);
            $strIndex .= $strDate.",";
        }
        $strIndex = substr($strIndex, 0, -1);
        $arrRequest = array(
            'query'=> array(
                'bool' => array(
                    'filter' => array(
                        array('term' => array('is_audited' => 0)),             // 0待审，1已审
                        array('term' => array('data.user_id' => $intUserId)),
                        array('term' => array('data.before_audit_type' => 1))  // 1先审后发，2先发后审
                    ),
                    'must_not' => array(
                        array('term' => array('data.is_machine_audit' => 1)),
                        array('term' => array('project_id' => 2772))
                    )
                )
            )
        );

        $strPathInfo = "/$strIndex/post/_count";
        $arrHeader = array(
            "pathinfo" => $strPathInfo,
            "content-type" => 'application/json',
        );
        $strParams = json_encode($arrRequest);
        Bingo_Timer::start(__FUNCTION__);
        $arrOutput = Tieba_Ral::call("uegnaudites", "post", $strParams, rand(), $arrHeader);
        Bingo_Timer::end(__FUNCTION__);
        if (empty($arrOutput)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " Tieba_Ral::call fail. input:[" . ($strParams) . "]; header:[" . serialize($arrHeader) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrOutput = json_decode($arrOutput, true);
        if ($arrOutput['timed_out'] || $arrOutput['status'] == 404) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " request es fail. input:[$strParams]; header:[" . serialize($arrHeader) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $intCount  = intval($arrOutput['count']);

        $arrRetData = array();
        $arrRetData['user_id'] = $intUserId;
        if($intCount == 0){
            $arrRetData['is_audited'] = 1;
        }else{
            $arrRetData['is_audited'] = 0;
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getItemUnauditByCityId($arrInput) {

        $arrDlInput = array();
        $arrOutput = Dl_Uegnaudit_Project::getItemUnauditByCityId($arrDlInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Project::getItemUnauditByCityId fail. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrDBData = $arrOutput['data'];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrDBData);
    }

}


