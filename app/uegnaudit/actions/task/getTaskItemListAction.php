<?php
/**
 * Created by PhpStorm.
 * User: wangyang66
 * Date: 2018/8/2
 * Time: 下午3:52
 */

class getTaskItemListAction extends Util_BaseAction
{
/**
 * @return array
 */
public function _getPrivateInfo() {
    // 访客状态
    $arrPrivateInfo['need_login']  = true;
    $arrPrivateInfo['check_login'] = true;
    $arrPrivateInfo['check_tbs']   = true;

    $arrPrivateInfo['project_id'] = intval($this->_getInput('project_id', 0));
    $arrPrivateInfo['task_id'] = intval($this->_getInput('task_id', 0));
    $arrPrivateInfo['keyword'] = strval($this->_getInput('keyword', ''));
    $arrPrivateInfo['task_op_uname'] = strval($this->_getInput('task_op_uname', ''));
    $arrPrivateInfo['task_op_parent_tag'] = ($this->_getInput('task_op_parent_tag', array()));
    $arrPrivateInfo['task_op_tag'] = ($this->_getInput('task_op_tag', array()));
    $arrPrivateInfo['data_uid'] = intval($this->_getInput('data_uid', 0));
    $arrPrivateInfo['data_post_id'] = intval($this->_getInput('data_post_id', 0));
    $arrPrivateInfo['data_forum_id'] = intval($this->_getInput('data_forum_id', 0));
    $arrPrivateInfo['data_thread_id'] = intval($this->_getInput('data_thread_id', 0));
    $arrPrivateInfo['data_feed_id'] = strval($this->_getInput('data_feed_id', ''));
    $arrPrivateInfo['data_group_id'] = intval($this->_getInput('data_group_id', 0));
    $arrPrivateInfo['is_audited'] = intval($this->_getInput('is_audited', 0));
    $arrPrivateInfo['begin_time'] = intval($this->_getInput('begin_time', 0));
    $arrPrivateInfo['end_time'] = intval($this->_getInput('end_time', 0));
    $arrPrivateInfo['user_id'] = intval($this->_getInput('user_id', 0));
    $arrPrivateInfo['pn'] = intval($this->_getInput('pn', 0));
    $arrPrivateInfo['ps'] = intval($this->_getInput('ps', 20));

    $this->_arrPageNeedPerm  = array(Libs_Define_Permission::PERM_SERVER_NUM_TASK_DETAIL);

    if ($arrPrivateInfo['keyword'] || $arrPrivateInfo['task_op_uname']
        || $arrPrivateInfo['parent_tag_id'] || $arrPrivateInfo['tag_id']
        || $arrPrivateInfo['data_uid'] || $arrPrivateInfo['data_post_id']
        || $arrPrivateInfo['data_forum_id']
        || $arrPrivateInfo['begin_time'] || $arrPrivateInfo['end_time']
    ) {
        $this->_arrPageNeedPerm[]  = Libs_Define_Permission::PERM_SERVER_NUM_TASK_DETAIL_FILTER;
    }

    return $arrPrivateInfo;
}

/**
 * @return bool
 */
public function _checkPrivate() {
    return true;
}

/**
 * @return bool
 */
public function _execute() {
    $intLoginUserId = $this->_objRequest->getCommonAttr('user_id');
    $intProjectId = $this->_objRequest->getPrivateAttr('project_id');
    $intTaskId = $this->_objRequest->getPrivateAttr('task_id');
    $strKeyWord = $this->_objRequest->getPrivateAttr('keyword');
    $strTaskOpUname = $this->_objRequest->getPrivateAttr('task_op_uname');
    $intParentTagId = $this->_objRequest->getPrivateAttr('task_op_parent_tag');
    $intTagId = $this->_objRequest->getPrivateAttr('task_op_tag');
    $intDataUid = $this->_objRequest->getPrivateAttr('data_uid');
    $intDataPostId = $this->_objRequest->getPrivateAttr('data_post_id');
    $intDataForumId = $this->_objRequest->getPrivateAttr('data_forum_id');
    $intIsAudited = $this->_objRequest->getPrivateAttr('is_audited');
    $intBeginTime = $this->_objRequest->getPrivateAttr('begin_time');
    $intEndTime = $this->_objRequest->getPrivateAttr('end_time');
    $intDataThreadId = $this->_objRequest->getPrivateAttr('data_thread_id');
    $intDataFeedId = $this->_objRequest->getPrivateAttr('data_feed_id');
    $intDataGroupId = $this->_objRequest->getPrivateAttr('data_group_id');
    $intPn = $this->_objRequest->getPrivateAttr('pn');
    $intPs = $this->_objRequest->getPrivateAttr('ps');
    $arrUserInfo = array();
    $bolPermPass = Util_User_User::checkCanDoProjectManage($intLoginUserId, $intProjectId, $arrUserInfo);

    if (!$bolPermPass) {
        $strLog = __CLASS__ . "::" . __FUNCTION__ . " you have no perm . project_id is ". $intProjectId . ", login_user_id is ". $intLoginUserId;
        Bingo_Log::warning($strLog);
        return $this->errRet(Tieba_Errcode::ERR_USER_NO_PERM , array(), "权限不足");
    }

    if($intIsAudited == 0){
            if($arrUserInfo['is_suspension'] == 2){
                return $this->errRet(Tieba_Errcode::ERR_USER_NO_PERM , array(), "您暂无审核权限");
            }elseif($arrUserInfo['is_suspension'] == 1){
                $arrServiceInput = array(
                    'user_id'    =>$intLoginUserId,
                    'project_id' =>$intProjectId
                );
                $strServiceName = "uegnaudit";
                $strServiceMethod = "checkSuspensionReview";
                $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                    return $this->errRet(Tieba_Errcode::ERR_USER_NO_PERM , array(), "您暂无审核权限");
                }
                $arrTrainList = $arrOutput['data'];
                if($arrTrainList){
                    $arrName = array();
                    foreach ($arrTrainList as $item){
                        $arrName[] = $item;
                    }
                    $strNames = "您暂无权限审核该项目，请通过[".implode("\\",$arrName)."]项目考核";
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . $strNames;
                    Bingo_Log::warning($strLog);
                    return $this->errRet(Tieba_Errcode::ERR_USER_NO_PERM , array(), $strNames);
                }
            }
        }

    if ($intTaskId) {
        $bolPermPass = Util_User_User::checkCanDoTaskManage($intLoginUserId, $intTaskId);
        if (!$bolPermPass) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " you have no perm . task_id is ". $intTaskId . ", login_user_id is ". $intLoginUserId;
            Bingo_Log::warning($strLog);
            return $this->errRet(Tieba_Errcode::ERR_USER_NO_PERM , array(), "权限不足");
        }
    }

    // 获取task info
    $intDefaultTag = 0;
    if ($intTaskId) {
        $arrServiceInput = array(
            'id'    => $intTaskId,
        );
        $strServiceName = "uegnaudit";
        $strServiceMethod = "getTaskInfo";
        $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrTaskInfo = $arrOutput['data'];
        $intDefaultTag  = (int)$arrTaskInfo['default_tag']['id'];
    }

    $arrServiceInput = array(
        'id' => $intProjectId,
    );
    $strServiceName = "uegnaudit";
    $strServiceMethod = "getProjectInfo";
    $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
    if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
        $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
        Bingo_Log::warning($strLog);
        return $this->errRet(Tieba_Errcode::CALL_DRS_SER_FAIL);
    }
    $arrData = $arrOutput['data'];
    $strWord = $arrData['highlight_word'];
    $intBid = intval($arrData['b_id']);
    $strProjectName = $arrData['name'];
    $arrDBDisplayField = (array)$arrData['display_field'];
    $arrDisplayField = array();
    foreach ($arrDBDisplayField as $item) {
        $arrDisplayField[$item['field']] = $item['display'];
    }

    $arrProjectGroup = $arrData['project_group'];

    if (empty($intTaskId) && empty($arrProjectGroup)) {
        $strLog = __CLASS__ . "::" . __FUNCTION__ . " current project group is empty. project_info :[" . serialize($arrData) . "]";
        Bingo_Log::warning($strLog);
        return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), "该项目所属用户组为空");
    }
    //针对未审数据获取打桩任务数据
    $arrPilngData = array();
    $intPilngCount = 0;
    if ($intIsAudited == 0) {
        $arrServiceInput = array(
            'project_id' => $intProjectId,
            'user_id' => $intLoginUserId,
            'status' => array(1,2),
        );
        $arrOutput = Tieba_Service::call("uegnaudit", "getPilingTaskOneByUidAndStatus", $arrServiceInput, null, null, 'post', null, 'utf-8');
        if(!empty($arrOutput['data']) && Tieba_Errcode::ERR_SUCCESS == $arrOutput["errno"]){
            $arrPilngData = $arrOutput['data']['list'];
            $intPilngCount = $arrOutput['data']['count'];
        }
    }
    if($intPilngCount > 0 && $intPilngCount <= $intPs ){
        $intPs  -= $intPilngCount;
    }

    if($intBid == 5){
        $intPs = 5;
    }
    // get List
    $arrServiceInput = array(
        'project_id' => $intProjectId,
        'task_id'    => $intTaskId,
        'is_audited' => $intIsAudited,
        'keyword'    => $strKeyWord,
        'task_op_uname' => $strTaskOpUname,
        'user_id' => $intLoginUserId,
        'data_uid'   => $intDataUid,
        'data_post_id' => $intDataPostId,
        'data_forum_id' => $intDataForumId,
        'data_thread_id' => $intDataThreadId,
        'data_feed_id' => $intDataFeedId,
        'data_group_id' => $intDataGroupId,
        'begin_time' => $intBeginTime,
        'end_time' => $intEndTime,
        'from'  => $intPn * $intPs,
        'size' => $intPs,
        'task_op_tag' => $intTagId,
        'task_op_parent_tag' => $intParentTagId,
        'hang_real_name' => $arrUserInfo['real_name']
    );

    $strServiceName = "uegnaudit";
    $strServiceMethod = "getTaskItemList";
    $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
    if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
        $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
        Bingo_Log::warning($strLog);
        return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
    }
    $arrTaskItemList = $arrOutput['data']['list'];
    $intAutoTaskId   = $arrOutput['data']['auto_task_id'];

    // 针对抽取未审核中过滤偶现出现的机审已审数据
    if ($intTaskId == 0 && $intIsAudited == 0) {
        $items = array();
        $data = array();
        $arr_Allocated_Audits = array();
        foreach ($arrTaskItemList as $key => $value) {
            if (($value['task_op_uid'] != 0 || $value['task_op_uname'] != '' || $value['review_op_tag'] != '') && $value['_source']['project_id'] != 0) {
                unset($arrTaskItemList[$key]);
                if ($value['task_op_uid'] == 1642266823) {
                    $items[] = array(
                        '_id'       => $value['_id'],
                        '_index'    => $value['_index'],
                    );
                }
                    //过滤非机审 但 已分配 已审核
                    if($value['task_op_uid'] != 1642266823 && $value['status'] == 1 && $value['is_audited'] == 1){
                        $arr_Allocated_Audits[] = $value['id'];
                    }
                    $data[] = $value;
                }
            }

        if (!empty($items) && count($items) > 0) {
            // 从ddbs中删除机审数据
            $arrServiceInput = array(
                'project_id' => $intProjectId,
                'items'      => $items,
                'status'     => array(
                    1,
                ),
            );
            $strServiceName = "uegnaudit";
            $strServiceMethod = "delTaskTodoItemByMd5";
            $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
            }
        }

        //从ddbs中更新 状态为 已分配 => 已审核
        if(!empty($arr_Allocated_Audits) && count($arr_Allocated_Audits) > 0){
            $arrServiceInput = array(
                'project_id'  => $intProjectId,
                'ids'          => $arr_Allocated_Audits,
            );
            $strServiceName = "uegnaudit";
            $strServiceMethod = "updateTaskTodoItemById";
            $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
            }
        }
    }

    $intDDBSCount = intval($arrOutput['data']['total']);

    if(!empty($arrPilngData) && !empty($arrTaskItemList)){
        // 2. ES获取相应数据
        $arrESIndexId2DDBSId = array();
        $arrDocs = array();
        foreach ($arrPilngData as $item) {
            $arrDocsItem = array(
                "_index" => $item['item_index'],
                "_type"  => Libs_Define_Es::ES_TYPE_POST,
                "_id"    => $item['item_id'],
            );
            $arrDocs[] = $arrDocsItem;
            $strKey = $item['item_index'] . '_' .  $item['item_id'];
            $arrESIndexId2DDBSId[$strKey] = $item['id'];
        }
        $arrServiceInput = array(
            'docs' => $arrDocs,
        );
        $strServiceName = "uegnaudit";
        $strServiceMethod = "getProjectItemListByMget";
        $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrProjectItemListInfo = $arrOutput['data'];
        foreach ($arrProjectItemListInfo['succ'] as &$item) {
            $strKey = $item['_index'] . '_' .  $item['_id'];
            $item['id'] = $arrESIndexId2DDBSId[$strKey];
        }
        $arrPilngItemData = $arrProjectItemListInfo['succ'];
        foreach ($arrPilngItemData as $value){
            array_push($arrTaskItemList,$value);
        }
        shuffle($arrTaskItemList);
        $intDDBSCount = count($arrTaskItemList);
    }

    $arrRetData = array(
        'list'         => self::_formatRetData($arrTaskItemList, $intPn * $intPs, $intDefaultTag, $intLoginUserId, $arrDisplayField, $strWord, $intBid),
        'total'        => $intDDBSCount,
        'auto_task_id' => intval($intAutoTaskId),
    );

    $arrRet = array(
        'data' => $arrRetData,
    );

    $intDataCount = intval($intDDBSCount) - count($arrPilngData);
    if($intDataCount > 0 && $intIsAudited == 0 ){          //获取数量（未审核）不统计计算打桩数据
        //数据组日志打点 【反作弊指标】 => 总获取数量
        Tieba_Stlog::setFileName('feye-stat');
        Tieba_Stlog::addNode('uip', intval(Bingo_Http_Ip::ip2long("*************")));
        $urlkey = 'uegnaudit-task-getTaskItemList';
        Tieba_Stlog::addNode('urlkey', $urlkey);
        Tieba_Stlog::addNode('obj_name', strval($arrUserInfo['user_name']));
        Tieba_Stlog::addNode('obj_param1', strval($arrUserInfo['real_name']));
        Tieba_Stlog::addNode('obj_id', strval($arrUserInfo['user_city']));
        Tieba_Stlog::addNode('obj_locate', $strProjectName);
        Tieba_Stlog::addNode('obj_to', $intProjectId);
        Tieba_Stlog::addNode('obj_src', $intBid);
        $strObjType = !empty($intTaskId) ? $intTaskId : 0;
        Tieba_Stlog::addNode('obj_type', $strObjType);
        Tieba_Stlog::addNode('obj_param2', $intDataCount);
        Tieba_Stlog::addNode('obj_param3', $intAutoTaskId);
        Tieba_Stlog::notice();
    }

    return $this->errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
}


/**
 * @param $arrData
 * @return mixed
 */
private static function  _formatRetData($arrData, $from, $intDefaultTag,  $intUserId, $arrDisplayField,$strWord,$intBid) {

    $bolHasPerm = Util_Perm_Perm::checkPerm($intUserId, Libs_Define_Permission::PERM_SERVER_NUM_PROJECT_DETAIL_FIELD);
    $i = 1;
    $arrRetData = array();
    foreach ($arrData as $key=>$item) {
        $intProjectId = intval($item['project_id']);
        $arrRetDataItem = $item;
        if (!isset($item['id'])) {
            $arrRetDataItem['id'] = $from + $i;
        }
        $intCreateTime = $item['create_time'];
        if(isset($item['train_id'])){
            if($key < 3){
                for ($j=1;$j<3;$j++){
                    if(isset($arrData[$j+$key]['project_id'])){
                        $intCreateTime = $arrData[$j+$key]['create_time'];
                        break;
                    }
                }
            }else{
                for ($j=1;$j<3;$j++){
                    if(isset($arrData[$key-$j]['project_id'])){
                        $intCreateTime = $arrData[$key-$j]['create_time'];
                        break;
                    }
                }
            }
        }
        $arrRetDataItem['create_time'] = date("Y-m-d H:i:s", $intCreateTime);

        if (! empty($arrRetDataItem['task_op_time'])) {
            $arrRetDataItem['task_op_time'] = date("Y-m-d H:i:s", $item['task_op_time']);
        }
        if (! empty($arrRetDataItem['review_op_time'])) {
            $arrRetDataItem['review_op_time'] = date("Y-m-d H:i:s", $item['review_op_time']);
        }

        if ($item['task_op_tag'] === 0 && $intDefaultTag) {
            $arrRetDataItem['task_op_tag'] = array($intDefaultTag);
        }
        $i ++;

        $arrPostDataTemp = $item['data'];
        $arrPostDataItem = array();
        foreach ($arrPostDataTemp as $postKey => $postValue) {
            if ($bolHasPerm) {
                if (isset($arrDisplayField[$postKey])) {
                    $arrPostDataItem[$postKey] = $postValue;
                }
            } else {
                if (isset($arrDisplayField[$postKey]) &&  $arrDisplayField[$postKey] == 1) {
                    $arrPostDataItem[$postKey] = $postValue;
                }
            }
            if(in_array($postKey,array('content','thread_content','frscontent','f_content'))){
                if(!empty($postValue)){
                    $arrPostDataItem[$postKey] = str_replace('static.tieba.baidu.com','tb1.bdstatic.com',$postValue);
                }
            }
        }
        if(in_array($intProjectId ,array(2044,2865 )) && !empty($arrPostDataItem['content'])){
            $arrPostDataItem['pinyin_content'] = Util_Project_Project::replaceHanConverPy($arrPostDataItem['content']);
        }
        if(isset($arrPostDataItem['user_icon']) && !empty($arrPostDataItem['user_icon'])){
            $arrPostDataItem['user_icon'] = str_replace('tb.himg.baidu.com','himg.baidu.com',$arrPostDataItem['user_icon']);
        }
        $strCommandNo = '';
        if(isset($arrPostDataTemp['command_no'])){
            $strCommandNo = intval($arrPostDataTemp['command_no']) == 1 ? '主题' : '回复';
        }
        if(isset($arrPostDataItem['quoteType'])){
            $arrPostDataItem['quoteType'] = $arrPostDataItem['quoteType'] > 0 ? '楼中楼' : $strCommandNo;
        }
        $arrRetDataItem['data'] = $arrPostDataItem;
        $arrRetData[] = $arrRetDataItem;
    }

    $strWord = empty($strWord) ? 'quanqugaoliangdc': $strWord.',quanqugaoliangdc';
    if($intBid == 5){
        $arrRetData = Util_Project_Project::multiReplaceLightFilterWord($strWord,$arrRetData);
    }else{
        $arrRetData = Util_Project_Project::replaceLightFilterWord($strWord,$arrRetData);
    }

    return $arrRetData;
}



}