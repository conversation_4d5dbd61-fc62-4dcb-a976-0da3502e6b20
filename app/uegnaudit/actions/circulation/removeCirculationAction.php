<?php

class removeCirculationAction extends Util_BaseAction
{
    /**
     * 获取数据
     * @return mixed
     */
    public function _getPrivateInfo()
    {
        $arrPrivateInfo['need_login']  = true;
        $arrPrivateInfo['check_login'] = true;
        $arrPrivateInfo['check_tbs']   = true;

        $arrPrivateInfo['id'] = intval($this->_getInput('id', 0));

        $this->_arrPageNeedPerm = array(Libs_Define_Permission::PERM_SERVER_CIRCULATION_REMOVE);
        return $arrPrivateInfo;
    }

    /**
     * 入参校验
     * @return bool
     */
    public function _checkPrivate()
    {
        $intId = $this->_objRequest->getPrivateAttr('id');

        if (empty($intId)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " param error .id:{ $intId }";
            Bingo_Log::warning($strLog);
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), "流转ID不能为空");
            return false;
        }

        return true;
    }

    /**
     * 主逻辑
     * @return bool
     */
    public function _execute()
    {
        $intLoginUserId = $this->_objRequest->getCommonAttr('user_id');
        $intId          = $this->_objRequest->getPrivateAttr('id');

        $arrInput         = array(
            'id'           => $intId,
            'create_uid'   => $intLoginUserId,
            'create_uname' => Util_Review_Review::getUNameByUIds($intLoginUserId),
        );
        $strServiceName   = "uegnaudit";
        $strServiceMethod = "removeCirculation";
        $arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrInput, null, null, 'post', null, 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call {$strServiceMethod} fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrRet = array(
            'data' => array(),
        );

        return $this->errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }
}