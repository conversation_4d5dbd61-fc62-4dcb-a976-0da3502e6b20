<?php
/**
 * Created by PhpStorm.
 * User: wangyang66
 * Date: 2018/8/2
 * Time: 下午3:52
 */

class getGroupListAction extends Util_BaseAction
{

/**
 * @return array
 */
public function _getPrivateInfo() {
    // 访客状态
    $arrPrivateInfo['need_login']  = true;
    $arrPrivateInfo['check_login'] = true;
    $arrPrivateInfo['check_tbs']   = true;

    $arrPrivateInfo['parent_id'] = intval($this->_getInput('parent_id', 0));

    $this->_arrPageNeedPerm  = array(Libs_Define_Permission::PERM_SERVER_NUM_USER);

    return $arrPrivateInfo;
}

/**
 * @return bool
 */
public function _checkPrivate() {

    return true;
}

/**
 * @return bool
 */
public function _execute() {
    $intUserId = $this->_objRequest->getCommonAttr('user_id');
    $intParentGroupId = $this->_objRequest->getPrivateAttr('parent_id');
    //$intUserId = 3430762071;
    // 获取用户信息
    $arrServiceInput = array(
        'user_id' => $intUserId,
    );

    $strServiceName = "uegnaudit";
    $strServiceMethod = "getUserOne";
    $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
    if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
        $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
        Bingo_Log::warning($strLog);
        return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
    }
    $arrUserInfo = $arrOutput['data'][0][0];
    $arrUserGroupRoleInfo = $arrOutput['data'][1];

    $arrServiceInput = array(
        'parent_id' => $intParentGroupId,
    );
    $strServiceName = "uegnaudit";
    $strServiceMethod = "getGroupListByParentId";
    $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
    if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
        $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
        Bingo_Log::warning($strLog);
        return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
    }
    $arrGroupList = $arrOutput['data']['list'];
    $arrGroupIds = array();
    if (intval($arrUserInfo['is_super']) != Libs_Define_Group::GROUP_TYPE_SUPER) {
        foreach ($arrUserGroupRoleInfo as $info){
            $intGroupId = intval($info['id']);
            $intParentId = intval($info['parent_id']);

            if($intParentId == 0 && $intParentGroupId == 0){
                $arrGroupIds[] = $intGroupId;
            }else{
                if ($intGroupId != $intParentGroupId){
                    $arrServiceInput = array(
                        'parent_id' => $intGroupId,
                    );
                    $strServiceName = "uegnaudit";
                    $strServiceMethod = "getGroupParents";
                    $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
                    if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                        $strLog = __CLASS__."::".__FUNCTION__." call uegnaudit::getGroupParents fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
                        Bingo_Log::warning($strLog);
                        return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                    }
                    foreach ($arrOutput['data'] as $value){
                        if($value['parent_id'] == $intParentGroupId){
                            $arrGroupIds[] = $value['id'];
                        }
                    }
                }
            }
        }
    }

    $arrRetData = array(
        'list' => self::_formatRetData($arrGroupList,$arrGroupIds),
    );
    $arrRetData['total'] = count($arrRetData['list']);
    
    $arrRet = array(
        'data' => $arrRetData,
    );

    return $this->errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
}

/**
 * @param $arrData
 * @return mixed
 */
private function _formatRetData($arrData,$arrGroupIds) {

    if (empty($arrGroupIds)) {
        return $arrData;
    }
    $arrRetData = array();
    foreach ($arrData as $key=>$item) {
        if(in_array($item['id'],$arrGroupIds)){
            $arrRetData[] = $item;
        }
    }

    return $arrRetData;
}



}