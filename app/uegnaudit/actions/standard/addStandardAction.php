<?php
/**
 * Created by PhpStorm.
 * User: wangyang66
 * Date: 2018/8/2
 * Time: 下午3:52
 */

class addStandardAction extends Util_BaseAction
{
/**
 * @return array
 */
public function _getPrivateInfo() {
    // 访客状态

    $arrPrivateInfo['need_login'] = true;
    $arrPrivateInfo['check_tbs'] = false;
    $arrPrivateInfo['check_login'] = false;
    $arrPrivateInfo['check_perm']  = false;

    $arrPrivateInfo['title'] = strval($this->_getInput('title', ''));
    $arrPrivateInfo['file_name'] = strval($this->_getInput('file_name', ''));
    $arrPrivateInfo['type'] = intval($this->_getInput('type', 0));
    $arrPrivateInfo['project_id'] = $this->_getInput('project_id', array());
    return $arrPrivateInfo;
}

/**
 * @return bool
 */
public function _checkPrivate() {
    $strTitle = $this->_objRequest->getPrivateAttr('title');
    $strFileName = $this->_objRequest->getPrivateAttr('file_name');
    $intType = $this->_objRequest->getPrivateAttr('type');
    $arrProjectId = $this->_objRequest->getPrivateAttr('project_id');

    if (empty($strTitle)) {
        $strLog = __CLASS__ . "::" . __FUNCTION__ . " param error title: $strTitle";
        Bingo_Log::warning($strLog);
        $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), "标题不能为空");
        return false;
    }

    $intStrLen = mb_strlen($strTitle);
    if ($intStrLen > Libs_Define_Role::ROLE_NAME_MAX) {
        $strLog = __CLASS__ . "::" . __FUNCTION__ . " param error title: $strTitle";
        Bingo_Log::warning($strLog);
        $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR , array(), "标题名称长度不应超过20个中文字符");
        return false;
    }

    if (empty($strFileName)) {
        $strLog = __CLASS__ . "::" . __FUNCTION__ . " param error file_name: $strFileName";
        Bingo_Log::warning($strLog);
        $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), "文件名称不能为空");
        return false;
    }
    if (empty($intType)) {
        $strLog = __CLASS__ . "::" . __FUNCTION__ . " param error type: $intType";
        Bingo_Log::warning($strLog);
        $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), "展示区域不能为空");
        return false;
    }
    if ($intType > 1 && empty($arrProjectId)) {
        $strLog = __CLASS__ . "::" . __FUNCTION__ . " param error project_id: $arrProjectId";
        Bingo_Log::warning($strLog);
        $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), "项目名称不能为空");
        return false;
    }
    return true;
}

/**
 * @return bool
 */
public function _execute() {
    $intUserId = $this->_objRequest->getCommonAttr('user_id');
    $strTitle = $this->_objRequest->getPrivateAttr('title');
    $strFileName = $this->_objRequest->getPrivateAttr('file_name');
    $intType = $this->_objRequest->getPrivateAttr('type');
    $arrProjectId = $this->_objRequest->getPrivateAttr('project_id');
    $strCreateUname = Util_User_User::getUNameByUIds($intUserId);

    $arrServiceInput = array(
        'title' => $strTitle,
    );
    $strServiceName = "uegnaudit";
    $strServiceMethod = "getStandardInfoByName";
    $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
    if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
        $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
        Bingo_Log::warning($strLog);
        return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
    }
    $arrStandardInfo = $arrOutput['data'];
    if (! empty($arrStandardInfo)) {
        return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array(), '已经有同名存在');
    }
    $arrServiceInput = array(
        'title' => $strTitle,
        'file_name' => $strFileName,
        'type' => $intType,
        'project_id' => implode(',',$arrProjectId),
        'create_uid' => $intUserId,
        'create_uname' => $strCreateUname,
    );

    $strServiceName = "uegnaudit";
    $strServiceMethod = "addStandard";
    $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
    if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
        $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
        Bingo_Log::warning($strLog);
        return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
    }

    $intId = $arrOutput['data'];

    $arrRetData = array(
        'id' => $intId,
    );

    $arrRet = array(
        'data' => $arrRetData,
    );

    return $this->errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
}


}