<?php

/**
 * Topic statistics
 */
class getTrainTaskItemStatAction extends Util_BaseAction
{
    /**
     * @return array
     */
    public function _getPrivateInfo()
    {
        // 访客状态
        $arrPrivateInfo['need_login']  = true;
        $arrPrivateInfo['check_login'] = true;
        $arrPrivateInfo['check_tbs']   = true;

        $arrPrivateInfo['pn']             = intval($this->_getInput('pn', 0));
        $arrPrivateInfo['ps']             = intval($this->_getInput('ps', 20));
        $arrPrivateInfo['train_id']       = intval($this->_getInput('train_id', 0));
        $arrPrivateInfo['begin_time']     = intval($this->_getInput('begin_time', 0));
        $arrPrivateInfo['end_time']       = intval($this->_getInput('end_time', 0));
        $arrPrivateInfo['data_post_id']   = intval($this->_getInput('data_post_id', 0));
        $arrPrivateInfo['data_thread_id'] = intval($this->_getInput('data_thread_id', 0));

        return $arrPrivateInfo;
    }

    /**
     * @return bool
     */
    public function _checkPrivate()
    {
        $intTrainId  = $this->_objRequest->getPrivateAttr('train_id');
        if (empty($intTrainId)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " param error";
            Bingo_Log::warning($strLog);
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), "项目必选项");
            return false;
        }
        return true;
    }

    /**
     * @return bool
     */
    public function _execute()
    {
        $intTrainId      = $this->_objRequest->getPrivateAttr('train_id');
        $intDataPostId   = $this->_objRequest->getPrivateAttr('data_post_id');
        $intDataThreadId = $this->_objRequest->getPrivateAttr('data_thread_id');
        $intBeginTime    = $this->_objRequest->getPrivateAttr('begin_time');
        $intEndTime      = $this->_objRequest->getPrivateAttr('end_time');
        $intPn           = $this->_objRequest->getPrivateAttr('pn');
        $intPs           = $this->_objRequest->getPrivateAttr('ps');

        if (($intPn + 1) * $intPs > Libs_Define_Project::PROJECT_LIST_TOTAL_ITEM_MAX) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " pn and ps is too large . pn is " . $intPn . ", ps is " . $intPs;
            Bingo_Log::warning($strLog);
            return $this->errRet(Tieba_Errcode::ERR_USER_NO_PERM, array(), "不允许查看超过10000条的数据, 请搜索查看");
        }
        $sortField = 'answer_total_num';
        $sortOrder = 'desc';

        // get List
        $arrServiceInput = array(
            'from'           => $intPn * $intPs,
            'size'           => $intPs,
            'train_id'       => $intTrainId,
            'begin_time'     => $intBeginTime,
            'end_time'       => $intEndTime,
            'sort_field'     => $sortField,
            'sort_order'     => $sortOrder,
            'data_post_id'   => $intDataPostId,
            'data_thread_id' => $intDataThreadId,
            //'is_audited'     => 1,
            'must_not'       => array('answer_total_num' => 0),
            '_source'        => 'create_time,answer_total_num,answer_succ_num,answer_op_tag,data.thread_id,data.post_id,data.command_no',
        );
        $strServiceName   = "uegnaudit";
        $strServiceMethod = "getProjectTrainItemList";
        $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrTaskItemList = $arrOutput['data']['list'];
        $intTotal        = $arrOutput['data']['total'];

        // Tag Info
        $arrServiceInput  = array(
            'train_id' => $intTrainId,
        );
        $strServiceMethod = "getTrainTagTree";
        $strServiceName   = "uegnaudit";
        $arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrTagInfoList = $arrOutput['data']['list'];
        $arrTagKeyName  = array();
        foreach($arrTagInfoList as $item){
            foreach($item['tag'] as $value){
                $arrTagKeyName[$value['id']] = $value['name'];
            }
        }
        // Project Info
        $arrTrainList = Util_Train_Train::getProjecTraintNameByIds(array($intTrainId));
        if (false === $arrTrainList) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Util_Train_Train::getProjecTraintNameByIds fail.";
            Bingo_Log::warning($strLog);
            return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrItem = array();
        foreach ($arrTaskItemList as $item){
            $intComNo                    = intval($item['data']['command_no']);
            $arrTemp['post_id']          = $item['data']['post_id'];
            $arrTemp['thread_id']        = $item['data']['thread_id'];
            $arrTemp['command_no']       = $intComNo == 0 ? '回复' : '主题';
            $arrTemp['create_time']      = date("Y-m-d H:i:s", $item['create_time']);
            $arrTemp['answer_total_num'] = intval($item['answer_total_num']);

            $arrTemp['train_id']       = $intTrainId;
            $arrTemp['train_name']     = $arrTrainList[$intTrainId];

            $arrTemp['answer_op_tag']    = array();
            foreach ($item['answer_op_tag'] as $value) {
                $arrTemp['answer_op_tag'][] = $arrTagKeyName[$value];
            }
            $arrTemp['answer_op_tag'] = implode(",", $arrTemp['answer_op_tag']);

            if ($item['answer_total_num'] == 0 || $item['answer_succ_num'] == 0) {
                $arrTemp['accuracy'] = 0;
            } else {
                $arrTemp['accuracy'] = sprintf("%.2f", ($item['answer_succ_num'] / $item['answer_total_num'] * 100));
            }
            $arrItem[] = $arrTemp;
        }
        $arrRetData = array(
            'list'  => $arrItem,
            'total' => $intTotal,
        );
        $arrRet = array(
            'data' => $arrRetData,
        );
        return $this->errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }
}