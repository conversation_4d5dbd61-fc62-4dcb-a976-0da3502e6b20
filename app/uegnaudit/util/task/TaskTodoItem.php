<?php
/**
 * Created by PhpStorm.
 * User: liujiaxiang01
 * Date: 2018/11/14
 * Time: 19:54
 */

class Util_Task_TaskTodoItem {

    /**
     * @param int
     * @return int | bool
     */
    public static function getUnauditItemCount($intProjectId) {
        if (empty($intProjectId)) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. project_id=$intProjectId";
            Bingo_Log::warning($strLog);
            return false;
        }

        $arrServiceInput = array(
            'project_id' => $intProjectId,
        );
        $arrOutput = Tieba_Service::call('uegnaudit', 'getUnauditItemCount', $arrServiceInput, null, null, 'post', null, 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call uegnaudit getUnauditItemCount fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return false;
        }

        $intUnAudtiCount = intval($arrOutput['data']['count']);
        return $intUnAudtiCount;
    }

    /**
     * @param array
     * @return int
     */
    public static function getOffset($intProjectId) {
        Bingo_Timer::start(__FUNCTION__);
        if (empty($intProjectId) || !is_numeric($intProjectId)) {
            return false;
        }

        $strKey = Libs_Define_Project::PROJECT_OFFSET_PREFIX . $intProjectId;
        $incr = Util_Redis_Redis::incrKey($strKey);
        if ($incr === false) {
            Bingo_Log::warning("incr offset fail, key=$strKey");
            return 0;
        }

        $intIncr = intval($incr);
        Bingo_Log::pushNotice('offset_incr', $intIncr);
        if ($intIncr <= 0) {
            return 0;
        }

        $arrDBInput = array(
            'project_id' => $intProjectId,
        );
        Bingo_Timer::start('getAuditParallelism');
        $arrOutput = Dl_Uegnaudit_Task::getAuditParallelism($arrDBInput);
        Bingo_Timer::end('getAuditParallelism');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call Dl_Uegnaudit_Task::getAuditParallelism fail. input:[".serialize($arrDBInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return 0;
        }

        $intParallelism = intval($arrOutput['data']['count']);
        Bingo_Log::pushNotice('offset_parallelism', $intParallelism);
        $intOffset = 0;
        if ($intParallelism > 0) {
            $intOffset = $intIncr % $intParallelism;
        }

        Bingo_Timer::end(__FUNCTION__);
        return ($intOffset);
    }

}