<?php

class Util_Es_Es
{

    /**
     *
     * @param
     * null
     * @return true
     */
    public static function mapping()
    {
        $intNowTime = Bingo_Timer::getNowTime();
        // prev 7 days, future 7 days
        for ($i = - 7; $i <= 7; $i ++) {
            $intTmpTime = $intNowTime + ($i * 86400);
            $strTmpDate = self::getEsDate($intTmpTime);

            if ($strTmpDate === false) {
                continue;
            }

            // post mapping
            $arrMapping = array(
                'settings' => array(
                    'number_of_shards' =>  1
                ),
                'mappings' => array(
                    Libs_Define_Es::ES_TYPE_POST => array(
                        'properties' => Libs_Define_Es::$arrEsTiebaUegnauditPostMapping
                    )
                )
            );
            $strIndex = Libs_Define_Es::ES_INDEX_UEGNAUDIT_POST_INFO . '_' . $strTmpDate;
            self::_putnew('/' . $strIndex, $arrMapping, Libs_Define_Es::ES_MAPPING_TIMEOUT);

            // statistics mapping
            $arrStatisticsMapping = array(
                'settings' => array(
                    'number_of_shards' =>  1
                ),
                'mappings' => array(
                    Libs_Define_Es::ES_TYPE_STATISTICS => array(
                        'properties' => Libs_Define_Es::$arrEsTiebaUegnauditStatisticsMapping
                    )
                )
            );
            $strStatisticsIndex = Libs_Define_Es::ES_INDEX_UEGNAUDIT_STATISTICS_INFO . '_' . $strTmpDate;
            self::_putnew('/' . $strStatisticsIndex, $arrStatisticsMapping, Libs_Define_Es::ES_MAPPING_TIMEOUT);

            // review mapping
            $arrReviewMapping = array(
                'settings' => array(
                    'number_of_shards' =>  1,
                    'refresh_interval' =>  '500ms'
                ),
                'mappings' => array(
                    Libs_Define_Es::ES_TYPE_REVIEW => array(
                        'properties' => Libs_Define_Es::$arrEsTiebaUegnauditReviewMapping
                    )
                )
            );
            $strReviewIndex = Libs_Define_Es::ES_INDEX_UEGNAUDIT_REVIEW_INFO . '_' . $strTmpDate;
            self::_putnew('/' . $strReviewIndex, $arrReviewMapping, Libs_Define_Es::ES_MAPPING_TIMEOUT);

        }

        //删除180天之前的数据
        self::mapping_del();

        $strTmpDateMonth = self::getEsDateMonth($intNowTime);
        // train mapping
        $arrTrainMapping = array(
            'settings' => array(
                'number_of_shards' =>  1
            ),
            'mappings' => array(
                Libs_Define_Es::ES_TYPE_POST => array(
                    'properties' => Libs_Define_Es::$arrEsTiebaUegnauditTrainMapping
                )
            )
        );
        $strTrainIndex = Libs_Define_Es::ES_INDEX_UEGNAUDIT_TRAIN_INFO . '_' . $strTmpDateMonth;
        if($strTmpDateMonth){
            self::_putnew('/' . $strTrainIndex, $arrTrainMapping, Libs_Define_Es::ES_MAPPING_TIMEOUT);
        }
        return true;
    }

    public static function mapping_del(){
        //delete 360~180 days
        $intNowTime = Bingo_Timer::getNowTime();
        $intTmpTime = $intNowTime - (180 * 86400);
        $strTmpDate = self::getEsDate($intTmpTime);
        if ($strTmpDate === false) {
            return false;
        }
        // post
        $strIndex = Libs_Define_Es::ES_INDEX_UEGNAUDIT_POST_INFO . '_' . $strTmpDate;
        self::_delete('/' . $strIndex, Libs_Define_Es::ES_MAPPING_TIMEOUT);
        // statistics
        $strStatisticsIndex = Libs_Define_Es::ES_INDEX_UEGNAUDIT_STATISTICS_INFO . '_' . $strTmpDate;
        self::_delete('/' . $strStatisticsIndex, Libs_Define_Es::ES_MAPPING_TIMEOUT);
        //review
        $strReviewIndex = Libs_Define_Es::ES_INDEX_UEGNAUDIT_REVIEW_INFO . '_' . $strTmpDate;
        self::_delete('/' . $strReviewIndex, Libs_Define_Es::ES_MAPPING_TIMEOUT);

        return true;
    }

    /**
     *
     * @param {Int} $intTime            
     * @return {String} $strDate
     */
    public static function getEsDate($intTime)
    {
        $strDate = strval(date('Y.m.d', $intTime));
        if (strlen($strDate) == 0) {
            return false;
        }
        return $strDate;
    }

    public static function getEsDateMonth($intTime)
    {
        $strDate = strval(date('Y.m', $intTime));
        if (strlen($strDate) == 0) {
            return false;
        }
        return $strDate;
    }

    /**
     *
     * @param {Int} $intTime
     * @return {String} $strDate
     */
    public static function getEsDateSuffix($strIndex)
    {
        $strDate = end(explode('_', $strIndex));
        if (strlen($strDate) == 0) {
            return false;
        }
        return $strDate;
    }

    /**
     *
     * @param {String} $strUri
     * @param {Array} $arrJson
     * @param {Int} $intTime
     *new_es (6.4)
     * @return {String} $ret
     */
    private static function _putnew($strUri, $arrJson, $intTimeOutMs)
    {
        $strHost = Libs_Define_Es::ES_UEGHOST;
        $strPort = Libs_Define_Es::ES_PORT;
        $strUrl = "http://{$strHost}:{$strPort}{$strUri}";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $strUrl);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($ch, CURLOPT_USERPWD, Libs_Define_Es::ES_UEGSUPERUSER);
        curl_setopt($ch, CURLOPT_NOSIGNAL, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT_MS, $intTimeOutMs);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($arrJson));
        $ret = curl_exec($ch);
        $intHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($intHttpCode !== 200 && $intHttpCode !== 201) {
            return false;
        }
        return $ret;
    }
    /**
     *
     * @param {String} $strUri            
     * @param {Int} $intTime
     *
     * @return {String} $ret
     */
    private static function _delete($strUri, $intTimeOutMs)
    {
        $strHost = Libs_Define_Es::ES_UEGHOST;
        $strPort = Libs_Define_Es::ES_PORT;
        $strUrl = "http://{$strHost}:{$strPort}{$strUri}";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $strUrl);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($ch, CURLOPT_USERPWD, Libs_Define_Es::ES_UEGSUPERUSER);
        curl_setopt($ch, CURLOPT_NOSIGNAL, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT_MS, $intTimeOutMs);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
        $ret = curl_exec($ch);
        $intHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($intHttpCode !== 200 && $intHttpCode !== 201) {
            return false;
        }
        return $ret;
    }

    public static function _diffMonth($intBeginTime,$intEndtime){
        $arrData = array();
        $beginDate = date('Y.m',$intBeginTime);
        $endDate = date('Y.m',$intEndtime);
        $brr = explode('.',$beginDate);
        $err = explode('.',$endDate);
        if($err[0] - $brr[0] == 0)
        {
            $num = $err[1]-$brr[1]+1;
        }else if($err[0]-$brr[0] == 1){
            $num = 12-$brr[1]+1+$err[1];
        }else{
            $num = 12*($err[0]-$brr[0])-$brr[1]+$err[1];
        }

        if($brr[0] == $err[0] && $brr[1] == $err[1])
        {
            $arrData[] = $beginDate."*";
        }else{
            for($i = 0 ; $i < $num; $i++)
            {
                $month = date('m',$intBeginTime);
                $year = date('Y',$intBeginTime);
                $month = $month+$i;
                if($month > 12)
                {
                    if($month%12 == 0)
                    {
                        $year = $year + floor($month/12-1)*1;
                        $month = $month-12*(floor($month/12)-1);
                    }else{
                        $year = $year + floor($month/12)*1;
                        $month = $month-12*floor($month/12);
                    }
                }
                $month = str_pad($month,2,"0",STR_PAD_LEFT)."*";
                array_push($arrData,$year.'.'.$month);
            }
        }
        return $arrData;
    }

}
