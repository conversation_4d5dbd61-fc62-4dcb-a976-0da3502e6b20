<?php

define('ROOT_PATH', '/home/<USER>/orp');
ini_set("memory_limit", -1);
Tieba_Init::init('uegnaudit');


/**
 * @param $strClassName
 * @return null
 */
function __autoload($strClassName)
{
    $strNewClassName = str_replace('_', '/', $strClassName . ".php");
    $arrClass = explode('/', $strNewClassName);
    $intPathLen = count($arrClass);
    $strLastName = $arrClass [$intPathLen - 1];
    $strTmp = strtolower($strNewClassName);
    $intPreLen = strlen($strTmp) - strlen($strLastName);
    $strNewClassName = substr($strTmp, 0, $intPreLen) . $strLastName;
    $strClassPath = ROOT_PATH . '/app/' . APP_NAME . '/' . $strNewClassName;
    require_once $strClassPath;
}


class recallData
{
    const UEGNAUDIT_RECALL_PRIFIX = "uegnaudit_recall_";
    const UEGNAUDIT_RECALL_INCR_PRIFIX = "uegnaudit_recall_incr_";
    const PROJECT_LOCK_SUCCESS = 1;
    const INTERVAL = 3600;
    const SIZE     = 100;
    const SCROLL   = '1m'; //scroll 时长

    /**
     * @param array
     * @return bool
     */
    public static function run() {
        $arrServiceInput = array(
            'offset' => 0,
            'limit'  => self::SIZE,
            'status' => Libs_Define_Project::PROJECT_STATUS_RECALL,
        );
        $strServiceName = "uegnaudit";
        $strServiceMethod = "getProjectList";
        $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
        }

        $arrProjectList = (array)$arrOutput['data']['list'];
        foreach ($arrProjectList as $item) {
            $intProjectId = $item['id'];

            $arrServiceInput = array(
                'id' => $intProjectId,
            );
            $strServiceName = "uegnaudit";
            $strServiceMethod = "getProjectInfo";
            $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return false;
            }

            $arrProjectInfo = $arrOutput['data'];
            self::recall($arrProjectInfo);
        }
    }

    /**
     * @param array
     * @return bool
     */
    public static function recall($arrInput) {
        $intProjectId = $arrInput['id'];
        $arrProjectInflow = $arrInput['project_inflow'];

        $arrInflowProjectId = array();
        $arrInflowTagId = array();
        foreach ($arrProjectInflow as $item) {
            $arrInflowProjectId[] = $item['project_id'];
            $arrInflowTagId = array_merge($arrInflowTagId, $item['tag']);
        }

        if ( !self::lockProject($intProjectId)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " project has locked. project_id=$intProjectId";
            Bingo_Log::notice($strLog);
            return true;
        }

        $arrRecallInfo = self::getRecallInfo($intProjectId, $arrInflowProjectId);
        if ($arrRecallInfo == false) {
            self::unlockProject($intProjectId);
            return true; // 继续处理下一个 project
        }

        $intStartTime = intval($arrRecallInfo['start_time']);
        $intEndTime = intval($arrRecallInfo['end_time']);
        $intCurrentTime = intval($arrRecallInfo['current_time']);
        if ($intCurrentTime < $intStartTime) {
            $intCurrentTime = $intStartTime;
        }

        for (; $intCurrentTime <= $intEndTime; $intCurrentTime += 86400) {
            $arrIndexInput = array(
                'current_time' => $intCurrentTime,
                'project_id' => $intProjectId,
                'inflow_project_id' => $arrInflowProjectId,
                'inflow_tag_id' => $arrInflowTagId,
            );
            $boolRes = self::recallIndex($arrIndexInput);
            if ($boolRes == false) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " recallIndex false. project_id=$intProjectId";
                Bingo_Log::notice($strLog);
                break;
            }
        }

        if ($intCurrentTime > $intEndTime) {
            $arrServiceInput = array(
                'id' => $intProjectId,
                'status' => Libs_Define_Project::PROJECT_STATUS_ON,
            );
            $arrOutput = Tieba_Service::call('uegnaudit', 'updateProjectStatus', $arrServiceInput, null, null, 'post', null, 'utf-8');
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call uegnaudit updateProjectStatus fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
            }
            self::delRecallInfo($intProjectId);
        } else {
            $intCurrentTime -= 86400;
            $arrRecallInfo['current_time'] = $intCurrentTime;
            $boolRes = self::setRecallInfo($arrRecallInfo);
            if ($boolRes == false) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " setRecallInfo false. recall_info" . serialize($arrRecallInfo);
                Bingo_Log::warning($strLog);
            }
        }

        self::unlockProject($intProjectId);
        return true;
    }

    /**
     * @param array
     * @return bool
     */
    public static function recallIndex($arrInput) {
        $intCurrentTime = $arrInput['current_time'];
        $intProjectId = $arrInput['project_id'];
        $arrInflowProjectId = $arrInput['inflow_project_id'];
        $arrInflowTagId = $arrInput['inflow_tag_id'];

        $strIndexSuffix = Util_Es_Es::getEsDate($intCurrentTime);
        $strIndex = Libs_Define_Es::ES_INDEX_UEGNAUDIT_POST_INFO;
        $strIndex .= "_$strIndexSuffix";

        $arrScrollInput = array(
            'index' => $strIndex,
            'project_id' => $arrInflowProjectId,
            'task_op_tag' => $arrInflowTagId,
        );
        $arrOutput = self::getFirstScroll($arrScrollInput);
        if ($arrOutput == false) {
            $strLog = __CLASS__."::".__FUNCTION__." getFirstScroll fail. input:[".serialize($arrScrollInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return false;
        }

        $strScrollId = $arrOutput['scroll_id'];
        $arrScrollData = $arrOutput['data'];
        while ( !empty($arrScrollData)) {
            $arrData = array();
            foreach ($arrScrollData as $item) {
                $arrData[] = $item['_source']['data'];
            }

            $arrServiceInput = array(
                'project_id' => $intProjectId,
                'datas'  => $arrData,
            );
            $arrOutput = Tieba_Service::call('uegnaudit', 'mImportPostData', $arrServiceInput, null, null, 'post', null, 'utf-8');
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call uegnaudit TransferDataDealer fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::warning($strLog);
                return false;
            }

            $arrScrollInput = array(
                'scroll_id' => $strScrollId,
            );
            $arrOutput = self::getNextScroll($arrScrollInput);
            if ($arrOutput == false) {
                $strLog = __CLASS__."::".__FUNCTION__." getNextScroll fail. input:[".($arrScrollInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
                return false;
            }

            $strScrollId = $arrOutput['scroll_id'];
            $arrScrollData = $arrOutput['data'];
        }

        return true;
    }

    /**
     * @param array
     * @return bool: true: lock success
     */
    public static function lockProject($intProjectId) {
        if (empty($intProjectId) || !is_numeric($intProjectId)) {
            return false;
        }

        $strKey = self::UEGNAUDIT_RECALL_INCR_PRIFIX . $intProjectId;
        $lock = Util_Redis_Redis::incrKey($strKey);
        if ($lock == self::PROJECT_LOCK_SUCCESS) {
            return true;
        }

        return false;
    }

    /**
     * @param array
     * @return bool: true: unlock success
     */
    public static function unlockProject($intProjectId) {
        if (empty($intProjectId) || !is_numeric($intProjectId)) {
            return false;
        }

        $strKey = self::UEGNAUDIT_RECALL_INCR_PRIFIX . $intProjectId;
        return Util_Redis_Redis::delKv($strKey);
    }

    /**
     * @param int
     * @param array
     * @return array | bool
     */
    public static function getRecallInfo($intProjectId, $arrInflowProjectId) {
        $strKey = self::UEGNAUDIT_RECALL_PRIFIX . $intProjectId;
        $strValue = Util_Redis_Redis::getKv($strKey);
        if ($strValue == false) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " get recall info not exist. project_id=$intProjectId";
            Bingo_Log::warning($strLog);
        }

        if (empty($strValue)) {
            $arrTime = self::getTimeBorder($arrInflowProjectId);
            $arrRecallInfo = array(
                'project_id' => $intProjectId,
                'start_time' => $arrTime['min'],
                'end_time' => $arrTime['max'],
                'current_time' => 0,
            );

            return $arrRecallInfo;
        }

        $arrRecallInfo = json_decode($strValue, true);
        return $arrRecallInfo;
    }

    /**
     * @param array
     * @return array | bool
     */
    public static function setRecallInfo($arrInput) {
        if (empty($arrInput['start_time']) || empty($arrInput['end_time']) ||
            empty($arrInput['current_time']) || empty($arrInput['project_id'])) {
            return false;
        }

        $intProjectId = $arrInput['project_id'];

        $strKey = self::UEGNAUDIT_RECALL_PRIFIX . $intProjectId;
        $strValue = json_encode($arrInput);

        $arrRedisInput = array(
            'key' => $strKey,
            'value' => $strValue,
        );
        $res = Util_Redis_Redis::setKv($arrRedisInput);
        if ($res == false) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " set recall info error. input=" . serialize($arrRedisInput);
            Bingo_Log::warning($strLog);
            return false;
        }

        return true;
    }

    /**
     * @param array
     * @return array | bool
     */
    public static function delRecallInfo($intProjectId) {
        $strKey = self::UEGNAUDIT_RECALL_PRIFIX . $intProjectId;

        $res = Util_Redis_Redis::delKv($strKey);
        if ($res == false) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " clear recall info error. input=$strKey";
            Bingo_Log::warning($strLog);
            return false;
        }

        return true;
    }


    /**
     * @param array
     * @return bool | array
     */
    public static function getTimeBorder($arrProjectId) {
        $arrRequest = array (
            'size' => 0,
            'query' => array(
                'bool' => array(
                    'filter' => array(
                        array('terms' => array('project_id' => $arrProjectId)),
                        array('term' => array('is_audited' => Libs_Define_Item::ITEM_STATUS_AUDITED))
                    )
                ),
            ),
            'aggs' => array(
                'task_op_time_min' => array('min' => array('field' => 'task_op_time')),
                'task_op_time_max' => array('max' => array('field' => 'task_op_time')),
            )
        );
        $strIndex = Libs_Define_Es::ES_INDEX_UEGNAUDIT_POST_INFO . '_*';
        $arrEsInput = array(
            'index' => $strIndex,
            'type' => Libs_Define_Es::ES_TYPE_POST,
            'request' => $arrRequest,
        );

        Bingo_Log::notice('es_input=' . json_encode($arrEsInput));
        $arrOutput = Dl_Uegnaudit_Statistics::query($arrEsInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Uegnaudit_Statistics::query fail. input:[" . serialize($arrEsInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return false;
        }

        $intTaskOpTimeMin = intval($arrOutput['data']['aggregations']['task_op_time_min']['value']);
        $intTaskOpTimeMax = intval($arrOutput['data']['aggregations']['task_op_time_max']['value']);

        $arrData = array(
            'min' => $intTaskOpTimeMin,
            'max' => $intTaskOpTimeMax,
        );

        return $arrData;
    }

    /**
     * @param array
     * @return array | bool
     */
    public static function getFirstScroll($arrInput) {
        if (empty($arrInput['project_id']) || !is_array($arrInput['project_id']) ||
            empty($arrInput['task_op_tag']) || !is_array($arrInput['task_op_tag']) ||
            empty($arrInput['index'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return false;
        }

        $arrProjectId = $arrInput['project_id'];
        $arrTagId = $arrInput['task_op_tag'];
        $strIndex = $arrInput['index'];

        $arrRequest = array(
            'size' => self::SIZE,
            'sort' => array('_doc'),
            'query' => array(
                'bool' => array(
                    'must' => array(
                        array('terms' => array('project_id' => $arrProjectId)),
                        array('terms' => array('task_op_tag' => $arrTagId))
                    )
                )
            )
        );

        $strType = Libs_Define_Es::ES_TYPE_POST;
        $interval = self::SCROLL;
        $strPathInfo = "/$strIndex/$strType/_search?scroll=$interval";
        $arrHeader = array(
            "pathinfo" => $strPathInfo,
            "content-type" => 'text/plain',
        );

        $strParams = json_encode($arrRequest);
        Bingo_Log::notice("pathinfo=$strPathInfo, es_input=$strParams");
        Bingo_Timer::start(__FUNCTION__);
        $arrOutput = Tieba_Ral::call("tieba_es", "post", $strParams, rand(), $arrHeader);
        Bingo_Timer::end(__FUNCTION__);
        if (empty($arrOutput)) {
            $strLog = __CLASS__."::".__FUNCTION__." Tieba_Ral::call fail. input:[".($strParams)."]; header:[" . serialize($arrHeader) . "]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return false;
        }

        $arrOutput = json_decode($arrOutput, true);
        if ($arrOutput['timed_out'] || $arrOutput['status'] == 404) {
            $strLog = __CLASS__."::".__FUNCTION__." request es fail. input:[$strParams]; header:[" . serialize($arrHeader) . "]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return false;
        }

        $arrData = array(
            'scroll_id' => $arrOutput['_scroll_id'],
            'data' => $arrOutput['hits']['hits'],
        );

        return $arrData;
    }

    /**
     * @param array
     * @return array | bool
     */
    public static function getNextScroll($arrInput) {
        if (empty($arrInput['scroll_id'])) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return false;
        }

        $strScroll = self::SCROLL;
        $strScrollId = $arrInput['scroll_id'];

        $arrRequest = array(
            'scroll' => $strScroll,
            'scroll_id' => $strScrollId,
        );

        $strPathInfo = "/_search/scroll";
        $arrHeader = array(
            "pathinfo" => $strPathInfo,
            "content-type" => 'text/plain',
        );

        $strParams = json_encode($arrRequest);
        Bingo_Log::notice("pathinfo=$strPathInfo, es_input=$strParams");
        Bingo_Timer::start(__FUNCTION__);
        $arrOutput = Tieba_Ral::call("tieba_es", "post", $strParams, rand(), $arrHeader);
        Bingo_Timer::end(__FUNCTION__);
        if (empty($arrOutput)) {
            $strLog = __CLASS__."::".__FUNCTION__." Tieba_Ral::call fail. input:[".($strParams)."]; header:[" . serialize($arrHeader) . "]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return false;
        }

        $arrOutput = json_decode($arrOutput, true);
        if ($arrOutput['timed_out'] || $arrOutput['status'] == 404) {
            $strLog = __CLASS__."::".__FUNCTION__." request es fail. input:[$strParams]; header:[" . serialize($arrHeader) . "]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return false;
        }

        $arrData = array(
            'scroll_id' => $arrOutput['_scroll_id'],
            'data' => $arrOutput['hits']['hits'],
        );

        return $arrData;
    }

}

echo "begin\n";
recallData::run();
echo "end\n";
