<?php
/**
 * Created by PhpStorm.
 * User: liujiaxiang01
 * Date: 9/25/18
 * Time: 17:04
 */

class Libs_Define_Dealer {

    const DEALER_SERVICE_NAME = 'uegnaudit';

    const DEALER_NAME_PUNISHMENT = 'punishment'; // 处置
    const DEALER_NAME_TRANSFER   = 'transfer';   // 流转
    const DEALER_NAME_STATISTICS = 'statistics'; // 统计


    const DEALER_METHOD_NAME_TRANSFER        = 'TransferDataDealer';
    const DEALER_METHOD_NAME_DELETE_THREAD   = 'DeleteThreadDealer';
    const DEALER_METHOD_NAME_RECOVER_THREAD  = 'RecoverThreadDealer';
    const DEALER_METHOD_NAME_BLOCK_MASK_USER = 'BlockAndMaskUserDealer';
    const DEALER_METHOD_NAME_BLOCK_USER      = 'BlockUserDealer';
    const DEALER_METHOD_NAME_UNBLOCK_USER    = 'UnBlockUserDealer';
    const DEALER_METHOD_NAME_SEND_MESSAGE    = 'SendMessageDealer';
    const DEALER_METHOD_NAME_BULK            = 'BulkDealer';

    public static $arrFeedTagDealer = array(
        '11'  =>  '不通过',
        '9'   =>  '低质',
        '6'   =>  '自见',
        '5'   =>  '加热',
        '4'   =>  '精选',
        '3'   =>  '推荐',
        '2'   =>  '普通',
        '100' =>  '服装',
        '179' =>  '帽子',
        '103' =>  '鞋',
        '106' =>  '韩风',
        '109' =>  '潮牌',
        '112' =>  '设计师',
        '115' =>  '穿搭分享',
        '102' =>  '包包',
        '105' =>  '极简',
        '108' =>  '欧美',
        '111' =>  '淘宝',
        '114' =>  '穿搭资讯',
        '101' =>  '配饰',
        '181' =>  '古着',
        '104' =>  '街头',
        '107' =>  '日系',
        '110' =>  '快时尚',
        '113' =>  '穿搭评测',
        '129' =>  '旅行vlog',
        '180' =>  '酒店',
        '149' =>  '风景',
        '47'  =>  '美食制作',
        '50'  =>  '吃播',
        '119' =>  '甜点',
        '173' =>  '饮品',
        '49'  =>  '美食探店',
        '118' =>  '减肥',
        '171' =>  '美食测评',
        '48'  =>  '美食展示',
        '117' =>  '方便食品',
        '150' =>  '零食',
        '182' =>  '试吃',
        '124' =>  '猫',
        '127' =>  '宠物用品',
        '126' =>  '卖萌',
        '125' =>  '狗',
        '128' =>  '调戏',
        '31'  =>  'cosplay',
        '116' =>  'lolita',
        '34'  =>  'jk制服',
        '33'  =>  '汉服',
        '120' =>  '健身',
        '123' =>  '舞蹈',
        '122' =>  '足球',
        '121' =>  '篮球',
        '188' =>  '滑板',
        '161' =>  '翻唱',
        '187' =>  '音乐制作',
        '186' =>  '弹唱',
        '162' =>  '演奏',
        '152' =>  '香薰',
        '155' =>  '3C',
        '158' =>  '收纳',
        '154' =>  '手机壳',
        '157' =>  '摆件',
        '183' =>  '杯子',
        '153' =>  '手帐',
        '156' =>  '乐高',
        '159' =>  '玩具',
        '87'  =>  '护肤',
        '177' =>  '身体护理',
        '90'  =>  '美甲',
        '93'  =>  '韩妆',
        '96'  =>  '美妆测评',
        '99'  =>  '护肤科普',
        '176' =>  '美瞳',
        '89'  =>  '发型',
        '92'  =>  '日妆',
        '95'  =>  '仿妆',
        '98'  =>  '美妆分享',
        '175' =>  '香水',
        '88'  =>  '彩妆',
        '91'  =>  '欧美妆',
        '94'  =>  '国货',
        '97'  =>  '妆容教程',
        '174' =>  '美妆工具',
        '164' =>  '校园',
        '167' =>  '写真',
        '170' =>  '画画',
        '166' =>  '摄影',
        '169' =>  'vlog',
        '185' =>  '探店',
        '165' =>  '高校',
        '168' =>  '学习',
        '184' =>  '手工',
        '190' =>  '男',
        '197' =>  '中性',
        '191' =>  '女',
        '200' =>  '航司',
        '202' =>  '风土人情',
        '201' =>  '景点',
        '205' =>  'hip-pop',
        '204' =>  'mv',
        '203' =>  '音乐现场',
        '206' =>  '电音',
        '229' =>  '明星',
        '233' =>  '动漫',
        '236' =>  '整蛊',
        '239' =>  '搞笑',
        '232' =>  '综艺',
        '235' =>  '脱口秀',
        '238' =>  '鬼畜',
        '231' =>  '影视',
        '234' =>  '自制剧',
        '237' =>  '配音',
        '240' =>  '游戏',
        '222' =>  '搬运',
        '198' =>  '情感',
        '227' =>  '大头照',
        '199' =>  '经历分享',
        '163' =>  '日常',
    );
}