<?php
/***************************************************************************
 * 
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Review.php
 * @date 2018/08/22 19:29:07
 * @brief 
 *  
 **/

class Libs_Define_Review {

    // 改状态保存在 ddbs 中, 表示每条记录的状态
    const ITEM_STATUS_NOT_REVIEW            = 0; // 未质检
    const ITEM_STATUS_REVIEW                = 1; // 已质检
    const ITEM_STATUS_REVIEW_OR_NOT_REVIEW  = 3; // 未质检&&已质检

    const ITEM_TAG_REVIEW_NORMAL        = 0; // 正常
    const ITEM_TAG_REVIEW_NOT_NORMAL    = 1; // 作弊

    // 改状态保存在 db 中, 表示每条任务的状态
    const REVIEW_STATUS_REVIEW_CREATING = 0; // 创建中
    const REVIEW_STATUS_REVIEW_AUDITING = 1; // 质检中
    const REVIEW_STATUS_REVIEW_DONE     = 2; // 已完成
    const REVIEW_STATUS_REVIEW_RELEASED = 3; // 已释放
    const REVIEW_STATUS_CONFIRMING      = 4; // 创建确认中

    public static $arrStatusInfo = array(
        0 => '创建中',
        1 => '质检中',
        2 => '已完成',
        3 => '已释放',
        4 => '创建确认中',
    );
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
