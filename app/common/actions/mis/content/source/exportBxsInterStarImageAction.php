<?php
/**
 * content source ui
 * 输入query名字 将特定接口里该query下的图片打包下载到本地
 */
define("BINGO_ENCODE_LANG", 'utf-8');
define("TMP_PATH",dirname(__file__)."/tmp/");
define("IQIYI_PHOTO_API","http://expand.video.iqiyi.com/baidu/idol/idol_personal_photo_add_01.json");

class exportBxsInterStarImageAction extends Util_Action{
    protected $bolOnlyAccessAmis = false;
    protected $bolOnlyInner = false;
    
 
    /**
     * @param bool
     * @return bool
     */
    public function _execute(){

        try {
            //参数获取
            $strQuery = strval(Bingo_Http_Request::get('query',''));//网红名称
            $intTmp = time();
            $intOpUid = intval(Util_User::$intUserId);
            $strOpUname = strval(Util_User::$strUserName);
            if($intOpUid <= 0 || empty($strOpUname)){
                Bingo_Log::warning('exportBxsSourceInfoAction invalid login.OpUid:['.$intOpUid.'] opuname:['.$strOpUname.']');
                return $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN, '用户未登录，请先登录。');
            }
            $arrOutput = array();
            $res = $this->fetchUrlDataFromOrp(IQIYI_PHOTO_API, 'GET', array(), array(), array(), 10000, 60, 10240000, 3);
            if(false == $res || 0 != $res['errno']){
                Bingo_Log::warning("call service fail :type [bsx-outsite-photo], url:".IQIYI_PHOTO_API." , res:".serialize($res));
            }else{
                $arrOutput = json_decode($res['data'],true);
            }
            $arrUrl = array();
            foreach ($arrOutput as $key => $value) {
                if($strQuery == $value['name']){
                    $arrUrl = $value['image'];
                }
            }
            if(empty($arrUrl)){
                $errMsg = "输入的名字不存在！";
                return $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), $errMsg);
            }
            $dir = TMP_PATH.$intTmp;
            mkdir($dir,0777,true);
            foreach ($arrUrl as $key => $value) {
                $strSuffix = end( explode('.', $value['url']));
                $res = $this->fetchUrlDataFromOrp($value['url'], 'GET', array(), array(), array(), 10000, 60, 10240000, 3);
                file_put_contents($dir.'/'.$key.'.'.$strSuffix, $res['data']);
            }
            
            $zip = new ZipArchive();
            // 打开一个zip文档，ZipArchive::OVERWRITE：如果存在这样的文档，则覆盖；ZipArchive::CREATE：如果不存在，则创建
            $res = $zip->open($dir.".zip",  ZipArchive::OVERWRITE);//var_dump($res);exit;
            if($res)
            {

                $handler = opendir($dir);//当前目录中的文件夹下的文件夹
                while( ($filename = readdir($handler)) !== false ) {
                      if($filename != "." && $filename != ".."){
                        $zip->addFile($dir.'/'.$filename,$filename);
                      }
                }
                closedir($handler);

            }
            // 关闭打开的压缩文档
            $res =  $zip->close();//var_dump($filename);exit;
            header ( "Cache-Control: max-age=0" );
            header ( "Content-Description: File Transfer" );
            header ( 'Content-disposition: attachment; filename=' .$strQuery.'.zip' ); // 文件名
            header ( "Content-Type: application/zip" ); // zip格式的
            header ( "Content-Transfer-Encoding: binary" ); // 告诉浏览器，这是二进制文件
            header ( 'Content-Length: ' . filesize ( $dir.".zip" ) ); // 告诉浏览器，文件大小
            readfile ( $dir.".zip" );//输出文件;
            unlink($dir.".zip");
            Bingo_Log::warning("del file success :".$dir.".zip");
            $this->rmDir($dir);
            exit;

        } catch (Exception $e) {
            Bingo_Log::warning( "errno =".$e->getCode() ." msg=".$e->getMessage() );
            $this->_jsonRet($e->getCode(), '未知错误');
        }
    }
    
    /**
     * inherit from parent and do nothing
     * @param array
     * @return bool
     */
    protected function _jsonRet($errno, $arrData=array(),$errmsg = ''){
        $arrRet = array(
            'errno' => $errno,
            'errmsg' => empty($errmsg)?Tieba_Error::getErrmsg($errno):$errmsg,
            'data' => $arrData,
        );
        echo json_encode($arrRet);
    }

    /**
     * inherit from parent and do nothing
     * @param string
     * @return bool
     */
    protected function rmDir($dir){
        if($handle = opendir($dir)){
            while(false !== ($item = readdir($handle))){
                if($item!="." && $item != ".."){
                    if(is_dir("$dir/$item")){
                        rmDir("$dir/$item");
                    }else{
                        unlink("$dir/$item");
                        Bingo_Log::warning("del file success :$dir/$item");
                   }
                }
            }
            closedir($handle);
            rmdir($dir);
            Bingo_Log::warning("del folder success :$dir/$item");
            return true;
        }
        return false;
    }

    /**
     * 
     * @param unknown $url
     * @param string $requestMethod
     * @param unknown $arrPostParam
     * @param unknown $arrHeaders
     * @param unknown $arrCookie
     * @param number $connect_timeout_ms
     * @param number $run_timeout_s
     * @param number $maxResponseSize
     * @param number $connRetry
     * @return boolean|Ambigous <multitype:, multitype:unknown multitype:unknown  , multitype:number string Ambigous <mixed, string, string, unknown, boolean> >
     */
    public static function fetchUrlDataFromOrp(
            $url,
            $requestMethod = 'GET',
            $arrPostParam = array(),
            $arrHeaders = array(),
            $arrCookie = array(),
            $connect_timeout_ms = 10000,
            $run_timeout_s = 60,
            $maxResponseSize = 1024000,
            $connRetry = 3
            ){
        //使用orp的fetchurl获取数据
        $httpOrpObj = Orp_FetchUrl::getInstance(
                array(
                    'timeout' =>$run_timeout_s*1000, //$intReadTimeOut,
                    'conn_timeout' => $connect_timeout_ms/1000, //$intConnTimeOut,
                    'max_response_size' => $maxResponseSize,
                    'conn_retry' => $connRetry
                )
        );
        if(false == $httpOrpObj || empty($httpOrpObj)){
            Bingo_Log::warning('create orp fetch url instance fail.');
            return false;
        }
        $res = null;
        if('GET' == $requestMethod){
            $res = $httpOrpObj->get($url, $arrHeaders, $arrCookie);
        }else if('POST' == $requestMethod){
            $res = $httpOrpObj->post($url, $arrPostParam, $arrHeaders, $arrCookie);
        }else{
            $res = $httpOrpObj->get($url, $arrHeaders, $arrCookie);
        }
        $arrOut = array();
        $errmsg = $httpOrpObj->errmsg();
        $errCode = $httpOrpObj->errno();
        $httpCode = $httpOrpObj->http_code();
        if(200 == $httpCode) {
            //成功
            $arrOut = array(
                'errno' => 0,
                'errmsg' => 'success',
                'data' => $res,
            );
        }else{
            //失败
            $arrOut = array(
                'errno'    => $errCode,
                'errmsg' => $errmsg, //$code,
                'data'    => array(
                    'http_code' => $httpCode,
                    'errmsg' => $errmsg,
                    'output' => $res,
                ),
            );
        }
        return $arrOut;
    }
}



?>
