<?php
define("BINGO_ENCODE_LANG", 'utf-8');

/**
 * @abstract addChannelAction
 * <AUTHOR>
 *
 */
class addChannelAction extends Actions_Common_Content_Util_Actioncontent{
	
	private static $_arrParam = array(
		'channel_name' => array(
			'need_check' => true,
			'type' => 1,  //1-string, 2-int
			'default' => '',
		),
		'account_name' => array(
			'need_check' => true,
			'type' => 1,  //1-string, 2-int
			'default' => '',
		),
		'account_id' => array(
			'need_check' => true,
			'type' => 2,  //1-string, 2-int
			'default' => 0,
		),
	);

	/**
	 * inherit from parent and do nothing
	 * @param null
	 * @return boolean
	 */
	public function process(){
		
		$arrParamValue = array();
		foreach (self::$_arrParam as $key => $arrConf){
			$varValue = '';
			$type = intval($arrConf['type']);
			$needCheck = (bool)$arrConf['need_check'];
			$checkPass = false;
			if(1 == $type){
				$varValue = Bingo_Http_Request::get($key, $arrConf['default']);
				$checkPass = ($needCheck? !empty($varValue) : true);
			}else if(2 == $type){
				$varValue = intval(Bingo_Http_Request::get($key, $arrConf['default']));
				$checkPass = ($needCheck? $varValue>0 : true);
			}
			if(!$checkPass){
				Bingo_Log::warning('addChannelAction input param is invalid.'.$key.':['.$varValue.']');
				$msg = "input param invalid.$key:[$varValue]";
				$this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, $msg);
				return true;
			}
			$arrParamValue[$key] = $varValue;
		}
		//$strChannelName = Bingo_Http_Request::get('channel_name', '');
		//$strAccountName = Bingo_Http_Request::get('account_name', '');
		//$accountID = intval(Bingo_Http_Request::get('account_id', 0));
		
		if(empty($arrParamValue)){
			//if(empty($strChannelName) || empty($strAccountName) || $accountID <= 0){
			Bingo_Log::warning('addChannelAction input param is invalid.input:['.serialize($arrParamValue).']');
			$this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
			return true;
		}
		
		$intOpUid = $this->_intUserId;
		$strOpUname = $this->_strUserName;
		if($intOpUid <= 0 || empty($strOpUname)){
			Bingo_Log::warning('addChannelAction invalid login.OpUid:['.$intOpUid.'] opuname:['.$strOpUname.']');
			return $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN, '用户未登录，请先登录。');
		}

		$arrInput = array(
			'channel_name' => $arrParamValue['channel_name'],
			'account_name' => $arrParamValue['account_name'],
			'account_id' => $arrParamValue['account_id'],
			'op_uid' => $intOpUid,
			'op_name' => $strOpUname,
			'status' => 0,
		);
		$arrRes = Tieba_Service::call('common', 'contentAddChannel', $arrInput, null, null, 'post', 'php', 'gbk');
		if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
			Bingo_Log::warning("call service fail ! input = ".serialize($arrInput)." output = ".serialize($arrRes));
			$this->_jsonRet(empty($arrRes['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRes['errno']);
			return true;
		}
		$this->_jsonRet(Tieba_Errcode::ERR_SUCCESS);
		return true;
	}
	
}