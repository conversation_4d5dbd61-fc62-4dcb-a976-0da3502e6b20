<?php
/**
 * <AUTHOR>
 * @date 2021/4/19
 */

class setHotIconAction extends Util_Actionbase {
    public function init() {
        return true;
    }

    public function process() {
        $strOpUname = Bingo_Http_Request::getServer('HTTP_AMIS_USER', '');
        $intId = intval(Bingo_Http_Request::getNoXssSafe('id', 0));
        $intHotIcon = intval(Bingo_Http_Request::getNoXssSafe('hotIcon', 0));

        if (empty($intId)) {
            return self::jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrArgument = array(
            'id' => $intId,
            'hot_icon' => $intHotIcon,
            'op_user' => $strOpUname,
        );

        $arrOutput = Tieba_Service::call('search', 'setHotIcon', $arrArgument);
        if ($arrOutput === false || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            Bingo_Log::warning('call search::editRankConf failed! input[' .serialize($arrArgument).'] output['. serialize($arrOutput) ."]");
            return self::jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::jsonRet(Tieba_Errcode::ERR_SUCCESS);
    }

    public static function jsonRet($errno, $arrData = array()) {
        $arrRet = array(
            'status' => $errno,
            'msg'    => Tieba_Error::getErrmsg($errno),
            'data'   => $arrData,
        );
        echo json_encode($arrRet);
    }
}