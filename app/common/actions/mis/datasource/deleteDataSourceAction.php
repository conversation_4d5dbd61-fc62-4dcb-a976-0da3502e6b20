<?php

/**
 * 删除数据源
 */

class deleteDataSourceAction extends Util_Actionbase{
    
    /**
     * @brief 初始化
     * @return bool
     */
    
    public function init()
    {
        self::setUiAttr('BROWSE_UI');
         if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }
    
    /**
     * @brief 主运行函数 
     * @return string
     */
    public function process(){
        $id = intval(Bingo_Http_Request::get('id')); 
        if (!isset($id)){
            Bingo_Log::warning('deleteDataSourceAction id is null [' . serialize(Bingo_Http_Request::get('id')) . ']');
            return self::jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);   
        }

        $arrInput = array(
            'id' =>   $id,
        );     
        $output = Tieba_Service::call('common', 'deleteDataSource', $arrInput, null, null, 'post', 'php', 'gbk' );
        if (false == $output || Tieba_Errcode::ERR_SUCCESS !== $output['errno']) {
            Bingo_Log::warning('call service deleteDataSource fail . output:[' . serialize($output) . ']');
            return self::jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::jsonRet(Tieba_Errcode::ERR_SUCCESS);           
    }
    


    /**
     * @brief 返回给amis平台的返回函数,内容结构符合amis要求
     * @return string
     */
    public static function jsonRet($errno, $arrData = array())
    {
        $arrRet = array(
            'status' => $errno,
            'msg'    => Tieba_Error::getErrmsg($errno),
            'data'   => $arrData,
        );
        echo json_encode($arrRet);
    }

}
