<?php

/**
 * 数据源列表页面
 */

class getDataSourceListAction extends Util_Actionbase{
    
    /**
     * @brief 初始化
     * @return bool
     */
    
    public function init()
    {
        self::setUiAttr('BROWSE_UI');
         if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }
    
    /**
     * @brief 主运行函数 获取全部数据源
     * @return string
     */
    public function process(){
        $arrInput = array();        
        $outPut = Tieba_Service::call('common', 'getDataSourceList', $arrInput, null, null, 'post', 'php', 'gbk' );
        if (false == $outPut || Tieba_Errcode::ERR_SUCCESS !== $outPut['errno']) {
            Bingo_Log::warning('call service getDataSourceList fail . output:[' . serialize($outPut) . ']');
            return self::jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrDataSource = $outPut['data'];
        if(empty($arrDataSource)){
            Bingo_Log::warning('There is no data source'. serialize($arrDataSource));
        }
               
        return self::jsonRet(Tieba_Errcode::ERR_SUCCESS,$arrDataSource);    
    }
    


    /**
     * @brief 返回给amis平台的返回函数，内容结构符合amis要求
     * @return string
     */
    public static function jsonRet($errno, $arrData = array())
    {
        $arrRet = array(
            'status' => $errno,
            'msg'    => Tieba_Error::getErrmsg($errno),
            'data'   => $arrData,
        );
        echo json_encode($arrRet);
    }

}
