<?php
/**
 * 注：此代表不能用于两个活动同时进行
 * Created by PhpStorm.
 * User: huxiaomei
 * Date: 2019/7/23
 * Time: 下午7:41
 */

define("BINGO_ENCODE_LANG", 'utf-8');
class getAwardRecordAction extends Util_Actionbase{
    const WORD_LIST_NAME = 'tb_wordlist_redis_Flop_Activity';
    const WORD_LIST_KEY = 'hot_forum_ids';
    const WORD_LIST_ACTIVITY_END_TIME = 'end_time';
    const WORD_LIST_ACTIVITY_BEGIN_TIME = 'start_time';
    const WORD_LIST_ACTIVITY_ALL_END_TIME = 'all_end_time';
    const ONE_DAY = 86400;
    const REDIS_PREFIX_TOTAL_COLLECT_FINISH_COUNT = 'card_total_collect_finish_count'; //总集齐人数，包含灌水
    const REDIS_PREFIX_TOTAL_COLLECT_REAL_FINISH_COUNT = 'card_total_collect_real_finish_count'; //真实总集齐人数
    //奖金来源
    const SOURCE_DRAW_AWARD = 0; //抽奖
    const SOURCE_CARVE_UP = 1; // 瓜分
    const SOURCE_DISMANTLE_BOX = 2; //拆礼盒
    const SOURCE_CASH_OUT = 3; //提现

    //奖金审核状态
    const PRIZE_STATUS_DEFAULT = 0; //审核中
    const PRIZE_STATUS_PASS = 1; //审核通过
    const PRIZE_STATUS_FAIL = 2; //审核未通过
    const PRIZE_STATUS_CASHED = 3; //发起提现
    const PRIZE_STATUS_CASH_SUCCESS = 4; //提现成功


    private static $awardIds = array('card4','card5',1,2,3,4,5,6,7,8,9);
    private static $_objMulti = null;
    /**
     * inherit from parent and do nothing
     * @return bool
     */

    public function init(){
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * inherit from parent and do nothing
     * @param null
     * @return boolean
     */
    public function process(){
        $arrRet = array();
        $rows = array();
        $isNow = Bingo_Http_Request::get('is_now', 0);

        $periodInfo = self::getPeriodInfo();
        if (false === $periodInfo){
            Bingo_Log::warning('getPeriodInfo fail!');
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return false;
        }
        $periodId = $periodInfo['period_id'];
        $periodBeginTime = $periodInfo['begin_time'];
        if (0 === $periodId){
            Bingo_Log::warning('getPeriodInfo no start!');
            $this->_jsonRet(-1,array(),'活动未开始或已结束');
            return true;
        }
        self::$_objMulti = new Tieba_Multi('flop_activity_multi_call');
        if ($isNow){
            $recordPeriodDate = $periodBeginTime;
            $arrInput = array(
                'serviceName' => 'common',
                'method'      => 'getAllAwardRecord',
                'input'       => array(
                    'period_date' => $recordPeriodDate
                )
            );
            self::$_objMulti->register('getAllAwardRecord', new Tieba_Service('common'), $arrInput);

            //获取redis最大值配置
            $arrInput = array(
                'serviceName' => 'common',
                'method'      => 'getAllAwardConf',
                'input'       => array()
            );
            self::$_objMulti->register('getAllAwardConf', new Tieba_Service('common'), $arrInput);

            //获取集齐人数
            $arrInput = array(
                'serviceName' => 'common',
                'method'      => 'getCollectFinishCount',
                'input'       => array(
                    'keys' => array(self::REDIS_PREFIX_TOTAL_COLLECT_FINISH_COUNT,self::REDIS_PREFIX_TOTAL_COLLECT_REAL_FINISH_COUNT),
                    'period_id' => $periodId
                )

            );
            self::$_objMulti->register('getCollectFinishCount', new Tieba_Service('common'), $arrInput);

            //获取邀新发卡数量
            $arrInput = array(
                'serviceName' => 'common',
                'method'      => 'getInviteCountByPeriodid',
                'input'       => array(
                    'period_id' => $periodId
                )

            );
            self::$_objMulti->register('getInviteCountByPeriodid', new Tieba_Service('common'), $arrInput);

            //获取红包金额数量
            $arrInput = array(
                'serviceName' => 'common',
                'method'      => 'getMoneyListByPeriodId',
                'input'       => array(
                    'period_id' => $periodId
                )

            );
            self::$_objMulti->register('getMoneyListByPeriodId', new Tieba_Service('common'), $arrInput);

        }else{
            $periodidArr = array();
            for ($i=0;$i<$periodId;$i++){
                //获取所有的记录
                $recordPeriodDate = $periodBeginTime - $i*86400;
                $recordPeriodId = $periodId - $i;
                array_push($periodidArr,$recordPeriodId);
                $arrInput = array(
                    'serviceName' => 'common',
                    'method'      => 'getAllAwardRecord',
                    'input'       => array(
                        'period_date' => $recordPeriodDate
                    )
                );
                self::$_objMulti->register('getAllAwardRecord_'.$i, new Tieba_Service('common'), $arrInput);

                //获取集齐人数
                $arrInput = array(
                    'serviceName' => 'common',
                    'method'      => 'getCollectFinishCount',
                    'input'       => array(
                        'keys' => array(self::REDIS_PREFIX_TOTAL_COLLECT_FINISH_COUNT,self::REDIS_PREFIX_TOTAL_COLLECT_REAL_FINISH_COUNT),
                        'period_id' => $periodId - $i
                    )

                );
                self::$_objMulti->register('getCollectFinishCount_'.$i, new Tieba_Service('common'), $arrInput);

            }

            //获取邀新发卡数量
            $arrInput = array(
                'serviceName' => 'common',
                'method'      => 'getInviteCountByPeriodid',
                'input'       => array(
                    'period_id' => $periodidArr
                )

            );
            self::$_objMulti->register('getInviteCountByPeriodids', new Tieba_Service('common'), $arrInput);

            //获取奖金信息
            $arrInput = array(
                'serviceName' => 'common',
                'method'      => 'getMoneyListByPeriodId',
                'input'       => array(
                    'period_id' => $periodidArr
                )

            );
            self::$_objMulti->register('getMoneyListByPeriodIds', new Tieba_Service('common'), $arrInput);

        }
        self::$_objMulti->call();

        //获取记录
        if ($isNow){
            $arrRes = self::$_objMulti->getResult('getAllAwardRecord');
            if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
                Bingo_Log::warning("call service fail ! input = ".serialize($arrRes)." output = ".serialize($arrRes));
                $this->_jsonRet(empty($arrRes['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRes['errno']);
                return true;
            }
            $awardRecord = $arrRes['data'];
            $record = array();
            $record['period_date'] = date('Y-m-d',$recordPeriodDate);
            $record['period_id'] = $periodId;
            foreach ($awardRecord as $awardId => $info){
                $record[$awardId.'_total_num'] = $info['total_num'];
            }

            //获取当期最大值配置
            $arrRes = self::$_objMulti->getResult('getAllAwardConf');
            if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
                Bingo_Log::warning("call service fail ! input = ".serialize($arrRes)." output = ".serialize($arrRes));
                $this->_jsonRet(empty($arrRes['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRes['errno']);
                return true;
            }
            $maxNumConf = $arrRes['data']['max_num_conf'];
            foreach ($maxNumConf as $k => $v){
                $maxNum = $v['value'];
                $awardId = $v['field'];
                $record[$awardId.'_left_num'] = $maxNum - $record[$awardId.'_total_num'];
                $record[$awardId.'_real_num'] = $record[$awardId.'_total_num'].'/'.$maxNum;
            }

            //获取集齐人数
            //集齐人数
            $arrRes = self::$_objMulti->getResult('getCollectFinishCount');
            if (false !== $arrRes && Tieba_Errcode::ERR_SUCCESS == $arrRes['errno']){
                Bingo_Log::notice('getCollectFinishCount_ret'.$periodId.'_'.serialize($arrRes));
                $record['fake_gather_all_person'] = isset($arrRes['data'][self::REDIS_PREFIX_TOTAL_COLLECT_FINISH_COUNT]) && intval($arrRes['data'][self::REDIS_PREFIX_TOTAL_COLLECT_FINISH_COUNT]) > 0 ? $arrRes['data'][self::REDIS_PREFIX_TOTAL_COLLECT_FINISH_COUNT] : 0;
                $record['gather_all_person'] = isset($arrRes['data'][self::REDIS_PREFIX_TOTAL_COLLECT_REAL_FINISH_COUNT]) && intval($arrRes['data'][self::REDIS_PREFIX_TOTAL_COLLECT_REAL_FINISH_COUNT]) > 0 ? $arrRes['data'][self::REDIS_PREFIX_TOTAL_COLLECT_REAL_FINISH_COUNT] : 0;
            }else{
                Bingo_Log::warning('call getCollectFinishCount fail !'.$this->intUserId.'_'.$this->periodId.'_'.serialize($arrRes));
            }

            //获取邀新人数
            $arrRes = self::$_objMulti->getResult('getInviteCountByPeriodid');
            if (false !== $arrRes && Tieba_Errcode::ERR_SUCCESS == $arrRes['errno']){
                Bingo_Log::notice('getInviteCountByPeriodid_ret'.$periodId.'_'.serialize($arrRes));
                $record['new_card4_count'] = isset($arrRes['data'][$periodId][4]['count']) && intval($arrRes['data'][$periodId][4]['count']) > 0 ? intval($arrRes['data'][$periodId][4]['count']) : 0;
                $record['new_card5_count'] = isset($arrRes['data'][$periodId][5]['count']) && intval($arrRes['data'][$periodId][5]['count']) > 0 ? intval($arrRes['data'][$periodId][5]['count']) : 0;
            }else{
                Bingo_Log::warning('call getCollectFinishCount fail !'.$this->intUserId.'_'.$this->periodId.'_'.serialize($arrRes));
            }

            //获取红包金额数
            $arrRes = self::$_objMulti->getResult('getMoneyListByPeriodId');
            if (false !== $arrRes && Tieba_Errcode::ERR_SUCCESS == $arrRes['errno']){
                Bingo_Log::notice('getMoneyListByPeriodId_ret'.$periodId.'_'.serialize($arrRes));
                //现金红包已发金额
                $totalAwardMoney = 0;
                foreach ($arrRes['data'][$periodId][self::SOURCE_DRAW_AWARD] as $key => $data){
                    $totalAwardMoney += $data['total_money'];
                }
                $record['total_award_money'] = $totalAwardMoney;
            }else{
                Bingo_Log::warning('call getMoneyListByPeriodId fail !'.$this->intUserId.'_'.$this->periodId.'_'.serialize($arrRes));
            }

            array_push($rows,$record);
        }else{
            //获取邀新人数
            $arrRes = self::$_objMulti->getResult('getInviteCountByPeriodids');
            $inviteInfo = array();
            if (false !== $arrRes && Tieba_Errcode::ERR_SUCCESS == $arrRes['errno']){
                Bingo_Log::notice('getInviteCountByPeriodids_ret'.serialize($arrRes));
                $inviteInfo = $arrRes['data'];
            }else{
                Bingo_Log::warning('call getInviteCountByPeriodids fail !'.$this->intUserId.'_'.$this->periodId.'_'.serialize($arrRes));
            }

            //获取奖金信息
            $arrRes = self::$_objMulti->getResult('getMoneyListByPeriodIds');
            $moneyList = array();
            if (false !== $arrRes && Tieba_Errcode::ERR_SUCCESS == $arrRes['errno']){
                Bingo_Log::notice('getMoneyListByPeriodIds_ret'.serialize($arrRes));
                $moneyList = $arrRes['data'];
            }else{
                Bingo_Log::warning('call getMoneyListByPeriodIds fail !'.$this->intUserId.'_'.$this->periodId.'_'.serialize($arrRes));
            }

            for ($i=0;$i<$periodId;$i++){
                $recordPeriodDate = $periodBeginTime - $i*86400;
                $arrRes = self::$_objMulti->getResult('getAllAwardRecord_'.$i);

                if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
                    Bingo_Log::warning("call service fail ! input = ".serialize($arrRes)." output = ".serialize($arrRes));
                    $this->_jsonRet(empty($arrRes['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRes['errno']);
                    return true;
                }
                $awardRecord = $arrRes['data'];
                $record = array();
                $record['period_date'] = date('Y-m-d',$recordPeriodDate);
                $record['period_id'] = $periodId - $i;
                foreach ($awardRecord as $awardId => $info){
                    $record[$awardId.'_total_num'] = $info['total_num'];
                }

                //获取集齐人数
                $arrRes = self::$_objMulti->getResult('getCollectFinishCount_'.$i);
                if (false !== $arrRes && Tieba_Errcode::ERR_SUCCESS == $arrRes['errno']){
                    Bingo_Log::notice('getCollectFinishCount_ret'.$record['period_id'].'_'.$record['period_date'].'_'.serialize($arrRes));
                    $record['fake_gather_all_person'] = isset($arrRes['data'][self::REDIS_PREFIX_TOTAL_COLLECT_FINISH_COUNT]) && intval($arrRes['data'][self::REDIS_PREFIX_TOTAL_COLLECT_FINISH_COUNT]) > 0 ? $arrRes['data'][self::REDIS_PREFIX_TOTAL_COLLECT_FINISH_COUNT] : 0;
                    $record['gather_all_person'] = isset($arrRes['data'][self::REDIS_PREFIX_TOTAL_COLLECT_REAL_FINISH_COUNT]) && intval($arrRes['data'][self::REDIS_PREFIX_TOTAL_COLLECT_REAL_FINISH_COUNT]) > 0 ? $arrRes['data'][self::REDIS_PREFIX_TOTAL_COLLECT_REAL_FINISH_COUNT] : 0;
                }else{
                    Bingo_Log::warning('call getCollectFinishCount fail !'.$record['period_id'].'_'.$record['period_date'].'_'.serialize($arrRes));
                }

                //获取邀新人数
                if (!empty($inviteInfo[$record['period_id']])){
                    Bingo_Log::notice('getInviteCountByPeriodid_ret'.$record['period_id'].'_'.serialize($arrRes));
                    $record['new_card4_count'] = isset($inviteInfo[$record['period_id']][4]['count']) && intval($inviteInfo[$record['period_id']][4]['count']) > 0 ? intval($inviteInfo[$record['period_id']][4]['count']) : 0;
                    $record['new_card5_count'] = isset($inviteInfo[$record['period_id']][5]['count']) && intval($inviteInfo[$record['period_id']][5]['count']) > 0 ? intval($inviteInfo[$record['period_id']][5]['count']) : 0;
                }else{
                    Bingo_Log::warning('has no invite record !'.$record['period_id'].'_'.$record['period_date'].'_'.serialize($arrRes));
                }

                //获取奖金信息
                if (!empty($moneyList[$record['period_id']])){
                    Bingo_Log::notice('getMoneyListByPeriodId_ret'.$record['period_id'].'_'.serialize($arrRes));
                    //抽奖红包信息
                    $totalAwardMoney = 0;//审核前
                    $totalAwardMoney2 = 0;//审核通过
                    $totalAwardMoney3 = 0;//已提现
                    foreach ($moneyList[$record['period_id']][self::SOURCE_DRAW_AWARD] as $k => $data){
                        $status = $data['status'];
                        $totalAwardMoney += $data['total_money'];
                        if (in_array($status,array(self::PRIZE_STATUS_PASS,self::PRIZE_STATUS_CASHED,self::PRIZE_STATUS_CASH_SUCCESS))){
                            $totalAwardMoney2 += $data['total_money'];
                        }
                        if (in_array($status,array(self::PRIZE_STATUS_CASHED,self::PRIZE_STATUS_CASH_SUCCESS))){
                            $totalAwardMoney3 += $data['total_money'];
                        }
                    }
                    $record['total_award_money'] = $totalAwardMoney;
                    $record['total_award_money2'] = $totalAwardMoney2;
                    $record['total_award_money3'] = $totalAwardMoney3;

                    //集齐（瓜分）奖金
                    $totalCarveMoney = 0;//审核前
                    $totalCarveMoney2 = 0;//审核通过
                    $totalCarveMoney3 = 0;//已提现
                    foreach ($moneyList[$record['period_id']][self::SOURCE_CARVE_UP] as $status => $data){
                        $status = $data['status'];
                        $totalCarveMoney += $data['total_money'];
                        if (in_array($status,array(self::PRIZE_STATUS_PASS,self::PRIZE_STATUS_CASHED,self::PRIZE_STATUS_CASH_SUCCESS))){
                            $totalCarveMoney2 += $data['total_money'];
                        }
                        if (in_array($status,array(self::PRIZE_STATUS_CASHED,self::PRIZE_STATUS_CASH_SUCCESS))){
                            $totalCarveMoney3 += $data['total_money'];
                        }
                    }
                    $record['total_carve_money'] = $totalCarveMoney;
                    $record['total_carve_money2'] = $totalCarveMoney2;
                    $record['total_carve_money3'] = $totalCarveMoney3;
                }else{
                    Bingo_Log::warning('has no money list !'.$record['period_id'].'_'.$record['period_date'].'_'.serialize($arrRes));
                }

                array_push($rows,$record);
            }
        }

        $arrRet = array(
            'count' => count($rows),
            'rows' => $rows
        );
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,$arrRet);
        return true;
    }

    /**
     * inherit from parent and do nothing
     * @param array
     * @return bool
     */
    protected function _jsonRet($errno, $arrData=array(),$errmsg=''){
        $arrRet = array(
            'errno' => $errno,
            'errmsg' => !empty($errmsg) ? $errmsg : Tieba_Error::getErrmsg($errno),
            'data' => $arrData,
        );
        echo json_encode($arrRet);
    }

    /**
     * 获取期的开始日期 getPeriodDate
     * @param  [type]  $intFid [description]
     * @return array bool        [description]
     */
    public static function getPeriodInfo()
    {
        $handleWordServer = Wordserver_Wordlist::factory();
        $wordList = array(self::WORD_LIST_ACTIVITY_BEGIN_TIME,self::WORD_LIST_ACTIVITY_END_TIME,self::WORD_LIST_ACTIVITY_ALL_END_TIME);
        $arrItemInfo      = $handleWordServer->getValueByKeys(array_values($wordList), self::WORD_LIST_NAME);
        if (false === $arrItemInfo) {
            Bingo_Log::warning(__METHOD__.' query wordlist fail:'.serialize($arrItemInfo));
            return false;
        }
        $beginTime = isset($arrItemInfo[self::WORD_LIST_ACTIVITY_BEGIN_TIME]) && intval($arrItemInfo[self::WORD_LIST_ACTIVITY_BEGIN_TIME]) > 0 ? intval($arrItemInfo[self::WORD_LIST_ACTIVITY_BEGIN_TIME]) : 0;
        $endTime = isset($arrItemInfo[self::WORD_LIST_ACTIVITY_END_TIME]) && intval($arrItemInfo[self::WORD_LIST_ACTIVITY_END_TIME]) > 0 ? intval($arrItemInfo[self::WORD_LIST_ACTIVITY_END_TIME]) : 0;
        $allEndTime = isset($arrItemInfo[self::WORD_LIST_ACTIVITY_ALL_END_TIME]) && intval($arrItemInfo[self::WORD_LIST_ACTIVITY_ALL_END_TIME]) > 0 ? intval($arrItemInfo[self::WORD_LIST_ACTIVITY_ALL_END_TIME]) : 0;

        $currentTime = time();
        $periodId = 0;
        $periodStartTime = 0;
        $periodEndTime = 0;
        $lastPeriodId = 0;

        if ($currentTime >= $beginTime && $currentTime <= $allEndTime){
            $todayStartTime = strtotime(date('Y-m-d 21:00:00',$currentTime));
            $lastDayStartTime = strtotime(date('Y-m-d 21:00:00',$currentTime - 86400));
            $periodStartTime = $currentTime > $todayStartTime ? $todayStartTime : $lastDayStartTime;
            $periodEndTime = $periodStartTime + self::ONE_DAY -1;
            $periodId = $currentTime <= $allEndTime ? ceil((time() - $beginTime)/86400) : 0;
            $lastPeriodId = $endTime <= $allEndTime ? ceil(($endTime - $beginTime)/86400) : 0;
        }

        $arrResult = array(
            'begin_time' => $periodStartTime,
            'end_time' => $periodEndTime,
            'period_id' => intval($periodId),
            'last_period_id' => intval($lastPeriodId)//获取活动最后一期 期数
        );
        return $arrResult;
    }

}