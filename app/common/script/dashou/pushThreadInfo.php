<?php
/**
 * 贴吧推送百家号任务
 */

//执行方式 cd $ORP_PATH ;  php app/msgpush/scripts/detectTimingTask.php  调整目录,add by magong<PERSON><PERSON>@baidu.com

define ('EASYSCRIPT_DEBUG',false);          //debug 模式
define ('EASYSCRIPT_THROW_EXEPTION',true);  //抛异常模式

define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../../' );
define ('SCRIPTNAME',basename(__FILE__,".php"));   //定义脚本名
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf");
define ('DATAPATH',BASEPATH."/data");
define ('LOGPATH',"./log/common/scripts/dashou");
define('IS_ORP_RUNTIME', true);
set_include_path(get_include_path() . PATH_SEPARATOR. BASEPATH.'/../../');


/**
 * 获取当前毫秒级时间戳
 * @param：null
 * @return：string
 */
function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');

/**
 * @param
 * @return
 */
function statLog($strLog){
	$log_path = ROOT_PATH . "/log/stlog/";
	if(!file_exists($log_path)){
		mkdir($log_path, 0777, true);
	}
	$strLogFileName = $log_path . 'wap' . "." . strftime('%Y%m%d%H');
	$logStr = 'NOTICE: ' . strftime('%m-%d %H:%M:%S') . ':  stat-log' . ' * ' .  posix_getpid() . " " . $strLog . "\n";
	file_put_contents($strLogFileName, $logStr, FILE_APPEND);
}


function printLog($strLog, $type = 'n', $bolEcho = true){
    if(empty($type)){
        return ;
    }
    if('n' == $type){
        Bingo_Log::notice($strLog);
    }else if('w' == $type){
        Bingo_Log::warning($strLog);
    }else if('f' == $type){
        Bingo_Log::fatal($strLog);
    }
    if($bolEcho){
        echo $strLog."\n";
    }
}

define ('LOG', 'log');
Bingo_Log::init(array(
    LOG => array(
        'file'  => LOGPATH ."/". SCRIPTNAME. ".log",
        'level' => 0x0f,
    ),
), LOG);

$easyScriptObj  = false;
try{
    printLog('tieba_push_dashou script ready run.');
    $strAppId = $argv[1];
    if(empty($strAppId)){
    	Lib_Log::warning('script name set is invalid,name:['.SCRIPTNAME.'] arrParam:['.serialize(argv).']');
    	exit;
    }
	$ScriptConf = array(
        'memory_limit'   => '1024M',
        'data_path'      => DATAPATH,
        'conf_path'      => CONFPATH,
        'lock_file_name' => SCRIPTNAME.'.lock',
        'done_file_name' => SCRIPTNAME.'.done',
        'db_alias'       => array(
            'im'     => 'forum_live',
        ),
        'conf_alias'     => array(
            'main'     => 'main.conf',
        ),
    );

	$easyScriptObj = new Util_EasyScript($ScriptConf);
    //防止脚本重复执行
    if( $easyScriptObj->checkScriptIsRuning() === true ){
        printLog('tieba_push_dashouscript is runing.');
        exit;
    }
    printLog('tieba_push_dashou script begin runing.');
    $lastRunTime = 0;
    $lastRunTime = $easyScriptObj->getLastSaveData('last_run_time', 0);
    $runCount = 0;
    $runCount = $easyScriptObj->getLastSaveData('script_run_count', 0);
    
    $pushdashou = new pushThreadInfoAction();
    $pushdashou->_execute($lastRunTime, $runCount,$strAppId);
    
    $easyScriptObj->addSaveData('last_run_time', $lastRunTime);
    $easyScriptObj->addSaveData('script_run_count', $runCount);
    $easyScriptObj->runSuccess();
    printLog('tieba_push_dashou script run success.');
}catch(Exception $e){
    if($easyScriptObj !== false){
       printLog('tieba_push_dashou script run fail!'.$easyScriptObj->getErr2String(), 'w');
    }else{
       printLog('tieba_push_dashou script run fail![null]', 'w');
    }
    printLog('tieba_push_dashou run fail.'.$easyScriptObj->getErr2String());
    exit;
}
printLog('tieba_push_dashou run finish.');

class pushThreadInfoAction {
	const TIEBA_PUSH_BAIJIAHAO='tb_wordlist_redis_TieBa_Push_DaShou';
	const TIEBA_DASHOU_NUMBER='tb_wordlist_redis_TieBa_DaShou_Number';
	private static  $arrAuthInfo =array(
		3000008 => array(
			'source_id' =>	3000008,
			'product' =>'tieba',
			'provider' =>'tieba',
		),
		3000013 => array(
			'source_id' =>	3000013,
			'product' =>'tieba_jingxuan',
			'provider' =>'贴吧头条',
		),
		3000014 => array(
			'source_id' =>	3000014,
			'product' =>'tieba_yule',
			'provider' =>'贴吧独家娱乐',
		),
		3000015 => array(
			'source_id' =>	3000015,
			'product' =>'tieba_xiaopage',
			'provider' =>'贴吧日报',
		),
	);
	public static function _execute(&$lastRunTime, &$runCount,$strAppId){
        Bingo_Log::warning('wangxbd======$$strAppId====='.var_export($strAppId,true));
		$arrAppId =explode("_",$strAppId);
        Bingo_Log::warning('wangxbd==========='.var_export($arrAppId[0],true));
        $boolReply =true;
        $intCountKey = count($arrAppId);
		if($intCountKey > 1){
			$boolReply = false;
		}
		//从词表取需要推荐的帖子
		$intAppId=$arrAppId[0];
		//读取账户对应信息
		$arrKeys = array($strAppId);
		$handleWordServer = Wordserver_Wordlist::factory();
		$arrNumberList= $handleWordServer->getValueByKeys($arrKeys,self::TIEBA_DASHOU_NUMBER);
		Bingo_Log::warning('wangxbd======$arrNumberInfo====='.var_export($arrNumberList,true));
		$arrNumberInfo = unserialize($arrNumberList[$strAppId]);
		if(empty($arrNumberInfo)){
			return ;
		}
		Bingo_Log::warning('wangxbd======$arrNumberInfo====='.var_export($arrNumberInfo,true));
		
		$arrKeys = array($strAppId);
		$handleWordServer = Wordserver_Wordlist::factory();
		$arrTidList= $handleWordServer->getValueByKeys($arrKeys,self::TIEBA_PUSH_BAIJIAHAO);
		Bingo_Log::warning('wangxbd======$arrTidList====='.var_export($arrTidList,true));
		Bingo_Log::warning('wangxbd=========$arrKeys=='.var_export($arrKeys,true));
		$intSleep =0;
        if(isset($arrTidList[$strAppId]) && !empty($arrTidList[$strAppId])){
			Bingo_Log::warning('wangxbd=====$arrTidList========'.var_export($arrTidList,true));
			$strTids =$arrTidList[$strAppId];
			$arrTids = explode(",",$strTids);
			if(empty($arrTids)){
				return ;
			}
			foreach ($arrTids as $intTid){
				//判断是否推送过
				$arrInput=array();
				$arrInput['thread_id'] =$intTid;
				$arrInput['source_id'] =$intAppId;
				$arrOutput = Molib_Tieba_Service::call('common', "dashouGetRecordInfo", $arrInput);
				Bingo_Log::warning('wangxbd=======dashouGetRecordInfo===='.var_export( $arrOutput,true));
				if ($arrOutput === false ||$arrOutput ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
					Bingo_Log::warning ( sprintf ( 'service error: %s_%s [%s] [%s]', 'common', 'dashouGetRecordInfo', serialize ( $arrInput ), serialize ( $arrOutput ) ) );
				}
				$boolPush =false;
				foreach ($arrOutput['data'] as $dataInfo){
					if($dataInfo['thread_id'] == $intTid && ($dataInfo['status']== 0 ||$dataInfo['status']== 2)){
						$boolPush =true;
						break;
					}
				}
				if($boolPush){
					continue;
				}
				
				$url='http://tieba.baidu.com/p/'.$intTid;
				//取帖子内容,先取一楼的内容
				$arrInputData=array(
					'tid' =>$intTid,
					'rn' =>1,
				);
				$onePostInfo=self::getPostInfoByTid($arrInputData, 'getPostsByThreadId');
               // Bingo_Log::warning('wangxbd=======getPostsByThreadId===='.var_export( $onePostInfo,true));
				if(empty($onePostInfo)){
					continue;
				}
				$strTitle =$onePostInfo[0]['title'];
				//取第一楼数据
				$content=array();
				if($onePostInfo[0]['post_no'] != 1){
					continue;
				}
				$arrRetData=self::processContent($onePostInfo[0]['content']);
				$content=$arrRetData['content'];
				$arrCoverImages =$arrRetData['coverimages'];
				if(empty($content)){
					continue;
				}
				//$arrCoverImages[]='http:\\/\\/c.hiphotos.baidu.com\\/forum\\/wh%3D218%2C146\\/sign=d60db221ecdde711e7874bf496d7e22a\\/58c024d4ad6eddc452b5b6df30dbb6fd5266337c.jpg';
				// if(empty($arrCoverImages) || count($arrCoverImages)==0){
				// 	continue;
				// }
				$pushInfo =array(
					//'source_id' => self::$arrAuthInfo[$intAppId]['source_id'],
					'source_id' => intval($arrNumberInfo[0]),
					'content_type' =>'image-text',
					'url' => $url,
					'title' => $strTitle,
					'content' => $content,
					'cover_images' =>  $arrCoverImages,
					//'product' => self::$arrAuthInfo[$intAppId]['product'],
					'product' => $arrNumberInfo[1],
					//'provider' => self::$arrAuthInfo[$intAppId]['provider'],
					'provider' => $arrNumberInfo[2],
				);
				$arrRetData=self::pushPostInfo($pushInfo);
				
				$arrInput=array();
				$arrInput['thread_id'] =$intTid;
				$arrInput['source_id'] =$intAppId;
				$arrInput['article_id'] =0;
				if($arrRetData ==false || $arrRetData['err_no']!=0){
					$arrInput['status'] =1;
					$arrInput['remark'] =json_encode($arrRetData);
				}else{
					$intSleep +=1;
					if($intSleep % 50 ==0){
						$intSleep =0;
						sleep(1800);
					}
				}
				Bingo_Log::warning('wangxbd==dashouAddRecord========='.var_export( $arrInput,true));
				$arrOutput = Molib_Tieba_Service::call('common', "dashouAddRecord", $arrInput);
				Bingo_Log::warning('wangxbd==dashouAddRecord========='.var_export( $arrOutput,true));
				if ($arrOutput === false ||$arrOutput ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
					Bingo_Log::warning ( sprintf ( 'service error: %s_%s [%s] [%s]', 'common', 'dashouAddRecord', serialize ( $arrInput ), serialize ( $arrOutput ) ) );
				}
				
			}
		}

	}
	/**
	 * 对封面进行裁剪
	 * @param unknown $arrInputData
	 * @param unknown $serviceName
	 * @return NULL|unknown
	 */
	private static function procImgTextLivePic($arrPicUrl){
		$picSpecWH = 'wh=218,146';
		$arrRet = array();
		foreach ($arrPicUrl as $picUrl){
			$strUrl = Molib_Util_ClientImgLogic::generateNewUrl($picUrl, true, $picSpecWH);
			$arrRet[]=$strUrl;
		}
		return $arrRet;
	
	}
	
	/**
	 * 获取帖子信息
	 * @param unknown $arrInputData
	 * @param unknown $serviceName
	 * @return NULL|unknown
	 */
	private static function getPostInfoByTid($arrInputData,$serviceName){
		$arrInput = array (
			'thread_id' => $arrInputData['tid'],
			'res_num' => $arrInputData['rn'],
			'offset' => 0,
			'see_author' => 0,
			'has_comment' => 0,
			'has_mask' => 0,
			'has_ext' => 0,
			'structured_content' => 1,
		);
		$arrOutput = Tieba_Service::call ( 'post', $serviceName, $arrInput, null, null, 'post', 'php', 'utf-8');
		if ($arrOutput === false ||$arrOutput ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( sprintf ( 'service error: %s_%s [%s] [%s]', 'post', $serviceName, serialize ( $arrInput ), serialize ( $arrOutput ) ) );
			return null;
		}
		$arrList = $arrOutput ['output'] ['output'] [0];
		if($arrList['is_thread_deleted'] ==1 || $arrList['user_mask'] ==1 
			|| $arrList['is_thread_mask'] ==1 || $arrList['is_thread_exceed'] ==1){
			return null;
		}
		return $arrList['post_infos'];
	}
	
	/**
	 * 处理文本内容
	 * @param
	 * @return bool
	 */
	private static function processContent($content){
		$arrContent =array();
		$arrContent['type'] =1;
		$arrContent['image_num'] =0;
		$arrContent['video_num'] =0;
		$arrItems =array();
		$intImageNum =0;
		$arrPicImages =array();
		foreach ($content as $value){
			$arrItemInfo = array();
			if($value['tag']=='plainText'){
				$arrItemInfo['type']='text';
				$arrItemInfo['data']=$value['value'];
				$arrItemInfo['style']=0;
				$arrItems[]= $arrItemInfo;
			}else if ($value['tag']=='img' && $value['class']=='BDE_Image'){
				$arrItemInfo['type']='image';
				$arrItemInfo['data']=array(
					'original' =>array(
						'src' =>$value['src'],
						'height' =>intval($value['height']),
						'width' =>intval($value['width']),
					),
				);
				$arrPicImages[] =$arrItemInfo['data']['original'];
				$intImageNum ++;
				$arrItems[]= $arrItemInfo;
			}
		}
		$arrContent['image_num'] =$intImageNum;
		$arrContent['items'] =$arrItems;
		$arrCoverImages=array();
		if($intImageNum >3){
			$arrCoverImages[]=$arrPicImages[0];
			$arrCoverImages[]=$arrPicImages[1];
			$arrCoverImages[]=$arrPicImages[2];
		}else if($intImageNum >0){
			$arrCoverImages[]=$arrPicImages[0];
		}
		$arrOutData=array(
			'content' =>$arrContent,
			'coverimages' =>$arrCoverImages,
		);
		return $arrOutData;
	}
	
	
	/**
	 * 推送到百家号
	 * @param unknown $arrInputData
	 * @param unknown $serviceName
	 * @return NULL|unknown
	 */
	private static function pushPostInfo($pushInfo){
		$arrInput = array(
			'source_id' => $pushInfo['source_id'],
			'content_type' => $pushInfo['content_type'],
			'url' => $pushInfo['url'],
			'title' => $pushInfo['title'],
			'content' => $pushInfo['content'],
			'list_img_url' =>  $pushInfo['cover_images'],
			'from' => $pushInfo['product'],
			'publish_time' => time(),
			'provider' => $pushInfo['provider'],
		);
		Bingo_Log::warning('wangxbd====pushPostInfo ======='.var_export($arrInput ,true));
		$arrInput=json_encode($arrInput);
// 		$ret = self::http_post('http://ucp-bogus-qa.spider.yq01.serv:8080/put', $arrInput, 50);
 		$ret = self::http_post('http://ucp-bogus.spider.yq01.serv:8080/put', $arrInput);
		Bingo_Log::warning('wangxbd====$ret ======='.var_export($ret ,true));
		return $ret;
		
	}
	
	private static function  http_post($url, $post, $timeout) {
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $post);
		curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($curl, CURLOPT_HTTPHEADER, array('Expect:'));
		$output = curl_exec($curl);
		$code  = curl_getinfo($curl, CURLINFO_HTTP_CODE);
		$errMsg= curl_multi_getcontent($curl);
		$err = curl_error($curl);
		$errno = curl_errno($curl);
		curl_close($curl);
		Bingo_Log::warning('wangxbd=====$code======'.var_export($code,true));
		Bingo_Log::warning('wangxbd=======$errMsg===='.var_export($errMsg,true));
		Bingo_Log::warning('wangxbd======$err====='.var_export($err,true));
		Bingo_Log::warning('wangxbd========= $output=='.var_export( $output,true));
		if($code != 200) {
			$arrRet = array(
				'err_no'    => 1,
				'err_msg' => $code,
				'data'    => $errMsg,
			);
			return $arrRet;
		}
		$arrMsg=json_decode($errMsg,true);
		if($arrMsg['errno'] !=0){
			$arrRet = array(
				'err_no'    => $arrMsg['errno'],
				'err_msg' => $errMsg,
			);
			return $arrRet;
		}
		$output=json_decode($output,true);
		$arrRet = array(
			'err_no'    => 0,
			'err_msg' => $code,
			'data'    => $output['data']['id'],
		);
		return $arrRet;
	}
	/**
	 * @param $url
	 * @param int $intReadTimeOut
	 * @param int $intConnTimeOut
	 * @param string $strJsonData
	 * @return bool
	 * @desc FetchUrl通过函数，调用方式为post
	 */
	private static function fetchUrlPost($url,$strJsonData = '' , $intReadTimeOut = 1000000, $intConnTimeOut = 5000)
	{
		$httpproxy = Orp_FetchUrl::getInstance(array('timeout' => $intReadTimeOut, 'conn_timeout' => $intConnTimeOut, 'max_response_size' => 1024000, 'conn_retry' => 0));
		$headers = array(
			'Content-Type: application/x-www-form-urlencoded',
		);
	
		$res = $httpproxy->post($url, $strJsonData, $headers);
		$err = $httpproxy->errmsg();
		$http_code = $httpproxy->http_code();
		Bingo_Log::warning('wangxbd=====$err======'.var_export($err,true));
		Bingo_Log::warning('wangxbd=======$http_code===='.var_export($http_code,true));
		Bingo_Log::warning('wangxbd=======$res===='.var_export($res,true));
		if ($err || $http_code != 200) {
			Bingo_Log::warning('fetchurlpost:' . $url . ' error! error:' . $err);
			$body=$httpproxy->body();
			$arrRet = array(
				'err_no'    => 1,
				'err_msg' => $err,
				'data'    => $body,
			);
			return $arrRet;
		} else {
			$header = $httpproxy->header();
			if ($http_code == 200) {
				return json_decode($res,true);
			}
		}
		return false;
	}
	
}


