<?php
/***************************************************************************
 * 
 * Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file GetUserLoginDays.php
 * <AUTHOR>
 * @date 2018/01/15 19:36:43
 * @brief 
 *  
 **/
define('NAME_SCRIPT' , 'Script_Duxiaoman_GetUserLoginDays');
set_time_limit(0);
ini_set('memory_limit', '2G');

class Script_Duxiaoman_GetUserLoginDays
{
    const PAGE_COUNT = 50;
    private static $_objDB = null;
    const DB_NAME_PALO = 'tiebadata';
    // DB object
    const TABLE_NAME_LOGIN_DAYS = 'user_login_days';

    /**
     * [_getDB description]
     * @param
     * @return [type] [description]
     */
    private static function _getDB(){
        if ( self::$_objDB ){
            return self::$_objDB;
        }

        Bingo_Timer::start('db_init');
        self::$_objDB = Tieba_Mysql::getDB(self::DB_NAME_PALO);
        Bingo_Timer::end('db_init');
        if ( self::$_objDB && self::$_objDB->isConnected() ){
            return self::$_objDB;
        }
        else{
            echo 'fail to connect db'."\n";
            exit(-1);
        }
    }

    /**
     * [_queryDB description]
     * @param  [type] $strSql   [description]
     * @param  string $strTimer [description]
     * @return [type]           [description]
     */
    private static function _queryDB($strSql, $strTimer = 'db_query'){
        $objDB = self::_getDB();
        if ( !$objDB ){
        	echo 'fail to get db'."\n";
            Bingo_Log::warning('fail to get db');
            exit(-1);
        }
        Bingo_Timer::start($strTimer);
        $arrRet = $objDB -> query($strSql);
        Bingo_Timer::end($strTimer);
        if ( $arrRet === false ){
            $strLastError = $objDB->error();
            Bingo_Log::warning("execute sql error [$strSql] [$strLastError]");
        }
        return $arrRet;
    }

    public static function start(){

	    $offset = 0;
	    $intDay = strval(date('Ymd', strtotime('-1 day')));
        // 数据库连接配置
        $objTbMysql = Tieba_Mysql::getDB("forum_friend");

        if ($objTbMysql && $objTbMysql->isConnected()) {
            $objTbMysql -> charset('utf8');
        } else {
            Bingo_Log::warning("db connect fail.");
            exit(-1);
        }
        $strSql = "select count(*) from ".self::TABLE_NAME_LOGIN_DAYS." where  day = ".$intDay ;
        $arrRet  = $objTbMysql -> query($strSql);
        if(false === $arrRet){
            //重试一次
            $arrRet  = $objTbMysql -> query($strSql);
            if(false === $arrRet){
                echo "retry failed. sql=".$strSql;
                Bingo_Log::warning("retry failed. sql=".$strSql);
                exit(0);
            }
        }

	    $offset = intval($arrRet[0]['count(*)']);
	    while(1){

        	$strSql = "select day,uid,login_cnt from tieba_mds_user_login_count_day where  day = ".$intDay ." order by uid limit ".self::PAGE_COUNT. " offset ".$offset;
            $arrRet = self::_queryDB($strSql);

			if ( count($arrRet[0]) === 0 ){
				echo "done \n";
				break;
			}
			$arrData = array();
			foreach ( $arrRet as $arrInfo ){
				if( $arrInfo['uid'] <= 0 || empty($arrInfo['day']) ){
					continue;
				}
				$arrParams = array(
					'day' => $intDay,
					'times' => intval($arrInfo['login_cnt']),
					'user_id' => intval($arrInfo['uid']),
				);				
				$arrData['data'][] = $arrParams;	

			}
			$arrOutput = Tieba_Service::call('common','importMultiLoginRecord',$arrData);
				
			if ( !$arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno'] ){
				//失败重试
				$arrOutput = Tieba_Service::call('common','importMultiLoginRecord',$arrData);
				if ( !$arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno'] ){
					Bingo_Log::warning("importMultiLoginRecord_script fail! input= ".serialize($arrData)." out:".serialize($arrOutput));							
					echo "import fail.input:".serialize($arrData)." out:".serialize($arrOutput);
				}
			
			}
			$offset+=self::PAGE_COUNT;
		}
	}
}

Script_Duxiaoman_GetUserLoginDays::start();
?>
