<?php
/**
 * 获取最右推荐页数据
 */

//执行方式 cd $ORP_PATH ;  php app/msgpush/scripts/detectTimingTask.php  调整目录,add by magong<PERSON><PERSON>@baidu.com

define ('EASYSCRIPT_DEBUG',false);          //debug 模式
define ('EASYSCRIPT_THROW_EXEPTION',true);  //抛异常模式

define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../../' );
define ('SCRIPTNAME',basename(__FILE__,".php"));   //定义脚本名
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf");
define ('DATAPATH',BASEPATH."/data");
define ('LOGPATH',"./log/common/scripts/grad");
define('IS_ORP_RUNTIME', true);
set_include_path(get_include_path() . PATH_SEPARATOR. BASEPATH.'/../../');


/**
 * 获取当前毫秒级时间戳
 * @param：null
 * @return：string
 */
function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');

/**
 * @param
 * @return
 */
function statLog($strLog){
	$log_path = ROOT_PATH . "/log/stlog/";
	if(!file_exists($log_path)){
		mkdir($log_path, 0777, true);
	}
	$strLogFileName = $log_path . 'wap' . "." . strftime('%Y%m%d%H');
	$logStr = 'NOTICE: ' . strftime('%m-%d %H:%M:%S') . ':  stat-log' . ' * ' .  posix_getpid() . " " . $strLog . "\n";
	file_put_contents($strLogFileName, $logStr, FILE_APPEND);
}

/**
 * @param
 * @return
 */
function printLog($strLog, $type = 'n', $bolEcho = true){
    if(empty($type)){
        return ;
    }
    if('n' == $type){
        Bingo_Log::notice($strLog);
    }else if('w' == $type){
        Bingo_Log::warning($strLog);
    }else if('f' == $type){
        Bingo_Log::fatal($strLog);
    }
    if($bolEcho){
        echo $strLog."\n";
    }
}

define ('LOG', 'log');
Bingo_Log::init(array(
    LOG => array(
        'file'  => LOGPATH ."/". SCRIPTNAME. ".log",
        'level' => 0x0f,
    ),
), LOG);

$easyScriptObj  = false;
try{
    printLog('tieba_getzuiyouhotpost script ready run.');
	$ScriptConf = array(
        'memory_limit'   => '1024M',
        'data_path'      => DATAPATH,
        'conf_path'      => CONFPATH,
        'lock_file_name' => SCRIPTNAME.'.lock',
        'done_file_name' => SCRIPTNAME.'.done',
        'db_alias'       => array(
            'im'     => 'forum_live',
        ),
        'conf_alias'     => array(
            'main'     => 'main.conf',
        ),
    );

	$easyScriptObj = new Util_EasyScript($ScriptConf);
    //防止脚本重复执行
    if( $easyScriptObj->checkScriptIsRuning() === true ){
        printLog('tieba_getzuiyouhotpost is runing.');
        exit;
    }
    printLog('tieba_getzuiyouhotpost script begin runing.');
    $lastRunTime = 0;
    $lastRunTime = $easyScriptObj->getLastSaveData('last_run_time', 0);
    $runCount = 0;
    $runCount = $easyScriptObj->getLastSaveData('script_run_count', 0);
    
    $zuiYouHotPost = new getZuiYouHotPostAction();
    $zuiYouHotPost->_execute($lastRunTime, $runCount);
    
    $easyScriptObj->addSaveData('last_run_time', $lastRunTime);
    $easyScriptObj->addSaveData('script_run_count', $runCount);
    $easyScriptObj->runSuccess();
    printLog('tieba_getzuiyouhotpost script run success.');
}catch(Exception $e){
    if($easyScriptObj !== false){
       printLog('tieba_getzuiyouhotpost script run fail!'.$easyScriptObj->getErr2String(), 'w');
    }else{
       printLog('tieba_getzuiyouhotpost script run fail![null]', 'w');
    }
    printLog('tieba_getzuiyouhotpost run fail.'.$easyScriptObj->getErr2String());
    exit;
}
printLog('tieba_getzuiyouhotpost run finish.');

class getZuiYouHotPostAction {
	
	CONST MAX_WIDTH = 570;
	CONST MAX_HEIGHT = 6000;
	CONST CURL_DATA = "{\"h_ts\":1498640919858,\"h_model\":\"HONOR H30-L01\",\"h_ch\":\"huawei\",\"token\":\"TdKfNe12IYJhlANgzkxtPvwjzs9AvFyjcXyOigqangKoLDcM=\",\"h_os\":19,\"h_dt\":0,\"h_av\":\"3.5.9\",\"offset\":20,\"h_nt\":1,\"h_m\":25553872,\"h_did\":\"864502023579303_f4:9f:f3\"}";
	CONST CURL_POSTFER = "' 'http://tbapi.ixiaochuan.cn/rank/hotpost?sign=b75e4167b3e7d8d5b88d46955d073413   '  -x ************:8080 ";
	CONST CURL_PREFER = "curl -i -s -k  -X 'POST' \
                        -H 'Content-Type: application/json' -H 'ZYP: mid=25553872' -H 'User-Agent: tieba/3.5.0 (iPhone; iOS 10.0.2; Scale/2.00)' -H 'Host:tbapi.ixiaochuan.cn' \
                        -b 'aliyungf_tc=AQAAAInetgkqAQcAUqmHPU7DJ3qA2gFM' \
                        --data-binary $'";
	
	
	private static $strPath='./imgs';
	/**
	 * @param unknown 
	 * @return NULL|unknown
	 */
	public static function _execute(&$lastRunTime, &$runCount){
		$has_more=0;
		$intPn=0;
		$intSuccCount=0;
		do{
			$data = self::CURL_DATA;
			$data = json_decode ( $data, true );
			$data ['offset'] = $intPn * 20;
			$url = self::CURL_PREFER . json_encode ( $data ) . self::CURL_POSTFER;
			
			var_dump ( $url );
			if (empty ( $url )) {
				return;
			}
			$retData = exec ( $url, $ret );
			$retData = json_decode ( $retData, true );
			
			$arrRecData = $retData ['data'] ['list'];
			if (empty ( $arrRecData ) || count ( $arrRecData ) <= 0) {
				printLog ( 'zuiyou_data_empty' );
				return;
			}
			$intMore=$retData ['data']['more'];
			if($intMore == 1){
				$intPn += 1;
				$has_more = 1;
			}
			
			$list = array ();
			foreach ( $arrRecData as $oneTopic ) {
				$strContent = $oneTopic ['content'];
				if (false !== strpos ( $strContent, '最右' ) || false !== strpos ( $strContent, '右右' ) || false !== strpos ( $strContent, '右友' ) || false !== strpos ( $strContent, '小右' )) {
					continue;
				}
				if (! isset ( $oneTopic ['imgs'] ) || empty ( $oneTopic ['imgs'] )) {
					continue;
				}
				
				foreach ( $oneTopic ['imgs'] as $k => $oneImg ) {
					if(/*($oneImg['fmt'] != 'jpeg'||$oneImg['fmt'] != 'fmt')
					|| ($oneImg ['fmt'] == 'gif' && $oneImg ['mp4']) || */
					$oneImg ['video']) {
						unset ( $oneTopic ['imgs'] [$k] );
					}
				}
				if (! empty ( $oneTopic ['imgs'] ) && count ( $oneTopic ['imgs'] ) >= 0) {
					$list [$oneTopic ['id']] = $oneTopic;
				}
			}
			
			var_dump ( 'list==========count' );
			var_dump ( count ( $list ) );
			$intSuccCount +=count ( $list );
			if (empty ( $list ) || count ( $list ) <= 0) {
				Bingo_Log::warning ( 'grab data empty' );
			}
			if (is_array ( $list )) {
				self::processList ( $list );
			}
			sleep(5);
		}
		while ($has_more);
		
		if(is_dir(self::$strPath)) {
			@exec('rm -rf  ' . self::$strPath);
		};
		var_dump('succ==========');
		var_dump($intSuccCount);
	}
	/**
	 * @param unknown
	 * @return NULL|unknown
	 */
	public function processList($responceData) {
		
		if(is_array($responceData)) {
			foreach($responceData as $item) {
				$content = $item['content'];
				$id = intval($item['id']);
				$pic = $item['imgs'];
				if(empty($content) || empty($pic)){
					continue;
				}
				$imgArr=array();
				foreach($pic as $onePic) {
					$upPicRet = self::getImgUrl($id, $onePic);
					if($upPicRet !=false){
						$height = $onePic['h'] ;
						$width = $onePic['w'] ;
						$picWH = array(
							'width' => $width,
							'height' => $height,
						);
						$picWH = self::getWidth($width, $height);
							
						if (false == $picWH) {
							$imgSrc = "<img class='BDE_Image' src=";
							$imgSrc .= "'" . $upPicRet . "'  width='100' height='100'>";
						} else {
							$imgSrc = "<img class='BDE_Image' src=";
							$imgSrc .= "'" . $upPicRet . "'  width='" . $picWH["width"] . "' height='" . $picWH["height"] . "'>";
						}
						$imgArr[] = $imgSrc;
					}
					
				}
				$title=$content;
				if(empty($imgArr) ||count($imgArr) <=0){
					Bingo_Log::warning ('grabdata_no_img');
					continue;
				}
				
				$recContent=$content. "<br />". join("<br />",$imgArr);
				if(strpos($recContent, 'BDE_Image') === false){
					Bingo_Log::warning ('grabdata_no_img');
					continue;
				}
				$arrInput= array(
					'function'=>'addGrabResource',
					'resource_id'=>$id,
					'resource_type'=>1,
					'resource_title'=>$title,
					'resource_content'=>$recContent,
					'channel' =>1,
				);
				var_dump($arrInput);
				$arrOutput = Tieba_Service::call ( 'common', 'grabResource', $arrInput);
				if ($arrOutput === false || $arrOutput ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
					Bingo_Log::warning ( sprintf ( 'service error: %s_%s [%s] [%s]', 'common', 'getGrabResource', serialize ( $arrInput ), serialize ( $arrOutput ) ) );
				}
			}
		}
	}
	/**
	 * @param unknown
	 * @return NULL|unknown
	 */
	public function getImgUrl($id, $img) {
		$hostIP = 'http://***************/img/view/id/'.$img['id'].'/sz/src';
		$url = 'curl -H "Host: tbfile.ixiaochuan.cn" ' . $hostIP .' -x ************:8080 ';
		$path = './imgs/' . $id ;
		$fileName = $path . '/' . $img['id'] . '.jpg';
		if(!is_dir($path)) {
			@exec('mkdir -p ' . $path);
		}
		//$list = $path . '/list';
	
		$ret = @exec($url .' > ' . $fileName , $res);
	
	
		$fcontent = file_get_contents($fileName);
		$upRet = self::_uploadToImgSystem($fcontent);
		printLog('zuiyou_uploadToImgSystem_url:['.serialize($upRet).']', 'w');
		unset($fcontent);
		if(!$upRet) {
			return false;
		}
		
// 		$ffp = fopen($list, 'a');
// 		fputs($ffp, $upRet  . "\t" . $img['h'] . "\t" . $img['w'] . "\n");
// 		fclose($ffp);
// 		if(is_dir($path)) {
// 			@exec('rm -rf  ' . $path);
// 		}
		return $upRet;
	}
	/**
	 * @param unknown
	 * @return NULL|unknown
	 */
	public function getWidth($width, $height) {
		if ($width > self::MAX_WIDTH || $height > self::MAX_HEIGHT) {
			return self::getWidth($width / 2, $height / 2);
		}
		return array(
				'width' => intval($width),
				'height' => intval($height),
		);
	}
	/**
	 * @param unknown
	 * @return NULL|unknown
	 */
	public function _uploadToImgSystem($strPicData, $uid = '77604597', $uname = '806162674'){
		$arrSpaceData = array(
			'pic' => $strPicData,
		);
		$arrSpaceData['log_data']['uid'] = intval($uid);
		$strOpUname = strval($uname);
		$arrSpaceData['log_data']['uname'] = is_utf8($strOpUname) ? Molib_Util_Encode::convertUTF8ToGBK($strOpUname):$strOpUname;
		$arrSpaceData['log_data']['uip'] = intval(ip2long('***********')) ;
		Bingo_Timer::start('upload_img');
		$arrRet = @Space_Imgcmnew::upload($arrSpaceData);
		unset($arrSpaceData);
		unset($strPicData);
		Bingo_Timer::end('upload_img');
		if ($arrRet === false){
			return false;
		}
		if (empty($arrRet['pic_id_encode'])){
			return false;
		}
	
		$picUrl=self::_getPicUrlByPicid($arrRet['pic_id']);
		return $picUrl;
	}
	/**
	 * @param unknown
	 * @return NULL|unknown
	 */
	public  function _getPicUrlByPicid($picid, $fid = 0){
		if (0 >= $picid) {
			return '';
		}
		$arrInput = array (
				array (
					'pic_id' => $picid,
					'foreign_key' => $fid,
					'pic_spec' => 'pic',
					'product_name' => 'forum',
					'domain' => 'img' . 'src.baidu.com' 
				) 
		);
		$outPut = Bd_Pic::pid2Url ( $arrInput, false );
		if ($outPut === false || empty ( $outPut ['resps'] [0] )) {
			return '';
		}
		return $outPut ['resps'] [0];
	}
}


