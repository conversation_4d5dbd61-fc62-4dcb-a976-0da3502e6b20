<?php
/**
 * 获取最右推荐页数据
 */

//执行方式 cd $ORP_PATH ;  php app/msgpush/scripts/detectTimingTask.php  调整目录,add by magong<PERSON><EMAIL>

define ('EASYSCRIPT_DEBUG',false);          //debug 模式
define ('EASYSCRIPT_THROW_EXEPTION',true);  //抛异常模式

define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../../' );
define ('SCRIPTNAME',basename(__FILE__,".php"));   //定义脚本名
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf");
define ('DATAPATH',BASEPATH."/data");
define ('LOGPATH',"./log/common/scripts/grad");
define('IS_ORP_RUNTIME', true);
set_include_path(get_include_path() . PATH_SEPARATOR. BASEPATH.'/../../');
require_once ROOT_PATH . "app/common/script/grab/lib/util.php";

/**
 * 获取当前毫秒级时间戳
 * @param：null
 * @return：string
 */
function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');

/**
 * @param
 * @return
 */
function statLog($strLog){
	$log_path = ROOT_PATH . "/log/stlog/";
	if(!file_exists($log_path)){
		mkdir($log_path, 0777, true);
	}
	$strLogFileName = $log_path . 'wap' . "." . strftime('%Y%m%d%H');
	$logStr = 'NOTICE: ' . strftime('%m-%d %H:%M:%S') . ':  stat-log' . ' * ' .  posix_getpid() . " " . $strLog . "\n";
	file_put_contents($strLogFileName, $logStr, FILE_APPEND);
}

/**
 * @param
 * @return
 */
function printLog($strLog, $type = 'n', $bolEcho = true){
    if(empty($type)){
        return ;
    }
    if('n' == $type){
        Bingo_Log::notice($strLog);
    }else if('w' == $type){
        Bingo_Log::warning($strLog);
    }else if('f' == $type){
        Bingo_Log::fatal($strLog);
    }
    if($bolEcho){
        echo $strLog."\n";
    }
}

define ('LOG', 'log');
Bingo_Log::init(array(
    LOG => array(
        'file'  => LOGPATH ."/". SCRIPTNAME. ".log",
        'level' => 0x0f,
    ),
), LOG);

$easyScriptObj  = false;
try{
    printLog('tieba_getZuiYouVideoGrab script ready run.');
	$ScriptConf = array(
        'memory_limit'   => '1024M',
        'data_path'      => DATAPATH,
        'conf_path'      => CONFPATH,
        'lock_file_name' => SCRIPTNAME.'.lock',
        'done_file_name' => SCRIPTNAME.'.done',
        'db_alias'       => array(
            'im'     => 'forum_live',
        ),
        'conf_alias'     => array(
            'main'     => 'main.conf',
        ),
    );

	$easyScriptObj = new Util_EasyScript($ScriptConf);
    //防止脚本重复执行
    if( $easyScriptObj->checkScriptIsRuning() === true ){
        printLog('tieba_getZuiYouVideoGrab is runing.');
        exit;
    }
    printLog('tieba_getZuiYouVideoGrab script begin runing.');
    $lastRunTime = 0;
    $lastRunTime = $easyScriptObj->getLastSaveData('last_run_time', 0);
    $runCount = 0;
    $runCount = $easyScriptObj->getLastSaveData('script_run_count', 0);
    
    $zuiYouVideo = new getZuiYouVideoGrabAction();
    $zuiYouVideo->_execute($lastRunTime, $runCount);
    
    $easyScriptObj->addSaveData('last_run_time', $lastRunTime);
    $easyScriptObj->addSaveData('script_run_count', $runCount);
    $easyScriptObj->runSuccess();
    printLog('tieba_getZuiYouVideoGrab script run success.');
}catch(Exception $e){
    if($easyScriptObj !== false){
       printLog('tieba_getZuiYouVideoGrab script run fail!'.$easyScriptObj->getErr2String(), 'w');
    }else{
       printLog('tieba_getZuiYouVideoGrab script run fail![null]', 'w');
    }
    printLog('tieba_getZuiYouVideoGrab run fail.'.$easyScriptObj->getErr2String());
    exit;
}
printLog('tieba_getZuiYouVideoGrab run finish.');

class getZuiYouVideoGrabAction {
	
	CONST MAX_WIDTH = 570;
	CONST MAX_HEIGHT = 6000;
	CONST CURL_DATA = "{\"h_model\":\"iPhone 6s\",\"h_ch\":\"appstore\",\"h_ts\":1500952782877,\"h_av\":\"3.5.9\",\"tab\":\"video\",\"h_did\":\"9230966b22d9a14118ca2fbf0eb625efe1f78b0d\",\"filter\":\"video\",\"h_m\":22179972,\"h_os\":\"10.000000\",\"h_nt\":1,\"token\":\"T9K7Ns1bZrdSNFpwHHYvPgeIJ3mjOfU716-Jxlp0IrghoTV4=\",\"h_dt\":1,\"direction\":\"down\"}";
	CONST CURL_POSTFER = "' 'http://tbapi.ixiaochuan.cn/index/recommend?sign=917edf15758685c44a806061da68a113   '  -x ************:8080 ";
	CONST CURL_PREFER = "curl -i -s -k  -X 'POST' \
                        -H 'Content-Type: application/json' -H 'ZYP: mid=22179972' -H 'User-Agent: tieba/3.5.9 (iPhone; iOS 10.0.2; Scale/2.00)' -H 'Host:tbapi.ixiaochuan.cn' \
                        -b 'aliyungf_tc=AQAAAOwSR2Q9Dw4AWKmHPRZWXccCTPwD' \
                        --data-binary $'";
	
	
	private static $strPath='./imgs';
	const BOS_BUCKET = "tieba-smallvideo-spider";
	const BOS_BLOCK_SIZE = 2097152;
	
	/**
	 * @param unknown 
	 * @return NULL|unknown
	 */
	public static function _execute(&$lastRunTime, &$runCount){

		
// 		脚本三分之一概率执行
		$arrBoolExc=array(false,true,false);
		$boolIndex =array_rand($arrBoolExc,1);
		if($arrBoolExc[$boolIndex]==false){
			Bingo_Log::warning ('tieba_getZuiYouVideoGrab script no exec');
			var_dump('tieba_getZuiYouVideoGrab script no exec');
			return ;
		}
		$url=self::CURL_PREFER . self::CURL_DATA . self::CURL_POSTFER;
		
		var_dump($url);
		if(empty($url)){
			return ;
		}
		$retData = exec($url, $ret);
		$retData = json_decode($retData, true);
		
		$arrRecData = $retData['data']['list'];
		if(empty($arrRecData) ||count($arrRecData) <=0){
			printLog('zuiyou_data_empty');
			return ;
		}
		$list = array();
		foreach($arrRecData as $oneTopic) {
			$strContent = $oneTopic ['content'];
			if (false !== strpos($strContent, '最右') ||false !== strpos($strContent, '右右') ||false !== strpos($strContent, '右友') ||false !== strpos($strContent, '小右')) {
				continue;
			}
			if (! isset ( $oneTopic ['imgs'] ) || empty ( $oneTopic ['imgs'] ) ||empty ( $oneTopic ['videos'] )){
				continue;
			}
			foreach ( $oneTopic ['imgs'] as $k => $oneImg ) {
				if (! $oneImg ['video']) {
					unset ( $oneTopic ['imgs'] [$k] );
				}
			}
			if (! empty ( $oneTopic ['imgs'] ) && count ( $oneTopic ['imgs'] ) >= 0 && ! empty ( $oneTopic ['videos'] ) ) {
				$list [$oneTopic ['id']] = $oneTopic;
			}
		}
			
			// var_dump($list);
		var_dump ( 'list==========count' );
		var_dump ( count ( $list ) );
		if (empty ( $list ) || count ( $list ) <= 0) {
			Bingo_Log::warning ( 'grab data empty' );
		}
		if (is_array ( $list )) {
			self::processList ( $list );
		}
		if (is_dir ( self::$strPath )) {
			@exec ( 'rm -rf  ' . self::$strPath );
		}
		;
		var_dump ( 'succ==========' );
		
	}
	/**
	 * @param unknown
	 * @return NULL|unknown
	 */
	public function processList($responceData) {
		var_dump($responceData);
		if(is_array($responceData)) {
			foreach($responceData as $item) {
				$content = $item['content'];
				$id = intval($item['id']);
				$arrVideoCover = $item['imgs'];
				$arrVideo= $item['videos'];
				if(empty($content) || empty($arrVideoCover) ||empty($arrVideo)){
					continue;
				}
				
				//获取视频信息
				foreach ($arrVideo as $pic => $videoInfo){
					unset($arrVideo[$pic]['h5url']);
					unset($arrVideo[$pic]['urlext']);
					unset($arrVideo[$pic]['urlsrc']);
				}
				
				var_dump('22222222222222');
				var_dump($arrVideoCover);
				var_dump($arrVideo);
				
				$arrInput= array(
						'function'=>'addGrabResource',
						'resource_id'=>$id,
						'resource_type'=>2,
						'resource_title'=>strval($content),
						'resource_title_md5' => empty($content)?'':md5($content),
						'resource_content' => '',
						'resource_cover'=>$arrVideoCover,
						'resource_video_url_list'=>$arrVideo,
						'channel' =>1,
				);
				var_dump($arrInput);
				$arrOutput = Tieba_Service::call ( 'common', 'grabResource', $arrInput);
				var_dump($arrOutput);
				if ($arrOutput === false || $arrOutput ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
					Bingo_Log::warning ( sprintf ( 'service error: %s_%s [%s] [%s]', 'common', 'getGrabResource', serialize ( $arrInput ), serialize ( $arrOutput ) ) );
				}
				
			}
		}
	}

	/**
	 * @param unknown
	 * @return NULL|unknown
	 */
	private static function _checkOutput($arrOut, $key = 'errno', $judgeValue = Tieba_Errcode::ERR_SUCCESS){
		if(null == $arrOut || empty($arrOut) || !isset($arrOut[$key]) || $judgeValue != $arrOut[$key]){
			return false;
		}
		return true;
	}
}


