<?php
/**
 * 投放抓取配置
 */

class Lib_DeliveryConf {

    const CHANNEL_UIDS_REDISKEY                 = "channel_uids_rediskey_";
    const CHANNEL_FIDS_REDISKEY                 = "channel_fids_rediskey_";

    const UIDS_AVAILABLE_REDISKEY               = "uids_available_rediskey_"; // 保存帐号当前是否可用（做投放时间频率粒度控制）
    const UIDS_AVAILABLE_TIMEOUT                = 1800;                       // 帐号投放时间频率 30min 间隔

    // 词表相关变量
    private static $CONF_TABLE                  = 'tb_wordlist_redis_common_grab';
    private static $CONF_KEY_UEG_FILTER_LIST    = 'ueg_filter_words_list';  // 过ueg策略词表列表
    private static $CONF_KEY_UEG_FILTER_SWITCH  = 'ueg_filter_switch';      // 过ueg策略开关
    private static $CONF_KEY_CHANNEL_FNAME      = 'channel_forums_list';    // 投放吧对应关系列表

    // UEG相关变量
    public static $_arrUegFilterWordsList       = array();
    public static $_intUegFilterSwitch          = 1; //默认打开

    // 投放相关
    public static $_isDeliveryNoForum           = 0; // 是否无吧投放（1、是;0、否）

    // redis相关变量
    public static $redisFidKey                  = null;
    public static $redisUidKey                  = null;
    private static $redisKeySeconds             = 86400;

    public static $arrKeyConf                   = array();
    public static $arrFidKey                    = array();
    public static $arrUidKey                    = array();

    public static $arrAllReplaceKeyWords = array(//全量替换关键词
        '小编' => '我',
        '右友' => '吧友',
        '段友' => '吧友',
    );
    public static $arrReplaceKeyWords = array(//需要替换的关键字词组
        '2' => array('段友' => '吧友'),// channel oldkeyWord newKeyWord
        '75' => array('特别声明：本文为网易自媒体平台“网易号”作者上传并发布，仅代表该作者观点。网易仅提供信息发布平台。' => ''),
    );

    /**
     * @param null
     * @return null
     */
    public function __construct(){

    }


    /**
     * @param null
     * @return boolean
     */
    public static function _getConf(){
        // 输出字段
        $arrOutput = array();

        $handleWordServer = Wordserver_Wordlist::factory();
        $arrKeys = array(
            self::$CONF_KEY_UEG_FILTER_LIST,
            self::$CONF_KEY_UEG_FILTER_SWITCH,
            self::$CONF_KEY_CHANNEL_FNAME,
        );
        $arrItemInfo = $handleWordServer->getValueByKeys($arrKeys, self::$CONF_TABLE);
        if(false == $arrItemInfo || empty($arrItemInfo)){
            printLog('get wordlist conf fail.input:[' . serialize($arrKeys) . '],output:[' . serialize($arrItemInfo) . ']', 'n', true);
            return false;
        }
        if(isset($arrItemInfo[self::$CONF_KEY_UEG_FILTER_LIST]) && !empty($arrItemInfo[self::$CONF_KEY_UEG_FILTER_LIST])){
            self::$_arrUegFilterWordsList = unserialize($arrItemInfo[self::$CONF_KEY_UEG_FILTER_LIST]);
        }
        if(isset($arrItemInfo[self::$CONF_KEY_UEG_FILTER_SWITCH]) && !empty($arrItemInfo[self::$CONF_KEY_UEG_FILTER_SWITCH])){
            self::$_intUegFilterSwitch = intval($arrItemInfo[self::$CONF_KEY_UEG_FILTER_SWITCH]);
        }

        if(isset($arrItemInfo[self::$CONF_KEY_CHANNEL_FNAME]) && !empty($arrItemInfo[self::$CONF_KEY_CHANNEL_FNAME])){
            $arrConfs = unserialize($arrItemInfo[self::$CONF_KEY_CHANNEL_FNAME]);
            foreach ($arrConfs as $item) {
                $arrItem = explode(',', $item);
                if (null == $arrItem || empty($arrItem) || count($arrItem) < 2) {
                    continue;
                }
                $channel = 0;
                $strChannel = strval($arrItem[0]);
                if (false !== strpos($strChannel, '_')) {
                    //有sitetab
                    $arrtmp = explode('_', $strChannel);
                    $channel = $arrtmp[0];
                    if (false !== strpos($channel, '|')) {
                        //批量多个channel，以逗号隔开
                        $channel = str_replace('|', ',', $channel);
                    } else {
                        $channel = intval($arrtmp[0]);
                    }
                } else {
                    $channel = intval($strChannel);
                }
                if (empty($channel) || empty($arrItem[1]) || empty($arrItem[2])) {
                    printLog('invalid config.channel:[' . $channel . '] fid:[' . $arrItem[1] . '] uid:[' . $arrItem[2] . ']', 'n', true);
                    continue;
                }
                self::$arrKeyConf[$channel]   = strval($arrItem[1]);
                self::$arrFidKey[$channel]    = strval($arrItem[1]);
                self::$arrUidKey[$channel]    = strval($arrItem[2]);
            }
            if(empty(self::$arrFidKey) || empty(self::$arrUidKey)){
                return false;
            }

            // 整理吧数据
            $arrFnameList   = array();
            $arrFidList     = array();
            $arrFInfo = $handleWordServer->getValueByKeys(self::$arrFidKey, self::$CONF_TABLE);
            foreach (self::$arrFidKey as $key => $value){
                if(null == $arrFInfo || empty($value) || !isset($arrFInfo[$value]) || empty($arrFInfo[$value])){
                    continue;
                }
                $arrForums = explode(',', $arrFInfo[$value]);
                if(empty($arrForums)){
                    continue;
                }
                $arrKey = explode('_', $value);
                if(empty($arrKey) || 2 != count($arrKey)){
                    continue;
                }
                if('fids' == $arrKey[1]){
                    //不需要转换的吧id列表
                    $arrFidList[$value] = $arrForums;
                }else if('fnames' == $arrKey[1]){
                    $arrFnameList[$value] = $arrForums;
                }else{
                    $arrFnameList[$value] = $arrForums;
                }
            }
            //把吧名转成吧id
            $arrFNameInfo = array();
            if(!empty($arrFnameList)){
                foreach ($arrFnameList as $key => $arrFnames){
                    $arrFids = self::_getFidsByFnames($arrFnames);
                    if(null == $arrFids || empty($arrFids)){
                        continue;
                    }
                    $arrFNameInfo[$key] = $arrFids;
                }
            }
            if(!empty($arrFNameInfo)){
                $arrFidList = array_merge($arrFidList, $arrFNameInfo);
            }
            foreach (self::$arrKeyConf as $channel => $newKey){
                if(!isset($arrFidList[$newKey]) || empty($arrFidList[$newKey])) {
                    continue;
                }
                $arrOutput[$channel]['fid_list'] = $arrFidList[$newKey];
                //判断key 在redis 是否存在
                self::$redisFidKey = self::CHANNEL_FIDS_REDISKEY . $channel . '_' . self::$arrKeyConf[$channel];
                $intExists = self::_isExistsGetRedis(self::$redisFidKey);
                if(!$intExists){
                    // 第一次需要，初始化redis
                    $arrMembers = array();
                    foreach ($arrOutput[$channel]['fid_list'] as $rowInfo) {
                        $arrMembers[] = array (
                            'member' => $rowInfo,
                            'score'  => 0,
                        );
                    }
                    self::_zaddRedis(self::$redisFidKey, $arrMembers);
                }
            }

            // 整理用户数据
            if(empty(self::$arrUidKey)){
                return false;
            }
            $arrUidList = $handleWordServer->getValueByKeys(self::$arrUidKey, self::$CONF_TABLE);
            foreach (self::$arrUidKey as $key => $uidKey){
                if(null == $arrUidList || empty($uidKey) || !isset($arrUidList[$uidKey]) || empty($arrUidList[$uidKey])){
                    continue;
                }
                $arrOutput[$key]['uid_list'] = explode(',', $arrUidList[$uidKey]);

                //先判断redis key 存在不，如果存在直接从redis 中取符合条件的，否则从词表取，并写入redis 中
                self::$redisUidKey = self::CHANNEL_UIDS_REDISKEY . $channel . '_' . self::$arrUidKey[$key];
                $intExists = self::_isExistsGetRedis(self::$redisUidKey);
                if(!$intExists){
                    //第一次需要，初始化redis
                    $arrMembers=array();
                    foreach ($arrOutput[$key]['uid_list'] as $rowInfo){
                        $arrMembers[] = array(
                            'member' => $rowInfo,
                            'score'  => 0,
                        );
                    }
                    self::_zaddRedis(self::$redisUidKey, $arrMembers);
                }
            }
        }

        return $arrOutput;
    }
    /**
     * @param unknown $arrOut
     * @param string $key
     * @return boolean
     */
    private static function _expireRedis($rediskey,$seconds){
        $arrInput = array(
            'function' => 'callGrabRedis',
            'call_method' => 'EXPIRE',
            'param' => array(
                'key' => $rediskey,
                'seconds' => $seconds,
            ),
        );
        $retryCount=3;
        while($retryCount-->0){
            $arrOut = Tieba_Service::call ( 'common', 'grabResource', $arrInput );
            if(!self::_checkOutput($arrOut)){
                printLog('call grabResource callGrabRedis fail.input:['.serialize($arrInput).'] arrOut:['.serialize($arrOut).'] retrycount:['.$retryCount.']');
                sleep(1);
                continue;
            }
            $ret = $arrOut['data'];
            if(!self::_checkOutput($ret, 'err_no')){
                printLog('call callGrabRedis fail.input:['.serialize($arrInput).'] ret:['.serialize($ret).'] retrycount:['.$retryCount.']');
                sleep(1);
                continue;
            }
            break;
        }
    }
    /**
     * @param unknown $arrOut
     * @param string $key
     * @return boolean
     */
    private static function _zaddRedis($rediskey,$arrMembers){
        $arrInput = array(
            'function' => 'callGrabRedis',
            'call_method' => 'ZADD',
            'param' => array(
                'key' => $rediskey,
                'members' => $arrMembers,
            ),
        );
        $retryCount=3;
        while($retryCount-->0){
            $arrOut = Tieba_Service::call ( 'common', 'grabResource', $arrInput );
            if(!self::_checkOutput($arrOut)){
                printLog('call grabResource callGrabRedis fail.input:['.serialize($arrInput).'] arrOut:['.serialize($arrOut).'] retrycount:['.$retryCount.']');
                sleep(1);
                continue;
            }
            $ret = $arrOut['data'];
            if(!self::_checkOutput($ret, 'err_no')){
                printLog('call callGrabRedis fail.input:['.serialize($arrInput).'] ret:['.serialize($ret).'] retrycount:['.$retryCount.']');
                sleep(1);
                continue;
            }
            self::_expireRedis($rediskey,self::$redisKeySeconds);
            break;
        }
    }
    /**
     * @param unknown $arrOut
     * @param string $key
     * @return boolean
     */
    public static function _isExistsGetRedis($rediskey){

        $arrInput = array(
            'function' => 'callGrabRedis',
            'call_method' => 'EXISTS',
            'param' => array(
                'key' => $rediskey,
            ),
        );
        $retryCount=3;
        $ret = array();
        while($retryCount-->0){
            $arrOut = Tieba_Service::call ( 'common', 'grabResource', $arrInput );
            if(!self::_checkOutput($arrOut)){
                printLog('call grabResource callGrabRedis fail.input:['.serialize($arrInput).'] arrOut:['.serialize($arrOut).'] retrycount:['.$retryCount.']');
                sleep(1);
                continue;
            }
            $ret = $arrOut['data'];
            if(!self::_checkOutput($ret, 'err_no')){
                printLog('call callGrabRedis fail.input:['.serialize($arrInput).'] ret:['.serialize($ret).'] retrycount:['.$retryCount.']');
                sleep(1);
                continue;
            }
            break;
        }
        $intExists = intval($ret['ret'][$rediskey]);
        return $intExists;
    }
    /**
     * @param unknown $arrOut
     * @param string $key
     * @return boolean
     */
    public static function _setexRedis($rediskey, $seconds){
        $arrInput = array(
            'function' => 'callGrabRedis',
            'call_method' => 'SETEX',
            'param' => array(
                'key' => $rediskey,
                'value' => time(),
                'seconds' => $seconds,
            ),
        );
        $retryCount=3;
        while($retryCount-->0){
            $arrOut = Tieba_Service::call ( 'common', 'grabResource', $arrInput );
            if(!self::_checkOutput($arrOut)){
                printLog('call grabResource callGrabRedis fail.input:['.serialize($arrInput).'] arrOut:['.serialize($arrOut).'] retrycount:['.$retryCount.']');
                sleep(1);
                continue;
            }
            $ret = $arrOut['data'];
            if(!self::_checkOutput($ret, 'err_no')){
                printLog('call callGrabRedis fail.input:['.serialize($arrInput).'] ret:['.serialize($ret).'] retrycount:['.$retryCount.']');
                sleep(1);
                continue;
            }
            break;
        }
    }
    /**
     * 
     * @param unknown $content
     * @param number $length
     * @return string
     */
    private static function _getTitleFromContent($content, $length = 60){
        if(empty($content)){
            return '';
        }
        $title = '';
        $arrContent = explode('<br />', $content);
        $subContent = strip_tags($arrContent[0]);
        if(!empty($subContent)){
            $title = Bingo_String::truncate($subContent, $length, '...', 'UTF-8');
        }
        return $title;
    }
    /**
     * @param multitype :$arrInput
     * @return boolean
     */
    public static function dealKeyWords(&$arrInput)
    {
        $channel = $arrInput['channel'];
        foreach (self::$arrAllReplaceKeyWords as $oldKeyWord => $newKeyWord) {
            $arrInput['title'] = str_replace($oldKeyWord, $newKeyWord, $arrInput['title']);
            $arrInput['content'] = str_replace($oldKeyWord, $newKeyWord, $arrInput['content']);
        }

        if (!isset(self::$arrReplaceKeyWords[$channel])) {
            return true;
        }
        foreach (self::$arrReplaceKeyWords[$channel] as $oldKeyWord => $newKeyWord) {
            $arrInput['title'] = str_replace($oldKeyWord, $newKeyWord, $arrInput['title']);
            $arrInput['content'] = str_replace($oldKeyWord, $newKeyWord, $arrInput['content']);
        }
        return true;

    }
    /**
     *统计文字数和图片数
     * @param unknown $content
     * @param number $length
     * @return string
     */
    private static function _getWordsAndPicsCnt($content)
    {
        $arrResult = array('wordsCnt'=>0,'picCnt'=>0);
        if (empty($content)) {
            return $arrResult;
        }
        $title = '';
        $arrContents = explode('<br />', $content);
        $strWords = '';
        foreach ($arrContents as $eleContent) {
            $subContent = str_replace(array("\n","\t","\r"),'',$eleContent);
            $subContent = trim($subContent);
            if(empty($subContent)){
                continue;
            }
            if(strpos($subContent, 'BDE_Image')){
                $arrResult['picCnt'] =  $arrResult['picCnt'] + 1;
            }else{
                $strWords .= $subContent;
            }
        }
        $arrResult['wordsCnt'] = self::utf8_strlen($strWords);
        return $arrResult;
    }
    /**
     * 暂时只针对图文
     * @param
     * @return
     */
    public static function _addThread($arrInput, &$isSuccess, &$thread_id, &$remark, $arrAttrInput = array()){
        if(!self::_checkParam($arrInput, 'uid', 'uname', 'fid', 'fname', 'title', 'content')
            || $arrInput['uid'] <= 0 || $arrInput['fid'] < 0
            || empty($arrInput['uname'])
            || empty($arrInput['title']) || empty($arrInput['content'])
        ){
            Bingo_Log::warning('_addThread call fail,invalid param.input:['.serialize($arrInput).']');
            return false;
        }
        if(null == $arrInput['title'] && empty($arrInput['title']) && 4 == intval($arrInput['channel'])){
            //对糗事百科没标题的进行处理
            $arrInput['title'] = self::_getTitleFromContent($arrInput['content']);
        }
        self::dealKeyWords($arrInput);
        $title = $arrInput['title'];
        $content = $arrInput['content'];
        if (empty($title) || empty($content)) {
            $remark = 'title or content is empty';
            return false;
        }
        $arrWordAndPicCnt = self::_getWordsAndPicsCnt($content);
        if ($arrWordAndPicCnt ['picCnt'] <= 0) {
            $remark = 'content has no picture';
            return false;
        }
        if (empty($content) || (strpos($content, 'BDE_Image') === false && $arrInput['channel']==1)) {
            return false;
        }
        if(false !== strpos($content, '最右') ||false !== strpos($content, '右右') ||false !== strpos($content, '右友') ||false !== strpos($content, '小右')){
            return false;
        }
        $title = is_utf8($title) ? $title : Bingo_Encode::convert($title, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        $content = is_utf8($content) ? $content : Bingo_Encode::convert($content, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        if (empty($title) || empty($content)){
            Bingo_Log::warning('_addThread call fail,title or content invalid.input:['.serialize($arrInput).']');
            return false;
        }

        $arrUip = array(
            '**************' => '**************',
            '**************' => '**************',
            '**************' => '**************',
            '**************' => '**************',
            '**************' => '**************',
            '**************' => '**************',
            '**************' => '**************',
            '**************' => '**************',
            '**************' => '**************',
            '**************' => '**************',
        );
        $strUip = array_rand($arrUip, 1);

        $input = array (
            'user_id'   => $arrInput['uid'],
            'user_name' => $arrInput['uname'],
            'forum_id'  => $arrInput['fid'],
            'forum_name'=> $arrInput['fname'],
            'title'     => $title,
            'content'   => $content
        );
        if ($arrInput['channel'] != 74) {
        	$input ['inner_type'] = 6;
        }
        $input['product_private_key'] = 'tieba_pc';
        $input['user_ip'] = ip2long($strUip);
        $input['create_time'] = time();
        $input['vcode_free_gate'] = true;
        if ($input['forum_id'] == 0) {
            $input['forum_name'] = '';
            $input['is_multi_forum'] = 1;
        }
        $input['ext_attr'][] = array('key'=>$arrAttrInput['key'],'value'=>$arrAttrInput['val']);
        $isSuccess=1;
        Bingo_Log::warning("addThread___input:" . serialize($input));
        $arrOut = Tieba_Service::call('post', 'addThread', array ('req' => $input), null, null, 'post', 'php', 'utf-8');
        Bingo_Log::warning("addThread___output:" . serialize($arrOut));
        if (!self::_checkOutput($arrOut)) {
            Bingo_Log::warning('call post addThread fail.input:['.serialize($input).'] out:['.serialize($arrOut).']');
            $isSuccess=0;
        }
        $thread_id  = $arrOut['res']['thread_id'];
        $post_id    = $arrOut['res']['post_id'];

        /*if (!empty($arrAttrInput)) {
            self::setFrsAndPbAttr($arrAttrInput, intval($thread_id), intval($post_id));
           
        }*/
        $delayTime = 15 + rand() % 30;
        sleep($delayTime);

        if(isset($arrOut['errno'])){
            $remark = Molib_Client_Error::getErrMsg($arrOut['errno']);
        }
        return true;
    }

    /**
     * @param unknown $arrOut
     * @return array
     */
    public static function setFrsAndPbAttr($arrInput, $intThreadId, $intPostId){
        $arrParamsPb  = array();
        $arrParamsFrs = array();

        if( empty ($arrInput) ){
            Bingo_Log::warning("param is invalid.");
            return false;
        }
        foreach($arrInput as $item){
            if (intval($item['type']) == 0){
                $arrParamsPb[] = array(
                    'fkey'   => $item['key'],
                    'value'  => $item['val'],
                );
                $arrParamsFrs[] = array(
                    'fkey'   => $item['key'],
                    'value'  => $item['val'],
                );
            }
            if (intval($item['type']) == 1){
                $arrParamsFrs[] = array(
                    'fkey'   => $item['key'],
                    'value'  => $item['val'],
                );
            }
            if (intval($item['type']) == 2){
                $arrParamsPb[] = array(
                    'fkey'   => $item['key'],
                    'value'  => $item['val'],
                );
            }

        }

        $arrInputParaPb = array(
            array(
                'pid'    => $intPostId,
                'fields' => $arrParamsPb,
            ),
        );
        $arrInputPb = array(
            'input' => $arrInputParaPb,
        );

        $arrInputParaFrs = array(
            array(
                'tid'   => $intThreadId,
                'fields' => $arrParamsFrs,
            ),
        );
        $arrInputFrs = array(
            'input' => $arrInputParaFrs,
        );

        //multi call service
        $objMulti = new Tieba_Multi('set_key');
        if (!empty($arrParamsPb)){

            $arrInput = array(
                'serviceName'=>'post',
                'method' => 'setKeyInPostInfoEx',
                'input'  => $arrInputPb,
            );
            $objMulti->register('set_pb', new Tieba_Service('post'), $arrInput);
        }

        if (!empty($arrParamsFrs)){
            $arrInput = array(
                'serviceName'=>'post',
                'method' => 'setKeyInThreadInfo',
                'input'  => $arrInputFrs,
            );
            $objMulti->register('set_frs', new Tieba_Service('post'), $arrInput);
        }
        $objMulti->call();

        if (!empty($arrParamsPb)){
            $arrResponse = $objMulti->getResult('set_pb');
            if (Tieba_Errcode::ERR_SUCCESS != $arrResponse['errno']) {
                Bingo_Log::warning('set_pb fail response:'.json_encode($arrResponse));
            }
        }
        if (!empty($arrParamsFrs)){
            $arrResponse = $objMulti->getResult('set_frs');
            if (Tieba_Errcode::ERR_SUCCESS != $arrResponse['errno']) {
                Bingo_Log::warning('set_frs fail response:'.json_encode($arrResponse));
            }
        }

    }

    /**
     * @param string $key
     * @return boolean
     */
    public static function _zrangebyscorewithscores($redisKey,$min,$max){
        $arrInput = array(
            'function' => 'callGrabRedis',
            'call_method' => 'ZRANGEBYSCOREWITHSCORES',
            'param' => array(
                'key' => $redisKey,
                'min' => $min,
                'max' => $max,
            ),
        );
        $retryCount=3;
        while($retryCount-->0){
            $arrOut = Tieba_Service::call ( 'common', 'grabResource', $arrInput );
            if(!self::_checkOutput($arrOut)){
                printLog('call grabResource callGrabRedis fail.input:['.serialize($arrInput).'] arrOut:['.serialize($arrOut).'] retrycount:['.$retryCount.']');
                sleep(1);
                continue;
            }
            $ret = $arrOut['data'];
            if(!self::_checkOutput($ret, 'err_no')){
                printLog('call callGrabRedis fail.input:['.serialize($arrInput).'] ret:['.serialize($ret).'] retrycount:['.$retryCount.']');
                sleep(1);
                continue;
            }
            break;
        }
        $arrRetData=array();
        //处理返回的id
        if(empty($ret['ret'][$redisKey])){
            return null;
        }
        foreach ($ret['ret'][$redisKey] as $row){
            $arrRetData[]=$row['member'];
        }
        return $arrRetData;

    }
    /**
     * @param null
     * @return boolean
     */
    public static function _zincrbyRedis($rediskey,$member){
        //先判断key是否存在，如果不存在，需要给key 设置失效时间
        $isExits=self::_isExistsGetRedis($rediskey);
        if($isExits){
            $arrInput = array(
                'function' => 'callGrabRedis',
                'call_method' => 'ZINCRBY',
                'param' => array(
                    'key' => $rediskey,
                    'step' => 1,
                    'member' => $member,
                ),
            );
            $arrOut = Tieba_Service::call ( 'common', 'grabResource', $arrInput );
            if(!self::_checkOutput($arrOut)){
                printLog('call grabResource callGrabRedis fail.input:['.serialize($arrInput).'] arrOut:['.serialize($arrOut).']');
            }
        }

    }

    /**
     *
     * @param unknown $arrFnames
     * @return bool
     */
    private static function _getFidsByFnames($arrFnames){
        if(empty($arrFnames)){
            return null;
        }
        $forumInput = array(
            'query_words' => $arrFnames,
        );
        $forumOutput = Tieba_Service::call('forum', 'getFidByFname', $forumInput, null, null, 'post', 'php', 'utf-8');
        if (!$forumOutput || $forumOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            printLog('call forum:getFidByFname fail, input:[' . serialize($forumInput) . '],output:[' . serialize($forumOutput).']', 'w', false);
            return null;
        }
        $arrFids = array();
        foreach ($forumOutput['forum_id'] as $foruminfo){
            if(intval($foruminfo['forum_id']) <= 0){
                continue;
            }
            $arrFids[] = intval($foruminfo['forum_id']);
        }
        return $arrFids;
    }

    /**
     *
     * @param unknown $content：待检测content
     * @param unknown $arrUegFilterWordList：ueg词表列表
     * @return boolean：true-需要过滤不投，false-可以投放
     */
    public static function _needFilterContentByUegWords($content, $arrUegFilterWordList, &$msg){
        if(0 == self::$_intUegFilterSwitch){
            Bingo_Log::warning('ueg filter switch is shutdown.swtich:['.self::$_intUegFilterSwitch.']');
            return false;
        }
        if(empty($content) || empty($arrUegFilterWordList)){
            Bingo_Log::warning('current content no need filter,content:['.$content.'] arrUegList:['.serialize($arrUegFilterWordList).']');
            return false;
        }
        $keyPrefix = 'grab_ueg_filter_call_';
        $arrReqParamsByIndex = array();
        $arrUegFilterWordsChunks = array_chunk($arrUegFilterWordList, 3);
        foreach ($arrUegFilterWordsChunks as $index => $arrUegChk){
            $arrCallUegInput = array(
                "req" => array(
                    "confilter_type" => "Confilter",
                    "reqs" => array(),
                ),
            );
            foreach ($arrUegChk as $uegword){
                if(empty($uegword)){
                    Bingo_Log::warning('current ueg work invalid.chk:['.serialize($arrUegChk).']');
                    continue;
                }
                $param = array(
                    "groupid" => -1,
                    "content" => $content,
                    "return_position" => "no",
                    "no_normalize" => "yes",
                    "dictlist" => $uegword,
                );
                $arrCallUegInput['req']['reqs'][] = $param;
            }
            if(empty($arrCallUegInput['req']['reqs'])){
                Bingo_Log::warning('invalid ueg call param:['.serialize($arrCallUegInput).'] chk:['.serialize($arrUegChk).']');
                continue;
            }
            $arrReqParam = array();
            $arrReqParam['serviceName'] = 'anti';
            $arrReqParam['method'] = 'antiConfilter';
            $arrReqParam['input'] = $arrCallUegInput;
            $arrReqParam['ie'] = 'gbk'; //'utf-8';
            $arrReqParamsByIndex[$keyPrefix.$index] = $arrReqParam;
        }
        if(empty($arrReqParamsByIndex)){
            Bingo_Log::warning('invalid ueg multi call param.arrReqParamsByIndex:['.serialize($arrReqParamsByIndex).']');
            return false;
        }
        $arrOutList = Util_BatchMulticall::multiCallService($arrReqParamsByIndex, 'grab_batch_multi_call');
        if(false === $arrOutList || empty($arrOutList) || !is_array($arrOutList)){
            Bingo_Log::warning('invalid ueg multi call param.arrReqParamsByIndex:['.serialize($arrReqParamsByIndex).']');
            return false;
        }
        foreach ($arrUegFilterWordsChunks as $newIndex => $arrUegChk){
            $arrOut = $arrOutList[$keyPrefix.$newIndex];
            if(!self::_checkOutput($arrOut)){
                Bingo_Log::warning('call anti antiConfilter fail.param:['.serialize($arrReqParamsByIndex[$keyPrefix.$newIndex]).'] out:['.serialize($arrOut).']');
                continue;
            }
            if(!self::_checkOutput($arrOut['res'], 'err_no')){
                Bingo_Log::warning('call anti antiConfilter return fail.index:['.$keyPrefix.$newIndex.'] param:['.serialize($arrReqParamsByIndex[$keyPrefix.$newIndex]).'] out:['.serialize($arrOut).']');
                continue;
            }
            foreach ($arrOut['res']['ans'] as $indexO => $wordObj){
                if(!is_object($wordObj)){
                    Bingo_Log::warning('ueg return data is not a object.wordObj:['.serialize($wordObj).']');
                    continue;
                }
                if(0 != $wordObj->count || !empty($wordObj->words)){
                    //命中ueg策略关键字
                    $wordName = $arrReqParamsByIndex[$keyPrefix.$newIndex]['input']['req']['reqs'][$indexO]['dictlist'];
                    $msg = '正文或标题命中ueg过滤词表:['.$wordName.']策略，命中词列表：['.$wordObj->words.']';
                    Bingo_Log::warning('this content hit ueg keywords,it is filter.content:['.$content.'] words:['.$wordObj->words.'] wcount:['.$wordObj->count.']');
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * @param unknown $arrOut
     * @return array
     */
    public function getFnameByFid($arrFid) {
        $arrOutput = array();
        $input = array (
            'forum_id' => $arrFid,
        );
        $output = Tieba_Service::call('forum', 'getFnameByFid', $input, null, null, 'post', 'php', 'utf-8');
        foreach ($output['forum_name'] as $k => $v) {
            $arrOutput[$k] = $v['forum_name'];
        }
        return $arrOutput;
    }

    /**
     * @param unknown $arrOut
     * @return array
     */
    public function getUnameByUid($arrUid) {
        $arrOutput = array();
        $input = array (
            'user_id' => $arrUid,
        );
        $output = Tieba_Service::call('user', 'getUnameByUids', $input, null, null, 'post', 'php', 'utf-8');
        foreach ($output['output']['unames'] as $k => $v) {
            $arrOutput[$v['user_id']] = $v['user_name'];
        }
        return $arrOutput;
    }


    /**
     * 值输出校验
     * @param unknown $arrOut
     * @param string $key
     * @return boolean
     */
    private static function _checkOutput($arrOut, $key = 'errno', $judgeValue = Tieba_Errcode::ERR_SUCCESS){
        if(null == $arrOut || empty($arrOut) || !isset($arrOut[$key]) || $judgeValue != $arrOut[$key]){
            return false;
        }
        return true;
    }
    /**
     * 参数校验（只校验存在性，不校验具体值）
     * @param multitype:$arrInput, key1, key2, ...keyn
     * @return boolean
     */
    private static function  _checkParam(){
        $args = func_get_args();
        if(null == $args || empty($args) || null == $args[0] || empty($args[0]) || !is_array($args[0])){
            return false;
        }
        $arrInput = $args[0];
        $count = count($args);
        for ($i = 1; $i < $count; $i++){
            if(!isset($arrInput[$args[$i]])){
                return false;
            }
        }
        return true;
    }
}


