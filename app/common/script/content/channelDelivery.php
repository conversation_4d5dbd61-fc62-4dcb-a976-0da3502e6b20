<?php
/**
 * 渠道数据推送
 */

define('EASYSCRIPT_DEBUG', false);          //debug 模式
define('EASYSCRIPT_THROW_EXEPTION', true);  //抛异常模式

define('ROOT_PATH', dirname(__FILE__) . '/../../../../');
define('SCRIPTNAME', basename(__FILE__, ".php"));   //定义脚本名
define('BASEPATH', dirname(__FILE__));
define('CONFPATH', BASEPATH . "/conf");
define('DATAPATH', BASEPATH . "/data");
define('LOGPATH', "./log/common/script/content");
define('IS_ORP_RUNTIME', true);
set_include_path(get_include_path() . PATH_SEPARATOR . BASEPATH . '/../../');

//require_once ROOT_PATH . "app/common/script/content/lib/bos/Util.php";


/**
 * 获取当前毫秒级时间戳
 * @param：null
 * @return：string
 */
function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) . '.php';
}

spl_autoload_register('__autoload');

/**
 * @param
 * @return
 */
function statLog($strLog)
{
    $log_path = ROOT_PATH . "/log/stlog/";
    if (!file_exists($log_path)) {
        mkdir($log_path, 0777, true);
    }
    $strLogFileName = $log_path . 'wap' . "." . strftime('%Y%m%d%H');
    $logStr = 'NOTICE: ' . strftime('%m-%d %H:%M:%S') . ':  stat-log' . ' * ' . posix_getpid() . " " . $strLog . "\n";
    file_put_contents($strLogFileName, $logStr, FILE_APPEND);
}

/**
 *
 * @param unknown $strLog
 * @param string $type
 * @param string $bolEcho
 * @return null
 */
function printLog($strLog, $type = 'n', $bolEcho = true)
{
    if (empty($type)) {
        return;
    }
    if ('n' == $type) {
        Bingo_Log::notice($strLog);
    } else if ('w' == $type) {
        Bingo_Log::warning($strLog);
    } else if ('f' == $type) {
        Bingo_Log::fatal($strLog);
    }
    if ($bolEcho) {
        echo $strLog . "\n";
    }
}

$modCount = -1;
$modBase = 10;

if (null != $argv && !empty($argv) && is_array($argv) && 3 == count($argv)) {
    $modBase = intval($argv[1]);
    $modCount = intval($argv[2]);
} else {
    printLog('channelDeliveryScript  script get invaild input param.argv:[' . serialize($argv) . ']', 'w');
}

$strScriptName = 'channelDeliveryScript';
if (0 <= $modCount) {
    $strScriptName = $strScriptName . '_' . $modCount;
} else {
    $modCount = 'all';
}


define('LOG', 'log');
Bingo_Log::init(array(
    LOG => array(
        'file' => LOGPATH . "/" . $strScriptName . ".log",
        'level' => 0x0f,
    ),
), LOG);

$easyScriptObj = false;
try {
    printLog('channel push script ready run.');
    $ScriptConf = array(
        'memory_limit' => '1024M',
        'data_path' => DATAPATH,
        'conf_path' => CONFPATH,
        'lock_file_name' => $strScriptName . '.lock',
        'done_file_name' => $strScriptName . '.done',
        'db_alias' => array(
            'im' => 'forum_xiaoying',
        ),
        'conf_alias' => array(
            'main' => 'main.conf',
        ),
    );

    $easyScriptObj = new Util_EasyScript($ScriptConf);
    //防止脚本重复执行
    if ($easyScriptObj->checkScriptIsRuning() === true) {
        printLog('channel push script is runing.');
        exit;
    }
    printLog('channel push script begin runing.');

    $channelPutterObj = new contentChannelDeliveryAction();
    $channelPutterObj->_execute($modCount, $modBase);
    $easyScriptObj->runSuccess();
    printLog('channel push script run success.');
} catch (Exception $e) {
    if ($easyScriptObj !== false) {
        printLog('channel push script run fail!' . $easyScriptObj->getErr2String(), 'w');
    } else {
        printLog('channel push script run fail![null]', 'w');
    }
    printLog('channel push run fail.' . $easyScriptObj->getErr2String());
    exit;
}
printLog('video channel push run finish.');


class contentChannelDeliveryAction
{

    private static $CONF_TABLE = 'tb_wordlist_redis_common_content';
    private static $CONF_KEY_TIME_INTERVAL = 'time_interval';
    private static $callTimeInterval = 10;  //ms，并发调用间隔
    private static $intClientIp = 0;
    const YESTERDAYDATA = 2;
    const TODAYDATA = 1;
    const DEFAULT_DELIVER_DATA_COUNT = 10;
    const THREAD_URL_PREFIX = 'http://tieba.baidu.com/p/';

    /**
     * @param null
     * @return boolean
     */
    private static function _checkParam()
    {
        $args = func_get_args();
        if (null == $args || empty($args) || null == $args[0] || empty($args[0]) || !is_array($args[0])) {
            return false;
        }
        $arrInput = $args[0];
        $count = count($args);
        for ($i = 1; $i < $count; $i++) {
            if (!isset($arrInput[$args[$i]])) {
                return false;
            }
        }
        return true;
    }

    /**
     *
     * @param unknown $arrOut
     * @param string $key
     * @return boolean
     */
    private static function _checkOutput($arrOut, $key = 'errno')
    {
        if (null == $arrOut || empty($arrOut) || !isset($arrOut[$key]) || Tieba_Errcode::ERR_SUCCESS != $arrOut[$key]) {
            return false;
        }
        return true;
    }

    public function __construct()
    {
        self::$intClientIp = ip2long('127.0.0.1');
    }

    /**
     *
     * @param string $runModFlag
     * @return boolean
     */
    public static function _execute($runModFlag = 'all', $modBase = 10)
    {
        self::_getConf();
        if ($runModFlag === 'all') {
            printLog('command line params error', 'n', true);
            return false;
        }
        $arrAvailableDistributeJobList = self::_getAvailableDistributeJobConfigs($runModFlag, $modBase);
        foreach ($arrAvailableDistributeJobList as $arrDistributeJobInfo) {
            if ($arrDistributeJobInfo['data_count'] <= 0) {
                printLog('Do not need deliver data arrDistributeJobInfo:[' . serialize($arrDistributeJobInfo) . ']', 'w');
                continue;
            }
            foreach($arrDistributeJobInfo['delivery_status_list'] as $intDelivery_status){
            //获取当期需要推送的频道数据
            $arrInput = array(
                'delivery_id' => $arrDistributeJobInfo['delivery_id'],
                'offset' => 0,
                'is_deliver' => 0,
                'res_num' => $arrDistributeJobInfo['data_count'],
                'delivery_status' => $intDelivery_status,
            );
            if (isset($arrDistributeJobInfo['job_desc']) && !empty($arrDistributeJobInfo['job_desc'])) {
                $arrJobDesc = unserialize($arrDistributeJobInfo['job_desc']);
                if (intval($arrJobDesc['data_time']) == self::TODAYDATA) {
                    $arrInput['begin_time'] = strtotime(date('Ymd'));
                    $arrInput['end_time'] = $arrInput['begin_time'] + 60 * 60 * 24;
                } else if (intval($arrJobDesc['data_time']) == self::YESTERDAYDATA) {
                    $arrInput['begin_time'] = strtotime(date('Ymd')) - 60 * 60 * 24;
                    $arrInput['end_time'] = $arrInput['begin_time'] + 60 * 60 * 24;
                }
            }
            $arrOut = Tieba_Service::call('common', 'getDeliveryDataByDeliveryId', $arrInput);
            if (false == $arrOut || !isset($arrOut['errno']) || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
                printLog('call common getDeliveryDataByDeliveryId fail. arrinput:[' . serialize($arrInput) . '] arrOut:[' . serialize($arrOut) . ']', 'w');
            }
            if ($arrOut === false) {//远程调用失败改为本地调用
                $arrOut = Service_Content_Channel_Delivery::getDeliveryDataByDeliveryId($arrInput);
                if ($arrOut === false || !isset($arrOut['errno']) || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
                    printLog('local call common getDeliveryDataByDeliveryId fail. arrinput:[' . serialize($arrInput) . '] arrOut:[' . serialize($arrOut) . ']', 'w');
                    continue;
                }
            }
            if (empty($arrOut['data'])) {
                printLog('no have common channel delivery data.arrOut:[' . serialize($arrOut) . '] arrInput:[' . serialize($arrInput) . ']', 'w');
                continue;
            }
            printLog('channel delivery count:[' . count($arrOut['data']) . ']');
            $arrReqParamsByIndex = array();
            foreach ($arrOut['data'] as $index => $arrRow) {
                $resourceid = $arrRow['resource_id'];
                $resourceType = $arrRow['resource_type'];
                if ($resourceid <= 0 || $resourceType < 0) {
                    continue;
                }
                $structuredContent = 1;
                $strChannelName = $arrRow['channel_name'];
                if ($strChannelName == '贴吧' || '汽车之家' == $strChannelName) {
                    $structuredContent = 0;
                }
                $arrResourceInput = array(
                    'resource_id' => $resourceid,
                    'resource_type' => $resourceType,
                    'structured_content' => $structuredContent,
                    'original_data' => $arrRow,
                    'need_comment' => 1,
                );
                $arrReqParam['serviceName'] = 'common';
                $arrReqParam['method'] = 'getResourceThreadDetail';
                $arrReqParam['input'] = $arrResourceInput;
                $arrReqParam['ie'] = 'gbk'; //'utf-8';
                $arrReqParamsByIndex[$index] = $arrReqParam;
            }
            if (empty($arrReqParamsByIndex)) {
                printLog('no have valid data.arrInput:[' . serialize($arrInput) . '] arrOut:[' . serialize($arrOut) . '] param:[' . serialize($arrReqParamsByIndex) . ']');
                continue;
            }

            $arrOutList = self::_multiCallService('common', 'getResourceThreadDetail', $arrReqParamsByIndex);
            if (empty($arrOutList)) {
                printLog('multi call common getResourceThreadDetail fail.arrInput:[' . serialize($arrReqParamsByIndex) . '] arrOut:[' . serialize($arrOutList) . ']', 'w', false);
                continue;
            }
            $arrParamsByIndex = array();
            foreach ($arrOutList as $index => $arrOut) {
                $originalData = $arrReqParamsByIndex[$index]['input']['original_data'];
                if (empty($originalData) || !is_array($originalData)) {
                    printLog('no have ogiginal data.arrOut:[' . serialize($arrOut) . '] arrInput:[' . serialize($arrReqParamsByIndex[$index]) . ']', 'w', false);
                    continue;
                }
                if (!self::_checkOutput($arrOut) || empty($arrOut['data'])) {
                    printLog('getResourceThreadDetail call fail.arrOut:[' . serialize($arrOut) . '] arrInput:[' . serialize($arrReqParamsByIndex[$index]) . ']', 'w', false);
                    if (Tieba_Errcode::BAZHU_THREAD_DELETED == $arrOut['errno']) {
                        //帖子删除
                        self::_updateChannelDeliveryRec($originalData, 0, $arrOut);
                    }
                    continue;
                }

                // 删掉id,该id可能会导致数据更新错位
                if (isset($arrOut['data']['id'])) {
                    unset($arrOut['data']['id']);
                }
                $arrParam = array_merge($originalData, $arrOut['data']);
                if ($arrParam['resource_type'] != Service_Content_Base_Base::SOURCE_TYPE_TUJI) {
                    if ($arrParam['resource_type'] < Service_Content_Base_Base::SOURCE_TYPE_EDIT && isset($arrParam['thread_id']) && 0 < intval($arrParam['thread_id'])) {
                        $arrParam['resource_structure_data'] = self::getPostInfoByTid($arrParam['thread_id']);
                    }
                }
                $arrParam['update_record'] = 1;
                $arrReqParam['serviceName'] = 'common';
                $arrReqParam['method'] = 'renderContentDelivery';
                $arrReqParam['input'] = $arrParam;
                $arrReqParam['ie'] = 'gbk'; //'utf-8';
                $arrParamsByIndex[$index] = $arrReqParam;
            }

            //$arrOutList = self::_multiCallService('common', 'renderContentDelivery', $arrParamsByIndex);
            $arrOutList = array();
            foreach ($arrParamsByIndex as $key => $arrReqParam) {
                //由远程并发调用改为本地类调用，防止远程调用超时
                $arrOutList[$key] = Service_Content_Channel_Delivery::renderContentDelivery($arrReqParam['input']);
            }
            if (empty($arrOutList)) {
                printLog('multi call common renderContentDelivery fail.arrInput:[' . serialize($arrParamsByIndex) . '] arrOut:[' . serialize($arrOutList) . ']', 'w', false);
                continue;
            }
            $pushSuccessCount = 0;
            foreach ($arrOutList as $index => $arrOut) {
                if (empty($arrOut) || !isset($arrOut['errno']) || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
                    printLog('multi call common renderContentDelivery have fail.arrInput:[' . serialize($arrParamsByIndex[$index]) . '] arrOut:[' . serialize($arrOut) . ']', 'w', false);
                    continue;
                }
                //增加统计数据
                $pushSuccessCount++;
                if ((isset($arrParamsByIndex[$index]['input']['update_record']) && 0 == $arrParamsByIndex[$index]['input']['update_record'])
                    || (isset($arrOut['data']) && 1 == $arrOut['data']['update_record'])) {
                    if (isset($arrOut['data']) && !empty($arrOut['data'])) {
                        $arrRet = Tieba_Service::call('common', 'updateChannelDeliveryRec', $arrOut['data']);
                        if (null == $arrRet || !isset($arrRet['errno']) || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
                            printLog('multi call common updateChannelDeliveryRec fail.arrInput:[' . serialize($arrOut['data']) . '] arrOut:[' . serialize($arrRet) . ']', 'w', false);
                        }
                    }
                }
            }
            printLog('contentChannelDeliveryAction push success count[' . $pushSuccessCount . '].');
            printLog('contentChannelDeliveryAction video channel push run finish.');
        }
        }
        return true;
    }

    /**
     *
     * @param unknown $arrInput
     * @param unknown $isSuccess
     * @param unknown $arrMask
     * @return null
     */
    private static function _updateChannelDeliveryRec($arrInput, $isSuccess, $arrMask)
    {
        if (!self::_checkParam($arrInput, 'id') || intval($arrInput['id']) <= 0) {
            printLog('call _updateChannelDeliveryRec fail.arrInput:[' . serialize($arrInput) . '] $arrMask:[' . serialize($arrMask) . ']', 'w', false);
            return;
        }
        $arrUpdateInput = array(
            'condition' => array(
                'id' => $arrInput['id'],
            ),
            'update_fields' => array(
                'is_deliver' => 1,
                'deliver_time' => time(),
                'is_success' => $isSuccess,
                'remark' => json_encode($arrMask),
            ),
        );
        $arrRet = Tieba_Service::call('common', 'updateChannelDeliveryRec', $arrUpdateInput);
        if (null == $arrRet || !isset($arrRet['errno']) || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            printLog('call common updateChannelDeliveryRec fail.arrInput:[' . serialize($arrUpdateInput) . '] arrOut:[' . serialize($arrRet) . ']', 'w', false);
        }
    }

    /**
     * 获取帖子信息
     * @param unknown $arrInputData
     * @return NULL|unknown
     */
    private static function getPostInfoByTid($threadid)
    {
        $arrInput = array(
            'thread_id' => $threadid,
            'res_num' => 1,
            'offset' => 0,
            'see_author' => 0,
            'has_comment' => 0,
            'has_mask' => 0,
            'has_ext' => 0,
            'structured_content' => 1,
        );
        $arrOutput = Tieba_Service::call('post', 'getPostsByThreadId', $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($arrOutput === false || $arrOutput ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf('service error: %s_%s [%s] [%s]', 'post', 'getPostsByThreadId', serialize($arrInput), serialize($arrOutput)));
            return null;
        }
        $arrList = $arrOutput ['output'] ['output'] [0];
        if ($arrList['is_thread_deleted'] == 1) {
            return null;
        }
        return $arrList['post_infos'];
    }

    /**
     * _multiCallService
     * @param unknown $serviceName
     * @param unknown $strMethod
     * @param unknown $arrReqParams
     * @return boolean
     */
    private static function _multiCallService($serviceName, $strMethod, $arrReqParams)
    {
        if (empty($serviceName) || empty($strMethod) || empty($arrReqParams)) {
            printLog('_multiCallvideoPutter input param is invalid.arrInput:[' . serialize($arrReqParams) . ']', 'w');
            return array();
        }
        $arrOutList = array();
        //32个分一组进行并发调用
        $arrReqParamsChunks = array_chunk($arrReqParams, 32, true);
        printLog('_multiCallService req ' . $serviceName . ' ' . $strMethod . ' param chunk count:[' . count($arrReqParamsChunks) . ']');
        foreach ($arrReqParamsChunks as $arrChunks) {
            $objMultiCall = new Tieba_Multi('delivery_multiCallService');
            if (null == $objMultiCall) {
                printLog('_multiCallService delivery_multiCallService is fail, objMultiCall create is invalid.');
                continue;
            }
            $key = $strMethod . '_job_';
            foreach ($arrChunks as $index => $arrReqParam) {
                //注册调用
                $ret = $objMultiCall->register($key . $index, new Tieba_Service($serviceName), $arrReqParam);
                if (false == $ret) {
                    printLog('_multiCallService register fail.key:[' . $key . $index . '] param:[' . serialize($arrReqParam) . ']');
                    continue;
                }
            }
            //调用
            $objMultiCall->call();
            //获取返回值
            foreach ($arrChunks as $index => $arrReqParam) {
                $arrOut = $objMultiCall->getResult($key . $index);
                if (false == $arrOut || !isset($arrOut['errno'])) {
                    Bingo_Log::warning('_multiCallService call fail.index:[' . $index . '] arrInput:[' . serialize($arrReqParam) . '] arrOut:[' . serialize($arrOut) . ']');
                    //continue;
                }
                $arrOutList[$index] = $arrOut;
            }
            unset($objMultiCall);
            $objMultiCall = null;
            if (0 < self::$callTimeInterval) {
                usleep(self::$callTimeInterval * 1000);
            }
        }
        return $arrOutList;
    }

    /**
     *
     * @param unknown $errno
     * @param unknown $arrData
     * @return multitype:unknown NULL
     */
    protected static function _errRet($errno, $arrData = array())
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $arrData,
        );
    }

    /**
     * @param null
     * @return boolean
     */
    private static function _getConf()
    {
        $handleWordServer = Wordserver_Wordlist::factory();
        $arrKeys = array(self::$CONF_KEY_TIME_INTERVAL);
        $arrItemInfo = $handleWordServer->getValueByKeys($arrKeys, self::$CONF_TABLE);
        if (false != $arrItemInfo && !empty($arrItemInfo)
            && isset($arrItemInfo[self::$CONF_KEY_TIME_INTERVAL]) && !empty($arrItemInfo[self::$CONF_KEY_TIME_INTERVAL])) {
            $strConfTime = $arrItemInfo[self::$CONF_KEY_TIME_INTERVAL]; //使用配置的间隔
            self::$callTimeInterval = intval($strConfTime);
            return true;
        }
        return false;
    }

    /**
     * @param $intModNum
     * @param $intModBase
     * @return array
     */
    private static function _getAvailableDistributeJobConfigs($intModNum, $intModBase)
    {
        $arrAC = array();
        $maxId = 0;
        $retry = 0;
        do {
            $arrInput = array(
                'maxid' => $maxId,
                'total' => $intModBase,
                'mod' => $intModNum,
            );
            $arrRet = Tieba_Service::call('common', 'getDeliveryConfigByScript', $arrInput);
            if (!$arrRet || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call common::getDeliveryConfig error. input:' . serialize($arrInput) . '   ret:' . serialize($arrRet));
                if ($retry == 4) {
                    break;
                }
                $retry++;
                continue;
            }
            if (empty($arrRet['data'])) {
                break;
            }
            $retry = 0;

            foreach ($arrRet['data'] as &$config) {
                //筛选出 需要执行的分发任务
                if (self::_checkIsNeedExecuteDistribute($config)) {
                    $arrAC[] = $config;
                }
                $maxId = $config['id'] > $maxId ? $config['id'] : $maxId;
            }
        } while (1);
        return $arrAC;
    }

    /**
     * 检查是否需要执行分发并获取合适的分发量
     * @param $arrDeliveryInfo
     * @return bool
     */
    private static function _checkIsNeedExecuteDistribute(&$arrDeliveryInfo)
    {
        if (empty($arrDeliveryInfo['delivery_id']) || !isset($arrDeliveryInfo['job_desc'])) {
            Bingo_Log::warning('_checkIsNeedExecuteDistribute params error input' . serialize($arrDeliveryInfo));
            return false;
        }
        $arrDeliveryInfo['data_count'] =  self::DEFAULT_DELIVER_DATA_COUNT;
        $arrDeliveryInfo['delivery_status_list'] = array(0); //默认分发只需要一步
        if (empty($arrDeliveryInfo['job_desc'])) {
            return true;
        }
        $arrJobDes = unserialize($arrDeliveryInfo['job_desc']);
        if ($arrJobDes === false) {
            Bingo_Log::warning('_checkIsNeedExecuteDistribute unserialize job_desc failed!!! arrDeliveryInfo:['.serialize($arrDeliveryInfo).']');
            return false;
        }
        if(!is_array($arrJobDes) || empty($arrJobDes) || count($arrJobDes) <= 0){
            Bingo_Log::warning('delivery_id['.$arrDeliveryInfo['delivery_id'].'] use default allocation');
            return true;
        }
        //获取分发步骤
        if(isset($arrJobDes['delivery_steps']) && 0 < intval($arrJobDes['delivery_steps'])){
            $arrDeliveryInfo['delivery_status_list'] = array();
            for($i = 0; $i < intval($arrJobDes['delivery_steps']); $i++){
                $arrDeliveryInfo['delivery_status_list'][] = $i;
            }
        }
        if(!isset($arrJobDes['job_conf']) || !is_array($arrJobDes['job_conf']) || empty($arrJobDes['job_conf'])){
            return true;
        }
        $intDistributeDataDate = intval($arrJobDes['data_time']);//-1分发昨天的数据 1分发今天的数据 其他值表示对数据时间无要求
        $arrJobExeRangeList = $arrJobDes['job_conf'];
        $intCurrentTime = time();
        $curDate = getdate($intCurrentTime);
        $curYear = $curDate['year'];
        $curMon = $curDate['mon'];
        $curMday = $curDate['mday'];
        foreach ($arrJobExeRangeList as $arrJobDetail) {
            if(intval($arrJobDetail['start_time'])<=0 || intval($arrJobDetail['end_time']) <=0 ){
                printLog('invalid job conf!! arrJobConf:['.serialize($arrJobDetail).']','w');
                continue;
            }
            $tempDate = getdate($arrJobDetail['start_time']);
            $tempHour = $tempDate['hours'];
            $tempMin = $tempDate['minutes'];
            $tempSec = $tempDate['seconds'];
            $intBeginTime = strtotime("$curYear-$curMon-$curMday $tempHour:$tempMin:$tempSec");
            $tempDate = getdate($arrJobDetail['end_time']);
            $tempHour = $tempDate['hours'];
            $tempMin = $tempDate['minutes'];
            $tempSec = $tempDate['seconds'];
            $intEndTime = strtotime("$curYear-$curMon-$curMday $tempHour:$tempMin:$tempSec");
            if ($intCurrentTime >= $intBeginTime && $intCurrentTime <= $intEndTime) {
                //获取这次需要分发的数据量
                $intDistributeDataCnt = intval($arrJobDetail['data_count']);
                if (intval($intDistributeDataCnt) <= 0) {
                    if ($intDistributeDataDate == self::YESTERDAYDATA) { //昨天的数据 均量投放
                        $arrInput = array(
                            'delivery_id' => $arrDeliveryInfo['delivery_id'],
                            'begin_time' => strtotime(date('Ymd')) - 60 * 60 * 24,
                            'end_time' => strtotime(date('Ymd')),
                        );
                        $arrRet = Tieba_Service::call('common', 'getDeliveryDataCntByDeliveryId', $arrInput);
                        if (!$arrRet || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                            Bingo_Log::warning('_checkIsNeedExecuteDistribute call getDistributeDataCountByDeliveryId failed' . serialize($arrInput));
                            $arrRet = Service_Content_Channel_Delivery::getDeliveryDataCntByDeliveryId($arrInput);
                            if ($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {// 本地调用都失败 使用默认值
                                Bingo_Log::warning('_checkIsNeedExecuteDistribute local call getDistributeDataCountByDeliveryId failed' . serialize($arrInput));
                                return true;
                            }
                        }
                        $intDistributeDataCnt = ($arrRet['data'][0]['cnt'] % count($arrJobExeRangeList)) === 0 ? (intval($arrRet['data'][0]['cnt']/count($arrJobExeRangeList))) : (intval($arrRet['data'][0]['cnt']/count($arrJobExeRangeList)) + 1);
                    } else {//不对投放数据量有要求 使用默认值
                        return true;
                    }
                }
                $arrInput = array(
                    'delivery_id' => $arrDeliveryInfo['delivery_id'],
                    'begin_time' => $intBeginTime,
                    'end_time' => $intEndTime,
                );
                $arrRet = Tieba_Service::call('common', 'getDeliveryedDataCntByDeliveryId', $arrInput);
                if (!$arrRet || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('_checkIsNeedExecuteDistribute call getDeliveryedDataCntByDeliveryId failed' . serialize($arrInput));
                    $arrRet = Service_Content_Channel_Delivery::getDeliveryedDataCntByDeliveryId($arrInput);
                    if($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){// 本地调用都失败 使用默认值
                        Bingo_Log::warning('_checkIsNeedExecuteDistribute local call getDeliveryedDataCntByDeliveryId failed' . serialize($arrInput));
                        return true;
                    }
                }
                $intCrtTimeRangeDlvDataCnt = $arrRet['data'][0]['cnt'];//当前时间段已投放的数据量
                $intRealneedDlvDataCnt = $intDistributeDataCnt - $intCrtTimeRangeDlvDataCnt;
                $intRealneedDlvDataCnt = $intRealneedDlvDataCnt < self::DEFAULT_DELIVER_DATA_COUNT ? $intRealneedDlvDataCnt : self::DEFAULT_DELIVER_DATA_COUNT;
                $arrDeliveryInfo['data_count'] = $intRealneedDlvDataCnt;
                return true;
            }
        }
        return false;
    }
}