<?php
/**
 * created by xiaof<PERSON>.
 * email: <EMAIL>
 * file name: pushToAuthor.php
 * create time: 2020-08-06 17:06:32
 * describe: 给作者推送贴子被收藏信息
 */

define('MODULE_NAME', 'common');
define('ROOT_PATH', dirname(__FILE__) . '/../../../../');
define('BASE_PATH', dirname(__FILE__));
define('DATA_PATH', BASE_PATH . "/data");
define('LOG_PATH', ROOT_PATH.'log/common/script/');

function __autoload($strClassName) {

}

spl_autoload_register('__autoload');

define ('LOG', 'log');
Bingo_Log::init(array(
    LOG => array(
        'file'  => LOG_PATH .  'pushToAuthor.log',
        'level' => 0x01 | 0x02 | 0x03 | 0x04 | 0x08,
    ),
), LOG);

require_once ROOT_PATH . 'app/common/lib/SendEmail.php';
require_once ROOT_PATH . 'app/common/script/collectpush/Base.php';

/**
 * http://wiki.baidu.com/pages/viewpage.action?pageId=1177249600
 * Class pushToAuthor
 */
class pushToAuthor extends Script_Collectpush_Base {
    protected $strFtpPath = 'ftp://tieba00:<EMAIL>/download_tieba_mds_push_collect_user_data/';

    private $strAbstract = '快去围观>>';
    private $queryFile = null;

    const MAX_NUM = 30; //每次批量30
    const FIELD_NUM = 5;

    public function execute() {
        $yesterday =  time() - self::ONE_DAY;
        $date = date('Ymd', $yesterday);
        $fileName = sprintf('author_%s.txt', $date);
        $this->queryFile = sprintf('%s/%s', DATA_PATH, $fileName);
        if (!is_dir(DATA_PATH)) {
            mkdir(DATA_PATH);
        }

        $strCmd = 'rm -f '. DATA_PATH .'/author_*';
        system($strCmd, $result);
        $intRetry = 0;
        //最多等待一小时，数据文件再不产生就返回退出
        while ($intRetry < 60) {
            $strCmd = 'wget '. escapeshellarg($this->strFtpPath . $fileName) . ' -O ' . escapeshellarg(DATA_PATH.'/'.$fileName);
            system($strCmd, $result);
            $fileSize = filesize($this->queryFile);
            if (!file_exists($this->queryFile) || empty($fileSize)) {
                Bingo_Log::warning("open file " . $this->queryFile . " fail,sleep 2 and try again!");
                $intRetry++;
                sleep(60);
                //超过1小时，给PM、QA发个邮件
                if ($intRetry >= 60) {
                    Bingo_Log::warning("download file " . $this->queryFile ." fail,scrip exit");
                    $strTargetEmail = '<EMAIL>,';
                    $strCCEmail = '<EMAIL>,<EMAIL>';
                    $strSenderEmail = '<EMAIL>';
                    $strTitle = '客户端作者push数据文件拉取失败';
                    $strContent = '客户端作者push数据文件拉取失败，快去找数据组同学看看吧，文件名：' . $fileName;
                    $this->sendEmail($strTargetEmail, $strCCEmail, $strSenderEmail, $strTitle, $strContent);

                    return true;
                }
            } else {
                break;
            }
        }

        //开始遍历读取文件，并向作者用户发push消息
        $this->_getAuthorFileData();
        $strCmd = 'rm -f '. DATA_PATH .'/author_*';
        system($strCmd, $result);

        return true;
    }

    /**
     * 构建封装push数据
     * @return bool
     */
    private function _getAuthorFileData() {
        $file = new SplFileObject($this->queryFile, 'r');
        $intMaxBatchNum = 0;
        $arrIds = array();
        $intCount = 0;
        $arrUserIds = array();
        $arrThreadIds = array();
        $arrAuthorIds = array();
        while (!$file->eof()) {
            $strContent = trim($file->current());
            if (strlen($strContent) <= 0) {
                $file->next();
                continue;
            }

            $arrContent = explode("\t", $strContent);
            //存在非法格式数据
            foreach ($arrContent as $item) {
                if ('\N' == $item) {
                    $file->next();
                    continue;
                }
            }
            //字段数小于5个 uid, shoubai_cuid, tid, collect_cnt, new_uid
            if (count($arrContent) < self::FIELD_NUM) {
                $file->next();
                continue;
            }
            //收藏数为0或无用户评论，过滤掉，不push
            if (empty($arrContent[3]) || empty($arrContent[4])) {
                $file->next();
                continue;
            }
            $intCount++;
            $arrId = array(
                'push_to_user_id' => $arrContent[0],
                'push_to_user_bdcuid' => trim($arrContent[1]),
                'thread_id' => $arrContent[2],
                'collect_count' => $this->formatCount($arrContent[3]),
                'new_collect_user_id' => $arrContent[4],
                'url' => sprintf('http://tieba.baidu.com/p/%d?fr=collect', $arrContent[2]),
                'push_to_client_type' => 0,
                'push_to_client_version' => '0.0.0',
            );
            $arrIds[] = $arrId;
            $intMaxBatchNum++;
            if (self::MAX_NUM == $intMaxBatchNum) {
                $arrUserIds = array();
                $arrThreadIds = array();
                $arrAuthorIds = array();
                foreach ($arrIds as $item) {
                    $arrUserIds[] = $item['new_collect_user_id'];
                    $arrThreadIds[] = $item['thread_id'];
                    $arrAuthorIds[] = $item['push_to_user_id'];
                }
                $arrThreadAndUserInfo = $this->getThreadAndUserInfo($arrUserIds, $arrThreadIds, $arrAuthorIds, 'collectpush_author_task_multi');
                if (false != $arrThreadAndUserInfo) {
                    $arrPushData = $this->_buildPushData($arrIds, $arrThreadAndUserInfo);
                    //echo json_encode($arrPushData).PHP_EOL;
                    $this->_pushMessageToAuthor($arrPushData);
                }
                $intMaxBatchNum = 0;
                $arrIds = array();
                usleep(200000);
            }

            $file->next();
        }

        if (count($arrIds) > 0 ) {
            $arrThreadAndUserInfo = $this->getThreadAndUserInfo($arrUserIds, $arrThreadIds, $arrAuthorIds, 'collectpush_author_task_multi');
            if (false != $arrThreadAndUserInfo) {
                $arrPushData = $this->_buildPushData($arrIds, $arrThreadAndUserInfo);
                $this->_pushMessageToAuthor($arrPushData);
            }
        }

        echo 'total push count:'.$intCount.PHP_EOL;

        return true;
    }

    /**
     * 发送push
     * @param $arrPushDatas
     * @return bool
     */
    private function _pushMessageToAuthor($arrPushDatas) {
        if (empty($arrPushDatas)) {
            return true;
        }

        $arrPushAccount = array(
            'push_user_id'   => self::PUSH_USER_ID,
            'push_user_name' => self::PUSH_USER_NAME,
            "user_type"      => 4,//贴吧精选使用4
            "group_type"     => 1,
            'service_id'     => self::SERVICE_ID,
            'task_id'        => "100001",
        );
        $this->pushMessageForPhp($arrPushDatas, 'collect_push_to_collector', $arrPushAccount);

        return true;
    }

    /**
     * 获取用户信息
     * @param $arrUserIds
     * @return array|bool
     */
    private function _getUserInfo($arrUserIds) {
        if (empty($arrUserIds)) {
            Bingo_Log::warning('user_ids  empty');
            return false;
        }

        $arrInput = array('user_id' => $arrUserIds);
        $arrUserInfos = Tieba_Service::call('user', 'mgetUserData', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false == $arrUserInfos || Tieba_Errcode::ERR_SUCCESS != $arrUserInfos['errno'] || empty($arrUserInfos['user_info'])) {
            return false;
        }

        $arrUserInfos = $arrUserInfos['user_info'];
        $arrOut = array(
            'user_list' => $arrUserInfos,
            'thread_list' => array(),
        );

        return $arrOut;
    }

    /**
     * 构建push数据
     * @param $arrIds
     * @param $arrThreadAndUserInfo
     * @return mixed
     */
    protected function _buildPushData($arrIds, $arrThreadAndUserInfo) {
        $arrUserInfos = $arrThreadAndUserInfo['user_list'];
        $arrThreadInfos = $arrThreadAndUserInfo['thread_list'];
        $arrUserCuids = $arrThreadAndUserInfo['user_cuids_list'];
        $arrUserMaxClientInfo = $this->getUserMaxClientVersion($arrUserCuids);
        foreach ($arrIds as $key => $arrId) {
            $intCollectId = $arrId['new_collect_user_id'];
            $intAuthorId = $arrId['push_to_user_id'];
            $nickName = isset($arrUserInfos[$intCollectId]['user_nickname']) ? $arrUserInfos[$intCollectId]['user_nickname'] : $arrUserInfos[$intCollectId]['user_name'];
            $intClientType = isset($arrUserMaxClientInfo[$intAuthorId]['client_type']) ? $arrUserMaxClientInfo[$intAuthorId]['client_type'] : 0;
            $intClientVersion = isset($arrUserMaxClientInfo[$intAuthorId]['client_version']) ? $arrUserMaxClientInfo[$intAuthorId]['client_version'] : '0.0.0';
            $arrIds[$key]['collect_nickname'] = $nickName;
            $arrIds[$key]['push_to_client_type'] = $intClientType;
            $arrIds[$key]['push_to_client_version'] = $intClientVersion;
        }

        //处理title 及 abstract
        foreach ($arrIds as $key => $arrId) {
            $intThreadId = $arrId['thread_id'];
            $arrThread = $arrThreadInfos[$intThreadId];

            //视频贴封面
            if (isset($arrThread['media'][0]) && 'flash' == $arrThread['media'][0]['type']) {
                $arrIds[$key]['image_url'] = $arrThread['media'][0]['vpic'];
            } else if (isset($arrThread['media'][0]) && 'pic' == $arrThread['media'][0]['type']) {
                //图文贴封面
                $arrIds[$key]['image_url'] = $arrThread['media'][0]['water_pic'];
            }

            $strPushTitle = "你发表的贴子被{$arrId['collect_count']}人收藏了";
            $strPushAbstract = "@{$arrId['collect_nickname']}回复了你的贴子，不要错过精彩评论";
            $arrIds[$key]['title'] = $strPushTitle;
            $arrIds[$key]['abstract'] = $strPushAbstract;
        }

        //过滤掉低版本的，不进行push
        $arrIds = $this->filterLowVersionPushData($arrIds);

        return $arrIds;
    }
}

$handle = new pushToAuthor();

$start = time();
echo 'start exec pushToAuthor'.PHP_EOL;
$handle->execute();
$end = time();

$cost = $end - $start;
echo 'finish exec pushToAuthor cost: '. $cost .PHP_EOL;