[dbConf]
dbRalName:DB_forum_usergrowth

[insertForumBusiness]
type:update
[.@command]
sql : insert into forum_business (user_id, forum_id, status, create_time, update_time) values ({user_id:n}, {forum_id:n}, {status:n}, {create_time:n}, {update_time:n})

[updateForumBusiness]
type:update
[.@command]
sql: update forum_business set {fields:r} where {conditions:r}

[autoRefuseApply]
type:update
[.@command]
sql: update forum_business set status=6,update_time={update_time:n}, where id = {id:n} and status=4 and create_time <= {time:n}

[getForumBusinessByUid]
type:query
[.@command]
sql : select id,user_id,forum_id,status,refuse_msg,op_uid,bazhu_uid,create_time,update_time,op_time,bazhu_time from forum_business where user_id = {user_id:n} order by create_time desc

[getForumBusinessById]
type:query
[.@command]
sql : select status,user_id,forum_id,bazhu_uid from forum_business where id = {id:n} order by create_time desc

[getCntByUidAndStatus]
type:query
[.@command]
sql : select COUNT(id) as count from forum_business where status in {status:r} and user_id = {user_id:n}

[getForumBusinessByFid]
type:query
[.@command]
sql : select id,user_id,forum_id,status,refuse_msg,op_uid,bazhu_uid,create_time,update_time,op_time,bazhu_time from forum_business where forum_id = {forum_id:n} and status = {status:n}

[getForumBusinessList]
type:query
[.@command]
sql : select id,user_id,forum_id,status,refuse_msg,op_uid,bazhu_uid,create_time,update_time,op_time,bazhu_time from forum_business where {conditions:r}

[getForumBusinessCount]
type:query
[.@command]
sql : select count(id) as count from forum_business where {conditions:r}

