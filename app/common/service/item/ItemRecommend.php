<?php

/***************************************************************************
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **************************************************************************/

/**
 * @file   ItemRecommend.php
 * <AUTHOR>
 * @date   2021/08/23
 * @brief  item推荐
 **/
class Service_Item_ItemRecommend {
    const REDIS_SUBSCRIPTION_RECOMMEND_PERFIX = 'item_subscription_recommend_top_';
    const REDIS_DOWNLOAD_RECOMMEND_PERFIX = 'item_download_recommend_top';
    const REDIS_CONF = 'sign';

    //筛选item数量
    const TOP_OFFSET = 0;
    const TOP_RN = 50;

    //需要的推荐列表长度
    const RECOMMEND_NUM = 20;

    /**
     * 根据用户客户端获取推荐预约列表itemid
     * @param array $arrInput 'client_type', 'client_version'
     * @return array
     */
    public static function getSubscriptionRecommendItemId($arrInput = array()) {

        //检查参数
        if (empty($arrInput['client_type']) || empty($arrInput['client_version'])) {
            Bingo_Log::warning('input params invalid. ['.serialize($arrInput).']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'output');
        }

        //查询缓存中是否存在
        $key = self::REDIS_SUBSCRIPTION_RECOMMEND_PERFIX.$arrInput['client_type'];
        $arrRedisInput = array(
            'key' => $key,
        );
        $arrCheck = Lib_Redis::call('GET', $arrRedisInput, self::REDIS_CONF);

        if (!$arrCheck || $arrCheck['err_no'] != Tieba_Errcode::ERR_SUCCESS || is_null($arrCheck['ret'][$key])) {
            //没有命中redis，更新推荐预约列表itemid并返回
            $arrOutput = self::_querySubscriptionRecommend($arrInput);
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput, 'output');
        } else {
            //命中redis，直接返回itemid
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, json_decode($arrCheck['ret'][$key], true), 'output');
        }
    }

    /**
     * 根据用户客户端获取推荐下载列表itemid
     * @param array $arrInput 'client_type', 'client_version'
     * @return array
     */
    public static function getDownloadRecommendItemId($arrInput = array()) {

        //检查参数
        if (empty($arrInput['client_type']) || empty($arrInput['client_version'])) {
            Bingo_Log::warning('input params invalid. ['.serialize($arrInput).']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'output');
        }

        //查询缓存中是否存在
        $key = self::REDIS_DOWNLOAD_RECOMMEND_PERFIX.$arrInput['client_type'];
        $arrRedisInput = array(
            'key' => $key,
        );
        $arrCheck = Lib_Redis::call('GET', $arrRedisInput, self::REDIS_CONF);

        if (!$arrCheck || $arrCheck['err_no'] != Tieba_Errcode::ERR_SUCCESS || is_null($arrCheck['ret'][$key])) {
            //没有命中redis，更新推荐下载列表itemid并返回
            $arrOutput = self::_queryDownloadRecommend($arrInput);
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput, 'output');
        } else {
            //命中redis，直接返回itemid
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, json_decode($arrCheck['ret'][$key], true), 'output');
        }
    }

    /**
     * 查询订阅及热度库，更新推荐预约列表itemid缓存
     * @param array $arrInput 'client_type', 'client_version'
     * @return array
     */
    private static function _querySubscriptionRecommend($arrInput = array()) {

        //查询订阅库
        $arrOutput = self::_getSubscriptionTop($arrInput);
        $itemIdList = array();
        foreach ($arrOutput['output'] as $v) {
            array_push($itemIdList, $v['item_id']);
        }

        //检查是否为可订阅状态
        $recommendList = self::_checkItemStatus($arrInput, $itemIdList, '预约', self::RECOMMEND_NUM);

        //若满足数量要求，则直接更新redis缓存
        if (self::RECOMMEND_NUM === count($recommendList)) {
            $key = self::REDIS_SUBSCRIPTION_RECOMMEND_PERFIX.$arrInput['client_type'];
            self::_updateRecommendRedis($key, $recommendList);
        }

        //查询热度库
        $arrOutput = self::_getRankTop();
        $itemIdList = array();
        foreach ($arrOutput['output'] as $v) {
            array_push($itemIdList, $v['item_id']);
        }

        //检查是否为可下载状态
        $downloadList = self::_checkItemStatus($arrInput, $itemIdList, '下载', self::RECOMMEND_NUM - count($recommendList));
        foreach ($downloadList as $v) {
            array_push($recommendList, $v);
        }

        //更新redis缓存
        $key = self::REDIS_SUBSCRIPTION_RECOMMEND_PERFIX.$arrInput['client_type'];
        self::_updateRecommendRedis($key, $recommendList);
        return $recommendList;
    }

    /**
     * 查询下载历史库，更新推荐下载列表itemid缓存
     * @param array $arrInput 'client_type', 'client_version'
     * @return array
     */
    private static function _queryDownloadRecommend($arrInput = array()) {

        //查询下载历史库
        $arrOutput = self::_getDownloadTop($arrInput);
        $itemIdList = array();
        foreach ($arrOutput['output'] as $v) {
            array_push($itemIdList, $v['item_id']);
        }

        //检查是否为可下载状态
        $recommendList = self::_checkItemStatus($arrInput, $itemIdList, '下载', self::RECOMMEND_NUM);

        //更新redis缓存
        $key = self::REDIS_DOWNLOAD_RECOMMEND_PERFIX.$arrInput['client_type'];
        self::_updateRecommendRedis($key, $recommendList);
        return $recommendList;
    }

    /**
     * 查询订阅库top itemid
     * @param array $arrInput 'client_type','client_version'
     * @return array
     */
    private static function _getSubscriptionTop($arrInput = array()) {

        //输入参数
        $arrDBInput = array(
            'function'    => 'getSubscriptionTopId',
            'client_type' => $arrInput['client_type'],
            'offset'      => self::TOP_OFFSET,
            'rn'          => self::TOP_RN,
        );

        $arrDBOutput = Lib_Db::call('item', $arrDBInput);
        if ($arrDBOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Log::warning('call Lib_Db::call failed! function: getSubscriptionTopId. input['.json_encode($arrDBInput).'] output['.var_export($arrDBOutput, true).']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrDBOutput['results'][0], 'output');
    }

    /**
     * 查询热度库top itemid(热度库不分端)
     * @return array
     */
    private static function _getRankTop() {

        //输入参数
        $arrDBInput = array(
            'function' => 'getRankTopId',
            'offset'   => self::TOP_OFFSET,
            'rn'       => self::TOP_RN,
        );

        $arrDBOutput = Lib_Db::call('item', $arrDBInput);
        if ($arrDBOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Log::warning('call Lib_Db::call failed! function: getRankTopId. input['.json_encode($arrDBInput).'] output['.var_export($arrDBOutput, true).']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrDBOutput['results'][0], 'output');
    }

    /**
     * 查询下载历史库top itemid
     * @param array $arrInput 'client_type','client_version'
     * @return array
     */
    private static function _getDownloadTop($arrInput = array()) {

        //输入参数
        $arrDBInput = array(
            'function'    => 'getDownloadTopId',
            'client_type' => $arrInput['client_type'],
            'offset'      => self::TOP_OFFSET,
            'rn'          => self::TOP_RN,
        );

        $arrDBOutput = Lib_Db::call('item', $arrDBInput);
        if ($arrDBOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Log::warning('call Lib_Db::call failed! function: getDownloadTopId. input['.json_encode($arrDBInput).'] output['.var_export($arrDBOutput, true).']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrDBOutput['results'][0], 'output');
    }

    /**
     * 检查itemid是否为checkStatus状态，将满足条件的的itemid返回
     * @param array $arrInput     'client_type','client_version'
     * @param array $itemIdList   'item_id'列表
     * @param string $checkStatus 需要检测的状态
     * @param int $needNum        需要返回的个数上限
     * @return array
     */
    private static function _checkItemStatus($arrInput, $itemIdList, $checkStatus, $needNum) {
        $recommendList = array();

        //若判断的item_id列表为空则直接返回
        if (0 === count($itemIdList)) {
            return $recommendList;
        }

        //输入参数
        $mgetInput = array(
            'item_ids'       => $itemIdList,
            'client_type'    => $arrInput['client_type'],
            'client_version' => $arrInput['client_version'],
            'call_from'      => 'client-item-mount',
        );

        //查询每个item的iteminfo
        $mgetOutput = Tieba_Service::call('common', 'mgetItemInfoByID', $mgetInput, null, null, 'post', 'php', 'utf-8');
        if (!isset($mgetOutput['errno']) || $mgetOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call mgetItemInfoByID failed. input['.serialize($mgetInput).'] output['.serialize($mgetOutput).']');
        } else {
            //逐个判断状态是否满足要求
            $itemInfoList = $mgetOutput['output'];
            foreach ($itemInfoList as $k => $v) {
                if ($checkStatus == $v['button_name']) {
                    array_push($recommendList, $k);

                    //满足要求后直接返回
                    if (count($recommendList) >= $needNum) {
                        break;
                    }
                }
            }
        }
        return $recommendList;
    }

    /**
     * 更新redis缓存
     * @param string $key
     * @param array $value
     */
    private static function _updateRecommendRedis($key, $value) {

        //输入参数
        $arrRedisInput = array(
            'key'     => $key,
            'value'   => json_encode($value),
            'seconds' => 60 * 60,
        );
        $arrRes = Lib_Redis::call('SETEX', $arrRedisInput, self::REDIS_CONF);
        if (!$arrRes || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('redis insert fail. ['.serialize($arrRes).']');
        }
    }
}