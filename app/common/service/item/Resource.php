<?php

/***************************************************************************
 *
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file Resource.php
 * <AUTHOR>
 * @date 2021/6/2 6:08 下午
 * @brief item开放平台
 **/
class Service_Item_Resource
{
    public static $_resourceActionType = array(
        1 => '认领游戏',  //认领游戏
        2 => '创建游戏',  //创建游戏
        3 => '更新游戏',  //更新游戏
    );

    public static $_updateDoList = array(
        'updateResouce' => 1,
        'onAudit' => 2,
        'insertPackage' => 3,
        'updatePublish' => 4,
    );

    /**
     * -2 删除 -1 过期 0 草稿 1 审核中 2 审核拒绝 3 审核通过 4 发布中 5 已发布
     */
    const RESOURCE_STATUS_DELETE = -2;
    const RESOURCE_STATUS_EXPIRED = -1;
    const RESOURCE_STATUS_DRAFT = 0;
    const RESOURCE_STATUS_AUDITING = 1;
    const RESOURCE_STATUS_AUDIT_REFUSE = 2;
    const RESOURCE_STATUS_AUDIT_PASS = 3;
    const RESOURCE_STATUS_RELEASEINGG = 4;
    const RESOURCE_STATUS_RELEASE = 5;

    const STATUS_PASS = 1;//通过
    const STATUS_UNPASS = 2;//拒绝
    const PUSH_UID = 4892865941;
    const PUSH_UNAME = '官方号小助手';
    const TASK_ID = "103";
    const SERVICE_ID = "103";

    const REDIS_NAME = 'sign';
    const ITEM_RESOURCE_PUBLISH_TYPE_KEY = 'item_resource_publish_type_'; //发布方式redis key前缀
    const ITEM_RESOURCE_EMAIL_ADDRESS = 'item_resource_email_address_'; //通知 邮箱地址 redis key 前缀
    const NINETY_DAYS_SECONDS = 7776000;
    const PUBLISH_TYPE_IMMEDIATE = 1;//直接发布
    const PUBLISH_TYPE_MANUAL = 2;//手动发布 默认

    const SENDER = "<EMAIL>";
    const EMAIL_TITLE = "游戏审核结果通知";

    private static $message_arr = array(
        1 => array(
            1 => "尊敬的开发者，你的游戏已通过审核，请前往游戏管理-全部游戏页面查看",
            2 => "尊敬的开发者，你的游戏未通过审核，请前往官方号平台修改后重新提交",
        ),
        2 => array(
            1 => "尊敬的开发者，你的安装包更新通过审核",
            2 => "尊敬的开发者，你的安装包更新未通过审核，驳回原因:"
        ),
    );
    /**
     * 拼接sql条件
     * @param array $arrInput
     * @return string
     */
    private static function _condition($arrInput = array())
    {
        $cond = array();
        $strCond = '1=1';

        if (isset($arrInput['id']) && !empty($arrInput['id'])) {
            $cond[] = 'id in (' . $arrInput['id'] . ')';
        }

        if ($arrInput['tag']) {
            if ($arrInput['isFuzzy']) {
                //模糊匹配
                $cond[] = 'tag like \'%' . strval($arrInput['tag']) . '%\'';
            } else {
                $cond[] = 'tag = \'' . strval($arrInput['tag']) . '\'';
            }
        }
        if (isset($arrInput['class_id']) && !empty($arrInput['class_id'])) {
            $cond[] = 'class_id in (' . $arrInput['class_id'] . ')';
        }
        if (isset($arrInput['status']) && $arrInput['status'] !== '') {
            $cond[] = 'status=' . $arrInput['status'];
        }
        if (!empty($cond)) {
            $strCond = implode(' and ', $cond);
        }
        return $strCond;
    }

    /**
     * @param array $arrInput
     * @return multitype
     * 获取item官方号认领信息
     */
    public static function getItemClaimInfo($arrInput = array())
    {
        if (intval($arrInput['item_id']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'output');
        }

        $status = $arrInput['status'] ? $arrInput['status'] : 1;

        $strCond = "";
        $arrCond[] = 'item_id = ' . $arrInput['item_id'];
        $arrCond[] = 'status = ' . $status;
        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }
        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getClaimInfoByCond',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getClaimInfoByCond call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'][0];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /**
     * @param array $arrInput
     * @return multitype
     * 获取正在审核中的item信息
     */
    public static function getItemResourceAuditingCount($arrInput = array())
    {
        if (count($arrInput['item_ids']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');

            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'output');
        }

        $status = $arrInput['status'] ? $arrInput['status'] : 1;

        $strCond = "";
        $arrCond[] = "item_id in (" . implode(',', $arrInput['item_ids']) . ")";
        $arrCond[] = "status = " . $status;

        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }
        $strCond .= " group by item_id";

        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getItemResourceCount',
        );
        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemResourceCount call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'][0];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /**
     * @param $arrInput
     * @return multitype
     * 获取游戏列表
     */
    public static function getGameListByCond($arrInput = array())
    {
        $arrData = array(
            'count' => 0,
            'list' => array(),
        );
        if (intval($arrInput['op_uid']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'output');
        }

        $intPn = empty($arrInput['pn']) ? 1 : $arrInput['pn'];
        $intRn = empty($arrInput['rn']) ? 20 : $arrInput['rn'];

        $strCond = "create_uid = " . $arrInput['op_uid'] . " and status >=0 ";
        $arrDlInput = array(
            'function' => 'getItemResourceCount',
            'cond' => $strCond,
        );
        $arrDlOutput = Lib_Db::call('item', $arrDlInput);

        if (!self::_checkOutput($arrDlOutput)) {
            Bingo_Log::warning('getGameListNumByCond call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOutput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $count = $arrDlOutput['results'][0][0]['count'];
        unset($arrDlOutput);

        if (intval($count) <= 0) {
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
        }

        $strSort = 'order by create_time desc';
        $arrDlInput = array(
            'function' => 'getGameListByCond',
            'cond' => $strCond,
            'sort' => $strSort,
            'offset' => ($intPn - 1) * $intRn,
            'rn' => $intRn,
        );
        $arrDlOutput = Lib_Db::call('item', $arrDlInput);

        if (!self::_checkOutput($arrDlOutput)) {
            Bingo_Log::warning('getGameListByCond call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOutput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrGameList = $arrDlOutput['results'][0];
        foreach ($arrGameList as $k => $v) {
            $arrGameIds[] = $v['id'];
            if ($v['action_type'] == 1) {
                //认领的游戏
                $arrGameItemIds[] = $v['item_id'];
            }

            //对发布状态，获取设置的包状态
            if ($v['status'] == self::RESOURCE_STATUS_RELEASE) {

                $arrCond = array();
                $strCond = '';
                if (!empty($v['id'])) {
                    $arrCond[] = 'item_resource_id = ' . $v['id'];
                }
                if (!empty($arrInput['op_uid'])) {
                    $arrCond[] = 'create_uid = ' . $arrInput['op_uid'];
                }

                if (!empty($arrCond)) {
                    $strCond .= implode(' and ', $arrCond);
                }
                $strCond .= ' order by create_time desc limit 1';
                $arrDlInput = array(
                    'cond' => $strCond,
                    'function' => 'getPublishStatusRecordByCond',
                );
                $arrDlOut = Lib_Db::call('item', $arrDlInput);

                if (!self::_checkOutput($arrDlOut)) {
                    Bingo_Log::warning('getPublishStatusRecordByCond call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
                }

                if (is_array($arrDlOut['results'][0][0]) && count($arrDlOut['results'][0][0]) > 0) {
                    $publishStatusRecord = $arrDlOut['results'][0][0];
                    $arrGameList[$k]['android_publish_status'] = $publishStatusRecord['android_publish_status'];
                    $arrGameList[$k]['ios_publish_status'] = $publishStatusRecord['ios_publish_status'];
                }
            }

            //对发布中的， 添加定时发布时间
            if ($v['status'] == self::RESOURCE_STATUS_RELEASEINGG) {

                $input = array(
                    'item_resource_id' => $v['id']
                );

                $arrGameList[$k]['timingstamp'] = 0;
                $output = self::getNearlyItemReleaseTasksByItemRId($input);
                if(!empty($output['output'][0][0]['timingstamp'])){
                    $arrGameList[$k]['timingstamp'] = $output['output'][0][0]['timingstamp'];
                }
                if (is_array($arrDlOut['results'][0][0]) && count($arrDlOut['results'][0][0]) > 0) {
                    $publishStatusRecord = $arrDlOut['results'][0][0];
                    $arrGameList[$k]['android_publish_status'] = $publishStatusRecord['android_publish_status'];
                    $arrGameList[$k]['ios_publish_status'] = $publishStatusRecord['ios_publish_status'];
                }
            }
        }
        //获取拒绝原因
        $gameAuditList = self::_getGameAuditInfoByIds($arrGameIds);
        foreach ($arrGameList as $k => $v) {
            foreach ($gameAuditList as $key => $value) {
                if (!empty($value['item_resource_id']) && $value['item_resource_id'] == $v['id']) {
                    $arrGameList[$k]['reject_reason'] = $value['reject_reason'];
                    $arrGameList[$k]['update_time'] = $v['update_time'] ? $v['update_time'] : $v['create_time'];
                }
            }
        }

        /*$packageAuditList = self::_getPackageAuditInfoByIds($arrGameIds);
        foreach ($arrGameList as $k => $v) {
            foreach ($packageAuditList as $key => $value) {
                if (!empty($value['item_resource_id']) && $value['item_resource_id'] == $v['id']) {
                    if (!empty(trim($value['reject_reason']))) {
                        $arrGameList[$k]['reject_reason'] = $value['reject_reason'];
                        $arrGameList[$k]['update_time'] = $v['update_time'] ? $v['update_time'] : $v['create_time'];
                        $arrGameList[$k]['status'] = 2; //审核拒绝
                    }
                }
            }
        }*/

        if (is_array($arrGameItemIds) && count($arrGameItemIds) > 0) {
            //认领游戏获取线上图标、标题
            $callInput = array(
                'item_ids' => $arrGameItemIds,
            );
            $itemRet = Tieba_Service::call('common', 'mgetItemInfoByID',
                $callInput, null, null,
                'post', 'php', 'utf-8'
            );

            if (!self::_checkOutput($itemRet)) {
                Bingo_Log::warning('call common:mgetItemInfoByID , input:[' . json_encode($callInput) . '],output:[' . json_encode($itemRet) . ']');
            }


            if (is_array($itemRet['output']) && count($itemRet['output']) > 0) {
                foreach ($arrGameList as $k => $v) {
                    foreach ($itemRet['output'] as $key => $value) {
                        if ($key == $v['item_id']) {
                            $item_name = trim($value['item_name']);
                            $icon_url = trim($value['icon_url']);
                            if (!empty($item_name)) {
                                $arrGameList[$k]['online_name'] = $item_name;
                            }
                            if (!empty($icon_url)) {
                                $arrGameList[$k]['online_icon_url'] = $icon_url;
                            }
                        }
                    }
                }
            }
        }

        $arrData = array(
            'count' => $count,
            'list' => $arrGameList,
        );

        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /**
     * 获取游戏审核信息
     * @param array $item_resource_ids
     * @return array
     */
    private static function _getGameAuditInfoByIds($item_resource_ids = array())
    {
        $arrData = array();
        if (count($item_resource_ids) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($item_resource_ids) . ']');
            return $arrData;
        }

        $strCond = "item_resource_id in (" . implode(',', $item_resource_ids) . ")";

        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getGameAuditInfoByIds',
        );

        $arrDlOutput = Lib_Db::call('item', $arrDlInput);

        if (!self::_checkOutput($arrDlOutput)) {
            Bingo_Log::warning('getItemResourceAuditInfoByIds call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOutput) . ']');
            return $arrData;
        }
        $arrData = (array)$arrDlOutput['results'][0];
        return $arrData;
    }

    /**
     * 获取游戏安装包审核信息
     * @param array $item_resource_ids
     * @return array
     */
    private static function _getPackageAuditInfoByIds($item_resource_ids = array())
    {
        $arrData = array();
        if (count($item_resource_ids) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($item_resource_ids) . ']');
            return $arrData;
        }

        $strCond = "item_resource_id in (" . implode(',', $item_resource_ids) . ") and status=1";

        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getPackageInfoByIds',
        );

        $arrDlOutput = Lib_Db::call('item', $arrDlInput);

        if (!self::_checkOutput($arrDlOutput)) {
            Bingo_Log::warning('getPackageInfoByIds call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOutput) . ']');
            return $arrData;
        }
        $arrData = (array)$arrDlOutput['results'][0];
        return $arrData;
    }

    /**
     * @param array $arrInput
     * @return multitype
     *
     * 校验游戏名是否重复
     * 1. 不与线上已存在游戏重复
     * 2. 不与item resource中审核已通过、发布中、已发布的游戏名重复
     */
    public static function isGameNameDuplicate($arrInput = array())
    {
        if (empty($arrInput['keyword']) || intval($arrInput['class_id']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'output');
        }

        //获取线上item_info数据
        $strCond = "";
        $arrCond = array();
        $arrCond[] = "class_id = " . $arrInput['class_id'];
        $arrCond[] = "status = 1";
        $arrCond[] = "name = '" . $arrInput['keyword'] . "'";

        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }

        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getEntityTotalNum',
        );

        $arrDlOutput = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOutput)) {
            Bingo_Log::warning('getEntityTotalNum call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOutput) . ']');
            $arrData = array(
                'isDuplicate' => true
            );
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
        }
        if (intval($arrDlOutput['results'][0][0]['count']) > 0) {
            $arrData = array(
                'isDuplicate' => true
            );
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
        }

        //判断是否与item_resource游戏名重复
        $strCond = "";
        $arrCond = array();
        $arrCond[] = "class_id = " . $arrInput['class_id'];
        $arrCond[] = "status in (3,4)";
        $arrCond[] = "name = '" . $arrInput['keyword'] . "'";

        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }
        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getItemResourceCount',
        );

        $arrDlOutput = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOutput)) {
            Bingo_Log::warning('getEntityTotalNum call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOutput) . ']');
            $arrData = array(
                'isDuplicate' => true
            );
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
        }
        if (intval($arrDlOutput['results'][0][0]['count']) > 0) {
            $arrData = array(
                'isDuplicate' => true
            );
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
        }

        $arrData = array(
            'isDuplicate' => false
        );
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /**
     * @param array $arrInput
     * @return multitype
     *
     * 保存草稿：基础信息-包信息-发布状态信息
     */
    public static function saveDraft($arrInput = array())
    {
        $action_type = intval($arrInput['action_type']);
        $item_resource_id = intval($arrInput['item_resource_id']);
        if (!isset($action_type) || empty($action_type) || !array_key_exists($action_type, self::$_resourceActionType)) {
            Lib_Log::warning('param action_type is empty! input[' . json_encode($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        if (($arrInput['action_type'] == 1 || $arrInput['action_type'] == 3) && intval($arrInput['item_id']) <= 0) {
            //认领/更新游戏流程item_id参数缺失失败
            Lib_Log::warning('param action_type is 1 but item_id is empty! input[' . json_encode($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }

        if ($item_resource_id == 0) {
            //insert游戏resource
            $isSuccess = self::createItemResource($arrInput);

            if (!$isSuccess) {
                return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array(), 'output');
        } else {
            //update游戏resource
            $isSuccess = self::updateItemResource($arrInput);

            if (!$isSuccess) {
                return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array(), 'output');
        }
    }

    public static function submitToAudit($arrInput = array())
    {
        $action_type = intval($arrInput['action_type']);
        $item_resource_id = intval($arrInput['item_resource_id']);

        if (!isset($action_type) || empty($action_type) || !array_key_exists($action_type, self::$_resourceActionType)) {
            Lib_Log::warning('param action_type is empty! input[' . json_encode($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        if (($arrInput['action_type'] == 1 || $arrInput['action_type'] == 3) && intval($arrInput['item_id']) <= 0) {
            //认领/更新游戏流程item_id参数缺失失败
            Lib_Log::warning('param action_type is 1 but item_id is empty! input[' . json_encode($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }

        //20210805 <AUTHOR>
        //添加判断 针对认领游戏
        if($arrInput['action_type'] == 1 ){
            $input['item_id'] = intval($arrInput['item_id']);
            $output = self::getItemClaimInfo($input);
            //1 判断该item是否已被认领 已阻止对已经过审的item进行认领
            if (!empty($output['output'][0])) {
                return Lib_Log::errRet(Tieba_Errcode::ERR_IR_HAS_CLAIMED, array(), 'output');
            }
            //2 判断该item_id是否有审核中的状态， 以阻止对已提审的item进行认领
            $input = array(
                'item_ids' => array(
                    intval($arrInput['item_id'])
                ),
                'type' => 1,
            );
            $output = self::checkOnAudit($input);
            //有审核中的数据
            if (!empty($output['output'][0])) {
                return Lib_Log::errRet(Tieba_Errcode::ERR_IR_HAS_CLAIMING, array(), 'output');
            }
        }

        if ($item_resource_id == 0) {

            //insert游戏resource
            $isSuccess = self::createItemResource($arrInput, true);
            if (!$isSuccess) {
                return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array(), 'output');
        } else {

            //update游戏resource
            $isSuccess = self::updateItemResource($arrInput, true);
            if (!$isSuccess) {
                return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array(), 'output');
        }
    }

    /**
     * 创建游戏资源
     * @param array $arrInput
     * @param bool $submitAudit
     * @return bool
     */
    public static function createItemResource($arrInput = array(), $submitAudit = false)
    {
        //插入item_resource
        $handle = Lib_Db::init('item');
        if (false == $handle) {
            Bingo_Log::warning(__FUNCTION__ . ' init db failed.');
            return false;
        }

        $handle = Lib_Db::startTransaction();

        if (false == $handle) {
            Bingo_Log::warning(__FUNCTION__ . ' db startTransaction failed.');
            return false;
        }

        $arrDlOutput = self::insert_item_resource($arrInput);
        $insertId = intval($arrDlOutput['output']['insertId']);

        if ($arrDlOutput == false || Tieba_Errcode::ERR_SUCCESS != $arrDlOutput['errno'] || $insertId <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call db error. [input:' . serialize($arrInput) . ']');
            Lib_Db::rollback();
            return false;
        }

        //插入item_package
        unset($arrDlOutput);
        if (!empty($arrInput['package_info']) && is_string($arrInput['package_info'])) {
            $package = json_decode($arrInput['package_info'], true);

            if (!empty($package['apk_url']) || !empty($package['ios_url']) || !empty($package['ios_itunes_id'])) {
                $arrDlOutput = self::insert_item_resource_package($insertId, $arrInput, $package, $submitAudit);
                if ($arrDlOutput == false || Tieba_Errcode::ERR_SUCCESS != $arrDlOutput['errno']) {
                    Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call db error. [input:' . serialize($arrInput) . ']');
                    Lib_Db::rollback();
                    return false;
                }
            }
        }

        //发布状态
        unset($arrDlOutput);
        $arrDlOutput = self::insert_item_resource_publish_status_record($insertId, $arrInput);
        if ($arrDlOutput == false || Tieba_Errcode::ERR_SUCCESS != $arrDlOutput['errno']) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call db error. [input:' . serialize($arrInput) . ']');
            Lib_Db::rollback();
            return false;
        }

        if ($submitAudit) {
            //提交写入审核
            $arrDlOutput = self::insert_item_resource_audit($insertId, $arrInput);
            if ($arrDlOutput == false || Tieba_Errcode::ERR_SUCCESS != $arrDlOutput['errno']) {
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call db error. [input:' . serialize($arrInput) . ']');
                Lib_Db::rollback();
                return false;
            }
        }

        $handle = Lib_Db::commit();
        if (false == $handle) {
            Bingo_Log::warning(__FUNCTION__ . ' db commit failed.');
            Lib_Db::rollback();
            return false;
        }
        //成功后保存 发布方式 以及 邮箱
        $arrRedisInput = array(
            'item_resource_id' => $insertId,
            'publish_type' => $arrInput['publish_type'],
            'email_address' => $arrInput['email_address'],
        );
        self::savePublishEmailByResourceId($arrRedisInput);
        return true;
    }

    /**
     * @param array $arrInput
     * @return multitype
     */
    private static function insert_item_resource($arrInput = array())
    {
        if ($arrInput['screenshots'] != "") {
            $screenshots = json_encode($arrInput['screenshots']);  //图片
        } else {
            $screenshots = "";
        }
        if ($arrInput['milestone_extra'] != "") {
            $milestone_extra = json_encode($arrInput['milestone_extra']);  //里程碑
        } else {
            $milestone_extra = "";
        }
        $arrDBInput = array(
            'item_id' => intval($arrInput['item_id']),
            'action_type' => intval($arrInput['action_type']),  //动作类型: 1 认领游戏 2 创建游戏 3 更新游戏
            'developer' => strval($arrInput['developer']),
            'publisher' => strval($arrInput['publisher']),
            'language' => intval($arrInput['language']),
            'is_network' => intval($arrInput['is_network']),
            'is_purchase' => intval($arrInput['is_purchase']),
            'compatibility' => intval($arrInput['compatibility']),
            'compatibility_desc' => strval($arrInput['compatibility_desc']),
            'is_anti_addiction' => intval($arrInput['is_anti_addiction']),
            'is_open_official' => intval($arrInput['is_open_official']),
            'icon_url' => strval($arrInput['icon_url']),
            'name' => strval($arrInput['name']),
            'class_id' => intval($arrInput['class_id']),
            'tags' => strval($arrInput['tags']),
            'brief' => strval($arrInput['brief']),

            'screenshots' => $screenshots,  //图片
            'videos' => strval($arrInput['videos']),  //视频
            'is_open_milestone' => intval($arrInput['is_open_milestone']),
            'milestone_extra' => $milestone_extra, //里程碑
            'qualification_extra' => strval($arrInput['qualification_extra']),  //资质信息

            'status' => intval($arrInput['status']),    //草稿
            'create_time' => time(),
            'create_uid' => intval($arrInput['op_uid']),
            'update_time' => time(),
            'function' => 'insertItemResource',
        );

        $arrDBOutput = Lib_Db::call('item', $arrDBInput);
        if (!$arrDBOutput || $arrDBOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Log::warning('call Lib_Db::call failed! input[' . json_encode($arrDBInput) . '] output[' . json_encode($arrDBOutput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }

        $intInsertId = Lib_Db::getInsertID();

        if (empty($intInsertId) || $intInsertId < 0) {
            Lib_Log::warning('call Lib_Db::call failed! input[' . json_encode($arrDBInput) . '] output[' . json_encode($arrDBOutput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array('insertId' => $intInsertId), 'output');
    }

    private static function insert_item_resource_package($item_resource_id = 0, $arrInput = array(), $package = array(), $submitAudit = false)
    {
        $status = 4;    //草稿态
        if ($submitAudit) {
            $status = 0;    //待审态
        }
        $arrDBInput = array(
            'item_resource_id' => intval($item_resource_id),
            'item_name' => strval($arrInput['name']),
            //android
            'apk_url' => strval($package['apk_url']),
            'apk_name' => strval($package['apk_name']),
            'apk_size' => strval($package['apk_size']),
            'apk_version' => strval($package['apk_version']),
            'apk_changelog' => strval($package['apk_changelog']),
            'apk_private_url' => strval($package['apk_private_url']),
            'apk_permissions' => strval($package['apk_permissions']),

            //ios
            'ios_url' => strval($package['ios_url']),
            'ios_itunes_id' => intval($package['ios_itunes_id']),
            'ios_version' => strval($package['ios_version']),
            'ios_changelog' => strval($package['ios_changelog']),
            'ios_private_url' => strval($package['ios_private_url']),

            'status' => $status,   //审核状态
            'source' => 0,    //来源：0默认全流程 1仅更新安装包
            'create_time' => time(),
            'create_uid' => intval($arrInput['op_uid']),
            'update_time' => time(),
            'update_uid' => intval($arrInput['op_uid']),
            'function' => 'insertItemResourcePackage',
        );

        $arrDBOutput = Lib_Db::call('item', $arrDBInput);

        if (!$arrDBOutput || $arrDBOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Log::warning('call Lib_Db::call failed! input[' . json_encode($arrDBInput) . '] output[' . json_encode($arrDBOutput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array(), 'output');
    }

    private static function insert_item_resource_publish_status_record($item_resource_id = 0, $arrInput = array())
    {
        if (empty($item_resource_id)) {
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        $arrDBInput = array(
            'item_resource_id' => intval($item_resource_id),
            'android_publish_status' => intval($arrInput['android_publish_status']),
            'ios_publish_status' => intval($arrInput['ios_publish_status']),
            'source' => intval($arrInput['source']),     //来源：0默认全流程 1仅更新安装包
            'release_status' => intval($arrInput['status']),
            'create_time' => time(),
            'create_uid' => intval($arrInput['op_uid']),
            'function' => 'insertItemResourcePublishStatusRecord',
        );

        $arrDBOutput = Lib_Db::call('item', $arrDBInput);

        if (!$arrDBOutput || $arrDBOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Log::warning('call Lib_Db::call failed! input[' . json_encode($arrDBInput) . '] output[' . json_encode($arrDBOutput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array(), 'output');
    }

    private static function insert_item_resource_audit($item_resource_id = 0, $arrInput = array())
    {
        if (empty($item_resource_id)) {
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        $arrDBInput = array(
            'item_resource_id' => intval($item_resource_id),
            'status' => 0,  //待审
            'action_type' => intval($arrInput['action_type']),
            'create_time' => time(),
            'create_uid' => intval($arrInput['op_uid']),
            'update_time' => time(),
            'update_uid' => intval($arrInput['uid']),
            'function' => 'insertItemResourceAudit',
        );

        $arrDBOutput = Lib_Db::call('item', $arrDBInput);

        if (!$arrDBOutput || $arrDBOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Log::warning('call Lib_Db::call failed! input[' . json_encode($arrDBInput) . '] output[' . json_encode($arrDBOutput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array(), 'output');
    }

    /**
     * 更新游戏资源
     * @param array $arrInput
     * @param bool $submitAudit
     * @return bool
     */
    public static function updateItemResource($arrInput = array(), $submitAudit = false)
    {
        //更新resource
        $handle = Lib_Db::init('item');
        if (false == $handle) {
            Bingo_Log::warning(__FUNCTION__ . ' init db failed.');
            return false;
        }

        $handle = Lib_Db::startTransaction();

        if (false == $handle) {
            Bingo_Log::warning(__FUNCTION__ . ' db startTransaction failed.');
            return false;
        }

        $arrDlOutput = self::update_item_resource($arrInput);
        if ($arrDlOutput == false || Tieba_Errcode::ERR_SUCCESS != $arrDlOutput['errno']) {
            Bingo_Log::warning('update_item_resource call db error. [input:' . serialize($arrInput) . ']');
            Lib_Db::rollback();
            return false;
        }

        //更新包
        if (!empty($arrInput['package_info']) && is_string($arrInput['package_info'])) {
            $package = array();
            $package = json_decode($arrInput['package_info'], true);

            unset($arrDlOutput);
            if (!empty($package['apk_url']) || !empty($package['ios_url']) || !empty(intval($package['ios_itunes_id']))) {
                $arrDlOutput = self::update_item_resource_package($arrInput, $package, $submitAudit);
                if ($arrDlOutput == false || Tieba_Errcode::ERR_SUCCESS != $arrDlOutput['errno']) {
                    Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call db error. [input:' . serialize($arrInput) . ']');
                    Lib_Db::rollback();
                    return false;
                }
            }
        }

        //更新游戏状态
        unset($arrDlOutput);
        $arrDlOutput = self::update_item_resource_publish_status_record($arrInput);

        if ($arrDlOutput == false || Tieba_Errcode::ERR_SUCCESS != $arrDlOutput['errno']) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call db error. [input:' . serialize($arrInput) . ']');
            Lib_Db::rollback();
            return false;
        }

        if ($submitAudit) {
            //提交写入审核
            $arrDlOutput = self::insert_item_resource_audit($arrInput['item_resource_id'], $arrInput);

            if ($arrDlOutput == false || Tieba_Errcode::ERR_SUCCESS != $arrDlOutput['errno']) {
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call db error. [input:' . serialize($arrInput) . ']');
                Lib_Db::rollback();
                return false;
            }
        }

        $handle = Lib_Db::commit();
        if (false == $handle) {
            Bingo_Log::warning(__FUNCTION__ . ' db commit failed.');
            Lib_Db::rollback();
            return false;
        }

        //成功后保存 发布方式 以及
        $arrRedisInput = array(
            'item_resource_id' => $arrInput['item_resource_id'],
            'publish_type' => $arrInput['publish_type'],
            'email_address' => $arrInput['email_address'],
        );
        self::savePublishEmailByResourceId($arrRedisInput);
        return true;
    }

    private static function update_item_resource($arrInput = array())
    {
        $item_resource_id = intval($arrInput['item_resource_id']);
        if (empty($item_resource_id)) {
            return false;
        }

        if ($arrInput['screenshots'] != "") {
            $screenshots = json_encode($arrInput['screenshots']);  //图片
        } else {
            $screenshots = "";
        }
        if ($arrInput['milestone_extra'] != "") {
            $milestone_extra = json_encode($arrInput['milestone_extra']);  //里程碑
        } else {
            $milestone_extra = "";
        }
        $arrDBInput = array(
            'item_resource_id' => $item_resource_id,
            'item_id' => intval($arrInput['item_id']),
            'action_type' => intval($arrInput['action_type']),  //动作类型: 1 认领游戏 2 创建游戏  3 更新游戏
            'developer' => strval($arrInput['developer']),
            'publisher' => strval($arrInput['publisher']),
            'language' => intval($arrInput['language']),
            'is_network' => intval($arrInput['is_network']),
            'is_purchase' => intval($arrInput['is_purchase']),
            'compatibility' => intval($arrInput['compatibility']),
            'compatibility_desc' => strval($arrInput['compatibility_desc']),
            'is_anti_addiction' => intval($arrInput['is_anti_addiction']),
            'is_open_official' => intval($arrInput['is_open_official']),
            'icon_url' => strval($arrInput['icon_url']),
            'name' => strval($arrInput['name']),
            'class_id' => intval($arrInput['class_id']),
            'tags' => strval($arrInput['tags']),
            'brief' => strval($arrInput['brief']),
            'screenshots' => $screenshots,  //图片
            'videos' => strval($arrInput['videos']),  //视频
            'is_open_milestone' => intval($arrInput['is_open_milestone']),
            'milestone_extra' => $milestone_extra,
            'qualification_extra' => strval($arrInput['qualification_extra']),  //资质信息
            'status' => intval($arrInput['status']),
            'update_time' => time(),
            'update_uid' => intval($arrInput['op_uid']),
            'function' => 'updateItemResource',
        );

        $arrDBOutput = Lib_Db::call('item', $arrDBInput);

        if (!$arrDBOutput || $arrDBOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Log::warning('call Lib_Db::call failed! input[' . json_encode($arrDBInput) . '] output[' . json_encode($arrDBOutput) . ']');
            return false;
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array(), 'output');
    }

    private static function update_item_resource_package($arrInput = array(), $package = array(), $submitAudit = false)
    {
        $item_resource_id = intval($arrInput['item_resource_id']);
        if (empty($item_resource_id)) {
            return false;
        }

        $status = 4;    //草稿态
        if ($submitAudit) {
            $status = 0;    //待审态
        }

        $arrDBInput = array(
            'item_resource_id' => $item_resource_id,
            //android
            'apk_url' => strval($package['apk_url']),
            'apk_name' => strval($package['apk_name']),
            'apk_size' => strval($package['apk_size']),
            'apk_version' => strval($package['apk_version']),
            'apk_changelog' => strval($package['apk_changelog']),
            'apk_private_url' => strval($package['apk_private_url']),
            'apk_permissions' => strval($package['apk_permissions']),

            //ios
            'ios_url' => strval($package['ios_url']),
            'ios_itunes_id' => intval($package['ios_itunes_id']),
            'ios_version' => strval($package['ios_version']),
            'ios_changelog' => strval($package['ios_changelog']),
            'ios_private_url' => strval($package['ios_private_url']),

            'reject_reason' => '',
            'status' => $status,
            'source' => intval($arrInput['source']),    //来源：0默认全流程 1仅更新安装包
            'update_time' => time(),
            'update_uid' => intval($arrInput['op_uid']),
            'function' => 'updateItemResourcePackage',
        );

        $arrDBOutput = Lib_Db::call('item', $arrDBInput);

        if (!$arrDBOutput || $arrDBOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Log::warning('call Lib_Db::call update_item_resource_package failed! input[' . json_encode($arrDBInput) . '] output[' . json_encode($arrDBOutput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array(), 'output');
    }

    private static function update_item_resource_publish_status_record($arrInput = array())
    {
        $item_resource_id = intval($arrInput['item_resource_id']);
        if (empty($item_resource_id)) {
            return false;
        }

        $arrDBInput = array(
            'item_resource_id' => $item_resource_id,
            'android_publish_status' => intval($arrInput['android_publish_status']),
            'ios_publish_status' => intval($arrInput['ios_publish_status']),
            'source' => intval($arrInput['source']),     //来源：0默认全流程 1仅更新安装包
            'release_status' => intval($arrInput['status']),
            'update_time' => time(),
            'update_uid' => intval($arrInput['op_uid']),
            'function' => 'updateItemResourcePublishStatusRecord',
        );

        $arrDBOutput = Lib_Db::call('item', $arrDBInput);

        if (!$arrDBOutput || $arrDBOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Log::warning('call Lib_Db::call update_item_resource_publish_status_record failed! input[' . json_encode($arrDBInput) . '] output[' . json_encode($arrDBOutput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array(), 'output');
    }

    /**
     * 获取草稿数据
     *
     * @param array $arrInput
     * @return multitype
     */
    public static function loadGameData($arrInput = array())
    {
        $arrResult = array();
        /*if (intval($arrInput['op_uid']) <= 0 || intval($arrInput['action_type']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'output');
        }*/

        $strCond = "";
        $arrCond = array();
        if (!empty($arrInput['action_type'])) {
            $arrCond[] = 'action_type = ' . $arrInput['action_type'];
        }
        if (!empty($arrInput['item_id'])) {
            $arrCond[] = 'item_id = ' . $arrInput['item_id'];
        }
        if (!empty($arrInput['item_resource_id'])) {
            $arrCond[] = 'id = ' . $arrInput['item_resource_id'];
        }
        if (!empty($arrInput['op_uid'])) {
            $arrCond[] = 'create_uid = ' . $arrInput['op_uid'];
        }
        if (isset($arrInput['resource_status'])) {
            $arrCond[] = 'status = ' . $arrInput['resource_status'];  //草稿状态
        }

        if (!empty($arrCond)) {
            $strCond .= implode(' and ', $arrCond);
        }

        $strCond .= ' order by create_time desc limit 1';
        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getItemResource',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemResource call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        if (empty($arrDlOut['results'][0][0]) || !is_array($arrDlOut['results'][0][0]) || empty($arrDlOut['results'][0][0]['id'])) {
            Bingo_Log::warning('getItemResource call db empty, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');

            //可以没有数据，直接返回空
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrResult, 'output');
        }
        $item_resource_id = intval($arrDlOut['results'][0][0]['id']);
        $arrResourceResult = null;    //空的情况传空字符串
        if (is_array($arrDlOut['results'][0][0]) && count($arrDlOut['results'][0][0]) > 0) {
            $arrResourceResult = $arrDlOut['results'][0][0];
        }
        $arrResult['resource'] = $arrResourceResult;

        if (!empty($arrResult['resource']['screenshots'])) {
            $arrResult['resource']['screenshots'] = json_decode($arrResult['resource']['screenshots'], true);
        }
        if (!empty($arrResult['resource']['videos'])) {
            $arrResult['resource']['videos'] = json_decode($arrResult['resource']['videos'], true);
        }
        if (!empty($arrResult['resource']['milestone_extra'])) {
            $arrResult['resource']['milestone_extra'] = json_decode($arrResult['resource']['milestone_extra'], true);
        }
        if (!empty($arrResult['resource']['qualification_extra'])) {
            $arrResult['resource']['qualification_extra'] = json_decode($arrResult['resource']['qualification_extra'], true);
        }


        //获取安装包信息
        $strCond = "";
        unset($arrCond);
        unset($arrDlOut);

        $arrCond = array();

        if (!empty($item_resource_id)) {
            $arrCond[] = 'item_resource_id = ' . $item_resource_id;
        }

        if (!empty($arrInput['op_uid'])) {
            $arrCond[] = 'create_uid = ' . $arrInput['op_uid'];
        }

        if (isset($arrInput['package_status'])) {
            $arrCond[] = 'status = ' . $arrInput['package_status'];
        } else {
            $arrCond[] = 'status = 4';  //草稿兜底
        }

        if (!empty($arrCond)) {
            $strCond .= implode(' and ', $arrCond);
        }
        $strCond .= ' order by create_time desc limit 1';
        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getPackageByCond',
        );
        $arrDlOut = Lib_Db::call('item', $arrDlInput);

        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getPackageByCond call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrPackage = null;
        if (is_array($arrDlOut['results'][0][0]) && count($arrDlOut['results'][0][0]) > 0) {
            $arrPackage = $arrDlOut['results'][0][0];
        }
        $arrResult['package'] = $arrPackage;

        //获取游戏状态信息
        $strCond = "";
        unset($arrCond);
        unset($arrDlOut);

        $arrCond = array();
        if (!empty($item_resource_id)) {
            $arrCond[] = 'item_resource_id = ' . $item_resource_id;
        }

        if (!empty($arrInput['op_uid'])) {
            $arrCond[] = 'create_uid = ' . $arrInput['op_uid'];
        }

        if (isset($arrInput['game_publish_status'])) {
            $arrCond[] = 'release_status = ' . $arrInput['game_publish_status'];
        } else {
            $arrCond[] = 'release_status = 0';  //草稿兜底
        }

        if (!empty($arrCond)) {
            $strCond .= implode(' and ', $arrCond);
        }
        $strCond .= ' order by create_time desc limit 1';
        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getPublishStatusRecordByCond',
        );
        $arrDlOut = Lib_Db::call('item', $arrDlInput);

        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getPublishStatusRecordByCond call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $publishStatusRecord = null;
        if (is_array($arrDlOut['results'][0][0]) && count($arrDlOut['results'][0][0]) > 0) {
            $publishStatusRecord = $arrDlOut['results'][0][0];
        }
        $arrResult['publishStatusRecord'] = $publishStatusRecord;
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrResult, 'output');
    }

    public static function insertItemClaimRecord($arrInput)
    {
        if (empty($arrInput['item_id'])) {
            return false;
        }

        $arrDBInput = array(
            'item_id' => intval($arrInput['item_id']),
            'create_time' => time(),
            'create_uid' => intval($arrInput['create_uid']),
            'update_time' => intval($arrInput['update_time']),
            'update_uid' => intval($arrInput['update_uid']),
            'function' => 'insertItemClaimRecord',
        );
        $arrDBOutput = Lib_Db::call('item', $arrDBInput);
        if (!$arrDBOutput || $arrDBOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Log::warning('call Lib_Db::call insertItemClaimRecord failed! input[' . json_encode($arrDBInput) . '] output[' . json_encode($arrDBOutput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array(), 'output');
    }

    public static function deleteDraft($arrInput = array())
    {
        if (intval($arrInput['op_uid']) <= 0 || intval($arrInput['item_resource_id']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'output');
        }

        $handle = Lib_Db::init('item');
        if (false == $handle) {
            Bingo_Log::warning(__FUNCTION__ . ' init db failed.');
            return false;
        }

        $handle = Lib_Db::startTransaction();

        if (false == $handle) {
            Bingo_Log::warning(__FUNCTION__ . ' db startTransaction failed.');
            return false;
        }

        $strCond = "";
        $arrCond = array();
        $arrCond[] = "id = " . $arrInput['item_resource_id'];
        $arrCond[] = "create_uid = " . $arrInput['op_uid'];
        $arrCond[] = "status = " . $arrInput['status'];

        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }

        $arrDlInput = array(
            'status' => -2,
            'cond' => $strCond,
            'function' => 'switchResourceStatusByCond',
        );

        $arrDlOutput = Lib_Db::call('item', $arrDlInput);

        if (!self::_checkOutput($arrDlOutput)) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call db error. [input:' . serialize($arrInput) . ']');
            Lib_Db::rollback();
            return false;
        }

        unset($arrDlOutput);
        $arrDlOutput = self::delete_package($arrInput);
        if ($arrDlOutput == false || Tieba_Errcode::ERR_SUCCESS != $arrDlOutput['errno']) {
            Bingo_Log::warning('delete_package call db error. [input:' . serialize($arrInput) . ']');
            Lib_Db::rollback();
            return false;
        }

        unset($arrDlOutput);
        $arrDlOutput = self::delete_publish_status($arrInput);
        if ($arrDlOutput == false || Tieba_Errcode::ERR_SUCCESS != $arrDlOutput['errno']) {
            Bingo_Log::warning('delete_publish_status call db error. [input:' . serialize($arrInput) . ']');
            Lib_Db::rollback();
            return false;
        }

        $handle = Lib_Db::commit();
        if (false == $handle) {
            Bingo_Log::warning(__FUNCTION__ . ' db commit failed.');
            Lib_Db::rollback();
            return false;
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array(), 'output');
    }

    public static function delete_package($arrInput = array())
    {
        if (intval($arrInput['op_uid']) <= 0 || intval($arrInput['item_resource_id']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'output');
        }

        $strCond = "";
        $arrCond = array();
        $arrCond[] = "item_resource_id = " . $arrInput['item_resource_id'];
        $arrCond[] = "create_uid = " . $arrInput['op_uid'];
        $arrCond[] = "status = 0";

        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }

        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'deletePackageByCond',
        );

        $arrDlOutput = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOutput)) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call db error. [input:' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array(), 'output');
    }

    public static function delete_publish_status($arrInput = array())
    {
        if (intval($arrInput['op_uid']) <= 0 || intval($arrInput['item_resource_id']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'output');
        }

        $strCond = "";
        $arrCond = array();
        $arrCond[] = "item_resource_id = " . $arrInput['item_resource_id'];
        $arrCond[] = "create_uid = " . $arrInput['op_uid'];
        $arrCond[] = "release_status = 0";

        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }

        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'deleteResourcePublishStatusRecord',
        );

        $arrDlOutput = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOutput)) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call db error. [input:' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array(), 'output');
    }


    /**
     * @param array $arrInput
     * @return multitype
     * 保存发布设置
     * 修改
     */
    public static function savePublishSetting($arrInput = array())
    {
        $handle = Lib_Db::init('item');
        if (false == $handle) {
            Bingo_Log::warning(__FUNCTION__ . ' init db failed.');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        $handle = Lib_Db::startTransaction();
        if (false == $handle) {
            Bingo_Log::warning(__FUNCTION__ . ' db startTransaction failed.');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }

        $arrDBInput = array(
            'op_uid' => intval($arrInput['op_uid']),
            'item_resource_id' => $arrInput['item_resource_id'],
            'item_id' => $arrInput['item_id'],
            'action_type' => $arrInput['action_type'],
            'source' => $arrInput['source'],  //全流程
            'type' => $arrInput['type'],
            'status' => intval($arrInput['status']),
            'timingstamp' => $arrInput['timingstamp'],
            'create_time' => time(),
            'create_uid' => intval($arrInput['op_uid']),
            'function' => 'insertItemReleaseTasks',
        );

        $arrDBOutput = Lib_Db::call('item', $arrDBInput);
        if (!$arrDBOutput || $arrDBOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Log::warning('call Lib_Db::call failed! input[' . json_encode($arrDBInput) . '] output[' . json_encode($arrDBOutput) . ']');
            Lib_Db::rollback();
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }

        $intInsertId = Lib_Db::getInsertID();

        if (empty($intInsertId) || $intInsertId < 0) {
            Lib_Log::warning('call Lib_Db::call failed! input[' . json_encode($arrDBInput) . '] output[' . json_encode($arrDBOutput) . ']');
            Lib_Db::rollback();
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }

        //修改item_resource状态为发布中
        $strCond = "";
        $arrCond = array();
        $arrCond[] = "id = " . $arrInput['item_resource_id'];
        $arrCond[] = "create_uid = " . $arrInput['op_uid'];

        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }
        $arrDlInput = array(
            'status' => 4,  //发布中
            'cond' => $strCond,
            'function' => 'switchResourceStatusByCond',
        );

        $arrDlOutput = Lib_Db::call('item', $arrDlInput);

        if (!self::_checkOutput($arrDlOutput)) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call db error. [input:' . serialize($arrInput) . ']');
            Lib_Db::rollback();
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }

        //如果是认领游戏，就要删除掉该item id对应的其他的认领记录
        if ($arrInput['action_type'] == 1) {
            $arrDlInput = array(
                'id' => $arrInput['item_resource_id'],
                'item_id' => $arrInput['item_id'],
                'function' => 'deleteResourceByItemIdAndId',
            );
            $arrDlOutput = Lib_Db::call('item',$arrDlInput);
            if (!self::_checkOutput($arrDlOutput)) {
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' call db error. [input:' . serialize($arrInput) . ']');
                Lib_Db::rollback();
                return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
            }
        }

        $handle = Lib_Db::commit();
        if (false == $handle) {
            Bingo_Log::warning(__FUNCTION__ . ' db commit failed.');
            Lib_Db::rollback();
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array('insertId' => $intInsertId), 'output');
    }

    /**
     * 校验非空、非0
     * @param unknown $arrOut
     * @return string $key
     */
    private static function _checkOutput($arrOut, $key = 'errno')
    {
        if (null == $arrOut || empty($arrOut) || !isset($arrOut[$key]) || Tieba_Errcode::ERR_SUCCESS != $arrOut[$key]) {
            return false;
        }
        return true;
    }

    /*
     * @param $arrInput
     * @return multitype
     *
    create_uid
    获取某个官方号创建过或即将创建的item信息
     */
    public static function getItemIdsByCreateUid($arrInput)
    {
        if (intval($arrInput['create_uid']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
//        //测试数据
//        $arrInput['create_uid'] = '206633161';
        $arrDlInput = array(
            'create_uid' => $arrInput['create_uid'],
            'function' => 'getItemIdsByCreateUid',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemIdsByCreateUid call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /*
     * @param $arrInput
     * @return multitype
     *
    item_id
    通过item_id取数据
     */
    public static function getItemResourceByItemId($arrInput)
    {
        if (intval($arrInput['item_id']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'item_id' => $arrInput['item_id'],
            'function' => 'getItemByItemId',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemByItemId call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /*
     * @param $arrInput
     * @return multitype
     *
    item_id
    通过item_id取数据
     */
    public static function getItemResourceById($arrInput)
    {
        if (intval($arrInput['id']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'id' => $arrInput['id'],
            'function' => 'getItemResourceById',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemResourceById call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /*
     * @param $arrInput
     * @return multitype
     *
    item_id
    通过item_id取数据
     */
    public static function getItemGameAttrByItemId($arrInput)
    {
        if (intval($arrInput['item_id']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'item_id' => $arrInput['item_id'],
            'function' => 'getItemGameAttrByItemId',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemGameAttrByItemId call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /*
     * @param $arrInput
     * @return multitype
     *
    package_id
    通过package_id获取最新已通过包数据
     */
    public static function getPackageById($arrInput)
    {
        if (intval($arrInput['id']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrDlInput = array(
            'id' => $arrInput['id'],
            'function' => 'getPackageById',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getPackageById call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /*
     * @param $arrInput
     * @return multitype
     *
     修改游戏行为的事务提交方法
     */
    public static function updateGame($arrInput)
    {

        if ((intval($arrInput['item_id']) <= 0) || (empty($arrInput['do_list']))) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $input = array(
            'item_id' => $arrInput['item_id'],
        );

        $output = self::getItemResourceByItemId($input);
        $itemResouceData = $output['output'][0][0];

        if (empty($itemResouceData)) {
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (!empty($arrInput['package_info']['apk_url'])) {
            $input = array(
                'item_resource_id' => $itemResouceData['id'],
                'apk_url' => $arrInput['package_info']['apk_url'],
            );
            $output = self::checkPackage($input);
            //该item曾经上传过的老包
            if (!empty($output['output'])) {
                return Lib_Log::errRet(Tieba_Errcode::ERR_PG_HAS_UPLOAD, array(), 'output');
            }
        }


        //仅更新包
        if (in_array(self::$_updateDoList['insertPackage'], $arrInput['do_list']) && !in_array(self::$_updateDoList['updateResouce'], $arrInput['do_list'])) {
            $input = array(
                'item_resource_ids' => array(
                    $itemResouceData['id']
                ),
                'type' => 2,
            );
            $output = self::checkOnAudit($input);
            //有审核中的数据
            if (!empty($output['output'][0])) {
                return Lib_Log::errRet(Tieba_Errcode::ERR_IR_HAS_AUDITING, array(), 'output');
            }

            $arrDlInput = array(
                'item_resource_id' => intval($itemResouceData['id']),
                'apk_url' => !empty($arrInput['package_info']['apk_url']) ? strval($arrInput['package_info']['apk_url']) : '',
                'apk_name' => !empty($arrInput['package_info']['apk_name']) ? strval($arrInput['package_info']['apk_name']) : '',
                'apk_version' => !empty($arrInput['package_info']['apk_version']) ? strval($arrInput['package_info']['apk_version']) : '',
                'apk_changelog' => !empty($arrInput['package_info']['apk_changelog']) ? strval($arrInput['package_info']['apk_changelog']) : '',
                'apk_private_url' => !empty($arrInput['package_info']['apk_private_url']) ? strval($arrInput['package_info']['apk_private_url']) : '',
                'apk_permissions' => !empty($arrInput['package_info']['apk_permissions']) ? strval($arrInput['package_info']['apk_permissions']) : '',
                'apk_size' => !empty($arrInput['package_info']['apk_size']) ? strval($arrInput['package_info']['apk_size']) : '',
                'ios_url' => !empty($arrInput['package_info']['ios_url']) ? strval($arrInput['package_info']['ios_url']) : '',
                'ios_itunes_id' => !empty($arrInput['package_info']['ios_itunes_id']) ? $arrInput['package_info']['ios_itunes_id'] : 0,
                'ios_version' => !empty($arrInput['package_info']['ios_version']) ? strval($arrInput['package_info']['ios_version']) : '',
                'ios_changelog' => !empty($arrInput['package_info']['ios_changelog']) ? strval($arrInput['package_info']['ios_changelog']) : '',
                'ios_private_url' => !empty($arrInput['package_info']['ios_private_url']) ? strval($arrInput['package_info']['ios_private_url']) : '',
                'status' => 0,
                'source' => 1,
                'create_time' => time(),
                'create_uid' => $arrInput['uid'],
                'update_time' => time(),
                'update_uid' => $arrInput['uid'],
                'item_name' => $itemResouceData['name'],
                'function' => 'insertItemResourcePackage',
            );

            $arrDlOut = Lib_Db::call('item', $arrDlInput);
            if (!self::_checkOutput($arrDlOut)) {
                Bingo_Log::warning('insertItemResourcePackage call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
                return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
//            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        if (in_array(self::$_updateDoList['updatePublish'], $arrInput['do_list']) && !in_array(self::$_updateDoList['updateResouce'], $arrInput['do_list'])) {
            $arrDlInput = array(
                'item_resource_id' => intval($itemResouceData['id']),
                'android_publish_status' => !empty($arrInput['android_publish_status']) ? intval($arrInput['android_publish_status']) : 0,
                'ios_publish_status' => !empty($arrInput['ios_publish_status']) ? intval($arrInput['ios_publish_status']) : 0,
                'source' => 1,
                'release_status' => 4,
                'create_time' => time(),
                'create_uid' => $arrInput['uid'],
                'update_time' => time(),
                'update_uid' => $arrInput['uid'],
                'function' => 'insertItemResourcePublishStatusRecord',
            );
            $arrDlOut = Lib_Db::call('item', $arrDlInput);
            if (!self::_checkOutput($arrDlOut)) {
                Bingo_Log::warning('insertItemResourcePublishStatusRecord call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
                return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
//            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        if (in_array(self::$_updateDoList['updateResouce'], $arrInput['do_list'])) {
            //有审核中或者已审核的resource或者有代审或者已过审的安装包， 都不能提交
            $input = array(
                'item_resource_ids' => array(
                    $itemResouceData['id']
                ),
                'type' => 2,
            );
            $output = self::checkOnAudit($input);
            //有审核中的数据
            if (!empty($output['output'][0])) {
                return Lib_Log::errRet(Tieba_Errcode::ERR_IR_HAS_AUDITING, array(), 'output');
            }

            $handle = Lib_Db::startTransaction();
            if (false == $handle) {
                Bingo_Log::warning(__FUNCTION__ . ' db startTransaction failed.');
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array());
            }
            //修改resource表
            $arrDlInput = array(
                'item_id' => !empty($arrInput['item_id']) ? intval($arrInput['item_id']) : 0,
                'action_type' => 3,
                'developer' => !empty($arrInput['developer']) ? strval($arrInput['developer']) : '',
                'publisher' => !empty($arrInput['publisher']) ? strval($arrInput['publisher']) : '',
                'language' => !empty($arrInput['language']) ? intval($arrInput['language']) : 0,
                'is_network' => !empty($arrInput['is_network']) ? intval($arrInput['is_network']) : 0,
                'is_purchase' => !empty($arrInput['is_purchase']) ? intval($arrInput['is_purchase']) : 0,
                'compatibility' => !empty($arrInput['compatibility']) ? intval($arrInput['compatibility']) : 0,
                'is_anti_addiction' => !empty($arrInput['is_anti_addiction']) ? intval($arrInput['is_anti_addiction']) : 0,
                'is_open_official' => !empty($arrInput['is_open_official']) ? intval($arrInput['is_open_official']) : 0,
//                'sub_official_account_uid' =>  !empty($arrInput['sub_official_account_uid']) ? intval($arrInput['sub_official_account_uid']) : 0,
                'icon_url' => !empty($arrInput['icon_url']) ? strval($arrInput['icon_url']) : '',
                'name' => !empty($arrInput['name']) ? strval($arrInput['name']) : '',
                'class_id' => !empty($arrInput['class_id']) ? intval($arrInput['class_id']) : 0,
                'tags' => !empty($arrInput['tags']) ? strval($arrInput['tags']) : '',
                'brief' => !empty($arrInput['brief']) ? strval($arrInput['brief']) : '',
                'screenshots' => !empty($arrInput['screenshots']) ? json_encode($arrInput['screenshots']) : '',
                'videos' => !empty($arrInput['videos']) ? json_encode($arrInput['videos']) : '',
                'is_open_milestone' => !empty($arrInput['is_open_milestone']) ? intval($arrInput['is_open_milestone']) : 0,
                'milestone_extra' => !empty($arrInput['milestone_extra']) ? json_encode($arrInput['milestone_extra']) : '',
                'qualification_extra' => !empty($arrInput['qualification_extra']) ? json_encode($arrInput['qualification_extra']) : '',
                'status' => 1,
//                'smart_color' => !empty($arrInput['smart_color']) ? strval($arrInput['smart_color']) : '',
                'update_time' => time(),
                'update_uid' => $arrInput['uid'],
                'function' => 'updateItemResourceByItemId',
            );
            $arrDlOut = Lib_Db::call('item', $arrDlInput);
            if (!self::_checkOutput($arrDlOut)) {
                Lib_Db::rollback();
                Bingo_Log::warning('updateItemResourceByItemId call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
                return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }

            //提交audit
            $arrDlInput = array(
                'item_resource_id' => intval($itemResouceData['id']),
                'status' => 0,
                'action_type' => 3,
                'create_time' => time(),
                'create_uid' => $arrInput['uid'],
                'update_time' => time(),
                'update_uid' => $arrInput['uid'],
                'function' => 'insertItemResourceAudit',
            );

            $arrDlOut = Lib_Db::call('item', $arrDlInput);
            if (!self::_checkOutput($arrDlOut)) {
                Lib_Db::rollback();
                Bingo_Log::warning('insertItemResourceAudit call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
                return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }

            //提交publish
            $arrDlInput = array(
                'item_resource_id' => intval($itemResouceData['id']),
                'android_publish_status' => !empty($arrInput['android_publish_status']) ? intval($arrInput['android_publish_status']) : 0,
                'ios_publish_status' => !empty($arrInput['ios_publish_status']) ? intval($arrInput['ios_publish_status']) : 0,
                'source' => 0,
                'release_status' => 3,
                'create_time' => time(),
                'create_uid' => $arrInput['uid'],
                'update_time' => time(),
                'update_uid' => $arrInput['uid'],
                'function' => 'insertItemResourcePublishStatusRecord',
            );

            $arrDlOut = Lib_Db::call('item', $arrDlInput);
            if (!self::_checkOutput($arrDlOut)) {
                Lib_Db::rollback();
                Bingo_Log::warning('insertItemResourcePublishStatusRecord call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
                return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }

            if (!empty($arrInput['package_info'])) {
                //提交package
                $arrDlInput = array(
                    'item_resource_id' => intval($itemResouceData['id']),
                    'apk_url' => !empty($arrInput['package_info']['apk_url']) ? strval($arrInput['package_info']['apk_url']) : '',
                    'apk_name' => !empty($arrInput['package_info']['apk_name']) ? strval($arrInput['package_info']['apk_name']) : '',
                    'apk_version' => !empty($arrInput['package_info']['apk_version']) ? strval($arrInput['package_info']['apk_version']) : '',
                    'apk_changelog' => !empty($arrInput['package_info']['apk_changelog']) ? strval($arrInput['package_info']['apk_changelog']) : '',
                    'apk_private_url' => !empty($arrInput['package_info']['apk_private_url']) ? strval($arrInput['package_info']['apk_private_url']) : '',
                    'apk_permissions' => !empty($arrInput['package_info']['apk_permissions']) ? strval($arrInput['package_info']['apk_permissions']) : '',
                    'apk_size' => !empty($arrInput['package_info']['apk_size']) ? strval($arrInput['package_info']['apk_size']) : '',
                    'ios_url' => !empty($arrInput['package_info']['ios_url']) ? strval($arrInput['package_info']['ios_url']) : '',
                    'ios_itunes_id' => !empty($arrInput['package_info']['ios_itunes_id']) ? $arrInput['package_info']['ios_itunes_id'] : 0,
                    'ios_version' => !empty($arrInput['package_info']['ios_version']) ? strval($arrInput['package_info']['ios_version']) : '',
                    'ios_changelog' => !empty($arrInput['package_info']['ios_changelog']) ? strval($arrInput['package_info']['ios_changelog']) : '',
                    'ios_private_url' => !empty($arrInput['package_info']['ios_private_url']) ? strval($arrInput['package_info']['ios_private_url']) : '',
                    'status' => 0,
                    'source' => 0,
                    'create_time' => time(),
                    'create_uid' => intval($arrInput['uid']),
                    'update_time' => time(),
                    'update_uid' => intval($arrInput['uid']),
                    'item_name' => $itemResouceData['name'],
                    'function' => 'insertItemResourcePackage',
                );
//                Bingo_Http_Request::getpost_dump($arrDlInput);exit;
                $arrDlOut = Lib_Db::call('item', $arrDlInput);
                if (!self::_checkOutput($arrDlOut)) {
                    Lib_Db::rollback();
                    Bingo_Log::warning('insertItemResourcePackage call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
                    return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                }
                $handle = Lib_Db::commit();
                if (false == $handle) {
                    Lib_Db::rollback();
                    Bingo_Log::warning(__FUNCTION__ . ' db commit failed.');
                    return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, $handle);
                }
//                return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS);
            }
        }
        //如果成功的话 保存邮箱地址
        $arrRedisInput = array(
            'item_resource_id' => $itemResouceData['id'],
            'publish_type' => $arrInput['publish_type'],
            'email_address' => $arrInput['email_address'],
        );
        self::savePublishEmailByResourceId($arrRedisInput);
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /*
     * @param $arrInput
     * item_resource_ids
     * type 1审核中 2 审核中和已审核还有发布中都查
     * @return multitype
     *
     查看批量查询 item_resource或package是否在审核中或已审核。
     */
    public static function checkOnAudit($arrInput)
    {
//        $arrInput['item_resource_ids'] = [1,3];
        if (empty($arrInput['item_resource_ids']) && (empty($arrInput['item_ids']))) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' param error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $strCond = "";
        $arrCond = array();
        if(!empty($arrInput['item_resource_ids'])){
            $arrCond[] = "`id` in (" . implode(',', $arrInput['item_resource_ids']) . ")";
        } else {
            $arrCond[] = "`item_id` in (" . implode(',', $arrInput['item_ids']) . ")";
        }

        if (empty($arrInput['type']) || $arrInput['type'] == 1) {
            $arrCond[] = "((`status` = 1) or (`status` = 4))";
        } elseif ($arrInput['type'] == 2) {
            $arrCond[] = "((`status` = 3) or (`status` = 1) or (`status` = 4))";
        }

        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }
        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getItemResourceIsOnAudit',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('checkOnAudit call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $itemResourceArr = $arrDlOut;
        $itemPackageArr = array();

        if(!empty($arrInput['item_resource_ids'])){
            $strCond = "";
            $arrCond = array();

            $arrCond[] = "`item_resource_id` in (" . implode(',', $arrInput['item_resource_ids']) . ")";
            if (empty($arrInput['type'])) {
                $arrCond[] = "`status` = 0 ";
            } elseif ($arrInput['type'] == 2) {
                $arrCond[] = "((`status` = 0) or (status = 2))";
            }

            if (!empty($arrCond)) {
                $strCond = implode(' and ', $arrCond);
            }
            $arrDlInput = array(
                'cond' => $strCond,
                'function' => 'getItemPackageIsOnAudit',
            );

            $arrDlOut = Lib_Db::call('item', $arrDlInput);
            if (!self::_checkOutput($arrDlOut)) {
                Bingo_Log::warning('checkOnAudit call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
                return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            $itemPackageArr = $arrDlOut;
        }
        if(!empty($itemPackageArr['results'][0])){
            $tmp = array_merge($itemResourceArr['results'][0], $itemPackageArr['results'][0]);
        } else {
            $tmp = $itemResourceArr['results'][0];
        }

        $blockIds = array();
        if(!empty($tmp)){
            foreach ($tmp as $key => $value) {
                if (!in_array($value['item_resource_id'], $blockIds)) {
                    $blockIds[] = $value['item_resource_id'];
                }
            }
        }

        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $blockIds, 'output');
    }

    /*
     * @param $arrInput
     * @return multitype
     *
    item_resource_id
    获取item_resource_id最新已通过包数据
     */
    public static function getApprovedPackage($arrInput)
    {
        if (intval($arrInput['item_resource_id']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrDlInput = array(
            'item_resource_id' => $arrInput['item_resource_id'],
            'function' => 'getApprovedPackage',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getApprovedPackage call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /*
     * @param $arrInput
     * @return multitype
     *
    item_resource_id
    获取item_resource_id的最新一条发布记录
     */
    public static function getPublishRecord($arrInput)
    {
        if (intval($arrInput['item_resource_id']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!isset($arrInput['release_status'])) {
            //默认取已发布
            $arrInput['release_status'] = 4;
        } else {
            $arrInput['release_status'] = intval($arrInput['release_status']);
        }

        $arrDlInput = array(
            'item_resource_id' => $arrInput['item_resource_id'],
            'release_status' => $arrInput['release_status'],
            'function' => 'getPublishRecord',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getPublishRecord call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /**
     * @param $arrInput
     * 获取是否有定时发布的记录
     */
    public static function getIsTimingPublishByResourceId($arrInput){
        //check param
        if(empty($arrInput['item_resource_id'])){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //call db
        $arrDlInput = array(
            'item_resource_id' => $arrInput['item_resource_id'],
            'status' => 0,//发布状态：-1 发布失败 0 待发布 1发布成功
            'type' => 2,//任务类型：1 立即发布 2定时发布
            'function' => 'getIsTimingPublishByResourceId',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getIsTimingPublishByResourceId call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrResult = $arrDlOut['results'][0][0];
        //build
        //return
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrResult, 'output');

    }

    /*
    * @param $arrInput
    * @return multitype
    *
   item_resource_id
   获取某类状态的resource信息
    */
    public static function getItemResourceByCond($arrInput)
    {
        if (!isset($arrInput['status'])) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $intPn = empty($arrInput['pn']) ? 1 : $arrInput['pn'];
        $intRn = empty($arrInput['rn']) ? 20 : $arrInput['rn'];

        $strCond = "";
        $arrCond = array();
        $arrCond[] = "`status` = {$arrInput['status']} ";
        if (!empty($arrInput['name'])) {
            $arrCond[] = "`name` = '{$arrInput['name']}' ";
        }
        if (!empty($arrInput['item_resource_id'])) {
            $arrCond[] = "`id` = {$arrInput['item_resource_id']} ";
        }

        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }


        $arrDlInput = array(
            'cond' => $strCond,
            'offset' => ($intPn - 1) * $intRn,
            'rn' => $intRn,
            'ie' => 'utf-8',
            'function' => 'getItemResourceByCond',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemResourceByCond call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /*
    * @param $arrInput
    * @return multitype
    *
   item_resource_id
   获取某类状态的resource信息
    */
    public static function getItemResourceCountByCond($arrInput)
    {
        if (!isset($arrInput['status'])) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $strCond = "";
        $arrCond = array();
        $arrCond[] = "`status` = {$arrInput['status']} ";
        if (!empty($arrInput['name'])) {
            $arrCond[] = "`name` = '{$arrInput['name']}' ";
        }
        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }
        $arrDlInput = array(
            'cond' => $strCond,
            'ie' => 'utf-8',
            'function' => 'getItemResourceCountByCond',
        );
        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemResourceCountByCond call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /*
     * @param $arrInput
     * item_resource_ids
     * 通过resource_id集合， 获取"全量更新游戏"提审状态的安装包信息
     * @return multitype
     *
     */
    public static function auditGetPackage($arrInput)
    {
//        $arrInput['item_resource_ids'] = [1,3];
        if (empty($arrInput['item_resource_ids'])) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' param error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $strCond = "";
        $arrCond = array();
        $arrCond[] = "`item_resource_id` in (" . implode(',', $arrInput['item_resource_ids']) . ")";
        $arrCond[] = "`status` = 0 ";
        $arrCond[] = "`source` = 0 ";

        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }
        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'auditGetPackage',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('checkOnAudit call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrData = $arrDlOut['results'];

        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }
    //发送审核通知
    public static function sendAuditNotify($arrInput)
    {
        if (empty($arrInput['uid'])) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' param error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if($arrInput['type'] != self::STATUS_PASS && $arrInput['type'] != self::STATUS_UNPASS
            || $arrInput['source'] != 1 && $arrInput['source'] != 2) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' param error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if(strlen($arrInput['reject_reason']) > 20) {
            $arrInput['reject_reason'] = mb_substr($arrInput['reject_reason'] , 0 , 20)."...";
        }
        $content = self::$message_arr[$arrInput['source']][$arrInput['type']];
        if($arrInput['source'] == 2) {
            $content .= $arrInput['reject_reason'];
        }

        $strEmailAddress = $arrInput['email_address'];

        $arrInput = array(
            'req' => array(
                'category_id' => 1, // 通知分类ID  1:系统消息，2:吧主消息 3:T豆消息 4:活动通知
                'user_id'     => $arrInput['uid'],
                'title'       => '官方号审核结果通知',
                'content'     => $content,
                'call_from'   => 'check_bawutask',
            ),
        );
        $ret = Tieba_Service::call('sysmsg', 'sendSysmsg', $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($ret === false || (isset($ret['errno']) && intval($ret['errno']) !== Tieba_Errcode::ERR_SUCCESS)) {
            Bingo_Log::warning(__FUNCTION__." call service failed : sendSysmsg, input =".serialize($arrInput)." output =".serialize($ret));
        }


        if(!empty($strEmailAddress)){
            $arrEmailInput = array(
                'send_email_to' => $strEmailAddress,
                'sender' => self::SENDER,
                'content' => $content,
                'title' => self::EMAIL_TITLE,
            );
            $arrRet = self::sendAuditEmail($arrEmailInput);
            if(false === $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno'] || 1 !=$arrRet['output']['status']){
                Bingo_Log::warning("misAudit:sendAuditEmail fail,input:".json_encode($arrEmailInput)."output:".json_encode($arrRet));
            }
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array(), 'output');
    }
    /*
     * @param $arrInput
     * item_resource_ids
     * 过审
     * @return multitype
     *
     */
    public static function misAudit($arrInput)
    {
//        $arrInput['item_resource_ids'] = [1,3];
        $item_resource_id = intval($arrInput['item_resource_id']);
        $source = intval($arrInput['source']);
        $type = intval($arrInput['type']);
        if (empty($item_resource_id) || empty($source) || empty($type) || empty($arrInput['uid'])) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' param error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //审核接口得到发送邮箱地址 以及全量更新时的发布方式
        $arrRedisInput = array(
            'item_resource_id' => intval($arrInput['item_resource_id']),
        );
        $arrRedisOutput = self::getPublishEmailByResourceId($arrRedisInput);
        $intPublishType = empty($arrRedisOutput['output']['publish_type']) ? 0 : $arrRedisOutput['output']['publish_type'];
        $strEmailAddress = empty($arrRedisOutput['output']['email_address']) ? '' : $arrRedisOutput['output']['email_address'];

        $icon_url = !empty($arrInput['icon_url']) ? strval($arrInput['icon_url']) : '';
        $log = $arrInput['log'];
        //仅更新安装包
        if ($arrInput['source'] == 2) {
            if (empty($arrInput['package_id'])) {
                return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            //过审， 没过审的包变更
            $input = array(
                'package_id' => $arrInput['package_id'],
                'status' => $arrInput['type'] == 1 ? 3 : 1,
                'op_uid' => $arrInput['op_uid'],
                'op_name' => $arrInput['op_name'],
                //同一时间只有一个package进入审核态 所有此处不判断source了
            );

            if ($arrInput['type'] == 2) {
                $input['reject_reason'] = $arrInput['reject_reason'];
            }

            // 检查是不是apk包，是apk包，则检查apk_version_code是不是解析出来了
            if ($arrInput['type'] == 1 && !self::checkApkVersionCode($arrInput)){
                Bingo_Log::warning('check apk_version_code fail,package_id is: '.$arrInput['package_id']);
                return Lib_Log::errRet(Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL);
            }

            $arrDlOut = self::updatePackage($input);
            //更新失败
            if (!self::_checkOutput($arrDlOut)) {
                Bingo_Log::warning('updatePackage call db fail, input:[' . json_encode($input) . '],output:[' . json_encode($arrDlOut) . ']');
                return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            if ($arrInput['type'] == 1) {
                $input = array(
                    'package_id' => $arrInput['package_id'],
                    'item_resource_id' => $arrInput['item_resource_id'],
                );
                $arrDlOut = self::updateItemGameAttrByResourceId($input);
                //更新失败
                if (!self::_checkOutput($arrDlOut)) {
                    Bingo_Log::warning('updateItemGameAttrByResourceId call db fail, input:[' . json_encode($input) . '],output:[' . json_encode($arrDlOut) . ']');
                    return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                }
            }
        }
        //全量变更
        if ($arrInput['source'] == 1) {

            $handle = Lib_Db::init('item');
            if (false == $handle) {
                Bingo_Log::warning(__FUNCTION__ . ' init db failed.');
                return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array());
            }
            $handle = Lib_Db::startTransaction();
            if (false == $handle) {
                Bingo_Log::warning(__FUNCTION__ . ' db startTransaction failed.');
                return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array());
            }
            //package
            if (!empty($arrInput['package_id'])) {
                $input = array(
                    'package_id' => $arrInput['package_id'],
                    'status' => $arrInput['type'] == 1 ? 2 : 1,
                    //同一时间只有一个package进入审核态 所以此处不判断source了
                );

                // 检查是不是apk包，是apk包，则检查apk_version_code是不是解析出来了
                if ($arrInput['type'] == 1 && !self::checkApkVersionCode($arrInput)){
                    Bingo_Log::warning('check apk_version_code fail,package_id is: '.$arrInput['package_id']);
                    return Lib_Log::errRet(Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL);
                }

                $arrDlOut = self::updatePackage($input);
                //更新失败
                if (!self::_checkOutput($arrDlOut)) {
                    Lib_Db::rollback();
                    Bingo_Log::warning('updatePackage call db fail, input:[' . json_encode($input) . '],output:[' . json_encode($arrDlOut) . ']');
                    return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                }
            }

            //默认手动发布-status=3 (审核通过)
            $intResourceStatus = $arrInput['type'] == 1 ? 3 : 2;
            //如果是过审后直接发布   1直接发布 2手动发布
            if(!empty($intPublishType) && self::PUBLISH_TYPE_IMMEDIATE == $intPublishType){
                //1 状态的变化 item_resource status=4 发布中
                $intResourceStatus = $arrInput['type'] == 1 ? 4 : 2;
                //2 写item_resource_release_tasks表 --在他们之后写
            }
            //item_resource修改
            $input = array(
                'id' => $arrInput['item_resource_id'],
                'status' => $intResourceStatus,
            );

            if ($arrInput['type'] == 1) {
                $input['smart_color'] = self::_getItemSmartColor($icon_url);
            }

            $arrDlOut = self::updateItemResourceById($input);
            //更新失败
            if (!self::_checkOutput($arrDlOut)) {
                Lib_Db::rollback();
                Bingo_Log::warning('updateItemResourceById call db fail, input:[' . json_encode($input) . '],output:[' . json_encode($arrDlOut) . ']');
                return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }

            //audit修改
            $input = array(
                'item_resource_id' => intval($arrInput['item_resource_id']),
                'status' => $arrInput['type'] == 1 ? 2 : 1,
                'item_name' => $arrInput['item_name'],
                'log' => addslashes($log),
                'op_uid' => $arrInput['op_uid'],
                'op_name' => $arrInput['op_name'],
            );

            if ($arrInput['type'] == 2) {
                $input['reject_reason'] = $arrInput['reject_reason'];
            }

            $arrDlOut = self::updateAuditByResourceId($input);
            if (!self::_checkOutput($arrDlOut)) {
                Lib_Db::rollback();
                Bingo_Log::warning('updateAuditByResourceId call db fail, input:[' . json_encode($input) . '],output:[' . json_encode($arrDlOut) . ']');
                return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            //认领行为回写item_claim_record 表
            if (($arrInput['action_type'] == '1') && ($arrInput['type'] == 1)) {
                $input = array(
                    'item_id' => $arrInput['item_id'],
                    'create_uid' => $arrInput['create_uid'],
                    'create_time' => time(),
                    'update_uid' => $arrInput['op_uid'],
                    'update_time' => time(),
                );
                $arrDlOut = self::insertItemClaimRecord($input);
                if (!self::_checkOutput($arrDlOut)) {
                    Lib_Db::rollback();
                    Bingo_Log::warning('insertItemClaimRecord call db fail, input:[' . json_encode($input) . '],output:[' . json_encode($arrDlOut) . ']');
                    return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                }
            }

            //同意或者拒绝 都要对record进行修改
            $arrDBInput = array(
                'item_resource_id' => intval($arrInput['item_resource_id']),
                'release_status' => $arrInput['type'] == 2 ? 2 : 3,
                'function' => 'changePublishStatusRecord',
            );
            $arrDBOutput = Lib_Db::call('item', $arrDBInput);
            if (!$arrDBOutput || $arrDBOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Lib_Db::rollback();
                Bingo_Log::warning('changePublishStatusRecord call db failed! input[' . json_encode($arrDBInput) . '] output[' . json_encode($arrDBOutput) . ']');
                return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            //2写item_resource_release_tasks表 --审核通过后且直接发布的话
            if(($arrInput['type'] == 1) && !empty($intPublishType) && self::PUBLISH_TYPE_IMMEDIATE == $intPublishType ){
                //直接发布不写操作人
                $arrDBInput = array(
                    'item_resource_id' => intval($arrInput['item_resource_id']),
                    'item_id' => intval($arrInput['item_id']),
                    'action_type' => $arrInput['action_type'],
                    'source' => 0,  //发布来源：0默认全流程 1仅更新安装包 2仅更新发布状态
                    'type' => 1,  //任务类型：1 立即发布 2定时发布
                    'status' => 0,  //待发布
                    'timingstamp' => 0, //直接发布不需要时间戳
                    'create_time' => time(),
                    'function' => 'insertItemReleaseTasksAfterAudit',
                );
                $arrDBOutput = Lib_Db::call('item', $arrDBInput);

                if (!$arrDBOutput || $arrDBOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                    Lib_Db::rollback();
                    Bingo_Log::warning('insertItemReleaseTasksAfterAudit call db failed! input[' . json_encode($arrDBInput) . '] output[' . json_encode($arrDBOutput) . ']');
                    return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                }

            }
            $handle = Lib_Db::commit();
            if (false == $handle) {
                Bingo_Log::warning(__FUNCTION__ . ' db commit failed.');
                Lib_Db::rollback();
                return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
            }
        }
        //发送审核通知
        $senInput = array(
            'source'       => $arrInput['source'],
            'type'         => $arrInput['type'],
            'uid'          => $arrInput['uid'],
            'reject_reason'=> $arrInput['reject_reason'],
            'email_address'=> $strEmailAddress,
        );
        $output = self::sendAuditNotify($senInput);
        if (!$output || $output['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('sendAuditNotify  failed! input[' . json_encode($senInput) . '] output[' . json_encode($output) . ']');
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array(), 'output');
    }

    /*
     * @param $arrInput
     * @return multitype
     *
    item_resource_id
    更新package
     */
    public static function updatePackage($arrInput)
    {
        if ((intval($arrInput['package_id']) <= 0) || (count($arrInput) < 2)) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $strCond = "";
        $arrCond = array();
        if (!empty($arrInput['status'])) {
            $arrCond[] = "`status` = {$arrInput['status']} ";
        }

        if (!empty($arrInput['reject_reason'])) {
            $arrCond[] = "`reject_reason` = '{$arrInput['reject_reason']}' ";
        }

        if (!empty($arrInput['op_uid'])) {
            $arrCond[] = "`op_uid` = {$arrInput['op_uid']} ";
        }

        if (!empty($arrInput['op_name'])) {
            $arrCond[] = "`op_name` = '{$arrInput['op_name']}' ";
        }

        $arrCond[] = "`update_time` = " . time();

        if (!empty($arrCond)) {
            $strCond = implode(' , ', $arrCond);
        } else {
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'package_id' => $arrInput['package_id'],
            'cond' => $strCond,
            'function' => 'updatePackage',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('updatePackage call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /*
     * @param $arrInput
     * @return multitype
     *
    item_resource_id
    更新resource
     */
    public static function updateItemResourceById($arrInput)
    {
        if ((intval($arrInput['id']) <= 0) || (count($arrInput) < 2)) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $strCond = "";
        $arrCond = array();
        if (!empty($arrInput['status'])) {
            $arrCond[] = "`status` = {$arrInput['status']} ";
        }

        if (!empty($arrInput['status'])) {
            $arrCond[] = "`smart_color` = '{$arrInput['smart_color']}' ";
        }

        if (!empty($arrInput['create_uid'])) {
            $arrCond[] = "`create_uid` = '{$arrInput['create_uid']}' ";
        }

        $arrCond[] = "`update_time` = " . time();

        if (!empty($arrCond)) {
            $strCond = implode(' , ', $arrCond);
        } else {
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'id' => $arrInput['id'],
            'cond' => $strCond,
            'function' => 'updateItemResourceById',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);

        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('updatePackage call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /*
    * @param $arrInput
    * @return multitype
    *
   item_resource_id
   更新线上attr
    */
    public static function updateItemGameAttrByResourceId($arrInput)
    {
        if ((intval($arrInput['item_resource_id']) <= 0) || (count($arrInput) < 2)) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $strCond = "";
        $arrCond = array();
        if (!empty($arrInput['package_id'])) {
            $arrCond[] = "`package_id` = {$arrInput['package_id']} ";
        }

        if (!empty($arrInput['developer'])) {
            $arrCond[] = "`developer` = '{$arrInput['developer']}' ";
        }
        if (!empty($arrInput['publisher'])) {
            $arrCond[] = "`publisher` = '{$arrInput['publisher']}' ";
        }
        if (!empty($arrInput['language'])) {
            $arrCond[] = "`language` = {$arrInput['language']} ";
        }
        if (!empty($arrInput['is_network'])) {
            $arrCond[] = "`is_network` = {$arrInput['is_network']} ";
        }
        if (!empty($arrInput['is_purchase'])) {
            $arrCond[] = "`is_purchase` = {$arrInput['is_purchase']} ";
        }
        if (!empty($arrInput['is_anti_addiction'])) {
            $arrCond[] = "`is_anti_addiction` = {$arrInput['is_anti_addiction']} ";
        }
        if (!empty($arrInput['is_open_official'])) {
            $arrCond[] = "`is_open_official` = {$arrInput['is_open_official']} ";
        }
        if (!empty($arrInput['screenshots'])) {
            $arrCond[] = "`screenshots` = '{$arrInput['screenshots']}' ";
        }
        if (!empty($arrInput['videos'])) {
            $arrCond[] = "`videos` = '{$arrInput['videos']}' ";
        }
        if (!empty($arrInput['qualification_extra'])) {
            $arrCond[] = "`qualification_extra` = '{$arrInput['qualification_extra']}' ";
        }
        if (!empty($arrInput['update_uid'])) {
            $arrCond[] = "`update_uid` = '{$arrInput['update_uid']}' ";
        }

        $arrCond[] = "`update_time` = " . time();

        if (!empty($arrCond)) {
            $strCond = implode(' , ', $arrCond);
        } else {
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'item_resource_id' => $arrInput['item_resource_id'],
            'cond' => $strCond,
            'function' => 'updateItemGameAttrByResourceId',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('updateItemGameAttrByResourceId call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /*
   * @param $arrInput
   * @return multitype
   *
  item_id
  更新线上attr
   */
    public static function updateItemGameAttrByItemId($arrInput)
    {
        if ((intval($arrInput['item_id']) <= 0) || (count($arrInput) < 2)) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $strCond = "";
        $arrCond = array();
        if (!empty($arrInput['package_id'])) {
            $arrCond[] = "`package_id` = {$arrInput['package_id']} ";
        }

        if (!empty($arrInput['developer'])) {
            $arrCond[] = "`developer` = '{$arrInput['developer']}' ";
        }
        if (!empty($arrInput['publisher'])) {
            $arrCond[] = "`publisher` = '{$arrInput['publisher']}' ";
        }
        if (!empty($arrInput['language'])) {
            $arrCond[] = "`language` = {$arrInput['language']} ";
        }
        if (!empty($arrInput['is_network'])) {
            $arrCond[] = "`is_network` = {$arrInput['is_network']} ";
        }
        if (!empty($arrInput['is_purchase'])) {
            $arrCond[] = "`is_purchase` = {$arrInput['is_purchase']} ";
        }
        if (!empty($arrInput['is_anti_addiction'])) {
            $arrCond[] = "`is_anti_addiction` = {$arrInput['is_anti_addiction']} ";
        }
        if (!empty($arrInput['is_open_official'])) {
            $arrCond[] = "`is_open_official` = {$arrInput['is_open_official']} ";
        }
        if (!empty($arrInput['screenshots'])) {
            $arrCond[] = "`screenshots` = '{$arrInput['screenshots']}' ";
        }
        if (!empty($arrInput['videos'])) {
            $arrCond[] = "`videos` = '{$arrInput['videos']}' ";
        }
        if (!empty($arrInput['qualification_extra'])) {
            $arrCond[] = "`qualification_extra` = '{$arrInput['qualification_extra']}' ";
        }

        $arrCond[] = "`update_time` = " . time();

        if (!empty($arrCond)) {
            $strCond = implode(' , ', $arrCond);
        } else {
            Bingo_Log::warning('updateItemGameAttrByItemId param_error, arrCond:' . json_encode($arrCond));
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'item_id' => $arrInput['item_id'],
            'cond' => $strCond,
            'function' => 'updateItemGameAttrByItemId',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('updateItemGameAttrByItemId call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /*
    * @param $arrInput
    * @return multitype
    *
   item_resource_id
   更新audit数据
    type对应查询的status状态 0 待审 1 审核拒绝 2 审核通过
    */
    public static function updateAuditByResourceId($arrInput, $type = 0)
    {
        if ((intval($arrInput['item_resource_id']) <= 0) || (count($arrInput) < 2)) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $strCond = "";
        $arrCond = array();
        if (!empty($arrInput['log'])) {
            $arrCond[] = "`log` = '" . $arrInput['log'] . "' ";
        }

        if (!empty($arrInput['status'])) {
            $arrCond[] = "`status` = {$arrInput['status']} ";
        }

        if (!empty($arrInput['reject_reason'])) {
            $arrCond[] = "`reject_reason` = '{$arrInput['reject_reason']}' ";
        }

        if (!empty($arrInput['op_name'])) {
            $arrCond[] = "`op_name` = '{$arrInput['op_name']}' ";
        }
        if (!empty($arrInput['op_uid'])) {
            $arrCond[] = "`op_uid` = {$arrInput['op_uid']} ";
        }

        if (!empty($arrInput['item_name'])) {
            $arrCond[] = "`item_name` = '{$arrInput['item_name']}' ";
        }

        $arrCond[] = "`update_time` = " . time();

        if (!empty($arrCond)) {
            $strCond = implode(' , ', $arrCond);
        } else {
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'item_resource_id' => $arrInput['item_resource_id'],
            'type' => $type,
            'cond' => $strCond,
            'function' => 'updateAuditByResourceId',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('updateItemGameAttrByResourceId call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }


    /*
    * @param $arrInput
    * @return multitype
    *
   item_resource_id
   获取某类状态的audit数据（mis后台已审核使用）
    */
    public static function getItemResourceAudited($arrInput)
    {
        $intPn = empty($arrInput['pn']) ? 1 : $arrInput['pn'];
        $intRn = empty($arrInput['rn']) ? 20 : $arrInput['rn'];

        $strCond = "";
        $arrCond = array();
        $arrCond[] = "((`status` = 1) or (`status` = 2)) ";
        if (!empty($arrInput['name'])) {
            $arrCond[] = "`item_name` = '{$arrInput['name']}' ";
        }

        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }
//        var_dump($strCond);exit;

        $arrDlInput = array(
            'cond' => $strCond,
            'offset' => ($intPn - 1) * $intRn,
            'rn' => $intRn,
            'function' => 'getItemResourceAudited',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemResourceAudited call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    public static function getItemResourceAuditedCount($arrInput)
    {
        $strCond = "";
        $arrCond = array();
        $arrCond[] = "((`status` = 1) or (`status` = 2)) ";
        if (!empty($arrInput['name'])) {
            $arrCond[] = "`name` = '{$arrInput['name']}' ";
        }

        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }

        $arrDlInput = array(
            'cond' => $strCond,
            'ie' => 'utf-8',
            'function' => 'getItemResourceAuditedCount',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemResourceAuditedCount call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }


    /**
     * @brief 获取智能取色色值
     * @return string
     */
    public function _getItemSmartColor($strIconUrl)
    {
        $product = 'bkv0iye6690kf0ui';
        $callfrom = 'tieba';
        $serviceUrl = '/v1/icolor-wuji/c/getMulticolor?method=c_get_multi_color&callfrom=' . $callfrom;
        $colors = array(
            "gradient_color" => array(
                "color_type" => 1,
                "is_filter_black" => 1,
                "is_filter_white" => 1,
                "color_first" => 1,
                "max_s" => 80,
                "min_s" => 40,
                "max_b" => 60,
                "min_b" => 40,
            ),
            "gradient_color_dark" => array(
                "color_type" => 1,
                "is_filter_black" => 1,
                "is_filter_white" => 1,
                "color_first" => 1,
                "max_s" => 80,
                "min_s" => 40,
                "max_b" => 50,
                "min_b" => 30,
            ),
            "button_color" => array(
                "color_type" => 1,
                "is_filter_black" => 1,
                "is_filter_white" => 1,
                "color_first" => 1,
                "max_s" => 80,
                "min_s" => 40,
                "max_b" => 90,
                "min_b" => 70,
            ),
        );
        $strategies = array(
            array(
                "roi" => '',
                "crop" => '',
                "colors" => $colors,
            ),
        );
        $strategies = json_encode($strategies);
        $arrSmartColor = array(
            "gradient_color" => array("value" => '474B4D', "model" => 3),
            "gradient_color_dark" => array("value" => '262829', "model" => 3),
            "button_color" => array("value" => '8E9599', "model" => 3),
        );
        if (!isset($strIconUrl) || empty($strIconUrl)) {
            $strSmartColor = json_encode(array($arrSmartColor));
            return $strSmartColor;
        }
        $arrInput = array(
            'strategies' => $strategies,
            'product' => $product,
            'image' => $strIconUrl,
            'serviceUrl' => $serviceUrl,
        );
        $arrOutput = Tieba_Service::call('common', 'getItemSmartColor', $arrInput, null, null, 'post', 'php', 'utf-8');
        //没有颜色则使用默认值
        if (empty($arrOutput['smartColor']) || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf("call common::getItemSmartColor failed! input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            $strSmartColor = json_encode(array($arrSmartColor));
            return $strSmartColor;
        }
        $ResSmartColor = json_decode($arrOutput['smartColor'], true);
        if (empty($ResSmartColor[0]['button_color']['value']) || empty($ResSmartColor[0]['gradient_color']['value']) || empty($ResSmartColor[0]['gradient_color_dark']['value'])) {
            $strSmartColor = json_encode(array($arrSmartColor));
            return $strSmartColor;
        }
        $strSmartColor = $arrOutput['smartColor'];
        return $strSmartColor;
    }

    /*
* @param $arrInput
* @return multitype
*
item_resource_id
获取某类状态的resourcePackage信息 mis使用
*/
    public static function getItemResourcePackageByCond($arrInput)
    {
        if (count($arrInput) < 2) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $intPn = empty($arrInput['pn']) ? 1 : $arrInput['pn'];
        $intRn = empty($arrInput['rn']) ? 20 : $arrInput['rn'];

        $strCond = "";
        $arrCond = array();

        if (!empty($arrInput['audited'])) {
            $arrCond[] = "`status` !=0 ";
        } else {
            $arrCond[] = "`status` = 0 ";
        }

        if (!empty($arrInput['name'])) {
            $arrCond[] = "`item_name` = '{$arrInput['name']}' ";
        }
        if (!empty($arrInput['item_resource_id'])) {
            $arrCond[] = "`item_resource_id` = {$arrInput['item_resource_id']} ";
        }
        if (!empty($arrInput['source'])) {
            $arrCond[] = "`source` = {$arrInput['source']} ";
        }

        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }

        $arrDlInput = array(
            'cond' => $strCond,
            'offset' => ($intPn - 1) * $intRn,
            'rn' => $intRn,
            'ie' => 'utf-8',
            'function' => 'getItemResourcePackageByCond',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemResourcePackageByCond call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /*
* @param $arrInput
* @return multitype
*
item_resource_id
获取某类状态的resourcePackage信息的数量 mis使用
*/
    public static function getItemResourcePackageCountByCond($arrInput)
    {
        if (count($arrInput) < 2) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $strCond = "";
        $arrCond = array();

        if (!empty($arrInput['audited'])) {
            $arrCond[] = "`status` !=0 ";
        } else {
            $arrCond[] = "`status` = 0 ";
        }
        if (!empty($arrInput['name'])) {
            $arrCond[] = "`item_name` = '{$arrInput['name']}' ";
        }
        if (!empty($arrInput['item_resource_id'])) {
            $arrCond[] = "`item_resource_id` = {$arrInput['item_resource_id']} ";
        }
        if (!empty($arrInput['source'])) {
            $arrCond[] = "`source` = {$arrInput['source']} ";
        }

        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }

        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getItemResourcePackageCountByCond',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemResourcePackageByCond call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /*
 * @param $arrInput
 * @return multitype
 *
item_resource_id
获取线上item_info和item_game_attr的数据（mis后台已审核使用）
 */
    public static function getItemGameInfo($arrInput)
    {
        $intPn = empty($arrInput['pn']) ? 1 : $arrInput['pn'];
        $intRn = empty($arrInput['rn']) ? 20 : $arrInput['rn'];

        $strCond = "";
        $arrCond = array();
        if (!empty($arrInput['name'])) {
            $arrCond[] = "a.name like '%{$arrInput['name']}%' ";
        }

        if (!empty($arrInput['class_id'])) {
            $arrCond[] = "a.class_id = {$arrInput['class_id']} ";
        }

        if (!empty($arrInput['has_package'])) {
            // 如果是查有包的数据，就根据传入的resource id查
            if ($arrInput['has_package'] == 1) {
                if (!empty($arrInput['resource_ids'])) {
                    $temp = implode(',',$arrInput['resource_ids']);
                    $arrCond[] = "b.item_resource_id in (".$temp.")";
                }
            } else {
                // 如果是查没有包的数据，就根据传入的resource id 和 package id是否为0进行查找
                if (!empty($arrInput['resource_ids'])) {
                    $temp = implode(',',$arrInput['resource_ids']);
                    $arrCond[] = "(b.package_id = 0 or b.item_resource_id in (".$temp.")".")";
                } else{
                    $arrCond[] = "b.package_id = 0";
                }
            }
        }
        if (!empty($arrInput['has_settled'])) {
            if ($arrInput['has_settled'] == '1') {
                $arrCond[] = "a.owner_uid != 0";
                $arrCond[] = "b.item_resource_id != 0";
            } else {
                $arrCond[] = "(a.owner_uid = 0 or b.item_resource_id = 0)";
            }
        }

        if (!empty($arrInput['item_id'])) {
            $arrCond = array("a.item_id = {$arrInput['item_id']} ");
        }

        //默认游戏
        $arrCond[] = "a.template_name = '游戏' ";

        if (!empty($arrCond)) {
            $strCond = "where " . implode(' and ', $arrCond);
        }

        $arrDlInput = array(
            'cond' => $strCond,
            'offset' => ($intPn - 1) * $intRn,
            'rn' => $intRn,
            'function' => 'getItemGameInfo',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
//        var_dump($arrDlOut);exit;
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemGameInfo call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /*
* @param $arrInput
* @return multitype
*
item_resource_id
获取线上item_info和item_game_attr的数据（mis后台已审核使用）
*/
    public static function getItemGameInfoCount($arrInput)
    {
        $strCond = "";
        $arrCond = array();
        if (!empty($arrInput['name'])) {
            $arrCond[] = "a.name like '%{$arrInput['name']}%' ";
        }

        if (!empty($arrInput['class_id'])) {
            $arrCond[] = "a.class_id = {$arrInput['class_id']} ";
        }

        if (!empty($arrInput['has_package'])) {
            if ($arrInput['has_package'] == 1) {
                if (!empty($arrInput['resource_ids'])) {
                    $temp = implode(',',$arrInput['resource_ids']);
                    $arrCond[] = "b.item_resource_id in (".$temp.")";
                }
            } else {
                if (!empty($arrInput['resource_ids'])) {
                    $temp = implode(',',$arrInput['resource_ids']);
                    $arrCond[] = "(b.package_id = 0 or b.item_resource_id in (".$temp.")".")";
                } else{
                    $arrCond[] = "b.package_id = 0";
                }
            }
        }

        if (!empty($arrInput['has_settled'])) {
            if ($arrInput['has_settled'] == '1') {
                $arrCond[] = "a.owner_uid != 0";
                $arrCond[] = "b.item_resource_id != 0";
            } else {
                $arrCond[] = "(a.owner_uid = 0 or b.item_resource_id = 0)";
            }
        }

        if (!empty($arrInput['item_ids'])) {
            $temp = implode(',',$arrInput['item_ids']);
            $arrCond[] = "a.item_id in (".$temp.")";
        }


        //默认游戏
        $arrCond[] = "a.template_name = '游戏' ";

        if (!empty($arrCond)) {
            $strCond = "where " . implode(' and ', $arrCond);
        }

        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getItemGameInfoCount',
        );
        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemGameInfoCount call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /**
     * @param array $arrInput
     * @return multitype
     *
     * 获取需要发布的游戏
     */
    public static function getPubTasks($arrInput = array())
    {
        $strCond = "";
        $arrCond = array();
        $arrCond[] = "source = " . $arrInput['source'];  //全流程
        $arrCond[] = "type = " . $arrInput['type'];    //定时发布
        $arrCond[] = "status <= 0";    //发布失败 or 待发布

        if ($arrInput['type'] == 2) {
            //定时发布，改为数据过期5分钟内，按先过期先发布
            $arrCond[] = "timingstamp <= " . time();
        }

        //test
//        $arrCond[] = "create_uid = 2319451302";

        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }

        if ($arrInput['type'] == 2) {
            $strCond .= " order by timingstamp asc limit 10";    //定时发布
        } else {
            $strCond .= " order by create_time asc limit 10";    //直接发布
        }

        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getItemReleaseTasks',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemGameInfoCount call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /*
    * @param $arrInput
    * @return multitype
    *
   item_resource_id
   获取item_resource_id所有发布过的包信息
    */
    public static function getPublishPackage($arrInput)
    {
        if (intval($arrInput['item_resource_id']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrDlInput = array(
            'item_resource_id' => $arrInput['item_resource_id'],
            'function' => 'getPublishPackage',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getPublishPackage call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    public static function updateItemResourcePublishStatusRecord($arrInput)
    {
        return self::update_item_resource_publish_status_record($arrInput);

    }

    /**
     * 写item_game_attr
     * @param $arrInput
     */
    public static function insertItemGameAttr($arrInput = array())
    {
        $arrDBInput = array(
            'item_id' => intval($arrInput['item_id']),
            'item_resource_id' => intval($arrInput['item_resource_id']),
            'developer' => strval($arrInput['developer']),
            'publisher' => strval($arrInput['publisher']),
            'language' => intval($arrInput['language']),
            'is_network' => intval($arrInput['is_network']),
            'is_purchase' => intval($arrInput['is_purchase']),
            'compatibility' => intval($arrInput['compatibility']),
            'compatibility_desc' => strval($arrInput['compatibility_desc']),
            'is_anti_addiction' => intval($arrInput['is_anti_addiction']),
            'is_open_official' => intval($arrInput['is_open_official']),
            'screenshots' => strval($arrInput['screenshots']),  //图片
            'videos' => strval($arrInput['videos']),  //视频
            'is_open_milestone' => intval($arrInput['is_open_milestone']),
            'milestone_extra' => $arrInput['milestone_extra'], //里程碑
            'qualification_extra' => strval($arrInput['qualification_extra']),  //资质信息
            'package_id' => intval($arrInput['package_id']),    //package_id
            'create_time' => time(),
            'create_uid' => intval($arrInput['create_uid']),
            'update_time' => time(),
            'update_uid' => intval($arrInput['create_uid']),
            'function' => 'insertItemGameAttr',
        );

        $arrDBOutput = Lib_Db::call('item', $arrDBInput);
        if (!$arrDBOutput || $arrDBOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Log::warning('call Lib_Db::call failed! input[' . json_encode($arrDBInput) . '] output[' . json_encode($arrDBOutput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }

        $intInsertId = Lib_Db::getInsertID();

        if (empty($intInsertId) || $intInsertId < 0) {
            Lib_Log::warning('call Lib_Db::call failed! input[' . json_encode($arrDBInput) . '] output[' . json_encode($arrDBOutput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array('insertId' => $intInsertId), 'output');
    }

    /**
     * 写item_game_attr
     * @param $arrInput
     */
    public static function insertItemGameAttrFromMis($arrInput = array())
    {
        $arrDBInput = array(
            'item_id' => intval($arrInput['item_id']),
            'item_resource_id' => 0,
            'developer' => strval($arrInput['developer']),
            'publisher' => strval($arrInput['publisher']),
            'language' => intval($arrInput['language']),
            'is_network' => intval($arrInput['is_network']),
            'is_purchase' => intval($arrInput['is_purchase']),
            'compatibility' => 0,
            'compatibility_desc' => '',
            'is_anti_addiction' => intval($arrInput['is_anti_addiction']),
            'is_open_official' => intval($arrInput['is_open_official']),
            'screenshots' => strval($arrInput['screenshots']),  //图片
            'videos' => strval($arrInput['videos']),  //视频
            'is_open_milestone' => 0,
            'milestone_extra' => '', //里程碑
            'qualification_extra' => strval($arrInput['qualification_extra']),  //资质信息
            'package_id' => 0,    //package_id
            'create_time' => time(),
            'create_uid' => 0,
            'update_time' => time(),
            'function' => 'insertItemGameAttr',
        );

        $arrDBOutput = Lib_Db::call('item', $arrDBInput);
        if (!$arrDBOutput || $arrDBOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Lib_Log::warning('call Lib_Db::call failed! input[' . json_encode($arrDBInput) . '] output[' . json_encode($arrDBOutput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }

        $intInsertId = Lib_Db::getInsertID();

        if (empty($intInsertId) || $intInsertId < 0) {
            Lib_Log::warning('call Lib_Db::call failed! input[' . json_encode($arrDBInput) . '] output[' . json_encode($arrDBOutput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'output');
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array('insertId' => $intInsertId), 'output');
    }


    /**
     * 写item_game_attr
     * @param $arrInput
     */
    public static function checkPackage($arrInput = array())
    {
        if ((intval($arrInput['item_resource_id']) <= 0) || empty(strval($arrInput['apk_url']))) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrDlInput = array(
            'item_resource_id' => $arrInput['item_resource_id'],
            'apk_url' => trim($arrInput['apk_url']),
            'function' => 'checkPackageApkUrl',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getPublishPackage call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'][0][0];

        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    /**
     * @param $arrInput
     * 根据itemID获取扩展attr表中数据-支持批量
     */
    public static function getItemGameAttrByItemIds($arrInput){
        //检查参数 item_ids不能为空 >=1 这里不需要client_type
        if (empty($arrInput['item_ids']) || !is_array($arrInput['item_ids'])) {
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        //组装条件
        $strItemIds = implode(',', $arrInput['item_ids']);
        $strCond = " item_id in (".$strItemIds.")";
        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getItemGameAttrByItemIds',
        );
        //然后调数据库批量获取
        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemGameAttrByItemIds call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        //构建返回值
        $arrData = $arrDlOut['results'][0];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);

    }

    /**
     * @param $arrInput
     * 根据resourceID 获取发布状态--支持批量
     * '安卓发布状态 1敬请期待 2预约 3测试 4 开放下载',
     * 'ios发布状态 1敬请期待 2预约 4 开放下载',
     */
    public static function getItemRecordStatusByResourceIds($arrInput){
        //检查参数
        if (empty($arrInput['item_resource_ids']) || !is_array($arrInput['item_resource_ids'])) {
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }

        //以状态为主 先根据端类型查对应的状态 如果是测试/开放下载的话一定会有包的信息 如果没有 就报异常 然后做一个匹配 然后返回
        $strItemResourceIds = implode(',', $arrInput['item_resource_ids']);

        $arrCond = array();
        $arrCond[] = " item_resource_id in (".$strItemResourceIds.")";

        //获取最新的发布状态
        $arrCond[] = "release_status = 4";    //已发布的
        $strCond = implode(' and ', $arrCond);

        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getItemRecordStatusByResourceIds',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemRecordStatusByResourceIds call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        //构建返回值
        $arrData = $arrDlOut['results'][0];
        if(empty($arrData)){
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
        }
        //不为空 按照item_resource_id分组并取出对应update_time最大的那条记录
        $arrRet = self::groupByKey($arrData,'item_resource_id','id');
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);

    }

    /**
     * @param $arrInput
     * @return array|multitype
     * 取最新的包
     */
    public static function getItemPackageByResourceIds($arrInput){

        //检查参数
        if (empty($arrInput['item_resource_ids']) || !is_array($arrInput['item_resource_ids'])
            || empty($arrInput['client_type'])) {
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }

        //以状态为主 先根据端类型查对应的状态 如果是测试/开放下载的话一定会有包的信息 如果没有 就报异常 然后做一个匹配 然后返回
        $strItemResourceIds = implode(',', $arrInput['item_resource_ids']);

        //先获取包信息--根据端类型获取最新的包
        $arrCond = array();
        $arrCond[] = " item_resource_id in (".$strItemResourceIds.")";

        if (Molib_Client_Define::CLIENT_TYPE_IPHONE == $arrInput['client_type']) {
            $arrCond[] = "ios_changelog != '' ";
        }
        if(Molib_Client_Define::CLIENT_TYPE_ANDROID == $arrInput['client_type']){
            $arrCond[] = "apk_url != '' ";
        }
        $arrCond[] = "status = 3";    //已发布的
        $strCond = implode(' and ', $arrCond);

        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getItemPackageByResourceIds',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemPackageByResourceIds call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        //构建返回值
        $arrData = $arrDlOut['results'][0];
        if(empty($arrData)){
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
        }
        //不为空 按照item_resource_id分组并取出对应update_time最大的那条记录
        $arrRet = self::groupByKey($arrData,'item_resource_id','update_time');
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);

    }


    /**
     * 将arr按照$gKey分组，并取出分组后组内$oKey对应value的最大值
     * @param $arr
     * @param string $gKey
     * @param $mKey
     * @return array
     */
    public static function groupByKey($arr, $gKey = '',$mKey = ''){
        $result = [];
        foreach($arr as $k => $v) {
            //逐渐淘汰
            if(isset($result[$v[$gKey]]) && $result[$v[$gKey]][$mKey] >= $v[$mKey]){
                continue;
            }
            $result[$v[$gKey]] = $v;
        }
        return $result;

    }

    /**
     * @param $arrInput
     * 根据item_ids获取对应的item扩展信息，包信息和发布状态
     */
    public static function getItemAttrExtraInfoByItemIds($arrInput){
        //tips:并不是所有的item_id都有对应的attr信息
        //检查参数
        if (empty($arrInput['item_ids']) || !is_array($arrInput['item_ids'])) {
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        //获取attr信息
        $arrAttrInput = array(
            'item_ids' => $arrInput['item_ids'],
        );
        $arrAttrOutput = self::getItemGameAttrByItemIds($arrAttrInput);
        if(false == $arrAttrOutput || Tieba_Errcode::ERR_SUCCESS != $arrAttrOutput['errno']){
            Bingo_Log::warning("getItemGameAttrByItemIds failed,[input]:".serialize($arrAttrInput)
                .",[output]:".serialize($arrAttrOutput));
            return Lib_Log::errRet($arrAttrOutput['errno']);
        }
        //如果没有符合条件的 返回空数组
        if(empty($arrAttrOutput['data'])){
            Bingo_Log::warning("getItemAttr success but no datas");
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }

        $arrAttrList = $arrAttrOutput['data'];

        $arrAttrRet = array();
        foreach ($arrAttrList as &$temp){
            //处理图片和视频
            if(!empty($temp['screenshots'])){
                $temp['screenshots'] = json_decode($temp['screenshots']);
                //如果screenshots带宽高信息，不需要pid来获取宽高信息直接赋值
                if(!empty($temp['screenshots'][0]['url'])) {
                    $temp['image'] = $temp['screenshots'];
                } else {
                    //获取图片的pid
                    $arrPid = self::getPidByUrls($temp['screenshots']);
                    $temp['image'] = $arrPid;
                }
            }
            //视频信息--数据库中有存""的数据 做下兼容 这里返回数组 方便以后扩展
            if(!empty($temp['videos']) && $temp['videos'] != "\"\""){
                $temp['video'][] = json_decode($temp['videos'],true);
            }
            $arrAttrRet[$temp['item_id']] = $temp;
        }

        //if需要包信息 client_type是必须的 没有传 只返回基础信息
        if(empty($arrInput['client_type'])){
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrAttrRet);
        }
        //如果client_type 存在且不为0 查找对应端包信息
        $arrResourceIds = array_column($arrAttrList, 'item_resource_id');

        //并行调用 获取包状态信息以及获取包信息
        $objMulti = new Tieba_Multi('get_item_resource');

        //注册
        $arrStatusInput = array(
            'serviceName' => 'common',
            'method' => 'getItemRecordStatusByResourceIds',
            'input' => array(
                'item_resource_ids' => $arrResourceIds,
            ),
            'ie' => 'utf-8',
        );
        $objMulti->register('common_getItemRecordStatusByResourceIds', new Tieba_Service('common'), $arrStatusInput);


        //获取已发布的包信息的信息--可能没有已发布的包
        $arrPackageInput = array(
            'serviceName' => 'common',
            'method' => 'getItemPackageByResourceIds',
            'input' => array(
                'item_resource_ids' => $arrResourceIds,
                'client_type' => $arrInput['client_type'],
            ),
            'ie' => 'utf-8',
        );
        $objMulti->register('common_getItemPackageByResourceIds', new Tieba_Service('common'), $arrPackageInput);

        $objMulti->call();
        //获取结果
        $arrStatusRet = self::getResourcePackageStatusRet($objMulti,$arrStatusInput);

        if(false === $arrStatusRet){
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array());
        }
        $arrPackageRet = self::getResourcePackageRet($objMulti,$arrPackageInput);

        if(false === $arrPackageRet){
            return Lib_Log::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array());
        }

        //最后拼装数据
        foreach ($arrAttrRet as &$attr){
            //获取状态信息以及包信息 ios发布状态 1敬请期待 2预约 3开放下载
            if (Molib_Client_Define::CLIENT_TYPE_IPHONE == $arrInput['client_type']) {
                $attr['package_status'] = $arrStatusRet[$attr['item_resource_id']]['ios_publish_status'];
                //开放下载  预约可能会没有url信息和ios_itunes_id
                if(3 == $attr['package_status']){
                    //获取包信息/ios_itunes_id(二者只能有一个)
                    $attr['ios_url'] = !empty($arrPackageRet[$attr['item_resource_id']]['ios_url']) ?
                        $arrPackageRet[$attr['item_resource_id']]['ios_url'] : "";
                    $attr['ios_id'] = !empty($arrPackageRet[$attr['item_resource_id']]['ios_itunes_id']) ?
                        $arrPackageRet[$attr['item_resource_id']]['ios_itunes_id'] : "";
                    //其他信息：版本信息 以及隐私数据
                    $attr['package_version'] = $arrPackageRet[$attr['item_resource_id']]['ios_version'];
                    $attr['private_url'] = $arrPackageRet[$attr['item_resource_id']]['ios_private_url'];
                    $attr['update_time'] = $arrPackageRet[$attr['item_resource_id']]['update_time'];
                }
            }
            //安卓发布状态 1敬请期待 2预约 3测试 4开放下载
            if(Molib_Client_Define::CLIENT_TYPE_ANDROID == $arrInput['client_type']){
                $attr['package_status'] = $arrStatusRet[$attr['item_resource_id']]['android_publish_status'];
                //测试、开放下载 获取包信息 因为预约可能没有url信息
                if(3 == $attr['package_status'] || 4 == $attr['package_status']){
                    $attr['apk_url'] = $arrPackageRet[$attr['item_resource_id']]['apk_url'];
                    $attr['apk_name'] = $arrPackageRet[$attr['item_resource_id']]['apk_name'];
                    //其他信息：版本信息 以及隐私数据 以及包大小
                    $attr['package_version'] = $arrPackageRet[$attr['item_resource_id']]['apk_version'];
                    $attr['private_url'] = $arrPackageRet[$attr['item_resource_id']]['apk_private_url'];
                    $attr['package_size'] = $arrPackageRet[$attr['item_resource_id']]['apk_size'];
                    $attr['update_time'] = $arrPackageRet[$attr['item_resource_id']]['update_time'];
                    $attr['package_version_code'] = $arrPackageRet[$attr['item_resource_id']]['apk_version_code'];
                }
            }
        }
        //返回 基础信息 以及包状态 以及包信息
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrAttrRet);
    }

    /**
     * @param $objMulti
     * @param $arrStatusInput
     * 获取包状态信息
     */
    public static function getResourcePackageStatusRet($objMulti,$arrStatusInput){
        $arrStatusOutput = $objMulti->getResult('common_getItemRecordStatusByResourceIds');

        //service异常
        if(false == $arrStatusOutput || Tieba_Errcode::ERR_SUCCESS != $arrStatusOutput['errno']){
            Bingo_Log::warning("getItemRecordStatusByResourceIds failed,[input]:".serialize($arrStatusInput)
                .",[output]:".serialize($arrStatusOutput));
            return false;
        }
        //没有对应的status也算异常
        if(empty($arrStatusOutput['data'])){
            Bingo_Log::warning("getItemRecordStatusByResourceIds failed, because data is illegal(empty), [input]:".serialize($arrStatusInput)
                .",[output]:".serialize($arrStatusOutput));
            return false;
        }
        //获取数据 key:item_resource_id value:包状态(两端都有 下边只取一端 根据端类型去取)
        return $arrStatusOutput['data'];

    }

    /**
     * @param $objMulti
     * @param $arrPackageInput
     * 获取包信息
     */
    public static function getResourcePackageRet($objMulti,$arrPackageInput){

        $arrPackageOutput = $objMulti->getResult('common_getItemPackageByResourceIds');
        //透传service异常
        if(false == $arrPackageOutput || Tieba_Errcode::ERR_SUCCESS != $arrPackageOutput['errno']){
            Bingo_Log::warning("getItemPackageByResourceIds failed,[input]:".serialize($arrPackageInput)
                .",[output]:".serialize($arrPackageOutput));
            return false;
        }
        //获取数据 key:item_resource_id value:包信息(两端都有 下边只取一端 根据端类型去取)
        return $arrPackageOutput['data'];
    }

    /**
     * @param $arrUrls
     * @return array
     * 通过url获取pid
     */
    public static function getPidByUrls($arrUrls){
        $arrPid = array();
        foreach ($arrUrls as $url){
            $strPicSign = Molib_Util_ClientImgLogic::getPicSignFromUrl($url);
            $arrSign = Space_Urlcrypt::decodePicUrlCrypt($strPicSign);
            $arrPid[]['pid'] = $arrSign[1];
        }
        return $arrPid;
    }

    /**
     * 根据item_id 读取权限信息的title以及描述
     * @param $arrInput
     */

    public static function getApkPermission($arrInput)
    {
        if (intval($arrInput['item_id']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'item_id' => $arrInput['item_id'],
            'function' => 'getItemGameAttrByItemId',
        );
        $arrDlOut = Lib_Db::call('item', $arrDlInput);

        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemGameAttrByItemId call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $cond = "item_resource_id = ".strval($arrDlOut[results][0][0]['item_resource_id']);
        $cond .= " and apk_url <> '' ";
        $cond .= " order by id desc ";
        $arrDlInput = array(
            'function' => 'auditGetPackage',
            'cond' => $cond,
        );
        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getPackageById call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $keyStr = $arrDlOut["results"][0][0]["apk_permissions"];
        $res = array(
            'permission' => array(),
        );
        if(empty($keyStr)) {
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $res, 'output');
        }
        $arrRes = explode("@", $keyStr);
        $tmpRes = array();
        foreach($arrRes as $value) {
            $tmpRes[$value] = "";
        }
        $handleWordServer = Wordserver_Wordlist::factory();
        $arrInput = array (
            'table' => 'tb_wordlist_redis_tieba_game_open_platform', //英文表名
            'start' => 0,
            'stop' => -1,
        );
        $arrRes = $handleWordServer->getTableContents ( $arrInput);
        $allInformation = explode("@", $arrRes["ret"]["android_permissions"]);
        $len = count($allInformation);
        for($i = 0; $i < $len - 2; $i++) {
            $key = $allInformation[$i];
            if(array_key_exists($key, $tmpRes)) {
                $tmpRes[$key] = $allInformation[$i + 1];
//                $i += 2;// 匹配成功，跳过后续描述.
            }
        }
        foreach($tmpRes as $key => $value) {
            array_push($res['permission'], array(
                'title' => $key,
                'desc' => $value,
            ));
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $res, 'output');
    }

    /**
     * 检查apk包是否被解析过，解析过则apk_version_code字段不为默认值
     * @param $arrInput
     */
    public static function checkApkVersionCode($arrInput = array()) {
        if (intval($arrInput['package_id']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return false;
        }
        $arrDlInput = array(
            'id' => intval($arrInput['package_id']),
            'function' => 'getPackageById',
        );

        $arrDlOut = Lib_Db::call('item',$arrDlInput);
        if(!self::_checkOutput($arrDlOut)){
            Bingo_Log::warning('getVersionCode call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return false;
        }

        $arrData = $arrDlOut['results'][0][0];

        $result = true;
        // 如果是安卓的包，就判断apk_version_code是不是为默认值0
        if(!empty($arrData['apk_url']) && intval($arrData['apk_version_code'] == 0)){
            $result = false;
        }
        return $result;
    }

    public static function updateItemReleaseTasksById($arrInput){
        if (intval($arrInput['id']) <= 0 || intval($arrInput['timingstamp']) < 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrDlInput = array(
            'id' => $arrInput['id'],
            'timingstamp' => $arrInput['timingstamp'],
            'function' => 'updateItemReleaseTasksById',
        );

        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('updateItemReleaseTasksByItemId call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    public static function getNearlyItemReleaseTasksByItemRId($arrInput){
        if (intval($arrInput['item_resource_id']) <= 0) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if(!isset($arrInput['status'])){
            $arrInput['status'] = 0;
        }
        $strCond = "";
        $arrCond[] = 'item_resource_id = ' . $arrInput['item_resource_id'];
        $arrCond[] = 'status = ' . $arrInput['status'];
        if (!empty($arrCond)) {
            $strCond = implode(' and ', $arrCond);
        }
        $strCond .= ' order by id desc limit 1';

        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getItemReleaseTasks',
        );
        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemReleaseTasks call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = $arrDlOut['results'];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData, 'output');
    }

    public static function getResourceIdByHasPackage($arrInput){
        // has_package 字段, 1表示有, 2表示没有
        if(empty((intval($arrInput['has_package'])))){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' parram error. [input: ' . serialize($arrInput) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        // 获取所有发布过的包记录
        $arrCond = array();
        $arrCond[] = 'status = 3';
        $strCond = implode(' and ', $arrCond);
        $arrDlInput = array(
            'cond' => $strCond,
            'function' => 'getItemPackageByResourceIds',
        );
        $arrDlOut = Lib_Db::call('item', $arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('getItemPackageByResourceIds call db fail, input:[' . json_encode($arrDlInput) . '],output:[' . json_encode($arrDlOut) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrData = $arrDlOut['results'][0];
        $allItemResourceIds = array();
        $hasPackageItemResourceIds = array();
        $resData = array();
        // 判断是不是有包
        foreach ($arrData as $single){
            $allItemResourceIds[] = $single['item_resource_id'];
            if(!empty($single['apk_url']) || !empty($single['ios_url']) || !empty($single['ios_itunes_id'])){
                $hasPackageItemResourceIds[] = $single['item_resource_id'];
            }
        }
        $allItemResourceIds = array_unique($allItemResourceIds);
        $hasPackageItemResourceIds = array_unique($hasPackageItemResourceIds);
        if (intval($arrInput['has_package'])==1 && count($hasPackageItemResourceIds)>0) {
            $resData = $hasPackageItemResourceIds;
        } elseif (intval($arrInput['has_package'])==2){
            $resData = array_diff($allItemResourceIds,$hasPackageItemResourceIds);
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $resData, 'output');
    }

    /**
     * @param $arrInput
     * 保存resource_id对应的发布方式 以及 邮箱地址
     */
    public function savePublishEmailByResourceId($arrInput){

        if(empty($arrInput['item_resource_id'])){
            Bingo_Log::warning("savePublishEmailByResourceId param error,empty item_resource_id");
            return false;
        }

        if(empty($arrInput['publish_type']) && empty($arrInput['email_address'])){
            return false;
        }
        $intResourceId = $arrInput['item_resource_id'];
        $arrParams = array('reqs' => array());

        //如果发布方式不为空
        if(!empty($arrInput['publish_type'])){
            $arrParams['reqs'][] = array(
                'key' => self::ITEM_RESOURCE_PUBLISH_TYPE_KEY . $intResourceId,
                'value'   => $arrInput['publish_type'],
                'seconds' => self::NINETY_DAYS_SECONDS + mt_rand(0, 1800),
            );
        }
        //如果 邮箱地址不为空
        if(!empty($arrInput['email_address'])){
            $arrParams['reqs'][] = array(
                'key' => self::ITEM_RESOURCE_EMAIL_ADDRESS . $intResourceId,
                'value'   => $arrInput['email_address'],
                'seconds' => self::NINETY_DAYS_SECONDS + mt_rand(0, 1800),
            );
        }
        //保存两个key
        $arrRes = Lib_Redis::call('SETEX', $arrParams, self::REDIS_NAME);
        if ($arrRes['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call Lib_Redis::SET failed! input[' . json_encode($arrParams) . '] output[' . json_encode($arrRes) . ']');
            //重试一次
            $arrRes = Lib_Redis::call('SETEX', $arrParams, self::REDIS_NAME);
            if ($arrRes['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call Lib_Redis::SET failed again! input[' . json_encode($arrParams) . '] output[' . json_encode($arrRes) . ']');
            }
            return false;
        }
        return true;
    }

    /**
     * @param $arrInput
     * 查redis获取 发布方式以及邮箱地址
     */
    public function getPublishEmailByResourceId($arrInput){
        if(empty($arrInput['item_resource_id'])){
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $intResourceId = $arrInput['item_resource_id'];
        $strPublishTypeKey = self::ITEM_RESOURCE_PUBLISH_TYPE_KEY . $intResourceId;
        $strEmailKey = self::ITEM_RESOURCE_EMAIL_ADDRESS . $intResourceId;
        $arrParams = array(
            'reqs' => array(
                array(
                    'key' => $strPublishTypeKey,
                ),
                array(
                    'key' => $strEmailKey,
                )
            )
        );

        $arrRes = Lib_Redis::call('GET', $arrParams, self::REDIS_NAME);
        //缓存查询失败
        if (false === $arrRes || $arrRes['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call Redis::GET game_code_[list_id]_list_detail failed! input[' . json_encode($arrParams) . '] output[' . json_encode($arrRes) . ']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        //查询成功的话
        $arrRet = $arrRes['ret'];
        $intPublishType = 0;
        $strEmailAddress = '';
        if(!empty($arrRet[$strPublishTypeKey])){
            $intPublishType = intval($arrRet[$strPublishTypeKey]);
        }
        if(!empty($arrRet[$strEmailKey])){
            $strEmailAddress = $arrRet[$strEmailKey];
        }
        $arrOutput = array(
            'publish_type' => $intPublishType,
            'email_address' => $strEmailAddress,
        );

        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS,$arrOutput,'output');
    }

    /**
     * 发送邮件审核通知
     * @param $arrInput
     */
    public function sendAuditEmail($arrInput){

        $strEmailTo = $arrInput['send_email_to'];
        $strSender = $arrInput['sender'];
        $strContent = $arrInput['content'];
        $strTitle = $arrInput['title'];

        if(empty($strEmailTo) || empty($strSender) || empty($strContent) || empty($strTitle)){
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $headers = "MIME-Version: 1.0\r\n";
        $headers .= "Content-Transfer-Encoding: 8bit\r\n";
        $headers .= "From: ".$strSender."\r\n";
        $headers .= 'Content-type: text/html; charset=utf-8'."\r\n";

        //主题
        $subject = $strTitle;
        $subject = "=?UTF-8?B?".base64_encode($subject)."?=";

        $intStatus = 0;
        $res = mail($strEmailTo, $subject, $strContent, $headers);
        if ($res) {
            $intStatus = 1;
            Bingo_Log::notice('send resource audit result mail success, '.'mail subject = '.$strTitle.",content = ".$strContent);
        } else {
            $intStatus = 0;
            Bingo_Log::warning('send resource audit result mail failed, '.'mail subject = '.$strTitle.",content = ".$strContent);
        }
        $arrOutput = array(
            'status' => $intStatus,
        );
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS,$arrOutput,'output');
    }
}