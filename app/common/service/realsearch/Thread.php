<?php
class Service_Realsearch_Thread{
	/**
	 * @param unknown $arrOut
	 * @return array
	 */
	public static function realsearchGetQueryTid($arrInput){
		if(!isset($arrInput['query_md5']) || strlen($arrInput['query_md5']) != 32
			||!isset($arrInput['query']) || empty($arrInput['query'])
			||md5(trim($arrInput['query'])) != $arrInput['query_md5']){
			Lib_Log::warning(Lib_Log::LOG_PARAM_INVALID,$arrInput);
			return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$input = array(
			'function' => 'getQueryTid',
			'query_md5' => $arrInput['query_md5'],
		);
		$output = Lib_Db::call('realsearch',$input);
		if (!$output || $output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($input).'],output:['.serialize($output).']');
            return Lib_Log::errRet($output['errno']); 
        }
		$thread_id = isset($output['results'][0][0]['thread_id']) ? intval($output['results'][0][0]['thread_id']) : 0;
		return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS,array('thread_id' => $thread_id));
	}

	/**
	 * @param unknown $arrOut
	 * @return array
	 */
	public static function realsearchAddQueryTid($arrInput){
		if(!isset($arrInput['query_md5']) || strlen($arrInput['query_md5']) != 32
			||!isset($arrInput['query']) || empty($arrInput['query'])
			||!isset($arrInput['thread_id']) || empty($arrInput['thread_id'])
			||md5(trim($arrInput['query'])) != $arrInput['query_md5']){
			Lib_Log::warning(Lib_Log::LOG_PARAM_INVALID,$arrInput);
			return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$input = array(
			'function' => 'addQueryTid',
			'query_md5' => $arrInput['query_md5'],
			'query' => trim($arrInput['query']),
			'thread_id' => $arrInput['thread_id'],
			'create_time' => time(),
		);
		$output = Lib_Db::call('realsearch',$input);
		if (!$output || $output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($input).'],output:['.serialize($output).']');
            return Lib_Log::errRet($output['errno']); 
        }
		return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS);
	}

	/**
	 * @param unknown $arrOut
	 * @return array
	 */
	public static function realsearchDelQueryTid($arrInput){
		if(!isset($arrInput['query_md5']) || strlen($arrInput['query_md5']) != 32
			||!isset($arrInput['query']) || empty($arrInput['query'])
			||!isset($arrInput['thread_id']) || empty($arrInput['thread_id'])
			||!isset($arrInput['status']) || empty($arrInput['status'])
			||md5(trim($arrInput['query'])) != $arrInput['query_md5']){
			Lib_Log::warning(Lib_Log::LOG_PARAM_INVALID,$arrInput);
			return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		
		$input = array(
			'function' => 'delQueryTid',
			'query_md5' => $arrInput['query_md5'],
			'query' => trim($arrInput['query']),
			'thread_id' => $arrInput['thread_id'],
			'create_time' => time(),
			'status' => $arrInput['status'],
		);
		$output = Lib_Db::call('realsearch',$input);
		if (!$output || $output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($input).'],output:['.serialize($output).']');
            return Lib_Log::errRet($output['errno']); 
        }
		return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS);
	}
}