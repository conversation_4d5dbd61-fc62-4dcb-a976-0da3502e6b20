// 贴吧智能小程序来源报表
CREATE TABLE `source` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `source` varchar(64) NOT NULL DEFAULT '' COMMENT '来源类型',
  `user_count` int(11) NOT NULL DEFAULT '0' COMMENT '启动用户数',
  `user_distributed` float NOT NULL DEFAULT '0' COMMENT '启动用户分布',
  `average_session_time` int(10) NOT NULL DEFAULT '0' COMMENT '均次使用时长(秒)',
  `create_time` int(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `time` (`create_time`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='贴吧智能小程序来源报表'