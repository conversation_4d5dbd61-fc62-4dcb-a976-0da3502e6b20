<?php
/**
 * @file Moretab.php
 * <AUTHOR>
 * @date 2021年4月2日
 * @brief pcfrs页广告tab——更多游戏
 *  
 **/
class Service_Forum_Moretab_Moretab {
    
    /**
     * 从通用配置后台获取更多广告tab
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getMoreTabConf($arrInput) {
        if (false === self::_checkQueryInput($arrInput)) {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        // schema名初始化
        $arrSchema = array();
        for ($idx = 1; $idx <= $arrInput['schema_num']; $idx++) {
            $arrSchema []= $arrInput['schema_prefix'] . $idx;
        }

        $arrCCMInput = array(
            'project' => $arrInput['project'],
            'conf_schemas' => $arrSchema,
            'need_test_conf' => 0
        );
        $arrRet = Tieba_Service::call('common', 'mgetConfBySchemaFromCCM', $arrCCMInput, null, null, 'post', 'php', 'utf-8', 'local');
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning('call common:mgetConfBySchemaFromCCM failed. input: [' . serialize($arrCCMInput) . '], output: ['. serialize($arrRet) . '].');
            return false;
        }
        $arrConfList = $arrRet['data']['conf_list'];
        
        $newTabConf = array();   // 命中的最新配置
        foreach ($arrSchema as $schema) {
            $conf = $arrConfList[$schema]['conf'];
            if (empty($conf)) {
                continue;
            }
            // 配置比当前已命中最新配置早，跳过
            if (!empty($newTabConf) && $conf['create_time'] < $newTabConf['create_time']) {
                continue;
            }
            if (self::_forumHitConf($arrInput, $conf['customized_fields'])) {
                $newTabConf = $conf;
            }  
        }
        
        $newTabConf = $newTabConf['customized_fields'];
        if (empty($newTabConf['tab_name']) || empty($newTabConf['tab_url'])) {
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $retData = array(
            'more_tab_name' => $newTabConf['tab_name'],
            'more_tab_url' => $newTabConf['tab_url']
        );
        if (!empty($newTabConf['tab_bubble'])) {
            $retData['more_tab_bubble'] = $newTabConf['tab_bubble'];
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $retData);
    }

    /**
     * 判断吧信息是否命中配置
     * @param  [type] $arrInput [description]
     * @param  [type] $conf [description]
     * @return [bool]           [description]
     */
    private static function _forumHitConf($arrInput, $tabConf) {
        // 同时配置一二级目录，需要同时匹配
        if (!empty($tabConf['forum_level1_dir']) && !empty($tabConf['forum_level2_dir'])) {
            if ($tabConf['forum_level1_dir'] === $arrInput['forum_level1_dir'] && $tabConf['forum_level2_dir'] === $arrInput['forum_level2_dir']) {
                return true;
            }
        } else if (!empty($tabConf['forum_level1_dir']) && $tabConf['forum_level1_dir'] === $arrInput['forum_level1_dir']) {
            return true;
        } else if (!empty($tabConf['forum_level2_dir']) && $tabConf['forum_level2_dir'] === $arrInput['forum_level2_dir']) {
            return true;
        }
        // 匹配吧名
        if (!empty($tabConf['forum_name'] && !empty($arrInput['forum_name']))) {
            $arrForumName = explode(',', $tabConf['forum_name']);
            if (in_array($arrInput['forum_name'], $arrForumName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查getMoreTabConf服务入参必要值，设置可选参数默认值
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    private static function _checkQueryInput(&$arrInput) {
        if (empty($arrInput['forum_name']) && 
            empty($arrInput['forum_level1_dir']) && 
            empty($arrInput['forum_level2_dir'])) {
                Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
                return false;
        }
        // 默认业务名
        if (empty($arrInput['project'])) {
            $arrInput['project'] = 'more_tab_config';
        }
        // 默认schema前缀
        if (empty($arrInput['schema_prefix'])) {
            $arrInput['schema_prefix'] = 'more_tab_schema_';
        }
        // schema数量不能超过20
        if (empty($arrInput['schema_num']) || $arrInput['schema_num'] > 20) {
            $arrInput['schema_num'] = 20;
        }
        return true;
    }

    /**
     * 规范化返回结果
     * @param
     * @return
     */
    private static function errRet($errno, $data = array()) {
        $arrRet = array(
            'errno'     => $errno,
            'errmsg'    => Tieba_Error::getErrmsg($errno),
        );
        if (!empty($data)) {
            $arrRet['data'] = $data;
        }
        return $arrRet;
    }
}
?>