<?PHP

class Service_Pic_PicCenter{
    const RECV_ADDR = "http://openapi.tieba.baidu.com/api/v1/thread/picResponse";//接收返回值的接口,暂时使用，之后替换
    public static function getPicCenter($arrInput){
        //智能裁图
        $data = Tieba_Service::getArrayParams($arrInput, 'data');
        //Bingo_Log::warning("getPicCenter json =====".json_encode($data,true));
        if (!isset($data['thread_id']) || $data['thread_id']<= 0)
        {
            Bingo_Log::warning(__METHOD__ . " add thread nmq call back failed, param error,thread_id<0, input=" . json_encode($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        //  只处理图片
        if( !isset($data['image_urls']) || count($data['image_urls'])<=0)
        {
            Bingo_Log::warning(__METHOD__ . " not picture thread  input=" . json_encode($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $picsize=array(
            'op_name'=>"big",
            'op_type'=>"OP_WIDTH_HEIGHT_RATIO",
            'max_width'=>30000,
            'max_height'=>30000,
            'width_ratio'=>1,
            'height_ratio'=>1,
            'crop_strategy'=>4,
            'src_type'=>2001,
            'reserved_days'=>30,
            'url_format'=>"TN_URL_SMART_CROP_RATIO",
        );
        $tn_args=array($picsize);
        $recv_addr=array(self::RECV_ADDR);
        $arrParams=array(
            'user_id'=>109,
            'user_token'=>"tieba",
            'client_version'=>"3.0",
            'protocol'=>4,
            //'trace_id'=>intval(),
            //'objurl'=>"http://tiebapic.baidu.com/forum/pic/item/d1c8a786c9177f3ef63ddc4567cf3bc79f3d5668.jpg",
            'sender_ip'=>"**************",//本机ip，无特殊要求可随意设置
            'recv_addr'=>$recv_addr,
           // 'context_data'=>"7199172926",
            'time_out_sec'=>300,
            'crawl_type'=>12,
            'calc_simihash'=>"false",
            'calc_lowquality'=>"false",
            'tn_args'=>$tn_args,
        );
        $cn=0;
        $all=count($data['image_urls']);
        if($all>3){
            $all=3;
        }
        foreach ($data['image_urls'] as $index=>$picurl){
            $cn++;
            if($cn>$all){
                break;
            }
            $strPicSign = Molib_Util_ClientImgLogic::getPicSignFromUrl($picurl);
            $arrSign = Space_Urlcrypt::decodePicUrlCrypt($strPicSign);
            $pid=$arrSign[1];
            $ontext_data=strval($data['thread_id']).",".$index.",".$pid.",".$all;
            $arrParams['objurl']=$picurl;
            $arrParams['context_data']=$ontext_data;
            $arrParams['trace_id']=intval($data['thread_id'].$index);
            $arrHeader = array(
                'pathinfo' => "Gips3Service/Query",
            );
            $ret = ral('pic_gips', 'post', json_encode($arrParams), rand(), $arrHeader);

            if ($ret === false) {
                Bingo_Log::warning("pic_gips fail errno=".ral_get_errno()." error_msg=".ral_get_error());
                return false;
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    private static function _errRet($errno, $data = array()) {
        $arrRet = array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
        if (!empty($data)) {
            $arrRet['data'] = $data;
        }
        return $arrRet;
    }

}

?>
