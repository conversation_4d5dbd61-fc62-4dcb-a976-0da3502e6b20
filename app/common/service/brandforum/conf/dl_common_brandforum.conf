[dbConf]
dbRalName:DB_forum_encourage


[getBrandThreadByTids]
type: query
[.@command]
sql : select tid, fid, brand_fid, status, hide_type from brandforum_thread_info where brand_fid = {brand_fid:n} and status != {status:n} and tid in ({tids:r}) and is_del = 0

[editBrandThreadStatus]
type: update
use_transaction : 0
[.@command]
sql : update brandforum_thread_info set status = {status:n}, hide_type = {hide_type:n}, op_uid = {op_uid:n}, op_uname = {op_uname:s}, op_time = {op_time:n} where tid in ({tids:r})

[getThreadCountByStatus]
type: query
[.@command]
sql : select count(tid) as count from brandforum_thread_info where brand_fid = {brand_fid:n} and status = {status:n} and is_del = 0

[getBrandThreadListByStatus]
type: query
[.@command]
sql : select tid, fid, uid, thread_type, brand_fid, status, hide_type, op_uid, op_uname, op_time, op_create_time from brandforum_thread_info where brand_fid = {brand_fid:n} and status = {status:n} and is_del = 0 order by op_time desc, op_create_time desc limit {offset:n}, {rn:n}

[delThreadInfoByTid]
type: update
[.@command]
sql : delete from brandforum_thread_info where tid = {tid:n} and status = 0

[dealDataBySql]
type: update
[.@command]
sql : {sql:r}

[delTimeoutThread]
type: update
[.@command]
sql : delete from brandforum_thread_info where op_create_time < {op_create_time:n} and status = 0

[editThreadIsDel]
type: update
use_transaction : 0
[.@command]
sql : update brandforum_thread_info set is_del = {is_del:n} where brand_fid = {brand_fid:n} and fid = {fid:n}
