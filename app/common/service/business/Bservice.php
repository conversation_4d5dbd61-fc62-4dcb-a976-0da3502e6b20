<?php
class Service_Business_Bservice{

    //验证码过期时间，默认600s
    const VARIFY_CODE_EXPIRE_TIME = 120;

    const VARIFY_CODE_LENGTH = 6;

    const VARIFY_CODE_SAVE_PRE = 'varifycode_';
    const REDIS_NAME = 'twlive';
    const TMP_IMG_PRE = "baccount_tmp_img_";
    private static $_call_form_app
        = array(
            1 => 'businessAccount',//商户号
            2 => 'unicom_tieba',//联通贴吧联合会员
            3 => 'mobile_tieba',//移动贴吧联合会员
        );
    protected static $redis = null;
    protected static $tryCount = 2;

    const CUSTOMER_STATUS_DEFAULT = 0;    //代理商全部客户数量
    const CUSTOMER_STATUS_UNPAY = 1;      //代理商待支付的客户数
    const CUSTOMER_STATUS_AUDITING = 2;   //代理商审核中的客户数
    const CUSTOMER_STATUS_SUPPLEMENT = 3; //代理商待补充资料的客户数
    const CUSTOMER_STATUS_PASS = 4;       //代理商审核通过的客户数

    //官方号来源
    const SOURCE_DEFAULT = 0; //用户主动申请
    const SOURCE_MIS = 1;     //运营后台配置
    const SOURCE_AGENT = 2;   //代理商添加
    const SOURCE_SUB_GAME = 3;//游戏子账号

    /**
     * 更新字段，含义见：http://wiki.baidu.com/pages/viewpage.action?pageId=**********
     * @var array
     */
    protected static $arrUpdateFields = array('business_name', 'first_class', 'second_class', 'identifi_explain',
        'business_licence_no', 'business_licence_img', 'identifi_letter_img', 'other_qualifications_img', 'business_nickname',
        'business_intro', 'business_area', 'id_positive_img', 'id_back_img', 'owner_name', 'owner_mobile', 'status',
        'pay_order_no', 'user_id', 'modify_time', 'pass_time', 'shoubai_c_appid', 'owner_cardnum', 'audit_status_bjh',
        'audit_status_auth', 'pay_time', 'bjh_last_fail_time', 'bjh_fail_reason', 'auth_last_fail_time', 'update_check_time', 'id', 
        'check_fail_reason', 'pay_money', 'type', 'source', 'ext_id', 'wait_authenticity_tag', 'auth_fail_type',
        );
	
	/**
	 * 传可变参数
	 * 该函数只校验是否存在该值，对具体的值有效性不做校验
	 * @param unknown $arrInput, x, xx, xxx,
	 * @return  string $key
	 */
	private static function  _checkParam(){
		$args = func_get_args();
		if(null == $args || empty($args) || null == $args[0] || empty($args[0]) || !is_array($args[0])){
			return false;
		}
		$arrInput = $args[0];
		$count = count($args);
		for ($i = 1; $i < $count; $i++){
			if(!isset($arrInput[$args[$i]])){
				return false;
			}
		}
		return true;
	}
	
	/**
	 * 校验非空、非0
	 * @param unknown $arrOut
	 * @return string $key
	 */
	private static function _checkOutput($arrOut, $key = 'errno'){
		if(null == $arrOut || empty($arrOut) || !isset($arrOut[$key]) || Tieba_Errcode::ERR_SUCCESS != $arrOut[$key]){
			return false;
		}
		return true;
	}
	
	/**
	 * @param unknown $arrOut
	 * @return array
	 */
	public static function getBusinessData($arrInput){
        $condition = ' 1 '; 
        if($arrInput['user_id']>0)  
        {
            $condition.= " and  user_id = ".$arrInput['user_id'];
        }
        if($arrInput['shoubai_c_appid']>0)
        {
            $condition.= " and shoubai_c_appid = ".$arrInput['shoubai_c_appid'];
        }
        if($arrInput['audit_status_bjh'] !==null)
        {
            $condition.= " and audit_status_bjh = ".$arrInput['audit_status_bjh'];
        }
        if($arrInput['audit_status_auth'] !==null)
        {
            $condition.= " and audit_status_auth = ".$arrInput['audit_status_auth'];
        }
        if($arrInput['wait_authenticity_tag'] !==null)
        {
            $condition.= " and wait_authenticity_tag = ".$arrInput['wait_authenticity_tag'];
        }
        if($arrInput['auth_fail_type'] !==null)
        {
            $condition.= " and auth_fail_type = ".$arrInput['auth_fail_type'];
        }
        if (isset($arrInput['ext_id'])) {
            $condition.= ' and ext_id = '. $arrInput['ext_id'];
        }
        if (isset($arrInput['source'])) {
            $condition.= ' and source = '. $arrInput['source'];
        }
        if($arrInput['status'] !==null)
        {
            $condition.= " and status = ".$arrInput['status'];
        }
        if($arrInput['business_nickname'] != null)
        {
            $condition.= " and business_nickname = '".$arrInput['business_nickname']."'";
        }
        if($arrInput['business_name'] != null)
        {
            $condition.= " and business_name = '".$arrInput['business_name']."'";
        }
        if($arrInput['pay_time_start'] > 0)
        {
            $condition.=' and pay_time >='.$arrInput['pay_time_start'];
        }
        if($arrInput['pay_time_end'] > 0)
        {
            $condition.=' and pay_time <'.$arrInput['pay_time_end'];
        }
        if($arrInput['status_not'] !==null)
        {
            $condition.= " and status not in(".$arrInput['status_not'].")";
        }
        $condition.=' order by create_time desc ';
        if($arrInput['limit_num'] !==null)
        {
		    $condition.=' limit '.$arrInput['limit_num'];
		}
        if($arrInput['limit_num_end'] > 0)
        {
            $condition.=' ,'.$arrInput['limit_num_end'];
        }
		$arrDlInput = array(
			'function' => 'queryBusinessInfoByCon',
			'condition' => $condition,
		);
        
		$arrDlOut = Lib_Db::call('business',$arrDlInput);
		if (!self::_checkOutput($arrDlOut)) {
			Bingo_Log::warning('queryBusinessInfoByCon call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
			return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		$arrData = $arrDlOut['results'][0];
		return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
	}

    /**
     * 通过user_id 批量获取商户号信息
     * @param $arrInput
     * @return multitype
     */
	public static function mgetBusinessData($arrInput) {
	    $userIds = $arrInput['user_ids'];
	    if ( !is_array($userIds) || empty($userIds)) {
            Bingo_Log::warning('mgetBusinessData param error, input:['.serialize($arrInput).']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

	    $userIdStr = "(" . implode(",", $userIds) . ")";
	    $dlInput = array(
	        'function'  => 'mgetBusinessInfoById',
            'user_ids'  => $userIdStr,
        );

	    $dlOutput = Lib_Db::call('business', $dlInput);
	    if (!self::_checkOutput($dlOutput)) {
	        Bingo_Log::warning("mgetBusinessData call db fail, input=" . serialize($dlInput) . "and output=" . serialize($dlOutput));
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

	    $res = array();
        foreach ($dlOutput['results'][0] as $item) {
            $res[$item['user_id']] = $item;
	    }
	    return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $res);
    }



    /**
     * @param unknown $arrOut
     * @return array
     */
    public static function addBusinessInfo($arrInput){
        $bolBusinessParam = self::_checkParam($arrInput,'business_name','user_id','id_positive_img','owner_name','owner_mobile','status');
        $bolUcParam = self::_checkParam($arrInput,'business_name','user_id','status');
        if($arrInput['source']!= 4 && !$bolBusinessParam){
            Lib_Log::warning(Lib_Log::LOG_PARAM_INVALID,$arrInput);
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if($arrInput['source']== 4 && !$bolUcParam){ //对uc来源的官方号做参数校验
            Lib_Log::warning(Lib_Log::LOG_PARAM_INVALID,$arrInput);
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $curTime = time();
        if($arrInput['owner_mobile'] != null || $arrInput['owner_cardnum'] != null)
        {
            $securityKey = Molib_Util_Ssm_Security::getSecurityKey();
            if($arrInput['owner_mobile'] != null)
            {
                $arrInput['owner_mobile'] = Molib_Util_Ssm_Security::aesEncryptCode($arrInput['owner_mobile'],$securityKey);
            }
            if($arrInput['owner_cardnum'] != null)
            {
                $arrInput['owner_cardnum'] = Molib_Util_Ssm_Security::aesEncryptCode($arrInput['owner_cardnum'],$securityKey);
            }
        }
        $arrReq = array(
            'function' => 'addBusinessInfo',
            'business_name' => strval($arrInput['business_name']),
            'first_class' => strval($arrInput['first_class']),
            'second_class' => strval($arrInput['second_class']),
            'identifi_explain' => strval($arrInput['identifi_explain']), 
            'business_licence_no' => strval($arrInput['business_licence_no']),
            'business_licence_img' => strval($arrInput['business_licence_img']),
            'identifi_letter_img' => strval($arrInput['identifi_letter_img']),
            'other_qualifications_img' => strval($arrInput['other_qualifications_img']),
            'business_nickname' => strval($arrInput['business_nickname']),
            'business_intro' => strval($arrInput['business_intro']),
            'business_area' => strval($arrInput['business_area']),
            'id_positive_img' => strval($arrInput['id_positive_img']),
            'id_back_img' => strval($arrInput['id_back_img']),
            'owner_name' => strval($arrInput['owner_name']),
            'owner_mobile' => strval($arrInput['owner_mobile']),
            'status' => intval($arrInput['status']),
            'user_id'=> intval($arrInput['user_id']),
            'shoubai_c_appid'=>$arrInput['shoubai_c_appid'],
            'owner_cardnum' => $arrInput['owner_cardnum'],
            'create_time' => $curTime,
            'audit_status_bjh' => $arrInput['audit_status_bjh'],
            'audit_status_auth' => $arrInput['audit_status_auth'],
            'wait_authenticity_tag' => isset($arrInput['wait_authenticity_tag']) ? intval($arrInput['wait_authenticity_tag']) : 0,
            'auth_fail_type' => isset($arrInput['auth_fail_type']) ? intval($arrInput['auth_fail_type']) : 0,
            'modify_time' => isset($arrInput['modify_time']) ? $arrInput['modify_time'] : $curTime,
            'pay_time' => isset($arrInput['pay_time']) ? $arrInput['pay_time'] : 0,
            'pass_time' => isset($arrInput['pass_time']) ? $arrInput['pass_time'] : 0,
            'type' => isset($arrInput['type']) ? intval($arrInput['type']) : 0,
            'source' => isset($arrInput['source']) ? intval($arrInput['source']) : 0,
            'ext_id' => isset($arrInput['ext_id']) ? $arrInput['ext_id'] : 0,
        );
        Bingo_Log::warning(__METHOD__.' input========'.json_encode($arrReq));
        $arrRet = Lib_Db::call('business',$arrReq);
        if (!self::_checkOutput($arrRet)) {
            Lib_Log::warning(Lib_Log::LOG_DB_CALL,$arrReq,$arrRet);
            return Lib_Log::errRet($arrRet['errno']);
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 编辑资料，主要用于第二页、第三页及补充资料使用
     * @param $arrInput
     * @return multitype
     */
    public static function editBusinessInfo($arrInput) {
        Bingo_Log::notice(__METHOD__. ' input:'. serialize($arrInput));
        $securityKey = Molib_Util_Ssm_Security::getSecurityKey();
        $update_fields = '';
        if(!self::_checkParam($arrInput, 'update_fields')
            || empty($arrInput['update_fields'])
            || !is_array($arrInput['update_fields'])){
            Bingo_Log::warning('editBusinessInfo input param is invalid.arrInput:['.serialize($arrInput).']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrCondition = $arrInput['condition'];
        $arrUpdateFields = $arrInput['update_fields'];
        if (isset($arrUpdateFields['business_licence_no'])) {
            $arrUpdateFields['business_licence_no'] = strval($arrUpdateFields['business_licence_no']);
        }
        foreach ($arrUpdateFields as $key => &$value){
            if(!in_array($key, self::$arrUpdateFields)){
                Bingo_Log::warning('editBusinessInfo input param has invalid field, update can ignore. key:['.$key.'] value:['.$value.']');
                continue;
            }
            if($value!=null && ($key=='owner_cardnum' || $key =="owner_mobile"))
            {
                $value = Molib_Util_Ssm_Security::aesEncryptCode($value,$securityKey);
            }
            if(is_int($value) && strlen($value) <= 20){
                empty($update_fields)?$update_fields .= ($key.'='.$value) : $update_fields = ($update_fields .= ', ') . ($key.'='.$value);
            }else if(is_string($value)){
                empty($update_fields)?$update_fields .= ($key.'=\''.$value.'\'') : $update_fields = ($update_fields .= ', ') . ($key.'=\''.$value.'\'');
            }
        }
        $conditon = '';
        foreach ($arrCondition as $key => $value){
            if(!in_array($key, self::$arrUpdateFields)){
                Bingo_Log::warning('editBusinessInfo input param has invalid field, condition can ignore. key:['.$key.'] value:['.$value.']');
                continue;
            }
            //批量操作处理
                if(is_numeric($value) && strlen($value) <= 20){
                    empty($conditon)?$conditon .= ($key.'='.$value) : $conditon = ($conditon .= ' and ') . ($key.'='.$value);
                }else if(is_string($value)){
                    empty($conditon)?$conditon .= ($key.'=\''.$value.'\'') : $conditon = ($conditon .= ' and ') . ($key.'=\''.$value.'\'');
                }
            //批量操作处理
        }
        Bingo_Log::warning(__METHOD__.' update fields:'.json_encode($update_fields));
        if(empty($update_fields)){
            Bingo_Log::warning('editBusinessInfo input param is invalid.arrInput:['.serialize($arrInput).']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        Bingo_Log::warning(__METHOD__.' update conditions:'.json_encode($arrCondition));

        $arrReq = array(
            'function' => 'editBusinessInfo',
            'update_fields' => $update_fields,
            'condition' => $conditon,
        );
        
        $arrRet = Lib_Db::call('business',$arrReq);
        if (!self::_checkOutput($arrRet)) {
            Lib_Log::warning(Lib_Log::LOG_DB_CALL,$arrReq,$arrRet);
            return Lib_Log::errRet($arrRet['errno']);
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS);
    }
    

    public static function getCheckFailReasonByUid($arrInput){
         
        if(empty($arrInput['condition']) ){
            Bingo_Log::warning('getCheckFailReasonByUid input param is invalid.arrInput:['.serialize($arrInput).']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrReq = array(
            'function' => 'getCheckFailReasonByUid',
            'condition' => $arrInput['condition'],
        );
        
        $arrRet = Lib_Db::call('business',$arrReq);
        if (!self::_checkOutput($arrRet)) {
            Bingo_Log::warning('getCheckFailReasonByUid arrReq======='.json_encode($arrReq));
            Bingo_Log::warning('getCheckFailReasonByUid db result======='.json_encode($arrRet));
            
            return Lib_Log::errRet($arrRet['errno']);
        }
        $res = $arrRet['results'][0][0];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS,$res);
    }

    //生成验证码，并发送验证码service方法
    //call_from 为来源标示 ， source_info为来源名称，用于短信发送内容拼装
    /**
     * @param $arrInput
     * @return array
     */
    public static function sendVarifyCode($arrInput){
        //T参数校验
        $paramsCheck = array(
            "mobile" , "call_from" , "source_info"  , "user_ip" 
        );
        foreach($paramsCheck as $param){
            if(!isset($arrInput[$param])){
                return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }   
        }       
        $strMobile = $arrInput["mobile"] ;
        $strSource = $arrInput["call_from"] ; 
        $strSourceInfo = ucfirst($arrInput["source_info"]) ;       

        if(self::$_call_form_app[$strSource] == null)
        {
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //生成验证码
        $strVarifyCode = self::generateVarifyCode() ;
        //写入Redis，并设置过期时间 , redis key : tbpass_186xxxxxx_2
        $strRedisSaveKey = self::VARIFY_CODE_SAVE_PRE.$strMobile.'_'.$strSource ;
        $redis_input = array(
            'key' => $strRedisSaveKey,
            'value' => $strVarifyCode."-".time(),
            'seconds' => self::VARIFY_CODE_EXPIRE_TIME,   //过期时间为2min
        ); 
        Bingo_Log::warning("send sms input redis=".var_export($redis_input,1)) ;
        $getredis = self::call("GET", $redis_input);  
        Bingo_Log::warning("getredis==".var_export($getredis,1)) ;
        if($getredis['ret'][$redis_input['key']] != null)
        {
            $redisret = $getredis['ret'][$redis_input['key']];
            $tmp = explode("-",$redisret);
            Bingo_Log::warning("lasttime===".$tmp[1]);
            if($tmp[1]-time() < 60)
            {
                //同一手机号60s内不能重复发送
                return Lib_Log::errRet(Tieba_Errcode::ERR_BUSI_REPEAT_SUBMIT);
            }
        }
        $redis_res = self::call("SETEX", $redis_input);
        Bingo_Log::warning("setredis==".var_export($redis_res,1)) ;
        if (false == $redis_res) {
                Bingo_Log::warning("get redis fail,input:".serialize($redis_input));
                return Lib_Log::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }        

        //发送短信
        $strSmsContent = "您申请的{$strSourceInfo}短信验证码是:{$strVarifyCode} ，有效期两分钟!若非本人操作请尽快修改密码！";
        $arrSmsInput = array(
            'to_mobile' => $strMobile,
            'content' => $strSmsContent,
            'type' => 'normal',//普通类短信；营销类短信自带后缀“回复TD退订”
        );
        $arrSmsOut = Util_Tools::sendSms($arrSmsInput);       
        if(empty($arrSmsOut) || $arrSmsOut['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("varify code send fail , request[".json_encode($arrInput)."]");
            return Lib_Log::errRet(Tieba_Errcode::VERIFICATION_VCODE_SEND_ERROR);
        }
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    public static function checkVarifyCode($arrInput){
        Bingo_Log::warning("input = ".var_export($arrInput,1));
        //T参数校验
        $paramsCheck = array(
            "mobile" , "call_from" , "varify_code"  , "user_ip" 
        );
        foreach($paramsCheck as $param){
            if(!isset($arrInput[$param])){
                return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }   
        }
        $strMobile = $arrInput["mobile"] ;
        $strSource = $arrInput["call_from"] ; 
        $strVarifyCode = $arrInput["varify_code"] ;       
        if(self::$_call_form_app[$strSource] == null)
        {
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        } 

        //粒度控制
        /*if(! self::uegCheck($arrInput["cuid"],$arrInput["user_ip"],$strMobile,$strSource)){
            return Lib_Log::errRet(Tieba_Errcode::ERR_MO_OP_TOO_FAST) ;
        }*/
        
        //验证码与Redis进行校验
        $strRedisSaveKey = self::VARIFY_CODE_SAVE_PRE.$strMobile.'_'.$strSource ;
        $redis_input = array(
            'key' => $strRedisSaveKey,
        );
        Bingo_Log::warning("redisinput==".var_export($redis_input,1));
        $redis_res = self::call("GET", $redis_input);
        if (false === $redis_res) {
                Bingo_Log::warning("get redis fail,input:".serialize($redis_input));
                return Lib_Log::errRet(Tieba_Errcode::VERIFICATION_VCODE_CHECK_ERROR);
        }        
        Bingo_Log::warning("redisret==".var_export($redis_res,1));
        Bingo_Log::warning("strVarifyCode==".$strVarifyCode);

        if($redis_res['ret'][$redis_input['key']] != null)   
        {
            $value = explode("-",$redis_res['ret'][$redis_input['key']]);
            if($value[0] == $strVarifyCode)
            {
                Bingo_Log::warning("in del redis");
                $redis_delres = self::call("DEL", $redis_input);
                if ($redis_delres === false) {
                    Bingo_Log::warning("del varify code redis wrong,input:".serialize($redis_input));
                }
                return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS);
            }
            else
            {
                Bingo_Log::warning("varify code wrong,input:".serialize($redis_input));
                return Lib_Log::errRet(Tieba_Errcode::VERIFICATION_VCODE_NOT_EQUAL);
            }
        }
        else
        {
            Bingo_Log::warning("varify code wrong,input:".serialize($redis_input));
            return Lib_Log::errRet(Tieba_Errcode::VERIFICATION_VCODE_NOT_EQUAL);
        }
       
    }

    /**
     * init redis
     * @param  $[name] [<description>]
     * @return  [<description>]
     */
    private static function initRedis() {
        if (self::$redis == null) {
            self::$redis = new Bingo_Cache_Redis(self::REDIS_NAME);
            if (self::$redis == false) {
                Bingo_Log::warning("init redis [" . self::REDIS_NAME. "] fail:" . serialize(Bd_RalRpc::get_error()));
                return false;
            }
        }
        return true;
    }

    /**
     * @param $method
     * @param $req
     * @return bool
     */
    public static function call($method, $req) {
        $try = 0;
        if (!self::initRedis()){
            return false;
        }

        while ($try < self::$tryCount) {
            $res = self::$redis->$method($req);
            if(Tieba_Errcode::ERR_SUCCESS == $res['err_no']){
                return $res;
            }
            $try++;
        }
        Bingo_Log::warning('call redis:'.$method.' fails. error msg: '. $res['err_msg']);
        return false;
    }
 

    //生成随机验证码
    private static function generateVarifyCode(){
            $min = pow(10 , (self::VARIFY_CODE_LENGTH - 1));
            $max = pow(10,self::VARIFY_CODE_LENGTH ) - 1;
            return (string)rand($min, $max);
    }
 
    private static function uegCheck($cuid,$userIp,$mobile,$callFrom){
        $arrUegCheckReqInfo=array(
            'req'=>array(
                'rulegroup'=>array('app'),
                'app' => self::$_call_form_app[$callFrom],
                'cmd' =>'tbpassLogin',
                'cuid' => $cuid,
                'ip' => $userIp ,
                'mobile' => $mobile ,
            ),
        );

        // 先submit下
        $arrUegCheckResInfo = Tieba_Service::call('anti','antiActsctrlSubmit', $arrUegCheckReqInfo);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrUegCheckResInfo['errno']) {
            Bingo_Log::warning(sprintf("ueg actsctr check request failed. [request : %s ] . [output = %s]", serialize( $arrUegCheckReqInfo ) , serialize($arrUegCheckResInfo)));
        }

        $arrUegCheckResInfo = Tieba_Service::call('anti','antiActsctrlQuery', $arrUegCheckReqInfo);
        if($arrUegCheckResInfo['errno'] === Tieba_Errcode::ERR_SUCCESS && $arrUegCheckResInfo['res']['err_no'] === Tieba_Errcode::ERR_SUCCESS){
            return true;
        }else{
            Bingo_Log::warning(sprintf("ueg actsctr check request failed. [request : %s ] . [output = %s]", serialize( $arrUegCheckReqInfo ) , serialize($arrUegCheckResInfo)));
            return false ;
        }
    }

    public static function getBusinessCountByCon($arrInput){
        $condition = ' 1 ';
        if($arrInput['user_id']>0)
        {
            $condition.= " and  user_id = ".$arrInput['user_id'];
        }
        if($arrInput['shoubai_c_appid']>0)
        {
            $condition.= " and shoubai_c_appid = ".$arrInput['shoubai_c_appid'];
        }
        if($arrInput['audit_status_bjh'] !==null)
        {
            $condition.= " and audit_status_bjh = ".$arrInput['audit_status_bjh'];
        }
        if($arrInput['audit_status_auth'] !==null)
        {
            $condition.= " and audit_status_auth = ".$arrInput['audit_status_auth'];
        }
        if (isset($arrInput['wait_authenticity_tag'])) {
            $condition.= ' and wait_authenticity_tag = '.$arrInput['wait_authenticity_tag'];
        }
        if (isset($arrInput['auth_fail_type'])) {
            $condition.= ' and auth_fail_type = '.$arrInput['auth_fail_type'];
        }
        if($arrInput['status'] !==null)
        {
            $condition.= " and status = ".$arrInput['status'];
        }
        if($arrInput['business_nickname'] != null)
        {
            $condition.= " and business_nickname = '".$arrInput['business_nickname']."'";
        }
        if($arrInput['business_name'] != null)
        {
            $condition.= " and business_name = '".$arrInput['business_name']."'";
        }
        if($arrInput['pay_time_start'] > 0)
        {
            $condition.=' and pay_time >='.$arrInput['pay_time_start'];
        }
        if($arrInput['pay_time_end'] > 0)
        {
            $condition.=' and pay_time <'.$arrInput['pay_time_end'];
        }
        $condition.=' order by create_time desc ';
        $arrDlInput = array(
            'function' => 'getBusinessCountByCon',
            'condition' => $condition,
        );

        $arrDlOut = Lib_Db::call('business',$arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('queryBusinessInfoByCon call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrData = $arrDlOut['results'][0];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
    }

    public static function getBusinessOrderByCon($arrInput){
        $arrDlInput = array(
            'function' => 'getBusinessOrderByCon',
            'start_time' => $arrInput['start_time'],
            'end_time' => $arrInput['end_time'],
            'limit_num' => $arrInput['limit_num'],
            'limit_num_end' => $arrInput['limit_num_end'],
        );

        $arrDlOut = Lib_Db::call('business',$arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('queryBusinessInfoByCon call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrData = $arrDlOut['results'][0];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
    }

    public static function getBusinessOrderCountByCon($arrInput){
        $arrDlInput = array(
            'function' => 'getBusinessOrderCountByCon',
            'start_time' => $arrInput['start_time'],
            'end_time' => $arrInput['end_time'],
        );

        $arrDlOut = Lib_Db::call('business',$arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning('queryBusinessInfoByCon call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrData = $arrDlOut['results'][0];
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
    }

    /**
     * 获取代理商各个类别客户数目
     * @param $arrInput
     * @return multitype
     */
    public static function getAgentAllCustomerCount($arrInput) {
        if (!isset($arrInput['agent_id']) || (isset($arrInput['agent_id']) && empty($arrInput['agent_id']))) {
            Bingo_Log::warning(__METHOD__.' params error, input:'.serialize($arrInput));
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }

        //全部数量
        $arrInput['customer_status'] = self::CUSTOMER_STATUS_DEFAULT;
        $arrDlInput = array(
            'function' => 'getAgentCustomerCount',
            'cond' =>  self::getAgentCustomerSql($arrInput),
        );
        $arrDlOut = Lib_Db::call('business',$arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning(__METHOD__.' call getAgentCustomerCount db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array());
        }
        $intAllCount = isset($arrDlOut['results'][0][0]) ? intval($arrDlOut['results'][0][0]['total_count']) : 0;
        if (empty($intAllCount)) {
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }

        //待付款
        $arrInput['customer_status'] = self::CUSTOMER_STATUS_UNPAY;
        $arrDlInput = array(
            'function' => 'getAgentCustomerCount',
            'cond' =>  self::getAgentCustomerSql($arrInput),
        );
        $arrDlOut = Lib_Db::call('business',$arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning(__METHOD__.' call getAgentCustomerCount db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
        }
        $intUnpayCount = isset($arrDlOut['results'][0][0]) ? intval($arrDlOut['results'][0][0]['total_count']) : 0;

        //审核中
        $arrInput['customer_status'] = self::CUSTOMER_STATUS_AUDITING;
        $arrDlInput = array(
            'function' => 'getAgentCustomerCount',
            'cond' =>  self::getAgentCustomerSql($arrInput),
        );
        $arrDlOut = Lib_Db::call('business',$arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning(__METHOD__.' getAgentCustomerCount call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
        }
        $intAuditingCount = isset($arrDlOut['results'][0][0]) ? intval($arrDlOut['results'][0][0]['total_count']) : 0;

        //补充资料
        $arrInput['customer_status'] = self::CUSTOMER_STATUS_SUPPLEMENT;
        $arrDlInput = array(
            'function' => 'getAgentCustomerCount',
            'cond' =>  self::getAgentCustomerSql($arrInput),
        );
        $arrDlOut = Lib_Db::call('business',$arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning(__METHOD__.' getAgentCustomerCount call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
        }
        $intSupplementCount = isset($arrDlOut['results'][0][0]) ? intval($arrDlOut['results'][0][0]['total_count']) : 0;

        //审核通过
        $arrInput['customer_status'] = self::CUSTOMER_STATUS_PASS;
        $arrDlInput = array(
            'function' => 'getAgentCustomerCount',
            'cond' =>  self::getAgentCustomerSql($arrInput),
        );
        $arrDlOut = Lib_Db::call('business',$arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning(__METHOD__.' getAgentCustomerCount call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
        }
        $intPassCount = isset($arrDlOut['results'][0][0]) ? intval($arrDlOut['results'][0][0]['total_count']) : 0;
        $arrOut = array(
            'all' => $intAllCount,
            'unpay' => $intUnpayCount,
            'auditing' => $intAuditingCount,
            'supplement' => $intSupplementCount,
            'pass' => $intPassCount,
        );
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOut);
    }

    /**
     * 查询代理商的某一类客户数量信息
     * @param $arrInput
     * @return multitype
     */
    public static function getAgentCustomerCount($arrInput) {
        if (!isset($arrInput['agent_id']) || (isset($arrInput['agent_id']) && empty($arrInput['agent_id'])) || !isset($arrInput['customer_status'])) {
            Bingo_Log::warning(__METHOD__.' params error, input:'.serialize($arrInput));
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }

        $strCond = self::getAgentCustomerSql($arrInput);
        if (empty($strCond)) {
            Bingo_Log::warning(__METHOD__.' params error, input:'.serialize($arrInput));
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        $arrDlInput = array(
            'function' => 'getAgentCustomerCount',
            'cond' => $strCond,
        );
        $arrDlOut = Lib_Db::call('business',$arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning(__METHOD__.' call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array());
        }
        $intCount = isset($arrDlOut['results'][0][0]) ? intval($arrDlOut['results'][0][0]['total_count']) : 0;
        $arrOut = array(
            'total_count' => $intCount,
        );
        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOut);
    }

    /**
     * 获取代理商客户list
     * @param $arrInput
     * @return multitype
     */
    public static function getAgentCustomerList($arrInput) {
        if (!isset($arrInput['agent_id']) || (isset($arrInput['agent_id']) && empty($arrInput['agent_id'])) || !isset($arrInput['customer_status'])) {
            Bingo_Log::warning(__METHOD__.' params error, input:'.serialize($arrInput));
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        $arrRes = self::getAgentCustomerCount($arrInput);
        if (!self::_checkOutput($arrRes)) {
            Bingo_Log::warning(__METHOD__.' call getAgentCustomerCount call db fail, input:['.serialize($arrInput).'],output:['.serialize($arrRes).']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array());
        }

        $arrOut = array(
            'list' => array(),
            'total_count' => 0
        );

        $intCount = intval($arrRes['data']['total_count']);
        if (empty($intCount)) {
            return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOut);
        }
        $limit = isset($arrInput['rn']) ? intval($arrInput['rn']) : 10;
        $pn = isset($arrInput['pn']) ? intval($arrInput['pn']) : 1;
        $offset = ($pn - 1) * $limit;
        $strCond = self::getAgentCustomerSql($arrInput);
        if (empty($strCond)) {
            Bingo_Log::warning(__METHOD__.' params error, input:'.serialize($arrInput));
            return Lib_Log::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array());
        }
        $arrDlInput = array(
            'function' => 'getAgentCustomerList',
            'cond' => $strCond,
            'offset' => $offset,
            'limit' => $limit,
        );
        $arrDlOut = Lib_Db::call('business',$arrDlInput);
        if (!self::_checkOutput($arrDlOut)) {
            Bingo_Log::warning(__METHOD__.' call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOut).']');
            return Lib_Log::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrData = $arrDlOut['results'][0];
        $arrOut = array(
            'list' => $arrData,
            'total_count' => $intCount
        );

        return Lib_Log::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOut);
    }

    /**
     * 构造基本查询sql
     * @param $arrInput
     * @return bool|string
     */
    private static function getAgentCustomerSql($arrInput) {
        $intAgentId = intval($arrInput['agent_id']);
        $intCustomerStatus = intval($arrInput['customer_status']);
        $strCond = '';
        switch ($intCustomerStatus) {
            //全部
            case self::CUSTOMER_STATUS_DEFAULT:
                $strCond = "ext_id = {$intAgentId} and source = " . self::SOURCE_AGENT;
                break;
            //待支付
            case self::CUSTOMER_STATUS_UNPAY:
                $strCond = "ext_id = {$intAgentId} and source = " . self::SOURCE_AGENT .' and status = 1';
                break;
            //审核中
            case self::CUSTOMER_STATUS_AUDITING:
                $strCond = "ext_id = {$intAgentId} and source = " . self::SOURCE_AGENT .' and status = 2 and audit_status_bjh in (0, 1) and audit_status_auth = 0';
                break;
            //补充资料
            case self::CUSTOMER_STATUS_SUPPLEMENT:
                $strCond = "ext_id = {$intAgentId} and source = " . self::SOURCE_AGENT .' and status = 2 and (audit_status_bjh > 1 or audit_status_auth > 1)';
                break;
            //已通过
            case self::CUSTOMER_STATUS_PASS:
                $strCond = "ext_id = {$intAgentId} and source = " . self::SOURCE_AGENT .' and status = 2 and audit_status_bjh = 1 and audit_status_auth = 1';
                break;
        }
        if (empty($strCond)) {
            Bingo_Log::warning(__METHOD__.' query sql is empty, input:'.serialize($arrInput));
            return false;
        }

        return $strCond;
    }
 
}
