<?php
/**
 * Created by PhpStorm.
 * User: huxiaomei
 * Date: 2019/6/27
 * Time: 下午4:52
 * 发放稀有卡+奖品策略
 */

class Service_Collectcard_Award_Award {
    const EXPIRE = 5184000;//60天
    const REDIS_COLLECTCARD = 'friend';
    //当期配置
    const REDIS_COMMON_INFO = 'award_common_info';
    const REDIS_MAXNUM_CONF = 'max_num_conf';
    CONST REDIS_TIME_NUMBER_CONF = 'time_number_conf';
    CONST REDIS_TIMES_CONF = 'times_conf';
    CONST REDIS_AWARD_RECORD = 'award_record';

    //必中时期配置
    const REDIS_DATE_RECORD = 'award_date_record';
    const REDIS_MUST_DATE_CONF = 'award_must_draw_conf';
    const REDIS_DATE_CONF = 'must_draw_conf';

    //切割时间
    const SLICE_AWARD_TIME = 1800;//发奖时间池切割时间 30分钟
    const MAX_TIME_INDEX = 47;//30分钟为维度时间index最大值
    const MIN_TIME_INDEX = 0;//30分钟为维度时间index最小值
    const SLICE_AWARD_DATE_TIME = 60;//必中时期时间维度1分钟
    const MAX_CARD_TIMES = 14;//抽卡次数上限值，抽过上限就统一处理
    const MAX_PRIZE_TIMES = 1;//抽奖次数上限值，抽过上限就统一处理
    const MAX_PRIZE_HIT_TIMES = 3;//抽奖必中上限次数

    // DB object
    const SERVICE_NAME = "Service_Collectcard_Award_Award";
    const DB_NAME = 'forum_friend';
    private static $_objDB = null;

    private static $awardIds = array('card4','card5',1,2,3,4,5,6,7,8,9, 13, 14, 15, 16, 17, 18, 19);
    private static $awardTypes = array('card4','card5','prize1','prize2','prize3','prize4', 'prize6', 'prize7', 'prize8');
    private static $cardAwardIds = array('card4','card5');

    //抽稀有卡&卡 方法
    /**
     * [drawAward 获取award配置 from db]
     * @param  [array] $arrInput [description]
     * award_type/can_get_awards/have_to_draw
     * @return [json]           [description]   ·
     */
    public static function drawAward($arrInput){
        //参数判断
        $needAwardType = array('card','prize');
        $cardAwards = array('card4','card5');
        $totalPrizeTypes = array('prize1','prize2','prize3','prize4', 'prize6', 'prize7', 'prize8', 
        'prize10', 'prize11', 'prize12', 'prize13', 'prize14', 'prize15', 'prize16', 'prize17', 'prize18');
        if (!isset($arrInput['award_type']) || !in_array($arrInput['award_type'],$needAwardType)
            || !isset($arrInput['can_get_awards']) || empty($arrInput['can_get_awards'])
            || !isset($arrInput['draw_times']) || intval($arrInput['draw_times']) <= 0
            || !isset($arrInput['period_date']) || intval($arrInput['period_date']) <= 0){
            Bingo_Log::warning("call common::drawAward fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $awardType = $arrInput['award_type'];
        $canGetAwards = $arrInput['can_get_awards'];
        $drawTimes = intval($arrInput['draw_times']);
        $periodDate = intval($arrInput['period_date']);
        $haveToDraw = isset($arrInput['have_to_draw']) && intval($arrInput['have_to_draw']) === 1 ? 1 : 0;
        $hasPeriod = isset($arrInput['has_period']) ? $arrInput['has_period'] : true;
        $randMustHit = isset($arrInput['rand_must_hit']) ? true : false ; //随机抽 必中
        $designatedPrizeType = isset($arrInput['designated_prize_type']) ? $arrInput['designated_prize_type']: 0 ; //指定命中奖品等级
        $arrOutput = array(
            'award_type' => $awardType,//奖品类型
            'award_id' => 0,//获得的奖id
            'have_to_draw'=> $haveToDraw//是否必中，请求必中&必中时期
        );
        //抽稀有卡逻辑
        if ($hasPeriod) {
            //获取当期开始时间
            $currentTime = time();
            $todayStartTime = strtotime(date('Y-m-d 21:00:00',$currentTime));
            $lastDayStartTime = strtotime(date('Y-m-d 21:00:00',$currentTime - 86400));
            $checkPeriodDate = $currentTime > $todayStartTime ? $todayStartTime : $lastDayStartTime;
            if ($periodDate != $checkPeriodDate){
                Bingo_Log::warning("call common::drawAward fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
        }
        if('card' == $awardType){
            //校验稀有卡类型
            $awardIds = array();
            foreach ($canGetAwards as $k => $award){
                if (!in_array($award,$cardAwards)){
                    Bingo_Log::warning("call common::drawAward fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
                    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
                }
                array_push($awardIds,$award);
            }
            $needAwardNum = count($awardIds);
            $drawTimes = $drawTimes > self::MAX_CARD_TIMES ? self::MAX_CARD_TIMES + 1 : $drawTimes;
            //step1 必中请求逻辑
            if (1 === $haveToDraw){
                if ($needAwardNum == 1){
                    $awardId = $awardIds[0];
                }else{
                    //step3 获取当前期的比例配置
                    $arrReq = array(
                        'key' => self::REDIS_COMMON_INFO,
                        'field' => 'rare_card_percent'
                    );
                    $arrRes = self::getAwardKey($arrReq);
                    if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                        Bingo_Log::warning('call self::getAwardKey failed! '.serialize($arrRes));
                    }else if (empty($arrRes['data'][self::REDIS_COMMON_INFO])){
                        Bingo_Log::warning('call self::getAwardKey failed! '.serialize($arrRes));
                    }else{
                        $rareCardPercent = $arrRes['data'][self::REDIS_COMMON_INFO];
                    }
                    $rareCardPercent = !empty($rareCardPercent) ? $rareCardPercent : '3,7';//默认3/7
                    $percentArr = explode(',',$rareCardPercent);
                    $card4Percent = intval($percentArr[0]);
                    $card5Percent = intval($percentArr[1]);
                    $randNum = rand(1,$card4Percent + $card5Percent);
                    if ($randNum <= $card4Percent){
                        $awardId = 'card4';
                    }else{
                        $awardId = 'card5';
                    }
                }
                $arrOutput['award_id'] = $awardId;//分配奖品
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrOutput);
            }
            //step2 必中时期逻辑
            //step2.1 获取必中时期总配置
            $arrRes = self::getAllAwardDateConf();
            if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                Bingo_Log::warning('call self::getAwardDateConf failed! '.serialize($arrRes));
                //return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }else if (empty($arrRes['data'])){
                Bingo_Log::warning('call self::getAwardDateConf empty! '.serialize($arrRes));
            }else{
                //step2.2 判断是否命中必中时期
                $key = self::REDIS_MUST_DATE_CONF;
                $totalDateConf = $arrRes['data'][$key];
                $dateConf = '';
                $startTime = 0;
                $endTime = 0;
                foreach ($totalDateConf as $conf){
                    $dateArr = explode('_',$conf['field']);
                    $startTime = intval($dateArr[0]);
                    $endTime = intval($dateArr[1]);
                    if ($startTime <= 0 || $endTime <= 0 || $currentTime > $endTime || $currentTime < $startTime){
                        continue;
                    }else{
                        $dateConf = $startTime.'_'.$endTime;
                        break;
                    }
                }

                //必中时期逻辑
                if (!empty($dateConf)){
                    $arrOutput['have_to_draw'] = 1;
                    //step2.3 随机发放奖品
                    if (count($canGetAwards) == 1){
                        $awardId = $canGetAwards[0];
                    }else{
                        $randNum = rand(1,2);
                        if ($randNum == 1){
                            $awardId = 'card4';
                        }else{
                            $awardId = 'card5';
                        }
                    }
                    //获取timeIndex
                    $timeIndex = floor(($currentTime - $startTime)/self::SLICE_AWARD_DATE_TIME);
                    //step2.4 发放奖品
                    $arrReq = array(
                        'award_id' => $awardId,
                        'start_time' => $startTime,
                        'end_time' => $endTime,
                        'time_index' => $timeIndex
                    );
                    $arrRes = self::incrAwardDateRecord($arrReq);
                    if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                        Bingo_Log::warning('call self::incrAwardDateRecord failed! [input]'.serialize($arrReq).'[output]'.serialize($arrRes));
                        return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                    }
                    $key = $awardId.'_'.$startTime.'_'.$endTime.'_'.self::REDIS_DATE_RECORD;
                    $awardNum = intval($arrRes['data'][$key]);

                    //step2.5 获取命中必中时期配置
                    $arrReq = array(
                        'start_time' => $startTime,
                        'end_time' => $endTime,
                        'time_index' => $timeIndex,
                        'award_id' => $awardId
                    );
                    $arrRes = self::getAwardDateConf($arrReq);
                    $key = $awardId.'_'.$startTime.'_'.$endTime.'_'.self::REDIS_DATE_CONF;
                    $maxAwardNum = intval($arrRes['data'][$key]);
                    if ($awardNum > $maxAwardNum){
                        //减去奖品发放，返回
                        $arrReq = array(
                            'award_id' => $awardId,
                            'start_time' => $startTime,
                            'end_time' => $endTime,
                            'time_index' => $timeIndex,
                            'step' => -1
                        );
                        $arrRes = self::incrAwardDateRecord($arrReq);
                        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                            Bingo_Log::warning('call self::incrAwardDateRecord failed! [input]'.serialize($arrReq).'[output]'.serialize($arrRes));
//                            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                        }
                    }else{
                        $arrOutput['award_id'] = $awardId;//分配奖品
                    }
                    return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrOutput);
                }
            }

            //step3 普通逻辑
            //step3.1 获取当前期的配置
            $arrReq = array(
                'need_times_conf' => $awardIds,
                'need_time_number_conf' => $awardIds
            );
            $arrRes = self::getAllAwardConf($arrReq);
            if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                Bingo_Log::warning('call self::getAllAwardConf failed! [input]'.serialize($arrReq).'[output]'.serialize($arrRes));
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }else if (empty($arrRes['data'])){
                Bingo_Log::warning('call self::getAllAwardConf empty! [input]'.serialize($arrReq).'[output]'.serialize($arrRes));
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            $outData = $arrRes['data'];
            $awardConf = array();
            foreach ($outData as $key => $value){
                if (strstr($key, self::REDIS_TIME_NUMBER_CONF)) {
                    //次数配置与时间配置，数组
                    foreach ($value as $k => $v){
                        $awardConf[$key][$v['field']] = json_decode($v['value'],true);
                    }
                }else{
                    foreach ($value as $k => $v){
                        $awardConf[$key][$v['field']] = $v['value'];
                    }
                }
            }

            $awardCommonInfo = $awardConf[self::REDIS_COMMON_INFO];
            $maxNumConf = $awardConf[self::REDIS_MAXNUM_CONF];

            //step3.2 判断是否需要调高比例
            $addPercent = intval($awardCommonInfo['add_percent']);
            $addPercentTimes = intval($awardCommonInfo['add_percent_times']);
            //获取当前时间段index
            $timeIndex = floor(($currentTime - $periodDate)/self::SLICE_AWARD_TIME);
            if ($timeIndex < self::MIN_TIME_INDEX || $timeIndex > self::MAX_TIME_INDEX){
                Bingo_Log::warning("call common::drawAward fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
//                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrOutput);
            }
            $timesAward = array();
            $totalPercent = 0;
            $percentArr = array();
            $maxIndexArr = array();
            foreach ($awardIds as $awardId){
                $timesAwardRet = array();
                //获取上一时间段的次数
                $arrReq = array(
                    'award_id' => $awardId,
                    'period_date' => $periodDate
                );
                $arrRes = self::getAwardRecord($arrReq);
                if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                    Bingo_Log::warning('call self::getAwardRecord failed! '.serialize($arrRes));
                    return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                }
                $key = $awardId.'_'.$periodDate.'_'.self::REDIS_AWARD_RECORD;
                $recordArr = $arrRes['data'][$key];
                if (!isset($recordArr) ||  empty($recordArr)){//无记录时取第一个的
                    $key = $awardId.'_'.self::REDIS_TIME_NUMBER_CONF;
                    $timeNumConf = $awardConf[$key][0];
                    $minNum = $timeNumConf['min_num'];
                    $maxNum = $timeNumConf['max_num'];
                    $timesAwardRet[0] = array(
                        'award_num' => 0,
                        'min_num' => $minNum,//时间段内对应的最大最小值
                        'max_num' => $maxNum
                    );
                    $maxRecordIndex = 0;
                }else{
                    foreach ($recordArr as $k => $v){
                        //获取获取该时间段的发奖次数
                        $key = $awardId.'_'.self::REDIS_TIME_NUMBER_CONF;
                        $timeNumConf = $awardConf[$key][$v['field']];
                        $minNum = $timeNumConf['min_num'];
                        $maxNum = $timeNumConf['max_num'];
                        $timesAwardRet[$v['field']] = array(
                            'award_num' => $v['value'],
                            'min_num' => $minNum,
                            'max_num' => $maxNum
                        );
                    }
                    ksort($timesAwardRet); //调整顺序、排序
                    end($timesAwardRet);
                    $maxRecordIndex = key($timesAwardRet);
                }
                $timesAward[$awardId] = $timesAwardRet;
                $maxRecord = $timesAwardRet[$maxRecordIndex];
                $key = $awardId.'_'.self::REDIS_TIMES_CONF;//每一次的概率
                //比例大于百分之1 ，抽奖次数 不大于 调高比例最高次数 前面的奖池未消耗完
                if (1 <= $addPercent && $drawTimes <= $addPercentTimes && $timeIndex > 0 && $maxRecordIndex < $timeIndex){
                    //满足调高条件
                    $awardConf[$key][$drawTimes - 1] += $addPercent;
                }
                $maxIndexArr[$awardId] = $maxRecordIndex < $timeIndex ? $maxRecordIndex : $timeIndex;
                $percentArr[$awardId] = $awardConf[$key][$drawTimes - 1]*10;
                $totalPercent += $percentArr[$awardId];
            }

            //step3.3 随机发卡
            $awardId = 0;
            $sumPercent = 0;
            $randNum = rand(1,1000);
            if ($randNum > $totalPercent){
                Bingo_Log::warning('rand not hit the award! '.serialize($arrInput));
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrOutput);
            }else{
                foreach ($percentArr as $k => $v){
                    $sumPercent += $v;
                    if ($randNum <= $sumPercent){
                        $awardId = $k;
                        break;
                    }
                }
            }
            if (!empty($awardId)){
                //step3.4 设置redis 更新记录
                $recordTimeIndex = $maxIndexArr[$awardId];
                $arrReq = array(
                    'award_id' => $awardId,
                    'period_date' => $periodDate,
                    'time_index' => $recordTimeIndex
                );
                $arrRes = self::incrAwardRecord($arrReq);
                if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                    Bingo_Log::warning('call self::incrAwardRecord failed! '.serialize($arrRes));
                    return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                }

                //step3.5 判断是否超总数 及 时间段总数
                $totalMaxNum = intval($maxNumConf[$awardId]);
                $totalAwardNum = $arrRes['data'][$periodDate.'_award_num'];
                $recordIndexMaxNum = $timesAward[$awardId][$recordTimeIndex]['max_num'];
                $recordIndexAwardNum = $arrRes['data'][$awardId.'_'.$periodDate.'_'.self::REDIS_AWARD_RECORD];
                $timeIndexMaxNum = $recordTimeIndex == $timeIndex ? $recordIndexMaxNum : 0;
                $timeIndexAwardNum = $recordTimeIndex == $timeIndex ? $recordIndexAwardNum : 0;
                if ($totalAwardNum > $totalMaxNum){
                    $arrReq = array(
                        'award_id' => $awardId,
                        'period_date' => $periodDate,
                        'time_index' => $recordTimeIndex,
                        'step' => -1
                    );
                    $arrRes = self::incrAwardRecord($arrReq);
                    if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                        Bingo_Log::warning('call self::incrAwardRecord failed! [input]'.serialize($arrReq).'[output]'.serialize($arrRes));
                    }
                    Bingo_Log::warning('the draw num has more than the total max num !'.$totalAwardNum.'_'.$totalMaxNum);
                }else if ($recordIndexAwardNum > $recordIndexMaxNum){
                    $arrReq = array(
                        'award_id' => $awardId,
                        'period_date' => $periodDate,
                        'time_index' => $recordTimeIndex,
                        'step' => -1
                    );
                    $arrRes = self::incrAwardRecord($arrReq);
                    if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                        Bingo_Log::warning('call self::incrAwardRecord failed! [input]'.serialize($arrReq).'[output]'.serialize($arrRes));
                    }
                    Bingo_Log::warning('the recordindex_drawnum has more than the recordindex_drawnum !'.$recordTimeIndex.'_'.$recordIndexAwardNum.'_'.$recordIndexMaxNum);
//                    var_dump($timesAward[$awardId]);
                    if ($recordTimeIndex < $timeIndex){
                        for ($i=$recordTimeIndex + 1;$i<=$timeIndex;$i++){
                            //获取获取该时间段的发奖次数
                            $key = $awardId.'_'.self::REDIS_TIME_NUMBER_CONF;
                            $currentTimeMaxNum = $awardConf[$key][$i]['max_num'];
                            if (0 < $currentTimeMaxNum){
                                $arrReq = array(
                                    'award_id' => $awardId,
                                    'period_date' => $periodDate,
                                    'time_index' => $i,
                                    'step' => 1
                                );
                                $arrRes = self::incrAwardRecord($arrReq);
                                if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                                    Bingo_Log::warning('call self::incrAwardRecord failed! [input]'.serialize($arrReq).'[output]'.serialize($arrRes));
                                }
                                $arrOutput['award_id'] = $awardId;
                                break;
                            }
                        }

                    }

                }else{
                    $arrOutput['award_id'] = $awardId;
                }

            }
        }

        //抽奖品逻辑
        if ('prize' == $awardType){
            //step1 校验奖品类型
            $prizeTypes = array();
            $awardIds = array();
            foreach ($canGetAwards as $k => $v){
                if (!in_array($k,$totalPrizeTypes) || empty($v)){
                    Bingo_Log::warning("call common::drawAward fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
                    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
                }
                array_push($prizeTypes,$k);
                foreach ($v as $award){
                    array_push($awardIds,$award);
                }
            }
            $needAwardTypeNum = count($prizeTypes);//奖品类型的数量
            $needAwardNum = count($awardIds);//奖品id的数量
            $drawTimes = $drawTimes > self::MAX_PRIZE_TIMES ? self::MAX_PRIZE_TIMES + 1 : $drawTimes;
            //step2.2 获取当前时间段 $timeIndex
            $timeIndex = floor(($currentTime - $periodDate)/self::SLICE_AWARD_TIME);
            if ($timeIndex < self::MIN_TIME_INDEX || $timeIndex > self::MAX_TIME_INDEX){
                Bingo_Log::warning("call common::drawAward fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."] ");
//                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrOutput);
            }
            $awardId = 0;
            //step1 获取当前期的配置
            $arrReq = array(
                'need_times_conf' => $prizeTypes,
                'need_time_number_conf' => $awardIds
            );
            $arrRes = self::getAllAwardConf($arrReq);
            if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                Bingo_Log::warning('call self::getAllAwardConf failed! [input]'.serialize($arrReq).'[output]'.serialize($arrRes));
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }else if (empty($arrRes['data'])){
                Bingo_Log::warning('call self::getAllAwardConf empty! [input]'.serialize($arrReq).'[output]'.serialize($arrRes));
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            $outData = $arrRes['data'];
            $awardConf = array();
            foreach ($outData as $key => $value){
                foreach ($value as $k => $v){
                    $awardConf[$key][$v['field']] = $v['value'];
                }
            }
            //step2 必中请求逻辑
            if (1 === $haveToDraw){
                if ($needAwardTypeNum > 1){
                    Bingo_Log::warning("call common::drawAward fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
                    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
                }

                if ($needAwardNum == 1){
                    $awardId = $awardIds[0];
                    $arrReq = array(
                        'award_id' => $awardId,
                        'time_index' => $timeIndex,
                        'award_conf' => $awardConf,
                        'period_date' => $periodDate
                    );
                    $arrRes = self::checkAwardNum($arrReq);
                    if ($arrRes === true){
                        $arrOutput['award_id'] = $awardId;
                    } else {
                        // 只能发印记了
                        Bingo_Log::fatal(__METHOD__ . " the S class has no award");
                    }
                } else {
                    $lNum = count($awardIds);
                    $leftAwardIds = $awardIds;
                    while($lNum > 0){
                        $randNum = rand(1,$lNum);
                        $awardId = $leftAwardIds[$randNum - 1];
                        $arrReq = array(
                            'award_id' => $awardId,
                            'time_index' => $timeIndex,
                            'award_conf' => $awardConf,
                            'period_date' => $periodDate
                        );
                        $arrRes = self::checkAwardNum($arrReq);
                        if ($arrRes === true){
                            $arrOutput['award_id'] = $awardId;
                            break;
                        }else{
                            array_splice($leftAwardIds, $randNum - 1, 1);
                            $lNum -= 1;
                        }
                    }
                    if ($arrRes === false) {
                        Bingo_Log::fatal(__METHOD__ . " the S class has no award");
                    }
                }
            }else{
                //step2 普通逻辑
                $totalPercent = 0;
                $percentArr = array();
                $hasMilliPercent = in_array('prize1',$prizeTypes) || in_array('prize6', $prizeTypes) ? 1 : 0;
                foreach ($prizeTypes as $prizeType){
                    //获得概率
                    $key = $prizeType.'_'.self::REDIS_TIMES_CONF;//每一次的概率
                    $randPercent = $hasMilliPercent === 0 ? $awardConf[$key][$drawTimes - 1] : $awardConf[$key][$drawTimes - 1]*10;
                    $percentArr[$prizeType] = $randPercent;
                    $totalPercent += $randPercent;
                }

                //step2.3 按照概率随机奖品等级
                $awardId = 0;
                $prizeType = '';
                $sumPercent = 0;
                $randNum = $hasMilliPercent === 1 ? rand(1,1000) : rand(1,100);
                if($randMustHit){ //随机抽必中
                    $randAwardTimes=1;
                    while($arrOutput['award_id'] == 0 && $randAwardTimes < self::MAX_PRIZE_HIT_TIMES){
                        Bingo_Log::warning('draw_times='.$randAwardTimes);
                        $randAwardTimes++;
                        $randNum = rand(1,$totalPercent); //随机抽必中
                        foreach ($percentArr as $k => $v){
                            $sumPercent += $v;
                            if ($randNum <= $sumPercent){
                                $prizeType = $k;
                                break;
                            }
                        }
                        if (!empty($prizeType)){
                            $hitAwardId = self::getRandAwardId($canGetAwards,$prizeType,$timeIndex,$awardConf,$periodDate);
                            $arrOutput['award_id'] = $hitAwardId ;
                        }
                    }
                    
                    if($arrOutput['award_id'] ==0 && $randAwardTimes >= self::MAX_PRIZE_HIT_TIMES ){ //N次未中 直接命中指定奖品等级
                        Bingo_Log::warning('has no hit prize,times='.$randAwardTimes.' designated_prize_type='.$designatedPrizeType);
                        $prizeType = $designatedPrizeType; 
                        $hitAwardId = self::getRandAwardId($canGetAwards,$prizeType,$timeIndex,$awardConf,$periodDate);
                        $arrOutput['award_id'] = $hitAwardId ;
                    } 

                }else{
                    if ($randNum > $totalPercent){
                        Bingo_Log::warning('rand not hit the award! '.serialize($arrInput));
                        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrOutput);
                    }else{
                        foreach ($percentArr as $k => $v){
                            $sumPercent += $v;
                            if ($randNum <= $sumPercent){
                                $prizeType = $k;
                                break;
                            }
                        }
                    }
                    if (!empty($prizeType)){
                        $awardIds = $canGetAwards[$prizeType];
                        $leftAwardNum = count($awardIds);
                        $leftAwardIds = $awardIds;
                        //step2.4 同等级别奖随机出具体的奖品
                        if ($leftAwardNum == 1){
                            $awardId = $awardIds[0];
                            $arrReq = array(
                                'award_id' => $awardId,
                                'time_index' => $timeIndex,
                                'award_conf' => $awardConf,
                                'period_date' => $periodDate
                            );
                            $arrRes = self::checkAwardNum($arrReq);
                            if ($arrRes === true){
                                $arrOutput['award_id'] = $awardId;
                            }
                        }else{
                            while($leftAwardNum > 0){
                                $randNum = rand(1,$leftAwardNum);
                                $awardId = $leftAwardIds[$randNum - 1];
                                $arrReq = array(
                                    'award_id' => $awardId,
                                    'time_index' => $timeIndex,
                                    'award_conf' => $awardConf,
                                    'period_date' => $periodDate
                                );
                                $arrRes = self::checkAwardNum($arrReq);
                                if ($arrRes === true){
                                    $arrOutput['award_id'] = $awardId;
                                    break;
                                }else{
                                    array_splice($leftAwardIds, $randNum - 1, 1);
                                    $leftAwardNum -= 1;
                                }
                            }
                        }
                    }
                }
            }
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrOutput);
    }
    
    private static function getRandAwardId($canGetAwards,$prizeType,$timeIndex,$awardConf,$periodDate){
        $hitAwardId = 0;
        $awardIds = $canGetAwards[$prizeType];
        $leftAwardNum = count($awardIds);
        $leftAwardIds = $awardIds;
        //step2.4 同等级别奖随机出具体的奖品
        if ($leftAwardNum == 1){
            $awardId = $awardIds[0];
            $arrReq = array(
                'award_id' => $awardId,
                'time_index' => $timeIndex,
                'award_conf' => $awardConf,
                'period_date' => $periodDate
            );
            $arrRes = self::checkAwardNum($arrReq);
            if ($arrRes === true){
                $hitAwardId = $awardId;
            }
        }else{
            while($leftAwardNum > 0){
                $randNum = rand(1,$leftAwardNum);
                $awardId = $leftAwardIds[$randNum - 1];
                $arrReq = array(
                    'award_id' => $awardId,
                    'time_index' => $timeIndex,
                    'award_conf' => $awardConf,
                    'period_date' => $periodDate
                );
                $arrRes = self::checkAwardNum($arrReq);
                if ($arrRes === true){
                    $hitAwardId = $awardId;
                    break;
                }else{
                    array_splice($leftAwardIds, $randNum - 1, 1);
                    $leftAwardNum -= 1;
                }
            }
        }

        return $hitAwardId ;
    } 

    /**
     * [checkAwardNum 检查award数量 from db]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    private static function checkAwardNum($arrInput){
        $recordTimeIndex = $arrInput['time_index'];
        $awardId = $arrInput['award_id'];
        $periodDate = $arrInput['period_date'];
        $awardConf = $arrInput['award_conf'];

        //step2.4 设置redis 更新记录
        $arrReq = array(
            'award_id' => $awardId,
            'period_date' => $periodDate,
            'time_index' => $recordTimeIndex
        );
        $arrRes = self::incrAwardRecord($arrReq);
        if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
            Bingo_Log::warning('call self::incrAwardRecord failed! '.serialize($arrRes));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        //step2.5 判断是否超总数 及 时间段总数
        $maxNumConf = $awardConf[self::REDIS_MAXNUM_CONF];
        $totalMaxNum = intval($maxNumConf[$awardId]);
        $totalAwardNum = intval($arrRes['data'][$periodDate.'_award_num']);
        $key = $awardId.'_'.self::REDIS_TIME_NUMBER_CONF;
        $timeIndexMaxNum = intval($awardConf[$key][$recordTimeIndex]);
        $timeIndexAwardNum = intval($arrRes['data'][$awardId.'_'.$periodDate.'_'.self::REDIS_AWARD_RECORD]);
        if ($totalAwardNum > $totalMaxNum || ($timeIndexAwardNum > $timeIndexMaxNum)){
            $arrReq = array(
                'award_id' => $awardId,
                'period_date' => $periodDate,
                'time_index' => $recordTimeIndex,
                'step' => -1
            );
            $arrRes = self::incrAwardRecord($arrReq);
            if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                Bingo_Log::warning('call self::incrAwardRecord failed! [input]'.serialize($arrReq).'[output]'.serialize($arrRes));
            }
            Bingo_Log::warning('checkAwardNum fail,'.$totalAwardNum.' totalMaxNum='.$totalMaxNum.' timeIndexAwardNum='.$timeIndexAwardNum.' timeIndexMaxNum='.$timeIndexMaxNum.' ipnput='.serialize($arrReq));
            return false;
        }
        return true;
    }
    //db操作

    /**
     * [getAwardConfs 获取award配置 from db]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function getAwardConfs(){
        $arrReq = array(
            'function'  => 'getAwardConfs'
        );
        Bingo_Timer::start('getAwardConfs');
        $arrOutput = Lib_Db::call('award_conf',$arrReq);
        Bingo_Timer::end('getAwardConfs');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning("exesql fail. input[".serialize($arrReq)."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        Bingo_Log::pushNotice('getAwardConfs_db_ret',"exesql success. input[".serialize($arrReq)."]output[".serialize($arrOutput)."]");
        $awardConfInfo = !empty($arrOutput['results'][0]) ? $arrOutput['results'][0] : array();
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$awardConfInfo);
    }

    /**
     * [getLastAwardConfFromDb 获取award配置 from db]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function getLastAwardConfFromDb(){
//        $arrReq = array(
//            'function'  => 'getLastAwardConf'
//        );
//        Bingo_Timer::start('getLastAwardConf');
//        $arrOutput = Lib_Db::call('award_conf',$arrReq);
//        Bingo_Timer::end('getLastAwardConf');
//        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
//            Bingo_Log::warning("exesql fail. input[".serialize($arrReq)."]");
//            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
//        }
//        Bingo_Log::pushNotice('getLastAwardConf_db_ret',"exesql success. input[".serialize($arrReq)."]output[".serialize($arrOutput)."]");
//        $awardConfInfo = !empty($arrOutput['results'][0][0]) ? $arrOutput['results'][0][0] : array();
//        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$awardConfInfo);

        Bingo_Timer::start('getLastAwardConf');
        $strSql = "select add_percent,add_percent_times,rare_card_percent,times_conf,time_number_conf,max_num_conf from award_conf order by id desc limit 1";
        $arrRet = self::_queryDB($strSql, 'DB_forum_friend');
        if ( $arrRet === false){
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        Bingo_Timer::end('getLastAwardConf');
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRet[0]);
    }

    /**
     * [addAwardConf 新增award配置]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function addAwardConf($arrInput){
        //参数判断
        $needParams = array('add_percent','add_percent_times','rare_card_percent','max_num_conf','times_conf','time_number_conf','op_uname');
        $noEmptyParams = array('rare_card_percent','max_num_conf','times_conf','time_number_conf','op_uname');
        foreach ($needParams as $k => $v){
            if (!array_key_exists($v,$arrInput)){
                Bingo_Log::warning('call common::addAwardConf fail !'. $v .' is not setted ERR_PARAM_ERROR. input['.serialize($arrInput).']');
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }else if (in_array($v,$noEmptyParams) && empty($arrInput[$v])){
                Bingo_Log::warning('call common::addAwardConf fail !'. $v .' is empty ERR_PARAM_ERROR. input['.serialize($arrInput).']');
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
        }
        $addPercent = $arrInput['add_percent'];
        $addPercentTimes = $arrInput['add_percent_times'];
        $rareCardPercent = $arrInput['rare_card_percent'];
        $maxNumConfArr = $arrInput['max_num_conf'];
        $timesConfArr = $arrInput['times_conf'];
        $timeNumberConfArr = $arrInput['time_number_conf'];
        $opUname = $arrInput['op_uname'];

        $maxNumConf = json_encode($maxNumConfArr);
        $timesConf = json_encode($timesConfArr);
        $timeNumberConf = json_encode($timeNumberConfArr);
        $arrReq = array(
            'function'  => 'addAwardConf',
            'add_percent' => $addPercent,
            'add_percent_times' => $addPercentTimes,
            'rare_card_percent' => $rareCardPercent,
            'times_conf' => $timesConf,
            'time_number_conf' => $timeNumberConf,
            'max_num_conf' => $maxNumConf,
            'op_uname' => $opUname,
            'create_time' => time()
        );
//        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrReq);
        Bingo_Timer::start('addAwardConf');
        $arrOutput = Lib_Db::call('award_conf',$arrReq);
        Bingo_Timer::start('addAwardConf');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning("exesql fail. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        //设置redis
        $arrRes = self::setAwardConf();
        Bingo_Log::warning('setAwardConf redis ret '.serialize($arrRes));
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * [getAwardDateConfFromDb 获取award 时间段配置 from db]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function getAwardDateConfFromDb(){
//        $arrReq = array(
//            'function'  => 'getAwardDateConf'
//        );
//        Bingo_Timer::start('getAwardDateConf');
//        $arrOutput = Lib_Db::call('award_date_conf',$arrReq);
//        Bingo_Timer::start('getAwardDateConf');
//        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
//            Bingo_Log::warning("exesql fail. input[".serialize($arrReq)."]");
//            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
//        }
//        $awardDateConfInfo = !empty($arrOutput['results'][0]) ? $arrOutput['results'][0] : array();
//        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$awardDateConfInfo);

        Bingo_Timer::start('getAwardDateConf');
        $strSql = "select * from award_date_conf order by id desc";
        $arrRet = self::_queryDB($strSql, 'DB_forum_friend');
        if ( $arrRet === false){
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        Bingo_Timer::end('getAwardDateConf');
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRet);

    }

    /**
     * [deleteAwardConf 删除award 数据库 策略配置]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function deleteAwardConf($arrInput){
        //参数判断
        $needParams = array('id');
        $noEmptyParams = array('id');
        foreach ($needParams as $k => $v){
            if (!array_key_exists($v,$arrInput)){
                Bingo_Log::warning('call common::deleteAwardConf fail !'. $v .' is not setted ERR_PARAM_ERROR. input['.serialize($arrInput).']');
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }else if (in_array($v,$noEmptyParams) && empty($arrInput[$v])){
                Bingo_Log::warning('call common::deleteAwardConf fail !'. $v .' is empty ERR_PARAM_ERROR. input['.serialize($arrInput).']');
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
        }
        $id = $arrInput['id'];
        //删除db 记录
        Bingo_Timer::start('deleteAwardConf');
        $arrReq = array(
            'function'  => 'deleteAwardConf',
            'id' => $id
        );
        $arrOutput = Lib_Db::call('award_conf',$arrReq);
        Bingo_Timer::start('deleteAwardConf');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning("exesql fail. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * [addAwardDateConf 获取award 必中时间段配置]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function addAwardDateConf($arrInput){
        //参数判断
        $needParams = array('start_time','end_time','max_num_conf','op_uname');
        $noEmptyParams = array('start_time','end_time','max_num_conf','op_uname');
        foreach ($needParams as $k => $v){
            if (!array_key_exists($v,$arrInput)){
                Bingo_Log::warning('call common::addAwardDateConf fail !'. $v .' is not setted ERR_PARAM_ERROR. input['.serialize($arrInput).']');
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }else if (in_array($v,$noEmptyParams) && empty($arrInput[$v])){
                Bingo_Log::warning('call common::addAwardDateConf fail !'. $v .' is empty ERR_PARAM_ERROR. input['.serialize($arrInput).']');
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
        }
        $startTime = $arrInput['start_time'];
        $endTime = $arrInput['end_time'];
        $maxNumConfArr = $arrInput['max_num_conf'];
        $maxNumConf = json_encode($arrInput['max_num_conf']);
        $opUname = $arrInput['op_uname'];
        $arrReq = array(
            'function'  => 'addAwardDateConf',
            'start_time' => $startTime,
            'end_time' => $endTime,
            'max_num_conf' => $maxNumConf,
            'op_uname' => $opUname,
            'create_time' => time(),
            'update_time' => time()
        );
        Bingo_Timer::start('addAwardDateConf');
        $arrOutput = Lib_Db::call('award_date_conf',$arrReq);
        Bingo_Timer::start('addAwardDateConf');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning("exesql fail. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        //设置redis
        $arrReq = array(
            'need_data' => 1,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'max_num_conf' => $maxNumConfArr,
        );
        $arrRes = self::setAwardDateConf($arrReq);
        Bingo_Log::warning('setAwardDateConf redis ret '.serialize($arrRes));
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes['data']);
    }

    /**
     * [updateAwardDateConf 更新award 必中时间段配置]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function updateAwardDateConf($arrInput){
        //参数判断
        $needParams = array('start_time','end_time','max_num_conf','op_uname','id');
        $noEmptyParams = array('start_time','end_time','max_num_conf','op_uname','id');
        foreach ($needParams as $k => $v){
            if (!array_key_exists($v,$arrInput)){
                Bingo_Log::warning('call common::updateAwardDateConf fail !'. $v .' is not setted ERR_PARAM_ERROR. input['.serialize($arrInput).']');
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }else if (in_array($v,$noEmptyParams) && empty($arrInput[$v])){
                Bingo_Log::warning('call common::updateAwardDateConf fail !'. $v .' is empty ERR_PARAM_ERROR. input['.serialize($arrInput).']');
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
        }
        $startTime = $arrInput['start_time'];
        $endTime = $arrInput['end_time'];
        $id = $arrInput['id'];
        $maxNumConfArr = $arrInput['max_num_conf'];
        $maxNumConf = json_encode($maxNumConfArr);
        $opUname = $arrInput['op_uname'];
        $arrReq = array(
            'function'  => 'updateAwardDateConf',
            'start_time' => $startTime,
            'end_time' => $endTime,
            'max_num_conf' => $maxNumConf,
            'id' => $id,
            'update_time' => time(),
            'op_uname' => $opUname
        );
        Bingo_Timer::start('updateAwardDateConf');
        $arrOutput = Lib_Db::call('award_date_conf',$arrReq);
        Bingo_Timer::start('updateAwardDateConf');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning("exesql fail. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        //设置redis
        $arrReq = array(
            'need_data' => 1,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'max_num_conf' => $maxNumConfArr,
        );
        $arrRes = self::setAwardDateConf($arrReq);
        Bingo_Log::warning('setAwardDateConf redis ret '.serialize($arrRes));
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes['data']);
    }

    /**
     * [deleteAwardDateConf 删除award 必中时间段配置]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function deleteAwardDateConf($arrInput){
        //参数判断
        $needParams = array('start_time','end_time','id');
        $noEmptyParams = array('start_time','end_time','id');
        foreach ($needParams as $k => $v){
            if (!array_key_exists($v,$arrInput)){
                Bingo_Log::warning('call common::deleteAwardDateConf fail !'. $v .' is not setted ERR_PARAM_ERROR. input['.serialize($arrInput).']');
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }else if (in_array($v,$noEmptyParams) && empty($arrInput[$v])){
                Bingo_Log::warning('call common::deleteAwardDateConf fail !'. $v .' is empty ERR_PARAM_ERROR. input['.serialize($arrInput).']');
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
        }
        $startTime = $arrInput['start_time'];
        $endTime = $arrInput['end_time'];
        $id = $arrInput['id'];

        //删除redis
        //删除总配置
        $key = self::REDIS_MUST_DATE_CONF;
        $field = $startTime.'_'.$endTime;
        $arrReq = array(
            'key' => $key,
            'field' => $field
        );
        $arrRes = self::hDelAwardKey($arrReq);
        if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
            Bingo_Log::warning("exesql fail. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        //删除该时间段配置
        foreach (self::$cardAwardIds as $awardId){
            $key = $awardId.'_'.$startTime.'_'.$endTime.'_'.self::REDIS_DATE_CONF;
            $arrReq = array(
                'key' => $key
            );
            $arrRes = self::hDelAwardKey($arrReq);
            if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
                Bingo_Log::warning("exesql fail. input[".serialize($arrInput)."]");
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
        }

        //删除db 记录
        Bingo_Timer::start('deleteAwardDateConf');
        $arrReq = array(
            'function'  => 'deleteAwardDateConf',
            'id' => $id
        );
        $arrOutput = Lib_Db::call('award_date_conf',$arrReq);
        Bingo_Timer::start('deleteAwardDateConf');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning("exesql fail. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        Bingo_Log::warning('setAwardDateConf redis ret '.serialize($arrRes));
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes['data']);
    }

    //redis操作
    //获取配置
    /**
     * [getAllAwardConf 获取award配置 from redis]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function getAllAwardConf($arrInput){
        $needTimesConf = $arrInput['need_times_conf'];
        $needTimeNumberConf = $arrInput['need_time_number_conf'];
        //获取配置
        $keyInfo = array(
            array(
                'key' => self::REDIS_COMMON_INFO,
            ),
            array(
                'key' => self::REDIS_MAXNUM_CONF,
            )
        );
        foreach ($needTimesConf as $k => $v){
            $key = $v.'_'.self::REDIS_TIMES_CONF;
            array_push($keyInfo,array(
                'key' => $key,
                'award_id' => $v
            ));
        }
        foreach ($needTimeNumberConf as $k => $v){
            $key = $v.'_'.self::REDIS_TIME_NUMBER_CONF;
            array_push($keyInfo,array(
                'key' => $key,
                'award_id' => $v
            ));
        }
        $valueInfo = array();

        foreach ($keyInfo as $k => $info){
            $key = $info['key'];
            $arrReq = array(
                'key'   => $key,
            );
            $arrRes = Lib_Redis::call('HGETALL', $arrReq,self::REDIS_COLLECTCARD);
            if (!$arrRes || $arrRes['err_no'] != 0) {
                Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'HGETALL');
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            $valueInfo[$key] = $arrRes['ret'][$key];
            //判断是否为空，为空则取db重新设置
            if (empty($valueInfo[$key])) {
                $arrReq = array(
                    'need_data' => 1
                );
                $type = $key;
                if (strstr($key, self::REDIS_TIMES_CONF)) {
                    $type = self::REDIS_TIMES_CONF;
                    $arrReq['type'] = $type;
                    $arrReq['award_type'] = $info['award_id'];
                } else if (strstr($key, self::REDIS_TIME_NUMBER_CONF)) {
                    $type = self::REDIS_TIME_NUMBER_CONF;
                    $arrReq['type'] = $type;
                    $arrReq['award_id'] = $info['award_id'];
                }
                $arrRes = self::setAwardConf($arrReq);
                if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                    Bingo_Log::warning($key.'is not exist in redis, set redis ret failed! '.serialize($arrRes));
                }else{
                    Bingo_Log::notice($key.'_set_redis_ret',serialize($arrRes));
                }
                $valueInfo[$key] = $arrRes['data'][$key];
            }
        }
//        $outData = array();
//        foreach ($valueInfo as $key => $value){
//            foreach ($value as $k => $v){
//                $outData[$key][$v['field']] = $v['value'];
//            }
//        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$valueInfo);
    }

    //设置配置
    /**
     * [setAwardConf 设置award redis配置]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function setAwardConf($arrInput){
        //指定key TODO LIST
        if (isset($arrInput['key']) && !empty($arrInput['key']) && isset($arrInput['value']) && !empty($arrInput['value'])){
            $key = $arrInput['key'];
            $value = $arrInput['value'];
            $fields = array();
            foreach ($value as $k => $v){
                array_push($fields,array(
                    'field' => $k,
                    'value' => $v
                ));
            }
            // 将数据存储到redis中
            $arrReq = array(
                'key'   => $key,
                'fields' => $fields
            );
            $arrRes = Lib_Redis::call('HMSET', $arrReq,self::REDIS_COLLECTCARD);
            if (!$arrRes || $arrRes['err_no'] != 0) {
                Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'HMSET');
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            // 设置过期时间
            $arrReq = array(
                'key'       => $key,
                'seconds'   => self::EXPIRE,
            );
            $arrRes = Lib_Redis::call('EXPIRE', $arrReq, self::REDIS_COLLECTCARD);
            if (!$arrRes || $arrRes['err_no'] != 0) {
                Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'EXPIRE');
            }
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $type = isset($arrInput['type']) && !empty($arrInput['type']) ? $arrInput['type'] : '';
        //获取配置
        $awardConfInfo = self::getLastAwardConfFromDb();
        if (!$awardConfInfo || $awardConfInfo['errno'] != 0) {
            Bingo_Log::warning('self::getLastAwardConfFromDb fail!'.serialize($awardConfInfo));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $awardConfInfo = $awardConfInfo['data'];
        //获取公共信息
        $addPercent = $awardConfInfo['add_percent'];
        $addPercentTimes = $awardConfInfo['add_percent_times'];
        $rareCardPercent = $awardConfInfo['rare_card_percent'];
        $maxNumConf = $awardConfInfo['max_num_conf'];
        $maxNumConfArr = json_decode($maxNumConf,true);
        $timesConf = $awardConfInfo['times_conf'];
        $timesConfArr = json_decode($timesConf,true);
        $timeNumberConf = $awardConfInfo['time_number_conf'];
        $timeNumberConfArr = json_decode($timeNumberConf,true);
        $setInfo = array();
        switch ($type){
            case self::REDIS_COMMON_INFO://公共信息
                //设置公共信息
                $setInfo[self::REDIS_COMMON_INFO] = array(
                    array(
                        'field' => 'add_percent',
                        'value' => $addPercent
                    ),
                    array(
                        'field' => 'add_percent_times',
                        'value' => $addPercentTimes
                    ),
                    array(
                        'field' => 'rare_card_percent',
                        'value' => $rareCardPercent
                    )
                );
                break;
            case self::REDIS_MAXNUM_CONF://最大值配置信息
                $fields = array();
                //设置当期最大值信息
                foreach ($maxNumConfArr as $k => $v){
                    array_push($fields,array(
                        'field' => $k,
                        'value' => $v
                    ));
                }
                $setInfo[self::REDIS_MAXNUM_CONF] = $fields;
                break;
            case self::REDIS_TIME_NUMBER_CONF:
                $awardId = isset($arrInput['award_id']) && !empty($arrInput['award_id']) ? $arrInput['award_id'] : '';
                //设置当期时间段上下限
                foreach ($timeNumberConfArr as $k => $v){
                    if (empty($awardId) || $k == $awardId){
                        $key = $k.'_'.self::REDIS_TIME_NUMBER_CONF;
                        $fields = array();
                        foreach ($v as  $field => $value){
                            array_push($fields,array(
                                'field' => $field,
//                                'value' => $value,
                                'value' => is_array($value) ? json_encode($value) : $value
                            ));
                        }
                        $setInfo[$key] = $fields;
                    }
                }
                break;
            case self::REDIS_TIMES_CONF:
                $awardType = isset($arrInput['award_type']) && !empty($arrInput['award_type']) ? $arrInput['award_type'] : '';
                //设置次数概率
                foreach ($timesConfArr as $k => $v){
                    if (empty($awardType) || $k == $awardType){
                        $key = $k.'_'.self::REDIS_TIMES_CONF;
                        $fields = array();
                        foreach ($v as  $field => $value){
                            array_push($fields,array(
                                'field' => $field,
//                                'value' => $value,
                                'value' => is_array($value) ? json_encode($value) : $value
                            ));
                        }
                        $setInfo[$key] = $fields;
                    }
                }
                break;
            default:
                //设置公共信息
                $setInfo[self::REDIS_COMMON_INFO] = array(
                    array(
                        'field' => 'add_percent',
                        'value' => $addPercent
                    ),
                    array(
                        'field' => 'add_percent_times',
                        'value' => $addPercentTimes
                    ),
                    array(
                        'field' => 'rare_card_percent',
                        'value' => $rareCardPercent
                    )
                );
                //设置当期最大值信息
                $fields = array();
                //设置当期最大值信息
                foreach ($maxNumConfArr as $k => $v){
                    array_push($fields,array(
                        'field' => $k,
                        'value' => $v
                    ));
                }
                $setInfo[self::REDIS_MAXNUM_CONF] = $fields;
                //设置当期时间段上下限
                foreach ($timeNumberConfArr as $k => $v){
                    $key = $k.'_'.self::REDIS_TIME_NUMBER_CONF;
                    $fields = array();
                    foreach ($v as  $field => $value){
                        array_push($fields,array(
                            'field' => $field,
//                          'value' => json_encode($value),
                            'value' => is_array($value) ? json_encode($value) : $value
                        ));
                    }
                    $setInfo[$key] = $fields;
                }
                //设置次数概率
                foreach ($timesConfArr as $k => $v){
                    $key = $k.'_'.self::REDIS_TIMES_CONF;
                    $fields = array();
                    foreach ($v as  $field => $value){
                        array_push($fields,array(
                            'field' => $field,
//                           'value' => json_encode($value),
                            'value' => is_array($value) ? json_encode($value) : $value
                        ));
                    }
                    $setInfo[$key] = $fields;
                }
        }

        foreach ($setInfo as $key => $value){
            // 将数据存储到redis中
            $arrReq = array(
                'key'   => $key,
                'fields' => $value
            );
            $arrRes = Lib_Redis::call('HMSET', $arrReq, self::REDIS_COLLECTCARD);
            if (!$arrRes || $arrRes['err_no'] != 0) {
                Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'HMSET');
//                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                continue;
            }
            // 设置过期时间
            $arrReq = array(
                'key'       => $key,
                'seconds'   => self::EXPIRE,
            );
            $arrRes = Lib_Redis::call('EXPIRE', $arrReq, self::REDIS_COLLECTCARD);
            if (!$arrRes || $arrRes['err_no'] != 0) {
                Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'EXPIRE');
            }
        }
        $needData = array();
        if (isset($arrInput['need_data']) && 1 === intval($arrInput['need_data'])){
            $needData = $setInfo;
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$needData);
    }

    //删除配置
    /**
     * [delAwardConf 设置award redis配置]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function delAwardConf(){
        //删除redis
        $needTimesConf = array('card4','card5','prize1','prize2','prize3','prize4');
        $needTimeNumberConf = array('card4','card5',1,2,3,4,5,6,7,8,9);
        //获取配置
        $keyInfo = array(
            array(
                'key' => self::REDIS_COMMON_INFO,
            ),
            array(
                'key' => self::REDIS_MAXNUM_CONF,
            )
        );
        foreach ($needTimesConf as $k => $v){
            $key = $v.'_'.self::REDIS_TIMES_CONF;
            array_push($keyInfo,array(
                'key' => $key
            ));
        }
        foreach ($needTimeNumberConf as $k => $v){
            $key = $v.'_'.self::REDIS_TIME_NUMBER_CONF;
            array_push($keyInfo,array(
                'key' => $key
            ));
        }

        //删除策略配置
        foreach ($keyInfo as $k => $info){
            $arrReq = $info;
            $arrRes = self::hDelAwardKey($arrReq);
            if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
                Bingo_Log::warning("exesql fail. input[".serialize($arrReq)."]");
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
        }
    }

    /**
     * [getAwardRecord 获取award记录]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function getAwardRecord($arrInput){
        $awardId = isset($arrInput['award_id']) && !empty($arrInput['award_id']) ? $arrInput['award_id'] : '';
        $periodDate = isset($arrInput['period_date']) && intval($arrInput['period_date']) > 0 ? $arrInput['period_date'] : 0;
        $minTimeIndex = 0;
        $maxTimeIndex = 47;
        if (!$awardId || !$periodDate){
            Bingo_Log::warning("call getAwardRecord fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
//        else if (!isset($arrInput['time_index']) || intval($arrInput['time_index']) < $minTimeIndex || intval($arrInput['time_index']) > $maxTimeIndex){
//            Bingo_Log::warning("call getAwardRecord fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
//            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
//        }
        $key = $awardId.'_'.$periodDate.'_'.self::REDIS_AWARD_RECORD;
        $arrReq = array(
            'key'   => $key
        );
        if (isset($arrInput['time_index'])){
            $timeIndex =  intval($arrInput['time_index']);
            $arrReq['field'] = $timeIndex;
            $arrRes = Lib_Redis::call('HGET', $arrReq,self::REDIS_COLLECTCARD);
        }else{
            $arrRes = Lib_Redis::call('HGETALL', $arrReq,self::REDIS_COLLECTCARD);
        }

        if (!$arrRes || $arrRes['err_no'] != 0) {
            Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'HGETALL');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $valueInfo[$key] = $arrRes['ret'][$key];
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$valueInfo);
    }

    /**
     * [getAllAwardRecord 获取award记录]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function getAllAwardRecord($arrInput){
        $awardId = isset($arrInput['award_id']) && !empty($arrInput['award_id']) ? $arrInput['award_id'] : '';
        $periodDate = isset($arrInput['period_date']) && intval($arrInput['period_date']) > 0 ? $arrInput['period_date'] : 0;

        if (!$periodDate){
            Bingo_Log::warning("call getAwardRecord fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $totalNumArr = array();
        $arrRet = array();
        $awardIdRecord = array();

        //总数
        $totalNumKey = $periodDate.'_award_num';
        $arrReq = array(
            'key'   => $totalNumKey
        );
        $arrRes = Lib_Redis::call('HGETALL', $arrReq,self::REDIS_COLLECTCARD);
        if (!$arrRes || $arrRes['err_no'] != 0) {
            Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'HGETALL');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        //处理数据格式
        foreach ($arrRes['ret'][$totalNumKey] as $k => $v){
            $totalNumArr[$v['field']] = intval($v['value']);
        }

        $awardIds = self::$awardIds;
        if (!empty($awardId)){
            $awardIds = array($awardId);
        }

        foreach ($awardIds as $awardId){
            //获取记录
            $key = $awardId.'_'.$periodDate.'_'.self::REDIS_AWARD_RECORD;
            $arrReq = array(
                'key'   => $key
            );
            $arrRes = Lib_Redis::call('HGETALL', $arrReq,self::REDIS_COLLECTCARD);
            if (!$arrRes || $arrRes['err_no'] != 0) {
                Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'HGETALL');
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            $record = array();
            foreach ($arrRes['ret'][$key] as $k => $v){
                $record[$v['field']] = intval($v['value']);
            }
            $arrRet[$awardId]['records'] = $record;
            $arrRet[$awardId]['total_num'] = isset($totalNumArr[$awardId]) ? $totalNumArr[$awardId] : 0;
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRet);
    }

    /**
     * [delAllAwardRecord 删除award记录]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function delAllAwardRecord($arrInput){
        $awardId = isset($arrInput['award_id']) && !empty($arrInput['award_id']) ? $arrInput['award_id'] : '';
        $periodDate = isset($arrInput['period_date']) && intval($arrInput['period_date']) > 0 ? $arrInput['period_date'] : 0;

        if (!$periodDate){
            Bingo_Log::warning("call getAwardRecord fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //总数
        $totalNumKey = $periodDate.'_award_num';
        $arrReq = array(
            'key'   => $totalNumKey
        );

        $arrRes = self::hDelAwardKey($arrReq);
        if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
            Bingo_Log::warning("exesql fail. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL,$arrReq);
        }

        $awardIds = self::$awardIds;
        if (!empty($awardId)){
            $awardIds = array($awardId);
        }

        foreach ($awardIds as $awardId){
            //获取记录
            $key = $awardId.'_'.$periodDate.'_'.self::REDIS_AWARD_RECORD;
            $arrReq = array(
                'key'   => $key
            );

            $arrRes = self::hDelAwardKey($arrReq);
            if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
                Bingo_Log::warning("exesql fail. input[".serialize($arrInput)."]");
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL,$arrReq);
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * [getAllAwardDateRecord 获取award记录]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function getAllAwardDateRecord($arrInput){
        $awardId = isset($arrInput['award_id']) && !empty($arrInput['award_id']) ? $arrInput['award_id'] : '';
        $startTime = isset($arrInput['start_time']) && intval($arrInput['start_time']) > 0 ? $arrInput['start_time'] : 0;
        $endTime = isset($arrInput['end_time']) && intval($arrInput['end_time']) > 0 ? $arrInput['end_time'] : 0;

        $timeConfs = array();
        $arrRet = array();
        $totalNumArr = array();

        if ($startTime && $endTime){
            $timeConfs = array($startTime.'_'.$endTime);
        }else{
            //获取配置
            $awardDateConfInfo = self::getAwardDateConfFromDb();
            if (!$awardDateConfInfo || $awardDateConfInfo['errno'] != 0) {
                Bingo_Log::warning('self::getAwardDateConfFromDb fail!'.serialize($awardDateConfInfo));
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            $awardDateConfInfo = $awardDateConfInfo['data'];
            //获取必中时期list && 各个list的最大值
            foreach ($awardDateConfInfo as $info){
                $startTime = $info['start_time'];
                $endTime = $info['end_time'];
                array_push($timeConfs,$startTime.'_'.$endTime);
            }
        }
        $awardIds = self::$cardAwardIds;
        if (!empty($awardId)){
            $awardIds = array($awardId);
        }

        foreach ($timeConfs as $timeConf){
            $records = array();
            $totalNum = array();
            foreach ($awardIds as $awardId){
                $totalNumArr[$timeConf][$awardId] = 0;
                $key = $awardId.'_'.$timeConf.'_'.self::REDIS_DATE_RECORD;
                $arrReq = array(
                    'key'   => $key
                );
                $arrRes = Lib_Redis::call('HGETALL', $arrReq,self::REDIS_COLLECTCARD);
                if (!$arrRes || $arrRes['err_no'] != 0) {
                    Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'HGETALL');
                    return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                }

                //处理为时间段格式
                foreach ($arrRes['ret'][$key] as $k => $v){
                    $records[$awardId][$v['field']] = intval($v['value']);
                    $totalNum[$awardId] += intval($v['value']);
                }
            }

            $arrRet[$timeConf] = array(
                'records' => $records,
                'total_num' => $totalNum
            );
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRet);
    }

    /**
     * [getAwardTotalNum 获取award总量]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function getAwardTotalNum($arrInput){
        $periodDate = isset($arrInput['period_date']) && intval($arrInput['period_date']) > 0 ? $arrInput['period_date'] : 0;
        if (!$periodDate){
            Bingo_Log::warning("call getAwardTotalNum fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $key = $periodDate.'_award_num';
        $arrReq = array(
            'key'   => $key
        );
        if (isset($arrInput['award_id'])){
            $awardId =  $arrInput['award_id'];
            $arrReq['field'] = $awardId;
            $arrRes = Lib_Redis::call('HGET', $arrReq,self::REDIS_COLLECTCARD);
        }else{
            $arrRes = Lib_Redis::call('HGETALL', $arrReq,self::REDIS_COLLECTCARD);
        }

        if (!$arrRes || $arrRes['err_no'] != 0) {
            Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'HGETALL');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $valueInfo[$key] = $arrRes['ret'][$key];
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$valueInfo);
    }

    /**
     * 自增自减award记录
     * @param $arrInput
     * @return array
     */
    public static function incrAwardRecord($arrInput){
        $awardId = isset($arrInput['award_id']) && !empty($arrInput['award_id']) ? $arrInput['award_id'] : '';
        $periodDate = isset($arrInput['period_date']) && intval($arrInput['period_date']) > 0 ? $arrInput['period_date'] : 0;
        $minTimeIndex = 0;
        $maxTimeIndex = 47;
        if (!$awardId || !$periodDate){
            Bingo_Log::warning("call incrAwardRecord fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }else if (!isset($arrInput['time_index']) || intval($arrInput['time_index']) < $minTimeIndex || intval($arrInput['time_index']) > $maxTimeIndex){
            Bingo_Log::warning("call incrAwardRecord fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $timeIndex = intval($arrInput['time_index']);
        $key = $awardId.'_'.$periodDate.'_'.self::REDIS_AWARD_RECORD;
        $maxNumKey = $periodDate.'_award_num';
        $field = $awardId;
        $step = isset($arrInput['step']) ? intval($arrInput['step']) : 1;
        $arrOut = array();
        //总的加1或减1
        $arrRes = self::hincrby($maxNumKey,$field,$step);
        if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
            Bingo_Log::warning("call incrAwardRecord::hincrby fail. input[".serialize($arrInput)."][output".serialize($arrRes));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrOut[$maxNumKey] = $arrRes['data'][$maxNumKey];
        //单个时间段加1或减1
        $arrRes = self::hincrby($key,$timeIndex,$step);
        if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
            Bingo_Log::warning("call incrAwardRecord::hincrby fail. input[".serialize($arrInput)."][output".serialize($arrRes));
            //反向操作
            $step = -$step;
            $arrRes = self::hincrby($maxNumKey,$field,$step);
            if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                Bingo_Log::warning("call incrAwardRecord::hincrby ret. input[$maxNumKey.'_'.$field.'_'.$step][output".serialize($arrRes));
            }
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrOut[$key] = $arrRes['data'][$key];
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrOut);
    }

    //获取配置
    /**
     * [getAllAwardDateConf 获取必中时间段award date配置]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function getAllAwardDateConf(){
        $key = self::REDIS_MUST_DATE_CONF;
        $arrReq = array(
            'key'   => $key,
        );
        $arrRes = Lib_Redis::call('HGETALL', $arrReq,self::REDIS_COLLECTCARD);
        if (!$arrRes || $arrRes['err_no'] != 0) {
            Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'HGETALL');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $valueInfo[$key] = $arrRes['ret'][$key];
        //todo list 判断是否为空，为空则取db重新设置
        if (empty($valueInfo[$key])){
            //设置必须时间配置
            $arrReq = array(
                'need_data' => 1
            );
            $arrRes = self::setAwardDateConf($arrReq);
            if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                Bingo_Log::warning('setAwardDateConf failed'.serialize($arrRes));
            }else{
                Bingo_Log::pushNotice('setAwardDateConf_ret',serialize($arrRes));
            }
            $valueInfo[$key] = $arrRes['data'][$key];
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$valueInfo);
    }

    //获取配置
    /**
     * [getAwardDateConf 获取必中时间段award date配置]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function getAwardDateConf($arrInput){
        $startTime = isset($arrInput['start_time']) && intval($arrInput['start_time']) > 0 ? intval($arrInput['start_time']) : 0;
        $endTime = isset($arrInput['end_time']) && intval($arrInput['end_time']) > 0 ? intval($arrInput['end_time']) : 0;
        $awardId = isset($arrInput['award_id']) && !empty($arrInput['award_id']) ? $arrInput['award_id'] : '';
        if ($startTime <= 0 || $endTime <= 0 || !isset($arrInput['time_index'])){
            Bingo_Log::warning("call getAwardDateConf fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $totalField = $startTime.'_'.$endTime;
        $key = $awardId.'_'.$totalField.'_'.self::REDIS_DATE_CONF;
        $timeIndex = intval($arrInput['time_index']);
        $arrReq = array(
            'key'   => $key,
            'field' => $timeIndex
        );
        $arrRes = Lib_Redis::call('HGET', $arrReq,self::REDIS_COLLECTCARD);
        if (!$arrRes || $arrRes['err_no'] != 0) {
            Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'HGETALL');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $valueInfo[$key] = $arrRes['ret'][$key];
        //todo list 判断是否为空，为空则取db重新设置
        if (empty($valueInfo[$key])){
            //设置必须时间配置
            $arrReq = array(
                'need_data' => 1
            );
            $arrRes = self::setAwardDateConf($arrReq);
            if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                Bingo_Log::warning('setAwardDateConf failed'.serialize($arrRes));
            }else{
                Bingo_Log::pushNotice('setAwardDateConf_ret',serialize($arrRes));
            }
            $valueInfo[$key] = $arrRes['data'][$key];
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$valueInfo);
    }

    /**
     * [setAwardDateConf 设置award redis配置]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function setAwardDateConf($arrInput){
        $setInfo = array();
        $totalFields = array();
        $totalKey = self::REDIS_MUST_DATE_CONF;

        //指定时间段 TODO LIST
        if(isset($arrInput['start_time']) && isset($arrInput['end_time'])){
            if (intval($arrInput['start_time']) <= 0 || intval($arrInput['end_time']) <= 0 || intval($arrInput['start_time']) >= intval($arrInput['end_time']) || !isset($arrInput['max_num_conf']) || empty($arrInput['max_num_conf'])){
                Bingo_Log::warning("call common::setAwardDateConf fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $startTime = $arrInput['start_time'];
            $endTime = $arrInput['end_time'];
            $maxNumConfArr = $arrInput['max_num_conf'];
            $totalField = $startTime.'_'.$endTime;
            //设置总时间段
            $setInfo[$totalKey] = array(
                'field' => $totalField,
                'value' => 1
            );
            $dateKey = $totalField.'_'.self::REDIS_DATE_CONF;
            //设置当期最大值信息
            foreach ($maxNumConfArr as $k => $v){
                $fields = array();
                //切割为10分钟
                $key = $k.'_'.$dateKey;
                $num = ceil(($endTime - $startTime)/self::SLICE_AWARD_DATE_TIME);
                $maxNum = ceil($v/$num);
                for ($i=0;$i<$num;$i++){
                    $maxNum = $v > $maxNum*($i + 1) ? $maxNum : $v - $maxNum*$i;
                    if (intval($maxNum) <= 0){
                        break;
                    }
                    array_push($fields,array(
                        'field' => $i,
                        'value' => $maxNum
                    ));
                }
                $setInfo[$key] = $fields;
            }
            //设置单个时间段
            foreach ($setInfo as $key => $value){
                if ($key == $totalKey){
                    // 将数据存储到redis中
                    $arrReq = array(
                        'key' => $key,
                        'field'   => $value['field'],
                        'value' => $value['value']
                    );
                    $arrRes = Lib_Redis::call('HSET', $arrReq, self::REDIS_COLLECTCARD);
                    if (!$arrRes || $arrRes['err_no'] != 0) {
                        Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'HSET');
//                      return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                        continue;
                    }
                }else{
                    // 将数据存储到redis中
                    $arrReq = array(
                        'key'   => $key,
                        'fields' => $value
                    );
                    $arrRes = Lib_Redis::call('HMSET', $arrReq, self::REDIS_COLLECTCARD);
                    if (!$arrRes || $arrRes['err_no'] != 0) {
                        Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'HMSET');
//                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                        continue;
                    }
                }

                // 设置过期时间
                $arrReq = array(
                    'key'       => $key,
                    'seconds'   => self::EXPIRE,
                );
                $arrRes = Lib_Redis::call('EXPIRE', $arrReq, self::REDIS_COLLECTCARD);
                if (!$arrRes || $arrRes['err_no'] != 0) {
                    Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'EXPIRE');
                }
            }

            $needData = array();
            if (isset($arrInput['need_data']) && 1 === intval($arrInput['need_data'])){
                $needData = $setInfo;
            }
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$needData);
        }

        //获取配置
        $awardDateConfInfo = self::getAwardDateConfFromDb();
        if (!$awardDateConfInfo || $awardDateConfInfo['errno'] != 0) {
            Bingo_Log::warning('self::getAwardDateConfFromDb fail!'.serialize($awardDateConfInfo));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $awardDateConfInfo = $awardDateConfInfo['data'];
        //设置 必中时期list && 各个list的最大值
        foreach ($awardDateConfInfo as $info){
            $startTime = $info['start_time'];
            $endTime = $info['end_time'];
            $totalField = $startTime.'_'.$endTime;
            array_push($totalFields, array(
                    'field' => $totalField,
                    'value' => 1
            ));
            $maxNumConf = $info['max_num_conf'];
            $maxNumConfArr = json_decode($maxNumConf,true);
            $dateKey = $totalField.'_'.self::REDIS_DATE_CONF;
            //设置当期最大值信息
            foreach ($maxNumConfArr as $k => $v){
                $fields = array();
                //切割为1分钟
                $key = $k.'_'.$dateKey;
                $num = ceil(($endTime - $startTime)/self::SLICE_AWARD_DATE_TIME);
                $maxNum = ceil($v/$num);
                for ($i=0;$i<$num;$i++){
                    $maxNum = $v > $maxNum*($i + 1) ? $maxNum : $v - $maxNum*$i;
                    if (intval($maxNum) <= 0){
                        break;
                    }
                    array_push($fields,array(
                        'field' => $i,
                        'value' => intval($maxNum)

                    ));
                }
                $setInfo[$key] = $fields;
            }
        }
        $setInfo[$totalKey] = $totalFields;

        foreach ($setInfo as $key => $value){
            // 将数据存储到redis中
            $arrReq = array(
                'key'   => $key,
                'fields' => $value
            );
            $arrRes = Lib_Redis::call('HMSET', $arrReq, self::REDIS_COLLECTCARD);
            if (!$arrRes || $arrRes['err_no'] != 0) {
                Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'HMSET');
//                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                continue;
            }
            // 设置过期时间
            $arrReq = array(
                'key'       => $key,
                'seconds'   => self::EXPIRE,
            );
            $arrRes = Lib_Redis::call('EXPIRE', $arrReq, self::REDIS_COLLECTCARD);
            if (!$arrRes || $arrRes['err_no'] != 0) {
                Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'EXPIRE');
            }
        }
        $needData = array();
        if (isset($arrInput['need_data']) && 1 === intval($arrInput['need_data'])){
            $needData = $setInfo;
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$needData);
    }

    /**
     * [getAwardDateRecord 获取award date记录]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function getAwardDateRecord($arrInput){
        $startTime = isset($arrInput['start_time']) && !empty($arrInput['start_time']) ? $arrInput['start_time'] : 0;
        $endTime = isset($arrInput['end_time']) && !empty($arrInput['end_time']) ? $arrInput['end_time'] : 0;
        $awardId = isset($arrInput['award_id']) && !empty($arrInput['award_id']) ? $arrInput['award_id'] : '';
        $minTimeIndex = 0;
        $maxTimeIndex = ceil(($endTime - $startTime)/self::SLICE_AWARD_DATE_TIME);
        if ($startTime <= 0 || $endTime <= 0){
            Bingo_Log::warning("call getAwardDateRecord fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }else if (isset($arrInput['time_index']) && (intval($arrInput['time_index']) < $minTimeIndex || intval($arrInput['time_index']) > $maxTimeIndex)){
            Bingo_Log::warning("call getAwardDateRecord fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $key = $awardId.'_'.$startTime.'_'.$endTime.'_'.self::REDIS_DATE_RECORD;
        $arrReq = array(
            'key'   => $key
        );
        if (isset($arrInput['time_index'])){
            $timeIndex =  intval($arrInput['time_index']);
            $arrReq['field'] = $timeIndex;
            $arrRes = Lib_Redis::call('HGET', $arrReq,self::REDIS_COLLECTCARD);
        }else{
            $arrRes = Lib_Redis::call('HGETALL', $arrReq,self::REDIS_COLLECTCARD);
        }

        if (!$arrRes || $arrRes['err_no'] != 0) {
            Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'HGETALL');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $valueInfo[$key] = intval($arrRes['ret'][$key]);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$valueInfo);
    }

    /**
     * [incrAwardDateRecord 自增自减必得时期award记录]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function incrAwardDateRecord($arrInput){
        $startTime = isset($arrInput['start_time']) && !empty($arrInput['start_time']) ? $arrInput['start_time'] : 0;
        $endTime = isset($arrInput['end_time']) && !empty($arrInput['end_time']) ? $arrInput['end_time'] : 0;
        $awardId = isset($arrInput['award_id']) && !empty($arrInput['award_id']) ? $arrInput['award_id'] : '';
        $minTimeIndex = 0;
        $maxTimeIndex = ceil(($endTime - $startTime)/self::SLICE_AWARD_DATE_TIME);
        if ($startTime <= 0 || $endTime <= 0){
            Bingo_Log::warning("call incrAwardRecord fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }else if (!isset($arrInput['time_index']) || intval($arrInput['time_index']) < $minTimeIndex || intval($arrInput['time_index']) > $maxTimeIndex){
            Bingo_Log::warning("call incrAwardRecord fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $timeIndex = intval($arrInput['time_index']);
        $key = $awardId.'_'.$startTime.'_'.$endTime.'_'.self::REDIS_DATE_RECORD;
        $step = isset($arrInput['step']) ? intval($arrInput['step']) : 1;
        $arrRes = self::hincrby($key,$timeIndex,$step);
        return $arrRes;
    }

    /**
     * [delAllAwardDateRecord 删除award记录]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function delAllAwardDateRecord($arrInput){
        $awardId = isset($arrInput['award_id']) && !empty($arrInput['award_id']) ? $arrInput['award_id'] : '';
        $startTime = isset($arrInput['start_time']) && intval($arrInput['start_time']) > 0 ? $arrInput['start_time'] : 0;
        $endTime = isset($arrInput['end_time']) && intval($arrInput['end_time']) > 0 ? $arrInput['end_time'] : 0;
        $isAll = isset($arrInput['is_all']) && intval($arrInput['is_all']) === 1 ? 1 : 0;

        $timeConfs = array();

        if ($startTime && $endTime){
            $timeConfs = array($startTime.'_'.$endTime);
        }else if ($isAll){
            //获取配置
            $awardDateConfInfo = self::getAllAwardDateConf();
            if (!$awardDateConfInfo || $awardDateConfInfo['errno'] != 0) {
                Bingo_Log::warning('self::getAwardDateConfFromDb fail!'.serialize($awardDateConfInfo));
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            $awardDateConfInfo = $awardDateConfInfo['data'][self::REDIS_MUST_DATE_CONF];
            //获取必中时期list && 各个list的最大值
            foreach ($awardDateConfInfo as $info){
                array_push($timeConfs,$info['field']);
            }
        }
        $awardIds = self::$cardAwardIds;
        if (!empty($awardId)){
            $awardIds = array($awardId);
        }
        foreach ($timeConfs as $timeConf){
            foreach ($awardIds as $awardId){
                $key = $awardId.'_'.$timeConf.'_'.self::REDIS_DATE_RECORD;
                $arrReq = array(
                    'key'   => $key
                );
                $arrRes = self::hDelAwardKey($arrReq);
                if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
                    Bingo_Log::warning("exesql fail. input[".serialize($arrReq)."]");
                    return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                }
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * [hincrby]
     * @param
     * @return
     */
    public static function getAwardKey($arrInput) {
        $strKey = isset($arrInput['key']) && !empty($arrInput['key']) ? $arrInput['key'] : '';
        $field = isset($arrInput['field']) && !empty($arrInput['field']) ? $arrInput['field'] : '';
        if (empty($strKey)) {
            Bingo_Log::warning("the key is invalid, key = $strKey, field = $field");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrReq = array(
            'key'   => $strKey
        );
        if (!empty($field)){
            $arrReq['field'] = $field;
            $arrRes = Lib_Redis::call('HGET', $arrReq, self::REDIS_COLLECTCARD);
        }else{
            $arrRes = Lib_Redis::call('HGETALL', $arrReq, self::REDIS_COLLECTCARD);
        }
        if (!$arrRes || $arrRes['err_no'] != 0) {
            Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'HINCRBY');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes['ret']);
    }

    /**
     * [hincrby]
     * @param
     * @return
     */
    public static function hincrby($strKey, $field, $intStep) {
        if (empty($strKey) || (empty($field) && $field !== 0) || !is_numeric($intStep)) {
            Bingo_Log::warning("the key, field ,step is invalid, key = $strKey, field = $field, step = $intStep ");
            return false;
        }
        $arrReq = array(
            'key'   => $strKey,
            'field' => $field,
            'step'  => $intStep,
        );
        $arrRes = Lib_Redis::call('HINCRBY', $arrReq, self::REDIS_COLLECTCARD);
        if (!$arrRes || $arrRes['err_no'] != 0) {
            Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'HINCRBY');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes['ret']);
    }

    //清除redis
    /**
     * [deleteKey]
     * @param
     * @return
     */
    public static function hDelAwardKey($arrInput) {
        $strKey = isset($arrInput['key']) && !empty($arrInput['key']) ? $arrInput['key'] : '';
        $field = isset($arrInput['field']) && !empty($arrInput['field']) ? $arrInput['field'] : '';
        if (empty($strKey)) {
            Bingo_Log::warning("the key is invalid, key = $strKey, field = $field");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrReq = array(
            'key'   => $strKey,
        );

        if (!empty($field)){
            $arrReq['field'] =$field;
            $arrRes = Lib_Redis::call('HDEL', $arrReq, self::REDIS_COLLECTCARD);
        } else {
            $arrRes = Lib_Redis::call('DEL', $arrReq, self::REDIS_COLLECTCARD);
        }

        if (!$arrRes || $arrRes['err_no'] != 0) {
            Lib_Log::fatal(Lib_Log::LOG_REDIS_CALL, $arrReq, $arrRes, 'HDEL');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * [getTimeInfo]
     * @param
     * @return
     */
    public static function getTimeInfo(){
        $currentTime = time();
        $year = date('Y',$currentTime);
        $month = date('m',$currentTime);
        $day = date('d',$currentTime);
        //获取期的日期
        $periodDate = strtotime($year.'-'.$month.'-'.$day.' 21:00:00');
        if ($currentTime >= $periodDate){
            $periodDate += 86400;
        }
        //获取时间段的区域 0～47
        $sliceTime =30*60;//30分钟
        $timeIndex = floor(($currentTime - $periodDate)/$sliceTime);
        $arrOutput = array(
            'period_date' => $periodDate,
            'time_index' => $timeIndex
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrOutput);
    }

    /**
     * [multiCall 并发请求]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    private static function multiCall($multiName,$multiService){
        $objRalMulti = new Tieba_Multi($multiName);
        foreach ($multiService as $k => $v){
            $objRalMulti->register($k, new Tieba_Service($v['serviceName']), $v);
        }
        $objRalMulti->call();
        $arrResult = array();
        foreach ($multiService as $k => $v){
            $arrResult[$k] = $objRalMulti->getResult($k);
        }
        return $arrResult;
    }

    /**
     * [_getDB description]
     * @param
     * @return [type] [description]
     */
    private static function _getDB()
    {
        if ( self::$_objDB ){
            return self::$_objDB;
        }

        Bingo_Timer::start('db_init');
        self::$_objDB = Tieba_Mysql::getDB(self::DB_NAME);
        Bingo_Timer::end('db_init');
        if ( self::$_objDB && self::$_objDB->isConnected() ){
            return self::$_objDB;
        }
        else{
            Bingo_Log::warning('fail to connect db');
            return null;
        }
    }
    /**
     * [_queryDB description]
     * @param  [type] $strSql   [description]
     * @param  string $strTimer [description]
     * @return [type]           [description]
     */
    private static function _queryDB($strSql, $strTimer = 'db_query')
    {
        $objDB = self::_getDB();
        if ( !$objDB ){
            Bingo_Log::warning('fail to get db');
            return false;
        }
        Bingo_Timer::start($strTimer);
        $arrRet = $objDB -> query($strSql);
        Bingo_Timer::end($strTimer);
        if ( $arrRet === false ){
            $strLastError = $objDB->error();
            Bingo_Log::warning("execute sql error [$strSql] [$strLastError]");
        }
        return $arrRet;
    }

    /**
     * [_errRet description]
     * @param  [type] $errno [description]
     * @param  array  $data  [description]
     * @return [type]        [description]
     */
    private static function _errRet($errno,$data=array()){
        $arrOutput = array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data'   => $data,
        );
        return $arrOutput;
    }

}