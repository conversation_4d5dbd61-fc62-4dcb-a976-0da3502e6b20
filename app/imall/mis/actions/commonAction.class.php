<?php

class commonAction extends TbMisBingo2Action {
	const NUM_PER_PAGE = 10;
	protected function indexAction(){
		$arrDefaultConfig = array('size'=> self::NUM_PER_PAGE);
		$arrInput = TbMisViewHelper::getCommonInput($arrDefaultConfig);
		//var_dump($arrInput);
		$arrTplVar = TbMisViewHelper::getDefaultTplVar();		
		$arrTplVar = array_merge($arrTplVar, array(
			'mis_table_template' => 'common_goods_list_template',
			'mis_name' => 'ͨ����Ʒ�б�'
			));	
		
		if (isset($arrInput['search_goods_name'])){
			$arrTplVar['search_goods_name'] = $arrInput['search_goods_name'];
		}
		if ($arrInput['status'] == MallDef::GOODS_STATUS_UP){
			$arrTplVar['mis_table'] = 'common_goods_list_up';
		}elseif ($arrInput['status'] == MallDef::GOODS_STATUS_DOWN){
			$arrTplVar['mis_table'] = 'common_goods_list_down';
		}else{
			die('error');
		}
		//
		$arrCategory = MallDef::$goodsTypeMap;
		foreach ($arrCategory as $key => $value){
			if($value['mall'] == MallDef::COMMON_GOODS_KIND){
		        $arrTplVar['CategoryNames'][$key] = $value['cname'];
		        $arrTplVar['CategoryIndex'][] = $key;
			}
		}			
		if (isset($arrInput['search_category_index'])){
			$arrTplVar['category_index_checked'] = $arrInput['search_category_index'];
		}
		
		$arrInput['goods_kind'] = MallDef::COMMON_GOODS_KIND;
		
		if (!isset($_REQUEST['search_sortby'])){
			$sortby = 2;
		}
		else{
			$sortby = $_REQUEST['search_sortby'];
		}
		$arrTplVar['search_sortby'] = $sortby;
	    if($sortby == 2){
	        if($arrInput['status'] == MallDef::GOODS_STATUS_UP){
			    $sortby = 2;
		    }
		    elseif($arrInput['status'] == MallDef::GOODS_STATUS_DOWN){
			    $sortby = 3;
		    }
		}
		
		$arrInput['order'] = MallDef::$goodsSortType[$sortby];
		if($sortby == 0){
			$arrInput['sort'] = 'ASC';
		}   
	    else{ 
	        $arrInput['sort'] = 'DESC';
	    }
		$arrResult = TbMisViewHelper::getDefaultResult();
		MallPage::noBuild();
		$arrOut = GoodsService::getGoodsByParams($arrInput);
		$arrResult['data'] = $arrOut['data'];
		$arrResult['page'] = $arrOut['page'];	
		$arrResult['total'] = GoodsService::getGoodsNumber($arrInput);
		TbMisViewHelper::viewProcess($arrInput,$arrResult,$arrTplVar,$arrDefaultConfig);	
	}	
	protected function downAllAction(){
		$arrInput = array(	
		    'goods_status' => MallDef::GOODS_STATUS_DOWN,
		    'down_time' => time(),
		    'goods_kind' => MallDef::COMMON_GOODS_KIND,
		);
		$ret = MallPage::setGoodsStatus($arrInput);
		if ($ret === false){
			TbMisLog::warning("At downAllAction error");
		}
	}
	protected function downAction(){
	    $goods_id = TbMisRequest::getNumric('goods_id');
		if ($goods_id == 0){
			return false;
		}
		$arrInput = array(
			'goods_id' => intval($goods_id),		
		    'goods_status' => MallDef::GOODS_STATUS_DOWN,
		    'down_time' => time(),
		);
		$ret = MallPage::goodsUpdateById($arrInput);
	    if ($ret === false){
			TbMisLog::warning("At downAction error");
		}
	}
	protected function upAction(){
	    $goods_id = TbMisRequest::getNumric('goods_id');
		if ($goods_id == 0){
			return false;
		}
		$arrInput = array(
			'goods_id' => intval($goods_id),		
		    'goods_status' => MallDef::GOODS_STATUS_UP,
		    'up_time' => time(),
		);
		$ret = MallPage::goodsUpdateById($arrInput);
	    if ($ret === false){
			TbMisLog::warning("At upAction error");
		}	
	}	
	protected function deleteAction(){
		$goods_id = TbMisRequest::getNumric('goods_id');
		if ($goods_id == 0){
			return false;
		}
		$arrInput = array(
			'goods_id' => intval($goods_id),		
		);
		$ret = MallPage::goodsDelById($arrInput);
	    if ($ret === false){
			TbMisLog::warning("At deleteAction error");
		}	
	}	
	protected function editformAction(){
		$goods_id = TbMisRequest::getNumric('goods_id');
		$arrGoods = false ;
		if ($goods_id != 0) {
			MallPage::noBuild();
			$arrGoods = MallPage::getGoods($goods_id);
		}
		if (empty($arrGoods)){
			$arrGoods = array(
			    'forum_id' => 0,
				'goods_id' => 0,
				'goods_name' => '',
				'goods_category_name' => 'template',
			    'goods_level' => 1,
			    'goods_kind' => MallDef::COMMON_GOODS_KIND,
				'goods_desc' => '', 
			    'goods_status' => 2,
				'goods_pic' => '',
				'goods_not4sale' => 0,
				'goods_price' => 0,
				'goods_discount' => 100,
			    'buff_data' => mc_pack_array2pack(array('left_top_pic'=>'',
			                         'right_top_pic' => '',
	                                 'left_bottom_pic' => '',
	                                 'right_bottom_pic' => '',
	                                 'background_pic' => '')),
				'goods_time_count' => 1,
			    'reserve1' => 0,
			);
		}
		else{
			$arrGoods = $arrGoods[0];
		}
		
		$arrCluster = array();
		$arrCategory = MallDef::$goodsTypeMap;
		foreach ($arrCategory as $name => $property){
			if($property['mall'] == MallDef::COMMON_GOODS_KIND){
		        $arrGoodsCategory[] = array('name' => $name, 'desc' => $property['cname']);
		        foreach($property['buff_data'] as $item){
			        $structTemp['item_name'] = $item['name'];
			        $structTemp['item_desc'] = $item['desc'];
			        $arrCluster[$name][] = $structTemp;
		        }
			}
		}
	    				
		if ($goods_id > 0 ){ 
			$arrGoods['goods_pic'] = MallDef::GOODS_PIC_EX.$arrGoods['goods_pic'];
			$obj = GoodsTypeFactory::get($arrGoods,false);
			$expireType = $obj->getGoodsExpireType();
			if($expireType == MallDef::DEFAULT_EXPIRE_TYPE)
			//if($arrGoods['goods_category_name'] == MallDef::TYPE_NAME_TEMPLATE) 
			{			
			    $arrGoods['goods_time_count'] = $arrGoods['goods_time_count']/24/3600/30; 
			}
		}
	    
        $arrGoodsKind = array(
            array('name' => MallDef::COMMON_GOODS_KIND, 'desc' => 'ͨ����Ʒ'),
            array('name' => MallDef::ALL_GOODS_KIND, 'desc' => '��ͨ��Ʒ'),
        );
	    if(isset($arrGoods['buff_data'])){
            $arrGoods['buff_data'] = mc_pack_pack2array($arrGoods['buff_data']);
            foreach ($arrGoods['buff_data'] as $key => $value){
        	    if(!empty($value)){
        	        $arrGoods['buff_data'][$key] = MallDef::GOODS_PIC_EX.$value;
        	    }
            }
        }

		TbMisView::assign('goods_category',$arrGoodsCategory);
		TbMisView::assign('goods_kind',$arrGoodsKind);
		TbMisView::assign('goods',$arrGoods);
		TbMisView::assign('option',$arrCluster);
		TbMisView::assign('mis_table','common_goods_form');
		TbMisView::render('base.tpl');			
	}
	protected function mergeAction(){
		$goods_id = TbMisRequest::getNumric('goods_id');

		$arrInput = array();
		$arrInput['goods_id'] = intval($goods_id);
		$arrInput['forum_id'] = 0;
		$arrInput['goods_name'] = TbMisRequest::getParam('goods_name','');
		//��Ʒ���Ĭ�ϡ�ģ�塱�������������ʱ��Ҫ��ע��ȥ��
		//$arrInput['goods_category_name'] = 'template';
		$arrInput['goods_category_name'] = TbMisRequest::getParam('goods_category_name','');
		$arrInput['goods_level'] = 1;
		$arrInput['goods_kind'] = MallDef::COMMON_GOODS_KIND;
		$arrInput['goods_pic'] = TbMisRequest::getParam('goods_pic','');
		$arrInput['goods_desc'] = TbMisRequest::getParam('goods_desc','');
		$arrInput['goods_not4sale'] = TbMisRequest::getParam('goods_not4sale',0);
		$arrInput['goods_price'] = TbMisRequest::getNumric('goods_price');
		$arrInput['goods_discount'] = TbMisRequest::getNumric('goods_discount');
		$arrInput['goods_num'] = 0;
		$arrInput['goods_time_count'] = TbMisRequest::getNumric('goods_time_count');
		$arrInput['reserve1'] = TbMisRequest::getNumric('reserve1');
		$arrInput['pay_type'] = TbMisRequest::getNumric('pay_type');
		$arrInput['goods_status'] = TbMisRequest::getParam('goods_status',2);
		$arrInput['down_time'] = TbMisRequest::getParam('down_time',time());
		if(!empty($arrInput['goods_pic'])){
			$arrInput['goods_pic'] = GoodsService::processPicUrl($arrInput['goods_pic']);
		}
		
		$obj = GoodsTypeFactory::get($arrInput,false);
		$expireType = $obj->getGoodsExpireType();
		if($expireType == MallDef::DEFAULT_EXPIRE_TYPE)
		//if($arrInput['goods_category_name'] == MallDef::TYPE_NAME_TEMPLATE) 
		{			
			$arrInput['goods_time_count'] = $arrInput['goods_time_count']*24*3600*30; 
		}
		
		$arrBuffData = array();
		$arrCategory = MallDef::$goodsTypeMap;
	    foreach ($arrCategory as $name => $property){
	    	if($name == $arrInput['goods_category_name']){
		        foreach($property['buff_data'] as $item){
			        $arrBuffData[$item['name']] = GoodsService::processPicUrl(TbMisRequest::getParam($item['name'],''));
		        }
		        break;
	    	}	    	
		}
		
		$arrInput['buff_data'] = mc_pack_array2pack($arrBuffData);
		$arrUser = TbMisDict::get('user');
		$arrInput['user_name'] = $arrUser['uname'];
		$arrInput['user_id'] = $arrUser['uid'];
		if ($arrInput['goods_name'] == ''
			|| $arrInput['goods_pic'] == ''){
				return false;
		}
		//create
		if ($goods_id == 0) {
			$ret = MallPage::goodsAdd($arrInput);
			if ($ret != true){
				TbMisLog::warning('create goods item fail',$arrInput);
			}else{
				TbMisLog::notice("{$arrUser['uname']} add goods item ",$arrInput);
			}
			
		}else{
			$ret = MallPage::goodsUpdateById($arrInput);
			if ($ret != true){
				TbMisLog::warning('edit goods item fail',$arrInput);
			}else{
				TbMisLog::notice("{$arrUser['uname']} add goods item ",$arrInput);
			}						
		}		
		TbMisView::render('dialog_close.tpl');
		return true;				
	}
}