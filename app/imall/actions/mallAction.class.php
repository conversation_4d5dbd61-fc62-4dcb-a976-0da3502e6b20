<?php
/**
 * mall page
 *@since 2012-01-04
 */
class mallAction extends MallActionBase
{
	public function execute() {
		$varType = Bingo_Http_Request::get('type', 'all');
		
		//browse check and data perpare...
		$this->bwsCheck();
		
		if ( in_array($varType,self::$varUserTypeMap) ) 
		{
			$varGoodStatus = array(MallDef::GOODS_STATUS_UP,MallDef::GOODS_STATUS_DOWN );
		}
		else 
		{
			$varGoodStatus = array(MallDef::GOODS_STATUS_UP);
		}
		
		//ע��  ��� ��ID  Ϊ��Ĭ�Ͻ��� ͨ�ðٱ���
		if ( empty($this->arrForum['fid'])) 
		{
//			if ( in_array($varType,self::$varTypeMap) ) 
//			{
//				//get common goods...
//				//*do for Performance*/$retList = MallPage::getCommonGoods($varGoodStatus);
//			}
//			else 
//			{
//				//get common goods by type...
//				//*do for Performance*/$retList = MallPage::getCommonGoodsByType($varType,$varGoodStatus);
//			}
			
			$varStlogType = 0;
			//no forum title
			$retGradeTitle = array();
			$varMall = MallDef::COMMON_GOODS_KIND;
		}
		else 
		{
			if ( in_array($varType,self::$varTypeMap) ) 
			{
				if (in_array($varType,self::$varUserTypeMap)) 
				{
					$varStlogType = 1;
				}
				else 
				{
					$varStlogType = 2;
				}
				//get all goods...
				//*do for Performance*/$retList = MallPage::getAllGoods(array(MallDef::ALL_GOODS_FORUM_ID ,$this->arrForum['fid']),$varGoodStatus);
			}
			else 
			{
				$varStlogType = 2;
				//get goods by type...
				//*do for Performance*/$retList = MallPage::getGoodsByType(array(MallDef::ALL_GOODS_FORUM_ID ,$this->arrForum['fid']),$varType,$varGoodStatus);
			}
			
			//get forum title
			$retGradeTitle = MallPage::getForumTitle($this->arrForum['fid'],$this->arrForum['fname']);
			$varMall = MallDef::ALL_GOODS_KIND ;
		}
		
		//stlog is pv 
		Tieba_Stlog::addNode('ispv', 1);
		//stlog page type
		Tieba_Stlog::addNode('pagetype', $varStlogType);
		
		$retAutoGoods = array();
		
		//get user like forum list 
		if ($this->arrUser['is_login']) 
		{
			$retForumList = RpcIdlFulike::getLikeList($this->arrUser['id'],1,MallDef::FORUM_LIST_PAGE_SIZE,0);
			
			//*do for Performance*/$retMyItems = MallPage::getMyItems($this->arrForum['fid'],$this->arrUser['id']);
			
			if (empty($retMyItems) || !is_array($retMyItems['res']['item_list'])) 
			{
				$retMyItems['res']['item_list'] = array();
			}
			
			if ( in_array($varType,self::$varUserTypeMap) ) 
			{
				$varWithOnlyMine = true;
				
				if ( self::MALL_USER_EXPIRE_TYPE == $varType ) 
				{
					$retExpired = array(MallDef::GOODS_IS_EXPIRED);
				}
				else 
				{
					$retExpired = array(MallDef::GOODS_NOT_EXPIRED);
					//get auto using goods
					//*do for Performance*/$retAutoGoods = MallPage::getUserAutoGoods($retList['info'],$this->arrForum['level_id']);
				}
			}
			else {
				$varWithOnlyMine = false;
				
				$retExpired = array(MallDef::GOODS_NOT_EXPIRED,MallDef::GOODS_IS_EXPIRED);
			}
			
			//*do for Performance*/$retList['info'] = MallPage::dealGoodsAndPersonalInfo($retList['info'],$retMyItems['res']['item_list'],$varWithOnlyMine,$retExpired,$this->arrForum['level_id']);
		}
		else 
		{
			$retForumList = array();
		}
		
		$retList['info'] = empty($retList['info'])? array() : array_values($retList['info']);
		
		$retAutoGoods = empty($retAutoGoods)? array() : array_values($retAutoGoods);
		
		$retList['info'] = array_merge($retList['info'],$retAutoGoods);
		
		//unset ip 
		unset($this->arrUser['ip']);
		
		//gen like tbs
		$retLikeTbs = Tieba_Tbs::gene($this->arrUser['is_login']);
		
		$this->setPageParams('list', $retList['info']);
		$this->setPageParams('forum_list', $retForumList);
		$this->setPageParams('title_list', $retGradeTitle);
		$this->setPageParams('type_list', self::filter($varMall));
		$this->setPageParams('mall_type', $varMall);
		$this->setPageParams('like_tbs', $retLikeTbs);
		
		//the following is for no username logic...
		//$this->showNameRule();
		$this->arrUser['email'] = Tieba_Util::maskEmail($this->arrUser['email']);
		$this->arrUser['mobilephone'] = Tieba_Util::maskPhone($this->arrUser['mobilephone']);
		$this->setPageParams(self::MALL_USER_INFO_KEY, $this->arrUser);
		
		$this->setTemplateFile('mall.php');
	}
	
	public function showNameRule(){
		if ($this->arrUser['is_login'] && 1 == $this->arrUser['no_un']) {
			if (!empty($this->arrUser['email'])) {
				$strShowEmail = Tieba_Util::maskEmail($this->arrUser['email']);
				
				//if (false !== $strShowEmail) {
					$this->arrUser['name'] = $strShowEmail;
					return true;
				//}
			}

			if (!empty($this->arrUser['mobilephone'])) {	//do not consider 0!
				$strShowMobile = Tieba_Util::maskPhone($this->arrUser['mobilephone']);
				
				//if (false !== $strShowMobile) {
					$this->arrUser['name'] = $strShowMobile;
					return true;
				//}
			}
		}
	}
	
	public static function encryptEmail($strEmail, $intEncryptLen = 3, $intDisplayLen = 3, $strEncryptChar = '*') {
		if (empty($strEmail)) {
			return false;
		}
		
		$strPart = strstr($strEmail, '@');
		
		if (false === $strPart) {
			return false;
		}
		
		$strToEncrypt = str_replace($strPart, '', $strEmail);
		$intLen = mb_strlen($strToEncrypt);
		
		if ($intDisplayLen >= $intLen) {
			$strToEncrypt = substr($strToEncrypt, 0, $intLen - 1);
			$strToEncrypt = str_pad($strToEncrypt, $intLen - 1 + $intEncryptLen, $strEncryptChar);
		}else {
			$strToEncrypt = substr($strToEncrypt, 0, $intDisplayLen);
			$strToEncrypt = str_pad($strToEncrypt, $intDisplayLen + $intEncryptLen, $strEncryptChar);
		}
		
		return $strToEncrypt.$strPart;
	}
	public static function encryptMobile($isMobile) {
		$strMobile = (string) $isMobile;
		$intLen = strlen($strMobile);
		
		if ($intLen < 11) {
			return false;
		}
		
		$strMobile{$intLen - 4} = '*';
		$strMobile{$intLen - 5} = '*';
		$strMobile{$intLen - 6} = '*';
		$strMobile{$intLen - 7} = '*';
		$strMobile{$intLen - 8} = '*';
		
		return $strMobile;
	}
	
	public static function filter($varMall = null)
	{
		if (null === $varMall) 
		{
			return MallDef::$goodsTypeMap;
		}
		
		foreach (MallDef::$goodsTypeMap as $varKey => $varItem) 
		{
			if ($varMall == $varItem['mall']) $retList[$varKey] = $varItem;
		}
		
		return $retList;
	}
}
