//@description: �㲥��Ƶ��Ʒ���飬������Դ�ڵ�������վ
//@author: haoyunfeng

struct video_detail
{
  uint64_t works_id;      	//	��Ʒid,ͬ����������ʱ����ϵͳ�Զ�����             
  string   site;          	//	 վ����д                                         
  string   site_name;    	//	վ������                                            
  uint64_t video_id;  	//	��������ƷID                                          
  string   works_type;	//	��Ʒ����                                              
  string   title;	//	��Ʒtitle                                                   
  string   trunk;	//	title����                                                   
  string   version;	//	��Ʒ�汾                                                  
  string   season;	//	��Ʒ����                                                  
  string   season_type;   	//	��������                                          
  string   max_season;	//	��ƷĿǰ���                                          
  string   max_episode;	//	��Ʒ��/����                                           
  uint32_t finished;	//	�Ƿ����                                                
  string   sub_season;	//	�ӱ���                                                
  string   title_eng;	//	Ӣ�ı���                                                
  string   alias;	//	��Ʒ����                                                    
  string   director;	//	����                                                    
  string   actor;	//	��Ա                                                        
  string   voiceactor;	//	����                                                  
  string   area;	//	����                                                        
  string   type;	//	����                                                        
  string   vimg_url;      	//	���溣��                                          
  string   language;      	//	����                                              
  string   intro;	//	���                                                        
  string   oneword;	//	һ�仰���                                                
  string   net_show_time;	//	��ӳʱ��                                            
  string   duration;	//	ʱ��                                                    
  string   rating;	//	����                                                      
  string   imdb;	//	imdb                                                        
  string   big_poster;	//	�󺣱�                                                
  string   source;	//	obj��Դ                                                   
  uint64_t last_time;	//	���һ�θ���ʱ��                                        
  string   obj_url;	//	վ����Ʒ�е�URL    																
};

service videodetail
{
    /*
	* @breif  :  ʹ��works_id��ȡ��Ʒ������Ϣ
	* @param [in] : works_id : uint64_t : ��ƷID
	* @param [out] : info: video_detail ����Ʒ������Ϣ
	*/
	void getByWorksId (uint64_t works_id,out video_detail info[]);
	
	/*
	* @breif  :  ʹ��video_id��ȡ��Ʒ������Ϣ
	* @param [in] : video_id : uint64_t : ��������ƷID
	* @param [in] : site ��string : ����վ����
	* @param [out] : info: video_detail ����Ʒ������Ϣ
	*/
	void getByVideoId	(uint64_t  video_id, string site,	out video_detail info[]);
	
	/*
	* @breif  :  ʹ��title��ȡ��Ʒ������Ϣ
	* @param [in] : title : string : ��Ʒ����
	* @param [out] : info: video_detail ����Ʒ������Ϣ
	*/
	void getByTitle (string title, out video_detail info[]);
	
	/*
	* @breif  :  ��������ʱ�������ȡָ�������ݵ���Ʒ�������ݣ�
	*            ���limit���Ĭ�ϲ�ѯ�������µ�100������
	* @param [in] : limit : uint32_t : ��ѯ������
	* @param [out] : info: video_detail ����Ʒ������Ϣ
	*/
	void getByLimitOnLastTime(uint32_t limit, out video_detail info[]);
	
	/*
	* @breif  :  ������Ʒ��������
	* @param [in] : info : video_detail : ��Ʒ��������
	* @param [out] : errno: uint32_t ��������
	*/
    commit void add (video_detail info, out uint32_t errno);
	
	/*
	* @breif  :  ����������Ʒ��������
	* @param [in] : info : video_detail : ��Ʒ��������
	* @param [out] : errno: uint32_t ��������
	*/
	commit void addBatch(video_detail info[],	out uint32_t errno);
	
	/*
	* @breif  :  ��works_idɾ������
	* @param [in] : works_id : uint64_t : ��ƷID
	* @param [out] : errno: uint32_t ��������
	*/
	commit void delByWorksId(uint64_t works_id, out uint32_t errno);
	
	/*
	* @breif  :  ��video_idɾ������
	* @param [in] : video_id : uint64_t : ��������ƷID
	* @param [in] : site �� string �� ������վƷ���
	* @param [out] : errno: uint32_t ��������
	*/
	commit void delByVideoId(uint64_t video_id,	string site,out uint32_t errno);
};
