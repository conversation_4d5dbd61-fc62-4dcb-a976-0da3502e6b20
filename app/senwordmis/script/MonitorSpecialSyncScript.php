<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
ini_set("memory_limit", "4G");
ini_set('display_errors', 1);
error_reporting(E_ERROR);
require('Init.php');
Senwordmis_Init::init('senwordmis');
define("MODULE_LOG_PATH",ROOT_PATH.'/log/app/'.'senwordmis'.'/');
define("MIS_LOG_LEVEL", 0X04 | 0X02);
define("MIS_LOG_UI","UI");
Bingo_Log::init(array(
    MIS_LOG_UI => array(
        "file" => MODULE_LOG_PATH . DIRECTORY_SEPARATOR . "ui.log",
        "level" =>MIS_LOG_LEVEL, 
    )
),MIS_LOG_UI);
$dataWord = new Service_Data_Word();
$dataPost = new Service_Data_Post();
$nowTime = date('Y-m-d H:i:s');
return;
try {
    $lists = $dataWord->getListInfo();
    foreach ($lists as $list) {
	if($list['post_table'] != 'senword_posts_politics3') continue;   
	$words = $dataWord->getLocalWords($list['list_id']);
		foreach ($words as $item) {
			try{
				$dataPost->syncPost($list['list_id'], $list['post_table'], $item, $list['sync_time'], $nowTime, $list['monitor_type']);
			}
			catch(Exception $e) {
				Dao_SenWordList::setInstance();
				Dao_SenWord::setInstance();
				Dao_Word::setInstance();
				Dao_SenPost::setInstance();
				Dao_SenWork::setInstance();
				Dao_SenPostsNum::setInstance();
				Dao_MonitorAnti02::setInstance();
				Bingo_Log::warning('sync error . trace :'.$e->getTraceAsString().' message:'.$e->getMessage().'errno :'.$e->getCode());
			}
		}
		try{	
			Dao_SenWordList::setInstance();
			$dataWord->updateList($list['list_id'], $nowTime);
			Bingo_Log::notice('monitorSync word '.$list['post_table']);
		}
		catch(Exception $e) {
			Dao_SenWordList::setInstance();
			Dao_SenWord::setInstance();
			Dao_Word::setInstance();
			Dao_SenPost::setInstance();
			Dao_SenWork::setInstance();
			Dao_SenPostsNum::setInstance();
			Dao_MonitorAnti02::setInstance();
			Bingo_Log::warning('updateList error . trace :'.$e->getTraceAsString().' message:'.$e->getMessage().'errno :'.$e->getCode());
		}
	}
} catch (Exception $e) {
	Dao_SenWordList::setInstance();
	Dao_SenWord::setInstance();
	Dao_Word::setInstance();
	Dao_SenPost::setInstance();
	Dao_SenWork::setInstance();
	Dao_SenPostsNum::setInstance();
	Dao_MonitorAnti02::setInstance();
   	Bingo_Log::warning('monitorSync error'.$e->getTraceAsString());
}
Bingo_Log::notice('monitorSync success');
var_dump('success');
exit(0);
?>
