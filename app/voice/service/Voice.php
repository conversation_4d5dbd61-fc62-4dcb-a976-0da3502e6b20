<?php
//@description: voice dl interface
//@author: <EMAIL>


define("MODULE","voice_service");
require_once('../app/voice/dl/voice.php');

class Service_Voice{
	const SERVICE_NAME = "Service_Voice";
	const VOICE_THREAD_TYPE = 11;
	const VOICE_POST_TYPE = 1;
    
    // commit command no define
    const CMD_PERSON_THR_COMMIT = 10013;

	protected static $_cache = null;
	protected static $_conf = null;

    /**
      * @brief init
      * @return: true if success. false if fail.

     **/
    private static function _init(){

         //add init code here. init will be called at every public function beginning.
         //not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
         //init should be recalled for many times.

		 // init conf 
		 /*if(self::$_conf == null){
			 self::$_conf = Bd_Conf::getConf("/app/voice/service_voice");
			 if(self::$_conf == false){
				 Bingo_Log::warning("init get conf fail.");
				 return false;
			 } 
		}*/
        return true;
    }

	private static function _errRet($errno){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

	public static function insertThreadVoiceInfo($arrInput){
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}

		$tmpOutput = Dl_Voice::insertThreadVoiceInfo($arrInput);;

		return $tmpOutput;
	}

	public static function getThreadVoiceInfosByTids($arrInput){
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}

		$tmpOutput = Dl_DdbsVoiceThreadWrite::getThreadVoiceInfosByTids($arrInput);

		return $tmpOutput;
	}

	public static function getThreadVoiceInfosByPids($arrInput){
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}

		$tmpOutput = Dl_DdbsVoiceThreadWrite::getThreadVoiceInfosByPids($arrInput);

		return $tmpOutput;
	}

	public static function addVoiceStream($arrInput){
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}

		$tmpOutput = Dl_DdbsVoiceStreamWrite::writeVoiceStreamInfo($arrInput);

		return $tmpOutput;
	}

	public static function getVoiceStream($arrInput){
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		$bolNeedBase64 = false;
		if(isset($arrInput["need_base64"]) && 1 == $arrInput["need_base64"]){
			$bolNeedBase64 = true;
		} 

		$tmpOutput = Dl_DdbsVoiceStreamWrite::getVoiceStreamInfo($arrInput);
		
		if($bolNeedBase64 && isset($tmpOutput["errno"]) && Tieba_Errcode::ERR_SUCCESS == $tmpOutput["errno"]){
			//针对需要提供base64编码的请求进行数据base64编码，主要防止php对二进制数据进行gbk与utf8互转
			if(!empty($tmpOutput['ret']["voiceStreamInfo"]) && !empty($tmpOutput['ret']["voiceStreamInfo"][0]['voice_content'])){
				$data = $tmpOutput['ret']["voiceStreamInfo"][0]['voice_content'];
				$tmpOutput['ret']["voiceStreamInfo"][0]['voice_content'] = base64_encode($data);
			}
		}

		return $tmpOutput;
	}


	public static function cmPostVoiceInfo($arrInput){
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		 $strInputData = file_get_contents("php://input");
        $strTransMcpack  = mc_pack_text2pack(substr($strInputData,5));
        if($strTransMcpack === false || empty($strTransMcpack)){
            Bingo_Log::warning("mc_pack_text2pack error. [".$_SERVER['REQUEST_URI']."]");
            return self::_errRet(Tieba_Errcode::ERR_MCPACK_ERR);
        }
        $arrTransPack = mc_pack_pack2array($strTransMcpack);
        if($arrTransPack === false || empty($arrTransPack)){
            Bingo_Log::warning("mc_pack_pack2array error. [".$_SERVER['REQUEST_UraRI']."]");
            return self::_errRet(Tieba_Errcode::ERR_MCPACK_ERR);
        }

		//require_once('../php/phplib/tb/Tieba/Type/Thread.php');
		//Bingo_Log::notice(serialize($arrTransPack));
        if($arrTransPack['thread_type'] == self::VOICE_THREAD_TYPE || $arrTransPack['ptype'] == self::VOICE_POST_TYPE){
            $voice_md5 = $arrTransPack['voice_md5'];
            $during_time = $arrTransPack['during_time'];
            if($arrTransPack['command_no'] == Tieba_Cmd::ThreadCommit){
                $thread_type = 0;
            }
            if($arrTransPack['command_no'] == Tieba_Cmd::PostCommit){
                $thread_type = 1;
            }
			//client bug, not commont voice_md5=86316c50250e78e761da659f3293bef5
			if($voice_md5  == '86316c50250e78e761da659f3293bef5'){
				return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
			}
            $arrInput = array(
                'thread_id' => $arrTransPack['thread_id'],
                'post_id' => $arrTransPack['post_id'],
                'thread_type' => $thread_type,
                'voice_md5' => $voice_md5,
                'during_time' => $during_time,
				'user_id' => $arrTransPack['user_id'],
            );

            $ret = Dl_Voice::insertThreadVoiceInfo($arrInput);
            if(!isset($ret['errno']) || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning("voice dl insert thread voice info failure!");
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }

			$ret = Dl_Voice::insertVoiceMd5Index($arrInput);
			if(!isset($ret['errno']) || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning("insert voice md5 index failure!");
				return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
			}
        }

		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

	
	public static function delThreadVoiceInfo($arrInput){
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		 $strInputData = file_get_contents("php://input");
        $strTransMcpack  = mc_pack_text2pack(substr($strInputData,5));
        if($strTransMcpack === false || empty($strTransMcpack)){
            Bingo_Log::warning("mc_pack_text2pack error. [".$_SERVER['REQUEST_URI']."]");
            return self::_errRet(Tieba_Errcode::ERR_MCPACK_ERR);
        }
        $arrTransPack = mc_pack_pack2array($strTransMcpack);
        if($arrTransPack === false || empty($arrTransPack)){
            Bingo_Log::warning("mc_pack_pack2array error. [".$_SERVER['REQUEST_UraRI']."]");
            return self::_errRet(Tieba_Errcode::ERR_MCPACK_ERR);
        }

		//require_once('../php/phplib/tb/Tieba/Type/Thread.php');
		//Bingo_Log::notice(serialize($arrTransPack));
        if($arrTransPack['thread_type'] == self::VOICE_THREAD_TYPE || $arrTransPack['ptype'] == self::VOICE_POST_TYPE){
            if($arrTransPack['command_no'] == Tieba_Cmd::maskDelete){
				//��ѯ����md5
				$arrInput = array(
					'pids' => array(
						array(
							'thread_id' => $arrTransPack['thread_id'],
							'post_id' => $arrTransPack['post_id'],
						),
					),
				);
				$ret = Dl_Voice::getThreadVoiceInfosByPids($arrInput);
				if(!isset($ret['errno']) || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
					Bingo_Log::warning("voice get thread voice infos by ptids failure!");
					return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
				}
				$voice_md5 = $ret['ret']['postVoiceList'][0]['voice_md5'];
				$arrInput = array(
					'thread_id' => $arrTransPack['thread_id'],
					'post_id' => $arrTransPack['post_id'],
				);
				$ret = Dl_Voice::delThreadVoiceInfo($arrInput);
				if(!isset($ret['errno']) || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
					Bingo_Log::warning("voice dl del thread voice info failure!");
					return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
				}
				$arrInput = array(
					'voice_md5' => $voice_md5,
				);
				$ret = Dl_Voice::delVoiceStream($arrInput);
				if(!isset($ret['errno']) || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
					Bingo_Log::warning("voice dl del voice stream failure!");
					return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
				}
            }
        }

		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

	public static function getVoiceStreamInfosByPids($arrInput){
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}

		$tmpOutput = Dl_DdbsVoiceStreamWrite::getVoiceStreamInfosByPids($arrInput);

		return $tmpOutput;
	}

	public static function getVoiceStreamInfosByMd5s($arrInput){
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}

		$tmpOutput = Dl_Voice::getVoiceStreamInfosByMd5s($arrInput);

		return $tmpOutput;
	}

	public static function getVoiceStreamInfosByTids($arrInput){
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}

		$tmpOutput = Dl_DdbsVoiceStreamWrite::getVoiceStreamInfosByTids($arrInput);

		return $tmpOutput;
	}
	
	public static function insertVoiceMd5Index($arrInput){
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}

		$tmpOutput = Dl_Voice::insertVoiceMd5Index($arrInput);

		return $tmpOutput;
	}

	public static function getVoiceMd5Index($arrInput){
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}

		$tmpOutput = Dl_Voice::getVoiceMd5Index($arrInput);

		return $tmpOutput;
	}

	public static function newCmPostVoiceInfo($arrInput){
		if(!self::_init()){
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		 $strInputData = file_get_contents("php://input");
        $strTransMcpack  = mc_pack_text2pack(substr($strInputData,5));
        if($strTransMcpack === false || empty($strTransMcpack)){
            Bingo_Log::warning("mc_pack_text2pack error. [".$_SERVER['REQUEST_URI']."]");
            return self::_errRet(Tieba_Errcode::ERR_MCPACK_ERR);
        }
        $arrTransPack = mc_pack_pack2array($strTransMcpack);
        if($arrTransPack === false || empty($arrTransPack)){
            Bingo_Log::warning("mc_pack_pack2array error. [".$_SERVER['REQUEST_UraRI']."]");
            return self::_errRet(Tieba_Errcode::ERR_MCPACK_ERR);
        }

		//require_once('../php/phplib/tb/Tieba/Type/Thread.php');
		//Bingo_Log::notice(serialize($arrTransPack));
        if($arrTransPack['thread_type'] == self::VOICE_THREAD_TYPE || $arrTransPack['ptype'] == self::VOICE_POST_TYPE){
            $voice_md5 = $arrTransPack['voice_md5'];
            $during_time = $arrTransPack['during_time'];
            if($arrTransPack['command_no'] == Tieba_Cmd::ThreadCommit ||
               $arrTransPack['command_no'] == self::CMD_PERSON_THR_COMMIT){
                $thread_type = 0;
            }
            if($arrTransPack['command_no'] == Tieba_Cmd::PostCommit){
                $thread_type = 1;
            }
			//client bug, not commont voice_md5=86316c50250e78e761da659f3293bef5
			if($voice_md5  == '86316c50250e78e761da659f3293bef5'){
				return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
			}
            $arrInput = array(
                'thread_id' => $arrTransPack['thread_id'],
                'post_id' => $arrTransPack['post_id'],
                'thread_type' => $thread_type,
                'voice_md5' => $voice_md5,
                'during_time' => $during_time,
				'user_id' => $arrTransPack['user_id'],
            );

			
			// ����Ϊddbs˫д
			$ret = Dl_DdbsVoiceThreadWrite::writeThreadVoiceInfo($arrInput);
			if(!isset($ret['errno']) || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning("ddbs write thread voice info failure!");
				return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
			}
			
			$ret = Dl_DdbsVoiceThreadWrite::writeVoiceMd5Index($arrInput);
			if(!isset($ret['errno']) || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning("ddbs write md5 index info failure!");
				return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
			}
        }

		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }
}
