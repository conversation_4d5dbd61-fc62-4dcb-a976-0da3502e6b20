<?php
//require_once "../service/Coupon.php"
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-13 17:22:45
 * @comment testcomment
 * @version
 */
class getCouponAction extends Util_Base {

    public function execute(){
        try {
			
			//������ȡ
            $activityName = strval(Bingo_Http_Request::get('activityName',''));
            $uid = intval(Bingo_Http_Request::get('uid',0));
			
			if ('' == $activityName)
            {
                $errno = Tieba_Errcode::ERR_PARAM_ERROR;
                $this->_jsonRet($errno,Tieba_Error::getErrmsg($errno));
                return;
            }
			
		    if (0 == $uid)
            {
                $errno = Tieba_Errcode::ERR_PARAM_ERROR;
                $this->_jsonRet($errno,Tieba_Error::getErrmsg($errno));
                return;
            }
			
			$loginUid =  Tieba_Session_Socket::getLoginUid();
    
            if ($loginUid !== $uid)
            {
                $errno = Tieba_Errcode::ERR_USER_NOT_LOGIN ;
                $this->_jsonRet($errno,Tieba_Error::getErrmsg($errno));
                return;
            }   
			
            $arrInput = array('uid' => $uid,
            		          'activityName' => $activityName,);
            //$arrOut = Tieba_Service::call('coupon', 'getCoupon', $arrInput);
            $arrOut = Service_Coupon::getCoupon($arrInput);
			//�����������ʼ��ΪĬ��ֵ�������޸�
            $retData=array();
			
			
            if (Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno'])
            {
            	$errno = $arrOut['errno'];
            }
            else
            {
            	$errno = Tieba_Errcode::ERR_SUCCESS;
            	$strCouponId = isset($arrOut['coupon_id'])?$arrOut['coupon_id']:"";
            	$retData['is_allocation'] = isset($arrOut['is_allocation'])?$arrOut['is_allocation']:"";
            	$retData['coupon_id'] = $strCouponId;
				
				if (("" !== $strCouponId) && ('n' === $retData['is_allocation']))
            	{           		
            		// �������ļ���ȡ˽�ŷ��͵�������Ϣ
            		$conf = Util_Conf::getUiConf();
					$arrConfRet = $conf["activity"];
					$fromUid = "";
            		$strMsg = "";
            		
					foreach($arrConfRet as $row)
            		{
            			$confActivityName = $row['name'];
						
            			if (($confActivityName === $activityName) && ('n' === $retData['is_allocation']))
            			{
							$fromUid = $row['from_id'];
            				$strMsg = $row['msg'];
            				$strMsg = str_replace('&coupon', $retData['coupon_id'], $strMsg);
            				Util_SendMsg::receiveMsg($uid, $fromUid, $strMsg);
            			}
            		} 
				
            	}
            }
			
            // Ĭ�ϳɹ�����ֵ
            $this->_jsonRet($errno,Tieba_Error::getUserMsg($errno),$retData);
            
        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            $this->_jsonRet($e->getCode(),$e->getMessage());
        }
    }
}
?>
