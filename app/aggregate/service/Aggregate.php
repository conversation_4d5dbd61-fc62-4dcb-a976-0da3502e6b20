<?php 
// 贴吧导流
// 聚合相关service

class Service_Aggregate {
	private static $boolReloaded = false;
	private static $_default_ie = 'utf-8';
	
	/**
	 * @param
	 * @return unknown|multitype:unknown
	 */
	private static function _errRet($errno){
		return array(
				'errno' => $errno,
				'errmsg' => Tieba_Error::getErrmsg($errno),
		);
	}
	
	/**
	 * @param
	 * @return unknown|multitype:unknown
	 */
	private static function _getMethodsArr(){
		$strCacheFile = self::_getCacheFile();
		if(file_exists($strCacheFile)){
			include ($strCacheFile);
			return $methods_to_subservices;
		}
	
		return self::_reloadMethodsArr();
	}
	
	/**
	 * @param
	 * @return unknown|multitype:unknown
	 */
	private static function _reloadMethodsArr(){
		$path = dirname(__FILE__);
		$dirs = scandir($path);
		//var_dump($dirs);
		$methods_to_subservices = array();
		foreach ($dirs as $dir){
			if($dir === '.' || $dir === '..'){
				continue;
			}
			if(is_dir("$path/$dir")){
				Bingo_Log::debug("find sub dir [$dir]");
				$udir = ucfirst($dir);
				$classFile = "$path/$dir/$udir.php";
				$className = "Service_$udir"."_$udir";
				if(file_exists($classFile)){
					require_once ($classFile);
					if(class_exists($className)){
						$ref = new ReflectionClass($className);
						$methods = $ref->getMethods(ReflectionMethod::IS_PUBLIC);
						//var_dump($methods);
						foreach ($methods as $method){
							$methods_to_subservices[$method->name] = $dir;
						}
							
					}else{
						Bingo_Log::warning("class name not exist [$className]");
						continue;
					}
	
				}else{
					Bingo_Log::warning("invalid dir found [$dir]");
					continue;
				}
			}
	
	
		}
	
		if(count($methods_to_subservices) == 0){
			Bingo_Log::warning("no valid method found. something wrong?");
		}
		$str = var_export ($methods_to_subservices,true);
		$str = "<?php\n\$methods_to_subservices = \n".$str.";\n?>";
		$final_file = self::_getCacheFile();
		$tmp_class_file = $final_file.".bak".rand();
		file_put_contents ($tmp_class_file,$str);
	
		//以下将临时文件修改为实际文件
		if (file_exists($tmp_class_file)) {
			rename ($tmp_class_file,$final_file);
		}
		if (file_exists ($tmp_class_file)) {
			unlink ($tmp_class_file);
		}
	
		self::$boolReloaded = true;
		return $methods_to_subservices;
	}
	
	/**
	 * @param
	 * @return unknown|multitype:unknown
	 */
	private static function _getCacheFile(){
		return dirname(__FILE__).'/methods.php';
	}
	
	/**
	 * @param
	 * @return unknown|multitype:unknown
	 */
	public static function call($name, $arguments){
	
		$methods_to_subservices = self::_getMethodsArr();
		if(!array_key_exists($name, $methods_to_subservices)){
			if(self::$boolReloaded){
				Bingo_Log::warning("methods call not found in service.[$name]");
				return self::_errRet(Tieba_Errcode::ERR_METHOD_NOT_FOUND);
			}else{
				$methods_to_subservices = self::_reloadMethodsArr();
				if(!array_key_exists($name, $methods_to_subservices)){
					Bingo_Log::warning("methods call not found in service.[$name]");
					return self::_errRet(Tieba_Errcode::ERR_METHOD_NOT_FOUND);
				}
			}
		}
		$subService = ucfirst(strtolower($methods_to_subservices[$name]));
		$subServiceFile = dirname(__FILE__)."/".strtolower($subService)."/$subService.php";
		$subServiceClass = "Service_$subService"."_$subService";
	
		if(!file_exists($subServiceFile)){
			Bingo_Log::warning("file call not found in service.[$subServiceFile]");
			return self::_errRet(Tieba_Errcode::ERR_FILE_NOT_FOUND);
		}
		require_once ($subServiceFile);
	
		if(method_exists($subServiceClass, 'preCall')){
			call_user_func_array(array($subServiceClass, 'preCall'),array($arguments));
		}
		$res = call_user_func_array(array($subServiceClass, $name),array($arguments));
		if(method_exists($subServiceClass, 'postCall')){
			call_user_func_array(array($subServiceClass, 'postCall'),array($arguments));
		}
		if($res){
			//$jsRes = Bingo_String::array2json($res);
			return  $res;
		}else{
			Bingo_Log::warning("call user func failed. [$subServiceClass::$name] .");
			if(!self::$boolReloaded){
				self::_reloadMethodsArr();
			}
			return self::_errRet(Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL);
		}
			
	}
	
	/**
	 * @param
	 * @return unknown|multitype:unknown
	 */
	public static function getIE($name){
	
		$methods_to_subservices = self::_getMethodsArr();
		if(!array_key_exists($name, $methods_to_subservices)){
			if(self::$boolReloaded){
				return self::$_default_ie;
			}else{
				$methods_to_subservices = self::_reloadMethodsArr();
				if(!array_key_exists($name, $methods_to_subservices)){
					return self::$_default_ie;
				}
			}
		}
		$subService = ucfirst(strtolower($methods_to_subservices[$name]));
		$subServiceFile = dirname(__FILE__)."/".strtolower($subService)."/$subService.php";
		$subServiceClass = "Service_$subService"."_$subService";
	
		if(!file_exists($subServiceFile)){
			return self::$_default_ie;
		}
		require_once ($subServiceFile);
	
		$service_ie = self::$_default_ie;
	
		if(property_exists($subServiceClass, 'service_ie'))
		{ //获取service的字符编码ie参数
			$service_pro = get_class_vars($subServiceClass);
			$tmp_ie = $service_pro['service_ie'];
			if($tmp_ie==='gbk' || $tmp_ie==='utf-8')
			{
				$service_ie = $tmp_ie;
			}
		}
		return $service_ie;
	}
}

