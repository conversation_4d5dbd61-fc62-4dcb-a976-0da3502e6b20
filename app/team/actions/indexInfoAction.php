<?php
/**
 * @file indexInfoAction
 * <AUTHOR>
 * @date 2016/03/19
 * @brief 
 **/

class indexInfoAction extends Util_Base {

    public function _execute() {
    	try {
    		if ($this->_arrUserInfo['is_login'] !== true ){
                //未登陆
                throw new Exception("user need login!",Tieba_Errcode::ERR_USER_NOT_LOGIN);
            }
			$user_id = $this->_arrUserInfo['user_id'];
			$first_group = Bingo_Http_Request::getNoXssSafe('first_group',0);
			$second_group = Bingo_Http_Request::getNoXssSafe('second_group',0);
			$arrParams = array(
				'forum_id'  => $_COOKIE['MEIZI_MIS_FID'],
			);
			if ($first_group > 0) {
				$arrParams['first_group'] = $first_group;
			}
    		if ($second_group > 0) {
				$arrParams['second_group'] = $second_group;
			}
			$arrRet = Tieba_Service::call('team', 'getGroupsIndexInfo', $arrParams, null, null, 'post', 'php', 'utf-8');
			if( $arrRet === false) {
				Bingo_Log::warning("call service getGroupsIndexInfo error. [input=".serialize($arrParams).'][output='.serialize($arrRet).']');
				throw new Exception("service call fail!",Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
			}
    		if( $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
				Bingo_Log::warning("call service getUserInfo error. [input=".serialize($arrParams).'][output='.serialize($arrRet).']');
				return $this->_jsonRet($arrRet['errno'], Tieba_Error::getErrmsg($arrRet['errno']));
			}
			$this->_jsonRet($arrRet['no'], Tieba_Error::getErrmsg($arrRet['no']),$arrRet['data']);
    	} catch(Exception $e){
            Bingo_Log::warning( "no=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), Tieba_Error::getErrmsg($e->getCode()));	
        }
		
    }
}


?>
