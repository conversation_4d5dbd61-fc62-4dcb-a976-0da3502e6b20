<?php
/**
 * @file getSecondGroupRankAction
 * <AUTHOR>
 * @date 2016/03/19
 * @brief 
 **/

class getSecondGroupRankAction extends Util_Base {

    public function _execute() {
    	try {
    		if ($this->_arrUserInfo['is_login'] !== true ){
                //未登陆
                throw new Exception("user need login!",Tieba_Errcode::ERR_USER_NOT_LOGIN);
            }
            $forum_id = Bingo_Http_Request::getNoXssSafe('fid');
			/*$first_group = Bingo_Http_Request::getNoXssSafe('first_group',0);
			$second_group = Bingo_Http_Request::getNoXssSafe('second_group',0);*/
			//$third_group  = Bingo_Http_Request::getNoXssSafe('third_group',0);
			$pn = Bingo_Http_Request::getNoXssSafe('pn', 1);
			$ps = Bingo_Http_Request::getNoXssSafe('ps', 10);
			$is_month = Bingo_Http_Request::getNoXssSafe('is_month', 0);
			
			$user_id  = $this->_arrUserInfo['user_id'];
			
			$arrParams = array(
				'forum_id' => $forum_id,
				'grouper_id'  => $user_id,
				'offset'   => ($pn-1) * $ps,
				'limit'    => $ps,
				'is_month' => $is_month,
			);
			$arrRet = Tieba_Service::call('team', 'getSecondGroupScoresRank', $arrParams, null, null, 'post', 'php', 'utf-8');
			if( $arrRet === false) {
				Bingo_Log::warning("call service getSecondGroupScoresRank error. [input=".serialize($arrParams).'][output='.serialize($arrRet).']');
				throw new Exception("service call fail!",Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
			}
    		if( $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
				Bingo_Log::warning("call service getGrouperList error. [input=".serialize($arrParams).'][output='.serialize($arrRet).']');
				return $this->_jsonRet($arrRet['errno'], Tieba_Error::getErrmsg($arrRet['errno']));
			}
			$data = Bingo_Encode::convert($arrRet['data'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
			
			$this->_jsonRet($arrRet['errno'], Tieba_Error::getErrmsg($arrRet['errno']),$data);
    	} catch(Exception $e){
            Bingo_Log::warning( "no=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), Tieba_Error::getErrmsg($e->getCode()));	
        }
		
    }
}


?>
