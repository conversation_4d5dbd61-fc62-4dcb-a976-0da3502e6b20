<?php
/**
 * @file getKickOutAction
 * <AUTHOR>
 * @date 2016/03/19
 * @brief 
 **/

class getKickOutAction extends Util_Base {

    public function _execute() {
    	try {
    		if ($this->_arrUserInfo['is_login'] !== true ){
                //未登陆
                throw new Exception("user need login!",Tieba_Errcode::ERR_USER_NOT_LOGIN);
            }
            
			$pn = Bingo_Http_Request::getNoXssSafe('pn', 1);
			$ps = Bingo_Http_Request::getNoXssSafe('ps', 10);
			$forum_id = $_COOKIE['MEIZI_MIS_FID'];
			$user_id = $this->_arrUserInfo['user_id'];
			
			$arrParams = array(
				'forum_id' => $forum_id,
				'user_id'  => $user_id,
				'offset'   => ($pn-1) * $ps,
				'limit'    => $ps,
			);
			$arrRet = Tieba_Service::call('team', 'getOutTeamLogs', $arrParams, NULL, NULL, 'post', 'php', 'utf-8');
			if( $arrRet === false) {
				Bingo_Log::warning("call service getGrouperList error. [input=".serialize($arrParams).'][output='.serialize($arrRet).']');
				throw new Exception("service call fail!",Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
			}
    		if( $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
				Bingo_Log::warning("call service getGrouperList error. [input=".serialize($arrParams).'][output='.serialize($arrRet).']');
				return $this->_jsonRet($arrRet['errno'], Tieba_Error::getErrmsg($arrRet['errno']));
			}
			$data = Bingo_Encode::convert($arrRet['data'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
			
			$this->_jsonRet($arrRet['errno'], Tieba_Error::getErrmsg($arrRet['errno']),$data);
    	} catch(Exception $e){
            Bingo_Log::warning( "no=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), Tieba_Error::getErrmsg($e->getCode()));	
        }
		
    }
}


?>
