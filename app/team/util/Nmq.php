<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2016/5/25
 * Time: 16:28
 */

class Util_Nmq {

    /**
     * @brief nmq提交
     * @param array
     * @return array
     **/
    public static function commitAction($strClass, $method, $arrInput, $not_send = 0)
    {
        if($not_send){
            return true;
        }

        $arrParam = array(
            'class' => $strClass,
            'method'=> $method,
            'param' => $arrInput,
        );
        $arrRet = Tieba_Commit::commit(Util_Define::NMQ_CMD_TEAM_TOPIC_NAME, Util_Define::NMQ_CMD_TEAM_DO_NAME, $arrParam);
        if(false === $arrRet)
        {
            Bingo_Log::warning("tieba_commit_fail. Tieba_Commit::commit error for topic[] cmd[] input=[".serialize($arrParam)."] output=[".serialize($arrRet)."]");
            return false;
        }
        return true;
    }


    /**
     * @brief 执行commitAction回调的方法
     * @param array
     * @return array
     **/
    public static function doAction($arrInput)
    {
        $strClass = $arrInput['class'];
        $strMethod= $arrInput['method'];
        $arrParam = $arrInput['param'];

        if (empty($strClass) || empty($strMethod))
        {
            Bingo_Log::warning('doAction failed[input]'.serialize($arrInput));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR, ' input error! ['.serialize($arrInput).']', __FUNCTION__);
        }

        if (method_exists($strClass, $strMethod) !==true)
        {
            Bingo_Log::warning('doAction invailed[input]'.serialize($arrInput));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR, ' input error! ['.serialize($arrInput).']', __FUNCTION__);
        }

        $arrRet = call_user_func(array($strClass, $strMethod), $arrParam);
        if ($arrRet === false)
        {
            Bingo_Log::warning('call_user_func_array failed[class]'.$strClass.'[method]'.$strMethod.'[param]'.serialize($arrParam));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR, ' input error! ['.serialize($arrInput).']', __FUNCTION__);
        }
        return $arrRet;
    }
}