<?php
class Util_Db {
	const DATABASE_NAME = "forum_team";
	private static $_mysql_operator_white_list = array("+=","-=","|=","&=");
	private static $_dbs = array();
	public static function getDB($dbname=null,$charset='utf8'){
		if(is_null($dbname)){
			$dbname = self::DATABASE_NAME;
		}
		$objTbMysql = Tieba_Mysql::getDB($dbname);
		if($objTbMysql && $objTbMysql->isConnected()) {
			$objTbMysql->charset($charset);
			return $objTbMysql;
		} else {
			Bingo_Log::warning("db connect fail.");
			return null;
		}
	}
	
	public static function getDBWithCache($dbname=null,$charset='utf8'){
		if(is_null($dbname)){
			$dbname = self::DATABASE_NAME;
		}
		if(isset(self::$_dbs[$dbname])){
			return self::$_dbs[$dbname];
		}
		$db = self::getDB($dbname,$charset);
		self::$_dbs[$dbname] = $db;
		return $db;
	}
	
	public static function insert($arrInput){
		if(!isset($arrInput['table']) ||!isset($arrInput['field']) || !is_array($arrInput['field'])){
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$table = $arrInput['table'];
		if(isset($arrInput['db'])){
			$db = $arrInput['db'];
		}else{
			$db = Util_Db::getDB();
		}
		if(is_null($db)){
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$fields = $arrInput['field'];
		$strOnDup = NULL;
		if(isset($arrInput['onDup'])&&is_array($arrInput['onDup'])){
			foreach($arrInput['onDup'] as $key=>$value){
				if($strOnDup !== NULL){
					$strOnDup .= ",";
				}
				$key = mysql_escape_string(trim($key));
				$value = mysql_escape_string(trim($value));
				if(in_array(substr($key,-2),self::$_mysql_operator_white_list)){
					$tmpField = substr($key,0,-2);
					$strOnDup .= "$tmpField=$tmpField".substr($key,-2,1)."$value ";
				}else{
					$strOnDup .= "$key'$value' ";
				}
			}
		}
		$ret = $db->insert($table, $fields,NULL,$strOnDup);
		if($ret <= 0){
			Bingo_Log::warning("[output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$insertId = $db->getInsertID();
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$insertId);
	}
	
	public static function update($arrInput){
		if(!isset($arrInput['table']) ||!isset($arrInput['field']) || !is_array($arrInput['field'])){
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$table = $arrInput['table'];
		$str_field = NULL;
		foreach($arrInput['field'] as $key=>$value){
			if($str_field !== NULL){
				$str_field .= ",";
			}
			$key = mysql_escape_string(trim($key));
			$value = mysql_escape_string(trim($value));
			if(in_array(substr($key,-2),self::$_mysql_operator_white_list)){
				$tmpField = substr($key,0,-2);
				$str_field .= "$tmpField=$tmpField".substr($key,-2,1)."$value ";
			}else{
				$str_field .= "$key'$value' ";
			}
		}
		$str_cond = NULL;
		if(isset($arrInput['cond'])&&is_array($arrInput['cond'])){
			foreach($arrInput['cond'] as $cond=>$value){
				if($str_cond !== NULL){
					$str_cond .= " and ";
				}
				if(is_array($value)){
					$value = array_map(array("Util_Db","addQuote"), $value);
					$str_cond .= "$cond in (".implode(",",$value).") ";
				}else{
					$key = mysql_escape_string(trim($cond));
					$value = mysql_escape_string(trim($value));
					if(in_array(substr($key,-2),self::$_mysql_operator_white_list)){
						$tmpField = substr($key,0,-2);
						$str_cond .= "$tmpField=$tmpField".substr($key,-2,1)."$value ";
					}else{
						$str_cond .= "$key'$value' ";
					}
				}
			}
		}
	
		$str_append = NULL;
		if(isset($arrInput['append'])){
			$str_append = $arrInput['append'];
		}
		if(isset($arrInput['db'])){
		    $db = $arrInput['db'];
		}else{
		    $db = Util_Db::getDB();
		}
		//$db = Util_Db::getDB();
		if(is_null($db)){
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$ret = $db->update($table, $str_field, $str_cond);
		if($ret===false){
			Bingo_Log::warning("[output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$affectedRows = $db->getAffectedRows();
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$affectedRows);
	}
	private static function addQuote($str){
		return "'$str'";
	}
	public static function select($arrInput){

		if(!isset($arrInput['table'])|| !isset($arrInput['field']) || !is_array($arrInput['field'])){
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);			
		}
		$table = $arrInput['table'];
		$arrFields = $arrInput['field'];
		$str_cond = NULL;
		if(isset($arrInput['cond'])&&is_array($arrInput['cond'])){
			foreach($arrInput['cond'] as $cond=>$value){
				if($str_cond !== NULL){
					$str_cond .= " and ";
				}
				if(is_array($value)){
					$value = array_map(array("Util_Db","addQuote"), $value);
					$str_cond .= "$cond in (".implode(",",$value).") ";
				}else{
					$key = mysql_escape_string(trim($cond));
					$value = mysql_escape_string(trim($value));
					if(in_array(substr($key,-2),self::$_mysql_operator_white_list)){
						$tmpField = substr($key,0,-2);
						$str_cond .= "$tmpField=$tmpField".substr($key,-2,1)."$value ";
					}else{
						$str_cond .= "$key'$value' ";
					}
				}
			}
		}
	
		$str_append = NULL;
		if(isset($arrInput['append'])){
			$str_append = $arrInput['append'];
		}
		if(isset($arrInput['db'])){
			$db = $arrInput['db'];
		}else{
			$db = Util_Db::getDB();
		}
		if(is_null($db)){
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$ret = $db->select($table, $arrFields, $str_cond, NULL, $str_append);
		if($ret===false){
			Bingo_Log::warning("[output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$ret);
	}
}
