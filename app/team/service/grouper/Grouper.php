<?php
define('BINGO_ENCODE_LANG','UTF-8');
class Service_Grouper_Grouper {
	//init
	public static $service_ie = 'utf-8';
	public static function getIE($methodName){
		return self::$service_ie;
	}

	private static $Grouper_Model = array(
		'id', //自增ID
		'grouper_id',
		'first_group',//第一分组
		'second_group',//第二分组
		'third_group',//第二分组
		'forum_id',//吧ID，不绑定吧，使用0
		'duty',//
		'start_time',//
		'time_scores',//
		'month_scores',//影院ID
		'total_scores',//电影ID
		'last_update_month',//队伍状态
	);
	
	/**
	 * @brief check grouper
	 * @param user_id
	 * @return array
	 **/
	public static function checkGrouper($arrInput) 
	{
		if (!isset($arrInput['grouper_id']) || !isset($arrInput['forum_id']) || intval($arrInput['grouper_id']) <= 0 ) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		$arrField = array('first_group', 'second_group');
		$grouper_id  = intval($arrInput['grouper_id']);
		$forum_id = intval($arrInput['forum_id']);
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		
		$cond = "forum_id=$forum_id and grouper_id=$grouper_id";
		$res  = $db->select('grouper', $arrField, $cond);
		if($res === false) {
			Bingo_Log::warning("getGrouperProperity error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$first_group = intval($res[0]['first_group']); 
		if (empty($res) || $first_group < 1) {
			Bingo_Log::warning("grouper is not match group! output: " . serialize($res));
			return Util_Function::errRet(Tieba_Errcode::ERR_USER_GROUP_NOT_MATCH);
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $res);
	}
	
	/**
	 * @brief insert grouper into group team,创建团队
	 * @param user_id
	 * @return array
	 **/
	public static function addGrouper($arrInput)
	{
		if (!isset($arrInput['user_id']) || !isset($arrInput['first_group']) || !isset($arrInput['second_group'])
			|| !isset($arrInput['third_group']) || !isset($arrInput['grouper_id']) || !isset($arrInput['forum_id'])
			|| intval($arrInput['user_id']) <= 0 || !is_array($arrInput['grouper_id'])) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		//check permission
		$ret = Util_Define::suCheck(intval($arrInput['user_id']));
		if ($ret === false) {
			return Util_Function::errRet(Tieba_Errcode::ERR_DIFANG_UPDATE_GROUP_NO_PERMISSION,"","super administrator error!");
		}
		
		$db = Util_Db::getDB();
		if($db == null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$arrGrouperId = $arrInput['grouper_id'];
		$count        = count($arrGrouperId);
		$forum_id     = intval($arrInput['forum_id']);
		//检查条件数量限制
		$arrInput['count'] = $count;
		$arrRet = self::checkGroupLimit($arrInput);
		if ($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			return $arrRet;
		}
		//check group and insertgroupscores
		/*
		 * check group if exist
		 * self::_checkGroup
		 * self::createGroup
		 * */
		//check user if existed
		$res_unames = array();
		$errno = Tieba_Errcode::ERR_SUCCESS;
		foreach ($arrGrouperId as $gid) {
			$arrParams = array(
				'grouper_id' => $gid['user_id'],
				'forum_id'   => $forum_id,
			);
			$ret = self::checkGrouperExist($arrParams);
			if ($ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
				if ($ret['errno'] == Tieba_Errcode::ERR_USER_HAS_EXISTED) {			//返回所有已存在uname
					$res_unames[] = $gid['user_name'];
					$errno = Tieba_Errcode::ERR_USER_HAS_EXISTED;
				} else {
					return $ret;
				}
				
			}
		}
		if ($errno == Tieba_Errcode::ERR_USER_HAS_EXISTED) {	
			$ret['data']  = $res_unames;
			$ret['errno'] = Tieba_Errcode::ERR_USER_HAS_EXISTED;
			return $ret;
		}
		
		$arrTimeScores= $arrInput['time_scores'];
		$first_group  = intval($arrInput['first_group']);
		$second_group = intval($arrInput['second_group']);
		$third_group  = intval($arrInput['third_group']);
		$duty         = isset($arrInput['duty']) ? intval($arrInput['duty']) : Util_Define::DUTY_TEAM_MEMBER; 
		$begin_month  = strtotime(date("Y-m-01"));
		$time  = time();
		
		//start transaction
		do {
			$db->startTransaction();
			$flag = 0;
			foreach ($arrGrouperId as $key => $value) {
				if ($value['user_id'] <= 0) {
					Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
					return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR, "", "uid unavailable");
				}
				$field = array(
					'grouper_id'   => $value['user_id'],
					'forum_id'     => $forum_id,
					'first_group'  => $first_group,
					'second_group' => $second_group,
					'third_group'  => $third_group,
					'start_time'   => $time,
					'time_scores'  => intval($arrTimeScores[$key]),
					'duty'         => $duty,
					'last_update_month' => $begin_month,
				);
				$arrRet = $db->insert('grouper', $field);
				if($arrRet === false || $db->getAffectedRows() <= 0) {
					Bingo_Log::warning("insert grouper fail! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
					$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
					Bingo_Log::pushNotice("insert_user_error",1);
					$flag = 1;
					break;
				}
			}
			if ($flag == 1) {
				break;
			}
			if (!$db->commit()) {
				Bingo_Log::warning("commit db error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows());
				$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				break;
			}
		} while(0);
		if( $errno != Tieba_Errcode::ERR_SUCCESS ){
			Bingo_Log::pushNotice('transaction_run',0);
			$tmp = $db->rollback();
			Bingo_Log::warning("db->rollback. [".serialize($tmp)."]");
			Bingo_Log::pushNotice("rollback",1);
			return Util_Function::errRet($errno);
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	
	/**
	 * @brief 日常给成员加分,不记录为操作
	 * @param user_id
	 * @return array
	 **/
	public static function addGrouperScore($arrInput) 
	{
		if (!isset($arrInput['grouper_id']) || !isset($arrInput['scores'])
			|| !isset($arrInput['forum_id']) || intval($arrInput['manager_id']) <= 0 ) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$forum_id    = intval($arrInput['forum_id']);
		$grouper_id  = intval($arrInput['grouper_id']);
		$scores      = intval($arrInput['scores']);
		$begin_month  = strtotime(date("Y-m-01"));
		//查询上次更新的月份
		$field = array('last_update_month');
		$cond = "forum_id=$forum_id and grouper_id=$grouper_id";
		$ret = $db->select('grouper', $field, $cond);
		if ($ret === false || $db->getAffectedRows() <= 0) {
			Bingo_Log::warning("select grouper error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		//需要同时更新本月积分和总积分
		$last_month = intval($ret[0]['last_update_month']);
		if ($begin_month != $last_month) {
			$sql_query = "update grouper set month_scores=$scores,total_scores=total_scores+$scores,last_update_month=$begin_month where grouper_id=$grouper_id and forum_id=$forum_id";
		} else {
			$sql_query = "update grouper set month_scores=month_scores+$scores,total_scores=total_scores+$scores where grouper_id=$grouper_id and forum_id=$forum_id";
		}
		$ret = $db->query($sql_query);
		if ($ret === false ) {
			Bingo_Log::warning("update db error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		} elseif ($db->getAffectedRows() !== 1){
			Bingo_Log::warning("update db fail! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_NO_RECORD);
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	
	/**
	 * @brief edit user in group team
	 * @param user_id
	 * @return array
	 **/
	public static function editGrouper($arrInput)
	{
		if (!isset($arrInput['user_id']) || !isset($arrInput['operation_tag']) || !isset($arrInput['forum_id'])
			|| intval($arrInput['operation_tag']) <= 0  || !isset($arrInput['grouper_id']) || intval($arrInput['grouper_id']) <= 0) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		//check permission
		$arrRet = Service_Manager_Manager::authenticationCheck($arrInput);
		if (!isset($arrRet['data']) || $arrRet['data'] == 0) {
			return Util_Function::errRet(Tieba_Errcode::ERR_DIFANG_UPDATE_GROUP_NO_PERMISSION,"","NO_PERMISSION");	
		}
		$forum_id      = intval($arrInput['forum_id']);
		$grouper_id    = intval($arrInput['grouper_id']);
		$operation_tag = intval($arrInput['operation_tag']);
		
		//build update field array
		$arrRet = self::_buildUpdateParams($arrInput);
		if ($arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			return $arrRet;
		}
		$sql_query = $arrRet['data']['sql_query'];
		$value = serialize($arrRet['data']['value']);
		
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		
		$errno = Tieba_Errcode::ERR_SUCCESS;
		//start transaction to update
		do {
			$db->startTransaction();
			//update grouper
			$ret = $db->query($sql_query);
			if ($ret === false || $db->getAffectedRows() !== 1) {
				Bingo_Log::warning("update db grouper error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
				$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				break;
			} 
			//insert into operation 
			$field = array(
				'grouper_id'    => $grouper_id,
				'manager_id'    => intval($arrInput['user_id']),
				'forum_id'      => $forum_id,
				'operation_tag' => $operation_tag,
				'value'         => $value,
				'reason'        => isset($arrInput['reason']) ? strval($arrInput['reason']) : "",
				'operation_time'=> time(),
			);
			$ret = $db->insert('operation', $field);
			if ($ret === false || $db->getAffectedRows() !== 1) {
				Bingo_Log::warning("insert db operation error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
				$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				break;
			}
			//add group total scores and month scores
			if ($operation_tag === Util_Define::OPERATION_SCORES) {
				$ret = self::setGroupScores($arrInput);
				if ($ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
					Bingo_Log::warning("setGroupScores! " . ' output: ' . serialize($ret));
					$errno = isset($ret['errno']) ? $ret['errno'] : Tieba_Errcode::ERR_DB_QUERY_FAIL;
					break;
				}
			}
			//commit
			if (!$db->commit()) {
				Bingo_Log::warning("commit db error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows());
				$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				break;
			}
		} while(0);
		if( $errno != Tieba_Errcode::ERR_SUCCESS ){
			$tmp = $db->rollback();
			Bingo_Log::warning("db->rollback. [".serialize($tmp)."]");
			Bingo_Log::pushNotice("rollback",1);
			return Util_Function::errRet($errno);
		}
		return Util_Function::errRet($errno);
	}
	
	/**
	 * @brief build array
	 * @param user_id
	 * @return array
	 **/
	private static function _buildUpdateParams($arrInput) 
	{
		$forum_id      = intval($arrInput['forum_id']);
		$grouper_id    = intval($arrInput['grouper_id']);
		$operation_tag = intval($arrInput['operation_tag']);
		$cond 	       = "forum_id=$forum_id and grouper_id=$grouper_id";
		
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$now_time  = time();
		$sql_query = "";
		$res_value = "";
		//分操作类型 build parameter
		switch ($operation_tag) {
			case Util_Define::OPERATION_CHANGE_TEAM:			//换队，必须传团队伍号
				//检查组上限
				$arrRet = self::checkGroupLimit($arrInput);
				if ($arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
					return $arrRet;
				}
				//检查职位是否有人担任
				$duty = isset($arrInput['duty']) ? intval($arrInput['duty']) : Util_Define::DUTY_TEAM_MEMBER;
				if ($duty < Util_Define::DUTY_TEAM_MEMBER && $duty > 0) {
					$arrRet = self::checkDutyAvailable($arrInput);
					if ($arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
						return $arrRet;
					}
				}
				$second_group = intval($arrInput['second_group']);
				$third_group  = intval($arrInput['third_group']);
				$duty      = isset($arrInput['duty']) ? intval($arrInput['duty']) : Util_Define::DUTY_TEAM_MEMBER;
				$sql_query = "update grouper set second_group=$second_group,third_group=$third_group,start_time=$now_time,time_scores=0,duty=$duty where " . $cond;
				$res_value = array('second_group' => $second_group, 'third_group' => $third_group, 'duty' => $duty);
				break;
			case Util_Define::OPERATION_SCORES:
				if (!isset($arrInput['scores'])) {
					Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
					return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
				}
				$symbol = isset($arrInput['symbol']) ? intval($arrInput['symbol']) : 1;
				$scores = intval($arrInput['scores']);
				$begin_month  = strtotime(date("Y-m-01"));
				//查询上次更新的月份
				$field = array('last_update_month');
				$cond = "forum_id=$forum_id and grouper_id=$grouper_id";
				$ret = $db->select('grouper', $field, $cond);
				if ($ret === false || $db->getAffectedRows() <= 0) {
					Bingo_Log::warning("select grouper error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
					return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
				}
				//需要同时更新本月积分和总积分
				$last_month = intval($ret[0]['last_update_month']);
				//加分or扣分
				if ($symbol === 1) {
					if ($begin_month != $last_month) {
						$sql_query = "update grouper set month_scores=$scores,total_scores=total_scores+$scores,last_update_month=$begin_month where " . $cond;
					} else {
						$sql_query = "update grouper set month_scores=month_scores+$scores,total_scores=total_scores+$scores where " . $cond;
					}
				} else {
					if ($begin_month != $last_month) {
						$sql_query = "update grouper set month_scores=0-$scores,total_scores=total_scores-$scores,last_update_month=$begin_month where " . $cond;
					} else {
						$sql_query = "update grouper set month_scores=month_scores-$scores,total_scores=total_scores-$scores where " . $cond;
					}
				}
				$res_value = array('symbol' => $symbol,'scores' => $scores);
				break;
			case Util_Define::OPERATION_TIME:
				if (empty($arrInput['time_scores'])) {
					Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
					return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
				}
				$symbol = isset($arrInput['symbol']) ? intval($arrInput['symbol']) : 1;
				$time_scores = intval($arrInput['time_scores']);
				//加分or no扣分
				$sql_query = "update grouper set time_scores=time_scores+$time_scores where " . $cond;
				
				$res_value = array('symbol' => $symbol, 'time_scores' => $time_scores);
				break;
			default:
				//$errno = Tieba_Errcode::ERR_PARAM_ERROR;
				break;
		}
		$res = array(
			'sql_query' => $sql_query,
			'value'     => $res_value,
		);
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $res);
	}
	
	/**
	 * @brief 检查团队职务是否存在
	 * @param fid,f/s/t/group,duty
	 * @return array
	 **/
	public static function checkDutyAvailable($arrInput)
	{
		if (!isset($arrInput['first_group']) || !isset($arrInput['second_group'])
			|| !isset($arrInput['third_group']) || !isset($arrInput['forum_id']) || !isset($arrInput['duty'])
			|| $arrInput['second_group'] <= 0 || $arrInput['third_group'] <= 0) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		$forum_id     = intval($arrInput['forum_id']);
		$first_group  = intval($arrInput['first_group']);
		$second_group = intval($arrInput['second_group']);
		$third_group  = intval($arrInput['third_group']);
		$duty         = intval($arrInput['duty']);
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$cond = "forum_id=$forum_id and first_group=$first_group and second_group=$second_group and third_group=$third_group and duty=$duty";
		$ret = $db->select('grouper',array('grouper_id'), $cond);
		if ($ret === false) {
			Bingo_Log::warning("select grouper id error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		if (isset($ret[0]['grouper_id']) && intval($ret[0]['grouper_id']) > 0) {
			Bingo_Log::warning("grouper id duty already exist! grouper:" . $ret[0]['grouper_id']);
			return Util_Function::errRet(Tieba_Errcode::ERR_PROTO_FIELD_ALREADY_EXISTS);
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	
	/**
	 * @brief 检查团小队上限
	 * @param user_id
	 * @return array
	 **/
	public static function checkGroupLimit($arrInput)
	{
		if (!isset($arrInput['first_group']) || !isset($arrInput['second_group'])
			|| !isset($arrInput['third_group']) || !isset($arrInput['forum_id'])
			|| $arrInput['second_group'] <= 0 || $arrInput['third_group'] <= 0) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		$count        = isset($arrInput['count']) ? intval($arrInput['count']) : 1;
		$forum_id     = intval($arrInput['forum_id']);
		$first_group  = intval($arrInput['first_group']);
		$second_group = intval($arrInput['second_group']);
		$third_group  = intval($arrInput['third_group']);
		if ($second_group > Util_Define::SECOND_GROUP_LIMIT || $third_group > Util_Define::THIRD_GROUP_LIMIT) {
			Bingo_Log::warning ( "group num over limit. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet(Tieba_Errcode::ERR_NZYQ_DEFINE_MAX, "", "group num over limit");
		}
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$cond = "forum_id=$forum_id and first_group=$first_group and second_group=$second_group and third_group=$third_group";
		$ret = $db->selectCount('grouper', $cond);
		if ($ret === false) {
			Bingo_Log::warning("select grouper count error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		if (($ret + $count) > Util_Define::GROUPER_LIMIT_PER_TEAM) {
			Bingo_Log::warning ( "group num over limit. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet(Tieba_Errcode::ERR_NZYQ_DEFINE_MAX, "", "team num already full");
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $ret);
	}
	
	/**
	 * @brief 检查成员是否存在
	 * @param user_id
	 * @return array
	 **/
	public static function checkGrouperExist($arrInput)
	{
		if (!isset($arrInput['grouper_id']) || !isset($arrInput['forum_id'])) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		$grouper_id  = intval($arrInput['grouper_id']);
		$forum_id = intval($arrInput['forum_id']);
		/*$time_scores  = intval($arrInput['time_scores']);
		$first_group  = intval($arrInput['first_group']);
		$second_group = intval($arrInput['second_group']);
		$third_group  = intval($arrInput['third_group']);*/
		
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$cond = "grouper_id=$grouper_id and forum_id=$forum_id";
		$ret = $db->select('grouper', array('id'), $cond);
		if ($ret === false) {
			Bingo_Log::warning("select grouper error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		if ($db->getAffectedRows() > 0 && !empty($ret[0])) {
			Bingo_Log::warning("grouper already exist!");
			return Util_Function::errRet(Tieba_Errcode::ERR_USER_HAS_EXISTED, "", "grouper already exist!");
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	
	/**
	 * @brief get 成员
	 * @param user_id
	 * @return array
	 **/
	public static function getGrouperList($arrInput)
	{
		if (!isset($arrInput['user_id']) || !isset($arrInput['forum_id'])) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		//check permission
		$arrRet = Service_Manager_Manager::authenticationCheck($arrInput);
		if (!isset($arrRet['data']) || $arrRet['data'] == 0) {
			return Util_Function::errRet(Tieba_Errcode::ERR_DIFANG_UPDATE_GROUP_NO_PERMISSION,"","NO_PERMISSION");	
		}
		$offset = intval($arrInput['offset']);
		$limit  = isset($arrInput['limit']) ? intval($arrInput['limit']) : 20;
		$first_group  = intval($arrInput['first_group']);
		$second_group = intval($arrInput['second_group']);
		$third_group  = intval($arrInput['third_group']);
		$forum_id = intval($arrInput['forum_id']);
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		
		//get grouper
		$field = Util_Define::$grouper_priority;
		$cond = "forum_id=$forum_id and first_group=$first_group and second_group=$second_group and third_group=$third_group";
		
		$append = "limit $offset,$limit";
		$ret = $db->select('grouper', $field, $cond, null, $append);
		if($ret === false) {
			Bingo_Log::warning("getGrouperList error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [input: $offset $limit ] [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		//get uname
		$ret = Service_Grouper_Grouper::_mgetUnamesByUid($ret, "grouper_id", "user_name");
        //get 入团时间
        $ret =  self::_mgetJoinTime($ret);
        
		//get grouper forum level thread nums
		$arrRet = self::_mgetForumLevelThreadNum($ret);
		if ($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( "_mgetForumLevelThreadNum fail. [" . serialize ( $arrRet ) . "]" );
			$arrRet['errno'] = isset($arrRet['errno']) ? $arrRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
			return Util_Function::errRet($arrRet['errno']);
		}
		
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
	}
	
	
	
	/**
	 * @brief kick out成员
	 * @param user_id
	 * @return array
	 **/
	public static function kickOutTeam($arrInput)
	{
		if (!isset($arrInput['user_id']) || !isset($arrInput['forum_id']) || !isset($arrInput['grouper_id'])) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		//check permission
		$arrRet = Service_Manager_Manager::authenticationCheck($arrInput);
		if (!isset($arrRet['data']) || $arrRet['data'] == 0) {
			return Util_Function::errRet(Tieba_Errcode::ERR_DIFANG_UPDATE_GROUP_NO_PERMISSION,"","NO_PERMISSION");	
		}
		
		$reason = isset($arrInput['reason']) ? strval($arrInput['reason']) : "";
		$forum_id     = intval($arrInput['forum_id']);
		$grouper_id  = intval($arrInput['grouper_id']);
		
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		
		$errno = Tieba_Errcode::ERR_SUCCESS;
		//start transaction to update
		do {
			$db->startTransaction();
			
			//insert operation 
			$field = array(
				'grouper_id'    => $grouper_id,
				'manager_id'    => intval($arrInput['user_id']),
				'forum_id'      => $forum_id,
				'reason'        => $reason,
				'kickout_time'  => time(),
			);
			$ret = $db->insert('kickout', $field);
			if ($ret === false || $db->getAffectedRows() !== 1) {
				Bingo_Log::warning("insert db kickout error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
				$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				break;
			}
			//delete item form group
			$cond = "forum_id=$forum_id and grouper_id=$grouper_id";
			$ret = $db->delete('grouper', $cond);
			if ($ret === false || $db->getAffectedRows() !== 1) {
				Bingo_Log::warning("delete db grouper error! sql: ". $db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
				$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				break;
			}
			//commit
			if (!$db->commit()) {
				Bingo_Log::warning("commit db error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows());
				$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				break;
			}
		} while(0);
		if( $errno != Tieba_Errcode::ERR_SUCCESS ){
			$tmp = $db->rollback();
			Bingo_Log::warning("db->rollback. [".serialize($tmp)."]");
			Bingo_Log::pushNotice("rollback",1);
			return Util_Function::errRet($errno);
		}
		return Util_Function::errRet($errno);
	}
	
	/**
	 * @brief 展示对成员操作记录
	 * @param user_id
	 * @return array
	 **/
	public static function getOperateLogs($arrInput)
	{
		if (!isset($arrInput['user_id']) || !isset($arrInput['forum_id']) || !isset($arrInput['grouper_id'])) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		//check permission
		$arrRet = Service_Manager_Manager::authenticationCheck($arrInput);
		if (!isset($arrRet['data']) || $arrRet['data'] == 0) {
			return Util_Function::errRet(Tieba_Errcode::ERR_DIFANG_UPDATE_GROUP_NO_PERMISSION,"","NO_PERMISSION");	
		}
		$limit  = isset($arrInput['limit']) ? intval($arrInput['limit']) : 10;
		$grouper_id  = intval($arrInput['grouper_id']);
		$forum_id = intval($arrInput['forum_id']);
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$field = Util_Define::$operation_log;
		$cond  = "forum_id=$forum_id and grouper_id=$grouper_id";
		$append = "limit $limit";
		$ret = $db->select('operation', $field, $cond, null, $append);
		if($ret === false) {
			Bingo_Log::warning("getOperateLogs error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [error: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		//build return data:
		//change uname from uids
		$user_ids = array();
		foreach ($ret as $value) {
			$user_ids[] = $value['manager_id'];
		}
		$input = array(
		    "user_id" => $user_ids
		);
		$res = Tieba_Service::call('user', 'getUnameByUids', $input, null, null, 'post', 'php', 'utf-8');
    	if ($res === false || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning("call service user getUidByUnames error. [input=".serialize($input).'][output='.serialize($res).']');
			return Util_Function::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}
		$unames = $res['output']['unames'];
		$user_names = array();
		foreach ($unames as $un) {
			$user_name[$un['user_id']] = $un['user_name'];
		}
		if (!empty($user_name)) {
			foreach ($ret as $key => &$oper) {
				$oper['manager_name'] = $user_name[$oper['manager_id']];
				//unseialize value data
				$oper['value'] = unserialize($oper['value']);
			}
		}
		
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $ret);
	}
	
	/**
	 * @brief 展示对成员踢队记录
	 * @param user_id
	 * @return array
	 **/
	public static function getOutTeamLogs($arrInput)
	{
		if (!isset($arrInput['user_id']) || !isset($arrInput['forum_id'])) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		//check permission
		$arrRet = Service_Manager_Manager::authenticationCheck($arrInput);
		if (!isset($arrRet['data']) || $arrRet['data'] < 3) {
			return Util_Function::errRet(Tieba_Errcode::ERR_DIFANG_UPDATE_GROUP_NO_PERMISSION,"","NO_PERMISSION");	
		}
		$offset = intval($arrInput['offset']);
		$limit  = isset($arrInput['limit']) ? intval($arrInput['limit']) : 10;
		$forum_id = intval($arrInput['forum_id']);
		
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$field = Util_Define::$kick_out_log;
		$cond  = "forum_id=$forum_id";
		$append = "limit $offset,$limit";
		$ret = $db->select('kickout', $field, $cond, null, $append);
		if($ret === false) {
			Bingo_Log::warning("getOutTeamLogs error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [error: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		
		//build return data:
		//change uname from uids
		/*$user_ids = array();
		foreach ($ret as $value) {
			$user_ids[] = $value['grouper_id'];
		}
		$input = array(
		    "user_id" => $user_ids
		);
		$res = Tieba_Service::call('user', 'getUnameByUids', $input, null, null, 'post', 'php', 'utf-8');
    	if ($res === false || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning("call service user getUidByUnames error. [input=".serialize($input).'][output='.serialize($res).']');
			return Util_Function::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}
		$unames = $res['output']['unames'];
		$user_names = array();
		foreach ($unames as $un) {
			$user_name[$un['user_id']] = $un['user_name'];
		}
		if (!empty($user_name)) {
			foreach ($ret as $key => &$oper) {
				$oper['grouper_name'] = $user_name[$oper['grouper_id']];
				//unseialize value data
				$oper['value'] = unserialize($oper['value']);
			}
		}*/Bingo_Log::warning(var_export($ret,1));
		$ret = self::_mgetUnamesByUid($ret, "grouper_id", "grouper_name");
		//page
		$total = $db->selectCount('kickout', $cond);
		if ($total === false) {
			Bingo_Log::warning("selectCount error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [input: $offset $limit ] [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$data = array(
			'list' => $ret,
			'page' => array(
				'count'   => $total,
				'pn'      => $offset,
				'total_pn'=> intval(ceil($total/$limit)),
			)
		);
		
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $data);
	}
	
	/**
	 * @brief get unames hash table by uids
	 * @param user_id
	 * @return array
	 **/
	public static function _mgetUnamesByUid($arrInput,$tag_id_key,$tag_name_key) 
	{
		//build return data:
		//change uname from uids
		$arrInfo = $arrInput;
		$user_ids = array();
		foreach ($arrInfo as $value) {
			if ($value[$tag_id_key] > 0) {
				$user_ids[] = $value[$tag_id_key];
			}
		}
		$input = array(
		    "user_id" => $user_ids
		);
		$res = Tieba_Service::call('user', 'mgetUserData', $input, null, null, 'post', 'php', 'utf-8');
    	if ($res === false || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning("call service user getUidByUnames error. [input=".serialize($input).'][output='.serialize($res).']');
			return $arrInfo;
		}
		$userInfo = $res['user_info'];
		if (!empty($userInfo)) {
			foreach ($arrInfo as $key => &$oper) {
				$oper[$tag_name_key] = $userInfo[$oper[$tag_id_key]]['user_name'];
				$oper['portrait'] = strval(Tieba_Ucrypt::encode($userInfo[$oper[$tag_id_key]]['user_id'], $oper[$tag_name_key],$userInfo[$oper[$tag_id_key]]['portrait_time']));
			}
		}
		return $arrInfo;
	}
	
	/**
	 * @brief get 成员个人信息
	 * @param user_id
	 * @return array
	 **/
	public static function getUserInfo($arrInput)
	{
		if (!isset($arrInput['user_id']) || !isset($arrInput['forum_id']) || !isset($arrInput['grouper_id'])) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		//check permission
		/*$arrRet = Service_Manager_Manager::authenticationCheck($arrInput);
		$role = isset($arrRet['data']) ? intval($arrRet['data']) : 0;*/
		
		$forum_id = intval($arrInput['forum_id']);
		$grouper_id  = intval($arrInput['grouper_id']);
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$field = Util_Define::$grouper_priority;
		$cond  = "forum_id=$forum_id and grouper_id=$grouper_id";
		$ret = $db->select('grouper', $field, $cond);
		if($ret === false) {
			Bingo_Log::warning("getGrouperList error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		//不是妹子团的返回错误
		if (empty($ret)) {
			Bingo_Log::warning("grouper is not match group! output: " . serialize($cond));
			return Util_Function::errRet(Tieba_Errcode::ERR_USER_GROUP_NOT_MATCH);		
		}
		$ret  = self::_mgetJoinTime($ret); 
		$user = $ret[0];
		$input = array(
		    "user_id"  => $grouper_id, //用户id
		    "get_icon" => 0
		);
		$res  = Tieba_Service::call('user', 'getUserData', $input, null, null, 'post', 'php', 'utf-8');
		if ($res === false || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( "getUserData fail. [" . serialize ( $input ) . "]" );
			$res['errno'] = isset($res['errno']) ? $res['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
			return Util_Function::errRet($res['errno']);
		}
		$res = $res['user_info'][0];
		$user['user_name'] = $res['user_name'];
		$user['portrait']  = strval(Tieba_Ucrypt::encode($grouper_id, $res['user_name'],$res['portrait_time']));
		//$achievements = isset($res['achievements']['list']) ? $res['achievements']['list'] : array();
		$arrPms = array(
			'user_id' => $grouper_id,
			'category_id' => 1,
		);
		$res_ach = self::_mgetUserAchievement($arrPms);
		if ($res_ach['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( "_mgetUserAchievement fail. [" . serialize ( $res_ach ) . "]" );
			$res_ach['errno'] = isset($res_ach['errno']) ? $res_ach['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
			return Util_Function::errRet($res_ach['errno']);
		}
		$achievements = $res_ach['data'];
		$icon = array();
		$arrPms = array(
			'user_id' => $grouper_id,
			'category_id' => Util_Define::MEIZI_ICON_CATEGORY,
		);
		$res_icon = self::_mgetUserIcon($arrPms);
		if ($res_icon['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( "getUserIcon fail. [" . serialize ( $res_icon ) . "]" );
			$res_icon['errno'] = isset($res_icon['errno']) ? $res_icon['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
			return Util_Function::errRet($res_icon['errno']);
		}
		$icon = isset($res_icon['data']) ? $res_icon['data'] : array();
		
		//get member
		$res_member = self::getThirdGroupers($user);
		if ($res_member === false || $res_member['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( "getUser member fail. [" . serialize ( $user ) . "]" );
			$res_member['errno'] = isset($res_member['errno']) ? $res_member['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
			return Util_Function::errRet($$res_member['errno']);
		}
		$members = $res_member['data'];
		
		$captain = array();
		$leader  = array();
		$others  = array();
		
		if ($user['duty'] == Util_Define::DUTY_TEAM_CAPTAIN) {
			$captain['username'] = $user['user_name'];
			$captain['portrait'] = strval(Tieba_Ucrypt::encode($grouper_id, $user['user_name'],$user['portrait_time']));
			
		} 
		if ($user['duty'] == Util_Define::DUTY_TEAM_LEADER) {
			$leader['username'] = $user['user_name'];
			$leader['portrait'] = strval(Tieba_Ucrypt::encode($grouper_id, $user['user_name'],$user['portrait_time']));
		} else {
		
			//get 团长
			$mem_captain = self::getSecondGroupLeader($user);
			if ($res_member === false || $res_member['errno'] != Tieba_Errcode::ERR_SUCCESS) {
				Bingo_Log::warning ( "get member leader fail. [" . serialize ( $user ) . "]" );
				$res_member['errno'] = isset($res_member['errno']) ? $res_member['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
				return Util_Function::errRet($$res_member['errno']);
			}
			if (isset($mem_captain['data'][0]) && !empty($mem_captain['data'][0])) {
				$members[] = $mem_captain['data'][0];
			}
			
			$member_ids = array();
			foreach ($members as $mem) {
				if (intval($mem['grouper_id']) > 0 ) {
					$member_ids[] = intval($mem['grouper_id']);
				}
			}
		}
		if (!empty($member_ids)) {
			$input = array(
			    "user_id"  => $member_ids,
			);
			$res_member_info = Tieba_Service::call('user', 'mgetUserData', $input, null, null, 'post', 'php', 'utf-8');
			if ($res_member_info === false || $res_member_info['errno'] != Tieba_Errcode::ERR_SUCCESS) {
				Bingo_Log::warning ( "getUserData fail. [" . serialize ( $input ) . "]" );
				$res_member_info['errno'] = isset($res_member_info['errno']) ? $res_member_info['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
				return Util_Function::errRet($res_member_info['errno']);
			}
			$member_info = $res_member_info['user_info'];
			
			foreach ($members as $key => $value) {
				if ($value['duty'] == Util_Define::DUTY_TEAM_CAPTAIN) {				//队长
					//$captain = $value;
					$captain['username'] = $member_info[$value['grouper_id']]['user_name'];
					$captain['portrait'] = strval(Tieba_Ucrypt::encode($value['grouper_id'], $member_info[$value['grouper_id']]['user_name'],$member_info[$value['grouper_id']]['portrait_time']));
				} elseif ($value['duty'] == Util_Define::DUTY_TEAM_LEADER) {			//团长？
					//$leader = $value;
					$leader['username']  = $member_info[$value['grouper_id']]['user_name'];
					$leader['portrait']  = strval(Tieba_Ucrypt::encode($value['grouper_id'], $member_info[$value['grouper_id']]['user_name'],$member_info[$value['grouper_id']]['portrait_time']));
				} else {																//芸芸众生
					//$other = $value;
					$others[] = array(
						'username' => $member_info[$value['grouper_id']]['user_name'],
						'portrait' => strval(Tieba_Ucrypt::encode($value['grouper_id'], $member_info[$value['grouper_id']]['user_name'],$member_info[$value['grouper_id']]['portrait_time'])),
					);
				}
			}
		}	
		$arrParams = array($user);
		$arrRet = self::_mgetForumLevelThreadNum($arrParams);
		if ($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( "_mgetForumLevelThreadNum fail. [" . serialize ( $arrRet ) . "]" );
			$arrRet['errno'] = isset($arrRet['errno']) ? $arrRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
			return Util_Function::errRet($arrRet['errno']);
		}
			
			//perm
			/*$input[] = array(
			    "forum_id" => $user['forum_id'], //吧id
			    "user_id"  => $grouper_id, 		 //用户id
				"user_ip"  => 0,
			);
			$res_perm = Tieba_Service::call('perm', 'getPerm', $input, null, null, 'post', 'php', 'utf-8');
			if ($res_perm === false || $res_perm['errno'] != Tieba_Errcode::ERR_SUCCESS) {
				Bingo_Log::warning ( "getPerm fail. [" . serialize ( $input ) . "]" );
				$res_perm['errno'] = isset($res_perm['errno']) ? $res_perm['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
				return Util_Function::errRet($res_perm['errno']);
			}
			$user['forum_level'] = $res_perm['output']['grade']['level_id'];
			
			//post num
			$res_threads = Tieba_Service::call('post', 'queryUserForumCount', $input, null, null, 'post', 'php', 'utf-8');
			if ($res_threads === false || $res_threads['errno'] != Tieba_Errcode::ERR_SUCCESS) {
				Bingo_Log::warning ( "queryUserForumCount fail. [" . serialize ( $input ) . "]" );
				$res_threads['errno'] = isset($res_threads['errno']) ? $res_threads['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
				return Util_Function::errRet($res_threads['errno']);
			}
			$user['thread_num'] = $res_threads['count']['thread_num'];*/
		
		
		$data = array(
			'user' => $arrRet['data'][0],
			'member' => array(
				'captain' => $captain,
				'leader'  => $leader,
				'members' => $others,
			),
			'achievements' => $achievements,
			'icons' => $icon,
		);
		
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $data);
	}
	
	/**
	 * @brief get USER ICON
	 * @param user_id
	 * @return array
	 **/
	private static function _mgetUserIcon($arrInput)
	{
		if (!isset($arrInput['user_id']) || !isset($arrInput['category_id'])) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		$ret_icon = Tieba_Service::call('icon', 'getUserValidIcon', $arrInput,  null, null, 'post', 'php', 'utf-8');
		if ($ret_icon === false || $ret_icon['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( "getUserIcon fail. [" . serialize ( $ret_icon ) . "]" );
			$ret_icon['errno'] = isset($ret_icon['errno']) ? $ret_icon['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
			return Util_Function::errRet($ret_icon['errno']);
		}
		$result = array();
		if (!empty($ret_icon)) {
			foreach ($ret_icon['data'] as $icon) {
				if (isset($icon['name']) && !empty($icon['name']) && in_array($icon['name'], Util_Define::$meizi_icon_names) === true ) {
					$input = array(
						'name' => strval($icon['name']),
					);
					$ret_pic = Tieba_Service::call('icon', 'getIcon', $input,  null, null, 'post', 'php', 'utf-8');
					if ($ret_pic === false || $ret_pic['errno'] != Tieba_Errcode::ERR_SUCCESS) {
						Bingo_Log::warning ( "getIcon fail. [ input: " . serialize($input) . " output: " . serialize ( $ret_pic ) . "]" );
						$ret_pic['errno'] = isset($ret_pic['errno']) ? $ret_icon['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
						return Util_Function::errRet($ret_pic['errno']);
					}
					
					$icon['pic'] = json_decode($ret_pic['data']['pic'],true);
					$icon['title'] = Bingo_Encode::convert($ret_pic['data']['title'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
					$icon['level'] = $ret_pic['data']['level'];
					$icon['title_url'] = $ret_pic['data']['title_url'];
					$icon['intro']     = Bingo_Encode::convert($ret_pic['data']['intro'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
					$icon['intro_url'] = $ret_pic['data']['intro_url'];
					$result[] = $icon;
				}
			}
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $result);
	}
	
	/**
	 * @brief get USER ICON
	 * @param user_id
	 * @return array
	 **/
	private static function _mgetUserAchievement($arrInput)
	{
		if (!isset($arrInput['user_id']) || !isset($arrInput['category_id'])) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		$ret_icon = Tieba_Service::call('achievement', 'getAchievementsByCategory', $arrInput,  null, null, 'post', 'php', 'utf-8');
		if ($ret_icon === false || $ret_icon['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( "getUserIcon fail. [" . serialize ( $ret_icon ) . "]" );
			$ret_icon['errno'] = isset($ret_icon['errno']) ? $ret_icon['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
			return Util_Function::errRet($ret_icon['errno']);
		}
		$achievement = array();
		foreach ($ret_icon['data'] as $achi) {
			if (isset($achi['user_status']) && $achi['user_status'] == 1) {
				$achievement[] = $achi;
			}
			
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $achievement);
	}
	
	/**
	 * @brief get 入团时间
	 * @param user_id
	 * @return array
	 **/
	private static function _mgetJoinTime($arrInput)
	{
		$intCurTime = strtotime(date('Y-m-d'));
		$arrGrouperInfo = $arrInput;
		foreach ($arrGrouperInfo as $key => &$value) {
			$intStartTime = intval($value['start_time']);
			if ($intCurTime < $intStartTime) {
				$value['total_time'] = 1;
			} else {
				$intDayDiff = $intCurTime-$intStartTime;
				$intDayDiff = ceil(($intDayDiff)/86400) + 1 + $value['time_scores'];
				$value['total_time'] = $intDayDiff;
			}
		}
        
		return $arrGrouperInfo;
	}
	
	/**
	 * @brief get forum level
	 * @param user_id
	 * @return array
	 **/
	private static function _mgetForumLevelThreadNum($arrInput)
	{
		$arrGrouperInfo = $arrInput;
		$arrParams = array();
		foreach ($arrGrouperInfo as $key => &$value) {
			$input = array(
				'user_id'  => $value['grouper_id'],
				'forum_id' => $value['forum_id'],
			);
			$arrParams[] = $input;
			//post->queryUserForumCount
			$res_threads   = Tieba_Service::call('post', 'queryUserForumCount',$input, null, null, 'post', 'php', 'utf-8');
			if ($res_threads === false || $res_threads['errno'] != Tieba_Errcode::ERR_SUCCESS) {
				Bingo_Log::warning ( "queryUserForumCount fail. [" . serialize ( $input ) . "] output : " . serialize($res_threads));
				$res_threads['errno'] = isset($res_threads['errno']) ? $res_threads['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
				return Util_Function::errRet($res_threads['errno']);
			}
			$value['thread_num'] = $res_threads['count']['thread_num'];
		}
		//perm->mgetUserForumLevel
		$res = Tieba_Service::call('perm', 'mgetUserForumLevel', array('req' => $arrParams), null, null, 'post', 'php', 'utf-8');
		if ($res === false || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( "mgetUserForumLevel fail. [" . serialize ( $arrParams ) . "] output : " . serialize($res));
			$res['errno'] = isset($res['errno']) ? $res['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
			return Util_Function::errRet($res['errno']);
		}
		$forum_levels = $res['score_info'];
		foreach ($arrGrouperInfo as $key => &$info) {
			$info['forum_level'] = $forum_levels[$key]['level_id'];
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrGrouperInfo);
	}
	
	/**
	 * @brief get 队员
	 * @param user_id
	 * @return array
	 **/
	public static function getSecondGroupers($arrInput)
	{
		if (!isset($arrInput['forum_id'])  || !isset($arrInput['first_group'])
			|| !isset($arrInput['second_group']) ) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		//DO NOT check permission
		$first_group  = intval($arrInput['first_group']);
		$second_group = intval($arrInput['second_group']);
		$offset = intval($arrInput['offset']);
		$limit  = isset($arrInput['limit']) ? intval($arrInput['limit']) : 11;
		$forum_id = intval($arrInput['forum_id']);
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$field = array('grouper_id','total_scores','duty');
		$cond = "forum_id=$forum_id and first_group=$first_group and second_group=$second_group";
		$append = "order by duty limit $offset,$limit";
		$ret = $db->select('grouper', $field, $cond, null, $append);
		if($ret === false) {
			Bingo_Log::warning("getSecondGroupers List error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $ret);
	}
	
	/**
	 * @brief get 团长
	 * @param user_id
	 * @return array
	 **/
	public static function getSecondGroupLeader($arrInput)
	{
		if (!isset($arrInput['forum_id'])  || !isset($arrInput['first_group']) || !isset($arrInput['second_group'])) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		//DO NOT check permission
		$first_group  = intval($arrInput['first_group']);
		$second_group = intval($arrInput['second_group']);
		$forum_id = intval($arrInput['forum_id']);
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$field = Util_Define::$grouper_priority;
		$cond = "forum_id=$forum_id and first_group=$first_group and second_group=$second_group and duty=" . Util_Define::DUTY_TEAM_LEADER;
		
		$ret = $db->select('grouper', $field, $cond);
		if($ret === false) {
			Bingo_Log::warning("getGrouperList error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $ret);
	}
	
	/**
	 * @brief get 队员
	 * @param user_id
	 * @return array
	 **/
	public static function getThirdGroupers($arrInput)
	{
		if (!isset($arrInput['forum_id'])  || !isset($arrInput['first_group'])
			|| !isset($arrInput['second_group']) || !isset($arrInput['third_group']) ) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		//DO NOT check permission
		$grouper_id  = intval($arrInput['grouper_id']);
		$first_group  = intval($arrInput['first_group']);
		$second_group = intval($arrInput['second_group']);
		$third_group  = intval($arrInput['third_group']);
		$forum_id = intval($arrInput['forum_id']);
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$field = Util_Define::$grouper_priority;
		$cond = "forum_id=$forum_id and first_group=$first_group and second_group=$second_group and third_group=$third_group and grouper_id!=$grouper_id";
		
		$ret = $db->select('grouper', $field, $cond);
		if($ret === false) {
			Bingo_Log::warning("getGrouperList error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $ret);
	}
	
	/**
	 * @brief get group f-1-2-3 index
	 * @param user_id
	 * @return array
	 **/
	public static function getGroupsIndexInfo($arrInput) 
	{
		if (!isset($arrInput['forum_id']) || intval($arrInput['forum_id']) <= 0) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		$forum_id = intval($arrInput['forum_id']);
		$first_group  = intval($arrInput['first_group']);
		$second_group = intval($arrInput['second_group']);
		$third_group  = intval($arrInput['third_group']);
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		
		$cond = "forum_id=$forum_id";
		$group_by = "first_group";
		if ($second_group > 0) {
			$cond .= " and second_group=$second_group and first_group=$first_group";
			$group_by = "third_group";
		} elseif ($first_group > 0)  {
			$cond .= " and first_group=$first_group";
			$group_by = "second_group";
		}
		$append = "group by $group_by";
		$ret  = $db->select('grouper', array($group_by), $cond, null, $append);
		if($ret === false) {
			Bingo_Log::warning("getGroupsIndexInfo error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$res =array();
		foreach ($ret as $value) {
			$res[] = $value[$group_by];
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $res);
	}
	
	
	/**
	 * @brief get grouper
	 * @param grouper_id,forum_id,field
	 * @return array
	 **/
	public static function getGrouperProperity($arrInput) 
	{
		if (!isset($arrInput['grouper_id']) || !isset($arrInput['forum_id']) || intval($arrInput['grouper_id']) <= 0 ) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		$arrField = isset($arrInput['field']) ? $arrInput['field'] : Util_Define::$grouper_priority;
		$grouper_id  = intval($arrInput['grouper_id']);
		$forum_id = intval($arrInput['forum_id']);
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		
		$cond = "forum_id=$forum_id and grouper_id=$grouper_id";
		$res  = $db->select('grouper', $arrField, $cond);
		if($res === false) {
			Bingo_Log::warning("getGrouperProperity error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		if (empty($res) || $db->getAffectedRows() < 1) {
			Bingo_Log::warning("getGrouperProperity empty! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_NO_RECORD);
		}
		//get time
		$res = self::_mgetJoinTime($res);
		//get 发帖量 forum等级,失败不赋值，非重要数据
		$res[0]['forum_id']   = $forum_id;
		$res[0]['grouper_id'] = $grouper_id;
		$ret = self::_mgetForumLevelThreadNum($res);
		if ($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( "_mgetForumLevelThreadNum fail. [ input: " . serialize($res) . " output: " . serialize ( $ret ) . "]" );
			//$arrRet['errno'] = isset($arrRet['errno']) ? $arrRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
			//return Util_Function::errRet($arrRet['errno']);
		} else {
			$res = $ret['data'];
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $res);
	}
	
	/**
	 * @brief get team leader 批量
	 * @param fst_group
	 * @return array
	 **/
	public static function getGroupLeader($arrInput)
	{
		$arrGrouperInfo = $arrInput;
		
		$db = Util_Db::getDB();
		if($db === null) {
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$field = array('grouper_id');
		foreach ($arrGrouperInfo as $key => $value) {
			$forum_id = intval($value['forum_id']);
			$first_group  = intval($value['first_group']);
			$second_group = intval($value['second_group']);
			if ($forum_id <= 0 || $first_group <= 0 || $second_group <= 0) {
				Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
				return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
			}
			$cond  = "forum_id=$forum_id and first_group=$first_group and second_group=$second_group and duty=" . Util_Define::DUTY_TEAM_LEADER;
			$res  = $db->select('grouper', $field, $cond);
			if($res === false) {
				Bingo_Log::warning("getGroupLeader error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
				return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
			}
			$leader_id = intval($res[0]['grouper_id']);
			if (!empty($leader_id)) {
				$input = array(
				    'user_id' => $leader_id, 	//用户id
					'get_icon' => 1
				);
				$res_uname = Tieba_Service::call('user', 'getUserData', $input, null, null, 'post', 'php', 'utf-8');
	    		if( $res_uname === false || $res_uname['errno'] != Tieba_Errcode::ERR_SUCCESS) {
					Bingo_Log::warning("call service user getUserData error. [input=".serialize($input).'][output='.serialize($res_uname).']');
					return Util_Function::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
				}
				$arrGrouperInfo[$key]['leader_name'] = $res_uname['user_info'][0]['user_name'];
				$arrGrouperInfo[$key]['portrait']    = strval(Tieba_Ucrypt::encode($leader_id, $res_uname['user_info'][0]['user_name'],$res_uname['user_info'][0]['portrait_time']));
			}
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$arrGrouperInfo);
	}
	
	/**
	 * @brief get team info in forum page
	 * @param grouper_id,forum_id
	 * @return array
	 **/
	public static function getTeamInfo($arrInput)
	{
		if (!isset($arrInput['grouper_id']) || !isset($arrInput['forum_id'])) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		//check permission
		/*$arrRet = Service_Manager_Manager::authenticationCheck($arrInput);
		$role = isset($arrRet['data']) ? intval($arrRet['data']) : 0;*/
		
		$forum_id = intval($arrInput['forum_id']);
		$grouper_id  = intval($arrInput['grouper_id']);
		$db = Util_Db::getDB();
		if($db === null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$field = Util_Define::$grouper_priority;
		
		$cond  = "forum_id=$forum_id and grouper_id=$grouper_id";
		$ret = $db->select('grouper', $field, $cond);
		if($ret === false) {
			Bingo_Log::warning("getGrouper Info error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		//不是妹子团的返回错误
		if (empty($ret) || $db->getAffectedRows() < 1) {
			Bingo_Log::warning("grouper is not match group! output: " . serialize($cond));
			return Util_Function::errRet(Tieba_Errcode::ERR_USER_GROUP_NOT_MATCH);		
		}
		$arrParams = $ret[0];
		$second_group = intval($ret[0]['second_group']);
		
		//get rank num
		$res_rank = self::_getGroupScoresRankNum($arrParams);
		if ($res_rank === false || $res_rank['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( "_getGroupScoresRankNum fail. [" . serialize ( $arrParams ) . "]" );
			$res_rank['errno'] = isset($res_rank['errno']) ? $res_rank['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
			return Util_Function::errRet($res_rank['errno']);
		}
		$rank_num = $res_rank['data'];
		
		//get member
		$res_member = self::getSecondGroupers($arrParams);
		if ($res_member === false || $res_member['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( "getThirdGroupers fail. [" . serialize ( $res_member ) . "]" );
			$res_member['errno'] = isset($res_member['errno']) ? $res_member['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
			return Util_Function::errRet($$res_member['errno']);
		}
		$members = $res_member['data'];
		$member_ids = array();
		foreach ($members as $mem) {
			$member_ids[] = intval($mem['grouper_id']);
		}
		$input = array(
		    "user_id"  => $member_ids,
		);
		$res_member_info = Tieba_Service::call('user', 'mgetUserData', $input, null, null, 'post', 'php', 'utf-8');
		if ($res_member_info === false || $res_member_info['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( "mgetUserData fail. [" . serialize ( $input ) . "]" );
			$res_member_info['errno'] = isset($res_member_info['errno']) ? $res_member_info['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
			return Util_Function::errRet($res_member_info['errno']);
		}
		$member_info = $res_member_info['user_info'];
		$result = array();
		foreach ($members as $key => &$value) {
			
			$value['username'] = $member_info[$value['grouper_id']]['user_name'];
			$value['portrait'] = strval(Tieba_Ucrypt::encode($value['grouper_id'], $member_info[$value['grouper_id']]['user_name'],$member_info[$value['grouper_id']]['portrait_time']));
			
		}
		$data = array(
			'second_group' => $second_group,
			'rank' => $rank_num,
			'list' => $members,
		);
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $data);
	}
	

	/**
	 * @brief set second group scores,团队总分
	 * @param grouper_id,forum_id
	 * @return array
	 **/
	public static function setGroupScores($arrInput)
	{
		if (!isset($arrInput['grouper_id']) || !isset($arrInput['forum_id'])
			|| intval($arrInput['grouper_id']) <= 0 || intval($arrInput['forum_id']) <= 0) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		$forum_id = intval($arrInput['forum_id']);
		$grouper_id = intval($arrInput['grouper_id']);
		$scores = intval($arrInput['scores']);
		
		$symbol = isset($arrInput['symbol']) ? intval($arrInput['symbol']) : 1;
		
		$db = Util_Db::getDB();
		if($db === null) {
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		
		$field = Util_Define::$grouper_priority;
		$cond  = "forum_id=$forum_id and grouper_id=$grouper_id";
		$ret = $db->select('grouper', $field, $cond);
		if($ret === false) {
			Bingo_Log::warning("getGrouper Info error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$first_group  = intval($ret[0]['first_group']);
		$second_group = intval($ret[0]['second_group']);
		//查询上次更新的月份
		//select last_update_month in groupscores
		$begin_month  = strtotime(date("Y-m-01"));
		$field = array('last_update_month');
		$cond = "forum_id=$forum_id and first_group=$first_group and second_group=$second_group";
		$ret = $db->select('groupscores', $field, $cond);
		if ($ret === false || $db->getAffectedRows() <= 0) {
			Bingo_Log::warning("select groupscores error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		//需要同时更新本月积分和总积分
		$last_month = intval($ret[0]['last_update_month']);
		//加分or扣分
		if ($symbol === 1) {
			if ($begin_month != $last_month) {
				$sql_query = "update groupscores set month_scores=$scores,total_scores=total_scores+$scores,last_update_month=$begin_month where " . $cond;
			} else {
				$sql_query = "update groupscores set month_scores=month_scores+$scores,total_scores=total_scores+$scores where " . $cond;
			}
		} else {
			if ($begin_month != $last_month) {
				$sql_query = "update groupscores set month_scores=0-$scores,total_scores=total_scores-$scores,last_update_month=$begin_month where " . $cond;
			} else {
				$sql_query = "update groupscores set month_scores=month_scores-$scores,total_scores=total_scores-$scores where " . $cond;
			}
		}
		$ret = $db->query($sql_query);
		if ($ret === false ) {
			Bingo_Log::warning("update db error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		} elseif ($db->getAffectedRows() !== 1){
			Bingo_Log::warning("update db fail! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_NO_RECORD);
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	
	/**
	 * @brief set second group scores
	 * @param grouper_id,forum_id
	 * @return array
	 **/
	public static function getSecondGroupScoresRank($arrInput)
	{
		if (!isset($arrInput['grouper_id']) || !isset($arrInput['forum_id']) || intval($arrInput['forum_id']) <= 0 ) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		//缓存开关  上下线开关
		$cache  = isset($arrInput['cache']) ? intval($arrInput['cache']) : 1; 
		$forum_id = intval($arrInput['forum_id']);
		$is_month = isset($arrInput['is_month']) ? intval($arrInput['is_month']) : 0;
		$offset = intval($arrInput['offset']);
		$limit  = isset($arrInput['limit']) ? intval($arrInput['limit']) : 10;
		$grouper_id = intval($arrInput['grouper_id']);
		$ret = array();
		$error = Tieba_Errcode::ERR_SUCCESS;
        //不走缓存
		if ($cache === 0) {
			$ret = self::_getSecondGroupScoresRank($arrInput);
			$data = $ret['data'];
			if($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
				$error = isset($ret['errno']) ? $ret['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
				Bingo_Log::warning("call _getGrouperScoresRank failed.[".serialize($ret)."]");
			}
		} else {
			$key = Util_Define::REDIS_PREFIX . '_team_second_grouper_scores_rank_list_forum_' . $forum_id . "_grouper_id_$grouper_id" . "_scores". "_offset_$offset" . "_limit_$limit";
			if ($is_month == 1) {
				$key .= '_is_month';
			}
			$arrParams = array( 'key' => $key );
			$data = Util_Redis::getFromRedis($arrParams);
			//TTL  todo
			if (empty($ret)) {
				$ret = self::_getSecondGroupScoresRank($arrInput);
			
				if($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
					$error = isset($ret['errno']) ? $ret['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
					Bingo_Log::warning("call _getSecondGroupScoresRank failed.[".serialize($ret)."]");
				} else {
					$arrParams = array(
						'key'   => $key,
						'value' => $ret['data'],
						'expire'=> Util_Define::REDIS_GROUPER_SCORES_RANK,
                	);
					Util_Redis::setToRedis($arrParams);
				}
				$data = $ret['data'];
			}
		}
		return Util_Function::errRet($error, $data);
	}
	
	/**
	 * @brief 成员积分榜, 团里的
	 * @param forum_id
	 * @return array
	 **/
	private static function _getSecondGroupScoresRank($arrInput)
	{
		if (!isset($arrInput['grouper_id']) || !isset($arrInput['forum_id']) || intval($arrInput['forum_id']) <= 0 ) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		//build parameters
		$is_month = isset($arrInput['is_month']) ? intval($arrInput['is_month']) : 0;
		$forum_id = intval($arrInput['forum_id']);
		$grouper_id = intval($arrInput['grouper_id']);
		
		$db = Util_Db::getDB();
		if($db == null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		
		$field = Util_Define::$grouper_priority;
		$cond  = "forum_id=$forum_id and grouper_id=$grouper_id";
		
		$ret = $db->select('grouper', $field, $cond);
		if($ret === false) {
			Bingo_Log::warning("getGrouper Info error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		//不是妹子团的返回错误
		if (empty($ret)) {
			Bingo_Log::warning("grouper is not match group! output: " . serialize($cond));
			return Util_Function::errRet(Tieba_Errcode::ERR_USER_GROUP_NOT_MATCH);		
		}
		$order_by = ($is_month == 1) ? 'month_scores' : 'total_scores';
		$offset = intval($arrInput['offset']);
		$limit  = isset($arrInput['limit']) ? intval($arrInput['limit']) : 10;		//排行榜数量
		
		$first_group  = intval($ret[0]['first_group']);
		$second_group = intval($ret[0]['second_group']);
		//查询排行
		$field = Util_Define::$grouper_priority;
		$cond  = "forum_id=$forum_id and first_group=$first_group and second_group=$second_group";
		if ($is_month == 1) {
			$begin_month  = strtotime(date("Y-m-01"));
			$cond .= " and last_update_month=$begin_month";
		}
		$append = "order by $order_by DESC limit $offset,$limit";
		$ret = $db->select('grouper', $field, $cond, null, $append);
		if($ret === false) {
			Bingo_Log::warning("get Second Grouper Rank List error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		if (empty($ret)) {
			Bingo_Log::warning("get Second Grouper Rank List empty! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, array());
		}
		$ret = self::_mgetUnamesByUid($ret, "grouper_id", "grouper_name");
		
		//时长
		$ret = self::_mgetJoinTime($ret);
		//get 发帖量 forum等级,失败不赋值，非重要数据
		$res = self::_mgetForumLevelThreadNum($ret);
		if ($res === false || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( "_mgetForumLevelThreadNum fail. [" . serialize ( $res ) . "]" );
			//$arrRet['errno'] = isset($arrRet['errno']) ? $arrRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
			//return Util_Function::errRet($arrRet['errno']);
		} else {
			$ret = $res['data'];
		}
		
		//分页
		$total = $db->selectCount('grouper', $cond);
		if ($total === false) {
			Bingo_Log::warning("selectCount error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [input: $offset $limit ] [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$data = array(
			'list' => $ret,
			'page' => array(
				'count'   => $total,
				'pn'      => $offset,
				'total_pn'=> intval(ceil($total/$limit)),
			)
		);
		
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $data);
	}
	
	
	/**
	*@brief 榜单
	*@param forum id
	*@return array
	**/
	public static function getGroupScoresRank($arrInput)
	{
		if (!isset($arrInput['forum_id']) || intval($arrInput['forum_id']) <= 0 ) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		//缓存开关  上下线开关
		$cache  = isset($arrInput['cache']) ? intval($arrInput['cache']) : 1; 
		$forum_id = intval($arrInput['forum_id']);
		$is_month = isset($arrInput['is_month']) ? intval($arrInput['is_month']) : 0;
		$offset = intval($arrInput['offset']);
		$limit  = isset($arrInput['limit']) ? intval($arrInput['limit']) : 10;
		$ret = array();
		$error = Tieba_Errcode::ERR_SUCCESS;
        //不走缓存
		if ($cache === 0) {
			$ret = self::_getGroupScoresRank($arrInput);
			$data = $ret['data'];
			if($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
				$error = isset($ret['errno']) ? $ret['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
				Bingo_Log::warning("call _getGrouperScoresRank failed.[".serialize($ret)."]");
			}
		} else {
			$key = Util_Define::REDIS_PREFIX . '_team_group_scores_rank_list_forum_' . $forum_id . "_offset_$offset" . "_limit_$limit";
			if ($is_month == 1) {
				$key .= '_is_month';
			}
			$arrParams = array( 'key' => $key );
			$data = Util_Redis::getFromRedis($arrParams);
			//TTL  todo
			if (empty($ret)) {
				$ret = self::_getGroupScoresRank($arrInput);
				$data = $ret['data'];
				if($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
					$error = isset($ret['errno']) ? $ret['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
					Bingo_Log::warning("call _getGrouperScoresRank failed.[".serialize($ret)."]");
				} else {
					$arrParams = array(
						'key'   => $key,
						'value' => $data,
						'expire'=> Util_Define::REDIS_GROUPER_SCORES_RANK,
                	);
					Util_Redis::setToRedis($arrParams);
				}
				
			}
		}
		return Util_Function::errRet($error, $data);
	}
	
	/**
	 * @brief 团队积分榜
	 * @param forum_id
	 * @return array
	 **/
	private static function _getGroupScoresRank($arrInput)
	{
		if (!isset($arrInput['forum_id']) || intval($arrInput['forum_id']) <= 0 ) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		$is_month = isset($arrInput['is_month']) ? intval($arrInput['is_month']) : 0;
		$db = Util_Db::getDB();
		if($db == null){
			Bingo_Log::warning("DB link fail.");
			$errno = Tieba_Errcode::ERR_DB_CONN_FAIL;
			return Util_Function::errRet($errno);
		}
		$order_by = ($is_month == 1) ? 'month_scores' : 'total_scores';
		$offset = intval($arrInput['offset']);
		$limit  = isset($arrInput['limit']) ? intval($arrInput['limit']) : 10;		//排行榜数量
		$forum_id = intval($arrInput['forum_id']);
		$is_month = isset($arrInput['is_month']) ? intval($arrInput['is_month']) : 0;
		$field = Util_Define::$group_scores_priority;
		$cond  = "forum_id=$forum_id and $order_by>0";
		if ($is_month == 1) {
			$begin_month  = strtotime(date("Y-m-01"));
			$cond .= " and last_update_month=$begin_month";
		}
		$append = "order by $order_by DESC limit $offset,$limit";
		$ret = $db->select('groupscores', $field, $cond, null, $append);
		if($ret === false) {
			Bingo_Log::warning("get Group Rank List error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		if (empty($ret)) {
			Bingo_Log::warning("get Group Rank List empty! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, array('list' => array(), 'page' => array('count' => 0)));
		}
		//$ret = self::_mgetUnamesByUid($ret, "grouper_id", "grouper_name");
		//get团长
		$ret_leader = self::getGroupLeader($ret);
		if ($ret_leader['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( "getGroupLeader fail. input: [" . serialize ( $ret ) . "] output: " . serialize ( $ret_leader ));
			return $ret_leader;
		}
		$ret = $ret_leader['data'];
		
		//分页
		$total = $db->selectCount('groupscores', $cond);
		if ($total === false) {
			Bingo_Log::warning("selectCount error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [input: $offset $limit ] [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$data = array(
			'list' => $ret,
			'page' => array(
				'count'   => $total,
				'pn'      => $offset,
				'total_pn'=> intval(ceil($total/$limit)),
			)
		);
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $data);
	}
	
	/**
	 * @brief 团队积分数
	 * @param forum_id
	 * @return array
	 **/
	private static function _getGroupScoresRankNum($arrInput)
	{
		if (!isset($arrInput['forum_id']) || intval($arrInput['forum_id']) <= 0 ) {
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		$db = Util_Db::getDB();
		if($db == null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$is_month = isset($arrInput['is_month']) ? intval($arrInput['is_month']) : 1;
		$order_by = ($is_month === 1) ? 'month_scores' : 'total_scores';
		$offset = intval($arrInput['offset']);
		$limit  = isset($arrInput['limit']) ? intval($arrInput['limit']) : 10;		//排行榜数量
		$forum_id = intval($arrInput['forum_id']);
		$first_group  = intval($arrInput['first_group']);
		$second_group = intval($arrInput['second_group']);
		$field = array($order_by);
		$cond  = "forum_id=$forum_id and first_group=$first_group and second_group=$second_group";
		if ($is_month == 1) {
			$begin_month  = strtotime(date("Y-m-01"));
			$cond .= " and last_update_month=$begin_month";
		}
		$ret = $db->select('groupscores', $field, $cond);
		if($ret === false) {
			Bingo_Log::warning("get Grouper Rank List error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$scores = intval($ret[0][$order_by]);
		//拿排第几
		$cond = "forum_id=$forum_id and first_group=$first_group and $order_by>$scores";
		if ($is_month == 1) {
			$cond .= " and last_update_month=$begin_month";
		}
		$total = $db->selectCount('groupscores', $cond);
		if ($total === false) {
			Bingo_Log::warning("selectCount error! sql: ".$db->getLastSQL() . ' affect_num: ' . $db->getAffectedRows()." [input: $offset $limit ] [output: ".serialize($db->error())."]");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$data = $total+1;
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $data);
	}


	/**
	 * @brief insert grouper into group team,队长创建团队
	 * @param user_id
	 * @return array
	 **/
	public static function addGrouperForLeader($arrInput)
	{
		if (
			!isset($arrInput['first_group']) || !isset($arrInput['second_group'])
			|| !isset($arrInput['third_group']) || !isset($arrInput['grouper_id'])|| !isset($arrInput['duty'])
			)
		{
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		if($arrInput['is_transaction']){
			if(!isset($arrInput['db'])){
				Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
				return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
			}
			$db = $arrInput['db']; //已存在事务，后面将不再开启事务，如果重新开启事务，已存在的事务则会自动提交
		} else {
			$db = Util_Db::getDB();
		}

		if($db == null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}

		$errno = Tieba_Errcode::ERR_SUCCESS;



		$first_group  = intval($arrInput['first_group']);
		$second_group = intval($arrInput['second_group']);
		$third_group  = intval($arrInput['third_group']);
		$duty         = isset($arrInput['duty']) ? intval($arrInput['duty']) : Util_Define::DUTY_TEAM_MEMBER;
		$grouper_id = intval($arrInput['grouper_id']);
		$time  = time();
		$forum_id = intval($arrInput['forum_id']);

		//start transaction
		do {
			if(!$arrInput['is_transaction']){
				$db->startTransaction();
			}

			$flag = 0;
			$field = array(
				'grouper_id'   => $grouper_id,
				'first_group'  => $first_group,
				'second_group' => $second_group,
				'third_group'  => $third_group,
				'duty'         => $duty,
				'forum_id'     => $forum_id,
			);
			$arrRet = $db->insert('grouper', $field);
			if($arrRet === false || $db->getAffectedRows() <= 0) {
				Bingo_Log::warning("insert grouper fail! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
				$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				Bingo_Log::pushNotice("insert_user_error",1);
				$flag = 1;
				break;
			}

			if ($flag == 1) {
				break;
			}


			if (!$arrInput['is_transaction'] && !$db->commit()) {
				Bingo_Log::warning("commit db error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows());
				$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				break;
			}


		} while(0);
		if( $errno != Tieba_Errcode::ERR_SUCCESS ){
			Bingo_Log::pushNotice('transaction_run',0);
			if(!$arrInput['is_transaction']){
				$tmp = $db->rollback();
				Bingo_Log::warning("db->rollback. [".serialize($tmp)."]");
				Bingo_Log::pushNotice("rollback",1);
			}
			return Util_Function::errRet($errno);
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
	}


	/**
	 * @brief insert grouper into group team,队员加入团队
	 * @param user_id
	 * @return array
	 **/
	public static function addGrouperForUser($arrInput)
	{
		if (
			!isset($arrInput['first_group']) || !isset($arrInput['second_group'])
			|| !isset($arrInput['third_group']) || !isset($arrInput['grouper_id'])|| !isset($arrInput['duty'])
			)
		{
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}

		if(intval($arrInput['first_group']) != Service_Grouper_Group::MOVIE_GROUP){
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}

		$db = Util_Db::getDB();
		if($db == null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}

		$errno = Tieba_Errcode::ERR_SUCCESS;


		$first_group  = intval($arrInput['first_group']);
		$second_group = intval($arrInput['second_group']);
		$third_group  = intval($arrInput['third_group']);
		$duty         = isset($arrInput['duty']) ? intval($arrInput['duty']) : Util_Define::DUTY_TEAM_MEMBER;
		$grouper_id = intval($arrInput['grouper_id']);

		//start transaction
		do {
			$db->startTransaction();

			$flag = 0;
			$field = array(
				'grouper_id'   => $grouper_id,
				'first_group'  => $first_group,
				'second_group' => $second_group,
				'third_group'  => $third_group,
				'duty'         => $duty,
				'forum_id'     => Service_Grouper_Group::genForumId($first_group,$second_group),
			);
			$arrRet = $db->insert('grouper', $field);
			if($arrRet === false || $db->getAffectedRows() <= 0) {
				Bingo_Log::warning("insert grouper fail! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
				$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				$flag = 1;
				break;
			}

			$arrGroupInput = array(
				'id' => $third_group,
				'db' => $db,
				'is_transaction' => true,
			);
			$arrRet = Service_Grouper_Group::updateGroupLeftNum($arrGroupInput);
			if($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ) {
				Bingo_Log::warning("updateGroupLeftNum  fail! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
				$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				$flag = 1;
				break;
			}

			if ($flag == 1) {
				break;
			}


			if (!$db->commit()) {
				Bingo_Log::warning("commit db error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows());
				$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				break;
			}


		} while(0);
		if( $errno != Tieba_Errcode::ERR_SUCCESS ){
			Bingo_Log::pushNotice('transaction_run',0);
			$tmp = $db->rollback();
			Bingo_Log::warning("db->rollback. [".serialize($tmp)."]");
			Bingo_Log::pushNotice("rollback",1);
			return Util_Function::errRet($errno);
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
	}

	/**
	 * @brief insert grouper into group team,队员退出团队
	 * @param user_id
	 * @return array
	 **/
	public static function quitGrouperForUser($arrInput)
	{
		if (
			!isset($arrInput['first_group']) || !isset($arrInput['second_group'])
			|| !isset($arrInput['third_group']) || !isset($arrInput['grouper_id'])|| !isset($arrInput['duty'])
			)
		{
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}

		$db = Util_Db::getDB();
		if($db == null){
			Bingo_Log::warning("DB link fail.");
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}

		$errno = Tieba_Errcode::ERR_SUCCESS;


		$first_group  = intval($arrInput['first_group']);
		$second_group = intval($arrInput['second_group']);
		$third_group  = intval($arrInput['third_group']);
		$grouper_id = intval($arrInput['grouper_id']);
		$id = intval($arrInput['id']);

		//start transaction
		do {
			$db->startTransaction();
			$flag = 0;

			$arrGroupSelect = array(
				'id' => $third_group,
				);
			$arrRet = Service_Grouper_Group::findGroupInfo($arrGroupSelect);
	      	if($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
	            $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
	            break;
	        }

        	$group = $arrRet['data'];
        	if ($group['status'] != Service_Grouper_Group::GROUP_CREATE_STATUS){
        		Bingo_Log::warning(' error status');
        		$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
	            break;
        	}

			$cond = ' grouper_id = '.$grouper_id .' and duty = '.Util_Define::DUTY_TEAM_MEMBER .' and  first_group = '.$first_group.' and second_group = '.$second_group .' and third_group = '.
			    $third_group;
			$arrRet = $db->delete('grouper', $cond);
			if($arrRet === false || $db->getAffectedRows() <= 0) {
				Bingo_Log::warning("insert grouper fail! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
				$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				$flag = 1;
				break;
			}

			$arrGroupInput = array(
				'id' => $third_group,
				'db' => $db,
				'is_transaction' => true,
				'is_add' => true,
			);
			$arrRet = Service_Grouper_Group::updateGroupLeftNum($arrGroupInput);
			if($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ) {
				Bingo_Log::warning("updateGroupLeftNum  fail! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
				$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				$flag = 1;
				break;
			}

			if ($flag == 1) {
				break;
			}


			if (!$db->commit()) {
				Bingo_Log::warning("commit db error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows());
				$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
				break;
			}

		} while(0);
		if( $errno != Tieba_Errcode::ERR_SUCCESS ){
			Bingo_Log::pushNotice('transaction_run',0);
			$tmp = $db->rollback();
			Bingo_Log::warning("db->rollback. [".serialize($tmp)."]");
			Bingo_Log::pushNotice("rollback",1);
			return Util_Function::errRet($errno);
		}
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
	}

	/**
	 * @brief insert grouper into group team,查询团队成员
	 * @param user_id
	 * @return array
	 **/
	public static function findGrouperByGroup($arrInput)
	{
		if (
			!isset($arrInput['first_group']) || !isset($arrInput['second_group'])
			|| !isset($arrInput['third_group'])
			)
		{
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}

		
//		$first_group  = intval($arrInput['first_group']);
//		$second_group = intval($arrInput['second_group']);
//		$third_group  = intval($arrInput['third_group']);


		$cond = array();
        foreach(self::$Grouper_Model as $key){
            if(isset($arrInput[$key])){
                $cond[$key.' = '] = $arrInput[$key];
            }
        }

        $field = array(
        	'grouper_id',
        	);

        $arrDbInput = array(
            'table' => 'grouper',
            'field' => $field,
            'cond' => $cond,
        );

        $arrRet = Util_Db::select($arrDbInput);

        if($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(printf(__METHOD__." fail,  input = %s output = %s",serialize($arrDbInput),serialize($arrRet)));
            return Util_Function::errRet($arrRet['errno']);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRet['data']);
		//start transaction
		
	}

	/**
	 * @brief 查询团队T码
	 * @param user_id
	 * @return array
	 **/
	public static function findGrouper($arrInput)
	{
		if (
			!isset($arrInput['first_group']) || !isset($arrInput['second_group'])
			|| !isset($arrInput['grouper_id'])
		)
		{
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		$output = array();
		$third_group = intval($arrInput['third_group']);
		unset($arrInput['third_group']);
		$cond = array();
		foreach(self::$Grouper_Model as $key){
			if(isset($arrInput[$key])){
				$cond[$key.' = '] = $arrInput[$key];
			}
		}

		$arrDbInput = array(
			'table' => 'grouper',
			'field' => self::$Grouper_Model,
			'cond' => $cond,
		);
		Bingo_Timer::start('grouper');
		$arrRet = Util_Db::select($arrDbInput);
		Bingo_Timer::end('grouper');

		if($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS || empty($arrRet['data'])){
			Bingo_Log::warning(__METHOD__." fail,  input =  ".serialize($arrDbInput)." output = " .serialize($arrRet));
//			return Util_Function::errRet($arrRet['errno']);
			//当前用户没有组队，查询指定队伍的信息
			$group_id = $third_group;
		} else {
			$grouper = $arrRet['data']['0'];
			$output['grouper'] = $grouper;
			$group_id =  intval($grouper['third_group']);
		}



		$arrGroupInput = array(
			'id' => $group_id ,
		);
		Bingo_Timer::start('group');
		$arrGroupRet = Service_Grouper_Group::findGroupInfo($arrGroupInput);
		Bingo_Timer::end('group');

		if($arrGroupRet == false || $arrGroupRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning(__METHOD__." select group  fail,  input = ".serialize($arrGroupInput)." output =  " .serialize($arrGroupRet));
			return Util_Function::errRet($arrRet['errno']);
		}
		$group = $arrGroupRet['data'];
		$output['group'] = $group;


		$arrCinemaInput = array(
			'id' => $group['cinema_id'],
		);
		Bingo_Timer::start('cinema');
		$arrCinemaRet = Service_Movie_Cinema::findCinema($arrCinemaInput);
		Bingo_Timer::end('cinema');

		if($arrCinemaInput == false || $arrCinemaInput['errno'] != Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning(__METHOD__." select cinema  fail,  input = ".serialize($arrCinemaInput)." output = ". serialize($arrCinemaRet));
			return Util_Function::errRet($arrRet['errno']);
		}
		$cinema = $arrCinemaRet['data'];
		$cinema['city'] = Service_Movie_City::getCity($cinema['city_id'])['data'];
		$cinema['cinema_type'] = Service_Movie_CinemaType::getCinemaType($cinema['cinemae_type'])['data'];
		$output['cinema'] = $cinema;

		if($group['status'] == Service_Grouper_Group::GROUP_TBKEY_STATUS){

			$arrTbkeyInput = array(
				'activity_id' => intval($cinema['tbkey_id']),
				'user_id' => intval($arrInput['grouper_id']),
			);
			Bingo_Timer::start('tbkey');
			$arrTbkeyRet = Tieba_Service::call("tbkey","acquireActivationKey",$arrTbkeyInput,null, null, 'post','php', 'utf-8');
			Bingo_Timer::end('tbkey');

			if($arrTbkeyRet == false || $arrTbkeyRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning(__METHOD__." tbkey call  fail,  input =   ".serialize($arrTbkeyInput)." output = ".serialize($arrTbkeyRet));
			}
			$output['tbkey'] = $arrTbkeyRet['data'];
		}

		if(intval($group['status']) == Service_Grouper_Group::GROUP_COMFRIM_STATUS
			|| intval($group['status']) == Service_Grouper_Group::GROUP_TBKEY_STATUS
			|| intval($group['status']) == Service_Grouper_Group::GROUP_TBKEY_FAIL_STATUS
		){
			$duty = $grouper['duty'];
			if($duty == Util_Define::DUTY_TEAM_LEADER){
				$arrIconInput = array(
					'name' => 'duizhangyinji',
					'level' => 1,
					'user_id' => intval($arrInput['grouper_id']),
				);
			}

			if($duty == Util_Define::DUTY_TEAM_MEMBER){
				$arrIconInput = array(
					'name' => 'duiyuanyinji',
					'level' => 1,
					'user_id' => intval($arrInput['grouper_id']),

				);
			}
			Bingo_Timer::start('icon');
			$arrIconRet = Tieba_Service::call("icon","setUserIcon",$arrIconInput,null, null, 'post','php', 'utf-8');
			Bingo_Timer::end('icon');

			if($arrIconRet != false && $arrIconRet['errno'] == Tieba_Errcode::ERR_SUCCESS ){
				$output['icon'] = array(
					'is_icon' => true,
				);
			}else{
				Bingo_Log::warning(__METHOD__." icon call fail,  input =  ".serialize($arrIconInput)." output = " .serialize($arrIconRet));
			}
		}


		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$output);

	}

	/**
	 * @param $arrInput
	 * @return array
	 */
	public static function setTeamIcon($arrInput){

		if (
			!isset($arrInput['first_group']) || !isset($arrInput['second_group'])
			|| !isset($arrInput['grouper_id'])
		)
		{
			Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		$output = array();

		$cond = array();
		foreach(self::$Grouper_Model as $key){
			if(isset($arrInput[$key])){
				$cond[$key.' = '] = $arrInput[$key];
			}
		}

		$arrDbInput = array(
			'table' => 'grouper',
			'field' => self::$Grouper_Model,
			'cond' => $cond,
		);

		$arrRet = Util_Db::select($arrDbInput);

		if($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS || empty($arrRet['data'])){
			Bingo_Log::warning(__METHOD__." fail,  input =  ".serialize($arrDbInput)." output = " .serialize($arrRet));
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$grouper = $arrRet['data']['0'];
		$output['grouper'] = $grouper;

		$group_id = intval($grouper['third_group']);
		$arrGroupInput = array(
			'id' => $group_id ,
		);
		$arrGroupRet = Service_Grouper_Group::findGroupInfo($arrGroupInput);
		if($arrGroupRet == false || $arrGroupRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning(__METHOD__." select group  fail,  input = ".serialize($arrGroupInput)." output =  " .serialize($arrGroupRet));
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$group = $arrGroupRet['data'];
		$output['group'] = $group;


		if(intval($group['status']) == Service_Grouper_Group::GROUP_COMFRIM_STATUS
			|| intval($group['status']) == Service_Grouper_Group::GROUP_TBKEY_STATUS
			|| intval($group['status'] == Service_Grouper_Group::GROUP_TBKEY_FAIL_STATUS)
		){
			Bingo_Log::warning(__METHOD__." err param ,  input =  ". serialize($group));
			$duty = $grouper['duty'];
			if($duty == Util_Define::DUTY_TEAM_LEADER){
				$arrIconInput = array(
					'name' => 'duizhangyinji',
					'level' => 1,
					'user_id' => intval($arrInput['grouper_id']),
				);
			}

			if($duty == Util_Define::DUTY_TEAM_MEMBER){
				$arrIconInput = array(
					'name' => 'duiyuanyinji',
					'level' => 1,
					'user_id' => intval($arrInput['grouper_id']),
				);
			}

			$arrIconRet = Tieba_Service::call("icon","setUserIcon",$arrIconInput,null, null, 'post','php', 'utf-8');

			if($arrIconRet == false || $arrIconRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
				Bingo_Log::warning(__METHOD__." icon call fail,  input =  ".serialize($arrIconInput)." output = " .serialize($arrIconRet));
				return Util_Function::errRet($arrIconRet['errno']);
			}
			$output['icon'] = $arrIconRet['data'];
		}else {
			return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR,null,"请联系队长" );

		}



		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$output);
	}




}
