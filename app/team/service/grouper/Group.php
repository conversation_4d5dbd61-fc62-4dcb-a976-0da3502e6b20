<?php
/**
 * Created by PhpStorm.
 * User: wangjunsheng
 * Date: 2016/5/18
 * Time: 17:17
 */

define('BINGO_ENCODE_LANG','UTF-8');
class Service_Grouper_Group {



    const MEIZI_GROUP = 1; //妹子团
    const SHOUHU_GROUP =2 ; // 守护团
    const MOVIE_GROUP =3; //观影团

    const GROUP_INIT_STATUS = 0;
    const GROUP_CREATE_STATUS = 1;
    const GROUP_COMFRIM_STATUS = 2;
    const GROUP_DEL_STATUS = 3;
    const GROUP_TBKEY_STATUS = 4;
    const GROUP_TBKEY_FAIL_STATUS = 5;



    const GROUP_TABLE = 'groupscores';

    const DEFAULT_FORUM_INDEX = 1000000000; //非吧绑定的队伍时，生成吧ID规则

    private static $Group_Model = array(
        'id', //自增ID
        'first_group',//第一分组
        'second_group',//第二分组
        'forum_id',//吧ID，不绑定吧，使用0
        'month_scores',//
        'total_scores',//
        'last_update_month',//
        'cinema_id',//影院ID
        'movie_id',//电影ID
        'status',//队伍状态
        'user_id',//队长ID
        'max_team_num',//队伍人数
        'left_team_num',//剩余人数
    );


    /**
     * @brief 创建团、队或者组
     * @param $arrInput
     * @return array
     */
    public static function insertGroup($arrInput){

        if(isset($arrInput['first_group']) && !empty($arrInput['first_group'])){
            $first_group = intval($arrInput['first_group']);

            switch($first_group){
                case self::MEIZI_GROUP :

                case self::SHOUHU_GROUP :
                    // todo 待补充

                    break;

                case self::MOVIE_GROUP :
                    if (empty($arrInput['cinema_id'])|| empty($arrInput['user_id'])
                        || empty($arrInput['second_group']) || empty($arrInput['team_type']) ){
                        Bingo_Log::warning(__METHOD__." error param . input : " . serialize($arrInput));
                        return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
                    }

                    $team_conf = Service_Movie_MovieTeam::getTeamConf(intval($arrInput['team_type']));
                    if($team_conf == false || $team_conf['errno'] != Tieba_Errcode::ERR_SUCCESS ){
                        Bingo_Log::warning(__METHOD__." error team . input : " . serialize($arrInput));
                        return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
                    }

                    $arrInput['max_team_num'] = $team_conf['data']['team_max'];
                    $arrInput['left_team_num'] = $team_conf['data']['team_max'];
                    $arrInput['duty'] =  Util_Define::DUTY_TEAM_LEADER; //创建group，当前用户默认是队长
                    $arrInput['forum_id'] = self::genForumId($first_group,$arrInput['second_group']); //不绑定吧，根据规则生成吧ID
                    break;
                default:
                    Bingo_Log::warning(__METHOD__." error first_group . input : " . serialize($arrInput));
                    return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
        }else {
            Bingo_Log::warning(__METHOD__." error first_group . input : " . serialize($arrInput));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);

        }

        $field = array();
        foreach(self::$Group_Model as $key){
            if(isset($arrInput[$key])){
                $field[$key] = $arrInput[$key];
            }
        }

        if(!empty($field))
        {

            $arrDbInput = array(
                'table' => self::GROUP_TABLE,
                'field' => $field,
                'raw_param' => $arrInput,
            );
            $arrRes = self::insertGroupAndGrouper($arrDbInput);
        }
        else
        {
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if($arrRes == false || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__METHOD__.serialize($arrDbInput).serialize($arrRes));
            return Util_Function::errRet($arrRes['errno']);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);

    }


    /**
     * @brief 更新队伍
     * @param $arrInput
     * @return array
     */
    public static function updateGroup($arrInput){
        if(empty($arrInput['id'])){
            Bingo_Log::warning(__METHOD__." error param . input : " . serialize($arrInput));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $field = array();
        foreach(self::$Group_Model as $key){
            if(isset($arrInput[$key])){
                $field[$key.' = '] = $arrInput[$key];
            }
        }

        $cond = array(
            'id = ' => intval($arrInput['id']),
        );


        $arrDbInput = array(
            'table' => self::GROUP_TABLE,
            'field' => $field,
            'cond' => $cond,
        );

        $arrRet = Util_Db::update($arrDbInput);
        if($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(printf(__METHOD__." find  fail,  input = %s output = %s",serialize($arrDbInput),serialize($arrRet)));
            return Util_Function::errRet($arrRet['errno']);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);

    }

    /**
     * @brief 查询队伍信息
     * @param $arrInput
     * @return array
     */
    public static function findGroupInfo($arrInput){
        if(empty($arrInput['id'])){
            Bingo_Log::warning(__METHOD__." error param . input : " . serialize($arrInput));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

//        $cond = array(
//            'id = ' => intval($arrInput['id']),
//        );

        $cond = array();
        foreach(self::$Group_Model as $key){
            if(isset($arrInput[$key])){
                $cond[$key.' = '] = $arrInput[$key];
            }
        }

        $arrDbInput = array(
            'table' => self::GROUP_TABLE,
            'field' => self::$Group_Model,
            'cond' => $cond,
        );

        $arrRet = Util_Db::select($arrDbInput);

        if($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(printf(__METHOD__." fail,  input = %s output = %s",serialize($arrDbInput),serialize($arrRet)));
            return Util_Function::errRet($arrRet['errno']);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRet['data']['0']);
    }


    /**
     * @brief 创建者删除自己的队伍
     * @param $arrInput
     * @return array
     */
    public static function delGroup($arrInput){
        if(empty($arrInput['id']) || empty($arrInput['user_id'])){
            Bingo_Log::warning(printf(__METHOD__." fail,  input = %s ",serialize($arrInput)));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrRet = self::findGroupInfo($arrInput);

        if($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(printf(__METHOD__." find  fail,  input = %s output = %s",serialize($arrInput),serialize($arrRet)));
            return Util_Function::errRet($arrRet['errno']);
        }

        $group = $arrRet['data'];
        if(intval($group['status']) != self::GROUP_CREATE_STATUS){
            Bingo_Log::warning(__METHOD__." error status , del fail ");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $db = Util_Db::getDB();
        if($db == null){
            Bingo_Log::warning("DB link fail.");
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $id = intval($arrInput['id']);
        $user_id = intval($arrInput['user_id']);
        $third_group = intval($group['id']);

        $errno = Tieba_Errcode::ERR_SUCCESS;
        do {
            $db->startTransaction();

//            $cond = "  id = $id and user_id = $user_id ";
            $update_sql = " update ".self::GROUP_TABLE ." set status = " . self::GROUP_DEL_STATUS ." where id = ".$id ." and user_id = " .$user_id ;
            $arrRet = $db->query($update_sql);

            if($arrRet === false || $db->getAffectedRows() !== 1) {
                Bingo_Log::warning("delete ".self::GROUP_TABLE. " fail! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                Bingo_Log::pushNotice("insert_user_error",1);
                break;
            }

            $third_group = $third_group;
            $first_group = intval($group['first_group']);
            $second_group = intval($group['second_group']);
            $forum_id = $group['forum_id'];


            switch($first_group){
                case self::MOVIE_GROUP :
                    $cond = "  third_group = " .$third_group. " and second_group = " .$second_group ."  and first_group = " .$first_group ." and forum_id = ". $forum_id;
                    $arrRet = $db->delete(" grouper ", $cond);
                    if($arrRet['errno'] !=  Tieba_Errcode::ERR_SUCCESS){
                        Bingo_Log::warning(" delete fail !" .$db->getLastSQL());
                        $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    }
                    break;
            }


            if ($errno == Tieba_Errcode::ERR_SUCCESS && !$db->commit()) {
                Bingo_Log::warning("commit db error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows());
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
        } while(0);

        if( $errno != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::pushNotice('transaction_run',0);
            $tmp = $db->rollback();
            Bingo_Log::warning("db->rollback. [".serialize($tmp)."]");
            Bingo_Log::pushNotice("rollback",1);
            return Util_Function::errRet($errno);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);

    }


    /**
     * @brief 队长创建队伍并同时更新队伍列表
     * @param $arrInput
     * @return array
     */
    private static  function insertGroupAndGrouper($arrInput){
        $db = Util_Db::getDB();
        if($db == null){
            Bingo_Log::warning("DB link fail.");
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        do {
            $db->startTransaction();

            $arrRet = $db->insert($arrInput['table'], $arrInput['field']);
            if($arrRet === false || $db->getAffectedRows() <= 0) {
                Bingo_Log::warning("insert grouper fail! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                Bingo_Log::pushNotice("insert_user_error",1);
                break;
            }

            $group = $arrInput['field'];
            $arr_raw_param = $arrInput['raw_param'];
            $first_group = intval($group['first_group']);
            $second_group = intval($group['second_group']);
            $third_group = $db->getInsertID();
            $duty = $arr_raw_param['duty'];
            $forum_id = $arr_raw_param['forum_id'];

            switch($first_group){
                case self::MOVIE_GROUP :
                    $arrGrouperInput = array(
                        'grouper_id'   => intval($group['user_id']),
                        'first_group'  => intval($group['first_group']),
                        'second_group' => $second_group,
                        'third_group'  => $third_group,
                        'duty'         => $duty,
                        'db'           => $db,
                        'forum_id'     => $forum_id,
                        'is_transaction' => true,
                    );
                    $arrRet = Service_Grouper_Grouper::addGrouperForLeader($arrGrouperInput);
                    if($arrRet['errno'] !=  Tieba_Errcode::ERR_SUCCESS){
                        $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                        break;
                    }

                    $arrDbInput = array(
                        'id' => $third_group,
                        'status' => self::GROUP_CREATE_STATUS,
                        'db'           => $db,
                        'is_transaction' => true,
                    );

                    $arrRet = self::updateGroup($arrDbInput);
                    if($arrRet['errno'] !=  Tieba_Errcode::ERR_SUCCESS){
                        $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                        break;
                    }

                    $arrDbInput = array(
                        'id' => $third_group,
                        'db'           => $db,
                        'is_transaction' => true,
                    );
                    $arrRet = self::updateGroupLeftNum($arrDbInput);
                    if($arrRet['errno'] !=  Tieba_Errcode::ERR_SUCCESS){
                        $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                        break;
                    }



                    break;
            }


            if ($errno == Tieba_Errcode::ERR_SUCCESS && !$db->commit()) {
                Bingo_Log::warning("commit db error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows());
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
        } while(0);

        if( $errno != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::pushNotice('transaction_run',0);
            $tmp = $db->rollback();
            Bingo_Log::warning("db->rollback. [".serialize($tmp)."]");
            Bingo_Log::pushNotice("rollback",1);
            return Util_Function::errRet($errno);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);

    }

    /**
     * @brief 更新队伍剩余人数,默认减少队员数量，使用is_add可以增加队伍数量
     * @param user_id
     * @return array
     **/
    public static function updateGroupLeftNum($arrInput)
    {
        if (!isset($arrInput['id']))
        {
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        if($arrInput['is_transaction']){
            if(!isset($arrInput['db'])){
                Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
                return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
            }
            $db = $arrInput['db']; //已存在事务，后面将不再开启事务，如果重新开启事务，已存在的事务则会自动提交
        } else {
            $db = Util_Db::getDB();
        }

        if($db == null){
            Bingo_Log::warning("DB link fail.");
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        $id = intval($arrInput['id']);

        //start transaction
        do {
            if(!$arrInput['is_transaction']){
                $db->startTransaction();
            }

            $flag = 0;
            if($arrInput['is_add']){
                 $update_sql = " update " . self::GROUP_TABLE . " set left_team_num = left_team_num + 1 where id = $id and left_team_num < max_team_num and status = ".self::GROUP_CREATE_STATUS;
            } else {
                 $update_sql = " update " . self::GROUP_TABLE . " set left_team_num = left_team_num - 1 where id = $id and left_team_num >= 1 and status = " .self::GROUP_CREATE_STATUS;
            }
           
            $arrRet = $db->query($update_sql);

            if($arrRet === false || $db->getAffectedRows() <= 0) {
                Bingo_Log::warning("update " .self::GROUP_TABLE."  fail! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows()." [output:".serialize($db->error())."]");
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                $flag = 1;
                break;
            }

            if ($flag == 1) {
                break;
            }


            if (!$arrInput['is_transaction'] && !$db->commit()) {
                Bingo_Log::warning("commit db error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows());
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }


        } while(0);
        if( $errno != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::pushNotice('transaction_run',0);
            if(!$arrInput['is_transaction']){
                $tmp = $db->rollback();
                Bingo_Log::warning("db->rollback. [".serialize($tmp)."]");
                Bingo_Log::pushNotice("rollback",1);
            }
            return Util_Function::errRet($errno);
        }

        $arrGroupRet = self::findGroupInfo($arrInput);
        if($arrGroupRet != false && $arrGroupRet['errno'] == Tieba_Errcode::ERR_SUCCESS){
            $group = $arrGroupRet['data'];
            if($group['left_team_num'] == 0){
                $arrNmqInput = array(
                    'leader_msg_type' => Service_Movie_Msg::CONFIRM_LEADER,
                    'second_group' => $group['second_group'],
                    'user_id' => $group['user_id'],
                );
                Util_Nmq::commitAction('Service_Movie_Msg','sendLeader',$arrNmqInput);
            }
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
    * @brief 队长确认队伍状态
    *   
    */
    public static function confrimGroup($arrInput){
        if (!isset($arrInput['id']) || !isset($arrInput['user_id'])){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //todo 检查队伍剩余人数和已报名的团队人数
        $ret = self::findGroupInfo($arrInput);
        if($ret == false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__METHOD__." find  fail ".serialize($arrInput));
            return Util_Function::errRet($ret['errno']);
        }

        $group = $ret['data'];

        if(empty($group)){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        if($group['left_team_num'] > 0){
            Bingo_Log::warning(__METHOD__." left_team_num > 0 ".serialize($arrInput));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR,'left_team_num > 0');
        }

//        $arrGroup  = array(
//            'id' =>  intval($arrInput['id']),
//            'status' => self::GROUP_COMFRIM_STATUS,
//            'user_id' => intval($arrInput['user_id']),
//            'left_team_num' => 0,
//        );

        $field = array(
            'status = ' => self::GROUP_COMFRIM_STATUS,
            'update_time =' => time(),
        );

        $cond = array(
            'id = ' => intval($arrInput['id']),
            'left_team_num = ' => 0,
        );

        $arrDbInput = array(
            'table' => self::GROUP_TABLE,
            'field' => $field,
            'cond' => $cond,
        );

        $arrRet = Util_Db::update($arrDbInput);
        if($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(printf(__METHOD__." find  fail,  input = %s output = %s",serialize($arrDbInput),serialize($arrRet)));
            return Util_Function::errRet($arrRet['errno']);
        }

        $msgInput = array(
            'third_group' => $group['id'],
            'first_group' => $group['first_group'],
            'second_group' => $group['second_group'],
            'member_msg_type' => Service_Movie_Msg::CONFIRM_TEAM_MEMBER,
        );
        $arrNMQRes = Util_Nmq::commitAction('Service_Movie_Msg','sendTeanMember',$msgInput);

//        Service_Movie_Msg::sendTeanMember($msgInput);

        return  Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRet['data']);

    }


    /**
    * @brief 查询确认状态的队伍列表
    *   
    */
    public static function getConfrimGroups($arrInput){
        if (
            !isset($arrInput['first_group']) || !isset($arrInput['second_group'])
           )
        {
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

//        $arrInput['status'] = self::GROUP_COMFRIM_STATUS;
//
//        $cond = array();
//        foreach(self::$Group_Model as $key){
//            if(isset($arrInput[$key])){
//                $cond[$key.' = '] = $arrInput[$key];
//            }
//        }
//
//        $arrDbInput = array(
//            'table' => self::GROUP_TABLE,
//            'field' => self::$Group_Model,
//            'cond' => $cond,
//        );
//
//        $arrRet = Util_Db::select($arrDbInput);
//
//        if($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
//            Bingo_Log::warning(printf(__METHOD__." fail,  input = %s output = %s",serialize($arrDbInput),serialize($arrRet)));
//            return Util_Function::errRet($arrRet['errno']);
//        }
        $first_group = intval($arrInput['first_group']);
        $second_group = intval($arrInput['second_group']);
        $select_sql = " select * from ". self::GROUP_TABLE ." where first_group = ".$first_group ." and second_group = ". $second_group.
                        " and ( status = ".self::GROUP_COMFRIM_STATUS ." or status = " . self::GROUP_TBKEY_STATUS. " or status = " . self::GROUP_TBKEY_STATUS." or status = " . self::GROUP_CREATE_STATUS.")";
        $total_sql = " select count(*) as total from ". self::GROUP_TABLE ." where first_group = ".$first_group ." and second_group = ". $second_group.
            " and ( status = ".self::GROUP_COMFRIM_STATUS ." or status = " . self::GROUP_TBKEY_STATUS." or status = " . self::GROUP_TBKEY_STATUS." or status = " . self::GROUP_CREATE_STATUS. ")"
            ." order by update_time desc ";
        $arrGroupRet =  self::findListByPage($select_sql,$total_sql,$arrInput['pn'],$arrInput['rn']);
        if($arrGroupRet == false || $arrGroupRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning(__METHOD__." find list fail,  input = ".$select_sql." output = " .serialize($arrGroupRet));
            return Util_Function::errRet($arrGroupRet['errno']);
        }

        $list = $arrGroupRet['data']['list'];
        $arrUserIds = array();
        $arrUserIdInput = array();
        foreach($list as $key => $group){
            $arrUserIds[$group['user_id']] = $key;
            $arrUserIdInput['user_id'][] = $group['user_id'];
        }
        $userRet = Tieba_Service::call("user","mgetUserData",$arrUserIdInput,null, null, 'post','php', 'utf-8');
        if($userRet != false && $userRet['errno'] == Tieba_Errcode::ERR_SUCCESS){
            $userInfo = $userRet['user_info'];
            foreach($userInfo as $key => $value){
                $user = array(
                    'user_id' => $value['user_id'],
                    'user_name' => $value['user_name'],
                    'portrait' => Tieba_Ucrypt::encode( $value['user_id'], $value['user_name']),
                );
                $list[$arrUserIds[$key]]['user'] = $user;
            }
        }
        $arrGroupRet['data']['list'] = $list;
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$arrGroupRet['data']);
    }



    /**
    * @brief 队长修改队伍信息
    *
    */
    public static function modifyGroup($arrInput){
        if(!isset($arrInput['id']) || !isset($arrInput['cinema_id']) || !isset($arrInput['team_type'])
            || !isset($arrInput['user_id'])
          ) {
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return Util_Function::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }


        $arrFindInput = array(
            'id' => intval($arrInput['id']),
            'user_id' => intval($arrInput['user_id']),
            );
        $arrRet = self::findGroupInfo($arrFindInput);

        if($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(printf(__METHOD__." find  fail,  input = %s output = %s",serialize($arrInput),serialize($arrRet)));
            return Util_Function::errRet($arrRet['errno']);
        }

        $group = $arrRet['data'];
        if(intval($group['status']) != self::GROUP_CREATE_STATUS){
            Bingo_Log::warning(__METHOD__."  not ".self::GROUP_CREATE_STATUS);
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $team_conf_id = intval($arrInput['team_type']);
        $arrTeamTypeRet = Service_Movie_MovieTeam::getTeamConf($team_conf_id);
        if($arrTeamTypeRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__METHOD__." team conf is not exist  ");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR,' team conf is not exist');
        }
        $team_conf = $arrTeamTypeRet['data'];

        if($group['max_team_num'] > $team_conf['team_max']){
            Bingo_Log::warning(__METHOD__." max_team_num > team_max ");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR,' max_team_num > team_max ');
        }

        $team_max = $team_conf['team_max'];
        $user_id = intval($arrInput['user_id']);
        $dif =  $team_max - $group['max_team_num'];
        $id = intval($arrInput['id']);
        $cinema_id = intval($arrInput['cinema_id']);

        $db = Util_Db::getDB();
        if($db == null){
            Bingo_Log::warning("DB link fail.");
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        do {
            $db->startTransaction();

            $select_sql = " select left_team_num from ".self::GROUP_TABLE . " where id = " .$id ." and user_id = " .$user_id . " for update ";
            $arrRet = $db->query($select_sql);
            Bingo_Log::warning(print_r($arrRet,1));
            if($arrRet === false || $db->getAffectedRows() <= 0) {
                Bingo_Log::warning(" lock  left_team_num fail! sql: ".$db->getLastSQL() );
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            $update_sql = " update ".self::GROUP_TABLE. " set cinema_id = $cinema_id , left_team_num = left_team_num + $dif ". " , max_team_num = $team_max where user_id = $user_id and id = $id";
        
            $arrRet = $db->query($update_sql);
            Bingo_Log::warning(print_r($arrRet,1));
            if($arrRet === false || $db->getAffectedRows() <= 0) {
                Bingo_Log::warning(" update  left_team_num fail! sql: ".$db->getLastSQL() );
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }   
            
            if ($errno == Tieba_Errcode::ERR_SUCCESS && !$db->commit()) {
                Bingo_Log::warning("commit db error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows());
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
        } while(0);

        if( $errno != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::pushNotice('transaction_run',0);
            $tmp = $db->rollback();
            Bingo_Log::warning("db->rollback. [".serialize($tmp)."]");
            Bingo_Log::pushNotice("rollback",1);
            return Util_Function::errRet($errno);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
        
    }

    /**
     * @bief 非吧绑定时，生成吧ID规则
     * @param $first_group
     * @param $second_group
     * @return int
     */
    public static  function genForumId($first_group,$second_group){
        return intval($first_group) * self::DEFAULT_FORUM_INDEX + intval($second_group);
    }

    /**
     * @param $selectSql
     * @param $totalSql
     * @param $pn
     * @param $rn
     * @return array
     */
    public static function findListByPage($selectSql,$totalSql,$pn,$rn){
        $pn = intval($pn);
        $rn = intval($rn);
        $pn = $pn <= 0 ? 1 : $pn;
        $rn = $rn <= 0 ? 20 : $rn;

        $offset = ($pn - 1) * $rn;
        $selectSql .= " limit $offset, $rn ";

        $ret =  self::executeSql($selectSql);
        if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("query $selectSql  failed. errno[{$ret['errno']}]");
            return  Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRet = self::executeSql($totalSql);
        $retTotal = $arrRet['data']['0']['total'];

        $output = array(
            'list' => $ret['data'],
            'page' => array(
                'current_pn'  => $pn,
                'total_count' => $retTotal,
                'total_pn'    => ceil($retTotal / $rn),
            ),
        );
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$output);
    }

    /**
     * SQL通用执行方法
     * @param $sql
     * @return array
     */
    private static function executeSql($sql){

        $db = Util_Db::getDB();
        if($db === null){
            Bingo_Log::warning("DB link fail.");
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $ret = $db->query($sql);
        if ($ret === false ) {
            Bingo_Log::warning("query db error! sql: ".$db->getLastSQL());
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$ret);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function updateCronGroup($arrInput){
        if(empty($arrInput['id'])){
            Bingo_Log::warning(__METHOD__." error param . input : " . serialize($arrInput));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $field = array(
            'cron_status = ' => 1,
        );


        $cond = array(
            'id = ' => intval($arrInput['id']),
        );


        $arrDbInput = array(
            'table' => self::GROUP_TABLE,
            'field' => $field,
            'cond' => $cond,
        );

        $arrRet = Util_Db::update($arrDbInput);
        if($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(printf(__METHOD__." find  fail,  input = %s output = %s",serialize($arrDbInput),serialize($arrRet)));
            return Util_Function::errRet($arrRet['errno']);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function findCronGroup($arrInput){


//        $cond = array(
//            'cron_status = ' => 0,
//        );
//
//        $arrDbInput = array(
//            'table' => self::GROUP_TABLE,
//            'field' => self::$Group_Model,
//            'cond' => $cond,
//        );
//
//        $arrRet = Util_Db::select($arrDbInput);
//        if($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
//            Bingo_Log::warning(printf(__METHOD__." find  fail,  input = %s output = %s",serialize($arrDbInput),serialize($arrRet)));
//            return Util_Function::errRet($arrRet['errno']);
//        }
        $first_group = intval($arrInput['first_group']);
        $pn = intval($arrInput['pn']);
        $rn = intval($arrInput['rn']);

        $select_sql = " select * from ".self::GROUP_TABLE ." where status = 2 and  cron_status = 0 and first_group = ".$first_group;
        $total_sql = " select count(*) as total from ".self::GROUP_TABLE." where status = 2 and  cron_status = 0 and first_group = ".$first_group;
        return self::findListByPage($select_sql,$total_sql,$pn,$rn);
//        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRet['data']);
    }

}