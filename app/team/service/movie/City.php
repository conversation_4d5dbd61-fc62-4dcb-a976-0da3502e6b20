<?php
/**
 * Created by PhpStorm.
 * User: wangjunsheng
 * Date: 2016/5/17
 * Time: 15:52
 */

define('BINGO_ENCODE_LANG','UTF-8');
class Service_Movie_City {

    private static $arrCity = array(
        1 => array(
            'id' => 1,
            'name' =>  '北京',
        ) ,

        2 => array(
            'id' => 2,
            'name' =>  '广州',
        ) ,

        3 => array(
            'id' => 3,
            'name' =>  '上海',
        ) ,

        4 => array(
            'id' => 4,
            'name' =>  '成都',
        ) ,

        5 => array(
            'id' => 5,
            'name' =>  '深圳',
        ) ,


        6 => array(
            'id' => 6,
            'name' =>  '佛山',
        ) ,

        7 => array(
            'id' => 7,
            'name' =>  '长沙',
        ) ,


        8 => array(
            'id' => 8,
            'name' =>  '无锡',
        ) ,

        9 => array(
            'id' => 9,
            'name' =>  '南京',
        ) ,

        10 => array(
            'id' => 10,
            'name' =>  '苏州',
        ) ,

        11 => array(
            'id' => 11,
            'name' =>  '合肥',
        ) ,
    );

    /**
     * @brief 根据城市ID获取城市
     * @param $cityId
     * @return array
     */
    public static function getCity($cityId){
        $id = intval($cityId);
        if (isset(self::$arrCity[$id])) {
            $city = self::$arrCity[$id];
            return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$city);
        } else {
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
    }

    /**
     * @brief 获取所有城市列表
     * @return array
     */
    public static function getAllCity(){
        $output = array();
        foreach(self::$arrCity as $city){
            $output [] = $city;
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$output);
    }

}