<?php
/**
 * Created by PhpStorm.
 * User: wangjunsheng
 * Date: 2016/5/16
 * Time: 18:52
 */

define('BINGO_ENCODE_LANG','UTF-8');
class Service_Movie_Cinema {

    const CINEMA_TABLE = "cinema";

    private static $Cinema_Model = array(
        'id', //自增ID
        'name',//影院名
        'online_time',//开场时间
        'seats',//座位数
        'left_seats',//剩余座位数
        'status',//状态
        'tbkey_id',//T码ID
        'cinemae_type',//影院类型
        'city_id',//城市ID
        'mv_id',//电影ID
        'create_time',//创建时间
        'update_time',//更新时间
        'op_user',//操作人
        'room',//厅位信息
    );


    const CINEMA_INIT = 0;

    /**
     * @brief 保存影院信息适用于MIS
     * @param $arrInput
     * @return array
     */
    public static function saveCinema($arrInput){

        $field = array();
        foreach(self::$Cinema_Model as $key){
            if(isset($arrInput[$key])){
                $field[$key] = $arrInput[$key];
            }
        }

        if(!empty($field))
        {
            $field['create_time'] = time();
            $field['update_time'] = time();
            $field['left_seats'] = intval($arrInput['seats']);

            $arrDbInput = array(
                'table' => self::CINEMA_TABLE,
                'field' => $field,
            );
            $arrRes = Util_Db::insert($arrDbInput);
        }
        else
        {
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if($arrRes == false || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(printf(__METHOD__." fail,  input = %s output = %s",serialize($arrDbInput),serialize($arrRes)));
            return Util_Function::errRet($arrRes['errno']);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     *
     * 更新影院信息适用于MIS
     */
    public static function updateCinema($arrInput){
        $field = array();
        foreach(self::$Cinema_Model as $key){
            if(isset($arrInput[$key])){
                $field[$key.' = '] = $arrInput[$key];
            }
        }

        $cond = array(
            'id = ' => $arrInput['id'],
        );

        if(!empty($field) && $arrInput['id'])
        {

            $field['update_time = '] = time();
            $arrDbInput = array(
                'table' => self::CINEMA_TABLE,
                'field' => $field,
                'cond' => $cond,
            );
            $arrRes = Util_Db::update($arrDbInput);
        }
        else
        {
            Bingo_Log::warning(printf(__METHOD__." param error,  input = %s ",serialize($arrInput)));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);

        }
        if($arrRes == false || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(printf(__METHOD__." fail,  input = %s output = %s",serialize($arrDbInput),serialize($arrRes)));
            return Util_Function::errRet($arrRes['errno']);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     *
     * 删除影院信息适用于MIS
     */
    public static function delCinema($arrInput){
        $db = Util_Db::getDB();
        if($db === null){
            Bingo_Log::warning("DB link fail.");
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $id = intval($arrInput['id']);
        $sql_query = " delete from " . self::CINEMA_TABLE . " where id = $id  and status = ".self::CINEMA_INIT;

        $ret = $db->query($sql_query);
        if ($ret === false ) {
            Bingo_Log::warning("query db error! sql: ".$db->getLastSQL());
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);


    }

    /**
     *
     * 查找影院信息
     */
    public static function findCinema($arrInput){
        $id = intval($arrInput['id']);
        if($id > 0)
        {
            $cond = array(
                'id = ' => $id,
            );

            $arrDbInput = array(
                'table' => self::CINEMA_TABLE,
                'field' => self::$Cinema_Model,
                'cond' => $cond,
            );

            $arrRes = Util_Db::select($arrDbInput);
        }
        else
        {
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if($arrRes == false || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(printf(__METHOD__." fail,  input = %s output = %s",serialize($arrDbInput),serialize($arrRes)));
            return Util_Function::errRet($arrRes['errno']);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes['data']['0']);
    }

    /**
     * @brief 根据电影查询影院
     * @param $arrInput
     * @return array
     */
    public static function findCinemaListByMovie($arrInput){
        $pn = intval($arrInput['pn']);
        $rn = intval($arrInput['rn']);
        $movie_id = intval($arrInput['movie_id']);

        if($movie_id <= 0){
            Bingo_Log::warning(printf(__METHOD__." err param ,  input = %s ",serialize($arrInput)));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);

        }

        $pn = $pn <= 0 ? 1 : $pn;
        $rn = $rn <= 0 ? 20 : $rn;

        $cond = array(
            'mv_id = ' => $movie_id,
        );

        $offset = ($pn - 1) * $rn;
        $append = " limit $offset, $rn ";

        $arrDbInput = array(
            'table' => self::CINEMA_TABLE,
            'field' => self::$Cinema_Model,
            'cond' => $cond,
            'append' => $append,
        );

        $ret =  Util_Db::select($arrDbInput);
        if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("query db  failed : " . serialize($arrDbInput));
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $sql = " select count(*) as total from ". self::CINEMA_TABLE ." where mv_id = $movie_id";
        $arrRet = self::executeSql($sql);
        if ($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $retTotal = $arrRet['data']['0']['total'];

        $list = array();
        foreach ($ret['data'] as $key => $value) {
            # code...
            $cinema_type = Service_Movie_CinemaType::getCinemaType($value['cinema_type']);
            $value['cinema_type_name'] = $cinema_type['name'];
            $list[] = $value;
        }

        $output = array(
            'list' => $list,
            'page' => array(
                'current_pn'  => $pn,
                'total_count' => $retTotal,
                'total_pn'    => ceil($retTotal / $rn),
            ),
        );
        return  Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$output);
    }

    /**
     * @brief 根据城市查询影院
     * @param $arrInput
     * @return array
     */
    public static function findCinemaListByCity($arrInput){
        $pn = intval($arrInput['pn']);
        $rn = intval($arrInput['rn']);
        $city_id = intval($arrInput['city_id']);
        $mv_id = intval($arrInput['mv_id']);


        if($city_id <= 0){
            Bingo_Log::warning(__METHOD__." err param ,  input =  ".serialize($arrInput));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $pn = $pn <= 0 ? 1 : $pn;
        $rn = $rn <= 0 ? 20 : $rn;

        $cond = array(
            'city_id = ' => $city_id,
            'mv_id = ' => $mv_id,
        );

        $offset = ($pn - 1) * $rn;
        $append = " limit $offset, $rn ";

        $arrDbInput = array(
            'table' => self::CINEMA_TABLE,
            'field' => self::$Cinema_Model,
            'cond' => $cond,
            'append' => $append,
        );

        $ret =  Util_Db::select($arrDbInput);
        if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("query db  failed : " . serialize($arrDbInput));
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $sql = " select count(*) as total from ". self::CINEMA_TABLE ." where city_id = $city_id  and mv_id = $mv_id";
        $arrRet = self::executeSql($sql);
        if ($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $retTotal = $arrRet['data']['0']['total'];

        $list = array();
        foreach ($ret['data'] as $key => $value) {
            $value['city'] = Service_Movie_City::getCity($value['city_id'])['data'];
            $value['cinema_type'] = Service_Movie_CinemaType::getCinemaType($value['cinemae_type'])['data'];
            $list[] = $value;
        }


        $output = array(
            'list' => $list,
            'page' => array(
                'current_pn'  => $pn,
                'total_count' => $retTotal,
                'total_pn'    => ceil($retTotal / $rn),
            ),
        );
        return  Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$output);
    }

    /**
     * @brief 根据城市查询影院
     * @param $arrInput
     * @return array
     */
    public static function sumSeatsByCity($arrInput){

        $city_id = intval($arrInput['city_id']);
        $mv_id = intval($arrInput['mv_id']);


        if($city_id <= 0){
            Bingo_Log::warning(__METHOD__." err param ,  input =  ".serialize($arrInput));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $sql = " select sum(left_seats) as total from ". self::CINEMA_TABLE ." where city_id = $city_id  and mv_id = $mv_id";
        $arrRet = self::executeSql($sql);
        if ($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $retTotal = $arrRet['data']['0']['total'];

        $output = array(
            'total' => $retTotal,
        );
        return  Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$output);
    }

    /**
     * @param $selectSql
     * @param $totalSql
     * @param $pn
     * @param $rn
     * @return array
     */
    public static function findCinemaListByPage($selectSql,$totalSql,$pn,$rn){
        $pn = intval($pn);
        $rn = intval($rn);
        $pn = $pn <= 0 ? 1 : $pn;
        $rn = $rn <= 0 ? 20 : $rn;

        $offset = ($pn - 1) * $rn;
        $selectSql .= " limit $offset, $rn ";

        $ret =  self::executeSql($selectSql);
        if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("query $selectSql  failed. errno[{$ret['errno']}]");
            return  Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRet = self::executeSql($totalSql);
        $retTotal = $arrRet['data']['0']['total'];

        $output = array(
            'list' => $ret['data'],
            'page' => array(
                'current_pn'  => $pn,
                'total_count' => $retTotal,
                'total_pn'    => ceil($retTotal / $rn),
            ),
        );
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$output);
    }

    /**
     * SQL通用执行方法
     * @param $sql
     * @return array
     */
    private static function executeSql($sql){

        $db = Util_Db::getDB();
        if($db === null){
            Bingo_Log::warning("DB link fail.");
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $ret = $db->query($sql);
        if ($ret === false ) {
            Bingo_Log::warning("query db error! sql: ".$db->getLastSQL());
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$ret);
    }


    /**
     * @brief 队长抢票
     * @param $arrInput
     * @return array
     */
    public static  function enterCinema($arrInput){
        if( !isset($arrInput['user_id']) || !isset($arrInput['third_group']) || !isset($arrInput['cinema_id'])){
            Bingo_Log::warning(__METHOD__." err param ,  input =  ".serialize($arrInput));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrFindGroup = array(
            'id' => intval($arrInput['third_group']),
            'user_id' => intval($arrInput['user_id']),
        );

        $arrRet = Service_Grouper_Group::findGroupInfo($arrFindGroup);
        if($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__METHOD__." err param ,  input = ".serialize($arrInput));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $group = $arrRet['data'];
        if(intval($group['status']) != Service_Grouper_Group::GROUP_COMFRIM_STATUS){
            Bingo_Log::warning(__METHOD__." err param ,  input =  ". serialize($group));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if(intval($group['status']) == Service_Grouper_Group::GROUP_TBKEY_STATUS){
            Bingo_Log::warning(__METHOD__." err param ,  input =  ". serialize($group));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $team_seats = $group['max_team_num'];
        $cinema_id = intval($group['cinema_id']);


        //当前队伍的影院信息
        $cond = array(
            'id = ' => $cinema_id,
        );
        $arrFindCinema = array(
            'table' => self::CINEMA_TABLE,
            'field' => self::$Cinema_Model,
            'cond'  => $cond,
        );

        $arrCinemaRet =Util_Db::select($arrFindCinema);
        if($arrCinemaRet == false || $arrCinemaRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__METHOD__." err param ,  input =  ".serialize($arrFindCinema));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $cinema = $arrCinemaRet['data']['0'];


        //防止抢其他影院的票数,当前队伍选择的影院票数不足时，才可以抢其他影院的电影票
        $second_cinema_id = intval($arrInput['cinema_id']);//备胎，可能和原影院ID一致，当前影院剩余票数不足时，查询备胎
        if($cinema['left_seats'] < $group['max_team_num']){

            if($cinema_id == $second_cinema_id){
                $output = array(
                    'has_more' => true,
                );
                Bingo_Log::warning(__METHOD__." err param ,  input = ".serialize($arrFindCinema));
                return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR,$output,' 请重新选择其他影院');
            }

            if($cinema_id != $second_cinema_id){
                $cond = array(
                    'id = ' => $second_cinema_id,
                );
                $arrFindCinema = array(
                    'table' => self::CINEMA_TABLE,
                    'field' => self::$Cinema_Model,
                    'cond'  => $cond,
                );

                $arrCinemaRet =Util_Db::select($arrFindCinema);
                if($arrCinemaRet == false || $arrCinemaRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
                    Bingo_Log::warning(__METHOD__.' err param ,  input =  '.serialize($arrFindCinema));
                    return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR,null,"请重新选择其他影院");
                }
                $second_cinema = $arrCinemaRet['data']['0'];

                if($second_cinema['left_seats'] < $group['max_team_num']){
                    $output = array(
                        'has_more' => true,
                    );
                    Bingo_Log::warning(__METHOD__." err param ,  input = ".serialize($arrFindCinema));
                    return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR,$output,' 请重新选择其他影院');
                }
                //备胎可用则进行更新
                $cinema_id = $second_cinema_id;
                $cinema = $second_cinema;
            }
        }


        $db = Util_Db::getDB();
        if($db == null){
            Bingo_Log::warning("DB link fail.");
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        $flag = 0;
        do {
            $db->startTransaction();

            $update_sql = " update ".self::CINEMA_TABLE. " set left_seats = left_seats - $team_seats  "."  where id = $cinema_id and left_seats >= $team_seats ";

            $arrRet = $db->query($update_sql);
            if($arrRet === false || $db->getAffectedRows() <= 0) {
                Bingo_Log::warning(" update  left_seats fail! sql: ".$db->getLastSQL() );
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                $flag = 1;
                break;
            }


            $arrUpdateGroup = array(
                'id' => intval($arrInput['third_group']),
                'user_id' => intval($arrInput['user_id']),
                'cinema_id' => $cinema_id,
                'db' => $db,
                'status' => Service_Grouper_Group::GROUP_TBKEY_STATUS,
            );
            $arrUpdateGroupRet = Service_Grouper_Group::updateGroup($arrUpdateGroup);
            if($arrUpdateGroupRet === false || $arrUpdateGroupRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning(" update  group fail! sql: ".serialize($arrUpdateGroup)." output " .serialize($arrUpdateGroupRet));
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            if ($errno == Tieba_Errcode::ERR_SUCCESS && !$db->commit()) {
                Bingo_Log::warning("commit db error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows());
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }
        } while(0);

        if( $errno != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::pushNotice('transaction_run',0);
            $tmp = $db->rollback();
            Bingo_Log::warning("db->rollback. [".serialize($tmp)."]");
            Bingo_Log::pushNotice("rollback",1);



            if($flag == 1){
                $arrMoreRet = self::findMoreCinemas($arrInput);
                if($arrMoreRet['errno'] == Tieba_Errcode::ERR_SUCCESS && empty($arrMoreRet['data']['list'])){
                    //如果事务失败，设置队伍的抢票失败状态
                    $arrUpdateGroup = array(
                        'id' => intval($arrInput['third_group']),
                        'user_id' => intval($arrInput['user_id']),
                        'cinema_id' => $cinema_id,
                        'status' => Service_Grouper_Group::GROUP_TBKEY_FAIL_STATUS,
                    );
                    $arrUpdateGroupRet = Service_Grouper_Group::updateGroup($arrUpdateGroup);
                    if($arrUpdateGroupRet === false || $arrUpdateGroupRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                        Bingo_Log::warning(" update  group fail! sql: ".serialize($arrUpdateGroup)." output " .serialize($arrUpdateGroupRet));
                    }
                }

            }

            return Util_Function::errRet($errno);
        }

        if($errno == Tieba_Errcode::ERR_SUCCESS){
            $arrTbkeyInput = array(
                'activity_id' => intval($cinema['tbkey_id']),
                'user_id' => intval($arrInput['user_id']),
            );
            $arrTbkeyRet = Tieba_Service::call("tbkey","acquireActivationKey",$arrTbkeyInput,null, null, 'post','php', 'utf-8');
        }

        $msgInput = array(
            'third_group' => $arrInput['third_group'],
            'first_group' => $group['first_group'],
            'second_group' => $group['second_group'],
            'member_msg_type' => Service_Movie_Msg::TBKEY_TEAM_MEMBER,
        );
        $arrNMQRes = Util_Nmq::commitAction('Service_Movie_Msg','sendTeanMember',$msgInput);
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$arrTbkeyRet['data'][0]);

    }


    /**
     * @brief 抢票失败情况下，查询更多影院
     * @param $arrInput
     * @return array
     */
    public static function findMoreCinemas($arrInput){
        $output = array();
        if( !isset($arrInput['user_id']) || !isset($arrInput['third_group'])){
            Bingo_Log::warning(__METHOD__." err param ,  input =  ".serialize($arrInput));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrFindGroup = array(
            'id' => intval($arrInput['third_group']),
        );

        $arrRet = Service_Grouper_Group::findGroupInfo($arrFindGroup);
        if($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__METHOD__." err param ,  input = ".serialize($arrInput));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $group = $arrRet['data'];
        $team_num = $group['max_team_num'];
        $cinema_id = $group['cinema_id'];

        //当前队伍的影院信息
        $cond = array(
            'id = ' => $cinema_id,
        );
        $arrFindCinema = array(
            'table' => self::CINEMA_TABLE,
            'field' => self::$Cinema_Model,
            'cond'  => $cond,
        );

        $arrCinemaRet =Util_Db::select($arrFindCinema);
        if($arrCinemaRet == false || $arrCinemaRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__METHOD__." err param ,  input =  ".serialize($arrFindCinema));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $cinema = $arrCinemaRet['data']['0'];

        $cinema['city'] = Service_Movie_City::getCity($cinema['city_id'])['data'];
        $cinema['cinema_type'] = Service_Movie_CinemaType::getCinemaType($cinema['cinemae_type'])['data'];
        $output['cinema'] = $cinema;
        if($cinema['left_seats'] >= $group['max_team_num'] ){
            Bingo_Log::warning(__METHOD__." err param ,  input =  ".serialize($arrFindCinema));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR,null,'影院还有余票，请抢票');
        }

        $city_id = $cinema['city_id'];

        $cond = array(
            'city_id = ' => $city_id,
            'left_seats >= ' => $team_num,
            'mv_id = ' => $cinema['mv_id'],
        );
        $arrFindCinema = array(
            'table' => self::CINEMA_TABLE,
            'field' => self::$Cinema_Model,
            'cond'  => $cond,
        );
        $arrCinemaListRet =Util_Db::select($arrFindCinema);
        if($arrCinemaListRet == false || $arrCinemaListRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__METHOD__." err param ,  input =  ".serialize($arrFindCinema));
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $list = array();
        foreach ($arrCinemaListRet['data'] as $key => $value) {
            $value['city'] = Service_Movie_City::getCity($value['city_id'])['data'];
            $value['cinema_type'] = Service_Movie_CinemaType::getCinemaType($value['cinemae_type'])['data'];
            $list[] = $value;
        }
        $output['list'] = $list;
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$output);

    }




}
