<?php
/**
 * Created by PhpStorm.
 * User: wangjunsheng
 * Date: 2016/5/17
 * Time: 16:10
 */


define('BINGO_ENCODE_LANG','UTF-8');
class Service_Movie_CinemaType {

    private static $arrCinemaType = array(
        '1' => array(
            'id' => 1,
            'name' => '3D英文',
        ),

        '2' => array(
            'id' => 2,
            'name' => '2D',
        ),
    );

    /**
     * @brief 根据团队ID获取团队配置
     * @param $cityId
     * @return array
     */
    public static function getCinemaType($id){
        $id = intval($id);
        if (isset(self::$arrCinemaType[$id])) {
            $team = self::$arrCinemaType[$id];
            return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$team);
        } else {
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
    }

    /**
     * @brief 获取所有团队配置
     * @return array
     */
    public static function getAllCinemaType(){
        $output = array();
        foreach(self::$arrCinemaType as $team){
            $output [] = $team;
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS,$output);
    }

}