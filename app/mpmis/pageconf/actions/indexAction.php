<?php
require_once 'pageconfAction.php';
require_once 'Logic/Dict.php';
class indexAction extends PageconfAction{	
	protected $tpl = 'index.php';
	public function execute(){
		if($this->version == '2.0'){
			$this->tpl = 'normal.php';
		} else {
			$user = Logic_Dict::get('user');
			$view = Mis_Request::getParam('view',0);
			if ($user['identity']=='pmleader' && $view!='origin') {
				echo "<META HTTP-EQUIV=\"Refresh\" CONTENT=\"0; URL=/pageconf/checkEffect\">";
				return;
			}
		}
		$this->returnInfo(TRUE, '');
	}
}
?>


