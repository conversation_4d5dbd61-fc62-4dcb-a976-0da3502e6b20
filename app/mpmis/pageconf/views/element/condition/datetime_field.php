<tr class="datetime_field" name="<?= $data_name ?>" 
	<?php if($is_hide) { ?>
		style="display:none;"
	<?php } ?>
>
	<th> <?=$field_name?>�� </th>
	<td>
		<?php 
			$hour = (isset($field_data) ? date('H', $field_data) : 0);
		?>
		<input
		<?php  if(isset($placeholder) && $placeholder != ''){ ?>
		placeholder="<?= $placeholder ?>"
		<?php } ?>
		 class="input" type="text" name="date" value="<?= (isset($field_data)&&$field_data!='') ? date('Y-m-d',$field_data) : '' ?>" />
		<select type="text" class="hour" name="hour" value="<?= isset($field_data) ? date('h', $field_data) : '' ?>" >
			<?php for($i = 0; $i < 24; $i ++) { ?>
				<option value="<?= $i ?>" <?php if($hour == $i) { ?> selected="selected" <?php } ?> ><?= $i ?></option>
			<?php } ?>
		</select> ʱ
	</td>
</tr>