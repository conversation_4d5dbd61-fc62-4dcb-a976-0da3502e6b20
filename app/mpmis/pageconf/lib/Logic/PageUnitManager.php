<?php 
require_once 'Data/Db.php';
require_once 'Product.php';
require_once 'Widget.php';
require_once 'PageUnit.php';
require_once 'Content.php';
require_once 'ContentManager.php';
require_once 'SceneManager.php';

class Logic_PageUnitManager {
    const DB_TABLE_DEFAULT_CONTENT = 'default_content';
    const DB_TABLE_WIDGET_UNIT_RELATION = 'widget_unit_relation';

    const ERROR_WIDGET_UNIT_RELATION_EXISTS = 101;

	private static $_instance = NULL;
	private $_db = NULL;
	
	private function __construct() {
		$this->_db = Data_Db::getConn(); 
	}
	
	static function getInstance() {
		if (self::$_instance==NULL) {
			self::$_instance = new self();
		}
		return self::$_instance;
	}
	
	function createPageUnit($unit_type, $unit_id) {
		$page_unit = new Logic_PageUnit();
		$page_unit->setUnitId($unit_id);
		$page_unit->setType($unit_type);
		//���չ�������name
		$last_underscore = strrpos($unit_id,'_');
		if ($last_underscore===false) {
			$name = $unit_id;
		} else {
			$name = substr($unit_id,$last_underscore+1);
		}
		$page_unit->setName($name);
		return $page_unit;
	}
	
	function getPageUnit($unit_key) {
		$result = $this->_getPageUnitsByFilter("where ".Logic_PageUnit::DB_TABLE.".unit_id='$unit_key'");
		if (empty($result)) {
			return FALSE;
		}
		$page_unit = $result[0];
		return $page_unit;
	}
	
	function getPageUnitById($id) {
		$filter = "where ".Logic_PageUnit::DB_TABLE.".id=$id";
		$page_units = $this->_getPageUnitsByFilter($filter);
		if (!empty($page_units)) {
			return $page_units[0];
		} else {
			return NULL;
		}
	}
	
	function getPageUnits($id_list, $by_product = false) {
		$page_units = array();
		if (!empty($id_list)) {
			$filter = 'where '.Logic_PageUnit::DB_TABLE.'.id in ('.join($id_list,',').')';
			if($by_product){
				$w = Logic_Widget::DB_TABLE;
		        $product = Logic_Product::getName();
		        $filter .= "and $w.product='$product'";
			}
			$page_units = $this->_getPageUnitsByFilter($filter);
		}
		return $page_units;
	}
	
	function getPageUnitArrByUnitId($unit_id_list) {
		$page_units = array();
		if (!empty($unit_id_list)) {
			//��������
			$unit_key_list = array();
			for ($i=0,$len=count($unit_id_list);$i<$len;$i++) {
				$unit_key_list[$i] = "'".$unit_id_list[$i]."'";
			}
			$filter = 'where '.Logic_PageUnit::DB_TABLE.'.unit_id in ('.join($unit_key_list,',').')';
			$result = $this->_getPageUnitsByFilter($filter);
			for ($i=0,$len=count($result);$i<$len;$i++) {
				$page_unit = $result[$i];
				$page_units[$page_unit->getUnitId()] = $page_unit;
			}
		}
		return $page_units;
	}
	
	function getAllPageUnits() {
        $w = Logic_Widget::DB_TABLE;
        $product = Logic_Product::getName();
        $filter = "where $w.product='$product'";
		return $this->_getPageUnitsByFilter($filter);
	}
	
    function deletePageUnitsOfWidget($widget_id) {
        $wur = self::DB_TABLE_WIDGET_UNIT_RELATION;
		$sql = "delete from $wur where widget=$widget_id";
		$result = $this->_db->query($sql);
		return $result;
	}
	
	private function _createDefaultContent($page_unit) {
		$default_content = Logic_ContentManager::getInstance()->createContent($page_unit->getType());
		$default_content->set(array(
			'page_unit' => $page_unit->getId(),
		));
		return $default_content;
	}
	
	private function _addDefaultContent($default_content) {
		$result = $this->_db->insert(self::DB_TABLE_DEFAULT_CONTENT,$default_content->toDbArray());
		if (empty($result)) {
			return FALSE;
		}
		return $this->_db->getInsertID();
	}
	
	function addPageUnit($page_unit, $widget_id) {
		$sql = "insert into ".Logic_PageUnit::DB_TABLE
			." (unit_id,description,type) values ('".$page_unit->getUnitId()."','".$page_unit->getDescription()."','".$page_unit->getType()."')";
		$result = $this->_db->query($sql);
		if (empty($result)) {
			return FALSE;
		}
		$id = $this->_db->getInsertID();
		$page_unit->setId($id);
		
		
		//�������󶨸�PageUnit��DefaultContent
		$default_content = $this->_createDefaultContent($page_unit);
		$result = $this->_addDefaultContent($default_content);
		if (empty($result)) {
			return FALSE;
		}
		$page_unit->setDefaultContent($default_content);		
		
		
		//�������󶨸�PageUnit�ļ�������
//		$scene_manager = Logic_Scene::getInstance();
//		$scene_types = $scene_manager->getAllSceneTypes();
//		foreach ($scene_types as $scene_type) {
//			$scene = $scene_manager->createScene($page_unit->getId());
//			$scene->setType($scene_type);
//		}

        //����һ��WidgetUnitRelation
        self::addWidgetUnitRelation($widget_id, $id);
		
		return $page_unit->getId();		
	}
	
	/*
	function updatePageUnit($page_unit) {
		$sql = "update ".Logic_PageUnit::DB_TABLE
			." set type='$page_unit->getType()' , unit_id='$page_unit->getUnitId()'"
			." where id=$page_unit->getId()";
		$result = $this->_db->query($sql);
		return $result;
	}
	*/
	
	function _deletePageUnit($id) {
		$result = $this->_db->delete(Logic_PageUnit::DB_TABLE,"id=$id");
		if (empty($result)) {
			return FALSE;
		}	
		return TRUE;
    }

    function addWidgetUnitRelation($widget_id, $unit_id) {
        $wur = self::DB_TABLE_WIDGET_UNIT_RELATION;
        $sql = "SELECT * from $wur WHERE widget=$widget_id AND unit=$unit_id";
        $result = $this->_db->query($sql);
        if (!empty($result)) {
            //relation exists
            return self::ERROR_WIDGET_UNIT_RELATION_EXISTS;
        }
        else {
            $sql = "INSERT INTO $wur (widget, unit) VALUES ($widget_id, $unit_id)";
            $result = $this->_db->query($sql);
            if (!$result) {
                return FALSE;
            }
            return TRUE;
        }            
    }

    function deleteWidgetUnitRelation($widget_id, $unit_id) {
        $result = $this->_db->delete(self::DB_TABLE_WIDGET_UNIT_RELATION, "widget=$widget_id AND unit=$unit_id");
        if (empty($result)) {
            return FALSE;
        }
        //����δ�����õ�unit
        self::_cleanupUnusedPageUnit();
        return TRUE;
    }

    /**
     * delete page units not belong to any widget.
     */
    function _cleanupUnusedPageUnit() {
        $wur = self::DB_TABLE_WIDGET_UNIT_RELATION;
        $pu = Logic_PageUnit::DB_TABLE;
        $sql = "select $pu.id from $wur right join $pu on $wur.unit=$pu.id where $wur.id is null";
        $unusedPageUnitList = $this->_db->query($sql);
        if (empty($unusedPageUnitList)) {
            return true;
        }
        $unusedPageUnitIds = array();
        foreach ($unusedPageUnitList as $unusedPageUnit) {
            $unusedPageUnitIds[] = $unusedPageUnit['id'];
        }
        $filter = "id in (".join($unusedPageUnitIds,',').")";
        $result = $this->_db->delete(Logic_PageUnit::DB_TABLE, $filter);
        if (!$result) {
            return false;
        }
        return TRUE; 
    }
	
	/**
	 * ���ݼ�����������PageUnit�б�
	 * @param String $filter like 'where ...'
	 * @return page_unit array. if non-result return empty array
	 */
	private function _getPageUnitsByFilter($filter="") {
        //���ݱ�ı���
        //widget_unit��ϵ��
        $wur = self::DB_TABLE_WIDGET_UNIT_RELATION;
        //widget��
        $w = Logic_Widget::DB_TABLE;
        $w_column = array('id');
        $w_querys = Logic_Tools::generateQueryItemsWithTable($w_column,$w);
		//page_unit��
		$pu = Logic_PageUnit::DB_TABLE;
		$pu_column = array('id','unit_id','description','type');
		$pu_querys = Logic_Tools::generateQueryItemsWithTable($pu_column,$pu);
		//default_content��
		$dc = self::DB_TABLE_DEFAULT_CONTENT;
		$dc_column = Logic_Content::getQueryColumns();
		$dc_querys = Logic_Tools::generateQueryItemsWithTable($dc_column,$dc);
		
		$table_querys = array($w_querys,$pu_querys,$dc_querys);
		
		$sql_arr = array("select");
		foreach ($table_querys as $table_query) {
			foreach ($table_query as $column => $column_query_items) {
				$sql_arr[] = "$column_query_items[0] as $column_query_items[1]";
				$sql_arr[] = ",";
			}
		}
		array_pop($sql_arr);	//�������һ������
		$sql_arr[] = "from $pu left join $dc on $pu.id=$dc.page_unit inner join $wur on $pu.id=$wur.unit inner join $w
		on $wur.widget=$w.id";
		$sql = join($sql_arr," ");
		$sql .= " $filter";
		
		$result = $this->_db->query($sql);
		$page_units = array();
		
		if (!empty($result)) {
            //�������������unit_widget�����������unit������widget
            $unit_list = array();
            foreach ($result as $row) {
                $unit_id = $row[$pu_querys['id'][1]];
                $widget_id = $row[$w_querys['id'][1]];
                $unit_list[$unit_id][] = $widget_id;
            }
            //ȥ��
            foreach ($unit_list as $key => $unit) {
                $unit_list[$key] = array_unique($unit);
            }

            foreach ($result as $row) {
				//��ȡdefault_content
				$content_arr = array();
				foreach ($dc_column as $column) {
					$content_arr[$column] = $row[$dc_querys[$column][1]];
				}
				$content_type = $content_arr['content_type'];		
				$content = Logic_ContentManager::getInstance()->createContent($content_type);
				$content->set($content_arr);
				//��ȡpage_unit
				$page_unit_type = $row[$pu_querys['type'][1]];
				$page_unit_id = $row[$pu_querys['unit_id'][1]];
				$page_unit = $this->createPageUnit($page_unit_type, $page_unit_id);
				$page_unit->setId($row[$pu_querys['id'][1]]);
				$page_unit->setDescription($row[$pu_querys['description'][1]]);
				$page_unit->setDefaultContent($content);
				$page_unit->setWidgetId($row[$w_querys['id'][1]]);
                $page_unit->setWidgetIds($unit_list[$row[$pu_querys['id'][1]]]);
				$page_units[] = $page_unit;
			}
		}
//		for ($i=0; $i<sizeof($result); $i++) {
//			$page_unit = $this->createPageUnit($result[$i]['type'], $result[$i]['unit_id']);
//			$page_unit->setId($result[$i]['id']);
//			$page_unit->setDescription($result[$i]['description']);
//			$page_units[] = $page_unit;
//		}
		return $page_units;	
	}
	
	function getPageUnitArrByWidgetIdList($widget_id_list) {
        $page_unit_arr = array();
        if (!empty($widget_id_list)) {
            $pu = Logic_PageUnit::DB_TABLE;
            $wur = self::DB_TABLE_WIDGET_UNIT_RELATION;
			$sql = "select $pu.id,$pu.unit_id,$pu.description,$pu.type,$wur.widget from ($wur left join $pu on $wur
			.unit=$pu.id)
			 where $wur.widget in (".join($widget_id_list,",").")";
			$result = $this->_db->query($sql);

			for ($i=0; $i<sizeof($result); $i++) {
				$page_unit = $this->createPageUnit($result[$i]['type'], $result[$i]['unit_id']);
				$page_unit->setId($result[$i]['id']);
				$page_unit->setDescription($result[$i]['description']);
				$widget_id = $result[$i]['widget'];
				$page_unit->setWidgetId($widget_id);
				if (!isset($page_unit_arr[$widget_id])) {
					$page_unit_arr[$widget_id] = array();
				}
				array_push($page_unit_arr[$widget_id], $page_unit);
			}
		}

        return $page_unit_arr;
	}
}
