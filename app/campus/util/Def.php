<?php
class Util_Def {
    const TYPE_HOT_STAR_LIST          = 1;
    const TYPE_PONTENTIAL_STAR_LIST   = 2;
    const HOT_STAR_LIST_PREFIX        = 'hot_star_list';
    const PONTENTIAL_STAR_LIST_PREFIX = 'pontential_star_list';
    const DAY_CLICK                   = 86400;
    const WEEK_CLICK                  = 604800;

    // 错误码范围，最高两位标识子系统，中间两位标识子UI，最后两位为子UI对应的错误码
    // 例如 娱乐频道 最高两位为 11
    //      榜单服务 中间两位为 04
    //      榜单为空 最后两位为 01
    //      那么 娱乐频道榜单服务中某榜单为空的错误码则为110401
    const ERR_CODE_MIN      = 100000;
    const ERR_CODE_MAX      = 999999;
    // UI 的错误码
    const ERR_UNKNOW_ERROR   = 100001;     // 位置错误

    public static $arrUiCodes = array(
        self::ERR_UNKNOW_ERROR => 'unknow error',    // 未知错误
    );

    /**
     * @desc 拉取对应的错误码
     * @param [in] errno : uint32_t : 错误码 
     * @return
     */
    public static function getUiErrmsg($errno) {
        $errno = (int)($errno);
        if (false === self::isValidUiCode($errno)) {
            return self::$arrUiCodes[self::ERR_UNKNOW_ERROR];
        } else {
            return isset(self::$arrUiCodes[$errno]) ? self::$arrUiCodes[$errno] : $arrUiCodes[self::ERR_UNKNOW_ERROR];
        }
    }

    /**
     * @desc 给定一个错误码，判断是不是本系统的
     * @param[in] errno : uint32_t : 错误码类别
     * @return
     */
    public static function isValidUiCode($errno) {
        $errno = (int)($errno);
        if ($errno >self::ERR_CODE_MIN && $errno < self::ERR_CODE_MAX) {
            return true;
        } else {
            return false;
        }
    }
}
