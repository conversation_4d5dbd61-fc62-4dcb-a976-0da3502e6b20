<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-07-02 19:54:01
 * @version
 */

 /*
    注意这里涉及到rd与fe的模块名，如果提前已经协商好统一模块名例如都叫 wlmonitor，那么这里就不要传第二个参数，默认即可：
        Tieba_Init::init("wlmonitor");
    但如果没协商，比如rd的模块名叫 wlmonitor，fe的模块名叫 wlmonitor_fe，那么这里就应当是（fe这个模块名用于 ROOT_PATH/template/ 下的文件夹名）
        Tieba_Init::init("wlmonitor","wlmonitor_fe");
    同理，也可以自定义omp模块名，默认同样使用 wlmonitor：
        Tieba_Init::init("wlmonitor",null,"wlmonitor_omp");
 */



define('BINGO_ENCODE_LANG', 'UTF-8');
define('MODULE_NAME', 'wlmonitor');
//定义模块相关信息
define('FOR_TEST', 'Off');
define('MODULE_ORP_BASE_PATH', dirname(__FILE__) . "/../..");
define('MODULE_UI_BASE_PATH', MODULE_ORP_BASE_PATH . '/app/' . MODULE_NAME);
define('MODULE_LOG_BASE_PATH', MODULE_ORP_BASE_PATH . '/log/app/' . MODULE_NAME);
define('MODULE_CONF_BASE_PATH', MODULE_ORP_BASE_PATH . '/conf/app/' . MODULE_NAME);
define('MODULE_VIEW_PATH',MODULE_ORP_BASE_PATH . '/template/'.MODULE_NAME);
define('MODULE_ACTION_PATH', MODULE_UI_BASE_PATH . '/actions');
define('MODULE_UTIL_PATH',MODULE_UI_BASE_PATH . '/util');

//设置全局加载目录
set_include_path(get_include_path() . PATH_SEPARATOR . realpath(MODULE_UTIL_PATH));

/**
 * 设置自动加载
 * @param string
 */
function __autoload($strClassName)
{
    require_once str_replace('_','/',$strClassName) . '.php';
}
spl_autoload_register('__autoload');

//设置默认时间
date_default_timezone_set('Asia/Chongqing');

//设置路由
$objHttpRouter = new Bingo_Http_Router_Pathinfo();
$objHttpRouter->getHttpRouter();
$arrPath = explode('/',Bingo_Http_Request::getDispatchRouter());
$strSubModule = isset($arrPath[1])?$arrPath[1]:'wlmonitor';
$arrRouter = array();
if (isset($arrRouter[$strSubModule])){
    $strSubModule = $arrRouter[$strSubModule];
}

Tieba_Init::init("wlmonitor","wlmonitor");
$objFrontController = Bingo_Controller_Front::getInstance(
    array(
        "actionDir" => MODULE_ACTION_PATH,
        "defaultRouter" => "index",
        "notFoundRouter" => "error",
        "beginRouterIndex" => 1,
    ));

Bingo_Timer::start('total');
Bingo_Page::init(
    array(
        "baseDir" => MODULE_VIEW_PATH,
        "debug" => false,
        "outputType" => ".",
        "isXssSafe" => true,
        "module" => "wlmonitor",
        "useTbView" => true,
        "viewRootpath" => MODULE_VIEW_PATH . "/../",
        //"catchPath" => "../../data/app/wlmonitor",
    ));

try{
   $objFrontController->dispatch();
}catch(Exception $e){
   //出错处理，直接转向到错误页面
   Bingo_Log::warning(sprintf('main process failure!HttpRouter=%s,error[%s]file[%s]line[%s]',
       Bingo_Http_Request::getStrHttpRouter(), $e->getMessage(), $e->getFile(), $e->getLine()));
   Bingo_Page::setErrno($e->getCode());
   header('location:http://static.tieba.baidu.com/tb/error.html');
}

Bingo_Timer::start('build_page');
Bingo_Page::buildPage();
Bingo_Timer::end('build_page');
Bingo_Timer::end('total');

$strTimeLog = Bingo_Timer::toString();
Bingo_Log::pushNotice('Timer','['.$strTimeLog.']');
Bingo_Log::buildNotice();           
?>
