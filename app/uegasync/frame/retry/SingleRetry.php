<?php
/**
 * 但服务调用重试，或多服务简单整体重试，支持SingleRal，SingleService，重试
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2019:07:01 11:26:32
 * @version
 * @structs & methods(copied from idl.)
 */
class Frame_Retry_SingleRetry extends Frame_Retry_BaseRetry{
	
	/**
	 * 是否需要重试，具体业务需要重写
	 * @param
	 * @return
	 */
	protected function shouldRetry($result){
		return $this->plugin->isFail($result);;
	}
	
	/**
	 * 重试是否成功，具体业务需要重写
	 * @param
	 * @return
	 */
	protected function isSuccess($result){
		$this->retryResult = $result;
		return !$this->plugin->isFail($result);
	}
	
   /**
     * 直接重新调用plugin处理逻辑
     * @param
     * @return
     */
    protected function retry(){
    	return $this->plugin->handle($this->retryRequest);
    }
	
}