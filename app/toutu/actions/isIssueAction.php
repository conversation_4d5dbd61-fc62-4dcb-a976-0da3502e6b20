<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file isIssueAction.php
 * <AUTHOR>
 * @date 2014/04/01 15:43:33
 * @brief 
 *  
 **/

class isIssueAction extends Util_Base {
    const WORDLIST_NAME_WENZI = 'tb_wordlist_redis_wenzi';
    const MANAGER_PERM = 'can_type2_audit_post';
    const PROFESSION_PERM = 'can_op_as_profession_manager';
    protected $_fname ;
    protected $_fid ;
    public function execute(){
        self::_getUserInfo();
        $this->_fid = Bingo_Http_Request::get('fid', -1);
        if($this->_fid == -1){
            Bingo_Log::warning('the fid is null!');
            $error = Tieba_Errcode::ERR_PARAM_ERROR;
            return self::_jsonRet($error, Tieba_Error::getErrmsg($error)); 
            
        }

        //登录检查
        if(!$this->_arrUserInfo['is_login']){
            Bingo_Log::warning('not login');
            $error = Tieba_Errcode::ERR_PARAM_ERROR;
            return self::_jsonRet($error, Tieba_Error::getErrmsg($error));

        }
        //权限检查
        if(!self::_checkPower()){
            Bingo_Log::warning('the user or the forum has no power! user_name:'.$this->_arrUserInfo['user_name'].'and forum_name:'.$this->_fname);
            $error = Tieba_Errcode::ERR_PARAM_ERROR;
            return self::_jsonRet($error, Tieba_Error::getErrmsg($error)); 
        }

        //提交到mis，进行审核
        $arrInput = array(
            'fid' => $this->_fid,
        );
        $res = Tieba_Service::call('headpic','checkIssue', $arrInput);
        echo json_encode($res);
        //Bingo_Page::assign('res', $res);
        //Bingo_setTpl("");
        return true;



    }

    //检查吧是否有对应的权限 + 用户是否是吧主
    protected function _checkPower(){
        if(!isset($this->_fid) || $this->_fid <= 0){
            return false;
        }
        $arrInput['forum_id'] = array($this->_fid);
        $forumInfo = Tieba_Service::call('forum', 'getFnameByFid', $arrInput);
        if(empty($forumInfo) || $forumInfo['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call service : getFnameByFid  failed');
            return false;
        }
        if($forumInfo['forum_name'] == false){
            Bingo_Log::warning('the fid is not exist, fid:'.$this->_fid);
            return false;
        }
        $this->_fname = $forumInfo['forum_name'][$this->_fid]['forum_name'];

        //检查吧是否有权限
        $key = iconv( "GBK","UTF-8",$this->_fname);
        $handleWordServer = Wordserver_Wordlist::factory();
        $arrBool = $handleWordServer->getValueByKeys(array($key),self::WORDLIST_NAME_WENZI);
        if($arrBool[$key] === null || empty($arrBool[$key])){
            Bingo_Log::warning('the forum has no power, forum_name : '.$key);
            return false;
        }


        //检查用户是否是吧主
        $arrInput['forum_id'] = $this->_fid;
        $arrInput['user_id'] = $this->_arrUserInfo['user_id'];
        $arrInput['user_ip'] = $this->_arrUserInfo['user_ip'];
        $arrRes= Tieba_Service::call('perm', 'getPerm', $arrInput);
        if(empty($arrRes) || $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call service perm: getPerm  failed input[' .serialize($arrInput) . "]out[" . serialize($arrRes) . "]");
            return false;
        }
        $arrPermInfo = $arrRes['output']['perm'];
        if($arrPermInfo[self::MANAGER_PERM] || $arrPermInfo[self::PROFESSION_PERM]) {
            return true;
        }
        return false;
    }
}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
