<?php
/**
 * 订单、账单查询 基类
 *
 * <AUTHOR>
 * @date 2016-11-02
 */
define('BINGO_ENCODE_LANG', 'UTF-8');
define("MODULE","Service_Libs_Bill");
class Service_Libs_Bill {

    public static $service_ie = 'utf-8';

    public function getInfoByAccountIdAndOrderId() {

    }

    public function getInfoByAccountIdAndOutOrderId() {

    }

    /**
     * test function
     * @param  Array $arrInput 入参
     * @return Array           出参
     */
    public static function test($arrInput) {

        // return self::_buildRet(Tieba_Errcode::ERR_SUCCESS);

    }

    /**
     * @brief return error info.
     * @param: int $errno 错误号 $data: 数据信息
     * @param: array $data: 数据信息
     * @return: array
     **/
    private static function _buildRet($errno, $data = null){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        );
    }
}
