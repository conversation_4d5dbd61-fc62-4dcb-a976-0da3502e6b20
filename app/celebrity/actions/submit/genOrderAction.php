<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-10-09
 * @comment json接口
 * @version
 * @brief 生成订单
 */
class genOrderAction extends Util_Base {

    public function _execute(){
        try {
            if (true !== Bingo_Http_Request::isPost()){
                throw new Exception("must post!",Tieba_Errcode::ERR_NOT_POST_METHOD);
            }
            
            //参数获取
            $forum_id = intval(Bingo_Http_Request::getNoXssSafe('forum_id', 0));
            $props_id= intval(Bingo_Http_Request::getNoXssSafe('props_id', 0));
            $props_num = intval(Bingo_Http_Request::getNoXssSafe('props_num', 0));
			$from = Bingo_Http_Request::getNoXssSafe('from', 'pc');
			// 0 关闭 1 打开
            $yy_pay_open = Bingo_Http_Request::getNoXssSafe('yy_pay_open', 0);
            // 0 未切换 1 已经切换
            $yy_is_convert = Bingo_Http_Request::getNoXssSafe('yy_is_convert', 0);

            Bingo_Log::pushNotice("ispv", 0);

            if ($this->_arrUserInfo['is_login'] !== true ){
                //未登陆
                throw new Exception("user need login!",Tieba_Errcode::ERR_USER_NOT_LOGIN);
            }
			
			//tbs验证
			$tbs = strval(Bingo_Http_Request::get('tbs',''));
			if( empty($tbs) ){
				throw new Exception("tbs is empty", Tieba_Errcode::ERR_PARAM_ERROR);
			}
			if( !Tieba_Tbs::check($tbs, true) ){
				//tbs验证失败
				throw new Exception("tbs check fail!", Tieba_Errcode::ERR_NVOTE_INVALID_TBS);
			}
			
            $user_id = $this->_arrUserInfo['user_id'];
			$user_ip = $this->_arrUserInfo['user_ip'];
			
            $intUserId = intval($user_id);
            $currency_type = 0;
            //获取Y币开关状态
            $bolYySwitch = Molib_Util_Yylive::isSwitchOpened($intUserId);
            if ($bolYySwitch) {
                if(!$yy_pay_open){
                    $this->_jsonRet(100000002, "服务器正T豆升级Y币中，请确认后重新使用该功能～～");
                    return;
                }
                //判断用户是否已进行Y币转换
                $bolConverted = $this->_checkTDouIsConverted($this->_arrUserInfo);

                if($bolConverted && !$yy_is_convert){
                    $this->_jsonRet(100000003, "您的T豆已转换成Y币，请确认是否使用Y币购买～～");
                    return;
                }

                //当不允许使用T豆的情况下，如果未进行Y币转换要提示用户切换
                if (!$bolConverted) {
                    $bolBackToTdou = Molib_Util_Yylive::isPcBackToTdou();
                    if(!$bolBackToTdou){
                        $this->_jsonRet(100000001, "服务器正T豆升级Y币中，请前往至T豆钱包确认转换Y币～～");
                        return;
                    }
                }else{
                    $currency_type = 1;
                }
            }
			
            $arrParams = array(
                'user_id'  => $user_id,
				'user_ip' => $user_ip,
                'forum_id'  => $forum_id,
                'props_id'  => $props_id,
                'props_num' => $props_num,
                'from'  => $from,
                'currency_type' => $currency_type,
            );
            $arrRet = Tieba_Service::call('celebrity', 'genOrderId', $arrParams, null, null, 'post', 'php', 'utf-8', 'local');
            if ( $arrRet === false || !isset($arrRet['errno']) ){
                throw new Exception("service call fail!",Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
			
			$data = isset($arrRet['data']) ? $arrRet['data'] : array();
			
			if(intval($arrRet['errno']) === 0){
				Bingo_Log::pushNotice("is_genorder_success", 1);
			}

            $this->_jsonRet($arrRet['errno'], $arrRet['errmsg'], $arrRet['data']);
        }catch(Exception $e){
            Bingo_Log::warning( "no=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), Tieba_Error::getUserMsg($e->getCode()));	
        }
    }



    /**
     * 判断用户的T豆余额是否转换成Y币
     * @param $userData 用户信息
     * @return bool true：> 0  false：===0
     */
    private function _checkTDouIsConverted($userData){
        if (empty($userData) || !isset($userData['Parr_scores']) || !isset($userData['Parr_scores']['scores_total'])){
            return true;
        }
        return intval($userData['Parr_scores']['scores_total']) === 0;
    }
}

