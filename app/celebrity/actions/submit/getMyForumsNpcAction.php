<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-11-27
 * @comment 获取我关注的吧的名人堂信息
 * @version
 */
class getMyForumsNpcAction extends Util_Base {

    /**
     * @param:null
     * @return:json
     **/
    public function _execute(){
        try {
            //参数获取
            $pn = intval(Bingo_Http_Request::getNoXssSafe('pn', 1));
            $ps = intval(Bingo_Http_Request::getNoXssSafe('ps', 20));
            $user_id = intval($this->_arrUserInfo['user_id']);
            
            Bingo_Log::pushNotice("ispv", 0);

			if(0 >= $user_id){
				Bingo_Log::warning('user is not login');
				return $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN,Tieba_Error::getErrmsg(Tieba_Errcode::ERR_USER_NOT_LOGIN));
			}
            
            $arrParams = array(
                'user_id' => $user_id,
                'pn' => $pn,
                'ps' => $ps,
            );
            $arrRet = Tieba_Service::call('celebrity', 'getMyForumsNpc', $arrParams, null, null, 'post', 'php', 'utf-8', 'local');
            if(false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
				Bingo_Log::warning('call celebrity.getMyForumsNpc error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
				return $this->_jsonRet($arrRet['errno'], $arrRet['errmsg']);
			}
            
            $data = $arrRet['data'];
            if( !empty($data) ){
                $data['page']['pn'] = $pn;
                $data['page']['ps'] = $ps;
                $data['page']['count_pn'] = ceil($data['page']['total']/$ps);
            }

            $this->_jsonRet($arrRet['errno'], $arrRet['errmsg'], $data);
        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), '未知错误');	
        }
    }
    
}