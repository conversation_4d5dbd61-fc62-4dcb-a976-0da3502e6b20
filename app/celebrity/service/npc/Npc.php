<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:04:03 11:51:41
 * @version 
 * @structs & methods(copied from idl.)
*/

class Service_Npc_Npc extends Service_Libs_Base{
    const DB_NPC_INFO_TABLE_NAME = 'npc_info';
    const DB_NPC_FANS_TABLE_NAME = 'npc_fans';
    const FORUM_MAX_NPC_NUM = 1;                   //吧内NPC最大数量
    
    const CELEBRITY_FORUM_ATTR = 'celebrity';
    
    private static $_npcInfos = null;
    private static $_userProps = null;
    private static $_npcBaseInfos = null;
    
    private static $_npcInfoFields = array(
        'id',
        'npc_id',
        'npc_name',
        'npc_real_name',
        'avatar',
        'forum_id',
        'forum_name',
        'fans_name',
        'status',
    );
    
    /**
     * @brief: 根据NPC_Id 获取NPC信息
     * @param: npc_id int
     * @return: npc_info array
     **/
    public static function getNpcInfoByNpcId($arrInput){
        if( !isset($arrInput['npc_id']) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        //input params
        $npc_id = intval($arrInput['npc_id']);
        $data = array();
        
        if( $npc_id <= 0 ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        $arrParams = array(
            'cond' => array(
                'npc_id=' => $npc_id,
                'status=' => 1,
            ),
        );
        $arrRet = self::_queryNpcInfoRecord($arrParams);
        if( $arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning(__FUNCTION__.' queryNpcInfoRecord error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }else{
            $data = $arrRet['data'][0];
        }
        
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $data);
    }
    
    /**
     * @brief: 获取我关注的吧的名人堂信息
     * @param: user_id——用户ID，ps——返回的记录数
     * @return: array
     **/
    public static function getMyForumsNpc($arrInput){
        if( !isset($arrInput['user_id']) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        //input params
        $user_id = intval($arrInput['user_id']);
        $pn = intval($arrInput['pn']);
        $pn = $pn > 0 ? $pn : 1;
        $ps = intval($arrInput['ps']);
        $ps = $ps > 0 ? $ps : 20;
        $res = array();
        
        if( $user_id <= 0 ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        //获取用户关注的全部吧列表
        $arrParams = array(
            'user_id' => $user_id,
            'page_no' => 1,
            'page_size' => 10000,
            'check_forum' => 1,
        );
        $arrRet = Tieba_Service::call('perm', 'getLikeForumList', $arrParams, null, null, 'post', 'php', 'utf-8');
        if( $arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning(__FUNCTION__.' perm.getLikeForumList error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $memberList = $arrRet['output']['member_list'];
        if( empty($memberList) || !is_array($memberList) ){
            //用户未关注任何吧
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
        }
        $fidsArr = array();
        foreach($memberList AS $item){
            $fidsArr[] = $item['forum_id'];
        }
        
        //根据吧ID查询NPC信息
        $start = ($pn - 1) * $ps;
        $limit = 'limit '.$start.','.$ps;
        $arrParams = array(
            'cond' => array(
                'forum_id' => $fidsArr,
                'status =' => 1,
            ),
            'append' => ' order by forum_id asc '.$limit,
        );
        $arrRet = self::_queryNpcInfoRecord($arrParams);
        if($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__FUNCTION__.' _queryNpcInfoRecord error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        
        $npcInfoArr = $arrRet['data'];
        if( empty($npcInfoArr) ){
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
        }
        
        $ids = array();
        $npc = array();
        foreach($npcInfoArr AS $item){
            $ids[] = intval($item['npc_id']);
            
            $npc[]['npc_info'] = array(
                'npc_avatar' => $item['avatar'],
                'npc_forum_id' => $item['forum_id'],
                'npc_forum_name' => $item['forum_name'],
                'npc_id' => $item['npc_id'],
                'npc_name' => $item['npc_name'],
                'total_person' => 0,
            );
        }
        
        //批量获取NPC的助攻数据
        $signNum = null;
        if( !empty($ids) ){
            $arrParams = array(
                'ids' => $ids,
            );
            $arrRet = Service_Support_Support::mgetNPCSignNum($arrParams);
            if( $arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS ){
                Bingo_Log::warning(__FUNCTION__.' mgetNPCSignNum error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            }else{
                $signNum = $arrRet['data'];
            }
        }
        
        if( !empty($signNum) && !empty($npc) ){
            foreach($npc AS $key=>$item){
                $npc_id = intval($item['npc_info']['npc_id']);
                $npc[$key]['npc_info']['total_person'] = intval($signNum[$npc_id]['total_person']);
            }
        }
        
        //计算总记录数
        $arrParams = array(
            'field' => array(
                'count(1) AS total',
            ),
            'cond' => array(
                'forum_id' => $fidsArr,
                'status =' => 1,
            ),
        );
        $arrRet = self::_queryNpcInfoRecord($arrParams);
        if( $arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning(__FUNCTION__.' _queryNpcInfoRecord error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
        }
        $total = intval($arrRet['data'][0]['total']);
        
        $res = array(
            'npc' => $npc,
            'page' => array(
                'total' => $total,
            ),
        );
        
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
    }
    
    /**
     * 获取所有开通名人堂的吧，及吧内名人信息
     * @param: null
     * @return: array
     **/
    public static function getNpcForum(){
        $arrParams = array(
            'cond' => array(
                'status=' => 1,
            ),
        );
        $arrRet = self::_queryNpcInfoRecord($arrParams);
        if( $arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('DB queryNpcInfoRecord error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $record = $arrRet['data'];
        $data = array();
        if( !empty($record) && is_array($record) ){
            foreach($record AS $key=>$value){
                $forum_id = $value['forum_id'];
                $data[$forum_id][] = array(
                    'npc_id' => $value['npc_id'],
                    'npc_name' => $value['npc_name'],
                    'fans_name' => $value['fans_name'],
                );
            }
        }
        
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $data);
    }
    
    /*
     * 获取NPC道具信息
     */
    public static function getNpcProps($arrInput){
        if( !isset($arrInput['npc_id']) || !isset($arrInput['user_id']) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        //input params
        $npc_id = $arrInput['npc_id'];
        $user_id = $arrInput['user_id'];
        
        if( $npc_id <= 0 || $user_id <= 0 ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        $arrParams = array(
            'npc_uid' => $npc_id,
        );
        $arrRet = Service_Props_Props::showPropsInfoByNpcId($arrParams);
        if( $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('func props.showPropsInfoByNpcId error.[input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $props = $arrRet['data'];
        if( empty($props) || !is_array($props)){
            Bingo_Log::warning('props is empty.[input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return self::_errRet(Tieba_Errcode::ERR_NO_RECORD);
        }
        
        //获取NPC信息
        $npc_info = null;
        $arrParams = array(
            'npc_id' => $npc_id,
        );
        $arrRet = self::getNpcInfoByNid($arrParams);
        if( $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('func_getNpcInfoByNid error.[input='.serialize($arrParams).'][out='.serialize($arrRet).']');
        }else{
            $npc_info = $arrRet['data'];
        }
        
        //获取用户签到信息
        $user_sign = null;
        $arrRet = Service_Support_Support::_getUserSignInfo($user_id, $npc_id);
        if( $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('func support._getUserSignInfo error.[user_id='.$user_id.'; npc_id='.$npc_id.'][out='.serialize($arrRet).']');
        }else{
            $user_sign = $arrRet['data'];
        }
        
        foreach($props AS &$item){
            $is_used = 0;
            $is_unlock = 0;
            $lock_npc_percent = 0;
            $can_get = 0;
            
            $is_unlock = (intval($item['lock_npc_level']) > intval($npc_info['cur_level'])) ? 0 : 1;
            if( $is_unlock == 1 ){
                $lock_npc_percent = 100;
                $is_used = self::_isUsedOfProps($user_id, $item) ? 1 : 0;
                $can_get = intval($user_sign['my_continue_days']) >= intval($item['limit_sign_level']) ? 1 : 0;
            }else{
                $lock_npc_percent = floor(intval($npc_info['cur_score']) / intval($item['lock_npc_score']) * 100);
                $lock_npc_percent = $lock_npc_percent > 100 ? 100 : $lock_npc_percent;
            }
            
            if( $can_get == 1 && $item['lock_need_vip'] == 1 ){
                $isVip = Service_Support_Support::_isForumVip($user_id, $npc_info['npc_forum_id']);
                $can_get = $isVip ? 1 : 0;
            }
            
            //如果道具已过期，或用户已经失去获取道具条件can_get=0，则is_used应置为0
            if( $is_used == 1 && $can_get == 0 ){
                $is_used = 0;
            }
            
            $item['is_used'] = $is_used;
            $item['is_unlock'] = $is_unlock;
            $item['lock_npc_percent'] = $lock_npc_percent;
            $item['can_get'] = $can_get;
        }
        
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $props);
    }

    /**
     * @brief: 批量验证用户是否可以领取NPC的道具
     * @param:
     * @return:
     **/
    public static function mcanGetNpcProps($arrInput){
        if( !isset($arrInput['user_id']) || !isset($arrInput['props_ids']) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        //input params
        $user_id = $arrInput['user_id'];
        $props_ids = $arrInput['props_ids'];
        $propsInfoArr = array();
        $npcInfoArr = array();
        $npcIdArr = array();
        $permInfoArr = array();
        $userSignArr = array();
        $userInfo = array();
        $rtn = array();
        $now = time();
        
        if( $user_id <= 0 || empty($props_ids) || !is_array($props_ids) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        //获取用户信息
        $arrParams = array(
            'user_id' => $user_id,
            'get_icon' => 0,
        );
        $arrRet = Tieba_Service::call("user","getUserData", $arrParams, null, null, 'post', 'php', 'utf-8');
        if( $arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('service user.getUserData error.[input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $userInfo = $arrRet['user_info'][0];
        
        if( empty($userInfo) ){
            Bingo_Log::warning('userInfo is empty.[input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        
        //批量获取道具信息
        $arrParams = array(
            'cond' => array(
                'props_id' => $props_ids,
            ),
        );
        $arrRet = Service_Props_Props::query($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' props.query error [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        if( empty($arrRet['data']) ){
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $rtn);
        }
        foreach($arrRet['data']  AS $item){
            $propsId = intval($item['props_id']);
            $npc_id = intval($item['npc_id']);
            
            $propsInfoArr[$propsId] = $item;
            $npcIdArr[] = $npc_id;
        }
        
        $npcIdArr = array_unique($npcIdArr);
        if( empty($propsInfoArr) || empty($npcIdArr) ){
            Bingo_Log::warning("props_is is invalid.[" . serialize ( $arrInput ) . "]");
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }
        
        //批量获取NPC等级信息
        $arrParams = array(
            'cond' => array(
                'npc_id' => $npcIdArr,
            ),
        );
        $arrRet = self::_queryNpcInfoRecord($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' _queryNpcInfoRecord error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $record = $arrRet['data'];
        if( empty($record) || !is_array($record) ){
            Bingo_Log::warning(__FUNCTION__.' _queryNpcInfoRecord result is empty. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return self::_errRet(Tieba_Errcode::ERR_NO_RECORD);
        }
        $req = array();
        foreach($record AS $item){
            $req[] = array(
                'user_id' => $item['npc_id'],
                'forum_id' => $item['forum_id'],
            );
        }
        $arrParams = array(
            'req' => $req,
        );
        $arrRet = Tieba_Service::call('perm', 'mgetUserForumLevel', $arrParams, null, null, 'post', 'php', 'utf-8');
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning('service perm.mgetUserForumLevel error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $score_info = $arrRet['score_info'];
        foreach($score_info AS $perm){
            $npc_id = $perm['user_id'];
            $permInfoArr[$npc_id] = $perm;
        }
        
        //批量获取用户签到信息
        $req = array();
        foreach($npcIdArr as $item){
            $req[] = array(
                'user_id' => $user_id,
                'npc_id' => $item,
            );
        }
        $arrParams = array(
            'req' => $req,
        );
        $arrRet = Service_Support_Support::mgetUserSignInfo($arrParams);
        if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
            Bingo_Log::warning(__FUNCTION__.' support.mgetUserSignInfo error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $userSignArr = $arrRet['data'];
        
        //批量验证用户是否可以领取道具
        foreach($props_ids AS $propId){
            $propId = intval($propId);
            if( $propId <= 0 || !isset($propsInfoArr[$propId]) ){
                continue;
            }
            
            $can_get = 0;
            $props = $propsInfoArr[$propId];
            $npc_id = $props['npc_id'];
            $npc_info = $permInfoArr[$npc_id];
            $user = $userSignArr[$user_id][$npc_id];
            
            do{
                if($props['lock_npc_score'] > $npc_info['cur_score']){
                    break;
                }
                if($props['limit_sign_level'] > $user['my_continue_days']){
                    break;
                }
                if($props['lock_need_vip'] == 1){
                    $isVip = 0;
                    $forum_id = $npc_info['forum_id'];
                    $mParr_props = $userInfo['mParr_props'];
                    
                    //超级会员
                    if( $mParr_props['level']['props_id'] == 2 && $mParr_props['level']['end_time'] > $now ){
                        $isVip = 1;
                    }
                    
                    if( $isVip ==0 && isset($mParr_props['forum_member'][$forum_id]) ){
                        $endTime = $mParr_props['forum_member'][$forum_id]['end_time'];
                        if( $endTime > $now ){
                            $isVip = 1;
                        }
                    }
                    
                    if( !$isVip ){
                        break;
                    }
                }
                $can_get = 1;
            }while(0);

            if( $can_get == 1 ){
                $sign_time = intval($user['sign_time']);
                $sign_time_str = date('Y-m-d 00:00:00', $sign_time);
                $sign_time = strtotime($sign_time_str);
                $sign_time = $sign_time + 24 * 3600 * 2;
                $max_time  = time() + 365 * 24 * 3600;

                $sign_time = ($props['limit_sign_level'] == 0) ? $max_time : $sign_time;

                $user['sign_time'] = $sign_time;
            }

            $res = array(
                'user' => $user,
                'can_get' => $can_get,
            );
            $rtn[$propId] = $res;
        }
        
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $rtn);
    }
    
    /*
     * 验证用户是否可以领取NPC的道具
     */
    public static function canGetNpcProps($arrInput){
        if( !isset($arrInput['user_id']) || !isset($arrInput['props_id']) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        //input params
        $user_id = $arrInput['user_id'];
        $props_id = $arrInput['props_id'];
        
        if( $user_id <= 0 || $props_id <= 0 ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        //获取道具基本信息
        $arrParams = array(
            'props_id' => $props_id,
        );
        $arrRet = Service_Props_Props::showPropsInfo($arrParams);
        if( $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            return $arrRet;
        }
        $props = $arrRet['data'];
        $npc_id = $props['npc_id'];
        //获取NPC信息
        $arrParams = array(
            'npc_id' => $npc_id,
        );
        $arrRet = self::getNpcInfoByNid($arrParams);
        if( $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            return $arrRet;
        }
        $npc_info = $arrRet['data'];
        //获取用户签到信息
        $arrRet = Service_Support_Support::_getUserSignInfo($user_id, $npc_id);
        if( $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            return $arrRet;
        }
        $user = $arrRet['data'];
        
        //验证用户是否可以领取道具
        $can_get = 0;
        do{
            if($props['lock_npc_level'] > $npc_info['cur_level']){
                break;
            }
            if($props['limit_sign_level'] > $user['my_continue_days']){
                break;
            }
            if($props['lock_need_vip'] == 1){
                $forum_id = $npc_info['npc_forum_id'];
                $isVip = Service_Support_Support::_isForumVip($user_id, $forum_id);
                if( !$isVip ){
                    break;
                }
            }
            $can_get = 1;
        }while(0);
        
        if( $can_get == 1 ){
            $sign_time = intval($user['sign_time']);
            $sign_time_str = date('Y-m-d 00:00:00', $sign_time);
            $sign_time = strtotime($sign_time_str);
            $sign_time = $sign_time + 24 * 3600 * 2;
            $max_time  = time() + 365 * 24 * 3600;
            
            $sign_time = ($props['limit_sign_level'] == 0) ? $max_time : $sign_time;
            
            $user['sign_time'] = $sign_time;
        }
        
        $res = array(
            'npc_info' => $npc_info,
            'user' => $user,
            'props' => $props,
            'can_get' => $can_get,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
    }
	
    /*
     * 根据吧ID 获取吧内角色信息
     */
    public static function getNpcInfoByFid($arrInput){
        if( !isset($arrInput['forum_id']) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input param
        $forum_id = $arrInput['forum_id'];

        if( $forum_id <= 0 ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrParams = array(
            'cond' => array(
                'forum_id=' => $forum_id,
                'status=' => 1,
            ),
        );
        $arrRet = self::_queryNpcInfoRecord($arrParams);
        if( $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('func_queryNpcInfoRecord error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
        }
        
        return $arrRet;
    }
	
    /*
     * 根据NPC ID 获取NPC详细信息
     */
    public static function getNpcInfoByNid($arrInput){
        if( !isset($arrInput['npc_id']) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input param
        $npc_id = intval($arrInput['npc_id']);
        $npc_info = array();

        if( $npc_id <= 0 ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        if( isset(self::$_npcInfos[$npc_id]) ){
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, self::$_npcInfos[$npc_id]);
        }

        //获取基本信息
        $arrNPCBaseInfo = array();
        if( !empty(self::$_npcBaseInfos[$npc_id]) ){
            $arrNPCBaseInfo = self::$_npcBaseInfos[$npc_id];
        }else{
            $arrParams = array(
                'cond' => array(
                    'npc_id=' => $npc_id,
                    'status=' => 1,
                ),
            );
            $arrRet = self::_queryNpcInfoRecord($arrParams);
            if( $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
                Bingo_Log::warning('func_queryNpcInfoRecord error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                return $arrRet;
            }
            $arrNPCBaseInfo = $arrRet['data'][0];
        }

        //获取NPC用户信息
        $arrNPCUserInfo = array();
        $npc_vip_level = 0;
        $arrParams = array(
            'user_id' => $npc_id,
            'get_icon' => 0,
        );
        $arrRet = Tieba_Service::call("user","getUserData", $arrParams, NULL, NULL, 'post', 'php', 'utf-8');
        if( $arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('call user.getUserData error. [input='.serialize($arrParams).'][output='.serialize($arrRet).']');
        }else{
            $arrNPCUserInfo = $arrRet['user_info'][0];
            $arrNPCUserInfo['user_nickname'] = isset($arrNPCUserInfo['user_nickname']) ? $arrNPCUserInfo['user_nickname'] : $arrNPCBaseInfo['npc_name'];
            $end_time = $arrNPCUserInfo['mParr_props']['level']['end_time'];
            $props_id = $arrNPCUserInfo['mParr_props']['level']['props_id'];
            $npc_vip_level = ($end_time >= time()) ? $props_id : 0;
        }

        //获取NPC吧内等级信息
        $arrNPCGradeInfo = array();
        $arrParams = array(
            'user_id' => $npc_id,
            'forum_id' => $arrNPCBaseInfo['forum_id'],
            'user_ip' => 0,
        );
        $arrRet = Tieba_Service::call("perm","getPerm", $arrParams, NULL, NULL, 'post', 'php', 'utf-8');
        if( $arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('call perm.getPerm error. [input='.serialize($arrParams).'][output='.serialize($arrRet).']');
        }else{
            $arrNPCGradeInfo = $arrRet['output']['grade'];
        }

        //获取本月签到总人数和粉丝数
        $arrRet = Service_Support_Support::getNPCSignNum($npc_id);
        $total_person = intval($arrRet['data']['total_person']);
        $total_fans = intval($arrRet['data']['total_fans']);

        //获取角色排名信息
        $npc_rank = 0;
        $arrParams = array(
            'npc_id' => $npc_id,
            'by_day' => 1,
        );
        $arrRet = Service_Rank_Rank::getNpcRank($arrParams);
        if( $arrRet['errno'] == Tieba_Errcode::ERR_SUCCESS ){
            $npc_rank = $arrRet['data'];
        }

        $npc_avatar = empty($arrNPCUserInfo['user_name']) ? '' : Tieba_Ucrypt::encode($npc_id, $arrNPCUserInfo['user_name']);
        $cur_level = self::_genNPCLevel($arrNPCGradeInfo['cur_score']);
        $score_left = $cur_level * Service_Props_PropsDefine::$intLevelUpStep - $arrNPCGradeInfo['cur_score'];

        $res = array(
            'total_person' => $total_person,
            'total_fans' => $total_fans,
            'npc_id' => $npc_id,
            'npc_name' => $arrNPCUserInfo['user_nickname'],
            'npc_real_name' => $arrNPCUserInfo['user_name'],
            'npc_avatar' => $npc_avatar,
            'npc_props' => '',
            'npc_vip_level' => $npc_vip_level,
            'npc_rank' => $npc_rank,
            'npc_forum_id' => $arrNPCBaseInfo['forum_id'],
            'npc_forum_name' => $arrNPCBaseInfo['forum_name'],
            'npc_fans_name' => $arrNPCBaseInfo['fans_name'],
            'level_id' => $arrNPCGradeInfo['level_id'],
            'level_name' => $arrNPCGradeInfo['level_name'],
            'cur_score' => $arrNPCGradeInfo['cur_score'],
            'cur_level' => $cur_level,
            'score_left' => $score_left,
        );
        self::$_npcInfos[$npc_id] = $res;

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $res);
    }
    
    /*
     * 根据吧ID删除吧内组织信息
     */
    public static function removeNpcFansInfo($arrInput){
        if( !isset($arrInput['forum_id']) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        //input param
        $forum_id = intval($arrInput['forum_id']);
        
        if( $forum_id <= 0 ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        $arrParams = array(
            'field' => array(
                'status=' => 0,
            ),
            'cond' => array(
                'forum_id=' => $forum_id,
            ),
        );
        $arrRet = self::_updateNpcFansRecord($arrParams);
        
        if( $arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('db.updateNpcFansRecord error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        
        $arrParams = array(
            'forum_id' => $forum_id,
            'attr_name' => self::CELEBRITY_FORUM_ATTR,
        );
        $arrRet = Tieba_Service::call("forum", "delForumAttr", $arrParams, NULL, NULL, 'post', 'php', 'utf-8');
        if ( $arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call delForumAttr error.[input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
        }
        
        return $arrRet;
    }

    /*
     * 设置NPC吧内组织信息
     */
    public static function setNpcFansInfo($arrInput){
        if( !isset($arrInput['forum_id']) || !isset($arrInput['fans_name']) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        //input params
        $forum_id = intval($arrInput['forum_id']);
        $fans_name = trim($arrInput['fans_name']);
        $fans_head_pic = trim($arrInput['fans_head_pic']);
        $fans_tail_pic = trim($arrInput['fans_tail_pic']);
        
        if( $forum_id <= 0 || empty($fans_name) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        $arrParams = array(
            'cond' => array(
                'forum_id=' => $forum_id,
            ),
        );
        $arrRet = self::_queryNpcFansRecord($arrParams);
        if($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('db func_queryNpcFansRecord error.[input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $record = $arrRet['data'][0];
        
        if( empty($record) ){
            $arrParams = array(
                'field' => array(
                    'forum_id' => $forum_id,
                    'fans_name' => $fans_name,
                    'fans_head_pic' => $fans_head_pic,
                    'fans_tail_pic' => $fans_tail_pic,
                    'status' => 1,
                ),
            );
            $arrRet = self::_insertNpcFansRecord($arrParams);
        }else{
            $arrParams = array(
                'field' => array(
                    'fans_name=' => $fans_name,
                    'fans_head_pic=' => $fans_head_pic,
                    'fans_tail_pic=' => $fans_tail_pic,
                    'status=' => 1,
                ),
                'cond' => array(
                    'forum_id=' => $forum_id,
                ),
            );
            $arrRet = self::_updateNpcFansRecord($arrParams);
        }
        if( $arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('db error.[input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        
        return $arrRet;
    }
    
    /*
     * 删除NPC信息
     */
    public static function removeNpcInfo($arrInput){
        if( !isset($arrInput['forum_id']) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        //input param
        $forum_id = intval($arrInput['forum_id']);
        
        if( $forum_id <= 0 ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        $arrParams = array(
            'field' => array(
                'status=' => 0,
            ),
            'cond' => array(
                'forum_id=' => $forum_id,
            ),
        );
        $arrRet = self::_updateNpcInfoRecord($arrParams);
        if( $arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('db.updateNpcInfoRecord error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        
        $arrParams = array(
            'cond' => array(
                'forum_id=' => $forum_id,
            ),
        );
        $arrRet = self::_queryNpcInfoRecord($arrParams);
        if( $arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('db.queryNpcInfoRecord error. [input='.serialize($arrRet).'][out='.serialize($arrRet).']');
        }
        
        return $arrRet;
    }
    
    /*
     * 设置NPC信息
     */
    public static function setNpcInfo($arrInput){
        if( !isset($arrInput['npc_id']) || !isset($arrInput['forum_id']) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        //input params
        $npc_id = intval($arrInput['npc_id']);
        $forum_id = intval($arrInput['forum_id']);
        $syn = isset($arrInput['syn']) ? intval($arrInput['syn']) : 1;
        
        if( $forum_id <= 0 || $npc_id <= 0 ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        //获取吧名
        $arrParams = array(
            'forum_id' => array($forum_id)
        );
        $arrRet = Tieba_Service::call('forum', 'getFnameByFid', $arrParams, NULL, NULL, 'post', 'php','utf-8');
        if ( $arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning("call getFnameByFid error.[input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $forum_name = $arrRet['forum_name'][$forum_id]['forum_name'];
        if( empty($forum_name) ){
            Bingo_Log::warning("call getFnameByFid error, forum_name empty.[input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        
        $arrParams = array(
            'cond' => array(
                'forum_id=' => $forum_id,
                'status=' => 1,
            ),
        );
        $arrRet = self::_queryNpcFansRecord($arrParams);
        if($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('db func_queryNpcFansRecord error.[input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $record = $arrRet['data'][0];
        
        if( empty($record) ){
            Bingo_Log::warning('NPC Fans is no record. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');   
            return self::_errRet(Tieba_Errcode::ERR_NO_RECORD);
        }
        
        $arrParams = array(
            'cond' => array(
                'npc_id=' => $npc_id,
            ),
        );
        $arrRet = self::_queryNpcInfoRecord($arrParams);
        if( $arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('db func_queryNpcInfoRecord error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $npcInfo = $arrRet['data'][0];
        
        //获取npc信息
        $arrParams = array(
            'user_id' => $npc_id,
            'get_icon' => 0,
        );
        $arrRet = Tieba_Service::call('user', 'getUserData', $arrParams, null, null, 'post', 'php', 'utf-8');
        if( $arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning(__FUNCTION__.' user.getUserData error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $userInfo = $arrRet['user_info'][0];
        if( empty($userInfo) ){
            Bingo_Log::warning(__FUNCTION__.' userInfo is empty. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return self::_errRet(Tieba_Errcode::ERR_NO_RECORD);
        }
        
        $npc_avatar = Tieba_Ucrypt::encode($npc_id, $userInfo['user_name']);

        if( empty($npcInfo) ){
            $arrParams = array(
                'field' => array(
                    'npc_id' => $npc_id,
                    'avatar' => $npc_avatar,
                    'forum_id' => $forum_id,
                    'forum_name' => $forum_name,
                    'fans_name' => $record['fans_name'],
                    'status' => 1,
                ),
            );
            $arrRet = self::_insertNpcInfoRecord($arrParams);
        }else{
            $arrParams = array(
                'field' => array(
                    'avatar=' => $npc_avatar,
                    'forum_name=' => $forum_name, 
                    'status=' => 1,
                ),
                'cond' => array(
                    'npc_id=' => $npc_id,
                    'forum_id=' => $forum_id,
                ),
            );
            $arrRet = self::_updateNpcInfoRecord($arrParams);
        }
        if( $arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('db error.[input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        
        //同步信息到吧属性
        if( $syn ){
            $arrRet = self::_syncNpcFansInfoToForum($forum_id);
            if( $arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
                Bingo_Log::warning('func_syncNpcFansInfoToForum error.[forum_id='.$forum_id.'][out='.serialize($arrRet).']');
            }
        }
        
        return $arrRet;
    }

    /**
    * @brief 累加用户npc亲密度
    * @param array
    * @return array
    **/
    public static function setIntimate($arrInput){
        $user_id = intval($arrInput['user_id']);
        $npc_id  = intval($arrInput['npc_id']);
        $type    = $arrInput['type'];
        $score   = intval($arrInput['score']);

        if ($user_id<=0 || $npc_id<=0 || $score<=0)
        {
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $redisKey = self::_genInimateRedisKey($user_id, $npc_id);
        $arrParam = array(
            'key' => $redisKey,
            'step'=> $score,
        );
        if ($type == 'dec')
        {
            $ret = Util_Redis::DECRBY($arrParam);
        }
        else
        {
            $ret = Util_Redis::INCRBY($arrParam);
        }

        if ($ret === false)
        {
            Bingo_Log::warning('call redis failed [input]' . serialize($arrParam));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $ret);
    }

    /**
    * @brief 获取用户npc亲密度
    * @param array
    * @return array
    **/
    public static function getIntimate($arrInput){
        $user_id = intval($arrInput['user_id']);
        $npc_id  = intval($arrInput['npc_id']);
        if ($user_id<=0 || $npc_id<=0)
        {
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $redisKey = self::_genInimateRedisKey($user_id, $npc_id);
        $arrParam = array(
            'key' => $redisKey,
        );

        $ret = Util_Redis::GET($arrParam);
        if ($ret === false)
        {
            Bingo_Log::warning('call redis failed [input]' . serialize($arrParam));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, intval($ret));
    }
    
    /*
     * 验证道具是否在使用中
     */
    private static function _isUsedOfProps($user_id, $props){
        $userProps = null;
        $isUsed = false;
        
        if( $props['props_category'] == Service_Props_Props::CELEBRITY_PROPS_CATEGORY_AI ){
            //自动顶贴机器人，用户自动获得
            return true;
        }
        
        if( $props['props_id'] == '2000022' ){
            //英三嘉哥婚姻礼堂，用户自动获得
            return true;
        }
        
        if( !isset(self::$_userProps[$user_id]) ){
            $arrParams = array(
                'user_id' => $user_id,
                'isUsed' => 1,
            );
            $arrRet = Tieba_Service::call("tbmall","getUserPropsFromDb", $arrParams, NULL, NULL, 'post', 'php', 'utf-8');
            if( $arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
                Bingo_Log::warning('tbmall.getUserPropsFromDb. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            }else{
                self::$_userProps[$user_id] = $arrRet['data'];
            }
        }
        $props_id = $props['props_id'];
        $userProps = self::$_userProps[$user_id];
        
        if( $props['props_category'] == Service_Props_Props::TBMALL_PROPS_CATEGORY_ICON ){
            $arrParams = array(
                'user_id' => $user_id,
                'name' => $props['name'],
                'level' => $props['props_type'],
            );
            $arrRet = Tieba_Service::call("icon","getUserIconByNameLevel", $arrParams, NULL, NULL, 'post', 'php', 'utf-8');
            if( $arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
                Bingo_Log::warning('service icon.getUserIconByNameLevel error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            }
            
            $isUsed = intval($arrRet['data']['end_time']) >= time() ? true : false;
        }
        else if( $props['props_category'] == Service_Props_Props::TBMALL_PROPS_CATEGORY_HOME ){
            $arrParams = array(
                'user_id' => $user_id,
                'ie' => 'utf-8'
            );
            $arrRet = Tieba_Service::call("user","getUserData",$arrParams,NULL, NULL, 'post', 'php');
            if( $arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning("call getUserData error.[input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
            }
            $bg_id = intval($arrRet['user_info'][0]['bg_id']);
            
            $isUsed = ($bg_id == $props_id) ? true : false;
        }
        else if( !empty($userProps) && is_array($userProps) ){
            $now = time();
            foreach( $userProps AS $item ){
                if( $item['props_id'] == $props_id && $item['open_status'] == 1 ){
                    $isUsed = true;
                    break;
                }
            }
        }

        return $isUsed;
    }
    
    /**
     * @brief: 计算NPC等级，每1000点经验为1级，初始等级为1
     * @param: int
     * @return: int
    **/
    private static function _genNPCLevel($score){
        $level = floor($score / Service_Props_PropsDefine::$intLevelUpStep) + 1;
        
        return $level;
    }
    
    /**
     * @brief: 查询NPC信息
     * @param: array
     * @return: array
     **/
    public static function _queryNpcInfoRecord($arrInput){
        $arrFields = self::$_npcInfoFields;
        
        if(!isset($arrInput['field'])){
            $arrInput['field'] = $arrFields;
        }
        $arrInput['table'] = self::DB_NPC_INFO_TABLE_NAME;
        return Util_Db::select($arrInput);
    }
    
    /*
     * 添加NPC信息
     */
    private static function _insertNpcInfoRecord($arrInput){
        $arrInput['table'] = self::DB_NPC_INFO_TABLE_NAME;
        return Util_Db::insert($arrInput);
    }
    
    /*
     * 更新NPC信息
     */
    private static function _updateNpcInfoRecord($arrInput){
        $arrInput['table'] = self::DB_NPC_INFO_TABLE_NAME;
        return Util_Db::update($arrInput);
    }
    
    /*
     * 查询NPC组织表信息
     */
    private static function _queryNpcFansRecord($arrInput){
        $arrFields = array(
            'id', 'forum_id', 'fans_name', 'fans_head_pic',
            'fans_tail_pic', 'ext1', 'ext2', 'status',
        );
        
        if( !isset($arrInput['field']) ){
            $arrInput['field'] = $arrFields;
        }
        $arrInput['table'] = self::DB_NPC_FANS_TABLE_NAME;
        return Util_Db::select($arrInput);
    }
    
    /*
     * 添加信息到NPC组织表
     */
    private static function _insertNpcFansRecord($arrInput){
        $arrInput['table'] = self::DB_NPC_FANS_TABLE_NAME;
        return Util_Db::insert($arrInput);
    }
    
    /*
     * 更新信息到NPC组织表
     */
    private static function _updateNpcFansRecord($arrInput){
        $arrInput['table'] = self::DB_NPC_FANS_TABLE_NAME;
        return Util_Db::update($arrInput);
    }

    /**
    * @brief 获取npc亲密度redis key
    * @param npc_id user_id
    * @return string
    **/
    private static function _genInimateRedisKey($user_id, $npc_id){
        if (empty($user_id) || empty($npc_id)){
            return '';
        }
        return Service_Libs_Define::NPC_INTIMATE_PREFIX . $npc_id . '_' . $user_id;
    }
    
    /*
     * 将角色组织信息更新到吧属性
     */
    private static function _syncNpcFansInfoToForum($forum_id){
        $arrParams = array(
            'cond' => array(
                'forum_id=' => $forum_id,
                'status=' => 1,
            ),
        );
        $arrRet = self::_queryNpcFansRecord($arrParams);
        if($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('db func_queryNpcFansRecord error.[input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $npcFansInfo = $arrRet['data'][0];
        
        if( empty($npcFansInfo) ){
            Bingo_Log::warning('db record is empty. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return self::_errRet(Tieba_Errcode::ERR_NO_RECORD);
        }
        
        $arrParams = array(
            'cond' => array(
                'forum_id=' => $forum_id,
                'status=' => 1,
            ),
        );
        $arrRet = self::_queryNpcInfoRecord($arrParams);
        if($arrRet === false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('db func_queryNpcFansRecord error.[input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $npcInfo = $arrRet['data'][0];
        
        if( empty($npcInfo) ){
            Bingo_Log::warning('db record is empty. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return self::_errRet(Tieba_Errcode::ERR_NO_RECORD);
        }
        
        $npc_id = $npcInfo['npc_id'];
        //获取NPC基本信息
        $npc_base_info = array();
        $arrParams = array(
            'npc_id' => $npc_id,
        );
        $arrRet = self::getNpcInfoByNid($arrParams);
        if( $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('func_service_Npc_Npc.getNpcInfoByNid error.[input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }else{
            $npc_base_info = $arrRet['data'];
        }
        
        //获取NPC道具信息
        $arrParams = array(
            'npc_uid' => $npc_id,
        );
        $arrRet = Service_Props_Props::showPropsInfoByNpcId($arrParams);
        if( $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('func props.showPropsInfoByNpcId error.[input='.serialize($arrParams).'][out='.serialize($arrRet).']');
            return $arrRet;
        }
        $props = $arrRet['data'];
        $prop = null;
        
        if( !empty($props) && is_array($props)){
            foreach($props AS $item){
                if( $npc_base_info['cur_level'] >= $item['lock_npc_level']){
                    $prop = $item;
                }else{
                    break;
                }
            }
        }
        
        $celebrity_status = Service_Vote_Vote::CELEBRITY_STATUS_NPC;
        if( in_array($npcInfo['fans_name'], Service_Libs_Define::$_thirdNPCFans) ){
            $celebrity_status = Service_Vote_Vote::CELEBRITY_STATUS_THIRD;
        }
        
        $value = array(
            'celebrity_status' => $celebrity_status,
            'fans_name' => trim($npcFansInfo['fans_name']),
            'fans_head_pic' => trim($npcFansInfo['fans_head_pic']),
            'fans_tail_pic' => trim($npcFansInfo['fans_tail_pic']),
        );
        $value['npc_info'][] = array(
            'npc_id' => $npc_base_info['npc_id'],
            'npc_name' => $npc_base_info['npc_name'],
            'npc_avatar' => $npc_base_info['npc_avatar'],
            'npc_vip_level' => $npc_base_info['npc_vip_level'],
            'npc_rank' => $npc_base_info['npc_rank'],
            'npc_fans_name' => $npc_base_info['npc_fans_name'],
            'level_id' => $npc_base_info['level_id'],
            'cur_level' => $npc_base_info['cur_level'],
            'level_name' => $npc_base_info['level_name'],
            'props_id' => strval($prop['props_id']),
            'props_title' => strval($prop['title']),
        );
        
        $arrParams = array(
            'forum_id' => $forum_id,
            'attr_name' => self::CELEBRITY_FORUM_ATTR,
            'attr_value' => $value,
        );
        $arrRet = Tieba_Service::call("forum", "setForumAttr", $arrParams, NULL, NULL, 'post', 'php', 'utf-8');
        if ( $arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call setForumAttr error.[input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
        }
        
        return $arrRet;
    }
}

?>