<?php
class addUserChanceAction extends Bingo_Action_Abstract
{
    const ERR_GET_MAX_USER_CHANCE = 1;

    private static $_strTbs;
    private static $_intActId;
    private static $_intCompId;
    private static $_intUserId;
    private static $_intForumId;
    
    public function execute()
    {
        $intErrno = self::_initParams();
        if ($intErrno !== Tieba_Errcode::ERR_SUCCESS)
        {
            $this->_build($intErrno);
            return false;
        }
        //对用户容错
        $arrOutput = array(
            'errno' => 0,
            'errmsg' => 'success',
        );
        //get component info
        $arrParam = array(
            'activity_id' => self::$_intActId,
        );
        $arrRes = Tieba_Service::call('genesis', 'getActivityInfo', $arrParam);
        if (false === $arrRes || $arrRes['errno'] !== 0)
        {
            Bingo_Log::warning("call genesis fail! ".serialize($arrRes).'_'.serialize($arrParam));
            //self::_build(Tieba_Errcode::ERR_SUCCESS);
            self::_build(Tieba_Errcode::ERR_SUCCESS, array('add_num' => intval(0)));
            return false;
        }
        if (intval($arrRes['output']['activity_info']['activity_status']) !== 2)
        {
            Bingo_Log::warning("activity_status error! ".$arrRes['output']['activity_info']['activity_status']);
            self::_build(Tieba_Errcode::ERR_SUCCESS, array('add_num' => intval(0)));
        }
        $intAddChance = $arrRes['output']['activity_info']['component_list'][self::$_intCompId]['extra_info']['activity_rule']['regular']['link_lottery_num'];
        if (empty($intAddChance) || intval($intAddChance) <= 0)
        {
            Bingo_Log::warning("activity not has link! ");
            //self::_build(Tieba_Errcode::ERR_SUCCESS);
            self::_build(Tieba_Errcode::ERR_SUCCESS, array('add_num' => intval(0)));
            return false;
        }
        $intTycheId = intval($arrRes['output']['activity_info']['component_list'][self::$_intCompId]['component_related_id']);
        $intInitChance = intval($arrRes['output']['activity_info']['component_list'][self::$_intCompId]['extra_info']['activity_rule']['regular']['init_lottery_num']);
        $intLimitChance = intval($arrRes['output']['activity_info']['component_list'][self::$_intCompId]['extra_info']['activity_rule']['regular']['limit_lottery_num']);
        //check user like this forum or not
        $arrParam = array(
            'forum_id' => self::$_intForumId,
            'user_id'  => self::$_intUserId,
            'user_ip'  => 0,
        );
        $arrRes = Tieba_Service::call('perm', 'getPerm', $arrParam);
        $intIsLike = intval($arrRes['output']['grade']['is_like']);
        if ($arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS)
        {
            Bingo_Log::warning("call perm::getPerm fail! ".serialize($arrRes).'_'.serialize($arrParam));
            //self::_build(Tieba_Errcode::ERR_SUCCESS);
            self::_build(Tieba_Errcode::ERR_SUCCESS, array('add_num' => intval(0)));
            return false;
        }       
        if ($intIsLike !== 1)
        {
            Bingo_Log::warning("user not like this forum! ".serialize($arrRes).'_'.serialize($arrParam));
            self::_build(Tieba_Errcode::ERR_MO_UO_NEED_LIKE_FORUM);
            return false;
        }
        $arrRes = self::_addUserChance($intTycheId, self::$_intUserId, $intInitChance, $intLimitChance, $intAddChance);    
        if (false === $arrRes)
        {
            Bingo_Log::warning("add user chance false!");
            self::_build(Tieba_Errcode::ERR_SUCCESS, array('add_num' => intval(0)));
        } 
        self::_build(Tieba_Errcode::ERR_SUCCESS, array('add_num' => intval($arrRes)));
        return true;
    }

    private static function _addUserChance($intTycheId, $intUserId, $intInitChance, $intLimitChance, $intAddChance, $intAddType = 0)
    {
        $arrParam = array(
            'award_act_id'   => $intTycheId,
            'user_id'        => $intUserId,
            'rec_start_time' => 0,
            'chance_default_count' => $intInitChance,
        );
        $arrRes = Tieba_Service::call('tyche', 'getUserChance', $arrParam);
        if (false === $arrRes || $arrRes['errno'] !== 0)
        {
            Bingo_Log::warning("call tyche::getUserChance fail! ".serialize($arrRes).'_'.serialize($arrParam));
            return false;
        }
        $intDaytime = strtotime(date('Y-m-d'));
        $intCount = 0;
        foreach ($arrRes['data']['chance_record'] as $chance_rec) 
        {
            if ($chance_rec['op_time'] >= $intDaytime && $chance_rec['chance_count'] > 0) 
            {
                $intCount += $chance_rec['chance_count'];
            }
        }
        $intDiff = $intLimitChance - $intCount;
        if ($intDiff > 0)
        {
            $arrParam = array(
                'user_id' => $intUserId,
                'award_act_id' => $intTycheId,
                'chance_type'  => $intAddType,
                'chance_count' => min($intDiff, $intAddChance),
            );
            $arrRes = Tieba_Service::call('tyche', 'addUserChance', $arrParam);
            if (false === $arrRes || $arrRes['errno'] !== 0)
            {
                Bingo_Log::warning("call tyche::addUserChance fail! ".serialize($arrRes).'_'.serialize($arrParam));
                return false;
            }
        }
        return min($intDiff, $intAddChance);
    }

    private static function _initParams()
    {
        self::$_strTbs    = strval(Bingo_Http_Request::get('tbs',          ''));
        self::$_intActId  = intval(Bingo_Http_Request::get('activity_id',  0));
        self::$_intCompId = intval(Bingo_Http_Request::get('component_id', 0));
        self::$_intForumId = intval(Bingo_Http_Request::get('forum_id', 0));

        if (self::$_intActId <= 0)
        {
            Bingo_Log::warning('illegal activity_id');
            return Tieba_Errcode::ERR_PARAM_ERROR;
        }
        if (self::$_intCompId <= 0)
        {
            Bingo_Log::warning('illegal component_id');
            return Tieba_Errcode::ERR_PARAM_ERROR;
        }
        if (self::$_intForumId <= 0)
        {
            Bingo_Log::warning('illegal forum_id');
            return Tieba_Errcode::ERR_PARAM_ERROR;
        }        
        self::$_intUserId = intval(Tieba_Session_Socket::getLoginUid());
        if (self::$_intUserId <= 0)
        {
            Bingo_Log::warning('illegal user_id');
            return Tieba_Errcode::ERR_PARAM_ERROR;
        }

        // check tbs
        $boolTbsPass = Tieba_Tbs::check(self::$_strTbs, true);
        if ($boolTbsPass === false)
        {
            Bingo_Log::warning('check tbs failed');
            return Tieba_Errcode::ERR_PARAM_ERROR;
        }

        return Tieba_Errcode::ERR_SUCCESS;
    }
    
    private static function _build($intErrno = Tieba_Errcode::ERR_SUCCESS, $arrData = array())
    {
        Bingo_Page::assign('no',     $intErrno);
        Bingo_Page::assign('errmsg', Tieba_Error::getErrmsg($intErrno));
        Bingo_Page::assign('data', $arrData);
        Bingo_Page::setOnlyDataType('json');
        return true;
    }
}
