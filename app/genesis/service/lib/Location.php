<?php
/*
 * Copyright (c) 2014 Baidu, Inc.
 * All rights reserved.
 *
 * <AUTHOR>
 * @date     2014/09/17
 * @abstract location
 *
 */

class Service_Lib_Location
{
    const KEY_MULTI_CHECKER = '_genesis_multi_checker_';

    public static function getLocationInfo($intActId)
    {
        // query db
        $intStatus = Service_Lib_Def::LOC_STATUS_OK;
        $strSql = "SELECT location_id,location_activity_id,location_type,location_related_id,location_status
                   FROM activity_location WHERE location_activity_id=$intActId AND location_status=$intStatus";
        $arrRet = Service_Lib_Connection::$objDB->query($strSql);
        if ($arrRet === false)
        {
            Bingo_Log::warning('get location info failed');
            Service_Lib_Connection::$objDB->getErrInfo();
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        
        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

    public static function getLocationSql($arrLocation, $intActId)
    {
        $strPre = "INSERT INTO activity_location(location_activity_id,location_type,location_related_id) VALUES ";
        $arrSql = array();
        foreach ($arrLocation as $arrItem)
        {
            $intLocType = $arrItem['location_type'];
            $intLocId   = $arrItem['location_related_id'];
            $arrSql[]   = "($intActId,$intLocType,$intLocId)";
        }

        $strSql = $strPre . implode(',', $arrSql);

        return $strSql;
    }

    public static function getConflictSql($arrLocation, $intStartTime, $intEndTime)
    {
        $arrSql = array();
        foreach ($arrLocation as $arrItem)
        {
            $intLocType = (int)$arrItem['location_type'];
            $intLocId   = (int)$arrItem['location_related_id'];
            $intStatus  = Service_Lib_Def::ACT_STATUS_CLOSED;
            $strSql = "SELECT activity_id FROM activity_info,activity_location
                       WHERE activity_id=location_activity_id AND activity_status<>$intStatus
                       AND start_time<$intEndTime AND end_time>$intStartTime
                       AND location_related_id=$intLocId AND location_type=$intLocType";
            $arrSql[] = $strSql;
        }

        return $arrSql;
    }

    public static function checkLocation($arrLocation)
    {
        $arrForumInput  = array();
        $arrThreadInput = array();

        foreach ($arrLocation as $arrItem)
        {
            $intLocType = (int)$arrItem['location_type'];
            $intLocId   = (int)$arrItem['location_related_id'];
            switch ($intLocType)
            {
                case Service_Lib_Def::LOCATION_FORUM:
                    if ($intLocId <= 0)
                    {
                        return Tieba_Errcode::ERR_GENESIS_ILLEGAL_LOCATION_FORUMID;
                    }
                    $arrForumInput[] = $intLocId;
                    break;
                case Service_Lib_Def::LOCATION_THREAD:
                    if ($intLocId <= 0)
                    {
                        return Tieba_Errcode::ERR_GENESIS_ILLEGAL_LOCATION_THREADID;
                    }
                    $arrThreadInput[] = $intLocId;
                    break;
                case Service_Lib_Def::LOCATION_STATIC:
                    break;
                default:
                    return Tieba_Errcode::ERR_GENESIS_ILLEGAL_LOCATION_TYPE;
            }
        }

        if (empty($arrForumInput) &&
            empty($arrThreadInput))
        {
            return Tieba_Errcode::ERR_SUCCESS;
        }
        // init
        $objMulti = new Tieba_Multi(self::KEY_MULTI_CHECKER);
        // register
        self::_registerCheckForum($objMulti, $arrForumInput);
        self::_registerCheckThread($objMulti, $arrThreadInput);
        // call
        $arrRet = $objMulti->call();
        // fetch result
        if ($arrRet === null)
        {
            Bingo_Log::warning('ral multi call failed');
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrForumOutput = $objMulti->getResult('checkForum');
        $arrThreadOutput = $objMulti->getResult('checkThread');
        // check forum id
        $intErrno = self::_checkForum($arrForumInput, $arrForumOutput);
        if ($intErrno !== Tieba_Errcode::ERR_SUCCESS)
        {
            return $intErrno;
        }
        // check thread id
        $intErrno = self::_checkThread($arrThreadInput, $arrThreadOutput);
        if ($intErrno !== Tieba_Errcode::ERR_SUCCESS)
        {
            return $intErrno;
        }

        return Tieba_Errcode::ERR_SUCCESS;
    }

    private static function _registerCheckForum($objMulti, $arrForumInput)
    {
        if (!empty($arrForumInput))
        {
            $arrInput = array(
                'serviceName' => 'forum',
                'method' => 'getFnameByFid',
                'input' => array(
                    'forum_id' => $arrForumInput,
                ),
            );
            $objMulti->register('checkForum', new Tieba_Service('forum'), $arrInput);
        }
        
    }

    private static function _registerCheckThread($objMulti, $arrThreadInput)
    {
        if (!empty($arrThreadInput))
        {
            $arrInput = array(
                'serviceName' => 'post',
                'method' => 'mgetThread',
                'input' => array(
                    'thread_ids'     => $arrThreadInput,
                    'forum_id'       => 0,
                    'need_abstract'  => 0,
                    'need_photo_pic' => 0,
                    'need_user_data' => 0,
                ),
            );
            $objMulti->register('checkThread', new Tieba_Service('post'), $arrInput);
        }
    }

    private static function _checkForum($arrForumInput, $arrForumOutput)
    {
        if (empty($arrForumInput))
        {
            return Tieba_Errcode::ERR_SUCCESS;
        }
        if ($arrForumOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS)
        {
            return Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
        }
        $arrForums = $arrForumOutput['forum_name'];
        foreach ($arrForumInput as $intFid)
        {
            $arrInfo = $arrForums[$intFid];
            if (!isset($arrInfo))
            {
                return Tieba_Errcode::ERR_GENESIS_ILLEGAL_LOCATION_FORUMID;
            }
            if ($arrInfo['exist'] !== 1)
            {
                return Tieba_Errcode::ERR_GENESIS_ILLEGAL_LOCATION_FORUMID;
            }
        }

        return Tieba_Errcode::ERR_SUCCESS;
    }

    private static function _checkThread($arrThreadInput, $arrThreadOutput)
    {
        if (empty($arrThreadInput))
        {
            return Tieba_Errcode::ERR_SUCCESS;
        }
        if ($arrThreadOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS)
        {
            return Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
        }
        $arrThreads = $arrThreadOutput['output']['thread_list'];
        foreach ($arrThreadInput as $intTid)
        {
            $arrInfo = $arrThreads[$intTid];
            if (!isset($arrInfo))
            {
                return Tieba_Errcode::ERR_GENESIS_ILLEGAL_LOCATION_THREADID;
            }
            if ($arrInfo['is_deleted'] === 1)
            {
                return Tieba_Errcode::ERR_GENESIS_ILLEGAL_LOCATION_THREADID;
            }
        }

        return Tieba_Errcode::ERR_SUCCESS;
    }
}
