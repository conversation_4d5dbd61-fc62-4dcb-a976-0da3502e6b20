<?php
/*
 * Copyright (c) 2014 Baidu, Inc.
 * All rights reserved.
 *
 * <AUTHOR>
 * @date      2014/07/01
 * @abstract  utility functions
 *            
 */

class Service_Lib_Utility
{
    public static function buildReturn($intErrno = Tieba_Errcode::ERR_SUCCESS, $arrOutput = array())
    {
        return array(
            'errno'  => $intErrno,
            'errmsg' => Tieba_Error::getErrmsg($intErrno),
            'output' => $arrOutput,
        );
    }
}
