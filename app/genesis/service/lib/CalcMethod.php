<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file CalcMethod.php
 * <AUTHOR>
 * @date 2014/10/10 12:12:18
 * @brief calc method
 *  
 **/
class Service_Lib_CalcMethod {
    
    const REDIS_PID     = 'gconforum';
    //support calculate type
    //calc_type => calc_method
    public static $_calcType = array(
        'incr_by_ratio'  => 'incrFakeCount',
        'multi_by_ratio' => 'multiFakeCount',
        'decr_by_ratio'  => 'decrFakeCount',
        'incr_random_by_ratio' => 'incrRandomCount',
    );
    /*
     * incr method
     */
    public static function incrFakeCount($arrInput) {
        if (!isset($arrInput['key']) || !isset($arrInput['ratio']) ){
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $strKey   = strval($arrInput['key']);
        $intRatio = intval($arrInput['ratio']);
        $objRedis = new Service_Lib_Redis(self::REDIS_PID);
        if (!$objRedis->init()) {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_INIT_REDIS);
        }
        $bolOut = $objRedis->incr($strKey, $intRatio);
        if (false === $bolOut) {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_FAIL_TALK_WITH_REDIS);
        }
        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS);
    }
    
    /*
     * multi method
     */
    public static function multiFakeCount($arrInput) {
        if (!isset($arrInput['key']) || !isset($arrInput['ratio']) ){
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $strKey   = strval($arrInput['key']);
        $intRatio = intval($arrInput['ratio']);
        $objRedis = new Service_Lib_Redis(self::REDIS_PID);
        if (!$objRedis->init()) {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_INIT_REDIS);
        }
        $intOut = $objRedis->get($strKey);
        if (false === $intOut) {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_FAIL_TALK_WITH_REDIS);
        }
        $intOut = intval($intOut);
        $intFakeValue = $intOut*$intRatio;
        $bolOut = $objRedis->set($strKey, $intFakeValue);
        if (false === $bolOut) {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_FAIL_TALK_WITH_REDIS);
        }
        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS);
    }
    
    /*
     * decr method
     */
    public static function decrFakeCount($arrInput) {
        if (!isset($arrInput['key']) || !isset($arrInput['ratio']) ){
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $strKey   = strval($arrInput['key']);
        $intRatio = intval($arrInput['ratio']);
        $objRedis = new Service_Lib_Redis(self::REDIS_PID);
        if (!$objRedis->init()) {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_INIT_REDIS);
        }
        $bolOut = $objRedis->decr($strKey, $intRatio);
        if (false === $bolOut) {
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_FAIL_TALK_WITH_REDIS);
        }
        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS);
    }

    /*
     * random incr method
     */
    public static function incrRandomCount($arrInput) {
        if (!isset($arrInput['key']) || !isset($arrInput['ratio']) ){
            Bingo_Log::warning("param error!".serialize($arrInput));
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $strKey   = strval($arrInput['key']);
        $arrRatio = $arrInput['ratio'];
        $intStep  = rand($arrRatio[0], $arrRatio[1]);
        Bingo_Log::pushNotice("ratio_0", $arrRatio[0]);
        Bingo_Log::pushNotice("ratio_1", $arrRatio[1]);
        Bingo_Log::pushNotice("step", $intStep);
        Bingo_Log::pushNotice("str_key", $strKey);
        $objRedis = new Service_Lib_Redis(self::REDIS_PID);
        if (!$objRedis->init()) {
            Bingo_Log::warning("init redis error!");
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_INIT_REDIS);
        }
        $bolOut = $objRedis->incr($strKey, $intStep);
        if (false === $bolOut) {
            Bingo_Log::warning("incr error!");
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_FAIL_TALK_WITH_REDIS);
        }
        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS);
    }
}