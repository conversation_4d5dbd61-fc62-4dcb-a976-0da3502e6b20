<?php

/**
 * @brief 加抽奖机会扩展
 */
class Service_Compnmq_Ext_Addchance implements Service_Compnmq_Ext_Base {
    private $_rule          = array();
    private $_command_no    = -1;
	private $_award_act_id 	= 0;
	private $_forum_id  	= 0;
	private $_user_id 		= 0;
	private $_init_chance 	= 0;
    private $_limit_chance  = 0;
    private $_content       = '';

	const CHANCE_INIT 		= 'init_lottery_num';
	const CHANCE_LIMIT 		= 'limit_lottery_num';
    const CHANCE_REPLY      = 'reply_lottery_num';
    const CHANCE_EGG        = 'keyword_egg';

    const NMQ_ADD_CHANCE    = 'addchance';

    /**
     * @brief 
     *
     * @param $arrActInfo
     *
     * @return 
     */
    public function nmqPreProcess($arrRequest) {
        foreach($arrRequest['thread_activity']['component_list'] as $component) {
            $intCompType = (int)$component['component_type'];
            if (Service_Lib_Def::COMP_TYPE_LOTTERY === $intCompType) {
                // check
                $this->_rule = $component['extra_info']['activity_rule'];
				if (!isset($this->_rule['regular'][self::CHANCE_INIT]) || !isset($this->_rule['regular'][self::CHANCE_LIMIT])) {
					//Bingo_Log::warning('no init chance or limit chance in activity rule, perhaps a multi component activity.');
					return false;
				}
                $this->_command_no = $arrRequest['command_no'];
				$this->_award_act_id 	= (int)$component['component_related_id'];
				$this->_forum_id 		= $arrRequest['forum_id'];
				$this->_user_id 		= $arrRequest['user_id'];
				$this->_init_chance 	= $this->_rule['regular'][self::CHANCE_INIT];
				$this->_limit_chance 	= $this->_rule['regular'][self::CHANCE_LIMIT];
                $this->_content         = $arrRequest['content'];
                $this->_postId          = $arrRequest['post_id'];
                return true;
            }
        }
        return false;
    }

    /**
     * @brief 
     *
     * @return 
     */
    public function nmqGetInput() {
		$arrMultiInput = array();
		$arrMultiInput [] = array(
			'serviceName' 	=> 'perm',
			'method' 		=> 'getPerm',
			'input' 	=> array(
				'forum_id' 	=> $this->_forum_id,
				'user_id' 	=> $this->_user_id,
				'user_ip' 	=> 0,
			),
		);
		$arrMultiInput [] = array(
			'serviceName' 	=> 'tyche',
			'method' 		=> 'getUserChance',
			'input' 	=> array(
				'award_act_id' 			=> $this->_award_act_id,
				'user_id' 				=> $this->_user_id,
				'rec_start_time' 		=> 0,
				'chance_default_count' 	=> $this->_init_chance, 
			),
		);
		//Bingo_Log::warning(print_r($arrMultiInput, true));
        return $arrMultiInput;
    }

    /**
     * @brief 
     *
     * @param $arrResult
     *
     * @return 
     */
    public function nmqHandle($arrMultiOut) {
        // debug
        //Bingo_Log::warning(print_r($arrMultiOut, true));

        // process
        $arrPermOut = $arrMultiOut[0];
        $arrGetUserChanceOut = $arrMultiOut[1];
        if (false === $arrPermOut || false === $arrGetUserChanceOut) {
            throw new Exception(sprintf('fail to call service when addchance, perm_out[%s], getchance_out[%s]', serialize($arrPermOut), serialize($arrGetUserChanceOut)), Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        
        // check if the user is like this forum.
        $intIsLike = (int)$arrPermOut['output']['grade']['is_like'];
        if (1  === $intIsLike) {
            // good
        } else {
            Bingo_Log::pushNotice('user_not_like', 1);
            return true;
        }

        // check current chance record.
        $intTodaytime = strtotime(date('Y-m-d'));
        $intCurChanceCount = 0;

        foreach($arrGetUserChanceOut['data']['chance_record'] as $chance_record) {
            $op_time = $chance_record['op_time'];
            $chance_count = $chance_record['chance_count'];
            if ($intTodaytime < $op_time && 0 < $chance_count) {
                $intCurChanceCount += $chance_count;
            }
        }

        $intNeed2Add    = 0;
        $intCanAdd      = 0;
        $intAddChance = 0;
        
        $strChanceType = '';
        // judge the different command
        switch($this->_command_no) {
            case Service_Lib_Def::NMQ_POST_COMMIT:
                $strChanceType = self::CHANCE_REPLY;
                $intNeed2Add = (int)$this->_rule['regular'][$strChanceType];
                
                // 加入彩蛋逻辑
                // 最终算到intNeed2Add就行了.
                $intEggChance = 0;
                if(isset($this->_rule['regular'][self::CHANCE_EGG])) {
                    $keyword = trim($this->_rule['regular'][self::CHANCE_EGG]['keyword']);
                    $mixHitEgg = strstr($this->_content, $keyword);
                    if (false === $mixHitEgg) {
                    } else {
                        Bingo_Log::pushNotice('hit_egg', 1);
                        $intEggChance = $this->_rule['regular'][self::CHANCE_EGG]['lottery_num'];
                        $arrPostInput = array(
                            'input' => array(
                                0 => array(
                                    'pid' => $this->_postId,
                                    'fields' => array(
                                        0 => array(
                                            'fkey' => 'post_act_bubble',
                                            'value' => 1,
                                        ),  
                                    ),  
                                ),  
                            ), 
                        );  
                        $arrRes = Tieba_Service::call('post', 'setKeyInPostInfo', $arrPostInput);
                        if ($arrRes['errno'] !== 0)
                        {   
                            Bingo_Log::warning("call post::setKeyInPostInfo fail! ".serialize($arrRes));
                        }                        
                    }
                }
                $intNeed2Add += $intEggChance;
                break;
            case Service_Lib_Def::NMQ_GENESIS_COMMIT:
                $strSubNmqType = $this->_request['sub_nmq_type'];
                if (self::NMQ_ADD_CHANCE === $strSubNmqType) {
                    // process
                } else {
                    // exit
                }
                break;
        }

        if (0 >= $intNeed2Add) {
            Bingo_Log::warning('invalid add chance type.');
            return true;
        }

        $intCanAdd = $this->_limit_chance - $intCurChanceCount;
        if ($intNeed2Add <= $intCanAdd) {
            $intAddChance = $intNeed2Add;
        } else {
            $intAddChance = $intCanAdd;
        }

        if (0 >= $intAddChance) {
            // reach limit
            Bingo_Log::pushNotice('addchance_limit_uid', $this->_user_id);
            Bingo_Log::pushNotice('addchance_limit_act_id', $this->_award_act_id);
            return true;
        }

        $arrInput = array(
            'user_id' => $this->_user_id,
            'award_act_id' => $this->_award_act_id,
            'chance_type' => $strChanceType,
            'chance_count' => $intAddChance,
        );

        $arrOutput = Tieba_Service::call('tyche', 'addUserChance', $arrInput);
        $intRalNo = (int)ral_get_errno();
        if (false === $arrOutput && 7 === $intRalNo) {
            $strMsg = sprintf('fail to add userchance, award_act_id[%d], user_id[%d], chance_type[%s].', $this->_award_act_id, $this->_user_id, $strChanceType);
            throw new Exception($strMsg, Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        if (0 !== (int)$arrOutput['errno']) {
            $strMsg = sprintf('fail to add userchance, award_act_id[%d], user_id[%d], chance_type[%s].', $this->_award_act_id, $this->_user_id, $strChanceType);
            throw new Exception($strMsg, Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return true;
    }

}
