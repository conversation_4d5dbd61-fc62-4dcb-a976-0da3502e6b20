<?php

/**
 * @brief postCommit
 */
class Service_Compnmq_Impl_postCommit implements Service_Compnmq_Impl_CommandBase {
    const NMQ_COMMAND_KEY = "postCommit";
    private $_request = array();

    /**
        * @brief 
        *
        * @return 
     */
    private function _filter() {
        //1. 过滤楼中楼
        if (0 < $this->_request['quote_id']) {
            return false;
        }
        return true;
    }

    /**
     * @brief 
     *
     * @param $arrRequest
     *
     * @return 
     */
    public function preProcess($arrRequest) {
        //init param
        $arrActInfo = array();
        $this->_request['conf']             = $arrRequest['conf'][self::NMQ_COMMAND_KEY];
        $intFid = $this->_request['forum_id']   = (int)$arrRequest['nmq_input']['forum_id'];
        $intTid = $this->_request['thread_id']  = (int)$arrRequest['nmq_input']['thread_id'];
        $this->_request['post_id'] = (int)$arrRequest['nmq_input']['post_id'];
        $this->_request['user_id'] = (int)$arrRequest['nmq_input']['user_id'];
        $this->_request['user_name'] = $arrRequest['nmq_input']['user_name'];
        $this->_request['content'] = $arrRequest['nmq_input']['rawdata'];
        $this->_request['nmq_type']= $arrRequest['nmq_type'];
        $this->_request['command_no'] = (int)$arrRequest['nmq_input']['command_no'];
        $this->_request['forum_name'] = $arrRequest['nmq_input']['forum_name'];
        $this->_request['thread_activity'] = $arrActInfo;
        $this->_request['post_type'] = $arrRequest['nmq_input']['ptype'];
        $this->_request['quote_id'] = (int)$arrRequest['nmq_input']['quote_id'];

        if (!$this->_filter()) {
            return false;
        }

        $arrInput = array(
            'thread_id' => $intTid,
        );

        $strModule = 'genesis';
        $strMethod = 'getActivityInfoByTid';
        $strTimerKey = "preProcess->$strModule::$strMethod";
        Bingo_Timer::start($strTimerKey);
        $arrOutput = Tieba_Service::call($strModule, $strMethod, $arrInput);
        Bingo_Timer::end($strTimerKey);
        if (false === $arrOutput) {
            $intRalNo = ral_get_errno();
            throw new Exception(sprintf('call %s:%s failed, self call failed, ral_no[%d]', $strModule, $strMethod, $intRalNo), Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        if (0 !== Tieba_Errcode::ERR_SUCCESS) {
            throw new Exception(sprintf('call %s:%s failed, errmsg[%s]', $strModule, $strMethod, serialize($arrOutput)), Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrActInfo = $arrOutput['output']['activity_info'];
        if (!empty($arrActInfo)) {
            $this->_request['thread_activity'] = $arrActInfo;
        } else {
            //todo
        }
        return true;
    }

    /**
     * @brief 
     *
     * @return 
     */
    public function execute() {
        if (empty($this->_request['thread_activity'])) {
            return true;
        }
        
        $intActId = (int)$this->_request['thread_activity']['activity_id'];
        Bingo_Log::pushNotice('activity_id', $intActId);

        $intActStartTime = (int)$this->_request['thread_activity']['start_time'];
        $intActEndTime = (int)$this->_request['thread_activity']['end_time'];
        $intStatus     = (int)$this->_request['thread_activity']['activity_status'];
        $intCurTime = time();
        if ($intCurTime < $intActStartTime || $intCurTime > $intActEndTime) {
            // 校验活动时间.
            return true;
        }
        if ($intStatus !== Service_Lib_Def::ACT_STATUS_ONLINE)
        {
            Bingo_Log::pushNotice("status_not_correct", $intStatus);
            return true;
        }
        $intTid = $this->_request['thread_id'];
        
        /*
         * use byTid interface.
         * get the proper thread
        $intCurActTid = 0;
        foreach($this->_request['activity_list'] as $act) {
            $intCurActTid = (int)$act['location_list']['location_related_id'];
            
            // 用list这个接口，只有一个location
            foreach($act['location_list'] as $location) {
                if (Service_Lib_Def::LOCATION_THREAD === $location['location_type']) {
                    $intCurActTid = (int)$location['location_related_id'];
                    break;
                }
            }

            if ($intTid === $intCurActTid) {
                $this->_request['thread_activity'] = $act;
                break;
            }
        }
         */

        $arrConf = $this->_request['conf'];

        $strMultiKey = self::NMQ_COMMAND_KEY;
        $objMulitCaller = new Tieba_Multi($strMultiKey);
        $strTimerKey = self::NMQ_COMMAND_KEY."_multi";
        $intMultiCount = 0;

        // object array
        $arrComponentList = array();
        foreach($arrConf['ext'] as $conf) {
            $strComponent = "Service_Compnmq_Ext_" . $conf;
            $objComponent = new $strComponent;
            if (!$objComponent->nmqPreProcess($this->_request)) {
                continue;
            }
            $arrMultiInput = $objComponent->nmqGetInput();
            $arrComponentList[$strComponent] = array(
                'component' => $objComponent,
            );
            foreach($arrMultiInput as $input) {
                $strModule = $input['serviceName'];
                $strMethod = $input['method'];
                $strTimerKey .= "_{$strModule}:{$strMethod}";
                $strRegKey = "{$strMultiKey}_{$strComponent}_{$strModule}_{$strMethod}";
                $objMulitCaller->register($strRegKey , new Tieba_Service($input['serviceName']), $input);
                $arrComponentList[$strComponent]['register_key'][] = $strRegKey;
                $intMultiCount++;
            }
        }

        if (0 < $intMultiCount) {
            Bingo_Timer::start($strTimerKey);
            $arrMultiResult = $objMulitCaller->call();
            Bingo_Log::debug(print_r($arrMultiResult, true));
            Bingo_Timer::end($strTimerKey);
        }

        foreach($arrComponentList as $strComponent => $arrComp) {
            $arrResult = array();
            $component = $arrComp['component'];
            foreach($arrComp['register_key'] as $strRegKey) {
                $arrResult[] = $arrMultiResult[$strRegKey];
            }
            $component->nmqHandle($arrResult);
        }
    }

}
