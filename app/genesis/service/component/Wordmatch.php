<?php
/*
 * Copyright (c) 2014 Baidu, Inc.
 * All rights reserved.
 *
 * <AUTHOR>
 * @date     2015/08/01
 * @abstract wordmatch component
 *
 */

class Service_Component_Wordmatch extends Service_Component_Base {
    const SERVICE_NAME = '';
    
    /**
        * @brief construct
        *
        * @return 
     */
    function __construct() {
        parent::__construct(Service_Lib_Def::COMP_TYPE_WORDMATCH);
    }

    /**
        * @brief
        *
        * @param $arrCompInfo
        *
        * @return 
     */
    public function _getServiceInfo($arrCompInfo) {
        return array();
    }

    /**
        * @brief 
        *
        * @param $arrInput
        *
        * @return 
     */
    public function _setServiceInfo($arrCompInfo) {
        return Service_Lib_Utility::buildReturn(0);
    }

    /**
        * @brief 
        *
        * @param $arrInput
        *
        * @return 
     */
    public function _addServiceInfo($arrInput) {
        $intCompId = (int)$arrInput['component_id'];
        $arrRet = array(
            'rel_id' => $intCompId,
        );
        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

    /**
        * @brief 
        *
        * @param $arrInput
        *
        * @return 
     */
    public function _getExtraInfo($strCompInfo) {
		$arrCompInfo = unserialize($strCompInfo);
		$arrPattern = array();
		foreach($arrCompInfo['wordmatch_rule']['pattern'] as $pattern) {
			$arrPattern[] = urldecode($pattern);
		}
		$arrCompInfo['wordmatch_rule']['pattern'] = $arrPattern;
		return $arrCompInfo;
    }

    /**
        * @brief 
        *
        * @param $arrData
        *
        * @return 
     */
    public function _setExtraInfo($arrCompInfo) {
		if (!isset($arrCompInfo['wordmatch_rule']['pattern'])) {
			return '';
		}
		// process the chn text
		$arrPattern = array();
		foreach($arrCompInfo['wordmatch_rule']['pattern'] as $pattern) {
			$arrPattern [] = urlencode($pattern);
		}
		$arrCompInfo['wordmatch_rule']['pattern'] = $arrPattern;
        $strCompInfo = serialize($arrCompInfo);
        return $strCompInfo;
    }

    /**
        * @brief 
        *
        * @param $intRelId
        *
        * @return 
     */
    public function _getMultiInput($intRelId) {
        return array();
    }
    
    /**
        * @brief 
        *
        * @param $arrCompInfo
        * @param $arrNmqInput
        *
        * @return 
     */
    public function _processNmqCommand($arrCompInfo, $arrNmqInput) {
        return Service_Lib_Utility::buildReturn();
    }

}
