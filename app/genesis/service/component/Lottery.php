<?php
/*
 * Copyright (c) 2014 Baidu, Inc.
 * All rights reserved.
 *
 * <AUTHOR>
 * @date     2014/09/25
 * @abstract lottery component
 *
 */

class Service_Component_Lottery extends Service_Component_Base
{
    const SERVICE_NAME = 'tyche';

    const CHANCE_INIT       = 'init_lottery_num'; //活动初始获得次数下标
    const CHANCE_LIMIT      = 'limit_lottery_num'; //活动次数上线下标
    const CHANCE_ADDSIGN    = 'sign_lottery_num'; //签到次数下标
    const CHANCE_ADDPOST    = 'reply_lottery_num'; //回复次数下标
    const CHANCE_ADDRELAY   = 'repost_lottery_num'; //转帖次数下标
    const CHANCE_LINK       = 'link_lottery_num'; //点击链接
    
    //加机会的字符串映射转为数字
    public static $chance_map = array(
        'init_lottery_num'      => 0,
        'sign_lottery_num'      => 1,
        'reply_lottery_num'     => 2,
        'repost_lottery_num'    => 3,
        'link_lottery_num'      => 4,
    );
    
    function __construct()
    {
        parent::__construct(Service_Lib_Def::COMP_TYPE_LOTTERY);
    }

    public function _getServiceInfo($arrCompInfo)
    {
        $intRelId = (int)$arrCompInfo['rel_id'];
        $arrInput = array(
            'award_act_id' => $intRelId,
        );        
        $arrRes = Tieba_Service::call(self::SERVICE_NAME, 'getAwardAct', $arrInput);
        if (false === $arrRes || $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call ".self::SERVICE_NAME.'::getAwardAct fail'.serialize($arrRes).'_'.serialize($arrInput));
            return $arrRes;
        }
        $arrOutput = array(
            'errno'  => Tieba_Errcode::ERR_SUCCESS,
            'errmsg' => 'success',
            'output' => $arrRes['data'],
        );
        return $arrOutput; 
    }

    public function _setServiceInfo($arrInput)
    {
        $intOpUserId = (int)$arrInput['op_user_id'];
        $intAwardActId = $arrInput['related_act_ids'][0];
        $intStartTime = (int)$arrInput['start_time'];
        $intEndTime   = (int)$arrInput['end_time'];
        
        $arrInputParam = array(
            'award_act_id'      => $intAwardActId,
            'op_user_id'        => $intOpUserId,
            'start_time'        => $intStartTime,
            'end_time'          => $intEndTime,
            'award_info'        => $arrInput['award_info'],
            'ext_info'          => $arrInput['component_input']['ext_info'],
        );

        $arrRes = Tieba_Service::call(self::SERVICE_NAME, 'modifyAwardAct', $arrInputParam);
        
        if (false === $arrRes || $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call ".self::SERVICE_NAME.'::modifyAwardAct fail'.serialize($arrRes).'_'.serialize($arrInputParam));
            return $arrRes;
        }
        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrRes);
    }

    public function _addServiceInfo($arrInput)
    {
        $intOpUserId  = (int)$arrInput['op_user_id'];
        $intStartTime = (int)$arrInput['start_time'];
        $intEndTime   = (int)$arrInput['end_time'];
        $intCompId    = (int)$arrInput['component_id'];
        $intRelatedType = isset($arrInput['related_act_type']) ? $arrInput['related_act_type'] : Service_Lib_Def::TYCHE_TYPE_KEY;
        
        $arrInputParam = array(
            'related_source'  => Service_Lib_Def::PRODUCT_KEY,
            'related_act_type'=> $intRelatedType,
            'related_act_id'   => $intCompId,
            'status'           => 1,
            'can_dup'          => 1,
            'start_time'       => $intStartTime,
            'end_time'         => $intEndTime,
            'audit_user_id'    => $intOpUserId,
            'op_user_id'       => $intOpUserId,
            'award_info'       => $arrInput['component_input']['award_info'],
            'init_partake'     => $arrInput['component_input']['init_partake'],
            'partake_ratio'    => $arrInput['component_input']['partake_ratio'],
            'forum_id'         => 0, //abandon
            'ext_info'         => $arrInput['component_input']['ext_info'],
        );
        $arrRes = Tieba_Service::call(self::SERVICE_NAME, 'createAwardAct', $arrInputParam);
        if (false === $arrRes || $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call ".self::SERVICE_NAME.'::createAwardAct fail'.serialize($arrRes).'_'.serialize($arrInputParam));
            return $arrRes;
        }
        $arrRes = array(
            'rel_id' => intval($arrRes['data']['award_act_id']),
        );
        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrRes);
    }

    public function _getExtraInfo($strData)
    {
        return unserialize($strData);
    }

    public function _setExtraInfo($arrData)
    {
        return serialize($arrData);
    }

    public function _getMultiInput($intRelId)
    {
        return array(
            'serviceName' => self::SERVICE_NAME,
            'method' => 'getAwardAct',
            'input' => array(
                'award_act_id' => $intRelId,
            ),
        );
    }
    
    public function _processNmqCommand($arrCompInfo, $arrNmqInput)
    {
        $intCommand  = intval($arrNmqInput['command_no']);
        if (!isset($arrCompInfo['extra_info']['activity_rule']) || !is_array($arrCompInfo['extra_info']['activity_rule']))
        {
            Bingo_Log::warning("extra_info not have activity_rule! ".serialize($arrCompInfo));
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        //普通规则
        $arrRegRule   = $arrCompInfo['extra_info']['activity_rule']['regular'];
        $intCompId = intval($arrCompInfo['component_id']);
        $intRelId  = intval($arrCompInfo['component_related_id']);
        $intUserId = -1;
        $intAddChance = -1;
        //检测是否设置初始次数和上限，否则返回错误
        if (!isset($arrRegRule[self::CHANCE_INIT]) || !isset($arrRegRule[self::CHANCE_LIMIT]))
        {
            Bingo_Log::warning("this activity has no init number or upper number");
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intInitChance  = intval($arrRegRule[self::CHANCE_INIT]);
        $intLimitChance = intval($arrRegRule[self::CHANCE_LIMIT]);
        $intAddType     = -1;
        switch($intCommand)
        {
            case Service_Lib_Def::NMQ_POST_COMMIT: //发表回复
            {
                $intAddType = self::$chance_map[self::CHANCE_ADDPOST];
                if (isset($arrNmqInput['user_id']) && isset($arrRegRule[self::CHANCE_ADDPOST])
                && $arrRegRule[self::CHANCE_ADDPOST] > 0)
                {
                    $intUserId = intval($arrNmqInput['user_id']);
                    $intAddChance = intval($arrRegRule[self::CHANCE_ADDPOST]);
                }                
                break;
            }
            case Service_Lib_Def::NMQ_GRADE_SIGN_MOBILE:
            case Service_Lib_Def::NMQ_GRADE_SIGN_EX:
            case Service_Lib_Def::NMQ_GRADE_SIGN:
            {
                $intAddType = self::$chance_map[self::CHANCE_ADDSIGN];
                if (isset($arrNmqInput['user_id']) && isset($arrRegRule[self::CHANCE_ADDSIGN])
                && $arrRegRule[self::CHANCE_ADDSIGN] > 0)
                {
                    $intUserId = intval($arrNmqInput['user_id']);
                    $intAddChance = intval($arrRegRule[self::CHANCE_ADDSIGN]);
                }                
                break;
            }
            case Service_Lib_Def::NMQ_POST_LOTTERY: //转帖
            {
                $intAddType = self::$chance_map[self::CHANCE_ADDRELAY];
                if (isset($arrNmqInput['user_id']) && isset($arrRegRule[self::CHANCE_ADDRELAY])
                && $arrRegRule[self::CHANCE_ADDRELAY] > 0)
                {
                    $intUserId = intval($arrNmqInput['user_id']);
                    $intAddChance = intval($arrRegRule[self::CHANCE_ADDRELAY]);
                }                
                break;
            }
            default:
            {
                Bingo_Log::warning('invalid cmd!'.serialize($arrNmqInput));
                break;    
            }    
        }
        if ($intAddChance === -1 || $intUserId === -1)
        {
            Bingo_Log::pushNotice("need_addchance", 0);
            return Service_Lib_Utility::buildReturn();
        }

        //check user like this forum or not
        $intForumId = intval($arrNmqInput['forum_id']);
        $arrParam = array(
            'forum_id' => $intForumId,
            'user_id'  => $intUserId,
            'user_ip'  => 0,
        );
        $arrRes = Tieba_Service::call('perm', 'getPerm', $arrParam);
        $intIsLike = intval($arrRes['output']['grade']['is_like']);
        if ($arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS)
        {
            Bingo_Log::warning("call perm::getPerm fail! ".serialize($arrRes).'_'.serialize($arrParam));
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }       
        if ($intIsLike !== 1)
        {
            Bingo_Log::pushNotice("user_not_like", 1);
            return Service_Lib_Utility::buildReturn();
        }
        Bingo_Log::pushNotice("user_id", $intUserId);
        Bingo_Log::pushNotice("tyche_id", $intRelId);
        Bingo_Log::pushNotice("forum_id", $intForumId);
        Bingo_Log::pushNotice("add_type", $intAddType);
        $arrRes = self::_addUserChance($intRelId, $intUserId, $intInitChance, $intLimitChance, $intAddChance, $intAddType);
        if ($arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS)
        {
            Bingo_Log::warning("add user chance error! $intUserId, $intInitChance, $intLimitChance, $intAddChance, $intRelId");
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        } 
        return Service_Lib_Utility::buildReturn();
    }

    private static function _addUserChance($intTycheId, $intUserId, $intInitChance, $intLimitChance, $intAddChance, $intAddType)
    {
        $arrParam = array(
            'award_act_id'   => $intTycheId,
            'user_id'        => $intUserId,
            'rec_start_time' => 0,
            'chance_default_count' => $intInitChance,
        );
        $arrRes = Tieba_Service::call('tyche', 'getUserChance', $arrParam);
        if (false === $arrRes || $arrRes['errno'] !== 0)
        {
            Bingo_Log::warning("call tyche::getUserChance fail! ".serialize($arrRes).'_'.serialize($arrParam));
            return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $intDaytime = strtotime(date('Y-m-d'));
        $intCount = 0;
        foreach ($arrRes['data']['chance_record'] as $chance_rec) 
        {
            if ($chance_rec['op_time'] >= $intDaytime && $chance_rec['chance_count'] > 0) 
            {
                $intCount += $chance_rec['chance_count'];
            }
        }
        $intDiff = $intLimitChance - $intCount;
        if ($intDiff > 0)
        {
            $arrParam = array(
                'user_id' => $intUserId,
                'award_act_id' => $intTycheId,
                'chance_type'  => $intAddType,
                'chance_count' => min($intDiff, $intAddChance),
            );
            $arrRes = Tieba_Service::call('tyche', 'addUserChance', $arrParam);
            if (false === $arrRes || $arrRes['errno'] !== 0)
            {
                Bingo_Log::warning("call tyche::addUserChance fail! ".serialize($arrRes).'_'.serialize($arrParam));
                return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }
        return Service_Lib_Utility::buildReturn();
    }
}
