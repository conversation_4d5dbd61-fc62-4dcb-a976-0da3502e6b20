<?php
/*
 * Copyright (c) 2014 Baidu, Inc.
 * All rights reserved.
 *
 * <AUTHOR>
 * @date     2014/11/11
 * @abstract Util methods
 *
 */

class Service_Util
{
    public static function clearCache($arrInput)
    {
        if (isset($arrInput['activity_id']))
        {
            $intActId = (int)$arrInput['activity_id'];
            if (!Service_Lib_CacheHelper::removeCacheByActivityId($intActId))
            {
                return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
            }
        }

        if (isset($arrInput['cache_prefix']))
        {
            $strPrefix = (string)$arrInput['cache_prefix'];
            if (Service_Lib_Connection::initCache())
            {
                Service_Lib_Connection::$objCache->remove($strPrefix);
            }
        }
        return Service_Lib_Utility::buildReturn(Tieba_Errcode::ERR_SUCCESS, 'good luck');
    }

    /**
        * @brief get status group
        *
        * @param $type
        *
        * @return array
     */
    public static function getStatusGroup($type = 0) {
        $arrRet = array();
        switch($type) {
            case 0:
                $arrRet [] = Service_Lib_Def::ACT_STATUS_ONLINE;
                break;
            default:
                $arrRet [] = Service_Lib_Def::ACT_STATUS_ONLINE;
                break;
        }
        return $arrRet;
    }

    /**
        * @brief get conf
        *
        * @param $strConfName
        *
        * @return 
     */
    public static function getConf($strConfName) {
        $arrRet = null;
        if (0 >= strlen($strConfName)) {
            Bingo_Log::warning('invalid conf name.');
            return $arrRet;
        }
        $arrRet = Bd_Conf::getConf("app/genesis/$strConfName");
        return $arrRet;
    }
}
