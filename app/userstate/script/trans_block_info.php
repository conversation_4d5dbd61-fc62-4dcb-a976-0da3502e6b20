<?php
$cache = null;
$userstatedbConf = array(
		'user'    => 'root',
		'pwd'     => 'root',
		'db'      => 'forum_userstate',
		'host'    => '127.0.0.1',
		'port'    => 3306,
);
$blockdbConf = array(
		'user'    => 'root',
		'pwd'     => 'root',
		'db'      => 'forum_block',
		'host'    => '127.0.0.1',
		'port'    => 3346,
);

define ('BASEPATH',dirname(__FILE__));
define ('LOG', 'log');
Bingo_Log::init(array(
LOG => array(
'file'  => BASEPATH . '/blockinfoData.log',
'level' => 0xFF,
),
), LOG);
writelog("[NOTICE]begin to deal!");

$filename="blockinfo.txt";
$userstatedb=getUserstateDB();
$blockdb=getBlockDB();
if($blockdb ==NULL ||$userstatedb==NULL){
	writelog('connect db fail!');
	logout(1);
}else{
	writelog('connect db ok!');
}
$backPoint=array();
if(file_exists($filename)){
	$file = fopen($filename,"r");
	$tempstr=fgets($file);
	$backPoint=json_decode($tempstr);
	$backPoint=(array)$backPoint;
	fclose($file);
}
//$userstatedb = getDBConnect($userstatedbConf['host'],$userstatedbConf['port'],$userstatedbConf['user'],$userstatedbConf['pwd'],$userstatedbConf['db']);
//$blockdb = getDBConnect($blockdbConf['host'],$blockdbConf['port'],$blockdbConf['user'],$blockdbConf['pwd'],$blockdbConf['db']);

for($table_index=0;$table_index<16;$table_index++){
	$last_id=0;
	if(isset($backPoint['block_info_'.$table_index]) && intval($backPoint['block_info_'.$table_index])>0){
		$last_id=intval($backPoint['block_info_'.$table_index]);
	}
	while(true){
		$selectSql=sprintf("select * from block_info_%s where id>%s order by id asc limit 100;",$table_index,$last_id);
		
		//writelog("select SQL :".$selectSql);
		for($trynum=0;$trynum<3;$trynum++){
			$res = $blockdb->query($selectSql);
			if($res === false){
				writelog('exec sql fail [' . $blockdb->errno . '] [' .  $blockdb->error . '] [' . $selectSql . ']');
				if($trynum==2){
					$backPoint['block_info_'.$table_index]=$last_id;
					$fp = fopen($filename,'w+');
					$ddd=json_encode($backPoint);
					fwrite($fp, $ddd, strlen($ddd));
					fclose($fp);
			        logout(1);
				}
			}
			else{
				break;
			}
		}
		$maxid=$last_id;
		$count=0;
		foreach($res as $userblockInfo){
			$maxid=max(intval($userblockInfo['id']),$maxid);
			
			$user_id=intval($userblockInfo['user_id']);
			$db_num=$user_id%128;
			$act_type=0;
			$monitor_type='';
			$call_from=$userblockInfo['call_from'];
			if(intval($userblockInfo['start_time'])==intval($userblockInfo['end_time'])){
				$act_type=1;
			}
			if(isset($userblockInfo['call_from'])){
				$temp=explode('|',$userblockInfo['call_from']);
				if(count($temp)>=2){
					$call_from=$temp[0];
					$monitor_type=$temp[1];
				}
			}
			
			$insertsql=sprintf("insert into user_state_log_%s(`user_id`,`user_name`,`op_uid`,`op_uname`,`start_time`,`timespan`,`end_time`,`forum_id`,`forum_name`,`block_type`,`opgroup`,`act_type`,`remarks`,`is_by_pm`,`call_from`,`monitor_type`,`extra_type1`)values(%s,'%s',%s,'%s',%s,%s,%s,%s,'%s',%s,%s,%s,'%s',%s,'%s','%s',%s);",
					$db_num,
					$user_id,
					$userblockInfo['user_name'],
					$userblockInfo['op_uid'],
					$userblockInfo['op_uname'],
					$userblockInfo['start_time'],
					intval($userblockInfo['end_time'])-intval($userblockInfo['start_time']),
					intval($userblockInfo['end_time']),
					$userblockInfo['forum_id'],
					$userblockInfo['forum_name'],
					$userblockInfo['block_type'],
					1,
					$act_type,
					$userblockInfo['remarks'],
					0,
					$call_from,
					$monitor_type,
					0);
			for($trynum=0;$trynum<3;$trynum++){
				$ires = $userstatedb->query($insertsql);
				if($ires === false){
					writelog("trynum :".$trynum);
					writelog('exec sql fail [' . $userstatedb->errno . '] [' .  $userstatedb->error . '] [' . $insertsql . ']');
					if($trynum==2){
						$backPoint['block_info_'.$table_index]=$last_id;
						$fp = fopen($filename,'w+');
						$ddd=json_encode($backPoint);
						fwrite($fp, $ddd, strlen($ddd));
						fclose($fp);
						logout(1);
					}
				}
				else{
					break;
				}
			}
		}
		writelog(sprintf("tableIndex: %s,   id: %s",$table_index,$maxid));
		if($last_id<$maxid){
			$last_id=$maxid;
			$backPoint['block_info_'.$table_index]=$last_id;
			$fp = fopen($filename,'w+');
			$ddd=json_encode($backPoint);
			fwrite($fp, $ddd, strlen($ddd));
			fclose($fp);
		}else{
			break;
		}
		usleep(50000);
	}
}



function _addCache($strKey, $mixValue, $intLifeTime = 0){
	/*global $cache;
	if(!$cache){
		$cache = Bingo_Cache::factory('memcached', array(
				'confPath' => dirname(__FILE__).'/',
				'confFileName' => 'BlockCacheConfig.php',
		));
	}
	 
	$ret = $cache->add(strval($strKey), $mixValue, $intLifeTime);
	if($ret === 0){
		return true;
	}else{
		return false;
	}*/
}

function getDBConnect($strHost,$intPort,$username,$password,$database){
	$mysqli = mysqli_init ();
	mysqli_options ($mysqli,MYSQLI_OPT_CONNECT_TIMEOUT,5);
	$bolRes = mysqli_real_connect ($mysqli,$strHost,$username,$password,$database,$intPort);

	if (mysqli_connect_errno()) {
		$strInfo = sprintf ("mysqli_real_connect %s %d failed [errno : %d] [error %s]",$strHost,$intPort,mysqli_connect_errno (),mysqli_connect_error ());
		writelog($strInfo);
		$mysqli->close ();
		exit();
	}
	writelog(" mysqli_real_connect ok! $strHost $intPort");
	return $mysqli;
}

function getUserstateDB(){
	$_db = new Bd_DB();
	if($_db == null){
		writelog("new bd_db fail.");
		return null;
	}
	$r = $_db->ralConnect("db_service_userstate");
	if(!$r){
		writelog("bd db ral connect fail.");
		$_db = null;
		return null;
	}
	return $_db;
}

function getBlockDB(){
	$_db = new Bd_DB();
	if($_db == null){
		writelog("new bd_db fail.");
		return null;
	}
	$r = $_db->ralConnect("DB_forum_block");
	if(!$r){
		writelog("bd db ral connect fail.");
		$_db = null;
		return null;
	}
	return $_db;
}

function logout($intCode){
	exit($intCode);
}

function writelog($str){
	Bingo_Log::debug($str);
	Bingo_Log::getModule()->flush();
}

function __autoload($strClassName)
{
	require_once str_replace('_', '/', $strClassName) . '.php';
}
