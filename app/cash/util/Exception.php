<?php
/**
 * 异常类
 **/
class Util_Exception extends Exception {

    private $_intNo  = 0;

    private $_strError  = 'success';
    
    private $_arrData = array();

    /**
     * @param   null
     * @return  null
     */
    public function __construct ($strMsg=null,$intCode=null,$arrData=array()) {
    	//错误内容去空格，方便统计
        $strMsg = urlencode($strMsg);
        $this->_arrData = $arrData;
        parent::__construct($strMsg,$intCode);
    }

    /**
     * @param   null
     * @return  array
     */
    public function getData(){
        return $this->_arrData;
    }

}
/* vim: set noexpandtab ts=4 sw=4 sts=4 tw=100: */