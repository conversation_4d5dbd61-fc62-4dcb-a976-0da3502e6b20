<?php
abstract class Service_Third_Module extends Service_Libs_Base{

    protected $_strThirdId  = '';   //第三方账号
    protected $_strRealName = '';   //用户真实姓名
    /**
        *
        *   构造函数
        *   @param
        *   @option
        *   @return
        *
     */
    public function __construct($arrInput) {
        
    }


    /**
        *
        *   提现方法
        *   @param
        *   @option
        *   @return
        *
     */
    abstract public function cashTrans($arrInput);

    /**
        *
        *   构造签名
        *   @param
        *   @option
        *   @return
        *
     */
    abstract protected function buildSign($strMethod,$arrInput);
}
