<?php
/**
 * Wallet.php 红包跟钱包交互相关
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 18/12/10
 */

class Service_Redpacket_Wallet extends Service_Libs_Base {
    const SECRET = '78Jus82&h6[1*(sh@q';
    const BFB_DOMAIN    = 'https://www.dxmpay.com';
    //const BFB_DOMAIN_OFFLINE = 'https://qatest.baifubao.com';
    const BFB_DOMAIN_OFFLINE = 'https://qatest.dxmpay.com'; //2021.7更新的测试域名，后续可能失效
    const BFB_DOMAIN_TEST = 'https://qatest.dxmpay.com';

    //const BFB_SP_NO = '1002946104'; // 贴吧商业化团队的商户号，已长时间未使用
    const BFB_SECRET_KEY    = 'JdArGQkuK8Mhp44R8ZnBA2UvkkZdMUMv';  // 贴吧商业化团队的私钥，已废弃

    const BFB_SP_NO = '1003300058'; 
    const BFB_SP_NO_TEST    = '1500000009';         // 测试环境的商户号
    const BFB_SP_NO_GROWTH  = '1003300058';         // 用户增长&内容生产团队的商户号
    const BFB_SP_NO_TBYY    = '1003302011';         // 主端的商户号

    // 各商户号对应的秘钥，已废弃
    private static $arrSecretKey = array(
        // self::BFB_SP_NO     => self::BFB_SECRET_KEY,
        self::BFB_SP_NO_TEST   => 's5jtKWmyKZDvQR97reTeG6VUPjx7zFYZ',
        self::BFB_SP_NO_GROWTH => 'e6irxrxwbMWsQsqxthjMSEL9SQ6E5QPi',
        self::BFB_SP_NO_TBYY   => 'rFbqmVNJ3Nvmf5JBijsGjhpff9DJwqFW',
    );


    const BFB_NOTIFY_URL    = 'http://tieba.baidu.com/cash/bfbpayCallback';   //回调通知地址
    const BFB_SERVICECODE  = 10;   //
    const BFB_CURRENCY  = 1;   //币种 1.人民币
    const BFB_TRANSFER_DESC = '红包';
    const BFB_OPPOSITE_TYPE = 1;
    const BFB_OPPOSITE_TYPE_UNIONID = 8;
    const BFB_API_TRANS = '/_u/remit_api/sp_payment';
    const BFB_API_QUERY = '/remit/0/query/0';
    const BFB_ACCOUNT_TYPE  = 1;    //账号类型 1：私人 2：共有
    const BFB_NOTIFY_METHOE = 1;  //回调地址请求方式 1：GET  2：POST
    const BFB_INPUT_CHARSET = 1;  //请求参数字符编码1：GBK
    const BFB_OUTPUT_FORMAT = 1;  // 请求响应参数格式 1：XML
    const BFB_OUTPUT_CHARSET = 1;  // 请求响应参数数据字符编码 1：GBK 2：UTF-8
    const BFB_COMMENT_EXT   = '';   //备注
    const BFB_BANK_COMMENT_EXT  = '';   //网银备注 1-8 字节，一个gbk汉字是2个字节
    const BFB_SIGN_METHOD   = 1;    //加密方式 1：MD5
    const METHOD_DRAW_REDPACKET = '/huodong/common/draw_user_lottery';
    /**
     * 钱包打款接口
     */
    const BFB_API_RECEIVE_CASH = '/api/0/transfer_application';

    const BFB_API_CASH_QUERY = '';
    
    const USER_NAME_TBYY = '********01';     //主端
    const USER_NAME_GROWTH = '********';   //增长
    const CA_USER_NAME = 'dxm';
    
    //度小满公钥2021.7.27
    const DXM_PUB_KEY = "-----BEGIN CERTIFICATE-----
MIIEpDCCAoygAwIBAgIIGhX7L0iLAfkwDQYJKoZIhvcNAQELBQAwLjEOMAwGA1UE
AwwFRHhtQ0ExDzANBgNVBAoMBmR4bXBheTELMAkGA1UEBhMCQ04wHhcNMTkwOTI0
MTQxMTUzWhcNMzkwOTE5MTQxMTUzWjB1MRcwFQYDVQQDDA53d3cuZHhtcGF5LmNv
bTESMBAGA1UECwwJZHV4aWFvbWFuMRIwEAYDVQQKDAlkdXhpYW9tYW4xEDAOBgNV
BAcMB0JlaWppbmcxEDAOBgNVBAgMB0JlaUppbmcxDjAMBgNVBAYTBUNoaW5hMIIB
IjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxuj58TWxA6ZioLL2rYtHkH2I
+pcGF7YQbfT6lVvXi2OQkQKM95RH/Q9vTM5gl8n9qcdM+TCfuay7x33m5VwWAwNV
flqp6g6NM1fQhsTG0V9qwFo+uNL8jSDRvtXCr8sPtaWgWfT9uhW1hi94ci1WzbQ3
EOCXB1TcH0Eo2b9nl7wlsI1v7U/DkHdFAlV5rKBbnHc8YgWnyKO+SDXAdwjItj3M
cOd2XLBqdvv/NWQTTs2UF6gsonFCR88q9pMPN7MvLnQW9jku6R5r1PmbdmprmIXb
29t5a6G9KU3gARbirGTSscIlnjWurjfqGb5SlzroxAp9Ye3FbZCm4Ytqg9EgcQID
AQABo38wfTAMBgNVHRMBAf8EAjAAMB8GA1UdIwQYMBaAFKYYvgPTeC+G8TqU6Pgh
ubtdodg0MB0GA1UdJQQWMBQGCCsGAQUFBwMCBggrBgEFBQcDBDAdBgNVHQ4EFgQU
GzenKiSLlzQY2w6GhlU4X7YqhagwDgYDVR0PAQH/BAQDAgXgMA0GCSqGSIb3DQEB
CwUAA4ICAQA/5tTfxAudvZwpKdmQW2/0gXCV5WshEAdat893n5lzEScX8AoA2pVg
1CI/xjdobq6jmY+a9jk2K4YFxdEwbbtJKCUd3GmFq/B/yNK1mMkmAt5GPhIldF1v
QFd1vQXjEYWMAJ71kvAfTGyAEfPVkPF6SZMSxSraz2am4YUvPabbxIoqZt8unWja
GBFOLVtK+wDSFmb05pIVpG8FRtWodSc9BQp8thniGpzfTiE94FSlmXbZIprj7EJe
RJ8uIf6iZ2c3wpJivNmflQNUWmqQFxv38tKDh/XL0lpgz1bEV5v6EKQmn5M9Hj3L
rRJtz4iGeTGpX+RK32VJ5+C0nLy0nvDJ2rYkBL8NueGw6gcoxtWZ7+0Avnn6aoNP
hVSvnfeCfHi1+gN/vh85dWVNetLhXgrdIF41MnSDRqNnI/b8RRJAQIHW+uJRwMYF
quCXKzuYuCQJ6Fvt0YhdesYmIHjeUep9IWnLpyAi07VsjIEauU1qIU9dj0NKuJXV
mvZ6WNTlkxh7NuuFex0yfXXDYNLzZbQ7zuLZyci1j8iLM7DFn0C0BsnoSUGA9MOk
BE5Dh5YSG/ig3hug2nwMPnAYPvaeFznhVCu/7DnuIBob8TB0hx0LoU0rjF8myq8R
I86/f4sXAQWT6Zc2LqDKA5qcIQGy3Ptsm1tZ6nCxhf++Ov0utiH7AA==
-----END CERTIFICATE-----";

    const BD_PRI_KEY_GROWTH = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

const BD_PRI_KEY_TBYY= "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";


    /**
        *
        *   钱包发红包service,打款
        *   @param
        *   @option
        *   @return
        *
     */
    public static function commitReceiveCash($arrInput){
        Bingo_Log::warning("commitReceiveCash Receive:" . serialize($arrInput));
        if (false === self::checkCashParam($arrInput)) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrRet = self::cashReceive($arrInput);
        if ($arrRet['errno'] == Tieba_Errcode::ERR_SUCCESS) {
            $arrUptParams = array(
                'field' => array(
                    //'bfb_order_no'=> strval($arrRet['data']['bfb_order_no']),
                    'pay_status'  => self::PACKET_STATUS_RECEIVED_SUCCESS,
                ),
                'cond' => array(
                    'id' => intval($arrInput['sub_redpacket_id']),
                ),
            );

            $arrRes = Dl_Redpacket_SubRedPacket::update($arrUptParams);
            if ($arrRes === false || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("call Dl_Redpacket_SubRedPacket update failed.input:[".serialize($arrUptParams)."].output:[".serialize($arrRes)."].");
            } else {
                $intAffectRows = intval($arrRes['data']);
                Bingo_Log::warning("Receive cash success " . serialize($arrInput));
            }

        } else {
            //支付失败后存失败原因的错误号
            $intFailReason = intval($arrRet['data']['transfer_status']);
            $arrUptParams  = array(
                'field' => array(
                    //'bfb_order_no'=> strval($arrRet['data']['bfb_order_no']),
                    'fail_reason'  => $intFailReason,
                ),
                'cond' => array(
                    'id' => intval($arrInput['sub_redpacket_id']),
                ),
            );

            $arrRes = Dl_Redpacket_SubRedPacket::update($arrUptParams);
            if ($arrRes === false || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("call Dl_Redpacket_SubRedPacket update failed.input:[".serialize($arrUptParams)."].output:[".serialize($arrRes)."].");
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
        *
        *   钱包发红包service,打款
        *   @param
        *   @option
        *   @return
        *
     */
    public static function cashReceive($arrInput){
        /*if (false === self::checkCashParam($arrInput)) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }*/

        $arrParam = array(
            'order_no'        => strval($arrInput['sub_redpacket_id']),
            'transfer_amount' => intval($arrInput['amount']),
            'opposite_no'     => strval($arrInput['user_id']),
        );
        $urlParam = self::buildReceiveParam($arrParam);
        $arrRet = Service_Libs_Base::get(self::BFB_DOMAIN_OFFLINE . self::BFB_API_RECEIVE_CASH . "?" . $urlParam);
        if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning("call Service_Libs_Base::post failed.input:[".serialize($arrParam)."].output:[".serialize($arrRet)."].");
            return $arrRet;
        }
        $xmlData   = simplexml_load_string($arrRet['data']);
        $arrBfbRet = json_decode(json_encode($xmlData), true);
        Bingo_Log::warning('BFB pay result:' . var_export($arrBfbRet,1));
        if ($arrBfbRet['transfer_status'] === "0" || $arrBfbRet['transfer_status'] === "20302") {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrBfbRet);
        }
        return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, $arrBfbRet);
    }

    /**
        *
        *   打款查询
        *   @param
        *   @option
        *   @return
        *
     */
    public static function cashQuery($arrInput) {
        $arrRet = self::buildQueryParam(__FUNCTION__,$arrInput);     
        if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning("call buildSign failed.input:[".serialize($arrInput)."].output:[".serialize($arrRet)."].");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $urlParam    = $arrRet['url_param'];
        Bingo_Timer::start("bfb_cash_query");
        $arrRet = Service_Libs_Base::get(self::BFB_DOMAIN.self::BFB_API_CASH_QUERY."?".$urlParam);
        Bingo_Timer::end("bfb_cash_query");
        if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning("call Service_Libs_Base::post failed.input:[".serialize($arrBfbParam)."].output:[".serialize($arrRet)."].");
            return $arrRet;
        }
        $arrBfbRet = json_decode($arrRet['data'],true);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrBfbRet);
    }

     /**
     *   @param
     *   @return
     */
    protected function checkCashParam($arrInput) {

        $intPrimaryRedpacketId = intval($arrInput['sub_redpacket_id']);
        $intUserId = intval($arrInput['user_id']);
        $arrParam = array(
            'order_no' => strval($arrInput['sub_redpacket_id']),
            'transfer_amount' => intval($arrInput['amount']),
            'opposite_no'     => strval($arrInput['user_id']),
        );
        //读取子红包列表，更新之后强制读主
        $arrSelParams = array(
            'field' => array(
                'pay_status',
            ),
            'cond' => array(
                'id' => $intPrimaryRedpacketId,
                'user_id' => $intUserId,
            ),
        );
        $arrRes = Dl_Redpacket_SubRedPacket::select($arrSelParams);
        if ($arrRes === false || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call Dl_Redpacket_SubRedPacket select failed.input:[".serialize($arrSelParams)."].output:[".serialize($arrRes)."].");
            //return $arrRes;
        }
        $arrRecord = isset($arrRes['data']) ? $arrRes['data'] : array();
        $intPayStatus = intval($arrRecord[0]['pay_status']);
        if ($intPayStatus != Service_Redpacket_Redpacket::PACKET_STATUS_UNRECEIVED) {
            return false;
        }
        return true;
    }

    /**
     *   @param
     *   @return
     */
    protected static function buildQueryParam($arrInput) {
        $arrDefaultParam    = array(
            'sp_no' => isset($arrInput['sp_no']) ? $arrInput['sp_no'] : self::BFB_SP_NO,
            'version' => 1,
            //'sign_method'   => self::BFB_SIGN_METHOD,
        );
        if(!is_array($arrInput)){
            $arrInput   = array();
        }
        $arrOutput = array_merge($arrDefaultParam,$arrInput);
        foreach($arrOutput as $key => $value){
            if(!in_array($key,self::$arrApiQueryKeys)){
                unset($arrOutput[$key]);
            }
        }
        return $arrOutput;
    }

    /**
        *
        *   构造trans参数
        *   @param
        *   @option
        *   @return
        *
     */
    protected static function buildReceiveParam ($arrInput) {
        $strSpNo    = isset($arrInput['sp_no']) ? strval($arrInput['sp_no']) : self::BFB_SP_NO;
        $strOrderNo = strval($arrInput['order_no']);
        $arrDefaultParam    = array(
            'service_code' => self::BFB_SERVICECODE,
            'sp_no' => isset($arrInput['sp_no']) ? $arrInput['sp_no'] : self::BFB_SP_NO,
            'order_create_time' => date('YmdHis'),
            'order_no'      => $strOrderNo,
            'transfer_desc' => '',
            'transfer_amount' => intval($arrInput['transfer_amount']),
            'currency'  => self::BFB_CURRENCY,
            'input_charset' => self::BFB_INPUT_CHARSET,
            'opposite_type' => self::BFB_OPPOSITE_TYPE_UNIONID,
            'opposite_no'   => strval($arrInput['opposite_no']),
            //'opposite_truename' => strval($arrInput['opposite_truename']),
            'output_type'   => self::BFB_OUTPUT_FORMAT,
            'output_charset' => self::BFB_OUTPUT_CHARSET,
            'version' => 1,
            //'sign_method'   => self::BFB_SIGN_METHOD,
        );

        if(self::BFB_SP_NO != $arrDefaultParam['sp_no']){
            $arrDefaultParam['order_no'] = strval($arrInput['order_no']);
        }

        // 订单描述
        $strTransferDesc = isset($arrInput['transfer_desc']) ? strval($arrInput['transfer_desc']) : '';
        if('' == $strTransferDesc){
            $strTransferDesc = self::BFB_TRANSFER_DESC;
        }
        $arrDefaultParam['transfer_desc'] = $strTransferDesc;
        $arrDefaultParam['transfer_desc'] = Bingo_Encode::convert($arrDefaultParam['transfer_desc'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);

        $arrDefaultParam = self::buildSortParam($arrDefaultParam);
        $strParam   = self::paramToString($arrDefaultParam);
        //$strSign    = md5($strParam . '&key=' . self::$arrSecretKey[$strSpNo]);
        //sign 添加到参数最后
        //$arrDefaultParam['sign'] = $strSign;
        $strUrlParam   = http_build_query($arrDefaultParam);
        //Bingo_Log::warning("md5:[$strParam].url param:[$strUrlParam].sign:[$strSign]");

        return $strUrlParam;
    }

    /**
        *
        *   构造排序参数
        *   @param
        *   @option
        *   @return
        *
     */
    protected static function buildSortParam($arrInput){
        ksort($arrInput);
        reset($arrInput);
        return $arrInput;
    }

    /**
        *
        *   数组转成加密串
        *   @param
        *   @option
        *   @return
        *
     */
    protected static function paramToString($arrInput){
        unset($arrInput['sign']);
        $strOutput  = '';
        foreach($arrInput as $key => $value){
            if(!empty($strOutput)){
                $strOutput  .= "&";
            }
            $strOutput  .= "$key=$value";
        }
        return $strOutput;
    }


    /**
     * <AUTHOR>
     * @brief    接口：商户付款给用户，给贴吧其它团队使用
     * @param    array  $arrInput
     * @return   array
     *
     * todo 示例 http://kangqinmou.service.tieba.otp.baidu.com/service/cash?method=transferMoney&format=json&ie=utf-8&user_id=220576629&order_no=226&transfer_amount=1
     */
    public static function transferMoney($arrInput){

        Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' input 输入: [' . json_encode($arrInput) . '], $_SERVER: [' . json_encode($_SERVER) . "].\n");

        if (empty($arrInput['user_id'])){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', user_id is empty, input: [' . json_encode($arrInput) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'user_id is empty');
        }

        $arrOutput = Tieba_Service::call ( 'tbmall', 'encodeDXMUnionId', array('user_id' => $arrInput['user_id']), null, null, 'post', 'php', 'utf-8');
        if ($arrOutput == false || $arrOutput ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ( "call tbmall encodeDXMUnionId fail. user_id:" . $arrInput['user_id'] . " output: " . serialize($arrOutput));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'encodeDXMUnionId error');
        }
        $strDXMUnionId = $arrOutput['data']['dxmid'];
        Bingo_Log::pushNotice('dxmid',$strDXMUnionId);

        // 获取订单号, 转账金额, 被打款人的用户id, 商户号, 订单描述
        $arrParams = array(
            'order_no'        => isset($arrInput['order_no'])        ? strval($arrInput['order_no'])        : '',
            'transfer_amount' => isset($arrInput['transfer_amount']) ? intval($arrInput['transfer_amount']) : 0,
            'opposite_no'     => $strDXMUnionId,
            'sp_no'           => isset($arrInput['sp_no'])           ? strval($arrInput['sp_no'])           : '',
            'transfer_desc'   => isset($arrInput['transfer_desc'])   ? strval($arrInput['transfer_desc'])   : '',
        );
        if(!$arrParams['order_no'] || $arrParams['transfer_amount'] <= 0 || empty($arrParams['opposite_no'])){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', order_no|transfer_amount|opposite_no is empty, input: [' . json_encode($arrInput) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'order_no|transfer_amount|user_id is empty');
        }

        // 商户号, 请求的域名
        $strHost = Bingo_Http_Request::getServer('SERVER_NAME', '');
        if($strHost && strstr($strHost, '.tieba.otp.baidu.com')){
            $arrParams['sp_no'] = self::BFB_SP_NO_TEST;     // 线下环境的商户号
            $strBfbHost = self::BFB_DOMAIN_TEST;            // 线下环境的接口的host，该host不是永久的
        }else{
            $strBfbHost = self::BFB_DOMAIN;
        }
        if(empty($arrParams['sp_no'])){
            $arrParams['sp_no'] = self::BFB_SP_NO_GROWTH;
        }
        /*if(empty(self::$arrSecretKey[$arrParams['sp_no']])){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', sp_no not existed, input: [' . json_encode($arrInput) . '], params: [' . json_encode($arrParams) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'sp_no not existed');
        }*/

        // 订单描述
        if(self::BFB_SP_NO_GROWTH == $arrParams['sp_no'] && '' == $arrParams['transfer_desc']){
            $arrParams['transfer_desc'] = '贴吧帮拆红包';
        }
        if('' == $arrParams['transfer_desc']){
            $arrParams['transfer_desc'] = '打款';
        }

        // 构建请求的参数
        $queryString = self::buildReceiveParam($arrParams);
        $AESKey = Util_Encryption::GenRandomAesKey();
        $arrPara['spNo'] = $arrParams['sp_no'];
        $arrPara['userName'] = self::USER_NAME_GROWTH;
        $arrPara['caUserName'] = self::CA_USER_NAME;
        $arrPara['encryptData'] = Util_Encryption::AES_CFB_Encrypt($AESKey, $queryString);
        $arrPara['encryptKey'] = Util_Encryption::RSA_Encrypt(self::DXM_PUB_KEY, $AESKey);
        $arrPara['signData'] = Util_Encryption::Sign_SHA256(self::BD_PRI_KEY_GROWTH, $queryString);
        $strUrl = $strBfbHost . self::BFB_API_RECEIVE_CASH . '?' . http_build_query($arrPara);

        // 发起请求
        $arrRet = Service_Libs_Base::get($strUrl);
        Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . " transferMoney url is [{$strUrl}], result: [" . json_encode($arrRet) . "].\n");
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno'] || empty($arrRet['data'])){
            Bingo_Log::warning(__CLASS__ . '::' .__FUNCTION__ . ", visit duxiaoman url failed, url: [{$strUrl}], output: [" . json_encode($arrRet) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_GET_URL_RESULT_FAILED, 'visit duxiaoman url failed');
        }

        // 处理结果
        $arrRet = json_decode($arrRet['data'], true);
        $mAESKey = Util_Encryption::RSA_Decrypt(self::BD_PRI_KEY_GROWTH, $arrRet['encryptKey']);
        $mData = Util_Encryption::AES_CFB_Decrypt($mAESKey, $arrRet['encryptData']);
        $res = Util_Encryption::VerifySign_SHA256(self::DXM_PUB_KEY, $mData, $arrRet['signData']);
        $mData = simplexml_load_string(strval($mData));
        $arrRet = json_decode(json_encode($mData), true);        // $arrRet = (array) $xmlData;

        Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' transferMoney deal result: [' . json_encode($arrRet) . "].\n");
        if(!is_array($arrRet) || !isset($arrRet['transfer_status']) || !is_numeric($arrRet['transfer_status'])){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ", url response error from duxiaoman, url: [{$strUrl}], output: [" . json_encode($arrRet) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, $arrRet);
        }

        // return
        $strErrno = intval($arrRet['transfer_status']);
        if(0 == $strErrno){
            return self::_errRet($strErrno, array(), 'transfer money success');
        }elseif(20302 == $strErrno){

            if(self::BFB_SP_NO_GROWTH == $arrParams['sp_no']){ // 历史原因, 如果后续这个商户号下线了, 则可去掉这段代码
                $strErrno = Tieba_Errcode::ERR_SUCCESS;
            }
            return self::_errRet($strErrno, array(), 'transfer repeat');
        }

        return self::_errRet($strErrno, $arrRet, 'error from duxiaoman');
    }



    /**
     * @brief   查询转账订单
     * @param   array  $arrInput
     * @return  array
     *
     * todo 示例 http://kangqinmou.service.tieba.otp.baidu.com/service/cash?method=getTransferOrderInfo&format=json&ie=utf-8&order_no=226
     */
    public static function getTransferOrderInfo($arrInput){

        // 获取订单号
        $strOrderNo = isset($arrInput['order_no']) ? strval($arrInput['order_no']) : '';
        if(!$strOrderNo){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', order_no is empty, input: [' . serialize($arrInput) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'order_no is empty');
        }

        // 获取请求参数
        $arrParams = array(
            'sp_no' => isset($arrInput['sp_no']) ? strval($arrInput['sp_no']) : '',
            'order_no' => $strOrderNo,
            'input_charset' => self::BFB_INPUT_CHARSET,         // 1 gbk  2 utf-8
            'output_charset' => self::BFB_OUTPUT_CHARSET,       // 1 gbk  2 utf-8
            'output_type' => self::BFB_OUTPUT_FORMAT,           // 1 xml  2 json
            'version' => 1,
            'sign_method' => self::BFB_SIGN_METHOD,             // 1 md5  2 sha-1
        );
        $strHost = Bingo_Http_Request::getServer('SERVER_NAME', '');
        if($strHost && strstr($strHost, '.tieba.otp.baidu.com')){
            $arrParams['sp_no'] = self::BFB_SP_NO_TEST;     // 线下环境的商户号
            $strBfbHost = self::BFB_DOMAIN_TEST;            // 线下环境的接口的host，该host不是永久的
        }else{
            $strBfbHost = self::BFB_DOMAIN;
        }
        if(empty($arrParams['sp_no'])){
            $arrParams['sp_no'] = self::BFB_SP_NO_GROWTH;
        }
        if(empty(self::$arrSecretKey[$arrParams['sp_no']])){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', sp_no not existed, input: [' . serialize($arrInput) . '], params: [' . serialize($arrParams) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'sp_no not existed');
        }

        // 拼接url
        $arrParams = self::buildSortParam($arrParams);
        $strParams = self::paramToString($arrParams);
        $arrParams['sign'] = md5($strParams . '&key=' . self::$arrSecretKey[$arrParams['sp_no']]);
        $strUrl = $strBfbHost . '/api/0/transfer_application_query?' . http_build_query($arrParams);

        // 发起请求
        $arrRet = Service_Libs_Base::get($strUrl);
        Bingo_Log::warning(var_export($arrRet, 1));
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno'] || empty($arrRet['data'])){
            Bingo_Log::warning(__CLASS__ . '::' .__FUNCTION__ . ", visit duxiaoman url failed, url: [{$strUrl}], output: [" . serialize($arrRet) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_GET_URL_RESULT_FAILED, 'visit duxiaoman url failed');
        }

        // 处理结果
        $xmlData = simplexml_load_string(strval($arrRet['data']));
        $arrRet = json_decode(json_encode($xmlData), true);        // $arrRet = (array) $xmlData;
        Bingo_Log::warning(var_export($arrRet, 1));
        if(!is_array($arrRet) || !isset($arrRet['query_status']) || !is_numeric($arrRet['query_status'])){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ", url response error from duxiaoman, url: [{$strUrl}], output: [" . serialize($arrRet) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, $arrRet);
        }

        $intQueryStatus = intval($arrRet['query_status']);
        if(0 != $intQueryStatus){
            return self::_errRet($intQueryStatus, $arrRet, 'error from duxiaoman');
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }



}