<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date      2018: 5: 29 23: 35: 24
 * @version
 * @structs & methods(copied from idl.)
 */
class Service_Redpacket_List extends Service_Libs_Base
{

    const DB_NAME = 'forum_tbmall';
    const PRIMARY_REDPACKET_TABLE = 'primary_redpacket_record';
    const SUB_REDPACKET_TABLE = 'sub_redpacket_record';

    /**
     * 读取用户发的主红包数量和金额
     *
     * @param array $arrInput
     *
     * @return array
     */
    public static function getPrimaryRedpacketNumAndAmountByUid($arrInput = array())
    {
        // 获取和验证入参
        $user_id = isset($arrInput['user_id']) ? intval($arrInput['user_id']) : 0;
        if($user_id <= 0){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', user_id is empty, input: [' . serialize($arrInput) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'user id is empty');
        }

        // 查询forum_tbmall.primary_redpacket_record表
        $arrParams = array(
            'field' => array('count(1) as total_num', 'sum(total_money) as total_money'),
            'cond' => array(
                'user_id'    => $user_id,
                'pay_status' => Service_Redpacket_Redpacket::PAY_STATUS_SUCCESS,    // pay_status: 1 支付成功
            ),
        );
        $arrRet = Dl_Redpacket_PrimaryRedPacket::select($arrParams);
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', get primary redpacket num and amount failed, input: [' .
                serialize($arrParams) . '], output: [' . serialize($arrRet) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), 'get primary redpacket num and amount failed');
        }

        // return
        $ret = array(
            'total_num'   => isset($arrRet['data'][0]['total_num']) ? intval($arrRet['data'][0]['total_num']) : 0,
            'total_money' => isset($arrRet['data'][0]['total_money']) ? intval($arrRet['data'][0]['total_money']) : 0,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $ret);
    }

    /**
     * 根据用户id获取发放的主红包列表
     *
     * @param array $arrInput
     *
     * @return array
     */
    public static function getPrimaryRedpacketListByUid($arrInput = array())
    {
        // 获取和验证入参
        $pn = isset($arrInput['pn']) ? intval($arrInput['pn']) : 1;
        $ps = isset($arrInput['ps']) ? intval($arrInput['ps']) : 10;
        $pn = ($pn < 1) ? 1 : $pn;
        $ps = ($ps < 1) ? 10 : $ps;

        $intUserId = isset($arrInput['user_id']) ? intval($arrInput['user_id']) : 0;
        if($intUserId <= 0){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', user_id is empty, input: [' . serialize($arrInput) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'user id is empty');
        }

        // 查询forum_tbmall.primary_redpacket_record
        $arrParams = array(
            'append' => 'order by start_time desc limit ' . ($ps*$pn - $ps) . ", {$ps} ",
            'cond' => array(
                'user_id'    => $intUserId,
                'pay_status' => Service_Redpacket_Redpacket::PAY_STATUS_SUCCESS,    // pay_status: 1支付成功
            ),
        );
        $arrPrimaryList = Dl_Redpacket_PrimaryRedPacket::select($arrParams);
        if(!$arrPrimaryList || Tieba_Errcode::ERR_SUCCESS != $arrPrimaryList['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', get primary red packet list failed, input: [' .
                serialize($arrParams) . '], output: [' . serialize($arrPrimaryList) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), 'get primary red packet list failed');
        }
        $arrPrimaryList = $arrPrimaryList['data'];

        // return
        Bingo_Log::warning(var_export($arrPrimaryList, 1));
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrPrimaryList);
    }

    /**
     * 读取用户发的子红包数量和金额
     *
     * @param array $arrInput
     *
     * @return array
     */
    public static function getSubRedpacketNumAndAmountByUid($arrInput = array())
    {
        // 获取和验证入参
        $user_id = (int) $arrInput['user_id'];
        if($user_id <= 0){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', user_id is empty, input: [' . serialize($arrInput) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'user_id is empty');
        }

        // 查询数据库
        $arrParams = array(
            'field' => array('count(1) as total_num', 'sum(money) as total_money'),
            'cond'  => array(
                'user_id'    => $user_id,
            ),
        );
        $arrRet = Dl_Redpacket_SubRedPacket::select($arrParams);
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', get sub redpacket num and amount failed, input: [' . serialize($arrParams) . '], output: [' . serialize($arrRet) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), 'get sub redpacket num and amount failed');
        }
        $arrRet = !empty($arrRet['data']) && is_array($arrRet['data']) ? $arrRet['data'] : array();

        // return
        $arrReturn = array(
            'total_num'   => isset($arrRet[0]['total_num'])   ? intval($arrRet[0]['total_num']) : 0,
            'total_money' => isset($arrRet[0]['total_money']) ? intval($arrRet[0]['total_money']) : 0,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrReturn);
    }

    /**
     * 根据用户id获取发放的子红包列表
     *
     * @param array $arrInput
     *
     * @return array
     */
    public static function getSubRedpacketListByUid($arrInput = array())
    {
        // 获取和验证入参
        $user_id = (int) $arrInput['user_id'];
        $pn = (int) $arrInput['pn'];
        $ps = (int) $arrInput['ps'];
        $pn = ($pn < 1) ? 1  : $pn;
        $ps = ($ps < 1) ? 10 : $ps;
        if($user_id <= 0){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', user_id is empty, input: [' . serialize($arrInput) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'user_id is empty');
        }

        // 查询数据库
        $arrParams = array(
            'append' => 'order by primary_redpacket_id desc limit ' . ($ps*$pn - $ps) . ", {$ps} ",
            'cond'  => array(
                'user_id'    => $user_id,
            ),
        );
        $arrRet = Dl_Redpacket_SubRedPacket::select($arrParams);
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', get sub redpacket list failed, input: [' .
                serialize($arrParams) . '], output: [' . serialize($arrRet) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), 'get sub redpacket list failed');
        }
        $arrRet = !empty($arrRet['data']) && is_array($arrRet['data']) ? $arrRet['data'] : array();

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

    /**
     * 根据主红包id查主红包信息
     *
     * @param array $arrInput
     *
     * @return array
     */
    public static function getPrimaryRedpacketByPrimaryIds($arrInput = array())
    {
        // 获取主红包ids
        $primary_redpacket_ids = !empty($arrInput['primary_redpacket_ids']) && is_array($arrInput['primary_redpacket_ids']) ? $arrInput['primary_redpacket_ids'] : array();
        $arrTemp = array();
        foreach($primary_redpacket_ids as $intId){
            $intId = (int) $intId;
            if($intId <= 0){
                continue;
            }
            $arrTemp[$intId] = 1;
        }
        $primary_redpacket_ids = array_keys($arrTemp);
        if(!$primary_redpacket_ids){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', primary redpacket ids is empty, input: [' . serialize($arrInput) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'primary redpacket ids is empty');
        }

        // 查询数据库: forum_tbmall.primary_redpacket_record
        $arrParams = array(
            'cond'   => 'id in (' . join(', ', $primary_redpacket_ids) . ') ',
        );
        $arrPrimaryList = Dl_Redpacket_PrimaryRedPacket::select($arrParams);
        if(!$arrPrimaryList || Tieba_Errcode::ERR_SUCCESS != $arrPrimaryList['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', get primary redpacket list failed, input: [' . serialize($arrParams) . '], output: [' . serialize($arrPrimaryList) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), 'get primary redpacket list failed');
        }
        $arrPrimaryList = !empty($arrPrimaryList['data']) && is_array($arrPrimaryList['data']) ? $arrPrimaryList['data'] : array();
        if(!$arrPrimaryList){
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }

        // 获取主红包id与主红包info的映射
        $arrTemp = array();
        foreach($arrPrimaryList as $item){
            $intId = (int) $item['id'];
            $arrTemp[$intId] = $item;
        }
        $arrPrimaryList = $arrTemp;
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrPrimaryList);
    }

    /**
     * 根据主红包id查子红包信息
     *
     * @param array $arrInput
     *
     * @return array
     */
    public static function getSubRedpacketByPrimaryId($arrInput = array())
    {
        // 页码
        $pn = isset($arrInput['pn']) ? intval($arrInput['pn']) : 1;
        $ps = isset($arrInput['ps']) ? intval($arrInput['ps']) : 10;
        $pn = ($pn < 1) ? 1  : $pn;
        $ps = ($ps < 1) ? 10 : $ps;

        // 主红包id
        $primary_redpacket_id = isset($arrInput['primary_redpacket_id']) ? intval($arrInput['primary_redpacket_id']) : 0;
        if($primary_redpacket_id <= 0){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', primary_redpacket_id is empty, input: [' . serialize($arrInput) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'primary_redpacket_id is empty');
        }

        // 根据主红包id获取子红包列表
        $arrStatus = array(
            Service_Redpacket_Redpacket::PACKET_STATUS_UNRECEIVED,          // 待领取
            Service_Redpacket_Redpacket::PACKET_STATUS_RECEIVED_SUCCESS,    // 已领取
            Service_Redpacket_Redpacket::PACKET_STATUS_INVALID,             // 已失效
        );
        $arrParams = array(
            'append' => 'order by update_time desc limit ' . ($ps*$pn - $ps) . ", {$ps} ",
            'cond' => array(
                'primary_redpacket_id' => $primary_redpacket_id,
                'pay_status' => array(
                    'val'    => '(' . join(', ', $arrStatus) . ')',
                    'opt'    => 'in',
                    'quotes' => 0,
                ),
            ),
        );
        $arrSubList = Dl_Redpacket_SubRedPacket::select($arrParams);
        if(!$arrSubList || Tieba_Errcode::ERR_SUCCESS != $arrSubList['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', get sub redpacket list failed, input: [' .
                serialize($arrParams) . '], output: [' . serialize($arrSubList) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), 'get sub redpacket list failed');
        }
        if(empty($arrSubList['data'])){
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, array(), 'sub redpacket list is empty');
        }
        $arrSubList = $arrSubList['data'];

        // 获取当前主红包的已被抢的子红包的数量
        $arrParams = array(
            'field' => array('count(1) as sub_num'),
            'cond'  => array(
                'primary_redpacket_id' => $primary_redpacket_id,
                'pay_status' => array(
                    'val'    => '(' . join(', ', $arrStatus) . ')',
                    'opt'    => 'in',
                    'quotes' => 0,
                ),
            ),
        );
        $arrRet = Dl_Redpacket_SubRedPacket::select($arrParams);
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', get sub redpacket count failed, input: [' . serialize($arrParams) . '], output: [' . serialize($arrRet) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), 'get sub redpacket count failed');
        }
        $intSubNum = isset($arrRet['data'][0]['sub_num']) ? intval($arrRet['data'][0]['sub_num']) : 0;

        // return
        $arrReturn = array(
            'sub_num'               => $intSubNum,      // 当前主红包已经被抢了多少子红包
            'sub_redpacket_list'    => $arrSubList,     // 当前主红包的子红包列表
            'current_sub_redpacket' => array(),         // 当前用户的子红包信息
        );
        $intUserId = isset($arrInput['user_id']) ? intval($arrInput['user_id']) : 0;
        if($intUserId <= 0){
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrReturn);
        }

        // 获取当前用户抢到的子红包
        $arrParams = array(
            'append' => 'limit 1',
            'cond'   => array(
                'user_id' => $intUserId,
                'primary_redpacket_id' => $primary_redpacket_id,
            ),
        );
        $arrSubInfo = Dl_Redpacket_SubRedPacket::select($arrParams);
        if(!$arrSubInfo || Tieba_Errcode::ERR_SUCCESS != $arrSubInfo['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', get sub redpacket info failed for current user, input: [' . serialize($arrParams) . '], output: [' . serialize($arrSubInfo) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), 'get sub redpacket info failed for current user');
        }
        $arrSubInfo = empty($arrSubInfo['data'][0]) ? array() : $arrSubInfo['data'][0];
        $arrReturn['current_sub_redpacket'] = $arrSubInfo;

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrReturn);
    }




    /**
     * @brief   贴吧app的个人中心，是否展示"我的红包"button
     * @param   array  $arrInput
     * @return  array
     */
    public static function showRedpacketInProfile($arrInput){

        // 获取用户id
        $intUserId = isset($arrInput['user_id']) ? intval($arrInput['user_id']) : 0;
        if($intUserId <= 0){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', user id is empty, input: [' . serialize($arrInput) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'user id is empty');
        }

        // 查询子红包表
        $arrParams = array(
            'append' => 'limit 1',
            'cond'   => array(
                'user_id' => $intUserId,
            ),
        );
        $arrSubInfo = Dl_Redpacket_SubRedPacket::select($arrParams);
        if(!$arrSubInfo || Tieba_Errcode::ERR_SUCCESS != $arrSubInfo['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', get sub redpacket info failed, input: [' . serialize($arrParams) .
                '], output: [' . serialize($arrSubInfo) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), 'get sub redpacket info failed');
        }
        if(!empty($arrSubInfo['data'][0])){
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, array('is_show_redpacket' => 1));
        }
        unset($arrSubInfo);

        // 查询主红表
        $arrParams = array(
            'append' => 'limit 1',
            'cond'   => array(
                'user_id' => $intUserId,
            ),
        );
        $arrPrimaryInfo = Dl_Redpacket_PrimaryRedPacket::select($arrParams);
        if(!$arrPrimaryInfo || Tieba_Errcode::ERR_SUCCESS != $arrPrimaryInfo['errno']){
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ', get primary redpacket info failed, input: [' . serialize($arrParams) .
                '], output: [' . serialize($arrPrimaryInfo) . "].\n");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, array(), 'get primary redpacket info failed');
        }
        if(!empty($arrPrimaryInfo['data'][0])){
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, array('is_show_redpacket' => 1));
        }
        unset($arrPrimaryInfo);

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, array('is_show_redpacket' => 0));
    }


}















