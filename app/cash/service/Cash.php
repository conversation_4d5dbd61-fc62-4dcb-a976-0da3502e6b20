<?php 
class Service_Cash {
	private static $boolReloaded = false;
	public static $service_ie = 'utf-8';
	private static $_default_ie = 'utf-8';

	const INVITE_CODE_LEN = 7;	//邀请码长度
	// const INVITE_CODE_SCALE_LEN = 62;	//62进制邀请码
	
	/**
	 * @param
	 * @return unknown|multitype:unknown
	 */
	private static function _errRet($errno, $data = array()){
		return array(
				'errno'  => $errno,
				'errmsg' => Tieba_Error::getErrmsg($errno),
				'data'   => $data,
		);
	}
	
	/**
	 * @param
	 * @return unknown|multitype:unknown
	 */
	private static function _getMethodsArr(){
		$strCacheFile = self::_getCacheFile();
		if(file_exists($strCacheFile)){
			include ($strCacheFile);
			return $methods_to_subservices;
		}
	
		return self::_reloadMethodsArr();
	}
	
	/**
	 * @param
	 * @return unknown|multitype:unknown
	 */
	private static function _reloadMethodsArr(){
		$path = dirname(__FILE__);
		$dirs = scandir($path);
		//var_dump($dirs);
		$methods_to_subservices = array();
		foreach ($dirs as $dir){
			if($dir === '.' || $dir === '..'){
				continue;
			}
			if(is_dir("$path/$dir")){
				Bingo_Log::debug("find sub dir [$dir]");
				$udir = ucfirst($dir);
				$classFile = "$path/$dir/$udir.php";
				$className = "Service_$udir"."_$udir";
				if(file_exists($classFile)){
					require_once ($classFile);
					if(class_exists($className)){
						$ref = new ReflectionClass($className);
						$methods = $ref->getMethods(ReflectionMethod::IS_PUBLIC);
						//var_dump($methods);
						foreach ($methods as $method){
							$methods_to_subservices[$method->name] = $dir;
						}
							
					}else{
						Bingo_Log::warning("class name not exist [$className]");
						continue;
					}
	
				}else{
					Bingo_Log::warning("invalid dir found [$dir]");
					continue;
				}
			}
	
	
		}
	
		if(count($methods_to_subservices) == 0){
			Bingo_Log::warning("no valid method found. something wrong?");
		}
		$str = var_export ($methods_to_subservices,true);
		$str = "<?php\n\$methods_to_subservices = \n".$str.";\n?>";
		$final_file = self::_getCacheFile();
		$tmp_class_file = $final_file.".bak".rand();
		file_put_contents ($tmp_class_file,$str);
	
		//以下将临时文件修改为实际文件
		if (file_exists($tmp_class_file)) {
			rename ($tmp_class_file,$final_file);
		}
		if (file_exists ($tmp_class_file)) {
			unlink ($tmp_class_file);
		}
	
		self::$boolReloaded = true;
		return $methods_to_subservices;
	}
	
	/**
	 * @param
	 * @return unknown|multitype:unknown
	 */
	private static function _getCacheFile(){
		return dirname(__FILE__).'/methods.php';
	}
	
	/**
	 * @param
	 * @return unknown|multitype:unknown
	 */
	public static function call($name, $arguments){
	
		$methods_to_subservices = self::_getMethodsArr();
		if(!array_key_exists($name, $methods_to_subservices)){
			if(self::$boolReloaded){
				Bingo_Log::warning("methods call not found in service.[$name]");
				return self::_errRet(Tieba_Errcode::ERR_METHOD_NOT_FOUND);
			}else{
				$methods_to_subservices = self::_reloadMethodsArr();
				if(!array_key_exists($name, $methods_to_subservices)){
					Bingo_Log::warning("methods call not found in service.[$name]");
					return self::_errRet(Tieba_Errcode::ERR_METHOD_NOT_FOUND);
				}
			}
		}
		$subService = ucfirst(strtolower($methods_to_subservices[$name]));
		$subServiceFile = dirname(__FILE__)."/".strtolower($subService)."/$subService.php";
		$subServiceClass = "Service_$subService"."_$subService";
	
		if(!file_exists($subServiceFile)){
			Bingo_Log::warning("file call not found in service.[$subServiceFile]");
			return self::_errRet(Tieba_Errcode::ERR_FILE_NOT_FOUND);
		}
		require_once ($subServiceFile);
	
		if(method_exists($subServiceClass, 'preCall')){
			call_user_func_array(array($subServiceClass, 'preCall'),array($arguments));
		}
		$res = call_user_func_array(array($subServiceClass, $name),array($arguments));
		if(method_exists($subServiceClass, 'postCall')){
			call_user_func_array(array($subServiceClass, 'postCall'),array($arguments));
		}
		if($res){
			//$jsRes = Bingo_String::array2json($res);
			return  $res;
		}else{
			Bingo_Log::warning("call user func failed. [$subServiceClass::$name] .");
			if(!self::$boolReloaded){
				self::_reloadMethodsArr();
			}
			return self::_errRet(Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL);
		}
			
	}
	
	/**
	 * @param
	 * @return unknown|multitype:unknown
	 */
	public static function getIE($name){
	
		$methods_to_subservices = self::_getMethodsArr();
		if(!array_key_exists($name, $methods_to_subservices)){
			if(self::$boolReloaded){
				return self::$_default_ie;
			}else{
				$methods_to_subservices = self::_reloadMethodsArr();
				if(!array_key_exists($name, $methods_to_subservices)){
					return self::$_default_ie;
				}
			}
		}
		$subService = ucfirst(strtolower($methods_to_subservices[$name]));
		$subServiceFile = dirname(__FILE__)."/".strtolower($subService)."/$subService.php";
		$subServiceClass = "Service_$subService"."_$subService";
	
		if(!file_exists($subServiceFile)){
			return self::$_default_ie;
		}
		require_once ($subServiceFile);
	
		$service_ie = self::$_default_ie;
	
		if(property_exists($subServiceClass, 'service_ie'))
		{ //获取service的字符编码ie参数
			$service_pro = get_class_vars($subServiceClass);
			$tmp_ie = $service_pro['service_ie'];
			if($tmp_ie==='gbk' || $tmp_ie==='utf-8')
			{
				$service_ie = $tmp_ie;
			}
		}
		return $service_ie;
	}

	/**
	 * [createInviteCode 生成邀请码]
	 * <AUTHOR> <[<email address>]>
	 * @param  [type] $arrInput [description]
	 * @return [type]          [description]
	 */
	public static function createInviteCode($arrInput) {
		if (empty($arrInput['user_id']) || 0 >= intval($arrInput['user_id'])) {
			Bingo_Log::warning("input params invalid. input: [" . serialize($arrInput) . "]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrRet = array();
		//$strSource = 'ShdQb75LFoGJRA4rOT1kxUPvtyj9iZ62azgWnBpXlVsmMfwucIHEeK8N3DqYC';
		$strSource = 'SQ75FGJRA4T1UP9Z62WBXVMHEK8N3DYC';
		$intSourceLen = strlen($strSource);
		$intUid    = intval($arrInput['user_id']);
		$strCode   = '';
		while ($intUid > 0) {
			$intMod = $intUid % $intSourceLen;
			$intUid = ($intUid - $intMod) / $intSourceLen;
			$strCode = $strSource[$intMod].$strCode;
		}
        // 最高位
		if (empty($strCode[6])) {
			$strCode = str_pad($strCode,7,'0',STR_PAD_LEFT);
		}

		$arrRet['invite_code'] = $strCode;
		return self:: _errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
	}

	/**
	 * [deInviteCode 邀请码转换uid]
	 * <AUTHOR> <[<email address>]>
	 * @param  [type] $arrInput [description]
	 * @return [type]          [description]
	 */
	public static function deInviteCode($arrInput) {
		$intLen = strlen($arrInput['invite_code']);
		if (empty($arrInput['invite_code']) || self::INVITE_CODE_LEN > $intLen) {
            Bingo_Log::warning("input params invalid. input: [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrRet = array();
		//$strSource = 'ShdQb75LFoGJRA4rOT1kxUPvtyj9iZ62azgWnBpXlVsmMfwucIHEeK8N3DqYC';
		$strSource = 'SQ75FGJRA4T1UP9Z62WBXVMHEK8N3DYC';
		$intSourceLen = strlen($strSource);
		$strCode = strval($arrInput['invite_code']);
		if (strrpos($strCode, '0') !== false) {
			$strCode = substr($strCode, strrpos($strCode, '0') + 1);
		}
		$intCodeLen = strlen($strCode);
		$strCode = strrev($strCode);
		$intUid = 0;

        for ($i=0; $i < $intCodeLen; $i++) { 
            $intUid += strpos($strSource, $strCode[$i]) * pow($intSourceLen, $i);
        }

		$arrRet['user_id'] = $intUid;
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
	}






	/**
	 * 读取用户发的主红包数量和金额
	 *
	 * @param $arrInput
	 *
	 * @return array
	 */
	public static function getPrimaryRedpacketNumAndAmountByUid($arrInput)
	{
		return Service_Redpacket_List::getPrimaryRedpacketNumAndAmountByUid($arrInput);
	}

	/**
	 * 根据用户id获取发放的主红包列表
	 *
	 * @param $arrInput
	 *
	 * @return array
	 */
	public static function getPrimaryRedpacketListByUid($arrInput)
	{
		return Service_Redpacket_List::getPrimaryRedpacketListByUid($arrInput);
	}

	/**
	 * 读取用户发的子红包数量和金额
	 *
	 * @param $arrInput
	 *
	 * @return array
	 */
	public static function getSubRedpacketNumAndAmountByUid($arrInput)
	{
		return Service_Redpacket_List::getSubRedpacketNumAndAmountByUid($arrInput);
	}

	/**
	 * 根据用户id获取发放的子红包列表
	 *
	 * @param $arrInput
	 *
	 * @return array
	 */
	public static function getSubRedpacketListByUid($arrInput)
	{
		return Service_Redpacket_List::getSubRedpacketListByUid($arrInput);
	}

	/**
	 * 根据主红包ids查主红包信息
	 *
	 * @param $arrInput
	 *
	 * @return array
	 */
	public static function getPrimaryRedpacketByPrimaryIds($arrInput)
	{
		return Service_Redpacket_List::getPrimaryRedpacketByPrimaryIds($arrInput);
	}

	/**
	 * 根据主红包id查子红包信息
	 *
	 * @param $arrInput
	 *
	 * @return array
	 */
	public static function getSubRedpacketByPrimaryId($arrInput)
	{
		return Service_Redpacket_List::getSubRedpacketByPrimaryId($arrInput);
	}

	/**
	 * snatchRedPacket
	 * @brief  抢红包
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function snatchRedPacket($arrInput)
	{
		return Service_Redpacket_Redpacket::snatchRedPacket($arrInput);
	}

    /**
     * getRedPacketList
     * @param $arrInput
     * @return array
     */
    public static function getRedPacketList($arrInput) {
        return Service_Redpacket_Redpacket::getRedPacketList($arrInput);
    }

	/**
	 * @brief   贴吧app的个人中心，是否展示"我的红包"button
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function showRedpacketInProfile($arrInput){
		return Service_Redpacket_List::showRedpacketInProfile($arrInput);
	}


    /**
     * getRedPacketCount
     * @param $arrInput
     * @return array
     */
    public static function getRedPacketCount($arrInput) {
        return Service_Redpacket_Redpacket::getRedPacketCount($arrInput);
    }

    /**
     * updateRedPacket
     * @param $arrInput
     * @return array
     */
    public static function updateRedPacket($arrInput) {
        return Service_Redpacket_Redpacket::updateRedPacket($arrInput);
    }

    /**
     * createRedPacket
     * @param $arrInput
     * @return array
     */
    public static function createRedPacket($arrInput) {
        return Service_Redpacket_Redpacket::createRedPacket($arrInput);
    }

    /**
     * receiveNmq
     * @param $arrInput
     * @return array
     */
    public static function receiveNmq($arrInput) {
        return Service_Redpacket_Redpacket::receiveNmq($arrInput);
    }

    /**
     * multiCreateSubRedPacket
     * @param $arrInput
     * @return array
     */
    public static function multiCreateSubRedPacket($arrInput) {
        return Service_Redpacket_Redpacket::multiCreateSubRedPacket($arrInput);
    }

    /**
     * createSubRedPacket
     * @param $arrInput
     * @return array
     */
    public static function createSubRedPacket($arrInput) {
        return Service_Redpacket_Redpacket::createSubRedPacket($arrInput);
    }

    /**
     * 支付
     * @param
     * @return int
     */
    /*public static function receiveRedpacket($arrInput) {
        return Service_Redpacket_Redpacket::receiveRedpacket($arrInput);
    }*/

    /**
     * 查剩余子红包
     * @param
     * @return int
     */
    public static function getRestSubPacket($arrInput) {
        return Service_Redpacket_Redpacket::getRestSubPacket($arrInput);
    }
    /**
        *
        *   钱包发红包service,打款
        *   @param
        *   @option
        *   @return
        *
     */
    public static function commitReceiveCash($arrInput) {
    	return Service_Redpacket_Wallet::commitReceiveCash($arrInput);
    }
    /**
        *
        *   钱包发红包service,打款
        *   @param
        *   @option
        *   @return
        *
     */
    public static function cashReceive($arrInput){
    	//这没写错，勿改,勿删
    	return Service_Redpacket_Wallet::commitReceiveCash($arrInput);
    }


	/**
	 * @brief   获取实名认证状态
	 * @param   array  $arrInput
	 * @return  array
	 */
	public static function getAuthStatus($arrInput){
		return Service_User_Auth::getAuthStatus($arrInput);
	}


	/**
	 * @brief    接口：商户付款给用户
	 * @param    array  $arrInput
	 * @return   array
	 */
	public static function transferMoney($arrInput){
		return Service_Redpacket_Wallet::transferMoney($arrInput);
	}



	/**
	 * @brief    接口：商户付款给用户
	 * @param    array  $arrInput
	 * @return   array
	 */
	public static function getTransferOrderInfo($arrInput){
		return Service_Redpacket_Wallet::getTransferOrderInfo($arrInput);
	}


}

