<?php

class Dl_Third_Order extends Dl_Db_Model {
    const DB_TABLE = 'third_order';
    const DB_NAME  = 'forum_cash';

    private static $_arrDB;
    private static $_instance;
    private static $arrField = array(
        'id',
        'user_id',
        'app_id',
        'order_id',
        'amount',
        'type',
        'third_order_id',
        'third_res',
        'in_out',
        'create_at',
        'create_by',
        'update_at',
        'update_by',
        'status',
        'mis_reason',
        'third_id',
        'id_card',
        'real_user_name',
        'phone',
        'to_bank_name',
        'ext',
    );

    /**
     * 获取数据库对象
     *
     * @param
     * @return db
     */
    public static function getModel() {
        $db_name = self::DB_NAME;
        $db_table= self::DB_TABLE;
        $arrKey  = $db_name.$db_table;

        if(isset(self::$_arrDB[$arrKey])){
            return self::$_arrDB[$arrKey];
        }else{
            $class_name = __CLASS__;
            self::$_arrDB[$arrKey] = new $class_name($db_table,$db_name);
            return self::$_arrDB[$arrKey];
        }
    }

    /**
     * 查询
     *
     * @param field[array]
     * @return
     */
    public static function select($arrInput) {
        if (!$arrInput['field']) {
            $arrInput['field'] = self::$arrField;
        }
        self::$_instance = self::getModel();
        return self::$_instance->baseSelect($arrInput);
    }

    /**
     * @brief 更新
     * @param
     * @return
     */
    public static function update($arrInput) {
        self::$_instance = self::getModel();
        $ret = self::$_instance->baseUpdate($arrInput);
        return $ret;
    }

    /**
     * @brief 插入
     * @param
     * @return
     */
    public static function insert($arrInput) {
        self::$_instance = self::getModel();
        return self::$_instance->baseInsert($arrInput);
    }


    /**
     * @brief  删除
     * @param  $arrInput
     * @return mixed
     */
    public static function deleteRecord($arrInput) {
        self::$_instance = self::getModel();
        return self::$_instance->baseDelete($arrInput);
    }

    /**
     * @brief  查询符合条件作品的总数
     * @param  $arrInput
     * @return array
     */
    public static function getTotalCount($arrInput) {
        $param = array(
            'field' => array('COUNT(id)'),
            'cond'  => $arrInput['cond'],
        );
        self::$_instance = self::getModel();
        $ret = self::$_instance->baseSelect($param);
        if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("get count failed. errno[{$ret['errno']}]");
            return self::$_instance->errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, 0);
        }
        return self::$_instance->errRet(Tieba_Errcode::ERR_SUCCESS,
            $ret['data'][0]['COUNT(id)']);
    }

    /**
     * @brief  自定义的query
     * @param  query[string]
     * @return array
     */
    public static function query($strSql) {
        self::$_instance = self::getModel();
        return self::$_instance->baseQuery($strSql);
    }

    /**
     * @brief 表字段过滤
     * @param array
     * @return array
     **/
    public static function filterFields($arrInput) {
        $arrRet = array();
        if (empty($arrInput) || !is_array($arrInput)) {
            return $arrRet;
        }
        foreach ($arrInput as $key=>$val) {
            if (in_array($key, self::$arrField)) {
                $arrRet[$key] = $val;
            }
        }
        return $arrRet;
    }

    /**
     * @brief  return the last SQL in DB
     * @return array
     */
    public static function getLastSQL()
    {
        self::$_instance = self::getModel();
        return self::$_instance->baseGetLastSQL();
    }
}

