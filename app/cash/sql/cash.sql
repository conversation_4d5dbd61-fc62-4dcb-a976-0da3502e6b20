# 贴吧答题表设计
CREATE DATABASE forum_cash;
USE forum_cash;

## 第三方信息表
DROP TABLE IF EXISTS `third_info`;
CREATE TABLE `third_info` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint(20) unsigned NOT NULL COMMENT 'baidu user id',
  `third_id` varchar(256) NOT NULL DEFAULT '' COMMENT 'third_id',
  `third_id_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '0-invalid,1-bankCard,2-b<PERSON><PERSON><PERSON><PERSON>,3-alipay,4-weixin',
  `real_user_name` varchar(128) NOT NULL default '' COMMENT 'real_user_name',
  `ext` varchar(1024) NOT NULL default '' COMMENT 'ext info serialize format',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT 'create_time',
  `modify_time` int(11) NOT NULL DEFAULT 0 COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id_type` (`user_id`, `third_id_type`),
  KEY `idx_third_id` (`third_id`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='third_info';

## 用户余额表
DROP TABLE IF EXISTS `user_balance`;
create table `user_balance`(
 `id` bigint unsigned NOT NULL auto_increment COMMENT 'id',
 `user_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT '用户id',
 `amount` bigint NOT NULL DEFAULT 0 COMMENT '用户余额/分',
 `create_at` int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间',
 `create_by` varchar(256) NOT NULL DEFAULT '' COMMENT '创建来源',
 `update_at` int unsigned NOT NULL DEFAULT 0 COMMENT '更新时间',
 `update_by` varchar(256) NOT NULL DEFAULT '' COMMENT '更新来源',
 `status` tinyint(1) unsigned NOT NULL DEFAULT 1 COMMENT '1 正常 2封禁',
 `ext` varchar(512) NOT NULL DEFAULT '' COMMENT '扩展',
 PRIMARY KEY (`id`),
 UNIQUE KEY `uk_uid` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='用户余额表';

## 上游业务表
DROP TABLE IF EXISTS `inner_resource_record`;
CREATE TABLE `inner_resource_record`(
    `id` bigint unsigned NOT NULL auto_increment COMMENT '自增id',
    `app_id` int unsigned NOT NULL DEFAULT 0 COMMENT 'appid',
    `order_id` varchar(64) NOT NULL DEFAULT '' COMMENT '订单orderid,自己生成',
    `inner_order_id` varchar(128) NOT NULL DEFAULT '' COMMENT '上游业务order_id',
    `user_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT '用户id',
    `amount` bigint NOT NULL DEFAULT 0 COMMENT '给用户添加的钱',
    `in_out` tinyint(1) unsigned NOT NULL DEFAULT 1 COMMENT '1 表示减T豆 2表示加T豆',
    `status` tinyint(1) unsigned NOT NULL DEFAULT 1 COMMENT '1 成功 2失败',
    `create_at` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间',
    `create_by` varchar(256) NOT NULL DEFAULT '' COMMENT '创建者',
    `update_at` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间',
    `update_by` varchar(256) NOT NULL DEFAULT '' COMMENT '更新者',
    `ext` varchar(256) NOT NULL DEFAULT '' COMMENT '扩展属性',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order` (`order_id`),
    UNIQUE KEY `uk_inner` (`inner_order_id`),
    KEY `idx_user` (`user_id`,`status`,`update_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT '上游业务表';

## 订单记录表
DROP TABLE IF EXISTS `inner_record`;
CREATE TABLE `inner_record` (
  `id` bigint(20) unsigned NOT NULL auto_increment,
  `user_id` bigint(10) unsigned NOT NULL default '0',
  `amount` bigint NOT NULL default '0' COMMENT '消耗金额',
  `left_amount` bigint NOT NULL default '0' COMMENT '剩余金额',
  `in_out` tinyint(1) unsigned NOT NULL DEFAULT 1 COMMENT '1 表示减T豆 2表示加T豆',
  `order_id` varchar(64) NOT NULL DEFAULT '' COMMENT '来源orderid',
  `order_type` int(10) unsigned NOT NULL default '0' COMMENT '订单类型',
  `create_at` int(10) unsigned NOT NULL default 0,
  `create_by` varchar(256) NOT NULL default '',
  `ext` varchar(256) NOT NULL default '' COMMENT '扩展信息',
  PRIMARY KEY  (`id`),
  UNIQUE KEY `uk_order` (`order_id`),
  KEY `idx_user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='内部订单表';

## 第三方订单表
DROP TABLE IF EXISTS `third_order`;
CREATE TABLE `third_order` (
  `id` bigint(20) unsigned NOT NULL auto_increment,
  `user_id` bigint(10) unsigned NOT NULL default 0 comment 'user_id',
  `order_id` varchar(64) NOT NULL default '' COMMENT '订单id',
  `amount` bigint NOT NULL default '0' COMMENT '消耗金额',
  `type` tinyint(1) unsigned NOT NULL default '0' COMMENT '订单类型 1:支付宝',
  `in_out` tinyint(1) unsigned NOT NULL DEFAULT 1 COMMENT '1 表示减T豆 2表示加T豆',
  `status` tinyint(2) unsigned NOT NULL DEFAULT 0 COMMENT '0 初始化 1审核通过 2审核不通过 3 提现中 4 提现成功 5提现失败',
  `third_order_id` varchar(512) NOT NULL default '' COMMENT '第三方orderid',
  `third_res` varchar(512) NOT NULL default '' COMMENT '第三方调用信息',
  `create_at` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间',
  `create_by` varchar(256) NOT NULL DEFAULT '' COMMENT '创建者',
  `update_at` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间',
  `update_by` varchar(256) NOT NULL DEFAULT '' COMMENT '更新者',
  `mis_reason` varchar(256) NOT NULL DEFAULT '' COMMENT 'mis审核信息',
   # mis 需要
  `third_id` varchar(100) NOT NULL default '' COMMENT '第三方id(银行卡号)',
  `id_card` varchar(50) NOT NULL default '' COMMENT '身份证号',
  `real_user_name` varchar(50) NOT NULL default '' COMMENT '真实姓名',
  `phone` varchar(50) NOT NULL default '' COMMENT '手机号',
  `to_bank_name` varchar(50) NOT NULL default '' COMMENT '银行名',
  `ext` varchar(256) NOT NULL default '',
  PRIMARY KEY  (`id`),
  UNIQUE KEY `idx_order` (`order_id`),
  UNIQUE KEY `idx_third_order` (`third_order_id`),
  KEY `idx_cond_ext` (`third_id`,`id_card`,`real_user_name`,`phone`,`to_bank_name`),
  KEY `idx_user` (`user_id`,`status`,`create_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='第三方订单表';

