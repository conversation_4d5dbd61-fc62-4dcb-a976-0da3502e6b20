<?php
//获取要提现的列表
$appIdList  = array(
    //100001,
    100002,
);
foreach($appIdList as $appId){
    $arrParam   = array(
        'app_id'    => $appId,
        'status'    => array(
            2,   //2 审核通过
            4,  //4提现中
        ),
    );
    $arrRet = Tieba_Service::call('cash','getThirdOrderList',$arrParam,null,null,'post','php','utf-8');
    if(false == $arrRet || 0 != $arrRet['errno']){
        echo "获取数据失败:output:[".serialize($arrRet)."]\n";
        exit(1);
    }
    $arrOrderList   = $arrRet['data'];
    foreach ($arrOrderList as $arrOrderInfo){
        //如果在提现中，查看订单的状态，如果提现成功了，则置状态为5，如果失败了，再看情况吧
        $appId  = $arrOrderInfo['app_id'];
        if($arrOrderInfo['status'] == 4 && $arrOrderInfo['order_id'] != $arrOrderInfo['third_order_id']){
            $arrParam   = array(
                'type'  => 2,
                'app_id'    => $appId,
                'order_no'  => $arrOrderInfo['order_id'],
            );
            $arrRet = Tieba_Service::call('cash','cashQuery',$arrParam,null,null,'post','php','utf-8');
            if(false == $arrRet || 0 != $arrRet['errno']){
                echo "查询【提现中】订单状态失败。input:[".serialize($arrParam)."].output:[".serialize($arrRet)."]\n";
            }
            $bfbPayStatus   = $arrRet['data']['state'];
            switch($bfbPayStatus){
            case 1:     //处理中
                break;
            case 2: //处理成功
                $arrParam   = array(
                    'order_id'  => $arrOrderInfo['order_id'],
                    'user_id'   => $arrOrderInfo['user_id'],
                    'status'    => 5,//处理成功
                );

                $arrRet = Tieba_Service::call('cash','editThirdOrder',$arrParam,null,null,'post','php','utf-8');
                if(false == $arrRet || 0 != $arrRet['errno']){
                    echo "更改订单状态失败:input:[".serialize($arrParam)."].output:[".serialize($arrRet)."]\n";
                    continue;
                }

                break;
            case 3: //处理失败  返现
                $arrParam =array(
                    'app_id'    => $appId,
                    'user_id'   => $arrOrderInfo['user_id'],
                    'inner_order_id'    => $arrOrderInfo['order_id'],
                    'third_status'  => 6,
                    'third_res' => $arrRet['data']['errmsg'],
                );
                $arrRet  = Tieba_Service::call('cash','backUserAmount',$arrParam,null,null,'post','php','utf-8');
                if(false == $arrRet || 0 != $arrRet['errno']){
                    echo "更改订单状态失败:input:[".serialize($arrParam)."].output:[".serialize($arrRet)."]\n";
                    continue;
                }
                break;
            }
        }else{
            // 1 修改目前的订单状态为提现中
            $arrParam   = array(
                'order_id'  => $arrOrderInfo['order_id'],
                'user_id'   => $arrOrderInfo['user_id'],
                'status'    => 4,//4 提现中
            );

            $arrRet = Tieba_Service::call('cash','editThirdOrder',$arrParam,null,null,'post','php','utf-8');
            if(false == $arrRet || 0 != $arrRet['errno']){
                echo "更改订单状态失败:input:[".serialize($arrParam)."].output:[".serialize($arrRet)."]\n";
                continue;
            }

            //2 调用提现api
            $arrParam   = array(
                'type'  => 2,
                'order_no'  => $arrOrderInfo['order_id'],
                'amount'    => $arrOrderInfo['amount'],
                'recv_name' => $arrOrderInfo['real_user_name'],
                'to_bank_name'  => $arrOrderInfo['to_bank_name'],
                'sub_branch'    => $arrOrderInfo['to_bank_name'],
                'bank_card_num' => $arrOrderInfo['third_id'],
                'app_id'    => $appId,
            );
            $arrRet = Tieba_Service::call('cash','cashTrans',$arrParam,null,null,'post','php','utf-8');

            $status = 0;
            if(false == $arrRet || 0 != $arrRet['errno']){
                echo "调用 提现api失败:input:[".serialize($arrParam)."].output:[".serialize($arrRet)."]\n";
                $status = 6;
                continue;
            }
            $third_order_id = $arrRet['data']['pay_no'];
            if(empty($third_order_id)){
                echo 'call bfb success,but not response.order id :['.$arrOrderInfo['order_id']."]\n";
                continue;
            }
            //更改订单状态
            $arrParam   = array(
                'user_id'   => $arrOrderInfo['user_id'],
                'order_id'  => $arrOrderInfo['order_id'],
            );
            !empty($third_order_id) && $arrParam['third_order_id'] = $third_order_id;
            !empty($status) && $arrParam['status']  = $status;
            $arrRet = Tieba_Service::call('cash','editThirdOrder',$arrParam,null,null,'post','php','utf-8');
            if(false == $arrRet || 0 != $arrRet['errno']){
                echo "更改订单状态失败:input:[".serialize($arrParam)."].output:[".serialize($arrRet)."]\n";
                continue;
            }
        }

    }

}
