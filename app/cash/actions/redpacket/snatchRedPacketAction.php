<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2018-11-13 23:35:17
 *5 @comment json接口，抢红包
 * @version
 */
class snatchRedPacketAction extends Molib_Client_BaseAction {
    private $_arrResult = array();
    private static $_arrWhiteList = array (
            838608334,
            3129272597,
            831268735 
    );
    private static $_arrBlackList = array(
        'EB76710CB2C8E25F661F0D0695C8D557' => 1,
    );
    const MAX_CHARGE_MONEY = 110;

    const REDIS_KEY_REDPACKET_USERS_SET = 'sub_user_ids_by_primary_redpacket_id_';

    /**
     *
     * @return array
     */
    protected function _getPrivateInfo() {
        $arrPrivateInfo = array();
        $arrPrivateInfo['check_login'] = true;
        $arrPrivateInfo['need_login'] = true;
        //$arrPrivateInfo['check_real_name']  = true;
        $arrPrivateInfo['ispv'] = 1;
        // tbs
        $arrPrivateInfo['check_tbs'] = false;
        // sign
        $this->_objRequest->addStrategy('check_sign', true);
        return $arrPrivateInfo;
    }
    
    /**
     *
     * @return bool
     */
    protected function _checkPrivate() {
        return true;
    }
    
    /**
     *
     * @return bool
     */
    protected function _execute() {
        $intUserId   = $this->_objRequest->getCommonAttr ( 'user_id', 0 );
        $strSubappType = $this->_objRequest->getCommonAttr ( "subapp_type", "tieba" );
        $intPrimaryRedpacketId = intval($this->_getInput('primary_redpacket_id', 0));
        $intAnchorId = intval($this->_getInput('anchor_id', 0));
        $intLiveId   = intval($this->_getInput('live_id', 0));

        if (false === $this->_checkAnti($intUserId)) {
            Bingo_Log::warning("isBlackUser $intUserId");
            return $this->_returnErr ( Service_Libs_Errno::ERR_NO_RECORD, Molib_Client_Error::getErrMsg ( Service_Libs_Errno::ERR_NO_RECORD ) );
        }

        // 抢过的人加进一个集合里, 用来直播间判断红包队列使用
        $arrRedisParams = array(
            'key'    => self::REDIS_KEY_REDPACKET_USERS_SET . $intPrimaryRedpacketId,
            'member' => array(
                $intUserId,
            ),
        );
        $arrRet = Util_Redis::saddToRedis($arrRedisParams);
        if ($arrRet === false) {
            Bingo_Log::warning('SADD fail' . serialize($arrRedisParams));
        }

        $arrParams = array (
            'user_id'              => $intUserId,
            'primary_redpacket_id' => $intPrimaryRedpacketId,
            'anchor_id'            => $intAnchorId,
            'live_id'              => $intLiveId,
        );
        $arrOutput = Tieba_Service::call ( 'cash', 'snatchRedPacket', $arrParams, null, null, 'post', 'php', 'utf-8', 'local');
        if ($arrOutput == false || $arrOutput ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ( "call snatchRedPacket fail. output: " . serialize($arrOutput) . ' input:' .serialize($arrParams));
            if ($arrOutput ['errno'] == Service_Libs_Errno::ERR_NO_RECORD) {
                //出现神奇的问题，红包不存在了
                return $this->_returnErr ( Service_Libs_Errno::ERR_NO_RECORD, Molib_Client_Error::getErrMsg ( Service_Libs_Errno::ERR_NO_RECORD ) );
            }
            if ($arrOutput ['errno'] == Service_Libs_Errno::ERR_LOTTERY_AWARD_NOT_EXIST) {
                //出现神奇的问题，红包没开始
                return $this->_returnErr ( Service_Libs_Errno::ERR_LOTTERY_AWARD_NOT_EXIST, Molib_Client_Error::getErrMsg ( Service_Libs_Errno::ERR_LOTTERY_AWARD_NOT_EXIST ) );
            }
            //return $this->_returnErr ( Tieba_Errcode::ERR_CALL_SERVICE_FAIL, Molib_Client_Error::getErrMsg ( Tieba_Errcode::ERR_CALL_SERVICE_FAIL ) );
        }
        
        $data = $arrOutput ['data'];
        $this->_buildResponse($data, $intPrimaryRedpacketId);
        if ($arrOutput ['errno'] == Service_Libs_Errno::ERR_IS_EXPIRED) {
            return $this->_returnErr ( Service_Libs_Errno::ERR_IS_EXPIRED, Molib_Client_Error::getErrMsg ( Service_Libs_Errno::ERR_IS_EXPIRED ), $this->_arrResult );
        } else{
            $this->_returnErr ( Tieba_Errcode::ERR_SUCCESS, Molib_Client_Error::getErrMsg ( Tieba_Errcode::ERR_SUCCESS ), $this->_arrResult );
        }
        return true;
    }

    /**
     *
     * @param array $arrRet         
     * @return bool
     */
    private function _checkAnti($intUserId) {
        $arrOutput = Tieba_Service::call ( 'tbmall', 'isBlackUser', array('user_id' => $intUserId), null, null, 'post', 'php', 'utf-8');
        if ($arrOutput == false || $arrOutput ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ( "call tbmall isBlackUser fail. output: " . serialize($arrOutput) . ' input:' . $intUserId);
            return true;
        }
        if (1 == intval($arrOutput['data']['is_black_user'])) {
            return false;
        }
        return true;
    }
    /**
     *
     * @param array $arrRet         
     * @return bool
     */
    private function _buildResponse($data = array(), $intPrimaryRedpacketId) {
        $intClientType    = $this->_objRequest->getCommonAttr ( 'client_type', 0 );
        if (!empty($data['redpacket_image'])) {
            $arrImage = json_decode($data['redpacket_image'],1);
            if ($intClientType == 1) {
                $strImage = strval($arrImage['ios']);
            } else {
                $strImage = strval($arrImage['android']);
            }
        } 
        if (empty($strImage)) {
            $strImage = Tieba_Ucrypt::encode(intval($data['send_user_id']), strval($data['send_user_name']));
        }
        
        $this->_arrResult = array(
            'data' => array(
                'is_got' => intval($data['is_got']),
                'primary_redpacket_id' => $intPrimaryRedpacketId,
                'sub_redpacket_id' => intval($data['sub_redpacket_id']),
                'send_user_name' => strval($data['send_user_name']),
                'send_user_id'   => intval($data['send_user_id']),
                'send_portrait'  => $strImage,
                'total_num' => intval($data['total_num']),
                'get_num'   => intval($data['get_num']),
                'get_user_id' => intval($data['get_user_id']),
                'get_user_name' => strval($data['get_user_name']),
                'total_money' => intval($data['total_money']),
                'get_money' => intval($data['get_money']),
                'sub_money' => intval($data['sub_money']),
                'sub_redpacket_list' => isset($data['sub_redpacket_list']) ? $data['sub_redpacket_list'] : array(),
            ),
        );
        return ;
    }
    
    /**
     *
     * @param
     *          $intErrno
     * @param
     *          $strErrmsg
     * @param array $arrRet         
     * @return bool
     */
    private function _returnErr($intErrno, $strErrmsg, $arrRet = array()) {
        $this->_error ( $intErrno, $strErrmsg );
        $this->_objResponse->setOutData ( $arrRet );
        return false;
    }
    
    /**
     *
     * @return array
     */
    protected function _getUserInfo() {
        if (! empty ( $this->_arrUserInfo )) {
            return $this->_arrUserInfo;
        }
        
        $bolLogin = ( boolean ) Tieba_Session_Socket::isLogin ();
        $intUserId = intval ( Tieba_Session_Socket::getLoginUid () );
        $strUserName = strval ( Tieba_Session_Socket::getLoginUname () );
        $intUserIp = Bingo_Http_Ip::newip2long ( Bingo_Http_Ip::getConnectIp () ) ;
        $bolNoUname = ( boolean ) Tieba_Session_Socket::getNo_un ();
        $strMobile = strval ( Tieba_Session_Socket::getMobilephone () );
        $strEmail = strval ( Tieba_Session_Socket::getEmail () );
        
        $arrUserSInfo = array ();
        if ($intUserId > 0) {
            $arrUserSInfo = Libs_Util_User::getUserData ( $intUserId );
            if ($arrUserSInfo === false) {
                $arrUserSInfo = array ();
            }
        }
        
        $arrUserSInfo ['is_login'] = $bolLogin;
        $arrUserSInfo ['user_id'] = $intUserId;
        $arrUserSInfo ['user_name_utf8'] = $arrUserSInfo ['user_name'];
        $arrUserSInfo ['portrait'] = Tieba_Ucrypt::encode ( $intUserId, $strUserName );
        $arrUserSInfo ['user_name'] = $strUserName;
        $arrUserSInfo ['user_ip'] = $intUserIp;
        $arrUserSInfo ['is_noname'] = $bolNoUname;
        $arrUserSInfo ['mobile'] = Tieba_Util::maskPhone ( $strMobile );
        $arrUserSInfo ['email'] = Tieba_Util::maskEmail ( $strEmail );
        $this->_arrUserInfo = $arrUserSInfo;
        return $this->_arrUserInfo;
    }
    
}