<?php
$self_path = dirname(__FILE__);
require_once "$self_path/../../../frsbase/libs/BaseHandle.class.php";

/*
 example�����޸�Ϊ��Ҫ������
*/

class example extends BaseHandle
{
	private static $_input;
	private static $_class_name = 'empty';
	private static $_instance = null;

/* get_instance() __construct() __clone()
** ������������Ϊ����ģʽ����ֹ���������
*/
	public static function get_instance()
	{
		if ( self::$_instance === null || self::$_instance === false )
		{
			$c = __CLASS__;
			self::$_instance = new $c;
		}
		return self::$_instance;
	}

	private function __construct() 
	{
	}

	public function __clone()
    {
        trigger_error("Clone is not allowed.", E_USER_ERROR);
    }

/*
** ��ʼ�������
*/
	public function init ($inputparam = array())
	{
		self::$_input = $inputparam;
		return true;
	}

/* check_param()
** �������ǰ��ִ�������Ƿ����㣬��Ҫ�Ǽ��init����Ĳ������Ƿ��������ִ������Ҫ�ı�ѡ����
*/
	public function check_param()
	{
		return true;
	}

/* need_cache()
** ���ش��ദ��������Ƿ���Ҫ���оۺ�cache��������������ķ����������Ƿ���оۺ�cache
*/
	public function need_cache()
	{
		return false;
	}


/* get_cache_param()
**  ���ش��ദ��ľۺ����ݵ�key���Լ�cacheʧЧʱ�䣬������Դ�key������cache��ȡ�����
**  �������ݸ�ʽ:
**  array
**	(
**		'key' => xxxx,       // cache��key,һ��Ϊ ������fid�ľۺ�
**      'timeout' => yyyy,   // cacheʧЧʱ�䣬�������Ϊ�գ�����Ĭ��ʹ��30s��ΪcacheʧЧʱ��    
**	)
*/
	public function get_cache_param()
	{
		self::$_class_name = get_class($this);
		$key_suf = 123;     //�����123�滻Ϊʵ�ʵ�key��һ��Ϊfid
		$key = self::$_class_name.$key_suf;
		return array(
						'key'=> $key,
						'timeout'=>50, //�����50�����滻Ϊʵ����Ҫ��ʧЧʱ��
					);
	}

/* get_value()
**	get_value����Ҫ�����Ǵ� init()����Ĳ����У�������������ʵ����������������sidl
**  ���������ת����ۺϺ󣬷������ε����ߣ�  get_value����Ҫʵ�ֵ�����Ҫ�ĺ���
*/
	public function get_value()
	{
		return array('aaaa'=>1,);
	}

/* finish()
** ������������һ������£����챾�ദ���������Ҫ��������־��array
*/
	public function finish()
	{
		return array();
	}
};

/* ����ʵ��
class test
{
	public static function execute()
	{
		$tmp = example::get_instance();
		$inputparam = array('aaa'=>1,'bbb'=>2,);
		$result = $tmp -> execute($inputparam);
	}

};
*/
?>
