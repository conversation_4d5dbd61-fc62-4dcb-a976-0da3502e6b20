<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR> <EMAIL>
 * @date 2011-12-16
 * @version 
 */
class Sapi_Errno{
    const PRODUCT_ACTS_CTRL_ERRNO = 18001; //产品线请求粒度控制过快
    const INVALID_OPEN_ID = 18002; //非法的产品线
    const CALL_RPC_ERROR = 18003; //请求后端数据错误
    const RETURN_DATA_IS_EMPTY = 18004;//请求返回的数据为空
    const INVALID_REQUEST = 18005; //非法请求
    const PARAM_IS_NOT_ENOUGH = 18006; //参数不全
    
}