<?php
/**
 *
 * @file Beyond.php
 * <AUTHOR>
 * @date 2016/01/19
 * brief: 标识是否是娱乐频道吧目录
 **/
class Beyond implements Tieba_Hello_ParallelAbstract{
    
    const BEYOND_CHANNEL_DATA_KEY = "beyond_channel_data_frs_pb_ui";  // 全部频道信息cache key
    const DEFAULT_EXPIRE = 60; // cache时间
    
    const USER_TYPE_M = 0; // M用户
    const USER_TYPE_T = 1; // T用户
    
    private static $_cache = null;
    
    private static $_arrChannelInfo = null;
    
    /**
	 * @param Tieba_Hello_Param $objParam
	 *
	 * @return mixed 
	 * */
	public static function preExecute(Tieba_Hello_Param $objParam){
        // 获取垂类频道全集（包括吧目录和类目信息）
        self::$_arrChannelInfo = self::_getCacheChannelData();
        if (false === self::$_arrChannelInfo) {
            $arrMultiInput['call'][] = array(
				'service_name'  => 'beyond',
				'method'        => 'getChannelListForThreadPush',
				'input'         => array(),
				'ie'            => 'utf-8',
			);
            self::$_arrChannelInfo = null;
        } else {
            $arrMultiInput['call'][] = array();
        }
		return $arrMultiInput;
	}

    /**
     * @brief 
     * @return 
     */
	public static function execute(Tieba_Hello_Param $objParam, array $arrMultiOut){
        if (is_null(self::$_arrChannelInfo)) {
            $arrRes = $arrMultiOut[0];
            if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
                Bingo_Log::warning("call service beyond/getChannelListForThreadPush failed. output:" . serialize($arrRes));
                $arrChannelInfo = array();
            } else {
                $arrChannelInfo = $arrRes['data'];
                self::_addCacheChannelData($arrChannelInfo);
            }
        } else {
            $arrChannelInfo = self::$_arrChannelInfo;
        }
        $arrBuildRet = self::_buildResult($objParam, $arrChannelInfo);
		$arrOut['beyond']= array('tpl_var' => $arrBuildRet);
        return $arrOut;	
	}
    
    private static function _buildResult(Tieba_Hello_Param $objParam, array $arrChannelInfo) {
        $arrRet = array(
            'thread_can_be_pushed' => false,
            'thread_has_been_pushed' => false,
        );
        //验证帖子是否已被推送
        $hasPushed = $objParam->getPostParam('has_pushed');
        if (1 == $hasPushed) {
            $arrRet['thread_has_been_pushed'] = true;
        }
        if (count($arrChannelInfo) == 0) {
            return $arrRet;
        }
        $arrRet['base']['channel_info'] = $arrChannelInfo;
        //验证娱乐频道目录
		$strLever1Name = $objParam->getForumParam('level_1_name');
        $strLever2Name = $objParam->getForumParam('level_2_name');
        $intForumId = $objParam->getForumParam('forum_id');

        $intChannelId = -1;
        foreach ($arrChannelInfo as $value) {
            foreach ($value['forum_dir'] as $key => $elem) {
                $strForumDir1 = (string)$elem['level_1_name'];
                if ($elem['type'] == 1 && in_array($intForumId, $elem['forum_id'])){//tyep==1为游戏单吧吧作为频道，add by jiangshuai
                    $intChannelId = (int)$value['id'];                  
                    break;
                } elseif ($elem['type'] == 0 && $strLever1Name == $strForumDir1 && (in_array($strLever2Name, $elem['level_2_name']) || in_array("", $elem['level_2_name']))) {
                    $intChannelId = (int)$value['id'];                  
                    break;
                }                
            }
            if (-1 != $intChannelId) {
                break;
            }
        }
        
        // 频道id
        $arrRet['base']['channel_id'] = $intChannelId;
        
        // 类目信息
        if (-1 != $intChannelId) {            
            foreach ($arrChannelInfo as $value) {
                if ($intChannelId == (int)$value['id']) {                    
                    $arrRet['channel_info'] = array(
                        'id' => (int)$value['id'],
                        'name' => (string)$value['name'],
                        'category' => array(),
                    );
                    foreach ($value['category'] as $val) {
                        $arrRet['channel_info']['category'][] = array(
                            'id' => (int)$val['id'],
                            'name' => (string)$val['name'],
                        );
                    }
                    break;
                }
            } 
            // 验证是否是M用户自己发的帖子 or T用户
            $intLoginUid = $objParam->getUserParam('user_id');
            $intThreadUid = $objParam->getPostParam('lz_uid');
            $arrBeyondUserInfo = $objParam->getUserParam('beyond_user_info');
            if (isset($arrBeyondUserInfo[$intChannelId])) {
                if ($intLoginUid == $intThreadUid || self::USER_TYPE_T == $arrBeyondUserInfo[$intChannelId]) {
                    $arrRet['thread_can_be_pushed'] = true;
                }
            }
        } else {
            $arrRet['channel_info'] = array();
        }
        return $arrRet;
    }
	
    /**
     * @brief 
     * @return 
     */
	public static function getCacheKey(Tieba_Hello_Param $objParam){
        return false;
	}
    
    /**
     * @brief 
     * @return 
     */
    private static function _getCacheChannelData(){
        if(!self::$_cache){
            Bingo_Timer::start('beyond_channel_memcached_init');
            self::$_cache = new Bingo_Cache_Conver("forum_official");
            Bingo_Timer::end('beyond_channel_memcached_init');
        }
        if(!self::$_cache){
            Bingo_Log::warning(self::MEMCACHED_NAME." init cache fail.");
            self::$_cache = null;
            return false;
        }
        $cacheKey = self::BEYOND_CHANNEL_DATA_KEY;
        Bingo_Timer::start('beyond_channel_memcached_get');
        $res = self::$_cache->get($cacheKey);
        Bingo_Timer::end('beyond_channel_memcached_get');
        if (null != $res && false != $res) {
            $data = unserialize($res);
            if(!is_array($data)){
                return false;
            }
            return $data;
        }
        return false;
    }
    
    /**
     * @brief 
     * @return 
     */
    private static function _addCacheChannelData($arrChannelData){
        if(!self::$_cache){
            Bingo_Timer::start('beyond_channel_memcached_init');
            self::$_cache = new Bingo_Cache_Conver("forum_official");
            Bingo_Timer::end('beyond_channel_memcached_init');
        }
        if(!self::$_cache){
            Bingo_Log::warning(self::MEMCACHED_NAME." init cache fail.");
            self::$_cache = null;
            return false;
        }
        $cacheKey = self::BEYOND_CHANNEL_DATA_KEY;
        $mixValue = serialize($arrChannelData);
        Bingo_Timer::start('beyond_channel_memcached_add');
        $ret = self::$_cache->add(strval($cacheKey), $mixValue, self::DEFAULT_EXPIRE );
        Bingo_Timer::end('beyond_channel_memcached_add');
        if ($ret == 0) {
            return true;
        } else {
            Bingo_Log::warning ( 'add cache err no : ' . $ret. " strKey:$cacheKey, value:$mixValue" );
            return false;
        }
    }
}
