<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Brandzone.php
 * <AUTHOR>
 * @date 2014/03/03 19:31:28
 * @brief ecom_asp���
 *  
 **/

class Brandzone implements Tieba_Hello_SerializeAbstract{

    const PAGE_TYPE = 'pb'; 
    const THREAD_TYPE_BRANDZONE	= 999999; //�����  
    const TERMINAM_TYPE = 1;
    const CUPID_BRANDZONE       = 201;
	
	public static function execute(Tie<PERSON>_Hello_Param $objParam){
		$intThreadType = $objParam->getPostParam('thread_type');
	    if($intThreadType == self::THREAD_TYPE_BRANDZONE){
            $inputEcom = self::getInputEcom($objParam);
            if(false != $inputEcom){
                $outEcom = Tbapi_Frs_Midl_Brandzone::query($inputEcom);
                if(false != $outEcom && $outEcom['res_type'] === 2 && isset($outEcom['tid_res_list']) && !empty($outEcom['tid_res_list'])){
                    $status = $outEcom['tid_res_list'][0]['status'];
                    $showMt = (2 == $status) ? $outEcom['tid_res_list'][0]['show_mt'] : '';
                    $isAd  = (2 == $status) ? 1 : 0;
                    $nickName = $outEcom['tid_res_list'][0]['nick_name'];
                    $arrExpIdList = $outEcom['exp_id_list'];
                    $exp = self::encodeExp($arrExpIdList);
		            $firstUsr = $objParam->getPostParam('first_uname');
		            if (empty($firstUsr)) {
		            	$firstUsr = $objParam->getPostParam('lz_uname');
                    }
                }
            }
        }
        if($isAd == 1){
        	 $arrRet['brandzone']['log_var']['is_ad'] = $isAd;
        }
        $arrRet['brandzone']['tpl_var'] = array(
        	'is_ad' => $isAd,
        	'content' => $showMt,
        	'first_name' => $firstUsr,
        	'nick_name' => $nickName,
        	'ecom' => $exp,
        );
        return $arrRet;
	}

	protected static function getInputEcom($objParam){
        $strFname = $objParam->getForumParam('forum_name');
        $arrCookie = $objParam->getContextParam('cookie');
        $bduid = isset ($arrCookie['BAIDUID']) ? substr ($arrCookie['BAIDUID'],0,32) : '';
        $intUserIp = $objParam->getUserParam('user_ip');
        $logid = REQUEST_ID;

        $ret = $objParam->checkCupid(self::CUPID_BRANDZONE) ? 1 : 0;
        if(0 == intval($ret)){
            return false;
        }

        if(!isset($strFname) || $strFname ===''){
            Bingo_Log :: warning ('forumName empty! Call ecom failure! logid :' . $logid);
            return false;
        }
		$intTid = $objParam->getPostParam('thread_id');

        if(empty($intTid)){
            Bingo_Log :: warning ('tid empty! Call ecom failure! forumName :' . $strFname);
            return false;
        }

        $inputEcom = array(
            'req_type' => 2,
            'terminal_type' => self::TERMINAM_TYPE,
            'logid' => (isset($logid)? $logid : 0),
            'baiduid' => (isset($bduid)? $bduid : ''),
            'user_ip' => (isset($intUserIp)? $intUserIp : 0),
            'forum_name' => $strFname,
            'pin_num' => 0,
            'start_no' => 0,
            'end_no' => 0,
            'thread_num' => 0,
            'reply_num' => 0,
            'up_num' => 0,
            'tid_list' => array($intTid),
        );
        return $inputEcom;
    }

    public static function encodeExp($exp_id_list)
    {
        $retValue = '';
        if(!empty($exp_id_list)){
            foreach($exp_id_list as $exp)
            {
                $retValue = $retValue . strval($exp) . '|';
            }
        }
        if(!empty($retValue)){
            $retValue = substr($retValue,0 ,-1);
        }
        return $retValue;
    }
	
	public static function getCacheKey(Tieba_Hello_Param $objParam){
    	return false;
    }   
    
    
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
