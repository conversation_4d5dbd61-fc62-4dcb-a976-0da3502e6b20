<?php
/**
 * Created by PhpStorm.
 * User: shiyibo
 * Date: 14-11-25
 * Time: 下午7:53
 */
class Travel implements Tieba_Hello_ParallelAbstract{
    const TABLE_NAME             = 'tb_wordlist_redis_zhongchou';  // 控制词表
    const LEVEL1_NAME            = '娱乐明星';                      // 只有娱乐明星目录下才执行
	
    static protected  $_conf     = null;                           // 配置文件
    static protected  $_intSwitchOn   = false;
	static protected  $_arrCrowdInfo  = array();

    private static $retCache = null;
    private static $str_cache_key = '';

    /**
     * @desc 准备并行调用的入参的哦
     * @param
     * @return
     */
    public static function preExecute(Tieba_Hello_Param $objParam){
        $strLevel1Name = $objParam->getForumParam('level_1_name');
        $intFid        = $objParam->getForumParam('forum_id');
        $arrStyles = $objParam->getForumParam('attrs');
        // 非娱乐明星目录下的吧，直接返回
        if (strcmp($strLevel1Name,self::LEVEL1_NAME)) {
            return array();
        }

        if ( !isset($arrStyles['star_travel']) || false === $arrStyles['star_travel']['with_travel']) {
			return array();
		}

        $arrMultiInput['call'][] = array(
            'service_name' => 'pluto',
            'method'       => 'getTravelInfo',
            'input'        => array(
                'forum_id' => $intFid,
            ),
            'ie'           => 'utf-8',
        );
        return $arrMultiInput;
    }

    /**
     * @desc 准备并行调用
     * @param
     * @return
     */
    public static function execute(Tieba_Hello_Param $objParam, array $arrMultiOut){
        self::$_arrCrowdInfo = array();
        $arrResult = $arrMultiOut[0];
        if (false === $arrResult || Tieba_Errcode::ERR_SUCCESS !== $arrResult['errno']) {
            $strMsg = sprintf('call pluto::getTravelInfo fail');
            Bingo_Log::warning($strMs);
        } else {
            self::$_arrCrowdInfo = $arrResult['data'];
        }

        // 不展现的话 直接返回
        if (false === self::showTravel())  {
            return array();
        }

        $arrOutput['traveling'] = array('tpl_var'  => self::$_arrCrowdInfo);
        return $arrOutput;
    }

    /**
     * @desc 判断当前的巡游是否可以展示
     * @param
     * @return
     */
    public static function showTravel() {
        $intStartTime    = (int)self::$_arrCrowdInfo['cdinfo']['start_time'];
        $intEndTime      = (int)self::$_arrCrowdInfo['cdinfo']['end_time'];
        $intTravelTime   = (int)self::$_arrCrowdInfo['cdinfo']['travel_time'];
        $intTargetOilNum = (int)self::$_arrCrowdInfo['cdinfo']['oil_num'];
        $intCurOilNum    = (int)self::$_arrCrowdInfo['cdinfo']['cur_oil_num'];
        $intStatus       = (int)self::$_arrCrowdInfo['cdinfo']['status'];
        $intBusNum       = (int)(self::$_arrCrowdInfo['cdinfo']['bus_num']);
        $intNow          = time();

        // 活动还没到巡游时间
        if ($intNow < $intTravelTime) {
            Tieba_Stlog::addNode('empty', 'not_start');
            return false;
        }
        
        // 到这里，只能是巡游阶段，巡游阶段 应援失败
        if ($intCurOilNum < $intTargetOilNum) {
            Tieba_Stlog::addNode('empty', 'crowdfunding_fail');
            return false; 
        }

        // 是否是在当前吧 
        $intStartTime = (int)(self::$_arrCrowdInfo['travel_info']['start_time']);
        $intEndTime   = (int)(self::$_arrCrowdInfo['travel_info']['end_time']);
        if ($intNow < $intStartTime || $intNow >= $intEndTime) {
            Tieba_Stlog::addNode('empty', 'not_me_now');
            return false;
        }

        // 巡游世界结束
        $intNumPerBus    = (int)(self::$_arrCrowdInfo['category']['travel_num']);
        $intTimePerForum = (int)(self::$_arrCrowdInfo['category']['travel_time']);
        $intEndTime      = $intTravelTime + $intBusNum * $intNumPerBus * $intTimePerForum;
        if ($intNow >= $intEndTime) {
            Tieba_Stlog::addNode('empty', 'cd_over');
            return false;
        }
        
        return true;
    }

    /**
     * @desc 
     * @param
     * @return
     */
    public static function getCacheKey(Tieba_Hello_Param $objParam){
        return false;
    }

    /**
     * @desc 
     * @param
     * @return
     */
    private static function _getPrivateCacheKey($arrInput){
        return false;
    }
}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
