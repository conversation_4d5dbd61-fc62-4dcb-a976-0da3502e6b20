<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-09-08
 * @version 
 * @copy by tangyan
 * @�û����������˵���Ϣ
 */
class Lib_Tbuser {
    /**
     * @param bduss
     * @return array   user info
     * */
    public static function getUserByBduss($strBduss) {
        if (empty($strBduss)){
            Bingo_log::warning('Lib_Service_User getUserByBduss but no bduss provide,may be a new client');
            return false;
        }
        $strBduss = explode ("|", $strBduss);
        $_COOKIE['BDUSS'] = strval($strBduss[0]);
        Bingo_Timer::start('pass_session');
        $bolIsLogin = Tieba_Session_Socket::isLogin();
        Bingo_Timer::end('pass_session');

        if ($bolIsLogin) {
            $intUid = intval(Tieba_Session_Socket::getLoginUid());
            $strUname = strval(Tieba_Session_Socket::getLoginUname());
            $strUname = Bingo_Encode::convert($strUname, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
            return array(
                'user_id' => $intUid,
                'user_name' => $strUname,
            );
        } else {
            Bingo_log::warning('Lib_Service_User getUserByBduss but bduss not login:'.strval($_COOKIE['BDUSS']));
            return false;
        }
    }

    /**
     * @param bduss
     * @return array   user info
     * */
    public static function mgetUserInfo($arrInput){
        $arrUserSet['user_id'] = $arrInput;
        Bingo_Timer::start('service_user');
        $res = Lib_Util_Service::call('user', 'mgetUserData', $arrUserSet,'remote');
        Bingo_Timer::end('service_user');
        if($res === false){
            Bingo_log::warning("talk with service mgetUserData error! input[" . serialize($arrInput) . "]");
            $res['errno']  = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            $res['errmsg'] = "talk with service mgetUserData error!";
            return $res;   
        }
        return $res;
    }

    /**
     * @param bduss
     * @return array   user info
     * */
    public static function batchGetUserInfo($arrInput){
        $MAX_REQ_NUM = 50;
        $output = array(
            'errno'     => Tieba_Errcode::ERR_SUCCESS,
            'user_info' => array(),
        );
        while( count($arrInput) > 0 ){
            $timeInput = array_splice($arrInput,0,$MAX_REQ_NUM);
            $tmpInput = array('user_id' => $timeInput );
            $tmpOnput = Lib_Util_Service::call('user', 'mgetUserData', $tmpInput);
            if($tmpOnput['errno'] != Tieba_Errcode::ERR_SUCCESS || !isset($tmpOnput['user_info'])){
                Bingo_Log::warning('get mgetUserData faile!'.serialize($tmpOnput)."|".serialize($tmpInput));
                continue;
            }
            $output['user_info'] = $output['user_info'] + $tmpOnput['user_info'];
            unset($timeInput);
        }
        return $output;
    }
    /**
     * @param bduss
     * @return array   user info
     * */
    public static function getUserNameByUid($user_id) {
        $username = '';
        $arrParam = array(
            'user_id' => array($user_id),
        );      

        $ret = Tieba_Service::call('user', 'getUnameByUids', $arrParam);

        if ($ret['errno'] === 0 && isset($ret['output'])){
            $arrOut = $ret['output'];
            if (isset($arrOut['unames']) && is_array($arrOut['unames'])){
                $username = $arrOut['unames'][0]['user_name'];  
            }       
        } else {
            Bingo_Log::warning("call user::getUnameByUids false. [".serialize($arrParam)."]");
        } 

        return $username;
    }

    /**
     * ����ͨ��uid��ȡuname
     * @param array $arrUids
     * @param string $ie
     * @return boolean | array {uid => uname}
     */
    public static function getUnameByUids($arrUids, $ie = 'utf-8'){
        $arrInput = array(
            'user_id' => $arrUids,
        );
        $ret = Tieba_Service::call('user', 'getUnameByUids', $arrInput, null, null, 'post', 'php', $ie);
        if ($ret === false || (isset($ret['errno']) && intval($ret['errno']) !== 0)) {
            Bingo_Log::warning(__FUNCTION__." call service failed : getUnameByUids, input =" . serialize($arrInput) . " output =" . serialize($ret));
            return false;
        }
        $userNames = array();
        foreach ($ret['output']['unames'] as $uname){
            $userNames[intval($uname['user_id'])] = $uname['user_name'];
        }
        return $userNames;
    }
}
