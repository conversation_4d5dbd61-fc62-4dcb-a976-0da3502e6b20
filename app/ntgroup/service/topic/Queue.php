<?php

/**
 * @file Queue.php
 * <AUTHOR>
 * @date 2016/07/19 10:30:50
 * @brief
 *
 **/
class Service_Topic_Queue extends Service_Libs_Base
{
    //const REDIS_NAME = 'nt_group';
    const REDIS_NAME = 'tbnt';
    const TOPIC_START_TIME_PREFIX = 'topic_start_time_';
    const LAST_TOPIC_PREFIX = 'last_topic_';

    /**
     * @brief  获取用户发的话题在队列中的位置
     * @param  $arrInput
     * @return integer
     */
    public static function getUserCommitPosition($arrInput)
    {
        $intUserId  = intval($arrInput['user_id']);
        $intGroupId = intval($arrInput['group_id']);

        // 获取队列中的所有内容
        $strKey = 'topic_queue_' . $intGroupId;
        $arrRedisInput = array(
            'key'   => $strKey,
            'start' => 0,
            'stop'  => -1,
        );
        $arrRet = Service_Topic_Redis::redisQuery(self::REDIS_NAME, 'LRANGE', $arrRedisInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("get all the commit queue failed. [input = %s] [output = %s]"));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        $arrUserCommitTopics = $arrRet['ret'][$strKey];

        // 遍历该队列找该用户发送的话题内容
        $intIndex = 0;
        foreach ($arrUserCommitTopics as $strTopicInfo) {
            ++$intIndex;
            $arrTopicInfo = explode('_', $strTopicInfo);
            if ($intUserId === intval($arrTopicInfo[2])) {
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $intIndex);
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, 0);
    }

    /**
     * @brief  将用户发的话题加入到队列
     * @param  $arrInput
     * @return integer
     */
    public static function pushTopicToQueueTail($arrInput)
    {
        $intUserId   = intval($arrInput['user_id']);
        $intGroupId  = intval($arrInput['group_id']);
        $intGTopicId = intval($arrInput['gtopic_id']);
        $intDuration = intval($arrInput['duration']);
        $strKey = "topic_queue_{$intGroupId}";
        $arrRedisInput = array(
            'key'   => $strKey,
            'value' => "{$intGTopicId}_{$intDuration}_{$intUserId}",
        );
        $arrRet = Service_Topic_Redis::redisQuery(self::REDIS_NAME, 'RPUSH', $arrRedisInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("push the topic to the queue failed.[input=%s] [output=%s]",
                serialize($arrRedisInput), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DIFANG_CALL_REDIS_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['ret'][$strKey]);
    }

    /**
     * @brief  将用户发的话题插入到队首
     * @param  $arrInput
     * @return integer
     */
    public static function insertTopicToQueueHead($arrInput)
    {
        $intUserId   = intval($arrInput['user_id']);
        $intGroupId  = intval($arrInput['group_id']);
        $intGTopicId = intval($arrInput['gtopic_id']);
        $intDuration = intval($arrInput['duration']);
        $strKey = "topic_queue_{$intGroupId}";
        $arrRedisInput = array(
            'key'   => $strKey,
            'value' => "{$intGTopicId}_{$intDuration}_{$intUserId}",
        );
        $arrRet = Service_Topic_Redis::redisQuery(self::REDIS_NAME, 'LPUSH', $arrRedisInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("push the topic to the queue failed.[input=%s] [output=%s]",
                serialize($arrRedisInput), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DIFANG_CALL_REDIS_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief  获取话题队列的头部
     * @param  $arrInput
     * @return mixed
     */
    public static function getTopicQueueHead($arrInput)
    {
        // 参数检查
        $intGroupId = intval($arrInput['group_id']);
        if (empty($intGroupId)) {
            Bingo_Log::warning(sprintf("param error. missing group_id",
                serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 获取队首元素
        $strKey = "topic_queue_{$intGroupId}";
        $arrRedisInput = array(
            'key'   => $strKey,
            'index' => 0,
        );
        $arrRet = Service_Topic_Redis::redisQuery(self::REDIS_NAME, 'LINDEX', $arrRedisInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("get the head of the topic queue failed. [input = %s] [output = %s]",
                serialize($arrRedisInput), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        if (empty($arrRet['ret'][$strKey])) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }
        else {
            $arrTopicInfo = explode('_', $arrRet['ret'][$strKey]);
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrTopicInfo);
        }
    }

    /**
     * @brief  从topic队列中pop出话题
     * @param  $arrInput
     * @return array
     */
    public static function popTopicFromQueue($arrInput) {
        // 参数检查
        $intGroupId = intval($arrInput['group_id']);
        if (empty($intGroupId)) {
            Bingo_Log::warning(sprintf("param error. missing user_id or group_id",
                serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // Pop出队首元素
        $strKey = "topic_queue_{$intGroupId}";
        $arrRedisInput = array(
            'key' => $strKey,
        );
        $arrRet = Service_Topic_Redis::redisQuery(self::REDIS_NAME, 'LPOP', $arrRedisInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("pop the head of the topic queue failed. [input = %s] [output = %s]",
                serialize($arrRedisInput), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        $arrTopicInfo = explode('_', $arrRet['ret'][$strKey]);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrTopicInfo);
    }

    /**
     * @brief   getTopicQueue
     * @param   $arrInput
     * @return  array
     */
    public static function getTopicQueue($arrInput) {
        $intGroupId = intval($arrInput['group_id']);
        if (empty($intGroupId)) {
            Bingo_Log::warning(sprintf("param error. missing user_id or group_id。 [input = %s]",
                serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 获取队列中的所有内容
        $strKey = 'topic_queue_' . $intGroupId;
        $arrRedisInput = array(
            'key'   => $strKey,
            'start' => 0,
            'stop'  => -1,
        );
        $arrRet = Service_Topic_Redis::redisQuery(self::REDIS_NAME, 'LRANGE', $arrRedisInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("get all the commit queue failed. [input = %s] [output = %s]"));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        $arrUserCommitTopics = $arrRet['ret'][$strKey];
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrUserCommitTopics);
    }

    /**
     * @brief   获取群话题队列长度
     * @param   $arrInput
     * @return  array
     */
    public static function getGroupTopicQueueLength($arrInput) {
        $intGroupId = intval($arrInput['group_id']);
        if (empty($intGroupId)) {
            Bingo_Log::warning(sprintf("param error. missing user_id or group_id。 [input = %s]",
                serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 获取队列中的所有内容
        $strKey = 'topic_queue_' . $intGroupId;
        $arrRedisInput = array(
            'key'   => $strKey,
        );
        $arrRet = Service_Topic_Redis::redisQuery(self::REDIS_NAME, 'LLEN', $arrRedisInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("get all the commit queue length failed. [input = %s] [output = %s]"));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        $arrUserCommitTopics = $arrRet['ret'][$strKey];
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrUserCommitTopics);
    }

    /**
     * @brief  设定当前群话题的start time
     * @param  $arrInput
     * @return array
     */
    public static function setCurrentGroupTopicStartTime($arrInput) {
        // 参数检查
        $intGroupId = intval($arrInput['group_id']);
        if (empty($intGroupId)) {
            Bingo_Log::warning(sprintf("param error. missing user_id or group_id",
                serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $strKey = "topic_start_time_{$intGroupId}";
        $arrRedisInput = array(
            'key'   => $strKey,
            'value' => time(),
        );
        $arrRet = Service_Topic_Redis::redisQuery(self::REDIS_NAME, 'SET', $arrRedisInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("set the current group topic start time failed. [input = %s] [output = %s]",
                serialize($arrRedisInput), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief  获取当前群话题的start time
     * @param  $arrInput
     * @return array
     */
    public static function getCurrentGroupTopicStartTime($arrInput) {
        // 参数检查
        $intGroupId = intval($arrInput['group_id']);
        if (empty($intGroupId)) {
            Bingo_Log::warning(sprintf("param error. missing user_id or group_id",
                serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $strKey = self::TOPIC_START_TIME_PREFIX.$intGroupId;
        $arrRedisInput = array(
            'key'   => $strKey,
        );
        $arrRet = Service_Topic_Redis::redisQuery(self::REDIS_NAME, 'GET', $arrRedisInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("get the current group topic start time failed. [input = %s] [output = %s]",
                serialize($arrRedisInput), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['ret'][$strKey]);
    }

    /**
     * @brief  获取当前群话题的start time
     * @param  $arrInput
     * @return array
     */
    public static function setLastTopicOfUserBrowseInGroup($arrInput) {
        // 参数检查
        $intUserId   = intval($arrInput['user_id']);
        $intGroupId  = intval($arrInput['group_id']);
        $intGtopicId = intval($arrInput['gtopic_id']);
        if (empty($intUserId) || empty($intGroupId) || empty($intGtopicId)) {
            Bingo_Log::warning(sprintf("param error. missing user_id, group_id or gtopic_id. [input = %s]",
                serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrRedisInput = array(
            'key' => self::LAST_TOPIC_PREFIX ."{$intGroupId}_{$intUserId}",
            'value' => $intGtopicId,
        );
        $arrRet = Service_Topic_Redis::redisQuery(self::REDIS_NAME, 'SET', $arrRedisInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("set the last group topic  failed. [input = %s] [output = %s]",
                serialize($arrRedisInput), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief  获取用户在某个群里最新浏览的话题id
     * @param  $arrInput
     * @return array
     */
    public static function getLastTopicOfUserBrowseInGroup($arrInput) {
        // 参数检查
        $intUserId   = intval($arrInput['user_id']);
        $intGroupId  = intval($arrInput['group_id']);
        if (empty($intUserId) || empty($intGroupId)) {
            Bingo_Log::warning(sprintf("param error. missing user_id, group_id. [input = %s]",
                serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 从Redis查询数据
        $strKey = self::LAST_TOPIC_PREFIX ."{$intGroupId}_{$intUserId}";
        $arrRedisInput = array(
            'key' => $strKey,
        );
        $arrRet = Service_Topic_Redis::redisQuery(self::REDIS_NAME, 'GET', $arrRedisInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("Get the last group topic  failed. [input = %s] [output = %s]",
                serialize($arrRedisInput), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['ret'][$strKey]);
    }

    /**
     * @brief  从队列删除某个话题
     * @param  $arrInput
     * @return integer
     */
    public static function rmTopicFromQueue($arrInput)
    {
        $intUserId   = intval($arrInput['user_id']);
        $intGroupId  = intval($arrInput['group_id']);
        $intGTopicId = intval($arrInput['gtopic_id']);
        $intDuration = intval($arrInput['duration']);
        $strKey = "topic_queue_{$intGroupId}";
        $arrRedisInput = array(
            'key'   => $strKey,
            'count' => 1,
            'value' => "{$intGTopicId}_{$intDuration}_{$intUserId}",
        );
        $arrRet = Service_Topic_Redis::redisQuery(self::REDIS_NAME, 'LREM', $arrRedisInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("push the topic to the queue failed.[input=%s] [output=%s]",
                serialize($arrRedisInput), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['ret'][$strKey]);
    }

    /**
     * @brief  检查用户是否有发送到群中的话题
     * @param  $arrInput
     * @return array
     */
    public static function hasTopicInQueue($arrInput) {
        
        $intUserId  = intval($arrInput['user_id']);
        $intGroupId = intval($arrInput['group_id']);

        // 获取队列中的所有内容
        $strKey = 'topic_queue_' . $intGroupId;
        $arrRedisInput = array(
            'key'   => $strKey,
            'start' => 0,
            'stop'  => -1,
        );
        $arrRet = Service_Topic_Redis::redisQuery(self::REDIS_NAME, 'LRANGE', $arrRedisInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("get all the commit queue failed. [input = %s] [output = %s]"), 
            serialize($arrRedisInput), serialize($arrRet));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $arrUserCommitTopics = $arrRet['ret'][$strKey];
        // 遍历该队列找该用户发送的话题内容
        $intIndex = 0;
        foreach ($arrUserCommitTopics as $strTopicInfo) {
            ++$intIndex;
            $arrTopicInfo = explode('_', $strTopicInfo);
            if ($intUserId === intval($arrTopicInfo[2])) {
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS, 1);
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, 0);
    }

    /**
     * @brief  通过话题id获取话题在队列中的位置信息
     * @param  $arrInput
     * @return array
     */
    public static function getTopicInfoInQueue($arrInput) {
        $intGroupId  = intval($arrInput['group_id']);
        $intGtopicId = intval($arrInput['gtopic_id']);

        // 获取队列中的所有内容
        $strKey = 'topic_queue_' . $intGroupId;
        $arrRedisInput = array(
            'key'   => $strKey,
            'start' => 0,
            'stop'  => -1,
        );
        $arrRet = Service_Topic_Redis::redisQuery(self::REDIS_NAME, 'LRANGE', $arrRedisInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("get all the commit queue failed. [input = %s] [output = %s]"),
                serialize($arrRedisInput), serialize($arrRet));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        // 遍历该队列找该用户发送的话题内容
        $arrUserCommitTopics = $arrRet['ret'][$strKey];
        foreach ($arrUserCommitTopics as $strTopicInfo) {
            $arrTopicInfo = explode('_', $strTopicInfo);
            if ($intGtopicId === intval($arrTopicInfo[0])) {
                $arrOutput = array(
                    'gtopic_id' => intval($arrTopicInfo[0]),
                    'duration'  => intval($arrTopicInfo[1]),
                    'user_id'   => intval($arrTopicInfo[2]),
                );
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
            }
        }

        // 若队列中无该话题信息，返回空
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, array());
    }

    /**
     * @brief  获取话题列表
     * @param  $arrInput
     * @return array
     */
    public static function getTopicListFromQueue($arrInput) {
        $intGroupId  = intval($arrInput['group_id']);
        $intStart    = intval($arrInput['start']);
        $intStop     = intval($arrInput['stop']);

        if (empty($intGroupId)) {
            Bingo_Log::warning(sprintf("param error. [input = %s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 获取队列中的所有内容
        $strKey = 'topic_queue_' . $intGroupId;
        $arrRedisInput = array(
            'key'   => $strKey,
            'start' => $intStart,
            'stop'  => $intStop,
        );
        $arrRet = Service_Topic_Redis::redisQuery(self::REDIS_NAME, 'LRANGE', $arrRedisInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("get all the commit queue failed. [input = %s] [output = %s]"),
                serialize($arrRedisInput), serialize($arrRet));
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['ret'][$strKey]);
    }
}
