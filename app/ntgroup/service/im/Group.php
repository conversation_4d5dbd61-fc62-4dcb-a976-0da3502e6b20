<?php

/**
 * 群组的在线信息
 * author : tangyan
 * date : Tue Jul 19 20:06:31 CST 2016
 */

class Service_Im_Group {
    /**
     * 增加group_id->user_id+cuid的映射
     *
     * @param user_id
     * @param cuid
     * @param group_id
     * @return
     */
    public static function addGroupUserCuid($arrInput) {
    }
    /**
     * 清除group_id->user_id+cuid的映射
     * group_online分表后这个方法不再支持
     *
     * group_online目前是单表，可以支持按照user_id+cuid
     * 来清除群组的在线信息，以后group_online按照group_id
     * 分表后，需要指定gids
     *
     * @param user_id
     * @param cuid
     * @return
     */
    public static function clearGroupUserCuid($arrInput) {
    }
    /**
     * 清除特定群的映射关系
     *
     * @param group_id
     * @param user_id
     * @param cuid
     */
    public static function clearGroupUserCuidByGid($arrInput) {
    }
}
