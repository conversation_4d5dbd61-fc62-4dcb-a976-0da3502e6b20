<?php

/***************************************************************************
 *
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * Author: <EMAIL>
 * Date: 2016/8/2
 * Time: 15:38
 * Brief:
 *
 ***************************************************************************/

class submitLiteraryAction extends Util_Base{

    private $strErrBuffer = '提示信息：';
    private $boolRet = true;

    /**
     * @param void
     * @return array
     */
    public function execute(){
        if(empty($this->_arrUserInfo['is_login'])){
            Bingo_Log::warning('user not login');
            return $this->_jsonRet(Tieba_Errcode::ERR_CLIENT_USER_CANNOT_LOGIN, '用户没有登录');
        }
        if (true !== Bingo_Http_Request::isPost()) {
            //必须是post方法
            Bingo_Log::warning('method not post');
            return $this->_jsonRet(Tieba_Errcode::ERR_NOT_POST_METHOD, '必须是post方法');
        }
        //tbs验证
        $strTbs = strval(Bingo_Http_Request::get('tbs'));
        if(empty($strTbs) || !$this->_tbsCheck($strTbs)){
            Bingo_Log::warning('tbs wrong');
            return $this->_jsonRet(Tieba_Errcode::ERR_NVOTE_INVALID_TBS, 'tbs校验失败');
        }
        $intUserId = intval($this->_arrUserInfo['user_id']);
        $strContent = urldecode(Bingo_Http_Request::get('content'));
        $arrGroupIds = Bingo_Http_Request::get('group_id');
        $strPic = urldecode(Bingo_Http_Request::get('pic'));
        if($intUserId < 1 || (empty($strContent) && empty($strPic)) || empty($arrGroupIds)){
            Bingo_Log::warning(sprintf("params illegal $intUserId, %s, %s, %s", $strContent, serialize($arrGroupIds), $strPic));
            return $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, '参数错误');
        }

        if(!$this->checkUser($intUserId)){
            return $this->_jsonRet(Tieba_Errcode::ERR_PRIVILEGE_NO_AUTH, '您没有被授权');
        }
        foreach($arrGroupIds as $key=>$value){
            $ret = $this->submitGroupTopic($intUserId, $value, $strContent, $strPic);
            if(empty($ret)){
                $this->boolRet = false;
                $this->strErrBuffer .= $this->_getGroupName($value).'提交失败   ';
            }
            if($ret['errno'] == Tieba_Errcode::ERR_LIGHT_SERVICE_ACTSCTRL){
                $this->boolRet = false;
                $this->strErrBuffer .= $this->_getGroupName($value).'缓冲队列中已经有该用户话题    ';
            }
            if($ret['errno'] != Tieba_Errcode::ERR_SUCCESS && $ret['errno'] != Tieba_Errcode::ERR_LIGHT_SERVICE_ACTSCTRL){
                $this->boolRet = false;
                $strMsg = Bingo_Encode::convert(Tieba_Error::getUserMsg(intval($ret['errno'])), Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
                $this->strErrBuffer .= $this->_getGroupName($value).$strMsg.'   ';
            }
        }
        if($this->boolRet){
            return $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, Bingo_Encode::convert(Tieba_Error::getUserMsg(Tieba_Errcode::ERR_SUCCESS), Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK));
        }
        else{
            Bingo_Log::warning("提交topic信息失败： ".$this->strErrBuffer);
            return $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, $this->strErrBuffer);
        }
    }


    /**
     * @param $intUserId
     * @return bool
     */
    private function checkUser($intUserId){
        $arrInput = array(
            'user_id' => $intUserId,
        );
        $arrRet = Tieba_Service::call('ntgroup', 'checkWhiteUser', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS == $arrRet['errno'] && $arrRet['data']['in'] == 1) {
            return true;
        }
        else{
            return false;
        }
    }


    /**
     * @param $intUserId
     * @param $intGroupId
     * @param $strContent
     * @param $strPic
     * @return bool|mixed|multitype|null
     */
    private function submitGroupTopic($intUserId, $intGroupId, $strContent, $strPic){
        $arrInput = array(
            'user_id'    => $intUserId,
            'group_id'   => $intGroupId,
            'topic_type' => 1, //图文类型为1
        );
        if(!empty($strContent)){
            $arrInput['content'] = $strContent;
        }
        if(!empty($strPic)){
            $arrInput['pic'] = $strPic;
        }
        $arrRet = Tieba_Service::call('ntgroup', 'commitTopic', $arrInput, null, null, 'post', 'php', 'utf-8');
        return $arrRet;
    }


    /**
     * @param $intGroupId
     * @return string
     */
    private function _getGroupName($intGroupId){
        if($intGroupId < 1){
            return '';
        }
        $arrInput = array(
            'group_ids' => array(
                $intGroupId,
            ),
        );
        $ret = Tieba_Service::call('ntgroup', 'mgetGroupInfo', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(empty($ret) || Tieba_Errcode::ERR_SUCCESS != $ret['errno']){
            Bingo_Log::warning(sprintf("getGroupName mgetGroupInfo failed input[%s] output[%s]", serialize($arrInput), serialize($ret)));
            return '';
        }
        return $ret['data'][0]['group_name'];
    }
}