<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-12-28 12:19:53
 * @comment json接口
 * @version
 */
class onlineAction extends Util_Im_Base {

    protected $strBduss = ""; 
    protected $strLat = ""; 
    protected $strLng = ""; 
    protected $strPosition = ""; 
    protected $strWidth = ""; 
    protected $strHheight = ""; 
    protected $strClientVersion   = ''; 
    protected $strClientSpecialVersion =  ""; 
    protected $strIosToken = ''; 
    protected $intClientType = 0;
    protected $intGroupId = 0;
    protected $project = ''; 
    protected $strUnreadMsg = 0;
    protected $arrHeartInterval = array(
        '180',
        '900',
    );  

    /**
     * @param  nill
     * @return  nill
     * */
    public function execute(){
        try {
            $this->_getInput();
            if (empty($this->strCuid)) {
                Bingo_Log::warning('online fail, the cuid or bduss is empty,bduss:['.$this->strBduss.'] cuid:['.$this->strCuid.']');
                $this->_retOutput(array(), Tieba_Errcode::ERR_PARAM_ERROR);
                return false;
            }
            $intUid = 0;
            $strUname = "";
            $ret = Lib_Tbuser::getUserByBduss($this->strBduss);
            if ($ret == false) {
                //非登陆用户
            } else {
                $intUid   = $ret['user_id'];
                $strUname = $ret['user_name'];
            }

            //1、上线，更新设备库
            $localIdc = $this->strLcsIdc ? $this->strLcsIdc : Bd_Idc::getCurrentIdcRoomOrDefault('jx');
            $arrReq = array(
                //todo : where is ios token ?
                'cuid' => $this->strCuid,
                'lcs_ip' => $this->strLcsIp,
                'lcs_port' => $this->intLcsPort,
                'lcs_fd' => $this->intLcsFd,
                'lcs_conn_time' => $this->intConntime,
                'device' => $this->strDevice,
                'bduss' => $this->strBduss,
                'user_id' => $intUid,
                'position' => $this->strPosition,
                'lat' => $this->strLat,
                'lng' => $this->strLng,
                'unread_msg' => $this->strUnreadMsg,
                'idc' => $localIdc,
                'in_time' => time(),
                'client_type' => $this->intClientType, 
            );
            $arrRes = Tieba_Service::call('impusher', 'updateClientInfo', $arrReq, 
                null, null, 'post', 'php', 'utf-8');
            if (!$arrRes || !isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::fatal("call impusher updateClientInfo failed! input :" . serialize($arrReq) . "output : " . serialize($arrRes));
                $this->_retOutput(array(), $arrRes['errno']);
                return false;
            }

            //2, 更新用户的在线信息
            $arrReq = array(
                'cuid' => $this->strCuid,
                'bduss' => $this->strBduss,
                'user_id' => $intUid,
                'in_time' => time(),
                'client_type' => $this->intClientType, 
            );
            $arrRes = Tieba_Service::call('ntgroup', 'userOnline', $arrReq, 
                null, null, 'post', 'php', 'utf-8');
            if (!$arrRes || !isset($arrRes['errno']) || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::fatal("on line failed! input :" . serialize($arrReq) . "output : " . serialize($arrRes));
                $this->_retOutput(array(), $arrRes['errno']);
                return false;
            }
            

            //2、获取用户最新的sid
            /*
            $arrReqLastSid = array(
                'uid' => $intUid,
                'followKey' => 'bazhuTaskUser', 
            );
            ral_set_idc('nj');
            $arrResLastSid = Tieba_Service::call('newpush', 'getLastSidByUid', $arrReqLastSid);
            if (!$arrResLastSid || !isset($arrResLastSid['errno']) || $arrResLastSid['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("call newpush getLastSidByUid failed! input :" . serialize($arrReqLastSid) . "output : " . serialize($arrResLastSid));
                $this->_retOutput(array(), $arrResLastSid['errno']);
                return false;
            }
            $intSid = isset($arrResLastSid['data'][$intUid]) ? intval($arrResLastSid['data'][$intUid]) : 0;
             */


            $arrResData = array(
                'pushInfo' 	=> array(
                    'sid' 	=> $intSid,
                ),
            );

            $this->_retOutput($arrResData,$arrRes['errno']);

        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            $this->_jsonRet($e->getCode(), '未知错误');	
        }
    }

    /**
     * @param  nill
     * @return  nill
     * */
    protected function _getInput(){
		Bingo_Log::warning(print_r($this->arrBody, true));
        $this->strBduss = strval($this->arrBody['bduss']);

        $this->strDevice = strval($this->arrBody['device']);
        $this->arrDevice = json_decode($this->strDevice,true);
        $this->arrDevice['_client_type'] = $this->_changeClientType($this->arrDevice['_client_type']);
        $this->strDevice = json_encode($this->arrDevice);

        $this->strLat = strval($this->arrBody['lat']);
        $this->strLng = strval($this->arrBody['lng']);
        $this->strWidth = strval($this->arrBody['width']);
        $this->strHeight = strval($this->arrBody['height']);
        $this->strUnreadMsg = strval($this->arrBody['unread_msg']);
        $this->strClientVersion = strval($this->arrDevice['_client_version']);
        $this->strClientSpecialVersion = strval($this->arrDevice['_special_version']);
        $this->intClientType = intval($this->arrDevice['_client_type']);
        $this->strPversion = strval($this->arrDevice['pversion']);
        $this->strIosToken = strval($this->arrDevice['ios_token']);
        $this->intGroupId = intval($this->arrBody['groupId']);
        $this->project = strval($this->arrBody['project']);
    }

    /**
     * @param  nill
     * @return  nill
     * */
    protected function _errRet($intErrno){
        $arrOutput = array(
            'errno'     => intval($intErrno),
            'errmsg'    => Tieba_Error::getUserMsg($intErrno),
            'data'      => array(),
        );

        if ($this->isLcs) {
            $arrOutput = Lib_Pack::array2pack($arrOutput, $this->strFormat);
        } else {
            $arrOutput = Bingo_String::array2json($arrOutput);
        }
        echo $arrOutput;

        return ;
    }
}
