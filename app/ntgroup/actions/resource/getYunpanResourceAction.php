<?php

/**
 * @file getYunpanResourceAction.php
 * <AUTHOR>
 * @date 2016/07/19 19:30:04
 * @brief
 *
 **/

class getYunpanResourceAction extends Util_Base
{
    /**
     * @brief  main stream
     * @return bool
     */
    public function execute()
    {
        // 参数检查
        $strUrl = Bingo_Http_Request::get('url', '');

        if (empty($strUrl)) {
            Bingo_Log::warning(sprintf("param error. missing the url param."));
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
            return false;
        }
        $url = 'http://thumbnail0.baidupcs.com/thumbnail/f130b65465af8a65a2d94dc19aba330f?fid=1764046935-250528-874068816223408&time=1471413600&rt=sh&sign=FDTAER-DCb740ccc5511e5e8fedcff06b081203-SNRBtm376W%2FpJQhRBaTtOvVG7wQ%3D&expires=8h&chkv=0&chkbd=0&chkpc=&dp-logid=5325601696991598852&dp-callid=0&size=c850_u580&quality=100';

        $arrRet = Bd_Pic::picCrawler('tieba', $url);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(print_r($arrRet, true));
            $this->jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail.');
            return false;
        }

        $reqUrl = array(
            array(
                "pic_id"       =>  $arrRet['pic_id'],
                "product_name" => "tieba",
            ),
        );
        $arrRet = Bd_Pic::pid2Url($reqUrl,true);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(print_r($arrRet, true));
            $this->jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail.');
            return false;
        }
        $this->jsonRet(Tieba_Errcode::ERR_SUCCESS, 'success', $arrRet['resps']);
        return true;
    }

    /**
     * @brief 返回json数据
     * @param $intErrno
     * @param $strErrMsg
     * @param $arrData
     */
    private function jsonRet($intErrno, $strErrMsg='', $arrData=null)
    {
        $arrReturnData = array(
            'no'    => $intErrno,
            'error' => $strErrMsg,
            'data'  => $arrData,
        );
        echo json_encode($arrReturnData);
    }
}