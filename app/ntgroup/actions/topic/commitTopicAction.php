<?php
/**
 * @file commitTopicAction.php
 * <AUTHOR>
 * @date 2016/07/25 16:49:59
 * @brief
 *
 **/

class commitTopicAction extends Util_Base {
    public function execute()
    {
        $arrInput = array(
            'user_id'  => intval(Bingo_Http_Request::get('user_id', '')),
            'group_id' => intval(Bingo_Http_Request::get('group_id', '')),
            'content'  => strval(Bingo_Http_Request::get('content', '')),
            'pic'      => strval(Bingo_Http_Request::get('pic', '')),
        );
        $arrRet = Tieba_Service::call('ntgroup', 'commitTopic', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call ntgroup commitTopic failed. [input = %s] [output = %s]",
                serialize($arrInput), serialize($arrRet)));
        }
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
    }
}
