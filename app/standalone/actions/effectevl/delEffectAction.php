<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/



/**
 * @file delEffectAction.php
 * <AUTHOR>
 * @date 2015/11/02 8:43:06
 * @brief 
 *  
 **/

class delEffectAction extends Util_Effect_Base {
    protected $_arrReqKeyFields = array('id');
    protected $_arrReqOptFields = array();
    protected $_strServiceType = 'delEffectAction';
    protected $_intResId;
    protected $_arrItemList;
    const CUSTOM_ERR_RES_ID = 3;
    const CUSTOM_ERR_DEL_EFFECT  = 2;
    public function preProcess() {
        return true;
    }
    
    public function process($arrBaseData, $arrOutput) {
        $this->_intResId = intval($this->_arrReqParam['id']);
       // $this->_arrItemList = $this->_arrReqParam['itemList'];
        if (false === $this->_checkParams()) {
            return false;
        }
        //create res evl item
        
        $arrInput = array(
            'input' => array(
                'res_id'       => $this->_intResId,
                'status'       => 0,
                'op_user_id'   => $this->_arrBaseData['user']['id'],
                'op_user_name' => $this->_arrBaseData['user']['name'],
            ),
        );
        //$arrItemRes = Tieba_Service::call('standalone', 'delRes', $arrInput);
        $arrItemRes = Service_Effect_Effect::delRes($arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrItemRes['errno']) {
            Bingo_Log::warning('call standalone delRes error ' . serialize($arrInput) . '_' . serialize($arrItemRes));
            Util_Effect_Common::jsonOutput(self::CUSTOM_ERR_DEL_EFFECT, 'del res fail');
            return false;
        }
        Util_Effect_Common::jsonOutput(Tieba_Errcode::ERR_SUCCESS);
        return true;

        
    }

    private function _checkParams() {
        if ($this->_intResId <= 0) {
            Util_Effect_Common::jsonOutput(self::CUSTOM_ERR_RES_ID, 'res id invalid');
            return false;
        }
        return true;
    }


    /*private function _checkItem($arrRestraintItems, $strItemType, $strItemName) {
        $intIsFind = 0;
        foreach ($arrRestraintItems as $value) {
            if ($value['evl_item_type_name'] === $strItemType && $value['evl_item_name'] === $strItemName) {
                $intIsFind = 1;
                break;
            }
        }
        return $intIsFind;
    }*/

    

}

?>
