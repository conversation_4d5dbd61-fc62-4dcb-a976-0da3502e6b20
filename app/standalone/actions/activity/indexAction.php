<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/



/**
 * @file indexAction.php
 * <AUTHOR>
 * @date 2015/05/19 10:25:06
 * @brief 
 *  
 **/

class indexAction extends Util_Common_Base {
    protected $_arrReqKeyFields = array('forum_id');
    protected $_strServiceType = 'indexAction';
    protected $_strTemplate = "activity/activity.php";
   
    public function preProcess() {
        $arrInput = array(
            'opmonitor' => array(
                array(
                    'method' => 'isHavePerm',
                    'input'  => array(
                        'req' => array(
                            'owner_forum_id' => $this->_intForumId,
                        ),
                    ),
                ),
            ),
            'official' => array(
                array(
                    'method' => 'getMatrixInfoByManageForumId',
                    'input'  => array(
                        'field' => array(
                            'id',
                            'msg_send_times',
                            'msg_link_status',
                        ),
                        'manage_forum_id' => $this->_intForumId,
                    ),
                ),
            ),
        );
        return $arrInput;
    }
    
    public function process($arrBaseData, $arrOutput) {
        $bolHasMonitorPerm = false;
        $bolHasMatrix = false;
        foreach ($arrOutput as $key => $value) {
            if ($value['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("call $key fail. Input[" . serialize($arrInput) . "]");
                return false;
            }
            if ($key === 'official') {
                $intId = intval($value['data']['id']);
                if ($intId > 0) {
                    $bolHasMatrix = true;
                }
            }
            if ($key === 'opmonitor') {
                if ($value['res']['have_perm'] === true) {
                    $bolHasMonitorPerm = true;
                }
            }
        }
        
        
        $arrPlatformSwitch = array();
        $arrForumStyle = $arrBaseData['forum']['attrs'];
        $arrSwitchFieldMap = array(
            'official_fortune_bag' => 'is_open_fortune',
            'official_activity'    => 'is_open_activity',
            'official_msg'         => 'is_open_message',
            'official_msg_custom'  => 'is_open_custom',         
        );
        $arrPlatform['switches'] = $this->_getSwitches($arrForumStyle, $arrSwitchFieldMap);
        $arrForumInfo = array(
            'forum_id'     => $this->_intForumId,
            'forum_name'   => Bingo_Encode::convert($arrBaseData['forum']['forum_name']['forum_name'],Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK),
            'avatar'       => $arrBaseData['forum']['card']['avatar'],
            'member_count' => $arrBaseData['forum']['member_count'],
            'official_type' => intval($arrBaseData['forum']['attrs']['official']['official_type']),
        );
        $strUrl = $this->_getQrcodeUrl();
        $arrPlatform['qrcode'] = $strUrl;
        $arrPlatform['switches']['is_open_matrix'] = (true === $bolHasMatrix) ? 1 : 0;
        $arrPlatform['switches']['is_open_sentiment'] = (true === $bolHasMonitorPerm) ? 1 : 0;
        
        $arrUser = Bingo_Encode::convert($arrBaseData['user'], Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        Bingo_Page::assign('platform', $arrPlatform);
        Bingo_Page::assign('forum', $arrForumInfo);
        Bingo_Page::assign('user', $arrUser);
        Bingo_Page::assign('perm', $arrBaseData['perm']);
        
        return true;
    }
    
    private function _getSwitches($arrForumStyle, $arrSwitchFieldMap) {
        foreach ($arrSwitchFieldMap as $key => $value) {
            if (isset($arrForumStyle['official']['official_common_switch'][$key])
                && 1 === intval($arrForumStyle['official']['official_common_switch'][$key])) {
                if (!empty($value)) {
                    $arrPlatformSwitch[$value] = 1;
                    continue;
                }
                $arrPlatformSwitch[$key] = 1;
            }
        }
        return $arrPlatformSwitch;
    }
}

?>
