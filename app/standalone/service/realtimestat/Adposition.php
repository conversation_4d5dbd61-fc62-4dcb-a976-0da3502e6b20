<?php
/**
 *
 * <AUTHOR>
 * @date 2019/3/19
 */
class Service_Realtimestat_Adposition extends Service_Realtimestat_Base
{

    /**
     * @var Dl_Realtimestat_Taskresult
     */
    public static $dl_class = 'Dl_Realtimestat_Adposition';

    /**
     * @param array $arrInput
     * @return array
     */
    public static function getAdpositionList($arrInput)
    {
        $arrInput['page_size'] = isset($arrInput['page_size']) ? $arrInput['page_size'] : 1000;
        $result = parent::getList($arrInput);

        return $result;
    }

    /**
     *
     * @param $arrInput
     */
    public static function getAdPositionTree($arrInput)
    {
        $arrOutput = self::getAdpositionList($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call standalone::getAdpositionList fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $treeData = self::buildTree($arrOutput['data']['list']);

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $treeData);
    }

    /**
     * @param array $list
     * @param int $parent_id
     * @return array
     */
    private static function buildTree($list, $parent_id = 0)
    {
        $tree = array();
        foreach ($list as $key => $item) {
            unset($list[$key]);
            if ($item['parent_id'] == $parent_id) {
                $tree[] = array(
                    'label' => "{$item['name']} ({$item['id']})",
                    'name' => "{$item['name']} ({$item['id']})",
                    'value' => $item['id'],
                    'children' => self::buildTree($list, $item['id'])
                );
            }
        }

        return $tree;
    }

    /**
     * 取广告位大类下所有根节点广告位
     *
     * @param array $arrInput
     * @return array
     */
    public static function getPositionTreeLeaf($arrInput)
    {
        $parentId = (array) $arrInput['parent_id'];
        while (true) {
            $childrenPosition = self::getChildrenPosition($parentId);
            if (!empty($childrenPosition)) {
                $parentId = array();
                foreach ($childrenPosition as $item) {
                    $parentId[] = $item['id'];
                }
            } else {
                break;
            }
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $parentId);
    }

    /**
     * @param $position_id
     * @return array
     */
    private static function getChildrenPosition($position_id)
    {
        $arrInput = array(
            'cond' => array(
                'parent_id' => array('in', (array)$position_id),
                'status' => 0
            )
        );
        $arrOutput = Tieba_Service::call('standalone', 'getAdpositionList', $arrInput, null, null, 'post', 'php', 'gbk');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call standalone::getAdPositionTree fail. input:[" . serialize($arrOutput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return array();
        }

        return $arrOutput['data']['list'];
    }


    /**
     * @param $arrInput
     * @return array|bool|int
     */
    public static function updateAdposition($arrInput)
    {
        return parent::update($arrInput);
    }

    /**
     * @param $arrInput
     * @return array|bool|int
     */
    public static function createAdposition($arrInput)
    {
        return parent::create($arrInput);
    }

}