<?php 
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 

/**
 * @file effect_evl_send_email_all.php
 * <AUTHOR>
 * @date 2015/11/10 11:48:06
 * @brief effect evl send email all
 *  
 **/
 
    @set_time_limit(0);
    @ini_set("memory_limit", "1G");
    $strDBName = 'forum_effect_evl';
    $objDB = DB::_getDB($strDBName);
    $intDateTime = strtotime(date('Y-m-d', time()));
    $intTime = $intDateTime;
    $intPreTime = strtotime("-1day", $intDateTime);
    $strDate = date('Y-m-d', $intTime);
    $strDate2 = date('Ymd', $intTime);
    $sql = "select * from evl_order where email_create_time >= end_time and email !='' and email_create_time >= $intPreTime and email_create_time<$intTime";
    $arrOrderRes = $objDB->query($sql);
    if (empty($arrOrderRes)) {
        var_dump('order is empty!');
        return true;
    }

    foreach ($arrOrderRes as $value) {
        $intThrowId = $value['tmp_id'];
        $intResId = intval($value['res_id']);
        $strResName = '';
        $strEmail = $value['email'];        
        $arrResOut = Tieba_Service::call('resource', 'getResourceList', array());
        if (Tieba_Errcode::ERR_SUCCESS !== $arrResOut['errno']) {
            Bingo_Log::warning('call resource getResourceList error' . serialize($arrResInput) . '_' . serialize($arrResOut));
            Util_Effect_Common::jsonOutput(self::CUSTOM_ERR_RES_LIST, 'call resource getResourceList fail');
            return false;
        }


        foreach ($arrResOut['data'] as $arrResValue) {
            if (intval($arrResValue['id']) === $intResId) {
                $strResName = $arrResValue['name'];
                break;
            }
        }
        $strResName = Bingo_Encode::convert($strResName, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        $strStart = date('Y-m-d H:i:s', $value['start_time']);
        $strEnd = date('Y-m-d H:i:s', $value['end_time']);
        $strTime = "$strStart - $strEnd";


        //get evl item value list
        $arrInput = array(
            'input' => array(
                'res_id'   => $intResId,
                'throw_id' => $intThrowId,
                'limit'    => 100,
                'offset'   => 0,
                'field'    => array(
                    'action_type',
                    'event_day',
                    'pv',
                    'uv',
                    'client_type',
                    'data_name',
                ),
                'opt' => array(
                    'client_type' => 'all',
                    'data_name'   => 'all',
                ),

            ),
        );

        $arrValueListRes = Tieba_Service::call('standalone', 'getEvlItemValueList', $arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrValueListRes['errno']) {
            Bingo_Log::warning('call standalone getEvlItemValueList error ' . serialize($arrInput) . '_' . serialize($arrValueListRes));
            Util_Effect_Common::jsonOutput(self::CUSTOM_ERR_GET_ORDER_LIST, 'getEvlItemValueList fail');
            return false;
        }


        $arrInput = array(
            'input' => array(
                'res_id'   => $intResId,
                'throw_id' => $intThrowId,
                'field'    => array(
                    'action_type',
                    'item_type',
                    'weight',
                ),
            ),
        );
        $arrWeightRes = Tieba_Service::call('standalone', 'getWeight', $arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrValueListRes['errno']) {
            Bingo_Log::warning('call standalone getWeight error ' . serialize($arrInput) . '_' . serialize($arrWeightRes));
            Util_Effect_Common::jsonOutput(self::CUSTOM_ERR_GET_ORDER_LIST, 'getWeight fail');
            return false;
        }

        $arrItemSwitch = _getItemSwitch($intResId);
        //get action type
        $arrActionType = array();
        $arrMap = array(
            '点击量' => 'CLICK',
            '浏览量' => 'VIEW',
            '展现量' => 'VIEW_TRUE',
        );
        foreach ($arrItemSwitch as $key => $arrSwicth) {
            $strActionType = $arrMap[$key];
            if (!in_array($strActionType, $arrActionType)) {
                $arrActionType[] = $strActionType;
            }
        }

        

        foreach ($arrValueListRes['data']['list'] as $key => $value) {
            $strCurDate = $value['event_day'];
            $strActionType = $value['action_type'];
            if (!in_array($strActionType, $arrActionType)) {
                continue;
            }
            $intPv = $value['pv'];
            $intUv = $value['uv'];
            $intPvWeight = _getWeight($arrWeightRes['data'], $strActionType, 'pv');
            $intUvWeight = _getWeight($arrWeightRes['data'], $strActionType, 'uv');

            $intPvSwitchValue = _getItemSwitchValue($arrItemSwitch, $strActionType, 'pv');
            $intUvSwitchValue = _getItemSwitchValue($arrItemSwitch, $strActionType, 'uv');
            

            if ($intPvWeight >= 1) {
                $intPv = $intPvWeight * $intPv;
            }
            if ($intUvWeight >= 1) {
                $intUv = $intUvWeight * $intUv;                
            }
            if ($strActionType === 'VIEW') {
                $strViewPv = $intPv;
                $strViewUv = $intUv;
                if (-1 !== $intPvSwitchValue) {
                    $arrTmp[$strCurDate]['viewPv'] = $strViewPv;
                }
                if (-1 !== $intUvSwitchValue) {
                    $arrTmp[$strCurDate]['viewUv'] = $strViewUv;
                }
            }
            if ($strActionType === 'VIEW_TRUE') {
                $strViewTruePv = $intPv;
                $strViewTrueUv = $intUv;
                if (-1 !== $intPvSwitchValue) {
                    $arrTmp[$strCurDate]['viewTruePv'] = $strViewTruePv;
                }
                if (-1 !== $intUvSwitchValue) {
                    $arrTmp[$strCurDate]['viewTrueUv'] = $strViewTrueUv;
                }
            }
            if ($strActionType === 'CLICK') {
                $strClickPv = $intPv;
                $strClickUv = $intUv;
                if (-1 !== $intPvSwitchValue) {
                    $arrTmp[$strCurDate]['clickPv'] = $strClickPv;
                }
                if (-1 !== $intUvSwitchValue) {
                    $arrTmp[$strCurDate]['clickUv'] = $strClickUv;
                }
            }      
        }
        $arrData = $arrTmp;
        _sendMail($intResId, $strDate, $arrData, $strEmail, $intThrowId, $strResName, $strTime);

    }

    function _getItemSwitchValue($arrItemSwitch, $strActionType, $strItemName) {
        $intSwitchValue = 0;
        //switch
        $arrMap = array(
            'CLICK'     => '点击量',
            'VIEW'      => '浏览量',
            'VIEW_TRUE' => '展现量',
        );
        $intIsFind = 0;
        foreach ($arrItemSwitch[$arrMap[$strActionType]]['data'] as $arrItem) {
            if (strstr($arrItem['name'], $strItemName)) {
                $intIsFind = 1;
                if (1 === intval($arrItem['value'])) {
                    $intSwitchValue = 1;
                    break;
                }
            }
        }
        if (0 === $intIsFind) {
            $intSwitchValue = -1;
        }
        return $intSwitchValue;
    }
        
    function _getWeight($arrWeight, $strActionType, $strItem) {
        $intWeight = 1;
        foreach ($arrWeight as $arrItemWeight) {
            if ($strActionType === $arrItemWeight['action_type']) {
                if ($arrItemWeight['item_type'] === $strItem) {
                    $intWeight = $arrItemWeight['weight'];
                    break;
                }
            }
        }
        return $intWeight;
    }

    function _getItemSwitch($intResId) {
         $arrInput = array(
            'input' => array(
                'res_id'       => $intResId,
            ),
        );
        $arrEvlItemRes = Tieba_Service::call('standalone', 'getResEvlItemList', $arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrEvlItemRes['errno']) {
            Bingo_Log::warning('call standalone createResItem error ' . serialize($arrInput) . '_' . serialize($arrItemRes));
            Util_Effect_Common::jsonOutput(self::CUSTOM_ERR_GET_EVL_ITEM_LIST, 'create evl item fail');
            return false;
        }
        
        foreach ($arrEvlItemRes['data'] as $value) {
            $strType = isset($value['evl_item_type']) ? $value['evl_item_type'] : $value['evl_item_type_name'];
            if (!in_array($strType, $arrEvlItemType)) {
                $arrEvlItemType[] = $strType;
            }
        }

        foreach ($arrEvlItemType as $strEvlType) {
            foreach ($arrEvlItemRes['data'] as $value) {
                $strType = isset($value['evl_item_type']) ? $value['evl_item_type'] : $value['evl_item_type_name'];
                if ($strType === $strEvlType) {
                    $arrTmp['id'] = $value['evl_item_id'];
                    $arrTmp['name'] = $value['evl_item_name'];
                    $arrTmp['value'] = $value['evl_item_switch'];
                    $arrItemListRet[$strEvlType]['data'][] = $arrTmp;
                }
            }
        }
        return $arrItemListRet;
    }


    function _sendMail($intResId, $strDate, $arrData, $strEmail, $intThrowId, $strResName, $strTime) {
        $toUserArr = array(
            $strEmail,
        );
        $toUserStr  = implode(',', $toUserArr);
        $curTime    = date('Y-m-d H:i:s');
        $curDate    = date('Y-m-d');
       // $strOpContent = implode(',', $arrContent);
        $strForumName = Bingo_Encode::convert($strForumName, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        /*$intTime = strtotime("-1 day", strtotime(date('Y-m-d', time())));
        $strDate = date('Y-m-d', $intTime);*/
        $strSubject = "百度效果评估系统投放效果数据日报$strTime";
        $subject    =  "=?UTF-8?B?" . base64_encode("$strSubject") . "?=";
        $strUrl = "http://tieba.baidu.com/standalone/effectevl/index#/promotion?id=$intResId&throwId=$intThrowId";
        $headers    = "MIME-Version: 1.0\r\nContent-type:text/html;charset=utf-8\r\nFrom: <EMAIL>\r\nContent-Transfer-Encoding: 8bit\r\n";
        $strPreContent = 
        '<!DOCTYPE html>
        <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    table {
                        border-collapse: collapse; 
                        border-spacing: 0; 
                        text-align:center; 
                        margin: 0 auto;
                    }
                    table th {
                        border: 1px solid #aaaaaa;
                        width:300px;
                    }
                    table td {
                        text-align: center;
                        vertical-align: middle;
                        border: 1px solid #aaaaaa;
                    }
                </style>
            </head>
            <body>
                <div>
                    <font style="text-align:left;font-weight:bold">'."投放id:$intThrowId  投放资源位:$strResName  投放时段:$strTime".'</font>
                    <br />
                    <table>
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>浏览量(PV)</th>
                                <th>访客数(UV)</th>
                                <th>点击量(PV)</th>
                                <th>点击量(UV)</th>
                                <th>展现量(PV)</th>
                                <th>展现量(UV)</th>
                            </tr>
                        </thead>';

                $intIndex = 0;
                foreach ($arrData as $key => $value) {
                    $intIndex++;
                    if ($intIndex % 2 == 0) {
                        $strDataContent .= '<tr style="background: #f9f9f9;" > ';
                    } else {
                        $strDataContent .= '<tr>';
                    }
                     $strCurDate = $key;
                     $strViewPv = $value['viewPv'];
                     $strViewUv = $value['viewUv'];
                     $strViewTruePv = $value['viewTruePv'];
                     $strViewTrueUv = $value['viewTrueUv'];
                     $strClickPv = $value['clickPv'];
                     $strClickUv = $value['clickUv'];

                     $strDataContent .=
                     '<td>'.$strCurDate.'</td>
                            <td>'.$strViewPv.'</td>
                             <td>'.$strViewUv.'</td>
                            <td>'.$strClickPv.'</td>
                            <td>'.$strClickUv.'</td>
                            <td>'.$strViewTruePv.'</td>
                            <td>'.$strViewTrueUv.'</td>'.'<tr>';

                }

    $strAfterContent = '</table>  
                     <br />               
                     <font style="text-align:left;">点击><a href='.$strUrl.'>此处</a>了解详细投放效果数据</font>
                </div>
            </body>
        </html>';

        $strContent = $strPreContent . $strDataContent . $strAfterContent;
        mail($toUserStr, $subject, $strContent, $headers);
    }
    


//get db
class DB {
    public static function _getDB($dbname) {
        $dbName = $dbname;
        $objDB = Tieba_Mysql::getDB($dbName);
        if ($objDB && $objDB->isConnected()) {
            return $objDB;
        } else {
            return NULL;
        }
    }
}







