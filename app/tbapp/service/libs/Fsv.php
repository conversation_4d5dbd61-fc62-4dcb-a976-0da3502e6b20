<?php
/**
 * Created by PhpStorm.
 * User: wangchao25
 * Date: 14-11-4
 * Time: 上午11:53
 */

// fast safe validator
class Service_Libs_Fsv{
  public static function is_valid(array $data, array &$rules) {
      foreach($rules as $filed => $rule) {
          if ($rule === "required" && !isset($data[$filed])) {
              $rules[$filed] = $rule . " not meet";
              return false;
          }
      }
      return true;
  }
}