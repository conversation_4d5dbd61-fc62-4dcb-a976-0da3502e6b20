/*
 *计划信息
 */
struct plan_info
{
	uint32_t plan_id   ; //计划id
	string   plan_name ; //计划名称
	int32_t  plan_type ; //计划类型
	string   place_fir ; //发起地点(省)
	string   place_sec ; //发起地点(市区)
	string   plan_sum  ; //计划摘要
	string   plan_image; //宣传封面图片url
	string   plan_video = optional(); //宣传封面视频url
	string   plan_desc ; //计划详细描述
	int32_t  vote_num  ; //投票数量
	int32_t  vote_hope_num ; //期望投票数量
	string   plan_url  ; //计划url
	uint32_t forum_bind ; //绑定吧id
	uint32_t user_id   ; //计划发起者id
	uint32_t user_name ; //计划发起者名称
	int32_t  date_time ; //计划发起时间
	int32_t  pub_time  ;  //计划审核通过时间	
	uint64_t thread_id ; //计划对应的帖子id
};

/*
 * 获取所有计划列表接口输出参数
 */
struct plan_list_input
{
	uint32_t forum_id;    //吧id
	uint32_t forum_name;  //吧名
	int32_t  tab_flag ;   //区分 0 默认 1 推荐 2人气卡
	int32_t  type_flag  ; //区分 0 默认 1 科技、2 文化、3 艺术、4 生活、5 公益，支持等计划类型
	uint32_t offset    ;  //分页偏移量
	uint32_t res_num;     //返回页记录个数
};

/*
 * 获取所有计划列表接口输出参数
 */
struct plan_info_input
{
	uint32_t thread_id ;    //帖子id
};

/*
 * 搜索计划接口输入参数
 */
struct search_input
{
	uint32_t plan_id    = optional()   ;    //帖子id
	uint32_t plan_name  = optional()   ;    //帖子内容
	string   user_name  = optional()   ;    //发起人名称
	int32_t  plan_type       = optional()   ; //分类  0 普通 1 推荐计划 

};


/*
 *获取所有计划列表接口输出参数
 */
struct plan_list_output
{

	int32_t     errno            			    ; //错误编号
	string      errmsg         = optional()     ; //错误消息
	plan_info   plan_list[]                     ; //获取计划列表   

};

/*
 * 建立计划输入参数
 */
struct plan_input
{
	uint32_t forum_id  ;  //吧id
	string   forum_name ; //吧名称
	string   plan_name  = optional() ;  //计划名称
	int32_t  plan_type  = optional() ;  //计划类型
	string   place_fir  = optional() ;  //发起地点(省)
	string   place_sec  = optional() ;  //发起地点(市区)
	string   plan_sum   = optional() ;  //计划摘要
	string   plan_image = optional() ;  //宣传封面图片url
	string   plan_video = optional() ; //宣传封面视频url
	string   plan_desc  = optional() ; //计划详细描述
	uint32_t forum_bind = optional() ; //绑定吧id
	uint32_t user_id    = optional() ; //计划发起者id
	uint32_t user_name  = optional() ; //计划发起者名称
	int32_t  date_time  = optional() ; //计划发起时间
};

/*
 * 建立计划输出参数
 */
struct plan_output
{
	int32_t     errno            			    ; //错误编号
	string      errmsg         = optional()     ; //错误消息
	uint32_t    plan_id                         ; //计划id      
};



/*
 *投票输入参数
 */
struct vote_input
{
	uint32_t thread_id  ; //主题id
	uint32_t user_id    ; //投票者id
	uint32_t user_name  ; //投票者名称
	int32_t  date_time  ; //投票时间
	int32_t  user_ip    ; //投票IP时间
	string   bduid      ; //bduid
	string vote_type       ; //来源: 1 pc 2 wap 智能版 3 client 客户端
};

/*
 *投票输出参数
 */
struct vote_output
{
	int32_t     errno            			    ; //错误编号
	string      errmsg         = optional()     ; //错误消息
	uint32_t    thread_id      = optional()    ;  //计划对应的thread_id  
	uint32_t    plan_id        = optional()    ;  //计划的plan_id  
	int32_t     vote_num       = optional()    ;  //投票数
	int32_t     vote_lef_num   = optional()    ;  //今天剩余投票数

};

/*
 *奖励输入参数
 */
struct award_input
{
	uint32_t user_id                          ; //用户id
	uint32_t user_name       = optional()     ; //用户名称
	int32_t  user_ip         = optional()     ; //IP地址
	int32_t  award_type      = optional()     ; //奖励类型
};

/*
 *奖励输出参数
 */
struct award_output
{
	int32_t     errno            			     ; //错误编号
	string      errmsg          = optional()     ; //错误消息
	int32_t     award_type      = optional()     ; //奖励类型	
	int32_t     have_award      = optional()     ; //是否中奖
};

/*
 *计划管理输入参数
 */
struct plan_manage_input
{
	string   plan_url          = optional()    ; //计划url
	uint32_t plan_id           = optional()    ; //计划id
	uint32_t thread_id         = optional()    ; //帖子id
	uint32_t vote_num     = optional()         ; //当前投票数
	uint32_t vote_hope_num     = optional()    ; //期望投票数
	int32_t  op_type           = optional()    ; //操作类型 1 通过 2 驳回 3 添加推荐计划
	//4 禁止推荐计划  

};

/*
 *计划管理输出参数
 */
struct plan_manage_output
{
	int32_t     errno            			     ; //错误编号
	string      errmsg          = optional()     ; //错误消息

};



/*
 *计划管理输入参数
 */
struct manage_tool_input
{

	uint32_t vote_num          = optional()    ; //参与投票数
	uint32_t plan_num          = optional()    ; //计划总数
	int32_t  op_type           = optional()    ; //操作类型 1 参与投票数修改 2 计划总数修改 

};

/*
 *计划管理输入参数
 */
struct manage_tool_output
{
	int32_t     errno            			     ; //错误编号
	string      errmsg          = optional()     ; //错误消息

};

service interestman
{

	/**
	 * @brief	获取所有计划列表接口
	 * @param [in]	req	: plan_list_input	:	输入参数
	 * @param [in]	res	: plan_list_output	:	输出参数
	 * 	
	 **/
	void getPlansList ( plan_list_input req, out plan_list_output res);

	 /**
	 * @brief	获取单条计划接口
	 * @param [in]	req	: plan_info_input	:	输入参数
	 * @param [in]	res	: plan_output	:	输出参数
	 * 	
	 **/
	void getPlanInfo ( plan_info_input req, out plan_output res);

	/**
	 * @brief	搜索计划接口
	 * @param [in]	req	: search_input	:	输入参数
	 * @param [in]	res	: plan_output	:	输出参数
	 * 	
	 **/
	void searchPlanInfo ( search_input req, out plan_output res);

	/**
	 * @brief	建立计划接口
	 * @param [in]	req	: plan_input	:	输入参数
	 * @param [in]	res	: plan_output	:	输出参数
	 * 	
	 **/
	void createPlan ( plan_input req, out plan_output res);

	/*
	 * @brief	投票接口
	 * @param [in]	req	: vote_input	:	输入参数
	 * @param [in]	res	: vote_output	:	输出参数
	 * 	
	 **/
	commit void addVote ( vote_input req, out vote_output res);

	/*
	 * @brief	领奖接口
	 * @param [in]	req	: award_input	:	输入参数
	 * @param [in]	res	: award_output	:	输出参数
	 * 	
	 **/
	void getAward ( award_input req, out award_output res);	

	/*
	 * @brief	发奖接口
	 * @param [in]	req	: award_input	:	输入参数
	 * @param [in]	res	: award_output	:	输出参数
	 * 	
	 **/
	commit void sendAward ( award_input req, out award_output res);	

	/*
	 * @brief	管理计划接口（通过&拒绝，新增推荐计划，禁用推荐计划，更新期望投票数）
	 * @param [in]	req	: plan_manage_input	:	输入参数
	 * @param [in]	res	: plan_manage_output	:	输出参数
	 * 	
	 **/
	commit void managePlan ( plan_manage_input req, out plan_manage_output res);

	/*
	 * @brief	数据管理接口（修改整体投票数，整体参与数）
	 * @param [in]	req	: manage_tool_input	:	输入参数
	 * @param [in]	res	: manage_tool_output	:	输出参数
	 * 	
	 **/
	commit void manageTool ( manage_tool_input req, out manage_tool_output res);		

};
