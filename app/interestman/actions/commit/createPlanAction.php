<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-05-16 18:59:35
 * @version
 */
class createPlanAction extends Base_Action{

	/**
	 * �ӿڳ�ʼ��
	 * 1����ʼ������
	 * 2�����ܲ���
	 * @see Base_Action::init()
	 */
	public function init() {
		if (! parent::init ()) {
			return false;
		}
		if(time()>=strtotime('2013-07-23 00:00:00')){
			return false;
		}	
		return true;
	}
		
	public function execute(){

		$strRes   = array();
		$intErrno = 0; 

		try {
			// ��ȡ��ز���
			$this->_getParamsForPost();
			
			// ��У��
			#$this->_commonCheck();

			//��½���
			$this->_checkLogin();

			//tbs���
			$this->_checkTbs();

			$serviceInput['req'] = array(
				'forum_id'           => $this->_intForumId,
				'forum_name'         => $this->_strForumName,
				'plan_type'          => $this->_intPlanType,
				'plan_name'          => $this->_strPlanName,
				'place_fir'          => $this->_strPlaceFir,
				'place_sec'          => $this->_strPlaceSec,
				'plan_sum'           => $this->_strPlanSummary,
				'plan_image'         => $this->_strPlanImage,
				'plan_video'         => $this->_strPlanVideo,
				'plan_desc'          => $this->_strContent, 	
				'forum_bind_id'      => $this->_intForumBindId,
				'forum_bind_name'    => $this->_strForumBindName,
				'forum_bind'         => $this->_arrForumBind,
				'user_id'            => Tieba_Session_Socket::getLoginUid(),
				'user_name'          => Tieba_Session_Socket::getLoginUname(),
					
				'gender'          => $this->_intGender,
				'phone_no'      => $this->_strPhoneNo,
				'qq_no'            => $this->_strQQNo,
				'real_name'      => $this->_strRealName, 	
			);
			$ret = Tieba_Service::call('interestman','createPlan',$serviceInput);
			if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']){
				Bingo_Log::warning('call service interestman::createPlan failed,errno[' . $ret['errno'] . '],errmsg[' . $ret['errmsg'] . ']');
				$intErrno = intval($ret['errno']);
			}
		} catch ( Exception $e ) {
			// ���������쳣

			$intErrno = $e->getMessage();
			Bingo_Log::warning ( sprintf('interestman/createPlan failure, errno:%s errmsg:%s',$intErrno,$strMsg ));
			$arrOutput = array(
				'errno'  => $intErrno,
				'errmsg' => $strMsg,
			);
		}
		
		Bingo_Page::assign('no',$intErrno);
		Bingo_Page::assign('error', Tieba_Error::getErrmsg($intErrno) );
		Bingo_Page::assign('data' ,$strRes);
		Bingo_Page::setOnlyDataType("json");
	}


}
?>
