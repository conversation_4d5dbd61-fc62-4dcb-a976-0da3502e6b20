<?php
/***************************************************************************
 * 
 * Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file index.php
 * <AUTHOR>
 * @date 2019/02/27 11:43:10
 * @brief 
 *  
 **/

/*
    注意这里涉及到rd与fe的模块名，如果提前已经协商好统一模块名例如都叫 opmsg，那么这里就不要传第二个参数，默认即可：
        Tieba_Init::init("opmsg");
    但如果没协商，比如rd的模块名叫 opmsg，fe的模块名叫 opmsg_fe，那么这里就应当是（fe这个模块名用于 ROOT_PATH/template/ 下的文件夹名）
        Tieba_Init::init("opmsg","opmsg_fe");
    同理，也可以自定义omp模块名，默认同样使用 opmsg：
        Tieba_Init::init("opmsg",null,"opmsg_omp");
    详情可以看Tieba_Init 内的实现
 */

Tieba_Init::init("opmsg");
define("LOG_UI","UI");
define("LOG_ACK", "ACK");
Bingo_Log::init(array(
    LOG_UI => array(
        'file'  => MODULE_LOG_PATH . '/ui.log',
        'level' => 0x7,
    ),
    LOG_ACK => array(
        'file' => MODULE_LOG_PATH . '/ack.log',
        'level' => 0x7,
    ),
), LOG_UI);

$objFrontController = Bingo_Controller_Front::getInstance(
    array(
        "actionDir" => MODULE_ACTION_PATH,
        "defaultRouter" => "index",
        "notFoundRouter" => "error",
        "beginRouterIndex" => 1,
    )
);

Bingo_Timer::start('total');

try{
    $objFrontController->dispatch();
}catch(Exception $e){
    //出错处理，直接转向到错误页面
    Bingo_Log::warning(sprintf('main process failure!HttpRouter=%s,error[%s]file[%s]line[%s]',
        Bingo_Http_Request::getStrHttpRouter(), $e->getMessage(), $e->getFile(), $e->getLine()));
    Bingo_Page::setErrno($e->getCode());
    header('location:http://static.tieba.baidu.com/tb/error.html');
}
Bingo_Timer::start('build_page');
Bingo_Page::buildPage();
Bingo_Timer::end('build_page');
Bingo_Timer::end('total');

$strTimeLog = Bingo_Timer::toString();
$strTimeLogLocal = '';
if(!empty($strTimeLog)) {
    $strTimeLogLocal = ' time[' . $strTimeLog . ']';
}

Bingo_Log::pushNotice('Timer','['.$strTimeLog.']');
Bingo_Log::buildNotice();




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
