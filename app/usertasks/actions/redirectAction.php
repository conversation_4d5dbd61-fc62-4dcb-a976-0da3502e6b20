<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file redirectAction.php
 * <AUTHOR>
 * @date 2016/10/17 10:04:19
 * @brief 
 *  
 **/
class redirectAction extends Util_Base{
    const DEFAUL_URL = 'http://www.tieba.com';  //第三方地址
    public function _execute(){
        
        try{
            $intTaskId = Bingo_Http_Request::get('task_id',0);      
            //todo
            if ($this->_arrUserInfo['is_login'] !== true ){
                //未登陆
                throw new Util_Exception("user need login!",Tieba_Errcode::ERR_USER_NOT_LOGIN);
            }
            $intUserId = $this->_arrUserInfo['user_id'];
            $arrInput = array(
                'user_id' => $intUserId,
                'task_id' => $intTaskId,
            );
            $arrOutTask = Tieba_Service::call('usertasks','addTaskScore',$arrInput,null,null, 'post', 'php','utf8');
            if(false == $arrOutTask || $arrOutTask['errno'] != Tieba_Errcode::ERR_SUCCESS ){
                Bingo_Log::warning("call usertasks::addTaskScore failed. input=".serialize($arrInput)." output=".serialize($arrOut));
                throw new Util_Exception("service call fail!",Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            
            }else if( 1 === intval($arrOutTask['data']['status']) ){
		list($t1, $t2) = explode(' ', microtime());    
		$msecond = (float)sprintf('%.0f',(floatval($t1)+floatval($t2))*1000);
		$arrParam = array(
                    'ranzhi' => 30,
                    'user_id' => $intUserId,
                    'from'=> 'task_'.$intTaskId,
                    'ext' =>'',
		    'msectime' =>$msecond,

                );
                $arrOut = Tieba_Service::call('ranzhi','addUserRanzhi',$arrParam,null,null, 'post', 'php','utf8');
                if(false == $arrOut || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS ){
                    Bingo_Log::warning("call ranzhi::addUserRanzhi failed. input=".serialize($arrParam)." output=".serialize($arrOut));
                    throw new Util_Exception("service call fail!",Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
                $arrParam = array(
                    'task_id' => $intTaskId,
                    'user_id' => $intUserId,
                );

                $arrOutTask = Tieba_Service::call('usertasks','updateDeliverStatus',$arrParam,null,null, 'post', 'php','utf8');
                if(false == $arrOutTask || $arrOutTask['errno'] != Tieba_Errcode::ERR_SUCCESS ){
                    Bingo_Log::warning("call usertasks::updateDeliverStatus failed. input=".serialize($arrInput)." output=".serialize($arrOut));
                    throw new Util_Exception("service call fail!",Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                
                }

            }
            //从词表中获取外链跳转地址
            $handleWordServer = Wordserver_Wordlist::factory();
            $strTableName = 'tb_wordlist_redis_FansFestival_RedirectLink';//英文表名
            $arrKeys = array($intTaskId);
            $arrItemInfo = $handleWordServer->getValueByKeys($arrKeys,$strTableName);
            if( !empty(strval($arrItemInfo[$intTaskId])) ){
            	Bingo_Http_Response::redirect(strval($arrItemInfo[$intTaskId]),true);
            }
            //Bingo_Http_Response::redirect(self::DEFAUL_URL,true);

        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), Tieba_Error::getUserMsg($e->getCode())); 
        }
        
    }

}
