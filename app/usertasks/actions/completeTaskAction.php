<?php
class completeTaskAction extends Util_Base {
	const TEST_AUTH_KEY = 'c09274fcc3aa65c4';
	public function _execute(){
        $intUserId	= intval($this->_arrUserInfo['user_id']);
        $intForumId = $_COOKIE['MEIZI_MIS_FID'];//intval(Bingo_Http_Request::getGet('forum_id'));
        $intTaskId = intval(Bingo_Http_Request::get('task_id'));
        $orderId = Bingo_Http_Request::get('order_id');
        if($intUserId <= 0 || $intForumId <= 0 || $intTaskId <= 0){
            //没有获取到用户id，提示用户重新登陆
            Bingo_Log::warning('no find uid or forum_id;uid['.$intUserId.']forum_id['.$intForumId.']');
            return self::_jsonRet(Tieba_Errcode::ERR_POST_CT_USER_NOT_LOGIN,'no find uid');
        }
        /***********************to do *********************/
        //校验是否有管理权限
        $arrInput = array(
            'user_id' => $intUserId,
            'forum_id' => $intForumId,
        );
        $arrRet = Tieba_Service::call('team','authenticationCheck',$arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        if($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS || $arrRet===false){
            Bingo_Log::warning('get root is fail;input['.serialize($arrInput).']output['.serialize($arrRet).']');
            return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL,'call team is fail');
        }
        if($arrRet['data'] < 2){
            Bingo_Log::warning('no root to down;input['.serialize($arrInput).']output['.serialize($arrRet).']');
            return self::_jsonRet(Tieba_Errcode::ERR_USER_NO_PERM,'no role');
        }
        /**************************************************/
        $arrInput = array(
            'user_id' => $intUserId,
            'task_id' => $intTaskId,
			'order_id' => $orderId,
			'auth_key' => self::TEST_AUTH_KEY,
        );
        //完成任务
        $arrRet = Tieba_Service::call('usertasks','passTask',$arrInput, NULL, NULL, 'post', 'php');
        if($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS || $arrRet===false){
            Bingo_Log::warning('do task list is fail;input['.serialize($arrInput).']output['.serialize($arrRet).']');
            return self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL,'call service is fail');
        }
        //加审核次数
        $arrCheckTimeInput = array(
            'manager_id' => $intUserId,
            'forum_id' =>$intForumId,
        );
        $arrCheckRet = Tieba_Service::call('team','addManagerReviewNum',$arrCheckTimeInput, NULL, NULL, 'post', 'php', 'utf-8');
        if($arrCheckRet['errno'] !== Tieba_Errcode::ERR_SUCCESS || $arrCheckRet===false){
            Bingo_Log::warning('do task list is fail;input['.serialize($arrCheckTimeInput).']output['.serialize($arrCheckRet).']');
        }
        $arrOutput = array(
            'errno' => Tieba_Errcode::ERR_SUCCESS,
            'errmsg' => 'success',
            'data' => intval($arrRet['data']),
        );
        return self::_jsonRet($arrOutput['errno'],$arrOutput['errmsg'],$arrOutput['data']);
    }
}