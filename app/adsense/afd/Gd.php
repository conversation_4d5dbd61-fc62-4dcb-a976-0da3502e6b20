<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Gd.php
 * <AUTHOR>
 * @date 2016/02/04 13:02:37
 * @brief 
 *  
 **/

class Afd_Gd {

    /**
     * @param mixed $adReq
     * @param mixed $sspConfig
     *
     * @return mixed | bool
     */
    public static function query($adReq, $sspConfig = array()) {
        $gdReq = self::adReqAdapter($adReq);
        $header = array(
            'pathinfo' => "service/adsense",
        );
        $sspConfig = array(
            'id' => $sspConfig['id'],
            'dynamic' => $sspConfig['dynamic'],
            'floor' => $sspConfig['floor'],
        ); 
        $gdReq['ssp'] = $sspConfig;
        $arrInput = array(
            'method' => 'gdCall',
            'format' => 'json',
            'ie' => 'utf-8',
            'input' => $gdReq,
        );
        Afd_Log::pushDebug('gdInput', $arrInput);
        $adRes = ral('service_afd_gd', 'post', http_build_query($arrInput), null, $header);
        //$adRes = Service_Adsense::gdCall($arrInput);
        if ($adRes === false) {
            return false;
        }
        $adRes = json_decode($adRes,true);
        Afd_Log::pushDebug('gdOutput', $adRes);
        if (!is_array($adRes) || $adRes['errno'] != 0) {
            return false;
        }
        $adRes = isset($adRes['data']) ? $adRes['data'] : array();
        if (count($adRes) > 0) {
            $adRes = self::adResAdapter($adRes, $adReq); 
        }
        return $adRes;   
    }

    /**
     * @param mixed $adReq
     *
     * @return mixed
     */
    public static function adReqAdapter($adReq){
        return $adReq;
    }

    /**
     * @param mixed $adRes
     * @param mixed $adReq
     *
     * @return mixed
     */
    public static function adResAdapter($adRes, $adReq) {
        $adArr = array();
        foreach ($adRes as $ad) {
            $adInfo = array();
            $temp_adInfo = array();
            $temp_adInfo['id'] = $ad['id'] . '_' . $adReq['ad']['reqId'];
            $temp_adInfo['productId'] = strval($adReq['ad']['productId']);
            $temp_adInfo['moduleType'] = 1;
            $temp_adInfo['clientType'] = intval($ad['clientType']);
            if (isset($ad['floor'])) {
                $temp_adInfo['floor'] = $ad['floor'];
            }
            $temp_adInfo['extra'] = $ad['extra'];
            $material = array(
                'id' => $ad['material'][0]['id'],
                'info' => $ad['material'][0]['info'],
                'templateId' => $ad['material'][0]['templateId'],
            ); 
            $temp_adInfo['material'] = array($material);
            if ($ad['floor']) {
                $locCode = $adReq['ad']['placeId'] . '_' . $ad['floor'];
            } else {
                $locCode = $adReq['ad']['placeId'];
            }
            $adInfo[] = $temp_adInfo;
            $adArr[] = array(
                'locCode' => strval($locCode),
                'adInfo' => $adInfo,
            );
        }
        return $adArr;
    }
}






/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
