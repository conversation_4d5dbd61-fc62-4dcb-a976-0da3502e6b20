<?php
/**
 * Created by PhpStorm.
 * User: fanyunyang
 * Date: 2015/9/16
 * Time: 16:48
 */

class Service_Libs_BcCtrReq implements Tieba_Multi_Caller{
    public function transRalMultiReq($input) {
        return array(
            'service'=> $input['strServer'],
            'method' => $input['strMethod'],
            'input'  => $input['strMethod']==='get' ? array() : $input['arrData'],
            'extra'  => isset($input['intBalanceKey']) ? $input['intBalanceKey'] : self::_genBalanceKey(),
            'header' => $input['header'],
        );
    }

    public function getRalMultiRes($ralRes) {
        return $ralRes;
    }

    private static function _genBalanceKey() {
        $nowTime = gettimeofday();
        srand($nowTime['usec']);
        return mt_rand();
    }
}

?>
