<?php
/**
 * Created by PhpStorm.
 * User: fanyunyang
 * Date: 2016/3/30
 * Time: 12:25
 */

/**
 * @param $url
 * @param int $intReadTimeOut
 * @param int $intConnTimeOut
 * @return bool
 */
function fetchUrlGet($url, $intReadTimeOut=100, $intConnTimeOut=50) {
    $httpproxy = Orp_FetchUrl::getInstance(array('timeout' =>$intReadTimeOut,'conn_timeout' =>$intConnTimeOut, 'max_response_size' => 1024000,'conn_retry' => 0));
    $res = $httpproxy->get($url);
    $err = $httpproxy->errmsg();
    $http_code = $httpproxy->http_code();
    if ($err) {
        Bingo_Log::warning('fetchurlget:'.$url.' error! error:'.$err);
        return false;
    } else {
        $header = $httpproxy->header();
        if( $http_code == 200 ){
            return $res;
        }
    }
    return false;
}

/**
 * @return array
 */
function getReportData(){

    $arrInput = array(
        'startTime' => '20160310',
        'endTime' => '20160410',
        'type' => 0,
        'idList' => '12345,12354',
    );
    $url = 'http://adp.baidu.com:8101/feedadmin/feedreport/getRemoteOrderReport.do?authCode=4353*y202ajd'.http_build_query($arrInput);
    /*
     * unauthorized：表示未授权
     * required parameter missing： 参数缺失
     * parameter error： 参数有误
     * timeOut：处理超时
     * systemError：系统异常
     */
    $ret = fetchUrlGet($url, 2000, 500);
    echo "raw res:\n";
    var_export($ret);
    echo "\n";
    $arrRes = json_decode($ret, true);
    $arrId2Info = array();
    foreach($arrRes['data'] as $singleInfo){
        $arrId2Info[$singleInfo['id']]['exposure'] += $singleInfo['exposure'];
        $arrId2Info[$singleInfo['id']]['click'] += $singleInfo['click'];
        $arrId2Info[$singleInfo['id']]['cost'] += $singleInfo['cost'];
    }
    echo "res:\n";
    var_export($arrId2Info);
    echo "\n";
    return $arrId2Info;
}

getReportData();