<?php

include "./curl.class.php";

$curl = new mini_tool_curl();

// $url = "http://cq01-forum-rdtest12.vm.baidu.com:8200/service/adsense?method=addConfigs&format=json&ie=utf-8";

// $data = $curl->post($url, $arrParams);

$goods_info = array(
		array(
			'user_name'=>'hi',
			'pic'=>'http://www.pic.com',
		),
		array(
				'user_name'=>'wzb',
				'pic'=>'http://www.pic1.com',
		),
);
for($i = 0; $i< 100 ;$i++) {
	$arrParams = array();
	$arrParams['name'] = '我的广告'.$i;
	$arrParams['url_type'] = 1;
	
	$arrParams['url'] = 'http://www.baidu.com';
	$arrParams['ios_url'] = 'http://www.baidu.com';
	$arrParams['apk_url'] = 'http://www.baidu.com';
	$arrParams['apk_name'] = 'taolisheng.miao.com.org';
	
	
	$arrParams['goods_info'] = $arrInput['goods_info'];
	$arrParams['click'] = 1000;
	$arrParams['download'] = 10000;
	$arrParams['first_dirs'] = '体育,小说';
	$arrParams['second_dirs'] = '欧美电影,韩国电影';
	$arrParams['hot_dirs'] = '体育_1,小说_1,小说_2';
	
	$arrParams['forum_names'] = '德国队,nba';
	$arrParams['black_names'] = '英格兰,剑灵';
	$arrParams['goods_info'] = json_encode($goods_info);
	$arrParams['page_name'] = 'FRS';
	$arrParams['client_type'] = 'PC';
	$arrParams['pos_name'] = '5';
	$arrParams['first_name'] = 'app';
	$arrParams['second_name'] = '游戏';
	$arrParams['start_time'] = time() ;
	$arrParams['end_time'] =  time()+36000;
	$arrParams['op_name'] = 'zhibinwang';
	$arrParams['ctime'] = time();
	$arrParams['mtime'] = time();
	$data = Tieba_Service::call('adsense', 'addAdverts', $arrParams, NULL, NULL, 'post', 'php', 'utf-8', NULL);
}
echo "-----------------------\r\n";
print_r($data);





