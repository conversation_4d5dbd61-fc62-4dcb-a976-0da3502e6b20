<?php
class ServiceModelGdTest extends TestBase
{
    public function beforeTest() {
        $db = Service_Libs_Db::getDB();
        $db->addHook(Bd_DB::HK_AFTER_QUERY, "after_query", 'Service_Libs_Db::afterQuery');
        $db->onFail('Service_Libs_Db::failQuery');
        parent::beforeTest();
    }

    public function testgetGdTable() {
        $partition = 2015;
        $arr = Service_Model_Gd::getGdTable($partition); 
        $this->assertEqual($arr, 'ad_gd');
    }

    public function testgetPdTable() {
        $partition = 2015;
        $arr = Service_Model_Gd::getPdTable($partition); 
        $this->assertEqual($arr, 'ad_period');
    }

    public function testsplitId() {
        $str_id = "20151030";
        $arr = Service_Model_Gd::splitId($str_id); 
        $this->assertEqual($arr, array('partition' => '2015', 'id' => 1030));	
    }

    public function testcreateId() {
        $id = 123;
        $partition = "2015";
        $arr = Service_Model_Gd::createId($id, $partition); 
        $this->assertEqual($arr, '2015123');	
    }

    public function testgetDB() {
        $arr = Service_Model_Gd::getDB(); 
        $this->assertIsA($arr, 'Bd_DB');
    }

    public function testaddGd() {
        $arr_par = array();
        $arr_par['firm_name'] = "阿迪";
        $arr_par['charge_mode'] = 1;
        $arr_par['throw_type'] = "NCPD";
        $arr_par['client_type'] = "PC";
        $arr_par['page_name'] = "FRS";
        $arr_par['pos_name'] = "8";
        $arr_par['loc_code'] = "c0007";
        $arr_par['typeid'] = "2001";
        $arr_par['remark'] = "addidas 冠高";
        $arr_par['contract_number'] = "c2015000002";
        $arr_par['good_infos'] = "{}";
        $arr_par['url_title'] = "标题";
        $arr_par['url_type'] = 2;
        $arr_par['ad_url'] = "http://www.sina.com.cn";
        $arr_par['ad_price'] = 4.20;
        $arr_par['partition'] = date('Y');
        $arr_par['ctime'] = time();
        $arr_par['cusername'] = "tangjiasheng";
        $arr_par['status'] = 0;
        $arr_par['lock_status'] = 1;
        $arr = Service_Model_Gd::addGd($arr_par); 
        $this->assertEqual($arr > 0, true);
    }

    public function testupdateGd() {
        $id = 1;
        $partition = 2015;
        $arr_par = array();
        $arr_par['firm_name'] = "阿迪";
        $arr_par['charge_mode'] = 1;
        $arr_par['throw_type'] = "NCPD";
        $arr_par['client_type'] = "PC";
        $arr_par['page_name'] = "FRS";
        $arr_par['pos_name'] = "8";
        $arr_par['loc_code'] = "c0007";
        $arr_par['typeid'] = "2001";
        $arr_par['remark'] = "addidas 冠高";
        $arr_par['contract_number'] = "c2015000002";
        $arr_par['good_infos'] = "{}";
        $arr_par['url_title'] = "标题";
        $arr_par['url_type'] = 2;
        $arr_par['ad_url'] = "http://www.sina.com.cn";
        $arr_par['ad_price'] = 4.20;
        $arr_par['partition'] = date('Y');
        $arr_par['mtime'] = time();
        $arr_par['musername'] = "tangjiasheng";
        $arr_par['status'] = 0;
        $arr_par['lock_status'] = 1;
        $arr = Service_Model_Gd::updateGd($id, $arr_par, $partition); 
        $this->assertEqual($arr === false, false);
    }

    public function testgetGd() {
        $id = '1';
        $partition = '2015';
        $arr = Service_Model_Gd::getGd($id, $partition); 
        $this->assertEqual(empty($arr), false);
    }

    public function testsearchGd() {
        $arrInput = array(
            'ctime_start' => time(),
            'ctime_end' => time(),
            'status' => 1,
            'client_type' => 'APP',
            'page_name' => 'FRS',
            'pos_name' => '7',
            'pn' => 1,
        );
        $arr = Service_Model_Gd::searchGd($arrInput); 
        $this->assertNotNull($arr);
    }

    public function testaddPeriod() {
        $arrInput = array(
            'start_time' => time(), 
            'end_time' => time() + 60,
            'forum_ids' => '123,123333',
            'gd_id' => 1,
            'partition' => 2015,
            'loc_code' => 'c0001',
            'status' => 1,
        );
        $arr = Service_Model_Gd::addPeriod($arrInput); 
        $this->assertEqual($arr > 0, true);
    }

    public function testupdatePeriod() {
        $arrInput = array(
            'start_time' => time(), 
            'end_time' => time() + 60,
            'forum_ids' => '12,1',
            'loc_code' => 'c0001',
        );
        $id = 25;
        $partition = 2015;
        $arr = Service_Model_Gd::updatePeriod($id, $arrInput, $partition); 
        $this->assertEqual($arr, 1);
    }

    public function testdelPeriod() {
        $id = 25;
        $partition = '2015';
        $arr = Service_Model_Gd::delPeriod($id, $partition); 
        $this->assertEqual($arr, 1);
    }

    public function testgetPeriod() {
        $id = 1;
        $partition = '2015';
        $arr = Service_Model_Gd::getPeriod($id, $partition); 
    }

    public function testgetPeriodByGd() {
        $gd_id = 1;
        $partition = '2015';
        $arr = Service_Model_Gd::getPeriodByGd($gd_id, $partition); 
    }

    public function testgdConflict() {
        $id = "2015120";
        $pn = 1;
        $arr = Service_Model_Gd::gdConflict($id, $pn); 
        $this->assertEqual($arr, array());
    }

    public function testdetailConflict() {
        $id1 = 1;
        $type1 = 1;
        $partition1 = 2015;
        $id2 = 1000000;
        $type2 = 2;
        $partition2 = 0;	
        $arr = Service_Model_Gd::detailConflict($id1, $type1, $partition1, $id2, $typ2, $partition2); 
    }

    public function testcreateAd() {
        return;
        $arrParams = array();
        $arrParams['name'] = "addidas";
        $arrParams['typeid'] = 0;
        $arrParams['url_type'] = 1;
        $arrParams['page_name'] = "FRS";
        $arrParams['client_type'] = "APP";
        $arrParams['pos_name'] = "3";
        $arrParams['start_time'] = time();
        $arrParams['end_time'] = time() + 3600;
        $arrParams['op_name'] = "tangjiasheng";
        $arrParams['global'] = 0;
        $arrParams['firm_name'] = "阿迪";
        $arrParams['url'] = "http://www.baidu.com";
        $arrParams['charge_mode'] = 1; 
        $arrParams['price'] =  0;
        $arrParams['throw_type'] = "NCPD";
        $arrParams['forum_ids'] = "123,23";
        $arrParams['goods_info'] = "{}";	
        $arr = Service_Model_Gd::createAd($arrParams); 
    }

    public function testcheckConflict() {
        $gd_id = "201501";	
        $pd_id = "201502";
        $loc_code = "c0007";
        $forum_ids = "1,2,3";
        $start_time = strtotime("2015-11-05 18:00:00");
        $end_time = strtotime("2015-12-05 19:00:00");
        $now_time = time();
        $arr = Service_Model_Gd::checkConflict($gd_id, $pd_id, $loc_code, $forum_ids, $start_time, $end_time, $now_time); 
    }

    public function testmail() {
        return;
        $mail_content = "ads 123123sdfasdfasdf ";
        $arrMail = array('subject' => 'gd alarm', 'from' =>'广告分发排期系统', 'from' => '<EMAIL>', 'cc' =>'<EMAIL>', 'to' => "<EMAIL>", 'content' => $mail_content);
        $strSubject  = '=?UTF-8?B?' . base64_encode($arrMail['subject']) . '?=';
        $strHeader   = "MIME-Version: 1.0\r\n";   
        $strHeader  .= "Content-type: text/html; charset=utf-8\r\n";   
        $strHeader  .= 'From: =?UTF-8?B?' . base64_encode($arrMail['from_name']) . "?= <$arrMail[from]>\r\n";
        $strHeader  .= "CC: $arrMail[cc]\r\n";
        mail($arrMail['to'], $strSubject, $arrMail['content'], $strHeader);
    }

}
