<?php
define('IS_ORP_RUNTIME',true);
define('ROOT_PATH', dirname(__FILE__) . '/../../../../');
define('MODULE_PATH', ROOT_PATH . 'adsense/');
set_include_path(get_include_path() . PATH_SEPARATOR. MODULE_PATH);
$className = $argv[1];
$methods = get_class_methods($className);
if ($methods) {
	echo "class " . str_replace('_', '', $className) . "Test extends TestBase\n";
	echo "{\n";
	foreach ($methods as $method) {
		echo "\tpublic function test" . $method . "() {\n";
		echo "\t\t//" . '$arr = ' . $className . "::" . $method ."(); \n";
		echo "\t}\n\n";
	}
	echo "}\n";
}
