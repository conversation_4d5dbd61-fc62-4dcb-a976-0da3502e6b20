<?php
/***************************************************************************
 *
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file SmallFlowCtrl.php
 * <AUTHOR>
 * @date: 2016/07/12 17:36
 * @brief:小流量实验控制
 *
 */

class Service_Model_SmallFlowCtrl
{
    const WORDLIST_TABLE_NAME = 'tb_wordlist_redis_forum_for_fengchao_experiment';
    /**
     * @brief: 判断是否进行获取小流量实验的吧
     * @param: null
     * @return: array
     */
    public static function getSmallFlowForumId($arrInput)
    {
        if (empty($arrInput['forum_id'])) {
            return false;
        }
        //词表中配置的吧、吧一级目录和二级目录
        $arrWordRes = self::_getWordList(self::WORDLIST_TABLE_NAME);
        if ($arrWordRes !== false) {
            $arrForumList = unserialize($arrWordRes['forum_ids']);
            $arrSecondLevelDirList = unserialize($arrWordRes['forum_second_dirs']);
            $arrFirstLevelDirList = unserialize($arrWordRes['forum_first_dirs']);
            $arrExtraParam = unserialize($arrWordRes['extra_param']);

            $arrForums = self::_getDirList($arrForumList);
            $forumName = $arrInput['forum_name'];
            //判断词表中是否配置了吧
            if (!empty($arrForums[$forumName])) {
                //吧流量均分
                $arrForumEids = explode('_', $arrForums[$forumName]);
                return self::_splitFlow($arrForumEids);
            }
            $arrSecondDirs = self::_getDirList($arrSecondLevelDirList);
            $secondDir = $arrInput['forum_second_dir'];
            //判断词表中是否配置了二级目录
            if (!empty($arrSecondDirs[$secondDir])) {
                //二级目录流量均分
                $arrEids = explode('_', $arrSecondDirs[$secondDir]);
                return self::_splitFlow($arrEids);
            }
            $arrFirstDirs = self::_getDirList($arrFirstLevelDirList);
            $firstDir = $arrInput['forum_dir'];
            //判断词表中是否配置了一级目录
            if (!empty($arrFirstDirs[$firstDir])) {
                //一级目录流量均分
                $arrsecEids = explode('_', $arrFirstDirs[$firstDir]);
                return self::_splitFlow($arrsecEids);
            }
            //
            if (empty($arrExtraParam)) {
                return false;
            }
            $res = self::_getCustomEid($arrInput, $arrExtraParam);
            if (!empty($res)) {
                return $res;
            }
        }

        return false;
    }
    /**
     * @param: 从词表中拿到需要进行小流量实验的吧id列表
     * @return:
     */
    private static function _getWordList($strTableName){
        $handleWordServer = Wordserver_Wordlist::factory();
        $arrInput = array(
            'table'	=>	$strTableName,
            'start'	=>	0,
            'stop'	=>	-1,
        );
        $arrItemInfo = $handleWordServer->getTableContents($arrInput);
        if(isset($arrItemInfo['ret']) && !empty($arrItemInfo['ret'])) {
            return  $arrItemInfo['ret'];
        }else{
            Bingo_Log::warning ( 'get wordlist error'. serialize($arrInput));
            return false;
        }
    }
    /**
     * @param: 把词表中配置的吧目录下的eid重新按照kv排列
     * @return:
     */
    private static function _getDirList($arrDirList) {
        $arrDirEid = array();
        if (empty($arrDirList)) {
            return $arrDirEid;
        }
        foreach ($arrDirList as $key => $value) {
            $arrTmp = explode(':', $value);
            $arrDirEid[$arrTmp[0]] = $arrTmp[1];
        }
        return $arrDirEid;
    }
    /**
     * @param: 自配置实验id
     * @return:
     */
    private static function _getCustomEid($arrInput, $arrExtraFlow) {
        $arrParam = self::_getCustomParam($arrExtraFlow);
        $res = self::_getEid($arrInput, $arrParam);
        return $res;
    }
    /**
     * @param: 解析把词表中配置的参数进行分流,得到eid
     * @return:
     */
    private static function _getCustomParam($arrExtraFlow) {
        $arrParam = array();
        if (empty($arrExtraFlow)) {
            return $arrParam;
        }
        foreach ($arrExtraFlow as $key => $value) {
            $arrTmp = explode(':', $value);
            $arrValue = explode(';', $arrTmp[1]);
            $arrEid = explode('_', $arrValue[0]);
            $arrRate = explode('_', $arrValue[1]);
            $arrParam[$arrTmp[0]]['eid'] = $arrEid;
            $arrParam[$arrTmp[0]]['rate'] = $arrRate;
        }
        return $arrParam;
    }
    /**
     * @param: 解析把词表中配置的参数进行分流,得到eid
     * @return:
     */
    private static function _getEid($arrInput, $arrParam) {
        $arrEids = array();
        foreach ($arrParam as $key => $value) {
            if (empty($arrInput[$key])) {
                continue;
            }
            $hashvalue = md5($arrInput[$key]);
            $strHash = substr($hashvalue, count($hashvalue) - 16, 16);
            $intHash = abs(intval(hexdec($strHash)));
            $intBucket = intval($value['rate'][1]);
            $intInterval = intval($value['rate'][0]);
            if ($intBucket <= 0 || $intInterval <= 0) {
                return false;
            }
            $res = $intHash%$intBucket;
            for ($i=0;$i < count($value['eid']);$i++) {
                if ($res >=$intInterval * $i && $res < $intInterval * ($i + 1)) {
                    $arrEids[] = intval($value['eid'][$i]);
                }
            }
        }
        return $arrEids;
    }
    /**
     *
     * @brief: 将md5值进行hash，2取模
     * @param:
     * @return:
     *
     **/
    private static function _hash(){
        $strUniqid = uniqid(rand(), true);
        $hashvalue = md5($strUniqid);
        $strHash = substr($hashvalue, count($hashvalue) - 16, 16);
        $intHash = abs(intval(hexdec($strHash)));
        return $intHash%2;
    }
    /**
     *
     * @brief: 流量均分
     * @param:
     * @return:
     *
     **/
    private static function _splitFlow($arrInput) {
        $intHash = intval(self::_hash());
        if ($intHash == 0) {
            return array(intval($arrInput[0]));
        } else {
            return array(intval($arrInput[1]));
        }
    }
}
