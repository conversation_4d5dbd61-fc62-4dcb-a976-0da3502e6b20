<?php
/**
 * Created by Ph<PERSON>Storm.
 * User: fanyunyang
 * Date: 2015/4/23
 * Time: 18:10
 */
class Service_Model_SelfDspInterface
{
    const SERVER_NAME = 'adsense';
    const METHOD_NAME_GET_ADV = 'getAdvertiseInfo';
    const REDIS_ADVERT_KEY = 'adsense_getAdvertiseInfo_';
    const FORUM_VIRTUAL_ID = 5;

    /*
     * 1 普通链接
     * 2 IOS下载链接
     * 3 Android下载链接
     * 4 只读广告
     */
    protected static  $_arrSelf2CientUrlType = array(
        Service_Model_Conf_Const::URL_TYPE_NORMAL => 1,
        Service_Model_Conf_Const::URL_TYPE_IOS => 2,
        Service_Model_Conf_Const::URL_TYPE_ANDROID => 3,
        Service_Model_Conf_Const::URL_TYPE_IOS_NORMAL => 1,
        Service_Model_Conf_Const::URL_TYPE_ANDROID_NORMAL => 1,
        Service_Model_Conf_Const::URL_TYPE_IOS_READ_ONLY => 4,
        Service_Model_Conf_Const::URL_TYPE_ANDROID_READ_ONLY => 4,
    );

    /*
    protected static $_arrSortConf = array(
    		Service_Model_Dsp_Manager::BAICHUAN_CPID => array(
    				array(
    						"key" => "charge_mode",
    						"priority" => array("CPD","CPC"),
    				),
    		),
    );
    */
    /**
     * @brief
     * @params:
     * $arrInput
    'user_name'       按用户策略检索
    'user_id'         按用户id策略检索
    'forum_name'      按吧名检索
    'forum_id'        按吧ID检索
    'forum_dir'       按一级目录检索
    'forum_second_dir' 按二级目录检索
    'baiduid'			百度用户ID，在使用PC和WAP时必须传入
    'cuid'				客户端用户ID，在使用APP时必须传入
    'device_type'      1表示iphone，2表示android，只在APP时需要考虑
    'app_type’ 		1表示都出，2表示只出APP类广告，3表示只出GAME
    'client_type'  	请求的客户端类型  'PC', 'APP', 'WAP', 'SDK', 'MINIAPP'
    'page_name'  	请求页面的名称PB,FRS
    'dsp'          请求dsp参数 array(
    array('dsp_id'=>, 'dsp_name'=>, 'dsp_rn'=>
    )
     * @return: $arrOutput:
     **/
    public static function getSelfDspAdvInfo($arrInput) {

        if (empty($arrInput['rn'])) {
            $arrInput['rn'] = 30;
        }
        if (empty($arrInput['lastid'])) {
            $arrInput['lastid'] = 0;
        }
        $arrAdvInfo = self::_getSelfAdvInfo($arrInput,$arrInput['client_type'], $arrInput['page_name'], $arrInput['rn'], $arrInput['lastid']);
        if(empty($arrAdvInfo)) {
            //Bingo_Log::warning("_getSelfAdvInfo return empty." .serialize($arrInput));
            return false;
        }
        $arrDspAdvInfo = array();
        //check params dsp  dsp_id, dsp_name, dsp_rn
        if(!isset($arrInput['dsp']) ||  !is_array($arrInput['dsp'])) {
            Bingo_Log::warning("dsp params error!");
            //build default
            foreach($arrAdvInfo as $k => $advInfo) {
                $dsp_id = $advInfo['app_type'];
                if($dsp_id == 1) {
                    $dsp_id = 4;
                }
                $advInfo['cpid'] = $dsp_id;
                $arrDspAdvInfo[$dsp_id][] = $advInfo;
            }
        } else {
            foreach($arrInput['dsp'] as $k => $dspInfo) {
                $cacheDspInfo[$dspInfo['dsp_id']] = $dspInfo['dsp_rn'];
            }
            foreach($arrAdvInfo as $k => $advInfo) {
                $dsp_id = $advInfo['app_type'];
                if($dsp_id == 1) {
                    $dsp_id = 4;
                }
                if(isset($cacheDspInfo[$dsp_id]) && $cacheDspInfo[$dsp_id]>0) {
                    $advInfo['cpid'] = $dsp_id;
                    $arrDspAdvInfo[$dsp_id][] = $advInfo;
                    //		$cacheDspInfo[$dsp_id] = $cacheDspInfo[$dsp_id]-1;
                }
            }
        }
        if(empty($arrDspAdvInfo)) {
            Bingo_Log::warning("arrDspAdvInfo no advInfo");
            return false ;
        }
        
        //$arrDspAdvInfo = self::sort($arrDspAdvInfo);
        return $arrDspAdvInfo;
    }



    /**
     * @brief
     * @params:
     * $arrInput
    'user_name'       按用户策略检索
    'user_id'         按用户id策略检索
    'forum_name'      按吧名检索
    'forum_id'        按吧ID检索
    'forum_dir'       按一级目录检索
    'forum_second_dir' 按二级目录检索
    'baiduid'			百度用户ID，在使用PC和WAP时必须传入
    'cuid'				客户端用户ID，在使用APP时必须传入
    'device_type'      1表示iphone，2表示android，只在APP时需要考虑
    'app_type’ 		1表示都出，2表示只出APP类广告，3表示只出GAME
     * 	$client_type  请求的客户端类型  'PC', 'APP', 'WAP', 'SDK', 'MINIAPP'
     *  $page_name  请求页面的名称
     *           	PB,FRS
     * @return: $arrOutput:
     **/
    public static function _getSelfAdvInfo($arrInput,$client_type, $page_name, $rn = 30, $lastid = 0) {
        if (empty($arrInput) || empty($client_type) ||  empty($page_name) || $rn <= 0)
        {
            Bingo_Log::pushErrtag("error_param_for_getSelfAdvInfo");
            Bingo_Log::warning("input param error, client_type=$client_type, page_name=$page_name, rn=$rn".serialize($arrInput));
            return false;
        }
        if(in_array($arrInput['page_name'], Service_Model_Conf_Const::$arrForumRelatedPage) && empty($arrInput['forum_id'])) {
            return false;
        }

        $arrInput['client_type'] = $client_type;
        $arrInput['page_name'] = $page_name;
        $arrInput['rn'] = $rn;

        //获取广告id,去重复，去掉黑名单
        Bingo_Timer::start('adsense_getAdvIds');
        $arrIds = self::_getAdvIds($arrInput);
        $arrAdvIds = array_unique(array_merge($arrIds['forum'],$arrIds['user']));
        if(false == $arrAdvIds) {
            Bingo_Log::debug('_getAdvIds empty! :'.serialize($arrInput));
            return false;
        }
        Bingo_Timer::end('adsense_getAdvIds');

        //获取任务的有效性,ctr,
        Bingo_Timer::start('adsense_mgetAdvValid');
        $arrData = self::_mgetAdvValid($arrAdvIds, $arrInput);
        Bingo_Timer::end('adsense_mgetAdvValid');

        //过滤失效的广告id
        if(!empty($arrData['valid'])) {
            $arrVaildAdvIds = Service_Libs_Valid::getValidAdvs($arrData['valid']);
        }
        if(empty($arrVaildAdvIds)) {
            Bingo_Log::debug('arrVaildAdvIds empty!:'.serialize($arrData));
            return false;
        }
        // 过滤掉被用户关闭过的左侧浮层广告
        if (!empty($arrData['user_adv_mask'])) {
            $arrVaildAdvIds = Service_Libs_UserMask::filter($arrVaildAdvIds, $arrData['user_adv_mask']);
        }
        if (empty($arrVaildAdvIds)) {
            Bingo_Log::debug('arrVaildAdvIds empty!:'.serialize($arrData));
            return false;
        }
        //@TODO CTR排序变化
        $arrAdvIds = Service_Libs_Ctr::order($arrVaildAdvIds, $arrData['ctr']);

        //最多取 30条广告
        if(count($arrAdvIds) > 30) {
            $arrAdvIds = array_slice($arrAdvIds, 0, 30);
        }
        //tieba_call调用获取广告
        Bingo_Timer::start('adsense_getAdvCall');
        $arrAdvInfo = self::_getAdvCall($arrAdvIds);
        Bingo_Timer::end('adsense_getAdvCall');

        if(empty($arrAdvInfo)) {
            Bingo_Log::debug('_getAdvCall empty!:'.serialize($arrAdvIds));
            return false;
        }
        //去掉下载，点击超过的广告
        $arrAdvInfo = Service_Libs_Plan::checkClickDownload($arrAdvInfo,$arrData['download'], $arrData['click']);
        if(empty($arrAdvInfo)) {
            Bingo_Log::debug('checkClickDownload empty!:'.serialize($arrAdvIds));
            return false;
        }
        //过滤广告中同时有人吧维度的不符合条件的广告
        $arrAdvInfo = self::_filterErrorAdvert($arrAdvInfo,$arrIds);
        //过滤自有系统的ios andorid
        $arrAdvInfo = self::_filterAdvert($arrAdvInfo, $arrInput);
        //TODO WLJ
        //若请求为非三星机型，过滤三星订单
        /*
        $arrAdvInfo = self::_filterSamsungAdvert($arrAdvInfo, $arrInput);
        */
        return $arrAdvInfo;
    }

    /**
     * <AUTHOR>
     * @param 
     *      array arrAdvInfo:
     *      array arrInput:
     * @return
     * @desc 该请求不属于三星机型时，过滤广告列表中的三星广告订单
     */
    public static function _filterSamsungAdvert($arrAdvInfo, $arrInput){

        //参数判断
        $arrNewAdvInfo = array();
        if(empty($arrInput['client_type']) || empty($arrInput['mobile_model'])){
            foreach ($arrAdvInfo as $advInfo){
                if($advInfo['app_type'] == Service_Model_Dsp_Manager::SAMSUNG_CPID){
                    continue;
                }else{
                    $arrNewAdvInfo[] = $advInfo;
                }
            }
            return $arrNewAdvInfo;
        }

        $client_type = $arrInput['client_type'];
        $mobile_model = $arrInput['mobile_model'];
        
        //APP机型
        $arrSamsungType = array('GT-I9308I','GT-I9300I','SCH-I939I','GT-I9508V','GT-I9508','GT-I9507V',
            'GT-I9502','GT-I9500','SCH-I959','SM-G9008W','SM-G9008V','SM-G9006V','SM-G9006W','SM-G9009D',
            'SM-G9009W','SM-N9008V','SM-N9008','SM-N9002','SM-N9006','SM-N9009','SM-N9008S','SM-N7508V',
            'SM-N7506V','SM-N7509V','SM-N9100','SM-N9106W','SM-N9108V','SM-N9109W','SM-N9150','SM-W2014',
            'SM-W2015','SM-G9098','SM-G9092','GT-I9152P','GT-I9158P','SCH-P709E','GT-I9158V','SM-G7108V',
            'SM-G7108','SM-G7106','SM-G7109','GT-I9168','GT-I9168I','GT-I9082C','SCH-I879E','SM-G3818',
            'SM-G3812','SM-G3819D','GT-S7898I','GT-S7898','SM-G3508','SM-G3508I','SM-G3508J','SM-G3502',
            'SM-G3502I','SM-G3502U','SM-G3502C','SM-G3509','SM-G3509I','GT-S7278U','GT-S7278','SCH-I679',
            'GT-S7272C','SM-T2556','SM-T2558','SM-T2519','SM-G3858','SM-C1158','SM-C1116','SM-G3518',
            'SM-G3568V','SM-G3556D','SM-G3558','SM-G3559','SM-G3586V','SM-G3588V','SM-G3589W','SM-G8508S',
            'SM-G3139D','SM-G7508Q','SM-G7509','SM-G3606','SM-G3608','SM-G3609','SM-G5306W','SM-G5308W',
            'SM-G5309W','SM-G5108','SM-G5108Q','SM-G5109','SM-A3000','SM-A3009','SM-A5000','SM-A5009',
            'SM-A7000','SM-A7009','SM-G7200','SM-E7000','SM-E7009','SM-G9200','SM-G9208','SM-G9209',
            'SM-G9250','GT-I8552','GT-I9300','GT-I9308','GT-N7100','GT-N7102','GT-N7108','GT-S7568',
            'SCH-N719','SM-N900','SM-N9005',      
        );
       
        //对于APP或着WAP端非三星请求,则过滤三星广告ID
        $arrNewAdvInfo = array();
        foreach ($arrAdvInfo as $advInfo){
            if($advInfo['app_type'] == Service_Model_Dsp_Manager::SAMSUNG_CPID){
                if(('APP' == $client_type && in_array($mobile_model, $arrSamsungType))
                    ||('WAP' == $client_type && in_array($mobile_model,$arrSamsungType))){
                   $arrNewAdvInfo[] = $advInfo;
                }
            }else{
                $arrNewAdvInfo[] = $advInfo;
            }
        }
      
        return $arrNewAdvInfo;
    }
    
    
    /**
     * <AUTHOR>
     * @param
     *	array $advInfo : 广告信息
     *	array $arrInput : 广告参数
     * @return
     * @desc 过滤ios,andirod 广告
     **/
    public static function _filterAdvert($advInfo, $arrInput) {
        $arrAdvInfo = array();
        $time = time();
        foreach($advInfo as $key => $value) {

            $page_names = explode('-', $value['page_name']);
            if (!in_array($arrInput['page_name'], $page_names)) {
                continue;
            }
            if($value['valid'] == 0 || $value['status'] == 1 || $value['del'] == 1) {
                continue;
            }
            
            if(!empty($value['period'])){
            	$periods = explode(",", $value['period']);
            	$time_valid = false;
            	foreach($periods as $period){
            		list($start_time,$end_time) = explode("_", $period,2);
            		if($time >= $start_time && $time <= $end_time) {
            			$value['start_time'] = $start_time;
            			$value['end_time'] = $end_time;
            			$time_valid = true;
            			break;
            		}
            	}
            	if(!$time_valid){
            		continue;
            	}
            }else{
	            if($time < $value['start_time'] || $time > $value['end_time']) {
	                continue;
	            }
            }
            if($arrInput['client_type'] != $value['client_type']) {
                continue;
            }
            if(!empty($arrInput['device_type']) && ($arrInput['device_type'] == Service_Model_Conf_Const::DEVICE_TYPE_IOS || $arrInput['device_type'] == Service_Model_Conf_Const::DEVICE_TYPE_ANDROID)) {

                if($arrInput['device_type'] == Service_Model_Conf_Const::DEVICE_TYPE_IOS && ($value['url_type'] == Service_Model_Conf_Const::URL_TYPE_ANDROID || $value['url_type'] == Service_Model_Conf_Const::URL_TYPE_ANDROID_NORMAL)) {
                    //ios  对应的 apk
                    continue;
                }
                if($arrInput['device_type'] == Service_Model_Conf_Const::DEVICE_TYPE_ANDROID && ($value['url_type'] == Service_Model_Conf_Const::URL_TYPE_IOS || $value['url_type'] == Service_Model_Conf_Const::URL_TYPE_IOS_NORMAL)) {
                    //apk 对应 的ios
                    continue;
                }
                $value['url_type'] = self::$_arrSelf2CientUrlType[$value['url_type']];
            }
            Bingo_Timer::start('filter user');

            //用户维度filter
            if(Service_Model_Dsp_Manager::USER_CPID === intval($value['app_type'])){
                if(empty($arrInput['ueg_profile'])){
                    continue;
                }
                else{
                    $user_config = unserialize($value['user_config']);
                    if(!empty($user_config)){
                        //wangchao38
                        //广告属性中将game_client_type属性和game_type属性拼接,存储在game_type属性中
                        if(!empty($user_config['game_type']) && !empty($user_config['game_client_type'])){
                            $arr_game_type_temp = explode(',', $user_config['game_type']);
                            $arr_game_client_type_temp = explode(',', $user_config['game_client_type']);
                            $game_mixed = array();
                            foreach($arr_game_client_type_temp as $arrgctt) {
                                foreach($arr_game_type_temp as $arrgtt) {
                                    array_push($game_mixed, $arrgctt . '-' . $arrgtt);
                                }
                            }
                            $user_config['game_type'] = $game_mixed;
                        }
                        elseif(empty($user_config['game_client_type']) && !empty($user_config['game_type'])){
                            $arr_game_client_type_temp = array(
                                'pc_网页游戏',
                                'pc_单机游戏',
                                '移动端游戏',
                                'pc_客户端网络游戏',
                                '轻游戏',
                                '掌机游戏',
                            );
                            $arr_game_type_temp = explode(',', $user_config['game_type']);
                            $game_mixed = array();
                            foreach($arr_game_client_type_temp as $arrgctt) {
                                foreach($arr_game_type_temp as $arrgtt) {
                                    array_push($game_mixed, $arrgctt . '-' . $arrgtt);
                                }
                            }
                            $user_config['game_type'] = $game_mixed;
                        }
                        $intUserTag = 1; //标志位，为0则该条配置不命中，break
                        foreach($user_config as $userKey => $config){
                            //广告配置为不限
                            if(empty($config)){
                                continue;
                            }

                            //用户数据不全的不出广告,直接退出
                            if($userKey == 'game_client_type' || $userKey == 'game_type') {
                                $user_true = $arrInput['ueg_profile']['game'];
                            }else {
                                $user_true = $arrInput['ueg_profile'][$userKey];
                            }

                            if(empty($user_true)){
                                $intUserTag = 0;
                                break;
                            }
                            if(!is_array($config)){
                                $arrConf = explode(',',$config);
                            }
                            else{
                                $arrConf = $config;
                            }
                            //ueg的数据和pm提供的不一致，在此做映射
                            if(isset(Service_Model_Conf_User::$arrUserConfigMap[$userKey])){
                                $arrMap = Service_Model_Conf_User::$arrUserConfigMap[$userKey];
                                $intTemp = 0;
                                //常用时间是离散逗号分隔的，临时发现数据的问题，先改成数据解决
                                $arrUserTrue = explode(',',$user_true);
                                foreach ($arrConf as $conf){
                                    //并行条件，只要有一个命中，则此条件命中，$arrUserTrue最多三个元素
                                    foreach($arrUserTrue as $userTrueConf){
                                        if(($userTrueConf >= $arrMap[$conf][0])&&($userTrueConf < $arrMap[$conf][1])){
                                            $intTemp = 1;
                                            break;
                                        }
                                    }
                                    if(1 == $intTemp){
                                        break;
                                    }
                                }
                            }
                            //value继续可以对得上的，不需要映射的内容
                            else{
                                //电影key值对不上，需要特殊处理
                                $intTemp = 0;
                                if(Service_Model_Conf_User::USER_CONFIG_MOVIE == $userKey){
                                    $user_true = $user_true[Service_Model_Conf_User::USER_CONFIG_MOVIE_SECOND_key];
                                    //数组，只要有一个维度对得上，就此条件命中
                                    foreach ($arrConf as $movie){
                                        if(isset($user_true[$movie])){
                                            $intTemp = 1;
                                            break;
                                        }
                                    }
                                }
                                //地区类的数据
                                elseif (Service_Model_Conf_User::USER_CONFIG_LOC == $userKey){
                                    $arrLoc = explode('-',$user_true,2);

                                    $locMatch = false;
                                    foreach($arrConf as $k => $locInfo){
                                        $arrTmp = explode(',', $locInfo, 2);
                                        $province = $arrTmp[0];
                                        $city = $arrTmp[1];
                                        if($arrLoc[0] == $province){
                                            if(empty($city) || $arrLoc[1] == $city){
                                                $locMatch = true;
                                                break;
                                            }
                                        }
                                    }
                                    if($locMatch == true){
                                        $intTemp = 1;
                                    }

                                }
                                //wangchao38
                                //游戏端类型数据,游戏类型为空时进行判断
                                elseif (Service_Model_Conf_User::USER_CONFIG_GAME_CLIENT_TYPE == $userKey 
                                    && !empty($user_config['game_client_type'])
                                    && empty($user_config['game_type'])) {
                                    foreach($user_true as $user_true_single){
                                        $arrGameClientType = explode('-', $user_true_single, 2);
                                        if('None'===$arrGameClientType[1]){
                                            if(in_array($arrGameClientType[0], $arrConf)){
                                                $intTemp = 1;
                                                break;
                                            }
                                        }
                                    }        
                                }
                                //游戏端类型和游戏类型都为空时,直接判断游戏类型数据 
                                elseif (Service_Model_Conf_User::USER_CONFIG_GAME_CLIENT_TYPE == $userKey 
                                    && !empty($user_config['game_client_type'])
                                    && !empty($user_config['game_type'])) {
                                        continue;
                                }
                                //wangchao38
                                //游戏类型数据
                                elseif (Service_Model_Conf_User::USER_CONFIG_GAME_TYPE == $userKey) {
                                    foreach($user_true as $user_true_single){
                                        $arrGameType = explode('-', $user_true_single, 2);
                                        if('None'!==$arrGameClientType[1]){
                                            if(in_array($user_true_single, $arrConf)){
                                                $intTemp = 1;
                                                break;
                                            }
                                        }
                                    }    
                                }
                                //其余正常数据
                                else{
                                    if(!(in_array($user_true,$arrConf))){
                                        $intTemp = 0;
                                    }
                                    else{
                                        $intTemp = 1;
                                    }
                                }

                            }
                            //有一个条件不命中，就直接break，丢弃该条广告
                            if(!$intTemp){
                                $intUserTag = 0;
                                break;
                            }
                            else{
                                $intUserTag = 1;
                            }
                        }
                        if(0 == $intUserTag){
                            continue;
                        }
                    }
                }
            }
            Bingo_Timer::start('filter user');
            $arrAdvInfo[$key] = $value;
        }

        return $arrAdvInfo;
    }


    private static function _getAdvCall($advs) {

        $mdata = self::mgetAdvInfo($advs);

        $arrData = $mdata['data'];
        $nohitData = $mdata['nohit'];
        $arrRes = array();
        if(!empty($nohitData)) {
            Bingo_Log::debug ( "get advsense_call cache ho hit!.".serialize($nohitData) );
            $objMulti = new Tieba_Multi('advsense_call');
            foreach($nohitData as $key=>$value){
                $objMulti->register(
                    self::METHOD_NAME_GET_ADV.'_'.$value,
                    new Tieba_Service(self::SERVER_NAME),
                    array(
                        'serviceName'=>self::SERVER_NAME,
                        'ralServiceName'=>'service_adsense_read',
                        'method'=>self::METHOD_NAME_GET_ADV,
                        'input'=>array(
                            'id' => $value,
                        ),
                        'httpMethod' => 'post',
                        'format' => 'php',
                        'ie' => 'utf-8',
                    )
                );

            }
            $objMulti->call();
            foreach($nohitData as $key=>$value){
                $arrRet= $objMulti->getResult(self::METHOD_NAME_GET_ADV.'_'.$value);
                if($arrRet == false || Tieba_Errcode::ERR_SUCCESS!=$arrRet['errno']){
                    Bingo_Log::pushErrtag("rpc_call_".self::SERVER_NAME."_".self::METHOD_NAME_GET_ADV."_fail");
                    Bingo_Log::warning('get adv info fail input='.serialize($value).'output='.serialize($arrRet));
                }
                $arrRes[$value] = $arrRet['output'];
            }

        }
        $data = array_merge($arrData, $arrRes);
        return $data;
    }


    private static function mgetAdvInfo($advs) {

        if( null === ($redis = Service_Libs_Redis::_getRedis()) ){
            Bingo_Log::pushErrtag("init_redis_adsense_fail");
            Bingo_Log::warning("get redis fail.");
            return false;
        }
        $input = array();
        foreach($advs as $k => $id) {
            $key = self::REDIS_ADVERT_KEY.$id;
            $input['reqs'][] = array('key'=>$key);
        }
        $arrRet = $redis->GET($input);
        if($arrRet === false || $arrRet['err_no'] !== 0){
            Bingo_Log::pushErrtag("rpc_call_redis_adsense_GET_fail");
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $arrRes = array();
        $noRes = array();
        foreach($advs as $k => $id) {
            $key = self::REDIS_ADVERT_KEY.$id;
            if(!empty($arrRet['ret'][$key])) {
                $arrRes[$id] = unserialize($arrRet['ret'][$key]);
                $arrRes[$id]['goods_info'] = json_decode($arrRes[$id]['goods_info'], true);
            } else {
                $noRes[$id] = $id;
            }
        }
        return array('data'=>$arrRes,'nohit'=>$noRes);
    }




    private static function _getAdvIds($arrInput) {

        Bingo_Timer::start('adsense_get_adv_ids');

        //定义一级目录下最热吧最多2个(1,2) 0表示没命中
        $intMemberN = 0;
        //查看是否在最热吧中
       /* Bingo_Timer::start('adsense_isHotForumMember');
        $intMemberN = self::_isHotForumMember($arrInput);
        Bingo_Timer::end('adsense_isHotForumMember');*/

        // 首页广告没有热吧逻辑
        /*if($arrInput['page_name'] != 'INDEX' && $intMemberN === false) {
            Bingo_Log::pushErrtag("isHotForumMember_return_false");
            Bingo_Log::warning("Controller isHotForumMember error!");
            return false;
        }*/
        $arrInput['memberN'] = $intMemberN;
        //批量获取吧，一级目录，二级目录，全吧投放，黑名单，最热吧的adv_id
        Bingo_Timer::start('adsense_mgetAdvids');
        $arrAdvList = self::_mgetAdvIds($arrInput);
        Bingo_Timer::end('adsense_mgetAdvids');
        //如果空则直接返回空数组
        if(empty($arrAdvList)) {
            Bingo_Log::debug('_mgetAdvIds empty!:'.serialize($arrInput));
            return false;
        }
        //归并Adv_id
        $arrAdvIds = Service_Libs_Adv::mergeAdvId($arrAdvList);
        //归并人维度
        $arrUserAdvIds = $arrAdvList['user_config'];

        //去掉黑名单中的advid
        $arrAdvIds = Service_Libs_Black::removeAdvId($arrAdvList['black'],$arrAdvIds);
        $arrUserAdvIds = Service_Libs_Black::removeAdvId($arrAdvList['black'],$arrUserAdvIds);
        if((empty($arrAdvIds))&&(empty($arrUserAdvIds))) {
            Bingo_Log::debug('removeAdvId empty!:'.serialize($arrAdvList['black']));
            return false;
        }
        $arrIds = array(
            'forum' => $arrAdvIds,
            'user'  => $arrUserAdvIds,
        );
        Bingo_Timer::end('adsense_get_adv_ids');


        return $arrIds;

    }


    private static function _isHotForumMember($arrInput) {

        if(empty($arrInput['forum_dir'])) {
            return 0;
        }

        if( null === ($redis = Service_Libs_Redis::_getRedis()) ){
            Bingo_Log::pushErrtag("init_redis_adsense_fail");
            Bingo_Log::warning("get redis fail.");
            return false;
        }
        $intHotnum = 2;
        $intN = 0;
        for($i = 1; $i <= $intHotnum; $i++) {
            $key = Service_Libs_BuildKey::buildHotKey($arrInput['forum_dir'], $i);
            $input = array(
                'key' => $key,
                'member' => $arrInput['forum_id'],
            );
            Bingo_Timer::start('adsense_sismember'.$i);
            $arrRet = $redis->SISMEMBER($input);
            Bingo_Timer::end('adsense_sismember'.$i);
            if($arrRet === false || $arrRet['err_no'] !== 0){
                Bingo_Log::pushErrtag("rpc_call_redis_adsense_SISMEMBER_fail");
                Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
                return false;
            }
            if($arrRet['ret'][$key] == 1) {
                $intN = $i;
                break;
            }
        }
        return $intN;
    }

        private static  function _mgetAdvIds($arrInput) {

        if( null === ($redis = Service_Libs_Redis::_getRedis()) ){
            Bingo_Log::pushErrtag("init_redis_adsense_fail");
            Bingo_Log::warning("get redis fail.");
            return false;
        }

        //获取端设备
        $arrInput['device'] = Service_Model_Conf_Base::getDevice($arrInput['client_type'], $arrInput['device_type']);
/*
        if($arrInput['memberN'] ==1 || $arrInput['memberN'] ==2) {
            $key = Service_Libs_BuildKey::buildHotKey($arrInput['forum_dir'], $arrInput['memberN']);
            $key_hot = Service_Libs_BuildKey::buildHotForumKey($key, $arrInput['device']);

        }
*/


        if(isset($arrInput['forum_vdir'][self::FORUM_VIRTUAL_ID])) {
            if(!empty($arrInput['forum_vdir'][self::FORUM_VIRTUAL_ID][0])) {
                $virtual_dirs = $arrInput['forum_vdir'][self::FORUM_VIRTUAL_ID][0];
                $key_virtual = Service_Libs_BuildKey::buildVirtualKey( $virtual_dirs, $arrInput['device'], $arrInput['page_name']);
            }
        }

        $key_global = Service_Libs_BuildKey::buildGlobalKey($arrInput['device'], $arrInput['page_name']);
        $key_page = Service_Libs_BuildKey::buildPageKey($arrInput['device'], $arrInput['page_name']);
        $key_first = Service_Libs_BuildKey::buildFirstKey($arrInput['forum_dir'],  $arrInput['device'], $arrInput['page_name']);
        $key_second = Service_Libs_BuildKey::buildSecondKey( $arrInput['forum_second_dir'], $arrInput['device'], $arrInput['page_name']);
        $key_forum = Service_Libs_BuildKey::buildForumKey( $arrInput['forum_id'], $arrInput['device'], $arrInput['page_name']);
        $key_black = Service_Libs_BuildKey::buildBlackKey($arrInput['forum_id']);
        //
        $input = array();

        if(!empty($arrInput['forum_id'])) {
            $input['reqs'][] = array(
                'key'=>$key_forum,
            );
            $input['reqs'][] = array(
                'key'=>$key_black,
            );
        }

        if (!in_array($arrInput['page_name'], Service_Model_Conf_Const::$arrForumRelatedPage)) {
            // 独立页面广告
            $input['reqs'][] = array(
                'key' => $key_page,
            );
        } else {
            // 全局广告
            $input['reqs'][] = array(
                'key' => $key_global,
            );
        }
        if(!empty($arrInput['forum_dir'])) {
            $input['reqs'][] = array(
                'key'=>$key_first,
            );
        }
        if(!empty($arrInput['forum_second_dir'])) {
            $input['reqs'][] = array(
                'key'=>$key_second,
            );
        }
        if(!empty($virtual_dirs)) {
            $input['reqs'][] = array(
                'key'=>$key_virtual,
            );
        }

     /*   if($arrInput['memberN'] ==1 || $arrInput['memberN'] ==2) {
            $input['reqs'][] = array(
                'key'=>$key_hot,
            );
        }*/
        //人维度
        if(!empty($arrInput['ueg_profile'])){
            $key_user = Service_Libs_BuildKey::buildUserKey($arrInput['device'], $arrInput['page_name']);
            $input['reqs'][] = array(
                'key'=>$key_user,
            );
        }

        $arrRet = $redis->GET($input);
        if($arrRet === false || $arrRet['err_no'] !== 0){
            Bingo_Log::pushErrtag("rpc_call_redis_adsense_GET_fail");
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = array();
        $data['first'] =  empty($arrRet['ret'][$key_first]) ? array():explode(',',$arrRet['ret'][$key_first]);
        $data['second'] =  empty($arrRet['ret'][$key_second]) ? array():explode(',',$arrRet['ret'][$key_second]);
        $data['virtual'] =  empty($arrRet['ret'][$key_virtual]) ? array():explode(',',$arrRet['ret'][$key_virtual]);
        $data['forum'] = empty($arrRet['ret'][$key_forum]) ? array():explode(',',$arrRet['ret'][$key_forum]);
        $data['black'] =  empty($arrRet['ret'][$key_black]) ? array():explode(',',$arrRet['ret'][$key_black]);
        //$data['hot'] =  empty($arrRet['ret'][$key_hot]) ? array():explode(',',$arrRet['ret'][$key_hot]);
        $data['global'] = empty($arrRet['ret'][$key_global]) ? array():explode(',',$arrRet['ret'][$key_global]);
        $data['page'] = empty($arrRet['ret'][$key_page]) ? array():explode(',',$arrRet['ret'][$key_page]);
        $data['user_config'] = empty($arrRet['ret'][$key_user])?array():explode(',',$arrRet['ret'][$key_user]);
        return $data;

    }


    private  static function _mgetAdvValid($arrAdvs, $arrInput) {

        if( null === ($redis = Service_Libs_Redis::_getRedis()) ){
            Bingo_Log::pushErrtag("init_redis_adsense_fail");
            Bingo_Log::warning("get redis fail.");
            return false;
        }
        $input = array();
        foreach($arrAdvs as $key => $value) {

            $key_valid = Service_Libs_BuildKey::buildValidKey($value);
            $key_download = Service_Libs_BuildKey::buildDownloadKey($value);
            $key_click = Service_Libs_BuildKey::buildClickKey($value);
            $key_ctr = Service_Libs_BuildKey::buildCtrKey($value,$arrInput['forum_id']);

            $input['reqs'][] = array('key'=>$key_valid);
            $input['reqs'][] = array('key'=>$key_download);
            $input['reqs'][] = array('key'=>$key_click);
            $input['reqs'][] = array('key'=>$key_ctr);
        }

        // 过滤掉用户已经关闭的广告
        $key_user_adv_mask = Service_Libs_BuildKey::buildUserAdvMaskKey($arrInput['user_id']);
        if (!empty($arrInput['user_id'])) {
            $input['reqs'][] = array('key' => $key_user_adv_mask);
        }
        Bingo_Timer::start('adsense_mgetAdvValid_get');
        $arrRet = $redis->GET($input);
        Bingo_Timer::end('adsense_mgetAdvValid_get');
        if($arrRet === false || $arrRet['err_no'] !== 0){
            Bingo_Log::pushErrtag("rpc_call_redis_adsense_GET_fail");
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = array();
        foreach($arrAdvs as $key => $value) {

            $key_valid = Service_Libs_BuildKey::buildValidKey($value);
            $key_download = Service_Libs_BuildKey::buildDownloadKey($value);
            $key_click = Service_Libs_BuildKey::buildClickKey($value);
            $key_ctr = Service_Libs_BuildKey::buildCtrKey($value,$arrInput['forum_id']);


            if(!empty($arrRet['ret'][$key_valid])) {
                $data['valid'][$value] = $arrRet['ret'][$key_valid];
            }
            if(!empty($arrRet['ret'][$key_download])) {
                $data['download'][$value] = $arrRet['ret'][$key_download];
            }
            if(!empty($arrRet['ret'][$key_click])) {
                $data['click'][$value] = $arrRet['ret'][$key_click];
            }
            if(!empty($arrRet['ret'][$key_ctr])) {
                $data['ctr'][$value] = $arrRet['ret'][$key_ctr];
            }
        }

        if (!empty($arrInput['user_id']) && !empty($arrRet['ret'][$key_user_adv_mask])) {
            $arrUserAdvMask = explode(',', $arrRet['ret'][$key_user_adv_mask]);
            if (!empty($arrUserAdvMask)) {
                $data['user_adv_mask'] = $arrUserAdvMask;
            }
        }
        return $data;
    }


    /*
     *@param
     * array $arrAdvInfo :广告信息
     * array $arrIds: 原始广告id数组
     * array $arrAdvInfo:最终筛选出来的id
     * @return
     * @desc 将返回的广告中，不满足人、吧维度的广告过滤
     */
    public static function _filterErrorAdvert($arrAdvInfo,$arrIds){
        $arrIdsSome = array_intersect($arrIds['user'],$arrIds['forum']);
        foreach ($arrAdvInfo as $key=>$value){
            //过滤没有同时满足人、吧维度的广告
            if(($value['throw_type'] == Service_Model_Conf_Const::THROW_TYPE_FORUM_AND_USER)
                &&(!in_array($value['id'],$arrIdsSome))){
                unset($arrAdvInfo[$key]);
            }
        }
        return $arrAdvInfo;
    }
    /*
    public static function sort($arrDspAdvInfo){
    	$arrRetDspAdvInfo = array(); 
    	foreach($arrDspAdvInfo as $intDspId => $arrAdvs){
    		$arrHoldAdvs = $arrAdvs;
    		if(isset(self::$_arrSortConf[$intDspId])){
    			foreach(self::$_arrSortConf[$intDspId] as $arrRule){
    				$arrHoldAdvs = self::sortField($arrHoldAdvs, $arrRule);
    			}
    		}
    		$arrRetDspAdvInfo[$intDspId] = $arrHoldAdvs;
    	}
    	return $arrRetDspAdvInfo;
    }
    
    public static function sortField($arrAdvInfo,$arrRule){
    	$arrAdvPoll = array();
    	foreach($arrAdvInfo as $arrAdv){
    		if(!isset($arrAdv[$arrRule['key']])){
    			continue;
    		}
    		$arrAdvPoll[$arrAdv[$arrRule['key']]][] = $arrAdv;
    	}
    	
    	if(isset($arrRule['priority'])){
	    	foreach($arrRule['priority'] as $strPriority){
	    		if(!empty($arrAdvPoll[$strPriority])){
	    			return $arrAdvPoll[$strPriority];
	    		}
	    	}
    	}
    	return $arrAdvInfo;
    }
    */
}
