<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-09-27 12:04:30
 * @version
 */

 /*
    ע�������漰��rd��fe��ģ�����������ǰ�Ѿ�Э�̺�ͳһģ�������綼�� group����ô����Ͳ�Ҫ���ڶ���������Ĭ�ϼ��ɣ�
        Tieba_Init::init("group");
    �����ûЭ�̣�����rd��ģ������ group��fe��ģ������ group_fe����ô�����Ӧ���ǣ�fe���ģ�������� ROOT_PATH/template/ �µ��ļ�������
        Tieba_Init::init("group","group_fe");
    ͬ��Ҳ�����Զ���ompģ������Ĭ��ͬ��ʹ�� group��
        Tieba_Init::init("group",null,"group_omp");
 */

 Tieba_Init::init("group","groups");

 $objFrontController = Bingo_Controller_Front::getInstance(array(
    "actionDir" => MODULE_ACTION_PATH,
    "defaultRouter" => "index",
    "notFoundRouter" => "error",
    "beginRouterIndex" => 1
 ));

 Bingo_Timer::start('total');
 Bingo_Page::init(array(
    "baseDir" => MODULE_VIEW_PATH,
    "debug" => false,
    "outputType" => ".",
    "isXssSafe" => true,
    "module" => "groups",
    "useTbView" => true,
    "viewRootpath" => MODULE_VIEW_PATH . "/../",
    "catchPath" => "../../data/app/group",
 ));

 try{
    $objFrontController->dispatch();
 }catch(Exception $e){
    //������ֱ��ת�򵽴���ҳ��
    Bingo_Log::warning(sprintf('main process failure!HttpRouter=%s,error[%s]file[%s]line[%s]',
        Bingo_Http_Request::getStrHttpRouter(), $e->getMessage(), $e->getFile(), $e->getLine()));
    Bingo_Page::setErrno($e->getCode());
    header('location:http://static.tieba.baidu.com/tb/error.html');
 }

//ģ����Ⱦ
Bingo_Timer::start('build_page');
Bingo_Page::buildPage();
Bingo_Timer::end('build_page');
Bingo_Timer::end('total');

$strTimeLog	= Bingo_Timer::toString();
if (! empty($strTimeLog)) {
	$strTimeLog = ' Timer[' . $strTimeLog .']';
}
Tieba_Stlog::setFileName('feye-stat');
Tieba_Stlog::addNode('mid', 'grouppc');
Tieba_Stlog::addNode('urlkey', 'grouppc_'.Bingo_Controller_Front::getInstance()->getDispatchRouter());
 
Bingo_Log::notice(Tieba_Stlog::notice() . $strTimeLog);      

?>