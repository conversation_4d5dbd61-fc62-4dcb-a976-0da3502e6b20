<?php
/**
 * Created by PhpStorm.
 * User: wangjunsheng
 * Date: 2016/11/15
 * Time: 15:16
 */


class uncollectTopicAction extends Libs_Client_BaseAction
{
    private static $_errMsg = array(
        '110000' => '请先登录',
        '110003' => '服务异常，请稍候重试',
        '3160007' => '已取消关注该话题',
    );

    private $owner_id = null;
    private $user_id = null;
    /**
     * @param
     * @return
     **/
    protected function _getPrivateInfo()
    {
        $arrPrivateInfo = array();
        $arrPrivateInfo['ispv'] = 1;

        $arrPrivateInfo['topic_id']        = intval($this->_getInput('topic_id', 0));
        $arrPrivateInfo['need_nt_userinfo'] = true;
        $arrPrivateInfo['check_login'] = true;
        $arrPrivateInfo['need_login'] = true;
        $this->_objRequest->addStrategy('check_sign', false);
        return $arrPrivateInfo;
    }

    protected function _add_log()
    {
        $intObjParam1 = 0;
        if($this->user_id > 0 && $this->owner_id > 0){
            $arrParam = array(
                'from_uid' => $this->user_id,
                'to_uid' => $this->owner_id,
            );
            $arrRet = Tieba_Service::call('ntuser', 'getUserRelation', $arrParam, null, null, 'post', 'php', 'utf-8');
            if(false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] || empty($arrRet['data']['user_relation'])){
                Bingo_Log::warning('service ntuser.getUserRelation error. [input='.serialize($arrParam).'][out='.serialize($arrRet).']');
            }
            else{
                if(intval($arrRet['data']['user_relation']) == 1){
                    $intObjParam1 = 1;
                }
            }
        }
        Tieba_Stlog::addNode('obj_param1', $intObjParam1);
    }

    /**
     * @brief  check private info
     * @return array
     */
    protected function _checkPrivate()
    {
        return true;
    }
    /**
     * @param:null
     * @return:null
     **/
    public function _execute(){
        try{
            // 参数检查
            $user_id  = intval($this->_objRequest->getCommonAttr('user_id'));
            $topic_id = intval($this->_objRequest->getPrivateAttr('topic_id'));
            $this->user_id = $user_id;
            if( $user_id <= 0 ){

                throw new Libs_Exception('not login!', Tieba_Errcode::ERR_USER_NOT_LOGIN);
            }
            if( $topic_id <= 0 ){

                throw new Libs_Exception('params error!', Tieba_Errcode::ERR_PARAM_ERROR);
            }

            //service call
            $arrParams = array(
                'user_id' => $user_id,
                'topic_id' => $topic_id,
            );
            $arrRet = Tieba_Service::call('ntuser', 'uncollectTopic', $arrParams, null, null, 'post', 'php', 'utf-8');
            if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                Bingo_Log::warning('service ntuser.uncollectTopic error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                $arrRet['errno'] = isset($arrRet['errno']) ? $arrRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
                throw new Libs_Exception($arrRet['errmsg'], $arrRet['errno']);
            }
            $arrParams = array(
                'topic_id' => $topic_id,
            );
            $arrRetTopic = Tieba_Service::call('nttopic', 'getTopicInfo', $arrParams, null, null, 'post', 'php', 'utf-8');
            if( Tieba_Errcode::ERR_SUCCESS == $arrRetTopic['errno'] ){
                $strTagIds = '';
                $this->owner_id = intval($arrRetTopic['data'][$topic_id]['user_id']);
                foreach( $arrRetTopic['data'][$topic_id]['tags'] as $key => $item ){
                    if(intval($item['tag_id'])){
                        if( empty($strTagIds)){
                            $strTagIds = $item['tag_id'];
                        }else{
                            $strTagIds = $strTagIds.'_'.$item['tag_id'];
                        }

                        //$objResponse->addLog('agent', $hid);
                    }
                }
                if( !empty($strTagIds)){
                    Tieba_Stlog::addNode('tag_id', $strTagIds);
                }
            }
            $arrRet['data'] = isset($arrRet['data']) ? $arrRet['data'] : array();
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, 'success', $arrRet['data']);
        }catch(Libs_Exception $e){
            $errno = $e->getCode();
            $errmsg = $e->getMessage();
            $errmsg = isset(self::$_errMsg[$errno]) ? self::$_errMsg[$errno] : '服务异常，请稍候重试';
            Bingo_Log::warning('errno='.$e->getCode().' msg='.$e->getMessage());
            $this->_jsonRet($e->getCode(), $errmsg);
        }
    }

}