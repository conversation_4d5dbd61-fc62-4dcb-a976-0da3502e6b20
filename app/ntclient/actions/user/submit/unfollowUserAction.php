<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file unfollowUserAction.php
 * <AUTHOR>
 * @date 2016-07-22
 * @brief 
 *  
 **/
class unfollowUserAction extends Libs_Client_BaseAction
{   
    private static $_errMsg = array(
        '110000' => '请先登录',
        '110003' => '服务异常，请稍候重试',
        '2500108' => '取消关注的用户不存在',
    	'3160007' => '已取消关注该用户',
    ); 
    /**
     * @param
     * @return
     **/
    protected function _getPrivateInfo()
    {
        $arrPrivateInfo = array();
        $arrPrivateInfo['ispv'] = 1;

        $arrPrivateInfo['to_uid']        = intval($this->_getInput('to_uid', 0));
        $arrPrivateInfo['need_nt_userinfo'] = true;
        $arrPrivateInfo['check_login'] = true;
        $arrPrivateInfo['need_login'] = true;
        $this->_objRequest->addStrategy('check_sign', false);
        return $arrPrivateInfo;
    }

    /**
     * @brief  check private info
     * @return array
     */
    protected function _checkPrivate()
    {
        return true;
    }
    /**
     * @param:null
     * @return:null
     **/
    public function _execute(){
        try{
  
            $from_uid = intval($this->_objRequest->getCommonAttr('user_id'));
            $to_uid = intval($this->_objRequest->getPrivateAttr('to_uid'));
            
            //Bingo_Log::pushNotice("ispv", 1);
            
        	if( $from_uid <= 0 ){
            
            	throw new Libs_Exception('not login!', Tieba_Errcode::ERR_USER_NOT_LOGIN);
            }
            if( $to_uid <= 0 ){
            
            	throw new Libs_Exception('params error!', Tieba_Errcode::ERR_PARAM_ERROR);
            }
            //验证用户id
            $arrParam = array(
                'user_id' => $to_uid,
            );
            $arrRet = Tieba_Service::call('ntuser', 'getUserInfo', $arrParam, null, null, 'post', 'php', 'utf-8');
            if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                Bingo_Log::warning('service ntuser.getUserInfo error. [input='.serialize($arrParam).'][out='.serialize($arrRet).']');
                //$arrRet['errno'] = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
                throw new Libs_Exception($arrRet['errmsg'], $arrRet['errno']);
            }
            if( empty($arrRet['data']['user_info']) ){
                throw new Libs_Exception('user is not exist!', Tieba_Errcode::ERR_VERTICAL_USER_NOT_EXIST);            
            }
            //service call
            $arrParams = array(
                'from_uid' => $from_uid,
                'to_uid' => $to_uid,
            );
            $arrRet = Tieba_Service::call('ntuser', 'unfollowUser', $arrParams, null, null, 'post', 'php', 'utf-8');
            if( false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] ){
                Bingo_Log::warning('service ntuser.unfollowUser error. [input='.serialize($arrParams).'][out='.serialize($arrRet).']');
                $arrRet['errno'] = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
                throw new Libs_Exception($arrRet['errmsg'], $arrRet['errno']);
            }
            
            $arrRet['data'] = isset($arrRet['data']) ? $arrRet['data'] : array();
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, 'success', $arrRet['data']);
        }catch(Libs_Exception $e){
            $errno = $e->getCode();
            $errmsg = $e->getMessage();
            $errmsg = isset(self::$_errMsg[$errno]) ? self::$_errMsg[$errno] : '服务异常，请稍候重试';
            Bingo_Log::warning('errno='.$e->getCode().' msg='.$e->getMessage());
            $this->_jsonRet($e->getCode(), $errmsg);
        }
    }
    
}

