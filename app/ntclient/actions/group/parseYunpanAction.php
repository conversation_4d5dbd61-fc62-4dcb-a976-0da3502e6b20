<?php
/**
 * @file parseYunpanAction.php
 * <AUTHOR>
 * @date 2016/07/28 16:28:53
 * @brief
 *
 **/

class parseYunpanAction extends Bingo_Action_Abstract {

    /**
     * @brief  main stream
     * @return bool
     */
    public function execute()
    {
        // 参数检查
        $strUrl = Bingo_Http_Request::get('url', '');
        if (empty($strUrl)) {
            Bingo_Log::warning(sprintf("param error. missing the url param."));
            $this->jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, '请输入云盘链接');
            return false;
        }

        if (!preg_match('/^(http|https):\/\/(yun\.baidu\.com\/share\/link|pan\.baidu\.com|yunpan\.cn).*/', $strUrl)) {
            Bingo_Log::warning(sprintf("param error. the url format is not correct."));
            $this->jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, '暂只支持百度云盘和360云盘哦~');
            return false;
        }

        // 创建Orp_FetchUrl的实例
        //$strUserAgent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'Orp_FetchUrl v1.0';
        $strUserAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.82 Safari/537.36';
        $strRefer = isset($_SERVER['HTTP_HOST']) ? "http://" . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] : "";
        $arrOption = array(
            'follow_location'   => true,
            'max_redirs'        => 3,
            'conn_retry'        => 3,
            'conn_timeout'      => 1000,
            'timeout'           => 3000,
            'user_agent'        => $strUserAgent,
            'referer'           => $strRefer,
            'encoding'          => '',
            'max_response_size' => 10485760, //10M
        );
        $objFetch = Orp_FetchUrl::getInstance($arrOption);
        $arrCookie = array(
            //'BAIDUID' => $_COOKIE['BAIDUID'],
            'BAIDUID' => '9FAA7E74853C8D0F0D938E9D39941485:FG=1',
        );
        $arrData = array(
            'thumbnail' => 'http://tb1.bdstatic.com/tb/r/image/2016-08-05/ce0ef3ac51e014328eb77de1d7ca39b5.png',
            'title'     => '云盘资源',
        );

        $strContent = $objFetch->get($strUrl, array(), $arrCookie);
        if (empty($strContent)) {
            Bingo_Log::warning("fetch url failed: {$strUrl}");
            $this->jsonRet(Tieba_Errcode::ERR_SUCCESS, 'success', $arrData);
            return false;
        } else {
            // 抓取缩略图
            /*if (preg_match("/yunData\.FILEINFO.*;/", $strContent, $match)) {
                $info = substr($match[0], 18, -1);
                $arrInfo = json_decode($info, true);
                if (!empty($arrInfo[0]['thumbs']['url3'])) {
                    $arrData['thumbnail'] = $arrInfo[0]['thumbs']['url3'];
                }
            }*/
            // 抓取title
            if (preg_match("/yunData\.FILENAME.*;/", $strContent, $match)) {
                $info = substr($match[0], 20, -2);
                if (!empty($info)) {
                    $arrData['title'] = $info;
                }
            }
            $this->jsonRet(Tieba_Errcode::ERR_SUCCESS, 'success', $arrData);
            return true;
        }
    }

    /**
     * @brief 返回json数据
     * @param $intErrno
     * @param $strErrMsg
     * @param $arrData
     */
    private function jsonRet($intErrno, $strErrMsg, $arrData = array())
    {
        $arrReturnData = array(
            'errno'    => $intErrno,
            'errmsg' => $strErrMsg,
            'data'  => $arrData,
        );
        echo json_encode($arrReturnData);
    }
}