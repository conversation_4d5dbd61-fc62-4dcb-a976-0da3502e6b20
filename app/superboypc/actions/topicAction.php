<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-07-16 13:22:17
 * @version
 */
 // demo ģ��
class topicAction extends Bingo_Action_Abstract {

    public function execute(){
		
    	Tieba_Stlog::addNode("superboyaction", "topic");
    	
		$conf = Util_Conf::getUiConf();
		if($conf["close_superboypc"] == true)
		{
			Bingo_Page::assign('no',"80000");
            Bingo_Page::assign('error',"��ѹرգ�");
			Bingo_Page::setOnlyDataType("json");
            return false;
		}

		$output = array();
        $pn = (int)Bingo_Http_Request::get("pn",1);
        $forum_id = (int)Bingo_Http_Request::get("forum_id",0);
		
		 $uiConf = Util_Conf::getUiConf();

        $serviceInput = array(
            'join_type' => 4,
			'ie' => "utf-8",
			"format"=>"json"
        );
	
        $ret = Tieba_Service::call('superboy','getRankinglist',$serviceInput);
		
        if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']){
            Bingo_Log::warning('call service thread::getList failed,errno[' . $ret['errno'] . '],errmsg[' . $ret['errmsg'] . ']');
        }	

		
		if($ret["ret"])
		{
			$data = array();
			foreach($ret["ret"] as $result)
			{
				/*
				$topdata = array();
				foreach($result["players"] as $item_key =>  $res_item)
				{
					$result["players"][$item_key]["id"] =   $res_item["player_id"];
					$result["players"][$item_key]["vote_count"] =   $res_item["vote_number"];
					
				}
				*/

				foreach($result["players"] as $key1 => $value1)
				{
					if($value1["vote_number"] > 9999999)
					{
						$result["players"][$key1]["vote_number"] = intval($value1["vote_number"]/10000)."��";
						
					}
					
				}
				$data["list{$result["ranking_type"]}"] = $result["players"];
			}

		
		}
		
       
		$output["data"] = $data;
		$output["errno"] = 0;
		$output["errmsg"] = "�ɹ�";
		

		
		Bingo_Page::assign('no',$output['errno']);
		Bingo_Page::assign('error',$output['errmsg']);
		Bingo_Page::assign('data',$output["data"]);
		
        // json���
		Bingo_Page::setOnlyDataType("json");
       
		




    }


}
?>