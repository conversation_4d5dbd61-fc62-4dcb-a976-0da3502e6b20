<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-07-16 13:22:17
 * @version
 */
 // demo ģ��
class frsdataAction extends Bingo_Action_Abstract {

    public function execute(){
		
        //void getMixInfoByForumId(uint32_t forum_id, uint32_t forum_type, out mix_info infos[]);

		$output = array();
        $pn = (int)Bingo_Http_Request::get("pn",1);
        $forum_id = (int)Bingo_Http_Request::get("forum_id",0);
		
		echo "<pre>";

        $serviceInput = array(
            'forum_id' => 5119862,
			"forum_type"=>1
        );

        $ret = Tieba_Service::call('superboy','getMixInfoByForumId',$serviceInput);
        if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']){
            Bingo_Log::warning('call service thread::getList failed,errno[' . $ret['errno'] . '],errmsg[' . $ret['errmsg'] . ']');
        }	

		
		$data["superboy_player_rank_info"] = $ret["ret"]["player_rank_info"];
		$data["superboy_pk_info"] = $ret["ret"]["pk_info"];
		$serviceInput = array(
            'join_type' => 2
        );

        $ret = Tieba_Service::call('superboy','getRankinglist',$serviceInput);
		if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']){
            Bingo_Log::warning('call service thread::getList failed,errno[' . $ret['errno'] . '],errmsg[' . $ret['errmsg'] . ']');
        }	
		
        $data["superboy_rank_list"] = $ret["ret"][0]["players"];
		print_r($data);

		$output["data"] = $data;
		$output["errno"] = 0;
		$output["errmsg"] = "�ɹ�";

    }


}
?>