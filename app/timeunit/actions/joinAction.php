<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file joinAction.php
 * <AUTHOR>
 * @date 2013/05/26 11:10:27
 * @brief 
 *  
 *  
 **/

class joinAction extends Bingo_Action_Abstract { 

const EXIT_SUCESS = 0;

const BAIDUID_KEY = 0;
const IP_KEY = 1;
const UID_KEY = 2;

const BAIDUID_CMD_NO =3827;
const IP_CMD_NO =3828;

const TOO_MANY_TIMES = 1;

public static $cmd_no ;  //3827��ʾͨ��baiduid���ȿ��ƣ�3828��ʾͨ��ip���ȿ���

public function execute()
{

    if( false === self::checkActsCtrl()){
        $arrOutput = array(
            'errno' => self::TOO_MANY_TIMES,
            'errormsg' => 'too many times.',
        );
        echo Bingo_String::array2json($arrOutput);
        return ;
    }

    $arrInput = array( 'taskid' => 0 );
    $arrInput['taskid'] = intVal(Bingo_Http_Request::get('taskid', 0));

    if( !isset($arrInput['taskid']) ){
        Bingo_Log::warning('get param error');
        $error = Tieba_Errcode::ERR_PARAM_ERROR;
        $arrOutput = array(
            'errno' => $error,
            'errormsg' => 'get param error',
        );
        echo Bingo_String::array2json($arrOutput);
        return true;
    }

    $arrOutput = Tieba_Service::call("timeunit", "addCount", $arrInput);
    if($arrOutput===false)
    {
    	Bingo_Log::warning('Tieba_Service_call_timeunit_addCount failed output['.serialize($arrOutput).'] input['.serialize($arrInput).']');
        
    	$error = Tieba_Errcode::ERR_PARAM_ERROR;
        $arrOutput = array(
            'errno' => $error,
            'errormsg' => 'Tieba_Service_call_timeunit_addCount failed',
        );
        echo Bingo_String::array2json($arrOutput);
        return true;
    }
    
    if( self::EXIT_SUCESS != $arrOutput['errno'] ){
        Bingo_Log::warning('call_addCount_failed_output['.serialize($arrOutput).'] input['.serialize($arrInput).']');
        $error = Tieba_Errcode::ERR_PARAM_ERROR;
        $arrOutput = array(
            'errno' => $error,
            'errormsg' => 'call addCount failed',
        );
        echo Bingo_String::array2json($arrOutput);
        return true;
    }
     Tieba_Stlog::addNode('pro', 'tieba');
   	 Tieba_Stlog::addNode('mid', 'starbantu');
   	 Tieba_Stlog::addNode('bduid', rtrim(strip_tags(Bingo_Http_Request::getCookie('BAIDUID')), ':FG=1'));
   	 Tieba_Stlog::addNode('url', strip_tags(Bingo_Http_Request::getServer('REQUEST_URI')));
   	 Tieba_Stlog::addNode('refer', strip_tags(Bingo_Http_Request::getServer('HTTP_REFERER')));
   	 Tieba_Stlog::addNode('agent', strip_tags(Bingo_Http_Request::getServer('HTTP_USER_AGENT')));
   	    
   	 Bingo_Log::buildNotice();
   	 Tieba_Stlog::notice();
    echo Bingo_String::array2json($arrOutput);
    return true;
}

private function getActsCtrlKey($key_type)
{
    $key = 0;
    switch($key_type){ 
        case self::BAIDUID_KEY : $key = $_COOKIE['BAIDUID']; break;
        case self::IP_KEY : $key = Bingo_Http_Ip::getUserClientIp(); break;
        case self::UID_KEY : $key = Tieba_Session_Socket::getLoginUid(); break;
    }
    return $key;
} 

private function checkActsCtrl()
{
    $key = self::getActsCtrlKey(self::BAIDUID_KEY);
    $arrParams = array(
        'cmd_no' => self::BAIDUID_CMD_NO,       
        'BAIDUID' => $key,
    );
    $arrOutput = Rpc_ActsCtrl::callActsctrl($arrParams);
    if ( !isset($arrOutput) || empty($arrOutput) || 0 != $arrOutput['err_no'] ) {
        return false;
    }


    $key = self::getActsCtrlKey(self::IP_KEY);
    $arrParams = array(
        'cmd_no' => self::IP_CMD_NO,       
        'USERIP' => $key,
    );
    $arrOutput = Rpc_ActsCtrl::callActsctrl($arrParams);
    if (! isset($arrOutput) || empty($arrOutput) || 0!=$arrOutput['err_no']) {
        return false;
    }

    return true;
}

}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
