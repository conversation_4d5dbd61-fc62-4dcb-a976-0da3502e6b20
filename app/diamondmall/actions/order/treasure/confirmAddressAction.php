<?php
/**
 * 确认收货地址
 * @authors xuyi05
 * @date    2015-12-25 10:26
 * @version $Id$
 */

class confirmAddressAction extends Util_Base {

    /**
     * 主逻辑
     * @return [array] [description]
     */
    public function execute() {

        $session = Util_Session::getSessionInfo();
        $uId = $session['intUid'];

        if ($uId <= 0) {
           return Util_Ret::jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN);
        }

        $tbs = Bingo_Http_Request::get('tbs', '');

        if (!Tieba_Tbs::check($tbs, $uId)) {
           return Util_Ret::jsonRet(Tieba_Errcode::ERR_TBSIG_CHECK_FAIL);
        }

        $orderId = Bingo_Http_Request::get('order_id', '');
        $addressId = Bingo_Http_Request::get('address_id', '');

        $arrInput = array(
            'user_id' => $uId, 
            'order_id' => $orderId,
            'address_id' => $addressId,
        );

        $ret = Tieba_Service::call ( 'diamondmall', 'confirmAddressTreasureOrder', $arrInput, null, null, 'post', 'php', 'utf-8');

        if ($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Util_Ret::jsonRet($ret['errno'], $ret['data']);
        } else {
            Util_Ret::jsonRet(Tieba_Errcode::ERR_SUCCESS);
        }
    }

}
