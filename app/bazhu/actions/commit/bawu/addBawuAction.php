<?php
/***************************************************************************
 *
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * delthreadbatch 吧主专版-上任吧主
 * <AUTHOR>
 * @date 20160310
 */
class addBawuAction extends Molib_Client_BaseAction {
	
	private $intForumId 		= 0;
	private $strUserName		= '';
	private $intRoleId			= 0;
	private $strRoleName		= '';
	private $intUserId			= 0;
	private $intOpUserId 		= 0;
	private $strOpName			= '';
	private $intExpiredTime 	= 86400;
	private $intIp				= 0;
	private $strForumName 		= '';
	private $intPmFlag			= 1;
	private $arrBawuRole		=	array(
		1=>'manager',
		2=>'assist',
		3=>'videoadmin',
		4=>'picadmin',
		5=>'publicadmin',
		6=>'publication_editor',
		7=>'daquan',
		8=>'daquan_editor',
		9=>'disk_editor',
		16=>'top_setter',
		17=>'post_deleter',
		18=>'vip',
		19=>'vip2',
		20=>'voiceadmin',
		21=>'fourth_manager',
		22=>'place_operator',
		23=>'place_editor',
		24=>'vertical_operator',
		25=>'profession_manager',
		26=>'broadcast_admin',
		27=>'place_category_editor',
		28=>'entertainment_manager',
	);
	
	const APPID = 1;
	const TPL = 'tb';
	private $addBawuWidgetName 	= 'addassist_auth_mobile';
	/**
	 * 获取入参
	 * @return multitype:boolean string
	 */
	public function _getPrivateInfo() {
		return array(
			'forum_id' 	=> $this->_getInput('forum_id',0),
			'user_name' => $this->_getInput('user_name',0),
			'role_id' 	=> $this->_getInput('role_id',0),
		);
	}
	
	/**
	 * 检查入参
	 * @return boolean
	 */
	public function _checkPrivate() {
		$this->intForumId 	= strval($this->_objRequest->getPrivateAttr('forum_id'));
		$this->strUserName 	= strval($this->_objRequest->getPrivateAttr('user_name'));
		$this->intRoleId	= strval($this->_objRequest->getPrivateAttr('role_id'));
		$this->strRoleName	= $this->arrBawuRole[$this->intRoleId];
		return true;
	}
	
	/**
	 * 执行
	 * @return boolean
	 */
	public function _execute() {
		
		$this->strForumName = $this->_getForumNameByForumId($this->intForumId);
		$this->intUserId	= $this->_getUserNameByUserId($this->strUserName);
		$this->strOpName	= $this->_objRequest->getCommonAttr('user_name');
		$this->intOpUserId 	= $this->_objRequest->getCommonAttr('user_id');
		$this->intIp 		= $this->_objRequest->getCommonAttr('ip_int');
	 	if( $this->strRoleName == 'manager' ){
			Bingo_Log::warning("add manager is banned!"."op_uid = ".$this->intOpUserId." uid:".$this->intUserId);
			$this->_error(Molib_Client_Error::BAZHU_PERM_CHECK_ERROR, Molib_Client_Error::getErrMsg(Tieba_Errcode::BAZHU_NO_BAZHU_PERM), array(), false);
			return false ;
		}	
		if(!Libs_Util_CheckBawuPerm::check($this->intOpUserId)){
			Bingo_Log::warning("check user perm,failure"."user_id = ".$this->intOpUserId);
			$this->_error(Molib_Client_Error::BAZHU_PERM_CHECK_ERROR, Molib_Client_Error::getErrMsg(Tieba_Errcode::BAZHU_NO_BAZHU_PERM), array(), false);
			return false ;
        }

        if ($this->strRoleName == 'manager' && !self::_checkProfessionManager($this->intForumId, $this->intOpUserId)) {
            $this->_error(Molib_Client_Error::BAZHU_PERM_CHECK_ERROR, Molib_Client_Error::getErrMsg(Tieba_Errcode::BAZHU_NO_BAZHU_PERM), array(), false);
            return false;
        }
		
		$strTbs 	= Bingo_Http_Request::get('tbs');
		if (false === Molib_Service_CommitService::checkTbs($strTbs)) {
			$this->_error(Tieba_Errcode::ERR_TBSIG_CHECK_FAIL, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_TBSIG_CHECK_FAIL));
			return false;
		}
		
		
		
		if(!$this->_checkPhoneVerified()){
			$arrOutData = array(
				'code'=>Tieba_Errcode::BAZHU_ADD_BAWU_CODE,
			);
			$error = Molib_Client_Error::PASSPORT_CHECK_ERROR;
			$this->_error($error, Molib_Client_Error::getErrMsg($error));
			$this->_objResponse->setOutData($arrOutData);
			return false;
		}else{
			$this->_processAddBawu();
		}
		
		return true;
	}
	
	/**
	 * 职业吧主不能添加吧主
	 * @param unknown $arrForumIdList
	 * @return boolean|multitype:unknown
     */
    private static function _checkProfessionManager($intFid, $intOpUserId) {
        $arrInput = array(
            'forum_id'  => $intFid,
        );
        $arrOutput = Tieba_Service::call('perm', 'getBawuList', $arrInput);
        if (!$arrOutput || !isset($arrOutput['errno']) || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call perm getBawuList fail ,input = ' . serialize($arrInput) . ',output = ' . serialize($arrOutput));
            return false;
        }
        if (isset($arrOutput['output']['profession_manager'])) {
            $arrProfessionManager = $arrOutput['output']['profession_manager'];
            foreach ($arrProfessionManager as $value) {
                if ($intOpUserId == $value['user']['user_id']) {
                    return false;
                }
            }
        }
        return true;
    }
	
	/**
	 * 根据用户Name获取用户ID
	 * @param unknown $arrForumIdList
	 * @return boolean|multitype:unknown
	 */
	private function _getUserNameByUserId($strUserName){
		$intUserId = 0;
		$arrInput = array(
			"user_name" => array(
				0 => $strUserName,
			),
		);
		$arrOutput = Molib_Tieba_Service::call('user', 'getUidByUnames', $arrInput, null, null, 'post', 'php', 'utf-8');
		if ($arrOutput == false) {
			Bingo_Log::fatal("call forum getUidByUnames fail, input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "]");
			$error = Tieba_Errcode::ERR_MO_INTERNAL_ERROR;
			$this->_error($error, Molib_Client_Error::getErrMsg($error));
			return false;
		}
		if( $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning("call forum getUidByUnames error, input[".serialize($arrInput)."] output[".serialize($arrOutput)."]");
			$error = Tieba_Errcode::ERR_MO_INTERNAL_ERROR;
			$this->_error($error, Molib_Client_Error::getErrMsg($error));
			return false;
		}
		$arrResult= $arrOutput['output']['uids'];
		$arrReturn = array();
		foreach ($arrResult as $value){
			$intUserId =$value['user_id'];
		}
		return $intUserId;
	}
	
	/**
	 * 
	 * @throws Exception
	 */
    private function _processAddBawu(){
        $strOpNameUTF8 = Bingo_Encode::convert($this->strOpName,Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
        $strUserNameUTF8 = Bingo_Encode::convert($this->strUserName,Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
		$input = array(
			'req' => array(
				'forum_id'      => $this->intForumId,
				'forum_name'    => $this->strForumName,
				'user_id'       => $this->intUserId,
				'user_name'     => $strUserNameUTF8,
				'op_user_id'    => $this->intOpUserId,
				'op_user_name'  => $strOpNameUTF8,
				'need_memo'     => $this->intPmFlag,
				'role_name'     => $this->strRoleName,
				'op_uid'        => $this->intOpUserId,
				'op_uname'      => $strOpNameUTF8,
				'op_ip'         => $this->intIp,
				'from_module'   => MODULE_NAME,
				'from_function' => __CLASS__,
			),
		);
		//$strPermMethod = 'setUserRole';
		
		Bingo_Timer::start('service_bawu_addUserRole');
		$arrOut = Tieba_Service::call('bawu', 'addUserRole', $input);
		Bingo_Timer::end('service_bawu_addUserRole');
		if ($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS || $arrOut === false) {
			Bingo_Log::warning('call service bawu::service_bawu_addUserRole failed,errno:' . serialize($arrOut) . ",input:" . serialize($input));
			$error = Tieba_Errcode::ERR_NO_RIGHT;
			$this->_error($error, Molib_Client_Error::getErrMsg($error));
		}
		
		if ($arrOut === false) {
			throw new Exception('call service perm::service_bawu_addUserRole failed', 1);
		}
	}
	
	/**
	 * 
	 * @return boolean
	 */
	private function _checkPhoneVerified() {
	
		$widget = new Passport_Pauth_AuthWidget(self::APPID, self::TPL);
		$intUid = $this->intOpUserId;
		$baiduId = $this->_objRequest->getCommonAttr('cuid');
		$ip = $this->intIp;
		$widget->setExpire($this->intExpiredTime)->setNames($this->addBawuWidgetName)->query($intUid, $baiduId, $ip);
		$isAuthed = $widget->isAuthed();
		if ($isAuthed) {
			return true;
		} else {
			return false;
		}
	}
	
	/**
	 * 根据吧id获取吧名字
	 * @param unknown $arrForumIdList
	 * @return boolean|multitype:unknown
	 */
	private function _getForumNameByForumId($intForumId){
		$strForumName = '';
		$arrInput = array(
			"forum_id" => array(
				0 => $intForumId,
			),
		);
		$arrOutput = Molib_Tieba_Service::call('forum', 'getFnameByFid', $arrInput);
		if ($arrOutput == false) {
			Bingo_Log::fatal("call forum getFnameByFid fail, input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "]");
			$error = Tieba_Errcode::ERR_MO_INTERNAL_ERROR;
			$this->_error($error, Molib_Client_Error::getErrMsg($error));
			return false;
		}
		if( $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning("call forum getFnameByFid error, input[".serialize($arrInput)."] output[".serialize($arrOutput)."]");
			$error = Tieba_Errcode::ERR_MO_INTERNAL_ERROR;
			$this->_error($error, Molib_Client_Error::getErrMsg($error));
			return false;
		}
		$arrResult= $arrOutput['forum_name'];
		$arrReturn = array();
		foreach ($arrResult as $value){
			$strForumName =$value['forum_name'];
		}
		return $strForumName;
	}
	
	
}
