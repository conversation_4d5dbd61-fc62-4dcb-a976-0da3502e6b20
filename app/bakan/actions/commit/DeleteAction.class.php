<?php
/**
 * ���ύ���������ɰɿ���ɾ������
 * 
 */
class DeleteAction extends ActionBakanCommit{ 
	
	public function execute() {
		//1 : ��ʼ��
		$this->__init();
		
		//2:���error
		if ($this->_errno !== 0) {
			MyLog::warning("Error: error happen in the __init().");
			return true;
		}
		
		//3:post����������
    	$arrKey = array('kw','kid');
		foreach ($arrKey as $key) {
			$post_param = HttpRequest::getPost($key,'');
			if (empty($post_param)){
				$str = sprintf("provide no [%s] value[%s] in post params!", $key,$post_param);
              	MyLog::warning($str);
              	$this->setError(CommonErrno::POST_PARAMS_ERROR,CommonErrmsg::POST_PARAMS_ERROR);
               	return false;
		    }
		}
		
		//4. �������
		$bakanBase = $this->getBakanBaseInfo();
		//4.1 ���ɿ������Ǳ༭�еİɿ�(���Բ��ü��)
		/*
		
		$bakanInfo = BakanCommon::getBakanInfoById($bakanBase['forum_id'],
			HttpRequest::getPost('kid',0));
		if (empty($bakanInfo)) {
			MyLog::warning('while delete,not find the bakaninfo');
			$this->setError(CommonErrno::BAKAN_STATUS_ERROR,CommonErrmsg::BAKAN_STATUS_ERROR);
			return false;
		}
		$intStatus = intval($bakanInfo['status']);
		if ( $intStatus !== BakanInc::$bakan_status['editing'] ) {
			MyLog::warning("while delete,the status[$intStatus] error.");
			$this->setError(CommonErrno::BAKAN_STATUS_ERROR,CommonErrmsg::BAKAN_STATUS_ERROR);
			return false;	
		}
		*/
		//5.  �������ݻ�ȡ
		
		//6. �����ύ����
		
		$arrParams = array(
			'command' => 'delete',
			'forum_id' => $bakanBase['forum_id'],
			'bakan_id' => HttpRequest::getPost('kid',0),
			
		);
		$bolRet = RpcIdlBakan::updateBakanInfo($arrParams);
		if (false === $bolRet) {
			MyLog::warning("while delete, updateBakanInfo error.");
			$this->setError(CommonErrno::TALK_RPC_ERROR,CommonErrmsg::TALK_RPC_ERROR);
               return false;
		}
		//6.3 ʧЧfrs��bakan cache
		RpcIdlBakanFrsInfo::deleteBakanFrsCache($bakanBase['forum_name']);
		
		return true;
	}

}

?>