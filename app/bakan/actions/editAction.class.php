<?php
/*
 *  @brief �ɿ��༭ҳ��
 */
class editAction extends ActionBakanView {
	
	public $bakanInfo = array(); //�ɿ�title����Ϣ
	
	public $bakanDir = array();	//�ɿ�Ŀ¼
	

	public function execute()
	{
		//1. ��ʼ��
		$this->__init();
		
		//2. error ���
		if($this->_errno !== 0) {
			MyLog::warning("ERROR: parent action has error.errno[$this->_errno] errmsg[$this->_errmsg]");
			return true;
		}
		
		//3: �������
		$forum_name = HttpRequest::getGet('kw','');
		$bakan_id = HttpRequest::getGet('kid','');
		if (intval($bakan_id) === 0) {
			$bakan_id = HttpRequest::getParamByIndex(2);
		}
		if (empty($forum_name) || empty($bakan_id)) {
			MyLog::warning("ERROR: get params error. kw[$forum_name] kid[$bakan_id]");
			$this->_errno = CommonErrno::VIEW_PAGE_PARAMS_ERROR;
			$this->_errmsg = CommonErrmsg::GET_FORUM_ERROR;
			return true;
		}

		//4. �������
		if ($this->checkUserLogin() === false){
			return false;
		}
		if ($this->checkUserPower() === false) {
			return false;
		}
		
		/*��ȡ�ɿ���Ϣ*/
		$arrParams = array (
			'forum_id' => $this->bakanBaseInfo['forum_id'],
			'bakan_id' => $bakan_id,
		);
		$arrRet = RpcIdlBakan::getBakanInfoById($arrParams);
		if ($arrRet === false) {
			MyLog::warning("ERROR: getBakanInfoById. kw[$forum_name] kid[$bakan_id]");
			$this->_errno = CommonErrno::TALK_RPC_ERROR;
			$this->_errmsg = CommonErrmsg::TALK_RPC_ERROR;
			return true;
		}
		$this->bakanInfo = $arrRet;
		//echo ('the bakan list is :<br>');
		//var_dump($this->bakanInfo);
		
		//4.2 ���ɿ��Ƿ���ڣ�״̬�Ƿ���ȷ
		if (empty($arrRet)) {
			MyLog::warning("Not find the bakan_info. kw[$forum_name] kid[$bakan_id]");
			$this->_errno = CommonErrno::GET_BAKAN_INFO_ERROR;
			$this->_errmsg = CommonErrmsg::GET_BAKAN_INFO_ERROR;
			return true;
		}
		if ($this->user_role['is_pm'] || $this->user_role['is_bakan_pm'] || $this->user_role['is_peditor']) { 
			MyLog::debug('the pm or bakan_pm can do edit anywhere');
		}else if (intval($arrRet['status']) !== BakanInc::$bakan_status['editing']) {
			MyLog::warning("The bakan status is not editing. kw[$forum_name] kid[$bakan_id]");
			$this->_errno = CommonErrno::BAKAN_STATUS_ERROR;
			$this->_errmsg = CommonErrmsg::BAKAN_STATUS_ERROR;
			return true;
		}
		//4.3 ���ɿ��Ƿ�����
		$arrRet = BakanCommon::checkBakanEditLock($this->bakanBaseInfo['forum_id'],$bakan_id);
		if (false === $arrRet) {
			MyLog::warning("while checkBakanEditLock error. kw[$forum_name] kid[$bakan_id]");
			$this->_errno = CommonErrno::TALK_RPC_ERROR;
			$this->_errmsg = CommonErrmsg::TALK_RPC_ERROR;
			return true;
		}elseif (true === $arrRet) {
			MyLog::debug("the bakan is not lock. kw[$forum_name] kid[$bakan_id]");
		}else {
			if (intval($arrRet['op_uid']) !== $this->user['id']) {
				$str = sprintf("the bakan[%s][%d] is lock by[%d]",$forum_name,$bakan_id,intval($arrRet['op_uid']));
				MyLog::warning($str);
				$this->_errno = CommonErrno::BAKAN_EDIT_LOCK;
				$this->_errmsg = CommonErrmsg::BAKAN_EDIT_LOCK;
				return false;
			}else {
				$str = sprintf("the bakan[%s][%d] is lock by me!![%d]",$forum_name,$bakan_id,intval($arrRet['op_uid']));
				MyLog::debug($str);
			}
		}
		//4.4 �����ɿ�
		$arrInput = array(
			'forum_id' => $this->bakanBaseInfo['forum_id'],
			'bakan_id' => $bakan_id,
			'user_id' => $this->user['id'],
			'user_name' => $this->user['name'],
		);
		$arrRet = RpcIdlBakan::updateBakanEditLock($arrInput);
		if (false === $arrRet) {
			MyLog::warning('while edit, updateBakanEditLock error');
			$this->_errno = CommonErrno::TALK_RPC_ERROR;
			$this->_errmsg = CommonErrmsg::TALK_RPC_ERROR;
			return false;
		}
		/*��ȡ�ɿ�Ŀ¼*/
		$arrParams = array(
			'bakan_id' => $bakan_id,
		); 
		$arrRet = RpcIdlBakan::getBakanDirs($arrParams);
		if (false === $arrRet) {
			MyLog::warning('ERROR: while RpcIdlBakan::getBakanDirs.');
			$this->_errno = CommonErrno::TALK_RPC_ERROR;
			$this->_errmsg = CommonErrmsg::TALK_RPC_ERROR;
			return false;
		}
		$this->bakanDir = $arrRet;
		//var_dump($this->bakanDir);
		
		//5: build template
		$this->__buildTemplate();
		
		return true;
	}
	/*
	 * ���ø��������init����ʼ������
	 */
	public function __init() {
		
		parent::__init();
		StLog::addNode('ispv',1);
		
	}
	
	private function __buildTemplate(){
		
		$bakan_dir = array();
		foreach ($this->bakanDir as $arrDir) {
			$bakan_dir[] = $arrDir['dir_name'];
		}
		//1 :
		$bakan = array(
			'id' => $this->bakanInfo['bakan_id'],
			'status' =>$this->bakanInfo['status'],
			'release_time' => date('Y-m-d G:i',$this->bakanInfo['public_time']),
   			'name' => $this->bakanInfo['title'],
			'intro' => $this->bakanInfo['brief'],
			'cover_img' =>$this->bakanInfo['pic_url'],
			'rss_num' => 0,
			'comment_num' => 0,//todo in future
			'thread_id' => $this->bakanInfo['thread_id'],
			'dir' => $bakan_dir,
			'bg_music_url' => $this->bakanInfo['music_background'],
		);
		//var_dump($bakan);
		$bakan_mod = array(
			'mod_name' => $this->bakanBaseInfo['module_name'],
		);
		$this->set('bakan_mod',$bakan_mod);
		//set to tpl
		$this->setTemplateFile('bakan/edit.tpl');
		$this->set('user',$this->getUserInfo());
		$this->set('forum',$this->forum);
		$this->set('bakan',$bakan);
		
		return true;
	}
	
	public function __destruct(){
		
		parent::__destruct();
	}
	
}

?>