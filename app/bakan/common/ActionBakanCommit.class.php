<?php
/*
 * bakan�ύ�ദ��Ļ����������ṩ�������жϺͺ���
 * 
 */
class ActionBakanCommit extends ActionBaseBakan
{

	protected $_templateFile = 'data/json.tpl';
	protected $_arrRet = array();
	
	/*
	 * �ύ����ж��ۺϴ������
	 */
	public function __init(){
		
		parent::__init();
		if ($this->_errno !== 0) {
			return false;
		}
		
		if (false === $this->methodCheck()) return false;
		
		if (false === $this->checkUserLogin()) return false;
		
		if (false === $this->userPowerCheck()) return false;
		
		if(false === $this->tbsCheck())  return false;
		
		//��username�ж�
		if(isset($this->user['no_un']) && $this->user['no_un'] == true ){
		    MyLog::warning('no user name can not commit!.');
			$this->setError(CommonErrno::BAKAN_NO_USERNAME, CommonErrmsg::BAKAN_NO_USERNAME);
			return false;
		}
		
		return true;
	}
	/*
	 * post �������
	 */
	protected function methodCheck()
	{
		$_strMethod = strtolower(strip_tags($_SERVER['REQUEST_METHOD']));
        if ($_strMethod != 'post') {
            MyLog::warning('commit method must be post,but now is ' . $_strMethod);
            $this->setError(CommonErrno::COMMIT_CHECK_INVALID, CommonErrmsg::COMMIT_CHECK_INVALID);
            return false;
        }
        return true;
	}
	/*
	 * �û���¼���
	 */
	protected function userLoginCheck()
	{
		$user_info = $this->getUserInfo();
		if ($user_info['is_login'] === true) {
			return true;
		}else {
			MyLog::warning('In commit command,the user is not login.');
			$this->setError(CommonErrno::USER_IS_LOGOUT, CommonErrmsg::USER_IS_LOGOUT);
			return false;
		}
	}
	/*
	 * �û�Ȩ����֤
	 */
	protected function userPowerCheck() {
		$url_key = $this->getUrlKey();
		$user_role = $this->getUserRole();
		if (empty($url_key)) {
			MyLog::warning("Not find the url_key.");
			$this->setError(CommonErrno::USER_IS_NO_POWER,CommonErrmsg::USER_IS_NO_POWER);
			return false;
		}
		if (!isset(CommitViewRoleConfig::$userOperationRole[$url_key])) {
			MyLog::warning("Not find the url_key[$url_key] in the userOperationRole.");
			$this->setError(CommonErrno::USER_IS_NO_POWER,CommonErrmsg::USER_IS_NO_POWER);
			return false;
		}
		$op_role = CommitViewRoleConfig::$userOperationRole[$url_key];
		if ( $op_role === 0 ||( isset($user_role[$op_role])  && $user_role[$op_role] === true ) ) {
			MyLog::debug("The user has the power to do the process[$url_key]op_role[$op_role] ".serialize($user_role));
			return true; 
		}else {
			MyLog::warning("ERROR:The user has not the power to do the process[$url_key] op_role[$op_role] ".serialize($user_role));
			$this->setError(CommonErrno::USER_IS_NO_POWER,CommonErrmsg::USER_IS_NO_POWER);
			return false;
		}
	}
	
	/*
	 *  ����tbs_check�Ĺ��ܷ���
	 */
	public function tbsCheck()
	{
		if (ENABLE_TBS_CHECK === false) { 
			return true;
		}
		$tbs = HttpRequest::getPost('tbs', '');
		$arrUser = $this->getUserInfo();
		if (false === CommonTbs::check($tbs, $arrUser['id'])) {
			MyLog::warning("ERROR:The tbs check error. tbs[$tbs]");
			$this->setError(CommonErrno::TBS_CHECK_ERROR,CommonErrmsg::TBS_CHECK_ERROR);
			return false;
		}
		return true;
	}
	/*
	 * ********************************************************
	 * ���涼��һЩclass�ڵĲ���
	 * ********************************************************
	 */
	protected function setError($errno, $errmsg)
	{
		$this->_errno = $errno;
		$this->_errmsg = $errmsg;
	}
	
	protected function addRet($strKey, $strValue)
	{
		$this->_arrRet[$strKey] = $strValue;
	}
	
	protected function setRet($arrRet)
	{
		$this->_arrRet = $arrRet;
	}
	
	protected function buildJson()
	{
		$arrJson = array(
			'error_no' => $this->_errno,
			'_info' => $this->_errmsg,
			'msg' => $this->_arrRet,
		);
		$this->set('json_data', $arrJson);
		return $arrJson;
	}
	
	protected function log($str)
	{
		//TODO
		MyLog::notice($str);
	}
	public function __destruct()
	{
		$this->buildJson();
		parent::__destruct();
	}
}
