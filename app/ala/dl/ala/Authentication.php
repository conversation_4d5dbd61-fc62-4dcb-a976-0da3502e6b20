<?php
/**
 * Authentication.php
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 17/7/14 下午3:25
*/

define('MODULE', 'Ala_dl');
class Dl_Ala_Authentication extends Dl_Ala_Base {


    const MODULE_NAME = 'ala';
    const SERVICE_NAME = 'Dl_Ala_Authentication';
    const DB_CHARSET = 'utf8mb4';

    const DATABASE_NAME = 'forum_ala';
    const AUTHENTICATION_TABLE_INFO = 'authentication_info';

    private static $_db = null;
    private static $_conf = null;

    /**
     * @param $errno
     * @return array
     */
    private static function _errRet($errno) {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @return Bd_DB|null
     */
    private static function _getDB() {
        if(self::$_db) {
            return self::$_db;
        }
        self::$_db = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if(self::$_db == null || !self::$_db->isConnected()) {
            Bingo_Log::warning('db connect fail.');
            return null;
        }
        self::$_db->query("set names ".self::DB_CHARSET);
        return self::$_db;
    }

    /**
     * @return array|bool
     */
    private static function _init() {
        if(self::_getDB() == null) {
            Bingo_Log::warning("init db fail.");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        if(self::$_conf == null) {
            $dlConfFile = '/app/' . self::MODULE_NAME . '/' .  strtolower(self::SERVICE_NAME);
            self::$_conf = Bd_Conf::getConf($dlConfFile);
            if(self::$_conf == false) {
                Bingo_Log::warning('init get conf fail.' . $dlConfFile);
                return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
            }
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return array|bool
     */
    public static function execSql($arrInput) {
        if(!isset($arrInput['function'])) {
            Bingo_Log::warning('input params invalid: function is empty. [' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $ret = self::_init();
        if($ret !== true) {
            return $ret;
        }
        Bingo_Timer::start('initlib');
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end('initlib');
        if($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $arrOut = $mdb->execSql($arrInput );
        return $arrOut;
    }

    /**
     * @param $arrParamInput
     * @return array
     */
    public static function getAuthenticationList($arrParamInput){
        if (empty($arrParamInput["offset"])) {
            $arrParamInput["offset"] = 0;
        }

        if (empty($arrParamInput["limit"])) {
            $arrParamInput["limit"] = Alalib_Conf_Define::DB_DEFAULT_PAGE_LIMIT;
        }

        if (empty($arrParamInput["order_item"])) {
            $arrParamInput["order_item"] = "final_op_time";
        }
        if (empty($arrParamInput["order"])) {
            $arrParamInput["order"] = "DESC";
        }

        $arrInput = array(
            'function'   => 'getAuthenticationList',
            'order_item' => $arrParamInput['order_item'],
            'order'      => $arrParamInput['order'],
            'offset'     => (int)$arrParamInput['offset'],
            'limit'      => (int)$arrParamInput['limit'],
            'where'      => '',
        );

        if (!empty($arrParamInput['start_time'])) {
            $arrInput['where'] = 'WHERE final_commit_time >= '.intval($arrParamInput['start_time']);
        }

        if (!empty($arrParamInput['end_time'])) {
            if (!empty($arrInput['where'])) {
                $arrInput['where'] .= ' AND final_commit_time <= '.intval($arrParamInput['end_time']);
            } else {
                $arrInput['where'] = 'WHERE final_commit_time <= '.intval($arrParamInput['end_time']);
            }
        }

        if (!empty($arrParamInput['user_id'])) {
            $arrInput['where'] = 'WHERE user_id = '.intval($arrParamInput['user_id']);
        }

        if (!empty($arrParamInput['user_ids'])) {
            $arrInput['where'] = 'WHERE user_id IN ('.implode(',', $arrParamInput['user_ids']).')';
        }

        if (!empty($arrParamInput['anchor_level_present'])) {
            if (!empty($arrInput['where'])) {
                $arrInput['where'] .= " AND anchor_level_present = '" . strval($arrParamInput['anchor_level_present']) . "'" ;
            } else {
                $arrInput['where'] = " WHERE anchor_level_present = '" . strval($arrParamInput['anchor_level_present']) . "'" ;
            }
        }

        $arrOutput = self::execSql($arrInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__. " call Dl_Ala_Authentication::execSql fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }


    /**
     * @param $arrParamInput
     * @return array
     */
    public static function getUserIdsWithAnchorLevelPresent($arrParamInput){

        $intPn = intval($arrParamInput['pn']);  // begin 0
        $intPs = isset($arrParamInput['ps']) ? intval($arrParamInput['ps']) : 50;
        $intOffset = $intPn * $intPs;

        $arrInput = array(
            'function'   => 'getUserIdsWithAnchorLevelPresent',
            'offset'     => $intOffset,
            'ps'      => $intPs,
        );

        if(isset($arrParamInput['anchor_level_present'])) {
            $strAnchorLevelPresent = strval($arrParamInput['anchor_level_present']);
            $arrInput['where'] = " anchor_level_present = '" . $strAnchorLevelPresent . "' ";
        }
        else {
            $arrInput['where'] = " anchor_level_present != '' ";
        }

        $arrOutput = self::execSql($arrInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__. " call Dl_Ala_Authentication::execSql fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];
        $arrUserIds = array();
        foreach ($arrRetData as $value) {
            $arrUserIds[] = intval($value['user_id']);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrUserIds);
    }

    /**
     * @param $arrParamInput
     * @return array
     */
    public static function getAuthenticationCount($arrParamInput){

        $arrInput = array(
            'function' => 'getAuthenticationCount',
            'where'    => '',
        );

        if (!empty($arrParamInput['start_time'])) {
            $arrInput['where'] = 'WHERE final_commit_time >= '.intval($arrParamInput['start_time']);
        }

        if (!empty($arrParamInput['end_time'])) {
            if (!empty($arrInput['where'])) {
                $arrInput['where'] .= ' AND final_commit_time <= '.intval($arrParamInput['end_time']);
            } else {
                $arrInput['where'] = 'WHERE final_commit_time <= '.intval($arrParamInput['end_time']);
            }
        }

        if (!empty($arrParamInput['user_id'])) {
            $arrInput['where'] = 'WHERE user_id = '.intval($arrParamInput['user_id']);
        }

        if (!empty($arrParamInput['user_ids'])) {
            $arrInput['where'] = 'WHERE user_id IN ('.implode(',', $arrParamInput['user_ids']).')';
        }

        if (!empty($arrParamInput['anchor_level_present'])) {
            if (!empty($arrInput['where'])) {
                $arrInput['where'] .= " AND anchor_level_present = '" . strval($arrParamInput['anchor_level_present']) . "'" ;
            } else {
                $arrInput['where'] = " WHERE anchor_level_present = '" . strval($arrParamInput['anchor_level_present']) . "'" ;
            }
        }


        $arrOutput = self::execSql($arrInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__. " call Dl_Ala_Authentication::execSql fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }


    /**
     * @param $arrParamInput
     * @return array
     */
    public static function selectAuthenticationInfoByPhone($arrParamInput){
        if(empty($arrParamInput['phone_number'])){
            $strLog = __CLASS__."::".__FUNCTION__. " param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR,array());
        }
        $arrInput = array(
            'function' => 'selectAuthenticationInfoByPhone',
            'table_name'    => self::AUTHENTICATION_TABLE_INFO,
            'phone_number' => $arrParamInput['phone_number'],
        );
        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__. " call Dl_Ala_Authentication::execSql fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrRetData = $arrOutput['results'][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }

    /**
     * @param $arrParamInput
     * @return array
     */
    public static function selectAuthenticationInfoByIdCard($arrParamInput){
        if('' == $arrParamInput['id_card_number']){
            $strLog = __CLASS__."::".__FUNCTION__. " param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR,array());
        }
        $arrInput = array(
            'function' => 'selectAuthenticationInfoByIdCard',
            'table_name'    => self::AUTHENTICATION_TABLE_INFO,
            'id_card_number' => $arrParamInput['id_card_number'],
        );
        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__. " call Dl_Ala_Authentication::execSql fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrRetData = $arrOutput['results'][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }
}
