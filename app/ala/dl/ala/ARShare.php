<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR> @date 2015:10:14 10:48:22
 * @version
 * @structs & methods(copied from idl.)
 */
define('MODULE', 'Ala_dl');

class Dl_Ala_ARShare extends Dl_Ala_Base
{

    // zm db config
    const MODULE_NAME = 'ala';

    const SERVICE_NAME = 'Dl_Ala_ARShare';

    const DB_CHARSET = 'utf8';

    const DATABASE_NAME = 'forum_ala';

    const TABLE_NAME = 'forum_ar_share';

    const ARSHARE_URL = 'https://tieba.baidu.com/mo/q/forum/arshare?share_id=%s';

    private static $_db = null;

    private static $_conf = null;

    /**
     * private _errRet
     *
     * @param
     *            $errno
     * @return array
     */
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Alalib_Conf_Error::getErrorMsg($errno)
        );
    }

    /**
     * private _getDB
     *
     * @param
     *            $errno
     * @return array
     */
    private static function _getDB()
    {
        if (self::$_db) {
            return self::$_db;
        }
        self::$_db = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if (self::$_db == null || ! self::$_db->isConnected()) {
            Bingo_Log::warning('db connect fail.');
            return null;
        }
        self::$_db->query("set names " . self::DB_CHARSET);
        return self::$_db;
    }

    /**
     * private _init
     *
     * @param
     *            $errno
     * @return array
     */
    private static function _init()
    {
        if (self::_getDB() == null) {
            Bingo_Log::warning("init db fail.");
            return self::_errRet(Alalib_Conf_Error::ERR_DB_CONN_FAIL);
        }
        if (self::$_conf == null) {
            $dlConfFile = '/app/' . self::MODULE_NAME . '/' . strtolower(self::SERVICE_NAME);
            self::$_conf = Bd_Conf::getConf($dlConfFile);
            if (self::$_conf == false) {
                Bingo_Log::warning('init get conf fail.' . $dlConfFile);
                return self::_errRet(Alalib_Conf_Error::ERR_LOAD_CONFIG_FAIL);
            }
        }
        return true;
    }

    /**
     * execSql
     *
     * @param
     *            $arrInput
     * @return array
     */
    public static function execSql($arrInput)
    {
        if (! isset($arrInput['function'])) {
            Bingo_Log::warning('input params invalid: function is empty. [' . serialize($arrInput) . ']');
            return self::_errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }
        $ret = self::_init();
        if ($ret !== true) {
            return $ret;
        }
        Bingo_Timer::start('initlib');
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end('initlib');
        if ($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::_errRet(Alalib_Conf_Error::ERR_DB_CONN_FAIL);
        }
        $bolPrintSql = true;
        $arrOut = $mdb->execSql($arrInput, $bolPrintSql);
        return $arrOut;
    }

    /**
     * 返回值
     *
     * @param {Int} $intUserId            
     * @param {String} $strJson            
     *
     * @return {Array} :errno :errmsg :{Array}output
     *        
     */
    public static function add($intUserId, $strJson)
    {
        $arrInput = array(
            'function' => 'add',
            'table_name' => self::TABLE_NAME,
            'user_id' => $intUserId,
            'create_time' => Bingo_Timer::getNowTime(),
            'json' => $strJson
        );
        
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_ARShare::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_DB_QUERY_FAIL);
        }
        
        $intShareId = self::$_db->getInsertID();
        if (false === $intShareId) {
            $strLog = "Dl_Ala_ARShare::getInsertID fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_DB_QUERY_FAIL);
        }
        
        $arrRet = array(
            'share_id' => $intShareId
        );
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

    /**
     * 返回值
     *
     * @param {Int} $intShareId            
     *
     * @return {Array} :errno :errmsg :{Array}output
     *        
     */
    public static function getUrl($intShareId)
    {
        $strUrl = sprintf(self::ARSHARE_URL, $intShareId);
        
        $arrRet = array(
            'share_url' => $strUrl
        );
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

    /**
     * 返回值
     *
     * @param {Int} $intShareId            
     *
     * @return {Array} :errno :errmsg :{Array}output
     *        
     */
    public static function getOne($intShareId)
    {
        $arrInput = array(
            'function' => 'getOne',
            'table_name' => self::TABLE_NAME,
            'share_id' => $intShareId
        );
        
        $arrDlRet = self::execSql($arrInput);
        if (false === $arrDlRet || Alalib_Conf_Error::ERR_SUCCESS != $arrDlRet["errno"]) {
            $strLog = "Dl_Ala_ARShare::execSql fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrDlRet) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_DB_QUERY_FAIL);
        }
        
        $arrRet = $arrDlRet["results"][0];
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }
}
