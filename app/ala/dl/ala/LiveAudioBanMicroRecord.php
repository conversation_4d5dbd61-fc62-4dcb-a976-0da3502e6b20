<?php
/*
* 音频交友房-审核下麦记录表
*
* (c) ivar <<EMAIL>>
* 
* Date: 2020/7/29 上午19:51
* 
* This source file is subject to the MIT license that is bundled with this source code in the file LICENSE.
*/

define('MODULE', 'Ala_dl');

class Dl_Ala_LiveAudioBanMicroRecord  extends Dl_Ala_Base {

    const MODULE_NAME   = 'ala';
    const DB_CHARSET    = 'utf8';
    const DATABASE_NAME = 'forum_ala';

    /**
     * 禁麦记录表
     */
    const TABLE_NAME_AUDIO_MICRO_PHONE_BAN        = 'live_audio_ban_micro_record';

    private static $_db = null;

    /**
     * @param $errno
     * @return array
     */
    private static function _errRet($errno)
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    private static function _getDB() {
        if(self::$_db) {
            return self::$_db;
        }
        self::$_db = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if(self::$_db == null || !self::$_db->isConnected()) {
            self::$_db = Tieba_Mysql::getDB(self::DATABASE_NAME);
            if(self::$_db == null || !self::$_db->isConnected()) {
                Bingo_Log::warning('db connect fail.');
                return null;
            }
        }
        self::$_db->query("set names ".self::DB_CHARSET);
        return self::$_db;
    }

    /**
     *
     * @var array
     */
    public static $_fields = array(
        'type'                  => 'in(1,2)',      // 1:未下麦，2:下麦成功
        'room_id'               => 'number',       // 直播room_id
        'uid'                   => 'number',       // 下麦uid
        'op_uid'                => 'number',       // 操作用户id
        'audit_reason'          => 'str',          // 审核警告理由
        'create_time'           => 'time',         // 警告时间
        'update_time'           => 'time',         // 更新时间
    );

    /**
     * 校验参数多少 以及参数格式
     * @param $arrParams
     * @return bool
     */
    protected static function checkParams($arrParams){
        $arrParamsKeys  = array_keys($arrParams);
        $arrFieldKeys   = array_keys(self::$_fields);

        foreach ($arrParamsKeys as $strParamsKey) {
            if (!in_array($strParamsKey,$arrFieldKeys)){
                return false;
            }
            $fileVal = self::$_fields[$strParamsKey];
            $boolValid = Libs_Util_Audio_Tools::validType($fileVal,$arrParams[$strParamsKey]);
            if ($boolValid == false){
                return false;
            }
        }
        return true;
    }

    /**
     * 音频直播交友 下麦
     * @param $arrInput
     * @return array
     */
    public static function insertAudioBanMicroRecord($arrInput){
        // 1、判断
        if (empty($arrInput['room_id']) || empty($arrInput['audit_reason']) || empty($arrInput['uid'])){
            $strLog = __CLASS__. "::". __FUNCTION__."  param error. input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR,array());
        }
        // 2、过滤一下arrInput参数
        $boolCheckParams = self::checkParams($arrInput);

        if ($boolCheckParams == false){
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR,array());
        }
        // 3、create new get db
        $objDb = self::_getDB();
        if( !$objDb ) {
            Bingo_Log::warning('create new getDB fail.');
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        // 4、插入静麦记录
        $arrDlRet = $objDb->insert(self::TABLE_NAME_AUDIO_MICRO_PHONE_BAN,$arrInput);
        if(false === $arrDlRet || Tieba_Errcode::ERR_SUCCESS != $arrDlRet["errno"]){
            $strLog = "Dl_Ala_Audio::insertAudioBanMicroRecord insert . insertAudioBanMicroRecord fail. input:[  arrInput= ".serialize($arrInput)."]; output:[".serialize($arrDlRet)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     *
     * @param $arrInput
     * @param $id
     * @return array
     */
    public static function updateAudioBanMicroRecordById($arrInput,$id){
        // 1、判断
        if (empty($id)){
            $strLog = __CLASS__. "::". __FUNCTION__."  param error. id=".$id." input:[".serialize($arrInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR,array());
        }

        // 2、过滤一下arrInput参数
        $boolCheckParams = self::checkParams($arrInput);
        if ($boolCheckParams == false){
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR,array());
        }
        // 3、create new get db
        $objDb = self::_getDB();
        if( !$objDb ) {
            Bingo_Log::warning('create new getDB fail.');
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $cond = array(
          'id=' => $id,
        );
        // 4、插入下麦记录
        $arrDlRet = $objDb->update(self::TABLE_NAME_AUDIO_MICRO_PHONE_BAN,$arrInput,$cond);
        if(false === $arrDlRet || Tieba_Errcode::ERR_SUCCESS != $arrDlRet["errno"]){
            $strLog = "Dl_Ala_Audio::updateAudioBanMicroRecordById update . updateAudioBanMicroRecordById fail. input:[  arrInput= ".serialize($arrInput)."]; output:[".serialize($arrDlRet)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * build cond
     * @param $cond
     * @return array
     */
    protected static function buildCondition($cond){
        $arrRet = array();
        if (isset($cond['uid'])){
            $arrRet['uid='] = $cond['uid'];
        }
        if (isset($cond['room_id'])){
            $arrRet['room_id='] = $cond['room_id'];
        }
        if (isset($cond['type'])){
            $arrRet['type='] = $cond['type'];
        }
        return $arrRet;
    }
    /**
     * 获取信息
     * @param $cond
     * @param string $field
     * @return array
     */
    public static function getAudioBanMicroRecordByCond($cond,$field = "*"){
        // create new get db
        $objDb = self::_getDB();
        if( !$objDb ) {
            Bingo_Log::warning('create new getDB fail.');
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $arrDBCond = self::buildCondition($cond);
        $arrDlRet = $objDb->select(self::TABLE_NAME_AUDIO_MICRO_PHONE_BAN,$field,$arrDBCond);

        if(false === $arrDlRet || Tieba_Errcode::ERR_SUCCESS != $arrDlRet["errno"]){
            $strLog = "Dl_Ala_Audio::getAudioBanMicroRecordByCond . getAudioBanMicroRecordByCond fail. input:[  arrInput= ".serialize($arrDBCond)."]; output:[".serialize($arrDlRet)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrRes = empty($arrDlRet) ? array() : $arrDlRet;

        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes);
    }

}