<?php
/**
 * Created by PhpStorm.
 * User: sunzhexuan
 * Date: 2019/5/15
 * Time: 下午8:38
 */
define('MODULE', 'Ala_dl');
class Dl_Ala_ActivityTasteLucky extends Dl_Ala_Base{
    const MODULE_NAME = 'ala';
    const SERVICE_NAME = 'Dl_Ala_Activity';
    const DB_CHARSET = 'utf8';

    const DATABASE_NAME = 'forum_ala';

    const TABLE_NAME_ACTIVITY_TASTE_LUCKY = 'activity_taste_lucky_record';

    const DB_DEFAULT_PAGE_LIMIT = 30;

    /**
     * subapp_type -> id map
     * @var array
     */
    private static $_arrSubAppTypeMap = array(
        Alalib_Conf_Sdk::SUBAPP_TIEBA   => 0,
        Alalib_Conf_Sdk::SUBAPP_SHOUBAI => 0,
        Alalib_Conf_Sdk::SUBAPP_HAOKAN  => 1,
        Al<PERSON><PERSON>_Conf_Sdk::SUBAPP_QUANMIN => 2,
    );

    private static $_db = null;
    private static $_conf = null;

    /**
     * 输出错误
     * @param {String} : errno
     * @return {Array} :errno :errmsg :output
     * */
    private static function _errRet($errno) {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * 获取DB连接
     * @param 无
     * @return {Resource} :self::$_db
     * */
    private static function _getDB() {
        if(self::$_db) {
            return self::$_db;
        }
        self::$_db = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if(self::$_db == null || !self::$_db->isConnected()) {
            Bingo_Log::warning('db connect fail.');
            return null;
        }
        self::$_db->query("set names ".self::DB_CHARSET);
        return self::$_db;
    }

    /**
     * 初始化设置
     * @param 无
     * @return {Bool} :true
     * */
    private static function _init() {
        if(self::_getDB() == null) {
            Bingo_Log::warning("init db fail.");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        if(self::$_conf == null) {
            $dlConfFile = '/app/' . self::MODULE_NAME . '/' .  strtolower(self::SERVICE_NAME);
            self::$_conf = Bd_Conf::getConf($dlConfFile);
            if(self::$_conf == false) {
                Bingo_Log::warning('init get conf fail.' . $dlConfFile);
                return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
            }
        }
        return true;
    }

    /**
     * @param null
     * @return null
     */
    public static function startTransaction()
    {
        $db = self::_getDB();
        return $db->startTransaction();
    }

    /**
     * @param null
     * @return null
     */
    public static function commit()
    {
        $db = self::_getDB();
        return $db->commit();
    }

    /**
     * @param null
     * @return null
     */
    public static function rollback()
    {
        $db = self::_getDB();
        return $db->rollback();
    }

    /**
     * 执行sql
     * @param {Array}
     * @return {Array}
     * */
    public static function execSql($arrInput) {
        if(!isset($arrInput['function'])) {
            Bingo_Log::warning('input params invalid: function is empty. [' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $ret = self::_init();
        if($ret !== true) {
            return $ret;
        }
        Bingo_Timer::start('initlib');
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end('initlib');
        if($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $arrOut = $mdb->execSql($arrInput, true);
        return $arrOut;
    }

    /**
     *
     * @param $arrParamInput
     * @return array
     */
    public static function addTasteLuckyRecord($arrParamInput){
        if (empty($arrParamInput['user_id']) || empty($arrParamInput['anchor_id'])
            || empty($arrParamInput['activity_id']) || empty($arrParamInput['gift_id'])
            || empty($arrParamInput['create_time'])
        ) {
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR,array());
        }

        $arrInput = array(
            'function'       => 'addTasteLuckyRecord',
            'table_name'     => self::TABLE_NAME_ACTIVITY_TASTE_LUCKY,
            'activity_id'    => $arrParamInput['activity_id'],
            'user_id'        => intval($arrParamInput['user_id']),
            'anchor_id'      => intval($arrParamInput['anchor_id']),
            'gift_id'        => intval($arrParamInput['gift_id']),
            'create_time'    => intval($arrParamInput['create_time']),
        );
        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __FUNCTION__ . " fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);

            return self::errRet(Alalib_Conf_Error::ERR_DB_QUERY_FAIL);
        }

        $arrRetData = $arrOutput['results'][0];
        return self::errRet(Tieba_Errcode::ERR_SUCCESS,$arrRetData);
    }

    /**
     * @param $arrParamInput
     * @return array
     */
    public static function getTasteLuckyRecord($arrParamInput){
        if (empty($arrParamInput['activity_id'])){
            Bingo_Log::warning(__CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];");
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR, array());
        }

        $arrInput = array(
            'function'       => 'getTasteLuckyRecord',
            'table_name'     => self::TABLE_NAME_ACTIVITY_TASTE_LUCKY,
            'activity_id'    => intval($arrParamInput['activity_id']),
        );
        $arrOutput = self::execSql($arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call Dl_Ala_ActivityTasteLucky::execSql fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrRetData = $arrOutput['results'][0];

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);
    }

}
