<?php
/**
 * Created by PhpStorm.
 * User: lixiaoxu03
 * Date: 2020/12/30
 * Time: 5:11 PM
 */

define('MODULE', 'Ala_dl');
class Dl_Ala_ActivityLoopPassTask extends Dl_Ala_Base
{
    const MODULE_NAME = 'ala';
    const SERVICE_NAME = 'Dl_Ala_ActivityLoopPassTask';
    const DB_CHARSET = 'utf8';
    const DATABASE_NAME = 'forum_ala';
    const TABLE_NAME = 'activity_looppasstask_record';

    public static $_db = null;
    public static $_conf = null;

    /**
     * 获取DB连接
     * @return Bd_DB|null
     */
    public static function _getDB() {
        if(self::$_db) {
            return self::$_db;
        }
        self::$_db = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if(self::$_db == null || !self::$_db->isConnected()) {
            Bingo_Log::warning('db connect fail.');
            return null;
        }
        self::$_db->query("set names ".self::DB_CHARSET);
        return self::$_db;
    }

    /**
     * 新增任务记录
     * @param $row
     * @param null $option
     * @param null $onDup
     * @return mixed
     * @throws Exception
     */
    public static function addRecord($row, $option = null, $onDup = null)
    {
        $db = self::_getDB();
        $arrOutput = $db->insert(self::TABLE_NAME, $row, $option, $onDup);
        if (false === $arrOutput) {

            $strLog = __CLASS__. "::".__FUNCTION__ . " fail. row:[".serialize($row)."]";
            Bingo_Log::fatal($strLog);

            throw new Exception(self::getDBError(), self::getDBErrno());
        }
        $insertId = $db->getInsertID();
        return $insertId;
    }

    /**
     * 更新任务记录
     * @param $row
     * @param null $conds
     * @param null $options
     * @param null $appends
     * @return bool|int
     * @throws Exception
     */
    public static function updateRecord($row, $conds = null, $options = null, $appends = null)
    {
        $db = self::_getDB();
        $arrOutput = $db->update(self::TABLE_NAME, $row, $conds, $options, $appends);
        if (false === $arrOutput) {

            $strLog = __CLASS__. "::".__FUNCTION__ . " fail. row:[".serialize($row)."]; conds:[".serialize($conds)."]";
            Bingo_Log::fatal($strLog);

            throw new Exception(self::getDBError(), self::getDBErrno());
        }
        return $arrOutput;
    }

    /**
     * @param null
     * @return null
     */
    static public function startTransaction()
    {
        $db = self::_getDB();
        return $db->startTransaction();
    }

    /**
     * @param null
     * @return null
     */
    static public function commit()
    {
        $db = self::_getDB();
        return $db->commit();
    }

    /**
     * @param null
     * @return null
     */
    static public function rollback()
    {
        $db = self::_getDB();
        return $db->rollback();
    }

    /**
     * @param null
     * @return int
     */
    static public function getDBErrno()
    {
        $db = self::_getDB();
        $res = $db->errno();
        if (false === $res) {
            Bingo_Log::warning(__CLASS__. "::". __FUNCTION__ . ' failed');
        }
        return $res;

    }
    /**
     * @param null
     * @return string
     */
    static public function getDBError()
    {
        $db = self::_getDB();
        $res = $db->error();

        if (false === $res) {
            Bingo_Log::warning(__CLASS__. "::". __FUNCTION__ . ' failed');
        }
        return $res;
    }
}
