<?php
/**
 * Created by PhpStorm.
 * User: lixiaoxu03
 * Date: 2019/5/6
 * Time: 11:10 AM
 */
class Dl_Quanmin_Ugc_UgcInfo extends Dl_Ala_Base
{
    const SERVICE_NAME = "service_mvideo";
    const METHOD_UGC_ANCHOR_INFO = "/feedvideoui/internal/getanchorinfo";


    /**
     * UGC主播信息
     *
     * @param array $arrInput (live_info)
     *
     * @return array $arrOutput
     **/
    public static function ugcAnchorInfo($arrInput)
    {
        $arrLiveInfo = array(
            'live_id' => intval($arrInput['live_id']),
            'uid'     => intval($arrInput['user_id']),
        );
        if ($arrLiveInfo['live_id'] <= 0) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return Util_Function::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }

        $arrReq = array(
            "pathinfo" => self::METHOD_UGC_ANCHOR_INFO,
            'host' => 'sv.baidu.com',
            "querystring" => http_build_query($arrLiveInfo),
        );
        $strResp = ral(self::SERVICE_NAME, 'GET', array(), array(),$arrReq);
        $arrResp = empty($strResp) ? false : json_decode($strResp, true);
        if (false === $arrResp || Alalib_Conf_Error::ERR_SUCCESS != $arrResp["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call service_anchor fail. input:[" . serialize($arrReq) . "]; output:[" . $strResp . "]";
            Bingo_Log::warning($strLog);
            $arrResp['errmsg'] = 'call service_anchor[group.haokan-inrouter.SUPERPAGE.all] fail**********************'.$strLog;
            return Util_Function::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL,array());
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrResp['data']);
    }
}
