<?php
/**
 * 季度赛--计算综合榜
 * Created by PhpStorm.
 * User: zhanghanqing
 * Date: 2018/7/13
 * Time: 11:28
 */

define('ROOT_PATH', '/home/<USER>/orp');
define('DATA_PATH', ROOT_PATH . '/data');
define('IS_ORP_RUNTIME', true);
Tieba_Init::init('ala');

set_time_limit(0);

class quarterlyAnchorFinalRank
{

    /**
     * @param $intNowTime
     * @return bool
     */
    public function run($intWrite) {

        $arrServiceInput  = array(
        );
        $strServiceName   = "ala";
        $strServiceMethod = "getQuarterlyAnchorRank";
        $arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return false;
        }
        $arrRankInfos = $arrOutput['data'];

        // 先算季度之星
        $arrUserAllScore = array();
        foreach ($arrRankInfos as $key => $tmpArrRank){
            if ($key == Service_Rank_Rank::QUARTERLY_RANK_FINAL_MARK){
                continue;
            }
            foreach ($tmpArrRank as $item){
                $intUserId = $item['member'];
                $intScore = $item['score'];
                if (empty($arrUserAllScore[$intUserId])){
                    $arrUserAllScore[$intUserId] = $intScore;
                }else{
                    $arrUserAllScore[$intUserId] += $intScore;
                }
            }
        }
        $intStarMaxScore = 0;
        foreach ($arrUserAllScore as $tmpUserId => $tmpUserScore){
            if ($tmpUserScore > $intStarMaxScore){
                $intStarMaxScore = $tmpUserScore;
            }
        }
        // $intStarUserId = 0;
        $arrStarUser = array();
        foreach ($arrUserAllScore as $tmpUserId => $tmpUserScore){
            if ($tmpUserScore >= $intStarMaxScore && $tmpUserScore != 0){
                $arrStarUser[] = array(
                    'member' => $tmpUserId,
                    'score' => $tmpUserScore,
                );
            }
        }

        // 计算综合排名
        $arrUserFinalScore = array();
        foreach ($arrRankInfos as $key => $tmpArrRank){
            if ($key == Service_Rank_Rank::QUARTERLY_RANK_FINAL_MARK){
                continue;
            }
            foreach ($tmpArrRank as $index => $item){
                $intUserId = $item['member'];
                $intScore = $item['score'];
                $intRank = $index + 1;
                $arrUserFinalScore[$intUserId][] = $intRank;
            }
        }
        // 只参加一个赛程的
        $arrLastUser = array();

        $arrUserRankScore = array();
        foreach ($arrUserFinalScore as $tmpUserId => $tmpArrRank){
            $intCount = count($tmpArrRank);
            if ($intCount == 1){
                // 1个 排末尾
                $arrLastUser[] = $tmpUserId;
                continue;
            }
            if ($intCount == 2){
                // 2个 直接算
                $floatRankScore = ($tmpArrRank[0] + $tmpArrRank[1]) / 2;
                $arrUserRankScore[$tmpUserId] = $floatRankScore;
                continue;
            }
            if ($intCount == 3){
                // 3个，去掉最大值
                sort($tmpArrRank);
                $floatRankScore = ($tmpArrRank[0] + $tmpArrRank[1]) / 2;
                $arrUserRankScore[$tmpUserId] = $floatRankScore;
                continue;
            }
        }
        $floatMaxRank = max($arrUserRankScore);

        asort($arrUserRankScore);

        // 排名分数相同必须按照 总T豆分高的排在前面
        $arrRankKey = array();
        foreach ($arrUserRankScore as $tmpUserId => $tmpScoreKey){
            // 这里float不能直接做数组键名，先转成string
            $arrRankKey[strval($tmpScoreKey)][] = $tmpUserId;
        }
        $arrDuplicateUserRank = array();
        foreach ($arrRankKey as $tmpScoreKey => $tmpArrUserId){
            if (count($tmpArrUserId) == 1){
                unset($arrRankKey[$tmpScoreKey]);
                continue;
            }
            // 这里$arrRankKey里的都是重复的了
            $arrDuplicateScore = array();
            foreach ($tmpArrUserId as $tmpUserId){
                $arrDuplicateScore[$tmpUserId] = $arrUserAllScore[$tmpUserId];
            }
            arsort($arrDuplicateScore);
            $arrDuplicateScore = array_keys($arrDuplicateScore);
            $arrDuplicateUserRank[$tmpScoreKey] = $arrDuplicateScore;
        }

        $arrUserFinalRank = array();
        $arrDuplicateMark = array();
        foreach ($arrUserRankScore as $tmpUserId => $floatRankScore){
            if (empty($arrDuplicateUserRank[$floatRankScore])){
                $arrUserFinalRank[] = array(
                    'member' => $tmpUserId,
                    'score' => $floatRankScore,
                );
            }else{
                if (!in_array($floatRankScore,$arrDuplicateMark)){
                    foreach ($arrDuplicateUserRank[$floatRankScore] as $tmpDuplicateUserId){
                        $arrUserFinalRank[] = array(
                            'member' => $tmpDuplicateUserId,
                            'score' => $floatRankScore,
                        );
                    }
                    $arrDuplicateMark[] = $floatRankScore;
                }
            }
        }

        $arrLastUserRank = array();
        foreach ($arrLastUser as $tmpUserId){
            $intRank = $arrUserFinalScore[$tmpUserId][0];
            $arrLastUserRank[$tmpUserId] = $intRank;
        }
        asort($arrLastUserRank);
        // 把只有一个的排在后面，按单次排名好的在前
        $intI = 1;
        foreach ($arrLastUserRank as $tmpUserId => $tmpRank){
            $arrUserFinalRank[] = array(
                'member' => $tmpUserId,
                'score' => $floatMaxRank + $intI,
            );
            $intI++;
        }

        if ($intWrite){
            // 写入redis
            // 1. 写入季度之星
            $arrServiceInput = array(
                'star_info' => $arrStarUser,
            );
            $strServiceName   = "ala";
            $strServiceMethod = "setQuarterlyAnchorStar";
            $arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
            if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
                $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
            }
            var_dump($arrOutput);

            // 2.写入综合榜
            $arrServiceInput = array(
                'members' => $arrUserFinalRank,
            );
            $strServiceName   = "ala";
            $strServiceMethod = "setQuarterlyAnchorFinalRank";
            $arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
            if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
                $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
            }
            var_dump($arrOutput);

        }
        return true;
    }
}

$objRank = new quarterlyAnchorFinalRank();
$objRank->run(0);