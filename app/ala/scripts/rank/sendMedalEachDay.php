<?php
/**
 * Created by PhpStorm.
 * User: zhoujunhui01
 * Date: 2019-04-04
 * Time: 13:42
 */

define('ROOT_PATH', '/home/<USER>/orp');
define('DATA_PATH', ROOT_PATH . '/data');
define('IS_ORP_RUNTIME', true);
Tieba_Init::init('ala');

set_time_limit(0);

class sendMedalEachDay
{
    const SERVICE_NAME = "service_livemis"; // group.haokan-inrouter.SUPERPAGE.all
    const SERVICE_NAME1 = "service_livemis1"; // zhibo-7237.orp.all
    const METHOD_LIVEMIS_MEDAL_ADD_AWARD = "/liveamis/medal/addaward";

    //勋章 id
    const DAY_MEDAL_ONE_ID = '100000005';//日榜第一名勋章 id
    const DAY_MEDAL_TWO_ID = '100000006';//日榜第二名勋章 id
    const DAY_MEDAL_THREE_ID = '100000007';//日榜第三名勋章 id
    const WEEK_MEDAL_ONE_ID = '100000002';//周榜第一名勋章 id
    const WEEK_MEDAL_TWO_ID = '100000003';//周榜第二名勋章 id
    const WEEK_MEDAL_THREE_ID = '100000004';//周榜第三名勋章 id


    /**
     * 每天0点5分左右执行，查询昨日 日榜和周榜(如果昨日是周日) 金主 top3
     * 并下发对应成就勋章，日榜勋章，有效期：一天；周榜勋章有效期：一周
     * @param $intDayTime
     * @return bool
     */
    public static function run($intDayTime) {
        // 获取指定日期的日榜
        $intDayKey  = intval(date('Ymd', $intDayTime));
        $intWeekKey = intval(date('YW', $intDayTime));

        //请求service
        $arrServiceInput = array(
            'type'      => 3,//金主
            'time_type' => 1,//日榜
            'date'      => $intDayKey,
            'offset'    => 0,
            'limit'     => 3, //获取前三名
            'merge'     => Libs_Define_Rank::MERGE_TYPE_QUANMIN_HAOKAN,
            'order_item'=> 'point',
            'order'     => 'DESC',

        );

        // 调取排行榜列表服务
        $strServiceName   = "ala";
        $strServiceMethod = "rankGetHistoryRankList";
        $arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call ala rankGetHistoryRankList fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);

            return false;
        }

        /*
         * {user_id: "678321", medal_id: "100000039", effective_time: "1553961600", invalid_time: ""}
            effective_time: "1553961600" 生效时间 当前时刻
            invalid_time: ""  失效时间晚上23：59：59
            medal_id: "100000039" 日榜勋章 id 要确定
            user_id: "678321"  金主 id
         */
        //日榜，周榜勋章 ID 数组
        $arrDayMedalIds = array(self::DAY_MEDAL_ONE_ID,self::DAY_MEDAL_TWO_ID,self::DAY_MEDAL_THREE_ID);
        $arrWeekMedalIds = array(self::WEEK_MEDAL_ONE_ID,self::WEEK_MEDAL_TWO_ID,self::WEEK_MEDAL_THREE_ID);

        //日榜勋章失效时间
        $dayEnd= strtotime(date('Ymd 23:59:59', time()));
        $effectiveTime = time();

        foreach ($arrOutput['data'] as $k => $v){

            $arrInput = array(
                'user_id' => $v['user_id'],
                'effective_time' => $effectiveTime,
                'invalid_time' => $dayEnd,
                'medal_id' => $arrDayMedalIds[$k],
            );
            self::sendMedal($arrInput);
        }

        // 若昨天是周日，下发周榜勋章
        if (0 == date('w', $intDayTime)) {

            $arrServiceReq = array(
                'type'      => 3,//金主
                'time_type' => 2,//周榜
                'date'      => $intWeekKey,
                'offset'    => 0,
                'limit'     => 3, //获取前三名
                'merge'     => Libs_Define_Rank::MERGE_TYPE_QUANMIN_HAOKAN,
                'order_item'=> 'point',
                'order'     => 'DESC',
            );

            // 调取排行榜列表服务
            $strServiceName   = "ala";
            $strServiceMethod = "rankGetHistoryRankList";
            $arrOutputData    = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceReq, null, null, "post", null, "utf-8");

            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__."::".__FUNCTION__." call ala rankGetHistoryRankList fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
                return false;
            }

            //金主周榜前三名
            $tt = self::this_sunday();
            $weekEnd= strtotime(date('Ymd 23:59:59', $tt));//周榜失效时间
            foreach ($arrOutputData['data'] as $key => $val){

                $arrInputParam = array(
                    'user_id' => $val['user_id'],
                    'effective_time' => $effectiveTime,
                    'invalid_time' => $weekEnd,
                    'medal_id' => $arrWeekMedalIds[$key],
                );
                self::sendMedal($arrInputParam);
            }
        }
        return true;
    }


    /**
     * 下发勋章方法
     * @param $intDayKey
     * @return array
     */
    private static function sendMedal($arrInput){

        $medalId = $arrInput['medal_id'];
        $userId = $arrInput['user_id'];
        $invalidTime = $arrInput['invalid_time'];
        $effectiveTime = $arrInput['effective_time'];

        $arrParam = array(
            'medal_id' => $medalId,
            'user_id'  => $userId,
            'invalid_time'=> $invalidTime,
            'effective_time'=> $effectiveTime
        );

        $arrReq = array(
            "pathinfo" => self::METHOD_LIVEMIS_MEDAL_ADD_AWARD,
            'host' => 'sv.baidu.com',
            "querystring" => http_build_query($arrParam)
        );

        $strResp = ral(self::SERVICE_NAME, 'GET', array(), array(), $arrReq);
        $arrResp = empty($strResp) ? false : json_decode($strResp, true);

        if (false === $arrResp || Alalib_Conf_Error::ERR_SUCCESS != $arrResp["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call service_livemis fail. input:[" . serialize($arrReq) . "]; output:[" . $strResp . "]";
            Bingo_Log::warning($strLog);
        } else {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call service_livemis ok. input:[" . serialize($arrReq) . "]; output:[" . $strResp . "]";
            Bingo_Log::warning($strLog);
        }

        return true;
    }

    /**
     * 计算本周日是几号
     * @param int $timestamp
     * @param bool $is_return_timestamp
     * @return mixed
     */
    private static function this_sunday($timestamp=0,$is_return_timestamp=true){
        static $cache ;
        $id = $timestamp.$is_return_timestamp;
        if(!isset($cache[$id])){
            if(!$timestamp) {$timestamp = time();}
            $sunday = self::this_monday($timestamp) + 518400;//6*86400
            if($is_return_timestamp){
                $cache[$id] = $sunday;
            }else{
                $cache[$id] = date('Y-m-d',$sunday);
            }
        }
        return $cache[$id];
    }

    /**
     * @param int $timestamp
     * @param bool $is_return_timestamp
     * @return mixed
     */
    private static function this_monday($timestamp=0,$is_return_timestamp=true){
        static $cache ;
        $id = $timestamp.$is_return_timestamp;
        if(!isset($cache[$id])){
            if(!$timestamp) {$timestamp = time();}
            $monday_date = date('Y-m-d', $timestamp-86400*date('w',$timestamp)+(date('w',$timestamp)>0?86400:-518400));//6*86400
            if($is_return_timestamp){
                $cache[$id] = strtotime($monday_date);
            }else{
                $cache[$id] = $monday_date;
            }
        }
        return $cache[$id];
    }
}

// 每天跑一次，需要设置为 0:00以后一点时间跑，跑昨天的
$intDayTime = strtotime(date('Y-m-d', strtotime('-1 day')));
$medal = new sendMedalEachDay();
$medal->run($intDayTime);