<?php
/**
 *
 * <AUTHOR>
 * @time 2020/6/29
 */

define('ROOT_PATH', '/home/<USER>/orp');
define('IS_ORP_RUNTIME', true);
define('APP_NAME', 'ala');
Tieba_Init::init('ala');
set_time_limit(0);
date_default_timezone_set("Asia/Chongqing");

class ActivityStockHandle
{
    private $activityId;
    private $handleType;
    private $componentIndex;
    private $sysTime;

    /**
     * 回收上一个小时库存
     */
    const HANDLE_TYPE_RECOVERY = 0;
    /**
     * 初始化库存
     */
    const HANDLE_TYPE_INIT     = 1;

    /**
     * 商城初始化库存
     */
    const HANDLE_TYPE_MALL_INIT = 2;

    /**
     * ActivityStockHandle constructor.
     *
     * @param int $activityId
     * @param int $handleType 默认0 将上一小时放进本小时库存
     * @param int $componentIndex 默认 1 第一个组件
     * @param int $sysTime 默认当前时间
     */
    public function __construct($activityId = 0, $handleType = 0, $componentIndex = 1, $sysTime = 0)
    {
        $this->activityId     = $activityId;
        $this->handleType     = $handleType;
        $this->componentIndex = $componentIndex;
        $this->sysTime        = $sysTime;
    }

    /**
     * 运行入口
     */
    public function run()
    {
        if (empty($this->sysTime)) {
            $this->sysTime = time();
        }
        if (empty($this->activityId)) {
            echo "empty activityId!";
            exit(1);
        }
        if (empty($this->handleType)) {
            $this->handleType = self::HANDLE_TYPE_RECOVERY;
        }
        $arrActivityConf = Libs_Util_ActivityRank::getConfigByActivityId($this->activityId)['live_activity_new']['activity_info'][0];
        if (empty($arrActivityConf)) {
            Bingo_Log::fatal('[ComponentMonitor][getConfigByActivityId failed] ' . $this->activityId . ' ' . $this->handleType . ' ' . $this->sysTime);
            exit(1);
        }
        $ret = [];
        if ($this->componentIndex) {
            if ($this->handleType == self::HANDLE_TYPE_INIT) {
                $ret = $this->runStockInit($arrActivityConf, $this->componentIndex);
            } elseif ($this->handleType == self::HANDLE_TYPE_MALL_INIT) {
                $ret = Service_Rank_Viewer_ActivityMall::goodsStockInit(['activity_id' => $this->activityId]);
            } else {
                $ret = $this->runStockRecovery($arrActivityConf, $this->componentIndex, $this->sysTime);
            }
        } else {
            foreach ($arrActivityConf['component_conf_list'] as $component) {
                $intComponentIndex = $component['index'];
                if ($this->handleType == self::HANDLE_TYPE_INIT) {
                    $ret = $this->runStockInit($arrActivityConf, $intComponentIndex, $this->sysTime);
                } elseif ($this->handleType == self::HANDLE_TYPE_MALL_INIT) {
                    $ret = Service_Rank_Viewer_ActivityMall::goodsStockInit(['activity_id' => $this->activityId]);
                } else {
                    $ret = $this->runStockRecovery($arrActivityConf, $intComponentIndex, $this->sysTime);
                }
            }
        }
        return $ret;
    }

    /**
     * 初始化库存
     * @param $arrActivityConf
     * @param $intComponentIndex
     *
     * @return array
     */
    private function runStockInit($arrActivityConf, $intComponentIndex)
    {
        $result = [];
        //防止库存重复初始化
        $lockKey = 'activity_runStockInit_lock#' . $arrActivityConf['activity_id'];
        $res = Service_Rank_Component_BaseComponent::getSnxLock($lockKey, 86400*10);
        if (1 !== $res) {return $result;}
        $intActivityId = $arrActivityConf['activity_id'];

        $iQYExchangeCodeList = Libs_Define_ActivityIQYcode::$iQYExchangeCodeList;
        $activeComponentConf = $arrActivityConf['component_conf_list'][$intComponentIndex - 1];
        if (empty($activeComponentConf)) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' getActiveComponentConf fail. input: ' . json_encode([$arrActivityConf]));
            return $result;
        }
        $iQYStockConf = $activeComponentConf['iqy_stock_conf'];
        foreach ($iQYStockConf as $iQYStockConfHour => $iQYStockConfHourNum) {
            $strStockKey = Service_Rank_Component_MassOrgComp::getGiftStockKey($intActivityId, $intComponentIndex, $iQYStockConfHour);
            $arrInput = [
                'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
                'key' => $strStockKey
            ];
//            Util_Redis::clearFromRedis($arrInput); //测试时清除库存
            $arrOutput = Util_Redis::LLEN($arrInput);
            if ($arrOutput[$strStockKey] > 0) {
                continue;
            }
            $codes = array_splice($iQYExchangeCodeList, 0, $iQYStockConfHourNum);
            $stockInput = [
                'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
                'key' => $strStockKey,
                'value' => $codes,
            ];

            $stockOutput = Util_Redis::RPUSH($stockInput);
            if (false === $stockOutput) {
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' Util_Redis::RPUSH fail! ' . serialize($stockInput));
                echo '[fail] initstock push error ' . ' ' . $strStockKey . PHP_EOL;
                $result[] = $strStockKey;
            }
        }
        return $result;
    }

    /**
     * 将上一小时放进本小时库存
     * @param $arrActivityConf
     * @param $intComponentIndex
     * @param $intSystemTime
     *
     * @return array
     */
    private function runStockRecovery($arrActivityConf, $intComponentIndex, $intSystemTime)
    {
        if ($intSystemTime < $arrActivityConf['begin_time'] || $intSystemTime > $arrActivityConf['end_time']) {
            return ['msg' => '不在活动时间内'];
        }
        $intActivityId = $arrActivityConf['activity_id'];
        $intLastHour = intval($intSystemTime) - 3600;
        //上一个小时
        $strLastHour = date("YmdH",$intLastHour);

        $strCurrSystemTime = date("YmdH",$intSystemTime);
        //每小时0秒
        $intSystemTimeInit = strtotime($strCurrSystemTime . '0000');
        if ($intSystemTime - $intSystemTimeInit < 10) {
            return ['msg' => '每小时前10秒不执行'];
        }
        //防止库存回收重复
        $lockKey = 'activity_runStockRecovery_lock#' . $arrActivityConf['activity_id'] .  "_" . $strCurrSystemTime;
        $res = Service_Rank_Component_BaseComponent::getSnxLock($lockKey, 86400*10);
        if (1 !== $res) {return ['msg' => '重复执行'];}
        $strLastStockKey = Service_Rank_Component_MassOrgComp::getGiftStockKey($intActivityId, $intComponentIndex, $strLastHour);
        $arrLastStockInput = [
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
            'key' => $strLastStockKey
        ];
        $arrOutput = Util_Redis::lget($arrLastStockInput);

        if (count($arrOutput) <= 0) {
            return ['msg' => '前一小时无库存'];
        }
        //将上一小时剩余库存放进当前小时
        $strCurrStockKey = Service_Rank_Component_MassOrgComp::getGiftStockKey($intActivityId, $intComponentIndex, $strCurrSystemTime);
        $arrCurrStockInput = [
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
            'key' => $strCurrStockKey,
            'value' => $arrOutput,
        ];

        $stockOutput = Util_Redis::RPUSH($arrCurrStockInput);
        if (false === $stockOutput) {
            Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' Util_Redis::RPUSH fail! ' . serialize($arrCurrStockInput));
            echo '[fail] recoverystock push error ' . ' ' . $strCurrStockKey . PHP_EOL;
        }
        //清除上一小时list
        Util_Redis::clearFromRedis($arrLastStockInput);

        return [];
    }
}

$activityId     = isset($argv[1]) ? intval($argv[1]) : 0;
$handleType     = isset($argv[2]) ? intval($argv[2]) : 0;
$componentIndex = isset($argv[3]) ? intval($argv[3]) : 1;
$sysTime        = isset($argv[4]) ? intval($argv[4]) : 0;

$proc = new ActivityStockHandle($activityId, $handleType, $componentIndex, $sysTime);
$r = $proc->run();
var_dump($r);