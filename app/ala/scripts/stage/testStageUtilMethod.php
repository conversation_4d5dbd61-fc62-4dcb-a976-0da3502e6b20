<?php
/**
 * testStageUtilMethod.php 测试Libs_Util_Stage的方法
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 17/7/11 上午10:44
*/

define('ROOT_PATH', '/home/<USER>/orp');
define('DATA_PATH', ROOT_PATH . '/data');
define('IS_ORP_RUNTIME', true);
Tieba_Init::init('ala');

set_time_limit(0);
ini_set('memory_limit', '-1');

if (empty($argv[1]) || 'debug' != $argv[1]) {
    // debug模式，可传入Libs_Util_Stage的方法、参数
    // php -f ~/orp/app/ala/scripts/stage/testStageUtilMethod.php 'debug' '{$strMethodName}' '{$strMethodParam1}' '{$strMethodParam2}' '{$strMethodParam3}' '...'
    // php -f ~/orp/app/ala/scripts/stage/testStageUtilMethod.php 'debug' 'onLiveStart' '2502481582' '942180'
    // php -f ~/orp/app/ala/scripts/stage/testStageUtilMethod.php 'debug' 'onLiveClose' '2138862954'
    if (empty($argv[2])) {
        echo 'Need to pass argv[2]: php -f ~/orp/app/ala/scripts/stage/testStageUtilMethod.php \'debug\' \'{$strMethodName}\' \'{$strMethodParam1}\' \'{$strMethodParam2}\' \'{$strMethodParam3}\' \'...\'';
        echo "\r\n";
        echo 'Note: If Method Param is array, use json_encode() to transfer array to string to pass';
        exit(0);
    }
}

// 执行
$obj = new testStageUtilMethod($argv);
$obj->run($argv);

/**
 * Class testStageUtilMethod
 */
class testStageUtilMethod
{
    public $intStartTime = 0;

    /**
     * constructor.
     */
    public function __construct() {
        $intStartTime = $this->intStartTime = time();
        $this->debugInfo(__CLASS__.'::'.__FUNCTION__.' start at:'.$intStartTime);
        Bingo_Log::warning(__Class__.": start at ".time());
    }

    /**
     * 执行
     * @param array $argv
     * @return
     */
    public function run($argv) {
        $strScriptName   = array_shift($argv);
        $strDebug        = array_shift($argv);
        $strMethodName   = array_shift($argv);
        $arrMethodParams = $argv;

        foreach ($arrMethodParams as $k=>$v) {
            if (null !== json_decode($v)) {
                $arrMethodParams[$k] = json_decode($v);
            }
        }

        $result = call_user_func_array(array('Libs_Util_Stage', $strMethodName), $arrMethodParams);

        $this->debugInfo(__CLASS__.'::'.__FUNCTION__." Call $strMethodName Params: ".serialize($arrMethodParams));
        $this->debugInfo("Result: ".$result);
    }

    /**
     * debug info
     * @param $str
     */
    public function debugInfo($str) {
        echo $str."\r\n";
    }

    /**
     * destructor.
     */
    public function __destruct() {
        $intEndTime = time();
        $intUseTime = $intEndTime - $this->intStartTime;
        $this->debugInfo(__CLASS__.'::'.__FUNCTION__.' end at:'.$intEndTime.' total:'.$intUseTime);
        Bingo_Log::warning(__Class__.": end at ".$intEndTime.' total:'.$intUseTime);
    }
}