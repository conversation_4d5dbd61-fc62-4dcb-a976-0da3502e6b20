<?php 
/**
 * 
 * 处理异常直播Session
 *  Live Avaliable Session
 * + 0，session 有流， 直播 进行中
 * - 1，session 有流，直播已结束
 * + 2，session 无流，处于有效期，直播 进行中
 * - 3，session 无流，处于有效期，直播已结束
 * - 4，session 无流，处于失效期，直播进行中
 * - 5，Session 无流，处于失效期，直播已结束
 * 
 * 以session为维度进行处理
 * */
define("MODULE","Ala_service");
define('IS_ORP_RUNTIME',true);
define ('MODULE_NAME', 'ala');
define('ROOT_PATH', dirname(__FILE__).'/../../../../');
include("./app/ala/dl/ala/Base.php");
include("./app/ala/dl/ala/User.php");
include("./app/ala/dl/ala/Live.php");
include("./app/ala/service/lss/Lss.php");
include("./app/ala/libs/define/Live.php");
include("./app/ala/libs/define/User.php");
include("./app/ala/util/Redis.php");
echo "init" ;
$arrInput = array();
$arrOutput = Tieba_Service::call("ala","liveProcessLiveStatus",$arrInput);
if(false == $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
    $strLog = __CLASS__."::".__FUNCTION__." call ala liveProcessLiveStatus fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
    echo $strLog;
    return false;
}
echo "done" ;
