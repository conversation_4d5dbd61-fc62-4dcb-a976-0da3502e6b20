<?php
/**
 * HotRecommend.php 广场热门推荐成就
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 17/11/6 下午4:08
*/

/**
 * Class Libs_Util_Live_Achievement_HotRecommend
 */
class Libs_Util_Live_Achievement_HotRecommend extends Libs_Util_Live_Achievement_Prototype
{
    protected $intAchievementId = Libs_Define_LiveAchievement::TYPE_HOT_RECOMMEND;

    const TREND_DOWN = 0;
    const TREND_UP   = 1;

    /**
     * 是否发送弹幕
     * @param array $arrAchievementInfo
     * @param array $arrExtAttr
     * @return bool
     */
    protected function isSendDanmu($arrAchievementInfo, $arrExtAttr) {
        // 升到第一时，发弹幕
        if ($arrExtAttr['hot_recommend'] == 1) {
            return true;
        }

        // 第一次进前8，发弹幕
        if ($arrExtAttr['is_first_time'] == 1) {
            return true;
        }

        return false;
    }

    /**
     * 扩展成就扩展属性
     * @param array $arrAchievementInfo
     * @param array $arrExtAttr
     * @param int $intLiveId
     * @return array
     */
    protected function extendAchievementAttr($arrAchievementInfo, $arrExtAttr, $intLiveId) {
        $strTrendSign = $arrExtAttr['trend'] == self::TREND_UP ? '↑' : '↓';

        $intHotTimeSeconds = $arrExtAttr['hot_time'];

        if ($intHotTimeSeconds >= 3600) {
            // 1小时以上
            $intHour = (int)floor($intHotTimeSeconds/3600);
            $intMin  = (int)floor($intHotTimeSeconds % 3600 / 60);
            $strHotTime = $intHour.'小时'.$intMin.'分钟';
        } else {
            $intMin = (int)floor($intHotTimeSeconds % 3600 / 60);
            $intMin = $intMin ? $intMin : 1;
            $strHotTime = $intMin.'分钟';
        }

        $arrNewAttr = array(
            'trend_sign'   => $strTrendSign,
            'hot_time_str' => $strHotTime,
        );

        return $arrNewAttr;
    }

    /**
     * 获取热门推荐列表，按照规则发放成就
     * @param array $arrThreadList 测试用的数据
     * @return bool
     */
    public static function runHotRecommendAchievement($arrThreadList = array()) {
        if (empty($arrThreadList)) {
            // #获取当前广场二级tab最热直播贴前8
            $intPn = 0;
            $intPs = 8;

            $arrInput = array(
                'pn' => $intPn,
                'ps' => $intPs,
            );

            $arrOut = Util_Service::call('recommendGetHotLiveThreadsForSubTab', $arrInput);

//            Util_Common::debugInfo(__CLASS__.'::'.__FUNCTION__.' $arrOut:'.serialize($arrOut));

            if (false === $arrOut) {
                return false;
            }

            $arrThreadList = $arrOut['thread_list'];
        }

        Util_Common::debugInfo(__CLASS__.'::'.__FUNCTION__.' $arrThreadList:'.serialize($arrThreadList));

        $arrLiveIds = array();
        foreach ($arrThreadList as $arrThreadInfo) {
            $arrLiveIds[] = $arrThreadInfo['ala_live_attr']['live_id'];
        }

        // #获取上次记录的热门列表
        $strRedisKey = __CLASS__.'_'.__FUNCTION__;

        $arrSTSRedisInput = array(
            'key' => $strRedisKey,
        );

        $arrLastRecordList = Util_STS::callRedis('liveAchievementRecord')->getFromRedis($arrSTSRedisInput);

        if (false === $arrLastRecordList) {
            return false;
        }

        Util_Common::debugInfo(__CLASS__.'::'.__FUNCTION__.' $arrLastRecordList:'.serialize($arrLastRecordList));

        $arrLastLiveIds = $arrLastLiveListMap = array();
        foreach ($arrLastRecordList as $arrRecordInfo) {
            $arrLastLiveIds[] = $arrRecordInfo['live_id'];

            $arrLastLiveListMap[$arrRecordInfo['live_id']] = $arrRecordInfo;
        }

        // ##之前已加入的直播间，计算升降度
        $arrExistingLiveIds = array_intersect($arrLastLiveIds, $arrLiveIds);

        Util_Common::debugInfo(__CLASS__.'::'.__FUNCTION__.' $arrExistingLiveIds:'.serialize($arrExistingLiveIds));

        $intNowTime = Bingo_Timer::getNowTime();

        $arrNeedNotice = $arrLiveExtAttr = array();
        foreach ($arrExistingLiveIds as $intLiveId) {
            // rank从1开始计数
            $intThisRank = array_search($intLiveId, $arrLiveIds) + 1;
            $intLastRank = array_search($intLiveId, $arrLastLiveIds) + 1;

            $bolNeedNotice = true;

            if ($intThisRank < $intLastRank) {
                // 上升
                $intTrend = self::TREND_UP;
            } else if ($intThisRank > $intLastRank) {
                // 下降
                $intTrend = self::TREND_DOWN;
            } else {
                // 与之前的齐平，仍使用之前的趋势，不需要发送成就通知
                $intTrend = $arrLastLiveListMap[$intLiveId]['trend'];
                $bolNeedNotice = false;
            }

            // 历史最佳排名
            $intBestRank = $arrLastLiveListMap[$intLiveId]['hot_recommend_best'];

            if (empty($intBestRank)) {
                $intBestRank = $intThisRank;
            } else {
                $intBestRank = min($intBestRank, $intThisRank);
            }

            // 占领热门时间
            $intLastHotTime = $arrLastLiveListMap[$intLiveId]['hot_time'];
            // 上次更新的热门时间
            $intLastHotTimeStamp = $arrLastLiveListMap[$intLiveId]['hot_time_stamp'];

            // 本次占领的时间计算
            $intHotTime = 1;
            if ($intLastHotTimeStamp) {
                $intHotTime = $intNowTime - $intLastHotTimeStamp + $intLastHotTime;
            }

            $arrLiveExtAttr[$intLiveId] = array(
                'is_first_time'      => 0,
                'hot_recommend'      => $intThisRank,
                'trend'              => $intTrend,
                'hot_time'           => $intHotTime,
                'hot_recommend_best' => $intBestRank,
            );

            if ($bolNeedNotice) {
                $arrNeedNotice[$intLiveId] = $arrLiveExtAttr[$intLiveId];
            }
        }

        // ##首次加入的直播间，均需要发送通知
        $arrFirstJoinedLiveIds = array_diff($arrLiveIds, $arrLastLiveIds);

        Util_Common::debugInfo(__CLASS__.'::'.__FUNCTION__.' $arrFirstJoinedLiveIds:'.serialize($arrFirstJoinedLiveIds));

        foreach ($arrFirstJoinedLiveIds as $intLiveId) {
            // rank从1开始计数
            $intThisRank = array_search($intLiveId, $arrLiveIds) + 1;
            $intBestRank = $intThisRank;
            $intTrend    = self::TREND_UP;
            $intHotTime  = 1;

            $arrLiveExtAttr[$intLiveId] = array(
                'is_first_time'      => 1,
                'hot_recommend'      => $intThisRank,
                'trend'              => $intTrend,
                'hot_time'           => $intHotTime,
                'hot_recommend_best' => $intBestRank,
            );

            $arrNeedNotice[$intLiveId] = $arrLiveExtAttr[$intLiveId];
        }

        Util_Common::debugInfo(__CLASS__.'::'.__FUNCTION__.' $arrNeedNotice:'.serialize($arrNeedNotice));

        // ###发送获得成就通知
        $objAchievement = new self();

        foreach ($arrNeedNotice as $intLiveId => $arrExtAttr) {
            $objAchievement->getNewAchievement($intLiveId, $arrExtAttr);
            usleep(1000);
        }

        // ##跌出热门的直播间
        $arrDisappearedLiveIds = array_diff($arrLastLiveIds, $arrLiveIds);

        $arrDisappearedNotice = array();
        foreach ($arrDisappearedLiveIds as $intLiveId) {
            // 上次更新的热门时间
            $intLastHotTimeStamp = $arrLastLiveListMap[$intLiveId]['hot_time_stamp'];

            // 判断跌出热门是否超过30秒，超过30秒则发送成就消失通知
            if ($intNowTime - $intLastHotTimeStamp >= 30) {
                $arrDisappearedNotice[] = array(
                    'live_id' => $intLiveId,
                );
            }
        }

        Util_Common::debugInfo(__CLASS__.'::'.__FUNCTION__.' $arrDisappearedNotice:'.serialize($arrDisappearedNotice));

        // ###发送取消成就通知
        foreach ($arrDisappearedNotice as $v) {
            $objAchievement->cancelAchievement($v['live_id']);
            usleep(1000);
        }

        // ####更新本次记录
        $arrUpdateList = array();
        foreach ($arrLiveIds as $intThisRank => $intLiveId) {
            // rank从1开始计数
            $intThisRank = $intThisRank + 1;

            $arrUpdateList[] = array(
                'live_id'            => $intLiveId,
                'hot_recommend'      => $intThisRank,
                'trend'              => $arrLiveExtAttr[$intLiveId]['trend'],
                'hot_time'           => $arrLiveExtAttr[$intLiveId]['hot_time'],
                'hot_recommend_best' => $arrLiveExtAttr[$intLiveId]['hot_recommend_best'],
                'hot_time_stamp'     => $intNowTime,
            );
        }

        Util_Common::debugInfo(__CLASS__.'::'.__FUNCTION__.' $arrUpdateList:'.serialize($arrUpdateList));

        $arrSTSRedisInput = array(
            'key'   => $strRedisKey,
            'value' => $arrUpdateList,
        );

        $arrOut = Util_STS::callRedis('liveAchievementRecord')->setToRedis($arrSTSRedisInput);

        if (false === $arrOut) {
            return false;
        }

        return true;
    }
}