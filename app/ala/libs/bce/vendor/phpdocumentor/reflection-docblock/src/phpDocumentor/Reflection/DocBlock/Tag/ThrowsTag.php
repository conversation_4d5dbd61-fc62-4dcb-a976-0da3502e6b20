<?php
/**
 * phpDocumentor
 *
 * PHP Version 5.3
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2010-2011 <PERSON> / Naenius (http://www.naenius.com)
 * @license   http://www.opensource.org/licenses/mit-license.php MIT
 * @link      http://phpdoc.org
 */

namespace phpDocumentor\Reflection\DocBlock\Tag;

/**
 * Reflection class for a @throws tag in a Docblock.
 *
 * <AUTHOR> <<EMAIL>>
 * @license http://www.opensource.org/licenses/mit-license.php MIT
 * @link    http://phpdoc.org
 */
class ThrowsTag extends ReturnTag
{
}
