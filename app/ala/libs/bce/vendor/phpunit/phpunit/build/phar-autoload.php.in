#!/usr/bin/env php
<?php
if (__FILE__ == realpath($GLOBALS['_SERVER']['SCRIPT_NAME'])) {
    $phar    = realpath($GLOBALS['_SERVER']['SCRIPT_NAME']);
    $execute = true;
} else {
    $files   = get_included_files();
    $phar    = $files[0];
    $execute = false;
}

define('__PHPUNIT_PHAR__', str_replace(DIRECTORY_SEPARATOR, '/', $phar));
define('__PHPUNIT_PHAR_ROOT__', 'phar://___PHAR___');

spl_autoload_register(
  function ($class)
  {
      static $classes = NULL;

      if ($classes === NULL) {
          $classes = array(
            ___CLASSLIST___
          );
      }

      $class = strtolower($class);

      if (isset($classes[$class])) {
          require __PHPUNIT_PHAR_ROOT__ . $classes[$class];
      }
  }
);

Phar::mapPhar('___PHAR___');

if ($execute) {
    if (isset($_SERVER['argv'][1]) && $_SERVER['argv'][1] == '--manifest') {
        print file_get_contents(__PHPUNIT_PHAR_ROOT__ . '/manifest.txt');
        exit;
    }

    PHPUnit_TextUI_Command::main();
}

__HALT_COMPILER();
