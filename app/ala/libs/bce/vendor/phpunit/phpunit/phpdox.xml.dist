<?xml version="1.0" encoding="utf-8" ?>
<phpdox xmlns="http://phpdox.net/config">
 <project name="PHPUnit" source="src" workdir="build/phpdox">
  <collector publiconly="false">
   <include mask="*.php" />
  </collector>

  <generator output="build">
   <enrich base="${basedir}/build/logs">
    <source type="build" />
    <source type="git" />
    <source type="phploc" />
    <source type="checkstyle" />
    <source type="pmd" />
   </enrich>

   <build engine="html" enabled="true" output="api">
    <file extension="html" />
   </build>
  </generator>
 </project>
</phpdox>

