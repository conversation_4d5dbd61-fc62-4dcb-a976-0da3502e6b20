<?php

/**
 * Class Libs_Define_Rank
 */
class Libs_Define_Rank
{
    /**
     * 魅力榜
     */
    const TYPE_CHARM  = 1;

    /**
     * 游戏榜
     */
    const TYPE_GAME   = 2;

    /**
     * 富豪榜
     */
    const TYPE_RICH   = 3;

    /**
     * 鲜花榜
     */
    const TYPE_FLOWER = 4;

    /**
     * 热度榜
     */
    const TYPE_HOT    = 5;

    /**
     * PK榜
     */
    const TYPE_PK     = 6;

    /**
     * PK榜
     */
    const TYPE_GUARD     = 7;

    /**
     * 饭团榜
     */
    const TYPE_FAMILY     = 8;

    /**
     * 偶像榜
     */
    const TYPE_IDOL   = 9;

    /**
     * 日榜类型
     */
    const TIME_TYPE_DAY   = 1;

    /**
     * 周榜类型
     */
    const TIME_TYPE_WEEK  = 2;

    /**
     * 总榜类型
     */
    const TIME_TYPE_TOTAL = 3;

    /**
     * 小时榜类型
     */
    const TIME_TYPE_HOUR = 4;

    /**
     * merge类型
     */
    const MERGE_TYPE_QUANMIN_HAOKAN = 1;    // merge全民和好看榜
    const MERGE_TYPE_ALL = 1;    // merge全民，好看，贴吧，手百榜 四端sdk统一的时候，四端榜单打通
    /**
     * 实时总榜类型（仅用于直播间卡片需要显示主播的总鲜花数）
     */
    const TIME_TYPE_ALL   = 4;

    // 七夕排行榜活动时间 中秋
//    const TIME_QIXI_RANK_START_TIME = 1565884800;
//    const TIME_QIXI_RANK_END_TIME = 1566316800;
    const TIME_QIXI_RANK_START_TIME = 1568217600;//20190912 000000
    const TIME_QIXI_RANK_END_TIME = 1568649600;//20190917 000000

    const QIXI_RANK_DEFAULT_TOP_NUM = 100;

    const QIXI_RANK_REDIS_SHOW_DEFAULT_START = 0;
    const QIXI_RANK_REDIS_SHOW_DEFAULT_STOP = 99;


    // 七夕活动限定礼物  现在是中秋活动了
    // 在wap-mobile-mowsenior\actions\ala\qixiRankPageAction.php  也有，要改一起改
    public static $_arrQiXiRankGift = array(
        10620,
        10621,
        10622,
        10604,
//        10388,
//        10550,
//        10449,
//        10447,
    );

    public static $_arrQiXiRankGiftScore = array(
        10620 => 1000,
        10621 => 9900,
        10622 => 131400,
        10604 => 1314000,
//        10388 => 1000,
//        10550 => 9900,
//        10449 => 131400,
//        10447 => 1314000,
    );

    // 年终盛典 需要收集的礼物id 及数量
    public static $_arrYearRankGift = array(
        10379 => 5000,
        10343 => 1000,
        10348 => 25,
    );
    // 年终盛典 资格赛开始时间
    // 共分为三个赛程，第一赛程（10.15日00：00：00——10.31日23：59：59）；第二赛程（11月1日00：00：00——11月15日23：59：59）；
    // 第三赛程（11月16日00：00：00——11月30日23：59：59）.
    const TIME_YEAR_RANK_QUALIFICATIONS_START_TIME = 1539532800;
    const TIME_YEAR_RANK_QUALIFICATIONS_END_TIME = 1543593599;

    const TIME_YEAR_RANK_QUALIFICATIONS_P1_START_TIME = 1539532800;
    const TIME_YEAR_RANK_QUALIFICATIONS_P1_END_TIME = 1541001599;
    const TIME_YEAR_RANK_QUALIFICATIONS_P2_START_TIME = 1541001600;
    const TIME_YEAR_RANK_QUALIFICATIONS_P2_END_TIME = 1542297599;
    const TIME_YEAR_RANK_QUALIFICATIONS_P3_START_TIME = 1542297600;
    const TIME_YEAR_RANK_QUALIFICATIONS_P3_END_TIME = 1543593599;

//    年终盛典 淘汰赛开始时间
//    共分为5个阶段，5个阶段实行淘汰赛制： 第一阶段（12月01日00：00：00——12月05日23：59：59）；
//    第二阶段（12月06日00：00：00——12月10日 23：59：59）；
//    第三阶段（12月11日00：00：00——12月15日23：59：59）；
//    第四阶段（12月16日00：00：00——12月20日23：59：59）；
//    第五阶段（12月20日00：00：00——12月26日23：59：59）；
    const TIME_YEAR_RANK_OBSOLETE_START_TIME = 1543593600;
    const TIME_YEAR_RANK_OBSOLETE_END_TIME = 1545839999;

    const TIME_YEAR_RANK_OBSOLETE_P1_START_TIME = 1543593600;
    const TIME_YEAR_RANK_OBSOLETE_P1_END_TIME = 1544025599;
    const TIME_YEAR_RANK_OBSOLETE_P1_NUM = 50;
    const TIME_YEAR_RANK_OBSOLETE_P2_START_TIME = 1544025600;
    const TIME_YEAR_RANK_OBSOLETE_P2_END_TIME = 1544457599;
    const TIME_YEAR_RANK_OBSOLETE_P2_NUM = 40;
    const TIME_YEAR_RANK_OBSOLETE_P3_START_TIME = 1544457600;
    const TIME_YEAR_RANK_OBSOLETE_P3_END_TIME = 1544889599;
    const TIME_YEAR_RANK_OBSOLETE_P3_NUM = 30;
    const TIME_YEAR_RANK_OBSOLETE_P4_START_TIME = 1544889600;
    const TIME_YEAR_RANK_OBSOLETE_P4_END_TIME = 1545321599;
    const TIME_YEAR_RANK_OBSOLETE_P4_NUM = 20;
    const TIME_YEAR_RANK_OBSOLETE_P5_START_TIME = 1545321600;
    const TIME_YEAR_RANK_OBSOLETE_P5_END_TIME = 1545839999;
    const TIME_YEAR_RANK_OBSOLETE_P5_NUM = 10;

//    年度盛典 淘汰赛小时榜key  结束时间  2018-12-26 23:00:00 
    const TIME_YEAR_RANK_OBSOLETE_HOUR_TIME_STOP = 1545836400;


//    年度盛典 总决赛
//  2018-12-27 00:00:00-------2018-12-27 23:59:59
    const TIME_YEAR_RANK_OBSOLETE_FINAL_K1_START_TIME = 1545840000;
    const TIME_YEAR_RANK_OBSOLETE_FINAL_K1_END_TIME = 1545926399;
//2018-12-28 00:00:00（脚本添加主播亲友团redis）——2018-12-28 23:59:59；
    const TIME_YEAR_RANK_OBSOLETE_FINAL_K2_START_TIME = 1545926400;
    const TIME_YEAR_RANK_OBSOLETE_FINAL_K2_END_TIME = 1546012799;
//2018-12-29 00:00:00（淘汰–进8）——2018-12-29 23:59:59
    const TIME_YEAR_RANK_OBSOLETE_FINAL_K3_START_TIME = 1546012800;
    const TIME_YEAR_RANK_OBSOLETE_FINAL_K3_END_TIME = 1546099199;
//2018-12-30 00:00:00（淘汰–进6）——2018-12-30 23:59:59
    const TIME_YEAR_RANK_OBSOLETE_FINAL_K4_START_TIME = 1546099200;
    const TIME_YEAR_RANK_OBSOLETE_FINAL_K4_END_TIME = 1546185599;
//2018-12-31 00:00:00（淘汰–进4）——2018-12-31 23:59:59
    const TIME_YEAR_RANK_OBSOLETE_FINAL_K5_START_TIME = 1546185600;
    const TIME_YEAR_RANK_OBSOLETE_FINAL_K5_END_TIME = 1546271999;
//2019-01-01 00:00:00
    const TIME_YEAR_RANK_OBSOLETE_FINAL_K6_START_TIME = 1546272000;

    //工会赛结束时间
    const TIME_YEAR_RANK_OBSOLETE_UNION__END_TIME = 1546271999;

    //    年终盛典 淘汰赛--黑马赛开始时间
//    共分为3个阶段，3个阶段实行淘汰赛制： 第一阶段（12月01日00：00：00——12月04日23：59：59）；
//    第二阶段（12月06日00：00：00——12月9日 23：59：59）；
//    第三阶段（12月11日00：00：00——12月14日23：59：59）；

    const TIME_YEAR_RANK_OBSOLETE_HORSE_P1_START_TIME = 1543593600;
    const TIME_YEAR_RANK_OBSOLETE_HORSE_P1_END_TIME = 1543939199;
    const TIME_YEAR_RANK_OBSOLETE_HORSE_P2_START_TIME = 1544025600;
    const TIME_YEAR_RANK_OBSOLETE_HORSE_P2_END_TIME = 1544371199;
    const TIME_YEAR_RANK_OBSOLETE_HORSE_P3_START_TIME = 1544457600;
    const TIME_YEAR_RANK_OBSOLETE_HORSE_P3_END_TIME = 1544803199;

    // 年终盛典 淘汰赛需要收集的礼物id 及数量
    public static $_arrYearObsoleteRankGift = array(
        10296 => 30,
    );


   // 擂台赛 登上大屏需要的T豆数量限制
   const ARENA_TO_TOP_SCORE_LIMIT = 200000;

    /**
     * 直播的所有场景号，请不要混入其他产品的场景号
     * @var array
     */
    public static $arrAlaSceneType = array(
        6200003,
        6200004,
        6200005,
        6200006,
        8000001,
        8000002,
        8000003,
        8000004,
        8000005,
        8000006,
        8000019,
        8000020,
        8000021,
        8000022,
        //背包
        //quanmin
        8000037,
        8000038,
        //haokan
        8000039,
        8000040,
        //tieba
        8000041,
        8000042,
        //mobilebaidu
        8000043,
        8000044,
        8000049,
        8000050,
        8000051,
        8000052,
    );

    public static $_arrChallengeRankUsers = array(
        2528527060,
        2099882215,
        2730452833,
        670967319,
        1916945848,
        1486551568,
        3075677775,
        1486765607,
        1836101686,
        3352460807,
        296966691,
        2605314364,
        2714833269,
        2999246957,
        3343330858,
        1670619088,
        3133871457,
        2408172186,
        996658880,
        1581692690,
        3446944125,
        1545454303,
        868929777,
        1076350265,
        3001711875,
        2788041158,
        1369326068,
        3152640310,
        2620683930,
        355067730,
        2693674782,
        3394402722,
        3482358554,
        909736646,
        3477587297,
        2442506298,
        2600497055,
        1484315225,
        1924925178,
        3392471021,
    );

    // 季度赛参赛人员名单
    // 3352460807,  上线后pm又让替换掉的
    // 3355759299,  pm又又又又让替换掉了
    public static $_arrQuarterlyRankUsers = array(
        3422768292,
        1521350674,
        942865865,
        3537028041,
        896306927,
        2551354691,
        3502294758,
        1388728628,
        1560570784,
        3219334401,
        1365990643,
        2555337207,
        3543881928,
        3601842998,
        1198398086,
        1484975388,
        2393347177,
        2775723066,
        445588496,
        3645562113,
        931410386,
        1279264619,
        2936633582,
        2781971927,
        1099196276,
        1408784061,
        855972868,
        730354431,
        1608180805,
        606810770,
        2851789641,
        1280017684,
        309335814,
        3442107931,
        3513798212,
        3503594114,
        2988196547,
        2627844349,
        2724119262,
        2120997659,
        3002808417,
        691089980,
        2704993482,
        3570131646,
        1788156608,
        2601027758,
        1579237969,
        2675544328,
        813296752,
        3021627432,
        3539387488,
        3565352577,
        3542375167,
        919345566,
        3126340605,
        2527368753,
        1243462805,
        2577771079,
        1354084322,
        1744875501,
    );

    // 年终盛典 25名主播，可不参加资格赛。候选人排行榜中需要过滤
    public static $arrYearPassQuarterlyRankUsers = array(
        2099882215,
        296966691,
        670967319,
        2528527060,
        3352460807,
        1486551568,
        1836101686,
        3019802067,
        1138077716,
        1825499035,
        2560750140,
        1403443169,
        1486765607,
        3372019085,
        702947943,
        2527368753,
        1243462805,
        2775723066,
        691089980,
        3021627432,
        1788156608,
        1388728628,
        1560570784,
        813296752,
        3645562113,
    );

//    年度盛典 主播守护关系
    public static $arrYearObsoleteFinalGuard = array(

        '3529079257' => array(
            '3892472416','2360494409',
        ),
        '897962336' => array(
            '1526004735','2120997659',
        ),
        '2528527060' => array(
            '3374647333','3021627432',
        ),
        '2099882215' => array(
            '3352460807','2164426180',
        ),
        '2560750140' => array(
            '3503594114','3372019085',
        ),
        '670967319' => array(
            '3045597354','2612618014',
        ),
        '1836101686' => array(
            '691089980','296966691',
        ),
        '2675544328' => array(
            '941397778','3173671521',
        ),
        '1486551568' => array(
            '1486765607','3496438849',
        ),
        '2775723066' => array(
            '702947943','2446852580',
        ),

    );
}