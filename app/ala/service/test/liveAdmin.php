<?php
/**
 * Created by PhpStorm.
 * User: wintercomming
 * Date: 16/11/7
 * Time: 14:09
 */

define('ROOT_PATH', '/home/<USER>/orp');
define('DATA_PATH', ROOT_PATH . '/data');
define('IS_ORP_RUNTIME', true);
Tieba_Init::init('ala');

set_time_limit(0);

$intAnchorId = 123;
$intAdminId = 456;
$intGroupId = 789;
$intUid = 111;

//$strService = 'ala';
//$strMethod = 'liveAdminAppoint';
//$arrServiceInput = array(
//    'anchor_id' => $intAnchorId,
//    'admin_id' => $intAdminId,
//);
//$arrOutput = Alalib_Util_Service::call($strService, $strMethod, $arrServiceInput);
//if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
//    $strLog = "$strService::$strMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
//    Bingo_Log::warning($strLog);
//}
//echo  "liveAdminAppoint: ". var_export($arrOutput, true) . "\n";
//
//$strService = 'ala';
//$strMethod = 'liveAdminFire';
//$arrServiceInput = array(
//    'anchor_id' => $intAnchorId,
//    'admin_id' => $intAdminId,
//);
//$arrOutput = Alalib_Util_Service::call($strService, $strMethod, $arrServiceInput);
//if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
//    $strLog = "$strService::$strMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
//    Bingo_Log::warning($strLog);
//}
//echo  "liveAdminFire: ". var_export($arrOutput, true) . "\n";
//
//$strService = 'ala';
//$strMethod = 'resignLiveAdmin';
//$arrServiceInput = array(
//    'anchor_id' => $intAnchorId,
//    'admin_id' => $intAdminId,
//);
//$arrOutput = Alalib_Util_Service::call($strService, $strMethod, $arrServiceInput);
//if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
//    $strLog = "$strService::$strMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
//    Bingo_Log::warning($strLog);
//}
//echo  "resignLiveAdmin: ". var_export($arrOutput, true) . "\n";
//
//$strService = 'ala';
//$strMethod = 'liveAdminIsAdmin';
//$arrServiceInput = array(
//    'anchor_id' => $intAnchorId,
//    'admin_id' => $intAdminId,
//);
//$arrOutput = Alalib_Util_Service::call($strService, $strMethod, $arrServiceInput);
//if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
//    $strLog = "$strService::$strMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
//    Bingo_Log::warning($strLog);
//}
//echo  "liveAdminIsAdmin: ". var_export($arrOutput, true) . "\n";
//
//$strService = 'ala';
//$strMethod = 'liveAdminGetAdmins';
//$arrServiceInput = array(
//    'anchor_id' => $intAnchorId,
//    'admin_id' => $intAdminId,
//);
//$arrOutput = Alalib_Util_Service::call($strService, $strMethod, $arrServiceInput);
//if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
//    $strLog = "$strService::$strMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
//    Bingo_Log::warning($strLog);
//}
//echo  "liveAdminGetAdmins: ". var_export($arrOutput, true) . "\n";

$strService = 'ala';
$strMethod = 'liveTalkBan';
$arrServiceInput = array(
    'ban_type' => 2,
    'anchor_id' => $intAnchorId,
    'group_id' => $intGroupId,
    'op_uid' => $intAdminId,
    'uid' => $intUid,
);
$arrOutput = Alalib_Util_Service::call($strService, $strMethod, $arrServiceInput);
if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
    $strLog = "$strService::$strMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
    Bingo_Log::warning($strLog);
}
echo  "liveTalkBan: ". var_export($arrOutput, true) . "\n";

$strService = 'ala';
$strMethod = 'liveTalkBan';
$arrServiceInput = array(
    'ban_type' => 1,
    'anchor_id' => $intAnchorId,
    'group_id' => $intGroupId,
    'op_uid' => $intAdminId,
    'uid' => $intUid,
);
$arrOutput = Alalib_Util_Service::call($strService, $strMethod, $arrServiceInput);
if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
    $strLog = "$strService::$strMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
    Bingo_Log::warning($strLog);
}
echo  "liveTalkBan: ". var_export($arrOutput, true) . "\n";

$strService = 'ala';
$strMethod = 'liveTalkIsBan';
$arrServiceInput = array(
    'ban_type' => 1,
    'ban_key' => $intGroupId,
    'uid' => $intUid,
);
$arrOutput = Alalib_Util_Service::call($strService, $strMethod, $arrServiceInput);
if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
    $strLog = "$strService::$strMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
    Bingo_Log::warning($strLog);
}
echo  "liveTalkIsBan: ". var_export($arrOutput, true) . "\n";

$strService = 'ala';
$strMethod = 'liveTalkIsBan';
$arrServiceInput = array(
    'ban_type' => 2,
    'ban_key' => $intAnchorId,
    'uid' => $intUid,
);
$arrOutput = Alalib_Util_Service::call($strService, $strMethod, $arrServiceInput);
if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
    $strLog = "$strService::$strMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
    Bingo_Log::warning($strLog);
}
echo  "liveTalkIsBan: ". var_export($arrOutput, true) . "\n";


