<?php
/**
 * Created by PhpStorm.
 * User: wintercomming
 * Date: 2018/8/22
 * Time: 16:21
 */

class Service_Live_SdkHotLive
{
    const REDIS_SDK_HOT_LIVE_KEY = 'sdk_hot_live_';
    const SERVICE_NAME = "service_haokan";
    const SERVICE_MVIDEO = "service_mvideo";
    const METHOD_NAME_HAOKAN = "/haokan/innerliverecommend";
    const METHOD_NAME_QUANMIN = "/mvideo/api/grliveid";

    const SERVICE_NAME_STRATEGY_PLATFORM = "service_strategy_live";
    const METHOD_NAME_STRATEGY_PLATFORM_SLIDE = "/ForkService/handle";

    /**
     * checkFieldExist
     * @param $arrInput
     * @return bool
     */
    private static function checkFieldExist($arrInput){
        $args = func_get_args();
        array_shift($args);
        if (empty($args)){
            return true;
        }
        foreach ($args as $arg) {
            if (!isset($arrInput[$arg])){
                Bingo_Log::warning("$arg not exist");
                return false;
            }
        }
        return true;
    }

    /**
     * 设置直播hot队列
     * @param $arrInput
     * @return array
     */
    public static function setLive($arrInput){
        if (!self::checkFieldExist($arrInput, 'live_id', 'score', 'subapp_type')) {
            $strLog = __CLASS__ ."::". __FUNCTION__ . " param error. input[".serialize($arrInput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }
        $intLiveId = (int)$arrInput['live_id'];
        $intUserId = (int)$arrInput['user_id'];
        $intScore = (int)$arrInput['score'];
        //划分业务线存储所属key
        $strSubappType = Libs_Util_Live::distributeSdkHotSubAppType((string)$arrInput['subapp_type']);

        // 屏蔽好看测试用户进入 SDK Hot List
        if (Alalib_Conf_Sdk::SUBAPP_HAOKAN == $strSubappType) {
            $arrNoDispatchReq = array(
                'user_id' => $intUserId,
                'subapp_type' => Alalib_Conf_Sdk::SUBAPP_HAOKAN,
            );

            $arrNoDispatchRet = Service_User_User::isNoDispatchUser($arrNoDispatchReq);
            if ($arrNoDispatchRet && 0 == $arrNoDispatchRet['errno'] && true == $arrNoDispatchRet['data']) {
                return self::errRet(Alalib_Conf_Error::ERR_SUCCESS);
            }
        }


        $strKey = self::REDIS_SDK_HOT_LIVE_KEY . $strSubappType;
        $arrReq = array(
            'key'     => $strKey,
            'members' => array(
                array(
                    'score' => $intScore,
                    'member' => $intLiveId,
                ),
            ),
        );
        $arrRes  = Util_Redis::zAddToRedis($arrReq);

        if(false === $arrRes){
            $strLog = __CLASS__."::".__FUNCTION__." call Util_Redis::zAddToRedis fail. input:[".serialize($arrReq)."]; output:[".serialize($arrRes)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS);
    }

    /**
     * 从hot队列删除，支持批量
     * @param $arrInput
     * @return array
     */
    public static function delLive($arrInput){
        if (!self::checkFieldExist($arrInput, 'live_ids', 'subapp_type')) {
            $strLog = __CLASS__ ."::". __FUNCTION__ . " param error. input[".serialize($arrInput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }
        $arrLiveIds = (array)$arrInput['live_ids'];
        //划分业务线存储所属key
        $strSubappType = Libs_Util_Live::distributeSdkHotSubAppType((string)$arrInput['subapp_type']);

        $strKey = self::REDIS_SDK_HOT_LIVE_KEY . $strSubappType;
        $arrReq = array(
            'key'     => $strKey,
            'member' => $arrLiveIds,
        );
        $arrRes  = Util_Redis::zRemFromRedis($arrReq);
        if(false === $arrRes){
            $strLog = __CLASS__."::".__FUNCTION__." call Util_Redis::zRemFromRedis fail. input:[".serialize($arrReq)."]; output:[".serialize($arrRes)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS);
    }
    /**
     * 获取好看/全民 gr推荐直播list
     * @param $arrInput
     * @return array
     */
    public static function getRmbGrLiveList($arrInput){
        if (!self::checkFieldExist($arrInput, 'subapp_type')) {
            $strLog = __CLASS__ ."::". __FUNCTION__ . " param error. input[".serialize($arrInput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }
        $strSubappType = (string)$arrInput['subapp_type'];
        $arrLiveIdList = array();
        $source = 'livesdk';

        if ($strSubappType ==  Alalib_Conf_Sdk::SUBAPP_HAOKAN) {
            $arrLiveIdList = self::getHaokanGrLiveIds($source);
        } elseif ($strSubappType == Alalib_Conf_Sdk::SUBAPP_QUANMIN) {
            $arrLiveIdList = self::getQuanminGrLiveIds($source);
        }

        if (empty($arrLiveIdList)){
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $strService = 'ala';
        $strMethod = 'liveGetInfo';
        $arrIn = array(
            'live_ids' => $arrLiveIdList,
        );
        $arrOut = Alalib_Util_Service::call($strService, $strMethod, $arrIn, null, null, 'post', null, 'utf-8', 'local');
        if ($arrOut === false || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strService $strMethod fail. input:[" . serialize($arrIn) . "]; output:[" . serialize($arrOut) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrLiveInfos = $arrOut['data'];

        $arrDelLiveIds = array();
        foreach ($arrLiveInfos as $intLiveId => $arrLiveInfo) {
            $intLiveId = $arrLiveInfo['live_info']['live_id'];
            $intLiveStatus = $arrLiveInfo['live_info']['live_status'];

            if (Libs_Define_Live::LIVE_OPEN != $intLiveStatus){
                //过滤非直播
                $arrDelLiveIds[] = $intLiveId;
                unset($arrLiveInfos[$intLiveId]);
                continue;
            }
        }

        if (!empty($arrDelLiveIds)) {
            $arrIn = array(
                'live_ids' => $arrDelLiveIds,
                'subapp_type' => $strSubappType,
            );
            $arrOut = self::delLive($arrIn);
            if ($arrOut === false || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call self::delLive fail. input:[" . serialize($arrIn) . "]; output:[" . serialize($arrOut) . "]";
                Bingo_Log::warning($strLog);
            }
        }

        $arrSortedLiveInfo = array();
        foreach ($arrLiveIdList as $intLiveId) {
            if (isset($arrLiveInfos[$intLiveId])){
                $arrSortedLiveInfo[] = $arrLiveInfos[$intLiveId];
            }
        }

        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrSortedLiveInfo);
    }

    //策略中台推荐数据
    public static function getStrategyPlatformLiveList($arrInput)
    {
        //数据需求来源，默认是上下滑
        $strForkFromSlid        = 'slide';//上下滑
        $strForkFromQuitLiveRec = 'quit_live_rec';//退出房间推荐

        $strForkFrom = 'slide';
        if (isset($arrInput['fork_from'])) {
            $strForkFrom = $arrInput['fork_from'];
        }

        $strSubappType = (string)$arrInput['subapp_type'];
        switch ($strSubappType) {
            case Alalib_Conf_Sdk::SUBAPP_SHOUBAI:
                $intProduct  = 0;
                $strForkType = 'live_video';
                if ($strForkFrom == $strForkFromQuitLiveRec) {
                    $strForkType = 'live_video_exit';
                }
                break;
            case Alalib_Conf_Sdk::SUBAPP_HAOKAN:
                $intProduct  = 100;
                $strForkType = 'live_feed_haokan';
                if ($strForkFrom == $strForkFromQuitLiveRec) {
                    $strForkType = 'live_feed_haokan_exit';
                }
                break;
            case Alalib_Conf_Sdk::SUBAPP_QUANMIN:
                $intProduct  = 102;
                $strForkType = 'live_video_quanmin';
                if ($strForkFrom == $strForkFromQuitLiveRec) {
                    $strForkType = 'live_video_quanmin_exit';
                }
                break;
            case Alalib_Conf_Sdk::SUBAPP_TIEBA:
                $intProduct  = 104;
                $strForkType = 'live_video_tieba';
                if ($strForkFrom == $strForkFromQuitLiveRec) {
                    $strForkType = 'live_video_tieba_exit';
                }
                break;
            default:
                $intProduct  = 104;
                $strForkType = 'live_video_tieba';
        }

        if ($arrInput['ps'] <= 0) {
            $arrInput['ps'] = 5;
        }

        $arrReq = array(
            'pathinfo' => self::METHOD_NAME_STRATEGY_PLATFORM_SLIDE,
        );

        $arrNetType = array(
            1 => '1_0', //wifi
            2 => '0_2', //2G
            3 => '0_3', //3G
            4 => '0_13', //4G
        );
        $intNetType = intval($arrInput['net_type']);
        $strNetWork = isset($arrNetType[$intNetType]) ? $arrNetType[$intNetType] : "0_13";

        $arrParam            = array(
            'logid'       => Bingo_Log::getLogId(),
            'fork_type'   => $strForkType,
            'cuid'        => strval($arrInput['cuid']),
            'uid'         => strval($arrInput['user_id']),
            'baiduid'     => strval($arrInput['baiduid']),
            'tab'         => 'slide',
            'sids'        => array(),
            'req_str'     => '',
            'product'     => $intProduct,
            'subapp_type' => $strSubappType,
        );
        $arrParamCommon      = array(
            'common_info'  => array(
                'fork_type' => $strForkType,
                'cuid'      => strval($arrInput['cuid']),
                'uid'       => strval($arrInput['user_id']),
                'baiduid'   => strval($arrInput['baiduid']),
                'tab'       => 'slide',
                'key'       => 'live_video',
                'product'   => $intProduct,
                'reqnum'    => $arrInput['ps'],
                'command'   => 'channel',
                'sids'      => array(),
            ),
            'device_info'  => array(
                'app_version' => $arrInput['_sdk_version'],
                'network'     => $strNetWork,
            ),
            'history_info' => array(
                'video_item' => array(),
            ),
            'refresh_info' => array(
                'session_id' => $arrInput['session_id'],
            ),
        );
        $arrParam['req_str'] = json_encode($arrParamCommon, JSON_UNESCAPED_UNICODE);
        //发起请求
        $arrResp = ral(self::SERVICE_NAME_STRATEGY_PLATFORM, 'POST', $arrParam, rand(), $arrReq);

        $strLog = __CLASS__ . "::" . __FUNCTION__ . " call /ForkService/handle data. input:[" . json_encode($arrParam) . "]; output:[" . json_encode($arrResp) . "]";
        Bingo_Log::warning($strLog);

        if (false === $arrResp || (!isset($arrResp["error_no"])) || $arrResp['error_no'] != 'OK') {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call /ForkService/handle fail. input:[" . serialize($arrParam) . "]; output:[" . serialize($arrResp) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $arrRespData = json_decode($arrResp['resp_str'], true);
        if (!is_array($arrRespData) || !isset($arrRespData['error_code']) || $arrRespData['error_code'] != "E_SUCC") {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " data illegal . input:[" . serialize($arrParam) . "]; output:[" . serialize($arrRespData) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }

        if (!isset($arrRespData['content']) || count($arrRespData['content']) == 0) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " has no data . input:[" . serialize($arrParam) . "]; output:[" . serialize($arrRespData) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $arrRoomIds = array();
        foreach ($arrRespData['content'] as $item) {
            //目前秀场直播使用的key是 live_video
            if ($item['key'] != 'live_video') {
                continue;
            }
            foreach ($item['items'] as $subItem) {
                $arrExtFe  = json_decode($subItem['ext']['fe'], true);
                $intRoomId = intval($arrExtFe['room_id']);
                if ($intRoomId == 0) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . "get illegal room_id . input:[" . serialize($arrParam) . "]; output:[" . serialize($subItem) . "]";
                    Bingo_Log::warning($strLog);
                    continue;
                }
                $arrRoomIds[] = $intRoomId;
            }
        }

        if (empty($arrRoomIds)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "room_id is empty. output:[" . json_encode($arrRespData) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $ret = Service_Live_Live::getLiveInfoByRoomIds(array('room_ids' => $arrRoomIds));
        if ($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call getLiveInfoByRoomIds fail. input:[" . json_encode($arrRoomIds) . "]; output:[" . json_encode($ret) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $arrLiveInfos      = $ret['data'];//这里的key是room_id
        $arrSortedLiveInfo = array();
        foreach ($arrLiveInfos as $intRoomId => $arrLiveInfo) {
            $intLiveStatus = $arrLiveInfo['live_info']['live_status'];
            if (Libs_Define_Live::LIVE_OPEN != $intLiveStatus) {
                //过滤关播
                continue;
            }
            $arrSortedLiveInfo[] = $arrLiveInfo;
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrSortedLiveInfo);
    }

    /**
     * 获取top直播列表
     * @param $arrInput
     * @return array
     */
    public static function getLiveList($arrInput){
        if (!self::checkFieldExist($arrInput, 'subapp_type')) {
            $strLog = __CLASS__ ."::". __FUNCTION__ . " param error. input[".serialize($arrInput)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }
        $strSubappType = (string)$arrInput['subapp_type'];
        $intPn = (int)$arrInput['pn']; // from 0
        $intPs = (int)$arrInput['ps']; // default 20
        $intPs = $intPs > 0 ? $intPs : Alalib_Conf_Define::DB_DEFAULT_PAGE_LIMIT;

        $strKey = self::REDIS_SDK_HOT_LIVE_KEY . $strSubappType;
        $arrReq = array(
            "key" => $strKey,
            "start" => $intPn * $intPs,
            "stop" => ($intPn+1) * $intPs,
        );
        $arrOut = Util_Redis::zRangeFromRedis($arrReq);
        if(false === $arrOut) {
            $strLog = __CLASS__."::".__FUNCTION__." call Util_Redis::zRangeFromRedis fail. input:[".serialize($arrReq)."]; output:[".serialize($arrOut)."]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL);
        }
        $arrLiveIdList = $arrOut[$strKey];
        if (empty($arrLiveIdList)){
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $strService = 'ala';
        $strMethod = 'liveGetInfo';
        $arrIn = array(
            'live_ids' => $arrLiveIdList,
        );
        $arrOut = Alalib_Util_Service::call($strService, $strMethod, $arrIn, null, null, 'post', null, 'utf-8', 'local');
        if ($arrOut === false || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strService $strMethod fail. input:[" . serialize($arrIn) . "]; output:[" . serialize($arrOut) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrLiveInfos = $arrOut['data'];

        $arrDelLiveIds = array();
        foreach ($arrLiveInfos as $intLiveId => $arrLiveInfo) {
            $intLiveId = $arrLiveInfo['live_info']['live_id'];
            $intLiveStatus = $arrLiveInfo['live_info']['live_status'];

            if (Libs_Define_Live::LIVE_OPEN != $intLiveStatus){
                //过滤非直播
                $arrDelLiveIds[] = $intLiveId;
                unset($arrLiveInfos[$intLiveId]);
                continue;
            }
        }

        if (!empty($arrDelLiveIds)) {
            $arrIn = array(
                'live_ids' => $arrDelLiveIds,
                'subapp_type' => $strSubappType,
            );
            $arrOut = self::delLive($arrIn);
            if ($arrOut === false || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call self::delLive fail. input:[" . serialize($arrIn) . "]; output:[" . serialize($arrOut) . "]";
                Bingo_Log::warning($strLog);
            }
        }

        $arrSortedLiveInfo = array();
        foreach ($arrLiveIdList as $intLiveId) {
            if (isset($arrLiveInfos[$intLiveId])){
                $arrSortedLiveInfo[] = $arrLiveInfos[$intLiveId];
            }
        }

        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrSortedLiveInfo);
    }

    /**
     * 返回值
     * @param int {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
     * @param array {Array} $arrOutData default array()
     * @param string {String} $strMsg default ""
     * @return array {Array} :errno :errmsg :{Array}output
     * */
    private static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = array(), $strMsg = "") {
        $arrOutput = array();
        $arrOutput['errno']   = $intErrno;
        $arrOutput['errmsg']  = Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput['usermsg'] = $strMsg;
        $arrOutput['data']    = $arrOutData;

        return $arrOutput;
    }

    /**
     * 获取好看Gr推荐liveIds
     * @param string
     * @return array
     */
    private static function getHaokanGrLiveIds($source)
    {
        $arrReq = array(
            'pathinfo' => self::METHOD_NAME_HAOKAN,
            'host' => 'sv.baidu.com',
        );
        $arrParam = array(
            'source' => $source,
        );
        $strResp = ral(self::SERVICE_NAME, 'POST', $arrParam, array(), $arrReq);
        $arrResp = empty($strResp) ? false : json_decode($strResp, true);

        if (false === $arrResp || (! isset($arrResp["status"]))) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call /haokan/innerliverecommend fail. input:[" . serialize($arrParam) . "]; output:[" . $strResp . "]";
            Bingo_Log::warning($strLog);
            return array();
        } elseif ($arrResp["status"] != 0) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call /haokan/innerliverecommend status is not ok. input:[" . serialize($arrParam) . "]; output:[" . $strResp . "]";
            Bingo_Log::warning($strLog);
            return array();
        }

        return $arrResp['data']['room_list'];
    }

    /**
     * 获取全民Gr推荐liveIds
     * @return array
     */
    public static function getQuanminGrLiveIds($source)
    {
        $arrParam = array(
            'source' => $source,
            'sign' => self::getSign($source),
        );

        $arrReq = array(
            'pathinfo' => self::METHOD_NAME_QUANMIN,
            'host' => 'sv.baidu.com',
            "querystring" => http_build_query($arrParam)
        );
        $strResp = ral(self::SERVICE_MVIDEO, 'GET', array(), array(), $arrReq);
        $arrResp = empty($strResp) ? false : json_decode($strResp, true);

        if (false === $arrResp || (! isset($arrResp["errno"]))) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call /mvideo/api/liveid fail. input:[" . serialize($arrParam) . "]; output:[" . $strResp . "]";
            Bingo_Log::warning($strLog);
            return array();
        } elseif ($arrResp["errno"] != 0) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call /mvideo/api/liveid errno is not ok. input:[" . serialize($arrParam) . "]; output:[" . $strResp . "]";
            Bingo_Log::warning($strLog);
            return array();
        }

        return $arrResp['data']['live_id'];
    }

    /**
     * @param string
     * @return String
     */
    private static function getSign($source)
    {
        return md5("rmb" . $source);
    }
}
