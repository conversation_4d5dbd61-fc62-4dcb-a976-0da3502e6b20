<?php
/**
 * Created by PhpStorm.
 * User: wintercomming
 * Date: 2018/05/16
 * Time: 16:13
 */

class Service_Family_FamilyConf{
    const PRIVILEGE_ID = 1; // 编号

    const PRIVILEGE_GIFT = 2; // 礼物

    const PRIVILEGE_ICON = 3; // 印记

    const PRIVILEGE_IM_EFFECT = 4; // 发言特效

    const PRIVILEGE_ENTER_EFFECT_ONE = 5; // 进场特效1

    const PRIVILEGE_ENTER_EFFECT_TWO = 6; // 进场特效2

    const PRIVILEGE_ENTER_EFFECT_THREE = 7; // 进场特效3

    const PRIVILEGE_NO_IM_BAN = 8;  // 防房管禁言


    public static $levelExp = array(
        0 => array(
            'from' => 0,
            'to' => 0,
            'privileges' => array(
                self::PRIVILEGE_ID,
                self::PRIVILEGE_GIFT,
            ),
        ),
        1 => array(
            'from' => 1,
            'to' => 59,
            'privileges' => array(
                self::PRIVILEGE_ID,
                self::PRIVILEGE_GIFT,
                self::PRIVILEGE_ICON,
            ),
            'mark_info' => array(
                array(
                    'mark_id' => 28,
                    'type' => Libs_Define_LiveMarkInfo::LIVE_MARK_TYPE_FAMILY,
                    'mark_pic' => 'https://imgsa.baidu.com/fex/pic/item/7acb0a46f21fbe094502fd8b67600c338644add4.jpg',
                    'height' => 48,
                    'width' => 141,
                ),
            ),
        ),
        2 => array(
            'from' => 60,
            'to' => 599,
            'privileges' => array(
                self::PRIVILEGE_ID,
                self::PRIVILEGE_GIFT,
                self::PRIVILEGE_ICON,
                self::PRIVILEGE_IM_EFFECT,
            ),
            'mark_info' => array(
                array(
                    'mark_id' => 29,
                    'type' => Libs_Define_LiveMarkInfo::LIVE_MARK_TYPE_FAMILY,
                    'mark_pic' => 'https://imgsa.baidu.com/fex/pic/item/0d338744ebf81a4c6490d256db2a6059242da68a.jpg',
                    'height' => 48,
                    'width' => 141,
                ),
            ),
        ),
        3 => array(
            'from' => 600,
            'to' => 5999,
            'privileges' => array(
                self::PRIVILEGE_ID,
                self::PRIVILEGE_GIFT,
                self::PRIVILEGE_ICON,
                self::PRIVILEGE_IM_EFFECT,
                self::PRIVILEGE_ENTER_EFFECT_ONE,
            ),
            'mark_info' => array(
                array(
                    'mark_id' => 30,
                    'type' => Libs_Define_LiveMarkInfo::LIVE_MARK_TYPE_FAMILY,
                    'mark_pic' => 'https://imgsa.baidu.com/fex/pic/item/bd3eb13533fa828b3fbda37cf11f4134960a5a96.jpg',
                    'height' => 48,
                    'width' => 141,
                ),
            ),
        ),
        4 => array(
            'from' => 6000,
            'to' => 59999,
            'privileges' => array(
                self::PRIVILEGE_ID,
                self::PRIVILEGE_GIFT,
                self::PRIVILEGE_ICON,
                self::PRIVILEGE_IM_EFFECT,
                self::PRIVILEGE_ENTER_EFFECT_TWO,
            ),
            'mark_info' => array(
                array(
                    'mark_id' => 31,
                    'type' => Libs_Define_LiveMarkInfo::LIVE_MARK_TYPE_FAMILY,
                    'mark_pic' => 'https://imgsa.baidu.com/fex/pic/item/4afbfbedab64034fa69dfc99a3c379310b551dd8.jpg',
                    'height' => 48,
                    'width' => 141,
                ),
            ),
        ),
        5 => array(
            'from' => 60000,
            'to' => 9999999,
            'privileges' => array(
                self::PRIVILEGE_ID,
                self::PRIVILEGE_GIFT,
                self::PRIVILEGE_ICON,
                self::PRIVILEGE_IM_EFFECT,
                self::PRIVILEGE_ENTER_EFFECT_THREE,
                self::PRIVILEGE_NO_IM_BAN,
            ),
            'mark_info' => array(
                array(
                    'mark_id' => 32,
                    'type' => Libs_Define_LiveMarkInfo::LIVE_MARK_TYPE_FAMILY,
                    'mark_pic' => 'https://imgsa.baidu.com/fex/pic/item/0df3d7ca7bcb0a46de6eb5a66763f6246a60afe4.jpg',
                    'height' => 48,
                    'width' => 141,
                ),
            ),
        ),
    );


    /**
     * 是否开启现金贷功能
     */
    const CASH_LOAN_SWITCH = 1; // 1--开启, 0--关闭

    const FAMILY_ICON_URL = "https://imgsa.baidu.com/fex/%70%69%63/item/1f178a82b9014a907ade18d0a4773912b21beeff.jpg";

    public static $arrWhiteList = array(
        1287164676, // zhangnannan
        791245334,  // zhangnannan
        791242890,  // zhangnannan
        823729741,  // zhangnannan
        813038052,  // zhangnannan
        269298329,  // zhangnannan
        1096254158, // hesixing
        838608334,  // hesixing
        1091390097, // chenjiannan
        889057323,  // chenjiannan
        254946837,  // wangyang66
        3036783232, // weiwei22
        3020820367, // limengru
        550955220,  // jiangchangzheng
        922017925,  // huanganda
        790059960,
        1706656080
    );

}

