<?php
/**
 * Created by PhpStorm.
 * User: wintercomming
 * Date: 2017/7/31
 * Time: 16:13
 */

class Service_Task_TaskConf{
    const WATCH_LIVE = 1; //观看直播
    const SHARE_LIVE = 2; //分享直播
    const FOLLOW_ANCHOR = 3; //关注主播
    const SEND_GIFT = 4; //送礼
    const TDOU_DEPOSIT = 5; //T豆充值
    const LEVEL_UP = 6; //升级
    const SIGN_IN = 7; //签到
    const RED_PACKET = 8; //发言赢红包

    const TASK_STATE_DOING = 1; //任务进行中
    const TASK_STATE_UNRECEIVED = 2; //任务已完成,未领取
    const TASK_STATE_DONE = 3; //任务已领取

    const TASK_TYPE_EVERY_DAY = 1;
    const TASK_TYPE_FOREVER = 2;

    const RED_PACKET_SWITCH = 1; //红包任务开关，紧急关闭可以修改这里
    const PETAL_TASK_SWITCH = 1; //任务icon开关
    const RED_PACKET_WITHDRAW_SWITCH = 1; //红包任务提现开关,红包任务开始后该开关要打开,否则前端展示有问题

    const REDIS_KEY_REDPACKET_COMPLETE_COUNT = 'redpacket_task_complete_count_';

    public static $arrConfig = array(
        self::WATCH_LIVE => array(
            'taskname' => "观看直播",
            'taskdetail' => "今日剩余%s次",
            'tasktype' => self::TASK_TYPE_EVERY_DAY,
            'steps' => array(
                1 => array(
                    'step_id' => 1,
                    'watch_time' => 60,
                    'petal_num' => 50,
                    'exp_num' => 1,
                ),
                2 => array(
                    'step_id' => 2,
                    'watch_time' => 120,
                    'petal_num' => 50,
                    'exp_num' => 2,
                ),
                3 => array(
                    'step_id' => 3,
                    'watch_time' => 180,
                    'petal_num' => 150,
                    'exp_num' => 3,
                ),
                4 => array(
                    'step_id' => 4,
                    'watch_time' => 240,
                    'petal_num' => 150,
                    'exp_num' => 4,
                ),
                5 => array(
                    'step_id' => 5,
                    'watch_time' => 300,
                    'petal_num' => 200,
                    'exp_num' => 5,
                ),
                6 => array(
                    'step_id' => 6,
                    'watch_time' => 360,
                    'petal_num' => 250,
                    'exp_num' => 6,
                ),
            ),
        ),
        self::FOLLOW_ANCHOR => array(
            'taskname' => "关注主播(%s/%s)",
            'taskdetail' => "关注3位主播",
            'tasktype' => self::TASK_TYPE_EVERY_DAY,
            'icon' => 'https://ala-gift.cdn.bcebos.com/gift/2020-12/1607484918408/%E5%85%B3%E6%B3%A8%403x.png',
            'icon_new' => 'https://ala-gift.cdn.bcebos.com/gift/2021-1/1610439651176/listIcon2.png',
            'steps' => array(
                1 => array(
                    'step_id' => 1,
                    'petal_num' => 1000,
                    'follow_count' => 3,
                    'exp_num' => 3,
                ),
            ),
        ),
//        self::SHARE_LIVE => array(
//            'taskname' => "分享直播(%s/%s)",
//            'taskdetail' => "分享直播间3次",
//            'tasktype' => self::TASK_TYPE_EVERY_DAY,
//            'steps' => array(
//                1 => array(
//                    'step_id' => 1,
//                    'petal_num' => 800,
//                    'share_count' => 3,
//                ),
//            ),
//        ),
        self::SEND_GIFT => array(
            'taskname' => "赠送礼物(%s/%s)",
            'taskdetail' => "赠送1次T豆礼物",
            'tasktype' => self::TASK_TYPE_EVERY_DAY,
            'icon' => 'https://ala-gift.cdn.bcebos.com/gift/2020-12/1607484969158/%E7%A4%BC%E7%89%A9%403x.png',
            'icon_new' => 'https://ala-gift.cdn.bcebos.com/gift/2021-1/1610439671057/listIcon3.png',
            'steps' => array(
                1 => array(
                    'step_id' => 1,
                    'petal_num' => 1500,
                    'exp_num' => 5,
                ),
            ),
        ),
        self::TDOU_DEPOSIT => array(
            'taskname' => "充值T豆(%s/%s)",
            'taskdetail' => "完成1次任意金额充值",
            'tasktype' => self::TASK_TYPE_EVERY_DAY,
            'icon' => 'https://ala-gift.cdn.bcebos.com/gift/2020-12/1607485009761/t%E8%B1%86%E5%95%86%E5%BA%97%403x.png',
            'icon_new' => 'https://ala-gift.cdn.bcebos.com/gift/2021-1/1610439689470/listIcon4.png',
            'steps' => array(
                1 => array(
                    'step_id' => 1,
                    'petal_num' => 2000,
                    'exp_num' => 15,
                ),
            ) ,
        ),
        self::LEVEL_UP => array(
            'taskname' => "等级奖励",
            'taskdetail' => "每次升级可获得",
            'tasktype' => self::TASK_TYPE_FOREVER,
            'icon' => 'https://ala-gift.cdn.bcebos.com/gift/2020-12/1607485080936/%E4%BB%BB%E5%8A%A1%403x.png',
            'icon_new' => 'https://ala-gift.cdn.bcebos.com/gift/2021-1/1610439713564/listIcon5.png',
            'steps' => array(
                1 => array(
                    'step_id' => 1,
                    'petal_num' => 0,
                    'from_level' => 1,
                    'to_level' => 1,
                    'exp_num' => 10,
                ),
                2 => array(
                    'step_id' => 2,
                    'petal_num' => 100,
                    'from_level' => 2,
                    'to_level' => 5,
                    'exp_num' => 10,
                ),
                3 => array(
                    'step_id' => 3,
                    'petal_num' => 200,
                    'from_level' => 6,
                    'to_level' => 9,
                    'exp_num' => 10,
                ),
                4 => array(
                    'step_id' => 4,
                    'petal_num' => 500,
                    'from_level' => 10,
                    'to_level' => 17,
                    'exp_num' => 10,
                ),
                5 => array(
                    'step_id' => 5,
                    'petal_num' => 1000,
                    'from_level' => 18,
                    'to_level' => 61,
                    'exp_num' => 10,
                ),
            ),
        ),
        self::SIGN_IN => array(
            'taskname' => "直播间签到",
            'taskdetail' => "完成1次直播间签到",
            'tasktype' => self::TASK_TYPE_EVERY_DAY,
            'icon' => 'https://ala-gift.cdn.bcebos.com/gift/2020-12/1607484724922/%E7%AD%BE%E5%88%B0%403x.png',
            'icon_new' => 'https://ala-gift.cdn.bcebos.com/gift/2021-1/1610439629579/listIcon1.png',
            'steps' => array(
                1 => array(
                    'step_id' => 1,
                    'petal_num' => 100,
                    'exp_num' => 2,
                ),
            ) ,
        ),
        self::RED_PACKET => array(
            'taskname' => "发言赢红包",
            'taskdetail' => "发言赢红包",
            'tasktype' => self::TASK_TYPE_EVERY_DAY,
            'steps' => array(
                1 => array(
                    'step_id' => 1,
                    'watch_time' => 60,
                    'start_time' => 1617955200, //2021-04-09 16:00:00
                    'end_time' => 1619798399, //2021-04-30 23:59:59
                    'withdraw_start_time' => 1617955200, //2021-04-09 16:00:00 提现开始时间要和红包任务开始时间保持一致
                    'withdraw_end_time' => 1620403199, //2021-05-07 23:59:59
                ),
            ) ,
        ),
    );

    /**
     * 初始化
     * Service_Task_TaskConf constructor.
     */
    public static function init()
    {
        $arrLevelUpConf = self::$arrConfig[self::LEVEL_UP]['steps'];
        $arrNewConf = array();
        foreach ($arrLevelUpConf as $item) {
            for($i = $item['from_level']; $i <= $item['to_level']; $i++){
                $arrNewConf[$i] = array(
                    'step_id' => $i,
                    'petal_num' => $item['petal_num'],
                    'exp_num' => $item['exp_num'],
                );
            }
        }
        self::$arrConfig[self::LEVEL_UP]['steps'] = $arrNewConf;

        $arrFollowConf = self::$arrConfig[self::FOLLOW_ANCHOR]['steps'];
        $arrUnit = end($arrFollowConf);
        $arrNewConf = array();
        for ($i = 1; $i <= $arrUnit['follow_count']; $i++) {
            $arrNewConf[$i] = array(
                'step_id' => $i,
                'petal_num' => 0,
                'display_petal_num' => $arrUnit['petal_num'],
                'exp_num' => 0,
                'display_exp_num' => $arrUnit['exp_num'],
            );
            if ($i == $arrUnit['follow_count']){
                $arrNewConf[$i]['petal_num'] = $arrUnit['petal_num'];
                $arrNewConf[$i]['exp_num'] = $arrUnit['exp_num'];

            }
        }
        self::$arrConfig[self::FOLLOW_ANCHOR]['steps'] = $arrNewConf;

//        $arrShareConf = self::$arrConfig[self::SHARE_LIVE]['steps'];
//        $arrUnit = end($arrShareConf);
//        $arrNewConf = array();
//        for ($i = 1; $i <= $arrUnit['share_count']; $i++) {
//            $arrNewConf[$i] = array(
//                'step_id' => $i,
//                'petal_num' => 0,
//                'display_petal_num' => $arrUnit['petal_num'],
//            );
//            if ($i == $arrUnit['share_count']){
//                $arrNewConf[$i]['petal_num'] = $arrUnit['petal_num'];
//            }
//        }
//        self::$arrConfig[self::SHARE_LIVE]['steps'] = $arrNewConf;
    }
}

Service_Task_TaskConf::init();
