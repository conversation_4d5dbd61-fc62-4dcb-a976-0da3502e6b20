<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file Strategy.php
 * <AUTHOR>
 * @date 20160316
 * @brief 
 *  
 **/
define("BINGO_ENCODE_LANG", "UTF-8");
define("MODULE","Ala_service");
class Service_Strategy_Strategy
{
    const AMIS_OPEN_LIVE_SWITCH_WXB_BLACK_LIST = 'wxb_black_open_live_switch';  //amis 开播验证wxb黑名单开关
    const AMIS_OPEN_LIVE_SWITCH_REALNAME_VERIFY = 'realname_verify_open_live_switch';  //amis 未实名验证禁止开播开关

    private static $wxbCertifyInfo = [];

    /**
     * 从风控侧获取是否命中开播黑名单和是否再pass认证
     * @param $arrInput
     * @return mixed
     */
    public static function getWxbCertifyInfo($arrInput)
    {
        $key = $arrInput['user_id'] . $arrInput['call_from'] . $arrInput['channel'];
        if (empty(self::$wxbCertifyInfo[$key])) {
            //获取网信办开播黑名单命中状态以及pass实名状态
            $arrInput  = array(
                'user_id'   => $arrInput['user_id'],
                'call_from' => $arrInput['call_from'],
                'channel'   => $arrInput['channel'],
            );
            $arrOutput = Tieba_Service::call('ala', 'getWxbCertifyStatus', $arrInput, null, null, 'post', null, 'utf-8', 'local');
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . "call ala getWxbCertifyStatus fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::fatal($strLog);
                self::$wxbCertifyInfo[$key] = array();
            } else {
                self::$wxbCertifyInfo[$key] = $arrOutput['data'];
            }
        }
        return self::$wxbCertifyInfo[$key];
    }

    /**
     * @param {Array} $arrParamInput
     * @return {Array} :{Int}errno :{String}errmsg :{Array}output
     * */
    public static function strategyGetAll($arrParamInput){
        if(empty($arrParamInput["user_id"])){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }
        $intUserId     = intval($arrParamInput["user_id"]);
        $intForumId    = intval($arrParamInput["forum_id"]);
        $intUserIP     = intval($arrParamInput["user_ip"]);
        $strSubappType = $arrParamInput['subapp_type'];
        $arrStrategy   = array();
        $arrOutput     = self::strategyCheckStartLive(
            array(
                'user_id'     => $intUserId,
                'forum_id'    => $intForumId,
                'user_ip'     => $intUserIP,
                'subapp_type' => $strSubappType,
            )
        );
        $arrStrategy   = array_merge($arrStrategy, $arrOutput["data"]);

        // 实名认证状态 201911
        $arrStrategy['certify'] = array(
            'switch' => 2,
            'text' => '未实名',
        );
        if ( $arrParamInput['subapp_type'] == 'mobilebaidu' ) {
            $arrStrategy['certify'] = array(
                'switch' => 1,
                'text' => '已实名',
            );
        } else {
            if ($intUserId) {
                $arrCertifyStatus = Tieba_Service::call('ala', 'getCertifyStatus', ['user_id' => $intUserId], null, null, 'get', null, 'utf-8', 'local');
                if ($arrCertifyStatus && 0 == $arrCertifyStatus['errno']) {
                    if (2 == $arrCertifyStatus['data']['certify_status']) {
                        $arrStrategy['certify'] = [
                            'switch' => 1,
                            'text'   => '已实名',
                        ];
                    }
                }
            }
        }

        /**
         * 第三方开播加入白名单的用户无需过百度云认证
         */
         $isOpenLiveConf = Bd_Conf::getAppConf('/app/ala/create_liveroom');
         if($isOpenLiveConf["thrid_certify"][$arrParamInput['subapp_type']]){
             $arrStrategy['certify'] = [
                 'switch' => 1,
                 'text'   => '已实名',
             ];
             $arrParamInput['verify_info_status'] = 2;
             $arrParamInput['verify_info_scene'][$arrParamInput['subapp_type']]=empty($arrParamInput['verify_info_scene'][$arrParamInput['subapp_type']])?0:1;
         }

        $arrOutput = self::strategyVerifyStatus(
            array(
                'verify_info_status' => intval($arrParamInput['verify_info_status']),
                'subapp_type' => $arrParamInput['subapp_type'],
                'verify_info_scene' => $arrParamInput['verify_info_scene'],
                'user_id' => $intUserId,
                'client_type' => intval($arrParamInput['client_type']),
                'client_version' => strval($arrParamInput['client_version'])
            )
        );
        $arrStrategy = array_merge($arrStrategy,$arrOutput["data"]);

        //从风控侧取实名信息，验证是否经过pass验证
        $bolWxbRealNameVerifySwitch = self::getAmisHashSwitch(self::AMIS_OPEN_LIVE_SWITCH_REALNAME_VERIFY, 'switch_' . $strSubappType);
        if ($bolWxbRealNameVerifySwitch && in_array($strSubappType, ['tieba', 'haokan', 'quanmin'])) {
            $arrInput           = array(
                'user_id'   => $intUserId,
                'call_from' => 'xiuchang',
                'channel'   => $strSubappType,
            );
            $arrBlackListOutput = self::getWxbCertifyInfo($arrInput);
            //verified_status 0 未实名 1 实名
            if (isset($arrBlackListOutput['verified_status']) && is_numeric($arrBlackListOutput['verified_status']) && 0 == $arrBlackListOutput['verified_status']) {
                $arrStrategy['certify'] = array(
                    "switch" => 2,
                    "text"   => '未实名',
                );
            }
        }

        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS,$arrStrategy);
    }

    /**
     * 圈子屏蔽逻辑
     * @param {Array} $arrParamInput
     * @return {Array} :{Int}errno :{String}errmsg :{Array}output
     * */
    public static function strategyCheckChannelBan($arrParamInput){


    }


    /**
     * @param {Array} $arrParamInput
     * @return {Array} :{Int}errno :{String}errmsg :{Array}output
     * */
    public static function strategyCheckStartLive($arrParamInput){
        $arrStrategy = array();
            $arrStrategy['start_live']=array(
                'switch' => 1,
                'text'   => '正常',
            );
            $intUserId = $arrParamInput['user_id'];
            $intForumId = intval($arrParamInput['forum_id']);
            $intIP = intval($arrParamInput['user_ip']);
            $strSubappType = $arrParamInput['subapp_type'];

            if(empty($intUserId)){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_SUCCESS,$arrStrategy);
        }
        // 后台权限设置
//        //Bingo_Log::warning(var_export($arrUserInfo,true));
//        if($arrUserInfo["user_perm"]){
//            if($arrUserInfo["user_perm"][Libs_Define_Perm::$arrPermList[Libs_Define_Perm::PERM_CANNOT_START_LIVE]]){
//                $intTime = intval($arrUserInfo["user_perm"][Libs_Define_Perm::$arrPermList[Libs_Define_Perm::PERM_CANNOT_START_LIVE]]);
//                if(time() < $intTime) {
//                    $arrStrategy["start_live"]=array(
//                        "switch" => 2,
//                        "text"   => "您已被封禁, 距离开播还有". Libs_Util_User::formatTime($intTime),
//                        "time"   => $intTime,
//                    );
//                    return self::errRet(Alalib_Conf_Error::ERR_SUCCESS,$arrStrategy);
//                }
//            }
//        }

        if (in_array($strSubappType, ['tieba', 'haokan', 'quanmin'])) {
            //是否命中wxb黑名单
            if (self::getAmisHashSwitch(self::AMIS_OPEN_LIVE_SWITCH_WXB_BLACK_LIST, 'switch_' . $strSubappType)) {
                $arrInput = array(
                    'user_id'   => $intUserId,
                    'call_from' => 'xiuchang',
                    'channel'   => $strSubappType,
                );
                $arrBlackListOutput = self::getWxbCertifyInfo($arrInput);
                //风控为了保险起见，如果未实名都一律返回命中黑名单。所以判断黑名单的条件 status = 0 && verified_status = 1
                if (isset($arrBlackListOutput['status']) && is_numeric($arrBlackListOutput['status']) && 0 == $arrBlackListOutput['status'] && //0封禁 1正常
                    isset($arrBlackListOutput['verified_status']) && is_numeric($arrBlackListOutput['verified_status']) && 1 == $arrBlackListOutput['verified_status']) {  //0已实名 1未实名
                    $text =  Alalib_Conf_Error::getErrorMsg(Alalib_Conf_Error::ERR_PERM_CANNOT_START_LIVE_WXB_BLACK_LIST);
                    $arrStrategy["start_live"] = array(
                        'switch'        => 2,
                        'text'          => $text,
                        'end_time'      => strtotime('2999-01-01'),
                        'remarks'       => $text,
                        'toast_content' => $text . '\\n',
                    );
                    return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrStrategy);
                }
            }
        }

        // 主播白名单
        $arrInput = array(
            "field" => "anchor_white",
        );
        $arrOutput = Tieba_Service::call("alamis","getMisItemConf",$arrInput,null,null,'post',null,'utf-8');

        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__. "::". __FUNCTION__."call alamis getMisItemConf fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            $arrOutput["data"] = array();
        }
        $boolSwitch = $arrOutput["data"]["anchor_white"]["value"];
        if($boolSwitch == 1){
            //$intUserId = 458964699;
            //开播白名单
            //TODO: <EMAIL> chenjinya#baidu.com
            $arrWhiteList = Alalib_Util_WhiteListLog::$arrWhiteUserIds;
            if(!isset($arrWhiteList[$intUserId])){
                $arrStrategy["start_live"]=array(
                    "switch" => 2,
                    "text"   => "功能维护中",
                );
                return self::errRet(Alalib_Conf_Error::ERR_SUCCESS,$arrStrategy);
            }
        }
        //ueg全局封禁状态查询  针对ueg对于ala的封禁
        $arrInput = array(
            'data' => array(
                'product' => 'ala',
                'user_id' => $intUserId,
            ),
        );
        $arrOutput = Tieba_Service::call("uegnewpro","newproQueryUserstate",$arrInput,null,null,'post',null,'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__. "::". __FUNCTION__." call uegnewpro/newproQueryUserstate  fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
        } else {
            if (isset($arrOutput['res']['gjinzhibo']['user_id']) && intval($arrOutput['res']['gjinzhibo']['user_id']) == $intUserId) {
                $intStartTime = strtotime($arrOutput['res']['gjinzhibo']['start_time']);
                $intEndTime = strtotime($arrOutput['res']['gjinzhibo']['end_time']);
                $intNowTime = time();
                if($intNowTime >= $intStartTime && $intNowTime < $intEndTime) {
                    $toastContent = '';
                    $remark = empty($arrOutput['res']['gjinzhibo']['remarks']) ? '违规内容' : $arrOutput['res']['gjinzhibo']['remarks'];
                    if ($intEndTime < $intNowTime + 2678400) { // 小于30天认为是非永久封禁
                        $toastContent = '因涉及' . $remark . "，您已被禁止开播\\n" . Libs_Util_User::formatTime($intEndTime) . '后可开播';
                    } else {
                        $toastContent = '因涉及' . $remark . '，您已被永久禁播\\n';
                    }

                    $arrStrategy["start_live"]=array(
                        "switch" => 2,
                        "text"   => "您的账号因直播内容涉嫌违规，已被禁止开播，距离解封还有" . Libs_Util_User::formatTime($intEndTime),
                        "end_time"   => $intEndTime,
                        "remarks"  => $arrOutput['res']['gjinzhibo']['remarks'],
                        'toast_content' => $toastContent,
                    );
                }
            }
        }

        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            // 该接口失败率高，重试一次
            $arrOutput = Tieba_Service::call("uegnewpro","newproQueryUserstate",$arrInput,null,null,'post',null,'utf-8');
            if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call uegnewpro/newproQueryUserstate  fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
                Bingo_Log::fatal($strLog);
                // 宽策略，失败认为没被禁
            } else{
                if (isset($arrOutput['res']['gjinzhibo']['user_id']) && intval($arrOutput['res']['gjinzhibo']['user_id']) == $intUserId) {
                    $intStartTime = strtotime($arrOutput['res']['gjinzhibo']['start_time']);
                    $intEndTime = strtotime($arrOutput['res']['gjinzhibo']['end_time']);
                    $intNowTime = time();
                    if($intNowTime >= $intStartTime && $intNowTime < $intEndTime) {
                        $toastContent = '';
                        $remark = empty($arrOutput['res']['gjinzhibo']['remarks']) ? '违规内容' : $arrOutput['res']['gjinzhibo']['remarks'];
                        if ($intEndTime < $intNowTime + 2678400) { // 小于30天认为是非永久封禁
                            $toastContent = '因涉及' . $remark . "，您已被禁止开播\\n" . Libs_Util_User::formatTime($intEndTime) . '后可开播';
                        } else {
                            $toastContent = '因涉及' . $remark . '，您已被永久禁播\\n';
                        }

                        $arrStrategy["start_live"]=array(
                            "switch" => 2,
                            "text"   => "您的账号因直播内容涉嫌违规，已被禁止开播，距离解封还有" . Libs_Util_User::formatTime($intEndTime),
                            "end_time"   => $intEndTime,
                            "remarks"  => $arrOutput['res']['gjinzhibo']['remarks'],
                            'toast_content' => $toastContent,
                        );
                    }
                }
            }
        }

        if (!empty($intForumId) && ($intUserId == 1096972466 || $intUserId == 1332721756)){
            // 1. 吧发帖权限检测，一般是吧等级限制
            $arrInput = array(
                "user_id" => $intUserId,
                "forum_id" => $intForumId,
                "user_ip" => 0,
            );
            $arrOutput = Tieba_Service::call("perm","getPerm",$arrInput,null,null,'post',null,'utf-8');
            if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
                $strLog = __CLASS__. "::". __FUNCTION__."call perm getPerm fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::fatal($strLog);
            }else{
                $arrParam = $arrOutput['output']['perm'];
                if($arrParam['can_post'] == false &&  $arrStrategy["start_live"]['switch'] == 1){
                    $arrStrategy["start_live"] = array(
                        "switch" => 2,
                        "text"   => "对不起，您不能在本吧开播，请选择其他吧进行开播。",
                    );
                }
            }

            // 2.ueg吧封禁等检测
            $arrInput=array(
                'forum_id'=> $intForumId,
                'user_id' => $intUserId,
            );
            if(!empty($intIP)){
                $arrInput['ip'] = $intIP;
            }
            $arrOutput = Tieba_Service::call("userstate","get_user_block_info",$arrInput,null,null,'post',null,'utf-8');
            if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
                $strLog = __CLASS__. "::". __FUNCTION__."call userstate get_user_block_info fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::fatal($strLog);
            }else{
                $arrRet = $arrOutput;
                if(count($arrRet['block_info'])&&  $arrStrategy["start_live"]['switch'] == 1){
                    // todo 优化 最好根据不同block_type返回不同文案
                    $arrStrategy["start_live"] = array(
                        "switch" => 2,
                        "text"   => "对不起，由于被封禁，您不能在本吧开播，请选择其他吧进行开播。",
                    );
                    Bingo_Log::warning("a blocked user want start_live. input:[".serialize($arrInput)."] output:[".serialize($arrOutput)."]");
                }
            }
        }



//        // 用户的封禁
//        $intForumId = intval($arrParamInput["forum_id"]);
//        if(!empty($intForumId) && !empty($intUserId)){
//            $strServiceName = 'anti';
//            $strServiceMethod = 'antiUserBlockQuery';
//            $arrInput = array(
//                "req" => array(
//                    "user_id" => $intUserId,
//                    "forum_id" => $intForumId,
//                ),
//            );
//            $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrInput);
//            if ($arrOutput === false || ($arrOutput['errno'] != Alalib_Conf_Error::ERR_SUCCESS && $arrOutput['errno'] != 220012)){  //用户被封禁错误码
//                $strLog = __CLASS__ . '::' . __FUNCTION__ . "call $strServiceName|$strServiceMethod fail. input [" . serialize($arrInput) . "] output [" . serialize($arrOutput) ."]";
//                Bingo_Log::fatal($strLog);
//                //容错处理
//                return self::errRet(Alalib_Conf_Error::ERR_SUCCESS,$arrStrategy);
//            }
//            $boolIsBlock = intval($arrOutput['is_block']);
//            if(1 == $boolIsBlock) {
//                $arrBlockInfo = $arrOutput['res']['block_info'];
//                $now = time();
//                $usermsg = "您已被封禁";
//                foreach ($arrBlockInfo as $blockInfo) {
//                    if($now > intval($blockInfo['start_time']) && $now < intval($blockInfo['end_time'])) {
//                        $hours = floor((intval($blockInfo['end_time']) - $now + 3600 - 1) / 3600);
//                        if(0 == $blockInfo['forum_id']) {
//                            $usermsg = "您已被全局封禁, 距离开播还有 $hours 小时";
//                        }
//                        if($intForumId == $blockInfo['forum_id']) {
//                            $usermsg = "您在本吧已被封禁, 距离开播还有 $hours 小时";
//                            break;
//                        }
//                    }
//                }
//                $arrStrategy["start_live"]=array(
//                    "switch" => 2,
//                    "text"   => $usermsg,
//                );
//                return self::errRet(Alalib_Conf_Error::ERR_SUCCESS,$arrStrategy);
//            }
//        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS,$arrStrategy);
    }

    /**
     * @param $arrParamInput
     * @return array
     */
    public static function strategyVerifyStatus($arrParamInput){
        $arrStrategy = array();
        
        $intClientType = intval($arrParamInput['client_type']);
        $strClientVersion = strval($arrParamInput['client_version']);
        
        if ($intClientType == 1 && Alalib_Util_Version::compare(Libs_Define_User::USER_AUTHEN_VERSION_IOS, $strClientVersion) >= 0) {
            $intAuthenType = Libs_Define_User::USER_AUTHEN_TYPE;
        } elseif ($intClientType == 2 && Alalib_Util_Version::compare(Libs_Define_User::USER_AUTHEN_VERSION_ANDORID, $strClientVersion) >= 0) {
            $intAuthenType = Libs_Define_User::USER_AUTHEN_TYPE;
        } else {
            $intAuthenType = Libs_Define_User::USER_AUTHEN_TYPE_DEFAULT;
        }
             
        $arrStrategy["user_verify"]=array(
            "switch" => 1,
            "type" => $intAuthenType,
            "text"   => "正常",
        );
        $intVerifyStatus = isset($arrParamInput["verify_info_status"]) ? intval($arrParamInput["verify_info_status"]) : -1;
        $intUserId = isset($arrParamInput["user_id"]) ? intval($arrParamInput["user_id"]) : 0;
        if($intVerifyStatus < 0){
            $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($arrParamInput)."];";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_SUCCESS,$arrStrategy);
        }
        /**
        if(true === Util_AlaUser::isNeedAuthen($intUserId)){
            if ($arrParamInput['subapp_type'] && $arrParamInput['verify_info_scene']) {
                $strSubAppType = $arrParamInput['subapp_type'];
                $arrVerifyScene = $arrParamInput['verify_info_scene'];
                if ($arrVerifyScene[$strSubAppType] != 1){
                    $arrStrategy["user_verify"]=array(
                        "switch" => 2,
                        "type" => $intAuthenType,
                        "text"   => "需要绑定手机并完成实名认证才能开始直播",
                    );
                }
            }

            if(Libs_Define_User::USER_AUTHEN_INFO_INIT == $intVerifyStatus) {
                $arrStrategy["user_verify"]=array(
                    "switch" => 2,
                    "type" => $intAuthenType,
                    "text"   => "需要绑定手机并完成实名认证才能开始直播",
                );
            }
            else if(Libs_Define_User::USER_AUTHEN_INFO_FAIL == $intVerifyStatus) {
                $arrStrategy["user_verify"]=array(
                    "switch" => 3,
                    "type" => $intAuthenType,
                    "text"   => "您的认证信息未通过，请重新修改提交后才能直播",
                );
            }
        }
         */
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS,$arrStrategy);
    }

    /**
     * 返回值
     * @param {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
     * @param {Array} $arrOutData default array()
     * @param {String} $strMsg default ""
     * @return {Array} :errno :errmsg :{Array}output
     * */
    private static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS,$arrOutData = array(),$strMsg = ""){
        $arrOutput = array();
        $arrOutput['errno'] = $intErrno;
        $arrOutput['errmsg'] = Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput['usermsg'] = $strMsg;
        $arrOutput['data'] = $arrOutData;
        return $arrOutput;
        
    }

    /**
     * 获取amis中的配置
     * @param $switchKey  string amis中的配置key
     * @param $field    string amis中的键
     * @return bool|mixed
     */
    public static function getAmisHashSwitch($switchKey, $field)
    {
        $arrReq    = array(
            'key' => $switchKey,
        );
        $arrOutput = Util_Redis::getHashFromRedis($arrReq);
        if (false === $arrOutput) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' Util_Redis::hmgetFromRedis fail. input: [' . serialize($arrReq) . ']; output: [' . serialize($arrOutput) . ']');
            return false;
        }

        $arrRet = array();
        foreach ($arrOutput as $key => $value) {
            $arrRet[$value['field']] = $value['value'];
        }

        if (isset($arrRet[$field])) {
            return $arrRet[$field] ? true : false;
        }

        return false;
    }
}

?>
