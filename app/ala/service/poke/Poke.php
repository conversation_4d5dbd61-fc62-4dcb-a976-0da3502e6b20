<?php
/**
 * Created by PhpStorm.
 * User: tan<PERSON><PERSON>
 * Date: 2020/11/03
 */
define("BINGO_ENCODE_LANG", "UTF-8");
define("MODULE", "Ala_service");

class Service_Poke_Poke
{
    const POKE_VERSION   = '4.1.0';  //戳一下开始版本
    const POKE_SWITCH    = 1; //戳一下开关
    const CAN_POKE_TIMES = 1;   //每次直播每个用户可戳次数上限

    const POKE_FLAG_UNABLE     = 0; //不可戳
    const POKE_FLAG_EXHAUST = 1; //可戳次数已用完
    const POKE_FLAG_ABLE       = 2; //可戳
    const IM_CONTENT           = '戳了一下';

    //获取pokeFlag
    public static function getPokeFlag($intPokeItems)
    {
        if (self::POKE_SWITCH) {
            return $intPokeItems >= self::CAN_POKE_TIMES ? self::POKE_FLAG_EXHAUST : self::POKE_FLAG_ABLE;
        } else {
            return self::POKE_FLAG_UNABLE;
        }
    }

    /**
     * 通过setnx做先到先拿的唯一锁
     * @param $pokeId
     * @param int $expire
     * @return bool|int
     */
    public static function getSnxLock($pokeId, $expire = 10)
    {
        $key = self::getSnxLockKey($pokeId);
        $arrReq = [
            'redis'   => Util_Redis::REDIS_NAME_ALA_NEW,
            'key'     => $key,
            'value'   => time(),
            'setnx'   => 1,
            'expire'  => $expire,
        ];
        $arrRes = Util_Redis::setToRedis($arrReq);
        if (false === $arrRes) {
            return false;
        }
        return 1;
    }

    public static function getSnxLockKey($pokeId) {
        return 'ala_live_poke_lock_' . $pokeId;
    }

    //解锁
    public static function dropSnxLock($pokeId)
    {
        $key = self::getSnxLockKey($pokeId);
        $arrReq = [
            'redis'   => Util_Redis::REDIS_NAME_ALA_NEW,
            'key'     => $key,
        ];
        $arrRes = Util_Redis::clearFromRedis($arrReq);
        return $arrRes;
    }

    public static function poke($arrInput)
    {
        $arrRet = array(
            'status' => Alalib_Conf_Error::ERR_SUCCESS,
        );

        //参数判断
        if (empty($arrInput['live_id']) || empty($arrInput['poke_user_id']) || empty($arrInput['charm_user_id'])) {
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }
        $intLiveId      = intval($arrInput['live_id']);
        $intPokeUserId  = intval($arrInput['poke_user_id']);
        $intCharmUserId = intval($arrInput['charm_user_id']);

        //检查是否满足poke次数
        $intPokedTimes = self::getPokeTimes($intLiveId, $intPokeUserId);
        if ($intPokedTimes === false) {
            return self::errRet(Alalib_Conf_Error::ERR_REDIS_CALL_FAIL);
        }

        $intPokeFlag = self::getPokeFlag($intPokedTimes);
        if ($intPokeFlag == self::POKE_FLAG_EXHAUST && $intPokeUserId != Service_MysteriousMan_MysteriousMan::MYSTERIOUS_MAN_USER_ID) {
            $arrRet['status']   = 1;
            $arrRet['msg']      = '用户可戳次数为0';
            $arrRet['poke_flag'] = $intPokeFlag;
            return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
        }

        //获取直播间信息
        $arrLiveGetInfoInput['live_ids'] = array($intLiveId);
        $arrLiveGetInfoOutput            = Tieba_Service::call('ala', 'liveGetInfo', $arrLiveGetInfoInput, null, null, 'post', null, 'utf-8');
        if (false === $arrLiveGetInfoOutput ||
            Tieba_Errcode::ERR_SUCCESS != $arrLiveGetInfoOutput["errno"] ||
            !$arrLiveGetInfoOutput['data'][$intLiveId]['live_info']['group_id']
        ) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala liveGetInfo fail. input:[" .
                json_encode($arrLiveGetInfoInput) . "]; output:[" . json_encode($arrLiveGetInfoOutput) . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_REDIS_CALL_FAIL);
        }
        $intGroupId = intval($arrLiveGetInfoOutput['data'][$intLiveId]['live_info']['group_id']);

        //获取用户信息
        $arrUserInfoInput  = array(
            "uids" => array($intCharmUserId, $intPokeUserId),
            "use_mysterious_man" => 1,
        );
        $arrUserInfoOutput = Tieba_Service::call('ala', 'userGetInfo', $arrUserInfoInput, null, null, "post", null, "utf-8");
        if (false === $arrUserInfoOutput || Tieba_Errcode::ERR_SUCCESS != $arrUserInfoOutput["errno"]) {
            $strLog = __CLASS__ . '::' . __FUNCTION__ . ' call ala userGetInfo fail. input:[' . json_encode($arrUserInfoInput) . ']; output:[' . json_encode($arrUserInfoOutput) . ']';
            Bingo_Log::warning($strLog);
        }
        $arrUserInfos         = $arrUserInfoOutput['data'];
        $strPokeUserNickname  = isset($arrUserInfos[$intPokeUserId]) ? $arrUserInfos[$intPokeUserId]['user_nickname'] : '';
        $strCharmUserNickname = isset($arrUserInfos[$intCharmUserId]) ? $arrUserInfos[$intCharmUserId]['user_nickname'] : '';
        if (empty($strPokeUserNickname) || empty($strCharmUserNickname)) {
            $strLog = __CLASS__ . '::' . __FUNCTION__ . ' get nickname fail. input:[' . json_encode($arrUserInfoInput) . ']; output:[' . json_encode($arrUserInfos) . ']';
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_REDIS_CALL_FAIL);
        }

        try {
            Dl_ALa_LivePoke::startTransaction();
            //写入数据库
            $arrInsertInput = array(
                'live_id'       => $intLiveId,
                'charm_user_id' => $intCharmUserId,
                'poke_user_id'  => $intPokeUserId,
                'create_time'   => time(),
            );
            $arrDlRet       = Dl_ALa_LivePoke::addPoke($arrInsertInput);
            if ($arrDlRet === false || $arrDlRet['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
                $strLog = __CLASS__ . '::' . __FUNCTION__ . '::Dl_ALa_LivePoke::insertPoke fail. input:[' .
                    json_encode($arrInput) . ']; output:[' . json_encode($arrDlRet) . ']';
                Bingo_Log::warning($strLog);
                return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
            }
            $intPokeId         = intval($arrDlRet['data']['poke_id']);
            $arrRet['poke_id'] = $intPokeId;

            //更新redis
            $bolIncrRet = self::incrPokeTimes($intLiveId, $intPokeUserId);
            if (!$bolIncrRet) {
                throw new Exception('incrPokeTimes failed');
            }

            //发送im
            $sendPokeImInput = array(
                'poke_id'             => $intPokeId,
                'poke_user_id'        => $intPokeUserId,
                'charm_user_id'       => $intCharmUserId,
                'poke_user_nickname'  => $strPokeUserNickname,
                'charm_user_nickname' => $strCharmUserNickname,
                'group_id'            => $intGroupId,
                'live_id'             => $intLiveId,
            );
            $bolSendImRet = self::sendPokeIm($sendPokeImInput);
            if (!$bolSendImRet) {
                throw new Exception('sendPokeIm failed');
            }

            Dl_ALa_LivePoke::commit();
        } catch (Exception $e) {
            //回滚数据库
            Dl_ALa_LivePoke::rollback();

            //重置redis次数
            if (isset($bolIncrRet) && $bolIncrRet) {
                self::setPokeTimes($intLiveId, $intPokeUserId, $intPokedTimes);
            }

            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' throw exception:' . $e->getMessage());
            return self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL);
        }

        ++$intPokedTimes;
        $arrRet['poke_flag']     = self::getPokeFlag($intPokedTimes);
        $arrRet['poke_user_id'] = $intPokeUserId;

        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

    private static function sendPokeBackIm($arrInput)
    {
        $arrImContent     = array(
            'content_type'           => 'live_poke_back',
            'is_show_other'          => 1,
            'poke_text'              => self::IM_CONTENT,
            'is_gift'                => $arrInput['gift_id'] > 0 ? 1 : 0,
            'gift_name'              => $arrInput['gift_name'],
            'charm_user_id'          => $arrInput['charm_user_id'],
            'poke_user_id'           => $arrInput['poke_user_id'],
            'pokeback_user_nickname' => $arrInput['poke_user_nickname'],
        );
        $arrImInput       = array(
            'live_id'  => $arrInput['live_id'],
            'group_id' => $arrInput['group_id'],
            'user_id'  => $arrInput['poke_user_id'],
            'msg_type' => Service_Im_Im::MSG_TYPE_LIVE_NOTICE,
            'content'  => json_encode($arrImContent),
        );
        $strServiceName   = 'ala';
        $strServiceMethod = 'imCommitPushNotifyGroupMsg';
        $arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrImInput, null, null, 'post', null, 'utf-8');

        //发不发成功都加个日志
        Bingo_Log::notice(__CLASS__ . '::' . __FUNCTION__ . '::sendIm, arrInput:[' . json_encode($arrImInput) . '], output:[' . json_encode($arrImInput));
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __class__ . '::' . __function__ . '  call imcommitpushnotifygroupmsg fail. input:[' . json_encode($arrImInput) . ']; output:[' . json_encode($arrImInput) . ']';
            Bingo_log::warning($strLog);
        }
    }

    // 并行发送戳一下im
    public static function sendPokeIm($arrInput)
    {
        $intGroupId           = intval($arrInput['group_id']);
        $intLiveId            = intval($arrInput['live_id']);
        $intPokeUserId        = intval($arrInput['poke_user_id']);
        $intCharmUserId       = intval($arrInput['charm_user_id']);
        $intPokeId            = intval($arrInput['poke_id']);
        $strPokeUserNickname  = $arrInput['poke_user_nickname'];
        $strCharmUserNickname = $arrInput['charm_user_nickname'];
        if ($intPokeUserId == Service_MysteriousMan_MysteriousMan::MYSTERIOUS_MAN_USER_ID) {
            $strPokeUserNickname = Service_MysteriousMan_MysteriousMan::MYSTERIOUS_MAN_NICKNAME;
        }
        $arrContent = array(
            'text'                => '主播使用了戳一下功能，更新版本可回戳',
            'content_type'        => 'remove_video',
            'poke_msg'            => 'poke_msg',
            'poke_text'           => self::IM_CONTENT,
            'poke_id'             => $intPokeId,
            'charm_user_id'       => $intCharmUserId,
            'poke_user_id'        => $intPokeUserId,
            'charm_user_nickname' => $strCharmUserNickname,
            'poke_user_nickname'  => $strPokeUserNickname,
            'gift_list'           => self::getPokeBackGiftList(),
            'is_show_other'       => 1,
        );
        $arrImInput = array(
            "live_id"     => $intLiveId,
            "user_id"     => $intCharmUserId,
            "to_uids"     => array($intPokeUserId),
            "user_name"   => $strPokeUserNickname,
            "anchor_id"   => $intCharmUserId,
            "group_id"    => $intGroupId,
            "msg_type"    => Service_Im_Im::MSG_TYPE_LIVE_NOTICE,
            "content"     => json_encode($arrContent),
            "subapp_type" => "quanmin"
        );
        $arrOutput  = Tieba_Service::call('ala', 'imCommitPushNotifyGroupMsg', $arrImInput, null, null, 'post', null, 'utf-8');

        Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . '::sendIm, ----arrInput:[' . json_encode($arrImInput) . '], ----output:[' . json_encode($arrOutput) . ']');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __class__ . '::' . __function__ . '  call imcommitpushnotifygroupmsg fail. input:[' . json_encode($arrImInput) . ']; output:[' . json_encode($arrOutput) . ']';
            Bingo_log::warning($strLog);
            return false;
        }
        return true;
    }

    //获取回戳的礼物列表
    private static function getPokeBackGiftList()
    {
        $key           = 'ala_pokeback_gift_list';
        $arrRedisInput = array(
            'key'   => $key,
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
        );

        $redisRet = Util_Redis::getFromRedis($arrRedisInput);
        if ($redisRet) {
            return $redisRet;
        }

        $arrGiftIds = array(
            10449, //撩一下
            10970, //水晶球
        );
//        $arrGiftIds = array(
//            10393, //大白
//            10449, //
//        );

        $arrParamInput = array(
            'gift_id' => $arrGiftIds,
        );
        $arrOutput     = Tieba_Service::call('present', 'getGiftById', $arrParamInput, null, null, 'POST', 'php', 'utf-8');
        if ($arrOutput === false || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call Present/getGiftById fail.[input]" . json_encode($arrParamInput));
            return false;
        }
        $arrGiftInfos = array_column($arrOutput['data'], null, 'gift_id');
        foreach ($arrGiftInfos as $giftId => $giftInfo) {
            $tmp              = array();
            $tmp['gift_id']   = $giftId;
            $tmp['gift_name'] = $giftInfo['gift_name'];
            $tmp['tdou']      = $giftInfo['price'];
            $tmp['pic']       = $giftInfo['large_thumbnail_url'];
            $tmp['remark']    = $giftInfo['price'] . 'T豆';
            $arrGiftList[]    = $tmp;
        }

        $arrGiftList[] = [
            'gift_id'   => 0,
            'gift_name' => '回戳一下',
            'tdou'      => 0,
            'pic'       => 'https://ala-gift.cdn.bcebos.com/gift/2020-10/1603423161843/100.png',
            'remark'    => '限时免费',
        ];

        $setRedisInput = array(
            'key'    => $key,
            'redis'  => Util_Redis::REDIS_NAME_ALA_NEW,
            'value'  => $arrGiftList,
            'expire' => 60 * 10,
        );
        Util_Redis::setToRedis($setRedisInput);
        return $arrGiftList;
    }

    public static function pokeBack($arrInput)
    {
        $intLiveId            = $arrInput['live_id'];
        $intGroupId           = $arrInput['group_id'];
        $intPokeUserId        = $arrInput['poke_user_id'];
        $intCharmUserId       = $arrInput['charm_user_id'];
        $intPokeId            = $arrInput['poke_id'];
        $strSceneFrom         = $arrInput['sceneFrom'];
        $intGiftId            = $arrInput['gift_id'];
        $intGiftPrice         = $arrInput['gift_price'];
        $strGiftName          = $arrInput['gift_name'];
        $strCharmUserNickname = $arrInput['charm_user_nickname'];
        $strPokeUserNickname  = $arrInput['poke_user_nickname'];

        $arrRet = array(
            'status' => Alalib_Conf_Error::ERR_SUCCESS,
            'msg'    => 'success'
        );

        //更新redis
        self::setPokeBackToRedis($intLiveId, $intPokeId);

        //更新数据库
        $updatePokeInput = array(
            'poke_id'              => $intPokeId,
            'update_time'          => time(),
            'poke_back_time'       => time(),
            'poke_back_gift_id'    => $intGiftId,
            'poke_back_gift_price' => $intGiftPrice,
            'scene_from'           => $strSceneFrom,
        );

        $updateRow = Dl_ALa_LivePoke::doPokeBackUpdate($updatePokeInput);
        if (!$updateRow) {
            $updateRow = Dl_ALa_LivePoke::doPokeBackUpdate($updatePokeInput);
            if (!$updateRow) {
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . 'doPokeBackUpdate failed, updatePokeInput:[' . json_encode($updatePokeInput) . ']');
            }
        }

        //更新redis
        $afterSetRedisRet = self::setPokeBackToRedis($intLiveId, $intPokeId);
        if (!$afterSetRedisRet) {
            $afterSetRedisRet = self::setPokeBackToRedis($intLiveId, $intPokeId);
            if (!$afterSetRedisRet) {
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . 'setPokeBackToRedis failed, intLiveId:' . $intLiveId . 'intPokeId:' . $intPokeId);
            }
        }

        //发送im
        $arrSendImInput                        = array();
        $arrSendImInput['charm_user_id']       = $intCharmUserId;
        $arrSendImInput['poke_user_id']        = $intPokeUserId;
        $arrSendImInput['gift_id']             = $intGiftId;
        $arrSendImInput['group_id']            = $intGroupId;
        $arrSendImInput['gift_name']           = $strGiftName;
        $arrSendImInput['charm_user_nickname'] = $strCharmUserNickname;
        $arrSendImInput['poke_user_nickname']  = $strPokeUserNickname;
        self::sendPokeBackIm($arrSendImInput);

        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrRet);
    }

    //回戳redis hash key
    private static function getPokeBackRedisKey($intLiveId)
    {
        return 'ala_poke_back_' . $intLiveId;
    }

    //是否已戳过
    public static function isAlreadyPokeBack($intLiveId, $intPokeId)
    {
        $strKey   = self::getPokeBackRedisKey($intLiveId);
        $arrInput = array(
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
            'key'   => $strKey,
            'field' => $intPokeId,
        );
        $arrRet   = Util_Redis::hgetFromRedis($arrInput);
        if ($arrRet == false) {
            Bingo_Log::warning(__CLASS__ . "::" . __FUNCTION__ . " call Util_Redis::mgetFromRedis fail! input:[" . json_encode($arrInput) . "]");
            return false;
        }
        return $arrRet;
    }

    //回戳之后将redis更新为已戳
    private static function setPokeBackToRedis($intLiveId, $intPokeId)
    {
        $strKey   = self::getPokeBackRedisKey($intLiveId);
        $arrInput = array(
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
            'key'   => $strKey,
            'field' => $intPokeId,
            'value' => 1,
        );
        $arrRet   = Util_Redis::hsetToRedis($arrInput);
        if ($arrRet == false) {
            Bingo_Log::warning(__CLASS__ . "::" . __FUNCTION__ . " call Util_Redis::hsetToRedis fail! input:[" . json_encode($arrInput) . "]");
            return false;
        }
        return $arrRet;
    }

    //清理直播间内戳一下缓存
    public static function clearPokeCache($intLiveId)
    {
        $arrKeys = array();
        $arrKeys[] = self::getPokeRedisKey($intLiveId);
        $arrKeys[] = self::getPokeBackRedisKey($intLiveId);

        $ret = true;
        foreach ($arrKeys as $strKey) {
            $arrInput = array(
                'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
                'key'   => $strKey,
            );
            $arrRet   = Util_Redis::clearFromRedis($arrInput);
            if ($arrRet == false) {
                Bingo_Log::warning(__CLASS__ . "::" . __FUNCTION__ . " call Util_Redis::clearFromRedis fail! input:[" . json_encode($arrInput) . "]");
                $ret = false;
            }
        }

        return $ret;
    }

    //主播戳一下redis hash key
    private static function getPokeRedisKey($intLiveId)
    {
        return 'ala_poke_list_' . $intLiveId;
    }

    //获取直播所有被戳用户次数
    public static function getPokeTimesList($intLiveId)
    {
        $strKey   = self::getPokeRedisKey($intLiveId);
        $arrInput = array(
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
            'key'   => $strKey,
        );
        $arrRet   = Util_Redis::hMGetAllPro($arrInput);
        if ($arrRet == false) {
            Bingo_Log::warning(__CLASS__ . "::" . __FUNCTION__ . " call Util_Redis::mgetFromRedis fail! input:[" . json_encode($arrInput) . "]");
            return false;
        }
        return $arrRet;
    }

    //获取用户当前直播间内被戳次数
    public static function getPokeTimes($intLiveId, $intPokeUserId)
    {
        $strKey   = self::getPokeRedisKey($intLiveId);
        $arrInput = array(
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
            'keys'  => array($strKey),
            'field' => $intPokeUserId,
        );
        $arrRet   = Util_Redis::mhgetFromRedis($arrInput);
        if ($arrRet == false) {
            Bingo_Log::warning(__CLASS__ . "::" . __FUNCTION__ . " call Util_Redis::mgetFromRedis fail! input:[" . json_encode($arrInput) . "]");
            return false;
        }

        $intTimes = is_null($arrRet[$strKey]) ? 0 : intval($arrRet[$strKey]);
        return $intTimes;
    }

    //设置被戳次数
    public static function setPokeTimes($intLiveId, $intPokeUserId, $intTimes)
    {
        $strKey   = self::getPokeRedisKey($intLiveId);
        $arrInput = array(
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
            'key'   => $strKey,
            'field' => $intPokeUserId,
            'value' => $intTimes,
        );
        $arrRet   = Util_Redis::hSet($arrInput);
        Bingo_Log::warning(__CLASS__ . "::" . __FUNCTION__ . "arrInput:[" . json_encode($arrInput). "]arrRet[" . json_encode($arrRet) . "]");

        if ($arrRet == false) {
            Bingo_Log::warning(__CLASS__ . "::" . __FUNCTION__ . " call Util_Redis::mgetFromRedis fail! input:[" . json_encode($arrInput) . "]");
            return false;
        }
        return $arrRet;
    }

    public static function incrPokeTimes($intLiveId, $intPokeUserId)
    {
        $strKey   = self::getPokeRedisKey($intLiveId);
        $arrInput = array(
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
            'key'   => $strKey,
            'field' => $intPokeUserId,
            'step'  => 1,
        );
        $arrRet   = Util_Redis::hincrBy($arrInput);
        if ($arrRet === false) {
            $arrRet = Util_Redis::hincrBy($arrInput);
            if ($arrRet === false) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call redis hincr fail. input:[" . serialize($arrInput) .
                    "]; output:[" . serialize($arrRet) . "]";
                Bingo_Log::fatal($strLog);
                return false;
            }
        }
        return true;
    }

    /**
     * 返回值
     * @param int $intErrno
     * @return array $arrOutput
     **/
    private static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = array(), $strUserMsg = "", $strErrMsg = "")
    {
        $arrOutput            = array();
        $arrOutput['errno']   = $intErrno;
        $arrOutput['errmsg']  = (strlen($strErrMsg) > 0) ? $strErrMsg : Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput['usermsg'] = $strUserMsg;
        $arrOutput['data']    = $arrOutData;
        return $arrOutput;
    }

    public static function pokeRedis($arrInput)
    {
        $m   = $arrInput['m'];
        $ret = array();
        if ($m == 'get') {
            $redisInput = array();
            Util_Redis::getFromRedis($redisInput);
        } else if ($m == 'poke_times_list') {
            $intLiveId   = $arrInput['live_id'];
            $list        = self::getPokeTimesList($intLiveId);
            $ret['list'] = $list;
        } else if ($m == 'reset_poke_times') {
            $intLiveId  = $arrInput['live_id'];
            $pokeUserId = $arrInput['poke_user_id'];
            $strKey     = self::getPokeRedisKey($intLiveId);
            $arrInput   = array(
                'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
                'key'   => $strKey,
                'field' => $pokeUserId,
            );
            $arrRet     = Util_Redis::hclearFromRedis($arrInput);
            if ($arrRet === false) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call redis hclearFromRedis fail. input:[" . json_encode($arrInput) .
                    "]; output:[" . json_encode($arrRet) . "]";
                Bingo_Log::fatal($strLog);
            }
            $ret = array('arrRet' => $arrRet);
        } else if ($m == 'gift_list') {
            $ret = self::getPokeBackGiftList();
        } else {
            $ret = array();
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $ret);
    }
}