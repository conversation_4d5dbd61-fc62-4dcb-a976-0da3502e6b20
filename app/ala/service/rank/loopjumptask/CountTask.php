<?php
/**
 * 特定数量礼物
 * Created by PhpStorm.
 * User: zhoujunhui01
 * Date: 2021/04/04
 * Time: 3:01 PM
 */

class Service_Rank_LoopJumpTask_CountTask extends Service_Rank_LoopJumpTask_BaseTask implements Service_Rank_LoopJumpTask_ActivityTask
{

    /**
     * 做任务 更新相关的任务数据
     * @param $arrInput
     * @return array
     */
    public static function doTask($arrInput)
    {
        $arrTaskData = $arrInput['task_data'];
        $arrTaskConf = $arrInput['task_conf'];
        $intGiftId   = $arrInput['gift_id'];
        $intGiftCnt  = intval($arrInput['gift_count']);

        // 判断送的礼物是不是目标礼物
        $arrSubTaskConf = $arrTaskConf['round_info'][$arrTaskData['round_no']];
        $arrGiftGoal    = $arrSubTaskConf['gift_goal'];
        if (empty($arrGiftGoal[$intGiftId])) {
            return self::errRet();
        }

        // 状态满足要求才做任务
        if (Service_Rank_LoopJumpTask_BaseTask::TASK_STATUS_DOING == $arrTaskData['status']) {
            // 更新任务状态
            $arrReqSetNum = [
                'redis'  => Util_Redis::REDIS_NAME_ALA_NEW,
                'key'    => $arrTaskData['addr'],
                'field'  => $intGiftId,
                'step'   => $intGiftCnt,
                'expire' => 86400,
            ];
            $arrNumRet    = Util_Redis::hincrBy($arrReqSetNum);
            if ($arrNumRet === false) {
                $arrNumRet = Util_Redis::hincrBy($arrReqSetNum);
                if ($arrNumRet === false) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call redis hincr fail. input:[" . serialize($arrReqSetNum) . "]; output:[" . serialize($arrNumRet) . "]";
                    Bingo_Log::fatal($strLog);
                    return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
            }
        }
        return self::errRet();
    }

    /**
     * 结算任务 发放奖励
     * @param $arrInput
     * @return array
     */
    public static function settleTask($arrInput)
    {
        $intActivityId    = $arrInput['activity_id'];
        $arrActivityConf  = $arrInput['activity_conf'];
        $arrComponentConf = $arrInput['component_conf'];
        $intCharmUserId   = $arrInput['charm_user_id'];
        $arrTaskConf      = $arrInput['task_conf'];
        $arrTaskData      = $arrInput['task_data'];
        $intLiveId        = $arrInput['live_id'];
        $intTrackId       = $arrInput['track_id'];
        $intSystemTime    = $arrInput['system_time'];
        //获取子任务详细配置项
        $arrSubTaskConf = $arrTaskConf['round_info'][$arrTaskData['round_no']];

        // 获取实时任务信息
        $res = self::getCurrentTaskInfo($arrTaskData['addr']);
        if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            return self::errRet($res['errno'], $res['ret']);
        }
        $arrActualValue = $res['ret'];
        $arrGoalValue   = $arrSubTaskConf['gift_goal'];
        $arrGoalRecord  = [];//用于数据库记录
        foreach ($arrGoalValue as $giftId => $value) {
            $arrGoalRecord[$giftId] = ['count' => $value['count']];
        }

        // 用于记录任务状态到db
        $taskRecord = [
            'sub_task_type' => $arrTaskData['sub_task_type'],
            'activity_id'   => $intActivityId,
            'time_tag'      => date('Ymd', $intSystemTime),
            'user_id'       => $intCharmUserId,
            'track_id'      => $intTrackId,
            'round_no'      => $arrTaskData['round_no'],
            'attempt_no'    => 1,
            'loop_no'       => $arrTaskData['loop_no'],
            'total_round'   => $arrTaskData['total_round'],
            'actual_value'  => json_encode($arrActualValue, JSON_UNESCAPED_UNICODE),
            'goal_value'    => json_encode($arrGoalRecord, JSON_UNESCAPED_UNICODE),
            'begin_time'    => $arrTaskData['begin_time'],
            'end_time'      => $arrTaskData['end_time'],
            'create_time'   => $intSystemTime,
        ];
        // 用于更新任务状态
        $taskInput = [
            'activity_id'   => $intActivityId,
            'sub_task_conf' => $arrSubTaskConf,
            'charm_user_id' => $intCharmUserId,
            'system_time'   => $intSystemTime,
            'total_round'   => $arrTaskData['total_round'],
            'round_no'      => $arrTaskData['round_no'],
            'loop_no'       => $arrTaskData['loop_no'],
        ];

        // 完成任务
        if (self::isfinishTask($arrActualValue, $arrGoalValue)) {
            $taskRecord['round_status'] = self::TASK_STATUS_DONE;
            $taskInput['status']        = self::TASK_STATUS_DONE;
            $taskInput['duration']      = $arrTaskConf['show_success_time'];
            $isCommit                   = false;//记录数据库是否成功

            try {
                Dl_Ala_ActivityLoopJumpTask::startTransaction();
                $insertId = Dl_Ala_ActivityLoopJumpTask::addRecord($taskRecord);
                $res      = self::setTaskStatus($taskInput);
                if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
                    Dl_Ala_ActivityLoopJumpTask::rollback();
                    return self::errRet($res['errno'], $res['ret']);
                }
                $arrTaskData = $res['ret'];
                Dl_Ala_ActivityLoopJumpTask::commit();
                $isCommit = true;
            } catch (Exception $e) {
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' add success record failed. input: ' . serialize($taskRecord) . ' msg: ' . $e->getMessage());
                Dl_Ala_ActivityLoopJumpTask::rollback();
            }

            // 发放奖励
            if (isset($insertId) && $insertId && isset($arrSubTaskConf['award']) && $isCommit) {
                self::sendAward($intSystemTime, $insertId, $arrSubTaskConf, $intCharmUserId, $intActivityId, $arrComponentConf, $arrTaskConf, $intLiveId, $arrTaskData);
            }
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrTaskData);
    }

    /**
     * 返回任务挂件所需数据
     * @param $arrInput
     * @return array
     */
    public static function taskPendant($arrInput)
    {
        $arrActivityConf  = $arrInput['activity_conf'];
        $intActivityId    = $arrInput['activity_id'];
        $arrTaskConf      = $arrInput['task_conf'];
        $arrComponentConf = $arrInput['component_conf'];
        $arrSubTaskConf   = $arrInput['sub_task_conf'];//这里要用传入的配置
        $arrTaskData      = $arrInput['task_data'];
        $intSystemTime    = $arrInput['system_time'];
        $intLiveId        = $arrInput['live_id'];
        $intTrackId       = $arrInput['track_id'];
        $intCharmUid      = $arrInput['charm_user_id'];

        // 轮询间隔
        $pullInterval = isset($arrTaskConf['pull_interval']) ? intval($arrTaskConf['pull_interval']) : 5;

        // 返回给FE的字段
        $arrPendant = [
            'track_id'          => $intTrackId,
            'sub_task_type'     => $arrTaskData['sub_task_type'],
            'pull_interval'     => $pullInterval,
            'current_time'      => $intSystemTime,
            'status_begin_time' => $arrTaskData['begin_time'],
            'status_end_time'   => $arrTaskData['end_time'],
            'round_no'          => $arrTaskData['round_no'],
            'total_round'       => $arrTaskData['total_round'],
            'task_info'         => [],
            'task_status'       => $arrTaskData['status'],
            'award_text'        => '',
            'day_rank'          => -1,
            'day_score'         => 0,
            'is_jump'           => isset($arrSubTaskConf['jump_info']) ? 1 : 0,
            'loop_no'           => $arrTaskData['loop_no'],
        ];

        if ($arrPendant['is_jump']) {
            // 计算回目加倍倍数
            $lastLoopNo = count($arrTaskConf['loop_rate']);
            if ($arrTaskData['loop_no'] >= $lastLoopNo) {
                $loopRate = end($arrTaskConf['loop_rate']);
            } else {
                $loopRate = $arrTaskConf['loop_rate'][$arrTaskData['loop_no']];
            }

            // 奖励分值动态变化
            $arrJumpInfoConf = $arrSubTaskConf['jump_info'];
            foreach ($arrJumpInfoConf as &$item) {
                if ($item['jump_award']['score'] > 0) {
                    $scoreText = '主播将获得' . intval($item['jump_award']['score'] * $loopRate) . '战力值';
                    $broadText = '全站广播';
                    $airText   = '直播间樱花宝箱掉落';

                    $awardText = $scoreText . '福利';
                    if (isset($item['jump_award']['broadcast']) && isset($item['jump_award']['air'])) {
                        $awardText = $scoreText . ',' . $broadText . '和' . $airText . '福利';
                    }

                    if (isset($item['jump_award']['broadcast']) && !isset($item['jump_award']['air'])) {
                        $awardText = $scoreText . '和' . $broadText . '福利';
                    }
                    $item['award_text'] = $awardText;
                }
            }
            unset($item);

            $arrPendant['jump_info'] = $arrJumpInfoConf;
        }

        // 奖励文案 todo：勋章待定
        $awardText = [];
        if (isset($arrSubTaskConf['award']['air'])) {
            $awardText['box'] = 1;
        }
        if (isset($arrSubTaskConf['award']['broadcast'])) {
            $awardText['broadcast'] = 1;
        }
        if (isset($arrSubTaskConf['award']['score'])) {
            $lastLoopNo = count($arrTaskConf['loop_rate']);
            if ($arrTaskData['loop_no'] >= $lastLoopNo) {
                $loopRate = end($arrTaskConf['loop_rate']);
            } else {
                $loopRate = $arrTaskConf['loop_rate'][$arrTaskData['loop_no']];
            }

            $awardText['score'] = intval($arrSubTaskConf['award']['score'] * $loopRate);
        }

        // 处理一键通关文案
        if ($arrTaskData['jump_round'] > 0) {
            $awardText   = [];
            $arrJumpInfo = $arrTaskData['jump_info'];
            $baseRoundNo = $arrJumpInfo['base_round_no']; // 跳之前所处关数
            $jumpInfo    = $arrTaskConf['round_info'][$baseRoundNo]['jump_info'];
            $jumpAward   = [];
            foreach ($jumpInfo as $item) {
                if ($arrTaskData['jump_round'] == $item['jump_round']) {
                    $jumpAward = $item['jump_award'];
                }
            }

            if (isset($jumpAward['air'])) {
                $awardText['box'] = 1;
            }
            if (isset($jumpAward['broadcast'])) {
                $awardText['broadcast'] = 1;
            }
            if (isset($jumpAward['score'])) {
                $lastLoopNo = count($arrTaskConf['loop_rate']);
                if ($arrTaskData['loop_no'] >= $lastLoopNo) {
                    $loopRate = end($arrTaskConf['loop_rate']);
                } else {
                    $loopRate = $arrTaskConf['loop_rate'][$arrTaskData['loop_no']];
                }

                $awardText['score'] = intval($jumpAward['score'] * $loopRate);
            }
        }

        $arrPendant['award_text'] = $awardText;

        // 如果是在任务进行中 需要展示任务进度
        if (self::TASK_STATUS_DOING == $arrTaskData['status']) {
            $res = self::getCurrentTaskInfo($arrTaskData['addr']);
            if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
                return self::errRet($res['errno'], $res['ret']);
            }

            $arrActualValue = $res['ret'];
            $arrGiftGoal    = $arrSubTaskConf['gift_goal'];
            $taskInfo       = [];
            foreach ($arrGiftGoal as $giftId => $value) {
                $taskItem = [
                    'name'         => $value['name'],
                    'goal_value'   => $value['count'],
                    'actual_value' => intval($arrActualValue[$giftId]) > $value['count'] ? $value['count'] : intval($arrActualValue[$giftId]),
                    'pic_url'      => $value['url'],
                ];
                array_push($taskInfo, $taskItem);
            }
            $arrPendant['task_info'] = $taskInfo;

        }

        if (self::TASK_STATUS_DONE == $arrTaskData['status'] || self::TASK_STATUS_ALLDONE == $arrTaskData['status']) {
            // 是否为一键通关
            if ($arrTaskData['jump_round'] > 0) {
                $arrPendant['jump_round'] = $arrTaskData['jump_round'];
            }
        }

        // 挂件底部分数与排名信息
        $res = self::getRankInfo($arrInput);
        if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' getTaskRankInfo fail. input: ' . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, ['msg' => 'getTaskRankInfo fail']);
        }

        $arrPendant['day_rank']  = $res['ret']['task_rank'];
        $arrPendant['day_score'] = $res['ret']['task_score'];

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrPendant);
    }

    /**
     * 从hash中获取当前任务信息,gift_id:gift_count
     * @param $addrKey
     * @return array
     */
    public static function getCurrentTaskInfo($addrKey)
    {
        $arrRedisOutput = Util_Redis::hMGetAllPro([
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
            'key'   => $addrKey,
        ]);
        if (false === $arrRedisOutput) {
            Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' hMGetAllPro fail. addrKey: ' . $addrKey);
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, ['msg' => 'get current task info failed']);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRedisOutput);
    }

    /**
     * 判断是否完成任务
     * @param $arrActualValue
     * @param $arrGoalValue
     * @return bool
     */
    private static function isfinishTask($arrActualValue, $arrGoalValue)
    {
        foreach ($arrGoalValue as $giftId => $value) {
            if ($arrActualValue[$giftId] < $value['count']) {
                return false;
            }
        }
        return true;
    }
}
