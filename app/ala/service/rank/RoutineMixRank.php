<?php
/**
 * Created by PhpStorm.
 * User: zhangpeng62
 * Date: 2019-07-02
 * Time: 16:36
 */

/**
 * 例行小时榜 所有端混排
 * Class Service_Rank_RoutineMixRank
 */
class Service_Rank_RoutineMixRank extends Service_Rank_RankBase {

    //榜单类型
    const RANK_TYPE_CHARM = 1;
    const RANK_TYPE_RICH  = 3;

    //子榜类型
    const RANK_TIME_TYPE_DAY = 1;
    const RANK_TIME_TYPE_WEEK = 2;
    const RANK_TIME_TYPE_TOTAL = 3;
    const RANK_TIME_TYPE_HOUR = 4;

    //榜单cache前缀
    const RANK_CACHE_PRE = "live_routine_mix_rank";

    /**
     * 例行榜单入口
     * @param $arrParamInput
     * @return array
     */
    public static function RoutineMixRank($arrParamInput)
    {
        $arrParamInput['activity_id'] = -1;//非活动榜单，设置activity_id = -1
        $intSendTime = intval($arrParamInput['send_gift_time']);
        $payUserId = intval($arrParamInput["user_id"]);
        $benefitUserId = intval($arrParamInput["to_user_id"]);
        $giftScore = intval(intval($arrParamInput["gift_score"]) / 100); //T豆换算成魅力值，这个值是本次消费的总数，不是单价
        $orderId = $arrParamInput["order_id"];

        //合法性校验
        if (empty($orderId) || empty($giftScore))
        {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $isJiaoyou = intval(isset($arrParamInput['is_jiaoyou']) ?$arrParamInput['is_jiaoyou'] :0);
        if($isJiaoyou == 1){
            return Service_Audio_Rank_RoutineMixRankAudio::RoutineMixRank($arrParamInput);
        }

        //对某些用户或者金主不进入榜单
        $arrUser = Util_AlaUser::getUserHiddenList();
        if (in_array($payUserId, $arrUser)) {
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        if (in_array($benefitUserId, $arrUser)) {
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $strRankHour = date('YmdH', $intSendTime);

        //主播小时榜
        $rankKey = self::getRankKey(self::RANK_TYPE_CHARM, self::RANK_TIME_TYPE_HOUR, $strRankHour);
        $rankRet = self::updateRankDataByRankType($payUserId, $benefitUserId, $rankKey, $giftScore, $orderId, self::RANK_TYPE_CHARM, $arrParamInput);
        if ($rankRet['errno'] != Tieba_Errcode::ERR_SUCCESS)
        {
            $arrRankFailExt = array(
                'pay_user_id' => $payUserId,
                'benefit_user_id' => $benefitUserId,
                'gift_score' => $giftScore,
                'send_gift_time' => $intSendTime,
                'order_id' => $orderId,
                'key' => $rankKey,
                'rank_type' => self::RANK_TYPE_CHARM,
            );
            self::logRankFail(Service_Rank_RankError::ERR_UPDATE_ROUTINE_CHARM_RANK, $intSendTime, -1, $arrRankFailExt);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 获取榜单数据
     * @param $limit
     * @param $rankType
     * @param $timeType
     * @param $dateTime
     * @return array|bool|mixed|null
     */
    public static function fetchRankData($limit, $rankType, $timeType, $dateTime)
    {
        //防注入&确保index命中
        $limit = intval($limit);
        $rankType = intval($rankType);
        $timeType = intval($timeType);
        if ($limit > 101) {
            $limit = 101;
        }

        //先从redis取榜单 若redis查询出错而且是查询历史榜单 则去db查询
        $redisRank = self::fetchRankDataFromRedis($limit, $rankType, $timeType, $dateTime);
        if (false === $redisRank) {
            if ($dateTime < self::formatTimeByType($timeType, time())) {
                $dbRank = self::fetchRankDataFromDb($limit, $rankType, $timeType, $dateTime);
                return $dbRank;
            }
            return false;
        }
        return $redisRank;
    }

    /**
     * 从redis取榜单
     * @param $limit
     * @param $rankType
     * @param $timeType
     * @param $dateTime
     * @return array|bool|mixed|null
     */
    public static function fetchRankDataFromRedis($limit, $rankType, $timeType, $dateTime)
    {
        $rankKey = self::getRankKey($rankType, $timeType, $dateTime);
        $arrInput = array(
            'key' => $rankKey,
            'start' => 0,
            'stop' => $limit - 1,
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
        );
        $arrOutput = Util_Redis::zRevRangeWithScoresFromRedis($arrInput);
        if (false === $arrOutput) {
            $strLog = __CLASS__ . '::' . __FUNCTION__ . ' zRevRangeWithScoresFromRedis fail. input:[' . serialize($arrInput) . ']; output:[' . serialize($arrOutput) . ']';
            Bingo_Log::fatal($strLog);
            return false;
        }
        if (isset($arrOutput[$rankKey]) && $arrOutput[$rankKey]) {
            return $arrOutput[$rankKey];
        } else {
            return [];
        }
    }

    /**
     * 从db取榜单
     * @param $limit
     * @param $rankType
     * @param $timeType
     * @param $dateTime
     * @return array|bool
     */
    private static function fetchRankDataFromDb($limit, $rankType, $timeType, $dateTime)
    {
        $arrInput = array(
            'date' => $dateTime,
            'time_type' => $timeType,
            'type' => $rankType,
            'order_item' => 'point',
            'order' => 'DESC',
            'limit' => $limit,
            'subapp_type' => 1,
        );
        $arrOutput = Dl_Ala_Rank::selectRankListOrderAll($arrInput);
        if (Alalib_Conf_Error::ERR_SUCCESS != $arrOutput['errno']) {
            $strLog = __CLASS__ . '::' . __FUNCTION__ . ' selectRankListOrderAll fail. input:[' . serialize($arrInput) . ']; output:[' . serialize($arrOutput) . ']';
            Bingo_Log::fatal($strLog);
            return false;
        }
        $arrRankList = [];
        foreach ($arrOutput['data'] as $value) {
            $arrRankList[] = [
                'member' => $value['user_id'],
                'score' => $value['point'],
            ];
        }
        return $arrRankList;
    }

    /**
     * 获取榜单中某个member的排名和分数信息
     * @param $member
     * @param $rankType
     * @param $timeType
     * @param $dateTime
     * @param array $arrRankList
     * @return array|bool|mixed|null
     */
    static public function getRankDataByMemberId($member, $rankType, $timeType, $dateTime, $arrRankList = [])
    {
        //返回数据格式
        $ret = array('current_rank' => 0, 'current_charm_value' => 0, 'up_charm_value' => 0, 'down_charm_value' => 0);

        //优先在现有排行榜中找
        $arrRankList = array_values($arrRankList);
        $intRankLength = count($arrRankList);
        for ($i = 0;$i < $intRankLength - 1;$i++) {
            if ($member == $arrRankList[$i]['member']) {
                $ret['current_rank'] = $i + 1;
                $ret['current_charm_value'] = intval($arrRankList[$i]['score']);
                $ret['current_contrib_value'] = intval($arrRankList[$i]['score']) * 100;
                $ret['up_charm_value'] = isset($arrRankList[$i - 1]) ? intval($arrRankList[$i - 1]['score']) : 0;
                $ret['down_charm_value'] = isset($arrRankList[$i + 1]) ? intval($arrRankList[$i + 1]['score']) : 0;

                $ret['up_contrib_value'] = isset($arrRankList[$i - 1]) ? intval($arrRankList[$i - 1]['score'])*100 : 0;
                $ret['down_contrib_value'] = isset($arrRankList[$i + 1]) ? intval($arrRankList[$i + 1]['score'])*100 : 0;
                return $ret;
            }
        }

        //从redis查 若redis查询出错而且是查询历史 则去db查询
        $redisRes = self::getRankDataByMemberIdFromRedis($member, $rankType, $timeType, $dateTime);
        if (false === $redisRes) {
            if ($dateTime < self::formatTimeByType($timeType, time())) {
                return self::getRankDataByMemberIdFromDb($member, $rankType, $timeType, $dateTime);
            }
            return false;
        }
        return $redisRes;
    }

    /**
     * 从redis查当前member排名
     * @param $member
     * @param $rankType
     * @param $timeType
     * @param $dateTime
     * @return array|bool
     */
    static public function getRankDataByMemberIdFromRedis($member, $rankType, $timeType, $dateTime)
    {
        //返回数据格式
        $ret = array('current_rank' => 0, 'current_charm_value' => 0, 'up_charm_value' => 0, 'down_charm_value' => 0);

        //查询排名
        $rankKey = self::getRankKey($rankType, $timeType, $dateTime);
        $arrInput = array(
            'key' => $rankKey,
            'member' => $member,
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
        );
        $arrOutput = Util_Redis::zRevRankFromRedis($arrInput);
        if (false === $arrOutput) {
            $strLog = __CLASS__ . '::' . __FUNCTION__ . ' zExistFromRedis Redis fail. input:[' . serialize($arrInput) . ']; output:[' . serialize($arrOutput) . ']';
            Bingo_Log::fatal($strLog);

            return false;
        } elseif (null === $arrOutput[$rankKey]) {
            return $ret;
        } else {
            $ret['current_rank'] = intval($arrOutput[$rankKey]) + 1;
        }

        //查询当前主播+前1名+后1名的分数
        if ($ret['current_rank'] == 1) {
            $arrInput = array(
                'key'   => $rankKey,
                'start' => 0,
                'stop'  => 1,
                'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
            );
            $arrOutput = Util_Redis::zRevRangeWithScoresFromRedis($arrInput);
            if (false === $arrOutput) {
                $strLog = __CLASS__ . '::' . __FUNCTION__ . ' zRevRangeWithScoresFromRedis Redis fail. input:[' . serialize($arrInput) . ']; output:[' . serialize($arrOutput) . ']';
                Bingo_Log::fatal($strLog);

                return false;
            }
            $ret['up_charm_value'] = 0;
            $ret['current_charm_value'] = intval($arrOutput[$rankKey][0]['score']);
            $ret['down_charm_value'] = isset($arrOutput[$rankKey][1]['score']) ? intval($arrOutput[$rankKey][1]['score']) : 0;
        } else {
            $arrInput = array(
                'key'   => $rankKey,
                'start' => $ret['current_rank'] - 2,
                'stop'  => $ret['current_rank'],
                'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
            );
            $arrOutput = Util_Redis::zRevRangeWithScoresFromRedis($arrInput);
            if (false === $arrOutput) {
                $strLog = __CLASS__ . '::' . __FUNCTION__ . ' zRevRangeWithScoresFromRedis Redis fail. input:[' . serialize($arrInput) . ']; output:[' . serialize($arrOutput) . ']';
                Bingo_Log::fatal($strLog);

                return false;
            }
            $ret['up_charm_value'] = intval($arrOutput[$rankKey][0]['score']);
            $ret['current_charm_value'] = intval($arrOutput[$rankKey][1]['score']);
            $ret['down_charm_value'] = isset($arrOutput[$rankKey][2]['score']) ? intval($arrOutput[$rankKey][2]['score']) : 0;
        }
        return $ret;
    }

    /**
     * 获取排名
     * @param $member
     * @param $rankType
     * @param $timeType
     * @param $dateTime
     * @return bool|int
     */
    public static function getUserRankFromRedis($member, $rankType, $timeType, $dateTime)
    {
        $rankKey = self::getRankKey($rankType, $timeType, $dateTime);
        $arrInput = array(
            'key' => $rankKey,
            'member' => $member,
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
        );
        $arrOutput = Util_Redis::zRevRankFromRedis($arrInput);
        if (false === $arrOutput) {
            $strLog = __CLASS__ . '::' . __FUNCTION__ . ' zExistFromRedis Redis fail. input:[' . serialize($arrInput) . ']; output:[' . serialize($arrOutput) . ']';
            Bingo_Log::fatal($strLog);

            return false;
        } elseif (null === $arrOutput[$rankKey]) {
            return 0;
        } else {
            return intval($arrOutput[$rankKey]) + 1;
        }
    }

    /**
     * 从db查当前member排名
     * @param $member
     * @param $rankType
     * @param $timeType
     * @param $dateTime
     * @return bool
     */
    static private function getRankDataByMemberIdFromDb($member, $rankType, $timeType, $dateTime)
    {
        $arrInput = array(
            'user_id' => $member,
            'type' => $rankType,
            'time_type' => $timeType,
            'date' => $dateTime,
            'subapp_type' => 1,
        );
        $arrOutput = Dl_Ala_Rank::selectRankbyUserId($arrInput);
        if ($arrOutput['errno'] == Alalib_Conf_Error::ERR_SUCCESS) {
            $record = $arrOutput['data'][0];
            $ret['current_rank'] = $record['rank'];
            $ret['current_charm_value'] = $record['point'];
            return $ret;
        } else {
            return false;
        }
    }

    /**
     * 构造榜单key
     * @param $rankType // 1=魅力榜 3=大哥榜
     * @param $timeType // 1=日榜 2=周榜 3=总榜 4=小时榜
     * @param $dateTime // 时间格式
     * @return string
     */
    public static function getRankKey($rankType, $timeType, $dateTime)
    {
        $ret = self::RANK_CACHE_PRE . "#{$rankType}#{$timeType}#{$dateTime}#";
        return $ret;
    }

    /**
     * 将int型时间戳转换为$timeType对应的str日期
     * @param $timeType
     * @param $intTime
     * @return bool|false|int
     */
    private static function formatTimeByType($timeType, $intTime)
    {
        if (self::RANK_TIME_TYPE_HOUR == $timeType) {
            return date('YmdH', $intTime);
        } elseif (self::RANK_TIME_TYPE_DAY == $timeType) {
            return date('Ymd', $intTime);
        } else {
            return false;
        }
    }
}
