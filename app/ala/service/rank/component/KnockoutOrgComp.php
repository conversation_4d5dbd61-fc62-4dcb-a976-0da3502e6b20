<?php
/**
 * Created by PhpStorm.
 * User: zhangpeng62
 * Date: 2019-11-11
 * Time: 13:12
 */

class Service_Rank_Component_KnockoutOrgComp extends Service_Rank_Component_BaseComponent implements Service_Rank_Component_ActivityComponent
{
    const COMPONENT_ID = 10015;
    const COMPONENT_NAME = 'KnockoutOrgComp';

    // 根据当前时间返回活动配置
    public static function componentConfig($arrInput)
    {
        $arrActivityConf = $arrInput['activity_conf'];
        $arrComponentConf = $arrInput['component_conf'];
        $intCharmUid = intval($arrInput['to_user_id']);
        $intSystemTime = intval($arrInput['system_time']);

        $strShowPendantType = 'h5_static';
        $intOrgId = self::getUserOrgId($intCharmUid);
        if ($intOrgId > 0) {
            $res = self::getCurrentWinner($intSystemTime, $arrActivityConf, $arrComponentConf, false, false, $intOrgId);
            if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getCurrentWinner fail. input: ' . serialize(func_get_args()));
                return self::errRet($res['errno']);
            }
            $winnerHash = $res['ret'];
            if (is_array($winnerHash) && isset($winnerHash[$intCharmUid])) {
                $strShowPendantType = 'h5_dynamic';
            } elseif ($winnerHash == -2){
                $strShowPendantType = 'h5_settle';
            }
        }

        $arrActivityConf['pic_type'] = Libs_Util_ActivityRank::PIC_TYPE_H5;
        if ($strShowPendantType == 'h5_dynamic') {
            $dynamicH5Url = $arrComponentConf['h5_dynamic_pendant_conf']['dynamic_url'];
            self::urlAppendArg($dynamicH5Url, ['activity_id' => $arrActivityConf['activity_id']]);
            $arrActivityConf['h5_url'] = $dynamicH5Url;
            $arrActivityConf['pic_loc_info'] = $arrComponentConf['h5_dynamic_pendant_conf']['pic_loc_info'];
        } elseif($strShowPendantType == 'h5_settle') {
            $settleH5Url = $arrComponentConf['h5_settle_pendant_conf']['settle_url'];
            $arrActivityConf['h5_url'] = $settleH5Url;
            $arrActivityConf['pic_loc_info'] = $arrComponentConf['h5_settle_pendant_conf']['pic_loc_info'];
        } else {
            $staticH5Url = $arrComponentConf['h5_static_pendant_conf']['static_url'];
            $arrActivityConf['h5_url'] = $staticH5Url;
            $arrActivityConf['pic_loc_info'] = $arrComponentConf['h5_static_pendant_conf']['pic_loc_info'];
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrActivityConf);
    }

    // 进行送礼后的处理
    public static function componentSendGift($arrInput)
    {
        $intActivityId    = $arrInput['activity_id'];
        $arrActivityConf  = $arrInput['activity_conf'];
        $arrComponentConf = $arrInput['component_conf'];
        $intPayUserId     = intval($arrInput['user_id']);
        $intBenefitUserId = intval($arrInput['to_user_id']);
        $intScore         = intval($arrInput['gift_score'] / 100);
        $intSendGiftTime  = $arrInput['send_gift_time'];
        $intGiftId        = $arrInput['gift_id'];

        // 检查时间是否在打榜时间内
        if ($intSendGiftTime < $arrComponentConf['begin_time'] || $intSendGiftTime > $arrComponentConf['end_time']) {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        // 检查组件配置是否正确
        if (self::COMPONENT_NAME != $arrComponentConf['component_name']) {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 获取公会id
        $intOrgId = self::getUserOrgId($intBenefitUserId);
        if (!$intOrgId) {
            return self::errRet();
        }

        // 判断是否晋级
        $res = self::getCurrentWinner($intSendGiftTime, $arrActivityConf, $arrComponentConf, false, false, $intOrgId);
        if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getCurrentWinner fail. input: ' . serialize(func_get_args()));
            return self::errRet($res['errno']);
        }
        $winnerHash = $res['ret'];
        if ($winnerHash == -2) {
            // 目前晋级名单未出 记录到结算buffer
            $arrBufferData = [
                'charm_id' => $intBenefitUserId,
                'rich_id' => $intPayUserId,
                'score' => $intScore,
                'org_id' => $intOrgId,
            ];
            $strSendGiftDay = date("Ymd", $intSendGiftTime);
            $strBufferKey   = self::getSettleBufferKey($intActivityId, 0, $strSendGiftDay);
            $strBufferData  = serialize($arrBufferData);
            $buffInput = [
                'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
                'key' => $strBufferKey,
                'value' => $strBufferData,
            ];
            $buffOutput = Util_Redis::RPUSH($buffInput);
            if (false === $buffOutput) {
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' Util_Redis::RPUSH fail! ' . serialize($buffInput));
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            // 入buff记录日志
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' Util_Redis::RPUSH success! ' . $strBufferData);
            return self::errRet();
        }
        if (empty($winnerHash) || !isset($winnerHash[$intBenefitUserId])) {
            return self::errRet();
        }

        // 更新公会日榜
        $intComponentIndex = $arrComponentConf['index'];
        $strSendGiftDay = date('Ymd', $intSendGiftTime);
        $strRankKey = self::getRankKey($intActivityId, $intComponentIndex, self::COMPONENT_ID, self::SCORE_TYPE_ORG_CHARM, self::RANK_TYPE_DAY, $strSendGiftDay);
        self::updateRankData($intActivityId, $intSendGiftTime, $strRankKey, $intOrgId, $intScore, 86400, $arrInput);

        // 更新主播威信榜
        //公会之星排名，由二三赛段前6公会第一名，改为第三赛段全部主播前三名
//        $strRankKey = self::getRankKey($intActivityId, $intComponentIndex, self::COMPONENT_ID, self::SCORE_TYPE_CHARM, self::RANK_TYPE_TOTAL, '', $intOrgId);
//        self::updateRankData($intActivityId, $intSendGiftTime, $strRankKey, $intBenefitUserId, $intScore, 86400, $arrInput, $intOrgId);

        // 更新公会主播日榜
        $orgCharmRankKey = self::getRankKey($intActivityId, $intComponentIndex, self::COMPONENT_ID, self::SCORE_TYPE_CHARM, self::RANK_TYPE_DAY, $strSendGiftDay, $intOrgId);
        self::updateRankData($intActivityId, $intSendGiftTime, $orgCharmRankKey, $intBenefitUserId, $intScore, 86400, $arrInput, $intOrgId);

        // 更新公会小时榜 公会主播小时榜
        $arrAwardHour = array_column($arrComponentConf['special_hour'], 'value');
        $strSendGiftHourPure = date('H', $intSendGiftTime);
        $strSendGiftHour = date('YmdH', $intSendGiftTime);
        if (in_array($strSendGiftHourPure, $arrAwardHour)) {
            $strRankKey = self::getRankKey($intActivityId, $intComponentIndex, self::COMPONENT_ID, self::SCORE_TYPE_ORG_CHARM, self::RANK_TYPE_HOUR, $strSendGiftHour);
            self::updateRankData($intActivityId, $intSendGiftTime, $strRankKey, $intOrgId, $intScore, 86400, $arrInput);
            //公会主播小时榜
            $strCharmHourRankKey = self::getRankKey($intActivityId, $intComponentIndex, self::COMPONENT_ID, self::SCORE_TYPE_CHARM, self::RANK_TYPE_HOUR, $strSendGiftHour, $intOrgId);
            self::updateRankData($intActivityId, $intSendGiftTime, $strCharmHourRankKey, $intBenefitUserId, $intScore, 86400, $arrInput, $intOrgId);
        }

        return self::errRet();
    }

    // 返回输入榜单对应的公会个数
    public static function componentOrgCount($arrInput)
    {
        $arrComponentConf  = $arrInput['component_conf'];
        return $arrComponentConf['show_org_count'];
    }

    // 返回输入榜单对应的主播个数
    public static function componentPlayerCount($arrInput)
    {
        $arrComponentConf  = $arrInput['component_conf'];
        return $arrComponentConf['show_org_player_count'];
    }

    // 返回输入榜单对应的公会晋级淘汰的分界线
    public static function componentOrgWinnerBorder($arrInput)
    {
        $arrComponentConf  = $arrInput['component_conf'];
        $intRankType       = $arrInput['rank_type'];
        $intScoreType      = $arrInput['score_type'];
        if ($intScoreType === self::SCORE_TYPE_ORG_POINT && $intRankType === self::RANK_TYPE_COMPONENT) {
            return $arrComponentConf['org_winner_count'];
        } else {
            return 0;
        }
    }

    // 返回输入榜单对应的奖励分界线
    public static function componentWinnerBorder($arrInput)
    {
        return 0;
    }

    // 返回输入日期结束后的主播晋级名单
    public static function componentWinnerList($arrInput)
    {
        $strDate          = $arrInput['date'];
        $intActivityId    = $arrInput['activity_id'];
        $intTrackId       = isset($arrInput['track_id']) ? $arrInput['track_id'] : 0;
        $arrActivityConf  = $arrInput['activity_conf'];
        $arrComponentConf = $arrInput['component_conf'];
        $boolNoCache      = isset($arrInput['no_cache']) && $arrInput['no_cache'] ? true : false;
        $boolClearCache   = isset($arrInput['clear_cache']) && $arrInput['clear_cache'] ? true : false;

        // 本组件最后一天 直接产出晋级公会的完整晋级名单
        $strComponentEndDate = date('Ymd', $arrComponentConf['end_time']);
        if ($strDate == $strComponentEndDate) {
            $winnerCacheKey = self::getWinnerCacheKey($intActivityId, $strDate);
            // 如果监控脚本发现当前cache中的数据不对 会通过该参数清理cache然后重新生成
            if ($boolClearCache) {
                $res = self::clearRankCache($winnerCacheKey);
                if (false === $res) {
                    $res = self::clearRankCache($winnerCacheKey);
                    if (false === $res) {
                        return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                    }
                }
            }
            if (!$boolNoCache) {
                // 重试3次
                $retry = 3;
                $cache = false;
                while ($retry > 0 && $cache === false) {
                    $cache = Util_Redis::getFromRedis([
                        'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
                        'key'   => $winnerCacheKey,
                    ]);
                    $retry--;
                }
                if (false === $cache) {
                    Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getFromRedis failed. key: ' . $winnerCacheKey);
                    // 这里不返回
                }
                if ($cache) {
                    return self::errRet(Tieba_Errcode::ERR_SUCCESS, $cache);
                }
            }

            // 延迟结算
            $intDateEndTime = strtotime($strDate) + 86400;
            $buffTime = intval($arrComponentConf['winner_settle_buffer']);
            if (time() <= $intDateEndTime + $buffTime) {
                return self::errRet(Tieba_Errcode::ERR_SUCCESS, ['charm_winner_hash' => -2, 'org_winner_hash' => -2]);
            }

            $intOrgWinnerCount = $arrComponentConf['org_winner_count'];
            $intComponentIndex = $arrComponentConf['index'];
            $strWinnerRankKey = self::getRankKey($intActivityId, $intComponentIndex, self::COMPONENT_ID, self::SCORE_TYPE_ORG_POINT, self::RANK_TYPE_COMPONENT);
            $arrOutput = self::getTopRank($intOrgWinnerCount, $strWinnerRankKey);
            if (Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getTopRank failed. key: ' . $strWinnerRankKey .' count:'. $intOrgWinnerCount);
                return self::errRet($arrOutput['errno']);
            }
            $rankList = isset($arrOutput['ret']) && $arrOutput['ret'] ? $arrOutput['ret'] : [];
            // 获取每个晋级公会的晋级主播
            $totalWinnerCharmHash = [];
            $totalWinnerOrgHash = [];
            foreach ($rankList as $key => $value) {
                $winnerOrgId = $value['member'];
                $res = Service_Rank_Component_BaseComponent::getCurrentWinner($arrComponentConf['begin_time'], $arrActivityConf, $arrComponentConf, false, false, $winnerOrgId);
                if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
                    Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getCurrentWinner fail. org_id: ' . $winnerOrgId);
                    return self::errRet($res['errno']);
                }
                $winnerHash = $res['ret'];
                if (empty($winnerHash)) {
                    Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getCurrentWinner empty. org_id: ' . $winnerOrgId);
                    return self::errRet($res['errno']);
                }
                foreach ($winnerHash as $uid => $dummy) {
                    $totalWinnerCharmHash[$uid] = $winnerOrgId;
                    $totalWinnerOrgHash[$winnerOrgId] = $key + 1;
                }
            }

            $winnerCacheData = [
                'charm_winner_hash' => $totalWinnerCharmHash,
                'org_winner_hash' => $totalWinnerOrgHash,
            ];
            // 读cache不为false时才写cache 不然每次读cache失败都会写cache
            if (!$boolNoCache && false !== $cache) { // 这里不能加isset($cache) 因为初始状态$cache为null
                $arrInput = [
                    'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
                    'key'   => $winnerCacheKey,
                    'value' => $winnerCacheData,
                    'expire' => 86400 * 30,
                ];
                // attention! 偶尔会出现晋级名单为空的异常情况 如果为空则不设置cache
                if (!empty($totalWinnerCharmHash) && !empty($totalWinnerOrgHash)) {
                    $arrOutput = Util_Redis::setToRedis($arrInput);
                    if (false === $arrOutput) {
                        Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' setToRedis fail. input: ' . serialize($arrInput));
                        // 这里出错不严重(下次送礼还会触发生成缓存) 所以不返回
                    } else {
                        // 重要事件 记录日志
                        Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' setToRedis success. input: ' . serialize($arrInput) . ' $_ENV: ' . serialize($_ENV) . ' $_SERVER: ' . serialize($_SERVER));
                    }
                    // 重要事件 短信邮件通知
//                    self::sendWarning(__CLASS__ . '::' . __FUNCTION__ . ' setToRedis. input: ' . json_encode($arrInput) . ' output: '. json_encode($arrOutput));
                } else {
                    // 如果执行到这里说明晋级名单为空 是很异常的情况 打印当时的环境变量 以便找问题原因
                    $strLog = __CLASS__ . '::' . __FUNCTION__ . ' setToRedis_empty. input: ' . json_encode($arrInput) . ' rankList: '. json_encode($rankList) . ' _ENV: ' . json_encode($_ENV) . ' _SERVER: ' . json_encode($_SERVER);
                    self::sendWarning($strLog);
                    Bingo_Log::fatal($strLog);
                }
            }
            return self::errRet(Tieba_Errcode::ERR_SUCCESS, $winnerCacheData);
        } else {
            // 非最后一天 返回上个组件最后一天的晋级名单(上个组件做了缓存所以这里不再缓存 否则会产生数据冗余)
            $intComponentIndex = $arrComponentConf['index'];
            $lastComponentIndex = $intComponentIndex - 1;
            $lastComponentConf = $arrActivityConf['component_conf_list'][$lastComponentIndex - 1];
            if (empty($lastComponentConf)) {
                return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $componentName = self::COMPONENT_NAME_PRE . $lastComponentConf['component_name'];
            $componentInput = [
                'date'           => date('Ymd', $lastComponentConf['end_time']), //时间传上一组件最后一天
                'activity_id'    => $arrActivityConf['activity_id'],
                'activity_conf'  => $arrActivityConf,
                'component_conf' => $lastComponentConf,
                'track_id'       => $intTrackId,
            ];
            $componentMethod = 'componentWinnerList';
            $arrOutput = $componentName::$componentMethod($componentInput);
            if (Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' componentWinnerList fail. input: ' . serialize($componentInput));
                return self::errRet($arrOutput['errno']);
            }
            $winnerHash = $arrOutput['ret'];
            return self::errRet(Tieba_Errcode::ERR_SUCCESS, $winnerHash);
        }
    }

    // 手动执行的监控 根据输入打印相关数据
    public static function componentManualMonitor($arrInput)
    {
        $intActivityId     = $arrInput['activity_id'];
        $intComponentIndex = $arrInput['component_index'];
        $arrActivityConf   = $arrInput['activity_conf'];
        $arrComponentConf  = $arrInput['component_conf'];
        $intSystemTime     = $arrInput['system_time'];
        $intOrgId          = $arrInput['org_id'];

        $intErrorQueueLimit = 100;
        $input = [];

        // 错误队列
        $strErrorQueueKey = self::getErrorQueueKey($intActivityId, $intSystemTime);
        $input['error_queue'] = [
            'key'   => $strErrorQueueKey,
            'limit' => $intErrorQueueLimit
        ];
        $strLastDay = date('Ymd', strtotime(date('Ymd', $intSystemTime)) - 1);
        $dayKey = self::getDayBenefitPointResKey($intActivityId, $strLastDay);
        $input['day_benefit_ret'] = [
            'key'   => $dayKey,
        ];
        $strLastHour = date('YmdH', intval($intSystemTime) - 3600);
        $hourKey = self::getHourBenefitPointResKey($intActivityId, $strLastHour);
        $input['hour_benefit_ret'] = [
            'key'   => $hourKey,
        ];

        // 获取监控数据
        $res = self::getManualMonitorData($input);
        if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            return self::errRet($res['errno'], $res['ret']);
        }
        $result = $res['ret'];
        if (!empty($intOrgId)) {
            $res = self::getCurrentWinner($intSystemTime, $arrActivityConf, $arrComponentConf, false, false, $intOrgId);
            if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getCurrentWinner fail. input: ' . serialize(func_get_args()));
                return self::errRet($res['errno']);
            }
            $winnerHash = $res['ret'];
            $result['winnerHash'] = $winnerHash;
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $result);
    }

    // 自动执行的监控 校验数据出错时报警
    public static function componentMonitor($arrInput)
    {
        $intActivityId     = $arrInput['activity_id'];
        $intComponentIndex = $arrInput['component_index'];
        $arrActivityConf   = $arrInput['activity_conf'];
        $arrComponentConf  = $arrInput['component_conf'];
        $intSystemTime     = $arrInput['system_time'];

        // 当前时间不在赛段内
        if ($intSystemTime < $arrComponentConf['begin_time'] || $intSystemTime > $arrComponentConf['end_time']) {
            return self::errRet();
        }

        $res    = self::handleSettleBuffer($arrActivityConf, $arrComponentConf, $intSystemTime);
        $result = $res['ret'];

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $result);
    }

    // 定时任务脚本调用到此方法时需要执行的事项
    public static function componentTask($arrInput)
    {
        $intActivityId     = $arrInput['activity_id'];
        $arrActivityConf   = $arrInput['activity_conf'];
        $arrComponentConf  = $arrInput['component_conf'];
        $intSystemTime     = $arrInput['system_time'];
        $intComponentIndex = $arrComponentConf['index'];
        $intComponentId    = $arrComponentConf['component_id'];
        $intLastDayTime    = intval($intSystemTime) - 86400;
        $intLastHourTime   = $intSystemTime - 3600;
        $strLastDay        = date("Ymd",$intLastDayTime);
        $strCurrentDay     = date("Ymd",$intSystemTime);

        $arrDayAwardList = $arrComponentConf['day_rank_award'];
        $arrHourAwardList = $arrComponentConf['hour_rank_award'];

        // 加成到公会积分赛段榜
        $strOrgPointRankKey      = self::getRankKey($intActivityId, $intComponentIndex, $intComponentId, self::SCORE_TYPE_ORG_POINT, self::RANK_TYPE_COMPONENT);

        // 日榜加成
        if (isset($arrDayAwardList[$strLastDay]) && !empty($arrDayAwardList[$strLastDay])) {
            $arrDayAwards = $arrDayAwardList[$strLastDay];
            $intComponentBeginDay = strtotime(date('Ymd', $arrComponentConf['begin_time']));//之所以这样写是因为赛段有可能不是从0点开启
            if ($intLastDayTime >= $intComponentBeginDay && $intLastDayTime < $arrComponentConf['end_time']) {
                self::handleDayBenefitPoint($arrActivityConf,$intSystemTime,[$strOrgPointRankKey],$arrDayAwards,0,self::SCORE_TYPE_ORG_CHARM);
            }
        }

        // 小时榜加成
        if (isset($arrHourAwardList[$strCurrentDay]) && !empty($arrHourAwardList[$strCurrentDay])) {
            $arrHourAwards = $arrHourAwardList[$strCurrentDay];
            if ($intLastHourTime >= $arrComponentConf['begin_time'] && $intLastHourTime < $arrComponentConf['end_time']) {
                self::handleHourBenefitPoint($arrActivityConf,$intSystemTime,[$strOrgPointRankKey],$arrHourAwards,0,self::SCORE_TYPE_ORG_CHARM);
            }
        }

    }

    /**
     * 将结算buffer里的数据转化到榜单上
     * @param $arrActivityConf
     * @param $arrComponentConf
     * @param $intSystemTime
     * @param $intTrackId
     * @param $winnerHash
     * @return array
     */
    private static function handleSettleBuffer($arrActivityConf, $arrComponentConf, $intSystemTime)
    {
        $intActivityId = $arrActivityConf['activity_id'];
        $strSystemDate = date('Ymd', $intSystemTime);

        $strBufferKey = self::getSettleBufferKey($intActivityId, 0, $strSystemDate);
        //取出全部队列通过公会获取全部主播  每次501条数据
        $arrInput = [
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
            'key'   => $strBufferKey,
            'start' => 0,
            'stop'  => 500,
        ];
        $arrOutput = Util_Redis::LRANGE($arrInput);
        if (false === $arrOutput) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' Util_Redis::LRANGE fail! ' . $strBufferKey);
            return self::errRet();
        }
        $strBufferDatas = $arrOutput[$strBufferKey];
        if (empty($strBufferDatas)) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' Util_Redis::LRANGE empty! ' . $strBufferDatas);
            return self::errRet();
        }
        $winnerHash = array();
        $orgHash    = array();//已查询过的公会
        foreach ($strBufferDatas as $strBufferData) {
            $arrBufferData = unserialize($strBufferData);
            // 判断是否晋级
            $intOrgId = $arrBufferData['org_id'];
            if (!isset($orgHash[$intOrgId]) && !empty($intOrgId)) {
                $res = self::getCurrentWinner($intSystemTime, $arrActivityConf, $arrComponentConf, false, false, $intOrgId);
                if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
                    Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getCurrentWinner fail. input: ' . serialize(func_get_args()));
                    return self::errRet();
                }
                if ($res['ret'] == -2) {
                    return self::errRet();
                }
                $orgHash[$intOrgId] = 1;//标识已经查过的公会

                $winnerHash += $res['ret'];
            }
        }
        if (empty($winnerHash)) {
            return self::errRet();
        }
        $startTime = time();
        $charmMap = [];
        $richMap = [];
        while (1) {
            $nowTime = time();
            if ($nowTime - $startTime >= 60) {
                break;//最多执行60秒
            }

            $arrOutput = Util_Redis::LPOP($arrInput);
            if (false === $arrOutput) {
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' Util_Redis::LPOP fail! ' . $strBufferKey);
                sleep(1);
                continue;
            }
            $strBufferData = $arrOutput[$strBufferKey];
            if (empty($strBufferData)) {
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' Util_Redis::LPOP empty! ' . $strBufferData);
                break;
            }
            // 出buffer记录日志
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' Util_Redis::LPOP success! ' . $strBufferData);
            $arrBufferData = unserialize($strBufferData);

            // 判断是否晋级
            $intCharmId = $arrBufferData['charm_id'];
            $intRichId  = $arrBufferData['rich_id'];
            $intScore   = intval($arrBufferData['score']);
            $intOrgId   = $arrBufferData['org_id'];
            // 给晋级的主播送的礼才是有效的
            if (isset($winnerHash[$intCharmId])) {
                $charmMap[$intCharmId] += $intScore;
                $richMap[$intRichId]   += $intScore;
            }
        }

        $intComponentIndex = $arrComponentConf['index'];
        $strRankKey = self::getRankKey($intActivityId, $intComponentIndex, self::COMPONENT_ID, self::SCORE_TYPE_ORG_CHARM, self::RANK_TYPE_DAY, $strSystemDate);
        foreach ($charmMap as $intBenefitUserId => $score) {
            $intOrgId = self::getUserOrgId($intBenefitUserId);
            // 更新公会日榜
            self::updateRankData($intActivityId, $intSystemTime, $strRankKey, $intOrgId, $score, 86400, $arrInput);

            // 更新公会主播日榜
            $orgCharmRankKey = self::getRankKey($intActivityId, $intComponentIndex, self::COMPONENT_ID, self::SCORE_TYPE_CHARM, self::RANK_TYPE_DAY, $strSystemDate, $intOrgId);
            self::updateRankData($intActivityId, $intSystemTime, $orgCharmRankKey, $intBenefitUserId, $score, 86400, $arrInput, $intOrgId);

            // 更新公会小时榜 公会主播小时榜 todo特殊小时送礼时间不能用系统时间 现阶段不做处理
        }


        return self::errRet(Tieba_Errcode::ERR_SUCCESS, ['charm_count' => count($charmMap), 'rich_count' => count($richMap)]);
    }
}
