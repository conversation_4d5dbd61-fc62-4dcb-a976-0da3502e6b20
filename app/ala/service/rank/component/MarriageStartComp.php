<?php
/**
 * 礼物大作战
 * <AUTHOR>
 * @time 2020/6/23
 */


class Service_Rank_Component_MarriageStartComp extends Service_Rank_Component_BaseComponent implements Service_Rank_Component_ActivityComponent {
    const COMPONENT_ID       = 10048;
    const COMPONENT_NAME     = 'MarriageStartComp';

    /**
     * 根据当前时间返回活动配置
     *
     * @param $arrInput
     *
     * @return array
     */
    public static function componentConfig($arrInput) {
        $arrActivityConf  = $arrInput['activity_conf'];
        $arrComponentConf = $arrInput['component_conf'];

        //全程静态挂件
        $staticUrl = $arrComponentConf['static_pic_url'];
        $arrActivityConf['pic_type'] = Libs_Util_ActivityRank::PIC_TYPE_STATIC;
        $arrActivityConf['pic_url'] = $staticUrl;

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrActivityConf);
    }

    /**
     * 进行送礼后的处理
     *
     * @param $arrInput
     *
     * @return array
     */
    public static function componentSendGift($arrInput) {
        $intActivityId     = $arrInput['activity_id'];
        $arrComponentConf  = $arrInput['component_conf'];
        $intComponentIndex = $arrComponentConf['index'];
        $intGiftId         = $arrInput['gift_id'];
        $intBenefitUserId  = $arrInput['to_user_id'];
        $intSendGiftTime   = $arrInput['send_gift_time'];
        $intPayUserId      = $arrInput['user_id'];
        $intScore          = intval($arrInput['gift_score'] / 100);
        $intGiftCount      = intval($arrInput['gift_count']);
        $strGiftName       = urldecode($arrInput['gift_name']);
        $intLiveId         = $arrInput['live_id'];
        // im的白名单
        $imSwitch = false;
        if (!empty($arrComponentConf['test_repay_user'])) {
            if (!in_array($intPayUserId, $arrComponentConf['test_repay_user'])) {
                $imSwitch = false;
            } else {
                $imSwitch = true;
            }
        } else {
            $imSwitch = true;
        }
        if (!$imSwitch) {
            return false;
        }

//        if (isset($arrComponentConf['im_gift'][$intGiftId])) {
//            $winnerRes = self::getCharmWinnerStatus($arrComponentConf['charm_activity_id'], $intBenefitUserId);
//            if ($winnerRes) {
//                $intScore = isset($arrComponentConf['im_gift'][$intGiftId]['score']) ? $arrComponentConf['im_gift'][$intGiftId]['score'] : $intScore;
//                $strGiftName = isset($arrComponentConf['im_gift'][$intGiftId]['gift_name']) ? $arrComponentConf['im_gift'][$intGiftId]['gift_name'] : $strGiftName;
//                $intScoreCount = $intGiftCount * $intScore;
//                $strImText = "送出" . $intGiftCount . "个" . $strGiftName . "为主播增加" . $intScoreCount . "荣耀值";
//                self::sendGiftBuffIm($intPayUserId, $intLiveId, $strImText);
//            }
//        }
        $strBindUserId = $intBenefitUserId . '#' . $intPayUserId;
        //如果为冲刺卡
        $arrTriggerGiftIds = array_column($arrComponentConf['sprint_card_gift'], 'gift_id');
        if (in_array($intGiftId, $arrTriggerGiftIds)) {
            //冲刺卡IM
            $winnerRes = self::getCharmWinnerStatus($arrComponentConf['charm_activity_id'], $intBenefitUserId);
            if ($winnerRes) {
                $strImText = $arrComponentConf['sprint_card_gift_rate'][$intGiftId]['winner_im_text'];
            }else {
                $strImText = $arrComponentConf['sprint_card_gift_rate'][$intGiftId]['im_text'];
            }
            self::sendGiftBuffIm($intPayUserId, $intLiveId, $strImText);
            // 计算分值加速
            $arrOutput = self::getSpeedupRateByGift($intActivityId, $strBindUserId, $intGiftId, $arrComponentConf['sprint_card_gift'], 1);
            if (Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                // 如果出错只打日志不退出 礼物分值按照不加速处理
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' getSpeedupRateByGift fail. input: ' . serialize($arrInput));
            }
        }
        $arrOutput = self::getSpeedupRate($intActivityId, $strBindUserId);
        if (Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' getSpeedupRate fail. input: ' . serialize([$intActivityId, $intBenefitUserId, $intGiftId]));
        }
        $speedRate = isset($arrOutput['ret']['rate']) ? floatval($arrOutput['ret']['rate']) : 1;
        $intScore = intval($intScore * $speedRate);
        if ($intScore <= 0) {
            return self::errRet();
        }
        //如果礼物id为不计算榜单消费的礼物则退出
        if (in_array($intGiftId, $arrComponentConf['no_activity_handle'])) {
            return self::errRet();
        }
        //用户贡献日榜
        $strSendGiftDay = date('Ymd', $intSendGiftTime);
        $richDayRankKey = self::getRankKey($intActivityId, $intComponentIndex, self::COMPONENT_ID, self::SCORE_TYPE_RICH, self::RANK_TYPE_DAY, $strSendGiftDay);
        self::updateRankData($intActivityId, $intSendGiftTime, $richDayRankKey, $intPayUserId, $intScore, 86400 * 15, $arrInput);
        return self::errRet();
    }

    public static function userConsumeSendPrizeRank($arrInput) {
        $intActivityId    = $arrInput['activity_id'];
        $intUserId        = $arrInput['user_id'];
        $arrComponentConf = $arrInput['component_conf'];
        $intSystemTime    = $arrInput['system_time'];
        $speedRate        = isset($arrInput['rate']) ? $arrInput['rate'] : 1;
        $intTdouAmount    = intval($arrInput['amount']);
        $intScore         = isset($arrInput['gift_score']) ? intval($arrInput['gift_score'] / 100) : 0;
        $intClientType    = isset($arrInput['client_type']) ? $arrInput['client_type'] : self::IOS_CLIENT_TYPE;
        $intSource        = $arrInput['source'];
        $intCharmUserId   = $arrInput['charm_user_id'];
        $intLiveId        = $arrInput['live_id'];
        $intC             = $arrInput['c'];
        if (empty($intSource) && !empty($intC)) {
            $intSource = Service_Task_Activity::c2source($intC);
        } else {
            $intC = Service_Task_Activity::source2c($intSource);
        }

        $intComponentIndex = $arrComponentConf['index'];
        if (empty($intScore)) {
            $intScore          = intval($intTdouAmount / 100);
        }

        if (!empty($arrComponentConf['test_repay_user'])) {
            if (!in_array($intUserId, $arrComponentConf['test_repay_user'])) {
                $imSwitch = false;
            } else {
                $imSwitch = true;
            }
        } else {
            $imSwitch = true;
        }
        if (!$imSwitch) {
            return self::errRet();
        }
        $intRankScore = intval($intScore * $speedRate);
        if ($intRankScore <= 0) {
            return self::errRet();
        }
        $strSendGiftDay = date('Ymd', $intSystemTime);
        $richDayRankKey = self::getRankKey($intActivityId, $intComponentIndex, self::COMPONENT_ID, self::SCORE_TYPE_RICH, self::RANK_TYPE_DAY, $strSendGiftDay);
        self::updateRankData($intActivityId, $intSystemTime, $richDayRankKey, $intUserId, $intRankScore, 86400 * 15, $arrInput);
        return true;
    }

    /**
     * 获取主播是否晋级
     * @param $charmActivityId
     * @param $intBenefitUserId
     *
     * @return bool
     */
    public static function getCharmWinnerStatus($charmActivityId, $intBenefitUserId) {
        $res = self::getCurrentWinnerList($charmActivityId, $intBenefitUserId);//todo
        if ($res['winner_status'] == self::ALL_JOIN) {//所有主播都参加
            return true;
        } elseif ($res['winner_status'] == self::SETTLED){//有晋级名单
            if (in_array($intBenefitUserId, $res['winner_list'])) {
                return true;
            }
        }
        return false;
    }

    /**
     * 下发 im
     * @param        $intUserId
     * @param        $intLiveId
     * @param        $strImText
     * @param array  $arrToUids
     * @param string $strJumpUrl
     *
     * @return bool
     */
    public static function sendGiftBuffIm($intUserId, $intLiveId, $strImText, $arrToUids = [], $strJumpUrl = '') {
        //获取直播间id
        $arrUserInfoOutput = Service_Rank_Component_BaseComponent::getUserInfo([$intUserId]);
        if (Tieba_Errcode::ERR_SUCCESS != $arrUserInfoOutput['errno']) {
            Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getUserInfoFailed. input:[' .
                serialize([$intUserId]) . '],output:[' . serialize($arrUserInfoOutput) . ']');
            return false;
        }

        //获取用户昵称
        $mapUserInfo = $arrUserInfoOutput['ret']['user_info'];
        $arrUserInfo = isset($mapUserInfo[$intUserId]) ? $mapUserInfo[$intUserId] : [];
        $strUserNickname = isset($arrUserInfo['user_nickname']) ? $arrUserInfo['user_nickname'] : '';

        $isJump = 0;
        $strJumpTitle = '';
        $strJumpBg = '';
        $strJumpTitleColor = '';
        if (!empty($strJumpUrl)) {
            $isJump = 1;
            $strJumpTitle = '点击查看';
            $strJumpBg = '#888888';
            $strJumpTitleColor = '#FFFFFF';
        }
        $arrExtra = array(
            'is_jump'          => $isJump, //1 可以点击 0 不可以点击
            'left_icon'        => urldecode(''), // 图片url 或者 '' 空字符串
            'start_bg_color'   => '#888888', //背景渐变色
            'end_bg_color'     => '#999999', //背景渐变色
            'alpha'            => '0.3', //透明度 0->1
            'text_array'       => array(
                '用户',
                $strUserNickname,
                $strImText,
            ),
            'text_color_array' => array(
                '#FFDC37',
                '#FFFFFF',
                '#FFDC37',
            ),
            'jump_item'        => array(
                'title'  => $strJumpTitle, //title
                'bg'     => $strJumpBg, //背景色
                'title_color' => $strJumpTitleColor, //标题颜色
                'url'    => urldecode($strJumpUrl),//跳转链接
                'screen' => 'half' // full 全屏 half 半屏
            ),
        );
        $arrImInput = [];
        $arrImInput['liveId'] = $intLiveId;
        $arrImInput['extra']  = $arrExtra;
        $arrImInput['subApp_type'] = 'quanmin';
        $arrImInput['to_uids'] = $arrToUids;
        $ret = Service_Rank_Viewer_ActivityIM::imCustomChannel($arrImInput);
        if ($ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . '::sendWinImFailed,input:[' .
                serialize($arrImInput) . '], output:[' . serialize($ret) . ']');
            return false;
        }
    }

    /**
     * FootBar
     * @param $arrInput
     * @return array
     */
    public static function componentArankFootBar($arrInput)
    {
        $arrExt           = $arrInput['ext'];
        $intFrom          = $arrInput['from'];
        $result           = $arrInput['result'];
        $intTrackId       = $arrExt['track_id'];
        $intCurUserId     = $arrInput['user_id'];
        $strRankKey       = $arrInput['rank_key'];
        $intActivityId    = $arrInput['activity_id'];
        $arrActivityConf  = $arrExt['activity_conf'];
        $arrComponentConf = $arrInput['component_conf'];
        $intUserTrackId   = 0;
        $footBar = [];
        if ($intTrackId != $intUserTrackId) {
            return self::errRet(Tieba_Errcode::ERR_SUCCESS, $footBar);
        }

        // 从榜单中查找底bar
        foreach ($result['list'] as $key => $value) {
            if ($intCurUserId == $value['raw_user_id']) {
                $footBar = $value;
                break;
            }
        }

        $footBar['track_id'] = $arrExt['track_id'];
        // 查询距离上一名差多少魅力值
        $arrWholeRank = isset($result['whole_rank']) ? $result['whole_rank'] : [];
        $lastGap  = 0;
        $intScore = null;
        $intRank  = -1;
        foreach ($arrWholeRank as $index => $value) {
            if ($intCurUserId == intval($value['member'])) {
                $intRank = $index + 1;
                $intScore = intval($value['score']);
                if($intRank > 1){
                    $lastGap = $arrWholeRank[$index - 1]['score'] - $intScore + 1;
                }
                break;
            }
        }

        // 单独查分数
        if (null === $intScore) {
            $intScore = self::getRankScore($strRankKey, $intCurUserId);
            if (false === $intScore) {
                Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' getRankScore fail. input: ' . serialize([$strRankKey, $intCurUserId]));
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }

        if($lastGap == 0){
            if($intScore <= 0 && count($arrWholeRank) < 100){
                $lastGap = 1;
            }else{
                $arrEndRank = end($arrWholeRank);
                $lastGap    = $arrEndRank['score'] - $intScore;
            }
        }
        $arrOutput = self::getUserInfo([$intCurUserId]);
        if (Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getUserInfo fail. input: ' . serialize([$intCurUserId]));
            return self::errRet($arrOutput['errno']);
        }

        $mapUserInfo = $arrOutput['ret']['user_info'];
        $arrUserInfo = isset($mapUserInfo[$intCurUserId]) ? $mapUserInfo[$intCurUserId] : [];
        $boolNeedRawUid = self::needRawUserId($intFrom);
        $isTopCharUserMysteriousMan = isset($arrUserInfo['is_mysterious_man']) && $arrUserInfo['is_mysterious_man'] == 1;
        $footBar['user_id']       = Alalib_Baidu_Account_Profile::getUkByUid($intCurUserId);
        $footBar['raw_user_id']   = $boolNeedRawUid ? strval($intCurUserId) : '';
        $footBar['rank']          = $intRank;
        $footBar['point']         = intval($intScore);
        $footBar['user_name']     = isset($arrUserInfo['user_name']) ? $arrUserInfo['user_name'] : '';
        $footBar['user_nickname'] = isset($arrUserInfo['user_nickname']) ? $arrUserInfo['user_nickname'] : '';
        $footBar['encrypted_portrait'] = Tieba_Ucrypt::encode($footBar['raw_user_id']);
        $footBar['avatar']        = isset($arrUserInfo['avatar']) ? $arrUserInfo['avatar'] : '';
        $footBar['bd_portrait']   = isset($arrUserInfo['bd_portrait']) ? $arrUserInfo['bd_portrait'] : '';
        $footBar['subapp_type']   = isset($arrUserInfo['subapp_type']) ? $arrUserInfo['subapp_type'] : '';
        $footBar['last_gap']      = intval($lastGap);
        $footBar['is_mysterious_man'] = $isTopCharUserMysteriousMan ? 1 : 0;
        if ($isTopCharUserMysteriousMan) {
            $footBar['user_nickname'] = Service_MysteriousMan_MysteriousMan::MYSTERIOUS_MAN_NICKNAME;
            $footBar['bd_portrait']   = Service_MysteriousMan_MysteriousMan::MYSTERIOUS_MAN_BD_PORTRAIT;
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $footBar);
    }

    /**
     * 返回输入榜单对应的上榜人数
     *
     * @param $arrInput
     *
     * @return mixed
     */
    public static function componentPlayerCount($arrInput) {
        $arrComponentConf = $arrInput['component_conf'];

        return $arrComponentConf['show_player_count'];
    }

    /**
     * 返回输入榜单对应的晋级淘汰的分界线
     *
     * @param $arrInput
     *
     * @return int
     */
    public static function componentWinnerBorder($arrInput) {
        // nothing to do
        return 0;
    }

    /**
     * 返回输入榜单对应的积分奖励分割线
     *
     * @param $arrInput
     *
     * @return int
     */
    public static function componentPointAwardBorder($arrInput) {
        // nothing to do
        return 0;
    }

    public static function componentGetGuardianInfo($arrInput) {
        $arrActivityConf   = $arrInput['activity_conf'];
        $arrComponentConf  = $arrInput['component_conf'];
        $intRankType       = $arrInput['rank_type'];
        $intScoreType      = $arrInput['score_type'];
        $strRankTimeTag    = $arrInput['rank_time_tag'];
        $intTrackId        = $arrInput['track_id'];

        return self::errRet();
    }

    /**
     * 返回输入榜单是否展示结算态
     *
     * @param $arrInput
     *
     * @return int
     */
    public static function componentShowSettle($arrInput) {
        // nothing to do
        return 0;
    }

    /**
     * 返回输入日期结束后的晋级名单
     *
     * @param $arrInput
     *
     * @return array
     */
    public static function componentWinnerList($arrInput) {
        return self::errRet();
    }

    /**
     * 手动执行的监控 根据输入打印相关数据
     *
     * @param $arrInput
     *
     * @return array
     */
    public static function componentManualMonitor($arrInput) {
        $intActivityId     = $arrInput['activity_id'];
        $intComponentIndex = $arrInput['component_index'];
        $arrActivityConf   = $arrInput['activity_conf'];
        $arrComponentConf  = $arrInput['component_conf'];
        $intSystemTime     = $arrInput['system_time'];
        $times             = $arrInput['key_num'];
        $keyType           = $arrInput['key_type'];
        $intErrorQueueLimit = isset($arrInput['error_limit']) ? $arrInput['error_limit'] :100;
        $input = [];

        // 错误队列
        $strErrorQueueKey = self::getErrorQueueKey($intActivityId, $intSystemTime);
        $input['error_queue'] = [
            'key'   => $strErrorQueueKey,
            'limit' => $intErrorQueueLimit
        ];
        $res = self::getManualMonitorData($input);
        if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            return self::errRet($res['errno'], $res['ret']);
        }
        $result = $res['ret'];
        $prizeConf = $arrComponentConf['prize_conf'];
        $odds = 0;
        foreach ($arrComponentConf['prize_conf'][$keyType]['high_probability_one'] as $item){
            $odds += $item['prize_odds'];
        }
        $prizeHighProbabilityOneList   = $prizeConf[$keyType]['high_probability_one'];//$times=100
        $prizeHighProbabilityTwoList   = $prizeConf[$keyType]['high_probability_two'];//$times 10-99
        $prizeHighProbabilityThreeList = $prizeConf[$keyType]['high_probability_three'];//$times 1-9
        $prizeLowProbabilityList       = $prizeConf[$keyType]['low_probability'];//低概率
        $isLowProbability = false;
        if ($arrComponentConf['open_high_probability'] == 1) {//手动调整配置概率模式
            $isLowProbability = false;
        } elseif ($arrComponentConf['open_low_probability'] == 1) {//手动调整配置概率模式
            $isLowProbability = true;
        } elseif ($intSystemTime > $arrComponentConf['open_high_probability_time']) {//活动最后一天22：00开启高利润版本,可调。
            $isLowProbability = false;
        } else {//脚本自动调整配置概率模式
            $costNoticeKey = Service_Rank_Component_MassOrgComp::getCostNoticeKey($intActivityId, $arrComponentConf['index']);
            $arrInput = array(
                'redis'   => Util_Redis::REDIS_NAME_ALA_NEW,
                'key'     => $costNoticeKey,
            );
            $arrRet = Util_Redis::getFromRedis($arrInput);
            if (!empty($arrRet)) {//已经添加报警
                $isLowProbability = true;
            }
        }

        if ($isLowProbability) {//低概率
            $prizeList = $prizeLowProbabilityList;
        } else {
            if ($times < 10) {
                $prizeList = $prizeHighProbabilityThreeList;
            } elseif($times == 100) {
                $prizeList = $prizeHighProbabilityOneList;
            } else {
                $prizeList = $prizeHighProbabilityTwoList;
            }
        }
        $result['current_probability'] = $prizeList;
        $result['is_low_probability']  = $isLowProbability;
        $highCostKey = Service_Rank_Component_MassOrgComp::getHighCostKey($intActivityId);
        $strHighCostKey1 = $highCostKey."_1";
        $strHighCostKey2 = $highCostKey."_2";

        $arrInput = array(
            'redis'   => Util_Redis::REDIS_NAME_ALA_NEW,
            'key'     => $highCostKey,
        );
        $arrRet = Util_Redis::getFromRedis($arrInput);

        $arrInput1 = array(
            'redis'   => Util_Redis::REDIS_NAME_ALA_NEW,
            'key'     => $strHighCostKey1,
        );
        $arrRet1 = Util_Redis::getFromRedis($arrInput1);

        $arrInput2 = array(
            'redis'   => Util_Redis::REDIS_NAME_ALA_NEW,
            'key'     => $strHighCostKey2,
        );
        $arrRet2 = Util_Redis::getFromRedis($arrInput2);

        $isHighCost  = false;
        $isHighCost1 = false;
        $isHighCost2 = false;
        if (!empty($arrRet)) {//达到最大成本
            $isHighCost = true;
        }
        if (!empty($arrRet1)) {//达到最大成本
            $isHighCost1 = true;
        }
        if (!empty($arrRet2)) {//达到最大成本
            $isHighCost2 = true;
        }
        $result['is_high_cost']  = $isHighCost;
        $result['is_high_cost1']  = $isHighCost1;
        $result['is_high_cost2']  = $isHighCost2;
        $receiveBigPrizeKey = Service_Rank_Component_MassOrgComp::getReceiveBigPrizeKey($intActivityId);
        $arrInput = array(
            'redis'   => Util_Redis::REDIS_NAME_ALA_NEW,
            'key'     => $receiveBigPrizeKey. '_5',
        );
        $bigPrizeExpire  = Util_Redis::kTtl($arrInput);//半小时内只能领取一次大奖
        $result['big_prize_expire1']  = $bigPrizeExpire;
        $arrInput = array(
            'redis'   => Util_Redis::REDIS_NAME_ALA_NEW,
            'key'     => $receiveBigPrizeKey. '_4',
        );
        $bigPrizeExpire  = Util_Redis::kTtl($arrInput);//半小时内只能领取一次大奖
        $result['big_prize_expire2']  = $bigPrizeExpire;
        //当前成本
        $arrServiceInput = array(
            'activity_id' => $intActivityId,
            'scene'       => Service_Rank_Viewer_ChestActivity::OPEN_CHEST_SCENE,
        );
        $arrOutput = Dl_Ala_MysteriousChestActivity::getChestHandleSumBySceneAll($arrServiceInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            return self::errRet($arrOutput['errno'], $arrOutput['ret']);
        }
//        $intKeyNum = $arrOutput['data'][0]['sum'];
//
//        $intKeyTotalValue = $intKeyNum * $arrComponentConf['key_price']/1000;//换算成元
        $arrKeyData       = $arrOutput['data'];
        $intKeyTotalValue = 0;
        $arrKeyTotalVal   = array();
        foreach ($arrKeyData as $arrKeyDatum) {
            $intNum           = $arrKeyDatum['num'];
            $intKtype         = $arrKeyDatum['key_type'];
            $intKPrice        = $arrComponentConf['key_price'][$intKtype] / 1000;
            $arrKeyTotalVal[$intKtype] += $intNum * $intKPrice;
            $intKeyTotalValue += $intNum * $intKPrice;
        }
        $arrServiceInput = array(
            'activity_id' => $intActivityId,
            'status'      => Service_Rank_Viewer_ChestActivity::ACTIVITY_CHEST_USER_REWARD_STATUS_SUCCESS,
            'prize_ids'   => $arrComponentConf['prize_conf']['have_cost_prize_ids'],//有价值的礼物
        );
        $arrOutput = Dl_Ala_MysteriousChestActivity::getChestRewardRecordPrizeNumByPrizeIds($arrServiceInput);

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            return self::errRet($arrOutput['errno'], $arrOutput['ret']);
        }
        $arrWinPrizeList = $arrOutput['data'];
        $arrPrizeList = array();
        foreach ($arrComponentConf['prize_conf']['show_prize_list'] as $item){
            $arrPrizeList[$item['prize_id']] = $item;
        }
        $intWinPrizeValue = 0;
        foreach ($arrWinPrizeList as $prizeSum) {
            if (isset($arrPrizeList[$prizeSum['prize_id']])) {
                $intWinPrizeValue += $arrPrizeList[$prizeSum['prize_id']]['prize_price'] * $prizeSum['sum'];
            }
        }
        //计算成本
        $profit = $intKeyTotalValue - $intWinPrizeValue;
        $costProportion = $intWinPrizeValue / $intKeyTotalValue;
        $result['key_total_value']  = $intKeyTotalValue;
        $result['key1_total_value']  = $arrKeyTotalVal[1];
        $result['key2_total_value']  = $arrKeyTotalVal[2];
        $result['win_prize_value']  = $intWinPrizeValue;
        $result['profit']  = $profit;
        $result['cost_proportion']  = $costProportion;
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $result);
    }

    /**
     * @param $componentInput
     * @param $arrInput
     *
     * @return mixed
     */
    public static function componentAfterHandleRank($componentInput, $arrInput) {


        return $arrInput;
    }

    /**
     * 自动执行的监控 校验数据出错时报警
     *
     * @param $arrInput
     *
     * @return array
     */
    public static function componentMonitor($arrInput) {
        // nothing to do
        return self::errRet();
    }

    /**
     *
     * 定时任务脚本调用到此方法时需要执行的事项
     *
     * @param $arrInput
     *
     * @return array
     */
    public static function componentTask($arrInput) {

        $arrActivityConf = $arrInput['activity_conf'];
        $intActivityId   = $arrActivityConf['activity_id'];
        $intSystemTime   = $arrInput['system_time'];
        $arrComponentConf = $arrInput['component_conf'];
        $intComponentIndex = $arrComponentConf['index'];
        $intComponentId    = $arrComponentConf['component_id'];
        $strCurDate      = date('Ymd', $intSystemTime);
        $intLastDayTime    = intval($intSystemTime) - 86400;
        $strLastDay        = date("Ymd",$intLastDayTime);
        $arrDayAwardList = $arrComponentConf['day_rank_award'];
        $intLimit        = count($arrDayAwardList);

        $strCurDateStartTime = strtotime($strCurDate . '000000');
        if ($intSystemTime > $strCurDateStartTime + 60) {
            $richDayRankKey = self::getRankKey($intActivityId, $intComponentIndex, self::COMPONENT_ID, self::SCORE_TYPE_RICH, self::RANK_TYPE_DAY, $strLastDay);
            $arrOutput = self::getTopRank($intLimit, $richDayRankKey);
            if (Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getTopRank failed. key: ' . $richDayRankKey .' count:'. $intLimit);
                self::fatal('handleDayBenefitPoint_getTopRankFail',time(),$intActivityId,['key' => $richDayRankKey, 'limit' => $intLimit]);
                return self::errRet();
            }
            $arrTopUsers = $arrOutput['ret'];
            $arrPrizeList     = is_array($arrComponentConf['prize_conf']['show_prize_list']) ? $arrComponentConf['prize_conf']['show_prize_list'] : [];
            foreach ($arrTopUsers as $index => $user){
                $intUserId = intval($user['member']);
                $intScore  = intval($user['score']);
                $arrCurAwardList = $arrDayAwardList[$index+1];
                self::dayRankReward($arrCurAwardList, $intUserId, $intActivityId, $arrPrizeList, $intSystemTime, $intScore);
            }
        }

        return self::errRet();
    }

    /**
     * 发送日榜奖励
     * @param $arrPrizeInfos
     * @param $intUserId
     * @param $intActivityId
     * @param $arrPrizeList
     * @param $intSystemTime
     * @param $intScore
     *
     * @return array
     */
    public static function dayRankReward($arrPrizeInfos, $intUserId, $intActivityId, $arrPrizeList, $intSystemTime, $intScore) {
        if (empty($arrPrizeInfos)){
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrPrizeInfos) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR, [], 'param error');
        }
        $curDate = date('Ymd', $intSystemTime);
        $strSubappType  = Alalib_Conf_Sdk::SUBAPP_QUANMIN;
        $intClientType  = Molib_Client_Define::CLIENT_TYPE_ANDROID;
        $intChannelId   = Libs_Util_ActivitySendPrize::FROM_ACTIVITY_AUTUMN;
//        $intChannel         = Libs_Util_ActivitySendPrize::FROM_ACTIVITY_USER;
        $strService     = Service_Activity_Activity::REPAY_KNAPSACK_SERVICE;
        $strPlatform    = Service_Activity_Activity::PLATFORM_SDK;
        foreach ($arrPrizeInfos as $arrPrizeInfo) {
            $intPrizeId = $arrPrizeInfo['prize_id'];
            $arrConfPrize = $arrPrizeList[$intPrizeId - 1];
            $arrGiftInfo = array();
            $arrDlInput = array(
                'activity_id' => $intActivityId,
                'user_id'     => $intUserId,
                'time_tag'    => $curDate,
                'prize_id'    => $intPrizeId,
            );
            $arrDlOutput = Dl_Ala_Activity::getRepayRewardByUserIdAndPrizeId($arrDlInput);
            if(false === $arrDlOutput || Tieba_Errcode::ERR_SUCCESS !== $arrDlOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Activity::getRepayRewardByUserId. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrDlOutput) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL);
            }

            $arrUserRewardList = $arrDlOutput['data'];
            if (!empty($arrUserRewardList)) {
                continue;
            }
            if($arrConfPrize['prize_type'] == Libs_Util_ActivitySendPrize::PRIZE_TYPE_KNAPSACK){
                //过期的礼物不处理
                if (isset($arrConfPrize['effect_expire']) && $intSystemTime > $arrConfPrize['effect_expire']) {
                    continue;
                }
                //保存订单id
                $seqId = md5(time() . mt_rand(1,1000000));
                $arrDbSendParam = array(
                    'subapp_type'     => $strSubappType,
                    'client_type'     => $intClientType,
                    'prize_num'       => $arrPrizeInfo['prize_num'],
                    'prize_effect_id' => $arrConfPrize['prize_effect_id'],
                    'send_order_id'   => $seqId,
                );
                $arrGiftInfo[] = array(
                    'user_id'           => $intUserId,
                    'item_id'           => $arrConfPrize['prize_effect_id'],
                    'amount'            => $arrPrizeInfo['prize_num'],
                    'outer_order_id'    => $seqId,
                );
            } else {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " send gift error. sendconf:[" . serialize($arrPrizeInfo) . "]";
                Bingo_Log::warning($strLog);
                return self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL, [], 'sendconf error');
            }
            $arrDlInput = array(
                'activity_id' => $intActivityId,
                'user_id'     => $intUserId,
                'time_tag'    => $curDate,
                "prize_id"    => $intPrizeId,
                "status"      => Service_Activity_Activity::ACTIVITY_REPAY_USER_REWARD_STATUS_NOT_SEND,
                "money"       => $intScore,
                "create_time" => $intSystemTime,
                "update_time" => $intSystemTime,
                "send_param"  => json_encode($arrDbSendParam),
            );
            $arrDlOutput = Dl_Ala_Activity::addRepayRewardRecord($arrDlInput);
            if(false === $arrDlOutput || Tieba_Errcode::ERR_SUCCESS !== $arrDlOutput['errno']) {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Activity::addRepayRewardRecord. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrDlOutput) . "]";
                Service_Rank_Component_BaseComponent::sendWarning($strLog,'day_rank_addRepayRewardRecord_error','15010333182',array('<EMAIL>'));
                return self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL, [], 'Dl error');
            }
            // 5.发放奖励
            // 发放奖励的失败都放在脚本中去补，所以这里后续都返回发放成功

            if($arrConfPrize['prize_type'] == Libs_Util_ActivitySendPrize::PRIZE_TYPE_KNAPSACK && !empty($arrGiftInfo)){
                $bolSend = Libs_Util_ActivityRank::sendGiftToKnapsack($intUserId,$arrGiftInfo,$intChannelId,$strService,$strPlatform,$strSubappType,$intClientType);
            }else {
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " send gift error. sendconf:[" . serialize($arrGiftInfo) . "]";
                Bingo_Log::warning($strLog);
                Service_Rank_Component_BaseComponent::sendWarning($strLog,'day_rank_send gift error','15010333182',array('<EMAIL>'));
                return self::errRet(Alalib_Conf_Error::ERR_CALL_DL_FAIL,  [], 'sendconf error');
            }
            $intSendStatus  = Service_Activity_Activity::ACTIVITY_REPAY_USER_REWARD_STATUS_SEND_DONE;

            if(!$bolSend){
                $intSendStatus = Service_Activity_Activity::ACTIVITY_REPAY_USER_REWARD_STATUS_NOT_SEND;
            }
            if($intSendStatus == Service_Activity_Activity::ACTIVITY_REPAY_USER_REWARD_STATUS_SEND_DONE){
                $arrDlInput = array(
                    'user_id'     => $intUserId,
                    "prize_id"    => $intPrizeId,
                    "activity_id" => $intActivityId,
                    "time_tag"    => $curDate,
                    "status"      => $intSendStatus,
                    "update_time" => $intSystemTime,
                );
                $arrDlOutput = Dl_Ala_Activity::updateRepayRewardStatus($arrDlInput);
                if(false === $arrDlOutput || Tieba_Errcode::ERR_SUCCESS !== $arrDlOutput['errno']) {
                    // 重试一次
                    $arrDlOutput = Dl_Ala_Activity::updateRepayRewardStatus($arrDlInput);
                    if(false === $arrDlOutput || Tieba_Errcode::ERR_SUCCESS !== $arrDlOutput['errno']) {
                        $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Activity::updateRepayRewardStatus. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrDlOutput) . "]";
                        Bingo_Log::warning($strLog);
                        Service_Rank_Component_BaseComponent::sendWarning($strLog,'day_rank_repay_user_update_status_error','15010333182',array('<EMAIL>'));
                    }
                }
            }
        }

    }

    /**
     * 按照概率抽奖
     * @param $prizeConf
     * @param $times
     * @param $intActivityId
     * @param $activeComponentConf
     * @param $intCurrTime
     *
     * @return array
     */
    public static function lotteryDraw($prizeConf, $times, $intActivityId, $activeComponentConf, $intCurrTime){

        $prizeHighProbabilityOneList   = $prizeConf['high_probability_one'];//$times=100
        $prizeHighProbabilityTwoList   = $prizeConf['high_probability_two'];//$times 10-99
        $prizeHighProbabilityThreeList = $prizeConf['high_probability_three'];//$times 1-9
        $prizeLowProbabilityList       = $prizeConf['low_probability'];//低概率
        $prizeHundredMustList          = $prizeConf['hundred_must'];//必中奖品
        $isLowProbability = false;
        if ($activeComponentConf['open_high_probability'] == 1) {//手动调整配置概率模式
            $isLowProbability = false;
        } elseif ($activeComponentConf['open_low_probability'] == 1) {//手动调整配置概率模式
            $isLowProbability = true;
        } elseif ($intCurrTime > $activeComponentConf['open_high_probability_time']) {//活动最后一天22：00开启高利润版本,可调。
            $isLowProbability = false;
        } else {//脚本自动调整配置概率模式
            $costNoticeKey = Service_Rank_Component_MassOrgComp::getCostNoticeKey($intActivityId, $activeComponentConf['index']);
            $arrInput = array(
                'redis'   => Util_Redis::REDIS_NAME_ALA_NEW,
                'key'     => $costNoticeKey,
            );
            $arrRet = Util_Redis::getFromRedis($arrInput);
            if (!empty($arrRet)) {//已经添加报警
                $isLowProbability = true;
            }
        }

        if ($isLowProbability) {//低概率
            $prizeList = $prizeLowProbabilityList;
        } else {
            if ($times < 10) {
                $prizeList = $prizeHighProbabilityThreeList;
            } elseif($times == 100) {
                $prizeList = $prizeHighProbabilityOneList;
            } else {
                $prizeList = $prizeHighProbabilityTwoList;
            }
        }

        $result     = array();
        $prizeIndex = 0;
        for ($i = 0; $i < $times; $i++) {
            //100抽必须出6种奖品
            if ($i > 93 && $times == 100) {
                if (!empty($prizeHundredMustList)) {
                    $prizeId = array_pop($prizeHundredMustList);
                    $result[]['prize_id'] = $prizeId;
                    continue;
                }
            }
            $intCountOdds   = 0;
            $randNum        = rand(1,10000);
            foreach($prizeList as $prize){
                $intCountOdds += $prize['prize_odds'];
                $prizeIndex++;
                if($randNum <= $intCountOdds*10000){
                    $result[] = $prize;
                    $search = array_search($prize['prize_id'], $prizeHundredMustList);
                    if ($search !== false) {//发现有必中奖品则删除必中奖品列表
                        unset($prizeHundredMustList[$search]);
                    }
                    break;
                }
            }
        }
        return $result;
    }

    /**
     * 礼物奖品格式化
     * @param $prizeInfos
     * @param $arrCurrPrize
     * @param $carEffectMaxExpire
     * @param $medalMaxExpire
     * @param $prizeConf
     * @param $times
     * @param $intActivityId
     * @param $activeComponentConf
     *
     * @return array
     */
    public static function prizeInfosFormat($prizeInfos, $arrCurrPrize, $carEffectMaxExpire, $medalMaxExpire, $prizeConf, $times, $intActivityId,$activeComponentConf, $intKeyType){
        $prizeInfosFormat = array();
        $sumPrice         = 0;
        $typeResult       = array();
        $prizeIds         = array();
        $haveBigPrize = false;
        $bigPrizeInfo = $prizeConf['big_prize'];//大奖
        $freePrizeInfo = $prizeConf['free_prize'];//免费的
        //百抽判断是否能中5大奖 1 用户只能中一次 2 收益超出720才发放 3 半小时只能领一次  同时满足则中

        if ($times == 100) {
            $highCostKey = Service_Rank_Component_MassOrgComp::getHighCostKey($intActivityId);
            if ($intKeyType>0) {
                $highCostKey = $highCostKey."_" . $intKeyType;
            }
            $arrInput = array(
                'redis'   => Util_Redis::REDIS_NAME_ALA_NEW,
                'key'     => $highCostKey,
            );
            $arrRet = Util_Redis::getFromRedis($arrInput);
            if (!empty($arrRet)) {//达到最大成本
                $receiveBigPrizeKey = Service_Rank_Component_MassOrgComp::getReceiveBigPrizeKey($intActivityId);
                $receiveBigPrizeKey = $receiveBigPrizeKey . '_' . $bigPrizeInfo['prize_id'];
                $arrInput = array(
                    'redis'   => Util_Redis::REDIS_NAME_ALA_NEW,
                    'key'     => $receiveBigPrizeKey,
                    'value'   => time(),
                    'expire'  => isset($bigPrizeInfo['setnx_expire']) ? $bigPrizeInfo['setnx_expire'] : 1800,
                );
                $noReceiveBigPrize  = Service_Rank_Component_MassOrgComp::setNxToRedis($arrInput);//半小时内只能领取一次大奖
                if ($noReceiveBigPrize) {
                    $haveBigPrize = true;
                    $typeResult[$bigPrizeInfo['prize_type']][$bigPrizeInfo['prize_effect_id']] = $bigPrizeInfo;
                    $typeResult[$bigPrizeInfo['prize_type']][$bigPrizeInfo['prize_effect_id']]['enter_effect_expire'] = $bigPrizeInfo['prize_expire'];
                }
            }
        }

        foreach ($prizeInfos as $prizeId) {
            if ($haveBigPrize && in_array($prizeId['prize_id'], $freePrizeInfo)) {//已经获取大奖 跳过一次免费
                $haveBigPrize = false;
                continue;
            }
            $prizeInfo = $prizeConf['show_prize_list'][$prizeId['prize_id'] - 1];
            if (isset($typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']])) {
                if ($prizeInfo['prize_type'] == Service_Rank_Viewer_ChestActivity::PRIZE_TYPE_KNAPSACK) {
                    $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['prize_num'] += $prizeInfo['prize_num'];
                } elseif ($prizeInfo['prize_type'] == Service_Rank_Viewer_ChestActivity::PRIZE_TYPE_EFFECT) {
                    //判断是否超出最大有效期 超出则用兜底礼品
                    //将要添加的有效期
                    $willExpire = $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['enter_effect_expire'] + $prizeInfo['prize_expire'];
                    if ($arrCurrPrize['award_' . $prizeInfo['award_no']] + $willExpire > $carEffectMaxExpire) {
                        $replacePrize = self::getReplacePrize($prizeConf);
                        $prizeInfo    = $replacePrize;
                        if (!isset($typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']])) {
                            $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']] = $prizeInfo;
                            $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['enter_effect_expire'] = $prizeInfo['prize_expire'];
                        } else {
                            $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['prize_num'] += $prizeInfo['prize_num'];
                            $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['prize_value'] += $prizeInfo['prize_value'];
                        }
                    } else {
                        $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['enter_effect_expire'] += $prizeInfo['prize_expire'];
                        $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['prize_num']           += $prizeInfo['prize_num'];
                    }
                } elseif ($prizeInfo['prize_type'] == Service_Rank_Viewer_ChestActivity::PRIZE_TYPE_MEDAL) {
                    //判断是否超出最大有效期 超出则用兜底礼品
                    //将要添加的有效期
                    $willExpire = $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['enter_effect_expire'] + $prizeInfo['prize_expire'];
                    if ($arrCurrPrize['award_' . $prizeInfo['award_no']] + $willExpire > $medalMaxExpire) {
                        $replacePrize = self::getReplacePrize($prizeConf);
                        $prizeInfo    = $replacePrize;
                        if (!isset($typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']])) {
                            $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']] = $prizeInfo;
                            $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['enter_effect_expire'] = $prizeInfo['prize_expire'];
                        } else {
                            $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['prize_num'] += $prizeInfo['prize_num'];
                            $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['prize_value'] += $prizeInfo['prize_value'];
                        }
                    } else {
                        $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['enter_effect_expire'] += $prizeInfo['prize_expire'];
                        $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['prize_num'] += $prizeInfo['prize_num'];
                    }
                } elseif ($prizeInfo['prize_type'] == Service_Rank_Viewer_ChestActivity::PRIZE_TYPE_LEVEL_EXP) {
                    $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['prize_num'] += $prizeInfo['prize_num'];
                    $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['prize_value'] += $prizeInfo['prize_value'];
                } elseif ($prizeInfo['prize_type'] == Libs_Util_ActivitySendPrize::PRIZE_TYPE_FRAGMENT) {
                    $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['prize_num'] += $prizeInfo['prize_num'];
                } elseif ($prizeInfo['prize_type'] == Libs_Util_ActivitySendPrize::PRIZE_TYPE_VOTE) {
                    $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['prize_num'] += $prizeInfo['prize_num'];
                }
            } else {
                if ($prizeInfo['prize_type'] == Service_Rank_Viewer_ChestActivity::PRIZE_TYPE_EFFECT) {
                    //判断是否超出最大有效期 超出则用兜底礼品
                    if ($arrCurrPrize['award_' . $prizeInfo['award_no']] + $prizeInfo['prize_expire'] > $carEffectMaxExpire) {
                        $replacePrize = self::getReplacePrize($prizeConf);
                        $prizeInfo    = $replacePrize;
                        if (!isset($typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']])) {
                            $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']] = $prizeInfo;
                            $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['enter_effect_expire'] = $prizeInfo['prize_expire'];
                        } else {
                            $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['prize_num'] += $prizeInfo['prize_num'];
                            $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['prize_value'] += $prizeInfo['prize_value'];
                        }
                        continue;
                    }
                    $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['enter_effect_expire'] = $prizeInfo['prize_expire'];
                } elseif ($prizeInfo['prize_type'] == Service_Rank_Viewer_ChestActivity::PRIZE_TYPE_MEDAL) {
                    //判断是否超出最大有效期 超出则用兜底礼品
                    if ($arrCurrPrize['award_' . $prizeInfo['award_no']] + $prizeInfo['prize_expire'] > $medalMaxExpire) {
                        $replacePrize = self::getReplacePrize($prizeConf);
                        $prizeInfo    = $replacePrize;
                        if (!isset($typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']])) {
                            $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']] = $prizeInfo;
                            $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['enter_effect_expire'] = $prizeInfo['prize_expire'];
                        } else {
                            $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['prize_num'] += $prizeInfo['prize_num'];
                            $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['prize_value'] += $prizeInfo['prize_value'];
                        }
                        continue;
                    }
                }
                $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']] = $prizeInfo;
                $typeResult[$prizeInfo['prize_type']][$prizeInfo['prize_effect_id']]['enter_effect_expire'] = $prizeInfo['prize_expire'];
            }
            //测试用
            if (isset($prizeIds[$prizeInfo['prize_id']])) {
                $prizeIds[$prizeInfo['prize_id']]++;
            } else {
                $prizeIds[$prizeInfo['prize_id']] = 1;
            }
        }
        foreach ($typeResult as $prizeType => $item) {
            foreach ($item as $intPrizeEffectId => $value) {
                $prizeInfosFormat[] = $value;
                $sumPrice += $value['prize_show_price'] * $value['prize_num'];
                $prizeSortIds[] = $value['prize_id'];
            }
        }
        //测试用
        ksort($prizeIds);
        //按照prize_id排序
        array_multisort($prizeSortIds, SORT_DESC, $prizeInfosFormat);
        return array($prizeInfosFormat, $sumPrice);
    }

    /**
     * 获取随机保底奖品
     * @param $prizeConf
     *
     * @return mixed
     */
    public static function getReplacePrize($prizeConf){
        $replaceCount = count($prizeConf['replace_prize_list']);
        $rand = mt_rand(0, $replaceCount - 1);
        return $prizeConf['replace_prize_list'][$rand];
    }

    /**
     * 批量发送礼品
     * @param $prizeInfos
     * @param $intActivityId
     * @param $intUserId
     * @param $arrCurrPrize
     * @param $intCurrTime
     * @param $openChestOrderId
     * @param $intClientType
     * @param $source
     * @param $intCharmUserId
     * @param $intLiveId
     *
     * @return array|bool
     */
    public static function sendRewardMulti($prizeInfos, $intActivityId, $intUserId, $arrCurrPrize, $intCurrTime, $openChestOrderId, $intClientType, $source, $intCharmUserId, $intLiveId, $intKeyType) {

        $updateChestInput = array();
        $rewrdList        = array();
        $arrSendInput     = array();
        $arrSendOrderId   = array();
        $arrSendParams    = array();
        $strService         = Service_Rank_Viewer_ChestActivity::CHEST_KNAPSACK_SERVICE;
        $intChannel         = Service_Rank_Viewer_ChestActivity::FROM_ACTIVITY_TEST;
        $strPlatform        = Service_Rank_Viewer_ChestActivity::PLATFORM_SDK;
        $strSubappType      = Service_Rank_Viewer_ChestActivity::getSubappType($source);
        Dl_Ala_MysteriousChestActivity::startTransaction();
        foreach ($prizeInfos as $prizeInfo) {
//            var_dump($prizeInfo);
            $rewrdList[] = ['prize_id' => $prizeInfo['prize_id'], 'num' => $prizeInfo['prize_num']];
            $intPrizeId = $prizeInfo['prize_id'];
            $status = Service_Rank_Viewer_ChestActivity::ACTIVITY_CHEST_USER_REWARD_STATUS_NOT_SEND;
            $arrDbSendParam = array();
            //保存订单id
            $seqId = md5(time() . mt_rand(1,1000000));
            $arrSendOrderId[$prizeInfo['prize_type']][] = $seqId;
            if($prizeInfo['prize_type'] == Service_Rank_Viewer_ChestActivity::PRIZE_TYPE_KNAPSACK){
                $updateChestInput['award_' . $prizeInfo['award_no']] = $prizeInfo['prize_num'];
                $intRewardEndTime = $intCurrTime + $prizeInfo['enter_effect_expire'];
                $arrDbSendParam = array(
                    'subapp_type'     => $strSubappType,
                    'client_type'     => $intClientType,
                    'begin_time'      => $intCurrTime,
                    'end_time'        => $intRewardEndTime,
                    'prize_effect_id' => $prizeInfo['prize_effect_id'],
                    'send_order_id'   => $seqId,
                );
                $arrSendInput[$prizeInfo['prize_type']][] = array(
                    'user_id'           => $intUserId,
                    'item_id'           => $prizeInfo['prize_effect_id'],
                    'amount'            => $prizeInfo['prize_num'],
                    'outer_order_id'    => $seqId,
                );
                //需要广播
                if(isset($prizeInfo['is_punish_gift']) && $prizeInfo['is_punish_gift'] == 1) {
                    $broadType = Libs_Util_BroadCast::BROADCAST_TYPE_ACTIVITY_PRIZE_NO_JUMP;
                    //当有主播和直播间id则适用跳转到直播间广播
                    if (!empty($intCharmUserId) && !empty($intLiveId)) {
                        $broadType = Libs_Util_BroadCast::BROADCAST_TYPE_ACTIVITY_PRIZE_JUMP;
                    }
                    $keywords = $prizeInfo['key_text'][$intKeyType];
                    $arrSendParams[Libs_Util_ActivitySendPrize::SEND_TYPE_PUSH_BROAD_CAST . $prizeInfo['prize_effect_id']] = Libs_Util_ActivitySendPrize::getPunishGiftBroadCastParams($intUserId, $broadType, $strSubappType, $intCharmUserId, $intLiveId,  $prizeInfo['prize_effect_id'], $prizeInfo['prize_name'], $keywords);
                }
            }else if($prizeInfo['prize_type'] == Service_Rank_Viewer_ChestActivity::PRIZE_TYPE_EFFECT){ // 发放入场动效
                $intEffectId = $prizeInfo['prize_effect_id'];
                $intCurrExistTime = !empty($arrCurrPrize['award_' . $prizeInfo['award_no']]) ? $arrCurrPrize['award_' . $prizeInfo['award_no']] : $intCurrTime;
                if ($intCurrExistTime < $intCurrTime) {
                    $intCurrExistTime = $intCurrTime;
                }
                $intRewardEndTime = $intCurrExistTime + $prizeInfo['enter_effect_expire'];
                $intRewardBeginTime = $intCurrTime;
                $updateChestInput['award_' . $prizeInfo['award_no']] = $prizeInfo['enter_effect_expire'];
                $arrDbSendParam = array(
                    'prize_effect_id' => $intEffectId,
                    'begin_time'      => $intRewardBeginTime,
                    'end_time'        => $intRewardEndTime,
                );
                $arrSendInput[$prizeInfo['prize_type']][] = array(
                    "user_id"         => $intUserId,
                    "enter_effect_id" => $intEffectId,
                    "effective_time"  => $intRewardBeginTime,
                    "invalid_time"    => $intRewardEndTime,
                );
            } else if($prizeInfo['prize_type'] == Service_Rank_Viewer_ChestActivity::PRIZE_TYPE_MEDAL){
                $intCurrExistTime = !empty($arrCurrPrize['award_' . $prizeInfo['award_no']]) ? $arrCurrPrize['award_' . $prizeInfo['award_no']] : $intCurrTime;
                if ($intCurrExistTime < $intCurrTime) {
                    $intCurrExistTime = $intCurrTime;
                }
                $updateChestInput['award_' . $prizeInfo['award_no']] = $prizeInfo['enter_effect_expire'];
                $intRewardEndTime = $intCurrExistTime + $prizeInfo['enter_effect_expire'];
                $intRewardBeginTime = $intCurrTime;
                $arrDbSendParam = array(
                    'prize_effect_id' => $prizeInfo['prize_effect_id'],
                    'begin_time'      => $intRewardBeginTime,
                    'end_time'        => $intRewardEndTime,
                );
                $arrSendInput[$prizeInfo['prize_type']] = array(
                    "user_id"         => $intUserId,
                    "enter_effect_id" => $prizeInfo['prize_effect_id'],
                    "effective_time"  => $intRewardBeginTime,
                    "invalid_time"    => $intRewardEndTime,
                );
            } else if($prizeInfo['prize_type'] == Service_Rank_Viewer_ChestActivity::PRIZE_TYPE_LEVEL_EXP){
                $updateChestInput['award_' . $prizeInfo['award_no']] = $prizeInfo['prize_value'];
                $arrDbSendParam = array(
                    'step' => $prizeInfo['prize_value'],
                );
                $arrSendInput[$prizeInfo['prize_type']] = array(
                    "user_id" => $intUserId,
                    'step'    => $prizeInfo['prize_value'],
                );
            } else if ($prizeInfo['prize_type']  == Service_Rank_Viewer_ChestActivity::PRIZE_TYPE_FRAGMENT)  {
                $updateChestInput[$prizeInfo['fragment_flag']] = $prizeInfo['prize_num'];
                $arrDbSendParam = array(
                    'award_no'  => $prizeInfo['prize_value'],
                    'prize_num' => $prizeInfo['prize_num'],
                );
                if (isset($arrSendInput[$prizeInfo['prize_type']])) {
                    $arrSendInput[$prizeInfo['prize_type']][$prizeInfo['fragment_flag']] = $prizeInfo['prize_num'];
                } else {
                    $arrSendInput[$prizeInfo['prize_type']] = array(
                        "user_id"     => $intUserId,
                        'activity_id' => $prizeInfo['fragment_activity_id'],
                        $prizeInfo['fragment_flag'] => $prizeInfo['prize_num'],
                    );
                }
            } else if($prizeInfo['prize_type'] == Libs_Util_ActivitySendPrize::PRIZE_TYPE_VOTE){
                $arrDbSendParam = array(
                    'prize_effect_id' => $prizeInfo['prize_effect_id'],
                    "user_id"         => $intUserId,
                    'vote_num'        => $prizeInfo['prize_num'],
                    'source'          => $source,
                    'scene'           => Libs_Util_ActivitySendPrize::VOTE_SCENE_SEND_PRIZE,
                    'client_type'     => $intClientType,
                );
                $arrSendInput[$prizeInfo['prize_type']] = array(
                    'activity_id'        => $prizeInfo['prize_effect_id'],
                    "user_id"            => $intUserId,
                    'source_activity_id' => $intActivityId,
                    'vote_num'           => $prizeInfo['prize_num'],
                    'source'             => $source,
                    'scene'              => Libs_Util_ActivitySendPrize::VOTE_SCENE_SEND_PRIZE,
                    'client_type'        => $intClientType,
                );
            }
            // 记录发放奖励的部分参数,为了后续补单

            $arrDlInput = array(
                'activity_id'    => $intActivityId,
                'user_id'        => $intUserId,
                "prize_id"       => $intPrizeId,
                'prize_type'     => $prizeInfo['prize_type'],
                'send_order_id'  => $seqId,
                'chest_order_id' => $openChestOrderId,
                'key_type'       => $intKeyType,
                "status"         => $status,
                "num"            => $prizeInfo['prize_num'],
                "create_time"    => $intCurrTime,
                "update_time"    => $intCurrTime,
                "send_param"    => json_encode($arrDbSendParam),
            );
            $arrDlOutput = Dl_Ala_MysteriousChestActivity::addChestRewardRecord($arrDlInput);
            //todo 可以批量入库
            if(false === $arrDlOutput || Tieba_Errcode::ERR_SUCCESS !== $arrDlOutput['errno']) {
                $arrDlOutput = Dl_Ala_MysteriousChestActivity::addChestRewardRecord($arrDlInput);
                if(false === $arrDlOutput || Tieba_Errcode::ERR_SUCCESS !== $arrDlOutput['errno']) {
                    Dl_Ala_MysteriousChestActivity::rollback();
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Activity::addChestRewardRecord. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrDlOutput) . "]";
                    Bingo_Log::warning($strLog);
                    Service_Rank_Component_BaseComponent::sendWarning($strLog,'addChestRewardRecord error','',array());
                    return false;
                }
            }
        }
        Dl_Ala_MysteriousChestActivity::commit();
        foreach ($arrSendInput as $prizeType => $item) {
            if ($prizeType == Libs_Util_ActivitySendPrize::PRIZE_TYPE_KNAPSACK) {
                $arrSendParams[Libs_Util_ActivitySendPrize::SEND_TYPE_GIFT_TO_KNAPSACK_MULTI] = Libs_Util_ActivitySendPrize::getSendGiftToKnapsackMultiParams($intUserId, $item, $intChannel, $strService, $strPlatform, $strSubappType, $intClientType);
            } elseif ($prizeType == Libs_Util_ActivitySendPrize::PRIZE_TYPE_EFFECT) {
                $arrSendParams[Libs_Util_ActivitySendPrize::SEND_TYPE_ENTER_EFFECT_MULTI] = Libs_Util_ActivitySendPrize::getSendEnterEffectMultiParams($item);
            } elseif ($prizeType == Libs_Util_ActivitySendPrize::PRIZE_TYPE_MEDAL)  {
                $arrSendParams[Libs_Util_ActivitySendPrize::SEND_TYPE_MEDAL] = Libs_Util_ActivitySendPrize::getSendMedalParams($item['user_id'], $item['enter_effect_id'], $item['effective_time'], $item['invalid_time']);
            } elseif ($prizeType == Libs_Util_ActivitySendPrize::PRIZE_TYPE_LEVEL_EXP)  {
                $arrSendParams[Libs_Util_ActivitySendPrize::SEND_TYPE_LEVEL_EXP] = Libs_Util_ActivitySendPrize::getLevelExpUpParams($item['user_id'], $item['step']);
            }elseif ($prizeType == Libs_Util_ActivitySendPrize::PRIZE_TYPE_FRAGMENT)  {
                $arrSendParams[Libs_Util_ActivitySendPrize::SEND_TYPE_FRAGMENT] = Libs_Util_ActivitySendPrize::getAddFragmentpParams($item);
            }elseif ($prizeType == Libs_Util_ActivitySendPrize::PRIZE_TYPE_VOTE)  {
                $arrSendParams[Libs_Util_ActivitySendPrize::SEND_TYPE_VOTE] = Libs_Util_ActivitySendPrize::getAddVoteParams($item);
            }
        }
        if (empty($arrSendParams)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Libs_Util_ActivitySendPrize::getSendParams. input:[" . serialize($arrSendInput) . "]; output:[" . serialize($arrSendParams) . "]";
            Service_Rank_Component_BaseComponent::sendWarning($strLog,'getSendParams error','',array());
            return false;
        }
        $arrSendResult = Libs_Util_ActivitySendPrize::doMultiRal($arrSendParams);
        foreach ($arrSendResult as $sendType => $bolSend){
            if(!$bolSend){
                $intSendStatus = Service_Rank_Viewer_ChestActivity::ACTIVITY_CHEST_USER_REWARD_STATUS_SENDING;
            } else {
                $intSendStatus = Service_Rank_Viewer_ChestActivity::ACTIVITY_CHEST_USER_REWARD_STATUS_SUCCESS;
            }
            $seqIds = $arrSendOrderId[Service_Rank_Viewer_ChestActivity::$sendPrizeType[$sendType]] ? $arrSendOrderId[Service_Rank_Viewer_ChestActivity::$sendPrizeType[$sendType]] : [];

            if(!empty($seqIds)){
                $arrDlInput = array(
                    'user_id'        => $intUserId,
                    "activity_id"    => $intActivityId,
                    'send_order_ids' => $seqIds,
                    "status"         => $intSendStatus,
                    "update_time"    => $intCurrTime,
                );
                $arrDlOutput = Dl_Ala_MysteriousChestActivity::updateChestRewardStatusBySendOrderIds($arrDlInput);
                if(false === $arrDlOutput || Tieba_Errcode::ERR_SUCCESS !== $arrDlOutput['errno']) {
                    // 重试一次
                    $arrDlOutput = Dl_Ala_MysteriousChestActivity::updateChestRewardStatusBySendOrderIds($arrDlInput);
                    if(false === $arrDlOutput || Tieba_Errcode::ERR_SUCCESS !== $arrDlOutput['errno']) {
                        $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Dl_Ala_Activity::updateRepayRewardStatus. input:[" . serialize($arrDlInput) . "]; output:[" . serialize($arrDlOutput) . "]";
                        Bingo_Log::warning($strLog);
                        Service_Rank_Component_BaseComponent::sendWarning($strLog,'repay_user_update_status_error','',array());
                    }
                }
            }
        }
        return $updateChestInput;
    }

    /**
     * 获取活动页面配置
     *
     * @param $arrInput
     *
     * @return mixed
     */
    public static function getActivityPage($arrInput) {
        $intActivityId    = $arrInput['activity_id'];
        $intUserId        = $arrInput['user_id'];
        $arrActivityConf  = $arrInput['activity_conf'];
        $arrComponentConf = $arrInput['component_conf'];
        $intSystemTime    = $arrInput['system_time'];
        $result = [
            'ad_url'                => $arrActivityConf['ad_url'],
            'activity_url_map'      => $arrActivityConf['ui_conf']['activity_url_map'],
            'rule_url'              => $arrActivityConf['ui_conf']['rule_url'],
            'reward_url'            => $arrActivityConf['ui_conf']['reward_url'],
            'my_prize_url'          => $arrActivityConf['ui_conf']['my_prize_url'],
            'current_time'          => $intSystemTime,
            'begin_time'            => $arrComponentConf['begin_time'],
            'freeze_time'           => $arrActivityConf['freeze_time'],
            'shopping_begin_time'   => $arrComponentConf['shopping_begin_time'],
            'mall_freeze_time'      => $arrComponentConf['mall_freeze_time'],
            'repay_reward_top_text' => $arrComponentConf['repay_reward_top_text'],
            'total_rank_rule'       => $arrComponentConf['total_rank_rule'],
            'day_rank_rule'         => $arrComponentConf['day_rank_rule'],
            'user_id'               => $intUserId,
            'activity_id_map'       => $arrActivityConf['ui_conf']['activity_id_map'],
        ];
        // 未登录不获取用户累计消费
        if (empty($intUserId)) {
            $result['is_login'] = 0;
        } else {
            $result['is_login'] = 1;
        }

        return $result;
    }

}
