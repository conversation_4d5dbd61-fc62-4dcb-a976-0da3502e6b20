<?php
/**
 * 错峰时间PK
 * Created by PhpStorm.
 * User: lixiaoxu03
 * Date: 2020/11/19
 * Time: 7:20 PM
 */

class Service_Rank_Pk_PkOrgPeakShiftingTime extends Service_Rank_Pk_PkCore
{

    /**
     * 公会错峰时间pk匹配
     * @param $intActivityId
     * @param $intSystemTime
     * @param $arrActivityConf
     * @param $activeComponentConf
     * @param $arrPkConf
     * @return array
     */
    public static function handleMatchPkOrgPeakShiftingTime($intActivityId, $intSystemTime, $arrActivityConf, $activeComponentConf, $arrPkConf)
    {
        $strDay = date('Ymd', $intSystemTime);
        // 送礼截止后5秒开始匹配
        if ($intSystemTime < $activeComponentConf['match_time'] + 5) {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, ['msg' => 'not in pk time']);
        }

        // pk时间已到不匹配
        if ($intSystemTime >= strtotime($strDay . $arrPkConf['begin_time'])) {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, ['msg' => 'pk started']);
        }

        // 获取晋级公会
        $arrComponentWinnerInput = [
            'activity_id'       => $intActivityId,
            'component_conf'    => $activeComponentConf,
        ];
        $res = Service_Rank_Component_AnnualOrg6Comp::componentWinnerList($arrComponentWinnerInput);
        if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' getTotalWinnerHash fail. input: ' . serialize(func_get_args()));
            return self::errRet($res['errno'], ['msg' => 'getTotalWinnerHash fail']);
        }
        $orgWinnerHash = $res['ret'];
        if (count($orgWinnerHash) <= 0) {
            return self::errRet($res['errno'], ['msg' => 'org_winner_hash count is ' . count($orgWinnerHash)]);
        }

        $resultSuccess = [];//存放匹配成功的信息
        $resultFail    = [];//存放匹配失败的信息
        $allPkAddress = self::matchPkOrgPeakShiftingTime($orgWinnerHash, $intActivityId, $intSystemTime);
        foreach ($allPkAddress as $intTrackId => $trackPkAddress) { // 获取赛段
            foreach ($trackPkAddress as $intRankId => $trackRankPkAddress) { // 获取冠军 季军争夺
                foreach ($trackRankPkAddress as $currentRoundId => $arrPkAddress) { // 获取每个冠军争夺的地址

                    // 获取pk轮次的时间信息
                    $arrPkRoundTime = self::getPkRoundTimeByPkRoundId($currentRoundId, $arrPkConf, $strDay, $intTrackId, $intRankId);
                    $roundRow = [
                        'activity_id'       => $intActivityId,
                        'track_id'          => $intTrackId,
                        'rank_id'           => $intRankId, // 冠军/季军争夺标志，1=冠军，2=季军
                        'time_tag'          => $strDay,
                        'round_no'          => intval($currentRoundId),
                        'player_count'      => count($arrPkAddress),
                        'round_begin_time'  => $arrPkRoundTime['round_begin_time'],
                        'round_end_time'    => $arrPkRoundTime['round_end_time'],
                        'create_time'       => intval($intSystemTime),
                        'update_time'       => intval($intSystemTime),
                    ];
//                    try {
//                        Dl_Ala_ActivityOrgPk::startTransaction();
//                        Dl_Ala_ActivityOrgPk::addRound($roundRow);
                        $currentRoundKey = self::getPkRoundKey($intActivityId, $strDay, $currentRoundId, $intTrackId);
                        $res = self::setPkAddress($currentRoundKey, $arrPkAddress);
//                        if (!$res) {
//                            throw new Exception('setPkAddress failed', -1);
//                        } else {
//                            Dl_Ala_ActivityOrgPk::commit();
//                            $resultSuccess[] = ['round_no' => $currentRoundId, 'pk_address' => $arrPkAddress];
//                        }
//                    } catch (Exception $e) {
//                        Dl_Ala_ActivityOrgPk::rollback();
//                        Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' init pk pair fail. ' . $e->getMessage());
//                        $resultFail[] = ['round_no' => $currentRoundId, 'msg' => 'init pk pair fail. ' . $e->getMessage()];
//                        continue;
//                    }
                }
            }
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, ['match_fail' => $resultFail, 'match_success' => $resultSuccess]);
    }

    /**
     * 公会pk分组
     * @param $orgWinnerHash
     * @param $intActivityId
     * @param $intSystemTime
     * @return array
     */
    private static function matchPkOrgPeakShiftingTime($orgWinnerHash, $intActivityId, $intSystemTime)
    {
        $pkAddress = [];
        $strTimeTag = date('Ymd', $intSystemTime);


        /**
         * [
         *      track_id => [ // 赛道id
         *          orgId1 => 1, // 工会id => 排名
         *          orgId2 => 2,
         *      ]
         * ]
         */

        /**
         * 第一赛道
         */

        var_dump($orgWinnerHash);

        $intTrackId = 1;
        $arrOrgIds  = array_keys($orgWinnerHash[$intTrackId]);

        // 第一赛道
        $track1p0 = $arrOrgIds[0];
        $track1p1 = $arrOrgIds[1];
        $track1p2 = $arrOrgIds[2];
        $track1p3 = $arrOrgIds[3];

        // 一二名
        $track1addr01_1 = self::getPkPairKey($intActivityId, $strTimeTag, 1, [$track1p0, $track1p1], $intTrackId);
        $track1addr01_2 = self::getPkPairKey($intActivityId, $strTimeTag, 2, [$track1p0, $track1p1], $intTrackId);
        $track1addr01_3 = self::getPkPairKey($intActivityId, $strTimeTag, 3, [$track1p0, $track1p1], $intTrackId);

        // 三四名
        $track1addr23_1 = self::getPkPairKey($intActivityId, $strTimeTag, 1, [$track1p2, $track1p3], $intTrackId);
        $track1addr23_2 = self::getPkPairKey($intActivityId, $strTimeTag, 2, [$track1p2, $track1p3], $intTrackId);
        $track1addr23_3 = self::getPkPairKey($intActivityId, $strTimeTag, 3, [$track1p2, $track1p3], $intTrackId);

        // 第一场
        $pkAddress[$intTrackId]['1']['1'][$track1p0] = $track1addr01_1;
        $pkAddress[$intTrackId]['1']['1'][$track1p1] = $track1addr01_1;
        $pkAddress[$intTrackId]['3']['1'][$track1p2] = $track1addr23_1;
        $pkAddress[$intTrackId]['3']['1'][$track1p3] = $track1addr23_1;
        // 第二场
        $pkAddress[$intTrackId]['1']['2'][$track1p0] = $track1addr01_2;
        $pkAddress[$intTrackId]['1']['2'][$track1p1] = $track1addr01_2;
        $pkAddress[$intTrackId]['3']['2'][$track1p2] = $track1addr23_2;
        $pkAddress[$intTrackId]['3']['2'][$track1p3] = $track1addr23_2;
        // 第三场
        $pkAddress[$intTrackId]['1']['3'][$track1p0] = $track1addr01_3;
        $pkAddress[$intTrackId]['1']['3'][$track1p1] = $track1addr01_3;
        $pkAddress[$intTrackId]['3']['3'][$track1p2] = $track1addr23_3;
        $pkAddress[$intTrackId]['3']['3'][$track1p3] = $track1addr23_3;

        /**
         * 第二赛道
         */
        $intTrackId = 2;
        $arrOrgIds  = array_keys($orgWinnerHash[$intTrackId]);
        // 第二赛道
        $track2p0 = $arrOrgIds[0];
        $track2p1 = $arrOrgIds[1];
        $track2p2 = $arrOrgIds[2];
        $track2p3 = $arrOrgIds[3];

        // 一二名
        $track2addr01_1 = self::getPkPairKey($intActivityId, $strTimeTag, 1, [$track2p0, $track2p1], $intTrackId);
        $track2addr01_2 = self::getPkPairKey($intActivityId, $strTimeTag, 2, [$track2p0, $track2p1], $intTrackId);
        $track2addr01_3 = self::getPkPairKey($intActivityId, $strTimeTag, 3, [$track2p0, $track2p1], $intTrackId);

        $track2addr23_1 = self::getPkPairKey($intActivityId, $strTimeTag, 1, [$track2p2, $track2p3], $intTrackId);
        $track2addr23_2 = self::getPkPairKey($intActivityId, $strTimeTag, 2, [$track2p2, $track2p3], $intTrackId);
        $track2addr23_3 = self::getPkPairKey($intActivityId, $strTimeTag, 3, [$track2p2, $track2p3], $intTrackId);

        // 第一场
        $pkAddress[$intTrackId]['1']['1'][$track2p0] = $track2addr01_1;
        $pkAddress[$intTrackId]['1']['1'][$track2p1] = $track2addr01_1;
        $pkAddress[$intTrackId]['3']['1'][$track2p2] = $track2addr23_1;
        $pkAddress[$intTrackId]['3']['1'][$track2p3] = $track2addr23_1;
        // 第二场
        $pkAddress[$intTrackId]['1']['2'][$track2p0] = $track2addr01_2;
        $pkAddress[$intTrackId]['1']['2'][$track2p1] = $track2addr01_2;
        $pkAddress[$intTrackId]['3']['2'][$track2p2] = $track2addr23_2;
        $pkAddress[$intTrackId]['3']['2'][$track2p3] = $track2addr23_2;
        // 第三场
        $pkAddress[$intTrackId]['1']['3'][$track2p0] = $track2addr01_3;
        $pkAddress[$intTrackId]['1']['3'][$track2p1] = $track2addr01_3;
        $pkAddress[$intTrackId]['3']['3'][$track2p2] = $track2addr23_3;
        $pkAddress[$intTrackId]['3']['3'][$track2p3] = $track2addr23_3;

        return $pkAddress;
    }

    /**
     * 获取工会的赛道id
     * @param $intActivityId
     * @param $arrComponentConf
     * @param $intOrgId
     * @param $intSystemTime
     * @return array|int|string
     */
    public static function getTrackAndRankInfo($intActivityId,$arrComponentConf,$intOrgId,$intSystemTime){

        // 获取晋级公会
        $arrComponentWinnerInput = [
            'activity_id'       => $intActivityId,
            'component_conf'    => $arrComponentConf,
        ];
        $res = Service_Rank_Component_AnnualOrg6Comp::componentWinnerList($arrComponentWinnerInput);
        if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' getTotalWinnerHash fail. input: ' . serialize(func_get_args()));
            return self::errRet($res['errno'], ['msg' => 'getTotalWinnerHash fail']);
        }
        $orgWinnerHash = $res['ret'];
        if (count($orgWinnerHash) <= 0) {
            return self::errRet($res['errno'], ['msg' => 'org_winner_hash count is ' . count($orgWinnerHash)]);
        }

        $arrPkConf = $arrComponentConf['pk_conf'];

        $result = [
            'track_id'  => -1,
            'rank'      => -1,
        ];

        foreach($orgWinnerHash as $intTrackId => $arrOrgRankMap){

            $arrTrackPkConf = $arrPkConf['pk_time'][$intTrackId];
            if(isset($arrOrgRankMap[$intOrgId]) && !empty($arrOrgRankMap[$intOrgId])){
                $result['track_id'] = $intTrackId;

                $intRankId = $arrOrgRankMap[$intOrgId];
                foreach($arrTrackPkConf as $rankKey => $rankValue){
                    if($intRankId >= $rankKey){
                        $result['rank'] = $rankKey;
                    }
                }
            }
        }

        return $result;

    }

    /**
     * 根据送礼数据打pk榜
     * @param $arrInput
     * @return array
     */
    public static function handleGift($arrInput)
    {
        $intActivityId    = $arrInput['activity_id'];
        $arrComponentConf = $arrInput['component_conf'];
        $intOrgId         = $arrInput['org_id'];
        $arrPkConf        = $arrInput['pk_conf'];
        $intBenefitUserId = $arrInput['benefit_user_id'];
        $intPayUserId     = $arrInput['pay_user_id'];
        $intScore         = $arrInput['score'];
        $intSendGiftTime  = $arrInput['send_gift_time'];
        $strCurDate       = date('Ymd',$intSendGiftTime);

        // 获取赛道id和排名信息
        $arrTrackAndRankInfo = self::getTrackAndRankInfo($intActivityId,$arrComponentConf,$intOrgId,$intSendGiftTime);
        $intTrackId = $arrTrackAndRankInfo['track_id'];
        $intRank = $arrTrackAndRankInfo['rank'];

        // 获取当前pk轮次
        $intPkRound = self::getCurrentOrgPkRound($intSendGiftTime, $arrPkConf['pk_time'], $intTrackId, $intRank);

        //获取当前pk轮次的时间
        $arrPkRoundTime = self::getPkRoundTimeByPkRoundId($intPkRound, $arrPkConf, $strCurDate, $intTrackId, $intRank);

        if ($intPkRound > 0 && $intSendGiftTime >= $arrPkRoundTime['rank_begin_time'] && $intSendGiftTime < $arrPkRoundTime['rank_end_time']) {

            $pkRoundKey = self::getPkRoundKey($intActivityId, $strCurDate, $intPkRound, $intTrackId);
            $pkAddress  = self::getPkAddressByUid($pkRoundKey, $intOrgId);
            if (false === $pkAddress) {
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getPkAddressByUid failed. input:' . serialize($arrInput));
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, ['msg' => 'getPkAddressByUid failed']);
            }
            if (empty($pkAddress)) {
                return self::errRet(Tieba_Errcode::ERR_SUCCESS, ['msg' => 'pk address is empty']);
            }
            // pk打榜
            Service_Rank_Component_BaseComponent::updateRankData($intActivityId, $intSendGiftTime, $pkAddress, $intOrgId, $intScore, 86400, [], $intTrackId);
        }

        return self::errRet();
    }

    /**
     * pk轮询
     * @param $intActivityId
     * @param $intCharmUserId
     * @param $intSystemTime
     * @param $arrActivityConf
     * @param $activeComponentConf
     * @param $arrPkConf
     * @param $intOrgId
     * @return array
     */
    public static function handleOrgPkPoll($intActivityId, $intCharmUserId, $intSystemTime, $arrActivityConf, $activeComponentConf, $arrPkConf, $intOrgId = 0)
    {

        if ($intOrgId <= 0) {
            $intOrgId = Service_Rank_Component_BaseComponent::getUserOrgId($intCharmUserId);
        }

        // 获取赛道id和排名信息
        $arrTrackAndRankInfo = self::getTrackAndRankInfo($intActivityId,$activeComponentConf,$intOrgId,$intSystemTime);

        // 赛道id
        $intTrackId = $arrTrackAndRankInfo['track_id'];

        // 冠军争夺或季军争夺的id
        $intRank    = $arrTrackAndRankInfo['rank'];

        // 获取当前pk轮次
        $intPkRound = self::getCurrentOrgPkRound($intSystemTime, $arrPkConf['pk_time'], $intTrackId, $intRank);

        $res = self::pkSettle($intActivityId, $intOrgId, $intSystemTime, $arrPkConf, $intPkRound, $activeComponentConf, $intTrackId, $arrActivityConf, $intRank);
        if (Tieba_Errcode::ERR_SUCCESS == $res['errno'] && $res['ret']['mine_pk_result']) {
            $minePkResult = $res['ret']['mine_pk_result'];
        } else {
            $minePkResult = [];
        }
        $res = self::orgPkPendant($intActivityId, $arrActivityConf, $intSystemTime, $intOrgId, $intPkRound, $arrPkConf, $minePkResult, $intTrackId, $intRank);
        if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            return self::errRet($res['errno'], $res['ret']);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $res['ret']);
    }

    /**
     * pk挂件数据 终极挑战
     * @param $intActivityId
     * @param $arrActivityConf
     * @param $intSystemTime
     * @param $intOrgId
     * @param $intPkRound
     * @param $arrPkConf
     * @param $minePkResult
     * @param $intTrackId
     * @param $intRank
     * @return array
     */
    private static function orgPkPendant($intActivityId, $arrActivityConf, $intSystemTime, $intOrgId, $intPkRound, $arrPkConf, $minePkResult, $intTrackId, $intRank)
    {

        $strDay = date('Ymd', $intSystemTime);

        // pk整体时间
        $pkBeginTime  = strtotime($strDay . $arrPkConf['pk_time'][$intTrackId][$intRank]['begin_time']);
        $pkFreezeTime = strtotime($strDay . $arrPkConf['pk_time'][$intTrackId][$intRank]['freeze_time']);

        // pk轮次时间
        $arrPkRoundTime = self::getPkRoundTimeByPkRoundId($intPkRound, $arrPkConf, $strDay, $intTrackId, $intRank);

        // pk地址
        $pkRoundKey = self::getPkRoundKey($intActivityId, $strDay, $intPkRound, $intTrackId);
        $pkAddress  = self::getPkAddressByUid($pkRoundKey, $intOrgId);

        if (false === $pkAddress) {
            Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getPkAddressByUid failed. input:' . serialize([$pkRoundKey, $intOrgId]));
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, ['msg' => 'getPkAddressByUid failed']);
        }
        $intOppoId = self::getOpponentIdFromPkAddress($pkAddress, $intOrgId);

        $pendantData = [
            'unit'      => $arrActivityConf['ui_conf']['score_type_unit'][Service_Rank_Component_BaseComponent::SCORE_TYPE_CHARM],
            'round_no'  => $intPkRound, // 当前的pk轮次
            'track_id'  => $intTrackId,
            'current_time'  => $intSystemTime,
            'align_time_interval' => $arrPkConf['align_time_interval'],
            'pull_interval' => $arrPkConf['pull_interval'],
            'pull_trigger'  => intval($arrPkConf['pull_trigger']),
            'pk_time' => [ // 整场pk时间，eg. 三场整体的开始结束时间
                'begin_time'        => $pkBeginTime,
                'freeze_time'       => $pkFreezeTime,
                'prepare_duration'  => $arrPkConf['prepare_duration'],
            ],
            'round_time' => $arrPkRoundTime, // 本轮pk时间，eg. 三场中的一场
            'org_info' => [ // 当前工会信息
                'org_id' => intval($intOrgId),
            ],
            'opponent_info' => [ // 对方工会信息
                'org_id' => intval($intOppoId),
            ],
        ];

        // 获取公会名称信息
        $pendantData['org_info']['org_name'] = Service_Rank_Component_BaseComponent::getOrgNameById($intOrgId);
        $pendantData['opponent_info']['org_name'] = Service_Rank_Component_BaseComponent::getOrgNameById($intOppoId);


        // pk状态
        if ($intPkRound < 0) {
            if ($intSystemTime < $pkBeginTime) { // pk还未开始，预热态
                $pendantData['pk_status'] = self::PK_STATUS_PREHEAT;
            } else { // pk已经结束，完成态
                $pendantData['pk_status'] = self::PK_STATUS_FINISH;
            }
            return self::errRet(Tieba_Errcode::ERR_SUCCESS, $pendantData);
        } else { // pk进行中
            if ($intSystemTime < $arrPkRoundTime['rank_begin_time']) {
                $pendantData['pk_status'] = self::PK_STATUS_PREPARE; // pk准备态
            } elseif ($intSystemTime >= $arrPkRoundTime['rank_end_time']) {
                $pendantData['pk_status'] = self::PK_STATUS_BREAK; // 单场pk结束态
            } else {
                $pendantData['pk_status'] = self::PK_STATUS_CONTEST; // 单场PK中 态
            }
        }

        // 如果胜负已分则直接结束比赛
        if ($intPkRound == 3 || ($intPkRound == 2 && $intSystemTime >= $arrPkRoundTime['rank_end_time'])) {

            $res = self::getOrgPkFinalResult($intActivityId, $strDay, $intOrgId);
            if (false === $res) {
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getOrgPkFinalResult failed. input:' . serialize([$intActivityId, $strDay, $intOrgId]));
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, ['msg' => 'getOrgPkFinalResult failed']);
            }
            if (isset($res['final_result'])) {
                $winnerRound = $res['round_no'];
                if($intRank == 3){
                    $pendantData['final_rank'] = $res['final_result'] == 'winner' ? 3 : 4;
                }
                if($intRank == 1){
                    $pendantData['final_rank'] = $res['final_result'] == 'winner' ? 1 : 2;
                    if($pendantData['final_rank'] == 1){

                        if($arrPkConf['broadcast_switch'] == 1){
                            // 加锁，防止计算时间长重入
                            $lockKey = 'activity_annualorg_pktopaward#' . $intActivityId;
                            $res = Service_Rank_Component_BaseComponent::getSnxLock($lockKey);
                            if (1 == $res) {
                                // 触发全站广播
                                $bolRes = self::punishTop1GiftBroad(Libs_Util_BroadCast::BROADCAST_TYPE_ACTIVITY_ANNUAL_ORG_PK , $pendantData['org_info']['org_name']);
                                if($bolRes === false){
                                    Service_Rank_Component_BaseComponent::fatal('ANNUAL_ORG_SEND_PK_BOARDCAST_FAIL', time(), $intActivityId,
                                        ['board_type'=> Libs_Util_BroadCast::BROADCAST_TYPE_ACTIVITY_ANNUAL_ORG_PK]);
                                }
                            }
                        }
                    }
                }
                $pendantData['final_pk_winner'] = $res['final_result'] == 'winner' ? 1 : 2;
                $pendantData['pk_status'] = self::PK_STATUS_FINISH;
                // 如果第二局就分胜负了 那整个第三局的时间直接展示结果
                if ($intPkRound == 3 && $winnerRound == 2) {
                    return self::errRet(Tieba_Errcode::ERR_SUCCESS, $pendantData);
                }
            }
        }

        // 每轮pk的匹配开始前加速轮询 以免匹配倒计时出现时少的太多
        if ($pendantData['pk_status'] == self::PK_STATUS_PREHEAT && $pkBeginTime - $intSystemTime <= 10) {
            $pendantData['pull_interval'] = 3;
        } elseif ($pendantData['pk_status'] == self::PK_STATUS_BREAK && $arrPkRoundTime['round_end_time'] - $intSystemTime <= 10) {
            $pendantData['pull_interval'] = 3;
        }

        // 获取当前的pk数据
        if ($pendantData['pk_status'] == self::PK_STATUS_CONTEST) {
            $res = self::getPkRank($pkAddress);
            if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getPkRank failed. input:' . serialize($pkAddress));
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, ['msg' => 'getPkRank failed']);
            }

            $arrPkRank = $res['ret'];
            $pendantData['org_info']['pk_score'] = intval($arrPkRank[$intOrgId]['score']);
            $pendantData['opponent_info']['pk_score'] = intval($arrPkRank[$intOppoId]['score']);
        }

        // 到了本轮的结算时间
        if ($intSystemTime >= $arrPkRoundTime['rank_end_time']) {
            if ($minePkResult) {
                $arrPkResult = $minePkResult;
            } else {
                $res = self::getPkResult($pkRoundKey, $intOrgId);
                if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
                    Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' getPkResult failed. input: ' . serialize([$pkRoundKey, $intOrgId]));
                    return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, ['msg' => 'getPkResult failed']);
                }
                $arrPkResult = isset($res['ret']) && $res['ret'] ? $res['ret'] : [];
            }
            if ($arrPkResult) {
                $pendantData['pk_result'] = $arrPkResult['pk_result'];
                $pendantData['org_info']['pk_score'] = $arrPkResult['score'];
                $pendantData['opponent_info']['pk_score'] = $arrPkResult['opponent_score'];
            }
            // 前端默认值有问题，这里pk_result必须有值
            $pendantData['pk_result'] = $pendantData['pk_result'] ? $pendantData['pk_result'] : 0;
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $pendantData);
    }

    /**
     * 发送冠军公会全站广播
     * @param $intBoardType
     * @param $strGiftName
     * @return bool
     */
    public static function punishTop1GiftBroad($intBoardType, $strGiftName){

        // 发全站广播
        $arrInput = array(
            "live_id"       => 0,
            "receiver"      => 0,
            "sender"        => 0,
            "gift_id"       => 0,
            "gift_count"    => 0,
            "gift_name"     => $strGiftName,
            "subapp_type"   => "quanmin",
            'broad_type'    => $intBoardType,
        );

        $strServiceName   = "ala";
        $strServiceMethod = "punishGiftBroadCastMsg";
        Bingo_Timer::start($strServiceName."::".$strServiceMethod);
        $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrInput, null, null, 'post', null, 'utf-8');
        Bingo_Timer::end($strServiceName."::".$strServiceMethod);
        Bingo_Log::warning($strServiceName."::".$strServiceMethod.":".Bingo_Timer::calculate($strServiceName."::".$strServiceMethod));
        if(false === $arrOutput || Alalib_Conf_Error::ERR_SUCCESS != $arrOutput["errno"]){
            Bingo_Log::fatal(__CLASS__."::".__FUNCTION__."  call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]");
            return false;
        }
        return true;
    }

    /**
     * 进行结算
     * @param $intActivityId
     * @param $intOrgId
     * @param $intSystemTime
     * @param $arrPkConf
     * @param $currentRoundId
     * @param $arrComponentConf
     * @param $intTrackId
     * @param $arrActivityConf
     * @param $intRank
     * @return array
     */
    private static function pkSettle($intActivityId, $intOrgId, $intSystemTime, $arrPkConf, $currentRoundId, $arrComponentConf, $intTrackId, $arrActivityConf, $intRank)
    {
        // 不在pk轮次中
        if ($currentRoundId < 0) {
            return self::errRet();
        }

        $strDay = date('Ymd', $intSystemTime);
        // 在pk轮次中但是不在结算时段内
        //获取当前pk轮次的时间
        $arrPkRoundTime = self::getPkRoundTimeByPkRoundId($currentRoundId, $arrPkConf, $strDay, $intTrackId, $intRank);

        // 这里延迟3秒再结算 因为送礼有延迟 要保障pk结束前送的礼物都打上榜了再结算
        $intDelaySettle = intval($arrPkConf['delay_settle']);
        if ($intSystemTime < $arrPkRoundTime['rank_end_time'] + $intDelaySettle) {
            return self::errRet();
        }

        // 如果已经结算过就不再结算
        $pkRoundKey = self::getPkRoundKey($intActivityId, $strDay, $currentRoundId, $intTrackId);
        $res = self::getPkResult($pkRoundKey, $intOrgId);
        if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' getPkResult failed. input: ' . serialize([$pkRoundKey, $intOrgId]));
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, ['msg' => 'getPkResult failed']);
        }
        $arrPkResult = isset($res['ret']) && $res['ret'] ? $res['ret'] : [];
        if ($arrPkResult) {
            return self::errRet(Tieba_Errcode::ERR_SUCCESS, ['mine_pk_result' => $res['ret']]);
        }

        // 第3轮如果已经pk最终结果了则不再结算
        if ($currentRoundId == 3) {
            $res = self::getOrgPkFinalResult($intActivityId, $strDay, $intOrgId);
            if (false === $res) {
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getFinalPkWinner failed. input:' . serialize([$intActivityId, $strDay, $intOrgId]));
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, ['msg' => 'getFinalPkWinner failed']);
            }
            if (isset($res['final_result'])) {
                return self::errRet();
            }
        }

        // 计算胜负和分数
        $res = self::doPkSettle($intActivityId, $intSystemTime, $arrPkConf, $currentRoundId, $intOrgId, $arrComponentConf,$intTrackId);
        if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            return self::errRet($res['errno'], $res['ret']);
        }
        $settleResult = $res['ret'];
        if (empty($settleResult)) {
            return self::errRet();
        }

        // 当前公会的pk结果
        $minePkResult = [];
        // 保存结算结果
        $redisSettleData = [];
        $dbSettleData    = [];
        $dbCommonFields  = [
            'activity_id' => $intActivityId,
            'track_id'    => $intTrackId,
            'time_tag'    => $strDay,
            'round_no'    => $currentRoundId,
        ];
        foreach ($settleResult as $key => $values) {
            foreach ($values as $value) {
                $redisSettleData[] = [
                    'field' => $value['org_id'],
                    'value' => serialize($value)
                ];
                $dbSettleData[] = array_merge($dbCommonFields, $value);
                if ($value['org_id'] == $intOrgId) {
                    $minePkResult = $value;
                }
            }
        }

        try {
            Dl_Ala_ActivityOrgPk::startTransaction();
            Dl_Ala_ActivityOrgPk::addMultiRecord($dbSettleData);
            $res = self::setPkResult($pkRoundKey, $redisSettleData);
            if (false === $res) {
                Dl_Ala_ActivityOrgPk::rollback();
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' setPkResult failed. input:' . serialize([$pkRoundKey, $redisSettleData]));
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, ['msg' => 'setPkResult failed']);
            }
            Dl_Ala_ActivityOrgPk::commit();
        } catch (Exception $e) {
            Dl_Ala_ActivityTask::rollback();
            Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' addMultiRecord failed. input: ' . serialize($dbSettleData) . ' msg: ' . $e->getMessage());
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, ['msg' => 'addMultiRecord failed']);
        }

        // 第2轮或第3轮会出现比赛结果
        if ($currentRoundId == 2 || $currentRoundId == 3) {
            self::settleOrgPkFinalResult($strDay, $intActivityId, $arrActivityConf, $arrComponentConf, $arrPkConf, $dbSettleData, $intOrgId, $currentRoundId);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, ['mine_pk_result' => $minePkResult]);
    }

    /**
     * 结算公会pk最终结果
     * @param $strDay
     * @param $intActivityId
     * @param $arrActivityConf
     * @param $arrComponentConf
     * @param $arrPkConf
     * @param $dbSettleData
     * @param $intOrgId
     * @param $currentRoundId
     * @return bool
     */
    private static function settleOrgPkFinalResult($strDay, $intActivityId, $arrActivityConf, $arrComponentConf, $arrPkConf, $dbSettleData, $intOrgId, $currentRoundId)
    {
        if ($currentRoundId != 2 && $currentRoundId != 3) {
            return false;
        }
        // 第2轮获得2胜就赢了
        if ($currentRoundId == 2) {
            foreach ($dbSettleData as $key => $value) {
                if ($value['win_times'] >= 2) {
                    $oppoKey = 1 - $key;//一次结算肯定是2个人所以这么处理
                    self::setOrgPkFinalResult($intActivityId, $strDay, $value['org_id'], 'winner', $currentRoundId, $value['win_times'], $value['total_score']);
                    self::setOrgPkFinalResult($intActivityId, $strDay, $value['opponent_id'], 'loser', $currentRoundId, $dbSettleData[$oppoKey]['win_times'], $dbSettleData[$oppoKey]['total_score']);
                    break;
                }
            }
        }
        if ($currentRoundId == 3) {
            // 比较胜场数
            if ($dbSettleData[0]['win_times'] > $dbSettleData[1]['win_times']) {
                self::setOrgPkFinalResult($intActivityId, $strDay, $dbSettleData[0]['org_id'], 'winner', $currentRoundId, $dbSettleData[0]['win_times'], $dbSettleData[0]['total_score']);
                self::setOrgPkFinalResult($intActivityId, $strDay, $dbSettleData[0]['opponent_id'], 'loser', $currentRoundId, $dbSettleData[1]['win_times'], $dbSettleData[1]['total_score']);
            } elseif ($dbSettleData[0]['win_times'] < $dbSettleData[1]['win_times']) {
                self::setOrgPkFinalResult($intActivityId, $strDay, $dbSettleData[1]['org_id'], 'winner', $currentRoundId, $dbSettleData[1]['win_times'], $dbSettleData[1]['total_score']);
                self::setOrgPkFinalResult($intActivityId, $strDay, $dbSettleData[1]['opponent_id'], 'loser', $currentRoundId, $dbSettleData[0]['win_times'], $dbSettleData[0]['total_score']);
            } else {
                // 比较3场的收入
                if ($dbSettleData[0]['total_score'] > $dbSettleData[1]['total_score']) {
                    self::setOrgPkFinalResult($intActivityId, $strDay, $dbSettleData[0]['org_id'], 'winner', $currentRoundId, $dbSettleData[0]['win_times'], $dbSettleData[0]['total_score']);
                    self::setOrgPkFinalResult($intActivityId, $strDay, $dbSettleData[0]['opponent_id'], 'loser', $currentRoundId, $dbSettleData[1]['win_times'], $dbSettleData[1]['total_score']);
                } elseif ($dbSettleData[0]['total_score'] < $dbSettleData[1]['total_score']) {
                    self::setOrgPkFinalResult($intActivityId, $strDay, $dbSettleData[1]['org_id'], 'winner', $currentRoundId, $dbSettleData[1]['win_times'], $dbSettleData[1]['total_score']);
                    self::setOrgPkFinalResult($intActivityId, $strDay, $dbSettleData[1]['opponent_id'], 'loser', $currentRoundId, $dbSettleData[0]['win_times'], $dbSettleData[0]['total_score']);
                } else {
                    // 比较公会晋级名单中的名次 TODO
                    $res = Service_Rank_Component_PkOrgComp::getOrgWinnerHash($intActivityId, $arrComponentConf);
                    if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
                        Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' getTotalWinnerHash fail. input: ' . serialize(func_get_args()));
                        return false;
                    }
                    $orgWinnerHash = $res['ret']['org_winner_hash'];
                    if (empty($orgWinnerHash) || -2 == $orgWinnerHash) {
                        return false;
                    }
                    $org1 = $dbSettleData[0]['org_id'];
                    $org2 = $dbSettleData[0]['opponent_id'];
                    if ($orgWinnerHash[$org1] > $orgWinnerHash[$org2]) {
                        self::setOrgPkFinalResult($intActivityId, $strDay, $org2, 'winner', $currentRoundId, $dbSettleData[1]['win_times'], $dbSettleData[1]['total_score']);
                        self::setOrgPkFinalResult($intActivityId, $strDay, $org1, 'loser', $currentRoundId, $dbSettleData[0]['win_times'], $dbSettleData[0]['total_score']);
                    } else {
                        self::setOrgPkFinalResult($intActivityId, $strDay, $org1, 'winner', $currentRoundId, $dbSettleData[0]['win_times'], $dbSettleData[0]['total_score']);
                        self::setOrgPkFinalResult($intActivityId, $strDay, $org2, 'loser', $currentRoundId, $dbSettleData[1]['win_times'], $dbSettleData[1]['total_score']);
                    }
                }
            }
        }
        return true;
    }

    /**
     * 进行pk结算及分数计算
     * @param $intActivityId
     * @param $intSystemTime
     * @param $arrPkConf
     * @param $currentRoundId
     * @param $intMineId
     * @param $arrComponentConf
     * @param $intTrackId
     * @return array
     */
    private static function doPkSettle($intActivityId, $intSystemTime, $arrPkConf, $currentRoundId, $intMineId, $arrComponentConf,$intTrackId)
    {

        $strDay = date('Ymd', $intSystemTime);
        $awardRemark = 'org_pk';
        $settleResult = [
            'winner' => [],
            'loser'  => [],
            'draw'   => [],
        ];

        // 获取pk地址
        $pkRoundKey = self::getPkRoundKey($intActivityId, $strDay, $currentRoundId, $intTrackId);
        $pkAddress = self::getPkAddressByUid($pkRoundKey, $intMineId);
        if (false === $pkAddress) {
            Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getPkAddressByUid failed. input:' . serialize([$pkRoundKey, $intMineId]));
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, ['msg' => 'getPkAddressByUid failed']);
        }
        if (empty($pkAddress)) {
            return self::errRet();
        }

        // 获取对手的id
        $intOppoId = self::getOpponentIdFromPkAddress($pkAddress, $intMineId);

        // 获取当前的pk数据
        $res = self::getPkRank($pkAddress);
        if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            $res = self::getPkRank($pkAddress);//retry
            if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' getPkRank failed. input:' . serialize($pkAddress));
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, ['msg' => 'getPkRank failed']);
            }
        }
        $arrPkRank = $res['ret'];
        $intMineScore = intval($arrPkRank[$intMineId]['score']);
        $intOppoScore = intval($arrPkRank[$intOppoId]['score']);

        // 获取上一场的结果
        if ($currentRoundId > 1) {
            $lastPkRoundKey = self::getPkRoundKey($intActivityId, $strDay, $currentRoundId - 1, $intTrackId);
            $res = self::batchGetPkResult($lastPkRoundKey, [$intMineId, $intOppoId]);
            if (Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' batchGetPkResult failed. input: ' . serialize([$lastPkRoundKey, $intMineId, $intOppoId]));
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, ['msg' => 'batchGetPkResult failed']);
            }
            $arrLastPkResult = isset($res['ret']) && $res['ret'] ? $res['ret'] : [];
            $mapLastPkResult = array_column($arrLastPkResult, null, 'org_id');
        } else {
            $mapLastPkResult = [];
        }
        // 累计分数
        $intMineTotalScore = intval($mapLastPkResult[$intMineId]['total_score']) + $intMineScore;
        $intOppoTotalScore = intval($mapLastPkResult[$intOppoId]['total_score']) + $intOppoScore;

        // 处理平局
        if ($intMineScore == $intOppoScore) {
            $settleResult['draw'][] = [
                'org_id' => $intMineId,
                'score' => $intMineScore,
                'total_score' => $intMineTotalScore,
                'award_point' => 0,
                'award_remark' => $awardRemark,
                'win_times' => intval($mapLastPkResult[$intMineId]['win_times']),
                'serial_win_times' => 0,
                'beat_serial_win_times' => 0,
                'pk_result' => self::PK_RESULT_DRAW,
                'opponent_id' => $intOppoId,
                'opponent_score' => $intOppoScore,
                'opponent_award_point' => 0,
                'opponent_award_remark' => $awardRemark,
                'create_time' => $intSystemTime,
            ];
            $settleResult['draw'][] = [
                'org_id' => $intOppoId,
                'score' => $intOppoScore,
                'total_score' => $intOppoTotalScore,
                'award_point' => 0,
                'award_remark' => $awardRemark,
                'win_times' => intval($mapLastPkResult[$intOppoId]['win_times']),
                'serial_win_times' => 0,
                'beat_serial_win_times' => 0,
                'pk_result' => self::PK_RESULT_DRAW,
                'opponent_id' => $intMineId,
                'opponent_score' => $intMineScore,
                'opponent_award_point' => 0,
                'opponent_award_remark' => $awardRemark,
                'create_time' => $intSystemTime,
            ];
            return self::errRet(Tieba_Errcode::ERR_SUCCESS, $settleResult);
        }

        // 决出了胜负
        if ($intMineScore > $intOppoScore) {
            $intWinnerId = $intMineId;
            $intLoserId  = $intOppoId;
            $intWinnerScore = $intMineScore;
            $intLoserScore  = $intOppoScore;
            $intWinnerTotalScore = $intMineTotalScore;
            $intLoserTotalScore = $intOppoTotalScore;
        } else {
            $intWinnerId = $intOppoId;
            $intLoserId  = $intMineId;
            $intWinnerScore = $intOppoScore;
            $intLoserScore  = $intMineScore;
            $intWinnerTotalScore = $intOppoTotalScore;
            $intLoserTotalScore = $intMineTotalScore;
        }

        // 胜方连胜+1
        $winnerSerialWinTimes = isset($mapLastPkResult[$intWinnerId]['serial_win_times']) ? intval($mapLastPkResult[$intWinnerId]['serial_win_times']) + 1 : 1;
        $loserSerialWinTimes = isset($mapLastPkResult[$intLoserId]['serial_win_times']) ? intval($mapLastPkResult[$intLoserId]['serial_win_times']) : 0;
        // 胜方胜场数+1
        $winnerWinTimes = isset($mapLastPkResult[$intWinnerId]['win_times']) ? intval($mapLastPkResult[$intWinnerId]['win_times']) + 1 : 1;
        $loserWinTimes = isset($mapLastPkResult[$intLoserId]['win_times']) ? intval($mapLastPkResult[$intLoserId]['win_times']) : 0;

        $settleResult['winner'][] = [
            'org_id' => $intWinnerId,
            'score' => $intWinnerScore,
            'total_score' => $intWinnerTotalScore,
            'award_point' => 0,
            'award_remark' => $awardRemark,
            'win_times' => $winnerWinTimes,
            'serial_win_times' => $winnerSerialWinTimes,
            'beat_serial_win_times' => $loserSerialWinTimes,
            'pk_result' => self::PK_RESULT_WIN,
            'opponent_id' => $intLoserId,
            'opponent_score' => $intLoserScore,
            'opponent_award_point' => 0,
            'opponent_award_remark' => '',
            'create_time' => $intSystemTime,
        ];

        $settleResult['loser'][] = [
            'org_id' => $intLoserId,
            'score' => $intLoserScore,
            'total_score' => $intLoserTotalScore,
            'award_point' => 0,
            'award_remark' => $awardRemark,
            'win_times' => $loserWinTimes,
            'serial_win_times' => 0,
            'beat_serial_win_times' => 0,
            'pk_result' => self::PK_RESULT_LOSE,
            'opponent_id' => $intWinnerId,
            'opponent_score' => $intWinnerScore,
            'opponent_award_point' => 0,
            'opponent_award_remark' => '',
            'create_time' => $intSystemTime,
        ];

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $settleResult);
    }

    /**
     * 获取某轮pk的时间信息
     * @param $intPkRoundId
     * @param $arrPkConf
     * @param $strPkDay
     * @param $intTrackId
     * @param $intRankId
     * @return array
     */
    public static function getPkRoundTimeByPkRoundId($intPkRoundId, $arrPkConf, $strPkDay = '', $intTrackId = 0, $intRankId = 0)
    {
        $ret = ['round_begin_time' => 0, 'round_end_time' => 0, 'rank_begin_time' => 0, 'rank_end_time' => 0];
//        if ($intPkRoundId < 1) {
//            return $ret;
//        }
        if (empty($strPkDay)) {
            $strPkDay = date('Ymd');
        }

        $pkTime = $arrPkConf['pk_time'];

        $intPkBeginTime    = strtotime($strPkDay . $pkTime[$intTrackId][$intRankId]['begin_time']);
        $pkRoundDuration   = $pkTime[$intTrackId][$intRankId]['round_duration']; // 一整轮pk时间，eg 20min
        $pkPrepareDuration = $pkTime[$intTrackId][$intRankId]['prepare_duration'];// 一整轮pk的准备时常，eg 5minn
        $pkRankDuration    = $pkTime[$intTrackId][$intRankId]['rank_duration']; // 一整轮pk的打榜pk时间，eg 10min

        if($intPkRoundId < 0){
            $intPkRoundId = 1;
        }

        $roundBeginTime    = $intPkBeginTime + (($intPkRoundId - 1) * $pkRoundDuration);
        $roundEndTime      = $roundBeginTime + $pkRoundDuration;

        $rankBeginTime     = $roundBeginTime + $pkPrepareDuration;
        $rankEndTime       = $rankBeginTime + $pkRankDuration;

        $ret = ['round_begin_time' => $roundBeginTime, 'round_end_time' => $roundEndTime, 'rank_begin_time' => $rankBeginTime, 'rank_end_time' => $rankEndTime];
        return $ret;
    }

    /**
     * 获取当前工会pk轮次
     * @param $intSystemTime
     * @param $arrPkConf
     * @param $intTrackId
     * @param $intRank
     * @return int
     */
    public static function getCurrentOrgPkRound($intSystemTime, $arrPkConf, $intTrackId, $intRank)
    {
        $intBeginTime  = strtotime(date('Ymd', $intSystemTime) . $arrPkConf[$intTrackId][$intRank]['begin_time']);
        $intFreezeTime = strtotime(date('Ymd', $intSystemTime) . $arrPkConf[$intTrackId][$intRank]['freeze_time']);
        if ($intSystemTime < $intBeginTime) {
            return -1;
        }

        if($intSystemTime >= $intFreezeTime){
            return 3;
        }

        $intTimeDiff = $intSystemTime - $intBeginTime;
        $round = $intTimeDiff / $arrPkConf[$intTrackId][$intRank]['round_duration'];
        if ($intSystemTime == $intFreezeTime) {
            $intPkRound = intval($round);
        } else {
            $intPkRound = intval($round) + 1;
        }
        return $intPkRound;
    }

    /**
     * 某场公会pk某公会主播榜的key
     * @param $pkRoundKey
     * @param $intOrgId
     * @return string
     */
    public static function getCharmMvpKey($pkRoundKey, $intOrgId)
    {
        return "activity_orgpk_cmvp#$pkRoundKey#$intOrgId";
    }

    /**
     * 某场公会pk某公会金主榜的key
     * @param $pkRoundKey
     * @param $intOrgId
     * @return string
     */
    public static function getRichMvpKey($pkRoundKey, $intOrgId)
    {
        return "activity_orgpk_rmvp#$pkRoundKey#$intOrgId";
    }

    /**
     * 设置公会pk最终结果
     * @param $intActivityId
     * @param $strDay
     * @param $intOrgId
     * @param $finalResult
     * @param $roundNo
     * @param $winTimes
     * @param $totalScore
     */
    private static function setOrgPkFinalResult($intActivityId, $strDay, $intOrgId, $finalResult, $roundNo, $winTimes, $totalScore)
    {
        $key = "activity_orgpk_finalres#$intActivityId#$strDay#$intOrgId";
        $arrInput = array(
            'redis'  => Util_Redis::REDIS_NAME_ALA_NEW,
            'key'    => $key,
            'value'  => ['final_result' => $finalResult, 'round_no' => $roundNo, 'win_times' => $winTimes, 'total_score' => $totalScore],
            'expire' => 86400 * 30,
        );
        $arrOutput = Util_Redis::setToRedis($arrInput);
        if (false === $arrOutput) {
            $arrOutput = Util_Redis::setToRedis($arrInput);
            if (false === $arrOutput) {
                Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' setToRedis fail. input: ' . serialize($arrInput));
                Service_Rank_Component_BaseComponent::fatal('setOrgPkFinalResult', time(), $intActivityId, [func_get_args()]);
            }
        }
    }

    /**
     * 获取公会pk最终结果
     * @param $intActivityId
     * @param $strDay
     * @param $intOrgId
     * @return bool|mixed|null
     */
    public static function getOrgPkFinalResult($intActivityId, $strDay, $intOrgId)
    {
        $key = "activity_orgpk_finalres#$intActivityId#$strDay#$intOrgId";
        $arrInput = [
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
            'key'   => $key,
        ];
        $cacheData = Util_Redis::getFromRedis($arrInput);
        if (false === $cacheData) {
            $cacheData = Util_Redis::getFromRedis($arrInput);
            if (false === $cacheData) {
                return false;
            }
        }
        return $cacheData;
    }

    /**
     * 批量获取公会pk最终结果
     * @param $intActivityId
     * @param $strDay
     * @param $intOrgIds
     * @return array|bool
     */
    public static function batchGetOrgPkFinalResult($intActivityId, $strDay, $intOrgIds)
    {
        $keys = [];
        $map = [];
        foreach ($intOrgIds as $intOrgId) {
            $key = "activity_orgpk_finalres#$intActivityId#$strDay#$intOrgId";
            $keys[] = $key;
            $map[$intOrgId] = $key;
        }
        $arrInput = [
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
            'keys'  => $keys,
        ];
        $res = Util_Redis::mgetFromRedis($arrInput);
        if (false === $res) {
            Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' mgetFromRedis fail. input: ' . serialize($arrInput));
            return false;
        }

        $result = [];
        foreach ($map as $intOrgId => $key) {
            $result[$intOrgId] = $res[$key];
        }
        return $result;
    }

    /**
     * 某场公会pk某公会主播榜的mvp
     * @param $pkRoundKey
     * @param $intOrgId
     * @return bool
     */
    public static function getCharmMvp($pkRoundKey, $intOrgId)
    {
        $strKey = self::getCharmMvpKey($pkRoundKey, $intOrgId);
        $arrInput = [
            'key' => $strKey,
            'start' => 0,
            'stop' => 0,
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
        ];
        $arrOutput = Util_Redis::zRangeFromRedis($arrInput);
        if (false === $arrOutput) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Util_Redis::zRangeFromRedis fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return false;
        }
        $mvpId = $arrOutput[$strKey][0];

        return $mvpId;
    }

    /**
     * 某场公会pk某公会榜的mvp
     * @param $pkRoundKey
     * @param $intOrgId
     * @return bool
     */
    public static function getRichMvp($pkRoundKey, $intOrgId)
    {
        $strKey = self::getRichMvpKey($pkRoundKey, $intOrgId);
        $arrInput = [
            'key' => $strKey,
            'start' => 0,
            'stop' => 0,
            'redis' => Util_Redis::REDIS_NAME_ALA_NEW,
        ];
        $arrOutput = Util_Redis::zRangeFromRedis($arrInput);
        if (false === $arrOutput) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call Util_Redis::zRangeFromRedis fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return false;
        }
        $mvpId = $arrOutput[$strKey][0];

        return $mvpId;
    }

    /**
     * 配合定时任务脚本进行自动轮询 触发pk结算
     * @param $intActivityId
     * @param $intSystemTime
     * @param $arrActivityConf
     * @param $activeComponentConf
     * @param $arrPkConf
     * @return array
     */
    public static function orgAutoPoll($intActivityId, $intSystemTime, $arrActivityConf, $activeComponentConf, $arrPkConf)
    {
        $strDay = date('Ymd');
        $currentRoundId = self::getCurrentPkRound($intSystemTime, $arrPkConf);

        // 不在pk轮次则不结算
        if ($currentRoundId <= 0) {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, ['msg' => 'not in pk time']);
        }
        // 如果不在结算阶段则不结算
        $pkRoundTimeArr = self::getPkRoundTimeByPkRoundId($currentRoundId, $arrPkConf);
        $intDelaySettle = intval($arrPkConf['delay_settle']);
        if ($intSystemTime <= $pkRoundTimeArr['rank_end_time'] + $intDelaySettle) {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, ['msg' => 'not in settle time']);
        }
        // 获取赛道配置 没有的话认为所有人在第0赛道pk
        $currentRoundKey = self::getPkRoundKey($intActivityId, $strDay, $currentRoundId);
        // 获取pk地址
        $res = self::getAllPkAddress($currentRoundKey);
        if (false === $res) {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR, ['msg' => $currentRoundKey . ' getAllPkAddress failed.']);
        }
        $arrPkOrgId = array_keys($res);

        $objMulti = new Tieba_Multi('pk_auto_poll');
        foreach ($arrPkOrgId as $orgId) {
            $queryKey = "pk_auto_poll_{$orgId}";
            $params = [
                'input' => [
                    'activity_id' => $intActivityId,
                    'org_id' => $orgId,
                    'salt' => rand(1, 1024),
                ],
                'ie' => 'utf-8',
                'method' => 'orgPkPoll',
                'serviceName' => 'ala',
                'format' => 'json',

            ];
            $objMulti->register($queryKey, new Tieba_Service('ala'), $params);
        }
        $objMulti->call();

        return self::errRet();
    }
}
