<?php
/**
 * Created by PhpStorm.
 * User: sunzhexuan
 * Date: 2020/2/7
 * Time: 下午2:49
 */
define("BINGO_ENCODE_LANG", "UTF-8");
define("MODULE", "Ala_service");
class Service_LiveChat_Third
{
    /**
     * 返回值
     * @param {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
     * @param {Array} $arrOutData default array()
     * @param {String} $strMsg default ""
     * @return {Array} :errno :errmsg :{Array}output
     * */
    private static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS,$arrOutData = array(),$strMsg = ""){
        $arrOutput = array();
        $arrOutput['errno'] = $intErrno;
        $arrOutput['errmsg'] = Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput['usermsg'] = $strMsg;
        $arrOutput['data'] = $arrOutData;
        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function startChatNotify($arrInput){
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }
}