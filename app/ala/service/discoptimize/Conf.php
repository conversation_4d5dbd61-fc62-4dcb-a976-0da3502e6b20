<?php
/**
 * Created by PhpStorm.
 * User: liuxiaoshuai
 * Date: 2020/9/13
 * Time: 4:25 PM
 */

define('BINGO_ENCODE_LANG', 'UTF-8');
define('MODULE', 'Ala_service');

class Service_Discoptimize_Conf
{

    const CLOSE = 0;
    const OPEN = 1;
    private static $confKey = 'disc_optimize';
    private static $confKeyHaokan = 'disc_optimize_haokan';

    private static $defaultConf = array(
        'switch' => self::CLOSE,
        'no_pay_user' => self::CLOSE,
        'paid_user' => self::CLOSE,
        'money_user' => self::CLOSE,
    );

    public static function getTotalSwitch($type)
    {
        $conf = self::getConf($type);
        if (empty($conf['switch'])) {
            return self::$defaultConf['switch'];
        }
        return $conf['switch'];
    }

    public static function getUserSwitch($strSubAppType,$uid = 0)
    {
        //根据类型返回开关，先判断下开关，如果总开关关了，那么直接返回 0；
        $conf = self::getConf($strSubAppType);
        if(empty($conf['switch'])){
            return self::CLOSE;
        }
        if(empty($uid)){
            if (empty($conf['not_login_user'])) {
                return self::CLOSE;
            } else {
                return self::OPEN;
            }
        }
        //四端金主统一
        $userType = Service_User_User::getUserTypeByTdou($uid);
        if($userType == Service_User_User::USER_TYPE_BY_T_DOU_GOLD){
            if(empty($conf['money_user'])){
                return self::CLOSE;
            }else{
                return self::OPEN;
            }
        } else if ($userType == Service_User_User::USER_TYPE_BY_ANCHOR) {
            if(empty($conf['anchor_user'])){
                return self::CLOSE;
            }else{
                return self::OPEN;
            }
        }elseif ($userType == Service_User_User::USER_TYPE_BY_T_DOU_PAY){
            if(empty($conf['paid_user'])){
                return self::CLOSE;
            }else{
                return self::OPEN;
            }
        }else{
            if(empty($conf['no_pay_user'])){
                return self::CLOSE;
            }else{
                return self::OPEN;
            }
        }
    }



    /*
     * 配置中心
     */
    private static function getConf($type)
    {
        if($type == Alalib_Conf_Sdk::SUBAPP_HAOKAN){
            $arrReq = array(
                'key' => self::$confKeyHaokan,
            );
        }else{
            $arrReq = array(
                'key' => self::$confKey,
            );
        }
        $arrOutput = Util_Redis::getHashFromRedis($arrReq);
        if ($arrOutput === false || $arrOutput === null) {
            Bingo_Log::warning(__CLASS__ . '::' . __FUNCTION__ . ' Util_Redis::getHashFromRedis fail. input: [' . serialize($arrReq) . ']; output: [' . serialize($arrOutput) . ']');
            return self::$defaultConf;
        }

        $arrRet = array();
        foreach ($arrOutput as $key => $value) {
            $arrRet[$value['field']] = $value['value'];
        }
        return $arrRet;
    }
}
