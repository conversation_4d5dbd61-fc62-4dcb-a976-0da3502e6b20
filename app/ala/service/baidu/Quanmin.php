<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

define("BINGO_ENCODE_LANG", "UTF-8");
define("MODULE", "Ala_service");
class Service_Baidu_Quanmin
{
    const SERVICE_NAME = "service_mvideo";
    const METHOD_SYNC_LIVE_INFO = "/mvideo/internal/liveinfonew";
    const METHOD_HAOKAN_ANCHOR_INCOME = "/mvideo/internal/gethaokananchorincome";
    const METHOD_HAOKAN_ANCHOR_SALARY = "/mvideo/internal/gethaokananchorsalary";
    const METHOD_INKE_PROFIT = "/mvideo/internal/getinkexqprofit";

    /**
     * 同步直播信息
     *
     * @param array $arrInput (live_info)
     *
     * @return array $arrOutput
     **/
    public static function syncLiveInfo($arrInput)
    {
        Bingo_Log::warning("quanmin syncLiveInfo input:" . json_encode($arrInput));
        $subappType = "";
        if(!empty($arrInput['subapp_type'])) {
            $subappType = $arrInput['subapp_type'];
        } else {
            $subappType = self::getSubappTypebyLiveid($arrInput['live_id']);
        }

        $arrLiveInfo = array(
            'live_id' => intval($arrInput['live_id']),
            'room_id' => intval($arrInput['room_id']),
            'user_id' => intval($arrInput['user_id']),
            'description' => strval($arrInput['description']),
            'cover' => strval($arrInput['cover']),
            'join_count' => intval($arrInput['join_count']),
            'zan_count' => intval($arrInput['zan_count']),
            'live_duration' => intval($arrInput['live_duration']),
            'charm_count' => intval($arrInput['charm_count']),
            'flower_count' => intval($arrInput['flower_count']),
            'live_status' => intval($arrInput['live_status']),
            'start_time' => intval($arrInput['start_time']),
            'end_time' => intval($arrInput['end_time']),
            'subapp_type' => $subappType,
            'session_info' => $arrInput['session_info'],
            'location' => $arrInput['location'] ? : '',
            'lng' => $arrInput['lng'] ? : '',
            'lat' => $arrInput['lat'] ? : '',
            'type' => empty($arrInput['type']) ? 1 : $arrInput['type'], // 1-秀场直播;2-语音直播
        );
        if (!empty($arrInput['show_name'])){
            $arrLiveInfo['show_name'] = $arrInput['show_name'];
        }
        Bingo_Log::warning("quanmin syncLiveInfo call liveinfonew input:" . json_encode($arrLiveInfo));
        if ($arrLiveInfo['live_id'] <= 0 || $arrLiveInfo['room_id'] <= 0 || $arrLiveInfo['live_status'] <= 0) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }

        // ral
        $arrReq = array(
            'pathinfo' => self::METHOD_SYNC_LIVE_INFO,
            'host' => 'sv.baidu.com', //这里是线上域名，测试时要改掉，有风险
        );

        $arrParam = array(
            'data' => json_encode($arrLiveInfo)
        );

        $strResp = ral(self::SERVICE_NAME, 'POST', $arrParam, array(), $arrReq);
        $arrResp = empty($strResp) ? false : json_decode($strResp, true);
        if (false === $arrResp || (! isset($arrResp["errno"]))) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call /mvideo/internal/liveinfonew fail. input:[" . json_encode($arrParam) . "]; output:[" . $strResp . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        } elseif ($arrResp["errno"] != 0) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call /mvideo/internal/liveinfonew status is not ok. input:[" . json_encode($arrParam) . "]; output:[" . $strResp . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }

        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS);
    }
    /**
     * 根据liveid获取subapp_type
     *
     * @param array $arrInput
     * @return array $arrOutput
     **/
    public static function getSubappTypebyLiveid($liveid)
    {
        $arrRoomInfoList = array();
        $arrOpenLiveIds = array();
        $subappType = "";

        $arrOpenLiveIds[] = $liveid;

        if (empty($arrOpenLiveIds)) {
            return $subappType; // 直接返回空串
        }

        $arrServiceInput = array(
            "live_ids" => $arrOpenLiveIds,
        );

        // 先从live_info表中获取，如果没有，再从room_info表中获取
        $strServiceName = "ala";
        $strServiceMethod = "liveGetDBInfoNew";
        $arrOutput = Alalib_Util_Service::call($strServiceName,$strServiceMethod,$arrServiceInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = "call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
        }
        $arrLiveInfoList = $arrOutput["data"];
        if (isset($arrLiveInfoList[$liveid]) && !empty($arrLiveInfoList[$liveid]['subapp_type'])) {
            $subappType = $arrLiveInfoList[$liveid]['subapp_type'];
            return $subappType;
        }

        // 从room_info中获取
        $strServiceName = "ala";
        $strServiceMethod = "selectRoomInfoByLiveId";
        $arrOutput = Alalib_Util_Service::call($strServiceName,$strServiceMethod,$arrServiceInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = "call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
        }
        $arrRoomInfoList = $arrOutput["data"];

        if (isset($arrRoomInfoList[$liveid]) && !empty($arrRoomInfoList[$liveid]['subapp_type'])) {
            $subappType = $arrRoomInfoList[$liveid]['subapp_type'];
        }

        return $subappType;
    }
    /**
     * 获取好看主播的审核状态、主播身份、分成比例
     *
     * @param array $arrInput
     *
     * @return array $arrOutput
     **/
    public static function getHaokanAnchorIncome($arrInput)
    {
        if (empty($arrInput['live_ids'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }

        // ral
        $arrReq = array(
            'pathinfo' => self::METHOD_HAOKAN_ANCHOR_INCOME,
            'host' => 'sv.baidu.com',
            "querystring" => http_build_query($arrInput),
        );

        $strResp = ral(self::SERVICE_NAME, 'GET', array(), array(), $arrReq);
        $arrResp = empty($strResp) ? false : json_decode($strResp, true);
        if (false === $arrResp || (! isset($arrResp["errno"]))) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call /mvideo/internal/gethaokananchorincome fail. input:[" . json_encode($arrParam) . "]; output:[" . $strResp . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        } elseif ($arrResp["errno"] != 0) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call /mvideo/internal/gethaokananchorincome status is not ok. input:[" . json_encode($arrParam) . "]; output:[" . $strResp . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrResp['data']);
    }

    /**
     * 获取好看主播每个月已发放的底薪
     *
     * @param array $arrInput
     *
     * @return array $arrOutput
     **/
    public static function getHaokanAnchorSalary($arrInput)
    {
        if (empty($arrInput['user_id']) || empty($arrInput['year_month_list'])) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . "  param error. input:[" . serialize($arrInput) . "];";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }

        // ral
        $arrReq = array(
            'pathinfo' => self::METHOD_HAOKAN_ANCHOR_SALARY,
            'host' => 'sv.baidu.com',
            "querystring" => http_build_query($arrInput),
        );
        $strResp = ral(self::SERVICE_NAME, 'GET', array(), array(), $arrReq);
        $arrResp = empty($strResp) ? false : json_decode($strResp, true);
        if (false === $arrResp || (! isset($arrResp["errno"]))) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call /mvideo/internal/gethaokananchorsalary fail. input:[" . json_encode($arrParam) . "]; output:[" . $strResp . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        } elseif ($arrResp["errno"] != 0) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call /mvideo/internal/gethaokananchorsalary status is not ok. input:[" . json_encode($arrParam) . "]; output:[" . $strResp . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrResp['data']);
    }

    /**
     * 获取inke相亲主播收益记录
     */
    public static function getInkeProfit($arrInput)
    {
        $arrReq = array(
            'pathinfo' => self::METHOD_INKE_PROFIT,
            'host' => 'sv.baidu.com',
            "querystring" => http_build_query($arrInput),
        );
        $strResp = ral(self::SERVICE_NAME, 'GET', array(), array(), $arrReq);
        $arrResp = empty($strResp) ? false : json_decode($strResp, true);
        if (false === $arrResp || (! isset($arrResp["errno"]))) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call /mvideo/internal/getinkexqprofit fail. input:[" . json_encode($arrParam) . "]; output:[" . $strResp . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        } elseif ($arrResp["errno"] != 0) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call /mvideo/internal/getinkexqprofit status is not ok. input:[" . json_encode($arrParam) . "]; output:[" . $strResp . "]";
            Bingo_Log::warning($strLog);
            return self::errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrResp['data']);
    }

    /**
     * 返回值
     *
     * @param int $intErrno
     *
     * @return array $arrOutput
     **/
    private static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = array(), $strUserMsg = "", $strErrMsg = "")
    {
        $arrOutput = array();
        $arrOutput['errno'] = $intErrno;
        $arrOutput['errmsg'] = (strlen($strErrMsg) > 0) ? $strErrMsg : Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput['usermsg'] = $strUserMsg;
        $arrOutput['data'] = $arrOutData;
        return $arrOutput;
    }

}
