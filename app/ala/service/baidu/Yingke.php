<?php
/**
 * Created by PhpStorm.
 * User: zhouyan08
 * Date: 2019-10-21
 * Time: 11:32
 */
define("BINGO_ENCODE_LANG", "UTF-8");
define("MODULE", "Ala_service");

class Service_Baidu_Yingke
{
	const YINGKE_ENTER_LIVE_URL = 'http://open.meelove.cn/api/haokan/adapter/room/member/event';
	//const YINGKE_ENTER_LIVE_URL = 'http://testservice.imilive.cn/api/haokan/adapter/room/member/event';
	const YINGKE_QUIT_LIVE_URL = 'http://open.meelove.cn/api/haokan/adapter/room/member/event';
	//const YINGKE_QUIT_LIVE_URL = 'http://testservice.imilive.cn/api/haokan/adapter/room/member/event';
	const YINGKE_SEND_GIFT_URL = 'http://open.meelove.cn/api/haokan/adapter/room/gift/event';
	const YINGKE_NOTIFY_LIVE_INFO_URL = 'http://open.meelove.cn/api/haokan/adapter/live/event';
	const YINGKE_SEND_IM_BAN_URL = 'http://open.meelove.cn/api/haokan/adapter/room/forbidden/event';
	const YINGKE_GET_ALL_LIVELIST_URL = 'https://open.meelove.cn/api/v1/adapter/live/getAllLiveInfo'; 

	const YINGKE_ENTER_LIVE_MARK = 'enter_live';
	const YINGKE_QUIT_LIVE_MARK = 'quit_live';
	const YINGKE_IN_USE_SWITCH = 1;
	const REDIS_KEY_YINGKE_CLOSE_LIVE_TO_NOTIFY = 'yingke_close_live_notify';
	

	public static function sendImBanByAdminToYingke($arrInput)
	{
		$ban_type  = (int)$arrInput['ban_type'];
		$anchor_id = (int)$arrInput['anchor_id'];
		$user_id   = (int)$arrInput['user_id'];
		$op_id     = (int)$arrInput['op_id'];
		$arrData   = array(
			'ban_type'  => $ban_type,
			'anchor_id' => $anchor_id,
			'user_id'   => $user_id,
			'op_uid'    => $op_id,
		);
		$ret       = Util_Yingke::fetchUrlPost(self::YINGKE_SEND_IM_BAN_URL, json_encode($arrData));
		if($ret == false){
			Bingo_Log::warning('send yingke enter live info failed: '.serialize($arrInput));
			return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
		}
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	

	private static function getUserIsAdmin($user_info, $anchor_id)
	{
		$user_id          = $user_info['user_id'];
		$arrServiceInput  = array(
			'anchor_id' => $anchor_id,
			'admin_id'  => $user_id,
		);
		$strServiceName   = "ala";
		$strServiceMethod = "liveAdminIsAdmin";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			return $user_info;
		}
		$user_info['is_live_admin'] = (int)$arrOutput['data'];
		return $user_info;
	}
	
	private static function userInfoFormatter($user_info)
	{
		$user_info['anchor_live']      = (int)$user_info['anchor_live'];
		$user_info['enter_live']       = (int)$user_info['enter_live'];
		$user_info['challenge_switch'] = (int)$user_info['challenge_switch'];
		return $user_info;
	}
	
	/**
	 * @brief  获取用户真爱团等级和经验
	 * <AUTHOR>
	 * @time   2019-10-22 10:14
	 *
	 * @param $user_info
	 * @param $anchor_id
	 *
	 * @return mixed
	 */
	private static function getUserInfoWithFamilyInfo($user_info, $anchor_id)
	{
		$user_id          = $user_info['user_id'];
		$arrServiceInput  = array(
			'anchor_id' => (int)$anchor_id,
			'audi_id'   => (int)$user_id,
		);
		$strServiceName   = "ala";
		$strServiceMethod = "getUserIntimacyAndLevel";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8", 'local');
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			return $user_info;
		}
		$user_info['true_love_exp']   = strval($arrOutput['data']['total']);
		$user_info['true_love_level'] = strval($arrOutput['data']['level']);
		return $user_info;
	}
	
	/**
	 * @brief  通知映客进直播间消息(机器人用)
	 * <AUTHOR>
	 * @time   2019-10-22 10:17
	 *
	 * @param $arrInput
	 *
	 * @return array
	 */
	public static function notifyRobotYingkeEnterLiveInfo($arrInput)
	{
		if(!array($arrInput)){
			Bingo_Log::warning('param error:'.serialize($arrInput));
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$user_info = self::getUserInfoByUid($arrInput['user_id']);
		if($user_info == false){
			return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}
		$user_info = self::userInfoFormatter($user_info);
		$arrData   = array(
			'live_id'   => (int)$arrInput['live_id'],
			'is_robot'  => 1,
			'anchor_id' => (int)$arrInput['anchor_id'],
			'user_info' => $user_info,
			'type'      => self::YINGKE_ENTER_LIVE_MARK,
		);
		$ret       = Util_Yingke::fetchUrlPost(self::YINGKE_ENTER_LIVE_URL, json_encode($arrData));
		if($ret == false){
			Bingo_Log::warning('send yingke enter live info failed: '.serialize($arrInput));
			return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
		}
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	
	/**
	 * @brief  通知映客进直播间消息
	 * <AUTHOR>
	 * @time   2019-10-22 10:17
	 *
	 * @param $arrInput
	 *
	 * @return array
	 */
	public static function notifyYingkeEnterLiveInfo($arrInput)
	{
		if(!array($arrInput)){
			Bingo_Log::warning('param error:'.serialize($arrInput));
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$user_info = $arrInput['user_info'];
		$user_info = self::getUserInfoWithFamilyInfo($user_info, (int)$arrInput['anchor_id']);
		$user_info = self::getUserIsAdmin($user_info, (int)$arrInput['anchor_id']);
		$user_info = self::userInfoFormatter($user_info);
		$arrData   = array(
			'live_id'   => (int)$arrInput['live_id'],
			'is_robot'  => (int)$arrInput['is_robot'],
			'anchor_id' => (int)$arrInput['anchor_id'],
			'user_info' => $user_info,
			'type'      => self::YINGKE_ENTER_LIVE_MARK,
		);
		$ret       = Util_Yingke::fetchUrlPost(self::YINGKE_ENTER_LIVE_URL, json_encode($arrData));
		if($ret == false){
			Bingo_Log::warning('send yingke enter live info failed: '.serialize($arrInput));
			return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
		}
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}

	/**
	 * @brief  获取映客全量直播列表
	 * <AUTHOR>
	 * @time   2020-01-02 23:47
	 * @param $arrInput
	 * @return array
	 */
	public static function inkeLiveListForGr($arrInput)
	{
		if(!array($arrInput)){
			Bingo_Log::warning('param error:'.json_encode($arrInput));
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}

		$arrData   = array(
			'page_no'   => (int)$arrInput['pn'],
			'page_size'  => (int)$arrInput['ps'],
		);
		Bingo_Log::warning('inkeLiveListForGr input:'.json_encode($arrData));
		$ret       = Util_Yingke::fetchUrlPost(self::YINGKE_GET_ALL_LIVELIST_URL, json_encode($arrData));
		Bingo_Log::warning('inkeLiveListForGr output:'.json_encode($ret));
		if($ret == false){
			Bingo_Log::warning('send yingke enter live info failed: '.json_encode($arrInput));
			return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL, array());
		}

		Bingo_Log::warning("inkeLiveListForGr input:" . json_encode($arrInput));
		Bingo_Log::warning("inkeLiveListForGr output:" . json_encode($ret));

		return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $ret);
	}

	/**
	 * @brief  通知映客出直播间消息
	 * <AUTHOR>
	 * @time   2019-10-22 10:17
	 *
	 * @param $arrInput
	 *
	 * @return array
	 */
	public static function notifyYingkeQuitLiveInfo($arrInput)
	{
		if(!array($arrInput)){
			Bingo_Log::warning('param error:'.serialize($arrInput));
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$user_info = self::getUserInfoByUid($arrInput['user_id']);
		if($user_info == false){
			return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}
		$user_info = self::getUserInfoWithFamilyInfo($user_info, (int)$arrInput['anchor_id']);
		$user_info = self::getUserIsAdmin($user_info, (int)$arrInput['anchor_id']);
		$user_info = self::userInfoFormatter($user_info);
		$arrData   = array(
			'live_id'   => (int)$arrInput['live_id'],
			'is_robot'  => (int)$arrInput['is_robot'],
			'anchor_id' => (int)$arrInput['anchor_id'],
			'user_info' => $user_info,
			'type'      => self::YINGKE_QUIT_LIVE_MARK,
		);
		$ret       = Util_Yingke::fetchUrlPost(self::YINGKE_QUIT_LIVE_URL, json_encode($arrData));
		if($ret == false){
			Bingo_Log::warning('send yingke quit live info failed: '.serialize($arrInput));
			return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
		}
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	
	/**
	 * @brief  通知映客送礼消息
	 * <AUTHOR>
	 * @time   2019-10-22 10:18
	 *
	 * @param $arrInput
	 *
	 * @return array
	 */
	public static function notifyYingkeSendGiftInfo($arrInput)
	{
		if(!array($arrInput)){
			Bingo_Log::warning('param error:'.serialize($arrInput));
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$user_info = self::getUserInfoByUid($arrInput['user_id']);
		if($user_info == false){
			return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}
		$user_info = self::getUserInfoWithFamilyInfo($user_info, (int)$arrInput['anchor_id']);
		$user_info = self::getUserIsAdmin($user_info, (int)$arrInput['anchor_id']);
		$user_info = self::userInfoFormatter($user_info);
		$consume   = self::getUserPayInLive($arrInput['user_id'], $arrInput['anchor_id'], $arrInput['live_id']);
		$arrData   = array(
			'live_id'   => (int)$arrInput['live_id'],
			'anchor_id' => (int)$arrInput['anchor_id'],
			'gift_id'   => (int)$arrInput['gift_id'],
			'num'       => (int)$arrInput['num'],
			'price'     => (int)$arrInput['price'],
			'consume'   => $consume,
			'charm'     => $consume / 100,
			'user_info' => $user_info,
		);
		$ret       = Util_Yingke::fetchUrlPost(self::YINGKE_SEND_GIFT_URL, json_encode($arrData));
		if($ret == false){
			Bingo_Log::warning('send yingke send gift info failed: '.serialize($arrInput));
			return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
		}
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	
	/**
	 * @brief  生成返回
	 * <AUTHOR>
	 * @time   2019-10-22 10:19
	 *
	 * @param $errno
	 * @param string $data
	 * @param string $data_key
	 *
	 * @return array
	 */
	protected static function _errRet($errno, $data = "", $data_key = "data")
	{
		$errmsg = Tieba_Error::getErrmsg($errno);
		$arrRet = array(
			'errno'  => $errno,
			'errmsg' => $errmsg,
		);
		if($data !== ""){
			$arrRet[$data_key] = $data;
		}
		return $arrRet;
	}
	
	/**
	 * @brief  获取用户信息
	 * <AUTHOR>
	 * @time   2019-10-22 10:19
	 *
	 * @param $user_id
	 *
	 * @return bool
	 */
	private static function getUserInfoByUid($user_id)
	{
		$arrServiceInput = array(
			'uids' => array($user_id),
		);
		$arrOutput       = Service_User_User::userGetInfo($arrServiceInput);
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
			Bingo_Log::fatal('call Service_User_User::userGetInfo err!'.'['.serialize(compact('arrServiceInput', 'arrOutput')).']');
			return false;
		}
		$user_info = $arrOutput['data'][$user_id];
		return $user_info;
	}
	
	/**
	 * @brief  获取用户在一场直播间中的
	 * <AUTHOR>
	 * @time   2019-10-22 10:19
	 *
	 * @param $user_id
	 * @param $anchor_id
	 * @param $live_id
	 *
	 * @return int
	 */
	private static function getUserPayInLive($user_id, $anchor_id, $live_id)
	{
		$arrServiceInput  = array(
			'benefit_userid' => (int)$anchor_id,
			'pay_userid'     => (int)$user_id,
			'live_id'        => (int)$live_id,
		);
		$strServiceName   = "present";
		$strServiceMethod = "getBenefitScoreByLiveId";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			return 0;
		}
		return intval($arrOutput['data']['score']);
	}
	
	public static function addCloseLiveToSendNotifyToYingKe($arrInput)
	{
		$live_id       = $arrInput['live_id'];
		$arrRedisInput = array(
			"key"   => self::REDIS_KEY_YINGKE_CLOSE_LIVE_TO_NOTIFY,
			"value" => $live_id,
		);
		$arrOutput     = Util_Redis::RPUSH($arrRedisInput);
		if(false === $arrOutput){
			// 重试一次
			$arrOutput = Util_Redis::RPUSH($arrRedisInput);
			if(false === $arrOutput){
				$strLog = __CLASS__."::".__FUNCTION__."  call redis error. input:[".serialize($arrRedisInput)."];";
				Bingo_Log::fatal($strLog);
				return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
			}
		}
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	
	public static function getCloseLiveToSendNotifyToYingKe($arrInput)
	{
		$arrInput = array(
			'key' => self::REDIS_KEY_YINGKE_CLOSE_LIVE_TO_NOTIFY,
		);
		$arrRet   = Util_Redis::LPOP($arrInput);
		if($arrRet === false){
			Bingo_Log::warning('rpush redis is fail,input['.serialize($arrInput).']output['.serialize($arrRet).']');
			self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
		}
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
	}
	
	public static function sendLiveInfoToYingke($arrInput)
	{
		$live_id = (int)$arrInput['live_id'];
		if($live_id <= 0){
			return self::_errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
		}
		$liveInfo = self::_getLiveInfo($live_id);
		if($liveInfo === false){
			return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array());
		}
		$arrAdmins = self::_getAdmins($liveInfo['user_id']);
		if($arrAdmins === false){
			return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, array());
		}
		$arrData = array(
			'admin_ids' => $arrAdmins,
			'live_info' => $liveInfo,
		);
		$ret     = Util_Yingke::fetchUrlPost(self::YINGKE_NOTIFY_LIVE_INFO_URL, json_encode($arrData));
		if($ret == false){
			Bingo_Log::warning('send yingke send live info failed: '.serialize($arrInput));
			return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
		}
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $ret);
	}
	
	private static function _getLiveInfo($live_id)
	{
		$arrServiceInput  = array(
			"live_ids" => array($live_id),
		);
		$strServiceName   = "ala";
		$strServiceMethod = "liveGetInfo";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			return false;
		}
		return $arrOutput['data'][$live_id]['live_info'];
	}
	
	private static function _getAdmins($anchor_id)
	{
		$arrServiceInput  = array(
			"anchor_id" => $anchor_id,
		);
		$strServiceName   = "ala";
		$strServiceMethod = "liveAdminGetAdmins";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			return false;
		}
		$arrAdmins = array();
		foreach($arrOutput['data'] as $item){
			$arrAdmins[] = $item['admin_id'];
		}
		return $arrAdmins;
	}
}