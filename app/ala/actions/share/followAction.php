<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file followAction.php
 * <AUTHOR>
 * @date 2016/02/25
 * @brief 关注
 */
 class followAction extends Util_Base {


 	public function execute(){
 	    self::_allowOrigin();
        $strTbs = strval(Bingo_Http_Request::get('tbs',''));
        $intUserId = intval(Bingo_Http_Request::get("user_id",0));
        $intMyUserId = intval($this->_arrUserInfo["user_id"]);
        $strSubappType = Bingo_Http_Request::get('subapp_type', Alalib_Conf_Sdk::SUBAPP_TIEBA);

        if (false == $this->_tbsCheck($strTbs)) {
            $strLog = __CLASS__ . "input param . user_id:[$intUserId] tbs:[$strTbs] login_user_id:[$intMyUserId]";
            Bingo_Log::warning($strLog);
            return $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }


        if(empty($intMyUserId)){
            return $this->errRet(Tieba_Errcode::ERR_NVOTE_NOT_LOGIN);
        }

        if(empty($intUserId)){
            $strLog = "input param error. user_id is emtpty";
            Bingo_Log::warning($strLog);
            return $this->errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
        }
 	    if($intMyUserId === $intUserId) {
 	        $strLog = "input param error. user_id and to_user_id is same";
 		    Bingo_Log::warning($strLog);
            return $this->errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
 	    }
 	    
 	    $bolIsRealName = $this->_arrUserInfo['is_real_name'];
 	    
 	    if(false === $bolIsRealName)
 	    {
 	        Bingo_Log::warning('user_id='.$intMyUserId.'is not real user');
 	        return $this->errRet(Tieba_Errcode::ERR_CLIENT_USER_HAS_NO_REALNAME);
 	    }

        $intError = $this->_getAntiState($intMyUserId);
        if ($intError !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('_getAntiState error, user_id = [' .$intMyUserId .  '] error = [' . $intError . ']');
            $strErrmsg = Molib_Client_Error::getErrMsg($intError);
            return $this->errRet(Alalib_Conf_Error::ERR_PARAM_ERROR, '', $strErrmsg);
        }

        $intError = $this->_getUserBlock($intMyUserId);
        if($intError !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('_getUserBlock error, user_id = [' .$intMyUserId .  '] error = [' . $intError . ']');
            $strErrmsg = Molib_Client_Error::getErrMsg($intError);
            return $this->errRet($intError,'', $this->gbk2Utf8(Tieba_Error::getUserMsg($intError)));
        }


        $arrInput = array(
            'user_id'           => $intMyUserId,
            'to_follow_user_id' => $intUserId,
            'in_live'           => 1,
            'need_notice' => 1, //消息提醒
        );
        $arrOutput = Tieba_Service::call('ala', 'setUserFollowerBoth', $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($arrOutput === false||Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::setUserFollowerBoth fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        // 现在直播场景关注直接同步手百 20190925

        // 根据分发手百白名单进行关注同, @weiyan02 2018.12.20  Begin
        // 白名单判断
        // $strServiceName = "ala";
        // $strServiceMethod = "getAnchorToShoubaiInfo";
        // $arrReqForWhite = array(
        //     'user_id' => $intUserId,

        // );
        // $arrResForWhite = Tieba_Service::call($strServiceName, $strServiceMethod, $arrReqForWhite, null, null, "post", null, "utf-8");

        // if (($arrResForWhite !== false && $arrResForWhite['errno'] == 0 && $arrResForWhite['data'][$intUserId])
         //   || $strSubappType == Alalib_Conf_Sdk::SUBAPP_SHOUBAI) {


        // 然后开始调手百的关注服务
        /*$inputForWhite = array(
            'user_id' => $intMyUserId,
            'followed_user_ids' => array($intUserId),
            'type' => 'follow',
        );
        $arrOutputForShouBai = Tieba_Service::call('ala', 'tengenSetFollower', $inputForWhite, null, null, 'post', 'php', 'utf-8');
        if (false === $arrOutputForShouBai || Tieba_Errcode::ERR_SUCCESS != $arrOutputForShouBai["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala::tengenSetFollower fail. input:[" . serialize($inputForWhite) . "]; output:[" . serialize($arrOutputForShouBai) . "]";
            Bingo_Log::warning($strLog);
        }
        Bingo_Log::warning('call user:tengenSetFollower success. input[' . serialize($inputForWhite) . '] output[' . serialize($arrOutputForShouBai) . ']');
        //}
        //      根据分发手百白名单进行关注同, @weiyan02 2018.12.20  End

        if (self::getDefaultPushSwitch() == Libs_Define_Im::LIVE_START_PUSH_SWITCH_ON){
            // 开启主播开播提醒
            $intPushSwitch = Libs_Define_Im::LIVE_START_PUSH_SWITCH_ON;
            $arrInput = array(
                "user_id" => $intMyUserId,
                "to_user_id" => $intUserId,
                "switch"    => $intPushSwitch,
            );
            $strServiceName = "ala";
            $strServiceMethod = "relationSwitchPushAlert";
            $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrInput);
            if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = "call $strServiceName $strServiceMethod fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($arrOutput) . "]";
                if (isset($arrOutput["errno"])) {
                    Bingo_Log::warning($strLog);
                } else {
                    Bingo_Log::fatal($strLog);
                }
            }
        }*/

        Bingo_Log::pushNotice('user_id', $intMyUserId);
        Bingo_Log::pushNotice('to_user_id', $intUserId);

        $arrInput = array(
            "user_id" => $intMyUserId,
            "to_user_id" => $intUserId,
        );
        $strServiceName = "ala";
        $strServiceMethod = "relationGetRelationInfo";
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = "call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            $intErrno = Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL;
            
            return $this->errRet($intErrno);
        }
        $arrRelationInfo = $arrOutput['data'][$intMyUserId];
        $arrRetData = array(
            "relation_info" => $arrRelationInfo,
        );


        $arrServiceInput = array(
            "user_id" => $intMyUserId,
        );
        $strServiceName = "ala";
        $strServiceMethod = "userUpdateRedisAttrInfo";
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,"post",null,"utf-8");
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = "call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
        }

        $arrServiceInput = array(
            "user_id" => $intUserId,
        );
        $strServiceName = "ala";
        $strServiceMethod = "userUpdateRedisAttrInfo";
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,"post",null,"utf-8");
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = "call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
        }



        /*
        $arrServiceInput = array(
            "uids" => array($intMyUserId),
        );
        $strServiceName = "ala";
        $strServiceMethod = "userGetInfo";
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,"post",null,"utf-8");
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__."::".__FUNCTION__." call $$strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return false;
        }
        $arrUserInfo = $arrOutput["data"][$intMyUserId];
        $intLiveId = $arrUserInfo["enter_live"];
        if(!empty($intLiveId)){

            $arrInput = array(
                "live_ids"     => array($intLiveId),
            );
            $strServiceName = "ala";
            $strServiceMethod = "liveGetInfo";
            $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrInput,null,null,'post',null,'utf-8');
            if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
                $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
                //return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $arrLiveInfo = $arrOutput["data"][$intLiveId]["live_info"];
            
            if($intUserId == $arrOutput["data"][$intLiveId]["live_info"]["user_id"]){
//                $arrInput = array(
//                    "uids"     => array($intMyUserId),
//                );
//                $strServiceName = "ala";
//                $strServiceMethod = "userGetInfo";
//                $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrInput,null,null,'post',null,'utf-8');
//                if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
//                    $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
//                    Bingo_Log::warning($strLog);
//                    //return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
//                }
//                $arrUserInfo = $arrOutput["data"][$intMyUserId];
                
                //Bingo_Log::warning(var_export($arrUserInfo,true));
                // 发通知
                $arrInput = array(
                    "user_id"     => $intMyUserId,
                    "user_name"   => empty($arrUserInfo['user_name']) ? strval($arrUserInfo['user_nickname']) : strval($arrUserInfo['user_name']),
                    "level_id"    => $arrUserInfo["level_id"],
                    "group_id"    => $arrLiveInfo["group_id"],
                );
                $strServiceName = "ala";
                $strServiceMethod = "imCommitFollowAnchorMsg";
                $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrInput,null,null,'post',null,'utf-8');
                if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
                    $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
                    Bingo_Log::warning($strLog);
                    //return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }

                $arrNmqInput = array(
                    'live_id' => $intLiveId,
                    'anchor_id' => $arrLiveInfo['user_id'],
                    'group_id' => $arrLiveInfo['group_id'],
                    'start_time' => $arrLiveInfo['start_time'],
                    'charm_count' => $arrLiveInfo['charm_count'],
                    'intimacy_degree' => $arrRelationInfo['intimacy_degree'],
                    'user_id' => $intMyUserId,
                    'user_name' => $arrUserInfo['user_name'],
                    'follow_time' => time(),
                    'from_app' => 'h5share',
                );
                $nmqRet = $this->sendFollowAnchorNmq($arrNmqInput);
                if (false === $nmqRet) {
                    Bingo_Log::error("send live start nmq fail");
                }
            }
        }*/

		return $this->errRet(Alalib_Conf_Error::ERR_SUCCESS,$arrRetData);
 	}

     /**
      * 获取关注时开播提醒默认开关
      * @return int
      */
 	public function getDefaultPushSwitch()
    {
        $arrInput = array();
        $strServiceName = "alamis";
        $strServiceMethod = "getMisConf";
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrInput,null,null,'post',null,'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);

            $arrOutput['data'] = array();
        }
        $arrMisConfig = $arrOutput['data'];
        $arrSwitch = array();
        foreach($arrMisConfig as $misKey => $item){
            if ($misKey == "follow_default_push_switch"){
                $arrSwitch = $item['value'];
            }
        }
        if (!empty($arrSwitch)){
            if (isset($arrSwitch['switch']) && $arrSwitch['switch'] == Libs_Define_Im::LIVE_START_PUSH_SWITCH_ON){
                return Libs_Define_Im::LIVE_START_PUSH_SWITCH_ON;
            }
        }
        return Libs_Define_Im::LIVE_START_PUSH_SWITCH_CLOSE;
    }

     /**
      * @param $arrInput
      * @return bool
      */
     private function sendFollowAnchorNmq($arrInput) {
         $intLiveId = isset($arrInput['live_id']) ? intval($arrInput['live_id']) : 0;
         $intAnchorId = isset($arrInput['anchor_id']) ? intval($arrInput['anchor_id']) : 0;
         $intGroupId = isset($arrInput['group_id']) ? intval($arrInput['group_id']) : 0;
         $intStartTime = isset($arrInput['start_time']) ? intval($arrInput['start_time']) : 0;
         $intCharmCount = isset($arrInput['charm_count']) ? intval($arrInput['charm_count']) : 0;
         $intIntimacyDegree = isset($arrInput['intimacy_degree']) ? intval($arrInput['intimacy_degree']) : 0;
         $intUserId = isset($arrInput['user_id']) ? intval($arrInput['user_id']) : 0;
         $strUserName = isset($arrInput['user_name']) ? strval($arrInput['user_name']) : '';
         $intFollowTime = isset($arrInput['follow_time']) ? intval($arrInput['follow_time']) : 0;
         $arrNmqInput = array(
             'live_id' => $intLiveId,
             'anchor_id' => $intAnchorId,
             'group_id' => $intGroupId,
             'start_time' => $intStartTime,
             'charm_count' => $intCharmCount,
             'intimacy_degree' => $intIntimacyDegree,
             'user_id' => $intUserId,
             'user_name' => $strUserName,
             'follow_time' => $intFollowTime,
         );

         $strNmqCmd = Tieba_Cmd_Ala::follow;
         $strAlaNmpTopic = 'ala';
         $nmqRet = Tieba_Commit::commit($strAlaNmpTopic, $strNmqCmd, $arrNmqInput);
         if (false === $nmqRet || Tieba_Errcode::ERR_SUCCESS !== $nmqRet['err_no']) {
             Bingo_Log::warning("send nmq fail, nmq topic: ".$strAlaNmpTopic." nmq cmd: $strNmqCmd"."nmq input is ".serialize($arrNmqInput).'nmq output is'.serialize($nmqRet));
             return false;
         }
         return true;
     }

     /**
      * @param
      *
      * @return
      */
     private function _getAntiState($intMyUserId)
     {
         $arrUserStateReq = array(
             'reqs' => array(
                 'check_block' => array(
                     'service_type' => 'blockid',
                     'key'          => $intMyUserId,
                     'forum_id'     => 0,
                 ),
             ),
         );
         $arrOut          = Tieba_Service::call("userstate", "queryBlockAndAppealInfo", $arrUserStateReq, null, null, 'post', 'php', 'utf-8');
         if(false == $arrOut || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS){
             Bingo_Log::warning('call userstate:queryBlockAndAppealInfo failed');

             return Tieba_Errcode::ERR_MO_INTERNAL_ERROR;
         }
         if(1 == $arrOut['res']['appeal_status']){
             return Tieba_Errcode::ERR_CLIENT_BLOCK_IS_APPEALED_ERROR;
         }

         return Tieba_Errcode::ERR_SUCCESS;
     }

     /**
      * @param
      *
      * @return
      */
     private function _getUserBlock($intMyUserId)
     {
         $arrInput         = array(
             'req' => array(
                 'user_id'        => $intMyUserId,
                 'forum_id'       => 0,//global
             ),
         );
         $arrOut = Molib_Tieba_Service::call('anti', 'antiUserBlockQuery', $arrInput);
         if(!isset($arrOut['errno'])){
             Bingo_Log::warning('call anti:antiUserBlockQuery failed. input['.serialize($arrInput).'] output['.serialize($arrOut).']');

             //but do not return fail
             return Tieba_Errcode::ERR_SUCCESS;
         }
         if($arrOut['is_block'] == 1){
             return Tieba_Errcode::ERR_ANTI_ID_BLOCKED;
         }

         return Tieba_Errcode::ERR_SUCCESS;
     }


     /**
      * [gbk2Utf8 description]
      * @method gbk2Utf8
      *
      * @param  [type]   $str [description]
      * @return [type]              [description]
      */
     private function gbk2Utf8($str)
     {
         return Bingo_Encode::convert($str, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
     }

     /**
      * 返回值
      * @param {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
      * @param {Array} $arrOutData default array()
      * @param {String} $strMsg default ""
      * @return {Array} :errno :errmsg :{Array}output
      * */
     private  function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS,$arrOutData = array(),$strMsg = ""){
         $arrOutput = array();
         $arrOutput['errno'] = $intErrno;
         $arrOutput['errmsg'] = Alalib_Conf_Error::getErrorMsg($intErrno);
         $arrOutput['usermsg'] = $strMsg;
         $arrOutput['data'] = $arrOutData;
         Bingo_Log::pushNotice("errno",$intErrno);
         Bingo_Http_Response::contextType('application/json');
         echo Bingo_String::array2json($arrOutput, 'utf-8');

     }

     /**
      *
      */
     private function _allowOrigin(){
         header("Access-Control-Allow-Credentials:true");
         header('Access-Control-Allow-Methods: GET, POST, OPTIONS');

         $origin = $this->_getOrigin();
         if(empty($origin)){
             $origin = '*';
         }
         else{
             if(strpos($origin,"baidu.com") === false){
                 return;
             }
         }
         header('Access-Control-Allow-Origin:'.$origin);
     }
 }
