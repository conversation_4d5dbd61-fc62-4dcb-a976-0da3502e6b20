<?php
/*
* @brief:对外-供审核平台调用，警告直播间用户
*
* (c) ivar <<EMAIL>>
* 
* Date: 2020/7/20 下午7:41
* 
* This source file is subject to the MIT license that is bundled with this source code in the file LICENSE.
*/
class warnUserAction extends Libs_Audio_ApiBaseAction
{
    /**
     * validate params
     * @return bool
     */
    protected function _checkParams(){
        if (empty($this->arrParam['req_from'])){
            return false;
        }
        if (!in_array($this->arrParam['req_from'],Libs_Util_Audio_Tools::$reqFrom)){
            return false;
        }
        if (empty($this->arrParam['room_id']) || empty($this->arrParam['op_id']) || empty($this->arrParam['user_id']) || empty($this->arrParam['audit_reason'])){
            return false;
        }
        if (!Libs_Util_Audio_Comm::judgeSign($this->arrParam)){
            return false;
        }
        return true;
    }

    /**
     * 函数执行
     */
    public function execute(){
        // 拒绝外网IP
        $this->_rejectExternalIp();
        // 1、获取参数
        $this->_getPostParams();
        // 2、校验参数
        $boolCheckRes = $this->_checkParams();
        if (!$boolCheckRes){
            Libs_Util_Audio_Tools::buildReturnServiceFailMessage($arrOutput, Alalib_Conf_Error::ERR_PARAM_ERROR);
            return $this->_renderJson();
        }
        // 4、判断当前直播间 是否为直播状态
        $arrRoomInfo = Service_Audio_Room::isOpenRoom($this->arrParam['room_id']);
        if (empty($arrRoomInfo)){
            Libs_Util_Audio_Tools::buildReturnServiceFailMessage($this->_arrOutput, Alalib_Conf_Error::ERR_LIVE_IS_CLOSE);
            return $this->_renderJson();
        }
        // 5、记录警告信息
        $arrInput = array(
            'type'          => 2,
            'room_id'       => $this->arrParam['room_id'],
            'op_uid'        => $this->arrParam['op_id'],
            'uid'           => $this->arrParam['user_id'],
            'audit_reason'  => $this->arrParam['audit_reason'],
            'create_time'   => Bingo_Timer::getNowTime(),
        );
        $arrRet = Dl_Ala_LiveAudioNoticeRecord::insertAudioNoticeRecord($arrInput);
        if (Libs_Util_Audio_Tools::boolArrRet($arrRet)){
            Libs_Util_Audio_Tools::buildCallServiceFailedLog(__CLASS__.":".__FUNCTION__. ' insert live_audio_notice_record failed ',$arrInput);
            Libs_Util_Audio_Tools::buildReturnServiceFailMessage($this->_arrOutput);
            return $this->_renderJson();
        }

        // 6、发送IM消息,让端展示一条警告信息
        $arrInput = array(
            'user_id'           => $this->arrParam['user_id'],
            'subapp_type'       => $arrRoomInfo['subapp_type'],
            'room_id'           => $this->arrParam['room_id'],
            'live_id'           => $arrRoomInfo['live_id'],
            'audit_reason'      => $this->arrParam['audit_reason'],
            'audit_send_type'   => 'notice_one_user',
        );

        $arrRet = Service_Audio_Im::sendLiveAudioAuditMessage($arrInput);
        if (Libs_Util_Audio_Tools::boolArrRet($arrRet)){
            Libs_Util_Audio_Tools::buildCallServiceFailedLog(__CLASS__.":".__FUNCTION__. ' send_get_new_room_info message',array($this->arrParam['room_id']));
//            Libs_Util_Audio_Tools::buildReturnServiceFailMessage($this->_arrOutput);
//            return $this->_renderJson();
        }
        return $this->_renderJson();
    }
}