<?php
/**
 * appointManagerAction.php
 *
 * Time: 2020/7/20 下午3:58
 * User: <EMAIL>
 * Desc: 任命房管
 */
class appointManagerAction extends Alalib_Action_BaseAction{
    const ADMIN_NUMBER = 40;        //房管的数量
    /**
     * @param
     *
     * @return array
     */
    protected function _getPrivateInfo(){
        $this->_objRequest->addStrategy('check_sign', false);   // 不检查tieba sign, 只检查与fsg约定的特殊sign
        $strSubappType = Bingo_Http_Request::getNoXssSafe('subapp_type', Alalib_Conf_Sdk::SUBAPP_TIEBA);
        $checkRealName = $strSubappType ? false : true;

        return array(
            'check_login'           => true,
            'need_login'            => true,
            'check_real_name'       => $checkRealName,
            'tbs'                   => Molib_Service_CommitService::processTbs(Bingo_Http_Request::getNoXssSafe('tbs', '')),
            'uk'                    => Bingo_Http_Request::getNoXssSafe('uk', ''),
            'room_id'               => Bingo_Http_Request::getNoXssSafe('room_id',0),
            'group_id'              => Bingo_Http_Request::getNoXssSafe('group_id',0),
            'subapp_type'           => Bingo_Http_Request::getNoXssSafe('subapp_type','tieba'),
        );
    }

    /**
     * @param
     *
     * @return
     */
    protected function _checkPrivate(){
        $strUserUk = $this->_objRequest->getPrivateAttr('uk');   //TODO UK
        $intRoomId = $this->_objRequest->getPrivateAttr('room_id');
        $intGroupId = $this->_objRequest->getPrivateAttr('group_id');
        if(empty($strUserUk) || empty($intRoomId) || empty($intGroupId)){
            Bingo_Log:: warning(__CLASS__ . ' ' . __FUNCTION__ . " param error");
            return false;
        }

        //粒度控制
        $arrInput = array(
            'cmd' => 'appoint_room_manager',
            'uid' => $this->_objRequest->getCommonAttr("user_id"),
        );
        $arrOut   = Tieba_Service::call('ala', 'actsctrl', $arrInput, null, null, 'post', 'php', 'utf-8');

        if (isset($arrOut['data']) && $arrOut['data'] === false){
            $this->errRet(Alalib_Conf_Error::ERR_UEG_OFFEN_FAILE, array());
            return false;
        }
        return true;
    }

    protected function _execute(){
        $arrInput = array(
            'uk'             => strval($this->_objRequest->getPrivateAttr('uk')),
            'room_id'        => intval($this->_objRequest->getPrivateAttr('room_id')),
            'sign_user_id'   => intval($this->_objRequest->getCommonAttr("user_id")),
            'subapp_type'    => strval($this->_objRequest->getPrivateAttr("subapp_type")),
            'group_id'       => strval($this->_objRequest->getPrivateAttr("group_id")),
        );

        $arrInput['user_id'] = Alalib_Baidu_Account_Profile::getUidByUk($arrInput['uk']);
        $roomIdArr = array(
            'room_ids' => array($arrInput['room_id']),
            'nocache'  => true
        );

        $roomInfo = Tieba_Service::call('ala', 'getRoomInfoByRoomIds', $roomIdArr, null, null, 'post', 'php', 'utf-8');

        if($roomInfo['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call user:getRoomInfoByRoomIds failed. input['.serialize($roomIdArr).'] output['.serialize($roomInfo).']');
            return $this->errRet(Tieba_Errcode::ERR_MO_INTERNAL_ERROR);
        }


        if (empty($roomInfo['data'])){
            return $this->errRet(Tieba_Errcode::ERR_MO_INTERNAL_ERROR,array(),"此房间不存在");
        }

        if ($arrInput['sign_user_id'] != $roomInfo['data'][0]['user_id']){
            return $this->errRet(Tieba_Errcode::ERR_MO_COMMIT_POWER_NOT_ENOUGH,array(),"您没有权限任命房管");
        }

        $userId = array();
        if (!empty($roomInfo['data']['0']['host_info'])){
            $userId = json_decode($roomInfo['data']['0']['host_info'],true);
            if (!is_array($userId) || empty($userId)){
                $userId = array();
            }
        }

        if (!empty($userId['admin_uids']) && count($userId['admin_uids']) >= self::ADMIN_NUMBER){
            return $this->errRet(Tieba_Errcode::ERR_MO_INTERNAL_ERROR,array(),"房管数量超出限制");
        }

        if (!empty($userId['admin_uids']) && in_array($arrInput['user_id'],$userId['admin_uids'])){
            return $this->errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }

        if (!in_array(strval($arrInput['user_id']),$userId['admin_uids'])){
            if (empty($userId['admin_uids']) || count($userId['admin_uids']) <= 0){
                $userId['admin_uids'] = array();
            }
            $userId['admin_uids'][] = intval($arrInput['user_id']);
        }

        $appointAdmin = array(
            'room_id'   => $arrInput['room_id'],
            'host_info' => json_encode($userId),
            'custom_room_id' => $roomInfo['data']['0']['custom_room_id'],
        );

        $appointResult = Tieba_Service::call('ala', 'appointAdmin', $appointAdmin, null, null, 'post', 'php', 'utf-8');
        if($appointResult['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call user:appointAdmin failed. input['.serialize($appointAdmin).'] output['.serialize($appointResult).']');
            return $this->errRet(Tieba_Errcode::ERR_MO_INTERNAL_ERROR);
        }

        //在直播间发送IM消息

        $arrReq = array(
            'uids' => array($arrInput['user_id'],$arrInput['sign_user_id']),
        );

        $strService = 'ala';
        $strMethod = 'userGetInfo';
        $arrRes = Alalib_Util_Service::call($strService, $strMethod, $arrReq);
        if ($arrRes === false || $arrRes['errno'] != 0){
            $strLog = __CLASS__."::".__FUNCTION__. "  call $strService::$strMethod fail. input:[".serialize($arrReq)."]; output:[".serialize($arrRes)."]";
            Bingo_Log::fatal($strLog);
            return $this->errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $arrAdminUserInfo = $arrRes['data'][$arrInput['user_id']];
        $arrHostUserInfo  = $arrRes['data'][$arrInput['sign_user_id']];
        if (!empty($arrAdminUserInfo) && !empty($arrHostUserInfo)){
            $imArr = array(
                'opt_type'          => Libs_Define_Live::LIVEADMIN_OPT_APPOINT,
                'admin_id'          => $arrAdminUserInfo['user_id'],
                'admin_name'        => $arrAdminUserInfo['user_name'],
                'admin_nickname'    => $arrAdminUserInfo['user_nickname'],
                'admin_level_id'    => $arrAdminUserInfo['level_id'],
                'homeowner_id'      => $arrHostUserInfo['user_id'],
                'homeowner_name'    => $arrHostUserInfo['user_name'],
                'homeowner_nickname'=> $arrHostUserInfo['user_nickname'],
                'homeowner_level_id'=> $arrHostUserInfo['level_id'],
                'group_id'          => $arrInput['group_id'],
                'live_id'           => $roomInfo['data']['0']['live_id'],
                'subapp_type'       => $arrInput['subapp_type'],
            );
            $appointIM = Tieba_Service::call('ala', 'imAppointManager', $imArr, null, null, 'post', 'php', 'utf-8');
            if($appointIM['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('call ala:imAppointManager failed. input['.serialize($imArr).'] output['.serialize($appointIM).']');
            }
        }

        return $this->errRet(Tieba_Errcode::ERR_SUCCESS, array());
    }
}