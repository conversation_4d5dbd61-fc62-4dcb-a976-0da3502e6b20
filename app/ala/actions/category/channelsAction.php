<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file listAction.php
 * <AUTHOR>
 * @date 2016/07/12
 * @brief 类目圈子列表
 */
class channelsAction extends Alalib_Action_BaseAction {

    
    /**
     * 获取入参
     * @param null
     * @return {Array} $arrPrivateInfo
     * */
 	public function _getPrivateInfo() {
 	    $arrPrivateInfo['category_id'] = intval($this->_getInput('category_id', ''));
 	    //访客状态
 	    $arrPrivateInfo['check_login'] = false;
 		return $arrPrivateInfo;
 	}
 	/**
     * 入参检查
     * @param null
     * @return {Bool} 
     * */
 	public function _checkPrivate() {
 	    $intCategoryId = $this->_objRequest->getPrivateAttr('category_id');
 	    
 	    if(empty($intCategoryId))
 	    {
 	        $strLog = __CLASS__."::".__FUNCTION__."  param error. input:[".serialize($intCategoryId)."];";
 	        Bingo_Log::warning($strLog);
 	        $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR);
 	        return false;
 	    }
 		return true;
 	}
 	
 	/**
 	 * execute
 	 * @param null
 	 * @return null
 	 * */
 	public function _execute(){
 	    
 	    $intCategoryId = $this->_objRequest->getPrivateAttr('category_id');
 	    $intPn = intval(Bingo_Http_Request::get("pn"));
 	    $intPs = intval(bingo_Http_Request::get("ps"));
 	    if($intPs > 200 || $intPs <= 0){
			$intPs = 30;
 	    }
 	    $arrPage = Alalib_Util_Pagination::getOffset(array("pn" => $intPn, "ps" => $intPs,));
 	    if(empty($intPs)){
			$intPs = $arrPage["limit"];
		}
 	    $arrServiceInput = array(
 	        "category_id" => $intCategoryId,
 	        "offset" => $arrPage["offset"],
 	        "limit" => $arrPage["limit"],
 	    );
 	    $strServiceName = "ala";
 	    $strServiceMethod = "channelGetListByCategoryId";
 	    $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,'post',null,'utf-8');
 	    if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
 	        $strLog = __CLASS__. "::". __FUNCTION__."call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
 	        Bingo_Log::fatal($strLog);
			return $this->errRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
 	    }
 	    $arrList =  $arrOutput['data'];
 	    
 	    $boolHasMore = count($arrList) >= $arrPage["limit"] ? 1: 0;
		if(empty($arrList)){
			$arrList = array();
		}
        $arrRetData = array(
            "list" => array_values($arrList),
            "page" => array(
                "pn" => $intPn,
                "ps" => $intPs,
                "has_more" => $boolHasMore,
            ),
        );
 	    

        $arrRet = array(
            "data" => $arrRetData,
        );

		return $this->errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
 	}
 	
 	
 	
 	
 }
