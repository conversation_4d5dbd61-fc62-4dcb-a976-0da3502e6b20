<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file loginAction.php
 * <AUTHOR>
 * @date 2016/02/25
 * @brief 新用户
 */
 class loginAction extends Alalib_Action_BaseAction {

 	public function _getPrivateInfo() {
 	   
 	    
 	    /**
 	     * BDUSS 这个可以从COOkie取
 	     * */
 	    $arrPrivateInfo['BDUSS'] = intval($this->_getInput('BDUSS', 0)) ;

 	    /**
 	     * 百度域内需要登录状态
 	     * */
 	    $arrPrivateInfo['check_login'] = true;
 	    
 		return $arrPrivateInfo;
 	}
 	public function _checkPrivate() {
 		
 		return true;
 	}
 	
 	public function _execute(){

 	    $strBDUSS = $this->_objRequest->getPrivateAttr('BDUSS');
        $strSubAppType = $this->_objRequest->getCommonAttr("subapp_type", "tieba");  //
 	    if(isset($_COOKIE["BDUSS"])){
 	        $strBDUSS = $_COOKIE["BDUSS"];
 	    }
 	    $arrSessionInput = array(
 	        "BDUSS" => $strBDUSS,
 	    );
 	    $arrPassPortSessionOutput = Alalib_Passport_Session::getSession($arrSessionInput);
 	    if($arrPassPortSessionOutput["errno"] != 0 || empty($arrPassPortSessionOutput['data']["bduss"])){
 	        $strLog = __CLASS__. "::". __FUNCTION__." Alalib_Passport_Session::getSession fail. input:[".serialize($arrSessionInput)."]; output:[".serialize($arrPassPortSessionOutput)."]";
 	        Bingo_Log::fatal($strLog);
 	        return $this->errRet(Tieba_Errcode::ERR_USER_NOT_LOGIN);
 	    }
 	    
 	    $arrPassPortUserInfo = $arrPassPortSessionOutput['data'];
 	    $strSessionUserName = $arrPassPortUserInfo["uname"] ? $arrPassPortUserInfo["uname"] : $arrPassPortUserInfo["displayname"];
// 	    $intUserId = Alalib_Passport_Passgate::getUserIdByBDUSS(array(
// 	        "BDUSS" => $strBDUSS,
// 	    ));
// 	    if(empty($intUserId)) {
// 	        return $this->errRet(Tieba_Errcode::ERR_USER_NOT_LOGIN);
// 	    }

		$intUserId = $arrPassPortUserInfo["uid"];
		if(empty($intUserId)) {
			return $this->errRet(Tieba_Errcode::ERR_USER_NOT_LOGIN);
		}
// 	    $arrUserPassInfo = Alalib_Passport_Passgate::getUserInfoByUid(array("user_id" =>$intUserId ));
 	    //$intUserId = $arrUserPassInfo['data']["userid"];
 	    $regip = $this->_objRequest->getCommonAttr('ip_int');
 	    //Bingo_Log::warning(var_export($arrUserPassInfo,true));
 	    $arrServiceInput = array(
 	        "uids" => array($intUserId),
 	    );
 	    $strServiceName = "ala";
 	    $strServiceMethod = "userGetInfo";
 	    $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,'post',null,'utf-8');
 	    if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
 	        $strLog = __CLASS__. "::". __FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
 	        Bingo_Log::fatal($strLog);
 	        return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
 	    }
 	    
 	    //存在用户
 	    if(!empty($arrOutput['data'])){
 	        $arrUserInfo = $arrOutput['data'][$intUserId];
 	        
 	    } else {
 	        //新增用户
 	        $arrPPInput = array(
 	            "user_id" =>$intUserId,
 	        );
 	        $arrPPOutput = Alalib_Passport_Passgate::getUserInfoByUid($arrPPInput);
 	        if($arrPPOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
 	            $strLog = __CLASS__. "::". __FUNCTION__." call Alalib_Passport_Service::getUserInfoByUid fail. input:[".
 	                serialize($arrPPInput)."]; output:[".serialize($arrPPOutput)."]";
 	            Bingo_Log::fatal($strLog);
 	            return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
 	        }
 	        $arrPassPortUserInfo = $arrPPOutput['data'][$intUserId];
 	        $strUserName = empty($arrPassPortUserInfo["username"]) ? $strSessionUserName :$arrPassPortUserInfo["username"];
 	        
 	        
 	        $strAvatar = self::getPortraitAvatar(array(
 	            "user_id" => $arrPassPortUserInfo["userid"],
 	            "user_name" => $arrPassPortUserInfo["username"],
 	        ));
 	        $arrInput = array(
 	            "user_id" => $arrPassPortUserInfo["userid"],
 	            "user_name" => $strUserName,
 	            "pass_name" => $strUserName,
 	            "portrait" => $strAvatar,
 	            "ip"      => $regip,
 	        );
 	       
 	        $strServiceName = "ala";
 	        $strServiceMethod = "userCreateUser";
 	        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrInput,null,null,'post',null,'utf-8');
 	        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
 	            $strLog = __CLASS__. "::". __FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
 	            Bingo_Log::fatal($strLog);
 	            return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
 	        }
 	        //打统计日志
 	        Bingo_Log::pushNotice("app_active", $arrPassPortUserInfo["userid"]);
 	        Bingo_Log::pushNotice('is_new_uid',1);
 	    }

 	    $arrServiceInput = array(
 	        "user_id" => $intUserId,
 	        "is_login" => Libs_Define_User::USER_IS_LOGIN,
 	        "ip"      => $regip,
 	    );
 	    $strServiceName = "ala";
 	    $strServiceMethod = "userEditLoginStatus";
 	    $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,'post',null,'utf-8');
 	    if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
 	        $strLog = __CLASS__. "::". __FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
 	        Bingo_Log::warning($strLog);
 	        return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
 	    }
 	    
 	    if(empty($arrUserInfo)) {
 	        //获取用户信息
 	        $arrInput = array(
 	            "uids" => array($arrPassPortUserInfo["userid"]),
 	             
 	        );
 	        $strServiceName = "ala";
 	        $strServiceMethod = "userGetInfo";
 	        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrInput,null,null,'post',null,'utf-8');
 	        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
 	            $strLog = __CLASS__. "::". __FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrInput)."]; output:[".serialize($arrOutput)."]";
 	            Bingo_Log::warning($strLog);
 	            return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
 	        }
 	        $arrUserInfo = $arrOutput['data'][$arrPassPortUserInfo["userid"]];
 	    } 
        $arrUserInfo["is_login"] = Libs_Define_User::USER_IS_LOGIN;

 	    $arrRetData["user_info"] = $arrUserInfo;
 	    $arrRetData['anti'] = array(
 	        'tbs' => Tieba_Tbs::gene(true),
 	    );
 	    $arrRet = array(
 	        "data" =>$arrRetData,
 	    );

        $arrServiceInput = array(
            "uid" => intval($arrUserInfo['user_id']),
            'subapp_type' => strval($strSubAppType)
        );
        $strServiceName = "ala";
        $strServiceMethod = "userSyncInfo";
        $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = "call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
        }
 	    
		return $this->errRet(Tieba_Errcode::ERR_SUCCESS,$arrRet);
 	}
 	
 	/**
 	 * @param {Array} $arrInput :user_id :user_name
 	 * @return portrait
 	 * */
 	private static function getPortraitAvatar($arrInput){
       // 大头像 http://himg.baidu.com/sys/portraith/item/
 	    // 小头像 http://himg.baidu.com/sys/portrait/item/
       return "http://himg.bdimg.com/sys/portraith/item/" .
            Tieba_Ucrypt::encode($arrInput["user_id"], $arrInput["user_name"]).'.jpg';
 	    
 	}
 	
 	
 	
 }
