<?php
/**
 * 擂台赛接口
 * Created by PhpStorm.
 * User: zhanghanqing
 * Date: 2018/9/5
 * Time: 10:55
 */

class getArenaRankInfoAction extends Util_Base
{

    /**
     * execute
     *
     * @param null
     *
     * @return array
     * */
    public function execute()
    {
        $arrBaseUserInfo = $this->_getUserInfo();
        $intLoginUserId  = $arrBaseUserInfo['user_id'];
        $intLiveId = intval(Bingo_Http_Request::get('_loc_live_id', 0));

        $strMultiCallKey = 'getArenaRankInfoAction';
        $objMultiCall = new Tieba_Multi($strMultiCallKey);
        $arrMultiCallParam = array();
        if (!empty($intLiveId)) {
            $arrMultiCallParam["ala"]["liveGetInfo"] = array(
                "live_ids" => array($intLiveId),
            );
        }
        $arrMultiCallParam["ala"]["arenaRankGetPreviousTopUser"] = array();
        $arrMultiCallParam["ala"]["arenaRankGetList"] = array();

        foreach ($arrMultiCallParam as $serviceName => $serviceMethod) {
            foreach ($serviceMethod as $methodName => $methodParam) {
                $arrInput = array(
                    "serviceName" => $serviceName,
                    "method" => $methodName,
                    'ie' => 'utf-8',
                    "input" => $methodParam,

                );
                $objMultiCall->register($methodName, new Tieba_Service($serviceName), $arrInput);
            }
        }
        Bingo_Timer::start('call_core_service');
        $objMultiCall->call();
        Bingo_Timer::end('call_core_service');

        $arrMultiCallResults = array();
        foreach ($arrMultiCallParam as $serviceName => $serviceMethod) {
            $arrMultiCallResults[$serviceName] = array();
            foreach ($serviceMethod as $methodName => $methodParam) {
                $arrOutput = $objMultiCall->getResult($methodName);
                $arrMultiCallResults[$serviceName][$methodName] = $arrOutput['data'];
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $serviceName $methodName fail. input:[" . serialize($methodParam) . "]  output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::warning($strLog);
                }
            }
        }

        $arrUserId = array();
        $arrRankList = $arrMultiCallResults['ala']['arenaRankGetList'];
        $arrTopRankList = $arrMultiCallResults['ala']['arenaRankGetPreviousTopUser'];
        foreach ($arrRankList as $item){
            $arrUserId[] = $item['member'];
        }
        $intOnRank = 0;
        if (!empty($intLiveId)){
            $arrLiveInfo = $arrMultiCallResults['ala']['liveGetInfo'][$intLiveId]["live_info"];
            $intAnchorUserId = $arrLiveInfo['user_id'];
            if (in_array($intAnchorUserId,$arrUserId)){
                $intOnRank = 1;
            }else if (!empty($intAnchorUserId)){
                $arrUserId[] = $intAnchorUserId;
                $arrServiceInput  = array(
                    "user_id" => $intAnchorUserId,
                );
                $strServiceName   = "ala";
                $strServiceMethod = "arenaRankGetUserInfo";
                $arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
                if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
                    $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
                    Bingo_Log::warning($strLog);
                }
                $arrAnchorRankInfo = $arrOutput['data'][$intAnchorUserId];
            }
        }

        foreach ($arrTopRankList as $item){
            $arrUserId[] = $item['member'];
        }

        $arrUserId = array_unique($arrUserId);
        $intNowTime = Bingo_Timer::getNowTime();
        $intNowHourTime = intval(strtotime(date('Y-m-d H') . ":0:0"));
        $intNextHourTime = $intNowHourTime + 3600;
        $intResetTime = $intNextHourTime - $intNowTime;

        if (empty($arrUserId)){
            $arrRet = array(
                'top_anchor_list' => array(),
                'arena_anchor_list' => array(),
                'current_anchor_info' => null,
                'reset_time' => $intResetTime,
            );
            return $this->_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
        }
        $arrServiceInput  = array(
            "uids" => $arrUserId,
        );
        $strServiceName   = "ala";
        $strServiceMethod = "userGetInfo";
        $arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
        }
        $arrUserInfos = $arrOutput['data'];
        foreach($arrUserInfos as $index => $tmpUserInfo){
            if(isset($tmpUserInfo['user_nickname'])){
                $arrUserInfos[$index]['user_name'] = $tmpUserInfo['user_nickname'];
            }
        }

        $arrRankListInfo = array();
        $intMinScore = $arrRankList[0]['score'];
        foreach ($arrRankList as $index => $item) {
            $tmpUserId = $item['member'];
            $arrUserInfo = $arrUserInfos[$tmpUserId];
            $arrRankListInfo[] = array(
                'user_name' => $arrUserInfo['user_name'],
                'user_id' => $item['member'],
                'portrait' => $arrUserInfo['portrait'],
                'user_status' => $arrUserInfo['user_status'],
                'sex' => $arrUserInfo['sex'],
                'live_id' => $arrUserInfo['live_id'],
                'live_status' => $arrUserInfo['live_status'],
                'score' =>  self::_scoreToChram($item['score']),
            );
            if ($intOnRank && $item['member'] == $intAnchorUserId){
                if ($index == 0){
                    $strText = '';
                }else{
                    $intDiff =  self::_scoreToChram($arrRankList[$index - 1]['score'] - $item['score']);
                    $strText = '距离上一名还差' . $intDiff . '魅力值';
                }
                $arrAnchorRankInfo = array(
                    'score' => $item['score'],
                    'rank' => $index + 1,
                    'rank_text' => $strText,
                );
            }
            if ($item['score'] < $intMinScore){
                $intMinScore = $item['score'];
            }
        }
        $arrTopRankListInfo = array();
        foreach ($arrTopRankList as $item) {
            $tmpUserId = $item['member'];
            $arrUserInfo = $arrUserInfos[$tmpUserId];
            $arrTopRankListInfo[] = array(
                'user_name' => $arrUserInfo['user_name'],
                'portrait' => $arrUserInfo['portrait'],
                'user_status' => $arrUserInfo['user_status'],
                'sex' => $arrUserInfo['sex'],
                'user_id' => $tmpUserId,
                'live_id' => $arrUserInfo['live_id'],
                'live_status' => $arrUserInfo['live_status'],
                'score' =>  self::_scoreToChram($item['score']),
            );
        }

        if (!empty($intAnchorUserId)) {
            $arrAnchorUserInfo = $arrUserInfos[$intAnchorUserId];
            $arrAnchorRankInfo['user_name'] = $arrAnchorUserInfo['user_name'];
            $arrAnchorRankInfo['portrait'] = $arrAnchorUserInfo['portrait'];
            $arrAnchorRankInfo['user_status'] = $arrAnchorUserInfo['user_status'];
            $arrAnchorRankInfo['sex'] = $arrAnchorUserInfo['sex'];
            $arrAnchorRankInfo['live_id'] = $arrAnchorUserInfo['live_id'];
            $arrAnchorRankInfo['live_status'] = $arrAnchorUserInfo['live_status'];
            $arrAnchorRankInfo['user_id'] = $intAnchorUserId;

            if (!$intOnRank){
                $intDiff =  self::_scoreToChram($intMinScore - $arrAnchorRankInfo['score']);
                $strText = '距离上榜还差' . $intDiff . '魅力值';
                $arrAnchorRankInfo['rank_text'] = $strText;
            }
            $arrAnchorRankInfo['score'] = self::_scoreToChram($arrAnchorRankInfo['score']);

            if (intval($arrAnchorRankInfo['rank']) > 10 || intval($arrAnchorRankInfo['rank']) <= 0){
                $arrAnchorRankInfo['rank'] = null;
            }
        }

        if (empty($arrAnchorRankInfo)){
            $arrAnchorRankInfo = null;
        }

        $intNowTime = Bingo_Timer::getNowTime();
        $intNowHourTime = intval(strtotime(date('Y-m-d H') . ":0:0"));
        $intNextHourTime = $intNowHourTime + 3600;
        $intResetTime = $intNextHourTime - $intNowTime;

        $arrRet = array(
            'top_anchor_list' => $arrTopRankListInfo,
            'arena_anchor_list' => $arrRankListInfo,
            'current_anchor_info' => $arrAnchorRankInfo,
            'reset_time' => $intResetTime,
        );
        return $this->_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

    /**
     * 返回结果
     * @param $intScore
     * @return int
     */
    private static function _scoreToChram($intScore)
    {
        return intval($intScore / 100);
    }

}