<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file getUserRecordAction.php
 * <AUTHOR>
 * @date 2016/08/15
 * @brief 关注
 */
class getUserRecordAction extends Bingo_Action_Abstract
{
	/**
	 * @return mixed
	 */
	public function execute()
	{
		/*
		 *

		http://haoyunfeng.service.tieba.otp.baidu.com/service/ala?method=selectTotalBonusRankList&offset=0&res_num=100&ie=utf-8&format=json

		 * */
		$strBDUSS = Bingo_Http_Request::get("BDUSS");
		$intUserId = 1;
		/*
		if(isset($_COOKIE["BDUSS"])){
			$strBDUSS = $_COOKIE["BDUSS"];
		}
		$arrSessionInput = array(
				"BDUSS" => $strBDUSS,
		);
		$arrPassPortSessionOutput = Alalib_Passport_Session::getSession($arrSessionInput);
		if($arrPassPortSessionOutput["errno"] != 0 || empty($arrPassPortSessionOutput['data']["bduss"])){
			$strLog = __CLASS__. "::". __FUNCTION__." Alalib_Passport_Session::getSession fail. input:[".serialize($arrSessionInput)."]; output:[".serialize($arrPassPortSessionOutput)."]";
			Bingo_Log::fatal($strLog);
			return $this->errRet(Tieba_Errcode::ERR_USER_NOT_LOGIN);
		}

		$arrPassPortUserInfo = $arrPassPortSessionOutput['data'];
		$intUserId = Alalib_Passport_Passgate::getUserIdByBDUSS(array(
				"BDUSS" => $strBDUSS,
		));*/


		$arrServiceInput = array(
			"user_id" =>$intUserId,
		);
		$strServiceName = "ala";
		$strServiceMethod = "selectUserBonusRecord";
		$arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,"post",null,"utf-8");
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			$arrOutput["data"] = array();
		}

		$arrRecords = $arrOutput["data"];


		$data = array(
			"user_record" => $arrRecords,
		);
		$this->errRet(Alalib_Conf_Error::ERR_SUCCESS,$data);
	}

	/**
	 * 返回值
	 * @param {Int} $intErrno default Alalib_Conf_Error::ERR_SUCCESS
	 * @param {Array} $arrOutData default array()
	 * @param {String} $strMsg default ""
	 * @return {Array} :errno :errmsg :{Array}output
	 * */
	private static function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS,$arrOutData = array(),$strMsg = ""){
		$arrOutput = array();
		$arrOutput['errno'] = $intErrno;
		$arrOutput['errmsg'] = Alalib_Conf_Error::getErrorMsg($intErrno);
		$arrOutput['usermsg'] = $strMsg;
		$arrOutput['data'] = $arrOutData;
		Bingo_Log::pushNotice("errno",$intErrno);
		Bingo_Http_Response::contextType('application/json');
		echo Bingo_String::array2json($arrOutput, 'utf-8');

	}
}
