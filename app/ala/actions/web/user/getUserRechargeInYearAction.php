<?php
/**
 * Created by PhpStorm.
 * User: sunzhexuan
 * Date: 2019/4/22
 * Time: 上午11:45
 * 首充活动  判断1年内是否充值过
 */

class getUserRechargeInYearAction extends Util_Base
{
    const SHOUCHONG_YES = 1;
    const SHOUCHONG_NOT = 2;

    public function execute()
    {
        $arrBaseUserInfo = $this->_getUserInfo();
        $intUserId       = $arrBaseUserInfo['user_id'];

        if(empty($intUserId)){
            $intUserId   = Bingo_Http_Request::get('user_id', 0);
        }

        if(empty($intUserId)){
            $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }

        $arrReq = array(
            'user_id' => $intUserId,
            'pn' => 0,
            'limit' => 2,
        );
        $arrRes = Tieba_Service::call("tbmall", "getPayOrder", $arrReq, null, null, 'post', 'php', 'utf-8');
        if ($arrRes['errno'] != Alalib_Conf_Error::ERR_SUCCESS){
            $strLog = __CLASS__."::".__FUNCTION__." call tbmall:getPayOrder fail. input[".serialize($arrReq)."] output[".serialize($arrRes)."]";
            Bingo_Log::warning($strLog);
            return $this->_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrRet['status'] = self::SHOUCHONG_YES;
        $intFirstOrderFinishTime = intval($arrRes['data']['order_list'][0]['finish_time']);
        $intTimeSpanDay = intval( (time() - $intFirstOrderFinishTime) / (24 * 60 * 60) );
        if($intTimeSpanDay < 365) {
            $arrRet['status'] = self::SHOUCHONG_NOT;
        }

        return $this->_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRet);
    }
}