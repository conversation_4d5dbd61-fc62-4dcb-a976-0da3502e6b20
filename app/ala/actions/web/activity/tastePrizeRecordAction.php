<?php
/**
 * Created by PhpStorm.
 * User: lixiaoxu03
 * Date: 2021/3/30
 * Time: 6:51 PM
 */

class tastePrizeRecordAction extends Alalib_Action_BaseAction
{
    /**
     * @return mixed
     */
    public function _getPrivateInfo()
    {
        // 允许跨域
        $this->allowOrigin("/\.baidu\.com(:[0-9]+)?$/");
        $arrPrivateInfo['activity_id']     = intval($this->_getInput('activity_id', 0));

        $this->_objRequest->addStrategy('check_sign', false);
        $arrPrivateInfo['check_login']     = false;
        $arrPrivateInfo['need_login']      = false;

        return $arrPrivateInfo;
    }

    /**
     * @return bool
     */
    public function _checkPrivate()
    {
        if (empty($this->_objRequest->getPrivateAttr('activity_id'))) {
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, [], '参数错误');
            return false;
        }
        return true;
    }

    /**
     * @return bool
     */
    public function _execute()
    {

        $arrServiceInput = [
            'activity_id' => $this->_objRequest->getPrivateAttr('activity_id'),
        ];

        $strServiceName       = 'ala';
        $strServiceMethod     = 'tastePrizeRecord';
        Bingo_Timer::start($strServiceMethod);
        $arrOutput            = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8', 'local');
        Bingo_Timer::end($strServiceMethod);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call {$strServiceName} {$strServiceMethod} fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, $arrOutput['ret']);
        }
        return $this->errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput['ret']);
    }

    /**
     * 允许跨域
     * @param $pregRule
     */
    private function allowOrigin($pregRule)
    {
        $origin = $this->_getOrigin();
        if (preg_match($pregRule, $origin)) {
            header('Access-Control-Allow-Origin:' . $origin);
            header("Access-Control-Allow-Credentials:true");
            header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        }
    }

    /**
     * 自定义返回数据格式 和liveserver返回格式保持一致
     * @param int $intErrno
     * @param array $arrOutData
     * @param string $strUserMsg
     * @return bool
     */
    public function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = [], $strUserMsg = '')
    {
        $arrOutput = [];

        $arrOutput['error_no'] = $intErrno;
        $arrOutput['message'] = $strUserMsg;

        // result 输出必须为对象, 否则客户端请求会出异常
        $arrOutput['result'] = empty($arrOutData) ? (object)array() : $arrOutData;

        $arrOutput['time'] = time();

        $this->_objResponse->setOutData($arrOutput);
        return true;
    }
}
