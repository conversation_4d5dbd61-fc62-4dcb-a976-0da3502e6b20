<?php
/**
 *
 * <AUTHOR>
 * @time 2020/5/13
 */

class orgRankAction extends Alalib_Action_BaseAction
{
    /**
     * @return mixed
     */
    public function _getPrivateInfo()
    {
        // 允许跨域
        $this->allowOrigin("/\.baidu\.com(:[0-9]+)?$/");

        $arrPrivateInfo['activity_id']     = intval($this->_getInput('activity_id', 0));
        $arrPrivateInfo['component_index'] = intval($this->_getInput('component_index', 0));
        $arrPrivateInfo['pn']              = intval($this->_getInput('pn', 0));
        $arrPrivateInfo['rn']              = intval($this->_getInput('rn', 0));
        $arrPrivateInfo['rank_type']       = intval($this->_getInput('rank_type', 0));
        $arrPrivateInfo['score_type']      = intval($this->_getInput('score_type', 0));
        $arrPrivateInfo['rank_time_tag']   = intval($this->_getInput('rank_time_tag', 0));
        $arrPrivateInfo['need_foot_bar']   = intval($this->_getInput('need_foot_bar', 0));
        $arrPrivateInfo['bro_count']       = intval($this->_getInput('bro_count', 0));
        $arrPrivateInfo['track_id']        = intval($this->_getInput('track_id', 0));
        $arrPrivateInfo['user_id']         = intval($this->_getInput('user_id', 0));
        $arrPrivateInfo['calltype']        = strval($this->_getInput('calltype', ''));
        $arrPrivateInfo['group_id']        = intval($this->_getInput('group_id', 0));
        $arrPrivateInfo['track_id']        = intval($this->_getInput('track_id', 0));

        $this->_objRequest->addStrategy('check_sign', false);
        $arrPrivateInfo['check_login'] = false;
        $arrPrivateInfo['need_login']  = false;

        return $arrPrivateInfo;
    }

    /**
     * @return bool
     */
    public function _checkPrivate()
    {
        if (empty($this->_objRequest->getPrivateAttr('activity_id')) ||
            empty($this->_objRequest->getPrivateAttr('component_index')) ||
            empty($this->_objRequest->getPrivateAttr('pn')) ||
            empty($this->_objRequest->getPrivateAttr('rn')) ||
            empty($this->_objRequest->getPrivateAttr('rank_type')) ||
            empty($this->_objRequest->getPrivateAttr('score_type'))

        ) {
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, [], '参数错误');
            return false;
        }
        return true;
    }

    /**
     * @return bool
     */
    public function _execute()
    {
        $calltype = $this->_objRequest->getPrivateAttr('calltype');

        $arrServiceInput = [
            'activity_id'     => $this->_objRequest->getPrivateAttr('activity_id'),
            'component_index' => $this->_objRequest->getPrivateAttr('component_index'),
            'pn'              => $this->_objRequest->getPrivateAttr('pn'),
            'rn'              => $this->_objRequest->getPrivateAttr('rn'),
            'rank_type'       => $this->_objRequest->getPrivateAttr('rank_type'),
            'score_type'      => $this->_objRequest->getPrivateAttr('score_type'),
            'rank_time_tag'   => $this->_objRequest->getPrivateAttr('rank_time_tag'),
            'need_foot_bar'   => $this->_objRequest->getPrivateAttr('need_foot_bar'),
            'bro_count'       => $this->_objRequest->getPrivateAttr('bro_count'),
            'user_id'         => $this->_objRequest->getPrivateAttr('user_id'),
            'track_id'        => $this->_objRequest->getPrivateAttr('track_id'),
            'group_id'        => $this->_objRequest->getPrivateAttr('group_id'),
        ];

        if ($calltype == 'remote' || $calltype == 'local') {
            $strServiceName   = 'ala';
            $strServiceMethod = 'orgRank';
            Bingo_Timer::start($strServiceMethod);
            $arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8', $calltype);
            Bingo_Timer::end($strServiceMethod);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__."::".__FUNCTION__." call {$strServiceName} {$strServiceMethod} fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
                return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        } else {
            $arrOutput = Service_Rank_Component_BaseComponent::orgRank($arrServiceInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                $strLog = __CLASS__."::".__FUNCTION__." call Service_Rank_Component_BaseComponent::orgRank fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
                return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }

        return $this->errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput['ret']);
    }

    /**
     * 允许跨域
     * @param $pregRule
     */
    private function allowOrigin($pregRule)
    {
        $origin = $this->_getOrigin();
        if (preg_match($pregRule, $origin)) {
            header('Access-Control-Allow-Origin:' . $origin);
            header("Access-Control-Allow-Credentials:true");
            header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        }
    }

    /**
     * 自定义返回数据格式 和liveserver返回格式保持一致
     * @param int $intErrno
     * @param array $arrOutData
     * @param string $strUserMsg
     * @return bool
     */
    public function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = [], $strUserMsg = '')
    {
        $arrOutput = [];

        $arrOutput['error_no'] = $intErrno;
        $arrOutput['message'] = $strUserMsg;

        // result 输出必须为对象, 否则客户端请求会出异常
        $arrOutput['result'] = empty($arrOutData) ? (object)array() : $arrOutData;

        $arrOutput['time'] = time();

        $this->_objResponse->setOutData($arrOutput);
        return true;
    }
}