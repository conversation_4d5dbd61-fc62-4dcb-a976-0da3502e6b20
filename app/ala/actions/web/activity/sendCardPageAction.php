<?php
/**
 * Created by PhpStorm.
 * User: zhangpeng62
 * Date: 2020-11-05
 * Time: 17:26
 */

class sendCardPageAction extends Alalib_Action_BaseAction
{
    /**
     * @return mixed
     */
    public function _getPrivateInfo()
    {
        // 允许跨域
        $this->allowOrigin("/\.baidu\.com(:[0-9]+)?$/");

        $arrPrivateInfo['activity_id']   = intval($this->_getInput('activity_id', 0));
        $arrPrivateInfo['card_id']       = intval($this->_getInput('card_id', 0));
        $arrPrivateInfo['c']             = intval($this->_getInput('c', 2));

        $this->_objRequest->addStrategy('check_sign', false);
        $arrPrivateInfo['check_login'] = false;
        $arrPrivateInfo['need_login']  = true;

        return $arrPrivateInfo;
    }

    /**
     * @return bool
     */
    public function _checkPrivate()
    {
        if (empty($this->_objRequest->getPrivateAttr('activity_id')) ||
            empty($this->_objRequest->getPrivateAttr('card_id')) ||
            empty($this->_objRequest->getPrivateAttr('c'))
        ) {
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, [], '参数错误');
            return false;
        }
        return true;
    }

    /**
     * @return bool
     */
    public function _execute()
    {
        $arrServiceInput = [
            'activity_id'   => $this->_objRequest->getPrivateAttr('activity_id'),
            'card_id'       => $this->_objRequest->getPrivateAttr('card_id'),
            'user_id'       => $this->_objRequest->getCommonAttr('user_id'),
            'c'             => $this->_objRequest->getPrivateAttr('c'),
        ];

        $arrOutput = Service_Rank_Viewer_Card::sendCardPage($arrServiceInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return $this->errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput['ret']);
    }

    /**
     * 允许跨域
     * @param $pregRule
     */
    private function allowOrigin($pregRule)
    {
        $origin = $this->_getOrigin();
        if (preg_match($pregRule, $origin)) {
            header('Access-Control-Allow-Origin:' . $origin);
            header("Access-Control-Allow-Credentials:true");
            header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        }
    }

    /**
     * 自定义返回数据格式 和liveserver返回格式保持一致
     * @param int $intErrno
     * @param array $arrOutData
     * @param string $strUserMsg
     * @return bool
     */
    public function errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS, $arrOutData = [], $strUserMsg = '')
    {
        $arrOutput = [];

        $arrOutput['error_no'] = $intErrno;
        $arrOutput['message'] = $strUserMsg;

        // result 输出必须为对象, 否则客户端请求会出异常
        $arrOutput['result'] = empty($arrOutData) ? (object)array() : $arrOutData;

        $arrOutput['time'] = time();

        $this->_objResponse->setOutData($arrOutput);
        return true;
    }
}
