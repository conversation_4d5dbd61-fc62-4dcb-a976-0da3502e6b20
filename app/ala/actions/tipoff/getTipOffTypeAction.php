<?php

/**
 * <AUTHOR>
 * @date   2020-06-29
 * 
 * 获取举报类型(举报原因文案)
 */
class getTipOffTypeAction extends Alalib_Action_BaseAction
{
    /**
     * @return array
     */
    public function _getPrivateInfo() {
        $arrPrivateInfo['live_id'] = intval($this->_getInput('live_id'), 0);

        //访客状态
        $arrPrivateInfo['check_login'] = false;

        return $arrPrivateInfo;
    }

    public function _checkPrivate() {
        return true;
    }

    public function _execute()
    {
        $intLiveID = $this->_objRequest->getPrivateAttr('live_id');
        $arrTipoffType = array(
            array(
                'content' => '色情低俗',
                'type'    => 1,
            ),
            array(
                'content' => '涉嫌违法犯罪',
                'type'    => 1,
            ),
            array(
                'content' => '时政信息不实',
                'type'    => 1,
            ),
            array(
                'content' => '广告诈骗',
                'type'    => 1,
            ),
            array(
                'content' => '未成年直播',
                'type'    => 1,
            ),
            array(
                'content' => '烟酒驾驶',
                'type'    => 1,
            ),
            array(
                'content' => '危险动作',
                'type'    => 1,
            ),
            array(
                'content' => '侮辱谩骂',
                'type'    => 1,
            ),
            array(
                'content' => '其他',
                'type'    => 0,// type=0 端上会调起输入面板
            ),
        );

        $arrResponse = array(
            'data' => array(
                'tipoff_type' => $arrTipoffType,
            ),
        );
        return $this->errRet(Tieba_Errcode::ERR_SUCCESS, $arrResponse);
    }
}
