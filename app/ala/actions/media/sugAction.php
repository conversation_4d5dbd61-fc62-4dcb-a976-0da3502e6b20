<?php

/**
 * 视频名称sug
 * <AUTHOR>
 *
 */
class sugAction  extends Alalib_Action_BaseAction {

	const PAGE_SIZE = 20;
	
     /**
      * 获取参数
      * @param null
      * @return {Array} $arrPrivateInfo
      * */
 	public function _getPrivateInfo() {
 	    $arrPrivateInfo['kw'] = strval($this->_getInput('kw', ''));
 	    $arrPrivateInfo['pn'] = intval($this->_getInput('pn', 0));

 	    //访客状态
 	    $arrPrivateInfo['check_login'] = false;
 	    // $arrPrivateInfo['check_tbs'] = true;
 	    
 		return $arrPrivateInfo;
 	}
 	/**
 	 * 参数校验
 	 * @param null
 	 * @return true
 	 * */
 	public function _checkPrivate() {
 		$intPn = $this->_objRequest->getPrivateAttr('pn');
 		if(empty($this->_objRequest->getPrivateAttr('kw'))
 			|| $intPn < 0 ){
 		    $this->errRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
 		    return false;
 		}
 		return true;
 	}
 	
	/**
	 * 执行函数
	 * @param null
	 * @return null
	 * */
	public function _execute(){

		$strKW = $this->_objRequest->getPrivateAttr("kw");
		$intPn = $this->_objRequest->getCommonAttr("pn");
		$arrMediaInfo = $this->_getMediaInfo($strKW, $intPn);
		
		if (false === $arrMediaInfo)
		{
			return $this->errRet(Tieba_Errcode::ERR_SUCCESS, array());
		}
		
		$arrRet = array(
				"data" => array('media_list' => $arrMediaInfo,),
		);
		return $this->errRet(Tieba_Errcode::ERR_SUCCESS,$arrRet);
	}
	
	/**
	 * 获取视频信息
	 * @param unknown_type $strKw
	 * @param unknown_type $intPn
	 * @return mixed|boolean|multitype:number string
	 */
	private function _getMediaInfo($strKw, $intPn)
	{
		$strCacheKey = 'search_media_title_'.$strKw.'_'.$intPn;
		$strCacheVal = Util_Memcached::getCache($strCacheKey);
		//var_dump($strCacheVal);
		if (null !== $strCacheVal)
		{
			$arrOut = unserialize($strCacheVal);
			return $arrOut;
		}

		$intOffset = self::PAGE_SIZE*$intPn;
		$intResNum = self::PAGE_SIZE;
		$arrRalInput = array('kw' => $strKw,
				'offset' => $intOffset,
				'res_num' => $intResNum,
				);
		
		$arrOutput = Tieba_Service::call('ala', 'selectMediaInfoByTitle', $arrRalInput,null,null,'post','php','utf-8');
		
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = "call ala::selectMediaInfoByTitle fail. input:[".serialize($arrRalInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::error($strLog);
			return false;
		}

	    $arrMediaInfo = isset($arrOutput['data']) ? $arrOutput['data'] : array();
	    $arrObjMediaInfo = $this->_buildMediaInfo($arrMediaInfo);
	    $strCacheVal = serialize($arrObjMediaInfo);
	    $intLifeTime = 10;
	    Util_Memcached::addCache($strCacheKey, $strCacheVal, $intLifeTime);
	    return $arrObjMediaInfo;
	}

    /**
     * 构造输出信息
     * @param unknown_type $arrMediaInfo
     * @return multitype:number string
     */
    private function _buildMediaInfo($arrMediaInfos)
    {
    	$arrOutput = array();
    	//var_dump($arrMediaInfos);
    	foreach($arrMediaInfos as $arrMediaInfo)
    	{
    		$arrObj = array();
    		$arrObj['media_id'] = isset($arrMediaInfo['media_id']) ? $arrMediaInfo['media_id'] : '';
    		$arrObj['title'] = isset($arrMediaInfo['title']) ? strval($arrMediaInfo['title']) : '';
    		$arrObj['play_count'] = isset($arrMediaInfo['play_count']) ? intval($arrMediaInfo['play_count']) : 1;
    		//$arrObj['duration'] = isset($arrMediaInfo['duration']) ? intval($arrMediaInfo['duration']) : 0;
    		//$arrObj['vod_file'] = isset($arrMediaInfo['vod_file']) ? strval($arrMediaInfo['vod_file']) : '';
    		//$arrObj['vod_cover'] = isset($arrMediaInfo['vod_cover']) ? strval($arrMediaInfo['vod_cover']) : '';
    		//$arrObj['create_time'] = isset($arrMediaInfo['create_time']) ? intval($arrMediaInfo['create_time']) : 0;
    		$arrOutput[] = $arrObj;
    	}
    	
    	return $arrOutput;
    }

}
