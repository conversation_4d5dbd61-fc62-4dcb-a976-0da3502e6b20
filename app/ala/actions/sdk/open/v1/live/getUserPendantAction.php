<?php

/***************************************************************************
 *
 * 查询指定用户挂件
 * http://wiki.baidu.com/pages/viewpage.action?pageId=1123571681#id-%E5%9F%BA%E6%9C%A8%E9%B1%BC%E5%AF%B9%E6%8E%A5server%E6%8E%A5%E5%8F%A3%E6%96%87%E6%A1%A3-%E6%9F%A5%E8%AF%A2%E6%8C%87%E5%AE%9A%E7%94%A8%E6%88%B7%E6%8C%82%E4%BB%B6
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/
class getUserPendantAction extends Alalib_Action_BaseAction
{

    // 针对不同的tpl提供不同的定制化设置
    private static $_allowTpl = array(
        'jimuyu',
    );

    /**
     * 入参检查
     *
     * @param
     *            null
     * @return {Array}
     *
     */
    public function _getPrivateInfo()
    {
        $arrPrivateInfo['ala_id'] = intval($this->_getInput('ala_id', ''));
        $arrPrivateInfo['user_id'] = intval($this->_getInput('user_id', ''));
        $arrPrivateInfo['tpl'] = strval($this->_getInput('tpl', ''));
        $arrPrivateInfo['need_expired'] = intval($this->_getInput('need_expired', ''));
        $arrPrivateInfo['ak'] = strval($this->_getInput('ak', ''));
        $arrPrivateInfo['sign'] = strval($this->_getInput('sign', ''));

        // 内网接口
        $this->_objRequest->addStrategy('check_sign', false);
        $arrPrivateInfo['check_login'] = false;
        $arrPrivateInfo['need_login'] = false;
        $arrPrivateInfo['no_stlog'] = true;

        return $arrPrivateInfo;
    }

    /**
     * 入参检查
     *
     * @param
     *            null
     * @return {Bool}
     *
     */
    public function _checkPrivate()
    {
        $userId = $this->_objRequest->getPrivateAttr('user_id');
        $alaId = $this->_objRequest->getPrivateAttr('ala_id');
        if (empty($userId)) {
            // 传了ala_id不传uid,则使用ala_id解出uid
            if (empty($alaId)) {
                Bingo_Log::warning("ala_id is empty");
                $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR);
                return false;
            } else {
                // 通过alaid获取uid
                $arrInput = array($alaId);
                $userId = Service_User_User::getUidsByAlaids($arrInput);
                if (empty($userId) || !isset($userId[0])) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . "alaid is invalid,  call Service_User_User::getUidsByAlaids fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($userId) . "]";
                    Bingo_Log::warning($strLog);
                    $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, "", "alaid is invalid");
                    return false;
                }
                $this->_objRequest->addPrivateAttr('user_id', $userId[0]);
            }
        }


        // sign
        $arrParam = array_merge(Bingo_Http_Request::getGetAll(), Bingo_Http_Request::getPostAll());
        if (empty($arrParam['ak']) || empty($arrParam['sign'])) {
            Bingo_Log::warning("sign or ak is invalid ,  param=".json_encode($arrParam));
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        
        $bolSign = Alalib_Util_Sdk::checkSign($arrParam['ak'], $arrParam['sign'], $arrParam);
        if ($bolSign == false) {
            Bingo_Log::warning("sign check fail ,  param=".json_encode($arrParam));
            $this->errRet(Tieba_Errcode::ERR_INVALID_SIGN);
            return false;
        }
        
        return true;
    }


    /**
     *
     * @return bool
     *
     */
    public function _execute()
    {
        $userId = (int)$this->_objRequest->getPrivateAttr('user_id');
        $returnExpired = (int)$this->_objRequest->getPrivateAttr('need_expired');
        $data = $this->getUserPendants($userId, $returnExpired);
        $data = array(
            'data' => array(
                'pendant_pos_main' => $this->fmtPendants($data['pendant_pos_main']),
                'pendant_pos_vice' => $this->fmtPendants($data['pendant_pos_vice']),
            )
        );
        $this->errRet(Tieba_Errcode::ERR_SUCCESS, $data);
        return true;
    }

    /**
     * @param $pendantList
     * @return array
     */
    private function fmtPendants($pendantList) {
        if (empty($pendantList)) {
            return array();
        }

        foreach ($pendantList as &$item) {
            // 因为jimuyu的挂件是八端统一，所以可以只返回1个
            $item['jump_url'] = $item['effect_scene_config'][0]['jump_url'];
            $item['jump_type'] = $item['effect_scene_config'][0]['jump_type'];
            unset($item['priority']);
        }

        return $pendantList;
    }

    /**
     * 获取用户配置的挂件
     *
     * @param $uid
     * @param int $returnExpired 是否返回已过期挂件，默认或0不返回，1返回
     * @return array
     */
    private function getUserPendants($uid, $returnExpired) {
        $strPath  = '/liveamis/anchor/manage';
        $arrInput = array(
            'method' => 'pendantConfigGetByUid',
            'user_id' => $uid,
            'return_expired' => $returnExpired,
        );
        $arrResp = Util_LiveamisProxy::call($strPath, $arrInput);
        if (Alalib_Conf_Error::ERR_SUCCESS !== $arrResp['errno']) {
            $arrLog = array(
                'arrInput' => $arrInput,
                'arrResp'  => $arrResp,
            );
            Bingo_Log::warning( 'call pendantConfigGetByUid fail, arrLog=' . json_encode($arrLog));
            return array();
        }
        if (empty($arrResp['data'])) {
            $arrLog = array(
                'arrInput' => $arrInput,
                'arrResp'  => $arrResp,
            );
            Bingo_Log::warning( 'user pendantConfigGetByUid is empty, arrLog=' . json_encode($arrLog));
            return array();
        }

        return $arrResp['data'];
    }

}
