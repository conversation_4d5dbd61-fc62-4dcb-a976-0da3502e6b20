<?php

/***************************************************************************
 *
 * 设置挂件接口
 * http://wiki.baidu.com/pages/viewpage.action?pageId=1123571681#id-%E5%9F%BA%E6%9C%A8%E9%B1%BC%E5%AF%B9%E6%8E%A5server%E6%8E%A5%E5%8F%A3%E6%96%87%E6%A1%A3-%E6%8C%82%E4%BB%B6%E8%AE%BE%E7%BD%AE
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/
class setPendantAction extends Alalib_Action_BaseAction
{

    // 针对不同的tpl提供不同的定制化设置
    private static $_allowTpl = array(
        'jimuyu',
    );

    /**
     * 入参检查
     *
     * @param
     *            null
     * @return {Array}
     *
     */
    public function _getPrivateInfo()
    {
        $arrPrivateInfo['ala_id'] = intval($this->_getInput('ala_id', ''));
        $arrPrivateInfo['user_id'] = intval($this->_getInput('user_id', ''));
        $arrPrivateInfo['tpl'] = strval($this->_getInput('tpl', ''));
        $arrPrivateInfo['ak'] = strval($this->_getInput('ak', ''));
        $arrPrivateInfo['sign'] = strval($this->_getInput('sign', ''));

        // 挂件配置参数
        $arrPrivateInfo['pendant_id'] = intval($this->_getInput('pendant_id', ''));
        $arrPrivateInfo['pendant_name'] = strval($this->_getInput('pendant_name', ''));
        // 参数范围：1主挂件，2副挂件
        $arrPrivateInfo['pos'] = intval($this->_getInput('pos', ''));
        // 挂件图片url，挂件url,静态挂件为图片url,H5挂件为h5_url,屏蔽挂件为空
        $arrPrivateInfo['pendant_url'] = strval($this->_getInput('pendant_url', ''));
        // 八端统一跳转url
        $arrPrivateInfo['jump_url'] = strval($this->_getInput('jump_url', ''));
        // 八端统一跳转类型：1全屏h5；2半屏h5；3scheme
        $arrPrivateInfo['jump_type'] = intval($this->_getInput('jump_type', ''));
        // 挂件生效开始时间，unix时间戳
        $arrPrivateInfo['begin_time'] = intval($this->_getInput('begin_time', ''));
        // 挂件生效结束时间，unix时间戳
        $arrPrivateInfo['end_time'] = intval($this->_getInput('end_time', ''));

        // 内网接口
        $this->_objRequest->addStrategy('check_sign', false);
        $arrPrivateInfo['check_login'] = false;
        $arrPrivateInfo['need_login'] = false;
        $arrPrivateInfo['no_stlog'] = true;
        // 默认参数
        switch ($arrPrivateInfo['tpl']) {
            case 'jimuyu': //基木鱼用的
                $arrPrivateInfo['pendant_width'] = 60;
                $arrPrivateInfo['pendant_height'] = 60;
                $arrPrivateInfo['priority'] = 1;
                $arrPrivateInfo['pendant_type'] = 1;
                // 在mis后台展示的操作人
                $arrPrivateInfo['operator'] = 'b_jimuyu';
                break;
            default:
                $strLog = __CLASS__ . "::" . __FUNCTION__ . " tpl is invalid";
                Bingo_Log::warning($strLog);
                $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, "", "tpl is invalid");
                return false;
        }

        return $arrPrivateInfo;
    }

    /**
     * 入参检查
     *
     * @param
     *            null
     * @return {Bool}
     *
     */
    public function _checkPrivate()
    {
        $userId = $this->_objRequest->getPrivateAttr('user_id');
        $alaId = $this->_objRequest->getPrivateAttr('ala_id');
        if (empty($userId)) {
            // 传了ala_id不传uid,则使用ala_id解出uid
            if (empty($alaId)) {
                Bingo_Log::warning("ala_id is empty");
                $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR);
                return false;
            } else {
                // 通过alaid获取uid
                $arrInput = array($alaId);
                $userId = Service_User_User::getUidsByAlaids($arrInput);
                if (empty($userId) || !isset($userId[0])) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . "alaid is invalid,  call Service_User_User::getUidsByAlaids fail. input:[" . serialize($arrInput) . "]; output:[" . serialize($userId) . "]";
                    Bingo_Log::warning($strLog);
                    $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, "", "alaid is invalid");
                    return false;
                }
                $userId = $userId[0];
                $this->_objRequest->addPrivateAttr('user_id', $userId);
            }
        }

        $pendantName = $this->_objRequest->getPrivateAttr('pendant_name');
        if (empty($pendantName)) {
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, "", "pendant_name is required");
            return false;
        }
        $pendantNameLen = mb_strlen($pendantName);
        if ($pendantNameLen > 60) {
            Bingo_Log::warning("len[{$pendantNameLen}] of pendant_name[{$pendantName}] can't more than 60 ");
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, "", "len of pendant_name can't more than 60 ");
            return false;
        }

        $pos = $this->_objRequest->getPrivateAttr('pos');
        if (empty($pos)) {
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, "", "pos is required");
            return false;
        }
        // 参数范围：1主挂件，2副挂件，3屏蔽挂件
        if (!in_array($pos, array(1,2,3))) {
            Bingo_Log::warning("pos[{$pos}] is out of range,uid={$userId}, alaid={$alaId} ");
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, "", "pos is out of range");
            return false;
        }

        $pendantUrl = $this->_objRequest->getPrivateAttr('pendant_url');
        if (empty($pendantUrl)) {
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, "", "pendant_url is required");
            return false;
        }
        $pendantUrlLen = mb_strlen($pendantUrl);
        // 挂件url
        if ($pendantUrlLen > 255) {
            Bingo_Log::warning("len[{$pendantUrlLen}] of pendant_url[{$pendantUrl}] can't more than 255 ,uid={$userId}, alaid={$alaId} ");
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, "", "len of pendant_url is out of range ");
            return false;
        }

        // 八端统一跳转url
        $jumpUrl = $this->_objRequest->getPrivateAttr('jump_url');
        if (empty($jumpUrl)) {
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, "", "jump_url is required");
            return false;
        }
        $jumpUrlLen = mb_strlen($jumpUrl);
        if ($jumpUrlLen > 500) {
            Bingo_Log::warning("len[{$jumpUrlLen}] of pendant_url[{$jumpUrl}] can't more than 500 ,uid={$userId}, alaid={$alaId} ");
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, "", "len of jump_url is out of range ");
            return false;
        }
        // 八端统一跳转类型：1全屏h5；2半屏h5；3scheme
        $jumpType = $this->_objRequest->getPrivateAttr('jump_type');
        if (!in_array($jumpType, array(1,2,3))) {
            Bingo_Log::warning(" jump_type[{$jumpType}] is out of range ,uid={$userId}, alaid={$alaId} ");
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, "", "jump_type is out of range");
            return false;
        }
        $beginTime = $this->_objRequest->getPrivateAttr('begin_time');
        if (empty($beginTime)) {
            Bingo_Log::warning("begin_time[{$beginTime}] is empty,uid={$userId}, alaid={$alaId} ");
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, "", "begin_time is invalid");
            return false;
        }
        $endTime = $this->_objRequest->getPrivateAttr('end_time');
        if (empty($endTime)) {
            Bingo_Log::warning("end_time[{$beginTime}] is empty,uid={$userId}, alaid={$alaId} ");
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, "", "end_time is invalid");
            return false;
        }
        if ($beginTime >= $endTime) {
            Bingo_Log::warning("end_time[{$endTime}] must greater than begin_time[{$beginTime}] ,uid={$userId}, alaid={$alaId} ");
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, "", "begin_time is invalid");
            return false;
        }

        // sign
        $arrParam = array_merge(Bingo_Http_Request::getGetAll(), Bingo_Http_Request::getPostAll());
        if (empty($arrParam['ak']) || empty($arrParam['sign'])) {
            Bingo_Log::warning("sign or ak is invalid ,  param=".json_encode($arrParam));
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        
        $bolSign = Alalib_Util_Sdk::checkSign($arrParam['ak'], $arrParam['sign'], $arrParam);
        if ($bolSign == false) {
            Bingo_Log::warning("sign check fail ,  param=".json_encode($arrParam));
            $this->errRet(Tieba_Errcode::ERR_INVALID_SIGN);
            return false;
        }
        
        return true;
    }


    /**
     *
     * @return bool
     *
     */
    public function _execute()
    {
//        if(true) {
//            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR);
//            return false;
//        }
        $pendantId = (int)$this->_objRequest->getPrivateAttr('pendant_id');
        $userId = (int)$this->_objRequest->getPrivateAttr('user_id');
        $beginTime = (int)$this->_objRequest->getPrivateAttr('begin_time');
        $endTime = (int)$this->_objRequest->getPrivateAttr('end_time');
        $pos = (int)$this->_objRequest->getPrivateAttr('pos');
        $tpl = $this->_objRequest->getPrivateAttr('tpl');

        // 检查起止时间跟原有挂件配置是否有冲突
        $conflictId = $this->checkConflict($userId, $beginTime, $endTime, $pos, $pendantId, $userPendant);
        if ($conflictId > 0) {
            Bingo_Log::warning("pendant conflict with old id:{$conflictId}; uid={$userId}, beginTime={$beginTime}, endTime={$endTime}, pos={$pos}, pendantId={$pendantId}");
            $this->errRet(Tieba_Errcode::ERR_BUSI_REPEAT_SUBMIT, "", "pendant conflict with id:{$conflictId}");
            return false;
        }
        if ($pendantId > 0) {
            if ($userPendant['user_id'] != $userId) {
                // 只能改user_id它自己的uid
                Bingo_Log::warning("can't update pendant; uid of pendant is {$userPendant['user_id']}, but update uid={$userId}, beginTime={$beginTime}, endTime={$endTime}, pos={$pos}, pendantId={$pendantId}");
                $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, "", "user_id doesn't match");
                return false;
            }
            // 修改挂件
            if (!$this->updatePendant()) {
                $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                return false;
            }
        } else {
            // 新增挂件
            $pendantId = $this->addPendant();
            if (empty($pendantId)) {
                $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                return false;
            }
        }
        $data = array(
            'data' => array('pendant_id' => $pendantId)
        );
        $this->errRet(Tieba_Errcode::ERR_SUCCESS, $data);
        return true;
    }


    private function addPendant() {
        $userId = $this->_objRequest->getPrivateAttr('user_id');
        $jumpType = $this->_objRequest->getPrivateAttr('jump_type');
        $jumpUrl = $this->_objRequest->getPrivateAttr('jump_url');
        $strPath  = '/liveamis/anchor/manage';
        $arrInput = array(
            'method' => 'pendantConfigAdd',
            'user_ids' => $userId,
            'begin_time' => $this->_objRequest->getPrivateAttr('begin_time'),
            'end_time' => $this->_objRequest->getPrivateAttr('end_time'),
            'pendant_type' => $this->_objRequest->getPrivateAttr('pendant_type'),
            'pendant_url' => $this->_objRequest->getPrivateAttr('pendant_url'),
            'pendant_height' => $this->_objRequest->getPrivateAttr('pendant_height'),
            'pendant_width' => $this->_objRequest->getPrivateAttr('pendant_width'),
            'pendant_name' => $this->_objRequest->getPrivateAttr('pendant_name'),
            'pos' => $this->_objRequest->getPrivateAttr('pos'),
            'priority' => $this->_objRequest->getPrivateAttr('priority'),
            'operator' => $this->_objRequest->getPrivateAttr('operator'),
            'effect_scene_config' => $this->getCommonEffectSceneConfig($jumpType, $jumpUrl),
        );
        $arrResp = Util_LiveamisProxy::call($strPath, $arrInput);
        if (Alalib_Conf_Error::ERR_SUCCESS !== $arrResp['errno'] || empty($arrResp['data'])) {
            $arrLog = array(
                'arrInput' => $arrInput,
                'arrResp'  => $arrResp,
            );
            Bingo_Log::warning( 'call pendantConfigEdit fail, arrLog=' . json_encode($arrLog));
            return false;
        }

        return $arrResp['data'];
    }

    /**
     * @return bool
     */
    private function updatePendant() {
        $pendantId = $this->_objRequest->getPrivateAttr('pendant_id');
        $userId = $this->_objRequest->getPrivateAttr('user_id');
        $jumpType = $this->_objRequest->getPrivateAttr('jump_type');
        $jumpUrl = $this->_objRequest->getPrivateAttr('jump_url');
        $strPath  = '/liveamis/anchor/manage';
        $arrInput = array(
            'method' => 'pendantConfigEdit',
            'id' => $pendantId,
            'user_id' => $userId,
            'begin_time' => $this->_objRequest->getPrivateAttr('begin_time'),
            'end_time' => $this->_objRequest->getPrivateAttr('end_time'),
            'pendant_url' => $this->_objRequest->getPrivateAttr('pendant_url'),
            'pendant_type' => $this->_objRequest->getPrivateAttr('pendant_type'),
            'pendant_height' => $this->_objRequest->getPrivateAttr('pendant_height'),
            'pendant_width' => $this->_objRequest->getPrivateAttr('pendant_width'),
            'pendant_name' => $this->_objRequest->getPrivateAttr('pendant_name'),
            'pos' => $this->_objRequest->getPrivateAttr('pos'),
            'priority' => $this->_objRequest->getPrivateAttr('priority'),
            'operator' => $this->_objRequest->getPrivateAttr('operator'),
            'effect_scene_config' => $this->getCommonEffectSceneConfig($jumpType, $jumpUrl),
        );
        $arrResp = Util_LiveamisProxy::call($strPath, $arrInput);
        if (Alalib_Conf_Error::ERR_SUCCESS !== $arrResp['errno']) {
            $arrLog = array(
                'arrInput' => $arrInput,
                'arrResp'  => $arrResp,
            );
            Bingo_Log::warning( 'call pendantConfigEdit fail, arrLog=' . json_encode($arrLog));
            return false;
        }

        return true;
    }

    /**
     * 获取八端统一跳转配置
     *
     * @param int $jumpType
     * @param string $jumpUrl
     * @return array
     */
    private function getCommonEffectSceneConfig($jumpType, $jumpUrl) {
        $conf = array();
        for ($i = 1; $i <= 8; $i++) {
            $conf[] = array(
                'effect_scene' => $i,
                'jump_type' => $jumpType,
                'jump_url' => $jumpUrl,
            );
        }

        return $conf;
    }

    /**
     * @param $uid
     * @param $newBeginTime
     * @param $newEndTime
     * @param int $pendantId 挂件id，如果是需要更新指定id，查重需要排除本身；如果是需要创建，这个值为0
     * @param array $pendantConf 顺便返回$pendantId对应的配置
     * @return int 返回冲突的挂件id
     */
    private function checkConflict($uid, $newBeginTime, $newEndTime, $pos, $pendantId = 0, &$pendantConf = array()) {
        // 查询当前用户的挂件
        $userPendants = $this->getUserPendants($uid);
        if (empty($userPendants)) {
            return 0;
        }
        $allowConflictNum = 1;
        $curConflictNum = 0;
        foreach ($userPendants as $userPendant) {
            // 更新挂件时，需要排除待更新挂件id
            if ($userPendant['id'] == $pendantId) {
                $pendantConf = $userPendant;
                continue;
            }
            // 只对同类型的挂件检查冲突
            if ($userPendant['pos'] != $pos) {
                continue;
            }
            // 删除就不检查了，避免已经删除了还不能加入同时间段的
            if ($userPendant['deleted'] == 1) {
                continue;
            }
            // 排除不冲突的情况
            if ($newBeginTime < $userPendant['begin_time'] && $newEndTime < $userPendant['begin_time']) {
                continue;
            } elseif ($newBeginTime > $userPendant['end_time'] && $newEndTime > $userPendant['end_time']) {
                continue;
            } else {
                // 挂件时间和既有挂件有冲突
                Bingo_Log::warning("new pendant conflict with pendant_id:{$userPendant['id']},  old begin_time:{$userPendant['begin_time']}, old end_time:{$userPendant['end_time']}; new begin_time:{$newBeginTime}, new end_time:{$newEndTime}");
                $curConflictNum ++;
            }

            // 同一时间内冲突的挂件数量达到限制了，返回一个冲突挂件id
            if ($curConflictNum >= $allowConflictNum) {
                return $userPendant['id'];
            }
        }

        return 0;
    }

    /**
     * 获取用户原有的挂件
     *
     * @param $uid
     * @return array
     */
    private function getUserPendants($uid) {
        $strPath  = '/liveamis/anchor/manage';
        $arrInput = array(
            'method' => 'pendantConfigList',
            'user_id' => $uid,
        );
        $arrResp = Util_LiveamisProxy::call($strPath, $arrInput);
        if (Alalib_Conf_Error::ERR_SUCCESS !== $arrResp['errno']) {
            $arrLog = array(
                'arrInput' => $arrInput,
                'arrResp'  => $arrResp,
            );
            Bingo_Log::warning( 'call pendantConfigList fail, arrLog=' . json_encode($arrLog));
            return array();
        }
        if (empty($arrResp['data']['rows'])) {
            $arrLog = array(
                'arrInput' => $arrInput,
                'arrResp'  => $arrResp,
            );
            Bingo_Log::warning( 'user pendantConfigList is empty, arrLog=' . json_encode($arrLog));
            return array();
        }

        return $arrResp['data']['rows'];
    }

}
