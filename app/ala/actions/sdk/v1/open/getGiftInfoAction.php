<?php
/**
 * Created by PhpStorm.
 * User: lixiaoxu03
 * Date: 2019/3/26
 * Time: 6:59 PM
 */

class getGiftInfoAction extends Alalib_Action_BaseAction
{

    public function _getPrivateInfo() {

        $arrPrivateInfo['gift_id'] = intval($this->_getInput('gift_id', ''));
        $this->_objRequest->addStrategy('check_sign', false);
        return $arrPrivateInfo;
    }
    public function _checkPrivate() {
        $intGiftId = $this->_objRequest->getPrivateAttr('gift_id');
        if(empty($intGiftId)){
            $strLog = "input params is empty";
            Bingo_Log::warning($strLog);
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }

        return true;
    }

    public function _execute()
    {
        $gift_id = $this->_objRequest->getPrivateAttr("gift_id");
        $arrGetParam = array(
            'gift_id' => array($gift_id),
        );
        $arrGiftInfo = Tieba_Service::call('present', 'getGiftById', $arrGetParam, null, null, 'post', 'php', 'utf-8');
        if ($arrGiftInfo === false || $arrGiftInfo['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("add gift failed![input]:" . serialize($arrGetParam) . "[output]" . serialize($arrGiftInfo));
        }
        return $this->errRet(Alalib_Conf_Error::ERR_SUCCESS, $arrGiftInfo);

    }
}
