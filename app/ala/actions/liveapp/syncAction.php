<?php
/**
 * Created by PhpStorm.
 * User: sunzhexuan
 * Date: 2019/1/3
 * Time: 上午10:27
 * 独立开播端sync配置
 */

class syncAction extends Alalib_Action_BaseAction
{
    private $arrConfig = array();
    private $arrLiveActivity = array();
    private $arrLiveActivityNew = array();
    private $arrLcsStrategy = array();
    private $strConfigVersion = '';
    private $arrSwitch = array();
    private $arrSessionLine = array();
    private $_bolNewClient = false;
    private $_intClientId = 0;
    private $_arrLiveHeadLine = array();//开播文案
    private static $intUserId = 0;
    private static $strSubappType = '';

    const FSG_CREDIT_URL = 'http://credit';
    const REDIS_LIVE_HEADLINE = '';

    /**
     * @return mixed
     */
    public function _getPrivateInfo()
    {
        $arrPrivateInfo['scr_h'] = intval(Bingo_Http_Request::get('scr_h', 0));
        $arrPrivateInfo['scr_w'] = intval(Bingo_Http_Request::get('scr_w', 0));
        $arrPrivateInfo['_phone_imei'] = Bingo_Http_Request::getNoXssSafe('_phone_imei', '');
        $arrPrivateInfo['_phone_newimei'] = Bingo_Http_Request::getNoXssSafe('_phone_newimei', '');
        $arrPrivateInfo['_os_version'] = Bingo_Http_Request::getNoXssSafe('_os_version', '');
        $arrPrivateInfo['_phone_screen'] = Bingo_Http_Request::getNoXssSafe('_phone_screen', '');
        $arrPrivateInfo['package'] = Bingo_Http_Request::getNoXssSafe('package', '');
        $arrPrivateInfo['versioncode'] = Bingo_Http_Request::getNoXssSafe('versioncode', '');
        $arrPrivateInfo['signmd5'] = Bingo_Http_Request::getNoXssSafe('signmd5', '');
        $arrPrivateInfo['md5'] = Bingo_Http_Request::getNoXssSafe('md5', '');
        $arrPrivateInfo['always_update'] = Bingo_Http_Request::getNoXssSafe('always_update', 0);
        $arrPrivateInfo['tieba_idfa'] = Bingo_Http_Request::getNoXssSafe('tieba_idfa', '');
        $arrPrivateInfo['mac'] = Bingo_Http_Request::getNoXssSafe('mac', '');
        $arrPrivateInfo['app_active'] = Bingo_Http_Request::getNoXssSafe('app_active', '');
        $arrPrivateInfo['ispv'] = 0;
        $arrPrivateInfo['check_login'] = false;
        $arrPrivateInfo['need_login'] = true;
        if($arrPrivateInfo['scr_w'] === 0){
            list($intScrW, $intScrH) = explode(',', $arrPrivateInfo['_phone_screen']);
            $arrPrivateInfo['scr_h'] = $intScrH;
            $arrPrivateInfo['scr_w'] = $intScrW;
        }
        $this->_objRequest->setStrategy(array('check_nonu' => false));
        $this->_objRequest->addStrategy('check_sign', false);

        return $arrPrivateInfo;
    }

    /**
     * @return bool
     */
    public function _checkPrivate()
    {
        return true;
    }

    /**
     *
     */
    public function _execute()
    {
        self::$intUserId = $this->_objRequest->getCommonAttr('user_id');
        self::$strSubappType = $this->_objRequest->getCommonAttr('subapp_type');
        /**
         * 不同端特殊处理
         */
        //ios独有逻辑
        if(Molib_Client_Define::CLIENT_TYPE_IPHONE == $this->_objRequest->getCommonAttr('client_type')){
            $this->iosLogic();
        }
        //android独有逻辑
        if(Molib_Client_Define::CLIENT_TYPE_ANDROID == $this->_objRequest->getCommonAttr('client_type')){
            $this->androidLogic();
        }
        /**
         * 多端共有逻辑
         */
        $this->commonLogic();
        /**
         * 小版本特殊处理
         */
        $this->specialLogic();
        $this->setResponse();
        //记录log
        $this->stLog();
    }

    /**
     *
     */
    private function commonLogic()
    {
        //0、判断是否新用户
        $this->isNewClient();
        //1、加载配置
        $this->loadConfig();
        //2、同步客户端信息
        $this->syncClientInfo();
        //3、同步开关信息
        $this->syncSwitchInfo();
        $this->getServiceConf();
        $this->setIOsPatch();
        //接口lcs信息
        //$this->getLcsStrategy();
        //设置直播间H5活动信息
        $this->getLiveActivity();
        // 设置视频广场进入TAB策略
        $this->getSquareTabStrategy();

        // 直播间关闭新策略
        $this->getNewLiveCloseStrategy();

        // 开播分辨率配置
        $this->getOpenLiveConf();
    }

    /**
     * ios独有逻辑
     */
    private function getLiveActivity()
    {
        $arrInput = array(
            'anchor_id' => (int)self::$intUserId,
        );
        $arrOutput = Tieba_Service::call('ala', 'liveActivityGet', $arrInput);
        if(Tieba_Errcode::ERR_SUCCESS === $arrOutput['errno']){
            if(!empty($arrOutput['data'][0]['pic_url']) && !empty($arrOutput['data'][0]['jump_url'])){
                $this->arrLiveActivity['pic_url'] = $arrOutput['data'][0]['pic_url'];
                $this->arrLiveActivity['jump_url'] = $arrOutput['data'][0]['jump_url'];
            }

            //$this->arrLiveActivityNew   begin
            $arrMyTempLiveActivityNew = array();
            $arrTempIcons = $arrOutput['data'];
            foreach ($arrTempIcons as $myValue) {
                $intBeginTime = intval($myValue['begin_time']);
                $intEndTime = intval($myValue['end_time']);
                $intTimeNow = time();
                if($intBeginTime <= $intTimeNow && $intTimeNow <= $intEndTime) {
                    if (Molib_Client_Define::CLIENT_TYPE_IPHONE == $this->_objRequest->getCommonAttr('client_type') && 1 == intval($myValue['version_ios'])) {
                        $arrMyTempLiveActivityNew[] = $myValue;
                    }
                    else if( Molib_Client_Define::CLIENT_TYPE_ANDROID == $this->_objRequest->getCommonAttr('client_type') && 1 == intval($myValue['version_and'] ) ) {
                        $arrMyTempLiveActivityNew[] = $myValue;
                    }
                }
            }

            array_multisort(array_column($arrMyTempLiveActivityNew,'position'),SORT_ASC,$arrMyTempLiveActivityNew);
            //标记为新活动
            $intTimeDistance = 86400;    //1天内是新活动
            $boolHasNew = 0;
            $strSubappType = $this->_objRequest->getCommonAttr('subapp_type');
            $arrTempLiveActivityNew = array();
            $intMaxdAddTime = 0;
            $intMaxdAddTimeIndex = -1;
            foreach ($arrMyTempLiveActivityNew as $intIdx => $myTempValue) {
                if(Alalib_Conf_Sdk::SUBAPP_TIEBA == $strSubappType && isset($myTempValue['to_tieba']) && intval($myTempValue['to_tieba']) <= 0) {
                    Bingo_Log::warning("icon config need not to tieba");
                    continue;
                }
                if(Alalib_Conf_Sdk::SUBAPP_SHOUBAI == $strSubappType && intval($myTempValue['to_shoubai']) <= 0) {
                    Bingo_Log::warning("icon config need not to shoubai");
                    continue;
                }

                $intAddTime = intval($myTempValue['add_time']);
                if((time() - $intAddTime) <= $intTimeDistance) {
                    $boolHasNew = 1;
                    if($intAddTime > $intMaxdAddTime) {
                        $intMaxdAddTime = $intAddTime;
                        $intMaxdAddTimeIndex = intval($myTempValue['sequence_id']);
                    }
                }
                else {
                    $myTempValue['is_new'] = 0;
                }

                $arrTempLiveActivityNew[] = $myTempValue;
            }
            $arrMyTempLiveActivityNew = $arrTempLiveActivityNew;

            if($boolHasNew) {
                foreach ($arrMyTempLiveActivityNew as $intIdx => &$myTempValue) {
                    if($intMaxdAddTimeIndex == intval($myTempValue['sequence_id'])) {
                        $myTempValue['is_new'] = 1;
                    }
                    else {
                        $myTempValue['is_new'] = 0;
                    }
                }
            }


            // 特殊处理有钱花运营活动
            $arrCreditConf = null;
            foreach ($arrMyTempLiveActivityNew as $intIdx => &$myTempValue) {
                if ($myTempValue['jump_url'] == self::FSG_CREDIT_URL){
                    $arrCreditConf = $myTempValue;
                    $arrCreditConf['jump_type'] = 1;  // 跳转类型，1表示现金贷入口
                    $arrCreditConf['jump_data'] = '';  // 跳转数据
                    unset($arrMyTempLiveActivityNew[$intIdx]);
                }
            }
            $arrMyTempLiveActivityNew = array_values($arrMyTempLiveActivityNew);

            $this->arrLiveActivityNew = array(
                'has_new' => $boolHasNew,
                'credit_info' => $arrCreditConf,
                'activity_info' => $arrMyTempLiveActivityNew,
            );
            //$this->arrLiveActivityNew   end

            return;
        }
        Bingo_Log::warning('ala liveActivityGet err! output : '.serialize($arrOutput));

        return;
    }

    /**
     * ios独有逻辑
     */
    private function iosLogic()
    {
    }

    /**
     * android独有逻辑
     */
    private function androidLogic()
    {
    }

    /**
     *
     */
    private function specialLogic()
    {
    }

    /**
     *
     */
    private function loadConfig()
    {
        $this->strConfigVersion = md5(json_encode($this->arrConfig));
    }

    /**
     *
     */
    private function syncClientInfo()
    {
        $this->arrConfig = Alalib_Conf_Client::$arrClientSyncConfig;
        //默认美颜指数
        /* $this->arrConfig["beauty_rate"] = array(
            "value"=> 0.6,//默认 0.6
            "text"  => "beauty_rate",
        ); */
        //推荐刷新时间间隔 ios rd@lixiurui
        $this->arrConfig["recommend_refresh"] = array("value" => 60,//默认60秒
            "text" => "recommend_refresh",);
        //推流丢包和码率控制 ios rd@fengzhiping
        $this->arrConfig["stream_config"] = array("thi" => 0.006, // 升高码率配置阈值
            "thd" => 0.3, // 降低码率配置阈值
            "thi_count" => 3,// 5秒内连续小于thi检测次数
            "configs" => array(array("frame" => 24,
                "bitrate" => 614400,// 600*1024 ,
            ),
                array("frame" => 24,
                    "bitrate" => 512000,// 500 *1024 ,
                ),
                array("frame" => 20,
                    "bitrate" => 409600,// 400*1024 ,
                ),
                array("frame" => 20,
                    "bitrate" => 307200,// 300 *1024 ,
                ),
                array("frame" => 15,
                    "bitrate" => 256000,// 250 *1024 ,
                ),),);
    }

    /**
     *
     */
    private function syncSwitchInfo()
    {
        $this->arrSwitch = Alalib_Conf_Client::$arrClientSyncSwitch;
        $this->arrSwitch["withdraw"] = array("switch" => 1,// 1开启，0关闭
            "text" => "提现功能暂未开放",);
        $this->arrSwitch["map_around_map"] = array("switch" => 1,// 默认开启
            "text" => "地图功能暂未开放",);
        //启动是否进入直播间
        $this->arrSwitch["enter_live"] = array("switch" => 0,// 默认开启
            "text" => "启动是否进入直播间",);
        //开启是否同步到贴吧功能
        $this->arrSwitch["sync_to_forum"] = array("switch" => 1,// 默认开启
            "text" => "是否同步到贴吧功能",);
        // 是否开启视频流异常上报功能
        $this->arrSwitch["stream_error_log"] = array("switch" => 1, // 默认开启
            "text" => "是否开启视频流异常上报功能",);
        // 直播同步到其它吧开关是否开启
        $this->arrSwitch["to_tb_some"] = array("switch" => 0, // 默认关闭
            "text" => "是否开启直播同步到其它吧开关",);
        // frs视频自动播放是否开启
        $this->arrSwitch["auto_play_video_frs"] = array("switch" => 1, // 默认开启
            "text" => "是否自动播放视频",);
        //首页视频自动播放是否开启
        $this->arrSwitch["auto_play_video_homepage"] = array("switch" => 1, // 默认开启
            "text" => "主页是否自动播放视频",);
        $this->arrSwitch["show_chushou_water_mark"]  = array("switch" => "0", // 1为用贴吧覆盖触手水印
            "text" => "关闭",);
        // 客态直播间多码率开关是否打开
        $this->arrSwitch["audience_transcode"] = $this->processTranscodeSwitch();

        // 主播关注提醒
        $this->arrSwitch["anchor_follow_tip"] = array("switch" => 0, "text" => "主播关注提醒"); // 默认关闭
    }

    /**
     *
     */
    private function setResponse()
    {
        $this->_objResponse->addOutData('client_ip', $this->_objRequest->getCommonAttr('ip_str'));
        $this->_objResponse->addOutData('config', $this->arrConfig);
        $this->_objResponse->addOutData('switch', $this->arrSwitch);
        $this->_objResponse->addOutData('lcs_strategy', $this->arrLcsStrategy);
        $this->_objResponse->addOutData('config_version', $this->strConfigVersion);
        $this->_objResponse->addOutData('client_id', $this->_intClientId);
        $this->_objResponse->addOutData('live_activity', $this->arrLiveActivity);
        $this->_objResponse->addOutData('live_activity_new', $this->arrLiveActivityNew);
        $this->_objResponse->addOutData('live_headline',$this->_arrLiveHeadLine);
    }

    /**
     *
     */
    private function stLog()
    {
        /*  $this->_objResponse->addLog('msg_status', $this->_objRequest->getPrivateAttr('_msg_status'));
         $this->_objResponse->addLog('new_client', intval($this->_bolNewClient));
         $this->_objResponse->addLog('_bduss', $_COOKIE['BDUSS']); */
        $appActive = $this->_objRequest->getPrivateAttr("app_active", "");
        if($appActive === ""){
            $appActive = 1;
        }
        if(!empty($appActive)){
            Bingo_Log::pushNotice("app_active", $appActive);
        }
    }

    /**
     * 获取service信息
     *
     * @param null
     *
     * @return null
     * */
    private function getServiceConf()
    {
        $arrConfigResult = array();
        // muti call start
        $strMutiCallKey                                      = __CLASS__ . "::" . __FUNCTION__ . "";
        $objMutiCall                                         = new Tieba_Multi($strMutiCallKey);
        $arrMutiCallParam                                    = array();
        $arrMutiCallParam["alamis"]["getAnnouncement"]       = array();
        $arrMutiCallParam["alamis"]["getMisConf"]            = array();
        $arrMutiCallParam['ala']['userGetSessionLine']       = array(
            'user_id' => self::$intUserId
        );
        $arrMutiCallParam['ala']['getCloseLiveTextToRedis']  = array();
        $arrMutiCallParam['ala']['getLiveChallengeActivity'] = array();
        $arrMutiCallParam['ala']['quickSendImInfo']          = array();
        //开播文案
        $arrMutiCallParam['ala']['getLiveHeadLine']          = array();
        //运营活动
        $arrMutiCallParam['ala']['getLiveAppValidYunyingInfo'] = array();

        foreach ($arrMutiCallParam as $serviceName => $serviceMethod) {
            foreach ($serviceMethod as $methodName => $methodParam) {
                $arrInput = array("serviceName" => $serviceName,
                    "method" => $methodName,
                    'ie' => 'utf-8',
                    "input" => $methodParam,);
                $objMutiCall->register($methodName, new Tieba_Service($serviceName), $arrInput);
            }
        }
        Bingo_Timer::start('call_core_service');
        $objMutiCall->call();
        Bingo_Timer::end('call_core_service');
        $arrMutiCallResults = array();
        foreach ($arrMutiCallParam as $serviceName => $serviceMethod) {
            $arrMutiCallResults[$serviceName] = array();
            foreach ($serviceMethod as $methodName => $methodParam) {
                $arrOutput = $objMutiCall->getResult($methodName);
                if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $serviceName $methodName fail. input:[" . serialize($methodParam) . "]  output:[" . serialize($arrOutput) . "]";
                    Bingo_Log::error($strLog);
                }
                $arrMutiCallResults[$serviceName][$methodName] = $arrOutput['data'];
            }
        }
        $arrTmp = $arrMutiCallResults["ala"]["userGetSessionLine"];
        $this->arrSessionLine['session_default'] = 1; // 默认主线
        if ($arrTmp['force'] == 1) {
            $this->arrSessionLine['session_default'] = $arrTmp['default_line'];
        } else if ($arrTmp['force'] == 2) {
            if (isset($arrTmp['last_line'])){
                $this->arrSessionLine['session_default'] = $arrTmp['last_line'];
            } else {
                $this->arrSessionLine['session_default'] = $arrTmp['default_line'];
            }
        }

        // muti call end
        $arrConfigResult["live_notice"] = $arrMutiCallResults["alamis"]["getAnnouncement"]["content"];
        if (Alalib_Util_Version::compare($this->_objRequest->getCommonAttr('client_version'), '8.9.0') > 0){
        	if(self::checkNoSendMsg()==true){
		        $arrConfigResult["live_notice"] = "【官方提醒】百度提倡绿色直播，网警24小时巡查，封面和直播内容涉及低俗、暴力等作封号处理。官方交流QQ群:108783。因弹幕系统升级，从即日起至6月6日将暂时关闭弹幕功能，由此给您带来的不便，敬请谅解。";
	        }else{
		        $arrConfigResult["live_notice"] = "【官方提醒】百度提倡绿色直播，网警24小时巡查，封面和直播内容涉及低俗、暴力等作封号处理。官方交流QQ群:108783。";
	        }
        }
        if (Alalib_Conf_Sdk::SUBAPP_SHOUBAI == self::$strSubappType){
            $arrConfigResult["live_notice"] = "官方提醒：提倡绿色直播，网管24小时巡查，禁止发布反动、色情、低俗等直播内容。";
        } else if (Alalib_Conf_Sdk::SUBAPP_HAOKAN == self::$strSubappType){
            $arrConfigResult["live_notice"] = "官方提醒：提倡绿色直播，网管24小时巡查，禁止发布反动、色情、低俗等直播内容。";
        } else if (Alalib_Conf_Sdk::SUBAPP_QUANMIN == self::$strSubappType){
            $arrConfigResult["live_notice"] = "官方提醒：提倡绿色直播，网管24小时巡查，禁止发布反动、色情、低俗等直播内容。";
        }

        // 是否开启直播间必须要验证通知 start
        //$arrConfigResult["verify_content"] = "实名认证让你成为一名合格的主播并可以获得更好的用户体验，快去个人中心开启你的认证之旅吧";
        // 是否开启直播间必须要验证通知 end
        $arrMisConfig = $arrMutiCallResults["alamis"]["getMisConf"];
        $arrSwitch = array();
        foreach($arrMisConfig as $item){
            if($item["type"] == 1){
                $arrConfigResult[$item["field"]] = $item["value"];
            }else{
                if($item["type"] == 2){
                    $arrSwitch[$item["field"]] = $item["value"];
                }
            }
        }

        $arrConfigResult['imNotice'][0] = str_replace('贴吧', '', $arrConfigResult['imNotice'][0]);
        $arrConfigResult['imNotice'][1] = 'https://tieba.baidu.com/tb/kaibozhushou_mobile_fuwuxieyi.html';

        $arrSwitch['live_challenge_activity'] = $arrMutiCallResults["ala"]["getLiveChallengeActivity"];
        if (Alalib_Conf_Sdk::SUBAPP_HAOKAN == self::$strSubappType || Alalib_Conf_Sdk::SUBAPP_QUANMIN == self::$strSubappType || Alalib_Conf_Sdk::SUBAPP_SHOUBAI == self::$strSubappType || Alalib_Conf_Sdk::SUBAPP_TIEBA == self::$strSubappType ){
            $arrSwitch['new_gift_t_dou_strategy'] =
                array("switch" => 0, // 好看全民关闭
                    "text" => "礼物T豆价格是否以K为单位展示",);
        }
        if($arrConfigResult["stream_config_8_3"]){
            if(Molib_Client_Define::CLIENT_TYPE_IPHONE == $this->_objRequest->getCommonAttr('client_type') && Molib_Util_Version::compare("8.3.0", $this->_objRequest->getCommonAttr('client_version')) >= 0){
                $arrConfigResult["stream_config"] = $arrConfigResult["stream_config_8_3"];
            }
            unset($arrConfigResult["stream_config_8_3"]);
        }

        if(!empty($arrConfigResult["stream_config_android_8_7"])){
            if(Molib_Client_Define::CLIENT_TYPE_ANDROID == $this->_objRequest->getCommonAttr('client_type') && Molib_Util_Version::compare("8.7.0", $this->_objRequest->getCommonAttr('client_version')) >= 0){
                $arrConfigResult["stream_config"] = $arrConfigResult["stream_config_android_8_7"];
            }
            unset($arrConfigResult["stream_config_android_8_7"]);
        }

        if(!empty($arrConfigResult["stream_level_high_android_8_7"])){
            if(Molib_Client_Define::CLIENT_TYPE_ANDROID == $this->_objRequest->getCommonAttr('client_type') && Molib_Util_Version::compare("8.7.0", $this->_objRequest->getCommonAttr('client_version')) >= 0){
                $arrConfigResult["stream_level_high"] = $arrConfigResult["stream_level_high_android_8_7"];
            }
            unset($arrConfigResult["stream_level_high_android_8_7"]);
        }
        //运营活动（主界面）
        $arrConfigResult['bd_live_activity']['main_page'] = $arrMutiCallResults["ala"]["getLiveAppValidYunyingInfo"];
        //开播文案
        $this->_arrLiveHeadLine = $arrMutiCallResults["ala"]["getLiveHeadLine"];

        $arrConfigResult['session_backup']['session_default'] = intval($this->arrSessionLine['session_default']);
        $this->arrConfig['close_live_room_text']=$arrMutiCallResults['ala']['getCloseLiveTextToRedis'];
        $this->arrConfig['im_info']=$arrMutiCallResults['ala']['quickSendImInfo'];
        $this->arrConfig = array_merge($this->arrConfig, $arrConfigResult);
        $this->arrSwitch = array_merge($this->arrSwitch, $arrSwitch);
    }

    /**
     *
     */
    private function setIOsPatch()
    {
        if(Molib_Client_Define::CLIENT_TYPE_IPHONE == $this->_objRequest->getCommonAttr('client_type')){
            $version = $this->_objRequest->getCommonAttr('client_version');
            $osversion = $this->_objRequest->getCommonAttr('os_version');
            $channel = $this->_objRequest->getCommonAttr('from');
            $iosPatchConf = Bd_Conf::getConf("/app/client-core/alaiosluapatch");
            if(false == $iosPatchConf){
                Bingo_Log::warning("init iospatchconf fail.");
            }else{
                $version = str_replace('.', '_', $version);
                if(isset($iosPatchConf[$version])){//不区分渠道
                    $this->arrConfig['ios_patch'] = $iosPatchConf[$version];
                }else{//区分渠道
                    $this->arrConfig['ios_patch'] = $iosPatchConf[$version.'_'.$channel];
                }
            }
            if(isset($this->arrConfig['ios_patch'])){
                if(isset($this->arrConfig['ios_patch']['js_os_version'])){
                    $jsosversion = $this->arrConfig['ios_patch']['js_os_version'];
                    if(empty($jsosversion) || $jsosversion === "*"){
                        //do nothing
                    }else{
                        $arrJsOsVersion = explode(",", $jsosversion);
                        $arrClientOsVersion = explode(".", $osversion);
                        if(!in_array($arrClientOsVersion[0], $arrJsOsVersion)){
                            unset($this->arrConfig['ios_patch']);
                        }
                    }
                }
            }
        }
    }

    /**
     *
     */
    private function isNewClient()
    {
        $this->_intClientId = $this->_objRequest->getCommonAttr('client_id', 0);
        if($this->_intClientId == '0' || $this->_intClientId == ''){
            $this->_intClientId = 'alaclt_'.intval(microtime(true) * 1000)."_".rand() % 1000;
            $this->_bolNewClient = true;
            $this->_objRequest->addCommonAttr('is_new_user', 1);
        }
    }
	/**
	 * @brief  checkSendMsg
	 *
	 * <AUTHOR>
	 *
	 * @time   2019-05-31 10:58
	 * @return bool|mixed|multitype|string|null
	 */
	private static function checkNoSendMsg()
	{
		$ret = Tieba_Service::call('alaim', 'checkNeedOnlyToAuthorMask', array(), null, null, 'post', null, 'utf-8');
		if(!$ret || Tieba_Errcode::ERR_SUCCESS != $ret['errno']){
			Bingo_Log::warning(__CLASS__.'::'.__FUNCTION__.', get alaim getMaskWithoutAuthorMask failed, input: ['.serialize(array()).'], output: ['.serialize($ret)."].\n");
			$ret = false;
		}else{
			$ret = $ret['data'];
		}
		return $ret;
	}
    /**
     *  获取直播视频广场用户进入位置
     *  视频广场各页面交互（分流量控制）
     * 50%用户记录上一次离开视频tab时的页面，本次进入时，默认进入上一次离开页面
     * 50%用户可以控制默认进入页面（直播or推荐or动态）
     */
    private function getSquareTabStrategy()
    {
        // 获取第一次随机，判断是用户是进入上一次进入的位置，还是进入指定的页面
        $intLessThanNo  = intval($this->arrConfig['video_square_tab_strategy']['user_id_last_number_less_than']);
        //unset($this->arrConfig['video_square_tab_strategy']);
        $intLastNumber = intval(self::$intUserId) % 100;
        if(empty(self::$intUserId)) {   //未登录用户
            $intLastNumber = rand(0,99);
        }
        if($intLastNumber >= $intLessThanNo){
            $this->arrConfig['square_tab_strategy'] = 3; // 策略为进入用户上一次进入的页面
        }
        else {
//			$intUserId = $this->_objRequest->getCommonAttr("user_id");
//			if($intUserId > 0){
//				$subTabNum = 4;
//			}else{
//				$subTabNum = 3;
//			}
//			// 2:表示直播TAB  3:推荐TAB  4: 动态TAB
//			$intRate2 = rand(2, $subTabNum);

            $this->arrConfig['square_tab_strategy'] = 2;  //直接进入直播tab
        }
    }

    /**
     *
     */
    private function getNewLiveCloseStrategy()  {
        $intLessThanNo  = intval($this->arrConfig['new_live_close_strategy_info']['user_id_last_number_less_than']);
        //unset($this->arrConfig['video_square_tab_strategy']);
        $intLastNumber = intval(self::$intUserId) % 100;
        if(empty(self::$intUserId)) {   //未登录用户
            $intLastNumber = rand(0,99);
        }
        if($intLastNumber >= $intLessThanNo){
            $this->arrConfig['new_live_close_strategy_value'] = 0;
        }
        else {
            $this->arrConfig['new_live_close_strategy_value'] = 1;  //直接进入直播tab
        }
        unset($this->arrConfig['new_live_close_strategy_info']);
    }

    /**
     * 获取客户直播间多码率开关是否打开
     * @param null
     * @return array
     */
    private function processTranscodeSwitch()
    {
        $intRatioNumber = intval(self::$intUserId) % 10;

        if (1 == $intRatioNumber)
        {
            $arrOut = array("switch" => 1, // 开启
                "text" => "客态直播间打开多码率开关",);
        }
        else
        {
            $arrOut = array("switch" => 0, // 默认关闭
                "text" => "客态直播间打开多码率开关",);
        }

        // 先都打开了
        $arrOut = array("switch" => 1, // 开启
            "text" => "客态直播间打开多码率开关",);
        return $arrOut;
    }

    /**
     * 获取开播配置
     * @return mixed|null
     */
    private function getOpenLiveConf()
    {
        $strModel = $this->_objRequest->getCommonAttr('model');

        // 默认配置打底
        $strConfFile = '/app/ala/open_live';
        $arrOpenLiveConf = Bd_Conf::getAppConf($strConfFile);

        // subapp_type 专用配置加载
        if (self::$strSubappType) {
            $strConfFile = '/app/ala/open_live_' . self::$strSubappType;
            $strSubAppConf = Bd_Conf::getAppConf($strConfFile);
            if ($strSubAppConf) {
                $arrOpenLiveConf = $strSubAppConf;
            }
        }

        // 配置文件default block配置打底
        $this->arrConfig['open_live'] = $arrOpenLiveConf['default'];

        // 配置文件model block配置加载，用于分机型配置
        if ($arrOpenLiveConf[self::$strSubappType]) {
            foreach ($arrOpenLiveConf[self::$strSubappType]['model_conf'] as $arrModelConf) {
                $bolFind = false;
                foreach ($arrModelConf['model'] as $strConfModel) {
                    if (strtolower($strConfModel) == strtolower($strModel)) {
                        $bolFind = true;
                        break;
                    }
                }
                if ($bolFind) {
                    unset($arrModelConf['model']);
                    $this->arrConfig['open_live'] = $arrModelConf;
                    return true;
                }
            }
        }

        return true;
    }
}