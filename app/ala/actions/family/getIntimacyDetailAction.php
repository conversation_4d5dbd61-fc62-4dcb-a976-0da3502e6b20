<?php
/**
 * Created by PhpStorm.
 * User: wangyang66
 * Date: 2018/5/20
 * Time: 下午9:45
 */

class getIntimacyDetailAction extends Alalib_Action_BaseAction {


    private $_intJoinStatus = 0;
    private $_intTotal = 0;
    private $_intTodayIncrease = array();
    private $_intLevel = 0;
    private $_intNextLevelNeed = 0;
    private $_intCurrentLevelRange = 0;
    private $_intWatchLive = 0;
    private $_intFirstPresent = 0;
    private $_intTdouConsume = 0;
    private $_credit_conf = array();

    /**
     * @param
     * @return mixed
     */
    public function _getPrivateInfo() {

        $arrPrivateInfo['anchor_id'] = intval(Bingo_Http_Request::get('anchor_id', 0));

        $arrPrivateInfo['check_login'] = false;
        $arrPrivateInfo['check_tbs'] = false;

        return $arrPrivateInfo;
    }


    /**
     * @param
     * @return mixed
     */
    public function _checkPrivate() {
        $intAnchorId = $this->_objRequest->getPrivateAttr('anchor_id');


        if (empty($intAnchorId)) {
            $strLog = "anchor_id is empty.";
            Bingo_Log::warning($strLog);
            $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), "主播ID为空");
            return false;
        }

        return true;
    }

    /**
     * @param
     * @return bool
     */
    public function _execute() {

        $intAudiId = $this->_objRequest->getCommonAttr("user_id");
        $intAnchorId = $this->_objRequest->getPrivateAttr('anchor_id');
        $intClientType = $this->_objRequest->getCommonAttr('client_type');

        if (empty($intAudiId)){
            return $this->errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        //join_status
        $arrServiceInput = array(
            'audi_id' => $intAudiId,
            'anchor_id' => $intAnchorId,
        );

        $strServiceName = "ala";
        $strServiceMethod = "getUserFamilyRelation";

        Bingo_Timer::start($strServiceName . "::" . $strServiceMethod);
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,'post',null,'utf-8');
        Bingo_Timer::end($strServiceName . "::" . $strServiceMethod);
        Bingo_Log::warning($strServiceName . "::" . $strServiceMethod ." cost:".Bingo_Timer::calculate($strServiceName . "::" . $strServiceMethod));

        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);

            return false;
        }

        $arrJoinInfo = $arrOutput['data'];
        if (!empty($arrJoinInfo)) {

            $this->_intJoinStatus = Libs_Define_Family::STATUS_JOINED;
        }


        //detail
        $arrServiceInput = array(
            'audi_id' => $intAudiId,
            'anchor_id' => $intAnchorId,

        );

        $strServiceName = "ala";
        $strServiceMethod = "getMyFamilyIntimacy";
        Bingo_Timer::start($strServiceName . "::" . $strServiceMethod);
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,'post',null,'utf-8');
        Bingo_Timer::end($strServiceName . "::" . $strServiceMethod);
        Bingo_Log::warning($strServiceName . "::" . $strServiceMethod ." cost:".Bingo_Timer::calculate($strServiceName . "::" . $strServiceMethod));

        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);

            return false;
        }


        $arrRes = $arrOutput['data'];


        if ($intAudiId > 0){
            $arrServiceInput = array(
                'user_id' => $intAudiId,
                'client_type' => $intClientType,
            );
            $strServiceName = "ala";
            $strServiceMethod = "getUserCreditSwitch";
            Bingo_Timer::start($strServiceName . "::" . $strServiceMethod);
            $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,'post',null,'utf-8');
            Bingo_Timer::end($strServiceName . "::" . $strServiceMethod);

            if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
                $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
            } else {
                $this->_credit_conf = $arrOutput['data'];
            }
        }


        $this->_intTotal = $arrRes['total'];
        $this->_intTodayIncrease = $arrRes['today_increase'];
        $this->_intLevel = $arrRes['level'];
        $this->_intNextLevelNeed = $arrRes['next_level_need'];
        $this->_intCurrentLevelRange = $arrRes['current_level_range'];
        $this->_intWatchLive = $arrRes['watch_live'];
        $this->_intFirstPresent = $arrRes['first_present'];
        $this->_intTdouConsume = $arrRes['tdou_consume'];


        // 组装数据
        $arrRetData = array(
            'join_status' => $this->_intJoinStatus,
            'total' => $this->_intTotal,
            'today_increase' => $this->_intTodayIncrease,
            'level' => $this->_intLevel,
            'next_level_need' => $this->_intNextLevelNeed,
            'current_level_range' => $this->_intCurrentLevelRange,
            'watch_live' => $this->_intWatchLive,
            'first_present' => $this->_intFirstPresent,
            'tdou_consume' => $this->_intTdouConsume,
            'credit_conf' => $this->_credit_conf,
        );

        return $this->errRet(Tieba_Errcode::ERR_SUCCESS, $arrRetData);

    }





    /**
     * @param $intErrno
     * @param array $arrOutData
     * @param string $strUserMsg
     * @return bool
     */
    private function _errRet($intErrno = Alalib_Conf_Error::ERR_SUCCESS,$arrOutData = array(), $strUserMsg=''){
        $arrOutput = $arrOutData;
        $strErrmsg = Alalib_Conf_Error::getErrorMsg($intErrno);
        $arrOutput['error'] = array();
        $arrOutput['error'] = array(
            'errno'   => $intErrno,
            'errmsg'  => $strErrmsg,
            'usermsg' => $strUserMsg,
        );
        if(empty($strUserMsg)) {
            unset($arrOutput['error']['usermsg']);
        }
        $this->_objResponse->setOutData($arrOutput);
        return true;
    }



}