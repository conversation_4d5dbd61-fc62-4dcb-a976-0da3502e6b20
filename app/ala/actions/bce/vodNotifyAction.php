<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/
/**
 * 录播生成，开放云回调接口
 * 2018/10/12 注意，sdk（手百、好看、全民等） 录播生成通知在 notifyAction 中处理  （by zhanghanqing）
 * @file   vodNotifyAction.php
 */

class vodNotifyAction extends Bingo_Action_Abstract {

    const REDIS_KEY_DEL_VOD_LIVE_ID = 'redis_key_del_vod_live_id';
    const VERIFY_PROCESS_STATUS_MEDIA_AND_THREAD = 1; // 需要生成录播视频和录播帖
    const VERIFY_PROCESS_STATUS_ONLY_MEDIA  = 2; // 由于实名认证未通过，只记录录播视频，不发录播帖
    const VERIFY_PROCESS_STATUS_NOT_ALL = 3; // 录播视频和录播帖都不发

    private static $intRoomId = 0;


	/**
	 * @param $intUserId
	 * @return array|bool
	 */
	private static function selectVerifyInfoInMis($intUserId) {
		if (empty($intUserId)) {
			Bingo_Log::warning("empty user id");
			return false;
		}
		$arrServiceInput = array(
				"user_id" => $intUserId,
		);
		$strServiceName = "ala";
		$strServiceMethod = "selectAuthenticationInfo";
		$arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
		if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
			$strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
			Bingo_Log::warning($strLog);
			return false;
		}
		return $arrOutput['data'];
	}
	
	/**
	 * @param $intUserId
	 * @return array|bool
	 */
	private function getUserInfo($intUserId) {
		if (empty($intUserId)) {
			Bingo_Log::warning("empty user id");
			return false;
		}
		$arrServiceInput = array(
				"uids" => array($intUserId),
		);
		$strServiceName = "ala";
		$strServiceMethod = "userGetInfo";
		$arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
		if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
			$strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
			Bingo_Log::warning($strLog);
			return false;
		}
		$arrUserInfo = $arrOutput['data'][$intUserId];
		return $arrUserInfo;
	}

	
    /**
     * @param: $url
     * @param: $param 接收录播文件生成通知
     * @return: true | false.
     **/
    public function execute(){
        $input = file_get_contents("php://input");
        if(!is_array($input)){
            $arrNotificationMessage = json_decode($input,true);
        }
        else {
            $arrNotificationMessage = $input;
        }
        Bingo_Log::warning('debug : vodNotify input : ' . serialize($arrNotificationMessage));
        $messageBody = json_decode($arrNotificationMessage["messageBody"], true);
        
        //////////////////////////////
        // test data
        /*
        $arrAttr = array(
        	'title' => 'tieba-c41bdb3ef72ad49e1499506118/recording_20170709003601-20170709023046',
        	'description' => 'lss recording, info:tieba-c41bdb3ef72ad49e1499506118/recording_20170709003601-20170709023046',
        	'sourceExtension' => 'mp4',
        );
        $arrMeta = array(
        	'sourceSizeInBytes' => 1509969064,
        	"durationInSeconds" => 6815,
        	"sizeInBytes" => 1509969064,
        );
        
        $messageBody = array('mediaId' => 'mda-hgi3i3shddx1uwht',
        		'status' => 'PUBLISHED',
        		'attributes' => $arrAttr,
        		'meta' => $arrMeta,
        		'playableUrlList' => array(
        				array(
        					'url' => 'http://ge1ijh7qci7i3d9d24z.exp.bcevod.com/mda-hgi3i3shddx1uwht/mda-hgi3i3shddx1uwht.mp4',
        				),
        		),
        		'thumbnailList' => array('http://ge1ijh7qci7i3d9d24z.exp.bcevod.com/mda-hgi3i3shddx1uwht/mda-hgi3i3shddx1uwht.jpg'),
        );
        
        $arrAttr = array(
        		'title' => '-tieba-c41bdb3ef72ad49e1499506118/[merge_recording]_1500297941',
        		'description' => '',
        		'sourceExtension' => 'NOSOURCE',
        );
        $arrMeta = array(
        		'sourceSizeInBytes' => 0,
        		"durationInSeconds" => 32371,
        		"sizeInBytes" => 2147483647,
        );
        
        $messageBody = array('mediaId' => 'mda-hgswc41rzhtywttx',
        		'status' => 'PUBLISHED',
        		'attributes' => $arrAttr,
        		'meta' => $arrMeta,
        		'playableUrlList' => array(
        				array(
        						'url' => 'http://ge1ijh7qci7i3d9d24z.exp.bcevod.com/mda-hgswc41rzhtywttx/mda-hgswc41rzhtywttx.mp4',
        				),
        		),
        		'thumbnailList' => array('http://ge1ijh7qci7i3d9d24z.exp.bcevod.com/mda-hgswc41rzhtywttx/mda-hgswc41rzhtywttx.jpg'),
        );
         */
        ////////////////////////////////
       
        $strMediaId = $messageBody['mediaId'];
        $status = $messageBody['status'];
        $title = explode('/', $messageBody['attributes']['title']);
        $strSessionId = $title[0];
        $merge_string = $title[1];
        $size = $messageBody['meta']['sizeInBytes'];
        $intMediaDuration = $messageBody['meta']['durationInSeconds'];
        $file = $messageBody['playableUrlList'][0]['url'];
        $cover = $messageBody['thumbnailList'][0];

		Bingo_Log::pushNotice('media_id', $strMediaId);
		Bingo_Log::pushNotice('status', $status);
		Bingo_Log::pushNotice('title', serialize($title));
		Bingo_Log::pushNotice('session_id', $strSessionId);
		Bingo_Log::pushNotice('merge_string', $merge_string);
		Bingo_Log::pushNotice('file', $file);
		Bingo_Log::pushNotice('cover', $cover);
		Bingo_Log::pushNotice('durationInSeconds', $intMediaDuration);
		
		// 检测status
		if ('PUBLISHED' !== $status)
		{
			Bingo_Log::warning("notify status is not PUBLISHED, the status is".$status);
			return false;
		}
		
		// 根据session id 获取直播信息
		$arrRpcInput = array(
            'session_id' => $strSessionId,
		);
		
		$arrOutput = Tieba_Service::call('ala', 'liveGetLiveInfoBySessionId', $arrRpcInput, null, null, 'post', null, 'utf-8');
		
		///////////////////////////////////////////////////
		// test data
		/*
		$arrData = array(
				"live_id" => 932132,
				"user_id" => 1656485225,
				"user_name" => "独想丶暖你",
				"group_id" => 979223,
				"session_id" => "tieba-c41bdb3ef72ad49e1499506118",
				"description" => "我就是是个是个傻子",
				"cover" => "http://uegpicture-bj.bj.bcebos.com/tieba-c41bdb3ef72ad49e1499506118/thumbnail_20170709022911.jpg",
				"join_count" => 6961,
				"peak_value" => 0,
				"zan_count" => 1616,
				"live_duration" => 32457,
				"charm_count" => 54,
				"start_time" => 1499506118,
				"end_time" => 1499538575,
				"location" => "",
				"lng" => 0,
				"lat" => 0,
				"forum_id" => 837839,
				"forum_name" => "王者荣耀",
				"live_status" => 2,
				"close_type" => 1,
				"close_reason" => "",
				"media_subtitle" => "",
				"media_url" => "",
				"media_pic" => "",
				"media_id" => "",
				"is_private" => 0,
				"live_type" => 2,
				"screen_direction" => 2,
				"third_app_id" => "",
				"cp_live_id" =>  0,
				"game_label" => "",
				"clarity" => 0
		);
		$arrOutput = array('errno' => 0,
				'data' => $arrData,
		);
		*/
		/////////////////////////////////////////////////////
		
		if ($arrOutput === false || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) 
		{
			Bingo_Log::pushNotice('no_vod_type', 'liveGetLiveInfoBySessionId_fail');
			Bingo_Log::fatal("liveGetLiveInfoBySessionId err! [$strSessionId], the input is ".serialize($arrRpcInput)."output is ".serialize($arrOutput));
			return false;
		}
		
		$arrLiveInfo = isset($arrOutput['data']) ? $arrOutput['data'] : array();

        if (empty($arrLiveInfo))
        {
            Bingo_Log::fatal("live info is null, the session id is ".$strSessionId);
            return false;
        }
        self::$intRoomId = $arrLiveInfo['room_id'];

        $intLiveId = isset($arrLiveInfo['live_id']) ? intval($arrLiveInfo['live_id']) : 0;
		$intLiveType = isset($arrLiveInfo['live_type']) ? intval($arrLiveInfo['live_type']) : 0;
		$intUserId = isset($arrLiveInfo['user_id']) ? intval($arrLiveInfo['user_id']) : 0;
		$intCloseType = isset($arrLiveInfo['close_type']) ? intval($arrLiveInfo['close_type']) : 0;
		$intLiveStatus = isset($arrLiveInfo['live_status']) ? intval($arrLiveInfo['live_status']) : 0;
        $intLiveDuration = isset($arrLiveInfo['live_duration']) ? intval($arrLiveInfo['live_duration']) : 0;
		$intStartTime = isset($arrLiveInfo['start_time']) ? intval($arrLiveInfo['start_time']) : 0;
        $intEndTime = isset($arrLiveInfo['end_time']) ? intval($arrLiveInfo['end_time']) : 0;
        
		if (0 === $intLiveId || 0 === $intLiveType || 0 === $intUserId)
		{
			Bingo_Log::fatal("live info is invalid, the live info is ".serialize($arrLiveInfo));
			return false;
		}

		$arrReq = array(
		    'live_ids' => array($intLiveId),
        );
		$arrTmpRes = Alalib_Util_Service::call('ala', 'liveGetInfo', $arrReq);
		if ($arrTmpRes && $arrTmpRes['errno'] == 0 && $arrTmpRes['data'][$intLiveId]['live_info']){
		    $arrLiveInfo = $arrTmpRes['data'][$intLiveId]['live_info'];
        }
		
		Bingo_Log::pushNotice('user_id', $intUserId);
		Bingo_Log::pushNotice('live_id', $intLiveId);
		Bingo_Log::pushNotice('close_type', $intCloseType);
		Bingo_Log::pushNotice('live_type', $intLiveType);
		Bingo_Log::pushNotice('live_status', $intLiveStatus);
		Bingo_Log::pushNotice('live_duration', $intLiveDuration);
		Bingo_Log::pushNotice('end_time', $intEndTime);
		
		// 如果是在直播没有关闭，在直播过程中收到部分录播片段，则返回，不处理
		if (Libs_Define_Live::LIVE_CLOSE !== $intLiveStatus && Libs_Define_Live::LIVE_RECORD_DELETE !== $intLiveStatus)
		{
			Bingo_Log::pushNotice('live_is_open', 1);
			Bingo_Log::warning("live_id=$intLiveId is open, the live_status=$intLiveStatus");
			return true;
		}
		
		//  如果是被UEG关闭的，则不生成录播
		if (Libs_Define_Live::LIVE_CLOSE_UEG == $intCloseType) {
			Bingo_Log::pushNotice('no_vod_type', 'live_close_ueg');
			Bingo_Log::warning("live is closed by ueg, live_id=$intLiveId, live_status=$intLiveStatus");
            self::sendVodFailNmq();
            return false;
		}
		
		
		// 判断当前收到的录播和直播时长差别是否大于3分钟，如果大于3分钟，则表明是不是完整的录播，需要先合并，再生成录播c
		// 为了防止反复重复合并，只对未合并过的进行合并
		if ((abs($intLiveDuration - $intMediaDuration) > Libs_Define_Live::LIVE_RECORD_MIN_TIME * 2))
		{
			Bingo_Log::pushNotice('media_file_is_chunk', 1);
			$arrRpcInput = array('title' => $strSessionId,
				'start_time' => $intStartTime,
				'end_time' => $intEndTime,
			);
			
			$arrOutput = Tieba_Service::call('ala', 'mergeLive', $arrRpcInput, null, null, 'post', null, 'utf-8');
			
			if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno'])
			{
				Bingo_Log::fatal('call ala::mergeLive fail, the input is '.serialize($arrRpcInput).' output is '.serialize($arrOutput));
				Bingo_Log::pushNotice('merge_live_fail', 1);
				return false;
			}
			
			// 合并视频是异步的，调用合并后，需要等待下次回调，调用后就行退出吧
			$mergeMediaId = isset($arrOutput['data']['media_id']) ? $arrOutput['data']['media_id'] : '';
			$mergeDuration = isset($arrOutput['data']['merge_duration']) ? intval($arrOutput['data']['merge_duration']) : 0;
			Bingo_Log::pushNotice('merge_media_id', $mergeMediaId);
			Bingo_Log::warning("goto merge_media");
			
			$arrRecordLiveInput = array('media_id' => $mergeMediaId,
				'session_id' => $strSessionId,
				'live_id' => $intLiveId,
			);
			
			$arrSetMergeLiveRet = self::setMergeRecordLiveFlag($arrRecordLiveInput);
			if (false === $arrSetMergeLiveRet)
			{
				Bingo_Log::fatal('set merge flag addRecordLive fail, the input is '.serialize($arrRecordLiveInput));
				Bingo_Log::pushNotice('set_merge_flag', 0);
				return false;
			}
			Bingo_Log::pushNotice('set_merge_flag', 1);
			return true;
		}
		
		$arrUserInfo = self::getUserInfo($intUserId);
		if (false === $arrUserInfo) 
		{
			Bingo_Log::fatal("get user info failed");
            self::sendVodFailNmq();
            return false;
		}
		
//		$arrVerifyInput = array('live_info' => $arrLiveInfo,
//		    'user_info' => $arrUserInfo,
//			'file' => $file,
//			'cover' => $cover,
//		);
//
//		// 处理实名认证相关处理流程
//		$verifyRet  = self::verifyProcess($arrVerifyInput);
//
//		Bingo_Log::pushNotice('verify_ret', $verifyRet);
//
//		if (false === $verifyRet)
//		{
//			Bingo_Log::fatal('process verify fail, the input is '.serialize($arrVerifyInput));
//			return false;
//		}
//
//		if (self::VERIFY_PROCESS_STATUS_NOT_ALL === $verifyRet)
//		{
//			Bingo_Log::warning("verify process is not commit vod thread all");
//			return false;
//		}
		
		// 如果视频时长小于3分钟，则不保留录播
		if ($intMediaDuration < Libs_Define_Live::LIVE_RECORD_MIN_TIME || $intLiveDuration < Libs_Define_Live::LIVE_RECORD_MIN_TIME)
		{
			Bingo_Log::warning('live duration is letter min '.Libs_Define_Live::LIVE_RECORD_MIN_TIME);
            self::sendVodFailNmq();
            return true;
		}
		
		if (!Alalib_Conf_Sdk::isIsolateToTieba($arrLiveInfo['subapp_type'])){
            $arrRecordLiveInput = array(
                'media_id' => $strMediaId,
                'session_id' => $strSessionId,
                'live_id' => $intLiveId,
                'duration' => $intMediaDuration,
                'file' => $file,
                'cover' => $cover,
            );
            $recordLiveRet = self::addRecordLive($arrRecordLiveInput);
            if (false === $recordLiveRet)
            {
                Bingo_Log::fatal('addRecordLive fail, the input is '.serialize($arrRecordLiveInput));
                return false;
            }

            // 不发录播贴
            $strRedisKey = self::REDIS_KEY_DEL_VOD_LIVE_ID;
            $arrRedisInput = array(
                "key" => $strRedisKey,
                "member" => $intLiveId,
            );
            $arrRedisOutput = Util_Redis::sismemberFromRedis($arrRedisInput);
            if ($arrRedisOutput === false){
                $strLog = __CLASS__."::".__FUNCTION__." call Util_Redis::sismemberFromRedis fail. input:[".serialize($arrRedisInput)."]; output:[".serialize($arrRedisOutput)."]";
                Bingo_Log::warning($strLog);
            }
            $bolIsDel = isset($arrOutput[$strRedisKey]) ? intval($arrOutput[$strRedisKey]) : 0;
            if(1 == $bolIsDel){
                // 已被删除
                $strLog = __CLASS__."::".__FUNCTION__." vod has been delete live_id: [$intLiveId]";
                Bingo_Log::warning($strLog);
                // $arrRedisOut = Util_Redis::sremToRedis($arrRedisInput);
                // if (false == $arrRedisOut) {
                //     Bingo_Log::fatal(__CLASS__ . '::' . __FUNCTION__ . ' call redis error input ['.serialize($arrRedisInput).']');
                // }
                return true;
            }

            if(false === Libs_Define_Live::CLOSE_LIVE_VIDEO_ENTRANCE) {   //关闭回放入口的时候,不再发直播回放贴了
                $arrVodThreadInput = array('live_id' => $intLiveId);
                $vodThread = self::addVodThread($arrVodThreadInput);

                if (false === $vodThread)
                {
                    Bingo_Log::fatal('addVodThread fail, the input is '.serialize($arrVodThreadInput));
                }
                Bingo_Log::pushNotice("new_vod_notify_suc", 1);
                Bingo_Log::warning("addVodThread success");
            }
        }



        $intNowTime = Bingo_Timer::getNowTime();
        $intRoomId = $arrLiveInfo['room_id'];
        if ($intRoomId > 0){
            $strTitle = $arrLiveInfo['description'];
            $intJoinCount = $arrLiveInfo['join_count'];
            if (!$strTitle){
                $strServiceName = "ala";
                $strServiceMethod = "getAnchorToShoubaiInfo";
                $arrReq = array(
                    'user_id' => $arrLiveInfo['user_id'],
                );
                if (Alalib_Conf_Sdk::SUBAPP_HAOKAN == $arrLiveInfo['subapp_type'] || Alalib_Conf_Sdk::SUBAPP_QUANMIN == $arrLiveInfo['subapp_type']){
                    $arrReq['subapp_type'] = $arrLiveInfo['subapp_type'];
                }
                $arrRes = Tieba_Service::call($strServiceName, $strServiceMethod, $arrReq, null, null, "post", null, "utf-8");
                if ($arrRes !== false && $arrRes['errno'] == 0 && in_array($intUserId, $arrRes['data'])) {
                    $arrShoubaiDisplayInfo = $arrRes['data'][$intUserId];
                    $strTitle = $arrShoubaiDisplayInfo['title'];
                } else {
                    $strTitle = !empty($strTitle) ? $strTitle : $arrLiveInfo['user_name'] . '的直播';
                }
            }
            if (Alalib_Conf_Sdk::SUBAPP_HAOKAN == $arrLiveInfo['subapp_type'] || Alalib_Conf_Sdk::SUBAPP_QUANMIN == $arrLiveInfo['subapp_type']){
                $intFromApp = Alalib_Conf_Sdk::getFromApp($arrLiveInfo['subapp_type']);
            } else {
                $intFromApp = Alalib_Conf_Sdk::getFromApp(Alalib_Conf_Sdk::SUBAPP_TIEBA);
            }
            // 记录sdk生成的录播
            $arrSdkVodInput = array(
                'media_id' => $strMediaId,
                'session_id' => $strSessionId,
                'live_id' => $intLiveId,
                'room_id' => $intRoomId,
                'duration' => $intMediaDuration,
                'file' => $file,
                'cover' => $cover,
                'description' => $strTitle,
                'join_count' => $intJoinCount,
                'start_time' => $intStartTime,
                'status' => Libs_Define_Live::LIVE_VOD_STATUS_INIT,
                'subapp_type' => $arrLiveInfo['subapp_type'],
            );
            $bolRecordLiveRet = self::addSdkRecordLive($arrSdkVodInput);
            if (false === $bolRecordLiveRet)
            {
                Bingo_Log::fatal('addSdkRecordLive fail, the input is '.serialize($arrSdkVodInput));
            }

            $arrBdAppParams = array(
                'room_id' => $intRoomId,
                'title' => $strTitle,
                'reply_url' => $file,
                'duration' => $arrLiveInfo['live_duration'],
                'cover' => $cover,
                'join_count' => $arrLiveInfo['join_count'],
                'start_time' => $arrLiveInfo['start_time'],
                'end_time' => $arrLiveInfo['end_time'],
                'subapp_type' => $arrLiveInfo['subapp_type'],
                'media_id' => $arrLiveInfo['media_id'],
                'from_app' => $intFromApp,
                'ts' => $intNowTime,
                'apikey' => Alalib_Conf_Sdk::SHOU_BAI_IM_AK,
                'sign' => md5($intRoomId . $intNowTime . Alalib_Conf_Sdk::SHOU_BAI_IM_SK),
            );
            $arrInput = array(
                'type' => 'vodNotify',
                'bdapp_params' => $arrBdAppParams,
            );
            $this->sendNmqDuImServ($arrInput);
        }

        return true;

    }


    /**
     * 发送nmq消息
     *
     * @param array $arrInput
     *
     * @return boolean
     */
    private function sendNmqDuImServ($arrInput)
    {
        if (empty($arrInput['type'])){
            Bingo_Log::warning("param error. sendNmqDuServ fail");
            return false;
        }

        $strNmqCmd   = Tieba_Cmd_Ala::DU_IM_SERV_CALL;
        $strNmqTopic = 'ala';
        Bingo_Timer::start('sendNmqDuImServ');
        $nmqRet = Tieba_Commit::commit($strNmqTopic, $strNmqCmd, $arrInput);
        //$nmqRet = Tieba_CommitAla::commit(Libs_Define_Nmq::ALA_NMQ_TOPIC, $strNmqCmd, $arrNmqInput);
        Bingo_Timer::end('sendNmqDuImServ');
        if(false === $nmqRet || Tieba_Errcode::ERR_SUCCESS !== $nmqRet['err_no']){
            Bingo_Log::fatal("send nmq fail, nmq topic: ".$strNmqTopic." nmq cmd: $strNmqCmd"."nmq input is ".serialize($arrInput).'nmq output is'.serialize($nmqRet));

            return false;
        }
        Bingo_Log::pushNotice('sendNmqDuImServ', 1);

        return true;
    }

    /**
     * 生成录播失败时调用
     * @param null
     * @return boolean
     */
    private function sendVodFailNmq()
    {
        if (empty(self::$intRoomId)){
            return false;
        }
        $intNowTime = Bingo_Timer::getNowTime();
        $arrBdAppParams = array(
            'room_id' => self::$intRoomId,
            'apikey' => Alalib_Conf_Sdk::SHOU_BAI_IM_AK,
            'sign' => md5(self::$intRoomId . $intNowTime . Alalib_Conf_Sdk::SHOU_BAI_IM_SK),
        );
        $arrInput = array(
            'type' => 'vodFailNotify',
            'bdapp_params' => $arrBdAppParams,
        );
        $this->sendNmqDuImServ($arrInput);
        return true;
    }


    /**
     * add record live
     * @param unknown $arrInput
     * @return bool
     */
    private static function addRecordLive($arrInput)
    {
    	$strMediaId = strval($arrInput['media_id']);
    	$strSessionId = strval($arrInput['session_id']);
    	$intLiveId = intval($arrInput['live_id']);
    	$intDuration = $arrInput['duration'];
    	$strFile = strval($arrInput['file']);
    	$strCover = strval($arrInput['cover']);
    	
    	$arrInput = array(
    			'media_id' => $strMediaId,
    			'session_id' => $strSessionId,
    			'live_id' => $intLiveId,
    			'duration' => $intDuration,
    			'file' => $strFile,
    			'cover' => $strCover,
    	);
    	
    	$arrOutput = Tieba_Service::call('ala', 'recordLive', $arrInput);
    	
    	if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno'])
    	{
    		Bingo_Log::pushNotice('add_record_live', 0);
    		Bingo_Log::fatal("recordLive err! the input is ".serialize($arrInput).' output is '.serialize($arrOutput));
    		return false;
    	}
    	Bingo_Log::pushNotice('add_record_live', 1);
    	return true;
    }
    
    /**
     * add vod thread
     * @param unknown $arrInput
     * @return bool
     */
    private static function addVodThread($arrInput)
    {
    	$intLiveId = isset($arrInput['live_id']) ? intval($arrInput['live_id']) : 0;
    	
    	// 发录播贴
    	$arrServiceInput = array(
    		'live_id' => $intLiveId,
    	);
    	
    	$arrServiceOutput = Tieba_Service::call('ala', 'addVodThread', $arrServiceInput, null, null, 'post', null, 'utf-8');

    	if ($arrServiceOutput['errno'] == Alalib_Conf_Error::ERR_VOD_NOT_MATCH_CONDITION){
            Bingo_Log::warning('ERR_VOD_NOT_MATCH_CONDITION. input[' . serialize($arrServiceInput) . "] output[" . serialize($arrServiceOutput) ."]");
        } else if ($arrServiceOutput == false || $arrServiceOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
    		Bingo_Log::pushNotice('add_vod_thread', 0);
    		// service会打fatal,一般发失败就失败了,所以不打fatal，并且返回成功即可
    		Bingo_Log::warning('send ala vod thread failed. input[' . serialize($arrServiceInput) . "] output[" . serialize($arrServiceOutput) ."]");
    		return false;
    	}
    	Bingo_Log::pushNotice('add_vod_thread', 1);
    	return true;
    }
    
    
    /**
     * 实名认证处理
     * @param unknown $arrInput
     * @return int 处理后的各种状态
     */
    private static function  verifyProcess($arrInput)
    {
    	$arrUserInfo = isset($arrInput['user_info']) ? $arrInput['user_info'] : array();
    	$arrLiveInfo = isset($arrInput['live_info']) ? $arrInput['live_info'] : array();
    	$file = isset($arrInput['file']) ? $arrInput['file'] : '';
    	$cover = isset($arrInput['cover']) ? $arrInput['cover'] : '';
    	
     	$intUserId = isset($arrUserInfo['user_id']) ? intval($arrUserInfo['user_id']) : 0;
     	$intLiveId = isset($arrLiveInfo['live_id']) ? intval($arrLiveInfo['live_id']) : 0;
     	$intLiveType = isset($arrLiveInfo['live_type']) ? intval($arrLiveInfo['live_type']) : 0;
     	$intUserVerifyVideoStatus = isset($arrUserInfo['verify_video_status']) ? intval($arrUserInfo['verify_video_status']) : 0;
     	Bingo_Log::pushNotice('user_verify_video_status', $intUserVerifyVideoStatus);
     	Bingo_Log::pushNotice('live_type', $intLiveType);
     	
    	if (0 === $intUserId)
    	{
    		Bingo_Log::warning("user id is 0");
    		return false;
    	}
    	
    	// 判断用户是否需要实名认证
    	$bolNeedAuthen = Util_AlaUser::isNeedAuthen($intUserId);
    	Bingo_Log::pushNotice('need_authen', intval($bolNeedAuthen));
    	
    	if (false === $bolNeedAuthen)
    	{
    		Bingo_Log::warning("not need authen, the user id is ".$intUserId);
    		return self::VERIFY_PROCESS_STATUS_MEDIA_AND_THREAD;
    	}
    	
        // 判断用户是否已经是实名认证视频
    	if (Libs_Define_User::USER_AUTHEN_VIDEO_SUCCESS === $intUserVerifyVideoStatus)
    	{
    		Bingo_Log::warning("user authen video success");
    		return self::VERIFY_PROCESS_STATUS_MEDIA_AND_THREAD;
    	}
    	
    	//游戏直播不走视频认证流程
    	if (Libs_Define_Live::LIVE_TYPE_GAME == $intLiveType) 
    	{   
    		Bingo_Log::pushNotice('no_vod_type', 'game_no_authen_video');
    		Bingo_Log::warning("live_type = " . Libs_Define_Live::LIVE_TYPE_GAME . ", need not deal about authentication");
    		return self::VERIFY_PROCESS_STATUS_MEDIA_AND_THREAD;
    	}
    	
    	// 判断该录播对应的是否是实名认证直播
    	$bolIsAuthenLive = self::isAuthenLive($intLiveId);
    	
    	if (false === $bolIsAuthenLive)
    	{
    		Bingo_Log::pushNotice('is_auth_live', 0);
    		Bingo_Log::warning("is not auth live, the live_id=$intLiveId");
    		return self::VERIFY_PROCESS_STATUS_ONLY_MEDIA;
    	}
    	else 
    	{
    		Bingo_Log::pushNotice('is_auth_live', 1);
    	}
    	
    	// 判断审核MIS中的认证状态
    	$arrVerifyMisInfo = self::selectVerifyInfoInMis($intUserId);
    	if (false === $arrVerifyMisInfo || empty($arrVerifyMisInfo)) 
    	{
    		Bingo_Log::pushNotice('no_vod_type', 'selectVerifyInfoInMis_fail');
    		Bingo_Log::warning("empty verify info, input[" . $intUserId . '],output[' . serialize($arrVerifyMisInfo) . ']');
    		return self::VERIFY_PROCESS_STATUS_ONLY_MEDIA;
    	}
    	
    	$intMisVerifyType = intval($arrVerifyMisInfo['auth_type']);
    	$intMisVerifyInfoStatus = intval($arrVerifyMisInfo['verify_info_status']);
    	$intMisVerifyVideoStatus = intval($arrVerifyMisInfo['verify_video_status']);
    	
    	Bingo_Log::pushNotice('mis_verify_info_status', $intMisVerifyInfoStatus);
    	Bingo_Log::pushNotice('mis_verify_info_status', $intMisVerifyVideoStatus);
    	
    	if (Libs_Define_User::USER_AUTHEN_INFO_PENDING != $intMisVerifyInfoStatus
    	   && Libs_Define_User::USER_AUTHEN_INFO_SUCCESS != $intMisVerifyInfoStatus) 
    	{
			Bingo_Log::pushNotice ( 'no_vod_type', "verify_info_status:{$intMisVerifyInfoStatus}_invalid" );
			Bingo_Log::warning ( "mis verify_info_status is invalid![info_status=$intMisVerifyInfoStatus,video_status=$intMisVerifyVideoStatus]" );
			return self::VERIFY_PROCESS_STATUS_ONLY_MEDIA;
		}
		
		if (Libs_Define_User::USER_AUTHEN_VIDEO_INIT == $intMisVerifyVideoStatus 
			|| Libs_Define_User::USER_AUTHEN_VIDEO_FAIL == $intMisVerifyVideoStatus) 
		{
			
			$arrVerifyInfo = unserialize ( $arrVerifyMisInfo ['verify_info'] );
			
			if (empty($arrVerifyInfo) || empty ($arrVerifyInfo ['questions'])) 
			{
				Bingo_Log::pushNotice ( 'no_vod_type', "authentication_question_empty" );
				Bingo_Log::warning ( "authentication question is empty, this video is not true" );
				return self::VERIFY_PROCESS_STATUS_ONLY_MEDIA;
			}
			
			$arrInput = array (
				'user_id' => $intUserId,
				'verify_info_status' => $intMisVerifyInfoStatus,
				'verify_video_status' => Libs_Define_User::USER_AUTHEN_VIDEO_PENDING,
				'vod_file' => $file,
				'vod_cover' => $cover, 
			);
			
			// 芝麻认证用户，视频审核直接通过
			if ($intMisVerifyType == Libs_Define_User::USER_AUTHEN_TYPE_ZMXY) {
                $arrInput['verify_video_status'] = Libs_Define_User::USER_AUTHEN_VIDEO_SUCCESS;
            }
			
			$arrRet = Tieba_Service::call ( 'ala', 'updateVodAuthenticationInfo', $arrInput, null, null, 'post', 'php', 'utf-8' );
			
			if (false === $arrRet || Alalib_Conf_Error::ERR_SUCCESS != $arrRet ['errno']) 
			{
				$arrRet = Tieba_Service::call ( 'ala', 'updateVodAuthenticationInfo', $arrInput, null, null, 'post', 'php', 'utf-8' );
				if (false === $arrRet || Alalib_Conf_Error::ERR_SUCCESS != $arrRet ['errno']) 
				{
					Bingo_Log::pushNotice ( 'no_vod_type', "updateVodAuthenticationInfo_fail" );
					$strLog = __CLASS__ . "::" . __FUNCTION__ . " call ala service::updateVodAuthenticationInfo fail. input:[" . serialize ( $arrInput ) . "]; output:[" . serialize ( $arrRet ) . "]";
					Bingo_Log::warning ( $strLog );
					return self::VERIFY_PROCESS_STATUS_ONLY_MEDIA;
				}
			}
			return self::VERIFY_PROCESS_STATUS_ONLY_MEDIA;
		} 
		else 
		{
			Bingo_Log::pushNotice ( 'no_vod_type', "video_verify_pending" );
			Bingo_Log::warning ( "verify pending no store vod!!!!!!" );
			return self::VERIFY_PROCESS_STATUS_ONLY_MEDIA;
		}
    }
    
    /**
     * 判断该录播对应的直播是否是实名认证直播，如果不是，则不存录播
     * @param unknown $intLiveId 直播ID
     * @return bool false : 不是认证直播， true : 是认证直播
     */
    private static function isAuthenLive($intLiveId)
    {
    	//为了保证录播过来的视频是实名认证视频,直播间关闭后录播到达顺序可能相反
    	$arrInput = array(
    			'op' => 'get',
    			'live_id'  => $intLiveId,
    	);
    	$arrOutput = Tieba_Service::call('ala', 'dealAuthenticationRedis', $arrInput, null, null, 'post', null, 'utf-8','local');
    	if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) 
    	{
    		Bingo_Log::warning('call ala/dealAuthenticationRedis err! [' . serialize(compact('arrInput', 'arrOutput')) . ']');
    		//return false;   这里不return
    	}
    	else 
    	{
    		$retData = isset($arrOutput['data']) ? intval($arrOutput['data']) : null ;
    		if(empty($retData)) {
    			Bingo_Log::pushNotice('no_vod_type', 'dealAuthenticationRedis_empty');
    			Bingo_Log::pushNotice('is_auth_live', 0);
    			Bingo_Log::warning("live_id=$intLiveId is not authentication live_id.");
    			return false;
    		}
    	}
    	Bingo_Log::pushNotice('is_auth_live', 1);
    	return true;
    }
    
    /**
     * 设置合并标识
     * @param unknown $arrInput
     * @return bool
     */
    private static function setMergeRecordLiveFlag($arrInput)
    {
    	$strMediaId = $arrInput['media_id'];
    	$intLiveId = isset($arrInput['live_id']) ? intval($arrInput['live_id']) : 0;
    	
    	$arrRpcInput = array(
    		'media_id' => $strMediaId,
    		'live_id'  => $intLiveId,
    	);
    	
    	$arrOutput = Tieba_Service::call('ala', 'setMergeRecordLiveFlag', $arrRpcInput, null, null, 'post', 'php', 'utf-8');
    	
    	if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno'])
    	{
    		Bingo_Log::fatal("call ala::setMergeRecordLiveFlag fail, the input is ".serialize($arrRpcInput).' output is '.serialize($arrOutput));
    		return false;
    	}
    	
    	Bingo_Log::warning("setMergeRecordLiveFlag true, ".serialize($arrInput));
    	return true;
    }

    /**
     * sdk录播记录
     * add sdk record live
     * @param array $arrInput
     * @return bool
     */
    private static function addSdkRecordLive($arrInput)
    {
        $arrOutput = Tieba_Service::call('ala', 'recordSdkLiveVod', $arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno'])
        {
            Bingo_Log::pushNotice('add_sdk_record_live', 0);
            Bingo_Log::warning(__CLASS__ . __FUNCTION__ . "recordSdkLiveVod err! the input is ".serialize($arrInput).' output is '.serialize($arrOutput));
            return false;
        }
        Bingo_Log::pushNotice('add_sdk_record_live', 1);
        return true;
    }

//     /**
//      * @param $intUserId
//      * @return array|bool
//      */
//     private static function selectVerifyInfoInMis($intUserId) {
//         if (empty($intUserId)) {
//             Bingo_Log::warning("empty user id");
//             return false;
//         }
//         $arrServiceInput = array(
//             "user_id" => $intUserId,
//         );
//         $strServiceName = "ala";
//         $strServiceMethod = "selectAuthenticationInfo";
//         $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
//         if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
//             $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
//             Bingo_Log::warning($strLog);
//             return false;
//         }
//         return $arrOutput['data'];
//     }

//     /**
//      * @param $intUserId
//      * @return array|bool
//      */
//     private function getUserInfo($intUserId) {
//         if (empty($intUserId)) {
//             Bingo_Log::warning("empty user id");
//             return false;
//         }
//         $arrServiceInput = array(
//             "uids" => array($intUserId),
//         );
//         $strServiceName = "ala";
//         $strServiceMethod = "userGetInfo";
//         $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
//         if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
//             $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
//             Bingo_Log::warning($strLog);
//             return false;
//         }
//         $arrUserInfo = $arrOutput['data'][$intUserId];
//         return $arrUserInfo;
//     }

}
