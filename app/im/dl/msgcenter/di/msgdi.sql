DROP DATABASE IF EXISTS forum_im;
CREATE DATABASE forum_im DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
USE forum_im;

SET @saved_cs_client = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `im_message_info` (
	 `msg_id` BIGINT unsigned NOT NULL default 0,
	 `msg_type` TINYINT unsigned NOT NULL default 0,
	 `group_id` BIGINT unsigned NOT NULL default 0,
	 `user_id` BIGINT unsigned NOT NULL default 0,
	 `content` varchar(10240) NOT NULL default '',
	 `duration` INT unsigned NOT NULL default 0,
	 `create_time` INT unsigned NOT NULL default 0,
	 `status` TINYINT unsigned NOT NULL default 0,
	 `record_id` BIGINT unsigned NOT NULL default 0,
	 `unuse1` INT unsigned NOT NULL default 0,
	 `unuse2` varchar(256) NOT NULL default '',
   PRIMARY KEY (`msg_id`),
   index `idx_group_id` (`group_id`,`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET character_set_client = @saved_cs_client;
