<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-09-25
 * @comment 举报群组
 * @version 1.0
 */
class reportGroupAction extends Lib_Im_Action_Base {
    protected $intGroupId = 0;
    protected $intReportType = 0;

    protected function _input() {
        $this->intGroupId  = intval($this->getInput('groupId', 0));
        $this->intReportType  = intval($this->getInput('reportType', 0));
        Tieba_Stlog::addNode('group_id', $this->intGroupId);
        Tieba_Stlog::addNode('reason_str', $this->intReportType);

        if($this->intGroupId<1 || $this->intReportType<0 || $this->intReportType>3) {
            $this->_error(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        return true;
    }

    protected function _process() {
        $arrReq = array(
            'user_id' => $this->intUserId,
            'group_id' => $this->intGroupId,
            'report_type' => $this->intReportType,
        );
        $arrRes = Lib_Util_Service::call('im', 'reportGroup', $arrReq);
        if(!$arrRes) {
            Lib_Util_Log::fatal("call reportGroup failed.  req:".serialize($arrReq)."   res:".serialize($arrRes));
            $this->_error(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return false;
        } else if($arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Lib_Util_Log::fatal("call reportGroup failed.  req:".serialize($arrReq)."   res:".serialize($arrRes));
            $this->_error($arrRes['errno']);
            return false;
        }

        $this->arrData = Lib_Im_Util::change_key_style($this->arrData);
        return true;
    }
}
