<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-04-30 21:12:10
 */
class getUserInfoAction extends Lib_Im_Action_BaseAction {
	/**
	 * 初始化
	 * @see Lib_Im_Action_BaseAction::init()
	 * @return boolean
	 */
	public function init() {
		if (! parent::init ()) {
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_UNKOWN;
			$this->_renderJson ();
			return false;
		}
		return true;
	}
	/**
	 * @see Lib_Im_Action_BaseAction::_process()
	 * @return
	 */
	protected function _process() {
		if (!$this->_bolUserLogin) {
			Bingo_Log::warning (  __CLASS__ .' the user do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_USER_NOT_LOGIN;
			return $this->_renderJson();
		}
		$chatUid = (int)Bingo_Http_Request::get('chatUid', 0);
		if($chatUid <= 0){
			Bingo_Log::warning(__FUNCTION__ . " input params invalid. chatUid");
			$this->_intErrorNo = Tieba_Errcode::ERR_PARAM_ERROR;
			return $this->_renderJson();
		}

//        $this->_arrTpl['chatUser'] = array();
//        return $this->_renderJson();
//		if (intval($this->_intUid) === 141160196) {
//            $this->_arrTpl['chatUser'] = array();
//            return $this->_renderJson();
//		}

		$arrInput = array(
			'user_id'  => $this -> _intUid,
			'chat_uid' => $chatUid,
			'offset'   => 0,
			'count'    => 1,
		);
		$ret = Lib_Util_Service::call('im', 'getContacts', $arrInput, 'local');
		if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			$this->_intErrorNo = $ret['errno'];
			return $this->_renderJson();
		}
		$chatUser = null;

		//先查找最近联系人，最近联系人没有返回用户名和id未读计数为0
		if(isset($ret['ret'][0])){
			$chatUser = $ret['ret'][0];
		}
		else{
			$user = Lib_Service_User::getUnameByUids(array($chatUid));			
			if(isset($user[$chatUid])){
				$uname = $user[$chatUid];
				//mgetUserData获取昵称字段
				$arrUids = array();
				$arrUids[]  = intval($chatUid);
				$arrInput = array('user_id' => $arrUids);
				$arrOutput = Tieba_Service::call('user', 'mgetUserData', $arrInput, null, null, 'post', 'php', 'utf-8');
				if (!$arrOutput || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
					Bingo_Log::warning(sprintf("call user::mgetUserData failed! input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
				}
				$chatUser = array(
					'uid'      => $chatUid,
					'uname'    => strlen($uname) > 0 ? $uname : $chatUid, //无u用户
					'show_nickname'=> Molib_Util_User::getShowNickname($arrOutput['user_info'][$chatUid]),
					'portrait' => Tieba_Ucrypt::encode($chatUid, $uname),
					'unread'   => 0,
					'lastReadMsgId' => 0,
				);
			}
		}
		$this->_arrTpl['chatUser'] = $chatUser;
		return $this->_renderJson();
	}
}
?>
