<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-04-13
 * @comment get like forum list and level bigger than 6
 * @version 1.0
 */
class getLivableForumListAction extends Lib_Im_Action_Base {
    const DEFAULT_MIN_LEVEL = 6;
    const DEFAULT_PAGE_SIZE = 50;

    protected $intUserId = 0;
	protected $intFilterFlag = 1;
    protected $intMinLevel = 6;
    protected $intLikeForumFlag = 0;
    protected $intPageSize = 50;
    protected $intPageNo = 1;

    protected function _input() {
        $this->intUserId  = intval($this->getInput('userId', 0));
		$this->intFilterFlag  = intval($this->getInput('filterFlag', 1));
        $this->intMinLevel = intval($this->getInput('minLevel', self::DEFAULT_MIN_LEVEL));
        $this->intLikeForumFlag = intval($this->getInput('likeForumFlag', 0));
        $this->intPageSize = intval($this->getInput('pageSize', self::DEFAULT_PAGE_SIZE));
        $this->intPageNo = intval($this->getInput('pageNo', 1));

        if($this->intUserId <= 0 || $this->intFilterFlag < 0 || $this->intMinLevel <= 0 ||
            $this->intLikeForumFlag < 0 || $this->intPageSize <= 0 || $this->intPageNo <= 0) {
            $this->_error(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        return true;
    }

    protected function _process() {
        //�Ƿ��¼У��
        if($this->intUserId <= 0) {
            $this->_error(Tieba_Errcode::ERR_USER_NOT_LOGIN);
            return false;
        }
   
        $arrReq = array(
            'userId' => $this->intUserId,
			'filterFlag' => $this->intFilterFlag,
            'minLevel' => $this->intMinLevel,
            'likeForumFlag' => $this->intLikeForumFlag,
            'pageSize' => $this->intPageSize,
            'pageNo' => $this->intPageNo,
        );

        $arrRes = Lib_Util_Service::call('livegroup', 'getLivableForumList', $arrReq);
        if(!$arrRes) {
            $this->_error(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            Lib_Util_Log::warning("getLivableForumListAction call getLivableForumList  fail! req:".serialize($arrReq).'   res:'.serialize($arrRes));
            return false;
        } else if($arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            $this->_error($arrRes['errno']);
            Lib_Util_Log::warning("getLivableForumListAction call getLivableForumList  fail! req:".serialize($arrReq).'   res:'.serialize($arrRes));
            return false;
        }

		$this->arrData['available'] = $arrRes['available'];
        $this->arrData['minLevel'] = $arrRes['minLevel'];
        $this->arrData['tips'] = $arrRes['tips'];
        $this->arrData['likeList'] = $arrRes['likeList'];
		$this->arrData['hasMore'] = $arrRes['hasMore'];
        Tieba_Stlog::addNode('ispv',1); 
        $this->arrData = Lib_Im_Util::change_key_style($this->arrData);
        return true;
    }
}
