<?php
/**
 * ����Ⱥ�����̴���
 * <EMAIL>
 * 2013/11/30 11:40
**/

function processTask($taskContent){
	$tstart = $taskContent['task_start'];
	$tstop = $taskContent['task_stop'];
	$tid = $taskContent['task_id'];
	$tupdateLib = $taskContent['update_lib'];
	
	// ����־�ļ�
	$tfp = fopen(sprintf("log/task_%d.log", $tid), 'w');
	if(!$tfp){
		Bingo_Log::notice(sprintf("task %d open log file faile!\r\n", $tid));
		exit;
	}

	$_scriptConf = array(
		'memory_limit'   => '512M',
		'lock_file_name' => 'task_' . $tid  . '.lock',
		'done_file_name' => 'task_' . $tid  . '.done',
		'db_alias'       => array(
			'im'     => 'forum_im',
		),
	);
	$tpid = getmypid();
	if(empty($taskContent)){
		$message = sprintf("process %d recive empty task, sucess exit!\r\n", $tpid);
		fwrite($tfp, $message);
		exit;
	}

	//��ʼ��redis 
	$_redis = new Bingo_Cache_Redis('msglogic');
	if($_redis == false || empty($_redis)){
		$message = sprintf("child %d init redis fail!!\r\n", $tpid);
		fwrite($tfp, $message);
		exit;
	}
	$_easyScriptObj = new Lib_Util_EasyScript($_scriptConf);
	$step = 1000;

	while($tstart <= ($tstop-$step)){
		$sql = 'select group_id, member_num, max_member_num, forum_id from im_group_info where group_type in (2,3,4) limit %d,%d';
		$arrRes  = $_easyScriptObj->db_im_query(sprintf($sql, $tstart, $step));
		$resNum = count($arrRes);
		if($resNum == 0) { break;}
		$mayBeHotGroups = array();
		$hotGroups = array();
		$forumGroups = array();
		//���������
		foreach($arrRes as $item){
			$forumGroups[$item['group_id']] = $item;
		}
		// ��������1:1-3����
		foreach($arrRes as $item){
			$diffMember = $item['max_member_num']-$item['member_num'];
			if($diffMember>= 1 && $diffMember<=3){
				$mayBeHotGroups[] = $item['group_id'];
			}
		}
		if(!empty($mayBeHotGroups)){
			$curTime = 0;
			$sql = 'select unix_timestamp()';
			$arrRes  = $_easyScriptObj->db_im_query($sql);
			foreach($arrRes[0] as $tmpKey => $tmpValue){
				$curTime = intval($tmpValue);
			}
			foreach($mayBeHotGroups as $hotGroup){
				//���˵���2:���һ�췢��
				$arrReq = array(
					'group_ids' => strval($hotGroup),
				);
				$arrRes = Lib_Util_Service::call('im', 'queryGroupLastReply', $arrReq,	'remote');
				if(!$arrRes || $arrRes['errno']!==Tieba_Errcode::ERR_SUCCESS ) {
					Lib_Util_Log::warning("call queryGroupLastReply fail." . serialize($arrReq));
					continue;
				}	
				$lastTime = intval($arrRes['last_reply_times'][intval($hotGroup)]);
				if($lastTime + 24*3600 >= $curTime){
					$tmpItem = array();
					$tmpItem['create_time'] = $lastTime;
					$tmpItem['group_id'] = intval($hotGroup);
					$hotGroups[] = $tmpItem;
				}
			}
			

			// д�밴ʱ������Ĵ��
			if(!empty($hotGroups)){
				$arrRedisInput = array();
				$members = array();
				foreach($hotGroups as $hotGroup){
					$tmpMember = array();
					$tmpMember['score'] = $hotGroup['create_time'];
					$tmpMember['member'] = $hotGroup['group_id'];
					$members[] = $tmpMember;;
				}
				$arrRedisInput = array(
					'key' => $tupdateLib,
					'members' => $members,
				);
				$arrRes = $_redis->ZADD($arrRedisInput);
				if(!isset($arrRes['err_no']) || $arrRes['err_no'] != 0){
					$message = sprintf("task %d write large lib data fail:%s\r\n", $tid, serialize($arrRedisInput));
					fwrite($tfp, $message);
				}else{
					$message = sprintf("task %d write large lib data sucess:%s\r\n", $tid, serialize($arrRedisInput));
					fwrite($tfp, $message);
				}
			}
			// д�밴�ɷ����С��
			if(!empty($hotGroups)){
				foreach($hotGroups as $hotGroup){
					$forumId = intval($forumGroups[$hotGroup['group_id']]['forum_id']);
					if($forumId > 0){
						$key = $forumId . FORUMHOTGROUPSUFFIX;
						$members = array(
							array(
								'score' => $hotGroup['create_time'],
								'member' => $hotGroup['group_id'],
							),
						);
						$arrRedisInput = array(
							'key' => $key,
							'members' => $members,
						);
						$arrRes = $_redis->ZADD($arrRedisInput);
						if(!isset($arrRes['err_no']) || $arrRes['err_no'] != 0){
							$message = sprintf("task %d write forum lib data fail:%s\r\n", $tid, serialize($arrRedisInput));
							fwrite($tfp, $message);
						}else{
							$message = sprintf("task %d write forum lib data sucess:%s\r\n", $tid, serialize($arrRedisInput));
							fwrite($tfp, $message);
						}
					}
				}
			}
		}

		// �Ѿ��������
		if($resNum < $step){
			break;
		}
		$message = sprintf("task %d loop pos:%d\r\n", $tid, $tstart);
		fwrite($tfp, $message);
		$tstart += $step;
	}

	$message = sprintf("task %d sucess finish!\r\n", $tid);
	fwrite($tfp, $message);
	fclose($tfp);
	exit;
}

define ('EASYSCRIPT_DEBUG',true);          //debug ģʽ
define ('EASYSCRIPT_THROW_EXEPTION',true);  //���쳣ģʽ
//require_once("EasyScript.php");

define ('SCRIPTNAME',basename(__FILE__,".php"));   //����ű���
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf");
define ('DATAPATH',BASEPATH."/data");
define ('LOGPATH',BASEPATH."/log");
set_include_path(get_include_path() . PATH_SEPARATOR. BASEPATH.'/../../../');

function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');


define ('HOTGROUPLIB_0', 'hot_group_lib_0');
define ('HOTGROUPLIB_1', 'hot_group_lib_1');
define ('HOTGROUPSUSINGLIBINFO',  'hot_groups_using_lib_info');
define ('FORUMHOTGROUPSUFFIX', '_forum_hot_groups');

define ('LOG', 'log');
Bingo_Log::init(array(
     LOG => array(
         'file'  => LOGPATH ."/". SCRIPTNAME. ".log",
         'level' => 0xFF,
     ),
), LOG);

$easyScriptObj  = false;
$ScriptConf = array(
	'memory_limit'   => '512M',
    'data_path'      => DATAPATH,
    'conf_path'      => CONFPATH,
    'lock_file_name' => SCRIPTNAME.'.lock',
    'done_file_name' => SCRIPTNAME.'.done',
    'db_alias'       => array(
		'im'     => 'forum_im',
	),
    'conf_alias'     => array(
		'main'     => 'main.conf',
	),
);
	
// ����־�ļ�
$fp = fopen('./log/runing.log', 'w');
if(!$fp){
	Bingo_Log::notice(' script running faile.');
	exit;
}
$easyScriptObj = new Lib_Util_EasyScript($ScriptConf);
//��ֹ�ű��ظ�ִ��
if( $easyScriptObj->checkScriptIsRuning() === true ){
	Bingo_Log::notice(' script is runing.');
    exit;
}

// ��¼������
$ppid = getmypid();

//��ʼ��redis 
$redis = new Bingo_Cache_Redis('msglogic');
if($redis == false || empty($redis)){
	Bingo_Log::warning('script init redis fail!');
	exit;
}

//ȷ�������Ǹ���
$updateLib = null;
$hotGroupsLibInfo = array();
$arrRedisInput = array(
	'key' => HOTGROUPSUSINGLIBINFO,
);
$arrRes = $redis->GET($arrRedisInput);
if(!isset($arrRes['err_no']) || $arrRes['err_no'] != 0){
	Bingo_Log::warning('redis get hot group using info fail!');
	exit;
}
if($arrRes['ret'][HOTGROUPSUSINGLIBINFO] == null){
	$updateLib = HOTGROUPLIB_0;
}else{
	$hotGroupsLibInfo = unserialize($arrRes['ret'][HOTGROUPSUSINGLIBINFO]);
	if($hotGroupsLibInfo['using_lib'] == HOTGROUPLIB_0){
		$updateLib = HOTGROUPLIB_1;
	}else{
		$updateLib = HOTGROUPLIB_0;
	}
	$arrRedisInput = array(
		'key' => $updateLib,
	);
	$arrRes = $redis->DEL($arrRedisInput);
	if($arrRes == false || $arrRes['err_no'] != 0){
		Bingo_Log::warning('script redis del fail!');
		//exit;
	}
}

// ��Ƭ����
$prcessNum = intval($easyScriptObj->conf_main_get('process_count', 5));
$sql = "select count(group_id) from im_group_info";
$arrRes  = $easyScriptObj->db_im_query($sql);
foreach($arrRes[0] as $key => $value){
	$taskNum = $value;
}
$averageNum = floor($taskNum / $prcessNum);
$taskStart = 0;
$sucessAllocNum = $prcessNum;
for($i = 0; $i < $prcessNum; $i++){
	$taskParam = array(
		'task_start' => $i * $averageNum,
		'task_stop' => $i * $averageNum + $averageNum,
		'task_id'	=> $i,
		'update_lib' => $updateLib,
	);
	$pid = pcntl_fork();
	if($pid == -1){
		$sucessAllocNum--;
		$errmes = sprintf("script fork failed!\r\n");
		sprintf($fp, $mes);
		Bingo_Log::warning($errmes);
	}else if($pid == 0){
		processTask($taskParam);
	}else{
		$errmes = sprintf("script fork process %d sucess!\r\n", $i);
		sprintf($fp, $errmes);
	}
}

// ��������β
if($ppid == getmypid()){
	try{
		while($sucessAllocNum--){
			$ttpid = pcntl_waitpid(-1, $status, WUNTRACED);
			if($ttpid==-1 || !pcntl_wifexited($status)){
				$errmes = sprintf("pcntl_waitpid child process failed! child process id:%d",$ttpid);
				sprintf($fp, $errmes);
				Bingo_Log::warning($errmes);
			}
		}
		//���¿���Ϣ
		$updateTime = time();
		$hotGroupsLibInfo['using_lib'] = $updateLib;
		$hotGroupsLibInfo['using_base'] = 0;
		$hotGroupsLibInfo['switch_time'] = $updateTime;
	
		$arrRedisInput = array(
			'key' => HOTGROUPSUSINGLIBINFO,
			'value' => serialize($hotGroupsLibInfo),
		);
		$arrRes = $redis->SET($arrRedisInput);
		if(!isset($arrRes['err_no']) || $arrRes['err_no'] != 0){
			$errmes = sprintf("script write hot groups using lib info fail:%s\r\n", serialize($arrRedisInput));
			fwrite($fp, $errmes);
		}

		$errmes = sprintf("script update info:\r\n");
		fwrite($fp, $errmes);
		$errmes = sprintf("[using_lib]:%s\r\n", $updateLib);
		fwrite($fp, $errmes);
		$errmes = sprintf("[switch_time]:%s\r\n", $updateTime);
		fwrite($fp, $errmes);

		Bingo_Log::debug('script update info:');
		Bingo_Log::debug('[using_lib]:' . $updateLib);
		Bingo_Log::debug('[switch_time]:' . $updateTime);
		Bingo_Log::debug(sprintf("process %d update info:", getmypid()));

		fclose($fp);
	}catch(Exception $e){
		if($easyScriptObj !== false ){
			Bingo_Log::warning('script run fail!'.$easyScriptObj->getErr2String());
		}else{
			Bingo_Log::warning('script run fail![null]');
		}
		Bingo_Log::notice('script run fail.'.$easyScriptObj->getErr2String());
		exit;
	}
	Bingo_Log::notice('script run success.');
}
