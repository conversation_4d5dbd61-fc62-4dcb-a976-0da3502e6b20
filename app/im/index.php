<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * <AUTHOR>
 * @date 2013-03-19
 * @version 1.0
 */
date_default_timezone_set("Asia/Chongqing");

define('MODULE_NAME', 'im');
function __autoload($strClassName)
{
   	require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');

//init static
define('BINGO_ENCODE_LANG', 'UTF-8');
define('FOR_TEST', 'Off');
define ('MODULE_ORP_BASE_PATH',        dirname(__FILE__)."/../../");                            
define ('MODULE_UI_BASE_PATH',         MODULE_ORP_BASE_PATH.'/app/'.MODULE_NAME);               
define ('MODULE_CONF_BASE_PATH',       MODULE_ORP_BASE_PATH.'/conf/'.MODULE_NAME);             
define ('ACTION_PATH', MODULE_UI_BASE_PATH .'/actions');                                 
//Tieba_Init::init("im");
//pc msg FE代码块
Tieba_Init::init("im","pc/message");
//MODULE_PATH   orp/app/im
//MODULE_ACTION_PATH  orp/app/im/actions
//MODULE_CONF_PATH orp/conf/app/im
//MODULE_LOG_PATH orp/log/app/im
 

// init appid cmd and module
Bingo_Http_Request::init();
Tieba_Stlog::setFileName('wap');

// lcs传递，用于路由
$appid = intval(Bingo_Http_Request::get('appid',''));
$cmd = intval(Bingo_Http_Request::get('cmd',''));
$module_id = intval(($cmd % 100000) / 1000);
$module_cmd = intval($cmd % 1000);

// 根据命令号路由到不同模块
$arrModuleHash = array(
    1 => 'register',
    2 => 'msgcenter',
    3 => 'group',
    4 => 'imuser',
    5 => 'pmsg',
    6 => 'chatroom',
	7 => 'live',
    8 => 'platforum',
	9 => 'pcmsg',
);
$strModule = strval($arrModuleHash[$module_id]);
//if($strModule == "") {
//    $strModule = "unknown";
//}
//Lib_Util_Log::debug("----REQUEST-PARAM---- module=$strModule cmd=$cmd appid=$appid");
//Lib_Util_Log::debug("----SERVER-PARAM---- ".serialize($_SERVER));
//Lib_Util_Log::debug("----GET-PARAM---- ".serialize($_GET));

define('UI_MODULE_NAME', $strModule);
define('UI_MODULE_CMD', $module_cmd);
define('MODULE_LOG_FILE', MODULE_LOG_PATH . "$strModule.ui.log");
define('MONITOR_LOG_FILE', MONITOR_LOG_PATH . "monitor.log"); 
define('UI_ACTION_PATH', ACTION_PATH . DIRECTORY_SEPARATOR . $strModule);
define ('MODULE_DATA_PATH',   MODULE_UI_BASE_PATH . '/data/'); 

//实例化对象，并设置处理Actions的根目录
require_once 'Bingo/Http/Router/Abstract.php';
require_once 'Bingo/Controller/Front.php';
class Im_Http_Router extends Bingo_Http_Router_Abstract
{
    public function getHttpRouter() {
	    $strHttpRouter  = '';
        $strUrl = strip_tags( $_SERVER['REQUEST_URI'] );
        $_intTmpPos = strpos($strUrl, '?');
        if ($_intTmpPos) {
            $strUrl = substr($strUrl, 0, $_intTmpPos);
        }
        $_intTmpPost = strpos($strUrl, '.');
        if ($_intTmpPos) {
            $strUrl = substr($strUrl, 0, $_intTmpPos);
        }
        $strUrl = trim(strtolower($strUrl), '/');
        if($strUrl == 'im/im' || $strUrl == 'im/im/submit'){
	
       	 	$strErrorHttpRouter = 'error';
        	//if (UI_MODULE_NAME == "unknown") {
        	//    Lib_Util_Log::warning('Im_Http_Router error ['.UI_MODULE_NAME.'/'. $strErrorHttpRouter .']');
        	//    return $strErrorHttpRouter;
        	//}

        	$arrCmdMap = Lib_Im_Conf::getModuleConf(UI_MODULE_NAME);
        	if(!is_array($arrCmdMap)) {
            	Lib_Util_Log::warning('client request cmd error, cannot locate module! ['.'module_name='.UI_MODULE_NAME.']');
            	return $strErrorHttpRouter;
        	}
        	$strHttpRouter = strval($arrCmdMap[UI_MODULE_CMD]);
        	if ($strHttpRouter == "") {
            	Lib_Util_Log::warning('client request cmd error, cannot locate action! ['.'module_name='.UI_MODULE_NAME. 'module_cmd='.UI_MODULE_CMD.']');
            	return $strErrorHttpRouter;
        	}
		}
		elseif( $strUrl == 'im/im/pcmsg' || $strUrl == 'im/im/pcmsg/submit' ){
			$strErrorHttpRouter = 'error';
			$arrCmdMap = Lib_Im_Conf::getModuleConf(UI_MODULE_NAME);
			if(!is_array($arrCmdMap)) {
				Lib_Util_Log::warning('client request cmd error, cannot locate module! ['.'module_name='.UI_MODULE_NAME.']');
				return $strErrorHttpRouter;
			}			
			$strHttpRouter = strval($arrCmdMap[UI_MODULE_CMD]);
			if ($strHttpRouter == "") {
				Lib_Util_Log::warning('client request cmd error, cannot locate action! ['.'module_name='.UI_MODULE_NAME. 'module_cmd='.UI_MODULE_CMD.']');
				return $strErrorHttpRouter;
			}
		}
		else{
			        require_once 'Bingo/Http/Router/Pathinfo.php';                                      
            $objRouter = new Bingo_Http_Router_Pathinfo(array(                                  
                'beginRouterIndex' => 1,                                                        
            ));                                                                                 
            Lib_Util_Log::debug('default route!');                                                 
            return $objRouter->getHttpRouter();   
        }

        Lib_Util_Log::debug('----CMD-ROUTER----['.UI_MODULE_NAME.'/'. $strHttpRouter .']');
        Bingo_Http_Request::setHttpRouter($strHttpRouter, array($strHttpRouter));
        return $strHttpRouter;
    }
}

//autoload
require_once 'Bingo/Load.php';
$objLoad = Bingo_Load::getInstance(MODULE_AUTOLOAD_PATH);
foreach($arrModuleHash as $module) {
    $objLoad->add(ACTION_PATH . DIRECTORY_SEPARATOR . "$module");
}

//dispatch
$objFrontController = Bingo_Controller_Front::getInstance(array(
    'actionDir' => UI_ACTION_PATH,
    'httpRouter'         => new Im_Http_Router(),
    'defaultRouter'  => 'error',
    'notFoundRouter' => 'error',
    'beginRouterIndex' => 0,
));

Bingo_Timer::start('total');
try {
    $objFrontController->dispatch();
    $strRouter = Bingo_Http_Request::getStrHttpRouter();
} catch(Exception $e) {
    Lib_Util_Log::warning(sprintf('main process failure! HttpRouter=%s, error[%s] file[%s] line[%s]',
        Bingo_Http_Request::getStrHttpRouter(), $e->getMessage(), $e->getFile(), $e->getLine()));
    //header('location:http://static.tieba.baidu.com/tb/error.html');
}
Bingo_Timer::end('total');

//Lib_Util_Log::buildNotice();
$strTimeLog = Bingo_Timer::toString();
Lib_Util_Log::notice('stat' . ' time[' . $strTimeLog .']'.' request['.$appid. ':' . $cmd.']'.Tieba_Stlog::notice());
