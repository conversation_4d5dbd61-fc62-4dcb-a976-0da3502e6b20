<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-04-07 14:03:45
 * @version
 */

define('BINGO_ENCODE_LANG','UTF-8');
Tieba_Init::init("salaweb", "encourage");

$objFrontController = Bingo_Controller_Front::getInstance(array(
    "actionDir" => MODULE_ACTION_PATH,
    "defaultRouter" => "index",
    "notFoundRouter" => "error",
    "beginRouterIndex" => 1,
    "internalEncode" => "utf-8",
));

Bingo_Timer::start('total');
Bingo_Page::init(array(
    "baseDir" => MODULE_VIEW_PATH,
    "debug" => false,
    "outputType" => ".",
    "isXssSafe" => true,
    "module" => "encourage",
    "useTbView" => true,
    "viewRootpath" => MODULE_VIEW_PATH . "/../",
));

try{
    $objFrontController->dispatch();
}catch(Exception $e){
    Bingo_Log::warning(sprintf('main process failure!HttpRouter=%s,error[%s]file[%s]line[%s]',
        Bingo_Http_Request::getStrHttpRouter(), $e->getMessage(), $e->getFile(), $e->getLine()));
    Bingo_Page::setErrno($intErrNo = $e->getCode() ? : Tieba_Errcode::ERR_UNKOWN);
    if(preg_match('#^api\.#', $strHost = Bingo_Http_Request::getServer('HTTP_HOST'))) {
        Bingo_Http_Response::contextType('application/json');
        Bingo_Page::setOnlyDataType("json");
        Bingo_Page::assign([
            'errno' => $intErrNo,
            'errmsg' => $e->getMessage() ? :Tieba_Error::getErrmsg($intErrNo),
        ]);
    } else {
        Bingo_Http_Response::location("http://$strHost/error.html");
    }
}

Bingo_Timer::start('build_page');
Bingo_Page::buildPage();
Bingo_Timer::end('build_page');
Bingo_Timer::end('total');

$strTimeLog = Bingo_Timer::toString();
Bingo_Log::pushNotice('Timer','['.$strTimeLog.']');
Bingo_Log::buildNotice();
