<?php
/***************************************************************************
 * 
 * Copyright (c) 2011 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file newCommitAction.php
 * <AUTHOR>
 * @date 2011/07/20 22:33:00
 * @brief 
 *  
 **/



class jubaoCommitAction extends TousuCommitBaseAction {

    protected $_cmd = 'jubao_commit';

    protected function _config() {
        //ȷ����Ҫ�Ĳ���
        $this->_arrFormNeed = array(
            'jubao_thread_id' => 'thread_id',
            'jubao_post_id' => 'post_id',
            'jubao_floor' => 'floor',
            'jubao_type' => 'type'
        );
    }

    protected function _process() {

        if($this->_arrLoginUser['id'] == 0 && $this->_arrLoginUser['name'] == '') {
            $this->error(JubaoErrno::NOT_LOGIN, JubaoErrmsg::NOT_LOGIN);
            Bingo_Log::warning("user loginout");
            return false;
        }
        //�����֤��
        $vcode = trim(Bingo_Http_Request::get('vcode',''));
        $vcode_md5 = trim(Bingo_Http_Request::get('vcode_md5',''));
        $ip = Bingo_Http_Ip::ip2long(Bingo_Http_Ip::getConnectIp());
        if( !CommonCheck::checkJubaoVcode($ip, Bingo_Http_Request::get('fid',0),
            $this->_arrLoginUser['id'], $vcode_md5, $vcode)){
            $this->error(JubaoErrno::VCODE_ERROR, JubaoErrmsg::VCODE_ERROR);
            Bingo_Log::warning("vcode error");
            return false;
        }		
        
        //Ͷ����Id/Ip���
        if(!UserInfo::isLegaluserId($this->_arrLoginUser)) {
            $this->error(JubaoErrno::ID_BAN, JubaoErrmsg::ID_BAN);
            Bingo_Log::warning("user id tousu ban[user id:{$this->_arrLoginUser['id']}]");
            return false;
        }
        if(!UserInfo::isLegalUserIp($this->_arrLoginUser)) {
            $this->error(JubaoErrno::IP_BAN, JubaoErrmsg::IP_BAN);
            Bingo_Log::warning("user ip tousu ban[user ip:{$this->_arrLoginUser['ip']}]");
            return false;
        }

        //Ϊ�ύ�����ݿ�׼������
        $arrInput = self::_getSqlData();
        if($arrInput === false) {
            return false;
        }

        //ȫ�ֻ���ڷ��
        if(!UserInfo::isLegalUser($this->_arrLoginUser, $arrInput['jubao_tieba_id'])) {
            $this->error(JubaoErrno::USER_BAN, JubaoErrmsg::USER_BAN);
            Bingo_Log::warning("user global or forum ban");
            return false;
        }


        //ȥ��
        $isSign = RpcIdlTousu::call('getJubaoSign', $arrInput);
        if($isSign > 0) {
            $this->error(JubaoErrno::REPEAT_JUBAO, JubaoErrmsg::REPEAT_JUBAO);
            Bingo_Log::warning("repeat sign");
            return false;
        }

        $arrNeedInt = array('id', 'post_uid', 'post_uip', 'post_time', 'status', 'is_vip', 
                            'op_uid', 'op_time', 'op_reply', 'jubao_tieba_id', 'jubao_thread_id',
                            'jubao_post_id', 'jubao_uid', 'jubao_uip', 'jubao_post_time',
                            'jubao_count', 'jubao_type', 'jubao_floor');
        foreach($arrNeedInt as $val) {
            if(isset($arrInput[$val])) {
                $arrInput[$val] = intval($arrInput[$val]);
            }
        }

        //���ȿ���
        if($arrInput['is_vip'] == 0){
	        
            if(!UserInfo::isLegalActs($this->_arrLoginUser, 2)) {
	            $this->error(JubaoErrno::ACTS_CTRL_FAIL, JubaoErrmsg::ACTS_CTRL_FAIL);
	            Bingo_Log::warning("user refused by acts_ctrl");
	            return false;
	        }

        }

        //tbs���
        if(!Tieba_Tbs::check($_REQUEST['tbs'], true)) {
            $this->error(JubaoErrno::TBS_FAIL, JubaoErrmsg::TBS_FAIL);
            Bingo_Log::warning("tbs fail");
            return false;
        }
       

        ////Add by Zhp for δͨ��ǰ̨js��֤��ֱ�����ύ�ӿڵ����  2012-08-30 16:26:53
        //$arrValidInfo = PuserInfo::getInfoByuid($this->_arrLoginUser['id'],array('email', 'securemobil'));
        //if(intval($arrValidInfo['securemobil']) > 0){
        //    //pass
        //}else{
        //    $this->error(JubaoErrno::COMMIT_USER_FAIL, JubaoErrmsg::COMMIT_USER_FAIL);
        //    Bingo_Log::warning("commit without html fail");
        //    return false;
        //}
        
        //�ж��Ƿ����˾ٱ���
        if($jubaoId = RpcIdlTousu::call('getJubaoId', $arrInput)) {
            $arrInput['id'] = $jubaoId;
            if(false === RpcIdlTousu::call('addJubaoCount', $arrInput)) {
                $this->error(JubaoErrno::ADD_COUNT_FAIL, JubaoErrmsg::ADD_COUNT_FAIL);
                Bingo_Log::warning("add count fail");
                return false;
            }
            if($arrInput['post_uid'] == $arrInput['jubao_uid']) {
                if(RpcIdlTousu::call('getJubaoPostUid', $arrInput) != $arrInput['jubao_uid']) {
                    if(false === RpcIdlTousu::call('updateJubaoMain', $arrInput)) {
                        $this->error(JubaoErrno::UPDATE_FAIL, JubaoErrmsg::UPDATE_FAIL);
                        Bingo_Log::warning("update fail");
                        return false;
                    }
                }
            }
        }
        else {
            $arrInput['id'] = RpcIdlClubcm::getId('tousu_id');
            if($arrInput['id'] == 0) {
                $this->error(JubaoErrno::ID_FAIL, JubaoErrmsg::ID_FAIL);
                Bingo_Log::warning("get id error");
                return false;
            }
            if(false === RpcIdlTousu::call('commitJubao', $arrInput)) {
                $this->error(JubaoErrno::COMMIT_FAIL, JubaoErrmsg::COMMIT_FAIL);
                Bingo_Log::warning("commit fail");
                return false;
            }
        }

        //�������ݿ��jubao_user_list
        $arrInput['jubao_id'] = $arrInput['id'];
        $arrInput['created_time'] = $arrInput['post_time'];
        if(false === RpcIdlTousu::call('addJubaoUser', $arrInput)) {
            $this->error(JubaoErrno::COMMIT_USER_FAIL, JubaoErrmsg::COMMIT_USER_FAIL);
            Bingo_Log::warning("commit user fail");
            return false;
        }
        $this->_arrRet = array('id' => $arrInput['id']);
        $this->_arrStat['id'] = $arrInput['id'];
        $this->_arrStat['ip'] = $this->_arrLoginUser['ip'];
        $this->_arrStat['fid'] = $arrInput['jubao_tieba_id'];
        $this->_arrStat['fname'] = $arrInput['jubao_tieba'];
        sleep(1);
        return true;
    }

    private function _getSqlData() {
        $arrInput = array(
            'id' => 0,
            'sign' => '',
            'post_uname' => '',
            'post_uid' => 0,
            'post_uip' => 0,
            'post_time' => 0,
            'status' => 0,
            'is_vip' => 0,
            'op_uname' => '',
            'op_uid' => 0,
            'op_time' => 0,
            'op_reply' => 0,
            'jubao_tieba' => '',
            'jubao_tieba_id' => 0,
            'jubao_title' => '',
            'jubao_thread_id' => 0,
            'jubao_post_id' => 0,
            'jubao_content' => '',
            'jubao_uname' => '',
            'jubao_uid' => 0,
            'jubao_uip' => 0,
            'jubao_post_time' => 0,
            'jubao_count' => 1,
            'jubao_type' => 0, 'jubao_floor' => 0
        );
        foreach($this->_arrForm as $key => $value) {
            $arrInput[$key] = $value;
        }
        if($arrInput['jubao_type'] < 1 || $arrInput['jubao_type'] > 5) {
            $this->error(JubaoErrno::TYPE_INVALID, JubaoErrmsg::TYPE_INVALID);
            Bingo_Log::warning("invalid jubao_type");
            return false;
        }
        $arrInput['post_uname'] = $this->_arrLoginUser['name'];
        $arrInput['post_uid'] = $this->_arrLoginUser['id'];
        $arrInput['post_uip'] = $this->_arrLoginUser['ip'];
        $arrInput['post_time'] = time();

        require_once('Rpc/Pblogic.php');
        $postInfo = Rpc_Pblogic::getPosts($arrInput['jubao_thread_id'], $arrInput['jubao_post_id'], 0, 1);
        if($postInfo['err_no'] != 0 || $postInfo['post_infos'][0]['post_id'] != $arrInput['jubao_post_id']) {
            $this->error(JubaoErrno::POST_NOT_EXIST, JubaoErrmsg::POST_NOT_EXIST);
            Bingo_Log::warning("get post info error");
            return false;
        }
        if($arrInput['jubao_floor'] != $postInfo['post_infos'][0]['post_no']) {
            $this->error(JubaoErrno::FLOOR_INVALID, JubaoErrmsg::FLOOR_INVALID);
            Bingo_Log::warning("invalid floor");
            return false;
        }
        $arrInput['jubao_tieba'] = $postInfo['keyword'];
        $arrInput['jubao_tieba_id'] = $postInfo['forum_id'];
        $arrInput['jubao_title'] =self::filterGbkChars($postInfo['post_infos'][0]['title'], '&#8364;'); 
        $arrInput['jubao_content'] =self::filterGbkChars($postInfo['post_infos'][0]['content'], '&#8364;');
        $arrInput['jubao_uname'] = $postInfo['post_infos'][0]['username'];
        if(isset($postInfo['post_infos'][0]['uid'])) {
            $arrInput['jubao_uid'] = $postInfo['post_infos'][0]['uid'];
        }
        /*
        else {
            $userInfo = RpcIdlFucenter::getUserInfoByUname($arrInput['jubao_uname']);
            if(false !== $userInfo) {
                $arrInput['jubao_uid'] = $userInfo[];
            }
        }
         */
        $arrInput['jubao_uip'] = $postInfo['post_infos'][0]['ip'];
        $arrInput['jubao_post_time'] = $postInfo['post_infos'][0]['create_time'];
        if(CommonCheck::isLegalVip($arrInput['post_uid'], $arrInput['post_uip'], $arrInput['jubao_tieba_id'])) {
            $arrInput['is_vip'] = 1;
        }elseif(CommonCheck::isLegalVipGov($arrInput['post_uid'], $arrInput['post_uip'], $arrInput['jubao_tieba_id'])) {
            $arrInput['is_vip'] = 2;
        }
    
        $checkIsComment =false;
        $comment_id = intval(Bingo_Http_Request::get('comment_id'));
        $arrInput['jubao_comment_id']=$comment_id;
        //���¥��¥comment_id��Ϊ��
        if($comment_id > 0)
        {
                //��֤�������ǲ���¥��¥
                $res=$this->_checkIsComment($arrInput['jubao_thread_id'],$comment_id);
                //������ص������飬����cid��Ϊ0,����ȷ������¥��¥�����ӵľٱ�
                if(is_array($res)&& intval($res["cid"])>0)
                {
                    $arrInput['jubao_content']=self::filterGbkChars($res['content'],'&#8364;');
                    $arrInput['jubao_uname']=$res['username'];
                    $arrInput['jubao_uid']=$res['userid'];
                    $arrInput['jubao_uip']=$res['userip'];
                    $arrInput['jubao_post_time']=$res['posttime'];
                    //���Ӷ�jubao_comment_id����ֶε����
                    $arrInput['jubao_comment_id']=$comment_id;
                    $checkIsComment=true;
                }
        }
        //�����true,��ô����sign�ķ�ʽ�ͻ��,Ҳ���ǰɾٱ���cid�ӽ�ȥ����md5
        if($checkIsComment)
        {
            $signKey=$arrInput['post_uid'].$arrInput['jubao_post_id'].$arrInput['jubao_type'].$arrInput['jubao_comment_id'];
        }else 
        {
            $signKey = $arrInput['post_uid'].$arrInput['jubao_post_id'].$arrInput['jubao_type'];
        }
        $arrInput['sign'] = md5($signKey);
        return $arrInput;
    }

    
    /**
     * form baike
     *
     */
    public static function filterGbkChars($str, $escapeEuroWith = "\x01\x01") {
    	$strout = '';
    	$len = strlen($str);
    
    	for ($i = 0 ; $i < $len; ++$i) {
    		$chrval = ord($str[$i]);
    		// ���ֽ�
    		if ($chrval < 0x80) {
    			if ($chrval === 0x0A) {
    				$strout .= ' ';
    			} else if ($chrval === 0x09) {
    				$strout .= ' ';
    			} else if (ctype_print($str[$i])) {
    				$strout .= $str[$i];
    			}
    		}
    		// ŷԪ�ֽ�
    		elseif ($chrval === 0x80) {
    			$strout .= $escapeEuroWith;
    		}
    		// ���ֽ�
    		else {
    			if ($i + 1 >= $len) {
    				break; // �������
    			}
    			$strout .= $str[$i] . $str[++$i];
    		}
    	}
    
    	return $strout;
    }

    protected function _checkIsComment($tid,$cid)
    {
        require "Rpc/PostComment.php";
        $res=Rpc_PostComment::getOneComment($tid,$cid);
        return $res;
    }  
    
}



/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
