<?php
$filecontent = file_get_contents($argv[1]);
$filecontent = str_replace("\r","",$filecontent);
$arr_config = explode("\n",$filecontent);

foreach($arr_config as $props) {
	if (substr($props,0,1) == '#' || strlen($props)< 2 ){
		continue;
	}
	$keys = explode("\t",$props);
	//$keys = preg_split("/[\s]+/",$props);
	$field['price'] = $keys[0];
	$field['remain'] = $keys[2];
	$field['score'] = $keys[6];
	$field['crowdtest_score'] = $keys[7];
	$field['name'] = $keys[8];
	$field['description'] = $keys[9];
	$field['type'] = $keys[11];
	$field['recommend'] = $keys[12];
	$field['gid'] = intval($keys[13]);
	$field['img_url1'] = $keys[14];
	$field['img_url2'] = $keys[15];
	$field['status'] = 1;


	$field['provider'] = "贴吧";
	$field['provider_detail_url'] = "local";
	$field['gift_detail_url'] = "local";

	//排行榜奖品，不参与兑奖
	if ($field['gid'] >= 3000) {
		$field['status'] = 0;
	}

	
	if (!$field['img_url2'] || !$field['img_url1']) {
		$field['status'] = 0;
	}

	//print_r($field);
	if ($field['gid'] > 3000){
		$ret = Tieba_Service::call('tbguess', 'insertPrizeOnDup', array('gift' => $field), NULL, NULL, 'post', 'php', 'utf-8');
	}
	else {
		$ret = Tieba_Service::call('tbguess', 'insertGiftOnDup', array('gift' => $field), NULL, NULL, 'post', 'php', 'utf-8');
	}
	print_r($ret);
}


