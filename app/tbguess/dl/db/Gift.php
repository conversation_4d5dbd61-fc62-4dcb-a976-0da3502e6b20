<?php
/**
 * User: <EMAIL>
 * Date: 2016-06-04
 * Time: 20:50
 */

class Dl_Db_Gift extends Dl_Db_Model {

    const DB_TABLE = 'gift';
    const DB_NAME  = 'forum_tbguess';
    private static $_arrDB;
    private static $_instance;

    private static $arrField = array(
        'id',
        'gid',
        'name',
        'type',
        'description',
        'score',
        'crowdtest_score',
        'img_url1',
        'img_url2',
        'remain',
        'status',
        'op_time',
        'price',
        'recommend',
        'provider',
        'provider_detail_url',
        'gift_detail_url',
        'weight',
        't_code_id',
        't_code_name',
        'code_intro',
        'code_link',
        'ordinary_code',
    );

    /**
     * @brief  get the database instance
     * @param  null
     * @return object [database instance]
     */
    public static function getModel(){
        $db_name = self::DB_NAME;
        $db_table= self::DB_TABLE;
        $arrKey  = $db_name.$db_table;
        if(isset(self::$_arrDB[$arrKey])){
            return self::$_arrDB[$arrKey];
        }else{
            $class_name = __CLASS__;
            self::$_arrDB[$arrKey] = new $class_name($db_table,$db_name);
            return self::$_arrDB[$arrKey];
        }
    }

    /**
     * @brief  select operation
     * @param  array
     * @return mixed
     */
    public static function select($arrInput){
        if (!$arrInput['field']) {
            $arrInput['field'] = self::$arrField;
        }
        self::$_instance = self::getModel();
        $arrRet = self::$_instance->baseSelect($arrInput);
        foreach($arrRet['data'] as &$arrDbRet){
            if(isset($arrDbRet['api_list'])){
                $arrDbRet['api_list']	= unserialize($arrDbRet['api_list']);
            }
        }
        return $arrRet;
    }

    /**
     * @brief  update operation
     * @param  array
     * @return mixed
     */
    public static function update($arrInput){
        self::$_instance = self::getModel();
        if(isset($arrInput['field']['api_list']) && is_array($arrInput['field']['api_list'])){
            $arrInput['field']['api_list']	= serialize($arrInput['field']['api_list']);
        }
        $ret = self::$_instance->baseUpdate($arrInput);
        return $ret;
    }

    /**
     * @brief  insert operation
     * @param  array
     * @return mixed
     */
    public static function insert($arrInput){
        self::$_instance = self::getModel();
        if(isset($arrInput['field']['api_list']) && is_array($arrInput['field']['api_list'])){
            $arrInput['field']['api_list']	= serialize($arrInput['field']['api_list']);
        }
        $arrRet	= self::$_instance->baseInsert($arrInput);
        return $arrRet;
    }


    /**
     * @brief  delete operation
     * @param  array
     * @return mixed
     */
    public static function deleteRecord($arrInput){
        self::$_instance = self::getModel();
        return self::$_instance->baseDelete($arrInput);
    }

    /**
     * @brief  customize query
     * @param  query[string]
     * @return array
     */
    public static function uQuery($strFields, $strConds = null, $strAppend = null) {
        $query = "SELECT $strFields FROM " . self::DB_TABLE;
        if ($strConds) {
            $query = "$query WHERE $strConds ";
        }
        if ($strAppend) {
            $query = "$query $strAppend";
        }
        self::$_instance = self::getModel();
        return self::$_instance->query($query);
    }

    /**
     * @brief  return the last SQL in DB
     * @param  $arrInput
     * @return array
     */
    public static function getLastSQL($arrInput) {
        self::$_instance = self::getModel();
        return self::$_instance->baseGetLastSQL($arrInput);
    }
}