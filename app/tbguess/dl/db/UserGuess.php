<?php
/**
 * User: <EMAIL>
 * Date: 16/5/31
 * Time: 上午10:42
 */

class Dl_Db_UserGuess extends Dl_Db_Model {

    const DB_TABLE = 'user_guess_wish';
    const DB_NAME  = 'forum_tbguess';
    private static $_arrDB;
    private static $_instance;

    private static $arrField = array(
        'id',
        'user_id',
        'category_id',
        'match_id',
        'a_scores',
        'b_scores',
        'draw_scores',
        'win_scores',
        'status',
        'create_time',
        'last_time',
    );

    /**
     * @brief  get the database instance
     * @param  null
     * @return object [database instance]
     */
    public static function getModel(){
        $db_name = self::DB_NAME;
        $db_table= self::DB_TABLE;
        $arrKey  = $db_name.$db_table;
        if(isset(self::$_arrDB[$arrKey])){
            return self::$_arrDB[$arrKey];
        }else{
            $class_name = __CLASS__;
            self::$_arrDB[$arrKey] = new $class_name($db_table,$db_name);
            return self::$_arrDB[$arrKey];
        }
    }

    /**
     * @brief  select operation
     * @param  array
     * @return mixed
     */
    public static function select($arrInput){
        if (!$arrInput['field']) {
            $arrInput['field'] = self::$arrField;
        }
        self::$_instance = self::getModel();
        $arrRet = self::$_instance->baseSelect($arrInput);
        foreach($arrRet['data'] as &$arrDbRet){
            if(isset($arrDbRet['api_list'])){
                $arrDbRet['api_list']	= unserialize($arrDbRet['api_list']);
            }
        }
        return $arrRet;
    }

    /**
     * @brief  update operation
     * @param  array
     * @return mixed
     */
    public static function update($arrInput){
        self::$_instance = self::getModel();
        if(isset($arrInput['field']['api_list']) && is_array($arrInput['field']['api_list'])){
            $arrInput['field']['api_list']	= serialize($arrInput['field']['api_list']);
        }
        $ret = self::$_instance->baseUpdate($arrInput);
        return $ret;
    }

    /**
     * @brief  insert operation
     * @param  array
     * @return mixed
     */
    public static function insert($arrInput){
        self::$_instance = self::getModel();
        if(isset($arrInput['field']['api_list']) && is_array($arrInput['field']['api_list'])){
            $arrInput['field']['api_list']	= serialize($arrInput['field']['api_list']);
        }
        $arrRet	= self::$_instance->baseInsert($arrInput);
        return $arrRet;
    }


    /**
     * @brief  delete operation
     * @param  array
     * @return mixed
     */
    public static function deleteRecord($arrInput){
        self::$_instance = self::getModel();
        return self::$_instance->baseDelete($arrInput);
    }

    /**
     * @brief  customize query
     * @param  query[string]
     * @return array
     */
    public static function uQuery($strFields, $strConds = null, $strAppend = null) {
        $query = "SELECT $strFields FROM " . self::DB_TABLE;
        if ($strConds) {
            $query = "$query WHERE $strConds ";
        }
        if ($strAppend) {
            $query = "$query $strAppend";
        }
        self::$_instance = self::getModel();
        return self::$_instance->query($query);
    }
}