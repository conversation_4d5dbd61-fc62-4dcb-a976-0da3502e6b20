<?php
/**
 * User: <EMAIL>
 * Date: 2016-06-06
 * Time: 9:28
 */

class Service_European_ConsumePath extends Service_Libs_Base {
    /**
     * @param  $arrInput
     * @return array
     */
    public static function modifyCurrencyOfConsumePath($arrInput) {
        if (empty($arrInput['pid']) || empty($arrInput['currency'])) {
            Bingo_Log::warning(sprintf("param error. [input=%s]", serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrParam = array(
            'field' => array(
                'currency' => $arrInput['currency'],
            ),
            'cond' => array(
                'pid' => intval($arrInput['pid']),
            ),
        );
        $arrRet = Dl_Db_ConsumePath::update($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("modify the currency of consume path failed. [input=%s][ouput=%s]",
                serialize($arrParam), serialize($arrRet)));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
    }
}