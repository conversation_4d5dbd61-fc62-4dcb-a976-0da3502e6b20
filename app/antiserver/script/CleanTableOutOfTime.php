<?php
// naf定时清理antiserver以及unihandle相关库的过时表
// zengfanhua 20151130 解决naf现在每天4:30，一堆并发删表请求，造成mysql监控报警的问题
//20160728 增加对forum_ueg_track_post库的处理，包括删旧表和当前表的旧数据
//20160826 增加对forum_ueg_acts库的处理，包括删旧表
define ( 'NAME_SCRIPT', 'Script_CleanTableOutOfTime' );

Bingo_Log::init ( array (
    'log' => array (
        'file' => dirname ( __FILE__ ) . '/../../../log/antiserver/' . NAME_SCRIPT . '.log',
        'level' => 0x04 | 0x02 
    ) 
), 'log' );

class Script_CleanTableOutOfTime {
    protected static $_db = null; // 三个数据库用同一个变量吧
                                  // 多个数据库的ral Name
    const DB_ANTI_DBRALNAME = 'db_forum_ueg_anti';
    const DB_DEBUG_DBRALNAME = 'db_forum_ueg_debug';
    const DB_HLOGIC_DBRALNAME = 'db_handlelogic';
    //新搭建的专门存原来db_forum_ueg_debug中debug_post表的数据库
    const DB_POST_DEBUG_DBRALNAME='db_forum_ueg_post_debug';    
    //新搭建的专门存原来db_forum_ueg_anti中monitor表的数据库
    const DB_UEG_MONITOR_DBRALNAME='DB_forum_ueg_monitor';
    //新搭建的用户帖子删帖、恢复追踪的数据库
    const DB_UEG_TRACK_POST='db_forum_ueg_track_post';
    //新搭建的事前粒度控制策略相关数据库
    const DB_UEG_ACTS='db_forum_ueg_acts';
    
    //20160826 增加对forum_ueg_acts库的处理，包括删旧表
    
    const DIR_CONF_NAF = '/app/antiserver/naf/runtime/forum/';
    private static $allTablePrefix; // 所有要处理的表前缀信息
    private static $delcount = 0;
    private static $failcount = 0;
    private static $sleeptime = 5; // 默认睡眠时间5s
    private static $trackpostday=3; //forum_ueg_track_post天表保存的天数
    private static $acts_ctrl_day=5; //forum_ueg_track_post天表保存的天数
    
    private static $groups = array (
        "anti",
        //"kuang",
        "newdev",
        //"lukuang",
        //"zhongtudao",
        "other",
        //"yun",
        //"zhida_im",
        //"general",
        //"lbs",
        //"nuomi",
        //"tbmall",
        //"zhida",
        "main"
    );
    //注意$groups数组中，main一定要放最后，因为多个配置文件中均有表的时间配置，但以main中的为准
    
    private static $handleLogicTables = array (
        "handle_history_" 
    ) // 这个表目前保留10天的
;
    
    private static function _getDB($dbname) {
        if (self::$_db) {
            return self::$_db;
        }
        self::$_db = new Bd_DB ();
        if (self::$_db == null) {
            Bingo_Log::notice ( "new bd_db fail." );
            return null;
        }
        $r = self::$_db->ralConnect ( $dbname );
        
        if (! $r) {
            Bingo_Log::notice ( "bd db ral connect fail." );
            self::$_db = null;
            return null;
        }
        return self::$_db;
    }
    
    // 以便下次使用数据库时，连接是新的
    private static function _getNewDB($dbname) {
        self::$_db = null;
        return self::_getDB ( $dbname );
    }
    
    private static function Init() {
        self::$allTablePrefix = array ();
        foreach ( self::$groups as $group ) {
            $confPath = self::DIR_CONF_NAF . "$group";
            $tempConf = Bd_Conf::getConf ( $confPath );
            if (! is_array ( $tempConf )) {
                Bingo_Log::warning ( "get conffail:" . $group );
                continue;
            }
            $tableInfo = $tempConf ['component'] ['Naf_Component_Forum_Dealer'] ['tableInfo'];
            if (! is_array ( $tableInfo )) {
                Bingo_Log::warning ( "get table info fail:" . $group );
                continue;
            }
            foreach ( $tableInfo as $tablekey => $table ) {
                if ($table ['partition'] != "time" || $table ['partition_param'] != "antiCommitTime" || $table ['maxTableNum'] < 1) {
                    Bingo_Log::warning ( "tableinfo error:" . serialize ( $table ) );
                    continue;
                }
                
                self::$allTablePrefix [$tablekey] = $table ['maxTableNum'];
            }
        }
        
        if (empty ( self::$allTablePrefix )) {
            var_dump ( "allTablePrefix is empty" );
            return false;
        }
        var_dump ( "End of Init" );
        return true;
    }
    
    // 清除过期的表，传入表名，要保存的最大表数量,以及数据库连接
    static function _clean_old_table($tableName, $maxTableNum, $dbRalName) {
        
        if (empty ( $tableName ) || $maxTableNum < 1) {
            Bingo_Log::warning ( "_clean_old_table para error {$tableName}" );
            return false;
        }
        //
        if ($maxTableNum < 2) {
            Bingo_Log::warning ( "table {$tableName} table num {$maxTableNum} too few" );
            $maxTableNum = 2;
        }
        
        $db = self::_getNewDB ( $dbRalName );
        if ($db == null) {
            Bingo_Log::warning ( "get DB fail, ral={$dbRalName}, table={$tableName} " );
            return false;
        }
        Bingo_Log::notice ( "table name: " . $tableName );
        $tables = $db->getTables ( $tableName . '_%' ); //下划线为任意一个字符;
        
        $now = ( int ) gettimeofday ( true );
        foreach ( $tables as $old_table ) {
            $date = substr ( $old_table, strlen ( $tableName ) );
            if (empty ( $date )) {
                Bingo_Log::warning ( "table {$tableName} date is empty" );
                continue;
            }
            if (! is_numeric ( $date ) || strlen ( $date ) != 8) {
                // 不是对应的表，跳过即可
                Bingo_Log::warning ( "table {$tableName} date is error" );
                continue;
            }
            $time = strtotime ( $date );
            if (empty ( $time )) {
                Bingo_Log::warning ( "table {$tableName} date is error" );
                continue;
            }
            if ($time < $now - ($maxTableNum) * 86400) {
                $db = self::_getNewDB ( $dbRalName );
                if ($db == null) {
                    Bingo_Log::warning ( "get DB fail drop table fail, ral={$dbRalName}, table={$old_table}" );
                    self::$delcount ++;
                    continue;
                }

                
                $sql = "drop table if exists `$old_table`";
                echo $sql . "\n";
                $db->query ( $sql );
                
                if ($db->errno () != 0) {
                    Bingo_Log::warning ( "drop table[$old_table] failed, sql[$sql] errno[" . $db->errno () . "] error[" . $db->error () . "]" );
                    self::$failcount ++;
                    self::$sleeptime = 30; // 如果此次sql执行失败(多半是因为ral超时)，那么就多sleep会
                } else {
                    Bingo_Log::notice ( "drop table [$old_table] success, sql [$sql] " );
                    self::$delcount ++;
                    self::$sleeptime = 5;
                }
                // 每执行一次drop，sleep下 sadf啊dsg
                Bingo_Log::notice ( "drop one table sleep  start :" . self::$sleeptime );
                sleep ( self::$sleeptime );
                Bingo_Log::notice ( "drop one table sleep  end :" . self::$sleeptime );
            
            }
        }
        return;
    }
    
    public static function run() {
        if (! self::Init ()) {
            Bingo_Log::warning ( "Init Fail" );
            echo "Init fail";
            return false;
        }
        echo "Begin to proces " . self::DB_ANTI_DBRALNAME . "\n";
        Bingo_Log::notice ( "Begin to proces :" . self::DB_ANTI_DBRALNAME . "*************" );
        foreach ( self::$allTablePrefix as $key => $day ) {
            self::_clean_old_table ( $key, $day, self::DB_ANTI_DBRALNAME );
        }
        
        echo "Begin to proces " . self::DB_DEBUG_DBRALNAME . "\n";
        Bingo_Log::notice ( "Begin to proces :" . self::DB_DEBUG_DBRALNAME . "*************" );
        foreach ( self::$allTablePrefix as $key => $day ) {
            if ($key == 'debug_monitor' || $key == 'debug_post') {
                Bingo_Log::notice ( "table ".$key."  keep day is ".$day );
                self::_clean_old_table ( $key, $day, self::DB_DEBUG_DBRALNAME );
            }
        }
        
        echo "Begin to proces :" . self::DB_HLOGIC_DBRALNAME . "\n";
        Bingo_Log::notice ( "Begin to proces :" . self::DB_HLOGIC_DBRALNAME . "*************" );
        foreach ( self::$handleLogicTables as $tablename ) {
            self::_clean_old_table ( $tablename, 10, self::DB_HLOGIC_DBRALNAME );
        }
        
        echo "Begin to proces " . self::DB_POST_DEBUG_DBRALNAME . "\n";
        Bingo_Log::notice ( "Begin to proces :" . self::DB_POST_DEBUG_DBRALNAME . "*************" );
        foreach ( self::$allTablePrefix as $key => $day ) {
            if ($key == 'debug_post' || $key == 'offline_monitor') {
                Bingo_Log::notice ( "table ".$key."  keep day is ".$day );
                self::_clean_old_table ( $key, $day, self::DB_POST_DEBUG_DBRALNAME );
            }
        }
        
        
        echo "Begin to proces " . self::DB_UEG_MONITOR_DBRALNAME . "\n";
        Bingo_Log::notice ( "Begin to proces :" . self::DB_UEG_MONITOR_DBRALNAME . "*************" );
        foreach ( self::$allTablePrefix as $key => $day ) {
            if ($key == 'monitor') {
                Bingo_Log::notice ( "table ".$key."  keep day is ".$day );
                self::_clean_old_table ( $key, $day, self::DB_UEG_MONITOR_DBRALNAME );
            }
        }

        echo "Begin to proces " . self::DB_UEG_TRACK_POST . "\n";
        Bingo_Log::notice ( "Begin to proces :" . self::DB_UEG_TRACK_POST . "*************" );
        self::_clean_old_table ( "post_", self::$trackpostday, self::DB_UEG_TRACK_POST );
        
        echo "Begin to proces " . self::DB_UEG_ACTS . "\n";
        Bingo_Log::notice ( "Begin to proces :" . self::DB_UEG_ACTS . "*************" );
        self::_clean_old_table ( "acts_hit_", self::$acts_ctrl_day, self::DB_UEG_ACTS );
        
        echo "Total delete table " . self::$delcount . "\n";
        
        echo "Fail count " . self::$failcount . "\n";
        return true;
    }
}
echo "start script \n";
Bingo_Log::notice ( "START[" . NAME_SCRIPT . "]" );
$CleanObject = new Script_CleanTableOutOfTime ();
$CleanObject->run ();

Bingo_Log::notice ( "END[" . NAME_SCRIPT . "]" );
echo "end script\n";

?>

