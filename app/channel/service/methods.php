<?php
$methods_to_subservices =
array(
    "insertDocument" => "elasticsearch",
    "getDocument"    => "elasticsearch",
    "deleteDocument" => "elasticsearch",
    "updateDocument" => "elasticsearch",
    "searchDocument" => "elasticsearch",
    "updateDocumentByTid" => "elasticsearch",
    "insertDocumentByTid" => "elasticsearch",
    "getThreadListFromES" => "elasticsearch",
    "computeThreadScore"  => "elasticsearch",
    "searchDocumentWithScrollInitial"   => "elasticsearch",
    "searchDocumentWithScrollSubseq"    => "elasticsearch",
    "nmqThreadSyncChannel"      => "nmqcallback",
    "saveThreadCtr"             => "nmqcallback",
    "getThreadCtr"              => "nmqcallback",
    'getNearbyPersonInfoList'   => 'nearbyperson',
    'getNearbyPersonList'       => 'nearbyperson',
    'getNearbyPersonInfo'       => 'nearbyperson',
    "uploadUserLocation"        => "nearbyperson",
    "checkAllowInsert"          => "nearbyperson",
    "removeExpiredData"         => "nearbyperson",
    'isHighQualityUser'      => 'highqualityuser',
    'mInsertHighQualityUser' => 'highqualityuser',
    'mDeleteHighQualityUser' => 'highqualityuser',
);