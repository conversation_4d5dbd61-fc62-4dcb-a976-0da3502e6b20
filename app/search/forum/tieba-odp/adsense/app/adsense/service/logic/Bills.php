<?php
/**
 * @file: Service_Logic_Bills.php
 * @author: <EMAIL>
 * @date: 2014-07-09 11:37:22
 * @description: 
 * ##BUG: the REDIS opration in incrBills(ZADD and INCR) and mergeBills(ZRANGE, ZREM, DEL) should be atomic
 */

class Service_Logic_Bills extends Service_Libs_Base {

    // mysql, redis consts
    const DUPLICATE_ERRNO = 1062;                     // mysql duplicate key errno
    const REDIS_PREFIX    = 'bills';                  // redis key prefix
    const SEPRATOR        = '_';                      // redis key seprator
    const POOL_NAME       = 'pool';                   // sorted set to store keys that need to sync
    const FETCH_NUM       = 64;                       // ZRANGE get this number of key from pool
    const BILLS_DELAY     = 900;                      // delay (seconds)

    // bills levels: second, minute, hour, day, month, year
    const LEVEL_SECOND = 'second';
    const LEVEL_MINUTE = 'minute';
    const LEVEL_HOUR   = 'hour';
    const LEVEL_DAY    = 'day';
    const LEVEL_MONTH  = 'month';
    const LEVEL_YEAR   = 'year';

    private static $_tableName = array(
        self::LEVEL_HOUR  => 'bills_hour_new',
        self::LEVEL_DAY   => 'bills_day',
        self::LEVEL_MONTH => 'bills_month',
    );

    // 数据库中的这些字段是整数类型
    private static $_clientType = array(
        'PC'     => 1,
        'WAP'    => 2,
        'APP'    => 3,
        'SDK'    => 4,
        'MINIAPP'   => 5,
    );
    private static $_pageName = array(
        'FRS' => 1,
        'frs' => 1,
        'PB'  => 2,
        'pb'  => 2,
        'INDEX' => 3,
        'index' => 3,
    );
    private static $_appType = array(
        'APP' =>   1,
        'GAME' =>  2,
    );

    protected static $_conf = null;
    private static $_numPools = 4;

    protected static function _init() {

        if (self::$_conf == null){
            self::$_conf = Bd_Conf::getConf("/app/adsense/bills");
            if (self::$_conf == false){
                Bingo_Log::warning("init get conf fail.");
                return false;
            }
            if (isset(self::$_conf['num_pool'])) {
                self::$_numPools = self::$_conf['num_pool'];
            }
        }
        return true;
    }

//    // merge redis data to db & merge low level bills to high level
//    public static function mergeBills($arrInput) {
//
//        self::_init();
//        if (!isset($arrInput['pool_index']) || !$arrInput['level_from'] || !$arrInput['level_to']) {
//            Bingo_Log::warning('invalid parameter:[' . serialize($arrInput) . ']');
//            return parent::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
//        }
//        if (!$arrInput['count']) {
//            $arrInput['count'] = self::FETCH_NUM;
//        } else {
//            $arrInput['count'] = (int)$arrInput['count'];
//        }
//
//        $redis = Service_Libs_Redis::_getRedis();
//        if (!$redis) {
//            Bingo_Log::warning('redis connect fail:[' . serialize($redis) . ']');
//            return parent::_errRet(Tieba_Errcode::ERR_CACHE_CONN_FAIL);
//        }
//
//        // ZRANGE: fetch keys from key pool
//        $poolKey = self::_buildKey(array(self::REDIS_PREFIX, self::POOL_NAME, $arrInput['level_from'], $arrInput['pool_index']));
//        $input = array(
//            'key' => $poolKey,
//            'start' => 0,
//            'stop' => $arrInput['count'] - 1,
//        );
//        $ret = $redis->ZRANGE($input);
//        if (!isset($ret['err_no']) || $ret['err_no']) {
//            Bingo_Log::warning('redis ZRANGE failed, input is :' . serialize($input));
//            return parent::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
//        }
//        $keys = $ret['ret'][$poolKey];
//        if (empty($keys)) {
//            return parent::_successRet(Tieba_Errcode::ERR_SUCCESS, array('count' => 0));
//        }
//
//        // MGET: get key's values
//        $input = array('reqs' => array_map(array(__CLASS__, '_key2array'), $keys));
//        $ret = $redis->GET($input);
//        if (!isset($ret['err_no']) || $ret['err_no'] || !$ret['ret']) {
//            Bingo_Log::warning('redis GET failed, input is ' . serialize($input));
//            return parent::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
//        }
//        $values = $ret['ret'];
//
//        // process these keys
//        $processed = array();
//        foreach ($keys as $key) {
//            $fields = self::_extractKey($key);
//            $advinfo = array(
//                'user_id' => $fields[1], 
//                'advertid' => $fields[2],
//                'type' => $fields[3],
//                'level' => $fields[4],
//                'time' => $fields[5],
//            );
//
//            // sync to mysql for the same level
//            $advinfo['num'] = $values[$key];
//            $advinfo['level'] = $arrInput['level_from'];
//            $ret = self::_singleUpdate($advinfo);
//            if (!isset($ret['errno']) || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
//                Bingo_Log::warning('self _singleUpdate call fail');
//                return parent::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
//            }
//
//            // merge to redis for the next level if level less than month
//            if ($arrInput['level_from'] != $arrInput['level_to']) {
//                $advinfo['level'] = $arrInput['level_to'];
//                $ret = self::incrBills($advinfo);
//                if (!isset($ret['errno']) || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
//                    Bingo_Log::warning('self incrBills call fail');
//                    return parent::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
//                }
//            }
//            $processed[] = $key;
//        }
//
//        // if empty return success
//        if (empty($processed)) {
//            return parent::_successRet(Tieba_Errcode::ERR_SUCCESS, array('count' => 0));
//        }
//        // delete keys from sorted set
//        $input = array(
//            'key' => $poolKey,
//            'member' => $processed,
//        );
//        $ret = $redis->ZREM($input);
//        if (!isset($ret['err_no']) || $ret['err_no']) {
//            Bingo_Log::warning('redis ZREM failed, the input is:' . serialize($input));
//            return parent::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
//        }
//
//        if ($ret['ret'][$poolKey] != count($processed)) {
//            Bingo_Log::warning('redis ZREM returned unexpected value');
//        }
//
//        // delete the keys & values
//        $input = array('reqs' => array_map(array(__CLASS__, '_key2array'), $processed));
//        $ret = $redis->DEL($input);
//        if (!isset($ret['err_no']) || $ret['err_no']) {
//            Bingo_Log::warning('redis DEL failed, the input is:' . serialize($input));
//            return parent::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
//        }
//        if (count($ret['ret']) != count($processed)) {
//            Bingo_Log::warning('redis DEL returned with unexpected value');
//        }
//
//        return parent::_successRet(Tieba_Errcode::ERR_SUCCESS, array('count' => count($processed)));
//    }
//
//    public static function incrBills($arrInput) {
//
//        self::_init();
//        if (!$arrInput['user_id'] || !$arrInput['advertid'] || !$arrInput['type'] || !$arrInput['level']
//                || !isset($arrInput['time'])/* || !isset($arrInput['num'])*/) {
//            Bingo_Log::warning('invalid parameter:[' . serialize($arrInput) . ']');
//            return parent::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
//        }
//
//        // bug to fix: race condition
//        if (!$arrInput['num']) {
//            Bingo_Log::warning('incrBills num = 0');
//            return parent::_successRet(Tieba_Errcode::ERR_SUCCESS);
//        }
//
//        $redis = Service_Libs_Redis::_getRedis();
//        if (!$redis) {
//            Bingo_Log::warning('redis connect fail:[' . serialize($redis) . ']');
//            return parent::_errRet(Tieba_Errcode::ERR_CACHE_CONN_FAIL);
//        }
//
//        // type check
//        if (!in_array($arrInput['type'], array('click', 'display', 'download'/*, 'cost'*/))) {
//            Bingo_Log::warning('invalid parameter:[' . serialize($arrInput) . ']');
//            return parent::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
//        }
//
//        // convert time
//        $arrInput['time'] = self::_convertTime($arrInput['time'], $arrInput['level']);
//
//        // insert the key into key pool for merge, sorted by time, use sorted set for storage
//        $poolKey = self::_buildKey(array(self::REDIS_PREFIX, self::POOL_NAME, $arrInput['level'], $arrInput['user_id'] % self::$_numPools));
//        $billKey = self::_buildKey(array(self::REDIS_PREFIX, $arrInput['user_id'], $arrInput['advertid'], $arrInput['type'], $arrInput['level'], $arrInput['time']));
//        $input = array(
//            'key'     => $poolKey,
//            'members' => array(array(
//                    'score' => $arrInput['time'],
//                    'member'=> $billKey,
//            )),
//        );
//        $ret = $redis->ZADD($input);
//        if (!isset($ret['err_no']) || $ret['err_no']) {
//            Bingo_Log::warning('redis ZADD failed, the input is:' . serialize($input));
//            return parent::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
//        }
//
//        // increase the key by step (or 1)
//        if ($arrInput['num'] > 1) {
//            $ret = $redis->INCRBY(array('key' =>$billKey, 'step' =>$arrInput['num']));
//        } else {
//            $ret = $redis->INCR(array('key' =>$billKey));
//        }
//        if (!isset($ret['err_no']) || $ret['err_no']) {
//            Bingo_Log::warning('redis INCR|INCRBY failed, the input is:' . serialize($input));
//            return parent::_errRet(Tieba_Errcode::ERR_CACHE_CALL_FAIL);
//        }
//
//        return parent::_successRet(Tieba_Errcode::ERR_SUCCESS, array('ret' => $ret[$billKey]));
//    }

    protected static function _singleUpdate($arrInput) {

        self::_init();
        if (!isset($arrInput['user_id']) || !$arrInput['advertid'] || !$arrInput['type'] || !$arrInput['level'] ||
            !isset($arrInput['page_name']) || !isset($arrInput['client_type']) || !isset($arrInput['cpid']) 
                || !isset($arrInput['time']) || !is_numeric($arrInput['advertid'])) {
            Bingo_Log::warning('invalid parameter:[' . serialize($arrInput) . ']');
            return parent::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (!in_array($arrInput['type'], array('click', 'display', 'download'/*, 'cost'*/))) {
            Bingo_Log::warning('invalid parameter:[' . serialize($arrInput) . ']');
            return parent::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (($arrInput['cost'] = intval($arrInput['cost'])) <= 0) {
            unset($arrInput['cost']);
        }

		// 转换为数字
        $arrInput['page_name'] = self::$_pageName[$arrInput['page_name']];
        $arrInput['client_type'] = self::$_clientType[$arrInput['client_type']];
        $arrInput['app_type'] = self::$_appType[$arrInput['app_type']];

        // bug to fix: race condition
        if (!$arrInput['num']) {
            Bingo_Log::warning('incrBills num = 0');
            return parent::_successRet(Tieba_Errcode::ERR_SUCCESS);
        }

        // convert time
        $arrInput['time'] = self::_convertTime($arrInput['time'], $arrInput['level']);

        $db = Service_Libs_Db::getDB();
        if (!$db) {
            Bingo_Log::warning('db connect fail:[' . serialize($db) . ']');
            return parent::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $tableName = self::$_tableName[$arrInput['level']];

        $id = self::_buildKey(array($arrInput['user_id'], $arrInput['advertid'], $arrInput['time']));

        // TODO: use transaction query>insert || query>update
        // check if the record exists
        $fields = array('id');
        $arrCond = array(
            'user_id' => $arrInput['user_id'],
            'advertid' => $arrInput['advertid'],
            'time' => $arrInput['time'],
            'page_name' => $arrInput['page_name'],
            'client_type' => $arrInput['client_type'],
            //'app_type' => $arrInput['app_type'],
            'cpid' => $arrInput['cpid'],
        );
        $strCond = self::_buildCondition($arrCond);
        $ret = $db->select($tableName, $fields, $strCond);
        if ($ret === false) {
            Bingo_Log::warning('db query error! [output:' . serialize($ret) . 'error:' . $db->error() . 'sql:' . $db->getLastSQL() . ']');
            return parent::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        } else if (empty($ret)) {
            $arrRow['user_id'] = $arrInput['user_id'];
            $arrRow['advertid'] = $arrInput['advertid'];
            $arrRow['time'] = $arrInput['time'];
            $arrRow['page_name'] = $arrInput['page_name'];
            $arrRow['client_type'] = $arrInput['client_type'];
            //$arrRow['app_type'] = $arrInput['app_type'];
            $arrRow['cpid'] = $arrInput['cpid'];
            $arrRow[$arrInput['type']] = $arrInput['num'];
            $arrRow['cost'] = $arrInput['cost'] ? $arrInput['cost'] : 0;
            $ret = $db->insert($tableName, $arrRow);
            if ($ret === false) {
                Bingo_Log::warning('db query error! [output:' . serialize($ret) . 'error:' . $db->error() . 'sql:' . $db->getLastSQL() . ']');
                return parent::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            return parent::_successRet(Tieba_Errcode::ERR_SUCCESS);
        }

        // if the record exists, update it
        $sql  = 'UPDATE `' . $tableName . '` SET `' . $arrInput['type'] . '` = `' . $arrInput['type'] . '` + ' . $arrInput['num'];
        if ($arrInput['cost']) {
            $sql .= ', `cost` = `cost` + ' . $arrInput['cost'];
        }
        $sql .= ' WHERE ' . $strCond;
        $ret = $db->query($sql);
        if ($ret === false) {
            Bingo_Log::warning('db query error! [output:' . serialize($ret) . 'error:' . $db->error() . 'sql:' . $db->getLastSQL() . ']');
            return parent::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return parent::_successRet(Tieba_Errcode::ERR_SUCCESS);
    }

    public static function getBills($arrInput) {

        self::_init();
        $db = Service_Libs_Db::getDB();
        if (!$db) {
            Bingo_Log::warning('db connect fail:[' . serialize($db) . ']');
            return parent::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        // only support hour, day and year level
        if (!in_array($arrInput['level'], array(self::LEVEL_HOUR, self::LEVEL_DAY, self::LEVEL_MONTH))) {
            $arrInput['level'] = self::LEVEL_HOUR;
        }
        // set time start to 24 hours ago if not set
        if (!$arrInput['time_start']) {
            $arrInput['time_start'] = time() - 24 * 60 * 60;
        }
        // set time_end to now if not set
        if (!$arrInput['time_end']) {
            $arrInput['time_end'] = time();
        }
        // convert time
        //$arrInput['time_start'] = self::_convertTime($arrInput['time_start'], $arrInput['level']);
        //$arrInput['time_end'] = self::_convertTime($arrInput['time_end'], $arrInput['level']);
        $tableName = self::$_tableName[$arrInput['level']];

        // prepare query
        $fields = array('id', 'user_id', 'advertid', 'display', 'click', 'download', 'cost', 'time');
        $arrCond = array();
        if ($arrInput['id']) {
            $arrCond[] = '`id` = \'' . $arrInput['id'] . '\'';
        }

        if ($arrInput['advertid']) {
            $arrCond[] = '`advertid` = ' . $arrInput['advertid'];
        }

        if ($arrInput['user_id']) {
            $arrCond[] = '`user_id` = ' . $arrInput['user_id'];
        }

        if ($arrInput['time_start']) {
            $arrCond[] = '`time` >= ' . $arrInput['time_start'];
        }

        if ($arrInput['time_end']) {
            $arrCond[] = '`time` <= ' . $arrInput['time_end'];
        }

        if ($arrCond) {
            $strCond = implode(' AND ' , $arrCond);
        }

        // TODO offset&limit
        $ret = $db->select($tableName, $fields, $strCond);
        if ($ret === false) {
            Bingo_Log::warning('db query error! [output:' . serialize($ret) . 'error:' . $db->error() . 'sql:' . $db->getLastSQL() . ']');
            return parent::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return parent::_successRet(Tieba_Errcode::ERR_SUCCESS, array('ret' => $ret));
    }

    public static function updateBills($arrInput) {

        self::_init();
        // call update 3 times
        $arrInput['level'] = self::LEVEL_HOUR;
        $ret = self::_singleUpdate($arrInput);
        if (!$ret || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            return $ret;
        }
        $arrInput['level'] = self::LEVEL_DAY;
        $ret = self::_singleUpdate($arrInput);
        if (!$ret || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            return $ret;
        }
        $arrInput['level'] = self::LEVEL_MONTH;
        $ret = self::_singleUpdate($arrInput);
        if (!$ret || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            return $ret;
        }
        return self::_successRet(Tieba_Errcode::ERR_SUCCESS);
    }

    public static function nmqCallback($arrInput) {

        self::_init();
        // read data from post raw
        $cmdNo = Tieba_Cmd_Adsense::$cmd[Tieba_Cmd_Adsense::addClick];
        $arrInput = Tieba_Service::getArrayParams($arrInput, 'data');
        if ($arrInput['_topic'] != 'adsense' || $arrInput['command_no'] != $cmdNo) {
            Bingo_Log::warning('invalid msg from nmq: ' . serialize($arrInput));
            return parent::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if(empty($arrInput['user_id'])) {
                Bingo_Log::warning('bill nmqCallback user_id empty.');
                return parent::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // complete the param
        $arrInput['advertid']      = $arrInput['adv_id'];
        $arrInput['num']           = 1;
        $arrInput['time']          = time();
        $arrInput['cost']          = $arrInput['price'];
        $arrInput['page_name']     = $arrInput['page'];
        $arrInput['client_type']   = $arrInput['client_type'];
        $arrInput['app_type']      = $arrInput['app_type'];
        $arrInput['cpid']          = $arrInput['cpid'];
        return self::updateBills($arrInput);
    }


    /**
     * TODO: 目前只针对数字型变量的条件
     */
    private static function _buildCondition($arrCond) {
        $arrTmp = array();
        foreach ($arrCond as $k => $v) {
            $arrTmp[] = "`$k` = $v";
        }
        return implode(' AND ', $arrTmp);
    }

    private static function _buildKey($fields) {
        return implode(self::SEPRATOR, $fields);
    }

    private static function _extractKey($key) {
        return explode(self::SEPRATOR, $key);
    }

    private static function _key2array($key) {
        return array('key' => $key);
    }

    private static function _convertTime($timestamp, $level) {

        $time_val = getdate($timestamp);
        switch ($level) {
        case self::LEVEL_SECOND:
            return $timestamp;
        case self::LEVEL_MINUTE:
            return $timestamp - ($timestamp % 60);
        case self::LEVEL_HOUR:
            return $timestamp - ($timestamp % 3600);
        case self::LEVEL_DAY:
            return mktime(0, 0, 0, $time_val['mon'], $time_val['mday'], $time_val['year']);
        case self::LEVEL_MONTH:
            return mktime(0, 0, 0, $time_val['mon'], 1, $time_val['year']);
        case self::LEVEL_YEAR:
            return mktime(0, 0, 0, 1, 1, $time_val['year']);
        }
        return false;
    }
}
