<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:04:23 15:01:35
 * @version 
 * @structs & methods(copied from idl.)
 */



define("MODULE","Search_dl");
class Dl_Posthistory_Posthistory{

    const SERVICE_NAME = "Dl_Posthistory_Posthistory";
    protected static $_conf = null;
    protected static $_use_split_db = false;
    const DATABASE_NAME = "search_history";
    const PRE_TABEL_NAME = "user_search_";
    const DEFAULT_TABLE_MOD = 100;
    const HISTORY_NUM = 10;
    const MAX_HISTORY_NUM = 50;
    const MAX_OFFSET_NUM = 10000;

    /**
     * @brief get mysql obj.
     * @return: obj of Bd_DB, or null if connect fail.

     **/		
    private static function _getDB(){
        $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if($objTbMysql && $objTbMysql->isConnected()) {
            return $objTbMysql;
        } else {
            Bingo_Log::warning("db connect fail.");
            return null;
        }
    }


    /**
     * @brief init
     * @return: true if success. false if fail.

     **/		
    private static function _init(){

        //add init code here. init will be called at every public function beginning.
        //not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
        //init should be recalled for many times.

        if(self::$_conf == null){	
            self::$_conf = Bd_Conf::getConf("/app/search/dl_posthistory_posthistory");
            if(self::$_conf == false){
                Bingo_Log::warning("init get conf fail.");
                return false;
            }

        }
        return true; 
    }


    private static function _errRet($errno){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    public static function preCall($arrInput){
        // pre-call hook
    }

    public static function postCall($arrInput){
        // post-call hook
    }
    /**
     * @brief
     * @arrInput:
     * array(
     *  'user_id'   => 11111,
     *  'word'      => 'test',
     * )
     * @return: $arrOutput
     * 	out_info data[]
     **/
    public static function addPostHistory($arrInput){

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if (!isset($arrInput['user_id']) || $arrInput['user_id'] < 1 || !isset($arrInput['word']) || empty($arrInput['word'])) {	
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        //input params.
        $intUid = $arrInput['user_id'];
        $strWord = $arrInput['word'];
        $intTime = time();

        if (!self::_init()) {
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.

        $db = self::_getDB();
        if ($db == null) {
            Bingo_Log::warning('get db fail');
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $strWord = $db->escapeString($strWord);

        $strTable = self::PRE_TABEL_NAME.($intUid % self::DEFAULT_TABLE_MOD);
        $strSql = 'delete  from ' . $strTable . ' where uid = ' . $intUid . ' and word = ' . "'$strWord'";
        $arrRes = $db->query($strSql);


        $strSql = sprintf("insert into %s (uid, word, time) values (%d, '%s', %d) ", $strTable, $intUid, $strWord, $intTime);
        $arrRes = $db->query($strSql);
        if (false === $arrRes) {
            Bingo_Log::warning('query sql fail, sql = ' . $strSql . ', the errno = ' . $db->errno() . ', error = ' . $db->error());
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }



        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => array(),
        );
        return $arrOutput;
    }
    /**
     * @brief
     * @arrInput:
     * 	in_info input
     * @return: $arrOutput
     * 	out_info data[]
     **/
    public static function delPostHistory($arrInput){

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if (!isset($arrInput['user_id']) || $arrInput['user_id'] < 1 || !isset($arrInput['word']) || empty($arrInput['word'])) {	
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        //input params.
        $intUid = $arrInput['user_id'];
        $strWord = $arrInput['word'];

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.

        //your code here......
        $db = self::_getDB();
        if ($db == null) {
            Bingo_Log::warning('get db fail');
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $strWord = $db->escapeString($strWord);

        $strTable = self::PRE_TABEL_NAME.($intUid % self::DEFAULT_TABLE_MOD);
        $strSql = 'delete  from ' . $strTable . ' where uid = ' . $intUid . ' and word = ' . "'$strWord'";
        $arrRes = $db->query($strSql);
        if (false === $arrRes) {
            Bingo_Log::warning('query sql fail, sql = ' . $strSql . ', the errno = ' . $db->errno() . ', error = ' . $db->error());
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }



        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => array(),
        );
        return $arrOutput;
    }
    /**
     * @brief
     * @arrInput:
     * 	in_info input
     * @return: $arrOutput
     * 	out_info data[]
     **/
    public static function getPostHistoryList($arrInput){

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if (!isset($arrInput['user_id']) || $arrInput['user_id'] < 1 ) {	
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        //input params.
        $intUid = $arrInput['user_id'];
        $intResnum = isset($arrInput['res_num']) ? intval($arrInput['res_num']) : self::HISTORY_NUM;
        if($intResnum > self::MAX_HISTORY_NUM) {
            $intResnum = self::HISTORY_NUM;
        }
        $intOffset = isset($arrInput['offset']) ? intval($arrInput['offset']) : 0;
        if ($intOffset < 0 || $intOffset > self::MAX_OFFSET_NUM) {
            $intOffset = 0;
        }

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.

        //your code here......
        $db = self::_getDB();
        if ($db == null) {
            Bingo_Log::warning('get db fail');
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }  
        $strTable = self::PRE_TABEL_NAME.($intUid % self::DEFAULT_TABLE_MOD);
        $strSql = sprintf("select * from %s where uid = %d order by time desc limit %d, %d", $strTable, $intUid, $intOffset, $intResnum);
        $arrRes = $db->query($strSql);
        if (false === $arrRes) {
            Bingo_Log::warning('query sql fail, sql = ' . $strSql . ', the errno = ' . $db->errno() . ', error = ' . $db->error());
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }


        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $arrRes,
        );
        return $arrOutput;
    }
    /**
     * @brief
     * @arrInput:
     * 	in_info input
     * @return: $arrOutput
     * 	out_info data[]
     **/
    public static function delAllPostHistory($arrInput){

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if (!isset($arrInput['user_id']) || $arrInput['user_id'] < 1 ) {	
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
        }

        //input params.
        $intUid = $arrInput['user_id'];

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.
        $data = false;

        //your code here......
        $db = self::_getDB();
        if ($db == null) {
            Bingo_Log::warning('get db fail');
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }  
        $strTable = self::PRE_TABEL_NAME.($intUid % self::DEFAULT_TABLE_MOD);
        $strSql = 'delete  from ' . $strTable . ' where uid = ' . $intUid;
        $arrRes = $db->query($strSql);
        if (false === $arrRes) {
            Bingo_Log::warning('query sql fail, sql = ' . $strSql . ', the errno = ' . $db->errno() . ', error = ' . $db->error());
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }


        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;
    }

}
