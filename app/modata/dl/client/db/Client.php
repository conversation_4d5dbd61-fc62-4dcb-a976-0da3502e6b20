<?php
class Dl_Client_Db_Client extends Dl_Client_Db_DataBasic{
	
    const TABLE_NAME = 'client_info';
    const DIS_MOD_NUM = 100;
    public function getData($arrParam) {
    	
    	$arrParam['tb_name'] = self::TABLE_NAME;
    	$strSql = "SELECT imei, subapp_type, os_version, client_id, first_time, update_time, channel, agent, phone_screen"
    				." FROM {tb_name:r}"
    				." WHERE imei = {imei:s}";
    	
    	$result = array();
    	$ret = $this->query($strSql, $arrParam, $result);
    	if ($ret === false) {
    		Bingo_Log::warning("getData fail, errno:".$this->get_errno());
    		return false;
    	}
    	
    	return $result;
    }

    /**
     * @param unknown $arrParam
     * @return boolean
     */
    public function getRecentCuidData($arrParam) {
        $result = array();
        $arrParam['tb_name'] = $this->getTableNameNew($arrParam,'cuid');
        $strCuidSql = "SELECT id,cuid,cuid2, subapp_type, client_type,update_time,last_time,origin_from FROM {tb_name:r} WHERE cuid = {cuid:s}";
        $ret = $this->query($strCuidSql, $arrParam, $result);
        if ($ret === false) {
            Bingo_Log::warning("getRecentCuidData fail, errno:".$this->get_errno()." cuid:".$arrParam['cuid']);
            return false;
        }
        
        return $result;
    }

    /**
     * @param unknown $arrParam
     * @return boolean
     */
    public function getRecentImeiData($arrParam) {
        $result = array();
        $arrParam['tb_name'] = $this->getTableNameNew($arrParam,'imei');
        $strImeiSql = "SELECT id,imei, subapp_type, client_type, update_time,last_time,origin_from FROM {tb_name:r} WHERE imei = {imei:s}";
        $ret = $this->query($strImeiSql, $arrParam, $result);
        if ($ret === false) {
            Bingo_Log::warning("getRecentImeiData fail, errno:".$this->get_errno()." imei:".$arrParam['imei']);
            return false;
        }
        
        return $result;
    }

    /**
     * @param unknown $arrParam
     * @return boolean
     */
    public function getRecentUserData($arrParam) {
        $result = array();
        $arrParam['tb_name'] = $this->getTableNameNew($arrParam,'user');
        $strUidSql = "SELECT id,user_id,brand, subapp_type, client_type,update_time,last_time,origin_from FROM {tb_name:r} WHERE user_id = {user_id:n} and brand = {brand:s}";
        $ret = $this->query($strUidSql, $arrParam, $result);
        if ($ret === false) {
            Bingo_Log::warning("getRecentUserData fail, errno:".$this->get_errno()." user_id:".$arrParam['user_id']." brand:".$arrParam['brand']);
            return false;
        }
        
        return $result;
    }

    
    public function getNewDisData($arrParam) {   
        $arrParam['tb_name'] = $this->getTableName($arrParam);
        $strSql = "SELECT cuid, subapp_type, client_type, client_id, first_time,imei,update_time,origin_from FROM {tb_name:r} WHERE cuid = {cuid:s}";     
        $result = array();
        $ret = $this->query($strSql, $arrParam, $result);
        if ($ret === false) {
            Bingo_Log::warning("getNewDisData fail, errno:".$this->get_errno());
            return false;
        }
        return $result;
    }
  
    public function addData($arrParam) {
        
        $arrParam['tb_name'] = self::TABLE_NAME;        
   		$strSql = "INSERT INTO {tb_name:r}"
   					." SET imei={imei:s}, phone_screen={phone_screen:s}, os_version={os_version:s},"
	   					." agent={agent:s}, client_id={client_id:s}, first_time={first_time:n},"
	   					." update_time={update_time:n}, channel={channel:s}, subapp_type={subapp_type:s}";
        
   		$result = array();        
        $ret = $this->query($strSql, $arrParam, $result);
        if ($ret === false) {
    		Bingo_Log::warning("addData fail, errno:".$this->get_errno().' arrParam:'.json_encode($arrParam, true));
        	return false;
        }
        
        return true;
    }
    
    public function addNewDisData($arrParam) {
        $arrParam['tb_name'] = $this->getTableName($arrParam);
        $strSql = "INSERT INTO {tb_name:r}  SET cuid={cuid:s}, client_type={client_type:n}, client_id={client_id:s}, first_time={first_time:n},subapp_type={subapp_type:s}, imei={imei:s}, update_time={update_time:n}, origin_from={origin_from:n}";
        $result = array();
        $ret = $this->query($strSql, $arrParam, $result);
        if ($ret === false) {
            Bingo_Log::warning("addNewDisData fail, errno:".$this->get_errno().' arrParam:'.json_encode($arrParam, true));
            return false;
        }
        return true;
    }

    /**
     * @param unknown $arrParam
     * @return boolean
     */
    public function addRecentCuidData($arrParam) {
        $result = array();
        $arrParam['tb_name'] = $this->getTableNameNew($arrParam,'cuid');
        $strSql = "INSERT INTO {tb_name:r}  SET cuid={cuid:s},cuid2={cuid2:s}, client_type={client_type:n}, subapp_type={subapp_type:s},  update_time={update_time:n},last_time={last_time:n},origin_from={origin_from:n}";
        $ret = $this->query($strSql, $arrParam, $result);
        if ($ret === false) {
            Bingo_Log::warning("addRecentCuidData fail, errno:".$this->get_errno().' arrParam:'.json_encode($arrParam, true));
            return false;
        }
        return $ret;
    }

    /**
     * @param unknown $arrParam
     * @return boolean
     */
    public function addRecentImeiData($arrParam) {
        $result = array();
        $arrParam['tb_name'] = $this->getTableNameNew($arrParam,'imei');
        $strSql = "INSERT INTO {tb_name:r}  SET imei={imei:s}, client_type={client_type:n}, subapp_type={subapp_type:s}, update_time={update_time:n}, last_time={last_time:n},origin_from={origin_from:n}";
        $ret = $this->query($strSql, $arrParam, $result);
        if ($ret === false) {
            Bingo_Log::warning("addRecentImeiData fail, errno:".$this->get_errno().' arrParam:'.json_encode($arrParam, true));
            return false;
        }
        return $ret;
    }

    /**
     * @param unknown $arrParam
     * @return boolean
     */
    public function addRecentUserData($arrParam) {
        $result = array();
        $arrParam['tb_name'] = $this->getTableNameNew($arrParam,'user');
        $strSql = "INSERT INTO {tb_name:r}  SET user_id={user_id:n},brand={brand:s}, client_type={client_type:n},subapp_type={subapp_type:s}, update_time={update_time:n}, last_time={last_time:n},origin_from={origin_from:n}";
        $ret = $this->query($strSql, $arrParam, $result);
        if ($ret === false) {
            Bingo_Log::warning("addRecentUserData fail, errno:".$this->get_errno().' arrParam:'.json_encode($arrParam, true));
            return false;
        }
        return $ret;
    }

    public function updateData($arrParam) {
    	
    	$arrParam['tb_name'] = self::TABLE_NAME;
    	$strSql = "UPDATE {tb_name:r}"
    				." SET os_version={os_version:s}, client_id={client_id:s}, update_time={update_time:n}, channel={channel:s}"
    				." WHERE imei={imei:s} AND subapp_type={subapp_type:s}";

    	$result = array();
    	$ret = $this->query($strSql, $arrParam, $result);
    	if ($ret === false) {
    		Bingo_Log::warning("updateData fail, errno:".$this->get_errno());
    		return false;
    	}
    	return true;
    }

    public function updateNewDisData($arrParam) {
        Bingo_Log::notice("updateNewDisData_count"); 
        $arrParam['tb_name'] =$this->getTableName($arrParam);
        $strSql = "update {tb_name:r}  SET imei={imei:s},client_type={client_type:n}, client_id={client_id:s},update_time={update_time:n} WHERE cuid={cuid:s} AND subapp_type={subapp_type:s}";

        $result = array();
        $ret = $this->query($strSql, $arrParam, $result);
        if ($ret === false) {
            Bingo_Log::warning("updateData fail, errno:".$this->get_errno());
            return false;
        }
        return true;
    }

    /**
     * @param unknown $arrParam
     * @return boolean
     */
    public function updateRecentCuidData($arrParam) {
        //Bingo_Log::notice("updateNewDisData_count"); 
        $arrParam['tb_name'] =$this->getTableNameNew($arrParam,'cuid');
        $strSql = "update {tb_name:r}  SET update_time={update_time:n}, last_time={last_time:n}, origin_from={origin_from:n}  WHERE cuid={cuid:s} and subapp_type={subapp_type:s}";

        $result = array();
        $ret = $this->query($strSql, $arrParam, $result);
        if ($ret === false) {
            Bingo_Log::warning("updateRecentCuidData fail, errno:".$this->get_errno());
            return false;
        }
        return $ret;
    }

    /**
     * @param unknown $arrParam
     * @return boolean
     */
    public function updateRecentImeiData($arrParam) {
        //Bingo_Log::notice("updateNewDisData_count"); 
        $arrParam['tb_name'] =$this->getTableNameNew($arrParam,'imei');
        $strSql = "update {tb_name:r}  SET update_time={update_time:n}, last_time={last_time:n}, origin_from={origin_from:n}  WHERE imei={imei:s} and subapp_type={subapp_type:s}";

        $result = array();
        $ret = $this->query($strSql, $arrParam, $result);
        if ($ret === false) {
            Bingo_Log::warning("updateRecentImeiData fail, errno:".$this->get_errno());
            return false;
        }
        return $ret;
    }

    /**
     * @param unknown $arrParam
     * @return boolean
     */
    public function updateRecentUserData($arrParam) {
        //Bingo_Log::notice("updateNewDisData_count"); 
        $arrParam['tb_name'] =$this->getTableNameNew($arrParam,'user');
        $strSql = "update {tb_name:r}  SET update_time={update_time:n}, last_time={last_time:n}, origin_from={origin_from:n}  WHERE user_id={user_id:n} and brand={brand:s} and subapp_type={subapp_type:s} ";

        $result = array();
        $ret = $this->query($strSql, $arrParam, $result);
        if ($ret === false) {
            Bingo_Log::warning("updateRecentUserData fail, errno:".$this->get_errno());
            return false;
        }
        return $ret;
    }

    /**
     * @param unknown $arrParam
     * @return boolean
     */
    public function delNewDisData($arrParam) {
    	//删除脏数据时，这些uid已经非法了，因此无法通过crc校验来找到正确的分表，必须通过外部接口显示调用
    	//$arrParam['tb_name'] =$this->getTableName($arrParam);
    	$strSql = "DELETE FROM {tb_name:r}  WHERE cuid={cuid:s} AND subapp_type={subapp_type:s}";
    	$result = array();
    	$ret = $this->query($strSql, $arrParam, $result);
    	if ($ret === false) {
    		Bingo_Log::warning("delNewDisData fail, errno:".$this->get_errno());
    		return false;
    	}
    	return true;
    }
    
    private function getTableName($arrParam){
         $cuid = $arrParam['cuid'];
         $crcCode =  sprintf("%u",crc32($cuid));
         $index = $crcCode % self::DIS_MOD_NUM;
         return  self::TABLE_NAME."_".$index;
    }

    /**
     * @param unknown $arrParam
     * @return boolean
     */
    private function getTableNameNew($arrParam,$type){
        if($type == 'cuid' || $type == 'imei'){
            $splitKey = $arrParam[$type];
            $tableNamePre = self::TABLE_NAME.'_'.$type;
        }elseif($type = 'user'){
            $splitKey = $arrParam['user_id'].$arrParam['brand'];
            $tableNamePre = self::TABLE_NAME.'_user';
        }else{
            return "";
        }
        $crcCode =  sprintf("%u",crc32($splitKey));
        $index = $crcCode % self::DIS_MOD_NUM;
        return  $tableNamePre."_".$index;
    }
    
//     public function getClientInfoByImei($arrParam){
//     	$strSqlTpl = 'SELECT imei,subapp_type FROM client_info WHERE imei = {imei:s}';
//     	$arrResult = $this->findAll($strSqlTpl,$arrParam);
//     	return $arrResult;
//     }
    
//     public function addClientInfo($arrParam){
//     	$strSqlTpl = 'INSERT INTO client_info SET imei = {imei:s},phone_screen ={phone_screen:s},os_version={os_version:s},agent={agent:s},client_id={client_id:s},first_time={first_time:n},update_time={update_time:n},channel={channel:s},subapp_type={subapp_type:s}';
//     	$strSql = $this->genSql($strSqlTpl,$arrParam);
//     	if ($strSql === false){
//     		return false;
//     	}
//     	$ret = $this->query($strSql);
//     	if ($ret === false){
//     		return false;
//     	}
//     	return true;
//     }

//     public function updateClientInfo($arrParam){
//     	$strSqlTpl = 'UPDATE client_info set os_version={os_version:s},client_id={client_id:s},update_time={update_time:n},channel={channel:s} WHERE imei = {imei:s} and subapp_type={subapp_type:s}';
//     	$strSql = $this->genSql($strSqlTpl,$arrParam);
//     	if ($strSql === false){
//     		return false;
//     	}
//     	$ret = $this->query($strSql);
//     	if ($ret === false){
//     		return false;
//     	}
//     	return true;
//     }
    
}
