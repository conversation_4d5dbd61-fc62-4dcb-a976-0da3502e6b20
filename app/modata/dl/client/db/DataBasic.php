<?php
/***************************************************************************
 * 
 * Copyright (c) 2010 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/

class Dl_Client_Db_DataBasic {
    protected $_db = null;
    protected $_tpl = null;
    const CHAR_SET_GBK = 'gbk';
    const CHAR_SET_UTF8 = 'utf8';

    public function __construct($db, $charset = self::CHAR_SET_GBK)
    {
		$this->_db = $db;
		$this->_db->charset($charset);
		$this->_tpl = new Bd_DB_SQLTemplate($db);
    }

    /**
     * 
     * @param string $sqlTpl
     * @param array $arrParams
     * @param array $outResult
     * @return string|string|unknown
     */
    public function query($sqlTpl, $arrParams, & $outResult) {

	$this->_tpl->prepare($sqlTpl);
	$strSql = $this->_tpl->bindParam($arrParams, NULL, true);
	if($strSql === null || $strSql === false){
	    Bingo_Log::warning('build sql fail [ ' . $sqlTpl . ' ]');
	    return false;
	}
	$result = $this->_db->query($strSql); 
	Bingo_Log::debug('exec sql [ ' . $strSql . ' ]' 
	    . '[errno:' . $this->_db->errno() . ']'
	    . '[error:' . $this->_db->error() . ']'
	    . '[rows:' . $this->_db->getAffectedRows() . ']'
	    . '[charset:' . $this->get_charset() . ']'
	    . '[cur sql time(us):' . $this->_db->getLastCost() . ']'
	    . '[all sql time(us):' . $this->_db->getTotalCost() . ']');  

	if ($result === false) {
	    Bingo_Log::warning('exec sql fail [' . $this->_db->errno() . '] [' .  $this->_db->error() . '] [' . $strSql . ']');
	    return false;
	}
	$outResult = $result;

	return $this->_db->getAffectedRows();
    }

    public function get_errno(){
	return $this->_db->errno();
    }

    public function need_retry(){
	$errno = $this->_db->errno();
	if(	$intErrno === 2006 || 
	    $intErrno === 2013 || 
	    $intErrno === 1030 ||
	    $intErrno === 1146) {
		return true;
	    }
	else {
	    return false;
	}
    }

    public function set_gbk() {
	return $this->_db->charset(self::CHAR_SET_GBK);
    }

    public function set_utf8() {
	return $this->_db->charset(self::CHAR_SET_UTF8);
    }

    public function get_charset() {
	return $this->_db->__get('db')->character_set_name();
    }
}
