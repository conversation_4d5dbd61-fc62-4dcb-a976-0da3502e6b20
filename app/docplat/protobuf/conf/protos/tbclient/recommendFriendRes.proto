// cmd:304106

package tbclient.RecommendFriend;
import "client.proto";


//皇冠信息
message ShowIcon{ 
	optional string icon = 1;
	optional string name = 2 ;
	optional string url = 3 ;
}

//地理位置信息
message LbsInfo{
	optional string distance = 1;
	optional int64 time = 2;
	optional int32 is_hide = 3;
}

//贴吧新人
message UserInfo{ 
  //id
	optional int32   id = 1;
  //头像
	optional string  portrait = 2 ;
	//昵称
	optional string name = 3 ;
	//性别
	optional int32   sex = 4;
	 //距离
	optional int32 distance = 5;
	//个人简介
	optional string intro = 6 ;
	
	//皇冠信息
	optional ShowIcon tshow_icon = 7;
	
	optional LbsInfo location = 8;
	optional string tag_name = 9 ;
	optional string st_type = 10 ;

	//默认文案
	optional string	message = 11 ;
}

//TA的帖子信息
message PostInfo{ 
  //TA的帖子图片
	optional string common_post_pic = 1;
	//TA的帖子大图
	optional string large_post_pic = 2;
}



//共同关注的吧
message ForumInfo{ 
  //共同关注的吧名
	optional string common_forum     = 1;
}

//物以类聚
message LikeUserInfo{ 
  //用户信息
	optional UserInfo  user_info = 1 ;

	//共同关注的吧
	repeated ForumInfo  forum_info = 2;
	
	//TA的帖子信息
	repeated PostInfo post_info = 3;

	//默认文案
	optional string	message = 4;
}

message DataRes {
     //贴吧新人
    optional UserInfo  new_user    = 1;
     
     //物以类聚
    repeated LikeUserInfo  like_user = 2;
}

message RecommendFriendResIdl {
    optional Error       error   = 1;
    optional DataRes     data    = 2;
}
