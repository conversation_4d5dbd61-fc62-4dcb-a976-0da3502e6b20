//根据经纬度坐标获取地址 cmd:303017
package tbclient.GetPoisByLocation;
import "client.proto";


message PoiInfo{
	//展现地址
	optional string name = 1;
	//精确地址
	optional string addr = 2;
	//展现地址加密串
	optional string sn = 3;
}
message DataRes {
	//地址名称信息
	optional string formatted_address = 1;
	//周边poi数组
	repeated PoiInfo poi_info = 2;
}

message GetPoisByLocationResIdl {
    optional Error       error   = 1;
    optional DataRes     data    = 2;
}