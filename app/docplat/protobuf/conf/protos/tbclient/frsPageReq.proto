//获取FrsPage cmd:301001
//url:/c/f/frs/page
package tbclient.FrsPage;
import "client.proto";



message DataReq {
	//吧名
    optional string kw = 1;
    //返回结果数
    optional int32  rn = 2;
    //页面实际展示的条数；例如rn=90，rn_need=30，那么页面展示30条贴子，其它60条只返回tid
    optional int32  rn_need = 3;
    //是否精品区
    optional int32  is_good = 4;
    //精品分类id
    optional int32  cid = 5;
    //是否带签到日历，目前ipad-client端使用
    optional int32  withcal = 6;
    optional int32  noval = 7;
    //是否查询群组信息，1标识查询，0不查询
    optional int32  with_group = 8;
    //可选，是否需要吧徽章信息，1需要，0不需要，默认0
    optional int32  need_badge = 9;
    optional int32  frs_rn = 10;
    //屏幕宽
    optional int32  scr_w = 11;
    //屏幕高
    optional int32  scr_h = 12;
    //屏幕dip
    optional double scr_dip = 13;
    //1:45, 2:80
    optional int32  q_type = 14;
    optional int32  pn = 15;
    optional string st_type = 16;
    optional int32  ctime = 17;
    optional int32  data_size = 18;
    optional int32  net_error = 19;
    
    //下面为根据工具分析生成的idl补充的字段
    optional bool   check_login = 20;
	optional string forum_name = 21;
	optional int32  result_num = 22;
	//精品分类id
	optional int32  class_id = 23;
	optional string ip_str = 24;
	optional int32  ip_int = 25;
	optional string module_name = 26;
	optional int32  st_param=27;
	optional int32  smile_grade=28;
	optional bool   support_noun=29;
	optional bool   login=30;
	optional int32  user_id=31;
	optional string user_name=32;
	optional int32   no_un=33;
	optional string portrait=34;
	optional string mobile=35;
	optional string email=36;
	
	optional bool debug=37;
	//所有cookie都以一个json形式传入
	optional string cookie = 38;
	optional CommonReq common = 39;
	//分发使用,可选参数，多个lastid以逗号分隔。 内容为：frs页上次展现过的广告id， 如果一个页面有多个广告id，传多个
	optional string lastids = 40;
	//分发使用,是否sdk
	optional int32 issdk = 41;
	//分发使用, 设备idfa, 多盟广告入参
	optional string da_idfa = 42;
	//分发使用, 硬件设备名称, 多盟广告入参
	optional string platform = 43;
}

message FrsPageReqIdl {
    optional DataReq    data    = 1;
}

