//获取friend feed cmd:303003
//url:/c/r/feed/page
package tbclient.FriendFeedPage;
import "client.proto";



message DataReq {
    optional int32  limit = 1;
    optional string timeline = 2;
    
    //下面为根据工具分析生成的idl补充的字段
    optional int32  st_param=3;
    //所有cookie都以一个json形式传入
    optional string cookie = 4;
    optional CommonReq common = 5;

    optional int32  scr_w = 6;
    optional int32  scr_h = 7;
    optional double scr_dip = 8;
    optional int32  q_type = 9;
}

message FriendFeedPageReqIdl {
    optional DataReq    data    = 1;
}

