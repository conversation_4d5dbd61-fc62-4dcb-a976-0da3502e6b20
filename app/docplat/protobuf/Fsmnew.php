<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013/11/21 16:21:00
 * @version 1.0
 * @brief 状态机解析protobuf
 * @structs & methods(copied from idl.)
 */

$strCur = dirname(__FILE__);
$strFileConst = $strCur . '/../libs/Const.php';
$strFileService = $strCur . '/../libs/Service.php';
require_once($strFileConst); 
require_once($strFileService); 
$pfp = fopen('fsm.log', 'w');
if($pfp === false){
	echo "fopen error.";
	exit;
}
fwrite($pfp, "fsm start.\r\n");
class Protobuf_Fsmnew{
	protected $parseFile = 'frsPageRes.proto';
	protected $intCurStatus = 0;
	protected $intEvent = 0;
	protected $arrTmpStruct = array();
	public $arrStructRes = array();
	public $arrInterface = array();
	public $intParseType = 0; //1 req 2 res 3common
	public $arrPublicStruct = array();
	protected $arrBaseTypes = array(
		1 => 'string',
		2 => 'uint32',
		3 => 'uint64',
		4 => 'bool',
		5 => 'int32',
		6 => 'int64',
		7 => 'double',
	);
	const STATUS_STRUCT_WIAT = 0;
	const STATUS_STRUCT_NEW = 1;
	const STATUS_STRUCT_FIELD = 2;
	const STATUS_STRUCT_END = 3;
	const EVENT_PARSE_STRUCT_NAME = 1;
	const EVENT_PARSE_STRUCT_FIELD = 2;
	const EVENT_PARSE_STRUCT_END = 3;
	const EVENT_PARSE_COMMENT = 4;
	const EVENT_PARSE_INTERFACE_CMD = 5;
	const EVENT_PARSE_INTERFACE_NAME = 6;
	const EVENT_PARSE_INTERFACE_URL = 7;
	const EVENT_PARSE_NULL = 8;
	
	public function __construct($file){
		$this->parseFile = $file;
	}

	public function parseFileName(){
		if(strstr($this->parseFile, "Req")){
			$this->intParseType = 1;
			$strFile =  $this->parseFile;
			$arrParsePath = explode("/", $strFile);
			$strFile = $arrParsePath[count($arrParsePath)-1];
			$strInterfaceName = trim(str_replace("Req.proto", "", $strFile));
			$this->arrInterface['name'] = $strInterfaceName;
			$this->arrInterface['type'] = 1;
		}
		else if(strstr($this->parseFile, "Res")){
			$this->intParseType = 2;
			$strFile =  $this->parseFile;
			$arrParsePath = explode("/", $strFile);
			$strFile = $arrParsePath[count($arrParsePath)-1];
			$strInterfaceName = trim(str_replace("Res.proto", "", $strFile));
			$this->arrInterface['name'] = $strInterfaceName;
			$this->arrInterface['type'] = 2;
		}else{
			$this->intParseType = 3;
		}
	}
/*	private function parseStructName($strLine){
		$ret = preg_match("/[A-Z]|[a-z].*?{/", $strLine, $matches);
		if($ret == false){
			$ret = preg_match("/[A-Z]|[a-z].*?Idl/", $strLine, $matches);
			if($ret == false){
				if(strstr($strLine, "message")){
					$strName = trim(str_replace("message", "", $strLine));
				}else{
					$strmsg = "pregmatch error.\r\n";
					fwrite($pfp, $strmsg);
					return false;
				}
			}else{
				$strName = trim($matches[0]);
			}
		}else{
			$strName = trim(str_replace("{", "", $matches[0]));
		}
		//这个文件太恶心了,不好改就写死
		if(strstr($this->parseFile, 'searchFriendRes.proto') && $strName=='Info'){
			$strName = 'userInfo';
		}
		return $strName;
	}*/

	/**
	 * @param $strLine
	 * @return bool|string
	 */
	private function parseStructName($strLine){
		$strLine = trim($strLine);
		if (strstr($strLine, "message")) {
			$arrRet = preg_split('/\s+/', $strLine);
			$strName = trim(str_replace("{", "", $arrRet[1]));
		} else {
			$strmsg = "pregmatch error.\r\n";
			fwrite($pfp, $strmsg);
			return false;
		}
		return $strName;
	}	
	
	/*private function parseStructField($strLine){
		$arrFieldInfo = array();
		$arrSplit = explode("=", $strLine);
		$arrFieldInfo['order'] = intval(trim(str_replace(";", "", $arrSplit[1])));
		$strFieldInfo = $arrSplit[0];
		if(strstr($strFieldInfo, "optional")){
			$arrFieldInfo['option'] = 1;
			$strFieldInfo = trim(str_replace("optional", "", $strFieldInfo));
		}
		if(strstr($strFieldInfo, "repeated")){
			$arrFieldInfo['option'] = 2;
			$strFieldInfo = trim(str_replace("repeated", "", $strFieldInfo));
		}
		$bolBaseType = false;
		foreach(Libs_Const::$arrBaseTypes as $key => $value){
			if(strstr($strFieldInfo, $value)){
				$arrFieldInfo['type'] = intval($key);
				$strFieldInfo = trim(str_replace($value, "", $strFieldInfo));
				$bolBaseType = true;
				break;
			}
		}
		if(!$bolBaseType){
			$arrFieldInfo['type'] = Libs_Const::OBJECT_TYPE; 
			$ret = preg_match("/[A-Z][a-z].*?\s/", $strLine, $matches);
			if($ret == false){
				$strmsg = "pregmatch error.\r\n";
				fwrite($pfp, $strmsg);
				return true;
			}
			$arrFieldInfo['objname'] = trim($matches[0]);
			$strFieldInfo = trim(preg_replace("/[A-Z][a-z].+\s/", "", $strFieldInfo));
		}
		$arrFieldInfo['name'] = trim($strFieldInfo);
		//这个文件太恶心了,不好改就写死
		if(strstr($this->parseFile, 'searchFriendRes.proto') && $arrFieldInfo['name']=='user user_info'){
			$arrFieldInfo['name'] = 'user_info';
			$arrFieldInfo['objname'] = 'userInfo';
		}
		return $arrFieldInfo;
	}*/

	/**
	 * @param $strLine
	 * @return array|bool
	 */
	public function parseStructField($strLine){
		$arrFieldInfo = array();
		$arrSplit = explode("=", $strLine);
		$arrFieldInfo['order'] = intval(trim(str_replace(";", "", $arrSplit[1])));
		$strFieldInfo = $arrSplit[0];
		if(strstr($strFieldInfo, "optional")){
			$arrFieldInfo['option'] = 1;
			$strFieldInfo = trim(str_replace("optional", "", $strFieldInfo));
		}
		if(strstr($strFieldInfo, "repeated")){
			$arrFieldInfo['option'] = 2;
			$strFieldInfo = trim(str_replace("repeated", "", $strFieldInfo));
		}
		$bolBaseType = false;
		foreach(Libs_Const::$arrBaseTypes as $key => $value){
			if(strstr($strFieldInfo, $value)){
				$arrFieldInfo['type'] = intval($key);
				$arrFieldInfo['name'] = trim(str_replace($value, "", $strFieldInfo));
				$bolBaseType = true;
				break;
			}
		}
		if(!$bolBaseType){
			$arrFieldInfo['type'] = Libs_Const::OBJECT_TYPE; 
			if (!preg_match("/[A-Z]|[a-z].*?\s/", $strFieldInfo)){
				$strmsg = "pregmatch error.\r\n";
				fwrite($pfp, $strmsg);
				return false;
			}
			$arrRet = preg_split('/\s+/', $strFieldInfo);
			$arrFieldInfo['objname'] = trim($arrRet[0]);
			$arrFieldInfo['name'] = trim($arrRet[1]);
		}
		return $arrFieldInfo;
	}

	private function parseEvent($strLine){
		if(preg_match("/^message\s.*?{/", $strLine)){
			$sname = $this->parseStructName($strLine);
			if($sname == false){
				$strmsg = "parse struct name error.\r\n";
				fwrite($pfp, $strmsg);
				return false;
			}
			//case /home/<USER>/orp001/conf/protos/tbclient/searchFriendReq.proto
			// if($sname == 'ReqData'){
			// 	$sname = 'DataReq';
			// }
			// if($sname == 'ResData'){
			// 	$sname = 'DataRes';
			// }
			return array(
				'event' => Libs_Const::EVENT_PARSE_STRUCT_NAME,
				'name' => $sname,
			);
		}
		else if(preg_match("/^message\s.*?Idl/", $strLine)){
			$sname = $this->parseStructName($strLine);
			if($sname == false){
				$strmsg = "parse struct name error.\r\n";
				fwrite($pfp, $strmsg);
				return false;
			}
			//case /home/<USER>/orp001/conf/protos/tbclient/searchFriendReq.proto
			// if($sname == 'ReqData'){
			// 	$sname = 'DataReq';
			// }
			// if($sname == 'ResData'){
			// 	$sname = 'DataRes';
			// }
			return array(
				'event' => Libs_Const::EVENT_PARSE_STRUCT_NAME,
				'name' => $sname,
			);
		}
		else if(preg_match("/^message\s.*?/", $strLine)){
			$sname = $this->parseStructName($strLine);
			if($sname == false){
				$strmsg = "parse struct name error.\r\n";
				fwrite($pfp, $strmsg);
				return false;
			}
			//case /home/<USER>/orp001/conf/protos/tbclient/searchFriendReq.proto
			// if($sname == 'ReqData'){
			// 	$sname = 'DataReq';
			// }
			// if($sname == 'ResData'){
			// 	$sname = 'DataRes';
			// }
			return array(
				'event' => Libs_Const::EVENT_PARSE_STRUCT_NAME,
				'name' => $sname,
			);
		}
		if(preg_match("/^optional\s.*?=\s*[1-9]*/", $strLine) || preg_match("/^repeated\s.*?=\s*[1-9]*/", $strLine)){
			$arrField = $this->parseStructField($strLine);
			if($arrField === false){
				$strmsg = "parse struct field error.\r\n";
				fwrite($pfp, $strmsg);
				return false;
			}
			//case /home/<USER>/orp001/conf/protos/tbclient/checkPostRes.proto
			// if($arrField['objname'] == 'ReqData'){
			// 	$arrField['objname']= 'DataReq';
			// }
			// if($arrField['objname'] == 'ResData'){
			// 	$arrField['objname']= 'DataRes';
			// }
			return array(
				'event' => Libs_Const::EVENT_PARSE_STRUCT_FIELD,
				'field' => $arrField,
			);
		}
		if(strstr($strLine, "//")){
			if(strstr($strLine, "cmd:")){
				$arrExplode = explode(":", $strLine);
				$strCmd = strval(trim($arrExplode[count($arrExplode)-1]));
				return array(
					'event' => Libs_Const::EVENT_PARSE_INTERFACE_CMD, 
					'interface_cmd' => $strCmd,
				);
			}
			if(strstr($strLine, "url:")){
				$strLine = str_replace("//", "", $strLine);
				$strLine = str_replace("url:", "", $strLine);
				$strUrl = strval($strLine);
				return array(
					'event' => Libs_Const::EVENT_PARSE_INTERFACE_URL,
					'interface_url' => $strUrl,
				);
			}
			$strComment = str_replace("//", "", $strLine);
			return array(
				'event' => Libs_Const::EVENT_PARSE_COMMENT,
				'comment' => $strComment,
			);
		}
		
		if($strLine=="}" || $strLine=="};"){
			return array(
				'event' => Libs_Const::EVENT_PARSE_STRUCT_END,
			);
		}
		
		if($this->intParseType == 3){
			if(preg_match("/package\s[a-z]*;/", $strLine)){
				$strLine = str_replace("package", "", $strLine);
				$strLine = str_replace(";", "", $strLine);
				$strPackage = strval(trim($strLine));
				return array(
					'event' => Libs_Const::EVENT_PARSE_INTERFACE_NAME,
					'package' => $strPackage,
				);
			}
		}else{
			if(preg_match("/package\s[a-z]*.[A-Z].*;/", $strLine)){
				$strLine = str_replace("package", "", $strLine);
				$arrLine = explode(".", trim($strLine));
				$strPackage = strval(trim($arrLine[0]));
				return array(
					'event' => Libs_Const::EVENT_PARSE_INTERFACE_NAME,
					'package' => $strPackage,
				);
			}
		}
		
		return array('event' => Libs_Const::EVENT_PARSE_NULL);
	}

	public function process($pfp){
		if($this->parseFile == '' || empty($this->parseFile)){
			$strmsg = "parse file empty.\r\n";
			fwrite($pfp, $strmsg);
			return false;
		}
		$this->parseFileName();
		$fp = fopen($this->parseFile, "r");
		if($fp === false){
			$strmsg = "fopen file error.\r\n";
			fwrite($pfp, $strmsg);
			return false;
		}
		$strFieldComment = '';		//先获取,后消费
		while(!feof($fp)){
			$strLine = trim(fgets($fp));
			if($strLine == ''){
				continue;
			}
			$arrEvent = $this->parseEvent($strLine);
			$strmsg = sprintf("return parse result:%s.\r\n", json_encode($arrEvent));
							fwrite($pfp, $strmsg);
			if($arrEvent === false){
				$strmsg = "fsm parse event error.\r\n";
				fwrite($pfp, $strmsg);
				return false;
			}
			//$strmsg = sprintf("chose line %s\r\n", $strLine);
			//fwrite($pfp, $strmsg);
			switch($this->intCurStatus){
				case Libs_Const::STATUS_STRUCT_WIAT:
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_STRUCT_NAME){
						$this->arrTmpStruct['name'] = $arrEvent['name'];
						$this->intCurStatus = Libs_Const::STATUS_STRUCT_NEW;
					}
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_COMMENT){
						$this->arrTmpStruct['comment'] = $strFieldComment;
					}
					if($arrEvent['event']==Libs_Const::EVENT_PARSE_STRUCT_FIELD
						|| $arrEvent['event']==Libs_Const::EVENT_PARSE_STRUCT_END){
						$strmsg = "fsm run error.\r\n";
						fwrite($pfp, $strmsg);
						return false;
					}
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_INTERFACE_URL){
						$this->arrInterface['url'] = $arrEvent['interface_url'];
					}
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_INTERFACE_CMD){
						$this->arrInterface['cmd'] = $arrEvent['interface_cmd'];
					}
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_INTERFACE_NAME){
						$this->arrInterface['package'] = $arrEvent['package'];
					}
					break;
				case Libs_Const::STATUS_STRUCT_NEW:
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_STRUCT_FIELD){
						$this->arrTmpStruct['field'][$arrEvent['field']['name']] = $arrEvent['field'];
						$this->intCurStatus = Libs_Const::STATUS_STRUCT_FIELD;
					}
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_COMMENT){
						$strFieldComment = $arrEvent['comment'];
					}
					if($arrEvent['event'] == Libs_Const::STATUS_STRUCT_END){
						$arrNewStruct = array(
							'name' => $this->arrTmpStruct['name'],
							'field' => $this->arrTmpStruct['field'],
							'comment' => $this->arrTmpStruct['comment'],
						);
						if($this->intParseType == 3){
							$arrNewStruct['ispublic'] = 1;
						}else{
							$arrNewStruct['from'] = $this->intParseType;
							$arrNewStruct['ispublic'] = 0;
						}
						$this->arrStructRes[$arrNewStruct['name']] = $arrNewStruct;
						$this->arrTmpStruct = array();
						$this->intCurStatus = Libs_Const::STATUS_STRUCT_WIAT;
					}
					break;
				case Libs_Const::STATUS_STRUCT_FIELD;
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_STRUCT_FIELD){
						if($strFieldComment != ''){
							$arrEvent['field']['comment'] = $strFieldComment;
							$strFieldComment = '';
						}
						$this->arrTmpStruct['field'][$arrEvent['field']['name']] = $arrEvent['field'];
					}
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_COMMENT){
						$strFieldComment = $arrEvent['comment'];
					}
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_STRUCT_END){
						$arrNewStruct = array(
							'name' => $this->arrTmpStruct['name'],
							'field' => $this->arrTmpStruct['field'],
							'comment' => $this->arrTmpStruct['comment'],
						);
						if($this->intParseType == 3){
							$arrNewStruct['ispublic'] = 1;
						}else{
							$arrNewStruct['from'] = $this->intParseType;
							$arrNewStruct['ispublic'] = 0;
						}
						$this->arrStructRes[$arrNewStruct['name']] = $arrNewStruct;
						$this->arrTmpStruct = array();
						$this->intCurStatus = Libs_Const::STATUS_STRUCT_WIAT;
					}
					if($arrEvent['event'] == Libs_Const::STATUS_STRUCT_NEW){
						$strmsg = "fsm run error.\r\n";
						fwrite($pfp, $strmsg);
						return false;
					}
					break;
				default:
			}
		}
		fclose($fp);
		return true;
	}
	
	public function genId(){
		 $strSign = 'tbdoc1234';
		$arrDate = gettimeofday();
         $strSec = strval($arrDate['sec']);
         $strUsec = strval($arrDate['usec']);
         $strRand = strval(rand(1000000, 9999999));
         return $strSign . $strRand . $strSec. $strUsec;
	}
	
	public function http_get($url){
		$curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_GET, 1);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $output = curl_exec($curl);
        $code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);
    
        if($code != 200) {
            Lib_Util_Log::warning('errrorrr. code:'.$code.'  error:'.curl_error($curl).'  url:'.$url);
            return false;
        }       
		return $output;
	}
	
	public function genAllId($pfp){
		if(!empty($this->arrStructRes)){
			foreach($this->arrStructRes as $sname => $sinfo){
				$strSid = strval($this->genId());
				$this->arrStructRes[$sname]['sid'] = $strSid;
			}
		}
		if(!empty($this->arrInterface)){
			if($this->intParseType == 2){
				$arrReq = array('name' => $this->arrInterface['name'], 'package' => $this->arrInterface['package']);
				$arrRet = Libs_Service::call("docplat", "getInterfaceByName", $arrReq);
				if(!$arrRet || $arrRet['errno']!=0){
					$strmsg = "call docplat::getInterfaceByName error\r\n";
					fwrite($pfp, $strmsg);
					return false;
				}
				$strIid = strval($arrRet['data']['iid']);
				if($strIid == ''){
					$strmsg = sprintf("get iid error. input:%s, output:%s\r\n", serialize($arrReq), serialize($arrRet));
					fwrite($pfp, $strmsg);
				}
			}else{
				$strIid = strval($this->genId());
			}
			$this->arrInterface['iid'] = $strIid;
		}

		return true;
	}
	
	public function interface2Db($pfp){
		if(!empty($this->arrInterface)){
			$arrReq = array(
				'name' => $this->arrInterface['name'],
				'iid' => $this->arrInterface['iid'],
				'url' => $this->arrInterface['url'],
				'cmd' => $this->arrInterface['cmd'],
				'owner' => strval($this->arrInterface['owner']),
				'comment' => strval($this->arrInterface['comment']),
				'package' => strval($this->arrInterface['package']),
			);
			$arrRet = Libs_Service::call("docplat", "addInterface", $arrReq);
			if(!$arrRet || $arrRet['errno']!=0){
				$strmsg = sprintf("call docplat::addInterface. input:%s, output:%s\r\n", serialize($arrReq), serialize($arrRet));
				fwrite($pfp, $strmsg);
			}
		}

		return true;
	}
	
	public function interfaceStructMap2Db($pfp){
		if(!empty($this->arrInterface) && !empty($this->arrStructRes)){
			foreach($this->arrStructRes as $sname => $sinfo){
				$arrReq = array(
					'iid' => $this->arrInterface['iid'],
					'sid' => $sinfo['sid'],
					'name' => $sinfo['name'],
					'from' => $sinfo['from'],
					'ispublic' => $sinfo['ispublic'],
				);
				if($arrReq['sid'] == ''){
					$strmsg = 'in interfaceStructMap2Db get iid error.\r\n';
					fwrite($pfp, $strmsg);
				}
				$arrRet = Libs_Service::call("docplat", "addInterfaceStructMap", $arrReq);
				if(!$arrRet || $arrRet['errno']!=0){
					$strmsg = 'call docplat::addInterfaceStructMap error\r\n';
					fwrite($pfp, $strmsg);
				}
				//usleep(200000);
			}

		}
		
		if(!empty($this->arrPublicStruct)){
			foreach($this->arrPublicStruct as $arrStruct){
				$arrReq = array(
					'iid' => $this->arrInterface['iid'],
					'sid' => $arrStruct['sid'],
					'name' => $arrStruct['name'],
					'from' => $sinfo['from'],
					'ispublic' => $arrStruct['ispublic'],
				);
				$arrRet = Libs_Service::call("docplat", "addInterfaceStructMap", $arrReq);
				if(!$arrRet || $arrRet['errno']!=0){
					$strmsg = 'call docplat::addInterfaceStructMap error\r\n';
					fwrite($pfp, $strmsg);
				}
				//usleep(200000);
			}
		}

		return true;
	}

	public function getPublicStruct($pfp){
		if(!empty($this->arrStructRes)){
			foreach($this->arrStructRes as $sname => $sinfo){
				foreach($sinfo['field'] as $fname => $finfo){
					if($finfo['type'] == Libs_Const::OBJECT_TYPE){
						if(!isset($this->arrStructRes[$finfo['objname']])){
							$arrReq = array('name' => $finfo['objname'], 'package' => $this->arrInterface['package']);
							$arrRet = Libs_Service::call("docplat", "getCommonStructByName", $arrReq);
							if(!$arrRet || $arrRet['errno']!=0){
								$strmsg = 'call docplat::getCommonStructByName error\r\n';
								fwrite($pfp, $strmsg);
								continue;
							}
							$this->arrStructRes[$sname]['field'][$fname]['objid'] = $arrRet['data']['sid'];
							$this->arrPublicStruct[] = $arrRet['data'];
						}else{
							$this->arrStructRes[$sname]['field'][$fname]['objid'] = $this->arrStructRes[$finfo['objname']]['sid'];
						}
					}
				}
			}
		}

		return true;
	}
	
	public function mapObjectId($pfp){
		if(!empty($this->arrStructRes)){
			foreach($this->arrStructRes as $skey => $sinfo){
				foreach($sinfo['field'] as $fkey => $finfo){
					if($finfo['type']==Libs_Const::OBJECT_TYPE){
						if(isset($this->arrStructRes[$finfo['objname']])){
							$this->arrStructRes[$skey]['field'][$fkey]['objid'] = $this->arrStructRes[$finfo['objname']]['sid'];
						}else{
							$strmsg = 'mapobjectid error\r\n';
							fwrite($pfp, $strmsg);
							return false;
						}
					}
				}
			}
		}

		return true;
	}

	public function struct2Db($pfp){
		if(!empty($this->arrStructRes)){
			foreach($this->arrStructRes as $sname => $sinfo){
				foreach($sinfo['field'] as $sfname => $sfinfo){
					$strmsg = sprintf("in call add field\r\n");
					fwrite($pfp, $strmsg);
					$strFid = strval($this->genId());
					$arrReq = array(
						'fid' => $strFid,
						'sid' => $sinfo['sid'],
						'name' => $sfname,
						'iorder' => intval($sfinfo['order']),
						'ioption' => intval($sfinfo['option']),
						'type' => intval($sfinfo['type']),
						'objname' => $sfinfo['objname'],
						'comment' => strval($sfinfo['comment']),
						'objid' => strval($sfinfo['objid']),
						'isneed' => 1,
					);
					if($sinfo['ispublic'] == 1){
						$strMethod = 'addCommonStructField';
					}else{
						$strMethod = 'addPrivateStructField';
					}
					$arrRet = Libs_Service::call("docplat", $strMethod, $arrReq);
					if(!$arrRet || $arrRet['errno']!=0){
						$strmsg = sprintf("docplat::%s error. input:%s, ret:%s\r\n", $strMethod, serialize($arrReq), serialize($arrRet));
						fwrite($pfp, $strmsg);
					}
					//usleep(200000);
				}
				if($sinfo['ispublic'] == 1){
					$this->arrInterface['iid'] = '';
				}
				$arrReq = array(
					'sid' => $sinfo['sid'],
					'iid' => $this->arrInterface['iid'],
					'name' => $sinfo['name'],
					'comment' => $sinfo['comment'],
					'ispublic' => $sinfo['ispublic'],
					'ifrom' => $sinfo['from'],
					'package' => $this->arrInterface['package'],
				);
				$strmsg = sprintf("in call addStructInfo\r\n");
				fwrite($pfp, $strmsg);
				$arrRet = Libs_Service::call("docplat", "addStructInfo", $arrReq);
				if(!$arrRet || $arrRet['errno']!=0){
					$strmsg = sprintf("docplat::addStructInfo error. input:%s, ret:%s\r\n", serialize($arrReq), serialize($arrRet));
					fwrite($pfp, $strmsg);
				}
				//usleep(200000);
			}
		}

		return true;
	}
}

$strProtoClient = $strCur . '/./conf/protos/tbclient/';
$strProtoTbim = $strCur . '/./conf/protos/tbim/';
$arrProtoPath = array($strProtoClient, $strProtoTbim);
foreach($arrProtoPath as $strPath){
	if($dir = opendir($strPath)){
		$arrReqFile = array();
		$arrResFile = array();
		$arrCommonFile = array();
		while(($file=readdir($dir))!==false){
			if($file!="." && $file!=".."){
				if(strstr($file, 'Req')){
					$arrReqFile[] = $file;
				}else if(strstr($file, 'Res')){
					$arrResFile[] = $file;
				}else if(strstr($file, 'proto')){
					$arrCommonFile[] = $file;
				}
			}
		}
		if(!empty($arrCommonFile)){
			foreach($arrCommonFile as $sfile){
				$file = $strPath . $sfile;
				$strmsg = sprintf("common chose file %s\r\n", $file);
				fwrite($pfp, $strmsg);
				$objFsm = new Protobuf_Fsmnew($file);
				$res = $objFsm->process($pfp);
				if($res == false){
					$strmsg = sprintf("common chose file %s error\r\n", $file);
					fwrite($pfp, $strmsg);
					continue;
				}
				$objFsm->genAllId($pfp);
				$strmsg = sprintf("common gen all id sucess\r\n", $file);
				fwrite($pfp, $strmsg);
				$res = $objFsm->mapObjectId($pfp);
				if($res == false){
					$strmsg = sprintf("common mapObjectId error\r\n", $file);
					fwrite($pfp, $strmsg);
					continue;
				}
				$res = $objFsm->struct2Db($pfp);
				if($res == false){
					$strmsg = sprintf("common struct2db error\r\n", $file);
					fwrite($pfp, $strmsg);
				}
			}
		}
		if(!empty($arrReqFile)){
			foreach($arrReqFile as $sfile){
				$file = $strPath . $sfile;
				$strmsg = sprintf("req choose file %s\r\n", $file);
				fwrite($pfp, $strmsg);
				$objFsm = new Protobuf_Fsmnew($file);
				$res = $objFsm->process($pfp);
				if($res == false){
					$strmsg = sprintf("req chose file %s error\r\n", $file);
					fwrite($pfp, $strmsg);
					continue;
				}
				$objFsm->genAllId($pfp);
				$strmsg = sprintf("req gen all id sucess\r\n", $file);
				fwrite($pfp, $strmsg);
				$res = $objFsm->getPublicStruct($pfp);
				if($res == false){
					$strmsg = sprintf("req parse public struct  error\r\n", $file);
					fwrite($pfp, $strmsg);
					continue;
				}
				$res = $objFsm->struct2Db($pfp);
				if($res == false){
					$strmsg = sprintf("req struct2db error\r\n", $file);
					fwrite($pfp, $strmsg);
				}
				$res = $objFsm->interfaceStructMap2Db($pfp);
				if($res == false){
					$strmsg = sprintf("req interface struct map2db error\r\n", $file);
					fwrite($pfp, $strmsg);
				}
				$res = $objFsm->interface2Db($pfp);
				if($res == false){
					$strmsg = sprintf("req interface2db error\r\n", $file);
					fwrite($pfp, $strmsg);
				}
			}
		}
		if(!empty($arrResFile)){
			foreach($arrResFile as $sfile){
				$file = $strPath . $sfile;
				$strmsg = sprintf("res choose file %s\r\n", $file);
				fwrite($pfp, $strmsg);
				$objFsm = new Protobuf_Fsmnew($file);
				$res = $objFsm->process($pfp);
				if($res == false){
					$strmsg = sprintf("res chose file %s error\r\n", $file);
					fwrite($pfp, $strmsg);
					continue;
				}
				$objFsm->genAllId();
				$strmsg = sprintf("res gen all id sucess\r\n", $file);
				fwrite($pfp, $strmsg);
				$res = $objFsm->getPublicStruct($pfp);
				if($res == false){
					$strmsg = sprintf("res parse public struct  error\r\n", $file);
					fwrite($pfp, $strmsg);
					continue;
				}
				$res = $objFsm->struct2Db($pfp);
				if($res == false){
					$strmsg = sprintf("res struct2db error\r\n", $file);
					fwrite($pfp, $strmsg);
				}
				$res = $objFsm->interfaceStructMap2Db($pfp);
				if($res == false){
					$strmsg = sprintf("res interface struct map2db error\r\n", $file);
					fwrite($pfp, $strmsg);
				}
			}
		}
		closedir($dir); 
	}
}
fwrite($pfp, "fsm end.\r\n");
fclose($pfp);
