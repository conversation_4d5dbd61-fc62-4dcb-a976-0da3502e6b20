<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013/11/21 16:21:00
 * @version 1.0
 * @brief ״̬������protobuf
 * @structs & methods(copied from idl.)
 */
$strCur = dirname(__FILE__);
$strFileConst = $strCur . '/../libs/Const.php';
$strFileService = $strCur . '/../libs/Service.php';
require_once($strFileConst); 
require_once($strFileService); 
class Protobuf_Fsmnew{
	protected $parseFile = 'frsPageRes.proto';
	protected $intCurStatus = 0;
	protected $intEvent = 0;
	protected $arrTmpStruct = array();
	public $arrStructRes = array();
	public $arrInterface = array();
	public $intParseType = 0; //1 req 2 res 3common
	public $arrPublicStruct = array();
	protected $arrBaseTypes = array(
		1 => 'string',
		2 => 'uint32',
		3 => 'uint64',
		4 => 'bool',
		5 => 'int32',
		6 => 'int64',
		7 => 'double',
	);
	const STATUS_STRUCT_WIAT = 0;
	const STATUS_STRUCT_NEW = 1;
	const STATUS_STRUCT_FIELD = 2;
	const STATUS_STRUCT_END = 3;
	const EVENT_PARSE_STRUCT_NAME = 1;
	const EVENT_PARSE_STRUCT_FIELD = 2;
	const EVENT_PARSE_STRUCT_END = 3;
	const EVENT_PARSE_COMMENT = 4;
	const EVENT_PARSE_INTERFACE_CMD = 5;
	const EVENT_PARSE_INTERFACE_NAME = 6;
	const EVENT_PARSE_INTERFACE_URL = 7;
	const EVENT_PARSE_NULL = 8;
	
	public function __construct($file){
		$this->parseFile = $file;
	}

	public function parseFileName(){
		if(strstr($this->parseFile, "Req")){
			$this->intParseType = 1;
			$strFile =  $this->parseFile;
			$arrParsePath = explode("/", $strFile);
			$strFile = $arrParsePath[count($arrParsePath)-1];
			$strInterfaceName = trim(str_replace("Req.proto", "", $strFile));
			$this->arrInterface['name'] = $strInterfaceName;
			$this->arrInterface['type'] = 1;
		}
		else if(strstr($this->parseFile, "Res")){
			$this->intParseType = 2;
			$strFile =  $this->parseFile;
			$arrParsePath = explode("/", $strFile);
			$strFile = $arrParsePath[count($arrParsePath)-1];
			$strInterfaceName = trim(str_replace("Res.proto", "", $strFile));
			$this->arrInterface['name'] = $strInterfaceName;
			$this->arrInterface['type'] = 2;
		}else{
			$this->intParseType = 3;
		}
		return true;
	}
	private function parseStructName($strLine){
		$ret = preg_match("/[A-Z][a-z].*?{/", $strLine, $matches);
		if($ret == false){
			$ret = preg_match("/[A-Z][a-z].*?Idl/", $strLine, $matches);
			if($ret == false){
				if(strstr($strLine, "message")){
					$strName = trim(str_replace("message", "", $strLine));
				}else{
					echo "pregmatch error.";
					return false;
				}
			}else{
				$strName = trim($matches[0]);
			}
		}else{
			$strName = trim(str_replace("{", "", $matches[0]));
		}
		//����ļ�̫������,���øľ�д��
		if(strstr($this->parseFile, 'searchFriendRes.proto') && $strName=='Info'){
			$strName = 'userInfo';
		}
		return $strName;
	}
	
	private function parseStructField($strLine){
		$arrFieldInfo = array();
		$arrSplit = explode("=", $strLine);
		$arrFieldInfo['order'] = intval(trim(str_replace(";", "", $arrSplit[1])));
		$strFieldInfo = $arrSplit[0];
		if(strstr($strFieldInfo, "optional")){
			$arrFieldInfo['option'] = 1;
			$strFieldInfo = trim(str_replace("optional", "", $strFieldInfo));
		}
		if(strstr($strFieldInfo, "repeated")){
			$arrFieldInfo['option'] = 2;
			$strFieldInfo = trim(str_replace("repeated", "", $strFieldInfo));
		}
		$bolBaseType = false;
		foreach(Libs_Const::$arrBaseTypes as $key => $value){
			if(strstr($strFieldInfo, $value)){
				$arrFieldInfo['type'] = intval($key);
				$strFieldInfo = trim(str_replace($value, "", $strFieldInfo));
				$bolBaseType = true;
				break;
			}
		}
		if(!$bolBaseType){
			$arrFieldInfo['type'] = Libs_Const::OBJECT_TYPE; 
			$ret = preg_match("/[A-Z][a-z].*?\s/", $strLine, $matches);
			if($ret == false){
				echo "pregmatch error.";
				return false;
			}
			$arrFieldInfo['objname'] = trim($matches[0]);
			$strFieldInfo = trim(str_replace($arrFieldInfo['objname'], "", $strFieldInfo));
		}
		$arrFieldInfo['name'] = trim($strFieldInfo);
		//����ļ�̫������,���øľ�д��
		if(strstr($this->parseFile, 'searchFriendRes.proto') && $arrFieldInfo['name']=='user user_info'){
			$arrFieldInfo['name'] = 'user_info';
			$arrFieldInfo['objname'] = 'userInfo';
		}
		return $arrFieldInfo;
	}

	private function parseEvent($strLine){
		if(preg_match("/^message\s.*?{/", $strLine)){
			$sname = $this->parseStructName($strLine);
			if($sname == false){
				echo "parse struct name error.";
				return false;
			}
			//case /home/<USER>/orp001/conf/protos/tbclient/searchFriendReq.proto
			if($sname == 'ReqData'){
				$sname = 'DataReq';
			}
			if($sname == 'ResData'){
				$sname = 'DataRes';
			}
			return array(
				'event' => Libs_Const::EVENT_PARSE_STRUCT_NAME,
				'name' => $sname,
			);
		}
		else if(preg_match("/^message\s.*?Idl/", $strLine)){
			$sname = $this->parseStructName($strLine);
			if($sname == false){
				echo "parse struct name error.";
				return false;
			}
			//case /home/<USER>/orp001/conf/protos/tbclient/searchFriendReq.proto
			if($sname == 'ReqData'){
				$sname = 'DataReq';
			}
			if($sname == 'ResData'){
				$sname = 'DataRes';
			}
			return array(
				'event' => Libs_Const::EVENT_PARSE_STRUCT_NAME,
				'name' => $sname,
			);
		}
		else if(preg_match("/^message\s.*?/", $strLine)){
			$sname = $this->parseStructName($strLine);
			if($sname == false){
				echo "parse struct name error.";
				return false;
			}
			//case /home/<USER>/orp001/conf/protos/tbclient/searchFriendReq.proto
			if($sname == 'ReqData'){
				$sname = 'DataReq';
			}
			if($sname == 'ResData'){
				$sname = 'DataRes';
			}
			return array(
				'event' => Libs_Const::EVENT_PARSE_STRUCT_NAME,
				'name' => $sname,
			);
		}
		if(preg_match("/^optional\s.*?=\s*[1-9]*/", $strLine) || preg_match("/^repeated\s.*?=\s*[1-9]*;/", $strLine)){
			$arrField = $this->parseStructField($strLine);
			if($arrField === false){
				echo "parse struct field error.";
				return false;
			}
			//case /home/<USER>/orp001/conf/protos/tbclient/checkPostRes.proto
			if($arrField['objname'] == 'ReqData'){
				$arrField['objname']= 'DataReq';
			}
			if($arrField['objname'] == 'ResData'){
				$arrField['objname']= 'DataRes';
			}
			return array(
				'event' => Libs_Const::EVENT_PARSE_STRUCT_FIELD,
				'field' => $arrField,
			);
		}
		if(strstr($strLine, "//")){
			if(strstr($strLine, "cmd:")){
				$arrExplode = explode(":", $strLine);
				$strCmd = strval(trim($arrExplode[count($arrExplode)-1]));
				return array(
					'event' => Libs_Const::EVENT_PARSE_INTERFACE_CMD, 
					'interface_cmd' => $strCmd,
				);
			}
			if(strstr($strLine, "url:")){
				$strLine = str_replace("//", "", $strLine);
				$strLine = str_replace("url:", "", $strLine);
				$strUrl = strval($strLine);
				return array(
					'event' => Libs_Const::EVENT_PARSE_INTERFACE_URL,
					'interface_url' => $strUrl,
				);
			}
			$strComment = str_replace("//", "", $strLine);
			return array(
				'event' => Libs_Const::EVENT_PARSE_COMMENT,
				'comment' => $strComment,
			);
		}
		
		if($strLine=="}" || $strLine=="};"){
			return array(
				'event' => Libs_Const::EVENT_PARSE_STRUCT_END,
			);
		}
		
		if($this->intParseType == 3){
			if(preg_match("/package\s[a-z]*;/", $strLine)){
				$strLine = str_replace("package", "", $strLine);
				$strLine = str_replace(";", "", $strLine);
				$strPackage = strval(trim($strLine));
				return array(
					'event' => Libs_Const::EVENT_PARSE_INTERFACE_NAME,
					'package' => $strPackage,
				);
			}
		}else{
			if(preg_match("/package\s[a-z]*.[A-Z].*;/", $strLine)){
				$strLine = str_replace("package", "", $strLine);
				$arrLine = explode(".", trim($strLine));
				$strPackage = strval(trim($arrLine[0]));
				return array(
					'event' => Libs_Const::EVENT_PARSE_INTERFACE_NAME,
					'package' => $strPackage,
				);
			}
		}
		
		return array('event' => Libs_Const::EVENT_PARSE_NULL);
	}


	public function process(){
		if($this->parseFile == '' || empty($this->parseFile)){
			echo "parse file empty.";
			return false;
		}
		$this->parseFileName();
		$fp = fopen($this->parseFile, "r");
		if($fp === false){
			echo "fopen file error.\r\n";
			return false;
		}
		$strFieldComment = '';		//�Ȼ�ȡ,������
		while(!feof($fp)){
			$strLine = trim(fgets($fp));
			if($strLine == ''){
				continue;
			}
			$arrEvent = $this->parseEvent($strLine);
			if($arrEvent === false){
				echo "fsm parse event error.\r\n";
				return false;
			}
			switch($this->intCurStatus){
				case Libs_Const::STATUS_STRUCT_WIAT:
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_STRUCT_NAME){
						$this->arrTmpStruct['name'] = $arrEvent['name'];
						$this->intCurStatus = Libs_Const::STATUS_STRUCT_NEW;
					}
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_COMMENT){
						$this->arrTmpStruct['comment'] = $strFieldComment;
					}
					if($arrEvent['event']==Libs_Const::EVENT_PARSE_STRUCT_FIELD
						|| $arrEvent['event']==Libs_Const::EVENT_PARSE_STRUCT_END){
						echo "fsm run error.\r\n";
						return false;
					}
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_INTERFACE_URL){
						$this->arrInterface['url'] = $arrEvent['interface_url'];
					}
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_INTERFACE_CMD){
						$this->arrInterface['cmd'] = $arrEvent['interface_cmd'];
					}
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_INTERFACE_NAME){
						$this->arrInterface['package'] = $arrEvent['package'];
					}
					break;
				case Libs_Const::STATUS_STRUCT_NEW:
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_STRUCT_FIELD){
						$this->arrTmpStruct['field'][$arrEvent['field']['name']] = $arrEvent['field'];
						$this->intCurStatus = Libs_Const::STATUS_STRUCT_FIELD;
					}
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_COMMENT){
						$strFieldComment = $arrEvent['comment'];
					}
					break;
				case Libs_Const::STATUS_STRUCT_FIELD;
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_STRUCT_FIELD){
						if($strFieldComment != ''){
							$arrEvent['field']['comment'] = $strFieldComment;
							$strFieldComment = '';
						}
						$this->arrTmpStruct['field'][$arrEvent['field']['name']] = $arrEvent['field'];
					}
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_COMMENT){
						$strFieldComment = $arrEvent['comment'];
					}
					if($arrEvent['event'] == Libs_Const::EVENT_PARSE_STRUCT_END){
						$arrNewStruct = array(
							'name' => $this->arrTmpStruct['name'],
							'field' => $this->arrTmpStruct['field'],
							'comment' => $this->arrTmpStruct['comment'],
						);
						if($this->intParseType == 3){
							$arrNewStruct['ispublic'] = 1;
						}else{
							$arrNewStruct['from'] = $this->intParseType;
							$arrNewStruct['ispublic'] = 0;
						}
						$this->arrStructRes[$arrNewStruct['name']] = $arrNewStruct;
						$this->arrTmpStruct = array();
						$this->intCurStatus = Libs_Const::STATUS_STRUCT_WIAT;
					}
					if($arrEvent['event'] == Libs_Const::STATUS_STRUCT_NEW){
						echo "fsm run error.\r\n";
						return false;
					}
					break;
				default:
			}
		}
		fclose($fp);
		return true;
	}
	
	public function interface2Db(){
		if(!empty($this->arrInterface)){
			$arrReq = array(
				'name' => $this->arrInterface['name'],
				'iid' => $this->arrInterface['iid'],
				'url' => $this->arrInterface['url'],
				'cmd' => $this->arrInterface['cmd'],
				'owner' => strval($this->arrInterface['owner']),
				'comment' => strval($this->arrInterface['comment']),
			);
			$arrRet = Libs_Service::call("docplat", "addInterface", $arrReq);
			if(!$arrRet || $arrRet['errno']!=0){
				echo "call docplat::addInterface error\r\n";
				return false;
			}
		}

		return true;
	}
	
	public function interfaceStructMap2Db(){
		if(!empty($this->arrInterface) && !empty($this->arrStructRes)){
			foreach($this->arrStructRes as $sname => $sinfo){
				$arrReq = array(
					'iid' => $this->arrInterface['iid'],
					'sid' => $sinfo['sid'],
					'name' => $sinfo['name'],
					'from' => $sinfo['from'],
					'ispublic' => $sinfo['ispublic'],
				);
				$arrRet = Libs_Service::call("docplat", "addInterfaceStructMap", $arrReq);
				if(!$arrRet || $arrRet['errno']!=0){
					echo "call docplat::addInterfaceStructMap error\r\n";
					return false;
				}
			}

		}
		
		if(!empty($this->arrPublicStruct)){
			foreach($this->arrPublicStruct as $arrStruct){
				$arrReq = array(
					'iid' => $this->arrInterface['iid'],
					'sid' => $arrStruct['sid'],
					'name' => $arrStruct['name'],
					'from' => $sinfo['from'],
					'ispublic' => $arrStruct['ispublic'],
				);
				$arrRet = Libs_Service::call("docplat", "addInterfaceStructMap", $arrReq);
				if(!$arrRet || $arrRet['errno']!=0){
					echo "call docplat::addInterfaceStructMap error\r\n";
					return false;
				}
			}
		}

		return true;
	}

	public function getPublicStruct(){
		if(!empty($this->arrStructRes)){
			foreach($this->arrStructRes as $sname => $sinfo){
				foreach($sinfo['field'] as $fname => $finfo){
					if($finfo['type'] == Libs_Const::OBJECT_TYPE){
						if(!isset($this->arrStructRes[$finfo['objname']])){
							$arrReq = array('name' => $finfo['objname']);
							$arrRet = Libs_Service::call("docplat", "getStructByName", $arrReq);
							if(!$arrRet || $arrRet['errno']!=0){
								echo "call docplat::getStructByName error\r\n";
								continue;
							}
							$this->arrStructRes[$sname['field'][$fname]['objid']] = $arrRet['data']['sid'];
							$this->arrPublicStruct[] = $arrRet['data'];
						}else{
							$this->arrStructRes[$sname['field'][$fname]['objid']] = $this->arrStructRes[$finfo['objname']]['sid'];
						}
					}
				}
			}
		}

		return true;
	}
	
	public function mapObjectId(){
		if(!empty($this->arrStructRes)){
			foreach($this->arrStructRes as $skey => $sinfo){
				foreach($sinfo['field'] as $fkey => $finfo){
					if($finfo['type']==Libs_Const::OBJECT_TYPE){
						if(isset($this->arrStructRes[$finfo['objname']])){
							$finfo['objid'] = $this->arrStructRes[$finfo['objname']['sid']];
						}else{
							echo "mapobjectid error\r\n";
							return false;
						}
					}
				}
			}
		}

		return true;
	}

	public function struct2Db(){
		if(!empty($this->arrStructRes)){
			foreach($this->arrStructRes as $sname => $sinfo){
				foreach($sinfo['field'] as $sfname => $sfinfo){
					$strFid = strval($this->genId());
					$arrReq = array(
						'fid' => $strFid,
						'sid' => $sinfo['sid'],
						'name' => $sfname,
						'order' => intval($sfinfo['order']),
						'option' => intval($sfinfo['option']),
						'type' => intval($sfinfo['type']),
						'objname' => $sfinfo['objname'],
						'comment' => intval($sfinfo['comment']),
						'objid' => intval($sfinfo['objid']),
					);
					if($sinfo['ispublic'] == 1){
						$strMethod = 'addCommonStructField';
					}else{
						$strMethod = 'addPrivateStructField';
					}
					$arrRet = Libs_Service::call("docplat", $strMethod, $arrReq);
					if(!$arrRet || $arrRet['errno']!=0){
						echo "call docplat::addPrivateStructField error\r\n";
						return false;
					}
				}
				$arrReq = array(
					'sid' => $sinfo['sid'],
					'iid' => $this->arrInterface['iid'],
					'name' => $sinfo['name'],
					'comment' => $sinfo['comment'],
					'ispublic' => $sinfo['ispublic'],
					'from' => $sinfo['from'],
				);
				$arrRet = Libs_Service::call("docplat", "addStructInfo", $arrReq);
				if(!$arrRet || $arrRet['errno']!=0){
					echo "call docplat::addStructInfo error\r\n";
					return false;
				}
			}
		}

		return true;
	}
	
	public function updateCommonReadonly(){
		if(empty($this->arrStructRes)){
			var_dump($this->arrStructRes);
			echo "parse interface struct error.\r\n";
			return false;
		}
		foreach($this->arrStructRes as $skey => $sinfo){
			//��ȡ�����ṹ��
			$arrReq = array('names' => array($sinfo['name']), 'package' => $this->arrInterface['package']);
			$arrRet = Libs_Service::call("docplat", "getCommonStructsByName", $arrReq);
			if(!$arrRet || $arrRet['errno']!=0){
				echo "call docplat::getCommonStructsByName error\r\n";
				return false;
			}
			$strSid = $arrRet['data'][$sinfo['name']]['sid'];
			$arrReq = array('sids' => array($strSid));
			$arrRet = Libs_Service::call("docplat", "queryCommonStructField", $arrReq);
			if(!$arrRet || $arrRet['errno']!=0){
				echo "call docplat::queryCommonStructField error\r\n";
				return false;
			}
			$arrFields = $arrRet['data'];
			$arrUpdateNames = array();
			foreach($sinfo['field'] as $fkey => $finfo){
				$arrUpdateNames[] = $finfo['name'];
			}
			//�����ֶ�
			foreach($arrFields[$strSid] as $finfo){
				if(in_array($finfo['name'], $arrUpdateNames)){
					$arrReq = array(
						'fid' => $finfo['fid'],
						'readonly' => 1,
					);
					$arrRet = Libs_Service::call("docplat", "updateCommonStructField", $arrReq);
					if(!$arrRet || $arrRet['errno']!=0){
						echo "call docplat::updateCommonStructField error\r\n";
						return false;
					}
				}
			}
			$arrReq = array(
				'sid' => $strSid,
				'readonly' => 1,
			);
			$arrRet = Libs_Service::call("docplat", "updateStructInfo", $arrReq);
			if(!$arrRet || $arrRet['errno']!=0){
				echo "call docplat::updateStructInfo error\r\n";
				var_dump($arrReq);
				var_dump($arrRet);
				return false;
			}
		}

		return true;
	}

	public function updatePrivateReadonly(){
		if(empty($this->arrInterface) || empty($this->arrStructRes)){
			var_dump($this->arrStructRes);
			var_dump($this->arrInterface);
			echo "parse interface struct error.\r\n";
			return false;
		}
		//��ѯ�ӿ�
		if(!empty($this->arrInterface)){
			$strName = $this->arrInterface['name'];
			$arrReq = array('name' => $strName, 'package' => $this->arrInterface['package']);
			$arrRet = Libs_Service::call("docplat", "getInterfaceByName", $arrReq);
			if(!$arrRet || $arrRet['errno']!=0){
				echo "call docplat::getInterfaceByName error\r\n";
				return false;
			}
			$strIid = $arrRet['data']['iid'];
			$intFrom = $this->arrInterface['type'];
		}
		if(!empty($this->arrStructRes)){
			foreach($this->arrStructRes as $skey => $sinfo){
				//�ӿ�ӳ���ȡ�ṹ��
				$arrReq = array(
					'iid' => $strIid,
					'ifrom' => $intFrom,
					'name' => $sinfo['name'],
				);
				$arrRet = Libs_Service::call("docplat", "getPrivateStructByName", $arrReq);
				if(!$arrRet || $arrRet['errno']!=0){
					echo "call docplat::getPrivateStructByName error\r\n";
					return false;
				}
				$strSid = $arrRet['data']['sid'];
				$arrReq = array('sids' => array($strSid));
				$arrRet = Libs_Service::call("docplat", "queryPrivateStructField", $arrReq);
				if(!$arrRet || $arrRet['errno']!=0){
					echo "call docplat::queryPrivateStructField error\r\n";
					return false;
				}
				$arrFields = $arrRet['data'];
				$arrUpdateNames = array();
				foreach($sinfo['field'] as $fkey => $finfo){
					$arrUpdateNames[] = $finfo['name'];
				}
				//�����ֶ�
				foreach($arrFields[$strSid] as $finfo){
					if(in_array($finfo['name'], $arrUpdateNames)){
						$arrReq = array(
							'fid' => $finfo['fid'],
							'readonly' => 1,
						);
						$arrRet = Libs_Service::call("docplat", "updatePrivateStructField", $arrReq);
						if(!$arrRet || $arrRet['errno']!=0){
							echo "call docplat::updatePrivateStructFielderror\r\n";
							return false;
						}
					}
				}
				$arrReq = array(
					'sid' => $strSid,
					'readonly' => 1,
				);
				$arrRet = Libs_Service::call("docplat", "updateStructInfo", $arrReq);
				if(!$arrRet || $arrRet['errno']!=0){
					echo "call docplat::updateStructInfo error\r\n";
				var_dump($arrReq);
				var_dump($arrRet);
					return false;
				}
			}
		}

		$arrReq = array(
			'iid' => $strIid,
			'readonly' => 1,
			'isScript' => 1,
		);
		$arrRet = Libs_Service::call("docplat", "updateInterfaceInfo", $arrReq);
		if(!$arrRet || $arrRet['errno']!=0){
			echo "call docplat::updateInterfaceInfo error\r\n";
			return false;
		}
		return true;
	}
}

$strProtoClient = $strCur . '/../../../conf/protos/tbclient/';
$strProtoTbim = $strCur . '/../../../conf/protos/tbim/';
$arrProtoPath = array($strProtoClient, $strProtoTbim);
foreach($arrProtoPath as $strPath){
	if($dir = opendir($strPath)){
		$arrFile = array();
		while(($file=readdir($dir))!==false){
			if($file!="." && $file!=".."){
				$arrFile[] = $file;
			}
		}
		if(!empty($arrFile)){
			foreach($arrFile as $sfile){
				$file = $strPath . $sfile;
				var_dump($file);
				$objFsm = new Protobuf_Fsmnew($file);
				$objFsm->process();
				if($objFsm->intParseType == 3){
					$objFsm->updateCommonReadonly();
				}else{
					$objFsm->updatePrivateReadonly();
				}
			}
		}
		closedir($dir); 
	}
}
