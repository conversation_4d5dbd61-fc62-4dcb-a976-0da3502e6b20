<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014:12:09 19:04:26
 * @version 1.0
 * @structs & methods(copied from idl.)
*/

class getStructListAction extends Libs_Base{
	private $strIid = '';

	public function _init(){
		$this->strIid = $this->getInput('iid', '');
		// get user name
		$this->strCurrentUserName = Util_Actionbaseext::getCurUserName();

		return true;
	}

	protected function process(){
		if($this->strIid == ''){
			$this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
			return false;
		}

		$arrReq = array('iid' => $this->strIid);
		$arrRet = Libs_Service::call('docplat', 'getInterfaceStructMap', $arrReq);
		if(!$arrRet || $arrRet['errno']!=Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning(sprintf("call getInterfaceStructMap error.input:[%s]", serialize($arrReq)));
			$this->_jsonRet($arrRet['errno']);
			return false;
		}
		$arrSids = array();
		$arrStructs = array();
		foreach($arrRet['data'][$this->strIid] as $smap){
			$arrSids[] = $smap['sid'];
		}
		if(!empty($arrSids)){
			$arrReq = array('sids' => $arrSids);
			$arrRet = Libs_Service::call('docplat', 'getStructInfos', $arrReq);
			if(!$arrRet || $arrRet['errno']!=Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning(sprintf("call getStructInfos error.input:[%s]", serialize($arrReq)));
				$this->_jsonRet($arrRet['errno']);
				return false;
			}	
			$arrStructInfos = $arrRet['data'];
			$arrCommonSids = array();
			$arrPrivateSids = array();
			foreach($arrStructInfos as $sinfo){
				if($sinfo['ispublic'] == Libs_Const::STRUCT_TYPE_COMMON){
					$arrCommonSids[] = $sinfo['sid'];
				}else{
					$arrPrivateSids[] = $sinfo['sid'];
				}
			}

			if(!empty($arrCommonSids)){
				$arrReq = array('sids' => $arrCommonSids);
				$arrRet = Libs_Service::call('docplat', 'queryCommonStructField', $arrReq);
				if(!$arrRet || $arrRet['errno']!=Tieba_Errcode::ERR_SUCCESS){
					Bingo_Log::warning(sprintf("call queryCommonStructField error.input:[%s]", serialize($arrReq)));
					$this->_jsonRet($arrRet['errno']);
					return false;
				}		
				$arrCommonFields = $arrRet['data'];
				foreach($arrCommonFields as $skey => $sinfo){
					$arrSmap = array();
					foreach($sinfo as $finfo){
						$arrSmap[$finfo['iorder']] = $finfo;
					}
					$arrCommonFields[$skey] = $arrSmap;
				}
			}
		
			if(!empty($arrPrivateSids)){
				$arrReq = array('sids' => $arrPrivateSids);
				$arrRet = Libs_Service::call('docplat', 'queryPrivateStructField', $arrReq);
				if(!$arrRet || $arrRet['errno']!=Tieba_Errcode::ERR_SUCCESS){
					Bingo_Log::warning(sprintf("call queryPrivateStructField error.input:[%s]", serialize($arrReq)));
					$this->_jsonRet($arrRet['errno']);
					return false;
				}
				$arrPrivateFields = $arrRet['data'];
				foreach($arrPrivateFields as $skey => $sinfo){
					$arrSmap = array();
					foreach($sinfo as $finfo){
						$arrSmap[$finfo['iorder']] = $finfo;
					}
					$arrPrivateFields[$skey] = $arrSmap;
				}
			}
		
			foreach($arrStructInfos as $skey => $sinfo){
				if($sinfo['ispublic']==Libs_Const::STRUCT_TYPE_COMMON && isset($arrCommonFields[$sinfo['sid']])){
					$arrStructInfos[$skey]['field'] = $arrCommonFields[$sinfo['sid']];
				}else{
					$arrStructInfos[$skey]['field'] = $arrPrivateFields[$sinfo['sid']];
				}
			}
		}
	
		$arrReq = array('iid' => $this->strIid);
		$arrRet = Libs_Service::call('docplat', 'getInterfaceByIid', $arrReq);
		if(!$arrRet || $arrRet['errno']!=Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning(sprintf("call getInterfaceByIid error.input:[%s]", serialize($arrReq)));
			$this->_jsonRet($arrRet['errno']);
			return false;
		}
		$arrInterface = $arrRet['data'];
		$arrInterface['canEdit'] = 1;
		if (1 === intval($arrInterface['status'])) {
			$arrInterface['canEdit'] = $arrInterface['lastedituser'] === $this->strCurrentUserName ? 1 : 0;
		}
		$arrData = array('struct' => $arrStructInfos, 'interface' => $arrInterface);
		$this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, $arrData);

		return true;
	}
}
