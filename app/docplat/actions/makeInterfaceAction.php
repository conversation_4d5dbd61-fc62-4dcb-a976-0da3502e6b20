<?php

/**
 * make common proto file
 * User: yangpingping
 * Date: 2014/12/02
 * Time: 17:52
 */
class makeInterfaceAction extends Libs_Base
{
	protected $strFileName = '';
	protected $strPackageName = '';
	protected $strType = '';
	protected $arrInterface = array();
	 
    protected function _init()
    {
    	$this->strFileName = $this->getInput('file_name', '');
		$this->strPackageName = $this->getInput('package_name', '');
		$this->strType = $this->getInput('type', '');
        return true;
    }
	
	protected function process() {
		//1 获取接口内容
		$arrInput = array (
			'name' => $this->strFileName ,
			'package' => $this->strPackageName,
		);
		$this->arrInterface = $this->getInterfaceContent($arrInput);
		
		//2 获取头部内容
		$strHead = $this->getInterfaceHead();
		
		//3 获取输入参数,写文件,用户下载
		if($this->strType =='req'){
			$strReq = $this->getInterfaceReq();
			$strReqFileName = $this->strFileName.'Req.proto';
			Libs_File::downloadFile( $strHead.$strReq, $strReqFileName);
			return ;	
		}
		
		//4获取输出参数,写文件,用户下载
		$strRes = $this->getInterfaceRes();
		$strResFileName = $this->strFileName.'Res.proto';
		Libs_File::downloadFile( $strHead.$strRes,$strResFileName );
		return ;
	
	}
	
	 
   private function getInterfaceContent($arrInput){
		$output = Libs_Service::call ( 'docplat', 'makeInterfaceFile', $arrInput);
		$arrInterface = $output ['data'];
		return $arrInterface;
   	
   }
   private function getInterfaceHead(){
   		$head = "//cmd:" . $this->arrInterface ['interface']['cmd'] . PHP_EOL;
   		if(!empty($this->arrInterface ['interface']['url'])){
			$head .= "//url:" . $this->arrInterface ['interface']['url'] . PHP_EOL;
   		}
		$head .= "package " . $this->arrInterface ['interface']['package'].'.'.ucfirst($this->arrInterface ['interface']['name']) . ';' . PHP_EOL;
		$head .= 'import "'.Libs_Const::$arrCommonPackage[$this->arrInterface ['interface']['package']].'";' . PHP_EOL;
		return $head;
   }
   
    private function getInterfaceReq() {		
		$req = '';
		foreach ( $this->arrInterface['req'] as $key => $value ) {
			$req .= sprintf ( 'message %s{' . PHP_EOL, $key );
			foreach ( $value as $one ) {
				$req .= $one;
			}
			$req .= '}' . PHP_EOL;
		}
		return $req;
		
		 
	}
	private function getInterfaceRes() {		
		foreach ( $this->arrInterface ['res'] as $key => $value ) {
			$res .= sprintf ( 'message %s{' . PHP_EOL, $key );
			foreach ( $value as $one ) {
				$res .= $one;
			}
			$res .= '}' . PHP_EOL;
		}
		return $res;
		 
	}
    
	

     
}
