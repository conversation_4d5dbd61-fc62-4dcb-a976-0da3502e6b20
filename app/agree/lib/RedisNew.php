<?php
/**
 * Created by PhpStorm.
 * User: x<PERSON><PERSON><PERSON>
 * Date: 17/3/16
 * Time: 下午6:15
 */

class Lib_RedisNew {
    protected static $redis = null;
    protected static $tryCount = 2;

    // redis实例
    const REDIS_PID = 'agree';
//	const REDIS_PID = 'push';


    /**
     * @param string $redisPid
     * @return bool
     */
    private static function initRedis($redisPid = null) {
        if(is_null($redisPid) ){
            $redisPid = self::REDIS_PID;
        }
        if(self::$redis == null) {
            self::$redis = new Bingo_Cache_Redis($redisPid);
            if(self::$redis == false) {
                Bingo_Log::warning("init redis ['.$redisPid.'] fail:" . serialize(Bd_RalRpc::get_error()));
                return false;
            }
        }
        return true;
    }

    /**
     * @param string $redisPid
     * @return bool
     */
    public static function getRedis($redisPid = null){
        if (!self::initRedis($redisPid)){
            return false;
        }

        return self::$redis;
    }

    /**
     * @param $method
     * @param $req
     * @param string $redisPid
     * @return bool
     */
    public static function call($method, $req, $redisPid = self::REDIS_PID) {
        $try = 0;
        if (!self::initRedis($redisPid)){
            return false;
        }

        while ($try < self::$tryCount) {
            $res = self::$redis->$method($req);
            if(Tieba_Errcode::ERR_SUCCESS == $res['err_no']){
                return $res;
            }
            $try++;
        }
        Bingo_Log::warning('call redis:'.$method.' fails. error msg: '. $res['err_msg']);
        return false;
    }

    /**
     * @brief: INCR
     * @param
     * @return
     **/
    public static function INCR($arrInput){
        if (!self::initRedis()){
            return false;
        }
        $arrRet = self::$redis->INCR($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'][$arrInput['key']];
        return $data;
    }

    /**
     * @brief: EXPIRE
     * @param
     * @return
     **/
    public static function EXPIRE($arrInput){
        if (!self::initRedis()){
            return false;
        }
        $arrRet = self::$redis->EXPIRE($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

}