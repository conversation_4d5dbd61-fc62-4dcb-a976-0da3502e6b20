<?php
/**
 * User: <EMAIL>
 * Date: 2017/6/7
 * Time: 16:13
 */

class Service_Nickname_Mis {

    // push官方账号 系统消息
    const OFFICIAL_ID = 3222425470;
    // push内容
    const NOTIFY_CONTENT = '亲爱的贴吧用户，您的昵称因包含违规内容，已被撤销。如有问题，请到贴吧服务中心反馈。';

    /**
     * @brief  撤销昵称，将用户的昵称设置为用户名
     * @param  $arrInput
     *  call_from
     *      admin 昵称后台
     *      ueg ueg策略撤销
     * @return mixed
     */
    public static function revokeNicknameByUid($arrInput) {
        // 获取用户用户名
        $intUserId = intval($arrInput['user_id']);
        if ($intUserId <= 0) {
            Bingo_Log::warning("user id is not illegal. [input = %s]", serialize($arrInput));
            return Lib_Func::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        // 请求来源
        $strCallFrom = !empty($arrInput['call_from']) ? strval($arrInput['call_from']) : 'admin';

        // 获取用户名
        $arrParam = array(
            'user_id' => $intUserId,
        );
        $arrRet = Tieba_Service::call('user', 'getUserData', $arrParam, null, null, 'post', 'php', 'utf-8');

        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call user getUserData failed. [input = %s] [output = %s]",
                serialize($arrParam),serialize($arrRet)));
            return Lib_Func::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        //$strUserName = $arrRet['user_info'][0]['user_name'];
        $strNickname = Molib_Util_User::initNickname($intUserId);
        // 昵称后台撤销，用户名重置成另外一种规则昵称
        if ($strCallFrom == 'admin') {
            $strNickname = $strNickname . 'x';
        } else if ($strCallFrom == 'ueg') {
            // ueg撤销，用户名不为空清空昵称，用户名为空则重置成默认昵称
            //$strNickname = !empty($strUserName) ? '' : $strNickname;
            //本次策略，不管用户名是否为空，都要有昵称 add by xiaofeng
            $strNickname = $strNickname . 'u';
        }

        // 用户名不为空清空昵称，用户名为空则重置成默认昵称（昵称后台和ueg目前处理逻辑一致）
        //$strNickname = !empty($strUserName) ? '' : $strNickname;

        // 设定昵称为默认昵称
        $arrParam = array(
            'user_id'    => $intUserId,
            'attr_name'  => 'user_nickname',
            'attr_value' => $strNickname,
        );
        $arrRet = Tieba_Service::call('user', 'setUserAttr', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call user setUserAttr failed. [input = %s] [output = %s]",
                serialize($arrParam), serialize($arrRet)));
            return Lib_Func::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        // 消息push
        self::_pushNotify($intUserId);

        return Lib_Func::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getUserInfoByNickname($arrInput) {
        $strNickname = strval($arrInput['nickname']);
        if (empty($strNickname)) {
            Bingo_Log::warning("user id is not illegal. [input = %s]", serialize($arrInput));
            return Lib_Func::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrParam = array();
    }

    /**
     * @brief  获取修改昵称的用户列表
     * @param  $arrInput
     * @return array
     */
    public static function getModifyUserList($arrInput) {
        $intUserId    = intval($arrInput['user_id']);
        $intStartTime = intval($arrInput['start']);
        $intEndTime   = intval($arrInput['end']);

        $intPn = intval($arrInput['pn']) <= 0 ? 1 : intval($arrInput['pn']);
        $intRn = intval($arrInput['rn']) <= 0 ? 20 : intval($arrInput['rn']);
        $intOffset = ($intPn - 1) * $intRn;

        if ($intUserId > 0) {
            if (isset($arrParam['cond'])) {
                $arrParam['cond'] .= " user_id = $intUserId ";
            }
            else {
                $arrParam['cond'] = " user_id = $intUserId ";
            }
        }
        if ($intStartTime > 0 && $intEndTime > 0 && $intEndTime > $intStartTime) {
            if (isset($arrParam['cond'])) {
                $arrParam['cond'] .= " update_time > $intStartTime and update_time < $intEndTime";
            }
            else {
                $arrParam['cond'] = " update_time > $intStartTime and update_time < $intEndTime";
            }
        }
        $arrParam['append'] = "limit $intOffset, $intRn";
        $arrRet = Dl_Db_NicknameModifyRecord::select($arrParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            return Lib_Func::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrOutput['rows'] = $arrRet['data'];

        foreach ($arrOutput['rows'] as &$value) {
            $value['nickname'] = base64_decode($value['nickname']);
        }

        $arrRet = Dl_Db_NicknameModifyRecord::getTotalCount($arrParam);
        $arrOutput['count'] = intval($arrRet['data']);
        return Lib_Func::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }


    /**
     * 消息推送
     * @param $intToUid
     * @return bool|mixed|null
     */
    private static function _pushNotify($intToUid) {

        $arrParam = array(
            'group_id' => 0,  //必填，填0
            'msg_type' => 1,  //1普通文本  2图片  3语音 10图文消息
            'user_id' => self::OFFICIAL_ID,  //消息发送方id, 在礼物系统中，写死为贴吧礼物小天使
            'user_type' => 4,  //消息发送方类型，0普通用户，1平台化官方吧用户类型，3订阅用户类型，4贴吧官方账号
            'to_user_id' => $intToUid, //消息接收方uid
            'to_user_type' => 0, //消息接收方类型，同上
            'content' => self::NOTIFY_CONTENT, //消息内容
            'duration' => 0,  //语音消息时长，必填字段，不是语音消息填0
            'record_id' => -1, //必填，填-1
            'cuid' => '',     //必填，填空
            'version' => '',  //必填，可以为空
            'data' => json_encode(array('client_version'=> '','seq_id'=>0)),
            'device' => '', //设备信息，可以为空
        );
        $arrRet = Tieba_Service::call('im','addPlatForumPMsg',$arrParam,null,null,'post','php','utf-8');
        if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning("call addPlatForumPMsg failed.input:[".serialize($arrParam)."].output:[".serialize($arrRet)."].");
            return $arrRet;
        }
        return true;
    }
}