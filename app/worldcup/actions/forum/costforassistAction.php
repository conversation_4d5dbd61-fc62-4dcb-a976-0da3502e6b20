<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-05-06 14:46:55
 * @comment 获取用户助攻乱入吧需要的T豆数
 * @version
 */
class costforassistAction extends Util_Base {

    public function execute(){
        try {
            //参数获取
            $match_id = intval(Bingo_Http_Request::get('match_id',0));
            $forum_id = intval(Bingo_Http_Request::get('forum_id',0));
	    $user_id = $this->_arrUserInfo['user_id'];

	    if (!$user_id || !$forum_id || !$forum_id) {
		throw new Util_Exception("param error user[$user_id] match[$match_id]".
		    " forum[$forum_id]", Tieba_Errcode::ERR_PARAM_ERROR);
	    }

	    $reqCount = array(
		'match_id' => $match_id,
		'forum_id' => $forum_id,
		'user_id' => $user_id,
	    );
	    $resCount = Tieba_Service::call('worldcup','getCountOfAssitForum',
		$reqCount,NULL,NULL,'post','php','gbk','local');
	    if ($resCount['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
		throw new Util_Exception("get cost for assist forum failed. input[".
		    serialize($reqCount)."] output[".serialize($resCount).']',
			Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
	    }
	    $count = $resCount['count'];

	    $cost = Util_Helper::getCostForAssist($count+1);
	    $retData = array('count' => $cost);

            // 默认成功返回值
	    $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,
		Tieba_Error::getUserMsg(Tieba_Errcode::ERR_SUCCESS),$retData);
        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), '未知错误');	
        }
    }

    protected static function _getCostForAssistForum($count) {
	$count = intval($count);
	switch ($count) {
	case 0 : $cost = 100; break;
	case 1 : $cost = 300; break;
	default : $cost = 500; break; 
	}
	return $cost;
    }

}
