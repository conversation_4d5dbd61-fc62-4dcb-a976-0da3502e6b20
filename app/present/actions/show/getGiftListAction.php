<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file getListAction.php
 * <AUTHOR>
 * @date 2016/01/12
 * @brief 
 *  
 **/
//if (! defined('BINGO_ENCODE_LANG')) define('BINGO_ENCODE_LANG', 'UTF-8');
class getGiftListAction extends Util_Base
{
    /**
    * @brief excute
    * @param void
    * @return array
    **/
    public function _execute()
    {
        try
        {
            if ($this->_arrUserInfo['is_login'] !== true )
            {
                //未登陆
                throw new Exception("user need login!",Tieba_Errcode::ERR_USER_NOT_LOGIN);
            }

            $intYyPayOpen   = intval(Bingo_Http_Request::get('yy_pay_open', 0));
            $intYyIsConvert = intval(Bingo_Http_Request::get('yy_is_convert', 0));
            //判断Y币开关状，检验与输入是否一致
            $intUserId        = intval($this->_arrUserInfo['user_id']);
            $bolYySwitch      = Molib_Util_Yylive::isSwitchOpened($intUserId);
            $intRealYyPayOpen = $bolYySwitch ? 1 : 0;

            if ($intRealYyPayOpen != $intYyPayOpen) {
                return $this->_jsonRet(100000001, iconv("UTF-8", "GB2312",'服务器T豆升级Y币中，请刷新页面体验此功能哦～'), '');
            }
            $bolNotConverted    = Molib_Util_Yylive::isNotConverted($intUserId);
            $intRealYyIsConvert = $bolNotConverted ? 0 : 1;
            if ($intRealYyIsConvert != $intYyIsConvert) {
                return $this->_jsonRet(100000001, iconv("UTF-8", "GB2312",'服务器T豆升级Y币中，请刷新页面体验此功能哦～'), '');
            }
            $intCurrency = 0;
            if ($bolYySwitch && !$bolNotConverted) {
                $intCurrency = 1;
            }

            $strSceneFrom = Bingo_Http_Request::get('scene_from', '');

            if (empty($strSceneFrom))
            {
                throw new Exception("param is error", Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $arrConf = Bd_Conf::getAppConf("/app/present/scene");
            if($arrConf == false)
            {
                Bingo_Log::warning("init get conf fail.");
                throw new Exception("init get conf fail", Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $intSceneId = isset($arrConf['scene_map'][$strSceneFrom]) ? intval($arrConf['scene_map'][$strSceneFrom]) : 0 ; //根据from配置获得

            $arrParams = array(
                'scene_id'    => $intSceneId,
                'client_type' => 'pc',
            );

            $arrOutput = Tieba_Service::call( 'present', 'getCategoryListBySceneId', $arrParams, null, null, 'POST', 'php', 'utf-8' );
            if ( $arrOutput == false ) 
            {
                Bingo_Log::warning( "call Present getCategoryListBySceneId fail.[input]".serialize($arrParams));
                 throw new Exception("call getCategoryListBySceneId failed", $arrOutput['errno']);
            }
            if ( !isset( $arrOutput['errno'] ) || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS )
            {
                Bingo_Log::warning( "call Present getCategoryListBySceneId error. [output: ]" . serialize( $arrOutput ) );
                 throw new Exception("call getGiftListByCategoryId failed", $arrOutput['errno']);
            }
 
            $arrData = (!empty($arrOutput['data'])) ? Bingo_Encode::convert($arrOutput['data'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8) : $arrOutput['data'];
			$arrData = Https_Replace::ssl_url_replace_recursive($arrData);
			$arrData['gift_list'] = $this->_fmtGiftList($arrData['gift_list'],$intCurrency);
            return $this->_jsonRet($arrOutput['errno'], $arrOutput['errmsg'], $arrData);
	   }
       catch (Exception $e)
       {
            $errno = $e->getCode();
            $error = $e->getMessage();
            Bingo_Log::warning("errno=$errno, msg=$error");
            return $this->_jsonRet($errno,  Tieba_Error::getUserMsg($errno), '');
        }
    }

    /**
     * @brief 礼物列表格式化
     *
     * @param
     *            array
     * @return array
     */
    private function _fmtGiftList($arrInput, $intCurrency = 0) {
        $arrRet = array();
        if (empty($arrInput) || !is_array($arrInput)) {
            return $arrRet;
        }

        foreach ($arrInput as $k => $v) {
            //T豆切Y币兼容
            if (1 == $intCurrency) {
                $arrInput[$k]['price']     = intval($v['yybi_price']);
                $arrInput[$k]['ios_price'] = intval($v['ios_yybi_price']);
            }
            unset($arrInput[$k]['yybi_price']);
            unset($arrInput[$k]['ios_yybi_price']);
        }
        return $arrInput;
    }
}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
