<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file getUseFreeChance.php
 * <AUTHOR>
 * @date 2016/01/15
 * @brief 
 *  
 **/
class getUserFreeChanceAction extends Util_Base
{
    /**
    * @brief excute
    * @param void
    * @return array
    **/
    public function _execute()
    {
        try
        {
            if ($this->_arrUserInfo['is_login'] !== true )
            {
                //未登陆
                throw new Exception("user need login!",Tieba_Errcode::ERR_USER_NOT_LOGIN);
            }

            $strSceneFrom = Bingo_Http_Request::get('scene_from', '');

            if (empty($strSceneFrom))
            {
                throw new Exception("param is error", Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $arrConf = Bd_Conf::getAppConf("/app/present/scene");
            if($arrConf == false)
            {
                Bingo_Log::warning("init get conf fail.");
                throw new Exception("init get conf fail", Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $intSceneId = isset($arrConf['scene_map'][$strSceneFrom]) ? intval($arrConf['scene_map'][$strSceneFrom]) : 0 ; //根据from配置获得

            $arrParams = array(
                'user_id' => intval($this->_arrUserInfo['user_id']),
                'scene_id'=> $intSceneId,
            );

            $arrOutput = Tieba_Service::call( 'present', 'getUserFreeChance', $arrParams, null, null, 'POST', 'php', 'utf-8' );
            if ( $arrOutput == false ) 
            {
                Bingo_Log::warning( "call Present getUserFreeChance fail.[input]".serialize($arrParams) );
                throw new Exception("call getUserFreeChance failed", $arrOutput['errno']);
            }
            if ( !isset( $arrOutput['errno'] ) || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS )
            {
                Bingo_Log::warning( "call Present getUserFreeChance error. [output: ]" . serialize( $arrOutput ) );
                throw new Exception("call getUserFreeChance failed", $arrOutput['errno']);
            }
            $arrOutput['data'] = Https_Replace::ssl_url_replace_recursive($arrOutput['data']); 
            return $this->_jsonRet($arrOutput['errno'], $arrOutput['errmsg'],  $arrOutput['data']);
	   }
       catch (Exception $e)
       {
            $errno = $e->getCode();
            $error = $e->getMessage();
            Bingo_Log::warning("errno=$errno, msg=$error");
            return $this->_jsonRet($errno,  Tieba_Error::getUserMsg($errno), '');
        }
    }
}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
