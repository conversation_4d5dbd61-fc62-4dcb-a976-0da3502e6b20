<?php
/**
 * 礼物外显配置
 * Created by PhpStorm.
 * User: zhanghanqing
 * Date: 2019/3/20
 * Time: 9:50 PM
 */

class Dl_GiftOutConf extends Dl_Libs_Base {

    static protected $DB_NAME = 'forum_ala';

    const TABLE_NAME = 'gift_out_conf';

    const GIFT_OUT_CONF_STATUS_NORMAL = 0;
    const GIFT_OUT_CONF_STATUS_DELETED = 1;

    /**
     * @brief
     * @param array
     * @return array
     **/
    public static function addGiftOutConf($arrInput){
        $arrInsert = array(
            'gift_id' => intval($arrInput['gift_id']),
            'client_type' => intval($arrInput['client_type']),
            'subapp_type' => strval($arrInput['subapp_type']),
            'add_time' => intval($arrInput['add_time']),
            'operator' => strval($arrInput['operator']),
            'update_time' => strval($arrInput['add_time']),
        );
        $db  = self::_getDB(self::$DB_NAME);
        $ret = $db->insert(self::TABLE_NAME, $arrInsert);
        if($ret === false){
            Bingo_Log::warning("db query error! [output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return false;
        }
        $arrOutput = array(
            'sql_res' => $ret,
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @param array
     * @return array
     **/
    public static function editGiftOutConf($arrInput) {
        $intId    = intval($arrInput['id']);

        $arrParam = array(
            'conds' => array(
                'id=' => intval($intId),
            ),
            'fields' => array(
                'gift_id' => intval($arrInput['gift_id']),
                'client_type' => intval($arrInput['client_type']),
                'subapp_type' => strval($arrInput['subapp_type']),
                'update_time' => intval($arrInput['update_time']),
            ),
        );
        $db  = self::_getDB(self::$DB_NAME);
        $ret = $db->update(self::TABLE_NAME, $arrParam['fields'], $arrParam['conds']);

        if($ret === false){
            Bingo_Log::warning("db query error! [output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return false;
        }
        $arrOutput = array(
            'sql_res' => $ret,
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @param array
     * @return array
     **/
    public static function getGiftOutConfList($arrInput){

        $arrInputParam = array(
            'fields' => array(
                'id',
                'gift_id',
                'client_type',
                'status',
                'subapp_type',
                'add_time',
                'update_time',
                'operator',
            ),
            'conds' => $arrInput['cond'],
            'append'  => 'order by id desc limit '. intval($arrInput['offset']) . ','.intval($arrInput['limit']),
        );
        if (empty($arrInputParam['conds'])){
            $arrInputParam['conds'] = null;
        }
        $db  = self::_getDB(self::$DB_NAME);
        $ret = $db->select(self::TABLE_NAME, $arrInputParam['fields'], $arrInputParam['conds'], null,$arrInputParam['append']);

        if($ret === false){
            Bingo_Log::warning("db query error! [output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return false;
        }

        $arrOutput['data']['list'] = $ret;

        return $arrOutput;
    }

    /**
     * @brief
     * @param array
     * @return array
     **/
    public static function getGiftOutConf($arrInput){

        $arrInputParam = array(
            'fields' => array(
                'id',
                'gift_id',
                'client_type',
                'status',
                'subapp_type',
                'add_time',
                'update_time',
                'operator',
            ),
            'conds' => 'client_type = '. intval($arrInput['client_type']) . ' and  subapp_type = "' . $arrInput['subapp_type'] .'" and status = ' . self::GIFT_OUT_CONF_STATUS_NORMAL,
            'append'  => 'order by id desc limit 1',
        );

        $db  = self::_getDB(self::$DB_NAME);
        $ret = $db->select(self::TABLE_NAME, $arrInputParam['fields'], $arrInputParam['conds'], null,$arrInputParam['append']);

        if($ret === false){
            Bingo_Log::warning("db query error! [output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return false;
        }

        $arrOutput['data'] = $ret;
        return $arrOutput;
    }

    /**
     * @brief
     * @param array
     * @return array
     **/
    public static function getGiftOutConfCountByCond($arrInput){

        $arrInputParam = array(
            'fields' => array(
                'count(1) as count',
            ),
            'conds' => $arrInput['cond'],
        );
        if (empty($arrInputParam['conds'])){
            $arrInputParam['conds'] = null;
        }

        $db  = self::_getDB(self::$DB_NAME);
        $ret = $db->select(self::TABLE_NAME, $arrInputParam['fields'], $arrInputParam['conds'], null,$arrInputParam['append']);

        if($ret === false){
            Bingo_Log::warning("db query error! [output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return false;
        }

        $arrOutput['data']['count'] = $ret[0]['count'];

        return $arrOutput;
    }

    /**
     * @brief
     * @param array
     * @return array
     **/
    public static function delGiftOutConf($arrInput){
        $intId    = intval($arrInput['id']);
        $arrParam = array(
            'conds' => array(
                'id=' => intval($intId),
            ),
            'fields' => array(
                'update_time' => intval($arrInput['update_time']),
                'status' => self::GIFT_OUT_CONF_STATUS_DELETED,
            ),
        );
        //db
        $db      = self::_getDB(self::$DB_NAME);
        $arrRet = $db->update(self::TABLE_NAME, $arrParam['fields'], $arrParam['conds']);
        if(false === $arrRet){
            Bingo_Log::warning('update failed .intput:['.serialize($arrInput).'].output:['.serialize($arrRet).'].sql:['.$db->getLastSQL().']');
            return    self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrOutput    = array(
            'sql_res'    => $arrRet,
            );
        return    $arrOutput;
    }
}

