<?php
/**
 * @package present
 * <AUTHOR> @date 2015-09-11
 */

class Service_Present_UserGiftCount extends Service_Libs_Base
{	
    const USER_GIFT_COUNT_ADD = 'add';
    const USER_GIFT_COUNT_DEC = 'dec';
	static protected $DB_NAME = 'forum_present';

    /**
    * @brief 添加用户礼物统计
    * @param array
    * @return array
    **/
    public static function addUserGiftCount($arrInput)
    {
        $arrCheckParam = array('user_id','blue_diamond','tdou','idou', 'num');
        if(Service_Libs_Util::checkAllSet($arrInput, $arrCheckParam,true) === false){
           Bingo_Log::warning('param error:'.serialize($arrInput)); 
           return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, ' input error! ['.serialize($arrInput).']', __FUNCTION__);
        }
        
        $db  = Dl_Libs_Base::_getDB(self::$DB_NAME);
        $db->startTransaction();
        $arrInput['type'] = self::USER_GIFT_COUNT_ADD;
        $arrInput['create_time'] = time();
        $arrInput['update_time'] = time();
        $ret = self::opUserGiftCount($arrInput);
        $intErrNo = (isset($ret['errno'])) ? $ret['errno'] : -1;
        if(empty($ret) || Tieba_Errcode::ERR_SUCCESS != $intErrNo){
            $db->rollback();
            Bingo_Log::warning('addUserGiftCount opUserGiftCount fail, input:[' . serialize($arrInput) . ']'); 
            return self::_errLogRet(Tieba_Errcode::ERR_DL_CALL_FAIL, '', ' opUserGiftCount fail, input:[' . serialize($arrInput) . ']');
        }
        $ret = $db->commit();
        if (false === $ret) {
            $strError = "db commit error! [output:".serialize($ret)."]error:".$db->error()." sql:".$db->getLastSQL()."]";
            $db->rollback();
            Bingo_Log::warning('addUserGiftCount opUserGiftCount db commit fail, input:[' . $strError . ']'); 
            return self::_errLogRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, 'db commit error', $strError);
        }
        //更新user属性 cgift
        $arrParam = array(
            'benefit_userid' => $arrInput['user_id'],
            //'cur_num'        => intval($arrOrder['num']),  //本次购买礼物数
        );
        Bingo_Timer::start('addUserGiftCount-setUserCgift');
        $arrRet = Service_Libs_Util::commitAction('Service_Present_Post', 'setUserCgift', $arrParam);
        Bingo_Timer::end('addUserGiftCount-setUserCgift');
        return self::_successRet(Tieba_Errcode::ERR_SUCCESS, array('data' => $ret['data']));
    }
    
    /**
    * @brief 获取用户礼物统计信息
    * @param array
    * @return array
    **/
    public static function getUserGiftCount($arrInput)
    {
        $arrCheckParam = array('user_id');
        if(Service_Libs_Util::checkAllEmpty($arrInput, $arrCheckParam,true) === false)
        {
           Bingo_Log::warning('param error:'.serialize($arrInput)); 
           return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, ' input error! ['.serialize($arrInput).']', __FUNCTION__);
        }
        //读取用户在该场景中的免费信息
        $arrParamInput = array(
            'conds' => array(
                'user_id=' =>intval($arrInput['user_id']),
            ),
            //'append' => 'for update',
        );
        $arrUserGiftCount = Dl_UserGiftCount::getUserGiftCount($arrParamInput);
        if(false === $arrUserGiftCount)
        {
           Bingo_Log::warning('Call dl getUserGiftCount fail,input:['.serialize($arrParamInput).']');
           return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'Call dl failed, input:[' . serialize($arrParamInput) .']', __FUNCTION__);
        }
        $retData = !empty($arrUserGiftCount['data'][0]) ? $arrUserGiftCount['data'][0] : array();

        return self::_successRet(Tieba_Errcode::ERR_SUCCESS, array('data' => $retData));
    }
    
    /**
    * @brief 统计信息修改
    * @param array
    * @return aray
    **/
    public static function opUserGiftCount($arrInput){
        $arrCheckParam = array('user_id','tdou','blue_diamond','idou','num');
        if(Service_Libs_Util::checkAllSet($arrInput, $arrCheckParam) === false){
           Bingo_Log::warning('param error:'.serialize($arrInput)); 
           return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, ' input error! ['.serialize($arrInput).']', __FUNCTION__);
        }
        
        $arrParamInput = array(
            'conds' => array(
                'user_id='=>intval($arrInput['user_id']),
            ),
            //'append' => 'for update',
        );
        $arrCount = Dl_UserGiftCount::getUserGiftCount($arrParamInput);
        if(false === $arrCount){
           Bingo_Log::warning('Call dl getUserGiftCount fail,input:['.serialize($arrParamInput).']');
           return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'Call dl failed, input:[' . serialize($arrParamInput) .']', __FUNCTION__);
        }
        //增加礼物统计信息
        if(self::USER_GIFT_COUNT_ADD == $arrInput['type']){
	        if (empty($arrCount['data'][0])) {
	            $ret = Dl_UserGiftCount::addUserGiftCount($arrInput);
	            if (false === $ret) {
                    Bingo_Log::warning('opUserGiftCount addUserGiftCount fail,input:['.serialize($arrInput).']');
	                return self::_errLogRet(Tieba_Errcode::ERR_DL_CALL_FAIL, '', 'addUserGiftCount failed, input:[' . serialize($arrParamInput) . ']');
	            }
	        }
	        else {      
	        	$strSqlTpl = 'UPDATE user_gift_count SET tdou=tdou+{tdou:n}, idou=idou+{idou:n}, num=num+{num:n}, blue_diamond=blue_diamond+{blue_diamond:n} , update_time={update_time:n} WHERE user_id={user_id:n}';
	        	$ret = Dl_UserGiftCount::queryUserGiftCount($arrInput,$strSqlTpl);
	            if (false === $ret) {
                    Bingo_Log::warning('opUserGiftCount queryUserGiftCount fail,input:['.serialize($arrInput).']');
	                return self::_errLogRet(Tieba_Errcode::ERR_DL_CALL_FAIL, '', 'queryUserGiftCount failed, input:[' . serialize($arrParamInput) . ']');
	            }
	        }
        }
        //todo 减少数量
        else {	  
        }
        return self::_successRet(Tieba_Errcode::ERR_SUCCESS, array('data' => $ret['data'])); 
    }
    
    //批量获取一批用户的礼物数
    public static function mgetUserGiftCount($arrInput){
        $arrCheckParam = array('uids');
        if(Service_Libs_Util::checkAllSet($arrInput, $arrCheckParam) === false){
            Bingo_Log::warning('param error:'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, ' input error! ['.serialize($arrInput).']', __FUNCTION__);
        }
        if(empty($arrInput['uids'])){
            Bingo_Log::warning('param error:'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, ' input error! ['.serialize($arrInput).']', __FUNCTION__);
        }
        $strUids = implode(',',$arrInput['uids']);
        $arrInputParam = array(
            'conds' => 'user_id in ('.$strUids.')',
        );
        $arrRet = Dl_UserGiftCount::mgetUserGiftCount($arrInputParam);
        if(false === $arrRet){
            Bingo_Log::warning('Call dl mgetUserGiftCount fail,input:['.serialize($arrInputParam).']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'Call dl failed, input:[' . serialize($arrInput) .']', __FUNCTION__);
        }
        //改变数组结果，key变为uid
        $arrData = array();
        foreach ($arrRet['sql_res'] as $value){
            $arrData[$value['user_id']] = $value;
        }
        return self::_successRet(Tieba_Errcode::ERR_SUCCESS,array('data' => $arrData));
    }
}
?>
