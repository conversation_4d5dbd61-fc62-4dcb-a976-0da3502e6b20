<?php
/**
 * Created by PhpStorm.
 * User: zhuanxu
 * Date: 2/2/15
 * Time: 3:44 PM
 */

require_once "../../vendor/autoload.php";
require_once "../../setUp.php";
//require_once "../../lib/Db/PdoConfig.php";

use TiebaTest\Db\PdoConfig;

// mysqldump  --xml -t --default-character-set=utf8 -h127.0.0.1 -uroot -proot -P3346 forum_tbmall user_scores > user_scores.xml

class tempTest extends PHPUnit_Extensions_Database_TestCase{
    private $pdo = null;
    private $database = "forum_tbmall";


    /**
     * Returns the test database connection.
     *
     * @return PHPUnit_Extensions_Database_DB_IDatabaseConnection
     */
    protected function getConnection()
    {
        if ($this->pdo === null) {
            $pdo = new PDOConfig($this->database);
            $this->pdo = $this->createDefaultDBConnection($pdo);
        }
        return $this->pdo;
    }

    /**
     * Returns the test dataset.
     *
     * @return PHPUnit_Extensions_Database_DataSet_IDataSet
     */
    protected function getDataSet()
    {
        $data1 = $this->createMySQLXMLDataSet(dirname(__FILE__) . "/data/pool_award.xml");
        $data2 = $this->createMySQLXMLDataSet(dirname(__FILE__) . "/data/user_scores.xml");

        $compositeDs = new PHPUnit_Extensions_Database_DataSet_CompositeDataSet();

        $compositeDs->addDataSet($data1);
        $compositeDs->addDataSet($data2);
        return $compositeDs;
    }
    private function getLotteryForCount($count, $user_id, $type) {
        $input = array(
            'user_id' => $user_id,
            'type' => $type,
        );
        for($i=0; $i<$count; $i++) {
            $output = Service_Treasurebox_Treasurebox::getLottery($input);
            $this->assertEquals(0, $output['errno']);
        }
    }

    function userIdAndDifferentType() {
        return array(
            array(1496, Service_Libs_Define::TREASUREBOX_TYPE_5, 0),
            array(1496, Service_Libs_Define::TREASUREBOX_TYPE_50, 0),
        );
    }
    function getDifferentLotteryWithMoney() {
        return array(
            array(1496, Service_Libs_Define::TREASUREBOX_TYPE_5, Service_Libs_Define::COST_LOTTERY_5),
            array(1496, Service_Libs_Define::TREASUREBOX_TYPE_50, Service_Libs_Define::COST_LOTTERY),
        );
    }

    function getDifferentLottery() {
        return array(
            array(1496, Service_Libs_Define::TREASUREBOX_TYPE_5, Service_Treasurebox_Treasurebox::TYPE_HIT_USER_POOL),
            array(1496, Service_Libs_Define::TREASUREBOX_TYPE_50, Service_Treasurebox_Treasurebox::TYPE_HIT_USER_POOL),
        );
    }
    function getTotalLottery() {
        $uids = array(
            1496, 5
        );
        return array(
            array($uids, Service_Libs_Define::TREASUREBOX_TYPE_5, Service_Treasurebox_Treasurebox::TYPE_HIT_TOTAL_POOL),
            array($uids, Service_Libs_Define::TREASUREBOX_TYPE_50, Service_Treasurebox_Treasurebox::TYPE_HIT_TOTAL_POOL),
        );
    }





    function testUserIdNotRightSoReturnParamError() {
        $this->markTestSkipped();
        $output = Service_Treasurebox_Treasurebox::_getLottery(array());
        $this->assertTrue($output['errno'] === Tieba_Errcode::ERR_PARAM_ERROR);
    }

    function testGetLottery() {
        $this->markTestSkipped();
        $ret = \TiebaTest\Db\DbFunction::truncate("forum_tbmall", "tdou_pool");
        $this->assertTrue($ret);
        $uid = 1496;
        $type = Service_Libs_Define::TREASUREBOX_TYPE_5;

        self::getLotteryForCount(2, $uid, $type);

        $input = array(
            'user_id' => $uid,
            'type' => $type,
        );

        $output = Service_Treasurebox_Treasurebox::_getLottery($input);
        $this->assertTrue($output['errno'] === 0);
    }

    /**
     * @dataProvider getDifferentLottery
     */
    function testUserHasZeroScoresAndGetLotteryHitUserPool($user_id, $type, $expected) { // 用户
        $ret1 = \TiebaTest\Db\DbFunction::truncate("forum_tbmall", "pool_award");
        $this->assertTrue($ret1);

        $this->getLotteryForCount(3, $user_id, $type); // just before get the card

        $input = array(
            'user_id' => $user_id,
            'type' => $type,
        );
        //$output = Service_Treasurebox_Treasurebox::getLottery($input);
        //var_dump($output);
        //$this->assertEquals($expected, $output['data']['type']);
    }

}