<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013:10:29 23:35:24
 * @version 
 * @structs & methods(copied from idl.)
*/

class Service_Nameplate_Nameplate extends Service_Libs_Base{

    const MAX_NAMEPLATE_LEN = 28;
    const NAMEPLATE_PAGE_SIZE = 48;
    const MIN_NAMEPLATE_ID = 1120000001;
    const NAMEPLATE_ALL_STATUS = 0;
    const NAMEPLATE_UNSOLD_STATUS = 3;
    const NAMEPLATE_SOLD_STATUS = 4;
    const PREFIX_MAX_NAMEPLATE_ID = 'max_nameplate_id_';
    const PREFIX_RAND_NAMEPLATE = 'rand_nameplate_';
    const TABLE_NAMEPALTE = 'nameplate'; 

    
    
    /**
     * @brief 狗牌购买接口
     * @arrInput:
     * 	uint32_t user_id
     *  uint32_t forum_id
     *  uint32_t end_time
     * @return: $arrOutput
     * 	BuyPropsOut output
     **/
    public static function buyNamePlatePropsNew($arrInput){
    
        /*check the tb sig, if no need, just remove the codes below.
         if(!Tieba_Service::checkTBSig($arrInput)){
        Bingo_Log::warning("check tb sig fail. invalid request.");
        return self::_errRet(Tieba_Errcode::ERR_TBSIG_CHECK_FAIL);
        }*/
        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
    
        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['user_id']) || !isset($arrInput['props_id']) ){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
    
        //input params.
        $user_id = intval($arrInput['user_id']);
        $props_id = intval($arrInput['props_id']);
    
        Bingo_Log::pushNotice("user_id",$user_id);
        Bingo_Log::pushNotice("props_id",$props_id);
    
        if($user_id <= 0 || $props_id <= 0){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
    
        //output params.
        $output = false;
        if( null === ($db = Service_Libs_Db::getDB()) ){
            Bingo_Log::warning("get db fail.");
            $error = Tieba_Errcode::ERR_DB_CONN_FAIL;
            return self::_errRet($error);
        }
        $has_db_error = false;
        $now_time = time();
        $tmp = 0;
        //your code here......
        $error = Tieba_Errcode::ERR_SUCCESS;
        do {
            //开始事务
            $db->startTransaction();
            
            $props_info = self::getNameplateProps($props_id,'for update');
            if ($props_info['props_id'] != $props_id) {
                Bingo_Log::warning("get props error! props_id: [".$props_id."] ret:[" . print_r($props_info, true) .']');
                $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                Bingo_Log::pushNotice("getOneProps_err",1);
                $has_db_error=true;
                break;
            }
            Bingo_Log::pushNotice("props_id",$props_id);
            Bingo_Log::pushNotice("props_scores",0);
            Bingo_Log::pushNotice("props_category",$props_info['props_category']);
            Bingo_Log::pushNotice("buy_num",1);
            if ($props_info['props_category'] != Service_Libs_Define::PROPS_CATEGORY_NAMEPLATE){//购买非狗牌道具
                Bingo_Log::warning('only suport buy PROPS_CATEGORY_NAMEPLATE');
                Bingo_Log::pushNotice("not_nameplate",1);
                $error = Tieba_Errcode::ERR_PARAM_ERROR;
                $has_db_error=true;
                break;
            }
            //判断狗牌的是否被占用
            if ($props_info['end_time'] > $now_time){
                Bingo_Log::warning('nameplate_inused!!');
                Bingo_Log::pushNotice("nameplate_inused",1);
                $error = Tieba_Errcode::ERR_REPEAT_PURCHASE;
                $has_db_error=true;
                break;
            }
            
            //调用service实现user_scores user_props操作，解决DDBS不支持分布式事务的问题。
                //service 需要返回勋章过期时间，
            $arrServiceInput = array(
                    'user_id' => $user_id,
                    'props_id' => $props_id,
                    'buy_type' => 1,
                    'title' => $props_info['title'],
                    'props_scores' => 0,
                    );
            
            Bingo_Timer::start('AddUserNamePlate');
            $arrRet = Tieba_Service::call("tbmall", "addUserNameplate", $arrServiceInput,NULL, NULL, 'post', 'php', 'utf-8');
            Bingo_Timer::end('AddUserNamePlate');
            if (!isset($arrRet['errno']) || intval($arrRet['errno']) !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('AddUserNamePlate error! output:' . serialize($arrServiceInput));
                Bingo_Log::pushNotice("AddUserNamePlate_err",1);
                $error = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
                $has_db_error=true;
                break;
            }

            Bingo_Log::pushNotice("total_cost",0);
            Bingo_Log::pushNotice("money_cost",0);
            Bingo_Log::pushNotice("other_cost",0);
            Bingo_Log::pushNotice("fetch_cost",0);
            
            $medal_endtime = $arrRet['data']['medal_endtime'];
            //更新props表，end_time 字段 
            $arr_cond = array();
            $arr_cond[] =  'props_id='.$props_id;
            $arr_fields = array();
            $arr_fields['update_time'] = $now_time;
            $arr_fields['user_id'] = $user_id;
            $arr_fields['end_time'] = $medal_endtime;//记录过期时间 目前是勋章时间
            $db_props = $db->update(self::TABLE_NAMEPALTE, $arr_fields, $arr_cond);
            Bingo_Log::warning("socre sql:".$db->getLastSQL());
            if($db_props <= 0 || $db->getAffectedRows() !== 1){
                Bingo_Log::warning("nameplate db update error! sql:". $db->getLastSQL()."db error:".serialize($db->error())." [output:".serialize($db_props)."]");
                Bingo_Log::pushNotice("nameplate_update_err",1);
                $error = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
                $has_db_error=true;
                break;
            }
            
    
        }while(0);
        //提交事务
        if ($has_db_error) {
            $tmp = $db->rollback();
            Bingo_Log::warning("db->rollback. [".serialize($tmp)."]");
            //$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
            Bingo_Log::pushNotice("rollback",1);
        }
        else {
            if (true === $db->commit()){
                
                 
                //更新SE
                $se_ret = Service_Libs_Se::updateData(array($props_id), $medal_endtime);
                Bingo_Log::pushNotice("se_ret",$se_ret);
                //道具对账
                $props_check = Service_Mall_Mall::_addPropsToUcenter($user_id,1);
                Bingo_Log::pushNotice("props_check",$props_check);
    
            } else{
                Bingo_Log::warning("db->commit. [".serialize($tmp)."]");
                $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
            }
        }
        //增加end_time 返回前端
        $props_info['end_time'] =  $medal_endtime;
        Bingo_Log::pushNotice("errno",$error);
        Bingo_Log::pushNotice("errmsg",Tieba_Error::getErrmsg($error));
        return self::_errRet($error,$props_info);
    }
    
    
    
    /**
     * @brief 狗牌购买中间处理接口
     * @arrInput:
     * 	uint32_t user_id
     *  uint32_t forum_id
     *  uint32_t end_time
     * @return: $arrOutput
     * 	BuyPropsOut output
     **/
    public static function addUserNameplate($arrInput){

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
    
        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['user_id']) || !isset($arrInput['buy_type']) ||!isset($arrInput['props_scores'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
    
        //input params.
        $user_id = intval($arrInput['user_id']);
        $buy_type = intval($arrInput['buy_type']);
        $props_scores = intval($arrInput['props_scores']);
        $title = $arrInput['title'];
        if ($user_id <= 0 || $props_scores < 0){
            Bingo_Log::warning("input params invalid props_scores<0 or user_id<=0. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        Bingo_Log::pushNotice("user_id",$user_id);
        Bingo_Log::pushNotice("props_scores",$props_scores);
        Bingo_Log::pushNotice("buy_type",$buy_type);
        
        

        //output params.
        $output = false;
        if( null === ($db = Service_Libs_Db::getDB()) ){
            Bingo_Log::warning("get db fail.");
            $error = Tieba_Errcode::ERR_DB_CONN_FAIL;
            return self::_errRet($error);
        }
        $has_db_error = false;
        $now_time = time();
        $tmp = 0;
        //your code here......

        //从user_progs表中查询用户勋章状态
        $user_level_info   		= Service_Libs_Db::getUserLevelInfo($user_id);
        //Bingo_Log::warning(print_r($user_level_info,1));
        //用户勋章级别，过期为0
        if (!isset($user_level_info['level'])){
            $user_level = 0;
        }else{
            $user_level = $user_level_info['end_time']>$now_time?intval($user_level_info['level']):0;
        }
        

        if ($user_level !== 2){
            Bingo_Log::warning("user_level:user_level not 2 !!! user_id:$user_id");
            Bingo_Log::pushNotice("level_not_2",1);
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        $free_nums = 0;
        if ($user_level > 0){
            $arrPropIds = Service_Mall_Mallconf::getUserLevelToPropsList($user_level);
            //Bingo_Log::warning(print_r($arrPropIds,1));
            $free_nums = isset($arrPropIds['nameplate']) ? $arrPropIds['nameplate'] :0;
        }
        
        
        $medal_endtime = $user_level_info['end_time'];
        Bingo_Log::pushNotice("medal_endtime",$medal_endtime);
        $error = Tieba_Errcode::ERR_SUCCESS;
        do {
            //开始事务
            $db->startTransaction();

            /*首先锁用户积分记录，保证不出现死锁
            $arrReduceRet = Service_Mall_Mall::reduceUserScores($user_id, $props_scores, 1);
            if (Tieba_Errcode::ERR_SUCCESS !== $arrReduceRet['errno']){
                $error = $arrReduceRet['errno'];
                Bingo_Log::warning("reduceUserScores error user_id:$user_id");
                $has_db_error = true;
                break;
            }*/
            
            if (isset($arrInput['props_id'])){
                $reduce_props_id = intval($arrInput['props_id']);
            }else if (isset($arrInput['new_props_id'])){
                $reduce_props_id = intval($arrInput['new_props_id']);
            }
            
            $arrParams = array(
                    'user_id' => $user_id,
                    'props_scores' => $props_scores,   //扣减用户T豆数
                    'props_id' => $reduce_props_id,     //业务ID
                    'props_name' => $title,    //业务名称
                    'order_type' => Service_Libs_Define::BUY_ORDER_TYPE_MALL ,        //业务类型
                    'num' => 1,                //购买数量 默认1
                    'to_ucenter' => 0,         //是否对账ucenter，外部如调用对账接口，此处填0
                    'no_trans' => 1,           //是否开启事务，外部如已经开启事务，此处填1
                    'ueg_feature' => $arrInput['ueg_feature'],
                    'order_start_time' => $now_time,
                    'order_end_time' => $medal_endtime,
            );
            $arrReduceRet = Service_Mall_Mall::reduceUserScoresOrder($arrParams);
            if ( $arrReduceRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                $error = $arrReduceRet['errno'];
                Bingo_Log::warning("reduceUserScoresOrder error user_id:$user_id");
                $has_db_error = true;
                break;
            }
            
            
            
            
            
            
            if ($buy_type === 1 ) {//购买逻辑
                if(!isset($arrInput['props_id'])  ){
                    Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                    Bingo_Log::pushNotice("props_id_error",1);
                    $error = Tieba_Errcode::ERR_PARAM_ERROR;
                    $has_db_error = true;
                }
                $props_id = intval($arrInput['props_id']);
                Bingo_Log::pushNotice("props_id",$props_id);
                
                //获取数据库中勋章赠送的狗牌已经使用了多少个
                $used_nameplate_num = self::_getNamePlateNumFromDB($user_id,1);
                if ($used_nameplate_num === false){
                    Bingo_Log::warning("nameplate used numbers get error!");
                    $has_db_error = true;
                    Bingo_Log::pushNotice("get_nameplates_err",1);
                    $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }
                
                //剩余免费购买个数 = 勋章赠送的 - 已经使用的
                $free_nums = $free_nums - $used_nameplate_num;
                Bingo_Log::pushNotice("free_nums",$free_nums);
                Bingo_Log::warning("free_nums:$free_nums used_nameplate_num:$used_nameplate_num");
                if($free_nums <= 0){//本期不支持收费购买
                    Bingo_Log::warning("nameplate numbers not enough!");
                    $has_db_error = true;
                    Bingo_Log::pushNotice("nameplates_not_enough",1);
                    $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }
                
                //从user_progs表中查是道具是否存在 for update
                $db_user_props = Service_Libs_Db::getUserPropsById($user_id, $props_id, 2,'FOR UPDATE');
                if ($db_user_props === false){
                    Bingo_Log::warning("getUserPropsById error! SQL:". $db->getLastSQL()." props_id: [".$props_id."] ret:[" . serialize($db_user_props) .']');
                    Bingo_Log::pushNotice("getUserPropsById_error",1);
                    $has_db_error = true;
                    $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }
                //update user_progs 道具记录更新。 end_time
                if (!empty($db_user_props)){ //存在记录
                    Bingo_Log::pushNotice("props_update",1);
                
                    $arr_fields = array();
                    $arr_fields['update_time'] = $now_time;
                    //end_time 是4级勋章的end_time
                    //如果开发购买 $arr_fields['end_time'] = $db_user_props['end_time'] > $now ? $db_user_props['end_time'] + $props_info['time_interval'] :$props_info['time_interval'];
                    $arr_fields['end_time'] = $medal_endtime;
                    $arr_fields['ext'] = $title;
                    $str_cond = 'user_id='.$user_id.' and props_id='.$props_id.' and used_status='.Service_Libs_Define::PROPS_USED;
                    $ret  = $db->update('user_props', $arr_fields, $str_cond);
                    Bingo_Log::warning("sql:".$db->getLastSQL());
                    if($ret <= 0 || $db->getAffectedRows() !== 1){
                        Bingo_Log::warning("db update error! sql:". $db->getLastSQL()." [output:".serialize($ret)."]");
                        $has_db_error = true;
                        Bingo_Log::pushNotice("update_user_props_err",1);
                        $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                        break;
                    }
                }else {
                    //表记录不存在，直接插入数据库。
                    Bingo_Log::pushNotice("props_insert",1);
                    $field = array();
                    $field['user_id'] 		= $user_id;
                    $field['create_time']		= $now_time;
                    $field['update_time'] 		= $now_time;
                    $field['end_time']		= $medal_endtime; //目前暂时是这样
                    $field['last_level_endtime']   =  0;
                    $field['props_count']   	= 0;
                    $field['left_num']		= 99;  //复用下，left_num = 99 表示是勋章送的狗牌
                    $field['props_category'] 	= Service_Libs_Define::PROPS_CATEGORY_NAMEPLATE;
                    $field['props_type']		= Service_Libs_Define::PROPS_TYPE_TIME;
                    $field['props_id']		= $props_id;
                    $field['ext']			= $title;
                    $field['used_status']		= Service_Libs_Define::PROPS_USED; //本期默认使用
                    $field['open_status']       = Service_Libs_Define::PROPS_OPEN;
                    $retTmp  = $db->insert('user_props', $field, NULL, NULL);
                    Bingo_Log::warning("sql:".$db->getLastSQL());
                    if($retTmp <= 0){
                        Bingo_Log::warning("_insertUserProps db insert or update error! sql:". $db->getLastSQL()." [output:".serialize($retTmp)."]");
                        $has_db_error = true;
                        $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                        Bingo_Log::pushNotice("props_insert_err",1);
                        break;
                    }
                
                }
                
                
            }else if ($buy_type === 2 ){ //替换逻辑
                if(!isset($arrInput['new_props_id']) || !isset($arrInput['old_props_id']) ){
                    Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
                    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
                }
                $old_props_id = intval($arrInput['old_props_id']);
                $new_props_id = intval($arrInput['new_props_id']);
                $props_id = $new_props_id;
                Bingo_Log::pushNotice("props_id",$props_id);
                Bingo_Log::pushNotice("old_props_id",$old_props_id);
                Bingo_Log::pushNotice("new_props_id",$new_props_id);
                
                
                //更新old_props_id 的end_time 为0;
                //从user_progs表中查是道具是否存在 for update
                $db_user_props = Service_Libs_Db::getUserPropsById($user_id, $old_props_id, 2,'FOR UPDATE');
                if ($db_user_props === false || empty($db_user_props)){
                    Bingo_Log::warning("getUserPropsById error or emptye! SQL:". $db->getLastSQL()." props_id: [".$old_props_id."] ret:[" . serialize($db_user_props) .']');
                    Bingo_Log::pushNotice("getUserPropsById_error",1);
                    $has_db_error = true;
                    $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }

                //更新老道具end_time = 0
                $arr_fields = array();
                $arr_fields['update_time'] = $now_time;
                $arr_fields['end_time'] = 0;
                $str_cond = 'user_id='.$user_id.' and used_status='.Service_Libs_Define::PROPS_USED.' and props_id='.$old_props_id;
                $ret  = $db->update('user_props', $arr_fields, $str_cond);
                Bingo_Log::warning("sql:".$db->getLastSQL());
                if($ret <= 0 || $db->getAffectedRows() !== 1){
                    Bingo_Log::warning("db update error! sql:". $db->getLastSQL()." [output:".serialize($ret)."]");
                    $has_db_error = true;
                    Bingo_Log::pushNotice("update_oldpropsid_err",1);
                    $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    break;
                }
                
                
                $field = array();
                $field['user_id'] 		= $user_id;
                $field['create_time']		= $now_time;
                $field['update_time'] 		= $now_time;
                $field['end_time']		= $medal_endtime; //目前暂时是这样
                $field['last_level_endtime']   =  0;
                $field['props_count']   	= 0;
                $field['left_num']		= 99;  //复用下，left_num = 99 表示是勋章送的狗牌
                $field['props_category'] 	= Service_Libs_Define::PROPS_CATEGORY_NAMEPLATE;
                $field['props_type']		= Service_Libs_Define::PROPS_TYPE_TIME;
                $field['props_id']		= $new_props_id;
                $field['ext']			= $title;
                $field['used_status']		= Service_Libs_Define::PROPS_USED; //本期默认使用
                $field['open_status']       = Service_Libs_Define::PROPS_OPEN;
                $arrDup = $field;
                unset($arrDup['props_id']);
                unset($arrDup['user_id']);
                $retTmp  = $db->insert('user_props', $field, NULL, $arrDup);
                Bingo_Log::warning("sql:".$db->getLastSQL());
                if($retTmp <= 0){
                    Bingo_Log::warning("_insertUserProps db insert or update error! sql:". $db->getLastSQL()." [output:".serialize($retTmp)."]");
                    $has_db_error = true;
                    $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                    Bingo_Log::pushNotice("props_insert_err",1);
                    break;
                }
                
                
            }

            //订单表
            /*
            $field = array();
            $field['props_id']		= $props_id;
            $field['user_id'] 		= $user_id;
            $field['props_name']    = $title;
            $field['scores']        = $props_scores;
            $field['total_scores']  = intval($arrReduceRet['total_scores']);
            $field['money_cost']    = isset($arrReduceRet['cost']['money_cost'])?$arrReduceRet['cost']['money_cost']:0;
            $field['fetch_cost']    = isset($arrReduceRet['cost']['fetch_cost'])?$arrReduceRet['cost']['fetch_cost']:0;
            $field['other_cost']    = isset($arrReduceRet['cost']['other_cost'])?$arrReduceRet['cost']['other_cost']:0;
            
            //时长类道具增加start_time\end_time
            $field['start_time']      = $now_time;
            $field['end_time']      = $medal_endtime;
            $field['num']      = 1;
            $field['create_time']   = $now_time;
            $field['status']        = 1;
            
            
            $arrPropsOrderInput = array(
                    'field' => $field,
                    'db' => $db,
            );
             
            $retPropsOrder = Dl_Db_PropsOrder::insert($arrPropsOrderInput);
            if ($retPropsOrder['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::pushNotice("insert_order_err",1);
                $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                $has_db_error = true;
                break;
            }
            */
        }while(0);
        //提交事务
        if ($has_db_error) {
            $tmp = $db->rollback();
            Bingo_Log::warning("db->rollback. [".serialize($tmp)."]");
            //$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
            Bingo_Log::pushNotice("rollback",1);

        }
        else {
            if (true === $db->commit()){
                
    
            } else{
                Bingo_Log::warning("db->commit. [".serialize($tmp)."]");
                $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
            }
        }
        //增加end_time 返回前端
        $ret_info = array();
        $ret_info['medal_endtime'] = $medal_endtime;
        $ret_info['cost'] = $arrReduceRet['data']['reduce'];
        Bingo_Log::pushNotice("errno",$error);
        Bingo_Log::pushNotice("errmsg",Tieba_Error::getErrmsg($error));
        return self::_errRet($error,$ret_info);
    }
    
    /**
     * @brief 狗牌更换接口
     * @arrInput:
     * 	uint32_t user_id
     *  uint32_t old_props_id
     *  uint32_t new_props_id
     *  uint32_t props_scores
     * @return: $arrOutput
     * 	BuyPropsOut output
     **/
    public static function changeNamePlatePropsNew($arrInput){
    
    
        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['user_id']) || !isset($arrInput['old_props_id']) ||!isset($arrInput['new_props_id']) ||!isset($arrInput['props_scores']) ){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
    
        //input params.
        $user_id = intval($arrInput['user_id']);
        $new_props_id = intval($arrInput['new_props_id']);
        $old_props_id = intval($arrInput['old_props_id']);
        $props_scores = intval($arrInput['props_scores']);
        $ueg_feature = $arrInput['ueg_feature'];
        Bingo_Log::pushNotice("user_id",$user_id);
        Bingo_Log::pushNotice("new_props_id",$new_props_id);
        Bingo_Log::pushNotice("old_props_id",$old_props_id);
        Bingo_Log::pushNotice("props_scores",$props_scores);
        if($user_id <= 0 || $new_props_id <= 0 || $old_props_id <= 0 || $props_scores < 0){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
    
        //output params.
        $output = false;
        if( null === ($db = Service_Libs_Db::getDB()) ){
            Bingo_Log::warning("get db fail.");
            $error = Tieba_Errcode::ERR_DB_CONN_FAIL;
            return self::_errRet($error);
        }
        //验证用户是否拥有要替换的狗牌
        $arrOwnIds = self::_getNamePlateIdFromDB($user_id);
        if (!in_array($old_props_id,$arrOwnIds)){
            Bingo_Log::warning("old_props_id error. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        $has_db_error = false;
        $now_time = time();
        $tmp = 0;
        //your code here......
        $error = Tieba_Errcode::ERR_SUCCESS;
        do {
            //开始事务
            $db->startTransaction();
            
            $props_info_new = self::getNameplateProps($new_props_id,'for update');
            if ($props_info_new['props_id'] != $new_props_id) {
                Bingo_Log::warning("get props error! props_id: [".$new_props_id."] ret:[" . print_r($props_info_new, true) .']');
                $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                Bingo_Log::pushNotice("getOneProps_err",1);
                $has_db_error = true;
                break;
            }
            Bingo_Log::pushNotice("props_scores",$props_scores);
            Bingo_Log::pushNotice("props_category",$props_info_new['props_category']);
            Bingo_Log::pushNotice("buy_num",1);
            if ($props_info_new['props_category'] != Service_Libs_Define::PROPS_CATEGORY_NAMEPLATE){//购买非狗牌道具
                Bingo_Log::warning('only suport buy PROPS_CATEGORY_NAMEPLATE');
                Bingo_Log::pushNotice("not_nameplate",1);
                $error = Tieba_Errcode::ERR_PARAM_ERROR;
                $has_db_error = true;
                break;
            }
             
            //判断狗牌的是否被占用
            if ($props_info_new['end_time'] > $now_time){
                Bingo_Log::warning('nameplate_inused!!');
                Bingo_Log::pushNotice("nameplate_inused",1);
                $error = Tieba_Errcode::ERR_PARAM_ERROR;
                $has_db_error = true;
                break;
            }
            
            
            $props_info_old = self::getNameplateProps($old_props_id,'for update');
            if ($props_info_old['props_id'] != $old_props_id) {
                Bingo_Log::warning("get props error! props_id: [".$old_props_id."] ret:[" . print_r($props_info_old, true) .']');
                $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                Bingo_Log::pushNotice("getOneProps_err",1);
                $has_db_error = true;
                break;
            }
            
            if ($props_info_old['props_category'] != Service_Libs_Define::PROPS_CATEGORY_NAMEPLATE){//购买非狗牌道具
                Bingo_Log::warning('only suport buy PROPS_CATEGORY_NAMEPLATE');
                Bingo_Log::pushNotice("not_nameplate",1);
                $error = Tieba_Errcode::ERR_PARAM_ERROR;
                $has_db_error = true;
                break;
            }
              
            //调用service实现user_scores user_props操作，解决DDBS不支持分布式事务的问题。
            //service 需要返回勋章过期时间，
            $arrServiceInput = array(
                    'user_id' => $user_id,
                    'old_props_id' => $old_props_id,
                    'new_props_id' => $new_props_id,
                    'buy_type' => 2,
                    'title' => $props_info_new['title'],
                    'props_scores' => $props_scores,
                    'ueg_feature' => $ueg_feature,
            );
            
            Bingo_Timer::start('AddUserNamePlate');
            $arrRet = Tieba_Service::call("tbmall", "addUserNameplate", $arrServiceInput,NULL, NULL, 'post', 'php', 'utf-8');
            Bingo_Timer::end('AddUserNamePlate');
            if (!isset($arrRet['errno']) || intval($arrRet['errno']) !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('AddUserNamePlate error! output:' . serialize($arrServiceInput));
                Bingo_Log::pushNotice("AddUserNamePlate_err",1);
                $error = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
                $has_db_error=true;
                break;
            }
            
            Bingo_Log::pushNotice("total_cost",$props_scores);
            Bingo_Log::pushNotice("money_cost",intval($arrRet['data']['money_cost']));
            Bingo_Log::pushNotice("other_cost",intval($arrRet['data']['other_cost']));
            Bingo_Log::pushNotice("fetch_cost",intval($arrRet['data']['fetch_cost']));
            
            $medal_endtime = $arrRet['data']['medal_endtime'];
            
            //更新props表，end_time 字段
            //1 new_props_id
            $arr_cond = array();
            $arr_cond[] =  'props_id='.$new_props_id;
            $arr_cond[] = 'end_time<'.$now_time;
            $arr_fields = array();
            $arr_fields['end_time'] = $medal_endtime;//记录过期时间
            $arr_fields['update_time'] = $now_time;
            $arr_fields['user_id'] = $user_id;
            $db_props = $db->update(self::TABLE_NAMEPALTE, $arr_fields, $arr_cond);
            Bingo_Log::warning("socre sql:".$db->getLastSQL());
            if($db_props <= 0 || $db->getAffectedRows() !== 1){
                Bingo_Log::warning("Props db update error! sql:". $db->getLastSQL()."db error:".serialize($db->error())." [output:".serialize($db_props)."]");
                Bingo_Log::pushNotice("new_props_update_err_or_inuse",1);
                $error = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
                $has_db_error=true;
            }
             
            //2 old_props_id
            $arr_cond = array();
            $arr_cond[] =  'props_id='.$old_props_id;
            $arr_cond[] = 'end_time>'.$now_time;
            $arr_fields = array();
            $arr_fields['end_time'] = 0;//更新为0
            $arr_fields['update_time'] = $now_time;
            $arr_fields['user_id'] = 0;
            $db_props = $db->update(self::TABLE_NAMEPALTE, $arr_fields, $arr_cond);
            Bingo_Log::warning("socre sql:".$db->getLastSQL());
            if($db_props <= 0 || $db->getAffectedRows() !== 1){
                Bingo_Log::warning("Props db update error! sql:". $db->getLastSQL()."db error:".serialize($db->error())." [output:".serialize($db_props)."]");
                Bingo_Log::pushNotice("old_props_update_err_or_inuse",1);
                $error = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
                $has_db_error=true;
            }
            
    
        }while(0);
        //提交事务
        if ($has_db_error) {
            $tmp = $db->rollback();
            Bingo_Log::warning("db->rollback. [".serialize($tmp)."]");
            //$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
            Bingo_Log::pushNotice("rollback",1);
            
        }
        else {
            if (true === $db->commit()){
                
                //更新SE
                $se_new_ret = Service_Libs_Se::updateData(array($new_props_id), $medal_endtime);
                Bingo_Log::pushNotice("se_new_ret",$se_new_ret);
                $se_old_ret = Service_Libs_Se::updateData(array($old_props_id), 0);
                Bingo_Log::pushNotice("se_old_ret",$se_old_ret);
                //道具对账
                $props_check = Service_Mall_Mall::_addPropsToUcenter($user_id,1);
                Bingo_Log::pushNotice("props_check",$props_check);
    
                //积分对账
                $scores_check = Service_Mall_Mall::_addScoresToUcenter($user_id, 1);
                Bingo_Log::pushNotice("scores_check", $scores_check);
    
            } else{
                Bingo_Log::warning("db->commit. [".serialize($tmp)."]");
                $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
            }
        }
        //增加end_time 返回前端
        $props_info['end_time'] =  $medal_endtime;
        Bingo_Log::pushNotice("errno",$error);
        Bingo_Log::pushNotice("errmsg",Tieba_Error::getErrmsg($error));
        return self::_errRet($error,$props_info);
    }
    
    
    
    private static function _getNamePlateNumFromDB($user_id,$type=0){
        if( null === ($db = Service_Libs_Db::getDB()) ){
            Bingo_Log::warning("get db fail.");
            return false;
        }
        //DB中获取狗牌的个数
        $strConds = "user_id = ".$user_id .' and props_category = '.Service_Libs_Define::PROPS_CATEGORY_NAMEPLATE .' and end_time>'.time();
        if ($type === 1){
            $strConds .= " and left_num=99"; //left_num = 99是勋章赠送的道具,复用下left_num
        }
        $appraise_count = $db->selectCount ( 'user_props', $strConds, NULL, NULL );
        Bingo_Log::warning("socre sql:".$db->getLastSQL());
        if (false === $appraise_count) {
            Bingo_Log::warning ( "user_props db selectCount error![output:" . serialize ( $db->error () ) . "]" );
            return false;
        }
        return $appraise_count;
    }
    
    private static function _getNamePlateIdFromDB($user_id,$type=0){
        if( null === ($db = Service_Libs_Db::getDB()) ){
            Bingo_Log::warning("get db fail.");
            return false;
        }
        //DB中获取评价个数
        $strField = array('props_id');
        $strConds = "user_id = ".$user_id .' and props_category = '.Service_Libs_Define::PROPS_CATEGORY_NAMEPLATE .' and end_time>'.time();
        if ($type === 1){
            $strConds .= " and left_num=99"; //left_num = 99是勋章赠送的道具,复用下left_num
        }
        $props_order_info = $db->select ( 'user_props', $strField, $strConds, NULL, NULL );
        if (false === $props_order_info) {
            Bingo_Log::warning ( "db select error! $user_id $type [output:" . serialize ( $db->error () ) . "] SQL:" .$db->getLastSQL());
            return self::_errRet ( Tieba_Errcode::ERR_DB_AFFECTED_ZERO_ROWS );
        }
        $arrIds = array();
        foreach ($props_order_info as $one){
            $arrIds[] = $one['props_id'];
        }
    
        return $arrIds;
    }

    /* nameplate start */
    private static function _selectCommon($arrInput){
        if( null === ($db = Service_Libs_Db::getDB()) ){
            Bingo_Log::warning("get db fail!");
            return false;
        }

        $strTableName = isset($arrInput['table_name']) ? $arrInput['table_name'] : NULL;
        $strFields = isset($arrInput['fields']) ? $arrInput['fields'] : NULL;
        $strConds = isset($arrInput['conds']) ? $arrInput['conds'] : NULL;
        $strAppends = isset($arrInput['appends']) ? $arrInput['appends'] : NULL;

        $result = $db->select($strTableName, $strFields, $strConds, NULL, $strAppends);

        if(false === $result){
            Bingo_Log::warning("sql[" . $db->getLastSQL() . "] _selectCommon db select error! [input:".serialize($arrInput)."] [output:".serialize($db->error())."]");
            return false;
        }

        return $result;
    }

    private static function _handleRandNameplateResult($data, $arrRet){
        if(empty($arrRet)){
            return $data;
        }
        //done : 处理从数据库random出来的数据格式, 注意要保证unique!
        foreach($arrRet as $_v){
            $data[intval($_v['props_id'])] = $_v;
        }
        return $data;
    }

    private static function _createNameplate($arrInput){
        if( null === ($db = Service_Libs_Db::getDB()) ){
            Bingo_Log::warning("get db fail.");
            return false;
        }

        //$tmp  = $db->query('set names utf8');
        $ret = $db->insert('nameplate', $arrInput['props_info']);
        if($ret <= 0){
            Bingo_Log::warning("_insertNameplate db insert error! [input:".serialize($arrInput)."] [output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return false;
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        return $arrOutput = array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg ( $errno ),
            'data' => $arrInput['prosp_id'],
        );
    }

    private static function _addNameplate($strTitle, $strTag = 'default', $arrNameplate = array()){
        if( false === ($intPropsId = Service_Libs_IdAlloc::getId('nameplate_id')) ){
            return false;
        }
        $arrPropsInfo = array();
        $arrPropsInfo['props_id']            =  intval($intPropsId);
        $arrPropsInfo['props_type']          =  Service_Libs_Define::PROPS_TYPE_TIME;
        $arrPropsInfo['props_category']      =  Service_Libs_Define::PROPS_CATEGORY_NAMEPLATE;
//        $arrPropsInfo['buy_count']           =  0;
//        $arrPropsInfo['is_recommened']       =  0;
        $arrPropsInfo['title']               =  $strTitle;
//        $arrPropsInfo['description']         =  '';
//        $arrPropsInfo['pic_url']             =  '';
//        $arrPropsInfo['example_url']         =  '';
//        $arrPropsInfo['ext']                 =  $strTag;
//        $arrPropsInfo['price']               =  100000; //暂时没用, 线上钱是为了防止漏洞
//        $arrPropsInfo['scores']              =  100000; //暂时没用, 线上钱是为了防止漏洞/
        $arrPropsInfo['time_interval']       =  86400;
//        $arrPropsInfo['count_interval']      =  0;
//        $arrPropsInfo['user_level']          =  0;
        $arrPropsInfo['free_user_level']     =  0;
        $arrPropsInfo['status']              =  1; //个性铭牌未被购买, 具体见sql建表语句注释
        $arrPropsInfo['end_time']            =  0;
        $arrPropsInfo['create_time']            =  intval(Bingo_Timer::getNowTime());
        $arrPropsInfo['first_dir']            =  $arrNameplate['first_dir'];
        $arrPropsInfo['second_dir']            =  $arrNameplate['second_dir'];
        $arrPropsInfo['optional_word']            =  serialize($arrNameplate['optional_word']);
        $arrPropsInfo['pattern']            =  serialize($arrNameplate['pattern']);

        $arrInput = array(
            'props_info' => $arrPropsInfo,
        );
        $arrOutput = self::_createNameplate($arrInput);
        if( !isset($arrOutput['errno']) || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno'] ){
            Bingo_Log::warning("_addNameplate call _createNameplate error [input:".serialize($arrInput)."] [output:".serialize($arrOutput)."]");
            return false;
        }

        return $intPropsId;
    }

    private static function _delNameplate($arrTitle, $arrId){
        $strConds = "title IN(";
        foreach($arrTitle as $strTitle){
            $strTitle = trim($strTitle);
            $strConds .= "'$strTitle', ";
        }
        $strConds= substr($strConds, 0, strlen($strConds) - 2);
        $strConds .= ")";
        if(empty($arrTitle)){
            $strConds = "";//可能没有title信息
        }

        $strCondsAppend = "OR props_id IN(";
        $intCnt = strlen($strCondsAppend);
        foreach($arrId as $intId){
            if(intval($intId) > 0){
                $intId = intval($intId);
                $strCondsAppend .= "$intId, ";
            }
        }
        if($intCnt < strlen($strCondsAppend)){
            $strCondsAppend = substr($strCondsAppend, 0, strlen($strCondsAppend) - 2);
            $strCondsAppend .= ")";
            $strConds = $strConds . " AND " . $strCondsAppend;
        }

        $arrInput = array(
            'table_name' => self::TABLE_NAMEPALTE,
            'fields' => 'props_id', //判断'user_order_status的依据
            'conds' => $strConds,
        );
        if( false === ($arrRet = self::_selectCommon($arrInput))){
            return false;
        }
        Bingo_Log::debug("delNameplate raw data from db : ".serialize($arrRet));

        $arrOutput = array();
        foreach($arrRet as $_v){
            $arrOutput[] = $_v['props_id'];
        }
        return $arrOutput;
    }

    private static function _getNameplateMaxId($intStatus){
        $intMaxId = self::MIN_NAMEPLATE_ID;
        $key = self::PREFIX_MAX_NAMEPLATE_ID . intval($intStatus);
        if(null !== ($intMaxId = Service_Libs_Cache::get($key))){
            if($intMaxId > 0){
                return intval($intMaxId); //$res is a unsigned int max props_id
            }
        }

        $intPropsCategory = Service_Libs_Define::PROPS_CATEGORY_NAMEPLATE;
        $strConds = "props_category = $intPropsCategory";
        $intNowTime = intval(Bingo_Timer::getNowTime());
        $strTimeConds = (self::NAMEPLATE_UNSOLD_STATUS === $intStatus) ?
            "end_time < $intNowTime" : "end_time > $intNowTime";
        $strConds .= (self::NAMEPLATE_ALL_STATUS === $intStatus) ? '' : " AND $strTimeConds";
//        switch($intStatus){
//            case 3 :
//                $strConds .= "AND $strTimeConds";
//                break;
//            case 4 :
//                $strConds .= "AND $strTimeConds";
//                break;
//            default :
//        }

        $arrInput = array(
            'table_name' => self::TABLE_NAMEPALTE,
            'fields' => 'MAX(props_id)', //判断'user_order_status的依据
            'conds' => $strConds,
        );
        if( false === ($arrRet = self::_selectCommon($arrInput))){
            Bingo_Log::warning('get max props_id failed!');
            return false;
        }
        $intMaxId = $arrRet[0]['MAX(props_id)'];

        Service_Libs_Cache::add($key, $intMaxId, 300);

        return $intMaxId;
    }

    private static function _replenishNameplateExpiredTime($data){
//        in param must have this format:
//        array(
//            'props_id' =,
//            ...
//        )
        if(empty($data))
            return $data;
        $arrPropsId = array();
        foreach($data as $value){
            if(intval($value['props_id']) >= self::MIN_NAMEPLATE_ID ){
                $arrPropsId[] = intval($value['props_id']);
            }
        }
        if(empty($arrPropsId)){
            return $data;
        }
        //todo : 按props_id从props表中取出个性铭牌的过期时间

        $arrInput = array(
            'table_name' => self::TABLE_NAMEPALTE,
            'fields' => 'props_id, end_time',
            'conds' => 'props_id IN(' . implode(',', $arrPropsId) . ')',
        );
        if( false === ($arrRet = self::_selectCommon($arrInput))){
            Bingo_Log::warning('get nameplate expired time error!');
            return $data;
        }

//        array (
//            0 =>
//                array (
//                    'props_id' => '1110000001',
//                    'end_time' => '0',
//                ),
//            1 =>
//                array (
//                    'props_id' => '1110000002',
//                    'end_time' => '0',
//                ),
//        ){"no":0,"error":"\u6210\u529f","data":{"nameplate":{"pt":1,"pn":0,"nameplates":[{"props_id":1110000012,"title":"test6"},{"props_id":1110000011,"title":"test7"},{"props_id":1110000010,"title":"test5"},{"props_id":1110000009,"title":"test8"}
        $arrOutput = array();
        foreach($data as $_v){
            $arrOutput[intval($_v['props_id'])] = $_v;
        }
        foreach($arrRet as $_v){
            $arrOutput[intval($_v['props_id'])]['end_time'] = intval($_v['end_time']);
        }
        $data = $arrOutput;

        return $data;
    }

    private static function _getRandNameplate($intBeginId, $intStatus, $intPageSize){
        $key = self::PREFIX_RAND_NAMEPLATE.$intBeginId.'_'.$intStatus.'_'.$intPageSize;
        if(null !== ($data = Service_Libs_Cache::get($key, intval(self::$_conf['nameplate_use_cache'])))){
            return $data;
        }

        $strConds = "props_id >= $intBeginId AND props_category = 112";
        $intNowTime = intval(Bingo_Timer::getNowTime());
        $strTimeConds = (self::NAMEPLATE_UNSOLD_STATUS === $intStatus) ?
            "end_time < $intNowTime" : "end_time > $intNowTime";
        $strConds .= (self::NAMEPLATE_ALL_STATUS === $intStatus) ? "" : " AND $strTimeConds";

        $intCount = 0;
        $intRetry = 2;
        $strLimitAppends = "LIMIT $intPageSize";
        $data = array();
        while($intCount < $intPageSize && $intRetry-- > 0){
            //自己做mod逻辑, 保证数据够数
            $arrInput = array(
                'table_name' => self::TABLE_NAMEPALTE,
                'fields' => 'props_id, title, end_time', //判断'user_order_status的依据
                'conds' => $strConds,
                'appends' => $strLimitAppends,
            );
            if( false === ($arrRet = self::_selectCommon($arrInput))){
                Bingo_Log::warning('get rand nameplates  failed!');
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            Bingo_Log::debug("getRandomNameplate rawdata from db : ".var_export($arrRet, true));
            $data = self::_handleRandNameplateResult($data, $arrRet);
            $intCount += count($data);
            $intRest = $intPageSize - $intCount;
            //保证只从数据库取两次数据!
            $strConds = "props_category = 112";
            $strConds .= (self::NAMEPLATE_ALL_STATUS === $intStatus) ? "" : " AND $strTimeConds";

            $strLimitAppends = "ORDER BY props_id LIMIT 0, $intRest";
        }

        Service_Libs_Cache::add($key, $data, 1800);
        return $data;
    }

    private static function _genSQLInConds($arrData, $boolIsString = false){
        // strConds is '1, 2, 3, 4';
        $strConds = '';
        foreach($arrData as $_v){
            if($boolIsString){
                $strConds .= "'$_v', ";
            }
            else{
                $strConds .= "$_v, ";
            }
        }
        $strConds= substr($strConds, 0, strlen($strConds) - 2);
        return $strConds === '' ? false : $strConds;
    }

    private static function _findOmitNamplateByTitle($arrInput){
        $arrRet = array();
        foreach($arrInput as $arrNameplate){
            $arrTitle = $arrNameplate['title'];
            if(false === ($arrOutput = Service_Libs_Se::selectData(0, $arrTitle, 0, 10))){
                Bingo_Log::warning('Service_Libs_Se::selectData error!');
                continue;
            }
            if($arrOutput['all_num'] == 0){
                $arrRet[] = $arrNameplate;
            }
        }
        return $arrRet;
    }

    private static function _transNameplateInfoFromDB($arrRawData){
        //处理_getNamePlateByTitle()返回的数据, 主要是反序列换optional_word和pattern
        $arrNampalteInfo = empty($arrRawData[0]) ? NULL : $arrRawData[0];
        if(isset($arrNampalteInfo['optional_word'])){
            $arrNampalteInfo['optional_word'] = unserialize($arrNampalteInfo['optional_word']);
        }
        if(isset($arrNampalteInfo['pattern'])){
            $arrNampalteInfo['pattern'] = unserialize($arrNampalteInfo['pattern']);
        }

        return $arrNampalteInfo;
    }

    private static function _getNamePlateByTitle($stringTitle){
        $arrNameplateInfo = NULL;
        $arrInput = array(
            'table_name' => self::TABLE_NAMEPALTE,
            'fields' => "props_id, title, first_dir, second_dir, optional_word, pattern, end_time",
            'conds' => "title = '$stringTitle'",
            'appends' => NULL,
        );
        if(false === ($arrNameplateInfo = self::_selectCommon($arrInput))){
            return false;
        }

        return self::_transNameplateInfoFromDB($arrNameplateInfo);
    }

    private static function _getNamePlateFromUserpropsByUserId($intUserId){
        $arrNameplateInfo = NULL;
        $intPropsCategory = Service_Libs_Define::PROPS_CATEGORY_NAMEPLATE;
        $intNowTime = Bingo_Timer::getNowTime();
        $arrInput = array(
            'table_name' => 'user_props',
            'fields' => "props_id, end_time",
            'conds' => "user_id = $intUserId AND props_category = $intPropsCategory AND end_time > $intNowTime",
            'appends' => NULL,
        );
        if(false === ($arrNameplateInfo = self::_selectCommon($arrInput))){
            return false;
        }
        return self::_transNameplateInfoFromDB($arrNameplateInfo);
    }

    private static function _getNamePlateById($intPropsId){
        $arrNameplateInfo = NULL;
        $intPropsCategory = Service_Libs_Define::PROPS_CATEGORY_NAMEPLATE;
        $arrInput = array(
            'table_name' => self::TABLE_NAMEPALTE,
            'fields' => "props_id, title, first_dir, second_dir, optional_word, pattern, end_time",
            'conds' => "props_id = $intPropsId AND props_category = $intPropsCategory",
            'appends' => NULL,
        );
        if(false === ($arrNameplateInfo = self::_selectCommon($arrInput))){
            return false;
        }
        return self::_transNameplateInfoFromDB($arrNameplateInfo);
    }

    private static function _mbStringToArray($string){
        $strlen = mb_strlen($string);
        while ($strlen) {
            $array[] = mb_substr($string,0,1,"UTF-8");
            $string = mb_substr($string,1,$strlen,"UTF-8");
            $strlen = mb_strlen($string);
        }
        return $array;
    }

    private static function _checkComplex($char){
        $char = Bingo_Encode::convert($char, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
        $simple_char = trans_to_gb($char);
        if($simple_char != $char){
            return false;
        }
        return true;
    }

    private static function _checkCharacter($char){
        $preg_rule = '/^[\x{4e00}-\x{9fa5}a-z]$/u';//只能是一个汉字或者是英文小写
        if(0 == preg_match($preg_rule, $char)){
            return false;
        }
        //不可以使繁体字
        return self::_checkComplex($char);
    }

    private static function _checkNameplate($arrNameplate, $type, $mixData, &$errno){

        switch($type){
            case 0:{ //0 是检查个性铭牌是否选自可选集合和是否修改的可修改的字
                if(!isset($arrNameplate['optional_word']) || !isset($arrNameplate['pattern'])){
                    return false;
                }
                $stringNewTitle = strval($mixData);
                $arrNewTitle = self::_mbStringToArray($stringNewTitle);
                $arrOldTitle = self::_mbStringToArray(trim($arrNameplate['title']));
                $arrPattern = $arrNameplate['pattern'];
                if(count($arrNewTitle) != count($arrOldTitle) || count($arrNewTitle) != count($arrPattern)){
                    return false;
                }
                $intTitleCnt = count($arrNewTitle);
                $intPatternCnt = count($arrPattern);
                for($i = 0; $i <= $intPatternCnt; ++$i){
                    switch($arrPattern[$i]){
                        case 0://0代表空白位, 即不可填写任何汉字
                            if($intTitleCnt != 0){
                                return false;
                            }
                            break;
                        case 1://代表固定不可更改的主题词
                            if($arrNewTitle[$i] != $arrOldTitle[$i]){
                                return false;
                            }
                            break;
                        case 2://代表是可选字, 可选字必须再可选集合里
                            if(false === array_search($arrNewTitle[$i], $arrNameplate['optional_word'])){
                                return false;
                            }
                            break;
                        case 3://3代表是用户自定义的字
                            if(!self::_checkCharacter($arrNewTitle[$i])){
                                $errno = 1;
                                return false;
                            }
                            break;
                        default :
                    }
                    --$intTitleCnt;
                }
                break;
            }
            default :
        }


        return true;
    }

    private static function _genNameplateTableInfo($arrNameplate, $arrUserNameplate){
        $arrTags = array('英雄联盟', '魔兽世界', '剑网三');
        $strTitle = $arrNameplate['title'];
//        if(mb_check_encoding($strTitle, 'GBK')){
//            $strTitle = Bingo_Encode::convert($strTitle, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
//        }
//        else if(!mb_check_encoding($strTitle, 'UTF-8')){
//            return false;
//        }
        $arrTitle = self::_mbStringToArray($strTitle);
        $arrPattern = array();
        $arrOptional = array('的', 'の', '之');
        $strFirst = in_array($arrNameplate['ext'], $arrTags) ? '游戏' : '漫画';

        $strSecond = trim($arrNameplate['ext']);
        $intFlag = 1;
        foreach($arrTitle as $char){
            if($char == '的'){
                $arrPattern[] = 2;
                $intFlag = 3;
                continue;
            }
            $arrPattern[] = $intFlag;
        }
        if($intFlag === 1){//三种固定的格式 : 暗夜精灵、死亡骑士和熊猫人
            if($arrTitle[0] == '暗'){//i am 山寨!
                $arrPattern = array();
                $intFlag = 1;
                foreach($arrTitle as $char){
                    if($char == '灵'){
                        $arrPattern[] = 1;
                        $intFlag = 3;
                        continue;
                    }
                    $arrPattern[] = $intFlag;
                }
            }
            else if($arrTitle[0] == '死'){
                $arrPattern = array();
                $intFlag = 1;
                foreach($arrTitle as $char){
                    if($char == '士'){
                        $arrPattern[] = 1;
                        $intFlag = 3;
                        continue;
                    }
                    $arrPattern[] = $intFlag;
                }
            }
            else if($arrTitle[0] == '熊'){
                $arrPattern = array();
                $intFlag = 1;
                foreach($arrTitle as $char){
                    if($char == '人'){
                        $arrPattern[] = 1;
                        $intFlag = 3;
                        continue;
                    }
                    $arrPattern[] = $intFlag;
                }
            }
            else if($arrTitle[0] == '七'){
                $arrPattern = array();
                $intFlag = 1;
                foreach($arrTitle as $char){
                    if($char == '秀'){
                        $arrPattern[] = 1;
                        $intFlag = 3;
                        continue;
                    }
                    $arrPattern[] = $intFlag;
                }
            }
            else if($arrTitle[0] == '圣'){
                $arrPattern = array();
                $intFlag = 1;
                foreach($arrTitle as $char){
                    if($char == '士'){
                        $arrPattern[] = 1;
                        $intFlag = 3;
                        continue;
                    }
                    $arrPattern[] = $intFlag;
                }
            }
            else if($arrTitle[0] == '天'){
                $arrPattern = array();
                $intFlag = 1;
                foreach($arrTitle as $char){
                    if($char == '策'){
                        $arrPattern[] = 1;
                        $intFlag = 3;
                        continue;
                    }
                    $arrPattern[] = $intFlag;
                }
            }
            else if($arrTitle[0] == '德'){
                $arrPattern = array();
                $intFlag = 1;
                foreach($arrTitle as $char){
                    if($char == '尼'){
                        $arrPattern[] = 1;
                        $intFlag = 3;
                        continue;
                    }
                    $arrPattern[] = $intFlag;
                }
            }
            else if($arrTitle[0] == '牛'){
                $arrPattern = array();
                $intFlag = 1;
                foreach($arrTitle as $char){
                    if($char == '人'){
                        $arrPattern[] = 1;
                        $intFlag = 3;
                        continue;
                    }
                    $arrPattern[] = $intFlag;
                }
            }
            else if($arrTitle[0] == '血'){
                $arrPattern = array();
                $intFlag = 1;
                foreach($arrTitle as $char){
                    if($char == '灵'){
                        $arrPattern[] = 1;
                        $intFlag = 3;
                        continue;
                    }
                    $arrPattern[] = $intFlag;
                }
            }
            else{
                Bingo_Log::warning("title : $strTitle");
            }
            $arrOptional = array();
        }

        $filed = array();
        $filed['props_id'] = intval($arrNameplate['props_id']);
        $filed['props_category'] = intval($arrNameplate['props_category']);
        $filed['end_time'] = intval($arrNameplate['end_time']);
        $filed['create_time'] = intval($arrNameplate['create_time']);
        $filed['title'] = $strTitle;
        $filed['scores'] = intval($arrNameplate['scores']);
        $filed['time_interval'] = intval($arrNameplate['time_interval']);
        $filed['status'] = intval($arrNameplate['status']);
        $filed['user_id'] = intval($arrUserNameplate['user_id']);
        $filed['optional_word'] = serialize($arrOptional);
        $filed['pattern'] = serialize($arrPattern);
        $filed['first_dir'] = $strFirst;
        $filed['second_dir'] = $strSecond;

        return $filed;
    }

    //↑private ↓public

    public static function addNameplate($arrInput){

        if (strlen($arrInput['title']) <= 0
            || !isset($arrInput['title'])  || !isset($arrInput['cmd'])){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        $strTitle = trim($arrInput['title']);
        $strTag = trim($arrInput['tag']);
        $strTag = $strTag == "" ? 'default' : $strTag;
        $intCmd = intval($arrInput['cmd']);
        $data = array();

        switch($intCmd){
            case 1 : //1代表添加
                if(false === ($intPropsId = self::_addNameplate($strTitle, $strTag, $arrInput))){
                    $errno = Tieba_Errcode::ERR_DB_AFFECTED_ZERO_ROWS;
                    return self::_errRet($errno);
                }
                if(false === ($arrOutput = Service_Libs_Se::insertData($intPropsId, $strTitle))){
                    $errno = Tieba_Errcode::ERR_UNKOWN;
                    return self::_errRet($errno);
                }
                break;
            default :
                Bingo_Log::warning('in addNameplate command no error!');
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        Bingo_Log::pushNotice("errno",$errno);
        $arrOutput = array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg ( $errno ),
            'data' => $data,
        );
        return $arrOutput;

    }

//    public static function delNameplate($arrInput){
//
//        if (!isset($arrInput['title'])  || !isset($arrInput['cmd']) ||
//            intval($arrInput['cmd']) < 0 || intval($arrInput['cmd']) > 5 ){
//            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
//            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
//        }
//        $intCmd = intval($arrInput['cmd']);
//        $arrUserId = $arrInput['user_id'];
//        $arrTitle = $arrInput['title'];
//        $arrId = $arrInput['id'];
//        $data = array();
//
//        switch($intCmd){
//            case 2 : //2代表删除
//                if(false === ($arrPropsId = self::_delNameplate($arrTitle, $arrId))){
//                    $errno = Tieba_Errcode::ERR_DB_AFFECTED_ZERO_ROWS;
//                    return self::_errRet($errno);
//                }
//                for
//                if(!empty($arrPropsId)){
//                    if(false === ($strConds = self::_genSQLInConds($arrPropsId))){
//                        Bingo_Log::warning("get sql fail!");
//                        $errno = Tieba_Errcode::ERR_UNKOWN;
//                        return self::_errRet($errno);
//                    }
//                    $sql_props = "DELETE FROM props WHERE props_id IN($strConds)";
//                    if(false === ($strConds = self::_genSQLInConds($arrPropsId))){
//                        Bingo_Log::warning("get sql fail!");
//                        $errno = Tieba_Errcode::ERR_UNKOWN;
//                        return self::_errRet($errno);
//                    }
//                    $sql_userprops = "DELETE FROM user_props WHERE props_id IN($strConds)";
//                    if(false === ($strConds = self::_genSQLInConds($arrUserId))){
//                        Bingo_Log::warning("get sql fail!");
//                        $errno = Tieba_Errcode::ERR_UNKOWN;
//                        return self::_errRet($errno);
//                    }
//                    $sql_userprops .= " AND user_id IN($strConds)";
//
//                    if( null === ($db = Service_Libs_Db::getDB()) ){
//                        Bingo_Log::warning("get db fail!");
//                        return false;
//                    }
//
//                    $errno = Tieba_Errcode::ERR_SUCCESS;
//                    $db->startTransaction();
//                    do{
//                        $result = $db->query($sql_props);
//                        if(false === $result){
//                            $errno = Tieba_Errcode::ERR_UNKOWN;
//                            Bingo_Log::warning("_delete db error! [input:".serialize($arrInput)."] [output:".serialize($db->error())."]");
//                            break;
//                        }
//                        $result = $db->query($sql_userprops);
//                        if(false === $result){
//                            $errno = Tieba_Errcode::ERR_UNKOWN;
//                            Bingo_Log::warning("_delete db error! [input:".serialize($arrInput)."] [output:".serialize($db->error())."]");
//                            break;
//                        }
//                        if(false === ($arrOutput = Service_Libs_Se::deleteData($arrPropsId))){
//                            $errno = Tieba_Errcode::ERR_UNKOWN;
//                            Bingo_Log::warning("se::deleteData error! input : [".serialize($arrPropsId)."]");
//                            break;
//                        }
//                        $db->commit();
//                    }while(0);
//                    if(Tieba_Errcode::ERR_SUCCESS != $errno){
//                        $db->rollback();
//                    }
//                }
//                break;
//            default :
//                Bingo_Log::warning('in addNameplate command no error!');
//        }
//
//        $errno = Tieba_Errcode::ERR_SUCCESS;
//        Bingo_Log::pushNotice("errno",$errno);
//        $arrOutput = array (
//            'errno' => $errno,
//            'errmsg' => Tieba_Error::getErrmsg ( $errno ),
//            'data' => $data,
//        );
//
//        return $arrOutput;
//    }

    public static function delSeNameplate($arrInput){
        $intPropsId = $arrInput['props_id'];
        if(false === ($arrOutput = Service_Libs_Se::deleteData(array($intPropsId)))){
            $errno = Tieba_Errcode::ERR_UNKOWN;
            Bingo_Log::warning("se::deleteData error! input : [".serialize($intPropsId)."]");
            return self::_errRet ( $errno );
        }
        $errno = Tieba_Errcode::ERR_SUCCESS;
        Bingo_Log::pushNotice("errno",$errno);
        $arrOutput = array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg ( $errno ),
            'data' => $intPropsId,
        );

        return $arrOutput;

    }

    public static function delNameplate($arrInput){
        if (!isset($arrInput['title'])){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        $stringTitle = $arrInput['title'];
        //根据铭牌名查询铭牌所对应的铭牌id
        if(flase === ($arrNameplateInfo = self::_getNamePlateByTitle($stringTitle))){
            Bingo_Log::warning("_getNamePlateByTitle error! input[" . serialize ( $stringTitle) . "]" );
//            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }
        $intPropsId = intval($arrNameplateInfo['props_id']);
//        $intUserId = intval($arrNameplateInfo['user_id']);
        $intUserId = intval($arrInput['user_id']);
        if(NULL == $arrNameplateInfo || $intPropsId <= 0 || $intUserId <= 0){
            Bingo_Log::warning("the nameplate with this title not exist! input[" . serialize ( $stringTitle) . "]" );
//            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }

        $intTime = Bingo_Timer::getNowTime();
        $sql_props = "DELETE FROM nameplate WHERE props_id = $intPropsId";
        $sql_userprops = "DELETE FROM user_props WHERE user_id = $intUserId AND props_id = $intPropsId and end_time > $intTime";

//        $arrInput = array(
//            'user_id' => $intUserId,
//            'props_id' => $intPropsId,
//        );
//        $arrOutput = Tieba_Service::call('tbmall', 'closeProps', $arrInput, NULL, NULL, 'post', 'php', 'utf-8','local');
//        if (false === $arrOutput || !isset($arrOutput['errno']) ||
//            $arrOutput['errno'] != 0){
//            Bingo_Log::warning ("call closeProps error! input[".serialize($arrInput)."]");
//            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
//        }

        if( null === ($db = Service_Libs_Db::getDB()) ){
            Bingo_Log::warning("get db fail!");
            return false;
        }
        $result = $db->query($sql_props);
        if(false === $result){
            $errno = Tieba_Errcode::ERR_UNKOWN;
            Bingo_Log::warning("_delete  nameplate db error! [input:".serialize($arrInput)."] [output:".serialize($db->error())."]");
//            return self::_errRet ( $errno );
        }
        $result = $db->query($sql_userprops);
        if(false === $result){
            $errno = Tieba_Errcode::ERR_UNKOWN;
            Bingo_Log::warning("_delete user_props db error! [input:".serialize($arrInput)."] [output:".serialize($db->error())."]");
//            return self::_errRet ( $errno );
        }
        if(false === ($arrOutput = Service_Libs_Se::deleteData(array($intPropsId)))){
            $errno = Tieba_Errcode::ERR_UNKOWN;
            Bingo_Log::warning("se::deleteData error! input : [".serialize($intPropsId)."]");
//            return self::_errRet ( $errno );
        }
        Service_Mall_Mall::_addPropsToUcenter($intUserId, 1);

        $errno = Tieba_Errcode::ERR_SUCCESS;
        Bingo_Log::pushNotice("errno",$errno);
        $arrOutput = array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg ( $errno ),
            'data' => '',
        );
        return $arrOutput;
    }

    public static function getRandomNameplate($arrInput){

        if (!isset($arrInput['status'])  || !isset($arrInput['page_size']) ||
            (intval($arrInput['status']) != self::NAMEPLATE_ALL_STATUS && intval($arrInput['status']) != self::NAMEPLATE_UNSOLD_STATUS && intval($arrInput['status']) != self::NAMEPLATE_SOLD_STATUS) ||
            intval($arrInput['page_size']) < 0 || intval($arrInput['page_size'] >= 150)){
            //3: 个性铭牌未被购买 4: 个性铭牌已被购买' 0:代表上述两种状态都要
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        $intStatus = intval($arrInput['status']);
        $intPageSize = intval($arrInput['page_size']);
        if(false === ($intMaxId = self::_getNameplateMaxId($intStatus))){
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        Bingo_Log::debug("getRandomNameplate max_id : $intMaxId");
        $intBeginId = rand(self::MIN_NAMEPLATE_ID, $intMaxId);
        Bingo_Log::debug("getRandomNameplate begin_id : $intBeginId");

        if(false === ($data = self::_getRandNameplate($intBeginId, $intStatus, $intPageSize))){
            Bingo_Log::warning("_getRandNameplate error! begin_id : $intBeginId");
            $data = array();
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        Bingo_Log::pushNotice("errno",$errno);
        $arrOutput = array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg ( $errno ),
            'data' => $data,
        );

        return $arrOutput;
    }

    public static function searchNameplate($arrInput){

        if (!isset($arrInput['keyword'])  || !isset($arrInput['page_no']) || !isset($arrInput['page_size'])){
            //3: 个性铭牌未被购买 4: 个性铭牌已被购买' 0:代表上述两种状态都要
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        $data = array();

        $strKeyword = trim($arrInput['keyword']);
        $intStatus = intval($arrInput['status']);
        $intPageNo = intval($arrInput['page_no']);
        $intPageSize = intval($arrInput['page_size']);

        //todo : 实现模糊搜索接口
        if(false === ($arrOutput = Service_Libs_Se::selectData($intStatus, $strKeyword, $intPageNo, $intPageSize))){
            $errno = Tieba_Errcode::ERR_DL_CALL_FAIL;
            return self::_errRet($errno);
        }

        $arrNameplate = array();
        if(count($arrOutput['rows']) > 0){
//            $arrOutput has this format
//            array (
//                'all_num' => 1,
//                'page_no' => 0,
//                'list_num' => 20,
//                'rows' =>
//                    array (
//                        0 =>
//                            array (
//                                'id' => 100,
//                                'title' => '土豪的朋友',
//                                '_del_flag' => 0,
//                                '_weight' => 0,
//                                'attr' => 1,
//                            ),
//                    ),
//                'err_no' => 0,
//            )

            foreach($arrOutput['rows'] as $value){
                $arrNameplate[] = array(
                    'props_id'        => intval($value['id']),
                    'title'           => $value['title'],
                );
            }
            if(count($arrNameplate) > 0){
                $arrNameplate = self::_replenishNameplateExpiredTime($arrNameplate);
            }
        }

        $data = array(
            'pt' => ceil(floatval($arrOutput['all_num']) / floatval($arrOutput['list_num'])) ,
            'pn' => intval($arrOutput['page_no']),
            'nameplate' => $arrNameplate,
        );

        $errno = Tieba_Errcode::ERR_SUCCESS;
        Bingo_Log::pushNotice("errno",$errno);
        $arrOutput = array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg ( $errno ),
            'data' => $data,
        );

        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array|bool
     */
    public static function add_se_nameplate($arrInput){
        $errno = Tieba_Errcode::ERR_SUCCESS;
        $intPropsId = $arrInput['props_id'];
        $stringTitle = $arrInput['title'];
        $intEndTime = $arrInput['end_time'];
        if(false === ($arrOutput = Service_Libs_Se::insertData($intPropsId, $stringTitle, $intEndTime))){
            Bingo_Log::warning('Service_Libs_Se::insertData error!');
            $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        }

        Bingo_Log::pushNotice("errno",$errno);
        $arrOutput = array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg ( $errno ),
            'data' => array(),
        );

        return $arrOutput;
    }

    public static function mod_se_nameplate($arrInput){

        $errno = Tieba_Errcode::ERR_SUCCESS;

        if(false === ($arrOutput = Service_Libs_Se::updateData(
                array(intval($arrInput['props_id'])),
                intval($arrInput['end_time'])
            ))){
            Bingo_Log::warning('Service_Libs_Se::updateData error!');
            $errno = Tieba_Errcode::ERR_UNKOWN;
        }

        Bingo_Log::pushNotice("errno",$errno);
        $arrOutput = array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg ( $errno ),
            'data' => array(),
        );

        return $arrOutput;

    }

    public static function find_omit_nameplate($arrInput){

//        $arrInput = array(
//            0 => array(
//                'title' => '',
//                'props_id' => ,
//            ),
//            ...
//        );

        $arrOmit = self::_findOmitNamplateByTitle($arrInput);

        $data = $arrOmit;

        $errno = Tieba_Errcode::ERR_SUCCESS;
        Bingo_Log::pushNotice("errno",$errno);
        $arrOutput = array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg ( $errno ),
            'data' => $data,
        );

        return $arrOutput;

    }

    public static function findOmitNameplate($arrInput){
        $data = array();
        $intNameplateCategory = Service_Libs_Define::PROPS_CATEGORY_NAMEPLATE;

        if(null === ($db = Service_Libs_Db::getDB())){
            $errno = Tieba_Errcode::ERR_DL_CALL_FAIL;
            return self::_errRet($errno);
        }
        $sql = "SELECT COUNT(props_id) FROM props WHERE props_category = $intNameplateCategory";
        if(false == ($data = $db->query($sql))){
            $errno = Tieba_Errcode::ERR_DL_CALL_FAIL;
            return self::_errRet($errno);
        }
        $intTotal = intval($data[0]['COUNT(props_id)']);
        $intPageSize = 100;

        $arrOmit = array();
        for($i = 0; $i < $intTotal; $i += $intPageSize){
            $j = $i + $intPageSize;
            $sql = "SELECT props_id, title FROM props WHERE props_category = $intNameplateCategory LIMIT $i, $j";
            if(false == ($data = $db->query($sql))){
                $errno = Tieba_Errcode::ERR_DL_CALL_FAIL;
                return self::_errRet($errno);
            }

            $arrOmit[] = self::_findOmitNamplateByTitle($data);
        }

        $data = $arrOmit;

        $errno = Tieba_Errcode::ERR_SUCCESS;
        Bingo_Log::pushNotice("errno",$errno);
        $arrOutput = array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg ( $errno ),
            'data' => $data,
        );

        return $arrOutput;

    }

    public static function identifyNameplate($arrInput){

        if( !isset($arrInput['props_id'])  || !isset($arrInput['new_title']) || !isset($arrInput['user_id']) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        $intPropsId = intval($arrInput['props_id']);
        $stringNewTitle = trim($arrInput['new_title']);
        $intUserId = intval($arrInput['user_id']);
        $intNowTime = intval(Bingo_Timer::getNowTime());
        if($intUserId <= 0 ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        if(count(self::_mbStringToArray($arrInput['new_title'])) > 6){//铭牌字符个数 < 6
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        //to_do : UEG敏感词过滤接口
        $arrParams = array(
            'req' => array(
                'confilter_type' => 'forum_confilter',
                '(raw)rawdata' => $stringNewTitle . '\0',
                'title' => $stringNewTitle,
                'detail_len' => strlen($stringNewTitle),
            ),
        );
        $arrOutput = Tieba_Service::call('anti', 'antiConfilter', $arrParams,  NULL, NULL, 'post', 'php', 'utf-8');
        if(220015 == $arrOutput['errno']){//这个错误号是命中ueg严策略
            Bingo_Log::warning ( "the new title not pass ueg confilter! [" . serialize ( $arrParams ) . "]" );
//            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
            return self::_errRet ( Tieba_Errcode::ERR_NAMAPLATE_UEG_FORBIDDEN );
        }

        //过新词表数据
        $arrParams = array(
            'req'=>array(
                'confilter_type'=>'Confilter',
                'reqs'=>array(
                    array(
                        'groupid'=>-1,
                        'content'=>$stringNewTitle,
                        'return_position'=>'no',
                        'no_normalize'=>'yes',
                        'dictlist'=>'confilter_mis_tbmall_nameplate'
                    ),
                ),
            ),
        );
        $arrOutput = Tieba_Service::call('anti', 'antiConfilter', $arrParams,  NULL, NULL, 'post', 'php', 'gbk');
        if(false == $arrOutput ||
            Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno'] ||
            $arrOutput['res']['ans'][0]->count > 0 ||
            !isset($arrOutput['res']['ans'])){//严策略
            Bingo_Log::warning ( "the new title not pass ueg confilter! [" . serialize ( $arrParams ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_NAMAPLATE_UEG_FORBIDDEN );
        }


        //查看新铭牌是否已经存在
        if(flase === ($arrNameplateInfo = self::_getNamePlateByTitle($stringNewTitle))){
            Bingo_Log::warning("_getNamePlateByTitle error! input[" . serialize ( $stringNewTitle) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }
        if(NULL !== $arrNameplateInfo){
            Bingo_Log::warning("new_title is already exist! input[" . serialize ( $stringNewTitle) . "]" );
            if($arrNameplateInfo['end_time'] > $intNowTime){
            return self::_errRet(Tieba_Errcode::ERR_NAMAPLATE_ALREADY_USE);
//                return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
            }
            return self::_errRet(Tieba_Errcode::ERR_NAMAPLATE_ALREADY_EXIST);
//            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }
        //查看需要更改的铭牌是否属于这个人
        if(flase === ($arrNameplateInfo = self::_getNamePlateFromUserpropsByUserId($intUserId))){
            Bingo_Log::warning("__getNamePlateFromUserpropsByUserId error! input[" . serialize ( $stringNewTitle) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }
        if(NULL === $arrNameplateInfo || $intPropsId != intval($arrNameplateInfo['props_id']) ||
            $intNowTime > intval($arrNameplateInfo['end_time'])){
            Bingo_Log::warning("this user not have this nameplate! input[" . serialize ( $arrInput) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }
        //根据props_id查看此铭牌是否符合可选词集和可更改字的要求
        if(flase === ($arrNameplateInfo = self::_getNamePlateById($intPropsId))){
            Bingo_Log::warning("_getNamePlateById error! input[" . serialize ( $stringNewTitle) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }
        //检查铭牌是否符合要求
        $no = 0;
        if(false === self::_checkNameplate($arrNameplateInfo, 0, $stringNewTitle, $no)){
            if($no !== 0){
                Bingo_Log::warning("_checkNameplate error! input[" . serialize ( $stringNewTitle) . "]" );
                return self::_errRet(Tieba_Errcode::ERR_NAMAPLATE_NOT_ALLOW);
            }
            Bingo_Log::warning("_checkNameplate error! input[" . serialize ( $stringNewTitle) . "]" );
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        Bingo_Log::pushNotice("errno",$errno);
        $arrOutput = array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg ( $errno ),
            'data' => $arrNameplateInfo,
        );

        return $arrOutput;
    }

    public static function getUniqueNameplate($arrInput){

        if( !isset($arrInput['title']) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        $arrNameplate = $arrInput['title'];
        $arrInput = array(
            'table_name' => self::TABLE_NAMEPALTE,
            'fields' => "title",
            'conds' => "title in (" . self::_genSQLInConds($arrNameplate, true) . ")",
            'appends' => NULL,
        );
        if(false === ($arrNameplateInfo = self::_selectCommon($arrInput))){
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }
        $arrRepeatNameplate = array();
        foreach($arrNameplateInfo as $_v){
            $arrRepeatNameplate[] = $_v['title'];
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        Bingo_Log::pushNotice("errno",$errno);
        $arrOutput = array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $arrRepeatNameplate,
        );

        return $arrOutput;
    }

    public static function addNameplateDir($arrInput){
        if( !isset($arrInput['sql']) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        $sql = $arrInput['sql'];

        if( null === ($db = Service_Libs_Db::getDB()) ){
            Bingo_Log::warning("get db fail!");
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }

        $result = $db->query($sql);

        if(false === $result){
            Bingo_Log::warning("addNameplateDir db error! [input:".serialize($arrInput)."] [output:".serialize($db->error())."]");
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        Bingo_Log::pushNotice("errno",$errno);
        $arrOutput = array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $result,
        );

        return $arrOutput;
    }


    public static function useNameplateRecastCard($arrInput){
        if( !isset($arrInput['props_id'])  || !isset($arrInput['new_title']) || !isset($arrInput['user_id']) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        $intPropsId = intval($arrInput['props_id']);
        $stringNewTitle = trim($arrInput['new_title']);
        $intUserId = intval($arrInput['user_id']);
        if($intUserId <= 0 ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        $arrOutput = self::identifyNameplate($arrInput);
        if(!isset($arrOutput['errno']) || Tieba_Errcode::ERR_SUCCESS !== intval($arrOutput['errno']) ){//中间某个出现失败
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }
        $arrNameplateInfo = $arrOutput['data'];
        $intEndTime = intval($arrNameplateInfo['end_time']);

        if( null === ($db = Service_Libs_Db::getDB()) ){
            Bingo_Log::warning("get db fail!");
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }

        //开始事务
        $db->startTransaction();
        $errno = Tieba_Errcode::ERR_SUCCESS;
        do{
            //首先锁用户积分记录，保证不出现死锁
            $arrReduceRet = Service_Mall_Mall::reduceUserScores($intUserId, 0, 1);
            if (Tieba_Errcode::ERR_SUCCESS !== $arrReduceRet['errno']){
                $error = $arrReduceRet['errno'];
                Bingo_Log::warning("reduceUserScores error user_id:$intUserId props_id:$intPropsId");
                break;
            }
            //减掉一个重铸卡
            $intRecastPropsId = 1140001;
            $sql = "update user_props set left_num = left_num - 1 where user_id = $intUserId and left_num > 0 and props_id = $intRecastPropsId";
            if(false === $db->query($sql)){
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                Bingo_Log::warning("reduce recaset props error! [input:".serialize($arrInput)."] [output:".serialize($db->error())."]");
                break;
            }
            if(0 === intval($db->__get("affectedRows"))){
                //如果影响的行数小于1证明这个人没买道具或者剩余不足了
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                Bingo_Log::warning("reduce recaset props error! [input:".serialize($arrInput)."] [output:".serialize($db->error())."]");
                break;
            }
            //更新user_props中的title
            $intNowTime = intval(Bingo_Timer::getNowTime());
            $sql = "update user_props set ext = '$stringNewTitle', update_time = $intNowTime where user_id = $intUserId and props_id = $intPropsId";
            if(false === $db->query($sql)){
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                Bingo_Log::warning("reduce recaset props error! [input:".serialize($arrInput)."] [output:".serialize($db->error())."]");
                break;
            }
            if(0 === intval($db->__get("affectedRows"))){
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                Bingo_Log::warning("reduce recaset props error! [input:".serialize($arrInput)."] [output:".serialize($db->error())."]");
                break;
            }
            $db->commit();
        }while(0);

        if(Tieba_Errcode::ERR_SUCCESS != $errno){
            $db->rollback();
        }
        else{
            //由于ddbs不支持分布式事务, 更新nameplate的操作抽到事务外, 更新nameplate中的title
            $sql = "update nameplate set title = '$stringNewTitle' where props_id = $intPropsId";
            if(false === $db->query($sql)){
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                Bingo_Log::warning("reduce recaset props error! [input:".serialize($arrInput)."] [output:".serialize($db->error())."]");
            }
            if(0 === intval($db->__get("affectedRows"))){
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                Bingo_Log::warning("reduce recaset props error! [input:".serialize($arrInput)."] [output:".serialize($db->error())."]");
            }
            //更新se(insert)的title
            if(false === ($arrOutput = Service_Libs_Se::insertData($intPropsId, $stringNewTitle, $intEndTime))){
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
            }
            Service_Mall_Mall::_addPropsToUcenter($intUserId, 1);
        }

//ispv
// 0
//
//recasted_name
// 铭牌重铸之后的名称
//
//custom_name
// 自定义部分名称，如果修改即可上传
//
//optional_name
// 可选部分名称，如果没有修改上传为空值
//
//props_id
// 被重铸的铭牌ID
//
//recast_type
// prefix/subfix 修改的是前缀或是后缀
        Bingo_Log::pushNotice("errno", $errno);
        Bingo_Log::pushNotice("ispv", 0);
        Bingo_Log::pushNotice("recasted_name", $stringNewTitle);
        Bingo_Log::pushNotice("props_id", $intPropsId);
        $arrTitle = self::_mbStringToArray($stringNewTitle);
        $arrPattern = $arrNameplateInfo['pattern'];
        $strOptionalWorld = "";
        $strUserWorld = "";
        $boolChangePrefix = false;
        if(is_array($arrPattern)){
            $intCnt = count($arrTitle);
            for($i = 0; $i < $intCnt; ++$i){
                if($i == 0 && $arrPattern[$i] == 3){
                    $boolChangePrefix = true;
                }
                if($arrPattern[$i] == 2){
                    $strOptionalWorld .= $arrPattern[$i];
                }
                else if($arrPattern[$i] == 3){
                    $strUserWorld .= $arrPattern[$i];
                }
            }
        }
        Bingo_Log::pushNotice("custom_name", $strUserWorld);
        Bingo_Log::pushNotice("optional_name", $strOptionalWorld);
        Bingo_Log::pushNotice("recast_type", $boolChangePrefix);

        Service_Libs_Base::noticeStLog('', $intUserId);

        $arrOutput = array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => '',
        );

        return $arrOutput;
    }

    //调整表结构, 迁移数据时使用
    public static function moveFromPropsToNameplate($arrInput){

        if( null === ($db = Service_Libs_Db::getDB()) ){
            Bingo_Log::warning("get db fail!");
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }

        $sql = "select count(props_id) from props where props_category = 112";
        if(false === ($ret = $db->query($sql))){
            $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
            Bingo_Log::warning("reduce recaset props error! [input:".serialize($arrInput)."] [output:".serialize($db->error())."]");
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }
        $intTotal = intval($ret[0]['count(props_id)']);
        $intPageSize = 100;
        $intCnt = 0;
        $intNow = Bingo_Timer::getNowTime();
        while($intCnt < $intTotal){
            $sql = "select * from props where props_category = 112 order by props_id limit $intCnt, $intPageSize";
            $intCnt += $intPageSize;
            if(false === ($ret = $db->query($sql))){
                $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                Bingo_Log::warning("reduce recaset props error! [input:".serialize($arrInput)."] [output:".serialize($db->error())."]");
                break;
            }
            foreach($ret as $arrNameplate){
                $intPropsId = $arrNameplate['props_id'];
                $sql = "select * from user_props where props_category = 112 and props_id = $intPropsId and end_time > $intNow";
//                if(false === ($arrUserNameplate = $db->query($sql))){
//                    $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
//                    Bingo_Log::warning("reduce recaset props error! [input:".serialize($arrInput)."] [output:".serialize($db->error())."]");
//                    break;
//                }
                $filed = self::_genNameplateTableInfo($arrNameplate, $arrUserNameplate[0]);
                if(false === $db->insert('nameplate', $filed)){
//                    $errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
//                    Bingo_Log::warning("reduce recaset props error! [input:".serialize($arrInput)."] [output:".serialize($db->error())."]");
//                    break;
                }
            }
        }

        Bingo_Log::pushNotice("errno",$errno);
        $arrOutput = array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => '',
        );

        return $arrOutput;
    }

    public static function moveDataForUidAndEndtime($arrInput){
        if( null === ($db = Service_Libs_Db::getDB()) ){
            Bingo_Log::warning("get db fail!");
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }
        $sql = $arrInput['sql'];
        $intUserId = intval($arrInput['user_id']);
        if(false === ($ret = $db->query($sql))){
            Bingo_Log::warning("moveDataForUidAndEndtime error! [input:".serialize($arrInput)."] [output:".serialize($db->error())."]");
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
        }
        if($intUserId > 0){
            Service_Mall_Mall::_addPropsToUcenter($intUserId, 1);
        }
        else{
            Bingo_Log::warning("user_id : $intUserId _addPropsToUcenter error!");
        }
        $errno = Tieba_Errcode::ERR_SUCCESS;
        Bingo_Log::pushNotice("errno",$errno);
        $arrOutput = array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => '',
        );
        return $arrOutput;
    }
    /* nameplate end */

    /**
     * @brief : 获得一个道具的信息
     * @param [in]  props_id    :  uint32_t    :   输入参数 道具id
     **/
    public static function getNameplateProps($props_id,$str_append = NULL) {
        if( null === ($db = Service_Libs_Db::getDB()) ){
            Bingo_Log::warning("get db fail.");
            return false;
        }
        $str_cond = 'props_id='.$props_id.' and status = '. Service_Libs_Define::PROPS_OK;
        $arr_fields = array('props_id','props_type','props_category','position','purchase_type','create_time','end_time','update_time','user_id','title','scores','time_interval','free_user_level','status','first_dir','second_dir','optional_word','pattern');
        //$tmp  = $db->query('set names utf8');
        $arr_ret = $db->select(self::TABLE_NAMEPALTE, $arr_fields, $str_cond, NULL, $str_append);
        if(false === $arr_ret){
            Bingo_Log::warning("getNameplateProps error! sql:".$db->getLastSQL()."[output:".serialize($db->error())."]");
            return false;
        }
        return is_array($arr_ret[0]) ? $arr_ret[0] : array();

    }

    /**
     * @brief 封装下掉铭牌的接口  rd：gaoweizhen
     * @param arrInput
     * @return arrOutput
     **/
    public static function delMulNameplate($arrInput){
        if(!empty($arrInput['title'])) {
            // if (!isset($arrInput['title'])){
            //     Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            //     return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
            // }
            $stringTitle = $arrInput['title'];
            //根据铭牌名查询铭牌所对应的铭牌id 
            if(flase === ($arrNameplateInfo = self::_getNamePlateByTitle($stringTitle))){
                Bingo_Log::warning("_getNamePlateByTitle error! input[" . serialize ( $stringTitle) . "]" );
                return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
            }
            $intPropsId = intval($arrNameplateInfo['props_id']);
            $intUserId = intval($arrInput['user_id']);
            
            if(null == $arrNameplateInfo || $intPropsId <= 0 || $intUserId <= 0){
                Bingo_Log::warning("the nameplate with this title not exist! input[" . serialize( $stringTitle) . "]" );
                return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
            }
        }else {   
            if (!isset($arrInput['props_id'])){
                Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
                return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
            }
            $intPropsId = $arrInput['props_id'];
            $intUserId = intval($arrInput['user_id']);
        }
        $intTime = Bingo_Timer::getNowTime();
        $sql_props = "DELETE FROM nameplate WHERE props_id = $intPropsId";
        $sql_userprops = "DELETE FROM user_props WHERE user_id = $intUserId AND props_id = $intPropsId and end_time > $intTime";
        if( null === ($db = Service_Libs_Db::getDB()) ){
            Bingo_Log::warning("get db fail!");
            return false;
        }
        $result = $db->query($sql_props);
        if(false === $result){
            $errno = Tieba_Errcode::ERR_UNKOWN;
            Bingo_Log::warning("_delete  nameplate db error! [input:".serialize($arrInput)."][output:".serialize($db->error())."]");
        }    
        $result = $db->query($sql_userprops);
        if(false === $result){
            $errno = Tieba_Errcode::ERR_UNKOWN;
            Bingo_Log::warning("_delete user_props db error! [input:".serialize($arrInput)."][output:".serialize($db->error())."]");
        }
        /*if(false === ($arrOutput = Service_Libs_Se::deleteData(array($intPropsId)))){
            $errno = Tieba_Errcode::ERR_UNKOWN;
            Bingo_Log::warning("se::deleteData error! input : [".serialize($intPropsId)."]");
        }*/
        $arrInput = array(
            'props_id' => $intPropsId,
        );
        $arrSeOutput = self::delSeNameplate($arrInput);
        if($arrSeOutput == false || $arrSeOutput['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("se::deleteData error! input : [".serialize($intPropsId)."]output: [".serialize($arrSeOutput)."]");
        }
        $arrInput = array(
            'user_id' => $intUserId,
        );
        self::_synPropsToUserAttr($arrInput);
        $errno = Tieba_Errcode::ERR_SUCCESS;
        Bingo_Log::pushNotice("errno",$errno);
        $arrOutput = array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg ( $errno ),
            'data' => '',
        );
        return $arrOutput;
    }
    
    /**
     * @brief 铭牌同步用户属性接口 rd：gaoweizhen
     * @param arrInput
     * @return arrOutput
     **/
    protected static function _synPropsToUserAttr($arrInput){
        if(empty($arrInput['user_id'])){
            Bingo_Log::warning("input param invalid. [" . serialize($arrInput)."]");
        }
        $intUserId = $arrInput['user_id'];
        $arrParams = array(
            'user_id' => $intUserId,
        );
        $arrOutput = Tieba_Service::call('user', 'getUserData', $arrParams,null,null,'post','php','utf-8');
        if($arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS || false === $arrOutput) {
            Bingo_Log::warning("call func getUserData failed,input:". serialize($arrParams)."output". serialize($arrOutput));
         //   return $arrOutput;
        }
        $mParr_props = $arrOutput['user_info'][0]['mParr_props'];
        if(!empty($mParr_props[112])){
            unset($mParr_props[112]);
        }
        $arrRet = Service_Libs_Userinfo::setUserMpropsInfo($intUserId, $mParr_props);
        if(false === $arrRet){
            Bingo_Log::warning("call setUserAttrByArray error.inuput=[".serialize($arrUserInput)."]output=[".serialize($arrOut)."]");
        }
        return true;
    }

}
	
