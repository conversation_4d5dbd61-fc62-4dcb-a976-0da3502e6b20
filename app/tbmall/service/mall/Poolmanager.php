<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date Wed Dec 24 11:48:57 CST 2014
*/


class Service_Mall_Poolmanager{

	protected static $_db = null;

	/**
		*
		*	初始化数据库
		* 	@param	object	: db
		* 	@return	bool
		*
	 */
	protected static function _initDB($db) {
		self::$_db = $db;
		if (!($db instanceof Bd_DB)
			|| !$db->isConnected()) {
			Bingo_Log::warning("db init failed.");
			return false;
		}
		return true;
	}

	/**
		*
		*	获得数据库句柄
		* 	@param	null
		* 	@return	object
		*
	 */
	protected static function _getDB() {
		return self::$_db;
	}
	
	/**
		*
		*	参数检测
		* 	@param	array	: arrInput
		* 	@return	bool	
		*
	 */
	private static function _checkParam($arrInput,$arrCheckParam,$positive=false){
        if($positive){
            foreach ($arrCheckParam as $key => $value){
                if(0 >= $arrInput[$value]){
                    Bingo_Log::warning("[$value] is wrong: [".$value.'].');
                    return false;
                }   
            }   
        }   
        else{
            foreach ($arrCheckParam as $key => $value){
                if(empty($arrInput[$value])){
                    Bingo_Log::warning("[$value] is empty: [".$value.'].');
                    return false;
                }   
            }   
        }   
                                                                                   
        return true;
	}

	/**
		*
		*	返回函数值
		* 	@param	int	: errno
		* 			array	: data
		* 			string	: data key
		* 	@return	array
		*
	 */
    protected static function _errRet($errno,$data = "",$data_key = "data"){
        $errmsg = Tieba_Error::getErrmsg($errno);
        $arrRet = array(
			'errno' => $errno,
			'errmsg' => $errmsg,
        );  
        if($data !== ""){
            $arrRet[$data_key] = $data;
        }   
        Bingo_Log::pushNotice("errno",$errno);
        return $arrRet;
    }   

	/**
		*
		*	池子操作
		* 	@param	array : arrPoolInfo
		* 	@return	array(
		* 		'pool_list' => array(
		* 		),
		* 		'pool_manager' => array(
		* 		),
		* 	)
		*
	 */
	private static function _operatePool($db,$arrInput){
		$arrPoolManager	= $arrInput['manager'];
		$arrPoolList	= $arrInput['pool_list'];

		$intPoolNumNow	= count($arrPoolList);
		$intSubNum	= $arrPoolManager['sub_num'];
		$intPid	= $arrPoolManager['pid'];

		//获得总T豆，money other豆
		$intTotalTdou	= 0;
		$intTotalMoney	= 0;
		$intTotalOther	= 0;
		foreach($arrPoolList as $arrSubPoolInfo){
			$intTotalTdou	+= $arrSubPoolInfo['scores_total'];
			$intTotalMoney	+= $arrSubPoolInfo['scores_money'];
			$intTotalOther	+= $arrSubPoolInfo['scores_other'];
		}
		$intTdouCost	= 0;
		$intMoneyCost	= 0;
		$intOtherCost	= 0;

		$intPoolTdou	= $arrPoolManager['max_tdou_num'];
		$intPoolMoney	= intval($intPoolTdou*$intTotalMoney/$intTotalTdou);
		$intPoolOther	= $intPoolTdou - $intPoolMoney;

		//当已经分过的子池数 大于等于要分的子池数时，都是update操作
		if($intPoolNumNow >= $intSubNum){

			for($i = 0;$i < $intPoolNumNow;$i++){
				if($i == $intSubNum - 1){
					$intPoolTdou	= $intTotalTdou - $intTdouCost;
					$intPoolMoney	= $intTotalMoney - $intMoneyCost;
					$intPoolOther	= $intTotalOther - $intOtherCost;
				}
				if($i >= $intSubNum){
					$intPoolTdou	= 0;
					$intPoolMoney	= 0;
					$intPoolOther	= 0;
				}
				$intTdouCost	+= $intPoolTdou;
				$intMoneyCost	+= $intPoolMoney;
				$intOtherCost	+= $intPoolOther;
				$arrParam	= array(
					'scores_total'	=> $intPoolTdou,
					'scores_money'	=> $intPoolMoney,
					'scores_other'	=> $intPoolOther,
					'pid'	=> $intPid,
					'sub_pid'	=> $i,
					'update_time'	=> time(),
				);
				$arrRet	= Service_Mall_Tdpool::updatePoolFields($arrParam,$db);
				if(Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
					Bingo_Log::warning('update pool fields failed.input:['.serialize($arrParam).'].output:['.serialize($arrRet).'].');
					return $arrRet;
				}
			}
		}
		else{	//已存在的子池数小于要分配的子池数的话，则要将有的部分update 没有的部分insert
			for($i = 0;$i < $intSubNum; $i++){
				if($i == $intSubNum - 1){	//最后一次将剩下的T豆、充值豆、活动豆都给最后一个池子
					$intPoolTdou	= $intTotalTdou - $intTdouCost;
					$intPoolMoney	= $intTotalMoney - $intMoneyCost;
					$intPoolOther	= $intTotalOther - $intOtherCost;
				}
				$intTdouCost	+= $intPoolTdou;
				$intMoneyCost	+= $intPoolMoney;
				$intOtherCost	+= $intPoolOther;

				if($i >= $intPoolNumNow){	//多余部分要insert
					$arrParam	= array(
						'scores_total'	=> $intPoolTdou,
						'scores_money'	=> $intPoolMoney,
						'scores_other'	=> $intPoolOther,
						'pid'	=> $intPid,
						'sub_pid'	=> $i,
					);
					unset($arrPoolList[0]['id']);
					$arrParam	= array_merge($arrPoolList[0],$arrParam);
					$arrRet	= Service_Mall_Tdpool::insertPoolFields($arrParam);
					if(Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
						Bingo_Log::warning('insert pool fields failed.input:['.serialize($arrParam).'].output:['.serialize($arrRet).'].');
						return $arrRet;
					}
				}
				else{	//已经有的子池要进行update
					$arrParam	= array(
						'scores_total'	=> $intPoolTdou,
						'scores_money'	=> $intPoolMoney,
						'scores_other'	=> $intPoolOther,
						'pid'	=> $intPid,
						'sub_pid'	=> $i,
						'update_time'	=> time(),
					);
					$arrRet	= Service_Mall_Tdpool::updatePoolFields($arrParam);
					if(Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
						Bingo_Log::warning('update pool fields failed.input:['.serialize($arrParam).'].output:['.serialize($arrRet).'].');
						return $arrRet;
					}
				}
			}
		}
		//验证T豆是否都分配完
		if($intTdouCost != $intTotalTdou || $intMoneyCost != $intTotalMoney || $intOtherCost != $intTotalOther){
			Bingo_Log::warning('the tdou is not match.intTdouCost:['.$intTdouCost.'].intTotalTdou:['.$intTotalTdou.'].intMoneyCost:['.$intMoneyCost.'].intTotalMoney:['.$intTotalMoney.'].intOtherCost:['.$intOtherCost.'].intTotalOther:['.$intTotalOther.'].');
			return self::_errRet(Tieba_Errcode::ERR_SCORES_NOT_MEET_REQUIREMENT,'tdou not match',__FUNCTION__);
		}
		Bingo_Log::pushNotice('pool_manager',serialize($arrPoolManager));
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrPoolManager);
	}
	/**
		*
		*	推荐池子
		* 	@param	array : transfer input
		* 			int	  : pattern
		* 	@return	array : transfer input{
		* 	}
		*
	 */
	public static function poolManager($arrInput,$pattern){
		//判断各方哪个是池子类型	扩展性后面可能有多方交易
		$arrFromAccount	= $arrInput['from'];
		$arrToAccount	= $arrInput['to'];
		$intPattern		= $pattern;

		//from方向的账户，如果是池子则按金额大的几率大来hash
		foreach($arrFromAccount as &$arrAccountInfo){
			if(Service_Mall_Mallconf::ACCOUNT_TYPE_POOL != $arrAccountInfo['account_type'] || $arrInput['scene_id'] != $arrAccountInfo['account_id']){
				Bingo_Log::warning('this is not a spilit pool.input:['.serialize($arrAccountInfo).'].');
				continue;
			}
			//查询池子状态
			$intPid	= intval($arrAccountInfo['account_id']);
			if(0 >= $intPid){
				Bingo_Log::warning('the scene_id is error.scene_id:['.$intPid.'].');
				return $arrInput;
			}
			//init
			if(null == ($db = Service_Libs_Db::getDB())){
				Bingo_Log::warning('get db failed.');
				return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
			}
			$arrParam	= array(
				'pid'	=> $intPid,
			);
			$arrRet	= self::getPoolInfo($db,$arrParam);
			if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
				Bingo_Log::warning('get pool info failed.input:['.serialize($arrParam).'].output:['.serialize($arrRet).'].');
				return $arrInput;
			}
			$arrPoolInfo	= $arrRet['data'];
			$arrPoolManager	= $arrPoolInfo['manager'];
			$arrPoolList	= $arrPoolInfo['pool_list'];

			$intPoolNumNow	= count($arrPoolList);
			$intPoolNumList	= $arrPoolManager['sub_num'];
			if($intPoolNumNow != $intPoolNumList){
				Bingo_Log::warning('manager pool num is not match.pool list num:['.$intPoolNumNow).'].pool manager num:['.$intPoolNumList.'].';
				return $arrInput;
			}
			//hash
			//get all tdou num
			$intTotalTdou	= 0;
			foreach($arrPoolList as $arrSubPoolInfo){
				$intTotalTdou	+= $arrSubPoolInfo['scores_total'];
			}
			//get hash num
			$intHashNum	= rand(0,$intTotalTdou);
			$intSubPoolId	= 0;
			foreach($arrPoolList as $arrSubPoolInfo){
				$intHashNum	-= $arrSubPoolInfo['scores_total'];
				if($intHashNum < 0){
					$intSubPoolId	= $arrSubPoolInfo['sub_pid'];
					break;
				}
			}
			//将hash到的子池的id赋值给开启分流策略的子池
			$arrAccountInfo['sub_pid']	= $intSubPoolId;
			Bingo_Log::pushNotice('sub_pid',$intSubPoolId);
			Bingo_Log::pushNotice('split_pool_info',$arrAccountInfo);
		}

		//to方向的账户,如果是池子的话，则有金额越大几率越小
		foreach($arrToAccount as &$arrAccountInfo){
			if(Service_Mall_Mallconf::ACCOUNT_TYPE_POOL != $arrAccountInfo['account_type'] || $arrInput['scene_id'] != $arrAccountInfo['account_id']){
				Bingo_Log::warning('this is not a split pool.input:['.serialize($arrAccountInfo).'].');
				continue;
			}
			//查询池子状态
			$intPid	= intval($arrAccountInfo['account_id']);
			if(0 >= $intPid){
				Bingo_Log::warning('the scene_id is error.scene_id:['.$intPid.'].');
				return $arrInput;
			}
			//init
			if(null == ($db = Service_Libs_Db::getDB())){
				Bingo_Log::warning('get db failed.');
				return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
			}
			$arrParam	= array(
				'pid'	=> $intPid,
			);
			$arrRet	= self::getPoolInfo($db,$arrParam);
			if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
				Bingo_Log::warning('get pool info failed.input:['.serialize($arrParam).'].output:['.serialize($arrRet).'].');
				return $arrInput;
			}
			$arrPoolInfo	= $arrRet['data'];
			$arrPoolManager	= $arrPoolInfo['manager'];
			$arrPoolList	= $arrPoolInfo['pool_list'];

			$intPoolNumNow	= count($arrPoolList);
			$intPoolNumList	= $arrPoolManager['sub_num'];
			if($intPoolNumNow != $intPoolNumList){
				Bingo_Log::warning('manager pool num is not match.pool list num:['.$intPoolNumNow).'].pool manager num:['.$intPoolNumList.'].';
				return $arrInput;
			}
			//hash
			//get all tdou num
			$intTotalTdou	= 0;
			foreach($arrPoolList as $arrSubPoolInfo){
				$intTotalTdou	+= $arrSubPoolInfo['scores_total'];
			}
			$intTotalTemp	= 0;
			foreach($arrPoolList as &$arrSubPoolInfo){
				$arrSubPoolInfo['scores_per']	= $intTotalTdou/$arrSubPoolInfo['scores_total'];
				$intTotalTemp	+= $arrSubPoolInfo['scores_per'];
			}
			$intTotalTdou	= $intTotalTemp;

			//get hash num
			$intHashNum	= rand(0,$intTotalTdou);
			$intSubPoolId	= 0;
			foreach($arrPoolList as $arrSubPoolInfo){
				$intHashNum	-= $arrSubPoolInfo['scores_per'];
				if($intHashNum < 0){
					$intSubPoolId	= $arrSubPoolInfo['sub_pid'];
					break;
				}
			}
			//将hash得到的sub pid赋值给传入的参数中
			$arrAccountInfo['sub_pid']	= $intSubPoolId;
			Bingo_Log::pushNotice('sub_pid',$intSubPoolId);
			Bingo_Log::pushNotice('split_pool_info',$arrAccountInfo);
		}
		$arrInput['from']	= $arrFromAccount;
		$arrInput['to']	= $arrToAccount;
		return $arrInput;
	}

    /**
     *
     * 获得池子信息以及子池的各项参数
     * @param required int : pid   //场景id
     * @param optional bool : trans  //是否需要加锁
     * @return  array   : pool_info
     *
     */
    public static function poolInfo($arrInput){
        //init
        if(null == ($db = Service_Libs_Db::getDB())){
            Bingo_Log::warning('get db failed.');
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $arrRet = self::getPoolInfo($db, $arrInput);
        if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
            Bingo_Log::warning('get pool info failed.input:['.serialize($arrParam).'].output:['.serialize($arrRet)
                .'].');
        }
        return $arrRet;
    }

	/**
		*
		*	获得池子信息包括子池
		* 	@param	int	: pid
		* 	@optional	int : trans
		* 	@return	array	: pool_info
		*
	 */
	public static function  getPoolInfo($db,$arrInput){
		//check param
		$arrCheckParam	= array(
			'pid',
		);
		if(false == self::_checkParam($arrInput,$arrCheckParam,true)){
			Bingo_Log::warning('param is error.input:['.serialize($arrInput).'].');
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, ' input error! ['.serialize($arrInput).']', __FUNCTION__);
		}

		//init
		$intPid	= intval($arrInput['pid']);
		$bolTrans	= $arrInput['trans'];
		if(!self::_initDB($db)){
			Bingo_Log::warning('init db failed.');
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
		}

		//查找数据库操作
		$arrParam	= array(
			'db'	=> self::_getDB(),
			'cond'	=> array(
				'pid'	=> $intPid,
			),
		);
		if(true == $bolTrans){
			$arrParam['append']	= ' FOR UPDATE';
		}

		$arrRet	= Dl_Db_TdouPool::select($arrParam);
		if(Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
			Bingo_Log::warning('get pool info failed.input:['.serialize($arrInput).'].output:['.serialize($arrRet).'].');
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, ' get pool info failed: ['.serialize($arrInput).']', __FUNCTION__);
		}
		$arrPoolList	= $arrRet['data'];

		$arrRet	= Dl_Db_PoolManager::select($arrParam);
		if(Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
			Bingo_Log::warning('get pool info failed.input:['.serialize($arrInput).'].output:['.serialize($arrRet).'].');
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, ' get pool info failed: ['.serialize($arrInput).']', __FUNCTION__);
		}
		$arrPoolManager	= $arrRet['data'][0];

		//拆分没用的子池,当没有开始事务时
		$intSubNum	= $arrPoolManager['sub_num'];
		//子池排序，sub_pid从小到大
		$arrPids	= array();
		foreach($arrPoolList as $arrSubPoolInfo){
			$arrPids[]	= $arrSubPoolInfo['sub_pid'];
		}
		array_multisort($arrPids,SORT_ASC,SORT_NUMERIC,$arrPoolList);
		if(true != $bolTrans){
			$arrPoolList	= array_slice($arrPoolList,0,$intSubNum);
		}
		$arrData	= array(
			'pool_list'	=> $arrPoolList,
			'manager'	=> $arrPoolManager,
		);
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrData);
	}

	/**
		*
		*	分池
		* 	@param	int	: pid
		* 			int : maxPoolNum
		* 			int : minPoolNum
		* 			int : maxTdouNum
		* 			int : minTdouNum
		* 			string	: user_name
		*
		* 	@return
		*
	 */
	public static function splitPool($arrInput){
		//check param
		$arrCheckParam	= array(
			'pid',
			'max_pool_num',
			'max_tdou_num',
		);
		if(false == self::_checkParam($arrInput,$arrCheckParam,true)){
			Bingo_Log::warning('param is error.input:['.serialize($arrInput).'].');
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, ' input error! ['.serialize($arrInput).']', __FUNCTION__);
		}

		//init
		$intPid			= intval($arrInput['pid']);
		$intMaxPoolNum	= intval($arrInput['max_pool_num']);
		$intMinPoolNum	= intval($arrInput['min_pool_num']);
		$intMaxTdouNum	= intval($arrInput['max_tdou_num']);
		$intMinTdouNum	= intval($arrInput['min_tdou_num']);
		$strUserName	= strval($arrInput['user_name']);

		if(null == ($db = Service_Libs_Db::getDB())){
			Bingo_Log::warning('get db failed.');
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}

		//start transaction
		if(false === ($tmp = $db->startTransaction())){
			Bingo_Log::warning('db start transaction failed.');
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,'db start transaction failed.',__FUNCTION__);
		}
		//获得池子状态
		$arrParam	= array(
			'pid'	=> $intPid,
			'trans'	=> true,
		);
		$arrRet	= self::getPoolInfo($db,$arrParam);
		if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
			Bingo_Log::warning('get pool info failed.input:['.serialize($arrInput).'].output:['.serialize($arrRet).'].');
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,'intput:['.serialize($arrParam).'].',__FUNCTION__);
		}
		$arrPoolInfo	= $arrRet['data'];
		$arrPoolList	= $arrPoolInfo['pool_list'];
		$arrPoolManager	= $arrPoolInfo['manager'];
		//获得总T豆
		$intTotalTdou	= 0;
		foreach($arrPoolList as $arrSubPoolInfo){
			$intTotalTdou	+= $arrSubPoolInfo['scores_total'];
		}
		$errno	= Tieba_Errcode::ERR_SUCCESS;

		do{
			//判断是否分过池
			$arrParam	= array(
				'db'	=> $db,
				'field'	=> array(
					'pid'	=> $intPid,
					'max_pool_num'	=> $intMaxPoolNum,
					'min_pool_num'	=> $intMinPoolNum,
					'max_tdou_num'	=> $intMaxTdouNum,
					'min_tdou_num'	=> $intMinTdouNum,
					'sub_num'	=> intval($intTotalTdou/$intMaxTdouNum) == 0 ? 1 : intval($intTotalTdou/$intMaxTdouNum),	//这面可以使max=1000，只有T豆100时，分出1个子池
					'op_time'	=> time(),
					'op'	=> $strUserName,
				),
			);

			$arrPoolInfo['manager'] = $arrParam['field'];
			if(empty($arrPoolManager)){
				//修改manager状态
				$arrRet	= Dl_Db_PoolManager::insert($arrParam);
				if(Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
					Bingo_Log::warning('insert pool info failed.input:['.serialize($arrInput).'].output:['.serialize($arrRet).'].');
					$errno	= Tieba_Errcode::ERR_DB_QUERY_FAIL;
					//return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, ' insert pool info failed: ['.serialize($arrInput).']', __FUNCTION__);
					break;
				}
			}
			else{
				//修改manager状态
				$arrParam['cond']['pid']	= $intPid;
				unset($arrParam['field']['pid']);
				$arrRet	= Dl_Db_PoolManager::update($arrParam);
				if(Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
					Bingo_Log::warning('update pool info failed.input:['.serialize($arrInput).'].output:['.serialize($arrRet).'].');
					$errno	= Tieba_Errcode::ERR_DB_QUERY_FAIL;
					//return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, ' update pool info failed: ['.serialize($arrInput).']', __FUNCTION__);
					break;
				}
			}

			$arrPoolInfo['total_score'] = $intTotalTdou;
			$arrPoolManager = $arrParam['field'];

			//分池操作
			$arrRet	= self::_operatePool($db,$arrPoolInfo);
			if(false == $arrRet	|| Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
				Bingo_Log::warning('db split pool failed.input:['.serialize($arrPoolInfo).'].output:['.serialize($arrRet).'].');
				//return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,'db split pool failed:['.serialize($arrPoolInfo).'].',__FUNCTION__);
				$errno	= Tieba_Errcode::ERR_DB_QUERY_FAIL;
				break;
			}

			//同步修改消费路径状态开关
			$arrParam	= array(
				'pid'	=> $intPid,
				'op'	=> $strUserName,
				'split_logic'	=> Service_Libs_Define::SPLIT_LOGIC_HASHWEIGHT,
			);
			$arrRet	= Service_Consumepath_Consumepath::modifyConsumePath($arrParam);
			if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
				Bingo_Log::warning('modify consumpath info failed.input:['.serialize($arrParam).'].output:['.serialize($arrRet).'].');
				$errno	= Tieba_Errcode::ERR_DB_QUERY_FAIL;
				break;
			}
		}while(0);

		if(Tieba_Errcode::ERR_SUCCESS != $errno){
			$db->rollback();
			Bingo_Log::pushNotice("rollback",1);
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,'db query failed.intput:['.serialize($arrPoolInfo).'].',__FUNCTION__);
		}
		if(false === ($tmp = $db->commit())){
			Bingo_Log::warning("commit db error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows());
			Bingo_Log::pushNotice("commit_err pid:",$intPid);
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,'db commit failed.',__FUNCTION__);
		}

		Bingo_Log::pushNotice("commit pid:",$intPid);
		//返回
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrPoolInfo);
	}


	/**
		*
		*	合池
		* 	@param
		* 	@return
		*
	 */
	public static function mergePool($arrInput){
		//check param
		$arrCheckParam	= array(
			'pid',
		);
		if(false == self::_checkParam($arrInput,$arrCheckParam,true)){
			Bingo_Log::warning('param is error.input:['.serialize($arrInput).'].');
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, ' input error! ['.serialize($arrInput).']', __FUNCTION__);
		}
		//init
		$intPid			= intval($arrInput['pid']);
		$strUserName	= strval($arrInput['user_name']);
		if(null == ($db = Service_Libs_Db::getDB())){
			Bingo_Log::warning('get db failed.');
			return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}

		//start transaction
		if(false === ($tmp = $db->startTransaction())){
			Bingo_Log::warning('db start transaction failed.');
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,'db start transaction failed.',__FUNCTION__);
		}
		//获得池子状态
		$arrParam	= array(
			'pid'	=> $intPid,
			'trans'	=> true,
		);
		$arrRet	= self::getPoolInfo($db,$arrParam);
		if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
			Bingo_Log::warning('get pool info failed.input:['.serialize($arrInput).'].output:['.serialize($arrRet).'].');
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,'intput:['.serialize($arrParam).'].',__FUNCTION__);
		}
		$arrPoolInfo	= $arrRet['data'];
		$arrPoolList	= $arrPoolInfo['pool_list'];
		$arrPoolManager	= $arrPoolInfo['manager'];
		//获得总T豆
		$intTotalTdou	= 0;
		foreach($arrPoolList as $arrSubPoolInfo){
			$intTotalTdou	+= $arrSubPoolInfo['scores_total'];
		}
		$errno	= Tieba_Errcode::ERR_SUCCESS;
		do{
			//判断是否分过池
			$arrParam	= array(
				'db'	=> $db,
				'field'	=> array(
					'pid'	=> $intPid,
					'max_pool_num'	=> 1,
					'min_pool_num'	=> 1,
					'max_tdou_num'	=> $intTotalTdou,
					'min_tdou_num'	=> $intTotalTdou,
					'sub_num'	=> 1,
					'op_time'	=> time(),
					'op'	=> $strUserName,
				),
			);
			$arrPoolInfo['manager'] = $arrParam['field'];
			if(empty($arrPoolManager)){
				//修改manager状态
				$arrRet	= Dl_Db_PoolManager::insert($arrParam);
				if(Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
					Bingo_Log::warning('insert pool info failed.input:['.serialize($arrInput).'].output:['.serialize($arrRet).'].');
					$errno	= Tieba_Errcode::ERR_DB_QUERY_FAIL;
					//return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, ' insert pool info failed: ['.serialize($arrInput).']', __FUNCTION__);
					break;
				}
			}
			else{
				//修改manager状态
				$arrParam['cond']['pid']	= $intPid;
				unset($arrParam['field']['pid']);
				$arrRet	= Dl_Db_PoolManager::update($arrParam);
				if(Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
					Bingo_Log::warning('update pool info failed.input:['.serialize($arrInput).'].output:['.serialize($arrRet).'].');
					$errno	= Tieba_Errcode::ERR_DB_QUERY_FAIL;
					//return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, ' update pool info failed: ['.serialize($arrInput).']', __FUNCTION__);
					break;
				}
			}
			$arrPoolManager = $arrParam['field'];


			//分池操作
			$arrRet	= self::_operatePool($db,$arrPoolInfo);
			if(false == $arrRet	|| Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
				Bingo_Log::warning('db split pool failed.input:['.serialize($arrPoolInfo).'].output:['.serialize($arrRet).'].');
				//return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,'db split pool failed:['.serialize($arrPoolInfo).'].',__FUNCTION__);
				$errno	= Tieba_Errcode::ERR_DB_QUERY_FAIL;
				break;
			}

			//同步修改消费路径状态开关
			$arrParam	= array(
				'pid'	=> $intPid,
				'op'	=> $strUserName,
				'split_logic'	=> Service_Libs_Define::SPLIT_LOGIC_NO_LOGIC,
			);
			$arrRet	= Service_Consumepath_Consumepath::modifyConsumePath($arrParam);
			if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
				Bingo_Log::warning('modify consumpath info failed.input:['.serialize($arrParam).'].output:['.serialize($arrRet).'].');
				$errno	= Tieba_Errcode::ERR_DB_QUERY_FAIL;
				break;
			}
		}while(0);
		if(Tieba_Errcode::ERR_SUCCESS != $errno){
			$db->rollback();
			Bingo_Log::pushNotice("rollback",1);
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,'db query failed.intput:['.serialize($arrPoolInfo).'].',__FUNCTION__);
		}
		if(false === ($tmp = $db->commit())){
			Bingo_Log::warning("commit db error! sql: ".$db->getLastSQL() . ' affect_num:' . $db->getAffectedRows());
			Bingo_Log::pushNotice("commit_err pid:",$intPid);
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,'db commit failed.',__FUNCTION__);
		}

		Bingo_Log::pushNotice("commit pid:",$intPid);
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrPoolInfo);
		//返回
	}

}
