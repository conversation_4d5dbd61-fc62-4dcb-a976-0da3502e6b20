<?php
class Model_Diamondaccount_DiamondAccount extends Model_Base {

    const EXPIRE_DURATION = 2592000; // 30 day

    const DIAMOND_EXPIRE_SCENE_ID = 2000189;
//    const DIAMOND_EXPIRE_SCENE_ID = 45;
    /**
     * 是否过期
     * @return bool
     */
    public function hasExpire() {
        return $this->expire_time < Bingo_Timer::getNowTime();
    }

    /**
     * 更新蓝钻过期时间,时长为end_time+30day
     * @param $end_time
     * @return bool
     */
    public function updateExpireTimeWithMemberEndTime( $end_time ) {
        $this->update_time = Bingo_Timer::getNowTime();
        $this->expire_time = $end_time + self::EXPIRE_DURATION;
        $query  = [
            'field' => [
                'update_time' => $this->update_time,
                'expire_time' => $this->expire_time,
            ],
            'cond'  => [
                'account_id' => $this->account_id,
                'account_type' => $this->account_type,
                'expire_time' => [
                    'opt' => '<',
                    'val' => $this->expire_time,
                ]
            ],
        ];
        return $this->doUpdate($query);
    }

    /**
     * 过期处理流程
     * @return bool
     */
    public function doDiamondExpireProcess() {

        // $currency->reduceAccountScores();
        // user to diamond_pool
        // 此处过期有什么需要注意的地方呢,必须要指定时间,扣多少蓝钻也是需要的
        // 此处为什么需要指定时间,假设有两个php脚本都在处理过期,并且一个快,一个慢,理论上只有一个操作成功,
        // 但是如果此时假设有第三个流程是加蓝钻,
        // 第一个过期成功了,在第二个过期中间,用户加上了蓝钻,则此时第二个过期多扣蓝钻,因此需要加上update_time的条件
        $user_id = $this->account_id;
        $scene_id = self::DIAMOND_EXPIRE_SCENE_ID;
        $amount = $this->scores_total;

        // 启动事务 pool2User
        if( NULL === ($from_db = Service_Libs_Db::getDB()) || NULL === ($to_db = Service_Libs_Db::getDB2()) ){
            Bingo_Log::warning("get db fail.");
            return false;
        }
        if(false === $from_db->startTransaction() || false === $to_db->startTransaction()){
            Bingo_Log::warning("start trans fail.");
            return false;
        }
        $error = Tieba_Errcode::ERR_SUCCESS;
        do {
            // 锁记录
            if(!Service_Currency_Diamond::lockAccountScoresWithUpdateTime($user_id,$this->update_time,$from_db)) {
                Bingo_Log::warning("lock user$user_id error.");
                $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            $transferInput = array(
                'order_id'   => Tbmall_Open_Order::genInnerOrderId($user_id,$scene_id),
                'scene_id'   => $scene_id,
                'from'       => array(
                    array(
                        'account_id'   => $user_id,
                        'account_type' => Service_Mall_Mallconf::ACCOUNT_TYPE_USER,
                        'scores'       => $amount
                    )
                ),
                'to'         => array(
                    array(
                        'account_type' => Service_Mall_Mallconf::ACCOUNT_TYPE_POOL,
                        'scores'       => $amount
                    )
                ),
                'no_trans' => 1,
                'to_ucenter' => 0,
            );
            $arrRet = Service_Mall_Mall::transfer($transferInput);
            if ($arrRet ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning ( "transfer err! input[" . serialize($transferInput) . "]. output[" . serialize ( $arrRet ) . "]" );
                $error = $arrRet ['errno'];
                break;
            }
        }while(0);

        if(Tieba_Errcode::ERR_SUCCESS !== $error){
            Bingo_Log::warning("rollback 1 [" . serialize($from_db->rollback()) . "] 2[" . serialize($to_db->rollback()) ."]");
            return false;
        }

        if (false === $from_db->commit() || false === $to_db->commit()) {
            Bingo_Log::error("db 1 commit err!");
            return false;
        }
        // 更新过期时间,和剩余蓝钻数量
        $this->update_time = Bingo_Timer::getNowTime();
        $this->scores_total = $arrRet['data']['data_tdou']['left_scores_info']['scores_total'];
        $this->scores_money = $arrRet['data']['data_tdou']['left_scores_info']['scores_money'];
        $this->scores_other = $arrRet['data']['data_tdou']['left_scores_info']['scores_other'];
        return true;
    }

    /**
     * @param $query
     * @return bool
     */
    protected function doUpdate( $query ) {
        $ret = Dl_Db_AccountScores::update( $query );
        if ( Service_Libs_Base::isFail( $ret ) ) {
            return false;
        }
        $data = $ret['data'];
        if ( $data != 1 ) { // affected row
            Bingo_Log::warning( "affected row[$data]." );
            return false;
        }

        return true;
    }
}