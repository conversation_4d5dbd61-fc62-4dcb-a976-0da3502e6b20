<?php

class Util_Scores {

	public static function getCostScores($scores_cost, $scores_other, $scores_money, $just_money = 0){//just_money这个参数为********************定制
		//处理scores_money, scores_other为负数的情况
		if ($scores_other < 0) {
			return array('money_cost'=>intval($scores_cost),
				'other_cost'=>0);
		}
		if ($scores_money < 0) {
			return array('other_cost'=>intval($scores_cost),
				'money_cost'=>0);
		}
		$scores_total = $scores_money + $scores_other;
		$money_cost = intval($scores_money / $scores_total * $scores_cost);
		$other_cost = intval($scores_other / $scores_total * $scores_cost);
		$left_cost = $scores_cost - $money_cost  - $other_cost;

		if($left_cost > 0) {
			if ($scores_other - $other_cost >= $left_cost) {
				$other_cost += $left_cost;
			}else if ($scores_money - $money_cost >= $left_cost) {
				$money_cost += $left_cost;
			}
		}

        if($just_money){
            $money_cost = $scores_cost;
            $other_cost = 0;
        }

		return array('money_cost'=>intval($money_cost),
			'other_cost'=>intval($other_cost));                                          
	}                              
}
