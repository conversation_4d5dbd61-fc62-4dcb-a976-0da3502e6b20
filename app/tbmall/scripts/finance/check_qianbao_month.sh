#!/bin/bash
#  sh check_qianbao_month.sh 201601
_strDataPath=''
_strDataSrc=''
_strDataDsc=''

#数据库路径在这里改
_strMysqlPath='/home/<USER>/.jumbo/bin/mysql'
#数据库备库链接信息，需要机器有权限，账号密码在这里修改
_strMysqlConnect="$_strMysqlPath --default-character-set=utf8 -h10.57.107.38 -utbmall_beiku -pJdsle7Sqws2 -Dforum_tbmall -P4666 "

_strTiebaOrderPath=''
_strQianbaoOrderPath=''
_strTiebaOrderRe=''
_strQianbaoOrderRe=''
#验证数据库是否正确
function checkMysql(){
	if [ ! -f "$_strMysqlPath" ];then
		echo '数据库路径不对，赶紧改下~~~'
		exit
	fi
	return 1
}

#比较双方订单
function diffOrder(){
	echo "diff $_strTiebaOrderRe $_strQianbaoOrderRe -y -W 150"
	diff $_strTiebaOrderRe $_strQianbaoOrderRe -y -W 150 > $_strDataPath/diff_$1
	echo ''
	echo "结果如下:******************************************* diff start ***********************************************"
	echo '左边为贴吧账单,右侧为钱包账单'
	cat $_strDataPath/diff_$1|grep -e '|' -e '>' -e '<'
	echo "******************************************* diff stop ********************************************************"
	#统计所有钱包订单钱数 和 订单笔数
	echo "统计所有钱包订单钱数 和 订单笔数"
	echo "************************************* total start ******************************************"
	echo "贴吧订单统计:"
	cat $_strTiebaOrderRe|awk -F "\t" '{count=count+1;total=total+$2} END {print "统计数量： "count", 统计金额: "total}'
	echo "钱包订单统计:"
	cat $_strQianbaoOrderRe|awk -F "\t" '{count=count+1;total=total+$2} END {print "统计数量： "count", 统计金额: "total}'

	echo "************************************* total stop ******************************************"
}

#双方订单排序整理数据
function buildOrder(){
	echo "贴吧账单路径:"$_strTiebaOrderPath
	echo "钱包账单路径:"$_strQianbaoOrderPath

	strQianbaoBuildPath="$_strDataDsc/qianbao_re_$1"
	strQianbaoBuild="/bin/grep -rn pay_ $_strQianbaoOrderPath|grep -rn no_|awk -F \"\t,\" '{print "'$2'"\"\t\""'$12'"}' |sort -g -k 1 > $strQianbaoBuildPath"

	strTiebaBuildPath="$_strDataDsc/tieba_re_$1"
	strTiebaBuild="cat $_strTiebaOrderPath |grep -v order_id|awk -F \"\t\" '{if ("'$6'" !=8) print "'$1'"\"\t\""'$2'"}'|sort -g -k 1 > $strTiebaBuildPath"

	#输出路径记录全局变量
	_strTiebaOrderRe=$strTiebaBuildPath
	_strQianbaoOrderRe=$strQianbaoBuildPath

	echo $strQianbaoBuild
	echo $strTiebaBuild
	#/bin/grep -rn pay_付款 $_strQianbaoOrderPath|/bin/grep -rn no_否|/bin/awk -F "\t," '{print $2"\t"$12}' |/bin/sort -g -k 1 >$strQianbaoBuildPath
	/bin/grep -rn pay_付款 $_strQianbaoOrderPath|/bin/grep -rn no_否|/bin/awk -F "\t," '{a[$2]+=$12} END {for(i in a){print i"\t"a[i]}}' |/bin/sort -g -k 1 >$strQianbaoBuildPath
	/bin/cat $_strTiebaOrderPath |grep -v order_id|/bin/awk -F "\t" '{if ($6 !=8) print $1"\t"$2}'|/bin/sort -g -k 1 > $strTiebaBuildPath
}

#获得数据路径
function getDataPath(){
	strDataFilePath='data'
	if [ ! -d "$strDataFilePath" ];then
		  mkdir -m 777 -v "$strDataFilePath"
		  mkdir -m 777 -v "$strDataFilePath/src"
		  mkdir -m 777 -v "$strDataFilePath/result"
		  mkdir -m 777 -v "$strDataFilePath/log"
	fi
	strPath=$(pwd)'/'$strDataFilePath
	_strDataPath=$strPath
	_strDataSrc="$strPath/src"
	_strDataDsc="$strPath/result"
}
#获得贴吧订单
function getTiebaOrder(){
	strYear=$1
	strMonth=$2
	strTime=$3
	strMonthFirst="$strYear-$strMonth-01"
	#获得当月末尾时间
	strMonthLast=$(date -d "last day $(date -d "1 month $strMonthFirst" +%Y%m%d)" +%Y-%m-%d)
	intStartTime=$(date -d "$strMonthFirst" +%s)
	intStopTime=$(($(date -d "$strMonthLast" +%s)+86400))
	#路径信息
	strDataPath=$_strDataPath
	strDataSrc=$_strDataSrc
	strDataDsc=$_strDataDsc
	strOrderPath=$_strDataSrc"/tieba_$strTime"
	_strTiebaOrderPath=$strOrderPath

	strMysqlQuery="select order_id,db_money,user_id,from_unixtime(finish_time),db_order_id,type from %s where finish_time >= $intStartTime and finish_time < $intStopTime and (channel = 2 or (channel = 6 and type IN (202,203))  )  and db_money > 0 and result_code = 10000"
    #2019-01 钱包没有权限获取好看全民微信的账单，先过滤掉，以后再对
	#strMysqlQuery="select order_id,db_money,user_id,from_unixtime(finish_time),db_order_id,type from %s where finish_time >= $intStartTime and finish_time < $intStopTime and (channel = 2 or (channel = 6 and type IN (202,203))  )  and db_money > 0 and result_code = 10000 and (type NOT IN (50,51,52,53) or (type IN (50,51,52,53) and pay_channel != 'BAIDU-SUPER-WECHAT-WISE'))"
	#echo "获得贴吧订单:"$_strMysqlConnect" -e "$strMysqlQuery" > $strOrderPath"
	#eval $_strMysqlConnect -e $strMysqlQuery > $strOrderPath
	echo "获得贴吧订单:/home/<USER>/pzb/caiwu/get_data_from_ddbs.sh -o $strOrderPath -t dubi_order -s "$strMysqlQuery""
	/home/<USER>/pzb/caiwu/get_data_from_ddbs.sh -o $strOrderPath -t dubi_order -s "$strMysqlQuery"
	chmod 777 $_strTiebaOrderPath

}

#获得钱包订单
function getQianBaoOrder(){
	strYear=$1
	strMonth=$2
	strTime=$3
	strMonthFirst="$strYear-$strMonth-01"
	#获得当月末尾时间
	strMonthLast=$(date -d "last day $(date -d "1 month $strMonthFirst" +%Y%m%d)" +%Y-%m-%d)
	#strPreUrl="ftp://yf-tuan-zhifu-paymis00.yf01.baidu.com:/home/<USER>/odp_reconcile/data/finance_third/product/020/$strYear/$strMonth/020_$strMonthFirst""_$strMonthLast.month.third.csv"
	#strPreUrl="ftp://m1-pay-wallet-juhe-reconcile-script01.m1.baidu.com/home/<USER>/odp/data/finance_third/product/020/$strYear/$strMonth/020_$strMonthFirst""_$strMonthLast.month.third.csv"
	strPreUrl="ftp://m1-pay-wallet-juhe-reconcile-script01.m1.baidu.com/home/<USER>/test.odp/data/reconcile/finance/020/$strYear/$strMonth/020_$strMonthFirst""_$strMonthLast.month.third.csv"
	getDataPath
	#路径信息
	strDataPath=$_strDataPath
	strDataSrc=$_strDataSrc
	strDataDsc=$_strDataDsc
	_strQianbaoOrderPath="$strDataSrc/qianbao_$strTime"

	echo "获得钱包订单:" wget $strPreUrl -xO "$strDataSrc/qianbao_$strTime"
	wget $strPreUrl -xO "$strDataSrc/qianbao_$strTime" >> $_strDataPath/log/$(date +%Y%m%d).log 2>&1

	#增加好看，全民业务线对账
	strPreUrl="ftp://m1-pay-wallet-juhe-reconcile-script01.m1.baidu.com/home/<USER>/test.odp/data/reconcile/finance/089/$strYear/$strMonth/089_$strMonthFirst""_$strMonthLast.month.third.csv"
	echo "获得钱包好看订单:" wget $strPreUrl -xO "$strDataSrc/qianbao_haokan_$strTime"
	wget $strPreUrl -xO "$strDataSrc/qianbao_haokan_$strTime" >> $_strDataPath/log/$(date +%Y%m%d)_haokan.log 2>&1

	strPreUrl="ftp://m1-pay-wallet-juhe-reconcile-script01.m1.baidu.com/home/<USER>/test.odp/data/reconcile/finance/090/$strYear/$strMonth/090_$strMonthFirst""_$strMonthLast.month.third.csv"
	echo "获得钱包全民订单:" wget $strPreUrl -xO "$strDataSrc/qianbao_quanmin_$strTime"
	wget $strPreUrl -xO "$strDataSrc/qianbao_quanmin_$strTime" >> $_strDataPath/log/$(date +%Y%m%d)_quanmin.log 2>&1

	cat $strDataSrc/qianbao_haokan_$strTime >> $strDataSrc/qianbao_$strTime
	cat $strDataSrc/qianbao_quanmin_$strTime >> $strDataSrc/qianbao_$strTime

	chmod 777 $_strQianbaoOrderPath
}


#对账订单
function check_order(){
	strTime=$1
	#获得月份时间
	strYear=${strTime:0:4}
	strMonth=${strTime:4}
	getQianBaoOrder $strYear $strMonth $strTime
	getTiebaOrder $strYear $strMonth $strTime
	buildOrder $strTime
	diffOrder $strTime
}

checkMysql

for strYearMonth in "$@"
do
	check_order $strYearMonth
done