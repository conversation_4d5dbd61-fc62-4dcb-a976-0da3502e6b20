<?php
class userGiftListAction extends Util_Base{
	public function _execute(){
		try{
		    if ($this->_arrUserInfo['is_login'] !== true ){
                //未登陆
                Bingo_Log::warning("user need login!");
	            return $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN, Tieba_Error::getErrmsg(Tieba_Errcode::ERR_USER_NOT_LOGIN));
            }
			$intUserId = $this->_arrUserInfo['user_id'];

			//查询礼包
			$intPn = intval(Bingo_Http_Request::get('pn',1));
			$intRn = intval(Bingo_Http_Request::get('rn',15));
			$intAct = intval(Bingo_Http_Request::get('act',1));
			$arrInput = array(
				'user_id' => $intUserId,
				'pn' => $intPn,
				'rn' => $intRn,
				'act' => $intAct,
			);
			$arrGifts = Util_Service::call('tbmall', 'getGiftsByUserId', $arrInput);
			if (false === $arrGifts || $arrGifts['errno'] != Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning("getGiftsByUserId error".serialize($arrGifts));
	           	return $this->_jsonRet(Tieba_Errcode::ERR_UNKOWN, Tieba_Error::getErrmsg(Tieba_Errcode::ERR_UNKOWN));
			}
			
		    $gifts = $arrGifts['data'];
			$gift_infos = array();
			
			foreach ($gifts['gift_info'] as $gift){
				$intGiftId = intval($gift['gift_id']);

				if($this->isSuitItems($intGiftId)){
				    $gift['active_code'] = explode("-", $gift['active_code']);
				}else{
					$gift['active_code'] = array($gift['active_code']);
				}
				$gift_infos[] = $gift;
			}
			$temp['gift_info']=$gift_infos;
			$temp['gift_count']=$gifts['gift_count'];
			
			$arrOut = array(
				'gifts' => $temp,
			);
			
			$this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),$arrOut);
		}
		catch (Util_Exception $e){
			$this->_jsonRet($e->getCode(), Tieba_Error::getUserMsg($e->getCode()));
		}
	}

	/**
	 * 判断是否是礼包组合
	 * @param  [int]  $intGiftId [礼包ID	]
	 * @return boolean            [true/false]
	 */
	private function isSuitItems($intGiftId) {
		return in_array($intGiftId, Util_Const::$GIFT_SUIT_ITEMS);
	}
}