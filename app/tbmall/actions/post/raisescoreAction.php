<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-10-29 23:35:18
 * @comment json接口
 * @version
 */
class raisescoreAction extends Util_Base {

    public function _execute(){
        try {
            //参数获取
            $forum_id = intval(Bingo_Http_Request::get('forum_id',0));
            $forum_name = Bingo_Http_Request::get('forum_name','');
            $skin_id = intval(Bingo_Http_Request::get('skin_id',0));
            $scores = intval(Bingo_Http_Request::get('scores',0));
            if (true !== Bingo_Http_Request::isPost()){
                throw new Util_Exception("must post!",Tieba_Errcode::ERR_NOT_POST_METHOD);
            }
            if ($this->_arrUserInfo['is_login'] !== true ){
                //未登陆
                throw new Util_Exception("user need login!",Tieba_Errcode::ERR_USER_NOT_LOGIN);
            }
            
            ######vcode check start#####
            $captcha_vcode_str = Bingo_Http_Request::get('captcha_vcode_str','');
            $captcha_input_str = Bingo_Http_Request::get('captcha_input_str','');
			$password = Bingo_Http_Request::get('password','');
			$password = urldecode($password);
            $arrInput = array(
            	'user_id'=>$this->_arrUserInfo['user_id'],//user_id
            	'captcha_input_str'=>$captcha_input_str, //user_input_code
            	'captcha_vcode_str'=>$captcha_vcode_str,//vcode_string_last_get
            	'check_password'	=>1,
				'password' =>$password,			//password
            	'score'	=> $scores,
            );
            $arrOut = Tieba_Service::call('tbmall', 'safeCheckVcode', $arrInput, NULL, NULL, 'post', 'php', 'utf-8','local');
            if($arrOut['errno'] !== 0)
            {
            	$errno = $arrOut['errno'];
            	 $this->_jsonRet($errno,Tieba_Error::getUserMsg($errno),$arrOut['data']);
            	 return ;
            } 
            ######vcode check end#####
            
            
			$arrParams = array('user_id' => $this->_arrUserInfo['user_id'], 'scores' => $scores, 'forum_id'=>$forum_id,'forum_name'=>$forum_name, 'skin_id'=>$skin_id);
			$arrOut = Tieba_Service::call('tbmall', 'contributeScores', $arrParams, NULL, NULL, 'post', 'php', 'utf-8', 'local');
			if ($arrOut === false|| !isset($arrOut['errno'])){
                throw new Util_Exception("service call fail!",Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $errno = $arrOut['errno'];

            Bingo_Log::pushNotice('fid',$forum_id);
            Bingo_Log::pushNotice('fname',$forum_name);
            Bingo_Log::pushNotice('skin_id',$skin_id);
            Bingo_Log::pushNotice('scores',$scores);
            
            $this->_jsonRet($arrOut['errno'],$arrOut['errmsg']);
        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), '未知错误');	
        }
    }
}
?>
