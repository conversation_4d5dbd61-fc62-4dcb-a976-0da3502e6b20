<?php
/**
 * 定时投放资源
 *
 * <AUTHOR>
 * @date 2018/11/7
 */

class Dl_Db_BusinesPromotAmbassador extends Dl_Db_Model
{

    protected static $_db_table = 'business_promot_ambassador';
    protected static $_db_name = 'forum_gcon';
    private static $_arrDB;
    private static $_instance;

    public static $fields = array(
        'id',
        'uid',
        'forum_id',
        'status',
        'op_user',
        'update_time',
        'create_time'
    );

    /**
     * @param null $db_name
     * @param null $db_table
     *
     * @return mixed
     */
    public static function getModel($db_name = null, $db_table = null)
    {
        if(!$db_name){
            $db_name = self::$_db_name;
        }
        if(!$db_table){
            $db_table = self::$_db_table;
        }
        $arrKey = $db_name.$db_table;
        if(isset(self::$_arrDB[$arrKey])){
            return self::$_arrDB[$arrKey];
        }else{
            $class_name            = __CLASS__;
            self::$_arrDB[$arrKey] = new $class_name($db_table, $db_name);

            return self::$_arrDB[$arrKey];
        }
    }

    /**
     * @brief get mysql obj.
     * @return: obj of Bd_DB, or null if connect fail.

     **/
    public  static function getDB() {
        $objTbMysql = Tieba_Mysql::getDB(self::$_db_name);
        if($objTbMysql && $objTbMysql->isConnected()) {
            $objTbMysql->charset('utf8');
            return $objTbMysql;
        } else {
            Bingo_Log::warning("db connect fail.");
            return null;
        }
    }

    /**
     * @return bool
     */
    protected function _after_select()
    {
        //Bingo_Log::warning("bbbb");
        return true;
    }

    /**
     * @return bool
     */
    protected function _before_select()
    {
        //Bingo_Log::warning("aaaa");
        return true;
    }

    /**
     * @param $arrInput
     *
     * @return mixed
     */
    public static function select($arrInput)
    {
        if(!$arrInput['field']){
            $arrInput['field'] = self::$fields;
        }
        self::$_instance = self::getModel($arrInput['db_name'], $arrInput['db_table']);

        return self::$_instance->baseSelect($arrInput);
    }

    /**
     * @param $arrInput
     *
     * @return mixed
     */
    public static function update($arrInput)
    {
        self::$_instance = self::getModel($arrInput['db_name'], $arrInput['db_table']);
        $ret = self::$_instance->baseUpdate($arrInput);
        //Bingo_Log::warning('hehe:'.self::$_instance->getLastSQL());
        return $ret;
    }

    /**
     * @param $arrInput
     *
     * @return mixed
     */
    public static function insert($arrInput)
    {
        self::$_instance = self::getModel($arrInput['db_name'], $arrInput['db_table']);
        return self::$_instance->baseInsert($arrInput);
    }

}
