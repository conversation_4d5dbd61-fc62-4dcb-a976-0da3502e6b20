/* page_list */
SELECT page_id,page_type,page_sync,page_status,page_title,page_desc,page_data FROM page_list WHERE page_id=1 FOR UPDATE;
UPDATE page_list SET page_title='aaa' WHERE page_id=1;
UPDATE page_list SET page_status=1 WHERE page_id=1;
UPDATE page_list SET page_desc='xxx' WHERE page_id=1;
UPDATE page_list SET page_data='aaa' WHERE page_id=1;
INSERT INTO page_list(page_type,page_sync,page_status,page_title,page_desc,page_data) VALUES(1,1,1,'aaa','aaa','aaa');
SELECT COUNT(page_id) AS total FROM page_list WHERE page_status IN(0,1,2,3);
SELECT page_id,page_type,page_sync,page_status,page_title,page_desc,page_data FROM page_list WHERE page_status IN(0,1,2,3) ORDER BY page_id DESC LIMIT $intPageStart, $intPageSize;
SELECT page_id,page_type,page_sync,page_status,page_title,page_desc,page_data FROM page_list WHERE page_id=1;
/* page_card */
INSERT INTO page_card(card_type,card_status,card_group,card_order,card_data,page_id) VALUES(1,1,1,1,'aaa',1);
SELECT card_type,card_status,card_group,card_order,card_data,page_id FROM page_card WHERE page_id=1 AND card_status=1;
UPDATE page_card SET card_status=1 WHERE card_id=1;
/* page_record */
INSERT INTO page_record(page_id,record_type,record_data,record_user_id,record_user_type,record_user_name) VALUES(1%,1,'aaa',1,1,'aaa');

alter table page_card MODIFY card_data VARCHAR(16384);
alter table page_list MODIFY page_data VARCHAR(16384);