<?php
/**
 * Copyright (c) 2016 Baidu, Inc.
 * All rights reserved.
 *
 * <AUTHOR>
 * @abstract 修改产品线名称
 *
 */

class updateAction extends Actions_Util_Base
{

    /**
     * @param
     * @return
     */
    public function execute()
    {
        // 检查参数
        $params = array(
            'product_id',
            'product_name',
        );
        $arrInput = $this->checkParams($params);
        if (!$arrInput)
        {
            return Util_Output::buildJSON(Tieba_Errcode::ERR_PARAM_ERROR, '您的参数有误');
        }

        // 检查用户权限
        if (!$this->checkUserAuth($arrInput['product_id'], Lib_Def::AUTH_TYPES_ADMIN))
        {
            return Util_Output::buildJSON(Tieba_Errcode::ERR_PARAM_ERROR, '您不是产品线管理员，不能修改产品线名称');
        }

        $arrOutput = Tieba_Service::call('blitz', 'updateProduct', $arrInput, null, null, 'post', 'php', 'utf-8');

        return Util_Output::buildJSON($arrOutput['errno'], $arrOutput['errmsg']);
    }
}
