<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-01-18 20:33:58
 * @version
 */

class Util_Session {

    protected static $_arrUserSInfo = array();

    public static function isLogin() {
        return Tieba_Session_Socket::isLogin();
    }

    public static function getUserId() {
        return Tieba_Session_Socket::getLoginUid();
    }

    public static function getUserName() {
        return Tieba_Session_Socket::getLoginUname();
    }
    
    public static function getUserSessionInfo() {
        if (!empty(self::$_arrUserSInfo)) {
            return self::$_arrUserSInfo;
        }

                $bolLogin    = (boolean)Tieba_Session_Socket::isLogin();
                $intUserId    = intval(Tieba_Session_Socket::getLoginUid());
                $strUserName    = strval(Tieba_Session_Socket::getLoginUname());
                $intUserIp        = intval(Bingo_Http_Ip::ip2long(Bingo_Http_Ip::getConnectIp()));
                $bolNoUname        = (boolean)Tieba_Session_Socket::getNo_un();
                $strMobile      = strval(Tieba_Session_Socket::getMobilephone());
                $strEmail       = strval(Tieba_Session_Socket::getEmail());
                
                
        $arrUserSInfo    = array(
            'bolLogin'     => $bolLogin,
            'intUid'    => $intUserId,
            'strUname'    => $strUserName,
            'intUip'    => $intUserIp,
            'bolNoname' => $bolNoUname,
            'strMobile' => $strMobile,
            'strEmail'  => $strEmail,
        );
        self::$_arrUserSInfo = $arrUserSInfo;
        return self::$_arrUserSInfo;
    }
}    
?>
