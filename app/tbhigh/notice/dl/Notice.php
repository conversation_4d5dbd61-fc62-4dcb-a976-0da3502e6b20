<?php 

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date Sun Dec 27 13:28:39 CST 2015
*/

class Notice_Dl_Notice {

    const PREFIX_NOTICE_LIST  		= 'NL_';
    const PREFIX_NOTICE_LIST_COUNT  = 'NLC_';
	/**
	 * 推送通知
	 *
	 * @param user_id
	 * @param peerUid
	 * @param type (0相册有新嗨，1关注，2回复，3点赞，4回复at)
	 * @param album_id
	 * @param hid
	 * @param content
	 * @param pic_url
	 * @return
	 */
	public static function sendNotice($arrInput) {
		$user_id     = intval($arrInput['user_id']);
		$peerUid     = intval($arrInput['peerUid']);
        $at_uid      = intval($arrInput['at_uid']);
		$type        = intval($arrInput['type']);
		$album_id    = intval($arrInput['album_id']);
		$hid         = intval($arrInput['hid']);
		$content     = strval($arrInput['content']);
        $cpid        = intval($arrInput['cpid']);
		$create_time = time();

		$arrValue = array(
			$peerUid,
			$type,
			$album_id,
			$hid,
			$content,
			$create_time,
            $cpid,
            $at_uid,
		);
		$strValue = serialize($arrValue);
        $param = array(
            'key'    => self::PREFIX_NOTICE_LIST . $user_id,
            'value'  => array($strValue),
        );
        $ret = Libs_Redis::redisQuery(Libs_Define::REDIS_NAME, 'LPUSH', $param);
        if ($ret == false || $ret['err_no'] !== 0) {
            Bingo_Log::warning("push notice failed. errno[{$ret['err_no']}]");
            return Libs_Func::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        /*
		$param = array(
			'key'  => self::PREFIX_NOTICE_LIST_COUNT . $user_id,
		);
		$ret = Libs_Redis::redisQuery(Libs_Define::REDIS_NAME, 'INCR', $param);
		if ($ret == false || $ret['err_no'] !== 0) {
			Bingo_Log::warning("incr notice count failed. errno[{$ret['err_no']}]");
		}
         */
        return Libs_Func::errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	/**
	 * 获取用户未读通知数量
	 *
	 * @param user_id
	 * @return
	 */
	public static function countNotice($arrInput) {
		$user_id = intval($arrInput['user_id']);
		if (!$user_id) {
			return Libs_Func::errRet(Tieba_Errcode::ERR_SUCCESS, 0);
		}

		$key = self::PREFIX_NOTICE_LIST_COUNT . $user_id;
        $param = array(
            'key'    => $key,
        );
        $ret = Libs_Redis::redisQuery(Libs_Define::REDIS_NAME, 'GET', $param);
        if ($ret == false || $ret['err_no'] !== 0) {
            Bingo_Log::warning("get notice failed. errno[{$ret['err_no']}]");
            return Libs_Func::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        return Libs_Func::errRet(Tieba_Errcode::ERR_SUCCESS, $ret['ret'][$key]);
	}
	/**
	 * 获取通知消息列表
	 *
	 * @param user_id
	 * @param limit
	 * @return
	 */
	public static function getNoticeList($arrInput) {
		$user_id = intval($arrInput['user_id']);
		$offset  = intval($arrInput['offset']);
		$limit   = intval($arrInput['limit']);
		if (!$user_id || !$limit) {
			Bingo_Log::warning("get notice list param error. user_id[$user_id] limit[$limit]");
			return Libs_Func::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}

		$key = self::PREFIX_NOTICE_LIST . $user_id;
        $param = array(
            'key'    => $key,
			'start'  => $offset,
			'stop' 	 => $offset + $limit - 1,
        );
        $ret = Libs_Redis::redisQuery(Libs_Define::REDIS_NAME, 'LRANGE', $param);
        if ($ret == false || $ret['err_no'] !== 0) {
            Bingo_Log::warning("get notice failed. errno[{$ret['err_no']}]");
            return Libs_Func::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

		$arrNotice = array();
		foreach ($ret['ret'][$key] as $_item) {
			list($uid, $type, $album_id, $hid, $content, $create_time, $cpid, $at_uid)
				= unserialize($_item);
			$arrNotice[] = array(
				'user_id'     => $uid,
                'at_uid'      => $at_uid,
				'type'        => $type,
				'album_id'    => $album_id,
				'hid'         => $hid,
				'content'     => $content,
				'create_time' => $create_time,
                'pic_url'     => $cpid ? Libs_Func::pid2Curl($cpid) : '',
			);
		}
		/**
		 * 清除未读数
		 */
        /*
		$key = self::PREFIX_NOTICE_LIST_COUNT . $user_id;
        $param = array(
            'key'    => $key,
        );
        $ret = Libs_Redis::redisQuery(Libs_Define::REDIS_NAME, 'DEL', $param);
        if ($ret == false || $ret['err_no'] !== 0) {
            Bingo_Log::warning("ltrim notice list failed. errno[{$ret['err_no']}]");
        }
         */

        return Libs_Func::errRet(Tieba_Errcode::ERR_SUCCESS, $arrNotice);
	}

    /**
     * 推送消息到贴吧客户端
     *
     * @param 
     * @return
     */
    public static function remindMsg($arrInput) {
        $user_id   = $arrInput['user_id'];
        $peer_uid  = $arrInput['peerUid'] ? $arrInput['peerUid'] : 
            $arrInput['at_uid'];
        if ($peer_uid) {
            $peer      = Libs_User::getUnameByUids(array($peer_uid));
            $peerUname = $peer[$peer_uid]['user_name'];
        }
        $content   = $arrInput['content'];
        $type      = $arrInput['type'];
        switch ($type) {
            case 0 : $content = "$peerUname 在你的活动发表了嗨"; break;
            case 1 : $content = "$peerUname 关注了你"; break;
            case 2 : $content = "$peerUname 回复说：$content"; break;
            case 3 : $content = "$peerUname 赞了你的嗨"; break;
            case 4 : $content = "$peerUname @我说：$content"; break;
            //case 5 : $content = $content; break;
        }
        if ($type < 5) {
            $content = array(
                'notify' => $content,
            );
        } else {
            $content = array(
                'hiactivity' => array(
                    'content' => $content,
                    'id'      => $arrInput['album_id'],
                ),
            );
        }
        $param = array(
            'notify_uid'  => $user_id,
            'notify_type' => 24,
            'terminal'    => 20, 
            'content'     => json_encode($content),
            'msg_type'    => 1,   
            'ext'         => array(
                'tp' => 4,
                'to' => $user_id,
            ), 
        );
        $ret = Tieba_Service::call('im', 'addCommonMsg', $param, 
            null, null, 'post', 'php','utf-8');
        Bingo_Log::warning("push msg to tbclient. uid[$user_id] type[$type] no[{$ret['errno']}]");
        if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("push notice failed. errno[{$ret['errno']}]");
        }
        return $ret;
    }
    /**
     * 推送消息到玩嗨客户端
     *
     * @param 
     * @return
     */
    public static function pushMsg2HIClient($arrInput) {
        $user_id   = $arrInput['user_id'];
        $peer_uid  = $arrInput['peerUid'] ? $arrInput['peerUid'] : 
            $arrInput['at_uid'];
        $type      = $arrInput['type'];
        if ($peer_uid) {
            $peer      = Libs_User::getUnameByUids(array($peer_uid));
            $peerUname = $peer[$peer_uid]['user_name'];
        }
        $content   = $arrInput['content'];
        switch ($type) {
        case 0 : 
            $content = "$peerUname 在你的活动发表了嗨"; 
            $extra   = array(
                'hid' => $arrInput['hid'], 
            );
            break;
        case 1 : 
            $content = "$peerUname 关注了你"; 
            $extra   = array(
                'user_id' => $user_id,
            );
            break;
            //case 2 : $content = "$peerUname 回复说：$content"; break;
            //case 3 : $content = "$peerUname 赞了你的嗨"; break;
            //case 4 : $content = "$peerUname @我说：$content"; break;
        case 5 : 
            $extra = array(
                'album_id' => $arrInput['album_id'],
            ); 
            break;
        default  : 
            return Libs_Func::errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $param = array(
            'user_id' => $user_id,
            'msg' => array(
                'title'   => $content,
                'content' => $content,
                'type'    => $type,
                /**
                 * 通知的id, 同一个类型的id只会在端上展示一条
                 */
                'nid'     => $type,
                'extra'   => $extra,
            ),
        );
        $ret = Libs_Bdpush::pushNotice($param);
        Bingo_Log::warning("push msg to hiclient. uid[$user_id] type[$type] ret[$ret]");
        if ($ret == false) {
            Bingo_Log::warning("push notice failed.");
            return Libs_Func::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return Libs_Func::errRet(Tieba_Errcode::ERR_SUCCESS);
    }
}
