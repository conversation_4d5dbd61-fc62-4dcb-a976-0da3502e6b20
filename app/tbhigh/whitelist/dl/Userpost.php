<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2016-04-01 14:18:01
 */
class Whitelist_Dl_Userpost
{

    const PREFIX_USERPOSTLIST = 'UP_';

    /**
     * @brief  用户发帖关联
     * @param user_id       : string : 用户id
     * @param forum_id       : string : 吧id
     * @param album_id       : string : album id
     * @return
     */
    public static function addUserPostList($arrInput)
    {
        $user_id     = intval($arrInput['user_id']);
        $forum_id  = intval($arrInput['forum_id']);
        $album_id  = intval($arrInput['album_id']);

        if (!$user_id || !$forum_id || !$album_id) {
            Bingo_Log::warning("add white redis param error. uid[$user_id] forum_id[$forum_id] album_id[$album_id]");
            return Libs_Func::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $param = array(
            'key'    => self::PREFIX_USERPOSTLIST . $user_id,
        );

        $param['value'] = json_encode(array(
            'forum_id' => $forum_id,
            'album_id' => $album_id,
        ));
        $ret = Libs_Redis::redisQuery(Libs_Define::REDIS_NAME, 'LPUSH', $param);
        if ($ret == false || $ret['err_no'] !== 0) {
            Bingo_Log::warning("set user whitelist failed. errno[{$ret['err_no']}]");
            return Libs_Func::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        return Libs_Func::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 获取用户发帖关联列表
     *
     * @param user_id    : string : 用户id
     * @return
     */
    public static function getUserPostList($arrInput)
    {
        $offset = intval($arrInput['offset']);
        $limit = intval($arrInput['limit']);
        $user_id = intval($arrInput['user_id']);

        if (!isset($offset) || !isset($limit)) {
            Bingo_Log::warning("get white list param error. offset[$offset] limit[$limit]");
            return Libs_Func::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $key = self::PREFIX_USERPOSTLIST . $user_id;

        $param = array(
            'key' => $key,
            'start' => $offset,
            'stop' => $offset + $limit -1,
        );
        $ret = Libs_Redis::redisQuery(Libs_Define::REDIS_NAME, 'LRANGE', $param);
        if ($ret == false || $ret['err_no'] !== 0) {
            Bingo_Log::warning("get forum whitelist failed. errno[{$ret['err_no']}]");
            return Libs_Func::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $param = array(
            'key' => $key,
        );
        $ret2 = Libs_Redis::redisQuery(Libs_Define::REDIS_NAME, 'LLEN', $param);
        if ($ret2 == false || $ret2['err_no'] !== 0) {
            Bingo_Log::warning("get forum whitelist failed. errno[{$ret['err_no']}]");
            return Libs_Func::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $output = array(
            'list' => array(),
            'count' => $ret2['ret'][$key],
        );
        foreach ($ret['ret'][$key] as $item) {
            $output['list'][] = json_decode($item);
        }
        return Libs_Func::errRet(Tieba_Errcode::ERR_SUCCESS, $output);
    }
}
