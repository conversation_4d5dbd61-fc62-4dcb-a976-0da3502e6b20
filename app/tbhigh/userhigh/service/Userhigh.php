<?php 

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date Sun Dec 27 13:28:39 CST 2015
*/

class Userhigh_Service_Userhigh {

    /**
     * 创建用户-嗨拉链
     *
     * @param
     * @return
     */
    public static function createHighOfUser($arrInput) {
        $ret = Userhigh_Dl_Userhigh::addUserHigh($arrInput);
        if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("add user high failed. errno[{$ret['errno']}]");
            return Libs_Func::errRet($ret['errno']);
        }
        return Libs_Func::errRet(Tieba_Errcode::ERR_SUCCESS);
    }
    /**
     * 获取用户的嗨列表
     *
     * @param user_id
     * @param offset
     * @param limit
     * @return
     */
    public static function getUserHighPage($arrInput) {
        $param = array(
            'user_id' => $arrInput['user_id'],
            'limit'   => $arrInput['limit'],
            'offset'  => $arrInput['offset'],
        );
        $userHighs = Userhigh_Dl_Userhigh::getUserHigh($param);
        if ($userHighs['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("get user high failed. errno[{$userHighs['errno']}]");
        }
        /**
         * 获取嗨的封面信息
         */
        $arrHids = array();
        foreach ($userHighs['data'] as $_high) {
            $arrHids[] = $_high['hid'];
        }
        $highCovers = High_Service_High::mgetHighCoverInfo(array('hids' => $arrHids));
        if ($highCovers['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("mget high cover failed. errno[{$highCovers['errno']}]");
            return Libs_Func::errRet($highCovers['errno']);
        }
        $list = array_values($highCovers['data']);

        $detail_width  = $arrInput['detail_width'];
        $detail_height = $arrInput['detail_height'];
        $list = High_Service_High::mExtHiDetailData(0, $arrInput['user_id'], 
            $list, null, $detail_width, $detail_height);
        $arrOutput = array(
            'list'     => $list,
            'has_more' => count($list) >= $arrInput['limit'] ? true : false,
        );
        return Libs_Func::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }
    /**
     * 用户的主页信息
     *
     * @param user_id
     * @param myId 
     * @param offset
     * @param limit
     * @return
     */
    public static function userHomePage($arrInput) {
        $user_id       = $arrInput['user_id'];
        $myId          = $arrInput['myId'];
        $offset        = $arrInput['offset'];
        $limit         = $arrInput['limit'];
        $detail_width  = $arrInput['detail_width'];
        $detail_height = $arrInput['detail_height'];

        if (!$user_id) {
            Bingo_Log::warning("get user main page param error. user_id[$user_id]");
            return Libs_Func::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        /**
         * 获取用户发表的嗨
         */
        $param = array(
            'user_id' => $user_id,
            'offset'  => $offset,
            'limit'   => $limit,
        );
        $userHighs = Userhigh_Dl_Userhigh::getUserHigh($param);
        if ($userHighs['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("get user high failed. errno[{$userHighs['errno']}]");
        }
        $userHighs = $userHighs['data'];
        /**
         * 获取嗨的封面信息
         */
        $arrHids = array();
        foreach ($userHighs as $_high) {
            $arrHids[] = $_high['hid'];
        }
        $highCovers = High_Service_High::mgetHighCoverInfo(array('hids' => $arrHids));
        if ($highCovers['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("mget high cover failed. errno[{$highCovers['errno']}]");
        }
        $highInfo   = array_values($highCovers['data']);
        $highInfo = High_Service_High::mExtHiDetailData(0, $user_id, $highInfo, 
            null, $detail_width, $detail_height);

        /**
         * 用户的粉丝数
         */
        $param = array('user_id' => $user_id);
        $nFans = Userrelation_Service_Userrelation::countUserFans($param);
        $nFans = intval($nFans['data']);
        /**
         * 用户的关注数
         */
        $nFollows = Userrelation_Service_Userrelation::countUserFollows($param);
        $nFollows = intval($nFollows['data']);
        /**
         * 是否关注了这个用户
         */
        $param = array(
            'user_id' => $myId,
            'fuids'   => array($user_id),
        );
        $bolFollow = Userrelation_Service_Userrelation::hasFollow($param);
        $bolFollow = (bool)$bolFollow['data'][$user_id];
        /**
         * 用户名+加密串
         */
        $arrUnams = Libs_User::getUnameByUids(array($user_id));

        $highList = array(
            'list'     => $highInfo,
            'has_more' => count($highInfo) >= $limit ? true : false,
        );
        $arrOutput = array(
            'user_id'     => $user_id,
            'user_name'   => $arrUnams[$user_id]['user_name'],
            'portrait'    => $arrUnams[$user_id]['portrait'],
            'num_fans'    => $nFans,
            'num_follows' => $nFollows,
            'has_follow'  => $bolFollow,
            'high'        => $highList,
        );
        return Libs_Func::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }
    /**
     * 获取用户的嗨列表
     *
     * @param user_id
     * @param offset
     * @param limit
     * @return
     */
    public static function getUserHighList($param) {
        $param = array(
            'user_id' => $param['user_id'],
            'offset'  => $param['offset'],
            'limit'   => $param['limit'],
        );
        $highList = Userhigh_Dl_Userhigh::getUserHigh($param);
        if ($highList['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("get user high list failed. errno[{$highList['errno']}]");
        }
        return Libs_Func::errRet($highList['errno'], (array)$highList['data']);
    }
    /**
     * 删除用户的嗨信息
     *
     * @param user_id
     * @param hid
     * @return
     */
    public static function delUserHigh($arrInput) {
        $param = array(
            'user_id' => $arrInput['user_id'],
            'hid'     => $arrInput['hid'],
        );
        $ret = Userhigh_Dl_Userhigh::delUserHigh($param);
        if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("delete user high failed. errno[{$ret['errno']}]");
        }
        return Libs_Func::errRet($ret['errno']);
    } 
    /**
     * 恢复用户的嗨信息
     *
     * @param user_id
     * @param hid
     * @return
     */
    public static function recoverUserHigh($arrInput) {
        $param = array(
            'user_id' => $arrInput['user_id'],
            'hid'     => $arrInput['hid'],
        );
        $ret = Userhigh_Dl_Userhigh::recoverUserHigh($param);
        if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("recover user high failed. errno[{$ret['errno']}]");
        }
        return Libs_Func::errRet($ret['errno']);
    } 
    /**
     * 标记删除用户在一个相册中发表的嗨
     *
     * @param user_id
     * @param album_id
     * @return
     */
    public static function delUserHighOfAlbum($arrInput) {
        $param = array(
            'user_id'  => $arrInput['user_id'],
            'album_id' => $arrInput['album_id'],
        );
        $ret = Userhigh_Dl_Userhigh::delUserHighOfAlbum($param);
        if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("del user high of album failed. errno[{$ret['errno']}]");
        }
        return Libs_Func::errRet($ret['errno']);
    }
    /**
     * 恢复用户在一个相册中发表的嗨
     *
     * @param user_id
     * @param album_id
     * @return
     */
    public static function recoverUserHighOfAlbum($arrInput) {
        $param = array(
            'user_id'  => $arrInput['user_id'],
            'album_id' => $arrInput['album_id'],
        );
        $ret = Userhigh_Dl_Userhigh::recoverUserHighOfAlbum($param);
        if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("recover user high of album failed. errno[{$ret['errno']}]");
        }
        return Libs_Func::errRet($ret['errno']);
    }
}
