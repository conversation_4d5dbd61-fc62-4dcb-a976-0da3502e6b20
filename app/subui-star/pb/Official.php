<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Frsstar.php
 * <AUTHOR>
 * @date 2014/03/03 19:31:28
 * @brief ����Ʊ��
 *  
 **/

class Official implements Tieba_Hello_ParallelAbstract{
	
   
    const MODULE_NAME_STAR_OFFICIAL = 'star_official';
    private static $_isOfficial = 0;
	const STAR_CUPID_NUMBER = 331;
	protected static $objCache = null;
	const CACHE_EXPIRE = 60;
	//const cache_pre = 'pb_star_offficial_';
	const cache_pre = 'pb_star_offficial_utf8_';
	private static $retCache = null;
	private static $str_cache_key = '';
	private static $official_info = array();
	

	public static function preExecute(Tieba_Hello_Param $objParam){
		$arrRet = array();
		$arrStyle = $objParam->getForumParam('attrs');
		if(isset($arrStyle[self::MODULE_NAME_STAR_OFFICIAL])){
			$bolOfficial = $objParam->checkCupid(self::STAR_CUPID_NUMBER);		
			if(!$bolOfficial){
				return false;
			}
			$arrOfficial = $arrStyle[self::MODULE_NAME_STAR_OFFICIAL];
			$official_id = intval($arrOfficial['official_id']);
			$arrUserOffical = $objParam->getUserParam('fans_attr');;
			$arrOfficial = array('official'=> $arrOfficial);
			if(isset($arrUserOffical[$official_id])){
				$arrMember = $arrUserOffical[$official_id];
				$arrMember['user_name'] = $objParam->getUserParam('user_name');
				$arrMember['is_member'] = 1;
				$arrMember['number'] = $arrMember['fans_id'];
				unset($arrMember['fans_id']);
				$arrOfficial['member']=$arrMember;
				self::$official_info = $arrOfficial;
			}
			$arrInput = array (
					'official_id' => $official_id,
					'forum_name'  => $objParam->getForumParam('forum_name'),
					'forum_id'    => $objParam->getForumParam('forum_id'),
					'user_id'       => $objParam->getUserParam('user_id'),
					'limit'       =>10,
					'day' => date('Ymd'),
			);
			self::$str_cache_key = self::_getPrivateCacheKey($arrInput);
			if (self::$str_cache_key == false) {
				Bingo_Log::warning ( 'get cache key fail' );
				return false;
			} else {
				self::$objCache = $objParam->getContextParam('cache_obj');
				if(self::$objCache == null || self::$objCache == false) {
					Bingo_Log::warning('init cache fail');
				}else{
					self::$retCache = self::$objCache->get ( self::$str_cache_key );
					self::$retCache = unserialize(self::$retCache);
					if (self::$retCache == false) {
						$out['call'][] = array(
								'service_name' =>'star',
								'method' => 'getLatestSchedule',
								'input' => $arrInput,
						);
						$out['call'][] = array(
								'service_name' =>'star',
								'method' => 'getDaySchedule',
								'input' => $arrInput,
						);
						$arrInput = array(
								'forum_name' => $arrInput['forum_name'],
								'forum_id'   => $arrInput['forum_id'],
								'need_abstract' => 0,
								'goodclass_id'  => 0,
								'offset'        => 0,
								'res_num'       => 0,
								'need_photo_pic' => 0,
								'icon_size'     =>0,
								'ngscfr'        =>0,
						);
						$out['call'][] = array(
								'service_name' =>'post',
								'method' => 'getGoodThreads',
								'input' => $arrInput,
						);	
					}else{
						$arrRet = array_merge(self::$retCache,self::$official_info);
						$arrOut['official_info'] = array('tpl_var' => $arrRet);
						$out['out'] = $arrOut;
					}
				}
			}
		}
		return $out;	
	}  
	public static function execute(Tieba_Hello_Param $objParam, array $arrMultiOut){
	    $arrRet = array ();
	    if(self::$retCache == null){
	    	$arrLastSchedule = $arrMultiOut[0];
	    	if ($arrLastSchedule == false || Tieba_Errcode::ERR_SUCCESS !== $arrLastSchedule ['errno']) {
	    		Bingo_Log::warning ( 'get schedul fail.input=' . $arrReq );
	    		return false;
	    	} else {
	    		$arrRet = array_merge ( $arrRet, $arrLastSchedule ['data'] );
	    	}
	    	$arrDaySchedule = $arrMultiOut[1];
	    	if ($arrDaySchedule == false || Tieba_Errcode::ERR_SUCCESS !== $arrDaySchedule ['errno']) {
	    		Bingo_Log::warning ( 'get schedul fail.input=' . $arrReq );
	    		return false;
	    	} else {
	    		$arrRet = array_merge ( $arrRet, $arrDaySchedule ['data'] );
	    	}
		    $arrGoodshow = $arrMultiOut[2];
	    	if($arrGoodshow == false || Tieba_Errcode::ERR_SUCCESS !== $arrGoodshow['errno']){
	    		Bingo_Log::warning('get goodclass_show.input='.$arrInput);
	    	}else{
	    		$arrRet['goodclass_show'] = $arrGoodshow['output']['forum_info']['goodclass_show'];
	    	}
	    	self::$objCache->add(self::$str_cache_key,serialize($arrRet),60);
	    	$arrRet = array_merge($arrRet,self::$official_info);
	    }else{
	   		 $arrRet = array_merge(self::$retCache,self::$official_info);
	    }
    	$arrOut['official_info'] = array('tpl_var' => $arrRet);
    	return $arrOut;
	}
    public static function getCacheKey(Tieba_Hello_Param $objParam){
    	return false;
    }  
    private static function _getPrivateCacheKey($arrInput){
    	if($arrInput['official_id']==0 ){
    		return false;
    	}
    	$strKey = self::cache_pre.$arrInput['official_id'];
    	return $strKey;
    }
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
