<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * @brief 统一日志格式
 * <AUTHOR>
 * @date 2015-07-09
 * @version 
 */
class Lib_Live_Log {
    const LOG_FORMAT_OLD   = 0;    //0: key[value] 
    const LOG_FORMAT_NEW   = 1;    //1: key=value
    protected static $_strLogPrefix = "";   //需要添加的log头
    protected static $_arrLogPrefix = null; //log的key=>value形式
    protected static $_intFormat = 1;    

    /**
     * @param unknown $mode_name
     * @return 
     */
    public static function setModeName($mode_name)
    {
        Bingo_Log::setModeName($mode_name);
    }

    /**
     * @param unknown $mode_name
     * @return
     */
    public static function init($arrConfig=array(), $strDefaultModule='',$ompConfig=false)
    {
        self::$_strLogPrefix = "";
        Bingo_Log::init($arrConfig, $strDefaultModule, $ompConfig);
    }

    /**
     * @param unknown $mode_name
     * @return
     */
    public static function pushNotice($strKey, $strValue)
    {
        Bingo_Log::pushNotice($strKey, $strValue);
    }

    /**
     * @param unknown $mode_name
     * @return
     */
    public static function buildNotice($strOtherLog='', $strModule='', $strFile='', $intLine=0, $intTraceLevel=0)
    {
        Bingo_Log::buildNotice($strOtherLog, $strModule, $strFile, $intLine, $intTraceLevel);
    }

    /**
     * @param unknown $mode_name
     * @return
     */
    public static function getNoticeNods()
    {
        Bingo_Log::getNoticeNods();
    }

    /**
     * @param unknown $mode_name
     * @return
     */
    public static function getNoticeNodes()
    {
        Bingo_Log::getNoticeNodes();
    }

    /**
     * @param unknown $mode_name
     * @return
     */
    public static function getLogId()
    {
        Bingo_Log::getLogId();
    }

    /**
     * @param unknown $mode_name
     * @return
     */
    public static function addModule($strModule, $objLog) 
    {
        Bingo_Log::addModule($strModule, $objLog);
    }

    /**
     * @param unknown $mode_name
     * @return
     */
    public static function getModule($strModule='')
    {
        Bingo_Log::getModule($strModule);
    }

    /**
     * @param unknown $mode_name
     * @return
     */
    public static function setFormat($intFormat = self::LOG_FORMAT_OLD)
    {
        self::$_intFormat = intval($intFormat);
    }

    /**
     * @param unknown $mode_name
     * @return
     */
    public static function addStrLogPrefix($strParam) 
    {
        if (empty($strParam) || !is_string($strParam)) {
            return false;
        }
        self::$_strLogPrefix .= $strParam . ' ';
        self::$_arrLogPrefix = null;
    }

    /**
     * @desc 添加全局log参数，对notice warning trace debug都有效
     * @param unknown $mode_name
     * @return
     */
    public static function addLogPrefix($arrParam)
    {
        if (empty($arrParam) || !is_array($arrParam)) {
            return false;
        }

        if (self::$_intFormat == self::LOG_FORMAT_OLD) {
            foreach($arrParam as $key => $value) {
                self::$_strLogPrefix .= $key . '[' . strval($value) . ']' . ' ';
            }
        } else {
            foreach($arrParam as $key => $value) {
                self::$_strLogPrefix .= $key . '=' . urlencode($value) . ' ';
            }
        }
        
        if(self::$_arrLogPrefix === null ){
            self::$_arrLogPrefix = array( $key => $value);
        }else{
            self::$_arrLogPrefix[$key] = $value;
        }
        return true;
	}
	
	/**
	 * @desc 从strPrefix中获取log的key =>value数组
	 * @param unknown $mode_name
	 * @return
	 */
	public static function getArrLogPrefix(){
	    if(self::$_arrLogPrefix !== null ){
	        return self::$_arrLogPrefix;
	    }
	    
	    self::$_arrLogPrefix = array();
	    if(self::$_strLogPrefix == '' ){    
	        return self::$_arrLogPrefix;
	    }
	    
	    if(self::$_intFormat == self::LOG_FORMAT_OLD){
	        $pattern = '/([^\[]+)\[([^\]]+)\]/';
	        $machNum = preg_match_all($pattern, self::$_strLogPrefix , $matches );
            if( $machNum != false && $machNum > 0 ){
                for ($index = 0; $index < $machNum; $index++) {
                    self::$_arrLogPrefix[trim($matches[1][$index])] = trim($matches[2][$index]);
                }
            }
	    }else{
            $arrLogParam = split(' ',self::$_strLogPrefix);
            foreach($arrLogParam as $strValue){
                list($key,$value) = split('=',$strValue);
                if($key != false){
                    self::$_arrLogPrefix[trim($key)] = urldecode(trim($value));
                }
            }
	    }
	    return self::$_arrLogPrefix;
	}

	/**
	 * @desc 获取log
	 * @param unknown $mode_name
	 * @return
	 */
	public static function getLogPrefix()
    {
        return self::$_strLogPrefix;
    }

    /**
     * @param unknown $mode_name
     * @return
     */
    protected static function _getFileAndLine($intLevel=0)
    {
        $arrTrace = debug_backtrace();
        $intDepth = 1 + $intLevel;
        $intTraceDepth = count($arrTrace);
        if ($intDepth > $intTraceDepth){
			$intDepth = $intTraceDepth;
		}
        $arrRet = $arrTrace[$intDepth];
        if (isset($arrRet['file'])){
        	$arrRet['file'] = basename($arrRet['file']);
        }
        $arrSys = $arrTrace[$intDepth+1];
        $arrRet['otherMsg'] = ' sys_print[';
    	if (isset($arrSys['class'])){
        	$arrRet['otherMsg'] = $arrRet['otherMsg'].' class '.$arrSys['class'].',';
        }
    	if (isset($arrSys['function'])){
        	$arrRet['otherMsg'] = $arrRet['otherMsg'].' function '.$arrSys['function'].',';
        }
   		if (isset($arrSys['file'])){
        	$arrRet['otherMsg'] = $arrRet['otherMsg'].' file_path '.$arrSys['file'].',';
        }
        $arrRet['otherMsg'] = $arrRet['otherMsg'].']';
        return $arrRet;
    }

	const LOG_UNKNOWN          = 100;//未知
	const LOG_PARAM_INVALID    = 10;//参数无效
	const LOG_REDIS_INIT       = 20;//redis初始化出错
	const LOG_REDIS_CALL       = 21;//redis调用失败
	const LOG_DB_CALL          = 30;//db调用失败
	const LOG_CACHE_INSTANCE   = 40;//cache获取实例失败
	const LOG_CACHE_EMPTY      = 41;//cache查询为空
	const LOG_DL_CALL          = 50;//DL调用失败
	const LOG_SERVICE_CALL     = 60;//内部service调用失败
	
	
	/**
	 * @desc 根据信息生成日志的字符串，以便统一日志格式
	 * @param string $function 调用者函数名称，调用时只填__FUNCTION__
	 * @param string $model 模块名，见const LOG_XX_XX定义
	 * @param string $callee 被调用的操作名称
	 * @param string $arrReq 输入参数
	 * @param string $arrRes 输出参数
	 * @return
	 * <AUTHOR> 2015-07-06
	 */
	private static function makeLogStr($model,$arrReq = '',$arrRes = '',$callee = ''){
	    $arrTrace = debug_backtrace();
	    $depth = 2;
	    if (isset($arrTrace[$depth]['function'])){
	        $function = $arrTrace[$depth]['function'];
	    }
	    switch ($model){
	    	case self::LOG_PARAM_INVALID:
	    	    return $function . " input params invalid.input: [". serialize($arrReq) . "]";
	    	case self::LOG_REDIS_INIT:
	    	    return $function . " init redis failed.";
	    	case self::LOG_REDIS_CALL:
	    	    return $function . " call redis " . $callee . " failed.input: [" . serialize($arrReq) . "] output: [" . serialize($arrRes) . "]";
	    	case self::LOG_DB_CALL:
	    	    if ( empty($callee) && isset($arrReq['function'] )){
	    	        $callee = $arrReq['function'];
	    	    }
	    	    return $function . " call db " . $callee . " failed.input: [" . serialize($arrReq)."] output: [" . serialize($arrRes) . "]";
	    	case self::LOG_CACHE_INSTANCE:
	    	    return $function . " get cache instance failed.";
	    	case self::LOG_CACHE_EMPTY:
	    	    return $function . " query cache " . $callee . " empty.input: [" . serialize($arrReq)."]  output: [".serialize($arrRes) . "]";
	    	case self::LOG_DL_CALL:
	    	    return $function . " call dl ". $callee ." failed.input: [" . serialize($arrReq)."] output: [".serialize($arrRes) . "]";
	    	case self::LOG_SERVICE_CALL:
	    	    return $function . " call service ". $callee ." failed.input: [" . serialize($arrReq)."] output: [".serialize($arrRes) . "]";
	    	case self::LOG_UNKNOWN:
	    	    return $function . ' '. $callee ." [" . serialize($arrReq)."] [".serialize($arrRes) . "]";
	    	default:
	    	    return $function .' '. $model;//默认情况下只返回首个参数，用来支持旧的调用方式warning('xxxx');
	    }
	    return $function .' '. $model;;
	}
	
	/**
	 * @param unknown $mode_name
	 * @return
	 */
	public static function fatal($model,$arrReq = '',$arrRes = '',$callee = '')
	{
	    $strLog = self::makeLogStr($model,$arrReq,$arrRes,$callee);
	    //这里不直接调用self::error是因为这样会增加debug_backtrace的层次，导致function名称错误
        $arrRet = self::_getFileAndLine(0);
        return Bingo_Log::fatal(self::$_strLogPrefix . $strLog . $arrRet['otherMsg'], '', $arrRet['file'], $arrRet['line'], 0);
	}
	
	/**
	 * @param unknown $mode_name
	 * @return
	 */
	public static function warning($model,$arrReq = '',$arrRes = '',$callee = '')
	{
	    $strLog = self::makeLogStr($model,$arrReq,$arrRes,$callee);
	    //这里不直接调用self::warning是因为这样会增加debug_backtrace的层次，导致function名称错误
	    $arrRet = self::_getFileAndLine(0);
        return Bingo_Log::warning(self::$_strLogPrefix . $strLog . $arrRet['otherMsg'], '', $arrRet['file'], $arrRet['line'], 0);
	}
	
	/**
	 * @param unknown $mode_name
	 * @return
	 */
	public static function notice($model,$arrReq = '',$arrRes = '',$callee = '')
	{
	    $strLog = self::makeLogStr($model,$arrReq,$arrRes,$callee);
	    $arrRet = self::_getFileAndLine(0);
        return Bingo_Log::notice(self::$_strLogPrefix . $strLog . $arrRet['otherMsg'], '', $arrRet['file'], $arrRet['line'], 0);
	}
	
	/**
	 * @param unknown $mode_name
	 * @return
	 */
	public static function trace($model,$arrReq = '',$arrRes = '',$callee = '')
	{
	    $strLog = self::makeLogStr($model,$arrReq,$arrRes,$callee);
	    $arrRet = self::_getFileAndLine(0);
        return Bingo_Log::trace(self::$_strLogPrefix . $strLog . $arrRet['otherMsg'], '', $arrRet['file'], $arrRet['line'], 0);
	}
	
	/**
	 * @param unknown $mode_name
	 * @return
	 */
	public static function debug($model,$arrReq = '',$arrRes = '',$callee = '')
	{
	    $strLog = self::makeLogStr($model,$arrReq,$arrRes,$callee);
	    $arrRet = self::_getFileAndLine(0);
        return Bingo_Log::debug(self::$_strLogPrefix . $strLog . $arrRet['otherMsg'], '', $arrRet['file'], $arrRet['line'], 0);
	}
	
	/**
	 * @param unknown $mode_name
	 * @return
	 */
	public static function error($model,$arrReq = '',$arrRes = '',$callee = '')
	{
	    $strLog = self::makeLogStr($model,$arrReq,$arrRes,$callee);
	    $arrRet = self::_getFileAndLine(0);
        return Bingo_Log::error(self::$_strLogPrefix . $strLog . $arrRet['otherMsg'], '', $arrRet['file'], $arrRet['line'], 0);
	}
}
