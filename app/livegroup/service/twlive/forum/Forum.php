<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Forum.php
 * <AUTHOR>
 * @date 2016/02/26 15:37:32
 * @brief  forum模块接口入口
 **/

define('MODULE', 'livegroup');
class Service_Twlive_Forum_Forum{
    /**
     * @param:
     * @return:
     */
    public static function getVirtualDirName($arrInput) {
        return Service_Twlive_Forum_VirtualDir::getVirtualDirName($arrInput);
    }

    //////   发帖、搬贴入口相关接口
    /**
     * @brief: 接arch nmq发回复命令
     * @param:
     * @return:
     */
    public static function addTWPost($arrInput){
        return Service_Twlive_Forum_UserThreadList::addTWPost($arrInput);
    }

    /**
     * @brief: 接arch nmq发贴命令
     * @param:
     * @return:
     */
    public static function addTWThread($arrInput) {
        return Service_Twlive_Forum_UserThreadList::addTWThread($arrInput);
    }
    /**
     * @brief: 接arch nmq删贴命令
     * @param:
     * @return:
     */
    public static function deleteTWLive($arrInput) {
        return Service_Twlive_Forum_UserThreadList::deleteTWLive($arrInput);
    }
    /**
     * @brief:搬贴接口直接调用 
     * @param:
     * @return:
     */
    public static function rollbackTWMovedThread($arrInput) {
        return Service_Twlive_Forum_UserThreadList::rollbackTWMovedThread($arrInput);
    }
    /**
     * @brief:搬贴接口直接调用 
     * @param:
     * @return:
     */
    public static function addTWMovedThread($arrInput) {
        return Service_Twlive_Forum_UserThreadList::addTWMovedThread($arrInput);
    }


    //////////// 头条相关
    /**
     * @brief:抢头条
     * @param:
     * @return:
     */
    public static function incrHotTWLive($arrInput) {
        return Service_Twlive_Forum_HeadlineList::incrHotTWLive($arrInput);
    }
    /**
     * @brief:搬贴接口直接调用 
     * @param:
     * @return:
     */
    public static function incrHotTWLiveCount($arrInput) {
        return Service_Twlive_Forum_HeadlineList::incrHotTWLiveCount($arrInput);
    }
    /**
     * @brief:获取头条列表
     * @param:
     * @return:
     */
    public static function getTWHotLiveList($arrInput) {
        return Service_Twlive_Forum_HeadlineList::getTWHotLiveList($arrInput);
    }
    /**
     * @brief:获取头条
     * @param:
     * @return:
     */
    public static function getTWHeadline($arrInput) {
        return Service_Twlive_Forum_HeadlineList::getTWHeadline($arrInput);
    }

    /**
     * @desc
     * 	根据threadId获得直播贴是否上过头条
     * @param
     * @return
     */
    public static function getHeadLineInfo($arrInput) {
    	return Service_Twlive_Forum_HeadlineList::getHeadLineInfo($arrInput);
    }



    ////////// 其他
	/**
     * @desc
     * 判断能否开图文直播贴
     * @param
     * @return
     */
    public static function isOpenTWZhiBo($arrInput){
    	return Service_Twlive_Forum_Other::isOpenTWZhiBo($arrInput);
    }
    /**
     * @param
     * @return
     */
    public static function setFrsStoreShowTS($arrInput){
        return Service_Core_TWLive::setFrsStoreShowTS($arrInput);
    }
    
    /**
     * @param
     * @return
     */
    public static function getFrsStoreShowTS($arrInput){
        return Service_Core_TWLive::getFrsStoreShowTS($arrInput);
    }
}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
