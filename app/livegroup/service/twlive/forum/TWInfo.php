<?php
/***************************************************************************
 *
* Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
*
**************************************************************************/
/**
 * @file TWInfo.php
* <AUTHOR>
* @date 2015/10/22 10:47:53
* @brief 图文直播贴信息
**/
define('MODULE', 'livegroup');
class Service_Twlive_Forum_TWInfo {
	
	const LIVEGROUP_HEADLINE_HISTORY = 'lg_headline_history'; 
	const LIVEGROUP_CACHE_FRS_TWINFO = '_lg_cache_frs_twinfo';
	const LIVEGROUP_HEADLINE_TID = 'lg_headline_tid';
	const LIVEGROUP_FRS_TWINFO = 'lg_frs_twinfo';
	const LIVEGROUP_LABEL_COUNT_MAX = 10;
	
	/**
	 * @desc
	 * 	获得图文直播贴子信息
	 * @param:
	 * 	forumId:int 吧id
	 * 	threadIds:array 帖子id
	 * 	labelCount:需要获取的印象标签数
	 * @return:
	 */
	public static function getTWInfosByTids($arrInput) {
		if (!isset($arrInput['forumId']) ||  !isset($arrInput['threadIds']) ){
			Lib_Live_Log::warning(Lib_Live_Log::LOG_PARAM_INVALID,$arrInput);
			return Service_Base_Live::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$forumId = $arrInput['forumId'];
		$threadIds = Tieba_Service::getArrayParams($arrInput,'threadIds');
		$labelCount = intval($arrInput['labelCount']);
		if (empty($threadIds) || $forumId <= 0){
			Lib_Live_Log::warning(Lib_Live_Log::LOG_PARAM_INVALID,$arrInput);
			return Service_Base_Live::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	
		Bingo_Timer::start('call_getTWMapInfoByTids');
		$twzhiboInfo = self::getTWMapInfoByTids($threadIds, $forumId);
		Bingo_Timer::end('call_getTWMapInfoByTids');
	
		if ($labelCount > 0 && $labelCount < self::LIVEGROUP_LABEL_COUNT_MAX) {
			foreach ($twzhiboInfo as $threadId => &$threadInfo) {
				if (isset($threadInfo['labels']) && isset($threadInfo['label_info'])) {
					$threadInfo['labelInfo'] = $threadInfo['labels'] = array_splice($threadInfo['labels'], 0, $labelCount, array());
					$threadInfo['label_info'] = array_splice($threadInfo['label_info'], 0, $labelCount, array());
				}
			}
		}
		
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
			'twzhiboInfo' => $twzhiboInfo,
		);
		return $arrOutput;
	}
	
	/**
	 * @desc
	 * 	根据输入threadid顺序，输出结果，内部调用不做参数验证
	 * @param:
	 * @return:
	 */
	private static function getTWMapInfoByTids($arrThreadIds, $forumId) {
		$twzhiboInfo = array();
		$twzhiboBaseInfo = array();
		Bingo_Timer::start('call_getThreadInfoFromCache');
		$twzhiboCacheInfo = self::getThreadInfoFromCache($arrThreadIds);
		Bingo_Timer::end('call_getThreadInfoFromCache');
		if (count($twzhiboCacheInfo) < count($arrThreadIds)) {
			$baseThreadIds = array();
			foreach ($arrThreadIds as $threadId) {
				if ($threadId > 0 && !isset($baseThreadIds[$threadId])) {
					$baseThreadIds[] = $threadId;
				}
			}
			Bingo_Timer::start('call_getThreadInfoFromBaseNew');
			$twzhiboBaseInfo = self::getThreadInfoFromBaseNew($baseThreadIds, $forumId);
			Bingo_Timer::end('call_getThreadInfoFromBaseNew');
		}
		$twzhiboInfo = $twzhiboCacheInfo + $twzhiboBaseInfo;
		return $twzhiboInfo;
	}
	
	/**
	 * @desc 
	 * 	从缓存中获取数据
	 * @param:
	 * @return:
	 */
	public function getThreadInfoFromCache($arrThreadIds) {
		//memcache空间不够用，因此使用redis
		if (!Service_Base_Live::initRedis()){
			Lib_Live_Log::fatal(Lib_Live_Log::LOG_REDIS_INIT);
			return Service_Base_Live::errRet(Tieba_Errcode::ERR_BUSI_CONN_REDIS_FAIL);
		}
		$arrInfos['reqs'] = array();
		$twzhiboInfo = array();
		foreach ($arrThreadIds as $tid) {
			$arrInfos['reqs'][] = array('key' => $tid. '_' . self::LIVEGROUP_FRS_TWINFO);
		}
	
		if (!empty($arrInfos['reqs'])) {
			$res = Service_Base_Live::$new_redis->GET($arrInfos);
			if(!$res || $res['err_no'] != 0) {
				Lib_Live_Log::warning(Lib_Live_Log::LOG_REDIS_CALL,$arrInfos,$res,'GET');
			} else {
				foreach ($res['ret'] as $key => $value) {
					$arrKey = explode("_", $key);
					$threadId = $arrKey[0];
					if (isset($value)) {
						$twzhiboInfo[$threadId] = Bingo_String::json2array($value,Bingo_Encode::ENCODE_UTF8);
					}
				}
			}
		}
		return $twzhiboInfo;
		
		/*
		$twzhiboInfo = array();
		$objCache = Lib_Util_Cache::getInstance();
 		$arrCacheReqs = array();
 		foreach ($arrThreadIds as $tid) {
 			$arrCacheReqs[] = $tid . self::LIVEGROUP_CACHE_FRS_TWINFO;
 		}
		if (!empty($objCache)){
			$arrCacheData = $objCache->mget($arrCacheReqs);
			if (!empty($arrCacheData)){//缓冲中有数据
				foreach ($arrCacheData as $key => $value) {
					$arrKey = explode("_", $key);
					$threadId = $arrKey[0];
					if (isset($value)) {
						$twzhiboInfo[$threadId] = $value;
					}
				}
			}
		}
		return $twzhiboInfo;
		*/
	}
	
	/**
	 * @desc 
	 * 	根据threadId获得直播贴信息及相关主播信息
	 * @param
	 * 	forumId:int 吧id
	 *  threadIds:array 帖子id
	 * @return
	 */
	public static function getThreadUserInfo($arrInput) {
		if (!isset($arrInput['forumId']) ||  !isset($arrInput['threadIds']) ){
			Lib_Live_Log::warning(Lib_Live_Log::LOG_PARAM_INVALID,$arrInput);
            return Service_Base_Live::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		$forumId = $arrInput['forumId'];
		$threadIds = Tieba_Service::getArrayParams($arrInput, 'threadIds');
		if ($forumId <= 0){
			Lib_Live_Log::warning(Lib_Live_Log::LOG_PARAM_INVALID,$arrInput);
			return Service_Base_Live::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		foreach ($threadIds as $key => $threadId){
			if ($threadId <= 0){
				unset($threadIds[$key]);
				continue;
			}
		}
		$twzhiboInfo = array();
		$userIds = array();
		Bingo_Timer::start('call_mgetThread');
		if (!empty($threadIds)){
			$arrReq = array(
				'thread_ids' => $threadIds,
				'need_abstract' => 1,
				'forum_id' => $forumId,
				'need_photo_pic' => 0,
				'need_user_data' => 0,
				'icon_size' => 0,
				'need_forum_name' => 1,
				'need_post_content' => 0,
				'call_from' => 'client_frs',
			);
			$arrRes = Tieba_Service::call('post', 'mgetThread', $arrReq, null, null, 'post', 'php', 'utf-8');
			if(!$arrRes || $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				Lib_Live_Log::fatal(Lib_Live_Log::LOG_SERVICE_CALL,$arrReq,$arrRes,'mgetThread');
	            return Service_Base_Live::errRet( Tieba_Errcode::ERR_CALL_SERVICE_FAIL );
			}
				
			$threadList = $arrRes['output']['thread_list'];
			if (isset($threadList)) {
				foreach($threadList as $threadId => $threadInfo) {
					$TWLiveInfo['thread_id'] = $TWLiveInfo['threadId'] = $threadInfo['thread_id'];
					$TWLiveInfo['title'] = trim(html_entity_decode($threadInfo['title'], ENT_COMPAT,'UTF-8'));
					$TWLiveInfo['content'] =  trim(html_entity_decode($threadInfo['abstract'],ENT_COMPAT,'UTF-8'));
					$TWLiveInfo['user_id'] = $TWLiveInfo['userId'] = $threadInfo['user_id'];
					$TWLiveInfo['media'] = $threadInfo['media'];
					$TWLiveInfo['raw_abstract_media'] = $threadInfo['raw_abstract_media'];
					$TWLiveInfo['reply_num'] = $TWLiveInfo['replyNum'] = $threadInfo['post_num'];
					$TWLiveInfo['forum_id'] = $TWLiveInfo['forumId'] = $threadInfo['forum_id'];
					$TWLiveInfo['forum_name'] = $TWLiveInfo['forumName'] = $threadInfo['forum_name'];
					$TWLiveInfo['livecover_src'] = $TWLiveInfo['livecoverSrc'] = $threadInfo['livecover_src'];
					$TWLiveInfo['last_modified_time'] = $TWLiveInfo['lastModifiedTime'] = $threadInfo['last_modified_time'];
					$TWLiveInfo['last_post_id'] = $TWLiveInfo['lastPostId'] = $threadInfo['last_post_id'];
					$TWLiveInfo['freq_num'] = $TWLiveInfo['freqNum'] = $threadInfo['freq_num'];
					$TWLiveInfo['is_deleted'] = $threadInfo['is_deleted'];
					
					if ($threadInfo['user_id'] > 0){
						$userIds[] = $threadInfo['user_id'];
					}
					$twzhiboInfo[$TWLiveInfo['thread_id']] = $TWLiveInfo;
				}
			}
		}
		Bingo_Timer::end('call_mgetThread');
		
		Bingo_Timer::start('call_mgetUserDataEx');
		if (!empty($userIds)) {
			$arrUserReq = array(
				'user_id' => $userIds,
				'need_follow_info' => 1,
			);
			$arrUserRes = Tieba_Service::call('user', 'mgetUserDataEx', $arrUserReq, null, null, 'post', 'php', 'utf-8');
			if(!$arrUserRes || $arrUserRes['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				Lib_Live_Log::fatal(Lib_Live_Log::LOG_SERVICE_CALL,$arrUserReq,$arrUserRes,'mgetUserDataEx');
	            return Service_Base_Live::errRet( Tieba_Errcode::ERR_CALL_SERVICE_FAIL );
			}
			$userInfos = $arrUserRes['user_info'];
		}
		Bingo_Timer::end('call_mgetUserDataEx');
		
		foreach($twzhiboInfo as $key => &$value) {
			if (isset($userInfos[$value['userId']])) {
				$value['userInfo'] = $value['user'] = array(
					'id' => $value['userId'],
					'userId' =>  $value['userId'],
					'name' => $userInfos[$value['userId']]['user_name'],
					'userName' => $userInfos[$value['userId']]['user_name'],
					'fansNum' => $userInfos[$value['userId']]['followed_count'],
					'fans_num' => $userInfos[$value['userId']]['followed_count'],
					'fansNickname' => $userInfos[$value['userId']]['fans_nickname'],
					'fans_nickname' => $userInfos[$value['userId']]['fans_nickname'],
					'portrait' => Tieba_Ucrypt::encode($value['userId'], $userInfos[$value['userId']]['user_name'],$userInfos[$value['userId']]['portrait_time']),
				);
			}
		}
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
			'twzhiboInfo' => $twzhiboInfo ,
		);
		return $arrOutput;
	}
	
	/**
	 * @desc
	 * 	根据threadId获得直播贴是否上过头条
	 * @param
	 * 	forumId:int 吧id
	 *  threadId:int 帖子id
	 * @return
	 * 	threadId:int 帖子id
	 * 	isHeadline:int 是否上过头条
	 */
	public static function getHeadLineInfo($arrInput) {
		if (!isset($arrInput['forumId']) ||  !isset($arrInput['threadId']) ){
			Lib_Live_Log::warning(Lib_Live_Log::LOG_PARAM_INVALID,$arrInput);
			return Service_Base_Live::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		Bingo_Timer::start('call_getHeadLineInfo');
		
		$forumId = intval($arrInput['forumId']);
		$threadId = intval($arrInput['threadId']);
		if ($forumId <= 0 ||  $threadId <= 0 ){
			Lib_Live_Log::warning(Lib_Live_Log::LOG_PARAM_INVALID,$arrInput);
			return Service_Base_Live::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		if (!Service_Base_Live::initRedis()){
			Lib_Live_Log::fatal(Lib_Live_Log::LOG_REDIS_INIT);
			return Service_Base_Live::errRet(Tieba_Errcode::ERR_BUSI_CONN_REDIS_FAIL);
		}
		$arrReq = array(
			'key' => $forumId .'_' . self::LIVEGROUP_HEADLINE_HISTORY,
			'member' => $threadId,
		);
		$isHeadline = 0;
		$arrRes = Service_Base_Live::$redis->ZSCORE($arrReq);
		if (!$arrRes || $arrRes['err_no'] != 0) {
			Lib_Live_Log::fatal(Lib_Live_Log::LOG_REDIS_CALL,$arrReq,$arrRes,'ZSCORE');
		} else {
			$isHeadline = intval($arrRes['ret'][$arrReq['key']]) > 0 ? 1 : 0;
		}
		Bingo_Timer::end('call_getHeadLineInfo');
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
			'threadId' => $threadId,
			'isHeadline' => $isHeadline ,
		);
		return $arrOutput;
	}
	
	/**
	 * @desc:清空一个月前的上头条记录 
	 * @param:
	 * @return:
	 */
	private static function clearHeadlineHistoryItem($forumId) {
		if(!Service_Base_Live::initRedis()){
			Lib_Live_Log::fatal(Lib_Live_Log::LOG_REDIS_INIT);
			return Service_Base_Live::errRet(Tieba_Errcode::ERR_BUSI_CONN_REDIS_FAIL);
		}
		$clearTidReqKey = $forumId.'_'.self::LIVEGROUP_HEADLINE_HISTORY;
		//清理一个月以前的数据
		$clearTs = time() - 2592000;//30*24*3600
		$clearTidScoreReq = array(
			'key' => $clearTidReqKey,
			'min' => 0,
			'max' => $clearTs,
		);
		$clearTidScoreRes = Service_Base_Live::$redis->ZREMRANGEBYSCORE($clearTidScoreReq);
		if(!$clearTidScoreRes || $clearTidScoreRes['err_no'] != 0) {
			Lib_Live_Log::warning(Lib_Live_Log::LOG_REDIS_CALL,$clearTidScoreReq,$clearTidScoreRes,'ZREMRANGEBYSCORE');
			return false;
		}
		return true;
	}
	
	/**
	 * 获取图文直播内容
	 * @param:
	 * @return:
	 */
	public static function getThreadInfoFromBaseNew($arrThreadIds, $forumId) {
		if (!Service_Base_Live::initRedis()){
			Lib_Live_Log::fatal(Lib_Live_Log::LOG_REDIS_INIT);
			return Service_Base_Live::errRet(Tieba_Errcode::ERR_BUSI_CONN_REDIS_FAIL);;
		}
		if ($forumId <= 0){
			Lib_Live_Log::warning(Lib_Live_Log::LOG_PARAM_INVALID,$arrInput);
			return Service_Base_Live::errRet( Tieba_Errcode::ERR_PARAM_ERROR );
		}
	
		$objRalMulti = new Tieba_Multi('multi_call_getThreadInfoFromBaseNew');
		foreach ($arrThreadIds as $key => $threadId){
			if ($threadId <= 0){
				unset($arrThreadIds[$key]);
				continue;
			}
			$arrMultiInput = array(
				'serviceName' => 'post',
				'method' => 'getInvertPostsByThreadId',
				'ie' => 'utf-8',
				'input' => array(
					'thread_id' => $threadId,
					'res_num' => 1,
					'offset' => 0,
					'see_author' => 0,
					'has_comment' => 0,
					'has_mask' => 0,
					'structured_content' => 0,
				),
			);
			$tmpKey = 'getInvertPostsByThreadId_' . $threadId;
			$objRalMulti->register($tmpKey,new Tieba_Service('post'),$arrMultiInput);
			
			$arrMultiInput = array(
				'serviceName' => 'livegroup',
				'method' => 'getHeadLineInfo',
				'ie' => 'utf-8',
				'input' => array(
					'threadId' => $threadId,
					'forumId' => $forumId,
				),
			);
			$tmpKey = 'getHeadLineInfo_' . $threadId;
			$objRalMulti->register($tmpKey,new Tieba_Service('livegroup'),$arrMultiInput);
		}
		
		if (!empty($arrThreadIds)){
			$arrMultiInput = array(
				'serviceName' => 'livegroup',
				'method' => 'queryLabelByThreadIdsForLivePost',
				'ie' => 'utf-8',
				'input' => array(
					'threadIds' => $arrThreadIds,
					'topN' => self::LIVEGROUP_LABEL_COUNT_MAX,
				),
			);
			$objRalMulti->register('queryLabelByThreadIdsForLivePost',new Tieba_Service('livegroup'),$arrMultiInput);
			
			$arrMultiInput = array(
				'serviceName' => 'livegroup',
				'method' => 'getThreadUserInfo',
				'ie' => 'utf-8',
				'input' => array(
					'threadIds' => $arrThreadIds,
					'forumId' => $forumId,
				),
			);
			$objRalMulti->register('getThreadUserInfo',new Tieba_Service('livegroup'),$arrMultiInput);
			$objRalMulti->call();
			$arrRet = $objRalMulti->results;
			
			//核心功能
			if (!$arrRet['getThreadUserInfo'] || $arrRet['getThreadUserInfo']['errno'] != Tieba_Errcode::ERR_SUCCESS){
				Lib_Live_Log::fatal(Lib_Live_Log::LOG_SERVICE_CALL,'',$arrRet,'getThreadUserInfo');
				return Service_Base_Live::errRet( Tieba_Errcode::ERR_CALL_SERVICE_FAIL );
			}
			if (isset($arrRet['getThreadUserInfo']['twzhiboInfo'])){
				$twzhiboInfo = $arrRet['getThreadUserInfo']['twzhiboInfo'];
				unset($arrRet['getThreadUserInfo']);
			}
			
			//非核心功能
			if ($arrRet['queryLabelByThreadIdsForLivePost']['errno'] == Tieba_Errcode::ERR_SUCCESS 
					&& isset($arrRet['queryLabelByThreadIdsForLivePost']['labelInfo'])){
				$labelInfo = $arrRet['queryLabelByThreadIdsForLivePost']['labelInfo'];
				unset($arrRet['queryLabelByThreadIdsForLivePost']);
			}
			
			$postNum = array();
			$isHeadline = array();
			foreach ($arrRet as $key => $value){
				if ($value['errno'] == Tieba_Errcode::ERR_SUCCESS){
					if (strpos($key, 'getInvertPostsByThreadId') !== false) {
						$TWLiveInfo = $value['output']['output'][0];
						if (isset($TWLiveInfo['post_infos'][0])) {
							$postNum[$TWLiveInfo['thread_id']] = $TWLiveInfo['post_infos'][0]['post_no'];
						}
						continue;
					}
					if (strpos($key, 'getHeadLineInfo') !== false) {
						$isHeadline[$value['threadId']] = $value['isHeadline'];
						continue;
					}
				}
			}
		}
		
		
		if (rand(0,99) == 0){
			self::clearHeadlineHistoryItem($forumId);
		}
		return self::setThreadInfoCache($forumId,$twzhiboInfo,$postNum,$labelInfo,$isHeadline);
	}
	
	/**
	 * @desc
	 * 	设置缓冲,内部调用，不做参数验证
	 * @param
	 * @return
	 */
	private static function setThreadInfoCache($forumId,$twzhiboInfo,$postNum,$labelInfo,$isHeadline){
		//memcached缓存不够用，使用redis
		if (!Service_Base_Live::initRedis()){
			Lib_Live_Log::fatal(Lib_Live_Log::LOG_REDIS_INIT);
			return Service_Base_Live::errRet(Tieba_Errcode::ERR_BUSI_CONN_REDIS_FAIL);;
		}
		Bingo_Timer::start('call_about_cache');
		$topTid = 0;
		$getTidReq = array(
			'key' => $forumId. '_' . self::LIVEGROUP_HEADLINE_TID,
		);
		$getTidRes = Service_Base_Live::$redis->GET($getTidReq);
		if(!$getTidRes || $getTidRes['err_no'] != 0) {
			Lib_Live_Log::warning(Lib_Live_Log::LOG_REDIS_CALL,$getTidReq,$getTidRes,'GET');
		} else {
			$topTid = intval($getTidRes['ret'][$getTidReq['key']]);
		}
		$arrSetReqs['reqs'] = array();
		$arrExpireReqs['reqs'] = array();
		foreach($twzhiboInfo as $key => &$value) {
			if (isset($postNum[$value['threadId']])) {
				$value['post_num'] = $value['postNum'] = $postNum[$value['threadId']];
			}
			if (isset($labelInfo[$value['threadId']])) {
				$value['labelInfo'] = $value['label_info'] = $value['labels'] = $labelInfo[$value['threadId']];
			} else {
				$value['labelInfo'] = $value['label_info'] = $value['labels'] = array();
			}
			if (isset($isHeadline[$value['threadId']])) {
				$value['is_headline'] = $value['isHeadline'] = $isHeadline[$value['threadId']];
			}
			if ($topTid > 0 && $topTid==$value['threadId']) {
				$value['is_new_headline'] = $value['isNewHeadline'] = 1;
			} else {
				$value['is_new_headline'] = $value['isNewHeadline'] = 0;
			}
		
			$cacheKey = $value['threadId']. '_' .self::LIVEGROUP_FRS_TWINFO;
			$arrSetReqs['reqs'][] = array(
				'key'=> $cacheKey,
				'value'=> Bingo_String::array2json($value,Bingo_Encode::ENCODE_UTF8) ,
			);
			$arrExpireReqs['reqs'][]= array(
				'key' => $cacheKey,
				'seconds' => 30,
			);
		}
		if(!empty($arrSetReqs['reqs'])){
			$res =  Service_Base_Live::$new_redis->SET($arrSetReqs);
			if(!$res || $res['err_no'] != 0) {
				Lib_Live_Log::warning(Lib_Live_Log::LOG_REDIS_CALL,$arrSetReqs,$res,'SET');
			}
		}
		if(!empty($arrExpireReqs['reqs'])){
			$res =  Service_Base_Live::$new_redis->EXPIRE($arrExpireReqs);
			if(!$res || $res['err_no'] != 0) {
				Lib_Live_Log::warning(Lib_Live_Log::LOG_REDIS_CALL,$arrExpireReqs,$res,'EXPIRE');
			}
		}
		Bingo_Timer::end('call_about_cache');
		return $twzhiboInfo;
		/*
		if (!Service_Base_Live::initRedis()){
			Lib_Live_Log::fatal(Lib_Live_Log::LOG_REDIS_INIT);
			return Service_Base_Live::errRet(Tieba_Errcode::ERR_BUSI_CONN_REDIS_FAIL);;
		}
		Bingo_Timer::start('call_about_cache');
		
		$topTid = 0;
		$getTidReq = array(
			'key' => $forumId. '_' . self::LIVEGROUP_HEADLINE_TID,
		);
		$getTidRes = Service_Base_Live::$redis->GET($getTidReq);
		if(!$getTidRes || $getTidRes['err_no'] != 0) {
			Lib_Live_Log::warning(Lib_Live_Log::LOG_REDIS_CALL,$getTidReq,$getTidRes,'GET');
		} else {
			$topTid = intval($getTidRes['ret'][$getTidReq['key']]);
		}

		
		$arrSetReqs = array();
		foreach($twzhiboInfo as $key => &$value) {
			if (isset($postNum[$value['threadId']])) {
				$value['post_num'] = $value['postNum'] = $postNum[$value['threadId']];
			}
			if (isset($labelInfo[$value['threadId']])) {
				$value['labelInfo'] = $value['label_info'] = $value['labels'] = $labelInfo[$value['threadId']];
			} else {
				$value['labelInfo'] = $value['label_info'] = $value['labels'] = array();
			}
			if (isset($isHeadline[$value['threadId']])) {
				$value['is_headline'] = $value['isHeadline'] = $isHeadline[$value['threadId']];
			}
			if ($topTid > 0 && $topTid==$value['threadId']) {
				$value['is_new_headline'] = $value['isNewHeadline'] = 1;
			} else {
				$value['is_new_headline'] = $value['isNewHeadline'] = 0;
			}
			$arrSetReqs[] = array(
				'key'=> $value['threadId'] . self::LIVEGROUP_CACHE_FRS_TWINFO,
				'value'=> $value,
			);
		}
			
		$objCache = Lib_Util_Cache::getInstance();
		if (!empty($objCache)){
			$arrCacheData = $objCache->madd($arrSetReqs,30);
		}
		Bingo_Timer::end('call_about_cache');
		return $twzhiboInfo;
		*/
	}
}

?>
