<?php
/***************************************************************************
 *
* Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
*
**************************************************************************/
/**
 * <AUTHOR>
 * @brief
 *
 **/
class createAction extends Util_BaseAction {

    private $_inputParamArray = array();

    private function _getPrivateInfo() {
        $this->_inputParamArray['method'] = Bingo_Http_Request::isPost() ? "POST" : '';    //请求类型
        $this->_inputParamArray['key'] = Bingo_Http_Request::get('method','');   //POST识别字段（关键）
        $this->_inputParamArray['suite_id'] = intval(Bingo_Http_Request::get('sid',''));     //套件ID
        $this->_inputParamArray['task_id'] = Bingo_Http_Request::get('tid','');     //任务ID
        $this->_inputParamArray['task_name'] = Bingo_Http_Request::get('tname','');       //任务名称
        $this->_inputParamArray['type'] = intval(Bingo_Http_Request::get('type',''));   //测试框架
        $this->_inputParamArray['from'] = intval(Bingo_Http_Request::get('from',''));   //任务来源
        $this->_inputParamArray['features'] = Bingo_Http_Request::get('features','');   //任务扩展属性
        return $this->_checkParam();
    }

    private function _checkParam() {
        $this->_inputParamArray['features'] = Libs_TaskConst::implodeFeatures( $this->_inputParamArray['features']);
        $this->_inputParamArray['owner_Name'] = $this->_user_info['user_name'];
        if(!$this->_inputParamArray || !is_array($this->_inputParamArray)) {
            Bingo_Log::warning(__CLASS__ . ' input params is empty!');
            return false;
        }
        if($this->_inputParamArray['key'] !== 'create' && $this->_inputParamArray['method'] !== 'POST' ) {
            Bingo_Log::warning(__CLASS__ . ' input params is empty!');
            return false;
        }
        unset($this->_inputParamArray['key']);
        unset($this->_inputParamArray['task_id']);
        unset($this->_inputParamArray['method']);

        foreach($this->_inputParamArray as $key => $value) {
            if($value === '') {
                Bingo_Log::warning(__CLASS__ . " $key input params is invalid! $value");
                return false;
            }
            if($key == 'suite_id' || $key == 'type') {
                if(!is_numeric($value) || intval($value) <= 0){
                    Bingo_Log::warning(__CLASS__ . " $key input params is invalid! $value");
                    return false;
                }
            }
            if($key == 'from') {
                if(!is_numeric($value) || intval($value) < 0) {
                    Bingo_Log::warning(__CLASS__ . " $key input params is invalid! $value");
                    return false;
                }
            }
        }
        $this->_inputParamArray['mark'] = '';
        return true;
    }
	
	public function execute(){
        if(! $this->_getPrivateInfo()){
            Bingo_Log::warning (  __CLASS__ . ' input params invalid!' );
            $this->_intErrorNo = Libs_Errno::ERR_PARAM_ERROR;
            return $this->_renderJson();
        }

        //调用创建任务的服务
        $arrInput = $this->getQueryInputArray();
        $arrOutput = Util_Service::call('automation', 'insertTask', $arrInput);
        if (!isset($arrOutput['errno']) || $arrOutput['errno'] !== Libs_Errno::SUCCESS) {
            Bingo_Log::warning('task::insertTask service call fail!');
            $this->_intErrorNo = isset($arrOutput['errno']) ? $arrOutput['errno'] :Libs_Errno::ERR_CALL_SERVICE_FAIL;
            return $this->_renderJson();
        }
		return $this->_renderJson();
	}

    private function getQueryInputArray() {
        $inputArray = array();
        foreach($this->_inputParamArray as $key => $value) {
            $inputArray[$key] = $value;
        }
        return $inputArray;
    }

}
