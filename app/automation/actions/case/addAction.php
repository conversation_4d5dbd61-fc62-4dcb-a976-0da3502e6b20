<?php
/***************************************************************************
 *
* Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
*
**************************************************************************/
/**
 * <AUTHOR>
 * @brief
 *
 **/
class addAction extends Util_BaseAction {
	
	public function execute(){
		$arrInput = $this->getAndCheckParam();
		Bingo_Log::warning(Bingo_String::array2json($arrInput));
		if(! $arrInput){
			Bingo_Log::warning (  __CLASS__ . ' input params invalid' );
			$this->_intErrorNo = Tieba_Errcode::ERR_PARAM_ERROR; 
			return $this->_renderJson();
		}
		$arrRes = Util_Service::call('automation', 'addCase',$arrInput);
		if (!isset($arrRes['errno']) || $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('service call fail,addCase');
			$this->_intErrorNo = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
			return $this->_renderJson();
		}
		$this->_arrTpl = $arrRes;
		return $this->_renderJson();
	}
	
	protected function getAndCheckParam(){
		$case_name = Bingo_Http_Request::get('case_name','');	
		$description = Bingo_Http_Request::get('description','');	
		$user_info = $this->_getUserInfo();
		$creator = $user_info['user_name'];
		$modifier = $user_info['user_name'];
		Bingo_Log::warning(Bingo_String::array2json($user_info));
		$productline_id = Bingo_Http_Request::get('productline_id',0);
		$frame_id = Bingo_Http_Request::get('frame_id',0);//default frame_id
		$status = Bingo_Http_Request::get('status',1);
		$priority = Bingo_Http_Request::get('priority',1);
		$url  = Bingo_Http_Request::get('url','');
		$request_method = Bingo_Http_Request::get('request_method','GET');
		$cmd  = intval(Bingo_Http_Request::get('cmd',0));
		$content = Bingo_Http_Request::get('content','');
		$ignore_data = Bingo_Http_Request::get('ignore_data','');
		$parameter_list = Bingo_Http_Request::get('parameter_list','');
		$assert_list = Bingo_Http_Request::get('assert_list','');
		
		
		$arrParam['case_name'] = $case_name;
		$arrParam['description'] = $description;
		$arrParam['creator'] = $creator;
		$arrParam['modifier'] = $modifier;
		$arrParam['productline_id'] = $productline_id;
		$arrParam['frame_id'] = $frame_id;
		$arrParam['status'] = $status;
		$arrParam['priority'] = $priority;
		$arrParam['url'] = $url;			//not null
		$arrParam['request_method'] = $request_method;
		$arrParam['cmd'] = $cmd;	//not null
		$arrParam['content'] = $content;	//not null
		$arrParam['ignore_data'] = $ignore_data;
		$arrParam['parameter_list'] = $parameter_list;	//not null  array
		$arrParam['assert_list'] = $assert_list;	//not null
// 		Bingo_Log::warning(Bingo_String::array2json($arrParam));
		if(! $this->checkParam($arrParam)){
			return false;
		}
	
		return $arrParam;
		
	}
	
	public function checkParam(&$arrParam){
		if(! $arrParam['case_name'] || ! $arrParam['productline_id'] || ! is_numeric($arrParam['productline_id'])  || ! $arrParam['frame_id']||
		 ! is_numeric($arrParam['frame_id'])){
			Bingo_Log::warning("must input [case_name] and [int productline_id] and [int frame_id]");
			return false;
		} 
		
		$productline_id = abs(intval($arrParam['productline_id']));
		$frame_id = abs(intval($arrParam['frame_id']));
		
		//对于get方法，无需对parameter_list 进行强校验，仅对post方法做校验
		if($arrParam['request_method'] == 'POST'){
			if(! $arrParam['parameter_list'] || ! is_array($arrParam['parameter_list'])){
				Bingo_Log::warning("POST method must input [array parameter_list] ! ");
				return false;
			}
		}
		//GET method + POST parameter_list不为空 或 array(array("")),转换为json
		if(is_array($arrParam['parameter_list']) && $arrParam['parameter_list'] == array(array(""))){
			$arrParam['parameter_list'] = "";
		}else{
			$arrParam['parameter_list'] = Bingo_String::array2json($arrParam['parameter_list']);
				
		}
		
		//目前无法确定对应frame_id需检查的入参,目前把frame_id hardcode来判断 
		switch($frame_id){
			case 1:{ //短连接diff
				if(! $arrParam['url']){
					Bingo_Log::warning("frame_id=1 must input [url]");
					return false;
				}
				break;
			};
			case 2:{ //短连接assert
				if(! $arrParam['url'] || ! $arrParam['assert_list'] || ! is_array($arrParam['assert_list'])){
					Bingo_Log::warning("frame_id=2 must input [url] and [array assert_list]");
					return false;
				}
				$arrParam['assert_list'] = Bingo_String::array2json($arrParam['assert_list']);
				break;
			}
			case 3:{ //长连接diff
				if(! $arrParam['cmd'] || ! is_numeric($arrParam['cmd'])){
					Bingo_Log::warning("frame_id=3 must input [int cmd]");
					return false;
				}
				break;
			}
			case 4:{ //长连接assert
				if(! $arrParam['cmd'] || ! is_numeric($arrParam['cmd']) || ! $arrParam['assert_list'] || ! is_array($arrParam['assert_list'])){
					Bingo_Log::warning("frame_id=4 must input [array assert_list] and [int cmd]");
					return false;
				}
				$arrParam['assert_list'] = Bingo_String::array2json($arrParam['assert_list']);
				break;
			}
			case 5:{ //文本
				if(! $arrParam['content']){
					Bingo_Log::warning("frame_id=5 must input [content]");
					return false;
				}
				break;
			}
			default:{ // to do
				if($arrParam['assert_list'] && is_array($arrParam['assert_list'])){
					$arrParam['assert_list'] = Bingo_String::array2json($arrParam['assert_list']);
				}
				break;
			};
		}
	
		$arrParam['productline_id'] = $productline_id;
		$arrParam['frame_id'] = $frame_id;
		return true;
	}
}
