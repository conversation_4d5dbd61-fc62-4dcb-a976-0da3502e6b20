<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Errno.php
 * <AUTHOR>
 * @date 2014/09/03 14:46:34
 * @brief 
 *  
 **/
class Libs_Errno {
	
	const SUCCESS = 0;
	const ERR_PARAM_ERROR = 1;
	const ERR_CALL_SERVICE_FAIL = 2;
	
    //opmanager errno start with 1
    const CANT_DEL_DEFAULT_TEMPLATE = 100001;
	

    //case and suite error start with 2
    const ERROR_CASE_DEL_FAIL = 210000;
    const ERROR_QUERY_CASE_EMPTY = 210001;

    //taskmanger errno start with 3
    const ERROR_MYSQL_ACCESS_FAIL   =   310000;
    const ERROR_MYSQL_QUERY_FAIL    =   310001;
    const ERROR_MYSQL_QUERY_ZERO    =   310002;
    const ERROR_REDIS_ACCESS_FAIL   =   320000;
    const ERROR_REDIS_QUEUE_EMPTY   =   320001;
    const NO_MATCH_RECORD   =   330001;
    const NO_MATCH_MODIFY_RECORD   =   330002;
    const STATUS_NUM_NO_EXISTS   =   330003;
    const RESULT_NUM_NO_EXISTS   =   330004;
    const START_LINK_IS_EMPTY   =   330005;
    const JENKINS_ACCESS_FAIL   =   330006;
    const SET_STATUS_ERROR   =   330007;
    const SET_STATUS_RULE_ERROR   =   330008;

    //analysis errno start with 4
    const ERROR_QUERY_SUITE_BY_ID_FAIL   =   410001;
    const ERROR_UPDATE_TASK_FAIL   =   410002;
    const ERROR_QUERY_CASE_BY_ID_FAIL   =   410003;
    const ERROR_INSERT_TASK_RECORD_FAIL   =   410004;
    const ERROR_QUERY_SUITES_BY_FID_FAIL = 410005;
    const ERROR_QUERY_CASE_LIST_INFO_FAIL = 410006;
    const ERROR_QUERY_FRAME_LIST_FAIL = 410007;

    public static $errMap = array(
		0	   => 'success',
		1	  => 'param error',
		2     => 'call service fail',
        100001 => 'can not del default template',

        210000 => 'this case is used by suite',
        210001 => 'query case empty',





        310000 => 'db access failed!',
        310001 => 'db query failed!',
        310002 => 'db query result is zero!',
        320000 => 'redis access failed!',
        320001 => 'redis queue empty!',
        330001 => 'no find match task!',
        330002 => 'task current status don\'t modify!',
        330003 => 'task status is not exist!',
        330004 => 'task result status is not exist!',
        330005 => 'test framework start link is empty!',
        330006 => 'jenkins add job failed!',
        330007 => 'task current status is bigger than modify one!',
        330008 => 'task status rule is error!',


        410002 => "query suite info by suite_id failed !",
        410001 => "update task result failed !",
        410003 => "query case info by suite_id failed !",
        410004 => "insert task_records failed !",
        410005 => "query suites info by frame_id failed !",
        410006 => "query case list info failed !",
        410007 => "query getFrameResultsInfo fail !",
        );

}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
