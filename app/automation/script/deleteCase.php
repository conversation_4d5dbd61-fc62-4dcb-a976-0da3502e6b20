<?php
ini_set("memory_limit", "2G");
define ('ROOT_PATH', dirname(__FILE__) . "/../../../");
define ('HOME_PHP_PATH',  realpath(ROOT_PATH.'/php/bin/php'));
define('IS_ORP_RUNTIME',true);
// define ('BINGO_LIB_PATH',  realpath(ROOT_PATH.'/php/phplib/tb/Bingo'));
// define('UTIL_PATH',realpath(ROOT_PATH.'/app/automation/Util'));
// set_include_path(get_include_path().PATH_SEPARATOR.HOME_PHP_PATH.PATH_SEPARATOR.BINGO_LIB_PATH.PATH_SEPARATOR.UTIL_PATH);
require_once ROOT_PATH.'/app/automation/Util/Service.php';
require_once ROOT_PATH.'/app/automation/Util/Log.php';


$from = intval($argv[1]);
$end = intval($argv[2]);
for($i = $from; $i <= $end; $i++){
    $arrInput = array(
	   'id' => intval($i),
	);
	echo "deleteCase id is ".$arrInput['id']."\n";
	$arrRes = Util_Service::call('automation','deleteCase',$arrInput);
	echo "deleteCase result is =>".var_export($arrRes,1)." \n";
}

?>
