user_service_UidExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getMyFavorForum&user_id=1496&format=json		[followed_count]	p1
user_service_UidNotExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getMyFavorForum&user_id=-327&format=json		[followed_count]	p1
user_service_UidNotSet	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getMyFavorForum&user_id=&format=json		[followed_count]	p1
user_service_SidExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getSignBySignIds&sign_id[]=1&format=json		[user_id,inner_sid,is_default,stitlefollowed_count]	p1
user_service_SidNotSet	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getSignBySignIds&sign_id[]=-327&format=json		[user_id,inner_sid,is_default,stitlefollowed_count]	p1
user_service_SidNotExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getSignBySignIds&sign_id[]=&format=json		[user_id,inner_sid,is_default,stitlefollowed_count]	p1
user_service_UnameExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUidByUnames&user_name[]=小波波&format=json		[followed_count]	p1
user_service_UnameNotExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUidByUnames&user_name[]=********************************&format=json		[followed_count]	p1
user_service_UnameNotSet	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUidByUnames&user_name=&format=json		[followed_count]	p1
user_service_UidExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUnameByUids&user_id[]=440084&format=json		[followed_count]	p1
user_service_UidNotExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUnameByUids&user_id[]=-327&format=json		[followed_count]	p1
user_service_UidNotSet	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUnameByUids&user_id[]=&format=json		[followed_count]	p1
user_service_UidExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUserDoubleFollows&user_id=1496&offset=0&limit=10&format=json		[followed_count]	p1
user_service_UidNotExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUserDoubleFollows&user_id=-327&offset=0&limit=10&format=json		[followed_count]	p1
user_service_UidNotSet	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUserDoubleFollows&user_id=&offset=0&limit=10&format=json		[followed_count]	p1
user_service_UidHasFollowedUser	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUserFollowedInfo&user_id=440084&req_user_id[]=1496&format=json		[followed_count]	p1
user_service_UidNotExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUserFollowedInfo&user_id=-327&req_user_id[]=1496&format=json		[followed_count]	p1
user_service_UidNotSet	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUserFollowedInfo&user_id=&req_user_id[]=&format=json		[followed_count]	p1
user_service_UidHasFollowUser	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUserFollowInfo&user_id=1&req_user_id[]=440084&format=json		[followed_count]	p1
user_service_UidNotExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUserFollowInfo&user_id=-327&req_user_id[]=-233&format=json		[followed_count]	p1
user_service_UidNotSet	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUserFollowInfo&user_id=&req_user_id[]=&format=json		[followed_count]	p1
user_service_UidExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUserinfo&user_id=440084&format=json		[followed_count]	p1
user_service_UidNotExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUserinfo&user_id=-111&format=json		[followed_count]	p1
user_service_UidNotSet	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUserinfo&user_id=&format=json		[followed_count]	p1
user_service_UidExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUserinfoEx&user_id=440084&format=json		[followed_count]	p1
user_service_UidNotExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUserinfoEx&user_id=-111&format=json		[followed_count]	p1
user_service_UidInvalid	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUserinfoEx&user_id=0&format=json		[followed_count]	p1
user_service_UidNotSet	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=getUserinfoEx&user_id=&format=json		[followed_count]	p1
user_service_UidExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=mgetUserInfo&user_id[]=1&user_id[]=440084&user_id[]=1496&format=json		[followed_count]	p1
user_service_UidNotExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=mgetUserInfo&user_id[]=-327&format=json		[followed_count]	p1
user_service_UidNotSet	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=mgetUserInfo&user_id=&format=json		[followed_count]	p1
user_service_UidExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=mgetUserInfoEx&user_id[]=440084&user_id[]=1496&format=json		[followed_count]	p1
user_service_UidNotExist	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=mgetUserInfoEx&user_id[]=-327&user_id[]=0&format=json		[followed_count]	p1
user_service_UidNotSet	liumanna	GET	http://service.tieba.baidu.com/service/user?ie=utf-8&method=mgetUserInfoEx&user_id[]=&format=json		[followed_count]	p1
