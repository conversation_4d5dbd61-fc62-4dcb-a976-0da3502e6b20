<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014:09:01 19:43:26
 * @version 
 * @structs & methods(copied from idl.)
*/



define("MODULE","Automation_dl");
class Dl_Testframe_Testframe{

const SERVICE_NAME = "Dl_Testframe_Testframe";
protected static $_conf = null;
protected static $_use_split_db = false;
const DATABASE_NAME = "forum_automation";

/**
 * @brief get mysql obj.
 * @return: obj of Bd_DB, or null if connect fail.

**/		
private static function _getDB(){
    $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
    if($objTbMysql && $objTbMysql->isConnected()) {
        return $objTbMysql;
    } else {
    	Bingo_Log::warning("db connect fail.");
        return null;
    }
}

	
/**
 * @brief init
 * @return: true if success. false if fail.

**/		
private static function _init(){
	
	//add init code here. init will be called at every public function beginning.
	//not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
	//init should be recalled for many times.
	
	if(self::$_conf == null){	
		self::$_conf = Bd_Conf::getConf("/app/automation/dl_testframe_testframe");
		if(self::$_conf == false){
			Bingo_Log::warning("init get conf fail.");
			return false;
		}
		
	}
	return true; 
}


private static function _errRet($errno){
    return array(
        'errno' => $errno,
        'errmsg' => Tieba_Error::getErrmsg($errno),
    );
}

public static function preCall($arrInput){
    // pre-call hook
}

public static function postCall($arrInput){
    // post-call hook
}
/**
 * @brief
 * @arrInput:
 * 	GetTestframeListInput req
 * @return: $arrOutput
 * 	GetTestframeListOut res
**/
public static function getTestFrameList($arrInput){

	$pn = Util_Util::getIntParam($arrInput, 'pn');
	$rn = Util_Util::getIntParam($arrInput, 'rn');
	$fName = Util_Util::getStrParam($arrInput, 'frame_name');
	$uName = Util_Util::getStrParam($arrInput, 'owner_name');
	// input params check;
	if($pn === false||$rn === false){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	$db = self::_getDB();
	if(!$db){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}

	$pn = ($pn-1)*$rn;
	$sql = "SELECT SQL_CALC_FOUND_ROWS f_id as frame_id, f_name as frame_name, u_name as owner_name , f_description as description ,f_startlink as start_link FROM frame WHERE is_del = 0";
	if($fName){
		$sql .= " and f_name = '$fName'";
	}	
	if($uName){
		$sql .= " and u_name = '$uName'";
	}
	$sql .=	" ORDER BY f_createtime DESC LIMIT $pn, $rn";
	Bingo_Timer::start('dbquery');
	$res = $db->query($sql);
	$countRes = $db->query("SELECT found_rows() AS rowcount");
	$count = $countRes[0]['rowcount'];
	Bingo_Timer::end('dbquery');
	if($res===false){
		Bingo_Log::warning(__FUNCTION__ . " query db failed: " . $db->getLastSQL() . " errno:" . $db->errno() . " err:" . $db->error());
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	
	}
	$data = array();
	$data['testframe_list'] = $res;
	$data['testframe_count'] = $count;
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'res' => $data,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	TestframeInfo req
 * @return: $arrOutput
 * 	TestframeInfo res
**/
public static function addTestFrame($arrInput){

	$fName = Util_Util::getStrParam($arrInput, 'frame_name');
	$uName = Util_Util::getStrParam($arrInput, 'owner_name');
	$fDescription = Util_Util::getStrParam($arrInput, 'description');
	$fStartlink = Util_Util::getStrParam($arrInput, 'start_link');
	$tId = Util_Util::getIntParam($arrInput, 'template_id');
	$createTime = intval(time());
	// input params check;
	if($fName === false||$uName === false||$fStartlink === false||$tId === false){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	$db = self::_getDB();
	if(!$db){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	$sql = "INSERT INTO frame (f_id, f_name, u_name, f_createtime, f_description, f_startlink, t_id, is_del) VALUES (NULL, '$fName', '$uName', $createTime, '$fDescription', '$fStartlink', $tId, 0)";
	Bingo_Timer::start('dbquery');
	$res = $db->query($sql);
	Bingo_Timer::end('dbquery');
	if($res===false){
		Bingo_Log::warning(__FUNCTION__ . " query db failed: " . $db->getLastSQL() . " errno:" . $db->errno() . " err:" . $db->error());
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	
	}
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'res' => $res,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	TestframeInfo req
 * @return: $arrOutput
**/
public static function modifyTestFrame($arrInput){

	$fId = Util_Util::getIntParam($arrInput, 'frame_id');
	$fName = Util_Util::getStrParam($arrInput, 'frame_name');
	$uName = Util_Util::getStrParam($arrInput, 'owner_name');
	$fDescription = Util_Util::getStrParam($arrInput, 'description');
	$fStartlink = Util_Util::getStrParam($arrInput, 'start_link');
	$tId = Util_Util::getIntParam($arrInput, 'template_id');
	// input params check;
	if($fId === false||$fName === false||$uName === false||$fStartlink === false||$tId === false){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	$db = self::_getDB();
	if(!$db){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	$sql = "UPDATE frame SET f_name = '$fName', u_name = '$uName', f_description = '$fDescription', f_startlink = '$fStartlink', t_id = $tId, is_del = 0 WHERE f_id = $fId";
	Bingo_Timer::start('dbquery');
	$res = $db->query($sql);
	Bingo_Timer::end('dbquery');
	if($res===false){
		Bingo_Log::warning(__FUNCTION__ . " query db failed: " . $db->getLastSQL() . " errno:" . $db->errno() . " err:" . $db->error());
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	
	}
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'res' => $res,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	DelTestframeInput req
 * @return: $arrOutput
**/
public static function delTestFrame($arrInput){

	$fId = Util_Util::getIntParam($arrInput, 'frame_id');
	if($fId === false){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	$db = self::_getDB();
	if(!$db){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	
	Bingo_Timer::start('dbquery');
	$res = $db->query("START TRANSACTION");
	if($res === false){
		Bingo_Log::warning("start transaction false.sql[START TRANSACTION]");
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	}
	$sql = "UPDATE frame SET is_del = 1 WHERE f_id = $fId";
	$res = $db->query($sql);
	if($res === false){
		Bingo_Log::warning(__FUNCTION__ . " query db failed: " . $db->getLastSQL() . " errno:" . $db->errno() . " err:" .$db->error());  
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	}
	$sql = "DELETE FROM productline_frame WHERE f_id = $fId";
	$res = $db->query($sql);
	if($res === false){
		Bingo_Log::warning(__FUNCTION__ . " query db failed: " . $db->getLastSQL() . " errno:" . $db->errno() . " err:" .$db->error());  
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	}
	$res = $db->query("COMMIT");
	if($res === false){
		$db->query("ROLLBACK");
		Bingo_Log::warning("COMMIT transaction false.sql[START TRANSACTION]");
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	}
	Bingo_Timer::end('dbquery');

	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'res' => $res,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	GetTestframeInput req
 * @return: $arrOutput
 * 	GetTestframeOutput res
**/
public static function getTestFrame($arrInput){

	$fId = Util_Util::getIntParam($arrInput, 'frame_id');
	if($fId === false){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	$db = self::_getDB();
	if(!$db){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	$sql = "SELECT f_id as frame_id, f_name as frame_name, u_name as owner_name, f_createtime as create_time, f_description as description, f_startlink as start_link, t_id as template_id FROM frame WHERE f_id = $fId and is_del = 0";
	Bingo_Timer::start('dbquery');
	$res = $db->query($sql);
	Bingo_Timer::end('dbquery');
	if($res===false){
		Bingo_Log::warning(__FUNCTION__ . " query db failed: " . $db->getLastSQL() . " errno:" . $db->errno() . " err:" . $db->error());
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	
	}
	$out = array();
	if(!empty($res[0])){
		$out = $res[0];
		$out['create_time'] = date('Y-m-d', $out['create_time']);
	}
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'res' => $out,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	GetTemplateListInput req
 * @return: $arrOutput
 * 	GetTemplateListOut res
**/
public static function getTemplateList($arrInput){

	$pn = Util_Util::getIntParam($arrInput, 'pn');
	$rn = Util_Util::getIntParam($arrInput, 'rn');
	$tName = Util_Util::getStrParam($arrInput, 'template_name');
	// input params check;
	if($pn === false||$rn === false){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	$db = self::_getDB();
	if(!$db){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	$pn = ($pn-1)*$rn;
	$sql = "SELECT SQL_CALC_FOUND_ROWS t_id as template_id, t_name as template_name ,t_components as components, t_createtime as create_time, t_description as description FROM template WHERE is_del = 0";
	if($tName){
		$sql .= " and t_name = '$tName'";
	}
	$sql .= " ORDER BY t_createtime DESC LIMIT $pn, $rn";
	Bingo_Timer::start('dbquery');
	$res = $db->query($sql);
	$countRes = $db->query("SELECT found_rows() AS rowcount");
	Bingo_Timer::end('dbquery');
	if($res===false){
		Bingo_Log::warning(__FUNCTION__ . " query db failed: " . $db->getLastSQL() . " errno:" . $db->errno() . " err:" . $db->error());
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	
	}
	$out = array();
	$out['template_list'] = $res;
	$out['template_count'] = $countRes[0]['rowcount'];
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'res' => $out,
	);
	return $arrOutput;

}
/**
 * @brief
 * @arrInput:
 * 	TemplateInfo req
 * @return: $arrOutput
 * 	TemplateInfo res
**/
public static function addTemplate($arrInput){

	$tName = Util_Util::getStrParam($arrInput, 'template_name');
	$tDescription = Util_Util::getStrParam($arrInput, 'description');
	$tComponents = Util_Util::getStrParam($arrInput, 'components');
	$createTime = intval(time());
	// input params check;
	if($tName === false||$tComponents === false){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	$db = self::_getDB();
	if(!$db){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	$sql = "INSERT INTO template (t_id, t_name, t_createtime, t_description,t_components, is_del) VALUES (NULL, '$tName', $createTime, '$tDescription', '$tComponents', 0)";
	Bingo_Timer::start('dbquery');
	$res = $db->query($sql);
	Bingo_Timer::end('dbquery');
	if($res===false){
		Bingo_Log::warning(__FUNCTION__ . " query db failed: " . $db->getLastSQL() . " errno:" . $db->errno() . " err:" . $db->error());
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	
	}
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'res' => $res,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	TemplateInfo req
 * @return: $arrOutput
**/
public static function modifyTemplate($arrInput){

	$tId = Util_Util::getIntParam($arrInput, 'template_id');
	$tName = Util_Util::getStrParam($arrInput, 'template_name');
	$tDescription = Util_Util::getStrParam($arrInput, 'description');
	$tComponents = Util_Util::getStrParam($arrInput, 'components');
	// input params check;
	if($tId === false||$tName === false||$tComponents === false){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	$db = self::_getDB();
	if(!$db){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	$sql = "UPDATE template SET t_name = '$tName', t_description = '$tDescription', t_components = '$tComponents', is_del = 0 WHERE t_id = $tId";
	Bingo_Timer::start('dbquery');
	$res = $db->query($sql);
	Bingo_Timer::end('dbquery');
	if($res===false){
		Bingo_Log::warning(__FUNCTION__ . " query db failed: " . $db->getLastSQL() . " errno:" . $db->errno() . " err:" . $db->error());
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	
	}
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'res' => $res,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	DelTemplateInput req
 * @return: $arrOutput
**/
public static function delTemplate($arrInput){

	$tId = Util_Util::getIntParam($arrInput, 'template_id');
	if($tId === false){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
	if($tId === 1){
		Bingo_Log::warning("can not del default template");
		return Util_Automation_Error::errRet(Lib_Errno::CANT_DEL_DEFAULT_TEMPLATE);
	}	
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	$db = self::_getDB();
	if(!$db){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	$sql = "UPDATE template SET is_del = 1 WHERE t_id = $tId";
	Bingo_Timer::start('dbquery');
	$db->query("START TRANSACTION");
	$res = $db->query($sql);
	if($res===false){
		Bingo_Log::warning(__FUNCTION__ . " query db failed: " . $db->getLastSQL() . " errno:" . $db->errno() . " err:" . $db->error());
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	
	}
	$sql = "UPDATE frame SET t_id = 1 where t_id = '$tId'";
	$res = $db->query($sql);
	if($res===false){
		Bingo_Log::warning(__FUNCTION__ . " query db failed: " . $db->getLastSQL() . " errno:" . $db->errno() . " err:" . $db->error());
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	
	}
	$res = $db->query("COMMIT");
	if($res===false){
		$res = $db->query("ROLLBACK");
		Bingo_Log::warning(__FUNCTION__ . " query db failed: " . $db->getLastSQL() . " errno:" . $db->errno() . " err:" . $db->error());
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	
	}

	Bingo_Timer::end('dbquery');
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'res' => $res,
	);
	return $arrOutput;
}
public static function getTemplate($arrInput){

	$tId = Util_Util::getIntParam($arrInput, 'template_id');
	if($tId === false){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	$db = self::_getDB();
	if(!$db){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	$sql = "SELECT t_id as template_id, t_name as template_name, t_components as components, t_createtime as create_time, t_description as description FROM template WHERE t_id = $tId and is_del = 0";
	Bingo_Timer::start('dbquery');
	$res = $db->query($sql);
	Bingo_Timer::end('dbquery');
	if($res===false){
		Bingo_Log::warning(__FUNCTION__ . " query db failed: " . $db->getLastSQL() . " errno:" . $db->errno() . " err:" . $db->error());
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	
	}
	$out = array();
	if(!empty($res[0])){
		$out = $res[0];
		$out['create_time'] = date('Y-m-d',$out['create_time']);
	}
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'res' => $out,
	);
	return $arrOutput;
}

public static function getDefaultTemplate(){

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	$db = self::_getDB();
	if(!$db){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	$sql = "SELECT t_id as template_id, t_name as template_name, t_components as components, t_createtime as create_time, t_description as description FROM template WHERE t_id = 1 and is_del = 0";
	Bingo_Timer::start('dbquery');
	$res = $db->query($sql);
	Bingo_Timer::end('dbquery');
	if($res===false){
		Bingo_Log::warning(__FUNCTION__ . " query db failed: " . $db->getLastSQL() . " errno:" . $db->errno() . " err:" . $db->error());
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	
	}
	$out = array();
	if(!empty($res[0])){
		$out = $res[0];
	}
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'res' => $out,
	);
	return $arrOutput;
}

private static function _getTestFrameByTid($arrInput){

	$tId = Util_Util::getIntParam($arrInput, 'template_id');
	if($tId === false){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	$db = self::_getDB();
	if(!$db){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	$sql = "SELECT f_id, f_name,u_name, f_createtime, f_description,f_startlink,t_id, is_del FROM frame WHERE t_id = $tId";
	Bingo_Timer::start('dbquery');
	$res = $db->query($sql);
	Bingo_Timer::end('dbquery');
	if($res===false){
		Bingo_Log::warning(__FUNCTION__ . " query db failed: " . $db->getLastSQL() . " errno:" . $db->errno() . " err:" . $db->error());
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	
	}
	$out = array();
	if(!empty($res[0])){
		$out = $res[0];
	}
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'res' => $out,
	);
	return $arrOutput;
}
public static function getFidsByPid($arrInput){

	$pId = Util_Util::getIntParam($arrInput, 'productline_id');
	if($pId === false){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    	return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	$db = self::_getDB();
	if(!$db){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	$sql = "SELECT f_id as frame_id  FROM productline_frame WHERE p_id = $pId";
	Bingo_Timer::start('dbquery');
	$res = $db->query($sql);
	Bingo_Timer::end('dbquery');
	if($res===false){
		Bingo_Log::warning(__FUNCTION__ . " query db failed: " . $db->getLastSQL() . " errno:" . $db->errno() . " err:" . $db->error());
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	
	}
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'res' => $res,
	);
	return $arrOutput;
}
}
