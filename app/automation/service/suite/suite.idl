/**
 * @brief : automation platform suite module service idl
 * <AUTHOR> bijianxin
 **


//suite概要信息
struct suite{
	uint32_t suite_id;	//编号
	string suite_name;	//套件名称
	string description = optional();	//描述
	uint32_t status;	//状态1-已完成 2-已评审 3-已上线
	uint32_t productline_id;	//所属产品线id
	uint32_t frame_id;	//所属框架
	string ext = optional();	//预留 
};

//套件信息列表
struct suite_list{
	uint32_t total;  	//套件总数
	suite	data[];		//套件信息列表
};

//套件用例
struct suite_case{
	uint32_t suite_id;	//套件id
	uint32_t case_id;   //用例id
	uint32_t seq;       //序号
};
//套件用例列表
struct suite_case_list{
	suite suite_info;  //套件信息
	case_list data;		//该套件下的用例信息
}; 

service suite{
/**
	 * 查询产品线->测试框架类型->套件信息
	 * @param [in] uint32_t	productline_id
	 * @param [in] uint32_t	frame_id
	 * @param [out] suite_list	data
	 */
	void getSuiteByCond(uint32_t productline_id,uint32_t frame_id,out suite_list data);
	
	/**
	 * 根据id查询套件信息
	 * @param [in] uint32_t	suite_id
	 * @param [out] suite_case_list	data
	 **/
	void getAllCaseInSuite(uint32_t suite_id,uint32_t offset, uint32_t num,out suite_case_list data);
	
	
	/**
	 * 添加套件
	 * @param [in] string suite_name	//套件名称
	 * @param [in] string description	//描述
	 * @param [in] uint32_t status	//状态1-进行中 2-完成 3-上线
	 * @param [in] uint32_t productline_id	//所属产品线id
	 * @param [in]  uint32_t frame_id	//所属框架id
	 * @param [in] uint32_t[] case_ids  //用例id列表
	 * @param [out] uint32_t id  //套件编号
	 */
	 void addSuite(string suite_name,string description,uint32_t status,uint32_t productline_id,
	 uint32_t frame_id, uint32_t[] case_ids,out uint32_t id);
	 
	 /**
	 * 添加套件用例
	 * @param [in] uint32_t suite_id  
	 * @param [in] uint32_t[] case_ids
	 * @param [out] uint32_t affected_row_num
	 void addCaseToSuite(uint32_t suite_id,uint32_t[] case_ids,out uint32_t affected_row_num);
	 
	/**
	 * 删除套件
	 * @param [in] uint32_t suite_id	//套件id
	 * @param [in] uint32_t[] case_ids
	 * @param [out] uint32_t affected_row_num
	 */
	 void deleteMultiCaseFromSuite(uint32_t id, uint32_t[] case_ids,out uint32_t affected_row_num);
	 
	/**
	 * 删除该套件下的所有用例
	 * @param [in] uint32_t id	//套件id 
	 * @param [out] uint32_t affected_row_num
	 */
	 void deleteSuite(uint32_t id,out uint32_t affected_row_num);
	 
	 

};