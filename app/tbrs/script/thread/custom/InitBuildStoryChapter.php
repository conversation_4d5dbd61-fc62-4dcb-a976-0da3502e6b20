<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
  
//获取所有的目录，然后通过二级目录来获取所有的吧id
define ('WENXUE', '文学');
define ('SELEEP_TIME', 2);
define ('SELEEP_NUM', 500);
define ('GET_FORUMID_NUM', 50);
define ('GET_GOOD_THREAD_NUM', 50);

$wenxue = WENXUE;
$wenxue = iconv("UTF8", "GBK", $wenxue);
$arrAllDir = Tieba_Service::call('forum', 'getAllDir', null);
if ($arrAllDir === false || $arrAllDir['errno'] != 0){
    echo "call service[forum], method[getAllDir]   fail ";
    exit(0);
}
var_dump($arrAllDir);
$arrWenxue = array();
foreach ($arrAllDir['output']['all_dir'] as $value){
    if ($value['level_1_name'] == $wenxue){
        $arrWenxue = $value['level_2_name'];
        break;
    }
}
$arrFid = array();//该文学目录下所有的吧id
$arrForumNum = array();//每个二级目录下对应的吧个数

$arrInput['input'] = array(
    'level_1_name'  => $wenxue,
    'level_2_name'  => $arrWenxue[0],
    'offset'        => 0,
    'res_num'       => 1,
);
$arrResFourmNumInfo = Tieba_Service::call('forum', 'getFidByDir', $arrInput);
var_dump($arrResFourmNumInfo);
if ($arrResFourmNumInfo === false || $arrResFourmNumInfo['errno'] != 0){
    echo "call service[forum], method[getFidByDir]  fail";
    exit(0);
}

foreach ($arrResFourmNumInfo['output']['dirnames_2'] as $value){
    $arrForumNum[$value['name']] = $value['has_forum_num'];
}

var_dump($arrWenxue);

foreach($arrWenxue as $value){
    for($i = 0; $i <= $arrForumNum[$value]; $i += GET_FORUMID_NUM){
        $arrInput['input'] = array(
            'level_1_name'  => $wenxue,
            'level_2_name'  => $value,
            'offset'        => $i,
            'res_num'       => GET_FORUMID_NUM,
        );
        $arrResForumInfo = Tieba_Service::call('forum', 'getFidByDir', $arrInput);
        var_dump($arrResForumInfo);
        if($arrResForumInfo === false || $arrResForumInfo['errno'] != 0){
            echo "call service[forum], method[getFidByDir]  fail";
            continue;
        }
        //$arrFid = array_merge($arrFid, $arrResForumInfo['output']['forum_id']);
		$arrFid = $arrResForumInfo['output']['forum_id'];
		_getVerticalThread($arrFid);
        sleep(SELEEP_TIME - 1);        
    }
    //break;
}


$arrFid[] = 6856335;
$arrFid[] = 2304648;
$arrFid[] = 2282914;
//_getVerticalThread($arrFid);

echo "update end \n";

function _getVerticalThread($arrFid)
{
	//通过id获取精品帖子thread_list
   $sleep_num = 0;//500个sleep一下
   $arrAddFail = array();
   foreach ($arrFid as $value){
    echo "cur forum_id is $value \n";
    $arrGoodThreadId = array();
    $offset = 0;
    do{
        $has_more = false;
        $arrInput = array(
            'forum_name'    => '',
            'forum_id'      => $value,
            'need_abstract' => 0,
            'goodclass_id'  => 0,
            'offset'        => $offset,
            'res_num'       => GET_GOOD_THREAD_NUM,
            'need_photo_pic'=> 0,
            'ngscfr'        => 'default',
        );
        $arrResGoodThreadList = Tieba_Service::call('post', 'getGoodThreads', $arrInput);
        if($arrResGoodThreadList === false || $arrResGoodThreadList['errno'] != 0){
            echo "call service[post]  method[getGoodThreads]  fail , the input ".serialize($arrInput)."  the output ".serialize($arrResGoodThreadList);
            //exit(0);
            break;
        }
        if(!isset($arrResGoodThreadList['output']['thread_list']) || empty($arrResGoodThreadList['output']['thread_list'])){
            echo "call service[post]  method[getGoodThreads]  empty fail\n";
            break;
        }
        //var_dump($arrResGoodThreadList['output']['thread_list']);
        $arrGoodThreadId = array();
        foreach ($arrResGoodThreadList['output']['thread_list'] as $arrThreadInfo){
            if ( _filterThread($arrThreadInfo) ){
                $arrGoodThreadId[] = $arrThreadInfo['thread_id'];
            }
        }
        $goodThreadCount = count($arrGoodThreadId);
        echo "good thread id list is $goodThreadCount \n"; 
        if (!empty($arrGoodThreadId))
        {
            _addRecord($value, $arrGoodThreadId);
        }
        $offset  += GET_GOOD_THREAD_NUM;
        $goodNum = $arrResGoodThreadList['output']['forum_info']['thread_num'];
        $has_more = ($offset < $goodNum) ? true : false;
        $sleep_num ++;
        if($sleep_num > SELEEP_NUM ){
            $sleep_num = 0;
            sleep(SELEEP_TIME);
        }
    
    }while($has_more);    
  }

}
//匹配出小说的连载章节
function _filterThread($arrThreadInfo){
    $title = $arrThreadInfo['title'];
    $thread_id = $arrThreadInfo['thread_id'];
    var_dump($title);
    //var_dump($thread_id);
    $title = iconv("gbk", "utf-8", $title);
    $strNumPrefix =
    "一|二|三|四|五|六|七|八|九|十|百|千|零|两|00|01|02|03|04|05|06|07|08|09|0|1|2|3|4|5|6|7|8|9|、|-";
    $strFlagPrefix = "话|集|章|节|卷";
    $strPreg = '/第[\s]*(?:';

   // $strPreg = iconv("UTF8", "GBK", $strPreg);
   // $strNumPrefix = iconv("UTF8", "GBK", $strNumPrefix);
   // $strFlagPrefix = iconv("UTF8", "GBK", $strFlagPrefix);
    
    $strPreg = $strPreg.$strNumPrefix.')*[\s]*(?:'.$strFlagPrefix.')/is';
    $pos = preg_match($strPreg, $title,$out);
    var_dump($pos);
    if($pos > 0 ){
        return true;
    } else {
        return false;
    }
}

function _addRecord($fid, $arrTid)
{
	//通过service把数据放入redis
	$arrInput['input'] = array(
			'fid'       => $fid,
			'thread'    => $arrTid,
	);
	$res = Tieba_Service::call('tbrs', 'maddStoryCharpterInfo', $arrInput);
    if($res === false || $res['errno'] !== 0){
		echo "add vertical recommend forum fail ,input is ".serialize($arrInput)."  output is".serialize($res."\n");
		$arrAddFail[] = $arrInput['req'];
	}
	else
	{
		echo "tbrs::maddStoryCharpterInfo success, the fid is $fid, the thread_id is \n ";
		var_dump($arrTid);
	}
}


/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
