<?php
  require_once "../../../service/Tbrs.php";
  require_once "../../../libs/rpc/Post.php"; 
  require_once "../../../libs/rpc/Brcsnmq.php";
  require_once "../../../libs/util/Const.php";
  
  define ( 'APP_NAME', 'tbrs' );
  define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../../../..' );
  define ( 'SCRIPT_LOG_PATH', ROOT_PATH . '/log/app/'.APP_NAME );
  define ('SCRIPT_NAME', 'build_brcs_index');
  $logFile = SCRIPT_LOG_PATH . '/'.SCRIPT_NAME.'.log';
  Bingo_Log::init(array(
				LOG_SCRIPT => array(
				'file'	=> $logFile,
				'level'	=> 0x01|0x02|0x04|0x08,
				),), LOG_SCRIPT);
  
  function isUTF8($str){
	  $_is_utf8 = false;
	  if(preg_match("/[\x{4e00}-\x{9fa5}]+/u",$str)){
	      $_is_utf8 = true;
	  }   
	  return $_is_utf8;
  }
			 
  //$value_date = date('Ymd',strtotime('-1 day'));
  $value_date = strval($argv[1]);
  $NUM = 0;
  //$file = "/home/<USER>/orp001/app/recommend/unitest/data/pbdata/".$value_date."/000000_0";
  $file = dirname ( __FILE__ )."/data/".$value_date."/pbdata.out";
  echo "Date: ".$value_date." ********in build_brcs_index.php script******\n";
  if(file_exists($file)) {
	  $time = time();
	  $time=date("y-m-d H:i:s",$time);
	  echo "start time:".$time."  ".$file."\n";
      $fp = fopen($file, "r");
	  while(!feof($fp)) {
		  $line = fgets($fp);
		  $NUM = $NUM + 1;
		  $arr = split("\t", $line);
		  $tid = intval($arr[0]);
		  if(isUTF8($arr[1])) {
			  $title = iconv("UTF-8", "GBK", $arr[1]);
		  }
		  else {
			  $title = $arr[1];
		  }
		  $post_cnt = intval($arr[11]);
		  $pv = intval($arr[9]);
		  $threadOutIdType = 1; 
          $arrInputParam = array(); 
  		  $arrInputParam['thread_id'] = $tid;
          $arrInputParam['title'] = $title;
          $arrInputParam['post_num'] = $post_cnt;
		  $arrInputParam['thread_pv'] = $pv;
		  $arrInputParam['outid_type'] = $threadOutIdType;
          echo "----------num:$NUM---------\n";
          $nmqRet = Libs_Rpc_Brcsnmq::brcsAddThread($arrInputParam);

		  if ($NUM % 1000 === 0) {
		      var_dump($nmqRet);
			  sleep(1);
		  }
		  //exit();
	  }
	  fclose($fp);
	  $time = time();
	  $time=date("y-m-d H:i:s",$time);
	  echo "end time:".$time."  ".$file."\n";
	}
	else
	{
	    echo "the data file does not exist!!!\n";
	}
?>


 
