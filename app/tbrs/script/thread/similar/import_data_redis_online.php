<?php
  require_once "../../../libs/redis/Redis.php";
  require_once "../../../libs/redis/Sortset.php";
  require_once "../../../libs/redis/Hashes.php";
  require_once "../../../service/Tbrs.php";
  require_once "../../../libs/rpc/Post.php";

 
  define ( 'APP_NAME', 'tbrs' );
  define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../../../..' );
  define ( 'SCRIPT_LOG_PATH', ROOT_PATH . '/log/app/'.APP_NAME );
  define ('SCRIPT_NAME', 'import_data_redis_online');
  $logFile = SCRIPT_LOG_PATH . '/'.SCRIPT_NAME.'.log';
  Bingo_Log::init(array(
				LOG_SCRIPT => array(
				'file'	=> $logFile,
				'level'	=> 0x01|0x02|0x04|0x08,
				),), LOG_SCRIPT);
 
  function isUTF8($str)
  {
  	$_is_utf8 = false;
    if(preg_match("/[\x{4e00}-\x{9fa5}]+/u",$str) )
    {
        $_is_utf8 = true;
    }
    return $_is_utf8;
  }

  /**
   * 将帖子的属性写入redis hash
   */
  function write2hash($arrs) 
  {
      echo "--------in write2hash-------\n";
	  $arrAddInputParam = array();
	  foreach($arrs as $arr)
	  {
		if (count($arr) < 26 || '' === $arr || null === $arr)
		{
			continue;
		}

	  	for($i = 0; $i < count($arr); $i++)
	    {
        	if(isUTF8($arr[$i]))
        	{
		    	$arr[$i] = iconv("UTF-8", "GBK", $arr[$i]);
            }
	    }
	    $thread_id = $arr[0];    //主题贴id
	    $title = $arr[1];        //贴子标题
	    $fid = $arr[2];          //吧id
	    $fname = $arr[3];        //吧名
	    $uid = $arr[4];          //用户id
	    $first_dir = $arr[5];    //一级目录
	    $second_dir = $arr[6];   //二级目录
	    $t_time = $arr[7];       //发贴时间(时间戳)
	    $event_day = $arr[8];    //主题贴行为日期
	    $pv = $arr[9];           //pv
	    $uv = $arr[10];          //uv
	    $post_cnt = $arr[11];    //贴子回复数
	    $post_num = $arr[12];    //贴子回复人数
	    $login_num = $arr[13];   //登录用户数
	    $top_cnt = $arr[14];     //置顶次数
	    $del_top_cnt = $arr[15]; //取消置顶次数
	    $good_cnt = $arr[16];    //加精次数
	    $del_good_cnt = $arr[17];    //取消加精次数
	    $collect_cnt = $arr[18];     //收藏次数
	    $del_collect_cnt = $arr[19]; //取消收藏次数
	    $floor_cnt = $arr[20];   //发楼中楼数
	    $floor_num = $arr[21];   //发楼中楼人数
	    $looklz_cnt = $arr[22];  //只看楼主数量
	    $sensitive_score = $arr[23]; //贴子敏感性分值
	    $exaggeration_score = $arr[24]; //贴子倾向性分值
	    $advance_score = $arr[25]; //其它特征分值,有多个值
	    $dir = $first_dir.";".$second_dir;
	    $time = $t_time.";".$event_day;
	    $visit = $pv.";".$uv.";".$post_cnt.";".$post_num.";".$login_num.";".$top_cnt.";".$del_top_cnt.";".$good_cnt.";".$del_good_cnt.";".$collect_cnt.";".$del_collect_cnt.";".$floor_cnt.";".$floor_num.";".$looklz_cnt;
	    $score = $sensitive_score.";".$exaggeration_score;
	     
	    $fields[] = array('field' => 'title', 'value' => $title);
	    $fields[] = array('field' => 'fid', 'value' => $fid);
	    $fields[] = array('field' => 'fname', 'value' => $fname);
	    $fields[] = array('field' => 'uid', 'value' => $uid);
	    $fields[] = array('field' => 'dir', 'value' => $dir);
	    $fields[] = array('field' => 'time', 'value' => $time);
	    $fields[] = array('field' => 'visit', 'value' => $visit);
	    $fields[] = array('field' => 'score', 'value' => $score);
	    $fields[] = array('field' => 'advance_score', 'value' => $advance_score);
	    $arrAddInputParam[$thread_id] = $fields;
	    unset($fields);
	    $fileds = array();
	}
	//var_dump($arrAddInputParam);
	if (!empty($arrAddInputParam))
	{
		$input = array('threadIdAndFields' => $arrAddInputParam);
		$addRet = Tieba_Service::call('tbrs','mkeyAddThreadAttr', $input);
		var_dump($addRet);
	}
	
  } 
  
  //$value_date = date('Ymd',strtotime('-1 day'));
  $value_date = strval($argv[1]);
  //$file = "/home/<USER>/orp001/app/recommend/script/offline/program/get_pbdata/data/pbdata/".$value_date."/000000_0.tr.out";
  $file = dirname ( __FILE__ )."/data/".$value_date."/pbdata.out";
  echo "Date: ".$value_date." ********in import_data_redis_online.php script******\n";
  
  if(file_exists($file)) 
  {
	$time = time();
  	$t = date("y-m-d H:i:s", $time);
  	echo "start time:".$t.":".$file."\n";
  	$fp = fopen($file, "r");
  	$NUM = 0;
  	$arrs = array();
  	while(!feof($fp))
  	{
  		$NUM = $NUM + 1;
  		$line = fgets($fp);
  		$line = str_replace("\n", "", $line);
  		$arr = split("\t", $line);
  		array_push($arrs, $arr);
  		
  		if($NUM % 15 === 0)
  		{
  			echo "----------num:$NUM---------\n";
  			write2hash($arrs);
  			unset($arrs);
  			$arrs = array();
  		}
  	}
  	
  	if($NUM % 15 != 0) 
  	{
  		write2hash($arrs);
  	}
  	fclose($fp);
  	$time = time();
  	$time=date("y-m-d H:i:s",$time);
  	echo "end time:".$time."  ".$file."\n";
  }
  else
  {
  	echo "the data file does not exist!!!\n";
  }
?>
