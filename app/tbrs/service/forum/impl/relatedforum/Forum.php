<?php
/**
 * ===========================================
 * @desc: 吧的相关吧
 * @author: fengzhen
 * @date: 2016-4-25
 * ===========================================
 * @version 1.0.0
 * @copyright Copyright (c) www.baidu.com
 */
class Service_Forum_Impl_Relatedforum_Forum {
	const MAX_REQ_NUM = 100;
	const DEFAULT_REQ_NUM = 30;
	const CACHE_KEY_PRE = 'tbrs_frecomm_';
	const CACHE_EXPIRE_TIME = 300;
	public static $arrScene = array(
		'pc_like_tab_recomm',//PC端浮层吧推荐
		'pc_frs_recomm',//PC端FRS吧推荐
		'app_like_tab_recomm',//客户端浮层吧推荐
		'app_frs_recomm',//客户端FRS吧推荐
		'wap_like_tab_recomm',//智能版浮层吧推荐
		'wap_frs_recomm',//智能版FRS吧推荐
	);
	private static function _errRet($errno, $ret = '') {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'ret' => $ret,
        );
    }

    private static function _succRet($ret) {
        $error = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'ret' => $ret,
        );
    }
    
    public static function getSepRecomForum($arrInput){
    	return self::_succRet(array());//下线2018.05.10
    	if(empty($arrInput['req_num']) || $arrInput['req_num'] < 0){
    		$arrInput['req_num'] = self::DEFAULT_REQ_NUM;
    	}
    	if($arrInput['req_num'] > self::MAX_REQ_NUM){
    		$arrInput['req_num'] = self::MAX_REQ_NUM;
    	}
    	$strScene = '';
    	if(in_array($arrInput['scene'], self::$arrScene)){
    		$strScene = $arrInput['scene'];
    	}
		// memcache迁移bdrp清理访问流量太小的业务
    	// $cacheKey = self::CACHE_KEY_PRE . $arrInput['fid'] . '_' . $strScene;
    	// $cacheRet = Libs_Util_Cache::getCache($cacheKey);      	
		// if (false !== $cacheRet && null !== $cacheRet){
		// 	$result = unserialize($cacheRet);
		// 	return self::_succRet($result);
		// }
		$arrParam = array(
			'version' => '1.0',
		    'fid' => $arrInput['fid'],
		    'req_num' => $arrInput['req_num'],
		    'scene' => $strScene,
		);
		$queryRet = ral('dsp_recomm','query', $arrParam, array());
    	if (false == $queryRet || $queryRet['err_code'] != 0){
			Bingo_Log::warning('call dsp_recomm query fail, input is '.serialize($arrParam).' output is '.serialize($queryRet));
			return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
		}
		$relatedForum = $queryRet['related'];
		// if(!empty($relatedForum)){
		// 	$strCacheRetVal = serialize($relatedForum);
		// 	$addCacheRet = Libs_Util_Cache::addCache($cacheKey, $strCacheRetVal, self::CACHE_EXPIRE_TIME);
		// }
		return self::_succRet($relatedForum);
    }
}