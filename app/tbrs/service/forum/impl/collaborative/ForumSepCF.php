<?php
/**
 * SEP挖掘的相关吧推荐数据
 * <AUTHOR>
 *
 */
class Service_Forum_Impl_Collaborative_ForumSepCF
{
	private static function _errRet($errno) 
	{
		return array (
				'errno' => $errno,
				'errmsg' => Tieba_Error::getErrmsg ( $errno ) 
		);
	}
	
	public static function getRecommend($arrInput) 
	{
		if (! isset($arrInput['forum_id'])) 
		{
			Bingo_Log::warning ( "get recommend forum param less ,the intput = " . serialize ( $arrInput ) );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}

		$forumId = intval($arrInput['forum_id']);
			
		if ($forumId < 1) 
		{
			Bingo_Log::warning("param fail, the forumid is $forumId");
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		// 用户已关注的吧
		$arrFavorFids = isset($arrInput['favor_fids']) ? $arrInput['favor_fids'] : array();
	
		$res = Dl_Forum_ForumSepCFSet::getSortSetByExpKey($forumId);
		if (false === $res) 
		{
			Bingo_Log::warning("get the recommend forum fail from redis" );
			return self::_errRet(Tieba_Errcode::ERR_DL_DATA);
		}
		
		$res = unserialize($res['data'][0]);
		$arrRecomFids = array();
		
		foreach ($res as $value) 
		{
			$fid = isset($value['forum_id']) ? intval($value['forum_id']) : 0;
			if ($fid > 0 && !in_array($fid, $arrFavorFids)) 
			{
				$arrRecomFids [] = $fid;
			}
		}
		
		$arrRes = array();
		if (! empty($arrRecomFids)) 
		{
			$arrForumBtxInput = array (
					'forum_id' => $arrRecomFids
			);
			
			$arrForumInfo = Tieba_Service::call('forum', 'mgetBtxInfoEx', $arrForumBtxInput);
			if (false === $arrForumInfo || $arrForumInfo ['errno'] !== Tieba_Errcode::ERR_SUCCESS) 
			{
				Bingo_Log::warning('call forum mgetBtxInfoEx fail , input = ' . serialize ( $arrForumBtxInput ) . ' output = ' . serialize ( $arrForumInfo ) );
				return self::_errRet($arrForumInfo['errno']);
			}
			$arrForumName = array();
			$arrFname2Fid = array();
			foreach ($arrForumInfo['output'] as $key => $value) 
			{
				if ($value ['forum_name']['exist']) 
				{
					$strForumName = isset($value ['forum_name'] ['forum_name']) ?
					             strval($value ['forum_name'] ['forum_name']) : '';
					$intForumId = $value ['forum_name'] ['forum_id'];
					
					if ('' === $strForumName)
					{
						continue;
					}
					
					$arrRes[$key]['forum_id'] = $intForumId;					
					$arrRes[$key]['forum_name'] = $strForumName;
					$arrRes[$key]['member_count'] = $value ['statistics'] ['member_count'];
					$arrRes[$key]['post_num'] = $value ['statistics'] ['post_num'];
					$arrRes[$key]['avatar'] = $value ['card'] ['avatar'];
					$arrRes[$key]['forum_class'] = $value ['dir'] ['level_1_name'];
					$arrForumName[] = $strForumName;
					$arrFname2Fid[$strForumName] = $intForumId;
				}
			}
			
			if (!empty($arrForumName))
			{
				$arrForbiddenInput = array('query_words' => $arrForumName);
				$arrForbiddenRet = Tieba_Service::call('forum', 'getFidByFname', $arrForbiddenInput);
				
				if (false === $arrForbiddenRet || $arrForbiddenRet ['errno'] !== Tieba_Errcode::ERR_SUCCESS)
				{
					Bingo_Log::warning('call forum getFidByFname fail , input = ' . serialize ( $arrForbiddenInput ) . ' output = ' . serialize ( $arrForbiddenRet ) );
					return self::_errRet($arrForumInfo['errno']);
				}
				
				$arrForumId = isset($arrForbiddenRet['forum_id']) ? $arrForbiddenRet['forum_id'] : array();
				
				foreach($arrForumId as $arrForbInfo)
				{
					$strQword = isset($arrForbInfo['qword']) ? strval($arrForbInfo['qword']) : '';
					$intIsForbidden = isset($arrForbInfo['is_forbidden']) ? intval($arrForbInfo['is_forbidden']) : 0;
					
					if (1 === $intIsForbidden)
					{
						$intForbForumId = $arrFname2Fid[$strQword];
						unset($arrRes[$intForbForumId]);
					}
				}
			}			
		}
	
		$errno = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array (
				'errno' => $errno,
				'errmsg' => Tieba_Error::getErrmsg($errno),
				'data' => $arrRes
		);
		return $arrOutput;
	}
	
	public static function addRecommend($arrInput) 
	{
		if (! isset($arrInput['input'])) 
		{
			Bingo_Log::warning ( "add recommend forum param less ,the intput = " . serialize ( $arrInput ) );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
				
		$input = $arrInput['input'];
		foreach ( $input as $key => $value ) 
		{
			if ($key < 1 || empty ( $value )) 
			{
				continue;
			}
			
			$res = Dl_Forum_ForumSepCFSet::delSortSetByExpKey( $key );
			$res = Dl_Forum_ForumSepCFSet::addSortSet( $key, serialize ( $value ) );
			
			if (false === $res) 
			{
				Bingo_Log::warning ( "add recommend foruminfo fail,the key is $key,the value is " . serialize ( $value ) );
			}
		}
	
		$errno = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array (
				'errno' => $errno,
				'errmsg' => Tieba_Error::getErrmsg($errno),
		);
		return $arrOutput;
	}
	
}
