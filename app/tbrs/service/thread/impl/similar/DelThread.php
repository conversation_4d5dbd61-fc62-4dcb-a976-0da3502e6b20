<?php

class Service_Thread_Impl_Similar_DelThread
{
	public static function delThread($arrInput)
	{
		// 如果是删除回复，就不同步给brcs了
		if (0 !== $arrInput['post_id'])
		{
			return true;
		}
		
		$intThreadId = isset($arrInput['thread_id'])?intval($arrInput['thread_id']):0;
		
		if (0 === $intThreadId)
		{
			Bingo_Log::warning("del thread nmq thread_id is not exist, the nmq info is ".serialize($arrInput));
			return false;
		}
		
		$arrCallInput = array('tid_list' => array($intThreadId));
		$callRet = Libs_Rpc_Brcsnmq::brcsDelThread($arrCallInput);
		
		if (false === $callRet)
		{
			Bingo_Log::warning("call Libs_Rpc_Brcsnmq::brcaDelThread fail, input is ".serialize($arrInput));
			return false;
		}
		
		return true;
	}
}
