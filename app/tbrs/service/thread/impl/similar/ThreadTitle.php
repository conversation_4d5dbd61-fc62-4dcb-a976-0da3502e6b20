<?php
/**
 * 帖子title内容相似度推荐
 * <AUTHOR>
 *
 */
class Service_Thread_Impl_Similar_ThreadTitle
{
	const THREAD3S_RESULT_EXPIRE = 600;
	const BRCS_RESULT_EXPIRE = 86400;	
	const ONLINE_TEST_SWITCH = 0;      // 线上测试小流量开关
	const SEP_TEST_SWITCH = true;
	public static $testUid = array(
		768502450,
		863163499,
		555221516,
	);
	
	public static function getRecommend($arrInput)
	{
		Bingo_Log::pushNotice('input_param',serialize($arrInput));
		//input params.
		$threadId = isset($arrInput['thread_id']) ? intval($arrInput['thread_id']) : 0;
		$threadTitle = isset($arrInput['thread_title']) ? $arrInput['thread_title'] : '';
		$forumId = isset($arrInput['forum_id']) ? intval($arrInput['forum_id']) : 0;
		$maxThreadNum = isset($arrInput['max_thread_num']) ? intval($arrInput['max_thread_num']) : 5;
        $callFrom = isset($arrInput['call_from']) ? strval($arrInput['call_from']) : '';
		
		if ('' === $threadTitle && $threadId <= 0) 
		{
			$error = Tieba_Errcode::ERR_PARAM_ERROR;
			$arrOutput = array (
					'errno' => $error,
					'errmsg' => Tieba_Error::getErrmsg($error),
					'data' => array(),
			);
			return $arrOutput;
		}
				
        $modelId = Service_Thread_Libs_Util_Const::MODEL_THREAD_TITLE_ONLINE_REC;
        
        // 判断是否开启小流量 
        if (1 === self::ONLINE_TEST_SWITCH)
        {
        	$baiduId = isset($arrInput['baidu_id']) ? $arrInput['baidu_id'] : '';
        	$isTestSwitch = false;
        	
        	if ('' !== $baiduId)
        	{
        		$isTestSwitch = Libs_Util_Tools::isTestSwitchByBaiduId($baiduId);
        	}
        	
        	if (true === $isTestSwitch)
        	{
        		$modelId = Service_Thread_Libs_Util_Const::MODEL_THREAD_TITLE_OFFLINE_TEST;
        	}
        }elseif(self::SEP_TEST_SWITCH){
        	$baiduId = isset($arrInput['baidu_id']) ? $arrInput['baidu_id'] : '';
        	if(!empty($baiduId)){
        		$isTestSwitch = Libs_Util_Tools::isTestSwitchByBaiduId($baiduId);
        		Bingo_Log::pushNotice('baidu_id_hit', intval($isTestSwitch));
        	}
        	if(in_array(intval($arrInput['user_id']), self::$testUid)){
        		$isTestSwitch = true;
        	}
        	if($isTestSwitch){
        		$modelId = Service_Thread_Libs_Util_Const::MODEL_THREAD_TITLE_SEP_TEST;
        	}elseif(!empty($baiduId)){
				$intAsc = sprintf("%u",crc32($baiduId));
				$intModel = $intAsc%20;
				if($intModel === 19){
					$modelId = Service_Thread_Libs_Util_Const::MODEL_THREAD_TITLE_SEP_TEST_SHANYU;
				}
        	}
        }
        
        Bingo_Log::pushNotice('model_id', $modelId);			    
	    $threadInfo = self::getRecThreadList($threadId, $threadTitle, $maxThreadNum, $forumId, $modelId, $callFrom);
	
    	if (false === $threadInfo) 
		{
			$error = Tieba_Errcode::ERR_RPC_CALL_FAIL;
			Bingo_Log::error("title get recommend thread fail, title=$threadTitle");
			$data = array ();
		} 
		else 
		{
			$error = Tieba_Errcode::ERR_SUCCESS;
			$data = isset($threadInfo['data']) ? $threadInfo['data'] : array();
		}
        
		$resultNum = count($data);
		Bingo_Log::pushNotice('result_num', $resultNum);
		
		if ($resultNum < $maxThreadNum)
		{
			Bingo_Log::warning("thread title recommend result_num < req_num, result_num=$resultNum, req_num=$maxThreadNum");
		}

		$arrOutput = array (
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'data' => $data,
				'model_id' => $modelId,
		);
		
		return $arrOutput;
	}

	/**
	 * 获取推荐帖子列表
	 * @param unknown_type $threadId
	 * @param unknown_type $threadTitle
	 * @param unknown_type $maxRetNum
	 * @param unknown_type $forumId
	 * @param unknown_type $modelId
	 * @return multitype:multitype: |multitype:mixed |boolean|multitype:multitype: multitype:multitype:
	 */
	public static function getRecThreadList($threadId, $threadTitle, $maxRetNum = 5, $forumId = 0, $modelId = 0, $callFrom = '')
	{
		$arrOutput = array('data' => array());
		if ($threadId <= 0 || '' === $threadTitle)
		{
			Bingo_Log::warning("input param error,the thread_id = $threadId, title = $threadTitle");
			return $arrOutput;
		}
		$strCacheKey = "thread_title_ret:$threadId:$maxRetNum";
        if( 'mo_wap_pb' == strval($callFrom) ) {
            $strCacheKey = "mo_wap_pb_" . $strCacheKey;
        }
		//小流量测试去掉cache，add by fengzhen 20151119
		if($modelId !== Service_Thread_Libs_Util_Const::MODEL_THREAD_TITLE_SEP_TEST && $modelId !== Service_Thread_Libs_Util_Const::MODEL_THREAD_TITLE_SEP_TEST_SHANYU){
            if( 'mo_wap_pb' == strval($callFrom) ) {
                $cacheRet = Libs_Util_MoCache::getCache($strCacheKey);
            } else {
                $cacheRet = Libs_Util_Cache::getCache($strCacheKey);
            }
	        	
			if (false !== $cacheRet && null !== $cacheRet)
			{
				$result = unserialize($cacheRet);
                return array('data' => $result);
			}
			
		}
        
		$queryRet = self::_getResultOnline($threadId, $threadTitle, $maxRetNum*2, $forumId, $modelId);
	
		if (false === $queryRet)
		{
			return $arrOutput;
		}
	
		$arrTidList = isset($queryRet['tid_list'])?$queryRet['tid_list']:array();
		$arrItemList = isset($queryRet['item_list'])?$queryRet['item_list']:array();
	
		if (empty($arrTidList))
		{
			return $arrOutput;
		}
		
		$need_forum_name = 1;
		$forum_id = 1;
		$need_photo_pic = 1;
		$need_user_data = 0;
		$icon_size = 3;
		$need_abstract = 1;
        if ('mo_wap_pb' == strval($callFrom)) {
            $call_from = 'client_frs';
        } else {
            $call_from = 'default';
		}
        $threadInfoRet = Libs_Rpc_Post::mgetThread($arrTidList,
				$need_forum_name, $forum_id,$need_photo_pic,
				$need_user_data, $icon_size, $need_abstract, $call_from);
		//var_dump($threadInfoRet);
		if (false === $threadInfoRet)
		{
			return false;
		}
	
		$threadInfoList = isset($threadInfoRet['thread_list'])?$threadInfoRet['thread_list']:array();
		//var_dump($threadInfoList);
		$arrDataOutput = array();
		$threadCount = 0;
	
		foreach ($arrTidList as $tid)
		{
			$tid = intval($tid);
			//echo "thread id = $threadId \n";
			$threadInfo = isset($threadInfoList[$tid])?$threadInfoList[$tid]:array();
			//var_dump($threadInfo);
			if (0 === $threadInfo['is_deleted'] && $threadId != $tid)
			{
				if (isset($arrItemList[$tid]['term_list']) && !empty($arrItemList[$tid]['term_list']))
				{
					$threadInfo['term_list'] = $arrItemList[$tid]['term_list'];
				}
	
				$arrDataOutput[] = $threadInfo;
				$threadCount++;
			}
				
			if ($threadCount >= $maxRetNum)
			{
				break;
			}
		}
	
		$strCacheRetVal = serialize($arrDataOutput);
		if($modelId !== Service_Thread_Libs_Util_Const::MODEL_THREAD_TITLE_SEP_TEST && $modelId !== Service_Thread_Libs_Util_Const::MODEL_THREAD_TITLE_SEP_TEST_SHANYU){
            if( 'mo_wap_pb' == strval($callFrom) ) {
                $addCacheRet = Libs_Util_MoCache::addCache($strCacheKey, $strCacheRetVal, self::THREAD3S_RESULT_EXPIRE);
            } else {
                $addCacheRet = Libs_Util_Cache::addCache($strCacheKey, $strCacheRetVal, self::THREAD3S_RESULT_EXPIRE);
            }
		}
		//var_dump($arrDataOutput);
		$arrOutput['data'] = $arrDataOutput;
		return $arrOutput;
	}
	
	private static function _getResultOnline($threadId, $threadTitle, $maxRetNum, $forumId, $modelId)
	{
		$strCacheKey = "thread_title_brcs:$threadId:$maxRetNum";
	
		if($modelId !== Service_Thread_Libs_Util_Const::MODEL_THREAD_TITLE_SEP_TEST && $modelId !== Service_Thread_Libs_Util_Const::MODEL_THREAD_TITLE_SEP_TEST_SHANYU){
			$cacheRet = Libs_Util_Cache::getCache($strCacheKey);
		    
			if (false !== $cacheRet && null !== $cacheRet)
			{
				$result = unserialize($cacheRet);
				return $result;
			}
		}
		$intMinQueryLen = 50; 
		$strQueryTitle = substr($threadTitle, 0, $intMinQueryLen);
		$strQueryTitle = Libs_Util_Tools::filterSpecialChar($strQueryTitle); 
		$queryRet = Libs_Rpc_Brcsquery::getQueryRecommend($strQueryTitle, $maxRetNum*2, $threadId, $forumId, $modelId);
        //var_dump($queryRet);	
		if (false === $queryRet)
		{
			return false;
		}
		else
		{
			$strCacheVal = serialize($queryRet);
			if($modelId !== Service_Thread_Libs_Util_Const::MODEL_THREAD_TITLE_SEP_TEST && $modelId !== Service_Thread_Libs_Util_Const::MODEL_THREAD_TITLE_SEP_TEST_SHANYU){
				$addCacheRet = Libs_Util_Cache::addCache($strCacheKey, $strCacheVal, self::BRCS_RESULT_EXPIRE);
			}
			return $queryRet;
		}
	}
}
