<?php
/**
 * 帖子推荐
 * <AUTHOR>
 *
 */

define("MODULE","thread_service");
define("MODULE_NAME","tbrs_thread");

class Service_Thread_Thread {
	const SERVICE_NAME = "Service_Thread";
	protected static $_conf = null;
	protected static $_passwd = "Aw!RKO7x";
	
	/**
	 * @brief init
	 * 
	 * @return : true if success. false if fail.
	 *        
	 */
	private static function _init() 
	{
		if (self::$_conf == null) 
		{
			self::$_conf = Bd_Conf::getConf ( "/app/tbrs/service_thread_thread" );
			if (self::$_conf == false) {
				Bingo_Log::warning ( "init get conf fail." );
				return false;
			}
		}
		return true;
	}
	
	private static function _errRet($errno) 
	{
		return array (
				'errno' => $errno,
				'errmsg' => Tieba_Error::getErrmsg ( $errno ) 
		);
	}

	public static function preCall($arrInput) 
	{
		// pre-call hook
	}
	
	public static function postCall($arrInput) 
	{
		// post-call hook
	}
	
	public static function getThreadTitleRecommend($arrInput) 
	{		
		if (!self::_init()) 
		{
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		Bingo_Timer::start( "Timer_tbrs_getThreadTitleRecommend" );
		$ret = Service_Thread_Impl_Similar_ThreadTitle::getRecommend($arrInput);
        Bingo_Timer::end( "Timer_tbrs_getThreadTitleRecommend" );
        
		if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS)
		{
			Bingo_Log::warning("getTitleSimpleSearch exec fail,input is"
					.serialize($arrInput)."output is".serialize($ret));
		}
		return $ret;
	}
	

	public static function getPsQueryRecommend($arrInput) 
	{	
		if (!self::_init ()) 
		{
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
		}
		
		$ret = Service_Thread_Impl_Similar_Psquery::getRecommend($arrInput);
		
		if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS)
		{
			Bingo_Log::warning("getPsQueryRecommend exec fail, input is "
					.serialize($arrInput)."output is".serialize($ret));
		}
		
		return $ret;
	}
	
	public static function getStoryChapterRecommend($arrInput)
	{
		if (!self::_init ())
		{
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
		}
		
		$ret = Service_Thread_Impl_Custom_StoryChapter::getRecommend($arrInput);
		
		if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS)
		{
			Bingo_Log::warning("getStoryChapterRecommend exec fail, init input is "
					.serialize($arrInput)."output is ".serialize($ret));
		}
		
		return $ret;
	}

         /**
	 * get story chapter by range
	 * @param array arrInput forum_id,thread_id,pre_num,after_num
	 * @return array story chapter by range
	 */	
	public static function getStoryChapterRecommendByRange($arrInput)
	{
		if (!self::_init ())
		{
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
		}
		
		$ret = Service_Thread_Impl_Custom_StoryChapter::getRecommendByRange($arrInput);
		
		if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS)
		{
			Bingo_Log::warning("getStoryChapterRecommend exec fail, init input is "
					.serialize($arrInput)."output is ".serialize($ret));
		}
		
		return $ret;
	}
	
	public static function eqid2Wd($arrInput)
	{
		if (!self::_init ())
		{
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
		}
		
		if (empty($arrInput) || !isset($arrInput['eqid']))
		{
			Bingo_Log::warning("get eqid2wd param is error ,the input = ".serialize($arrInput));
			$errno = Tieba_Errcode::ERR_PARAM_ERROR;
			return self::_errRet($errno);
		}
		
		$eqid = $arrInput['eqid'];		
		$ret = Service_Thread_Libs_Rpc_Eclipse::getQueryByEqid($eqid);
		
		if (false === $ret || '' === $ret)
		{
			Bingo_Log::warning("Service_Thread_Libs_Rpc_Eclipse::getQueryByEqid fail ,the eqid = $eqid");
			$errno = Tieba_Errcode::ERR_RPC_CALL_FAIL;
			return self::_errRet($errno);
		}
		
		$errno = Tieba_Errcode::ERR_SUCCESS;
		$data = array('ps_query' => $ret);		
		$arrOut = array (
				'errno' => $errno,
				'errmsg' => Tieba_Error::getErrmsg($errno),
				'data'  => $data,
		);
		return $arrOut;
	}
	
	public static function threadProcessNmqRequest($arrInput) 
	{
		if (! isset ( $arrInput ['format'] )) {
	       $arrInput ['format'] = 'mcpack';
		}
		$strFormat = strtolower ( $arrInput ['format'] );
		if ($strFormat !== 'mcpack' && $strFormat !== 'json' && $strFormat !== 'mcpackraw' ) {
			Bingo_Log::warning ( "input params wrong format:$strFormat." );
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		
		if(!self::_init()){
			Bingo_Log::warning("init fails");
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		
		$arrData = array (); 
		$arrData = Tieba_Service::getArrayParams($arrInput,"data");
		Bingo_Log::notice('nmq data'.serialize($arrData));		
	
		$ret = Service_Thread_Impl_Base_ReceiveNmq::parseNmqRequest($arrData);
		if(false === $ret)
		{
			$error = Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL;
			Bingo_Log::warning("call Impl_Base_ReceiveNmq::parseNmqRequest fail, req is ".serialize($arrData));
		}
		else
		{
			$error = Tieba_Errcode::ERR_SUCCESS;
		}
	
		return self::_errRet($error);
	}
	
	
	public static function maddStoryCharpterInfo($arrInput)
	{
		if (!self::_init())
		{
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		
		$ret = Service_Thread_Impl_Custom_StoryChapter::mAddStoryChapter($arrInput);
		
		if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS)
		{
			Bingo_Log::warning("maddStoryCharpterInfo exec fail, init input is "
					.serialize($arrInput)."output is ".serialize($ret));
		}
		
		return $ret;
	}
	
	public static function mkeyAddThreadAttr($arrInput)
	{
		if (!self::_init())
		{
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		
		$ret = Service_Thread_Impl_Similar_ThreadAttr::mkeyAddThreadAttrHash($arrInput);
		
		if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS)
		{
			Bingo_Log::warning("maddStoryCharpterInfo exec fail, init input is "
					.serialize($arrInput)."output is ".serialize($ret));
		}
		
		return $ret;
	}
	
	public static function getAllThreadAttrByKey($arrInput)
	{
		if (!self::_init())
		{
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
		
		$arrInput['table_name'] = 'BtattHash_';		
		$ret = Service_Thread_Impl_Similar_ThreadAttr::getAllHash($arrInput);
		
		if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS)
		{
			Bingo_Log::warning("getAllThreadAttrByKey exec fail, init input is "
					.serialize($arrInput)."output is ".serialize($ret));
		}
		
		return $ret;
	}
	
	public static function delThreadAttrByKey($arrInput)
	{
		if (!self::_init())
		{
			return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
		}
	
		$arrInput['table_name'] = 'BtattHash_';
		$ret = Service_Thread_Impl_Similar_ThreadAttr::delKey($arrInput);
	
		if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS)
		{
			Bingo_Log::warning("delThreadAttrByKey exec fail, init input is "
					.serialize($arrInput)."output is ".serialize($ret));
		}
	
		return $ret;
	}
}
