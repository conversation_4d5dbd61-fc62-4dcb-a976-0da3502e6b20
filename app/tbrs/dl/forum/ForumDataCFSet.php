<?php
/**
*  �������ھ�İ��Ƽ�����
**/
class Dl_Forum_ForumDataCFSet
{
	private static $TABLE_NAME_PRE = 'FDTSortSet_'; 
	
	/**
	 * ��������
	 * @param unknown_type $Forumid
	 * @param unknown_type $members
	 * @return boolean
	 */
	public static function addSortSet($forumid, $members)
	{
		if ($forumid <= 0 || empty($members))
		{
			Bingo_Log::warning("input param error, forum id is $forumid,".print_r($members, true));
			return false;
		}
		
		$ret = Libs_Redis_Sortset::addSortedSetByExpKey(self::$TABLE_NAME_PRE, $forumid,  $members);
		
		if (false == $ret)
		{
			Bingo_Log::warning("madd sort set $key fail");
			return false;
		}
		
		$intLifeTime = 86400 * 7;
		Libs_Redis_Redis::expireByExpKey(self::$TABLE_NAME_PRE, $forumid, $intLifeTime);		
		return true;
	}

	/**
	 * ɾ���Ѿ����ڵ�key
	 * @param unknown_type $forumid
	 * @param unknown_type $members
	 */
	public static function delSortSetByExpKey($forumid){
		if ($forumid <= 0)
		{
			Bingo_Log::warning("input param error, forum id is $forumid");
			return false;
		}
		
		$ret = Libs_Redis_Sortset::delSortedSet(self::$TABLE_NAME_PRE, $forumid, $members);
		
		if (false == $ret)
		{
			Bingo_Log::warning("mdel sort set FattSortSet_$forumid fail");
			return false;
		}
		
		return true;
		
	}

	/**
	 * ͨ��key��ȡ�Ƽ�������
	 * @param unknown_type $forumid
	 * @param unknown_type $members
	 */
	public static function getSortSetByExpKey($forumid){
	
		if ($forumid <= 0)
		{
			Bingo_Log::warning("input param error, forum id is $forumid");
			return false;
		}
		
		$ret = Libs_Redis_Sortset::zrange(self::$TABLE_NAME_PRE.$forumid, 0);
		
		if (false == $ret)
		{
			Bingo_Log::warning("get sort set form FattSortSet_$forumid by $forumid fail");
			return false;
		}
		
		return $ret;
	}
	
}
