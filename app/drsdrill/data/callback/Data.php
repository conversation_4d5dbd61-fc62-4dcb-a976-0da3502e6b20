<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Data.php
 * <AUTHOR>
 *  
 **/

class Data_Callback_Data{
	// constants
	const SERVICE_NAME = "service_managerapply";
	const DB_NAME = 'forum_drstc';

   
	// DB object
    //
    // forum drs table about exception 
    // | exception_service_history | 
/*
 *        CREATE TABLE `exception_service_history` (
 *          `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 *          `op_user` varchar(64) NOT NULL,
 *          `op_time` int(10) unsigned NOT NULL,
 *          `op_info` varchar(10240) NOT NULL,
 *          `op_cluster` varchar(32) NOT NULL,
 *          `op_type` int(10) unsigned NOT NULL,
 *          `status` int(10) unsigned NOT NULL,
 *          PRIMARY KEY (`id`),
 *          KEY `status` (`status`)
 *        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 | 
 *
 */
	private static $_objDB = null;

	private static function _getDB()
	{
		if ( self::$_objDB ){
			return self::$_objDB;
		}

		Bingo_Timer::start('db_init');
		self::$_objDB = Tieba_Mysql::getDB(self::DB_NAME);
		Bingo_Timer::end('db_init');
		if ( self::$_objDB && self::$_objDB->isConnected() ){
			return self::$_objDB;
		}
		else{
			Bingo_Log::warning('fail to connect db');
			return null;
		}
	}

	private static function _queryDB($strSql, $strTimer = 'db_query')
	{
		$objDB = self::_getDB();
		if ( !$objDB ){
			Bingo_Log::warning('fail to get db');
			return false;
		}
		Bingo_Timer::start($strTimer);
		$arrRet = $objDB->query($strSql);
		Bingo_Timer::end($strTimer);
		if ( $arrRet === false ){
			$strLastError = $objDB->error();
			Bingo_Log::warning("execute sql error [$strSql] [$strLastError]");
		}
		return $arrRet;
	}

	private static function _escape($strWord)
	{
		$objDB = self::_getDB();
		if ( !$objDB ){
			Bingo_Log::warning('fail to get db');
			return false;
		}
		return $objDB->escapeString($strWord);
	}

	private static function _buildReturn($intErrno = Tieba_Errcode::ERR_SUCCESS, $arrOutput = null)
	{
		$strErrmsg = Tieba_Error::getErrmsg($intErrno);
		$arrResult = array(
			'errno'  => $intErrno,
			'errmsg' => $strErrmsg,
		);
		if ( $arrOutput !== null ){
			$arrResult['output'] = $arrOutput;
		}

		return $arrResult;
	}

	private static function _checkInput($arrArgList, $arrInput)
	{
		foreach ( $arrArgList as $strArg ){
			if ( !isset($arrInput[$strArg]) ){
				Bingo_Log::warning('arg ['.$strArg.'] is not existed.');
				return false;
			}
		}
		return true;
	}

    public static function callbackService($arrInput)
    {
        
        $op_type = $arrInput['op_type'];
        $status_running = Data_Exception_Data::STATUS_RUNNING;
        $info_type = Data_Exception_Data::INFO_TYPE_BLACK;

        // recover & add
        if ( $op_type == Data_Exception_Data::OP_TYPE_RECOVER )
        {
            $arrInput['success'] == 1 ? $status = Data_Exception_Data::STATUS_OFF : $status = Data_Exception_Data::STATUS_ERROR;
            
            if (strpos($arrInput['op_info'], 'core') === 0)
            {

                $info_type = Data_Exception_Data::INFO_TYPE_WHITE;
                $op_info = $arrInput['op_info'];
    
                /* when add, update core1 or core0, recover update core% */
                $sql = "UPDATE exception_service_info set 
                        status = $status 
                        WHERE op_info like '${op_info}%' and status = $status_running and info_type = $info_type ";
    
                $ret = self::_queryDB($sql);
            
                if(false == $ret)
                {
                    Bingo_Log::warning("exec sql failed!");
                    return false;
                }
    
            } else {

                $op_infos = Bingo_String::json2array($arrInput['op_info']);
    
                foreach($op_infos as $info)
                {
                    $op_info = $info['c'] . ":" . $info['s'] . ":" . $info['m']; 
                    $op_cluster = $info['i'];
                    $sql = "UPDATE exception_service_info set status = $status WHERE op_cluster = '$op_cluster' and op_info = '$op_info' and status = $status_running and info_type = $info_type";
        
                    $ret = self::_queryDB($sql);
            
                    if(false == $ret)
                    {
                        Bingo_Log::warning("exec sql failed!");
                        return false;
                    }
                }
           
            }

        }else if ( $op_type == Data_Exception_Data::OP_TYPE_ADD) {

            $arrInput['success'] == 1 ? $status = Data_Exception_Data::STATUS_ON : $status = Data_Exception_Data::STATUS_ERROR;
  
            if (strpos($arrInput['op_info'], 'core') === 0)
            {

                $info_type = Data_Exception_Data::INFO_TYPE_WHITE;
                $op_info = $arrInput['op_info'];
    
                /* when add, update core1 or core0, recover update core% */
                $sql = "UPDATE exception_service_info set 
                        status = $status 
                        WHERE op_info = '$op_info' and status = $status_running and info_type = $info_type ";
    
                $ret = self::_queryDB($sql);
            
                if(false == $ret)
                {
                    Bingo_Log::warning("exec sql failed!");
                    return false;
                }
    
            } else {

                $op_infos = Bingo_String::json2array($arrInput['op_info']);
    
                foreach($op_infos as $info)
                {
                    $op_info = $info['c'] . ":" . $info['s'] . ":" . $info['m']; 
                    $op_cluster = $info['i'];
                    $sql = "UPDATE exception_service_info set status = $status WHERE op_cluster = '$op_cluster' and op_info = '$op_info' and status = $status_running and info_type = $info_type";
        
                    $ret = self::_queryDB($sql);
            
                    if(false == $ret)
                    {
                        Bingo_Log::warning("exec sql failed!");
                        return false;
                    }
                }
           
            }
        } else if ( $op_type == Data_Exception_Data::OP_TYPE_DEL ) {

            Bingo_Log::warning(var_export($arrInput, true));

            $arrInput['success'] == 1 ? $status = Data_Exception_Data::STATUS_OFF : $status = Data_Exception_Data::STATUS_ERROR;
            $op_info = $arrInput['op_info'];
            list($op_info, $op_cluster) = explode('#', $arrInput['op_info']); 

            $sql = "UPDATE exception_service_info set 
                    status = $status 
                    WHERE op_cluster = '$op_cluster' and op_info = '$op_info' and status = $status_running and info_type = $info_type ";
    
            $ret = self::_queryDB($sql);
        
            if(false == $ret)
            {
                Bingo_Log::warning("exec sql failed!");
                return false;
            }
        } 
    }
}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
