<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Forum.php
 * <AUTHOR>
 * @date 2014/01/27 15:15:17
 * @brief 
 *  
 **/

class Core_Outdata_Forum
{
    /**
     *
     * @param
     * @return array()
     */
    public static function build($arrCoreData)
    {
        $arrForum = $arrCoreData['forum'];
        $arrPost  = $arrCoreData['post']['output'];
        $arrGrade = $arrCoreData['perm']['output']['grade'];

        $arrResult = array(
            // forum
            'id'            => $arrForum['forum_id'],
            'name'          => $arrForum['forum_name'],
            'first_class'   => $arrForum['dir']['level_1_name'],
            'second_class'  => $arrForum['dir']['level_2_name'],
            // grade & perm
            'is_like'       => $arrGrade['is_like'],
            'user_level'    => $arrGrade['level_id'],
            'level_id'      => $arrGrade['level_id'],
            'level_name'    => $arrGrade['level_name'],
            'cur_score'     => $arrGrade['cur_score'],
            'levelup_score' => $arrGrade['cur_score'] + $arrGrade['score_left'],
            'member_num'    => $arrCoreData['member']['output']['member_count'],
            // post
            'is_exists'     => $arrPost['forum_info']['is_exists'],
            'thread_num'    => $arrPost['forum_info']['thread_num'],
            'post_num'      => $arrPost['forum_info']['post_num'],
            'good_classify' => self::_getGoodClass($arrPost['forum_info']['goodclass_show']),
            // bawu
            'managers'      => self::_getManagerList($arrCoreData['bawu']['output']['manager']),
            // sign
            'sign_in_info'  => self::_getSignInfo($arrCoreData['sign']),
            // other
            'album_open_photo_frs' => true,
            'is_readonly'          => '',
            'is_search_people'     => isset($arrForum['attrs']['findpeople_p1']),
            'is_stage_forum'       => isset($arrForum['attrs']['official_p1']),
            'forumvip_show_icon'   => Libs_Util_Tools::buildForumMemberEnter($arrCoreData['forum'], $arrCoreData['user']['user_info'][0]),
        );

        return $arrResult;
    }

    /**
     *
     * @param
     * @return array()
     */
    private static function _getGoodClass($arrClass)
    {
        $arrClassInfo = array();
        foreach ( $arrClass as $arrItem )
        {
            $arrClassInfo[] = array(
                'id'         => $arrItem['id'],
                'name'       => $arrItem['name'],
                'class_id'   => $arrItem['id'],
                'class_name' => $arrItem['name'],
            );
        }

        return $arrClassInfo;
    }

    /**
     *
     * @param
     * @return array()
     */
    private static function _getManagerList($arrManagers)
    {
        $arrOutput   = array();
        if (is_array($arrManagers)) {
            foreach ( $arrManagers as $arrManager ){
                $arrOutput[] = array(
                    'id'   => $arrManager['user']['user_id'],
                    'name' => $arrManager['user']['user_name'],
                );
            }
        }

        return $arrOutput;
    }

    /**
     *
     * @param
     * @return array()
     */
    private static function _getSignInfo($arrSign)
    {
        $arrSignInfo = array(
            'user_info'  => $arrSign['user_info'],
            'forum_info' => $arrSign['forum_info'],
        );
        $arrSignInfo['user_info']['c_sign_num'] = $arrSignInfo['user_info']['cont_sign_num'];
        
        $intContSignNum    = $arrSignInfo['user_info']['cont_sign_num'];
        $intLastSignTime   = $arrSignInfo['user_info']['sign_time'];
        $intYesterdayDate  = getdate(time()-86400);
        $intLatestSignTime = mktime(0, 0, 0, $intYesterdayDate['mon'], $intYesterdayDate['mday'], $intYesterdayDate['year']);
        if ( $intLatestSignTime <= $intLastSignTime ){
            $arrSignInfo['user_info']['c_sign_num'] = $intContSignNum;
        }
        else{
            $arrSignInfo['user_info']['c_sign_num'] = 0;
        }
        
        // 不知道这是什么毛线逻辑
        if ( $arrSignInfo['forum_info']['is_filter'] === false ){
            $arrSignInfo['forum_info']['is_on'] = true;
        }
        else{
            $arrSignInfo['forum_info']['is_on'] = false;
        }
        // end

        return $arrSignInfo;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
