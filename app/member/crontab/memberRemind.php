<?php
/**
 * @file memberRemind.php
 * <AUTHOR>
 * @date 2015/12/22 16:12:52
 * @brief 贴吧会员过期提醒 在过期前3天每天提醒
 *        会员关闭提醒 当天提醒一次
 *        会员召回提醒 过期后3天连续提醒
 **/


$intType = $argv[1];
memberRemind::run($intType);

class memberRemind
{
	//Service_Lib_Im 定义一致
	const MEMBER_EXPIRE_TIP = 4;  //会员过期提醒 即将到期3天发送
    const MEMBER_CLOSED     = 5;  //会员关闭
    const MEMBER_CALLBACK   = 6;  //会员召回提醒 过期后 3天发送

	private static $_objTbMysql = null;
	
	private static $_intStime   = 0;
	private static $_intEtime   = 0;
	private static $_status     = 0;  //会员状态

	private static $_arrRow     = array();

	public static function run($intType)
	{
		if ($intType != self::MEMBER_EXPIRE_TIP && $intType != self::MEMBER_CLOSED && $intType != self::MEMBER_CALLBACK)
		{
			echo "cmd error\n";
			echo "Usage: php memberRemind 4|5|6\n";
			echo "4-会员过去提醒\n5-会员服务关闭提醒\n6-会员召回提醒\n";
			exit;
		}
		self::_init($intType);  //初始化
		self::_getRows($intType);       //获得待处理会员列表
		self::_process($intType);       //process
	}
    /**
    * @brief 初始化
    * @param void
    * @return void
    **/
	private static function _init($intType)
	{
		$objTbMysql = self::_getDb();
		$objTbMysql = empty($objTbMysql) ? self::_getDb() : $objTbMysql;
		self::$_objTbMysql = $objTbMysql;

		//获取各个消息类型下查询时间范围
		switch ($intType)
		{
			case self::MEMBER_CALLBACK : //过期提醒 到期前3天每天发一条
				$intTime = date("Y-m-d", strtotime("-3 days"));
				self::$_intStime = strtotime($intTime);
				self::$_intEtime = strtotime(date("Y-m-d"));
				self::$_status   = ' =0 ';
				break;
			case self::MEMBER_CLOSED : //会员关闭提醒 当天发一次
				$intTime = date("Y-m-d");
				self::$_intStime = strtotime($intTime);
				self::$_intEtime = strtotime($intTime . " 23:59:59");
				//self::$_status   = ' >0 ';
				break;
			case self::MEMBER_EXPIRE_TIP :  //召回提醒 过期后3天每天发一条
				$intTime = date("Y-m-d", strtotime("+4 days"));
				self::$_intStime = strtotime(date("Y-m-d", strtotime("+1 day")));
				self::$_intEtime = strtotime($intTime); //结束时间
				self::$_status   = ' >0 ';
				break;
			default :
				echo "intType error\n";
				exit;
		}
		return true;

	}

	/**
	* @brief process
	* @param array
	* @return
	**/
	private static function _process($intType)
	{
		if (empty(self::$_arrRow) && !is_array(self::$_arrRow))
		{
			return false;
		}

		foreach (self::$_arrRow as $row)
		{
			$intUserId = intval($row['user_id']);
			//由于vip_grow中的过期时间不是总的过期时间 所以需要读取user属性中的过期时间
			$endTime   = ($intType==self::MEMBER_CALLBACK) ? $row['endtime'] : self::_getEndTime($intUserId);
			if ($endTime >= self::$_intStime && $endTime < self::$_intEtime )
			{
				//发送过期提醒
				$arrParam = array(
					'type' => $intType,  //Service_Lib_Im 中定义
					'userId' => $intUserId,
					'expire' => $endTime,
					'score'  => $row['score'],
					'level'  => $row['level'],
				);
				self::_sendMsg($arrParam);
			}
		}
	}
    
    /**
    * @brief 获取符合条件的记录
    * @param  intTime
    * @return array
    **/
	private static function _getRows($intType)
	{
		$arrData = array();
        //是否分页
        if ($intType == self::MEMBER_CLOSED)
        {
        	$sql = "select id,user_id,score,level,endtime,now_status from vip_grow where endtime >=" . self::$_intStime . " and endtime < " . self::$_intEtime;
        }
        else
        {
        	$sql = "select id,user_id,score,level,endtime,now_status from vip_grow where endtime >=" . self::$_intStime . " and endtime < " . self::$_intEtime . " and now_status " . self::$_status;
        }

        echo "[sql]" . $sql . "\n";
		if (!empty(self::$_objTbMysql))
		{
			$arrData = self::$_objTbMysql->query($sql);
		}
		else
		{
			echo "get objMysql failed\n";
		}
		self::$_arrRow = $arrData;
		return true;
	}

	/**
	* @brief 获得会员超时时间
	* @param user_id
	* @return int
	**/
	private static function _getEndTime($user_id=0)
	{
		$intEndTime= 0;
		$intUserId = intval($user_id);
		if ($intUserId <=0 )
		{
			return $intEndTime;
		}

		$arrInput = array(
			'user_id' => $intUserId,
		);

		$arrUserOutput = Tieba_Service::call("user", "getUserData", $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
		if (!isset($arrUserOutput['errno']) || intval($arrUserOutput['errno']) !== 0) 
		{
			echo 'getUserPropsInfo error! [input]'.serialize($arrInput).'[output]' . serialize($arrUserOutput)."\n";
			return $intEndTime;
		}
		if(!isset($arrUserOutput['user_info'][0]))
		{
			echo "user_info has no data. user_id=". $user_id . "\n";
			return $intEndTime;
		}
		$arrUser = $arrUserOutput['user_info'][0];

		return intval($arrUser['mParr_props']['level']['end_time']);
	}

	/**
	* @brief 发送消息
	* @param array
	* @boolean
	**/
	private static function _sendMsg($arrInput)
	{
		$arrRet = Tieba_Service::call("member", "sendMsg", $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
		if (!isset($arrRet['errno']) || intval($arrRet['errno']) !== 0) 
		{
			echo 'call member  sendMsg failed! [input]'.serialize($arrInput).'[output]' . serialize($arrRet) . "\n";
		}
		else
		{
			echo "ExpireRemind send succ:".$arrInput['userId']."\n";
		}
	}
    
    /**
    * @brief 获取数据库对象
    * @param $dbname
    * @param $charset
    * @return obj
    **/
	private static function _getDb($dbname=null,$charset='utf8')
	{
		if(is_null($dbname))
		{
	       $dbname = 'forum_tbmall';
	    }
	    $objTbMysql = Tieba_Mysql::getDB($dbname);
	       if($objTbMysql && $objTbMysql->isConnected()) {
		   $objTbMysql->charset($charset);
		   return $objTbMysql;
	    } 
	    else
	    {
		   Bingo_Log::warning("db connect fail.");
		   return null;
	   }
	}

}
?>