<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-03-11 13:40:01
 * @version
 */
class commitAction extends Bingo_Action_Abstract { 
	
	public static $intErrNo  = 0;
	public static $arrData   = array();
	public static $erroMsg = '';
	
	public static $pc_cmd_no =3825;
	public static $wx_cmd_no =3826;
	public static $cmd_no ;  //3825��ʾpc���ȿ��ƣ�3826��ʾ�������ȿ���
	public static $fid = 0;
	public static $tbs ;
	public static $small_flow =1;
	public static $type =1;       //1Ϊ�ύ�ӿڣ�2Ϊ�����ӿ�
	
	public static $md5key = 'star_@key';  //�����߹�ͨ�õ���Կ
	
	
    public function execute()
   {
   	   
   	    
   	    self::iniParam();
   	    
   	    //�����ӿڣ�search�൱��һ����֧
        if(2==self::$type){   
	    	self::search();
	    }
	    
	    
	    //����-----start-----
   	   self::$intErrNo=3;
   	   self::$erroMsg = "xia xian";
   	   self::$arrData = array();
   	   self::exitFunction();
   	   //����-----end----- 
	    
	    self::checkFunction();
	    
	    //��ȡʡ��
	    $strIp = Bingo_Http_Ip::getUserClientIp();
//$strIp='*************';
	    $strIp = self::reSetIp($strIp);
	    $intIp= Bingo_Http_Ip::ip2long($strIp);
	   
   	    $arrInfo = self::getProvinceByIp($intIp);
   	    if(false === $arrInfo || $arrInfo['province']=='')
   	    {
   	   	    self::$intErrNo=2;
   	        self::$erroMsg = "get ProvinceName fail";
   	        self::$arrData = array();
   	        self::exitFunction();
   	    }
   	    $province_name=$arrInfo['province'];
   	    
   	    //��ȡ����
   	    $fname = self::getFnameByFid(self::$fid);
   	    if(false ==$fname)
   	    {
   	    	self::$intErrNo=2;
   	        self::$erroMsg = "this fid invalid";
   	        self::$arrData = array();
   	        self::exitFunction();
   	    }
   	    $arrInput = array(
            'forum_id'=>self::$fid,
            'forum_name'=>"$fname",
   	        'province_name'=>"$province_name"
            );
   	    $res = Tieba_Service::call("fstar","starCommit",$arrInput);
   	    if($res!==true)
   	    {
   	    	
   	    	self::$intErrNo=2;
   	        self::$erroMsg = "commit fail";
   	        self::$arrData = array();
   	        Bingo_Log::warning(self::$erroMsg." ouput[".$res."]");
   	        self::exitFunction();
   	    }
   	    self::$intErrNo=0;
   	    self::$erroMsg = "commit success";
   	    self::$arrData = array();
   	    Bingo_Log::notice(self::$erroMsg." ouput[".$res."]");
   	    
   	    Tieba_Stlog::addNode('pro', 'tieba');
   	    Tieba_Stlog::addNode('mid', 'starbantu');
   	    Tieba_Stlog::addNode('bduid', rtrim(strip_tags(Bingo_Http_Request::getCookie('BAIDUID')), ':FG=1'));
   	    Tieba_Stlog::addNode('url', strip_tags(Bingo_Http_Request::getServer('REQUEST_URI')));
   	    Tieba_Stlog::addNode('refer', strip_tags(Bingo_Http_Request::getServer('HTTP_REFERER')));
   	    Tieba_Stlog::addNode('agent', strip_tags(Bingo_Http_Request::getServer('HTTP_USER_AGENT')));
   	    
   	    Bingo_Log::buildNotice();
   	    Tieba_Stlog::notice();
   	    
   	    self::exitFunction();
    }
 

 //��麯��
 function checkFunction()
 {
       //���Ŀ¼
	    $bolIsDir = self::checkDir();
	    if(false ===$bolIsDir)//˵��������ЩĿ¼
	    {
	    	self::$intErrNo=2;
   	        self::$erroMsg = "this forum is not in 5 dirs";
   	        self::$arrData = array('fid'=>self::$fid);
   	        self::exitFunction();
	    }
	    //����Ƿ��½
	    $isLogined= false;
        $isLogined  =  Tieba_Session_Socket::isLogin();
        if(!$isLogined)
        { 
			self::$intErrNo=5;
   	        self::$erroMsg = "no login in";
   	        self::$arrData = array('fid'=>self::$fid);
   	        self::exitFunction();      	
        }
        
        //���tbs
        /*$isTbs = false;
        $isTbs = Tieba_Tbs::check(self::$tbs,$isLogined,$intActiveTime=0);
        if(!$isTbs)
        {
        	self::$intErrNo=8;
   	        self::$erroMsg = "tbs invalid";
   	        self::$arrData = array('fid'=>self::$fid);
   	        self::exitFunction();   
        }*/
   	    //���ȿ���
   	    $bolPass = self::checkActsCtrl();
   	    if(false ===$bolPass)
   	    {
   	    	self::$intErrNo=1;
   	    	self::$erroMsg = "too many time";
   	    	self::$arrData = array();
   	    	self::exitFunction();
   	    }
 }
 
//����Ŀ¼�Ƿ��ڹ̶�Ŀ¼
static function checkDir()
{
	$arrInput   = array('forum_id'=>self::$fid);
	$arrDirInfo = Tieba_Service::call("forum","getForumDir",$arrInput);
	if(false===$arrDirInfo || 0!=$arrDirInfo['errno']||empty($arrDirInfo['output']))
	{
		self::$intErrNo=1;
   	    self::$erroMsg = "get dir info fail by fid=".self::$fid;
   	    self::$arrData = array();
   	    self::exitFunction();
	}
	$dirInfo = $arrDirInfo['output'];
	$query = array(
		'type'=>'starbantu',
	//    'dirname'=>$dirInfo['level_1_name']
		'forum_second_dir'=>$dirInfo['level_2_name']
    );
    $res = Tbapi_Midlib_Midl_Cupid::query($query);
    if(empty($res) || !isset($res[self::$small_flow]))
    {
    	return false;
    }
    return true;
	
	
}
static function search()
{
	$strSearch  = trim(Bingo_Http_Request::get("s_input",''));
	if($strSearch=='')
	{
		self::$intErrNo=2;
   	    self::$erroMsg = "invalid s_input";
   	    self::$arrData = array();
   	    self::exitFunction();
	}
	$arrFname = Rpc_Fgate::getFidByFnames(array($strSearch));
	if(empty($arrFname) ||$arrFname[0]['has_forum_id']!=1 || empty($arrFname[0]['forum_res']))
	{
		self::$intErrNo=1;
   	    self::$erroMsg = "get finfo by fid fail";
   	    self::$arrData = array();
   	    self::exitFunction();
	}
	$info =$arrFname[0]['forum_res'];
	self::$fid= $info['forumid'];
	$bolPassDir = self::checkDir();
	if(false===$bolPassDir)
	{
		self::$intErrNo=1;
   	    self::$erroMsg = "no this star";
   	    self::$arrData = array();
   	    self::exitFunction();
	}else 
	{
		self::$intErrNo=0;
   	    self::$erroMsg = "yes,have this star";
   	    self::$arrData = array('fid'=>self::$fid);
   	    self::exitFunction();
	}
	return;
	
}
//������ʼ��
static function iniParam()
{
	$type   =  intval(Bingo_Http_Request::get("type",1));  //type=2����������id���Ǳ��룬1�Ļ��Ǳ���
	$fid    =  intval(Bingo_Http_Request::get("fid",0));
	if(0==$fid && 2!=$type)
	{
		self::$intErrNo=2;
   	    self::$erroMsg = "fid invalid";
   	    self::$arrData = array();
   	    self::exitFunction();
	}
	self::$fid  = $fid;
	self::$cmd_no  = self::$pc_cmd_no;
	self::$type = $type; //type=2�����������ӿ���
	
	self::$tbs = Bingo_Http_Request::get("tbs",'');
	
}
    
//��ȡʡ����Ϣ
public function  getProvinceByIp($intIp) {
		$strMod = 'getIpInfor';
		$strIp = Bingo_Http_Ip::long2ip($intIp);
		$arrInput = array(
			'command_no' => 32000,
			'user_ip' => $intIp,
		);
		Bingo_Timer::start($strMod);
        $bolCwlRes = camel('cwl', 'query', $arrInput, $arrOutput);
		Bingo_Timer::end($strMod);
        //�������cwl�Ľ��Ϊfalse��Ϊ��
        if (false === $bolCwlRes) {
			Bingo_Log::warning("cwl_query_fail");
			return false;
        }
        //����ȥ��ipû���ҵ�����
        elseif (0 !== $bolCwlRes['err_no'] || $bolCwlRes['province'] =='none' || $bolCwlRes['province'] =='None'){
	        $arrCwl = array(
				'province' => '����',
				'city' => '',
	        );
        }
        //�ɹ��ҵ�����
        else {
	        $arrCwl = array(
				'province' => $bolCwlRes['province'],
				'city' => $bolCwlRes['city'],
	        );
        }
        if($arrCwl['province']=='����')
        {
        	$res = self::getOther3Ip($intIp);
        	if($res!=false && $res!='')
        	{
        		$arrCwl['province']=$res;
        	}
        }
        return $arrCwl;
	}

//exit��������
function exitFunction()
{
	$arrData = array(
	'no'=>self::$intErrNo,
	'data'=>self::$arrData,
	'error'=>self::$erroMsg
	);
	echo Bingo_String::array2json($arrData);
	exit;
}
//���ȿ���
private function checkActsCtrl()
{
	
	$key = self::getActsCtrlKey();
	
	//�ж��Ƿ������߹���
	$bduid = $key;
	$myKey = trim(Bingo_Http_Request::get('key',''));
	$md5str = md5($bduid.self::$md5key);
	if($md5str==$myKey)
	self::$cmd_no  = self::$wx_cmd_no;
	
	if($key=='')
	{
		Bingo_Log::warning("getActsCtrlKey fail");
		return false;
	}
	$arrParams = array(
    	'cmd_no' => self::$cmd_no,        //self::$cmd_no=3825��pc�����ȿ��ƣ�3826�����ߵ����ȿ���
    	'BAIDUID' => $key.self::$fid,
    );
    $arrOutput = Rpc_ActsCtrl::callActsctrl($arrParams);
    if (! isset($arrOutput) || empty($arrOutput) || 0!=$arrOutput['err_no']) {
        return false;
    }
    return true;
       
      
}


//��ȡȥ���ȿ��Ƶ�Կ��
function getActsCtrlKey()
{
	//$key = $_COOKIE['BAIDUID'];
	//$key = Bingo_Http_Ip::getUserClientIp();
	$key = Tieba_Session_Socket::getLoginUid();
	return $key;
}    
    
function getFnameByFid($fid=0)
{
	if($fid==0)
	{
		return false;
	}
	$info = Rpc_Fgate::getFnameById(array($fid));
	if($info==false ||!isset($info[0]['forum_name']))
	{
		return false;
	}
	return $info[0]['forum_name'];
}
function getOther3Ip($intIp)
{
	//̨��ip
	$arrTwIP = array(
    0 => '************-**************',
    1 => '*********-*************',
    2 => '**********-************',
    3 => '***********-*************',
    4 => '*********-*************',
    5 => '*********-*************',
    6 => '*********-*************',
    7 => '*********-************',
    8 => '**********-************',
    9 => '**********-************',
    10 => '**********-************',
    11 => '**********-************',
    12 => '**********-************',
    13 => '**********-*************',
    14 => '***********-*************',
    15 => '***********-*************',
    16 => '***********-*************',
    17 => '***********-*************',
    18 => '*********-************',
    19 => '**********-*************',
    20 => '***********-*************',
    21 => '***********-************',
    22 => '**********-************',
    23 => '**********-************',
    24 => '**********-************',
    25 => '**********-************',
    26 => '61.58.96.0-61.58.255.255',
    27 => '61.59.0.0-61.59.159.255',
    28 => '61.59.160.0-61.61.123.255',
    29 => '61.61.124.0-61.61.124.255',
    30 => '61.61.125.0-61.61.255.255',
    31 => '61.62.0.0-61.62.255.255',
    32 => '61.63.0.0-61.63.31.255',
    33 => '61.63.32.0-61.63.95.255',
    34 => '61.63.96.0-61.64.63.255',
    35 => '61.64.64.0-61.64.119.255',
    36 => '61.64.120.0-61.65.255.255',
    37 => '61.66.0.0-61.66.255.255',
    38 => '61.67.0.0-61.67.255.255',
    39 => '61.70.75.0-61.70.75.255',
    40 => '61.70.100.0-61.71.255.255',
    41 => '61.216.0.0-61.216.12.255',
    42 => '61.216.13.0-61.216.13.255',
    43 => '61.216.14.0-61.217.178.255',
    44 => '61.217.179.0-61.217.179.255',
    45 => '61.217.180.0-61.219.255.255',
    46 => '61.220.0.0-61.220.79.255',
    47 => '61.220.80.0-61.220.150.255',
    48 => '61.220.151.0-61.222.255.255',
    49 => '61.223.0.0-61.223.255.255',
    50 => '61.224.0.0-61.224.255.255',
    51 => '61.225.0.0-61.225.255.255',
    52 => '61.226.0.0-61.228.9.255',
    53 => '61.228.10.0-61.228.10.255',
    54 => '61.228.11.0-61.228.152.255',
    55 => '61.228.153.0-61.228.153.255',
    56 => '61.228.154.0-61.231.255.255',
    57 => '134.208.0.0-***************',
    58 => '139.175.0.0-***************',
    59 => '140.92.0.0-140.92.255.255',
    60 => '140.96.0.0-140.96.255.255',
    61 => '140.109.0.0-140.111.255.255',
    62 => '140.112.0.0-140.112.255.255',
    63 => '140.113.0.0-140.138.255.255',
    64 => '163.13.0.0-163.13.255.255',
    65 => '163.14.0.0-163.15.255.255',
    66 => '163.19.0.0-163.19.255.255',
    67 => '163.20.0.0-163.21.255.255',
    68 => '163.24.0.0-163.24.255.255',
    69 => '163.25.0.0-163.25.255.255',
    70 => '163.26.0.0-163.26.255.255',
    71 => '163.27.0.0-163.27.255.255',
    72 => '163.28.0.0-163.32.77.255',
    73 => '163.32.78.0-163.32.80.255',
    74 => '163.32.81.0-163.32.255.255',
    75 => '168.95.0.0-168.95.9.255',
    76 => '168.95.10.0-168.95.199.255',
    77 => '168.95.200.0-168.95.255.255',
    78 => '192.188.171.0-192.188.171.255',
    79 => '192.192.0.0-192.192.97.255',
    80 => '192.192.99.0-192.192.255.255',
    81 => '194.112.177.140-194.112.177.143',
    82 => '194.117.103.2-194.117.103.2',
    83 => '194.117.103.10-194.117.103.10',
    84 => '194.117.103.11-194.117.103.11',
    85 => '194.117.103.15-194.117.103.15',
    86 => '194.117.103.17-194.117.103.17',
    87 => '194.117.103.19-194.117.103.19',
    88 => '194.117.103.21-194.117.103.21',
    89 => '194.117.103.24-194.117.103.24',
    90 => '194.117.103.25-194.117.103.25',
    91 => '194.117.103.50-194.117.103.50',
    92 => '194.117.103.55-194.117.103.55',
    93 => '194.117.103.80-194.117.103.80',
    94 => '194.117.103.87-194.117.103.87',
    95 => '194.117.103.100-194.117.103.100',
    96 => '194.117.103.132-194.117.103.132',
    97 => '194.117.103.135-194.117.103.135',
    98 => '194.117.103.137-194.117.103.137',
    99 => '194.117.103.153-194.117.103.153',
    100 => '194.117.103.156-194.117.103.156',
    101 => '194.117.103.184-194.117.103.185',
    102 => '194.117.103.190-194.117.103.190',
    103 => '194.117.103.194-194.117.103.194',
    104 => '194.117.103.226-194.117.103.226',
    105 => '195.112.167.40-195.112.167.43',
    106 => '195.112.167.64-195.112.167.67',
    107 => '195.112.167.164-195.112.167.167',
    108 => '195.112.167.224-195.112.167.227',
    109 => '195.112.176.0-195.112.176.3',
    110 => '195.112.177.76-195.112.177.79',
    111 => '195.112.177.88-195.112.177.91',
    112 => '195.112.177.116-195.112.177.119',
    113 => '195.112.177.128-195.112.177.131',
    114 => '195.112.177.140-195.112.177.143',
    115 => '195.112.177.216-195.112.177.223',
    116 => '195.112.177.236-195.112.177.239',
    117 => '199.107.119.0-199.107.119.255',
    118 => '202.1.237.0-202.1.237.255',
    119 => '202.2.52.0-202.2.55.255',
    120 => '202.5.224.0-202.5.255.255',
    121 => '202.8.169.0-202.8.184.255',
    122 => '202.14.8.0-202.14.15.255',
    123 => '202.39.0.0-202.39.255.255',
    124 => '202.43.64.0-202.43.95.255',
    125 => '202.43.126.0-202.43.126.255',
    126 => '202.52.64.0-202.52.127.255',
    127 => '202.60.64.0-202.60.95.255',
    128 => '202.61.32.0-202.61.63.255',
    129 => '202.79.100.0-202.79.127.255',
    130 => '202.80.128.0-202.80.159.255',
    131 => '202.129.192.0-202.129.223.255',
    132 => '202.132.0.0-202.133.255.255',
    133 => '202.145.32.0-202.145.255.255',
    134 => '202.154.206.0-202.154.206.255',
    135 => '202.160.64.0-202.160.95.255',
    136 => '202.162.64.0-202.162.95.255',
    137 => '202.166.192.0-202.166.255.255',
    138 => '202.168.192.0-202.168.207.255',
    139 => '202.178.128.0-202.178.132.255',
    140 => '202.178.148.0-202.178.151.255',
    141 => '202.178.224.0-202.178.255.255',
    142 => '202.181.185.0-202.181.185.255',
    143 => '203.64.0.0-203.75.255.255',
    144 => '203.77.0.0-203.77.127.255',
    145 => '203.78.0.0-203.78.31.255',
    146 => '203.79.112.0-203.79.127.255',
    147 => '203.79.128.0-203.79.130.255',
    148 => '203.79.131.0-203.79.131.255',
    149 => '203.79.132.0-203.79.132.255',
    150 => '203.79.133.0-203.79.133.255',
    151 => '203.79.134.0-203.79.134.255',
    152 => '203.79.135.0-203.79.227.255',
    153 => '203.79.228.0-203.79.228.255',
    154 => '203.79.229.0-203.79.229.255',
    155 => '203.79.230.0-203.79.230.255',
    156 => '203.79.231.0-203.79.255.255',
    157 => '203.95.128.0-203.95.227.255',
    158 => '203.95.228.0-203.95.228.255',
    159 => '203.95.229.0-203.95.255.255',
    160 => '203.107.0.0-203.107.63.255',
    161 => '203.133.0.0-203.133.63.255',
    162 => '203.135.224.0-203.135.255.255',
    163 => '203.160.224.0-203.160.255.255',
    164 => '203.187.32.0-203.187.127.255',
    165 => '203.203.78.0-203.203.107.255',
    166 => '203.203.108.0-203.203.108.255',
    167 => '203.203.109.0-203.203.188.255',
    168 => '203.203.189.0-203.203.189.255',
    169 => '203.203.190.0-203.203.255.255',
    170 => '203.204.0.0-203.204.0.255',
    171 => '203.204.1.0-203.204.152.255',
    172 => '210.17.0.0-210.17.127.255',
    173 => '210.58.0.0-210.58.35.255',
    174 => '210.58.36.0-210.58.36.255',
    175 => '210.58.37.0-210.58.147.255',
    176 => '210.58.148.0-210.58.148.255',
    177 => '210.58.149.0-210.58.255.255',
    178 => '210.59.0.0-210.68.72.255',
    179 => '210.68.73.0-210.68.73.255',
    180 => '210.68.74.0-210.71.19.255',
    181 => '210.71.20.0-210.71.20.255',
    182 => '210.71.21.0-210.71.255.255',
    183 => '210.85.0.0-210.85.59.255',
    184 => '210.85.60.0-210.85.135.255',
    185 => '210.85.136.0-210.85.242.255',
    186 => '210.85.253.0-210.85.253.255',
    187 => '210.192.100.0-210.192.109.255',
    188 => '210.192.111.0-210.192.126.255',
    189 => '210.192.128.0-210.192.255.255',
    190 => '210.200.0.0-210.200.95.255',
    191 => '210.200.96.0-210.200.98.255',
    192 => '210.200.99.0-210.200.99.255',
    193 => '210.200.100.0-210.200.100.255',
    194 => '210.200.101.0-210.200.101.255',
    195 => '210.200.102.0-210.200.102.255',
    196 => '210.200.103.0-210.200.103.255',
    197 => '210.200.104.0-210.200.104.255',
    198 => '210.200.105.0-210.200.107.255',
    199 => '210.200.108.0-210.200.108.255',
    200 => '210.200.109.0-210.200.109.255',
    201 => '210.200.110.0-210.200.110.255',
    202 => '210.200.111.0-210.200.127.255',
    203 => '210.200.128.0-210.200.128.255',
    204 => '210.200.129.0-210.200.189.255',
    205 => '210.200.190.0-210.200.190.255',
    206 => '210.200.191.0-210.200.195.255',
    207 => '210.200.196.0-210.200.196.255',
    208 => '210.200.197.0-210.200.216.255',
    209 => '210.200.217.0-210.200.217.255',
    210 => '210.200.218.0-210.201.2.255',
    211 => '210.201.3.0-210.201.3.255',
    212 => '210.201.4.0-210.201.8.255',
    213 => '210.201.9.0-210.201.9.255',
    214 => '210.201.10.0-210.201.18.255',
    215 => '210.201.19.0-210.201.19.255',
    216 => '210.201.20.0-210.201.35.255',
    217 => '210.201.36.0-210.201.255.255',
    218 => '210.202.0.0-210.203.127.255',
    219 => '210.208.0.0-210.208.61.255',
    220 => '210.208.62.0-210.208.62.255',
    221 => '210.208.63.0-210.208.188.255',
    222 => '210.208.189.0-210.208.189.255',
    223 => '210.208.190.0-210.209.63.255',
    224 => '210.240.0.0-210.240.185.255',
    225 => '210.240.186.0-210.240.186.255',
    226 => '210.240.187.0-210.245.22.255',
    227 => '210.245.24.0-210.247.255.255',
    228 => '211.20.0.0-211.20.244.255',
    229 => '211.20.245.0-211.20.245.255',
    230 => '211.20.246.0-211.21.78.255',
    231 => '211.21.81.0-211.22.32.255',
    232 => '211.22.33.0-211.22.33.255',
    233 => '211.22.34.0-211.22.255.255',
    234 => '211.23.0.0-211.23.150.159',
    235 => '211.23.150.160-211.23.150.167',
    236 => '211.23.150.168-211.23.161.255',
    237 => '211.23.162.0-211.23.162.255',
    238 => '211.23.163.0-211.23.254.255',
    239 => '211.23.255.0-211.23.255.255',
    240 => '211.72.0.0-211.72.89.255',
    241 => '211.72.90.0-211.72.90.255',
    242 => '211.72.91.0-211.75.4.255',
    243 => '211.75.5.0-211.75.5.255',
    244 => '211.75.6.0-211.75.71.255',
    245 => '211.75.72.0-211.75.72.255',
    246 => '211.75.73.0-211.75.90.255',
    247 => '211.75.91.0-211.75.91.255',
    248 => '211.75.92.0-211.75.95.255',
    249 => '211.75.96.0-211.75.96.255',
    250 => '211.75.97.0-211.79.255.255',
    251 => '202.39.128.0-202.39.255.255',
    252 => '203.69.0.0-203.69.255.255',
    253 => '203.75.0.0-203.75.255.255',
    254 => '203.74.0.0-203.74.255.255',
    255 => '210.71.128.0-210.71.255.255',
    256 => '210.61.0.0-210.61.255.255',
    257 => '210.62.252.0-210.62.255.255',
    258 => '210.59.128.0-210.59.255.255',
    259 => '210.242.0.0-210.242.127.255',
    260 => '203.68.0.0-203.68.255.255',
    261 => '203.64.0.0-203.64.255.255',
    262 => '203.71.0.0-203.71.255.255',
    263 => '203.72.0.0-203.72.255.255',
    264 => '210.70.0.0-210.70.255.255',
    265 => '210.71.0.0-210.71.127.255',
    266 => '210.60.0.0-210.60.255.255',
    267 => '210.62.224.0-210.62.239.255',
    268 => '210.62.240.0-210.62.247.255',
    269 => '210.67.248.0-210.67.255.255',
    270 => '210.62.64.0-210.62.95.255',
    271 => '210.59.0.0-210.59.127.255',
    272 => '210.240.0.0-210.240.255.255',
    273 => '210.243.0.0-210.243.64.255',
    274 => '203.67.0.0-203.67.255.255',
    275 => '203.70.0.0-203.70.255.255',
    276 => '203.73.0.0-203.73.255.255',
    277 => '210.64.0.0-210.64.255.255',
    278 => '210.66.0.0-210.66.255.255',
    279 => '210.68.0.0-210.68.255.255',
    280 => '210.244.0.0-210.244.127.255',
    281 => '203.65.192.0-203.65.223.255',
    282 => '210.67.64.0-210.67.95.255',
    283 => '210.63.64.0-210.63.127.255',
    284 => '210.241.128.0-210.241.159.255',
    285 => '203.65.0.0-203.65.127.255',
    286 => '203.65.128.0-203.65.159.255',
    287 => '194.117.103.017-194.117.103.017',
    288 => '192.188.171.000-192.188.171.255',
    289 => '194.117.103.132-194.117.103.132',
    290 => '194.117.103.025-194.117.103.025',
    291 => '163.025.000.000-163.025.255.255',
    292 => '199.107.119.000-199.107.119.255',
    293 => '194.117.103.087-194.117.103.087',
    294 => '194.117.103.011-194.117.103.011',
    295 => '195.112.176.000-195.112.176.003',
    296 => '210.071.020.000-210.071.020.255',
    297 => '210.208.189.000-210.208.189.255',
    298 => '168.095.010.000-168.095.199.255',
    299 => '163.028.000.000-163.032.255.255',
    300 => '140.112.000.000-140.112.255.255',
    301 => '210.017.000.000-210.017.127.255',
    302 => '202.178.224.000-202.178.255.255',
    303 => '202.166.192.000-202.166.255.255',
    304 => '202.145.032.000-202.145.063.255',
    305 => '202.145.064.000-202.145.127.255',
    306 => '202.145.128.000-202.145.255.255',
    307 => '202.014.008.000-202.014.015.255',
    308 => '202.162.064.000-202.162.095.255',
    309 => '202.160.064.000-202.160.095.255',
    310 => '203.079.000.000-203.079.255.255',
    311 => '203.078.000.000-203.078.031.255',
    312 => '202.060.064.000-202.060.095.255',
    313 => '202.043.064.000-202.043.095.255',
    314 => '210.240.000.000-210.247.255.255',
    315 => '203.135.224.000-203.135.255.255',
    316 => '203.064.000.000-203.075.255.255',
    317 => '203.160.224.000-203.160.255.255',
    318 => '202.080.128.000-202.080.159.255',
    319 => '202.002.052.000-202.002.055.255',
    320 => '203.095.128.000-203.095.255.255',
    321 => '210.192.000.000-210.192.255.255',
    322 => '203.077.000.000-203.077.127.255',
    323 => '140.109.000.000-140.138.255.255',
    324 => '202.052.064.000-202.052.127.255',
    325 => '202.061.032.000-202.061.063.255',
    326 => '210.059.000.000-210.071.255.255',
    327 => '210.208.000.000-210.209.063.255',
    328 => '***************-***************',
    329 => '***************-***************',
    330 => '***************-***************',
    331 => '***************-***************',
    332 => '***************-***************',
    333 => '***************-***************',
    334 => '***************-***************',
    335 => '***************-***************',
    336 => '***************-***************',
    337 => '***************-***************',
    338 => '***************-***************',
);
    //���ip
	$arrHKIP = array(
    0 => '*********-*************',
    1 => '*********-*************',
    2 => '*********-*************',
    3 => '*********-*************',
    4 => '*************-***************',
    5 => '***********-***************',
    6 => '**********-**************',
    7 => '***********-***************',
    8 => '*********-*************',
    9 => '***********-***************',
    10 => '***********-***************',
    11 => '**********-**************',
    12 => '***********-***************',
    13 => '**********-**************',
    14 => '*************-***************',
    15 => '************-**************',
    16 => '*************-***************',
    17 => '**************-**************',
    18 => '***************-***************',
    19 => '***************-***************',
    20 => '***************-***************',
    21 => '*************-*************',
    22 => '***************-***************',
    23 => '*************-***************',
    24 => '202.0.77.0-202.0.78.255',
    25 => '202.0.100.0-202.0.100.255',
    26 => '202.0.104.0-202.0.104.255',
    27 => '202.0.112.0-202.0.112.255',
    28 => '202.0.122.0-202.0.123.255',
    29 => '202.0.128.0-202.0.147.255',
    30 => '202.0.160.0-202.0.183.255',
    31 => '202.1.6.0-202.1.7.255',
    32 => '202.2.32.0-202.2.51.255',
    33 => '202.2.64.0-202.2.95.255',
    34 => '202.4.159.0-202.4.223.255',
    35 => '202.7.128.0-202.7.159.255',
    36 => '202.12.4.0-202.12.7.255',
    37 => '202.14.67.0-202.14.68.255',
    38 => '202.14.80.0-202.14.80.255',
    39 => '202.14.222.0-202.14.222.255',
    40 => '202.20.66.0-202.20.66.255',
    41 => '202.20.88.0-202.20.89.255',
    42 => '202.20.94.0-202.20.95.255',
    43 => '202.20.98.0-202.20.98.255',
    44 => '202.20.100.0-202.20.101.255',
    45 => '202.20.111.0-202.20.111.255',
    46 => '202.20.117.0-202.20.118.255',
    47 => '202.20.125.0-202.20.127.255',
    48 => '202.21.128.0-202.21.128.255',
    49 => '202.40.0.0-202.40.255.255',
    50 => '202.45.0.0-202.45.255.255',
    51 => '202.52.128.0-202.52.159.255',
    52 => '202.53.128.0-202.53.159.255',
    53 => '202.57.224.0-202.57.255.255',
    54 => '202.60.224.0-202.60.255.255',
    55 => '202.63.0.0-202.63.31.255',
    56 => '202.63.128.0-202.63.159.255',
    57 => '202.64.0.0-202.65.31.255',
    58 => '202.65.198.0-202.69.247.255',
    59 => '202.69.248.0-202.69.251.255',
    60 => '202.69.252.0-202.74.31.255',
    61 => '202.74.224.0-202.75.95.255',
    62 => '202.75.224.0-202.76.127.255',
    63 => '202.76.224.0-202.78.31.255',
    64 => '202.78.224.0-202.79.31.255',
    65 => '202.79.224.0-202.80.31.255',
    66 => '202.81.0.0-202.89.255.255',
    67 => '202.123.192.0-202.123.255.255',
    68 => '202.130.64.0-202.130.159.255',
    69 => '202.140.96.0-202.140.127.255',
    70 => '202.144.224.0-202.144.255.255',
    71 => '202.146.96.0-202.146.127.255',
    72 => '202.161.224.0-202.161.255.255',
    73 => '202.163.0.0-202.163.31.255',
    74 => '202.169.0.0-202.169.31.255',
    75 => '202.170.0.0-202.170.31.255',
    76 => '202.177.0.0-202.177.31.255',
    77 => '202.180.128.0-202.180.158.255',
    78 => '202.181.176.0-202.181.176.255',
    79 => '202.181.224.0-202.181.255.255',
    80 => '202.182.224.0-202.182.255.255',
    81 => '203.78.65.0-203.78.100.255',
    82 => '203.80.0.0-203.86.255.255',
    83 => '203.90.224.0-203.90.255.255',
    84 => '203.99.162.0-203.99.162.255',
    85 => '203.105.0.0-203.105.63.255',
    86 => '203.131.224.0-203.131.255.255',
    87 => '203.168.199.0-203.168.199.255',
    88 => '203.169.182.0-203.169.182.255',
    89 => '203.176.145.0-203.176.145.255',
    90 => '203.184.141.0-203.184.141.255',
    91 => '203.184.192.0-203.185.63.255',
    92 => '203.186.0.0-203.186.95.255',
    93 => '203.195.0.0-203.195.255.255',
    94 => '203.198.0.0-203.198.255.255',
    95 => '203.218.0.0-203.218.255.255',
    96 => '205.252.135.0-205.252.150.255',
    97 => '206.220.224.0-206.220.227.255',
    98 => '207.176.113.0-207.176.113.255',
    99 => '208.139.98.0-208.139.98.255',
    100 => '208.151.79.0-208.151.92.255',
    101 => '210.0.150.0-210.0.178.255',
    102 => '210.0.180.0-210.0.195.255',
    103 => '*********-*************',
    104 => '*********-*************',
    105 => '***********-*************',
    106 => '************-**************',
    107 => '***********-***************',
    108 => '*************-***************',
    109 => '*************-***************',
    110 => '*************-***************',
    111 => '***********-***************',
    112 => '***********-***************',
    113 => '**********-**************',
    114 => '**********-**************',
);
 //����ip
	$arrAomenIP = array(
    0 => '************-**************',
    1 => '***********-**************',
    2 => '***********-**************',
    3 => '**********-*************',
    4 => '**********-**************',
    5 => '************-**************',
    6 => '**********-*************',
    7 => '*************-***************',
    8 => '************-**************',
    9 => '************-**************',
    10 => '*************-***************',
    11 => '***********-*************',
    12 => '***********-***************',
    13 => '*************-***************',
    14 => '**********-**************',
    15 => '*************-***************',
    );



    $strIp = Bingo_Http_Ip::long2ip(trim($intIp));
    $intIp = ip2long($strIp);
   
   
    foreach($arrTwIP as $val)
    {
    	$arrData = explode("-",$val);
    	 	if(count($arrData)!=2)
    	continue;
    	$arrData[0]= self::reSetIp($arrData[0]);
    	$arrData[1]= self::reSetIp($arrData[1]);
    	$intLow   = ip2long(trim($arrData[0]));
    	$intHight = ip2long(trim($arrData[1]));
    	if($intIp>=$intLow&&$intIp<$intHight)
    	{
    		return '̨��';
    		break;
    	}
    }
    foreach($arrHKIP as $val)
    {
    	$arrData = explode("-",$val);
    	if(count($arrData)!=2)
    	continue;
    	$arrData[0]= self::reSetIp($arrData[0]);
    	$arrData[1]= self::reSetIp($arrData[1]);
    	$intLow   = ip2long(trim($arrData[0]));
    	$intHight = ip2long(trim($arrData[1]));
    	if($intIp>=$intLow&&$intIp<$intHight)
    	{
    		return '���';
    		break;
    	}
    }
    foreach($arrAomenIP as $val)
    {
    	$arrData = explode("-",$val);
    	if(count($arrData)!=2)
    	continue;
    	$arrData[0]= self::reSetIp($arrData[0]);
    	$arrData[1]= self::reSetIp($arrData[1]);
    	$intLow   = ip2long(trim($arrData[0]));
    	$intHight = ip2long(trim($arrData[1]));
    	if($intIp>=$intLow&&$intIp<$intHight)
    	{
    		return '����';
    		break;
    	}
    }
    return false;
	
}
function reSetIp($ip)
{
	$arrNum = explode('.',$ip);
	foreach($arrNum as &$val)
	{
		if($val{0}==0&&$val{1}==0)
		{
			$val=$val{2};
		}
	    if($val{0}==0&&$val{1}!=0)
		{
			$val=$val{1}.$val{2};
		}
		
	}
	$strIp = intval($arrNum[0]).'.'.intval($arrNum[1]).'.'.intval($arrNum[2]).'.'.intval($arrNum[3]);
	return $strIp;
}
 /**
  * ���notice��־
  * 
  * @param array $arrData
  * @return boolean 
  * <AUTHOR>
  */   
  public function pushNoticInfo($arrData)
  {
  	    if(empty($arrData))return false;
  	    foreach($arrData as $key =>$value)
  	    {
  	    	if($key == 'idcard' || $key == 'name') continue;
  	    	Bingo_Log::pushNotice($key,$value);
  	    }
  	    return true;
  }
 }


?>
