<?php

class setResInfoAction extends Util_Base {
    	protected $_is_check = false;
	public function execute(){
		try {
			$machine_id = strval(Bingo_Http_Request::getNoXssSafe('machine_id', ''));
			$res_type_id = strval(Bingo_Http_Request::getNoXssSafe('res_type_id', ''));
			$version_id = strval(Bingo_Http_Request::getNoXssSafe('version_id', ''));
			$res_infos = strval(Bingo_Http_Request::getNoXssSafe('res_infos', ''));
			
			if (empty ($machine_id) || empty($res_type_id) || empty($version_id) || 
				empty($res_infos)) {
				Bingo_Log::warning ( "param is error");
				$intErrno = Tieba_Errcode::ERR_PARAM_ERROR;
				throw new Util_Exception ( Tieba_Error::getErrmsg ( $intErrno ), $intErrno );
			}
			
			$arrInput = array();
			$arrInput['machine_id'] = $machine_id;
			$arrInput['res_type_id'] = $res_type_id;
			$arrInput['version_id'] = $version_id;
			$arrInput['res_infos'] = Bingo_String::json2array($res_infos);
			
			$arrOut = Tieba_Service::call('resourcecenter', 'setResInfo', $arrInput, NULL, NULL, 'post', 'php', 'utf-8', 'local');		
			if ($arrOut === false || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				Bingo_Log::warning(("call setResInfo fail. [input: ".serialize($arrInput)."][ret: ".serialize($arrOut)."]"));
				$intErrno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
				throw new Util_Exception(Tieba_Error::getErrmsg($intErrno), $intErrno);
			}
			
			$retData = $arrOut['outInfo'];
			
			// 默认成功返回值
			$this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,Tieba_Error::getUserMsg(Tieba_Errcode::ERR_SUCCESS),$retData);
		}catch(Util_Exception $e){
			Bingo_Log::warning( "errno=".$e->getCode() ." errmsg=".$e->getMessage());
			$this->_jsonRet($e->getCode(), $e->getMessage());
		}
	}
}

?>
