<?php
require_once(dirname(__FILE__)."/ResourceCalculator.php");

class RedisCalculator extends ResourceCalculator {
    
    const RES_TYPE = 'Redis';
    const RESOUCE_STAT_API = 'http://st01-arch-platform00.st01.baidu.com:8023/resource/redis?product=forum';
    const API_RETRY = 3;
    
    protected static $_team_conf = null;
    
    public function __construct() {
        self::_init(ResourceCalculator::RES_TYPE_REDIS, ResourceCalculator::MACHINE_TYPE_C32);
    }
    
    protected function _getCostInfo() {
        
        $ret = false;
        $errno = -1;
        $intRetry = self::API_RETRY;
        $strUrl = self::RESOUCE_STAT_API;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $strUrl);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);
        
        while ($intRetry --){
            $ret = curl_exec($ch);            
            $errno = curl_errno($ch);
            if($errno == 0) {
                break;
            } else {
                Bingo_Log::warning("call redis api fail. retry: ".$intRetry);
            }
        }
        curl_close($ch);
        
        if ($errno !== 0) {
            Bingo_Log::fatal("all retry fail.");
            return false;
        }
        
        $arrRawData = Bingo_String::json2array($ret);        
        if ($arrRawData['errno'] !== 0) {
            Bingo_Log::fatal("get redis raw data fail, ret: ".$ret);
            return false;
        }
        
        $arrCostInfo = array();
        
        foreach ($arrRawData['data'] as $redisName => $costInfo) {
        	$info = array();
        	$arrTmp = explode('forum-', $redisName);
        	$resName = str_replace('-', '_', $arrTmp[1]);
        	$info[ResourceCalculator::STATS_KEY_RES_NAME] = $resName;
        	$info[ResourceCalculator::STATS_KEY_MACHINE_NUM] = $costInfo['cost'];
        	$arrCostInfo []= $info;
        }
        	
        return $arrCostInfo;        
    }
        
    protected function _getTeamInfo($resList) {
    	 
    	if (self::$_team_conf == null) {
    		self::$_team_conf = Bd_Conf::getConf("/app/resourcecenter/redis_team_info");
    		if(self::$_team_conf == false){
    			Bingo_Log::fatal("init team conf fail");
    			return false;
    		}
    	}
    	 
    	if (empty($resList)) {
    		Bingo_Log::fatal("invalid res list.");
    		return false;
    	}
    	 
    	$teamInfo = array();
    
    	foreach ($resList as $resName) {
    		if (isset(self::$_team_conf[$resName])) {
    			$teamInfo[$resName] = intval(self::$_team_conf[$resName]);
    		} else {
    			Bingo_Log::warning("no team info for: ".$resName);
    			$teamInfo[$resName] = ResourceCalculator::TEAM_ID_UNKNOWN;
    		}
    	}
    	 
    	return $teamInfo;
    }
    
}