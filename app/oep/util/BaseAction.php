<?php
/**
 * ui action的基类
 **/
abstract class Util_BaseAction extends Bingo_Action_Abstract {

    // 错误号
    protected $_intErrno = 0;

    // 错误信息
    protected $_strError = 'success';

    //用户信息
    protected $_user_info = array();
    //hades调用
	protected $_hades = 0;
    public function init(){
    	//验证是否为hades调用
    	$this->_hades = Bingo_Http_Request::get('hades', '');
    	if(1 == intval($this->_hades)){
    		return true;
    	}
        //获取当前用户信息
        $this->_getUserInfo();
        return true;
    }

    protected function _tbsCheck($strTbs) {
        if(!Tieba_Tbs::check($strTbs, true)){
            return false;
        }
        return true;
    }

    /*
     * uuap 获取用户登录状态以及权限
     *
     */
    protected function _getUserInfo() {

        if($this->_user_info['is_login'] == false)
        {
            $this->_user_info = UserAuth::getUserInfo ();
            //if ($this->_user_info['is_login'] !== true) {
            //	return false;
            //}
           // $perm = UserAuth :: getUserLevel ($this->_user_info['name']);
            //$this->_user_info['perm'] = $perm;
        }
    }

    // 权限检测
    protected  function _checkPower () {
        //判断用户是否登录
        if (false === $this->_user_info['is_login']) {
            $strErr = sprintf ('the user is not login, no power to update project info');
            //TODO 补充错误号
            throw new Oep_Error(Oep_Errno::NO_LOGIN, $strErr);
        }


        if (0 === $this->_user_info['perm']) {
            $strErr = sprintf ('the user has no power to update project info');
            throw new Oep_Error(Oep_Errno::NO_POWER, $strErr);
        }
    }

    /*
    * 返回值赋值为json
    * 
    */
    protected function assignJson ($arrInput) {
        Bingo_Timer::start('build_json');
        //echo Bingo_String::array2json (self::$_arrOut);
        echo json_encode($arrInput);
        Bingo_Timer::end('build_json');
    }

    protected function _errRet($errno,$errmsg){
        $erroInfo = array(
            'errno' => $errno,
            'errmsg' => $errmsg,
        );
        echo json_encode($erroInfo);
    }

    //兼容之前的版本，todo 后面和_jsonRet合并
    protected function _buildRawJsonRet($errno,$errmsg,array $arrExtData=array()){
        $arrRet = array(
            'error'=>array(
                'errno'=>intval($errno),
                'errmsg'=>strval($errmsg),
            ),
        );
        $arrRet = array_merge($arrRet,$arrExtData);

        echo json_encode($arrRet);
        Bingo_Http_Response::contextType('application/json');
    }



    protected function _jsonRet($errno,$errmsg,array $arrExtData=array()){
        $arrRet = array(
            'no'=>intval($errno),
            'error'=>strval($errmsg),
            'data'=>$arrExtData,
        );
        foreach($arrRet as $k=>$v){
            Bingo_Page::assign($k,$v);
        }
        Bingo_Page::setOnlyDataType("json");
        Bingo_Http_Response::contextType('application/json');
    }
    
    

    protected function _serialRet($errno,$errmsg,array $arrExtData=array()){
        $arrRet = array(
            'no'=>intval($errno),
            'error'=>strval($errmsg),
            'data'=>$arrExtData,
        );
        foreach($arrRet as $k=>$v){
            Bingo_Page::assign($k,$v);
        }
        Bingo_Page::setOnlyDataType("serial");
        Bingo_Http_Response::contextType('application/json');
    }

    protected function _retInnerError(){

        $errorArr =  array(
            'errnum' => 1001,
            'errmsg' => '内部错误'
        );


        $arrRet = array(
            'error' => $errorArr
        );

        echo json_encode($arrRet);
    }

}
