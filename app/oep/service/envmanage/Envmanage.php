<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-08-08 09:37:45
 * @version
 * @structs & methods(copied from idl.)
*/

require_once dirname(__FILE__)."/../../../../libs/lib/invite/InviteInterface.php";
class Service_Envmanage_Envmanage extends Util_BaseService{

const SERVICE_NAME = "Service_Envmanage_Envmanage";
//根据产品线id获取产品线名称 
private static function _getProLineByID($pid){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	$productID = intval($pid);
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$dlArrInput = array(
			'function' => 'getProLineByID',
			'pid' => $productID,
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	$proLine = $dlArrOut['results'][0][0]['pidName'];
	return $proLine;
}
//根据产品线名称获取产品线id
private static function _getPidByProLine($proLine){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$dlArrInput = array(
			'function' => 'getPidByProLine',
			'pidName' => $proLine,
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	$pid = $dlArrOut['results'][0]['pid'];
	return $pid;
}
//根据envTypeId获取环境类型信息
private static function _getEnvTypeInfoByTypeID($envTypeId){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	$typeID = intval($envTypeId);
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$dlArrInput = array(
			'function' => 'getEnvTypeInfoByTypeID',
			'envTypeId' => $typeID,
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	$envTypeInfo = $dlArrOut['results'][0][0];
	return $envTypeInfo;
}
//验证用户角色
private static function _checkUser($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['username']) || !isset($arrInput['role']) || !isset($arrInput['pid'])){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	//角色验证
	$productID = intval($arrInput['pid']);
	$role = intval($arrInput['role']);
	$dlArrInput=array(
			'function' => 'checkRoleInfo',
			'pid' => $productID,
			'username' => $arrInput['username'],
			'role' => $role,
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	//角色不存在或者查找失败
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	if(!count($dlArrOut['results'][0])){
		Util_Log::warning('the user not exit ');
		return self::_errRet(Oep_Errno::USER_NOT_EXIT);
	}
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;

}
public static function sendEmail($arrInput){
	$title = $arrInput['title'];
	$mailserver = 'mail2-in.baidu.com';
	$to_addr = $arrInput['toAddr'];
	$from_name = 'oep';
	$to_name = 'touser';
	$content = $arrInput['content'];
	$from_mail = '<EMAIL>';
	$email_title  = iconv('utf-8','gbk',$title);

	$objSmtp = new Smtp($mailserver);
	$bolRes = $objSmtp->send ($from_name,$to_name,$from_mail,
			$to_addr,$email_title,false,$content,false);
}
//扩容新增service 和 group 入库
public static function addServiceAndGroup(){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$arrInput = array(
			'username' => 'yanyuee',
			'pid' => 1,
			'role' => 2,
	);
	$envTypeLists = Util_Service::innerCall('oep','getEnvTypeInfoOfPid',$arrInput);
	if( $envTypeLists['errno'] != Oep_Errno::SUCCESS){
		Util_Log::warning("call getEnvTypeInfoOfPid fail");
		return self::_errRet(Oep_Errno::CALL_SERVICE_ERROR);
	}
	$envTypeList=$envTypeLists['userInfo'];
	foreach ($envTypeList as $envTemp){
		//查找每种环境类型现有总数
		$arrInfoInput = array(
				'username' => 'yanyuee',
				'pid' => 1,
				'role' => 2,
				'envTypeId' => $envTemp['envTypeId'],
		);
		$envLists = Util_Service::innerCall('oep','findEnv',$arrInfoInput);
		if( $envLists['errmsg'] != Oep_Errno::SUCCESS){
		    Util_Log::warning("call findEnv fail");
	    }
		$existNum=count($envLists['envInfo']);
		//调orp扩容接口
		for($i=1;($existNum+$i) <= $envTemp['totalNumInstance'] ;$i++){
			$arrAddService=array(
					'service_name' => 'oepService' . time(),
					'cluster_id' => 111,
					'operator' => 'test',
					'cpu' => $envTemp['cpu'],
					'mem' => $envTemp['mem'],
					'platform' => "oep",
					'arr_dynamic_port' => 90,
					'url' => 'http://oep.baidu.com/oep/callback/cbAddservice?hades=1',
					'param' => array(
							'envTypeId' => $envTemp['envTypeId'],
							),
			);
			$serviceHadOut = Util_Service::innerCall('oep','addservice',$arrAddService);
			//解析返回
			$serviceParamTmp = json_decode($serviceHadOut, true);
			Bingo_log::warning(print_r( $serviceParamTmp ,1));
	
		/*	$arrAddgroup=array(
					'service_id' => $serviceParamTmp['data']['service_id'],
					'group_name' => 'oepGroup',
					'operator' => 'test',
					'idc' => 'jx',
			);
			$groupHadOut = Util_Service::innerCall('oep','addgroup',$arrAddgroup);
			//解析返回
			$groupParamTmp = json_decode($groupHadOut, true);
			Bingo_log::warning(print_r( $groupParamTmp ,1));
	
			//插入数据库
			$arrInsertInput=array(
					'envTypeId'=>$envTemp['envTypeId'],
					'serviceId'=>$serviceParamTmp['data']['service_id'],
					'groupId'=>$groupParamTmp['data']['group_id'],
			);
			$setEnvInput = Util_Service::innerCall('oep','setEnvInfo',$arrInsertInput);
			*/
		}
	}

	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;
}

//扩容入库
public static function expandInstance(){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	
	$unEnvNameListsOut = Util_Service::innerCall('oep','getAllUnEnvName','');
	if( $unEnvNameListsOut['errmsg'] != Oep_Errno::SUCCESS){
		Util_Log::warning("call getAllUnEnvName fail");
		return self::_errRet(Oep_Errno::CALL_SERVICE_ERROR);
	}
	$unEnvNameLists=$unEnvNameListsOut['envInfo'];
	foreach ($unEnvNameLists as $envTmp){
		$arrExpand=array(
				'group_id' => $envTmp['groupId'],
				'count' => 1,
				'url' => 'http://oep.baidu.com/oep/callback/cbExpand?hades=1',
				'param' => array(
						'envTypeId'=>$envTmp['envTypeId'],
						'serviceId'=>$envTmp['serviceId'],
						'groupId'=>$envTmp['groupId'],
				),
		);
		$expandHadOut = Util_Service::innerCall('oep','expand',$arrExpand);
	}

	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;
}

//扩容插入环境serviceId和groupId和envTypeId
public static function getAllUnEnvName(){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	
	$dlArrInput = array(
			'function' => 'getAllUnEnvName',
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	$envInfo=$dlArrOut['results'][0];
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'envInfo' => $envInfo,
	);
	return $arrOutput;
}
//获取待更新环境
public static function getAllUnupEnv($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	$dlArrInput = array(
				'function' => 'getAllUnupEnv',
		);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	$envInfo=$dlArrOut['results'][0];
	$unUpName=array();
	foreach ($envInfo as $tmp){
		$arrEnvTypeID = array(
				'envTypeId'	=> 	$tmp['envTypeId'],
		);
		$envTypeConf = Util_Service::innerCall('oep','getBaseEnvConfByEnvTypeId',$arrEnvTypeID);
		if($envTypeConf['errno'] !== Oep_Errno::SUCCESS){
			Util_Log::warning('call service getBaseEnvConfByEnvTypeId fail');
			return self::_errRet(Oep_Errno::CALL_SERVICE_ERROR);
		}
		$tmp['pid']=$envTypeConf['envConfInfo'][0]['pid'];
		if(( isset($arrInput['pid']) && $tmp['pid']==intval($arrInput['pid']) ) || !isset($arrInput['pid'])) {
			$unUpName[]=$tmp;
		}
	}
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'envInfo' => $unUpName,
	);
	return $arrOutput;
}
//获取待删除环境
public static function getAllUnDelEnv(){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}

	$dlArrInput = array(
			'function' => 'getAllUnDelEnv',
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	$envInfo=$dlArrOut['results'][0];
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'envInfo' => $envInfo,
	);
	return $arrOutput;
}

//扩容插入环境serviceId和groupId和envTypeId
public static function setEnvInfo($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!isset($arrInput['envTypeId']) || !isset($arrInput['serviceId']) || !isset($arrInput['groupId']) ){
		Util_Log::warning("input params invalid.");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$serviceId=intval($arrInput['serviceId']);
	$groupId=intval($arrInput['groupId']);
	$envTypeId=intval($arrInput['envTypeId']);
	$dlArrInput = array(
			'function' => 'setEnv',
			'serviceId' => $serviceId,
			'envTypeId' => $envTypeId,
			'groupId' => $groupId,
			'envStatus' => 3,
				
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;
}

//扩容插入环境
public static function upEnvName($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!isset($arrInput['envName']) || !isset($arrInput['envTypeId']) || !isset($arrInput['machineName']) || !isset($arrInput['serviceId']) || !isset($arrInput['groupId']) ){
		Util_Log::warning("input params invalid.");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$serviceId=intval($arrInput['serviceId']);
	$groupId=intval($arrInput['groupId']);
	$envTypeId=intval($arrInput['envTypeId']);
	$dlArrInput = array(
			'function' => 'upEnv',
			'serviceId' => $serviceId,
			'envTypeId' => $envTypeId,
			'groupId' => $groupId,
			'envName' => $arrInput['envName'],
			'machineName' => $arrInput['machineName'],

			
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;
}

//更新环境的recover状态
public static function upEnvInfoOfRecoverStat($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!isset($arrInput['envName']) || !isset($arrInput['envTypeId']) || !isset($arrInput['jobNum']) ){
		Util_Log::warning("input params invalid.");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$envTypeId=intval($arrInput['envTypeId']);
	$jobNum=intval($arrInput['jobNum']);
	//检测回调次数
	$checkInput = array(
			'function' => 'checkEnvRecoverResult',
			'envTypeId' => $envTypeId,
			'envName' => $arrInput['envName'],
	);
	$checkOutput = self::_dl_query($checkInput);
	if (!$checkOutput || $checkOutput['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($checkInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	if($checkOutput['results'][0][0]['recoverResult']!=$jobNum){
		$upInput = array(
				'function' => 'upEnvInfoOfRecoverStat',
				'envTypeId' => $envTypeId,
				'envName' => $arrInput['envName'],
				'recoverResult' => $checkOutput['results'][0][0]['recoverResult']+1,
		);
		$upOutput = self::_dl_query($upInput);
		if (!$upOutput || $upOutput['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail. dlArrInput = '.serialize($upInput));
			return Oep_Errno::GET_PROLINE_FAIL;
		}
	}else {
		$upInput = array(
		'function' => 'upEnvInfoOfRecoverStat',
		'envTypeId' => $envTypeId,
		'envName' => $arrInput['envName'],
		'recoverResult' => 0,
		);
		$upOutput = self::_dl_query($upInput);
		if (!$upOutput || $upOutput['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail. dlArrInput = '.serialize($upInput));
			return Oep_Errno::GET_PROLINE_FAIL;
		}
	}
	/*$dlArrInput = array(
			'function' => 'upEnvInfoOfRecoverStat',
			'envTypeId' => $envTypeId,
			'envName' => $arrInput['envName'],
			'recoverResult' => 0,				
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}*/
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;
}

//更新环境的envStatus状态
public static function upEnvInfoOfenvStatus($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!isset($arrInput['envName']) || !isset($arrInput['envStatus']) ){
		Util_Log::warning("input params invalid.");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$envStatus=intval($arrInput['envStatus']);
	$dlArrInput = array(
			'function' => 'upEnvInfoOfenvStatus',
			'envName' => $arrInput['envName'],
			'envStatus' => $envStatus,
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;
}

//更新环境的到期时间
public static function upEnvEndtime($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!isset($arrInput['envName']) || !isset($arrInput['endTime']) ){
		Util_Log::warning("input params invalid.");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	$endTime = intval($arrInput['endTime'])+86400;
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$envStatus=intval($arrInput['envStatus']);
	$dlArrInput = array(
			'function' => 'upEnvEndtime',
			'envName' => $arrInput['envName'],
			'endTime' => $endTime,
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;
}

//扩容插入环境
public static function setPortInfo($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!isset($arrInput['envName']) || !isset($arrInput['envTypeId']) || !isset($arrInput['port']) ){
		Util_Log::warning("input params invalid.");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$envTypeId=intval($arrInput['envTypeId']);
	$portInfo=$arrInput['port'];
	$dlArrInput = array(
			'function' => 'setPort',
			'envTypeId' => $envTypeId,
			'envName' => $arrInput['envName'],
			'portInfo' => $portInfo,
				
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;
}
//根据ServiceName获取环境信息
public static function getEnvInfoByServiceName($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!isset($arrInput['serviceId']) ){
		Util_Log::warning("input params invalid.");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$serviceId=intval($arrInput['serviceId']);
	$dlArrInput = array(
			'function' => 'getEnvInfoByServiceName',
			'serviceId' => $serviceId,
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	$envInfo = $dlArrOut['results'][0][0];
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'envInfo' => $envInfo,
	);
	return $arrOutput;
}
//根据EnvName获取serviceId和groupId
public static function getEnvInfoByEnvName($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!isset($arrInput['envName']) ){
		Util_Log::warning("input params invalid.");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$dlArrInput = array(
			'function' => 'getSerAndGroIdByEnvName',
			'envName' => $arrInput['envName'],
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	$envInfo = $dlArrOut['results'][0][0];
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'envInfo' => $envInfo,
	);
	return $arrOutput;
}
//更新环境的envStatus状态
public static function addProduct($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!isset($arrInput['productId']) || !isset($arrInput['productName']) ){
		Util_Log::warning("input params invalid.");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$productId=intval($arrInput['productId']);
	$dlArrInput = array(
			'function' => 'addProduct',
			'pid' => $productId,
			'pidName' => $arrInput['productName'],
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;
}
//获取用户未审核通过的信息
public static function getUserUnPassInfo($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!isset($arrInput['username']) ){
		Util_Log::warning("input params invalid.");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}

	$dlArrInput = array(
			'function' => 'getUserUnPassInfo',
			'username' => $arrInput['username'],
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	$userInfo=$dlArrOut['results'][0];
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'userInfo' => $userInfo,
	);
	return $arrOutput;
}
//代码部署用，查询环境接口
public static function getEnvForModuleDeploy($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['envName'])){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	$nowTime = time();

	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}

	$dlArrInput = array(
			'function' => 'findEnvByEnvName',
			'envName' => $arrInput['envName'],
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	$envResult = $dlArrOut['results'][0][0];

	if(count($dlArrOut['results'][0]) == 0){
		Util_Log::warning('env not exit');
		return self::_errRet(Oep_Errno::NO_ENV);
	}
	if($envResult['envStatus'] == 4 || $envResult['envStatus'] == 3 || $envResult['envStatus'] == 0 || ($envResult['envStatus'] == 1 && $envResult['endTime'] < $nowTime)){
		Util_Log::warning('env not variable');
		return self::_errRet(Oep_Errno::ENV_NOT_VARIABLE);
	}
	//根据envTypeId获取envType和pid
	$arrEnvTypeID = array(
			'envTypeId'	=> 	$envResult['envTypeId'],
	);
	$envTypeConf = Util_Service::innerCall('oep','getBaseEnvConfByEnvTypeId',$arrEnvTypeID);
	if($envTypeConf['errno'] !== Oep_Errno::SUCCESS){
		Util_Log::warning('call service getBaseEnvConfByEnvTypeId fail');
		return self::_errRet(Oep_Errno::CALL_SERVICE_ERROR);
	}
	$proLine = self::_getProLineByID($envTypeConf['envConfInfo'][0]['pid']);
	if($proLine === Oep_Errno::GET_PROLINE_FAIL || $proLine === Oep_Errno::DB_CONN_ERROR){
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	$envInfo = array(
			'productLine' => $proLine,
			'pid' => $envTypeConf['envConfInfo'][0]['pid'],
	);
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'envInfo' => $envInfo,
	);
	return $arrOutput;
}
//代码部署用，检测并更新module_deploy库
public static function updateModuleDeploy($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['envName']) || !isset($arrInput['pipeline'])){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}

	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}

	$dlArrInput = array(
			'function' => 'checkModuleDeploy',
			'envName' => $arrInput['envName'],
			'pipeline' => $arrInput['pipeline'],
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	$checkModuleDeployResult = $dlArrOut['results'][0];
	$nowTime = time();
	if(count($checkModuleDeployResult) == 0){
		$dlInsertArrInput = array(
				'function' => 'insertModuleDeploy',
				'envName' => $arrInput['envName'],
				'pipeline' => $arrInput['pipeline'],
				'deployStat' => 1,
				'ts' => $nowTime,
		);
		$dlInsertArrOut = self::_dl_query($dlInsertArrInput);
		if (!$dlInsertArrOut || $dlInsertArrOut['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlInsertArrInput));
			return self::_errRet(Oep_Errno::DL_CALL_FAIL);
		}
	}else{
		$dlUpArrInput = array(
				'function' => 'upModuleDeploy',
				'envName' => $arrInput['envName'],
				'pipeline' => $arrInput['pipeline'],
				'deployStat' => 1,
				'ts' => $nowTime,
				'job_id' => '',
				'noahUrl' => '',
		);
		$dlUpArrOut = self::_dl_query($dlUpArrInput);
		if (!$dlUpArrOut || $dlUpArrOut['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlUpArrInput));
			return self::_errRet(Oep_Errno::DL_CALL_FAIL);
		}
	}
	
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;
}
//代码部署用，更新job_id
public static function updateJobId($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['envName']) || !isset($arrInput['pipeline']) || !isset($arrInput['job_id'])){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}

	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}

	$dlArrInput = array(
			'function' => 'upJobId',
			'envName' => $arrInput['envName'],
			'pipeline' => $arrInput['pipeline'],
			'job_id' => $arrInput['job_id'],
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}

	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;
}
//代码部署用，更新状态和诺亚单
public static function updateDeployResult($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['envName']) || !isset($arrInput['pipeline']) || !isset($arrInput['noahUrl']) || !isset($arrInput['deployStat'])){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}

	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}

	$dlArrInput = array(
			'function' => 'updateDeployResult',
			'envName' => $arrInput['envName'],
			'pipeline' => $arrInput['pipeline'],
			'noahUrl' => $arrInput['noahUrl'],
			'deployStat' => $arrInput['deployStat'],
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}

	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;
}
//代码部署用，查看环境部署信息
public static function getModuleDeployInfo($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['envName']) || !isset($arrInput['pipeline'])){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}

	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}

	$dlArrInput = array(
			'function' => 'getModuleDeployInfo',
			'envName' => $arrInput['envName'],
			'pipeline' => $arrInput['pipeline'],
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	$deployInfo=$dlArrOut['results'][0][0];

	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'deployInfo' => $deployInfo,
	);
	return $arrOutput;
}
//代码部署用，总入口
public static function moduleDeploy($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['envName']) || !isset($arrInput['pipeline'])  || !isset($arrInput['submitInfo']) ){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}

	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}

	//调用updateModuleDeploy的service接口,检测并更新数据库
	$arrOutputTemp = Util_Service::innerCall('oep','updateModuleDeploy',$arrInput);
	if($arrOutputTemp['errno'] != Oep_Errno::SUCCESS){
		Bingo_Log::warning ( "call updateModuleDeploy fail" );
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	
	//根据envName获取group_id
	$arrEnvInfo = Util_Service::innerCall('oep','getEnvInfoByEnvName',$arrInput);
	if($arrEnvInfo['errno'] != Oep_Errno::SUCCESS){
		Bingo_Log::warning ( "call getEnvInfoByEnvName fail" );
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	$group_id=$arrEnvInfo['envInfo']['groupId'];
		
	//调hades部署接口
	$submitInput=$arrInput['submitInfo'];
	$submitRes = Util_Service::innerCall('oep','deploysubmit',$submitInput);
	$submitRes = json_decode($submitRes,true);
	Util_Log::warning('deploysubmit output is: '.print_r($submitRes,1));
	if (!$submitRes || $submitRes['result'] != true )
	{
		Bingo_Log::warning ( "call deploysubmit fail" );
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
		
	//根据生成的job_id更新数据库 updateJobId
	$arrUpJobIdInput=array(
			'envName'=>$arrInput['envName'],
			'pipeline'=>$arrInput['pipeline'],
			'job_id'=>$submitRes['data']['job_id'],
	);
	$arrUpJobIdOut = Util_Service::innerCall('oep','updateJobId',$arrUpJobIdInput);
	if($arrUpJobIdOut['errno'] != Oep_Errno::SUCCESS){
		Bingo_Log::warning ( "call updateJobId fail" );
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	//调hades执行接口
	$deployInput = array
	(
			'job_id'=> $submitRes['data']['job_id'],
			'group_id'=> $group_id,
			'url'=> 'http://oep.baidu.com/oep/callback/cbModuleDeploy?hades=1',
			'param'=> array(
					'envName' => $arrInput['envName'],
					'pipeline'=> $arrInput['pipeline'],
			)
	);
	$deployRes = Util_Service::innerCall('oep','deployrunjob',$deployInput);
		
	$deployRes = json_decode($deployRes,true);
	Bingo_Log::warning('deployrunjob output is: '.print_r($deployRes, 1));
	//对部署替换结果进行判断
	if (!$deployRes || $deployRes['result'] != true )
	{
		Bingo_Log::warning ( "call deployrunjob fail" );
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
		
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;
}
//查找环境
public static function findEnv($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['username']) || !isset($arrInput['role']) ||!isset($arrInput['pid']) || (!isset($arrInput['envStatus']) && !isset($arrInput['envTypeId']) && !isset($arrInput['envName']) && !isset($arrInput['user']))){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	$productID = intval($arrInput['pid']);
	$nowTime = time();
	$role = intval($arrInput['role']);
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	//角色验证
	$arrDlInput=array(
			'username' => $arrInput['username'],
			'pid' => $productID,
			'role' => $role,
	);
	$checkUser = self::_checkUser($arrDlInput);
	if (!$checkUser || $checkUser['errno'] != Oep_Errno::SUCCESS){
		Util_Log::warning('no perm');
		return self::_errRet(Oep_Errno::NO_POWER);
	}
	
	
	
	/*$userInfo = Service_Envmanage_Envmanage::findUser(array('username'=>$arrInput['username'],'rolename'=>"yanyuee",'role'=>2,'pid'=>1));
	if ($userInfo['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call findUser fail');
		return self::_errRet(Oep_Errno::GET_USERINFO_FAIL);
	}
	if($userInfo['userInfo']['role']!=$arrInput['role'] || $userInfo['userInfo']['pid']!=$arrInput['pid']){
		Util_Log::warning("input params invalid");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	*/
	if(isset($arrInput['envTypeId'])){
		$envTypeId = intval($arrInput['envTypeId']);
		//根据envTypeId获取envType和pid
		$arrEnvTypeID = array(
				'envTypeId'	=> 	$envTypeId,
		);
		$envTypeConf = Util_Service::innerCall('oep','getBaseEnvConfByEnvTypeId',$arrEnvTypeID);
		if($envTypeConf['errno'] !== Oep_Errno::SUCCESS){
			Util_Log::warning('call service getBaseEnvConfByEnvTypeId fail');
			return self::_errRet(Oep_Errno::CALL_SERVICE_ERROR);
		}
		if($arrInput['role'] !=2 && $envTypeConf['envConfInfo'][0]['pid'] != $productID){
			Util_Log::warning("input params invalid");
			return self::_errRet(Oep_Errno::PARAM_ERR);
		}
		if(!self::_init(self::SERVICE_NAME)){
			return Oep_Errno::CONF_ERROR;
		}
		$dlTypeIDInput = array(
				'function' => 'findEnvInfoByTypeID',
				'envTypeId' => $envTypeId,
				//'endTime' => $nowTime,
		);
		$dlTypeIDOutput=self::_dl_query($dlTypeIDInput);
		if (!$dlTypeIDOutput || $dlTypeIDOutput['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlTypeIDInput));
			return self::_errRet(Oep_Errno::DL_CALL_FAIL);
		}
		$envInfoOuts = $dlTypeIDOutput['results'][0];
		$envInfo = array();
		$typeInfo = self::_getEnvTypeInfoByTypeID($envTypeId);
		if($typeInfo === Oep_Errno::GET_PROLINE_FAIL || $typeInfo === Oep_Errno::DB_CONN_ERROR){
			return Oep_Errno::GET_PROLINE_FAIL;
		}
		$pidName = self::_getProLineByID($envTypeConf['envConfInfo'][0]['pid']);
		if($pidName == Oep_Errno::GET_PROLINE_FAIL || $pidName == Oep_Errno::DB_CONN_ERROR){
			return Oep_Errno::GET_PROLINE_FAIL;
		}
		foreach ($envInfoOuts as $envInfoOut){
			if($envInfoOut['username']!=$arrInput['username'] && $arrInput['role']==0){
				continue;
			}
			$envInfo[] = array(
					'envName' => $envInfoOut['envName'],
					'productLine' => $pidName,
					'pid' => $envTypeConf['envConfInfo'][0]['pid'],
					'username' => $envInfoOut['username'],
					'endTime' => $envInfoOut['endTime']-86400,
					'envStatus' => $envInfoOut['envStatus'],
					'renewNum' => $envInfoOut['renewNum'],
					'remark' => $envInfoOut['remark'],
					'portInfo' => $envInfoOut['portInfo'],
					'recoverResult' => $envInfoOut['recoverResult'],
					'cpu' => $typeInfo['cpu'],
					'disk' => $typeInfo['disk'],
					'mem' => $typeInfo['mem'],
					'envType' =>  $envTypeConf['envConfInfo'][0]['envType'],
					'envTypeId' => $envTypeId,
			);
		}
		//返回值
		$error = Oep_Errno::SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Oep_Errno::getErrnoMsg($error),
				'envInfo' => $envInfo,
		);
		return $arrOutput;
	}
	if(isset($arrInput['envName'])){
		$dlArrInput = array(
				'function' => 'findEnvByEnvName',
				'envName' => $arrInput['envName'],
				//'endTime' => $nowTime,
		);
		$dlArrOut = self::_dl_query($dlArrInput);
		if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
			return self::_errRet(Oep_Errno::DL_CALL_FAIL);
		}
		$envResult = $dlArrOut['results'][0][0];
		if($envResult['username']!=$arrInput['username'] && $arrInput['role']==0){
			Util_Log::warning('the user has no perm ');
		    return self::_errRet(Oep_Errno::NO_POWER);
		}
		if(count($envResult) === 0){
			Util_Log::warning('env not exit');
			return self::_errRet(Oep_Errno::NO_ENV);
		}
		//根据envTypeId获取envType和pid
		$arrEnvTypeID = array(
				'envTypeId'	=> 	$envResult['envTypeId'],
		);
		$envTypeConf = Util_Service::innerCall('oep','getBaseEnvConfByEnvTypeId',$arrEnvTypeID);
		if($envTypeConf['errno'] !== Oep_Errno::SUCCESS){
			Util_Log::warning('call service getBaseEnvConfByEnvTypeId fail');
			return self::_errRet(Oep_Errno::CALL_SERVICE_ERROR);
		}
		if($arrInput['role'] !=2 && $envTypeConf['envConfInfo'][0]['pid'] != $productID){
			Util_Log::warning("input params invalid");
			return self::_errRet(Oep_Errno::PARAM_ERR);
		}	
		$envTypeInfo = self::_getEnvTypeInfoByTypeID($envResult['envTypeId']);
		if($envTypeInfo === Oep_Errno::GET_PROLINE_FAIL || $envTypeInfo === Oep_Errno::DB_CONN_ERROR){
			return Oep_Errno::GET_PROLINE_FAIL;
		}
		$proLine = self::_getProLineByID($envTypeConf['envConfInfo'][0]['pid']);
		if($proLine === Oep_Errno::GET_PROLINE_FAIL || $proLine === Oep_Errno::DB_CONN_ERROR){
			return Oep_Errno::GET_PROLINE_FAIL;
		}
		$envInfo = array(
				
				'envName' => $arrInput['envName'],
				'productLine' => $proLine,
				'pid' => $envTypeConf['envConfInfo'][0]['pid'],
				'endTime' => $envResult['endTime']-86400,
				'envStatus' => $envResult['envStatus'],
				'renewNum' => $envResult['renewNum'],
				'portInfo' => $envResult['portInfo'],
				'remark' => $envResult['remark'],
				'recoverResult' => $envResult['recoverResult'],
				'cpu' => $envTypeInfo['cpu'],
				'disk' => $envTypeInfo['disk'],
				'mem' => $envTypeInfo['mem'],
				'envType' => $envTypeConf['envConfInfo'][0]['envType'],
				'envTypeId' => $envResult['envTypeId']
		);
		//返回值
		$error = Oep_Errno::SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Oep_Errno::getErrnoMsg($error),
				'envInfo' => $envInfo,
		);
		return $arrOutput;
	}
	
	if(isset($arrInput['envStatus'])){
		$dlArrInput = array(
				'function' => 'findEnvByenvStatus',
				'envStatus' => $arrInput['envStatus'],
		);
		$dlArrOut = self::_dl_query($dlArrInput);
		if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
			return self::_errRet(Oep_Errno::DL_CALL_FAIL);
		}
		$envResults = $dlArrOut['results'][0];
		$envInfo = array();
		foreach ($envResults as $envResult){
			$arrEnvTypeID = array(
					'envTypeId'	=> 	$envResult['envTypeId'],
			);
			$envTypeConf = Util_Service::innerCall('oep','getBaseEnvConfByEnvTypeId',$arrEnvTypeID);
			if($envTypeConf['errno'] !== Oep_Errno::SUCCESS){
				Util_Log::warning('call service getBaseEnvConfByEnvTypeId fail');
				return self::_errRet(Oep_Errno::CALL_SERVICE_ERROR);
			}
			$envInfo[] = array(
					'envName' => $envResult['envName'],
					'envTypeId' => $envResult['envTypeId'],
					'pid' => $envTypeConf['envConfInfo'][0]['pid'],
					'envType' => $envTypeConf['envConfInfo'][0]['envType'],
			);
		}
		//返回值
		$error = Oep_Errno::SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Oep_Errno::getErrnoMsg($error),
				'envInfo' => $envInfo,
		);
		return $arrOutput;
	}
	if(isset($arrInput['user'])){
		if($arrInput['username']!=$arrInput['user'] && $role==0){
			Util_Log::warning('the user has no perm ');
			return self::_errRet(Oep_Errno::NO_POWER);
		}
		if($role==0 || (isset($arrInput['from']) && $arrInput['from'] == 'envup')){
			$dlArrInput = array(
					'function' => 'findEnvByUserName',
					'username' => $arrInput['user'],
					'endTime' => $nowTime,
			);
			$dlArrOut = self::_dl_query($dlArrInput);
			if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
				Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
				return self::_errRet(Oep_Errno::DL_CALL_FAIL);
			}
			$userEnvInfos = $dlArrOut['results'][0];
			/*if(count($userEnvInfos) === 0){
			 Util_Log::warning('no env exit');
			return self::_errRet(Oep_Errno::NO_ENV);
			}*/
			$envInfo = array();
			foreach ($userEnvInfos as $userEnvInfo){
				//根据envTypeId获取envType和pid
				$arrEnvTypeID = array(
						'envTypeId'	=> 	$userEnvInfo['envTypeId'],
				);
				$envTypeConf = Util_Service::innerCall('oep','getBaseEnvConfByEnvTypeId',$arrEnvTypeID);
				if($envTypeConf['errno'] !== Oep_Errno::SUCCESS){
					Util_Log::warning('call service getBaseEnvConfByEnvTypeId fail');
					return self::_errRet(Oep_Errno::CALL_SERVICE_ERROR);
				}
				if($envTypeConf['envConfInfo'][0]['pid'] != $productID){
					continue;
				}
				$envTypeInfo = self::_getEnvTypeInfoByTypeID($userEnvInfo['envTypeId']);
				if($envTypeInfo === Oep_Errno::GET_PROLINE_FAIL || $envTypeInfo === Oep_Errno::DB_CONN_ERROR){
					return Oep_Errno::GET_PROLINE_FAIL;
				}
				$proLine = self::_getProLineByID($envTypeConf['envConfInfo'][0]['pid']);
				if($proLine === Oep_Errno::GET_PROLINE_FAIL || $proLine === Oep_Errno::DB_CONN_ERROR){
					return Oep_Errno::GET_PROLINE_FAIL;
				}
				$envInfo[] = array(
						'envName' => $userEnvInfo['envName'],
						'productLine' => $proLine,
						'username' => $arrInput['user'],
						'pid' => $envTypeConf['envConfInfo'][0]['pid'],
						'endTime' => $userEnvInfo['endTime']-86400,
						'envStatus' => $userEnvInfo['envStatus'],
						'renewNum' => $userEnvInfo['renewNum'],
						'portInfo' => $userEnvInfo['portInfo'],
						'remark' => $userEnvInfo['remark'],
						'recoverResult' => $userEnvInfo['recoverResult'],
						'cpu' => $envTypeInfo['cpu'],
						'disk' => $envTypeInfo['disk'],
						'mem' => $envTypeInfo['mem'],
						'envType' => $envTypeConf['envConfInfo'][0]['envType'],
						'envTypeId' => $userEnvInfo['envTypeId'],
				);
			}
		}elseif ($role==1){
			$proLine = self::_getProLineByID($productID);
			if($proLine === Oep_Errno::GET_PROLINE_FAIL || $proLine === Oep_Errno::DB_CONN_ERROR){
				return Oep_Errno::GET_PROLINE_FAIL;
			}
			$arrPidInput=array(
					'pid'=>$productID,
					);
			$arrEnvConfOut = Util_Service::innerCall('oep','getEnvTypeByPid',$arrPidInput);
			if(!$arrEnvConfOut || $arrEnvConfOut['errno'] !== Oep_Errno::SUCCESS){
				Util_Log::warning('call service getBaseEnvConfByPid fail');
				return self::_errRet(Oep_Errno::CALL_SERVICE_ERROR);
			}
			$arrEnvConf=$arrEnvConfOut['envTypeInfo'];
			$arrEnvListe=array();
			$envInfo=array();
			if(!self::_init(self::SERVICE_NAME)){
				return Oep_Errno::CONF_ERROR;
			}
			foreach ($arrEnvConf as $temp){
				$arrEnvTypeIdInput=array(
						'function' => 'findAllEnvByTypeID',
						'envTypeId'=> $temp['envTypeId'],
						);
				$arrEnvOut = self::_dl_query($arrEnvTypeIdInput);
				if (!$arrEnvOut || $arrEnvOut['errno'] != Oep_Errno::SUCCESS ){
					Util_Log::warning('call dl query fail. dlArrInput = '.serialize($arrEnvTypeIdInput));
					return self::_errRet(Oep_Errno::DL_CALL_FAIL);
				}
				$arrEnvList=$arrEnvOut['results'][0];
				$envTypeInfo=self::_getEnvTypeInfoByTypeID($temp['envTypeId']);
				if($typeInfo === Oep_Errno::GET_PROLINE_FAIL || $typeInfo === Oep_Errno::DB_CONN_ERROR){
					return Oep_Errno::GET_PROLINE_FAIL;
				}
				foreach ($arrEnvList as $envInfoOut){
					$envInfo[] = array(
							'envName' => $envInfoOut['envName'],
							'productLine' => $proLine,
							'username' => $envInfoOut['username'],
							'pid' => $productID,
							'endTime' => $envInfoOut['endTime']-86400,
							'envStatus' => $envInfoOut['envStatus'],
							'renewNum' => $envInfoOut['renewNum'],
							'portInfo' => $envInfoOut['portInfo'],
							'remark' => $envInfoOut['remark'],
							'recoverResult' => $envInfoOut['recoverResult'],
							'cpu' => $envTypeInfo['cpu'],
							'disk' => $envTypeInfo['disk'],
							'mem' => $envTypeInfo['mem'],
							'envType' => $temp['envType'],
							'envTypeId' =>$temp['envTypeId'],
					);
				}
			}
		}

		//返回值
		$error = Oep_Errno::SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Oep_Errno::getErrnoMsg($error),
				'envInfo' => $envInfo,
		);
		return $arrOutput;
	}
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'envInfo' => '',
	);
	return $arrOutput;
}
//获取对应产品线的管理员
public static function getManageOfPid($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!isset($arrInput['pid']) ){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	$productId= intval($arrInput['pid']);
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$arrDlInput=array(
			'function' => 'getManageOfPid',
			'pid' => $productId,
	);
	$ArrDlOut = self::_dl_query($arrDlInput);
	if (!$ArrDlOut || $ArrDlOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($ArrDlOut));
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	
	$manageInfo = $ArrDlOut['results'][0];
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'userInfo' => $manageInfo,
	);
	return $arrOutput;

}
//获取产品线的审核通过的用户
public static function getDecideUser($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['username']) || !isset($arrInput['role']) || !isset($arrInput['pid']) ){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$productID = intval($arrInput['pid']);
	$role = intval($arrInput['role']);
	if($role!=0){
		//验证角色是否存在
		$arrDlInput=array(
				'username' => $arrInput['username'],
				'pid' => $productID,
				'role' => $role,
		);
		$checkUser = self::_checkUser($arrDlInput);
		if (!$checkUser || $checkUser['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('user not exit');
			return self::_errRet(Oep_Errno::USER_NOT_EXIT);
		}
		$decUsers=array();
		if($role==1){
			$arrPidInput=array(
					'function' => 'getUserOfPid',
					'pid' => $productID,
					'userPass' => 1,
			);
			$arrDecOut = self::_dl_query($arrPidInput);
			if (!$arrDecOut || $arrDecOut['errno'] != Oep_Errno::SUCCESS ){
				Util_Log::warning('call dl query fail');
				return self::_errRet(Oep_Errno::DL_CALL_FAIL);
			}
			$arrDecUser=$arrDecOut['results'][0];
			$pidName=self::_getProLineByID($productID);
			if($pidName == Oep_Errno::GET_PROLINE_FAIL || $pidName == Oep_Errno::DB_CONN_ERROR){
				return Oep_Errno::GET_PROLINE_FAIL;
			}
			foreach ($arrDecUser as $decUser){
				$temp=array();
				$temp['pid']=$productID;
				$temp['role']=$decUser['role'];
				$temp['pidName']=$pidName;
				$temp['username']=$decUser['username'];
				$decUsers[]=$temp;
			}
				
		}elseif($role == 2){
			$arrPidInput=array(
					'function' => 'getAllPassUser',
					'userPass' => 1,
			);
			$decOut = self::_dl_query($arrPidInput);
			if (!$decOut || $decOut['errno'] != Oep_Errno::SUCCESS ){
				Util_Log::warning('call dl query fail');
				return self::_errRet(Oep_Errno::DL_CALL_FAIL);
			}
			$arrDecUser=$decOut['results'][0];
			foreach ($arrDecUser as $unDecUser){
				$temp=array();
				$pidName=self::_getProLineByID($unDecUser['pid']);
				if($pidName == Oep_Errno::GET_PROLINE_FAIL || $pidName == Oep_Errno::DB_CONN_ERROR){
					return Oep_Errno::GET_PROLINE_FAIL;
				}
				$temp['pid']=$productID;
				$temp['role']=$unDecUser['role'];
				$temp['pidName']=$pidName;
				$temp['username']=$unDecUser['username'];
				$decUsers[]=$temp;
			}
		}
	}
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'userInfo' => $decUsers,
	);
	return $arrOutput;

}

//获取产品线的未审核用户
public static function getUnDecideUser($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['username']) || !isset($arrInput['role']) || !isset($arrInput['pid']) ){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$productID = intval($arrInput['pid']);
	$role = intval($arrInput['role']);
	if($role!=0){
		//验证角色是否存在
		$arrDlInput=array(
				'username' => $arrInput['username'],
				'pid' => $productID,
				'role' => $role,
		);
		$checkUser = self::_checkUser($arrDlInput);
		if (!$checkUser || $checkUser['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('user not exit');
			return self::_errRet(Oep_Errno::USER_NOT_EXIT);
		}
		$unDecUsers=array();
		if($role==1){
			$arrPidInput=array(
					'function' => 'getUserOfPid',
					'pid' => $productID,
					'userPass' => 0,
			);
			$arrUnDecOut = self::_dl_query($arrPidInput);
			if (!$arrUnDecOut || $arrUnDecOut['errno'] != Oep_Errno::SUCCESS ){
				Util_Log::warning('call dl query fail');
				return self::_errRet(Oep_Errno::DL_CALL_FAIL);
			}
			$arrUnDec=$arrUnDecOut['results'][0];
			foreach ($arrUnDec as $unDecUser){
				$temp=array();
				$pidName=self::_getProLineByID($productID);
				if($pidName == Oep_Errno::GET_PROLINE_FAIL || $pidName == Oep_Errno::DB_CONN_ERROR){
					return Oep_Errno::GET_PROLINE_FAIL;
				}
				$temp['pid']=$productID;
				$temp['role']=$unDecUser['role'];
				$temp['pidName']=$pidName;
				$temp['username']=$unDecUser['username'];
				$unDecUsers[]=$temp;
			}
			
		}elseif($role == 2){
			$arrPidInput=array(
					'function' => 'getAllPassUser',
					'userPass' => 0,
			);
			$arrUnDecOut = self::_dl_query($arrPidInput);
			if (!$arrUnDecOut || $arrUnDecOut['errno'] != Oep_Errno::SUCCESS ){
				Util_Log::warning('call dl query fail');
				return self::_errRet(Oep_Errno::DL_CALL_FAIL);
			}
			$arrUnDec=$arrUnDecOut['results'][0];
			foreach ($arrUnDec as $unDecUser){
				$temp=array();
				$pidName=self::_getProLineByID($unDecUser['pid']);
				if($pidName == Oep_Errno::GET_PROLINE_FAIL || $pidName == Oep_Errno::DB_CONN_ERROR){
					return Oep_Errno::GET_PROLINE_FAIL;
				}
				$temp['pid']=$unDecUser['pid'];
				$temp['role']=$unDecUser['role'];
				$temp['pidName']=$pidName;
				$temp['username']=$unDecUser['username'];
				$unDecUsers[]=$temp;
			}
		}
	}
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'userInfo' => $unDecUsers,
	);
	return $arrOutput;

}
//获取所有产品线
public static function getAllPro(){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$arrInput=array(
			'function' => 'getAllPro',
	);
	$arrOutPut = self::_dl_query($arrInput);
	if (!$arrOutPut || $arrOutPut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($arrInput));
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	$proInfo = $arrOutPut['results'][0];
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'proInfo' => $proInfo,
	);
	return $arrOutput;

}
//获取用户在某产品线的权限
public static function getUserRoleOfPid($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['username']) || !isset($arrInput['pid'])){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$productID = intval($arrInput['pid']);
	$userInput=array(
			'function' => 'getUserInfoByPid',
			'username' => $arrInput['username'],
			'pid' => $productID,
	);
	$userArrOut = self::_dl_query($userInput);
	if (!$userArrOut || $userArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($userInput));
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	$userRole=array();
	$userInfoTemp = $userArrOut['results'][0][0];
	if(count($userInfoTemp)==0 || $userInfoTemp['userPass']==0){
		Util_Log::warning('user not exit');
		return self::_errRet(Oep_Errno::USER_NOT_EXIT);
	}else{
		$userRole['role']=$userInfoTemp['role'];
	}
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'userInfo' => $userRole,
	);
	return $arrOutput;

}
//获取用户所有产品线信息
public static function getUserInfo($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['username']) ){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}

	$userInput=array(
			'function' => 'getUserInfo',
			'username' => $arrInput['username'],
	);
	$userArrOut = self::_dl_query($userInput);
	if (!$userArrOut || $userArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($userInput));
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	$userInfoTemp = $userArrOut['results'][0];
	$userInfo = array();
	foreach ($userInfoTemp as $temp){
		if($temp['userPass']==1){
			$tempInfo=array();
			$tempInfo['pid']=$temp['pid'];
			$tempInfo['pidName']=self::_getProLineByID($temp['pid']);
			if($tempInfo['pidName'] == Oep_Errno::GET_PROLINE_FAIL || $tempInfo['pidName'] == Oep_Errno::DB_CONN_ERROR){
				return Oep_Errno::GET_PROLINE_FAIL;
			}
			$tempInfo['role']=$temp['role'];
			$userInfo[]=$tempInfo;
		}
	}
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'userInfo' => $userInfo,
	);
	return $arrOutput;

}

//获取对应产品线的所有环境类型配置信息
public static function getEnvTypeInfoOfPid($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['username']) || !isset($arrInput['role']) || !isset($arrInput['pid'])){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$productId=intval($arrInput['pid']);
	$role=intval($arrInput['role']);

	//角色验证
	$arrDlInput=array(
			'username' => $arrInput['username'],
			'pid' => $productId,
			'role' => $role,
	);
	$checkUser = self::_checkUser($arrDlInput);
	if (!$checkUser || $checkUser['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('no perm');
		return self::_errRet(Oep_Errno::NO_POWER);
	}
	$arrEnvTypeInfo=array();
	if($role==0){
		Util_Log::warning("current user has no perm");
		return Oep_Errno::NO_POWER;
	}elseif($role==1){
		$arrInput = array(
					'pid' => $productId,
			);
		$envTypeListsOut = Util_Service::innerCall('oep','getBaseEnvConfByPid',$arrInput);
		if(!$envTypeListsOut || $envTypeListsOut['errmsg'] != Oep_Errno::SUCCESS){
			Bingo_Log::warning ( "call getBaseEnvConfByPid fail" );
			$arrOutput ['errno'] = Oep_Errno::CALL_SERVICE_ERROR;
			$arrOutput ['errmsg'] = 'call service fail';
			throw new Oep_Error ( $arrOutput ['errno'], $arrOutput ['errmsg'] );
		}
		$pidName=self::_getProLineByID($productId);
		if($pidName == Oep_Errno::GET_PROLINE_FAIL || $pidName == Oep_Errno::DB_CONN_ERROR){
			return Oep_Errno::GET_PROLINE_FAIL;
		}
		$envTypeLists=$envTypeListsOut['envConfInfo'];
		foreach ($envTypeLists as $envTemp){
			$envTypeInfo=self::_getEnvTypeInfoByTypeID($envTemp['envTypeId']);
			if($envTypeInfo === Oep_Errno::GET_PROLINE_FAIL || $envTypeInfo === Oep_Errno::DB_CONN_ERROR){
				return Oep_Errno::GET_PROLINE_FAIL;
			}
			$envTypeInfo['envTypeId']=$envTemp['envTypeId'];
			$envTypeInfo['envType']=$envTemp['envType'];
			$envTypeInfo['pid']=$productId;
			$envTypeInfo['pidName']=$pidName;
			$arrEnvTypeInfo[]=$envTypeInfo;
		}
	}elseif($role==2){
		$envTypeListsOut = Util_Service::innerCall('oep','getAllBaseEnvConf',$arrInput);
		if(!$envTypeListsOut || $envTypeListsOut['errmsg'] != Oep_Errno::SUCCESS){
			Bingo_Log::warning ( "call getAllBaseEnvConf fail" );
			$arrOutput ['errno'] = Oep_Errno::CALL_SERVICE_ERROR;
			$arrOutput ['errmsg'] = 'call service fail';
			throw new Oep_Error ( $arrOutput ['errno'], $arrOutput ['errmsg'] );
		}
		$envTypeLists=$envTypeListsOut['envConfInfo'];
		foreach ($envTypeLists as $envTemp){
			$pidName=self::_getProLineByID($envTemp['pid']);
			if($pidName == Oep_Errno::GET_PROLINE_FAIL || $pidName == Oep_Errno::DB_CONN_ERROR){
				return Oep_Errno::GET_PROLINE_FAIL;
			}
			$envTypeInfo=self::_getEnvTypeInfoByTypeID($envTemp['envTypeId']);
			if($envTypeInfo == Oep_Errno::GET_PROLINE_FAIL || $envTypeInfo == Oep_Errno::DB_CONN_ERROR){
				return Oep_Errno::GET_PROLINE_FAIL;
			}
			$envTypeInfo['envTypeId']=$envTemp['envTypeId'];
			$envTypeInfo['envType']=$envTemp['envType'];
			$envTypeInfo['pid']=$envTemp['pid'];
			$envTypeInfo['pidName']=$pidName;
			$arrEnvTypeInfo[]=$envTypeInfo;
		}
	}
	
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'userInfo' => $arrEnvTypeInfo,
	);
	return $arrOutput;

}

//获取对应环境信息
public static function getMachineInfo($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!isset($arrInput['envName']) ){
		Util_Log::warning("input params invalid.");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$dlArrInput = array(
			'function' => 'getMachineInfo',
			'envName' => $arrInput['envName'],
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	$envInfo = $dlArrOut['results'][0][0];
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'envInfo' => $envInfo,
	);
	return $arrOutput;
}

//获取对应环境类型信息
public static function getEnvTypeInfoOfenvTypeId($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['envTypeId'])){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$envTypeId=intval($arrInput['envTypeId']);
 
	$arrEnvTypeInfo=array();
	$envTypeInfo=self::_getEnvTypeInfoByTypeID($envTypeId);
	if($envTypeInfo === Oep_Errno::GET_PROLINE_FAIL || $envTypeInfo === Oep_Errno::DB_CONN_ERROR){
		return Oep_Errno::GET_PROLINE_FAIL;
	}

	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'envTypeInfo' => $envTypeInfo,
	);
	return $arrOutput;
}

//修改环境类型配置信息
public static function setEnvTypeInfo($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['singleMaxTime']) || !isset($arrInput['maxUseNum']) || 
			!isset($arrInput['totalNumInstance']) || !isset($arrInput['maxOwnEnv']) || !isset($arrInput['cpu']) ||
			!isset($arrInput['disk']) || !isset($arrInput['mem']) || !isset($arrInput['envTypeId']) ||
			!isset($arrInput['username']) || !isset($arrInput['role']) || !isset($arrInput['pid'])){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if($arrInput['singleMaxTime'] <=0 || $arrInput['maxUseNum'] <= 0 || $arrInput['totalNumInstance']<=0 || $arrInput['maxOwnEnv']<=0 || $arrInput['cpu']<=0 || $arrInput['disk']<=0 || $arrInput['mem'] <=0){
		Util_Log::warning("input params invalid.");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$productId=intval($arrInput['pid']);
	$role=intval($arrInput['role']);
	$envTypeId=intval($arrInput['envTypeId']);
	$singleMaxTime=intval($arrInput['singleMaxTime']);
	$maxUseNum=intval($arrInput['maxUseNum']);
	$maxOwnEnv=intval($arrInput['maxOwnEnv']);
	$cpu=intval($arrInput['cpu']);
	$disk=intval($arrInput['disk']);
	$mem=intval($arrInput['mem']);
	$totalNumInstance=intval($arrInput['totalNumInstance']);

	//角色验证
	$arrDlInput=array(
			'username' => $arrInput['username'],
			'pid' => $productId,
			'role' => $role,
	);
	$checkUser = self::_checkUser($arrDlInput);
	if (!$checkUser || $checkUser['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('no perm');
		return self::_errRet(Oep_Errno::NO_POWER);
	}
	if($role==0){
		Util_Log::warning("current user has no perm");
		return Oep_Errno::NO_POWER;
	}elseif($role==1){
		$arrInput = array(
				'envTypeId' => $envTypeId,
		);
		$envTypeListsOut = Util_Service::innerCall('oep','getBaseEnvConfByEnvTypeId',$arrInput);
		if(!$envTypeListsOut || $envTypeListsOut['errmsg'] != Oep_Errno::SUCCESS){
			Bingo_Log::warning ( "call getBaseEnvConfByPid fail" );
			$arrOutput ['errno'] = Oep_Errno::CALL_SERVICE_ERROR;
			$arrOutput ['errmsg'] = 'call service fail';
			throw new Oep_Error ( $arrOutput ['errno'], $arrOutput ['errmsg'] );
		}
		if($envTypeListsOut['envConfInfo'][0]['pid']!=$productId){
			Util_Log::warning('no perm');
			return self::_errRet(Oep_Errno::NO_POWER);
		}
		$typeInfo = self::_getEnvTypeInfoByTypeID($envTypeId);
		if($typeInfo === Oep_Errno::GET_PROLINE_FAIL || $typeInfo === Oep_Errno::DB_CONN_ERROR){
			return Oep_Errno::GET_PROLINE_FAIL;
		}
		if(count($typeInfo)==0){
			$dlArrInput=array(
					'function' => 'insertEnvTypeInfo',
					'envTypeId' => $envTypeId,
					'singleMaxTime' => $singleMaxTime,
					'maxUseNum' => $maxUseNum,
					'maxOwnEnv' => $maxOwnEnv,
					'cpu' => $cpu,
					'mem' => $mem,
					'disk' => $disk,
					'totalNumInstance'=> $totalNumInstance,
			);
			$dlArrOut = self::_dl_query($dlArrInput);
			if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
				Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
				return self::_errRet(Oep_Errno::DL_CALL_FAIL);
			}
			
		}else{
			$dlArrInput=array(
					'function' => 'setEnvTypeInfo',
					'envTypeId' => $envTypeId,
					'singleMaxTime' => $singleMaxTime,
					'maxUseNum' => $maxUseNum,
					'maxOwnEnv' => $maxOwnEnv,
					'cpu' => $cpu,
					'mem' => $mem,
					'disk' => $disk,
					'totalNumInstance'=> $totalNumInstance,
			);
			$dlArrOut = self::_dl_query($dlArrInput);
			if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
				Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
				return self::_errRet(Oep_Errno::DL_CALL_FAIL);
			}
		}
	}elseif($role==2){
		$typeInfo = self::_getEnvTypeInfoByTypeID($envTypeId);
		if($typeInfo === Oep_Errno::GET_PROLINE_FAIL || $typeInfo === Oep_Errno::DB_CONN_ERROR){
			return Oep_Errno::GET_PROLINE_FAIL;
		}
		if(count($typeInfo)==0){
			$dlArrInput=array(
					'function' => 'insertEnvTypeInfo',
					'envTypeId' => $envTypeId,
					'singleMaxTime' => $singleMaxTime,
					'maxUseNum' => $maxUseNum,
					'maxOwnEnv' => $maxOwnEnv,
					'cpu' => $cpu,
					'mem' => $mem,
					'disk' => $disk,
					'totalNumInstance'=> $totalNumInstance,
			);
			$dlArrOut = self::_dl_query($dlArrInput);
			if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
				Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
				return self::_errRet(Oep_Errno::DL_CALL_FAIL);
			}
				
		}else{
			$dlArrInput=array(
					'function' => 'setEnvTypeInfo',
					'envTypeId' => $envTypeId,
					'singleMaxTime' => $singleMaxTime,
					'maxUseNum' => $maxUseNum,
					'maxOwnEnv' => $maxOwnEnv,
					'singleMaxTime' => $singleMaxTime,
					'cpu' => $cpu,
					'mem' => $mem,
					'disk' => $disk,
					'totalNumInstance'=> $totalNumInstance,
			);
			$dlArrOut = self::_dl_query($dlArrInput);
			if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
				Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
				return self::_errRet(Oep_Errno::DL_CALL_FAIL);
			}
		}
	}

	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;

}
public static function findUser($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['rolename']) || !isset($arrInput['role']) || !isset($arrInput['username']) || !isset($arrInput['pid'])){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	//角色验证
	$productID = intval($arrInput['pid']);
	$dlArrInput=array(
			'function' => 'checkRoleInfo',
			'pid' => $productID,
			'username' => $arrInput['rolename'],
			'role' => $arrInput['role'],
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	//角色不存在或者查找失败
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	if(!count($dlArrOut['results'][0])){
		Util_Log::warning('the user has no perm ');
		return self::_errRet(Oep_Errno::NO_POWER);
	}
	//查找用户角色
	if($arrInput['role']!=2){
		$dlArrInput=array(
				'function' => 'getUserInfoByPid',
				'pid' => $productID,
				'username' => $arrInput['username'],
		);
		$dlArrOut = self::_dl_query($dlArrInput);
	}else{
		$dlArrInput=array(
				'function' => 'getUserInfo',
				'username' => $arrInput['username'],
		);
		$dlArrOut = self::_dl_query($dlArrInput);
	}
	//角色不存在或者查找失败
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	if(count($dlArrOut['results'][0]) === 0){
		Util_Log::warning('no user exit');
		return self::_errRet(Oep_Errno::USER_NOT_EXIT);
	}
	$userTemp=$dlArrOut['results'][0];
	$userInfo=array();
	foreach ($userTemp as $temp){
		if($temp['userPass']==1){
			$userTmp=array();
			$userTmp['pid']=$temp['pid'];
			$userTmp['role']=$temp['role'];
			$userTmp['username']=$arrInput['username'];
			$userTmp['pidName']=self::_getProLineByID($temp['pid']);
			$userInfo[]=$userTmp;
		}
	}
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'userInfo' => $userInfo,
	);
	return $arrOutput;

}

//续借环境
public static function renewEnv($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['username']) || !isset($arrInput['role']) || !isset($arrInput['envName']) || !isset($arrInput['pid']) || !isset($arrInput['endTime'])){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	$productID = intval($arrInput['pid']);
	$endTime = intval($arrInput['endTime'])+86400;
	$role=$arrInput['role'];
	if($endTime <= time()){
		Util_Log::warning("endTime error");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	//角色验证
	$arrDlInput=array(
			'username' => $arrInput['username'],
			'pid' => $productID,
			'role' => $role,
	);
	$checkUser = self::_checkUser($arrDlInput);
	if (!$checkUser || $checkUser['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('no perm');
		return self::_errRet(Oep_Errno::NO_POWER);
	}
	//查找环境是否存在
	$dlArrInput=array(
			'function' => 'getEnvByEnvName',
			'envName' => $arrInput['envName'],
			'endTime' => time(),
	);
	$envInfoOut = self::_dl_query($dlArrInput);
	if (!$envInfoOut || $envInfoOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	$envInfo=$envInfoOut['results'][0][0];
	if(!count($envInfo)){
		Util_Log::warning('env not exit');
		return self::_errRet(Oep_Errno::NO_ENV);
	}
	//根据envTypeId获取envType和pid
	$arrEnvTypeID = array(
			'envTypeId'	=> 	$envInfo['envTypeId'],
	);
	$envTypeConf = Util_Service::innerCall('oep','getBaseEnvConfByEnvTypeId',$arrEnvTypeID);
	if($envTypeConf['errno'] !== Oep_Errno::SUCCESS){
		Util_Log::warning('call service getBaseEnvConfByEnvTypeId fail');
		return self::_errRet(Oep_Errno::CALL_SERVICE_ERROR);
	}
	//权限验证
	if($envInfo['username'] != $arrInput['username']){
		if($role == 0){
			Util_Log::warning('no perm');
			return self::_errRet(Oep_Errno::NO_POWER);
		}elseif($role == 1 && $envTypeConf['envConfInfo'][0]['pid'] != $productID){
			Util_Log::warning('no perm');
			return self::_errRet(Oep_Errno::NO_POWER);
		}
		
	}
	//根据envTypeId获取配置信息
	$envTypeInfo = self::_getEnvTypeInfoByTypeID($envInfo['envTypeId']);
	if($envTypeInfo === Oep_Errno::GET_PROLINE_FAIL || $envTypeInfo === Oep_Errno::DB_CONN_ERROR){
		return Oep_Errno::GET_PROLINE_FAIL;
	}
	if($envTypeInfo['maxUseNum']<=$envInfo['renewNum']){
		Util_Log::warning('renewEnv number is limited');
		return self::_errRet(Oep_Errno::RENEWENV_LIMITED);
	}elseif($endTime > (time()+intval($envTypeInfo['singleMaxTime'])*86400) && $role==0){
		Util_Log::warning('time over the limit');
		return self::_errRet(Oep_Errno::TIME_ERROR);
	}else{
		$envRenewInput = array(
				'function' => 'updateUserEnv',
				'renewNum' => $envInfo['renewNum']+1,
				'endTime' => $endTime,
				'username' => $envInfo['username'],
				'envName' => $arrInput['envName'],
		);
		$updateResult = self::_dl_query($envRenewInput);
		if (!$updateResult || $updateResult['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail. dlArrInput = '.serialize($envRenewInput));
			return self::_errRet(Oep_Errno::DL_CALL_FAIL);
		}
	}
	//通知用户
	$dataSend=date('Y-m-d H:i:s',$endTime);
	$arrSendEmailInput = array(
			'title' => '恭喜，资源续借成功',
			'toAddr' => $arrInput['username'].'@baidu.com',
			'content' => '您资源为'.$arrInput['envName'].'的机器续借成功，到期时间为'.$dataSend,
	);
	self::sendEmail($arrSendEmailInput);
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;

}
//退出产品线
public static function exitPro($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['username']) || !isset($arrInput['applayer']) || !isset($arrInput['pid']) || !isset($arrInput['currentPid']) || !isset($arrInput['role'])){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	$currProductID = intval($arrInput['currentPid']);
	$productID = intval($arrInput['pid']);
	$role = intval($arrInput['role']);
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	if($arrInput['applayer']!=$arrInput['username'] || $arrInput['pid']!=$arrInput['currentPid']){
		//验证是否有权限
		$arrDlInput=array(
				'username' => $arrInput['username'],
				'pid' => $currProductID,
				'role' => $role,
		);
	    $checkUser = self::_checkUser($arrDlInput);
		if (!$checkUser || $checkUser['errno'] != Oep_Errno::SUCCESS || $role==0 || ($currProductID!=$productID && $role!=2)){
			Util_Log::warning('no perm');
			return self::_errRet(Oep_Errno::NO_POWER);
		}	
		$dlArrInput=array(
				'function' => 'delUserInfo',
				'pid' => $productID,
				'username' => $arrInput['applayer'],
		);
		$dlArrOut = self::_dl_query($dlArrInput);
		if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail');
			return self::_errRet(Oep_Errno::DL_CALL_FAIL);
		}
		
	}else {
		$dlArrInput=array(
				'function' => 'delUserInfo',
				'pid' => $productID,
				'username' => $arrInput['username'],				
		);
		$dlArrOut = self::_dl_query($dlArrInput);
		if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail');
			return self::_errRet(Oep_Errno::DL_CALL_FAIL);
		}
	}

	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;

}
//设置/申请产品线角色为管理员
public static function setUserRole($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['username']) || !isset($arrInput['applayer']) || !isset($arrInput['currentPid']) || !isset($arrInput['pid']) || !isset($arrInput['role'])){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	$currProductID = intval($arrInput['currentPid']);
	$productID = intval($arrInput['pid']);
	$role = intval($arrInput['role']);
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	if($arrInput['applayer']!=$arrInput['username'] || $arrInput['pid']!=$arrInput['currentPid']){
		
		//验证是否有权限
		$arrDlInput=array(
				'username' => $arrInput['username'],
				'pid' => $currProductID,
				'role' => $role,
		);
		$checkUser = self::_checkUser($arrDlInput);
		if (!$checkUser || $checkUser['errno'] != Oep_Errno::SUCCESS || $role==0 || ($currProductID!=$productID && $role!=2)){
			Util_Log::warning('no perm');
			return self::_errRet(Oep_Errno::NO_POWER);
		}

		$dlArrInput=array(
				'function' => 'updateUserRole',
				'role' => 1,
				'pid' => $productID,
				'username' => $arrInput['applayer'],
				'userPass' => 1,
		);
		$dlArrOut = self::_dl_query($dlArrInput);
		if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail');
			return self::_errRet(Oep_Errno::DL_CALL_FAIL);
		}
	}else {
		$dlArrInput=array(
				'function' => 'insertProUser',
				'role' => 1,
				'pid' => $currProductID,
				'username' => $arrInput['username'],
				'userPass' => 0,
		);
		$dlArrOut = self::_dl_query($dlArrInput);
		if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail');
			return self::_errRet(Oep_Errno::DL_CALL_FAIL);
		}
		//通知审核
		$proLineName=self::_getProLineByID($productID);
		$toAddr1=self::getManageOfPid(array('pid'=>$productID));
		$toAddr2=self::getManageOfPid(array('pid'=>1));
		$adminUserArr=array();
		foreach ($toAddr1['userInfo'] as $tmp){
			$adminUserArr[]=$tmp['username'];
		}
		foreach ($toAddr2['userInfo'] as $tmp){
			if(!in_array($tmp['username'],$adminUserArr)){
				$adminUserArr[]=$tmp['username'];
			}
		}
		$toAddr='';
		foreach ($adminUserArr as $tmp){
			$toAddr.=$tmp.'@baidu.com,';
		}
		$arrSendEmailInput = array(
				'title' => $arrInput['username'].'已提交角色申请，请您尽快审核',
				'toAddr' => $toAddr,
				'content' => $arrInput['username'].'已提交 '.$proLineName.' 产品线 管理员 角色申请，请您尽快审核.',
		);
		self::sendEmail($arrSendEmailInput);
		//通知用户
		$arrSendEmailInput = array(
				'title' => '您提交的角色申请已在审核中，请耐心等待。',
				'toAddr' => $arrInput['username'].'@baidu.com',
				'content' =>'您已提交 '.$proLineName.' 产品线  产品线管理员角色的申请，请耐心等待审核.',
		);
		self::sendEmail($arrSendEmailInput);
	}
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;

}
//取消产品线管理员
public static function unSetUserRole($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['username']) || !isset($arrInput['applayer']) || !isset($arrInput['pid']) || !isset($arrInput['currentPid']) || !isset($arrInput['role'])){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	$currProductID = intval($arrInput['currentPid']);
	$productID = intval($arrInput['pid']);
	$role = intval($arrInput['role']);
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	if($arrInput['applayer']!=$arrInput['username'] || $arrInput['pid']!=$arrInput['currentPid']){
		
		//验证是否有权限
		$arrDlInput=array(
				'username' => $arrInput['username'],
				'pid' => $currProductID,
				'role' => $role,
		);
		$checkUser = self::_checkUser($arrDlInput);
		if (!$checkUser || $checkUser['errno'] != Oep_Errno::SUCCESS || $role==0 || ($currProductID!=$productID && $role!=2)){
			Util_Log::warning('no perm');
			return self::_errRet(Oep_Errno::NO_POWER);
		}
		$dlArrInput=array(
				'function' => 'updateUserRole',
				'role' => 0,
				'pid' => $productID,
				'username' => $arrInput['applayer'],
				'userPass' => 1,
		);
		$dlArrOut = self::_dl_query($dlArrInput);
		if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail');
			return self::_errRet(Oep_Errno::DL_CALL_FAIL);
		}

	}else {
		$dlArrInput=array(
				'function' => 'updateUserRole',
				'role' => 0,
				'pid' => $currProductID,
				'username' => $arrInput['username'],
				'userPass' => 1,
		);
		$dlArrOut = self::_dl_query($dlArrInput);
		if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail');
			return self::_errRet(Oep_Errno::DL_CALL_FAIL);
		}
	}
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;

}
//申请产品线权限
public static function applyProductRole($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['username']) || !isset($arrInput['role']) || !isset($arrInput['pid']) || ($arrInput['pid']==1 && $arrInput['role']!=2) || ($arrInput['pid']!=1 && $arrInput['role']==2)){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	$productID = intval($arrInput['pid']);
	$role = intval($arrInput['role']);
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	//对应产品线id是否存在
	$dlArrInput=array(
			'function' => 'getProLineByPid',
			'pid' => $productID,
	);
	$proLineOut = self::_dl_query($dlArrInput);
	if (!$proLineOut || $proLineOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail');
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	if(!count($proLineOut['results'][0])){
		Util_Log::warning('pid not exist');
		return self::_errRet(Oep_Errno::NO_PID);
	}
	$dlCheckArrInput=array(
			'function' => 'checkApplyRole',
			'pid' => $productID,
			'username'=>$arrInput['username'],
	);
	$checkOut = self::_dl_query($dlCheckArrInput);
	if (!$checkOut || $checkOut['errno'] != Oep_Errno::SUCCESS || count($checkOut['results'][0])!=0){
		Util_Log::warning('you have applyed');
		return self::_errRet(Oep_Errno::HAVE_APPLYED);
	}
	$checkResult=self::_checkUser($arrInput);
	if ($checkResult['errno'] == Oep_Errno::SUCCESS){
		Util_Log::warning('the role exist');
		return self::_errRet(Oep_Errno::USER_ALREADY_EXIST);
	}
	//高权限不可以申请低权限
	if($role==0){		
		$dlArrInput=array(
				'function' => 'checkRoleInfo',
				'pid' => $arrInput['pid'],
				'username' => $arrInput['username'],
				'role' => 1,
		);
		$dlArrOut = self::_dl_query($dlArrInput);
		//角色不存在或者查找失败
		if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
			return self::_errRet(Oep_Errno::DL_CALL_FAIL);
		}
		if(count($dlArrOut['results'][0]) != 0){
			Util_Log::warning('Cant apply lower role.');
			return self::_errRet(Oep_Errno::CANT_APPLY_LOWER_ROLE);
		}
	}
	$userInfoInput=array(
			'function' => 'insertProUser',
			'username' => $arrInput['username'],
			'role' => $role,
			'pid' => $productID,
			'userPass' => 0,
	);
	$proLineOut = self::_dl_query($userInfoInput);
	if (!$proLineOut || $proLineOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('you have applyed or call dl query fail');
		return self::_errRet(Oep_Errno::HAVE_APPLYED);
	}
	//通知审核
	$appRole='';
	if($role==0){
		$appRole='普通用户';
	}elseif($role==1){
		$appRole='产品线管理员';
	}elseif($role==2){
		$appRole='系统管理员';
	}
	$proLineName=self::_getProLineByID($productID);
	$toAddr1=self::getManageOfPid(array('pid'=>$productID));
	$toAddr2=self::getManageOfPid(array('pid'=>1));
	$adminUserArr=array();
	foreach ($toAddr1['userInfo'] as $tmp){
		$adminUserArr[]=$tmp['username'];
	}
	foreach ($toAddr2['userInfo'] as $tmp){
		if(!in_array($tmp['username'],$adminUserArr)){
			$adminUserArr[]=$tmp['username'];
		}
	}
	$toAddr='';
	foreach ($adminUserArr as $tmp){
		$toAddr.=$tmp.'@baidu.com,';
	}
	$arrSendEmailInput = array(
			'title' => $arrInput['username'].'已提交角色申请，请您尽快审核',
			'toAddr' => $toAddr,
			'content' => $arrInput['username'].'已提交 '.$proLineName.' 产品线 '.$appRole.' 角色申请，请您尽快审核.',
	);
	self::sendEmail($arrSendEmailInput);
	//通知用户
	$arrSendEmailInput = array(
			'title' => '您提交的角色申请已在审核中，请耐心等待。',
			'toAddr' => $arrInput['username'].'@baidu.com',
			'content' =>'您已提交 '.$proLineName.' 产品线 '.$appRole.' 角色申请，请耐心等待审核.',
	);
	self::sendEmail($arrSendEmailInput);
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;

}

//浏览产品线
public static function browsProductline(){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$dlArrInput=array(
			'function' => 'getProLine',
	);
	//获取产品线信息，返回产品线名称，pid以及审核状态
	$proLineOut = self::_dl_query($dlArrInput);
	if (!$proLineOut || $proLineOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail');
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	$productLines = $proLineOut['results'];
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'proLines' => $productLines,
	);
	return $arrOutput;

}
//添加产品线
/*
public static function addProductline($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['username']) || !isset($arrInput['pid']) || !isset($arrInput['role']) || !isset($arrInput['productLine'])){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	//当前用户角色验证
	$productID = intval($arrInput['pid']);
	$dlArrInput=array(
			'function' => 'checkRoleInfo',
			'pid' => $productID,
			'username' => $arrInput['username'],
			'role' => $arrInput['role'],
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	if(!count($dlArrOut['results'][0])){
		Util_Log::warning('the curent user is not exit ');
		return self::_errRet(Oep_Errno::USER_NOT_EXIT);
	}
	//系统管理员
	if($arrInput['role'] === 2){
		$proLineInput=array(
				'function' => 'insertProLine',
				'pidName' => $arrInput['productLine'],
		);
		$proLineArrOut = self::_dl_query($proLineInput);
		if (!$proLineArrOut || $proLineArrOut['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail');
			return self::_errRet(Oep_Errno::DL_CALL_FAIL);
		}
	}else{ //非管理员，申请待审核，通知审核
		$proLineInput=array(
				'function' => 'insertProLine',
				'productLine' => $arrInput['productLine'],
		);
		$proLineArrOut = self::_dl_query($proLineInput);
		if (!$proLineArrOut || $proLineArrOut['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail');
			return self::_errRet(Oep_Errno::DL_CALL_FAIL);
		}
		//设置申请者即为该产品线的产品线管理员
		$appProID = self::_getPidByProLine($arrInput['productLine']);
		$appProRoleRes = Service_Envmanage_Envmanage::applyProductRole(array('username'=>$arrInput['username'],'pid'=>$appProID,'role'=>1));
		if($appProRoleRes['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('set role fail');
			return self::_errRet(Oep_Errno::SET_USER_ROLE_FAIL);
		}
		//通知管理员审核产品线以及用户角色？？？？？？？？？？？？？
	}
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;

}
*/
//查找用户加入的产品线
/*
public static function getProLineOfUser($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	//参数校验
	if(!isset($arrInput['username'])  ){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	$dlArrInput=array(
			'function' => 'getProOfUser',
			'username' => $arrInput['username'],
	);
	$dlArrOut = self::_dl_query($dlArrInput);
	if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
		Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	$proUsers = $dlArrOut['results'][0];
	$proLineOfUser = array();
	foreach ($proUsers as $proUser){
		$proLine = self::_getProLineByID($proUser['pid']);
		if($proLine === Oep_Errno::GET_PROLINE_FAIL || $proLine === Oep_Errno::DB_CONN_ERROR){
			return Oep_Errno::GET_PROLINE_FAIL;
		}
		$proLineOfUser[]=array(
			'proLine' => $proLine,
				'role'=> $proUser['role'],	
		);
	}
	//返回值
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
			'proLineOfUser' => $proLineOfUser,
	);
	return $arrOutput;

}
*/
/*
 * function:普通用户申请产品线所拥有的环境
 * 
 * param:
 * 		 pid int
 * 		 username string
 * 		 envTypeId int
 * 		 endTime int
 * 		 remark String
 * 
 * return:
 * 		$arrOutput = array(
		'errno' => $error,
		'output' => array(
				'envName' => $envName,
				'envTypeId' => $envTypeId,
    			'username' => $username,
    			'endTime' => $endTime,
    			'remark' => $remark,
    			'envStatus' => 1,
    			'renewNum' => 1,
    			'recoverResult' => 0,
    			'portInfo' => '',
    	),
	);
 * 
 */
public static function applyEnv($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['envTypeId'])|| !isset($arrInput['pid']) || !isset($arrInput['username'])|| !isset($arrInput['endTime'])){
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	
	$pid = intval($arrInput['pid']);
	$envTypeId = intval($arrInput['envTypeId']);  //环境类型id
	$username = $arrInput['username']; //用户名
	
	//目前orp还不支持密码登录，orp支持后，再提供密码
	//$name = $arrInput['name']; //环境帐号
	//$passwd = $arrInput['passwd']; //帐号对应密码
	
	$endTime = intval($arrInput['endTime'])+86400; //环境到期时间
	if($endTime<=time()){
		Util_Log::warning("endTime error");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	$remark = isset($arrInput['remark'])?$arrInput['remark']:''; //用户备注
	
	if(!self::_init(self::SERVICE_NAME)){
		return self::_errRet(Oep_Errno::CONF_ERROR);
	}
	
	//根据pid和username获取用户是否通过审核,角色是否正确
	$userInfo = Service_Envmanage_lib_BaseEnv::getUserInfoByPidAndUsername($pid, $username);
	$userPass = intval($userInfo['info']['userPass']); //角色申请是否通过 0未通过 1通过
	if(0==$userPass){
		Bingo_Log::warning("the userRole has not been approvaled");
		return self::_errRet(Oep_Errno::HAS_NOT_BEEN_APPROVALED);
	}
	
	//获取环境类型信息
	$res= Service_Envmanage_lib_BaseEnv::findEnvTypeInfoByTypeID($envTypeId);
	if(false == $res['succ']){
		Bingo_Log::warning("failed to get envType_info");
		return self::_errRet(Oep_Errno::NO_ENVTYPE);
	}
	if($endTime > (time()+intval($res['info'][0]['singleMaxTime'])*86400)){
		Util_Log::warning('time over the limit');
		return self::_errRet(Oep_Errno::TIME_ERROR);
	}
	//环境类型为$envTypeId的每人同时拥有的最大环境数maxOwnEnv值
	$maxOwnEnv = intval($res['info'][0]['maxOwnEnv']);
	//获取该用户已拥有某一类型环境的个数
	$res = Service_Envmanage_lib_BaseEnv::getUserOwnEnvnum($envTypeId, $username);
	if(false == $res['succ']){
		Bingo_Log::warning("failed to get ownEnvNum");
	}
	$ownEnvNum = intval($res['info'][0]['COUNT(*)']);
	//用户拥有该类型的环境数大于等于设定值maxOwnEnv时，提示用户申请机器已经达到上限，申请失败，并邮件通知
	if($ownEnvNum >= $maxOwnEnv){
		//邮件通知
	
		Bingo_Log::warning("User application environment have reached a limit");
		return self::_errRet(Oep_Errno::ENV_NUM_LIMIT);
	}
	
	//判断是否有指定类型的空闲环境, $envStatus=0（未被申请）, $recoverResult=0（绑定成功）
	$res = Service_Envmanage_lib_BaseEnv::getFreeEnvInfo($envTypeId, 0, 0);
	
	if(false == $res['succ']){
		Bingo_Log::warning("failed to get freeEnv");
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	if (empty($res['info'])){
		//邮件通知
		
		Bingo_Log::warning("There is no free environment to be applied");
		return self::_errRet(Oep_Errno::FREE_ENV_EMPTY);
	}else {
		//取出特定类型环境的第一个空闲的机器
		$freeEnvtobeApply=$res['info'];
	}
	
	//环境名称
	$envName = $freeEnvtobeApply['envName'];
	//orp提供机器端口信息
	//$portInfo= $freeEnvtobeApply['portInfo'];
	
	Bingo_Log::Notice("the applied env is :".var_export($freeEnvtobeApply,1));
	//根据envTypeId,修改产品线-环境表pid_info中第一个空闲机器信息，分配给用户。$envStatus=1表示机器已申请，$renewNum=1表示机器续借次数为1次
	$res = Service_Envmanage_lib_BaseEnv::updateProEnvByEnvName($envName, $username, $endTime, $remark, 1, 1);
	if(false == $res['succ']){
		Bingo_Log::warning("failed to update Table pid_info");
		return self::_errRet(Oep_Errno::DL_CALL_FAIL);
	}
	//通知用户
	$arrSendEmailInput = array(
			'title' => '恭喜，资源申请成功',
			'toAddr' => $arrInput['username'].'@baidu.com',
			'content' => '您申请的资源为'.$envName.',机器详情请至oep环境管理模块查看。',
	);
	self::sendEmail($arrSendEmailInput);
	
	//弹层提示 & 邮件通知用户申请环境成功
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => "apply env success",
		'output' => array(
				'envName' => $envName,
				'envTypeId' => $envTypeId,
    			'username' => $username,
    			'endTime' => $endTime,
    			'remark' => $remark,
    			'envStatus' => 1,
    			'renewNum' => 1,
    	),
	);
	return $arrOutput;
}

/*
 * function:管理员可以指定申请人。
*
* param:
* 		 pid int
* 		 username string
* 		 opName String
* 		 role int
* 		 envName string
* 		 endTime int
* 		 remark String
*
* return:
* 		$arrOutput = array(
		'errno' => $error,
		'output' => array(
				'envName' => $envName,
				'envTypeId' => $envTypeId,
				'username' => $username,
				'endTime' => $endTime,
				'remark' => $remark,
				'envStatus' => 1,
				'renewNum' => 1,
				'recoverResult' => 0,
				'portInfo' => '',
		),
);
*
*/
public static function setApplyUser($arrInput){
	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['pid'])|| !isset($arrInput['username'])|| !isset($arrInput['opName']) || !isset($arrInput['role']) || !isset($arrInput['envName'])|| !isset($arrInput['endTime'])){
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	

	$pid = intval($arrInput['pid']);   //产品线id
	$username = $arrInput['username']; //用户名
	$opName = $arrInput['opName']; //操作者
	$role = intval($arrInput['role']); //用户角色
	$envName = $arrInput['envName']; //环境名称
	$endTime = intval($arrInput['endTime'])+86400; //环境到期时间
	$remark = isset($arrInput['remark'])?$arrInput['remark']:''; //用户备注

	if(!self::_init(self::SERVICE_NAME)){
		return self::_errRet(Oep_Errno::CONF_ERROR);
	}
         
	//根据pid和opName获取操作用户是否通过审核,角色是否正确
	$userInfo = Service_Envmanage_lib_BaseEnv::getUserInfoByPidAndUsername($pid, $opName);
//	Bingo_Log::warning("==========".var_export($userInfo,1));
	$userPass = intval($userInfo['info']['userPass']); //角色申请是否通过 0未通过 1通过
	$role1 = intval($userInfo['info']['role']); //用户角色信息，，0普通用户 1产品线管理员 2系统管理员 3其他
	if(0==$userPass){
		Bingo_Log::warning("the opName has not been approvaled");
		return self::_errRet(Oep_Errno::HAS_NOT_BEEN_APPROVALED);
	}
	if($role!=$role1||$role!=1&&$role!=2){
		Bingo_Log::warning("the opName's Role is wrong");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	
	//根据pid和username获取用户是否通过审核,角色是否正确
	$userInfo = Service_Envmanage_lib_BaseEnv::getUserInfoByPidAndUsername($pid, $username);
	$userPass = intval($userInfo['info']['userPass']); //角色申请是否通过 0未通过 1通过
	$role1 = intval($userInfo['info']['role']); //用户角色信息，，0普通用户 1产品线管理员 2系统管理员 3其他
	if(0==$userPass){
		Bingo_Log::warning("the username has not been approvaled");
		return self::_errRet(Oep_Errno::HAS_NOT_BEEN_APPROVALED);
	}
	
	
	//根据envName获取环境信息
	$envInfo = Service_Envmanage_lib_BaseEnv::getEnvInfoByEnvName($envName);
	$envTypeId = intval($envInfo['info']['envTypeId']); //环境类型
	$envStatus = intval($envInfo['info']['envStatus']); //环境状态，0未申请，1已申请，2已锁定
	/*if(1==$envStatus){
		Bingo_Log::warning("the env has been applied, cannot be applied");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}*/
	/*if(2==$envStatus){
		Bingo_Log::warning("the env has been locked, cannot be applied");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}*/
	
	//获取环境类型信息
	$res= Service_Envmanage_lib_BaseEnv::findEnvTypeInfoByTypeID($envTypeId);
	if(false == $res['succ']){
		Bingo_Log::warning("failed to get envType_info");
		return self::_errRet(Oep_Errno::NO_ENVTYPE);
	}
	//环境类型为$envTypeId的每人同时拥有的最大环境数maxOwnEnv值
	$maxOwnEnv = intval($res['info'][0]['maxOwnEnv']);
	//获取该用户已拥有某一类型环境的个数
	$res = Service_Envmanage_lib_BaseEnv::getUserOwnEnvnum($envTypeId, $username);
	if(false == $res['succ']){
		Bingo_Log::warning("failed to get ownEnvNum");
	}
	$ownEnvNum = intval($res['info'][0]['COUNT(*)']);
	//用户拥有该类型的环境数大于等于设定值maxOwnEnv时，提示用户申请机器已经达到上限，申请失败，并邮件通知
	/*if($ownEnvNum >= $maxOwnEnv){
		Bingo_Log::warning("User application environment have reached a limit");
		return self::_errRet(Oep_Errno::ENV_NUM_LIMIT);
	}*/

	
	//根据envTypeId,修改产品线-环境表pid_info中第一个空闲机器信息，分配给用户。$envStatus=1表示机器已申请，$renewNum=1表示机器续借次数为1次
	$res = Service_Envmanage_lib_BaseEnv::updateProEnvByEnvName($envName, $username, $endTime, $remark, 1, 1);
	if(false == $res['succ']){
		Bingo_Log::warning("failed to update Table pid_info");
		return self::_errRet(Oep_Errno::CONF_ERROR);
	}


	//弹层提示 & 邮件通知用户申请环境成功
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => "apply env success",
			'output' => array(
					'envName' => $envName,
					'envTypeId' => $envTypeId,
					'username' => $username,
					'endTime' => $endTime,
					'remark' => $remark,
					'envStatus' => 1,
					'renewNum' => 1,
			),
	);
	return $arrOutput;
}

/*
 * 说明：释放用户当前占用的环境,释放后即根据环境类型对环境进行更新。普通用户只能释放自己的环境，产品线管理员可以释放产品线的环境，系统管理员可以释放所有的环境。
 * param:
	  	pid int
		username string
		role int
		envName string
 * 
 */
public static function freeEnv($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['pid']) || !isset($arrInput['username']) || !isset($arrInput['envName'])|| !isset($arrInput['role'])){
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Oep_Errno::PARAM_ERR);
	}

	//input params.
	$pid = intval($arrInput['pid']); //产品线id
	$username = $arrInput['username']; //用户名
	$envName = $arrInput['envName'];  //环境名称
	$role = intval($arrInput['role']); //用户角色信息，，0普通用户 1产品线管理员 2系统管理员 3其他

	if(!self::_init(self::SERVICE_NAME)){
		return self::_errRet(Oep_Errno::CONF_ERROR);
	}
	
	//根据pid和username获取用户是否通过审核
	$userInfo = Service_Envmanage_lib_BaseEnv::getUserInfoByPidAndUsername($pid, $username);
	$userPass = intval($userInfo['info']['userPass']); //角色申请是否通过 0未通过 1通过
	$role1 = intval($userInfo['info']['role']); //用户角色信息，，0普通用户 1产品线管理员 2系统管理员 3其他
	if(0==$userPass){
		Bingo_Log::warning("the userRole has not been approvaled");
		return self::_errRet(Oep_Errno::HAS_NOT_BEEN_APPROVALED);
	}
	if($role!=$role1){
		Bingo_Log::warning("the userRole is wrong");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	
	if(1==$role1||2==$role1){//系统管理员或者产品线管理员释放资源
		//根据envName获取环境信息
		$envInfo = Service_Envmanage_lib_BaseEnv::getEnvInfoByEnvName($envName);
		$envStatus = intval($envInfo['info']['envStatus']); //环境状态，0未申请，1已申请，2已锁定
		$envUsername =  $envInfo['info']['username']; //指定envName记录的username
		
		if(0==$envStatus){
			Bingo_Log::warning("the env is not be applied, cannot be released");
			return self::_errRet(Oep_Errno::PARAM_ERR);
		}
		
		if(2==$envStatus){
			Bingo_Log::warning("the env is locked");
			return self::_errRet(Oep_Errno::PARAM_ERR);
		}
		
		
		//
		//调用orp接口，缩容
		$envArr=array('instance_name'=>$envName);
		$freeResultOut = Util_Service::innerCall('oep','freeinstance',$envArr);
		$freeResult = json_decode($freeResultOut, true);
		if($freeResult['result'] == true){
			$envInputArr=array('envName'=>$envName,'envStatus'=>4);
			$envUpInfoOut=Util_Service::innerCall('oep','upEnvInfoOfenvStatus',$envInputArr);
			//重置申请人以及到期时间
			$dlArrInput = array(
					'function' => 'reSetEnvNameAndEndtime',
					'envName' => $envName,
					'username' => '',
					'endTime' =>0,
			);
			$dlArrOut = self::_dl_query($dlArrInput);
			if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
				Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
				return Oep_Errno::GET_PROLINE_FAIL;
			}
		/*	sleep(120);
			$envInputArr=array('envName'=>$envName);
			$groupInfo=Util_Service::innerCall('oep','getEnvInfoByEnvName',$envInputArr);
			$groupArr=array('group_id'=>$groupInfo['envInfo']['groupId']);
			$delGroupOut = Util_Service::innerCall('oep','deletegroup',$groupArr);
			$freeEnvInfo = false;
			$freeEnvInfo = Service_Envmanage_lib_BaseEnv::freeEnv($envUsername, $envName, 0);
			if(0 == $freeEnvInfo ['succ']){
				Bingo_Log::warning("failed to free the env ");
				return self::_errRet(Oep_Errno::FREE_ENV_ERROR);
			}
			*/
			//邮件通知用户环境释放成功
			Bingo_Log::notice("success to free the env");
		}
		//将产品线-环境表env_info中的recoverResult置为success
		/*$recoverResultInfo = Service_Envmanage_lib_BaseEnv::resetRecoverResult($envName, 0);
		
		if(false == $recoverResultInfo ['succ']){
			Bingo_Log::warning("failed to bind the env ");
			return self::_errRet(Oep_Errno::BIND_ENV_ERROR);
		}*/
		//邮件通知用户释放的环境绑定成功
		
		
	}elseif (0==$role1){//普通用户释放自己的资源
		//根据envName获取环境信息
		$envInfo = Service_Envmanage_lib_BaseEnv::getEnvInfoByEnvName($envName);
		$envStatus = intval($envInfo['info']['envStatus']); //环境状态，0未申请，1已申请，2已锁定
		$envUsername =  $envInfo['info']['username']; //指定envName记录的username
		if(0==$envStatus){
			Bingo_Log::warning("the env is not be applied, cannot be released");
			return self::_errRet(Oep_Errno::PARAM_ERR);
		}
		
		if(2==$envStatus){
			Bingo_Log::warning("the env is locked");
			return self::_errRet(Oep_Errno::PARAM_ERR);
		}
		if($envUsername!=$username){
			Bingo_Log::warning("the username is wrong");
			return self::_errRet(Oep_Errno::PARAM_ERR);
		}
		
		//
	//调用orp接口，缩容
		$envArr=array('instance_name'=>$envName);
		$freeResultOut = Util_Service::innerCall('oep','freeinstance',$envArr);
		$freeResult = json_decode($freeResultOut, true);
		if($freeResult['result'] == true){
			$envInputArr=array('envName'=>$envName,'envStatus'=>4);
			$envUpInfoOut=Util_Service::innerCall('oep','upEnvInfoOfenvStatus',$envInputArr);
			//重置申请人以及到期时间
			$dlArrInput = array(
					'function' => 'reSetEnvNameAndEndtime',
					'envName' => $envName,
					'username' => '',
					'endTime' =>0,
			);
			$dlArrOut = self::_dl_query($dlArrInput);
			if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
				Util_Log::warning('call dl query fail. dlArrInput = '.serialize($dlArrInput));
				return Oep_Errno::GET_PROLINE_FAIL;
			}
			/*sleep(120);
			$envInputArr=array('envName'=>$envName);
			$groupInfo=Util_Service::innerCall('oep','getEnvInfoByEnvName',$envInputArr);
			$groupArr=array('group_id'=>$groupInfo['envInfo']['groupId']);
			$delGroupOut = Util_Service::innerCall('oep','deletegroup',$groupArr);
			$freeEnvInfo = false;
			$freeEnvInfo = Service_Envmanage_lib_BaseEnv::freeEnv($envUsername, $envName, 0);
			if(0 == $freeEnvInfo ['succ']){
				Bingo_Log::warning("failed to free the env ");
				return self::_errRet(Oep_Errno::FREE_ENV_ERROR);
			}
			*/
			//邮件通知用户环境释放成功
			Bingo_Log::notice("success to free the env");
		}
		
		//将产品线-环境表env_info中的recoverResult置为success
	/*	$recoverResultInfo = Service_Envmanage_lib_BaseEnv::resetRecoverResult($envName, 0);
		
		if(false == $recoverResultInfo ['succ']){
			Bingo_Log::warning("failed to bind the env ");
			return self::_errRet(Oep_Errno::BIND_ENV_ERROR);
		}*/
		//邮件通知用户释放的环境绑定成功
		
	}
	
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),//错误信息
	);
	return $arrOutput;
}

//定时删除数据库中已释放环境
public static function delFreeEnv($arrInput){
	if(!self::_init(self::SERVICE_NAME)){
		return Oep_Errno::CONF_ERROR;
	}
	if(!self::_dbInit()){
		Util_Log::warning("DB get fail");
		return Oep_Errno::DB_CONN_ERROR;
	}
	if(!isset($arrInput['username']) || !isset($arrInput['envName'])){
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	$freeEnvInfo = false;
	$freeEnvInfo = Service_Envmanage_lib_BaseEnv::freeEnv($arrInput['username'], $arrInput['envName'], 4);
	if(0 == $freeEnvInfo ['succ']){
		Bingo_Log::warning("failed to free the env ");
		return self::_errRet(Oep_Errno::FREE_ENV_ERROR);
	}
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Oep_Errno::getErrnoMsg($error),
	);
	return $arrOutput;
}

/*
 * 审批角色申请
 * 请求参数：flag为1 通过，为0拒绝
 */
public static function approvalRoleApply($arrInput){
	// input params check;
	if(!isset($arrInput['username']) || !isset($arrInput['flag']) || !isset($arrInput['pid']) || !isset($arrInput['applayer']) || !isset($arrInput['currentPid']) || !isset($arrInput['role'])){
		Util_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	
	//input params.
	$currentPid = intval($arrInput['currentPid']); //产品线id
	$username = $arrInput['username']; //UUAP用户名，用户名是邮箱前缀
	$pid = intval($arrInput['pid']); 
	$role = intval($arrInput['role']);
	$flag = intval($arrInput['flag']);
	if(!self::_init(self::SERVICE_NAME)){
		return self::_errRet(Oep_Errno::CONF_ERROR);
	}
	//角色验证
	$arrDlInput=array(
			'username' => $arrInput['username'],
			'pid' => $currentPid,
			'role' => $role,
	);
	$checkUser = self::_checkUser($arrDlInput);
	if (!$checkUser || $checkUser['errno'] != Oep_Errno::SUCCESS || $role==0 || ($currentPid!=$pid && $role!=2)){
		Util_Log::warning('no perm');
		return self::_errRet(Oep_Errno::NO_POWER);
	}
	if($flag==0){
		$dlArrInput=array(
				'function' => 'delUnOrPassUser',
				'pid' => $pid,
				'username' => $arrInput['applayer'],
				'userPass' => 0,
		);
		$dlArrOut = self::_dl_query($dlArrInput);
		if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail');
			return self::_errRet(Oep_Errno::DL_CALL_FAIL);
		}
		//通知审核
		$proLineName=self::_getProLineByID($pid);
		//通知用户
		$arrSendEmailInput = array(
				'title' => '您提交的角色申请未通过。',
				'toAddr' => $arrInput['applayer'].'@baidu.com',
				'content' =>'您提交的 '.$proLineName.' 产品线 的角色申请未通过，请联系产品线管理员.',
		);
		self::sendEmail($arrSendEmailInput);
	}elseif ($flag==1){
		$dlArrInput=array(
				'function' => 'delUnOrPassUser',
				'pid' => $pid,
				'username' => $arrInput['applayer'],
				'userPass' => 1,
		);
		$dlArrOut = self::_dl_query($dlArrInput);
		if (!$dlArrOut || $dlArrOut['errno'] != Oep_Errno::SUCCESS ){
			Util_Log::warning('call dl query fail');
			return self::_errRet(Oep_Errno::DL_CALL_FAIL);
		}
		//根据pid和username修改产品线-用户表中的userPass字段
		$approvalRoleInfo = Service_Envmanage_lib_BaseEnv::approvalRole($pid, $arrInput['applayer'], $flag);
		if('true'!= $approvalRoleInfo ['succ']){
			Bingo_Log::warning("failed to approval role");
			return self::_errRet(Oep_Errno::APPROVAL_ROLE_ERROR);
		}
		$proLineName=self::_getProLineByID($pid);
		//通知用户
		$arrSendEmailInput = array(
				'title' => '您提交的角色申请已通过。',
				'toAddr' => $arrInput['applayer'].'@baidu.com',
				'content' =>'您提交的 '.$proLineName.' 产品线 的角色申请已通过审核.',
		);
		self::sendEmail($arrSendEmailInput);
	}

	Bingo_Log::notice("success to approval role!");
	//邮件通知角色申请用户，审批结果
	
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),//错误信息
	);
	return $arrOutput;

}

/*
 * 说明：对新申请的产品线申请进行审核。只有系统管理员才拥有此操作。
 * 请求参数：productLine、pid、username
 * 
 */
public static function approvalProductlineApply($arrInput){
	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['productLine'])||!isset($arrInput['pid'])||!isset($arrInput['username'])){
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	
	//input params.
	$pid = intval($arrInput['pid']); //产品线id
	$username = $arrInput['username']; //申请产品线的用户名
	$productLine = $arrInput['productLine']; //产品线名称
	//根据pid和username获取该产品线是否已通过审核
	$proInfo = Service_Envmanage_lib_BaseEnv::getProInfoByPid($pid);	
	$proPass = intval($proInfo['info']['proPass']); //产品线申请是否通过，1为通过，0为未通过

  
	if(1==$proPass){
		Bingo_Log::warning("the proPass has been approvaled");
		return self::_errRet(Oep_Errno::HAS_BEEN_APPROVALED);
	}

	if(!self::_init(self::SERVICE_NAME)){
		return self::_errRet(Oep_Errno::CONF_ERROR);
	}

	//根据pid、productLine修改产品线表pid_info中的proPass字段为1
	$approvalProductlineInfo = Service_Envmanage_lib_BaseEnv::approvalProductline($productLine, $pid, 1);

	if(false == $approvalProductlineInfo ['succ']){
		Bingo_Log::warning("failed to approval Productline");
		return self::_errRet(Oep_Errno::APPROVAL_PRODUCTLINE_ERROR);
	}
	
	//在产品线-用户user_info表中，新增一个记录，产品线申请者为产品线的管理员
	$insertProUserInfo = Service_Envmanage_lib_BaseEnv::insertProUser($username, $pid, 1, 1);
	if('true' != $insertProUserInfo ['succ']){
		Bingo_Log::warning("failed to insert ProUser");
		return self::_errRet(Oep_Errno::INSERT_USER_ERROR);
	}
	
	//邮件通知角色申请用户，审批结果

	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),//错误信息
	);
	return $arrOutput;
}


/*
 * 说明：各个产品线到期的环境自动释放，通过jenkins跑定时脚本，将过期的、已被申请的环境释放掉，并更新环境。
 * 
 */
public static function autoRelease(){

	
//	$nowtime = date('Y-m-d');//当前时间
	$nowtime =time();
	$envStatus = 1;
	Bingo_Log::warning("===========");

	if(!self::_init(self::SERVICE_NAME)){
		return self::_errRet(Oep_Errno::CONF_ERROR);
	}

	$expireEnvArrayInfo = Service_Envmanage_lib_BaseEnv::getExpireEnv($nowtime, $envStatus);
	
	if(false == $expireEnvArrayInfo ['succ']){
		Bingo_Log::warning("failed to get expireEnv");
		return self::_errRet(Oep_Errno::INSERT_USER_ERROR);
	}
	
	$length = count($expireEnvArrayInfo['info']);
	for ($i=0; $i<$length; $i++){
		//$pid = $expireEnvArrayInfo['info'][$i]['pid'];
		$username = $expireEnvArrayInfo['info'][$i]['username'];
		//$envID = $expireEnvArrayInfo['info'][$i]['envID'];
		$envStatus = $expireEnvArrayInfo['info'][$i]['envStatus'];
		$envName= $expireEnvArrayInfo['info'][$i]['envName'];
		$arrInput = array(
    			'pid' => 1,
    			'username' => 'yanyuee',
    			'envName' => $envName,
				'role'=>2,
    	);
		self::freeEnv($arrInput);	
	}
	
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),//错误信息
	);
	return $arrOutput;

}


/*
 * 说明：即将到期环境自动提醒，包括邮件、短信（临近一天到期可以考虑短信）
*
*/
public static function autoRemind(){

	$nowtime = date('Y-m-d');//当前时间

	if(!self::_init(self::SERVICE_NAME)){
		return self::_errRet(Oep_Errno::CONF_ERROR);
	}

	$expireEnvArrayInfo = Service_Envmanage_lib_BaseEnv::getExpireEnv();

	if(false == $expireEnvArrayInfo ['succ']){
		Bingo_Log::warning("failed to get expireEnv");
	}

	$length = count($expireEnvArrayInfo[info]);
	for ($i=0; i<$length; $i++){
		$pid = $expireEnvArrayInfo['info'][$i]['pid'];
		$username = $expireEnvArrayInfo['info'][$i]['username'];
		$envID = $expireEnvArrayInfo['info'][$i]['envID'];
		$envStatus = $expireEnvArrayInfo['info'][$i]['envStatus'];
		$arrInput = array(
				'pid' => $pid,
				'username' => $username,
				'envID' => $envID,
				'envStatus' => 1,
		);
		self::freeEnv($arrInput);
	}

	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),//错误信息
	);
	return $arrOutput;

}


/*
 * 说明：锁定环境，只有产品线管理员和系统管理员才有此权限
 * 请求参数：pid、username、role、envName
 *	
 */
public static function lockEnv($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['pid'])|| !isset($arrInput['username']) || !isset($arrInput['role']) || !isset($arrInput['envName'])){
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	
	$pid = intval($arrInput['pid']);   //产品线id
	$username = $arrInput['username']; //用户名
	$role = intval($arrInput['role']); //用户角色
	$envName = $arrInput['envName']; //环境名称
	
	if(0==$role){
		Bingo_Log::warning("this user is a common user, has no right to lock the env");
		return self::_errRet(Oep_Errno::LOCK_ENV_ERROR);
	}
	
	//根据pid和username获取用户是否通过审核
	$userInfo = Service_Envmanage_lib_BaseEnv::getUserInfoByPidAndUsername($pid, $username);
	$userPass = intval($userInfo['info']['userPass']); //角色申请是否通过 0未通过 1通过
	$role1 = intval($userInfo['info']['role']); //用户角色信息，，0普通用户 1产品线管理员 2系统管理员 3其他
	if(0==$userPass){
		Bingo_Log::warning("the userRole has not been approvaled");
		return self::_errRet(Oep_Errno::HAS_NOT_BEEN_APPROVALED);
	}
	if(0==$role1){
		Bingo_Log::warning("this user is a common user, has no right to lock the env");
		return self::_errRet(Oep_Errno::LOCK_ENV_ERROR);
	}
	if($role!=$role1){
		Bingo_Log::warning("input role invalid.");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	
	if(!self::_init(self::SERVICE_NAME)){
		return self::_errRet(Oep_Errno::CONF_ERROR);
	}
	
	//判断环境当前状态：1）如果是未申请，则可以锁定；2）如果是已申请，提示“环境已被申请，不能被锁定”；3）如果是已锁定，提示“环境已被锁定，无需再锁定”
	//根据envName获取环境信息
	$envInfo = Service_Envmanage_lib_BaseEnv::getEnvInfoByEnvName($envName);
	$envStatus = intval($envInfo['info']['envStatus']); //环境状态，0未申请，1已申请，2已锁定
	if(1==$envStatus){
		Bingo_Log::warning("the env has been applied, cannot be locked");
		return self::_errRet(Oep_Errno::ENV_CANT_LOCK);
	}
	
	if(2==$envStatus){
		Bingo_Log::warning("the env has been locked, cannot be locked again");
		return self::_errRet(Oep_Errno::ENV_CANT_LOCK);
	}
	
	
	$lockEnvInfo = false;
	
	$lockEnvInfo = Service_Envmanage_lib_BaseEnv::lockEnv($envName, 2);

	if(0 == $lockEnvInfo ['succ']){
		Bingo_Log::warning("failed to lock the env ");
		return self::_errRet(Oep_Errno::LOCK_ENV_ERROR);
	}
	Bingo_Log::notice("success to lock the env");	
	
	
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),//错误信息
	);
	return $arrOutput;

}


/*
 * 说明：解锁环境，只有产品线管理员和系统管理员才有此权限
* 请求参数：pid、username、role、envName
*
*/
public static function unlockEnv($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['pid'])|| !isset($arrInput['username']) || !isset($arrInput['role']) || !isset($arrInput['envName'])){
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}

	$pid = intval($arrInput['pid']);   //产品线id
	$username = $arrInput['username']; //用户名
	$role = intval($arrInput['role']); //用户角色
	$envName = $arrInput['envName']; //环境名称

	if(0==$role){
		Bingo_Log::warning("this user is a common user, has no right to unlock the env");
		return self::_errRet(Oep_Errno::UNLOCK_ENV_ERROR);
	}

	//根据pid和username获取用户是否通过审核
	$userInfo = Service_Envmanage_lib_BaseEnv::getUserInfoByPidAndUsername($pid, $username);
	$userPass = intval($userInfo['info']['userPass']); //角色申请是否通过 0未通过 1通过
	$role1 = intval($userInfo['info']['role']); //用户角色信息，，0普通用户 1产品线管理员 2系统管理员 3其他
	if(0==$userPass){
		Bingo_Log::warning("the userRole has not been approvaled");
		return self::_errRet(Oep_Errno::HAS_NOT_BEEN_APPROVALED);
	}
	if(0==$role1){
		Bingo_Log::warning("this user is a common user, has no right to unlock the env");
		return self::_errRet(Oep_Errno::LOCK_ENV_ERROR);
	}

	if($role!=$role1){
		Bingo_Log::warning("input role invalid.");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}

	if(!self::_init(self::SERVICE_NAME)){
		return self::_errRet(Oep_Errno::CONF_ERROR);
	}

	//判断环境当前状态：1）如果是未申请，则可以锁定；2）如果是已申请，提示“环境已被申请，不能被锁定”；3）如果是已锁定，提示“环境已被锁定，无需再锁定”
	//根据envName获取环境信息
	$envInfo = Service_Envmanage_lib_BaseEnv::getEnvInfoByEnvName($envName);
	$envStatus = intval($envInfo['info']['envStatus']); //环境状态，0未申请，1已申请，2已锁定
	if(1==$envStatus){
		Bingo_Log::warning("the env has been applied, cannot be unlocked");
		return self::_errRet(Oep_Errno::ENV_CANT_UNLOCK);
	}

	if(0==$envStatus){
		Bingo_Log::warning("the env has not been applied, cannot be unlocked");
		return self::_errRet(Oep_Errno::ENV_CANT_UNLOCK);
	}


	$unlockEnvInfo = false;

	$unlockEnvInfo = Service_Envmanage_lib_BaseEnv::unlockEnv($envName, 0);

	if(0 == $unlockEnvInfo ['succ']){
		Bingo_Log::warning("failed to unlock the env ");
		return self::_errRet(Oep_Errno::UNLOCK_ENV_ERROR);
	}
	Bingo_Log::notice("success to unlock the env");


	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),//错误信息
	);
	return $arrOutput;

}

/*
 * 说明：变更备注
* param:
* 	pid int
	username string
	role int
	envName string
	remark string
*
*/
public static function updataRemark($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['pid'])|| !isset($arrInput['username']) || !isset($arrInput['role']) || !isset($arrInput['envName'])|| !isset($arrInput['remark'])){
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}

	$pid = intval($arrInput['pid']);   //产品线id
	$username = $arrInput['username']; //用户名
	$role = intval($arrInput['role']); //用户角色
	$envName = $arrInput['envName']; //环境名称
	$remark = $arrInput['remark']; //环境备注

	if(!self::_init(self::SERVICE_NAME)){
		return self::_errRet(Oep_Errno::CONF_ERROR);
	}
	
	//根据pid和username获取用户是否通过审核,角色是否正确
	$userInfo = Service_Envmanage_lib_BaseEnv::getUserInfoByPidAndUsername($pid, $username);
	$userPass = intval($userInfo['info']['userPass']); //角色申请是否通过 0未通过 1通过
	$role1 = intval($userInfo['info']['role']); //用户角色信息，，0普通用户 1产品线管理员 2系统管理员 3其他
	if(0==$userPass){
		Bingo_Log::warning("the userRole has not been approvaled");
		return self::_errRet(Oep_Errno::HAS_NOT_BEEN_APPROVALED);
	}
	if($role!=$role1){
		Bingo_Log::warning("the userRole is wrong");
		return self::_errRet(Oep_Errno::PARAM_ERR);
	}
	
	if(1==$role||2==$role){//系统管理员或者产品线管理员修改备注
		//根据envName获取环境信息
		$envInfo = Service_Envmanage_lib_BaseEnv::getEnvInfoByEnvName($envName);
		$envUsername =  $envInfo['info']['username']; //指定envName记录的username
		
		$envInfo = false;
		$envInfo = Service_Envmanage_lib_BaseEnv::updateRemark($envName, $remark);
		if(0 == $envInfo ['succ']){
			Bingo_Log::warning("failed to update remark");
			return self::_errRet(Oep_Errno::UPDATE_REMARK_ERROR);
		}
		//邮件通知用户环境释放成功
		Bingo_Log::notice("success to update remark");
		
	}elseif (0==$role){//普通用户修改自己的环境备注
		//根据envName获取环境信息
		$envInfo = Service_Envmanage_lib_BaseEnv::getEnvInfoByEnvName($envName);
		$envUsername =  $envInfo['info']['username']; //指定envName记录的username
		if($envUsername!=$username){
			Bingo_Log::warning("the username is wrong");
			return self::_errRet(Oep_Errno::PARAM_ERR);
		}
		
		$envInfo = false;
		$envInfo = Service_Envmanage_lib_BaseEnv::updateRemark($envName, $remark);
		if(0 == $envInfo ['succ']){
			Bingo_Log::warning("failed to update remark");
			return self::_errRet(Oep_Errno::UPDATE_REMARK_ERROR);
		}
		
		Bingo_Log::notice("success to update remark");
	}
	
	$error = Oep_Errno::SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),//错误信息
	);
	return $arrOutput;
}
}
