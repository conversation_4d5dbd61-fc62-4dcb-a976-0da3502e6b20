<?php
//自动删除释放环境 
$unDelEnvListsOut = Tieba_Service::call('oep','getAllUnDelEnv','');
if( $unDelEnvListsOut['errmsg'] != 0){
	return 1;
}
$unDelEnvLists=$unDelEnvListsOut['envInfo'];
foreach ($unDelEnvLists as $tmpEnv){
	sleep(3);
	$groupArr=array('group_id'=>$tmpEnv['groupId']);
	$delGroupOut = Tieba_Service::call('oep','deletegroup',$groupArr);
	$delGroupResult = json_decode($delGroupOut, true);
	if($delGroupResult['result'] == true){
		$delInput=array(
		        'username'=>'',
				'envName'=>$tmpEnv['envName'],
		);
		$delEnvOut = Tieba_Service::call('oep','delFreeEnv',$delInput);
		if( $delEnvOut['errmsg'] != 0){
			return 1;
		}
	}
}
