#!/home/<USER>/marsers/mahong/code/python/bin/python
# -*- coding: utf-8 -*-
# author = 'mahong'
# version = '1.0'
# date = '2013-7-4'
# descripton : ִ�� stop/start

import init
import os
import json

execConf = os.getcwd() + "/restart/env_modules.json"
execAction = ["restart","stop"]
execGroup = ["ALL","SANDBOX"]

def execEnv(Action,Group,envName):
	
	
	if not Action in execAction and not Group in execGroup:
		return -1
	
	execDirt = json.load(open(execConf))
	execDirt = execDirt["env_modules"]
	execLoad = []
	
	if len(execDirt) == 0:
		return -1

	if Group == "ALL":
		Group = execGroup
	else:
		Group = [Group]
	
	for module in execDirt:
		if execDirt[module]["module_group"] in Group and execDirt[module]["monitor_flag"] != "2":
			execLoad.append(execDirt[module]["load_script"])
	
	for load in execLoad:
		execCmd = "matrix_jail %s /bin/bash %s %s 2>&1" % (envName,load,Action)
		execInfo=os.popen(execCmd)
	

	return 0

if __name__ == "__main__":
        print os.getcwd()
	execEnv("start","BACKEND")

