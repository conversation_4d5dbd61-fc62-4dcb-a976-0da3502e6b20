#!/home/<USER>/marsers/mahong/code/python/bin/python
# -*- coding: utf-8 -*-
# author = 'mahong'
# version = '1.0'
# date = '2013-7-4'
# descripton : ֹͣ����

import ExecEnv
import sys
import subprocess


if __name__ == "__main__":
    #��ȡ��ǰ����Tag
    Tag = sys.argv[1]
    envName = sys.argv[2]
    if len(sys.argv) == 3 and Tag != "NULL":
        ExecEnv.execEnv("stop",Tag,envName)
    else:
        print "  \033[0;33;1m Stop ������������\033[0m"
        print "   --args: [ALL|SANDBOX]"
    cmd = 'curl --data "method=setEnvProc&envName=%s&processId=1&ie=utf-8&format=json" "service.tieba.baidu.com/service/oep"' % (envName)
    handle = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE)
