#!/home/<USER>/marsers/mahong/code/python/bin/python
# -*- coding: utf-8 -*-
# author = 'mahong'
# version = '1.0'
# date = '2013-2-24'
__version__ = "1.0"

from string import Template

class envTemplate(object):
	envTemplate = None

	def __init__(self,varTemplateFile):
		self.envTemplateFile = varTemplateFile
		#To Do 
		#�ļ��쳣�ж�
		self.envTemplateContent = open(self.envTemplateFile).read()
		#self.envTemplate(self.envTemplateContent)
		self.evnContent = ""
	
	def assign(self,envTemplateValue):
		self.envTemplate = Template(self.envTemplateContent)
		self.envContent = self.envTemplate.safe_substitute(envTemplateValue)
	
	def display(self,envGenFile):
		#mode "w" if not exist then create it
		open(envGenFile,"w").write(self.envContent)

if __name__ == "__main__":
	testObject = envTemplate("/home/<USER>/marsers/mahong/code/env-python/conf/DEPLOY/deploy_html.template")
	testTemplateValue = {"result":"Success","errno":1,"errmsg":"TESTTEST"}
	testObject.assign(testTemplateValue)
	testObject.display("/home/<USER>/release/myContent.html")
	
		
		
