#!/home/<USER>/marsers/mahong/code/python/bin/python
# -*- coding: utf-8 -*-
# author = 'mahong'
# version = '1.0'
# date = '2013-2-24'
__version__ = "1.0"

import pexpect
import os


def envScp(varFrom, varTo=".",varPasswd="forumapptest"):
    foo = pexpect.spawn('scp -r %s %s' % (varFrom,varTo))
    foo.expect('.ssword:*')
    foo.sendline(varPasswd)
    #foo.interact()
    foo.expect(pexpect.EOF)
    foo.close()
    return 0

def envFtp(varFrom, varTo=".",  varPasswd="getprod"): 
    #To Do :��pexpect ʵ��Ftp ���ش���
	varUname = varFrom.split("@")[0]
	varFtpdir = varFrom.split("@")[1]
	if len(varUname) == 0 or len(varFtpdir) == 0:
		return -1
	cmd = "wget -r --no-remove-listing --retr-symlinks -P %s ftp://%s:%s@%s/ -l 0 > /dev/null 2>&1;" % (varTo, varUname, varPasswd, varFtpdir.replace("\n",""))
	os.system(cmd)
	cmd="cp -rf %s %s" % (varTo+"/"+varFtpdir.replace("\n","").replace(":","/"),varTo)
	os.system(cmd)
	cmd = "rm -rf %s" % (varTo+"/buildprod.scm.baidu.com")
	os.system(cmd)
	return 0


if __name__ == "__main__":
    #To Test envScp()
    testFrom = "/home/<USER>/marsers/mahong/code/env-python/lib/libenv/envLog.py"
    testTo = "<EMAIL>:/home/<USER>/"
    envScp(testFrom,testTo)
    #To Test envFtp()
    testFrom = "<EMAIL>:/temp/data/prod-64/app/search/forum/forum-php/tieba-commit/forum9.0.2299/r146010-9"
    testTo = "ABC"
    envFtp(testFrom, testTo)
