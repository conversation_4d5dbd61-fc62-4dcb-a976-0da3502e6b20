<?php
//批量扩容
$unEnvNameListsOut = Tieba_Service::call('oep','getAllUnEnvName','');
if( $unEnvNameListsOut['errmsg'] != 0){
	return 1;
}	
$unEnvNameLists=$unEnvNameListsOut['envInfo'];
foreach ($unEnvNameLists as $envTmp){
	sleep(3);
	$arrExpand=array(
			'group_id' => $envTmp['groupId'],
			'count' => 1,
			'url' => 'http://oep.baidu.com/oep/callback/cbExpand?hades=1',
			'param' => array(
					'envTypeId'=>$envTmp['envTypeId'],
					'serviceId'=>$envTmp['serviceId'],
					'groupId'=>$envTmp['groupId'],
			),
	);
	$expandHadOut = Tieba_Service::call('oep','expand',$arrExpand);
}