<?php
require_once("./SyncFile.php");
class parseToRal{
	
	public static $RALCONF_SRC="/home/<USER>/orp001/conf/ral/ral.conf";
	public static $RALPWCONF_SRC="/home/<USER>/orp001/conf/ral/ral-passwd.conf";
	public static $ral = array();
	public static $ralpwd = array();
	public static $dstpath = "ral.conf.new";
	public static $dstralpasswd = "ral-passwd.conf.new";
	public static $targetUser = "root";
	public static $targetPasswd = "root";
	public static $targetPort = 3346;
	public static $targetCPort = 3306;
	public static $dbTargetHost = "***********";
	public static $redisTargetHost = "************";
	
	//??ȡdba??Ϣ
	public static function dbaInfo()
	{
		$dbarray = array();
		$dbaConf  = SyncFile::fetchDBConf(1);

		foreach($dbaConf as $key)
		{
			foreach($key['dbnames'] as $dbname)
			{
				$dbarray[$dbname] = array 
				(
					'hostname' => $key['hostname'],
					'port'     => $key['port'],
					'is_match' => 0,
				);
			}
		}	
		return $dbarray;
	}
	
	//??ȡddbs??Ϣ
	public static function ddbsInfo()
	{
		$ddbsConf = SyncFile::fetchDBConf(2);
		$ddbsarray = array();
		foreach($ddbsConf as $key)
		{
			foreach($key['dbnames'] as $dbname)
			{
				$ddbsarray[$dbname] = array 
				(
					'hostname' => $key['hostname'],
					'port'     => $key['port'],
					'username' => $key['namepass']['username'],
					'passwd'   => $key['namepass']['passwd'],
					'is_match' => 0,
				);
			}
		}	
		return $ddbsarray;
	}
	
	//??ȡral????
	public static function getDBOnlineConf()
	{
		$ralPath=dirname(self::$RALCONF_SRC);
		$ralFile=basename(self::$RALCONF_SRC);
		self::$ral=config_load($ralPath,$ralFile);
		return self::$ral['CamelConfig']['ServiceConfig']['Local']['Service'];
    
	}
	
	//??ȡral-webfoot????
	public static function getWebfootConf()
	{
		$ralPath=dirname(self::$RALCONF_SRC);
		$ralFile=basename(self::$RALCONF_SRC);
		self::$ral=config_load($ralPath,$ralFile);
		return self::$ral['CamelConfig']['ServiceConfig']['Webfoot']['Service'];
    
	}
	
	//??ȡral-pass????
	public static function getDBOnlinePwdConf()
    {       
        $ralpwdFile=basename(self::$RALPWCONF_SRC);
        $ralpwdPath=dirname(self::$RALPWCONF_SRC);
        self::$ralpwd = config_load($ralpwdPath,$ralpwdFile);
		return true;
    }

	public static function getRedisPort($redisServerName)
	{
		$redisfile = "/home/<USER>/envtools/Redis/redis_ral.list";
		$redisral = file($redisfile);
		foreach($redisral as $value)
		{

			$valuearray = explode(":",$value);  
			if(strpos($redisServerName,$valuearray[0]))
			{
				return trim($valuearray[1]);
			}
		}

	}
	//parseRedis????redis??ral.conf??
	public static function parseRedis($ralConf)
	{
		$search = '/^Redis.*[^offline]$/';
		
		foreach($ralConf as $confItem)
		{
				if( preg_match($search,$confItem['Name']) )
				{
						
						//??ȡ??Ӧredis?Ľӿ?
						$redisPort = self::getRedisPort($confItem['Name']);
						//??????offline????
						self::$ral['CamelConfig']['ServiceConfig']['Local']['Service'][] = array(
							'Name' =>  $confItem['Name'] . "_offline",
							'DefaultPort' => $redisPort,
							'DefaultRetry' => '',
							'DefaultConnectType' => 0,
							'DefaultConnectTimeOut' => 1500,
							'DefaultWriteTimeOut' => 1500,
							'DefaultReadTimeOut' => 1500,
							'Extra' => '',
							'Server' => array(
								0 => array(
									'IP' => self::$redisTargetHost,
								),
							),
							'SuperStrategy' => array(
								'Balance' => 'Random',
								'ConnectQueueSize' => '100',
								'ConnectX1' => '10',
								'ConnectX2' => '40',
								'ConnectY2' => '6',
								'ConnectY1' => '95',
								'CrossRoom' => 'Off',
								'Hybrid'    => 'On',
								'HealthyBackupThreshold' => '3',
								'HealthyCheckTime' => '3',
								'HealthyMinRate' => '0.1',
								'HealthyQueueSize' => '100',
								'HealthyTimeout' => '100',
							),
							'Protocol' => array(
								'Name' => 'nshead',
							),
							'Converter' => array(
								'Name' => 'mcpack2',
							),
						
						);
					
			
						
					
				}
				
				
		}
		
	
	}
	
	
	//????pwd??ral-passwd ????
	public static function parsePwd($dbConf,$serverName,$dbname,$flag)
	{
		//DBA??????
		if($flag == 1)
		{
			self::$ralpwd['PasswdConfig']['Service'][] = array(
				'Name' => $serverName,
				'User' => 'qa',
				'Passwd' => 'MhxzKhl',
			
			);
			return true;
			
		}
		//DDBS????
		elseif($flag == 2)
		{
			self::$ralpwd['PasswdConfig']['Service'][] = array(
				'Name' => $serverName,
				'User' => $dbConf[$dbname]['username'],
				'Passwd' => $dbConf[$dbname]['passwd'],
			);
			return true;
		}
		elseif($flag == 3)
		{
			self::$ralpwd['PasswdConfig']['Service'][] = array(
				'Name' => $serverName,
				'User' => self::$targetUser,
				'Passwd' => self::$targetPasswd,
			);
			return true;
		}
		else
		{
			return false;
		}
		
	}
	
	//????beiku???鵽ral??ral-pass??
	public static function beikuRalConf($dbConf,$confItem,$dbname,$flag)
	{
	
			$confItem['Name'] = $confItem['Name'] . "_beiku";
			$confItem['DefaultPort'] = $dbConf[$dbname]['port'];
			if($flag == 1)
			{
				$serverCmd = "get_instance_by_service -i %s | awk '{print $2}'";
				$cmd = sprintf($serverCmd, $dbConf[$dbname]['hostname']);
				exec($cmd, $serverIp);
				$dbConf[$dbname]['hostname'] = $serverIp[0];
			}
			$confItem['Server'] = array( 
				0 => array(
					'IP' => $dbConf[$dbname]['hostname'],
				)
			);
			self::$ral['CamelConfig']['ServiceConfig']['Local']['Service'][] = $confItem;
			self::parsePwd ($dbConf,$confItem['Name'],$dbname,$flag);
	
	}
	
	//???����????鵽ral??ral-pass??
	public static function offlineRalConf($confItem,$dbname)
	{			
			$confItem['Name'] = $confItem['Name'] . "_offline";
			$confItem['DefaultPort'] = self::$targetPort;
			$confItem['Server'] = array(
				0 => array(
					'IP' => self::$dbTargetHost,
				),
			);
			self::$ral['CamelConfig']['ServiceConfig']['Local']['Service'][] = $confItem;
			self::parsePwd ($dbConf,$confItem['Name'],$dbname,3);
	}
	
	//????c???⵽ral??
	public static function cbeikuRalConf($dbConf,$dbname,$value,$flag,$is_beiku)
	{
	
			if($is_beiku == 1)
			{
				$serverName = "DB_".$dbname."_beiku";
				$port = $value['port'];
				$extra = "{\"dbname\":\"".$dbname."\"}";
				$host  = $value['hostname'];

				self::parsePwd ($dbConf,$serverName,$dbname,$flag);
			}
			
			self::$ral['CamelConfig']['ServiceConfig']['Local']['Service'][] = array(
				'Name' =>  $serverName,
				'DefaultPort' => $port,
				'DefaultRetry' => '',
				'DefaultConnectType' => 0,
				'DefaultConnectTimeOut' => 1500,
				'DefaultWriteTimeOut' => 1500,
				'DefaultReadTimeOut' => 1500,
				'Extra' => $extra,
				'Server' => array(
					0 => array(
						'IP' => $host,
					),
				),
				'SuperStrategy' => array(
					'Balance' => 'Random',
					'CrossRoom' => 'Off',
				),
				'Protocol' => array(
				'Name' => 'mysql',
				),
				'Converter' => array(
					'Name' => 'string',
				),
						
			);
		
			
	
	}
	
	//?????????����?db??ral.conf?Լ?ral-passwd.conf?Լ?ral-passwd
	public static function parseDba($dbConf,$ralLocal,$flag)
	{
	
			foreach($ralLocal as $confItem)
			{
				if( isset($confItem['Extra']) && ($confItem['Extra'] !== "") )
				{
					$dbInfo=json_decode($confItem['Extra'],true);
					//??????offline???ã???ral,ral-passwd��??
					self::offlineRalConf($confItem,$dbInfo['dbname']);

					//?????ж??Ƿ???????beiku??
					if (isset($dbConf[$dbInfo['dbname']]))
					{
						$dbConf[$dbInfo['dbname']]['is_match'] = 1;
						//??????beiku???ã???ral,ral-passwd��??
						self::beikuRalConf($dbConf,$confItem,$dbInfo['dbname'],$flag);
					}
				}
			
			}
			foreach($dbConf as $dbname => $value)
			{
				if($value['is_match'] == 0)
				{
					self::cbeikuRalConf($dbConf,$dbname,$value,$flag,1);
				}
			}
			
	}
	
	//??ral????ƴ??ral.conf
	public function makeRalConf()
    {
        $ralconf = '';
        $ralConf .= "[CamelConfig]\n";
        $ralConf = $ralConf . "[.WebfootBase]\n";
        foreach(self::$ral['CamelConfig']['WebfootBase'] as $key => $value) 
        {       
            $ralConf = $ralConf . $key ." : " . $value . "\n"; 
        }       
        $ralConf .= "[.ServiceConfig]\n";  
        $ralConf = $ralConf . "[..Webfoot]\n";
        foreach( self::$ral['CamelConfig']['ServiceConfig']['Webfoot']['Service'] as $key => $value)
		{
                $ralConf = $ralConf . "[...@Service]\n";
                foreach($value as $sub_key => $sub_value){
                    $ralConf = $ralConf . $sub_key . " : " . $sub_value . "\n";
                }
        }
    
		$ralConf = $ralConf . "[..Local]\n";
        foreach ( self::$ral['CamelConfig']['ServiceConfig']['Local']['Service'] as $key => $value )
		{
            if(!is_array($value) || count($value) == 0)
                continue;
            $ralConf = $ralConf . "[...@Service]\n";
            foreach ($value as $sub_key => $sub_value){
                if ($sub_key == "Server"){
                    foreach ($sub_value as $end_key => $end_value){
                        $ralConf = $ralConf . "[....@Server]\n";
                        foreach($end_value as $tail_key => $tail_value ){
                            $ralConf = $ralConf . $tail_key . " : " . $tail_value . "\n";
                        }
                    }
                }else if($sub_key == "SuperStrategy"){
                    $ralConf = $ralConf . "[....SuperStrategy]\n";
                    foreach ($sub_value as $end_key => $end_value){
                        $ralConf = $ralConf . $end_key . " : " . $end_value . "\n";
                    }
                }else if($sub_key == "Protocol"){
                    $ralConf = $ralConf . "[....Protocol]\n";
                    foreach ($sub_value as $end_key => $end_value){
                        $ralConf = $ralConf . $end_key . " : " . $end_value . "\n";
                    }
                }else if($sub_key == "Converter"){
                    $ralConf = $ralConf . "[....Converter]\n";
                    foreach ($sub_value as $end_key => $end_value){
                        $ralConf = $ralConf . $end_key . " : " . $end_value . "\n";
                    }
                }else{
                    $ralConf = $ralConf . $sub_key . " : " . $sub_value . "\n";
				}
            }
            $ralConf = $ralConf . "\n";
        }
        $fw = fopen(self::$dstpath, 'w');
        fwrite($fw, $ralConf);
        fclose($fw);

    }
	
	//??ral-passwd????ƴ??ral-passwd.conf
	public function makeRalPwdConf()
	{
		$content = "[PasswdConfig]\n";
        foreach(self::$ralpwd["PasswdConfig"]["Service"] as $index => $service){
            $content = $content."[.@Service]\n"."Name : ".$service["Name"]."\n".
                "User : ".$service["User"]."\n"."Passwd : ".$service["Passwd"]."\n\n";
        }       
      
        $fp = fopen(self::$dstralpasswd,"w");
        fwrite($fp,$content);
        fclose($fp);
	}
	

}
//END CLASS


//??ȡral??????ral-pass????
$ralLocal = parseToRal::getDBOnlineConf();
$ralWebfoot = parseToRal::getWebfootConf();
$ralPwd = parseToRal::getDBOnlinePwdConf();

//??ȡRedis??Ϣ???ҽ?Redis??Ϣд??ral????
parseToRal::parseRedis($ralLocal);
parseToRal::parseRedis($ralWebfoot);



//??ȡdba??Ϣ???ҽ?dba??Ϣд??ral?????Լ?ral-passwd
$dbConf = parseToRal::dbaInfo();
parseToRal::parseDba($dbConf,$ralLocal,1);

//??ȡddbs??Ϣ???ҽ?ddbs??Ϣд??ral?????Լ?ral-passwd
$ddbsConf = parseToRal::ddbsInfo();
parseToRal::parseDba($ddbsConf,$ralLocal,2);


//var_dump(parseToRal::$ral);
//var_dump(parseToRal::$ralpwd);

//?????µ?ral?ļ???pass?ļ?
parseToRal::makeRalConf();
parseToRal::makeRalPwdConf();


?>

