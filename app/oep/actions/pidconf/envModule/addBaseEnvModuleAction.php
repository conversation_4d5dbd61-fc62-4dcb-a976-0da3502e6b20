<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014:09:07
 * @version
 * 新增环境模块
 */
class addBaseEnvModuleAction extends Util_BaseAction {
    protected $_pidInfo = array();
    public function execute() {
    	try{
    	 	$this->_initParams();
   
    	 	//当前只支持orp模块
             if(1 == intval($this->_pidInfo['isOrpModule'])){
              	//添加orp模块
             	$baseEnvModuleInput = array(            	
             		'envTypeId' => $this->_pidInfo['envTypeId'],
             		'envModule' => $this->_pidInfo['envModule'],  
             		'isOrpModule' => $this->_pidInfo['isOrpModule'],         	
             		'onlinePath' => $this->_pidInfo['unOrpBaseNode'],
             		'offlinePath' => $this->_pidInfo['offlinePath'],
             		'owner' => $this->_pidInfo['owner'],
             		'remark' => $this->_pidInfo['remark'],            	
             	);
             	unset($arrOutput);
             	$arrOutput = Util_Service::innerCall('oep', 'addBaseEnvModule', $baseEnvModuleInput);
             	if(0 != intval($arrOutput['errno'])){
                Util_Log::warning("fail to add base env module : ".serialize($arrOutput));
                throw new Oep_Error($arrOutput['errno'], $arrOutput['errmsg']);
            	}    
             }
             else if(0 == intval($this->_pidInfo['isOrpModule'])){//暂时不支持非orp模块
             	Bingo_Log::warning("can not add unorpModule curently ");
             	$arrOutput['errno'] = Oep_Errno::UN_ORP_MODULE_FAIL;
             	$arrOutput['errmsg'] = 'add un orp module fail';
                throw new Oep_Error($arrOutput['errno'], $arrOutput['errmsg']);
             }
                        
             $this->assignJson($arrOutput);
             
         }catch (Oep_Error $e){//捕获异常 
            $this->assignJson($arrOutput);
            $this->_errno = $e->getNo();
        }
     }
            
    
   //获取提交入参
    private function _initParams () {

   
        $this->_pidInfo['envTypeId'] = trim(Bingo_Http_Request::getNoXssSafe('envTypeId', ''));
    	if (!isset($this->_pidInfo['envTypeId'])) {
            $strErr = sprintf ('invalid envTypeId[%s]', $this->_pidInfo['envType']);
            throw new Oep_Error(Oep_Errno::PARAM_ERR, $strErr);
        }
        $this->_pidInfo['isOrpModule'] = trim(Bingo_Http_Request::getNoXssSafe('isOrpModule', ''));
     	if (!isset($this->_pidInfo['isOrpModule'])) {
            $strErr = sprintf ('invalid isOrpModule[%s]', $this->_pidInfo['isOrpModule']);
            throw new Oep_Error(Oep_Errno::PARAM_ERR, $strErr);
        }
        $this->_pidInfo['envModule'] = trim(Bingo_Http_Request::getNoXssSafe('envModule', ''));
     	if (!isset($this->_pidInfo['envModule'])) {
            $strErr = sprintf ('invalid moduleName[%s]', $this->_pidInfo['envModule']);
            throw new Oep_Error(Oep_Errno::PARAM_ERR, $strErr);
        }
        $this->_pidInfo['onlinePath'] = trim(Bingo_Http_Request::getNoXssSafe('onlinePath', ''));
    	if (!isset($this->_pidInfo['onlinePath'])) {
            $strErr = sprintf ('invalid onlinePath[%s]', $this->_pidInfo['onlinePath']);
            throw new Oep_Error(Oep_Errno::PARAM_ERR, $strErr);
        }
        $this->_pidInfo['offlinePath'] = trim(Bingo_Http_Request::getNoXssSafe('offlinePath', ''));
    	if (!isset($this->_pidInfo['offlinePath'])) {
            $strErr = sprintf ('invalid offlinePath[%s]', $this->_pidInfo['offlinePath']);
            throw new Oep_Error(Oep_Errno::PARAM_ERR, $strErr);
        }
        $this->_pidInfo['owner'] = trim(Bingo_Http_Request::getNoXssSafe('owner', ''));
     	if (!isset($this->_pidInfo['owner'])) {
            $strErr = sprintf ('invalid owner[%s]', $this->_pidInfo['owner']);
            throw new Oep_Error(Oep_Errno::PARAM_ERR, $strErr);
        }
        $this->_pidInfo['remark'] = trim(Bingo_Http_Request::getNoXssSafe('remark', ''));
       
    }

}