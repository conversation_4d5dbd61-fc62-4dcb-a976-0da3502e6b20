<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014:09:15
 * @version
 * 获取IP Port信息
 */
class getIPPortAction extends Util_BaseAction {
	protected $_pidInfo = array();
    public function execute() {
    	try{
    	 	 $this->_initParams();
    	 	 $ipPortInfo = Service_Pidconf_Conf_BaseEnvConf::$ralIpPortConf;
    	 	 $ipInfo = $ipPortInfo['ip'];
    	 	 $portInfo = $ipPortInfo['port'];
    	 	 $arrOutput['errno'] = 0;
    	 	 $arrOutput['errmsg'] = 'success';            
             $arrOutput['data']['ip'] = $ipInfo; 
             $arrOutput['data']['port'] = $portInfo;     		   		            
             $this->assignJson($arrOutput);
             
         }catch (Oep_Error $e){//捕获异常 
            $this->assignJson($arrOutput);
            $this->_errno = $e->getNo();
        }
     }
            
    
   //获取提交入参
    private function _initParams () {
       
    }
}