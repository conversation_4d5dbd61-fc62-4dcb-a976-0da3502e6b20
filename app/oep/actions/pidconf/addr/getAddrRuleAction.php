<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014:09:08
 * @version
 * 获取地址替换规则
 */
class getAddrRuleAction extends Util_BaseAction {
	const NUM_PER_PAGE = 10;
    protected $_pidInfo = array();
    public function execute() {
    	try{
    	 	 $this->_initParams(); 
    	 	 if(empty($this->_pidInfo['ruleTypeId']) && empty($this->_pidInfo['envTypeId'])){//根据pid获取addrRule
    	 	 	$ruleInput = array(             	
             		'pid' => $this->_pidInfo['pid'],           	       	
             	);
             	unset($arrOutput);
             	$arrOutput = Util_Service::innerCall('oep', 'getRuleByPid', $ruleInput);
             	if(0 != intval($arrOutput['errno'])){
                	Bingo_Log::warning("fail to get ral rule : ".serialize($arrOutput));
                	throw new Oep_Error($arrOutput['errno'], $arrOutput['errmsg']);
             	}
             	$ruleInfo = $arrOutput['ruleInfo'];
             	$data = array();
             	$page = array();
             	$page['current_pn'] = empty($this->_pidInfo['page'])?1:intval($this->_pidInfo['page']);
             	$page['total_count'] = count($ruleInfo);
             	$page['total_pn'] = ceil($page['total_count']/self::NUM_PER_PAGE);
             	if($this->_pidInfo ['page'] > $page['total_pn']){
					$list = array();
				}else{
					$start = self::NUM_PER_PAGE*($page['current_pn'] - 1);
					$list = array_slice($ruleInfo,$start,self::NUM_PER_PAGE);
				}       
            	$data['page'] = $page;
            	$data['list'] = $list;      
    	 	 }
    		 else if(empty($this->_pidInfo['ruleTypeId']) && !empty($this->_pidInfo['envTypeId'])){//根据envTypeId获取rule    		   
             	$ruleInput = array(             	
             		'envTypeId' => $this->_pidInfo['envTypeId'],           	       	
             	);
             	unset($arrOutput);
             	$arrOutput = Util_Service::innerCall('oep', 'getRuleByEnvTypeId', $ruleInput);
             	if(0 != intval($arrOutput['errno'])){
                	Bingo_Log::warning("fail to get ral rule : ".serialize($arrOutput));
                	throw new Oep_Error($arrOutput['errno'], $arrOutput['errmsg']);
             	}
             	$ruleInfo = $arrOutput['ruleInfo'];
             	$data = array();
             	$page = array();
             	$page['current_pn'] = empty($this->_pidInfo['page'])?1:intval($this->_pidInfo['page']);
             	$page['total_count'] = count($ruleInfo);
             	$page['total_pn'] = ceil($page['total_count']/self::NUM_PER_PAGE);
             	if($this->_pidInfo ['page'] > $page['total_pn']){
					$list = array();
				}else{
					$start = self::NUM_PER_PAGE*($page['current_pn'] - 1);
					$list = array_slice($ruleInfo,$start,self::NUM_PER_PAGE);
				}       
            	$data['page'] = $page;
            	$data['list'] = $list;                                   	
    		 }
    		 else if(!empty($this->_pidInfo['ruleTypeId']) && !empty($this->_pidInfo['envTypeId'])){//根据ruleTypeId获取rule
    		 	$ruleInput = array(             	
             		'ruleTypeId' => $this->_pidInfo['ruleTypeId'],           	       	
             	);
             	unset($arrOutput);
             	$arrOutput = Util_Service::innerCall('oep', 'getRuleByRuleTypeId', $ruleInput);
             	if(0 != intval($arrOutput['errno'])){
                	Bingo_Log::warning("fail to get rule : ".serialize($arrOutput));
                	throw new Oep_Error($arrOutput['errno'], $arrOutput['errmsg']);
             	}
             	$ruleInfo = $arrOutput['ruleInfo'];
             	$data = array();
             	$page = array();
             	$page['current_pn'] = empty($this->_pidInfo['page'])?1:intval($this->_pidInfo['page']);
             	$page['total_count'] = count($ruleInfo);
             	$page['total_pn'] = ceil($page['total_count']/self::NUM_PER_PAGE);
             	if($this->_pidInfo ['page'] > $page['total_pn']){
					$list = array();
				}else{
					$start = self::NUM_PER_PAGE*($page['current_pn'] - 1);
					$list = array_slice($ruleInfo,$start,self::NUM_PER_PAGE);
				}       
            	$data['page'] = $page;
            	$data['list'] = $list;   
    		 }
    		 unset($arrOutput);
    		 $arrOutput['errno'] = 0;
             $arrOutput['errmsg'] = 'success';
             $arrOutput['data'] = $data;               	 	    	 	                        
             $this->assignJson($arrOutput);
             
         }catch (Oep_Error $e){//捕获异常 
            $this->assignJson($arrOutput);
            $this->_errno = $e->getNo();
        }
     }
            
    
   //获取提交入参
    private function _initParams () {
    	$this->_pidInfo['pid'] = trim(Bingo_Http_Request::getNoXssSafe('pid', ''));
        if (!isset($this->_pidInfo['pid'])) {
            $strErr = sprintf ('invalid pid[%s]', $this->_pidInfo['pid']);
            throw new Oep_Error(Oep_Errno::PARAM_ERR, $strErr);
        }   
        $this->_pidInfo['envTypeId'] = trim(Bingo_Http_Request::getNoXssSafe('envTypeId', ''));        
    	$this->_pidInfo['ruleTypeId'] = trim(Bingo_Http_Request::getNoXssSafe('ruleTypeId', ''));     
        $this->_pidInfo['page'] = trim(Bingo_Http_Request::getNoXssSafe('page', ''));  
    }
}