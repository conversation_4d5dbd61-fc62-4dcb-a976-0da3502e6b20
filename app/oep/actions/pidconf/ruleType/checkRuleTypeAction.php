<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014:09:09
 * @version
 * 检测ruleType是否已存在
 */
class checkRuleTypeAction extends Util_BaseAction {
    protected $_pidInfo = array();
    public function execute() {
    	try{
    	 	$this->_initParams();                      
            $ruleTypeInput = array(
             	'envTypeId' => $this->_pidInfo['envTypeId'], 
             	'ruleType'  => $this->_pidInfo['ruleType'],       	       	
             );
             unset($arrOutput);
             $arrOutput = Util_Service::innerCall('oep', 'checkRuleType', $ruleTypeInput);
             if(0 != intval($arrOutput['errno'])){
                Bingo_Log::warning("fail to check ruleType : ".serialize($arrOutput));
                throw new Oep_Error($arrOutput['errno'], $arrOutput['errmsg']);
             }  
             $ruleTypeInfo = $arrOutput;
             unset($arrOutput);
             if(1 == intval($ruleTypeInfo['isExist'])){
                $arrOutput['errno'] = -1;
                $arrOutput['errmsg'] = 'ruleType alreay existed';            
             }       
             else{   
                $arrOutput['errno'] = 0;
                $arrOutput['errmsg'] = 'success';            
             }                                
             $this->assignJson($arrOutput);
             
         }catch (Oep_Error $e){//捕获异常 
            $this->assignJson($arrOutput);
            $this->_errno = $e->getNo();
        }
     }
            
    
   //获取提交入参
    private function _initParams () {
        $this->_pidInfo['envTypeId'] = trim(Bingo_Http_Request::getNoXssSafe('envTypeId', ''));
        if (!isset($this->_pidInfo['envTypeId'])) {
            $strErr = sprintf ('invalid envTypeId[%s]', $this->_pidInfo['envTypeId']);
            throw new Oep_Error(Oep_Errno::PARAM_ERR, $strErr);
        }  
        $this->_pidInfo['ruleType'] = trim(Bingo_Http_Request::getNoXssSafe('ruleType', ''));
     	if (!isset($this->_pidInfo['ruleType'])) {
            $strErr = sprintf ('invalid ruleType[%s]', $this->_pidInfo['ruleType']);
            throw new Oep_Error(Oep_Errno::PARAM_ERR, $strErr);
        }                
    }
}