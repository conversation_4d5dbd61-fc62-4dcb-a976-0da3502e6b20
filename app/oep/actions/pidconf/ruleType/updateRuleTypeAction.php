<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014:09:16
 * @version
 * 更新地址替换规则类型ruleType
 */
class updateRuleTypeAction extends Util_BaseAction {
    protected $_pidInfo = array();
    public function execute() {
    	try{
    	 	$this->_initParams();

             $ruleTypeInput = array(            	
             	'envTypeId' => $this->_pidInfo['envTypeId'],
             	'ruleType' => $this->_pidInfo['ruleType'],
             	'ruleTypeId' => $this->_pidInfo['ruleTypeId'],             	             		
             	'owner' => $this->_pidInfo['owner'],
             	'remark' => $this->_pidInfo['remark'],            	
             );
             unset($arrOutput);
             $arrOutput = Util_Service::innerCall('oep', 'updateRuleType', $ruleTypeInput);
             if(0 != intval($arrOutput['errno'])){
               	Util_Log::warning("fail to update rule type : ".serialize($arrOutput));
                throw new Oep_Error($arrOutput['errno'], $arrOutput['errmsg']);
             }                                        
             $this->assignJson($arrOutput);
             
         }catch (Oep_Error $e){//捕获异常 
            $this->assignJson($arrOutput);
            $this->_errno = $e->getNo();
        }
     }
            
    
   //获取提交入参
    private function _initParams () {

   
        $this->_pidInfo['envTypeId'] = trim(Bingo_Http_Request::getNoXssSafe('envTypeId', ''));
    	if (!isset($this->_pidInfo['envTypeId'])) {
            $strErr = sprintf ('invalid envTypeId[%s]', $this->_pidInfo['envType']);
            throw new Oep_Error(Oep_Errno::PARAM_ERR, $strErr);
        }
        $this->_pidInfo['ruleType'] = trim(Bingo_Http_Request::getNoXssSafe('ruleType', ''));
     	if (!isset($this->_pidInfo['ruleType'])) {
            $strErr = sprintf ('invalid ruleType[%s]', $this->_pidInfo['ruleType']);
            throw new Oep_Error(Oep_Errno::PARAM_ERR, $strErr);
        } 
    	$this->_pidInfo['ruleTypeId'] = trim(Bingo_Http_Request::getNoXssSafe('ruleTypeId', ''));
    	if (!isset($this->_pidInfo['ruleTypeId'])) {
            $strErr = sprintf ('invalid ruleTypeId[%s]', $this->_pidInfo['ruleTypeId']);
            throw new Oep_Error(Oep_Errno::PARAM_ERR, $strErr);
        }     
        $this->_pidInfo['owner'] = trim(Bingo_Http_Request::getNoXssSafe('owner', ''));
     	if (!isset($this->_pidInfo['owner'])) {
            $strErr = sprintf ('invalid owner[%s]', $this->_pidInfo['owner']);
            throw new Oep_Error(Oep_Errno::PARAM_ERR, $strErr);
        }
        $this->_pidInfo['remark'] = trim(Bingo_Http_Request::getNoXssSafe('remark', ''));
       
    }

}