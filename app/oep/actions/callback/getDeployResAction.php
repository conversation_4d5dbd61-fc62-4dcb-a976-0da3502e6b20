<?php
/**
 * 更新环境页面
 * 提供给orp进行回调
 * <AUTHOR>
 * 调用格式：
 *
 *getDeployResAction
 *
 *返回结果：
 *
 *{
 *        data: [
 *                 {
 *                         'errno',
 *                }
 *               ],
 *}
 */
class getDeployResAction extends Util_BaseAction {
	protected $_callbackInfo = array();
	protected $failEnvup = 404;
    public function execute() {
    	try{
    	 	 $this->_initParams();
    	 	 //获取hades接口返回值
			 $callbackInfo = $this->_callbackInfo;
			 //获取参数
    	 	 $sendParamTmp = json_decode($callbackInfo['params'], true);//调用hades接口时传入的参数
             $instanceInfo = json_decode($callbackInfo['data'], true);
             Bingo_Log::warning ( "the callbackInfo is ".print_r($instanceInfo,1) );
    	 	 //判断回调是否成功
    	 	 if(1 != $callbackInfo['success']){
    	 	 	Bingo_Log::warning('failed to get instance info from hades');
                $arrOutput['result'] = false;
                $arrOutput['errmsg'] = 'failed to get instance info from hades';

				$arrSetEnvInput = array
				(
					'processId' => $this->failEnvup,
					'envName'   => $instanceInfo['envName'],
					'remark'    => $instanceInfo['ciweb_url'],
				); 

				$setRes = Util_Service::innerCall('oep','setEnvProc',$arrSetEnvInput);
                throw new Oep_Error($arrOutput['result'], $arrOutput['errmsg']);
    	 	 }

    	 	 //根据获取的数据，进行操作

			 $arrSetEnvInput = array
			 (
				'processId' => $instanceInfo['processId'] ,
				'envName'   => $instanceInfo['envName'],
				'remark'    => $instanceInfo['ciweb_url'],
			 ); 
    	 	  
             $setRes = Util_Service::innerCall('oep','setEnvProc',$arrSetEnvInput);

			 if(!$setRes || $setRes['errmsg'] != Oep_Errno::SUCCESS){
				Bingo_Log::warning ( "call setEnvProc fail" );
				$arrOutput ['errno'] = Oep_Errno::CALL_SERVICE_ERROR;
				$arrOutput ['errmsg'] = 'call service fail';
				throw new Oep_Error ( $arrOutput['errno'], $arrOutput['errmsg'] );
			}
             
			//如果envName是基准环境，则调用finish接口
			$orpJobIdInput = array(
				'envTypeId' => $instanceInfo['envTypeId'],
				'orpBaseNode' => $instanceInfo['envName'],
			);
			$orpJobIdOutput = Util_Service::innerCall('oep','getOrpJobId',$orpJobIdInput);
    	 	if(!$orpJobIdOutput || $orpJobIdOutput['errmsg'] != Oep_Errno::SUCCESS){
				Bingo_Log::warning ( "call getOrpJobId fail" );				
				throw new Oep_Error ( $orpJobIdOutput['errno'], $orpJobIdOutput['errmsg'] );
			}
			if(0 != $orpJobIdOutput['orpJobId']){//envName为基准环境
				$envInfoInput = array(
					'envName' => $instanceInfo['envName'],
				);
				$envInfoOutput = Util_Service::innerCall('oep', 'getEnvInfoByEnvName', $envInfoInput);
				if(0 != $envInfoOutput['errno']){
					Bingo_Log::warning('failed to get service_id of envName');
					return array(
						'errno' => $envInfoOutput['errno'],
						'errmsg' => $envInfoOutput['errmsg'],
					);
				}			
				$finishInput = array(
					'job_id' => $orpJobIdOutput['orpJobId'],
					'service_id' => $envInfoOutput['envInfo']['serviceId']
				);
				$finishOutput = Util_Service::innerCall('oep','deployfinish',$finishInput);
				if(!$finishOutput || $finishOutput['errmsg'] != Oep_Errno::SUCCESS){
					Bingo_Log::warning ( "call getOrpJobId fail" );				
					throw new Oep_Error ( $finishOutput['errno'], $finishOutput['errmsg'] );
				}
			}
			
    	 	 //返回回调接口执行结果
    	 	 $arrOutput['result'] = true;
             $this->assignJson($arrOutput);
             
         }catch (Oep_Error $e){//捕获异常 
            $this->assignJson($arrOutput);
            $this->_errno = $e->getNo();
        }
     }
            
    
   //获取提交入参
    private function _initParams () {
    	$this->_callbackInfo =  array_merge(Bingo_Http_Request::getGetNoXssSafe(), Bingo_Http_Request::getPostNoXssSafe());
    }
}
/* vim: set noexpandtab ts=4 sw=4 sts=4 tw=0: */
