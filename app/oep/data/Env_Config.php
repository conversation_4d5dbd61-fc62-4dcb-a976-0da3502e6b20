<?php
class Env_Config {
	protected static $_root_path = "/home/<USER>/orp/conf";
	protected static $_file_path = "/app/oep/run/";
	protected static $_file_name = "env_info";
	protected static $_env_conf = null;

	public static function loadConfig(){
		self::$_env_conf= Bd_Conf::getConf(self::$_file_path.self::$_file_name);
		if (null === self::$_env_conf) {
			Bingo_Log::warning("load entity configure error. file_name:".self::$_file_path.self::$_file_name);
			return false;
		}
		
		return self::$_env_conf;
	}
	
	public static function getConfig(){
		return self::$_env_conf;
	}
	
	/*���޸ĺ��array���浽�����ļ���*/
	public static function setConfig($conf){
		self::$_env_conf = $conf;
		$name = self::$_root_path.self::$_file_path.self::$_file_name.".conf";
		copy($name, $name.".bak");
		$f = fopen($name, 'w');
		if (null === $f){
				Bingo_Log::warning("open file error, file name:".$name);
				return false;
		}
		$str = Bd_Conf_Utils::array2configure(self::$_env_conf);
		fwrite($f, $str);
		fclose($f);
		return true;
	}
	
	public static function getEntityAttrs(){
		$attrs = array();
		foreach(self::$_env_conf['main_attr'] as $key => $value) {
			$name = null;
			foreach (self::$_env_conf['entity'] as $entity) {
				if ($entity['id'] == $value) {
					$name = $entity['name'];
				}
			}
			if (null === $name) {
				Bingo_Log::warning("can not find entity: id".$value);
				return false;
			}
			
			$attrs[] = array(
					'attr' => $key,
					'entity_name' => $name,
			);
		}
		
		return $attrs;
	}
	public static function addNode($info, $type){
		if (null === self::$_env_conf){
			Bingo_Log::warning("configure is null");
			return null;
		}
		if (null === $info || !isset($info) || !isset($info['attr']) || !isset($info['attr']['main_attr'])) {
			Bingo_Log::warning("add $type error, input = ".serialize($info));
			return null;
		}
		
		$max_num = self::$_env_conf['max_num'][$type];
		self::$_env_conf['max_num'][$type]++;
	
		if ('entity' == $type) {
			$main_att_value = array();
			foreach($info['attr']['main_attr'] as $attr){
				if (null != self::$_env_conf['main_attr'][$attr] || isset(self::$_env_conf['main_attr'][$attr])) {
					Bingo_Log::warning("main_attr name error, main_attr=$attr");
					return false;
				}
				$main_attr_value[] = $attr;
			}

			foreach($main_attr_value as $attr) {
				self::$_env_conf['main_attr'][$attr] = $max_num - 2;
			}
			$info['id'] = $max_num - 2;
			$post_entity = null;
			$thread_entity = null;
			$count = 0;

			/*need to set the thread and post last*/
			foreach(self::$_env_conf['entity'] as $entity) {
				$thread_entity = $post_entity;
				$post_entity = $entity;
				$count++;
			}
			if (null != $post_entity && null != $thread_entity){
				if ("post" != $post_entity['name'] && "thread" != $thread_entity['thread']) {
					Bingo_Log::warning("find the thread and post entity error, post:".serialize($post_entity). " thread: ".serialize($thread));
					return null;
				}
				
				array_splice(self::$_env_conf[$type], $count - 2, 2);
				$thread_entity['id'] = $max_num - 1;
				$post_entity['id'] = $max_num;	
				self::$_env_conf['main_attr']['thread_id'] = $max_num - 1;
				self::$_env_conf['main_attr']['post_id'] = $max_num;
				
			} else {
				Bingo_Log::warning("can not find the thread and post entity");
				return null;
			}
		}

		self::$_env_conf[$type][] = $info;

		if ('entity' == $type) {
			self::$_env_conf[$type][] = $thread_entity;
			self::$_env_conf[$type][] = $post_entity;
		}

		$ret = self::setConfig(self::$_env_conf);

		if (false === $ret) {
			Bingo_Log::warning("add configure node error.");
			$index = count(self::$_env_conf[$type]) - 1;
			array_splice(self::$_env_conf[$type], $index);
			self::$_env_conf['max_num'][$type]--;
			if ('entity' == $type) {
				foreach($main_attr_value as $attr)
					unset(self::$_env_conf['main_attr'][$attr]);
			}
			return null;
		}
		Bingo_Log::notice("add configure node", "success");
		
		return self::$_env_conf;
	}
	
	public static function delNode($id, $type){
		if (null === $id || $id < 0 || $id >= self::$_env_conf['max_num'][$type]) {
			Bingo_Log::warning("del entity error, id = ".$id);
			return false;
		}
	
		$count = 0;
		foreach (self::$_env_conf[$type] as $value) {
			if (!isset($value['id'])) {
				Bingo_Log::warning("the $type info error, count ".$count);
				return false;
			}
			
			if (intval($value['id']) === intval($id))
				break;
			$count++;
		}
		
		if (null === $value) {
			Bingo_Log::warning("find no $type . id = ".$id);
			return false;
		}
		
		if ('entity' == $type){
			foreach($value['attr']['main_attr'] as $attr) {
				unset(self::$_env_conf['main_attr'][$attr]);
			}
		}

		array_splice(self::$_env_conf[$type], $count, 1);
		
		$ret = self::setConfig(self::$_env_conf);

		if (false === $ret) {
			Bingo_Log::warning("del configure node error.");
			if ('entity' == $type){
				foreach($value['attr']['main_attr'] as $attr) {
					self::$_env_conf['main_attr'][$attr] = $id;
				}
			}
			array_splice(self::$_env_conf[$type], $count, 0,$value);
			return null;
		}
		Bingo_Log::notice("del configure node", "success");
		
		return true;
	}
	
}

?>
