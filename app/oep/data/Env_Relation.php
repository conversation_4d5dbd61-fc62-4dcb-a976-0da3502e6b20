<?php
class Env_Relation {
	private $_no = null;
	private $_name = null;
	private $_attr_class = null;
	private $_main_attr = array();
	private $_key_attr = array();
	private $_entity_info = array();
	private $_is_revert = 0;
	/*entity_info[] =array('entity' => aa, 'conn_attr' => bbb)
	 * */
	
	/*need to judge the in and out of the node when flag = 0*/
	public function __construct($no, $name, $main_attr, $flag, $res_infos = null, $key_attr = null){
		$this->_no = $no;
		$this->_name = $name;
		/*the entity as the relation up node*/
		$chose_attr = null;
		if (0 == $flag) {
			if (null == $key_attr) {
				$chose_info = Common::getUpEntity($main_attr);
				if (null == $chose_info) {
					Bingo_Log::warning("choose the up entity error.name:$name");
					return ;
				}
			} else {
				$chose_info = Common::getEntityIdByAttr($key_attr);
				if (null == $chose_info) {
					Bingo_Log::warning("choose the up entity error.key_attr:$key_attr");
					return ;
				}
				$this->_is_revert = 1;
			}
			$chose_attr = $chose_info['conn_attr'];
			$chose_no = $chose_info['entity'];
			$this->_key_attr = $chose_attr;
			$ret = Common::$global_entity[$chose_no]->inputRelation($this->_no, $chose_info['conn_attr']);
			if (false == $ret) {
				Bingo_Log::warning("choose the up entity error.name:$name");
				return ;
			}
		}
		
/*		foreach($main_attr as $attr){
			$this->_main_attr[] = $attr;
			if ($chose_attr == $attr) continue;
			
			$entity_no = Common::$global_entity_id[$attr];
			$info = array(
					'entity' => $entity_no,
					'conn_attr' => $attr,
			);
			$this->_entity_info[] = $info;
		}
 */		
		if (null != $res_infos) {
			$this->_attr_class = new Env_Attribute($name, $res_infos);
		}
	}
	
	/*input: the data input
	 * $arr_type: the data belong to the attr type
	 * */
	public function execute() {
		if (isset(Common::$global_info['limit_info'])) {
                        $flag = false;
			$modle_limit = false;
                        foreach(Common::$global_info['limit_info'] as $limit_info) {
                                if ("modle" == $limit_info['limit_type']) {
                                        $limit_name = strval($limit_info['limit_value']);
                                        $name = strval($this->_name);
                                        $res = strstr($name, $limit_name);
                                        if (true == $res) {
                                                $flag = true;
                                                break;
                                        }
					$modle_limit = true;
                                }
                        }
                        if (false == $flag && true == $modle_limit) {
                                Bingo_Log::warning("no need to execute relation name:".$this->_name);
                                return true;
                        }
		}	
		if (null != $this->_attr_class) {
			$out_put = $this->_attr_class->execute();
			if (null === $out_put) {
				Bingo_Log::warning("attribute execute error, relation name:".$this->_name);
				return false;
			}
		}
		
		$obj = Bingo_Log::getModule('');
                $obj->flush();  
		/*deal the output that the entity has executed.*/
		if (count($out_put) < 1)
			return true;
		foreach($out_put as $key => $value) {
			if (count($value) < 1)
				continue;
			$entity_info = Common::getEntityIdByAttr($key);
			if (null == $entity_info) {
				Bingo_Log::warning("get entity by attr error, attr=$key");
				continue;
			}
			$no = intval($entity_info['entity']);
			Common::$global_entity[$no]->execute(1, $key, $value);

		}
		/*the relation is the last modle of this execute*/
		/*
		if (1 == intval($step)) {	
			Bingo_Log::notice("the last ralation relation_no= ".$this->_no. " entity_no = $entity_no, input attr = $attr");
			return true;
		}
		$out_put[$arr_type] = $input;
		foreach($this->_entity_info as $info) {
			$entity_no = intval($info['entity']);
			$attr = $info['conn_attr'];
			
			Bingo_Log::notice("begin exectuer entity relation_no= ".$this->_no. " entity_no = $entity_no, input attr = $attr");
			if (isset($out_put[$attr]) && count($out_put[$attr]) > 0) {
				$ret = Common::$global_entity[$entity_no]->execute($out_put[$attr], $attr, $this->_is_revert);
				if (false == $ret) {
					Bingo_Log::warning("entity execute error, relation name:".$this->_name);
					continue;
				}
				Bingo_Log::notice("end execute entity: success no = $entity_no");
			} else {
				Bingo_Log::notice("end execute entity: no need exectuer entity no = $relat_no");
			}
		}
		$obj = Bingo_Log::getModule('');
		$obj->flush();
		 */
		return true;
	}
}

?>
