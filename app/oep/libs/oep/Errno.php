<?php
/**
 * 错误号定义
 * add by mahong
 **/
class Oep_Errno {

	const SUCCESS				= 0;			// 访问成功
	// 通用错误号
	const ACCESS_DENY			= 100001;		// 非法访问
	const NO_LOGIN				= 100002;		// 未登录
	const NO_POWER				= 100003;		// 无权限
	const SYS_ERROR				= 100004;		// 系统错误包括mysql错误
	const PARAM_ERR				= 100007;		// 入参错误
    const CONF_ERROR            = 100008;       // 配置错误
    const DL_CALL_FAIL          = 100009;       // dl错误
    const DB_CONN_ERROR         = 100010;       // db配置错误
    const CALL_SERVICE_ERROR    = 100011;       // service调用错误

	// 模块相关错误号在此添加..
	//pidconf模块相关错误号
	const ENVTYPE_EXIST         = 200001; //envType已存在
	const ORP_MODULE_NOT_EXIST  = 200002; //orp模块不存在
	const RULETYPE_EXIST        = 200003; //ruleType已存在
	const RULE_EXIST            = 200004;// rule已存在
	const UN_ORP_MODULE_FAIL    = 200005; //暂时不支持非orp模块
	const ENVMODULE_EXIST       = 200006; //模块名已存在
	const ENVUPMODULE_EXIST     = 200007;//环境更新模块已存在
	const RAL_RULE_EXIST        = 200008;//ral替换规则已存在
	const RAL_RULE_NOT_EXIST    = 200009;//ral替换规则不存在
	const ENVUPMODULE_NOT_EXIST = 200010;//envupModule不存在
	const RULETYPE_NOT_EXIST    = 200011;//ruleType不存在
	const PATH_NOT_EXIST        = 200012;//rp_addr_path表中不存在该path
	const PORT_NUM_FAIL        = 200013;//获取container端口号失败
	//环境管理
	const GET_USERINFO_FAIL     = 300001;    //获取用户信息失败
	const GET_PROLINE_FAIL      = 300002;    //获取产品线名称失败
	const RENEWENV_LIMITED      = 300004;    //续借次数超过上限
	const NO_PID                = 300005;    //产品线不存在
	const USER_NOT_EXIT         = 300006;    //用户不存在
	const SET_USER_ROLE_FAIL    = 300007;    //设置用户角色失败
	const NO_ENV                = 300008;    //环境不存在
	
	const ENV_NUM_LIMIT 		= 300009;	 //申请环境数达到设定的上限
	const FREE_ENV_EMPTY		= 300010;	 //无空闲的环境
	const NO_ENVTYPE			= 300011;	 //环境类型不存在
	const FREE_ENV_ERROR		= 300012;	 //释放环境失败
	const BIND_ENV_ERROR		= 300013;	 //绑定环境失败
	const HAS_BEEN_APPROVALED	= 300014;	 //角色或产品线已经被审批
	const HAS_NOT_BEEN_APPROVALED	= 300015;	 //角色或产品线未被审批
	const APPROVAL_ROLE_ERROR	= 300016;	 //角色审批失败
	const APPROVAL_PRODUCTLINE_ERROR	= 300017;	 //产品线审批失败
	const INSERT_USER_ERROR		= 300018;	 //新增用户失败
	const LOCK_ENV_ERROR		= 300019;	 //锁定环境失败
	const UNLOCK_ENV_ERROR		= 300019;	 //解锁环境失败
	const UPDATE_REMARK_ERROR		= 300020;	 //更新备注失败
	const TIME_ERROR                =300021; //续借环境时间超时
	const USER_ALREADY_EXIST                =300022; //用户已经存在
	const HAVE_APPLYED             =300023;//用户已申请过角色
	const CANT_APPLY_LOWER_ROLE             =300024;//用户已申请过角色
	const ENV_CANT_UNLOCK            =300025;//环境不能被解锁
	const ENV_CANT_LOCK            =300026;//环境不能被锁定
	const ENV_NOT_VARIABLE           =300027;//环境无效
    //未定义错误
    const NOT_DEFINE_ERROR = 999999;

	//环境更新
	const SOURCE_INFO_ERROR = 400001;	//源环境获取失败
	const MODULE_PATH_ERROR = 400002;	//模块路径获取失败	
	const ENV_DEPLOY_ERROR = 400003;	//环境部署失败
	const USER_ENV_NULL = 400004;		//用户机器列表为空
	const ENV_TYPE_ERROR = 400005;		//环境类型不存在
	const MODULE_NAME_ERROR = 400006;	//模块名错误
	const ENV_REPLACE_ERROR = 400007;	//替换错误

    public static function getErrnoMsg($erron)
    {
        $class = new ReflectionClass('Oep_Errno');
        $constants = $class->getConstants();
        $constName = null;
        foreach ( $constants as $name => $value )
        {
            if ( $value == $erron )
            {
                $constName = $name;
                break;
            }
        }
        return $constName;
    }
}
/* vim: set noexpandtab ts=4 sw=4 sts=4 tw=100: */
