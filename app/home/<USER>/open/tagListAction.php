<?php

/***************************************************************************
 *
 * Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file tagListAction.php
 * <AUTHOR>
 * @date 2021/5/28 2:52 下午
 * @brief 检索标签信息
 **/
class tagListAction extends Util_Base
{
    private $_uid;

    private $_tag; // 标签名

    private $_classId;

    const IS_FUZZY_TRUE = 1;    //是否模糊检索

    const DEFAULT_PN = 1;

    const DEFAULT_RN = 1000;

    public function execute()
    {
        try {
            //初始化
            self::_initParam();

            $arrData = array();
            //获取标签结果
            $arrTagList = $this->_getTagList();
            if (!is_array($arrTagList) || $arrTagList < 0) {
                throw new Util_Exception('call common service false', Tieba_Errcode::ERR_HOME_UI_PARAM);
            }
            $arrData = $arrTagList;

            // 默认成功返回值
            $intErrcode = Tieba_Errcode::ERR_SUCCESS;
            $strErrmsg = $this->_getErrmsg($intErrcode);
            $this->_jsonRet($intErrcode, $strErrmsg, $arrData);

        } catch (Util_Exception $e) {
            $intErrcode = $e->getCode();
            $strErrmsg = $this->_getErrmsg($intErrcode);
            Bingo_Log::warning("errno=" . $intErrcode . " errmsg=" . $strErrmsg);
            $this->_jsonRet($intErrcode, $strErrmsg);
        }
    }

    /**
     * 初始化参数
     */
    private function _initParam()
    {
        //参数获取
        $this->_classId = intval(Bingo_Http_Request::get('class_id'));
        $this->_tag = strval(Bingo_Http_Request::get('tag', ''));
        $this->_uid = Tieba_Session_Socket::getLoginUid();

        if (empty($this->_uid)) {
            throw new Util_Exception('accessed user id is empty ', Tieba_Errcode::ERR_HOME_UI_PARAM);
        } elseif (empty($this->_classId)) {
            throw new Util_Exception('class_id is empty', Tieba_Errcode::ERR_HOME_UI_PARAM);
        }
    }

    /**
     * 获取游戏标签信息
     * @return array|bool
     */
    private function _getTagList()
    {
        $arrInput = array(
            'class_id' => $this->_classId,
            'status' => 1,
            'tag' => $this->_tag,
            'isFuzzy' => self::IS_FUZZY_TRUE,
            'pn' => self::DEFAULT_PN,
            'rn' => self::DEFAULT_RN,
        );
        $arrOutput = Tieba_Service::call('common', 'getTagsListByCond', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false == $arrOutput || !isset($arrOutput['errno']) || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(__METHOD__ . " get tag list error, input=" . json_encode($arrInput) . " and output=" . json_encode($arrOutput));
            return -1;
        }
        return (array)$arrOutput['output'];
    }
}

?>



