<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 16:39:01
 * @LastEditTime: 2021-06-08 14:40:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /home/<USER>/home/<USER>/creative/homeAction.php
 */

require_once(ROOT_PATH . '/app/home/<USER>/work.php');
class publishAction extends Util_Base {
    public function execute() {
        if (Libs_Work::redirectToLogin()) {
            return;
        }

        if (!Libs_Work::checkVideoCreator()) {
            return;
        }
        
        $this->_strTplName = 'index.php';
        Bingo_Page::setTpl($this->_strTplName);
        Bingo_Page::assign('tbs', Tieba_Tbs::gene(Tieba_Session_Socket::isLogin()));
    }
}