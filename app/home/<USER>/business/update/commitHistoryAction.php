<?php

/***************************************************************************
 *
 * Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file commitHistoryAction.php
 * <AUTHOR>
 * @date 2021/10/12 5:02 下午
 * @brief
 **/
require_once ROOT_PATH . '/app/home/<USER>/business.php';
class commitHistoryAction extends Util_Base {
    public function execute() {
        if (Libs_Business::redirectToLogin()) {
            return true;
        }
        $this->_strTplName = 'index.php';
        Bingo_Page::setTpl($this->_strTplName);
        Bingo_Page::assign('tbs', Tieba_Tbs::gene(Tie<PERSON>_Session_Socket::isLogin()));
        return true;
    }
}