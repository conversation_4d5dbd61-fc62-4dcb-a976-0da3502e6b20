<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-14 16:22:16
 * @comment ������Ƭ 
 * @version
 */
class begcardAction extends Util_Base {

    public function execute(){
        try {
            //������ȡ
            self::_initParam();
				
            //��ȡ�����û��ͱ������û�����Ϣ
            self::_getTwoUserInfo();
            $arrCurUser = Util_Dict::get(Util_Dict::CUR_USER_INFO);
			$arrAccessedUser = Util_Dict::get(Util_Dict::ACCESSED_USER_INFO);
            if($arrCurUser['id'] <= 0) {
            	throw new Util_Exception("cur user id <= 0, maybe no login", Tieba_Errcode::ERR_USER_NOT_LOGIN);
            }
            
            //������Ƭ
            Data_Home::exchangeCard($arrCurUser['id'], $arrAccessedUser['user_id'], true);

            //�����������ʼ��ΪĬ��ֵ�������޸�
            $arrData = array();

            // Ĭ�ϳɹ�����ֵ
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,Tieba_Error::getUserMsg(Tieba_Errcode::ERR_SUCCESS),$arrData);
        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." errmsg=".$e->getMessage());
            $this->_jsonRet($e->getCode(), $e->getComErrMsg());
        }
    }
    
    private function _initParam() {
    	//������ȡ
        $strTbs = strval(Bingo_Http_Request::get('tbs',''));
        $strUname = self::_getUname();
	    $strHomeId = strval(Bingo_Http_Request::get('id',''));	    
	    
        //tbs���
        self::_tbsCheck($strTbs);
            
        //���û��un������ȥid����
        if('' === $strUname) {
	        //$strHomeId = Tieba_Ucrypt::encode(1024,'');
	        $intAccessedUserId = intval(Tieba_Ucrypt::decode($strHomeId));
		    if($intAccessedUserId <= 0) {
		    	throw new Util_Exception('accessed user id <= 0', Tieba_Errcode::ERR_HOME_UI_PARAM);
		    }
		    Util_Dict::set(Util_Dict::ACCESSED_USER_ID, $intAccessedUserId);
        }
        else {
		    Util_Dict::set(Util_Dict::ACCESSED_USER_NAME, $strUname);
        }
    }
}
?>