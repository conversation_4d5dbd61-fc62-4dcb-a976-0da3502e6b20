

service channelproxy {

	//单播消息推送，异步接口
    commit void pushMessageByUid
    (
        uint32_t user_id,//用户id
        string channel_id,//channel id
        string channel_message_key = optional(),//消息key，如果不传，会默认分配一个
        string channel_message_ext//消息携带的数据，客户端自己解析
    );
    
    //单播消息推送，同步接口
    commit void pushMessageByUidSync
    (
        uint32_t user_id,//用户id
        string channel_id,//channel id
        string channel_message_key = optional(),//消息key，如果不传，会默认分配一个
        string channel_message_ext//消息携带的数据，客户端自己解析
    );
    
    //广播消息推送，同步接口
    commit void broadcastMessage
    (
        string channel_message_key = optional(),//消息key，如果不传，会默认分配一个
        string channel_message_ext//消息携带的数据，客户端自己解析
    );
    
};