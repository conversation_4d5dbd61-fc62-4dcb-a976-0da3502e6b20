<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013:05:21 18:55:41
 * @version 
 * @structs & methods(copied from idl.)
*/



define("MODULE","userclient_dl");
class Dl_Userclient_Userclient{

const SERVICE_NAME = "Dl_Userclient_Userclient";
const CACHE_KEY_IOS = "message_user_client";
const CACHE_KEY_ANDROID = "message_user_client_android";
const CACHE_KEY_IMEI_ANDROID = "message_imei_client_android";
const CACHE_EXPIRE_TIME = 3600;//cache失效时间：一小时
const PID='forum';
const APP='gpush';
const TK='forum';
const REDIS_USER_KEY = 'push_count_user_id_';
protected static $_cache = null;
protected static $_redis = null;
protected static $_conf = null;
protected static $_db = null;
protected static $_use_split_db = false;
const DB_RAL_SERVICE_NAME = "db_message";

/**
 * @brief get mysql obj.
 * @return: obj of Bd_DB, or null if connect fail.

**/		
private static function _getDB(){
    if(self::$_db){
        return self::$_db ;
    }
    self::$_db = new Bd_DB();
    if(self::$_db == null){
        Bingo_Log::warning("new bd_db fail.");
        return null;
    }
    if(self::$_use_split_db){
        $splitDBConfPath = ROOT_PATH."/conf/db/";
        $splitDBConfFile = "db_dl_userclient_userclient.conf";
        $r = self::$_db -> enableSplitDB(self::DB_RAL_SERVICE_NAME, $splitDBConfPath, $splitDBConfFile);
        if(!$r){
            Bingo_Log::warning("enable splitdb fail.");
            self::$_db = null;
            return null;
        }
        return self::$_db;
    }else{
    	Bingo_Timer::start('dbinit');
        $r = self::$_db->ralConnect(self::DB_RAL_SERVICE_NAME);
        Bingo_Timer::end('dbinit');
        if(!$r){
            Bingo_Log::warning("bd db ral connect fail.");
            self::$_db = null;
            return null;
       }
       return self::$_db;

    }
    return null;
}

/**
 * @brief get cache obj.
 * @return: obj of Bingo_Cache_Memcached, or null if connect fail.

**/	
private static function _getCache(){
	if(self::$_cache){
		return self::$_cache ;
	}
	Bingo_Timer::start('cacheinit');
	
	//$cacheConf = Bd_Conf::getConf('cache/cache_dl_userclient_userclient');
	//if(!$cacheConf){
	//	Bingo_Log::warning("get cache config fail.[cache/cache_dl_userclient_userclient]");
	//	return null;
	//}
	//self::$_cache = new Bingo_Cache_Memcached(array(
	//			'conf' => $cacheConf,	
	//	)); 
	self::$_cache = new Bingo_Cache_Memcached('forum_message_token');
	
	Bingo_Timer::end('cacheinit');	
	
	if(!self::$_cache || !self::$_cache->isEnable()){
		Bingo_Log::warning("init cache fail.");
		self::$_cache = null;
		return null;
	}
	return self::$_cache;
}

private static function _getRedis(){
	if(self::$_redis){
		return self::$_redis;
	}
	Bingo_Timer::start('redisinit');
	
	$arrInput = array(
		'pid'=>self::PID, 
		'app'=>self::APP, 
		'tk'=>self::TK
	);
	self::$_redis = Bd_RalRpc::create('Ak_Service_Redis', $arrInput);
	
	Bingo_Timer::end('cacheinit');	
	
	if(!self::$_redis){
		Bingo_Log::warning("init redis fail.");
		self::$_redis = null;
		return null;
	}
	return self::$_redis;
}

private static function _add2Cache($strKey, $mixValue, $intLifeTime = 0) {
	if (is_null(self::_getCache())) {
		Bingo_Log::warning ('init cache fail');
		return false;
	}
	$ret = self::$_cache->add ( strval ( $strKey ), $mixValue, $intLifeTime );
	if ($ret == 0) {
		return true;
	} else {
		Bingo_Log::warning ( 'add cache err no : ' . $ret );
		return false;
	}
}

//获取失败返回NULL
private static function _getFromCache($strKey) {
	if (is_null(self::_getCache())) {
		Bingo_Log::warning ('init cache fail');
		return false;
	}
	$ret = self::$_cache->get(strval($strKey));
	return $ret;
}

private static function _mgetFromCache($keys) {
	if (is_null(self::_getCache())) {
		Bingo_Log::warning ('init cache fail');
		return false;
	}
	$ret = self::$_cache->multipleGet($keys);
	return $ret;
}
/*private static function _msetToCache($keys) {
	if (is_null(self::_getCache())) {
		Bingo_Log::warning ('init cache fail');
		return false;
	}
	$ret = self::$_cache->multipleSet($keys);
	return $ret;
}*/
private static function _msetToCache($arrKeyValue) {
	if (is_null(self::_getCache())) {
		Bingo_Log::warning ('init cache fail');
		return false;
	}
    foreach($arrKeyValue as $key => $value){
        $res = self::_add2Cache($key, $value, self::CACHE_EXPIRE_TIME);
    }
    return true;    
}
private static function _removeFromCache($strKey) {
	//Bingo_Log::warning ('clear cache input : '.var_export($strKey,true));
	if (is_null(self::_getCache())) {
		Bingo_Log::warning ('init cache fail');
		return false;
	}
	$ret = self::$_cache->remove(strval($strKey));
	if ($ret == 0) {
		return true;
	} else {
		Bingo_Log::warning ( 'remove cache err no : ' . $ret );
		return false;
	}
}
	
/**
 * @brief init
 * @return: true if success. false if fail.

**/		
private static function _init(){
	
	//add init code here. init will be called at every public function beginning.
	//not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
	//init should be recalled for many times.
	
	if(self::$_conf == null){	
		self::$_conf = Bd_Conf::getConf("/app/message/dl_userclient_userclient");
		if(self::$_conf == false){
			Bingo_Log::warning("init get conf fail.");
			return false;
		}
		
	}
	return true; 
}


private static function _errRet($errno){
    return array(
        'errno' => $errno,
        'errmsg' => Tieba_Error::getErrmsg($errno),
    );
}

public static function preCall($arrInput){
    // pre-call hook
}

public static function postCall($arrInput){
    // post-call hook
}


/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32_t terminal_type
 * 	uint32_t valid_record_num = optional()
 * @return: $arrOutput
**/
public static function getClientsByUserId($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['terminal_type'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
    $user_id = intval($arrInput['user_id']);
    $terminal_type = $arrInput['terminal_type'];
    
    if(isset($arrInput['valid_record_num'])){
	    $valid_record_num = intval($arrInput['valid_record_num']);
    }

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	
	//output params.
	
	//your code here......
	if(1 == self::$_conf['need_cache']){
		//先从cache里取数据
		$cache_key_prefix = '';		
		if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS) {
			$cache_key_prefix = self::CACHE_KEY_IOS;				
		} else {
			$cache_key_prefix = self::CACHE_KEY_ANDROID;				
		}
		$strKey = $cache_key_prefix."_".strval($user_id);
		$value = self::_getFromCache($strKey);
		if($value !== NULL && $value !== false){
			//hit cache
			Bingo_Log::debug("get cache success, key: ".var_export($strKey,true).",value: ".var_export($value,true));
			$value = unserialize($value);
			$error = Tieba_Errcode::ERR_SUCCESS;
			$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'client' => $value,
			);
			return $arrOutput;	
		}
		Bingo_Log::warning("get cache fail, key: ".var_export($strKey,true).",value: ".var_export($value,true));
	}
	
	if (!self::_getDB()){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	
	$client = array();
    $objUserClient = new Dl_Userclient_Db_Userclient(self::$_db);
    if(isset($valid_record_num)){
        $client = $objUserClient->getClientsByUserId($user_id, $terminal_type, $valid_record_num);
    }
    else{
        $client = $objUserClient->getClientsByUserId($user_id, $terminal_type);
    }
    if(false === $client){
        Bingo_Log::warning("getClientsByUserId return false");
        return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);      
    }

    if(1 == self::$_conf['need_cache']){
    	$cache_key_prefix = '';
    	if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS) {
    		$cache_key_prefix = self::CACHE_KEY_IOS;
    	} else {
    		$cache_key_prefix = self::CACHE_KEY_ANDROID;
    	}
    	$strKey = $cache_key_prefix."_".strval($user_id);    	 
    	$value = serialize($client);
		$res = self::_add2Cache($strKey, $value, self::CACHE_EXPIRE_TIME);
		if($res === true){
			Bingo_Log::debug("add cache success, key: ".var_export($strKey,true).",value: ".var_export($value,true));
		}else{
			Bingo_Log::warning("add cache fail, key: ".var_export($strKey,true).",value: ".var_export($value,true));
		}
    }
    
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'client' => $client,
	);
	return $arrOutput;
}

public static function mgetClientsByUserId($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id_arr']) || !is_array($arrInput['user_id_arr']) || count($arrInput['user_id_arr']) === 0 
		|| !isset($arrInput['terminal_type'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	if(isset($arrInput['valid_record_num'])){
	        $valid_record_num = intval($arrInput['valid_record_num']);
	}

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	
	//output params.

	$trueResult = array();
	$userIdArr = $arrInput['user_id_arr'];
	$terminal_type = $arrInput['terminal_type'];
	$misIdArr = array();
	$cache_key_prefix = '';		
	if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS) {
		$cache_key_prefix = self::CACHE_KEY_IOS;				
	} else if ($terminal_type == Service_Lib_UserMessage::TERMINAL_ANDROID) {
		$cache_key_prefix = self::CACHE_KEY_ANDROID;				
	}
	if(1 == self::$_conf['need_cache']){
		//先从cache里取数据
	    foreach ($userIdArr as $user_id) {
			$strKeys[] = $cache_key_prefix."_".strval($user_id);
	    }
	    $ret = self::_mgetFromCache($strKeys);
	    if ($ret === NULL || $ret === false) {
			Bingo_Log::warning("get cache fail, key: ".var_export($strKeys,true).",value: ".var_export($ret,true));
			$misIdArr = $userIdArr;
	    } else {
			foreach ($userIdArr as $user_id) {
			    $value = $ret[$cache_key_prefix."_".strval($user_id)];
			    if ($value === NULL) {
					$misIdArr[] = $user_id;
			    } else {
					Bingo_Log::debug("get cache success: key: ".$cache_key_prefix."_".strval($user_id)." value: ".var_export($value, true));
					$trueResult[$user_id] = unserialize($value);
			    }
			}
	    }
	} else {
	    $misIdArr = $userIdArr;
	}
	
	if (count($misIdArr) === 0) {
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
				'client' => $trueResult,
		);
		return $arrOutput;
	}
	
	if (!self::_getDB()){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	//your code here......
	$objUserClient = new Dl_Userclient_Db_Userclient(self::$_db);
	if(isset($valid_record_num)){
	    $result = $objUserClient->mgetClientsByUserId($misIdArr, $terminal_type, $valid_record_num);
	} else{
	    $result = $objUserClient->mgetClientsByUserId($misIdArr, $terminal_type);
	}
	if(false === $result){
	    Bingo_Log::warning("mgetClientsByUserId return false");
	    return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);      
	}
	
	$items = array();
	if(1 == self::$_conf['need_cache']){
	    $hasItems = false;
	    foreach ($misIdArr as $user_id) {
		if (isset($result[$user_id])) {
		    $strKey = $cache_key_prefix."_".strval($user_id);
	       	    $value = serialize($result[$user_id]);
	       	    $items[$strKey] = $value;
		    $trueResult[$user_id] = $result[$user_id];
		    $hasItems = true;
		} else {
		    $trueResult[$user_id] = null;
		}
	    }
	    if ($hasItems) {
		$ret = self::_msetToCache($items);
	    	if ($ret === false) {
	    	    Bingo_Log::warning("mset to cache fail, key: ".var_export($items, true).", ret: ".var_export($ret, true));
	    	} else {
	    	    Bingo_Log::debug("mset to cache success, key: ".var_export($items, true).", ret: ".var_export($ret, true));
	    	}
	    }
	} else {
	    foreach ($misIdArr as $user_id) {
		if (isset($result[$user_id])) {
	       	    $trueResult[$user_id] = $result[$user_id];
		} else {
		    $trueResult[$user_id] = null;
		}
	    }
	}
    
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'client' => $trueResult,
	);
	return $arrOutput;
}

/**
 * @brief
 * @arrInput:
 * 	string imei
 * 	uint32_t terminal_type
 * 	uint32_t valid_record_num = optional()
 * @return: $arrOutput
 **/
public static function getClientsByImei($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['imei']) || !isset($arrInput['terminal_type']) 
		|| $arrInput['terminal_type'] != Service_Lib_UserMessage::TERMINAL_ANDROID){
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}

	//input params.
	$imei = $arrInput['imei'];
	$terminal_type = $arrInput['terminal_type'];

	if(isset($arrInput['valid_record_num'])){
		$valid_record_num = intval($arrInput['valid_record_num']);
	}

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}

	//output params.

	//your code here......	
	if(1 == self::$_conf['need_cache']){
		//先从cache里取数据
		$strKey = self::CACHE_KEY_IMEI_ANDROID."_".strval($imei);
		$value = self::_getFromCache($strKey);
		if($value !== NULL && $value !== false){
			//hit cache
			Bingo_Log::debug("get cache success, key: ".var_export($strKey,true).",value: ".var_export($value,true));
			$value = unserialize($value);
			$error = Tieba_Errcode::ERR_SUCCESS;
			$arrOutput = array(
					'errno' => $error,
					'errmsg' => Tieba_Error::getErrmsg($error),
					'client' => $value,
			);
			return $arrOutput;
		}
		Bingo_Log::warning("get cache fail, key: ".var_export($strKey,true).",value: ".var_export($value,true));
	}

	if (!self::_getDB()){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}

	$client = array();
	$objUserClient = new Dl_Userclient_Db_Userclient(self::$_db);
	if(isset($valid_record_num)){
		$client = $objUserClient->getClientsByImei($imei, $terminal_type, $valid_record_num);
	}
	else{
		$client = $objUserClient->getClientsByImei($imei, $terminal_type);
	}
	if(false === $client){
		Bingo_Log::warning("getClientsByImei return false");
		return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
	}

	if(1 == self::$_conf['need_cache']){
		$cache_key_prefix = '';
		$strKey = self::CACHE_EXPIRE_TIME."_".strval($imei);
		$value = serialize($client);
		$res = self::_add2Cache($strKey, $value, self::CACHE_EXPIRE_TIME);
		if($res === true){
			Bingo_Log::debug("add cache success, key: ".var_export($strKey,true).",value: ".var_export($value,true));
		}else{
			Bingo_Log::warning("add cache fail, key: ".var_export($strKey,true).",value: ".var_export($value,true));
		}
	}

	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
			'client' => $client,
	);
	return $arrOutput;
}

public static function mgetClientsByImei($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if (!isset($arrInput['imei_arr']) || !is_array($arrInput['imei_arr']) || count($arrInput['imei_arr']) === 0
		|| !isset($arrInput['terminal_type']) || $arrInput['terminal_type'] != Service_Lib_UserMessage::TERMINAL_ANDROID){
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}

	//input params.
	if(isset($arrInput['valid_record_num'])){
		$valid_record_num = intval($arrInput['valid_record_num']);
	}

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}

	//output params.

	$trueResult = array();
	$arrImei = $arrInput['imei_arr'];
	$terminal_type = $arrInput['terminal_type'];
	$arrImeiMisCache = array();

	if(1 == self::$_conf['need_cache']){
		//先从cache里取数据
		foreach ($arrImei as $imei) {
			$strKeys[] = self::CACHE_KEY_IMEI_ANDROID."_".strval($imei);
		}
		$ret = self::_mgetFromCache($strKeys);
		if ($ret === NULL || $ret === false) {
			Bingo_Log::warning("get cache fail, key: ".var_export($strKeys,true).",value: ".var_export($ret,true));
			$arrImeiMisCache = $arrImei;
		} else {
			foreach ($arrImei as $imei) {
				$value = $ret[self::CACHE_KEY_IMEI_ANDROID."_".strval($imei)];
				if ($value === NULL) {
					$arrImeiMisCache[] = $imei;
				} else {
					Bingo_Log::debug("get cache success: key: ".self::CACHE_KEY_IMEI_ANDROID."_".strval($imei)." value: ".var_export($value, true));
					$trueResult[$imei] = unserialize($value);
				}
			}
		}
	} else {
		$arrImeiMisCache = $arrImei;
	}
	
	if (count($arrImeiMisCache) === 0) {
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
			'client' => $trueResult,
		);
		return $arrOutput;
	}

	if (!self::_getDB()){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	//your code here......
	$objUserClient = new Dl_Userclient_Db_Userclient(self::$_db);
	if(isset($valid_record_num)){
		$result = $objUserClient->mgetClientsByImei($arrImeiMisCache, $terminal_type, $valid_record_num);
	} else{
		$result = $objUserClient->mgetClientsByImei($arrImeiMisCache, $terminal_type);
	}
	if(false === $result){
		Bingo_Log::warning("mgetClientsByImei return false");
		return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
	}

	$setCacheItems = array();
	if(1 == self::$_conf['need_cache']){
		$needSetCache = false;
		foreach ($arrImeiMisCache as $imei) {
			if (isset($result[$imei])) {
				$strKey = self::CACHE_KEY_IMEI_ANDROID."_".strval($imei);
				$value = serialize($result[$imei]);
				$setCacheItems[$strKey] = $value;
				$trueResult[$imei] = $result[$imei];
				$needSetCache = true;
			} else {
				$trueResult[$imei] = null;
			}
		}
		if ($needSetCache) {
			$ret = self::_msetToCache($setCacheItems);
			if ($ret === false) {
				Bingo_Log::warning("mset to cache fail, key: ".var_export($setCacheItems, true).", ret: ".var_export($ret, true));
			} else {
				Bingo_Log::debug("mset to cache success, key: ".var_export($setCacheItems, true).", ret: ".var_export($ret, true));
			}
		}
	} else {
		foreach ($arrImeiMisCache as $imei) {
			if (isset($result[$imei])) {
				$trueResult[$imei] = $result[$imei];
			} else {
				$trueResult[$imei] = null;
			}
		}
	}

	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'client' => $trueResult,
	);
	return $arrOutput;
}

/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32_t terminal_type
 * 	string token = optional()
 * 	string channel_id = optional()
 * 	string version
 * 	string imei
 * 	string from_id = optional()
 * @return: $arrOutput
**/
public static function loginUser($arrInput){

	// input params check;
    // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['terminal_type']) || !isset($arrInput['version']) 
		|| !isset($arrInput['imei'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
	
	$terminal_type = $arrInput['terminal_type'];
	
	if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS && !isset($arrInput['token'])) {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
	
	if ($terminal_type == Service_Lib_UserMessage::TERMINAL_ANDROID && (!isset($arrInput['channel_id']) || !isset($arrInput['from_id']))) {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}
			
	//input params.
	$user_id = intval($arrInput['user_id']);
	$version = $arrInput['version'];
	$imei = $arrInput['imei'];
	
	if (isset($arrInput['token'])) {
		$token = $arrInput['token'];
	}
	if (isset($arrInput['channel_id'])) {
		$channel_id = $arrInput['channel_id'];
	}
	if (isset($arrInput['from_id'])) {
		$from_id = $arrInput['from_id'];
	}
	
    if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	
	//output params.
	$status = false;
    $error = Tieba_Errcode::ERR_SUCCESS;

	//your code here......
	if(1 == self::$_conf['need_cache']){
		//查询原来的user-token对应关系
		$result = false;
		$cache_key_prefix = '';
		if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS) {
			$result = self::getClientByToken(array('token' => $token));
			$cache_key_prefix = self::CACHE_KEY_IOS;
		} else {
			$result = self::getClientByChannelId(array('channel_id' => $channel_id));
			$cache_key_prefix = self::CACHE_KEY_ANDROID;
		}
		if($result['errno'] == Tieba_Errcode::ERR_SUCCESS && isset($result['user_id'])){
			//清除cache
			$strKey = $cache_key_prefix."_".strval($result['user_id']);
			$res = self::_removeFromCache($strKey);
			if(true == $res){
				Bingo_Log::debug("clear cache success, key : ".var_export($strKey,true));
			}
			else{
				Bingo_Log::warning("clear cache fail, key : ".var_export($strKey,true));
			}
		}
	}
	
	if (!self::_getDB()){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
    
	$objUserClient = new Dl_Userclient_Db_Userclient(self::$_db);
	$status = $objUserClient->loginUser($user_id, $terminal_type, $token, $channel_id, $version, $imei, $from_id);
    if(false === $status || -1 == $status){
		$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
    }
    else if(-2 == $status){
		$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
    }
    else {
    	if (1 == self::$_conf['need_cache']) {
    		//清除cache
    		$cache_key_prefix = '';
    		if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS) {
    			$cache_key_prefix = self::CACHE_KEY_IOS;
    		} else {
    			$cache_key_prefix = self::CACHE_KEY_ANDROID;
    		}
    		
    		$strKey = $cache_key_prefix."_".strval($user_id);
    		$res = self::_removeFromCache($strKey);
    		if(true == $res){
    			Bingo_Log::debug("clear cache success, key : ".var_export($strKey,true));
    		}
    		else{
    			Bingo_Log::warning("clear cache fail, key : ".var_export($strKey,true));
    		}
    	}
    }

    $arrOutput = array(
        'errno' => $error,
        'errmsg' => Tieba_Error::getErrmsg($error),
        'status' => $status,
    );  
    return $arrOutput;
}

/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32_t terminal_type
 * 	string token = optional()
 * 	string channel_id = optional()
 * 	string version
 * @return: $arrOutput
**/
public static function logoutUser($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['terminal_type']) || !isset($arrInput['version'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }
    
    $terminal_type = $arrInput['terminal_type'];
    
    if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS && !isset($arrInput['token'])) {
    	Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
    	return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
    }
    
    if ($terminal_type == Service_Lib_UserMessage::TERMINAL_ANDROID && !isset($arrInput['channel_id'])) {
    	Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
    	return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
    }
    
	//input params.
	$user_id = intval($arrInput['user_id']);
	$version = $arrInput['version'];
	
	if (isset($arrInput['token'])) {
		$token = $arrInput['token'];
	}
	if (isset($arrInput['channel_id'])) {
		$channel_id = $arrInput['channel_id'];
	}

    if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
    if (!self::_getDB()){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	//output params.
	$status = false;
    $error = Tieba_Errcode::ERR_SUCCESS;

	//your code here......
    
    $objUserClient = new Dl_Userclient_Db_Userclient(self::$_db);
    $status = $objUserClient->logoutUser($user_id, $terminal_type, $token, $channel_id, $version);
    if(-1 == $status){ //登出失败，需要重试
        $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
    }
    else if(-2 == $status){ //登出失败，不需要重试
        $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
    }
    else {
    	if(1 == self::$_conf['need_cache']){
    		//清除cache
    		$cache_key_prefix = '';
    		if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS) {
    			$cache_key_prefix = self::CACHE_KEY_IOS;
    		} else {
    			$cache_key_prefix = self::CACHE_KEY_ANDROID;
    		}    		
    		
			$strKey = $cache_key_prefix."_".strval($user_id);
			$res = self::_removeFromCache($strKey);
			if(true == $res){
				Bingo_Log::debug("clear cache success, key : ".var_export($strKey,true));
			}
			else{
				Bingo_Log::warning("clear cache fail, key : ".var_export($strKey,true));
			}
		}
    }

    $arrOutput = array(
        'errno' => $error,
        'errmsg' => Tieba_Error::getErrmsg($error),
        'status' => $status,
    );
    return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32_t terminal_type
 * 	string token = optional()
 * 	string channel_id = optional()
 * 	string version
 * @return: $arrOutput
**/
public static function nmqLogoutUser_old($arrInput){
	
	$arrInput = Tieba_Service::getArrayParams($arrInput, 'userclient');

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['terminal_type']) || !isset($arrInput['version'])){
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}
	
	$terminal_type = $arrInput['terminal_type'];
	
	if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS && !isset($arrInput['token'])) {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}
	
	if ($terminal_type == Service_Lib_UserMessage::TERMINAL_ANDROID && !isset($arrInput['channel_id'])) {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}
	
	//input params.
	$user_id = intval($arrInput['user_id']);
	$version = $arrInput['version'];
	
	if (isset($arrInput['token'])) {
		$token = $arrInput['token'];
	}
	if (isset($arrInput['channel_id'])) {
		$channel_id = $arrInput['channel_id'];
	}
	
	if(!self::_init()){
		header("HTTP/1.0 404 Not Found");
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	if (!self::_getDB()){
		header("HTTP/1.0 404 Not Found");
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	//output params.
	$status = false;
	$error = Tieba_Errcode::ERR_SUCCESS;
	
	//your code here......
	
	$objUserClient = new Dl_Userclient_Db_Userclient(self::$_db);
	$status = $objUserClient->logoutUser($user_id, $terminal_type, $token, $channel_id, $version);
	if(-1 == $status){ //登出失败，需要重试
		header("HTTP/1.0 404 Not Found");
		$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
	}
	else if(-2 == $status){ //登出失败，不需要重试
		$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
	}
	else {
		if(1 == self::$_conf['need_cache']){
			//清除cache
			$cache_key_prefix = '';
			if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS) {
				$cache_key_prefix = self::CACHE_KEY_IOS;
			} else {
				$cache_key_prefix = self::CACHE_KEY_ANDROID;
			}
	
			$strKey = $cache_key_prefix."_".strval($user_id);
			$res = self::_removeFromCache($strKey);
			if(true == $res){
				Bingo_Log::debug("clear cache success, key : ".var_export($strKey,true));
			}
			else{
				Bingo_Log::warning("clear cache fail, key : ".var_export($strKey,true));
			}
		}
	}
	
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
			'status' => $status,
	);
	return $arrOutput;
}

/**
 * @brief
 * @arrInput:
 * 	string token
 * @return: $arrOutput
 * 	uint32_t user_id
**/
private function getClientByToken($arrInput){
	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['token'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }

	//input params.
	$token = $arrInput['token'];

    if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
    if (!self::_getDB()){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	//output params.

	//your code here......
    $objUserClient = new Dl_Userclient_Db_Userclient(self::$_db);
    $client = $objUserClient->getClientBytoken($token);
    if(false == $client || empty($client)){
        return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
    }
    
    $error = Tieba_Errcode::ERR_SUCCESS;
    $arrOutput = array(
        'errno' => $error,
        'errmsg' => Tieba_Error::getErrmsg($error),
        'user_id' => $client[0]['user_id'],
        'version' => $client[0]['version'],
        'imei'	  => $client[0]['imei'],
    );
    return $arrOutput;
}

/**
 * @brief
 * @arrInput:
 * 	string token
 * @return: $arrOutput
 * 	uint32_t user_id
 **/
private function getClientByChannelId($arrInput){
	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['channel_id'])){
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}

	//input params.
	$channel_id = $arrInput['channel_id'];

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	if (!self::_getDB()){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	//output params.

	//your code here......
	$objUserClient = new Dl_Userclient_Db_Userclient(self::$_db);
	$client = $objUserClient->getClientByChannelId($channel_id);
	if(false == $client || empty($client)){
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);		
	}
	
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
			'user_id' => $client[0]['user_id'],
			'version' => $client[0]['version'],
			'imei' => $client[0]['imei'],
	        'channel_uid' => $client[0]['channel_uid'],
	);
	return $arrOutput;
}

/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32_t terminal_type
 * 	string token = optional()
 * 	string channel_id = optional()
 *  string channel_uid = optional()
 * 	string version
 * 	string imei
 * 	string from_id = optional()
 * @return: $arrOutput
 **/
public static function registerClient($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['terminal_type']) || !isset($arrInput['version'])
	|| !isset($arrInput['imei'])) {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}

	$terminal_type = $arrInput['terminal_type'];

	if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS && !isset($arrInput['token'])) {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}
	
	//ios必须传有效user_id
	if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS && intval($arrInput['user_id']) === 0) {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}

	if ($terminal_type == Service_Lib_UserMessage::TERMINAL_ANDROID && (!isset($arrInput['channel_id']) || !isset($arrInput['from_id']))) {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);
	$version = $arrInput['version'];
	$imei = $arrInput['imei'];

	if (isset($arrInput['token'])) {
		$token = $arrInput['token'];
	}
	if (isset($arrInput['channel_id'])) {
		$channel_id = $arrInput['channel_id'];
	}
	if (isset($arrInput['channel_uid'])) {
		$channel_uid = $arrInput['channel_uid'];
	} else {
		$channel_uid = 0;
	}
	if (isset($arrInput['from_id'])) {
		$from_id = $arrInput['from_id'];
	}

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}

	//output params.
	$status = false;
	$error = Tieba_Errcode::ERR_SUCCESS;

	//your code here......
	if(1 == self::$_conf['need_cache']){
		//查询原用户的user-client对应关系
		$result = false;
		$cache_key_prefix = '';
		if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS) {
			$result = self::getClientByToken(array('token' => $token));
			$cache_key_prefix = self::CACHE_KEY_IOS;
		} else {
			$result = self::getClientByChannelId(array('channel_id' => $channel_id));
			$cache_key_prefix = self::CACHE_KEY_ANDROID;
		}
		if($result['errno'] == Tieba_Errcode::ERR_SUCCESS ){
		    $noNeedUpdate = false;
	        if ($terminal_type == Service_Lib_UserMessage::TERMINAL_ANDROID) {
    		    if ($result['user_id'] == $user_id && $result['imei'] == $imei && $result['version'] == $version && $result['channel_uid'] == $channel_uid) {
    		        $noNeedUpdate = true;
    		    }
	        }
	        else {
    		    if ($result['user_id'] == $user_id && $result['imei'] == $imei && $result['version'] == $version) {
    		        $noNeedUpdate = true;
    		    }
	        }
	        if ($noNeedUpdate) {
		        Bingo_Log::notice("all info don't change! no need to update to db!!!" . "input:".serialize($arrInput) . "| ret:".serialize($result));
		        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	        }

			if (isset($result['user_id']) && $result['user_id'] != 0) {
				//清除原用户的user-client cache
				$strKey = $cache_key_prefix."_".strval($result['user_id']);
				$res = self::_removeFromCache($strKey);
				if(true == $res){
					Bingo_Log::debug("clear cache success, key : ".var_export($strKey,true));
				}
				else{
					Bingo_Log::warning("clear cache fail, key : ".var_export($strKey,true));
				}
			}
		}
	}

	if (!self::_getDB()){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}

	$objUserClient = new Dl_Userclient_Db_Userclient(self::$_db);
	$status = $objUserClient->registerClient($terminal_type, $token, $channel_id, $channel_uid, $user_id, $imei, $version, $from_id);
	if(false === $status || -1 == $status){
		$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
	}
	else if(-2 == $status){
		$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
	}
	else {
		if (1 == self::$_conf['need_cache']) {
			if ($user_id != 0) {
				//清除新用户的user->client cache
				$cache_key_prefix = '';
				if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS) {
					$cache_key_prefix = self::CACHE_KEY_IOS;
				} else {
					$cache_key_prefix = self::CACHE_KEY_ANDROID;
				}
				
				$strKey = $cache_key_prefix."_".strval($user_id);
				$res = self::_removeFromCache($strKey);
				if(true == $res){
					Bingo_Log::debug("clear cache success, key : ".var_export($strKey,true));
				}
				else{
					Bingo_Log::warning("clear cache fail, key : ".var_export($strKey,true));
				}
			}
			
			///////////////
			if ($terminal_type == Service_Lib_UserMessage::TERMINAL_ANDROID) {
				//清除imei->client cache
				$strKey = self::CACHE_KEY_IMEI_ANDROID."_".$imei;
				$res = self::_removeFromCache($strKey);
				if(true == $res){
					Bingo_Log::debug("clear cache success, key : ".var_export($strKey,true));
				}
				else{
					Bingo_Log::warning("clear cache fail, key : ".var_export($strKey,true));
				}
			}
			///////////////////	
		}
	}

	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
			'status' => $status,
	);
	return $arrOutput;
}

/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32_t terminal_type
 * 	string token = optional()
 * 	string channel_id = optional()
 *  string channel_uid = optional()
 * 	string version
 * @return: $arrOutput
 **/
public static function logoutClient($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['terminal_type']) || !isset($arrInput['version'])){
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}

	$terminal_type = $arrInput['terminal_type'];

	if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS && !isset($arrInput['token'])) {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}

	if ($terminal_type == Service_Lib_UserMessage::TERMINAL_ANDROID && !isset($arrInput['channel_id'])) {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}

	//input params.
	$user_id = intval($arrInput['user_id']);
	$version = $arrInput['version'];

	if (isset($arrInput['token'])) {
		$token = $arrInput['token'];
	}
	if (isset($arrInput['channel_id'])) {
		$channel_id = $arrInput['channel_id'];
	}
	if (isset($arrInput['channel_uid'])) {
		$channel_uid = $arrInput['channel_uid'];
	} else {
		$channel_uid = 0;
	}
	
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	if (!self::_getDB()){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	//output params.
	$status = false;
	$error = Tieba_Errcode::ERR_SUCCESS;

	//your code here......

	$objUserClient = new Dl_Userclient_Db_Userclient(self::$_db);
	$status = $objUserClient->logoutClient($terminal_type, $token, $channel_id, $channel_uid, $user_id, $version);
	if(-1 == $status){ //登出失败，需要重试
		$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
	}
	else if(-2 == $status){ //登出失败，不需要重试
		$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
	}
	else {
		if(1 == self::$_conf['need_cache']){
			if ($user_id != 0) {
				//清除user-client cache
				$cache_key_prefix = '';
				if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS) {
					$cache_key_prefix = self::CACHE_KEY_IOS;
				} else {
					$cache_key_prefix = self::CACHE_KEY_ANDROID;
				}
				
				$strKey = $cache_key_prefix."_".strval($user_id);
				$res = self::_removeFromCache($strKey);
				if(true == $res){
					Bingo_Log::debug("clear cache success, key : ".var_export($strKey,true));
				}
				else{
					Bingo_Log::warning("clear cache fail, key : ".var_export($strKey,true));
				}
			}
			
			///////////////
			if ($terminal_type == Service_Lib_UserMessage::TERMINAL_ANDROID) {
				//清除imei->client cache
				$result = self::getClientByChannelId(array('channel_id' => $channel_id));
				if($result['errno'] == Tieba_Errcode::ERR_SUCCESS ){
					if (isset($result['imei']) && $result['imei'] != '') {						
						$strKey = self::CACHE_KEY_IMEI_ANDROID."_".$result['imei'];
						$res = self::_removeFromCache($strKey);
						if(true == $res){
							Bingo_Log::debug("clear cache success, key : ".var_export($strKey,true));
						}
						else{
							Bingo_Log::warning("clear cache fail, key : ".var_export($strKey,true));
						}
						
					}
				}
			}
			///////////////////
		}
	}

	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
			'status' => $status,
	);
	return $arrOutput;
}

/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32_t terminal_type
 * 	string token = optional()
 * 	string channel_id = optional()
 *  string channel_uid = optional()
 * 	string version
 * @return: $arrOutput
 **/
public static function nmqLogoutUser($arrInput){
	
	$arrInput = Tieba_Service::getArrayParams($arrInput, 'userclient');

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['terminal_type']) || !isset($arrInput['version'])){
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}

	$terminal_type = $arrInput['terminal_type'];

	if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS && !isset($arrInput['token'])) {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}

	if ($terminal_type == Service_Lib_UserMessage::TERMINAL_ANDROID && !isset($arrInput['channel_id'])) {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
		return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	}

	//input params.
	$user_id = intval($arrInput['user_id']);
	$version = $arrInput['version'];

	if (isset($arrInput['token'])) {
		$token = $arrInput['token'];
	}
	if (isset($arrInput['channel_id'])) {
		$channel_id = $arrInput['channel_id'];
	}
	if (isset($arrInput['channel_uid'])) {
		$channel_uid = $arrInput['channel_uid'];
	} else {
		$channel_uid = 0;
	}

	if(!self::_init()){
		header("HTTP/1.0 404 Not Found");
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	if (!self::_getDB()){
		header("HTTP/1.0 404 Not Found");
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	//output params.
	$status = false;
	$error = Tieba_Errcode::ERR_SUCCESS;

	//your code here......

	$objUserClient = new Dl_Userclient_Db_Userclient(self::$_db);
	$status = $objUserClient->logoutClient($terminal_type, $token, $channel_id, $channel_uid, $user_id, $version);
	if(-1 == $status){ //登出失败，需要重试
		header("HTTP/1.0 404 Not Found");
		$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
	}
	else if(-2 == $status){ //登出失败，不需要重试
		$error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
	}
	else {
		if(1 == self::$_conf['need_cache']){
			if ($user_id != 0) {
				//清除user-client cache
				$cache_key_prefix = '';
				if ($terminal_type == Service_Lib_UserMessage::TERMINAL_IOS) {
					$cache_key_prefix = self::CACHE_KEY_IOS;
				} else {
					$cache_key_prefix = self::CACHE_KEY_ANDROID;
				}

				$strKey = $cache_key_prefix."_".strval($user_id);
				$res = self::_removeFromCache($strKey);
				if(true == $res){
					Bingo_Log::debug("clear cache success, key : ".var_export($strKey,true));
				}
				else{
					Bingo_Log::warning("clear cache fail, key : ".var_export($strKey,true));
				}
			}
				
			///////////////
			if ($terminal_type == Service_Lib_UserMessage::TERMINAL_ANDROID) {
				//清除imei->client cache
				$result = self::getClientByChannelId(array('channel_id' => $channel_id));
				if($result['errno'] == Tieba_Errcode::ERR_SUCCESS ){
					if (isset($result['imei']) && $result['imei'] != '') {
						$strKey = self::CACHE_KEY_IMEI_ANDROID."_".$result['imei'];
						$res = self::_removeFromCache($strKey);
						if(true == $res){
							Bingo_Log::debug("clear cache success, key : ".var_export($strKey,true));
						}
						else{
							Bingo_Log::warning("clear cache fail, key : ".var_export($strKey,true));
						}

					}
				}
			}
			///////////////////
		}
	}

	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
			'status' => $status,
	);
	return $arrOutput;
}

public static function getUserPushCount($arrInput){
	if(!isset($arrInput['user_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}

	//input params.
	$user_id = intval($arrInput['user_id']);

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	if (!self::_getRedis()){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	
	$key = self::REDIS_USER_KEY . "$user_id";
	$arrInput = array(
	    'key' => $key,
	);
    $ret = self::$_redis->GET($arrInput);
    if ($ret == null || $ret['err_no'] !== 0) {
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);		
	}
	$user_push_count = intval($ret['ret'][$key]);
	
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
			'user_push_count' => $user_push_count,
	);
	return $arrOutput;
}

public static function incrUserPushCount($arrInput){
	if(!isset($arrInput['user_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}

	//input params.
	$user_id = intval($arrInput['user_id']);

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	if (!self::_getRedis()){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	//output params.

	//your code here......
	$key = self::REDIS_USER_KEY . "$user_id";
	$arrInput = array(
	    'key' => $key,
	);
    $ret = self::$_redis->INCR($arrInput);
    if ($ret == null || $ret['err_no'] !== 0) {
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);		
	}
	$delaTime = mktime(23, 59, 59) - time();  //当前时间戳与当天零点时间戳的差值（秒数）
	$arrInput = array(
	    'key' => $key,
	    'seconds' => $delaTime,
	);
    $ret = self::$_redis->EXPIRE($arrInput);
    if ($ret == null || $ret['err_no'] !== 0) {
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);		
	}
	
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
			'errno' => $error,
			'errmsg' => Tieba_Error::getErrmsg($error),
	);
	return $arrOutput;
}

}
