<?php
class Service_Lib_VersionCmp{
	
	/**
	 * brief 对比两个版本，if version1 newer than version2，return 1
	 * 	else if version1 older than version2， return -1，else return 0
	 * version separated by "."
	 * @param $strVersion1
	 * @param $strVersion2
	 */
	public static function compare($strVersion1, $strVersion2){
    	$arrVersion1 = explode( '.', $strVersion1);
    	$arrVersion2 = explode( '.', $strVersion2);
    	
    	$i = 0;
    	$intCnt1 = count($arrVersion1);
    	$intCnt2 = count($arrVersion2);
    	for (;$i < $intCnt1 && $i < $intCnt2;$i++){
    		$intV1 = intval($arrVersion1[$i]);
    		$intV2 = intval($arrVersion2[$i]);
    		if ($intV1 > $intV2) {
    			return 1;
    		}
    		else if ($intV1 < $intV2){
    			return -1;
    		}
    		else {
    			continue;
    		}
    	}
    	if ($intCnt1 > $intCnt2){
			return 1;
		}
		else if ($intCnt1 < $intCnt2){
			return -1;
		}
		else {
			return 0;
		}
    }
}