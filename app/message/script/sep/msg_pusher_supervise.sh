#!/bin/bash

PHP="./php/bin/php"
BIN_DIR="./app/message/script/sep"
MESSAGE_LOADER="$BIN_DIR/msg_loader.php"
MESSAGE_PUSHER="$BIN_DIR/msg_pusher.php"
ORP_TASK_ID=$1
LOG_FILE="./log/message/script/msg_pusher_supervise_$ORP_TASK_ID.log"
ORP_TASK_ID_TAG="task_"$ORP_TASK_ID"_tag"
ORP_PUSHER_TASK_COUNT=6
PUSHER_COUNT_PER_TASK=15
MAX_PUSH_SPEED_ANDROID=500
MAX_PUSH_SPEED_IOS=500

PUSHER_START_TIME=7
PUSHER_KILL_TIME=22
PUSHER_END_TIME=23

PUSHER_END_MINUTES=58

TOTAL_PUSHER_COUNT=$[$ORP_PUSHER_TASK_COUNT * $PUSHER_COUNT_PER_TASK]

function log() {
	echo "$(date +'%Y-%m-%d %H:%M:%S') $*" >> $LOG_FILE
}

function killPusher() {
	ps -elF | grep $MESSAGE_PUSHER | grep $ORP_TASK_ID_TAG | grep -v "grep" | gawk '{print $4}' | xargs kill
}

#trap "killPusher" EXIT

mkdir -p ./log/message/script/

######### load message from message storage #########
$PHP $MESSAGE_LOADER $ORP_TASK_ID
if [ $? != 0 ]; then
	log "load message fail!"
	exit 0 
fi
log "load message success!"

######### task loop to send message #########
while true; do

	### push time check ###
	hour=$(date +%H)
	if [ $hour -lt $PUSHER_START_TIME ]; then
		log "time not to start, so exit...."
		#sleep 30
		exit 0
	fi

	minutes=$(date +%M)
	#if [ $hour -gt $PUSHER_END_TIME ]; then
	if [ $minutes -ge $PUSHER_END_MINUTES ] || [ $hour -ge $PUSHER_END_TIME ]; then
		log "one hour time's up! time to exit because orp task is ...!"
		if [ $hour -ge $PUSHER_KILL_TIME ]; then
			log "it's the last hour! time to kill all pushers and exit!"
			killPusher
		fi
		exit 0
	fi

	### check pusher num every 10s ###
	CUR_PUSHER_COUNT=$(ps -elF | grep $MESSAGE_PUSHER | grep $ORP_TASK_ID_TAG | grep -v "grep" | wc -l)
	log "current pusher count: $CUR_PUSHER_COUNT"
	START_PUSHER_NUM=$[$PUSHER_COUNT_PER_TASK - $CUR_PUSHER_COUNT]

	if [ $START_PUSHER_NUM -gt 0 ]; then
		log "starting $START_PUSHER_NUM pusher(s)..."
		k=0;
		for ((i=0; i<$START_PUSHER_NUM; i++)); do

			$PHP $MESSAGE_PUSHER $ORP_TASK_ID $TOTAL_PUSHER_COUNT $MAX_PUSH_SPEED_ANDROID $MAX_PUSH_SPEED_IOS $ORP_TASK_ID_TAG &>/dev/null &

			if [ $? != 0 ]; then
				log "one pusher start fail!"
			else
				k=$[$k+1]
			fi
		done

		log "$k pusher(s) start success."
	else
		log "all pushers are running."
	fi

	sleep 30
done


exit 0
