function InsertSort(a,e,f){var b,c;if(f=="desc"){for(var d=1;d<a.length;d++){if(a[d-1][e]<a[d][e]){b=a[d];c=d-1;do{a[c+1]=a[c];c--}while(c>-1&&a[c][e]<b[e]);a[c+1]=b}}}else{if(f=="asc"){for(var d=1;d<a.length;d++){if(a[d-1][e]>a[d][e]){b=a[d];c=d-1;do{a[c+1]=a[c];c--}while(c>-1&&a[c][e]>b[e]);a[c+1]=b}}}}return a}var EvalLogic={eval_aside_dialog:null,setColor:function(e){var d=["#C77CA4","#1A920D","#D92B4D","#40A5E8","#5512F8","#F00302","#0692AD","#F91181","08CE37","#BFFD1A","#FFB400","#FC8548","#0B7BF6","#FF200F","#E0E736"];var b;for(var c=0,a=e.length;c<a;c++){e[c].color=d[c%d.length]}return e},setSize:function(f){var e=[18,22,24,26,30,32,34,36];for(var d=0,a=f.length;d<a;d++){f[d].idx=d}wordlistSort=InsertSort(f,"count","asc");var b="";var c=-1;for(var d=0,a=wordlistSort.length;d<a;d++){if(wordlistSort[d].count!=b){c=c<e.length-1?c+1:e.length-1;wordlistSort[d].size=e[c];b=f[d].count}else{wordlistSort[d].size=e[c]}}wordlistSort=InsertSort(wordlistSort,"idx","asc");return wordlistSort},setPanelMouseOver:function(a){a.className="back";a.getElementsByTagName("img")[0].style.visibility="visible"},setPanelMouseOut:function(a){a.className="white";a.getElementsByTagName("img")[0].style.visibility="hidden"},PanelDeleteEvalConfirm:function(c){var b='<div style="text-align:left; padding:20px 0 10px 31px; font-size:14px; line-height:22px">ȷ��Ҫɾ����������ô��<br>ɾ���󲻿ɻָ����Լ��ͱ��˶����������������ۡ�</div>';var a=Fe.Dialog.confirm(b,{title:"ɾ������",width:373});a.onaccept=function(d){EvalLogic.PanelDeleteEval(c)}},PanelDeleteEval:function(obj){var eval_id=obj.parentNode.getAttribute("eval_id");var url="http://tieba.baidu.com/i/commit";var params="cmd=del_evaluate&tbs="+PageData.tbs+"&ie=utf-8&id="+eval_id;Fe.Ajax.post(url,params,function(xmlHttp){var json=eval("("+xmlHttp.responseText+")");if(json.is_done){var wordlist=PageData.evaluate.evaluate_word;for(var i=0,len=wordlist.length;i<len;i++){if(eval_id==wordlist[i].id){Fe.Array.removeAt(wordlist,i);PageData.evaluate.evaluate_word=wordlist;break}}EvalLogic.set_myeval_eval("eval_panel")}else{}})},wordListHTML:function(f,e){var c=[];var d=0;var g="";for(var b=0,a=f.length;b<a;b++){g=Fe.String.subByte(f[b].evaluate,12,"��");d+=(Fe.String.getByteLength(g)+1);if(d>e){break}c.push(Fe.format('<span title="#{evalue}" style="color:#{color}" >#{eval_cut}</span>',{size:f[b].size,color:f[b].color,eval_cut:g,evalue:Fe.String.escapeHTML(f[b].evaluate)}))}return c.join("")},getPosition:function(a){var b=curtop=0;if(a.offsetParent){b=a.offsetLeft;curtop=a.offsetTop;while(a=a.offsetParent){b+=a.offsetLeft;curtop+=a.offsetTop}}return[b,curtop]},setEvalPosition:function(d){var c=Fe.G(d);var g=EvalLogic.getPosition(c);var b=g[1]+224;var h;var f=0;var a=Fe.G(d).getElementsByTagName("a");for(var e=0;e<a.length;e++){h=EvalLogic.getPosition(a[e]);if(parseInt(h[1]+a[e].offsetHeight)>=b){c.removeChild(a[e]);e--}}},getMaxWordArr:function(f,c){var e=0;var g="";var b=[];for(var d=0,a=f.length;d<a;d++){g=Fe.String.subByte(f[d].evaluate,12,"��");e+=(Fe.String.getByteLength(g)*(f[d].size/2)+30);if(e>c){b=f.slice(0,d);break}}if(e<=c){b=f}b=InsertSort(b,"time","desc");b=EvalLogic.setColor(b);return b},wordPanelHTML:function(f,c){var e=[];var b=EvalLogic.getMaxWordArr(f,c);for(var d=0,a=b.length;d<a;d++){e.push(Fe.format('<a title="����������#{count}�Σ�#{evalue}" value="#{evalue}" href=# style="font-size:#{size}px; color:#{color}" onclick="return false;" onmouseover="this.className=\'back\'" onmouseout="this.className=\'white\'" ><span>#{eval_cut}</span></a>',{count:b[d].count,size:b[d].size,color:b[d].color,eval_cut:Fe.String.escapeHTML(Fe.String.subByte(b[d].evaluate,12,"��")),evalue:Fe.String.escapeHTML(b[d].evaluate)}))}return e.join("")},wordMyEvalPanelHTML:function(g,c){function f(i){var h="";Fe.each(i.author_last,function(j){h+=j.name_show+"��"});h=h.substring(0,h.length-1);return h}var e=[];var b=EvalLogic.getMaxWordArr(g,c);for(var d=0,a=b.length;d<a;d++){e.push(Fe.format('<a title="#{author_last}����������" eval_id="#{eval_id}" href=# style="font-size:#{size}px; color:#{color}" onclick="return false;" onmouseover="EvalLogic.setPanelMouseOver(this)" onmouseout="EvalLogic.setPanelMouseOut(this)" ><span>#{eval_cut}</span><img title="ɾ��" height="9" width="9" border="0" src="http://static.tieba.baidu.com/tb/static-itieba/img/eval/icon_x.gif" onclick="EvalLogic.PanelDeleteEvalConfirm(this);"/></a>',{author_last:f(b[d]),size:b[d].size,color:b[d].color,eval_cut:Fe.String.escapeHTML(b[d].evaluate),eval_id:b[d].id}))}return e.join("")},set_aside_eval:function(a){var c=PageData.evaluate.evaluate_word;if(c.length>0){var d=EvalLogic.setColor(c);Fe.G(a).innerHTML=EvalLogic.wordListHTML(d,60);Fe.on(Fe.G(a),"click",function(){EvalLogic.open_eval_dialog(PageData.itieba.creator.name_link);Stats.sendRequest("st_mod=eval&fr=tb0_itieba&st_type=eval")});Fe.G(a).style.cursor="pointer"}else{var b=PageData.itieba.creator.sex!=2?"��":"��";Fe.G(a).innerHTML='<div style="padding-left:10px;">'+b+"��û���յ������ۡ�<a href=# onclick=\"EvalLogic.open_eval_dialog('"+PageData.itieba.creator.name_link+"');Stats.sendRequest('st_mod=eval&fr=tb0_itieba&st_type=eval'); return false;\" style=\"padding-left:20px; background:url(http://static.tieba.baidu.com/tb/static-itieba/img/eval/icon_eval.gif) no-repeat; margin-left:0px; color:#0449BE; text-decoration:underline\">����"+b+"</a></div>"}},set_myeval_eval:function(a){var c=PageData.evaluate.evaluate_word;var d=EvalLogic.setSize(c);var b=Fe.G("eval_panel");if(!b){return}b.innerHTML=EvalLogic.wordMyEvalPanelHTML(d,2000);EvalLogic.setEvalPosition(a)},set_dialog_eval:function(a){var b=PageData.evaluate.evaluate_word;if(b.length>0){Fe.G("word_title").style.display="";wordlistSort=EvalLogic.setColor(b);wordlistSort=EvalLogic.setSize(b);Fe.G(a).innerHTML=EvalLogic.wordPanelHTML(wordlistSort,1300);EvalLogic.setEvalPosition(a);if(PageData.evaluate.can_evaluate==0){Fe.each(Fe.G(a).getElementsByTagName("a"),function(c){Fe.on(c,"click",function(){Fe.G("eval").value=c.getAttribute("value")})})}}else{Fe.G("no_eval_tip").style.paddingTop="20px";Fe.G("no_eval_tip").innerHTML='<div style="color:#808080; text-align:center;">'+PageData.itieba.creator.name_show+"��û���յ�������...</font>"}},open_eval_dialog:function(b){var a=393;if(PageData.evaluate.can_evaluate==1||PageData.evaluate.can_evaluate==2){a=350}if(typeof PageData.evaluate.evaluate_word!="undefined"&&PageData.evaluate.evaluate_word.length==0){a=185}EvalLogic.eval_aside_dialog=Fe.Dialog.open("/i/"+b+"/evaluate",{title:"��������",width:372,height:a,buttonbar:false,locked:true,contentType:"iframe",scrolling:"no"})},CutInput:function(c,a){if(!c){return}var e=c.value;var d="";var b=1;if(e.replace(/[^\u0000-\u007f]/g,"\u0061\u0061").length>a){while(d.replace(/[^\u0000-\u007f]/g,"\u0061\u0061").length<a){d=e.substring(0,b++)}if(d.replace(/[^\u0000-\u007f]/g,"\u0061\u0061").length>a){d=e.substring(0,b-2)}c.value=d}}};