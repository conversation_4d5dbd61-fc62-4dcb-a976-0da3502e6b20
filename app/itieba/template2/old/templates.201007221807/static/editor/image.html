<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="content-type" content="text/html; charset=gbk">
	<script type="text/javascript" src="http://static.tieba.baidu.com/tb/js/setDomain.js?v=l5lq7q"></script>
<script type="text/javascript">

	TbUtil.setDomain();
try{document.execCommand("BackgroundImageCache",false,true);}catch(e) {}

String.prototype.trim=function(){return this.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g, "")};
String.prototype.getByteLength=function(){return this.replace(/[^\x00-\xff]/g, "mm").length;};

TiImage = {
    IE : (!!(window.attachEvent && !window.opera)),
    accept: function (){

        var editor = parent.BdeText;
		var img_tip = document.getElementById('bde_img_tip');
		var img_url = document.getElementById('bde_img_url');
		var img_url_value = img_url.value.trim();

		
		var lower_url = img_url_value.toLowerCase();
		if(lower_url.length <= 0){
			this.showError("图片链接不能为空");
			return false;
		}
		
		urlexp = /(\.jpg|\.jpeg|\.bmp|\.png|\.gif|\.tif)$/;
		
		if(img_url_value.getByteLength() > editor.urlLength || (!urlexp.test(lower_url))){
			this.showError("输入链接有误，请重试");
			return false;
		}
		this.showTipMsg();
        return true;
	},
	execute : function(editor, url){

		var html = '<img class="BDE_Image" changedsize="false" src="' + editor.formatURL(url) + '">';
		editor._focus();
		editor.paste(html);
		editor._focus();
		
		editor.dispatch("oneditorselectionchange");
	},
    showTipMsg : function(){
		document.getElementById('bde_img_tip').innerHTML = "请浏览选择要上传的图片";
		document.getElementById('bde_img_tip').style.color = "#666666";
	},
	showError : function(msg){
		document.getElementById('bde_img_tip').innerHTML = msg;
		document.getElementById('bde_img_tip').style.color = "#ff0000";
	}

};


window.onload = function(){
    TiImage.showTipMsg();
    /*
	var input = document.getElementById('bde_img_url');
	input.focus();
    try{
	    if(TiImage.IE){
			input.select();
		}else{
			input.setSelectionRange(0, input.value.length);
		}
    }catch(e){}
    */
};

</script>
</head>
<body style="margin:0;">
    <form action="" onSubmit="return TiImage.accept()" style="margin:0">
        <table cellpadding="2" cellspacing="0" border="0" style="margin-left:12px;font-size:12px">
<tr><td colspan="3" style="color:#666666;height:30px;font-size:12px">注意：一层楼最多可上传<script>document.write(parent.PageData.editor.imageLimite);</script>张图片</td></tr>
<tr><td width="340px"><input type="file" name="bde_img_url" id="bde_img_url" size=50></td></tr>
<tr><td><div id="bde_img_tip" style="color:#ff0000;font-size:12px">&nbsp;</div></td></tr>
<tr><td colspan="3" style="padding-top:6px"><input type="submit" name="insertAccept" id="insertAccept" value="上传图片"></td></tr>
</table>
</form>
</body>
</html>