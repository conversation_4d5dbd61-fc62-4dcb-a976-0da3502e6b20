var ImageUpLoad={urlLength:1024,is_end_preview:false,alertDialog:function(a,c){var b='<div style="padding:15px 20px;">'+a+"</div>";Fe.Dialog.alert(b,{title:c})},checkUpLoadImg:function(d){var c=this;var b=Fe.String.trim(d.value);if(b==""){return}var a=/(.jpg|.jpeg|.bmp|.png|.gif|.tif)$/i;if(Fe.String.getByteLength(b)>c.urlLength){c.alertDialog("ͼƬ·������","����ʧ��");d.value="";return}else{if(!a.test(b)){c.alertDialog("���ϴ�JPG��GIF��PNG��BMP��TIF��ʽ��ͼƬ","����ʧ��");d.value="";return}}setTimeout(function(){c.startUp(b)},10);return},startUp:function(a){Fe.G("bdeUpImgWidth").value="0";Fe.G("bdeUpImgHeight").value="0";Fe.G("bdeUpImgResized").value="false";Fe.show("bdeUpImgLoad");document.bdeUpImgForm.submit()},uploaded:function(c){var b=this;var d=Fe.G("bdeUpImgToolPanel");if(!d){return}var a='<table cellspacing="0" cellpadding="0">';a+="<tbody><tr>";a+='<td class="smallImgPreTd"><img id="smallImgPre" border="0"/></td>';a+='<td class="deleteImgPreTd"><a href="#" onclick="ImageUpLoad.deleteImg();return false">ɾ��</a></td>';a+="</tr></tbody></table>";d.innerHTML=a;PreviewImg.resizeImg(Fe.G("smallImgPre"),c,80,40,function(e){})},onImgLoaded:function(c,a){var b=this},uploadError:function(b){var a=this;a.alertDialog(b,"�ϴ�ʧ��");Fe.G("bdeUpImgUrl").value="";Fe.G("bdeUpImgWidth").value="0";Fe.G("bdeUpImgHeight").value="0";Fe.G("bdeUpImgResized").value="false";a.buildImgToolPanel()},deleteImg:function(){var a=this;Fe.G("bdeUpImgUrl").value="";Fe.G("bdeUpImgWidth").value="0";Fe.G("bdeUpImgHeight").value="0";Fe.G("bdeUpImgResized").value="false";a.buildImgToolPanel()},buildImgToolPanel:function(){var a=Fe.G("bdeUpImgToolPanel");if(a){a.innerHTML='<input type="file" name="photo" id="photo" class="bdeUpImgPhoto" size="40" onchange="ImageUpLoad.checkUpLoadImg(this);return false"><div id="bdeUpImgTip" class="bdeUpImgMsg">ÿ�Ŵ�С��3M����,������ϴ�JPG��GIF��BMP��PNG��ʽ��ͼƬ</div>'}},onToolOver:function(a){a.style.cursor="pointer"},onToolOut:function(a){a.style.cursor="default"}};function resultCall(a){if(a.resultNo=="0"){var c="http://imgsrc.baidu.com/forum/pic/item/"+a.picid+".jpg";setTimeout(function(){ImageUpLoad.uploaded(c)},1000)}else{var b={3153937:"ͼƬ��󲻵ó���3M",3153939:"��ָ����ͼƬ�����ڻ�ͼƬ��ʽ������",3153938:"��ָ����ͼƬ�����ڻ�ͼƬ��ʽ������",3153941:"�������Ȳ��ܳ���100�����֣�������",3153943:"����������ݰ������������֣�������",3153935:"ʣ����ÿռ䲻��",3153944:"��������",3162114:"�������Ʋ��ܳ���31�����֣�������",3162116:"����������ӵ�ַ������������",3162121:"����������ư������������֣�������",3162122:"����������ӵ�ַ�������ʵ���Ϣ��������",3145732:"�ϴ�ʧ�ܣ�ͼƬ��ַ��������˳���¼"};if(b[a.resultNo]){ImageUpLoad.uploadError(b[a.resultNo])}else{ImageUpLoad.uploadError("δ֪����")}}};