var IAdmin={url:"/i/commit",cur_btn_follow:null,setBlack:function(){var a=Fe.Q("user");Fe.each(a,function(b){Fe.on(b,"mouseover",function(){Fe.ac(this,"back");var c=Fe.G("btn_"+this.getAttribute("portrait"));if(c){Fe.show(c)}});Fe.on(b,"mouseout",function(){Fe.rc(this,"back");this.setAttribute("cur","0");var c=Fe.G("btn_"+this.getAttribute("portrait"));if(c){Fe.hide(c)}})})},setUnFollow:function(c){var b='<div style="text-align:center; padding:20px 0; font-size:14px;">��ȷ��Ҫȡ����ע'+c.getAttribute("name_show")+"?";var a=Fe.Dialog.confirm(b,{title:"ȡ����ע",width:300});a.onaccept=function(d){IAdmin.followAction(c)}},changeBtn:function(e){var f=e.getAttribute("portrait");var d=e.getAttribute("cmd");var a=Fe.G("p_"+f);var c=Fe.G("child_"+f);var b=Fe.G("ipt_"+f);if(d=="unfollow"){Fe.show(b);Fe.hide(c);Fe.G("p_"+f).setAttribute("btn_type","follow");Fe.G("fnum_"+f).innerHTML=parseInt(Fe.G("fnum_"+f).innerHTML)-1}else{if(d=="follow"){Fe.hide(b);Fe.show(c);Fe.G("p_"+f).setAttribute("btn_type","unfollow");Fe.G("fnum_"+f).innerHTML=parseInt(Fe.G("fnum_"+f).innerHTML)+1}}},init:function(){var a=Fe.G("search_list").getElementsByTagName("input");Fe.each(a,function(b){Fe.on(b,"click",function(){if(b.className=="btn_unfollow"){var d='<div style="text-align:center; padding:20px 0; font-size:14px;">��ȷ��Ҫȡ����ע'+b.getAttribute("name_show")+"?";var c=Fe.Dialog.confirm(d,{title:"ȡ����ע",width:300});c.onaccept=function(f){IAdmin.followAction(b)}}else{if(b.className=="btn_follow"){IAdmin.followAction(b)}}})})},followAction:function(obj){IAdmin.cur_btn_follow=obj;var params=Fe.format("cmd=#{0}&tbs=#{1}&portrait=#{2}&t=#{3}",obj.getAttribute("cmd"),obj.getAttribute("tbs"),obj.getAttribute("portrait"),Math.random());Fe.Ajax.post(IAdmin.url,params,function(r){var json=eval("("+r.responseText+")");if(json.is_done==true){if("true"==obj.getAttribute("creator")){setTimeout(function(){location.reload()},100)}else{IAdmin.changeBtn(obj)}}else{if(json.error_no==4){Fe.showLoginLayer()}else{Itieba.showErrorTip("�ӹ�ע",json._info);if(json.error_no==19){IAdmin.changeBtn(obj)}}}})},setAllFollow:function(){var ipts=Fe.Q("btn_follow");var cmd="follow_all";if(ipts.length==0){return}var ipts_c=new Array();var ipts_obj=new Array();var crypt=ipts[0].getAttribute("crypt");Fe.each(ipts,function(obj){if(Fe.G("p_"+obj.getAttribute("portrait")).getAttribute("btn_type")=="follow"){ipts_c[ipts_c.length]=obj.getAttribute("portrait");ipts_obj[ipts_obj.length]=obj}});var url=IAdmin.url;var params=Fe.format("cmd=#{0}&tbs=#{1}&itieba_id=#{2}&portraits=#{3}&t=#{4}",cmd,PageData.tbs,PageData.user.itieba_id,ipts_c.join(","),Math.random());Fe.Ajax.post(url,params,function(r){var json=eval("("+r.responseText+")");if(json.is_done==true){Fe.each(ipts_obj,function(obj){IAdmin.changeBtn(obj)})}else{if(json.error_no==4){Fe.showLoginLayer()}}})},showBtn:function(c,a,b){if(a){Fe.hide("ipt_"+c);Fe.show("child_"+c);Fe.G("p_"+c).setAttribute("btn_type","unfollow")}else{Fe.show("ipt_"+c);Fe.hide("child_"+c);Fe.G("p_"+c).setAttribute("btn_type","follow")}if(b==PageData.user.id){Fe.hide("p_"+c)}}};Fe.ready(IAdmin.setBlack);Fe.ready(function(){Fe.on("btn_c_all","click",function(){IAdmin.setAllFollow()})});if(Fe.isIE){Fe.ready(function(){Fe.on("copyLink","click",function(){clipboardData.setData("Text",Fe.G("copyLinkStr").value);Fe.G("copytip").innerHTML="���Ƴɹ�"})})};