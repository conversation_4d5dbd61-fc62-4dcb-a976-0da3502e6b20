var oldLink=null;function setActiveStyleSheet(e,f){var d,c,b;for(d=0;(c=document.getElementsByTagName("link")[d]);d++){if(c.getAttribute("rel").indexOf("style")!=-1&&c.getAttribute("title")){c.disabled=true;if(c.getAttribute("title")==f){c.disabled=false}}}if(oldLink){oldLink.style.fontWeight="normal"}oldLink=e;e.style.fontWeight="bold";return false}function selected(b,a){b.sel.value=a;if(b.dateClicked&&(b.sel.id=="sel1"||b.sel.id=="sel3")){b.callCloseHandler()}NewVoteTime.showCalendarTime(a)}function setDatebyCal(c){if(c.split("-").length==3&&G("vote_time_end")){var b=c.split("-");var a=new Date();var e=new Date(b[0],(b[1]*1-1),b[2],a.getHours(),a.getMinutes(),a.getMilliseconds());G("vote_time_end").innerHTML="����ͶƱ����"+(b[0])+"��"+(b[1])+"��"+(b[2])+"��"+(a.getHours())+":"+(a.getMinutes().toString().length==1?"0"+a.getMinutes():a.getMinutes())+"��ֹ";var d=new Date();d.setTime(Math.abs(e.getTime()-a.getTime()));if(G("period")){G("period").value=Math.round(d.getTime()/(1000*60*60*24))}}}function closeHandler(a){a.hide()}function showCalendar(e,d,a){var b=document.getElementById(e);if(calendar!=null){calendar.hide()}else{var c=new Calendar(false,null,a,selected,closeHandler);calendar=c;c.setRange(2003,2200);c.create()}calendar.setDateFormat(d);calendar.parseDate(b.value);calendar.sel=b;calendar.showAtElement(b,"Tl");window.location.hash="bm";document.getElementById("time_error_div").style.display="none";return false}var MINUTE=60*1000;var HOUR=60*MINUTE;var DAY=24*HOUR;var WEEK=7*DAY;function isDisabled(b){var a=new Date();return(Math.abs(b.getTime()-a.getTime())/DAY)>10}function flatSelected(c,a){var b=document.getElementById("preview");b.innerHTML=a}function showFlatCalendar(){var a=document.getElementById("display");var b=new Calendar(false,null,flatSelected);b.weekNumbers=false;b.setDisabledHandler(isDisabled);b.setDateFormat("DD, M d");b.create(a);b.show()};