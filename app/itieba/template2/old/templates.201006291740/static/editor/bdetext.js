var BdeText={EH:Fe.hide,ES:Fe.show,IE:Fe.isIE,Gecko:Fe.isGecko,Safari:Fe.isSafari,stepLog:0,textPanel:"bdeTextPanel",textTool:"bdeTextTool",textArea:"bdeTextArea",uneditToolbar:"bdeUneditToolbar",uneditArea:"bdeUneditArea",height:0,width:0,isHideEditArea:0,popup:null,doc:null,win:null,listeners:{},urlLength:1024,isLoaded:false,path:"http://static.tieba.baidu.com/tb/static-itieba/editor/",appendHtml:function(c){if(!c||c==""){return}var e=this.doc,a=e.body;if(this.isEmptyContent(a.innerHTML)){a.innerHTML=c}else{a.innerHTML=a.innerHTML+c}},clearHtml:function(){try{var f=this.doc,a=f.body;if(this.IE){a.innerHTML=""}else{a.innerHTML="<br>"}}catch(c){}},getImageNum:function(){return this.getElemNum("IMG","BDE_Image")},getSmileyNum:function(){return this.getElemNum("IMG","BDE_Smiley")},getFlashNum:function(){var a=this.getElemNum("EMBED","BDE_Flash");if(!this.IE){a+=this.getElemNum("IMG","BDE_Flash")}return a},isEmpty:function(){if(this.getOutToolHtml()!=""){return false}if(this.doc&&this.doc.body){return this.isEmptyContent(this.doc.body.innerHTML)}else{return true}},getHtml:function(){var q=this;q.reLayout();var p=Fe.setAttribute,m=Fe.getAttribute;var n=q.doc.createElement("SPAN");n.innerHTML=q.doc.body.innerHTML;q.parseURL(n);var k="";if(q.IE){var b=q.tags(n,"A");for(var e=0,c=b.length;e<c;e++){b[e].target="_blank"}var l=q.tags(n,"EMBED");for(var e=0,c=l.length;e<c;e++){p(l[e],"style",null)}var h=q.tags(n,"IMG");for(var e=0,c=h.length;e<c;e++){p(h[e],"style",null)}k=q.clearEmailLink(n.innerHTML)}else{var h=q.tags(n,"IMG");for(var e=0,c=h.length;e<c;e++){var d=h[e];if(d.className=="BDE_Flash"){var a=q.doc.createElement("EMBED");p(a,"class","BDE_Flash");p(a,"type","application/x-shockwave-flash");p(a,"pluginspage","http://www.macromedia.com/go/getflashplayer");p(a,"wmode","transparent");p(a,"play","true");p(a,"loop","false");p(a,"menu","false");p(a,"src",m(d,"flash_url"));p(a,"width",m(d,"width"));p(a,"height",m(d,"height"));p(a,"allowscriptaccess","always");p(a,"scale","noborder");d.parentNode.insertBefore(a,d);p(d,"src",null);d.style.display="none"}}for(var e=h.length-1;e>=0;e--){if(h[e].className=="BDE_Flash"){h[e].parentNode.removeChild(h[e])}}k=n.innerHTML}var r=q.replace(k,[[/<p *>/gi,""],[/< *\/p *>\r?\n/gi,"<br>"],[/< *\/p *>/gi,""],[/<p\/>/gi,"<br>"],[/<div><br><\/div>/gi,"<br>"],[/<div *>/gi,"<br>"],[/< *\/div *>\r?/gi,""],[/<div\/>/gi,"<br>"],[/<\/?strong>/gi,""],[/<\/?u>/gi,""],[/<\/?i>/gi,""],[/<\/?b>/gi,""],[/<\/?em>/gi,""]]);return r+q.getOutToolHtml()},getOutToolHtml:function(){var d,c;try{var f=this;var d="";var i=Fe.G("bdeUpImgUrl")?Fe.G("bdeUpImgUrl").value:"";if(i!=""){d+='<img class="BDE_Image" changedsize="'+Fe.G("bdeUpImgResized").value+'" src="'+f.formatURL(i)+'" ';d+=' width="'+Fe.G("bdeUpImgWidth").value+'" height="'+Fe.G("bdeUpImgHeight").value+'">';if(!f.isEmptyContent(f.doc.body.innerHTML)){d="<br>"+d}}var c="";var g=Fe.G("bdeFlashUrl")?Fe.G("bdeFlashUrl").value:"";if(g!=""){var a=450,b=500;if(g.toLowerCase().indexOf("baidu.com")>-1){b=480;a=410}else{b=500;a=450}c='<embed class="BDE_Flash" pluginspage="http://www.macromedia.com/go/getflashplayer"';c+=' src="'+f.formatURL(g)+'"';c+=' width="'+b+'" height="'+a+'"';c+=' type="application/x-shockwave-flash" wmode="transparent" play="true" loop="false"';c+=' menu="false" allowscriptaccess="always" scale="noborder">';if(!f.isEmptyContent(f.doc.body.innerHTML)){c="<br>"+c}}}catch(h){}return d+c},focus:function(){if(this.isEmpty()){this._focus()}else{this.focus2end()}},hideEditArea:function(){this.EH("bdeTextArea");this.EH("bdeUneditArea");Fe.G("editorInput").style.height=40+"px"},showEditArea:function(){this.ES("bdeTextArea");if(!this.editable){this.ES("bdeUneditArea")}Fe.G("editorInput").style.height=(this.height+40)+"px"},init:function(a){var b=this;b.stepLog=1;b.height=a.height||140;b.width=a.width||350;b.isHideEditArea=a.isHideEditArea||false;b.editable=a.editable;if(b.editable){b.EH(b.uneditToolbar);b.EH(b.uneditArea);b.listen("oneditorselectionchange",a.oneditorselectionchange);b.listen("oneditorfocus",a.oneditorfocus);b.listen("oneditorclick",a.oneditorclick);b.listen("oneditorkeyup",a.oneditorkeyup);b.listen("oneditorloaded",a.oneditorloaded);b.listen("oneditorpaste",a.oneditorpaste)}else{b.ES(b.uneditToolbar);b.ES(b.uneditArea)}if(b.isHideEditArea){b.hideEditArea()}else{b.showEditArea()}b.stepLog=2;b.createArea();b.stepLog=3;ImageUpLoad.buildImgToolPanel();b.makeEditable();b.stepLog=4;return this},previewToolImg:function(b){var d=Fe.G(b).value;if(d!=""){ImageUpLoad.is_end_preview=false;var c=Fe.G("bdePreviewToolPopup");Fe.hide("bdePreviewToolPopup");var a=Fe.G("bdePreviewToolPopupWraper");a.innerHTML='<img border=0 src="'+d+"?stamp="+new Date().getTime()+'" onload="ImageUpLoad.onImgLoaded(this,false);">'}else{Fe.hide("bdePreviewToolPopup")}},endPreviewImg:function(){ImageUpLoad.is_end_preview=true;Fe.hide("bdePreviewToolPopup")},previewToolFlash:function(a){var c=Fe.G(a).value;if(c!=""){var b=Fe.G("bdePreviewToolPopup");b.style.left="90px";Fe.G("bdePreviewToolPopupWraper").innerHTML='<img border=0 src="http://static.tieba.baidu.com/tb/static-itieba/editor/images/flashPreview.png" >';Fe.show("bdePreviewToolPopup")}else{Fe.hide("bdePreviewToolPopup")}},addFlash:function(a){var b=this;Fe.G("bdeFlashUrl").value=a;Fe.hide("bdeFlashTool");Fe.G("bdeFlashName").innerHTML=Fe.getFileName(a,14);Fe.show("bdeFlashComplete")},deleteFlash:function(){Fe.G("bdeFlashUrl").value="";Fe.G("bdeFlashName").innerHTML="";Fe.hide("bdeFlashComplete");Fe.show("bdeFlashTool")},doImage:function(){},doFlash:function(){this.createPopup(this.path+"flash.html?v=l4k1zv",535,115,"������Ƶ")},doSmiley:function(){var a=this;if(!a._smileyLoaded){a._smileyLoaded=true;a.loadSmiley()}a.createPopup(this.path+"smiley.html?v=l4k1zv",420,305,"�������")},_smileyLoaded:false,loadSmiley:function(){var b=this.path+"images/default/";var d=["jFace.gif","fFace.gif","wFace.gif","tFace.gif","bFace.gif"];for(var c=0,a=d.length;c<a;c++){(new Image()).src=b+d[c]}},clickContentTextTool:function(a){var b=this;if(a){Fe.G(a).style.top="172px"}b.showEditArea();Fe.hide("bdeContentTextPanel");Fe.hide("bdeContentTextPanelLogo")},createArea:function(){var b=this;var d="javascript:void(0)";if(b.IE){d="javascript:void( (function(){document.open();document.domain='baidu.com';document.write(\""+b.buildBody()+'");document.close();})() )'}b.stepLog=21;var c=document.createElement("iframe");c.style.width=b.width+"px";c.style.height=b.height+"px";c.frameBorder=0;c.src=d;b.stepLog=22;var a=Fe.G(b.textArea);a.innerHTML="";a.appendChild(c);b.stepLog=23;b.win=c.contentWindow},buildBody:function(){return("<html><head><script>document.domain='baidu.com';window.onerror=function(msg,url,line){try{parent.BdEditorLoader.setError('iLoadError',msg);}catch(e){} return true;};<\/script><style>body{background-color:#fff;padding:1px 2px;margin:0px;cursor:text;width:"+(this.width-20)+"px;height:"+(this.height-20)+"px;}body,td{font-family:'����', Verdana, sans-serif;font-size:13px;line-height:18px;word-break:break-all;}p{margin:0px;}a{line-height:18px;color:#0449be;text-decoration:underline;word-break:break-all;}.BDE_Flash{margin:4px 1px;border:#C1C2C2 1px solid;background:url(http://static.tieba.baidu.com/tb/static-itieba/editor/images/flashlogo.gif) no-repeat 50% 35%;}.BDE_Image{margin:4px 1px;}img{border:none;}</style></head><body><br></body></html>")},makeEditable:function(){var f=BdeText;var c=null,h=null,a=null;try{c=f.win;h=c.document;a=h.body}catch(g){}if(a){f.stepLog=31;f.doc=h;if(f.IE){a.disabled=true;a.contentEditable=true;a.removeAttribute("disabled")}else{try{a.spellcheck=false;h.designMode="on";h.execCommand("enableObjectResizing",false,true);h.execCommand("enableInlineTableEditing",false,false)}catch(g){}}f.stepLog=32;f.onload()}else{setTimeout(f.makeEditable,10)}},onload:function(){var a=this;a.stepLog=33;if(a.IE){a.doc.body.innerHTML=""}else{var c=a.doc;c.open();c.write(a.buildBody());c.close()}a.stepLog=34;a.doc=a.win.document;a.stepLog=35;a.bindEvent();a.stepLog=36;if(Fe.G(a.textTool)){a.disableSelection(Fe.G(a.textTool))}try{if(a.IE){document.execCommand("BackgroundImageCache",false,true)}}catch(b){}a.stepLog=37;a.ES(a.textPanel);a.isLoaded=true;a.dispatchDelay("oneditorloaded")},bindEvent:function(){var a=this;var b=a.doc;if(a.IE){a.eon(b,"selectionchange",function(c){a.selectionChangeHandler(c)});a.eon(b,"beforedeactivate",function(c){a.beforeActiveHandler(c)});a.eon(b,"activate",function(c){a.activeHandler(c)});a.eon(b.body,"focus",function(c){a.focusHandler(c)});a.eon(b.body,"paste",function(c){return a.doPasteIE(c)})}else{a.eon(b,"mouseup",function(c){a.selectionChangeHandler(c)});a.eon(b,"focus",function(c){a.focusHandler(c)});a.eon(b,"paste",function(c){return(c.returnValue=a.doPasteGecko(c))})}a.eon(b,"keydown",function(c){a.keydownHandler(c)});a.eon(b,"keyup",function(c){a.keyUpHandler(c)});a.eon(b,"click",function(c){a.clickHandler(c)});a.eon(b,"dblclick",function(c){a.clickHandler(c)})},eon:function(c,b,a){if(!c){return false}b=b.replace(/^on/,"").toLowerCase();if(c.attachEvent){c.attachEvent("on"+b,a)}else{c.addEventListener(b,a,false)}},listen:function(b,c){if(b&&c){var a=this.listeners;if(typeof(a[b])!="object"){a[b]=[]}(a[b])[a[b].length]=c}},dispatch:function(c){var b=this.listeners;if(b[c]){for(var a=0;a<b[c].length;a++){b[c][a].call(null,c)}}},dispatchDelay:function(a){var b=this;if(b.scTimer){clearTimeout(b.scTimer);delete b.scTimer}b.scTimer=setTimeout(function(){b.dispatch(a)},100)},doPasteIE:function(b){var c=clipboardData.getData("Text");if((!c)||c==""){return true}var a=this;c=a.replace(c,[[/&/g,"&amp;"],[/</g,"&lt;"],[/>/g,"&gt;"],[/\r?\n/g,"<br>"],[/\t/g,"&nbsp;&nbsp;&nbsp;&nbsp;"],[/\s/g,"&nbsp;"],[/\u3000/g,"&nbsp;&nbsp;"]]);a.paste(c);setTimeout(function(){a.dispatch("oneditorpaste")},50);return false},doPasteGecko:function(b){var a=this;setTimeout(function(){a.filte();a.dispatch("oneditorpaste")},50);return true},selectionChangeHandler:function(a){this.dispatch("oneditorselectionchange")},focusHandler:function(a){this.dispatch("oneditorfocus")},clickHandler:function(a){this.dispatch("oneditorclick")},keydownHandler:function(h){var f=this.win.event||h;var a=8;var j=9;var g=f.which||f.keyCode||f.charCode;if(f.ctrlKey||f.metaKey||f.shiftKey||f.altKey){return(f.returnValue=true)}else{if(g==a&&this.IE){var d=this.doc.selection;if("control"==d.type.toLowerCase()){var b=d.createRange();for(var c=b.length-1;c>=0;c--){b(c).parentNode.removeChild(b(c))}return(f.returnValue=false)}}else{if(g==j){Fe.G("postSubmit").focus();return(f.returnValue=false)}}}return(f.returnValue=true)},keyUpHandler:function(a){this.dispatchDelay("oneditorkeyup")},ieConntrol:null,ieBookmark:null,beforeActiveHandler:function(f){var c=this;var a=c.doc.selection;var b=a.createRange();if("control"==a.type.toLowerCase()){var d=b(0);b=c.doc.body.createTextRange();b.moveToElementText(d);c.ieControl=d}if(b){c.ieBookmark=b.getBookmark()}},activeHandler:function(d){var c=this;var a=c.doc.selection;var b;try{if("control"==a.type.toLowerCase()){b=c.doc.body.createControlRange();if(c.ieControl){b.addElement(c.ieControl)}}else{b=c.doc.body.createTextRange();if(c.ieBookmark){b.moveToBookmark(c.ieBookmark);b.select()}}}catch(d){}c.ieControl=null;c.ieBookmark=null},disableSelection:function(b){if(this.IE){b.unselectable="on";var c,a=0;while((c=b.all[a++])){switch(c.tagName){case"IFRAME":case"TEXTAREA":case"INPUT":case"SELECT":break;default:c.unselection="on"}}}else{if(this.Gecko){b.style.MozUserSelect="none"}else{b.style.userSelect="none"}}},replace:function(c,b){for(var a=0;a<b.length;a++){c=c.replace(b[a][0],b[a][1])}return c},tags:function(b,a){return b.getElementsByTagName(a)},getElemNum:function(e,f){var g=0;var a=this.tags(this.doc.body,e);for(var d=0,b=a.length;d<b;d++){if(a[d].className==f){g++}}return g},paste:function(b){var c=this;var d=c.doc;if(c.IE){if(d.selection.type.toLowerCase()=="control"){d.execCommand("Delete",false,null)}var a=d.selection.createRange();a.select();a.pasteHTML(b);a.collapse(false);a.select()}else{d.execCommand("insertHTML",false,b)}},_focus:function(){var a=this;try{if(a.IE&&a.doc.hasFocus()){a.ensureFocusIE()}a.win.focus();if(a.IE){a.ensureFocusIE()}}catch(b){}},ensureFocusIE:function(){this.doc.body.setActive();var c=this.doc.selection.createRange();var a=c.parentElement();var e=a.nodeName.toLowerCase();var d={address:1,blockquote:1,center:1,div:1,dl:1,fieldset:1,form:1,h1:1,h2:1,h3:1,h4:1,h5:1,h6:1,hr:1,marquee:1,noscript:1,ol:1,p:1,pre:1,script:1,table:1,ul:1};var b={p:1,div:1,form:1,h1:1,h2:1,h3:1,h4:1,h5:1,h6:1,address:1,pre:1,ol:1,ul:1,li:1,td:1,th:1};if(a.childNodes.length>0||!(d[e]||b[e])){return}c.moveEnd("character",1);c.select();if(c.boundingWidth>0){c.moveEnd("character",-1);c.select()}},focus2end:function(){var f=this;try{f.win.focus();if(f.IE){f.win.document.body.innerHTML=f.win.document.body.innerHTML}else{var a=f.win;if(a.getSelection){var h=a.document;var b=a.getSelection();var c=h.createRange();c.setStartAfter(h.body.lastChild);c.setEndAfter(h.body.lastChild);b.removeAllRanges();b.addRange(c)}}}catch(g){}},filte:function(){var e=this;var f=e.doc;e._focus();e.paste("<img id='BDE_cursor' class='BDE_Image' style='width:1px;height:1px'>");var b=f.body.innerHTML;b=e.replace(b,[[/\[/g,"[[-"],[/\]/g,"-]]"],[/<img ([^>]*class=["']?(BDE_Image|BDE_Flash|BDE_Smiley)["']?[^>]*)>/gi,"[img $1]"],[/<br[^>]*( *\/?)>/gi,"[br$1]"],[/<\/ ?p[^>]*>/gi,"[br]"],[/<table[^>]*>/gi,"[br]"],[/<\/ ?tr[^>]*>/gi,"[br]"],[/<\/ ?td[^>]*>/gi,"&nbsp;&nbsp;"],[/<(ul|dl|ol)[^>]*>/gi,"[br]"],[/<(li|dd)[^>]*>/gi,"[br]"],[/<script[^>]*>(.|\r?\n)*<\/script>/gi,""],[/<xml[^>]*>(.|\r?\n)*<\/xml>/gi,""],[/<style[^>]*>(.|\r?\n)*<\/style>/gi,""],[/<applet[^>]*>(.|\r?\n)*<\/applet>/gi,""],[/<object[^>]*>(.|\r?\n)*<\/object>/gi,""],[/<!--(.*)-->/gi,""],[/<[^>]*>/gi,""],[/\[img ([^\]]*)\]/gi,"<img $1>"],[/\[br( *\/?)\]/gi,"<br$1>"],[/\[\[\-/g,"["],[/\-\]\]/g,"]"]]);f.body.innerHTML=b;e._focus();var a=f.createRange();a.selectNode(f.getElementById("BDE_cursor"));var c=e.win.getSelection();c.removeAllRanges();c.addRange(a);if(e.Safari){e.paste("")}else{a.deleteContents()}},parseURL:function(e){var b="(=*+)";var a="(+*=)";var d=4;var c=function(f){if(f.nodeType==3){var g=f.nodeValue;if(g&&g.length>d){g="."+g;g=g.replace(/([^0-9a-zA-Z])((www\.|http:\/\/|mms:\/\/|rtsp:\/\/|ftp:\/\/|https:\/\/)[0-9a-zA-Z\.\!\~\#\?\:\/\\\&\%\-\+\*\'\=\@\_]+)/gi,"$1"+b+'a href="$2" target="_blank"'+a+"$2"+b+"/a"+a);g=g.substring(1);f.nodeValue=g}}else{if(f.nodeType==1){var h=f.firstChild;while(h){if(h.nodeName.toUpperCase()!="A"){c(h)}h=h.nextSibling}}}};c(e);e.innerHTML=this.replace(e.innerHTML,[[/\(\=\*\+\)a\ href="www\./gi,'<a href="http://www.'],[/\(\=\*\+\)/gi,"<"],[/\(\+\*\=\)/gi,">"]])},isEmptyContent:function(a){if(!a){return true}var b=this.replace(a,[[/<br *\/?>/gi,""],[/<\/?p *\/?>/gi,""],[/<\/?a *\/?>/gi,""],[/&nbsp;/g,""],[/(\r?\n)+/g,""],[/[\u3000\s]+/g,""]]);return(b=="")},clearEmailLink:function(a){return a.replace(/<a[^>]*href=[\'\"]?mailto[^>]*>([^>]*)<\/a>/gi,"$1")},reLayout:function(){var m=this;var k=Fe.setAttribute,j=Fe.getAttribute,o=Fe.getWidthHeight,a=Fe.setMaxLayout;var b=function(p,g){var i=o(p,"height");var f=o(p,"width");if(g.toLowerCase().indexOf("baidu.com")>-1){k(p,"width",480);k(p,"height",410)}else{k(p,"width",500);k(p,"height",450)}};var h=m.tags(m.doc,"IMG");for(var e=0;e<h.length;e++){var d=h[e];if(d.className=="BDE_Image"){if(a(d,"width",570)){k(d,"changedsize","true")}}else{if(d.className=="BDE_Flash"){b(d,j(d,"flash_url"))}else{if(d.className=="BDE_Smiley"){var c=false;if(o(d,"width")>40||o(d,"height")>40){k(d,"width",40);k(d,"height",40)}else{k(d,"width",o(d,"width"));k(d,"height",o(d,"height"))}if(c){k(d,"changedsize","true")}}}}}var l=m.tags(m.doc,"EMBED");for(var e=0;e<l.length;e++){var n=l[e];if(n.className=="BDE_Flash"){b(n,j(n,"src"))}}},createPopup:function(b,a,d,c){var e=this;e.popup=Fe.Dialog.open(b,{contentType:"iframe",width:a,height:d,title:c});e.popup.onclose=function(){e._focus();return true}},closePopup:function(){if(this.popup){this.popup.close()}},formatURL:function(a){return this.replace(a,[[/[\u3000\s]+/g,""],[/\\/g,"/"],[/</g,""],[/>/g,""],[/"/g,""],[/;/g,""],[/\(/g,""],[/\)/g,""]])}};