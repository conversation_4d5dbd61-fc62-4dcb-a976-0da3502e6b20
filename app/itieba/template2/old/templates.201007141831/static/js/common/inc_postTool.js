Fe.fireEvent=Fe.fireEvent||function(c,b){node=Fe.G(c);if(!node){return}b=b.replace(/^on/,"").toLowerCase();if(Fe.isIE){node[b]()}else{var a=document.createEvent("MouseEvents");a.initEvent(b,true,true);node.dispatchEvent(a)}};(function(window,undefined){var postTool=function(){var self=this;this.maxLength=64;this.inputNode=Fe.G("postTitle");this.postBtn=Fe.G("postSubmit");this.richBtn=Fe.G("postaddMore");this.voteBtn=Fe.G("postaddVote");this.titleNumTip=Fe.G("titleNumTip");this.containers=["headContainer","titleErrorTip","topContainer","richContainer","ToolVoteBox","bottomContainer"];Fe.each(this.containers,function(ele,index){self[ele]=Fe.G(ele)});Fe.on(this.inputNode,"onpaste",function(){setTimeout(function(){self.setInputTips()},50)});Fe.on(this.inputNode,"onkeydown",function(event){event=event||window.event;var keyCode=event.which||event.keyCode||event.charCode,target=event.target||event.srcElement;if(keyCode==9){if(self.richContainer.style.display=="block"){Fe.G("bdeTextArea").focus()}else{self.postBtn.focus()}event.returnValue=false;if(event.preventDefault){event.preventDefault()}}});Fe.on(this.inputNode,"onkeyup",function(event){event=event||window.event;var keyCode=event.which||event.keyCode||event.charCode,target=event.target||event.srcElement;switch(keyCode){case 13:if(event.ctrlKey===true){Fe.fireEvent(self.postBtn,"click")}break;default:self.setInputTips();break}});Fe.on(self.postBtn,"click",function(){if(!self.checkInput()){self.inputNode.focus();return}if(postTool.checkRichTextLength("richAreaError")===false){return}var post={title:self.inputNode.value,content:BdeText.getHtml().replace(/^\<br\>/g,"").replace(/\.\.\//g,"http://static.tieba.baidu.com/"),itieba_id:PageData.itieba.inner_id,cmd:"add_thread"};self.postBtn.disabled=true;postTool.postThread(post,function(json){self.postBtn.disabled=false;Fe.extend(post,{},json);fakeData.create("thread",post);self.inputNode.value="";BdeText.clearHtml();self.initView()},self)});Fe.on(this.postNode,"onclick",function(){});this.richBtn.onclick=function(){self.richView();return false};this.voteBtn.onclick=function(){self.voteView();return false};Fe.G("bderichToolBoxDelete").onclick=Fe.G("bdevoteToolBoxDelete").onclick=function(){self.initView();return false};BdeText.init()};Fe.extend(postTool,{postThread:function(params,callBack,obj){Fe.each(params,function(value,key){params[key]=value?value:""});params=Fe.extend(params,{tbs:PageData.tbs,cmd:"add_thread",ie:"utf-8",stamp:new Date().getTime()});var url="/i/commit?ie=utf-8&stamp="+new Date().getTime();Fe.Ajax.post("/i/commit",Fe.Object.toQueryString(params),function(xhr){var json=null;var success=false;try{json=eval("("+xhr.responseText+")");success=true}catch(ex){alert("UI�˴���: "+xhr.responseText)}if(success){if(json.error_no==0){if(callBack){callBack.call(this,json.ret)}}else{if(json.error_no==4){Fe.showLoginLayer()}else{if(json.error_no==12){Fe.Dialog.alertMessageFace("�ȴ����...",json._info.replace(/\r?\n/gi,"<br>"),{title:"��������",width:350})}else{Fe.Dialog.alertErrorFace("����ʧ��",json._info.replace(/\r?\n/gi,"<br>"),{title:"��������",width:350})}}}}if(obj){obj.postBtn.disabled=false}})},checkRichTextLength:function(error_id){var CFG=[{handler:"getImageNum",label:"ͼƬ",limit:PageData.editor.imageLimite||10},{handler:"getFlashNum",label:"��Ƶ",limit:PageData.editor.flashLimite||10},{handler:"getMusicNum",label:"����",limit:PageData.editor.musicLimite||10},{handler:"getSmileyNum",label:"����",limit:PageData.editor.smileLimite||10}];for(var i=0,len=CFG.length;i<len;i++){var obj=CFG[i];if(BdeText[obj.handler]()>obj.limit){if(error_id){Fe.G(error_id).innerHTML="���������"+obj.limit+"��"+obj.label}return false}}return true}});Fe.extend(postTool.prototype,{checkInput:function(){var result=this.getInputValue()==""?false:true;if(result){this.titleErrorTip.style.display="none"}else{this.titleErrorTip.innerHTML="���ⲻ��Ϊ��Ӵ";this.titleErrorTip.style.display="block"}return result},getInputValue:function(){var str=Fe.String.trim(this.inputNode.value);if(Fe.String.getByteLength(str)>this.maxLength){this.inputNode.value=str=Fe.String.subByte(str,this.maxLength,"")}return str},setInputTips:function(){this.titleErrorTip.style.display="none";var str=this.getInputValue();var len=Fe.String.getByteLength(str);var num=(64-len)/2;if(num<0){num=0}this.titleNumTip.innerHTML=Fe.format("����������#{0}����",parseInt(num))},initView:function(){var self=this;Fe.each(this.containers,function(ele,index){self[ele].style.display=(index==0||index==2)?"block":"none"});Fe.G("topContainer_right").appendChild(this.postBtn);Fe.G("richAreaError").innerHTML="";self.inputNode.focus()},richView:function(){var self=this;Fe.each(this.containers,function(ele,index){if(index!=1){self[ele].style.display=(index==0||index==3||index==5)?"block":"none"}});Fe.G("richBtnArea").appendChild(this.postBtn);self.titleErrorTip.style.display="none"},voteView:function(){var self=this;Fe.each(this.containers,function(ele,index){self[ele].style.display=(index==4?"block":"none")});if(!self.initVote){Fe.G("voteBoxIframe").src="/i/"+PageData.user.itieba_id+"/vote?stamp="+new Date().getTime();self.initVote=true}}});window.postTool=postTool})(window);var MainPostTool=null;Fe.ready(function(){if(Fe.G("postTitle")){MainPostTool=new postTool()}});