var TipPanel={tip_showed:false,mouse_out:true,bShow:false,hideTime:500,curPointObj:null,init:function(c,a){window.TipGlobalDiv_clickObj=c;TipPanel.config=a;var b;if(c.offsetWidth<115){b=Math.floor((115-c.offsetWidth)/2)+c.offsetWidth}else{b=115}if(!document.all){TipPanel.config.left=b+7}else{TipPanel.config.left=TipPanel.curPointObj.offsetWidth+3}window.TipGlobalDiv=this;if(Fe.G("TipParentDiv")){Fe.G("TipParentDiv").style.display="none"}TipPanel._buildHTML();TipPanel._show()},changeSize:function(e,a){TipPanel.config.width=e;TipPanel.config.height=a;var d=Fe.G("TipParentDiv");d.style.height=a+"px";d.style.width=e+"px";var c=Fe.G("memTipPanel");c.style.height=a+"px";c.style.width=e+"px";var b=Fe.G("tip_iframe");b.style.height=a+"px";b.style.width=e+"px";d.style.display=""},_show:function(){if(window.TipGlobalDiv_clickObj){var c=TipPanel._getPosition(window.TipGlobalDiv_clickObj);var b=TipPanel._getTip();b.style.left=TipPanel.config.left+"px";b.style.top=TipPanel.config.top+"px";TipPanel.tip_showed=true;var a=Fe.G("memTipPanel");a.style.height=TipPanel.config.height+"px";a.style.width=TipPanel.config.width+"px"}},hide:function(){var a=document.getElementById("TipParentDiv");if(a){a.style.display="none"}},_buildHTML:function(){var a='<div  id="memTipClosePanel" style="position:absolute; top:0px; right:0px; width:25px; height:30px; padding: 10px 0px 0px 0px;"><a id="memTipMemTipClose" href="javascript:void(0)" style="display:block; width: 15px;height: 15px;" onclick="javascript:TipPanel.hide()"></a></div>';a+='<div id="memTipPanel" style="z-index:1000">';a+=TipPanel.config.content;a+="</div>";a+='<div style="clear:both"></div>';var c=TipPanel._getTip();if(c){c.innerHTML=a}Fe.on(Fe.G("TipParentDiv"),"mouseout",function(d){TipPanel.mouse_out=true;need_show_tip=false;hideTipPanel()});Fe.on(Fe.G("TipParentDiv"),"mouseover",function(d){TipPanel.mouse_out=false;clearTimeout(time_tip_hide);need_show_tip=true});var b=TipPanel.config.src.indexOf("?")!=-1?"&":"?";Fe.G("tip_iframe").src=TipPanel.config.src+b+"t="+Math.random()},searchParent:function(c,a,b){var d=a;while(d=d.parentNode){if(c!=""){if(d.tagName.toLowerCase()==b&&d.className.toLowerCase().indexOf(c)!=-1){return d}}else{if(d.tagName.toLowerCase()==b){return d}}}return null},_getPosition:function(a){var b=curtop=0;if(a.offsetParent){b=a.offsetLeft;curtop=a.offsetTop;while(a=a.offsetParent){b+=a.offsetLeft;curtop+=a.offsetTop}}return[b,curtop]},_getTip:function(){var b=document.getElementById("TipParentDiv");if(!b){b=document.createElement("div");b.id="TipParentDiv";b.style.width=TipPanel.config.width+"px";b.style.height=TipPanel.config.height+"px";b.style.position="absolute";b.style.zIndex="999";b.style.zoom="1";b.style.display="none"}if(TipPanel.curPointObj){var a=TipPanel.searchParent("post_icon",TipPanel.curPointObj,"li");a.appendChild(b)}return b}};var tip_img_out=true;var need_show_tip=true;function initShowTipPanel(a){tip_img_out=false;need_show_tip=true;if(TipPanel.curPointObj!=null){if(TipPanel.curPointObj==a&&TipPanel.tip_showed){return}}setTimeout(function(){showTipPanel(a)},300)}var time_tip_hide=null;function hideTipPanel(a){tip_img_out=true;need_show_tip=false;time_tip_hide=setTimeout(function(){realHideTip(a)},350)}function showTipPanel(b){if(!need_show_tip){return}TipPanel.curPointObj=b;var a='<div id="tip_inner"><iframe id="tip_iframe" width="280px" height="165px" frameborder="0" scrolling="no" src="about:blank"></iframe></div>';var d=(document.all)?107:116;var c=(document.all)?2:5;TipPanel.init(b,{width:280,height:165,content:a,left:d,top:c,src:"/i/sys/panel?un="+b.getAttribute("username")})}function realHideTip(a){TipPanel.lastPointObj=TipPanel.curPointObj;if(!need_show_tip){TipPanel.hide();TipPanel.tip_showed=false}}function panel_open_eval(b,a){var c=Fe.Dialog.open("/i/"+b+"/evaluate",{title:"��������",width:372,height:a,buttonbar:false,locked:true,contentType:"iframe",scrolling:"no"})};