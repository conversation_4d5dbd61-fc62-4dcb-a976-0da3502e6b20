<?php
global $rpcActionMethoditiebaMisDal;
$rpcActionMethoditiebaMisDal = array (	
		/* itieba ui call */
		'addAuditPost' => array (
        
        'class' => 'MisAuditPostDal', 
        'method' => 'save' 
    ), 
    'addFilterPost' => array (
        
        'class' => 'MisFilterPostDal', 
        'method' => 'save' 
    ), 
    'addVip' => array (
        
        'class' => 'MisVipVerifyDal', 
        'method' => 'save' 
    ), 
    'findVerifyStatusByUid' => array (
        
        'class' => 'MisVipVerifyDal', 
        'method' => 'findVerifyStatusByUid' 
    ), 
    //mis�������ã���Ҫ�ڹ��캯���н���select_db('itieba');
    //�Ƚ�ɽկ��Ϊ��ʡRPC����,���½�
    'searchByThread' => array (
        
        'class' => 'MisSearchDal', 
        'method' => 'searchByThread' 
    ), 
    'searchByItieba' => array (
        
        'class' => 'MisSearchDal', 
        'method' => 'searchByItieba' 
    ),   
    'getPostDelInfo' => array (
        
        'class' => 'MisSearchDal', 
        'method' => 'getPostDelInfo' 
    ),   
    'getThreadDelInfo' => array (
        
        'class' => 'MisSearchDal', 
        'method' => 'getThreadDelInfo' 
    ),   
     //mis call		
    //�����
    'addWord' => array (
        
        'class' => 'MisWordDal', 
        'method' => 'addWord' 
    ), 
    'deleteWord' => array (
        
        'class' => 'MisWordDal', 
        'method' => 'deleteWord' 
    ), 
    'isWordExist' => array (
        
        'class' => 'MisWordDal', 
        'method' => 'isWordExist' 
    ), 
    'searchWord' => array (
        
        'class' => 'MisWordDal', 
        'method' => 'searchWord' 
    ), 
    'getValidWord' => array (
        
        'class' => 'MisWordDal', 
        'method' => 'getValidWord' 
    ), 
    'getWordByID' => array (
        
        'class' => 'MisWordDal', 
        'method' => 'getWordByID' 
    ), 
    'getExpireWord' => array (
        
        'class' => 'MisWordDal', 
        'method' => 'getExpireWord' 
    ), 
    'findWordByProperty' => array (
        
        'class' => 'MisWordDal', 
        'method' => 'findWordByProperty' 
    ),  //��������
    'searchAuditPost' => array (
        
        'class' => 'MisAuditPostDal', 
        'method' => 'searchAuditPost' 
    ), 
    'updateAuditPost' => array (
        
        'class' => 'MisAuditPostDal', 
        'method' => 'update' 
    ), 
    'findAuditPostByID' => array (
        
        'class' => 'MisAuditPostDal', 
        'method' => 'findByID' 
    ),  //���������	
    'searchFilterPost' => array (
        
        'class' => 'MisFilterPostDal', 
        'method' => 'searchFilterPost' 
    ), 
    
    //����֪ͨ
    'addQipaoNotice' => array (
        
        'class' => 'MisQipaoNoticeDal', 
        'method' => 'save' 
    ), 
    'updateQipaoNotice' => array (
        
        'class' => 'MisQipaoNoticeDal', 
        'method' => 'update' 
    ), 
    'findQipaoNoticeByID' => array (
        
        'class' => 'MisQipaoNoticeDal', 
        'method' => 'findByID' 
    ), 
    'findQipaoNoticeByProperty' => array (
        
        'class' => 'MisQipaoNoticeDal', 
        'method' => 'findAllByProperty' 
    ), 
    'searchQipaoNotice' => array (
        
        'class' => 'MisQipaoNoticeDal', 
        'method' => 'searchItemsByPage' 
    ), 
    
    //VIP��֤��
    'updateVip' => array (
        
        'class' => 'MisVipVerifyDal', 
        'method' => 'update' 
    ), 
    'findVipByID' => array (
        
        'class' => 'MisVipVerifyDal', 
        'method' => 'findByID' 
    ), 
    'searchVip' => array (
        
        'class' => 'MisVipVerifyDal', 
        'method' => 'searchVip' 
    ), 
    'updateVipByCond' => array (
        
        'class' => 'MisVipVerifyDal', 
        'method' => 'updateByCond' 
    ), 
    'findVipByUid' => array (
        
        'class' => 'MisVipVerifyDal', 
        'method' => 'findVipByUid' 
    ), 
    'findVipSameUid' => array (
        
        'class' => 'MisVipVerifyDal', 
        'method' => 'findVipSameUid' 
    ), 
    'findVipByPrpperty' => array (
        
        'class' => 'MisVipVerifyDal', 
        'method' => 'findAllByProperty' 
    ), 
    'findVipByProperty' => array (
        
        'class' => 'MisVipVerifyDal', 
        'method' => 'findAllByProperty' 
    ),     
    //VIPͶ����
    'searchVipTousu' => array (
        
        'class' => 'MisVipTousuDal', 
        'method' => 'searchVipTousu' 
    ), 
    'addVipTousu' => array (
        
        'class' => 'MisVipTousuDal', 
        'method' => 'addVipTousu' 
    ), 
    'deleteVipFname' => array (
        
        'class' => 'MisVipTousuDal', 
        'method' => 'deleteVipFname' 
    ), 
    'deleteVipUser' => array (
        
        'class' => 'MisVipTousuDal', 
        'method' => 'deleteVipUser' 
    ), 
    'findVipUnameExist' => array (
        
        'class' => 'MisVipTousuDal', 
        'method' => 'findVipUnameExist' 
    ), 
    'findVipFnameExist' => array (
        
        'class' => 'MisVipTousuDal', 
        'method' => 'findVipFnameExist' 
    ), 
    
    'findVipFnameByProperty' => array (
        
        'class' => 'MisVipFnameDal', 
        'method' => 'findAllByProperty' 
    ), 
    'findVipFnameByID' => array (
        
        'class' => 'MisVipFnameDal', 
        'method' => 'findByID' 
    ) ,
    'findExpireVipUser' => array (
        
        'class' => 'MisVipTousuDal', 
        'method' => 'findExpireVipUser' 
    ) 
);
