<?php

/**
 * @brief  ת����ش���
 * <AUTHOR>
 *
 */
class DVShare {
	
	//modify by hlx 2012.3.28 ȥ��StLog::addStrNode()
	public static function statOpenShareApi($arrShare) {
		$intV   = 0;
		$s_key  = '';
		$s_name = '';
		$s_link = '';
		$s_type = '';
        if ($arrShare['error_no'] === 0) {
        	$intV   = 1;
        	$s_link = $arrShare['domain'];
        	$s_type = $arrShare['type'];
        	
        	if ($arrShare['source'] !== '') {
        		$s_key = $arrShare['source'];
        	} else {
        		$s_key = $arrShare['domain'];
        	}
        }
        // ����ͳ��
		StLog::addNode('v', $intV);
		StLog::addNode('key', $s_key);
		StLog::addNode('name', $s_name);
		StLog::addNode('link', $s_link);
		StLog::addNode('type', $s_type);
        $str = sprintf('v=%d key=%s name=%s link=%s type=%d', $intV, $s_key, $s_name, $s_link, $s_type);
        ItbDict::set(ItbUiInc::SHARE_STAT_KEY, $str);
		//StLog::addStrNode($str);
		return true;
	}
	
	public static function getOpenShareData() {
		$arrShare = array(
			'title' => '',
			'content' => '',
			'type' => 0,
			'source' => '',
			'link' => '',
			'domain' => '',
			'extra' => array(),
			'succ' => false, // ��ʼ��ʧ�ܵ�
			'error_no' => -1,
		);
		$getVar = ItbDict::get(ItbUiInc::GET_PARAM_KEY);
		if (!isset($getVar['link']) || strlen($getVar['link']) <= 0) {
			// �쳣�����link����ȱʧ
			ItbDict::set(ItbUiInc::SHARE_PARSER_DATA, $arrShare);
			return true;
		}
		// init
		$url = $getVar['link'];
		if (strncasecmp($url, 'http', 4)!==0) {
			$url = 'http://'.$url;
		}
		$arrShare['link'] = $url;
		$arrShare['domain'] = ItbUtil::getDomainByLink($url);
		
		// �Ƚ���partner
		$strPartner = '';
		PartnerParser::setConfig(ItiebaOpenConfig::$config);
		PartnerParser::parser($url, $strPartner);
		
		$HtmlParser = new HtmlParser($url);
		// ���ó�ʱ������
		$HtmlParser->setCurlTimeoutMs(ItiebaGlobalConfig::OPEN_SHARE_CURL_TIMEOUT_MS);
		$HtmlParser->setCurlRetry(ItiebaGlobalConfig::OPEN_SHARE_CURL_RETRY);
		// �Ȱ�����Ƶ���ݽ���		
		$res = $HtmlParser->regularParser($out);
		if ($res !== false) {
			$arrShare['type'] = ItbUiInc::$shareType['video'];
			$arrShare['title'] = $out['title'];
			$arrShare['content'] = isset($out['content']) ? $out['content'] : '';
			$arrShare['source'] = $strPartner;
			$arrShare['extra']['video_pic'] = $out['extra']['video_pic'];
			$arrShare['extra']['video_swf'] = $out['extra']['video_swf'];
			$arrShare['succ'] = true;
			$arrShare['error_no'] = 0;
			ItbDict::set(ItbUiInc::SHARE_PARSER_DATA, $arrShare);
			self::statOpenShareApi($arrShare);
			return true;
		}
		$err = $HtmlParser->getError();
		if ($err !== "") {
			MyLog::warning(ItbUtil::getLogStr()."getOpenShareData fail ERR:$err");
		}
		
		// ������ͨ����ȥ����
		$nolyTitle = true;
		$res = $HtmlParser->htmlParser($out, $nolyTitle);
		//��ʱ����������ת�����ⲻһ������	add by hlx 2012.2.13
		self::processShareTitle($out);
		if ($res !== false) {
			$arrShare['type'] = ItbUiInc::$shareType['text'];
			$arrShare['title'] = $out['title'];
			$arrShare['source'] = $strPartner;
			$arrShare['extra']['video_pic'] = '';
			$arrShare['extra']['video_swf'] = '';
			if (strlen($arrShare['title']) > 0) {
				$arrShare['succ'] = true;
				$arrShare['error_no'] = 0;
				ItbDict::set(ItbUiInc::SHARE_PARSER_DATA, $arrShare);
				self::statOpenShareApi($arrShare);
				return true;
			} else {
				MyLog::warning(ItbUtil::getLogStr()."getOpenShareData fail ERR title empty ".print_r($arrShare, true));
			}
		}
		$err = $HtmlParser->getError();
		if ($err !== "") {
			MyLog::warning(ItbUtil::getLogStr()."getOpenShareData fail ERR:$err");
		}
		// ����ʧ��
		ItbDict::set(ItbUiInc::SHARE_PARSER_DATA, $arrShare);
		return true;
	}
	
	//��ʱ������ȥ��html_title����ġ�_���� ������Ϊת������	add by hlx 2012.2.13
	public function processShareTitle(& $results){
    	$arrHtmlTitle = explode("_", $results['html_title']);
        if(count($arrHtmlTitle) < 3){
        	return false;
        }
        $last_item = $arrHtmlTitle[count($arrHtmlTitle) - 1];
        if("���� " == $last_item){
        	$results['title'] = substr($results['html_title'], 0, -6);
        }
    }
}