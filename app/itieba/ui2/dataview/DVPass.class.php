<?php
/***************************************************************************
 * 
 * Copyright (c) 2010 Baidu.com, Inc. All Rights Reserved
 * $Id: DVPass.class.php,v 1.10 2010/04/08 04:06:15 zhangtianhong Exp $ 
 * 
 **************************************************************************/



/**
 * @file DVPass.class.php
 * <AUTHOR>
 * @date 2010/03/23 14:53:10
 * @version $Revision: 1.10 $ 
 * @brief 
 *  
 **/

class DVPass {

    const USER_PROFILE_KEY = 'userdetail';
    const USER_SEX_KEY = 'sex';
    const USER_BIRTHDAY_YEAR_KEY = 'birthday_year';
    const USER_BIRTHDAY_MONTH_KEY = 'birthday_month';
    const USER_BIRTHDAY_DAY_KEY = 'birthday_day';
    const USER_PROVINCE_KEY = 'province';
    const USER_ADDRESS_KEY = 'address';
    const USER_TAGORDER_KEY = 'tagorder';
    const USER_TAGINFO_KEY = 'taginfo';
    const USER_TAGSIGN_KEY = 'tagsign';
    const USER_REGTIME_KEY = 'regtime';
    const USER_CONSTELLATION_KEY = 'constellation';
    const USER_AGE = 'age';

    const FORUM_POSTCM_LOGIN_CMD = 1000;

    public static function getUserLogin() {
        $bolLogin    = (boolean)Tieba_Session_Socket::isLogin();
        $intUserId   = intval(Tieba_Session_Socket::getLoginUid());
        $strUserName = strval(Tieba_Session_Socket::getLoginUname());
        $intUserIp   = intval(Bingo_Http_Ip::ip2long(Bingo_Http_Ip::getConnectIp()));
        $bolNoUname  = (boolean)Tieba_Session_Socket::getNo_un();
        //$strMobile   = strval(Tieba_Session_Socket::getMobilephone());
        //$strEmail    = strval(Tieba_Session_Socket::getEmail());
        $strPortrait = Tieba_Ucrypt::encode($intUserId, $strUserName);

        $user = array(
            'is_login'  => $bolLogin,
            'id'        => $intUserId,
            //'user_id'   => $intUserId,
            'name'      => $strUserName,
            //'user_name' => $strUserName,
            'ip'        => $intUserIp,
            //'user_ip'   => $intUserIp,
            'portrait'  => $strPortrait,
            //'no_name'   => $bolNoUname,
            //'strMobile' => $strMobile,
            //'strEmail'  => $strEmail,
            
        );
        if($bolNoUname) {
            $user['user_name'] = strval(Tieba_Session_Socket::getDisplayname());
            $user['is_half'] =  intval(Tieba_Session_Socket::getIsHalfUser());
            $user['half_source_id'] = intval(Tieba_Session_Socket::getSourceId());
        }

        ItbDict::set(ItbUiInc::PASS_USER_LOGIN, $user);
        return true;

    }

    public static function checkUrlNotNeedSLogin() {
        return true;
    }
	//��ȡi���ɴ����ߵ�pass��ϸ��Ϣ
    public static function getItiebaCreatorProfile() {
        $itieba = ItbDict::get(ItbUiInc::LDM_ITB_INFO);
        
        $arrReq = array(
            self::USER_PROFILE_KEY, 
            self::USER_SEX_KEY, 
            self::USER_PROVINCE_KEY,
            self::USER_BIRTHDAY_YEAR_KEY, 
            self::USER_BIRTHDAY_MONTH_KEY, 
            self::USER_BIRTHDAY_DAY_KEY,
            self::USER_TAGINFO_KEY,
            self::USER_REGTIME_KEY,
            self::USER_CONSTELLATION_KEY,
            self::USER_AGE,
        );
        //$arrUInfo = PuserInfo::getInfoByuid($itieba['user_id'], $arrReq);
        $arrOutput = Tieba_Service::call('user', 'getUserinfoEx', array('user_id'=>$itieba['user_id'], 'extra_fields' => array('regtime')));
        if ($arrOutput === false) {
            $str = ItbUtil::getLogStr();
            $strLog = $str.'getItiebaProfile ERR(user-service getUserinfoEx false)';
            MyLog::warning($strLog);
            return true;
        }
        if($arrOutput['errno'] !== 0) {
            $str = ItbUtil::getLogStr();
            $strLog = $str.'getItiebaProfile ERR(user-service getUserinfoEx errno != 0)';
            MyLog::warning($strLog);
            return true;       	
        }
        $arrCreatorProfile = array();
        $arrUInfo = $arrOutput['user_info']['puserinfo'][$itieba['user_id']];
        // intro
        $arrCreatorProfile['intro'] = $arrUInfo[self::USER_PROFILE_KEY];
        // sex
        $arrCreatorProfile['sex'] = intval($arrUInfo[self::USER_SEX_KEY]);
        // age
        $intMonth = intval($arrUInfo[self::USER_BIRTHDAY_MONTH_KEY]);
        $intDay = intval($arrUInfo[self::USER_BIRTHDAY_DAY_KEY]);
        $intYear = intval($arrUInfo[self::USER_BIRTHDAY_YEAR_KEY]);
        if ($intYear <= 0) {
            $itieba['creator']['age'] = 0;
        } else {
            $intYearNow = intval(date('Y'));
            $intToday = mktime(0, 0, 0, $intMonth, $intDay, $intYearNow);
            $itieba['creator']['age'] = $intYearNow - $intYear;
            if (time() < $intToday) {
                --$itieba['creator']['age'];
            }
        }
        $arrCreatorProfile['age'] = $itieba['creator']['age'];
        // address
        //$arrCity = explode('-', $arrUInfo[self::USER_TAGINFO_KEY][1]);
        $arrAddress = self::parseAddress($arrUInfo[self::USER_TAGINFO_KEY]); 
        $arrCity = $arrAddress[0];
        $strCity = '';
        $arrCity = array_filter($arrCity,'strlen');
		$strCity = implode('��',$arrCity);
        $arrCreatorProfile['city'] = $strCity;
        $arrCreatorProfile['address'] = $arrCity;

        // reg time
        $intDaySec = 86400;
        $intRegTime = intval($arrUInfo[self::USER_REGTIME_KEY]);
        $intYear = intval(date('Y'));
        $intMonth = intval(date('n'));
        $intDay = intval(date('j'));
        $intToday = mktime(0, 0, 0, $intMonth, $intDay, $intYear) + $intDaySec;
        $intRegDay = floor(($intToday - $intRegTime)/$intDaySec);
        //���ذٶ��˺ŵ�ע������	add by hlx 2012-5-24
        //$arrCreatorProfile['reg_days'] = $intRegDay;

        $arrCreatorProfile['constellation'] = ItbUtil::getConstellationStr(intval($arrUInfo[self::USER_CONSTELLATION_KEY]));
        ItbDict::set(ItbUiInc::PASS_VISITING_ITIEBA_INFO,$arrCreatorProfile);
        return true;
    }
    public static function parseAddress($strAddress) {
		$arrTag = explode(chr(1) , $strAddress);
        $arrRet = array();
		if (is_array($arrTag) && count($arrTag) > 0) {
             foreach ($arrTag as $tag) { 
                 if (0 < strlen($tag)) {
                     $tagIndex = ord($tag[0]);
                     $tagValue = substr($tag , 1);
 
                     if (strlen($tagValue) > 0) {
                         $arrTagSect = explode(chr(2) , $tagValue);
                         if (is_array($arrTagSect) && count($arrTagSect) > 0) {
                             $arrRet[] = $arrTagSect;
                         } else {
                             $arrRet[] = array();
                         }       
                     } else {
                         $arrRet[] = array(); 
                     }       
                 }       
             }                    
		}
		return $arrRet;      
	}
    
    public static function doForumSecondLogin($intUserId, $strUserName, $intUserIp) {
        $arrInput = array(
            'command_no' => self::FORUM_POSTCM_LOGIN_CMD,
            'user_id' => $intUserId,
            'username' => $strUserName,
            'ip' => $intUserIp,
            'now_time' => time(),
        );
        $bolRes = Rpc::rpcCall('postcm', 'common', $arrInput, $arrOutput);
        if (true !== $bolRes) {
            $str = ItbUtil::getLogStr();
            $strLog = $str.'doForumSecondLogin ERR(rpcCall err)';
            MyLog::warning($strLog);
            return false;
        }
        return true;
    }
    
    public static function gerUserPhone() {
		$arrUserPhone = ItbUtil::getUserPhone();
		ItbDict::set(ItbUiInc::V_USER_PHONE,$arrUserPhone);
        return true;
    }      
}




/* vim: set ts=4 sw=4 sts=4 tw=100 */
?>
