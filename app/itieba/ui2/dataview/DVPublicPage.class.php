<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2011-12-10
 * @version 
 * @deprec �ṩ��������ҳ��Ŀ���߼��������
 */
class DVPublicPage {
    /**
    * ��ȡ�û�������Ϣ  ���ṩ��������ҳ�ӿڣ�
    * 
    * ��ʽ���ͣ�
    * ħ������� �������� �ȼ�:8  ����ֵ: 2340
    * ����� (С��)  �ȼ�:4  ����ֵ:1400
    * ��Ů��  �ȼ�:2  ����ֵ:900
    * 
    */
    public static function getUserHonour() {
        $itieba = ItbDict::get(ItbUiInc::LDM_ITB_INFO);
        //�û���member�е���Ϣ
        $manager_list = ItbDict::get(ItbUiInc::MEMBER_VISITING_USER_MANAGER_INFO, false);
        $arrBairenhui = ItbDict::get(ItbUiInc::MEMBER_VISITING_USER_OTHRER_INFO, false);
        /*  
        //$manager_list ��ʽ
        Array
        (
            [user_name] => abc
            [user_id] => 1496
            [is_private_position] => 
            [forum_position] => Array
                (
                    [0] => Array
                        (
                            [id] => 35
                            [name] => test
                            [position] => ����
                        )
         
                    [1] => Array
                        (
                            [id] => 82131
                            [name] => <div> 
                            [position] => ����
                        )
         
                )
         
            [has_position] => 1
        )
        */
        //�û���ϲ����Top5����
        $arrListTopList = ItbDict::get(ItbUiInc::MY_GRADE_INFO, false);
        
        //���list��ľ�����ݣ�ֱ�ӷ��أ���set���ݣ����������ȡ�����
        if ($manager_list===false && $arrListTopList===false){
            //ItbDict::set(ItbUiInc::PUBLIC_V_HONOUR, false);
            return true;
        }
                
        //��ȡ�����й���Ȩ�ްɵĵȼ���Ϣ
        $arrManagerList = array();
		$arrLikeInfo = array();
        if (!empty($manager_list) && !empty($manager_list['forum_position'])){
            $arrInput = array();
            foreach($manager_list['forum_position'] as $_f){
                $arrInput[] = array(
                    'user_id'   => $manager_list['user_id'],
                    'forum_id'  => $_f['id'],
                );
            }
            //��gradeģ��ץȡ���еȼ���Ϣ
            $arrLikeInfo = RpcIdlGradeService::mgetUserLevel($arrInput);
            if ($arrLikeInfo === false) {
                MyLog::warning(ItbUtil::getLogStr()."DVPublicPage::getUserHonour  RpcIdlGradeService::mgetUserLevel() fail[]");
                //return false;
            }
            $arrLikeInfo = isset($arrLikeInfo['res']) ? $arrLikeInfo['res'] : array();       
        }
        
        //�����like����Ϣ  (���������like��Ϣ��top5����like��Ϣ�ϲ�)
        $arrLikeInfo = array_merge($arrListTopList,$arrLikeInfo);
        $arrLikeResult = array();
        if (!empty($arrLikeInfo)){
            foreach($arrLikeInfo as $_f){
                $arrLikeResult[$_f['forum_id']] = $_f;
            }
        }
        unset($arrLikeInfo);
        unset($arrListTopList);
        
        //���մ�����ȡ����ɵĵȼ���Ϣ
        $arrRet = array(
            // comment finally by huangling02
            'itieba_id'     => $itieba['itieba_id'],
            'outer_id'      => $itieba['outer_id'],
            'uid'           => $itieba['user_id'],
            'uname'         => $itieba['user_name'],
            'tieba_data'    => array(),
        );
        if (!empty($manager_list['forum_position'])){
            foreach($manager_list['forum_position'] as $_f){
                $_like =  isset($arrLikeResult[$_f['id']])&&$arrLikeResult[$_f['id']]['is_like'] ? $arrLikeResult[$_f['id']] : array();
                $arrRet['tieba_data'][] = array(
                    'forum_name'    => $_f['name'],
                    'tieba_name'    => $_f['name'],
                    'forum_id'      => $_f['id'],
                    'link'          => ItbLink::tbHost() . ItbLink::forumFrs($_f['name']),
                    'position'      => $_f['position'],
                    'grade'         => empty($_like) ? array() : array(
                        'level_id'      => $_like['level_id'] ,
                        'level_name'    => $_like['level_name'] ,
                        'score'         => $_like['cur_score'] ,
                    ),
                );
                //��Like���б�������ð���Ϣ
                if (!empty($_like)){
                    unset($arrLikeResult[$_f['id']]);                    
                }
            }
        }
        if (!empty($arrLikeResult)){
            foreach($arrLikeResult as $_like){
                $arrRet['tieba_data'][] = array(
                    'forum_name'    => $_like['forum_name'],
                    'tieba_name'    => $_like['forum_name'],
                    'forum_id'      => $_like['forum_id'],
                    'link'          => ItbLink::tbHost() . ItbLink::forumFrs($_like['forum_name']),
                    'position'      => '',
                    'grade'         => empty($_like) ? array() : array(
                        'level_id'      => $_like['level_id'] ,
                        'level_name'    => $_like['level_name'] ,
                        'score'         => $_like['cur_score'] ,
                    ),
                );
            }
        } 
        unset($arrLikeResult);       
        unset($manager_list);
        
        //���Ӱ��˻���Ϣ	add by hlx 2012.8.24
        $arrRet['is_bairenhui'] = $arrBairenhui['is_bairenhui'];
                                           
        //������ɣ�����set��dict
		ItbDict::set(ItbUiInc::PUBLIC_V_HONOUR, $arrRet);
        return true;
    }
 
    /**
     * ��������ҳ���ݵ��û�ͷ����Ϣ���ܴ���Ϣ (��ͷ����ܴ������)
     *    
     *  ʾ��URL��
     *  portrait=05006d616c696e670000&ucs=05006d616c696e670000,05006d616c696e670000,05006d616c696e670000
     */
    public static function parsePortraitList(){
        $getVar = ItbDict::get(ItbUiInc::GET_PARAM_KEY);
        $postVar = ItbDict::get(ItbUiInc::POST_PARAM_KEY);
        $portrait_uids = NULL;
        if (isset($getVar['ucs'])){
            $portrait_uids = explode(',',$getVar['ucs']); 
        }
        elseif(isset($postVar['ucs'])){
            $portrait_uids = explode(',',$postVar['ucs']);    
        }
        if (empty($portrait_uids)){
            MyLog::warning(ItbUtil::getLogStr()."DVPublicPage::parsePortraitList parse Portrait fail: portrait-str is null");
            return false;
        }
        $uids = array();
        foreach($portrait_uids as $uc){
            $_uid = Ucrypt :: ucrypt_decode($uc, false); 
            if ($_uid !== false && is_int($_uid) && $_uid > 0 ){
                $uids[] = $_uid;
            }
        }
        if (empty($uids)) {
            MyLog::warning(ItbUtil::getLogStr()."DVPublicPage::parsePortraitList parse Portrait fail: some portrait-s invalid");
            return false;
        }
        
        ItbDict::set(ItbUiInc::LDM_USER_REL_UIDS, $uids);
        return true;
    }
    
    	
}