<?php

/**
 * @file dataview/DVSign.class.php
 * <AUTHOR> (<EMAIL>)
 * @date 2012/11/07
 * @version $Revision:  $ 
 * @brief 
 *  
 **/

class DVSign {
	
	//��ȡ�Ұ�������������е�ǩ����
	public static function getSignOfConcernForum() {
    	$itieba = ItbDict::get(ItbUiInc::LDM_ITB_INFO);
		$arrConcernForum = ItbDict::get(ItbUiInc::MY_CONCERN_FORUM);
		$arrForumIds = array();
		$forum_ids = array();
		foreach ($arrConcernForum['forums'] as $strFname => &$arrForumInfo) {
			//��sign��ȡ�����Ƿ�ǩ��
			$intFid = intval($arrForumInfo['feed_item_balv']['forum_id']);
			$arrForumIds[] = array(
				'forum_id' => $intFid,
			);
			$forum_ids[] = $intFid;
			//�ȳ�ʼ�����а�û��ǩ��
			$arrForumInfo['feed_item_balv']['is_sign'] = 0;
		}
		
		if((1 === ItiebaGlobalConfig::IS_NEED_SIGN) &&
			(count($forum_ids)) > 0) {
			//(count($arrForumIds)) > 0) {
			//$arrOutputNeed = array(
			//	'need_current_sign_info' => 1,   // �Ƿ���Ҫ�ɵ�ǰǩ����Ϣ
			//	'need_yesterday_sign_info' => 0, // �Ƿ���Ҫ������ǩ����Ϣ
			//	'need_weekly_sign_info' => 0,    // �Ƿ���Ҫ����ǩ����Ϣ
			//	'need_monthly_sign_info' => 0,   // �Ƿ���Ҫ����ǩ����Ϣ
			//	'need_user_sign_info' => 0,      // �Ƿ���Ҫ�û�ǩ����Ϣ
			//	'need_loyalty_info' => 0,        // �Ƿ���Ҫ�ҳ϶���Ϣ
			//	'need_org_info' => 0,            // �Ƿ���Ҫ������Ϣ, 0-��,1-��
			//	'need_tip_info' => 0,            // �Ƿ���Ҫ������Ϣ, 0-��,1-��
			//);
			//$arrSign = Tbapi_Userforum_Midl_Sign::getUserSignForums($arrForumIds,intval($itieba['user_id']),$arrOutputNeed);
			$in = array(
				'user_id'=>intval($itieba['user_id']),
				'forum_id'=>$forum_ids,
			);
			$arrSign = Tieba_Service::Call('sign', 'getUserSignForums', $in);
			//if(false === $arrSign) {
			if(false === $arrSign || 0 !== $arrSign['errno']) {
				MyLog::warning(" Tbapi_Userforum_Midl_Sign::getUserSignForums false forum_ids[".serialize($arrForumIds).
					"] uid[".$itieba['user_id']."]");
				//ѹ���ֵ�
		        ItbDict::set(ItbUiInc::MY_CONCERN_FORUM_NEW, $arrConcernForum);
				return false;
			}
			//$i = 0;
			foreach ($arrConcernForum['forums'] as $strFname => &$arrForumInfo) {
				//$arrForumInfo['feed_item_balv']['is_sign'] = $arrSign['arr_user_info'][$i]['is_sign_in'];
				$intFid = intval($arrForumInfo['feed_item_balv']['forum_id']);
				$arrForumInfo['feed_item_balv']['is_sign'] = $arrSign['arr_user_info'][$intFid]['is_sign_in'];
				//$i++;
			}
		}
		

		//ѹ���ֵ�
        ItbDict::set(ItbUiInc::MY_CONCERN_FORUM_NEW, $arrConcernForum);
	}
}
