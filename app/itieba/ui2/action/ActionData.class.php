<?php
/***************************************************************************
 * 
 * Copyright (c) 2010 Baidu.com, Inc. All Rights Reserved
 * $Id: ActionData.class.php,v 1.16 2010/04/22 17:36:53 zhangtianhong Exp $ 
 * 
 **************************************************************************/
 
 
 
/**
 * @file ActionData.class.php
 * <AUTHOR>
 * @date 2010/03/23 11:37:00
 * @version $Revision: 1.16 $ 
 * @brief 
 *  
 **/

class ActionData extends Action {

    public function execute($context, $actionParams = null) {
    	self::_processKey();
    	$key = ItbUtil::getByPassKey();  	

		//�����othersҳ����Ҫ����͹�����ҳ�����ת	add by hlx 2012.2.8
		/*if('others' == $key){
			self::_processOthers();
		}*/
		
		//����ѡ��̬С��������
		if('main' === $key || 'allfeed' === $key || 'ifeed' === $key) {
			self::_processSmallFlowOfGoodthreadfeed($key);
		}
		
		//����rp�Ƽ�С��������
		if('main' === $key || 'allfeed' === $key || 'ifeed' === $key) {
			self::_processSmallFlowOfRP($key);
		}
		
        $arrProcess = DVProcessConfig::$process[$key];
        foreach ($arrProcess as $process) {
            time_start($process[1]);
            $bolRes = call_user_func_array(array($process[0], $process[1]), array());
            time_end($process[1]);
            if (false === $bolRes) {
                $str = ItbUtil::getLogStr();
                $strLog = sprintf('%s %s:%s ERR', $str, $process[0], $process[1]);
                MyLog::warning($strLog);
                if ($process[2]) {
                    return false;
                }
            }
        }
        return true;    	 
    }
    //bypass key����ݾ���չʾ�߼�����һ���ı仯
    private function _processKey(){
    	$key = ItbDict::get(ItbUiInc::BYPASS_KEY);
    	if (!isset(DVProcessConfig::$process[$key])) {        	
            $key = 'main';
       	}    
		
        /*������Ƿ����Լ���itieba������ת��������url����UI2�ڲ�keyΪothers 
		 * add by dyb
		 * 2011.7.12
		 */
       	$isCreator = ItbDict::get(ItbUiInc::STAT_IS_CREATOR);
       	if(!$isCreator) {
       		if('main' == $key){ // || 'one' == $key)	{	update by hlx 2012.2.8
       			//itieba�˳�������������ҳ	add by hlx 2012.8.13
       			$strIsLogout='';
				if(false !== strpos($_SERVER['QUERY_STRING'], ItiebaGlobalConfig::FR_ITB_LOGOUT)){
				        $strIsLogout = '?' . ItiebaGlobalConfig::FR_ITB_LOGOUT;
				}
				$location = sprintf('Location: /i/%s/others%s',RequestUrl::$routerParams[':out_id'],$strIsLogout);
       			
       			//$location = sprintf('Location: /i/%s/others', RequestUrl::$routerParams[':out_id']);
				header($location); 
				exit();
       		}
       	}
       	
    }
    
    //����othersҳ�͹�����ҳ�����ת
    private function _processOthers(){
    	$itieba = ItbDict::get(ItbUiInc::LDM_ITB_INFO);
    	//itieba�˳�������������ҳ	add by hlx 2012.8.13
		$arrNoJumpPrincess = array('fr=princess', 'from=princess',ItiebaGlobalConfig::FR_ITB_LOGOUT,'from=itb_logout',
			'tmp_device=pc','pn=');
		//���url����Ĳ�������fr=princess��from=princess������othersҳʱ���Խ�������ȥ��
		/*if(('fr=princess' != $_SERVER['QUERY_STRING']) && 
			('from=princess' != $_SERVER['QUERY_STRING']) && 
			(0 !== strpos($_SERVER['QUERY_STRING'], '&pn='))){
			*/
		foreach($arrNoJumpPrincess as $strParams) {
    		if(false !== strpos($_SERVER['QUERY_STRING'], $strParams)) {
        		return true;
    		}
		}
		ItbUtil::jumpToPublicMainPage($itieba['user_name']);
    }
    
    //����ѡ��̬��С��������
    private function _processSmallFlowOfGoodthreadfeed($key) {
        $itieba = ItbDict::get(ItbUiInc::LDM_ITB_INFO);
        $intUid = $itieba['user_id'];
        $portrait = $itieba['portrait'];
        $intIsSmall = true;
        //���������С������ģ5!=0�����߽�ֹ��ѡ��̬
        if(((1 === ItiebaGlobalConfig::IS_GOODTHREADFEED_SMALLFLOW) && (0 !== ($intUid % 5)))
            || (1 < ItiebaGlobalConfig::IS_GOODTHREADFEED_SMALLFLOW)) {
            //����cupid����С����	add by hlx 2012.8.23
           	$arrInput = array(
           		'type' => 'goodthreadfeed',
           		'user_id' => $intUid,
           	);
           	$arrOutput = array();
            $bolCupidRs = Rpc::rpcCall('cupid', 'query', $arrInput, $arrOutput);
            //������С����������cupid����Ҳ��������С��������
            if ((false === $bolCupidRs) || empty($arrOutput) || (1 !== $arrOutput['res'][0]['small_flow_id'])) {
            	$intIsSmall = false;
	            //�����mainҳ������ת��allfeedҳ
	            if('main' === $key) {
	                //��ת��ȥ��exit��
	                ItbUtil::jumpToAllfeedPage($portrait);
	           	}
            }
        }
        ItbDict::set(ItbUiInc::V_IS_SMALL, $intIsSmall);
    }
    
    //����rp�Ƽ�С��������
    private function _processSmallFlowOfRP($key) {
        $itieba = ItbDict::get(ItbUiInc::LDM_ITB_INFO);
        $intUid = $itieba['user_id'];
        $intIsSmall = true;
        $strEncryUid = '';
        //���������С������ģ5!=1�����߽�ֹrp�Ƽ�
        if(((1 === ItiebaGlobalConfig::IS_RP_SMALLFLOW) && (1 !== ($intUid % 5)))
            || (1 < ItiebaGlobalConfig::IS_RP_SMALLFLOW)) {
        	$intIsSmall = false;
        }
        //����չ��rp�Ƽ�
        else {
        	$intIsSmall = true;
        	$strKey	= '4f89b6ebff8341a69f6bb9c6d4e7e7a9';
			$intID	= 1;
			$strEncryUid = fcrypt_id_2hstr($strKey, $intID, intval ($intUid));
        }
        ItbDict::set(ItbUiInc::V_ENCRY_UID, $strEncryUid);
        ItbDict::set(ItbUiInc::V_IS_RP_SMALL, $intIsSmall);
    }

}




/* vim: set ts=4 sw=4 sts=4 tw=100 noet: */
?>
