<?php
/**
 * �ύ���������
 * <AUTHOR>
 * @since 2009-09-17
 * @package itieba-cm
 */
class ItbSubmitCommand
{
    /**
     * ��ע
     *
     */
    const FOLLLOW = 'follow';
    const FOLLOW_ALL = 'follow_all';
    /**
     * ȡ����ע
     *
     */
    const UNFOLLOW = 'unfollow';
    const UNFOLLOW_ALL = 'unfollow_all';
    /**
     * ת��
     *
     */
    const REPASTE = 'repaste';
    /**
     * ������
     *
     */
    const ADD_THREAD = 'add_thread';
    /**
     * ���ظ�
     *
     */
    const ADD_POST = 'add_post';
    /**
     * ɾ������
     *
     */
    const DEL_THREAD = 'del_thread';
    /**
     * ɾ������
     *
     */
    const DEL_POST = 'del_post';
    
    const SET_THREAD_TOP = 'set_thread_top';
    
    const UNSET_THREAD_TOP = 'unset_thread_top';
    
    const LOCK_THREAD = 'lock_thread';
    
    const UNLOCK_THREAD = 'unlock_thread';
    
    const UPDATE_STYLE = 'update_style';
    
    const UPDATE_EVENT_STATUS = 'update_event_status';
    /*
     * �ύ������֤
     */
    const VERIFY = 'verify';
    //�����ɾ�̬���ύ��֤���󣬲���Ҫ�û���¼//<EMAIL>
    const OPEN_VIERFY = 'open_verify';
    
    const WISE_UPLOAD_IMAGE = 'wise_upload_image';
    
    /**
     * ��ӵ�������
     *
     */
    const ADD_BLACK_LIST = 'add_black_list';
    /**
     * ȡ������
     *
     */
    const CANCEL_BLACK_LIST = 'cancel_black_list';
    /**
     * վ�����
     *
     */
    const OPEN_SHARE = 'open_share';
    
    /**
     * ������Ϸ���֣���̬/i���ɣ�
     */
    const SHARE_GAME_SCORE = 'share_game_score';
    /**
     * @brief �����ڲ�����
     */
    const TB_SHARE = 'tb_share';
    
	const ACTIVE_PHONE = 'active_phone';

    /**
     * @brief: ��������
     */
    const ADD_EVALUATE = 'add_evaluate';
    /**
     * @brief: ɾ������
     */
    const DEL_EVALUATE = 'del_evaluate';
    
    //��Ӱɹ�ע
    const FOLLOW_FORUM = 'follow_forum';
    //ȡ���ɹ�ע
    const UNFOLLOW_FORUM = 'unfollow_forum';
    
    //������ӿڣ���Ҫ���ڳ�����ã���Ҫ����
    const ADD_COMMON_THREAD = 'add_common_thread';
    
    //ɾ�����뿴�ĳ�ȥ����(i������ҳ) add by xhl 2011.2.24
    const DEL_FAVO_FORUM = 'del_favoforum';
    
    //����ƽ̨��Ӷ�̬
    const OPENAPI_ADD_EVENT = 'openapi_add_event';	//add by xhl 2011.3.11
    
    //��ȡ�Ѿ����Ļظ����� add by xhl 2011.3.16
    const DEL_REPLY_REMIND = 'del_replyremind';
    
    //�ֶ���ӳ�ȥ���� 
    const ADD_USER_FAVO_FORUM = 'add_user_favoforum';
    
    //�ֶ�ɾ���û�����ȥ����
    const DEL_USER_FAVO_FORUM = 'del_user_favoforum';
    
    //�ṩ�������������ĵ�ͳһ�����ӿ�  add by xhl 2011.8.20
    const SUPER_FAVO_SUBMIT = 'super_favo_submit';
    
	//ɾ���ҹ�ע���İ�  add by hlx 2011.10.26
	const DEL_CONCERN_FORUM = 'del_concernforum';
	
	//ȡ�������ղ�״̬  add by hlx 2011.12.11
	const CANCEL_STORE_THREAD = 'cancel_storethread';
	
	//���������ղ�״̬  add by hlx 2011.12.12
	const OPEN_STORE_THREAD = 'open_storethread';
	
	//����δ�����ղ�����  add by hlx 2011.12.12
	const READ_STORE_THREAD = 'read_storethread';
		
	//�Ӿ���ֵ  add by hlx 2012.9.21
	const ADD_SCORE = 'add_score';
	
	//�Ӿ���ֵ  add by hlx 2012.9.21
	const CREATE_ITIEBA = 'create_itieba';
    
    /**
     * ���������
     *
     * @var unknown_type
     */
    public static $ActiveCommand = array(
        self::FOLLLOW,
        self::FOLLOW_ALL,
        self::UNFOLLOW,
        //self::UNFOLLOW_ALL,
        
        self::REPASTE,
        self::ADD_THREAD,
        self::ADD_POST,
        self::DEL_THREAD,
        self::DEL_POST,
        
        self::SET_THREAD_TOP,
        self::UNSET_THREAD_TOP,
        self::LOCK_THREAD,
        self::UNLOCK_THREAD,
        self::VERIFY,
        self::OPEN_VIERFY,
        
        self::UPDATE_EVENT_STATUS,
        self::UPDATE_STYLE,
        
        self::WISE_UPLOAD_IMAGE,
        self::ADD_BLACK_LIST,
        self::CANCEL_BLACK_LIST,
		self::OPEN_SHARE,
		self::TB_SHARE,
		self::ACTIVE_PHONE,
        self::ADD_EVALUATE,
        self::DEL_EVALUATE,
        self::FOLLOW_FORUM,
        self::UNFOLLOW_FORUM,
        
        self::ADD_COMMON_THREAD,
        self::SHARE_GAME_SCORE,
        
        self::DEL_FAVO_FORUM,
        
        self::OPENAPI_ADD_EVENT,
        
        self::DEL_REPLY_REMIND,
        
        self::ADD_USER_FAVO_FORUM,
        self::DEL_USER_FAVO_FORUM,
        
        self::SUPER_FAVO_SUBMIT,
        
        //add by hlx 2011.10.27
        self::DEL_CONCERN_FORUM,
         
        self::CANCEL_STORE_THREAD,
        self::OPEN_STORE_THREAD,
        self::READ_STORE_THREAD,
        self::ADD_SCORE,
        self::CREATE_ITIEBA,
    );
}
