<?php
/**
 * ���ȿ��Ƶ������ļ�
 * <AUTHOR>
 * @since 2009-09-18
 * @package itieba-cm
 *
 */
class SubmitActsConfig
{
    /**
     * �ύ�����Ӧ���ȿ����е������
     * 
     *
     */
    const CMD_ADD_THREAD = 1;
    const CMD_ADD_POST = 2;
    const CMD_FOLLOW = 3;
    const CMD_UNFOLLOW = 4;
    const CMD_REPASTE = 5;
    const CMD_ADD_EVALUATE = 6;
    public static $activeCmds = array();
}
SubmitActsConfig::$activeCmds = array(
    SubmitCommand::FOLLLOW => SubmitActsConfig::CMD_FOLLOW , 
    SubmitCommand::UNFOLLOW => SubmitActsConfig::CMD_UNFOLLOW , 
    SubmitCommand::REPASTE => SubmitActsConfig::CMD_REPASTE , 
    SubmitCommand::ADD_POST => SubmitActsConfig::CMD_ADD_POST , 
    SubmitCommand::ADD_THREAD => SubmitActsConfig::C<PERSON>_ADD_THREAD,
);
