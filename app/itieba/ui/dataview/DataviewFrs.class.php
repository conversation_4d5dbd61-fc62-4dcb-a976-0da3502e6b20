<?php
/***************************************************************************
 * 
 * Copyright (c) 2009 Baidu.com, Inc. All Rights Reserved
 * $Id: DataviewFrs.class.php,v 1.5 2009/10/12 09:19:43 zhouren2 Exp $ 
 * 
 **************************************************************************/
 
 
 
/**
 * @file DataviewFrs.class.php
 * <AUTHOR>
 * @date 2009/09/11 16:32:23
 * @version $Revision: 1.5 $ 
 * @brief 
 *  
 **/

class DataviewFrs {

    public static function getFeedThreadCount() {
        $action = Dict::getProperty('action');
        $arrIds = array();
        foreach ($action as $arrPer) {
            if ($arrPer['from']['type'] === ItiebaUiInc::FEED_PRODUCT_ID_TIEBA) {
                $arrIds[] = array('threadid' => $arrPer['feed_id']);
            }
        }
        if (count($arrIds) <= 0) {
            return true;
        }
        $arrOutput = RpcFrs::getThreadCount($arrIds);
        if (false === $arrOutput) {
            return true;
        }
        $index = 0;
        foreach ($action as & $arrPer) {
            if ($arrPer['from']['type'] === ItiebaUiInc::FEED_PRODUCT_ID_TIEBA) {
                $arrPer['reply_num'] = $arrOutput[$index]['postnum'];
                $arrPer['view_num'] = $arrOutput[$index]['freq'];
                ++$index;
            }
        }
        Dict::modifyProperty('action', $action);
        return true;
    }
}


/* vim: set ts=4 sw=4 sts=4 tw=100 */
?>
