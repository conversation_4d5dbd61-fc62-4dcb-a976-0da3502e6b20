<?php
/**
 * user-relation��Ӧ��RPC�ӿ�
 * <AUTHOR>
 * @since 2009-09-22
 * @package itieba
 */
class RpcUserRelation
{
    const SERVER_NAME = 'userRelation';
    
    const CALL_NAME = 'query';
    
    /**
     * ������̬����
     *
     */
    const EVENT_STATUS_OPEN = 0;
    /**
     * �رն�̬����
     *
     */
    const EVENT_STATUS_CLOSE = 1;
    
    public static $eventStatusArray = array();
    
    private static $_input = array(
        'header' => array(
            'content_type' => 'McpackRpc',
            'connection' => 0,
        ),
        'content' => array(
            array(
                'method' => '',
                'params' => array(),
                //'result_params' => array(),
                'id' => ITIEBA_REQUEST_ID,
            ),
        ),
    );
    
    /**
     * ��ѯ$itiebaId�Ƿ���$itiebaIdHost�ĺ�������
     *
     * @param int $itiebaId
     * @param int $itiebaIdHost
     */
    public static function isInBlackList($itiebaId,$itiebaIdHost)
    {
        $input = self::_buildInput('get_black_info_by_itieba_id',array(
            'itieba_id'=>$itiebaId,
            'itieba_ids'=>array($itiebaIdHost),
        ));
        $boolRs = Rpc::rpcCall(self::SERVER_NAME,self::CALL_NAME,$input,$output); 
        if ( !$boolRs || !$output || !isset($output['content'][0]['result_params']) ) {
            //��������
            return false;
        }
        $output = $output['content'][0]['result_params']['status'];
        if (intval($output[0])==1) {
        	return true;
        }
        return false;
    }
    
    public static function getUserFollows($intItiebaId, $offset=0, $limit=100)
    {
    	$arrInput = self::_buildInput('get_user_follows',array(
            'itieba_id' => $intItiebaId,
            'offset' => $offset,
    		'limit' => $limit,
    		'order_time' => 0,
        ));
        $boolRs = Rpc::rpcCall(self::SERVER_NAME, self::CALL_NAME, $arrInput,$arrOutput); 
        if ( !$boolRs || !$arrOutput || !isset($arrOutput['content'][0]['result_params']) ) {
            //��������
            return false;
        }
        return $arrOutput['content'][0]['result_params'];
    }
    /**
     * ��ѯ$itiebaId�Ƿ�����̬���ͣ�����static�Ż�
     *
     * @param itieba_id $itiebaId
     * @return true/false
     */
    public static function isEventOpen($itiebaId)
    {
        $eventStatus = 0;
        if (!isset(self::$eventStatusArray[$itiebaId]))
        {
            self::getUserStatus(array($itiebaId));
        }
        if (isset(self::$eventStatusArray[$itiebaId]))
        {
            $eventStatus = self::$eventStatusArray[$itiebaId];
        }
        $eventStatus = $eventStatus & 0x01; //��ֽϵͳ����ֽ�ȼ����Ƿ��ֹ��ע����user_relation��user_status�ֶ�,����ȡ���Ƿ��ע��ֵ
        if (intval($eventStatus) == self::EVENT_STATUS_OPEN)
        {
            return true;
        }
        return false;
    }
    
    /**
     * ������ȡ�û���̬��״̬
     *
     * @param ���� $itiebaIds
     */
    public static function getUserStatus($itiebaIds)
    {
        return true;
    }
    
    private static function _buildInput($method,$params)
    {
        self::$_input['content'][0]['method'] = $method;
        self::$_input['content'][0]['params'] = $params;
        return self::$_input;
    }
    /**
     * �����û�ID��ȡ�û���iteiba_id��
     *     'itieba_id' => int 0
     *     'user_type' => int 0
     *     'outer_id' => int 0
     *     'user_id' => int 0
     *     'user_name' => string '' (length=0)
     *     'user_url' => string '' (length=0)
     *     'user_nickname' => string '' (length=0)
     *     'user_status' => int 0
     *
     * @param array $uids
     * @return array/false
     */
    public static function getItiebaIdsByUids($uids=array())
    {
        return true;
    }
    public static function getItiebaInfosByUsernames($arrUsernames=array())
    {
        return true;
	}

	public static function getItiebaInfoByUsername($strUsername) 
	{
        return true;
	}
    /**
     * �����û������û�ID��ȡ�û���itieba_id������û���itieba�����ڣ��򴴽�
     *
     * @param int $uid
     * @param string $uname
     * @return itieba_id/false
     */
    public static function getItiebaId($uid,$uname)
    {
        return true;
    }
    /**
     * ��ȡ�û�����Ĺ�ϵ
     *
     * @param unknown_type $itiebaId
     * @return unknown
     */
    public static function getUserFollowInfo($itiebaId)
    {
        //TODO
    }
}
