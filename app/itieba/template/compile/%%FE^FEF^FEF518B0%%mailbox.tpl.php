<?php /* Smarty version 2.6.20, created on 2010-07-16 20:05:26
         compiled from invite/mailbox.tpl */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'json_encode', 'invite/mailbox.tpl', 30, false),array('modifier', 'escape', 'invite/mailbox.tpl', 35, false),)), $this); ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<!--STATUS OK-->
<html>
<head>
<meta http-equiv="content-type" content="text/html; charset=gbk" />
<meta http-equiv="X-UA-Compatible" content="IE=7" />
<title>�ҵ�i����_�����ע      </title>
<link rel="shortcut icon" href="http://static.tieba.baidu.com/tb/favicon.ico" />
<link rel="stylesheet" href="http://static.tieba.baidu.com/tb/static-itieba/style/base.css?v=l5lq08" type="text/css" />
<link id="itieba_base1_style" rel="stylesheet" href="http://static.tieba.baidu.com/tb/static-itieba/style/base_1.css?v=l5lq08" type="text/css" />
<link rel="stylesheet" href="http://static.tieba.baidu.com/tb/style/common.css?v=<?php echo $this->_tpl_vars['tb_tag']['jscss_tag']; ?>
_l5lq08" type="text/css" />

<?php if ($this->_tpl_vars['itieba']['style_id'] > 0): ?>
<link id="itieba_custom_style" rel="stylesheet" href="http://static.tieba.baidu.com/tb/static-itieba/style/set/set_<?php echo $this->_tpl_vars['itieba']['style_id']; ?>
.css?v=<?php echo $this->_tpl_vars['tb_tag']['jscss_tag']; ?>
_l5lq08" type="text/css" />
<?php endif; ?>

<script type="text/javascript" src="http://static.tieba.baidu.com/tb/js/Fe.js?v=6.0"></script>
<script type="text/javascript" src="http://static.tieba.baidu.com/tb/js/common_logic.js?v=<?php echo $this->_tpl_vars['tb_tag']['jscss_tag']; ?>
_l5lq08"></script>
<script src="http://static.tieba.baidu.com/tb/static-itieba/js/common_logic.js?v=<?php echo $this->_tpl_vars['tb_tag']['jscss_tag']; ?>
_l5lq08" type="text/javascript"></script>
<link rel="stylesheet" href="http://static.tieba.baidu.com/tb/static-itieba/style/invite.css?v=<?php echo $this->_tpl_vars['tb_tag']['jscss_tag']; ?>
_l5lq08" type="text/css" />
<script src="http://static.tieba.baidu.com/tb/static-itieba/js/invite.js?v=<?php echo $this->_tpl_vars['tb_tag']['jscss_tag']; ?>
_l5lq08" type="text/javascript"></script>
<script type="text/javascript">
Invite.home_url = "/i/<?php echo $this->_tpl_vars['itieba']['creator']['name_link']; ?>
/invite";
</script>
<script type="text/javascript">
var PageData = {
tbs : "<?php echo $this->_tpl_vars['base']['tbs']; ?>
", 
image_tbs : "<?php echo $this->_tpl_vars['base']['image_tbs']; ?>
",
user : <?php echo smarty_function_json_encode(array('var' => ($this->_tpl_vars['user'])), $this);?>
, 
editor : <?php echo smarty_function_json_encode(array('var' => ($this->_tpl_vars['editor'])), $this);?>
 
};
<?php if (! empty ( $this->_tpl_vars['itieba'] )): ?>
PageData.itieba = <?php echo smarty_function_json_encode(array('var' => ($this->_tpl_vars['itieba'])), $this);?>
;
PageData.itieba.creator.name_url = "<?php echo ((is_array($_tmp=$this->_tpl_vars['itieba']['creator']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'url') : smarty_modifier_escape($_tmp, 'url')); ?>
";
PageData.itieba.home_url = "/i/<?php echo $this->_tpl_vars['itieba']['creator']['name_link']; ?>
";	
<?php if (! empty ( $this->_tpl_vars['tb_tag'] )): ?>
	PageData.tb_tag = <?php echo smarty_function_json_encode(array('var' => ($this->_tpl_vars['tb_tag'])), $this);?>
;
<?php endif; ?>
	<?php if (! empty ( $this->_tpl_vars['evaluate'] )): ?>
	PageData.evaluate = <?php echo smarty_function_json_encode(array('var' => ($this->_tpl_vars['evaluate'])), $this);?>
;
	<?php else: ?>
	//evaluate is null
	<?php endif; ?>
<?php endif; ?>
<?php if ($this->_tpl_vars['user']['is_login']): ?>
PageData.user.name_url = "<?php echo ((is_array($_tmp=$this->_tpl_vars['user']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'url') : smarty_modifier_escape($_tmp, 'url')); ?>
";
<?php endif; ?>
</script>

</head>
<body>

<div id="userbar"></div>
<div id="local_flash_cnt"></div>
<script type="text/javascript">
UserBar.init(PageData.user,'itieba');
</script>


<div id="main_wrapper">
    <div id="main_back_img">
		
		<div id="main_back_bottom">
		
		<div id="main_header_logo" style="width:950px;margin:0 auto 5px auto;">
			<a href="/" title="���ٶ�������ҳ" style="background:url(http://static.tieba.baidu.com/tb/static-itieba/style/iHome/post-jg.png) no-repeat 0 0;width:133px;height:47px;display:block"></a>					
		</div>
		
		
		<div id="container_border">
			<div class="top_t"><b></b><i></i></div>
		
			<div id="verify_banner"></div>
			<div id="container" class="clearfix">
				

<div id="content">
	
<div id="ucenter_tab">

	<h2 id="ucenter_title">�����ע</h2>
</div>
	<div id="invite_cnt">
		
		<div id="invite_wrapper">
			<!-- ����ͨѶ¼���� -->
			<div id="mail_invite">
			<p>���������䵼��ͨѶ¼��ѡ��������˲����������ʼ���<span style="margin-left:32px;"><br>Ŀǰ֧��sina��gmail��yahoo�ȳ������䡣</p>
			<p>
			<label for="mail_account">���������ʺţ�</label><input type="text" value="" id="mail_account" name="mail_account" class="input_text">&nbsp;@
			<select id="server_type" name="server_type">
			    <option value="">��ѡ������</option>
			    <option value="@gmail.com">gmail.com</option>
			    <option value="@sina.com">sina.com</option>
			    <option value="@yahoo.com.cn">yahoo.com.cn</option>
			    <option value="@yahoo.cn">yahoo.cn</option>
			    <option value="@yahoo.com">yahoo.com</option>
			    <!--<option value="@163.com">163.com</option>
			    <option value="@126.com">126.com</option>-->
			    </select>
			</p>
			<p><label for="mail_pass">�����������룺</label><input type="password" id="mail_pass" name="mail_pass" class="input_text"></p>
			<p>
			<input type="button" value="����ͨѶ¼" class="input_submit" onclick="Invite.Mailbox.importFriendList();">
			<span id="tip_mailbox" class="tip"></span>
			</p>
			<div class="up_tip_wrapper">
			    <div class="up_tip">����ģ��ٶȲ����¼�������롣</div>
			</div>
			</div>
		</div>
	
		<div id="friend_list">
			<div id="result_tip"></div>
			<div id="result_title">
			<input type="checkbox" onclick="selectAll(this,'friend');" checked="checked" style="margin-top:-3px;vertical-align:middle;" id="select_all">
			<label for="select_all">ȫѡ</label>
			</div>
			<div id="invite_friends"></div>
			<p id="invite_tip"></p>
			<p id="invite_button"><input value="����" type="button" onclick="Invite.Mailbox.sendInvitation()"></p>
		</div>
	
		<div id="invite_result"></div>
	</div>
</div>


			</div>
			
			<div class="top_b"><b></b><i></i></div>	
		</div>
		<!-- end container_border -->
		
		<div id="footer">                                                                                                         
	<span style="color:#77c">&copy;<?php echo $this->_tpl_vars['base']['year']; ?>
 Baidu</span> 
	<a href="http://static.tieba.baidu.com/tb/eula.html">����Э��</a>&nbsp;
	<a href="/�����������" target="_blank">�������</a>
</div>
		
		</div>

    </div>
</div>
<div id="LCF_info"></div>	



<?php if ($this->_tpl_vars['user']['is_login']): ?>
<script src="http://msg.baidu.com/ms?ct=18&cm=3&tn=bmSelfUsrStat&mpn=<?php echo $this->_tpl_vars['user']['id']; ?>
&un=<?php echo ((is_array($_tmp=$this->_tpl_vars['user']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'url') : smarty_modifier_escape($_tmp, 'url')); ?>
"></script>

<script type="text/javascript">
UserBar.showMsg(msgnum);
</script>
<script type='text/javascript' src='http://static.tieba.baidu.com/tb/nocache/js/mulan_loader.js'></script>
<script type='text/javascript' src='http://static.tieba.baidu.com/tb/js/mulan/itieba-mulan.js?v=<?php echo $this->_tpl_vars['base']['stamp_time']; ?>
'></script>
<script type='text/javascript'>                                                                                                                            
    var userid = PageData.user.id;
    var rangeNum = 50;
    if(typeof(userid) != "string") {
         userid = userid.toString();
    }
	var url = window.location.href;
    var len = userid.length;
    var ret = (userid.substring(len-2,len) < rangeNum) ? true : false;
    if(ret || url.indexOf('mulan=true') > 0){mulan.Core.init(PageData.user.name)};
</script>
<?php endif; ?>


<?php if (! $this->_tpl_vars['user']['is_creator'] && ! $this->_tpl_vars['itieba']['creator']['is_verify']): ?>
<script type="text/javascript">
/** ��ʼ��WebIM���û��Ŀռ���Ϣ **/
Fe.on(window, 'load', function() {
    var signs = "<?php echo $this->_tpl_vars['itieba']['creator']['portrait']; ?>
";
	UserInfo.request(signs);
});
</script>
<?php endif; ?>


</body>
</html>