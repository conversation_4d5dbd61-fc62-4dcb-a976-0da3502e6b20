var FeDialogInited=false;Fe.Dialog=function(a){Sys.call(this);this.help=false;this.font="normal 12px sans-serif";this.title="Fe.Dialog";this.width="";this.height="";this.autofit=false;this.content="&nbsp;";this.overflow="visible";this.position="center";this.titlebar=true;this.scrolling="auto";this.buttonbar=false;this.statusbar=false;this.resizable=false;this.controlbar=true;this.buttonClose=true;this.contentType="HTMLString";this.buttonAccept=false;this.buttonCancel=false;this.buttonbarAlign="center";this.buttonAcceptValue="\u786e \u5b9a";this.buttonCancelValue="\u53d6 \u6d88";this.locked=true};Fe.Dialog.Extends(Sys,"Fe.Dialog");Fe.Dialog.cssFilePath=Sys.resourcePath+"/FeDialog.css";Fe.Dialog.prototype.render=function(b,f){if("undefined"!=typeof(b)){this.content=b}if("object"==typeof(f)){Fe.extend(this,f)}if("number"==typeof(this.width)){this.width+="px"}if("number"==typeof(this.height)){this.height+="px"}var a=this,c=this.dialog=Fe.DialogFactory.produce();c.onaccept=function(){a.accept()};c.oncancel=function(){a.cancel()};c.onclose=function(){a.close()};c.onhelp=function(){a.help()};c.changeSize=function(g,d){a.changeSize(g,d)};c.show(this);c.setActive();if(this.buttonbar&&this.buttonAccept){Fe.G("FeDialogButtonAccept_"+c.hashCode).focus()}if(this.locked){BdLockWindow.lock({opacity:0.4,backgroundColor:"#FFFFFF",zIndex:(52000-10)});this.addEventListener("onclose",function(){BdLockWindow.unlock()})}setTimeout(function(){a.dispatchEvent(new Sys.Event("onopen"))},10)};Fe.Dialog.prototype.setContent=function(a){this.dialog.setContent(a)};Fe.Dialog.prototype.setCaption=function(a){this.dialog.setCaption(a)};Fe.Dialog.prototype.setWidth=function(a){this.dialog.setWidth(a)};Fe.Dialog.prototype.setHeight=function(a){this.dialog.setHeight(a)};Fe.Dialog.prototype.getIframe=function(){return Fe.G("FeDialogIframe_"+this.dialog.hashCode)};Fe.Dialog.prototype.close=function(){var a=this,b=new Sys.Event("onclose");a.dispatchEvent(b);if(!b.returnValue){return}this.dialog.hide(function(){a.dispose()})};Fe.Dialog.prototype.accept=function(){var a=new Sys.Event("onaccept");this.dispatchEvent(a);if(!a.returnValue){return}this.close()};Fe.Dialog.prototype.cancel=function(){var a=new Sys.Event("oncancel");this.dispatchEvent(a);if(!a.returnValue){return}this.close()};Fe.Dialog.prototype.help=function(){this.dispatchEvent(new Sys.Event("onhelp"))};Fe.DialogFactory=function(){Sys.call(this);this.active=false;this.resizable=false;Fe.DialogFactory.dialogs.push(this)};Fe.DialogFactory.Extends(Sys,"Fe.DialogFactory");Fe.DialogFactory.prototype.setActive=function(){return false;var a=Fe.DialogFactory.currentDialog;if(a==this){return}if(a){var b=Fe.G("FeDialog_"+a.hashCode).style;b.zIndex=parseInt(b.zIndex)-4000;Fe.rc("FeDialog_"+a.hashCode,"focused_")}Fe.ac("FeDialog_"+this.hashCode,"focused_");Fe.DialogFactory.currentDialog=this;var b=Fe.G("FeDialog_"+this.hashCode).style;b.zIndex=parseInt(b.zIndex)+4000};Fe.DialogFactory.dialogs=[];Fe.DialogFactory.currentDialog=null;Fe.DialogFactory.png=function(){return(Fe.isIE>=7||Fe.isIE<=0)};Fe.DialogFactory.prototype.create=function(){var b="";if(Fe.isIE==6){b='<iframe id="FeDialog_iframe_#{0}" src="javascript:false;" style="position:absolute; z-index:'+(52000+Fe.DialogFactory.dialogs.length*3-2)+'; display:none;"></iframe>'}var a=["<div ",'id="FeDialog_#{0}" ','class="FeDialog" ','style="position:absolute;z-index:',(52000+Fe.DialogFactory.dialogs.length*3),';display:none" ',"onclick=\"Sys.I('#{0}').click(event)\">",'<div class="FeDialog_inner',(Fe.DialogFactory.png()?" png_":""),'">','<table class="FeDialog_wrapper" border="0" cellpadding="0" cellspacing="0">','<tr class="middle_">','<td class="left_ horizontal_"></td>','<td class="center_ midland_">','<div class="FeDialog_container">','<div id="FeDialogCaption_#{0}" class="FeDialogCaption" onselectstart="return false">','<div id="FeDialogControlBar_#{0}" class="FeDialogControlBar">','<a id="FeDialogButtonClose_#{0}" href="#" onfocus="this.blur();" onclick="Sys.I(\'#{0}\').onclose(); return false;">','<img alt="close" src="http://img.baidu.com/hi/img/dialogclose.gif" />',"</a>","</div>",'<div id="FeDialogCaptionText_#{0}" onmousedown="Sys.I(\'#{0}\').setActive()" class="FeDialogCaptionText">FeDialog</div>',"</div>",'<div id="FeDialogContent_#{0}" class="FeDialogContent">&nbsp;</div>','<div id="FeDialogButtonBar_#{0}" class="FeDialogButtonBar">','<input id="FeDialogButtonAccept_#{0}" type="button" value="\u786e \u5b9a" onclick="Sys.I(\'#{0}\').onaccept()" class="accept_" />','<input id="FeDialogButtonCancel_#{0}" type="button" value="\u53d6 \u6d88" onclick="Sys.I(\'#{0}\').oncancel()" class="cancel_" />',"</div>",'<div id="FeDialogStatusBar_#{0}" class="FeDialogStatusBar" style="display:none">&nbsp;</div>',"</div>","</td>",'<td class="right_ horizontal_"></td>',"</tr>","</table>","</div>","</div>",'<div id="FeDialog_shadow_#{0}" class="FeDialog_shadow" ','style="position:absolute;z-index:',(52000+Fe.DialogFactory.dialogs.length*3-1),';display:none"></div>',b];a=Fe.format(a.join(""),this.hashCode);if(document.body){document.body.insertAdjacentHTML("afterBegin",a)}else{document.write(a)}};Fe.DialogFactory.prototype.show=function(op){var me=this;function _(id){return Fe.G(id+"_"+me.hashCode)}if(op.icon){_("FeDialogCaption").style.background="url("+op.icon+") no-repeat left 3px";_("FeDialogCaptionText").style.paddingLeft="18px"}_("FeDialog").style.fontSize="12px";_("FeDialogCaptionText").innerHTML=op.title;_("FeDialogCaption").style.display=op.titlebar?"":"none";_("FeDialogStatusBar").style.display=op.statusbar?"":"none";_("FeDialogControlBar").style.display=op.controlbar?"":"none";_("FeDialogButtonBar").style.display=op.buttonbar?"":"none";_("FeDialogButtonClose").style.display=op.buttonClose?"":"none";_("FeDialogButtonAccept").style.display=op.buttonAccept?"":"none";_("FeDialogButtonCancel").style.display=op.buttonCancel?"":"none";_("FeDialogButtonBar").style.textAlign=op.buttonbarAlign;_("FeDialogButtonAccept").value=op.buttonAcceptValue;_("FeDialogButtonCancel").value=op.buttonCancelValue;with(_("FeDialogContent").style){width=op.width;height=op.height;overflow=op.overflow}if(op.contentType.toLowerCase()=="htmlelement"&&!Fe.G(op.content)){op.contentType="HTMLString"}switch(op.contentType.toLowerCase()){case"htmlstring":_("FeDialogContent").innerHTML=op.content;break;case"htmlelement":var e=Fe.G(op.content);this.opContentDisplay=e.style.display;e.insertAdjacentHTML("beforeBegin","<input type='button' id='FeDialogFactoryInset_"+this.hashCode+"' style='width:"+e.offsetWidth+"px; height:"+e.offsetHeight+"px; padding:0; margin:0; border:none; visibility:hidden' />");_("FeDialogContent").innerHTML="";e.style.display="";_("FeDialogContent").appendChild(e);break;default:_("FeDialogContent").innerHTML="<iframe frameborder='0' scrolling='"+op.scrolling+"' id='FeDialogIframe_"+this.hashCode+"' name='FeDialog_"+this.hashCode+"' style='width:"+(op.width||"100%")+"; height:"+(op.height||"100%")+"' src='"+op.content+"'></iframe>";break}var a=Fe.trim(op.position).toLowerCase().split(/\s/);var body=Fe.body();Fe.show("FeDialog_"+this.hashCode);Fe.show("FeDialog_shadow_"+this.hashCode);if(Fe.isIE==6){Fe.show("FeDialog_iframe_"+this.hashCode)}if(Sys.ie&&_("FeDialogContent").offsetWidth<136){_("FeDialogContent").style.width="130px"}if(Sys.ie&&_("FeDialogContent").offsetHeight<50){_("FeDialogContent").style.height="50px"}var top=(Math.max(parseInt((body.viewHeight-_("FeDialog").offsetHeight)/2),0)+body.scrollTop)+"px";var left=(Math.max(parseInt((body.viewWidth-_("FeDialog").offsetWidth)/2),0)+body.scrollLeft)+"px";if(a.length==1){if(a[0]==""||a[0]=="center"){}else{if(a[0]=="top"){top=body.scrollTop+"px"}else{if(a[0]=="bottom"){top=(body.scrollTop+body.viewHeight-_("FeDialog").offsetHeight)+"px"}else{if(a[0]=="left"){left=body.scrollLeft+"px"}else{if(a[0]=="right"){left=(body.scrollLeft+body.viewWidth-_("FeDialog").offsetWidth)+"px"}else{if(/\d+%/.test(a[0])){top=a[0]}else{if(/(\d+)(cm|mm|in|pt|pc|px|em|ex)?/.test(a[0])){top=parseInt(RegExp.$1)+RegExp.$2||"px"}}}}}}}}else{if(a.length>1){if(/\d+%/.test(a[0])){top=a[0]}else{if(/(\d+)(cm|mm|in|pt|pc|px|em|ex)?/.test(a[0])){top=parseInt(RegExp.$1)+RegExp.$2||"px"}}if(/\d+%/.test(a[1])){left=a[1]}else{if(/(\d+)(cm|mm|in|pt|pc|px|em|ex)?/.test(a[1])){left=parseInt(RegExp.$1)+RegExp.$2||"px"}}if(a[0]=="top"||a[1]=="top"){top=body.scrollTop+"px"}if(a[0]=="bottom"||a[1]=="bottom"){top=(body.scrollTop+body.viewHeight-_("FeDialog").offsetHeight)+"px"}if(a[0]=="left"||a[1]=="left"){left=body.scrollLeft+"px"}if(a[0]=="right"||a[1]=="right"){left=(body.scrollLeft+body.viewWidth-_("FeDialog").offsetWidth)+"px"}}}_("FeDialog").style.top=top;_("FeDialog").style.left=left;_("FeDialog_shadow").style.top=parseInt(top)+3+"px";_("FeDialog_shadow").style.left=parseInt(left)+3+"px";_("FeDialog_shadow").style.width=_("FeDialog").offsetWidth+"px";_("FeDialog_shadow").style.height=_("FeDialog").offsetHeight+"px";if(Fe.isIE==6){_("FeDialog_iframe").style.top=parseInt(top)+3+"px";_("FeDialog_iframe").style.left=parseInt(left)+3+"px";_("FeDialog_iframe").style.width=_("FeDialog").offsetWidth+"px";_("FeDialog_iframe").style.height=_("FeDialog").offsetHeight+"px"}Fe.Dialog.dragDrop.initElement(_("FeDialogCaptionText"),_("FeDialog"),_("FeDialog_shadow"),_("FeDialog_iframe"));this.active=true};Fe.Dialog.prototype.changeSize=function(c,a){var d=this;function b(f){return Fe.G(f+"_"+d.dialog.hashCode)}b("FeDialogContent").style.width=c+"px";b("FeDialogContent").style.height=(a-22)+"px";b("FeDialog_shadow").style.width=(c+2)+"px";b("FeDialog_shadow").style.height=(a+4)+"px";b("FeDialogIframe").style.width=c+"px";b("FeDialogIframe").style.height=(a-22)+"px";if(Fe.isIE==6){b("FeDialog_iframe").style.width=c+"px";b("FeDialog_iframe").style.height=(a-22)+"px"}};Fe.DialogFactory.prototype.hide=function(d){Fe.hide("FeDialog_"+this.hashCode);Fe.hide("FeDialog_shadow_"+this.hashCode);Fe.hide("FeDialog_iframe_"+this.hashCode);var b=this;if(Fe.G("FeDialogFactoryInset_"+b.hashCode)){var c=Fe.G("FeDialogFactoryInset_"+b.hashCode);var a=Fe.G("FeDialogContent_"+b.hashCode).childNodes[0];c.parentNode.insertBefore(a,c);c.parentNode.removeChild(c);a.style.display=b.opContentDisplay}if("function"==typeof(d)){d(b)}setTimeout(function(){b.active=false;b.setContent("&nbsp;");var f=Fe.G("FeDialogContent_"+b.hashCode);if(f){f.style.width=f.style.height=f.style.overflow=""}},50);clearTimeout(this.timer)};Fe.DialogFactory.prototype.dispose=function(){Fe.DialogFactory.dialogs=Fe.DialogFactory.dialogs.remove(this);Sys.prototype.dispose.call(this)};Fe.DialogFactory.prototype.setWidth=function(a){var b;if(b=Fe.G("FeDialogContent_"+this.hashCode)){b.style.width=a}};Fe.DialogFactory.prototype.setHeight=function(a){var b;if(b=Fe.G("FeDialogContent_"+this.hashCode)){b.style.height=a}};Fe.DialogFactory.prototype.setCaption=function(a){Fe.G("FeDialogCaptionText_"+this.hashCode).value=a};Fe.DialogFactory.prototype.setContent=function(a){Fe.G("FeDialogContent_"+this.hashCode).innerHTML=a};Fe.DialogFactory.prototype.setStatus=function(a){Fe.G("FeDialogStatusText_"+this.hashCode).innerHTML=a};Fe.DialogFactory.prototype.click=function(a){(window.event||a).cancelBubble=true;this.setActive()};Fe.DialogFactory.prototype.resizeBy=function(){var f=this,c="FeDialogLayer_"+this.hashCode;Fe.G("FeDialogBgLayer_"+this.hashCode).style.width=Fe.G(c).offsetWidth+"px";Fe.G("FeDialogBgLayer_"+this.hashCode).style.height=Fe.G(c).offsetHeight+"px";if(Sys.ie&&Fe.G("FeDialogBgLayer_"+this.hashCode)){if(Fe.isIE<5.5){Fe.G("FeDialogLayer_"+this.hashCode).style.width="130px";Fe.G("FeDialog_"+this.hashCode).style.width=Fe.G("FeDialogLayer_"+this.hashCode).offsetWidth+"px"}var d=Fe.G("FeDialogBgLayer_"+this.hashCode);if(d.rows[0].cells[0].currentStyle){var b=parseInt(d.rows[0].cells[0].currentStyle.height);var a=parseInt(d.rows[2].cells[0].currentStyle.height);if(!isNaN(b)&&!isNaN(a)){d.rows[1].cells[1].style.height=(Math.max(Fe.G(c).offsetHeight-b-a,12))+"px"}}}if(window.opera&&Fe.G("FeDialogLayerTable_"+this.hashCode).offsetWidth<130){Fe.G("FeDialogLayerTable_"+this.hashCode).style.width="130px"}this.timer=setTimeout(function(){f.resizeBy()},50)};Fe.DialogFactory.produce=function(){for(var a=0,b=Fe.DialogFactory.dialogs.length;a<b;a++){if(!Fe.DialogFactory.dialogs[a].active){if(a==(b-1)){setTimeout(function(){new Fe.DialogFactory().create()},20)}return Fe.DialogFactory.dialogs[a]}}return null};Fe.on(document.body,"onkeydown",function(c){var b=c.target||c.srcElement;if(!b){return false}if(b.tagName.toLowerCase()=="textarea"){return false}var a=c.which||c.keyCode;if(Fe.DialogFactory.currentDialog&&Fe.DialogFactory.currentDialog.active){if(a==27){Fe.DialogFactory.currentDialog.oncancel()}else{if(a==13){Fe.DialogFactory.currentDialog.onaccept();try{c.keyCode=0}catch(d){}c.returnValue=false}}}});function BdLockWindow(){Sys.call(this);this.initialize()}BdLockWindow.Extends(Sys,"BdLockWindow");BdLockWindow.prototype.initialize=function(){var div=this.element=BdLockWindow.element=document.createElement("DIV");div.id=BdLockWindow.id;this.decontrol();with(div.style){zIndex=1;top=left="0px";width=height="100%";border=display="none";margin=padding=0;position="absolute";backgroundColor="#666699";backgroundImage="url("+Sys.resourcePath+"/blank.gif)"}if((Fe.isIE&&Fe.isIE<5.5)||(Fe.isOpera&&Fe.isOpera<8.5)){div.style.backgroundColor="";div.style.backgroundImage="url("+Sys.resourcePath+"/dotted.gif)"}BdLockWindow.onResize();document.body.insertBefore(div,document.body.firstChild)};Fe.extend(BdLockWindow,{onResize:function(){BdLockWindow.element.style.width="100%";BdLockWindow.element.style.height="100%";setTimeout(function(){var b=Fe.body();var a=b.documentWidth-4;var c=b.documentHeight-4;if(Fe.isIE){a-=5;c-=5}BdLockWindow.element.style.width=a+"px";BdLockWindow.element.style.height=c+"px"},100)},_restore:function(a){var c=document.getElementsByTagName(a);for(var b=c.length-1;b>-1;b--){c[b].style.visibility=c[b].getAttribute("att_BdLockWindow_v")||"";c[b].removeAttribute("att_BdLockWindow_v")}},_safeguard:function(a){var c=document.getElementsByTagName(a);for(var b=c.length-1;b>-1;b--){if(c[b].className=="safe"){continue}c[b].setAttribute("att_BdLockWindow_v",c[b].style.visibility,0);c[b].style.visibility="hidden"}},id:"BdLockWindow_"+Sys.getUniqueId()+"_"+new Date().getTime().toString(36),lock:function(d){var g=this;if(!g.instance){g.instance=new BdLockWindow()}Fe.show(g.id);Fe.on(window,"onresize",g.onResize);var c=g.element.style;g.onResize();var h=Fe.extend({zIndex:1,opacity:0.5},d||{});c.zIndex=h.zIndex;c.backgroundColor=h.backgroundColor||"#666699";if(Fe.isIE){c.filter="alpha(opacity:"+Math.round(h.opacity*100)+")"}else{c.opacity=h.opacity;c.MozOpacity=h.opacity;c.KHTMLOpacity=h.opacity}for(var b=["SELECT","OBJECT","EMBED"],f=0,a=b.length;f<a;f++){this._safeguard(b[f])}},unlock:function(){if(!this.instance){this.instance=new BdLockWindow();return}Fe.hide(this.id);Fe.un(window,"onresize",this.onResize);for(var b=["SELECT","OBJECT","EMBED"],c=0,a=b.length;c<a;c++){this._restore(b[c])}}});if(Fe.isIE&&Fe.isIE<7){try{document.execCommand("BackgroundImageCache",false,true)}catch(e){}}Fe.Dialog.open=function(b,c){if(!FeDialogInited){new Fe.DialogFactory().create();FeDialogInited=true}var a=new Fe.Dialog();a.render(b,c);return a},Fe.Dialog.alert=function(a,b){return this.open(a,Fe.extend({buttonbar:true,buttonAccept:true},b||{}))},Fe.Dialog.alertErrorFace=function(a,c,d){var b='<div class="alertFacePanel">';b+='<div class="errorFaceTitle">'+a+"</div>";b+='<div class="faceContent">'+c+"</div>";b+="</div>";return Fe.Dialog.alert(b,d)},Fe.Dialog.alertMessageFace=function(a,c,d){var b='<div class="alertFacePanel">';b+='<div class="messageFaceTitle">'+a+"</div>";b+='<div class="faceContent">'+c+"</div>";b+="</div>";return this.open(b,Fe.extend({buttonbar:true,buttonAccept:true},d||{}))},Fe.Dialog.confirm=function(a,b){return this.open(a,Fe.extend({locked:true,buttonbar:true,buttonAccept:true,buttonCancel:true},b||{}))},Fe.Dialog.showModalDialog=function(a,b){return this.open(a,Fe.extend({locked:true,position:"center"},b||{}))},Fe.Dialog.submit=function(b,d){var a=this.open("about:blank",Fe.extend({contentType:"page"},d||{}));var c=b.target;b.target=a.getIframe().name;b.submit();b.target=c;return a},Fe.Dialog.close=function(){for(var b=0,c=Fe.DialogFactory.dialogs.length;b<c;b++){var a=Fe.DialogFactory.dialogs[b];if(a.active&&typeof(a.onclose=="function")){a.onclose()}}};Fe.Dialog.dragDrop={initialMouseX:undefined,initialMouseY:undefined,startX:undefined,startY:undefined,draggedObject:undefined,draggedElement:undefined,initElement:function(c,a,d,b){Fe.on(c,"mousedown",Fe.Dialog.dragDrop.startDragMouse);Fe.Dialog.dragDrop.element=a;Fe.Dialog.dragDrop.shadow=d;Fe.Dialog.dragDrop.iframe=b},startDragMouse:function(b){Fe.Dialog.dragDrop.startDrag();var a=b||window.event;a.cancelBubble=true;if(a.stopPropagation){a.stopPropagation()}Fe.Dialog.dragDrop.initialMouseX=a.clientX;Fe.Dialog.dragDrop.initialMouseY=a.clientY;Fe.on(document,"mousemove",Fe.Dialog.dragDrop.dragMouse);Fe.on(document,"mouseup",Fe.Dialog.dragDrop.releaseElement);return false},startDrag:function(){if(Fe.Dialog.dragDrop.draggedObject){Fe.Dialog.dragDrop.releaseElement()}Fe.Dialog.dragDrop.startX=Fe.Dialog.dragDrop.element.offsetLeft;Fe.Dialog.dragDrop.startY=Fe.Dialog.dragDrop.element.offsetTop;Fe.Dialog.dragDrop.draggedObject=Fe.Dialog.dragDrop.element},dragMouse:function(d){var c=d||window.event;var b=c.clientX-Fe.Dialog.dragDrop.initialMouseX;var a=c.clientY-Fe.Dialog.dragDrop.initialMouseY;Fe.Dialog.dragDrop.setPosition(b,a);return false},setPosition:function(b,a){var c=Fe.Dialog.dragDrop;c.element.style.left=c.startX+b+"px";c.element.style.top=c.startY+a+"px";if(Fe.isIE==6){c.iframe.style.left=c.startX+b+"px";c.iframe.style.top=c.startY+a+"px"}c.shadow.style.left=c.startX+b+3+"px";c.shadow.style.top=c.startY+a+3+"px"},releaseElement:function(){Fe.un(document,"mousemove",Fe.Dialog.dragDrop.dragMouse);Fe.un(document,"mouseup",Fe.Dialog.dragDrop.releaseElement);Fe.Dialog.dragDrop.draggedObject=null}};