window.baidu=window.baidu||{version:"1-0-0",emptyFn:function(){}};baidu.isString=function(a){return(typeof a=="object"&&a&&a.constructor==String)||typeof a=="string"};baidu.swf=baidu.swf||{};baidu.swf.getMovie=function(a){return document[a]||window[a]};baidu.swf.getVersion=function(){var e=navigator;if(e.plugins&&e.mimeTypes.length){var a=e.plugins["Shockwave Flash"];if(a&&a.description){return a.description.replace(/([a-zA-Z]|\s)+/,"").replace(/(\s)+r/,".")+".0"}}else{if(window.ActiveXObject&&!window.opera){for(var d=10;d>=2;d--){try{var b=new ActiveXObject("ShockwaveFlash.ShockwaveFlash."+d);if(b){return d+".0.0";break}}catch(c){}}}}};baidu.swf.createHTML=function(f){f=f||{};var o=baidu.swf.getVersion(),i=1,h=f.ver||"6.0.0",l,e;if(o){o=o.split(".");h=h.split(".");for(var n=0;n<3;n++){l=parseInt(o[n],10);e=parseInt(h[n],10);if(e<l){break}else{if(e>l){i=0;break}}}}else{i=0}if(!i){return""}var m=f.vars,a,c,j,g=["classid","codebase","id","width","height","align"];f.align=f.align||"middle";f.classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000";f.codebase="http://fpdownload.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=6,0,0,0";f.movie=f.url||"";delete f.vars;delete f.url;if(baidu.isString(m)){f.flashvars=m}else{var b=[];for(a in m){j=m[a];if(j){b.push(a+"="+encodeURIComponent(j))}}f.flashvars=b.join("&")}var k=["<object "];for(n=0,c=g.length;n<c;n++){j=g[n];k.push(" ",j,'="',f[j],'"')}k.push(">");var d={wmode:1,scale:1,quality:1,play:1,loop:1,menu:1,salign:1,bgcolor:1,base:1,allowscriptaccess:1,allownetworking:1,allowfullscreen:1,seamlesstabbing:1,devicefont:1,swliveconnect:1,flashvars:1,movie:1};for(a in f){j=f[a];if(d[a]&&j){k.push('<param name="'+a+'" value="'+j+'" />')}}f.src=f.movie;f.name=f.id;delete f.id;delete f.movie;delete f.classid;delete f.codebase;f.type="application/x-shockwave-flash";f.pluginspage="http://www.macromedia.com/go/getflashplayer";k.push("<embed");for(a in f){j=f[a];if(j){k.push(" ",a,'="',j,'"')}}k.push("></embed></object>");return k.join("")};baidu.swf.create=function(d,b){d=d||{};var a=baidu.swf.createHTML(d),c=true;if(b&&baidu.isString(b)){b=document.getElementById(b)}if(a.length<=0){a=d.errorMessage||"";c=false}if(b){b.innerHTML=a}else{document.write(a)}return c};