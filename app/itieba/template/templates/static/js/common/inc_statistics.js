var TbStatsParams={};var Statistics={addParams:function(b,c){var a=this.getUrl(b,c);_html=b.innerHTML;b.href=a;b.innerHTML=_html},getUrl:function(d,f){var b="";b=d.href;var e=b;var c="";var a=b.indexOf("#");if(a>=0){e=b.slice(0,a);c=b.slice(a)}e+=(e.indexOf("?")==-1?"?":"&");b=e+f+c;return b},sendRequest:function(b){if(document.images){var a=new Image();window["bd_pv_"+(new Date().getTime())]=a;a.src="http://static.tieba.baidu.com/tb/img/pv.gif?"+b+"&t="+new Date().getTime();a=null}},onElementClick:function(f){var f=window.event||f;var d=f.target||f.srcElement;if(d.tagName!="A"&&d.tagName!="INPUT"&&d.tagName!="IMG"){return}var b=this.id;var h=d;var c="";var a="";var g="";while(true){c=h.getAttribute("st_type")||h.getAttribute("cId");if(c){g="area="+c;if(c in TbStatsParams){a=h.getAttribute("st_value");g=TbStatsParams[c]+(a==null?"":("&st_value="+a))}Statistics.sendRequest(g);break}if(h.id==b||h.tagName=="BODY"){break}else{h=h.parentNode}}}};Statistics.init=function(a,d){var c=document.getElementById(a);if(c){if(typeof d=="object"){for(var b in d){TbStatsParams[b]=d[b]}}Fe.on(c,"click",Statistics.onElementClick)}};var Stats=Statistics;