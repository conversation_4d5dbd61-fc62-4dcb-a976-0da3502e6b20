function acDiv(b,h,g,c,j,a){this.div=b;this.x=h;this.y=g;this.width=c;this.height=j;this.src=a;this.name="acdiv";var f=this;try{window.attachEvent("onload",i)}catch(d){window.addEventListener("load",i,false)}function i(){f.init();f.hide()}}acDiv.prototype.init=function(h,b,g,d,a,f){var e=this;var h=e.div;if(typeof(h)=="string"){h=document.getElementById(h)}e.root=document.createElement("div");b=b||e.x;g=g||e.y;d=d||e.width;a=a||e.height;f=f||e.src;var c=e.root;document.body.appendChild(c);c.style.position="absolute";c.style.left=b+"px"||0;c.style.top=g+"px"||0;c.style.width=d+"px";c.style.height=a+"px";c.style.zIndex="65536";e.st=c.style.visibility;if(h){c.appendChild(h)}else{e.createFrame(f)}};acDiv.prototype.hide=function(){var b=this;var a=b.root.style.visibility;if(a!="hidden"){b.root.style.visibility="hidden"}};acDiv.prototype.show=function(){var b=this;var a=b.root.style.visibility;if(a=="hidden"){b.root.style.visibility="visible"}var c=new Date().getTime();b.ifr.src=b.src+"?t="+c};acDiv.prototype.sh=function(){var b=this;var a=b.root.style.visibility;if(a=="hidden"){b.show()}else{b.hide()}};acDiv.prototype.createFrame=function(c){var a=this;var d=document.all;if(d){try{d=document.createElement("<iframe frameborder=0 scrolling='no'>")}catch(b){d=document.createElement("iframe")}}else{d=document.createElement("iframe")}a.root.appendChild(d);d.src=c||"about:blank";d.frameBorder="0";d.scrolling="no";d.width=a.width;d.height=a.height;a.ifr=d};