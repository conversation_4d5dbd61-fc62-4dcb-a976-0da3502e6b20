<html>
<head>
<meta http-equiv=content-type content="text/html; charset=gb2312">
<title>IE下用js修改链接的href值，css设置的visited会失效问题</title>
<style>
a:link{color:black;}
a:visited{color:red;}
</style>
</head>
<body>
若使用相对地址，点击后，再刷新页面，通过js修改的链接地址，链接颜色会失效。<br>
<a href="a_link.html" id="link" target="_blank">使用相对地址，链接颜的颜色失效</a>
<br><br>
若使用全地址，点击后，就会有效<br>
<a href="http://tieba.baidu.com/" id="link_2" target="_blank">使用绝对地址，链接颜的颜色有效</a>
<script>
var ItiebaStatistics = {
	addParams : function(link_ele,params){
		var _href = "";//链接的href值
		_href = link_ele.href;
		var _real_url = _href;//链接的url真实值
		var _hash = "";//链接的hash值
		var _hash_pos = _href.indexOf("#");//链接的hash位置
		if(_hash_pos >= 0){
			_real_url = _href.slice(0,_hash_pos);
			_hash = _href.slice(_hash_pos);
		}
		_real_url += (_real_url.indexOf("?") == -1 ? "?" : "&");
		_html = link_ele.innerHTML;
		link_ele.href = _real_url + params + _hash;//加空格的原因是 在动态修改链接的 href 时，如果链接文字中包含 @ 符号，则会触发这个 bug，导致链接文字同时被修改。(被修改的需要是绝对路径，如 http://www.qq.com)
		link_ele.innerHTML = _html;
	}
};

var _link = document.getElementById("link");
_link.style.display = "none";
ItiebaStatistics.addParams(_link, "a=1&b=2");
_link.style.display = "";


_link = document.getElementById("link_2");
_link.style.display = "none";
ItiebaStatistics.addParams(_link, "a=1&b=2");
_link.style.display = "";
</script>
</body>
 </html>