String.prototype.trim=function(){return this.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)/g,"")};var NewVote={isDescriptionOpen:false,isImageOpen:false,isAdvanceOpen:false,clickTitle:function(a){this.clearTitleInputError(a)},clearTitleInputError:function(b){var a=this;setTimeout(function(){var c=Fe.G("voteTitleMsg");if(c.innerHTML=="�����������ܳ���31������"&&(Fe.String.getByteLength(b.value))<=62){a.showCommonMsg("voteTitleMsg","������31������")}if(c.innerHTML=="���ⲻ��Ϊ��"&&(Fe.String.trim(b.value))!=""){a.showCommonMsg("voteTitleMsg","������31������")}},500)},clearDescriptionInputError:function(b){var a=this;setTimeout(function(){var c=Fe.G("voteDescriptionMsg");if(c.innerHTML=="�����������ܳ���200������"&&(Fe.String.getByteLength(b.value))<=400){a.showCommonMsg("voteDescriptionMsg","������200������")}},500)},clearOptionsInputError:function(b){var a=this;setTimeout(function(){var c=Fe.G("optionsMsg");if(c.innerHTML=="����������д����"&&a._getRealInputNum()>=2){a.showCommonMsg("optionsMsg","������д����")}},500)},clickAddDescription:function(b){var a=NewVote;if(a.isDescriptionOpen){Fe.G("addDescription").innerHTML="�������";Fe.hide("voteDescriptionPanel");a.hideError("voteDescriptionMsg");a.isDescriptionOpen=false;a.addOutFrameHeight(-110)}else{Fe.G("addDescription").innerHTML="�ر�����";Fe.show("voteDescriptionPanel");Fe.G("voteDescriptionMsg").innerHTML="";a.showCommonMsg("voteDescriptionMsg","������200������");a.addOutFrameHeight(110);a.isDescriptionOpen=true;if(Fe.String.trim(Fe.G("voteDescription").value)==""){Fe.G("voteDescription").focus()}}b.blur()},addOutFrameHeight:function(a){var b=parent.Fe.G("voteBoxIframe");b.height=(parseInt(b.height.replace("px",""))+a)+"px"},find:function(f,d,e){var g=f||document;var b=g.getElementsByTagName(d);var a=[];for(var c=0;c<b.length;c++){if(b[c].className.indexOf(e)>-1){a.push(b[c])}}return a},setVisible:function(a,c){for(var b=0;b<a.length;b++){if(c){Fe.show(a[b])}else{Fe.hide(a[b])}}},checkImage:function(b){var a=this;setTimeout(function(){var d=b.value.trim();var e=b.id.substring("optionItemURL_".length);var c=Fe.G("optionItemImage_"+e).getElementsByTagName("IMG")[0];if(a._isImageURL(d)){a._previewImage(b.id,"optionItemURLMsg_"+e,d,c)}else{a._showDefaultImage(c)}},500);return true},_isImageURL:function(c){var b=c.toLowerCase();var a=/^(https:\/\/|http:\/\/|ftp:\/\/|rtsp:\/\/|mms:\/\/)/;var d=/(.jpg|.jpeg|.bmp|.png|.gif|.tif)$/;return(b!=""&&(Fe.String.getByteLength(b)<=1024)&&a.test(b)&&d.test(b))},_showDefaultImage:function(a){a.src="http://static.tieba.baidu.com/tb/img/vote/defaultVoteImage.png";a.style.width="150px";a.style.height="150px";a.style.left="0px";a.style.top="0px"},clickAddOption:function(c){var g=PageData.add_option_num;var b=PageData.max_option_num;var h=NewVote;var j=Fe.G("optionsPanel");var a=h.isImageOpen;var f=h._getInputNum();if(f<b){var k=f+g-b;if(k>0){k=g-k}else{k=g}for(var d=f+1;d<f+1+k;d++){var e=document.createElement("DIV");e.id="option_p"+d;e.className="optionPanel";j.appendChild(e);e.innerHTML="";e.innerHTML=h._buildOption(d,a)}h.addOutFrameHeight(93)}c.blur()},_getInputNum:function(){return this.find(Fe.G("optionsPanel"),"DIV","optionNum").length},_getRealInputNum:function(){var c=this.find(Fe.G("optionsPanel"),"INPUT","optionItemText");var a=0;for(var b=0;b<c.length;b++){if(c[b].value.trim()!=""){a++}}return a},_getErrorInputNum:function(){var b=this;var d=0;var c=b._getInputNum();for(var a=1;a<=c;a++){if(Fe.G("optionItemTextMsg_"+a).innerHTML!=""){d++}}return d},_buildOption:function(a,d){var c=d?"":' style="display:none" ';var b='<div class="optionNum">'+a+'</div><div class="optionContentPanel"><input id="optionItemText_'+a+'" type="text" onkeydown="NewVote.clearOptionsInputError(this);" onpaste="NewVote.clearOptionsInputError(this);" class="optionItemText" onchange="NewVote.showMutiSelect()"><div id="optionItemTextMsg_'+a+'"></div><div id="optionImage_'+a+'" class="optionImage" '+c+' >    ͼƬ���ӣ�<input id="optionItemURL_'+a+'" onfocus="NewVote.checkImage(this)" onblue="NewVote.checkImage(this)" onpaste="NewVote.checkImage(this)" onchange="NewVote.checkImage(this)" type="text" class="optionItemURL">    <div id="optionItemURLMsg_'+a+'" class="optionItemURLMsg"></div>   <div id="optionItemImage_'+a+'" class="optionItemImage">        <img src="http://static.tieba.baidu.com/tb/img/vote/defaultVoteImage.png">    </div></div></div><div style="clear:both;height:0px;line-height:0px;"></div>';return b},selectLimiteType:function(a){if(a.value==0){Fe.show("otherAdvanceOptionPanel")}else{Fe.hide("otherAdvanceOptionPanel")}},hideMutiSelectItems:function(){Fe.hide("mutiSelectItems")},showMutiSelect:function(){if(!Fe.G("mutiSelect").checked){return}var f=this;var b=Fe.G("mutiSelectItems");b.innerHTML="&nbsp;����ѡ&nbsp;";var d=document.createElement("SELECT");d.id="mutiSelectNum";var e=f._getRealInputNum();if(e<2){e=2}for(var c=2;c<=e;c++){var a=document.createElement("option");a.text=c;a.value=c;d.options.add(a)}if(e<=2){d.disabled=true}b.appendChild(d);b.innerHTML+="&nbsp;��";Fe.show("mutiSelectItems")},changeMutiSelect:function(){var a=this;if(Fe.G("mutiSelect").checked){a.showMutiSelect()}},showCommonMsg:function(a,b){if(typeof a=="string"){a=Fe.G(a)}a.style.color="#666";a.innerHTML=b;a.style.display=""},showError:function(a,b){if(typeof a=="string"){a=Fe.G(a)}a.style.color="#ff0000";a.innerHTML=b;a.style.display=""},hideError:function(a){if(typeof a=="string"){a=Fe.G(a)}a.innerHTML="";a.style.display="none"},_buildParams:function(a){var b="";for(property in a){if(a[property]){b+="&"+property+"="+encodeURIComponent(a[property])}}return b.length>0?b.substring(1):""},tryAddVote:function(a){Fe.isLogined(function(c){var b=NewVote;if(!c){parent.Fe.Dialog.alert("<div style='padding:20px 50px;'>�����˳���¼�����¼�����·���</div>",{title:"����ͶƱʧ��",width:350})}else{b.addVote(a)}})},addVote:function(elem){var me=this;var curErrorNum=me._getErrorInputNum();var input=me.checkInput(false);var newErrorNum=me._getErrorInputNum();var heightChange=(newErrorNum-curErrorNum)*20;me.addOutFrameHeight(heightChange);if(input.result){var url="/vote/commit/add_vote?alt=json";var data=input.data;var pstr=me._buildParams(data)+"&ie=utf-8";Fe.Ajax.post(url,pstr,function(xmlHttp){var rs=eval("("+xmlHttp.responseText+")");if(rs.errno==0){NewVote.addVoteToForum(rs,data.title)}else{if(rs.errno==6){NewVote.showWhereError(rs.errmsg);var newErrorNum=me._getErrorInputNum();var heightChange=(newErrorNum-curErrorNum)*20;me.addOutFrameHeight(heightChange)}else{Fe.Dialog.alertErrorFace("����ͶƱʧ��",rs.errmsg.replace(/\r?\n/gi,"<br>"),{title:"����ʧ��",width:350})}}})}},showWhereError:function(f){var e=this._getInputNum();for(var c=0;c<f.length;c++){var b=f[c];if(b[0]==1){this.showError("voteTitleMsg",b[1])}else{if(b[0]==2){this.showError("voteDescriptionMsg",b[1])}else{var d=2;for(var a=1;a<=e;a++){if(Fe.G("optionItemText_"+a).value.trim()!=""){d++;if(d==b[0]){this.showError("optionItemTextMsg_"+a,b[1])}}}}}}},addVoteToForum:function(voteData,title){var me=this;var content=voteData.vote_id+","+voteData.sign_id;var params=Fe.format("cmd=#{0}&tbs=#{1}&title=#{2}&content=#{3}&itieba_id=#{4}&tid=#{5}&vote_id=#{6}","add_thread",PageData.tbs,encodeURIComponent(title),encodeURIComponent(content),encodeURIComponent(PageData.itieba.inner_id),"",encodeURIComponent(voteData.vote_id));var url="/i/commit?ie=utf-8&stamp="+new Date().getTime();Fe.Ajax.post(url,params,function(xmlHttp){var json=eval("("+xmlHttp.responseText+")");if(json.is_done){setTimeout(function(){json.title=title;NewVote.addVoteRelation(json)},300)}else{Fe.Dialog.alertErrorFace("����ͶƱ��ʧ��",json._info.replace(/\r?\n/gi,"<br>"),{title:"����ʧ��",width:350})}})},addVoteRelation:function(data){var me=this;var postData={product_name:"itieba",id_1:data.ret.tid,id_2:data.ret.fid,vote_id:data.ret.vid,sign_id:data.ret.sign};var url="/vote/commit/add_vote_relation?alt=json";var str=me._buildParams(postData)+"&ie=utf-8";Fe.Ajax.post(url,str,function(xmlHttp){var rs=eval("("+xmlHttp.responseText+")");if(rs.errno==0){setTimeout(function(){var newAction=parent.ItiebaPost.buildFeedAction(data.ret.tid,data.title);var pageName=parent.iHome.getCurPageName();var pageNum=parent.iHome.getCurPageNum();if(pageName=="all"){parent.ItiebaPost.flushPage(PageData.itieba.home_url)}else{if(pageNum==1){parent.iHome.insertPostListRow(newAction);parent.VotePostTool.is_new=true;parent.MainPostTool.show();parent.Fe.G("postTitle").value="";parent.Fe.G("titleErrorTip").innerHTML="";parent.Fe.hide("titleErrorTip");parent.Fe.G("postTitle").focus()}else{if(pageName==""){parent.ItiebaPost.flushPage(PageData.itieba.home_url)}else{parent.ItiebaPost.flushPage()}}}},500)}else{Fe.Dialog.alertErrorFace("����ͶƱ����ʧ��",rs.errmsg,{title:"����ʧ��",width:350})}})},previewVote:function(){var a="http://static.tieba.baidu.com/tb/static-itieba/previewvote.html?t="+new Date().getTime();window.open(a,"newVotePreview","menubar=yes, resizable=yes,toolbar=yes, scrollbars=yes, location=yes")},clickShowInfo:function(a){var c=Fe.Dom.getOffset(a);var b=Fe.G("poptips");b.style.left=c.left+"px";b.style.top=(c.top-160)+"px";Fe.show("poptips")},_createVoteData:function(){return{product_name:"itieba",title:"",content:"",expire_time:"",item_type:"0",max_select_num:1,perm:"0"}},checkInput:function(m){var z=this;var l=z._createVoteData();var s=true;var w=z.isImageOpen;var A=Fe.G("voteTitle").value.trim();if(A==""){s=false;z.showError("voteTitleMsg","���ⲻ��Ϊ��")}else{if(Fe.String.getByteLength(A)>62){s=false;z.showError("voteTitleMsg","�����������ܳ���31������")}else{z.showCommonMsg("voteTitleMsg","������31������")}}l.title=A;var t="";z.showCommonMsg("voteDescriptionMsg","������200������");if(z.isDescriptionOpen){t=Fe.G("voteDescription").value.trim();if(Fe.String.getByteLength(t)>400){s=false;z.showError("voteDescriptionMsg","�����������ܳ���200������")}}l.content=t;var x=z._getRealInputNum();if(x<2){s=false;z.showError("optionsMsg","����������д����")}else{z.showCommonMsg("optionsMsg","������д����")}var a=z.find(Fe.G("optionsPanel"),"INPUT","optionItemText");var k,c;var d=false;var e=false;var y=[];for(var v=1;v<=a.length;v++){k=Fe.G("optionItemText_"+v).value.trim();c=Fe.G("optionItemURL_"+v).value.trim();var q=false;var b=false;if(z.isImageOpen){if(k==""&&c==""){z.hideError("optionItemTextMsg_"+v);z.hideError("optionItemURLMsg_"+v);continue}if(k==""){s=false;z.showError("optionItemTextMsg_"+v,"ѡ���Ϊ��")}else{if(Fe.String.getByteLength(k)>150){s=false;z.showError("optionItemTextMsg_"+v,"ѡ���������ܳ���75������")}else{q=true;z.hideError("optionItemTextMsg_"+v)}}if(c==""){e=true;z.showError("optionItemURLMsg_"+v,"��ͼƬ����Ϊ��")}else{if((!z._isImageURL(c))||Fe.G("optionItemURL_"+v).loadError){e=true;z.showError("optionItemURLMsg_"+v,"��ͼƬ������Ч")}else{d=true;b=true;z.hideError("optionItemURLMsg_"+v)}}if(m){y.push([v,k,(b?c:"")])}else{if(q){y.push([v,k,(b?c:"")])}}}else{if(k==""){z.hideError("optionItemTextMsg_"+v);z.hideError("optionItemURLMsg_"+v);continue}else{if(Fe.String.getByteLength(k)>150){s=false;z.showError("optionItemTextMsg_"+v,"ѡ���������ܳ���75������")}else{q=true;z.hideError("optionItemTextMsg_"+v)}}if(m){y.push([v,k,""])}else{if(q){y.push([v,k,""])}}}}if(e){if(d){s=false}else{w=false}}l.item_type=w?"1":"0";l.max_select_num=Fe.G("mutiSelect").checked?Fe.G("mutiSelectNum").value:1;if(NewVoteTime._isCustomEndTime){var f=Fe.G("customEndTime").value.trim();if(f==""){s=false;z.showError("time_error_div","��ѡ��ͶƱ��Ч��")}else{NewVoteTime.showCalendarTime(f)}}l.expire_time=Fe.G("endTime").value;l.perm="0";var g="";if(NewVote.isAdvanceOpen){var p=z._getRadiosValue("limiteType");if(p==0){l.perm=Fe.G("ipLimite").checked?(1+""):"0"}else{l.perm=p+"";if(l.perm=="4"){l.perm="3"}if(p==3||p==4){g=p+""}}}if(m){l.vote_properties=[{property_key:"forum_id",property_value:PageData.forumID},{property_key:"forum_name",property_value:PageData.forumName_u},{property_key:"forum_power",property_value:g}];l.vote_items=[];for(var r=0;r<y.length;r++){var h="";if(l.item_type==1){if(y[r][2]==""){h="http://static.tieba.baidu.com/tb/img/vote/defaultVoteImage.png"}else{h=y[r][2]}}l.vote_items.push({item_no:(r+1),item_title:y[r][1],item_content:h,item_count:0})}l.errno="0";l.error="����ɹ�";l.vote_id="123456";l.is_expired="0";l.status="0";l.commit_time=new Date().getTime();l.commit_uid=PageData.userID;l.commit_uname=PageData.userName;l.commit_ip=PageData.userIP;l.is_votable="1";l.total_count="0";l.show_result=PageData.show_result}else{l.attr_key_1="forum_name";l.attr_value_1=PageData.forumName;l.attr_key_2="forum_id";l.attr_value_2=PageData.forumID;if(g!=""){l.attr_key_3="forum_power";l.attr_value_3=g}for(var r=0;r<y.length;r++){var o=y[r][0];l["item_title_"+o]=y[r][1];if(l.item_type==1){l["item_content_"+o]=(y[r][2]==""?"http://static.tieba.baidu.com/tb/img/vote/defaultVoteImage.png":y[r][2])}}}return{result:s,data:l}},_getRadiosValue:function(c){var a=document.getElementsByName(c);for(var b=0;b<a.length;b++){if(a[b].checked){return a[b].value}}}};var NewVoteTime={_isCustomEndTime:false,changeSelectOption:function(c){var a=new Date();var b=parseInt(c.value);switch(b){case 7:a=Fe.Date.addDays(a,7);break;case 30:a=Fe.Date.addMonths(a,1);break;case 182:a=Fe.Date.addMonths(a,6);break;case 365:a=Fe.Date.addMonths(a,12);break}this._setEndTime(a)},_setEndTime:function(b){var d="����ͶƱ����yyyy��MM��dd�ս�ֹ";var c="yyyy-MM-dd HH:mm:ss";c=Fe.Date.format(b,c);d=Fe.Date.format(b,d);var a=Fe.G("endTimeInfo");a.innerHTML=d;a.style.display="";Fe.G("endTime").value=c},custom:function(a){Fe.hide("fixEndTimePanel");Fe.hide("endTimeInfo");Fe.show("customEndTimePanel");NewVote.hideError("time_error_div");this._isCustomEndTime=true},cancelCustom:function(b){Fe.show("fixEndTimePanel");Fe.hide("customEndTimePanel");NewVote.hideError("time_error_div");Fe.G("endTime").value="";var a=Fe.G("endTimeSelect");var c=a.selectedIndex;a.options[c].selected=false;a.options[0].selected=true;this.initEndTime();this._isCustomEndTime=false},showCalendarTime:function(d){var c=d.split("-");var a=Fe.G("endTimeInfo");if(c.length==3&&a){var b=new Date();b.setFullYear(c[0],parseInt(c[1])-1,parseInt(c[2]));this._setEndTime(b)}Fe.hide("time_error_div")},initEndTime:function(){var a=new Date();a=Fe.Date.addDays(a,7);this._setEndTime(a)}};var PreviewImg={resizeImg:function(a,c,e,d,b){var f=new Image();f.onload=function(){var i=PreviewImg.getRightWH(f.width,f.height,e,d);var g=(e-i[0])/2;var h=(d-i[1])/2;a.style.width=i[0]+"px";a.style.height=i[1]+"px";a.style.left=g+"px";a.style.top=h+"px";a.src=c;var j=false;if(typeof(b)!="undefined"){b(j)}};f.onerror=function(){var g=true;if(typeof(b)!="undefined"){b(g)}};f.src=c},getRightWH:function(a,d,b,f){var c=0,e=a,g=d;if(a>b){c+=1}if(d>f){c+=2}switch(c){case 1:e=b;g=d*b/a;case 2:g=f;e=a*f/d;case 3:g=(d/f>a/b)?f:d*b/a;e=(d/f>a/b)?a*f/d:b}e=parseInt(e);g=parseInt(g);return[e,g]}};