var iStyle={saveStyle:function(){var me=this;var selectedStyle=Fe.G("selectedStyle").value;var selectedImg=Fe.G("selectedImg").value;var params=Fe.format("cmd=#{0}&style_id=#{1}&icon_str=#{2}&itieba_id=#{3}&tbs=#{4}","update_style",selectedStyle,selectedImg,PageData.itieba.inner_id,PageData.tbs);var url="/i/commit?stamp="+new Date().getTime();Fe.Ajax.post(url,params,function(xhr){var json=eval("("+xhr.responseText+")");if(json.is_done){top.location.reload()}else{if(json.error_no==4){Fe.showLoginLayer()}else{var html='<div style="padding:18px 0 12px 0;text-align:center;">'+json._info+"</div>";Fe.Dialog.alert(html,{width:"260px",height:"90px",title:"��ʽ����ʧ��"})}}})},cancelStyle:function(){var b=Fe.G("currentStyle").value;var c=Fe.G("currentImg").value;var a=Fe.G("selectedStyle").value;var d=Fe.G("selectedImg").value;if(b==a&&c==d){Fe.Dialog.close()}else{Fe.Dialog.close();top.location.reload()}}};