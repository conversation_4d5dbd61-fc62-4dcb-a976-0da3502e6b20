<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @date 2009-7-23
 * @version
 */
//������֤����
class ValidateRules {
    public static $config;
    //һ���򵥵��ж���֤���Ƿ���Ч�ķ���
    public static function isVaildRules($key) {
        $key = trim ( strval ( $key ) );
        if( strlen ( $key ) == 0) {
            return false;
        }
        if( ! is_array ( self::$config ) || count ( self::$config ) == 0) {
            return false;
        }
        return isset ( self::$config [$key] );
    }
}
ValidateRules::$config = array (
    
    'word' => array (
        
        array (
            
            'type' => 'string', 
            'check' => 'empty', 
            'error' => '�ؼ��ʲ���Ϊ��' 
        ), 
        array (
            
            'type' => 'string', 
            'check' => 'length', 
            'param' => 200, 
            'error' => '�ؼ��ʲ��ܳ���200����' 
        ) 
    ), 
    'id' => array (
        
        array (
            
            'type' => 'int', 
            'check' => 'notzero', 
            'error' => 'ID����Ϊ0' 
        ) 
    ) 
);