<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR> z<PERSON><PERSON><PERSON>@baidu.com
 * @date 2009-11-17
 * @version
 */
class ItiebaFollowService {
    const MUST_FOLLOW = 5;
    const OPTIONAL_FOLLOW = 6;
    public static function searchFollow($arrInput) {
        if( isset ( $arrInput ['user_name'] )) {
            $arrInput ['word'] = $arrInput ['fname'];
            unset ( $arrInput ['fname'] );
        }
        //����type������������5����6��������Ǿͻ�Ϊ5
        $arrInput ['type'] = self::_processType ( $arrInput ['type'] );
        $result = MisWordService::searchWord ( $arrInput );
        if($result['total'] > 0){
            $arrAllUserItiebaInfo = array();
            foreach ($result['data'] as &$row){
                $uname = $row['word'];
                $uid = PUserInfoUtil::getUidByUn($uname);
                if(!isset($arrAllUserItiebaInfo[$uid])){
                    $arrItiebaInfo = UserRelationService::getItiebaInfoByUid($uname,$uid);
                    $arrAllUserItiebaInfo[$uid] = $arrItiebaInfo;
                }
                $row['nick_name'] = $arrAllUserItiebaInfo[$uid]['user_nickname'];
            }
        }
        return $result;
    }
    public static function deleteFollow($arrInput) {
        $arrInput ['type'] = self::_processType ( $arrInput ['type'] );
        return ServiceHelper::deleteMisWord ( $arrInput, ServiceContext::get ( 'user' ) );
    }
    public static function addFollow($arrInput) {
        $arrInput ['type'] = self::_processType ( $arrInput ['type'] );
        $uid = PUserInfoUtil::getUidByUn ( $arrInput ['user_name'] );
        if( $uid === false) {
            ServiceContext::append ( 'feedback', "{$arrInput['user_name']}����һ����Ч�û�" );
            return false;
        }
        $arrItiebaInfo = UserRelationService::getItiebaInfoCreateByUid ( $arrInput ['user_name'], $uid );
        $arrParam = DataAccessUtils::fetchArray ( $arrInput, array (
            
            'type' 
        ) );
        $arrParam ['itieba_id'] = $arrItiebaInfo ['itieba_id'];
        $arrParam ['word'] = $arrInput ['user_name'];
        $ret = ServiceHelper::addMisWord ( $arrParam, ServiceContext::get ( 'user' ) );
        usleep ( 500 );
        if( $ret === ServiceDefine::RETURN_SUCCESS) {
            ServiceContext::append ( 'feedback', "��ӹ�ע�û�{$arrInput['user_name']}�ɹ�" );
            return true;
        }
        else if( $ret === ServiceDefine::RETURN_EXIST) {
            ServiceContext::append ( 'feedback', "��ע�û�{$arrInput['user_name']}�Ѵ���" );
            return true;
        }
    }
    private static function _processType($type) {
        if( $type != self::MUST_FOLLOW && $type != self::OPTIONAL_FOLLOW) {
            $type = self::MUST_FOLLOW;
        }
        return $type;
    }
}