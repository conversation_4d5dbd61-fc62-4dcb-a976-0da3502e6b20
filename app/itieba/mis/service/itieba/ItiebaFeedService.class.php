<?php
/**
* @copyright Copyright (c) www.baidu.com
* <AUTHOR> <PERSON><PERSON><PERSON><PERSON>@baidu.com
* @date 2009-12-4
* @version
*/
class ItiebaFeedService {
    const TYPE_IPFEED = 8;
    public static function searchIpFeed($arrInput){
        $arrInput['type'] = self::TYPE_IPFEED;
        $result = MisWordService::searchWord ( $arrInput );
        if($result['total'] > 0){            
            foreach ($result['data'] as &$row){
                $uname = $row['user_name'];
                $uid = $row['user_id'];
                $arrItiebaInfo = UserRelationService::getItiebaInfoByUidFromCache($uname,$uid);
                $row['nick_name'] = $arrItiebaInfo['user_nickname'];
            }
        }       
        return $result;
    } 
    public static function addIpFeed($arrInput){
        $arrInput ['type'] = self::TYPE_IPFEED;
        $uid = PUserInfoUtil::getUidByUn ( $arrInput ['user_name'] );
        if( $uid === false) {
            ServiceContext::append ( 'feedback', "{$arrInput['user_name']}����һ����Ч�û�" );
            return false;
        }
        $arrItiebaInfo = UserRelationService::getItiebaInfoCreateByUid ( $arrInput ['user_name'], $uid );
        if( $arrItiebaInfo ['user_type'] == 0) {
            ServiceContext::append ( 'feedback', "{$arrInput['user_name']}����һ����֤�û�" );
            return false;
        }
        $arrParam = DataAccessUtils::fetchArray ( $arrInput, array ( 'province','city', 'user_name') );
        $arrParam ['user_id'] = $uid;
        $arrParam ['word'] = $arrInput ['user_name'];
        $arrParam ['itieba_id'] = $arrItiebaInfo['itieba_id'];
        //��cmҪһ��ID
        $intFeedId = GlobalIDService::newMisID();
        $arrParam['feed_id'] = $intFeedId;
        $arrParam['type'] = self::TYPE_IPFEED;
        //������4���ֶ��жϴ��Ƿ����
        $arrExistCond = array('word','type','province','city');
        $ret = ServiceHelper::addMisWord ( $arrParam, ServiceContext::get('user'),$arrExistCond);
        usleep ( 500 );
        if( $ret === ServiceDefine::RETURN_SUCCESS) {            
            $arrDalInput = array();
            $arrDalInput['feed_id'] = $intFeedId;
            $arrDalInput['user_name'] = $arrParam['user_name'];
            $arrDalInput['user_id'] = $uid;
            $arrDalInput['province'] = $arrParam['province'];
            $arrDalInput['city'] = $arrParam['city'];
            $arrDalInput['itieba_id'] = $arrParam ['itieba_id'];
            ServiceHelper::fillOpUserInfoFromContext($arrDalInput);
            ServiceHelper::fillOpUserIPInfoFromContext($arrDalInput);
            MisService::callDal('addIpFeed',$arrDalInput);
            ServiceContext::append ( 'feedback', "����û�{$arrInput['user_name']}�ĵ���Ĭ�Ϲ�ע���óɹ�" );
            return true;
        }
        else if( $ret === ServiceDefine::RETURN_EXIST) {
            ServiceContext::append ( 'feedback', "�û�{$arrInput['user_name']}�ĵ���Ĭ�Ϲ�ע�����Ѵ���" );
            return true;
        }               
    }
    public static function deleteIpFeed($arrInput){
        $arrInput ['type'] = self::TYPE_IPFEED;
        $arrDetail = MisWordService::getWordByID ( $arrInput );
        if( empty ( $arrDetail )) {
            return false;
        }
        $arrDetail['type'] = self::TYPE_IPFEED;;
        $arrDalInput = array();
        $arrDalInput['feed_id'] = $arrDetail['feed_id'];
        ServiceHelper::fillOpUserInfoFromContext($arrDalInput);
        ServiceHelper::fillOpUserIPInfoFromContext($arrDalInput);   
        MisService::callDal('deleteIpFeed',$arrDalInput);    
        ServiceHelper::deleteMisWord ( $arrDetail, ServiceContext::get('user'));        
    }
}