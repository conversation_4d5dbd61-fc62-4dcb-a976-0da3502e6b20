<?php
/**
* @copyright Copyright (c) www.baidu.com
* <AUTHOR> <PERSON><PERSON><PERSON><PERSON>@baidu.com
* @date 2009-11-27
* @version
*/
class ITBSearchAction extends MisViewAction {
    //��������ID��ѯ
    const SEARCH_BY_THREAD = 'thread';
    //ʹ��tinyse��ѯ
    const SEARCH_BY_TINYSE = 'tinyse';
    //ָ��ĳ��i���ɽ��в�ѯ
    const SEARCH_BY_ITIEBA = 'itieba';
    protected function _config($obj = null){
        $type = Request::getParam('type',1);
        $str = 'mis_search_list';
        if($type == ItiebaPostService::POST_SEARCH_TYPE_RECYCLE_THREAD ||
            $type == ItiebaPostService::POST_SEARCH_TYPE_RECYCLE_POST){
            $str .= '_del';
        }
        //mistable
        $this->_setConfig(self::KEY_MIS_TABLE,$str);
        //ʹ�õ�service
        $this->_setConfig(self::KEY_SEARCH_SERVICE,'ItiebaPostService');
        $searchBy = Request::getParam('search_by',self::SEARCH_BY_TINYSE);
        switch ($searchBy) {
        	case self::SEARCH_BY_TINYSE:
        	$this->_setConfig(self::KEY_SEARCH_METHOD,'searchTinysePost');
        	break;
        	case self::SEARCH_BY_THREAD:
        	$this->_setConfig(self::KEY_SEARCH_METHOD,'searchThread');
        	break;
        	case self::SEARCH_BY_ITIEBA:
        	$this->_setConfig(self::KEY_SEARCH_METHOD,'searchItieba');
        	break;        	        	
        	default:
        	$this->_setConfig(self::KEY_SEARCH_METHOD,'searchTinysePost');
        	break;
        }
        
    }
   //��ʼ��ѯǰ��������������
   protected function _beforeQuery(&$arrInput){
        $searchBy = Request::getParam('search_by',self::SEARCH_BY_TINYSE);
        $intIsSpecial = Request::getNumric ( 'search_is_special' );
        $arrInput ['is_special'] = $intIsSpecial;
        $type = Request::getParam('type',1);
        $arrInput['search_type'] = $type;
        switch ($searchBy) {
        	case self::SEARCH_BY_TINYSE:
        	$arrInput = $this->_processTinyseInput($arrInput);
        	break;
        	case self::SEARCH_BY_THREAD:
        	$arrInput = $this->_processThreadInput($arrInput);
        	break;
        	case self::SEARCH_BY_ITIEBA:
        	$arrInput = $this->_processItiebaInput($arrInput);
        	break;        	        	
        	default:
        	$arrInput = $this->_processTinyseInput($arrInput);
        	break;
        }
       
   }
   private function _processTinyseInput($arrInput){        
        $arrInput = $this->_processKeywordInput($arrInput);
        //�ؼ��ֺ��û���������һ��
        if( ! isset ( $arrInput ['keyword'] ) && 
            ! isset ( $arrInput ['user_name'] )) {
            MisView::addMisFeedback('�ؼ��ֻ��û���������һ��');
            return false;
        }
        return $arrInput;
   }
   private function _processThreadInput($arrInput){
       $intThreadID = Request::getNumric('search_thread');
       if($intThreadID == 0){
           MisView::addMisFeedback('����ID����Ϊ0');
           return false;
       }
       $arrInput['thread_id'] = $intThreadID;
       return $arrInput;
   }
   private function _processItiebaInput($arrInput){
       $arrInput = $this->_processKeywordInput($arrInput);
       $intIsNickName = Request::getNumric('search_is_nickname');
       $str = Request::getStr('search_itieba');
       if($str == ''){
           MisView::addMisFeedback('����һ����Ч��i����');
           return false;           
       }
       if ($intIsNickName == 0){
          $arrInput['itieba_name'] = $str;
       }else{
          $arrItiebaInfo = UserRelationService::getItiebaInfoByNickName($str);
          if ($arrItiebaInfo === false ){
           MisView::addMisFeedback('����һ����Ч��i����');
           return false;                  
          }
          $arrInput['itieba_name'] = $arrItiebaInfo['user_name'];
       }
       
       return $arrInput;
   }
   private function _processKeywordInput($arrInput){
        $str = Request::getParam ( 'search_uname', '' );
        if( strlen ( $str ) != 0) {
            $arrInput ['user_name'] = $str;
        }
        $str = Request::getParam ( 'search_keyword', '' );
        if( strlen ( $str ) != 0) {
            $arrInput ['keyword'] = $str;
        }
        $str = Request::getParam ( 'search_ip', '' );
        if( strlen ( $str ) > 0) {
            $ip = CTools::ipToNum ( $str );
            $arrInput ['ip'] = $ip;
        }   
        return $arrInput;    
   }
   //����ط������ǵ���excel���
   protected function _afterQuery(&$arrData){
       if (Request::isExist('toexcel')){
       		MisLog::debug("post search to excel");
       		$strContent = "itieba_id\tthread_id\tpost_id\t�û���\t����֤�û�\t�ǳ�\t����\t����\tip\t����ʱ��\t¥��\t������\t����i����\t����i��������\t����i�����ǳ�\n";
       		foreach ($arrData as $row){
       			$strContent .= "{$row['itieba_id']}\t{$row['thread_id']}\t{$row['post_id']}\t{$row['user_name']}\t".
       						($row['is_vip'] >0 ?'��':'����')."\t{$row['nick_name']}\t{$row['title']}\t"
       						.str_replace(array("\n","\r","\n\r"),"",$row['content'])."\t".
       						CTools::ipToNum($row['ip'])."\t".date("Y-m-d H:i:s",$row['post_time'])."\t{$row['floor_num']}\t".
       						($row['is_topic']>0?'��':'����')."\t{$row['itieba_name']}\t".($row['itieba_type']>0?'��':'����')."\t{$row['itieba_nick_name']}\n";
            			
       		}
				Header("Content-type: application/octet-stream");
			    Header("Accept-Ranges: bytes");
			    header("Cache-Control:"); 
				Header("Accept-Length: ".strlen($strContent));
				Header("Content-Disposition: attachment; filename=post.xls");
		        echo $strContent;       		
       		exit;
       }
   }
}