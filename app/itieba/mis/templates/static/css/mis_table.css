#nav {
	float: left;
	width: 230px;
	background: #BFEE66; /* Green */
}

#mis_split {
	background: #E6E6E6;
	cursor: pointer;
	font-weight: bold;
}

#search_input {
	margin: 20px;
	margin-bottom: 50px
}

#nav_header {
	text-align: center;
}

#nav_content dl {
	margin-left: 6px;
	margin-top: 10px;
}

#nav_content dt {
	font-weight: bold;
	font-size: 14px;
	cursor: pointer;
}

#nav_content dd {
	margin-left: 1em;
}

.mis_nav_split {
	margin-top: 20px;
	margin-bottom: 5px;
}

#mis_list_table {
	margin: 0 auto;
}

#mis_list_table {
	border-left: 1px solid #000;
	border-top: 1px solid #000;
}

#mis_list_table th,#mis_list_table td {
	border-right: 1px solid #000;
	border-bottom: 1px solid #000;
	word-break: break-all;
}

#mis_list_table td {
	padding-left: 0.3em;
}

#mis_list_table th {
	color: #fff;
	background: #0061AD;
}

#mis_list_table table td {
	border: none;
}

#mis_list_table .alt {
	background: #eee;
}

.operation textarea {
	width: 242px;
	overflow: auto;
}

.operation select {
	width: 165px;
}

.detail_popup {
	position: absolute;
	display: none;
	z-index: 100;
	width: 400px;
	height: 180px;
	zoom: 1
}

.detail_popup iframe {
	display: none;
	_display: block;
	border: none;
	position: absolute;
	top: 0px;
	left: 0px;
	width: 100%;
	height: 100%;
	z-index: 200;
	filter: alpha(Opacity =           0);
}

.detail_popup_inner {
	position: absolute;
	width: 400px;
	height: 180px;
	overflow: auto;
	border: 1px solid #4E4B3F;
	background: #EEE5C0;
	z-index: 300;
}

#table_footer,#table_header {
	margin: 0 auto;
	width: 98%;
}

.important {
	color: red;
}

#pager {
	text-align: center;
	margin: 20px 0;
}

#mis_nav {
	background: #eee;
}

#welcome {
	color: red;
}

#mis_area {
	background: #1864D6;
	color: #fff;
	margin: 5px 0;
}

#welcome,#mis_area {
	font-size: 14px;
}

.large_checkbox {
	width: 20px;
	height: 20px;
}

#mis_radio_search {
	margin: 10px;
	margin-bottom: 5px;
}

#mis_radio_search label {
	margin-right: 5px;
}

#search_wrap {
	margin: 10px;
}

#search_wrap p {
	margin-bottom: 10px;
}

.add_form {
	margin: 10px;
}

.add_form h3 {
	background: #8986C8;
}

.post_title {
	color: #1864D6;
}