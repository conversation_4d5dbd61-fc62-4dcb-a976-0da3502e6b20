/* 
 * My97 DatePicker 4.1
 * Ƥ������:default
 * Ƥ������:My97
 */ /* ����ѡ������ DIV */
.WdateDiv {
	width: 180px;
	background-color: #FFFFFF;
	border: #bbb 1px solid;
	padding: 2px;
}

.WdateDiv * {
	font-size: 9pt;
}

/****************************
 * ����ͼ��
 ***************************/
.WdateDiv .NavImg {
	cursor: pointer;
	width: 16px;
	height: 16px;
	margin-top: 1px;
}

.WdateDiv .NavImgll {
	background: url(img.gif) no-repeat;
}

.WdateDiv .NavImgl {
	background: url(img.gif) no-repeat -16px 0px;
}

.WdateDiv .NavImgr {
	background: url(img.gif) no-repeat -32px 0px;
}

.WdateDiv .NavImgrr {
	background: url(img.gif) no-repeat -48px 0px;
}

/****************************
 * ����·����
 ***************************/ /* ����·��� DIV */
.WdateDiv #dpTitle {
	height: 24px;
	margin-bottom: 2px;
	padding: 1px;
}

/* ����·������ INPUT */
.WdateDiv .yminput {
	margin-top: 2px;
	text-align: center;
	border: 0px;
	height: 16px;
	width: 50px;
	cursor: pointer;
}

/* ����·�������ý���ʱ����ʽ INPUT */
.WdateDiv .yminputfocus {
	margin-top: 2px;
	text-align: center;
	font-weight: bold;
	color: blue;
	border: #ccc 1px solid;
	height: 16px;
	width: 50px;
}

/* �˵�ѡ��� DIV */
.WdateDiv .menuSel {
	position: absolute;
	background-color: #FFFFFF;
	border: #ccc 1px solid;
	display: none;
}

/* �˵�����ʽ TD */
.WdateDiv .menu {
	cursor: pointer;
	background-color: #fff;
}

/* �˵���mouseover��ʽ TD */
.WdateDiv .menuOn {
	cursor: pointer;
	background-color: #BEEBEE;
}

/* �˵���Чʱ����ʽ TD */
.WdateDiv .invalidMenu {
	color: #aaa;
}

/* ��ѡ����ƫ�� DIV */
.WdateDiv .YMenu {
	margin-top: 16px;
}

/* ��ѡ����ƫ�� DIV */
.WdateDiv .MMenu {
	margin-top: 16px; *
	width: 62px;
}

/* ʱѡ����λ�� DIV */
.WdateDiv .hhMenu {
	margin-top: -90px;
	margin-left: 26px;
}

/* ��ѡ����λ�� DIV */
.WdateDiv .mmMenu {
	margin-top: -46px;
	margin-left: 26px;
}

/* ��ѡ����λ�� DIV */
.WdateDiv .ssMenu {
	margin-top: -24px;
	margin-left: 26px;
}

/****************************
 * �����
 ***************************/
.WdateDiv .Wweek {
	text-align: center;
	background: #DAF3F5;
	border-right: #BDEBEE 1px solid;
}

/****************************
 * ����,�������
 ***************************/ /* ������ TR */
.WdateDiv .MTitle {
	background-color: #BDEBEE;
}

/* ��������� TABLE */
.WdateDiv .WdayTable {
	line-height: 20px;
	border: #c5d9e8 1px solid;
}

/* ���ڸ����ʽ TD */
.WdateDiv .Wday {
	cursor: pointer;
}

/* ���ڸ��mouseover��ʽ TD */
.WdateDiv .WdayOn {
	cursor: pointer;
	background-color: #C0EBEF;
}

/* ��ĩ���ڸ����ʽ TD */
.WdateDiv .Wwday {
	cursor: pointer;
	color: #FF2F2F;
}

/* ��ĩ���ڸ��mouseover��ʽ TD */
.WdateDiv .WwdayOn {
	cursor: pointer;
	color: #000;
	background-color: #C0EBEF;
}

.WdateDiv .Wtoday {
	cursor: pointer;
	color: blue;
}

.WdateDiv .Wselday {
	background-color: #A9E4E9;
}

/* �����·ݵ����� */
.WdateDiv .WotherDay {
	cursor: pointer;
	color: #6A6AFF;
}

/* �����·ݵ�����mouseover��ʽ */
.WdateDiv .WotherDayOn {
	cursor: pointer;
	background-color: #C0EBEF;
}

/* ��Ч���ڵ���ʽ,�������ڷ�Χ�������ڸ����ʽ,����ѡ������� */
.WdateDiv .WinvalidDay {
	color: #aaa;
}

/****************************
 * ʱ�����
 ***************************/ /* ʱ���� DIV */
.WdateDiv #dpTime {
	float: left;
	margin-top: 3px;
	margin-right: 30px;
}

/* ʱ������ SPAN */
.WdateDiv #dpTime #dpTimeStr {
	margin-left: 1px;
}

/* ʱ������� INPUT */
.WdateDiv #dpTime input {
	height: 16px;
	width: 18px;
	text-align: center;
	border: #ccc 1px solid;
}

/* ʱ�� ʱ INPUT */
.WdateDiv #dpTime .tB {
	border-right: 0px;
}

/* ʱ�� �ֺͼ���� ':' INPUT */
.WdateDiv #dpTime .tE {
	border-left: 0;
	border-right: 0;
}

/* ʱ�� �� INPUT */
.WdateDiv #dpTime .tm {
	width: 7px;
	border-left: 0;
	border-right: 0;
}

/* ʱ���ұߵ����ϰ�ť BUTTON */
.WdateDiv #dpTime #dpTimeUp {
	height: 10px;
	width: 13px;
	border: 0px;
	background: url(img.gif) no-repeat -32px -16px;
}

/* ʱ���ұߵ����°�ť BUTTON */
.WdateDiv #dpTime #dpTimeDown {
	height: 10px;
	width: 13px;
	border: 0px;
	background: url(img.gif) no-repeat -48px -16px;
}

/****************************
 * ����
 ***************************/
.WdateDiv #dpQS {
	float: left;
	margin-right: 3px;
	margin-top: 3px;
	background: url(img.gif) no-repeat 0px -16px;
	width: 20px;
	height: 20px;
	cursor: pointer;
}

.WdateDiv #dpControl {
	text-align: right;
	margin-top: 3px;
}

.WdateDiv .dpButton {
	height: 20px;
	width: 45px;
	border: #ccc 1px solid;
	padding: 2px;
}