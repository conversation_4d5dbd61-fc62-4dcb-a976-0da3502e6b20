<?php

/**
 * Project : all
 * User: zhaoshiya
 * Date: 2018/12/17
 * Time: 上午11:34
 *
 */
class Service_Tool_Tool extends Libs_BaseService {
    /**
     * @param $tids
     * @return array
     */
    public static function getThreadsInfo($tids) {
        $mgetThreadInput = array(
            'thread_ids' => $tids,
            'need_abstract' => 1,
            'forum_id' => 1,
            'need_photo_pic' => 1,
            'need_user_data' => 1,
            'icon_size' => 1,
            'need_forum_name' => 1,
            'call_from' => 'pc_frs',
            'need_post_content' => 1,
            'structured_content' => 1,
        );
        $res = Tieba_Service::call('post', 'mgetThread', $mgetThreadInput, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $res['errno']) {
            Bingo_Log::warning('Failed to get thread info, output: ' . serialize($res));
            return array();
        }

        $mgetThreadRes = $res['output'];
        foreach ($mgetThreadRes['thread_list'] as $tid => &$threadInfo) {
            $threadInfo['user_name'] = $mgetThreadRes['thread_user_list'][$threadInfo['user_id']]['user_name'];
        }
        return $mgetThreadRes;
    }

    /**
     * 根据pid 取到相应帖子的详细内容
     * @param $pids
     * @return array
     */
    public static function getPostInfoByPostIds($pids) {
        if (!$pids) {
            return array();
        }
        $getPostInfoInput = array(
            "post_ids" => $pids,
        );

        $res = Tieba_Service::call('post', 'getPostInfo', $getPostInfoInput, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $res['errno']) {
            Bingo_Log::warning('Failed to get post info, output: ' . serialize($res));
            return array();
        }
        $postInfoRes = $res['output'];
        $postRet = array();
        foreach ($postInfoRes as $onePost) {
            $postRet[intval($onePost['post_id'])] = $onePost;
        }
        return $postRet;
    }

    /**
     * @param $fids
     * @return array
     */
    public static function getForumInfo($fids) {
        if (!$fids) {
            return array();
        }
        foreach ($fids as $k => &$oneFid) {
            if (!$oneFid) {
                unset($fids[$k]);
            }
        }
        $forumInfoInput = array(
            'forum_id' => $fids,
        );
        $res = Tieba_Service::call('forum', 'mgetBtxInfo', $forumInfoInput, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $res['errno']) {
            Bingo_Log::warning('Failed to get forum info, output: ' . serialize($res));
            return array();
        }
        $forumInfoRes = $res['output'];
        return $forumInfoRes;
    }

    /**
     * return true if this thread is deleted and false if call service failed or not deleted
     * @param $tid
     * @return bool
     */
    public static function checkIfDeleted($arrInput) {
        $checkTid = true;
        if ($arrInput['check_pid'] == true) {
            $checkTid = false;

        }
        $tid = $arrInput['thread_id'] ? $arrInput['thread_id'] : 1;
        $pid = $arrInput['post_id'] ? $arrInput['post_id'] : 0;

        $tidArr = array($tid);
        $pidArr = array();
        if ($pid) {
            $pidArr[] = $pid;
        }

        $input = array(
            'input' => array(
                'thread_ids' => $tidArr,
                'post_ids' => $pidArr,
            ),
        );
        $ret = Tieba_Service::call('post', 'getMaskInfo', $input, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning('error get mask info, input: ' . serialize($input));
            return false;
        }
        if ($checkTid) {
            return $ret['output']['threads_mask_status'][$tid]['is_key_deleted'];
        }
        return $ret['output']['posts_mask_status'][$pid]['is_key_deleted'];
    }

    /**
     * @description 获取ext_attr
     * @param array
     * @return
     */
    public static function getExtAttr($arrPostData) {
        $arrExtAttr = array();
        foreach ($arrPostData['ext_attr'] as $arrAttr) {
            $arrExtAttr[$arrAttr['key']] = $arrAttr['value'];
        }
        return $arrExtAttr;
    }


    /**
     * @param $input
     * @return array
     */
    public static function getBjhThreadInfo($input) {
        $nid = $input['nid'];
        $type = $input['type'];

        $input = array(
            'nids' => array(
                $nid
            ),
            'types' => array(
                $type
            ),
            'need_tb_format' => 1,
        );
        $ret = Tieba_Service::call("thirdparty", 'getBJHThreadContent', $input,
            null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning('Failed to get thread data.' . serialize($ret));
            return self::errRet($ret['errno']);
        }
        $data = $ret['output'][$nid];
        $title = $data['title'];
        $content = self::processBjhContent($data['content']);
        $imgUrls = self::processBjhImage($data);

        $info = array(
            'title' => Bingo_Encode::convert($title, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8),
            'rawdata' => Bingo_Encode::convert($content, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8),
            'utf8_title' => $title,
            'utf8_rawdata' => $content,
            'image_urls' => $imgUrls,
            'image_num' => count($imgUrls),
        );
        return self::succRet($info);
    }

    /**
     * @param $data
     * @return array
     */
    private static function processBjhImage($data) {
        //$imgUrls = $data['imageurls'];
        $imageArr = array();
        $content = $data['content'];
        foreach ($content as $key => $value) {
            if ('image' == $value['type']) {
                $imageArr[] = $value['data']['original']['url'];
            }
        }
        return $imageArr;

    }

    /**
     * @param $data
     * @return string
     */
    private static function processBjhContent($data) {
        $strArr = array();
        foreach ($data as $key => $value) {
            if ('text' == $value['type']) {
                $strArr[] = $value['data'];
            }
        }
        return join("\n", $strArr);
    }

    /**
     * 是否是百家号帖子
     * @param $arrInput
     * @return bool
     */
    public function isBjhThread($arrInput) {
        $arrExtAttr = Service_Tool_Tool::getExtAttr($arrInput);
        if (isset($arrExtAttr['nid']) && isset($arrExtAttr['bjh_tid'])) {
            return true;
        }
        return false;
    }

}