<?php
$methods_to_subservices =
    array(
        'followTabBusi' =>
            array(
                'subService' => 'busi',
                'filename' => 'busi',
            ),
        'pushNeedReAuditToUeg' =>
            array(
                'subService' => 'busi',
                'filename' => 'busi',
            ),
        'pushToUegAudit' =>
            array(
                'subService' => 'busi',
                'filename' => 'busi',
            ),
        'executeDigestCall' =>
            array(
                'subService' => 'interaction',
                'filename' => 'digest',
            ),
        'executeFeatureCall' =>
            array(
                'subService' => 'interaction',
                'filename' => 'feature',
            ),
        'getFileContent' =>
            array(
                'subService' => 'interaction',
                'filename' => 'image',
            ),
        'getOnePicOneFeature' =>
            array(
                'subService' => 'interaction',
                'filename' => 'image',
            ),
        'getOnePicHash' =>
            array(
                'subService' => 'interaction',
                'filename' => 'image',
            ),
        'processOnePic1' =>
            array(
                'subService' => 'interaction',
                'filename' => 'image',
            ),
        'processOnePic' =>
            array(
                'subService' => 'interaction',
                'filename' => 'image',
            ),
        'getFileName' =>
            array(
                'subService' => 'interaction',
                'filename' => 'image',
            ),
        'executeImageFeature' =>
            array(
                'subService' => 'interaction',
                'filename' => 'image',
            ),
        'multiCallInteraction' =>
            array(
                'subService' => 'videointeraction',
                'filename' => 'interaction',
            ),
        'executeQualityCall' =>
            array(
                'subService' => 'interaction',
                'filename' => 'quality',
            ),
        'getUegFeature' =>
            array(
                'subService' => 'interaction',
                'filename' => 'uegfeature',
            ),
        'addThreadManually' =>
            array(
                'subService' => 'nmq',
                'filename' => 'nmq',
            ),
        'receiveNmq' =>
            array(
                'subService' => 'nmq',
                'filename' => 'nmq',
            ),
        'processAddPost' =>
            array(
                'subService' => 'nmq',
                'filename' => 'nmq',
            ),
        'addThreadPublic' =>
            array(
                'subService' => 'nmq',
                'filename' => 'nmq',
            ),
        'processAddThread' =>
            array(
                'subService' => 'nmq',
                'filename' => 'nmq',
            ),
        'processDeleteThread' =>
            array(
                'subService' => 'nmq',
                'filename' => 'nmq',
            ),
        'checkIfDeleted' =>
            array(
                'subService' => 'nmq',
                'filename' => 'nmq',
            ),
        'processEditVideo' =>
            array(
                'subService' => 'nmq',
                'filename' => 'video',
            ),
        'processDeleteVideoThread' =>
            array(
                'subService' => 'nmq',
                'filename' => 'video',
            ),
        'processAddVideoPost' =>
            array(
                'subService' => 'nmq',
                'filename' => 'video',
            ),
        'generalFeatureInput' =>
            array(
                'subService' => 'nmq',
                'filename' => 'video',
            ),
        'generalFinalFeature' =>
            array(
                'subService' => 'nmq',
                'filename' => 'video',
            ),
        'qualitySatisfied' =>
            array(
                'subService' => 'nmq',
                'filename' => 'video',
            ),
        'addRecord' =>
            array(
                'subService' => 'nmq',
                'filename' => 'nmq',
            ),
        'deleteFromImageTableByIds' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'deleteFromImageTable' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'replaceRecord' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'multiDeleteRecord' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'realDelete' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'realDeleteMaskInfo' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'rmLongTimeData' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'insertIntoMint' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'bugInsert' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'insertIntoRedisDb' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'offlineInsertRedisDb' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),

        'deleteFromRedisDb' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'getThreadInfoFromRedisDb' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'mgetThreadInfoFromRedisDb' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'mgetThreadInfoFromMint' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'getOneThreadInfo' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),
        'replaceVideoRecord' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),
        'deleteVideoRecord' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),
        'deleteVideoFromRedis' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),
        'getVideoThreadInfoFromRedis' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),
        'updateInfoOnAddPost' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),
        'getVideoThreadInfo' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),
        'videoUegFeature' =>
            array(
                'subService' => 'videointeraction',
                'filename' => 'uegfeatureimg',
            ),
        'getVideoInfoFromMgetthread' =>
            array(
                'subService' => 'videointeraction',
                'filename' => 'videodetail',
            ),
        'videofeatures' =>
            array(
                'subService' => 'videointeraction',
                'filename' => 'videofeature',
            ),
        'videoFrameContents' =>
            array(
                'subService' => 'videointeraction',
                'filename' => 'videoframe',
            ),
        'videoHashAndDatou' =>
            array(
                'subService' => 'videointeraction',
                'filename' => 'videohash',
            ),
        'videoText' =>
            array(
                'subService' => 'videointeraction',
                'filename' => 'videotext',
            ),
        'updateVideoFeature' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),
        'updateMultiVideoFeature' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),
        'deleteByTids' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'addVideoByManual' =>
            array(
                'subService' => 'nmq',
                'filename' => 'video',
            ),
        'videoRecomQuality' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),
        'updateMultiFeature' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'updateOneFeature' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'deleteVideoFromMint' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),
        'updateSignTable' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),
        'getVideoSign' =>
            array(
                'subService' => 'videointeraction',
                'filename' => 'videohash',
            ),
        'getSignAndInsert' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),
        'getInfoFromDatabase' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'updateVideoExpire' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),

        'getVideoFileContent' =>
            array(
                'subService' => 'interaction',
                'filename' => 'image',
            ),
        'getHashByBase64Content' =>
            array(
                'subService' => 'interaction',
                'filename' => 'image',
            ),
        'processImageNew' =>
            array(
                'subService' => 'interaction',
                'filename' => 'image',
            ),
        'getOnePicOneFeatureNew' =>
            array(
                'subService' => 'interaction',
                'filename' => 'image',
            ),
        'getHashByImageUrl' =>
            array(
                'subService' => 'interaction',
                'filename' => 'image',
            ),
        'setReAuditTag' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'setTagToRecom' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'manualDel' =>
            array(
                'subService' => 'store',
                'filename' => 'store',
            ),
        'getVideoFeatureFromMint' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),
        'getFeatureFromRedisAndInsertIntoMint' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),
        'delVideoManually' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),
        'processAddThreadImage' =>
            array(
                'subService' => 'interaction',
                'filename' => 'image',
            ),
        'getForumInfo' =>
            array(
                'subService' => 'tool',
                'filename' => 'tool',
            ),
        'executeForumFeature' =>
            array(
                'subService' => 'interaction',
                'filename' => 'forum',
            ),
        'insertVideoFeatureToMint' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),

        'insertVideoFeatureToMintNew' =>
            array(
                'subService' => 'store',
                'filename' => 'video',
            ),
        'deleteImageTextOdyssey' =>
            array(
                'subService' => 'odyssey',
                'filename' => 'imagetext',
            ),
        'getDynamicData' =>
            array(
                'subService' => 'odyssey',
                'filename' => 'imagetext',
            ),
        'commitImageTextOdyssey' =>
            array(
                'subService' => 'odyssey',
                'filename' => 'imagetext',
            ),
        'tiebaVideoUnderStand' =>
            array(
                'subService' => 'odyssey',
                'filename' => 'video',
            ),

        'saveDynamicDataIntoRedis' =>
            array(
                'subService' => 'odyssey',
                'filename' => 'imagetext',
            ),
        'getVideoHashFeaturesSaveIntoDb' =>
            array(
                'subService' => 'videointeraction',
                'filename' => 'videohash',
            ),
        'getVideoFeatureFromImgDb' =>
            array(
                'subService' => 'videointeraction',
                'filename' => 'videohash',
            ),
        'getEsSharp' =>
            array(
                'subService' => 'interaction',
                'filename' => 'image',
            ),
        'processBjhThread' =>
            array(
                'subService' => 'nmq',
                'filename' => 'bjh',
            ),
        'getNidByTid' =>
            array(
                'subService' => 'odyssey',
                'filename' => 'imagetext',
            ),
        'commitVideoBackToOdyssey' =>
            array(
                'subService' => 'odyssey',
                'filename' => 'video',
            ),
        'deleteVideoFromZp' =>   array(
            'subService' => 'odyssey',
            'filename' => 'video',
        ),
        'updateOdysseyInfo' =>
            array(
                'subService' => 'odyssey',
                'filename' => 'imagetext',
            ),
        'commitImageTextOdysseyB' =>
            array(
                'subService' => 'odyssey',
                'filename' => 'imagetext',
            ),
        'insertOdysseyTimeIntoDB' =>
            array(
                'subService' => 'odyssey',
                'filename' => 'imagetext',
            ),
        'getTidByNidFromRedis' =>
            array(
                'subService' => 'nmq',
                'filename' => 'bjh',
            ),
        'insertNidTid' =>
            array(
                'subService' => 'nid',
                'filename' => 'nid',
            ),
        'mgetRealTidByNid' =>
            array(
                'subService' => 'nid',
                'filename' => 'nid',
            ),
        'getRealTidByNid' =>
            array(
                'subService' => 'nid',
                'filename' => 'nid',
            ),
        'mgetRealNidByTid' =>
            array(
                'subService' => 'nid',
                'filename' => 'nid',
            ),
        'getRealNidByTid' =>
            array(
                'subService' => 'nid',
                'filename' => 'nid',
            ),
        'deleteNid' =>
            array(
                'subService' => 'nid',
                'filename' => 'nid',
            ),
        'deleteTooLongTimeNid' =>
            array(
                'subService' => 'nid',
                'filename' => 'nid',
            ),

        'getForumNumFeature' => array(
            'subService' => 'interaction',
            'filename' => 'foruminfo',
        ),
        'getForumPosts' => array(
            'subService' => 'interaction',
            'filename' => 'forumthread',
        ),
        'getUserRecentPost' => array(
            'subService' => 'interaction',
            'filename' => 'postrecent',
        ),
        'getUserRecentPostInForum' => array(
            'subService' => 'interaction',
            'filename' => 'recentfpost',
        ),
        'getUserRecentThreadInForum' => array(
            'subService' => 'interaction',
            'filename' => 'recentfthread',
        ),
        'getUserRecentThread' => array(
            'subService' => 'interaction',
            'filename' => 'threadrecent',
        ),
        'getUserFeature' =>  array(
            'subService' => 'interaction',
            'filename' => 'userfeature',
        ),
        'getUserFollow' => array(
            'subService' => 'interaction',
            'filename' => 'userfollow',
        ),
        'getUserPerm' => array(
            'subService' => 'interaction',
            'filename' => 'userperm',
        ),
        'getUserPost' => array(
            'subService' => 'interaction',
            'filename' => 'userpost',
        ),
        'getUserAgreeNum' => array(
             'subService' => 'interaction',
             'filename' => 'agree',
        ),
    );


