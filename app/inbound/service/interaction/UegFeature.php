<?php

/**
 * Created by PhpStorm.
 * User: z<PERSON><PERSON><PERSON>
 * Date: 2018/3/20
 * Time: 下午2:18
 */
class Service_Interaction_UegFeature extends Libs_BaseInteraction {


    CONST UEG_MODULE = 'uegfea';
    CONST UEG_METHOD = 'getFeature';

    public $params = array(
        'thread_id' => 'tid',
        'post_id' => 'pid',
    );

    /**
     * @param
     * @return
     */
    protected function call() {
        $this->checkParam();
        $ret = $this->callServer();
        $this->returnInfo = $ret;
    }


    /**
     * @param
     * @return array
     */
    private function callServer() {
        $input = array(
            'data' => array(
                'tieba_ueg_thread_rts' => array(
                    array(
                        'id' => intval($this->processedParam['pid']),
                    ),
                ),
            ),
        );
        //$res = ral($this->serverName, '', $this->processedParam, rand());
        $ret = Tieba_Service::call(self::UEG_MODULE, self::UEG_METHOD, $input, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning('Error occurred while calling server . server :'
                . $this->serverName . " input: " . serialize($this->processedParam) . " res: " . serialize($ret));
        }
        $pidRes = $ret['res']['tieba_ueg_thread_rts'][$this->processedParam['pid']];

        $sexScore = intval($pidRes['sexy_score']['value']);
        $pornScore = intval($pidRes['porn_score']['value']);

        $finalRet = array();
        if ($sexScore) {
            $finalRet['sexy'] = $sexScore / 1000;
        }
        if ($pornScore) {
            $finalRet['porn'] = $pornScore / 1000;

        }
        return $finalRet;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getUegFeature($arrInput) {
        $obj = new Service_Interaction_UegFeature($arrInput);
        $obj->call();
        return self::succRet($obj->returnInfo);
    }

}