<?php

/**
 * Created by PhpStorm.
 * User: z<PERSON><PERSON><PERSON>
 * Date: 2018/3/20
 * Time: 下午2:18
 */
class Service_Interaction_ForumThread extends Libs_BaseInteraction {


    CONST MODULE_NAME = 'post';
    CONST METHOD = 'queryUserForumCount';

    public $params = array(
        'user_id' => 'user_id',
        'forum_id' => 'forum_id'
    );

    /**
     * @param
     * @return
     */
    protected function call() {
        $this->checkParam();
        $ret = $this->callServer();
        $this->returnInfo = $ret;
    }


    /**
     * @param
     * @return array
     */
    private function callServer() {

        $ret = Tieba_Service::call(self::MODULE_NAME, self::METHOD, $this->processedParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning('Error occurred while calling server . server :'
                . " input: " . serialize($this->processedParam) . " res: " . serialize($ret));
        }
        $userFollowInfo = $ret['count'];

        $finalRet = array();
        if (isset($userFollowInfo['post_num']) && !empty($userFollowInfo['post_num'])) {
            $finalRet['user_forum_post'] = $userFollowInfo['post_num'];
        }
        if (isset($userFollowInfo['thread_num']) && !empty($userFollowInfo['thread_num'])) {
            $finalRet['user_forum_thread'] = $userFollowInfo['thread_num'];
        }
        return $finalRet;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getForumPosts($arrInput) {
        $obj = new Service_Interaction_ForumThread($arrInput);
        $obj->call();
        return self::succRet($obj->returnInfo);
    }

}