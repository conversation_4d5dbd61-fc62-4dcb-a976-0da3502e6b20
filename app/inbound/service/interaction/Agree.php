<?php

/**
 * Created by PhpStorm.
 * User: z<PERSON><PERSON><PERSON>
 * Date: 2018/3/20
 * Time: 下午2:18
 */
class Service_Interaction_Agree extends Libs_BaseInteraction {


    CONST MODULE_NAME = 'agree';
    CONST METHOD = 'getUserAgreeNum';

    public $params = array(
        'user_id' => 'user_id',
    );

    /**
     * @param
     * @return
     */
    protected function call() {
        $this->checkParam();
        $ret = $this->callServer();
        $this->returnInfo = $ret;
    }


    /**
     * @param
     * @return array
     */
    private function callServer() {
        $input = array(
            "agreed_user_id" => $this->processedParam['user_id'],
        );
        $ret = Tieba_Service::call(self::MODULE_NAME, self::METHOD, $input, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning('Error occurred while calling server . server :'
                . " input: " . serialize($input) . " res: " . serialize($ret));
        }
        $userFollowInfo = $ret['data'];

        $finalRet = array();
        if (isset($userFollowInfo['normal']) && !empty($userFollowInfo['normal'])) {
            $finalRet['user_total_agree'] = $userFollowInfo['normal'];
        }

        return $finalRet;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getUserAgreeNum($arrInput) {
        $obj = new Service_Interaction_Agree($arrInput);
        $obj->call();
        return self::succRet($obj->returnInfo);
    }

}