<?php

/**
 * Created by PhpStorm.
 * User: z<PERSON><PERSON><PERSON>
 * Date: 2018/3/20
 * Time: 下午2:18
 */
class Service_Interaction_ForumInfo extends Libs_BaseInteraction {


    CONST MODULE_NAME = 'forum';
    CONST METHOD = 'mgetBtxInfoEx';

    public $params = array(
        'forum_id' => 'forum_id',
    );

    /**
     * @param
     * @return
     */
    protected function call() {
        $this->checkParam();
        $ret = $this->callServer();
        $this->returnInfo = $ret;
    }


    /**
     * @param
     * @return array
     */
    private function callServer() {
        $input = array(
            'forum_id' => array(intval($this->processedParam['forum_id'])),
        );
        $ret = Tieba_Service::call(self::MODULE_NAME, self::METHOD, $input, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning('Error occurred while calling server . server :'
                . " input: " . serialize($input) . " res: " . serialize($ret));
        }
        $userFollowInfo = $ret['output'][$this->processedParam['forum_id']]['statistics'];

        $finalRet = array();
        if (isset($userFollowInfo['thread_num']) && !empty($userFollowInfo['thread_num'])) {
            $finalRet['forum_thread_num'] = intval($userFollowInfo['thread_num']);
        }
        if (isset($userFollowInfo['post_num']) && !empty($userFollowInfo['post_num'])) {
            $finalRet['forum_post_num'] = intval($userFollowInfo['post_num']);
        }
        if (isset($userFollowInfo['member_count']) && !empty($userFollowInfo['member_count'])) {
            $finalRet['forum_member_count'] = intval($userFollowInfo['member_count']);
        }
        return $finalRet;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getForumNumFeature($arrInput) {
        $obj = new Service_Interaction_ForumInfo($arrInput);
        $obj->call();
        return self::succRet($obj->returnInfo);
    }

}