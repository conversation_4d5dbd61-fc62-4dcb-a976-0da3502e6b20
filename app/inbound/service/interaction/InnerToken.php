<?php
/*
 * 面向百度内部应用，提供两种新的access token，一种供内部应用服务端调用Open API时使用，一种供内部应用基于浏览器的程序使用。
 * 这两种access token，都可以基于一定的规则，进行自验证。
 * 此SDK的目的，是尽可能的方便内部产品线生成此类access token，使相关细节对产品线透明。
 *
 * 其中，供基于浏览器的应用使用的access token，需要由此应用的服务端生成这个access token，然后传给浏览器客户端，浏览器缓存。
 * 浏览器被清cookie后，页面刷新时，服务端重新生成token，传给客户端。
 *
 * 使用这两种token需要申请授权，可参考：
 * 	http://wiki.babel.baidu.com/twiki/bin/view/Ecmp/OpenPlatform/Openapi_faq#如何申请内部token权限
 *
 * <code>
 * require_once 'InnerToken.php';
 * $appid = '8333446';
 * $uid = '67890';
 * $sk = 'x3yjA5YPqBRfCFeG1DoTcaDhzdXDkOZw';
 * $it = new InnerToken(11);
 * $token = $it->generateToken($appid, $uid, $sk);
 * if ($token === false) {
 * 	//错误处理
 * }
 * </code>
 *
 * <AUTHOR>
 * @note If you need C/C++ SDK, do not hesitate to let me know.
 */
class Service_Interaction_InnerToken {
    /**
     * 服务端请求Open API时，使用的access token的类型，常量
     * @var const int
     */
    const TOKEN_TYPE_SERVER = 11;

    /**
     * 基于浏览器的程序请求Open API时，使用的access token的类型，常量
     * @var const int
     */
    const TOKEN_TYPE_BROWSER = 10;

    /**
     * 这两种新access token的类型
     * @var int
     */
    protected $token_type;

    /**
     * token生成时的时间戳，用于签名
     * @var int
     */
    protected $timestamp;

    /**
     * 构造函数
     * @param int $token_type
     */
    public function __construct($token_type)
    {
        switch ((int)$token_type) {
            case self::TOKEN_TYPE_SERVER:
                $this->token_type = self::TOKEN_TYPE_SERVER;
                break;
            case self::TOKEN_TYPE_BROWSER:
                $this->token_type = self::TOKEN_TYPE_BROWSER;
                break;
            default:
                $this->token_type = 0;//invalid
        }

        $this->timestamp = time();
    }

    /**
     * 签名函数
     * browser oriented: sign=md5(timestamp+cookie_BAIDUID+appid+sk)
     * server oriented: sign=md5(timestamp+passport_ID+appid+sk)
     * @param int $appid 应用的ID
     * @param int $id (服务端用token时，为passport ID) or string (浏览器用token时，为cookie baiduid)
     * @param string $sk 应用的secret key
     * @return string or bool false
     */
    protected function sign($appid, $id, $sk)
    {
        if ((int)$appid < 1 || !isset($sk[31]) || !ctype_alnum($sk))
            return false;

        switch ($this->token_type) {
            case self::TOKEN_TYPE_SERVER:
                //passport ID
                if (!ctype_digit($id))
                    return false;
                return md5($this->timestamp . $id . $appid . $sk);
            case self::TOKEN_TYPE_BROWSER:
                //cookie BAIDUID
                if (empty($id))
                    return false;
                return md5($this->timestamp . $id . $appid . $sk);
            default:
                return false;
        }
    }

    /**
     * token生成函数
     * @param int $appid 应用的ID
     * @param int $id (服务端用token时, 为passport ID, 未登录时以0表示) or string (浏览器用token时, 为cookie BAIDUID)
     * @param string $sk 应用的secret key
     * @return string or bool false
     */
    public function generateToken($appid, $id, $sk)
    {
        if (!$this->token_type)
            return false;

        $sign = $this->sign($appid, $id, $sk);
        if (!$sign)
            return false;

        switch ($this->token_type) {
            case self::TOKEN_TYPE_SERVER:
                if (!ctype_digit($id))
                    return false;
                return self::TOKEN_TYPE_SERVER . ".$sign.{$this->timestamp}.$id-$appid";
            case self::TOKEN_TYPE_BROWSER:
                return self::TOKEN_TYPE_BROWSER . ".$sign.{$this->timestamp}.$appid";
            default:
                return false;
        }
    }
}