<?php

/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2017/10/18
 * Time: 下午5:10
 */
class Service_Interaction_Interaction extends Libs_BaseService {
    CONST SERVICE_NAME = 'inbound';

    /**
     * @param $arrInput
     * @return array
     */
    public static function multiCallInteraction($arrInput) {
        if (!self::checkParam($arrInput)) {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $objMulti = new Tieba_Multi('multiCall');
        $methods = array(
//            'executeQualityCall',
            'executeForumFeature',
            'executeFeatureCall',
            'executeImageFeature',
            'getForumNumFeature',
            'getForumPosts',
            'getUserRecentPost',
            'getUserRecentPostInForum',
            'getUserRecentThreadInForum',
            'getUserRecentThread',
            'getUserFeature',
            'getUserFollow',
            'getUserPerm',
            'getUserPost',
            'getUserAgreeNum',
        );
        foreach ($methods as $oneMethod) {
            $objMulti->register(
                $oneMethod,
                new Tieba_Service('inbound'),
                array(
                    'serviceName' => self::SERVICE_NAME,
                    'method' => $oneMethod,
                    'ie' => 'utf-8',
                    'input' => $arrInput,
                    'extra' => array(
                        'rtimeout' => 3800,
                    ),
                )
            );
        }
        $objMulti->call();
        $retArr = array();
        foreach ($methods as $oneMethod) {
            $tempResult = $objMulti->getResult($oneMethod);
            if (Tieba_Errcode::ERR_SUCCESS !== $tempResult['errno']) {
                Bingo_Log::warning('Failed to call service ' . $oneMethod);
                continue;
            }
            $retArr[$oneMethod] = $tempResult;
        }
        $featureMethods = array(
            'executeForumFeature',
            'executeFeatureCall',
            'executeImageFeature',
            'getForumNumFeature',
            'getForumPosts',
            'getUserRecentPost',
            'getUserRecentPostInForum',
            'getUserRecentThreadInForum',
            'getUserRecentThread',
            'getUserFeature',
            'getUserFollow',
            'getUserPerm',
            'getUserPost',
            'getUserAgreeNum',
        );
        $featureRet = array();
        foreach ($featureMethods as $oneMethod) {

            //Bingo_Log::notice('method: ' . $oneMethod . serialize($retArr[$oneMethod]));
            if (isset($retArr[$oneMethod]) && is_array($retArr[$oneMethod]['data'])) {
                $featureRet = array_merge($featureRet, $retArr[$oneMethod]['data']);
            }
        }
        //Bingo_Log::notice('tid:  ' . $arrInput['thread_id']  . " ". serialize($featureRet));

        $qualityInput = $arrInput;
        foreach($featureRet as $k => $v) {
            if(!isset($arrInput[$k])) {
                $qualityInput[$k] = $v;
            }
        }

        Bingo_Timer::start('executeQualityCall');
        $qualityRet = Service_Interaction_Quality::executeQualityCall($qualityInput);
        Bingo_Log::notice("quality input: ".  serialize($qualityInput) . " output: ". serialize($qualityRet));
        Bingo_Timer::end('executeQualityCall');

        $ret = array(
            'quality' => $qualityRet['data'],
            'feature' => $featureRet,
        );
        return self::succRet($ret);
    }


    /**
     * 获取
     * @param $arrInput
     * @return array
     */
    public static function getForumFeature($arrInput) {
        $ret = Tieba_Service::call('forum', 'getBtxInfo', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning('Failed to get forum info');
            return self::errRet($ret['errno']);
        }
        $info = array(
            'first_dir' => $ret['attrs']['dir']['level_1_name'],
            'second_dir' => $ret['attrs']['dir']['level_2_name'],
        );
        return self::succRet($info);
    }

    /**
     * @param $arrInput
     * @return bool
     */
    private static function checkParam($arrInput) {
        $paramList = array(
            'thread_id',
            //        'forum_id',
            'user_id',
            //        'user_name',
            //        'forum_name',
            //'title',
            //'rawdata'
        );
        foreach ($paramList as $oneParam) {
            if (!isset($arrInput[$oneParam]) || empty($arrInput[$oneParam]))
                return false;
        }
        return true;
    }



}