<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON><PERSON><PERSON>
 * Date: 2018/1/11
 * Time: 上午10:44
 */

class Service_Interaction_Image extends Libs_BaseInteraction {
    public $serverName = '';
    public $params = array(
        'image_num' => 'image_num',
        'image_urls' => 'image_urls',
        'thread_id' => 'thread_id',
        'force_idl' => 'force_idl',
    );

    private $noEnoughFeature = false;
    private $picMaxNum = 3;
//    private $defaultAppid = 9514037;
    private $defaultAppid = 10343051;
//    private $defaultsk = 'VyicVTI78xxZWsaNe9AFvQAo1rc1oLIG';
    private $defaultsk = 'hCuNcSm9juLfLkDZAELLHD8mr3Dg0vUe';
    private $ocrMaxLength = 150;


    const TIME_OUT_CURL_MS = 2000;
    const TIME_OUT_CONN_MS = 1000;    //ms

    const TRY_TIMES = 2;
    // es sharpness const
    const BUSINESS_NAME = 'Nanifeature_feed_clarity_2';
    const RESOURCE_KEY = 'nani';
    const NANI_VIDEO_THREAD_REDIS_KEY = 'nani_video_thread_redis_queue_es';
    const AUTH_KEY = '09dda359-332a-5c64-a925-28dae560d3c9';
    const FEATURE_NAME = 'FEATURE_FEED_CLARITY';
    const PATH_INFO = 'xvision/feat_sync';
    const HOST = 'baijiahao-author.baidu.com';
    const VIDEO_EDIT_TABLE_PREFIX = 'video_edit_';
    // es sharpness const


    CONST IMAGE_SIGN_SERVER = 'video_hash_server_pandora';
    CONST IMAGE_SIGN_CHECK_TYPE = 0b1000;

    private static $LocalMachine = 'cq01-pic-favo11.cq01.baidu.com:8080';
//    private $urlList = array(
//        'classify' => array('url' => 'http://openapi-test.jpaas-matrixoff00.baidu.com/rest/2.0/vis-classify/v1/classify/general?access_token=21.21cda41bd9739ce5a083f3326f64b610.2592000.1469180474.1686270206-11101624'),
//        'clarity' => array('url' => 'http://openapi-test.jpaas-matrixoff00.baidu.com/rest/2.0/vis-imgquality/v1/imagequality/clarity?access_token=21.21cda41bd9739ce5a083f3326f64b610.2592000.1469180474.1686270206-11101624'),
//        'aesthetic' => array('url' => 'http://openapi-test.jpaas-matrixoff00.baidu.com/rest/2.0/vis-imgquality/v1/imagequality/aesthetic?access_token=21.21cda41bd9739ce5a083f3326f64b610.2592000.1469180474.1686270206-11101624'),
//        'ocr' => array('url' => 'http://openapi-test.jpaas-matrixoff00.baidu.com/rest/2.0/vis-ocr/v1/ocr/general?access_token=21.21cda41bd9739ce5a083f3326f64b610.2592000.1469180474.1686270206-11101624'),
//        'guessword' => array('url' => 'http://openapi-test.jpaas-matrixoff00.baidu.com/rest/2.0/vis-guessword/v2/advanced_guessword?access_token=21.21cda41bd9739ce5a083f3326f64b610.2592000.1469180474.1686270206-11101624'),
//    );

    private $urlList = array(
        'classify' => array('url' => 'http://inner.openapi.baidu.com/rest/2.0/vis-classify/v1/classify/general', 'param' => array('top_num' => 3,)),
        'clarity' => array('url' => 'http://inner.openapi.baidu.com/rest/2.0/vis-imgquality/v1/imagequality/general_clarity'),
        'aesthetic' => array('url' => 'http://inner.openapi.baidu.com/rest/2.0/vis-imgquality/v1/imagequality/general_aesthetic'),
        'disgust' => array('url' => 'http://inner.openapi.baidu.com/rest/2.0/vis-classify/v1/disgust'),
        //'antiporn' => array('url' => 'http://inner.openapi.baidu.com/rest/2.0/vis-antiporn/v2/antiporn', 'appid' => 10343051, 'sk' => 'hCuNcSm9juLfLkDZAELLHD8mr3Dg0vUe'),
        'ocr' => array('url' => 'http://inner.openapi.baidu.com/rest/2.0/vis-ocr/v1/ocr/general', 'appid' => 10343051, 'sk' => 'hCuNcSm9juLfLkDZAELLHD8mr3Dg0vUe'),
        'guessword' => array('url' => 'http://inner.openapi.baidu.com/rest/2.0/vis-guessword/v2/advanced_guessword', 'appid' => 10343051, 'sk' => 'hCuNcSm9juLfLkDZAELLHD8mr3Dg0vUe'),
        'antiporn' => array(
            'url' => array(
                'common' => 'http://inner.openapi.baidu.com/rest/2.0/vis-antiporn/v1/anti_porn',
                'gif' => 'http://inner.openapi.baidu.com/rest/2.0/vis-antiporn/v1/anti_porn_gif',
            ),
            'appid' => 10343051,
            'sk' => 'hCuNcSm9juLfLkDZAELLHD8mr3Dg0vUe',
            'param' => array('class_id' => 16),
        ),
    );


    /**
     *  先从数据库中拿数据，没有的话，再从 idl 拿
     * @param
     * @return
     */
    protected function call() {
        $this->checkParam();
        if (empty($this->processedParam['image_urls'])) {
            Bingo_Log::notice('no pic for this thread');
            $this->returnInfo = array();
            return;
        }
        if (!$this->processedParam['force_idl']) {
            $tempArr = $this->getFromDataBase();
            if (!$tempArr) {
                $tempArr = $this->getFromImageDb();
            }
        }
        if (!$tempArr || $this->processedParam['force_idl']) {
            $idl = true;
            $tempArr = $this->getFromIDL();
        }
        if (!empty($tempArr)) {
            foreach ($tempArr as $img => $feature) {
                /*
                if ($feature['antiporn']['porn'] >= 0.98 || $feature['antiporn']['sexy'] >= 0.8 || $feature['disgust'] >= 0.98) {
                    $tempArr['quality_img'] = -1;
                }
                */

                //ocr结果处理
                if ($feature['ocr'] && $idl) {
                    $wordResult = $feature['ocr'];
                    $ocrStr = '';
                    $tempOcrResult = array();
                    foreach ($wordResult as $oneWord) {

                        $ocrStr .= $oneWord['words'];
                        if (mb_strlen($ocrStr) >= $this->ocrMaxLength) {
                            break;
                        }
                        $tempOcrResult[] = $oneWord['words'];
                    }
                    unset($tempArr[$img]['ocr']);
                    $tempArr[$img]['ocr'] = $tempOcrResult;
                }
            }
            Bingo_Log::notice('imgf: ' . serialize($tempArr));
            $this->returnInfo = array('imgf' => $tempArr);
            if ($this->noEnoughFeature) {
                $this->returnInfo['img_error'] = 1;
            }
        }
    }

    /**
     * @param
     * @return array
     */
    private function getFromIDL() {
        $imageFeature = $this->processImageNew();
        $info = $imageFeature['image_feature'];
        $tempArr = array();
        foreach ($info as $onePic => $onePicInfo) {
            if ($onePicInfo['classify'] && is_array($onePicInfo['classify'])) {
                $classifyInfo = array();
                foreach ($onePicInfo['classify'] as $oneClassInfo) {
                    $className = $oneClassInfo['class_name'];
                    $probability = sprintf("%.5f", $oneClassInfo['probability']);
                    if ($className) {
                        $classId = intval(Util_ImageClassify::getImageClassifyId($className));
                    }
                    if ($probability > 0) {
                        $classifyInfo[] = array(
                            'cid' => $classId,
                            'pro' => doubleval($probability),
                        );
                    }
                }
                unset($onePicInfo['classify']);
                if ($classifyInfo) {
                    $onePicInfo['classify'] = $classifyInfo;
                }
            }
            if ($onePicInfo['antiporn'] && count($onePicInfo['antiporn']) > 0) {
                $antipornInfo = array();
                foreach ($onePicInfo['antiporn'] as $oneClassInfo) {
                    $className = $oneClassInfo['class_name'];
                    $probability = sprintf("%.5f", $oneClassInfo['probability']);
                    if ($probability > 0) {
                        $classEnName = Util_ImageClassify::getAntipornClass($className);
                        $antipornInfo[$classEnName] = doubleval($probability);
                    }
                }
                unset($onePicInfo['antiporn']);
                if ($antipornInfo) {
                    $onePicInfo['antiporn'] = $antipornInfo;
                }
            }
            $tempArr[$onePic] = $onePicInfo;
        }
        return $tempArr;
    }

    /**
     * @param empty
     * @return bool
     */
    private function getFromDataBase() {
        $preInfoRet = Service_Store_Store::getOneThreadInfo($this->processedParam);
        if (Tieba_Errcode::ERR_SUCCESS !== $preInfoRet['errno']) {
            return false;
        }
        $preInfo = $preInfoRet['data'];
        if (!$preInfo || empty($preInfo)) {
            return false;
        }
        $preFeatureStr = $preInfo['feature'];
        $preFeatureArr = json_decode($preFeatureStr, true);
        if (!$preFeatureStr) {
            return false;
        }
        $imgFetature = $preFeatureArr['imgf'];
        if ($preFeatureArr['img_error']) {
            $this->noEnoughFeature = 1;
        }
        return $imgFetature;
    }

    /**
     * @param
     * @return
     */
    public function getFromImageDb() {
        Bingo_Timer::start(__FUNCTION__);
        $tid = $this->processedParam['thread_id'];
        $dlInput = array(
            'tid' => $tid,
        );
        $dbRet = Dl_Store_Image::getTidImage($dlInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $dbRet['errno']) {
            Bingo_Log::warning('nothing from img db');
            Bingo_Timer::end(__FUNCTION__);
            return false;
        }
        foreach ($dbRet['data'] as $oneRow) {
            $file = $oneRow['image_url'];
            $imgf = $oneRow['imgf'];
            $decodeImgf = json_decode($imgf, true);
            if (!$decodeImgf) {
                Bingo_Timer::end(__FUNCTION__);
                return false;
            }
            if (empty($decodeImgf['antiporn']) || !isset($decodeImgf['disgust']) || !$decodeImgf['hash']) {
                Bingo_Log::pushNotice('no_enough_feature', 1);
                $this->noEnoughFeature = true;
            }
            $retArr[$file] = $decodeImgf;
        }
        Bingo_Timer::end(__FUNCTION__);
        Bingo_Log::notice($tid . "\t" . serialize($retArr));
        return $retArr;
    }

    /**
     * @param $url
     * @return bool|string
     */
    private static function getPicSign($url) {
        $tempArr = preg_split("/\//", $url, null, PREG_SPLIT_NO_EMPTY);
        if (count($tempArr) > 0) {
            $fileName = end($tempArr);
            return substr($fileName, 0, 40);
        }
        return false;
    }


    /**
     * Query the picture real Ids
     * @param picId: the picture id
     * @return the real picture id
     */
    private static function queryPicRealId($picId) {
        $realId = $picId;
//    $ret = ral('imgmeta_query', 'nothing', array("cmd_no" => 1010, "pic_id" => $picId), rand());
        $ret = Space_Imgquery::queryPictureInfo(array("cmd_no" => 1010, "pic_id" => $picId)); // hiphotos升级 update zgw2014 20191011
        if (isset($ret['mapping_pic_id'])) {
            $mapping_pic_id = $ret['mapping_pic_id'];
            if ($mapping_pic_id != 0) {
                if (preg_match("/^18[0-9]+$/", $mapping_pic_id)) {
                    $realId = -1;
                } else {
                    $realId = $mapping_pic_id;
                }
            }
        } else {
            $realId = -1;
        }
        return $realId;
    }

    /**
     * @param $url
     * @return array
     */
    private static function hiphotos2bos($url) {
        $obj = new Bd_Pic_UrlCrypt();
        $res = array();
        $urlArr = explode("/", $url);
        $arrLength = count($urlArr);
        $imgNameArr = explode(".", $urlArr[$arrLength - 1]);
        $imageEncode = $imgNameArr[0];
        $imageId = $obj->decode_pic_url_crypt($imageEncode);
        $imageRealId = self::queryPicRealId($imageId);
        if ($imageRealId != -1) {
            $space = array("q" => 80, "w" => 580);

            $imageUrl = Space_ImgBosAuth::getBosUrl($imageRealId, $space);
            Bingo_Log::notice("hiphotos2bos, Space_ImgBosAuth::getBosUrl({$imageRealId}) = {$imageUrl}");
            //$imageUrl = "http://*************/hiphotos-sz/".$imageRealId;
            $res['error'] = 0;
            $res['bosurl'] = $imageUrl;
            $res['picId'] = $imageRealId;
        } else {
            $res['error'] = 1;
            $res['bosurl'] = '';
        }
        return $res;
    }

    /**
     * @param $url
     * @return bool
     */
    private static function isTiebaUrl($url) {
        if (strstr($url, "imgsrc.baidu.com") != false || strstr($url, "tiebapic.baidu.com") != false) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getFileContent($arrInput) {
        $tryTwice = false;
        $url = $arrInput['url'];;
        $fileName = $arrInput['file_name'];
        Bingo_Timer::start(__FUNCTION__ . $fileName);
        $tid = $arrInput['thread_id'];
        Bingo_Log::pushNotice('fileName', $fileName);
        Bingo_Log::pushNotice('thread_id', $tid);
        $picSign = self::getPicSign($url);
        $obj_BdPic = new Bd_Pic_UrlCrypt();
        $picId = $obj_BdPic->decode_pic_url_crypt($picSign);
        Bingo_Log::pushNotice('pic_id', $picId);
        if ($picId) {
            $arrPicInfo = array(
                array(
                    "pic_id" => $picId,
                    "domain" => "imgsrc.baidu.com",
                    "product_name" => "forum",
                    "pic_spec" => "w=580",
                )
            );
            $output = Bd_Pic::pid2Url($arrPicInfo, false);
            $picSmall = $output['resps'][0];
        }
        //      $fetchUrl = $url;
        $fetchUrl = $picSmall ? $picSmall : $url;

        if (self::isTiebaUrl($url)) {
            Bingo_Log::pushNotice('curl', 1);
            $isFetchFromBos = 1;
            Bingo_Timer::start("get_from_bos");
            $content = self::fetchUrlCurl($url, $isFetchFromBos);
            Bingo_Timer::end("get_from_bos");

            if(!$content || strlen(base64_encode($content)) > 4000000) {
                Bingo_Log::pushNotice('bos_curl', 1);
                Bingo_Timer::start("bos_curl");
                $content = self::fetchUrlGet($fetchUrl);
                Bingo_Timer::end("bos_curl");
            }
        } else {
            $content = self::fetchUrlGet($fetchUrl);
        }

        Bingo_Log::notice("tid:" . $tid . " url :" . $fetchUrl . " origin url: " . $url);
        if (!$content) {
            if ($tryTwice) {
                $content = self::fetchUrlGet($fetchUrl);
                Bingo_Log::pushNotice('try_twice', 1);
            }
            if (!$content) {
                Bingo_Log::notice("tid:" . $tid . " url :" . $fetchUrl . " origin url: " . $url);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }

        $ret = array(
            'content' => $content,
            'fileName' => $fileName,
        );
        Bingo_Timer::end(__FUNCTION__ . $fileName);
        return self::succRet($ret);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getVideoFileContent($arrInput) {
        $tryTwice = false;
        $fileName = $arrInput['file_name'];
        $tid = $arrInput['thread_id'];
        Bingo_Timer::start(__FUNCTION__ . $fileName);
        $url = $arrInput['url'];
        if (strpos($url, 'imgsrc') !== false) {
            $processedUrl = str_replace('imgsrc.baidu.com', self::$LocalMachine, $url);
        }
        Bingo_Log::pushNotice('fileName', $fileName);
        Bingo_Log::pushNotice('thread_id', $tid);
        $content = self::fetchUrlGet($processedUrl);
        Bingo_Log::notice("tid:" . $tid . " url :" . $processedUrl . " origin url: " . $url);
        if (!$content) {
            if ($tryTwice) {
                $content = self::fetchUrlGet($processedUrl);
                Bingo_Log::pushNotice('try_twice', 1);
            }
            if (!$content) {
                Bingo_Log::fatal("tid:" . $tid . " url: " . $processedUrl . " origin url: " . $url);
                Bingo_Timer::end(__FUNCTION__ . $fileName);
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }

        $ret = array(
            'content' => $content,
            'fileName' => $fileName,
        );
        Bingo_Timer::end(__FUNCTION__ . $fileName);
        return self::succRet($ret);
    }


    /**
     * 获取图片的各种特征
     * @param
     * @return
     */
    protected function processImage() {
        Bingo_Timer::start(__FUNCTION__);
        $imageSrc = $this->processedParam['image_urls'];
        Bingo_Log::pushNotice('image_urls', count($imageSrc));
        if (empty($imageSrc)) {
            Bingo_Log::notice('no image for this thread, tid:' . $this->processedParam['thread_id']);
            return array();
        }
        $toProcessArr = array();
        $i = 0;
        $mObj = new Tieba_Multi('get_file');

        foreach ($imageSrc as $onePic) {
            if ($i > $this->picMaxNum) {
                break;
            }
            $fileName = $this->getFileName($onePic);
            $tempArr = array();
            $tempArr['url'] = $onePic;
            $tempArr['fileName'] = $fileName;
            $toProcessArr[] = $tempArr;
            $mObj->register($fileName, new Tieba_Service('inbound'),
                array(
                    'serviceName' => 'inbound',
                    'method' => 'getFileContent',
                    'input' => array(
                        'file_name' => $fileName,
                        'url' => $onePic,
                    ),
//                    'extra' => array(
//                        'rtimeout' => 500,
//                    ),
                    'ie' => 'utf-8',
                )
            );
            $i++;
        }
        Bingo_Log::pushNotice('fileCnt', count($toProcessArr));
        if (empty($toProcessArr)) {
            return array();
        }

        Bingo_Timer::start('get_file');
        $mRet = $mObj->call();
        Bingo_Timer::end('get_file');
        $contentArr = array();
        foreach ($toProcessArr as $onePicInfo) {
            $fileName = $onePicInfo['fileName'];
            $ret = $mObj->getResult($fileName);
            if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
                continue;
            }
            $contentArr[] = array(
                'fileName' => $ret['data']['fileName'],
                'content' => $ret['data']['content'],
            );
        }


        Bingo_Log::pushNotice('success_get_file', count($contentArr));
        if (empty($contentArr)) {
            return array();
        }

        $md5Arr = array();
        $multi = new Tieba_Multi('img_feature');

        foreach ($contentArr as $oneFile) {
            $fileName = $oneFile['fileName'];
            $content = $oneFile['content'];
            if ($content) {
                $md5Arr[$fileName] = strtoupper(md5($content));
            }
            // 图片的 idl 特征
            foreach ($this->urlList as $key => $oneInfo) {
                $appid = $this->defaultAppid;
                $sk = $this->defaultsk;
                if (isset($oneInfo['appid']) && isset($oneInfo['sk'])) {
                    $appid = $oneInfo['appid'];
                    $sk = $oneInfo['sk'];
                }
                $token = $this->getToken($appid, $sk);
                $url = $oneInfo['url'];
                if (!$url) {
                    continue;
                }
                $info = array(
                    'image' => base64_encode($content),
                    'access_token' => $token,
                    'ie' => 'utf8',
                    'format' => 'json',
                );
                if (!empty($oneInfo['param']) && is_array($oneInfo['param'])) {
                    $info = array_merge($info, $oneInfo['param']);
                    Bingo_Log::notice('memory_get_usage :' . memory_get_usage());
                }
                $featureInput = array(
                    'info' => $info,
                    'url' => $url,
                    'file' => $fileName,
                    'feature' => $key,
                    'tid' => $this->processedParam['thread_id'],
                );


                $multi->register("$fileName:$key", new Tieba_Service('inbound'),
                    array(
                        'serviceName' => 'inbound',
                        'method' => 'getOnePicOneFeature',
                        'input' => $featureInput,
                        'extra' => array(
                            'rtimeout' => 1500,
                        ),
                        'ie' => 'utf-8',
                    )
                );
            }

            $hashInput = array(
                'thread_id' => $this->processedParam['thread_id'],
                'file' => $fileName,
                'content' => $content,
            );
            $multi->register(
                $fileName . ":hash",
                new Tieba_Service('inbound'),
                array(
                    'serviceName' => 'inbound',
                    'method' => 'getOnePicHash',
                    'input' => $hashInput,
                    'ie' => 'utf-8',
                )
            );
            unset($content);
        }

        $multi->call();
        $data = array();

        foreach ($contentArr as $oneFile) {
            $fileName = $oneFile['fileName'];
//            if ($md5Arr[$fileName]) {
//                $data[$fileName]['md5'] = $md5Arr[$fileName];
//            }
            foreach ($this->urlList as $key => $oneInfo) {
                $pk = $fileName . ":" . $key;
                $tempRet = $multi->getResult($pk);
                if ($tempRet['data']['feature']) {
                    $data[$fileName][$key] = $tempRet['data']['feature'];
                }
            }
            $hashKey = $fileName . ":hash";
            $tempRet = $multi->getResult($hashKey);
            if ($tempRet['data']['image_sign']) {
                $data[$fileName]['hash'] = $tempRet['data']['image_sign'];
            }
        }
        return array('image_feature' => $data);
    }


    /**
     * 获取图片的各种特征
     * @param
     * @return
     */
    public function processImageNew() {
        Bingo_Timer::start(__FUNCTION__);
        $imageSrc = $this->processedParam['image_urls'];
        Bingo_Log::pushNotice('image_urls', count($imageSrc));
        if (empty($imageSrc)) {
            Bingo_Log::notice('no image for this thread, tid:' . $this->processedParam['thread_id']);
            return array();
        }
        //$imageSrc = array_slice($imageSrc, 0, $this->picMaxNum);


        $multi = new Tieba_Multi('img_feature');
        $i = 0;
        foreach ($imageSrc as $oneFile) {
            if ($i >= $this->picMaxNum) {
                break;
            }
            $i++;
            $fileName = self::getFileName($oneFile);
            // 图片的 idl 特征
            foreach ($this->urlList as $key => $oneInfo) {
                $appid = $this->defaultAppid;
                $sk = $this->defaultsk;
                if (isset($oneInfo['appid']) && isset($oneInfo['sk'])) {
                    $appid = $oneInfo['appid'];
                    $sk = $oneInfo['sk'];
                }
                $token = $this->getToken($appid, $sk);
                $url = $oneInfo['url'];

                if (!$url) {
                    continue;
                }
                $info = array(
                    'access_token' => $token,
                    'ie' => 'utf8',
                    'format' => 'json',
                );
                if (!empty($oneInfo['param']) && is_array($oneInfo['param'])) {
                    $info = array_merge($info, $oneInfo['param']);
                    //Bingo_Log::notice('memory_get_usage :' . memory_get_usage());
                }
                $featureInput = array(
                    'info' => $info,
                    'api' => $url,
                    'url' => $oneFile,
                    'file' => $fileName,
                    'feature' => $key,
                    'tid' => $this->processedParam['thread_id'],
                );

                $multi->register("$fileName:$key", new Tieba_Service('inbound'),
                    array(
                        'serviceName' => 'inbound',
                        'method' => 'getOnePicOneFeatureNew',
                        'input' => $featureInput,
                        'extra' => array(
                            'rtimeout' => 3800,
                        ),
                        'ie' => 'utf-8',
                    )
                );
            }

            $hashInput = array(
                'thread_id' => $this->processedParam['thread_id'],
                'file' => $fileName,
                'url' => $oneFile,
            );
            $multi->register(
                $fileName . ":hash",
                new Tieba_Service('inbound'),
                array(
                    'serviceName' => 'inbound',
                    'method' => 'getHashByImageUrl',
                    'input' => $hashInput,
                    'ie' => 'utf-8',
                )
            );
        }

        $multi->call();
        $data = array();

        foreach ($imageSrc as $oneFile) {
            $fileName = self::getFileName($oneFile);;
            //$data[$fileName]['md5'] = $md5Arr[$fileName];
            foreach ($this->urlList as $key => $oneInfo) {
                $pk = $fileName . ":" . $key;
                $tempRet = $multi->getResult($pk);
                if ($tempRet['data']['feature']) {
                    $data[$fileName][$key] = $tempRet['data']['feature'];
                }
            }
            $hashKey = $fileName . ":hash";
            $tempRet = $multi->getResult($hashKey);
            /*
            if ($tempRet['data']['md5']) {
                $data[$fileName]['md5'] = $tempRet['data']['md5'];
            }
            */
            if ($tempRet['data']['image_sign']) {
                $data[$fileName]['hash'] = $tempRet['data']['image_sign'];
            }
            if ($tempRet['data']['g']) {
                $data[$fileName]['g'] = 1;
            }
        }
        return array('image_feature' => $data);
    }


    /**
     * @param $arrInput
     * @return
     */
    public static function getOnePicHash($arrInput) {
        $imgContent = $arrInput['content'];
        $file = $arrInput['file']; // 图片文件名，用这个做合并
        $threadId = $arrInput['thread_id'];

        $input = array(
            'check_type' => self::IMAGE_SIGN_CHECK_TYPE,
            'thread_id' => $threadId,
            'image' => base64_encode($imgContent),
        );
        $ret = ral(self::IMAGE_SIGN_SERVER, '', $input);
        if (Tieba_Errcode::ERR_SUCCESS != $ret['errno']) {
            Bingo_Log::warning('Failed to get image_sign, image: ' . $file . ' res: ' . serialize($ret));
            Bingo_Log::fatal('Failed to get image_sign, image: ' . $file . ' res: ' . serialize($ret));
            return self::errRet($ret['errno']);
        }
        $returnArr['file'] = $file;
        $returnArr['image_sign'] = $ret['data']['image_sign'];
        return self::succRet($returnArr);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getHashByBase64Content($arrInput) {
        $imgContent = $arrInput['content'];
        $file = $arrInput['file']; // 图片文件名，用这个做合并
        $threadId = $arrInput['thread_id'];

        $input = array(
            'check_type' => self::IMAGE_SIGN_CHECK_TYPE,
            'thread_id' => $threadId,
            'image' => $imgContent, // 已经用 base64_encode 处理过的图片，便于离线调用
        );
        $ret = ral(self::IMAGE_SIGN_SERVER, '', $input);
        if (Tieba_Errcode::ERR_SUCCESS != $ret['errno']) {
            Bingo_Log::warning('Failed to get image_sign, image: ' . $file . ' res: ' . serialize($ret));
            return self::errRet($ret['errno']);
        }
        $returnArr['file'] = $file;
        $returnArr['image_sign'] = $ret['data']['image_sign'];
        return self::succRet($returnArr);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getHashByImageUrl($arrInput) {
        $imgUrl = $arrInput['url'];
        $file = $arrInput['file']; // 图片文件名，用这个做合并
        $threadId = $arrInput['thread_id'];

        $input = array(
            'file_name' => $file,
            'url' => $imgUrl,
            'thread_id' => $threadId,
        );
        if ($arrInput['try_twice']) {
            $input['try_twice'] = 1;
        }
        $contentRet = Tieba_Service::call('inbound', 'getFileContent', $input, null, null, 'post', 'php', 'utf-8');
        $content = $contentRet['data']['content'];
        $isGif = false;
        $head6 = substr($content, 0, 6);
        if ("GIF89a" == $head6 || "GIF87a" == $head6) {

            $isGif = true;
        }

        $input = array(
            'check_type' => self::IMAGE_SIGN_CHECK_TYPE,
            'thread_id' => $threadId,
            'image' => base64_encode($content),
        );
        $ret = ral(self::IMAGE_SIGN_SERVER, '', $input);
        Bingo_Log::notice('Call service: ' . self::IMAGE_SIGN_SERVER . ' here.');
        if (Tieba_Errcode::ERR_SUCCESS != $ret['errno']) {
            Bingo_Log::warning('Failed to get image_sign, image: ' . $file . ' res: ' . serialize($ret));
            Bingo_Log::fatal('Failed to get image_sign, image: ' . $file . ' res: ' . serialize($ret));
            Bingo_Log::notice('Service: ' . self::IMAGE_SIGN_SERVER . ' call Failed');
            return self::errRet($ret['errno']);
        }
        $returnArr['file'] = $file;
        $returnArr['image_sign'] = $ret['data']['image_sign'];
        if ($isGif) {
            $returnArr['g'] = 1;
        }
//        $returnArr['md5'] = md5($content);
        return self::succRet($returnArr);
    }


    public static function getEsSharp($arrInput) {
        //get file
        $fileName = $arrInput['file'];
        $tid = $arrInput['tid'];
        $arrInput['thread_id'] = $tid;
        Bingo_Timer::start('get_file' . $fileName);
        $cObj = new Tieba_Multi('get_file_content');
        $getFileContentInput = array(
            'file_name' => $fileName,
            'url' => $arrInput['url'],
            'thread_id' => $tid,
        );
        $tryTimes = self::TRY_TIMES;
        for ($i = 0; $i < $tryTimes; $i++) {
            $cObj->register(
                $fileName . $i,
                new Tieba_Service('inbound'),
                array(
                    'serviceName' => 'inbound',
                    'method' => 'getFileContent',
                    'input' => $getFileContentInput,
                    'ie' => 'utf-8',
                    'extra' => array(
                        'rtimeout' => 2000,
                    ),
                )
            );
        }
        Bingo_Timer::start(__FUNCTION__ . "_getFileContent");
        $cObj->call();
        Bingo_Timer::end(__FUNCTION__ . "_getFileContent");
        for ($i = 0; $i < $tryTimes; $i++) {
            $result = $cObj->getResult($fileName . $i);
            if (Tieba_Errcode::ERR_SUCCESS === $result['errno']) {
                $contentRet = $result;
                break;
            }
        }

        Bingo_Timer::end('get_file' . $fileName);
        if (Tieba_Errcode::ERR_SUCCESS !== $contentRet['errno']) {
            Bingo_Log::warning('failed to get image content. ' . serialize($arrInput));
            Bingo_Log::fatal('failed to get image content. ' . serialize($arrInput));
            return self::errRet($contentRet['errno']);
        }
        $contentData = $contentRet['data'];
        $content = $contentData['content'];
        $arrInput = array(
            'business_name' => self::BUSINESS_NAME,
            'resource_key' => self::RESOURCE_KEY,
            'auth_key' => self::AUTH_KEY,
            'feature_list' => array(
                array(
                    'feature_name' => self::FEATURE_NAME,
                )
            ),
            'input_message' => array(
                'img_base64' => base64_encode($content),
            ),
        );

        $arrHeader = array(
            'pathinfo' => self::PATH_INFO,
            'Content-Type' => 'application/json',
        );

        $ret = ral('new_xvision_forvideo', 'post', $arrInput, rand(), $arrHeader);
        $score = $ret['feature_result_list'][0]['value'];
        $ret = array(
            'name' => 'es_sharpness',
            'feature' => $score,
        );
        Bingo_Log::notice('debug:: es sharp' . serialize($ret));
        return self::succRet($ret);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getOnePicOneFeatureNew($arrInput) {
        //get file
        $fileName = $arrInput['file'];
        $feature = $arrInput['feature'];
        $tid = $arrInput['tid'];
        $url = $arrInput['api']; // api url maybe array
        $info = $arrInput['info'];
        $arrInput['thread_id'] = $tid;
        Bingo_Timer::start('get_file' . $fileName);
        $cObj = new Tieba_Multi('get_file_content');
        $tryTimes = self::TRY_TIMES;
        $getFileContentInput = array(
            'file_name' => $fileName,
            'url' => $arrInput['url'],
            'thread_id' => $tid,
        );
        for ($i = 0; $i < $tryTimes; $i++) {
            $cObj->register(
                $fileName . $i,
                new Tieba_Service('inbound'),
                array(
                    'serviceName' => 'inbound',
                    'method' => 'getFileContent',
                    'input' => $getFileContentInput,
                    'ie' => 'utf-8',
                    'extra' => array(
                        'rtimeout' => 2000,
                    ),
                )
            );
        }
        Bingo_Timer::start(__FUNCTION__ . "_getFileContent");
        $cObj->call();
        Bingo_Timer::end(__FUNCTION__ . "_getFileContent");
        for ($i = 0; $i < $tryTimes; $i++) {
            $result = $cObj->getResult($fileName . $i);
            if (Tieba_Errcode::ERR_SUCCESS === $result['errno']) {
                $contentRet = $result;
                break;
            }
        }

//      $contentRet = self::getFileContent($arrInput);
        Bingo_Timer::end('get_file ' . $fileName);
        if (Tieba_Errcode::ERR_SUCCESS !== $contentRet['errno']) {
            Bingo_Log::warning('failed to get image content. ' . serialize($arrInput));
            Bingo_Log::fatal('failed to get image content. ' . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_FILE_NOT_FOUND);
        }
        $contentData = $contentRet['data'];
        $content = $contentData['content'];
        $isGif = false;
        $head6 = substr($content, 0, 6);
        if ("GIF89a" == $head6 || "GIF87a" == $head6) {
            $isGif = true;
        }
        if (is_array($url)) {
            if ($isGif) {
                $url = $url['gif'];
            } else {
                $url = $url['common'];
            }
        }
        Bingo_Log::pushNotice('isGif', intval($isGif));
        $info['image'] = base64_encode($content);
        Bingo_Log::notice("tid" . $tid . " file_name:" . $fileName . " feature:" . $feature . " content-length:" . strlen(base64_encode($content)) . " content-md5:" . md5($content));
        Bingo_Log::notice("tid" . $tid . " file_name:" . $fileName . " feature:" . $feature . "  content : " . substr(base64_encode($content), 0, 100));

        Bingo_Timer::start(__FUNCTION__ . "_" . $fileName . "_" . $feature);
        Bingo_Log::pushNotice('tid', $tid);
        Bingo_Log::pushNotice('feature', $feature);
        Bingo_Log::pushNotice('file', $fileName);
        Bingo_Log::pushNotice('file_length', strlen($info['image']));

        $conn = curl_init($url);
        curl_setopt($conn, CURLOPT_URL, $url);
        curl_setopt($conn, CURLOPT_POST, 1);
        curl_setopt($conn, CURLOPT_POSTFIELDS, $info);
        curl_setopt($conn, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($conn, CURLOPT_TIMEOUT, 2);
        curl_setopt($conn, CURLOPT_HTTPHEADER, array('Expect:'));
        $imgRet = curl_exec($conn);
        $curlInfo = curl_getinfo($conn);
        curl_close($conn);

        //Bingo_Log::notice('curl_info' . serialize($curlInfo));
        Bingo_Log::notice('tid: ' . $tid . ' pic: ' . $fileName . ' feature ' . $feature . ' ret : ' . $imgRet);
        if(false == $imgRet) {
            Bingo_Log::warning('tid: ' . $tid . ' pic: ' . $fileName . ' feature ' . $feature
                . ' ret : ' . $imgRet . " curlinfo:" . print_r($curlInfo, true));
        }
        $tempRet = json_decode($imgRet, true);
        if ($tempRet && $tempRet['result']) {
            $imgF = is_double($tempRet['result'])
                ? sprintf("%.5f", $tempRet['result']) : $tempRet['result'];
        } else if ($tempRet && $tempRet['words_result']) {
            $imgF = $tempRet['words_result'];
        }
        $ret = array(
            'name' => $feature,
            'feature' => $imgF,
        );
        Bingo_Timer::end(__FUNCTION__ . "_" . $fileName . "_" . $feature);
        return self::succRet($ret);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getOnePicOneFeature($arrInput) {
        $info = $arrInput['info'];
        $url = $arrInput['url'];
        $feature = $arrInput['feature'];
        $fileName = $arrInput['file'];
        $tid = $arrInput['tid'];
        Bingo_Timer::start(__FUNCTION__ . "_" . $fileName . "_" . $feature);
        Bingo_Log::pushNotice('tid', $tid);
        Bingo_Log::pushNotice('feature', $feature);
        Bingo_Log::pushNotice('file', $fileName);
        $conn = curl_init($url);

        curl_setopt($conn, CURLOPT_URL, $url);
        curl_setopt($conn, CURLOPT_POST, 1);
        curl_setopt($conn, CURLOPT_POSTFIELDS, $info);
        curl_setopt($conn, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($conn, CURLOPT_TIMEOUT, 2);
        //curl_setopt($conn, CURLOPT_TIMEOUT, )
        curl_setopt($conn, CURLOPT_HTTPHEADER, array('Expect:'));
        $imgRet = curl_exec($conn);
        $curlInfo = curl_getinfo($conn);
        curl_close($conn);

        Bingo_Log::notice('curl_info' . serialize($curlInfo));
        Bingo_Log::notice('tid: ' . $tid . ' pic: ' . $fileName . ' ret : ' . $imgRet);
        $tempRet = json_decode($imgRet, true);
        if ($tempRet && $tempRet['result']) {
            $imgF = is_double($tempRet['result'])
                ? sprintf("%.5f", $tempRet['result']) : $tempRet['result'];
        } else if ($tempRet && $tempRet['words_result']) {
            $imgF = $tempRet['words_result'];
        }

        $ret = array(
            'name' => $feature,
            'feature' => $imgF,
        );
        Bingo_Timer::end(__FUNCTION__ . "_" . $fileName . "_" . $feature);
        return self::succRet($ret);
    }

    /**
     * @param
     * @return
     */
    public function processOnePic1($file, $expect = 0, $gzip = 0) {
        if (!$file) {
            $file = "http://imgsrc.baidu.com/forum/pic/item/17130eb30f2442a7fe34e575da43ad4bd01302ed.jpg";
        }

        $token = $this->getToken();
        $i = 0;
        $fileName = $this->getFileName($file);
        $tempArr = explode(".", $fileName);
        $fileName = $tempArr[0];
        foreach ($this->urlList as $key => $url) {
            $conn = curl_init($url);
            Bingo_Timer::start('get_file_image');
            $info = array(
                'image' => base64_encode(file_get_contents($file)),
                'access_token' => $token,
                'ie' => 'utf8',
                'format' => 'json',
            );
            Bingo_Timer::end('get_file_image');
            curl_setopt($conn, CURLOPT_URL, $url);
            curl_setopt($conn, CURLOPT_POST, 1);
            curl_setopt($conn, CURLOPT_POSTFIELDS, $info);
            curl_setopt($conn, CURLOPT_RETURNTRANSFER, 1);

            if ($expect) {
                curl_setopt($conn, CURLOPT_HTTPHEADER, array('Expect:'));
                Bingo_Log::pushNotice('expect', 1);
            }
            if ($gzip) {
                curl_setopt($conn, CURLOPT_ENCODING, "gzip");
                Bingo_Log::pushNotice('gzip', 1);
            }
            //curl_setopt($conn, CURLOPT_TIMEOUT, )
            $info = curl_exec($conn);
            $curlInfo = curl_getinfo($conn);
            Bingo_Log::notice(serialize($curlInfo));
            Bingo_Log::notice(serialize($info));
        }
        $content = json_decode($info, true);
        return $content;

    }

    /**
     * @param
     * @return
     */
    public static function processOnePic($arrInput) {
        $obj = new Service_Interaction_Image($arrInput);
        $ret = $obj->processOnePic1($arrInput['img'], $arrInput['expect'], $arrInput['gzip']);

        return self::succRet($ret);
    }

    /**
     * @param
     * @return string
     */
    private function getToken($appid = null, $sk = null) {
        require_once "InnerToken.php";
        $uid = '1884599608';
        if (!$appid || !$sk) {
            $appid = $this->defaultAppid;
            $sk = $this->defaultsk;
        }
        $it = new Service_Interaction_InnerToken(11);
        $token = $it->generateToken($appid, $uid, $sk);
        return $token;
    }

    /**
     * 下载图片到本地
     * @param
     * @return
     */
    private function downloadImage() {
        Bingo_Timer::start(__FUNCTION__);
        $imageSrc = $this->processedParam['image_urls'];
        $tid = $this->processedParam['thread_id'];
        Bingo_Log::pushNotice("thread_id", $tid);
        if (!$imageSrc) {
            return array();
        }
        $path = ROOT_PATH . "/data/inbound/image";
        if (!is_dir($path)) {
            system("mkdir -p $path");
        }
        $files = array();
        $urlFile = ROOT_PATH . "/data/inbound/url$tid";
        $fp = fopen($urlFile, "w");
        $cnt = 0;
        foreach ($imageSrc as $oneSrc) {

            $fileName = $this->getFileName($oneSrc);
            $filePath = $path . "/" . $fileName;
            fputs($fp, $oneSrc . "\n");

            $files[] = $filePath;
            $cnt++;
            if ($cnt > $this->picMaxNum) {
                break;
            }
        }
        fclose($fp);

        $cmd = "wget -i $urlFile -P $path";
        //$cmd = "cat $urlFile | xargs -n 1 -P 0 -I {} wget -q -e http_proxy={} -O {}";
        @exec($cmd, $ret);
        unlink($urlFile);
        if (false === $ret) {
            return array();
        }
        Bingo_Timer::end(__FUNCTION__);
        return $files;
    }

    /**
     * @param $url
     * @param int $intReadTimeOut
     * @param int $intConnTimeOut
     * @return bool|string
     */
    private static function fetchUrlGet($url, $intReadTimeOut = 2000, $intConnTimeOut = 300) {
        $httpproxy = Orp_FetchUrl::getInstance(
            array(
                'timeout' => $intReadTimeOut,
                'conn_timeout' => $intConnTimeOut,
                'max_response_size' => 10240000,
                'conn_retry' => 2,
                'referer' => 'baidu.com',
            )
        );
        $res = $httpproxy->get($url, array('Expect:'));
        $err = $httpproxy->errmsg();
        $http_code = $httpproxy->http_code();
        if ($err) {
            Bingo_Log::warning('failed to get data ' . $url . "\t " . $httpproxy->errmsg() . "  " . serialize($httpproxy->curl_info()));
            return false;
        } else {
            Bingo_Log::notice('success get data. ' . $url);
            if ($http_code == 200) {
                return $res;
            }
        }
        return false;
    }

    /**
     * @param $url
     * @return array|mixed
     */
    private static function fetchUrlCurl($url, $isFetchFromBos) {
        Bingo_Timer::start('curl_read');
        $res = array();
        if ($isFetchFromBos) {
            $urlTansformRes = self::hiphotos2bos($url);
            if (isset($urlTansformRes['error']) && $urlTansformRes['error'] == 0) {
                $url = $urlTansformRes['bosurl'];
            } else {
                return false;
            }
        }
        $ch = curl_init($url);
        //设置curl_exec返回的值包含Http头
        curl_setopt($ch, CURLOPT_HEADER, 0);
        //设置curl_exec的超时时间
        curl_setopt($ch, CURLOPT_TIMEOUT_MS, self::TIME_OUT_CURL_MS);
        //设置curl的连接超时时间
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT_MS, self::TIME_OUT_CONN_MS);
        //设置curl_exec返回的值包含Http内容
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        //服务器返回的数据（包括内容）
        $content = curl_exec($ch);
        //获取此次抓取的相关信息
        $httpinfo = curl_getinfo($ch);
        //获取请求错误码，0表示正常
        $errno = curl_errno($ch);
        if ($errno) {
            Bingo_Log::warning('The image Url is:' . $url . ', and the uegasync curl error number is :' . $errno . ', and the curl error msg is:' . curl_error($ch));
        }
        $res['errno'] = $errno;
        // 是否是新图片服务生成的图片
        $isNewPhoto = 0;
        if (isset($urlTansformRes['picId']) && $urlTansformRes['picId'] >= Bd_Pic::NEW_IMGSYS_PID) {
            $isNewPhoto = 1;
        }
        if ($isFetchFromBos && $isNewPhoto == 0 && strlen($content) > 32) {
            // 旧图片服务（hiphotos）生成的图片，截去前面 32 个字符
            $res['img'] = substr($content, 32);
        } else {
            $res['img'] = $content;
        }
        $res['http_code'] = $httpinfo['http_code'];
        Bingo_Timer::end('curl_read');
        return $res['img'];
    }

    /**
     * @param $url
     * @return bool|mixed|String
     */
    public function getFileName($url) {
        $tempArr = preg_split("/\//", $url, null, PREG_SPLIT_NO_EMPTY);
        if (count($tempArr) > 0) {
            $fileName = end($tempArr);
            if (preg_match("/tieba-video-frame/", $url)) {
                return $fileName;
            }
            return substr($fileName, 0, 40);
        }
        return false;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function executeImageFeature($arrInput) {
        Bingo_Timer::start(__FUNCTION__);
        $obj = new Service_Interaction_Image($arrInput);
        $obj->call();
        Bingo_Timer::end(__FUNCTION__);
        return self::succRet($obj->returnInfo);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function processAddThreadImage($arrInput) {
        if (!isset($arrInput['format'])) {
            $arrInput['format'] = 'mcpack';
        }
        $strFormat = $arrInput['format'];
        if ($strFormat !== 'mcpack' && $strFormat !== 'json' && $strFormat !== 'mcpackraw') {
            Bingo_Log::warning("input params wrong format:$strFormat.");
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrCmd = Tieba_Service::getArrayParams($arrInput, 'data');
        if (!isset ($arrCmd ['command_no'])) {
            $strInputData = file_get_contents("php://input");
            if ($strFormat === 'mcpack') {
                $arrCmd = mc_pack_text2pack(substr($strInputData, 5));
                $arrCmd = mc_pack_pack2array($arrCmd);
            }
            if (!isset ($arrCmd ['command_no'])) {
                Bingo_Log::warning("no command no found.input [" . serialize($arrInput) . "]");
                return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
        }
        $commandNo = $arrCmd['command_no'];
        switch ($commandNo) {
            case Tieba_Cmd_Post::$cmd[Tieba_Cmd_Post::threadCommit]:
            case Tieba_Cmd_Post::$cmd[Tieba_Cmd_Post::personThrCommit]:

                if (!Service_Nmq_Nmq::isValidTwThread($arrCmd) && !Service_Nmq_Nmq::isValidVideoThread($arrCmd, 1) || $arrCmd['bjh_thread_type']) {
                    return self::succRet();
                }

                if (Service_Tool_Tool::checkIfDeleted($arrCmd)) {
                    return self::succRet();
                }

                if (Service_Nmq_Nmq::isValidTwThread($arrCmd)) {
                    return self::getImgf($arrCmd);
                } elseif (Service_Nmq_Nmq::isValidVideoThread($arrCmd, 1)) {
                    Service_Videointeraction_Videoimage::videoImgf($arrCmd);
                    Service_Videointeraction_Videohash::getVideoHashFeaturesSaveIntoDb($arrCmd);
                    return self::succRet();
                }
                break;
            case Tieba_Cmd_Post::$cmd['bjhThreadCommit']:
                $action = $arrCmd['action'];
                $bjhThreadType = $arrCmd['bjh_thread_type'];
                $input = $arrCmd;
                if ($action == 1 && ($bjhThreadType == Util_Odyssey::ODYSSEY_TYPE_DYNAMIC_TUWEN)) {
                    $isWhiteInfo = false;
                    if (Util_Odyssey::inWhiteList($input)) {
                        $isWhiteInfo = true;
                        $input['bjh_white_uid'] = 1;
                        // todo 因流量问题，把动态白名单的图片特征暂时不取了。
                    }
                    $dataType = $input['m_ext']['displaytype_exinfo']['data_type'];
                    Bingo_Log::pushNotice('data_type', $dataType);
                    if (($bjhThreadType === Util_Odyssey::ODYSSEY_TYPE_DYNAMIC_TUWEN || $bjhThreadType === Util_Odyssey::ODYSSEY_TYPE_DYNAMIC_VIDEO)
                        && $dataType == Util_Odyssey::TIEBA_DATATYPE) {
                        $isWhiteInfo = true;
                    }

                    if (!$isWhiteInfo) {
                        Bingo_Log::notice('nid:' . $input['nid'] . ' tid:' . $input['thread_id'] . " user_id " . $input['user_id'] . ' not in white list');
                        return self::succRet($input['nid']);
                    }

                    $addThreadNmq['thread_id'] = $input['thread_id'];
                    $addThreadNmq['tid'] = $input['thread_id'];
                    $addThreadNmq['user_id'] = $input['user_id'];
                    $addThreadNmq['nid'] = $input['nid'];
                    // todo username

                    $addThreadNmq['forum_id'] = 0;
                    $addThreadNmq['forum_name'] = '';
                    $addThreadNmq['title'] = $input['title'] ? Bingo_Encode::convert($input['title'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8) : '';
                    $addThreadNmq['rawdata'] = $input['content'] ? Bingo_Encode::convert($input['content'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8) : '';
                    $picImgArr = $input['m_image_urls'];
                    Bingo_Log::notice("bjh_img_urls  " . serialize($picImgArr));
                    if (isset($picImgArr['imgurls']) && !empty($picImgArr['imgurls'])) {
                        foreach ($picImgArr['imgurls'] as $onePicInfo) {
                            $imgUrls[] = $onePicInfo['url'];
                        }

                        $addThreadNmq['image_urls'] = $imgUrls;
                    }
                    $addThreadNmq['image_num'] = $arrInput['m_ext']['displaytype_exinfo']['image_num'];
                    $addThreadNmq['create_time'] = intval($input['public_time']);
                    $addThreadNmq['thread_type'] = 0;
                    $addThreadNmq['bjh_thread_type'] = $bjhThreadType;
                    return self::getImgf($addThreadNmq);
                }
                break;

        }
        return self::succRet();
    }

    /**
     * @param $num
     * @return
     */
    public function setMaxPic($num) {
        $this->picMaxNum = $num;
    }

    /**
     * @param $arrInput
     * @return
     */
    public static function getImgf($arrInput) {

        $tid = $arrInput['thread_id'];
        $isVideo = $arrInput['is_video'];

        $obj = new Service_Interaction_Image($arrInput);
        if ($isVideo) {
            $obj->setMaxPic(4);
            Bingo_Log::pushNotice('is_video', 1);
            Bingo_Log::pushNotice('max_pic', 4);
        }
        $obj->checkParam();

        $feature = $obj->getImageFeature($arrInput);

        if (!empty($feature)) {
            $processImgf = $obj->processImgfRet($feature);
            foreach ($processImgf as $fileName => $imgf) {
                $dlInput = array(
                    'tid' => $tid,
                    'image_url' => $fileName,
                    'imgf' => json_encode($imgf, JSON_UNESCAPED_UNICODE),
                    'create_time' => $arrInput['create_time'],
                );
                Bingo_Log::notice('tid' . $arrInput['thread_id'] . ' imgf: ' . serialize($imgf));
                $ret = Dl_Store_Image::insert($dlInput);
                if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
                    Bingo_Log::warning('failed to insert into database. input: ' . serialize($dlInput));
                }
            }
        }
        return self::succRet();
    }

    /**
     * @param $imageFeature
     * @return array
     */
    public function processImgfRet($imageFeature) {
        $info = $imageFeature['image_feature'];
        $tempArr = array();
        foreach ($info as $onePic => $onePicInfo) {
            if ($onePicInfo['classify'] && is_array($onePicInfo['classify'])) {
                $classifyInfo = array();
                foreach ($onePicInfo['classify'] as $oneClassInfo) {
                    $className = $oneClassInfo['class_name'];
                    $probability = sprintf("%.5f", $oneClassInfo['probability']);
                    if ($className) {
                        $classId = intval(Util_ImageClassify::getImageClassifyId($className));
                    }
                    if ($probability > 0) {
                        $classifyInfo[] = array(
                            'cid' => $classId,
                            'pro' => doubleval($probability),
                        );
                    }
                }
                unset($onePicInfo['classify']);
                if ($classifyInfo) {
                    $onePicInfo['classify'] = $classifyInfo;
                }
            }
            if ($onePicInfo['antiporn'] && count($onePicInfo['antiporn']) > 0) {
                $antipornInfo = array();
                foreach ($onePicInfo['antiporn'] as $oneClassInfo) {
                    $className = $oneClassInfo['class_name'];
                    $probability = sprintf("%.5f", $oneClassInfo['probability']);
                    if ($probability > 0) {
                        $classEnName = Util_ImageClassify::getAntipornClass($className);
                        $antipornInfo[$classEnName] = doubleval($probability);
                    }
                }
                unset($onePicInfo['antiporn']);
                if ($antipornInfo) {
                    $onePicInfo['antiporn'] = $antipornInfo;
                }
            }
            if ($onePicInfo['ocr']) {
                $wordResult = $onePicInfo['ocr'];
                $ocrStr = '';
                $tempOcrResult = array();
                foreach ($wordResult as $oneWord) {
                    $ocrStr .= $oneWord['words'];
                    if (mb_strlen($ocrStr) >= $this->ocrMaxLength) {
                        break;
                    }
                    $tempOcrResult[] = $oneWord['words'];
                }
                unset($onePicInfo['ocr']);
                $onePicInfo['ocr'] = $tempOcrResult;
            }
            if ($onePicInfo['guessword']) {
                foreach ($onePicInfo['guessword'] as $idx => $oneGuessWord) {
                    $guessWordKeyword = $oneGuessWord['keyword'];
                    if (isset(Util_ImageBlackWords::$imageBlackWords[$guessWordKeyword])) {
                        $onePicInfo['guessword'][$idx]['is_block'] = 1;
                    }
                }
            }
            $tempArr[$onePic] = $onePicInfo;
        }
        return $tempArr;
    }

    /**
     * @param $threadInfo
     * @return bool
     */
    private static function isValidTwThread($threadInfo) {
        Bingo_Log::pushNotice('thread_type', $threadInfo['thread_type']);
        if ($threadInfo['thread_type'] != 0 && $threadInfo['thread_type'] !== 14 && $threadInfo['thread_type'] !== 6) { //非普通图文帖立即返回
            Bingo_Log::notice("not tw thread, thread id is :" . $threadInfo['thread_id'] . ' thread_type is ' . $threadInfo['thread_type']);
            return false;
        }
        if (Service_Tool_Tool::checkIfDeleted($threadInfo)) {
            return false;
        }
        return true;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function getImageFeature($arrInput) {
        $imageSrc = array_slice($arrInput['image_urls'], 0, $this->picMaxNum);
        Bingo_Log::pushNotice('image_urls', count($imageSrc));
        if (empty($imageSrc)) {
            Bingo_Log::notice('no image for this thread, tid:' . $arrInput['thread_id']);
            return array();
        }
        $isVideo = $arrInput['is_video'];

        $multi = new Tieba_Multi('img_feature');
        foreach ($imageSrc as $oneFile) {
            $fileName = self::getFileName($oneFile);
            // 图片的 idl 特征
            foreach ($this->urlList as $key => $oneInfo) {
                if ($isVideo) {
                    if ($key == "ocr" || $key == "guessword") {
                        continue; //视频不需要这几个特征
                    }
                }
                $appid = $this->defaultAppid;
                $sk = $this->defaultsk;
                if (isset($oneInfo['appid']) && isset($oneInfo['sk'])) {
                    $appid = $oneInfo['appid'];
                    $sk = $oneInfo['sk'];
                }
                $token = $this->getToken($appid, $sk);
                $url = $oneInfo['url'];

                if (!$url) {
                    continue;
                }
                $info = array(
                    'access_token' => $token,
                    'ie' => 'utf8',
                    'format' => 'json',
                );
                if (!empty($oneInfo['param']) && is_array($oneInfo['param'])) {
                    $info = array_merge($info, $oneInfo['param']);
                }
                $featureInput = array(
                    'info' => $info,
                    'api' => $url,
                    'url' => $oneFile,
                    'file' => $fileName,
                    'feature' => $key,
                    'tid' => $arrInput['thread_id'],
                    'try_twice' => 1,
                );

                $multi->register("$fileName:$key", new Tieba_Service('inbound'),
                    array(
                        'serviceName' => 'inbound',
                        'method' => 'getOnePicOneFeatureNew',
                        'input' => $featureInput,
                        'extra' => array(
                            'rtimeout' => 2500,
                        ),
                        'ie' => 'utf-8',
                    )
                );

                // es_sharpness

            }

            $hashInput = array(
                'thread_id' => $arrInput['thread_id'],
                'file' => $fileName,
                'url' => $oneFile,
                'try_twice' => 1,
            );
            $multi->register(
                $fileName . ":hash",
                new Tieba_Service('inbound'),
                array(
                    'serviceName' => 'inbound',
                    'method' => 'getHashByImageUrl',
                    'input' => $hashInput,
                    'ie' => 'utf-8',
                )
            );
            /*
            $multi->register(
                $fileName . ":es",
                new Tieba_Service('inbound'),
                array(
                    'serviceName' => 'inbound',
                    'method' => 'getEsSharp',
                    'input' => $hashInput,
                    'ie' => 'utf-8',
                )
            );
            */
        }

        $multi->call();
        $data = array();
        foreach ($imageSrc as $oneFile) {
            $fileName = self::getFileName($oneFile);;
            foreach ($this->urlList as $key => $oneInfo) {
                $pk = $fileName . ":" . $key;
                $tempRet = $multi->getResult($pk);
                if ($tempRet['data']['feature']) {
                    $data[$fileName][$key] = $tempRet['data']['feature'];
                }
            }
            $hashKey = $fileName . ":hash";

            $tempRet = $multi->getResult($hashKey);

            if ($tempRet['data']['image_sign']) {
                $data[$fileName]['hash'] = $tempRet['data']['image_sign'];
            }
            $esSharpKey = $fileName . ":es";
            $sharpRet = $multi->getResult($esSharpKey);
            if ($sharpRet['data']['feature']) {
                $data[$fileName]['es_sharpness'] = $sharpRet['data']['feature'];
            }
            if ($tempRet['data']['g']) {
                $data[$fileName]['g'] = 1;
            }
        }
        return array('image_feature' => $data);
    }
}