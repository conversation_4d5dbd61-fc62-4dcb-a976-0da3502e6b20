<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2019/7/16
 * Time: 4:14 PM
 */

class Service_Odyssey_Video extends Libs_BaseService {


    /**
     * @param $input
     * @return array
     */
    public static function tiebaVideoUnderStand() {
        $arrInput = file_get_contents("php://input");
        Bingo_Log::notice(serialize($arrInput));
        $arrInput = json_decode($arrInput, true);
        $input = self::checkAndDecodeInput($arrInput);
        if (!$input) {
            return array(
                'errno' => Tieba_Errcode::ERR_SUCCESS,
                'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
                'nid' => $arrInput['nid'],
                'res' => 1,
                'data' => array(),
                'logid' => Bd_Log::genLogID(),
                'id' => $arrInput['id'],
                'versionid' => $arrInput['versionid'],
            );

        }
        $obj = new Service_Nmq_Video();
        $ret = $obj->callTiebaVideoContentUnderstanding($input);
        if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning("failed to get data from understanding server." . serialize($ret));
            return self::generalOutput($arrInput, $input, $ret);
        }
        return self::generalOutput($arrInput, $input, $ret);
    }


    /**
     * @param $arrInput
     * @return array
     */
    private static function generalOutput($arrInput, $input, &$ret) {
        $ret['nid'] = $input['nid'];
        $ret['res'] = 1;
        $ret['intervene'] = 0;
        $data = json_encode($ret['data'], JSON_UNESCAPED_UNICODE);

        $orData = $ret['data'];
        unset($ret['data']);

        $ret['data']['strategy_exinfo'] = $data;
        $ret['data']['rmb_tags'] = preg_replace("/,/", "$$", $orData['tag']);
        Bingo_Log::notice('output: ' . serialize($ret));
        // 因为odyssey解析非0的errno有问题

        if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning('errno is no 0' . serialize($ret));
            $ret['errno'] = Tieba_Errcode::ERR_SUCCESS;
            $ret['data'] = array();
        }
        return $ret;
    }

    /**
     * @param $arrInput
     * @return bool|mixed
     */
    private static function checkAndDecodeInput($arrInput) {
        if (!isset($arrInput['nid'])) {
            Bingo_Log::warning('no nid , invalid input.' . serialize($arrInput));
            return false;
        }
        if (!isset($arrInput['data']) || empty($arrInput['data'])) {
            Bingo_Log::warning('no data, invalid input.' . serialize($arrInput));
            return false;
        }
        $strategyExInfo = $arrInput['data']['strategy_exinfo'];
        if (!$strategyExInfo) {
            Bingo_Log::warning('no exinfo, invalid input.' . serialize($arrInput));
            return false;
        }

        $input = json_decode($strategyExInfo, true);
        if (!$input) {
            Bingo_Log::warning('strategy_exinfo is not json, please check.' . serialize($arrInput));
            return false;
        }
        if (!isset($input['origin_input']) && !isset($input['edit_info'])) {
            Bingo_Log::warning('no origin_input and edit_info' . serialize($arrInput));
            return false;
        }
        $input['origin_input']['level_1_name'] = $input['first_dir'];
        $input['origin_input']['level_2_name'] = $input['second_dir'];
        $input['nid'] = $arrInput['nid'];
        $odysseyLogid = $arrInput['logid'];
        $input['odyssey_logid'] = $odysseyLogid;
        self::log($input);
        return $input;
    }

    /**
     * @param $arrInput
     * @return
     */
    private static function log($arrInput) {
        Bingo_Log::pushNotice("nid", $arrInput['nid']);
        Bingo_Log::pushNotice("tid", $arrInput['origin_input']['thread_id']);
        Bingo_Log::pushNotice("odyssey_logid", $arrInput['odyssey_logid']);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function commitVideoBackToOdyssey($arrInput) {
        $utilObj = new Util_Odyssey();
        $url = $utilObj->generalUrl($arrInput['thread_id']);
        $feature = $arrInput['feature'];
        $bjhOriginInfo = $arrInput['bjh_info_origin'];

        $feature['thread_id'] = $arrInput['thread_id'];
        $feedData = self::generalVideoInfo($feature, $bjhOriginInfo);
        if (!$feedData) {
            Bingo_Log::notice('$bjhOriginInfo ' . serialize($bjhOriginInfo));
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $feedInput = array(
            'url' => $url,
            'data' => $feedData,
            'service_name' => Util_Odyssey::FEED_SERVICE_VIDEO,
        );

        if (function_exists("fastcgi_finish_request")) {
            // 耗时太长，直接返回， ，异步请求。
            self::asyncRet($feedInput, $arrInput['format']);
            fastcgi_finish_request();
        }
        Bingo_Timer::start("callOdysseyServer_1");
        $feedRes = Util_Odyssey::callOdysseyServer($feedInput);
        Bingo_Log::notice("video_import from -> " . serialize($feedData) . " output . " . serialize($feedRes));
        Bingo_Timer::end("callOdysseyServer_1");
        if (!$feedRes) {
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::succRet($arrInput['thread_id']);

    }

    /**
     * @param $ret
     * 支持格式 mcpack json php
     */
    protected static function asyncRet($ret, $format) {
        $result = null;
        $arrOutput = self::succRet($ret);
        if ($format == 'mcpack') {
            $result = mc_pack_pack2text(mc_pack_array2pack($arrOutput));
        } else if ($format == 'json' || $format == 'mcpackraw') {
            $result = Bingo_String::array2json($arrOutput, self::$service_ie);
        } else {
            $result = serialize($arrOutput);
        }
        echo $result;
    }

    /**
     * @param $feature
     * @param $bjhOriginInfo
     * @return array|bool
     */
    private static function generalVideoInfo($feature, $bjhOriginInfo) {

//        $bjhVideoContent = $bjhOriginInfo['video_content'] ? $bjhOriginInfo['video_content'] : $bjhOriginInfo['content'];
        $bjhVideoContent = self::generalVideoContent($feature, $bjhOriginInfo);
        if (!$bjhVideoContent) {
            Bingo_Log::notice("bjhVideoContent Empty . " . json_encode($bjhOriginInfo));
            return false;
        }
        $input = array();
        $input['title'] = $feature['title'];
        $input['type'] = 'video';
        $input['description'] = $bjhVideoContent['desc'] ? $bjhVideoContent['desc'] : $input['title'];
        $input['cover_images'] = $bjhVideoContent[0]['http']['cover'] ? json_encode(array($bjhVideoContent[0]['http']['cover'])) : "";
        $input['content'] = $bjhVideoContent;
        $input['status'] = "publish";

        $input['publish_at'] = $feature['create_time'] ? $feature['create_time'] : time();

        $input['source_from_id'] = json_encode(array('tieba_import' => array(9000008)));
        $input['source_from'] = json_encode(array("tieba_video"));

        // 百家号引入内容需要区分source
        if (isset($feature['bjh_thread_type']) && $feature['bjh_thread_type']) {
            $input['source_from'] = Util_Odyssey::$dynamicVideoSourceFrom;
            $input['source_from_id'] = Util_Odyssey::$dynamicVideoSourceFromID;
        }
        $input['feed_content'] = "";
        $input['rmb_checkAccept'] = 1;
        $input['displaytype_exinfo'] = "";
        $input['strategy_exinfo'] = $feature['strategy_exinfo'];
        $input['rmb_tags'] = preg_replace("/,/", "$$", $feature['tag']);
        $utilObj = new Util_Odyssey();
        $url = $utilObj->generalUrl($feature['thread_id']);

        $input['url'] = $url;
        if ($bjhOriginInfo['video_type']) {
            $videoType = $bjhOriginInfo['video_type'];
        } elseif ($feature['bjh_thread_type'] == Util_Odyssey::ODYSSEY_TYPE_DYNAMIC_VIDEO
            || $feature['bjh_thread_type'] == Util_Odyssey::ODYSSEY_TYPE_QUANMIN_VIDEO) {
            $videoType = "3";
        }
        $input['video_type'] = $videoType;
        return $input;
    }


    /**
     * @param $feature
     * @param $bjhOriginInfo
     * @return array|string
     */
    private static function generalVideoContent($feature, $bjhOriginInfo) {
        if ($bjhOriginInfo['video_content']) {
            return $bjhOriginInfo['video_content'];
        }
        if ($bjhOriginInfo['m_ext']['displaytype_exinfo']) {
            $displayInfo = $bjhOriginInfo['m_ext']['displaytype_exinfo'];
            $oneContent = $displayInfo;
            $oneContent['title'] = $feature['title'];
            $oneContent['desc'] = $feature['title'];
            $oneContent['mediaId'] = $displayInfo['mediaId'];
            $oneContent['local'] = 1;
            $oneContent['size'] = $displayInfo['size'] ? $displayInfo["size"] : 1;
            $oneContent['long'] = $displayInfo['long'] ? $displayInfo['long'] : 1;
            $oneContent['videoName'] = $feature['title'];
            $oneContent['http']['file'] = $displayInfo['play_http'];
            $oneContent['http']['cover'] = $displayInfo['http_avatar'];
            $oneContent['https']['file'] = $displayInfo['play_https'];
            $oneContent['https']['cover'] = $displayInfo['https_avatar'];
            $content = array($oneContent);
            return $content;
        }
        return "";
    }

    /**
     * 添加请求Odyssey跳过的下游节点相关参数
     * @param $feedInput
    */
    private static function addVue(&$feedInput) {
        $feedInput['ext_data'] = array(
            '__DATA_NOTIFY__' => array(
                'miners_stub_info' => array(
                    array(
                        'name' => 'VuNode',
                    ),
                    array(
                        'name' => 'TiebaVideoAudit'
                    ),
                    array(
                        'name' => 'TiebaVideoUnderstand'
                    )
                ),
            ),
        );
    }

    /**
     * 更新正排里的内容
     * @param $input
     * @return array
     */
    public static function updateVideoInfoInZp($input) {
        if (!isset($input['tid']) || empty($input['tid'])) {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $utilObj = new Util_Odyssey();
        $url = $utilObj->generalUrl($input['tid']);
        $strategyExinfo = $input['feature'];
        $feedData = array(
            'strategy_exinfo' => $strategyExinfo,
        );
        $feedInput = array(
            'url' => $url,
            'data' => $feedData,
            'service_name' => Util_Odyssey::FEED_SERVICE_VIDEO,
        );
        if (isset($input['need_odyssey_jump']) && $input['need_odyssey_jump']) {
            self::addVue($feedInput);
        }
        $feedRes = Util_Odyssey::callOdysseyServer($feedInput, Util_Odyssey::COMMAND_UPDATE);
        if (!$feedRes) {
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::succRet($input['tid']);
    }

    /**
     * @param $input
     * @return array
     */
    public static function deleteVideoFromZp($input) {
        if (!isset($input['tid']) || empty($input['tid'])) {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        Bingo_Log::notice(__FUNCTION__ . "  " . serialize($input));

        $utilObj = new Util_Odyssey();
        $url = $utilObj->generalUrl($input['tid']);
        $strategyExinfo = $input['feature'];
        $feedData = array(
            'strategy_exinfo' => $strategyExinfo,
            'status' => 'deleted',
        );
        $feedInput = array(
            'url' => $url,
            'data' => $feedData,
            'service_name' => Util_Odyssey::FEED_SERVICE_VIDEO,
        );
        // 因为百家号的内容删除会清理截图，所以引入内容删除的时候，是真-删除，UGC还是update

        $decodeFeature = json_decode($strategyExinfo, true);
        if($decodeFeature['bjh_thread_type'] > 0 || $input['bjh_thread_type'] > 0) {
            $command = Util_Odyssey::COMMAND_DEL;
        } else {
            $command = Util_Odyssey::COMMAND_UPDATE;
        }

        $feedRes = Util_Odyssey::callOdysseyServer($feedInput, $command);
        if (!$feedRes) {

            $feedRes = Util_Odyssey::callOdysseyServer($feedInput, $command);
            if(!$feedRes) {
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }

        return self::succRet($input['tid']);
    }
}