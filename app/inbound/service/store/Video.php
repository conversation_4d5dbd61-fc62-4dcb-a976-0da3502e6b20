<?php

/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2017/10/11
 * Time: 下午3:38
 */
class Service_Store_Video extends Libs_BaseService {

    public static $service_ie = 'utf-8';

    public static $redisName = "frecommend";
    public static $keyPrefix = "thread_store_video";
    public static $signPrefix = 'sign_store_video';
    public static $mintFeature = 'tieba_thread_video';

    /**
     * nmq 之后存储
     * @param $arrInput
     * @return array
     */
    public static function addVideoRecord($arrInput, $bjhInfo = false) {
        //Bingo_Log::notice(serialize($arrInput));
        if (!isset($arrInput['tid']) && isset($arrInput['thread_id'])) {
            $arrInput['tid'] = $arrInput['thread_id'];
        }
        $isBjhThread = $arrInput['bjh_thread_type'] ? 1 : 0;
        $tid = $arrInput['tid'];
        $sign = $arrInput['sign'];
        $width = $arrInput['video_width'];
        $height = $arrInput['video_height'];
        $duration = $arrInput['video_duration'];
        $opStatus = $arrInput['op_status'];
        $isManual = $arrInput['is_manual'];

        $features = $arrInput;
        unset($features['op_status']);
        //获取之前这个帖子的状态，和是否和已入库的帖子状态冲突
        if ($sign) { // 如果当前 sign 计算失败了，采用宽策略，放行
            Bingo_Timer::start("sameSignOnline_1");
            $preInfo = self::sameSignOnline($arrInput);
            Bingo_Timer::end("sameSignOnline_1");
            $preOnline = false;


            Bingo_Log::notice('pre_info: ' . serialize($preInfo));
            foreach ($preInfo as $oneSignInfo) {
                if ($oneSignInfo['tid'] == $tid) {
                    $preOnline = ($oneSignInfo['op_status'] == Util_VideoConst::OP_STATUS_ONLINE) ? true : false;
                } else if ($oneSignInfo['sign'] == $sign
                    && $oneSignInfo['video_width'] == $width
                    && $oneSignInfo['video_height'] == $height
                    && $oneSignInfo['video_duration'] == $duration
                    && $oneSignInfo['op_status'] == Util_VideoConst::OP_STATUS_ONLINE) {
                    $opStatus = Util_VideoConst::OP_STATUS_HASH_CONFLICT;
                    $arrInput['op_status'] = $opStatus;
                }
            }
        }

        $hashList = $features['hash_list'];
        unset($features['hash_list']);
        if ($preOnline || $opStatus == Util_VideoConst::OP_STATUS_ONLINE || $isManual) {
            if ($opStatus != Util_VideoConst::OP_STATUS_ONLINE) {
                $features['reset'] = -1;
            }
            $arrInput['feature'] = json_encode($features, JSON_UNESCAPED_UNICODE);
            // 写 redis
            if ($sign || $isBjhThread) { // 线上正常的视频也有可能是 sign 为 0，比如截图是黑的
                self::insertVideoIntoRedis($arrInput);
            } else {
                $arrInput['op_status'] = Util_VideoConst::OP_STATUS_NO_SIGN;
            }

        }
        if ($hashList) {
            $features['hash_list'] = $hashList;
        }
        $features = Util_VideoConst::unsetNoDbKeys($features); // 特征Server返回很多没用的，db也不存了；
        $strFeature = Util_Const::filterEmoji(json_encode($features, JSON_UNESCAPED_UNICODE));

        $len = strlen($arrInput['feature']);
        if ($len > 4800) {
            Bingo_Log::fatal('feature too long. please notice!!!!');
            foreach ($features as $key => &$value) {
                if (is_null($value) || empty($value) || !$value) {
                    unset($features[$key]);
                    continue;
                }
                if (is_double($value)) {
                    $value = sprintf("%.03f", $value);
                }
            }
            $strFeature = Util_Const::filterEmoji(json_encode($features, JSON_UNESCAPED_UNICODE));
        }
        $arrInput['feature'] = $strFeature;
        $arrInput['last_update_time'] = time();
        // 处理first_valid_time
        if ($arrInput['op_status'] == Util_VideoConst::OP_STATUS_ONLINE) {
            $preFeatureRet = self::getVideoThreadInfo($arrInput);
            if (Tieba_Errcode::ERR_SUCCESS !== $preFeatureRet['errno']) {
                Bingo_Log::warning('failed to get from database. input : ' . serialize($arrInput));
            } else {
                $preFirstValidTime = $preFeatureRet['data']['first_valid_time'];
                $arrInput['first_valid_time'] = $preFirstValidTime ? $preFirstValidTime : time();
            }
        }
        Bingo_Log::notice('insert into db: ' . serialize($arrInput));

        if ($bjhInfo) {
            $features['strategy_exinfo'] = $strFeature;
            $odysseyInput = array(
                'thread_id' => $tid,
                'thread_create_time' => $arrInput['create_time'],
                'strategy_exinfo' => $strFeature,
                'feature' => $features,
                'bjh_info_origin' => $bjhInfo,
            );
            // call thirdparty
            // $ret = Service_Odyssey_Video::commitVideoBackToOdyssey($odysseyInput);
            $ret = Tieba_Service::call('inbound', 'commitVideoBackToOdyssey', $odysseyInput, null, null, 'post', 'php', 'utf-8');
            //$ret = Tieba_Service::call("thirdparty", "odysseyCommitVideo", $odysseyInput, null, null, 'php', 'post', 'utf-8');
            if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
                Bingo_Log::warning('failed to call local, input: ' . serialize($odysseyInput) . ' output: ' . serialize($ret));
            }
        }
        //Bingo_Log::notice('insert into db: ' . serialize($arrInput));
        return Dl_Store_Video::uniteInsert($arrInput);
    }


    /**
     *  如果有之前的与当前帖子不是同一个 tid 但是 sign 相同且是线上状态的帖子，则不能入库
     * +屎一样的 nani 逻辑
     * @param $arrInput
     * @return bool
     */
    public static function sameSignOnline($arrInput) {
        $hash = $arrInput['sign'];
        $hashList = $arrInput['hash_list'];
        $hashArr = $hashList;
        $hashArr[] = $hash;
        //$hashArr = self::similarImageHash($hash, $hashList);
        $dlInput = array(
            'hash_value' => $hashArr,
        );
        $preRet = Dl_Store_Video::similarHash($dlInput);
        return $preRet['data'];
    }

    /**
     * 保留 first_valid_time 写入数据库
     * @param $arrInput
     * @return array
     */
    public static function replaceVideoRecord($arrInput, $needPre = false) {
        if ($needPre) {
            $preInfo = self::getVideoThreadInfo($arrInput);
            if (Tieba_Errcode::ERR_SUCCESS !== $preInfo['errno']) {
                Bingo_Log::warning('failed to get pre info');
            }
            $firstValidTime = $preInfo['data']['first_valid_time'];
            $arrInput['first_valid_time'] = $firstValidTime;
        }
        return self::addVideoRecord($arrInput);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function deleteVideoRecord($arrInput) {
        if (!isset($arrInput['tid'])) {
            Bingo_Log::warning('param error. input: ' . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        return Dl_Store_Video::deleteVideoThreadRecord($arrInput);
    }

    /**
     * @param $arrInput
     * @return array
     */
    private static function insertVideoIntoRedis($arrInput) {
        if (!isset($arrInput['thread_id']) || !isset($arrInput['feature'])) {
            Bingo_Log::warning('no tid or feature input. input : ' . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $feature = $arrInput['feature'];
        $input = array(
            'value' => $feature,
        );
        $input['feature'] = self::$mintFeature;
        $input['key'] = $arrInput['thread_id'];
        $ret = Libs_Mint::setKv($input);
        if (!$ret) {
            Bingo_Log::warning('Failed to insert into mint. input:' . serialize($input) . " output : " . serialize($ret));
            return self::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        Bingo_Log::notice("insert into mint succeed. key: " . $arrInput['thread_id']);
        return self::succRet();
    }


    /**
     * @param $arrInput
     * @return array|multitype
     */
    public static function getVideoFeatureFromMint($arrInput) {
        $feature = self::$mintFeature;
        $key = $arrInput['tid'];
        $input = array(
            'feature' => $feature,
            'tids' => array($key),
        );
        $ret = Libs_Mint::getKv($input);
        if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning('failed to get data from mint. output: ' . serialize($ret));
            return self::errRet($ret['errno']);
        }
        return self::succRet($ret['data']);
    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function insertVideoFeatureToMintNew($arrInput) {
        $feature = self::$mintFeature;
        $key = $arrInput['tid'] ? $arrInput['tid'] : $arrInput['thread_id'];
        $v = json_decode($arrInput['feature'], true);

        $value = json_encode($v, JSON_UNESCAPED_UNICODE);
        //$value = json_encode($arrInput['feature'], JSON_UNESCAPED_UNICODE);
        $input = array(
            'feature' => $feature,
            'key' => $key,
            'value' => $value,
        );
        $ret = Libs_Mint::setKv($input);
        if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning('failed to update  mint info. input: ' . serialize($input) . ' output: ' . serialize($ret));return self::errRet($ret['errno']);
        }
        $dlInput = array(
            'tid' => $arrInput['tid'],
            'feature' => Util_Const::filterEmoji(json_encode($value, JSON_UNESCAPED_UNICODE)),
        );
        $ret = Service_Odyssey_Video::updateVideoInfoInZp($dlInput);
        if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning('failed to update odyssey info. input: ' . serialize($dlInput) . ' output: ' . serialize($ret));
            return self::errRet($ret['errno']);
        }
        return self::succRet($ret);

    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function insertVideoFeatureToMint($arrInput) {
        $feature = self::$mintFeature;
        $key = $arrInput['tid'] ? $arrInput['tid'] : $arrInput['thread_id'];
        $value = json_encode($arrInput['feature'], JSON_UNESCAPED_UNICODE);
        $input = array(
            'feature' => $feature,
            'key' => $key,
            'value' => $value,
        );
        $ret = Libs_Mint::setKv($input);
        return self::succRet($ret);

    }

    /**
     * @param $arrInput
     * @return array|multitype
     */
    public static function getFeatureFromRedisAndInsertIntoMint($arrInput) {
        if (!isset($arrInput['tid']) || !$arrInput['tid']) {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $libRedis = new Libs_Redis(self::$redisName);
        $tid = $arrInput['tid'];
        $key = self::getKey(strval($tid));

        $ret = $libRedis->getKv($key);

        if (!$ret) {
            return self::succRet($ret);
        }
        $input = array(
            'feature' => self::$mintFeature,
            'key' => $tid,
            'value' => $ret,
        );
        Bingo_Log::notice('trans into mint : key: ' . $tid . " value: " . serialize($ret));
        $ret = Libs_Mint::setKv($input);
        return self::succRet($ret);
    }

    /**
     * @param $arrInput
     * @return array|multitype
     */
    public static function updateVideoExpire($arrInput) {
        if (!isset($arrInput['thread_id'])) {
            Bingo_Log::warning('no tid input. input : ' . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $libRedis = new Libs_Redis(self::$redisName);
        $key = self::getKey($arrInput['thread_id']);
        $ret = $libRedis->updateExpireTime($key);
        return self::succRet($ret);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function deleteVideoFromRedis($arrInput) {
        if (!isset($arrInput['tid']) && !isset($arrInput['thread_id'])) {
            Bingo_Log::warning('no tid input. input : ' . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $tid = $arrInput['tid'] ? $arrInput['tid'] : $arrInput['thread_id'];
        $libRedis = new Libs_Redis(self::$redisName);
        $key = self::getKey($tid);
        $input = array($key);
        $ret = $libRedis->mdelKv($input);
        if (!$ret) {
            Bingo_Log::warning('Failed to del from redis. input:' . $key . ' output:' . serialize($ret));
            return self::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        return self::succRet();
    }


    public static function deleteFromMint($arrInput){
        if($arrInput['tid'] && !$arrInput['thread_id']) {
            $arrInput['thread_id'] = $arrInput['tid'];
        }


        $preInfoRet = self::getVideoThreadInfo($arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $preInfoRet['errno']) {
            Bingo_Log::warning('failed to get pre info');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if(empty($preInfoRet['data']) || empty($preInfoRet['data']['feature'])) {
            return self::succRet($arrInput);
        }
        $feature = json_decode($preInfoRet['data']['feature'], true);

        $feature['reset'] = -1;
        $preInfo['thread_id'] = $arrInput['thread_id'];
        $preInfo['tid'] = $arrInput['tid'];
        $filterFeature = Util_VideoConst::filterFinalKeysNoConvert($feature);
        $preInfo['feature'] = $filterFeature; // 对于存到mint的特征，需要进行过滤
        $ret = Service_Store_Video::insertVideoFeatureToMint($preInfo);
        if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning('failed to update mint feature, : ' .  serialize($preInfo));
        }
        $preInfo['feature'] = json_encode($filterFeature, JSON_UNESCAPED_UNICODE);
        //Bingo_Log::notice(__FUNCTION__ . " " . serialize($preInfo));
        $ret = Service_Odyssey_Video::deleteVideoFromZp($preInfo);
        return self::succRet($ret);
    }
    /**
     * @param $arrInput
     * @return array
     */
    public static function getVideoThreadInfoFromRedis($arrInput) {
        if (!isset($arrInput['thread_id']) || empty($arrInput['thread_id'])) {
            Bingo_Log::warning('no tid input. input : ' . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $libRedis = new Libs_Redis(self::$redisName);
        $key = self::getKey(strval($arrInput['thread_id']));

        $ret = $libRedis->getKv($key);
        if (false === $ret) {
            Bingo_Log::warning('Failed to get data from redis. key input:  ' . $key);
            return self::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        return self::succRet($ret);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function mgetVideoThreadInfoFromRedis($arrInput) {
        if (!isset($arrInput['tids']) || !is_array($arrInput['tids'])) {
            Bingo_Log::warning('no tids input. input : ' . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $libRedis = new Libs_Redis(self::$redisName);
        foreach ($arrInput['tids'] as $tid) {
            $input[] = self::getKey($tid);
        }
        $redisRet = $libRedis->mgetKv($input);

        if (false === $redisRet) {
            Bingo_Log::warning('Failed to get data from redis. key input:  ' . $input);
            return self::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        $ret = array();
        return self::succRet($ret);
    }


    /**
     * @param $tid
     * @return string
     */
    private static function getKey($tid) {
        return self::$keyPrefix . $tid;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getVideoThreadInfo($arrInput) {
        if (!isset($arrInput['thread_id'])) {
            Bingo_Log::warning('No thread_id is set. input: ' . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrInput['tid'] = $arrInput['thread_id'];
        return Dl_Store_Video::getVideoThreadInfo($arrInput);
    }

    /**
     *  更新视频的一个特征：注意不能更新 sign!!!!
     * @param $arrInput
     * @return array|multitype
     */
    public static function updateVideoFeature($arrInput) {
        if (!isset($arrInput['thread_id']) || !isset($arrInput['key']) || !isset($arrInput['value'])) {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if($arrInput['force_in']) {
            $forceIn = true;
        }
        if($arrInput['no_update_zp']) {
            $noUpdateZp = true;
        }
        $preInfoRet = self::getVideoThreadInfo($arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $preInfoRet['errno']) {
            Bingo_Log::warning('failed to get pre info');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $preFeatures = json_decode($preInfoRet['data']['feature'], true);
        $value = json_decode($arrInput['value']);

        $key = $arrInput['key'];
        $preFeatures[$key] = $value;
        if($forceIn) {
            unset($preFeatures['reset']);
            $preFeatures['scene'] = Util_Const::MANUAL_INSERT_SCENE;
        }

        $feature = Util_Const::filterEmoji(json_encode($preFeatures, JSON_UNESCAPED_UNICODE));
        $dlInput = array(
            'tid' => $arrInput['thread_id'],
            'thread_id' => $arrInput['thread_id'],
            'feature' => $feature,
        );
        if($forceIn) {
            $dlInput['last_update_time'] = time();
            $dlInput['op_status'] = Util_Const::OP_STATUS_ONLINE;
        }
        Dl_Store_Video::updateVideoThreadInfo($dlInput);
        foreach(Util_VideoConst::$noMintKeys as $key) {
            unset($preFeatures[$key]);
        }

        $dlInput['feature'] = Util_Const::filterEmoji(json_encode($preFeatures, JSON_UNESCAPED_UNICODE));

        if ($preInfoRet['data']['op_status'] == Util_VideoConst::OP_STATUS_ONLINE || $forceIn) {
            self::insertVideoIntoRedis($dlInput);
            $dlInput['format'] = $arrInput['format'];
            $dlInput['ie'] = $arrInput['ie'];
            if($noUpdateZp) {
                return self::succRet($arrInput['thread_id']);
            }
            $ret = Service_Odyssey_Video::updateVideoInfoInZp($dlInput);
            if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
                Bingo_Log::warning('failed to update odyssey info. input: ' . serialize($dlInput) . ' output: ' . serialize($ret));
                return self::errRet($ret['errno']);
            }
        }
        return self::succRet($arrInput['thread_id']);
    }



    /**
     *  更新视频的一个特征：注意不能更新 sign!!!!
     * @param $arrInput
     * @return array|multitype
     */
    public static function updateMultiVideoFeature($arrInput) {
        if (!isset($arrInput['thread_id']) || !isset($arrInput['feature'])) {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if($arrInput['force_in']) {
            $forceIn = true;
        }
        $preInfoRet = self::getVideoThreadInfo($arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $preInfoRet['errno']) {
            Bingo_Log::warning('failed to get pre info');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $toProcessFeature = $arrInput['feature'];
        $preFeatures = json_decode($preInfoRet['data']['feature'], true);
        if ($arrInput['base_64']) {
            $featureInput = json_decode(base64_decode($toProcessFeature), true);
        } else {
            $featureInput = json_decode($toProcessFeature, true);
        }
        Bingo_Log::notice("feature to update. " . serialize($toProcessFeature) . " decode: " . serialize($featureInput));
        if (empty($featureInput)) {
            Bingo_Log::warning("feature input invalid. " . serialize($toProcessFeature) . " decode: " . serialize($featureInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        foreach($featureInput as $key => $value) {
            $preFeatures[$key] = $value;
        }

        if($forceIn) {
            unset($preFeatures['reset']);
            $preFeatures['scene'] = Util_Const::MANUAL_INSERT_SCENE;
        }

        $feature = Util_Const::filterEmoji(json_encode($preFeatures, JSON_UNESCAPED_UNICODE));
        $dlInput = array(
            'tid' => $arrInput['thread_id'],
            'thread_id' => $arrInput['thread_id'],
            'feature' => $feature,

        );
        if (isset($arrInput['last_update_time']) && $arrInput['last_update_time']) {
            $dlInput['last_update_time'] = $arrInput['last_update_time'];
        }
        if($forceIn) {
            $dlInput['last_update_time'] = time();
            $dlInput['op_status'] = Util_Const::OP_STATUS_ONLINE;
        }
        Dl_Store_Video::updateVideoThreadInfo($dlInput);
        foreach(Util_VideoConst::$noMintKeys as $key) {
            unset($preFeatures[$key]);
        }

        $dlInput['feature'] = Util_Const::filterEmoji(json_encode($preFeatures, JSON_UNESCAPED_UNICODE));
        if ($preInfoRet['data']['op_status'] == Util_VideoConst::OP_STATUS_ONLINE || $forceIn) {
            self::insertVideoIntoRedis($dlInput);
            $dlInput['format'] = $arrInput['format'];
            $dlInput['ie'] = $arrInput['ie'];
            if (isset($arrInput['need_odyssey_jump']) && $arrInput['need_odyssey_jump']) {
                $dlInput['need_odyssey_jump'] = $arrInput['need_odyssey_jump'];
            }
            $ret = Service_Odyssey_Video::updateVideoInfoInZp($dlInput);
            if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
                Bingo_Log::warning('failed to update odyssey info. input: ' . serialize($dlInput) . ' output: ' . serialize($ret));
                return self::errRet($ret['errno']);
            }
        }
        return self::succRet($arrInput['thread_id']);
    }

    /**
     *  判断是不是已经入库，判断视频质量
     * @param $arrInput
     * @return array|multitype
     */
    public static function videoRecomQuality($arrInput) {
        if (!isset($arrInput['thread_id']) || !isset($arrInput['video_type']) || !$arrInput['thread_id']
        ) {
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $videoType = $arrInput['video_type'];
        $tid = $arrInput['thread_id'];
        Bingo_Log::pushNotice('tid', $tid);
        Bingo_Log::pushNotice('video_type', $videoType);
        $mgetRet = Service_Videointeraction_Videodetail::getVideoInfoFromMgetthread($arrInput);
        if (!$mgetRet || Tieba_Errcode::ERR_SUCCESS !== $mgetRet['errno']) {
            return self::errRet($mgetRet['errno']);
        }
        $videoInfo = $mgetRet['data']['video_info'];
        $width = $videoInfo['video_width'];
        $height = $videoInfo['video_height'];
        $duration = $videoInfo['video_duration'];
        $arrInput['mget'] = $mgetRet['data'];
        $hashRet = Service_Videointeraction_Videohash::videoHashAndDatou($arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $hashRet['errno']) {
            return self::errRet($hashRet['errno']);
        }
        $isDatou = $hashRet['data']['is_datou'];
        $isHeibian = $hashRet['data']['is_heibian'];
        $sign = $hashRet['data']['sign'];
        $hashList = $hashRet['data']['hash_list'];
        $signInput = array(
            'sign' => $sign,
            'hash_list' => $hashList,
        );
        Bingo_Timer::start("sameSignOnline");
        $preInfo = self::sameSignOnline($signInput);
        Bingo_Timer::end("sameSignOnline");

        $conflictWithNani = 0;
        $conflictWithTieba = 0;
        $recommending = 0;
        foreach ($preInfo as $oneSignInfo) {
            if ($oneSignInfo['tid'] == $tid) {
                $recommending = 1;
                break;
            } else if ($oneSignInfo['sign'] == $sign
                && $oneSignInfo['video_width'] == $width
                && $oneSignInfo['video_height'] == $height
                && $oneSignInfo['video_duration'] == $duration
                && $oneSignInfo['op_status'] == Util_VideoConst::OP_STATUS_ONLINE) {
                if (in_array($oneSignInfo['video_type'], Util_VideoConst::$whiteSmallVideoTypes)) {
                    $conflictWithNani = 1;
                } else {
                    $conflictWithTieba = 1;
                }
            }
        }
        $conflictType = 0;
        if ($conflictWithTieba) {
            $conflictType += 1;
        }
        if ($conflictWithNani) {
            $conflictType += 2;
        }
        if ($recommending) {
            $conflictType = 100;
        }
        $rubbishTypeInput = array(
            'is_datou' => $isDatou,
            'is_heibian' => $isHeibian,
        );
        $rubbishType = self::generalRubbishType($rubbishTypeInput);

        $ret = array(
            'thread_id' => $tid,
            'rec_type' => $conflictType,
            'rubbish_type' => $rubbishType,
        );
        Bingo_Log::pushNotice('rec_type', $conflictType);
        Bingo_Log::pushNotice('rubbish_type', $rubbishType);
        Tieba_Service::call('recom', 'videoQualityCallback', $ret, null, null, 'post', 'php', 'utf-8');
        return self::succRet($ret);
    }

    /**
     * @param $arrInput
     * @return int
     */
    private static function generalRubbishType($arrInput) {
        $rubbishType = 0;
        if ($arrInput['is_datou']) {
            $rubbishType |= 1;
        }
        if ($arrInput['is_heibian']) {
            $rubbishType |= 2;
        }
        return $rubbishType;
    }

//    /**
//     * @param $arrInput
//     * @return array|multitype
//     */
//    public static function deleteVideoFromMint($arrInput) {
//        if (!isset($arrInput['thread_id']) || empty($arrInput['thread_id'])) {
//            Bingo_Log::warning('no thread_id input');
//            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
//        }
//        $tid = $arrInput['thread_id'];
//        $preInfo = self::getVideoThreadInfoFromRedis($arrInput);
//        //从 redis 里面删除
//        if (!$preInfo['data']) {
//            Bingo_Log::notice('nothing in redis, ' . $arrInput['thread_id']);
//        } else {
//            self::deleteVideoFromRedis($arrInput);
//        }
//
//        $input = array(
//            'tid' => $tid,
//            'op_status' => Util_VideoConst::OP_STATUS_HASH_CONFLICT,
//        );
//        $ret = Dl_Store_Video::updateVideoStatus($input);
//
//        return self::succRet($ret);
//    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function updateSignTable($arrInput) {
        return Dl_Store_Video::updateVideoSignTable($arrInput);
    }


    /**
     * @param $arrInput
     * @return array|multitype
     */
    public static function getSignAndInsert($arrInput) {
        if (!isset($arrInput['thread_id']) || empty($arrInput['thread_id']) || !$arrInput['thumbnail_url']) {
            Bingo_Log::warning('no thread_id input');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $tid = $arrInput['thread_id'];
        $signRet = Service_Videointeraction_Videohash::getVideoSign($arrInput);
        $sign = $signRet['data'];
        $preInfo = self::getVideoThreadInfo($arrInput);
        $featureRet = $preInfo['data']['feature'];
        $feature = json_decode($featureRet, true);
        $signInput = array(
            'sign' => $sign,
            'tid' => $tid,
            'thread_id' => $tid,
            'video_width' => $feature['video_width'],
            'video_height' => $feature['video_height'],
            'video_duration' => $feature['video_duration'],
            'op_status' => $preInfo['data']['op_status'],
            'video_type' => $feature['video_type'],
        );
        $ret = Dl_Store_Video::insertIntoSignTable($signInput);
        Bingo_Log::notice('insert into sign table, input : ' . serialize($signInput) . " output: " . serialize($ret));
        $ret = Dl_Store_Video::updateVideoThreadTableSign($signInput);
        Bingo_Log::notice('insert into thread table, input : ' . serialize($signInput) . " output: " . serialize($ret));
        // 写到线上存储
        if ($preInfo['data']['op_status'] == Util_Const::OP_STATUS_ONLINE) {
            $preInfo = self::getVideoThreadInfoFromRedis($arrInput);
            $data = json_decode($preInfo['data'], true);
            $data['sign'] = $sign;
            $feature = json_encode($data, JSON_UNESCAPED_UNICODE);
            $redisInput = $arrInput;
            $redisInput['feature'] = $feature;
            self::insertVideoIntoRedis($redisInput);
        }
        return self::succRet($tid);
    }

    /**
     * @param $arrInput
     * @return array|multitype
     */
    public static function delVideoManually($arrInput) {
        if (!isset($arrInput['thread_id']) || empty($arrInput['thread_id'])) {
            Bingo_Log::warning('no thread_id input');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $input = array(
            'tids' => array(
                $arrInput['thread_id'],
            ),
            'feature' => self::$mintFeature,
        );
        $preMintData = Libs_Mint::getKv($input);
        $mintRet = $preMintData['data'][0]['values'];
        $preFeature = json_decode($mintRet[0]['value'], true);
        if (!empty($preFeature)) {
            $preFeature['reset'] = -1;
            $preFeature['manually_delete'] = 1;
            $mintInput = array(
                'feature' => self::$mintFeature,
                'key' => $arrInput['thread_id'],
                'value' => json_encode($preFeature, JSON_UNESCAPED_UNICODE),
            );
            $ret = Libs_Mint::setKv($mintInput);
            if (!$ret) {
                Bingo_Log::warning('failed to insert into mint, key: ' . $arrInput['thread_id'] . " value: " . serialize($preFeature));
                return self::errRet(Tieba_Errcode::ERR_FAIL_TALK_WITH_REDIS, $ret);
            }
        }

        $preDbInfoRet = self::getVideoThreadInfo($arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $preDbInfoRet['errno']) {
            Bingo_Log::warning('failed to get data from database. input: ' . $arrInput['thread_id']);
            return self::errRet($preDbInfoRet['errno']);
        }
        $preDBInfo = $preDbInfoRet['data'];
        if ($preDBInfo['op_status'] !== Util_VideoConst::OP_STATUS_DELETED) {
            $preDBInfo['op_status'] = Util_VideoConst::MANUALLY_OFFLINE;
        }
        $preFeature = json_decode($preDBInfo['feature'], true);
        $preFeature['reset'] = -1;
        $preFeature['manually_delete'] = 1;
        $preDBInfo['feature'] = json_encode($preFeature, JSON_UNESCAPED_UNICODE);

        Dl_Store_Video::updateVideoThreadInfo($preDBInfo);
        // 正排更新操作
        $zpInput = array(
            'tid' => $arrInput['thread_id'],
            'thread_id' => $arrInput['thread_id'],
            'feature' => json_encode($preFeature, JSON_UNESCAPED_UNICODE),
        );
        $ret = Service_Odyssey_Video::updateVideoInfoInZp($zpInput);
        if (!$ret) {
            Bingo_Log::warning('failed to update zp, key: ' . $arrInput['thread_id'] . " value: " . serialize($preFeature));
        }

        $sign = $preFeature['sign'];
        if ($sign) {
            $preDBInfo['sign'] = $sign;
            Dl_Store_Video::updateVideoSignTable($preDBInfo);
        }
        return self::succRet($arrInput['thread_id']);
    }


    /**
     * 更新数据库里的Feature
     * @param $arrInput
     * @return array
     */
    public static function updateVideoThreadInfo($arrInput) {
        if (!isset($arrInput['thread_id']) || empty($arrInput['thread_id']) || !isset($arrInput['feature'])) {
            Bingo_Log::warning('no thread_id or feature input. input : ' . serialize($arrInput));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if ($arrInput['feature']['thumb_imgf']) {
            foreach ($arrInput['feature']['thumb_imgf'] as $key => &$value) {
                unset($value['ocr']);
                unset($value['guessword']);
                unset($value['md5']);
            }
        }
        $arrInput['feature'] = json_encode($arrInput['feature'], JSON_UNESCAPED_UNICODE);
        $arrInput['tid'] = $arrInput['thread_id'];
        $arrInput['last_update_time'] = time();

        return Dl_Store_Video::updateVideoThreadInfo($arrInput);
    }
}
