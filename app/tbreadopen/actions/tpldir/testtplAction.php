<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-03-18 15:06:18
 * @comment 模版接口
 * @version
 */
class testtplAction extends Util_Base {

    public function execute(){
    	try {
            //参数获取
            $uid = intval(Bingo_Http_Request::get('uid',0));




            // 模板输出
            //模板变量输出,初始化为默认值，自行修改
            Bingo_Page::assign('fname','');
            Bingo_Page::assign('infoArr',array());

            Bingo_Page::setTpl("index.php");
        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            Bingo_Page::setTpl("index.php");
            //也可以跳转302
            //Tieba_Error::fetalError302();
            
        }
    }

}
?>