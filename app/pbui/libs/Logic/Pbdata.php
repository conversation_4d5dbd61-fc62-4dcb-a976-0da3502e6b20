<?php
/**
 * ģ�������Ʋο�http://fe.baidu.com/doc/tieba/web/wangwen/php_interface.text
 * <AUTHOR> <<EMAIL>>
 *
 */
class Logic_Pbdata
{
	const THREAD_TYPE_NORMAL		= 0; // ��ͨ����
	const THREAD_TYPE_PIC_ALBUM		= 1; // �������
	const THREAD_TYPE_TBDISK		= 2; // ��������
	const THREAD_TYPE_VIDEO			= 3; // ��Ƶ����
	const THREAD_TYPE_VIDEO_ALBUM	= 4; // ��Ƶר������
	const THREAD_TYPE_ACTIVITY		= 5; // �����
	const JUMP_URL_DOMAIN_NAME = 'http://jump.bdimg.com';
     protected static $uid = null;
		
	public static $arrWaterInfo;
	public static $intPostTime;

    public static $fromPc = 	0;
	protected static $_arrSubUserMap = array();
    protected static $arrPage = array(
        'cur_page' => 1,
        'total_page' => 1,
        'exist_forbidden_tip' => 0,
    );		
  public function encodeUrl($matches)
    {
    	$orignalUrl = $matches[1];

        if((Request::$bolLogin === false) || (empty(Request::$intUid)))
        {
            $uid = 1;
        }
        else 
        {
        	$uid = Request::$intUid;
        } 
    
        $tmpUrl = htmlspecialchars_decode($orignalUrl);  
    	$arrInput = array(array(
                        'url' => $tmpUrl,
    			'uid' => $uid,
    			'bid'  => ltrim(Tieba_Session_Socket::getBaiduid(),'"'),
    	));
        $tmpStrstr = strstr($orignalUrl, "http://tieba.baidu.com/i/sys/jump");
        if ($tmpStrstr){
            return "<a href=\"" . $tmpurl."\" ".$matches[2]. ">" .$matches[3] . "</a>";
        }else{
      $encryptUrl = Tieba_Anti_UrlEncodeAndDecode::encode($arrInput);
      $url = $encryptUrl[0];     

      return "<a href=\"" . self::JUMP_URL_DOMAIN_NAME."/safecheck/index?url=".$url."\" " . $matches[2]. ">" .
    			$matches[3] . "</a>";
    }
    }

    public  function getUrlAndEncrypt(&$postContent)
    {
     	$patten="/<a\s[^>]*href=\"(http[^\"]*)\"(.*)>(.*)<\/a>/siU";

        $postContent = preg_replace_callback($patten,
    			array('Logic_Pbdata', "encodeUrl"),
    			$postContent); 
        return $postContent;
    }
    
    public static function encryptUrlInPost(&$arrPostinfos, &$arrPostContents)
    {
    	/*if(empty($arrPostinfos) || empty($arrPostContents))
    	{
    		Bingo_Log::warning("arrPostinfos is null.");
    		return;
    	}*/
     	
        foreach($arrPostinfos as $key => $post)
        {
            $content = $post['content'];
            if(strlen($content) > 0)
            {
            	$ret = self::getUrlAndEncrypt($content);
            	$arrPostinfos[$key]['content'] = $content;
            }    
    	}
    
      foreach($arrPostContents as $post_id=>$comment_info)
        {
        
            if(!isset($comment_info['list']) || !is_array($comment_info['list']))
            {
                continue;
            }

            foreach($comment_info['list'] as $key=>$comment)
            {
               $content = $comment['content'];
               if(strlen($content) > 0)
               {
               	   $ret = self::getUrlAndEncrypt($content);
               	   $arrPostContents[$post_id]['list'][$key]['content'] = $content;
               }
                
            }
            
        }
        
    	return;
    }
    
public static function buildThread($arrPbdata)
{
    $strTitle = isset($arrPbdata['post_infos'][0]['title'])?(string)$arrPbdata['post_infos'][0]['title']:'';
		/**
		 * title�����⴦���п���������ɾ���ˣ�ֱ���ǵ�һ�����ӻ������⡣	
		 * ���ڲ���ɾ����һ¥�����п��ܱ�ɾ��
		 * @var unknown_type
		 */
		$strTitle = Logic_Util::ltrimStr($strTitle, '�ظ���');		
		
		$intIp	= isset($arrPbdata['post_infos'][0]['ip'])?$arrPbdata['post_infos'][0]['ip']:0;
		$strUsername = (string)$arrPbdata['first_post_username'];
		if (empty($strUsername)) {
			$strUsername = (string) $arrPbdata['post_infos'][0]['username'];
		}
        $intTid = intval($arrPbdata['thread_id']);
        /*
         *����ת����Ŀ�Ĺ���
		Bingo_Timer::start('itieba_event');
		$intRepostName = Rpc_ItiebaEvent::getRepostNum($intTid);
		Bingo_Timer::end('itieba_event');
        $intRepostName = intval($intRepostName);
         */

		// �ղ�״̬
		if (Request::$bolLogin) {
			Bingo_Timer::start('thread_store');
            // ����ʧ�ܣ���Ϊû���ղ�
            $threadStoreStatus = Rpc_Tcollect::getCollectInfoEx (Request::$intUid, Request::$intTid);
            $intCollectStatus = intval($threadStoreStatus['kept_type']);
            $intMarkPid = intval($threadStoreStatus['mark_pid']);
			Bingo_Timer::end('thread_store');
		} else {
            $intCollectStatus = 0;
            $intMarkPid = 0;
		}
		
		$arrTopicInfo = Data_Topic::getTopicInfo(Request::$strFname, Request::$intTid, Request::$intFid);
		
		$arrThread = array(
			'id'				=> $intTid,
			'title'				=> $strTitle ,
            'reply_num'			=> intval($arrPbdata['valid_post_num']) + Request::$intSubPostNum,
            'valid_post_num'    => intval($arrPbdata['valid_post_num']),    //��ͨ�ظ�����������¥��¥
            //'repost_num'		=> $intRepostName,
            'repost_num'		=> 0,               //ת����Ŀ
			'user_name'			=> $strUsername,
			//'user_ip'			=> $intIp,
            'collect_status'	=> intval ($intCollectStatus),
            'collect_mark_pid'  => $intMarkPid,
			'thread_type'   	=> $arrPbdata['thread_type'], // �������ͣ���ͨ������������
			'topic'			    => $arrTopicInfo,
        );
        //�����ֻ��¥��ģʽ����ô�ظ���Ŀֻ��¥����������Ŀ��������һ¥��������¥��¥
        if(1 == Request::$intLzOnly){
            $arrThread['reply_num'] = intval($arrPbdata['valid_post_num']);
        }

		// ��������
		/*
		if ($arrThread['thread_type']==self::THREAD_TYPE_TBDISK) {
			$_arrDisk = self::_getTbDisk();
			if ($_arrDisk===false) { // ����ʧ�ܻ��߷���������
				$arrThread['thread_type']==self::THREAD_TYPE_NORMAL;
			} else {
				$_arrPostInfos = Dict::getDict('post_list');
				$_arrPostInfos[0]['idisk'] = $_arrDisk;
				Dict::setDict('post_list',$_arrPostInfos);
			}
        }
		*/
        //�������� by luhua01
        if ($arrThread['thread_type']==self::THREAD_TYPE_TBDISK) {
        	$_arrDisk = self::_getPostFile();
        	if($_arrDisk===false) {
        		$arrThread['thread_type']==self::THREAD_TYPE_NORMAL;
        	} else {
        		Tieba_Stlog::addNode('filepost', 1);
        		$_arrPostInfos = Dict::getDict('post_list');
        		$_arrPostInfos[0]['idisk'] = $_arrDisk;
        		Dict::setDict('post_list',$_arrPostInfos);
        	}
        }
         
        //Brandzone
        $arrThread['is_ad'] = Brandzone::$_isAd;

        if( 1 == $arrThread['is_ad']){
            Dict::setDict('ecom', array('exp' => Brandzone::$_exp));
            Tieba_Stlog::addNode('is_ad', $arrThread['is_ad']);
        }
        //�жϵ��İ�������pm
        $arrPower = Dict::getDict('power');
        if (isset($arrPower['user_roles'])) {
            $userRoles = $arrPower['user_roles'];
            if ($userRoles['is_forum_fourth_manager'] || $userRoles['is_forum_pm']) {
                $tid = Request::$intTid;
                $threadInput = array(
                    'thread_ids'     => array($tid),
                    'need_abstract'  => 0,
                    'forum_id'       => Request::$intFid,
                    'need_photo_pic' => 0,
                );
                $threadData = Tieba_Service::call('post', 'mgetThread', $threadInput);
                if ($threadData['errno'] == 0 && isset($threadData['output']['thread_list'][$tid])) {
                    $arrThread['freq_num'] = $threadData['output']['thread_list'][$tid]['freq_num'];
                }
            }
        }
		Dict::setDict('thread', $arrThread);		
		Tieba_Stlog::addNode('reply_num',$arrThread['reply_num'] );
		return $arrThread;
	}
	public static function buildPage($arrPbdata)
	{
		if (isset($arrPbdata['valid_post_num'])) {
			$intPageno = intval(Bingo_Http_Request::getGet('pn', 1));
			if (Request::$bolIsWap) { // wap �ͻ��˵�����
				$intDefaultPageSize = intval(Conf::get('wap_default_page_size', 70));
				$intMaxPageSize		= intval(Conf::get('wap_max_page_size', 70));
			} else { // ��ͨpc���
				$intDefaultPageSize = intval(Conf::get('default_page_size', 30));
				$intMaxPageSize		= intval(Conf::get('max_page_size', 50));
			}
			$intPageSize = intval(Bingo_Http_Request::getGet('rn', $intDefaultPageSize));
			if ($intPageSize > $intMaxPageSize) $intPageSize = $intMaxPageSize;
			if ($intPageSize <= 0 ) $intPageSize = $intDefaultPageSize;
			//335675392
			if ( (isset($_GET['ct']) && intval($_GET['ct']) == 335675392) || ! empty(Request::$intPid) || !empty(Request::$intSPid) ) {
				//���������⴦��
				$intPageno = intval($arrPbdata['start_no'] / $intPageSize) + 1;
			}
			$intTotalPost = intval($arrPbdata['valid_post_num']);
			if ($intTotalPost > 0) {
                self::$arrPage['cur_page'] = $intPageno;
                self::$arrPage['total_page'] = ceil($intTotalPost / $intPageSize);
			}
		}
        Dict::setDict('page', self::$arrPage);
        Tieba_Stlog::addNode('cur_page',self::$arrPage['cur_page'] );
        Tieba_Stlog::addNode('pn',self::$arrPage['cur_page']);
		return self::$arrPage;
	}
	
	public static function buildPostinfos($arrPbdata, $intPbWidth = 0)
	{
		/**
		 * 
		 * �û����������Ҫ��FE��ͨ�£������������
		 * @var unknown_type
		 */
        $arrPostinfos = array();
        $arrSinglePost = array();
		$arrSignIds = array();
		$arrNoIdUsernames = array();//�������е�post����user_id
		if (Request::$bolLogin) {
			$arrUserMap = array(
				Request::$strUname => Request::$intUid
			);// user_name => user_id
		} else {
			$arrUserMap = array();
		}
		$arrQuotes = array();
		
		if (! empty($arrPbdata['post_infos'])) {
			//�Ƿ���Ҫ����¥��¥С����
			$bolNeedPostContent = false;
			if (isset(Request::$arrSep['has_sub_post']) && (Request::$arrSep['has_sub_post']>0) ) {
				$bolNeedPostContent = true;				
				$arrPostContentReq = array(
					'opid' => Request::$intTid,
					'req'  => array(),
				);
			}
			//build post��Ϣ
			$_bolIsAn = false;
			$_bolBold = false;
            $intOldContentPostId = intval(Conf::get('old_content_post_id', 5256746613));
            $arrExtPdi = Data_ExtPdi::getExtPdis($arrPbdata['post_infos']);

			foreach ($arrPbdata['post_infos'] as $key => $_arrPost) {
				//¥��¥��ش���
				$_intSearchPid = 0;
				$_intSearchCid = 0;
				if ($bolNeedPostContent && $_arrPost['has_comment'] >= 1) {//TODO
					//��¥��¥
					if (Request::$intPid > 0 && intval($_arrPost['post_id']) == Request::$intPid) {
						$_intSearchCid = Request::$intCid;
						$arrPostContentReq['req'][] = array(
							'oid' => $_arrPost['post_id'],
							'now_cid' => $_intSearchCid,
						);
						
					} else {
						$arrPostContentReq['req'][] = array(
							'oid' => $_arrPost['post_id'],
						);
					}
				}
				$_bolIsAn = false;
				$_bolBold = false;
				/*�û���Ϣ��ȡ*/
				if (Tieba_Util::isAnonymousUserName($_arrPost['username'])) {
					//��IP���������û�
					$_bolIsAn = true;
				}else {
					//������
					if (isset($_arrPost['uid']) && ! empty($_arrPost['uid'])) {
						//�����û�ID
						$arrUserMap[(string)($_arrPost['username'])] = intval($_arrPost['uid']);
					} else {
						if (! in_array($_arrPost['username'], $arrNoIdUsernames, true)) {
							$arrNoIdUsernames[] = (string)($_arrPost['username']);
							$arrUserMap[(string)($_arrPost['username'])] = 0;
						}
					}
				}
				if (isset($_arrPost['sign_id']) & !empty($_arrPost['sign_id']) )  {
					$arrSignIds[] = $_arrPost['sign_id'];
				}
				/*post ��Ϣ����*/
				$strOpenid = isset($_arrPost['openid'])?$_arrPost['openid']:'';
				$strOpenType = isset($_arrPost['phone_type'])?$_arrPost['phone_type']:'';
				if (empty($strOpenid) && (!empty($strOpenType))) $strOpenid = 'wap';
				//ͼƬ��Ƶ����
				$strImgUrl = '';
				$arrVideo = array();
				if (isset($_arrPost['image_url']) && !empty($_arrPost['image_url']) &&  'a:0:{}' != $_arrPost['image_url'] ) {
					$strImgUrl = $_arrPost['image_url'];
					$_mixRet = Logic_Util::parseImgUrl($strImgUrl);
					if ($_mixRet === false) {
						//ͼƬ
						
					} else {
						//video					
						$arrVideo	= array(
							'url'	=> $strImgUrl,
							'type'	=> intval($_mixRet),
						);
						$strImgUrl 	= '';
					}
				}
				if ($_arrPost['post_id'] == Request::$intPid) {
					$_bolBold = true;//��ʾ��������Ҫ����
					$_intSearchPid = Request::$intPid;
				}
				//type����
				$_intType = isset($_arrPost['post_refer'])?intval($_arrPost['post_refer']):0;
				if ($_intType > 1) $_intType = 1; 
				//�ظ����ô���
				$_intQuoteId	= 0;
				if (isset($_arrPost['quote']) && isset($_arrPost['quote']['post_id']) ) {
					$_intQuoteId = intval($_arrPost['quote']['post_id']);
					$arrQuotes[$_intQuoteId] = array(
						'id'		=> $_intQuoteId,
						'floor'		=> intval($_arrPost['quote']['post_no']),
						'is_deleted'=> (bool)($_arrPost['quote']['is_deleted']>0),
						'user_name' => (string)($_arrPost['quote']['uname']),
						'content'	=> (string)($_arrPost['quote']['content'])
					);
				}
				//vote��ش���
				$strVoteCrypt = '';
				$_strContent = $_arrPost['content'];
				if (isset($_arrPost['vote_id']) && intval($_arrPost['vote_id']) > 0 ) {
					if (Logic_Util::canViewVote()) {
						//vote id ����
						$_strKey = (string) Conf_Config::get('fcrypt.tieba_key', 
							'Baidu.Vote.2007.04.12', 'tieba');
						$_intVPid		 = intval(Conf_Config::get('fcrypt.vote_pid', 1, 'tieba'));
						if (function_exists('fcrypt_id_2hstr')) {
							$strVoteCrypt = fcrypt_id_2hstr($_strKey, $_intVPid, $_arrPost['vote_id']);
						}
					} else {
						Bingo_Log::warning('no power to view vote');
					}
				} else {
					//���ݵĴ���
					//$_strContent = $_arrPost['content'];
					if ($_arrPost['post_id'] <= $intOldContentPostId) {
						$_strContent = format_text_to_html($_strContent, 1);
					}
                }
                //wap��ƵURL�Ż���صĴ���
                $arr_video = array();
                if(isset($_arrPost['res_url'])){
                    $wap_video = $_arrPost['res_url'];
                    $counter = count($wap_video);
                    for($indicator = 0; $indicator < $counter; $indicator++){
                        $arr_video[$indicator] = $wap_video[$indicator];
                    }
                }
                self::$arrWaterInfo = unserialize($_arrPost['imgWaterInfo']);
                self::$intPostTime = $_arrPost['create_time'];        
                if($intPbWidth){
					$_strContent = self::imageUrlProcess($_strContent);
				}
				//build
				$arrSinglePost = array(
					'id' 		=> $_arrPost['post_id'],
					'title'		=> (string) $_arrPost['title'],
					'floor'		=> $_arrPost['post_no'],
					'time'		=> $_arrPost['create_time'],
					'open_id'	=> $strOpenid,
					'open_type'	=> $strOpenType,
					//'user_ip'	=> isset ($_arrPost['ip']) ? $_arrPost['ip'] : '', ��¶�û���Ϣ��ȥ����ģ��û���õ�ypp��
					'user_name' => $_arrPost['username'],//�п�����IP
					'content'	=> $_strContent,
					'need_bold'	=> $_bolBold,
					'search_pid'=> $_intSearchPid,
					'search_cid'=> $_intSearchCid,
					'type'		=> $_intType,
					'is_anonym'	=> $_bolIsAn,
					'sign_id'	=> $_arrPost['sign_id'],
					'video'		=> $arrVideo,
					'img'		=> $strImgUrl,
					'quote_id'	=> $_intQuoteId,
                    // add by yanbin02 for voice thread 2013.9.4
                    'ptype' => $_arrPost['ptype'],
                    'vote_crypt'=> $strVoteCrypt,
                    'arr_video' => $arr_video,          //wap����URL���������
                    'lbs_info'  => $_arrPost['lbs_info'], //wap���ߵ�����Ϣ         
                );

                //pb��չ����
                $srch_key = Data_ExtPdi::REDIS_KEY_PREFIX . $_arrPost['post_id'];
                if( isset($arrExtPdi[$srch_key]) ){
                    foreach( $arrExtPdi[$srch_key] as $value ){
                        $fkey = $value['field'];
                        $fvalue = Data_ExtPdi::_dealFieldInPdi($fkey,$value['value']);
                        $arrSinglePost[$fkey] = $fvalue;
                    }
                }
                $arrPostinfos[] = $arrSinglePost;
			}
			//end foreach
			if ($bolNeedPostContent) {
				$arrPostContents = self::_getPostContent($arrPostContentReq);
			}
        }

        $threadCreater = $arrPbdata['first_post_username'];
        if(!isset($arrUserMap[$threadCreater])){
            $arrNoIdUsernames[] = $threadCreater;
        }

        //��ȡ�û���ϸ��Ϣ
		//username -> user_id , passgate  user_list
		require_once 'Rpc/Passgate.php';
		if (! empty($arrNoIdUsernames)) {
			Bingo_Timer::start('passgate_uid');
			$arrRet = Rpc_Passgate::getUidsByUnames($arrNoIdUsernames);
			Bingo_Timer::end('passgate_uid');
			if (! empty($arrRet)) {
				foreach ($arrRet as $_strUname => $_arrUinfo) {
					$arrUserMap[$_strUname] = intval($_arrUinfo['userid']);
				}
			}
        }
        //2012-09-04 luhua01
        $arrPostUserMap = array();
        $arrPostUserMap = $arrUserMap;
        //end
		if (! empty($arrUserMap)) {
			//��ȡ�û�����
			Logic_User::buildUserList($arrUserMap, self::$_arrSubUserMap);	
			if (! empty(self::$_arrSubUserMap)) {
				foreach (self::$_arrSubUserMap as $_strUn => $intUid ) {
					$arrUserMap[$_strUn] = $intUid;
				}
            }
        }

        $_arrForum = Dict::getDict('forum');
        $_arrForum['shield_post'] = 0;
        //add by zhongchi begin 2013-05-13 ���˱����ص��û�������
        if(!empty(Request::$intUid) && 'enabled' == Conf::get('HideUserAndPost', 'disabled'))
        {
            $_arrForum['shield_post'] = 1;
            self::filterPostAndComment($arrPostinfos, $arrPostContents, $arrPbdata['thread_id'], $arrUserMap);
        }
        Dict::setDict('forum', $_arrForum);
        //add by zhongchi end 2013-05-13

        //get the user list whose posts have been hide
        $arrUserList = array();
        foreach($arrUserMap as $_uid)
        {
            $arrUserList[] = $_uid;
        }
        Bingo_Timer::start('user_mask');
        $arrUserHide = Data_Userhide::getUserHideList($arrUserList);
        Bingo_Timer::end('user_mask');
        $arrOutput = self::threadHide($arrUserHide, $arrPbdata, $arrPostinfos, $arrPostContents, $arrUserMap);
        $arrPostinfos = $arrOutput['post_info'];
        $arrPostContents = $arrOutput['sub_post_info'];
        self::setUserHideInfo($arrUserHide);

        //���������¥�����ܸ��ǹ��������
        if(1 === intval(Request::$intPageNo)){
            if(!empty(Brandzone :: $_show_mt)){
                $arrPostinfos[0]['content'] = Brandzone :: $_show_mt;
            }
        }

		//sign
		$arrSigns = array();
		if (! empty($arrSignIds)) {
			$arrSigns = self::_getSigns($arrSignIds);
        }

        if(self::$fromPc == 1)
        {
        	Bingo_Timer::start('encry_time');
        	self::encryptUrlInPost($arrPostinfos, $arrPostContents);
        	Bingo_Timer::end('encry_time');
        }	
      
        //���������ʱ��
        $pids = array();
        foreach($arrPostinfos as $item){
            if($item['ptype'] == 1){
                $pid = array(
                    'thread_id' => $arrPbdata['thread_id'],
                    'post_id' => $item['id'],
                );
                $pids[] = $pid;
            }
        }
		foreach($arrPostContents as $key => $value){
			foreach($value['list'] as $item){
                if($item['ptype'] == 1){
				    $pid = array(
					    'thread_id' => $arrPbdata['thread_id'],
					    'post_id' => intval($item['id']),
				    );
				    $pids[] = $pid;
                }
			}
		}
        if(!empty($pids)){
        $arrInput = array('pids' => $pids);
        $res = Tieba_Service::call('voice', 'getThreadVoiceInfosByPids', $arrInput);
        if(!isset($res['errno']) || $res['errno'] != Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('get thread voice infos by pids failure!'.serialize($arrInput));
        }
        $arrVoiceInfo = array();
        if($res['errno'] == 0){
            $res = $res['ret']['postVoiceList'];
            foreach($res as $item){
                $arrVoiceInfo[$item['post_id']] = $item;
            }
        }
        foreach($arrPostinfos as &$item){
            $item['during_time'] = $arrVoiceInfo[$item['id']]['during_time'];
            unset($item);
        }
		
		foreach($arrPostContents as $key => &$value){
			foreach($value['list'] as &$item){
				$item['during_time'] = $arrVoiceInfo[$item['id']]['during_time'];
				unset($item);
			}
            unset($value);
		}
        }
        //����׷������׷����merge����
        if(1 === intval(Request::$intPageNo)){
            foreach ($arrPostContents[$arrPostinfos[0]['id']]['list'] as $arrContent) {
                if ($arrPostinfos[0]['user_name'] == $arrContent['user_name']) {
                    $arrPostinfos[0]['content'] .= "</br>{$arrContent['content']}";
                }
            }
            unset($arrPostContents[$arrPostinfos[0]['id']]);
        }
		Dict::setDict('quote_list', $arrQuotes);
		Dict::setDict('sign_list', $arrSigns);
		Dict::setDict('user_map', $arrUserMap);
        Dict::setDict('post_list', $arrPostinfos);
        Dict::setDict('post_user_map', $arrPostUserMap);
		Dict::setDict('sub_post_info_list', $arrPostContents);
		return $arrPostinfos;
	}

    //add by zhongchi begin 2013-05-13
    //���˻�����¥��¥�б����ص�����
    private static function filterPostAndComment(&$arrPostinfos, &$arrPostContents, $thread_id, $arrUserMap)
    {
        if(empty($thread_id) || empty($arrPostinfos))
        {
            return ;
        }
        $uids = array();
        $unames = array();
        $postids = array();
        //��ȡ��ҳ���е�uid��postid
        foreach($arrPostinfos as $post)
        {
            $unames[] = $post['user_name'];
            $uids[$post['user_name']] = $arrUserMap[$post['user_name']];
            //$uids[] = $post['uid'];
            $postids[] = $post['id'];
        }
        //$uids = array_unique($uids);
        //$ui= array('user_name'=>$unames);
        //$uo= Tieba_Service::Call('user', 'getUidByUnames', $ui); 
        //if(false === $uo|| 0 !== $uo['errno']
        // || !isset($uo['output']['uids'][0]))
        //{       
        //    return ;
        //}       
        //foreach($uo['output']['uids'] as $user)
        //{
        //    $uids[$user['user_name']] = $user['user_id'];
        //}
        foreach($arrPostContents as $comment_info)
        {
            if(!isset($comment_info['list']) || !is_array($comment_info['list']))
            {
                continue;
            }
            foreach($comment_info['list'] as $comment)
            {
                $uids[$comment['user_name']] = $comment['user_id'];
                $postids[] = $comment['id'];
            }
        }
        //��ȡ�����ص��û�������
        $input = array(
                'req'=>array(
                    'type'=>'UP',
                    'userId'=>Request::$intUid,
                    'subjectId'=>$thread_id,
                    'pUserId'=>$uids,
                    'postId'=>$postids,
                    ),
                'conf'=>array(
                    'instanceName'=>'Redis_spam_umask_forum',
                    'redis_uname'=>'spam',
                    'redis_tk'=>'spam',
                    ),
                );
        Bingo_Log::debug("input of antiUserMaskQuery : ". print_r($input, true));
        Bingo_Timer::start('umask_filterPostAndComment');
        $output = Ueg_Mask_Umask::antiUserMaskQuery($input);
        //$output = Tieba_Service::Call('umask','antiUserMaskQuery',$input);
        Bingo_Timer::end('umask_filterPostAndComment');
        //if(false === $output || 0 !== $output['errno'])
        if(0 !== $output['errno'])
        {
            Bingo_Log::warning("fail to call antiUserMaskQuery : ". print_r($output, true));
            return ;
        }
        Bingo_Log::debug("output of antiUserMaskQuery : ". print_r($output, true));
        $maskuids = $output['res']['maskUserId'];
        $maskpids = $output['res']['maskPostId'];
        //��������
        foreach($arrPostinfos as $key=>$post)
        {
            $uid = $uids[$post['user_name']];
            if(in_array($uid, $maskuids, true)
             ||in_array(strval($post['id']), $maskpids, true))
            {
                unset($arrPostinfos[$key]);
            }
        }
        //����¥��¥�ظ�
        Bingo_Log::debug("arrPostinfos after filtering : ". print_r($arrPostinfos, true));
        foreach($arrPostContents as $post_id=>$comment_info)
        {
            if(in_array(intval($post_id), $maskpids, true))
            {
                unset($arrPostContents[$post_id]);
                continue;
            }
            if(!isset($comment_info['list']) || !is_array($comment_info['list']))
            {
                continue;
            }
            foreach($comment_info['list'] as $key=>$comment)
            {
                if(in_array($comment['id'], $maskpids, true)
                 || in_array($comment['user_id'], $maskuids, true))
                {
                    unset($arrPostContents[$post_id]['list'][$key]);
                }
            }
            if(empty($arrPostContents[$post_id]['list']) && $arrPostContents['total_num']<=10)
            {
                unset($arrPostContents[$post_id]);
            }
        }
        Bingo_Log::debug("arrPostContents after filtering : ". print_r($arrPostContents, true));
        return ;
    }

    //����¥��¥�б����ص�����
    public static function filterComment($comments, $thread_id)
    {
        if(empty($thread_id) || empty($comments))
        {
            return $comments;
        }
        $uids = array();
        $commentids = array();
        //��ȡ��ҳ���е�uid��postid
        foreach($comments as $comment)
        {
            $uids[] = $comment['userid'];
            $commentids[] = $comment['cid'];
        }
        $uids = array_unique($uids);
        //��ȡ�����ص��û�������
        $input = array(
                'req'=>array(
                    'type'=>'UP',
                    'userId'=>Request::$intUid,
                    'subjectId'=>$thread_id,
                    'pUserId'=>$uids,
                    'postId'=>$commentids,
                    ),
                'conf'=>array(
                    'instanceName'=>'Redis_spam_umask_forum',
                    'redis_uname'=>'spam',
                    'redis_tk'=>'spam',
                    ),
                );
        Bingo_Log::debug("input of antiUserMaskQuery : ". print_r($input, true));
        Bingo_Timer::start('umask_filtercomment');
        $output = Ueg_Mask_Umask::antiUserMaskQuery($input);
        //$output = Tieba_Service::Call('umask','antiUserMaskQuery',$input);
        Bingo_Timer::end('umask_filtercomment');
        //if(false === $output || 0 !== $output['errno'])
        if(0 !== $output['errno'])
        {
            Bingo_Log::warning("fail to call antiUserMaskQuery : ". print_r($output, true));
            return ;
        }
        Bingo_Log::debug("output of antiUserMaskQuery : ". print_r($output, true));
        $maskuids = $output['res']['maskUserId'];
        $maskpids = $output['res']['maskPostId'];
        //����¥��¥�ظ�
        foreach($comments as $key=>$comment)
        {
            if(in_array(intval($comment['userid']), $maskuids, true)
             ||in_array($comment['cid'], $maskpids, true))
            {
                unset($comments[$key]);
            }
        }
        Bingo_Log::debug("comments after filtering : ". print_r($comments, true));
        return $comments;
    }

    //�ж������Ƿ����أ��������򷵻�true�����򷵻�false
    public static function isThreadHide($thread)
    {
        if('enabled' != Conf::get('HideUserAndPost', 'disabled') || empty($thread))
        {
            return false;
        }
        if($thread['first_post_username'] != $thread['post_infos'][0]['username'])
        {
            return false;
        }
        //��ȡ���ⴴ���˵��û�id
        $creater_uid = $thread['post_infos'][0]['uid'];
        //$uinput = array('user_name'=>array($thread['first_post_username']));
        //$uoutput = Tieba_Service::Call('user', 'getUidByUnames', $uinput);
        //if(false === $uoutput || 0 !== $uoutput['errno']
        // || !isset($uoutput['output']['uids'][0]['user_id']))
        //{
        //    return false;
        //}
        //$creater_uid = $uoutput['output']['uids'][0]['user_id'];
        $input = array(
                'req'=>array(
                    'type'=>'T',
                    'userId'=>Request::$intUid,
                    'forumId'=>$thread['forum_id'],
                    'subjectId'=>array($thread['thread_id']),
                    'pUserId'=>array($creater_uid),
                    ),
                'conf'=>array(
                    'instanceName'=>'Redis_spam_umask_forum',
                    'redis_uname'=>'spam',
                    'redis_tk'=>'spam',
                    ),
                );
        Bingo_Log::debug("input of antiUserMaskQuery : ". print_r($input, true));
        Bingo_Timer::start('umask_isthreadhide');
        $output = Ueg_Mask_Umask::antiUserMaskQuery($input);
        //$output = Tieba_Service::Call('umask','antiUserMaskQuery',$input);
        Bingo_Timer::end('umask_isthreadhide');
        //if(false === $output || 0 !== $output['errno'])
        if(0 !== $output['errno'])
        {
            Bingo_Log::warning("fail to call umask->antiUserMaskQuery : ". print_r($output, true));
            return false;
        }
        Bingo_Log::debug("output of antiUserMaskQuery : ". print_r($output, true));
        if(in_array(strval($thread['thread_id']), $output['res']['maskSubjectId'], true)
         ||in_array($creater_uid, $output['res']['maskUserId'], true))
        {
            return true;
        }
        return false;
    }
    //add by zhongchi end 2013-05-13

	/**
	 * ��ȡ¥��¥�������
	 * @param $arrReq
	 */
	protected static function _getPostContent($arrReq)
	{
		$arrPostContents = array();
		$arrRetUids = array();
		$arrRet = Rpc_PostComment::getPbComment($arrReq);
		if ($arrRet && ! empty($arrRet) && isset($arrRet['total'])) {
			Request::$intSubPostNum = intval($arrRet['total']);
			if (! empty($arrRet['res'])) {
				foreach ($arrRet['res'] as $_arrItem) {
					if (isset($_arrItem['oid']) && isset($_arrItem['total']) &&$_arrItem['total'] > 0 ) {
						$_arrTmp = array();
						$_arrTmp['total_num'] = intval($_arrItem['total']);
						$_arrTmp['cur_page'] = ceil($_arrItem['offset']/10)+1;
						$_arrTmp['total_page'] = ceil($_arrTmp['total_num']/10);
						$_arrTmp['list'] = array();
						if (! empty($_arrItem['comments'])) {
							foreach ($_arrItem['comments'] as $_arrC) {
								$_strTmpUname = (string)trim($_arrC['username']);
								$_intTmpUid = intval($_arrC['userid']);
								$_arrTmp['list'][] = array(
									'id' => $_arrC['cid'],
									'user_name' => $_strTmpUname,
									'user_id'	=> $_intTmpUid,
									'content'	=> $_arrC['content'],
									'time'		=> $_arrC['posttime'],
                                    // add by yanbin02 for voice thread 2013.9.4
                                    'ptype' => $_arrC['ptype'],
								);
								self::$_arrSubUserMap[$_strTmpUname] = $_intTmpUid;
							}
						}
						
						//
						$arrPostContents[intval($_arrItem['oid'])] = $_arrTmp;
					}
				}
			}
		}
		return $arrPostContents;
	}
	
	protected static function _getSigns($arrSignIds)
	{
        $arrSigns = array();
        Bingo_Timer::start('fucenter');
        $arrRet = Tieba_Service::call('user', 'mgetSignaturesBySids', array('sids'=>$arrSignIds));
        Bingo_Timer::end('fucenter');
        if ($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            return array();
        } else {
            $arrRet = $arrRet['signatures'];
            if (!empty($arrRet)) {
                foreach ($arrRet as $sid=>$_arrSign) {
                    $arrSigns[$sid] = array(
                        'url'    => (string) $_arrSign['surl'],
                        'height' => (int) $_arrSign['simg_height'],
                        'width' => (int) $_arrSign['simg_width'],
                    );
                }
            }
        }
		return $arrSigns;
	}
	/**
	 * ��������������Ϣ
	 **/
	protected static function _getTbDisk () {
		$arrDiskInfo = Rpc_Tbdisk::getDiskThread(Request::$intFid, Request::$intTid);
		if ($arrDiskInfo==false) { // ����ʧ��
			return false;
		}
		if (!isset ($arrDiskInfo['output']['is_valid']) || $arrDiskInfo['output']['is_valid']!=1) { // ��Ч
			return false;
		}
		$strClassifyName = $arrDiskInfo['output']['classify_name'];
		$intClassifyId = $arrDiskInfo['output']['classify_id'];
		$intUploadTime = $arrDiskInfo['output']['create_time'];
		// ����ʱ�䣬-1 ��ʾ������Ч����СʱΪ��λ��
		if ($intClassifyId<=0) { // δ�����
			$arrTmp = Rpc_Tbdisk::getForumConfig(Request::$intFid);
			if ($arrTmp == false || !isset ($arrTmp['expire_time'])) { // ����ʧ����Ϊ������Ч
				$intTimeLeft = -1;
			} else {
				$intTimeLeft = $intUploadTime + $arrTmp['expire_time'] - time();
				if ($intTimeLeft<=0) {
					$intTimeLeft = 0;
				} else {
					$intTimeLeft = intval ( ( $intTimeLeft + 3599) / 3600 );
				}
			}
		} else {
			$intTimeLeft = -1;
		}
		$arrFiles = array();
		if (!empty ($arrDiskInfo['output']['files'])) {
			foreach ($arrDiskInfo['output']['files'] as $arrFileInfo) {
				$arrFiles[] = array (
					'name'		=> $arrFileInfo['filename'],
					'size'		=> intval ($arrFileInfo['size']),
					'downloads'	=> $arrFileInfo['download_num'],
					'type'		=> $arrFileInfo['filetype'],
					'id'		=> $arrFileInfo['file_id'],
				);
			}
		}
		$arrOut = array (
			'classify'		=> $strClassifyName,
			'classify_id'	=> $intClassifyId,
			'upload_date'	=> $intUploadTime,
			'timeleft'		=> $intTimeLeft,
			'file'			=> $arrFiles,
		);
		return $arrOut;
    }

    //add by xiayi
    private static function threadHide($arrUserHide, $arrPbdata, $arrPostinfos, $arrPostContents, $arrUserMap)
    {
        //the number of posts have been masked
        $countMaskPost = 0;
        //the number of sub-posts have been masked
        $countMaskSubPost = 0;

        // pbҳ����������ʾ����������������
        $maskPostLimit = 15;         
        // ¥��¥����������ʾ��������¥��¥��������,Ĭ��Ϊ5��¥��¥�ܲ���С��10��ʱ��Ϊ1
        //$maskSubPostLimit = 5; 
        
        // pbҳ����������ʾ������������־���� by haoyunfeng 2013.10.11 begin
        $maskPostNetLogLimit = 24;
        
        // ��ͨ��������Ϣ
        $maskPostInfos = array();
        /////////////////////////////////////////////////////////
        
        
        if(!empty($arrUserHide)){
            $newArrPostinfos = array();
            $newArrPostContents = array();
            $threadCreater = $arrPbdata['first_post_username'];
            $threadCreaterId = $arrUserMap[$threadCreater];
            $firstUid = intval($arrPbdata['post_infos'][0]['uid']);

            if( !empty($threadCreaterId) &&  isset($arrUserHide[$threadCreaterId])){
                Bingo_Page::setTpl('none.php');
                Request :: $hideThread = true;
                return $arrPostinfos;
            }
            //�����ͷ�������⴦��
            if( 0 === intval($arrPbdata['start_no']) && !empty($firstUid) && isset($arrUserHide[$firstUid]) ){
                Bingo_Page::setTpl('none.php');
                Request :: $hideThread = true;
                return $arrPostinfos;
            }

            foreach($arrPostinfos as $_post)
            {
                $username = $_post['user_name'];
                $userid   = $arrUserMap[$username];
                if( !empty($userid) && isset($arrUserHide[$userid])){
                    $tid = $_post['id'];
                    unset($arrPostContents[$tid]);
                    $countMaskPost++;
                    $maskPostInfos[] = $_post;
                }else{
                    $newArrPostinfos[] = $_post;
                }
            }

            foreach($arrPostContents as $key => $value)
            {
                $subPost = $value;
                $subPostList = array();
                foreach($value['list'] as $number => $_subpost)
                {
                    $userid = $_subpost['user_id'];
                    if( !empty($userid) && isset($arrUserHide[$userid])){
                        //$subPost['total_num']--;
                        $countMaskSubPost++;
                    }else{
                        $subPostList[] = $_subpost;
                    }
                }
                $subPost['list'] = $subPostList;
                /*
                //¥��¥����С�ڵ���15��ʱ�����޸ĳ�1
                if ($subPost['total_num'] <= 15) {
                    $maskSubPostLimit = 1;
                }
                if ($subPost['total_num'] > 5 && $countMaskSubPost >= $maskSubPostLimit){
                    $subPost['exist_forbidden_tip'] = 1;
                }*/
                if(!empty($subPostList)){
                    $newArrPostContents[$key] = $subPost;
                }
            }
            $arrPostinfos = $newArrPostinfos;
            $arrPostContents = $newArrPostContents;
        }
        //add by lizherui 2013-01-04
        if ($countMaskPost >= $maskPostLimit) {
            self::$arrPage['exist_forbidden_tip'] = 1;
            Dict::setDict('page', self::$arrPage);
        }

        //add to log
        $strLogUeg = "|";
        foreach($arrPostinfos as $key => $value){
            $strLogUeg = $strLogUeg . $value['id'] . "_";
        }
        $strLogUeg = substr($strLogUeg, 0, strlen($strLogUeg)-1)."|";
        Tieba_Stlog::addNode('post_id_reply_num', $strLogUeg);

        // add by haoyunfeng 2013.10.11 begin
        // ��������Ϣ������־
        $strMaskLogUeg = "|"; 
        if ($countMaskPost >= $maskPostNetLogLimit)
        {
        	foreach ($maskPostInfos as $key => $value)
        	{
        		$strMaskLogUeg = $strMaskLogUeg . $value['id'] . "_";
        	}
        	
        	$strMaskLogUeg = substr($strMaskLogUeg, 0, strlen($strMaskLogUeg)-1)."|";
        	Tieba_Stlog::addNode('post_mask_id', $strMaskLogUeg);
        }
        // add by haoyunfeng 2013.10.11 end
        //add to stlog
        Tieba_Stlog::addNode('post_mask_num', intval ($countMaskPost));
        Tieba_Stlog::addNode('subp_mask_num', intval ($countMaskSubPost));

        $arrOutput = array(
            'post_info' => $arrPostinfos,
            'sub_post_info' => $arrPostContents,
        );
        return $arrOutput;
    }

    private static function setUserHideInfo($arrUserHide)
    {
        $arrUids = Dict::getDict('user_list');
        $arrLogin = Dict::getDict('logined_user');

        foreach($arrUids as $_uid => $_uinfo)
        {
            if(isset($arrUserHide[$_uid])){
                $arrUids[$_uid]['userhide'] = 1;
            }else{
                $arrUids[$_uid]['userhide'] = 0;
            }
        }

        if( isset($arrUserHide[$arrLogin['id']]) ){
            $arrLogin['userhide'] = 1;
        }else{
            $arrLogin['userhide'] = 0;
        }
        //black pop
        $need_black_pop = Rpc_SpringDefense::needPop(Request::$intFid, Request::$intUid );
        $black_pop_level = Rpc_SpringDefense::getBlackPopLevel();

        $arrLogin['need_black_pop'] = $need_black_pop;
        $arrLogin['black_pop_level'] = $black_pop_level;

        Tieba_Stlog::addNode('black_pop', intval($need_black_pop));
        Tieba_Stlog::addNode('pop_level', $black_pop_level);
        //black pop end

        Dict::setDict('user_list', $arrUids);
        Dict::setDict('logined_user',$arrLogin);
    }


	/**
	 * ������������Ϣ
	 * by  luhua01
	 **/
	protected static function _getPostFile () {
		//��ȡ�ļ�������Ϣ
		$arrFileInfo = Tbapi_Filepost_Midl_Filepost::getFileInfo(Request::$intTid, Request::$intFid);
		if ($arrFileInfo == false || (isset($arrFileInfo['no']) && intval($arrFileInfo['no']) <= 0) || !isset($arrFileInfo['files_info'])) {
			return false;
		}
		//��֤�ļ���Ч��
		foreach($arrFileInfo['files_info'] as $intIndex => $arrEachFile) {
			$intShareId = intval($arrEachFile['share_id']);
			$intUk = intval($arrEachFile['uk']);
			$strPath =$arrEachFile['file_path'];
            $fid = $arrEachFile['fs_id'];
			$strPath = Bingo_Encode::convert($strPath, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
			//$arrRet = Tbapi_Pan_Midl_Pan::getFileDlink($intShareId, $intUk, $strPath, null, 'get');
			$arrRet = Tbapi_Pan_Midl_Pan::getFileDlink($intShareId, $intUk, $fid);
			if($arrRet == false || $arrRet['errno'] != 0) {
				$arrFileInfo['files_info'][$intIndex]['status'] = -1;
				$arrFileInfo['files_info'][$intIndex]['url'] = '';
			}
			else {
				$arrFileInfo['files_info'][$intIndex]['status'] = 0;
				//$arrFileInfo['files_info'][$intIndex]['url'] = $arrRet['dlink'];
				$arrFileInfo['files_info'][$intIndex]['url'] = "http://pan.baidu.com/share/link?uk=$intUk&shareid=$intShareId&fid=$fid";
			}
		}
	
		$arrOut = array (
				'file'	=> $arrFileInfo['files_info'],
		);
		return $arrOut;
	}
	
	public static function imageUrlProcess($strContent){
		$strMatchStr = Conf::get('pb_image_match_url', "");
		if(empty($strMatchStr))
			return $strContent;
		$strContent = preg_replace_callback($strMatchStr, array('self', 'imageReplace'), $strContent); 
		return $strContent;
	}
	
	public static function imageReplace($arrMatch){
		if(!isset($arrMatch[1]) || empty($arrMatch[1])){
				return $arrMatch[0];
		}
		
		$strPic = $arrMatch[1];
		
		$objBdPic = new Bd_Pic_UrlCrypt();
		
		$intPicId = $objBdPic->decode_pic_url_crypt($strPic);
		
		if($intPicId === false){
			return $arrMatch[0];
		}
		
		$intPbWidth = intval(Conf::get('pb_width_param', "580"));
		$strWaterInfo = "";
		if(!empty(self::$arrWaterInfo) && isset(self::$arrWaterInfo[$intPicId])){
			$strWaterInfo = self::getWaterInfo(self::$arrWaterInfo[$intPicId], $intPbWidth);		
		}
		
		$strPic = "w=".$intPbWidth;
		if(!empty($strWaterInfo)){
			$strPic = $strPic.";".$strWaterInfo; 	
		}
		
		$arrInput = array(
							'pic_id' 				=> $intPicId,
							'product_name'	=> Conf::get('image_product_name', "forum"),
							'pic_spec'			=> $strPic,
							'domain'				=> Conf::get('pb_image_demain', "cq01-testing-arch-iknow02.vm.baidu.com:8082"),
							'foreign_key'		=> Request::$intFid,
						);
						
		$arrOutput = $objBdPic->BatPid2Url(array($arrInput));
		if($arrOutput && $arrOutput['err_no'] == 0 && !empty($arrOutput['resps'][0])){
			return 	$arrOutput['resps'][0];
		}
		
		return $arrMatch[0];
	}
	
	public static function getWaterInfo($arrInput, $intPbWidth){
		/*if(!isset($arrInput['width']) || empty($arrInput['width'])
			|| !isset($arrInput['height']) || empty($arrInput['height'])
			|| !isset($arrInput['forum']) || empty($arrInput['forum'])){
			return "";		
		}
		
		$ret = self::waterurl($arrInput['width'], $arrInput['height'], $intPicId, $intPbWidth, htmlspecialchars_decode($arrInput['forum'], ENT_QUOTES));
		if($ret === false){
				return "";
		}
		*/
		$arrInput['intStaticWidth']	= $intPbWidth;
		$arrInput['waterinfo'] = htmlspecialchars_decode($arrInput['forum'], ENT_QUOTES);
		$arrInput['time'] = self::$intPostTime;
    $strWaterInfo = image_waterUrl::createWaterInfo($arrInput);                                                                                                   
		
		return $strWaterInfo;	
	}
	/*
	 public static function waterurl($inputParam_width,
                                    $inputParam_height,
                                    $inputParam_picid,
                                    $inputParam_Pbwidth,
                                    $waterFname) {
        $water_limit_width = 300;
        $water_limit_heigth = 150;
        
        if($inputParam_width < $water_limit_width || 
           $inputParam_height < $water_limit_heigth ){
        	 Bingo_Log::debug('picture is too small or format is gif!');
             return false;                            	
        }
        
        if($inputParam_width > $inputParam_Pbwidth){
						$inputParam_height = intval($inputParam_height * ($inputParam_Pbwidth/$inputParam_width));        	
        		$inputParam_width = $inputParam_Pbwidth;        		
        }        
        
        $water_pic_left = 10; 
        $water_pic_height = 27;                                                                            
        $water_pic_width = 85;                                                                             
        $tugao = $inputParam_height-($water_pic_left + $water_pic_height);                                 
        $zizuo = $water_pic_left + $water_pic_width + 9;                                                   
        $zisize = 26;                                                                                      
        $zigao =$inputParam_height - $zisize;                                                              
                                                                                                                                                                
        $strWaterInfo = "cp=tieba,$water_pic_left,$tugao;ap=$waterFname,$zizuo,$zigao";
        return $strWaterInfo;
	}*/	
}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
