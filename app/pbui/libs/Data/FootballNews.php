<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2012.07
 * @version  1.0
**/



class Data_FootballNews {

	const LECAI_CLIENT_API_KEY ='yRlmiCbrv56CcjfMwS21DkqP';//lecai
	const LECAI_CLIENT_SECRET_KEY = 'AID6cZA1E021IP2EmRlqrvp9olBIljBj';
	const DES_ACCESS_TOKEN_KEY = 'lecai_tieba_2014';
	const CACHE_KEY = 'footballnews';
	const CACHE_EXPIRE = 60;
	
    protected static $_objCache = null;
    
    protected static function _getCache() {
    	if (is_null(self::$_objCache)) {
    		self::$_objCache = new Bingo_Cache_Memcached("forum_relay_in_pb");
    	}
    	return self::$_objCache;
    }
    
    public static function getAccessToken($arrInput){    	
    	if(!$arrInput['special']['is_match_news']) {  //���
    		return false;
    	}
    	if($arrInput['thread_type'] != 20){
    		return false;
    	}
    	$cache = self::_getCache ();
    	if ($cache == false) {
    		Bingo_Log::warning ( 'get cache fail' );
    		return false;
    	} else {
    		$strKey = self::CACHE_KEY . $arrInput['user_id'];
    		$retCache = $cache->get ( $strKey );
    		if ($retCache) {
    			$arrInfo = unserialize ( $retCache );
    			Dict::setDict ( 'Business', $arrInfo);
    			return true;
    		} else {
    			$strOut = self::_getAccessToken();
    			$arrOut = json_decode($strOut,true);
    			$access = $arrOut['access_token'];
    			if($access == false){
    				return false;
    			}
    			$enAccess = self::do_mencrypt($access,self::DES_ACCESS_TOKEN_KEY);
    			$arrInfo = array(
    					'football_news' => array(
    							'access_token' => $enAccess,
    					)
    			);
    			$ret = $cache->add ( $strKey, serialize ( $arrInfo ), self::CACHE_EXPIRE );
    			Dict::setDict ( 'Business', $arrInfo);
    		}
    	}

    }
	
    private static function _getAccessToken(){
    	  $strHost = 'http://***********'; //bvs yf/jx     
        if(ral_get_idc() == 'tc') {
        	$strHost = 'http://***********';
        }      
        $arrServer = array(
            array(
                'host' => $strHost,
                'port' => 8000,
            ),
        );
        $server = new Bd_Rpc_Http('access_token',$arrServer);
        $server->setOptions(array (
            'connect_timeout' =>  1000,
            'read_timeout'    => 2000,
        ));
        Bingo_Timer::start('access_token');
        $arrPostData = array(
        	'grant_type' => 'bduss',
        	'client_id' => self::LECAI_CLIENT_API_KEY,
        	'client_secret' => self::LECAI_CLIENT_SECRET_KEY,
        	'bduss' => Tieba_Session_Socket::getBDUSS(),
        );
        $strOut = $server->call(array(        					 
        					'url'=>'/oauth/2.0/token',
        					'method' => 'post',
        					'post_vars' => $arrPostData,
                            'curl_opts'=>array (
                                CURLOPT_NOSIGNAL => 1,
                             )), 
        					 -1);    
        Bingo_Timer::end('access_token');
        if ($server->getErrno() !== 0){
            Bingo_Log::warning('get openapi url is error: /oauth/2.0/token:'.json_encode($arrPostData));
    	}
    	return $strOut;
    	
    }
    
	private static function do_mencrypt($input, $key)
    {
        $input = str_replace("\n", "", $input);
        $input = str_replace("\t", "", $input);
        $input = str_replace("\r", "", $input);
        $key = substr(md5($key), 0, 24);
        $td = mcrypt_module_open('tripledes', '', 'ecb', '');
        $iv = mcrypt_create_iv(mcrypt_enc_get_iv_size($td), MCRYPT_RAND);
        mcrypt_generic_init($td, $key, $iv);
        $encrypted_data = mcrypt_generic($td, $input);
        mcrypt_generic_deinit($td);
        mcrypt_module_close($td);
        return trim(chop(base64_encode($encrypted_data)));
    }
};

?>
