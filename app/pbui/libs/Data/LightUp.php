<?php
/***************************************************************************
 *
 * Copyright (c) 2011 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/
/**
 * @file LightUp.php
 * <AUTHOR>
 * @date	 
 * @brief 
 *
 **/
class Data_LightUp {

	const CACHE_SERVER_NAME = 'notitle_frs';

	const CACHE_KEY = 'pblightup';
	const CACHE_KEY_CHALOU = 'pblightup_chalou';
	const CACHE_KEY_LOGINUSER = 'pblightup_loginuser';
	
	const CACHE_EXPIRE = 3600;
	const CACHE_EXPIRE_CHALOU = 10;
	const CACHE_EXPIRE_LOGINUSER = 1800;
	
	protected static $_objCache = null;

	protected static function _call () {}


	protected static function _getCache () {
		if (null === self::$_objCache) {
			self::$_objCache = new Util_Cache(self::CACHE_SERVER_NAME);
		}
		return self::$_objCache;
	}
	
	/**
	 * ��½�û�����������
	 * Enter description here ...
	 */
	public static function getLoginUserLighted() {
		$intFid = Request::$intFid;
		$intUid = Request::$intUid;
		$arrLoginUser  = Dict::getDict('logined_user');
		//var_dump($arrLoginUser);
		$arrServiceInput = array(
			'fid' => $intFid,
			'uids' => array($intUid),
		);
		$cache = self::_getCache();
		
		if ($cache == false) {
			$ret = Tieba_Service::call('light', 'getLightedCountByUid', $arrServiceInput);
			if ($ret === false || $ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				Bingo_Log::warning('get getLightedCountByUid failed! ['. print_r($arrServiceInput, true).']');
				return false;
			}
			$arrLoginUser['light_up_num'] = intval($ret['data'][0]['lighted_count']);
			Dict::setDict('logined_user',$arrLoginUser);
			return true;
		} else {
			$strKey = self::CACHE_KEY_LOGINUSER . '_' . $intFid . '_' . $intUid;
			$retCache = $cache->get($strKey);
			if ($retCache) {
				$arrInfo = unserialize($retCache);
				Dict::setDict('logined_user',$arrInfo);
				return true;
			}else {
				$ret = Tieba_Service::call('light', 'getLightedCountByUid', $arrServiceInput);
				if ($ret === false || $ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
					Bingo_Log::warning('get getLightedCountByUid failed! ['. print_r($arrServiceInput, true).']');
					return false;
				}
				$arrLoginUser['light_up_num'] = intval($ret['data'][0]['lighted_count']);
				$cache->set($strKey, serialize($arrLoginUser), false, self::CACHE_EXPIRE_LOGINUSER);
				Dict::setDict('logined_user',$arrLoginUser);
				return true;
			}
		}
	}
	
	
	/**
	 * �ظ��û�����������
	 * Enter description here ...
	 */
	public static function getLightedCountByUid(){
		$intFid = Request::$intFid;
		$arrUserList = Dict::getDict('user_list');
		$arrUids = array_keys($arrUserList);
		$arrServiceInput = array(
			'fid' => $intFid,
			'uids' => $arrUids,
		);
		//var_dump($arrServiceInput);exit;
		$cache = self::_getCache();
		if ($cache == false) {
			$ret = Tieba_Service::call('light', 'getLightedCountByUid', $arrServiceInput);
			if ($ret === false || $ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				Bingo_Log::warning('get getLightedCountByUid failed! ['. print_r($arrServiceInput, true).']');
				return false;
			}
			foreach ($arrUserList as $key=>$user) {
				$arrUserList[$key]['light_up_num'] = 0;
				$arrUserList[$key]['light_types'] = 0;
				foreach ($ret['data'] as $k=>$v) {
					if ($key == $v['uid']) {
						$arrUserList[$key]['light_up_num'] = $v['lighted_count'];
						$arrUserList[$key]['light_types'] = $v['level'];
						break;
					}
				}
			}
			Dict::setDict('user_list',$arrUserList);
			return true;
		} else {
			$strUids = implode('_', $arrUids);
			$strKey = self::CACHE_KEY . '_' . md5($strUids);
			$retCache = $cache->get($strKey);
			if ($retCache) {
				$arrInfo = unserialize($retCache);
				Dict::setDict('user_list',$arrInfo);
				return true;
			}else {
				$ret = Tieba_Service::call('light', 'getLightedCountByUid', $arrServiceInput);
				if ($ret === false || $ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
					Bingo_Log::warning('get getLightedCountByUid failed! ['. print_r($arrServiceInput, true).']');
					return false;
				}
				
				foreach ($arrUserList as $key=>$user) {
					$arrUserList[$key]['light_up_num'] = 0;
					$arrUserList[$key]['light_types'] = 0;
					foreach ($ret['data'] as $k=>$v) {
						if ($key == $v['uid']) {
							$arrUserList[$key]['light_up_num'] = $v['lighted_count'];
							$arrUserList[$key]['light_types'] = $v['level'];
							if ($v['level']>0) {
								Tieba_Stlog::addNode('light_user',$v['uid']);
								Tieba_Stlog::addNode('light_level',$v['level']);
							}
							break;
						}
					}
				}
				$cache->set($strKey, serialize($arrUserList), false, self::CACHE_EXPIRE);
				Dict::setDict('user_list',$arrUserList);
				return true;
			}
		}
	}
	
	/**
	 * ��¥ģ��
	 * Enter description here ...
	 */
	public static function getChalouByTid(){
		$intTid = Request::$intTid;
		
		$arrServiceInput = array(
			'tid' => $intTid,
		);
		
		$cache = self::_getCache();
		if ($cache == false) {			
			$ret = Tieba_Service::call('light', 'getChalouByTid', $arrServiceInput);
			if ($ret === false || $ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				Bingo_Log::warning('get getLightedCountByUid failed! ['. print_r($arrServiceInput, true).']');
				return false;
			}
			$arrChalou = $ret['data'];
			if (!empty($arrChalou)){
				$arrPbLogicIn = array();
				$arrChalouPids = array();
				foreach ($arrChalou as $key=>$v) {
					$arrChalouPids[] = $v['pid'];
					$arrPbLogicIn[$key]['thread_id'] = $intTid;
					$arrPbLogicIn[$key]['post_id'] = $v['pid'];
					$arrPbLogicIn[$key]['page_num'] = 1;//(Request::$intPageNo-1) * Request::$intPageSize;
					$arrPbLogicIn[$key]['res_num'] = 1;//Request::$intPageSize;
					$arrPbLogicIn[$key]['see_author'] = Request::$intLzOnly;
					$arrPbLogicIn[$key]['reverse'] = Request::$intDirection;
				}
				/*var_dump(Rpc_Pblogicnew::getPosts(
							7293231,//Request::$intTid,
							56723983,//Request::$intPid,
							(Request::$intPageNo-1) * Request::$intPageSize,
							Request::$intPageSize,
							Request::$intLzOnly,
							Request::$intDirection ));exit;*/
				//var_dump($arrPbLogicIn);
				$ret = Rpc_Pblogicnew::getBatchPosts($arrPbLogicIn);					
				if ($ret === false) {
					Bingo_Log::warning('get getChalouByTid failed! ['. print_r($arrPbLogicIn, true).']');
					return false;
				}
				$postInfos = array();
				$pids = array();
				foreach ($ret as $post) {
					if ($post['err_no'] == 0 ) {
						if (!in_array($post['post_infos'][0]['post_id'], $pids) && in_array($post['post_infos'][0]['post_id'], $arrChalouPids)) {
							$pids[] = $post['post_infos'][0]['post_id'];
							$postInfos[] = $post['post_infos'][0];							
						}
					}
				}
				foreach ($postInfos as $key=>$post) {
					foreach ($arrChalou as $k=>$v) {
						if($post['post_id'] == $v['pid']) {
							$postInfos[$key]['light_up_num'] = $v['light_count'];
						}
					}
					//����post_listģ�����
					if (isset($post['post_id'])) {
						$postInfos[$key]['id'] = $post['post_id'];
					}
					if (isset($post['username'])) {
						$postInfos[$key]['user_name'] = $post['username'];
					}
					if (isset($post['image_url'])) {
						$postInfos[$key]['img'] = $post['image_url'];
					}
					if (isset($post['create_time'])) {
						$postInfos[$key]['time'] = $post['create_time'];
					}
				}
								
				Dict::setDict('light_list',$postInfos);
			}
			return true;			
		} else {
			$strKey = self::CACHE_KEY_CHALOU . '_' . $intTid;
			$retCache = $cache->get($strKey);
			if ($retCache) {
				$postInfos = unserialize($retCache);				
				Dict::setDict('light_list',$postInfos);
				return true;
			}else {
				$ret = Tieba_Service::call('light', 'getChalouByTid', $arrServiceInput);
				//var_dump($ret);exit;
				if ($ret === false || $ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
					Bingo_Log::warning('get getLightedCountByUid failed! ['. print_r($arrServiceInput, true).']');
					return false;
				}
				$arrChalou = $ret['data'];
				
				if (!empty($arrChalou)){
					Tieba_Stlog::addNode('light_has_chalou', 1);
					$arrPbLogicIn = array();
					$arrChalouPids = array();
					foreach ($arrChalou as $key=>$v) {
						$arrChalouPids[] = $v['pid'];
						$arrPbLogicIn[$key]['thread_id'] = $intTid;
						$arrPbLogicIn[$key]['post_id'] = $v['pid'];
						$arrPbLogicIn[$key]['page_num'] = 1;//(Request::$intPageNo-1) * Request::$intPageSize;
						$arrPbLogicIn[$key]['res_num'] = 1;//Request::$intPageSize;
						$arrPbLogicIn[$key]['see_author'] = Request::$intLzOnly;
						$arrPbLogicIn[$key]['reverse'] = Request::$intDirection;
					}
					/*
					$serviceInput = array(
						'post_id' => $arrChalouPids[0],
					);
					var_dump($serviceInput);
					$ret = Tieba_Service::call('post' , 'getPostInfoEx', $serviceInput);
					var_dump($ret);exit;*/
					$ret = Rpc_Pblogicnew::getBatchPosts($arrPbLogicIn);
					//var_dump($arrChalou);
					//var_dump($ret);
					
					if ($ret === false) {
						Bingo_Log::warning('get getChalouByTid failed! ['. print_r($arrPbLogicIn, true).']');
						return false;
					}
					$postInfos = array();
					$pids = array();
					foreach ($ret as $post ) {
						if ($post['err_no'] == 0 ) {
							if (!in_array($post['post_infos'][0]['post_id'], $pids) && in_array($post['post_infos'][0]['post_id'], $arrChalouPids)) {
								$pids[] = $post['post_infos'][0]['post_id'];
								$postInfos[] = $post['post_infos'][0];							
							}
						}
					}
					
					//var_dump($postInfos);exit;
					foreach ($postInfos as $key=>$post) {
						foreach ($arrChalou as $k=>$v) {
							if($post['post_id'] == $v['pid']) {
								$postInfos[$key]['light_up_num'] = $v['light_count'];
							}
						}
						if (isset($post['post_id'])) {
							$postInfos[$key]['id'] = $post['post_id'];
						}
						if (isset($post['username'])) {
							$postInfos[$key]['user_name'] = $post['username'];
						}
						if (isset($post['image_url'])) {
							$postInfos[$key]['img'] = $post['image_url'];
						}
						if (isset($post['create_time'])) {
							$postInfos[$key]['time'] = $post['create_time'];
						}
					}
					
					$cache->set($strKey, serialize($postInfos), false, self::CACHE_EXPIRE_CHALOU);
					
					Dict::setDict('light_list',$postInfos);
				}
				return true;
			}
		}
	}
	
	/**
	 * pid���������� ��pbҳcache
	 * Enter description here ...
	 */
	public static function getLightCountByPid(){
		//var_dump(Dict::getDictAll());
		$intTid = Request::$intTid;
		$arrPostList = Dict::getDict('post_list');
		//var_dump(get_include_path());
		//var_dump($arrPostList);exit;
		$arrPids = array();
		foreach ($arrPostList as $k => $post) {
			$arrPids[] = $post['id'];
		}
		$arrServiceInput = array(
			'tid'	=> $intTid,
			'pids'	=> $arrPids,
		);
		//var_dump($arrServiceInput);
		$ret = Tieba_Service::call('light', 'getLightCountByPid', $arrServiceInput);
		//var_dump($ret);exit;
		if ($ret === false || $ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('get getLightCountByPid failed! ['. print_r($arrServiceInput, true).']');
			return false;
		}
		foreach ($arrPostList as $key=>$post) {
			$arrPostList[$key]['light_up_num'] = 0;
			foreach ($ret['data'] as $k=>$v) {
				if ($post['id'] == $v['pid']) {
					$arrPostList[$key]['light_up_num'] = $v['light_count'];
					break;
				}
			}
		}
		Dict::setDict('post_list', $arrPostList);
		//var_dump(Dict::getDictAll());exit;
		return true;
	}

}
 /* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */