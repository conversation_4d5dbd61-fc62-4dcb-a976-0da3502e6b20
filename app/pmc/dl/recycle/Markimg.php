<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @abstract ���ӻ���վ ��ͼ����ָ�����
 *    
 */

class Dl_Recycle_Markimg {
		
	const SERVICE_NAME = "Dl_Recycle_Markimg";
	protected static $_conf = null;
	protected static $_db = null;
	//protected static $_use_split_db = true;
	protected static $_use_split_db = false;	// hhvm not support
	const DB_RAL_SERVICE_NAME = "forum_pmc";
	
	private static function _getDB() {
		if (self::$_db) {
			return self::$_db;
		}
		self::$_db = new Bd_DB ();
		if (self::$_db == null) {
			Bingo_Log::warning ( "[get db failed] new bd_db fail." );
			return null;
		}
		if (self::$_use_split_db) {
			$splitDBConfPath = ROOT_PATH . "/conf/db/";
			$splitDBConfFile = "db_dl_pmc_pmc.conf";
			$r = self::$_db->enableSplitDB ( self::DB_RAL_SERVICE_NAME, $splitDBConfPath, $splitDBConfFile );
			if (! $r) {
				Bingo_Log::warning ( "[get db failed] enable splitdb fail." );
				self::$_db = null;
				return null;
			}
			return self::$_db;
		}
		else {
			Bingo_Timer::start ( 'dbinit' );
			$r = self::$_db->ralConnect ( self::DB_RAL_SERVICE_NAME );
			Bingo_Timer::end ( 'dbinit' );
			if (! $r) {
				Bingo_Log::warning ( "[get db failed] bd db ral connect fail." );
				self::$_db = null;
				return null;
			}
			return self::$_db;
		
		}
		return null;
	}
	
	private static function _init() {
		if (self::$_conf == null) {
			self::$_conf = Bd_Conf::getConf ( "/app/pmc/dl_pmc_pmc" );
			if (self::$_conf == false) {
				Bingo_Log::warning ( "init get conf fail." );
				return false;
			}
		}
		return true;
	}
	
	private static function _errRet($errno) {
		return array (
				'errno' => $errno, 
				'errmsg' => Tieba_Error::getErrmsg ( $errno ) );
	}
	
	private static function _succRet($ret) {
		$error = Tieba_Errcode::ERR_SUCCESS;
		return array (
				'errno' => $error, 
				'errmsg' => Util_Errno::getErrmsg ( $error ), 
				'ret' => $ret 
			);
	}
	
	public static function getMarkImgTasksByUserIdAndPostId($arrInput) {
		if (! self::_init ()) {
			return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
		}
		
		if (! Util_Params::checkNum ( $arrInput, 'user_id' )) {
			Bingo_Log::warning ( __FUNCTION__ . ':input params invalid, [' . serialize ( $arrInput ) . '] ' );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		if (! Util_Params::checkNum ( $arrInput, 'pid' )) {
			Bingo_Log::warning ( __FUNCTION__ . ':input params invalid, [' . serialize ( $arrInput ) . '] ' );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		if (! Util_Params::checkNum ( $arrInput, 'tid' )) {
			Bingo_Log::warning ( __FUNCTION__ . ':input params invalid, [' . serialize ( $arrInput ) . '] ' );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		$userId = $arrInput ['user_id'];
		$postId = $arrInput ['pid'];
		$threadId = $arrInput['tid'];
		
		$dbObj = self::_getDB ();
		
		if (! $dbObj) {
			return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL );
		}
		
		$strSql = "select * from mark_task where user_id=$userId and pid=$postId and tid=$threadId";
		
		if (false === ($dbRet = $dbObj->query ( $strSql ))) {
			Bingo_Log::warning ( __FUNCTION__ . ':query db failed, [' . $strSql . '], error:' . $dbObj->error () );
			return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL );
		}

		return self::_succRet( array ('tasks' => $dbRet ) );
	}
	
	public static function addMarkTask($arrInput) {
		if (! self::_init ()) {
			return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
		}
		
		$userId = $arrInput ['user_id'];
		$postId = $arrInput ['pid'];
		$threadId = $arrInput ['tid'];
		
		$dbObj = self::_getDB ();
		
		if (! $dbObj) {
			return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL );
		}
		
		$userName = $dbObj->escapeString ( $userName );
		$query = "insert into mark_task set user_id = $userId, pid =$postId,tid=$threadId, start_time = " . time ();
		if (false === ($dbRet = $dbObj->query ( $query ))) {
			Bingo_Log::warning ( __FUNCTION__ . ':query db failed, [' . $query . '], error:' . $dbObj->error () );
			return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL );
		}
		
		return self::_succRet(array ('taskId' => $dbObj->getInsertID () ));
	}
	

	
	
	
}




?>
