<?php
/**
 * Created by PhpStorm.
 * User: yanglei
 * Date: 2018/3/6
 * Time: 下午12:10
 */

class Dl_Pmcmis_Pmcmis
{
    const SERVICE_NAME = "Dl_Pmcmis_Pmcmis";

    protected static $_conf = null;
    protected static $_dbSplit = null;
    protected static $_db = null;
    //protected static $_use_split_db = true;
    protected static $_use_split_db = false;	// hhvm not support

    const DB_RAL_SERVICE_NAME = "forum_mis_pmc";

    const TYPE_BLOCK = 0;
    const TYPE_MASK = 1;
    const TYPE_VCODE = 2;
    const TYPE_BLOCKMASK = 3;
    // 待审核
    const STATUS_UNDO = 0;
    // 审核通过
    const STATUS_PASS = 1;
    // 审核拒绝
    const STATUS_DENY = 2;
    const STATUS_DOING= 3;
    // 暂不处理
    const STATUS_NOTHING = 4;
    const ACCOUNT_UUAP = 0;
    const ACCOUNT_PASSPORT = 1;

    /**
     * @param null $split
     * @return Bd_DB|null
     */
    private static function _getDB($split = null) {
        if ($split === null) {
            $isSplit = self::$_use_split_db;
        } else {
            $isSplit = $split;
        }

        if ($isSplit) {
            if (!is_null(self::$_dbSplit)) {
                if (self::$_dbSplit->isConnected()) {
                    return self::$_dbSplit;
                }
            }
            self::$_dbSplit = new Bd_DB ();
            $splitDBConfPath = ROOT_PATH . "/conf/db/";
            $splitDBConfFile = "db_dl_pmcmis_pmcmis.conf";
            $r = self::$_dbSplit->enableSplitDB ( self::DB_RAL_SERVICE_NAME, $splitDBConfPath, $splitDBConfFile );
            if (! $r) {
                Bingo_Log::warning ( "[get db failed] enable splitdb fail." );
                self::$_dbSplit = null;
                return null;
            }
            return self::$_dbSplit;
        } else {
            if (!is_null(self::$_db)) {
                if (self::$_db->isConnected()) {
                    return self::$_db;
                }
            }

            self::$_db = new Bd_DB ();
            Bingo_Timer::start ( 'dbinit' );
            $r = self::$_db->ralConnect ( self::DB_RAL_SERVICE_NAME );
            Bingo_Timer::end ( 'dbinit' );
            if (! $r) {
                Bingo_Log::warning ( "[get db failed] bd db ral connect fail." );
                self::$_db = null;
                return null;
            }
            return self::$_db;
        }
        return null;
    }

    /**
     * @return bool
     */
    private static function _init() {
        if (self::$_conf == null) {
            self::$_conf = Bd_Conf::getConf ( "/app/pmc/dl_pmcmis_pmcmis" );
            if (self::$_conf == false) {
                Bingo_Log::warning ( "init get conf fail." );
                return false;
            }
        }
        return true;
    }

    /**
     * @param $errno
     * @return array
     */
    private static function _errRet($errno) {
        return array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg ( $errno ) );
    }

    /**
     * @param $data
     * @return array
     */
    private static function _successRet($data) {
        return array (
            'errno' => Tieba_Errcode::ERR_SUCCESS,
            'errmsg'=> Tieba_Error::getErrmsg ( Tieba_Errcode::ERR_SUCCESS ),
            'ret'	=> $data
        );
    }

    /**
     * 用户的申诉记录
     * @param $arrInput
     * @return array
     */
    public static function getUserApealList($arrInput) {
        if (! is_numeric ( $arrInput ['apeal_uid'] ) || ! is_numeric ( $arrInput ['apeal_flag'] ) || ! is_numeric ( $arrInput ['apeal_num'] )) {
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params.
        $apealUid = intval ( $arrInput ['apeal_uid'] );
        $apealFlag = intval ( $arrInput ['apeal_flag'] );
        $apealNum = intval ( $arrInput ['apeal_num'] );

        if (! self::_init ()) {
            return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
        }
        //output params.
        $ret = false;
        $use_split_db = true;
        if (isset($arrInput['use_split_db'])){
            $use_split_db = $arrInput['use_split_db'];
        }
        $db = self::_getDB ( $use_split_db );
        if (! $db) {
            Bingo_Log::warning ( "db connect failed" );
            return false;
        }
        $where = '';
        if (isset($arrInput ['apeal_code']) && $arrInput ['apeal_code']){
            $where = "and apeal_code={$arrInput ['apeal_code']}";
        }
        $num = '';
        if (!$use_split_db) {
            $num = $apealUid % 128;
        }

        $sql = "select apeal_id,apeal_code,apeal_uid,apeal_time,apeal_type,apeal_num,op_uid,op_uname,op_time,status,op_reason from user_apeal{$num} where apeal_uid=$apealUid and apeal_flag=$apealFlag {$where} order by apeal_time desc limit 0, $apealNum";
        $ret = $db->query ( $sql );
        if ($ret === false) {
            Bingo_Log::warning ( "execute sql fail: $sql" );
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        }
        else {
            $error = Tieba_Errcode::ERR_SUCCESS;
        }
        $arrOutput = array (
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg ( $error ),
            'ret' => $ret );
        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getApealList($arrInput) {
        if (! is_numeric ( $arrInput ['page'] ) || ! is_numeric ( $arrInput ['page_size'] ) || ! isset ( $arrInput ['status'] ) || ! isset ( $arrInput ['role'] ) || ! is_numeric ( $arrInput ['is_vip'] ) || ! is_numeric ( $arrInput ['is_auto'] ) || ! is_numeric ( $arrInput ['apeal_num'] )) {
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        //input params.
        $page = intval ( $arrInput ['page'] );
        $pageSize = intval ( $arrInput ['page_size'] );
        $isVip = intval ( $arrInput ['is_vip'] );
        $isAuto = intval ( $arrInput ['is_auto'] );
        $apealNum = intval ( $arrInput ['apeal_num'] );
        $status = $arrInput ['status'];
        $apealCode = is_numeric ( $arrInput ['apeal_code'] ) ? intval ( $arrInput ['apeal_code'] ) : 0;
        $apealUid = $arrInput ['apeal_uid'];
        $lockUid = intval ( $arrInput ['lock_uid'] );
        $role = intval ( $arrInput ['role'] );
        if (! self::_init ()) {
            return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
        }
        //output params.
        $ret = false;

        $start = ($page - 1) * $pageSize;

        $db = self::_getDB ();
        $sql = "select apeal_id,apeal_code,apeal_uid,apeal_time,apeal_type,apeal_num,op_uid,op_uname,op_reason,status,is_auto,is_vip,punish_byman,punish_fid from apeal_list";
        if (! $db) {
            Bingo_Log::warning ( "db connect failed" );
            return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL );
        }

        $sql .= " where 1";
        if ($apealNum){
            if ($apealNum == 1) {
                $sql .= " and apeal_num=1";
            } else if ($apealNum > 1) {
                $sql .= " and apeal_num>1";
            }
        }
        if ($lockUid > 0) {
            $sql .= " and lock_uid=$lockUid";
        }
        switch ($status){
            case 'undo':
                $sql .= " and status=0";
                break;
            case 'nothing':
                $sql .= " and status=4";
                break;
            default:
                //$sql .= " and status in (1,2)";
                break;
        }
        if ($apealCode) {
            $sql .= " and apeal_code=$apealCode";
        }
        if ($apealUid) {
            $sql .= " and apeal_uid=$apealUid";
        }
        if (isset ( $arrInput ['punish_fid'] )) {
            $sql .= " and punish_fid=" . intval ( $arrInput ['punish_fid'] );
        }
        if (isset ( $arrInput ['apeal_type'] )) {
            $sql .= " and apeal_type=" . intval ( $arrInput ['apeal_type'] );
        }
        if ($isVip != -1){
            $sql .= " and is_vip=$isVip";
        }
        if ($isAuto != -1){
            $sql .= " and is_auto=$isAuto";
        }
        if ($role != -1){
            $sql .= " and role=$role";
        }

        $sql .= " order by apeal_id desc limit $start,$pageSize";
        Bingo_Log::notice('getApealList='.$sql);
        $ret = $db->query ( $sql );
        if ($ret === false) {
            Bingo_Log::warning ( "query db failed: $sql" );
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        }
        else {
            $error = Tieba_Errcode::ERR_SUCCESS;
        }
        $arrOutput = array (
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg ( $error ),
            'ret' => $ret );
        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array|bool
     */
    public static function getPunishHistory($arrInput) {
        if (! is_numeric ( $arrInput ['apeal_code'] )) {
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        $apealCode = intval ( $arrInput ['apeal_code'] );
        //input params.
        if (! self::_init ()) {
            return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
        }

        $db = self::_getDB ();
        if (! $db) {
            Bingo_Log::warning ( "db connect failed" );
            return false;
        }

        $sql = "select punish_start_time,punish_end_time,punish_user,punish_reason,punish_forum,punish_fid,punish_method,monitor_type from punish_history where apeal_code=$apealCode";
        $ret = $db->query ( $sql );
        if ($ret === false) {
            Bingo_Log::warning ( "query db failed : $sql" );
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
            $output = array ();
        }
        else {
            $error = Tieba_Errcode::ERR_SUCCESS;
            $output = $ret;
        }
        $arrOutput = array (
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg ( $error ),
            'output' => $output );
        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array|bool
     */
    public static function getApealInfo($arrInput) {
        if (! is_numeric ( $arrInput ['apeal_code'] )) {
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        //input params.
        $apealCode = intval ( $arrInput ['apeal_code'] );
        if (! self::_init ()) {
            return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
        }
        //output params.
        $ret = false;
        $db = self::_getDB ();
        if (! $db) {
            Bingo_Log::warning ( "db connect failed" );
            return false;
        }
        $sql = "select apeal_id,apeal_code,apeal_flag,apeal_uid,apeal_time,apeal_type,apeal_num,op_uid,op_uname,op_time,apeal_reason,punish_byman,status,punish_fid,is_vip from apeal_list where apeal_code=$apealCode";
        $ret = $db->query ( $sql );
        if ($ret === false) {
            Bingo_Log::warning ( "execute sql fail: $sql" );
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        }
        else {
            $error = Tieba_Errcode::ERR_SUCCESS;
        }
        $arrOutput = array (
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg ( $error ),
            'ret' => $ret );
        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public function updateApeal($arrInput) {
        if (! is_numeric ( $arrInput ['op_uid'] ) || ! isset ( $arrInput ['op_uname'] ) || ! is_numeric ( $arrInput ['apeal_code'] ) || ! is_numeric ( $arrInput ['apeal_uid'] ) || ! is_numeric ( $arrInput ['status'] ) || ! isset ( $arrInput ['op_reason'] )) {
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        $apealUid = intval ( $arrInput ['apeal_uid'] );
        $opUid = intval ( $arrInput ['op_uid'] );
        $opUname = $arrInput ['op_uname'];
        $apealCode = intval ( $arrInput ['apeal_code'] );
        $opTime = time ();
        $opReason = $arrInput ['op_reason'];
        $status = intval ( $arrInput ['status'] );
        //input params.
        if (! self::_init ()) {
            return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
        }
        $error = Tieba_Errcode::ERR_SUCCESS;
        $db = self::_getDB ( false );
        $dbSplit = self::_getDB ( true );
        if (! $db || ! $dbSplit) {
            Bingo_Log::warning ( "db connect failed" );
            $error = Tieba_Errcode::ERR_DB_CONN_FAIL;
            $arrOutput = array (
                'errno' => $error,
                'errmsg' => Tieba_Error::getErrmsg ( $error ) );
            return $arrOutput;
        }
        $opUname = $db->escapeString ( $opUname );
        $opReason = $db->escapeString ( $opReason );
        $sql1 = "update apeal_list set op_uid=$opUid,op_uname='$opUname',status=$status,op_time=$opTime,op_reason='$opReason' where apeal_code=$apealCode";
        $ret1 = $db->query ( $sql1 );
        if (!$ret1){
            Bingo_Log::warning ( "query db failed sql1: $sql1" );
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        }
        $sql2 = "update user_apeal set op_uid=$opUid,op_uname='$opUname',status=$status,op_time=$opTime,op_reason='$opReason' where apeal_uid=$apealUid and apeal_code=$apealCode";
        $ret2 = $dbSplit->query ( $sql2 );
        if (!$ret2){
            Bingo_Log::warning ( "query db failed sql2: $sql2" );
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        }
        $arrOutput = array (
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg ( $error )
        );
        return $arrOutput;
    }
}