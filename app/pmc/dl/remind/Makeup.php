<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @abstract �û���Ȩ������Ϣ����ӺͶ�ȡ
 *    
 */
class Dl_Remind_Makeup {
	
	const REDIS_SERVICE_NAME = 'Redis_forum_pmc_markup';
	const PID	= 'forum';
	const TK	= 'forum';
	const APP	= 'pmc';
	const INSTANCE	= 'markup';
	
	private static $_objRedis = null;
	
	private static function _errRet($errno) {
		return array (
				'errno' => $errno, 
				'errmsg' => Tieba_Error::getErrmsg ( $errno ) 
				);
	}
	
	private static function _successRet($data) {
		return array (
				'errno' => Tieba_Errcode::ERR_SUCCESS, 
				'errmsg' => Tieba_Error::getErrmsg ( Tieba_Errcode::ERR_SUCCESS ),
				'ret' => $data  
				);
	}
	
	private static function _getRedisInstance ( ) {
		if ( null === self::$_objRedis ) {
			$arrConf = array(
				'pid'		=> self::PID,
				'tk'		=> self::TK,
				'app'		=> self::APP,
				'instance'	=> self::INSTANCE
			);
			self::$_objRedis = Bd_RalRpc::create('AK_Service_Redis', $arrConf);
			if ( null === self::$_objRedis ) {
				Bingo_Log::warning( __FUNCTION__ . ":" . Util_Log::$strCallRedisFailed . "[error]" . serialize(Bd_RalRpc::get_error()));
			}
		}
		return self::$_objRedis;
	}
	
	private static function _getKey ( $intUserId, $strMakeupType ) {
		return "p:$strMakeupType:$intUserId";
	}
	
	private static function _getMakeupType ( $strMakeupType = null ) {
		$arrMakeupTypes = array(
				Util_Const::$protectServiceTypeBlock, 
				Util_Const::$protectServiceTypeMask,
				Util_Const::$protectServiceTypeBlockmask,
				Util_Const::$protectServiceTypeVcode,
				Util_Const::$protectServiceTypeAll,
				Util_COnst::$vcodePreventMakeup
		);
		
		if ( null === $strMakeupType ) {
			return $arrMakeupTypes;
		}
		
		if ( in_array( $strMakeupType, $arrMakeupTypes ) ) {
			return $arrMakeupTypes[$strMakeupType];
		}
		
		Bingo_Log::warning( __FUNCTION__ . ":" . Util_Log::$strParamsInvilid . " [params]" . serialize($strMakeupType) );
		return false;
	}
	
	public static function getPrivilegeMakeup( $arrInput ) {
		if ( !isset($arrInput['input']) ) {
			Bingo_Log::warning( __FUNCTION__ . ":" . Util_Log::$strParamsInvilid . "[params]" . serialize($arrInput) );
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		
		$input = Tieba_Service::getArrayParams($arrInput, 'input');
		if ( !isset($input['user_id']) || !is_numeric($input['user_id']) ) {
			Bingo_Log::warning( __FUNCTION__ . ":" . Util_Log::$strParamsInvilid . "[params]" . serialize($arrInput) );
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		
		$intUserId = intval($input['user_id']);
		
		$objRedis = self::_getRedisInstance();
		if ( null === $objRedis ) {
			return self::_errRet(Tieba_Errcode::ERR_DIFANG_CALL_REDIS_FAIL);
		}
		
		$arrRpcInput = array('reqs' => array());
		foreach ( self::_getMakeupType() as $strMakeupType ) {
			array_push($arrRpcInput['reqs'], array('key'=>self::_getKey($intUserId, $strMakeupType)));
		}

		$arrResult = $objRedis->GET($arrRpcInput);
		
		if ( false === $arrResult || $arrResult['err_no'] !== Tieba_Errcode::ERR_SUCCESS ) {
			Bingo_Log::warning( __FUNCTION__ . ":" . Util_Log::$strCallRedisFailed . '[ret]' . serialize($arrResult) );
		}
		
		$arrData = array();
		if ( !empty($arrResult['ret']) && is_array($arrResult['ret']) ) {
			foreach ( $arrResult['ret'] as $key=>$value ) {
				$arrKeys = explode(':', $key);
				if ( isset($arrKeys[1]) && !empty($arrKeys[1]) && !empty($value) ) {
					$strType = $arrKeys[1];
					$arrData[$strType] = Bingo_String::json2array($value);
				}
			}
		}
		
		return self::_successRet($arrData);
	}
	
	private static function _checkParams( $input ) {
		if ( !isset($input['user_id']) || !is_numeric($input['user_id']) ) {
			return false;
		}
		if ( !isset($input['makeup_type']) && !in_array($input['makeup_type'], self::_getMakeupType()) ) {
			return false;
		}
		if ( !isset($input['expire']) || !is_numeric($input['expire']) ) {
			return false;
		}
		if ( !isset($input['affect_days']) || !is_numeric($input['affect_days']) ) {
			return false;
		}
		if ( !isset($input['start_time']) || !is_numeric($input['start_time']) ) {
			return false;
		}
		if ( !isset($input['end_time']) || !is_numeric($input['end_time']) ) {
			return false;
		}
		if ( !isset($input['range']) || empty($input['range']) || !is_array($input['range']) ) {
			return false;
		}
		
		foreach ( $input['range'] as $fid ) {
			if ( !is_numeric($fid) ) {
				return false;
			}
		}
		
		return true;
	}
	
	public static function updatePrivilegeMakeup( $arrInput ) {
		if ( !isset($arrInput['input']) ) {
			Bingo_Log::warning( __FUNCTION__ . ":" . Util_Log::$strParamsInvilid . "[params]" . serialize($arrInput) );
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		
		$input = Tieba_Service::getArrayParams($arrInput, 'input');
		if ( !self::_checkParams($input) ) {
			Bingo_Log::warning( __FUNCTION__ . ":" . Util_Log::$strParamsInvilid . "[params]" . serialize($arrInput) );
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		
		$intUserId = intval($input['user_id']);
		$strMakeupType = $input['makeup_type'];
		$intLifeTime = intval($input['expire']);
		
		$arrMakeupInfo = array(
			'affect_days'	=> intval($input['affect_days']),
			'start_time'	=> intval($input['start_time']),
			'end_time'		=> intval($input['end_time']),
			'range'			=> array_map('intval', $input['range']),
			'ctime'			=> time()
		);
		
		$objRedis = self::_getRedisInstance();
		if ( null === $objRedis ) {
			return self::_errRet(Tieba_Errcode::ERR_DIFANG_CALL_REDIS_FAIL);
		}
		
		$strRedisKey = self::_getKey($intUserId, $strMakeupType);
		
		$arrRpcInput = array('key'=>$strRedisKey, 'value'=>Bingo_String::array2json($arrMakeupInfo) );
		$arrResult = $objRedis->SET($arrRpcInput);
		
		if ( false === $arrResult || $arrResult['err_no'] !== Tieba_Errcode::ERR_SUCCESS ) {
			Bingo_Log::warning( __FUNCTION__ . ":" . Util_Log::$strCallRedisFailed . '[errmsg]' . $arrResult['err_msg'] . '[ret]' . serialize($arrResult) );
			return self::_successRet(false);
		}
		
		$arrExpireInput = array( 'key'=>$strRedisKey, 'seconds'=> $intLifeTime );
		$mixResult = $objRedis->EXPIRE($arrExpireInput);
		if ( false === $mixResult || $mixResult['err_no'] !== Tieba_Errcode::ERR_SUCCESS ) {
			Bingo_Log::warning( __FUNCTION__ . ":" . Util_Log::$strCallRedisFailed . '[errmsg]' . $mixResult['err_msg'] . '[ret]' . serialize($mixResult) );
			return self::_successRet(false);
		}
		
		return self::_successRet(true);
	}
	
}
