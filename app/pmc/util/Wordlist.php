<?php 


/*
 * ���ôʱ���service��д����
 * WL = wordlist
 * WI = worditem
 */
class Util_Wordlist 
{
	const WL_FID_PHONEVCODE_NOTALLOW_TOP = ''; // �����������Щ�ɵ����ӣ���ô������ͨ��  �ֻ���֤��ָ�ɾ�� �� ������ҳ�Ƽ��İ�
	
	public static function addWiUeg()
	{
		
	}
	
	public static function getWiUeg()
	{
		
	}
	
	/*
	 * ��Ӵ���
	 */
	public static function addWI()
	{
		$input = array(
		);
		
		$output = Tieba_Service::call();
		
		
	}
	
	/*
	 * ��ѯ����
	 */
	public static function getWI()
	{
		$input = array(
		);
		
		$output = Tieba_Service::call();
	}
	
}

