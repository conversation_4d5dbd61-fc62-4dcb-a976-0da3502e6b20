<?php

class Util_Anti {
	
	/*
	 * ��ȡ�û���������������
	 */
	
	public static function antiGetBlockReason( $user_id , $forum_id )
	{
		$req = array(
			'user_id' => $user_id,
			'forum_id' => $forum_id,
		);
		$res = Tieba_Service::call('anti','antiGetBlockReason',$req);
		return $res;
	}
	
	private static function getParams( $intUserId ) {
		$req=array(
			'req'=>array(
				 'rulegroup'=>array('ueg'),
				 'app'=>'pmc',
				 'cmd'=>'submitAppeal',
				 'op_group'=>'system',
				 'user_id'=>intval($intUserId),
				 'uid'=>intval($intUserId),
			 ),
		);
		return $req;
	}
	
	
	private static function getBawuParams( $intUserId ) {
		$req=array(
				'req'=>array(
						'rulegroup'=>array('ueg'),
						'app'=>'pmc',
						'cmd'=>'submitAppeal',
						'op_group'=>'bawu',
						'user_id'=>intval($intUserId),
						'uid'=>intval($intUserId),
				),
		);
		return $req;
	}
	public static function actsctrlQuery ( $intUserId ) {
		$req = self::getParams($intUserId);
		$res = Tieba_Service::call('anti','antiActsctrlQuery',$req);
		return $res;
	}
	
	public static function actsctrlSubmit ( $intUserId ) {
		$req = self::getParams($intUserId);
		$res = Tieba_Service::call('anti','antiActsctrlSubmit',$req);
		return $res;
	}
	
	public static function getCaptcha ( $intUserId ) {
		$req = self::getParams($intUserId);
        $req['call_from'] = 'pmc';
        $req['group'] = 'antiGetCaptcha';
		$res = Tieba_Service::call('anti','antiGetCaptcha',$req);
		return $res;
	}
	
	public static function checkCaptcha ( $intUserId, $strVcode, $strInput) {
		$req=array(
			'req'=>array(
				 'rulegroup'=>array('ueg'),
				 'app'=>'pmc',
				 'cmd'=>'submitAppeal',
				 'op_group'=>'system',
				 'user_id'=>intval($intUserId),
				 'uid'=>intval($intUserId),
				 'captcha_vcode_str'=>$strVcode,
				 'captcha_input_str'=>$strInput
			 ),
		);
		$res = Tieba_Service::call('anti','antiCheckCaptcha',$req);
		return $res;
	}
	public static function getBawuCaptcha ( $intUserId ) {
		$req = self::getBawuParams($intUserId);
        $req['call_from'] = 'pmc';
        $req['group'] = 'antiGetCaptcha';
		$res = Tieba_Service::call('anti','antiGetCaptcha',$req);
		return $res;
	}
	public static function checkBawuCaptcha ( $intUserId, $strVcode, $strInput) {
		$req=array(
				'req'=>array(
						'rulegroup'=>array('ueg'),
						'app'=>'pmc',
						'cmd'=>'submitAppeal',
						'op_group'=>'bawu',
						'user_id'=>intval($intUserId),
						'uid'=>intval($intUserId),
						'captcha_vcode_str'=>$strVcode,
						'captcha_input_str'=>$strInput
				),
		);
		$res = Tieba_Service::call('anti','antiCheckCaptcha',$req);
		return $res;
	}
	/**
	 * ��ѯ��ϵͳɾ�����������ٻز�������12�����ڣ�
	 */
	public static function getDeletPostMonitorList($postId,$intDeletTime){
		$arrInput = array(
			 'req' => array(
		        'post_id' => intval($postId),
		        'dateTable' => date('Ymd',$intDeletTime),//'20140709',
		     ),
		);
		$arrRes = Tieba_Service::call('antiserver' , 'queryDebugMonitor' , $arrInput);
		if ( false == $arrRes || $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
			Bingo_Log::warning( __FUNCTION__ . 'call service userstate:get_block_list failed! [input]' . serialize($arrInput) . ' [res]' . serialize($arrRes) );
			return false;
		 }
		 return $arrRes['res']['listMonitor'];
	}
	/**
	 * ��ѯһ�����Ƿ���confilter�ʱ���
	 * if_exist=0,����
	 * if_exist=1,��
	 * if_exist>1,��ȷ��
	 */
	public static function checkIfKeyExistInConfilter($strContent,$strTableName,$ie = 'gbk'){
		$result = array(
			'if_exist' => 2, 		
		);
        //confilter -----//
        $arrInputPara = array(
                              array(
                                    'groupid'=>-1,
                                    'content'=>$strContent,
                                    'return_position'=>'no',
                                    'no_normalize'=>'yes',
                                    'dictlist'=>$strTableName,
                                   ),
                             );
        $arrInput=array('req'=>array('confilter_type'=>'Confilter','reqs'=>$arrInputPara),);
        $arrOutput= Tieba_Service::call('anti','antiConfilter',$arrInput,null,null,'post', 'php', $ie);
        if(false == $arrOutput){
            Bingo_Log::warning("call anti_antiConfilter false. [input:".serialize($arrInput)."] [output:".serialize($arrOutput)."]");
            return $result;//����ʧ��
        }elseif(Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']){
            Bingo_Log::warning("call anti_antiConfilter err. [input:".serialize($arrInput)."] [output:".serialize($arrOutput)."]");
			return $result;
        }elseif(isset($arrOutput['res']['ans'])){
            $ans = $arrOutput['res']['ans'][0];
            if($ans->count > 0){//�дʱ�
            	Bingo_Log::warning("$strContent hit $strTableName");
            	$result['if_exist'] = 1;
                return $result;
            }else{
            	Bingo_Log::warning("$strContent not hit $strTableName");
            	$result['if_exist'] = 0;
                return $result;
            }
            //Bingo_Log::warning("call anti_antiConfilter succ. [input:".serialize($arrInput)."] [output:".serialize($arrOutput)."]");
        }
		return $result;
	
	}

	/**************** ��Ҫ����Ǩ�Ƶķ��� zengguowang 20190109 start *******************************/

    private static function getCommonParams($intUserId, $cmd, $app, $ip){
        $req = array(
            'req' => array(
                'rulegroup' => array('ueg'),
                'app' => $app,
                'cmd' => $cmd,
                'uid' => intval($intUserId),
                'ip'  => $ip
            ),
        );
        return $req;
    }
    /**
     * ͨ�õ����ȿ���
     * @param unknown_type $intUserId
     * @param unknown_type $cmd
     */
    public static function actsctrlQueryCommon ( $intUserId, $cmd, $ip, $app = 'hermes') {
        $req = self::getCommonParams($intUserId,$cmd,$app, $ip);
        $res = Tieba_Service::call('anti','antiActsctrlQuery',$req);
        Bingo_Log::notice( __FUNCTION__ . ". antiActsctrlQuery, req=".serialize($req).', res='.serialize($res));
        return $res;
    }
    public static function actsctrlSubmitCommon($intUserId, $cmd, $ip, $app = 'hermes'){
        $req = self::getCommonParams($intUserId,$cmd,$app, $ip);
        $res = Tieba_Service::call('anti','antiActsctrlSubmit',$req);
        Bingo_Log::notice( __FUNCTION__ . ". antiActsctrlSubmit, req=".serialize($req).', res='.serialize($res));
        return $res;
    }

    /**************** ��Ҫ����Ǩ�Ƶķ��� zengguowang 20190109 end *******************************/
}
