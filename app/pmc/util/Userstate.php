<?php

class Util_Userstate {
	
	private static $serviceTypeBlockId = 'blockid';
	private static $serviceTypeVcode   = 'vcode';
	
	private static $opGroupSystem = 'system';
	private static $opGroupAdmin  = 'admin';
	private static $opGroupBawu   = 'bawu';
	
	private static $blockMaskType = 16;


	private static function _getReqParams( $intUserId, $bolIsBawu ) {
		if ( $bolIsBawu ) {
			return array(
				'reqs'=> array(
					'bawu_block' => array('service_type'=>self::$serviceTypeBlockId,'opgroup'=>self::$opGroupBawu,'key'=>$intUserId)
				)
			);
		}
		return array(
			'reqs'=> array(
				'system_block' => array('service_type'=>self::$serviceTypeBlockId,'opgroup'=>self::$opGroupSystem,'key'=>$intUserId),
				'admin_block'  => array('service_type'=>self::$serviceTypeBlockId,'opgroup'=>self::$opGroupAdmin,'key'=>$intUserId),
				'system_vcode' => array('service_type'=>self::$serviceTypeVcode,'opgroup'=>self::$opGroupSystem,'key'=>$intUserId),
				'admin_vcode'  => array('service_type'=>self::$serviceTypeVcode,'opgroup'=>self::$opGroupAdmin,'key'=>$intUserId),
			)
		);
	}
	
	public static function getAllStateInfos ( $intUserId, $bolIsBawu ) {
		$arrInput = self::_getReqParams($intUserId, $bolIsBawu);
		$arrRes = Tieba_Service::call( 'userstate', 'getAllStateInfos', $arrInput );
		if ( false === $arrRes || $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
			Bingo_Log::warning( __FUNCTION__ . 'call service failed! [input]' . serialize($arrInput) . ' [res]' . serialize($arrRes) );
			return array();
		}
		$arrAllState = array();
		$arrUserStates = self::_decorateUserSates( $arrRes['res'] );
		foreach ( $arrUserStates as $punishType=>$arrItems ) {
			if ( false === $arrItems ) continue;
			if ( in_array($punishType, array('system_block','system_bm','system_vcode') ) && !empty($arrItems) ) {
				$arrAllState[$punishType] = self::_mergePunish( $arrItems, $punishType );
			} elseif ( in_array($punishType, array('admin_block','admin_bm','admin_vcode','bawu_block') ) && !empty ($arrItems) ) {
				$arrAllState[$punishType] = self::_dealPunish( $arrItems, $punishType );
			}
		}
		return $arrAllState;
	}
	
	private static function _getPunishType ( $punishType ) {
		$arrMap = array(
			'system_block'  => Util_Const::$punishTypeBanID,
			'system_bm'		=> Util_Const::$punishTypeBanAndMask,
			'system_vcode'	=> Util_Const::$punishTypeSetUserState,
			'admin_block'	=> Util_Const::$punishTypeBanID,
			'admin_bm'		=> Util_Const::$punishTypeBanAndMask,
			'admin_vcode'	=> Util_Const::$punishTypeSetUserState,
			'bawu_block'	=> Util_Const::$punishTypeBanID
		);
		return $arrMap[$punishType];
	}
	
	private static function _mergePunish ( $arrItems, $key ) {
		$punishType		= self::_getPunishType($key);
		$arr = explode('_', $key);
		$opUserType		= Util_Const::opgroup2OpUserType($arr[0]);
		$punishStartTime = 0;
		$punishEndTime = 0;
		$punishRange = array();
		foreach ( $arrItems as $arrItem ) {
			if ( $arrItem['start_time'] < $punishStartTime || empty($punishStartTime) ) {
				$punishStartTime = $arrItem['start_time'];
			}
			if ( $arrItem['time'] > $punishEndTime ) {
				$punishEndTime  = $arrItem['time'] > 4294967295 ? 4294967295 : $arrItem['time'];//̫�������û�� ��2��32�ΰ�
			}
			array_push( $punishRange, intval($arrItem['fid']) );
		}
		$punishRange = array_unique($punishRange);
		sort($punishRange);
		$record = $opUserType . '_' . $punishType . '_' . $punishStartTime . '_' . $punishEndTime . '_' . implode(',', $punishRange);
		return array(
			$record => array(
				'op_user_type' => $opUserType,
				'punish_type'  => $punishType,
				'punish_start_time' => $punishStartTime,
				'punish_end_time'	=> $punishEndTime,
				'punish_range'		=> implode(',', $punishRange)
			)
		);
	}
	
	private static function _dealPunish ( $arrItems, $key ) {
		$arrRes = array();
		$punishType		= self::_getPunishType($key);
		$arr = explode('_', $key);
		$opUserType		= Util_Const::opgroup2OpUserType($arr[0]);
		foreach ( $arrItems as $arrItem ) {
			$punishStartTime = $arrItem['start_time'];
			$punishEndTime  =  $arrItem['time'] > 4294967295 ? 4294967295 : $arrItem['time'];//̫�������û�� ��2��32�ΰ�
			$punishRange	= intval($arrItem['fid']);
			$record = $opUserType . '_' . $punishType . '_' . $punishStartTime . '_' . $punishEndTime . '_' . $punishRange;
			$item = array(
				'op_user_type' => $opUserType,
				'punish_type'  => $punishType,
				'punish_start_time' => $punishStartTime,
				'punish_end_time'	=> $punishEndTime,
				'punish_range'		=> $punishRange
			);
			$arrRes[$record] = $item;
		}
		return $arrRes;
	}


	private static function _decorateUserSates ( $arrUserStates ) {
		foreach ( $arrUserStates as $punishType=>&$arrItems ) {
			if ( false === $arrItems || !in_array($punishType, array('system_block','admin_block')) ) {
				continue;
			}
			foreach ( $arrItems as $index=>$arrItem ) {
				if ( intval($arrItem['block_type']) !== self::$blockMaskType ) {
					continue;
				}
				if ( $punishType == 'system_block' ) {
					if ( !isset($arrUserStates['system_bm']) ) {
						$arrUserStates['system_bm'] = array();
					}
					$arrItem['fid'] = 0;
					array_push($arrUserStates['system_bm'], $arrItem);
					unset($arrItems[$index]);
				}
				if ( $punishType == 'admin_block' ) {
					if ( !isset($arrUserStates['admin_bm']) ) {
						$arrUserStates['admin_bm'] = array();
					}
					$arrItem['fid'] = 0;
					array_push($arrUserStates['admin_bm'], $arrItem);
					unset($arrItems[$index]);
				}
			}
		}
		return $arrUserStates;
	}
	
	/**
	 * ��ѯ�û���ϵͳ�����������������Σ��Ĳ������� 
	 * block_type,��������1:���ڷ��, 2:���ڷ�IP, 4:ȫ�ַ��, 8:ȫ�ַ�IP, 16:ȫ�ַ������
	 * forum_id,��ID, ��ѡ, block_typeΪ4,8,16ʱ�ɲ���ֵ
     * user_id,�û�ID, ��ѡ
     * opgroup, �����˽�ɫ, ��ѡ, (system:ϵͳ, bawu:����, admin:����Ա,)
	 */
	public static function getPunishMonitorType($intUid,$intPunishType,$intForumId,$opGroup = 'system'){
		 $arrInput = array(
			'block_type' => $intPunishType,
		    'forum_id' => $intForumId,  
		    'user_id' => $intUid,
		    'opgroup' => $opGroup,
		 );
		 $arrRes = Tieba_Service::call('userstate', 'get_block_list', $arrInput);
		 if ( false === $arrRes || $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS || empty($arrRes['user_block_list']['data'])) {
			Bingo_Log::warning( __FUNCTION__ . 'call service userstate:get_block_list failed! [input]' . serialize($arrInput) . ' [res]' . serialize($arrRes) );
			return false;
		 }
		 return $arrRes['user_block_list']['data'][0];
	}
	
	/**
	 * ��ȡ�û�Ŀǰ���еĴ�����¼���ɸ��ݴ�������block_type��opgroup��ѯ
	 * block_type,��������1:���ڷ��, 2:���ڷ�IP, 4:ȫ�ַ��, 8:ȫ�ַ�IP, 16:ȫ�ַ������
	 * opgroup:1-ϵͳ��2-����3-����Աpm
	 * @param unknown_type $arrInput
	 */
	public static function getPunishList($arrInput){
		if(!isset($arrInput['user_id'])){
			Bingo_Log::warning("input param errno:".serialize($arrInput));
			return false;
		}
		$uid = intval($arrInput['user_id']);
		$ret = Tieba_Service::call ( 'userstate', 'get_user_all_blocking_info', array ('uid' => $uid ) );
		if ($ret === false || $ret ['errno'] !== Tieba_Errcode::ERR_SUCCESS || $ret ['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( __FUNCTION__ . ":$uid call block failed:" . serialize($ret) );
			return false;
		}
		$arrRecord = $ret['block_info'];
		$returnData = $arrRecord;
		if(isset($arrInput['block_type'])){
			$blockType = intval($arrInput['block_type']);	
			foreach($returnData as $key => $info){
				if(intval($info['block_type']) !== $blockType){
					unset($returnData[$key]);
				}
			}
		}
		if(isset($arrInput['opgroup'])){
			$opGroup = intval($arrInput['opgroup']);
			foreach($returnData as $key => $info){
				if(intval($info['opgroup']) !== $opGroup){
					unset($returnData[$key]);
				}
			}
		}
		if(isset($arrInput['monitor_type'])){
			$monitorType = intval($arrInput['monitor_type']);
			foreach($returnData as $key => $info){
				if(intval($info['monitor_type']) !== $monitorType){
					unset($returnData[$key]);
				}
			}
		}
		return $returnData;
	}

    /**
     * ��ȡ�û�Ŀǰ���еĴ�����¼���ɸ��ݴ�������block_type��opgroup��ѯ
     * block_type,��������1:���ڷ��, 2:���ڷ�IP, 4:ȫ�ַ��, 8:ȫ�ַ�IP, 16:ȫ�ַ������
     * opgroup:1-ϵͳ��2-����3-����Աpm
     * @param unknown_type $arrInput
     */
    function getNowPunish($arrInput){
        if(!isset($arrInput['user_id'])){
            Bingo_Log::warning("input param errno:".serialize($arrInput));
            return false;
        }
        $uid = intval($arrInput['user_id']);

        $input = array(
            "forum_id" => 0,
            "user_id" => $uid,
            "ip" => 0
        );
        $ret = Tieba_Service::call('userstate', 'get_user_block_info', $input);
        if ($ret === false || $ret ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ( __FUNCTION__ . ":$uid call block failed:" . serialize($ret) );
            return false;
        }

        $returnData = $ret['block_info']['0'];
        return $returnData;
    }

	/**
	*��ѯ��ǰ��Ч�����д���
	*@param
	*@return
	*/
	public static function queryUserState($arrInput , $ie = 'utf-8')
	{
		$req = array(
			'key' => intval($arrInput['key']),//user_id
			'forum_id' => $arrInput['forum_id'],//forum_id
			'service_type' => $arrInput['service_type'],
            'req_source'=> $arrInput['req_source'],
            'extra' => $arrInput['extra'],
		);

		$ret = Tieba_Service::call('userstate', 'queryUserState', $req ,null ,null, 'post', 'php', $ie);
		if( false === $ret || (!isset($ret['errno'])) || 0!==intval($ret['errno']) )
		{
			Bingo_Log::warning('ERR[call service failed] IN['.__FUNCTION__.'] CALL[queryUserState] REQ['.serialize($req).'] RES['.serialize($ret).']');
			return false;
		}
		/*
		 * ������֤�븽���ѯ
		 * ����û�û�б���֤�븽���� output�᷵��false �������Ϊ���� array()
		 */
		if( isset($ret['output']) && false == $ret['output'] ){
			return array();
		}
		
		return $ret['output'];
	}
 	/**
	*��ѯ��ǰ��Ч�����д���
	*@param
	*@return
	*/
	public static function tbmaskQuery($arrInput)
	{
		$userId = intval($arrInput['user_id']);
		$req = array(
			'req' => array(
				'id_list' => array(
					0 => $userId,
				),
				'mask_list' => array(
					0 => 'anti_browse',
				),
				'strMethod' =>	'tbmask_query',
			),
		);
		
		$ret = Tieba_Service::call('anti', 'antiTbmaskQuery', $req );
		
		if( false === $ret || (!isset($ret['errno'])) || 0!==intval($ret['errno']) )
		{
			Bingo_Log::warning('ERR[call service failed] IN['.__FUNCTION__.'] CALL[antiTbmaskQuery] REQ['.serialize($req).'] RES['.serialize($ret).']');
			return false;
		}
		
		return $ret['res'];
	}
}
