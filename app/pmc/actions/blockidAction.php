<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2013-04-17 21:12:10
 */
class blockidAction extends Util_BaseAction {
	public function init() {
		if (! parent::init ()) {
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_UNKOWN;
			$this->_renderJson ();
			return false;
		}
		return true;
	}
	protected function _process() {
		if (! $this->_bolUserLogin) {
			Bingo_Log::warning ( 'the user do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
			$this->_renderJson ();
			return false;
		}
		
		$fid = Bingo_Http_Request::get ( "fid" );
		$tbs = Bingo_Http_Request::get ( 'tbs', '' );
		$unames = Bingo_Http_Request::get ( 'user_name', array () );
		$day = Bingo_Http_Request::get ( "day", 1 );
		$isBlockIp = Bingo_Http_Request::get ( "ip", 0 );
		$pid = Bingo_Http_Request::get ( "pid", 0 );
		$reason = Bingo_Http_Request::get ( "reason", '' );
		
		if (! Tieba_Tbs::check ( $tbs, true )) {
			$this->_intErrorNo = Util_Errno::$invalidTbs;
			$this->_renderJson ();
			return true;
		}
		
		//���ɲ���Ϊ��
		if ($reason == '' || $fid <= 0) {
			$this->_intErrorNo = Util_Errno::$paramError;
			$this->_renderJson ();
			return true;
		}
		//�����10��
		if ($day > 10) {
			$this->_intErrorNo = Util_Errno::$punishDayLimit;
			$this->_renderJson ();
			return true;
		}
		
		$permInput = array (
				'forum_id' => $fid, 
				'user_id' => $this->_intUid, 
				'user_ip' => $this->_intUip );
		$arrOut = Util_Perm::getUserPerm ( $permInput );
		$role = 'none';
		if ($arrOut ['can_type3_audit_post'])
			$role = 'admin';
		elseif ($arrOut ['can_type2_audit_post'])
			$role = 'manager';
		elseif ($arrOut ['can_type1_audit_post'])
			$role = 'assist';
		$needMemo = 1;
		if ($role == 'admin')
			$needMemo = 0;
			//С������һ��
		if ($role == 'none' || ($role == 'assist' && $day > 1) || ($role == 'assist' && $isBlockIp == 1)) {
			$this->_intErrorNo = Util_Errno::$authFail;
			$this->_renderJson ();
			return true;
		}
		
		$users = array ();
		$userInput = array ('user_name' => $unames );
		$userRet = Tieba_Service::call ( 'user', 'getUidByUnames', $userInput );
		if ($userRet ['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( 'call service_user::getUidByUnames failed, input:' . serialize ( $userInput ) . " output:" . serialize ( $userRet ) );
		}
		foreach ( $userRet ['output'] ['uids'] as $u ) {
			$users [] = $u;
		}
		if (count ( $users ) == 0) {
			$this->_intErrorNo = Util_Errno::$userNotExist;
			$this->_renderJson ();
			return true;
		}
		//���ipֻ�ܶ�һ���û�
		if (count ( $users ) > 1 && $isBlockIp == 1) {
			$this->_intErrorNo = Util_Errno::$singleIpLimit;
			$this->_renderJson ();
			return true;
		}
		
		foreach ( $users as $user ) {
			$blockIdRet = Util_User::forumBanId ( $fid, $user ['user_id'], $day, $user ['user_name'], $this->_intUid, $this->_strUname, 0, $reason, $needMemo );
			if ($blockIdRet === false) {
				$this->_intErrorNo = Util_Errno::$systemFailed;
				$this->_renderJson ();
				return true;
			}
		}
		if ($isBlockIp == 1) {
			$postInput = array ('post_ids' => array ($pid ) );
			$postOutput = Tieba_Service::call ( 'post', 'getPostInfo', $postInput );
			$postIp = $postOutput ['output'] [0] ['ip'];
			if ($postOutput ['errno'] != Tieba_Errcode::ERR_SUCCESS) {
				Bingo_Log::warning ( 'call service_post::getPostInfo failed, input:' . serialize ( $postInput ) . " output:" . serialize ( $postOutput ) );
				$this->_intErrorNo = Util_Errno::$systemFailed;
				$this->_renderJson ();
				return true;
			}
			if ($postIp > 0) {
				$blockIpRet = Util_User::forumBanIp ( $fid, $users [0] ['user_id'], $day, $users [0] ['user_name'], $this->_intUid, $this->_strUname, $postIp, $reason, $needMemo );
				if ($blockIpRet === false) {
					$this->_intErrorNo = Util_Errno::$systemFailed;
					$this->_renderJson ();
					return true;
				}
			}
		}
		
		$this->_intErrorNo = 0;
		$this->_renderJson ();
		return true;
	}
}
?>