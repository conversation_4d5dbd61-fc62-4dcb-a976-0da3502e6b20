<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-04-17 21:12:10
 */
class userthreadinfoAction extends Util_BaseAction {

	public function init() {
		if (! parent::init ()) {
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			return false;
		}
		return true;
	}

	protected function _checkParams($arrInput) {
		$cur_page = $arrInput ['cur_page'];
		if ($cur_page <= 0) {
			Bingo_Log::warning ( 'cur_page is ' . $cur_page );
			return false;
		}
		$page_num = $arrInput ['page_num'];
		if ($page_num <= 0) {
			Bingo_Log::warning ( 'page_num is ' . $page_num );
			return false;
		}
		$by_who = $arrInput ['by_who'];
		if ($by_who != 'bawu' && $by_who != 'system' && $by_who != 'louzhu' && $by_who != 'self') {
			Bingo_Log::warning ( 'by_who is ' . $by_who );
			return false;
		}
		return true;
	}
	
	protected function _process() {
		Tieba_Stlog::addNode ( 'service_type', 'recycle' );
		if (! $this->_bolUserLogin) {
			$this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
			$this->_renderJson ();
			return false;
		}
		
		$cur_page = ( int ) Bingo_Http_Request::get ( "curpage" );
		$page_num = ( int ) Bingo_Http_Request::get ( "pagenum" );
		$by_who = Bingo_Http_Request::get ( "bywho" );
		$input = compact('cur_page','page_num','by_who');
		if ( false === $this->_checkParams ( $input ) ) {
			Bingo_Log::warning ( __CLASS__ . ': Check param error!' . serialize(Bingo_Http_Request::getParams ()) );
			$this->_intErrorNo = Tieba_Errcode::ERR_PARAM_ERROR;
			return $this->_pageRet();
		}
		
		$input ['user_id'] = $this->_intUid;
		$input ['user_name'] = $this->_strUname;
		$mixPageRet = Page_Post_Recycle::build($input);
		if ( true !== $mixPageRet ) {
			$this->_intErrorNo = $mixPageRet;
			return $this->_pageRet();
		}
		
		return $this->_pageRet();
	}
	
	private function _pageRet(){
		$this->_arrTplVar['errno']  = $this->_intErrorNo;
		$this->_arrTplVar['errmsg'] = Util_Errno::getErrmsg ( $this->_intErrorNo );
		$this->_renderJson ();
		return true;
	}	
	
}

?>
