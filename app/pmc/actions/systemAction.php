<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2013-04-17 21:12:10
 */

class systemAction extends Util_BaseAction {
	
	public function init() {
		if (! parent::init ()) {
			Tieba_Stlog::addNode ( 'service_type', 'browser' );
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			return false;
		}
		if (! parent::geneTbs ()) {
			Tieba_Stlog::addNode ( 'service_type', 'browser' );
			Bingo_Log::warning ( __CLASS__ . ':gen tbs error' );
			return false;
		}
		$this->_strTemplate = 'pmc_main.php';
		return true;
	}
	
	protected function _checkUserStatus($type, $forumId) {
		$arrInput = array (
				'type' => $type, 
				'forum_id' => $forumId, 
				'user_id' => $this->_intUid );
		$srvInput = array (
				'input' => Bingo_String::array2json ( $arrInput ), 
				'format' => 'json' );
		$srvRet = Service_Pmc::mainPageCheckUser ( $srvInput );
		// Bingo_Log::debug ( var_export ( $srvRet, true ) );
		if ($srvRet ['errno'] === Util_Errno::$errPmcRederict || $srvRet ['ret'] === false) {
			return false;
		}
		return true;
	}
	
	private function _resetPwdRemind ( ) {
		$appealCode = Bingo_Http_Request::get ( "appealcode", '' );
		$msgCode = (int) Bingo_Http_Request::get ( "status", 0 );
		if ( !empty($appealCode) && array_key_exists($msgCode, Util_Const::getResetPwdRemindMap()) ) {
			$arrInput = array('input'=>array('appeal_code'=>$appealCode, 'user_id'=>$this->_intUid));
			$srvRet = Service_Pmc::getResetPwdTaskByUserIdAndAppealCode($arrInput);
			$arrTaskId = array();
			if ( $srvRet['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
				Bingo_Log::warning( __FUNCTION__ . ': call getResetPwdTaskByUserIdAndAppealCode error [res]' . serialize($srvRet) );
			} elseif ( !empty($srvRet['ret']) ) {
				foreach ( $srvRet['ret'] as $arrTask ) {
					array_push($arrTaskId, intval($arrTask['task_id']));
				}
			}
			
			if ( !empty($arrTaskId) || $msgCode === Util_Const::$resetSignInvalid ) {
				$arrMap = Util_Const::getResetPwdRemindMap();
				$arrRemind = array(
					'status'	=>  $msgCode,
					'msg'		=> $arrMap[$msgCode]
				);
				$this->_arrTpl['remind'] = $arrRemind;
			}
			
			$srvUpRet = Service_Pmc::updateResetPwdTaskReadedByUidAndAppealCode($arrInput);
			if ( $srvUpRet['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
				Bingo_Log::warning( __FUNCTION__ . 'call updateResetPwdTaskReadedByUidAndAppealCode failed [res]' . serialize($srvUpRet) );
			}
		}
		return true;
	}
	
	protected function _process() {
		Tieba_Stlog::addNode ( 'service_type', 'main' );
		if (! $this->_bolUserLogin) {
			Bingo_Log::warning ( __CLASS__ . ':The user do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
			header ( 'Location:' . 'https://passport.baidu.com/v2/?login' );
		}
		
		$type = Bingo_Http_Request::get ( "type" );
		$forumId = Bingo_Http_Request::get ( "fid" );
		
		$this->_resetPwdRemind();
		
		/*
		if (! is_null ( $type ) || ! is_null ( $forumId )) {
			if (is_null ( $type )) {
				$type = Util_Const::$upcPunishTypeBan;
			}
			$params [] .= 'type=' . $type;
			if (! is_null ( $forumId )) {
				$params [] .= 'fid=' . $forumId;
			}
			$strParams = '?' . implode ( '&', $params );
			if (false === $this->_checkUserStatus ( $type, $forumId )) {
				Bingo_Log::warning ( __CLASS__ . ': this user must go to upc module now!' );
				header ( 'Location:' . '/upc/userinfo' . $strParams );
			}
		}
		*/
		
		$srvInput = array (
				'input' => Bingo_String::array2json ( array (
						'user_id' => $this->_intUid ) ), 
				'format' => 'json' );
		$srvRet = Service_Pmc::getMainPageInfo ( $srvInput );
		if ($srvRet ['errno'] !== Tieba_Errcode::ERR_SUCCESS || $srvRet ['ret'] === false) {
			Bingo_Log::warning ( __CLASS__ . ': get main page info error!' );
			$this->_intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
			$this->_renderView ();
			return false;
		}
		$arrInput = array ();
		$arrInput ['punish_type'] = 0;
		$messageCount = 0;
		if ($srvRet ['ret'] ['isBan'] > 0) {
			$messageCount ++;
			$arrInput ['message'] = Util_Const::$alertMessageBan;
		}
		if ($srvRet ['ret'] ['isMask'] > 0) {
			$messageCount ++;
			$arrInput ['message'] = $arrInput ['message'] . "<br>" . Util_Const::$alertMessageMask;
		}
		if ($srvRet ['ret'] ['isValve'] > 0) {
			$messageCount ++;
			$arrInput ['message'] = $arrInput ['message'] . "<br>" . Util_Const::$alertMessageValue;
		}
		if ($srvRet ['ret'] ['isBanAndMask'] > 0) {
			$messageCount ++;
			$arrInput ['message'] = $arrInput ['message'] . "<br>" . Util_Const::$alertMessageBanAndMask;
		}
		if($messageCount === 0){
			$arrInput ['message'] = Util_Const::$alertMessageNomal;
		}elseif($messageCount >= 2){
			$arrInput ['message'] = Util_Const::$alertMessageMerge;
		}
                $unappealInput=array(
                    'user_id'=>$this->_intUid,
                    'is_bawu'=>1);
                $bawuUnAppeal=  Service_Pmc::getUnAppealMsgCount($unappealInput);
                $unappealInput['is_bawu']=0;
                $systemUnAppeal=Service_Pmc::getUnAppealMsgCount($unappealInput);
                $arrInput['system_alert']=$systemUnAppeal['ret']['msgCt'];
                $arrInput['bawu_alert']=$bawuUnAppeal['ret']['msgCt'];
		$arrInput ['message'] = trim($arrInput ['message'],'<br>');
		$arrInput ['unread_count'] = $srvRet ['ret'] ['unReadCt'];
		$arrInput ['appealling_count'] = $srvRet ['ret'] ['appealCt'];
                $this->_arrTpl = array_merge ( $this->_arrTpl, $arrInput );
		$this->_intErrorNo = Tieba_Errcode::ERR_SUCCESS;
		$this->_renderView ();
		return true;
	}
}
?>
