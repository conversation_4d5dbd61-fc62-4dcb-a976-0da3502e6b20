<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2013-04-17 21:12:10
 */
class checkvcodeAction extends Util_BaseAction {
	
	public function init() {
		if (! parent::init ()) {
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_UNKOWN;
			$this->_renderJson ();
			return false;
		}
		return true;
	}
	protected function _checkParams($arrInput) {
		$vcode = $arrInput ['vcode'];
		if (is_null ( $vcode ) || (strlen ( $vcode ) != 6) || $vcode === '' || ! is_numeric ( $vcode )) {
			return false;
		}
		return true;
	}
	
	protected function _checkUserStatus($msgId) {
		$arrInput = array ('user_id' => $this->_intUid, 'msg_id' => $msgId );
		$srvInput = array (
				'input' => Bingo_String::array2json ( $arrInput ), 
				'format' => 'json' );
		$srvRet = Service_Pmc::mobileCheckUser ( $srvInput );
		
		if ($srvRet ['errno'] !== Tieba_Errcode::ERR_SUCCESS || $srvRet ['ret'] === false) {
			return false;
		}
		return true;
	}
	
	protected function _process() {
		if (! $this->_bolUserLogin) {
			Bingo_Log::warning ( 'the use do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
			$this->_renderJson ();
			return false;
		}
		
		$msgId = Bingo_Http_Request::get ( "msgid" );
		$vcode = Bingo_Http_Request::get ( "vcode", '' );
		$arrInput = array ('msgid' => $msgId, 'vcode' => $vcode );
		if (false === $this->_checkParams ( $arrInput )) {
			Bingo_Log::warning ( __CLASS__ . ': Check vcode param error!' );
			$this->_intErrorNo = Util_Errno::$errPhoneVcodeWrong;
			$this->_renderJson ();
			return false;
		}
		
		if (false === $this->_checkUserStatus ( $msgId )) {
			Bingo_Log::warning ( __CLASS__ . ': this user can not mobile appeal now.[' . $this->_strUname . ']' );
			$this->_intErrorNo = Util_Errno::$errUserCannotMobileAppeal;
			$this->_renderJson ();
			return false;
		}
		
		$result = Util_User::checkPhoneVcode ( $vcode );
		if ($result !== Util_Errno::$errPhoneSuccess) {
			$this->_intErrorNo = Util_Errno::$errPhoneVcodeWrong;
			$this->_renderJson ();
			return false;
		}
		
		$srvInput = array (
				'input' => Bingo_String::array2json ( array (
						'user_id' => $this->_intUid, 
						'msg_id' => $msgId ) ), 
				'format' => 'json' );
		$srvResult = Service_Pmc::submitMobileAppeal ( $srvInput );
		
		if ($srvResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS || $srvResult ['ret'] === false) {
			Bingo_Log::warning ( __CLASS__ . ': submit mobile appeal task error!' );
			$this->_intErrorNo = $srvResult ['errno'];
			$this->_renderJson ();
			return false;
		
		}
		$actsStatus = $srvResult ['ret'] ['actsStatus'];
		$this->_arrTpl ['as'] = $actsStatus;
		$this->_intErrorNo = Tieba_Errcode::ERR_SUCCESS;
		$this->_renderJson ();
		return true;
	}

}
?>