<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2013-04-17 21:12:10
 */

class appealAction extends Util_BaseAction {
	
	public function init() {
		if (! parent::init ()) {
			Tieba_Stlog::addNode ( 'service_type', 'browser' );
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			return false;
		}
		if (! parent::geneTbs ()) {
			Tieba_Stlog::addNode ( 'service_type', 'browser' );
			Bingo_Log::warning ( __CLASS__ . ':gen tbs error' );
			return false;
		}
		$this->_strTemplate = 'pmc_appeal.php';
		return true;
	}
	protected function _checkParams($arrInput) {
		return is_numeric ( $arrInput ['msg_id'] ) && $arrInput ['msg_id'] > 0;
	}
	protected function _process() {
		Tieba_Stlog::addNode ( 'service_type', 'page' );
		if (! $this->_bolUserLogin) {
			Bingo_Log::warning ( __CLASS__ . ':The user do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
			header ( 'Location:' . 'https://passport.baidu.com/v2/?login' );
		}
		
		$msgId = Bingo_Http_Request::get ( "msgid" );
		if (false === $this->_checkParams ( array ('msg_id' => $msgId ) )) {
			Bingo_Log::warning ( __CLASS__ . ': Check param error!' );
			$this->_intErrorNo = Tieba_Errcode::ERR_PARAM_ERROR;
			$this->_renderView ();
			return false;
		}
		
		$arrInput = array ();
		$srvInput = array (
				'input' => Bingo_String::array2json ( array (
						'msg_id' => $msgId, 
						'user_id' => $this->_intUid, 
						'get_appeal_auth' => true ) ), 
				'format' => 'json' );
		
		$srvResult = Service_Pmc::getAppealMsgByMsgIdAndUserId ( $srvInput );
		
		if ($srvResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS || $srvResult ['ret'] === false) {
			Bingo_Log::warning ( __CLASS__ . ': get msg service error, errno: ' . $srvResult ['errno'] );
			$this->_intErrorNo = $srvResult ['errno'];
			$this->_renderView ();
			return false;
		}
		
		$arrInput ['punish_info'] ['punish_type'] = $srvResult ['ret'] ['punish_type'];
		$arrInput ['punish_info'] ['limits'] = $srvResult ['ret'] ['limits'];
		$arrInput ['punish_info'] ['punish_start_time'] = $srvResult ['ret'] ['punish_start_time'];
		$arrInput ['punish_info'] ['punish_day_num'] = $srvResult ['ret'] ['punish_day_num'];
		$arrInput ['appeal_auth'] = $srvResult ['ret'] ['appeal_auth'];
		$arrInput ['mobile_left_ct'] = $srvResult ['ret'] ['mobile_left_ct'];
		$arrInput ['mark_left_ct'] = $srvResult ['ret'] ['mark_left_ct'];
		
		$this->_arrTpl = array_merge ( $this->_arrTpl, $arrInput );
// 		Bingo_Log::debug ( var_export ( $this->_arrTpl, true ) );
		$this->_intErrorNo = Tieba_Errcode::ERR_SUCCESS;
		$this->_renderView ();
		return true;
	}
}
?>