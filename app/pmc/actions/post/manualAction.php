<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @abstract ����վ���� �˹�����ָ�
 * 
 */

class manualAction extends Util_BaseAction {
	
	public function init() {
		if (! parent::init ()) {
			Tieba_Stlog::addNode ( 'service_type', 'manual' );
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			return false;
		}
		$this->_strTemplate = 'by_manual.php';
		return true;
	}
	
	protected function _process() {
		Tieba_Stlog::addNode ( 'service_type', 'page' );
		if (! $this->_bolUserLogin) {
			Bingo_Log::warning ( __CLASS__ . ':The user do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
			header ( 'Location:' . 'https://passport.baidu.com/v2/?login' );
		}
		
		$postId		= Bingo_Http_Request::get ( "pid" );
		$threadId	= Bingo_Http_Request::get ( "tid" );
		$isMobile = intval(Bingo_Http_Request::get ( "is_mobile" ));//add by tanzheng, �����ƶ���������Ҫ
		//����У��
		if (false === Page_Post_Restore::checkParams ( array ('pid' => $postId, 'tid' => $threadId) )) {
			Bingo_Log::warning ( __CLASS__ . ': Check param error!' );
			$this->_intErrorNo = Tieba_Errcode::ERR_PARAM_ERROR;
			if(empty($isMobile)) {
				$this->_renderView ();
			}
			else {
				$this->_renderJson();
			}
			return false;
		}
		
		//����Ϸ���
		$res = Page_Post_Restore::checkIsLegalAndGetRealOpUsertype ( array ('pid' => $postId, 'tid' => $threadId, 'user_id'=>$this->_intUid ) );
		if (false === $res) {
			Bingo_Log::warning ( __CLASS__ . ': post restore by Manual is illegal.[' . $this->_strUname . ']' );
			$this->_intErrorNo = Util_Errno::$errRestorePostIllegal;
			if(empty($isMobile)) {
				$this->_renderView ();
			}
			else {
				$this->_renderJson();
			}
			return false;
		}
		//todo ���м�İ�

        $strTbs = Tieba_Tbs::gene( $this->_bolUserLogin );
        $this->_arrTpl ['tbs'] = $strTbs;
		$this->_arrTpl ['pid'] = $postId;
		$this->_arrTpl ['tid'] = $threadId;
		$this->_arrTpl ['op_user_type'] = $res;
		$this->_intErrorNo = Tieba_Errcode::ERR_SUCCESS;
		if(empty($isMobile)) {
			$this->_renderView ();
		}
		else {
			$this->_renderJson();
		}
		return true;
	}
}
?>