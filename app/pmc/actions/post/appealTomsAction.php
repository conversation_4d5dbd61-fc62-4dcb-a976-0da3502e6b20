<?php
class appealTomsAction extends Util_BaseAction {
    /**
     * @brief 
     *
     * @return 
     */
    public function init() {
        if (! parent::init ()) {
            Tieba_Stlog::addNode ( 'service_type', 'browser' );
            Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
            return false;
        }
        if (! parent::geneTbs ()) {
            Tieba_Stlog::addNode ( 'service_type', 'browser' );
            Bingo_Log::warning ( __CLASS__ . ':gen tbs error' );
            return false;
        }
        $this->_strTemplate = 'other_del_post.php';
        return true;
    }

    /**
     * @brief 
     *
     * @param $arrInput
     *
     * @return 
     */
    protected function _checkParams($arrInput) {
        if (!is_numeric ( $arrInput ['pid'] ) || $arrInput ['pid'] < 0) {
            return false;
        }
        if (!is_numeric ( $arrInput ['tid'] ) || $arrInput ['tid'] < 0) {
            return false;
        }
        return true;
    }

    /**
     * @brief 
     *
     * @return 
     */
    protected function _process() {
        Tieba_Stlog::addNode ( 'service_type', 'page' );
        if (! $this->_bolUserLogin) {
            Bingo_Log::warning ( __CLASS__ . ':The user do not login.' );
            $this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
            header ( 'Location:' . 'https://passport.baidu.com/v2/?login' );
        }

        $postId = Bingo_Http_Request::get("pid");
        $threadId = Bingo_Http_Request::get("tid");
        $forumId = Bingo_Http_Request::get("fid");
        $applicationId = Bingo_Http_Request::get("application_id");

        if (false === $this->_checkParams ( array ('pid' => $postId, 'tid' => $threadId ) )) {
            Bingo_Log::warning ( __CLASS__ . ': Check param error!' );
            $this->_intErrorNo = Tieba_Errcode::ERR_PARAM_ERROR;
            $this->_renderView ();
            return false;
        }

        $this->_arrTpl['pid'] = $postId;
        $this->_arrTpl['tid'] = $threadId;
        $this->_arrTpl['fid'] = $forumId;
        $this->_arrTpl['application_id'] = $applicationId;

        $user = array(
            'id' => $this->_intUid,
            'name' => $this->_strUname,
            'is_login' => true,	
        );
        $this->_arrTpl['user'] = $user;
        $this->_intErrorNo = Tieba_Errcode::ERR_SUCCESS;
        $this->_renderView ();
        return true;
    }
}
?>
