<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @abstract ����վ���� ��ͼ����ָ�
 * 
 */

class imgAction extends Util_BaseAction {
	
	public function init() {
		if (! parent::init ()) {
			Tieba_Stlog::addNode ( 'service_type', 'browser' );
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			return false;
		}
		$this->_strTemplate = 'by_markimg.php';
		return true;
	}
	
	protected function _process() {
		Tieba_Stlog::addNode ( 'service_type', 'page' );
		if (! $this->_bolUserLogin) {
			Bingo_Log::warning ( __CLASS__ . ':The user do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
			header ( 'Location:' . 'https://passport.baidu.com/v2/?login' );
		}
		
		$postId		= Bingo_Http_Request::get ( "pid" );
		$threadId	= Bingo_Http_Request::get ( "tid" );

		//����У��
		if (false === Page_Post_Restore::checkParams ( array ('pid' => $postId, 'tid' => $threadId) )) {
			Bingo_Log::warning ( __CLASS__ . ': Check param error!' );
			$this->_intErrorNo = Tieba_Errcode::ERR_PARAM_ERROR;
			$this->_renderView ();
			return false;
		}
		
		//�ų���24���ɵ�����
		$mixRet = Page_Post_Restore::isRestoreAllowManualOnly(array ('pid' => $postId, 'tid' => $threadId));
		if (is_numeric($mixRet) ) {
			Bingo_Log::warning ( __CLASS__ . ': Check isRestoreAllowManualOnly error!' );
			$this->_intErrorNo = $mixRet;
			$this->_renderJson ();
			return false;
		}
		if ( true === $mixRet || Util_Buffet::isPostMarkImgDeny($this->_intUid) ) {
			Bingo_Log::warning ( __CLASS__ . ': Check isRestoreAllowManualOnly error!' );
			$this->_intErrorNo = Util_Errno::$errRestorePostIllegal;
			$this->_renderJson ();
			return false;
		}
		
		
		
		//�������û��ȼ����
		$maxUserLevel = Util_User::getUserMaxLevelByUid (intval($this->_intUid) );
		if ($maxUserLevel === false || $maxUserLevel < Util_Const::$userMarkImgGradeLimit) {
			$this->_intErrorNo = Util_Errno::$errGradeLack;
			$this->_renderView();
			return false;
		}
		
		//����Ϸ���
		if (false === ($intOpUserType = Page_Post_Restore::checkIsLegalAndGetOpUsertype ( array ('pid' => $postId, 'tid' => $threadId, 'user_id'=>$this->_intUid )) )) {
			Bingo_Log::warning ( __CLASS__ . ': post restore by phone is illegal.[' . $this->_strUname . ']' );
			$this->_intErrorNo = Util_Errno::$errRestorePostIllegal;
			$this->_renderView ();
			return false;
		}
		
		//����Ƿ��ǻ���ɾ��
		if ( false === Page_Post_Restore::isOpUserMachine($intOpUserType) ) {
			Bingo_Log::warning ( __CLASS__ . ': post restore by phone is illegal due to opUserType is not machine.[' . $this->_strUname . ']' );
			$this->_intErrorNo = Util_Errno::$errRestorePostIllegal;
			$this->_renderView ();
			return false;
		}
		
		//�������
		$actsRet = Util_User::queryActsCtrl ( ( int ) $this->_intUid, Util_Const::$actsCtrCmdRestorePostByMarkImg );
		if ( $actsRet === 0 ) {
			Bingo_Log::warning ( __CLASS__ . ': this user can not use mark img restore post now.[' . $this->_strUname . ']' );
			$this->_intErrorNo = Util_Errno::$errUserCannotMarkImgAppeal;
			$this->_renderView ();
			return false;
		}
		
		//@todo ���м�İ�
		
		
		$this->_arrTpl ['post_id'] = $postId;
		$this->_arrTpl ['thread_id'] = $threadId;
		$this->_intErrorNo = Tieba_Errcode::ERR_SUCCESS;
		$this->_renderView ();
		return true;
	}
	
}
?>