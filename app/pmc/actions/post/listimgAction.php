<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @abstract ����վ���� ��ͼ����ָ�
 * 
 */

class listimgAction extends Util_BaseAction {
	public function init() {
		if (! parent::init ()) {
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			return false;
		}
		return true;
	}
	
	protected function _process() {
		Tieba_Stlog::addNode ( 'service_type', 'listimg' );
		if (! $this->_bolUserLogin) {
			Bingo_Log::warning ( 'the use do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
			$this->_renderJson ();
			return false;
		}
		
		$postId		= Bingo_Http_Request::get ( "pid" );
		$threadId	= Bingo_Http_Request::get ( "tid" );
	
		//�������û��ȼ����
		$maxUserLevel = Util_User::getUserMaxLevelByUid (intval($this->_intUid) );
		if ($maxUserLevel === false || $maxUserLevel < Util_Const::$userMarkImgGradeLimit) {
			$this->_intErrorNo = Util_Errno::$errGradeLack;
			$this->_renderJson ();
			return false;
		}
		
		//�ų���24���ɵ�����
		$mixRet = Page_Post_Restore::isRestoreAllowManualOnly(array ('pid' => $postId, 'tid' => $threadId));
		if (is_numeric($mixRet) ) {
			Bingo_Log::warning ( __CLASS__ . ': Check isRestoreAllowManualOnly error!' );
			$this->_intErrorNo = $mixRet;
			$this->_renderJson ();
			return false;
		}
		if ( true === $mixRet || Util_Buffet::isPostMarkImgDeny($this->_intUid) ) {
			Bingo_Log::warning ( __CLASS__ . ': Check isRestoreAllowManualOnly error!' );
			$this->_intErrorNo = Util_Errno::$errRestorePostIllegal;
			$this->_renderJson ();
			return false;
		}
		
		//����Ϸ���
		if (false === ($intOpUserType = Page_Post_Restore::checkIsLegalAndGetOpUsertype ( array ('pid' => $postId, 'tid' => $threadId, 'user_id'=>$this->_intUid )) )) {
			Bingo_Log::warning ( __CLASS__ . ': post restore by phone is illegal.[' . $this->_strUname . ']' );
			$this->_intErrorNo = Util_Errno::$errRestorePostIllegal;
			$this->_renderJson ();
			return false;
		}
		
		//����Ƿ��ǻ���ɾ��
		if ( false === Page_Post_Restore::isOpUserMachine($intOpUserType) ) {
			Bingo_Log::warning ( __CLASS__ . ': post restore by phone is illegal due to opUserType is not machine.[' . $this->_strUname . ']' );
			$this->_intErrorNo = Util_Errno::$errRestorePostIllegal;
			$this->_renderJson ();
			return false;
		}
		
		//�������
		$actsRet = Util_User::queryActsCtrl ( ( int ) $this->_intUid, Util_Const::$actsCtrCmdRestorePostByMarkImg );
		if ( $actsRet === 0 ) {
			Bingo_Log::warning ( __CLASS__ . ': this user can not use mark img restore post now.[' . $this->_strUname . ']' );
			$this->_intErrorNo = Util_Errno::$errUserCannotMarkImgAppeal;
			$this->_renderJson ();
			return false;
		}
		
		$srvInput = array (
				'input' => Bingo_String::array2json ( array (
						'user_id' => $this->_intUid, 
						'pid' => $postId,
						'tid'=> $threadId) ), 
				'format' => 'json' );
		$newTask = Service_Recycle_Post::getUserHasNoFinishedMarkTask ( $srvInput );
		
		if ($newTask ['errno'] !== Tieba_Errcode::ERR_SUCCESS || $newTask ['ret'] === false) {
			Bingo_Log::notice ( __CLASS__ . ': has no unfinished task found!' );
			$newTask = Service_Recycle_Post::getImageList ( $srvInput );
			if ($newTask ['errno'] !== Tieba_Errcode::ERR_SUCCESS || $newTask ['ret'] === false) {
				Bingo_Log::warning ( __CLASS__ . ': Add new task and get images error!' );
				$this->_intErrorNo = $newTask ['errno'];
				$this->_renderJson ();
				return false;
			}
		}
		
		foreach ( $newTask ['ret'] ['images'] as &$image ) {
			unset ( $image ['base_type'] );
		}
		
		shuffle ( $newTask ['ret'] ['images'] );
		$this->_arrTpl ['post_id'] = $postId;
		$this->_arrTpl ['thread_id'] = $threadId;
		$this->_arrTpl ['taskid'] = $newTask ['ret'] ['taskId'];
		$this->_arrTpl ['data'] = $newTask ['ret'] ['images'];
		$this->_arrTpl ['bc'] = $newTask ['ret'] ['blackCount'];
		
		$this->_intErrorNo = Tieba_Errcode::ERR_SUCCESS;
		$this->_renderJson ();
		return true;
	}

}
?>