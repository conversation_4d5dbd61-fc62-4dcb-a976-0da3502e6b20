<?php

class indexAction extends Util_BaseAction{
    private static $_arrCate = array(
        '1' => '同城交友',
        '2' => '美食天地',
        '3' => '母婴亲子',
        '4' => '业主家园',
        '5' => '车行天下',
        '6' => '文体娱乐',
        '7' => '人在职场',
        '8' => '社会百态',
        '9' => '家庭情感',
        '10' => '旅游摄影',
        '11' => '教育培训',
        '12' => '家居装饰',
        '13' => '美容健身',
        '14' => '宠物乐园',
        '15' => '投资理财',
        '16' => '生活杂谈',
    );
	public function init() {
		if (! parent::init ()) {
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			return false;
		}
		if (! parent::geneTbs ()) {
			Tieba_Stlog::addNode ( 'service_type', 'browser' );
			Bingo_Log::warning ( __CLASS__ . ':gen tbs error' );
			return false;
		}
		$this->_strTemplate = 'cate_appeal.php';
		return true;
	}

	protected function _process() {
		if (! $this->_bolUserLogin) {
			Bingo_Log::warning ( __CLASS__ . ':The user do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
			header ( 'Location:' . 'https://passport.baidu.com/v2/?login' );
		}
		if (is_utf8(self::$_arrCate['1'])) {
			self::$_arrCate = Bingo_Encode::convert(self::$_arrCate, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
		}
		$arrOut = array();
		foreach (self::$_arrCate as $strKey=>$arrCate) {
		    $arrRow = array();
			$arrRow['second_class_id'] = intval($strKey);
			$arrRow['second_class_name'] = $arrCate;
			$arrOut['categories_info'][] = $arrRow;
		}
		$this->_arrTpl = array_merge($this->_arrTpl, $arrOut);
		$this->_intErrorNo = Tieba_Errcode::ERR_SUCCESS;
		$this->_renderView ();
		return true;
	}
}
