<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2013-04-17 21:12:10
 */
class checkbindAction extends Util_BaseAction {
	
	public function init() {
		if (! parent::init ()) {
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			return false;
		}
		return true;
	}
	
	protected function _process() {
		if (! $this->_bolUserLogin) {
			Bingo_Log::warning ( 'the use do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
			$this->_renderJson ();
			return false;
		}
		header ( 'location:http://static.tieba.baidu.com/tb/error.html' );
	}

}
?>