<?php

class jubaoAction extends Util_BaseAction{

	public function init() {
		if (! parent::init ()) {
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			return false;
		}
		if (! parent::geneTbs ()) {
			Tieba_Stlog::addNode ( 'service_type', 'browser' );
			Bingo_Log::warning ( __CLASS__ . ':gen tbs error' );
			return false;
		}
		$this->_strTemplate = 'list_report.php';
		return true;
	}

	protected function _process() {
		if (! $this->_bolUserLogin) {
			Bingo_Log::warning ( __CLASS__ . ':The user do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
			header ( 'Location:' . 'https://passport.baidu.com/v2/?login' );
		}
		$this->_intErrorNo = Tieba_Errcode::ERR_SUCCESS;
		$this->_renderView ();
        return true;
	}
	
}

?>
