<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @abstract ��ҳ��������(�ʺ��쳣/�����쳣/��������) ���Ϊ�Ѷ�
 * 
 */
class readAction extends Util_BaseAction {
	
	const REMIND_READ_TYPE_ACCOUNT  = 'account';
	const REMIND_READ_TYPE_M2MB		= 'm2mb';
	const REMIND_READ_TYPE_POST		= 'post';
	
	public function init() {
		if (! parent::init ()) {
			Tieba_Stlog::addNode ( 'service_type', 'browser' );
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			return false;
		}
		if (! parent::geneTbs ()) {
			Tieba_Stlog::addNode ( 'service_type', 'browser' );
			Bingo_Log::warning ( __CLASS__ . ':gen tbs error' );
			return false;
		}
		return true;
	}
	
	protected function _process() {
		Tieba_Stlog::addNode ( 'service_type', 'page' );
		if (! $this->_bolUserLogin) {
			Bingo_Log::warning ( __CLASS__ . ':The user do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
			header ( 'Location:' . 'https://passport.baidu.com/v2/?login' );
		}
		
		$strType = trim(Bingo_Http_Request::get ( "type" ));
		$arrRemindType = array(self::REMIND_READ_TYPE_ACCOUNT, self::REMIND_READ_TYPE_M2MB, self::REMIND_READ_TYPE_POST);
		if ( !is_string($strType) || !in_array($strType, $arrRemindType) ) {
			Bingo_Log::warning ( __CLASS__ . ': ' . Util_Log::$strParamsInvilid . " [type]$strType" );
			$this->_intErrorNo = Tieba_Errcode::ERR_PARAM_ERROR;
			$this->_renderJson();
			return false;
		}
		
		$mixUpdateRead = true;
		$arrInput = array(
			'input'=>Bingo_String::array2json(array('user_id'=>$this->_intUid)),
			'format'=>'json'
		);
		switch ($strType) {
			case self::REMIND_READ_TYPE_ACCOUNT:
				$mixUpdateRead = Service_Remind_Remind::updateAccountExceptionReaded($arrInput);
				break;
			case self::REMIND_READ_TYPE_M2MB:
				$mixUpdateRead = Service_Remind_Remind::updateMask2MaskBlockReaded($arrInput);
				break;
			case self::REMIND_READ_TYPE_POST:
				$mixUpdateRead = Service_Remind_Remind::updatePostExceptionReaded($arrInput);
				break;
			default :
				Bingo_Log::warning( __CLASS__ . "error remind type:" . $strType);
				$mixUpdateRead = false;
				break;
		}
		
		if ( false === $mixUpdateRead || $mixUpdateRead['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
			Bingo_Log::warning( __CLASS__ . 'call update exception read time error:' . serialize($mixUpdateRead) );
			$this->_intErrorNo = Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL;
			$this->_renderJson();
			return false;
		} else if ( $mixUpdateRead['ret'] ) {
			$this->_intErrorNo = Tieba_Errcode::ERR_SUCCESS;
		} else {
			$this->_intErrorNo = Util_Errno::$errInternalError;
		}
		$this->_renderJson ();
		return true;
	}
	
}

