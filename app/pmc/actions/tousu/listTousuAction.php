<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2013-04-17 21:12:10
 */
class listTousuAction extends Util_BaseAction {
	const PAGE_SIZE = 10;
	public function init() {
		if (! parent::init ()) {
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_UNKOWN;
			$this->_renderJson ();
			return false;
		}
		return true;
	}
	protected function _process() {
		if (! $this->_bolUserLogin) {
			Bingo_Log::warning ( 'the use do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
			$this->_renderJson ();
			return false;
		}
		$page = (int)Bingo_Http_Request::get('page', 1);
		$status = Bingo_Http_Request::get('status', '');
		$arrInput = array(
			'complaint_uid' => $this->_intUid,
			'complaint_start_time' => strtotime("-3 month"),
			'offset' => $page > 1? ($page-1) * self::PAGE_SIZE : 0,
			'limit' => self::PAGE_SIZE,		
		);
		if($status === 'undo'){
			$arrInput['status'] = Util_Const::COMPLAINT_STATUS_UN_AUDIT;
		}elseif($status === 'done'){
			$arrInput['status'] = Util_Const::COMPLAINT_STATUS_DONE;
		}else{
			Bingo_Log::warning("status invalid:".$status);
			$this->_intErrorNo = Tieba_Errcode::ERR_PARAM_ERROR;
			$this->_renderJson ();
			return false;
		}
		$res = Service_Tousu_Managercomplaint::getComplaintList($arrInput);
		if(false === $res){
			$this->_intErrorNo = Util_Errno::$getUserComplaintListFailed;
			$this->_renderJson ();
			return false;
		}
		$data = array(
			'list' => $res['data'],
			'page' => $this->_paging($page, $res['total'], self::PAGE_SIZE),
		);
		$this->_arrTpl ['data'] = $data;
		$this->_intErrorNo = Tieba_Errcode::ERR_SUCCESS;
		$this->_renderJson ();
		return true;
	}
	
	protected function _paging($pageNum, $dataTotal, $pageSize = 10) {
		//��ҳ����С�ڵ���0����С�ڵ���0��ʹ��Ĭ�Ϸ�ҳ��30
		$pageSize = intval ( $pageSize );
		if ($pageSize <= 0) {
			$pageSize = 30;
		}
		$total = intval ( $total );
		$pageTotal = ceil ( $dataTotal / $pageSize );
		return array (
				'page_num' => $pageNum,  //��ǰҳ
				'page_size' => $pageSize,  //ÿҳ����
				'page_total' => $pageTotal,  //��ҳ��
				'data_total' => $dataTotal, //������
		); 
	}

}
?>