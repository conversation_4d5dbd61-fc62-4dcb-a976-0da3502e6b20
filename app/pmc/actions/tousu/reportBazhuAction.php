<?php

class reportBazhuAction extends Util_BaseAction{

	public function init() {
		if (! parent::init ()) {
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			return false;
		}
		if (! parent::geneTbs ()) {
			Tieba_Stlog::addNode ( 'service_type', 'browser' );
			Bingo_Log::warning ( __CLASS__ . ':gen tbs error' );
			return false;
		}
		$this->_strTemplate = 'report_bazhu.php';
		return true;
	}

	protected function _process() {
		if (! $this->_bolUserLogin) {
			Bingo_Log::warning ( __CLASS__ . ':The user do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
			header ( 'Location:' . 'https://passport.baidu.com/v2/?login' );
		}
		$arrInput = array(
			'complaint_uid' => $this->_intUid,
			'complaint_start_time' => strtotime("-3 month"),
			'offset' => 0,
			'limit' => 1,		
		);
		$count = array(
			'totalNum' => 0,
			'undoNum' => 0,
			'doneNum' => 0,
		);
		$arrInput['status'] = Util_Const::COMPLAINT_STATUS_UN_AUDIT;
		$res = Service_Tousu_Managercomplaint::getComplaintList($arrInput);
		if(false === $res){
			$this->_intErrorNo = Util_Errno::$getUserComplaintListFailed;
			$this->_arrTpl['count'] = $count;
			$this->_renderView();
			return false;
		}
		$count['undoNum'] = intval($res['total']);
		$arrInput['status'] = Util_Const::COMPLAINT_STATUS_DONE;
		$res = Service_Tousu_Managercomplaint::getComplaintList($arrInput);
		if(false === $res){
			$this->_intErrorNo = Util_Errno::$getUserComplaintListFailed;
			$this->_arrTpl['count'] = $count;
			$this->_renderView();
			return false;
		}
		$count['doneNum'] = intval($res['total']);
		$count['totalNum'] = $count['doneNum'] + $count['undoNum'];
		$this->_arrTpl['count'] = $count;
		$this->_intErrorNo = Tieba_Errcode::ERR_SUCCESS;
		$this->_renderView ();
        return true;
	}
	
}

?>
