<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2013-04-17 21:12:10
 */

class listimgAction extends Util_BaseAction {
	public function init() {
		if (! parent::init ()) {
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			return false;
		}
		return true;
	}
	
	protected function _checkParams($arrInput) {
		$msgId = $arrInput ['msg_id'];
		if (! is_numeric ( $msgId ) || $msgId <= 0) {
			return false;
		}
		return true;
	}
	
	protected function _checkUserStatus($msgId) {
		$arrInput = array ('user_id' => $this->_intUid, 'msg_id' => $msgId );
		$srvInput = array (
				'input' => Bingo_String::array2json ( $arrInput ), 
				'format' => 'json' );
		$srvRet = Service_Pmc::markImgCheckUser ( $srvInput );
		
		if ($srvRet ['errno'] !== Tieba_Errcode::ERR_SUCCESS || $srvRet ['ret'] === false) {
			return false;
		}
		return true;
	}
	
	protected function _process() {
		Tieba_Stlog::addNode ( 'service_type', 'listimg' );
		if (! $this->_bolUserLogin) {
			Bingo_Log::warning ( 'the use do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
			$this->_renderJson ();
			return false;
		}
		
		$msgId = Bingo_Http_Request::get ( "msgid" );
		
		$arrInput = array ('msg_id' => $msgId );
		if (false === $this->_checkParams ( $arrInput )) {
			Bingo_Log::warning ( __CLASS__ . ': Check param error!' );
			$this->_intErrorNo = Tieba_Errcode::ERR_PARAM_ERROR;
			$this->_renderJson ();
			return false;
		}
		
		if (false === $this->_checkUserStatus ( $msgId )) {
			Bingo_Log::warning ( __CLASS__ . ': this user can not mark image appeal now.[' . $this->_strUname . ']' );
			$this->_intErrorNo = Util_Errno::$errUserCannotMarkImgAppeal;
			$this->_renderJson ();
			return false;
		}
		
		$srvInput = array (
				'input' => Bingo_String::array2json ( array (
						'user_id' => $this->_intUid, 
						'msg_id' => $msgId ) ), 
				'format' => 'json' );
		$newTask = Service_Pmc::checkUserHasNoFinishedMarkTask ( $srvInput );
		
		if ($newTask ['errno'] !== Tieba_Errcode::ERR_SUCCESS || $newTask ['ret'] === false) {
			Bingo_Log::notice ( __CLASS__ . ': has no unfinished task found!' );
			$newTask = Service_Pmc::getImageList ( $srvInput );
			if ($newTask ['errno'] !== Tieba_Errcode::ERR_SUCCESS || $newTask ['ret'] === false) {
				Bingo_Log::warning ( __CLASS__ . ': Add new task and get images error!' );
				$this->_intErrorNo = $newTask ['errno'];
				$this->_renderJson ();
				return false;
			}
		}
		
		foreach ( $newTask ['ret'] ['images'] as &$image ) {
			unset ( $image ['base_type'] );
		}
		
		shuffle ( $newTask ['ret'] ['images'] );
		$this->_arrTpl ['msgid'] = $msgId;
		$this->_arrTpl ['taskid'] = $newTask ['ret'] ['taskId'];
		$this->_arrTpl ['data'] = $newTask ['ret'] ['images'];
		$this->_arrTpl ['bc'] = $newTask ['ret'] ['blackCount'];
		
		$this->_intErrorNo = Tieba_Errcode::ERR_SUCCESS;
		$this->_renderJson ();
		return true;
	}

}
?>