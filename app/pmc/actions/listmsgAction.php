<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2013-04-17 21:12:10
 */

class listmsgAction extends Util_BaseAction {
	public function init() {
		if (! parent::init ()) {
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			return false;
		}
		return true;
	}
	
	protected function _paramsMap ( ) {
		$arrParams = array(
			'opUserType' => array(
				'system'=>array(Util_Const::$opUserTypeMachine, Util_Const::$opUserTypePm),
				'bawu'=>Util_Const::$opUserTypeForumManager
			),
			'appealStatus' => array(
				'todo'=>array(Util_Const::$appealStatusUnAppeal, Util_Const::$appealStatusAppealling, Util_Const::$appealStatusFail, Util_Const::$appealStatusException),
				'done'=>array(Util_Const::$appealStatusSucc, Util_Const::$appealStatusAutoDealed),
				//'unapply'=>array(Util_Const::$appealStatusUnAppeal)
				'unapply'=>array(Util_Const::$appealStatusAppealling, Util_Const::$appealStatusFail, Util_Const::$appealStatusException)
			),
			'readStatus' => array(
				'readed' => Util_Const::$appealReaded,
				'unread' => Util_Const::$appealUnRead,
				'all'	 => array(Util_Const::$appealReaded, Util_Const::$appealUnRead)
			)
		);
		return $arrParams;
	}

	protected function _checkParams($arrInput) {
		if ( !Util_Params::checkNum($arrInput, 'page') ) {
			return false;
		}
		$arrParaMap = $this->_paramsMap();
		if ( !array_key_exists($arrInput['opUserType'], $arrParaMap['opUserType']) ) {
			return flase;
		}
		if ( !array_key_exists($arrInput['appealStatus'], $arrParaMap['appealStatus']) ) {
			return flase;
		}
		if ( !array_key_exists($arrInput['readStatus'], $arrParaMap['readStatus']) ) {
			return flase;
		}
		return true;
	}
	protected function _process() {
		Tieba_Stlog::addNode ( 'service_type', 'list' );
		if (! $this->_bolUserLogin) {
			Bingo_Log::warning ( 'the use do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_POST_CT_NEEDLOGIN;
			$this->_renderJson ();
			return false;
		}
		
		$opUserType = Bingo_Http_Request::get('punish_type', 'system');
		$appealStatus = Bingo_Http_Request::get('status', 'todo');
		$readStatus = Bingo_Http_Request::get('read', 'all');//@todo FE��apiû�м�
		$page	   = Bingo_Http_Request::get('currpage', 1);
		
		//FE����δ����δ����Ĳ������� ������ǿ�Ʒֿ�
		if ( $readStatus == 'unapply' ) {
			$readStatus = 'all';
			$appealStatus = 'unapply';
		}
		
		/*
		$time = ( int ) Bingo_Http_Request::get ( "time", - 1 );
		$status = ( int ) Bingo_Http_Request::get ( "status", - 1 );
		$page = ( int ) Bingo_Http_Request::get ( "pn", 1 );
		$isBawu=(int)Bingo_Http_Request::get('bawu',0);
		*/
		
		if ( false === $this->_checkParams ( compact('opUserType','appealStatus','readStatus','page') ) ) {
			Bingo_Log::warning ( __CLASS__ . ': Check param error!' . var_export ( Bingo_Http_Request::getParams (), true ) );
			$this->_intErrorNo = Tieba_Errcode::ERR_PARAM_ERROR;
			$this->_renderJson ();
			return false;
		}
		/*
		$srvInput = array (
				'input' => Bingo_String::array2json ( array (
						'user_id' => $this->_intUid, 
						'pn' => $page, 
						'size' => Util_Const::$queryMsgPageSize, 
						'time' => $time,
                                                'is_bawu'=>$isBawu,
						'status' => $status ) ), 
				'format' => 'json' );
		*/
		$arrParaMap = $this->_paramsMap();
		$srvInput = array (
				'input' => Bingo_String::array2json ( array (
						'user_id' => $this->_intUid, 
						'pn' => $page, 
						'size' => Util_Const::$queryMsgPageSize,
						'op_user_type' => $arrParaMap['opUserType'][$opUserType],
						'appeal_status'=> $arrParaMap['appealStatus'][$appealStatus], 
						'read_status'  => $arrParaMap['readStatus'][$readStatus]
						 ) ), 
				'format' => 'json' );
		$srvResult = Service_Pmc::findAppealMsgsByUserId ( $srvInput );
		
		if ($srvResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS || $srvResult ['ret'] === false) {
			Bingo_Log::warning ( __CLASS__ . ': get msgs service error, errno: ' . $srvResult ['errno'] );
			$this->_intErrorNo = $srvResult ['errno'];
			$this->_renderJson ();
			return false;
		}
		
		$mixUnReadAndUnApplyRet = $this->_getUnReadAndUnApplyCnt($opUserType, $appealStatus, $readStatus);
		if ( false === $mixUnReadAndUnApplyRet ) {
			$this->_renderJson();
			return false;
		}
		
		$pageInfo = $srvResult ['ret'] ['page'];
		$data = $srvResult ['ret'] ['data'];
		
		$this->_arrTpl ['data'] = array (
			'unread' => $mixUnReadAndUnApplyRet['unread'],
			'unapply'=> $mixUnReadAndUnApplyRet['unapply'],
			'list' => $data, 
			'page' => $pageInfo 
		);
		
		$this->_intErrorNo = Tieba_Errcode::ERR_SUCCESS;
		$this->_renderJson ();
		return true;
	}
	
	protected function _getUnReadAndUnApplyCnt ( $opUserType, $appealStatus, $readStatus ) {
		$arrRemindInfo = array('unread'=>0,'unapply'=>0);
		
		$arrParaMap = $this->_paramsMap();
		if ( $appealStatus == 'unapply' ) {
			$appealStatus = 'todo';
		}
		//�����ϵͳ��������δ��
		$srvUnReadInput = array (
				'input' => Bingo_String::array2json ( array (
						'user_id' => $this->_intUid, 
						'op_user_type' => $arrParaMap['opUserType'][$opUserType],
						'appeal_status'=> $arrParaMap['appealStatus'][$appealStatus], 
						'read_status'  => $arrParaMap['readStatus']['unread']
						 ) ), 
				'format' => 'json' );
		$srvUnreadResult = Service_Pmc::getRemindMsgCount ( $srvUnReadInput );
		if ($srvUnreadResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS || $srvUnreadResult ['ret'] === false) {
			Bingo_Log::warning ( __CLASS__ . ': getUnReadMsgCount service error, errno: ' . $srvUnreadResult ['errno'] );
			$this->_intErrorNo = $srvUnreadResult ['errno'];
			return false;
		}
		
		$arrRemindInfo['unread'] = intval($srvUnreadResult ['ret']['msgCt']);
		
		//ϵͳ δ����д����������
		if ( $opUserType == 'system' && in_array($appealStatus, array('todo','unapply')) ) {
			$srvUnApplyInput = array (
				'input' => Bingo_String::array2json ( array (
						'user_id' => $this->_intUid, 
						'op_user_type' => $arrParaMap['opUserType'][$opUserType],
						'appeal_status'=> array( Util_Const::$appealStatusAppealling, Util_Const::$appealStatusFail, Util_Const::$appealStatusException), 
						'read_status'  => $arrParaMap['readStatus']['all']
						 ) ), 
				'format' => 'json' );
			$srvUnApplyResult = Service_Pmc::getRemindMsgCount ( $srvUnApplyInput );
			if ($srvUnApplyResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS || $srvUnApplyResult ['ret'] === false) {
				Bingo_Log::warning ( __CLASS__ . ': getUnApplyMsgCount service error, errno: ' . $srvUnApplyResult ['errno'] );
				$this->_intErrorNo = $srvUnApplyResult ['errno'];
				return false;
			}
			$arrRemindInfo['unapply'] = intval($srvUnApplyResult ['ret']['msgCt']);
		}
		
		return $arrRemindInfo;
	}

}
?>