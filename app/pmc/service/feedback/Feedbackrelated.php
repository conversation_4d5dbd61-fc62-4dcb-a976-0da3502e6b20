<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @desc 我要反馈相关
 * @date 2019-01-04
 */
class Service_Feedback_Feedbackrelated
{
    const FEEDBACK_USER_NAME_NOT_EXIST    = 50001; // 填写的用户名不存在
    const FEEDBACK__USER_NOT_BLOCK        = 50002; // 填写的用户名存在，但没有被封禁
    const FEEDBACK_USER_BLOCK_NOT_APPEAL  = 50003; // 用户被封禁了，但是还没有进行申诉
    const FEEDBACK_USER_BLOCK_APPEAL_ON   = 50004; // 用户被封禁了，并且已经提交申诉了，但是还未被处理
    const FEEDBACK_USER_BLOCK_APPEAL_DONE = 50005; // 用户被封禁了，并且已经提交申诉了，并且已被处理
    const FEEDBACK_USER_IS_MANAGER        = 60001; // 是吧主
    const FEEDBACK_USER_IS_NOT_MANAGER    = 60002; // 不是吧主

    // ufo配置()
    const UFO_API_URL_COMMIT = 'http://ufosdk.baidu.com/?m=Api&a=postMsg'; // 提交
    const UFO_API_APPID      = 222251; // appid
    const UFO_API_CHANNEL    = 33527; // 反馈来源

    public static $userBlockStatus = array(
        50001  => '该帐号不存在哦，请核实后重新填写',
        50002  => '亲提供的帐号未被封禁，请核实后重新填写',
        50003  => '帐号被封禁，可点击下方按钮发起线上申诉，客服小姐姐24h内会通过页面右上角-消息-我的通知给你发送处理结果哦',
        50004  => '您的帐号已提交解封申诉，24h内可在小铃铛-通知-系统消息查看处理结果',
        50005  => "您的申诉客服小姐姐已经处理啦。请您点击以下链接前往系统消息通知查看处理结果：<a href='http://tieba.baidu.com/sysmsg/index?type=notity&category_id=1'>系统消息</a>",
        60001  => '该用户是吧主',
        60002  => '该用户不是吧主'
    );

    /**
     * 获取用户封禁状态以及申诉状态
     * @param array $arrInput
     * @return array
     */
    public static function getBlockResInfo($arrInput=array())
    {
        Bingo_Log::warning('call '.__METHOD__.' input ['.serialize($arrInput).']');
        if(empty($arrInput) || !isset($arrInput['uid']) || !isset($arrInput['username']) || !intval($arrInput['uid']))
        {
            return self::_errRet(Tieba_Errcode::ERR_HOME_UI_PARAM);
        }

        // 获取用户id
        $uid   = intval($arrInput['uid']);
        $uname = trim($arrInput['username']);
        if(!empty($uname))
        {
            $arrParam = array(
                'names' => array($uname),
            );
            $arrRet = Tieba_Service::call('user', 'getUidByNames', $arrParam, null, null, 'post', 'php', 'utf-8');
            Bingo_Log::warning('call '.__METHOD__.' user::getUidByNames input ['.serialize($arrParam).'] ooutput ['.serialize($arrRet).']');
            if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'])
            {
                Bingo_Log::warning('call '.__METHOD__.' getUidByNames method failed! input: [' . serialize($arrParam) . '].output: [' . serialize($arrRet) . ']');
                return self::_errRet($arrRet['errno']);
            }

            if(!isset($arrRet['data'][$uname]['0']['user_id']) || !intval($arrRet['data'][$uname]['0']['user_id']))
            {
                return self::_errRet(self::FEEDBACK_USER_NAME_NOT_EXIST, self::$userBlockStatus[self::FEEDBACK_USER_NAME_NOT_EXIST]);
            }

            $uid = intval($arrRet['data'][$uname]['0']['user_id']);
        }

        // 获取用户当前封禁状态
        $blockInput = array(
            "req" => array(
                "user_id"  => $uid,
                "forum_id" => 0,
            )
        );
        $blockOutput = Tieba_Service::call('anti', 'antiUserBlockQuery', $blockInput, null, null, 'post', 'php', 'utf-8');
        Bingo_Log::warning('call '.__METHOD__.' anti::antiUserBlockQuery input ['.serialize($blockInput).'] ooutput ['.serialize($blockOutput).']');
        if(false === $blockOutput || Tieba_Errcode::ERR_ANTI_ID_BLOCKED != $blockOutput['errno'])
        {
            Bingo_Log::warning('call '.__METHOD__.' antiUserBlockQuery method failed! input: [' . serialize($blockInput) . '].output: [' . serialize($blockOutput) . ']');
            $errno = self::FEEDBACK__USER_NOT_BLOCK;
            return self::_errRet($errno, self::$userBlockStatus[$errno]);
        }

        // 获取用户封禁后的申诉状态
        $userstateInput = array(
            'reqs' => array(
                'check_block' => array(
                    'service_type' => 'blockid',
                    'forum_id'     => 0,
                    'key'          => $uid,
                )
            )
        );
        $userstateOutput = Tieba_Service::call('userstate', 'queryBlockAndAppealInfo', $userstateInput, null, null, 'post', 'php', 'utf8');
        Bingo_Log::warning('call '.__METHOD__.' userstate::queryBlockAndAppealInfo input ['.serialize($userstateInput).'] ooutput ['.json_encode($userstateOutput).']');
        if (false === $userstateOutput || false === $userstateOutput['ret'] || Tieba_Errcode::ERR_SUCCESS != $userstateOutput['errno']
            || !isset($userstateOutput['res']['appeal_status']) || !isset($userstateOutput['res']['appeal_times']))
        {
            Bingo_Log::warning('call '.__METHOD__.' userstate::queryBlockAndAppealInfo fail, input ['.serialize($userstateInput).'] output ['.serialize($userstateOutput).']');
            return self::_errRet(Tieba_Errcode::ERR_FAIL_TO_CALL_SERVICE_EMS);
        }

        // 已申诉
        if(1 == $userstateOutput['res']['appeal_status'])
        {
            return self::_errRet(self::FEEDBACK_USER_BLOCK_APPEAL_ON, self::$userBlockStatus[self::FEEDBACK_USER_BLOCK_APPEAL_ON]);
        }

        // 申诉状态为0，可能是未申诉、也可能是已经申诉了，被拒绝了
        if(0 == $userstateOutput['res']['appeal_status'])
        {
            // 封禁中，未申诉
            if($userstateOutput['res']['appeal_times'] < 1)
            {
                return self::_errRet(self::FEEDBACK_USER_BLOCK_NOT_APPEAL, self::$userBlockStatus[self::FEEDBACK_USER_BLOCK_NOT_APPEAL]);
            }else{
                // 已经申诉了，并且被拒绝了
                return self::_errRet(self::FEEDBACK_USER_BLOCK_APPEAL_DONE, self::$userBlockStatus[self::FEEDBACK_USER_BLOCK_APPEAL_DONE]);
            }
        }

        // 执行到这就是异常
        return self::_errRet(Tieba_Errcode::ERR_FAIL_TO_CALL_SERVICE_EMS);
    }

    /***
     * 获取用户是否是吧主
     * @param array $arrInput
     * @return array
     */
    public static function getUserManagerInfo($arrInput=array())
    {
        Bingo_Log::notice('call '.__METHOD__.' input ['.serialize($arrInput).']');
        if(empty($arrInput) || !is_array($arrInput) || !isset($arrInput['uid']) || !intval($arrInput['uid']))
        {
            return self::_errRet(Tieba_Errcode::ERR_HOME_UI_PARAM);
        }

        // 获取用户是否是某个吧的吧主
        $input = array(
            "user_id" => intval($arrInput['uid']),
        );
        $arrOut = Tieba_Service::call('perm', 'getUserManagerList', $input, NULL, NULL, 'post', 'php', 'utf-8');
        Bingo_Log::notice('call '.__METHOD__.' perm::getUserManagerList input ['.serialize($arrOut).'] output ['.serialize($arrOut).']');
        if(false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno'])
        {
            return self::_errRet(Tieba_Errcode::ERR_FAIL_TO_CALL_SERVICE_EMS);
        }

        $forumIdList = array();
        if(!isset($arrOut['output']['0']['forum_id']) || empty($arrOut['output']))
        {
            // 获取是否是吧务
            $arrInput = array(
                "user_id" => intval($arrInput['uid']), //用户id
            );
            $arrRes  = Tieba_Service::call('perm', 'getUserBawuForum', $arrInput, null, null, 'post', 'php', 'utf-8');
            if (!$arrRes || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS)
            {
                Bingo_Log::warning('call '.__METHOD__.' perm err.  [sevice_name:perm] [method:getUserBawuForum] [input:'.serialize($arrInput).'] [output:'.serialize($arrRes).']');
                return self::_errRet(self::FEEDBACK_USER_IS_NOT_MANAGER, self::$userBlockStatus[self::FEEDBACK_USER_IS_NOT_MANAGER]);
            }

            if(!isset($arrRes['forum_user_roles']) || !is_array($arrRes['forum_user_roles']) || count($arrRes['forum_user_roles']) <= 0)
            {
                Bingo_Log::notice('call '.__METHOD__.' Not a small bar owner or bar, just quit，input ['.serialize($arrInput).'] output ['.serialize($arrRes).']');
                return self::_errRet(self::FEEDBACK_USER_IS_NOT_MANAGER, self::$userBlockStatus[self::FEEDBACK_USER_IS_NOT_MANAGER]);
            }

            // 是吧务
            foreach ($arrRes['forum_user_roles'] as $roleKey => $roleVlaue){
                foreach ($roleVlaue as $role)
                {
                    $forumIdList[] = $role['forum_id'];
                }
            }
            return self::_errRet(self::FEEDBACK_USER_IS_MANAGER, self::$userBlockStatus[self::FEEDBACK_USER_IS_MANAGER], $forumIdList);
        }

        // 是吧主
        foreach($arrOut['output'] as $item){
            $forumIdList[] = $item['forum_id'];
        }

        return self::_errRet(self::FEEDBACK_USER_IS_MANAGER, self::$userBlockStatus[self::FEEDBACK_USER_IS_MANAGER], $forumIdList);
    }

    /***
     * 添加我要反馈信息
     * @return array
     */
    public static function addPmcFeedback($arrInput)
    {
        $arrInput_log = $arrInput;
        unset($arrInput_log['bduss']);
        Bingo_Log::warning('call '.__METHOD__.' input ['.serialize($arrInput_log).']');
        if(!intval($arrInput['fb_type']) || !intval($arrInput['user_id']) || empty($arrInput['fb_desc']))
        {
            Bingo_Log::warning("user_id invalid:".serialize($arrInput_log));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 提交反馈数据到UFO后台
        $res = self::syncToFeebackUfo($arrInput);
        Bingo_Log::warning('call '.__METHOD__.' syncToFeebackUfo input ['.serialize($arrInput_log).'] output ['.serialize($res).']');
        if($res ['errno'] !== Tieba_Errcode::ERR_SUCCESS)
        {
            return $res;
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', $res['data']);
    }

    /***
     * 提交反馈数据到ufo平台
     * @param array $arrInput
     */
    public static function syncToFeebackUfo($arrInput)
    {
        $arrInput_log = $arrInput;
        unset($arrInput_log['bduss']);
        Bingo_Log::warning('call '.__METHOD__.' input ['.serialize($arrInput_log).']');
        // 接口参数
        $input                            = array();
        $input['appid']                   = self::UFO_API_APPID;
        $input['content']                 = $arrInput['fb_desc'];
        $input['screenshot']              = $arrInput['fb_pic_urls'];
        $input['phonetime']               = time();
        $input['feedback_type']           = $arrInput['fb_type'];
        $input['contact_way']             = $arrInput['fb_contact'];
        $input['extras']                  = json_encode(array('order' => $arrInput['fb_order']));
        $input['extend_feedback_channel'] = self::UFO_API_CHANNEL;

        // 请求接口
        $cookie = array('BDUSS' => $arrInput['bduss']); // 用户ufo平台获取当前提交反馈的用户
        $url    = self::UFO_API_URL_COMMIT;
        Bingo_Timer::start ( 'pmc_feedback' );
        $httpproxy = Orp_FetchUrl::getInstance(array('timeout' => 10000, 'conn_timeout' => 5000, 'max_response_size' => 1024000));
        Bingo_Log::warning('call '.__METHOD__.' Submit data to the ufo platform， url ['.$url.'] input ['.serialize($input).']');
        $output    = $ret = $httpproxy->post($url, $input, array(), $cookie);
        Bingo_Log::warning('call '.__METHOD__.' Submit data to the ufo platform，cookie [] output ['.serialize($output).']');
        $err       = $httpproxy->errmsg();
        $http_code = $httpproxy->http_code();
        Bingo_Timer::end ( 'pmc_feedback' );
        Bingo_Log::warning('call '.__METHOD__.' Submit data to the ufo platform， result err ['.serialize($err).'] http_code ['.serialize($http_code).']');

        if(!$output || $err || 200 != $http_code)
        {
            Bingo_Log::warning('call '.__METHOD__.' Request ufo interface, initiate http request failed');
            return self::_errRet(Tieba_Errcode::ERR_TBMALL_CALL_THIRD_FAIL,'系统错误，请您稍后再试');
        }

        if(false !== stripos($output, 'sucess'))
        {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $res = json_decode($output, true);
        Bingo_Log::warning('call '.__METHOD__.' process result ['.serialize($res).']');
        if(empty($res) || Tieba_Errcode::ERR_SUCCESS != $res['errno'] || !isset($res['id']) || !intval($res['id'])
            || !isset($res['feedback_id']) || !intval($res['feedback_id']))
        {
            Bingo_Log::warning('call '.__METHOD__.' Request ufo interface, initiate http success, interface returns failure');
            return self::_errRet(Tieba_Errcode::ERR_IM_USER_UPDATE_MASK_FAILED, '系统错误，请您稍后再试');
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, '', array('id' => intval($res['id'])));
    }

    private static function _errRet($errno, $errmsg = '', $data=array())
    {
        return array(
            'errno'  => $errno,
            'errmsg' => $errmsg ? $errmsg : Util_Errno::getErrmsg($errno),
            'data'   => $data
        );
    }
}