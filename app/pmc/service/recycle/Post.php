<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @abstract ����վ ϵͳɾ�� ����ָ�
 *    
 */

class Service_Recycle_Post {
	
	const SERVICE_NAME = "Service_Recycle_Post";
	
	protected static $_conf = null;
	
	const WAIT_RESTORE				= 1;//���ָ�
	const WAIT_MARK_RESTORE			= 2;//��ͼ�ָ���
	const WAIT_MANUAL_RESTORE		= 3;//�˹�������
	const MANUAL_RESTORE_FAILED		= 4;//�˹��ָ�ʧ��
	const MARK_RESTORE_FAILED		= 5;//��ͼ�ָ�ʧ��
	const PHPONE_RESTORE_FAILED		= 6;//�ֻ���֤�ָ�ʧ��
	const RESTORE_NOT_ALLOW			= 7;//¥��¥���Ӳ������ָ�
	
	const FILTER_BEFORE_THREE_MONTH_AGO_FLAG = 'm3';

	private static function _init() {
		if (self::$_conf == null) {
			self::$_conf = Bd_Conf::getConf ( "/app/pmc/service_recycle_post" );
			if (self::$_conf == false) {
				Bingo_Log::warning ( "init get conf fail." );
				return false;
			}
		}
		return true;
	}
	
	private static function _errRet($errno) {
		return array (
				'errno' => $errno, 
				'errmsg' => Util_Errno::getErrmsg ( $errno ) 
				);
	}
	
	private static function _succRet($ret) {
		$error = Tieba_Errcode::ERR_SUCCESS;
		return array (
				'errno' => $error, 
				'errmsg' => Util_Errno::getErrmsg ( $error ), 
				'ret' => $ret 
			);
	}
	
	/**
	 * ��ѯ�û���ɾ���˵����ӵ���Ϣ
	 * @param array $arrInput array('user_id'=>,'offset'=>,'res_num')
	 * @return Array
	 */
	public static function queryUserDelPosts( $arrInput ) {
		if ( !isset($arrInput['input']) || empty($arrInput['input']) ) {
			Bingo_Log::warning(__FUNCTION__ . 'input params invalid ' . serialize($arrInput));
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$input = Tieba_Service::getArrayParams($arrInput, 'input');
		
		if ( !Util_Params::checkNum($input, 'user_id') || !Util_Params::checkNum($input, 'offset') || 
				!Util_Params::checkNum($input, 'res_num') ) {
			Bingo_Log::warning(__FUNCTION__ . 'input params invalid ' . serialize($arrInput));
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$srvInput = array();
		$srvInput ['user_id'] = $input['user_id'];
		$srvInput ['offset'] = $input['offset'];
		$srvInput ['res_num'] = $input['res_num'];
		$srvInput ['order_type'] = 1;
		$srvInput ['delete_type'] = 1;
		$srvInput ['need_content'] = 1;
        $intIe = 'gbk';
        if (isset($input['ie'])) {
            $intIe = strval($input['ie']);
            unset($input['ie']);
        }
		$arrPostResult = Tieba_Service::call ( 'post', 'queryUserPost', array ('input' => $srvInput ), null, null, 'post', 'php', $intIe );
		if (! isset ( $arrPostResult ['errno'] ) || $arrPostResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( __CLASS__ . ': queryUserPost service error, errno: ' . $arrPostResult ['errno'] );
			return self::_errRet( $arrPostResult ['errno'] );
		}

		if ( empty ( $arrPostResult ['post'] ['post'] ) ) {
			return self::_succRet(array());
		}
		return self::_succRet( $arrPostResult ['post'] ['post'] );
	}

	/**
	 * ��ȡ���ӵ���Ϣ
	 * @param int $intPid
	 * @return array('tid'=>,'pid'=>,'forum_name'=>)
	 */
	
	public static function checkIsBawuPostCanRestore($arrInput) {
	
		if (! Util_Params::checkNum ( $arrInput, 'pid' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		if (! Util_Params::checkNum ( $arrInput, 'tid' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
	
		$intPostId		= intval($arrInput['pid']);
		$intThreadId	= intval($arrInput['tid']);
		$intUserId      = intval($arrInput['user_id']);
	
		/*
		 * ��� tid , pid �Ƿ�ƥ��
		* ��ѯʧ�ܿ���ԣ�����false , Ĭ��ƥ��
		* add by qmy
		*
		* checkPostInfo �ӿ���bug��������
		*/
		/*
			$mixRet = Util_Post::checkPostInfo($intThreadId , $intPostId);
		if( false !== $mixRet && Util_Post::IS_TID_PID_MATCH != $mixRet )
		{
		Bingo_Log::warning ( __CLASS__ . ':ERR[tid pid not match] TID['.serialize($intThreadId).'] PID['.serialize($intPostId).']' );
		return self::_errRet( Util_Errno::$errRestorePostIllegal );
		}
		*/
	
		/*
		 * ��� tid , pid �Ƿ�ƥ��
		* ��ѯʧ�ܿ���ԣ�����false , Ĭ��ƥ��
		* by qmy
		*/
	
		$inPost = array(
				'0' => $intPostId ,
		);
	
		$outPost = Util_Post::getPostInfoByPids($inPost);
	
		$threadIdCheck = isset( $outPost['0']['thread_id'] ) ? $outPost['0']['thread_id'] : false ;
	
		if( false !== $threadIdCheck && $intThreadId != $threadIdCheck )
		{
			Bingo_Log::warning ( __CLASS__ . ':ERR[tid pid not match] TID['.serialize($intThreadId).'] PID['.serialize($intPostId).']' );
			return self::_errRet( Util_Errno::$errRestorePostIllegal );
		}
	
		//У�������ǲ����û��Լ���
		//$arrPostInfo = Util_Post::getPostInfoByPids(array($intPostId));
		$arrPostInfo = $outPost ;
	
		if ( empty($arrPostInfo) ) {
			return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
		}
		$intPostUid = isset($arrPostInfo[0]['user_id']) ? intval($arrPostInfo[0]['user_id']):null;
		if ( null === $intPostUid || $intPostUid !== $intUserId  ) {
			return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
		}
	
		//¥��¥���Ӳ��ָܻ�
		$arrDuplexInfo = Util_Post::getClassifyPostAttributeByPids( array($intPostId) );
		if (isset($arrDuplexInfo[$intPostId])) {
			if (  !$arrDuplexInfo[$intPostId]['is_valid'] ) { //�Ƿ�
				return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
			}
			/* getCommentInfo���ص�is_post_delete��׼ �˶�ע�͵�
				elseif ( !$arrDuplexInfo[$intPostId]['is_thread_deleted'] && !$arrDuplexInfo[$intPostId]['is_post_deleted']
						&& !$arrDuplexInfo[$intPostId]['is_comment_deleted'] ) { //û�б�ɾ��
			return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
			}
			*/
			//¥��¥����һ
			elseif ( $arrDuplexInfo[$intPostId]['is_comment'] && ($arrDuplexInfo[$intPostId]['is_post_deleted'] ||
					$arrDuplexInfo[$intPostId]['is_thread_deleted']) ) {
				return self::_errRet(Util_Errno::$errThreadDeleted);
			} //¥��¥���ζ�
			elseif ( $arrDuplexInfo[$intPostId]['is_post'] && $arrDuplexInfo[$intPostId]['is_thread_deleted'] ) {
				return self::_errRet(Util_Errno::$errThreadDeleted);
			}
		}
	
		//���ӵ�ɾ����Ϣ
		$arrDelPostParams = array(array('pid'=>$intPostId,'tid'=>$intThreadId));
		$arrSrvDelPostInfo = Service_Recycle_Post::getDelPostInfo($arrDelPostParams);
		if ( $arrSrvDelPostInfo['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
			return self::_errRet($arrSrvDelPostInfo['errno']);
		}
		if ( !isset($arrSrvDelPostInfo['ret'][$intPostId]) ) {
			return self::_errRet(Util_Errno::$errPostNotDeleted);
		}
	
		$arrPostDelInfo = $arrSrvDelPostInfo['ret'][$intPostId];
		/*
			if ( empty($arrPostDelInfo['del_username']) && strstr ( $arrPostDelInfo['call_from'], 'bawu' ) === false) {
		$arrPostDelInfo['call_from'] = 'machine_unknow';
		}
		*/
		/*
			$arrPostDelInfo = $arrSrvDelPostInfo['ret'][$intPostId];
		if ( empty($arrPostDelInfo['delete_time']) || empty($arrPostDelInfo['del_username']) ) {
		return self::_errRet(Util_Errno::$errPostNotDeleted);
		}
		if ($strTab == self::FILTER_BEFORE_THREE_MONTH_AGO_FLAG  && intval($arrPostDelInfo['delete_time']) < time () - 3 * 30 * 3600 * 24 ) {
		return self::_errRet(Util_Errno::$errPostDeleteThreeMonthAgo);
		}
		*/
		$source = intval($arrPostDelInfo['source']);
		$bawuSource = intval(Util_Const::$delPostSourceMap[Util_Const::$sourceTypeBawu]);
		$maskSource = intval(Util_Const::$delPostSourceMap[Util_Const::$sourceTypeMask]);
		if($source !== $bawuSource && $source !== $maskSource){
			return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
		}
		//ɾ�����ӵ��û���uid
		/*$intDelUid = null;
		$strDelUserName = $arrPostDelInfo['del_username'];
		if ( !empty($strDelUserName) ) {
			$mixDelUserInfo = Util_User::getUidByUnames(array($strDelUserName));
			if ( false === $mixDelUserInfo || !isset($mixDelUserInfo[0]['user_id']) || empty($mixDelUserInfo[0]['user_id'])) {
				Bingo_Log::warning( __CLASS__ . "[getUidByUnames] failed [uname]$strDelUserName" );
				//return self::_errRet(Util_Errno::$errDelUidQueryFail);
			} else {
				$intDelUid = intval($mixDelUserInfo[0]['user_id']);
			}
		}
		$arrPostDelInfo['del_uid'] = $intDelUid;//add by fengzhen
		//ɾ�����ӵ��ʻ�����
		$intDelUserType = Util_Appeal::getAppealOpType($intDelUid, $strCallFrom);
		if ( $intDelUserType !== Util_Const::$opUserTypeForumManager  ) {
			return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
		}*/
		
		$srvInput = array('pid' => array($intPostId));
		$srvResult = Service_Recycle_Appeal::getBawuPostAppealListsByPids($srvInput);
		if ( $srvResult  === false || $srvResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS  ) {
			Bingo_Log::warning ( __CLASS__ . ': getPostApplyStatusInfo service error, errno: ' . $srvResult ['errno'] );
			return self::_errRet(Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL);
		}
		$dataInfo = $srvResult['ret'][$intPostId];
		if($dataInfo && intval($dataInfo['status']) === Util_Const::$bawuPostRestoreDealing ){
			return self::_errRet(Util_Errno::$errRestorePostIllegal);//����д��������߼�¼���ܽ�������
		}
	
		/*if ( $bolReturnOpUsertype ) {
			//���������ָ��������л���ʹ�õ���� ֱ�Ӱ�opgroup��Ϊadmin========
			if ( Util_Buffet::isPostSelfHelpDeny($intUserId) ) {
				$intDelUserType = Util_Const::$opUserTypePm;
			}
			$arrRes = array(
					'ret'=>true,
					'opUserType'=>$intDelUserType,
					'postDelInfo'=>$arrPostDelInfo,
					'monitor_type'=>$intMonitorType
			);
			return self::_succRet($arrRes);
		}*/
	
		return self::_succRet(true);
	}
	public static function getPostInfoByPid($intPid) {
		if ( !is_numeric($intPid) ) {
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrPostInfo = array();
		$srvInput = array('post_ids'=>array(intval($intPid)));
		$srvRet = Tieba_Service::call('post', 'getPostInfo', $srvInput);
		if ( $srvRet['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
			Bingo_Log::warning(__FUNCTION__ . ' post getPostInfo error' . $srvRet['errno']);
		}
		if ( isset($srvRet['output'][0]) && !empty($srvRet['output'][0]) && is_array($srvRet['output'][0]) ) {
			$arrPostInfo = $srvRet['output'][0];
		}
		return self::_succRet($arrPostInfo);
	}

	/**
	 * ��ȡ���ӱ�ɾ������ϸ��Ϣ
	 * ֧������
	 * @param Array
	 * @return Array | boolean
	 * array("pid"=>array("delete_timme"=>,"del_username"=>,"call_from"=>)
	 */
	public static function getDelPostInfo($arrPostInfo, $ie = 'gbk') {
		if ( empty($arrPostInfo) || !is_array($arrPostInfo) ) {
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		
		foreach ( $arrPostInfo as $arrPostParam ) {
			if ( !isset($arrPostParam['tid']) || !is_numeric($arrPostParam['tid']) ||
					!isset($arrPostParam['pid']) || !is_numeric($arrPostParam['pid']) ) {
				return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
			}
		}
		
		$arrPidTidMap = array();
		$arrPostIds = array ();
		foreach ( $arrPostInfo as $arrThreadInfo ) {
			$arrPostIds [] = array (
					'thread_id' => intval($arrThreadInfo ['tid']), 
					'post_id' => intval($arrThreadInfo ['pid'])
				);
			$arrPidTidMap[intval($arrThreadInfo ['tid'])] = intval($arrThreadInfo ['pid']);
		}
		
		//�ܲ鵽���ӵ�ɾ����Ϣ
		/*
		$arrDelReq = array ();
		$arrDelReq ['batch_del_post_req'] = array ('post_ids' => $arrPostIds );
        
		$arrDelInfoRes = Tieba_Service::call ( 'post', 'query_del_post', $arrDelReq );
		if (! isset ( $arrDelInfoRes ['errno'] ) || $arrDelInfoRes ['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( __CLASS__ . ': post query_del_post service error, errno: ' . $arrDelInfoRes ['errno'] );
			return self::_errRet($arrDelInfoRes ['errno']);
		}
		
		if (empty($arrDelInfoRes ['batch_del_post_res'] ['del_post_res'])) {
			return self::_succRet(array());
		}
		
		$arrPostDelInfo = array ();
		foreach ( $arrDelInfoRes ['batch_del_post_res'] ['del_post_res'] as $arrDelInfo ) {
			$post_id = intval($arrDelInfo ['post_id']);
			$arrPostDelInfo [$post_id] = array (
					'delete_time' => $arrDelInfo ['delete_time'], 
					'del_username' => $arrDelInfo ['del_username'], 
					'call_from' => $arrDelInfo ['call_from'],
					'monitor_type' => $arrDelInfo ['monitor_type']
				);
		}
		 */
		
		$arrInput = array ('post_ids' => $arrPostIds );
		$arrDelInfoRes = Tieba_Service::call('post','getDelpostInfo',$arrInput,null,null,'post','php',$ie);
		if (! isset ( $arrDelInfoRes ['errno'] ) || $arrDelInfoRes ['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( __CLASS__ . ': post getDelpostInfo service error, errno: ' . $arrDelInfoRes ['errno'] );
			return self::_errRet($arrDelInfoRes ['errno']);
		}
		
		if (empty($arrDelInfoRes ['output'] ['delpost_res']) && empty($arrDelInfoRes ['output'] ['delthread_res'])) {
			return self::_succRet(array());
		}
		
		if ( !empty($arrDelInfoRes ['output'] ['delthread_res']) ) {
			foreach ( $arrDelInfoRes ['output'] ['delthread_res'] as $tid=>$item ) {
				$item['post_id'] = $arrPidTidMap[$tid];
				if ( !isset($arrDelInfoRes ['output'] ['delpost_res'][$arrPidTidMap[$tid]]) ) {
					$arrDelInfoRes ['output'] ['delpost_res'][$arrPidTidMap[$tid]] = $item;
				}
			}
		}
		
		$arrUserIds = array();
		foreach ( $arrDelInfoRes ['output'] ['delpost_res'] as $arrDelInfo ) {
			$intUserId = intval($arrDelInfo['op_uid']);
			array_push($arrUserIds, $intUserId);
		}
		$arrUserIds = array_unique($arrUserIds);
		
		$arrUidUnameMap = array();
		if ( !empty($arrUserIds) ) {
			$arrOut = Tieba_Service::call ( 'user', 'getUnameByUids', array('user_id'=>$arrUserIds), null, null, 'post', 'php', $ie );
			if (Tieba_Errcode::ERR_SUCCESS !== $arrOut ['errno']) {
				Bingo_Log::warning ( 'call service user::getUserInfoByUids failed,errno[' . $arrOut ['errno'] . '],errmsg[' . $arrOut ['errmsg'] . ']' );
				return self::_errRet($arrOut ['errno']);
			}
			foreach ($arrOut ['output'] ['unames'] as $item) {
				$arrUidUnameMap[$item['user_id']] = $item['user_name'];
			}
		}
		
		$arrPostDelInfo = array ();
		foreach ( $arrDelInfoRes ['output'] ['delpost_res'] as $arrDelInfo ) {
			$post_id = intval($arrDelInfo ['post_id']);
			$arrPostDelInfo [$post_id] = array (
					'delete_time' => $arrDelInfo ['op_time'], 
					'del_username' => isset($arrUidUnameMap[$arrDelInfo['op_uid']]) ? $arrUidUnameMap[$arrDelInfo['op_uid']] : '' , 
					'del_user_id'	=> $arrDelInfo['op_uid'],
					'call_from' => $arrDelInfo ['call_from'],
					'monitor_type' => $arrDelInfo ['monitor_type'],
					'source'	=> $arrDelInfo['source']
				);
		}
		
		return self::_succRet($arrPostDelInfo);
	}
	
	/**
	 * ���û�����ѯ�û�id
	 * @param Array $arrUname
	 * @return Array array('user_name'=>'user_id')
	 */
	public static function getUidsByUnames($arrUnames, $ie = 'gbk') {
		if ( empty($arrUnames) || !is_array($arrUnames) ) {
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrUnameAndUid = array();
		
		$arrUnames = array_chunk($arrUnames, 10);
		foreach ( $arrUnames as $arrItemUnames ) {
			$arrUserInfo = Tieba_Service::call ( 'user', 'getUidByUnames', array ('user_name' => $arrItemUnames), null, null, 'post', 'php', $ie );
			if (! isset ( $arrUserInfo ['errno'] ) || $arrUserInfo ['errno'] != Tieba_Errcode::ERR_SUCCESS) {
				Bingo_Log::warning ( __CLASS__ . ': user getUidByUnames service error, errno: ' . $arrUserInfo ['errno'] );
				return self::_errRet($arrUserInfo ['errno']);
			}
			
			if ( isset($arrUserInfo ['output'] ['uids']) && !empty($arrUserInfo ['output'] ['uids']) ) {
				foreach ( $arrUserInfo ['output'] ['uids'] as $arrUser ) {
					$arrUnameAndUid[$arrUser['user_name']] = intval($arrUser['user_id']);
				}
			}
		}
		
		return self::_succRet($arrUnameAndUid);
	}

	/**
	 * У�������Ƿ��Ǳ������˺Ż�PMɾ�� ���ڹ���ʱ�䷶Χ��
	 * @param Array $arrPostInfo
	 * @param String $strFilterTimeFlag
	 * @return boolean
	 */
	public static function isPostDelByAccountOrPmAndValid($arrPostInfo, 
				$strFilterTimeFlag = self::FILTER_BEFORE_THREE_MONTH_AGO_FLAG) {

		if ( !isset($arrPostInfo['call_from']) || empty($arrPostInfo['call_from']) ||
				!isset($arrPostInfo['delete_time']) || empty($arrPostInfo['delete_time']) ||
				!isset($arrPostInfo['del_uid']) || empty($arrPostInfo['del_uid']) ) {
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		
		$strCallFrom = $arrPostInfo['call_from'];
		$intDelTime = $arrPostInfo['delete_time'];
		$intStartTime = 0;
		if ($strFilterTimeFlag == self::FILTER_BEFORE_THREE_MONTH_AGO_FLAG) {
			$intStartTime = time () - 3 * 30 * 3600 * 24;
		}
		if ($intDelTime < $intStartTime) {
			return self::_succRet(false);
		}
		//�û�id
		$intDelUid = $arrPostInfo['del_uid'];
		//ɾ���û�������
		$delUserType = Util_Appeal::getAppealOpType ( $intDelUid, $strCallFrom );
		if ($delUserType !== Util_Const::$opUserTypeMachine && $delUserType !== Util_Const::$opUserTypePm) {
			return self::_succRet(false);
		}
		return self::_succRet(true);
	}
	
	/**
	 * �ж��û���ͼ�ĵȼ��Ƿ��㹻
	 * @param int $userId
	 * @return boolean
	 */
	private static function _isUserMarkImgLevelEnough($userId) {
		$maxUserLevel = Util_User::getUserMaxLevelByUid ( $userId );
		if ($maxUserLevel === false || $maxUserLevel < Util_Const::$userMarkImgGradeLimit) {
			Bingo_Log::warning ( __FUNCTION__ . ": this user can not be restored post by mark images cause level too low!" );
			return false;
		}
		return true;
	}
	
	/**
	 * ��ȡ�û����ֻ�ʣ�����
	 * @param int $intUserId
	 * @return int
	 */
	public static function _getAvailablePhoneTimes($intUserId) {
		$actsRet = Util_User::queryActsCtrl ( ( int ) $intUserId, Util_Const::$actsCtrCmdRestorePostByPhone );
		if ($actsRet == 0) {
			Bingo_Log::warning ( __FUNCTION__ . ": [POST RESTORE BY PHONE] The user hits acts_ctrls [$intUserId]!" );
		}
		return $actsRet;
	}
	
	/**
	 * ��ȡ�û�����ʹ�õı�ͼ�Ĵ���
	 * @param int $intUserId
	 * @return int
	 */
	private static function _getAvailableMarkImgTimes($intUserId) {
		$actsRet = Util_User::queryActsCtrl ( ( int ) $intUserId, Util_Const::$actsCtrCmdRestorePostByMarkImg );
		if ($actsRet == 0) {
			Bingo_Log::warning ( __FUNCTION__ . ": [POST RESTORE BY MARK IMG] The user hits acts_ctrls [$intUserId]!" );
		}
		return $actsRet;
	}
	
	/**
	 * ��ȡ�ָ����ӵķ�ʽ��ʣ�����
	 * @param type $intUserId
	 * @param type $intUserLevel
	 * @return type
	 */
	public static function getRestorePostWayLeft($intUserId, $intForumId, $intUserLevel = null) {
		if ( !is_numeric($intUserId) ) {
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$appealAuth = Util_Const::$restorePostWayByManual;
		$arrRestorePostWayLeft = array('mark_left_cnt'=>0,'mobile_left_cnt'=>0,'appeal_auth'=>$appealAuth);
		
		if ( in_array($intForumId, Util_Const::$restorePostAllowManualOnlyForumIds) ) {
			return self::_succRet($arrRestorePostWayLeft);
		}
		
		if ( null === $intUserLevel ) {
			$intUserLevel = Util_User::getUserMaxLevelByUid ( $intUserId );
		}
		if ($intUserLevel === false || $intUserLevel < Util_Const::$userMarkImgGradeLimit || Util_Buffet::isPostMarkImgDeny($intUserId) ) {
			Bingo_Log::warning ( __FUNCTION__ . ": this user can not be restore post by mark images cause level too low!" );
		} else {
			$arrRestorePostWayLeft['mark_left_cnt'] = self::_getAvailableMarkImgTimes($intUserId);
			$appealAuth |= Util_Const::$restorePostWayByMarkimg;
		}
		$arrRestorePostWayLeft['mobile_left_cnt'] = self::_getAvailablePhoneTimes($intUserId);
		$appealAuth |= Util_Const::$restorePostWayByPhone;
		$arrRestorePostWayLeft['appeal_auth'] = $appealAuth;
		return self::_succRet($arrRestorePostWayLeft);
	}

	
	/**
	 * ��ȡͨ����ͼ��ʽ���ڻָ��е����
	 * @param Array $arrParams
	 * @return Array
	 */
	private static function _getAppealingPostInfoByMarkImg($arrParams) {
		$applyStatusInfo = array();
		
		$arrParams['restore_type'] = Util_Const::$appealTypeMarkimg;
		$arrParams['appeal_status'] = Util_Const::$appealStatusAppealling;
		$postAppealRes = Dl_Recycle_Restore::getPostRestoreTasks($arrParams);
		if ($postAppealRes ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			return self::_errRet ( $postAppealRes ['errno'] );
		}
		//@todo ��ͼ׼ȷ���Ƿ��������
		if (!empty($postAppealRes['ret'])) {
			foreach ( $postAppealRes['ret'] as $arrAppealRow ) {
				$appealTime	  = intval($arrAppealRow['appeal_time']);
				if ( empty($applyStatusInfo) ) {
					$applyStatusInfo = $arrAppealRow;
				} else {
					if ( (isset($applyStatusInfo['appeal_time']) && $applyStatusInfo['appeal_time'] < $appealTime ) ) {
						$applyStatusInfo = $arrAppealRow;
					}
				}
			}

			$applyStatusInfo['apply_time'] = date("Y-m-d H:i:s", $applyStatusInfo['appeal_time']);
			$waitTime = 4*3600-(time()-$applyStatusInfo['appeal_time']);
			$waithours = floor($waitTime/3600);
			$waitMinutes = floor(($waitTime-$waithours*3600)/60);
			if ( $waithours < 0 ) {
				$waithours = 0;
			}
			if ( $waitMinutes < 0 ) {
				$waitMinutes = 0;
			}
			$strCutdown = sprintf("%dСʱ%d����", $waithours, $waitMinutes);
			$applyStatusInfo['count_down'] = $strCutdown;
			$applyStatusInfo['status'] = Util_Const::$restorePostStatusWaitMarkImg;
		}
		
		return self::_succRet($applyStatusInfo);
	}
	
	/**
	 * ��ȡ���ӵ��˹���������
	 * @param type $arrParams
	 * @return type
	 */
	private static function _getAppealingPostInfoByManual($arrParams) {
		$manualStatusInfo = array();
		$arrFields = array('reply','status','post_time','op_time','tousu_time');
		$arrParams['status'] = array(Util_Const::$applyRestorePostAgain, Util_Const::$applyRestorePostDelete, Util_Const::$applyRestorePostWait);
		$postAppealRes = Dl_Recycle_Appeal::getPostAppealLists($arrParams, $arrFields);
		if ($postAppealRes ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			return self::_errRet ( $postAppealRes ['errno'] );
		}
		if ( isset($postAppealRes['ret']) && is_array($postAppealRes['ret']) && !empty($postAppealRes['ret']) ) {
			foreach ( $postAppealRes['ret'] as $arrPostManual ) {
				$intAppealTime = strtotime($arrPostManual['tousu_time']);
				$intManualStatus = intval($arrPostManual['status']);
				if ( empty($manualStatusInfo) ) {
					$manualStatusInfo = $arrPostManual;
				} elseif ( strtotime($manualStatusInfo['tousu_time']) <= $intAppealTime && intval($manualStatusInfo['status']) >= $intManualStatus) {
					$manualStatusInfo = $arrPostManual;
				}
			}
		}
		return self::_succRet($manualStatusInfo);
	}
	
	/**
	 * @brief ��uegmis��ȡ���ӵ��˹���������
	 * @param type $arrParams
	 * @return type
	 */
	private static function _getAppealingInfoByManual($arrInput) {
		if ( !isset($arrInput['pid']) || !is_numeric($arrInput['pid']) ) {
			Bingo_Log::warning( __FUNCTION__ . 'input params invalid : ' . serialize($arrInput) );
		}
		
		$manualStatusInfo = array();
		$srvRet = Tieba_Service::call( 'uegmis', 'getPostRecover', array('post_id'=>array($arrInput['pid'])) );
		if ( false === $srvRet && $srvRet['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
			Bingo_Log::warning( __FUNCTION__ . ':' . Util_Log::$strCallServiceFailed . ': [input]' . serialize($arrInput) . ' [output]' . serialize($srvRet));
			return self::_errRet( Tieba_Errcode::ERR_CALL_SERVICE_FAIL );
		}
		if ( !isset($srvRet['ret']) || empty($srvRet['ret']) ) {
			return self::_succRet( $manualStatusInfo );
		}
		
		if ( isset($srvRet['ret']) && is_array($srvRet['ret']) && !empty($srvRet['ret']) ) {
			foreach ( $srvRet['ret'] as $arrRow ) {
				$intAppealTime = intval($arrRow['apply_time']);
				$intPid		   = intval($arrRow['post_id']);
				$intManualStatus = intval($arrRow['op_status']);
				if ( $intManualStatus === Util_Const::$applyRestorePostAutoAgain ) {
					$intManualStatus = Util_Const::$applyRestorePostAgain;
				}
				$arrPostManual = array(
					'status'=> $intManualStatus,
					'reply' => $arrRow['op_reply'],
					'op_time'=> intval( $arrRow['op_time'] ),
					'tousu_time' => date('Y-m-d H:i:s', $intAppealTime)
				);
				if ( empty($manualStatusInfo) ) {
					$manualStatusInfo = $arrPostManual;
				} elseif (  $manualStatusInfo['status'] !== Util_Const::$applyRestorePostWait && strtotime($manualStatusInfo['tousu_time']) <= $intAppealTime && intval($manualStatusInfo['status']) >= $intManualStatus) {
					$manualStatusInfo = $arrPostManual;
				}
			}
		}
		return self::_succRet($manualStatusInfo);
	}
	
	/**
	 * ����ָ��к��˹��ָ�ʧ�ܵ����ӵ���Ϣ
	 * @param Array $arrApplyStatusByMark
	 * @param Array $arrApplyStatusByManul
	 * @return Array
	 */
	private static function _decorateApplyStatusInfo($arrApplyStatusByMark, $arrApplyStatusByManul, $intUserId, $intOpUserType, $bolIsUserMarkImgLevelEnough, $bolRestorePostAllowManualOnly) {
		$arrApplyStatusInfo = array();
		if ( !empty($arrApplyStatusByMark) && is_array($arrApplyStatusByMark) ) {
			$arrApplyStatusInfo['count_down'] = $arrApplyStatusByMark['count_down'];
			$arrApplyStatusInfo['apply_time'] = $arrApplyStatusByMark['apply_time'];
			$arrApplyStatusInfo['status'] = Util_Const::$restorePostStatusWaitMarkImg;
		} 
		else if ( !empty($arrApplyStatusByManul) && is_array($arrApplyStatusByManul) ) {
			$intStatus = intval($arrApplyStatusByManul['status']);
			$arrApplyStatusInfo['apply_time'] = date('Y-m-d H:i:s', strtotime($arrApplyStatusByManul['tousu_time']));
			
			if ( $intStatus === Util_Const::$applyRestorePostAgain || $intStatus === Util_Const::$applyRestorePostDelete ) {
				$arrApplyStatusInfo['apply_fail_time'] = date('Y-m-d H:i:s', $arrApplyStatusByManul['op_time']);
				$arrApplyStatusInfo['status'] = Util_Const::$restorePostStatusManualFail;
			} else {
				$arrApplyStatusInfo['apply_fail_time'] = '';
				$arrApplyStatusInfo['status'] = Util_Const::$restorePostStatusWaitManual;
			}
			$arrApplyStatusInfo['apply_fail_reason'] = $arrApplyStatusByManul['reply'];
		}
		
		//���ݲ�ͬ��status���Ƿ���Ҫ�� �ֻ����ȿ��� �� ��ͼ���ȿ���
		if ( !$bolRestorePostAllowManualOnly && !empty($arrApplyStatusInfo) ) {
			$status = intval($arrApplyStatusInfo['status']);
			//���ֻ�ʣ�����
			if ( $status === Util_Const::$restorePostStatusWaitMarkImg || $status === Util_Const::$restorePostStatusWaitManual ) {
				if ( $intOpUserType === Util_Const::$opUserTypeMachine ) {
					$arrApplyStatusInfo['phone_times'] = self::_getAvailablePhoneTimes($intUserId);
				}
			}
			//���ͼʣ�����
			if ($intOpUserType === Util_Const::$opUserTypeMachine && $bolIsUserMarkImgLevelEnough && 
					$status === Util_Const::$restorePostStatusWaitManual && !Util_Buffet::isPostMarkImgDeny($intUserId) ) {
				$arrApplyStatusInfo['markimg_times'] = self::_getAvailableMarkImgTimes($intUserId);
			}
		}
		
		return $arrApplyStatusInfo;
	}
	
	/**
	 * ��ȡɾ�����û������� ������pm��machine �����ǷǷ�����
	 * @param type $arrInput
	 * @return type
	 */
	private static function _getDelPostUserTypeAndMonitor($arrInput) {
		$intPostId = $arrInput['pid'];
		$intThreadId = $arrInput['tid'];

		//���ӵ�ɾ����Ϣ
		$arrDelPostParams = array(array('pid'=>$intPostId,'tid'=>$intThreadId));
		$arrSrvDelPostInfo = self::getDelPostInfo($arrDelPostParams);
		if ( $arrSrvDelPostInfo['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
			return self::_errRet($arrSrvDelPostInfo['errno']);
		}
		
		if ( !isset($arrSrvDelPostInfo['ret'][$intPostId]) ) {
			return self::_errRet(Util_Errno::$errPostNotDeleted);
		}
		
		$arrPostDelInfo = $arrSrvDelPostInfo['ret'][$intPostId];
		/*
		if (empty($arrPostDelInfo['del_username']) && strstr ( $arrPostDelInfo['call_from'], 'bawu' ) === false) {
			$arrPostDelInfo['call_from']= 'machine_unknow';
		}
		 */
		/*
		if ( empty($arrPostDelInfo['del_username']) ) {
			return self::_errRet(Util_Errno::$errPostNotDeleted);
		}
		 */

		$strCallFrom = $arrPostDelInfo['call_from'];
		$intMonitorType = 0;
		if ( isset($arrPostDelInfo['monitor_type']) && !is_null($arrPostDelInfo['monitor_type']) ) {
			$intMonitorType = intval( $arrPostDelInfo['monitor_type'] );
		}
		
		//ɾ�����ӵ��û���uid
		$intDelUid = null;
		$strDelUserName = $arrPostDelInfo['del_username'];
		if ( !empty($strDelUserName) ) {
			$mixDelUserInfo = Util_User::getUidByUnames(array($strDelUserName));
			if ( false === $mixDelUserInfo || !isset($mixDelUserInfo[0]['user_id']) || empty($mixDelUserInfo[0]['user_id'])) {
				Bingo_Log::warning( __CLASS__ . "[getUidByUnames] failed [uname]$strDelUserName" );
				//return self::_errRet(Util_Errno::$errDelUidQueryFail);
			} else {
				$intDelUid = intval($mixDelUserInfo[0]['user_id']);
			}
			
		}
		
		//ɾ�����ӵ��ʻ�����
		$intDelUserType = Util_Appeal::getAppealOpType($intDelUid, $strCallFrom);
		if ( $intDelUserType !== Util_Const::$opUserTypeMachine && $intDelUserType !== Util_Const::$opUserTypePm ) {
			return self::_errRet(Util_const::$errPostDeltypeNotAllowRerstore);
		}
		return self::_succRet( array('op_user_type'=>$intDelUserType, 'monitor_type'=>$intMonitorType) );
	}

	/**
	 * ��ȡ���ӵ�״̬��Ϣ
	 * @todo ¥��¥���������б�ҳ�͸��� ״̬��ѯ�������� FEֱ�ӳ������ok
	 * @param type $arrInput
	 * @return type
	 */
	public static function getPostApplyStatusInfo($arrInput) {
		if (! isset ( $arrInput ['input'] )) {
			Bingo_Log::warning ( "input params invalid. [" . print_r ( $arrInput, true ) . "]" );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		$input = Tieba_Service::getArrayParams ( $arrInput, 'input' );
		
		if (! self::_init ()) {
			return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
		}
		
		if (! Util_Params::checkNum ( $input, 'user_id' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		if (! Util_Params::checkNum ( $input, 'pid' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		if (! Util_Params::checkNum ( $input, 'tid' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		$intUserId = intval($input['user_id']);
		
		$intPostId		= intval($input['pid']);
		$arrSrvPostInfo = Service_Recycle_Post::getPostInfoByPid($intPostId);
		if ( $arrSrvPostInfo['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
			Bingo_Log::warning( __CLASS__ . 'getPostInfoByPid error' );
			return self::_errRet ( $arrSrvPostInfo['errno'] );
		}
		if ( empty($arrSrvPostInfo['ret']) ) {
			Bingo_Log::warning( __CLASS__ . 'getPostInfoByPid ret empty' );
			return self::_errRet ( Util_Errno::$errQueryPostInfoFail );
		}
		$intForumId = intval($arrSrvPostInfo['ret']['forum_id']);
		
		$bolRestorePostAllowManualOnly = false;
		if ( in_array($intForumId, Util_Const::$restorePostAllowManualOnlyForumIds) ) {
			$bolRestorePostAllowManualOnly = true;
		}
		
		//�����ж��ǹ���Աɾ������ϵͳɾ��(����Աɾ��ֻ�����˹�����һ�ַ�ʽ)
		$srvOpUsertype = self::_getDelPostUserTypeAndMonitor($input);
		if ($srvOpUsertype ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			return self::_errRet ( $srvOpUsertype ['errno'] );
		}
		$intOpUserType = $srvOpUsertype['ret']['op_user_type'];
		//����monitor_type�İ�������ֻ�ṩ�˹�����
		$intMonitorType = intval($srvOpUsertype['ret']['monitor_type']);
		if ( in_array($intMonitorType, Util_Const::$manualMonitorType) ) {
			$bolRestorePostAllowManualOnly = true;
		}
		//���������ָ��������л���ʹ�õ���� ֱ�Ӱ�opgroup��Ϊadmin========
		if ( Util_Buffet::isPostSelfHelpDeny($intUserId) ) {
			$bolRestorePostAllowManualOnly = true;
		}
		//������ٻ�����ֻ���˹�����
		$createTime = intval($arrSrvPostInfo['ret']['now_time']);
		$res = Page_Post_Restore::checkIfMutiRecall($intPostId, $createTime);
		if($res){
			$bolRestorePostAllowManualOnly = true;
		}
		$applyInfoByMarkImg = array();
		if ( $intOpUserType === Util_Const::$opUserTypeMachine && !$bolRestorePostAllowManualOnly ) {
			$bolIsUserMarkImgLevelEnough = self::_isUserMarkImgLevelEnough($intUserId);
			if ( $bolIsUserMarkImgLevelEnough ) {
				//��ͼ�ָ���
				$dlApplyInfoByMarkImg = self::_getAppealingPostInfoByMarkImg($input);
				if ($dlApplyInfoByMarkImg ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
					return self::_errRet ( $dlApplyInfoByMarkImg ['errno'] );
				}

				if ( !empty($dlApplyInfoByMarkImg['ret']) ) {
					$applyInfoByMarkImg = $dlApplyInfoByMarkImg['ret'];
				}	
			}
		}

		//�˹��ָ��л��˹��ָ�ʧ��
		$applyInfoByManul = array();
		if ( empty($applyInfoByMarkImg) ) {
			//$dlApplyInfoByManual = self::_getAppealingPostInfoByManual($input);
			$dlApplyInfoByManual = self::_getAppealingInfoByManual($input);
			if ($dlApplyInfoByManual ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				return self::_errRet ( $dlApplyInfoByManual ['errno'] );
			}
			if ( !empty($dlApplyInfoByManual['ret']) ) {
				$applyInfoByManul = $dlApplyInfoByManual['ret'];
			}
		}
		
		$arrApplyStatusInfo = self::_decorateApplyStatusInfo($applyInfoByMarkImg, $applyInfoByManul, $intUserId, $intOpUserType, $bolIsUserMarkImgLevelEnough, $bolRestorePostAllowManualOnly);
		return self::_succRet($arrApplyStatusInfo);
	}
	
	/**
	 * ��������Ƿ��ڵȴ��ظ��� ��ͼ ������ �˹�����
	 * @param type $arrInput
	 * @return type
	 */
	public static function isPostRestoreStatusWait($arrInput) {
		if (! isset ( $arrInput ['input'] )) {
			Bingo_Log::warning ( "input params invalid. [" . print_r ( $arrInput, true ) . "]" );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		$input = Tieba_Service::getArrayParams ( $arrInput, 'input' );
		
		if (! self::_init ()) {
			return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
		}
		
		if (! Util_Params::checkNum ( $input, 'user_id' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		if (! Util_Params::checkNum ( $input, 'pid' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		if (! Util_Params::checkNum ( $input, 'tid' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		if (! Util_Params::checkNum ( $input, 'user_level' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		$intUserLevel = intval($input['user_level']);
		if ( $intUserLevel >= Util_Const::$userMarkImgGradeLimit) {
			//��ͼ�ָ���
			$dlApplyInfoByMarkImg = self::_getAppealingPostInfoByMarkImg($input);
			if ($dlApplyInfoByMarkImg ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				return self::_errRet ( $dlApplyInfoByMarkImg ['errno'] );
			}
			
			if ( !empty($dlApplyInfoByMarkImg['ret']) ) {
				return self::_succRet(true);
			}	
		}

		//�˹��ָ���
		//$dlApplyInfoByManual = self::_getAppealingPostInfoByManual($input);
		$dlApplyInfoByManual = self::_getAppealingInfoByManual($input);
		if ($dlApplyInfoByManual ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			return self::_errRet ( $dlApplyInfoByManual ['errno'] );
		}
		if ( !empty($dlApplyInfoByManual['ret']) && intval($dlApplyInfoByManual['ret']['status']) === Util_Const::$applyRestorePostWait ) {
			return self::_succRet(true);
		}
		
		return self::_succRet(false);

	}
	/**
	 * @param $arrInput
	 * @return 
	 * @desc �ж��Լ�ɾ���������Ƿ���Իָ�
	 */
	public static function checkIsPostCanRestore2($arrInput) {
		
		if (! Util_Params::checkNum ( $arrInput, 'pid' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		if (! Util_Params::checkNum ( $arrInput, 'tid' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		$intPostId		= intval($arrInput['pid']);
		$intThreadId	= intval($arrInput['tid']);
		$intUserId      = intval($arrInput['user_id']);
		
		
		$inPost = array(
			'0' => $intPostId ,
		);
		
		$outPost = Util_Post::getPostInfoByPids($inPost);
		
		$threadIdCheck = isset( $outPost['0']['thread_id'] ) ? $outPost['0']['thread_id'] : false ;
		
		if( false !== $threadIdCheck && $intThreadId != $threadIdCheck )
		{
			Bingo_Log::warning ( __CLASS__ . ':ERR[tid pid not match] TID['.serialize($intThreadId).'] PID['.serialize($intPostId).']' );
			return self::_errRet( Util_Errno::$errRestorePostIllegal );
		}
		
		//У�������ǲ����û��Լ���
		//$arrPostInfo = Util_Post::getPostInfoByPids(array($intPostId));
		$arrPostInfo = $outPost ;
		
		if ( empty($arrPostInfo) ) {
			return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
		}
		$intPostUid = isset($arrPostInfo[0]['user_id']) ? intval($arrPostInfo[0]['user_id']):null;
		if ( null === $intPostUid || $intPostUid !== $intUserId  ) {
			return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
		}
		
		//¥��¥���Ӳ��ָܻ�
		$arrDuplexInfo = Util_Post::getClassifyPostAttributeByPids( array($intPostId) );
		if (isset($arrDuplexInfo[$intPostId])) {
			if (  !$arrDuplexInfo[$intPostId]['is_valid'] ) { //�Ƿ�
				return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
			} 
			/* getCommentInfo���ص�is_post_delete��׼ �˶�ע�͵�
			elseif ( !$arrDuplexInfo[$intPostId]['is_thread_deleted'] && !$arrDuplexInfo[$intPostId]['is_post_deleted'] 
						&& !$arrDuplexInfo[$intPostId]['is_comment_deleted'] ) { //û�б�ɾ��
				return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
			}
			 */ 
			//¥��¥����һ
			elseif ( $arrDuplexInfo[$intPostId]['is_comment'] && ($arrDuplexInfo[$intPostId]['is_post_deleted'] || 
						$arrDuplexInfo[$intPostId]['is_thread_deleted']) ) {
				return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
			} //¥��¥���ζ�
			elseif ( $arrDuplexInfo[$intPostId]['is_post'] && $arrDuplexInfo[$intPostId]['is_thread_deleted'] ) {
				return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
			}
			if(!$arrDuplexInfo[$intPostId]['is_thread']) 	{
				return self::_errRet(100000);//tmp
			}
		}
		
		//���ӵ�ɾ����Ϣ
		$arrDelPostParams = array(array('pid'=>$intPostId,'tid'=>$intThreadId));
		$arrSrvDelPostInfo = Service_Recycle_Post::getDelPostInfo($arrDelPostParams);
		if ( $arrSrvDelPostInfo['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
			return self::_errRet($arrSrvDelPostInfo['errno']);
		}
		if ( !isset($arrSrvDelPostInfo['ret'][$intPostId]) ) {
			return self::_errRet(Util_Errno::$errPostNotDeleted);
		}
		
		$arrPostDelInfo = $arrSrvDelPostInfo['ret'][$intPostId];
		
		//ɾ�����ӵ��û���uid
		$intDelUid = null;
		$strDelUserName = $arrPostDelInfo['del_username'];
		if ( !empty($strDelUserName) ) {
			$mixDelUserInfo = Util_User::getUidByUnames(array($strDelUserName));
			if ( false === $mixDelUserInfo || !isset($mixDelUserInfo[0]['user_id']) || empty($mixDelUserInfo[0]['user_id'])) {
				Bingo_Log::warning( __CLASS__ . "[getUidByUnames] failed [uname]$strDelUserName" );
				//return self::_errRet(Util_Errno::$errDelUidQueryFail);
			} else {
				$intDelUid = intval($mixDelUserInfo[0]['user_id']);
			}
		}
		if($intDelUid != $intUserId) {
			return self::_errRet(Util_Errno::$errOpUserTypeDesc);
		}
		//return self::_succRet(true);
		$arrRes = array(
			'ret' => true,
			'opUserType'=> $intDelUserType,
			'postDelInfo' => $arrPostDelInfo,
			'monitor_type'=> $intMonitorType,	
		);
		return self::_succRet($arrRes);
	}
	/**
	 * ��������Ƿ�������ָ�
	 * @param type $arrInput
	 * @return type
	 */
	public static function checkIsPostCanRestore($arrInput, $bolReturnOpUsertype = false) {
		
		if (! Util_Params::checkNum ( $arrInput, 'pid' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		if (! Util_Params::checkNum ( $arrInput, 'tid' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		$intPostId		= intval($arrInput['pid']);
		$intThreadId	= intval($arrInput['tid']);
		$intUserId      = intval($arrInput['user_id']);
		
		/*
		 * ��� tid , pid �Ƿ�ƥ��
		 * ��ѯʧ�ܿ���ԣ�����false , Ĭ��ƥ��
		 * add by qmy
		 * 
		 * checkPostInfo �ӿ���bug��������
		 */
		/*
		$mixRet = Util_Post::checkPostInfo($intThreadId , $intPostId);
		if( false !== $mixRet && Util_Post::IS_TID_PID_MATCH != $mixRet )
		{
			Bingo_Log::warning ( __CLASS__ . ':ERR[tid pid not match] TID['.serialize($intThreadId).'] PID['.serialize($intPostId).']' );
			return self::_errRet( Util_Errno::$errRestorePostIllegal );
		}
		*/
		
		/*
		 * ��� tid , pid �Ƿ�ƥ��
		 * ��ѯʧ�ܿ���ԣ�����false , Ĭ��ƥ��
		 * by qmy
		 */
		
		$inPost = array(
			'0' => $intPostId ,
		);
		
		$outPost = Util_Post::getPostInfoByPids($inPost);
		
		$threadIdCheck = isset( $outPost['0']['thread_id'] ) ? $outPost['0']['thread_id'] : false ;
		
		if( false !== $threadIdCheck && $intThreadId != $threadIdCheck )
		{
			Bingo_Log::warning ( __CLASS__ . ':ERR[tid pid not match] TID['.serialize($intThreadId).'] PID['.serialize($intPostId).']' );
			return self::_errRet( Util_Errno::$errRestorePostIllegal );
		}
		
		//У�������ǲ����û��Լ���
		//$arrPostInfo = Util_Post::getPostInfoByPids(array($intPostId));
		$arrPostInfo = $outPost ;
		
		if ( empty($arrPostInfo) ) {
			return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
		}
		$intPostUid = isset($arrPostInfo[0]['user_id']) ? intval($arrPostInfo[0]['user_id']):null;
		if ( null === $intPostUid || $intPostUid !== $intUserId  ) {
			return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
		}
		
		//¥��¥���Ӳ��ָܻ�
		$arrDuplexInfo = Util_Post::getClassifyPostAttributeByPids( array($intPostId) );
		if (isset($arrDuplexInfo[$intPostId])) {
			if (  !$arrDuplexInfo[$intPostId]['is_valid'] ) { //�Ƿ�
				return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
			} 
			/* getCommentInfo���ص�is_post_delete��׼ �˶�ע�͵�
			elseif ( !$arrDuplexInfo[$intPostId]['is_thread_deleted'] && !$arrDuplexInfo[$intPostId]['is_post_deleted'] 
						&& !$arrDuplexInfo[$intPostId]['is_comment_deleted'] ) { //û�б�ɾ��
				return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
			}
			 */ 
			//¥��¥����һ
			elseif ( $arrDuplexInfo[$intPostId]['is_comment'] && ($arrDuplexInfo[$intPostId]['is_post_deleted'] || 
						$arrDuplexInfo[$intPostId]['is_thread_deleted']) ) {
				return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
			} //¥��¥���ζ�
			elseif ( $arrDuplexInfo[$intPostId]['is_post'] && $arrDuplexInfo[$intPostId]['is_thread_deleted'] ) {
				return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
			}
		}
		
		//���ӵ�ɾ����Ϣ
		$arrDelPostParams = array(array('pid'=>$intPostId,'tid'=>$intThreadId));
		$arrSrvDelPostInfo = Service_Recycle_Post::getDelPostInfo($arrDelPostParams);
		if ( $arrSrvDelPostInfo['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
			return self::_errRet($arrSrvDelPostInfo['errno']);
		}
		if ( !isset($arrSrvDelPostInfo['ret'][$intPostId]) ) {
			return self::_errRet(Util_Errno::$errPostNotDeleted);
		}
		
		$arrPostDelInfo = $arrSrvDelPostInfo['ret'][$intPostId];
		/*
		if ( empty($arrPostDelInfo['del_username']) && strstr ( $arrPostDelInfo['call_from'], 'bawu' ) === false) {
			$arrPostDelInfo['call_from'] = 'machine_unknow';
		}
		 */
		/*
		$arrPostDelInfo = $arrSrvDelPostInfo['ret'][$intPostId];
		if ( empty($arrPostDelInfo['delete_time']) || empty($arrPostDelInfo['del_username']) ) {
			return self::_errRet(Util_Errno::$errPostNotDeleted);
		}
		if ($strTab == self::FILTER_BEFORE_THREE_MONTH_AGO_FLAG  && intval($arrPostDelInfo['delete_time']) < time () - 3 * 30 * 3600 * 24 ) {
			return self::_errRet(Util_Errno::$errPostDeleteThreeMonthAgo);
		}
		 */
		$strCallFrom = $arrPostDelInfo['call_from'];
		
		$intMonitorType = 0;
		if ( isset($arrPostDelInfo['monitor_type']) && !is_null($arrPostDelInfo['monitor_type']) ) {
			$intMonitorType = intval($arrPostDelInfo['monitor_type']);
		}
		
		//ɾ�����ӵ��û���uid
		$intDelUid = null;
		$strDelUserName = $arrPostDelInfo['del_username'];
		if ( !empty($strDelUserName) ) {
			$mixDelUserInfo = Util_User::getUidByUnames(array($strDelUserName));
			if ( false === $mixDelUserInfo || !isset($mixDelUserInfo[0]['user_id']) || empty($mixDelUserInfo[0]['user_id'])) {
				Bingo_Log::warning( __CLASS__ . "[getUidByUnames] failed [uname]$strDelUserName" );
				//return self::_errRet(Util_Errno::$errDelUidQueryFail);
			} else {
				$intDelUid = intval($mixDelUserInfo[0]['user_id']);
			}
		}
		$arrPostDelInfo['del_uid'] = $intDelUid;//add by fengzhen
		//ɾ�����ӵ��ʻ�����
		$intDelUserType = Util_Appeal::getAppealOpType($intDelUid, $strCallFrom);
		if ( $intDelUserType !== Util_Const::$opUserTypeMachine && $intDelUserType !== Util_Const::$opUserTypePm  ) {
			return self::_errRet(Util_Errno::$errPostDeltypeNotAllowRerstore);
		}
		
		if ( $bolReturnOpUsertype ) {
			//���������ָ��������л���ʹ�õ���� ֱ�Ӱ�opgroup��Ϊadmin========
			if ( Util_Buffet::isPostSelfHelpDeny($intUserId) ) {
				$intDelUserType = Util_Const::$opUserTypePm;
			}
			$arrRes = array(
				'ret'=>true, 
				'opUserType'=>$intDelUserType, 
				'postDelInfo'=>$arrPostDelInfo, 
				'monitor_type'=>$intMonitorType
			);
			return self::_succRet($arrRes);
		}
		
		return self::_succRet(true);
	}
	
	/**
	 * �ָ�����
	 * @param type $arrPost
	 * @return type
	 */
	private static function _restorePost($arrPost) {
		$arrInput = array(
				'op_uid'	=> 915079260,
				'op_uname'	=> '�����Զ�����',
				'post_id'	=> intval($arrPost['pid']),
				'call_from'	=> 'machine_pmc',
				'op_ip'		=> Bingo_Http_Ip::ip2long(Bingo_Http_Ip::getUserClientIp()),
				'transform'	=> 1
			);
		$srvRet = Tieba_Service::call('post', 'recoverPost', array('req'=>$arrInput));
		if ( $srvRet['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
			return self::_errRet($srvRet['errno']);
		}
		return self::_succRet(true);
	}
	
	
	/**
	 * ��ȡͨ����ͼ��ʽ���ڻָ��е����
	 * @param Array $arrParams pids user_id
	 * @return Array
	 */
	public static function getAppealingPostInfoByMarkImg($arrParams) {
		if ( !isset($arrParams['user_id']) || !is_numeric($arrParams['user_id']) ||
				!isset($arrParams['pid']) || !is_array($arrParams['pid']) || empty($arrParams['pid']) ) {
			Bingo_Log::warning( __FUNCTION__ . 'prams invalid : ' . serialize($arrParams) );
		}
		
		$arrAppealingPidsInfoByMarkImg = array();
		
		$arrParams['restore_type'] = Util_Const::$appealTypeMarkimg;
		$arrParams['appeal_status'] = Util_Const::$appealStatusAppealling;
		$postAppealRes = Dl_Recycle_Restore::getPostRestoreTasks($arrParams, array('pid'));
		if ($postAppealRes ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			return self::_errRet ( $postAppealRes ['errno'] );
		}
		//@todo ��ͼ׼ȷ���Ƿ��������
		if (!empty($postAppealRes['ret'])) {
			foreach ( $postAppealRes['ret'] as $arrAppealRow ) {
				array_push($arrAppealingPidsInfoByMarkImg, intval($arrAppealRow['pid']) );
			}
		}
		
		return self::_succRet($arrAppealingPidsInfoByMarkImg);
	}
	
	
	
//======================restore post by phone code =======================
	/**
	 * �ֻ���֤�뷽ʽ�ָ�����
	 * @param type $arrInput
	 * @return type
	 */
	public static function restorePostByPhone($arrInput) {
		if (! isset ( $arrInput ['input'] )) {
			Bingo_Log::warning ( "input params invalid. [" . print_r ( $arrInput, true ) . "]" );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		$input = Tieba_Service::getArrayParams ( $arrInput, 'input' );
		
		if (! self::_init ()) {
			return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
		}
		
		$arrParams = array('tid','pid','del_user_type','user_id');
		if ( !Util_Params::checkString($input, 'user_name') ) {
			Bingo_Log::warning ( __FUNCTION__ . ":check user name error!" );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		foreach ( $arrParams as $param ) {
			if (! Util_Params::checkNum ( $input, $param )) {
				Bingo_Log::warning ( __FUNCTION__ . ":check $param error!" );
				return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
			}
		}
		
		//�ָ�����
		$srvRestore = self::_restorePost($input);
		if ( $srvRestore['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			return self::_errRet($srvRestore['errno']);
		}
		if ( $srvRestore['ret'] ) {
			$input['appeal_status'] = Util_Const::$restorePostStatusSuccess;
		} else {
			$input['appeal_status'] = Util_Const::$restorePostStatusFailed;
		}
		
		//�����û�������¼
		$input['restore_type'] = Util_Const::$appealTypePhone;
		$input['appeal_time']  = time();
		$input['restore_time'] = time();
		
		$userId = $input['user_id'];
		$postId = $input['pid'];
		$threadId = $input['tid'];
		$arrPhoneInput = array(
				'user_id' => $userId,
				'pid'	  => $postId,
				'tid'	  => $threadId,
				'restore_type' => Util_Const::$appealTypePhone
			);
		$dlPhoneResult = Dl_Recycle_Restore::getPostRestoreTasks($arrPhoneInput);
		if ($dlPhoneResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( __FUNCTION__ . ":getPostRestoreTasks error.[" . $dlPhoneResult ['errno'] . ']' );
			return self::_errRet ( $dlPhoneResult ['errno'] );
		}
		$intDbId = null;
		$intManualTimes = 0;
		if ( isset($dlPhoneResult['ret']) && !empty($dlPhoneResult['ret']) ) {
			$intDbId = intval($dlPhoneResult['ret'][0]['id']);
			$intManualTimes = intval($dlPhoneResult['ret'][0]['manual_times']);
		}
		//�Ѿ����� �͸��³����µ�
		if ( null === $intDbId ) {
			$dlAddRecord = Dl_Recycle_Restore::addPostRestoreTasks(array($input));
			if ( $dlAddRecord['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
				return self::_errRet($dlAddRecord['errno']);
			}
		} else {
			$dlUpdateInput = array();
			$dlUpdateInput['id'] = $intDbId;
			$dlUpdateInput['user_id'] = $userId;
			$dlUpdateInput ['appeal_time'] = time ();
			$dlUpdateInput ['appeal_status'] = $input['appeal_status'];
			$dlUpdateInput ['manual_times'] = $intManualTimes+1;
			$dlResult = Dl_Recycle_Restore::updatePostRestoreTask($dlUpdateInput);
			if ($dlResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				Bingo_Log::warning ( __FUNCTION__ . ":update restore post by phone error.[" . $dlResult ['errno'] . ']' );
				return self::_errRet ( $dlResult ['errno'] );
			}
		}
		$phone = strval(Tieba_Session_Socket::getMobilephone());
		$isBindPhone = strlen($phone) == 0 ? false : true;
		if(!$isBindPhone) {
			Bingo_Log::warning ( __FUNCTION__ ."not bind phone");
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);//gen xiamian yizhi
		}
		$actsRetSpec = Util_User::submitPhoneActsCtrl ( $phone, Util_Const::$actsCtrlCmdPostSelfVcode);
		$actsRet = Util_User::submitActsCtrl ( ( int ) $input['user_id'], Util_Const::$actsCtrCmdRestorePostByPhone );
		if ($actsRet === false) {
			Bingo_Log::warning ( __FUNCTION__ . ": user can not submit cause acts_ctrl [{$input['user_id']}]!" );
			return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL );
		}
		else {
			$actsRet = Util_User::queryActsCtrl ( ( int ) $input['user_id'], Util_Const::$actsCtrCmdRestorePostByPhone );
		}

		return self::_succRet ( array ('actsStatus' => $actsRet ) );
	}
	
	
	//======================restore post by mark img =======================
	/**
	 * ����Ѿ�����һ�����õı�ͼ���񣬾�������ɣ�������һ��û���½������񣬻�û�г�������´ν����������������
	 */
	public static function getUserHasNoFinishedMarkTask($arrInput) {
		if (! isset ( $arrInput ['input'] )) {
			Bingo_Log::warning ( "input params invalid. [" . print_r ( $arrInput, true ) . "]" );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		$input = Tieba_Service::getArrayParams ( $arrInput, 'input' );
		
		if (! self::_init ()) {
			return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
		}
		
		if (! Util_Params::checkNum ( $input, 'user_id' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		if (! Util_Params::checkNum ( $input, 'pid' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		if (! Util_Params::checkNum ( $input, 'tid' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		$intUserId	 = $input ['user_id'];
		$intPostId	 = $input ['pid'];
		$intThreadId = $input['tid'];
		
		$dlResult = Dl_Recycle_Markimg::getMarkImgTasksByUserIdAndPostId ( $input );
		
		if ($dlResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS || ! isset ( $dlResult ['ret'] ['tasks'] )) {
			return self::_errRet ( $dlResult ['errno'] );
		}
		
		$markTasks = $dlResult ['ret'] ['tasks'];
		
		if (empty ( $markTasks )) {
			return self::_succRet ( false );
		}
		
		$flag = false;
		$arrMarkTask = array();
		foreach ( $markTasks as $markTask ) {
			if (intval ( $markTask ['op_status'] ) === Util_Const::$markImgStatusNew) {
				$flag = true;
				$arrMarkTask = $markTask;
				break;
			}
		}
		
		if ($flag === false) {
			return self::_succRet ( false );
		}
		
		$markTaskId = $arrMarkTask ['task_id'];
		
		$dlInput = array ('task_id' => $markTaskId );
		$dlResult = Dl_Pmc_Pmc::findMarkImgsByTaskId ( $dlInput );
		
		if ($dlResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS || ! isset ( $dlResult ['ret'] ['images'] )) {
			return self::_errRet ( $dlResult ['errno'] );
		}
		
		if (empty ( $dlResult ['ret'] ['images'] )) {
			// if we found this task has no images, get images again;
			$dlResult4 = Dl_Pmc_Pmc::getRandImages ();
			
			if ($dlResult4 ['errno'] !== Tieba_Errcode::ERR_SUCCESS || ! isset ( $dlResult4 ['ret'] ['images'] )) {
				return self::_errRet ( $dlResult4 ['errno'] );
			}
			
			$randImages = $dlResult4 ['ret'] ['images'];
			foreach ( $randImages as $randImage ) {
				$dlResult2 = Dl_Pmc_Pmc::addMarkImg ( array (
						'taskId' => $markTaskId, 
						'imgId' => $randImage ['img_id'] ) );
				if ($dlResult2 ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
					return self::_errRet ( $dlResult2 ['errno'] );
				}
			}
		}
		else {
			$imageIds = array ();
			foreach ( $dlResult ['ret'] ['images'] as $markImg ) {
				$imageIds [] = $markImg ['img_id'];
			}
			
			$dlResult3 = Dl_Pmc_Pmc::findImagesByImageIds ( array (
					'image_ids' => $imageIds ) );
			if ($dlResult3 ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				return self::_errRet ( $dlResult3 ['errno'] );
			}
			if (empty ( $dlResult3 ['ret'] ['images'] )) {
				return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL );
			}
			$randImages = $dlResult3 ['ret'] ['images'];
		}
		
		// get image by image ids
		$blackCount = 0;
		foreach ( $randImages as $randImage ) {
			if ($randImage ['base_type'] == Util_Const::$baseTypeBlack) {
				$blackCount ++;
			}
		}
		
		$ret ['taskId'] = $markTaskId;
		$ret ['images'] = $randImages;
		$ret ['blackCount'] = $blackCount;
		
		return self::_succRet ( $ret );
	}
	
	/**
	 * �����ͼʱ��û���ҵ����õ�������ô������һ��
	 */
	public static function getImageList($arrInput) {
		if (! isset ( $arrInput ['input'] )) {
			Bingo_Log::warning ( "input params invalid. [" . print_r ( $arrInput, true ) . "]" );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		$input = Tieba_Service::getArrayParams ( $arrInput, 'input' );
		 
		if (! self::_init ()) {
			return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
		}
		
		if (! Util_Params::checkNum ( $input, 'user_id' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		if (! Util_Params::checkNum ( $input, 'pid' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		if (! Util_Params::checkNum ( $input, 'tid' )) {
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		$userId = $input ['user_id'];
		$postId = $input ['pid'];
		$threadId = $input['tid'];
		
		$dlResult = Dl_Pmc_Pmc::getRandImages ();
		
		if ($dlResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS || ! isset ( $dlResult ['ret'] ['images'] )) {
			return self::_errRet ( $dlResult ['errno'] );
		}
		
		$randImages = $dlResult ['ret'] ['images'];
		
		$dlResult = Dl_Recycle_Markimg::addMarkTask ( $input );
		if ($dlResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS || ! isset ( $dlResult ['ret'] ['taskId'] )) {
			return self::_errRet ( $dlResult ['errno'] );
		}
		
		$taskId = $dlResult ['ret'] ['taskId'];
		
		$blackCount = 0;
		foreach ( $randImages as $randImage ) {
			if ($randImage ['base_type'] == Util_Const::$baseTypeBlack) {
				$blackCount ++;
			}
			$dlResult = Dl_Pmc_Pmc::addMarkImg ( array (
					'taskId' => $taskId, 
					'imgId' => $randImage ['img_id'] ) );
			if ($dlResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				return self::_errRet ( $dlResult ['errno'] );
			}
		}
		
		$ret ['taskId'] = $taskId;
		$ret ['images'] = $randImages;
		$ret ['blackCount'] = $blackCount;
		
		return self::_succRet ( $ret );
	}
	
	
	/**
	 * �ύ��ͼ���������Ȩ�޿��ƣ��ύʱ���ŵ�ui��ȥ�����˽ӿڲ���¶���ⲿ�û�
	 */
	public static function submitMarkRestorePost($arrInput) {
		if (! isset ( $arrInput ['input'] )) {
			Bingo_Log::warning ( "input params invalid. [" . print_r ( $arrInput, true ) . "]" );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		$input = Tieba_Service::getArrayParams ( $arrInput, 'input' );
		
		if (! self::_init ()) {
			return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
		}
		
		if (! Util_Params::checkNum ( $input, 'task_id' )) {
			Bingo_Log::warning ( __FUNCTION__ . ":check task_id error!" );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		if (! Util_Params::checkNum ( $input, 'user_id' )) {
			Bingo_Log::warning ( __FUNCTION__ . ":check user_id error!" );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		if (! Util_Params::checkNum ( $input, 'pid' )) {
			Bingo_Log::warning ( __FUNCTION__ . ":check msg_id error!" );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		if (! Util_Params::checkNum ( $input, 'tid' )) {
			Bingo_Log::warning ( __FUNCTION__ . ":check msg_id error!" );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		if (! Util_Params::checkArray ( $input, 'img', false )) {
			Bingo_Log::warning ( __FUNCTION__ . ":check img error!" );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		$ret = Util_Const::$markDeny;
		
		$taskId = $input ['task_id'];
		$userId = $input ['user_id'];
		$postId = $input ['pid'];
		$threadId = $input['tid'];
		$markRets = $input ['img'];
		$strUname = $input['user_name'];
		
		$actsRet = Util_User::submitActsCtrl ( ( int ) $userId, Util_Const::$actsCtrCmdRestorePostByMarkImg );
		if ($actsRet === false) {
			Bingo_Log::warning ( __FUNCTION__ . ": user can not submit cause acts_ctrl [$userId]!" );
			return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL );
		}
		else {
			$actsRet = Util_User::queryActsCtrl ( ( int ) $userId, Util_Const::$actsCtrCmdRestorePostByMarkImg );
		}
		
		$images = array ();
		
		foreach ( $markRets as $markRet ) {
			if (isset ( $markRet ['id'] )) {
				$images [$markRet ['id']] = $markRet ['val'];
			}
		}
		

		$dlResult = Dl_Pmc_Pmc::findImagesByImageIds ( array (
				'image_ids' => array_keys ( $images ) ) );
		
		if ($dlResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( __FUNCTION__ . ":find images by image ids error!" );
			return self::_errRet ( $dlResult ['errno'] );
		}
		if (empty ( $dlResult ['ret'] ['images'] )) {
			Bingo_Log::warning ( __FUNCTION__ . ":find images by image ids error!" );
			return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL );
		}
		
		$realImages = $dlResult ['ret'] ['images'];
		
		$whiteRightCount = 0;
		$blackRightCount = 0;
		
		$whiteTotalCount = 0;
		$blackTotalCount = 0;
		
		foreach ( $realImages as $realImage ) {
			if ($realImage ['base_type'] == Util_Const::$baseTypeWhite) {
				$whiteTotalCount ++;
				if (isset ( $images [$realImage ['img_id']] ) && Util_Const::$markTypeMarkGood == $images [$realImage ['img_id']]) {
					$whiteRightCount ++;
				}
			}
			if ($realImage ['base_type'] == Util_Const::$baseTypeBlack) {
				$blackTotalCount ++;
				if (isset ( $images [$realImage ['img_id']] ) && Util_Const::$markTypeMarkBad == $images [$realImage ['img_id']]) {
					$blackRightCount ++;
				}
			}
		}
		
		if ($whiteTotalCount === 0) {
			$whiteAccRate = 0;
		}
		else {
			$whiteAccRate = round ( $whiteRightCount / $whiteTotalCount, 2 );
		}
		
		if ($blackTotalCount === 0) {
			$blackAccRate = 0;
		}
		else {
			$blackAccRate = round ( $blackRightCount / $blackTotalCount, 2 );
		}
		
		if ($whiteAccRate >= Util_Const::$minWhiteAccRate && $blackAccRate >= Util_Const::$minBlackAccRate) {
			$ret = Util_Const::$markPass;
			$taskMarkRet = Util_Const::$markImgStatusSucc;
		}
		else {
			$ret = Util_Const::$markDeny;
			$taskMarkRet = Util_Const::$markImgStatusFail;
		}
		
		$dlInput = array ();
		$dlInput ['user_id'] = $userId;
		$dlInput ['task_id'] = $taskId;
		$dlInput ['finish_time'] = time ();
		$dlInput ['op_status'] = $taskMarkRet;
		$dlInput ['good_acc_rate'] = $whiteAccRate;
		$dlInput ['bad_acc_rate'] = $blackAccRate;
		
		// ��ͼ�ύ�ɹ������±�ͼ����
		$dlResult = Dl_Pmc_Pmc::updateMarkTaskByTaskIdAndUserId ( $dlInput );
		
		if ($dlResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( __FUNCTION__ . ":update mark task error.[" . $dlResult ['errno'] . ']' );
			return self::_errRet ( $dlResult ['errno'] );
		}
		
		// ����ÿ��ͼƬ�Ľ�����ű�ÿ�춼�����������
		foreach ( $images as $imgId => $val ) {
			$dlInput = array ();
			$dlInput ['img_id'] = $imgId;
			$dlInput ['task_id'] = $taskId;
			$dlInput ['mark'] = $val;
			
			$dlResult = Dl_Pmc_Pmc::updateMarkImgByTaskIdAndImgId ( $dlInput );
			
			if ($dlResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				Bingo_Log::warning ( __FUNCTION__ . ":update mark img error.[" . $dlResult ['errno'] . ']' );
				return self::_errRet ( $dlResult ['errno'] );
			}
		}
		
		// �����ͼͨ����������Ϣ����˵ȴ������У����򣬲������£���Ȼ��������ܴ��һ���ȴ�������˹����ߣ�����Ҫ���Ǹ��˹������Ϊ�����״̬��
		// ��Ϊ�ڽ��ܵ�mis�Ĵ���ʱ�����ǻ��жϹ�����������Ϣ�ǲ����Ѿ����˹�����״̬������ϵ��˹����߲����ٸ���״̬
		if ($ret === Util_Const::$markPass) {
			$dlInput = array ();
			$dlInput ['user_id'] = $userId;
			$dlInput ['user_name'] = $strUname;
			$dlInput ['pid'] = $postId;
			$dlInput ['tid'] = $threadId;
			$dlInput ['appeal_time'] = time ();
			$dlInput ['restore_type'] = Util_Const::$appealTypeMarkimg;
			$dlInput ['appeal_task_id'] = $taskId;
			$dlInput ['appeal_status'] = Util_Const::$appealStatusAppealling;
			
			$arrMarkInput = array(
					'user_id' => $userId,
					'pid'	  => $postId,
					'tid'	  => $threadId,
					'restore_type' => Util_Const::$appealTypeMarkimg
				);
			$dlMarkResult = Dl_Recycle_Restore::getPostRestoreTasks($arrMarkInput);
			if ($dlMarkResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				Bingo_Log::warning ( __FUNCTION__ . ":getPostRestoreTasks error.[" . $dlMarkResult ['errno'] . ']' );
				return self::_errRet ( $dlMarkResult ['errno'] );
			}
			$intDbId = null;
			$intManualTimes = 0;
			if ( isset($dlMarkResult['ret']) && !empty($dlMarkResult['ret']) ) {
				$intDbId = intval($dlMarkResult['ret'][0]['id']);
				$intManualTimes = intval($dlMarkResult['ret'][0]['manual_times']);
			}
			//�Ѿ����� �͸��³����µ�
			if ( null === $intDbId ) {
				$dlResult = Dl_Recycle_Restore::addPostRestoreTasks(array($dlInput));
				if ($dlResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
					Bingo_Log::warning ( __FUNCTION__ . ":update appeal msg error.[" . $dlResult ['errno'] . ']' );
					return self::_errRet ( $dlResult ['errno'] );
				}
			} else {
				$dlUpdateInput = array();
				$dlUpdateInput['id'] = $intDbId;
				$dlUpdateInput['user_id'] = $userId;
				$dlUpdateInput ['appeal_time'] = time ();
				$dlUpdateInput ['appeal_task_id'] = $taskId;
				$dlUpdateInput ['appeal_status'] = Util_Const::$appealStatusAppealling;
				$dlUpdateInput ['manual_times'] = $intManualTimes+1;
				$dlResult = Dl_Recycle_Restore::updatePostRestoreTask($dlUpdateInput);
				if ($dlResult ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
					Bingo_Log::warning ( __FUNCTION__ . ":update appeal msg error.[" . $dlResult ['errno'] . ']' );
					return self::_errRet ( $dlResult ['errno'] );
				}
			}
		}
		Bingo_Log::notice ( __FUNCTION__ . ": Submit Mark image sucess: " . serialize ( $input ) );
		return self::_succRet ( array (
				'markRet' => $ret, 
				'actsStatus' => $actsRet ) );
	}
	
	
	
	
}
