//@description: ���˹������Ŀ��Žӿ�
//@author: liubo03

struct msg_info
{
	uint32_t command_no;//�����
	uint32_t ip					= optional();//ip
	string   word				= optional();//����
	uint32_t user_id;//�û�id
	string   username;//�û���
	uint32_t user_ip;//�û�ip
	uint32_t op_uid;//������id
	string   op_uname;//������
	uint32_t now_time;//����ʱ��
	uint32_t day_num			= optional();//��������
	string   call_from 			= optional();//������Դƽ̨
	upd_info upd_list[] 		= optional();//�����û�id�б�
	uint32_t reset_id_list[] 	= optional();//�������û�id�б�
};
struct upd_info
{
	uint32_t id;//�û�id
}
struct manual_appeal_res
{
	uint32_t   	appeal_code;//���߱��
	uint32_t	result;//���߽��
	string 		message;//��˷���ģ��
	uint32_t 	user_id;//�����û�id
};
struct res_info
{
	uint32_t errno;//�����
	string errmsg;//������Ϣ
	boolean ret					= optional();//����ֵ
}
service pmc
{
	/**
     * @brief :  ����nmq����ӿ�
     * @param [in]	data	: msg_info	:	nmq����������
     * @param [out]	ret		: res_info :	������Ϣ
     **/
	commit void getMsg(msg_info data, out res_info ret );
	
	/**
     * @brief :  ����pmcmis�����Ĵ���������Ϣ
     * @param [in]	input	: manual_appeal_res	:	������Ϣ
     * @param [out]	ret		: res_info :	������Ϣ
     **/
	commit void manualAppeal(manual_appeal_res input, out res_info ret );
	
	/**
     * @brief :  �ж��˺��Ƿ�Ϊ�����˺�
     * @param [in]	user_id	: uint32_t	:	�˺�id
     * @param [out]	ret		: res_info :	����ֵ��Ϣ��errnoΪ0��ret['ret']==true,���ʾΪ�����˺�
     **/
	void isMachineAccount(uint32_t user_id,out res_info ret);
};