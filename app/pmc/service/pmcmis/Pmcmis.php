<?php
/**
 * Created by PhpStorm.
 * User: yanglei
 * Date: 2018/3/5
 * Time: 下午9:09
 */

class Service_Pmcmis_Pmcmis
{

    const SERVICE_NAME = "Service_Pmcmis_Pmcmis";

    const SERVICE_TYPE_VCODE = 'vcode';
    const OP_GROUP_SYSTEM = 'system';

    protected static $_conf = null;
    /**
     * @brief init
     *
     * @return : true if success. false if fail.
     *
     *
     */
    private static function _init() {

        //add init code here. init will be called at every public function beginning.
        //not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
        //init should be recalled for many times.
        if (self::$_conf == null) {
            self::$_conf = Bd_Conf::getConf ( "/app/mis/service_pmcmis_pmcmis" );
            if (self::$_conf == false) {
                Bingo_Log::warning ( "init get conf fail." );
                return false;
            }
        }
        return true;
    }

    /**
     * @param $errno
     * @return array
     */
    private static function _errRet($errno) {
        return array (
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg ( $errno )
        );
    }

    /**
     * @param $data
     * @return array
     */
    private static function _successRet($data) {
        return array (
            'errno' => Tieba_Errcode::ERR_SUCCESS,
            'errmsg' => Tieba_Error::getErrmsg ( Tieba_Errcode::ERR_SUCCESS ),
            'ret' => $data
        );
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getUserApealList($arrInput) {
        if (! is_numeric ( $arrInput ['apeal_uid'] ) || ! is_numeric ( $arrInput ['apeal_code'] )) {
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        if (! self::_init ()) {
            return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
        }
        //input params.
        $apealInfo = Dl_Pmcmis_Pmcmis::getApealInfo ( $arrInput );
        Bingo_Log::notice("in getUserApealList, appealInfo=".json_encode($apealInfo));
        if ($apealInfo ['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ( "call dl  Dl_Pmcmis_Pmcmis::getApealInfo failed error[{$apealInfo['errno']}] errmsg[{$apealInfo['errmsg']}]" );
            return self::_errRet ( Tieba_Errcode::ERR_DL_CALL_FAIL );
        }
        $apealFlag = $apealInfo ['ret'] [0] ['apeal_flag'];
        $apealUid = intval ( $arrInput ['apeal_uid'] );
        $apealNum = intval ( $apealInfo ['ret'] [0] ['apeal_num'] );
        $apealInput = array (
            'apeal_uid' => $apealUid,
            'apeal_flag' => $apealFlag,
            'apeal_num' => $apealNum );
        $apealList = Dl_Pmcmis_Pmcmis::getUserApealList ( $apealInput );
        if ($apealList ['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ( "call dl Dl_Pmcmis_Pmcmis::getUserApealList failed" );
            $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
        }
        else {
            $error = Tieba_Errcode::ERR_SUCCESS;
            $output = $apealList ['ret'];
        }
        $data = array ();
        foreach ( $output as $key => $item ) {
            $data [$key] ['complainRecord'] = $item ['apeal_code'];
            $data [$key] ['complainTime'] = $item ['apeal_time'];
            $data [$key] ['punishResult']=$item ['status'];
            $data [$key] ['opUname'] = $item ['op_uname'] ? $item ['op_uname'] : '';
            $data [$key] ['opTime'] = $item ['op_time'];
            $data [$key] ['opReason'] = $item ['op_reason'];
        }
        $arrOutput = array (
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg ( $error ),
            'ret' => $data );
        return $arrOutput;
    }

    /**
     * @brief 得到申诉列表
     *
     * @param
     *        	array : $arrInput :
     *        	(apeal_num,page,page_size,is_auto,is_vip,status,role)
     * @return array | boolean
     */
    public static function getApealList($arrInput) {
        if (! is_numeric ( $arrInput ['apeal_num'] ) || ! is_numeric ( $arrInput ['page'] ) || ! is_numeric ( $arrInput ['page_size'] ) || ! is_numeric ( $arrInput ['is_auto'] ) || ! is_numeric ( $arrInput ['is_vip'] ) || ! isset ( $arrInput ['status'] ) || ! is_numeric ( $arrInput ['role'] )) {
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        if (! self::_init ()) {
            Bingo_Log::warning ( __CLASS__.', '.__FUNCTION__.". _init err" );
            return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
        }
        $apealList = Dl_Pmcmis_Pmcmis::getApealList ( $arrInput );

        Bingo_Log::notice(__CLASS__.', '.__FUNCTION__.'. input='.serialize($arrInput).', apealList='.serialize($apealList));
        if ($apealList ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ( "call dl Dl_Pmcmis_Pmcmis::getApealList failed" );
            $error = Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL;
        }
        else {
            $error = Tieba_Errcode::ERR_SUCCESS;
            $output = $apealList ['ret'];
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array (
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg ( $error ),
            'ret' => $output );
        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getPunishHistory($arrInput) {
        if (! is_numeric ( $arrInput ['apeal_code'] )) {
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        if (! self::_init ()) {
            return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
        }
        $historyOutput = Dl_Pmcmis_Pmcmis::getPunishHistory ( $arrInput );
        if ($historyOutput ['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ( "call Dl_Pmcmis_Pmcmis::getPunishHistory failed error[" . $historyOutput ['errno'] . "] errmsg[" . $historyOutput ['errmsg'] . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_DL_CALL_FAIL );
        }
        $data = array ();
        $apealInfo = Dl_Pmcmis_Pmcmis::getApealInfo ( $arrInput );
        $apealInfo = $apealInfo ['ret'] [0];
        foreach ( $historyOutput ['output'] as $key => $item ) {
            $data [$key] ['punishTime'] = $item ['punish_start_time'];
            $data [$key] ['punishEndTime'] = $item ['punish_end_time'];
            $data [$key] ['opUser'] = $item ['punish_user'];
            $data [$key] ['punishReason'] = $item ['punish_reason'];
            $data [$key] ['punishFid'] = $item ['punish_fid'];
            $data [$key] ['punishRange'] = $item ['punish_forum'];
            $data [$key] ['punishDuring'] = ceil ( ($item ['punish_end_time'] - $item ['punish_start_time']) / (3600 * 24) );
            $data [$key] ['punishMethod'] = $item ['punish_method'];
            $data [$key] ['complainReason'] = $apealInfo ['apeal_reason'];
            $data [$key] ['monitor'] = $item ['monitor_type'];
        }
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array (
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg ( $error ),
            'ret' => $data );
        return $arrOutput;
    }

    /**
     * @param $uid
     * @return bool|mixed|multitype|null
     */
    public static function getUserState($uid) {
        $arrInput = array (
            'service_type' => self::SERVICE_TYPE_VCODE,
            'opgroup' => self::OP_GROUP_SYSTEM,
            'key' => $uid,
            'forum_id' => - 1 );
        $ret = Tieba_Service::call ( 'userstate', 'queryUserState', $arrInput );
        if ($ret === false || $ret ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ( "get user vcode info failed: $uid" );
        }
        return $ret;
    }

    /**
     * @brief 根据用户Id获取封禁信息
     *
     * @param integer $uid
     * @return array
     *
     */
    public static function getBlockInfo($uid) {
        $ret = Tieba_Service::call ( 'userstate', 'get_user_all_blocking_info', array (
            'uid' => $uid ) );
        if ($ret === false || $ret ['errno'] !== Tieba_Errcode::ERR_SUCCESS || $ret ['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ( __FUNCTION__ . ':call block failed:' . $uid );
            $error = Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL;
            $arrOut = array (
                'errno' => $error,
                'errmsg' => Tieba_Error::getErrmsg ( $error ) );
            return $arrOut;
        }
        $maxDay = 0;
        $range = '';
        $data = array ();
        $sortArray = array ();
        $list = array ();
        foreach ( $ret ['block_info'] as $key => $item ) {
            $day = $item ['day_num'];
            if ($day > $maxDay) {
                $maxDay = $day;
                if ($item ['block_type'] == 4) {
                    $range = '全局';
                }
                else {
                    $range = $item ['forum_name'];
                }
            }
            if ($item ['block_type'] == 4) {
                $item ['global'] = 1;
                $item ['forum_id'] = 0;
                $item ['forum_name'] = '全局';
            }
            else
            {
                $item ['global'] = 0;
            }

            $data [$key] = $item;
            $sortArray [$key] = $item ['start_time'];
        }
        asort ( $sortArray );
        foreach ( $sortArray as $key => $item ) {
            $list [] = $data [$key];
        }
        $retObj = array (
            'errno' => Tieba_Errcode::ERR_SUCCESS,
            'maxRange' => $range,
            'maxDay' => $maxDay,
            'list' => $list );
        return $retObj;
    }

    /**
     * 申诉转到马甲区（状态设为不处理）
     * @param $arrInput
     * @return array
     */
    public static function dealApealNothing($arrInput) {
        if (! is_numeric ( $arrInput ['op_uid'] ) || ! isset ( $arrInput ['op_uname'] ) || ! is_numeric ( $arrInput ['apeal_code'] ) || ! is_numeric ( $arrInput ['apeal_uid'] )) {
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        if (! self::_init ()) {
            return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
        }
        $getApealInfoInput = array(
            'apeal_code' => $arrInput['apeal_code'],
        );
        $repeatApeal = Dl_Pmcmis_Pmcmis::getApealInfo ( $getApealInfoInput );
        Bingo_Log::notice('Dl_Pmcmis_Pmcmis::getApealInfo, input='.serialize($getApealInfoInput).', output='.serialize($repeatApeal));
        if ($repeatApeal ['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ( "call dl Dl_Pmcmis_Pmcmis::getApealInfo failed! input=".serialize($getApealInfoInput) );
            $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
            return self::_errRet ( $error );
        }
        // 申诉内容中的申诉用户id 与入参不符
        if ($repeatApeal ['ret'] [0] ['apeal_uid'] != $arrInput ['apeal_uid'] ){
            Bingo_Log::warning ( "apeal_uid err! arrInput=".serialize($arrInput).", repeatApeal=".serialize($repeatApeal) );
            $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
            return self::_errRet ( $error );
        }
        // 此条申诉不是待审核状态
        if ($repeatApeal ['ret'] [0] ['status'] != Dl_Pmcmis_Pmcmis::STATUS_UNDO ) {
            Bingo_Log::warning ( "status not undo! arrInput=".serialize($arrInput).", repeatApeal=".serialize($repeatApeal) );
            $error = Tieba_Errcode::ERR_SUCCESS;
            $arrOutput = array (
                'errno' => $error,
                'errmsg' => Tieba_Error::getErrmsg ( $error ),
                'repeat' => 1 );
            return $arrOutput;
        }
        $updateApealinput = array(
            'op_uid' => $arrInput ['op_uid'],
            'op_uname' => $arrInput ['op_uname'],
            'apeal_code' => $arrInput ['apeal_code'],
            'apeal_uid' => $arrInput ['apeal_uid'],
            'status' => Dl_Pmcmis_Pmcmis::STATUS_NOTHING,
            'op_reason' => 'monitor_type: '.$arrInput['monitor_type']
        );
        $apealRet = Dl_Pmcmis_Pmcmis::updateApeal ( $updateApealinput );
        Bingo_Log::notice('Dl_Pmcmis_Pmcmis::updateApeal, input='.serialize($updateApealinput).', output='.serialize($apealRet));
        if ($apealRet ['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ( "call dl Dl_Pmcmis_Pmcmis::updateApeal failed! updateApealinput=".serialize($updateApealinput).', $apealRet='.serialize($apealRet) );
            $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
            return self::_errRet ( $error );
        }
        else {
            $error = Tieba_Errcode::ERR_SUCCESS;
            $arrOutput = array (
                'errno' => $error,
                'errmsg' => Tieba_Error::getErrmsg ( $error ),
                'repeat' => 1
            );
            return $arrOutput;
        }
    }

}