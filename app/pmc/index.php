<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2013-04-17 21:12:10
 */

Tieba_Init::init ( "pmc", "pmc" );
if (!defined('MODULE_NAME')){//����MODULE_NAME��add by feng<PERSON>
    define('MODULE_NAME', 'pmc');
}
// utf8 ui
$arrUtf8Ui = array(
    '/pmc/userthreadinfo',
    '/pmc/userthreadinfo/',
    '/pmc/post/userthreadinfo',
    '/pmc/post/userthreadinfo/',
    '/pmc/appealpoststatus',
    '/pmc/appealpoststatus/',
);
if (isset($_SERVER['PATH_INFO']) && in_array($_SERVER['PATH_INFO'], $arrUtf8Ui)) {
    define ('BINGO_ENCODE_LANG', 'UTF-8');
} else {
    define ('BINGO_ENCODE_LANG', 'GBK');
}

class Core_User_Http_Router extends Bingo_Http_Router_Abstract {
	public function getHttpRouter() {
		$strUrl = $_SERVER ['PATH_INFO'];
		$strUrl = strip_tags ( $strUrl );
		if (empty ( $strUrl )) {
			$strHttpRouter = 'index';
		}
		$arrUrl = explode ( '/', $strUrl );
		$strHttpRouter = $arrUrl [2];
		if (isset($arrUrl[3]) && is_string($arrUrl[3]) && 
            preg_match("/^\w+$/", $arrUrl[3])) {
            $strHttpRouter .= '/'.$arrUrl[3];
        }
		Bingo_Http_Request::setHttpRouter ( $strHttpRouter, array (
				$strHttpRouter ) );
		return $strHttpRouter;
	}
}


$objFrontController = Bingo_Controller_Front::getInstance ( array (
		"actionDir" => MODULE_ACTION_PATH, 
		"httpRouter" => new Core_User_Http_Router (), 
		"defaultRouter" => "index", 
		"notFoundRouter" => "error", 
		"beginRouterIndex" => 1 ) );

//��ҳ��url��� ����һ����̬·��
$objFrontController->addStaticRouter('main', 'index');

//����վ ���Ӿ�̬·��
$objFrontController->addStaticRouter('recycle', 'post/index');//����վ��ҳ
$objFrontController->addStaticRouter('userthreadinfo', 'post/userthreadinfo');//����վ�б�ӿ�ҳ
$objFrontController->addStaticRouter('appealpoststatus', 'post/appealpoststatus'); //������̬����

//����վ������ӽӿ�·��
$objFrontController->addStaticRouter('delonerecycle','post/delonerecycle');//����վ���һ������
$objFrontController->addStaticRouter('delallrecycle','post/delallrecycle');//����վ�������


//�ʺ�ģ�� ���Ӿ�̬·��
$objFrontController->addStaticRouter('appeal', 'account/appeal');
$objFrontController->addStaticRouter('checkbind', 'account/checkbind');
$objFrontController->addStaticRouter('checkvcode', 'account/checkvcode');
$objFrontController->addStaticRouter('commitimg', 'account/commitimg');
$objFrontController->addStaticRouter('commitmanual', 'account/commitmanual');
$objFrontController->addStaticRouter('detailmsg', 'account/detailmsg');
$objFrontController->addStaticRouter('img', 'account/img');
$objFrontController->addStaticRouter('listimg', 'account/listimg');
$objFrontController->addStaticRouter('listmsg', 'account/listmsg');
$objFrontController->addStaticRouter('manual', 'account/manual');
$objFrontController->addStaticRouter('phone', 'account/phone');
$objFrontController->addStaticRouter('readmsg', 'account/readmsg');
$objFrontController->addStaticRouter('sendvcode', 'account/sendvcode');
$objFrontController->addStaticRouter('system', 'account/system');
$objFrontController->addStaticRouter('membermanual','account/membermanual');
$objFrontController->addStaticRouter('commitmembermanual','account/commitmembermanual');
$objFrontController->addStaticRouter('memberinfo','account/memberinfo');
//�ʺ�ģ�� ���� ���Ӿ�̬·��
$objFrontController->addStaticRouter('bawu', 'account/bawu');
$objFrontController->addStaticRouter('bawuappeal', 'account/bawuappeal');

//�ٱ�ģ�� ���Ӿ�̬·��
$objFrontController->addStaticRouter('jubao', 'jubao/index');
$objFrontController->addStaticRouter('detailjubao', 'jubao/detail');
$objFrontController->addStaticRouter('listjubao', 'jubao/list');

//����Ͷ��
$objFrontController->addStaticRouter('reportBazhu', 'tousu/reportBazhu');
$objFrontController->addStaticRouter('listTousu', 'tousu/listTousu');

//api ��̬·��
$objFrontController->addStaticRouter('tpl', 'api/tpl');
$objFrontController->addStaticRouter('blockid', 'api/blockid');
$objFrontController->addStaticRouter('userbaseinfo', 'api/userbaseinfo');

// ��Ҫ����
$objFrontController->addStaticRouter('feedback', 'feedback/index');
$objFrontController->addStaticRouter('getUserBlockInfo', 'feedback/getUserBlockInfo');
$objFrontController->addStaticRouter('getUserManager', 'feedback/getUserManager');
$objFrontController->addStaticRouter('commitFeedback', 'feedback/commitFeedback');

// ��������
$objFrontController->addStaticRouter('service', 'service/index');

Bingo_Timer::start ( 'total' );
Bingo_Page::init ( array (
		"baseDir" => MODULE_VIEW_PATH, 
		"debug" => false, 
		"outputType" => ".", 
		"isXssSafe" => true, 
		"module" => "pmc", 
		"useTbView" => true, 
		"viewRootpath" => MODULE_VIEW_PATH . "/../" ) );

require_once 'Tieba/Cookie/UserType.php';
$is_new_user = 0;
$user_start_time = 0;
if (class_exists ( 'Tieba_Cookie_UserType', true )) {
	$is_new_user = intval ( Tieba_Cookie_UserType::isNew () );
	$user_start_time = Tieba_Cookie_UserType::getStartTime ();
}
else {
	$is_new_user = 0;
	$user_start_time = 0;
	Bingo_Log::warning ( 'load Tieba_Cookie_UserType failure!' );
}

Tieba_Stlog::setFileName ( 'feye-stat' );
Tieba_Stlog::addNode ( 'pro', 'tieba' );
Tieba_Stlog::addNode ( 'mid', 'pmc' );
Tieba_Stlog::addNode ( 'bduid', rtrim ( strip_tags ( Bingo_Http_Request::getCookie ( 'BAIDUID' ) ), ':FG=1' ) );
Tieba_Stlog::addNode ( 'url', strip_tags ( Bingo_Http_Request::getServer ( 'REQUEST_URI' ) ) );
Tieba_Stlog::addNode ( 'refer', strip_tags ( Bingo_Http_Request::getServer ( 'HTTP_REFERER' ) ) );
Tieba_Stlog::addNode ( 'agent', strip_tags ( Bingo_Http_Request::getServer ( 'HTTP_USER_AGENT' ) ) );

try {
	$objFrontController->dispatch ();
	Tieba_Stlog::addNode ( 'is_new_user', $is_new_user );
	Tieba_Stlog::addNode ( 'user_start_time', $user_start_time );
} catch ( Exception $e ) {
	// ������ֱ��ת�򵽴���ҳ��
	Bingo_Log::warning ( sprintf ( 'main process failure!HttpRouter=%s,error[%s]file[%s]line[%s]', Bingo_Http_Request::getStrHttpRouter (), $e->getMessage (), $e->getFile (), $e->getLine () ) );
	Bingo_Page::setErrno ( $e->getCode () );
	header ( 'location:http://cq01-forum-rdtest11q412.vm.baidu.com:8090/tb/error.html' );
}

Bingo_Timer::start ( 'build_page' );
Bingo_Page::buildPage ();
Bingo_Timer::end ( 'build_page' );
Bingo_Timer::end ( 'total' );

$strTimeLog = Bingo_Timer::toString ();
if (! empty ( $strTimeLog )) {
	Bingo_Log::pushNotice ( "timer", $strTimeLog );
}

Bingo_Log::buildNotice ();
Tieba_Stlog::notice ();
