<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @abstract ����վ��������ҳ���߼�����
 * 
 */

class Page_Post_Restore {
	
	public static function checkParams($arrInput) {
		if (!is_numeric ( $arrInput ['pid'] ) || $arrInput ['pid'] < 0) {
			return false;
		}
		if (!is_numeric ( $arrInput ['tid'] ) || $arrInput ['tid'] < 0) {
			return false;
		}
		return true;
	}
	
	public static function checkIsLegal($arrInput) {
		$srvCheckCanRestoreRet = Service_Recycle_Post::checkIsPostCanRestore($arrInput);
		
		if ($srvCheckCanRestoreRet ['errno'] !== Tieba_Errcode::ERR_SUCCESS || 
				$srvCheckCanRestoreRet ['ret'] === false) {
			return false;
		}
		return true;
	}
	
	public static function checkIsLegalAndGetOpUsertype($arrInput) {
		$srvCheckCanRestoreRet = Service_Recycle_Post::checkIsPostCanRestore($arrInput, true);
		
		if ($srvCheckCanRestoreRet ['errno'] !== Tieba_Errcode::ERR_SUCCESS || 
				$srvCheckCanRestoreRet ['ret']['ret'] === false) {
			return false;
		}
		//monitor_type���а�������ȥ���Զ��ָ����� ֻ�������˹� �����ü��ķ�ʽ��systemת��admin
		$intMonitorType = intval($srvCheckCanRestoreRet ['ret']['monitor_type']);
		if ( in_array($intMonitorType, Util_Const::$manualMonitorType) ) {
			return Util_Const::$opUserTypePm;
		}
		
		return $srvCheckCanRestoreRet ['ret']['opUserType'];
	}
	
	/***
	 * ���Ϸ��ԣ����һ�ȡop�����˵���������
	 * fengzhen
	 */
	public static function checkIsLegalAndGetRealOpUsertype($arrInput) {
		$srvCheckCanRestoreRet = Service_Recycle_Post::checkIsPostCanRestore($arrInput, true);
		
		if ($srvCheckCanRestoreRet ['errno'] !== Tieba_Errcode::ERR_SUCCESS || 
				$srvCheckCanRestoreRet ['ret']['ret'] === false) {
			return false;
		}
		//��ȡop type
		$postDelInfo = $srvCheckCanRestoreRet ['ret']['postDelInfo'];
		$intDelUid = $postDelInfo['del_uid'];
		$strCallFrom = $postDelInfo['call_from'];
		
		$intDelUserType = Util_Appeal::getAppealOpType($intDelUid, $strCallFrom);
		return $intDelUserType;
	}
	
	public static function isOpUserMachine ( $intOpUserType ) {
		if ( $intOpUserType === Util_Const::$opUserTypeMachine ) {
			return true;
		}
		return false;
	}
	
	public static function isRestoreAllowManualOnly ( $arrInput ) {
		$intPostId		= intval($arrInput['pid']);
		$arrSrvPostInfo = Service_Recycle_Post::getPostInfoByPid($intPostId);
		if ( $arrSrvPostInfo['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
			Bingo_Log::warning( __CLASS__ . 'getPostInfoByPid error' );
			return $arrSrvPostInfo['errno'];
		}
		if ( empty($arrSrvPostInfo['ret']) ) {
			Bingo_Log::warning( __CLASS__ . 'getPostInfoByPid ret empty' );
			//return Util_Const::$errQueryPostInfoFail;	// �����û���塭���ķ��ش���� by qmy
			return Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL ; 
		}
		$intForumId = intval($arrSrvPostInfo['ret']['forum_id']);
		
		if ( in_array($intForumId, Util_Const::$restorePostAllowManualOnlyForumIds) ) {
			return true;
		}
		return false;
	}
	
	/**
	 * �ж������Ƿ����������ϲ���(ɾ����Ⱥ�Ĳ���)�����ٻ�,�ϲ��ԣ�����ʧ�ܣ�Ĭ������
	 */
 	public static function checkIfMutiRecall($postId,$intDeletTime){
 		$res = Util_Anti::getDeletPostMonitorList($postId, $intDeletTime);
 		if($res === false){
 			Bingo_Log::warning( __CLASS__ . 'getDeletPostMonitorList fail,postid:'. $postId);
 			return true;
 		}
 		if(count($res) > 20){//�ٻ�������20����ֱ������
 			Bingo_Log::warning( __CLASS__ . 'hit monitors more than 20,postid:'. $postId);
 			return true;
 		}
 		if(count($res) > 0){
 			$count = 0;
 			foreach($res as $node){
 				$cluter = self::_getMonitorCluter($node);
 				if(in_array($cluter, Util_Const::$deletMonitorClusters)){//��ɾ��������
 					$count ++;
 				}
 			}
 			if($count >= 2){
 				Bingo_Log::warning( __CLASS__ . 'multi-recall,postid:'. $postId);
 				return true;
 			}		
 		}
 		return false;
 	}
 	
 	private static function _getMonitorCluter($node){
 		$node = trim($node);
 		$pos = strpos($node, '_');
		$cluter = substr($node, 0,$pos);
		return $cluter;
 	}
}

