<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-02-07 00:07:53
 * @version
 */

class Util_Cache {

    private static $_cache;

    public static function getUiCache(){
        if(self::$_cache){
            return self::$_cache ;
        }
        Bingo_Timer::start('cacheinit');

        //$cacheConf = Bd_Conf::getConf('cache/cache_ui_cui');
        //if(!$cacheConf){
        //    Bingo_Log::warning("get cache config fail.[cache/cache_ui_cui]");
        //    return null;
        //}
        //self::$_cache = new Bingo_Cache_Memcached(array(
        //            'conf' => $cacheConf,   
        //            ));     
        self::$_cache = new Bingo_Cache_Memcached("");

        Bingo_Timer::end('cacheinit');  

        if(!self::$_cache || !self::$_cache->isEnable()){
            Bingo_Log::warning("init cache fail.");
            self::$_cache = null; 
            return null;
        }
        return self::$_cache;
    }
}
?>
