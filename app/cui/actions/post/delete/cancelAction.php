<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-02-07 00:07:53
 * @version
 * ɾ���ӿ� ipv6
 */
class cancelAction extends Commit_Action {

	/**
	 * �ӿڳ�ʼ��
	 * 1����ʼ������
	 * 2�����ܲ���
	 * @see Commit_Action::init()
	 */
	public function init() {
		if (! parent::init ()) {
			return false;
		}
		// ��ȡ��ز���
		$this->_getParamsForPost();

		return true;
	}

	/**
	 * �������Լ�飬ʹ���쳣������
	 */
	public function execute() {

		try {
			//��½���
			$this->_checkLogin();

			//tbs���
			$this->_checkTbs();


			//������ϣ�����������commit_service			
			//����ֵ��ʱû������
			$arrInput = Util_Dict::pushToArray();


			//�����ϰ�bawu���ָ�post����Ҫ�ָ�thread
			$arrResPost = $this->_getPostInfo(intval($arrInput['thread_id']),intval($arrInput['post_id']));

			if ( ! $arrResPost ){

				throw new Exception('',Tieba_Errcode::ERR_POST_CT_TID_PID_NOT_FIX);

			}	

			$intThreadDelete = intval($arrResPost['is_thread_deleted']);
			$intPostDelete   = intval($arrResPost['is_post_deleted']);
			$intPostNo       = intval($arrResPost['post_no']);

			//ipv6
			$arrIp = self::getIpCompatible();
			$arrInput['op_ip'] = $arrIp['ipv4_int'];
			$arrInput['op_ip6'] = $arrIp['ipv6_str'];
			
			if ( $intThreadDelete > 0  && $intPostDelete > 0  ){
				$arrOutput = $this->_callCommitService( $arrInput,self::STR_SERVICE_NAME, self::STR_METHOD_REC_THREAD, 0, 'service_commit');
				usleep(500); //�����ڲ��ԣ����λָ�����֮����50����
				//bawu�ӿ���gbk����
				$arrInput['ie'] = 'gbk';
				$arrOutput = $this->_callCommitService( $arrInput , self::BAWU_SERVICE_NAME, self::BAWU_METHOD_RECOVERPOST);
			}else if ( $intThreadDelete > 0 && $intPostDelete == 0 ){
				$arrOutput = $this->_callCommitService( $arrInput,self::STR_SERVICE_NAME, self::STR_METHOD_REC_THREAD, 0, 'service_commit');
				usleep(500); //�����ڲ��ԣ����λָ�����֮����50����
				if (  $intPostNo > 1 ){
					//bawu�ӿ���gbk����
					$arrInput['ie'] = 'gbk';
					$arrOutput = $this->_callCommitService( $arrInput , self::BAWU_SERVICE_NAME, self::BAWU_METHOD_RECOVERPOST);
				}
			}else if ( $intThreadDelete == 0 && $intPostDelete > 0 ){
				//bawu�ӿ���gbk����
				$arrInput['ie'] = 'gbk';
				$arrOutput = $this->_callCommitService( $arrInput , self::BAWU_SERVICE_NAME, self::BAWU_METHOD_RECOVERPOST);
			}else{
				//bawu�ӿ���gbk����
				$arrInput['ie'] = 'gbk';
				$arrOutput = $this->_callCommitService( $arrInput , self::BAWU_SERVICE_NAME, self::BAWU_METHOD_RECOVERPOST);
		
			}


			//ͳһ��������Ľӿ�
			$this->_displayResult($arrOutput);

		} catch ( Exception $e ) {
			// ���������쳣

			$intErrno = $e->getMessage();
			$strMsg   = Tieba_Error::getErrmsg($intErrno);
			Bingo_Log::warning ( sprintf('/post/delete/cancel failure, errno:%s errmsg:%s',$intErrno,$strMsg ));
			$arrOutput = array(
				'errno'  => $intErrno,
				'errmsg' => $strMsg,
				'res'    => Util_Dict::pushToArray(),
			);
			$this->_displayResult($arrOutput);
			return false;
		}
		// Tieba_Stlog::setFileName('feye-stat');
		// Tieba_Stlog::notice();
		return true;			
	}

	function __destruct() {
		//Tieba_Stlog::setFileName('feye-stat');
		//Tieba_Stlog::notice();
	}

	/**
	 * ip compatible ipv4 ipv6
	 * @param
	 * @return
	 */
	private static function getIpCompatible(){
		//ipv6 compatible
		$arrRet = array();
		$arrRet['ipv4_int'] = 0;
		$arrRet['ipv4_str'] = '';
		$arrRet['ipv6_str'] = '';
	
		$arrIp = Bingo_Http_Ip::getConnectIpExt();
		$arrRet['type'] = $arrIp['type'];
	
		if($arrIp['type']=='IPv6'){
			$arrRet['ipv6_str'] = $arrIp['ip'];
		}else{
			$arrRet['ipv4_str'] = $arrIp['ip'];
			$arrRet['ipv4_int']= Bingo_Http_Ip::newip2long($arrIp['ip']);
		}
	
		return $arrRet;
	}


}
