<?php

class Umpf_HttpUtil {
	public static function httpPost($url, $params) {
		$ch = curl_init ();
		curl_setopt ( $ch, CURLOPT_URL, $url );
		curl_setopt ( $ch, CURLOPT_HEADER, 0 );
		curl_setopt ( $ch, CURLOPT_RETURNTRANSFER, true );
		
		curl_setopt ( $ch, CURLOPT_POST, 1 );
		curl_setopt ( $ch, CURLOPT_POSTFIELDS, $params );
		
		$output = curl_exec ( $ch );
		
		if ($output === FALSE) {
			Bd_Log::warning ( "cURL Error: " . curl_error ( $ch ) );
		}
		curl_close ( $ch );
		
		return $output;
	}
}

?>