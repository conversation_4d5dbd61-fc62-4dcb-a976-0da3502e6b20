<?php
/**
 * @name Action_ListTasks
 * @desc Ajax Action，列出任务
 * <AUTHOR>
 */
class Action_ListTasks extends Umpf_Action {
	protected function action() {
		$page = $this->_getParam ( 'page', 1 );
		$objServicePagePvfrsListTasks = new Service_Page_Pvpb_ListTasks ();
		$arrResult = $objServicePagePvfrsListTasks->execute ( array (
				'page' => $page, 
				'size' => 30 ) );
		if ($arrResult ['errno'] != 0) {
			Bd_Log::warning ( 'List error:' . var_export ( $arrResult, true ) );
			$this->_outputErrorJson ( $arrResult ['errmsg'], $arrResult ['errno'] );
		} else {
			echo json_encode ( array (
					'result' => 0, 
					'data' => $arrResult ['data'] ['tasks'], 
					'pager' => $arrResult ['data'] ['pager'] ) );
		}
	}
}

?>