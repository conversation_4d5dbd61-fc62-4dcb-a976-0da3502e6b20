<?php
/**
 * @name Action_PostList
 * @desc Action，frs评估任务列表
 * <AUTHOR>
 */
class Action_PostList extends Umpf_Action {
	protected function action() {
		$pvId = $this->_getParam ( 'pvId' );
		$pPage = $this->_getParam ( 'pPage', 1 ); 
		$objServicePagePvfrsPostList = new Service_Page_Pvfrs_PostList ();
		$arrResult = $objServicePagePvfrsPostList->execute ( $pvId );
		if ($arrResult ['errno'] != 0) {
			Bd_Log::warning ( 'List error:' . var_export ( $arrResult, true ) );
			$this->_go2ErrorPage ( $arrResult ['errmsg'], $arrResult ['errno'], __CLASS__ );
		} else {
			$this->_assignParam ( 'pv', $arrResult ['data'] ['pv'] );
			$this->_assignParam ( 'pPage', $pPage );            
                   
                        $this->_assignParam ( 'nextpv',$arrResult ['data'] ['nextpv'] );
			$this->_setTpl ( 'evaluationpf/pvfrs/postlist.tpl' );
		}
	}
}

?>
