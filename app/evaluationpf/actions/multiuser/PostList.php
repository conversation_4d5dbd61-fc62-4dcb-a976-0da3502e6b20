<?php
/**
 * @description ： get user list by task id
 * <AUTHOR> z<PERSON><PERSON><PERSON>@baidu.com
 * @date : 2015-07-28 16:55:40
 *
 */
class Action_PostList extends Umpf_Action {

	/**
	 * actions
	 * @param : no param
	 * @return [type] [description]
	 */
	protected function action() {
		$userId = $this->_getParam('user_id');
		$taskId = $this->_getParam('task_id');
		$page = $this->_getParam('page', 1);
		$size = $this->_getParam('size', 30);
		$objServicePageMultiuserPostList = new Service_Page_Multiuser_PostList();
		$arrResult = $objServicePageMultiuserPostList->execute($taskId, $userId, $page, $size);
		if ($arrResult['errno'] != 0) {
			Bd_Log::warning('List error:' . var_export($arrResult, true));
			$this->_outputErrorJson($arrResult['errmsg'], $arrResult['errno']);
		} else {
			$this->_assignParam('user_id', $userId);
			$this->_assignParam('task', $arrResult['data']['task']);
			echo json_encode(
			
			    array(
			        'result' => 0,
			        'data' => $arrResult['data']['posts'],
			        'pager' => $arrResult['data']['pager'],
			    )
			);
		}
	}

}

?>