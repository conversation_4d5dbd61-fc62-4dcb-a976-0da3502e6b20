<?php
/**
 * @description ： get user list by task id
 * <AUTHOR> z<PERSON><PERSON><PERSON>@baidu.com
 * @date : 2015-07-28 16:55:40
 *
 */
class Action_UserList extends Umpf_Action {

	/**
	 * actions
	 * @param : no param
	 * @return [type] [description]
	 */
	protected function action() {
		$taskId = $this->_getParam('task_id');
		$page = $this->_getParam('page', 1);
		$size = $this->_getParam('size', 30);
		if (!isset($taskId) || 0 === $taskId) {
			$this->_outputErrorJson('Missing params: task_id is required.', 200003);
		}
		$objServicePageMultiuserUserList = new Service_Page_Multiuser_UserList();
		$arrResult = $objServicePageMultiuserUserList->execute($taskId, $page, $size);

		if ($arrResult['errno'] != 0) {
			Bd_Log::warning('List error:' . var_export($arrResult, true));
			$this->_go2ErrorPage($arrResult['errmsg'], $arrResult['errno'], __CLASS__);

			$this->_outputErrorJson($arrResult['errmsg'], $arrResult['errno']);
		} else {
			$this->_assignParam('task_id', $arrResult['data']['task_id']);
			//$this->_assignParam('pPage', $pPage);

			$this->_assignParam('task', $arrResult['data']['task']);
			$this->_setTpl('evaluationpf/multiuser/userlist.tpl');

			// echo json_encode(
			// 	array(
			// 		'result' => 0,
			// 		'data' => $arrResult['data']['users'],
			// 		'pager' => $arrResult['data']['pager'],
			// 	)
			// );
		}
	}

}

?>