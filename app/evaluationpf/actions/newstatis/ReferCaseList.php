<?php
class Action_ReferCaseList extends Umpf_Action {
	protected function action() {
		$taskId = $this->_getParam ( 'taskId' );
		$referName = $this->_getParam ( 'referName' );
		$pvIds = $this->_getParam ( 'referPvIds' );
		$objServicePageStatisFrsStatisData = new Service_Page_NewStatis_ListReferCase();
		$arrResult = $objServicePageStatisFrsStatisData->execute ( $taskId, $pvIds );
		if ($arrResult ['errno'] != 0) {
			Bd_Log::warning ( 'List error:' . var_export ( $arrResult, true ) );
			$this->_go2ErrorPage ( $arrResult ['errmsg'], $arrResult ['errno'], __CLASS__ );
		} else {
			//Ap_Controller_Abstract::redirect ( WEBROOT_RELATIVE_PATH . '/pvfrs/list' );
			
			$this->_assignParam ('case',$arrResult ['data'] ['case']);
			$this->_assignParam ('taskId',$taskId);
			$this->_assignParam ('referName',$referName);
			$this->_setTpl ( 'evaluationpf/newstatis/referpvcase.tpl' );
			
		}
	}
}

?>