<?php

class Service_Page_Pvfrs_ListPvs {
	private $_objServiceDataPv;
	private $_objServiceDataMark;
	private $_objServiceDataTask;
	
	public function __construct() {
		$this->_objServiceDataPv = new Service_Data_Pv ();
		$this->_objServiceDataMark = new Service_Data_MarkPost ();
		$this->_objServiceDataTask = new Service_Data_Task ();
	}
	
	public function execute($arrInput) {
		Bd_Log::debug ( 'pv list data page service called' );
		$arrResult = array ();
		$arrResult ['errno'] = 0;
		try {
			$page = $arrInput ['page'];
			$size = $arrInput ['size'];
			$taskId = $arrInput ['taskId'];
			
			$totalCount = $this->_objServiceDataPv->getPvCountByTaskId ( $taskId );
			if (! is_numeric ( $totalCount )) {
				$totalCount = 0;
			}
			$pvRecordCount = $this->_objServiceDataPv->getRecordPvCountByTaskId ( $taskId );
			$arrResult ['data'] ['pvRecordCount'] = $pvRecordCount;
			$pager = Umpf_PagerUtil::genPager ( $totalCount, $page, $size, 5, array ('taskId' ) );
			$arrResult ['data'] ['pager'] = $pager;
			
			if ($totalCount == 0) {
				$arrResult ['data'] ['pvs'] = array ();
			} else {
				$result = $this->_objServiceDataPv->findPvsByTaskId ( $taskId, $pager ['base'] ['page'], $size );
				$offset = Umpf_PagerUtil::getOffset ( array ('page' => $pager ['base'] ['page'], 'size' => $size ) );
				foreach ( $result as $key => $pv ) {
					$result [$key] ['id'] = ++ $offset;
					$pvIds [] = $pv ['pvId'];
				}
				
				$totalPvIds = $this->_objServiceDataPv->findPvsIdsByTaskId ( $taskId );
				$pvIds = array ();
				foreach ( $totalPvIds as $pvRecord ) {
					$pvIds [] = $pvRecord ['pvId'];
				}
				$totalRecordPvIds = $this->_objServiceDataPv->findRecordPvsIdsByTaskId ( $taskId );
				$recordPvIds = array ();
				foreach ( $totalRecordPvIds as $pvRecord ) {
					$recordPvIds [] = $pvRecord ['pvId'];
				}
				$pullutionCounts = $this->_objServiceDataMark->getPostPullutionCountByPvIds ( $pvIds );
				$pullutionRecordCounts = $this->_objServiceDataMark->getPostPullutionCountByPvIds ( $recordPvIds );
				$totalCounts = $this->_objServiceDataMark->getPostCountByPvIds ( $pvIds );
				
				$arrResult ['data'] ['pullutionCount'] = count ( $pullutionRecordCounts );
				$arrResult ['data'] ['totalCount'] = intval ( $totalCount );
				if ($totalCount == 0) {
					$arrResult ['data'] ['pullutionRatio'] = 0;
				} else {
					$arrResult ['data'] ['pullutionRatio'] = round ( count ( $pullutionRecordCounts ) / $pvRecordCount, 5 );
				}
				$this->_objServiceDataTask->updateRatioByTaskId ( $taskId, $arrResult ['data'] ['pullutionRatio'] );
				foreach ( $pullutionCounts as $pullutionCount ) {
					$pullutionMap [$pullutionCount ['pvId']] = $pullutionCount ['ct'];
				}
				
				foreach ( $totalCounts as $totalCount ) {
					$totalMap [$totalCount ['pvId']] = $totalCount ['ct'];
				}
				
				foreach ( $result as $key => $pv ) {
					if (isset ( $pullutionMap [$result [$key] ['pvId']] )) {
						$result [$key] ['pullutionCount'] = $pullutionMap [$result [$key] ['pvId']];
					} else {
						$result [$key] ['pullutionCount'] = 0;
					}
					if (isset ( $totalMap [$result [$key] ['pvId']] )) {
						$result [$key] ['totalCount'] = $totalMap [$result [$key] ['pvId']];
					} else {
						$result [$key] ['totalCount'] = 0;
					}
				
				}
				$arrResult ['data'] ['pvs'] = $result;
			}
		} catch ( Exception $e ) {
			Bd_Log::warning ( $e->getMessage (), $e->getCode () );
			$arrResult ['errno'] = $e->getCode ();
			$arrResult ['errmsg'] = $e->getMessage ();
		}
		return $arrResult;
	}
}

?>