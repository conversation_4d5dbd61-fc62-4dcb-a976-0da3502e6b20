<?php
class Service_Page_Statis_ListForums {
	private $_objServiceDataPv;
	
	public function __construct() {
		$this->_objServiceDataPv = new Service_Data_Pv ();
	}
	public function execute($taskId) {
		Bd_Log::debug ( 'Service_Page_Statis_ListForums called' );
		$arrResult = array ();
		$arrResult ['errno'] = 0;
		try {
			$totalCount = $this->_objServiceDataPv->getForumCountByTaskId ( $taskId );
			if (! is_numeric ( $totalCount )) {
				$totalCount = 0;
			}
			$arrResult ['data'] ['totalCount'] = intval ( $totalCount );
			$result = $this->_objServiceDataPv->findPullutionForumsByTaskId ( $taskId );
			$arrResult ['data'] ['forums'] = $result;
			$arrResult ['data'] ['pullutionCount'] = count ( $result );
			if ($totalCount == 0) {
				$arrResult ['data'] ['pullutionRatio'] = 0;
			} else {
				$arrResult ['data'] ['pullutionRatio'] = round ( count ( $result ) / $totalCount, 5 );
			}
		} catch ( Exception $e ) {
			Bd_Log::warning ( $e->getMessage (), $e->getCode () );
			$arrResult ['errno'] = $e->getCode ();
			$arrResult ['errmsg'] = $e->getMessage ();
		}
		return $arrResult;
	}
}