<?php

//require_once LIB_PATH . '/ext/simple_html_dom.php';
require_once APP_PATH. '/evaluationpf/library/simple_html_dom.php';

class Service_Page_Thread_ListThreadPosts {
	private $_objServiceDataPost;
	
	public function __construct() {
		$this->_objServiceDataPost = new Service_Data_DictPost ();
	
	}
	
	public function autolink($foo) {
		$foo = eregi_replace ( '(((f|ht){1}tp://)[-a-zA-Z0-9@:%_/+.~#?&//=]+)', '<a href="/1" mce_href="/1" target=_blank rel=nofollow>/1</a>', $foo );
		if (strpos ( $foo, "http" ) === FALSE) {
			$foo = eregi_replace ( '(www.[-a-zA-Z0-9@:%_/+.~#?&//=]+)', '<a href="http:///1" mce_href="http:///1" target=_blank rel=nofollow >/1</a>', $foo );
		} else {
			$foo = eregi_replace ( '([[:space:]()[{}])(www.[-a-zA-Z0-9@:%_/+.~#?&//=]+)', '/1<a href="http:///2" mce_href="http:///2" target=_blank rel=nofollow >/2</a>', $foo );
		}
		return $foo;
	}
	public function url2html($text) {
		preg_match_all ( '/(http:\/\/|https:\/\/|ftp:\/\/)([\w:\/\.\?=&-_]+)/is', $text, $links );
		foreach ( $links [0] as $link_url ) {
			$link_text = $link_url;
			$text = str_replace ( $link_url, '< a href=' + $link_url + ' target=_blank rel=nofollow>$link_text</a>', $text );
		}
		return $text;
	}
	
	protected function _dealContent($content) {
		try {
			$content = stripslashes ( $content );
			$html = str_get_html ( $content );
		
		} catch ( Exception $e ) {
			$text = $content;
		}
		$href = array ();
		$plainText = array();
		foreach ( $html->find ( 'a' ) as $a ) {
			$href [] = $a->href;
			$plainText[] = $a->plaintext;
		}
		$text = $html->plaintext;
		$text = Evaluationpf_PVUtil::htmlDisplay ( $text );
		$images = array ();
		foreach ( $html->find ( 'img' ) as $img ) {
			
			if ($img->class != 'BDE_Smiley') {
				$images [] = $img->src;
			}
		}
		$videos = array ();
		foreach ( $html->find ( 'embed' ) as $video ) {
			if ($video->class == 'BDE_Flash') {
				$videos [] = $video->src;
			}
		}
		
		$href = array_unique($href); // add by qmy 20140623
		for($i = 0; $i < count ( $href ); $i ++) {
			/*
			 * bug fix by qinmengyao
			 * 
			 * 当 $text 即帖子文本中存在一直重复的url时，如下 str_replace() 会进入死循环，
			 * 应该是 $href 和$plainText 查找的有问题，replace时会使$text 大小一直翻倍，直至php 内存耗尽
			 * 
			 * 拟用 array_unique( $href ) 解决，不保证正确性……
			*/
			$text = str_replace ( $plainText [$i], "<a href=\"" . $href [$i] . "\" target=\"_blank\">" . $plainText [$i] . "</a>", $text );
		}
		return array ('text' => $text, 'images' => $images, 'videos' => $videos );
	}
	
	public function execute($arrInput) {
		$serviceDataTask = new Service_Data_Task ();
		Bd_Log::debug ( 'spampost list data page service called' );
		$arrResult = array ();
		$arrResult ['errno'] = 0;
		try {

			$page = $arrInput ['page'];
			$size = $arrInput ['size'];
			$taskId = $arrInput ['taskId'];
			$radios = $this->_objServiceDataPost->getRadioGroupsNum ( 1 );
			foreach ( $radios as $key => $one ) {
				$radios [$key] ['groupId'] = $one ['pid'];
				$dict = $this->_objServiceDataPost->findDictsByPid ( $one ['pid'] );
				$radios [$key] ['dicts'] = $dict;
			}
			$arrResult ['data'] ['radios'] = $radios;
			$results = $this->_objServiceDataPost->getSpamPostIdsByTaskId ( $taskId );
			$serviceDataTask->changeTaskStatusByTaskId ( $taskId, 2 );
			$totalCount = count ( $results );
			$arrResult ['data'] ['totalCount'] = $totalCount;
			$pager = Umpf_PagerUtil::genPager ( $totalCount, $page, $size, 5, array ('taskId' ) );
			$arrResult ['data'] ['pager'] = $pager;
			$posts = $this->_objServiceDataPost->getPostIdsByTaskId ( $taskId, $pager ['base'] ['page'], $size );
			$postIds = array ();
			foreach ( $posts as $post ) {
				$postIds [] = $post ['postId'];
			}
			$dicts = $this->_objServiceDataPost->getPostDictsByPostIds ( $postIds, $taskId );
			$arrResult ['data'] ['dicts'] = $dicts;
			$pullutionPostCounts = $this->_objServiceDataPost->getPullutionPostCountByTaskId ( $taskId );
			$arrResult ['data'] ['pullutionPostCount'] = count ( $pullutionPostCounts );
			$result = $this->_objServiceDataPost->findPostsByPostIds ( $postIds );
			$offset = Umpf_PagerUtil::getOffset ( array ('page' => $pager ['base'] ['page'], 'size' => $size ) );
			foreach ( $result as $key => $post ) {
				$result [$key] ['id'] = ++ $offset;
				$dealedCon = $this->_dealContent ( $post ['content'] );
				$result [$key] ['text'] = $dealedCon ['text'];
				$result [$key] ['images'] = $dealedCon ['images'];
				$result [$key] ['videos'] = $dealedCon ['videos'];
			}
			$arrResult ['data'] ['posts'] = $result;
			$totalRecordCount = $this->_objServiceDataPost->getRecordPostCountByTaskId ( $taskId );
			$arrResult ['data'] ['totalRecordCount'] = $totalRecordCount;
			if ($totalRecordCount == 0) {
				$totalRecordCount = 1;
			}
			$pullutionRatio = round ( count ( $pullutionPostCounts ) / $totalRecordCount, 5 );
			$serviceDataTask->updateRatioByTaskId ( $taskId, $pullutionRatio );
		
		} catch ( Exception $e ) {
			Bd_Log::warning ( $e->getMessage (), $e->getCode () );
			$arrResult ['errno'] = $e->getCode ();
			$arrResult ['errmsg'] = $e->getMessage ();
		}
		return $arrResult;
	}
}

?>