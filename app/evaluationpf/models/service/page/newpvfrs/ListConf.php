<?php

class Service_Page_NewPvfrs_ListConf {
	private $_objServiceDataTask;
	
	public function __construct() {
		$this->_objServiceDataTask = new Service_Data_Task ();
	}
	
	public function execute($taskType) {
		Bd_Log::debug ( 'list conf page service called' );
		$arrResult = array ();
		$arrResult ['errno'] = 0;
		try {
			$result = $this->_objServiceDataTask->getConfByTaskType ( $taskType );
			$arrResult ['data'] ['rule'] = $result;
		} catch ( Exception $e ) {
			Bd_Log::warning ( $e->getMessage (), $e->getCode () );
			$arrResult ['errno'] = $e->getCode ();
			$arrResult ['errmsg'] = $e->getMessage ();
		}
		return $arrResult;
	}
}

?>