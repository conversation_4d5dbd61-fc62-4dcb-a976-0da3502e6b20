<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [Task.php description]
 * <AUTHOR>
 * @DateTime 16/7/18 17:04
 * @brief
 */

class Service_Page_Task extends Service_Page_Base{

    private $_objServiceDataTask;

    public function __construct()
    {
        $this->_objServiceDataTask = new Service_Data_Task ();
    }


    /**
     * [listTasks 根据类型获取任务列表]
     * <AUTHOR>
     * @param
     * @return
     */
    public function listTasks($type, $page, $size){

        try{
            $output = array();
            if(is_array($type)){
                $totalCount = $this->_objServiceDataTask->getTasksCountByTypes($type);
            }else{
                $totalCount = $this->_objServiceDataTask->getTasksCountByType($type);
            }
            if (! is_numeric ( $totalCount )) {
                $totalCount = 0;
            }

            $pager = Umpf_PagerUtil::genPager ( $totalCount, $page, $size, 5 );
            $output['pager'] = $pager;

            if ($totalCount == 0) {
                $output['tasks'] = array ();
            } else {
                if(is_array($type)){
                    $result = $this->_objServiceDataTask->findTasksByTypes ( $pager ['base'] ['page'], $size, $type);
                }else{
                    $result = $this->_objServiceDataTask->findTasksByType ( $pager ['base'] ['page'], $size, $type);
                }
                $output['tasks'] = $result;

                /*
                foreach ($output['tasks'] as &$task){
                    $task['evaluated'] = $this->_objServiceDataTask->getImgsCount(array('taskid ='. $task['taskId'], 'type > 0'));
                }
                */
            }


            return $this->result("success", Tieba_Errcode::ERR_SUCCESS, $output);
        }catch(Exception $e){
            return $this->result($e->getMessage(), $e->getCode());
        }
    }

    /**
     * [deleteTask 删除任务]
     * <AUTHOR>
     * @param
     * @return
     */
    public function deleteTask($taskId, $deluser){
        try{
            if($this->_objServiceDataTask->delTaskByTaskId($taskId, $deluser, date("Y-m-d H:i:s"))){
                return $this->result("success", Tieba_Errcode::ERR_SUCCESS);
            }else{
                return $this->result("failed", -1);
            }
        }catch (Exception $e){
            return $this->result($e->getMessage(), $e->getCode());
        }
    }

    /**
     * [newTask 创建任务]
     * <AUTHOR>
     * @param
     * @return
     */
    public function newTask($arrInput){
        try{
            $task = array();
            $task['taskType'] = $arrInput['taskType'];
            $task['userName'] = $arrInput['userName'];
            $task['userId'] = $arrInput['userId'];
            $task['beginTime'] = $arrInput['beginDate']." ".$arrInput['beginTime'];
            $task['endTime'] = $arrInput['endDate']." ".$arrInput['endTime'];
            $task['dataType'] = $arrInput['dataType'];
            $task['createTime'] = date("Y-m-d H:i:s", time());

            switch ($arrInput['taskType']){
                case Evaluationpf_EvaluationInc::TASK_TYPE_STRATEGY_PHOTO :
                    $task['name'] = '图片策略评估_'.Evaluationpf_EvaluationInc::$datatype[$task['dataType']];
                    $task['eachNum'] = $arrInput['eachNum'];
                    $task['monitorList'] = $arrInput['monitorList'];
                    $monitorList = str_replace(array(",", ";", "\n", "\r\n", " "), " ", $arrInput['monitorList']);
                    $monitorListArr = explode(" ", $monitorList);
                    $task['monitorList'] = join("\n", $monitorListArr);
                    $task['totalNum'] = count($monitorListArr) * $task['eachNum'];
                    break;
                case Evaluationpf_EvaluationInc::TASK_TYPE_THREAD_PHOTO :
                    $task['name'] = '图片主题评估_'.Evaluationpf_EvaluationInc::$datatype[$task['dataType']];;
                    $task['totalNum'] = $arrInput['totalNum'];
                    break;
                case Evaluationpf_EvaluationInc::TASK_TYPE_POST_PHOTO :
                    $task['name'] = '图片回复评估_'.Evaluationpf_EvaluationInc::$datatype[$task['dataType']];;
                    $task['totalNum'] = $arrInput['totalNum'];
                    break;
                case Evaluationpf_EvaluationInc::TASK_TYPE_ACCOUNT :
                    $task['name'] = '账号分布评估';
                    $task['totalNum'] = $arrInput['totalNum'];
                    break;
                case Evaluationpf_EvaluationInc::TASK_TYPE_ACCESS :
                    $task['name'] = '账号分布评估';
                    $task['totalNum'] = $arrInput['totalNum'];
                    break;
                default:
                    $arrResult['errmsg'] = "未识别的任务类型";
                    $arrResult['errno'] = -1;
                    return $this->result("未识别的任务类型", -1);
            }


            if( $this->_objServiceDataTask->addTask($task)){
                return $this->result("success", Tieba_Errcode::ERR_SUCCESS);
            }else{
                return $this->result("添加失败了", -1);
            }

        }catch(Exception $e){
            return $this->result($e->getMessage(), $e->getCode());
        }
    }

    /**
     * [updateTask description]
     * <AUTHOR>
     * @param
     * @return
     */
    public function updateTask($taskId, $arrFields){
        try{
            if($this->_objServiceDataTask->updateTask($taskId, $arrFields)){
                return $this->result("success", Tieba_Errcode::ERR_SUCCESS);
            }else{
                return $this->result("更新失败了", -1);
            }
        }catch(Exception $e){
            return $this->result($e->getMessage(), $e->getCode());
        }
    }


    /**
     * [addImg description]
     * <AUTHOR>
     * @param
     * @return
     */
    public function addImg($imginfo){
        try{
            $insertid = $this->_objServiceDataTask->addImg($imginfo);

            if($insertid > 0){
                return $this->result("success", Tieba_Errcode::ERR_SUCCESS);
            }else{
                return $this->result("添加失败了", -1);
            }
        }catch(Exception $e){

        }
    }

    /**
     * [addUser ]
     * <AUTHOR>
     * @param
     * @return
     */
    public function addUser($userinfo){
        try{
            $insertid = $this->_objServiceDataTask->addUser($userinfo);

            if($insertid > 0){
                return $this->result("success", Tieba_Errcode::ERR_SUCCESS);
            }else{
                return $this->result("添加失败了", -1);
            }
        }catch (Exception $e){
            return $this->result($e->getMessage(), $e->getCode());
        }

    }

    /**
     * [listImgs description]
     * <AUTHOR>
     * @param
     * @return
     */
    public function listImgs($taskId, $page, $size){
        try{
            $output = array();
            $totalCount = $this->_objServiceDataTask->getImgsCount(array('taskid=' => $taskId));

            if (! is_numeric ( $totalCount )) {
                $totalCount = 0;
            }

            $pager = Umpf_PagerUtil::genPager ( $totalCount, $page, $size, 5 );
            $output['pager'] = $pager;

            if ($totalCount == 0) {
                $output['items'] = array ();
            } else {

                $result = $this->_objServiceDataTask->listImgs($page, $size, array('taskid=' => $taskId));
                $output['items'] = $result;

                foreach ($output['items'] as $key=>&$item){
                    $item['id'] = Umpf_PagerUtil::getOffset(array('page' =>$page, 'size' => $size))+$key+1;
                }
                unset($item);
            }

            $output['total'] = $totalCount;
            $output['evaluated'] = $this->_objServiceDataTask->getImgsCount(array('taskid =' => $taskId, 'type >' => 0));
            return $this->result("success", Tieba_Errcode::ERR_SUCCESS, $output);
        }catch(Exception $e){
            return $this->result($e->getMessage(), $e->getCode());
        }
    }

    /**
     * [countImgs description]
     * <AUTHOR>
     * @param
     * @return
     */
    public function countImgs($conds){
        try{
            $totalCount = $this->_objServiceDataTask->getImgsCount($conds);
            return $this->result("sucess", Tieba_Errcode::ERR_SUCCESS, $totalCount);
        }catch(Exception $e){
            return $this->result($e->getMessage(), $e->getCode());
        }
    }

    /**
     * [markImg description]
     * <AUTHOR>
     * @param
     * @return
     */
    public function markImg($taskId, $imgId, $type){
        try{
            $res = $this->_objServiceDataTask->markImg($taskId, $imgId, $type);

            if($res){
                $totalCount = $this->_objServiceDataTask->getImgsCount(array('taskid =' => $taskId));
                $evaluatedCount = $this->_objServiceDataTask->getImgsCount(array('taskid =' => $taskId, 'type >' => 0));

                if($totalCount == $evaluatedCount){
                    $this->_objServiceDataTask->changeTaskStatusByTaskId($taskId, Evaluationpf_EvaluationInc::TASK_STATUS_COMPLETED);
                } else if($evaluatedCount > 0){
                    $this->_objServiceDataTask->changeTaskStatusByTaskId($taskId, Evaluationpf_EvaluationInc::TASK_STATUS_IN_EVALUATION);
                }

                return $this->result("success", Tieba_Errcode::ERR_SUCCESS);
            }
        }catch(Exception $e){
            return $this->result($e->getMessage(), $e->getCode());
        }
    }


    /**
     * [listPosts description]
     * <AUTHOR>
     * @param
     * @return
     */
    public function listPosts($taskId, $page, $size){
            try{
                $output = array();
                $totalCount = $this->_objServiceDataTask->getPostsCount(array('task_id=' => $taskId));

                if (! is_numeric ( $totalCount )) {
                    $totalCount = 0;
                }

                $pager = Umpf_PagerUtil::genPager ( $totalCount, $page, $size, 5 );
                $output['pager'] = $pager;

                if ($totalCount == 0) {
                    $output['items'] = array ();
                } else {

                    $result = $this->_objServiceDataTask->listPosts($page, $size, array('task_id=' => $taskId));
                    $output['items'] = $result;

                    $postids = array();

                    foreach ($output['items'] as $key=>&$item){
                        $item['id'] = Umpf_PagerUtil::getOffset(array('page' =>$page, 'size' => $size))+$key+1;
                        $postids[] = $item['post_id'];
                    }

                    unset($item);

                    if(!empty($postids)){
                        $arrPosts = Evaluationpf_PBUtil::getPostsInfo ( $postids );
                        $tmpposts = array();

                        foreach ($arrPosts as $key => $post){
                            $tmpposts[$post['post_id']] = $post;
                        }

                        foreach($output['items'] as $key=>&$item){
                            $item = array_merge($item, $tmpposts[$item['post_id']]);
                        }
                    }
                    unset($item);
                }

                return $this->result("success", Tieba_Errcode::ERR_SUCCESS, $output);
            }catch(Exception $e){
                return $this->result($e->getMessage(), $e->getCode());
            }
    }

    /**
     * [countPosts description]
     * <AUTHOR>
     * @param
     * @return
     */
    public function countPosts($conds){
        try{
            $totalCount = $this->_objServiceDataTask->getPostsCount($conds);
            return $this->result("sucess", Tieba_Errcode::ERR_SUCCESS, $totalCount);
        }catch(Exception $e){
            return $this->result($e->getMessage(), $e->getCode());
        }
    }



    /**
     * [getUser description]
     * <AUTHOR>
     * @param
     * @return
     */
    public function getUser($taskId, $uid){
        try{
            $user = $this->_objServiceDataTask->getUser(array('taskid =' => $taskId, 'uid =' => $uid));
            return $this->result("success", Tieba_Errcode::ERR_SUCCESS, $user);
        }catch(Exception $e){
            return $this->result($e->getMessage(), $e->getCode());
        }
    }

    /**
     * [listUsers 获取任务中用户列表]
     * <AUTHOR>
     * @param
     * @return
     */
    public function listUsers($taskId, $page, $size){
        try{
            $output = array();
            $totalCount = $this->_objServiceDataTask->getUsersCount(array('taskid=' => $taskId));

            if (! is_numeric ( $totalCount )) {
                $totalCount = 0;
            }

            $pager = Umpf_PagerUtil::genPager ( $totalCount, $page, $size, 5 );
            $output['pager'] = $pager;

            if ($totalCount == 0) {
                $output['items'] = array ();
            } else {

                $result = $this->_objServiceDataTask->listUsers($page, $size, array('taskid=' => $taskId));
                $output['items'] = $result;

                foreach ($output['items'] as $key=>&$item){
                    $item['id'] = Umpf_PagerUtil::getOffset(array('page' =>$page, 'size' => $size))+$key+1;
                }
                unset($item);
            }

            //计算评估情况
            $typesCount = $this->_objServiceDataTask->getUsersCountByGroup(array('taskid=' => $taskId), array('type'));

            $output['typesCount'] = array(
                'total' => 0
            );
            foreach($typesCount as $row){
                $output['typesCount']['type'.$row['type']] = $row['count'];
                $output['typesCount']['total'] += $row['count'];
            }

            return $this->result("success", Tieba_Errcode::ERR_SUCCESS, $output);
        }catch(Exception $e){
            return $this->result($e->getMessage(), $e->getCode());
        }
    }

    /**
     * [countUsers 计数]
     * <AUTHOR>
     * @param
     * @return
     */
    public function countUsers($conds){
        try{
            $totalCount = $this->_objServiceDataTask->getUsersCount($conds);
            return $this->result("sucess", Tieba_Errcode::ERR_SUCCESS, $totalCount);
        }catch(Exception $e){
            return $this->result($e->getMessage(), $e->getCode());
        }
    }


    /**
     * [markUser 标注用户类型]
     * <AUTHOR>
     * @param
     * @return
     */
    public function markUser($taskId, $uid, $type){
        try{
            $res = $this->_objServiceDataTask->markUser($taskId, $uid, $type);

            if($res){
                //更新评估率
                $totalCount = $this->_objServiceDataTask->getUsersCount(array(
                    'taskid='=>$taskId,
                ));

                $evaluatedCount = $this->_objServiceDataTask->getUsersCount(array(
                    'taskid='=>$taskId,
                    'type > 0',
                ));

                if($totalCount == $evaluatedCount){
                    $this->_objServiceDataTask->changeTaskStatusByTaskId($taskId, Evaluationpf_EvaluationInc::TASK_STATUS_COMPLETED);
                } else if($evaluatedCount > 0){
                    $this->_objServiceDataTask->changeTaskStatusByTaskId($taskId, Evaluationpf_EvaluationInc::TASK_STATUS_IN_EVALUATION);
                }
                return $this->result("success", Tieba_Errcode::ERR_SUCCESS);
            }
        }catch(Exception $e){
            return $this->result($e->getMessage(), $e->getCode());
        }
    }

    public function getNextUser($taskId, $uid){
        try{
            $user = $this->_objServiceDataTask->getNextUser($taskId, $uid);
            return $this->result("success", Tieba_Errcode::ERR_SUCCESS, $user);
        }catch(Exception $e){
            return $this->result($e->getMessage(), $e->getCode());
        }
    }
}