<?php
class Service_Page_Newstatis_PbExcelOut {
	private $_objServiceDataPost;
	private $_objServiceDataPv;
	private $_objServiceDictPost;
	
	public function __construct() {
		$this->_objServiceDataPv = new Service_Data_Pv ();
		$this->_objServiceDataPost = new Service_Data_MarkPost ();
		$this->_objServiceDictPost = new Service_Data_DictPost ();
	}
	
	public function execute($taskId) {
		Bd_Log::debug ( 'Service_Page_Statis_ExcelOut called' );
		$arrResult = array ();
		$arrResult ['errno'] = 0;
		try {
			$totalCount = $this->_objServiceDataPv->getPvCountByTaskId ( $taskId );
			Bd_Log::debug ( $totalCount );
			if ($totalCount == 0) {
				$arrResult ['data'] ['pvs'] = array ();
			} else {
				$totalPvIds = $this->_objServiceDataPv->findPvsIdsByTaskId ( $taskId );
				$pvIds = array ();
				foreach ( $totalPvIds as $pvRecord ) {
					$pvIds [] = $pvRecord ['pvId'];
				}
				$posts = $this->_objServiceDictPost->getAllPostsByPvIds ( $pvIds );
				$dicts = $this->_objServiceDictPost->getTotalDictNames ();
				$dictNames = array ();
				foreach ( $dicts as $dict ) {
					$dictNames [$dict ['dict_id']] = $dict ['name'];
				}
				$data = array (0 => array ('浏览时间', '吧ID', '吧名', '主题ID', '帖子标题', '内容', '发帖用户', '用户ID', '终端类型', '发帖时间', '作弊类型', '帖子类型', 'ip' ) );
				
				$realPosts = array ();
				$realPostsTags = array ();
				foreach ( $posts as $row ) {
					$realPosts [$row ['post_id']] = $row;
					if ($row ['dict_id'] > 1) {
						$realPostsTags [$row ['post_id']] [] = $row ['dict_id'];
					}
				}
				//$postPids = array ();
				foreach ( $realPosts as $postId => $post ) {
					//$postPids [] = $postId;
					$tags = $realPostsTags [$postId];
					$strTag = '';
					$postTag = '';
					$subTitle = substr ( iconv('utf-8', 'gbk', $post ['title']), 0, 6 );
					if ($subTitle == iconv('utf-8', 'gbk', '回复：')) {
						$postTag = '回复';
					} else {
						$postTag = '主题';
					}
					if (! empty ( $tags )) {
						foreach ( $tags as $tag ) {
							$strTag .= $dictNames [$tag] . ',';
						}
						$strTag = trim ( $strTag, ',' );
					}
					array_push ( $data, array ($post ['pv_time'], $post ['forum_id'], $post ['forum_name'], $post ['thread_id'], $post ['title'], $post ['content'], $post ['user_name'], $post ['user_id'], $post ['post_type'], $post ['create_time'], $strTag, $postTag, $post ['ip'] ) );
				}
				$postComments = $this->_objServiceDictPost->getAllCommentsByPvIds ( $pvIds );
				$realComments = array ();
				$realCommentsTags = array ();
				foreach ( $postComments as $row ) {
					$realComments [$row ['post_id']] = $row;
					if ($row ['dict_id'] > 1) {
						$realCommentsTags [$row ['post_id']] [] = $row ['dict_id'];
					}
				}
				foreach ( $realComments as $postId => $comment ) {
					//$postPids [] = $postId;
					$tags = $realCommentsTags [$postId];
					$strTag = '';
					$postTag = '楼中楼';
					if (! empty ( $tags )) {
						foreach ( $tags as $tag ) {
							$strTag .= $dictNames [$tag] . ',';
						}
						$strTag = trim ( $strTag, ',' );
					}
					array_push ( $data, array ($comment ['pv_time'], $comment ['forum_id'], $comment ['forum_name'], $comment ['thread_id'], $comment ['title'], $comment ['content'], $comment ['user_name'], $comment ['user_id'], $comment ['post_type'], $comment ['create_time'], $strTag, $postTag, $comment ['ip'] ) );
				}
				
				require_once ROOT_PATH . '/app/' . MODULE_NAME . '/library/evaluationpf/php-excel.class.php';
				Bd_Log::debug ( 'before excel' );
				Bd_Log::debug ( json_encode ( $data [1] ) );
				$date = date ( 'YmdHis' );
				$xls = new Excel_XML ();
				$xls->addArray ( $data );
				$xls->generateXML ( "$date" );
			}
		
		} catch ( Exception $e ) {
			Bd_Log::warning ( $e->getMessage (), $e->getCode () );
			$arrResult ['errno'] = $e->getCode ();
			$arrResult ['errmsg'] = $e->getMessage ();
		}
		return $arrResult;
	}
}

?>