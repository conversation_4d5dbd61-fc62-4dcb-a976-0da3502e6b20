<?php

//require_once LIB_PATH . '/ext/simple_html_dom.php';
require_once APP_PATH. '/evaluationpf/library/simple_html_dom.php';


class Service_Page_Spam_ListSpamPosts {
	private $_objServiceDataPost;
	
	public function __construct() {
		$this->_objServiceDataPost = new Service_Data_MarkPost ();
	
	}
	
	public function autolink($foo) {
		$foo = eregi_replace ( '(((f|ht){1}tp://)[-a-zA-Z0-9@:%_/+.~#?&//=]+)', '<a href="/1" mce_href="/1" target=_blank rel=nofollow>/1</a>', $foo );
		if (strpos ( $foo, "http" ) === FALSE) {
			$foo = eregi_replace ( '(www.[-a-zA-Z0-9@:%_/+.~#?&//=]+)', '<a href="http:///1" mce_href="http:///1" target=_blank rel=nofollow >/1</a>', $foo );
		} else {
			$foo = eregi_replace ( '([[:space:]()[{}])(www.[-a-zA-Z0-9@:%_/+.~#?&//=]+)', '/1<a href="http:///2" mce_href="http:///2" target=_blank rel=nofollow >/2</a>', $foo );
		}
		return $foo;
	}
	public function url2html($text) {
		preg_match_all ( '/(http:\/\/|https:\/\/|ftp:\/\/)([\w:\/\.\?=&-_]+)/is', $text, $links );
		foreach ( $links [0] as $link_url ) {
			$link_text = $link_url;
			$text = str_replace ( $link_url, '< a href=' + $link_url + ' target=_blank rel=nofollow>$link_text</a>', $text );
		}
		return $text;
	}
	protected function _dealContent($content) {
		try {
			$content = stripslashes ( $content );
			$html = str_get_html ( $content );
		
		} catch ( Exception $e ) {
			$text = $content;
		}
		$href = array ();
		foreach ( $html->find ( 'a' ) as $a ) {
			$href [] = $a->href;
		}
		$text = $html->plaintext;
		$text = Evaluationpf_PVUtil::htmlDisplay ( $text );
		$images = array ();
		foreach ( $html->find ( 'img' ) as $img ) {
			
			if ($img->class != 'BDE_Smiley') {
				$images [] = $img->src;
			}
		}
		$videos = array ();
		foreach ( $html->find ( 'embed' ) as $video ) {
			if ($video->class == 'BDE_Flash') {
				$videos [] = $video->src;
			}
		}
		for($i = 0; $i < count ( $href ); $i ++) {
			$text = str_replace ( $href [$i], "<a href=\"" . $href [$i] . "\" target=\"_blank\">" . $href [$i] . "</a>", $text );
		}
		return array ('text' => $text, 'images' => $images, 'videos' => $videos );
	}
	
	public function execute($arrInput) {
		$serviceDataTask = new Service_Data_Task ();
		Bd_Log::debug ( 'spampost list data page service called' );
		$arrResult = array ();
		$arrResult ['errno'] = 0;
		try {
			$page = $arrInput ['page'];
			$size = $arrInput ['size'];
			$taskId = $arrInput ['taskId'];
			$results = $this->_objServiceDataPost->getSpamPostIdsByTaskId ( $taskId );
			$serviceDataTask->changeTaskStatusByTaskId ( $taskId, 2 );
			$postIds = array ();
			foreach ( $results as $record ) {
				$postIds [] = $record ['postId'];
			}
			$totalCount = count ( $postIds );
			$arrResult ['data'] ['totalCount'] = $totalCount;
			$pager = Umpf_PagerUtil::genPager ( $totalCount, $page, $size, 5, array ('taskId' ) );
			$arrResult ['data'] ['pager'] = $pager;
			$result = $this->_objServiceDataPost->findSpamPostsByPostIds ( $postIds, $pager ['base'] ['page'], $size );
			$pullutionPostCounts = $this->_objServiceDataPost->getPullutionPostCountByPostIds ( $postIds );
			$arrResult ['data'] ['pullutionPostCount'] = count ( $pullutionPostCounts );
			$totalRecordCount = $this->_objServiceDataPost->getRecordPostCountByTaskId ( $taskId );
			if ($totalRecordCount == 0) {
				$totalRecordCount = 1;
			}
			$pullutionRatio = round ( count ( $pullutionPostCounts ) / $totalRecordCount, 5 );
			$serviceDataTask->updateRatioByTaskId ( $taskId, $pullutionRatio );
			$offset = Umpf_PagerUtil::getOffset ( array ('page' => $pager ['base'] ['page'], 'size' => $size ) );
			foreach ( $result as $key => $post ) {
				$result [$key] ['id'] = ++ $offset;
				$dealedCon = $this->_dealContent ( $post ['content'] );
				$result [$key] ['text'] = $dealedCon ['text'];
				$result [$key] ['images'] = $dealedCon ['images'];
				$result [$key] ['videos'] = $dealedCon ['videos'];
			}
			$arrResult ['data'] ['posts'] = $result;
		} catch ( Exception $e ) {
			Bd_Log::warning ( $e->getMessage (), $e->getCode () );
			$arrResult ['errno'] = $e->getCode ();
			$arrResult ['errmsg'] = $e->getMessage ();
		}
		return $arrResult;
	}
}

?>