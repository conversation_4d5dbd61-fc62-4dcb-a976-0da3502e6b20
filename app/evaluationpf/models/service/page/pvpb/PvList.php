<?php

class Service_Page_Pvpb_PvList {
	private $_objServiceDataTask;
	
	public function __construct() {
		$this->_objServiceDataTask = new Service_Data_Task ();
	}
	
	public function execute($taskId) {
		Bd_Log::debug ( 'pv list page service called' );
		$arrResult = array ();
		$arrResult ['errno'] = 0;
		try {
			$task = $this->_objServiceDataTask->getTaskByTaskId ( $taskId );
			$arrResult ['data'] ['task'] = $task;
		} catch ( Exception $e ) {
			Bd_Log::warning ( $e->getMessage (), $e->getCode () );
			$arrResult ['errno'] = $e->getCode ();
			$arrResult ['errmsg'] = $e->getMessage ();
		}
		return $arrResult;
	}
}

?>