<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2012.10.24
 * @update by gongwei01 2012-11-30 17:20:47
 * @version  2.0
**/
class Data_Zhibo {

	// zhibo-ui�ﴫ���ı�������
	const ZHIBO_TEXT = 1; // ����ֱ����
	const ZHIBO_TALK = 2; // ��̸ֱ����
	const TOPIC_TEXT = 3; // ���ֻ�����
	const TOPIC_PICT = 4; // ͼƬ������

	// ģ�������Ҫ��ӡ�ı�������
	const DISPLAY_TYPE_ZHIBO_TEXT = 1;
	const DISPLAY_TYPE_ZHIBO_TALK = 2;
	const DISPLAY_TYPE_TOPIC_TEXT = 1;
	const DISPLAY_TYPE_TOPIC_PICT = 2;

	public static function getZhiboInfo($strFname) 
	{
		$arrRet = self::getFormZhibo ($strFname);		

		return $arrRet;
	}

	//��zhibo ui��ȡ��ǰ�ɿ��ŵ�ֱ������Ϣ
	private static function getFormZhibo($strFname) {
		$arrData = array('kw'=>$strFname);
		$url = "/zhibo/data/forum";
		$res = Tbapi_Midlib_Midl_InnerHttp::call($arrData, $url, rand());

		$arrRes = self::_initData ();
		$res = Bingo_String::json2array($res);
		if (false === $res || !isset ($res['no']) || $res['no']!=0) {
			Bingo_Log :: warning( sprintf(__CLASS__ ."::".__METHOD__. " call Tbapi_Midlib_Midl_InnerHttp::call() fail url[%s] fname[%s]", $url, $strFname));
		} else {
			$arrRes = self::_convertRes ($res);
		}
		return $arrRes;
	}

	// ��ȡĬ�Ϸ���ֵ
	protected static function _initData () {
		return array (
			'zhibo'		=> array (
				'forum'		=> 0, // ��id
				'tid'			=> 0, // ����id
				'status'		=> 0, // �Ƿ��� �Ժ���ʹ��
				'type'			=> 0, // ���� 1 ��ʾ���ֻ����� ����ֱ���� 2 ��ʾͼƬ������
				'int1'			=> 0, // Ҫ���ߵı�������ʶ�Ƿ���ֱ����������
				'has_lpost'		=> 0, // �����ӵı�����1 ��ʾ��ֱ��������������ֱ�����ͷ�̸ֱ��������
				'lpost_type'	=> 0, // �����ӵı�����1 ��ʾ����ֱ���� 2 ��ʾ��̸ֱ����
			),
			'has_live_post'	=> 0, // �ɱ�����ֻ������ֱ����ʱ�Ż���Ϊ1���Ժ������
		);
	}

	// �������һ��ת�����������Ͻӿ�
	protected static function _convertRes ($arrInfo) {
		$arrData = $arrInfo['data'];
		$arrRet = self::_initData ();
		if (isset ($arrData['forum'])) {
			$arrRet['zhibo']['forum'] = $arrData['forum'];
		} else if (isset ($arrData['fid'])) {
			$arrRet['zhibo']['forum'] = $arrData['fid'];
		} else {
			$arrRet['zhibo']['forum'] = 0;
		}
		
		$arrRet['zhibo']['tid'] = $arrData['tid'];
		if (isset ($arrData['version']) && isset ($arrData['version'])==2) { // �°汾
			switch ($arrData['lpost_type']) {
				case self::ZHIBO_TEXT:
					$arrRet['zhibo']['status'] = 1; // ���ݾɽӿ�
					$arrRet['zhibo']['type'] = self::DISPLAY_TYPE_TOPIC_TEXT; // ���ݾɽӿ�
					$arrRet['zhibo']['int1'] = 1; // ���ݾɽӿ�
					$arrRet['zhibo']['has_lpost'] = 1;
					$arrRet['zhibo']['lpost_type'] = self::DISPLAY_TYPE_ZHIBO_TEXT;
					$arrRet['has_live_post'] = 1; // ���ݾɽӿ�
					break;
				case self::ZHIBO_TALK:
					$arrRet['zhibo']['has_lpost'] = 1;
					$arrRet['zhibo']['lpost_type'] = self::DISPLAY_TYPE_ZHIBO_TALK;
					break;
				case self::TOPIC_TEXT:
					$arrRet['zhibo']['status'] = 1;
					$arrRet['zhibo']['type'] = self::DISPLAY_TYPE_TOPIC_TEXT;
					break;
				case self::TOPIC_PICT:
					$arrRet['zhibo']['status'] = 1;
					$arrRet['zhibo']['type'] = self::DISPLAY_TYPE_TOPIC_PICT;
			}
		} else {
			$arrRet['zhibo']['status'] = 1;
			$arrRet['zhibo']['type'] = $arrData['type'];
			$arrRet['zhibo']['int1'] = $arrData['int1'];
			$arrRet['has_live_post'] = $arrData['int1'];
			if ($arrData['int1']) { // ����ֱ��
				$arrRet['zhibo']['has_lpost'] = 1;
				$arrRet['zhibo']['lpost_type'] = self::DISPLAY_TYPE_ZHIBO_TEXT;
			}
		}
		return $arrRet;
	}
		
}
/* vim: set noexpandtab ts=4 sw=4 sts=4 tw=0: */