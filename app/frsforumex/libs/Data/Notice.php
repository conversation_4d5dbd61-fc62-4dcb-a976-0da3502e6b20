<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2012.07
 * @version  1.0
**/


class Data_Notice {
	private static $all_notice_info = '/home/<USER>/forum-php/data/frs/all_notice_info';
	public static function getNotice($input=array()) 
	{
		if (defined('IS_ORP_RUNTIME') && IS_ORP_RUNTIME) {
			self::$all_notice_info = ROOT_PATH . '/data/app/frsui/all_notice_info';
		}
			

		$result = array('thread_notice'=>null);
		if ( !isset($input['forum_name']) || !isset($input['dir1_name']))
		{
			Bingo_Log :: warning("err input: ".serialize($input));
			return $result;
		}
		$key = 'notice_thread';
		$notice_threads = FrsEaccCache::get($key);
		$notices = array();
		$file = self::$all_notice_info;
		if($notice_threads === NULL && file_exists($file))
		{
			$data = file($file);
			foreach($data as $value)
			{
				$value = trim($value);
				$thread_info = explode ("\t",$value);
				if (count($thread_info) < 3)
				{
					continue;
				}
                        $items = explode("/", $thread_info[1]);
                        $tid = $items[count($items)-1];
				$notice_thread = array 
					(       
					 'title'         =>  $thread_info[0],
					 'title_link'   =>  $thread_info[1],
					 'author'        =>  '���ɹ���',
					 'id'           => $tid,
					);      
				$notice_threads[$thread_info[2]] = $notice_thread;
			}       
			FrsEaccCache::add($key,$notice_threads);
		}       

		if($notice_threads === NULL){
			return $result;
		}       
		//      var_dump($notice_threads);
		$dir1 = $input['dir1_name'];
		if($dir1 === ''){ 
			$dir1 = 'blank';
		}       
		if(isset($notice_threads['all'])){
			$notices[]=$notice_threads['all'];
		}       
		if(isset($notice_threads[$dir1])){
			$notices[]=$notice_threads[$dir1];
		}
		$result['thread_notice'] = $notices;       
		return $result;
	}
};

?>
