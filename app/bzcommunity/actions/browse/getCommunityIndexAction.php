<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2016-07-14 11:02:06
 * @comment json接口
 * @version
 */
class getCommunityIndexAction extends Util_Base {
    const WORDLIST_NAME_BANNER  = 'tb_wordlist_redis_bzcommunity_banner';
    const WORDLIST_START_BANNER = 0;
    const WORDLIST_END_BANNER   = 5;
    const WORDLIST_NAME_TOPIC   = 'tb_wordlist_redis_bzcommunity_topic';
    const WORDLIST_START_TOPIC  = 0;
    const WORDLIST_END_TOPIC    = 1;

    private $_intErrno = 0;

    private static $_arrMulti = array(
        'Thread',
        'UserPower',
    );
    private static $_arrSingle = array(
        'Tag',
        'Banner',
        'Topic',
    );
    private $_outData = array(
        'thread_list' => array(),
        'has_more'    => 0,
        'thread_id_list' => array(),
        'user_role'   => array(),
        'banner_list' => array(),
        'today_topic' => array(),
        'tag_list'    => array(),
    );
    /**
     * @param
     * @return
     **/
    public function  _getPrivateInfo(){
        $arrPrivate = array(
            'rn'    => (int)$this->_getInput('rn', 30),
            'rn_ids'=> $this->_getInput('rn_ids', 30),
        );
        return $arrPrivate;
    }   
    /**
     * @param
     * @return
     **/
    public function _checkBZPrivate(){
        return true;
    }
    /**
     * [_execute description]
     * @param
     * @return [type] [description]
     */
    public function _execute() {
        foreach(self::$_arrSingle as $strategy){
            $bolRet = call_user_func(array(self, '_get' . $strategy), $arrInput);
        }

        $arrMultiOutput = array();
        $arrResult = array();
        $objRalMulti = new Tieba_Multi('bzc_index');
        foreach(self::$_arrMulti as $strategy){
            $arrMultiInput[$strategy] = call_user_func(array(self, '_get' . $strategy . 'Input'), $arrInput);
            foreach($arrMultiInput[$strategy] as $key => $multiInput){
                $objRalMulti -> register($strategy . '_' . $key, new Tieba_Service($multiInput['service_name']), $multiInput);
            }
        }

        $objRalMulti -> call();
        foreach(self::$_arrMulti as $strategy){
            foreach($arrMultiInput[$strategy] as $key => $multiInput){
                $temp = $objRalMulti -> getResult($strategy . '_' . $key);
                // if(false === $temp||Tieba_Errcode::ERR_SUCCESS !== $temp['errno']){
                //     $errno = (isset($temp['errno']) ? $temp['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                //     $strReason = "call service failed!";
                //     Bingo_Log::warning(sprintf(" %s input[%s] output[%s]", $strReason, serialize($arrMultiInput[$strategy]), serialize($temp)));
                //     //return self::_errRet($errno);
                // }
                $arrMultiOutput[$strategy][$key] = $temp;
            }
            $bolRet = call_user_func(array(self, '_get' . $strategy . 'Output'), $arrMultiOutput[$strategy]);
            if(!$bolRet){
                $this->_stLog();
                $this->_error($this->_intErrno, Molib_Client_Error::getErrMsg($this->_intErrno), $this->_outData, false);
                return false;
            }
        }
        
        $this->_stLog();
        $this->_objResponse->setOutData($this->_outData);
        return true;

    }
    /**
     * [_stLog description]
     * @param
     * @return [type] [description]
     */
    private function _stLog(){
        Tieba_Stlog::addNode('obj_type', 'index');
    }
    /**
     * [_getThreadInput description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    private function _getThreadInput($arrInput){
        $intRn = $this->_objRequest->getPrivateAttr('rn');
        $intRnIds = $this->_objRequest->getPrivateAttr('rn_ids');
        $intPn = 1;

        $arrMultiInput[] = array(
            'service_name' => 'bzcommunity',
            'method'       => 'getThreadList',
            'input'        => array(
                'rn'    => $intRn,
                'rn_id' => $intRnIds,
                'pn'    => $intPn,
            ),
            'ie' => 'utf-8',
        );
        return $arrMultiInput;
    }
    /**
     * [_getThreadOutput description]
     * @param  [type] $arrOutput [description]
     * @return [type]            [description]
     */
    private function _getThreadOutput($arrOutput){
        if(false === $arrOutput[0]){
            Bingo_Log::fatal(sprintf("get thread_list failed! input[%s]",serialize($arrMultiInput[$strategy]) ));
            $this->_intErrno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            return false;
        }else if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput[0]['errno']){
            $this->_intErrno = $arrOutput[0]['errno'];
            Bingo_Log::warning(sprintf(" get threadlist failed! input[%s] output[%s]", serialize($arrMultiInput[$strategy]), serialize($arrOutput)));
            return false;
        }
        $arrExtraThreadIds =array();
        foreach($arrOutput[0]['data']['thread_id'] as $id){
            $arrExtraThreadIds [] =(int)$id;
        }
        $intHasMore = (int)$arrOutput[0]['data']['has_more'];
        $arrThreadList = array();
        foreach($arrOutput[0]['data']['thread_list'] as $thread){
            $temp = array();
            $temp['tid'] =$thread['thread_id'];
            $temp = array(
                'user_info' => $thread['user_info'],
                'tid'       => (int)$thread['thread_id'],
                'title'     => $thread['title'],
                'reply_num' => (int)$thread['reply_num'],
                'last_time' => (int)$thread['last_time'],
                'tag_info'  => array(
                    'tag_id' => (int)$thread['tag_info']['tag_id'],
                    'tag_name' => $thread['tag_info']['tag_name'],
                    'tag_desc' => $thread['tag_info']['tag_desc'],
                    'icon' => $thread['tag_info']['icon'],
                ),
                'voice_info' => $thread['voice_info'],
                'media'      => $thread['media'],
                'location'   => $thread['location'],
                'abstract'   => urldecode($thread['abstract']),
            );
            $arrThreadList []= $temp;
        }
        $arrThreadList = Lib_Media::buildFrsMedia($arrThreadList,$this->_getSpec());

        $this->_setOutData('has_more', $intHasMore);
        $this->_setOutData('thread_list', $arrThreadList);
        $this->_setOutData('thread_id_list', $arrExtraThreadIds);
        return true;
    }
    /**
     * [_getUserPowerInput description]
     * @param
     * @return [type] [description]
     */
    private function _getUserPowerInput(){
        $intUid = $this->_objRequest->getCommonAttr('user_id');
        $arrMultiInput[] = array(
            'service_name' => 'bzcommunity',
            'method'       => 'getUserPower',
            'input'        => array(
                'user_id'  => $intUid,
            ),
            'ie' => 'utf-8',
        );
        return $arrMultiInput;
    }
    /**
     * [_getUserPowerOutput description]
     * @param  [type] $arrOutput [description]
     * @return [type]            [description]
     */
    private function _getUserPowerOutput($arrOutput){
        if(false === $arrOutput[0]){
            Bingo_Log::fatal(sprintf("get user_power failed! input[%s]",serialize($arrMultiInput[$strategy])));
            $this->_intErrno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            //return false;
        }else if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput[0]['errno']){
            $this->_intErrno = $arrOutput[0]['errno'];
            Bingo_Log::warning(sprintf(" get userpower failed! input[%s] output[%s]", serialize($arrMultiInput[$strategy]), serialize($arrOutput)));
            //return false;
        }
        $arrOut = $arrOutput[0]['data'];
        $arrPower = array(
            'can_del_thread' => (int)$arrOut['can_delpost'],
        );
        $this->_setOutData('user_role', $arrPower);
        return true;
    }
    /**
     * [_getBannerInput description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    /*private function _getBannerInput($arrInput){
        $arrMultiInput[] = array(
            'service_name' => 'wordlist',
            'method'       => 'queryWLItemDirectly',
            'input'        => array(
                'table_name' => self::WORDLIST_NAME,
                'key' => self::BANNER_KEY_1,
            ),
            'ie' => 'gbk',
        );
        $arrMultiInput[] = array(
            'service_name' => 'wordlist',
            'method'       => 'queryWLItemDirectly',
            'input'        => array(
                'table_name' => self::WORDLIST_NAME,
                'key' => self::BANNER_KEY_2,
            ),
            'ie' => 'gbk',
        );
        $arrMultiInput[] = array(
            'service_name' => 'wordlist',
            'method'       => 'queryWLItemDirectly',
            'input'        => array(
                'table_name' => self::WORDLIST_NAME,
                'key' => self::BANNER_KEY_3,
            ),
            'ie' => 'gbk',
        );
        return $arrMultiInput;
    }*/
    /**
     * [_getBannerOutput description]
     * @param  [type] $arrOutput [description]
     * @return [type]            [description]
     */
    /*private function _getBannerOutput($arrOutput){
        $arrOut = array();
        $arrBanner = array();
        $arrOut[] = $arrOutput[0]['data'][self::BANNER_KEY_1];
        $arrOut[] = $arrOutput[1]['data'][self::BANNER_KEY_2];
        $arrOut[] = $arrOutput[2]['data'][self::BANNER_KEY_3];
        foreach($arrOut as $key => $val){
            //$banner = Bingo_String::json2array($val, Bingo_Encode::ENCODE_UTF8);
            if(isset($arrOut[$key])){
                $banner = unserialize($val);
                $arrBanner []= array(
                    'id'       => $banner[0],
                    'jump_url' => $banner[1],
                    'pic_url'  => $banner[2],
                    'abstract' => $banner[3],
                );
            }   
        }
        $this->_setOutData('banner_list', $arrBanner);
    }*/
    /**
     * [_getHotInput description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    /*private function _getHotInput($arrInput){
        $arrMultiInput[] = array(
            'service_name' => 'wordlist',
            'method'       => 'queryWLItemDirectly',
            'input'        => array(
                'table_name' => self::WORDLIST_NAME,
                'key' => self::HOT_KEY,
            ),
            'ie' => 'gbk',
        );
        return $arrMultiInput;
    }*/
    /**
     * [_getHotOutput description]
     * @param  [type] $arrOutput [description]
     * @return [type]            [description]
     */
    /*private function _getHotOutput($arrOutput){

        $strOut = $arrOutput[0]['data'][self::HOT_KEY];
        $arrHot = array();
        if(isset($strOut)){
            $arrOut = unserialize($strOut);
            $arrHot = array(
                'title' => $arrOut[0],
                'thread_id' => $arrOut[1],
                'pic_url' => $arrOut[2],
                'topic_tag' => $arrOut[3],
            );
        }
        $this->_setOutData('today_topic', $arrHot);
    }*/
    /**
     * [_getTag description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    private function _getTag($arrInput){
        $arrTags = Lib_Def::$arrTagInfo;
        $arrRet = array();
        foreach($arrTags as $tag){
            $arrRet []= $tag;
        }
        $this->_setOutData('tag_list', $arrRet);
        return true;
    }
    private function _getBanner($arrInput){
        $objWordServer = Wordserver_Wordlist::factory();
        $arrInput = array(
            'table' => self::WORDLIST_NAME_BANNER,
            'start' => self::WORDLIST_START_BANNER,
            'stop' =>  self::WORDLIST_END_BANNER,
        );
        $ret = $objWordServer->getTableContents($arrInput);
        if(false == $ret || !isset($ret['err_no'])||  $ret['err_no'] !=0){
            Bingo_Log::warning('call getTableContents fail, input '.serialize($arrInput) . ' output: ' . serialize($ret));
            return false;
        }
        $arrBanner = array();

        foreach($ret['ret'] as $itemKey => $itemValue){
            $item = unserialize($itemValue);
            if(3 !== count($item)) {
                continue;
            }
            $temp = array(
                'id' => (int)$itemKey,
                'jump_url' => $item[0],
                'pic_url'   => $item[1],
                'abstract' => $item[2],
            );
            $arrBanner[]=$temp;
        }
        $this->_setOutData('banner_list', $arrBanner);
        return true;
    }
    private function _getTopic($arrInput){
        $objWordServer = Wordserver_Wordlist::factory();
        $arrInput = array(
            'table' => self::WORDLIST_NAME_TOPIC,
            'start' => self::WORDLIST_START_TOPIC,
            'stop' =>  self::WORDLIST_END_TOPIC,
        );
        $ret = $objWordServer->getTableContents($arrInput);
        if(false == $ret || !isset($ret['err_no'])||  $ret['err_no'] !=0){
            Bingo_Log::warning('call getTableContents fail, input '.serialize($arrInput) . ' output: ' . serialize($ret));
            return false;
        }
        $arrTopic = array();

        foreach($ret['ret'] as $itemKey => $itemValue){
            $item = unserialize($itemValue);
            if(4 !== count($item)) {
                continue;
            }
            $temp = array(
                'title' => $item[0],
                'thread_id' => $item[1],
                'pic_url'   => $item[2],
                'topic_tag' => $item[3],
            );
            $arrTopic[]=$temp;
        }
        $this->_setOutData('today_topic', $arrTopic[0]);
        return true;
    }
    
    /**
     * [_getSpec description]
     * @param
     * @return [type] [description]
     */
    private function _getSpec(){
        $intScreenWidth   = (int)$this->_getInput('scr_w', 320);
        $intScreenHeight  = (int)$this->_getInput('scr_h', 380);
        $intScreenDip     = (int)$this->_getInput('scr_dip', 2);
        $intQType         = (int)$this->_getInput('q_type', 2);

        $arrSpec = array(
            'screen_w'   => $intScreenWidth,
            'screen_h'   => $intScreenHeight,
            'screen_dip' => $intScreenDip,
            'q_type'     => $intQType,
        );
        return $arrSpec;
    }
    /**
     * [_setOutData description]
     * @param [type] $key  [description]
     * @param [type] $data [description]
     */
    private function _setOutData($key, $data){
        $this->_outData[$key] = $data;
    }
    /**
     * [_errRet description]
     * @param  [type] $errno [description]
     * @param  array  $data  [description]
     * @return [type]        [description]
     */
    private static function _errRet($errno, $data = array()){
        return array(
            'errno' => $errno,
            'errmsg' => Molib_Client_Error::getErrmsg($errno),
            'data'   => $data,
        );
    }
    /**
     *
     * @param
     * @return
     * */
    private static function _succRet($data = array()){

        return array(
            'errno'  => Tieba_Errcode::ERR_SUCCESS,
            'errmsg' => Molib_Client_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
            'data'   => $data,
        );
    }

}
?>
