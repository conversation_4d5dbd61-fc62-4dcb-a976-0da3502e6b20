<?php
class getPostListAction extends Molib_Client_BaseAction{

    private $intUserId = 0;
    private $_intCdnErrTime = 0;
    /**
     * @param
     * @return
     **/
    public function _getPrivateInfo(){
        return array(
            'check_login' => true,
            'need_login' => true,
            'pn' => intval($this->_getInput('pn', 0)),
            'rn' => intval($this->_getInput('rn', 0)),
            'tid' => intval($this->_getInput('tid', 0)),
        );
    }

    
    /**
     * @param
     * @return
     **/
    public function _checkPrivate(){
        return true;
    }


    /**
     * @param
     * @return
     **/
    public function _execute(){
        //$arrInput = $this->_getPrivateInfo();
        $arrPostList = $this->_getPostList($arrInput);
        $this->_stLog();

        if($arrPostList === false){
            //Bingo_Log::fatal("get post list err. input[".$arrInput."]");
            return false;
        }
        return true;
    }

    /**
     * @param
     * @return
     **/
    public function _getPostList($arrInput){
        $intPn = $this->_objRequest->getPrivateAttr('pn');
        $intRn = $this->_objRequest->getPrivateAttr('rn');
        $intThreadId = $this->_objRequest->getPrivateAttr('tid');
        $intUserId = intval($this->_objRequest->getCommonAttr('user_id'));

        // get user power in bzcommunity
        $arrInput = array(
            'user_id' => $intUserId, 
        );
        $arrOutput = Tieba_Service::call('bzcommunity', 'getUserPower', $arrInput, null, null, 'post', 'php', 'utf-8');
        if($arrOutput === false || !isset($arrOutput['errno'])){
            Bingo_Log::fatal(sprintf("get bzcommunity::getUserPower failed! input[%s]",serialize($arrMultiInput[$strategy]) ));
        }else if($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            $strMsg = sprintf('call bzcommunity::getUserPower failed in [%s] input:[%s] output:[%s]', __FUNCTION__, serialize($arrInput), serialize($arrOutput)); 
            Bingo_Log::warning($strMsg);
        }
        $arrRet['user_role'] = array(
            'can_del_thread' => isset($arrOutput['data']['can_delpost']) ? $arrOutput['data']['can_delpost'] : 0,
        );


        // get thread info
        $arrInput = array(
            'thread_id' => array(
                $intThreadId,
            ), 
            'is_del' => Lib_Def::IS_DEL_NO,
        );
        $arrOutput = Tieba_Service::call('bzcommunity', 'getThreadInfoByTids', $arrInput, null, null, 'post', 'php', 'utf-8');
        if($arrOutput === false || !isset($arrOutput['errno'])){
            Bingo_Log::fatal(sprintf("get bzcommunity::getThreadInfoByTids failed! input[%s]",serialize($arrMultiInput[$strategy]) ));
            $this->_error(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL), array(), false);
            return false;
        }else if($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(sprintf('call bzcommunity::getThreadInfoByTids failed in [%s] input:[%s] output:[%s]', __FUNCTION__, serialize($arrInput), serialize($arrOutput)) );
            $this->_error($arrOutput['errno'], Molib_Client_Error::getErrMsg($arrOutput['errno']), array(), false);
            return false;
        }

        foreach($arrOutput['data']['thread_list'] as $value){
            $temp = array(
                'user_info' => $value['user_info'],
                'tid' => $value['thread_id'],
                'title' => $value['title'],
                'reply_num' => $value['reply_num'],
                'last_time' => $value['last_time'],
                'tag_info' => $value['tag_info'],
                'voice_info' => $value['voice_info'],
                'media' => $value['media'],
                'location' => $value['location'],
                'abstract' => $value['abstract'],
            );
            $arrRet['thread_info'] = $temp;
        }


        // get post list
        $arrInput = array(
            'thread_id' => $intThreadId,
            'is_del' => Lib_Def::IS_DEL_NO,
            'rn' => $intRn,
            'pn' => $intPn,
        );
        $arrOutput = Tieba_Service::call('bzcommunity', 'getPostList', $arrInput, null, null, 'post', 'php', 'utf-8');
        if($arrOutput === false || !isset($arrOutput['errno'])){
            Bingo_Log::fatal(sprintf("get bzcommunity::getPostList failed! input[%s]",serialize($arrMultiInput[$strategy]) ));
            $this->_error(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL), array(), false);
            return false;
        }else if($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(sprintf('call bzcommunity::getPostList failed in [%s] input:[%s] output:[%s]', __FUNCTION__, serialize($arrInput), serialize($arrOutput)) );
            $this->_error($arrOutput['errno'], Molib_Client_Error::getErrMsg($arrOutput['errno']), array(), false);
            return false;
        }


        // join the variables
        foreach($arrOutput['data']['post_list'] as $value){
            $temp = array(
                'pid' => $value['posts_id'],
                'tid' => $value['thread_id'],
                'create_time' => $value['create_time'],
                'content' => Lib_Media::buildPbMedia($value['content'],$this->_getSpec()),
                'user_info' => $value['user_info'],
                'floor_num' => $value['floor_no'],
                'sub_post_num' => $value['sub_post_num'],
                //'comment_list' => $value['comment_list'],
            );
            foreach($value['comment_list'] as $key => $comment){
                $temp['comment_list'][] = $comment;
            }
            //ksort($temp['comment_list']);
            if($temp['pid'] == $temp['tid']){
                $temp['floor_num'] = 1;
            }
            $arrPosts[] = $temp;
        }
        $arrRet['post_list'] = $arrPosts;
        //uasort($arrRet['post_list'], "_rankByFloor");
        $arrRet['has_more'] = $arrOutput['data']['has_more'];

        $this->_objResponse->setOutData($arrRet);
        return true;
    }

    private function _rankByFloor($a, $b){
        if($a == $b){
            return 0;
        }
        return ($a['floor_num'] < $b['floor_num']) ? -1 : 1;
    }

    /**
     * [_stLog description]
     * @param
     * @return [type] [description]
     */
    private function _stLog(){
        Tieba_Stlog::addNode('obj_id', $this->_objRequest->getPrivateAttr('tid'));
    }
    /**
     * [_getSpec description]
     * @param  [type] $arrContents [description]
     * @return [type]              [description]
     */
    private function _getSpec($arrContents){
        $intScreenWidth   = (int)$this->_getInput('scr_w', 320);
        $intScreenHeight  = (int)$this->_getInput('scr_h', 380);
        $intScreenDip     = (int)$this->_getInput('scr_dip', 2);
        $intQType         = (int)$this->_getInput('q_type', 2);

        $arrSpec = array(
            'screen_w'   => $intScreenWidth,
            'screen_h'   => $intScreenHeight,
            'screen_dip' => $intScreenDip,
            'q_type'     => $intQType,
        );
        return $arrSpec;
        
    }
}
?>
