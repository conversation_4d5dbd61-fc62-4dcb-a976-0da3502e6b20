<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2016-07-14 11:01:58
 * @version
 */

 /*
    注意这里涉及到rd与fe的模块名，如果提前已经协商好统一模块名例如都叫 bzcommunity，那么这里就不要传第二个参数，默认即可：
        Tieba_Init::init("bzcommunity");
    但如果没协商，比如rd的模块名叫 bzcommunity，fe的模块名叫 bzcommunity_fe，那么这里就应当是（fe这个模块名用于 ROOT_PATH/template/ 下的文件夹名）
        Tieba_Init::init("bzcommunity","bzcommunity_fe");
    同理，也可以自定义omp模块名，默认同样使用 bzcommunity：
        Tieba_Init::init("bzcommunity",null,"bzcommunity_omp");
  */

Tieba_Init::init("bzcommunity");

$objFrontController = Bingo_Controller_Front::getInstance(array(
    "actionDir" => MODULE_ACTION_PATH,
    "defaultRouter" => "index",
    "notFoundRouter" => "error",
    "beginRouterIndex" => 1,
));

Bingo_Timer::start('total');
Bingo_Page::init(array(
    "baseDir" => MODULE_VIEW_PATH,
    "debug" => false,
    "outputType" => ".",
    "isXssSafe" => true,
    "module" => "bzcommunity",
    "useTbView" => true,
    "viewRootpath" => MODULE_VIEW_PATH . "/../",
    //"catchPath" => "../../data/app/bzcommunity",
));

// StLog
$strRouter = Bingo_Http_Request::getStrHttpRouter();
$urlkey = 'bzcommunity-'.str_replace('/', '-', $strRouter);
Tieba_Stlog::addNode('urlkey', $urlkey);



Tieba_Stlog::setFileName('wap');
Tieba_Stlog::addNode('pro', 'client');
Tieba_Stlog::addNode('mid', 'api');
Tieba_Stlog::addNode('url', Bingo_Http_Request::getServer('REQUEST_URI'));
Tieba_Stlog::addNode('refer', Bingo_Http_Request::getServer('HTTP_REFERER'));
// Tieba_Stlog::addNode('ispv',1);
Tieba_Stlog::addNode('errno',0);
Tieba_Stlog::addNode('logid',Bingo_Log::getLogId());

Tieba_Stlog::addNode('php_start_time', $php_start_time);
if(isset($_SERVER['CGI_START_TIME'])) {
	$cgi_start_time = $_SERVER['CGI_START_TIME'];
	Tieba_Stlog::addNode('cgi_start_time', $cgi_start_time);
	Tieba_Stlog::addNode('cgi_wait_time', $php_start_time - $cgi_start_time);
}



try{
    $objFrontController->dispatch();
}catch(Exception $e){
    //出错处理，直接转向到错误页面
    Bingo_Log::warning(sprintf('main process failure!HttpRouter=%s,error[%s]file[%s]line[%s]',
        Bingo_Http_Request::getStrHttpRouter(), $e->getMessage(), $e->getFile(), $e->getLine()));
    Bingo_Page::setErrno($e->getCode());
    header('location:http://static.tieba.baidu.com/tb/error.html');
}

Bingo_Timer::start('build_page');
Bingo_Page::buildPage();
Bingo_Timer::end('build_page');
Bingo_Timer::end('total');

$strTimeLog = Bingo_Timer::toString();
$strTimeLogLocal = '';
if(!empty($strTimeLog)) {
	$strTimeLogLocal = ' time[' . $strTimeLog . ']';
}

$arrLogData = Tieba_Stlog::getLogData();
$intErrno = isset($arrLogData['errno']) ? intval($arrLogData['errno']) : 0;
Bingo_Log::notice('stat'.Tieba_Stlog::notice().''.$strTimeLogLocal, '', '', 0, 0, array(), $intErrno);

?>
