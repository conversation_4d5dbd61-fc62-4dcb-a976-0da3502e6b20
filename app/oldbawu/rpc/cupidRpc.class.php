<?php
/**
 * cupid��RPC�ӿ�
 * <AUTHOR>
 * @since 2013-03-08
 * @package rpc
 */
require_once 'Rpc/Cupid.php';
        
class cupidRpc
{   
   /**
    * ����ǰ׺С����
    * @param 
    */
    public static function getPostPrefix($strFirst, $strSecond) {
	    $_bolPostPre = (bool)Rpc_Cupid::getPostPrefix($strFirst, $strSecond);
	    return $_bolPostPre;
	}
	public static function getPostPrefixForum($strFname, $intFid) {
		$_bolPostPre = (bool)Rpc_Cupid::getPostPrefixForum($strFname, $intFid);
		return $_bolPostPre;
	}
    	public static function getNewBawu($strFname, $intFid) {
		$_bolNewBawu = (bool)Rpc_Cupid::getNewBawu($strFname, $intFid);
		return $_bolNewBawu;
	}

}
