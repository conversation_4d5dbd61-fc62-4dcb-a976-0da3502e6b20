<?php
/**
 * POSTCM���񽻻���IDL�ӿ�
 * <AUTHOR>
 * @since 2009-08-27
 * @package forum-manager
 *
 */

function postcm_array2mcpack($arr)
{
	if ( isset($arr['detail']) && !empty($arr['detail']) )
	{
		$arr['detail_len'] = strlen($arr['detail'])+1;
		$_detail = $arr['detail'];
		unset($arr['detail']);
		$arr['(raw)rawdata'] = $_detail . "\0";
	}

	return $arr;
}

function dummy_mcpack2array($pack)
{
	// In RAL2 It is already an array
	return $pack;
}


class postcmRpc
{
    /**
     * POSTCM ����
     *
     * @param ����� $cm
     * @param ���� $word
     * @param IP��ַ $ip
     * @param cookie $cookie
     * @param �����ֶ� $other
     * @return ��������/�߳�fale
     */
    public static function call($cm,$word,$ip,$cookie,$other=array())
    {
        $other['command_no'] = $cm;
        $other['word'] = $word;
        $other['ip'] = $ip;
        $other['cookie'] = $cookie;
        return self::_call($other);
    }
    
    public static function baseCall($input)
    {
    	return self::_call($input);
 
    	
    	$uid = $input['uid'];
    	$cm = $input['command_no'];
    	
    	switch($cm){
    		case cmCommandNo::FilterForumUser:
    			$fid = $input['forum_id'];
    			$day_num = $input['day_num'];
    			$username = $input['op_uname'];
    			$opuid = $input['op_uid'];
    			$op_time = $input['op_time'];
    			$output = PermServer::FilterForumUser($fid, $uid, $username, $op_time, $opuid, $day_num, TRUE);
    			break;
    		case cmCommandNo::FilterForumIP:
    			$fid = $input['forum_id'];
    			$day_num = $input['day_num'];
    			$op_time = $input['op_time'];
    			$opuid = $input['op_uid'];
    			$username = $input['op_uname'];
    			$op_time = $input['op_time'];
    			$ip_type = $input['ip_type'];
    			$output = PermServer::FilterForumIP($fid, $uid, $username, $op_time, $opuid, $day_num, $ip_type, TRUE);
    			break;
    		case cmCommandNo::CancelFilterForumUser:
    			$fid = $input['forum_id'];
    			$opuid = $input['op_uid'];
    			$output = PermServer::CancelFilterForumUser($fid, $uid, $opuid, TRUE);
    			break;
    		case cmCommandNo::CancelFilterForumIP:
    			$fid = $input['forum_id'];
    			$opuid = $input['op_uid'];
    			$output = PermServer::CancelFilterForumIP($fid, $uid, $opuid, TRUE);
    			break;
    		case cmCommandNo::CancelFilterUser:
    			$username = $input['username'];
			    $opuid = PassportUserInfo::getUidByUname($username);
			    $output = PermServer::CancelFilterUser($uid, $opuid, TRUE);
    			break;
    		default:
    			$fid = $input['fid'];
			    $username = $input['username'];
			    $opuid = PassportUserInfo::getUidByUname($username);
			    $roleid = $input['role_id'];
			    $output = PermServer::setUserRole($fid, $uid, $roleid, $username, $opuid, $cm, TRUE);
    			break;
    	}
    	
    	$arrErr = PermServer::getLastError;
    	
    	if ( !isset($output) || empty($output) )
        {
            Rpc::putLog('warning','RpcCall Postcm Failure!input:'.print_r($input,TRUE),
                        __FILE__,__LINE__,NULL,0,NULL);
            return FALSE;
        }
        
    	if ( !isset($arrErr['errorNum']) || 0 === intval($arrErr['errorNum']) )
        {
            //succ
        }
        else 
        {
            //failure
            Rpc::putLog('warning',
                sprintf('postcmRpc:post manager fail with error_no[%d]: [%s]',$arrErr['errorNum'], $arrErr['errorText']),
                __FILE__,__LINE__);
        }
    	//parse reserve int[16]
        if (isset($output['reserve']))
        {
            $output['reserve'] = unpack('I16',$output['reserve']);
        }
        //parse term_buffer,char ,need check!
        if (isset($output['term_buffer']))
        {
            $output['term_buffer'] = bin2hex($output['term_buffer']);
        }
        //parse res_brws_term, struct post_postcm_data_t
        //todo
        if (isset($output['res_brws_term']))
        {
            //todo
        }
    	return $output;
    	
        // return self::_call($input);
    }
    /**
     * ��POSTCM�������Է��ص��ֶν����˽ṹ�嵽�����ת������������ʧ���򷵻�FALSE
     *
     * @param ������������ $input
     * @return �������ݣ�����|False��
     */
    private static function _call($input)
    {
    	if (is_array($input['perm_flag'])){
    		$input['perm_flag'] = PermServer::perm2decbin($input['perm_flag']);
    	}
        if (!isset($input['magic'])) $input['magic'] = rpcUtil::getMagicNum();
        Rpc::rpcCall('postcm','query',$input,$output);
        //check $output
        if ( !isset($output) || empty($output) )
        {
            Rpc::putLog('warning','RpcCall Postcm Failure!input:'.print_r($input,TRUE),
                        __FILE__,__LINE__,NULL,0,NULL);
            return FALSE;
        }
        //check err_no
        if ( isset($output['err_no']) && 0 === intval($output['err_no']) )
        {
            //succ
        }
        else 
        {
            //failure
            Rpc::putLog('warning',
                sprintf('postcmRpc:post manager fail with error_no[%d]',$output['err_no']),
                __FILE__,__LINE__);
        }
        //parse reserve int[16]
        if (isset($output['reserve']))
        {
            $output['reserve'] = unpack('I16',$output['reserve']);
        }
        //parse term_buffer,char ,need check!
        if (isset($output['term_buffer']))
        {
            $output['term_buffer'] = bin2hex($output['term_buffer']);
        }
        //parse res_brws_term, struct post_postcm_data_t
        //todo
        if (isset($output['res_brws_term']))
        {
            //todo
        }
        return $output;
    }
}
