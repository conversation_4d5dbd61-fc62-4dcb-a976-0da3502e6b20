<?php

class fameHallRpc {
    const SERVICE_NAME = 'fameHallDal';
    const SERVICE_METHOD = 'dalCall';
    
    const FAMEHALLPAGESIZE = 9;
    const MAXPAGESIZE = 300;
    
    public static function call($arrInput){
        Bingo_Log::debug("rpc call ".self::SERVICE_NAME." [input:".print_r($arrInput, true)."]", LOG_TYPE_RPC);
        Bingo_Timer::start($arrInput['cmd']);
        $ret = Rpc::rpcCall(self::SERVICE_NAME, self::SERVICE_METHOD, $arrInput, $out);
        Bingo_Timer::end($arrInput['cmd']);
        if ($ret === true){
            return $out['res'];
        }else{
            Bingo_Log::warning("rpc call ".self::SERVICE_NAME." fail [input:".print_r($arrInput, true)."]", LOG_TYPE_RPC);
            return false;
        }
    }
    
    /**
     * get user's user list
     *
     * @param int|string $varForumID
     * @param int|string $varStart
     * @param int|string $varLimit
     * @return unknown
     */
    public static function getUserFameHall($varForumID,$varStart = 0,$varLimit = self::FAMEHALLPAGESIZE) {
    	$arrInput = array(
    		'cmd' => 'get_user',
    		'forum_id' => $varForumID,
    		'offset' => $varStart,
    		'pagesize' => $varLimit,
    	);
    	return self::call($arrInput);    	
    }
    /**
	 * function for get one famous user 
	 *
	 * @param int $varForumID
	 * @param int $varUserID
	 */
    public static function getTheUserFameHall($varForumID,$varUserID)
    {
    	$arrInput = array(
    		'cmd' => 'get_user_detail',
    		'forum_id' => $varForumID,
    		'member_id' => $varUserID,
    	);
    	return self::call($arrInput);  
    }
    
    /**
     * add user into DB
     *
     * @param int|string $varForumID
     * @param int|string $varUserID
     * @param int|string $varPoints
     * @param  string $varMemberInfo
     * @param  string $varMemberAchi
     * @return unknown
     */
    public static function addUserFameHall($varForumID,$varUserID,$varPoints = 0,$varMemberInfo = '',$varMemberAchi = '',$varMemberBoutique = '',$varBatch = 0 ) {
    	$arrInput = array(
    		'cmd' => 'add_user',
    		'forum_id' => $varForumID,
    		'member_id' => $varUserID,
    		'points' => $varPoints,
    		'member_info' => $varMemberInfo,
    		'member_achi' => $varMemberAchi,
    		'member_boutique' => $varMemberBoutique,
    		'data' => $varBatch,
    	);
    	return self::call($arrInput);    	
    }
    
    /**
     * update user into DB
     *
     * @param int|string $varForumID
     * @param int|string $varUserID
     * @param int|string $varPoints
     * @param  string $varMemberInfo
     * @param  string $varMemberAchi
     * @return unknown
     */
    public static function setUserFameHall($varForumID,$varUserID,$varPoints = 0,$varMemberInfo = '',$varMemberAchi = '',$varMemberBoutique = '',$varBatch = 0 ) {
    	$arrInput = array(
    		'cmd' => 'set_user',
    		'forum_id' => $varForumID,
    		'member_id' => $varUserID,
    		'points' => $varPoints,
    		'member_info' => $varMemberInfo,
    		'member_achi' => $varMemberAchi,
    		'member_boutique' => $varMemberBoutique,
    		'data' => $varBatch,
    	);
    	return self::call($arrInput);    	
    }
    
    /**
     * delete user from DB
     *
     * @param int|string $varForumID
     * @param int|string $varUserID
     * @return unknown
     */
    public static function delUserFameHall($varForumID,$varUserID) {
    	$arrInput = array(
    		'cmd' => 'del_user',
    		'forum_id' => $varForumID,
    		'member_id' => $varUserID,
    	);
    	return self::call($arrInput);    	
    }
    
    /**
     * get user's user list
     *
     * @param int|string $varForumID
     * @param int|string $varStart
     * @param int|string $varLimit
     * @return unknown
     */
    public static function getRecommandUser($varForumID,$varStart = 0,$varLimit = self::FAMEHALLPAGESIZE,$varStatus=0) {
    	$arrInput = array(
    		'cmd' => 'get_recommand_user',
    		'forum_id' => $varForumID,
    		'offset' => $varStart,
    		'pagesize' => $varLimit,
    		'status' => $varStatus,
    	);
    	return self::call($arrInput);    	
    }
    
    /**
	 * function for get one famous user 
	 *
	 * @param int $varForumID
	 * @param int $varUserID
	 */
    public static function getTheRecommandUser($varForumID,$varUserID,$varStatus=0)
    {
    	$arrInput = array(
    		'cmd' => 'get_recommand_user_detail',
    		'forum_id' => $varForumID,
    		'member_id' => $varUserID,
    		'status' => $varStatus,
    	);
    	return self::call($arrInput);  
    }
    
    /**
     * add user into DB
     *
     * @param int|string $varForumID
     * @param int|string $varUserID
     * @param int|string $varPoints
     * @param  string $varMemberInfo
     * @param  string $varMemberAchi
     * @return unknown
     */
    public static function addRecommandUser($varForumID,$varUserID,$varStatus=0,$varMemberInfo = '',$varMemberAchi = '',$varMemberBoutique = '',$varBatch = 0,$varVote = 0 , $varRecommand = 0) {
    	$arrInput = array(
    		'cmd' => 'add_recommand_user',
    		'forum_id' => $varForumID,
    		'member_id' => $varUserID,
    		'status' => $varStatus,
    		'member_info' => $varMemberInfo,
    		'member_achi' => $varMemberAchi,
    		'member_boutique' => $varMemberBoutique,
    		'data' => $varBatch,
    		'vote' => $varVote,
    		'recommand' => $varRecommand,
    	);
    	return self::call($arrInput);    	
    }
    
    /**
     * update user into DB
     *
     * @param int|string $varForumID
     * @param int|string $varUserID
     * @param int|string $varPoints
     * @param  string $varMemberInfo
     * @param  string $varMemberAchi
     * @return unknown
     */
    public static function setRecommandUser($varForumID,$varUserID,$varStatus=0,$varMemberInfo = '',$varMemberAchi = '',$varMemberBoutique = '',$varBatch = 0 ,$varVote = 0 , $varRecommand = 0) {
    	$arrInput = array(
    		'cmd' => 'set_recommand_user',
    		'forum_id' => $varForumID,
    		'member_id' => $varUserID,
    		'status' => $varStatus,
    		'member_info' => $varMemberInfo,
    		'member_achi' => $varMemberAchi,
    		'member_boutique' => $varMemberBoutique,
    		'data' => $varBatch,
    		'vote' => $varVote,
    		'recommand' => $varRecommand,
    	);
    	return self::call($arrInput);    	
    }
    
    /**
     * delete user from DB
     *
     * @param int|string $varForumID
     * @param int|string $varUserID
     * @return unknown
     */
    public static function delRecommandUser($varForumID,$varUserID) {
    	$arrInput = array(
    		'cmd' => 'del_recommand_user',
    		'forum_id' => $varForumID,
    		'member_id' => $varUserID,
    	);
    	return self::call($arrInput);    	
    }
    
    /**
     * get forum list for cache.
     *
     * @return unknown
     */
    public static function getForumList()
    {
    	$arrInput = array(
    		'cmd' => 'get_forum_list',
    	);
    	return self::call($arrInput);  
    }
    
    
    /**
     * open Status
     *
     * @return unknown
     */
    public static function openStatus()
    {
    	$arrInput = array(
    		'cmd' => 'set_status',
    	);
    	return self::call($arrInput);  
    }
    
    
    /**
     * close Status
     *
     * @return unknown
     */
    public static function closeStatus()
    {
    	$arrInput = array(
    		'cmd' => 'get_forum_list',
    	);
    	return self::call($arrInput);  
    }
    
    
    /**
     * get  Status
     *
     * @return unknown
     */
    public static function getStatus()
    {
    	$arrInput = array(
    		'cmd' => 'get_status',
    	);
    	return self::call($arrInput);  
    }
    
    
}