<?php

class photoRpc {
    const SERVICE_NAME         = 'photo_http';
    const PHOTO_FORUM_INFO_URL = '/photo/bw/forum/base';
    
    
    public static function call($arrInput,$url){
        Bingo_Timer::start($arrInput['photo']);
        $ret = Rpc::rpcCall(self::SERVICE_NAME, $url, $arrInput, $out);
        Bingo_Timer::end($arrInput['photo']);
        
        if ($ret === true && $out !== ''){
            $res = Bingo_String::json2array($out);
            if($res !== false ){   
                return $res;
            }
        }
        
        Rpc::putLog('warning',"rpc call ".self::SERVICE_NAME." fail [input:".print_r($arrInput, true)."]".serialize($out) ,__FILE__,__LINE__,NULL,0,NULL);
        return false;
    }
    
    /**
     * get forum info
     *
     * @param $forum_name ����
     * @return array
     */
    public static function getForumPhotoInfo($forum_name) {
        $output = array();
        
    	$arrInput = array(
    		'kw' => $forum_name,
    	);
    	$url = self::PHOTO_FORUM_INFO_URL."?kw=".trim($forum_name);
    	$res = self::call($arrInput,$url);    	
    	if($res === false || !isset($res['no']) || !isset($res['data'])|| $res['no'] != 0 ){
    	   Rpc::putLog('warning',"rpc call ".self::SERVICE_NAME." return fail !".serialize($res) ,__FILE__,__LINE__,NULL,0,NULL);
    	   return false;
    	}
    	return $res['data'];
    }
    
}