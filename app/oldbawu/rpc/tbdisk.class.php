<?php
/**
 * ����rpc�����api
 *
 */
class tbdiskRpc
{
	//�洢rpc����ľ�̬����
	protected static $_objRpc = null;
	
	//���tbdisk��rpc����
	public static function getRpc()
	{
		if (is_null(self::$_objRpc)) {
			require_once 'Bd/Rpc/Camel.php';
			self::$_objRpc = new Bd_Rpc_Camel('tbdisk');	
		}
		return self::$_objRpc;
	}
	
	//���postcm��rpc����
	public static function getPostCmRpc()
	{
			if (is_null(self::$_objRpc)) {
					require_once 'Bd/Rpc/Camel.php';
					self::$_objRpc = new Bd_Rpc_Camel('postcm');
			}
			return self::$_objRpc;
	}

	//���clubcm��rpc����
	public static function getClubCmRpc()
	{
			if (is_null(self::$_objRpc)) {
					require_once 'Bd/Rpc/Camel.php';
					self::$_objRpc = new Bd_Rpc_Camel('clubcm');
			}
			return self::$_objRpc;
	}

	//���ݰ�id��ȡ��������
	public static function getForumConfig($intFid)
	{
		if(!is_integer($intFid)){
			return false;
		}
		//���촫�����
		$arrInput = array(
			'header' => array(
				'content_type' => 'Bingo-Rpc',
            	'connection' => 0,
        	),
        	'content' => array(
            	array(
                	'method' => 'get_disk_forum_config',
                	'params' => array(
                        'forum_id' => $intFid,
                	),
                	'id' => 0,
            	),
        	),
		);
		//��ȡrpc����
		$objRpc = self::getRpc();
		$arrOutput = $objRpc->call($arrInput);
		if (! empty($arrOutput) && isset($arrOutput['content'][0]['result_params']['output']) ) {
			return $arrOutput['content'][0]['result_params']['output'];
		}
		Rpc::putLog('warning','tbdiskRpc::getForumConfig  Call Failure!input:'.print_r($arrOutput,TRUE),__FILE__,__LINE__,NULL,0,NULL);
		return false;
	}

	//ɾ������
	public static function delc($fid,$cid)
	{
		if(!is_int($fid))
		{
			return false;
		}
		if(!is_int($fid))
		{
			return false;
		}
		//��ȡ��id
		$fid=intval($fid);
		//��ȡ����id
		$cid=intval($cid);

		$arrInput['command_no'] = 3603;
		$arrInput['forum_id'] = $fid;
		$arrInput['disk_classify_id'] = $cid;
		
		$objRpc = self::getPostCmRpc();
		$arrOutput = $objRpc->call($arrInput);
		return true;
	}
	
    	//���÷���
	public static function setc($fid,$cid,$tid)
	{
		if(!is_int($fid)){
			return false;
		}
		if(!is_int($fid)){
			return false;
		}
		if(!is_int($tid)){
			return false;
		}
		
		$fid=intval($fid);
		$cid=intval($cid);
		$tid=intval($tid);
		
		$arrInput = array(
			'header' => array(
				'content_type' => 'Bingo-Rpc',
            	'connection' => 0,
        	),
        	'content' => array(
            	array(
                	'method' => 'DiskUpdateClassify',
                	'params' => array(
                        'forum_id' => $intFid,
                	),
                	'id' => 0,
            	),
        	),
		);
		$objRpc = self::getRpc();
		$arrOutput = $objRpc->call($arrInput);
		if (! empty($arrOutput) && isset($arrOutput['content'][0]['result_params']['output']) ) {
			return $arrOutput['content'][0]['result_params']['output'];
		}
		trigger_error('talk with tbdisk error!getForumConfig failure!', E_USER_WARNING);
		return false;
	}
	
    
	
  //��������
	public static function createc($fid,$cname)
	{
		if(!is_int($fid)){
			return false;
		}
		if(!is_string($cname)){
			return false;
		}
		
		$fid=intval($fid);
		$cname=strval($cname);
		
		$arrInput = array();
                $arrInput['command_no'] = 3602;
                $arrInput['forum_id'] = $fid;
		$arrInput['disk_classify_id']=self::getClassifyId();
                $arrInput['disk_classify_name'] = $cname;

		$objRpc = self::getPostCmRpc();
		$arrOutput = $objRpc->call($arrInput);
		return true;
	}
	
    	//���·�����
	public static function renamec($fid,$cid,$cname)
	{
		if(!is_int($fid))
		{
			return false;
		}
		if(!is_int($cid))
		{
			return false;
		}
		if(!is_string($cname))
		{
			return false;
		}
		
		$fid=intval($fid);
		$cid=intval($cid);
		$cname=strval($cname);
		
		$arrInput = array();
        	$arrInput['command_no'] = 3605;
        	$arrInput['forum_id'] = $fid;
        	$arrInput['disk_classify_id'] =$cid;
        	$arrInput['disk_classify_name'] = $cname;
        
		$objRpc = self::getPostCmRpc();
		$arrOutput = $objRpc->call($arrInput);
		return true;
	}
	
	
	public static function getClassifyId()
	{
        	$arrIdType = array();
		$arrIdType[0] = 'disk_file_id';

		$arrInParam = array();
		$arrInParam['id_type'] = $arrIdType;
		$arrInParam['command_no'] = 25000;//conf_Command::classify_alloc_id;

		$arrCmOutput = self::getClubCmRpc()->call($arrInParam);
		if ($arrCmOutput === false) {
			return false;
		}

		$errno = $arrCmOutput['error_no'];
		if($errno != 0 || empty($arrCmOutput['id'])){
			return false;
		}

		return $arrCmOutput['id'][0];
	}
	
	public static function getClassify($intFid)
	{
		if(!is_int($intFid))
		{
			return false;
		}
		
		$arrInput = array(
			'header' => array(
				'content_type' => 'Bingo-Rpc',
            	'connection' => 0,
        	),
        	'content' => array(
            	array(
                	'method' => 'get_disk_classify_list',
                	'params' => array(
		            	'forum_id' 		=> $intFid,
                	),
                	'id' => 0,
            	),
        	),
		);
		$objRpc = self::getRpc();
		$arrOutput = $objRpc->call($arrInput);
		if (! empty($arrOutput) && isset($arrOutput['content'][0]['result_params']['output']) ) {
			return $arrOutput['content'][0]['result_params']['output'];
		}
		return false;
	}


	 public static function bawu_get_disk_resources_list($forum_id,$is_process,$page_cur,$page_num,$is_delete)
        {

                $arrInput = array(
                        'header' => array(
                                'content_type' => 'Bingo-Rpc',
                'connection' => 0,
                ),
                'content' => array(
                array(
                        'method' => 'bawu_get_disk_resources_list',
                        'params' => array(
				'forum_id' => $forum_id,
                                'is_process' => $is_process,
                                'page_cur' => $page_cur,
                                'page_num' => $page_num,
                                'is_delete' => $is_delete,
                        ),
                        'id' => 0,
                ),
                ),
                ); 
                $objRpc = self::getRpc();
                $arrOutput = $objRpc->call($arrInput);
		
                if (! empty($arrOutput) && isset($arrOutput['content'][0]['result_params']) ) {
                        return $arrOutput['content'][0]['result_params'];
                }
                return false;
        }

        public static  function sec2time($sec)
	{  
		$sec = round($sec/60);  
		if ($sec >= 60){  
			$hour = floor($sec/60);  
			$min = $sec%60;  
			$res = $hour.' Сʱ ';  
			$min != 0  &&  $res .= $min.' ��';  
		}else{  
			$res = $sec.' ����'; 
		}  
		return $res;  
	}
	// ��ȡ��������Ϣ
	public static function get_disk_thread ($intFid, $intTid) {
		$intFid = intval ($intFid);
		$intTid = intval ($intTid);
		if ($intFid<=0 || $intTid<=0) {
			return false;
		}
		$arrInput = array(
			'header' => array(
				'content_type'	=> 'Bingo-Rpc',
				'connection'	=> 0,
			),
			'content' => array(
				array(
					'method' => 'get_disk_thread',
					'params' => array(
						'forum_id'		=> $intFid,
						'thread_id'		=> $intTid,
					),
					'id' => 0,
				),
			),
		);
		$objRpc = self::getRpc();
		$arrOutput = $objRpc->call($arrInput);
		if (! empty($arrOutput) && isset($arrOutput['content'][0]['result_params']) ) {
			return $arrOutput['content'][0]['result_params'];
		}
		return false;
	}
}
/* vim: set noexpandtab ts=4 sw=4 sts=4 tw=100: */
