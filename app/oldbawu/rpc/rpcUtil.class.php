<?php
/**
 * RPC�ӿ���ʹ�õ���һЩͨ�ú���
 * <AUTHOR>
 * @since 2009-08-27
 * @package forum-manager
 *
 */
class rpcUtil
{    
    public static $magicNum = 12345;
    
    public static function getMagicNum()
    {
        return self::$magicNum;
    }
    
    public static function setMagicNum($_magicNum)
    {
        self::$magicNum = $_magicNum;
    }
    
    public static function getIp()
    {
        if (method_exists('Phpbean_Util','getIp'))
        {
            return Phpbean_Util::getIp();
        }
        else 
        {
            if (isset($_SERVER["HTTP_X_FORWARDED_FOR"])) 
            {
                $ip = $_SERVER["HTTP_X_FORWARDED_FOR"];
            }
            elseif (isset($_SERVER["HTTP_CLIENT_IP"]))
            {
                $ip = $_SERVER["HTTP_CLIENT_IP"];
            }
            elseif (isset($_SERVER["REMOTE_ADDR"])) 
            {
                $ip = $_SERVER["REMOTE_ADDR"];
            }
            else 
            {
                $ip = "0.0.0.0";
            }
            $ip = strip_tags(trim($ip));
            return $ip;
        }
    }
}