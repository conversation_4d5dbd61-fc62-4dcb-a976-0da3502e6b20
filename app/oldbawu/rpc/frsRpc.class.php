<?php
/**
 * FRS�����ӿ�
 * <AUTHOR>
 * @package forum-manager
 *
 */
class frsRpc
{
    
    const MAX_GOOD_CLASS_NUM = 8;
    
    public static function canAddGoodClassName($fid,$name)
    {
        return TRUE;
        /**
        $input = array(
            'post_category'=>31,
            'forum_id'=>$fid,
        	'forum_name'=>$name,
            'page_num'=>0,
            'req_num'=>0,
            'goodclass_id'=>0,
        );
        $output = self::_StructCall($input);
        if ( isset($output) && ($output['status']==0) )
        {
            return TRUE;
        }
        return FALSE;
        */
    }
    
    public static function canRenameGoodClassName($fid,$name,$nameId)
    {
        return TRUE;
        /**
        $input = array(
            'post_category'=>32,
            'forum_id'=>$fid,
        	'forum_name'=>$name,
            'page_num'=>0,
            'req_num'=>0,
            'goodclass_id'=>$nameId,
        );
        $output = self::_StructCall($input);
        if ( isset($output) && ($output['status']==0) )
        {
            return TRUE;
        }
        return FALSE;
       */
    }

    public static function getGoodClass($fname, $fid)
    {
        $input = array(
            'forum_name' => $fname,
            'forum_id' => $fid,
            'need_abstract' => 0,
            'goodclass_id' => 1,
            'offset' => 0,
            'res_num' => 10,
            'need_photo_pic' => 0,
            'icon_size' => 0,
        );
        $output = self::_StructCall2($input);
        if ($output && isset($output['output']['forum_info']['goodclass_show'])) {
            $goods = $output['output']['forum_info']['goodclass_show'];
            $classGoods = array();
            if (!empty($goods)) {
                array_shift($goods);
                foreach ($goods as $item => $good) {
                    $classGood = array();
                    foreach ($good as $key => $value) {
                        $classGood['class_' . $key] = $value;
                    }
                    $classGoods[$item] = $classGood;
                }
            }
            return $classGoods;
        }
        return FALSE;;
    }
    
    private static function _StructCall($input)
    {
        $input['ip'] = rpcUtil::getIp();
        if (!isset($input['magic'])) $input['magic'] = rpcUtil::getMagicNum();
        $bolRes = Rpc :: rpcCall ('frs','getThread',$input,$output);
        if ( !$bolRes || !isset($output) || empty($output) )
        {
            Rpc::putLog('warning','frsRpc Call Failure!input:'.print_r($input,TRUE),__FILE__,__LINE__,NULL,0,NULL);
            return FALSE;
        }
        return $output;
    }

    private static function _StructCall2($input)
    {
        $input['ip'] = rpcUtil::getIp();
        if (!isset($input['magic'])) $input['magic'] = rpcUtil::getMagicNum();
        $res = Tieba_Service::call('post', 'getGoodThreads', $input);
        if (Tieba_Errcode::ERR_SUCCESS !== intval($$res['errno']) || empty($res)) {
            Rpc::putLog('warning', 'frsRpc Call Failure!input:' . print_r($input, TRUE), __FILE__, __LINE__, NULL, 0, NULL);
            return FALSE;
        }
        return $res;
    }
}