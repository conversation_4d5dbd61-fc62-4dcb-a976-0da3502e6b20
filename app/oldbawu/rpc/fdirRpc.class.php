<?php
/**
 * fdir��RPC�ӿ�
 * <AUTHOR>
 * @since 2009-09-03
 * @package rpc
 */
class fdirRpc
{    
    const CMD_DIR_BWS = 1101;
    const CMD_FINFO_BWS = 1102;
    const CMD_TAG_SEARCH = 1103;
    const CMD_FNAME_SEARCH = 1104;
    const CMD_NAVIGATE_BWS = 1105;
    const CMD_ALL_DIR_BWS = 1106;
    /**
     * sizeof(forum_stat_info)
     *
     */
    const SIZE_FORUM_STAT_INFO = 808;
    
    const SIZE_FORUM_DIR_2_INFO = 44;
    
    const SIZE_FORUM_DIR_NAME = 40;
    
    /**
     * typedef struct _forum_dir_name
{
    int fid;
    char level_1_name[FDIR_MAX_FORUM_DIR_SIZE];  //һ��Ŀ¼��
    char level_2_name[FDIR_MAX_FORUM_DIR_SIZE];  //����Ŀ¼��
} forum_dir_name;
FDIR_MAX_FORUM_DIR_SIZE = 40
     *
     * @param unknown_type $fid
     * @param unknown_type $dir1
     * @param unknown_type $dir2
     */
    public static function packForumDirName($fid,$dir1,$dir2)
    {
        $rs = '';
        
        $boolRs = pack('I',$fid);
        if (!$boolRs) return FALSE;
        $rs .= $boolRs;
        
        $boolRs = pack('a40',$dir1);
        if (!$boolRs) return FALSE;
        $rs .= $boolRs;
        
        $boolRs = pack('a40',$dir2);
        if (!$boolRs) return FALSE;
        $rs .= $boolRs;
        
        return $rs;
    }
    /**
     * ��ȡ���ɵ�һ������Ŀ¼���������鷽ʽ���أ�
     * Ŀǰ֧��ʹ��ext��ʽ������php unpack���������ַ�ʽ
	*/
    public static function getAllDirs()
    {
        $input = array(
            'forum_id'=>35,
            'cmd_no'=>self::CMD_ALL_DIR_BWS,
            'forum_name'=>'test',
        );
        $output = self::_structCall($input);
        $detail = $output['detail'];
        if (function_exists('fdir_detail2alldirs'))
        {
            return fdir_detail2alldirs($detail);
        }
        //split
        $detail = substr($detail,self::SIZE_FORUM_STAT_INFO);
        //process
        $maxLen = $output['detail_len'] - self::SIZE_FORUM_STAT_INFO;
	    //echo $maxLen;
        $tmpStart = 0;
        $dirInfo = array();
        while ($tmpStart<$maxLen)
        {
            $firstNode = substr($detail,$tmpStart,self::SIZE_FORUM_DIR_2_INFO);
            $firstNode = unpack('inum/a40',$firstNode);
            
            if (empty($firstNode))
            {
                break;
            }
            //print_r($firstNode);
            $_num = intval($firstNode['num']);
            $_name = $firstNode[1];
            if ($_num>0)
            {
                $tmpStart = $tmpStart + self::SIZE_FORUM_DIR_2_INFO;
                for ($_i=0;$_i<$_num;$_i++)
                {
                    $_tmpNode = substr($detail,$tmpStart,self::SIZE_FORUM_DIR_NAME);
                    $_tmpNode = unpack('a40',$_tmpNode);
                    $dirInfo[$_name][] = $_tmpNode[1];
                    $tmpStart = $tmpStart + self::SIZE_FORUM_DIR_NAME;
                }
            }
            else
            {
                $dirInfo[$_name] = array();
                $tmpStart = $tmpStart + self::SIZE_FORUM_DIR_2_INFO;
            }
            //echo $tmpStart;
        }
        return $dirInfo;
    }
    /**
     * ��ȡ�ɵ�Ŀ¼��Ϣ
     *
     * @param ��ID $fid
     * @return unknown
     */
    public static function getForumDirInfo($fid)
    {
        $input = array(
            'cmd_no'=> 1108,
            'fdir_forum_id'=>$fid,
        );
        $output = self::_mcpackCall($input);
        if (!empty($output))return $output;
        return FALSE;
    }
    
    private static function _mcpackCall($input)
    {
        if (!isset($input['magic'])) $input['magic'] = rpcUtil::getMagicNum();
        Rpc::rpcCall('fdir','query',$input,$output);
        if ( !isset($output) || empty($output) )
        {
            Rpc::putLog('warning','fdirRpc Call Failure!input:'.print_r($input,TRUE),__FILE__,__LINE__,NULL,0,NULL);
            return FALSE;
        }
        return $output;
    }
    
    private static function _structCall($input)
    {
        Rpc::rpcCall('fdir','struct',$input,$output);
        if ( !isset($output) || empty($output) )
        {
            Rpc::putLog('warning','fdirRpc Call Failure!input:'.print_r($input,TRUE),__FILE__,__LINE__,NULL,0,NULL);
            return FALSE;
        }
        return $output;
    }
}