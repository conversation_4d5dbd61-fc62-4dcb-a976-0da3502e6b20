<?php
/**
 * memoQueryType ��־�ۺ�����
 * <AUTHOR>
 * @package forum-manager
 * @since 2009-09-09
 *
 */
class memoQueryType
{
    const QUERY_TYPE_ALL = 0;
    
    const QUERY_TYPE_TOP = 1;
    
    const QUERY_TYPE_DEL = 2;
    
    const QUERY_TYPE_GOOD = 3;
    
    const QUERY_TYPE_TEAM = 23;//�ϲ��ˣ�23
    
    const QUERY_TYPE_ALBUM = 5;
    
    const QUERY_TYPE_PIC = 6;
    
    const QUERY_TYPE_MEMBER = 19;//todo
    
    const QUERY_TYPE_VIDEO = 8;
    
    const QUERY_TYPE_CAL = 10;
    
    const QUERY_TYPE_DEVELOP = 20;//TODO��չ����
    
    const QUERY_TYPE_SAFE = 21;//TODO ά���ΰ�
    
    const QUERY_TYPE_FILTER = 22;//���
    
    public static $queryType2cmdNo = array();
    
    public static $operateType2cmd = array();
    
    public static $operateType2mainCmd = array();
    
    public static function init()
    {
        self::$queryType2cmdNo = array(
            self::QUERY_TYPE_ALL =>array(
                cmCommandNo::MASK_DELETE,
            ),
        );
    }
    
    public static function getCmdName($cmd)
    {
        return cmCommandNo::getName($cmd);
    }
    
    public static function getOperateType($cmd)
    {
        if (isset(self::$operateType2cmd[$cmd]))
        {
            return self::$operateType2cmd[$cmd];
        }
        return '';
    }
    
    public static function getOperateTypeForMain($cmd,$postid=0)
    {
        if (isset(self::$operateType2mainCmd[$cmd]))
        {
            $ret = self::$operateType2mainCmd[$cmd];
            if ($cmd == cmCommandNo::MASK_DELETE)
            {
                if (empty($postid))
                {
                    $ret .= '����';
                }
                else 
                {
                    $ret .= '����';
                }
            }
            if ($cmd == cmCommandNo::MGR_AUDIT)
            {
                if ($postid=='2')
                {
                    $ret = '�ܾ�';
                }
                elseif($postid=='1')
                {
                    $ret = '��׼';
                }
            }
            return $ret;
        }
        return $cmd;
    }
    public static function getTeamNameByCmd($cmd)
    {
        switch ($cmd)
        {
            case cmCommandNo::SetForumManager:
            case cmCommandNo::DelForumManager:                
            case cmCommandNo::ApplicationResg:
                return '����';
            case cmCommandNo::SetForumAssistant:
            case cmCommandNo::DelForumAssistant:
                return 'С����';
            case cmCommandNo::SetForumPicAdmin:
            case cmCommandNo::DelForumPicAdmin:
                return 'ͼƬС��';
            case cmCommandNo::SetForumVideoAdmin:
            case cmCommandNo::DelForumVideoAdmin:
                return '��ƵС��';
        }
        return '';
    }
    
    public static function getLogTypeByCmd($cmd)
    {
        switch ($cmd)
        {
            case cmCommandNo::MaskGood:
            case cmCommandNo::CancelMaskGood:
                return 'thread_set_good';
            case cmCommandNo::MASK_DELETE:
            case cmCommandNo::CancelMaskDelete:
                return 'delete_thread';
            case cmCommandNo::MaskTop:
            case cmCommandNo::CancelMaskTop:
            case cmCommandNo::ZCSetHeadLine:
                return 'thread_set_top';
            case cmCommandNo::FilterForumUser:
            case cmCommandNo::CancelFilterForumUser:
            case cmCommandNo::FilterForumIP:
            case cmCommandNo::CancelFilterForumIP:
                return 'prison';
            case cmCommandNo::SetForumMember:
            case cmCommandNo::DelForumMember:
            case cmCommandNo::DelFourmApplier:
                return 'member';
            case cmCommandNo::SetForumManager:
            case cmCommandNo::DelForumManager:
            case cmCommandNo::ApplicationResg:
            case cmCommandNo::SetForumAssistant:
            case cmCommandNo::DelForumAssistant:
            case cmCommandNo::SetForumPicAdmin:
            case cmCommandNo::DelForumPicAdmin:
            case cmCommandNo::SetForumVideoAdmin:
            case cmCommandNo::DelForumVideoAdmin:
                return 'team';
        }
        return '';
    }
    //public static function 
}
memoQueryType::$operateType2cmd = array(
    cmCommandNo::MaskGood=>'��Ϊ',
    cmCommandNo::CancelMaskGood=>'ȡ��',
    cmCommandNo::MASK_DELETE=>'ɾ��',
    cmCommandNo::CancelMaskDelete=>'�����ָ�',
    
    cmCommandNo::MaskTop=>'��Ϊ',
    cmCommandNo::CancelMaskTop=>'ȡ��',
    cmCommandNo::ZCSetHeadLine=>'��Ϊͷ��',
    
    cmCommandNo::FilterForumUser=>'���',
    cmCommandNo::CancelFilterForumUser=>'ȡ�����',
    cmCommandNo::FilterForumIP=>'���',
    cmCommandNo::CancelFilterForumIP=>'ȡ�����',
    cmCommandNo::DelForumMember=>'ȡ��',
    
    
    cmCommandNo::SetForumMember=>'��׼',
    cmCommandNo::DelFourmApplier=>'�ܾ�',
    
    cmCommandNo::SetForumManager=>'��Ϊ',
    cmCommandNo::DelForumManager=>'����',
    cmCommandNo::ApplicationResg=>'��ȥ',
    cmCommandNo::SetForumAssistant=>'��Ϊ',
    cmCommandNo::DelForumAssistant=>'����',
    cmCommandNo::SetForumPicAdmin=>'��Ϊ',
    cmCommandNo::DelForumPicAdmin=>'����',
    cmCommandNo::SetForumVideoAdmin=>'��Ϊ',
    cmCommandNo::DelForumVideoAdmin=>'����',
    cmCommandNo::SetBlackList=>'���������',
    cmCommandNo::DelBlackList=>'�Ƴ�������',
);

memoQueryType::$operateType2mainCmd = array(
    cmCommandNo::MASK_DELETE=>'ɾ��',
    cmCommandNo::CancelMaskDelete=>'�ָ�',
    
    cmCommandNo::MaskTop => '�ö�',
    cmCommandNo::CancelMaskTop => 'ȡ���ö�',
    
    cmCommandNo::MaskGood=>'���þ�Ʒ',
    cmCommandNo::CancelMaskGood=>'������Ʒ',
    
    cmCommandNo::SetForumMember=>'������Ա',
    cmCommandNo::DelForumMember=>'ɾ����Ա',
    cmCommandNo::DelForumMemberByName=>'ɾ����Ա',
    cmCommandNo::DelForumMemberSelf=>'��Ա�˳�����',
    cmCommandNo::SetForumBlackByName=>'���������',
    cmCommandNo::SetForumBlackFromApplier=>'���������',
    cmCommandNo::SetForumBlackFromMember=>'���������',
    cmCommandNo::DelForumBlacklist=>'�Ƴ�������',
    cmCommandNo::ModDoorValue=>'�޸İ��ż�',
    
    
    //�����Ŷ�
    cmCommandNo::SetForumAssistant=>'���С����',
    cmCommandNo::DelForumAssistant=>'ȡ��С����',
    cmCommandNo::SetForumPicAdmin=>'����ͼƬС��',
    cmCommandNo::DelForumPicAdmin=>'ɾ��ͼƬС��',
    cmCommandNo::SetForumVideoAdmin=>'������ƵС��',
    cmCommandNo::DelForumVideoAdmin=>'ɾ����ƵС��',
    cmCommandNo::DelFourmApplier=>'�ܾ�',
    cmCommandNo::ZcStarFilUser=>'���������',
    cmCommandNo::ZcStarUnfilUser=>'�Ƴ�������',
    cmCommandNo::SetBlackList=>'���������',
    cmCommandNo::DelBlackList=>'�Ƴ�������',
    
    cmCommandNo::MGR_AUDIT=>'�������',
    //�������
    cmCommandNo::ZCAddCalendarEvent=>'��������',
    cmCommandNo::ZCDelCalendarEvent=>'ɾ�������',
    cmCommandNo::ZCModCalendarEvent=>'�޸������',
    cmCommandNo::ZCForumAddCalendar=>'�������',
    cmCommandNo::ZCForumDelCalendar=>'ɾ������',
    cmCommandNo::ZCForumModCalendar=>'�޸�������',
    //���ͼƬ
    cmCommandNo::ZCVAlbumCreat=>'����ר��',
    cmCommandNo::ZCVAlbumDel=>'ɾ��ר��',
    cmCommandNo::ZCVAlbumEdit=>'�༭ר��',
    cmCommandNo::ZcCreatAlbum=>'�������',
    cmCommandNo::ZcDelAlbum=>'ɾ�����',
    cmCommandNo::ZcEditAlbum=>'�༭���',
    cmCommandNo::ZcSetAlbumCover=>'��������',
    cmCommandNo::ZcAlbumAddPic=>'�ϴ�ͼƬ',
    cmCommandNo::ZcAlbumDelPic=>'ɾ��ͼƬ',
    cmCommandNo::ZcAlbumEditPic=>'�༭ͼƬ',
    cmCommandNo::ZcAlbumMovPic=>'�������',
    cmCommandNo::ZcAlbumAdunfilPic=>'�ָ�ͼƬ',
    //��������ó�Ա
    cmCommandNo::ZcStarModTitle=>'�༭����',
    cmCommandNo::ZcStarAddUser=>'��������ó�Ա',
    cmCommandNo::ZcStarDelUser=>'ɾ�������ó�Ա',
    cmCommandNo::ZcStarSetTop=>'��Ա�ö�',
    cmCommandNo::ZcStarEditUser=>'�༭��Ա',
    cmCommandNo::ZcStarFilUser=>'����Ա���������',
    cmCommandNo::ZcStarUnfilUser=>'ȡ����Ա������',
    cmCommandNo::ZcStarDelTop=>'��Աȡ���ö�',
    //��Ƶ
    cmCommandNo::ZCVideoUpdate=>'�ϴ���Ƶ',
    cmCommandNo::ZCVideoDelete=>'ɾ����Ƶ',
    cmCommandNo::ZCVideoEdit=>'�༭��Ƶ',
    cmCommandNo::ZCVideoRecover=>'�ָ���Ƶ',
    //������
    cmCommandNo::FilterForumUser=>'����û�',
    cmCommandNo::CancelFilterForumUser=>'�������',
    cmCommandNo::FilterForumIP=>'���IP',
    cmCommandNo::CancelFilterForumIP=>'�������',
);
