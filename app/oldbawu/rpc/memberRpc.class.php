<?php
/**
 * member��RPC IDL
 * <AUTHOR>
 * @package forum-manager
 * @since 2009-09-09
 *
 */
class memberRpc
{
    const PERM_FORUM_VIDEOADMIN =  0x1;
    const PERM_FORUM_PICADMIN =    0x2;
    const PERM_FORUM_ASSIST =      0x4;
    const PERM_FORUM_MANAGER =     0x8;
    const PERM_FORUM_MEMBER =      0x10;
    const PERM_FORUM_MEMBER_APPLY =0x20;
    const PERM_ROOT_MANAGER =      0x1000000;//���ɺ�̨����Ա
    const PERM_ROOT_PM =           0x2000000;//PMȨ��
    
    const PERM_FORUM_PUBLICATION_EDITOR = 0x4000; //�ɿ�����Ȩ��λ
    /**
     * detail�ֶνڵ�Ĵ�С��ԭ�����Բ���Ҫ��
     *
     */
    const DETAIL_NOTE_SIZE =         300;
    /**
     * ��ȡ�û��������ɵ�ʱ��
	*/
    public static function getMemberEnterTime($_uid,$_fid,$_perm)
    {
        //1073741825
        $output = self::_call(array(
        	'command_no'=>1073741825,
        	'nid'=>$_fid,
            'perm_flag0'=>$_perm,
            'uid'=>$_uid,
        ));
        $rs = FALSE;
        if ( (!empty($output)) && isset($output['perm_in_time']) )
        {
            if(isset($output['perm_in_time'][0]))
            {
                if (isset($output['perm_in_time'][0]['in_time']))
                {
                    $rs = $output['perm_in_time'][0]['in_time'];
                    if ($rs == 0)
                    {
                        //2008-6-23֮ǰ����
                        $rs = strtotime('20080623');
                    }
                    return $rs;
                }
            }
        }
        return $rs;
    }
    
    public static function getMemberByPerm($_fid,$perm=NULL,$_start=0,$_end=10)
    {
        if (empty($perm))$perm = self::PERM_FORUM_MEMBER;
        $output = self::_call(array(
        	'command_no'=>0x20,
        	'nid'=>$_fid,
            'perm_flag1'=>$perm,
            'start'=>$_start,
            'end'=>$_end,
        ));
        $rs = array(
            'total'=>0,
            'data'=>array(),
        );
        if ( (!empty($output)) && isset($output['detail']) && !empty($output['detail']) && isset($output['num']) )
        {            
            $rs['total'] = intval($output['num']);
            //todo data parse
            $rs['data'] = self::_parseDetail($output['detail'],$output['detail_len']);//print_r($_data);
        }
        return $rs;
    }
    /**
     * ��ȡ�ɵķ����Ϣ
     *
     * @param ��ID $_fid
     * @param ���ͣ�1���û���2��IP $type
     * @param int $start
     * @param int $end
     */
    public static function getForumFilter($_fid,$type=1,$_start=0,$_end=100,$arrConditions=array())
    {
    	/*
        $input = array(
        	'command_no'=>0x800,
        	'nid'=>$_fid,
            'perm_flag1'=>$type,
            'start'=>$_start,
            'end'=>$_end,
        );
        $input = array_merge($arrConditions,$input);
        $output = self::_call($input);
         */
        $output = PermServer::getForumBlock($type, $_fid, 0, 0, $_start, $_end, TRUE);
        
        $rs = array();
        $output = $output['user_block_list'];
        $rs['total'] = sizeof($output);
        $pm_role = PermConfig2::$roles['ROOT_PM'];
        if (!empty($output)){
        	foreach ($output as $arrVal){
        	
        		$_userid		= $arrVal['block']['user_id'];
        		$_userName		= $arrVal['block']['username'];
        		$_userIP		= $_userName;					// ��ȷ��
        		$_prisonTime	= $arrVal['end_time'] - $arrVal['start_time'];
        		$_operateByPm	= $arrVal['op_user']['username'];
        		$_operator		= $arrVal['op_user']['username'];
        		$_operate_time	= strtotime(Date('Y-m-d H:i:s'));	// ��ȷ��
        		$_ipType		= $arrVal['ip_type'];

        		$operate_by_pm 	= FALSE;	// ���ǹ���Ա
        		
        		if (PermServer::getUserHasRole($_userid, $_fid, $pm_role, TRUE)){
					$_operateByPm = TRUE;
					$_operator = Bawu_Global::MANAGER_NAME;
        		}
        		
				if (2 == $type)
				{
					//���IP
					$_prisonTime = ceil($_prisonTime/24);
					$_userNameIsIp = TRUE;
					if ($_ipType == 4)
					{
						//����Ƿ����ڷ���û���ʱ��ͬʱ�����IP
						$_userName = TiebaUtil::formatIp(Phpbean_Util::long2ip($_userName),3);
						$_ipType = BawuUrlToken::encodeIp($_userName);
					} else {
						//��ͨ���
						$_userName = TiebaUtil::formatIp(Phpbean_Util::long2ip($_userName));
					}
				}
                        
	        	$data[] = array(
					'user_id'=>$_userid,
					'user_name'=>$_userName,
					'user_ip'=>$_userIP,
					'user_name_is_ip'=>$_userNameIsIp,
					'prison_time'=>$_prisonTime, 
					'operate_by_pm' => $_operateByPm, //Ϊtrueʱ�����ǹ���Ա                    
					'operator'=>$_operator,
					'operate_time'=>$_operate_time,
					'ip_type'=>$_ipType,
				);
        	}
        }
        /*if ( (!empty($output)) && isset($output['detail']) && !empty($output['detail']) && isset($output['num']) )
        {
            $rs = array();
            $rs['total'] = intval($output['num']);
            //todo data parse
            $_data = self::_parseDetail($output['detail'],$output['detail_len']);//print_r($_data);
            $data = array();
            if (!empty($_data))
            {
                $_i = 0;
                foreach ($_data as $_d)
                {
                    if (isset($_d['statics']))
                    {
                        $statics = $_d['statics'];
                    }
                    else 
                    {
                        $statics = self::_getStaticsFromDetail($output['detail'],$_i);//print_r($statics);
                    }
                    //$_d['perm_flag']
                    $_operateByPm = FALSE;
                    $_operator = $_d['msg'];
                    $_userName = $_d['username'];
                    $_userNameIsIp = FALSE;
                    $_ipType = 0;
                    if (empty($statics))
                    {
                        $_prisonTime = 0;
                    }
                    else 
                    {
                        $_prisonTime = $statics[0];
                        $_operatorPerm = $statics[1];
                        if ( TiebaPerm::isManager($_operatorPerm))
                        {
                            $_operateByPm = TRUE;
                            $_operator = Bawu_Global::MANAGER_NAME;
                        }
                        //
                        if (2==$type)
                        {
                            //���IP                           
                            $_prisonTime = ceil($_prisonTime/24);
                            $_userNameIsIp = TRUE;
                            //����Ƿ����ڷ���û���ʱ��ͬʱ�����IP
                            if ( isset($statics[2]) && intval($statics[2])==1 )
                            {
                                $_userName = TiebaUtil::formatIp(Phpbean_Util::long2ip($_d['username']),3);
                                $_ipType = BawuUrlToken::encodeIp($_d['username']);
                            }
                            else 
                            {
                                //��ͨ���
                                $_userName = TiebaUtil::formatIp(Phpbean_Util::long2ip($_d['username']));
                            }
                        }
                    }
                    $data[] = array(
                        'user_id'=>$_d['uid'],
                        'user_name'=>$_userName,
                        'user_ip'=>$_d['username'],
                        'user_name_is_ip'=>$_userNameIsIp,
                        'prison_time'=>$_prisonTime, 
                        'operate_by_pm' => $_operateByPm, //Ϊtrueʱ�����ǹ���Ա                    
                        'operator'=>$_operator,
                        'operate_time'=>$_d['f_time'],
                        'ip_type'=>$_ipType,
                    );
                    ++ $_i;
                }
            }
            $rs['data'] = $data;
            return $rs;
        }
        */
        return FALSE;
    }
    /**
     * ����ָ���û���Ȩ�ޣ�ȫ�ֵģ���PM����HM
     */
    public static function getUserGlobalPerm($_uid)
    {
    	$output = PermServer::getUserPerm(0, $_uid, NULL, TRUE, TRUE);
    	return $output;
        /*$output = self::_call(array(
        	'command_no'=>0x1,
        	'uid'=>$_uid,
            'perm_flag0'=>self::PERM_ROOT_PM|self::PERM_ROOT_MANAGER,
        ));
        if ( (!empty($output))&&(isset($output['perm_flag'])) )
        {
            if ($output['perm_flag'] == self::PERM_ROOT_PM)
            {
                return self::PERM_ROOT_PM;
            }
            if ($output['perm_flag'] == self::PERM_ROOT_MANAGER)
            {
                return self::PERM_ROOT_MANAGER;
            }
        }
        return FALSE;
        */
    }
    /**
     * ����ָ���û���ָ���ɵ�Ȩ��
     *
     * @param �û�ID $_uid
     * @param ���� $_fid
     * @return unknown
     */
    public static function getUserPermInForumWithFname($_uid,$_fid,$_fname)
    {
    	if (empty($_fid)){
    		$_fid = $_fname;
    	}
    	$output = PermServer::getUserPerm($_fid, $_uid, NULL, TRUE, TRUE);
    	return $output;
    	/*
    	$output = self::_call(array(
        	'command_no'=>0x2,
        	'uid'=>$_uid,
            'nid'=>$_fid,
            'node_name'=>$_fname,
        ));
        if ( (!empty($output))&&(isset($output['perm_flag'])) )
        {
            return $output['perm_flag'];
        }
        return FALSE;
        */        
    }
    
    /**
     * ����ָ���û���ָ�����Ƿ����ָ��Ȩ��
     * 
     * @param �û�ID			- $intUserID
     * @param ��ID			- $intForumID
     * @param ָ��Ȩ��		- $intPermID
     * @param ָ��ϸ��Ȩ��	- $intConditionID
     * @return TRUE/FALSE
     */
    public static function checkUserPermInForum($intUserID, $intForumID, $intPermID, $intIP=NULL, $intConditionID=0){
    	//$strIP = CommonLib::getClientIP();
    	$boolRes = PermServer::checkUserPerm($intUserID, $intForumID, $intPermID, $intIP, $intConditionID);
    	return $boolRes;
    }
    
    /**
     * ����ָ���û���ָ���ɵ�Ȩ��
     *
     * @param �û�ID $_uid
     * @param ���� $_fid
     * @return unknown
     */
    public static function getUserPermInForum($_uid,$_fid)
    {
    	$intIP = ip2long(CommonLib::getClientIP());
    	$arrResTmp = PermServer::getUserPerm($_uid, $_fid, $intIP, TRUE);
    	$arrRes = $arrResTmp;
    	return $arrRes;
        /*
        $output = self::_call(array(
        	'command_no'=>0x2,
        	'uid'=>$_uid,
            'nid'=>$_fid,
        ));
        
        if ( (!empty($output))&&(isset($output['perm_flag'])) )
        {
            return $output['perm_flag'];
        }
        return FALSE;
        */        
    }
    /**
     * ȡ����������Ȩ���û�
     *
     * @param unknown_type $_fid
     * @param unknown_type $_permFlag
     * @return unknown
     */
    public static function getForumManager($_fid,$_permFlag)
    {
        $intOffset = 0;
        $intLimit = 1000;
        $intIsAdmin = 0;
        $intSortBy = 0;
        $intType = 0;
        $bolRunSoon = TRUE;
    	$arrRes = PermServer::getRoleUserList($_fid, $_permFlag, $intOffset, $intLimit, $intIsAdmin, $intSortBy, $intType, $bolRunSoon);
    	$arrRes = $arrRes['res'];
    	return $arrRes;
        /*$output = self::_call(array(
        	'command_no'=>0x40,
        	'perm_flag1'=>$_permFlag,
            'nid'=>$_fid,
        ));
        if ( (!empty($output)) && isset($output['detail']) && !empty($output['detail']) )
        {
            return self::_parseDetail($output['detail'],$output['detail_len']);
        }
        return FALSE;
        */
    }
    /**
     * ���ݰ�ID��ȡ�ɵĻ�Ա��,just member
     *
     * @param ���� $_fid
     * @return ��Ա��
     */
    public static function getMemberNumByFid($_fid)
    {
    	$arrInfo = PermServer::getForumInfo($_fid, TRUE);
    	$intNum = $arrInfo['member'];
    	return $intNum;
    	/*
        $output = self::_call(array(
        	'command_no'=>0x200,
        	'nid'=>$_fid));
        if ( (!empty($output))&&(isset($output['num'])) )
        {
            return intval($output['num']);
        }
        return 0;
        */
    }
    /**
     * ��ȡ�ɵĲ�ͬ�û�����Ա��PM���������б�
     *
     * @param unknown_type $_fid
     * @param unknown_type $perm
     * @param unknown_type $start
     * @param unknown_type $end
     * @return unknown
     */
    public static function getMembersByFid($_fid,$start,$end,$perm = NULL)
    {
        if ( empty($perm) )
        {
            //$perm = self::PERM_FORUM_MEMBER;
            $perm = PermConfig2::$roles['FORUM_MEMBER'];
        }
        $output = self::_call(array(
        	'command_no'=>0x20000,
        	'nid'=>$_fid,
            'perm_flag1'=>$perm,
            'start'=>$start,
            'end'=>$end,
        ));
        if ( (!empty($output)) && isset($output['detail']) && !empty($output['detail']) )
        {
            return self::_parseDetail($output['detail'],$output['detail_len']);
        }
        return FALSE;
    }
    /**
     * �����û�idȡ�û�����Ϣ(�û�����ע��ʱ��)
     *
     * @param unknown_type $uid
     * @return unknown
     */
    public static function getUserInfoByUid($_uid)
    {
    	$output = self::_call(array(
            'command_no' => 0x40000,
            'uid'=>$_uid,
        ));
        return $output;
    }
    /**
     * �����û�ID��ȡ�û���
     *
     * @param unknown_type $_uid
     * @return unknown
     */
    public static function getUserName($_uid)
    {
        $info = self::getUserInfoByUid($_uid);
        if (isset($info['username'])) return $info['username'];
        return FALSE;
    }
    /**
     * �����û�ID��ȡ�û�ע��ʱ��
     *
     * @param unknown_type $_uid
     * @return unknown
     */
    public static function getRegTimeByUid($_uid)
    {
        $info = self::getUserInfoByUid($_uid);
        if (isset($info['reg_date'])) return $info['reg_date'];
        return FALSE;
    }
    
    /**
     * ��ȡ�û��ڵ�½������ʾ�İ����б�
     *
     * @param ���� $_uid
     * @return ����
     */
    public static function getTopShowForums($_uid)
    {
    	$output = PermServer::getUserForumList($_uid, 0, 0, 1000, TRUE);
    	return $output;
    	/*$output = self::_call(array(
        	'command_no'=>0x200000,
        	'uid'=>$_uid));
        if ( (!empty($output)) && isset($output['top_forums']))
        {
            return $output['top_forums'];
        }
        return array();
        */
    }
    /**
     * �����û�ID��ȡָ����(start,end)�İ��б�
     *
     * @param ���� $_uid
     * @param ���� $_start
     * @param ���� $_end
     * @return ����
     */
    public static function getMemberForumsByUid($_uid,$_start,$_end)
    {
        $output = PermServer::getUserForumList($_uid, 0, $_start, $_end, TRUE);
        return $output;
    	/*
        $output = self::_call(array(
            'command_no' => 0x100000,
            'uid'=>$_uid,
            'start'=>$_start,
            'end'=>$_end,
        ));
        return $output['top_member_forums'];
        */
    }
    /**
     * �����û�idȡ�û���ȫ�ַ��ʣ��ʱ��
     *
     * @param unknown_type $uid
     * @return unknown
     */
    public static function getGlobalFilterTimeByUid($_uid)
    {
    	$t_output = PermServer::getUserBlockInfo(0, $_uid, NULL, TRUE );
    	$tmp_output = $t_output[0];
    	$output = $tmp_output['end_time'] - $tmp_output['start_time'];
   		return $output;
    	/*
        $output = self::_call(array(
            'command_no' => 0x80000,
            'uid'=>$_uid,
        ));
        if ( (!empty($output)) && isset($output['global_fil_time']))
        {
            return $output['global_fil_time'];
        }
        return 0;
        */
    }
    
    private static function _call($input)
    {
        if (!isset($input['magic'])) $input['magic'] = rpcUtil::getMagicNum();
        if (!isset($input['real_ip'])) $input['real_ip'] = self::_getIp();

        Rpc::rpcCall('member','query',$input,$output);
        if ( !isset($output) || empty($output) )
        {
            Rpc::putLog('warning','RpcCall Postcm Failure!input:'.print_r($input,TRUE),__FILE__,__LINE__,NULL,0,NULL);
            return FALSE;
        }
        return $output;
    }
    
    private static function _parseDetail($detail,$detail_len=0)
    {
        if (function_exists('member_detail2array'))
        {
            if (empty($detail_len)) $detail_len = strlen($detail);
            $_num = floor($detail_len/self::DETAIL_NOTE_SIZE);
            return member_detail2array($detail,$_num);
        }
        else 
        {
            trigger_error('memberRpc:can not find function[member_detail2array]',E_USER_ERROR);
            return FALSE;
        }
    }
    private static function _getStaticsFromDetail($detail,$index)
    {
        $nodeSize = self::DETAIL_NOTE_SIZE;
        $begin = $index * self::DETAIL_NOTE_SIZE + 16;
        $str = substr($detail,$begin,16);
        if (16 == strlen($str))
        {
            $rs = unpack('I4',$str);
            return $rs;
        }
        return FALSE;
    }
    /**
     * TODO
     *
     * @param unknown_type $detail
     * @param unknown_type $detailLen
     */
    private static function _parseDetailWithPhp($detail,$detailLen=0)
    {
        if (empty($detail_len)) $detail_len = strlen($detail);
        $nodeSize = self::DETAIL_NOTE_SIZE;
        $_num = floor($detail_len/$nodeSize);
        $data = array();
        $_item = array();
        for ($i=0;$i<$_num;$i++)
        {
            $_nowStr = substr($detail,$i*$nodeSize,$nodeSize);
                        
        }
    }
    
    private static function _getIp()
    {
        return rpcUtil::getIp();
    }
}
