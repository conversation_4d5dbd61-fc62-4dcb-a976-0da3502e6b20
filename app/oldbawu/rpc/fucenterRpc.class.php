<?php
/**
 * fucenter��RPC IDL
 * <AUTHOR>
 * @package forum-manager
 * @since 2009-09-09
 *
 */
class fucenterRpc
{
    const MAX_GET_USERINFO_BY_UIDS_NUM = 45;
    /**
     * �����û����ƻ�ȡ�û���Ϣ
     *
     * @param �û��� $uname
     * @return Array/False
     */
    public static function getUserInfoByUname($uname)
    {
		$intUserId = self::getUseridByUname($uname);
		if ( false === $intUserId || !is_numeric($intUserId) ) {
			return false;
		}
        $arrInput = array ('user_id' => $intUserId, 'need_pass_info' => 1 );
		$arrOut = Tieba_Service::call ( 'user', 'getUserDataEx', $arrInput );
		if (Tieba_Errcode::ERR_SUCCESS !== $arrOut ['errno']) {
			Bingo_Log::warning ( 'call service user::getUserDataEx failed,errno[' . $arrOut ['errno'] . '],errmsg[' . $arrOut ['errmsg'] . ']' );
			return false;
		}
		$arrUserInfo = $arrOut ['output'] ['user_info']['puserinfo'];
		return $arrUserInfo;
    }
    /**
     * �����û����ƻ�ȡ�û�ID
     *
     * @param �û��� $uname
     * @return �û�ID/0
     */
    public static function getUseridByUname($uname)
    {
        $arrInput = array ('user_name' => array($uname) );
		$arrOut = Tieba_Service::call ( 'user', 'getUidByUnames', $arrInput );
		if (Tieba_Errcode::ERR_SUCCESS !== $arrOut ['errno']) {
			Bingo_Log::warning ( 'call service user::getUidByUnames failed,errno[' . $arrOut ['errno'] . '],errmsg[' . $arrOut ['errmsg'] . ']' );
			return false;
		}
		$arrUserInfo = $arrOut ['output'] ['uids'];
		if ( isset($arrUserInfo[0]['user_name']) && $arrUserInfo[0]['user_name'] == $uname ) {
			return $arrUserInfo[0]['user_id'];
		}
		return false;
    }
    /**
     * ���������û�ID��ȡ�����û�����Ϣ
     *
     * @param �û�ID���� $uids
     * @return ����/FALSE
     */
    public static function getUserinfoByUids($uids)
    {
        if( count($uids) <= fucenterRpc::MAX_GET_USERINFO_BY_UIDS_NUM ){
            $input['uids'] = $uids;
            $input['command_no'] = 701;
            $output = self::_call($input);
            if ( !empty($output) && isset($output['uinfos']) )
            {
                return $output['uinfos'];
            }
            return FALSE;
        }else{
            $uidArray = array_chunk($uids, fucenterRpc::MAX_GET_USERINFO_BY_UIDS_NUM,false);
            $retArray   = array();
            
            foreach($uidArray as $chunk_uids){
                $input['uids'] = $chunk_uids;
                $input['command_no'] = 701;
                $output = self::_call($input);
                if ( !empty($output) && isset($output['uinfos']) )
                {
                    $retArray = array_merge($retArray,$output['uinfos']);
                }else{
                    return FALSE;
                }
            }
            return $retArray;
        }
    }
    
    private static  function _call($input)
    {
        if (!isset($input['magic'])) $input['magic'] = rpcUtil::getMagicNum();
        Rpc::rpcCall('fucenter','query',$input,$output);
        if ( !isset($output) || empty($output) )
        {
            Rpc::putLog('warning','fucenterRpc Call Failure!input:'.print_r($input,TRUE),__FILE__,__LINE__,NULL,0,NULL);
            return FALSE;
        }
        return $output;
    }
}