<?php
/**
 * zycontrolRpc���񽻻���IDL�ӿ�
 * <AUTHOR>
 * @since 2009-08-27
 * @package forum-manager
 *
 */
class zycontrolRpc
{
    const ZYCTRL_GET_FORUM_INFO    = 0x201001;          //����forum-name, forum-id����
    const ZYCTRL_GET_MODU        = 0x201002;           //����forum-id��ȡģ����Ϣ
    const ZYCTRL_GET_FORUM_INFO_PURE = 0x201003;	//����forum-name, forum id���飬���ǲ�������ʹ����ת��
    
    const ZY_MOUD_HALL       = 0x01;             //������
    const ZY_MOUD_FILE       = 0x02;             //���� 
    const ZY_MOUD_ALBUM      = 0x03;             //ͼƬ��
    const ZY_MOUD_GOOD    = 0x04;             //��Ʒ��
    const ZY_MOUD_KANDIAN    = 0x05;             //���ɿ��� 
    const ZY_MOUD_RELFORUM   = 0x06;             //������� 
    const ZY_MOUD_FRIFORUM   = 0x07;             //��������
    const ZY_MOUD_SELFDEFINE = 0x08;             //�Զ���ģ��
    const ZY_MOUD_VEDIO      = 0x09;             //��Ƶ
    const ZY_MOUD_STOCK      = 0x0a;             //��Ʊ
    const ZY_MOUD_STOCKNEWS  = 0x0b;             //��Ʊ����
    const ZY_MOUD_WEATHER    = 0x0c;             //����ģ��
    const ZY_MOUD_HI         = 0x0d;             //hi
    const ZY_MOUD_CALENDAR   = 0x0e;             //����ģ��
    const ZY_MOUD_FDEFINE    = 0x0f;
    
    const ZY_MOUD_PM_ON = 0x1;
    const ZY_MOUD_BZ_ON = 0x2;       //��������
    
    private static $_hashModuleInfo = array();
    
    /**
     * �ж�$fid�İ��Ƿ�ͨ��ģ��$modid
	*/
    public static function isModuleOpen($fid,$fname,$modId)
    {
        return FALSE;
        //$mods = self::getForumModuInfo($fid,$fname);
        //if ( $mods && isset($mods[$modId]))
        //{
        //    if ( $mods[$modId] & self::ZY_MOUD_BZ_ON )
        //    {
        //        return TRUE;
        //    }
        //}
        //return FALSE;
    }
    /**
     * ��ȡһ���ɵ�ģ�鿪����Ϣ������static����
	*/
    public static function getForumModuInfo($fid,$fname)
    {
        // static cache
        if (!empty(self::$_hashModuleInfo[$fid]))
        {
            return self::$_hashModuleInfo[$fid];
        }
        $output = self::_mcpackCall(array(
            'cmd_no'=>self::ZYCTRL_GET_MODU,
            'forum_id'=>$fid,
            'forum_name'=>$fname,
            'req_num'=>1,//�����һ��object����detail��	һ��object���飬object�������
            'detail'=>array(),
        ));
        if ( !empty($output) && isset($output['detail']) )
        {
            //build hash
            $hash = array();
            foreach ($output['detail'] as $_mod)
            {
                $hash[$_mod['moud_id']] = $_mod['status'];
            }
            self::$_hashModuleInfo[$fid] = $hash;//static cache;
            return $hash;
        }
        return FALSE;
    }
    /**
     * �ͺ��ģ����н���
	*/
    private static function _mcpackCall($input)
    {
        if (!isset($input['magic'])) $input['magic'] = rpcUtil::getMagicNum();
        Rpc::rpcCall('zycontrol','query',$input,$output);
        if ( !isset($output) || empty($output) )
        {
            Rpc::putLog('warning','zycontrolRpc Call Failure!input:'.print_r($input,TRUE),__FILE__,__LINE__,NULL,0,NULL);
            return FALSE;
        }
        return $output;
    }
}
