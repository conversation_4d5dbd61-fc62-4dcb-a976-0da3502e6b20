<?php
/**
 * PASSPORT �ӿ��ļ�����ȡ�û���Ϣ����Puserinfo�Ļ����Ͻ�һ����װ
 * <AUTHOR>
 * @since 2009-08-31
 * @package forum-manager
 *
 */
//require_once("/home/<USER>/php5/lib/php-libs/Tieba/Service.php");
//require_once("/home/<USER>/php5/lib/php-libs/Tieba/Errcode.php");
class PassportUserInfo
{
    /**
     * Ĭ���ֶ�
     *
     * @var ����
     */
    public static $fields = array(
        'uid',
        'userid',
        'username',
        'realname',
        'userstate',
        'email',
        'secure',
        'lockmail',
        'userdetail',
        'sex',
        'regtime',
        'regip',
        'lasttime',
        'lastip',
    );
    /**
     * ͨ���û�����ȡ�û�ID
     *
     * @param �û��� $username
     * @return �û�ID/FALSE
     */
    /*
    public static function getUidByUname($username)
    {
        $rs = PuserInfo::getInfoByun($username);
        if ( $rs && isset($rs['uid']) )
        {
            return $rs['uid'];
        }
        return FALSE;
    }*/
    public static function getUidByUname($username) {
	    $arrInput = array(
		    'user_name' => array($username),
		    );
	    $arrOut = Tieba_Service::call('user', 'getUidByUnames',$arrInput);
	    if(Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']) {
		    return false;
	    }
	    return $arrOut['output']['uids'][0]['user_id'];
    }
    /**
     * ͨ���û�ID��ȡ�û���
     *
     * @param �û��� $uid
     * @return �û�ID/FALSE
     */
    /*
    public static function getUnameByUid($uid)
    {
        $rs = PuserInfo::getInfoByuid($uid,array('username'));
        if ( $rs && isset($rs['username']) )
        {
            return $rs['username'];
        }
        return FALSE;
    }*/
    public static function getUnameByUid($uid) {
	    $arrInput = array(
		    'user_id' => array($uid),
	    );
	    $arrOut = Tieba_Service::call('user', 'getUnameByUids', $arrInput);
	    if(Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']) {
		    return false;
	    }
	    return $arrOut['output']['unames'][0]['user_name'];
    }
    /**
     * �����û�ID��ȡ�û��������Ϣ
     *
     * @param �û�ID $uid
     * @param �ֶ� $fields
     * @return ����
     */
    public static function getUserInfoByUid($uid,$fields=array())
    {
        if (empty($fields)) $fields = self::$fields;
        return PuserInfo::getInfoByuid($uid,$fields);
    }
    /**
     * �����û����ƻ�ȡ�û��������Ϣ
     *
     * @param �û��� $uname
     * @param �ֶ� $fields
     * @return ����
     */
    public static function getUserInfoByUname($uname,$fields=array())
    {
        if (empty($fields)) $fields = self::$fields;
        return PuserInfo::getInfoByun($uname,$fields);
    }
}
