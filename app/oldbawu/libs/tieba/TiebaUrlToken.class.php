<?php
/**
 * URLһ����У��Ŀ��࣬�ṩ���ܺ�����ƥ�书��
 * <AUTHOR>
 * @since 2009-08-27
 * @package forum-manager
 *
 */
class TiebaUrlToken
{
    /**
     * ����˽Կ
     *
     * @var �ַ���
     */
    private static $_privateKey = 'baiduTieBa123&*($';
    /**
     * �������ܴ�
     *
     * @param cm����� $cm
     * @param �û�ID $uid
     * @param �ؼ��֣����� $word
     * @param �û��� $uname
     * @param ��ID $fid
     * @param ����ID $tid
     * @param ����ID $pid
     * @return �����ַ���
     */
    public static function generate($cm,$uid,$word='',$uname='',$fid=0,$tid=0,$pid=0)
    {
        $str = sprintf('cm=%d&word=%s&fid=%d&uname=%s&uid=%d&tid=%d&pid=%d&key=%s',
            $cm,$word,$fid,$uname,$uid,$tid,$pid,self::$_privateKey);
        return self::_md5($str);
    }
    /**
     * �������ַ�������ȷ��
     *
     * @param ��Ҫ���ļ����ַ��� $tbstoken
     * @param CM����� $cm
     * @param �û�ID $uid
     * @param ���� $word
     * @param �û��� $uname
     * @param ��ID $fid
     * @param ����ID $tid
     * @param ����ID $pid
     * @return true/false
     */
    public static function check($tbstoken,$cm,$uid,$word='',$uname='',$fid=0,$tid=0,$pid=0)
    {
        $str = sprintf('cm=%d&word=%s&fid=%d&uname=%s&uid=%d&tid=%d&pid=%d&key=%s',
            $cm,$word,$fid,$uname,$uid,$tid,$pid,self::$_privateKey);
        if ( self::_md5($str) == $tbstoken )
        {
            return TRUE;
        }
        return FALSE;
    }


    public static function check2($tbstoken)
    {
        if ( Tieba_Tbs::check($tbstoken,true) )
        {
            return TRUE;
        }
        return FALSE;
    }






    /**
     * ���ܷ���
     *
     * @param ����ַ��� $value
     * @return ���ܽ��
     */
    private static function _md5($value)
    {
        //return md5($value,TRUE);
        return md5($value);
    }
    /**
     * ���ü���˽Կ
     *
     * @param �ַ�����˽Կ $str
     */
    public static function setPrivateKey($str)
    {
        self::$_privateKey = $str;
    }
}
