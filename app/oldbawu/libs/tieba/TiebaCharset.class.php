<?php
/**
 * ����ת�����࣬����textprocess extension,������textprocess��չ���صļ��
 * <AUTHOR>
 * @package forum
 * @since 2009-09-07
 *
 */
class TiebaCharset
{
    const UCONV_INVCHAR_IGNORE   = 0x01;
    const UCONV_INVCHAR_REPLACE  = 0x02;
    const UCONV_INVCHAR_ERROR    = 0x03;
    const UCONV_INVCHAR_ENTITES  = 0x04;
    
    /**
     * �Ƿ��Ѿ�������textprocess extension,���ȷ���˿���ֱ�����ó�TRUE���Ӷ��������
     *
     * @var true/false
     */
    private static $_isLoad = FALSE;
	/**
     * �ж��������ַ����Ƿ�����������֡��»��ߡ����֡����ֺ���ĸ��
     *
     * @param string $str
     * @return true/false
     */
    public static function gbkIsNormalStr($str)
    {
        if (self::_checkIsLoad('gbk_is_normal_str'))
        {
            return gbk_is_normal_str($str);
        }
        return TRUE;
    }
	/**
     * ��ȡGBK�ַ�����������utf->gbk��ת��
     *
     * @param string $str
     * @return string
     */
    public static function getGbk($str)
    {
        //noting todo����ת���Ѿ���֮ǰ�������
        return $str;      
    }
    /**
     * utf8_to_gbk����ת��
     *
     * @param �ַ��� $str
     * @param ��ʶ $flag
     * @return �ַ���
     */
    public static function utf8ToGbk($str,$flag=NULL)
    {
        return self::getGbk($str);
    }
    /**
     * �ж��ַ����Ƿ���UTF8��ʽ�ģ�Ĭ����TRUE
     *
     * @param �����ַ� $value
     * @param �Ƿ������һ���ַ� $checkLastChar
     * @return true/false
     */
    public static function isUtf8($value,$checkLastChar=TRUE)
    {
        if (self::_checkIsLoad('is_utf8'))
        {
            return is_utf8($value,$checkLastChar);
        }
        return TRUE;
    }
    /**
     * ����Ƿ��������չ��������$functionName����
     *
     * @param �ַ��� $functionName
     * @return true/false
     */
    private static function _checkIsLoad($functionName=NULL)
    {
        if (FALSE === self::$_isLoad)
        {
            if ( extension_loaded('textprocess') )
            {
                self::$_isLoad = TRUE;
            }
            else 
            {
                trigger_error('Load textprocess Failure!can not find [textprocess] extension',E_USER_WARNING);
                return FALSE;
            }
        }
        if (function_exists($functionName))
        {
            return TRUE;
        }
        else 
        {
            trigger_error('Load textprocess Failure!can not find function ['.
                $functionName . ']',E_USER_WARNING);
            return FALSE;
        }
    }
}