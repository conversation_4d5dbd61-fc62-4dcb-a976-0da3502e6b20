<?php
/**
 * ���ɵĳ�������
 * <AUTHOR>
 * @package forum
 * @since 2009-09-07
 *
 */
class TiebaUtil 
{
	const IP_KEY = 123456;
	const IP_STR_KEY = 'IP_ENCODE';
	
	private static $_arrBakan = array();
    private static $_arrDaquan = array();

    private static function _getHttp($url){
        $ch = curl_init();    
        curl_setopt($ch, CURLOPT_URL, $url);    
        curl_setopt($ch, CURLOPT_HEADER, 0);    
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);  
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 1); 
        curl_setopt($ch, CURLOPT_TIMEOUT, 1); 
        $data = curl_exec($ch);    
        curl_close($ch);
        return $data;
    }

	public static function getBakanIsOpen($strFname)
	{
		if ( array_key_exists($strFname, self::$_arrBakan) ) {
			return self::$_arrBakan[$strFname];
		}
		$url = sprintf(BAKAN_IS_OPEN_URL, rawurlencode($strFname));
        //		$ret = file_get_contents($url);
        $ret = self::_getHttp($url);
		if (intval($ret) == 1) {
			self::$_arrBakan[$strFname] = true;
			return true;
		}
		self::$_arrBakan[$strFname] = false;
		return false;
    }

    public static function getDaquanIsOpen($strFname)
    {  
        if ( array_key_exists($strFname, self::$_arrDaquan) ) { 
            return self::$_arrDaquan[$strFname];
        }
        $url = sprintf(DAQUAN_IS_OPEN_URL, rawurlencode($strFname));
        // $ret = file_get_contents($url);
        $ret = self::_getHttp($url);
        if (intval($ret) == 1) {
            self::$_arrDaquan[$strFname] = true;
            return true;
        }   
        self::$_arrDaquan[$strFname] = false;
        return false;
    } 


	public static function getDecodeIp($strEncode)
	{
		$retArr = fcrypt_hstr_2id(self :: IP_STR_KEY, $strEncode);
		if ( is_array($retArr) && (count($retArr) == 2) ) {
			if ($retArr[1] == self :: IP_KEY) {
				return intval($retArr[0]);
			}
		}
		return 0;
	}

	public static function getEncodeIp($intIp)
	{
		return fcrypt_id_2hstr(self :: IP_STR_KEY, $intIp, self :: IP_KEY);
	}

    public static function checkResignation($value)
    {
        $value = rawurldecode($value);
        $value = TiebaCharset::getGbk($value);      
        if ( ($len=mb_strlen($value,'gbk'))>1000)
        {
            return FALSE;
        }
        return TRUE;
    }
    
    public static function checkFdsHour($value)
    {
    	if (in_array(intval($value), Bawu_Global::$ALLOW_FDS_HOURS)) {
    		return true;
    	}
    	return false;
    }
    
    public static function checkFdsGrade($value)
    {
    	if (in_array(intval($value), Bawu_Global::$ALLOW_FDS_GRADES)) {
    		return true;
    	}
    	return false;
    }
    
    public static function checkJzwFdsGrade($value)
    {
    	if (in_array(intval($value), Bawu_Global::$ALLOW_JZW_FDS_GRADES)) {
    		return true;
    	}
    	return false;
    }
    
    
	public static function checkJzwFdsHour($value)
    {
    	if (in_array(intval($value), Bawu_Global::$ALLOW_JZW_FDS_HOURS)) {
    		return true;
    	}
    	return false;
    }
    
	public static function checkJzwFdsThreadThreshold($value)
    {
    	if (in_array(intval($value), Bawu_Global::$ALLOW_JZW_THREAD_THRESHOLD)) {
    		return true;
    	}
    	return false;
    }
    
    /**
     * ��ȡ�û���sessionId,��cookie�л�ȡ
     *
     * @param cookie�ֶεĹؼ��� $cookieKey
     * @return unknown
     */
    public static function getSessionId($cookieKey=NULL)
    {
        if (NULL == $cookieKey) $cookieKey = 'BDUSS';
        if (isset($_COOKIE[$cookieKey]))$cookie = Phpbean_Filter::noTags($_COOKIE[$cookieKey]);
        if (!empty($cookie))
        {
            return String::substr($cookie,0,64);
        }
        return FALSE;
    }
    /**
     * �ж��Ƿ���IP��ַ
     *
     * @param unknown_type $ip
     * @return unknown
     */
    public static function isIp($ip)
    {
        $ips = array_filter(explode('.',$ip));
        if (count($ips)==4)
        {
            return true;
        }
        return false;
    }
    /**
     * ��ʽ�����IP
     *
     * @param unknown_type $ip
     * @return unknown
     */
    public static function formatIp($ip,$maskNum = 1)
    {
        $ips = explode('.',$ip);
        $maskNum = 4-$maskNum;
        if ($maskNum<1) $maskNum = 1;
        for ($i=3;$i>=$maskNum;$i--)
        {
            $ips[$i] = '*';
        }
        return implode('.',$ips);
    }
    /**
     * �ж��Ƿ����ֻ�IP,��ӻ��湦��
     *
     * @param IP��ַ $ip
     * @param IP�ļ�·�� $ipFileName
     * @return unknown
     */
    public static function isWapIp($ip,$ipFileName)
    {
        /*
        $basePath = BAWU_DATA_ROOT_PATH;
        $hasCacheFile = $basePath . '/ipcache';
        $cacheDir = $hasCacheFile.'_data/';
        if (!file_exists($hasCacheFile))
        {
            //not file cache   
            $boolRs = TRUE;
            if ( !is_dir($cacheDir) || !file_exists($cacheDir) )
            {
                $boolRs = @mkdir($cacheDir,0777);                
            }
            
            if ($boolRs)
            {
                @touch($hasCacheFile);
                $lines = file($ipFileName); 
                if ($lines && is_array($lines))
                {
                    $lines = array_filter($lines);
                    foreach ($lines as $line)
                    {
                        $line = trim($line);
                        @touch($cacheDir.md5($line));
                    }
                }
            }
        }
        if ( file_exists($cacheDir.md5($ip)) )
        {
            return TRUE;
        }*/
        if (file_exists($ipFileName))
        {
            $lines = file($ipFileName);            
            //echo $ip;
            //print_r($lines);
            if ($lines && is_array($lines))
            {
                $lines = array_filter($lines);
                $lines = array_map('trim',$lines);
                //print_r($lines);
                if (in_array($ip,$lines))
                {
                    //echo 'in';
                    return TRUE;
                }
            }
        }
        return FALSE;
    }
}
