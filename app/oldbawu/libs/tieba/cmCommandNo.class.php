<?php
/**
 * CM����ż���
 * <AUTHOR>
 * @since 2009-08-27
 * @package forum-manager
 *
 */
class cmCommandNo
{
    const PostCommit				= 0;		// Post�ύ		
	const ThreadCommit 			= 1;		// Post�ύ		
	const ForumCommit				= 2;		// Post�ύ		
 	const NewForumCommit			= 3;		//�½���������󷢹��ܵ������
    /**
     * ɾ������
     *
     */
    const MASK_DELETE = 12;
    
    const MaskFilter				= 10;		// ��ǹ���
	const CancelMaskFilter		= 11; 		// ��ǹ��˻ָ�
	//MaskDelete				= 12,		// ���ɾ��
	const CancelMaskDelete		= 13;		// ���ɾ���ָ�
	const RealDelete				= 14;		// ����ɾ��
	const MaskRelease				= 15;		// ��Ƿ���
	const CancelMaskRelease 		= 16;		// ȡ����Ƿ���
        //��Ʒ���
    const MaskGood = 17;
    
    const CancelMaskGood = 18;
    //�ö�
    const MaskTop = 25;
    
    const CancelMaskTop = 26;
    //��Ʒ�������
    const DelGoodClass = 35;
    
    const ResortGoodClass = 36;
    
    const AddGoodClass = 37;
    
    const ReNameGoodClass = 38;
    const FrontCancelMaskDelete	= 39;		//ǰ̨��ǻָ����ӣ���postcm��ת��ΪCancelMaskDelete)
    //���÷����
    const DirApplySecClass = 263;
    
    //�����صģ�����Ա
    const FilterUser				= 202;		// �û�����
	const CancelFilterUser 		= 203;		// ȡ���û�����
	const FilterIP				= 204;		// IP����
	const CancelFilterIP			= 205;		// ȡ��IP����
    //������   ������
    const FilterForumUser         = 213;      // �û�����
	const CancelFilterForumUser   = 214;      // ȡ���û�����
	const FilterForumIP           = 216;      // IP����
	const CancelFilterForumIP     = 217;      // ȡ��IP����
    //ͷ��
    
    //����
    const SetForumManager 		= 200;	// ���ð���
	const DelForumManager 		= 201;		// ȡ������
	
	const ApplicationAdmin		= 222;		// �������
	const SetFriendsList			= 225;
	const AddFriend				= 223;		// �������
	const DelFriend				= 224;		// ɾ������
	const SetFriendSecret			= 226;		// ���ĺ����Ƿ񹫿�
	const SetForumAssistant		= 227;		// ���С����
	const DelForumAssistant		= 228;		// ȡ��С����
	
	const ApplicationResg			= 244;   //����ǳ�
	
	const SetForumPicAdmin		= 2001;	// �������ͼƬ����Ա
	const DelForumPicAdmin		= 2002;		// ɾ������ͼƬ����Ա
	
	const ZcCreatAlbum			= 2003;		//�������
	const ZcDelAlbum  			= 2004;		//ɾ�����
	const ZcEditAlbum				= 2005;		//�༭���
	const ZcSetAlbumCover 		= 2006;		//��������
	const ZcAlbumAddPic			= 2007;		//�ϴ�ͼƬ
	const ZcAlbumDelPic			= 2008;		//ɾ��ͼƬ
	const ZcAlbumEditPic			= 2009;		//�༭ͼƬ
	const ZcAlbumMovPic			= 2010;		//�������
	const ZcAlbumAdunfilPic		= 2011;		//�ָ�ͼƬ
	const ZcStarModTitle			= 2012;		//�༭����
	const ZcStarAddUser			= 2013;		//��������ó�Ա
	const ZcStarDelUser			= 2014;		//ɾ�������ó�Ա
	const ZcStarSetTop			= 2015;		//��Ա�ö�
	const ZcStarEditUser			= 2016;		//�༭��Ա
	const ZcStarFilUser			= 2017;		//����Ա���������
	const ZcStarUnfilUser			= 2018;		//ȡ����Ա������	
	const ZcStarDelTop            = 2019;     //��Աȡ���ö�
	const ZCVideoUpdate			= 2020;		//�ϴ���Ƶ
	const ZCVideoDelete			= 2021;		//ɾ����Ƶ
	const ZCVideoEdit				= 2022;		//�༭��Ƶ
	const ZCVideoRecover			= 2023;		//�ָ���Ƶ
	const ZCVAlbumCreat			= 2024;		//����ר��
	const ZCVAlbumDel				= 2025;		//ɾ��ר��
	const ZCVAlbumEdit			= 2026;		//�༭ר��
	const ZCAddCalendarEvent 		= 2027;		//��������
	const ZCDelCalendarEvent 		= 2028;		//ɾ�������	
	const ZCModCalendarEvent 		= 2029;		//�޸������
	const ZCForumAddCalendar 		= 2030;		//�������
	const ZCForumDelCalendar 		= 2031;		//ɾ������
	const ZCForumModCalendar 		= 2032;		//�޸�������
	
	const ZCSetForumStyle 		= 2033;		//���÷��
 	const ZCSetHeadLine 			= 2034; 	//����ͷ��
 	const ZCOpenHeadLine 			= 2035;		//��ͨͷ��
 	const ZCCloseHeadLine 		= 2036;		//�ر�ͷ��������Ϊδ��ͨ״̬��
 	const ZCDispHeadLineUser 		= 2037;		//��������ͷ��չʾ״̬
 	const ZCUnDispHeadLineUser 	= 2038;		//��������ͷ����չʾ״̬
 	const ZCDispHeadLineAdmin 	= 2039;		//����Ա���ͨ��
 	const ZCUnDispHeadLineAdmin 	= 2040;		//����Ա��˲�ͨ��
 	
 	const MGR_AUDIT = 2102;//��������
	
    const SetForumVideoAdmin		= 2601;		//���������Ƶ����Ա
	const DelForumVideoAdmin		= 2602;		//ɾ��������Ƶ����Ա
	const SetForumMember = 2603;				//���ӻ�Ա(�������ߵ���Ա��
	const DelForumMember = 2604;				//ɾ����Ա���ӻ�Ա�б�ɾ����
	const SetForumBlackByName = 2605;//ֱ������������������û��������������
	const DelForumMemberByName = 2614;		//ֱ�Ӹ����û���ɾ���û�
	const SetForumBlackFromApplier = 2615;////�����������������
	const DelForumMemberSelf = 2624;			//��Ա�˳�����
	const SetForumBlackFromMember = 2625;		//�ӻ�Ա��������������ҳ�����ӣ�
	const DelForumBlacklist = 2606;		//�Ӻ������Ƴ�
	const SetForumApplier = 2607;				//����������(�û�������룩
	const DelFourmApplier = 2608;				//��ȥ�����ߣ��������б��Ƴ���
	const ModDoorValue = 2613;					//�޸İ��ż�ֵ
	
	const SetForumPublication = 2634;//���ðɿ�С��	
	const GetForumPublication = 2635;//ȡ���ɿ�С��	
	
	const SetForumPublicationEditor     = 2636;//���ðɿ�����
	const DelForumPublicationEditor     = 2637;      //ȡ���ɿ�����

	const SetForumDaquan = 2638;//��ȫС��
	const DelForumDaquan = 2639;//ȡ����ȫС��

	const SetForumDaquanEditor       = 2640; //���ô�ȫ����
	const DelForumDaquanEditor       = 2641; //ȡ����ȫ����

	const SetForumDiskEditor  = 2660;//��������С��
	const DelForumDiskEditor  = 2661;//ȡ������С��

	const SetFds = 3150;
	const DelFds = 3151;

	const SetJzwFds = 3152;
	const DelJzwFds = 3153;
	
	//�°�����
	const SetBlackList = 3402;
	const DelBlackList = 3403;
	
	const addRecommandUser = 34200;
	const setRecommandUser = 34201;
	const delRecommandUser = 34202;
	
	//�������
	const delDiskClassify    = 3603;
        const setDiskClassify    = 3605;
        const createDiskClassify = 3602;
        const renameDiskClassify = 3604;

	const delDiskThread   =12;
        const recDiskThread   =39;


	
    public static $name2cmd = array();
    /**
     * ����
     *
     * @var ����
     */
    public static $name = array();
    /**
     * ��ȡ����ŵ�����
     *
     * @param unknown_type $cmd
     * @return unknown
     */
    public static function getName($cmd)
    {
        if (isset(self::$name[$cmd]))
        {
            return self::$name[$cmd];
        }
        return FALSE;
    }
    
    public static function getCmd($name)
    {
        if (isset(self::$name2cmd[$name]))
        {
            return self::$name2cmd[$name];
        }
        return NULL;
    }
}
cmCommandNo::$name2cmd = array(
    'add_good'=>cmCommandNo::AddGoodClass,
    'edit_good'=>cmCommandNo::ReNameGoodClass,
    'del_good'=>cmCommandNo::DelGoodClass,
    'set_good_sort'=>cmCommandNo::ResortGoodClass,
    //dir
    'set_forum_dir'=>cmCommandNo::DirApplySecClass,
    //�����ְ
    'apply_resign'=>cmCommandNo::ApplicationResg,
    //�������
    'remove_sub_admin'=>cmCommandNo::DelForumAssistant,
    'remove_pic_admin'=>cmCommandNo::DelForumPicAdmin,
    'remove_video_admin'=>cmCommandNo::DelForumVideoAdmin,
	'add_sub_admin'=>cmCommandNo::SetForumAssistant,
    'add_pic_admin'=>cmCommandNo::SetForumPicAdmin,
    'add_video_admin'=>cmCommandNo::SetForumVideoAdmin,
	'add_bakan_admin' => cmCommandNo::SetForumPublication,
	'remove_bakan_admin' => cmCommandNo::GetForumPublication,
	'add_bakan_editor'	 => cmCommandNo::SetForumPublicationEditor,
    'remove_bakan_editor'=> cmCommandNo::DelForumPublicationEditor,

    'add_daquan_admin' => cmCommandNo::SetForumDaquanEditor,
    'remove_daquan_admin' => cmCommandNo::DelForumDaquanEditor,
    'add_daquan_editor'  => cmCommandNo::SetForumDaquan,
    'remove_daquan_editor'=> cmCommandNo::DelForumDaquan,
     //����С�����
    'add_disk_admin' => cmCommandNo::SetForumDiskEditor, 
    'remove_disk_admin'=> cmCommandNo::DelForumDiskEditor,
   


    //����û����
    'un_ban_user'=>cmCommandNo::CancelFilterForumUser,
    'un_ban_ip'=>cmCommandNo::CancelFilterForumIP,
    'filter_forum_user'=>cmCommandNo::FilterForumUser,
    'filter_forum_ip'=>cmCommandNo::FilterForumIP,
    //ɾ���ָ�
    //'un_delete_thread'=>cmCommandNo::FrontCancelMaskDelete,
    //'un_delete_post'=>cmCommandNo::FrontCancelMaskDelete,
	//��������
	'set_fds' => cmCommandNo::SetFds,
	'del_fds' => cmCommandNo::DelFds,
	//���ɾ�ֹ��
	'set_fds_jzw' => cmCommandNo::SetJzwFds,
	'del_fds_jzw' => cmCommandNo::DelJzwFds,
	
	'add_recommand_user' => cmCommandNo::addRecommandUser ,
	'set_recommand_user' => cmCommandNo::setRecommandUser ,
	'del_recommand_user' => cmCommandNo::delRecommandUser ,

	//�������
	'delc'=> cmCommandNo::delDiskClassify ,
        'setc'=> cmCommandNo::setDiskClassify,
        'createc'=> cmCommandNo::createDiskClassify,
        'renamec'=> cmCommandNo::renameDiskClassify,
	'delete'=> cmCommandNo::delDiskThread,
	'recover'=> cmCommandNo::recDiskThread,

	
);

cmCommandNo::$name = array(
    cmCommandNo::MASK_DELETE => 'delete_thread',
);
