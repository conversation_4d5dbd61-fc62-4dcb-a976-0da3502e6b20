<?php
/**
 * ���ɵ�Ȩ����ϵ��������permConfig.class.php���ܶ��ĵĴ��롣
 * <AUTHOR>
 * @since 2009-09-01
 * @package forum-manager
 *
 */
class TiebaPerm
{
    /**
     * ��ȡ�û���Ȩ��λ
     *
     * @param �û�ID $uid
     * @param ��ID $fid
     * @param ���� $fname
     * @return false/perm
     */
    public static function getPerm($uid=0,$fid=0,$fname='')
    {
        if (empty($uid))
        {
            //get login uid
            $uid = BawuSession::getLoginUid();
        }
        
        $arrGlobalRole = PermServer::getUserPerm($uid, 0, NULL, FALSE);
        if (sizeof($arrGlobalRole) == 0) {
			$globalRole = FALSE;
		} else {
			$globalRole = TRUE;
//			$arrGlobalRole = 
		}
        //$globalRole = memberRpc::getUserGlobalPerm($uid);
        if (FALSE === $globalRole)
        {
            //get forum role
            if (!empty($fid) && !empty($fname))
            {
            	if (empty($fid)){
            		$fid = $fname;
            	}
            	$forumRole = PermServer::getUserPerm($uid, $fid, NULL, FALSE);
                //$forumRole = memberRpc::getUserPermInForumWithFname($uid,$fid,$fname);
                return $forumRole;
            }
        }
        else
        {
            return $globalRole;
        }
        return FALSE;
    }
    /**
     * ����ύ�û�Ȩ��
     *
     * @param ����� $cm
     * @param ��ɫ $role
     * @return true/false
     */
    public static function checkCmPerm($cm,$perm)
    {
        return self::_checkPermDetail($perm,self::_getCmRole($cm));
    }
    /**
     * �������û���Ȩ��
     *
     * @param URL·���� $router
     * @param Ȩ��λ $perm
     * @return true/false
     */
    public static function checkBrowsePerm($router,$perm)
    {
        return self::_checkPermDetail($perm,self::_getBrowseRole($router));
    }
    
    public static function isOldMember($uid,$fid)
    {
        if (empty($uid) || empty($fid))
        {
            return FALSE;
        }
        $enterTime = memberRpc::getMemberEnterTime($uid,$fid,memberRpc::PERM_FORUM_MEMBER);
        $nowTime = Phpbean_Util::time();
        if ( $nowTime - $enterTime > Bawu_Global::OLD_MEMBER_ENTERTIME_PLUS )
        {
            return TRUE;
        }
        return FALSE;
    }
    
    public static function isManagerTeam($perm)
    {
    	$intRoleID = memberRpc::PERM_FORUM_MANAGER_TEAM;
        return self::_checkRolePerm($intRoleID, $perm);
        
    	/*
        if ( (memberRpc::PERM_FORUM_ASSIST & $perm) ||
            (memberRpc::PERM_FORUM_PICADMIN & $perm) ||
            (memberRpc::PERM_FORUM_VIDEOADMIN & $perm) )
        {
            return TRUE;
        }
        return FALSE;
        */
    }

	public static function isAssist($perm)
	{
		$intRoleID = memberRpc::PERM_FORUM_ASSIST;
        return self::_checkRolePerm($intRoleID, $perm);
        
        /*if (memberRpc::PERM_FORUM_ASSIST & $perm) {
			return true;
		}
		return false;
		*/
	}
    
    public static function isManagerOrBaZhu($perm)
    {
        return (self::isBazhu($perm) || self::isManager($perm));
        
        /*if ( (memberRpc::PERM_ROOT_MANAGER & $perm) || 
            (memberRpc::PERM_ROOT_PM & $perm )||
            (memberRpc::PERM_FORUM_MANAGER & $perm) )
        {
            return TRUE;
        }
        return FALSE;
        */
    }

    public static function isBaZhu($perm)
    {
    	$intRoleID = memberRpc::PERM_FORUM_MANAGER;
    	return self::_checkRolePerm($intRoleID, $perm);
    	/*
        if ( memberRpc::PERM_FORUM_MANAGER & $perm )
        {
            return TRUE;
        }
        return FALSE;
        */
    }


    /**
     * �Ƿ��ǹ���Ա
     *
     * @param Ȩ�� $perm
     * @return true/false
     */
    public static function isManager($perm)
    {
        $intRoleID = memberRpc::PERM_ROOT_MANAGER;
        return self::_checkRolePerm($intRoleID, $perm);
        
        /*if ( (memberRpc::PERM_ROOT_MANAGER & $perm) || (memberRpc::PERM_ROOT_PM&$perm) )
        {
            return TRUE;
        }
        return FALSE;
        */
    }
    /**
     * �Ƿ��ǻ�Ա
     *
     * @param Ȩ��λ $perm
     * @return true/false
     */
    public static function isMember($perm)
    {
        $intRoleID = memberRpc::PERM_FORUM_MEMBER;
        return self::_checkRolePerm($intRoleID, $perm);
        
        /*if (memberRpc::PERM_FORUM_MEMBER & $perm)
        {
            return TRUE;
        }
        return FALSE;
        */
    }
    /**
     * ��ȡ��ɫ��Ȩ��λ
     *
     * @param ��ɫ $role
     * @return Ȩ��λ
     */
    public static function getPermByRole($role)
    {
        if (isset(PermConfig::$role2perm[$role]))
        {
            return PermConfig::$role2perm[$role];
        }
        return NULL;
    }
    
    private static function _checkPermDetail($perm, $allPerms)
    {
    	$fid = 0;
        if (isset(ThreadData::$request['forum_id']))
        {
            $fid = ThreadData::getRequest('forum_id');
        }
        
        $_fdirName = btxRpc::getForumDirInfo($fid);
        if ($_fdirName['fdir_level_one'] != '��������'){
        	$_isArea = FALSE;
        } else {
        	$_isArea = TRUE;
        }
        $uid = BawuSession::getLoginUid();
        //$member_id = PermConfig::$roles['member'];		// ��Ա��ɫID
        //$member_id_arr = PermServer::getForumRoleByName($fid, 'member', TRUE);
        //$member_id = $member_id_arr['role_info']['role_id'];
        $member_id = PermConfig2::$roles['FORUM_MEMBER'];
        $intUserRole = PermServer::getUserHasRole($fid, $uid, $member_id, TRUE);
        if ($intUserRole == 1 && $_isArea){
        	return TRUE;
        } else {
        	if (PermServer::checkUserPermInPermArray($perm, $allPerms) !== FALSE){
        		return TRUE;
        	}
        }
        return FALSE;
    }
    /**
     * ˽�з��������ݽ�ɫ���飬���$perm�Ƿ�ӵ������һ����ɫ
     *
     * @param Ȩ��λ $perm
     * @param ��ɫ���� $roleArray
     * @return true/false
     */
    /*
    private static function _checkPermDetail($perm,$roleArray)
    {
        $fid = 0;
        if (isset(ThreadData::$request['forum_id']))
        {
            $fid = ThreadData::getRequest('forum_id');
        }
        
        $_fdirName = btxRpc::getForumDirInfo($fid);
        if ($_fdirName['fdir_level_one'] != '��������'){
        	$_isArea = FALSE;
        } else {
        	$_isArea = TRUE;
        }
        
        if (empty($roleArray))return FALSE;
        foreach ($roleArray as $role)
        {
        	if ($role == 'member' && !$_isArea) {
        		continue;
        	}	
            $_perm = self::getPermByRole($role);
            if ($_perm && ($perm&$_perm) )
            {
                return TRUE;
            }
        }
        return FALSE;
    }
    */ 
    /**
     * �������ļ��л�ȡ���Ȩ�޵�����
     *
     * @param ·�� $router
     * @return ��ɫ����
     */    
    private static function _getBrowseRole($router)
    {
        $_arr = array();
        if (isset(PermConfig2::$browseConfig[$router]))
        {
            $_arr[] = PermConfig2::$browseConfig[$router];
        }
        //$_arr[] = PermConfig2::$allBrowserPermRoles;
        return $_arr;
    }
    /**
     * ��ȡһ������Ŷ�Ӧ����Щ��ɫ��Ȩ��
     *
     * @param ����� $cm
     * @return ��ɫ����
     */
    private static function _getCmRole($cm)
    {
        $_arr = array();
        if (isset(PermConfig2::$cmConfig[$cm]))
        {
            $_arr[] = PermConfig2::$cmConfig[$cm];
        }
        //$_arr[] = PermConfig2::$allCmPermRoles;
        return $_arr;
    }
    
    /**
     * ����ģ�飺�鿴���Ȩ�޲���֤�û�Ȩ��
     */
    private static function _checkRolePerm($intRoleID, $perm){
		$intForumID = 0;
        if (isset(ThreadData::$request['forum_id']))
        {
            $intForumID = ThreadData::getRequest('forum_id');
        }
        $intIP = NULL;
        $arrPerm = PermServer::getPermByRole($intRoleID, $intForumID, $intIP, 0);
        $intPerm = PermServer::perm2decbin($arrPerm);
        
        if ($perm & $intPerm){
        	return TRUE;
        } else {
        	return FALSE;
        }
    }
}
