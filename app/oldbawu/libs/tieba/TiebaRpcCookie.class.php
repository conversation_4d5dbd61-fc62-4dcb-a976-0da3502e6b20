<?php
/**
 * �����ϵ�COOKIE�ֶθ�POSTCM������ȡ��
 * <AUTHOR>
 * @since 2009-08-28
 * @package forum-manager
 * todo 
 */
class TiebaRpcCookie
{
    const COOKIE_HEADER = 'FRUSERNAME:';
    
    const COOKIE_SIGN_FLAG = 'SIgnFoRum:';
    
    const COOKIE_USERID_FLAG = 'USERid:';
    
    const COOKIE_BD = 'BD:';
    /**
     * ����һ��cookie�ֶθ�postcm����Ҫ��psotcm���н�������Ӧ��$username��$uid�ֶ�
     *
     * @param �û��� $username
     * @param �û�ID $uid
     * @return cookie�ַ���
     */
    public static function buildOldVersionCookie($username,$uid)
    {
        return self::COOKIE_HEADER . $username . self::COOKIE_SIGN_FLAG . 
            self::COOKIE_USERID_FLAG . $uid . self::COOKIE_BD;
    }
}