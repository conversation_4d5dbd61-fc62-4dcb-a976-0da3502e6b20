<?php
/**
 * URL token���ܵ�ʵ�֣���bawuʹ��
 * <AUTHOR>
 * @since 2009-08-31
 * @package forum-manager
 *
 */
class BawuUrlToken
{
    const IP_ENCODE_SECURE_KEY = 'IPKEY!@#$$%^&';
    /**
     * ����
     * @param string $cm
     * @param int $tid
     * @param int $pid
     * @return string tbs
	*/
    public static function encode($cm,$tid=0,$pid=0)
    {
        $uid = BawuSession::getLoginUid();
        $word = ThreadData::getRequest('forum_name','gbk',FALSE);
        $uname = ThreadData::getRequest('op_uname','gbk',FALSE);
        $fid = ThreadData::getRequest('forum_id','gbk',FALSE);
        return TiebaUrlToken::generate($cm,$uid,$word,$uname,$fid,$tid,$pid);
    }
    
    public static function encodeIp($intIp)
    {
        return md5($intIp . self::IP_ENCODE_SECURE_KEY);                
    }
}