<?php
/**
 * ��־�����Դ�ķ�װ
 * <AUTHOR>
 * @since 2009-08-31
 * @package forum-manager
 *
 */
class MemoResource
{
	const CMD_NO_TOP = 25;
	const CMD_NO_CANCEL_TOP = 26;
	const CMD_NO_GOOD = 17;
	const CMD_NO_CANCEL_GOOD = 18;
	const CMD_NO_DEL = 12;
	const CMD_NO_CANCEL_DEL = 13;
    /**
     * ��������ʱ����ֶΣ�˽�з���
	*/
    private static function _buildSearchKey()
    {
        $searchType = BawuRequest::getSearchType();
        $searchKeyword = BawuRequest::getKeyword();
        $searchKey = array();
        if (empty($searchKeyword))
        {
            return $searchKey;
        }
        switch ($searchType)
        {
            case 'operate_time':
                //split
                list($begin,$end) = explode('/',$searchKeyword);
                $begin = Phpbean_Filter::int($begin);
                $end = Phpbean_Filter::int($end);
                if ($end<$begin)
                {
                    $end = $begin;
                }
                if ( !empty($begin) && !empty($end) )
                {
                    $searchKey = array(
                        'start'=>$begin,
                        'end'=>$end,
                    );
                }
                break;
            case 'operator':
                if (!empty($searchKeyword))
                {
                    $searchKey = array(
                        'manager'=>$searchKeyword,
                    );
                }
                break;
            case 'user_name':
                $searchKey = array(
                    'user'=>$searchKeyword,
                );
                break;
            case 'event_name':
                $searchKey = array(
                    'title'=>$searchKeyword,
                );
                break;
            case 'album_name':
                $searchKey = array(
                    'title'=>$searchKeyword,
                );
                break;
            default:
                $searchKey = array(
                    'title'=>$searchKeyword,
                );
                break;
        }
        return $searchKey;
    }
    /**
     * ����ά���ΰ���������Դ
	*/
    public static function getSafe()
    {
        $fid = ThreadData::getRequest('forum_id');
        $nowPage = BawuRequest::getNowPage();
        $pageSize = BawuRequest::getPageSize(20);
        //$offset = ($nowPage-1)*$pageSize;
        $offset = $nowPage - 1 ;
        //���������,todo               
        $searchKey = self::_buildSearchKey();
        //$source = memoRpc::getSafe($fid,$offset,$pageSize,$searchKey);
        $_totalPage = 0;
        $_total = 0;
        if ($source['total']>0)
        {
            $_total = $source['total'];
            $_totalPage = ceil($_total/$pageSize);
        }
        return array(
            'cur_page' => $nowPage,
    		'total_page' => $_totalPage,
    		'total_num' => $_total,
            'server_time'=> Phpbean_Util::time(),
            'data'=>$source['data'],
        );
    }
    /**
     * ���췢չ�����������Դ
	*/
    public static function getDevelop()
    {
        $fid = ThreadData::getRequest('forum_id');
        $nowPage = BawuRequest::getNowPage();
        $pageSize = BawuRequest::getPageSize(20);
        //$offset = ($nowPage-1)*$pageSize;
        $offset = $nowPage - 1 ;
        //���������,todo
        $searchKey = self::_buildSearchKey();//var_dump($searchKey);
        //$source = memoRpc::getDevelop($fid,$offset,$pageSize,$searchKey);
        $_totalPage = 0;
        $_total = 0;
        if ($source['total']>0)
        {
            $_total = $source['total'];
            $_totalPage = ceil($_total/$pageSize);
        }
        return array(
            'cur_page' => $nowPage,
    		'total_page' => $_totalPage,
    		'total_num' => $_total,
            'server_time'=> Phpbean_Util::time(),
            'data'=>$source['data'],
        );
    }
    /**
     * ����������־��������Դ
	*/
    public static function getCal()
    {
        $fid = ThreadData::getRequest('forum_id');
        $nowPage = BawuRequest::getNowPage();
        $pageSize = BawuRequest::getPageSize(5);
        //$offset = ($nowPage-1)*$pageSize;
        $offset = $nowPage - 1 ;
        //���������,todo
        $searchKey = self::_buildSearchKey();
        //$source = memoRpc::getCal($fid,$offset,$pageSize,$searchKey);
        $_totalPage = 0;
        $_total = 0;
        if ($source['total']>0)
        {
            $_total = $source['total'];
            $_totalPage = ceil($_total/$pageSize);
        }
        return array(
            'cur_page' => $nowPage,
    		'total_page' => $_totalPage,
    		'total_num' => $_total,
            'data'=>$source['data'],
        );
    }
    /**
     * ������־���ܵ�������Դ
	*/
    public static function getTotal()
    {
        $fid = ThreadData::getRequest('forum_id');
        $nowPage = BawuRequest::getNowPage();
        $pageSize = BawuRequest::getPageSize(5);
        //$offset = ($nowPage-1)*$pageSize;
        $offset = $nowPage - 1;
        //���������,todo
        $searchKey = self::_buildSearchKey();
        //$source = memoRpc::getTotal($fid,$offset,$pageSize,$searchKey);
        $_totalPage = 0;
        $_total = 0;
        if ($source['total']>0)
        {
            $_total = $source['total'];
            $_totalPage = ceil($_total/$pageSize);
        }
        return array(
            'cur_page' => $nowPage,
    		'total_page' => $_totalPage,
    		'total_num' => $_total,
            'data'=>$source['data'],
        );
    }
    /**
     * ������Ƶ��־��������Դ
	*/
    public static function getVedio()
    {
        $fid = ThreadData::getRequest('forum_id');
        $nowPage = BawuRequest::getNowPage();
        $pageSize = BawuRequest::getPageSize(5);
        //$offset = ($nowPage-1)*$pageSize;
        $offset = $nowPage - 1 ;
        //���������,todo
        $searchKey = self::_buildSearchKey();
        //$source = memoRpc::getVideo($fid,$offset,$pageSize,$searchKey);
        $_totalPage = 0;
        $_total = 0;
        if ($source['total']>0)
        {
            $_total = $source['total'];
            $_totalPage = ceil($_total/$pageSize);
        }
        return array(
            'cur_page' => $nowPage,
    		'total_page' => $_totalPage,
    		'total_num' => $_total,
            'data'=>$source['data'],
        );
    }  
    /**
     * �����Ա��־��������Դ
	*/
    public static function getMmeber()
    {
        $fid = ThreadData::getRequest('forum_id');
        $nowPage = BawuRequest::getNowPage();
        $pageSize = BawuRequest::getPageSize(5);
        //$offset = ($nowPage-1)*$pageSize;
        $offset = $nowPage - 1 ;
        //���������,todo
        $searchKey = self::_buildSearchKey();
        //$source = memoRpc::getMmeber($fid,$offset,$pageSize,$searchKey);
		 $source = memoRpc::getMemberNew($fid,$offset,$pageSize,$searchKey);
        $_totalPage = 0;
        $_total = 0;
        if ($source['total']>0)
        {
            $_total = $source['total'];
            $_totalPage = ceil($_total/$pageSize);
        }
        return array(
            'cur_page' => $nowPage,
    		'total_page' => $_totalPage,
    		'total_num' => $_total,
            'data'=>$source['data'],
        );
    }
    /**
     * ����ͼƬ��־��������Դ
	*/
    public static function getPic()
    {
        $fid = ThreadData::getRequest('forum_id');
        $nowPage = BawuRequest::getNowPage();
        $pageSize = BawuRequest::getPageSize(5);
        //$offset = ($nowPage-1)*$pageSize;
        $offset = $nowPage - 1 ;
        //���������,todo
        $searchKey = self::_buildSearchKey();
        //$source = memoRpc::getPic($fid,$offset,$pageSize,$searchKey);
        $_totalPage = 0;
        $_total = 0;
        if ($source['total']>0)
        {
            $_total = $source['total'];
            $_totalPage = ceil($_total/$pageSize);
        }
        return array(
            'cur_page' => $nowPage,
    		'total_page' => $_totalPage,
    		'total_num' => $_total,
            'data'=>$source['data'],
        );
    }
    
    /**
     * ���������־��������Դ
	*/
    public static function getAlbum()
    {
        $fid = ThreadData::getRequest('forum_id');
        $nowPage = BawuRequest::getNowPage();
        $pageSize = BawuRequest::getPageSize(5);
        //$offset = ($nowPage-1)*$pageSize;
        $offset = $nowPage - 1 ;
        //���������,todo
        $searchKey = self::_buildSearchKey();
        //$source = memoRpc::getAlbum($fid,$offset,$pageSize,$searchKey);
        $_totalPage = 0;
        $_total = 0;
        if ($source['total']>0)
        {
            $_total = $source['total'];
            $_totalPage = ceil($_total/$pageSize);
        }
        return array(
            'cur_page' => $nowPage,
    		'total_page' => $_totalPage,
    		'total_num' => $_total,
            'data'=>$source['data'],
        );
    }
    /**
     * ��������Ŷ���־��������Դ
	*/
    public static function getTeam()
    {
        $fid = ThreadData::getRequest('forum_id');
        $nowPage = BawuRequest::getNowPage();
        $pageSize = BawuRequest::getPageSize(5);
        //$offset = ($nowPage-1)*$pageSize;
        $offset = $nowPage - 1 ;
        //���������,todo
        $searchKey = self::_buildSearchKey();
        //$source = memoRpc::getTeam($fid,$offset,$pageSize,$searchKey);
		$source = memoRpc::getTeamNew($fid,$offset,$pageSize,$searchKey);
        $_totalPage = 0;
        $_total = 0;
        if ($source['total']>0)
        {
            $_total = $source['total'];
            $_totalPage = ceil($_total/$pageSize);
        }
        return array(
            'cur_page' => $nowPage,
    		'total_page' => $_totalPage,
    		'total_num' => $_total,
            'data'=>$source['data'],
        );
    }
    /**
     * ��������־��������Դ
	*/
    public static function getFilter()
    {
        $fid = ThreadData::getRequest('forum_id');
        $nowPage = BawuRequest::getNowPage();
        $pageSize = BawuRequest::getPageSize(5);
        //$offset = ($nowPage-1)*$pageSize;
        $offset = $nowPage - 1 ;
        //���������,todo
        $searchKey = self::_buildSearchKey();
        //$source = memoRpc::getFilter($fid,$offset,$pageSize,$searchKey);
		$source = memoRpc::getFilterNew($fid,$offset,$pageSize,$searchKey);
        $_totalPage = 0;
        $_total = 0;
        if ($source['total']>0)
        {
            $_total = $source['total'];
            $_totalPage = ceil($_total/$pageSize);
        }
        return array(
            'cur_page' => $nowPage,
    		'total_page' => $_totalPage,
    		'total_num' => $_total,
            'tbs_ip'=> BawuUrlToken::encode(cmCommandNo::CancelFilterForumIP),
            'tbs_user'=>BawuUrlToken::encode(cmCommandNo::CancelFilterForumUser),
            'data'=>$source['data'],
        );
    }
    
    /**
     * �����ö���־��������Դ
	*/
    public static function getTop()
    {
        //���������,todo
        $searchKey = self::_buildSearchKey();
        return self::_getTopOrDelOrGood2(self::CMD_NO_TOP,$searchKey);
    }

    public static function getCancelTop()
    {
        //���������,todo
        $searchKey = self::_buildSearchKey();
        return self::_getTopOrDelOrGood2(self::CMD_NO_CANCEL_TOP,$searchKey);
    }
    /**
     * ����ɾ����־��������Դ
	*/
    public static function getDel()
    {
        //���������,todo
        $searchKey = self::_buildSearchKey();
        return self::_getTopOrDelOrGood2(self::CMD_NO_DEL,$searchKey);
    }

    public static function getCancelDel()
    {
        //���������,todo
        $searchKey = self::_buildSearchKey();
        return self::_getTopOrDelOrGood2(self::CMD_NO_CANCEL_DEL,$searchKey);
    }
    /**
     * ���쾫����־��������Դ
	*/
    public static function getGood()
    {
        //���������,todo
        $searchKey = self::_buildSearchKey();
        return self::_getTopOrDelOrGood2(self::CMD_NO_GOOD,$searchKey);
    }

    public static function getCancelGood()
    {
        //���������,todo
        $searchKey = self::_buildSearchKey();
        return self::_getTopOrDelOrGood2(self::CMD_NO_CANCEL_GOOD,$searchKey);
    }
     private static function _getTopOrDelOrGood2($type,$searchKey)
    {
        $fid = ThreadData::getRequest('forum_id');
        $nowPage = BawuRequest::getNowPage();
        $pageSize = BawuRequest::getPageSize(5);
        //$offset = ($nowPage-1)*$pageSize;
        $offset = $nowPage - 1 ;
        //$source = memoRpc::getTopOrDelOrGood($fid,$type,$offset,$pageSize,$searchKey);
		$source = memoRpc::getTopOrDelOrGoodNew2($fid,$type,$offset,$pageSize,$searchKey);
        //var_dump($source);
        $_totalPage = 0;
        $_total = 0;
        if ($source['total']>0)
        {
            $_total = $source['total'];
            $_totalPage = ceil($_total/$pageSize);
        }
        return array(
            'cur_page' => $nowPage,
    		'total_page' => $_totalPage,
    		'total_num' => $_total,
            'data'=>$source['data'],
        );
    }
   
    private static function _getTopOrDelOrGood($type,$searchKey)
    {
        $fid = ThreadData::getRequest('forum_id');
        $nowPage = BawuRequest::getNowPage();
        $pageSize = BawuRequest::getPageSize(5);
        //$offset = ($nowPage-1)*$pageSize;
        $offset = $nowPage - 1 ;
        //$source = memoRpc::getTopOrDelOrGood($fid,$type,$offset,$pageSize,$searchKey);
		$source = memoRpc::getTopOrDelOrGoodNew($fid,$type,$offset,$pageSize,$searchKey);
        //var_dump($source);
        $_totalPage = 0;
        $_total = 0;
        if ($source['total']>0)
        {
            $_total = $source['total'];
            $_totalPage = ceil($_total/$pageSize);
        }
        return array(
            'cur_page' => $nowPage,
    		'total_page' => $_totalPage,
    		'total_num' => $_total,
            'data'=>$source['data'],
        );
    }
}
