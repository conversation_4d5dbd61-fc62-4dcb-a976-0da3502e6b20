<?php
/**
 * ��ȡ�ⲿ���ݣ�BAD
 * <AUTHOR>
 * @since 2009-08-27
 * @package forum-manager
 *
 */
class BawuRequest
{
    /**
     * ��ȡ��ǰҳ��
	*/
    public static function getNowPage()
    {
        return Phpbean_Filter::int(BawuRequest::get(Bawu_Global::INPUT_NOWPAGE_KEY,1));
    }
    /**
     * ��ȡ������ʽ
	*/
    public static function getSearchType()
    {
        return Phpbean_Filter::noTags(BawuRequest::get(Bawu_Global::INPUT_SEARCH_TYPE_KYE,''));
    }
    /**
     * ��ȡ�����ؼ���
	*/
    public static function getKeyword()
    {
        $word = Phpbean_Filter::noTags(BawuRequest::get(Bawu_Global::INPUT_SEARCH_KEYWORD_KYE,''));
        $word = TiebaCharset::getGbk($word);
        return $word;
    }
    /**
     * ��ȡҳ���С
	*/
    public static function getPageSize($defaultSize=0)
    {
        if ($defaultSize<=0)$defaultSize = Bawu_Global::DEFAULT_PAGE_SIZE;
        $pageSize = BawuRequest::get(Bawu_Global::INPUT_PAGESIZE_KEY,$defaultSize);
        //max check
        if ($pageSize>Bawu_Global::MAX_PAGE_SIZE)
        {
            return Bawu_Global::MAX_PAGE_SIZE;
        }
        return Phpbean_Filter::int($pageSize);
    }
    /**
     * ͨ��GBK��ʽ��ȡ$_GET,$_POST,_COOKIE�е�����
	*/
    public static function getGbk($key,$defaultValue=NULL)
    {
        $value = self::get($key,$defaultValue);
        if (NULL != $value)
        {
            $value = TiebaCharset::getGbk($value);
        }
        return $value;
    }
    /**
     * ��ȡ$_GET,$_POST,_COOKIE�е�����
	*/
    public static function get($key,$defaultValue=NULL)
    {
        $value = self::_get($key,$_REQUEST,$defaultValue);
        if (NULL === $value)
        {
            Bawu_Log::warning('BawuRequest::getRequest failure!key['.$key.'] is NULL');
        }
        return $value;
    }
    /**
     * ��ȡ$_GET�е�����
	*/
    public static function getGet($key,$defaultValue=NULL)
    {
        $value = self::_get($key,$_GET,$defaultValue);
        if (NULL === $value)
        {
            Bawu_Log::warning('BawuRequest::getGet failure!key['.$key.'] is NULL');
        }
        return $value;
    }
    /**
     * ��ȡ$_POST�е�����
	*/
    public static function getPost($key,$defaultValue=NULL)
    {
        $value = self::_get($key,$_POST,$defaultValue);
        if (NULL === $value)
        {
            Bawu_Log::warning('BawuRequest::getPost failure!key['.$key.'] is NULL');
        }
        return $value;
    }
    /**
     * ��ȡ_COOKIE�е�����
	*/
    public static function getCookie($key,$defaultValue=NULL)
    {
        $value = self::_get($key,$_COOKIE,$defaultValue);
        if (NULL === $value)
        {
            Bawu_Log::warning('BawuRequest::getCookie failure!key['.$key.'] is NULL');
        }
        return $value;
    }
    
    private static function _get($key,$array,$defaultValue)
    {
        if (isset($array[$key]))
        {
            return $array[$key];
        }
        return $defaultValue;
    }
}