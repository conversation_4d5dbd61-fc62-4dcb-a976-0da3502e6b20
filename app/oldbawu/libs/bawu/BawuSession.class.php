<?php
/**
 * ���������û�session��صĲ���
 * <AUTHOR>
 * @since 2009-08-31
 * @package forum-manager
 *
 */
class BawuSession
{
    /**
     * �Ƿ������session��RPC����
     *
     * @var unknown_type
     */
    private static $_hasRpcCall = FALSE;

    /**
     * ���������ݽ��
     *
     * @var unknown_type
     */
    private static $_rpcCallRs = array();
    
    /**
     * ��½�û�����ϸ��Ϣ
     *
     * @var unknown_type
     */
    private static $_userinfo = array();
     
     /**
     * ��ȡ��½�û����û�email
     *
     * @return UID/FALSE
     */
    public static function getEmail ()
    {
        self::_sessionRpc();
        if ( !empty(self::$_rpcCallRs) && isset(self::$_rpcCallRs['email']) )
        {
            return self::$_rpcCallRs['email'];
        }
        return '';
    }
    /**
     * ��ȡ��½�û����û�mobilephone
     *
     * @return UID/FALSE
     */
    public static function getMobilephone ()
    {
        self::_sessionRpc();
        if ( !empty(self::$_rpcCallRs) && isset(self::$_rpcCallRs['mobilephone']) )
        {
            return self::$_rpcCallRs['mobilephone'];
        }
        return '';
    }
    
    /**
     * ��ȡ��½�û��Ƿ�����username��¼��־
     *
     * @return UID/FALSE
     */
    public static function getNo_un ()
    {
        self::_sessionRpc();
        if ( !empty(self::$_rpcCallRs) && isset(self::$_rpcCallRs['no_un']) )
        {
            return self::$_rpcCallRs['no_un'];
        }
        return 0;
    }
    
    
    /**
     * �û��Ƿ��Ѿ���¼
     *
     * @return TRUE/FALSE
     */
    public static function isLogin()
    {
        self::_sessionRpc();
        if (!empty(self::$_rpcCallRs))
        {
            return TRUE;
        }
        return FALSE;
    }
    /**
     * ��ȡ��½�û����û�ID
     *
     * @return UID/FALSE
     */
    public static function getLoginUid()
    {
        self::_sessionRpc();
        if ( !empty(self::$_rpcCallRs) && isset(self::$_rpcCallRs['uid']) )
        {
            return self::$_rpcCallRs['uid'];
        }
        return FALSE;
    }
    /**
     * ��ȡ�û��ĵ�¼��
     *
     * @return userName/False
     */
    public static function getLoginUname()
    {
        self::_sessionRpc();
        if ( !empty(self::$_rpcCallRs) && isset(self::$_rpcCallRs['un']) )
        {
            return self::$_rpcCallRs['un'];
        }
        return FALSE;
    }
    /**
     * ��ȡ��½�û�����ϸ��Ϣ
     *
     * @return ����
     */
    public static function getLoginUserInfo()
    {
        if (empty(self::$_userinfo))
        {        
            self::_sessionRpc();        
            if (!empty(self::$_rpcCallRs) && isset(self::$_rpcCallRs['uid']))
            {
                self::$_userinfo = PassportUserInfo::getUserInfoByUid(self::$_rpcCallRs['uid']);
            }
        }
        return self::$_userinfo;
    }
    /**
     * ��passport session��������ȡ�û�����
     *
     */
    private static function _sessionRpc()
    {
        if ( FALSE === self::$_hasRpcCall)
        {
            PassportSession::setCallBack(array('BawuSession','checkUrlNotNeedSLogin'));
            self::$_rpcCallRs = PassportSession :: checkUserLogin ();
            self::$_hasRpcCall = TRUE;
        }
    }
    
    public static function checkUrlNotNeedSLogin()
    {
        return TRUE;
    }
}