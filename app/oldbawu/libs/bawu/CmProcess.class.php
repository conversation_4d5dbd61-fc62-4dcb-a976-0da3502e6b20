<?php
/**
 * �ύ�����Ĵ�����
 * <AUTHOR>
 * @since 2009-08-31
 * @package forum-manager
 *
 */
class CmProcess
{
    
    const APPID = 1;
    const TPL = 'tb';
    const Expired_time = 120;
    
    /**
     * �����������Ƿ���GBK�������ͨ�ַ���
     * ���ҵ���FRS�ӿڽ��з������Ƶ��Ƿ������ӵļ��
	*/
    private static function _checkClassName($name)
    {
        $boolRs = TiebaCharset::gbkIsNormalStr($name);
        if (!$boolRs)
        {
            Bawu_Log::warning('gbkIsNormalStr check false,name['.$name.']');
            ThreadData::error(cmErrorNo::DATA_CHECK_ERROR,cmErrorMsg::INPUT_VALUE_INVALID);
            return false;
        }
        $fid = ThreadData::getRequest('forum_id');
        //����frs�ӿڼ��
        //frsRpc::
        if (frsRpc::canAddGoodClassName($fid,$name))
        {
            return TRUE;
        }
        else
        {
            Bawu_Log::warning('canAddGoodClassName check false,name['.$name.']');
            ThreadData::error(cmErrorNo::DATA_CHECK_ERROR,cmErrorMsg::INPUT_VALUE_INVALID);
        }
        return false;
    }
    /**
     * ��Ӿ�Ʒ�������ʱ�򣬼���Ƿ�������
	*/
    public static function checkGoodClassNum()
    {
        $title = ThreadData::getRequest('title');
        $fname = ThreadData::getRequest('forum_name');
        $fid = ThreadData::getRequest('forum_id');
        $rs = frsRpc::getGoodClass($fname, $fid);
        if (count($rs)>=frsRpc::MAX_GOOD_CLASS_NUM)
        {
            ThreadData::error(cmErrorNo::DATA_CHECK_ERROR,cmErrorMsg::ADD_GOOD_CLASS_NUM_OVER);
            return TRUE;
        }
        //����Ƿ��Ѿ�����
        if ( !empty($rs) )
        {
            foreach ($rs as $_item)
            {
                if ($title == $_item['class_name'])
                {
                    ThreadData::error(cmErrorNo::DATA_CHECK_ERROR,cmErrorMsg::GOOD_CLASS_NAME_EXIST);
                    return TRUE;
                }
            }
        }
        //�������Ƿ��������ַ�
        self::_checkClassName($title);
        return TRUE;
    }
    /**
     * �޸ľ�Ʒ�������ʱ�򣬼���Ƿ�����޸�
	*/
    public static function checkGoodClassName()
    {
        $title = ThreadData::getRequest('title');
        $fname = ThreadData::getRequest('forum_name');
        $fid = ThreadData::getRequest('forum_id');
        $boolRs = TiebaCharset::gbkIsNormalStr($title);
        if (!$boolRs)
        {
            Bawu_Log::warning('gbkIsNormalStr check false,name['.$title.']');
            ThreadData::error(cmErrorNo::DATA_CHECK_ERROR,cmErrorMsg::INPUT_VALUE_INVALID);
            return TRUE;
        }

        $rs = frsRpc::getGoodClass($fname, $fid);
        //����Ƿ��Ѿ�����
        if ( !empty($rs) )
        {
            foreach ($rs as $_item)
            {
                if ($title == $_item['class_name'])
                {
                    ThreadData::error(cmErrorNo::DATA_CHECK_ERROR,cmErrorMsg::GOOD_CLASS_NAME_EXIST);
                    return TRUE;
                }
            }
        }
        //����frs�ӿڼ��
        if (frsRpc::canRenameGoodClassName($fid,$title,ThreadData::getRequest('post_id')))
        {
            return TRUE;
        }
        else
        {
            Bawu_Log::warning('canAddGoodClassName check false,name['.$title.']');
            ThreadData::error(cmErrorNo::DATA_CHECK_ERROR,cmErrorMsg::INPUT_VALUE_INVALID);
        }
        return TRUE;
    }
    
    /**
     * ������Ʒ����ʱ�򣬶�classid����pack���
	*/
    private static function _packClassIds($array)
    {
        $num = count($array);
        $rs = '';
	    //pack 0 ȫ��
        //$rs .= pack('I',0);
	    for ($i=0;$i<$num;$i++)
	    {
	        $rs .= pack('I',$array[$i]);
	    }
	    return $rs;
    }
    /**
     * ������Ʒ����ʱ�򣬶�classid����pack���
	*/
    public static function buildDetailWithClassIds()
    {
        $goodIds = ThreadData::getRequest('good_ids');
        if (!empty($goodIds))
        {
            //Bawu_Log::notice('get good_ids [' . $goodIds .']');
            $goodIds = array_filter( explode(',',$goodIds) );
            //pack
            if (!empty($goodIds))
            {
                $_detail = self::_packClassIds($goodIds);
                ThreadData::setRequest('detail',$_detail);
                ThreadData::setRequest('post_id',count($goodIds));
                ThreadData::setRequest('second_class_id',explode(',',ThreadData::getRequest('good_ids')));
                Bawu_Log::notice('set goodids to detail succ,detail['.$_detail.']');
            }
            else
            {
                Bawu_Log::warning('get good_ids failure!good_ids['.print_r($goodIds,TRUE).']');
            }
        }
        else
        {
            //process by validator
            Bawu_Log::warning('get good_ids failure!good_ids['.$goodIds.']');
        }
        return TRUE;
    }

    /**
     * �ύ���ɷ����ʱ�򣬶Է�����Ϣ����pack���
	*/
    public static function buildDetailWithForumDir()
    {
        $fid = ThreadData::getRequest('forum_id');
        $firstClass = ThreadData::getRequest('first_class');
        $secondClass = ThreadData::getRequest('second_class');
        $packStr = fdirRpc::packForumDirName($fid,$firstClass,$secondClass);
        if ($packStr)
        {
            ThreadData::setRequest('detail',$packStr,TRUE);
            ThreadData::setRequest('username',ThreadData::getRequest('op_uname'),true);
            Bawu_Log::notice('buildDetailWithForumDir succ!detail['.$packStr.']');
        }
        else
        {
            Bawu_Log::warning('buildDetailWithForumDir failure!detail['.$packStr.']');
        }

        //<NAME_EMAIL>
        //����ϵ�ع���������Ŀ¼��Ҫͬʱ�԰���ϵ��fdir���и��¡�
        //��Ȼ�Ƚ϶��ģ�ֻ����ӵ������ˡ�ͨ��midlͬ������btx
        $uid = ThreadData::getRequest('op_uid');
        $uname = ThreadData::getRequest('op_uname');
        $optime = time();
        $res = btxRpc::applyDir($fid,$firstClass,$secondClass,$uname,$uid,$optime);
        if($res === false){
        	Bawu_Log::warning('btx talk error. forum manager apply dir');
        }

        return TRUE;
    }

    /**
     * ��ְʱ����ֻ���֤���Ƿ���2����ǰУ���
     */
    private static function _checkPhoneVerified() {
        $widget = new Passport_Pauth_AuthWidget(self::APPID, self::TPL);
        $intUid = Tieba_Session_Socket::getLoginUid();
        $baiduId = Tieba_Session_Socket::getBaiduid();
        $ip = Bingo_Http_Ip::getConnectIp();
        $authed = $widget->setExpire(self::Expired_time)->setNames('auth_mobile')->query($intUid, $baiduId, $ip);
        $isAuthed = $widget->isAuthed();
        if ($isAuthed) {
            return true;
        } else {
            ThreadData::error(cmErrorNo::NO_PHONE_AUTHCODE_PAST, cmErrorMsg::PHONE_AUTHCODE_PAST);
            return false;
        }
    }
    
    /**
     * �԰����ǳʽ���pack���
	*/
    public static function packResignation()
    {
        if (self::_checkPhoneVerified()) {
            $resignation = ThreadData::getRequest('resignation');
            ThreadData::setRequest('detail',$resignation,TRUE);
            return TRUE;
        } else {
            return false;
        }
    }
    //�����Ŷӹ���
    /**
     * ����С����
	*/
    public static function setForumAssistant()
    {
    	ThreadData::error(cmErrorNo::NOT_SERV,cmErrorMsg::NOT_SERV);
    	return false;
        return self::_setUserIdByUnameAndCheckMember(PermConfig2::$roles['FORUM_ASSIST'], PermConfig2::$cmConfig[cmCommandNo::SetForumAssistant]);
    	/*if (self::_checkTeamNum(memberRpc::PERM_FORUM_ASSIST))
        {
            self::_setUserIdByUnameAndCheckMember();
        }
        return TRUE;
        */
    }
    /**
     * ����ͼƬС��
	*/
    public static function setForumPicAdmin()
    {
    	ThreadData::error(cmErrorNo::NOT_SERV,cmErrorMsg::NOT_SERV);
    	return false;
        return self::_setUserIdByUnameAndCheckMember(PermConfig2::$roles['FORUM_PICADMIN'], PermConfig2::$cmConfig[cmCommandNo::SetForumPicAdmin]);
    	/*if (self::_checkTeamNum(memberRpc::PERM_FORUM_PICADMIN))
        {
            self::_setUserIdByUnameAndCheckMember();
        }
        return TRUE;
        */
    }
    /**
     * ������ƵС��
	*/
    public static function setForumVideoAdmin()
    {
    	ThreadData::error(cmErrorNo::NOT_SERV,cmErrorMsg::NOT_SERV);
    	return false;
        return self::_setUserIdByUnameAndCheckMember(PermConfig2::$roles['FORUM_VIDEOADMIN'], PermConfig2::$cmConfig[cmCommandNo::SetForumVideoAdmin]);
    	/*if (self::_checkTeamNum(memberRpc::PERM_FORUM_VIDEOADMIN))
        {
            self::_setUserIdByUnameAndCheckMember();
        }
        return TRUE;
        */
    }

     /**
     * ��������С��
        */
    public static function setForumDiskEditor()
    {
    	ThreadData::error(cmErrorNo::NOT_SERV,cmErrorMsg::NOT_SERV);
    	return false;
        return self::_setUserIdByUnameAndCheckMember(PermConfig2::$roles['FORUM_DISK_EDITOR'], PermConfig2::$cmConfig[cmCommandNo::SetForumDiskEditor]);
        /*if (self::_checkTeamNum(memberRpc::PERM_FORUM_VIDEOADMIN))
        {
            self::_setUserIdByUnameAndCheckMember();
        }
        return TRUE;
        */
    }


    public static function setBakanAdmin()
    {
    	ThreadData::error(cmErrorNo::NOT_SERV,cmErrorMsg::NOT_SERV);
    	return false;
    	return self::_setUserIdByUnameAndCheckMember(PermConfig2::$roles['FORUM_PUBLICATION'], PermConfig2::$cmConfig[cmCommandNo::SetForumPublication]);
    	/*if (self::_checkTeamNum(RpcIdlMember::PERM_FORUM_BAKANADMIN))
        {
            self::_setUserIdByUnameAndCheckMember();
        }
        return TRUE;
        */
    }
	public static function setBakanEditor()
    {
    	ThreadData::error(cmErrorNo::NOT_SERV,cmErrorMsg::NOT_SERV);
    	return false;
    	return self::_setUserIdByUnameAndCheckMember(PermConfig2::$roles['FORUM_PUBLICATION_EDITOR'], PermConfig2::$cmConfig[cmCommandNo::SetForumPublicationEditor]);
    	/*
    	if (self::_checkTeamNum(memberRpc::PERM_FORUM_PUBLICATION_EDITOR))
        {
            self::_setUserIdByUnameAndCheckMember();
        }
        return TRUE;
        */
    }

    public static function setDaquanZhubian()
    {
    	ThreadData::error(cmErrorNo::NOT_SERV,cmErrorMsg::NOT_SERV);
    	return false;
        return self::_setUserIdByUnameAndCheckMember(PermConfig2::$roles['FORUM_DAQUAN_ZHUBIAN'], PermConfig2::$cmConfig[cmCommandNo::SetForumDaquanEditor]);
    }
    public static function setDaquanXiaobian()
    {
    	ThreadData::error(cmErrorNo::NOT_SERV,cmErrorMsg::NOT_SERV);
    	return false;
        return self::_setUserIdByUnameAndCheckMember(PermConfig2::$roles['FORUM_DAQUAN_XIAOBIAN'], PermConfig2::$cmConfig[cmCommandNo::SetForumDaquan]);
    }
    /**
     * ��Ӱ����Ŷӵ�ʱ�򣬼���û����Ƿ�������
	*/
    private static function _setUserIdByUnameAndCheckMember($role, $perm_flag)
    {
    	if (self::_checkTeamNum($role)){
    		return FALSE;
    	}

        $username = ThreadData::getRequest('username');
        $fid = ThreadData::getRequest('forum_id');
        $op_uid = ThreadData::getRequest('op_uid');
        //$uid = fucenterRpc::getUseridByUname($username);
		 $uid = PassportUserInfo::getUidByUname($username);
        $boolPass = PermServer::checkUserPerm($op_uid, $fid, $perm_flag);
        $boolMember = PermServer::checkUserRole($fid, $uid, PermConfig2::$roles['FORUM_MEMBER']);
        $boolManager = PermServer::checkUserRole($fid, $uid, PermConfig2::$roles['FORUM_MANAGER']);

        // PM �ж�
        $is_pm = ThreadData::getRequest('is_pm');
        if ($is_pm){
            //$boolPass = TRUE;
            $boolPass = FALSE;
        }
        if ( !$boolMember ){
        	// ������ǻ�Ա������FALSE(�����û��ȼ��ж��Ƿ�>=3��)
        	$retLikeInfo = RpcIdlFulike::getUserLevel($fid,$uid);
    		//check user level and is_like

    		if (empty($retLikeInfo) || false == is_array($retLikeInfo))
    		{
    			ThreadData::error(cmErrorNo::CM_PROCESS_FAILURE,cmErrorMsg::ADD_USER_NOT_MEMBER);
    			return TRUE;
    		}

    		if ( !isset($retLikeInfo['is_like']) || 1 != $retLikeInfo['is_like'])
    		{
    			ThreadData::error(cmErrorNo::CM_PROCESS_FAILURE,cmErrorMsg::ADD_USER_NOT_MEMBER);
    			return TRUE;
    		}

    		if ( !isset($retLikeInfo['level_id']) || Bawu_Global::MEMBER_LEVEL > $retLikeInfo['level_id'])
    		{
    			//ThreadData::error(cmErrorNo::CM_PROCESS_FAILURE,cmErrorMsg::ADD_USER_NOT_MEMBER);
    			ThreadData::error(cmErrorNo::CM_PROCESS_FAILURE,cmErrorMsg::ADD_USER_LEVEL_NOT_ENOUGH);
    			return TRUE;
    		}
        }

        if ( (!empty($username) || $username == '0') && $boolPass && !$boolManager ){
        	ThreadData::setRequest('user_id',$uid,TRUE);
        } else {
        	ThreadData::error(cmErrorNo::CM_PROCESS_FAILURE,cmErrorMsg::ADD_USER_NOT_MANAGER);
        }
        return TRUE;

    }

    /**
     * �������Ŷӵ���Ŀ�Ƿ�ﵽ���ƶ�������
     *
     * @param unknown_type $permFlag
     */
    private static function _checkTeamNum($roleid)
    {
    	$fid = ThreadData::getRequest('forum_id');
        return PermServer::isForumRoleMaxNum($fid, $roleid);
    }
    /**
     * ����û���ʱ�򣬵��ø÷�����ͨ���û�����ȡ�û�ID��ͬʱ����������Ƿ�Ϸ�
	*/
    public static function filterForumUser()
    {
        $username = ThreadData::getRequest('username');
        ThreadData::setRequest('user_name', $username, TRUE);
        if ( !empty($username) || $username == '0' )
        {
            //$uid = fucenterRpc::getUseridByUname($username);
			 $uid = PassportUserInfo::getUidByUname($username);
            if (empty($uid) || $uid === false)
            {
                ThreadData::error(cmErrorNo::CM_PROCESS_FAILURE,cmErrorMsg::ADD_USERNAME_NOT_FOUND);
            }
            ThreadData::setRequest('user_id',$uid,TRUE);
        }
        self::_checkFilterDayNums();
        return TRUE;
    }
    /**
     * ���IP��IP�ķ��ʱ��ʱ�������ļ�������
     *
     */
    public static function filterForumIp()
    {
        //self::_checkFilterDayNums();
        ThreadData::setRequest('day_num',Bawu_Global::FILTER_IP_DAY_NUM,TRUE);

        $ip = ThreadData::getRequest('user_id');
		$ip = TiebaUtil :: getDecodeIp($ip);
		ThreadData::setRequest('user_id', $ip,TRUE);
        //����Ƿ���ͬʱ���ID
        $isActionWithUser = FALSE;
        if (isset($_POST['ip_secure_str']))
        {
            $_ipSecureStr = Phpbean_Filter::noTags($_POST['ip_secure_str']);
            if ($_ipSecureStr == BawuUrlToken::encodeIp($ip))
            {
                $isActionWithUser = TRUE;
            }
        }
        if ($isActionWithUser)
        {
            ThreadData::setRequest('ip_type',1);
        }
        else
        {
            ThreadData::setRequest('ip_type',0);
        }

        ThreadData::setResponse('__isFilterIpWithFilterUser',$isActionWithUser);
        //����Ƿ����ֻ�IP
        $ip = Phpbean_Util::long2ip($ip);
        $isPhoneIp = TiebaUtil::isWapIp($ip,BAWU_DATA_ROOT_PATH . Bawu_Global::WAP_IP_DICT_FILEPATH);
        if ($isPhoneIp)
        {
            ThreadData::error(cmErrorNo::DATA_CHECK_ERROR,cmErrorMsg::DATA_CHECK_ERROR);
            Bawu_Log::warning('Filter Failure!ip['.$ip.'] in wapip');
        }
        return TRUE;
    }
    /**
     * ȡ������û�
     *
     * @return unknown
     */
    public static function cancelFilterForumUser()
    {

        $fid = ThreadData::getRequest('forum_id');
	$op_uid = ThreadData::getRequest('op_uid');
        $boolManager = PermServer::checkUserRole($fid, $op_uid, PermConfig2::$roles['FORUM_MANAGER']);
	//$boolManager = false;
	if ( $boolManager ){
        	$username = ThreadData::getRequest('username');
        	ThreadData::setRequest('user_name', $username, TRUE);
        	if ( !empty($username) || $username == '0' )
        	{
           	 //$uid = fucenterRpc::getUseridByUname($username);
			 $uid = PassportUserInfo::getUidByUname($username);
            		if (empty($uid) || $uid === false)
            		{
               			ThreadData::error(cmErrorNo::CM_PROCESS_FAILURE,cmErrorMsg::ADD_USERNAME_NOT_FOUND);
            		}
            	ThreadData::setRequest('user_id',$uid,TRUE);
        	}
	}
	else
	{
		ThreadData::error(cmErrorNo::CM_PROCESS_FAILURE,cmErrorMsg::USER_PERM_ERROR);
	}
        return TRUE;
    }

    public static function cancelFilterForumIP()
    {
        $ip = ThreadData::getRequest('user_id');
        $ipType = strip_tags(trim($_POST['ip_type']));
        if ( $ipType == BawuUrlToken::encodeIp($ip) )
        {
            ThreadData::setResponse('__isFilterIpWithFilterUser',TRUE);
        }
        else
        {
            ThreadData::setResponse('__isFilterIpWithFilterUser',FALSE);
        }
    }
    /**
     * �������ޣ�����
     */
    public static function setFds()
    {
    	if (! self::_checkFds()) {
    		return true;
    	}
    	$intHour = ThreadData::getRequest('hour_num');
    	$intTime = time();
    	$endTime = $intTime + $intHour * 3600;
    	ThreadData::setRequest('begin_time', $intTime, TRUE);
    	ThreadData::setRequest('end_time', $endTime, TRUE);
    	return true;
    }

    public static function delFds()
    {
    	if (! self::_checkFds()) {
    		return true;
    	}
    	return true;
    }

    private static function _checkFds()
    {
    	$fid = ThreadData::getRequest('forum_id');
    	if (! RpcIdlFds::hasModule($fid)) {
    		ThreadData::error(cmErrorNo::DATA_CHECK_ERROR,cmErrorMsg::DATA_CHECK_ERROR);
            Bawu_Log::warning('_checkFds Failure!');
    		return false;
    	}
    	return true;
    }


	/**
     * @desc ���ɾ�ֹ�ܣ�����
     * <AUTHOR> <EMAIL>
     */
    public static function setJzwFds()
    {
    	if (! self::_checkJzwFds()) {
    		return true;
    	}
    	$intHour = ThreadData::getRequest('hour_num');
    	$intTime = time();
    	//$intThreadThreshold = ThreadData::getRequest('thread_threshold');
    	$intThreadThreshold = ThreadData::getRequest('ban_num');
    	$intThreadThreshold = strtotime('-' . $intThreadThreshold . ' days');
    	$endTime = $intTime + $intHour * 3600;
    	Bawu_Log::debug(sprintf('Set JingZhiWa [begin_time]: %d, [end_time]: %d, [thread_threshold]: %d', $intTime, $endTime, $intThreadThreshold));
    	ThreadData::setRequest('begin_time', $intTime, TRUE);
    	ThreadData::setRequest('end_time', $endTime, TRUE);
    	ThreadData::setRequest('thread_threshold', $intThreadThreshold, TRUE);
    	ThreadData::setRequest('ban_num', $intThreadThreshold, TRUE);
    	return true;
    }

    public static function delJzwFds()
    {
    	if (! self::_checkJzwFds()) {
    		return true;
    	}
    	return true;
    }

    private static function _checkJzwFds()
    {
    	$fid = ThreadData::getRequest('forum_id');
    	if (! RpcIdlFds::hasJzwModule($fid)) {
    		ThreadData::error(cmErrorNo::DATA_CHECK_ERROR,cmErrorMsg::DATA_CHECK_ERROR);
            Bawu_Log::warning('_checkJzwFds Failure!');
    		return false;
    	}
    	return true;
    }
    /**
     * ����������Ƿ��������ķ�����������������ļ�������
	*/
    private static function _checkFilterDayNums()
    {
        $day_num = ThreadData::getRequest('day_num');
		$perm_flag = ThreadData::getRequest('perm_flag');
		$forum_id = ThreadData::getRequest('forum_id');
        $user_id = BawuSession::getLoginUid();
        $res = PermServer::checkUserRole($forum_id, $user_id, PermConfig2::$roles['FORUM_ASSIST'],true);
        if (!empty($res)) {
		$arrAllowDays = Bawu_Global::$ASSIST_FILTER_DAY_NUMS;
	} else {
		$arrAllowDays = Bawu_Global::$FILTER_DAY_NUMS;
	}

	//�ж����÷����С����
        if($day_num == 36500){
		$cupidObj = new Cupid();
		$cupidNo = 216;

		$cupidObj->setInputArr(array(
			'fname_uname'   => ThreadData::getRequest('forum_name')." ".ThreadData::getRequest('op_uname'),		                        )
		);
		$tipinfo =  $cupidObj->getSmallFlow('bawu', $cupidNo);
	}

        if ( empty($arrAllowDays) || !in_array($day_num,$arrAllowDays) || ($day_num == 36500 && $tipinfo == false))
        {
        	ThreadData::error(cmErrorNo::DATA_CHECK_ERROR,cmErrorMsg::DATA_CHECK_ERROR);
            	Bawu_Log::warning('Filter Failure!day_num['.$day_num.'] is invalid!'.
                print_r(Bawu_Global::FILTER_DAY_NUMS,TRUE));
        }
    }

	public static function addrecommanduser()
	{
		$varForumID		= ThreadData::getRequest('forum_id','',FALSE);
		$varUserID		= ThreadData::getRequest('user_id','',FALSE);
		$varMemberInfo	= ThreadData::getRequest('member_info','',FALSE);
		$varMemberAchi	= ThreadData::getRequest('member_achi','',FALSE);
		$varMemberBoutique= ThreadData::getRequest('member_boutique','',FALSE);
		$varBatch		= ThreadData::getRequest('batch','',FALSE);
		$varVote		= ThreadData::getRequest('vote','',FALSE);
		$varRecommand	= ThreadData::getRequest('recommand','',FALSE);

		//check user id
		$retUserInfo = RpcIdlFucenter::getUserinfoByUids(array($varUserID));
		if (empty($retUserInfo))
		{
			return false;
		}

		$retLikeInfo = RpcIdlFulike::getUserLevel($varForumID,$varUserID);
		//check user level and is_like

		if (empty($retLikeInfo) || false == is_array($retLikeInfo))
		{
			ThreadData::error(cmErrorNo::CM_PROCESS_FAILURE,cmErrorMsg::ADD_USER_NOT_MEMBER);
			return false;
		}

		if ( !isset($retLikeInfo['is_like']) || 1 != $retLikeInfo['is_like'])
		{
			ThreadData::error(cmErrorNo::CM_PROCESS_FAILURE,cmErrorMsg::ADD_USER_NOT_MEMBER);
			return false;
		}

		if ( !isset($retLikeInfo['level_id']) || commonConfig::THEMINLEVEL > $retLikeInfo['level_id'])
		{
			ThreadData::error(cmErrorNo::CM_PROCESS_FAILURE,cmErrorMsg::ADD_USER_NOT_MEMBER);
			return false;
		}

		//check can the user operate the data
		if ( ThreadData::getRequest('is_pm') == 1){
		    $bolIsManager = TRUE;
		} else {
			$varOpUserID = BawuSession::getLoginUid();
		    $bolIsManager = PermServer::checkUserRole($varForumID, $varOpUserID, PermConfig2::$roles['FORUM_MANAGER']);
		}

		if (true != $bolIsManager)
		{
			ThreadData::error(cmErrorNo::USER_PERM_ERROR,cmErrorMsg::USER_PERM_ERROR);
			return false;
		}

		$result = fameHallRpc::addRecommandUser($varForumID,$varUserID,0,$varMemberInfo,$varMemberAchi,$varMemberBoutique,$varBatch,$varVote , $varRecommand);

		if ( 0 < intval($result))
		{
			return true;
		}
		else
		{
			ThreadData::error(cmErrorNo::CM_PROCESS_FAILURE,cmErrorMsg::OPERATION_FAILRE);
			return false;
		}
	}

	public static function setrecommanduser()
	{
		$varForumID		= ThreadData::getRequest('forum_id','',FALSE);
		$varUserID		= ThreadData::getRequest('user_id','',FALSE);
		$varMemberInfo	= ThreadData::getRequest('member_info','',FALSE);
		$varMemberAchi	= ThreadData::getRequest('member_achi','',FALSE);
		$varMemberBoutique= ThreadData::getRequest('member_boutique','',FALSE);
		$varBatch		= ThreadData::getRequest('batch','',FALSE);
		$varVote		= ThreadData::getRequest('vote','',FALSE);
		$varRecommand	= ThreadData::getRequest('recommand','',FALSE);

		//check can the user operate the data
		if ( ThreadData::getRequest('is_pm') == 1){
		    $bolIsManager = TRUE;
		} else {
			$varOpUserID = BawuSession::getLoginUid();
		    $bolIsManager = PermServer::checkUserRole($varForumID, $varOpUserID, PermConfig2::$roles['FORUM_MANAGER']);
		}

		if (true != $bolIsManager)
		{
			ThreadData::error(cmErrorNo::USER_PERM_ERROR,cmErrorMsg::USER_PERM_ERROR);
			return false;
		}

		$result = fameHallRpc::setRecommandUser($varForumID,$varUserID,0,$varMemberInfo,$varMemberAchi,$varMemberBoutique,$varBatch,$varVote , $varRecommand);

		if ( false !== $result )
		{
			return true;
		}
		else
		{
			ThreadData::error(cmErrorNo::CM_PROCESS_FAILURE,cmErrorMsg::OPERATION_FAILRE);
			return false;
		}
	}

	public static function delrecommanduser()
	{
		$varForumID		= ThreadData::getRequest('forum_id','',FALSE);
		$varUserID		= ThreadData::getRequest('user_id','',FALSE);

		//check can the user operate the data
		if ( ThreadData::getRequest('is_pm') == 1){
		    $bolIsManager = TRUE;
		} else {
			$varOpUserID = BawuSession::getLoginUid();
		    $bolIsManager = PermServer::checkUserRole($varForumID, $varOpUserID, PermConfig2::$roles['FORUM_MANAGER']);
		}

		if (true != $bolIsManager)
		{
			ThreadData::error(cmErrorNo::USER_PERM_ERROR,cmErrorMsg::USER_PERM_ERROR);
			return false;
		}

		$result = fameHallRpc::delRecommandUser($varForumID,$varUserID);

		if ( 0 < intval($result))
		{
			return true;
		}
		else
		{
			ThreadData::error(cmErrorNo::CM_PROCESS_FAILURE,cmErrorMsg::OPERATION_FAILRE);
			return false;
		}
	}




}
