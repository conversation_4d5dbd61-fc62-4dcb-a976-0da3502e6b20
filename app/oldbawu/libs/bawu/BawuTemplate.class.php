<?php
/**
 * ��ͼ�������࣬����smarty
 * <AUTHOR>
 * @since 2009-08-31
 * @package forum-manager
 *
 */
class BawuTemplate
{
    public static $view = NULL;
    /**
     * ��ȡ��ͼ����ʵ�ֵĶ���
	*/
    public static function getView()
    {
        if ( empty(self::$view) )
        {
            $objSmarty = new Smarty; 
            $objSmarty->template_dir = Bawu_Global::TEMPLATE_BASEFE_PATH . Bawu_Global::SMARTY_TEMPLATE_DIR;
            $objSmarty->compile_dir  = Bawu_Global::TEMPLATE_BASE_PATH . Bawu_Global::SMARTY_COMPILE_DIR;
            $objSmarty->config_dir = Bawu_Global::TEMPLATE_BASE_PATH . Bawu_Global::SMARTY_CONFIG_DIR;
            $objSmarty->cache_dir = Bawu_Global::TEMPLATE_BASE_PATH . Bawu_Global::SMARTY_CACHE_DIR; 
            $objSmarty->plugins_dir = array(Bawu_Global::TEMPLATE_BASEFE_PATH . Bawu_Global::SMARTY_PLUGIN_DIR, 'plugins'); 
            $objSmarty->left_delimiter = Bawu_Global::SMARTY_LEFT_DELIMITER; 
            $objSmarty->right_delimiter = Bawu_Global::SMARTY_RIGHT_DELIMITER; 
            $objSmarty->compile_check = Bawu_Global::SMARTY_COMPILE_CHECK; 
            self::$view = $objSmarty;
        }
        return self::$view;
    }
    /**
     * ����Ҫ�����ֵ
	*/
    public static function set($key,$value)
    {
        self::getView()->assign($key,$value);
    }
    /**
     * ��Ⱦҳ��
	*/
    public static function render($template)
    {
        self::getView()->display($template);
    }
}