<?php
/**
 * ��������Դ���������ṩ��FEģ��ʹ��
 * <AUTHOR>
 * @since 2009-08-27
 * @package forum-manager
 *
 */
class BawuResource
{
    /**
     * ������Դ
	*/
    public static function getBase()
    {
    	$fid = ThreadData::getRequest('forum_id','',FALSE);
    	$has_fds = RpcIdlFds::hasModule($fid) || RpcIdlFds::hasJzwModule($fid);
        return array(
            'errno'=>ThreadData::$errno,
            'errmsg'=>ThreadData::$errmsg,
            'retno'=>ThreadData::$retNo,
            'tbs_resign'=>BawuUrlToken::encode(cmCommandNo::ApplicationResg),//������ְ
            'year'=>date('Y',Phpbean_Util::time()),
        	'has_fds' => $has_fds,
        );
    }
    /**
     * �û������Ϣ
	*/
    public static function user()
    {
        $fid = ThreadData::getRequest('forum_id','',FALSE);
        $islogin = BawuSession::isLogin();        
        $username = BawuSession::getLoginUname();
        $uid = BawuSession::getLoginUid();
        
        //add by xuming for manager mobile phone
        $needmobilephone = ThreadData::getRequest('needmobilephone','',FALSE);
        
        if ( TRUE === $islogin && $uid!=FALSE )
        {
            //login
            $perm = ThreadData::getRequest('op_perm','',FALSE);
            $sid = TiebaUtil::getSessionId(Bawu_Global::SESSION_ID_IN_COOKIE_KEY);
            if ($sid)
            {
                $uname = $username;        
                /*$_isMember = TiebaPerm::isMember($perm); 
                $_isOldMember = FALSE;
                $_isManagerTeam = FALSE;
                $_isManager = FALSE;*/
                if ( ThreadData::getRequest('is_pm') == 1){
                    $bolIsManager = TRUE;
                } else {
                    $bolIsManager = PermServer::checkUserRole($fid, $uid, PermConfig2::$roles['FORUM_MANAGER']);
                }
                $bolIsDiskManager = PermServer::checkUserRole($fid, $uid, PermConfig2::$roles['FORUM_DISK_EDITOR']);

                $user = array(
                	'is_login'=>TRUE,
                	'id'=>$uid,
                	'sid'=>$sid,
                    'portrait'=>Ucrypt::ucrypt_encode($uid,$uname),
                    'name'=>$uname,
                    'is_member'=>TiebaPerm2::isMember($fid, $uid),
                    'is_oldmember'=>TiebaPerm2::isOldMember($uid,$fid),
                    'is_managerteam'=>TiebaPerm2::isManagerTeam($fid, $uid),
                    //                    'is_manager'=>TiebaPerm2::isManagerOrBaZhu($fid, $uid),
                    'is_manager'=> $bolIsManager,
                    'is_disk_admin'=> $bolIsDiskManager,
                    'is_needmobilephone' =>$needmobilephone,               
                );
            }
            else
            {
                $user = array('is_login'=>FALSE);
            }
        }
        else 
        {
            //
            $user = array('is_login'=>FALSE);
        }
        return $user;
    }
    /**
     * һ�����ɵ�������Դ
	*/
    public static function forum()
    {
        $fid = ThreadData::getRequest('forum_id','',FALSE);
        $fname = ThreadData::getRequest('forum_name','gbk',FALSE);
        if ( empty($fid) || (empty($fname)&&($fname!='0')) )
        {
            return  array(
    			'is_exist' => FALSE, //���ڳ�noneҳ��,todo
            );
        }
        //get managers
        //$managers = memberRpc::getForumManager($fid,memberRpc::PERM_FORUM_MANAGER);
        $managers = memberRpc::getForumManager($fid, PermConfig2::$roles['FORUM_MANAGER']);
        $managerNameArray = array();
        if (!empty($managers))
        {
            foreach ($managers as $_m)
            {
                //$managerNameArray[] = $_m['username'];
                $managerNameArray[] = $_m['user']['user_name'];
            }
        }
        //get dir info
        $fdirInfo = btxRpc::getForumDirInfo($fid);
        $_first_class = '';
        $_second_class = '';
        $_class_rank = '';
        //$_result_cat = '';
        if ( FALSE !== $fdirInfo)
        {
            if (isset($fdirInfo['fdir_level_one']))$_first_class = $fdirInfo['fdir_level_one'];
            if (isset($fdirInfo['fdir_level_two']))$_second_class = $fdirInfo['fdir_level_two'];
            if (isset($fdirInfo['fdir_rank']))$_class_rank = $fdirInfo['fdir_rank'];
            //if (isset($fdirInfo['fdir_hot_value']))$_result_cat = $fdirInfo['fdir_hot_value'];
        }
	$arrConfig=tbdiskRpc::getForumConfig($fid);
	if(isset($arrConfig["is_enable"]) && intval($arrConfig["is_enable"])==1)
	{
		$open_disk=true;
	}else{
		$open_disk=false;
	}
		//build
        $forum = array(
    		'is_exist' => true, //���ڳ�noneҳ��,todo
    		'id' => $fid,
    		'name' => $fname,
    		'managers' => $managerNameArray, //�����б�
            'manager_num' => count($managerNameArray),
    		'first_class' => $_first_class,
    		'second_class' => $_second_class,
    		'class_rank' => $_class_rank,
            'has_album'=>zycontrolRpc::isModuleOpen($fid,$fname,zycontrolRpc::ZY_MOUD_ALBUM),
            'has_calendar'=>zycontrolRpc::isModuleOpen($fid,$fname,zycontrolRpc::ZY_MOUD_CALENDAR),
            //'has_video'=>zycontrolRpc::isModuleOpen($fid,$fname,zycontrolRpc::ZY_MOUD_VEDIO),
            'has_video'=>fstyleRpc::hasVideo($fid, $fname),
            'has_hall'=>zycontrolRpc::isModuleOpen($fid,$fname,zycontrolRpc::ZY_MOUD_HALL),
            'has_bakan' => TiebaUtil::getBakanIsOpen($fname),
	    	//'has_daquan' => TiebaUtil::getDaquanIsOpen($fname), //daquan mokuai xiaxian add by cuishichao 20130913
	    	'has_daquan'=>false,
	    	'has_postpre' => cupidRpc::getPostPrefix($_first_class, $_second_class) || cupidRpc::getPostPrefixForum($fname, $fid),
	    	'has_new_bawu' => cupidRpc::getNewBawu($fname, $fid),
            'album_opened' => (photoRpc::getForumPhotoInfo($fname)==false ? 0:1),
//            'has_daquan' => 1,
    		//'result_cat' => $_result_cat,
	    	'open_disk'=> $open_disk,
        	'is_system_start_shenshou'=>self::_isSystemStartShenshou(),
        	'is_system_start_jzw'=>self::_isSystemStartJzw(),
        );
    	return $forum;     
    }
    /**
     * ���ɷ���ǰ׺��������Դ
	*/
    public static function getPostPrefix()
    {
		$fid = ThreadData::getRequest('forum_id','',FALSE);
		$arrInput = array(				
			'forum_id'		=> $fid,
			'module_id'	=> 98,
			'page_id'		=> 1,
			'format'		=> 'json',
		);
		//require_once("/home/<USER>/php5/lib/php-libs/Tieba/Service.php");
		//require_once("/home/<USER>/php5/lib/php-libs/Tieba/Errcode.php");
		$arrOut = Tieba_Service::call('forum', 'getForumAttr', $arrInput, NULL, NULL, 'post', 'json');
		if(Tieba_Errcode::ERR_SUCCESS === $arrOut['errno']){					
			$arrData = $arrOut['output'];
			$arrStyle = array();
			foreach ($arrData as $arrEach) {
				if (98 === intval($arrEach['module_id'])) {
					$strStyle = strval($arrEach['style_name']);
				}
			}
			$arrStyle = Bingo_String::json2array($strStyle);
		}
		return $arrStyle;
    }
    /**
     * ���ɾ�Ʒ�������������Դ
	*/
    public static function forumGoodClassify()
    {
        $fname = ThreadData::getRequest('forum_name');
        $fid = ThreadData::getRequest('forum_id');
        $data = frsRpc::getGoodClass($fname, $fid);
        return array(
            'tbs_add_good'=>BawuUrlToken::encode(cmCommandNo::AddGoodClass),
            'tbs_del_good'=>BawuUrlToken::encode(cmCommandNo::DelGoodClass),
            'tbs_set_good_sort'=>BawuUrlToken::encode(cmCommandNo::ResortGoodClass),
            'tbs_edit_good'=>BawuUrlToken::encode(cmCommandNo::ReNameGoodClass),
            'data'=>$data,
        );
    }
    /**
     * ���ɷ����������Դ
     * 
     */
    public static function classes()
    {
        return array(
            'tbs'=>BawuUrlToken::encode(cmCommandNo::DirApplySecClass),
        	'data'=>btxRpc::getAllDirs(),
        );
    }
    /**
     * �����Ŷӵ�������Դ
	*/
    public static function teams()
    {
        $fid = ThreadData::getRequest('forum_id');
        $fname = ThreadData::getRequest('forum_name');
        
        //getRole (BY XIECHAO01)
        //$arrRole = Array(memberRpc::PERM_FORUM_ASSIST);
        $arrRole = Array(PermConfig2::$roles['FORUM_ASSIST']);

        $team = array(
            'sub_admin'=>array(
                'tbs_add'=>BawuUrlToken::encode(cmCommandNo::SetForumAssistant),
                'tbs_remove'=>BawuUrlToken::encode(cmCommandNo::DelForumAssistant),
            ),  
            'pic_admin'=>array(
                'tbs_add'=>BawuUrlToken::encode(cmCommandNo::SetForumPicAdmin),
                'tbs_remove'=>BawuUrlToken::encode(cmCommandNo::DelForumPicAdmin),
            ),  
            'video_admin'=>array(
                'tbs_add'=>BawuUrlToken::encode(cmCommandNo::SetForumVideoAdmin),
                'tbs_remove'=>BawuUrlToken::encode(cmCommandNo::DelForumVideoAdmin),
            ),  
            'bakan_admin' => array(
                'tbs_add'=>BawuUrlToken::encode(cmCommandNo::SetForumPublication),
                'tbs_remove'=>BawuUrlToken::encode(cmCommandNo::GetForumPublication),
            ),  
            'bakan_editor' => array(
                'tbs_add'=>BawuUrlToken::encode(cmCommandNo::SetForumPublicationEditor),
                'tbs_remove'=>BawuUrlToken::encode(cmCommandNo::DelForumPublicationEditor),
            ),

            'daquan_admin' => array(
                'tbs_add'=>BawuUrlToken::encode(cmCommandNo::SetForumDaquanEditor),
                'tbs_remove'=>BawuUrlToken::encode(cmCommandNo::DelForumDaquanEditor),
            ),  
            'daquan_editor' => array(
                'tbs_add'=>BawuUrlToken::encode(cmCommandNo::SetForumDaquan),
                'tbs_remove'=>BawuUrlToken::encode(cmCommandNo::DelForumDaquan),
            ),  
	    //����
            'disk_admin' => array(
                'tbs_add'=>BawuUrlToken::encode(cmCommandNo::SetForumDiskEditor),
                'tbs_remove'=>BawuUrlToken::encode(cmCommandNo::DelForumDiskEditor),
            ),  

        );

        // Ini
        $_subAdmin = array();
        $_videoAdmin = array();
        $_picAdmin = array();
        $_bakanAdmin = array();
        $_bakanEditorAdmin = array();
        $_daquanXiaoBian = array();
        $_daquanZhuBian = array();
        $_diskAdmin = array();

        //pic module check
        $_isPicOpen = zycontrolRpc::isModuleOpen($fid,$fname,zycontrolRpc::ZY_MOUD_ALBUM);
        
        //�����°�����Ƿ���
        if(!$_isPicOpen && photoRpc::getForumPhotoInfo($fname) !=false){
            $_isPicOpen = true;
        }
        
        //$_isVedioOpen = zycontrolRpc::isModuleOpen($fid,$fname,zycontrolRpc::ZY_MOUD_VEDIO);
        $_isVedioOpen = fstyleRpc::hasVideo($fid, $fname);
        if ($_isPicOpen)
        {
        	// CANCEL BY XIECHAO01
            // $permFlag = $permFlag | memberRpc::PERM_FORUM_PICADMIN;
            array_push($arrRole, PermConfig2::$roles['FORUM_PICADMIN']);
        }
        if ($_isVedioOpen)
        {
        	// CANCEL BY XIECHAO01
            // $permFlag = $permFlag | memberRpc::PERM_FORUM_VIDEOADMIN;
            array_push($arrRole, PermConfig2::$roles['FORUM_VIDEOADMIN']);
        }
        if (TiebaUtil::getBakanIsOpen($fname)) {
        	// CANCEL BY XIECHAO01
        	// $permFlag = $permFlag | RpcIdlMember::PERM_FORUM_BAKANADMIN;
        	// $permFlag = $permFlag | memberRpc::PERM_FORUM_PUBLICATION_EDITOR;
        	array_push($arrRole, PermConfig2::$roles['FORUM_PUBLICATION']);
        	array_push($arrRole, PermConfig2::$roles['FORUM_PUBLICATION_EDITOR']);
        }
        if (TiebaUtil::getDaquanIsOpen($fname) || 1 == 1) {
            array_push($arrRole, PermConfig2::$roles['FORUM_DAQUAN_XIAOBIAN']);
            array_push($arrRole, PermConfig2::$roles['FORUM_DAQUAN_ZHUBIAN']);
        }
	//��ȡ�����Ƿ���
	$arrConfig=tbdiskRpc::getForumConfig($fid);
	if(isset($arrConfig["is_enable"]) && intval($arrConfig["is_enable"])==1)
	{
		array_push($arrRole, PermConfig2::$roles['FORUM_DISK_EDITOR']);
	}

        //��ȡadmins
        foreach ($arrRole as $intRole){
        	if (!isset($intRole)){
        		continue;
        	}
        	$arrTmpRes[$intRole] = PermServer::getRoleUserList($fid, $intRole);
        }
        $arrRes = PermServer::executeRpc();
        foreach ($arrTmpRes as $intRoleID => $intResID){
        	$admin_tmp = $arrRes[$intResID]['res'];
        	if (!empty($admin_tmp) && is_array($admin_tmp)){
        		$uids = array();
        		// ��ѯ�Ŷӻ�����Ϣ
        		foreach ($admin_tmp as $admin){
        			$uid = $admin['user']['user_id'];
        			$uname[$uid] = $admin['user']['user_name'];
        			$uids[] = $uid;
        			//$last_login_time = $_uinfo['lastlogin_time'];
        		}
        		// ��ѯ�û�����¼ʱ��
        		$uidsInfo = fucenterRpc::getUserinfoByUids($uids);
        		foreach ($uidsInfo as $_uinfo){
        			$uidHash[$_uinfo['uid']] = $_uinfo['lastlogin_time'];
                }
                // �齨����
        		switch ($intRoleID){
        			case PermConfig2::$roles['FORUM_ASSIST']:
        				// С����
        				foreach ($uids as $uid){
	        				$_subAdmin[] = array(
	                            'user_id'=>$uid,
	                            'user_name'=>$uname[$uid],
	                            'last_login_time'=>$uidHash[$uid],
	                        );
        				}
        				break;
        			case PermConfig2::$roles['FORUM_PICADMIN']:
        				// ͼƬ
        				foreach ($uids as $uid){
	        				$_picAdmin[] = array(
	                            'user_id'=>$uid,
	                            'user_name'=>$uname[$uid],
	                            'last_login_time'=>$uidHash[$uid],
	                        );
        				}
        				break;
        			case PermConfig2::$roles['FORUM_VIDEOADMIN']:
        				// ��Ƶ
        				foreach ($uids as $uid){
	        				$_videoAdmin[] = array(
	                            'user_id'=>$uid,
	                            'user_name'=>$uname[$uid],
	                            'last_login_time'=>$uidHash[$uid],
	                        );
        				}
        				break;
        			case PermConfig2::$roles['FORUM_PUBLICATION']:
        				// �ɿ�
        				foreach ($uids as $uid){
	        				$_bakanAdmin[] = array(
	                            'user_id'=>$uid,
	                            'user_name'=>$uname[$uid],
	                            'last_login_time'=>$uidHash[$uid],
	                        );
        				}
        				break;
        			case PermConfig2::$roles['FORUM_PUBLICATION_EDITOR']:
        				// �ɿ�����
        				foreach ($uids as $uid){
	        				$_bakanEditorAdmin[] = array(
	                            'user_id'=>$uid,
	                            'user_name'=>$uname[$uid],
	                            'last_login_time'=>$uidHash[$uid],
	                        );
        				}
                        break;
                    case PermConfig2::$roles['FORUM_DAQUAN_ZHUBIAN']:
                        foreach ($uids as $uid){
                            $_daquanZhuBian[] = array(
                                'user_id'=>$uid,
                                'user_name'=>$uname[$uid],
                                'last_login_time'=>$uidHash[$uid],
                            );
                        }
                        break;
		   
		     case PermConfig2::$roles['FORUM_DISK_EDITOR']:
                        foreach ($uids as $uid){
                            $_diskAdmin[] = array(
                                'user_id'=>$uid,
                                'user_name'=>$uname[$uid],
                                'last_login_time'=>$uidHash[$uid],
                            );
                        }
                        break;

                    case PermConfig2::$roles['FORUM_DAQUAN_XIAOBIAN']:
                        foreach ($uids as $uid){
                            $_daquanXiaoBian[] = array(
                                'user_id'=>$uid,
                                'user_name'=>$uname[$uid],
                                'last_login_time'=>$uidHash[$uid],
                            );
                        }
                        break;
        			default:
        				break;
        		}
        	}
        }
                $team = array(
                	'sub_admin'=>array(
                        'tbs_add'=>BawuUrlToken::encode(cmCommandNo::SetForumAssistant),
                        'tbs_remove'=>BawuUrlToken::encode(cmCommandNo::DelForumAssistant),
                        'users'=>self::_sortTeam($_subAdmin),
                    ),
                    'pic_admin'=>array(
                        'tbs_add'=>BawuUrlToken::encode(cmCommandNo::SetForumPicAdmin),
                        'tbs_remove'=>BawuUrlToken::encode(cmCommandNo::DelForumPicAdmin),
                        'users'=>self::_sortTeam($_picAdmin),
                    ),
                    'video_admin'=>array(
                        'tbs_add'=>BawuUrlToken::encode(cmCommandNo::SetForumVideoAdmin),
                        'tbs_remove'=>BawuUrlToken::encode(cmCommandNo::DelForumVideoAdmin),
                        'users'=>self::_sortTeam($_videoAdmin),
                    ),
                    'bakan_admin' => array(
                		'tbs_add'=>BawuUrlToken::encode(cmCommandNo::SetForumPublication),
                		'tbs_remove'=>BawuUrlToken::encode(cmCommandNo::GetForumPublication),
                    	'users'=>self::_sortTeam($_bakanAdmin),
           			),
           			'bakan_editor' => array(
           				'tbs_add'=>BawuUrlToken::encode(cmCommandNo::SetForumPublicationEditor),
                		'tbs_remove'=>BawuUrlToken::encode(cmCommandNo::DelForumPublicationEditor),
           				'users' => self::_sortTeam($_bakanEditorAdmin),
                    ),
                
                    'daquan_admin' => array(
                        'tbs_add'=>BawuUrlToken::encode(cmCommandNo::SetForumDaquanEditor),
                        'tbs_remove'=>BawuUrlToken::encode(cmCommandNo::DelForumDaquanEditor),
                        'users'=>self::_sortTeam($_daquanZhuBian),
                    ),
                    'daquan_editor' => array(
                        'tbs_add'=>BawuUrlToken::encode(cmCommandNo::SetForumDaquan),
                        'tbs_remove'=>BawuUrlToken::encode(cmCommandNo::DelForumDaquan),
                        'users' => self::_sortTeam($_daquanXiaoBian),
                    ),
		  'disk_admin' => array(
                        'tbs_add'=>BawuUrlToken::encode(cmCommandNo::SetForumDiskEditor),
                        'tbs_remove'=>BawuUrlToken::encode(cmCommandNo::DelForumDiskEditor),
                        'users' => self::_sortTeam($_diskAdmin),
                    ),
                );
            //}
        //}
        return $team;
    }
    /**
     * �԰����Ŷӵ����ݽ�������
	*/
    private static function _sortTeam($arr)
    {
        if (empty($arr))return $arr;
        foreach ($arr as $key=>$value)
        {
            $uid[$key] = $value['user_id'];
            $uname[$key] = $value['user_name'];
            $loginTime[$key] = $value['last_login_time'];
        }    
        array_multisort($loginTime,SORT_DESC,$uid,SORT_ASC,$uname,SORT_ASC,$arr);
        return $arr;
    }
    /**
     * ȡ����
     *
     * @return unknown
     */
    public static function teams2()
    {
        $fid = ThreadData::getRequest('forum_id');
        //assist
        $sub_admins = memberRpc::getForumManager($fid,PermConfig2::$roles['FORUM_ASSIST']);
        $pic_admins = memberRpc::getForumManager($fid,PermConfig2::$roles['FORUM_PICADMIN']);
        $video_admins = memberRpc::getForumManager($fid,PermConfig2::$roles['FORUM_VIDEOADMIN']);
        /*
        $sub_admins = memberRpc::getForumManager($fid,memberRpc::PERM_FORUM_ASSIST);
        $pic_admins = memberRpc::getForumManager($fid,memberRpc::PERM_FORUM_PICADMIN);
        $video_admins = memberRpc::getForumManager($fid,memberRpc::PERM_FORUM_VIDEOADMIN);
        */
        $teams = array(
                'sub_admin'=>array(),
                'pic_admin'=>array(),
                'video_admin'=>array(),
            );
        //get uids
        $uids = array();
        if (!empty($sub_admins))
        {
            foreach ($sub_admins as $_u)
            {
                $uids[] = $_u['uid'];
            }
        }
        if (!empty($pic_admins))
        {
            foreach ($pic_admins as $_u)
            {
                $uids[] = $_u['uid'];
            }
        }
        if (!empty($video_admins))
        {
            foreach ($video_admins as $_u)
            {
                $uids[] = $_u['uid'];
            }
        }
        if (empty($uids))
        {
            return $teams;
        }
        else 
        {
            //Bawu_Log::notice('BawuResource::teams():get user num = ' . count($uids));
            //get user lastlogintime
            $uidsInfo = fucenterRpc::getUserinfoByUids($uids);
            //build uid =>lastlogin_time hash
            $uidHash = array();
            if (!empty($uidsInfo))
            {
                foreach ($uidsInfo as $_uinfo)
                {
                    $uidHash[$_uinfo['uid']] = $_uinfo['lastlogin_time'];
                }
                //build output
                if (!empty($sub_admins))
                {
                    foreach ($sub_admins as $_u)
                    {
                        $teams['sub_admin'][] = array(
                            'user_name'=>$_u['username'],
                            'last_login_time'=>$uidHash[$_u['uid']],
                        );
                    }
                }
                if (!empty($pic_admins))
                {
                    foreach ($pic_admins as $_u)
                    {
                        $teams['pic_admin'][] = array(
                            'user_name'=>$_u['username'],
                            'last_login_time'=>$uidHash[$_u['uid']],
                        );
                    }
                }
                if (!empty($video_admins))
                {
                    foreach ($video_admins as $_u)
                    {
                        $teams['vedio_admin'][] = array(
                            'user_name'=>$_u['username'],
                            'last_login_time'=>$uidHash[$_u['uid']],
                        );
                    }
                }
            }
        }
        Bawu_Log::notice('BawuResource::teams():sub_admin size =' . count($teams['sub_admin']).
            ',vedio_admin = ' . count($teams['vedio_admin']) . 
            ',pic_admin = ' . count($teams['pic_admin'])
        );
        return $teams;
    }
    /**
     * ���IP������Դ
	*/
    public static function filterIp()
    {
        $fid = ThreadData::getRequest('forum_id');
        $nowPage = BawuRequest::getNowPage();
        $pageSize = BawuRequest::getPageSize(10);
        $tbs = BawuUrlToken::encode(cmCommandNo::CancelFilterForumIP);
        $rs = self::_getFilter($fid,2,$nowPage,$pageSize,$tbs);
        $rs['tbs_ip'] = $tbs;
        return $rs;
    }
    /**
     * ����û���������Դ
	*/
    public static function filterUser()
    {
        $fid = ThreadData::getRequest('forum_id');
        $nowPage = BawuRequest::getNowPage();
        $pageSize = BawuRequest::getPageSize(10);
        $tbs = BawuUrlToken::encode(cmCommandNo::CancelFilterForumUser);
        $rs = self::_getFilter($fid,1,$nowPage,$pageSize,$tbs);
        $rs['tbs_user'] = $tbs;
        return $rs;
    }
    
    private static function _getFilter($fid,$type,$nowPage,$pageSize,$tbs)
    {
        $_start = ($nowPage-1)*$pageSize;
        //        $_end = $_start+$pageSize-1;
        $_end = $pageSize;
        //������������
        $arrConditions = self::_buildFilterSearchParams();
        //$rs = memberRpc::getForumFilter($fid,$type,$_start,$_end,$arrConditions);

        if (isset($arrConditions['username'])){
            //$uid = fucenterRpc::getUseridByUname($arrConditions['username']);
			 $uid = PassportUserInfo::getUidByUname($arrConditions['username']);
            if ($uid == 0 || $uid === false){ 
                $uid = -1; 
            }   
        } else {
            $uid = 0;
        }   

        if (isset($arrConditions['search_name'])){
            //$op_uid = fucenterRpc::getUseridByUname($arrConditions['search_name']);
			 $op_uid = PassportUserInfo::getUidByUname($arrConditions['search_name']);
            if ($op_uid == 0 || $op_uid === false){ 
                $op_uid = -1; 
            }   
        } else {
            $op_uid = 0;
        }   

        if (isset($arrConditions['op_time_lower'])){
            $op_start_time = $arrConditions['op_time_lower'];
        } else {
            $op_start_time = 0;
        }   

        if (isset($arrConditions['op_time_higher'])){
            $op_end_time = $arrConditions['op_time_higher'];
        } else {
            $op_end_time = 0;
        }

        $search_type = 0;

        //$rs = PermServer::getForumBlock($type, $fid, 0, 0, $_start, $_end, TRUE);
        $rs = PermServer::getForumBlock($type, $fid, $uid, 0, $op_uid, $op_start_time, $op_end_time, $search_type, $_start, $_end, TRUE);

        if (empty($rs['user_block_list']['total']) || $rs['user_block_list']['total'] == 0)
        {
            Bawu_Log::notice('getFilter num=0,type=['.$type.']');
            return array(
            	'cur_page'=>1,
                'total_page'=>0,
            	'total_num'=>0,
                'data'=>array(),
            );
        }
        $arrRs = $rs['user_block_list'];
        $_total = $arrRs['total'];
        $_totalPage = ceil($_total/$pageSize);
        // �������� add by xiechao01
        $arrRs = $arrRs['data'];
        foreach ($arrRs as $arrVal){
        	$intBlockType = $arrVal['block_type'];
        	if ($intBlockType == 2 || $intBlockType == 8){
        		$intUserNameIsIp = 1;
        	} else {
        		$intUserNameIsIp = 0;
        	}
        	$intUserID = $arrVal['block']['user_id'];
            $strUserName = $arrVal['block']['user_name'];
        	if ($intBlockType == 2 || $intBlockType == 8){
                $strUserName = Phpbean_Util::long2ip($intUserID);
                // ��ʾIP�Ĳ�ͬ��ʽ
                // ������Ƿ��ID&IP
                if ($arrVal['ip_type'] == 0){
                    if (strpos($strUserName, '.') > 0 ){
                        $strTmp = explode('.', $strUserName);
                        $strUserName = $strTmp[0] . "." . $strTmp[1] . "." . $strTmp[2] . ".*";
                    }
                } else {
                    if (strpos($strUserName, '.') > 0 ){
                        $strTmp = explode('.', $strUserName);
                        $strUserName = $strTmp[0] . ".*.*.*";
                    } 
                }
        	}
            $intOpUserID = $arrVal['op_user']['user_id'];
            $strOpUserName = $arrVal['op_user']['user_name'];
            if ( $arrVal['is_by_pm'] == 0){
                $intOpPm = 0;
            } else {
                $intOpPm = 1;
            }
            
            $intIpType = $arrVal['ip_type'];
        	$intOpTime = $arrVal['start_time'];
            $intPrisonTime = ($arrVal['end_time'] - $arrVal['start_time'])/86400;
            $intPrisonTime = intval($intPrisonTime);
        	$arrData[] = Array(
        		'user_id' => $intUserID,
        		'user_name' => $strUserName,
        		'user_ip' => $intUserID,
        		'user_name_is_ip' => $intUserNameIsIp,
        		'prison_time' => $intPrisonTime,
        		'operate_by_pm' => $intOpPm,
        		'operator' => $strOpUserName,
        		'operate_time' => $intOpTime,
        		'ip_type' => $intIpType,
        	);
        }
        Bawu_Log::notice('getFilter num=['.$_total.'],type=['.$type.']');
        return array(
            'cur_page' => $nowPage,
    		'total_page' => $_totalPage,
    		'total_num' => $_total,
            'data'=>$arrData,
        );
    }
    
    private static function _buildFilterSearchParams()
    {
        $searchType = BawuRequest::getSearchType();         
        $searchKeyword = BawuRequest::getKeyword();
        $searchKey = array();
        if (empty($searchKeyword))
        {
            return $searchKey;
        }
        switch ($searchType)
        {
            case 'operate_time':
                //split
                list($begin,$end) = explode('/',$searchKeyword);
                $begin = Phpbean_Filter::int($begin);
                $end = Phpbean_Filter::int($end);
                if ($end == 0)
                {
                    $end = Phpbean_Util::time();
                }
                if ($end<$begin)
                {
                    $end = $begin;
                }
                if ( !empty($begin) || !empty($end) )
                {
                    $searchKey = array(
                        'op_time_lower'=>$begin,
                        'op_time_higher'=>$end,
                    );
                }
                break;
            case 'operator':
                if (!empty($searchKeyword))
                {
                    $searchKey = array(
                        'search_name'=>$searchKeyword,
                    );
                }
                break;
            case 'user_name':
                $searchKey = array(
                    'username'=>$searchKeyword,
                );
                break;
        }
        return $searchKey;
    }
    /**
     * PB����û�IP��ʱ����Ҫ��������Դ
	*/
    public static function getFilterParams()
    {
        //ip or un
        $filterType = strtolower(Phpbean_Filter::noTags(BawuRequest::get('type','ban_ip')));
        $ipInt = '';
        $ip = '';
        $isPhoneIp = FALSE;
        if ('get_tbs'==$filterType)
        {
            //ip
            $ipInt = Phpbean_Filter::int(BawuRequest::get('user_name',''));
            $ipSecureStr = '';
            $ip = Phpbean_Util::long2ip($ipInt);
            if ($ip)
            {
                //check if is phone_ip,at the end
            }
            else
            {
                //ip invalid
                Bawu_Log::warning('getFilterParams():input id invalid!ip['.$ipInt.'],ipstr['.$ip.']');
            }
        }
        elseif ('get_ip_tbs'==$filterType)
        {
            $_un = Phpbean_Filter::noTags(BawuRequest::getGbk('user_name',FALSE));
            if ( $_un || $_un == '0' )
            {
                //get ip
                $userinfo = fucenterRpc::getUserInfoByUname($_un);
                if ($userinfo)
                {
                    $ipInt = $userinfo['lastlogin_ip'];
                    $ipSecureStr = BawuUrlToken::encodeIp($ipInt);
                    $ip = Phpbean_Util::long2ip($ipInt);
                }
            }
            else 
            {
                //not found
                Bawu_Log::warning('getFilterParams():input uname invalid!uname['.$_un.']');
            }
	}
	elseif ('get_post_ip' == $filterType) 
	{
		$intTid = intval($_GET['tid']);
		$intPid = intval($_GET['pid']);
		require_once 'Rpc/Pblogic.php';
		$arrRet = Rpc_Pblogic::getPosts($intTid, $intPid, 0, 1);
		if ($arrRet && isset($arrRet['post_infos'][0]['ip'])) {
			$ipInt = intval($arrRet['post_infos'][0]['ip']);
			$ipSecureStr = BawuUrlToken::encodeIp($ipInt);
			$ip = Phpbean_Util::long2ip($ipInt);
		}
	}
        //check if is phone_ip
        $isPhoneIp = TiebaUtil::isWapIp($ip,BAWU_DATA_ROOT_PATH . Bawu_Global::WAP_IP_DICT_FILEPATH);
        //$isPhoneIp = TRUE;
        $ip = TiebaUtil::formatIp($ip);
	
	// edit  by huangling02 , SECURE ISSUE

	return array(
    	    	'ip' => '*.*.*.*', //��211.19.1.*
    		'ip_int' => '',//TiebaUtil :: getEncodeIp($ipInt), //23423434
    		'tbs_ban_user' => BawuUrlToken::encode(cmCommandNo::FilterForumUser),
    		'tbs_ban_ip' => '',//BawuUrlToken::encode(cmCommandNo::FilterForumIP),
    		'is_phone_ip' => $isPhoneIp, //�Ƿ����ֻ�ip
            	'ip_secure_str' => '',//$ipSecureStr,
        );        
	// edit  by huangling02 , SECURE ISSUE end
    }

    /**
     * 
$shenshou = array(
	��is_open�� => true,//�Ƿ��Ѿ�����
	��log_num�� => 10,//�����¼����
);
/**
     *
     */
    public static function getShenShou()
    {
    	$fid = ThreadData::getRequest('forum_id');
    	$bolRet = RpcIdlFds::hasModule($fid);
    	$bolOpen = RpcIdlFds::hasOpen($fid);
    	if (! $bolRet) {
    		ThreadData::error(cmErrorNo::USER_PERM_ERROR,cmErrorMsg::USER_PERM_ERROR);
    		Bawu_Log::warning('getShenShou error!not open');
    	}
    	if (empty($bolRet)){
    		$bolRet = 0;
    	}
    	if (empty($bolOpen)){
    		$bolOpen = 0;
    	}
    	
    	return array(
    		'is_open' => $bolRet,
    		'is_start' => $bolOpen,
    		'is_system_start'=>self::_isSystemStartShenshou(),
    		'log_num' => self::_getLogNum(),
    	);
    }
    
    //�����Ƿ�Ϊpm�Ĳ������ж��Ƿ�Ϊ�Զ��������� by hfx
    public static function _isSystemStartShenshou()
    {
    	$isSystemStart = false;
    	
    	$fid = ThreadData::getRequest('forum_id');
    	
    	$bolOpen = RpcIdlFds::hasOpen($fid);
    	
    	if($bolOpen)
    	{
    		$arrRes = RpcIdlFds::getLog($fid, 0, 1); //��ȡ���µ�һ������
    		
    		if($arrRes[0]['op_is_pm'] == 1)
    			$isSystemStart = true;
    	}
    	
    	return $isSystemStart;
    }
    
    public static function getShenShouLog()
    {
    	$fid = ThreadData::getRequest('forum_id');
    	$bolRet = RpcIdlFds::hasModule($fid);
    	if (! $bolRet) {
    		ThreadData::error(cmErrorNo::USER_PERM_ERROR,cmErrorMsg::USER_PERM_ERROR);
    		Bawu_Log::warning('getShenShou error!not open');
    		$arrJson = array(
    			'cur_page' => 1,
    			'total_num' => 0,
    			'total_page' => 0,
    			'data' => array(),
    		);
    		return $arrJson;
    	}
    	$intNum = self::_getLogNum();
    	$fid = ThreadData::getRequest('forum_id');
    	$arrRet = array();
    	if ($intNum > 0) {
    		$pn = isset($_GET['pn'])?intval($_GET['pn']):1;
    		$rn = isset($_GET['page_size'])?intval($_GET['page_size']):5;
    		if ($pn <1) $pn = 1;
    		$arrRes = RpcIdlFds::getLog($fid, ($pn-1)*$rn, $rn);
    		$_arrTmp = array();
    		foreach($arrRes as $node) {
    			$_arrTmp = $node;
    			$_arrTmp['log_status'] = intval( $node['end_time'] > time() );
    			$_arrTmp['operator'] = $_arrTmp['op_uname'];
    			$_arrTmp['operate_by_pm'] = intval($_arrTmp['op_is_pm']);
    			$arrRet[] = $_arrTmp;
    		}
    	}
    	$arrJson = array(
    		'cur_page' => $pn,
    		'total_num' => $intNum,
    		'total_page' => ceil($intNum/$rn),
    		'data' => $arrRet,
    	);
    	return $arrJson;
    }
    
/**
     * 
$shenshou = array(
	��is_open�� => true,//�Ƿ��Ѿ��ž�ֹ�ܡ�
	��log_num�� => 10,//�����¼����
);
/**
     *
     */
    public static function getJzw()
    {
    	$fid = ThreadData::getRequest('forum_id');
    	$bolRet = RpcIdlFds::hasJzwModule($fid);
    	$bolOpen = RpcIdlFds::hasJzwOpen($fid);
    	if (! $bolRet) {
    		ThreadData::error(cmErrorNo::USER_PERM_ERROR,cmErrorMsg::USER_PERM_ERROR);
    		Bawu_Log::warning('getShenShou error!not open');
    	}
    	if (empty($bolRet)){
    		$bolRet = 0;
    	}
    	if (empty($bolOpen)){
    		$bolOpen = 0;
    	}
    	
    	
    	return array(
    		'is_open' => $bolRet,
    		'is_start' => $bolOpen,
    		'is_system_start' => self::_isSystemStartJzw(),
    		'log_num' => self::_getJzwLogNum(),
    	);
    }
    
    //�����Ƿ�Ϊpm�Ĳ������ж��Ƿ�Ϊ�Զ�������ֹ��  by hfx
    public static function _isSystemStartJzw()
    {
    	$isSystemStart = false;
    	$fid = ThreadData::getRequest('forum_id');
    	$bolOpen = RpcIdlFds::hasJzwOpen($fid);
    	if($bolOpen)
    	{
    		$arrRes = RpcIdlFds::getJzwLog($fid, 0, 1); //��ȡ���µ�һ������
    		
    		if($arrRes[0]['op_is_pm'] == 1)
    			$isSystemStart = true;
    	}
    	
    	return $isSystemStart;
    }
    
    public static function getJzwLog()
    {
    	$fid = ThreadData::getRequest('forum_id');
    	$bolRet = RpcIdlFds::hasJzwModule($fid);
    	if (! $bolRet) {
    		ThreadData::error(cmErrorNo::USER_PERM_ERROR,cmErrorMsg::USER_PERM_ERROR);
    		Bawu_Log::warning('getShenShou error!not open');
    		$arrJson = array(
    			'cur_page' => 1,
    			'total_num' => 0,
    			'total_page' => 0,
    			'data' => array(),
    		);
    		return $arrJson;
    	}
    	$intNum = self::_getJzwLogNum();
    	$fid = ThreadData::getRequest('forum_id');
    	$arrRet = array();
    	if ($intNum > 0) {
    		$pn = isset($_GET['pn'])?intval($_GET['pn']):1;
    		$rn = isset($_GET['rn'])?intval($_GET['page_size']):20;
    		if ($pn <1) $pn = 1;
    		$arrRes = RpcIdlFds::getJzwLog($fid, ($pn-1)*$rn, $rn);
    		$_arrTmp = array();
    		foreach($arrRes as $node) {
    			$_arrTmp = $node;
    			$_arrTmp['jzw_status'] = intval( $node['end_time'] > time() );
    			$_arrTmp['operator'] = $_arrTmp['op_uname'];
    			$_arrTmp['operate_by_pm'] = intval($_arrTmp['op_is_pm']);
    			$_arrTmp['ban_date'] = intval($_arrTmp['thread_threshold']);
    			$arrRet[] = $_arrTmp;
    		}
    	}
    	$arrJson = array(
    		'cur_page' => $pn,
    		'total_num' => $intNum,
    		'total_page' => ceil($intNum/$rn),
    		'data' => $arrRet,
    	);
    	return $arrJson;
    }
    
    protected static $_intLogNum = null;
    protected static function _getLogNum()
    {
    	if (is_null(self::$_intLogNum)) {
    		$fid = ThreadData::getRequest('forum_id');
    		self::$_intLogNum = RpcIdlFds::getLogTotalNum($fid);
    	}
    	return self::$_intLogNum;
    }
    
	protected static $_intJzwLogNum = null;
    protected static function _getJzwLogNum()
    {
    	if (is_null(self::$_intJzwLogNum)) {
    		$fid = ThreadData::getRequest('forum_id');
    		self::$_intJzwLogNum = RpcIdlFds::getJzwLogTotalNum($fid);
    	}
    	return self::$_intJzwLogNum;
    }
    
    public static function getgradeuser()
	{
		$varForumID = ThreadData::getRequest('forum_id','',FALSE);
		$varStart = ThreadData::getRequest('start','',FALSE);
		$varLimit = ThreadData::getRequest('limit','',FALSE);
		
		if (empty($varStart)) $varStart = 0;
		if (empty($varLimit)) $varLimit = fameHallRpc::MAXPAGESIZE ;
		if ( fameHallRpc::MAXPAGESIZE < $varLimit ) $varLimit = fameHallRpc::MAXPAGESIZE ;
		
		$retList = gradeLevelRpc::getUserFameHall($varForumID,$varStart,$varLimit);
		
		//get fame hall user list 
		$retFameHallList = fameHallRpc::getUserFameHall($varForumID);
		
		if (!empty($retFameHallList)) 
		{
			foreach ($retFameHallList as $varItem)
			{
				$varFameHallIDs[] = $varItem['member_id'];
			}
		}
		
		if (!empty($retList)) 
		{
			foreach ($retList as $varKey => $varItem) 
			{
				if (in_array($varItem['user_id'],$varFameHallIDs)) 
				{
					unset($retList[$varKey]);
					continue;
				}
				
				$varIDList[] = $varItem['user_id'];
			}
			
			if (!empty($varIDList)) 
			{
				$retUserInfo = RpcIdlFucenter::getUserinfoByUids($varIDList);
				
				if (!empty($retUserInfo)) 
				{
					foreach ($retUserInfo as $varInfo) 
					{
						$varUserInfo[$varInfo['uid']] = $varInfo['username'];
					}
				}
			}
			
			foreach ($retList as $varKey => $varItem) 
			{
				$retList[$varKey]['uname'] = $varUserInfo[$varItem['user_id']];
			}
		}
		
		return $retList;
	}
	
	public static function getrecommanduser()
	{
		$varForumID = ThreadData::getRequest('forum_id','',FALSE);
		$varStart = ThreadData::getRequest('start','',FALSE);
		$varLimit = ThreadData::getRequest('limit','',FALSE);
		
		if (empty($varStart)) $varStart = 0;
		if (empty($varLimit)) $varLimit = fameHallRpc::MAXPAGESIZE ;
		if ( fameHallRpc::MAXPAGESIZE < $varLimit ) $varLimit = fameHallRpc::MAXPAGESIZE ;
		
		$retList = fameHallRpc::getRecommandUser($varForumID,$varStart,$varLimit);
		
		if (!empty($retList)) 
		{
			foreach ($retList as $varKey => $varItem) 
			{
				$varIDList[] = $varItem['member_id'];
			}
			
			if (!empty($varIDList)) 
			{
				$retUserInfo = RpcIdlFucenter::getUserinfoByUids($varIDList);
				
				if (!empty($retUserInfo)) 
				{
					foreach ($retUserInfo as $varInfo) 
					{
						$varUserInfo[$varInfo['uid']] = $varInfo['username'];
					}
				}
			}
			
			foreach ($retList as $varKey => $varItem) 
			{
				$retList[$varKey]['uname'] = $varUserInfo[$varItem['member_id']];
			}
		}
		
		return $retList;
	}
	
	public static function getrecommandstatus()
	{
		$result = fameHallRpc::getStatus();
		
		if (empty($result)) 
		{
			return false;
		}
		else 
		{
			return true;
		}
	}


	//��ȡ�����������ݵ��첽api
	public static function gettbdiskthreads()
	{
		//��id,ֱ�Ӵ�ȫ�ֶ�������������
		$intForumId  = Phpbean_Filter::int(ThreadData::getRequest('forum_id','',FALSE));
		//��ǰҳ,�������Ѿ������жϺ����λ�����
		$intNowPage  = BawuRequest::getNowPage();
		//ÿҳ��С,�������Ѿ������жϺ����λ�����
		$intPageSize = BawuRequest::getPageSize();
		//��������,�ֳ����࣬�Ѵ����ȴ���������վ,Ĭ����ʾ�Ѿ�����
		$status = BawuRequest::get("status","tobehandled"); 
		switch(strval($status)){
			 case 'tobehandled':
				 $is_process = 0;
				 $is_delete  = 0;
				 break;
			 case 'handled':
				 $is_process = 1;
				 $is_delete  = 0;
				 break;
			 case 'recycle':
				 $is_process = 1;
				 $is_delete  = 1;
				 break;
			 default:
				 $is_process = 0;
				 $is_delete= 0 ;
		}
		/**
		 * ��ȡ������������
		 * @param int  $intForumid
		 * @param int  $is_process
		 * @param int  $intNowPage
		 * @param int  $intPageSize
		 * @return array
		 */
		$res=tbdiskRpc::bawu_get_disk_resources_list($intForumId,$is_process,$intNowPage,$intPageSize,$is_delete);
		//���ݰ�id��ȡ������Ϣ������ֵΪ����
		$arrTmp=tbdiskRpc::getClassify($intForumId);
		//���ݰ�id��ȡ���̵�������Ϣ
		$arrConfig=tbdiskRpc::getForumConfig($intForumId);
		//��ʼ���յķ�������
		$arrData=array();	
		//�����жϵ���ʱclassid����
		$arrClassIds=array();
		//������id�Լ�����������$arrData['classify']������
		foreach($arrTmp['classifys'] as $key_c=> $value)
		{
			$arrData['classify'][$key_c]['id']   = $value['classify_id']; 
			$arrData['classify'][$key_c]['name'] = $value['classify_name']; 
			$arrClassIds[]=$value['classify_id'];
		}
		
		//��������Ϣ�洢$arrData['data']������
		foreach($res['output']['threads'] as $key => $oneData)
		{
			//��ȡ����thread_id
			$arrData['data'][$key]['tid']       = $oneData['thread_id'];
			//��ȡ����post_id
			$arrData['data'][$key]['pid']       = $oneData['post_id'];
			//��ȡ���ӱ���
			$arrData['data'][$key]['title']     = $oneData['thread_name'];
			//��ȡ���ӵ����ش���
			$arrData['data'][$key]['downloads'] = $oneData['download_total_num'];
			//��ȡ�ļ��ܴ�С
			$arrData['data'][$key]['size']     = intval($oneData['total_size']);
			//��ȡ����ʱ��,����id����0�ľ������ñ��棬�����������
			if(Phpbean_Filter::int($oneData['files'][0]['classify_id'])>0 && in_array(Phpbean_Filter::int($oneData['files'][0]['classify_id']),$arrClassIds))
			{
				$arrData['data'][$key]['timeleft']     = "����";
			}
			else{
				$intTime = Phpbean_Filter::int($oneData['create_time'])+Phpbean_Filter::int($arrConfig['expire_time']) - time();
				if($intTime<0)
				{
					$arrData['data'][$key]['timeleft']="�ѹ���";
				}else{
					$arrData['data'][$key]['timeleft']= Rpc_tbdisk::sec2time($intTime);;
				}
				
			}
			//��ȡ�����ļ����ļ����Լ��ļ�����
			foreach($oneData['files'] as $key_f => $tmpData)
			{
				 $arrData['data'][$key]['files'][$key_f]['name']=$tmpData['filename'];
				 $arrData['data'][$key]['files'][$key_f]['type']=$tmpData['filetype'];
			}
			$arrData['data'][$key]['classify_id']     = $oneData['classify_id'];
			$arrData['data'][$key]['classify_name']     = $oneData['classify_name'];
		}

		//��ȡҳ����Ҫ���ֶ�
		$arrData['total_page']      = Phpbean_Filter::int($res['output']['total_page_num']);
		$arrData['total_num']       = Phpbean_Filter::int($res['output']['resources_count']);
		$arrData['cur_page']        = Phpbean_Filter::int($res['output']['page_cur']);
		$arrData['status']          = $status;

		$arrData['handled_num']     = Phpbean_Filter::int($res["record_count"]["process_count"]);
		$arrData['tobehandled_num'] = Phpbean_Filter::int($res["record_count"]["not_process_count"]);
		$arrData['recycle_num']     = Phpbean_Filter::int($res["record_count"]["delete_count"]);
		
		$arrData['tbs'] = Tieba_Tbs::gene(true); 
		//�ͷŽ����
		unset($res);
		unset($arrTmp);
		unset($arrConfig);
		unset($arrClassIds);
		//���ع���õ���������
		return $arrData;
	}



}
