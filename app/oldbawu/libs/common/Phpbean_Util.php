<?php
/**
 * util
 * <AUTHOR>
 * @package phpbean
 *
 */
class Phpbean_Util
{
    /**
     * ��ȡʱ�䣬����registry���л���
	*/
    public static function time()
    {
        $_key = '_GLOBAL_TIME';
        if (Phpbean_Registry::isExist($_key))
        {
            return Phpbean_Registry::get($_key);
        }
        else
        {
            Phpbean_Registry::set($_key,time(),TRUE);
            return Phpbean_Registry::get($_key);
        }
    }
    /**
     * ��ȡIP��ַ������registry���л���
	*/
    public static function getIp()
    {
        $_key = '_GLOBAL_IP';
        if (Phpbean_Registry::isExist($_key))
        {
            return Phpbean_Registry::get($_key);
        }
        else
        {
            $ip = self::ip2long(self::_getIp());
            Phpbean_Registry::set($_key,$ip,TRUE);
            return $ip;
        }
    }
    private static function _getIp()
    {
        return HttpIpRequest::getConnectIp();
    }
    
	/**
     * ip2long
     *
     * @param String $ip
     * @return long
     */
    public static function ip2long($ip)
    {
        return ip2long(implode('.', array_reverse(explode('.', $ip))));
    }
    
    /**
     * long2ip
     *
     * @param long $iplong
     * @return String
     */
    public static function long2ip($iplong)
    {
        $ipStr = long2ip($iplong);
        if ($ipStr)
        {
            return implode('.',array_reverse(explode('.',$ipStr)));
        }
        else 
        {
            return FALSE;
        }
    }
}