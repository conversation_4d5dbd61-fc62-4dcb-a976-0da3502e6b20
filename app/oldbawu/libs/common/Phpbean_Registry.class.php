<?php
/**
 * registry
 * <AUTHOR>
 * @package phpbean
 *
 */
class Phpbean_Registry{
	
   	private static $_registry=array();
   	
   	public static function set($name,$obj,$over=false)
   	{ 
   		if ( !$over && isset(self::$_registry[$name]) )
   		{
   			trigger_error('Phpbean_Registry:Unable to register var,'.$name.' is exists!',E_USER_WARNING);
   		}
   		else 
   		{
   		    self::$_registry[$name]=$obj;
   		    return $obj;
   		}
   	}
   
   	public static function get($name)
   	{
   		if(isset(self::$_registry[$name]))
   		{   		
   			return self::$_registry[$name];
   		}
   		return null;
   	}
   	
   	public static function isExist($name)
   	{
   		return isset(self::$_registry[$name]); 
   	}
   	
   	public static function getIfSet($name,$value='')
   	{
   		if (isset(self::$_registry[$name]))
   		{
   			return self::$_registry[$name];
   		}else
   		{
   			self::$_registry[$name] = $value;
   			return $value;
   		}
   	}
   
   	static public function remove($name)
   	{
   		if(self::isExist($name))
   		{
   			unset(self::$_registry[$name]);
   		}
   		return true;
   	}
}