<?php
/**
 * ��������ȫ������ά��
 * <AUTHOR>
 * @since 2009-08-30
 * @package forum-manager
 * 
 */
class ThreadData
{
    /**
     * ��������
     *
     * @var ��������
     */
    public static $request = array();
    /**
     * �������
     *
     * @var ��������
     */
    public static $response = array();
    
    /**
     * �����
     * 
     * @var int
     */
    public static $errno = 0;
    
    /**
     * ��������
     *
     * @var �ַ���
     */
    public static $errmsg = '';
    /**
     * ����ֵ
     *
     * @var int
     */
    public static $retNo = 0;
    
    /**
     * �ж��Ƿ�����˴���
     *
     * @return true/false
     */
    public static function isError()
    {
        if (0!=self::$errno)
        {
            return TRUE;
        }
        return FALSE;
    }
    /**
     * ��������
     *
     * @param int $_no
     * @param string $_msg
     */
    public static function error($_no,$_msg)
    {
        self::$errno = $_no;
        self::$errmsg = $_msg;
    }
    /**
     * ��ȡ���������������Ӧ�ı���ת������
     *
     * @param string $key
     * @param string $encode
     * @return string/array/resource
     */
    public static function getRequest($key,$encode='gbk',$warning=TRUE)
    {
        if (isset(self::$request[$key]))
        {
            $value = self::$request[$key];
            if (is_string($value))
            {
                if ( 'gbk' == $encode )
                {
                    //encode change
                    $value = rawurldecode($value);
                    $value = TiebaCharset::getGbk($value);
                }
            }
            return $value;
        }
        else 
        {
            if ($warning)trigger_error('GetRequest Failure!key ['.$key.'] is not exist!',E_USER_WARNING);
            return NULL;
        }
    }
    /**
     * �����������ݵ�Request����
     *
     * @param �ؼ��� $key
     * @param ���õ�ֵ $value
     * @param �Ƿ񸲸� $override
     * @return true/false
     */
    public static function setRequest($key,$value,$override=TRUE)
    {
        if ( isset(self::$request[$key]) && TRUE != $override )
        {
            trigger_error('Request Set Warning!key['.$key.'] is exist!',E_USER_WARNING);
            return FALSE;
        }
        else 
        {
            self::$request[$key] = $value;
            return TRUE;
        }
    }
    /**
     * ����������ݵ�Response����
     *
     * @param �ؼ��� $key
     * @param ���õ�ֵ $value
     * @param �Ƿ񸲸� $override
     * @return true/false
     */
    public static function setResponse($key,$value,$override=TRUE)
    {
        if ( isset(self::$response[$key]) && TRUE != $override )
        {
            trigger_error('Response Set Warning!key['.$key.'] is exist!',E_USER_WARNING);
            return FALSE;
        }
        else 
        {
            self::$response[$key] = $value;
            return TRUE;
        }
    }
    /**
     * ��response�����л�ȡ���
     *
     * @param �ؼ��� $key
     * @return value
     */
    public static function getResponse($key,$warning=FALSE)
    {
        if (isset(self::$response[$key]))
        {
            return self::$response[$key];
        }
        if ($warning)trigger_error('GetResponse Failure!key ['.$key.'] is not exist!',E_USER_WARNING);
        return NULL;
    }
}
