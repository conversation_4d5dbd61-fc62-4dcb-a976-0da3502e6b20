<?php
class BaWuFameHallDalConfig {
	public static $partition;  // db�ֱ�
    public static $process;  // db����
    public static $cache_base; // cache
    public static $cache;  // cache
}

/*
 * @brief �ֱ�
 */
BaWuFameHallDalConfig::$partition = array(
);

BaWuFameHallDalConfig::$process = array(
    /**
     * @brief ���ݰ�id��ȡ�û���Ϣ
     **/
    'get_user' => array(
        'type' => Bsql::TYPE_QUERY,
		'key' => array('forum_id','offset', 'pagesize'),
        'sql' => 'SELECT * FROM members WHERE forum_id = {forum_id:n} ORDER BY points DESC LIMIT {offset:n},{pagesize:n};',
    ),
    /**
     * @brief ��ȡ���б� cache data // less than 10000
     **/
    'get_forum_list' => array(
        'type' => Bsql::TYPE_QUERY,
        'sql' => 'SELECT forum_id FROM members GROUP BY forum_id ;',
    ),
    
    /**
     * @brief ��ȡĳһ�û���Ϣ
     **/
    'get_user_detail' => array(
        'type' => Bsql::TYPE_QUERY,
		'key' => array('forum_id','member_id'),
        'sql' => 'SELECT * FROM members WHERE forum_id = {forum_id:n} AND member_id = {member_id:n};',
    ),
    
    /**
     * @brief ���ݰ�id��ȡ��ѡ�����û���Ϣ
     **/
    'get_user_grade_level' => array(
        'type' => Bsql::TYPE_QUERY,
		'key' => array('forum_id','offset', 'pagesize'),
        'sql' => 'SELECT * FROM fame_user WHERE forum_id = {forum_id:n} ORDER BY score DESC LIMIT {offset:n},{pagesize:n};',
    ),
    
    /**
     * @brief ���û���Ϣ
     **/
    'add_user' => array(
        'type' => Bsql::TYPE_COMMIT,
		'key' => array('forum_id','points','member_id','data'),
		'opt_key' => array
    	(
    		'member_info' => ' ,member_info={member_info:s}',
    		'member_achi' => ', member_achi={member_achi:s}',
    		'member_boutique' => ', member_boutique={member_boutique:s}',
    	),
        'sql' => 'INSERT INTO members SET 
        		forum_id={forum_id:n},
        		member_id={member_id:n},
        		points={points:n},
        		data={data:n}
        		{member_info} 
        		{member_boutique} 
        		{member_achi}
        		ON DUPLICATE KEY UPDATE points=points;',
    ),
    
    /**
     * @brief �޸Ļ���Ϣ
     **/
    'set_user' => array(
        'type' => Bsql::TYPE_COMMIT,
		'key' => array('forum_id','member_id','points','data','member_info','member_achi','member_boutique'),
        'sql' => 'UPDATE members SET points={points:n},data={data:n},member_info={member_info:s} ,member_achi={member_achi:s},member_boutique={member_boutique:s} WHERE forum_id={forum_id:n} AND member_id={member_id:n};',
    ),
    
    /**
     * @brief ɾ���û���Ϣ
     **/
    'del_user' => array(
        'type' => Bsql::TYPE_COMMIT,
		'key' => array('forum_id','member_id'),
        'sql' => 'DELETE FROM members WHERE forum_id = {forum_id:n} AND member_id = {member_id:n} LIMIT 1;',
    ),
    
    /**
     * @brief ���ݰ�id��ȡ�û���Ϣ
     **/
    'get_recommand_user_detail' => array(
        'type' => Bsql::TYPE_QUERY,
		'key' => array('forum_id','member_id'),
		'opt_key' => array
    	(
    		'status' => ' AND status={status:n} ',
    	),
        'sql' => 'SELECT * FROM recommand_members WHERE forum_id = {forum_id:n} AND member_id ={member_id:n} {status} ;',
    ),
    
    /**
     * @brief ���ݰ�id��ȡ�û���Ϣ
     **/
    'get_recommand_user' => array(
        'type' => Bsql::TYPE_QUERY,
		'key' => array('forum_id','offset', 'pagesize'),
		'opt_key' => array
    	(
    		'status' => ' AND status={status:n} ',
    	),
        'sql' => 'SELECT * FROM recommand_members WHERE forum_id = {forum_id:n} {status} LIMIT {offset:n},{pagesize:n};',
    ),
    
    /**
     * @brief ����Ƽ��û���Ϣ
     **/
    'add_recommand_user' => array(
        'type' => Bsql::TYPE_COMMIT,
		'key' => array('forum_id','member_id','status','data'),
		'opt_key' => array
    	(
    		'member_info' => ' ,member_info={member_info:s}',
    		'member_achi' => ', member_achi={member_achi:s}',
    		'member_boutique' => ', member_boutique={member_boutique:s}',
    		'recommand' => ',recommand={recommand:n}',
    		'vote' => ', vote={vote:n}',
    	),
        'sql' => 'INSERT INTO recommand_members SET 
        		forum_id={forum_id:n},
        		member_id={member_id:n},
        		status={status:n},
        		data={data:n}
        		{member_info} 
        		{member_boutique} 
        		{member_achi}
        		{vote}
        		{recommand}
        		ON DUPLICATE KEY UPDATE status=0;',
    ),
    
    /**
     * @brief �޸Ļ���Ϣ
     **/
    'set_recommand_user' => array(
        'type' => Bsql::TYPE_COMMIT,
		'key' => array('forum_id','member_id','status','data','member_info','member_achi','member_boutique','vote','recommand'),
        'sql' => 'UPDATE recommand_members SET 
        		status={status:n},
        		data={data:n},
        		member_info={member_info:s} ,
        		member_achi={member_achi:s},
        		member_boutique={member_boutique:s},
        		vote={vote:n},
        		recommand={recommand:n} 
        		WHERE forum_id={forum_id:n} AND member_id={member_id:n};',
    ),
    
    /**
     * @brief ɾ���û���Ϣ
     **/
    'del_recommand_user' => array(
        'type' => Bsql::TYPE_COMMIT,
		'key' => array('forum_id','member_id'),
        'sql' => 'DELETE FROM recommand_members WHERE forum_id = {forum_id:n} AND member_id = {member_id:n} LIMIT 1;',
    ),
    
    
    /**
     * @brief ����״̬��Ϣ open
     **/
    'set_status' => array(
        'type' => Bsql::TYPE_COMMIT,
        'sql' => 'INSERT INTO status SET id = 1 , status = 1 ON DUPLICATE KEY UPDATE status = 1;',
    ),
    
    /**
     * @brief ɾ��״̬��Ϣ close
     **/
    'del_status' => array(
        'type' => Bsql::TYPE_COMMIT,
        'sql' => 'DELETE FROM status WHERE id = 1;',
    ),
    
    /**
     * @brief ��ȡ״̬��Ϣ
     **/
    'get_status' => array(
        'type' => Bsql::TYPE_QUERY,
        'sql' => 'SELECT * FROM status WHERE id = 1;',
    ),
    
    
);