<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2011-9-15
 * @version 
 */
class BaWuGradeLevelDal extends Bsql {
    protected $_log_mod = null;

    public function __construct($dbConn, $logMod = 'dal') {
		if ($dbConn === false || $dbConn===NULL){
			Bingo_Log::warning("BaWuGradeLevelDal::_construct: DB Error: not yet mysql connection. please check rpc configure", $this->_log_mod);
		}
		$dbConn->set_charset("GBK");
        parent::__construct($dbConn);
        $this->_log_mod = $logMod;
    }
    
    private function errLog($arrInput) {
    	$log_str = sprintf('cmd[%s] input[%s] fail[%s]', $arrInput['cmd'], print_r($arrInput, true), $this->getError());
        Bingo_Log::warning($log_str, $this->_log_mod);
        return true;
    }
    
    private function debugLog($arrInput, $mixSqlStmt) {
    	$log_str = sprintf('cmd[%s] sql[%s]', $arrInput['cmd'], str_replace(array("\n", "\r", "\t"), ' ', print_r($mixSqlStmt, true)));
    	Bingo_Log::debug($log_str, $this->_log_mod);
    	return true;
    }

    public function dalCall($arrInput) {
        $cmd = $arrInput['cmd'];
        if (!array_key_exists($cmd, BaWuFameHallDalConfig::$process)) {
        	$this->errLog($arrInput);
            return false;
        }
        $arrConf = BaWuFameHallDalConfig::$process[$cmd];

        // process
        if (isset($arrConf['proc'])) {
            $mixRes = $this->$arrConf['proc']($arrInput, $arrConf, BaWuFameHallDalConfig::$partition);
        } else {
        	switch ($arrConf['type']) {
        		case Bsql::TYPE_QUERY:
        			$arrConf = $this->_processOptField($arrInput, $arrConf, BaWuFameHallDalConfig::$partition);
        			if (isset($arrInput['_FROM_DB_MASTER'])) {
        			    $bolRet = $this->processSystem(array(), array('key' => array(), 'sql' => 'start transaction'), null);
				        if (! $bolRet) {
				    		$this->errLog($arrInput);
				    		return false;
				    	}       				
        			}
        			$mixRes = $this->processQuery($arrInput, $arrConf, BaWuFameHallDalConfig::$partition);
        			if (isset($arrInput['_FROM_DB_MASTER'])) {
        				$this->processSystem(array(), array('key' => array(), 'sql' => 'commit'), null);
        			}
        			break;
        		case Bsql::TYPE_COMMIT:
        			$arrConf = $this->_processOptField($arrInput, $arrConf, BaWuFameHallDalConfig::$partition);
        			if (is_array($arrConf['sql'])) {
        				$mixRes = $this->_processTransCommit($arrInput, $arrConf, BaWuFameHallDalConfig::$partition);
        			} else {
        				$mixRes = $this->processCommit($arrInput, $arrConf, BaWuFameHallDalConfig::$partition);
        			}
        			break;
        		case Bsql::TYPE_SYSTEM:
        			$mixRes = $this->processSystem($arrInput, $arrConf, BaWuFameHallDalConfig::$partition);
        			break;					
        		case Bsql::TYPE_SUBMIT:
        			$mixRes = $this->processSubmit($arrInput, $arrConf, BaWuFameHallDalConfig::$partition);
        			break;
        		case Bsql::TYPE_UPDATE:
        			$mixRes = $this->processUpdate($arrInput, $arrConf, BaWuFameHallDalConfig::$partition);
        			break;
        		case Bsql::TYPE_SUBMIT_PROCEDURE:
        			$mixRes = $this->processProcedure($arrInput, $arrConf, BaWuFameHallDalConfig::$partition);
        			break;
        		default:
        			return false;
        	}
        }
		//var_dump($mixRes);
        //var_dump($this->getError());
        if ($mixRes === false) {
			$this->errLog($arrInput);
            return false;
        }
        $this->debugLog($arrInput, $this->getSqlSource());
        return array('res' => $mixRes);
    }
    
    protected function _processOptField($arrInput, $arrConf, $arrTbnPart) {
		if (isset($arrConf['opt_key'])) {			
			$arrTmp = array_intersect_key($arrConf['opt_key'], $arrInput);			
	        $arrExistField = array();
	        $arrExistValue = array();
	        foreach ($arrTmp as $k => $v) {
	        	$arrExistField[] = "{{$k}}";
	        	$arrExistValue[] = $v;
	        	$arrConf['key'][] = $k; 
	        }
			$arrTmp = array_diff_key($arrConf['opt_key'], $arrInput);
		    $arrNotExistField = array();
	        foreach ($arrTmp as $k => $v) {
	        	$arrNotExistField[] = "{{$k}}";
	        }	        
	        
	        $bolMultiSql = is_array($arrConf['sql']);
	        if ($bolMultiSql) {
	        	$arrSql = $arrConf['sql'];
	        } else {
	        	$arrSql = array($arrConf['sql']);
	        }
	        foreach ($arrSql as $k => $strSql) {
		        if (count($arrExistField) > 0) {
		        	$strSql = str_replace($arrExistField, $arrExistValue, $strSql);
		        }
		        if (count($arrNotExistField) > 0) {
		        	$strSql = str_replace($arrNotExistField, '', $strSql);
		        }
		        $arrSql[$k] = $strSql;
	        }
	        if ($bolMultiSql) {
            	$arrConf['sql'] = $arrSql;
	        } else {
	        	$arrConf['sql'] = $arrSql[0];
	        }
        }
        
		return $arrConf;
    }
        
    protected function _processTransCommit($arrInput, $arrConf, $arrTbnPart) {   	
    	$arrTmpConf = $arrConf;
    	if (! is_array($arrTmpConf['sql'])) {
    		$arrTmpConf['sql'] = array($arrTmpConf['sql']);
    	}
    	$arrSql = $this->getSubmitSqls($arrInput, $arrTmpConf, $arrTbnPart);
    	if (! is_array($arrSql) || count($arrSql) < 1) {
    		$this->errLog($arrInput);
    		return false;    		
    	}
    	
    	$arrTmpConf['key'] = array();
    	unset($arrTmpConf['tbn']);
    	
        $bolRet = $this->processSystem(array(), array('key' => array(), 'sql' => 'start transaction'), null);
    	//Bingo_Log::notice("sql:{start transaction} res={$bolRet}", LOG_TYPE_RPC);
    	$this->debugLog($arrInput, $this->getSqlSource());
        if (! $bolRet) {
    		$this->errLog($arrInput);
    		return false;
    	}
    	foreach ($arrSql as $strSql) {
    		$arrTmpConf['sql'] = $strSql;
    		$mixedRes = $this->processCommit($arrInput, $arrTmpConf, $arrTbnPart);
    		//Bingo_Log::notice("sql:{$strSql} res={$mixedRes}", LOG_TYPE_RPC);
    		$this->debugLog($arrInput, $this->getSqlSource());
    		if ($mixedRes === false) {
    			$this->processSystem(array(), array('key' => array(), 'sql' => 'rollback'), null);
    			$this->errLog($arrInput);
    			return false;
    		}
    	}
    	$bolRet = $this->processSystem(array(), array('key' => array(), 'sql' => 'commit'), null);
    	//Bingo_Log::notice("sql:{commit} res={$bolRet}", LOG_TYPE_RPC);
    	return true;
    }
}
