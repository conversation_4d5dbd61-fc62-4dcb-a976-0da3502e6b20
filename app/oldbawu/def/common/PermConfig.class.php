<?php
/**
 * Ȩ�������ļ�
 * <AUTHOR>
 * @since 2009-09-02
 * @package forum-manager
 *
 */
class PermConfig 
{
    /**
     * ��ɫ=��Ȩ�޵Ĺ�ϣ��
     *
     * @var unknown_type
     */
    public static $role2perm = array();
    /**
     * cm�ύ�����Ȩ������
     *
     * @var unknown_type
     */
    public static $cmConfig = array();
    
    public static $allCmPermRoles = array();
    /**
     * ��������Ȩ�����ã�����ROUTER����hashӳ��
     *
     * @var unknown_type
     */
    public static $browseConfig = array();
    /**
     * ӵ��ȫ�����Ȩ�޵Ľ�ɫ
     *
     * @var unknown_type
     */
    public static $allBrowserPermRoles = array();
}
/**
 * ��ɫ��Ȩ��λ�Ķ�Ӧ��ϵ
*/
PermConfig::$role2perm = array(
    'assist'=>memberRpc::PERM_FORUM_ASSIST,
    'manager'=>memberRpc::PERM_FORUM_MANAGER,
    'member'=>memberRpc::PERM_FORUM_MEMBER,
    'pic_admin'=>memberRpc::PERM_FORUM_PICADMIN,
    'video_admin'=>memberRpc::PERM_FORUM_VIDEOADMIN,
    'root_manager'=>memberRpc::PERM_ROOT_MANAGER,
    'pm'=>memberRpc::PERM_ROOT_PM,
);

PermConfig::$cmConfig = array(
    cmCommandNo::MASK_DELETE=>array(),
    cmCommandNo::AddGoodClass=>array(),
    cmCommandNo::FilterForumIP=>array('assist'),
    cmCommandNo::FilterForumUser=>array('assist'),
);
PermConfig::$allCmPermRoles = array('pm','root_manager','manager');

PermConfig::$browseConfig = array(
    'info'=>array('member','assist','pic_admin','video_admin'),
    'log/build_info'=>array('member','assist','pic_admin','video_admin'),
	'log/protect_info'=>array('member','assist','pic_admin','video_admin'),
	'filter/get_ip_tbs'=>array('assist'),
);
PermConfig::$allBrowserPermRoles = array('pm','root_manager','manager');
