<?php
class NewPermConfig
{
	/**
	 * Router => PermFlag
	 * @var ����
	 */
	public static $arrBrowseConfig = array();
	/**
	 * command_no => PermFlag
	 * @var ����
	 */
	public static $arrCmConfig = array();
}

NewPermConfig::$arrBrowseConfig = array(
	'info' => CPermFlag::BROWSE_BAWU_INFO,
	'main' => CPermFlag::BROWSE_BAWU_CENTER,
	'log' => CPermFlag::BROWSE_BAWU_LOG,
	'shenshou' => CPermFlag::BROWSE_FDS,
	'jzw' => CPermFlag::BROWSE_FDS,
);

NewPermConfig::$arrCmConfig = array(
	cmCommandNo::SetForumPublication => CPermFlag::ADD_MANAGER_TEAM,
	cmCommandNo::SetForumVideoAdmin => CPermFlag::ADD_MANAGER_TEAM,
	cmCommandNo::SetForumPicAdmin => CPermFlag::ADD_MANAGER_TEAM,
	cmCommandNo::SetForumAssistant => CPermFlag::ADD_MANAGER_TEAM,
	
	cmCommandNo::GetForumPublication => CPermFlag::DEL_MANAGER_TEAM,
	cmCommandNo::DelForumAssistant => CPermFlag::DEL_MANAGER_TEAM,
	cmCommandNo::DelForumPicAdmin => CPermFlag::DEL_MANAGER_TEAM,
	cmCommandNo::DelForumVideoAdmin => CPermFlag::DEL_MANAGER_TEAM,
	
	cmCommandNo::AddGoodClass => CPermFlag::MANAGER_GOOD_CLASS,
	cmCommandNo::ReNameGoodClass => CPermFlag::MANAGER_GOOD_CLASS,
	cmCommandNo::DelGoodClass => CPermFlag::MANAGER_GOOD_CLASS,
	cmCommandNo::ResortGoodClass => CPermFlag::MANAGER_GOOD_CLASS,
	
	cmCommandNo::FilterForumIP => CPermFlag::FILTER_IP,
	cmCommandNo::CancelFilterForumIP => CPermFlag::FILTER_IP,
	cmCommandNo::FilterForumUser => CPermFlag::FILTER_ID,
	cmCommandNo::CancelFilterForumUser => CPermFlag::FILTER_ID,

	cmCommandNo::SetFds => CPermFlag::OP_FDS,
	cmCommandNo::DelFds => CPermFlag::OP_FDS,
	
	cmCommandNo::FrontCancelMaskDelete => CPermFlag::CANCEL_MAKE_DELETE,
);

