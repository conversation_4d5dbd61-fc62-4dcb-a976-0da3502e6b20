<?php
/**
 * Ȩ�������ļ�
 * <AUTHOR>
 * @since 2009-09-02
 * @package forum-manager
 *
 */
class PermConfig2
{
    /**
     * ��ɫ=��Ȩ�޵Ĺ�ϣ��
     *
     * @var unknown_type
     */
    public static $role2perm = array();
    /**
     * cm�ύ�����Ȩ������
     *
     * @var unknown_type
     */
    public static $cmConfig = array();

    /**
     * ��������Ȩ�����ã�����ROUTER����hashӳ��
     *
     * @var unknown_type
     */
    public static $browseConfig = array();
    /**
     * ȫ����ɫ
     *
     * @var unknown_type
     */
    public static $roles = array();
}
PermConfig2::$cmConfig = array(
    cmCommandNo::MASK_DELETE=>20000,
   // cmCommandNo::AddGoodClass=>20001,
   // cmCommandNo::FilterForumIP=>20002,
   // cmCommandNo::FilterForumUser=>20003,
    
    cmCommandNo::SetForumPublication => CPermFlag::ADD_MANAGER_TEAM,
    cmCommandNo::SetForumVideoAdmin => CPermFlag::ADD_MANAGER_TEAM,
    cmCommandNo::SetForumPicAdmin => CPermFlag::ADD_MANAGER_TEAM,
    cmCommandNo::SetForumAssistant => CPermFlag::ADD_MANAGER_TEAM,
    //����
    cmCommandNo::SetForumDiskEditor => CPermFlag::ADD_MANAGER_TEAM,
        
    cmCommandNo::GetForumPublication => CPermFlag::DEL_MANAGER_TEAM,
    cmCommandNo::DelForumAssistant => CPermFlag::DEL_MANAGER_TEAM,
    cmCommandNo::DelForumPicAdmin => CPermFlag::DEL_MANAGER_TEAM,
    cmCommandNo::DelForumVideoAdmin => CPermFlag::DEL_MANAGER_TEAM,
    //����
    cmCommandNo::DelForumDiskEditor => CPermFlag::DEL_MANAGER_TEAM,
        
    cmCommandNo::AddGoodClass => CPermFlag::MANAGER_GOOD_CLASS,
    cmCommandNo::ReNameGoodClass => CPermFlag::MANAGER_GOOD_CLASS,
    cmCommandNo::DelGoodClass => CPermFlag::MANAGER_GOOD_CLASS,
    cmCommandNo::ResortGoodClass => CPermFlag::MANAGER_GOOD_CLASS,
        
    cmCommandNo::FilterForumIP => CPermFlag::FILTER_IP,
    cmCommandNo::CancelFilterForumIP => CPermFlag::FILTER_IP,
    cmCommandNo::FilterForumUser => CPermFlag::FILTER_ID,
    cmCommandNo::CancelFilterForumUser => CPermFlag::FILTER_ID,

    cmCommandNo::SetFds => CPermFlag::OP_FDS,
    cmCommandNo::DelFds => CPermFlag::OP_FDS,

    cmCommandNo::SetJzwFds => CPermFlag::OP_FDS,
    cmCommandNo::DelJzwFds => CPermFlag::OP_FDS,
    
    cmCommandNo::FrontCancelMaskDelete => CPermFlag::CANCEL_MAKE_DELETE,
    
    cmCommandNo :: PostCommit => 10001,
	cmCommandNo :: ThreadCommit => 10001,
	cmCommandNo :: ForumCommit => 10001,
	cmCommandNo :: NewForumCommit => 10001,
	
	cmCommandNo :: MaskFilter => 10001,
	cmCommandNo :: CancelMaskFilter => 10001,
	cmCommandNo :: CancelMaskDelete => 10001,
	cmCommandNo :: RealDelete => 10001,
	cmCommandNo :: MaskRelease => 10001,
	cmCommandNo :: CancelMaskRelease => 10001,
	cmCommandNo :: MaskGood => 10001,
	cmCommandNo :: CancelMaskGood => 10001,
	cmCommandNo :: MaskTop => 10001,
	cmCommandNo :: CancelMaskTop => 10001,
	cmCommandNo :: DirApplySecClass => 10001,
	cmCommandNo :: FilterUser => 10001,
	cmCommandNo :: CancelFilterUser => 10001,
	cmCommandNo :: FilterIP => 10001,
	cmCommandNo :: CancelFilterIP => 10001,
	cmCommandNo :: ApplicationAdmin => 10001,
	cmCommandNo :: SetFriendsList => 10001,
	cmCommandNo :: AddFriend => 10001,
	cmCommandNo :: DelFriend => 10001,
	cmCommandNo :: SetFriendSecret => 10001,
	cmCommandNo :: ApplicationResg => 10001,
	cmCommandNo :: ZcCreatAlbum => 10001,
	cmCommandNo :: ZcDelAlbum => 10001,
	cmCommandNo :: ZcEditAlbum => 10001,
	cmCommandNo :: ZcSetAlbumCover => 10001,
	cmCommandNo :: ZcAlbumAddPic => 10001,
	cmCommandNo :: ZcAlbumDelPic => 10001,
	cmCommandNo :: ZcAlbumEditPic => 10001,
	cmCommandNo :: ZcAlbumMovPic => 10001,
	cmCommandNo :: ZcAlbumAdunfilPic => 10001,
	cmCommandNo :: ZcStarModTitle => 10001,
	cmCommandNo :: ZcStarAddUser => 10001,
	cmCommandNo :: ZcStarDelUser => 10001,
	cmCommandNo :: ZcStarSetTop => 10001,
	cmCommandNo :: ZcStarEditUser => 10001,
	cmCommandNo :: ZcStarFilUser => 10001,
	cmCommandNo :: ZcStarUnfilUser => 10001,
	cmCommandNo :: ZcStarDelTop => 10001,
	cmCommandNo :: ZCVideoUpdate => 10001,
	cmCommandNo :: ZCVideoDelete => 10001,
	cmCommandNo :: ZCVideoEdit => 10001,
	cmCommandNo :: ZCVideoRecover => 10001,
	cmCommandNo :: ZCVAlbumCreat => 10001,
	cmCommandNo :: ZCVAlbumDel => 10001,
	cmCommandNo :: ZCVAlbumEdit => 10001,
	cmCommandNo :: ZCAddCalendarEvent => 10001,
	cmCommandNo :: ZCDelCalendarEvent => 10001,
	cmCommandNo :: ZCModCalendarEvent => 10001,
	cmCommandNo :: ZCForumAddCalendar => 10001,
	cmCommandNo :: ZCForumDelCalendar => 10001,
	cmCommandNo :: ZCForumModCalendar => 10001,
	cmCommandNo :: ZCSetForumStyle => 10001,
	cmCommandNo :: ZCSetHeadLine => 10001,
	cmCommandNo :: ZCOpenHeadLine => 10001,
	cmCommandNo :: ZCCloseHeadLine => 10001,
	cmCommandNo :: ZCDispHeadLineUser => 10001,
	cmCommandNo :: ZCUnDispHeadLineUser => 10001,
	cmCommandNo :: ZCDispHeadLineAdmin => 10001,
	cmCommandNo :: ZCUnDispHeadLineAdmin => 10001,
	cmCommandNo :: MGR_AUDIT => 10001,
	cmCommandNo :: SetForumMember => 10001,
	cmCommandNo :: DelForumMember => 10001,
	cmCommandNo :: SetForumBlackByName => 10001,
	cmCommandNo :: DelForumMemberByName => 10001,
	cmCommandNo :: SetForumBlackFromApplier => 10001,
	cmCommandNo :: DelForumMemberSelf => 10001,
	cmCommandNo :: SetForumBlackFromMember => 10001,
	cmCommandNo :: DelForumBlacklist => 10001,
	cmCommandNo :: SetForumApplier => 10001,
	cmCommandNo :: DelFourmApplier => 10001,
	cmCommandNo :: ModDoorValue => 10001,
	cmCommandNo :: SetForumPublication => 10001,
	cmCommandNo :: GetForumPublication => 10001,
	cmCommandNo :: SetForumPublicationEditor => 10001,
	cmCommandNo :: DelForumPublicationEditor => 10001,

    cmCommandNo :: SetForumDaquan => 10001,
    cmCommandNo :: DelForumDaquan => 10001,
    cmCommandNo :: SetForumDaquanEditor => 10001,
    cmCommandNo :: DelForumDaquanEditor => 10001,    
    
    cmCommandNo :: addRecommandUser  => 10001,    
    cmCommandNo :: delRecommandUser  => 10001,    
    cmCommandNo :: setRecommandUser  => 10001,    
 cmCommandNo::delDiskClassify =>1888,
 cmCommandNo::setDiskClassify =>1888,
 cmCommandNo::createDiskClassify =>1888,
 cmCommandNo::renameDiskClassify =>1888,
 cmCommandNo::delDiskThread  =>1888,
 cmCommandNo::recDiskThread =>1888,

);

PermConfig2::$browseConfig = array(
	'log/build_info'=> CPermFlag::BROWSE_BAWU_INFO,
	'log/protect_info'=> CPermFlag::BROWSE_BAWU_INFO,
	'filter/get_ip_tbs'=>10003,
	'info' => CPermFlag::BROWSE_BAWU_INFO,
	'resource' => CPermFlag::BROWSE_BAWU_INFO,
    'resource_pic' => CPermFlag::BROWSE_BAWU_INFO,
	'main' => CPermFlag::BROWSE_BAWU_CENTER,
	'log' => CPermFlag::BROWSE_BAWU_LOG,
	'shenshou' => CPermFlag::BROWSE_FDS,
	'shenshou_apply' => CPermFlag::BROWSE_FDS,
//	'jzw' => CPermFlag::BROWSE_JZW_FDS,
	
	'log/shenshou' => 10001,
    'log/jzw' => 10001,
    'log/all'=>10001,
    'log/delete_thread'=>10001,
    'log/delete_cancel_thread'=>10001,
    'log/prison'=>10001,
    'log/thread_set_top'=>10001,
    'log/thread_set_cancel_top'=>10001,
    'log/member'=>10001,
    'log/thread_set_good'=>10001,
    'log/thread_set_cancel_good'=>10001,
    'log/team'=>10001,
    'log/video'=>10001,
    'log/pic'=>10001,
    'log/album'=>10001,
    'log/calendar'=>10001,
    //���IP
    'log/prison_ip'=>10001,
    'log/prison_user'=>10001,
    //��Ʒ��
    'add_good'=>10001,
    //
    'get_classes'=>10001,
    //��ȡ���ʱIP���û���ص���Ϣ
	'team'=>10001,
  	'famehall' => 10001,
	'disk'=>1888,
	'tbdisk/threadsinfo'=>1888,
);

PermConfig2::$roles = array(
    'FORUM_MANAGER' =>0x1,     // ����
    'FORUM_ASSIST' =>0x2,          // С����
    'FORUM_VIDEOADMIN' =>0x3,  // ��ƵС��
    'FORUM_PICADMIN' =>0x4,        // ͼƬС��
    'FORUM_PUBLICATION' =>0x5,     // �ɿ�С��
    'FORUM_PUBLICATION_EDITOR' =>0x6, //�ɿ�����

    'FORUM_DAQUAN_ZHUBIAN' => 0x8,
    'FORUM_DAQUAN_XIAOBIAN' => 0x7,
    'FORUM_DISK_EDITOR' => 0x9, //��������С��

    'FORUM_TOP_SETTER' =>0x10,    //�ö�ģ��༭Ա
    'FORUM_POST_DELETER' =>0x11,  //����ɾ��Ա
    'FORUM_VIP' =>0x12,  //VIP�û�
    'FORUM_MEMBER' =>0x10000,  // ��Ա
    'FORUM_CANDIDATE' =>0x10001,   // ������
    'FORUM_BLACKLIST' =>0x10002,   // ������

    'FORUM_MEMBER_APPLY' =>1005,
    'ROOT_MANAGER' =>      1006,//���ɺ�̨����Ա
    'ROOT_PM' =>           1007,//PMȨ��
);
