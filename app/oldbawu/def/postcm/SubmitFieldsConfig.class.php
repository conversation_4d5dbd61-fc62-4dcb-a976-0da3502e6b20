<?php
/**
 * �ύ��POSTCM���ֶ������ļ�
 * <AUTHOR>
 * @since 2009-08-28
 * @package forum-manager
 * todo 
 */
class SubmitFieldsConfig
{
    public static $Config = array();
    
    public static $msgConfig = array();
    
    public static $mustFields = array();
    
    public static function getMsgByRet($cm,$ret)
    {
        $key = $cm . '_' . $ret;
        if (isset(self::$msgConfig[$key]))
        {
            return self::$msgConfig[$key];
        }
        return '';
    }
    
    public static function get($cm)
    {
        if (isset(self::$Config[$cm]))
        {
            return array_merge(self::$Config[$cm],self::$mustFields);
        }
        return FALSE;
    }
}
SubmitFieldsConfig::$mustFields = array('command_no','word','ip','cookie');
SubmitFieldsConfig::$Config = array(  
    cmCommandNo::MASK_DELETE =>array('post_id','forum_id','thread_id','username',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uid'=>ThreadData::getRequest('op_uid'),'op_uname'=>ThreadData::getRequest('op_uname'),'fid'=>ThreadData::getRequest('forum_id'),'need_memo'=>1),'perm_flag'),
    //��Ʒ��
    cmCommandNo::AddGoodClass => array('title',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uid'=>ThreadData::getRequest('op_uid'),'op_uname'=>ThreadData::getRequest('op_uname'),'fid'=>ThreadData::getRequest('forum_id'),'forum_id'=>ThreadData::getRequest('forum_id'),'need_memo'=>1,'first_class_id'=>0,'thread_cap'=>-1,'second_class_name'=>ThreadData::getRequest('title')),'perm_flag'),
    cmCommandNo::ReNameGoodClass => array('title','post_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uid'=>ThreadData::getRequest('op_uid'),'op_uname'=>ThreadData::getRequest('op_uname'),'fid'=>ThreadData::getRequest('forum_id'),'forum_id'=>ThreadData::getRequest('forum_id'),'first_class_id'=>0,'second_class_id'=>ThreadData::getRequest('post_id'),'second_class_name'=>ThreadData::getRequest('title'))),
    cmCommandNo::DelGoodClass =>array('post_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uid'=>ThreadData::getRequest('op_uid'),'op_uname'=>ThreadData::getRequest('op_uname'),'fid'=>ThreadData::getRequest('forum_id'),'forum_id'=>ThreadData::getRequest('forum_id'),'need_memo'=>1,'first_class_id'=>0,'second_class_id'=>ThreadData::getRequest('post_id')),'perm_flag'),
    cmCommandNo::ResortGoodClass=>array('detail','post_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uid'=>ThreadData::getRequest('op_uid'),'op_uname'=>ThreadData::getRequest('op_uname'),'fid'=>ThreadData::getRequest('forum_id'),'forum_id'=>ThreadData::getRequest('forum_id'),'first_class_id'=>0),'second_class_id'),
    //�ύĿ¼����
    cmCommandNo::DirApplySecClass=>array('detail','op_uname','username',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'))),
    //�����ص�
    cmCommandNo::CancelFilterUser=>array('username','user_id','day_num',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag'),
    cmCommandNo::ApplicationResg=>array('detail',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'),'forum_id'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag'),
    //�����Ŷ����
    cmCommandNo::DelForumAssistant=>array('username','user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'forum_id'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag'),
    cmCommandNo::DelForumPicAdmin=>array('username','user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'forum_id'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag'),
    cmCommandNo::DelForumVideoAdmin=>array('username','user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'forum_id'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag'),
    cmCommandNo::SetForumAssistant=>array('username','user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'forum_id'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag'),
    cmCommandNo::SetForumPicAdmin=>array('username','user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'forum_id'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag'),
    cmCommandNo::SetForumVideoAdmin=>array('username','user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'forum_id'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag'),
    cmCommandNo::SetForumPublication=>array('username','user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag', 'forum_id'),
    cmCommandNo::GetForumPublication=>array('username','user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag', 'forum_id'),
    cmCommandNo::SetForumPublicationEditor=>array('username','user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag', 'forum_id'),
    cmCommandNo::DelForumPublicationEditor=>array('username','user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag', 'forum_id'),

    cmCommandNo::SetForumDaquan=>array('username','user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag', 'forum_id'),
    cmCommandNo::DelForumDaquan=>array('username','user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag', 'forum_id'),
    cmCommandNo::SetForumDaquanEditor=>array('username','user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag', 'forum_id'),
    cmCommandNo::DelForumDaquanEditor=>array('username','user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag', 'forum_id'),
    //����
    cmCommandNo::SetForumDiskEditor=>array('username','user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag', 'forum_id'),
    cmCommandNo::DelForumDiskEditor=>array('username','user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag', 'forum_id'),

    //���
    cmCommandNo::CancelFilterForumUser=>array('user_id','username','user_name',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'fid'=>ThreadData::getRequest('forum_id'),'need_memo'=>1,'op_time'=>Phpbean_Util::time()),'forum_id','op_uname','op_uid','perm_flag'),
    cmCommandNo::CancelFilterForumIP=>array('user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'fid'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1,'op_time'=>Phpbean_Util::time()),'forum_id','op_uname','op_uid','perm_flag'),
    cmCommandNo::FilterForumIP=>array('user_id','day_num',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'fid'=>ThreadData::getRequest('forum_id'),'need_memo'=>1,'op_time'=>Phpbean_Util::time()),'forum_id','op_uname','op_uid','perm_flag','ip_type'),
    cmCommandNo::FilterForumUser=>array('user_id','username','user_name','day_num',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'fid'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1,'op_time'=>Phpbean_Util::time()),'forum_id','op_uname','op_uid','perm_flag'),
    //ȡ��ɾ��
    cmCommandNo::FrontCancelMaskDelete=>array('thread_id','post_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'need_memo'=>1),'perm_flag'),
    //��������
    cmCommandNo::SetFds => array(array('opgroup'=>'bawu','op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'fid'=>ThreadData::getRequest('forum_id'), 'op_is_pm'=>ThreadData::getRequest('is_pm')), 'begin_time', 'end_time', 'op_uid', 'op_uname','grade'),
    cmCommandNo::DelFds => array(array('opgroup'=>'bawu','op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'now_time'=> Phpbean_Util::time())),

    //���ɾ�ֹ��
    cmCommandNo::SetJzwFds => array(array('opgroup'=>'bawu','op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'fid'=>ThreadData::getRequest('forum_id'), 'op_is_pm'=>ThreadData::getRequest('is_pm')), 'ban_num', 'begin_time', 'end_time', 'op_uid', 'op_uname','grade'),
    cmCommandNo::DelJzwFds => array(array('opgroup'=>'bawu','op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'), 'now_time'=> Phpbean_Util::time())),
    
    cmCommandNo::addRecommandUser => array('user_id','member_info','member_achi','member_boutique','batch','vote','recommand',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'))),
	cmCommandNo::setRecommandUser => array('user_id','member_info','member_achi','member_boutique','batch','vote','recommand',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'))),
	cmCommandNo::delRecommandUser => array('user_id',array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uid'=>ThreadData::getRequest('op_uid'),'op_uname'=>ThreadData::getRequest('op_uname'),'fid'=>ThreadData::getRequest('forum_id'))),

	//������ص������������
	cmCommandNo::delDiskClassify     => array(array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'forum_id' => ThreadData::getRequest('forum_id'),'fid'=>ThreadData::getRequest('forum_id')),'disk_classify_id'),
	cmCommandNo::delDiskThread    => array(array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'forum_id' => ThreadData::getRequest('forum_id'),'post_id'=>0,'op_uid'=>0,'fid'=>ThreadData::getRequest('forum_id')),'thread_id','username'), 
	cmCommandNo::recDiskThread    => array(array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'forum_id' => ThreadData::getRequest('forum_id'),'op_uid'=>0,'fid'=>ThreadData::getRequest('forum_id')),'thread_id','post_id','username'), 
	cmCommandNo::setDiskClassify     => array(array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'forum_id' => ThreadData::getRequest('forum_id'),'fid'=>ThreadData::getRequest('forum_id')),'disk_classify_id','thread_id','user_id'),
	cmCommandNo::createDiskClassify  => array(array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'),'forum_id' => ThreadData::getRequest('forum_id'),'disk_classify_id' => tbdiskRpc::getClassifyId()),'disk_classify_name'),
	cmCommandNo::renameDiskClassify  => array(array('op_ip'=>0,'role'=>0,'op_role'=>0,'op_module'=>0,'op_reason'=>'','register_time'=>0,'op_time'=>Phpbean_Util::time(),'op_uname'=>ThreadData::getRequest('op_uname'),'op_uid'=>ThreadData::getRequest('op_uid'),'fid'=>ThreadData::getRequest('forum_id'),'forum_id' => ThreadData::getRequest('forum_id')),'disk_classify_id','disk_classify_name'),


);

SubmitFieldsConfig::$msgConfig = array(
    '263_268'=>'ÿ��ֻ���ύһ�����ɷ������룬�������ɽ����Ѿ��ύ�������ˣ������ĵȴ���',
    '263_0'=>'�ύ�ɹ��������ĵȴ���ˣ�',
);
