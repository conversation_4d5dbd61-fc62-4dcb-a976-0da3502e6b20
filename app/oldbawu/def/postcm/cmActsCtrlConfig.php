<?php
/**
 * ���ȿ��Ƶ������ֶε������ļ�
 * <AUTHOR>
 * @since 2011-11-28
 * @package forum-manager
 *
 */
class cmActsCtrlConfig
{
    public static $config = array();
    
	public static function getConfig($cm)
    {
        $config = array();
        if (isset(self::$config[$cm]))
        {
            $config = self::$config[$cm];
        }
        return $config;
    }
}

cmActsCtrlConfig::$config = array(
   //���С�����������ȿ���
   cmCommandNo::SetForumAssistant => array(
       'checkRule' => array('ActionActsCtrlCheck','setForumAssCheck'),
       'actsInput' => array(
		   'forum_id'  => 'forum_id',
		   'op_uid'    => 'user_id',
       ),
       'actsFunc' => array(
           'cmd_no'   => cmCommandNo::SetForumAssistant,
       ),
   )
);