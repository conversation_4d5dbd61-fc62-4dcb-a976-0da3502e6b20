<?php
/**
 * �����ֶε������ļ�
 * <AUTHOR>
 * @since 2009-08-27
 * @package forum-manager
 *
 */
class cmInputConfig
{
    public static $config = array();
    /**
     * �����CM KEY
	*/
    public static $inputCmName = 'cm';
    /**
     * ����Ҫ��д���ֶ�
	*/
    public static $staticConfig = array();
    
    public static $filterConfig = array();
    
    public static function getConfig($cm)
    {
        $config = array();
        if (isset(self::$config[$cm]))
        {
            $config = self::$config[$cm];
        }
        return array_merge(self::$staticConfig,$config);
    }
    
    public static function getFilterconfig($field)
    {
        if (isset(self::$filterConfig[$field]))
        {
            return self::$filterConfig[$field];
        }
        return FALSE;
    }
}

cmInputConfig::$staticConfig = array(
    'fid'=>'forum_id',
    'word'=>'word',
    'tbs'=>'tbstoken',
);
cmInputConfig::$config = array(
    cmCommandNo::MASK_DELETE => array(
        
    ),
    //��Ʒ������
    cmCommandNo::AddGoodClass => array(
        'good_name'=>'title',
    ),
    cmCommandNo::ReNameGoodClass=>array(
        'good_name'=>'title',
        'good_id'=>'post_id',
    ),
    cmCommandNo::DelGoodClass=>array(
        'good_id'=>'post_id',
    ),
    cmCommandNo::ResortGoodClass=>array(
        'good_ids'=>'good_ids'
    ),
    //�ύ������Ϣ
    cmCommandNo::DirApplySecClass=>array(
        'first_class'=>'first_class',
        'second_class'=>'second_class',
    ),
    //�����ص�
    cmCommandNo::CancelFilterUser=>array(
        'user_name'=>'username',
        'user_id'=>'user_id',
        'day_num'=>'day_num'
    ),
    //����ǳ�
    cmCommandNo::ApplicationResg=>array(
        'resignation'=>'resignation',
    ),
    //�����Ŷӹ���
    cmCommandNo::DelForumAssistant=>array(
        'user_name'=>'username',
        'user_id'=>'user_id',
    ),
    cmCommandNo::DelForumPicAdmin=>array(
        'user_name'=>'username',
    	'user_id'=>'user_id',
    ),
    cmCommandNo::DelForumVideoAdmin=>array(
        'user_name'=>'username',
        'user_id'=>'user_id',
    ),
    cmCommandNo::SetForumAssistant=>array(
        'user_name'=>'username',
    ),
    cmCommandNo::SetForumPicAdmin=>array(
        'user_name'=>'username',
    ),
    cmCommandNo::SetForumVideoAdmin=>array(
        'user_name'=>'username',
    ),
    cmCommandNo::SetForumPublication=>array(
        'user_name'=>'username',
    ),
    cmCommandNo::GetForumPublication=>array(
        'user_name'=>'username',
    	'user_id'=>'user_id',
    ),
    cmCommandNo::SetForumPublicationEditor=>array(
        'user_name'=>'username',
    ),
    cmCommandNo::DelForumPublicationEditor=>array(
        'user_name'=>'username',
    	'user_id'=>'user_id',
    ),

    // add by xiechao01
    cmCommandNo::SetForumDaquan=>array(
        'user_name'=>'username',
    ),
    cmCommandNo::DelForumDaquan=>array(
        'user_name'=>'username',
        'user_id'=>'user_id',
    ),
    cmCommandNo::SetForumDaquanEditor=>array(
        'user_name'=>'username',
    ),
    cmCommandNo::DelForumDaquanEditor=>array(
        'user_name'=>'username',
        'user_id'=>'user_id',
    ),
   //��������С������
   cmCommandNo::SetForumDiskEditor=>array(
        'user_name'=>'username',
    ),
    //ɾ������С������
    cmCommandNo::DelForumDiskEditor=>array(
        'user_name'=>'username',
        'user_id'=>'user_id',
    ),



    //���
    cmCommandNo::CancelFilterForumUser=>array(
        'user_id'=>'user_id',
    	'user_name'=>'username',
    ),
    cmCommandNo::CancelFilterForumIP=>array(
        'user_id'=>'user_id',
    ),
    cmCommandNo::FilterForumIP=>array(
        'user_ip'=>'user_id',
        'ban_days'=>'day_num',
    ),
    cmCommandNo::FilterForumUser=>array(
        'user_name'=>'username',
        'ban_days'=>'day_num',
    ),
    ////ɾ���ָ�
    cmCommandNo::FrontCancelMaskDelete=>array(
        'thread_id'=>'thread_id',
        'post_id'=>'post_id',
    ),
    //��������
    cmCommandNo::SetFds => array(
    	'hour_num' => 'hour_num',
        'grade' =>'grade',
    ),
    cmCommandNo::DelFds => array(
    	
    ),
    
    //���ɾ�ֹ��
    cmCommandNo::SetJzwFds => array(
    	'hour_num' => 'hour_num',
    	'ban_num' => 'ban_num',
        'grade' =>'grade',
    ),
    cmCommandNo::DelJzwFds => array(
    	
    ),
    cmCommandNo::addRecommandUser => array(
    	'user_id'=>'user_id',
    	'member_info'=>'member_info',
    	'member_achi'=>'member_achi',
    	'member_boutique'=>'member_boutique',
    	'batch'=>'batch',
    	'vote'=>'vote',
    	'recommand'=>'recommand',
    ),
    cmCommandNo::setRecommandUser => array(
    	'user_id'=>'user_id',
    	'member_info'=>'member_info',
    	'member_achi'=>'member_achi',
    	'member_boutique'=>'member_boutique',
    	'batch'=>'batch',
    	'vote'=>'vote',
    	'recommand'=>'recommand',
    ),
    cmCommandNo::delRecommandUser => array(
    	'user_id'=>'user_id',
    ),

    //�������
    cmCommandNo::delDiskClassify  => array(
        'disk_classify_id'=>'disk_classify_id',
    ),
    cmCommandNo::setDiskClassify => array(
        'user_id'=>'user_id',
        'disk_classify_id'=>'disk_classify_id',
        'thread_id'=>"thread_id",
    ),
    cmCommandNo::createDiskClassify => array(
        'disk_classify_name'=>'disk_classify_name',
    ),
    cmCommandNo::renameDiskClassify => array(
        'disk_classify_id'=>"disk_classify_id",
        'disk_classify_name'=>"disk_classify_name",
   ),
   cmCommandNo::delDiskThread => array(
        'tid'=> 'thread_id',
        'uname'=>'username',
   ),
   cmCommandNo::recDiskThread => array(
        'tid'=> 'thread_id',
        'pid'=> 'post_id',
        'uname'=>'username',
   ),

    
);
cmInputConfig::$filterConfig = array(
    'forum_id'=>'int',
    'word'=>'nothing',
    'forum_name'=>'nothing',
	'grade'=>'int',
);
