<?php
/**
 * �����ļ�
 * <AUTHOR>
 * @since 2009-09-29
*/
class RuleRouterControllerConfig 
{
    public static $config = array();
}
/**
 * ��̬·��
 */
RuleRouterControllerConfig::$config['staticMapping'] = array(
	'cm'=>array(
	    'cmActionChain',
	    FRAMEWORK_ACTIONS_PATH.'/ActionChain.class.php',
	    array(
	        array(
	            'cmActionInit',
	            BAWU_UI_PATH . '/cm/ActionCmInit.class.php',
	            NULL,
	        ),
	        array(
	            'userLoginCheck',
	            BAWU_UI_PATH . '/common/ActionUserLogin.class.php',
	            array(
	            	'from'=>'cm',
	            ),
	        ),

	        array(
	            'userPermCheck',
	            BAWU_UI_PATH . '/common/ActionUserPerm.class.php',
	            array(
	            	'from'=>'cm',
	            ),
	        ),
	        array(
	            'cmPostDataCheck',
	            BAWU_UI_PATH . '/cm/ActionDataCheck.class.php',
	            NULL,
	        ),

	        array(
	            'cmUrlCheck',
	            BAWU_UI_PATH . '/cm/ActionUrlCheck.class.php',
	            NULL,
	        ),

	        array(
	            'cmActsCtrlCheck',
	            BAWU_UI_PATH . '/cm/ActionActsCtrlCheck.class.php',
	            NULL,
	        ),
	        array(
	            'cmProcess',
	            BAWU_UI_PATH . '/cm/ActionCmProcess.class.php',
	            NULL,
	        ),
	        array(
	            'cmSubmit',
	            BAWU_UI_PATH . '/cm/ActionSubmit.class.php',
	            NULL,
	        ),
	        array(
	            'cmBuildJson',
	            BAWU_UI_PATH . '/cm/ActionBuildJson.class.php',
	            NULL,
	        ),
	    ),
	),
);
/**
 * ·�ɹ���
 */
RuleRouterControllerConfig::$config['rules'] = array();
/**
 * ·��������
 */
RuleRouterControllerConfig::$config['routerConfig'] = array(
    'beginRouterIndex'=>1,//��ʼ�����index
	'sepOfRouter'=>'/',
    'sepOfParams'=>'-',
    'sepOfRouterAndParams'=>'-',
    'endOfParams'=>'.',//���鲻Ҫ�޸�
);
/**
 * router=>Action��map
 */
RuleRouterControllerConfig::$config['ruleMapping'] = array();
/**
 * Ĭ�ϴ���������Ӧ����routerΪ�յ����
 */
RuleRouterControllerConfig::$config['defaultAction'] = array();
/**
 * �������������Ҳ�����router���ڵ�Action��ʱ�򣬵��á�
 */
RuleRouterControllerConfig::$config['notFoundAction'] = array(
	'BrosweActionChain',
    FRAMEWORK_ACTIONS_PATH.'/ActionChain.class.php',
    array(
        array(
        	'init',
            BAWU_UI_PATH . '/browse/ActionBrowseInit.class.php',
            NULL,
        ),
        array(
            'loginCheck',
            BAWU_UI_PATH . '/common/ActionUserLogin.class.php',
            NULL,
        ),

        array(
            'permCheck',
            BAWU_UI_PATH . '/common/ActionUserPerm.class.php',
            NULL,
        ),

        array(
            'process',
            BAWU_UI_PATH . '/browse/ActionProcess.class.php',
            NULL,
        )
    ),
);
