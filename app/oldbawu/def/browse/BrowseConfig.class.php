<?php
/**
 * ��������ļ�
 * <AUTHOR>
 * @since 2009-09-02
 * @package forum-manager
 *
 */
class BrowseConfig
{
    /**
     * router=>resoure
     *
     * @var unknown_type
     */
    public static $resourceConfig = array();
    
    public static $tplConfig = array();
    
    public static $defaultTplConfig = array();
    
    public static $errorTplConfig = array();
}
BrowseConfig::$resourceConfig = array(
    'base'=>array('BawuResource','getBase'),
    'user'=>array('BawuResource','user'),
    'forum'=>array('BawuResource','forum'),
    'forum_good_classify'=>array('BawuResource','forumGoodClassify'),
    'all_dirs'=>array('BawuResource','classes'),
    'teams'=>array('BawuResource','teams'),
    'filter_ip'=>array('BawuResource','filterIp'),
	'filter_user'=>array('BawuResource','filterUser'),
    'filter_param'=>array('BawuResource','getFilterParams'),
//memo
    //'log_all'=>array('MemoResource','getTotal'),
    'log_cal'=>array('MemoResource','getCal'),
    'log_vedio'=>array('MemoResource','getVedio'),
    'log_member'=>array('MemoResource','getMmeber'),
    'log_pic'=>array('MemoResource','getPic'),
    'log_album'=>array('MemoResource','getAlbum'),
    'log_top'=>array('MemoResource','getTop'),
    'log_cancel_top'=>array('MemoResource','getCancelTop'),
    'log_del'=>array('MemoResource','getDel'),
    'log_cancel_del'=>array('MemoResource','getCancelDel'),
    'log_good'=>array('MemoResource','getGood'),
    'log_cancel_good'=>array('MemoResource','getCancelGood'),
    'log_filter'=>array('MemoResource','getFilter'),
    'log_team'=>array('MemoResource','getTeam'),
    'log_develop'=>array('MemoResource','getDevelop'),
    'log_safe'=>array('MemoResource','getSafe'),

	'shenshou' => array('BawuResource', 'getShenShou'),
	'shenshou_log' => array('BawuResource', 'getShenShouLog'),
	'jzw' => array('BawuResource', 'getJzw'),
	'jzw_log' => array('BawuResource', 'getJzwLog'),
	
	'getgradeuser' => array('BawuResource', 'getgradeuser'),
	'getrecommanduser' => array('BawuResource', 'getrecommanduser'),
	'getrecommandstatus' => array('BawuResource', 'getrecommandstatus'),
	//�������̵Ļص�����
	'gettbdiskthreads' => array('BawuResource','gettbdiskthreads'),
	'post_prefix' => array('BawuResource','getPostPrefix'),
	'new_bawu' => array('BawuResource','getNewBawu'),
);
BrowseConfig::$defaultTplConfig = array(
    'tpl'=>'bawu/error.tpl',
    'vars'=>array(
        'base'=>'base',
        'user'=>'user',
        'forum'=>'forum',
    )
);
BrowseConfig::$errorTplConfig = array(
    'page'=>BrowseConfig::$defaultTplConfig,
    'json'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'base',
        ),
    ),
);
/**
 * router to tpl
 */
BrowseConfig::$tplConfig = array(
    'info'=>array(
        'tpl'=>'bawu/info.tpl',
        'vars'=>array(
        	'base'=>'base',
        	'user'=>'user',
        	'forum'=>'forum',
        ),
    ),
    'resource'=>array(
        'tpl'=>'bawu/resource.tpl',
        'vars'=>array(
                'base'=>'base',
                'user'=>'user',
                'forum'=>'forum',
        ),
    ),

   'resource_pic'=>array(
        'tpl'=>'bawu/resource_pic.tpl',
        'vars'=>array(
                'base'=>'base',
                'user'=>'user',
                'forum'=>'forum',
        ),
    ),
    'main'=>array(
        'tpl'=>'bawu/main.tpl',
        'vars'=>array(
        	'base'=>'base',
        	'user'=>'user',
        	'forum'=>'forum',
            'prison_user'=>'filter_user',
            'teams'=>'teams',
            'log'=>'log_all',
		'post_prefix'=>'post_prefix',
		'new_bawu'=>'new_bawu',
        ),
    ),
    'log'=>array(
        'tpl'=>'bawu/log.tpl',
        'vars'=>array(
        	'base'=>'base',
        	'user'=>'user',
        	'forum'=>'forum',
        ),
    ),
    'get_classes'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'all_dirs',
        ),
    ),
    'shenshou' => array(
    	'tpl' => 'bawu/shenshou.tpl',
    	'vars' => array(
    		'base' => 'base',
    		'shenshou' => 'shenshou',
    		'jzw' => 'jzw',
    		'user'=>'user',
        	'forum'=>'forum',
    	),
    ),
    'shenshou_apply' => array(
    	'tpl' => 'bawu/shenshou_apply.tpl',
    	'vars' => array(
          'base'=>'base',
          'user'=>'user',
          'forum'=>'forum',
    	),
    ),
    //log 
    'log/shenshou' => array(
    	'tpl' => 'data/json.tpl',
    	'vars' => array(
    		'json_data' => 'shenshou_log',
    	),
    ),
    'log/jzw' => array(
    	'tpl' => 'data/json.tpl',
    	'vars' => array(
    		'json_data' => 'jzw_log',
    	),
    ),
    'log/all'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'log_all',
        ),
    ),
    'log/delete_cancel_thread'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'log_cancel_del',
        ),
    ),

    'log/delete_thread'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'log_del',
        ),
    ),
    'log/prison'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'log_filter',
        ),
    ),
    'log/thread_set_top'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'log_top',
        ),
    ),

    'log/thread_set_cancel_top'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'log_cancel_top',
        ),
    ),
    'log/member'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'log_member',
        ),
    ),
    'log/thread_set_cancel_good'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'log_cancel_good',
        ),
    ),

    'log/thread_set_good'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'log_good',
        ),
    ),
    'log/team'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'log_team',
        ),
    ),
    'log/video'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'log_vedio',
        ),
    ),
    'log/pic'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'log_pic',
        ),
    ),
    'log/album'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'log_album',
        ),
    ),
    'log/calendar'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'log_cal',
        ),
    ),
    'log/build_info'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'log_develop',
        ),
    ),
    'log/protect_info'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'log_safe',
        ),
    ),
    //���IP
    'log/prison_ip'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'filter_ip',
        ),
    ),
    'log/prison_user'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'filter_user',
        ),
    ),
    //��Ʒ��
    'add_good'=>array(
        'tpl'=>'bawu/edit_good.tpl',
        'vars'=>array(
            'base'=>'base',
        	'user'=>'user',
        	'forum'=>'forum',
            'forum_good_classify'=>'forum_good_classify',
        ),
    ),
    //
    'get_classes'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'all_dirs',
        ),
    ),
    //��ȡ���ʱIP���û���ص���Ϣ
    'filter/get_ip_tbs'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'filter_param',
        ),
    ),
    'team'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'teams',
        ),
    ),
    'famehall'=>array(
        'tpl'=>'bawu/famehall.tpl',
        'vars'=>array(
        	'getgradeuser' => 'getgradeuser',
			'getrecommanduser' => 'getrecommanduser',
			'getrecommandstatus'=>'getrecommandstatus',
			'base'=>'base',
        	'user'=>'user',
        	'forum'=>'forum',
        ),
    ),

    //���̵�չʾҳ��
    'disk'=>array(
        'tpl'=>'bawu/tbdisk.tpl',
        'vars'=>array(
                'base'=>'base',
                'user'=>'user',
                'forum'=>'forum',
        ),
    ),

    //����չʾҳ���ȡ���ݵ�api�ӿ�
     'tbdisk/threadsinfo'=>array(
        'tpl'=>'data/json.tpl',
        'vars'=>array(
            'json_data'=>'gettbdiskthreads',
        ),
   )
    
);
