<?php
/**
 * �������Ĵ���ҳ�棬���ݲ�ͬ��router�����û�ȡ��Ӧ����Դ����ʹ��smarty������Ⱦ�����
 * <AUTHOR>
 * @since 2009-09-06
 * @package forum-manager
 *
 */
class ActionProcess extends Action 
{
    private $_resourceConfig = array();
    
    private $_tplConfig = array();
    
    private $_defaultTplConfig = array();
    
    private $_errorTplConfig = array();
    
    public function execute($context, $actionParams = null) 
    {
        //config check
        if (!class_exists('BrowseConfig'))
        {
            Bawu_Log::warning('Load BrowseConfig Failure!can not find class BrowseConfig');
            header('location:'.Bawu_Global::ERROR_URL);
            return TRUE;
        }
        else 
        {
            //success            
            $this->_tplConfig = BrowseConfig::$tplConfig;
            $this->_defaultTplConfig = BrowseConfig::$defaultTplConfig;
            $this->_resourceConfig = BrowseConfig::$resourceConfig;
            $this->_errorTplConfig = BrowseConfig::$errorTplConfig;
        }
        $router = RequestUrl::getRouter();
        //get tpl config
        //Bawu_Log::notice('begin ActionProcess!router['.$router.']');
        $tplConfig = $this->_getTplConfig($router);
        //error process
		BawuTemplate::set('need_safe',0);
		if (!ThreadData::isError())
		{
			$isLogin = BawuSession::isLogin();
			if ($isLogin)
			{
				$word = ThreadData::getRequest('forum_name');
				$forum_name = $word;
				$user_name = BawuSession::getLoginUname();
				$ret = Rpc_SpringDefense::checkDefense($forum_name, $user_name);
				if( $ret == Rpc_SpringDefense::ERR_NO_HIT ||  $ret == Rpc_SpringDefense::ERR_PASS )
				{

				}
				else 
				{
					ThreadData::error($ret,cmErrorMsg::BROWSER_INIT_FAILURE);
				}
				Tieba_Stlog::addNode('spring', $ret);
                            
				$hit = ($ret == Rpc_SpringDefense::ERR_NO_HIT) ? 0 : 1;
				BawuTemplate::set('need_safe',$hit);
			}
		}
		if (ThreadData::isError())
        {
            Bawu_Log::warning('Process find a error!building error Page!errno['.
            ThreadData::$errno.'],msg:'.ThreadData::$errmsg);
            $tplConfig = $this->_errorTplConfig['page'];
        }     
        //get resource
        if (!empty($tplConfig) && isset($tplConfig['tpl']) )
        {
            $_tpl = $tplConfig['tpl'];
            if (isset($tplConfig['vars']))
            {
                $_vars = $tplConfig['vars'];
                if (!empty($_vars))
                {
                    foreach ($_vars as $_key=>$_resourceName)
                    {
                        $_resource = $this->_getResource($_resourceName);
                        if ($_resource)
                        {
                            //set page var
                            BawuTemplate::set($_key,$_resource);
                            //Bawu_Log::notice('set view param ['.$_key.']');
                        }
                    }
                }
            }
            //build page
            BawuTemplate::render($_tpl);
            Bawu_Log::notice('build page success!tpl['.$_tpl.'],router['.$router.']');
        }
        else
        {
            //tpl config error
            Bawu_Log::warning('parse tplConfig Failure!tplConfig empty or tpl empty!'.
                print_r($tplConfig,TRUE));
            header('location:'.Bawu_Global::ERROR_URL);
        }
        Bawu_Log::notice( Tieba_Stlog::notice() );

        return TRUE;
    }
    
    private function _getResource($resourceName)
    {
        if ( isset($this->_resourceConfig[$resourceName])
            && is_callable($this->_resourceConfig[$resourceName])
        )
        {
            $resource = call_user_func($this->_resourceConfig[$resourceName]);
            if (FALSE === $resource)
            {
                Bawu_Log::warning('Get resource Failure!resourceName['.$resourceName.']'.
                    'resourceConfig:'.print_r($this->_resourceConfig[$resourceName],TRUE).
                   	'ret[' . print_r($resource,TRUE) . ']'
                );
                return FALSE;
            }
            return $resource;
        }
        else
        {
        	if(!in_array($resourceName,array('log_all')))
        	{
           		 Bawu_Log::warning('Can not find Resource!resourceName['.$resourceName.']');
        	}
           	return FALSE;
        }
    }
    
    private function _getTplConfig($router)
    {
        if (isset($this->_tplConfig[$router]))
        {
            return $this->_tplConfig[$router];
        }
        else
        {
            return $this->_defaultTplConfig;        
        }
    }
}
