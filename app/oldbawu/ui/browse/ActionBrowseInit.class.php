<?php
/**
 * ��������ʼ������Ҫ�ǰ�����ID���
 * <AUTHOR>
 * @since 2009-09-02
 * @package forum-manager
 *
 */
class ActionBrowseInit extends Action
{
    public function execute($context, $actionParams = null) 
    {
        //todo charset change
        $word = '';
        $forum_id = 0;
        if (isset($_GET['word']))
        {
            $word = trim($_GET['word']);
        }
        if (isset($_GET['kw']))
        {
            $word = trim($_GET['kw']);
        }
        $word = rawurldecode($word);
        if (isset($_GET['fid']))
        {
            $forum_id = Phpbean_Filter::int($_GET['fid']);
        }
        
        if ( ( empty($word) && $word!='0') || empty($forum_id))
        {
            //TODO�������Ͱ�ID�Ƿ�ƥ��
            ThreadData::error(cmErrorNo::BROWSER_INIT_FAILURE,cmErrorMsg::BROWSER_INIT_FAILURE);
            Bawu_Log::warning('BrowseInit Failure!empty word or forum_id');
        }
        else 
        {                
            ThreadData::setRequest('word',$word,TRUE);
            ThreadData::setRequest('forum_name',$word,TRUE);
            //get forum_id,must set
            ThreadData::setRequest('forum_id',$forum_id,TRUE);        
            //set router
            ThreadData::setRequest('url_router',RequestUrl::getRouter(),TRUE);
        }
        return TRUE;       
    }
}
