<?php
/**
 * URLһ������֤��ע�⣬�������thread_id��post_id���������POST����ʾ�Ĵ��ݳ�����
 * ��Ҫ��thread_id��post_id�ڲ�ͬCMD�ִ��ڸ��á�
 * <AUTHOR>
 * @since 2009-08-27
 * @package forum-manager
 * todo 
 */
class ActionUrlCheck extends Action
{
    public function execute($context, $actionParams = null) 
    {
        //return if error
        if (ThreadData::isError())
        {
            return TRUE;
        }
        if ( TRUE === Bawu_Global::URL_CHECK_ENABLE)
        {
        	$tbstoken = Phpbean_Filter::noTags(ThreadData::getRequest('tbstoken'));
        	if (empty($tbstoken)) {
        		//������ˣ���ʵû�б�Ҫ
        		Bawu_Log::notice('no  need URL Check !');
        		return true;
        	}
            $cm = ThreadData::getRequest('command_no');
            $word = ThreadData::getRequest('word');
            $uid = ThreadData::getRequest('op_uid');
            $uname = ThreadData::getRequest('op_uname');
            $fid = ThreadData::getRequest('forum_id');
            $tid = $pid = 0;
            if (isset($_POST['thread_id']))$tid = Phpbean_Filter::int($_POST['thread_id']);
            if (isset($_POST['post_id']))$pid = Phpbean_Filter::int($_POST['post_id']);            
            
            if ( (TRUE === TiebaUrlToken::check($tbstoken,$cm,$uid,$word,$uname,$fid,$tid,$pid)) || (TRUE === TiebaUrlToken::check2($tbstoken)))
            {
                Bawu_Log::notice('URL Check pass!tbstoken[' . $tbstoken . '],cm[' . $cm .'],uid['.$uid.']');
	    }else 
            {
                //not pass
                Bawu_Log::warning(sprintf('URL Check Failure!tbstoken[%s],cm[%d],word[%s],uid[%d],uname[%s],fid[%d],tid[%d],pid[%d]',
                    $tbstoken,$cm,$word,$uid,$uname,$fid,$tid,$pid));
                ThreadData::error(cmErrorNo::URL_CHECK_ERROR,cmErrorMsg::URL_CHECK_ERROR);
            }
        }
        return TRUE;
    }
}
