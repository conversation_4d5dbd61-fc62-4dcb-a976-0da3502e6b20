<?php
/**
 * �����յĽ������<PERSON>son�������
 * <AUTHOR>
 * @since 2009-08-27
 * @package forum-manager
 * todo 
 */
class ActionBuildJson extends Action
{
    public function execute($context, $actionParams = null) 
    {
        //todo
        if (ThreadData::isError())
        {
            //�����û�е�¼����û��Ȩ��
            if (ThreadData::$errno == cmErrorNo::USER_LOGIN_ERROR ||
            ThreadData::$errno == cmErrorNo::USER_PERM_ERROR)
            {
                header('Status: 403');
                $rs = array(
                    'info'=>ThreadData::$errmsg,
                );
                BawuTemplate::set('json_data',$rs);
                BawuTemplate::render('data/json.tpl');
            }
            else 
            {
                $this->_output(0,'',ThreadData::$errno,ThreadData::$errmsg);
            }
            Bawu_Log::warning('submit error!errno['.ThreadData::$errno.'],msg['.ThreadData::$errmsg.']');
        }
        else 
        {
            //
            $cm = ThreadData::getRequest('command_no');
            $retNo = ThreadData::$retNo;
            $msg = SubmitFieldsConfig::getMsgByRet($cm,$retNo);
            $this->_output($retNo,$msg,0,$msg);
            Bawu_Log::notice('submit succ!cm['.$cm.'],retno['.$retNo.'],msg['.$msg.']');
        }
        $this->_log();
        return TRUE;
    }
    
    private function _log()
    {
        $cm = ThreadData::getRequest('command_no');
        $errno = ThreadData::$errno;
        $uid = ThreadData::getRequest('op_uid');
        $uname = ThreadData::getRequest('op_uname');
        
        $fid = ThreadData::getRequest('forum_id');
        $fname = ThreadData::getRequest('forum_name');
        
        $perm_flag = ThreadData::getRequest('perm_flag');
        if (empty($perm_flag)) $perm_flag = ThreadData::getRequest('op_perm');
        
        $router = RequestUrl::getRouter();
        $tbs = ThreadData::getRequest('tbstoken');
        
        
        $logStr = '[BAWU_SUBMIT:['.$cm.']';
        $logStr .= ' errno[' . $errno . '], uid[' . $uid . '], uname[' . $uname . '], fid[' . $fid . '], fname[' . $fname . ']';
        $logStr .= ', perm_flag[' . $perm_flag .'], router[' . $router . '], tbs[' . $tbs . ']';
        Bawu_Log::notice($logStr);
    }
    
    private function _output($retval,$retMsg,$errno,$errmsg)
    {
        $rs = array(
            'retval'=>$retval,
            'msg'=>$retMsg,
            'errno'=>$errno,
            'errmsg'=>$errmsg,
        );
        $rs = array('error'=>$rs);
        BawuTemplate::set('json_data',$rs);
        BawuTemplate::render('data/json.tpl');
    }
}