<?php
/**
 * ������ύ��POSTCM���ύ�ֶ��ǿ������õġ������ļ���SubmitFieldsConfig�С�
 * <AUTHOR>
 * @since 2009-08-28
 * @package forum-manager
 * 
 */
class ActionSubmit extends Action
{
    public function execute($context, $actionParams = null) 
    {
        if (ThreadData::isError())
        {
            return TRUE;
        }
        //todo
        $cm = ThreadData::getRequest('command_no');
        if ( $cm == cmCommandNo::FilterForumIP
            || $cm == cmCommandNo::CancelFilterForumIP
            || $cm == cmCommandNo::FilterForumUser
            || $cm == cmCommandNo::CancelFilterForumUser){
            return true;
        }
        $_fields = SubmitFieldsConfig::get($cm);
        if ($_fields!==FALSE && (!empty($_fields)))
        {
            $input = array();
            foreach ($_fields as $_field)
            {
                if (is_string($_field))
                {
                    if ($_field == 'detail')
                    {
                        $input[$_field] = ThreadData::$request[$_field];
                    }
                    else 
                    {
                        $input[$_field] = ThreadData::getRequest($_field);
                    }
                    if ( NULL === $input[$_field])
                    {
                        //warning
                        Bawu_Log::warning('SubmitToPostcm failure:'.$_field.' is empty!');
                    }
                }
                if (is_array($_field))
                {
                    foreach ($_field as $_key=>$_value)
                    {
                        $input[$_key] = $_value;
                    }
                }
                
            }
            //rpc
            //��need_memo�������⴦�?�ܶ���
//            $perm_flag = ThreadData::$request['perm_flag'];
            $user_id = ThreadData::$request['user_id'];
            $forum_id = ThreadData::$request['forum_id'];
            $input['perm_flag'] = 0;
            //$input['perm_flag'] = PERM_ROOT_MANAGER || PERM_ROOT_PM;
            //            if (TiebaPerm2::isManager($forum_id, $user_id))
            //�����IP�Ǻͷ��IDһ������ģ����IP������memo
            //�������½ӿڣ���Ҫ�ֶ����perm_flagȨ��λΪPM�����ܴ������塣��ע��
            if ( $cm == cmCommandNo::FilterForumIP
                || $cm == cmCommandNo::CancelFilterForumIP
                || $cm == cmCommandNo::FilterForumUser
                || $cm == cmCommandNo::CancelFilterForumUser){
                $input['perm_flag'] = 152;    
                }
            if (ThreadData::getRequest('is_pm') == 1)
            {   
                $input['need_memo'] = 0;
                $input['perm_flag'] = memberRpc::PERM_ROOT_PM;
            }

            if ( $cm == cmCommandNo::FilterForumIP && ThreadData::getResponse('__isFilterIpWithFilterUser'))
            {
                // Record Log
                $input['need_memo'] = 0;
                $input['perm_flag'] = memberRpc::PERM_ROOT_PM;
            }            
            if ( $cm == cmCommandNo::CancelFilterForumIP && ThreadData::getResponse('__isFilterIpWithFilterUser'))
            {
                $input['need_memo'] = 0;
                $input['perm_flag'] = memberRpc::PERM_ROOT_PM;
            }
            //if ($cm == cmCommandNo::SetForumPublication || $cm == cmCommandNo::GetForumPublication){
            	$input['real_ip'] = $input['ip'];
            	$input['now_time'] = Phpbean_Util::time();
        	//}
                	
            // �Ծ�ֹ�ܽ���һ�±�������ת��
        	if ($cm == cmCommandNo::SetJzwFds){
        		$input['thread_threshold'] = $input['ban_num'];
        	}

        	if (is_array($input['perm_flag']) && empty($input['perm_flag'])){
        		// ���Ȩ�������飬�ύ֮ǰ��ת��
                $input['perm_flag'] = 0;
        	}

	     if($cm==cmCommandNo::SetForumDiskEditor || $cm == cmCommandNo::DelForumDiskEditor)
	     {
		$input['role_id']=PermConfig2::$roles['FORUM_DISK_EDITOR'];
	     }


            // Ϊ��˼�¼ʹ��
            $input['resource_from'] = 8;
            $input['op_username'] = ThreadData::getRequest('op_uname');

            Bawu_Log::debug('INPUT : ' . serialize($input) );

            $ret = postcmRpc::baseCall($input);

            $errno = 0;
            if (isset($ret['err_no'])) $errno = $ret['err_no'];
            //Bawu_Log::notice('PostCM Submit,input:'.print_r($input,TRUE).',ret['.print_r($ret,TRUE).'],errno['.$errno.']');
            if ( FALSE === $ret )
            {
                Bawu_Log::warning('postcmRpc Rpc failure!input['.print_r($input,TRUE).']');
                ThreadData::error(cmErrorNo::SUBMIT_POSTCM_ERROR,cmErrorMsg::SUBMIT_POSTCM_ERROR);
            }
            else 
            {
                //true
                Bawu_Log::notice('POSTCM Rpc Succ!cm['.$cm.']');
                ThreadData::setResponse('rpcRet',$ret);
	    }
	    Tieba_Stlog::addNode('cm', $cm);//2012-12-12 luhua01
            Bawu_Log::notice(Tieba_Stlog::notice() . $strTimeLog);
            //retno
            ThreadData::$retNo = $errno;
        }
        else 
        {
            //no field
            Bawu_Log::warning('SubmitToPostcm failure:cm['.$cm.']');
            ThreadData::error(cmErrorNo::SUBMIT_POSTCM_ERROR,cmErrorMsg::SUBMIT_POSTCM_ERROR);
        }
        return TRUE;
    }
}
