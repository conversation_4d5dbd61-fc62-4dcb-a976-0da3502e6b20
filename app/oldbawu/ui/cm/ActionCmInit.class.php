<?php
/**
 * cm�ĳ�ʼ������ȡ����ţ��������������ݵ��ڲ����ݵ�ӳ��
 * <AUTHOR>
 * @since 2009-08-27
 * @package forum-manager
 *
 */
class ActionCmInit extends Action
{
    public function execute($context, $actionParams = null) 
    {
        $cmd = Phpbean_Filter::noTags($_POST[cmInputConfig::$inputCmName]);
        $cm = cmCommandNo::getCmd($cmd);
        if ( NULL == $cm)
        {
            //CM�������
            ThreadData::error(cmErrorNo::CM_CHECK_FAILURE,cmErrorMsg::CM_CHECK_FAILURE);
            Bawu_Log::warning('Command is invalid or not active!cm[' . $cm .'],cmd['.$cmd.']');
        }
        else 
        {
            //get inputConfig
            $config = $this->_getInputConfig($cm);
            if (!empty($config))
            {
                foreach ($config as $_input=>$_output)
                {
                    ThreadData::setRequest($_output,$this->_getInputVar($_input,$_output),TRUE);
                }
            }
            else 
            {
                Bawu_Log::warning('ActionCmInit:can not find InputConfig!cm['.$cm.']');
                ThreadData::error(cmErrorNo::CM_INIT_FAILURE,cmErrorMsg::CM_INIT_FAILURE);
            }
            //set ip        
            ThreadData::setRequest('ip',Phpbean_Util::getIp(),TRUE);
            //set cm
            ThreadData::setRequest('command_no',$cm,TRUE);
            //set forum_name
            ThreadData::setRequest('forum_name',ThreadData::getRequest('word'),TRUE);
        } 
        return TRUE;
    }
    /**
     * ��ȡ���˺�����ݣ�����key������Դ�����key�������˲���
     *
     * @param ����key $input
     * @param ���key $output
     * @return unknown
     */
    private function _getInputVar($input,$output)
    {
        if (!isset($_POST[$input]))
        {
            Bawu_Log::warning('GetInputVar:can not find input keyword!key['.$input.']');
            return NULL;
        }
        $filter = $this->_getFilterConfig($output);
        if (FALSE === $filter)
        {
            return $_POST[$input];
        }
        else
        {
            switch (trim($filter))
            {
                case 'int':
                    return Phpbean_Filter::int($_POST[$input]);
                case 'nothing':
                    return $_POST[$input];
                case 'string':
                default:
                    return Phpbean_Filter::noTags(rawurldecode($_POST[$input]));
            }
        }
    }
    
    private function _getInputConfig($cm)
    {
        return cmInputConfig::getConfig($cm);
    }
    
    private function _getFilterConfig($field)
    {
        return cmInputConfig::getFilterconfig($field);
    }
}
