<?php
/**
 * ���������飬���ύ��CM�����ݽ�����֤��Ŀǰʵ�ַ�ʽ�Ƚϲ
 * <AUTHOR>
 * @since 2009-08-27
 * @package forum-manager
 * todo 
 */
class ActionDataCheck extends Action
{
    private static $_ValidatorArray = array();
    
    public function execute($context, $actionParams = null) 
    {
        //return if error
        if (ThreadData::isError())
        {
            return TRUE;
        }
        //check REQUEST_METHOD
        $_method = strtolower(Phpbean_Filter::noTags($_SERVER['REQUEST_METHOD']));
        if ( ('post'!=$_method) || (empty(ThreadData::$request)) )
        {
            $this->_error(cmErrorNo::DATA_CHECK_ERROR,cmErrorMsg::DATA_CHECK_ERROR);
            Bawu_Log::warning('ActionDataCheck::execute():REQUEST_METHOD['.$_method.'] is not "post"');
            return TRUE;
        }
        //check 
        $command_no = ThreadData::getRequest('command_no');
        if (isset(cmValidatorConfig::$commandCheckFileds[$command_no]))
        {
            if (TRUE === $this->_validator($command_no))
            {
                //pass
                return TRUE;
            }
        }
        else
        {
            //$this->_error(cmErrorNo::DATA_CHECK_ERROR,cmErrorMsg::DATA_CHECK_ERROR);
            Bawu_Log::notice('ActionDataCheck::execute():can not find ValidatorFields!command_no['.$command_no.']');
        }
        return TRUE;
    }
    private function _validator($command_no)
    {
        $validatorFields = cmValidatorConfig::$commandCheckFileds[$command_no];
        //get all fields
        $validatorFields = array_merge($validatorFields,cmValidatorConfig::$mustCheck);
        //Bawu_Log::warning(print_r($validatorFields,TRUE));
        if (!empty($validatorFields))
        {
            $validator = new Validator(cmValidatorConfig::$breakOnFailure);
            foreach ($validatorFields as $_field)
            {
                /**
                 * TODO BADCODE ��Ҫ�Ż�
                 */
                //���ַ������Ҵ��ڶ�Ӧ�����ù���
                if ( (is_string($_field)) && isset(cmValidatorConfig::$fieldRules[$_field]))
                {
                    $_rule = cmValidatorConfig::$fieldRules[$_field];
                    if (!empty($_rule))
                    {
                        //get var
                        $_var = ThreadData::getRequest($_field);
                        //get validator
                        $_validator = $this->_getValidator($_rule['validator']);
                        $_msg = $_rule['error_msg'];
                        if(empty($_msg))$_msg = '[' . $_field .']';
                        $validator->addValidator($_var,$_validator,$_msg);
                        //Bawu_Log::notice('addValidator:field['.$_field.'],var['.$_var.']');               
                    }
                }
                //���������
                if (is_array($_field))
                {
                    foreach ($_field as $_f=>$_r)
                    {
                        if (!empty($_r))
                        {
                            //get var
                            $_var = ThreadData::getRequest($_f);
                            //get validator
                            $_validator = $this->_getValidator($_r['validator']);
                            $_msg = $_r['error_msg'];
                            if(empty($_msg))$_msg = '[' . $_f .']';
                            $validator->addValidator($_var,$_validator,$_msg);
                            //Bawu_Log::notice('addValidator:field['.$_f.'],var['.$_var.']');               
                        }        
                    }
                }                    
            }
            if ($validator->isValid())
            {
                Bawu_Log::notice('Validator pass!command_no['.$command_no.']');
                return TRUE;
            }
            else
            {
                //failure
                $this->_error(cmErrorNo::DATA_CHECK_ERROR,cmErrorMsg::DATA_CHECK_ERROR);
                Bawu_Log::warning('Validator failure!msg[' . $validator->getMessage2String(';') . ']');
                return FALSE;
            }
        }
        //Bawu_Log::notice('validator:validatorFields is empty');
        return FALSE;
    }
    /**
     * ��ȡvalidatorУ����
     *
     * @param ���� $key
     */
    private function _getValidator($key)
    {
        //Bawu_Log::warning('_getValidator:key['.var_export($key,TRUE).']');
        $validator = Validator::getValidator($key);
        //Bawu_Log::notice('_getValidator:key['.$key.'],validator['.print_r($validator,TRUE).']');
        return $validator;
    }
    
    private function _error($no,$msg)
    {
        ThreadData::error($no,$msg);
    }
}
