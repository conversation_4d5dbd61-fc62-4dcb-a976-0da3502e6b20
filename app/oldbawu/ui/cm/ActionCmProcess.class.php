<?php
/**
 * ���ù��ӻ��ƣ����ݲ�ͬ��������ò�ͬ�Ĵ�������
 * <AUTHOR>
 * @since 2009-08-27
 * @package forum-manager
 *
 */
class ActionCmProcess extends Action 
{
    private $_config = array();
    
    public function initial($initObject) 
    {
        if (class_exists('cmProcessConfig'))
        {
            $this->_config = cmProcessConfig::$config;    
        }
        else 
        {
            Bawu_Log::warning('Load cmProcessConfig Failure!Can not found cmProcessConfig');
        }
        if (empty($this->_config))
        {
            Bawu_Log::warning('ActionCmProcess::$config is empty');
        }
        return TRUE;
    }
    public function execute($context, $actionParams = null) 
    {
        if (ThreadData::isError())
        {
            return TRUE;
        }
        //todo
        $cm = ThreadData::getRequest('command_no');
        if ( isset($this->_config[$cm]) )
        {
            $cmProcess = $this->_config[$cm];
            if (is_callable($cmProcess))
            {
                $boolRs = call_user_func($cmProcess);
                if ($boolRs)
                {
                    Bawu_Log::notice('call_user_func execute success,cm['.$cm.'],cmProcess['.
                    print_r($cmProcess,TRUE).']');
                }
                else
                {
                    Bawu_Log::warning('call_user_func execute failure,cm['.$cm.'],ret['.$boolRs.'],cmprocess['.
                    print_r($cmProcess,TRUE).']');
                }
            }
            else 
            {
                Bawu_Log::warning('cmProcess can not execute!cm[' . $cm . '],cmProcess['.print_r($cmProcess,TRUE).']');
            }
        }
        else 
        {
            Bawu_Log::notice('No Process find!cm[' .$cm . ']' );
        }
    }
}
