<?php
define('LIBS_PATH',	ROOT_PATH . "/libs");
define('LIB_PATH',	ROOT_PATH . "/libs/lib");
define('CONF_PATH',	ROOT_PATH . "/conf/app/" . MODULE_NAME);
define('SRC_PATH',	ROOT_PATH . "/app/" . MODULE_NAME);
define('TEMPLATE_PATH',	ROOT_PATH . "/template/manager-apply");
define('IS_ORP_RUNTIME',true);

require_once ("Bd/Idc.php");

if(($current_idc = Bd_Idc::getCurrentIdcRoomOrDefault(null))) {
	define('M_CURRENT_CONF', $current_idc);
}
include_once (CONF_PATH . "/CommonConfig.class.php");
include_once (LIBS_PATH . "/bingo/common/env_init.php");
require_once (CONFIG_PATH.'/GlobalConfig.class.php');

if (!defined('REQUEST_ID')){
	$requestTime = gettimeofday();
	define('REQUEST_ID', (intval($requestTime['sec'] * 100000 + $requestTime['usec'] / 10) & 0x7FFFFFFF));    
}

if (function_exists('camel_set_logid')) {
	camel_set_logid(REQUEST_ID);
}   	
init_root_action();
framework_inital();

include_once (LIB_PATH . '/autoload/autoload.php');
$obj = MyAutoLoad::getInstance(AUTOLOAD_DATA_PATH);
$obj->add(LIB_PATH);
$obj->add(SRC_PATH);

if(!defined('IS_CRON_SCRIPT')) {
	include_once (USER_GLOBAL_INIT_FILE);
	$context->callAction($context->rootAction->actionID);
}

/* --------- functions ---------------- */
function init_root_action () {
	global $context;

	if (!defined('ACTION_DISPATCH_FILE') || !defined('ACTION_DISPATCH_METHOD')) {
		trigger_error (sprintf ('action dispatch def not find request is %s',$_SERVER['REQUEST_URI']),
				E_USER_WARNING);
		return false;
	}
	$rootAction[0] = ACTION_DISPATCH_METHOD;
	if (defined ('ACTION_DISPATCH_METHOD_FILE')) {
		$rootAction[1] = ACTION_DISPATCH_METHOD_FILE;
	}
	else {
		$rootAction[1] = FRAMEWORK_ACTIONS_PATH."/".ACTION_DISPATCH_METHOD.".class.php";
	}

	include_once (ACTION_DISPATCH_FILE);
	$class_name = ACTION_DISPATCH_METHOD.'Config';

	$tmp= get_class_vars($class_name);

	if (!isset($tmp['config'])) {
		return false;
	}
	$rootAction[2] = $tmp['config'];
	GlobalConfig::setRootAction($rootAction);

	return true;
}
