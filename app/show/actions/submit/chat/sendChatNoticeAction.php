<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-10-29 23:35:17
 * @comment json接口
 * @version
 */
class sendChatNoticeAction extends Util_Base {
    
    public function _execute(){
        try {
            /*
            if (true !== Bingo_Http_Request::isPost()){
                throw new Util_Exception("must post!",Tieba_Errcode::ERR_NOT_POST_METHOD);
            }*/
            //参数获取
            $room_id = intval(Bingo_Http_Request::get('room_id',0));
            $content = strval(Bingo_Http_Request::get('content',''));
            $stamp = intval(Bingo_Http_Request::get('stamp',0));
            $prop = intval(Bingo_Http_Request::get('prop',1));
            $count = intval(Bingo_Http_Request::get('count',1));
            if ($this->_arrUserInfo['is_login'] !== true ){
                //未登陆
                throw new Util_Exception("user need login!",Tieba_Errcode::ERR_USER_NOT_LOGIN);
            }
            $user_id = $this->_arrUserInfo['user_id'];
            
            
            if ($user_id <=0 ){
                throw new Util_Exception("user_id error!",Tieba_Errcode::ERR_PARAM_ERROR);
            }
            
            $arrInput = array(
                    'user_id'  => $user_id,
                    'room_id' => $room_id,
                    'content' => $content,
                    'stamp' => $stamp,
                    'count' => $count,
                    'prop' => $prop,
                    );
            //Bingo_Log::warning("ui---\n".print_r($arrInput,1) );
            $arrOut = Tieba_Service::call('show', 'sendChatNotice', $arrInput, NULL, NULL, 'post', 'php', 'utf-8','local');
			
            $this->_jsonRet($arrOut['errno'],$arrOut['errmsg'],$arrOut['data']);
        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), '未知错误');	
        }
    }
}
?>

