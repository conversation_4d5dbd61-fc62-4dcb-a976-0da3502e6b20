<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-05-14 23:35:17
 * @comment json接口 投诉
 * @version
 */
class reportAction extends Util_Base {
    protected static $_ui_ie = 'gbk';
    
    public function _execute(){
        try {
            if (true !== Bingo_Http_Request::isPost()){
                throw new Util_Exception("must post!",Tieba_Errcode::ERR_NOT_POST_METHOD);
            }
			$this->_getUserInfo();
            //参数获取
            $report_type = intval(Bingo_Http_Request::get('report_type',0));
            $room_id = intval(Bingo_Http_Request::get('room_id',0));
            $report_images = Bingo_Http_Request::get('report_images',array());
            Bingo_Log::pushNotice("room_id",$room_id);

            if($room_id<=0 || $report_type <=0 || empty($report_images))
            {
                throw new Util_Exception("user need login!",Tieba_Errcode::ERR_PARAM_ERROR);
            }

            if ($this->_arrUserInfo['is_login'] !== true ){
                //未登陆
                throw new Util_Exception("user need login!",Tieba_Errcode::ERR_USER_NOT_LOGIN);
            }

            $arrInput  = array('room_ids' => array($room_id));
            $arrRet = Tieba_Service::call('show', 'mgetRoomInfo', $arrInput, NULL, NULL, 'post', 'php', self::$_ui_ie, 'local');
            $roomInfo = array();
            if ($arrRet === false || !isset($arrRet['errno']) || $arrRet['errno'] != 0 || !isset($arrRet['res'][$room_id])) { 
                Bingo_Log::warning('mgetRoomInfo error! line:' . __LINE__);
                throw new Util_Exception("room not exist!", Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            } else {
                $roomInfo = $arrRet['res'][$room_id];
            }       
            $arrInput = array(
                'req' => array(
                    'report_type' => $report_type, // 举报类型 1 衣着暴露 2 非法内容 3 其他
                    'room_id'     => $room_id,
                    'user_id'     => $roomInfo['user_id'],
                    'user_name'   => $roomInfo['user_name'], //主播
                    'room_name'   => $roomInfo['room_name'], 
                    'forum_id'    => $roomInfo['forum_id'],
                    'forum_name'  => $roomInfo['forum_name'],
                    'room_pic'    => $roomInfo['pic'],
                    'media_id'    => $roomInfo['media_id'],
                    'report_uid'  => $this->_arrUserInfo['user_id'], //举报人id
                    'report_uname'=> $this->_arrUserInfo['user_name'], //举报人用户名
                    'report_images' => $report_images,
                )
            );

            $arrRet = Tieba_Service::call('uegmis', 'reportTbLive', $arrInput, NULL, NULL, 'post', 'php', self::$_ui_ie);
            if ($arrRet === false || $arrRet['errno'] != 0 ) {
                Bingo_Log::warning('ueg call error! line:' . __LINE__);
                throw new Util_Exception("ueg call error!", Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            $errno = Tieba_Errcode::ERR_SUCCESS;
            
            //打日志
            Bingo_Log::pushNotice("user_id",$user_id);
            Bingo_Log::pushNotice("fr",Bingo_Http_Request::get('fr',""));

            $this->_jsonRet($errno,Tieba_Error::getUserMsg($errno),array());
        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), Tieba_Error::getUserMsg($e->getCode()));	
        }
    }
}
?>
