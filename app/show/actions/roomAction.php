<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-05-16 23:35:22
 * @comment ����ҳ�洦�� 
 * @version
 */
class roomAction extends Util_Base
{
	protected static $_ui_ie = 'gbk';
	protected $_intRoomid = 0;
	protected static $_key = 'tieba_video_show';

    const SECRET = 'xkdhra';
    
	public function _execute()
	{
/*        if (!isset($this->_arrUserInfo['superboy']) || $this->_arrUserInfo['superboy'] != 'tbmall2'){
            echo "access deny!!!";
            exit;
}*/
		$this->_intRoomId	= intval(Bingo_Http_Request::getRouterParam('room_id'));
		$this->_getUserInfo();
		$roomInfo = array();
		if ($this->_intRoomId > 0) {
			$arrInput  = array('room_ids' => array($this->_intRoomId));
			$arrRet = Tieba_Service::call('show', 'mgetRoomInfo', $arrInput, NULL, NULL, 'post', 'php', self::$_ui_ie, 'local');
			if ($arrRet === false || $arrRet['errno'] != 0 || intval($arrRet['res'][$this->_intRoomId]) <= 0) {
				Bingo_Log::warning("room not exist! room_id: " .$this->_intRoomId); 
			}
			$roomInfo = $arrRet['res'][$this->_intRoomId];
		}

		if(empty($roomInfo) || $roomInfo['status']==1)
		{
			self::_302ShowHome();
		}
		
		$arrInput = array (
			'reqs' =>
			array (
				0 =>
				array (
					'rank_name' => 'relation',
					'scope' => 1,
					'interval' => 1,//1���հ�2���ܰ�3���°�4���ܰ�
					//'user_id' => 5,
					'rt_user_id' => intval($arrRet['res'][$this->_intRoomId]['user_id']),
					'room_id' => $this->_intRoomId,
					//'forum_id' => 6,
					//'props_id' => 1180001,
				),
			)
		);
		$arrOutput = Tieba_Service::call('show', 'mgetRankList', $arrInput, NULL, NULL, 'post', 'php', self::$_ui_ie, 'local');
		if ($arrOutput == false  || !isset($arrOutput['errno']) || intval($arrOutput['errno']) !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning("call getRankList error!, input: " . serialize($arrInput) . ' ,output:[ ' .serialize($arrOutput). ' ]');
		} else {
			$arrRanks = $arrOutput['data'][0] ? $arrOutput['data'][0] : array();
            $arrTmp = array();
            foreach($arrRanks as $user_id => $v){
                $v['user_id'] = $user_id;
                $arrTmp[] = $v;
            }
            $arrRanks = $arrTmp;
		}

		$permInfo = array();
		$user_id = $this->_arrUserInfo['user_id'];
		if ($user_id > 0) {
			$arrInputPerm = array('user_id' => $user_id, 'room_id' => $this->_intRoomId);
			$arrRetPerm   = Tieba_Service::call('show', 'getLivePerm', $arrInputPerm, NULL, NULL, 'post', 'php', self::$_ui_ie, 'local');
			if ($arrRetPerm == false  || !isset($arrRetPerm['errno']) || intval($arrRetPerm['errno']) !== Tieba_Errcode::ERR_SUCCESS) {
				Bingo_Log::warning("call perm, set live rt error! input: " . serialize($arrInputPerm) . ' , output:[ ' .serialize($arrRetPerm). ' ]');
			} else {
				$permInfo = $arrRetPerm['output'];
			}
		}
		
		$arrGiftInfo = array();
		$arrOutput = Tieba_Service::call('show', 'getGiftList', array('type'=>0,'room_id' => $this->_intRoomId), NULL, NULL, 'post', 'php', self::$_ui_ie, 'local');
		
		if ($arrOutput == false  || !isset($arrOutput['errno']) || intval($arrOutput['errno']) !== Tieba_Errcode::ERR_SUCCESS)
		{
			Bingo_Log::warning("call getGiftList error! ,output:[ " .serialize($arrOutput). ' ]');
		}
		else
		{
			$arrGiftInfo = $arrOutput['data'];
		}

		$grade = array();

		if($user_id>0)
		{
			$arrInput= array(
				'user_id' => $user_id,
				'forum_id' => $roomInfo['forum_id'],
				'user_ip' => $roomInfo['user_ip'],	
			);
			$arrOutput = Tieba_Service::call('perm', 'getPerm', $arrInput, NULL, NULL, 'post', 'php', self::$_ui_ie);

			if ($arrOutput == false  || !isset($arrOutput['errno']) || intval($arrOutput['errno']) !== Tieba_Errcode::ERR_SUCCESS)
			{
				Bingo_Log::warning("call getPerm error! ,output:[ " .serialize($arrOutput). ' ]');
			}
			else
			{
				$grade = $arrOutput['data']['grade'];
			}
		}
		//��ȡ�ʻ���
		//$arrOutput = Tieba_Service::call('show', 'getUserFlower', array('user_id'=> $this->_arrUserInfo['user_id']), NULL, NULL, 'post', 'php', 'utf-8', 'local');
		//$this->_arrUserInfo['video_flower_num'] = intval($arrOutput['data']);
        //��ȡһ���û��Ƿ��עһ����
        $arrInput = array(
            'forum_id' => intval($roomInfo['forum_id']),
            'user_id' => $user_id,
            'user_ip' => 0,
        );
        $arrOutput = Tieba_Service::call('perm', 'getPerm', $arrInput, NULL, NULL, 'post', 'php', self::$_ui_ie);
        $is_like = intval($arrOutput['output']['grade']['is_like']);
        $grade['is_like'] = $is_like;

        //��������û��б�������Ϣ
        $arrInput = array(
            'groupId' => $this->_intRoomId,
            'needUser' => 0,
        );
        $arrOutput = Tieba_Service::call('im', 'queryUserListInGroup', $arrInput ,NULL, NULL, 'post', 'php');
        if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning('get im ulist err! input[' . serialize($arrInput) .']');
        }
        $loginuser_num = intval($arrOutput['userInfo']['loginuser_num']) * 8 + rand(0, 5);
        $guessuser_num = intval($arrOutput['userInfo']['guessuser_num']) * 8 + rand(0, 5);
        $roomInfo['loginuser_num'] = $loginuser_num;
        $roomInfo['guessuser_num'] = $guessuser_num;
		//�޸ĸ��ݷ��������л�ģ��
		//pangzhanbo 2014-08-12
		//��÷�������
		$req = Tieba_Service::call('show','getRoomType',array('room_id' => $this->_intRoomId), NULL, NULL, 'post', 'php', self::$_ui_ie, 'local');
		$room_type = $req['data'];
		$roomInfo['room_type'] = $room_type;
		if(isset($room_type['is_star'])){
			$arrGetSeatInfo = array('room_id' => $this->_intRoomId);
			$ret = Tieba_Service::call('show','getRoomSeatInfo',$arrGetSeatInfo,NULL,NULL,'post','php','local');
			if(!isset($ret['errno']) || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning('get room seat info	failed!Input:['.serialize($arrGetSeatInfo).'].output:['.serialize($ret).']');
			}
			$seat_info = $ret['data'];
			$arrSeat = array();
			foreach($seat_info as $k => $v){
				$ret = Tieba_Service::call('user','getUserData',array('user_id' => $seat_info[$k]['user_id']));
				if(!isset($ret['errno']) || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
					Bingo_Log::warning('get user data	failed!Input:['.serialize($arrGetSeatInfo).'].output:['.serialize($ret).']');
				}
				$seat_info[$k]['user_info'] = $ret['user_info'][0];
				$seat_info[$k]['user_name'] = $ret['user_info'][0]['user_name'];
				$seat_info[$k]['portrait'] =  strval(Tieba_Ucrypt::encode($seat_info[$k]['user_id'], ''));
				$arrSeat[] = $seat_info[$k];
			}
			Bingo_Page::assign('seat_info',$arrSeat);
            $roomInfo['is_star'] = true;
            $roomInfo['superstar_rank'] = Util_Action::getStarRank($this->_intRoomId);
        }
        
        $bduss = Util_Encrypt::encryptDeviceId(Bingo_Http_Request::getCookie('BDUSS'), self::SECRET);

		//��ʱ���room HLS ���� //���С����
		$arrInput = array(
			'room_id' => $this->_intRoomId,
			);
		$ret =	Tieba_Service::call('show','getRoomHlsInfo',$arrInput,null,null,'post','php','gbk','local');
		if($ret['errno'] == Tieba_Errcode::ERR_SUCCESS && isset($ret['data'])){
			$roomInfo['is_hls'] = $ret['data']['is_hls'];
			$roomInfo['hls_url'] = $ret['data']['hls_url'];
		}

		//��Ʊ��Ϣ
		$ticket_id = 1304005;
		$ret = Tieba_Service::call("tbmall","mgetProps",array('props_ids' => array($ticket_id)),NULL, NULL,'post', 'php', 'gbk');
		if($ret['errno'] == Tieba_Errcode::ERR_SUCCESS && isset($ret['data'][0])){
			$retExt = unserialize($ret['data'][0]['ext']);
			if($this->_intRoomId == $retExt['room_id']){
				$ticket_info = array();
				$ticket_info['ticket_id'] = $ticket_id;
				$ticket_info['ticket_name'] = $ret['data'][0]['title'];
				$ticket_info['end_time'] = $retExt['ticket_time'];
				$ticket_info['program_time'] = $retExt['program_time'];
				$ticket_info['ticket_intro'] = $ret['data'][0]['description'];
				$ticket_info['scores'] = $ret['data'][0]['scores'];
				Bingo_Page::assign('ticket_info', $ticket_info);
			}
		}

		//���user ticket info 
		$sign = self::_getUserCDNSign(array('room_info' => $roomInfo,'ticket_info' =>$ticket_info,'bduss' => $bduss));
		$this->_arrUserInfo['ticket_info'] = self::_getUserTicketInfo();
		if(!(!empty($ticket_info) && !$this->_arrUserInfo['ticket_info']) ){
			if($sign !== false){
				Bingo_Page::assign('sign',$sign);
			}
		}
		//��־���
		Bingo_Log::pushNotice("ispv",1);
		Bingo_Page::assign('tbs', Tieba_Tbs::gene($this->_arrUserInfo['is_login']));
		Bingo_Page::assign('user', $this->_arrUserInfo);

		Bingo_Page::assign('roominfo', $roomInfo);
		Bingo_Page::assign('perm', $permInfo);
		Bingo_Page::assign('grade', $grade);
		Bingo_Page::assign('rank', $arrRanks);
		Bingo_Page::assign('giftinfo', $arrGiftInfo);

        Bingo_Page::assign('openid', $bduss);
		//ͳ������
		Tieba_Stlog::addNode('bduss',Bingo_Http_Request::getCookie('BDUSS'));
		Tieba_Stlog::addNode('room_id',$this->_intRoomId);
		//��Ӱ���������Ϣ
		$arrGetRoomExpensivePropsInfo = array(
			'room_id' => $this->_intRoomId,
			);
		$arrExpensivePropsRet =	Tieba_Service::call('show','getRoomExpensivePropsInfo',$arrGetRoomExpensivePropsInfo,NULL,NULL,'post','php','local');
		if(!empty($arrExpensivePropsRet['data']) && $arrExpensivePropsRet['errno'] == Tieba_Errcode::ERR_SUCCESS){
			Bingo_Page::assign('expensive_props', $arrExpensivePropsRet['data']);
		}
        //Bingo_Page::assign('bduss', Bingo_Http_Request::getCookie('BDUSS'));
        //Bingo_Page::assign('unbduss', Util_Encrypt::decrypt($bduss, self::SECRET));

        if(true == $roomInfo['is_star']){
            Bingo_Page::setTpl("starroom.php");
        }
        else{
            Bingo_Page::setTpl("liveroom.php");
        }
	}
	//����û�ƱȨ����Ϣ
	protected function _getUserTicketInfo(){
		$user_ticketinfos = $this->_arrUserInfo['Parr_props'][130];
		$arrRoomTicketInfos = array();
		$arrSortProgramTime = array();
		foreach($user_ticketinfos as $key => $v){
			if($v['room_id'] == $this->_intRoomId){
				$arrRoomTicketInfos[] = $v;
				$arrSortProgramTime[] = $v['program_time'];
			}
		}
		//todo

		array_multisort($arrSortProgramTime,$arrRoomTicketInfos,SORT_DESC);
		$arrOutput = $arrRoomTicketInfos[0];
		return $arrOutput;
	}
	//CDN�û�Ȩ��
	protected function _getUserCDNSign($arrInput){
		$bduss = $arrInput['bduss'];
		$fileName = '/'.$arrInput['room_info']['media_id'].'.m3u8';
		$allKey = self::$_key.$bduss.$fileName;
		$sign = md5($allKey);
		return $sign;
	}
	protected static function _302ShowHome()
	{
        $conf = Bd_Conf::getConf("/app/show/ui_show");
        if($conf == false){
            Bingo_Log::warning("init get conf fail.");
            throw new Util_Exception("args error!", Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $jump_url = $conf['home_url'];
		header('location:'.$jump_url);
		exit(0);
	}

    private static function _getStarRank($room_id){
        $arrInput  = array(
            'room_id' => $room_id,
            'time_interval' => 1,
        );
        $arrRet = Tieba_Service::call('show', 'getSuperStarList', $arrInput, NULL, NULL, 'post', 'php', self::$_ui_ie, 'local');
        if ($arrRet === false || $arrRet['errno'] != 0 ) {
            Bingo_Log::warning('getSuperStarList err' . serialize($arrInput));
            return array();
        }
        $data = $arrRet['data'];
        $user_ids = array();
        foreach($data as $v){
            $user_ids[] = $v['member'];
        }
        $arrInput = array('user_id' => $user_ids);
        $arrRet = Tieba_Service::call('user', 'getUnameByUids', $arrInput, NULL, NULL, 'post', 'php', self::$_ui_ie);
        if ($arrRet === false || $arrRet['errno'] != 0 ) {
            Bingo_Log::warning('getSuperStarList err' . serialize($arrInput));
            return array();
        }
        $user_names = $arrRet['output']['unames'];

        $ret = array();
        foreach($data as $v){
            $ret[intval($v['member'])]['score'] = intval($v['score']);
        }
        foreach($user_names as $v){
            $user_id = $v['user_id'];
            $user_name = $v['user_name'];
            $portrait = ($user_id > 0) ? strval(Tieba_Ucrypt::encode($user_id, '')) : '';
            $ret[$user_id]['superstar_uid'] = $user_id;
            $ret[$user_id]['portrait'] = $portrait;
            $ret[$user_id]['name'] = $user_name;
            $ret[$user_id]['forum_name'] = isset(self::$arrUidToFid[$user_id]) ? self::$arrUidToFid[$user_id] : 'snh48';
        }

        $arrOutput = array();
        foreach($data as $v){
            $user_id = intval($v['member']);
            $arrOutput[] = $ret[$user_id];
        }

        return $arrOutput;

    }


}
/* vim: set noexpandtab ts=4 sw=4 sts=4 tw=100: */
