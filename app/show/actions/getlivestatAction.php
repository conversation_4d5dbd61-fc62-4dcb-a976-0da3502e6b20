<?php
/**
 *Author: ding<PERSON>hu
 *Filename: getroomstatusAction.php
 *Date: 2014-12-23
 *Desc: 获取房间当前的直播状态
 */
class getlivestatAction extends Util_Base{
	protected static $_ui_ie='utf-8';
	protected $_tbs_check = 0;
	
	public function _execute(){
        try{
			$fids = Bingo_Http_Request::get('fids', '');
			if (empty($fids)) {
				throw new Util_Exception("args error!",Tieba_Errcode::ERR_PARAM_ERROR);
			}

			if( $this->_arrUserInfo['is_login'] !== true){
				//未登录
				throw new Util_Exception("user need login!",Tieba_Errcode::ERR_USER_NOT_LOGIN);
			}

			if( $this->_tbs_check ){
				$tbs = strval(Bingo_Http_Request::get('tbs',''));
				if( empty($tbs) ){
					throw new Util_Exception("tbs is empty", Tieba_Errcode::ERR_PARAM_ERROR);
				}
				if( !Tieba_Tbs::check($tbs,true) ){
					//tbs验证失败
					throw new Util_Exception("tbs check fail!", Tieba_Errcode::ERR_TBSIG_CHECK_FAIL);
				}
			}
			
			$fidArr = explode(',', $fids);
			$arrParams = array(
				'fids' => $fidArr,
				'scene_id' => 2000046,
			);
			$arrRet = Tieba_Service::call('show', 'getLiveStat', $arrParams, NULL, NULL, 'post', 'php', self::$_ui_ie, 'local');
			
			if ($arrRet === false || $arrRet['errno'] != 0) {
				throw new Util_Exception("getLiveStat service call fail! line:".__LINE__, $arrRet['errno']);
			}
			$errno = $arrRet['errno'];
			
			$this->_jsonRet($errno, Tieba_Error::getUserMsg($errno), $arrRet['data']);
        }catch (Util_Exception $e){
			Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
			//数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
			$this->_jsonRet($e->getCode(), '未知错误');
        }

	}
}
