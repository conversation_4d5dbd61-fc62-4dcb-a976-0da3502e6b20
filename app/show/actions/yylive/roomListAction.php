<?php
/**
 *Author: ji<PERSON><PERSON>bin
 *Filename: roomListAction.php
 *Date: 2014-09-02
 *Desc: 房间列表页
 */
class roomListAction extends Util_Base{
	public function _execute(){
	    $tag_id = Bingo_Http_Request::get('tag_id',0);
        $fid = intval(Bingo_Http_Request::get('forum_id',0));
		$game_id = Bingo_Http_Request::get('game_id',0);
		if($game_id > 0){
			$tag_id = $game_id;
		}
        $fname = strval(Bingo_Http_Request::get('forum_name',''));
        try{
            if($tag_id <= 0){
                Bingo_Log::warning('tag_id error');
                throw new Util_Exception('tag_id error',Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $arrForumInfo = array();
            if($fid <= 0 && $fname == ''){//传入的吧名不对,获取主吧的信息
                $arrInput = array(
                    'tag_id' => $tag_id
                );
                $arrOut = Tieba_Service::call('show','getYYMainForumByTagid',$arrInput,NULL,NULL,'POST','php','gbk','local');
                if($arrOut == false || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
                    Bingo_Log::warning('call show[getYYMainForumByTagid] fail');
                    throw new Util_Exception('service call fail',Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
                $data = $arrOut['data'];
                if(!empty($data)){//说明绑定吧
                    $forum_id = $data['forum_id'];
                    $forumInfo = Data_Forum::getFnameByFid($forum_id);
                    if($forumInfo == false){
                        Bingo_Page::assign('get forum info fail');
                        throw new Util_Exception('service call fail',Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                    }
                    Bingo_Page::assign('forum',$forumInfo);
                }
            }else{
                if($fid <= 0 && $fname != ''){
                    $arrForumInfo = Data_Forum::getFidByFname($fname);
                }else if($fid > 0){
                    $arrForumInfo = Data_Forum::getFnameByFid($fid);
                }
                if($arrForumInfo == false){
                    Bingo_Page::assign('get forum info fail');
                    throw new Util_Exception('service call fail',Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
                Bingo_Page::assign('forum',$arrForumInfo);
            }
            $url = '/' . $tag_id;
            $user = $this->_getUserInfo();
            $bdInfo = Data_User::getUserOpenInfo($_COOKIE['BDUSS'],$user['is_login']);
            Bingo_Page::assign('iframe_url',$url);
            Bingo_Page::assign('tag_id',$tag_id);
            Bingo_Page::assign('user',$user);
            Bingo_Page::assign('bd_info',$bdInfo);
            Bingo_Page::setTpl('yy_live_list.php');
            Bingo_Log::pushNotice('tag_id',$tag_id);
        }catch (Util_Exception $e){
            Tieba_Error::illegalError302();
        }
	}

}
