<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-05-16 23:35:17
 * @comment json接口
 * @version
 */
class getrankAction extends Util_Base {
    protected static $_ui_ie='utf-8';

    public function _execute(){
        try {
			$rank_name = Bingo_Http_Request::get('rank_name', '');
            $scope = Bingo_Http_Request::get('scope', 0);
            $interval = Bingo_Http_Request::get('interval', 0);
            $user_id = Bingo_Http_Request::get('user_id', 0);
            $rt_user_id = Bingo_Http_Request::get('rt_user_id', 0);
            $room_id = Bingo_Http_Request::get('room_id', 0);
            $forum_id = Bingo_Http_Request::get('forum_id', 0);
            $props_id = Bingo_Http_Request::get('props_id', 0);
            $pn = Bingo_Http_Request::get('pn', 0);
            $ps = Bingo_Http_Request::get('ps', 10);

            $req = array(
                'rank_name'     =>       $rank_name,
                'scope'         =>       $scope,
                'interval'          =>       $interval,
                'user_id'           =>       $user_id,
                'rt_user_id'            =>      $rt_user_id,
                'room_id'           =>       $room_id,
                'forum_id'          =>       $forum_id,
                'props_id'          =>       $props_id,
                'pn'          =>       $pn * $ps,
                'ps'          =>       $pn * $ps + $ps - 1,
            );
            $arrInput = array(
                'reqs' => array(
                    $req,
                ),
            );
			$arrRet = Tieba_Service::call('show', 'mgetRankList', $arrInput, NULL, NULL, 'post', 'php', self::$_ui_ie, 'local');
			if ($arrRet === false || !isset($arrRet['errno']) || $arrRet['errno'] != 0) {
				Bingo_Log::warning('input[' . serialize($arrInput). '] output[' . serialize($arrRet) . ']');
			} else {
				$data = $arrRet['data'];
			}
            $res = array();
            foreach($data[0] as $k => $v){
                $v['user_id'] = $k;
                $res[] = $v;
            }

            $errno = $arrRet['errno'];
            $this->_jsonRet($errno, Tieba_Error::getUserMsg($errno), $res);
        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), Tieba_Error::getUserMsg($e->getCode()));	
        }
    }
}
?>
