<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-05-16 23:35:17
 * @comment json接口
 * @version
 */
class getOnlineUserAction extends Util_Base {
    protected static $_ui_ie='utf-8';

    public function _execute(){
        try {

            $room_id = intval(Bingo_Http_Request::get('room_id', 0));
            $page_no = intval(Bingo_Http_Request::get('pn', 0));
            $page_size = intval(Bingo_Http_Request::get('ps', 0));

            if($room_id <= 0){
                throw new Util_Exception("args error!", Tieba_Errcode::ERR_PARAM_ERROR);
            }

            $arrInput = array(
                'room_id' => $room_id,
                'page_no' => $page_size * $page_no,
                'page_size' => $page_size * $page_no + $page_size - 1 ,
            );
			$arrRet = Tieba_Service::call('show', 'getOnlineList', $arrInput, NULL, NULL, 'post', 'php', self::$_ui_ie, 'local');
			$user_list = array();
			if ($arrRet === false || !isset($arrRet['errno']) || $arrRet['errno'] != 0) {
				Bingo_Log::warning('getFrsRooms error! line:input[' .serialize($arrInput).']');
			} else {
				$user_list = $arrRet['data'];
			}

            //获取用户性别
            $user_ids = array();
            foreach($user_list as $_v){
                $user_ids[] = intval($_v['uid']);
            }
            $arrInput = array(
                'user_id' => $user_ids,
            );
            $arrOutput = Tieba_Service::call('user', 'mgetUserDataEx', $arrInput,NULL,NULL,'post','php',self::$_ui_ie);
            if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
                Bingo_Log::warning('get user sex err! input['.serialize($arrInput).'] output['.serialize($arrOutput).']');
            }
            $user_infos = (array)$arrOutput['user_info'];
            // for test
//            $t_weight  = 100;
            foreach($user_list as $_k => $_v){
                $_v['uid'] = intval($_v['uid']);
                $user_id = intval($_v['uid']);
                $_v['sex'] = intval($user_infos[$user_id]['user_sex']);
                $user_list[$_k] = $_v;

                // for test
//                if($room_id == 1000032){
//
//                    $_v['weight'] = $t_weight--;
//                    $_v['uname'] = $t_weight;
//                    $_v['uid'] = $t_weight - 10000 * $page_no;
//                    $user_list[$_k] = $_v;
//                }
            }



            $errno = $arrRet['errno'];
            $this->_jsonRet($errno, Tieba_Error::getUserMsg($errno), $user_list);

        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), Tieba_Error::getUserMsg($e->getCode()));	
        }
    }
}
?>
