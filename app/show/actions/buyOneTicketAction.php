<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-05-16 23:35:17
 * @comment json接口
 * @version
 */
class buyOneTicketAction extends Util_Base {
    protected static $_ui_ie='utf-8';
    public function _execute(){
        try {


            $user_id = intval(Tieba_Session_Socket::getLoginUid());
			$ticket_id = intval(Bingo_Http_Request::get('ticket_id', ''));

            if($user_id <= 0){
                throw new Util_Exception("args error!", Tieba_Errcode::ERR_PARAM_ERROR);
            }

			//超时不能购买
			/*
			$end_selling = 111111111111110;
			if(time() > $end_selling){
				$errno = Tieba_Errcode::ERR_IS_EXPIRED;
				return $this->_jsonRet($errno,Tieba_Error::getErrmsg($errno));
			}
			*/

			//不能重复购买
			//todo
			if(isset($this->_arrUserInfo['Parr_props'][130][$ticket_id])){
				$errno = Tieba_Errcode::ERR_REPEAT_PURCHASE;
				return $this->_jsonRet($errno, Tieba_Error::getErrmsg($errno));
			}

			$ticket_id = 1304005;
			//购买逻辑
            $arrInput = array(
				'props_id' => $ticket_id,
                'user_id' => $user_id,
				'buy_num' => 1,//只能买一个
				'order_type' => Service_Mall_Define::BUY_ORDER_TYPE_MALL,
            );
            $arrOutput = Tieba_Service::call('tbmall', 'buyOneProps', $arrInput, NULL, NULL, 'post', 'php' );
			if(!isset($arrOutput['errno']) || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				return $this->_jsonRet($arrOutput['errno'],Tieba_Error::getErrmsg($arrOutput['errno']));
			}
			//统计需求
			Tieba_Stlog::addNode('ticket_id',$ticket_id);
            $errno = Tieba_Errcode::ERR_SUCCESS;
            $this->_jsonRet($errno, Tieba_Error::getErrmsg($errno));

        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), Tieba_Error::getUserMsg($e->getCode()));	
        }
    }
}
?>
