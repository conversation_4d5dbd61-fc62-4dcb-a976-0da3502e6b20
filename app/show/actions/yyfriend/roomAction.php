<?php
/**
 *Author: ji<PERSON><PERSON>bin
 *Filename: roomAction.php
 *Date: 2014-11-26
 *Desc: 
 */
class roomAction extends Util_Base{
	public function _execute(){
        $room_id = Bingo_Http_Request::get('id',0);
        try{
            $user = $this->_getUserInfo();
            $bdInfo = Data_User::getUserOpenInfo($_COOKIE['BDUSS'],$user['is_login']);
            $domain = Data_YYFriend::getDomain();
            $forum_id = Data_YYFriend::getForumId();
            $arrForum = Data_Forum::getFnameByFid($forum_id);
            $arrParams = array(
                'forum_id' => $forum_id,
                'forum_name' => $arrForum['forum_name_utf8'],
                'id' => $room_id
            );
            $url = $domain . '/index/love/tieba?' . http_build_query($arrParams);
            $third_info = array(
                'domain' => $domain,
                'iframe_url' => $url
            );
            Data_Header::setForumHeaderPageInfo($forum_id,$arrForum['forum_name'],$user['user_id'],$user);
            Bingo_Page::assign('third_info',$third_info);
            Bingo_Page::assign('user',$user);
            Bingo_Page::assign('bd_info',$bdInfo);
            Bingo_Page::setTpl('yy_friend_page.php');
			Bingo_Page::assign('url_path','/show/yyfriend/room');
            Bingo_Log::pushNotice('iframe_url',$url);
        }catch (Util_Exception $e){
            Tieba_Error::fetalError302();
        }
	}
}
