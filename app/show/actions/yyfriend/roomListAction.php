<?php
/**
 *Author: jiang<PERSON>bin
 *Filename: roomListAction.php
 *Date: 2014-11-26
 *Desc: 
 */
class roomListAction extends Util_Base{
    public function _execute(){
        try{
            $user = $this->_getUserInfo();
            $bdInfo = Data_User::getUserOpenInfo($_COOKIE['BDUSS'],$user['is_login']);
            $domain = Data_YYFriend::getDomain();
            $forum_id = Data_YYFriend::getForumId();
            $arrForum = Data_Forum::getFnameByFid($forum_id);
            $arrParams = array(
                'forum_id' => $forum_id,
                'forum_name' => $arrForum['forum_name_utf8'],
            );
            $url = $domain . '/index/love/tblist?' . http_build_query($arrParams);
            $third_info = array(
                'domain' => $domain,
                'iframe_url' => $url
            );
            Bingo_Log::warning(print_r($user,true));
            Data_Header::setForumHeaderPageInfo($forum_id,$arrForum['forum_name'],$user['user_id'],$user);
            Bingo_Page::assign('third_info',$third_info);
            Bingo_Page::assign('user',$user);
            Bingo_Page::assign('bd_info',$bdInfo);
			Bingo_Page::assign('url_path','/show/yyfriend/roomList');
            Bingo_Page::setTpl('yy_friend_page.php');
            Bingo_Log::pushNotice('iframe_url',$url);
        }catch (Util_Exception $e){
            Tieba_Error::fetalError302();
        }
    }
}

