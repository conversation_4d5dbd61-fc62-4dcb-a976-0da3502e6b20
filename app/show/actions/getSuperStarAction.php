<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-05-16 23:35:17
 * @comment json接口
 * @version
 */
class getSuperStarAction extends Util_Base {
    protected static $_ui_ie='utf-8';
	protected $_tbs_check = 0; 
    public function _execute(){
        try {
			$room_id    = Bingo_Http_Request::get('room_id', array());
            $time_interval = Bingo_Http_Request::get('time_interval', array());
			if ($room_id <= 0) {
                throw new Util_Exception("args error!",Tieba_Errcode::ERR_PARAM_ERROR);
			}

            $rank_info = Util_Action::getStarRank($room_id, $time_interval, 'utf-8');

            $errno = Tieba_Errcode::ERR_SUCCESS;
            $this->_jsonRet($errno, Tieba_Error::getErrMsg($errno), $rank_info);
        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), Tieba_Error::getUserMsg($e->getCode()));	
        }
    }

}
?>
