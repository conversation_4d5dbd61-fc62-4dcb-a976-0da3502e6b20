<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-05-16 23:35:17
 * @comment json接口
 * @version
 */
class saveasAction extends Util_Base {
    protected static $_ui_ie='utf-8';
    public function _execute(){
        try {
            
            $room_id = Bingo_Http_Request::get('room_id', 0);
			if ($room_id<=1) {
                throw new Util_Exception("args error!",Tieba_Errcode::ERR_PARAM_ERROR);
			}

			$arrInput  = array('room_ids' => array($room_id));
			$arrRet = Tieba_Service::call('show', 'mgetRoomInfo', $arrInput, NULL, NULL, 'post', 'php', self::$_ui_ie, 'local');
			$roomInfo = array();
			if ($arrRet === false || !isset($arrRet['errno']) || $arrRet['errno'] != 0 || !isset($arrRet['res'][$room_id]['room_name'])) {
				Bingo_Log::warning('getRooms error! line:' . serialize($arrRet));
                throw new Util_Exception("args error!",Tieba_Errcode::ERR_PARAM_ERROR);
			} else {
				$roomInfo = $arrRet['res'][$room_id];
			}
            $room_name = $roomInfo['room_name'];
            if(is_string($room_name) && strlen($room_name)>0)
            {
                $conf = Bd_Conf::getConf("/app/show/ui_show");
                $base_url = $conf['base_url']; 
                if (empty($base_url))
                {
                    $base_url = 'http://cq01-forum-rdtest12.vm.baidu.com:8080/show';
                }
                Header("Content-type: application/octet-stream");
                Header("Accept-Ranges: bytes");
                Header("Content-Disposition: attachment; filename=$room_name.url");
                echo "[InternetShortcut]\r\nURL=$base_url/$room_id\r\nIconFile=%SystemRoot%\system32\shell32.dll\r\nIconIndex=43";
                exit(0);
            }


            $errno = Tieba_Errcode::ERR_PARAM_ERROR;
            $this->_jsonRet($errno, Tieba_Error::getUserMsg($errno), $roomInfo);
        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), Tieba_Error::getUserMsg($e->getCode()));	
        }
    }
}
?>
