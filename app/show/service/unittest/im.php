<?php
/**
 * Created by PhpStorm.
 * User: xuke02
 * Date: 14-6-20
 * Time: ����11:03
 */

$arrInput = array(
    'groupId' => 58,
    'offset' => 0,
    'limit' => 150,
    'needUser' => 1,
);
$arrOutput = Tieba_Service::call('im', 'queryUserListInGroup', $arrInput ,NULL, NULL, 'post', 'php');

$arrUids = $arrOutput['userInfo']['loginuser_ids'];

$arrInput = array(
    'user_id' => $arrUids,
);

$arrOutput = Tieba_Service::call('user', 'getUnameByUids', $arrInput ,NULL, NULL, 'post', 'php');


var_export($arrOutput);
