<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014:05:06 12:20:14
 * @version
 * @structs & methods(copied from idl.)
 */



define("MODULE","Show_service");
class Service_User_User{

    private static $_service_ie = 'utf-8';

    const SERVICE_NAME = "Service_Show_User";
    protected static $_conf = null;
    protected static $_use_split_db = false;
    const DATABASE_NAME = "forum_show";
    const MEMCACHED_NAME = "forum_show";
    protected static $_cache = null;

    const REDIS_NAME = "forum_show";
    protected static $_redis = null;

    const LOGIN_CMD = 1;
    const LOGOUT_CMD = 0;

    const REIDS_HASH_ONLINE_USER = 'ONLINE_USER';
    const REIDS_ZSET_ONLINE_LIST = 'ONLINE_LIST';

    const REIDS_RANK_RELATION = 'RELATION';
    const REIDS_RANK_ACTIVE = 'ACTIVE';
    const REIDS_RANK_RICHE = 'RICHE_V2';
    const REIDS_RANK_STRA = 'STAR';
    const REIDS_RANK_GIFT_GIVE = 'GIFT_GIVE';
    const REIDS_RANK_GIFT_RECEIVE = 'GIFT_RECEIVE';

    const REIDS_FLOWER_INFO = 'flower_info';

    const RANK_SCOPE_ROOM = 1;
    const RANK_SCOPE_FORUM = 2;
    const RANK_SCOPE_ALL = 3;

    const RANK_TIME_DAY = 1;
    const RANK_TIME_WEEK = 2;
    const RANK_TIME_MONTH = 3;
    const RANK_TIME_FOREVER= 4;

    const RANK_ATTR_NONE = 0;

    const MAX_RANK_CNT = 3;

    const PROBABILITY_VALUE = 100;

    //高级会员
    const ADVANCED_MEMBER = 2;
    const COMMON_MEMBER = 1;

    const ROLE_RT_VALUE = 1000000000; //10亿

    const ADVANCED_MEMBER_VALUE = 500000000; //5亿

    const COMMON_MEMBER_VALUE= 200000000; //2亿

    const ROLE_SOP_VALUE = 10500000; //1千零50万
    const ROLE_OW_VALUE = 10020000; //1百零2万
    const ROLE_OP_VALUE = 10010000; //1百零1万

    const ROLE_RT = 'u_is_rt';
    const ROLE_OP = 'u_is_op';
    const ROLE_OW = 'u_is_ow';
    const ROLE_SOP = 'u_is_sop';

    const FLOWER_PROPS_ID = 1180001;

    const NMQ_COMMAND_RANK_RELATION = 3000;
    const NMQ_COMMAND_RANK_ACTIVE = 3001;
    const NMQ_COMMAND_RANK_RICHE = 3002;
    const NMQ_COMMAND_RANK_STAR = 3003;
    const NMQ_COMMAND_RANK_GIFT_GIVE = 3004;
    const NMQ_COMMAND_RANK_GIFT_RECEIVE = 3005;

    const MIN_RECOVER_USER_NUM = 50;

    const RANK_LIST_NUM = 10;
    const INF = 100000000;

    public static $RANK_NAMES = array(
        'relation',
        //'active',
        'riche',
        'star',
        'gift_give',
        'gift_receive',
    );


    /**
     * @brief get mysql obj.
     * @return: obj of Bd_DB, or null if connect fail.

     **/
    private static function _getDB(){
        $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if($objTbMysql && $objTbMysql->isConnected()) {
            return $objTbMysql;
        } else {
            Bingo_Log::warning("db connect fail.");
            return null;
        }
    }

    /**
     * @brief get cache obj.
     * @return: obj of Bingo_Cache_Memcached, or null if connect fail.

     **/
    private static function _getCache(){
        if(self::$_cache){
            return self::$_cache ;
        }
        Bingo_Timer::start('memcached_init');
        self::$_cache = new Bingo_Cache_Memcached(self::MEMCACHED_NAME);
        Bingo_Timer::end('memcached_init');

        if(!self::$_cache || !self::$_cache->isEnable()){
            Bingo_Log::warning("init cache fail.");
            self::$_cache = null;
            return null;
        }
        return self::$_cache;
    }

    /**
     * @brief get redis obj.
     * @return: obj of Bingo_Cache_Redis, or null if connect fail.

     **/
    private static function _getRedis(){
        if(self::$_redis){
            return self::$_redis ;
        }
        Bingo_Timer::start('redis_init');
        self::$_redis = new Bingo_Cache_Redis(self::REDIS_NAME);
        Bingo_Timer::end('redis_init');

        if(!self::$_redis || !self::$_redis->isEnable()){
            Bingo_Log::warning("init redis fail.");
            self::$_redis = null;
            return null;
        }
        return self::$_redis;
    }


    /**
     * @brief init
     * @return: true if success. false if fail.

     **/
    private static function _init(){
        return true;

        //add init code here. init will be called at every public function beginning.
        //not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
        //init should be recalled for many times.

        if(self::$_conf == null){
            self::$_conf = Bd_Conf::getConf("/app/show/service_show_show");
            if(self::$_conf == false){
                Bingo_Log::warning("init get conf fail.");
                return false;
            }

        }
        return true;
    }


    private static function _errRet($errno){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    public static function preCall($arrInput){
        // pre-call hook
    }

    public static function postCall($arrInput){
        // post-call hook
    }
    /**
     * @brief
     * @arrInput:
     * 	RankReq req[]
     * @return: $keys
     * 	RankList data[]
     **/
    public static function mgetRankList($arrInput){

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['reqs'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input params.
        $reqs = Tieba_Service::getArrayParams($arrInput, 'reqs');

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.
        $data = false;

        //your code here......
        $arrInput = array();
        foreach($reqs as $req){
            $keys = self::_getRankKey($req);
            foreach($keys as $key){
                $arrInput[] = $key;
            }
        }

        if(false == ($data = Service_Lib_Redis::mZREVRANGEWITHSCORES($arrInput))){
            Bingo_Log::warning("get rank err! input[" . serialize($arrInput) ."]");
            return self::_errRet(Tieba_Errcode::ERR_RANK_REDIS_TIMEOUT);
        }

        //todo $data数据merge self::mgetUserInfo

        $user_infos = array();
        foreach($reqs as $req){
            $room_id = intval($req['room_id']);
            $user_ids = array();

            $keys = self::_getRankKey($req);
            $key = $keys[0]['key'];

            foreach($data[$key] as $v){
                $user_id = $v['member'];
                $score = $v['score'];
                $user_ids[] = $user_id;
            }

            $arrInput = array(
                'room_id' => $room_id,
                'user_ids' => $user_ids,
            );
            $arrOutput = self::mgetUserInfo($arrInput);
            if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
                continue;
            }
            $tmp = array();
            foreach($data[$key] as $v){
                $user_id = $v['member'];
                $tmp[$user_id] = array_merge($v, $arrOutput['data'][$user_id]);
                $tmp[$user_id]['portrait'] = strval(Tieba_Ucrypt::encode($user_id, ''));
            }

            $user_infos[] = $tmp;
        }


        $error = Tieba_Errcode::ERR_SUCCESS;
        $keys = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $user_infos,
        );
        return $keys;
    }

    public static function getOnlineList($arrInput){

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['room_id']) || !isset($arrInput['page_no']) || !isset($arrInput['page_size'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input params.
        $room_id = intval($arrInput['room_id']);
        $page_no = intval($arrInput['page_no']);
        $page_size = intval($arrInput['page_size']);

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.

        //your code here......
        $key = self::_genKey( array(self::REIDS_ZSET_ONLINE_LIST, $room_id) );
        $arrInput = array(
            'key' => $key,
            'start' => $page_no,
            'stop' => $page_size,
        );
        if(false === ($user_ids = Service_Lib_Redis::ZREVRANGE($arrInput))){
            return self::_errRet(Tieba_Errcode::ERR_BUSI_USE_REDIS_ERR);
        }
        $arrInput = array(
            'room_id' => $room_id,
            'user_ids' => $user_ids,
        );
        $keys = self::mgetUserInfo($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS !== $keys['errno']){
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $data = $keys['data'];

        //按user_id权重显示, 并附加上权重
        $user_list = array();
        foreach($user_ids as $user_id){
            $data[$user_id]['uid'] = $user_id;
            $data[$user_id]['weight'] = self::_calUserWeight($data[$user_id]);
            $user_list[] = $data[$user_id];
        }


        $error = Tieba_Errcode::ERR_SUCCESS;
        $keys = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $user_list,
        );
        return $keys;
    }

    public static function getSearchList($arrInput){

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['room_id'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input params.
        $room_id = intval($arrInput['room_id']);

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.

        //your code here......
        $key = self::_genKey( array(self::REIDS_HASH_ONLINE_USER, $room_id) );
        $arrInput = array(
            'key' => $key,
        );
        if(false === ($user_infos = Service_Lib_Redis::HGETALL($arrInput))){
            return self::_errRet(Tieba_Errcode::ERR_BUSI_USE_REDIS_ERR);
        }
        $unames = array();
        foreach($user_infos as $_v){
            $unames[] = $_v['value'];
        }


        $error = Tieba_Errcode::ERR_SUCCESS;
        $keys = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $unames,
        );
        return $keys;
    }

    public static function refreshOnlineList($arrInput){
        if(!isset($arrInput['room_id'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $room_id = intval($arrInput['room_id']);
        //删除此房间的在线列表
        if(false == self::_delOnlineList($room_id)){
            return self::_errRet(Tieba_Errcode::ERR_HOME_UI_HOME_DELVISITOR);
        }

        //从im获取最新的在线列表
        $arrInput = array(
            'groupId' => $room_id,
            'offset' => 0,
            'limit' => 150,
            'needUser' => 0,
        );
        $arrOutput = Tieba_Service::call('im', 'queryUserListInGroup', $arrInput ,NULL, NULL, 'post', 'php', self::$_service_ie);
        if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning('get im ulist err! input[' . serialize($arrInput) .']');
            return self::_errRet(Tieba_Errcode::ERR_IM_GROUP_NOT_EXIST);
        }
        $user_ids = $arrOutput['userInfo']['loginuser_ids'];
        if(empty($user_ids)){
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        //重新批量上线此房间的在线用户
        if(false == self::_mlogin($room_id, $user_ids)){
            return self::_errRet(Tieba_Errcode::ERR_IM_GROUP_TOO_MANY_GROUP);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $user_ids,
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @arrInput:
     * 	uint32_t type
     * 	uint32_t room_id
     * 	uint32_t user_ids[]
     * @return: $keys
     **/
    public static function msetOnlineList($arrInput){

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['type']) || !isset($arrInput['room_id']) || !isset($arrInput['user_ids']) ){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input params.
        $type = intval($arrInput['type']);
        $room_id = intval($arrInput['room_id']);
        $user_ids = Tieba_Service::getArrayParams($arrInput, 'user_ids');

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.

        //your code here......
        switch($type){
            case self::LOGIN_CMD :
                if(false === ($keys = self::_mlogin($room_id, $user_ids))){
                    return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
                }

                break;
            case self::LOGOUT_CMD :
                if(false === ($keys = self::_mlogout($room_id, $user_ids))){
                    return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
                }
                break;
        }



        $error = Tieba_Errcode::ERR_SUCCESS;
        $keys = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $keys;
    }

    /**
     * @brief
     * @arrInput:
     * 	uint32_t room_id
     * 	uint32_t user_ids[]
     * @return: $keys
     * 	UserInfo data[]
     **/
    public static function mgetUserInfo($arrInput){

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['room_id']) || !isset($arrInput['user_ids'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input params.
        $room_id = intval($arrInput['room_id']);
        $user_ids = Tieba_Service::getArrayParams($arrInput, 'user_ids');

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.
        $data = array();

        if(empty($user_ids)){
            $error = Tieba_Errcode::ERR_SUCCESS;
            $keys = array(
                'errno' => $error,
                'errmsg' => Tieba_Error::getErrmsg($error),
                'data' => $data,
            );
            return $keys;
        }

        //获取视频直播间相关权限
        $arrInput = array(
            'room_id' => $room_id,
            'user_id' => $user_ids,
        );
        $arrOutput = Tieba_Service::call('show', 'mgetLiveRole', $arrInput, NULL, NULL, 'post', 'php', self::$_service_ie, 'local');
        if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning('get live role err input[' . serialize($arrInput) . ']');
            //return self::_errRet(Tieba_Errcode::ERR_HOME_UI_USER_DATAEX_BYUN);
        }
        $role_info = $arrOutput['output'];

        $user_data = array();//存放user信息的总容器, 已user_id为下标
        foreach($user_ids as $user_id){
            $user_data[$user_id]['role'] = $role_info[$user_id];
        }

        $arrInput = array(
            'user_id' => $user_ids,
        );
        $arrOutput = Tieba_Service::call('user', 'mgetUserData', $arrInput, NULL, NULL, 'post', 'php', self::$_service_ie);
        if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning('get user data err input[' . serialize($arrInput) . ']');
            //return self::_errRet(Tieba_Errcode::ERR_HOME_UI_USER_DATAEX_BYUN);
        }
        $user_info = $arrOutput['user_info'];
        foreach($user_ids as $user_id){
            $user_data[$user_id]['mParr_props'] = $user_info[$user_id]['mParr_props'];
        }

        //your code here......

        //delete start
//        $data = array();
//        foreach($user_ids as $user_id){
//            $data[$user_id] = array(
//                'member_level' => 2,
//                'is_sop' => false,
//                'is_op' => false,
//                'is_rt' => true,
//            );
//        }
        //end

        //添加用户名信息
        $arrInput = array(
            'user_id' => $user_ids,
        );
        $keys = Tieba_Service::call('user', 'getUnameByUids', $arrInput, NULL, NULL, 'post', 'php', self::$_service_ie);
        if(Tieba_Errcode::ERR_SUCCESS !== $keys['errno']){
            Bingo_Log::warning('get unames err! in['.serialize($user_ids).'] out['.serialize($keys).']');
            //return self::_errRet(Tieba_Errcode::ERR_HOME_UI_USER_DATAEX_BYUN);
        }
        $unames = $keys['output']['unames'];
        foreach($unames as $_v){
            $intUserId = intval($_v['user_id']);
            $strUserName = $_v['user_name'];
            $user_data[$intUserId]['uname'] = $strUserName;
        }


        $error = Tieba_Errcode::ERR_SUCCESS;
        $keys = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $user_data,
        );
        return $keys;
    }
    /**
     * @brief
     * @arrInput:
     * 	uint32_t room_id
     * 	uint32_t user_id
     * 	uint32_t rt_user_id
     * 	uint32_t props_id
     * 	uint32_t score
     * @return: $keys
     **/
    public static function dealGift($arrInput){

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['room_id']) || !isset($arrInput['user_id']) || !isset($arrInput['rt_user_id']) || !isset($arrInput['props_id']) || !isset($arrInput['score'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input params.
        $room_id = intval($arrInput['room_id']);
        $user_id = intval($arrInput['user_id']);
        $rt_user_id = intval($arrInput['rt_user_id']);
        $props_id = intval($arrInput['props_id']);
        $score = intval($arrInput['score']);

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.

        //your code here......



        $error = Tieba_Errcode::ERR_SUCCESS;
        $keys = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $keys;
    }
    /**
     * @brief
     * @arrInput:
     * 	RankReq req
     * @return: $keys
     **/
    public static function setRankList($arrInput){

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['req'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input params.
        $req = Tieba_Service::getArrayParams($arrInput, 'req');

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.

        //your code here......

        $key = self::_getRanklistKey($req);



        $error = Tieba_Errcode::ERR_SUCCESS;
        $keys = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $keys;
    }


    public static function giveFlower($arrInput){

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['user_id'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input params.
        $user_id = intval($arrInput['user_id']);

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.

        //your code here......

        //判断今天是否获取过鲜花
        $key = self::_genKey(array(self::REIDS_FLOWER_INFO, $user_id));
        $arrInput = array(
            'key' => $key,
            'field' => 'time',
        );
        if(false === ($arrOutput = Service_Lib_Redis::HGET($arrInput))){
            Bingo_Log::warning('get time err! in[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_RANK_REDIS_TIMEOUT);
        }

        $today_time = strtotime(date('Y-m-d'));
        $get_time = intval($arrOutput);
        if($today_time <= $get_time){
            $arrInput = array(
                'user_id' => $user_id,
            );
            $arrOutput = self::getUserFlower($arrInput);
            $error = Tieba_Errcode::ERR_SUCCESS;
            $keys = array(
                'errno' => $error,
                'errmsg' => Tieba_Error::getErrmsg($error),
                'data'=> intval($arrOutput['data']),
            );
            return $keys;
        }

        //给用户加上鲜花
        $flower_cnt = 5;
        //区分会员用户
        $arrOutput = Service_Lib_Userinfo::getUserData($user_id);
        if(intval($arrOutput['mParr_props']['level']['end_time']) > time()){
            $flower_cnt = 10;
        }

        $arrInput = array(
            'key' => $key,
            'field' => 'flower',
            'step' => $flower_cnt,
        );
        if(false === ($arrOutput = Service_Lib_Redis::HINCRBY($arrInput))){
            Bingo_Log::warning('set flower err! in[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_RANK_REDIS_TIMEOUT);
        }
        $flower_cnt = intval($arrOutput);

        //更新当天获取鲜花时间
        $arrInput = array(
            'key' => $key,
            'field' => 'time',
            'value' => time(),
        );
        if(false === ($arrOutput = Service_Lib_Redis::HSET($arrInput))){
            Bingo_Log::warning('set time err! in[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_RANK_REDIS_TIMEOUT);
        }


        $error = Tieba_Errcode::ERR_SUCCESS;
        $keys = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $flower_cnt,
        );
        return $keys;
    }

    public static function getUserFlower($arrInput){

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['user_id'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input params.
        $user_id = intval($arrInput['user_id']);

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.

        //your code here......

        $key = self::_genKey(array(self::REIDS_FLOWER_INFO, $user_id));
        $arrInput = array(
            'key' => $key,
            'field' => 'flower',
        );
        if(false === ($arrOutput = Service_Lib_Redis::HGET($arrInput))){
            Bingo_Log::warning('get time err! in[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_RANK_REDIS_TIMEOUT);
        }

        $flower_cnt = intval($arrOutput);

        $error = Tieba_Errcode::ERR_SUCCESS;
        $keys = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $flower_cnt,
        );
        return $keys;
    }

    public static function reduceUserFlower($arrInput){

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['user_id'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input params.
        $user_id = intval($arrInput['user_id']);
        $cnt = intval($arrInput['cnt']);
        if($user_id <= 0 || $cnt <= 0){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.

        //your code here......

        $key = self::_genKey(array(self::REIDS_FLOWER_INFO, $user_id));
        $arrInput = array(
            'key' => $key,
            'field' => 'flower',
        );
        if(false === ($arrOutput = Service_Lib_Redis::HGET($arrInput))){
            Bingo_Log::warning('get time err! in[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_RANK_REDIS_TIMEOUT);
        }

        $flower_cnt = intval($arrOutput);

        if($flower_cnt < $cnt){
            Bingo_Log::warning('left flower not enough! input[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::VIDEO_FLOWER_NOT_ENOUGH);//VIDEO_FLOWER_NOT_ENOUGH
        }

        $arrInput = array(
            'key' => $key,
            'field' => 'flower',
            'step' => -$cnt,
        );
        if(false === ($arrOutput = Service_Lib_Redis::HINCRBY($arrInput))){
            Bingo_Log::warning('set flower err! in[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_RANK_REDIS_TIMEOUT);
        }



        $error = Tieba_Errcode::ERR_SUCCESS;
        $keys = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $keys;
    }

    private  static function _genKey($keys){
        return implode('_', $keys);
    }

    private static function _mgetLoginUserWeight($room_id, $user_ids){
        $arrInput = array(
            'room_id' => $room_id,
            'user_ids' => $user_ids,
        );
        $arrOutput = self::mgetUserInfo($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            return false;
        }
        $user_data = $arrOutput['data'];

        $keys = array();
        foreach($user_ids as $user_id){
            $weight = self::_calUserWeight($user_data[$user_id]);
            $keys[] = array(
                'user_id' => intval($user_id),
                'user_weight' => $weight,
            );
        }

        return $keys;
    }

    private static function _mlogin($room_id, $user_ids){
        //更新某一房间在线用户名列表, 用于前端搜索
        $key = self::_genKey( array(self::REIDS_HASH_ONLINE_USER, $room_id) );
        $arrInput = array(
            'user_id' => $user_ids,
        );
        $keys = Tieba_Service::call('user', 'getUnameByUids', $arrInput, NULL, NULL, 'post', 'php', self::$_service_ie);
        if(Tieba_Errcode::ERR_SUCCESS !== $keys['errno']){
            Bingo_Log::warning('get unames err! in['.serialize($user_ids).'] out['.serialize($keys).']');
            return false;
        }
        $unames = $keys['output']['unames'];
        //生成fileds, value
        $arrInput = array();
        foreach($unames as $_v){
            $intUserId = intval($_v['user_id']);
            $strUserName = $_v['user_name'];
            $arrInput[] = array(
                'key' => $key,
                'field' => $intUserId,
                'value' => $strUserName,
            );
        }
        if(false === Service_Lib_Redis::mHSET($arrInput)){
            Bingo_Log::warning('add unames err! in['.serialize($arrInput).']');
            return false;
        }

        //更新在线用户列表
        $key = self::_genKey(array(self::REIDS_ZSET_ONLINE_LIST, $room_id));
        $user_weights = self::_mgetLoginUserWeight($room_id, $user_ids);
        //生成score, member
        $arrInput = array(
            'key' => $key,
            'members' => array(),
        );
        foreach($user_weights as $_v){
            $intUserId = intval($_v['user_id']);
            $doubleUserWeights = doubleval($_v['user_weight']);
            $arrInput['members'][] = array( 'score' => $doubleUserWeights, 'member' => $intUserId);
        }
        if(false === Service_Lib_Redis::ZADD($arrInput)){
            Bingo_Log::warning('add uweight err! in['.serialize($arrInput).']');
            return false;
        }

        return true;
    }

    private static function _mlogout($room_id, $user_ids){
        //更新某一房间在线用户名列表, 用于前端搜索
        $key = self::_genKey( array(self::REIDS_HASH_ONLINE_USER, $room_id) );
        //生成fileds, value
        $arrInput = array();
        foreach($user_ids as $user_id){
            $arrInput[] = array(
                'key' => $key,
                'field' => intval($user_id),
            );
        }
        if(false === Service_Lib_Redis::mHDEL($arrInput)){
            Bingo_Log::warning('del uid err! in['.serialize($arrInput).']');
            return false;
        }

        //更新在线用户列表
        $key = self::_genKey(array(self::REIDS_ZSET_ONLINE_LIST, $room_id));
        //生成score, member
        $arrInput = array(
            'key' => $key,
            'members' => array(),
        );
        foreach($user_ids as $user_id){
            $arrInput['member'][] = intval($user_id);
        }
        if(false === Service_Lib_Redis::ZREM($arrInput)){
            Bingo_Log::warning('del uweight err! in['.serialize($arrInput).']');
            return false;
        }

        return true;
    }

    private static function _genItem($string){
        return "[$string]";
    }

    private static function _genScope($scope, $forum_id, $room_id){
        switch($scope){
            case self::RANK_SCOPE_ROOM:
                $string = self::_genKey(array($scope, $room_id));
                break;
            case self::RANK_SCOPE_FORUM:
                $string = self::_genKey(array($scope, $forum_id));
                break;
            case self::RANK_SCOPE_ALL:
                $string = $scope;
                break;
        }
        return $string;
    }

    public static function _genTimeInternal($time_internal){
        switch($time_internal){
            case self::RANK_TIME_DAY:
                $string = strtotime(date("Y-m-d"));
                break;
            case self::RANK_TIME_WEEK:
                $string = strtotime("last Monday");
                break;
            case self::RANK_TIME_MONTH:
                $string = strtotime(date("Y-m"));
                break;
            case self::RANK_TIME_FOREVER:
                $string = self::RANK_TIME_FOREVER;
                break;
        }
        return self::_genKey(array($time_internal, $string));
    }

    private static function _genLastTimeInternal($time_internal){
        switch($time_internal){
            case self::RANK_TIME_DAY:
                $string = strtotime(date("Y-m-d")) - 86400;
                break;
            case self::RANK_TIME_WEEK:
                $string = strtotime("-1 weeks monday");
                break;
            case self::RANK_TIME_MONTH:
                $month = date('m');
                if($month == 1){
                    $string = 0;
                    break;
                }
                $month = $month - 1;
                $year = date('Y');
                $string = strtotime("$year-$month");
                break;
            case self::RANK_TIME_FOREVER:
                $string = 0;
                break;
        }
        return self::_genKey(array($time_internal, $string));
    }

    private static function _setRanklist($req){
        $rank_name          =       $req['rank_name'];
        $scope              =       $req['scope'];
        $interval           =       $req['interval'];
        $user_id            =       $req['user_id'];
        $rt_user_id         =       $req['rt_user_id'];
        $room_id            =       $req['room_id'];
        $forum_id           =       $req['forum_id'];
        $props_id           =       $req['props_id'];
        $relation           =       $req['relation'];
        $score              =       $req['score'];
        $charm              =       $req['charm'];
        $gift_cnt           =       isset($req['gift_cnt']) ? intval($req['gift_cnt']) : 1;


        $input_zrem= array();
        $input_incr= array();
        switch($rank_name){
            case 'relation' :
                if(0 == $relation){
                    break;
                }
                for($time_type = 1; $time_type <= self::RANK_TIME_FOREVER; ++$time_type){
                    $prefix = self::_genItem(self::REIDS_RANK_RELATION);
                    $scope = self::_genItem(self::_genScope(self::RANK_SCOPE_ROOM, $forum_id, $room_id));
                    $begin_time = self::_genItem(self::_genTimeInternal($time_type));
                    $attr = self::_genItem($rt_user_id); //组播id

                    $key = self::_genKey( array($prefix, $scope, $begin_time, $attr) );


                    $input_incr[] = array(
                        'key' => $key,
                        'step' => $relation, //亲密度
                        'member' => $user_id, //用户id
                    );
                }
                //魅力值是全量数据不做zrem操作
//                $input_zrem[] = array(
//                    'key' => $key,
//                    'start' => 0,
//                    'stop' => self::MAX_RANK_CNT,
//                );
                break;
            case 'active' :
                break;
            case 'riche' :
                if(0 == $score){
                    break;
                }
                for($scope_type = 1; $scope_type <= self::RANK_SCOPE_ALL; ++$scope_type){
                    for($time_type = 1; $time_type <= self::RANK_TIME_FOREVER; ++$time_type){
                        $prefix = self::_genItem(self::REIDS_RANK_RICHE);
                        $scope = self::_genItem(self::_genScope($scope_type, $forum_id, $room_id));
                        $begin_time = self::_genItem(self::_genTimeInternal($time_type));
                        $attr = self::_genItem(self::RANK_ATTR_NONE);

                        $key = self::_genKey( array($prefix, $scope, $begin_time, $attr) );

                        $input_incr[] = array(
                            'key' => $key,
                            'step' => $score,
                            'member' => $user_id, //用户id
                        );

                        if(self::RANK_TIME_FOREVER == $scope_type){//总榜单不进行zrem操作, 保存全量数据
                            continue;
                        }
                        $input_zrem[] = array(
                            'key' => $key,
                            'start' => 0,
                            'stop' => self::MAX_RANK_CNT,
                        );
                    }
                }
                break;
            case 'star' :
                if(0 == $charm){
                    break;
                }
                for($scope_type = 1; $scope_type <= self::RANK_SCOPE_ALL; ++$scope_type){
                    for($time_type = 1; $time_type <= self::RANK_TIME_FOREVER; ++$time_type){
                        $prefix = self::_genItem(self::REIDS_RANK_STRA);
                        $scope = self::_genItem(self::_genScope($scope_type, $forum_id, $room_id));
                        $begin_time = self::_genItem(self::_genTimeInternal($time_type));
                        $attr = self::_genItem(self::RANK_ATTR_NONE);

                        $key = self::_genKey( array($prefix, $scope, $begin_time, $attr) );

                        $input_incr[] = array(
                            'key' => $key,
                            'step' => $charm, //魅力值
                            'member' => $rt_user_id, //用户id
                        );

                        if(self::RANK_TIME_FOREVER== $scope_type){//总榜单不进行zrem操作, 保存全量数据
                            continue;
                        }
                        $input_zrem[] = array(
                            'key' => $key,
                            'start' => 0,
                            'stop' => self::MAX_RANK_CNT,
                        );
                    }
                }
                break;
            case 'gift_give' :
                if(0 == $gift_cnt){
                    break;
                }
                for($scope_type = 1; $scope_type <= self::RANK_SCOPE_ALL; ++$scope_type){
                    for($time_type = 1; $time_type <= self::RANK_TIME_FOREVER; ++$time_type){
                        $prefix = self::_genItem(self::REIDS_RANK_GIFT_GIVE);
                        $scope = self::_genItem(self::_genScope($scope_type, $forum_id, $room_id));
                        $begin_time = self::_genItem(self::_genTimeInternal($time_type));
                        $attr = self::_genItem($props_id);

                        $key = self::_genKey( array($prefix, $scope, $begin_time, $attr) );

                        $input_incr[] = array(
                            'key' => $key,
                            'step' => $gift_cnt,
                            'member' => $user_id, //用户id
                        );

                        if(self::RANK_TIME_FOREVER == $scope_type){//总榜单不进行zrem操作, 保存全量数据
                            continue;
                        }
                        $input_zrem[] = array(
                            'key' => $key,
                            'start' => 0,
                            'stop' => self::MAX_RANK_CNT,
                        );
                    }
                }
                break;
            case 'gift_receive':
                break;//此数据不用暂时去掉
                if(0 == $gift_cnt){
                    break;
                }
                for($scope_type = 1; $scope_type <= self::RANK_SCOPE_ALL; ++$scope_type){
                    for($time_type = 1; $time_type <= self::RANK_TIME_FOREVER; ++$time_type){
                        $prefix = self::_genItem(self::REIDS_RANK_GIFT_RECEIVE);
                        $scope = self::_genItem(self::_genScope($scope_type, $forum_id, $room_id));
                        $begin_time = self::_genItem(self::_genTimeInternal($time_type));
                        $attr = self::_genItem($props_id);

                        $key = self::_genKey( array($prefix, $scope, $begin_time, $attr) );

                        $input_incr[] = array(
                            'key' => $key,
                            'step' => $gift_cnt,
                            'member' => $rt_user_id, //主播用户id
                        );

                        if(self::RANK_TIME_FOREVER == $scope_type){//总榜单不进行zrem操作, 保存全量数据
                            continue;
                        }
                        $input_zrem[] = array(
                            'key' => $key,
                            'start' => 0,
                            'stop' => self::MAX_RANK_CNT,
                        );
                    }
                }
                break;
        }

        Bingo_Log::debug(var_export($input_incr, true));
        //Bingo_Log::warning(var_export($input_zrem, true));

        if(isset($key)){
            if(false == Service_Lib_Redis::mZINCRBY($input_incr)){
                Bingo_Log::warning("set rank err! input[" . serialize($input_incr) . "]");
                return false;
            }
        }

        //按概率对zset中的数据进行截断操作, 这样做的目的是减少无价值数据量
        //if(self::_hitProbability() && !empty($input_zrem)){
        //    if(false == Service_Lib_Redis::mZREMRANGEBYRANK($input_zrem)){
        //        Bingo_Log::warning("zrem rank err! input[" . serialize($input_zrem) . "]");
        //        return false;
        //    }
        //}


        return true;
    }

    private static function _getRankKey($req){
        $rank_name          =       $req['rank_name'];
        $scope_type         =       $req['scope'];
        $interval           =       $req['interval'];
        $user_id            =       $req['user_id'];
        $rt_user_id         =       $req['rt_user_id'];
        $room_id            =       $req['room_id'];
        $forum_id           =       $req['forum_id'];
        $props_id           =       $req['props_id'];

        $pn                 =       isset($req['pn']) ? intval($req['pn']) : 0;
        $ps                 =       isset($req['ps']) ? intval($req['ps']) : 9;

        switch($rank_name){
            case 'relation' :
                $prefix = self::_genItem(self::REIDS_RANK_RELATION);
                $scope = self::_genItem(self::_genScope(self::RANK_SCOPE_ROOM, $forum_id, $room_id));
                $begin_time = self::_genItem(self::_genTimeInternal($interval));
                $attr = self::_genItem($rt_user_id); //主播id

                $key = self::_genKey( array($prefix, $scope, $begin_time, $attr) );

                break;
            case 'active' :
                break;
            case 'riche' :
                $prefix = self::_genItem(self::REIDS_RANK_RICHE);
                $scope = self::_genItem(self::_genScope($scope_type, $forum_id, $room_id));
                $begin_time = self::_genItem(self::_genTimeInternal($interval));
                $attr = self::_genItem(self::RANK_ATTR_NONE);

                $key = self::_genKey( array($prefix, $scope, $begin_time, $attr) );

                break;
            case 'star' :
                $prefix = self::_genItem(self::REIDS_RANK_STRA);
                $scope = self::_genItem(self::_genScope($scope_type, $forum_id, $room_id));
                $begin_time = self::_genItem(self::_genTimeInternal($interval));
                $attr = self::_genItem(self::RANK_ATTR_NONE);

                $key = self::_genKey( array($prefix, $scope, $begin_time, $attr) );

                break;
            case 'gift_give' :
                $prefix = self::_genItem(self::REIDS_RANK_GIFT_GIVE);
                $scope = self::_genItem(self::_genScope($scope_type, $forum_id, $room_id));
                $begin_time = self::_genItem(self::_genTimeInternal($interval));
                $attr = self::_genItem($props_id);

                $key = self::_genKey( array($prefix, $scope, $begin_time, $attr) );

                break;
            case 'gift_receive' :

                $prefix = self::_genItem(self::REIDS_RANK_GIFT_RECEIVE);
                $scope = self::_genItem(self::_genScope($scope_type, $forum_id, $room_id));
                $begin_time = self::_genItem(self::_genTimeInternal(self::RANK_TIME_WEEK));
                $attr = self::_genItem($props_id);

                $key = self::_genKey( array($prefix, $scope, $begin_time, $attr) );

                break;
        }

        Bingo_Log::debug(var_export($key, true));

        $arrInput = array();
        $arrInput[] = array(
            'key' => $key,
            'start' => $pn,
            'stop' => $ps,
        );

        return $arrInput;
    }

    private static function _hitProbability(){
        if(self::PROBABILITY_VALUE >= rand(1, 100)){
            return true;
        }
        return false;
    }

    public static function test($arrInput){
        Bingo_Log::debug(var_export($arrInput, true));
        return self::_setRanklist($arrInput['req']);
    }

    public static function rankNmqCallBack($arrInput){
        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        //input params.
        $data = Tieba_Service::getArrayParams($arrInput, 'data');
        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        //input params.

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.

        //your code here......
        if (!isset($data['command_no'])) {
            Bingo_Log::warning ( "command_no loss." );
            $errno = Tieba_Errcode::ERR_SUCCESS;
            return array(
                'errno' => $errno,
                'errmsg' => Tieba_Error::getErrmsg($errno),
            );

        }
        $intCmd = intval($data['command_no']);

        $intTransId = 0;
        if(isset($data ['trans_id'])) {
            $intTransId = intval($data['trans_id']);
        }
        Bingo_Log::pushNotice("command_no",$intCmd);
        Bingo_Log::pushNotice("trans_id",$intTransId);

        switch ($intCmd) {
            case  9700: //发礼物命令
                $req = array();
                $req['rank_name'] = $data['rank_name'];
                $req['user_id'] = intval($data['user_id']);
                $req['rt_user_id'] = intval($data['anchor_id']);
                $req['room_id'] = intval($data['room_id']);
                $req['forum_id'] = intval($data['forum_id']);
                $req['props_id'] = intval($data['props_id']);
                $req['relation'] = intval($data['add_intimate']);
                $req['score'] = intval($data['cost_scores']);
                $req['charm'] = intval($data['add_charm']);
                $req['gift_cnt'] = intval($data['send_num']);

                if('relation' == $req['rank_name']){
                    //旧榜单
                    $arrRankListOld = self::_getRelationList(intval($req['rt_user_id']), intval($req['room_id']));
                    //获取送礼用户的魅力值
                    $user_id = intval($req['user_id']);
                    $room_id = intval($req['room_id']);
                    $relation = intval($req['relation']);
                    $arrInput = array(
                        'user_id' => $user_id,
                        'rt_user_id' => intval($req['rt_user_id']),
                        'room_id' => $room_id,
                    );
                    $arrOutput = self::getRelation($arrInput);
                    $relation_value = intval($arrOutput['data']);
                    $arrUserItem = array(
                        'member' => $user_id,
                        'score' => $relation_value + $relation,
                        'role' => array(),
                        'user_id' => $user_id,
                    );
                }

                if(false === self::_setRanklist($req)){
                    Bingo_Log::warning('refresh rank err!');
                }

                //推亲密度列表
                $arrRankListOld[] = array('score' => 0);
                $arrRank = array();
                if('relation' == $req['rank_name']){
                    //为了去重, 过滤, 榜单可能已经存在这个发礼物的用户, 先给他过滤掉
                    foreach($arrRankListOld as $key => $value){
                        if($value['user_id'] == $user_id){
                            unset($arrRankListOld[$key]);
                        }
                    }
                    //下标连续在$arrRank[]解决

                    $pos = self::INF;
                    foreach($arrRankListOld as $key => $value){
                        if(intval($value['score']) < $arrUserItem['score'] && $pos == self::INF){
                            if($key < self::RANK_LIST_NUM){
                                $pos = $key;
                                //证明此用户是核心用户, 在10名榜单内部需要推送榜单数据进行更新
                                //补全此用户会员信息
                                $arrInput = array(
                                    'room_id' => $room_id,
                                    'user_ids' => array($user_id),
                                );
                                $arrOutput = self::mgetUserInfo($arrInput);
                                $arrUserItem = array_merge($arrUserItem, (array)$arrOutput['data'][$user_id]);
                                $arrRank[] = $arrUserItem;

                            }
                        }
                        if(0 != $value['score']){//之前的哨兵数据score==0不要
                            $arrRank[] = $value;
                        }
                    }
                    if($pos < self::RANK_LIST_NUM){
                        //发送榜单信息
                        $msgBody = array(
                            'msg_list' => array(
                                0 => array(
                                    'msg_type' => Service_Lib_Define::MSG_TYPE_RELATION_LIST,
                                    'uid' => intval($user_id),
                                    'room_id' => $room_id,
                                    'rank' => $arrRank,
                                )
                            )
                        );

                        $ret = Service_Lib_Lcs::sendMsgToLCSRoom($msgBody, 1, $room_id, 0);
                        Bingo_Log::pushNotice("sendLcs",intval($ret));

                    }
                }

                break;
            case  58006: //joinPcLiveGroup, im进入房间命令
                Bingo_Log::debug(var_export($data, true));
                if($data['user_id'] <= 0){//代表没登陆, 不计算未登陆的用户
                    break;
                }
                $room_id = intval($data['room_id']);
                if(/*($room_id == 48  || $room_id == 100185 )&& */rand(0, 100) < 20 &&
                        intval($data['cmd']) == self::LOGIN_CMD){//明星房间只推送登陆用户数目
                    if(intval($data['loginuser_num'] < 20)){
                        break;
                    }
                    $msgBody = array(
                        'msg_list' => array(
                            0 => array(
                                'msg_type' => self::LOGIN_CMD == intval($data['cmd']) ?
                                        Service_Lib_Define::MSG_TYPE_LINE : Service_Lib_Define::MSG_TYPE_LOGOUT,
                                'uid' => 0,
                                'room_id' => $room_id,
                                'user_name' => '',
                                'role' => '',
                                'mParr_props' => '',
                                'uname' => '',
                                'prop' => intval($data['cmd']),
                                'loginuser_num' => intval($data['loginuser_num']) * 8 + rand(0, 5),
                                'guessuser_num' => intval($data['guessuser_num']) * 8 + rand(0, 5),
                                'weight' => 0,
                                'flow_cnt' => 0,
                                'need_remind' => 0,
                            )
                        )
                    );

                    $ret = Service_Lib_Lcs::sendMsgToLCSRoom($msgBody, 1, $room_id, 0);
                    Bingo_Log::pushNotice("sendLcs",intval($ret));

                    break;
                }
                else /*if($room_id == 48 || $room_id == 100185)*/{
                    break;
                }
                $loginuser_num = intval($data['loginuser_num']);
                if($loginuser_num < self::MIN_RECOVER_USER_NUM && $loginuser_num != -1){//-1 代表上游取数据错误
                    $arrInput = array(
                        'room_id' => intval($data['room_id']),
                    );
                    $arrOutput = self::refreshOnlineList($arrInput);
                    if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
                        Bingo_Log::warning('refreshOnlineList err! input['.serialize($arrInput).']');
                    }
                }
                else{
                    $arrInput = array(
                        'room_id' => intval($data['room_id']),
                        'type' => intval($data['cmd']),
                        'user_ids' => array(intval($data['user_id'])),
                    );
                    $arrOutput = self::msetOnlineList($arrInput);
                    if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
                        Bingo_Log::warning('msetOnlineList err! input['.serialize($arrInput).']');
                    }
                }

                $user_id = intval($data['user_id']);
                $room_id = intval($data['room_id']);
                $need_remind = 1;
                if(self::LOGIN_CMD == intval($data['cmd'])){
                    //每天第一次登陆送给用户鲜花
                    $user_id = intval($data['user_id']);
                    $arrInput = array(
                        'user_id' => $user_id,
                    );
                    $arrOutput = self::giveFlower($arrInput);
                    Bingo_Log::debug(var_export($arrOutput, true));
                    $flow_cnt = 0;
                    if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
                        Bingo_Log::warning('give flower err! uid[' . $user_id .']');
                    }
                    else{
                        $flow_cnt = intval($arrOutput['data']);
                        Bingo_Log::pushNotice('flower', 1);
                    }


                    if (Service_Lib_Anti::loginCtrlSubmit($user_id,$room_id) > 0){
                        $need_remind = 0;
                    }

                }

                //UEG粒度控制 1. 单个用户3秒发一条 2. 整体每秒不超过10条. 注意: 由于这条策略生效的时候一定房间人数已经很多, 所以在线列表中人数和列表人数不强匹配的事情不必考虑
                if (Service_Lib_Anti::chatCtrlSubmit($user_id,$room_id) > 0){
                    Bingo_Log::warning('hit anti control!');
                    break;
                }
                $arrInput = array(
                    'room_id' => $room_id,
                    'user_ids' => array($user_id),
                );
                $arrOutput = self::mgetUserInfo($arrInput);
                $content = isset($arrOutput['data']) ? $arrOutput['data'] : array();
                //$arrNames = Service_Lib_Userinfo::getUserNameByUids(array($user_id));
                $msgBody = array(
                    'msg_list' => array(
                        0 => array(
                            'msg_type' => self::LOGIN_CMD == intval($data['cmd']) ?
                                    Service_Lib_Define::MSG_TYPE_LINE : Service_Lib_Define::MSG_TYPE_LOGOUT,
                            'uid' => intval($user_id),
                            'room_id' => $room_id,
                            'user_name' => $content[$user_id]['uname'],
                            'role' => $content[$user_id]['role'],
                            'mParr_props' => $content[$user_id]['mParr_props'],
                            'uname' => $content[$user_id]['uname'],
                            'prop' => intval($data['cmd']),
                            'loginuser_num' => intval($data['loginuser_num']),
                            'guessuser_num' => intval($data['guessuser_num']),
                            'weight' => self::_calUserWeight($content[$user_id]),
                            'flow_cnt' => $flow_cnt < 0 ? 0 : $flow_cnt,
                            'need_remind' => $need_remind,
                        )
                    )
                );

                $ret = Service_Lib_Lcs::sendMsgToLCSRoom($msgBody, 1, $room_id, 0);
                Bingo_Log::pushNotice("sendLcs",intval($ret));
                Bingo_Log::pushNotice("loginuser_num",intval($data['loginuser_num']));
                Bingo_Log::pushNotice("guessuser_num",intval($data['guessuser_num']));

                break;
            default :
                $errno = Tieba_Error::ERR_SUCCESS;
                $res = array(
                    'errno' => $errno,
                    'errmsg' => Tieba_Error::getErrmsg($errno),
                );
                break;
        }

        return $res;
    }

    private  static function _delAllRankKey(){
        $arrInput = array();

        $rank_list = array('relation', 'active', 'riche', 'star', 'gift_give', 'gift_receive');

        $forum_id = 6;
        $room_id = 6;
        $props_id = 6;
        $user_id = 6;
        $rt_user_id = 6;


        foreach($rank_list as $rank_name){
            for($scope_type = 1; $scope_type <= self::RANK_SCOPE_ALL; ++$scope_type){
                for($time_type = 1; $time_type <= self::RANK_TIME_FOREVER; ++$time_type){

                   $req['rank_name'] = $rank_name;
                   $req['scope'] = $scope_type;
                   $req['interval'] = $time_type;
                   $req['user_id'] = $user_id;
                   $req['rt_user_id'] = $rt_user_id;
                   $req['room_id'] = $room_id;
                   $req['forum_id'] = $forum_id;
                   $req['props_id'] = $props_id;

                    $arrInput[] = array(
                        'key' => self::_getRankKey($req),
                    );
                }
            }
        }

        if(false == Service_Lib_Redis::mDEL($arrInput)){
            Bingo_Log::warning('');
            return false;
        }

        return true;
    }

    private static function _calUserWeight($user_info){
        $weight = 0;
        $now = time();
        $role = $user_info['role'];

        if(true == $role[self::ROLE_RT]){
            $weight += self::ROLE_RT_VALUE;
        }
        else if(self::ADVANCED_MEMBER === $user_info['mParr_props']['level']['props_id'] &&
            $user_info['mParr_props']['level']['end_time'] > $now){
            $weight += self::ADVANCED_MEMBER_VALUE;
        }
        else if(self::COMMON_MEMBER=== $user_info['mParr_props']['level']['props_id'] &&
            $user_info['mParr_props']['level']['end_time'] > $now){
            $weight += self::COMMON_MEMBER_VALUE;
        }

        if(true == $role[self::ROLE_SOP]){
            $weight += self::ROLE_SOP_VALUE;
        }
        else if(true == $role[self::ROLE_OW]){
                $weight += self::ROLE_OW_VALUE;
        }
        else if(true == $role[self::ROLE_OP]){
            $weight += self::ROLE_OP_VALUE;
        }

        return $weight;
    }

    private static function _msendNmqCmd($arrInput)
    {
        if(empty($arrInput))
        {
            return true;
        }
        $input = end($arrInput);
        $command_no = $input['command_no'];
        $nmqClass = 'gradeSignIn';
        Bingo_Log::debug("inputs for nmq is : ". print_r($arrInput, true));
        $nmq_out = Tieba_Commit::multi_commit('sign', $nmqClass, $arrInput);
        Bingo_Log::debug("outputs for nmq is : ". print_r($nmq_out, true));
        if(empty($nmq_out))
        {
            Bingo_Log::warning("send cmds fail, input : ".serialize($nmq_out));
            $nmq_out = Tieba_Commit::multi_commit('sign', $nmqClass, $arrInput);
            Bingo_Log::debug("outputs for nmq is : ". print_r($nmq_out, true));
        }
        if(empty($nmq_out))
        {
            Bingo_Log::warning("fail to send cmd for input : ".serialize($arrInput));
            return false;
        }
        return true;
    }

    public static function nmqCommit($arrInput){

        $arrInput['command_no'] = 9700;
        foreach(self::$RANK_NAMES as $rank_name){
            $nmqParams = $arrInput;
            $nmqParams['rank_name'] = $rank_name;
            self::_sendNmqCmd($nmqParams);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $keys = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
        );
        return $keys;

    }

    private static function _sendNmqCmd($arrInput)
    {
        $nmqClass = 'sendGift';

        Bingo_Log::debug("input for nmq is : ". print_r($arrInput, true));
        $nmq_out = Tieba_Commit::commit('show', $nmqClass, $arrInput);
        Bingo_Log::debug("output for nmq is : ". print_r($nmq_out, true));
        if(!isset($nmq_out['error_no']) || 0 != $nmq_out['error_no'])
        {
            Bingo_Log::warning("send cmd fail, input[". serialize($arrInput) ."] output[".serialize($nmq_out) . "]");
            $r_nmq_out = Tieba_Commit::commit('show', $nmqClass, $arrInput);
            if(!isset($r_nmq_out['error_no']) || 0 != $r_nmq_out['error_no'])
            {
                Bingo_Log::warning("send cmd fail, input[". serialize($arrInput) ."] output[".serialize($nmq_out) . "]");
            }
        }
    }

    private static function _delOnlineList($room_id){
        $key = self::_genKey( array(self::REIDS_HASH_ONLINE_USER, $room_id) );
        $arrInput = array();
        $arrInput[] = array(
            'key' => $key,
        );
        $key = self::_genKey( array(self::REIDS_ZSET_ONLINE_LIST, $room_id) );
        $arrInput[] = array(
            'key' => $key,
        );
        if(false == Service_Lib_Redis::mDEL($arrInput)){
            Bingo_Log::warning('del online list err! input[' . serialize($arrInput). ']');
            return false;
        }
        return true;
    }

    public static function getRelation($arrInput){

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['user_id']) || !isset($arrInput['rt_user_id']) || !isset($arrInput['room_id'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input params.
        $user_id = intval($arrInput['user_id']);
        $rt_user_id = intval($arrInput['rt_user_id']);
        $room_id = intval($arrInput['room_id']);

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.

        //your code here......

        $prefix = self::_genItem(self::REIDS_RANK_RELATION);
        $scope = self::_genItem(self::_genScope(self::RANK_SCOPE_ROOM, 0, $room_id));
        $begin_time = self::_genItem(self::_genTimeInternal(self::RANK_TIME_DAY));
        $attr = self::_genItem($rt_user_id); //组播id

        $key = self::_genKey( array($prefix, $scope, $begin_time, $attr) );

        $arrInput = array(
            'key' => $key,
            'member' => $user_id,
        );
        if(false === ($arrOutput = Service_Lib_Redis::ZSCORE($arrInput))){
            Bingo_Log::warning('get time err! in[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_RANK_REDIS_TIMEOUT);
        }

        $relation_value = intval($arrOutput); //亲密度

        $error = Tieba_Errcode::ERR_SUCCESS;
        $keys = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $relation_value,
        );
        return $keys;
    }

    private static function _getRelationList($rt_user_id, $room_id){
        $arrInput = array (
            'reqs' =>
                array (
                    0 =>
                        array (
                            'rank_name' => 'relation',
                            'scope' => 1,
                            'interval' => self::RANK_TIME_DAY,
                            'rt_user_id' => $rt_user_id,
                            'room_id' => $room_id,
                        ),
                )
        );
        $arrOutput =  self::mgetRankList($arrInput);
        if ($arrOutput == false  || !isset($arrOutput['errno']) || intval($arrOutput['errno']) !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call getRankList error!, input: " . serialize($arrInput) . ' ,output:[ ' .serialize($arrOutput). ' ]');
        } else {
            $arrRanks = $arrOutput['data'][0] ? $arrOutput['data'][0] : array();
            $arrTmp = array();
            foreach($arrRanks as $user_id => $v){
                $v['user_id'] = $user_id;
                $arrTmp[] = $v;
            }
            $arrRanks = $arrTmp;
        }

        return $arrRanks;
    }





}
