<?php

/**
 * 吧的tag信息管理
 *
 * <AUTHOR>
 * @date Thu Jul 16 16:34:37 CST 2015
 */
class Service_Recommend_Task {

    const FORUMATTR_NAME = 'zhanqi_tag_id';

	/**
	 * 增加tag
	 *
	 * @param
	 * @return
	 */
	public static function submitTask($arrInput) {
        $storage_path   = strval($arrInput['storage_path']);
		$file_encoding  = strval($arrInput['file_encoding']);
        $task_type      = intval($arrInput['task_type']);
        $tag_id         = intval($arrInput['tag_id']);
        $level1         = strval($arrInput['level1']);
        $level2         = strval($arrInput['level2']);
        $official_forum = intval($arrInput['official_forum']);
        $tbapp_forum    = intval($arrInput['tbapp_forum']);
        $mask_forum     = strval($arrInput['mask_forum']);
        $op             = strval($arrInput['op']);
        if (!$tag_id || !$op) {
            Bingo_Log::warning("submit task param error. tag_id[$tag_id] op[$op]");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if ($task_type == 0
            && !$storage_path
            && (!$level1 || !$level2)) {
            Bingo_Log::warning("submit task param error.".
                " storage_path[$storage_path] level1[$level1] leve2[$level2]");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $file_encoding = $file_encoding === 'utf-8' ? 'utf-8' : 'gbk';

        $forum_type = 0;
        $forum_type = $official_forum ? 1 : 0;
        if ($tbapp_forum) {
            $forum_type += 2;
        }

        $time = time();
        if (($task_id = self::allocTaskid()) === false) {
            Bingo_Log::warning("alloc task id failed.");
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrFields = array(
            'field' => array(
                //'task_id'      => $task_id,
                'forum_type'   => $forum_type,
                'task_type'    => $task_type,
                'mask'         => $mask_forum,
                'tag_id'       => $tag_id,
                'level1'       => $level1,
                'level2'       => $level2,
                'storage_path' => $storage_path,
                'file_encoding'=> $file_encoding,
                'op'           => $op,
                'create_time'  => $time,
            ),
        );
        $obj = new Dl_Db_VideoTask();
		$ret = $obj->insert($arrFields);
		$errno = Tieba_Errcode::ERR_SUCCESS;
		if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning("add task failed. error[{$ret['errno']}]");
			$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
		}
		return Util_Function::errRet($errno, $ret['data']);
    }

    /**
     * 吧绑定一个标签
     *
     * @param
     * @return
     */
    public static function bindTag($arrInput) {
        $forum_id = intval($arrInput['forum_id']);
        $tag_id   = intval($arrInput['tag_id']);
        if (!$forum_id || !$tag_id) {
            Bingo_Log::warning("param error. forum[$forum_id] tag[$tag_id]");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (!self::updateBindTag($forum_id, $tag_id)) {
            Bingo_Log::warning("update tag info failed.");
            return Util_Function::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        if (!self::setTagToForumAttr($forum_id, $tag_id)) {
            Bingo_Log::warning("set forum attr failed.");
            return Util_Function::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 解除吧绑定的标签
     *
     * @param
     * @return
     */
    public static function unbindTag($arrInput) {
        $forum_id = intval($arrInput['forum_id']);
        if (!$forum_id) {
            Bingo_Log::warning("param error. forum[$forum_id]");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (!self::updateBindTag($forum_id, 0)) {
            Bingo_Log::warning("update tag info failed.");
            return Util_Function::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        if (!self::setTagToForumAttr($forum_id, 0)) {
            Bingo_Log::warning("set forum attr failed.");
            return Util_Function::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 获取未执行的task
     *
     * @param
     * @return
     */
    public static function getUnCompleteTask($arrInput) {
        $param = array(
            'cond' => array(
                'finish_time' => 0,
            ),
        );
        $obj = new Dl_Db_VideoTask();
		$ret = $obj->select($param);
		$errno = Tieba_Errcode::ERR_SUCCESS;
		if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning("get task failed. error[{$ret['errno']}]");
			$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
		}
		return Util_Function::errRet($errno, $ret['data']);
    }

    /**
     * 完成任务后更新任务状态
     *
     * @param
     * @return
     */
    public static function taskDone($arrInput) {
        $id = $arrInput['id'];
        if (!$id) {
            Bingo_Log::warning("param error. id[$id]");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $param = array(
            'field' => array(
                'finish_time' => time(),
            ),
            'cond' => array(
                'id' => $id,
            ),
        );
        $obj = new Dl_Db_VideoTask();
		$ret = $obj->update($param);
		$errno = Tieba_Errcode::ERR_SUCCESS;
		if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning("update task status failed. error[{$ret['errno']}]");
			$errno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
		}
		return Util_Function::errRet($errno, $ret['data']);
    }

    /**
     * 更新db中的吧和tag的对应关系
     *
     * @param
     * @return
     */
    protected static function updateBindTag($forum_id, $tag_id) {
        $param = array(
            'forum_id' => $forum_id,
            'tag_id'   => $tag_id,
        );
        $ret = Service_Recommend_Forum::updateBindTag($param);
        if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("update bind tag failed.");
            return false;
        }
        return true;
    }

    /**
     * 设置吧属性中的tag标签
     *
     * @param
     * @return
     */
    protected static function setTagToForumAttr($forum_id, $tag_id) {
        $arrInput = array(
            'forum_id'   => $forum_id,
            'attr_name'  => self::FORUMATTR_NAME,
            'attr_value' => $tag_id,
        );
        $arrRet = Tieba_Service::call('forum','setForumAttr',$arrInput);
        if($arrRet == false){
            Bingo_Log::warning("set forum attr failed. tag[$tag_id]");
            return false;
        }
        return true;
    }

	/**
	 * 分配Id
	 *
	 * @param
	 * @return
	 */
	protected static function allocTaskid() {
        $obj = new Dl_Db_VideoTask();
        $ret = $obj->getMaxId();
		if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning("get task max id failed. error[{$ret['errno']}]");
            return false;
		}
        return ($ret['data'] === null ? 1 : $ret['data'] + 1);
	}

}
