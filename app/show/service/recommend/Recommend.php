<?php

/**
 * frs页视频推荐mis
 *
 * <AUTHOR> tanxinyun
 * @date Thu Jul 16 16:34:37 CST 2015
 */
class Service_Recommend_Recommend {

    /**
     * 保存第三方平台配置
     * @param  [array] $arrInput [description]
     * @return [array]           [description]
     */
    public static function savePlatform($arrInput) {
        return Service_Recommend_Platform::save($arrInput);
    }

    /**
     * 增加第三方平台
     *
     * @param
     * @return
     */
    public static function addPlatform($arrInput) {
		return Service_Recommend_Platform::add($arrInput);
    }

    /**
     * 修改平台配置
     *
     * @param
     * @return
     */
    public static function modifyPlatform($arrInput) {
		return Service_Recommend_Platform::modify($arrInput);
    }

    /**
     * 获取平台列表
     *
     * @param
     * @return
     */
    public static function getPlatformList($arrInput) {
		return Service_Recommend_Platform::getList($arrInput);
    }

    /**
     * 为平台增加一个tag（tag对应一组视频，在frs展现）
     *
     * @param
     * @return
     */
    public static function addTag($arrInput) {
		return Service_Recommend_Tag::add($arrInput);
    }

    /**
     * 修改tag信息
     *
     * @param
     * @return
     */
    public static function modifyTag($arrInput) {
		return Service_Recommend_Tag::modify($arrInput);
    }

    /**
     * 根据平台（可选）、一级分类（可选）、二级分类（可选）查询tag
     *
     * @param
     * @return
     */
    public static function getTagInfo($arrInput) {
		return Service_Recommend_Tag::getTagInfo($arrInput);
    }

    /**
     * 获取线上的tag信息
     *
     * @param
     * @return
     */
    public static function getTagWithPlatform($arrInput) {
		return Service_Recommend_Tag::getTagWithPlatform($arrInput);
    }

    /**
     * 根据tag获取推荐的内容
     *
     * @param
     * @return
     */
    public static function getRecommendByTag($arrInput) {
		return Service_Recommend_Platform::getRecommendByTag($arrInput);
    }

    /**
     * 设置tag的推荐内容
     *
     * @param
     * @return
     */
    public static function setRecommendByTag($arrInput) {
		return Service_Recommend_Tag::setRecommendByTag($arrInput);
    }

    /**
     * 把tag和吧绑定
     * 可以绑定目录下的所有吧，也可以上传吧列表，耗时会非常长
     * 因此这个方法只是提交一个任务，具体操作由定时脚本来执行 
     *
     * @param
     * @return
     */
    public static function submitTask($arrInput) {
        return Service_Recommend_Task::submitTask($arrInput);
    }

    /**
     * 吧绑定tag
     *
     * @param
     * @return
     */
    public static function bindTag($arrInput) {
        return Service_Recommend_Task::bindTag($arrInput);
    }

    /**
     * 解除吧绑定
     *
     * @param
     * @return
     */
    public static function unbindTag($arrInput) {
        return Service_Recommend_Task::unbindTag($arrInput);
    }

    /**
     * 获取未执行的task
     *
     * @param
     * @return
     */
    public static function getUnCompleteTask($arrInput) {
        return Service_Recommend_Task::getUnCompleteTask($arrInput);
    }

    /**
     * 任务完成后更新状态
     *
     * @param
     * @return
     */
    public static function taskDone($arrInput) {
        return Service_Recommend_Task::taskDone($arrInput);
    }

    /**
     * 获取和tag绑定的吧列表
     *
     * @param
     * @return
     */
    public static function getForumsByTagId($arrInput) {
        return Service_Recommend_Forum::getForumsByTagId($arrInput);
    }

    /**
     * 清除tag下面的推荐视频
     *
     * @param
     * @return
     */
    public static function clearContentOfTag($arrInput) {
    }
}
