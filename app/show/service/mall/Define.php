<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file 
 * <AUTHOR>
 * @date 2013/08/12 14:32:25
 * @brief 
 *  
 **/

class Service_Mall_Define {
	const STLOG_MID = "tbmall";
	
	const DEFAUL_SCORES_FROM_TYPE = 'unknow';
	const MAX_NAMEPLATE_LEN = 28;
	

	const PROPS_NULL 	= 0;
	const PROPS_OK   	= 1;
	const PROPS_DEL  	= 2;
    
	
	const NUMMAX = 1000000000;
	
    //add by tangyan, 20140303
    //---------BEGIN-----------
    const PROPS_IS_RECOMMENED = 1;
    const PROPS_NEW_ARRIVAL = 3;
    const PROPS_STAR = 1;
    //add by tangyan, 20140303
    //---------END-----------
	
	const SCORES_IN  	= 0;
	const SCORES_OUT 	= 1;
	
	const PROPS_NOUSED 	= 0;
	const PROPS_USED 	= 1;
	const PROPS_CANCEL 	= 2;
	const PROPS_EXPIRED 	= 3;
	const PROPS_NOEXPIRED   = 4;
	const PROPS_UNOPEN 	= 0;
	const PROPS_OPEN 	= 1;
	const PROPS_TYPE_TIME	= 0;
	const PROPS_TYPE_COUNT	= 1;
	
	const PROPS_CATEGORY_RECOMMEND 		    = 0; //热门推荐
	const PROPS_CATEGORY_POSTBUBBLE 		= 101; //发帖气泡
	const PROPS_CATEGORY_PICWATERMARK  		= 103; //图片水印
	const PROPS_CATEGORY_COLORFONT			= 104; //炫彩字体
	const PROPS_CATEGORY_USERLEVEL			= 105; //全吧等级
	const PROPS_CATEGORY_STAR   			= 106; //明星道具包
	const PROPS_CATEGORY_APPRAISE           = 107; //魔法弹
	const PROPS_CATEGORY_SIGN               = 108; //补签卡
	const PROPS_CATEGORY_PORTRAIT           = 109; //动态头像
	const PROPS_CATEGORY_AVATARFRAME        = 110; //头像边框
	const PROPS_CATEGORY_HOME               = 113; //个人中心背景
	const PROPS_CATEGORY_NAMEPLATE          = 112; //个性铭牌
	const PROPS_CATEGORY_SAVEFACE           = 117; //挽尊卡
	const PROPS_CATEGORY_REBUILDNAMEPLATE   = 114; //重铸卡
	const PROPS_CATEGORY_LOTTERY            = 115; //抽奖道具
    const PROPS_CATEGORY_BILLBOARD          = 116; //贴条
    const PROPS_CATEGORY_MEMBERTOP          = 118; //会员置顶卡
    
    const PROPS_CATEGORY_VIDEO              = 130; //视频道具
    
    
    
	const NMQ_COMMAND_CHANGE_HEAD_PIC = 10000;
	const NMQ_COMMAND_ADDSCORES = 55103;
	
	const REDIS_PROPS_LIST_BY_CATEGORY_EXPIRE = 60; 		// 60   1min
	const REDIS_PROPS_LIST_EXPIRE = 60;
	const REDIS_PROPS_ONE_EXPIRE  = 60;
	
	const NAMEPLATE_PAGE_SIZE = 48;
	const MIN_NAMEPLATE_ID = 1120000001;
	const NAMEPLATE_ALL_STATUS = 0;
	const NAMEPLATE_UNSOLD_STATUS = 3;
	const NAMEPLATE_SOLD_STATUS = 4;
	const PREFIX_MAX_NAMEPLATE_ID = 'max_nameplate_id_';
	const PREFIX_RAND_NAMEPLATE = 'rand_nameplate_';
	
	const USERDATA_KEY_APPRAISE_PROPS = 'appraise';
	const USERDATA_KEY_SCORES_PROPS = 'Parr_scores';
	const USERDATA_KEY_MPROPS = 'mParr_props';
	const POSTDATA_KEY_APPRAISE_PROPS = 'appraise';
	const POSTDATA_KEY_SOFA_PROPS = 'sofa';
	
	const APPRAISE_SHOW_TIME = '3600';
	
	const SERVICE_NAME = "Service_Tbmall";
	const DATABASE_NAME = "forum_tbmall";
	const MEMCACHED_NAME = "forum_tbmall";
	const PREFIX_REDIS = 'tbmall';
	const REDIS_NAME = "tbscore";
	



	//added by gzh
	const MSG_PROPS_EXPIRING_TIME = 604800;//7 days
	const MSG_PROPS_TYPE_LEVEL_EXPIRING = "level_expiring";
	const MSG_PROPS_TYPE_LEVEL_EXPIRED = "level_expired";
	const MSG_PROPS_TYPE_PROPS_EXPIRED = "props_expired";
	const MSGPOOL_TYPE_TBSCORE = 3;
	const MSG_PROPS_CATEGORY_PROPS = 0;
	const MSG_PROPS_CATEGORY_LEVEL = 1;
	const MSG_NO_NOTIFY = 0;
	const MSG_HAS_NOTIFY = 1;
	
	const REDIS_KEY_PROPS_CORRECT = "tbmall_props_correct_";
	const REDIS_KEY_SAVEFACE = "saveface_";
	const USERDATA_KEY_TBMALL_PROPS = "Parr_props";
	const USERDATA_VALUE_TBMALL_PROPS_CORRECT_PROBABILITY = 10;//概率更新，1-100的概率值
	const USERDATA_VALUE_TBMALL_PROPS_CORRECT_COUNT_MAX = 10;//频率更新
	const USERDATA_VALUE_TBMALL_PROPS_CORRECT_TIMEOUT = 86400;//超时更新，单位秒
	
	const TBMALLMSG_CATEGORY_PROPS_EXPIRED = 1;
	const TBMALLMSG_CATEGORY_LEVEL_EXPIRED = 4;
	const TBMALLMSG_CATEGORY_GOOD_THREAD = 3;
	
	/* 消费类型 调用buyOneProps、reduceUserScoresOrder时使用
	 *0-商城购买 1-抽奖 2-吧皮肤 3-点券购买 4-活动赠送 5-API赠送 7-智能版购买',
	*/
	const BUY_ORDER_TYPE_MALL = 0;//PC商城渠道购买
	const BUY_ORDER_TYPE_LOTTERY = 1;//抽奖中的
	const BUY_ORDER_TYPE_FORUMSKIN = 2; //吧皮肤
	const BUY_ORDER_TYPE_DIANJUAN = 3; //通过点劵开通会员时使用。
	const BUY_ORDER_TYPE_ACT = 4; //通过活动赠送的
	const BUY_ORDER_TYPE_API = 5; //通过API接口赠送的
	const BUY_ORDER_TYPE_SOFA = 6; //强沙发
	const BUY_ORDER_TYPE_WAP = 7; //通过智能版购买
	const BUY_ORDER_TYPE_IOS = 8; //通过ios购买
	const BUY_ORDER_TYPE_ANDROID = 9; //通过android购买	
	const BUY_ORDER_TYPE_EGGS_SEARCH = 10; //搜彩蛋服务费
	const BUY_ORDER_TYPE_API_FEE = 11; //API服务费
	const BUY_ORDER_TYPE_PC = 12; //通过PC支付购买
	const BUY_ORDER_TYPE_REWARDTHREAD = 13; //奖励贴
	const BUY_ORDER_TYPE_FETCHWATER = 14; //fetch水费
	const BUY_ORDER_TYPE_VIDEO = 20; //视频直播
	const BUY_ORDER_TYPE_VIDEO_MO = 38; //视频直播智能版

}


/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
