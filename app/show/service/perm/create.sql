create database forum_show default charset utf8 COLLATE utf8_general_ci;
use forum_show;
CREATE TABLE `user_room_perm_ban` (
    `id` bigint(20) unsigned NOT NULL auto_increment,
    `user_id` int(11) unsigned NOT NULL COMMENT 'user_id',
    `room_id` int(11) unsigned NOT NULL COMMENT 'room_id',
    `perm_id` int(11) unsigned NOT NULL COMMENT 'perm_id',
    `expire_time` int(11) unsigned NOT NULL default '4294967295',
    `update_time` int(11) unsigned NOT NULL default '0' COMMENT 'update_time',
    `no_use` int(11) unsigned NOT NULL default '0',
    PRIMARY KEY  (`id`),
    UNIQUE KEY `user_room_ban` (`user_id`,`room_id`,`perm_id`),
    KEY `user_expire_time` (`user_id`,`expire_time`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='ok';

CREATE TABLE `sop_user` (
    `id` bigint(20) unsigned NOT NULL auto_increment,
    `user_id` int(11) unsigned NOT NULL COMMENT 'user_id',
    `expire_time` int(11) unsigned NOT NULL default '0',
    `update_time` int(11) unsigned NOT NULL default '0' COMMENT 'update_time',
    `no_use` int(11) unsigned NOT NULL default '0',
    PRIMARY KEY  (`id`),
    UNIQUE KEY `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='ok';

CREATE TABLE `user_room_role` (
    `id` bigint(20) unsigned NOT NULL auto_increment,
    `user_id` int(11) unsigned NOT NULL COMMENT 'user_id',
    `room_id` int(11) unsigned NOT NULL COMMENT 'room_id',
    `role_id` int(11) unsigned NOT NULL COMMENT 'role_id',
    `expire_time` int(11) unsigned NOT NULL default '0',
    `update_time` int(11) unsigned NOT NULL default '0' COMMENT 'update_time',
    `no_use` int(11) unsigned NOT NULL default '0',
    PRIMARY KEY  (`id`),
    UNIQUE KEY `user_room_role` (`user_id`,`room_id`,`role_id`),
    KEY `room_role` (`room_id`,`role_id`),
    KEY `user_role` (`user_id`,`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='ok';
