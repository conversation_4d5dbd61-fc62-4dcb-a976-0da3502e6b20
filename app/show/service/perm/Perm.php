<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014:05:08 10:53:36
 * @version 
 * @structs & methods(copied from idl.)
*/



define("MODULE","Show_service");
class Service_Perm_Perm{

const SERVICE_NAME = "Service_Perm_Perm";
protected static $_conf = null;
protected static $_use_split_db = false;
const DATABASE_NAME = "forum_show";
const MEMCACHED_NAME = "forum_show";
protected static $_cache = null;

const REDIS_NAME = "show";
const ALL_ROOM_ID = 1;
const MAX_INT = 2147483647;
const RT = 90001;
const OP = 90002;
const OW = 90003;
const CAN_CHAT=30002;
const CAN_SHOW=30004;
const CAN_HORN=30007;
const ALL_PERM_ID= 1;
const MGET_MAX=50;

protected static $_redis = null;

protected static $_perm_map = array( //加u是为了防止和吧权限系统冲突
    30001=>'u_can_notice', //发房间公告
    30002=>'u_can_chat', //聊天
    30003=>'u_can_ctrl_room_chat', //对房间 禁止/解禁公聊
    30004=>'u_can_show', //上麦表演
    30005=>'u_can_ctrl_show', //封禁/解禁 上麦
    30006=>'u_can_give', //送礼
    30007=>'u_can_horn', //喇叭
    30008=>'u_can_ctrl_user_chat', //对人 禁止/解禁聊天(踢人)
    30009=>'u_can_ctrl_user_black', //对人 加入/解禁黑名单,黑名单用户丧失所有权限
    self::ALL_PERM_ID=> 'u_can_base',//封禁
);
protected static $_role_map = array( //加u是为了防止和吧权限系统冲突
    self::RT=>'u_is_rt', //主播
    self::OP=>'u_is_op', //房间管理员
    self::OW=>'u_is_ow', //经纪公司管理员
);
protected static $_default_perm = array(
    'u_can_chat' => true,
    'u_can_give' => true,
    'u_can_horn' => true,
    );
protected static $_sop_perm = array(
    'u_can_notice' => true,
    'u_can_ctrl_show' =>true,
    'u_can_ctrl_room_chat' =>true,
    'u_can_ctrl_user_chat' =>true,
    'u_can_ctrl_user_black' =>true,
    );
protected static $_role_perm_map = array(
    'u_is_rt'=> array(
        'u_can_notice' =>true,
        'u_can_show' =>true,
        'u_can_ctrl_room_chat' =>true,
        'u_can_ctrl_user_chat' =>true,
    ),
    'u_is_op'=> array(
        'u_can_notice' =>true,
        'u_can_ctrl_room_chat' =>true,
        'u_can_ctrl_user_chat' =>true,
    ),
    'u_is_ow'=> array(
        'u_can_notice' =>true,
        'u_can_ctrl_show' =>true,
        'u_can_ctrl_room_chat' =>true,
        'u_can_ctrl_user_chat' =>true,
        'u_can_ctrl_user_black' =>true,
    ),
);

/**
 * @brief get mysql obj.
 * @return: obj of Bd_DB, or null if connect fail.

**/		
private static function _getDB(){
    $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
    if($objTbMysql && $objTbMysql->isConnected()) {
        return $objTbMysql;
    } else {
    	Bingo_Log::warning("db connect fail.");
        return null;
    }
}

/**
 * @brief get cache obj.
 * @return: obj of Bingo_Cache_Memcached, or null if connect fail.

**/
private static function _getCache(){
	if(self::$_cache){
		return self::$_cache ;
	}
	Bingo_Timer::start('memcached_init');
	self::$_cache = new Bingo_Cache_Memcached(self::MEMCACHED_NAME);
	Bingo_Timer::end('memcached_init');

	if(!self::$_cache || !self::$_cache->isEnable()){
		Bingo_Log::warning("init cache fail.");
		self::$_cache = null;
		return null;
	}
	return self::$_cache;
}

/**
 * @brief get redis obj.
 * @return: obj of Bingo_Cache_Redis, or null if connect fail.

**/
private static function _getRedis(){
	if(self::$_redis){
		return self::$_redis ;
	}
	Bingo_Timer::start('redis_init');
	self::$_redis = new Bingo_Cache_Redis(self::REDIS_NAME);
	Bingo_Timer::end('redis_init');

	if(!self::$_redis || !self::$_redis->isEnable()){
		Bingo_Log::warning("init redis fail.");
		self::$_redis = null;
		return null;
	}
	return self::$_redis;
}

	
/**
 * @brief init
 * @return: true if success. false if fail.

**/		
private static function _init(){
	
	//add init code here. init will be called at every public function beginning.
	//not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
	//init should be recalled for many times.
	
	if(self::$_conf == null){	
		self::$_conf = Bd_Conf::getConf("/app/show/service_perm_perm");
		if(self::$_conf == false){
			Bingo_Log::warning("init get conf fail.");
			return false;
		}
		
	}
	return true; 
}


private static function _errRet($errno){
    return array(
        'errno' => $errno,
        'errmsg' => Tieba_Error::getErrmsg($errno),
    );
}

public static function preCall($arrInput){
    // pre-call hook
}

public static function postCall($arrInput){
    // post-call hook
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32_t room_id
 * @return: $arrOutput
 * 	perm_role output
**/
public static function getLivePerm($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['room_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);
	$room_id = intval($arrInput['room_id']);

    if($user_id<=0 || $room_id<=0)
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = false;

    //your code here......
    $user_perm = self::$_default_perm; 
    $user_role = array();

    $_role = self::_get_user_role($user_id);
    if(is_array($_role))
    {
        if(isset($_role['u_is_sop'])) 
        {
            $user_perm = array_merge($user_perm,self::$_sop_perm); 
            $user_role = array_merge($user_role,array('u_is_sop'=>true,));
        }
        if(is_array($_role[$room_id]))
        {
            $room_perm = array();
            $room_role = array();
            foreach($_role[$room_id] as $role_id =>$bool)
            {
                if(isset(self::$_role_map[$role_id]))
                {
                    $role_name=self::$_role_map[$role_id];
                    $room_role[$role_name] = true;
                    if(is_array(self::$_role_perm_map[$role_name]))
                    {
                        $room_perm = array_merge($room_perm,self::$_role_perm_map[$role_name]); 
                    } 
                }
            }
            $user_perm = array_merge($user_perm,$room_perm); 
            $user_role = array_merge($user_role,$room_role);
        } 
    }

    $all_ban_perm = self::_get_user_ban_perm($user_id);
    $ban_result = array();
    if(is_array($all_ban_perm))
    {
        $ban_room_array = array();
        $ban_global_array = array();
        if(is_array($all_ban_perm[$room_id]))
        {
            foreach($all_ban_perm[$room_id] as $perm_id => $time)
            {
                if(isset(self::$_perm_map[$perm_id]))
                {
                    $perm_name = self::$_perm_map[$perm_id];
                    $ban_room_array[$perm_name] = $time;
                }
            } 
        } 
        if(is_array($all_ban_perm[self::ALL_ROOM_ID]))
        {
            foreach($all_ban_perm[self::ALL_ROOM_ID] as $perm_id => $time)
            {
                if(isset(self::$_perm_map[$perm_id]))
                {
                    $perm_name = self::$_perm_map[$perm_id];
                    $ban_global_array[$perm_name] = $time;
                }
            } 
        }
//        $ban_result = array_merge($ban_room_array,$ban_global_array);
        $ban_result = $ban_room_array;
        foreach($ban_global_array as $perm_name=>$time)
        {
            $ban_result[$perm_name] = ( isset($ban_result[$perm_name]) && ($ban_result[$perm_name]>=$time) ) ? $ban_result[$perm_name] : $time;
        }
        if(isset($ban_result['u_can_base']))
        {
            $user_perm = array();
        } 
        else
        {  
            foreach($ban_result as $perm_name => $time)
            {
                if(isset($user_perm[$perm_name]))
                {
                    unset($user_perm[$perm_name]);
                }
            }
        } 
    } 


    $output = array(
        'perm' => $user_perm,
        'role' => $user_role,
        'ban_perm' => $ban_result,
        );

	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id[]
 * 	uint32_t room_id
 * @return: $arrOutput
 * 	mrole output
**/
public static function mgetLiveRole($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if( !isset($arrInput['user_id']) || !isset($arrInput['room_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_ids = Tieba_Service::getArrayParams($arrInput, 'user_id');
	$room_id = intval($arrInput['room_id']);
    if($room_id<=0)
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = array();

    $i = 0;

    foreach($user_ids as $user_id)
    {
        $user_role = array();
        $_role = self::_get_user_role($user_id);
        if(is_array($_role))
        {
            if(isset($_role['u_is_sop'])) 
            {
                $user_role = array_merge($user_role,array('u_is_sop'=>true,));
            }
            if(is_array($_role[$room_id]))
            {
                $room_role = array();
                foreach($_role[$room_id] as $role_id =>$bool)
                {
                    if(isset(self::$_role_map[$role_id]))
                    {
                        $role_name=self::$_role_map[$role_id];
                        $room_role[$role_name] = true;
                    }
                }
                $user_role = array_merge($user_role,$room_role);
            }

        }
        $output[$user_id] = $user_role;
        $i++;
        if($i>=self::MGET_MAX)
        {
            break;
        }
    }
	//your code here......

	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * @return: $arrOutput
 * 	setout output
**/
public static function setLiveSOP($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = array();

	//your code here......
	$error = Tieba_Errcode::ERR_SUCCESS;
    $ret = self::_set_sop($user_id);
    if($ret === false)
    {
        Bingo_Log::warning("call set_sop err! user_id:$user_id");
        $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
    }

	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * @return: $arrOutput
 * 	setout output
**/
public static function deleteLiveSOP($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = array();

	//your code here......
	$error = Tieba_Errcode::ERR_SUCCESS;
    $ret = self::_delete_sop($user_id);
    if($ret === false)
    {
        Bingo_Log::warning("call delete_sop err! user_id:$user_id");
        $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
    }

	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32 room_id
 * @return: $arrOutput
 * 	setout output
**/
public static function setLiveOW($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['room_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);
	$room_id = intval($arrInput['room_id']);
    
    if($user_id<=0 || $room_id<=0){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = array();

	//your code here......
    $error = Tieba_Errcode::ERR_SUCCESS;

    $ret = self::_set_user_room_role($user_id,$room_id,self::OW); 
    if($ret === false)
    {
        Bingo_Log::warning("call _set_user_room_role err! user_id:$user_id, room_id:$room_id");
        $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
    }

	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32 room_id
 * @return: $arrOutput
 * 	setout output
**/
public static function deleteLiveOW($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['room_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);
	$room_id = intval($arrInput['room_id']);
    
    if($user_id<=0 || $room_id<=0){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = array();

	//your code here......
    $error = Tieba_Errcode::ERR_SUCCESS;

    $ret = self::_delete_user_room_role($user_id,$room_id,self::OW); 
    if($ret === false)
    {
        Bingo_Log::warning("call _delete_user_room_role err! user_id:$user_id, room_id:$room_id");
        $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
    }

	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32 room_id
 * @return: $arrOutput
 * 	setout output
**/
public static function setLiveOP($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['room_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);
	$room_id = intval($arrInput['room_id']);

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = array();

	//your code here......
	$error = Tieba_Errcode::ERR_SUCCESS;
    $ret = self::_set_user_room_role($user_id,$room_id,self::OP); 
    if($ret === false)
    {
        Bingo_Log::warning("call _set_user_room_role err! user_id:$user_id, room_id:$room_id");
        $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
    }
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32 room_id
 * @return: $arrOutput
 * 	setout output
**/
public static function deleteLiveOP($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['room_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);
	$room_id = intval($arrInput['room_id']);

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = array();

	//your code here......
	$error = Tieba_Errcode::ERR_SUCCESS;
    $ret = self::_delete_user_room_role($user_id,$room_id,self::OP); 
    if($ret === false)
    {
        Bingo_Log::warning("call _delete_user_room_role err! user_id:$user_id, room_id:$room_id");
        $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
    }
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32 room_id
 * @return: $arrOutput
 * 	setout output
**/
public static function setLiveRT($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['room_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);
	$room_id = intval($arrInput['room_id']);

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = array();

	//your code here......
	$error = Tieba_Errcode::ERR_SUCCESS;
    $ret = self::_set_user_room_role($user_id,$room_id,self::RT); 
    if($ret === false)
    {
        Bingo_Log::warning("call _set_user_room_role err! user_id:$user_id, room_id:$room_id");
        $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
    }
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32 room_id
 * @return: $arrOutput
 * 	setout output
**/
public static function deleteLiveRT($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['room_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);
	$room_id = intval($arrInput['room_id']);

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = array();

	//your code here......
	$error = Tieba_Errcode::ERR_SUCCESS;
    $ret = self::_delete_user_room_role($user_id,$room_id,self::RT); 
    if($ret === false)
    {
        Bingo_Log::warning("call _delete_user_room_role err! user_id:$user_id, room_id:$room_id");
        $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
    }
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32_t room_id
 * 	uint32_t time_len
 * @return: $arrOutput
 * 	setout output
**/
public static function banLiveShow($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['room_id']) || !isset($arrInput['time_len'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);
	$room_id = intval($arrInput['room_id']);
	$time_len = intval($arrInput['time_len']);

    if($user_id<=0 || $time_len<=0)
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }
    if($room_id<=0 && $room_id!==self::ALL_ROOM_ID)
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = array();

	//your code here......
	$error = Tieba_Errcode::ERR_SUCCESS;
    $ret = self::_ban_user_room_perm($user_id,$room_id,self::CAN_SHOW,$time_len); 
    if($ret === false)
    {
        Bingo_Log::warning("call _ban_user_room_perm err! user_id:$user_id, room_id:$room_id");
        $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
    }
    $arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32_t room_id
 * @return: $arrOutput
 * 	setout output
**/
public static function cancelBanLiveShow($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['room_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);
	$room_id = intval($arrInput['room_id']);
    if($user_id<=0 )
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }
    if($room_id<=0 && $room_id!==self::ALL_ROOM_ID)
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = array();

	//your code here......
	$error = Tieba_Errcode::ERR_SUCCESS;
    $ret = self::_cancel_ban_user_room_perm($user_id,$room_id,self::CAN_SHOW); 
    if($ret === false)
    {
        Bingo_Log::warning("call _cancel_ban_user_room_perm err! user_id:$user_id, room_id:$room_id");
        $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
    }
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32_t room_id
 * 	uint32_t time_len
 * @return: $arrOutput
 * 	setout output
**/
public static function banLiveChat($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['room_id']) || !isset($arrInput['time_len'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);
	$room_id = intval($arrInput['room_id']);
	$time_len = intval($arrInput['time_len']);
    
    if($user_id<=0 || $time_len<=0)
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }
    if($room_id<=0 && $room_id!==self::ALL_ROOM_ID)
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = array();

	//your code here......
	$error = Tieba_Errcode::ERR_SUCCESS;
    $ret = self::_ban_user_room_perm($user_id,$room_id,self::CAN_CHAT,$time_len); 
    if($ret === false)
    {
        Bingo_Log::warning("call _ban_user_room_perm err! user_id:$user_id, room_id:$room_id");
        $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
    }
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32_t room_id
 * @return: $arrOutput
 * 	setout output
**/
public static function cancelBanLiveChat($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['room_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);
	$room_id = intval($arrInput['room_id']);
    if($user_id<=0 )
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }
    if($room_id<=0 && $room_id!==self::ALL_ROOM_ID)
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = array();

	//your code here......
	$error = Tieba_Errcode::ERR_SUCCESS;
    $ret = self::_cancel_ban_user_room_perm($user_id,$room_id,self::CAN_CHAT); 
    if($ret === false)
    {
        Bingo_Log::warning("call _cancel_ban_user_room_perm err! user_id:$user_id, room_id:$room_id");
        $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
    }
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}

/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32_t room_id
 * 	uint32_t time_len
 * @return: $arrOutput
 * 	setout output
**/
public static function banLiveHorn($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['room_id']) || !isset($arrInput['time_len'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);
	$room_id = intval($arrInput['room_id']);
	$time_len = intval($arrInput['time_len']);
    
    if($user_id<=0 || $time_len<=0)
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }
    if($room_id<=0 && $room_id!==self::ALL_ROOM_ID)
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = array();

	//your code here......
	$error = Tieba_Errcode::ERR_SUCCESS;
    $ret = self::_ban_user_room_perm($user_id,$room_id,self::CAN_HORN,$time_len); 
    if($ret === false)
    {
        Bingo_Log::warning("call _ban_user_room_perm err! user_id:$user_id, room_id:$room_id");
        $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
    }
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32_t room_id
 * @return: $arrOutput
 * 	setout output
**/
public static function cancelBanLiveHorn($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['room_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);
	$room_id = intval($arrInput['room_id']);
    if($user_id<=0 )
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }
    if($room_id<=0 && $room_id!==self::ALL_ROOM_ID)
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = array();

	//your code here......
	$error = Tieba_Errcode::ERR_SUCCESS;
    $ret = self::_cancel_ban_user_room_perm($user_id,$room_id,self::CAN_HORN); 
    if($ret === false)
    {
        Bingo_Log::warning("call _cancel_ban_user_room_perm err! user_id:$user_id, room_id:$room_id");
        $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
    }
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}

/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32_t room_id
 * 	uint32_t time_len
 * @return: $arrOutput
 * 	setout output
**/
public static function banLiveAll($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['room_id']) || !isset($arrInput['time_len'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);
	$room_id = intval($arrInput['room_id']);
	$time_len = intval($arrInput['time_len']);
    
    if($user_id<=0 || $time_len<=0)
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }
    if($room_id<=0 && $room_id!==self::ALL_ROOM_ID)
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = array();

	//your code here......
	$error = Tieba_Errcode::ERR_SUCCESS;
    $ret = self::_ban_user_room_perm($user_id,$room_id,self::ALL_PERM_ID,$time_len); 
    if($ret === false)
    {
        Bingo_Log::warning("call _ban_user_room_perm err! user_id:$user_id, room_id:$room_id");
        $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
    }
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t user_id
 * 	uint32_t room_id
 * @return: $arrOutput
 * 	setout output
**/
public static function cancelBanLiveAll($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['user_id']) || !isset($arrInput['room_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$user_id = intval($arrInput['user_id']);
	$room_id = intval($arrInput['room_id']);
    if($user_id<=0 )
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }
    if($room_id<=0 && $room_id!==self::ALL_ROOM_ID)
    {
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
    }

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$output = array();

	//your code here......
	$error = Tieba_Errcode::ERR_SUCCESS;
    $ret = self::_cancel_ban_user_room_perm($user_id,$room_id,self::ALL_PERM_ID); 
    if($ret === false)
    {
        Bingo_Log::warning("call _cancel_ban_user_room_perm err! user_id:$user_id, room_id:$room_id");
        $error = Tieba_Errcode::ERR_DL_CALL_FAIL;
    }
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'output' => $output,
	);
	return $arrOutput;
}
// private function
const USER_ROLE_KEY_PRFX = 'permrole';
const USER_BAN_KEY_PRFX = 'permban';

private static function _get_cache_key($prefix, $key) {
    return $prefix.'_'.strval($key);
}

private static function _set_kv_redis($key,$value,$expire=0) {

    if( null === ($redis = self::_getRedis()) ){
        Bingo_Log::warning("get redis fail.");
        return false;
    }
    $arrParams = array(
        'key' => $key,
        'value' => serialize($value),
    );    
    Bingo_Log::pushNotice("_addRedis_is_setnx_key:$key",$setnx);
    $arrRet = $redis->SET($arrParams);
    //Bingo_Log::debug("set redis data.[key=$key][ret=".serialize($arrRet)."]");^M
    if($arrRet === false || $arrRet['err_no'] !== 0){
        Bingo_Log::pushNotice("_addRedis_key:$key",0);
        Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
        return false;
    }else if($expire > 0){
        Bingo_Log::pushNotice("_addRedis_key:$key",1);
        $arrParams = array(
            'key' => $key,
            'seconds' => intval($expire),
        );
        $arrRet = $redis->EXPIRE($arrParams);
    }
    return true;
}


private static function _get_kv_redis($key) {
    if( null === ($redis = self::_getRedis()) ){
        Bingo_Log::warning("get redis fail.");
        return false;
    }
    $arrParams = array(
        'key' => $key,
    );
    $arrRet = $redis->GET($arrParams);
    if($arrRet === false || $arrRet['err_no'] !== 0){
        Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
        return false;
    }
    $data = false;
    if(!is_null($arrRet['ret'][$key])) {
        Bingo_Log::pushNotice("_hitRedis_key:$key",1);
        $data = unserialize($arrRet['ret'][$key]);
    } else {
        Bingo_Log::pushNotice("_hitRedis_key:$key",0);
        $data = false;
    }
    return $data;
}

private static function _delete_kv_redis($key) {
    if( null === ($redis = self::_getRedis()) ){
        Bingo_Log::warning("get redis fail.");
        return false;
    }
    $arrParams = array(
        'key' => $key,
    );
    $arrRet = $redis->DEL($arrParams);
    if($arrRet === false || $arrRet['err_no'] !== 0){
        Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
        return false;
    }
    return true;
}

private static function _get_user_role($user_id) {

    $userRole = array();

    $user_id = intval($user_id); 
    $hitCache = FALSE;
    $cacheKey = self::_get_cache_key(self::USER_ROLE_KEY_PRFX, $user_id);
    $getRes = self::_get_kv_redis($cacheKey);
    
    if (is_array($getRes)) { 
        Bingo_Log::debug("hit cache. key[{$cacheKey}]");
        $userRole = $getRes;
        $hitCache = TRUE; 
    }       

    if (!$hitCache) {
        $has_db_err = false;
        Bingo_Log::debug("miss cache, query db. key[{$cacheKey}]");
        $objDB = self::_getDB();
        if (!$objDB) {
            Bingo_Log::warning('init db fail! ');
            return false;
        }       

        $table = 'user_room_role';
        $fields = array(
            'user_id',
            'room_id',
            'role_id',
        ); 
        $conds = array("user_id=$user_id");
        $dbRes = $objDB->select($table, $fields, $conds);
        if (!is_array($dbRes)) {
            $has_db_err = true;
            Bingo_Log::warning('query sql fail! sql['.$objDB->getLastSQL().']');
        }       
        else{
            foreach ($dbRes as $role) {
                $userRole[$role['room_id']][$role['role_id']] = true;
            }
        }

        $table = 'sop_user';
        $fields = array(
            'user_id',
        );
        $conds = array("user_id=$user_id");
        $dbRes = $objDB->select($table, $fields, $conds);
        if (!is_array($dbRes)) {
            $has_db_err = true;
            Bingo_Log::warning('query sql fail! sql['.$objDB->getLastSQL().']');
        }
        else{
            foreach ($dbRes as $role) {
                if ( !empty($role['user_id']))
                    {
                    $userRole['u_is_sop']=true;
                    break;
                }
            }
        }
        if(!$has_db_err)
        {
            $addRes = self::_set_kv_redis($cacheKey, $userRole,36000);
            if ($addRes==false)
            {
                Bingo_Log::warning("add cache fail! key[{$cacheKey}]");
            }
        }
    }   

    return $userRole;     
}

private static function _get_user_ban_perm($user_id) {

    $user_ban_perm = array();

    $user_id = intval($user_id); 
    $hitCache = FALSE;
    $cacheKey = self::_get_cache_key(self::USER_BAN_KEY_PRFX, $user_id);
    $getRes = self::_get_kv_redis($cacheKey);
    
    if (is_array($getRes)) { 
        Bingo_Log::debug("hit cache. key[{$cacheKey}]");
        $hitCache = TRUE;
        $time_now = time();

        foreach($getRes as $room_id => $perms)
        {
            if(is_array($perms))
            {
                foreach($perms as $perm_id => $exptime)
                {
                    if( $exptime>$time_now ) 
                    {
                        $user_ban_perm[$room_id][$perm_id] = $exptime;
                    }
                }
            }    
        } 
    }       

    if (!$hitCache) {
        $has_db_err = false;
        Bingo_Log::debug("miss cache, query db. key[{$cacheKey}]");
        $objDB = self::_getDB();
        if (!$objDB) {
            Bingo_Log::warning('init db fail! ');
            return $user_ban_perm;
        }       

        $table = 'user_room_perm_ban';
        $fields = array(
            'user_id',
            'room_id',
            'perm_id',
            'expire_time',
        ); 
        $conds = array("user_id=$user_id");
        $dbRes = $objDB->select($table, $fields, $conds);
        if (!is_array($dbRes)) {
            $has_db_err = true;
            Bingo_Log::warning('query sql fail! sql['.$objDB->getLastSQL().']');
        }       
        else{
            $need_delete = false;
            $time_now = time();
            foreach ($dbRes as $ban) {
                if($ban['expire_time']>$time_now)
                {
                    $user_ban_perm[$ban['room_id']][$ban['perm_id']] = $ban['expire_time'];
                }
                else
                {
                    $need_delete = true;
                }
            }
            if($need_delete)
            {
                $table = 'user_room_perm_ban';
                $conds = array("user_id=$user_id and expire_time<=$time_now");
                $dbRes = $objDB->delete($table, $conds);
                if($dbRes===false)
                {
                    Bingo_Log::warning('query sql fail! sql['.$objDB->getLastSQL().']');
                }
            }
        }

        if(!$has_db_err)
        {
            $addRes = self::_set_kv_redis($cacheKey, $user_ban_perm,36000);
            if ($addRes==false)
            {
                Bingo_Log::warning("add cache fail! key[{$cacheKey}]");
            }
        }
    }   
    return $user_ban_perm;     
}
private static function _set_user_room_role($user_id,$room_id,$role_id) {
    if( $user_id<=0 || $room_id<=0 ||$role_id <=0 || !isset(self::$_role_map[$role_id]))
    {
        Bingo_Log::warning("param error! input[$user_id,$room_id,$role_id]");
        return false;
    }
    $user_id = intval($user_id);
    $room_id = intval($room_id);
    $role_id = intval($role_id);
    
    $objDB = self::_getDB();
    if (!$objDB) {
        Bingo_Log::warning('init db fail! ');
        return false;
    }       

    $time_now = time();
    $table = 'user_room_role';
    $arrField = array(
        'user_id' => $user_id,
        'room_id' => $room_id,
        'role_id' => $role_id,
        'update_time' => $time_now,
        'expire_time' => self::MAX_INT, //最大整数
    ); 
    $dbRes = $objDB->insert($table, $arrField);
    if($dbRes <=0 )
    {
        Bingo_Log::warning('query sql fail! sql['.$objDB->getLastSQL().']'); 
        return false; 
    }
    $cacheKey = self::_get_cache_key(self::USER_ROLE_KEY_PRFX, $user_id);
    $delRes = self::_delete_kv_redis($cacheKey);
    return true;
}
private static function _delete_user_room_role($user_id,$room_id,$role_id) {
    if( $user_id<=0 || $room_id<=0 ||$role_id <=0 || !isset(self::$_role_map[$role_id]))
    {
        Bingo_Log::warning("param error! input[$user_id,$room_id,$role_id]");
        return false;
    }
    $user_id = intval($user_id);
    $room_id = intval($room_id);
    $role_id = intval($role_id);
    
    $objDB = self::_getDB();
    if (!$objDB) {
        Bingo_Log::warning('init db fail! ');
        return false;
    }       

    $table = 'user_room_role';
    $conds = array("user_id=$user_id and room_id=$room_id and role_id=$role_id");
    $dbRes = $objDB->delete($table,$conds);
    if($dbRes === false )
    {
        Bingo_Log::warning('query sql fail! sql['.$objDB->getLastSQL().']'); 
        return false; 
    }
    $cacheKey = self::_get_cache_key(self::USER_ROLE_KEY_PRFX, $user_id);
    $delRes = self::_delete_kv_redis($cacheKey);
    return true;
}

private static function _ban_user_room_perm($user_id,$room_id,$perm_id,$time_len) {
    if( $user_id<=0 || $room_id<=0 ||$perm_id <=0 || $time_len <=0 ||!isset(self::$_perm_map[$perm_id]))
    {
        Bingo_Log::warning("param error! input[$user_id,$room_id,$perm_id,$time_len]");
        return false;
    }
    $user_id = intval($user_id);
    $room_id = intval($room_id);
    $perm_id = intval($perm_id);
    $time_len = intval($time_len);

    $objDB = self::_getDB();
    if (!$objDB) {
        Bingo_Log::warning('init db fail! ');
        return false;
    }       
    
    $time_now = time();
    $expire_time = $time_now+$time_len;
    $table = 'user_room_perm_ban';
    $fields = array(
        'user_id',
        'room_id',
        'perm_id',
        'expire_time',
    ); 
    $conds = array("user_id=$user_id and room_id=$room_id and perm_id=$perm_id");
    $dbRes = $objDB->select($table, $fields, $conds);
    if (isset($dbRes[0]['user_id'])) {
        $strFields = "update_time=$time_now,expire_time=$expire_time";
        $strCond = "user_id=$user_id and room_id=$room_id and perm_id=$perm_id";
        $tmp  = $db->update($table, $strFields, $strCond);
        if($tmp === false){
            Bingo_Log::warning(" db update error!  [error:" .$db->error()." sql: " .$db->getLastSQL(). "]" );
            return false;
        }    
        $cacheKey = self::_get_cache_key(self::USER_BAN_KEY_PRFX, $user_id);
        $delRes = self::_delete_kv_redis($cacheKey);
        return true;
    }       
    
    $arrField = array(
        'user_id' => $user_id,
        'room_id' => $room_id,
        'perm_id' => $perm_id,
        'update_time' => $time_now,
        'expire_time' => $expire_time,
    ); 
    
    $dbRes = $objDB->insert($table, $arrField);
    if($dbRes <=0 )
    {
        Bingo_Log::warning('query sql fail! sql['.$objDB->getLastSQL().']'); 
        return false; 
    }
    $cacheKey = self::_get_cache_key(self::USER_BAN_KEY_PRFX, $user_id);
    $delRes = self::_delete_kv_redis($cacheKey);
    return true;
}

private static function _cancel_ban_user_room_perm($user_id,$room_id,$perm_id) {
    if( $user_id<=0 || $room_id<=0 ||$perm_id <=0 ||!isset(self::$_perm_map[$perm_id]))
    {
        Bingo_Log::warning("param error! input[$user_id,$room_id,$perm_id]");
        return false;
    }
    $user_id = intval($user_id);
    $room_id = intval($room_id);
    $perm_id = intval($perm_id);

    $objDB = self::_getDB();
    if (!$objDB) {
        Bingo_Log::warning('init db fail! ');
        return false;
    }       

    $table = 'user_room_perm_ban';
    $conds = array("user_id=$user_id and room_id=$room_id and perm_id=$perm_id");
    $dbRes = $objDB->delete($table,$conds);
    if($dbRes===false )
    {
        Bingo_Log::warning('query sql fail! sql['.$objDB->getLastSQL().']'); 
        return false; 
    }
    $cacheKey = self::_get_cache_key(self::USER_BAN_KEY_PRFX, $user_id);
    $delRes = self::_delete_kv_redis($cacheKey);
    return true;
}
private static function _set_sop($user_id,$time_len=94608000) { //默认三年有效期
    if( $user_id<=0 || $time_len <=0 )
    {
        Bingo_Log::warning("param error! input[$user_id,$time_len]");
        return false;
    }
    $user_id = intval($user_id);
    $time_len = intval($time_len);

    $objDB = self::_getDB();
    if (!$objDB) {
        Bingo_Log::warning('init db fail! ');
        return false;
    }       

    $time_now = time();
    $expire_time = $time_now+$time_len;
    $table = 'sop_user';
    $arrField = array(
        'user_id' => $user_id,
        'update_time' => $time_now,
        'expire_time' => $expire_time,
    ); 
    $dbRes = $objDB->insert($table, $arrField);
    if($dbRes <=0 )
    {
        Bingo_Log::warning('query sql fail! sql['.$objDB->getLastSQL().']'); 
        return false; 
    }
    $cacheKey = self::_get_cache_key(self::USER_ROLE_KEY_PRFX, $user_id);
    $delRes = self::_delete_kv_redis($cacheKey);
    return true;
}

private static function _delete_sop($user_id){
    if( $user_id<=0 )
    {
        Bingo_Log::warning("param error! input[$user_id]");
        return false;
    }
    $user_id = intval($user_id);

    $objDB = self::_getDB();
    if (!$objDB) {
        Bingo_Log::warning('init db fail! ');
        return false;
    }       
    $table = 'sop_user';
    $conds = array("user_id=$user_id");
    $dbRes = $objDB->delete($table,$conds);

    if($dbRes === false )
    {
        Bingo_Log::warning('query sql fail! sql['.$objDB->getLastSQL().']'); 
        return false; 
    }
    $cacheKey = self::_get_cache_key(self::USER_ROLE_KEY_PRFX, $user_id);
    $delRes = self::_delete_kv_redis($cacheKey);
    return true;
}
}
