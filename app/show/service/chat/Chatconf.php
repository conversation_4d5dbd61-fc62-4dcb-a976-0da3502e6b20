<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013:10:29 23:35:24
 * @version 
 * @structs & methods(copied from idl.)
*/

class Service_Chat_Chatconf extends Service_Lib_Base{
    
    public static $chat_level_limit = array(
		0 => array(
			'word_limit' => 10,
			'face_limit' => 10,
    	),
    	1 => array(
    		'word_limit' => 20,
    		'face_limit' => 10,
    	),
    	2 => array(
    		'word_limit' => 20,
    		'face_limit' => 10,
    	),
    		
    );
	
	/*
	 * 统计内容的字符数，表情算2个字符
	 */
    public static function countWords($str,array $face_input = array()){
        //$a= 'bacasdfdsb[x[111]xx]yyyyy';
        //$a= 'bacasdfdsb[x[111]xx]yyyyy[aaa][bbb]mmm';
        //$a= '呵呵[乐乐][mmm]啊啊啊';
        $str = htmlspecialchars_decode($str,ENT_QUOTES);
        $len = strlen($str);
        $begin = '';
        $end = '';
        $newstr = '';
        $count = 0;
        for ( $i = 0 ; $i < $len ; $i ++){
            //echo $str{$i}."==\n";
            $char = $str{$i};
            if ($char === '['){
                if ($begin === ''){
                    $begin = $char;
                }else{
                    $newstr .= $begin;
                    $begin = $char;
                }
            } else if ($char === ']'){
                if ($begin !== ''){
                    
                    if (!in_array($begin.']', $face_input)){
                        Bingo_Log::warning("ui---$begin\n" );
                        $newstr .= $begin;
                        $newstr .= ']';
                        $begin = '';
                    }else{
                        
                    
                        if (strlen($begin) > 15){
                            //输出个比较大的让被调用函数返回字符限制
                            return array('word_count'=> 10000,'face_count'=> $count);
                        }
                        $count ++;
                        $begin = '';
                    }
                }else {
                    $newstr .= $char;
                }
            } else {
                if ($begin === ''){
    
                    $newstr .= $char;
                }else{
                    $begin .= $char;
                }
            }
    
            //echo "begin=$begin ; newstr = $newstr\n";
    
        }
        //echo $newstr."\n".$count."\n";
        //echo iconv_strlen($newstr,'utf8')."\n";
        //echo iconv_strlen('哈哈aaa','utf8')."\n";
        //Bingo_Log::warning(print_r(array('word_count'=> mb_strlen($newstr,'utf8'),'face_count'=> $count),1) );
        return array('word_count'=> mb_strlen($newstr,'utf8'),'face_count'=> $count);
    }
	
}
	
