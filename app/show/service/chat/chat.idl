
struct RetOut {
}

service show   
{
	/**
	 * @brief 送礼物
	 * @param [in] room_id : uint32_t //房间ID
	 * @param [in] user_id : uint32_t  
	 * @param [in] forum_id : uint32_t  
	 * @param [in] props_id : uint32_t  //道具信息
	 * @param [in] content : string 
	 * @param [out] output :  RetOut // 榜单信息 
	**/
	void sendLoudSpeaker(uint32_t room_id, uint32_t user_id,uint32_t forum_id,uint32_t props_id, out RetOut output);

	/**
	 * @brief 获取礼物道具
	 * @param [in] user_id : uint32_t 
	 * @param [in] room_id : uint32_t 
	 * @param [in] content : string 
	 * @param [out] output :  PropsList // 榜单信息 
	**/
	void sendChatMsg(uint32_t type, out RetOut output);
	
};

