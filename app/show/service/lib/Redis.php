<?php
/**
 *  **/
class Service_Lib_Redis{

    protected static $_redis = array();


    /**
     * @brief get redis obj.
     * @return: obj of Bingo_Cache_Redis, or null if connect fail.

     **/

    const REDIS_NAME = "show";

    public static function _getRedis($name = null){
        if(is_null($name)){
            $name = self::REDIS_NAME;
        }
        if(isset(self::$_redis[$name])){
            return self::$_redis[$name];
        }
        Bingo_Timer::start('redis_init');
        $redis = new Bingo_Cache_Redis($name);
        Bingo_Timer::end('redis_init');

        if(!$redis || !$redis->isEnable()){
            Bingo_Log::warning("init redis fail.");
            return null;
        }
        self::$_redis[$name] = $redis;
        return self::$_redis[$name];
    }

    public static function mDEL($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->DEL(array('reqs' => $arrInput));
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function ZSCORE($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZSCORE($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'][$arrInput['key']]; //直接取出数值
        return $data;
    }

    public static function ZADD($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZADD($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function mZADD($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZADD(array('reqs' => $arrInput));
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function mZINCRBY($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZINCRBY( array('reqs' => $arrInput) );
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function mZREMRANGEBYRANK($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZREMRANGEBYRANK( array('reqs' => $arrInput) );
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function mZRANGEWITHSCORES($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZRANGEWITHSCORES( array('reqs' => $arrInput) );
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function ZREVRANGEWITHSCORES($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZREVRANGEWITHSCORES($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function mZREVRANGEWITHSCORES($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZREVRANGEWITHSCORES( array('reqs' => $arrInput) );
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function mHSET($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HSET( array('reqs' => $arrInput));
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function mHDEL($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HDEL( array('reqs' => $arrInput));
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function mHMGET($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HMGET( array('reqs' => $arrInput));
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function HGET($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HGET($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $key = $arrInput['key'];
        $data = $arrRet['ret'][$key];
        return $data;
    }

    public static function HSET($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HSET($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $key = $arrInput['key'];
        $data = $arrRet['ret'][$key];
        return $data;
    }

    public static function HINCRBY($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HINCRBY($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $key = $arrInput['key'];
        $data = $arrRet['ret'][$key];
        return $data;
    }

    public static function HGETALL($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->HGETALL($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $key = $arrInput['key'];
        $data = $arrRet['ret'][$key];
        return $data;
    }

    public static function ZREM($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZREM($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }

    public static function ZRANGE($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZRANGE($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $key = $arrInput['key'];
        $data = $arrRet['ret'][$key];
        return $data;
    }

    public static function ZREVRANGE($arrInput){
        if(null == ($redis = self::_getRedis())){
            Bingo_Log::warning('get redis err!');
            return false;
        }
        $arrRet = $redis->ZREVRANGE($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        $key = $arrInput['key'];
        $data = $arrRet['ret'][$key];
        return $data;
    }

    public static function getHashFromRedis($arrInput){
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            $arrOutput = array(
                'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
                'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR),
                'data' => null,
            );
            return $arrOutput;
        }
        $key = $arrInput['key'];

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        $arrParams = array(
            'key' => $key,
        );
        $arrRet = $redis->HGETALL($arrParams);
        //Bingo_Log::debug("get redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
        }
        $data = null;
        if(!is_null($arrRet['ret'][$key])){
            Bingo_Log::pushNotice("_hitRedis_hashkey:$key",1);
            $data = $arrRet['ret'][$key];
        }else{
            Bingo_Log::pushNotice("_hitRedis_hashkey:$key",0);
        }
        return $data;
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;
    }

    //��redis�ж�ȡcache
    public static function getFromRedis($arrInput){
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            $arrOutput = array(
                'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
                'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR),
                'data' => null,
            );
            return $arrOutput;
        }
        $key = $arrInput['key'];

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        $arrParams = array(
            'key' => $key,
        );
        $arrRet = $redis->GET($arrParams);
        //Bingo_Log::debug("get redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
        }
        $data = null;
        if(!is_null($arrRet['ret'][$key])){
            Bingo_Log::pushNotice("_hitRedis_key:$key",1);
            $data = unserialize($arrRet['ret'][$key]);
        }else{
            Bingo_Log::pushNotice("_hitRedis_key:$key",0);
        }
        return $data;
    }
    //��redis�����cache
    public static function setToRedis($arrInput){
        if(!isset($arrInput['key']) || !isset($arrInput['value'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $key = $arrInput['key'];
        $value = $arrInput['value'];
        $setnx = isset($arrInput['setnx'])?intval($arrInput['setnx']):0;
        $expire = isset($arrInput['expire'])?intval($arrInput['expire']):0;

        $redis = self::_getRedis();
        $arrParams = array(
            'key' => $key,
            'value' => serialize($value),
        );


        if($setnx){
            Bingo_Log::pushNotice("_addRedis_is_setnx_key:$key",1);
            $arrRet = $redis->SETNX($arrParams);
        }else{
            $arrRet = $redis->SET($arrParams);
        }
        //Bingo_Log::debug("set redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::pushNotice("_addRedis_key:$key",0);
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
			return false;
        }else if($expire > 0){
            Bingo_Log::pushNotice("_addRedis_key:$key",1);
            $arrParams = array(
                'key' => $key,
                'seconds' => intval($expire),
            );
            $arrRet = $redis->EXPIRE($arrParams);
            //Bingo_Log::debug("set redis key expire.[key=$key][ret=".serialize($arrRet)."]");
        }
		return true;
    }

    public static function clearFromRedis($arrInput){
        if(!isset($arrInput['key'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $key = $arrInput['key'];
        $data = 0;

        $redisName = isset($arrInput['redis'])?$arrInput['redis']:null;
        $redis = self::_getRedis($redisName);
        $arrParams = array(
            'key' => strval($key),
        );
        $arrRet = $redis->DEL($arrParams);
        //Bingo_Log::debug("clear redis data.[key=$key][ret=".serialize($arrRet)."]");
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::pushNotice("clearRedis_key:$key",0);
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
        }else{
            $data = 1;
            Bingo_Log::pushNotice("clearRedis_key:$key",1);
        }
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $data,
        );
        return $arrOutput;

    }

    /**
     * [ZCARD description]
     * @param [array] $arrInput [description]
     * @return [int] [description]
     */
    public static function ZCARD($arrInput){
        if(null == ($redis = self::_getRedis())){
            return false;
        }
        $arrRet = $redis->ZCARD($arrInput);
        if($arrRet===false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
            return false;
        }
        return $arrRet['ret'][$arrInput['key']];
    }

    /**
     * [set description]
     * @param [array] $arrInput [description]
     * @return [bool/string] [description]
     */
    public static function set($arrInput) {
        if (empty($arrInput) || !isset($arrInput['key']) || !isset($arrInput['value'])) {
            return false;
        }

        $key = $arrInput['key'];

        $input = array(
            'key' => $key,
            'value' => serialize($arrInput['value']),
        );

        $redis = self::_getRedis();

        if (!empty($arrInput['seconds'])) {
            $input['seconds'] = $arrInput['seconds'];
            $arrRet = $redis->SETEX($input);
        } else {
            $arrRet = $redis->SET($input);
        }

        if($arrRet === false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error. [".serialize($arrRet)."]");
            return false;
        }

        return $arrRet['ret'][$key];
    }

    /**
     * [get description]
     * @param  [array] $arrInput [description]
     * @return [bool/string]           [description]
     */
    public static function get($arrInput) {
        if (empty($arrInput) || !isset($arrInput['key'])) {
            return false;
        }

        $key = $arrInput['key'];

        $input = array(
            'key' => $key,
        );

        $redis = self::_getRedis();
        $arrRet = $redis->GET($input);

        if($arrRet === false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error. [".serialize($arrRet)."]");
            return false;
        }

        $data = $arrRet['ret'][$key];

        if (!empty($data)) {
            $data = unserialize($data);
        }

        return $data;
    }

    /**
     * [del description]
     * @param  [array] $arrInput [description]
     * @return [bool]           [description]
     */
    public static function del($arrInput) {
        if (empty($arrInput) || !isset($arrInput['key'])) {
            return false;
        }

        $input = array(
            'key' => $arrInput['key'],
        );

        $redis = self::_getRedis();
        $arrRet = $redis->DEL($input);

        if($arrRet === false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error. [".serialize($arrRet)."]");
            return false;
        }

        return true;
    }

    /**
     * [mGet description]
     * @param  [array] $arrInput [description]
     * @return [bool/string]           [description]
     */
    public static function mGet($arrInput) {
        if (empty($arrInput) || empty($arrInput['reqs'])) {
            return false;
        }

        $redis = self::_getRedis();
        $arrRet = $redis->GET($arrInput);

        if($arrRet === false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error. [".serialize($arrRet)."]");
            return false;
        }

        foreach ($arrRet['ret'] as $k => &$v) {
            $v = unserialize($v);
        }

        return $arrRet['ret'];
    }

    /**
     * [mSet description]
     * @param  [array] $arrInput [description]
     * @return [bool/int]           [description]
     */
    public static function mSet($arrInput) {
        if (empty($arrInput) || empty($arrInput['reqs'])) {
            return false;
        }

        foreach ($arrInput['reqs'] as &$req) {
            $req['value'] = serialize($req['value']);
        }

        $input = array(
            'reqs' => $arrInput['reqs'],
        );

        $redis = self::_getRedis();
        $arrRet = $redis->SET($input);

        if($arrRet === false || $arrRet['err_no'] !== 0){
            Bingo_Log::warning("call redis error. [".serialize($arrRet)."]");
            return false;
        }

        return $arrRet['ret'];
    }
}
/* vim: set noexpandtab ts=4 sw=4 sts=4 tw=100: */
