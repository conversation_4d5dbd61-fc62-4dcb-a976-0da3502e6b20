<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file 
 * <AUTHOR>
 * @date 2013/08/12 14:32:25
 * @brief 
 *  
 **/

class Service_Lib_Base {
	protected static $_conf = null;
	/**
	 * @brief init
	 * @return: true if success. false if fail.
	
	 **/
	protected static function _init(){
	
		//add init code here. init will be called at every public function beginning.
		//not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
		//init should be recalled for many times.
	
		if(self::$_conf == null){
			self::$_conf = Bd_Conf::getConf("/app/tbmall/service_tbmall");
			if(self::$_conf == false){
				Bingo_Log::warning("init get conf fail.");
				return false;
			}
	
		}
		return true;
	}
	
	
	protected static function _errRet($errno,$data = "",$data_key = "data"){
		$errmsg = Tieba_Error::getErrmsg($errno);
		$arrRet = array(
				'errno' => $errno,
				'errmsg' => $errmsg,
		);
		if($data !== ""){
			$arrRet[$data_key] = $data;
		}
		Bingo_Log::pushNotice("errno",$errno);
		return $arrRet;
	}
	
	protected static function noticeStLog($urlKey,$uid=0){
		 
		$arrBasicDefalutStlogFields   = array(
				'bduid' => '',
				'uid'   => $uid,
				'un' => '',
				'uip'    => 0,
				'no_un' => 0,
				'mobilephone' => 0,
				'email'  => '',
				'url' => '',
				'refer' => '',
				'agent' => '',
				'logid' => Bingo_Log::getLogId(),
				'is_new_user' => 0,
				'optime' => time(),
				'pro' => "tieba",
				'mid' => "tbmall",
				'cookieuid' => '',
				'urlkey' => $urlKey,
				'errno' => 0,
				'ispv' => 0,
		);
	
	
		$arrNodes = Bingo_Log::getNoticeNodes();
		$arrNodes = array_merge($arrBasicDefalutStlogFields,$arrNodes);
	
		Bingo_Log::pushNotice('urlkey',$urlKey);
		foreach ($arrNodes as $key=>$val){
			if(strncmp($key,"_",1) === 0){
				continue;
			}
			Tieba_Stlog::addNode($key,$val);
		}
	
		Tieba_Stlog::setFileName('feye-stat');
		Tieba_Stlog::notice();
	}
	protected function _array2Index(array $array,$index){
		$ret = array();
		foreach($array as $item){
			if(isset($item[$index])){
				$ret[$item[$index]] = $item;
			}
		}
		return $ret;
	}
	
	protected function _noticeStlog($strUrlkey = null){
		//获取路由名，并打到ui日志里
		if(is_null($strUrlkey)){
			$strUrlkey = Bingo_Http_Request::getStrHttpRouter();
		}
		$arrUserInfo = $this->_getUserInfo();
		Bingo_Log::pushNotice("urlkey",$strUrlkey);
		$arrBasicDefalutStlogFields   = array(
				'bduid' => Tieba_Session_Socket::getBaiduid(),
				'uid'   => $arrUserInfo['user_id'],
				'un' => $arrUserInfo['user_name'],
				'uip'    => $arrUserInfo['user_ip'],
				'no_un' => intval($arrUserInfo['is_noname']),
				'mobilephone' => $arrUserInfo['mobile'],
				'email'  => $arrUserInfo['email'],
				'url' => Bingo_Http_Request::getServer("REQUEST_URI",""),
				'refer' => Bingo_Http_Request::getServer("HTTP_REFERER",""),
				'agent' => Bingo_Http_Request::getServer("HTTP_USER_AGENT",""),
				'logid' => Bingo_Log::getLogId(),
				'is_new_user' => intval(Tieba_Cookie_UserType::isNew()),
				'optime' => time(),
				'pro' => "tieba",
				'mid' => $this->_strStlogMid,
				'cookieuid' => Tieba_Session_Socket::getCookieUid(),
				'urlkey' => $strUrlkey,
				'errno' => 0,
				'ispv' => 0,
		);
	
		$arrNodes = Bingo_Log::getNoticeNodes();
		$arrStlogFields = array_merge($arrBasicDefalutStlogFields,$arrNodes);
	
		foreach ($arrStlogFields as $key=>$val){
			if(strncmp($key,"_",1) === 0){
				continue;
			}
			Tieba_Stlog::addNode($key,$val);
		}
	
		Tieba_Stlog::setFileName($this->_strStlogFile);
		Tieba_Stlog::notice();
	}

	
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>