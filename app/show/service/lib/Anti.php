<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file libs/UserPerm.php
 * <AUTHOR>
 * @date 2013/08/12 14:32:25
 * @brief 
 *  
 **/

class Service_Lib_Anti {
	
    public static function filterWords($content){
        //关键词过滤
        //UEG敏感词过滤接口
        $arrParams = array(
            'req' => array(
                'confilter_type' => 'Confilter',
                'reqs' => array(
                    array(
                        'groupid' => -1,
                        'content' => $content,
                        'return_position' => 'no',
                        'no_normalize' => 'yes',
                        'dictlist' => 'confilter_zhibojian_antispam',
                        ),
                    array(
                        'groupid' => -1,
                        'content' => $content,
                        'return_position' => 'no',
                        'no_normalize' => 'yes',
                        'dictlist' => 'confilter_page_search_blackwords',
                        ),
                    ),
                ),
            );
        $arrOutput = Tieba_Service::call('anti', 'antiConfilter', $arrParams,  NULL, NULL, 'post', 'php');
        if(Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']){
            Bingo_Log::warning('anti confilter errno.Input:['.serialize($arrParams).'].output:['.serialize($arrOutput).']');
            return true;
        }
        $count = 0;
        foreach($arrOutput['res']['ans'] as $k => $v){
            $count += $v->count;
        }
        if($count > 0){
            Bingo_Log::warning ( "the content not pass ueg confilter! [" . serialize ( $arrParams ) . "]" );
            //            return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
            return true; //命中过滤词
        }
        return false;
    }

    /*
     * 粒度控制接口
     * @param user_id
     * @param room_id 房间ID
     */
    public static function loginCtrlSubmit($user_id,$room_id){
        //粒度控制1 单个用户3秒发一条
        $arrParams = array(
            'req'=>array(
                'rulegroup' =>array('app'),
                'app'=>'livegroup',
                'cmd'=>'enterwelcome',
                'uid'=>intval($user_id),
                'group_id'=>intval($room_id),
            ),
        );
        $arrRet = Tieba_Service::call("anti","antiActsctrlSubmit",$arrParams, NULL, NULL, 'post', 'php', 'utf-8');

        if($arrRet == false){//宽策略
            return 0;
        }

        if($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning ( "loginCtrlSubmit ERR_OPERATE_TOO_FREQUENT! input:[" . serialize ( $arrParams ) . "] output[".serialize ( $arrRet )."]" );
            Bingo_Log::pushNotice("ActsctrlSubmit",intval($arrRet['res']['err_no']));
            return 1;

        }

        return 0;
    }
    /*
     *
     * 粒度控制接口
     * @param user_id
     * @param room_id 房间ID
     */
    public static function chatCtrlSubmit($user_id,$room_id){
        //粒度控制1 单个用户3秒发一条
        $arrParams = array(
                'req'=>array(
                        'rulegroup' =>array('app'),
                        'app'=>'video',
                        'cmd'=>'commitMsg',
                        'uid'=>intval($user_id),
                        'group_id'=>intval($room_id),
                ),
        );
        $arrRet = Tieba_Service::call("anti","antiActsctrlSubmit",$arrParams, NULL, NULL, 'post', 'php', 'utf-8');
        //Bingo_Log::warning(print_r($arrRet,1));
        if($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning ( "chatCtrlSubmit ERR_OPERATE_TOO_FREQUENT! input:[" . serialize ( $arrParams ) . "] output[".serialize ( $arrRet )."]" );
            Bingo_Log::pushNotice("ActsctrlSubmit",intval($arrRet['res']['err_no']));
            if ($arrRet['res']['reason'] === 65532){ //命中uid本room 3秒限制
                return 1;
            }else if ($arrRet['res']['reason'] === 65533){//命中所有uid每秒50次限制
                return 2;
            }else {
                return 3;
            }

        }
        
        return 0;
    }
    
    /*
     * 视频 聊天 和 喇叭的ugc接入ueg的接口。。
     */
    public static function antiContentCommit($arrInput){
        
        if(!isset($arrInput['user_id']) || !isset($arrInput['user_name']) 
                || !isset($arrInput['content']) || !isset($arrInput['user_id'])){
            Bingo_Log::warning ( "input params invalid less. [" . serialize ( $arrInput ) . "]" );
            return Service_Libs_Base::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        
        $user_id = intval($arrInput['user_id']);
        $user_name = strval($arrInput['user_name']);
        $content = strval($arrInput['content']);
        $chat_type = intval($arrInput['chat_type']);
        $user_ip = intval($arrInput['user_ip']);

        $arrParams = array(
                'anti_cmd'=>'recallApp',
                "req" => array(
                        "user_id" => $user_id, //发送请求的用户id
                        "user_name" => $arrInput['user_name'],//发送请求的用户的用户名
                        "forum_id" => "",//吧id，没有添0
                        "word" => "",//吧名，没有添“”
                        "content" => $arrInput['content'],//ugc内容
                        "app_key" => $arrInput['chat_type'] ,//10 视频聊天  11喇叭
                        "ip" => $arrInput['user_ip'],//用户本次操作ip
                        "thread_id" => 0,//主题id,没有添0
                        "post_id" => 0,//贴子id，没有添0
                        "user_id_ed" => 0,//接受好友请求用户id，为有多用户之间交互的业务预留，没有添0
                        "user_name_ed" => "",//接受好友请求用户的用户名，为有多用户之间交互的业务预留，没有添“”
        
                ),
        );
        $arrRet = Tieba_Service::call("anti","antiCommit", $arrParams, NULL, NULL, 'post', 'php', 'utf-8');
        
        return $arrRet;
    }
    
    
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
