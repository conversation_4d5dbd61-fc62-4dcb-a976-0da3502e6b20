<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file libs/UserPerm.php
 * <AUTHOR>
 * @date 2013/08/12 14:32:25
 * @brief 
 *  
 **/

class Service_Lib_Lcs {


	public static function sendMsgToLCSRoom($content, $type , $room_id = 0, $forum_id = 0 ,$priorUserList = array()) {
    	$req = Tieba_Service::call('show','getRoomType',array('room_id' => $room_id), NULL, NULL,'post', 'php', 'utf-8', 'local');
        $room_type = $req['data'];
        if(true  == $room_type['is_poll']){
           //添加lcs模式的 redis写入
           $arrGmessagePc = array();
           $arrGmessageMo = array();
           foreach($content['msg_list'] as $k => $v){
              if($v['msg_type'] == Service_Lib_Define::MSG_TYPE_LOUDSPERKER ||
                $v['msg_type'] == Service_Lib_Define::MSG_TYPE_GIFT||
                $v['msg_type'] == Service_Lib_Define::MSG_TYPE_SOFA ||
                $v['msg_type'] == Service_Lib_Define::MSG_TYPE_RELATION_LIST){
                    $arrGmessagePc['msg_list'][] = $v;
              } 
              if($v['msg_type'] == Service_Lib_Define::MSG_TYPE_GIFT){
                    $arrGmessageMo['msg_list'][] = $v;
                }
           }

            if(count($arrGmessagePc['msg_list'])){
               //添加PC
               $fields = array(
                   'msg_type' => "1".$room_id,
                   'update_time' => time(),
                   'end_time' => time()+600,
                   'msg_status' => 1,
                   'msg_body' => $arrGmessagePc,
                   );
               $arrInput = array(
                   'type' => "1".$room_id,
                   'field' => $fields,
                   'num' => 50,
                   );
               $ret = Tieba_Service::call('gmessage','sendGlobalMsg',$arrInput,null,null,'post','php','utf-8');
               if($ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
                   Bingo_Log::warning('gmessage sendGlobalMsg to pc failed~.input['.serialize($arrInput).'].output['.serialize($ret).'].');
               }
            }
            if(count($arrGmessageMo['msg_list'])){
               //添加智能版
               $fields = array(
                   'msg_type' => "2".$room_id,
                   'update_time' => time(),
                   'end_time' => time()+600,
                   'msg_status' => 1,
                   'msg_body' => $arrGmessageMo,
                   );
               $arrInput = array(
                   'mtype' => "2".$room_id,
                   'field' => $fields,
                   'num' => 25,
                   );
               $ret = Tieba_Service::call('gmessage','sendGlobalMsg',$arrInput,null,null,'post','php','utf-8');
               if($ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
                   Bingo_Log::warning('gmessage sendGlobalMsg to mo failed~.input['.serialize($arrInput).'].output['.serialize($ret).'].');
               }
            }
        }
        
		if ($type === 1){ //推送本房间
			$reqs = array(
                array(
                    'groupId' => $room_id,
                    'priorUserList' => $priorUserList,
                ),
			);
		}else if ($type ===2 ){ //推送全吧
			//获取吧对应的房价列表。
			$arr_room_ids = array();
			$i = 1;
			while($i < 10){//最多取10次
    			$arrInput = array(
    			        'forum_id' => $forum_id,
    			        'page_num' => $i,
    			        'limit' => 10,
    			        );
    			
    			$arrRetRoom = Service_Room_Room::getFrsRooms($arrInput);
    			
    			//Bingo_Log::warning(print_r($arrInput,1));
    			//Bingo_Log::warning(print_r($arrRetRoom,1));
    			if (!is_array($arrRetRoom['res']['list']) || empty($arrRetRoom['res']['list'])){
    			    break;
    			}
    			
    			foreach ($arrRetRoom['res']['list'] as $one){
    			    $reqs[] = array(
    			            'groupId' => $one['room_id'],
    			            'priorUserList' => $priorUserList,
    			            );
    			}
    			$i ++;
			}

		}
		
		$arrInput = array(
		        'msgType' => 'msgcenter',
		        'reqs' =>  $reqs,
		        'userId' => 0, //填无效值
		        'msgBody' => json_encode($content), //消息体JSON串
		        );
		//Bingo_Log::warning("===type=$type===");
        //Bingo_Log::warning(print_r($arrInput,1));
       // Bingo_Log::warning(print_r($content,1));
		$ret = Tieba_Service::call('im', 'pushMsgBody', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
       // Bingo_Log::warning(print_r($ret),1);
		if (!isset( $ret['errno'] ) || $ret['errno'] !==  Tieba_Errcode::ERR_SUCCESS){
		    Bingo_Log::warning('[sendUserMsgToLCSByRoom] send lcs error :'.$ret['errno']);
		    return false;
		}
		return true;
	}
	
	public static function sendUserMsgToLCSByRoom($content , $user_id) {
	
        $arrInput = array(
                'msgType' => 'pmsg',
                'groundId' =>  0,
                'userId' => $user_id, //填无效值
                'msgBody' => json_encode($content), //消息体JSON串
        );
        $ret = Tieba_Service::call('im', 'pushMsgBody', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        if (!isset( $ret['errno'] ) || $ret['errno'] !==  Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('[sendUserMsgToLCSByRoom] send lcs error :'.$ret['errno']);
            return false;
        }
       
	    return true;
	}
	
	

}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
