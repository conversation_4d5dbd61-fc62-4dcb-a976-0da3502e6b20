<?php
/**
 * 定时更新frs视屏推荐的tag内容
 *
 * <AUTHOR> tanxinyun
 */

mylog('begin updating...');

$start = 0;
$limit = 10;
while (true) {
    $tagList = getTagList($start, $limit);
    if ($tagList === false) {
        mylog("get tag list failed, start: $start, limit: $limit");
        continue;
    }
    if (empty($tagList)) {
        break;
    }
    updateRecommend($tagList);
    $start += $limit;
}

mylog('finished');

/**
 * 日志输出
 * @param  [string] $msg [description]
 * @return [string]      [description]
 */
function mylog($msg, $newline = true) {
    $sfx = $newline ? "\r\n" : '';
    $line = date('Y-m-d H:i:s').' '.$msg.$sfx;

    echo "$line";

    return $line;
}

/**
 * 获取要更新的tag信息
 *
 * @param
 * @return
 */
function getTagList($start, $limit) {
    $param = array(
        'offset' => $start,
        'limit' => $limit,
    );
    $ret = Tieba_Service::call('show', 'getTagWithPlatform', $param);
    
    if (!$ret || $ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
        mylog("get online tags failed, ret: ".serialize($ret));
        return false;
    }
    
    return $ret['data']['data'];
}

/**
 * 更新tag内容
 *
 * @param
 * @return
 */
function updateRecommend($tagList) {
    foreach ($tagList as $tag) {
        mylog($tag['platform_name'].': '.$tag['tag_id'].' => ', false);

        // online置为0,则不更新
        if (!$tag['online']) {
            mylog('not online');
            continue;
        }

        $ret = setRecommend($tag);
        
        if ($ret === false) {
            continue;
        }

        mylog('success');
    }
}

/**
 * 将tag的推荐内容塞到redis中, frs会直接访问redis
 *
 * @param
 * @return
 */
function setRecommend($tag) {
    $input = array(
        'tag_info' => $tag,
    );

    $ret = Tieba_Service::call('show', 'setRecommendByTag', $input); 
    
    if (!$ret || $ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
        mylog("set recommend by tag failed, ret: ".serialize($ret));
        return false;
    }

    return true;
}
