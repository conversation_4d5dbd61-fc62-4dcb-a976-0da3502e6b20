<?php
/**
 *Author: ji<PERSON><PERSON><PERSON>
 *Filename: getFrsList.php
 *Date: 2014-08-27
 *Desc: 获取FRS页的顶部的推广信息
 */
$start_time = time();
$zhanqiHost = "http://www.zhanqi.tv";

require_once(dirname(__FILE__) . '/../../util/YYLive.php');
require_once(dirname(__FILE__) . '/../../data/Forum.php');


//获取所有的tag列表
$arrRet = Tieba_Service::call('show','getAllYYTagList',array());
if($arrRet == false || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
    echo "call show[getYYtagList] fail\n";
    var_dump($arrRet);
    exit;
}
$tag_list = array();
foreach($arrRet['data']['list'] as $key=>$value){
        $tag_list[$value['tag_name']] = $value['tag_id'];
}

// echo print_r($tag_list,1);

if(empty($tag_list)){
    echo  "has no tag\n";
    exit;
}

// echo dirname(__FILE__).'/../../../../conf/app/show/forumAndTagMap.csv';
$fileContent =  array();
$arrayContent = file(dirname(__FILE__).'/../../../../conf/app/show/forumAndTagMap.csv');
foreach($arrayContent as $line => $content){
    echo 'line '.($line + 1).':'.$content;
    $fileContent[] = explode(',',$content);
}

$file = fopen(dirname(__FILE__).'/../../../../conf/app/show/forumAndTagMap.csv','r'); 

while ($data = fgetcsv($file)) { //每次读取CSV里面的一行内容
echo " old ".print_r($data,1)."\n"; //此为一个数组，要获得每一个数据，访问数组下标即可
// $fileContent[] = $data;
 }
// echo print_r($goods_list,1);


 fclose($file);

 $coutSuc =0;
 $coutFail =0;
 $coutZero =0;
 foreach ($fileContent as $key=>$value){
    echo print_r($value,1)." \n";
    $forumName = $value['0'];
    $tag_name = $value['3'];
    $arrForumInfo = Data_Forum::getFidByFname($forumName);
    if($arrForumInfo == false){
        $coutFail++;
        echo $forumName." get forum info fail \n";
        continue;
    }
    if(0 ==$arrForumInfo['forum_id'] ){
        $coutZero++;
        echo $forumName." get forum_id is 0 \n";
        continue;
    }
    $forum_id = array();
    $forum_id[]=$arrForumInfo['forum_id'];
    $tag_id=$tag_list[$tag_name];
    $arrInput  = array(
        'forum_ids' => $forum_id, 
        'tag_id' => $tag_id,
        );
    $arrRet = Tieba_Service::call('show','bindYYTagWithForum',$arrInput);
    if($arrOut == false || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
        $coutFail ++;
        echo $forumName.' bind tag with forum fail'."\n";
        continue;
    }
    $coutSuc++;
    echo $forumName." and ".$tag_name." success \n";
}   
echo $coutSuc."   success\n";
echo $coutFail."  fail\n";
echo $coutZero."  zero\n";
$end_time = time();
echo "total process time:" . ($end_time - $start_time);
