<?php
/**
 *Author: wangjunsheng
 *Filename: getTagList.php
 *Date: 2015-04-10
 *Desc: 获取所有的tag_list，复用了原yylive的FRS广告配置平台,所以看到yyLive的service是正常
 */
define ('BINGO_ENCODE_LANG','UTF-8');
require_once(dirname(__FILE__) . '/../../util/YYLive.php');
$objOrp = Orp_FetchUrl::getInstance();
$url = 'http://www.zhanqi.tv/api/static/game.lists/100-1.json';
$strRet = $objOrp->get($url);
echo "the request url is[" . $url .']';
echo "\n";
if($strRet == false){
    echo "call yy url fail";
    exit;
}
$arrRet = Bingo_String::json2array($strRet);
if($arrRet['code'] != 0){//返回结果错误
    echo "yy return fail status\n";
    echo $strRet;
    exit;
}

$arrData = $arrRet['data']['games'];


//获取所有当前所有的tag_list，和从yy获取的做diff去重,之后再添加
$arrInput = array(
);
$arrOut = Tieba_Service::call('show','getAllYYTagList',$arrInput,NULL,NULL,'POST','php','utf-8');
if($arrOut==false||Tieba_Errcode::ERR_SUCCESS!=$arrOut['errno']){
    echo 'call service getYYTagList fail.the result is[' . var_dump($arrOut) . ']';
    exit;
}
$arrAddTag = array();
foreach($arrData as $key=>$value){
   $arrAddTag[$value['id']] = $value;
}
$tag_list = $arrOut['data']['list'];
$arrTagNameList = array();
$arrTagidList = array();
$arrTagList = array();
if(!empty($tag_list)){
    foreach($tag_list as $key=>$value){
        $arrTagNameList[] = $value['tag_name'];
        $arrTagidList[] = $value['tag_id'];
        $arrTagList[$value['tag_id']] = $value;
    }
}
foreach($arrAddTag as $key=>$value){
    $arrInput = array(
        'tag_id' => $value['id'],
        'tag_name' => $value['name']
    );
    if(isset($arrTagList[$value['id']])){
        //看两者信息是否完全一致,如果不一致,需要update
        if($value['name']!=$arrTagList[$value['id']]['name']){
            $arrOut = Tieba_Service::call('show','updateYYTagInfo',$arrInput,NULL,NULL,'POST','php','utf-8');
            if($arrOut == false || Tieba_Errcode::ERR_SUCCESS!=$arrOut['errno']){
                echo "update tag_id[" . $value['id'] ."],tag_name[" .$value['name']."] fail\n";
            }else{
                echo "update tag_id[" . $value['id'] ."],tag_name [" . $value['name']."] success\n";
            }
        }
    }else{
        $arrOut = Tieba_Service::call('show','addNewYYTag',$arrInput,NULL,NULL,'POST','php','utf-8');
        if($arrOut == false || Tieba_Errcode::ERR_SUCCESS!=$arrOut['errno']){
            echo "add tag_id[" . $value['id'] ."],tag_name[" .$value['name']."] fail\n";
        }else{
            echo "add tag_id[" . $value['id'] ."],tag_name [" . $value['name']."] success\n";
        }
    }
}
echo "success";
