<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-05-04 19:55:37
 * @version
 */

class Util_Conf {

    private static $_conf;

    public static function getUiConf(){
        if(self::$_conf){
            return self::$_conf ;
        }
        self::$_conf = Bd_Conf::getConf('/app/show/ui_show');

        if(false === self::$_conf){
            Bingo_Log::warning("read conf fail");
            return null;
        }
        
        return self::$_conf ;
    }
}
?>