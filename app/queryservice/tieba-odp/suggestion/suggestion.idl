struct forum_intro{
	string forum_name; //吧名称
	string forum_id; //吧名称
	string avastar = optional();//吧头像
	string slogan = optional();//吧简介
	string dir = optional(); //吧目录
	uint32_t member_num=optional();//吧会员
	uint32_t thread_num=optional();//吧帖子数
};

struct output {
	forum_intro intro[]; 
};

service sug {
	void getSug(string keyword[],out output ret);	
	void getSugByFname(string forum_name[],out output ret);	
};
