<?php
/**
* @copyright Copyright (c) www.baidu.com
* <AUTHOR> z<PERSON><PERSON><PERSON>@baidu.com
* @date 2009-12-29
* @version
*/
class OpenConfService {
    const FILE_TPL = 
'<?php
//this file is published by imis
class ItiebaOpenConfig {
    public static $config;
}
ItiebaOpenConfig::$config = %s;
';
    
    public static function searchOpenConf(){
        self::_readFile();
        $arrResult = ServiceHelper::defaultResult();
        $arrResult['total'] = count(ItiebaOpenConfig::$config);
        if ($arrResult['total'] > 0){
            foreach (ItiebaOpenConfig::$config as $key => $row){
                $arrResult['data'][] = array_merge($row,array('key' => $key));
            }
        }
        return $arrResult;
    }
    public static function deleteOpenConf($arrParam){
        self::_readFile();
        $config = ItiebaOpenConfig::$config;
        $strKey = $arrParam['key'];
        if (isset($config[$strKey])){            
            unset($config[$strKey]);  
            $ret = self::_writeFile($config);
            if($ret == true){
                MisLog::notice('del openconf',$arrParam);    
            }              
        }else{
            MisLog::warning("$strKey is not defined");
        }
        
    }
    public static function addOpenConf($arrParam){
        $arrInput = DataAccessUtils::fetchArray($arrParam,array('domain','name','link'));
        $strDomain = $arrInput['domain'][0];
        if (empty($strDomain)){
            return false;
        }
        $strDomain = str_replace('.','_',$strDomain);
        $strDomain = str_replace(':','_',$strDomain);
        $strKey = substr($strDomain,0,32);
        self::_readFile();
        $config = ItiebaOpenConfig::$config;
        if(isset($config[$strKey])){
            ServiceContext::append('feedback',"{$strKey}��ͻ������ϵRD���߼�������Ƿ��Ѵ���");
            return false;
        }
        $name = $arrInput['name'];
        foreach ($config as $each){
            if ($each['name'] == $name){
                ServiceContext::append('feedback',"{$name}�Ѵ���");
                return false;
            }
        }
        $config[$strKey] = $arrInput;
        $ret = self::_writeFile($config);
        if($ret === true){
            MisLog::notice('add openconf',$arrParam);
            return true;    
        }
        return false;
        
    }
    private static function _writeFile($config){
        if (!is_array($config)){
            return false;
        }
        $file = MIS_TMP_PATH.'/ItiebaOpenConfig.class.php';
        $fileTest = MIS_TMP_PATH.'/TestItiebaOpenConfig.class.php';        
        file_put_contents($fileTest,sprintf(self::FILE_TPL,var_export($config,true)));
        $phpHome = Configuration::getConfig ( 'php_home' );
        $intErrNo = 0;
        $cmd = "$phpHome/php -l $fileTest";
        $arrOutput = array ();
        exec ( $cmd, $arrOutput, $intErrNo );
        if($intErrNo > 0){
            ServiceContext::append('�����ļ��ڲ���������ϵRD');
            return false;
        }
        file_put_contents($file,file_get_contents($fileTest));
        self::sendConf();
        return true;
    }
    private static function _readFile(){
        $file = MIS_TMP_PATH.'/ItiebaOpenConfig.class.php';
        if (!file_exists($file)) {
        	self::_initDefault($file);
        }
        require_once($file);      
    }
    private static function _initDefault($file){
        file_put_contents($file,sprintf(self::FILE_TPL,var_export(array(),true)));
    }
    public static function sendConf(){
        $file = 'ItiebaOpenConfig.class.php';
        $sender = new RemoteFileSender ( MIS_TMP_PATH );
        $sender->setEmailTo ( Configuration::getConfig ( 'alarm_mail_to' ) );
        $arrFile = array (
            $file
        );
        $sender->send ( $arrFile, Configuration::getConfig ( 'front_ui_machine' ), 
            Configuration::getConfig ( 'front_ui_uname' ), Configuration::getConfig ( 'front_ui_conf_path' ) ); 
         return true;       
    }
}