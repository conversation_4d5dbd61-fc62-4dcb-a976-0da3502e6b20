<?php
///***************************************************************************
// * 
// * Copyright (c) 2009 Baidu.com, Inc. All Rights Reserved
// * 
// **************************************************************************/
// 
// 
// 
///**
// * @file dal_info.php
// * <AUTHOR>
// * @date 2010/03/22 14:50:03
// * @brief 
// *  
// **/
//
//$GLOBALS['rpcActionMethodbakanDal'] = array ( 
//	'searchDir' => array('class' => 'BakanMis', 'method' => 'searchDir'),
//	'getAllBakanBaseInfo'    => array ('class' => 'Bakan',    'method' => 'getAllBakanBaseInfo'),
//    'getBakanBaseInfo'    => array ('class' => 'Bakan',    'method' => 'getBakanBaseInfo'),
//	'getBakanBaseInfoById'    => array ('class' => 'Bakan',    'method' => 'getBakanBaseInfoById'),
//	'getBakanInfoById'    => array ('class' => 'Bakan',    'method' => 'getBakanInfoById'),
//    'getBakanInfoByThreadId'    => array ('class' => 'Bakan',    'method' => 'getBakanInfoByThreadId'),
//	'getBakanContentById'    => array ('class' => 'Bakan',    'method' => 'getBakanContentById'),
//	'getBakanAllContents'    => array ('class' => 'Bakan',    'method' => 'getBakanAllContents'),
//	'getBakanContentNum'    => array ('class' => 'Bakan',    'method' => 'getBakanContentNum'),
//
//	'getBakanPublicCount'    => array ('class' => 'Bakan',    'method' => 'getBakanPublicCount'),
//	'getBakanPublicList'    => array ('class' => 'Bakan',    'method' => 'getBakanPublicList'),
//	'getBakanNoPublicList'    => array ('class' => 'Bakan',    'method' => 'getBakanNoPublicList'),
//	'getBakanImportList'    => array ('class' => 'Bakan',    'method' => 'getBakanImportList'),
//	'getBakanLastList'    => array ('class' => 'Bakan',    'method' => 'getBakanLastList'),
//	'getBakanPublicSetInfo'    => array ('class' => 'Bakan',    'method' => 'getBakanPublicSetInfo'),
//	'getBakanEditLock'    => array ('class' => 'Bakan',    'method' => 'getBakanEditLock'),
//	'getAutoPublicList'    => array ('class' => 'Bakan',    'method' => 'getAutoPublicList'),
//	'getPublicNoThreadList' => 	array ('class' => 'Bakan',    'method' => 'getPublicNoThreadList'),
//
//	'newBakan'    => array ('class' => 'Bakan',    'method' => 'newBakan'),
//	'updateModuleName'    => array ('class' => 'Bakan',    'method' => 'updateModuleName'),
//	'updateFdirInfo' => array ('class' => 'Bakan',    'method' => 'updateFdirInfo'),
//
//	'switchPublicTime'    => array ('class' => 'Bakan',    'method' => 'switchPublicTime'),
//	'updateBakanPublicSet'    => array ('class' => 'Bakan',    'method' => 'updateBakanPublicSet'),
//	'updateBakanInfo'    => array ('class' => 'Bakan',    'method' => 'updateBakanInfo'),
//	'updateBakanContent'    => array ('class' => 'Bakan',    'method' => 'updateBakanContent'),
//	'updateBakanEditLock'    => array ('class' => 'Bakan',    'method' => 'updateBakanEditLock'),
//	'resetBakanPublicTime'    => array ('class' => 'Bakan',    'method' => 'resetBakanPublicTime'),
//	'approvedBakanPublic'    => array ('class' => 'Bakan',    'method' => 'approvedBakanPublic'),
//	'rejectBakanPublic'    => array ('class' => 'Bakan',    'method' => 'rejectBakanPublic'),
//	'openBakanModule'    => array ('class' => 'Bakan',    'method' => 'openBakanModule'),
//	'addBakanEditLog'    => array ('class' => 'Bakan',    'method' => 'addBakanEditLog'),
//
//	'getNextPublicTimeRpc'    => array ('class' => 'Bakan',  'method' => 'getNextPublicTimeRpc'),
//	//mis conf 
//	'openBakanModule' => array ('class' => 'Bakan',  'method' => 'openBakanModule'),
//	'closeBakanModule' => array ('class' => 'Bakan',  'method' => 'closeBakanModule'),
//	//audit 
//	'rejectBakanPublic' => array ('class' => 'Bakan',  'method' => 'rejectBakanPublic'),
//	'approvedBakanPublic' => array ('class' => 'Bakan',  'method' => 'approvedBakanPublic'),
//	
//	//mis 
//	'searchMisBakanBase' => array('class' => 'BakanMis',  'method' => 'searchMisBakanBase'),
//	'searchMisBakanList' => array('class' => 'BakanMis',  'method' => 'searchMisBakanList'),
//	'getForumNameByFids' => array('class' => 'BakanMis',  'method' => 'getForumNameByFids'),
//	'recoverBakan' => array('class' => 'BakanMis',  'method' => 'recoverBakan'),
//
//	// add for feedback
//	'getBakanMemberGradeAll'    => array ('class' => 'Bakan',  'method' => 'getBakanMemberGradeAll'),
//	'getBakanUserMemberGrade'    => array ('class' => 'Bakan',  'method' => 'getBakanUserMemberGrade'),
//	'getBakanUserMemberFeedback'    => array ('class' => 'Bakan',  'method' => 'getBakanUserMemberFeedback'),
//	'getBakanFeedbackNum'    => array ('class' => 'Bakan',  'method' => 'getBakanFeedbackNum'),
//	'addBakanMemberFeedback'    => array ('class' => 'Bakan',  'method' => 'addBakanMemberFeedback'),
//	'addBakanMemberGrade'    => array ('class' => 'Bakan',  'method' => 'addBakanMemberGrade'),
//	'updateBakanDir'    => array ('class' => 'Bakan',  'method' => 'updateBakanDir'),
//	'getBakanDirs'    => array ('class' => 'Bakan',  'method' => 'getBakanDirs'),
//	
//
//);
//
//
//
///* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
