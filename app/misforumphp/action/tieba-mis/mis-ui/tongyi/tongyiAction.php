<?php
class tongyiAction extends TbMisBingo2Action
{
	public static $intConnectTimeOut = 200;
	
	public static $intReadTimeOut = 200;
	protected static $feedback = "";
	const  NUMBER_PER_PAGE = 30;
	public function indexAction(){
		$arrDefaultConfig = array(
                      'size' => self::NUMBER_PER_PAGE, 
			'page' => 1,
			'word' => '',
                    );
        	$arrInput = TbMisViewHelper::getCommonInput($arrDefaultConfig);

		$arrRes = TongyiService::getData($arrInput);
		$arrTpl['mis_feedback']=self::$feedback;  
		$arrTpl = array_merge($arrTpl, array(
					'mis_table' => 'tongyi',
					'mis_table_template' => 'tongyi_template',
                		    'mis_name' => 'ͬ������MIS',
					));
		$arrData = array(
                                'page' => $arrInput['page'],
                                'total' => $arrRes['count'],
                                'data' => $arrRes['data'],
                        );
		TbMisViewHelper::viewProcess($arrInput,$arrData, $arrTpl, $arrDefaultConfig);
		Bingo_Log::notice("build postaudit done");
	}

	public function addAction(){
		$strTopic = Bingo_Http_Request::getNoXssSafe('topic', '');
		$strTopicContent = Bingo_Http_Request::getNoXssSafe('topic_content', '');

		$arrContent = explode("\n", $strTopicContent);
		$arrForum = Array($strTopic);
		$arrForum = array_merge($arrForum, $arrContent);

		$arrInput['word'] = $arrForum;

		TongyiService::addData($arrInput);
	}
}
