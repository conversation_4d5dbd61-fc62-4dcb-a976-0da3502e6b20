<?php
class adpicAction extends TbMisBingo2Action
{
	protected static $feedback = "";
	public function indexAction(){
		$arrDefaultConfig = array(
                      'size' => 0, 
                    );
        $arrInput = TbMisViewHelper::getCommonInput($arrDefaultConfig);
        $arrData = array();
        $arrTpl = array();
        $arrTpl['mis_feedback']=self::$feedback;  
		$arrTpl = array_merge($arrTpl, array(
				'mis_table_template' => 'molihuapic',
		));
		TbMisViewHelper::viewProcess($arrInput,$arrData, $arrTpl, $arrDefaultConfig);
	}
	public function newAction(){
		$urls = TbMisRequest::getParam('urls','');
		$arrUrl = TbMisTools::uniqKeyword($urls);
		$arrInput['cmd'] = 4;
	    $arrInput['product_id'] = 3;
	    $arrInput['data_type'] = 1;
	    $arrInput['log_id'] = REQUEST_ID;
	    $arrInput['receive_ip'] = $_SERVER['REMOTE_ADDR'];
	    $arrInput['receive_port'] = 7700;
	    $arrInput['user_data'] = pack("i",3);
		foreach ($arrUrl as $url){
			$arrInput['img_url'] = $url;
	      	$ret = rpc::rpcCall('imagelib','send',$arrInput, $arrOut);  			
	      	if ($ret === false){
	      		 TbMisLog::warning("talk with imagelib error");
	      		 return false;
	      	}
	      	TbMisLog::debug("talk with imagelib sucess");
		}		
		sleep(1);
		self::$feedback = "�ύ�ɹ�"; 
		$this->indexAction();
	}
}
