<?php
class WordsrvFilterStrLike extends WordsrvFilterAbstract {
	public function getCond() {
		$arrParam = $this->_objBroker->getData();
		$mixedRes = false;
		if (isset($arrParam[$this->_strField]) && ($strValue = trim($arrParam[$this->_strField])) != '') {
			$strValue = WordsrvDb::escapeStr($strValue);
			$mixedRes = "{$this->_strField} like '%{$strValue}%'";
		}
		
		return $mixedRes;
	}
	
	public function getHtml() {
		$arrParam = $this->_objBroker->getData();
		
		ob_start();
        include(MIS_TEMPLATES_PATH . '/include/filter/str_like.php');
        $strBuffer = ob_get_contents();
        ob_end_clean();
        
        return $strBuffer;		
	}
}