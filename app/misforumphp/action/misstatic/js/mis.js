var Mis = Mis || {
    /** @ignore */
    version:"20100301",
    /** @function */
    emptyFn:function(){}
};

function getRadioChecked(name) {
	var radios = document.getElementsByName(name);
	var elm = false;
	
	Fe.each(radios, function(radio) {
		if (radio.checked) {
			elm = radio;
		}
	});
	
	return elm;
}

//��ർ����
Mis.nav = {
    init: function(){
      $('#nav_content dl dt').bind("click", function(e){
          Mis.nav.toggleItem(this);
          e.preventDefault();
      });
    },
    toggleItem: function(elm) {
        var dds = $(elm).parent().children().filter("dd");
        if ($(dds[0]).css('display') != 'none') {
            elm.innerHTML = elm.innerHTML.replace(/^\-/, '+');
            dds.hide();
        } else {
            elm.innerHTML = elm.innerHTML.replace(/^\+/, '-');
            dds.show();
        }
    },
    closeAllItem: function() {
        $('#nav_content dl dd').hide();
        $('#nav_content dl dt').each(function() {
           this.innerHTML = this.innerHTML.replace(/^\-/, '+'); 
        });
    },
    openAllItem: function() {
        $('#nav_content dl dd').show();
        $('#nav_content dl dt').each(function() {
           this.innerHTML = this.innerHTML.replace(/^\+/, '-'); 
        });        
    },
    toggle: function() {
        var mis_nav = Fe.G('mis_nav');
        var mis_split = Fe.G('mis_split');
        if (mis_nav.style.display != 'none') {
            mis_nav.style.display = 'none';
            mis_split.title = 'show nav';
            mis_split.innerHTML = '&gt;';
        } else {
            mis_nav.style.display = '';
            mis_split.title = 'hide nav';
            mis_split.innerHTML = '&lt;';            
        }
    }
};

Mis.checkbox = {
	selectAll: function() {
		var selectAll = Fe.G('select_all');
		var checked = selectAll.checked;
		var checkboxs = document.getElementsByName('select_checkbox');
		Fe.each(checkboxs, function(checkbox) {			
			checkbox.checked = checked;
		});
	},
	sync: function() {
		var all_checked = true;
		var selectAll = Fe.G('select_all');
		var checkboxs = document.getElementsByName('select_checkbox');
		Fe.each(checkboxs, function(checkbox) {
			if(!checkbox.checked) {
				all_checked = false;
			}
		});
		if(all_checked) {
			selectAll.checked = 'checked';	
		} else {
			selectAll.checked = false;
		}
	},
	_getSelectedIds : function() {
		var select_ids = [];	
		var checkboxs = document.getElementsByName('select_checkbox');
		Fe.each(checkboxs, function(checkbox) {
			if(checkbox.checked) {
				select_ids.push(checkbox.value);
			}
		});
		return  select_ids.join(',');;
	},
        _getUnSelectedIds : function() {
                var select_ids = [];
                var checkboxs = document.getElementsByName('select_checkbox');
                Fe.each(checkboxs, function(checkbox) {
                        if(!checkbox.checked) {
                                select_ids.push(checkbox.value);
                        }
                });
                return select_ids.join(',');;
        },
	//ѡ�еĽ��лָ�����
	recoverSelected: function(url) {
		var selected_ids = this._getSelectedIds();
		var unselected_ids = this._getUnSelectedIds();
		//��������
		$.post(url, {sids: selected_ids, usids: unselected_ids});
		document.location.reload();
	},
        build_id_form:function(elm){
		var selected_ids = this._getSelectedIds();
		var unselected_ids = this._getUnSelectedIds();
                elm.sids.value=  selected_ids ;
                elm.usids.value=  unselected_ids ;
        }
}

//����������
Mis.radio_search = {
	_radio_list : [],
	_types :{
		'ip' : {
			name : 'ip'
		},
		'username' : {
			name : '�û���'
		},
		'userid' : {
			name : '�û�id'
		},
		'fname' : {
			name : '����'
		},						
		'word' : {
			name : '�ؼ���'
		},
		'adder' : {
			name : '�����'
		},
		'deleter' : {
			name : 'ɾ����'
		},
		'created_at' : {
			name : '����ʱ��',
			click : 'Mis.radio_search.change_input.created_at()',
			input : function(keyword, keyword_2) {
				return '��<input type="text" name="keyword" value="'+ keyword +'" />��<input type="text" name="keyword_2" value="'+ keyword_2 +'" />(��ʽ:2008-03-01 14:09)';
			}
		},
		'addtime' : {
			name : '���ʱ��',
			click : 'Mis.radio_search.change_input.created_at()',
			input : function(keyword, keyword_2) {
				return '��<input type="text" name="keyword" value="'+ keyword +'" />��<input type="text" name="keyword_2" value="'+ keyword_2 +'" />(��ʽ:2008-03-01 14:09)';
			}
		},
		'failtime' : {
			name : 'ʧЧʱ��',
			click : 'Mis.radio_search.change_input.created_at()',
			input : function(keyword, keyword_2) {
				return '��<input type="text" name="keyword" value="'+ keyword +'" />��<input type="text" name="keyword_2" value="'+ keyword_2 +'" />(��ʽ:2008-03-01 14:09)';
			}
		},
		'deleted_at' : {
			name : 'ɾ��ʱ��',
			click : 'Mis.radio_search.change_input.deleted_at()',
			input : function(keyword, keyword_2) {
				return '��<input type="text" name="keyword" value="'+ keyword +'" />��<input type="text" name="keyword_2" value="'+ keyword_2 +'" />(��ʽ:2008-03-01 14:09)';
			}
		},
		'description' : {
			name : '��ע'
		},
		'disable_at' : {
			name : 'ʣ��ʱ��',
			click : 'Mis.radio_search.change_input.disable_at()',
			input : function(keyword) {
				if (keyword == '') keyword = 3;
				var output = ['<select name="keyword">'];
				Fe.each([1, 3, 7, 30, -1], function(key) {
					if (key != -1) {
						if (keyword == key) {
							output.push(Fe.format('<option value="#{key}" selected="selected">#{key}��</option>', {key: key}));
						} else {
							output.push(Fe.format('<option value="#{key}">#{key}��</option>', {key: key}));	
						}
					} else {
						if (keyword == key) {
							output.push(Fe.format('<option value="#{key}" selected="selected">����</option>', {key: key}));
						} else {
							output.push(Fe.format('<option value="#{key}">����</option>', {key: key}));	
						}
					}
				});
				return output.join('');
			}
		}
	},
	set_types : function(types){
		Mis.radio_search._types = types;
	},
	init: function(radio_list) {
		Mis.radio_search._radio_list = radio_list;
	},
	build: function(search_type, keyword, keyword_2) {
		var output = [];

		if (typeof search_type === 'undefined') {
			search_type = '';
		}

		if (typeof keyword  === 'undefined') {
			keyword = '';
		}

		if (typeof keyword_2 === 'undefined') {
			keyword_2 = '';
		}

		//���û��search_type���򽫵�һ��radio_list����search_type
		if (search_type == '') {
			search_type = Mis.radio_search._radio_list[0];
		}		
		var radio_search = Fe.G('mis_radio_search');
		
		if (keyword != '') {
			if (search_type == 'created_at' || search_type == 'addtime' || search_type == 'failtime') {
				output.push(Fe.format('<p class="warning">�� #{search_type} ���� #{keyword} �� #{keyword_2} �������</p>', { search_type: Mis.radio_search._types[search_type]['name'], keyword: keyword, keyword_2: keyword_2}));
			} else if(search_type == 'disable_at') {
				if (keyword == -1) {
					output.push(Fe.format('<p class="warning">�� #{search_type} ���� ���� �������</p>', { search_type: Mis.radio_search._types[search_type]['name']}));
				} else {
					output.push(Fe.format('<p class="warning">�� #{search_type} ���� #{keyword} �������</p>', { search_type: Mis.radio_search._types[search_type]['name'], keyword: keyword}));
				}
			} else {
				output.push(Fe.format('<p class="warning">�� #{search_type} ���� #{keyword} �������</p>', { search_type: Mis.radio_search._types[search_type]['name'], keyword: keyword}));
			}
		}

		
		output.push('<p id="mis_radio_search_input">');

		
		if ('input' in Mis.radio_search._types[search_type]) {
			output.push(Mis.radio_search._types[search_type].input(keyword, keyword_2));
		} else {
			output.push('<input type="text" name="keyword" value="'+ keyword +'"/>');
		}

		output.push('<input type="submit" value="����" /><p>');
		
		var radio_id = 0;
		
		Fe.each(Mis.radio_search._radio_list, function(radio) {

			radio_id++;
			
			if (radio != search_type) {
				var checked = '';
			} else {
				var checked = 'checked="checked"';
			}

			var click = '';

			if ('click' in Mis.radio_search._types[radio]) {
				click = Mis.radio_search._types[radio]['click'];
			} else {
				click = 'Mis.radio_search.change_input._normal_()';
			}
			
			output.push(Fe.format('<label for="#{radio_id}"><input type="radio" id="#{radio_id}" name="search_type" value="#{search_type}" #{checked} onclick="#{click}"/>#{name}</label>', {
				'radio_id': 'search_type_' + radio_id,
				'checked': checked,
				'search_type': radio,
				'click': click,
				'name': Mis.radio_search._types[radio]['name']
			}));
		});
		
		output.push('</p>');
		
		radio_search.innerHTML = output.join('');
	},
	change_input: {
		'created_at' : function() {
			Fe.G('mis_radio_search_input').innerHTML = '��<input type="text" name="keyword" />��<input type="text" name="keyword_2" />(��ʽ:2008-03-01 14:09) <input type="submit" value="����" />';
		},
		'deleted_at' : function() {
			Fe.G('mis_radio_search_input').innerHTML = '��<input type="text" name="keyword" />��<input type="text" name="keyword_2" />(��ʽ:2008-03-01 14:09) <input type="submit" value="����" />';
		},
		'disable_at' : function() {
			Fe.G('mis_radio_search_input').innerHTML = ['<select name="keyword">',
				'<option value="1">1��</option>',
				'<option value="3" selected="selected">3��</option>',
				'<option value="7">7��</option>',
				'<option value="30">30��</option>',
				'<option value="-1">����</option>',
				'</select> <input type="submit" value="����" />'].join('');
		},
		'_normal_' : function() {
			Fe.G('mis_radio_search_input').innerHTML = '<input type="text" name="keyword" /><input type="submit" value="����" />';
		}
	},
	check_form: function() {
		var keyword = document.getElementsByName('keyword')[0].value;

		if (getRadioChecked('search_type').value == 'created_at') {
			if (keyword == '') {
				alert('���ڲ���δ��');
				return false;
			}

			var keyword_2 = document.getElementsByName('keyword_2')[0].value;

			if (keyword_2 == '') {
				alert('���ڲ���Ϊ��');
				return false;
			}

			var re = /\d{4}-\d{2}-\d{2} \d{2}:\d{2}/;
			
			if (re.test(keyword) && re.test(keyword_2)) {
			} else {
				alert('���ڲ��Ϸ�����ʽ��2008-03-01 14:09��');
				return false;
			}
		}

		if (keyword == '') {
			alert('�ؼ��ֲ���Ϊ��');
			return false;
		}

	}
}
Mis.common = {
		op:function (url,elm){
			$.get(url+'&stamp='+ new Date().getTime(),function(){
					$(elm).parent().html('�����ɹ�');
				});
		},
		btnop:function (url,elm){
			$.get(url+'&stamp='+ new Date().getTime(),function(){
					elm.disabled = true;
				});
		},		
		refesh:function (url,elm){//����Ƿ�����д���ˣ����Ǻܶ�������ã�û�취
			$.get(url+'&stamp='+ new Date().getTime(),function(){
					self.location.href = self.location.href;
				});
		},
		refresh:function (url,elm){
			$.get(url+'&stamp='+ new Date().getTime(),function(){
					self.location.href = self.location.href;
				});
		},		
		post:function (url,obj,elm){
			$.post(url+'&stamp='+ new Date().getTime(),obj,function(){
					$(elm).parent().html('�����ɹ�');
				});
		},
		get:function (url,elm){
			$.get(url+'&stamp='+ new Date().getTime(),function(){
					$(elm).parent().html('�����ɹ�');
				});
		}					
}
Mis.post = {
    display_all : function(elm) {
        //��������ͼ
        var div = elm.parentNode;
        var td = elm.parentNode.parentNode;
        var table = td.getElementsByTagName('table')[0];
        
        if (table) {
            table.style.display = 'none';
        }
        
        var content = div.getElementsByTagName('p')[0].innerHTML;
        elm.parentNode.innerHTML = content;
    },
    getImgRealSize : function() { 	
        $('span.resized_img').each(function(index, span) {
            var img_src = $(span).attr('img_src');
            var image = new Image();
            image.onload = function() {          	
                $(span).html(this.width + ' * ' + this.height);
            }
            image.src = img_src;
            console.log(img_src);

        });
    }
}

Mis.basedialog = {
	simple_confirm : function(url){
		var dialog = Fe.Dialog.confirm('ȷ��ִ�в�����',{
			  width:'250px',
			  title:'ȷ��'
			}); 
		dialog.onaccept = function(){
				$.get(url+'&stamp='+ new Date().getTime(),function(){
					setTimeout(function(){
						self.location.href = self.location.href;	
					},300);
				})
			};		
	},
	btn_confirm : function(url,msg,elm){
		var dialog = Fe.Dialog.confirm(msg,{
			  width:'250px',
			  title:'ȷ��'
			}); 
		dialog.onaccept = function(){
				$.get(url+'&stamp='+ new Date().getTime(),function(){
					elm.disabled = true;
				})
			};		
	},	
	msg_confirm : function(url,msg){
		var dialog = Fe.Dialog.confirm(msg,{
			  width:'250px',
			  title:'ȷ��'
			}); 
		dialog.onaccept = function(){
				$.get(url+'&stamp='+ new Date().getTime(),function(){
					setTimeout(function(){
						self.location.href = self.location.href;	
					},300);
				})
			};		
	},	
	iframe : function(url,title_desc,width,height){
		Fe.Dialog.open(url+'&stamp='+ new Date().getTime(), { 
		    contentType:'iframe',
		    width:width+'px',
		    height:height+'px',
		    title: title_desc
		  });		
	}
}

//��ʾ��ϸ��Ϣ�ĵ�����
Mis.detail = {
	_init_: function() {
		$(document).bind('click', function(e){
			var className = e.target.parentNode.className;
			
			if (className == 'cpl_detail' || className == 'detail_popup') {
			} else {
				Mis.detail.hide();
			}
		});
	},
	_lastElm: false,
	show: function(elm) {
		var popup = $(elm).next()[0];
		
		if (Mis.detail._lastElm != popup) {
			if (Mis.detail._lastElm) {
				$(this._lastElm).fadeOut('fast');
			}
			
			$(popup).fadeIn('fast');
			
			var offset = $(elm).offset();
			popup.style.left = offset.left + 5 + 'px';
			popup.style.top = offset.top + 5 + 'px';
			
			Mis.detail._lastElm = popup;
		}		
	},
	hide: function() {
		if (this._lastElm) {
			$(this._lastElm).fadeOut('fast');
			this._lastElm = false;
		}
	}
}
Mis.init = function() {
	Mis.detail._init_();
	Mis.nav.init();
};
$(function(){
   Mis.init();
});

