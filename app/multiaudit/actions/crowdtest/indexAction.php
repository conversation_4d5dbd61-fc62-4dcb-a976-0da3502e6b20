<?php

class indexAction extends Util_BaseAction {
	public function init() {
		if (! parent::init ()) {
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_UNKOWN;
			$this->_renderJson ();
			return false;
		}
		return true;
	}
	protected function _process() {	
		if (! $this->_bolUserLogin) {
			Bingo_Log::warning ( __CLASS__ . ':The user do not login.' );
			header ( 'Location:' . 'https://passport.baidu.com/v2/?login' );
		}
		$taskId = (int)Bingo_Http_Request::get('task_id', 0);
		$isInUidList = $this->_isInUidlist($this->_intUid, $taskId);
		if(! $this->_bolIsValid && !$isInUidList) {
			Bingo_Log::warning ( __CLASS__ . ':The user do not valid.' );
			header ( 'Location:' . 'http://tieba.baidu.com/' );
		}
		$this->_arrTpl['data'] = Util_Crowdtest::$bugTypeToChs;
		$isSpecial = Bingo_Http_Request::get('isSpecial');
		if(intval($isSpecial) === 1 && $isInUidList)  { 
			$this->_strTemplate = 'questionnaire.php';

		} else  {
			$this->_strTemplate = 'publicsurvey.php';
		}

		$this->_renderView();
	}	
}
?>
