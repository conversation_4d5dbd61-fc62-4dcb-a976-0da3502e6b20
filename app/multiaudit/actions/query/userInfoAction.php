<?php
/**
 * 
 * 用户基本信息和排行榜信息
 * <AUTHOR>
 * @since 2014-11-19
 */
class userInfoAction extends Util_BaseAction {
	public function init() {
		if (! parent::init ()) {
			Bingo_Log::warning ( __CLASS__ . ':Parent init failed.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_UNKOWN;
			$this->_renderJson ();
			return false;
		}
		return true;
	}	
	protected function _process(){
		//用户是否登录
		if (!$this->_bolUserLogin) {
			Bingo_Log::warning (  __CLASS__ .' the user do not login.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_USER_NOT_LOGIN;
			return $this->_renderJson();
		}
		
		//校验是否为众审用户（培训用户和志愿者）
		if(!$this->_bolIsValid) {
			Bingo_Log::warning (  __CLASS__ .' the user not a valid user.' );
			$this->_intErrorNo = Tieba_Errcode::ERR_USER_NO_PERM;
			return $this->_renderJson();
		}
		$tbs = Bingo_Http_Request::get ( 'tbs', '' );
		if (! Tieba_Tbs::check ( $tbs, true )) {
			Bingo_Log::warning (  __CLASS__ . " tbs check fail." );
			$this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
			return $this->_renderJson();
		}	
		
		//月度积分榜
		$rankListRet = Service_User::getRankList($this->_IsPostpic);
		if($rankListRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			$this->_intErrorNo	= $rankListRet['errno'];
			return $this->_renderJson();
		}
		//个人排名信息
		if($this->_bolIsVolunteer) {
			$arrInput = array(
			'user_id' 			=> $this->_arrUser['user_id'],
			'month_score'		=> $this->_arrUser['month_score'],
			'type'       		=> $this->_IsPostpic,
			);
			$rankRet = Service_User::getRank($arrInput,$rankListRet['ret']);
			if($rankRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				$this->_intErrorNo	= $rankRet['errno'];
				return $this->_renderJson();
			}
			$this->_arrUser['rank'] = $rankRet['ret'];
		}
		$this->_arrTpl['data'] = array(
			'user' 		=> $this->_arrUser,
			'rank_list'	=> $rankListRet['ret'],
		);
		return $this->_renderJson();
	}
	
}