<?php
class canAction extends Appui_Browser_Action
{

	protected $forum_name = "";

	public function init()
	{
		$this->_arrNeedParams = array(
				'kw' => TypeConf::STRING_TYPE,
				'rs11' => TypeConf::INT_TYPE,//TODO
				'str1' => TypeConf::STRING_TYPE,
				'str2' => TypeConf::STRING_TYPE,
				);
		if(false === parent::init())
		{
			Bingo_Log::warning("fail to init vote create page");
			return false;
		}
		$this->forum_name = $this->_arrInputParam['kw'];
		if(empty($this->forum_name))
		{
			Bingo_Log::warning("forum_name is empty!");
			return false;
		}
		$this->reserved11 = $this->_arrInputParam['rs11'];
		$this->str1 = $this->_arrInputParam['str1'];
		$this->str2 = $this->_arrInputParam['str2'];
		return true;
	}
	
	public function process()
	{
		//�ж��û��Ƿ��¼
		$result = array(
				'errno'=>Tieba_Errcode::ERR_SUCCESS,//errno=0
				'can-post'=>0,//����ͶƱ
				'sign'=>"",
			       );
		if(false === $this->_bolUserLogin)
		{
			print json_encode($result);
			return ;
		}

		//�û���Ϣ
		$user = array(
				'is_login'=>$this->_bolUserLogin,
				'id'=>$this->_intUid,
				'name'=>$this->_strUname,
				'ip'=>$this->_intUip,
			     );
		Bingo_Log::debug('user_info : '. print_r($user, true));

		//��ȡ����Ϣ����Ҫ��Ϊ���õ���id�����жϰ��Ƿ����
		$forum_input = array('query_words'=>array($this->forum_name));
		$forum_info = Tieba_Service::Call('forum', 'getFidByFname', $forum_input);
		if(false === $forum_info)
		{
			Bingo_Log::warning("fail to call forum->getFidByFname!");
			$result['errno'] = Tieba_Errcode::ERR_UNKOWN;//errno=000001
			print json_encode($result);
			return ;
			
		}
		Bingo_Log::debug('forum_info : '. print_r($forum_info, true));
		if(!isset($forum_info['forum_id'][0]['has_forum_id'])
		 ||1 != $forum_info['forum_id'][0]['has_forum_id'])
		{
			Bingo_Log::warning("miss id for forum[{$this->forum_name}]");
			$result['errno'] = $forum_info['errno'];
			print json_encode($result);
			return ;
		}
		$forum = array(
				'id'=>$forum_info['forum_id'][0]['forum_id'],
				'forum_name'=>$this->forum_name,
			      );

		//��ȡvote_id
		$ret = fcrypt_hstr_2id('Baidu.Vote.2007.04.12', $this->str1);
		if(false === $ret || VoteConfig::FCRYPT_KEY_VOTE_ID != $ret[0] || $ret[1] > VoteConfig::MAX_VID || $ret[1] <= 0)//TODO
		{
			Bingo_Log::warning("err query : str1[{$this->str1}], result of fcrypt_hstr_2id is ". print_r($ret, true));
			$result['errno'] = VoteConfig::VOTESIGN_ERRNO;//errno=860
			print json_encode($result);
			return ;
		}
		Bingo_Log::debug("vote_id info is : ". print_r($ret, true));

		//���md5ֵ
		$url1 = sprintf("%s%d%s%u%u", "new_vote", $ret[1], $forum['forum_name'],$forum['id'],$this->reserved11);
		Bingo_Log::debug("url1 for check is : $url1");
		//$url1 = sprintf("%s%s%s%s", "new_vote", $ret[1], $this->id_1, $this->id_2);
		$sign1 = md5($url1);
		if(0 != strcasecmp($sign1, $this->str2))
		{
			Bingo_Log::warning("not equal between md5_1[$sign1] and md5_2[{$this->str2}]");
			$result['errno'] = VoteConfig::VOTEMD5_ERRNO; //errno=861
			print json_encode($result);
			return ;
		}

		//��ȡ���û��Ľ�ɫ
		$role_input = array(
				'forum_id'=>$forum['id'],
				'user_id'=>$user['id'],
				'user_ip'=>$user['ip'],
				);
		$role_info = Tieba_Service::Call('perm', 'getRole', $role_input);
		if(false == $role_info)
		{
			Bingo_Log::warning("fail to call perm->getRole!");
			$result['errno'] = Tieba_Errcode::ERR_UNKOWN;//errno=000001
			print json_encode($result);
			return ;
		}
		Bingo_Log::debug("role_info : ". print_r($role_info, true));
		$result['errno'] = $role_info['errno'];
		if(!isset($role_info['output']['role']) 
		 || !is_array($role_info['output']['role']))
		{
			Bingo_Log::warning("err role_info : ". print_r($role_info, true));
			print json_encode($result);
			return ;
		}
		$role_info = $role_info['output']['role'];

		//��̨����Ա��pm�أ�TODO
		$is_manager = $role_info['is_forum_pm'] //pmȨ��
			|| $role_info['is_forum_manager'] //�����Ȩ��
			|| $role_info['is_forum_assist'] //С����Ȩ��
			|| $role_info['is_forum_picadmin'] //ͼƬ����ԱȨ��
			|| $role_info['is_forum_videoadmin']; //��Ƶ����ԱȨ��
		if(VoteConfig::VOTE_MEMBER == $this->reserved11)//ֻ��member����ͶƱ//TODO
		{
			$result['can-post'] = $is_manager || $role_info['is_forum_member'] /*��Ա*/;
		}
		elseif(VoteConfig::VOTE_ADMIN == $this->reserved11)//ֻ�а�����Ա����ͶƱ//TODO
		{
			$result['can-post'] = $is_manager;
		}
		$result['can-post'] = intval($result['can-post']);

		//����ǩ��
		$url2 = sprintf("%s%u%u%u", "new_vote", $ret[1], $user['id'], $user['ip']);
		Bingo_Log::debug("url2 for sign is : $url2");
		$result['sign'] = md5($url2);
		Bingo_Log::debug("result_info : ". print_r($result, true));
		print json_encode($result);

	}


}
?>
