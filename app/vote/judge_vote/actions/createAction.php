<?php
class createAction extends Appui_Browser_Action
{
	protected $forum_name = "";

	public function init()
	{
		$this->_arrNeedParams = array(
				'kw' => TypeConf::STRING_TYPE,
				);
		if(false === parent::init())
		{
			Bingo_Log::warning("fail to init vote create page");
			return false;
		}
		$this->forum_name = $this->_arrInputParam['kw'];
		if(empty($this->forum_name))
		{
			Bingo_Log::warning("forum_name is empty!");
			return false;
		}
		$this->_objView->getScript()->setBaseDir(VIEW_PATH);
		$this->_objView->setOutputType('');
		$this->_strTemplate = 'create_vote.php';
		return true;
	}
	
	public function process()
	{
		//��ȡ�û���Ϣ
		$user = array(
				'is_login'=>$this->_bolUserLogin,
				'id'=>$this->_intUid,
				'name'=>$this->_strUname,
			    );
		Bingo_Log::debug('user_info : '. print_r($user, true));

		//�ж��û��Ƿ�Ϸ����Ƿ��¼
		if(0 == $user['is_login'] || 0 == $user['id'])
		{
			$this->_strTemplate = 'no_right.php';
			return false;
		}

		//��ȡ����Ϣ����Ҫ��Ϊ���õ���id�����жϰ��Ƿ����
		$forum_input = array('query_words'=>array($this->forum_name));
		$forum_info = Tieba_Service::Call('forum', 'getFidByFname', $forum_input);
		if(false === $forum_info)
		{
			Bingo_Log::warning("fail to call forum->getFidByFname!");
			return false;
			
		}
		Bingo_Log::debug('forum_info : '. print_r($forum_info, true));
		if(!isset($forum_info['forum_id'][0]['has_forum_id'])
		 ||1 != $forum_info['forum_id'][0]['has_forum_id'])
		{
			$this->_strTemplate = 'no_right.php';
			return false;
		}
		$forum = array(
				'id'=>$forum_info['forum_id'][0]['forum_id'],
				'forum_name'=>$this->forum_name,
			      );

		//��ȡ��Ŀ¼��Ϣ
		$forum_dir_input = array('forum_id'=>$forum['id']);
		$forum_dir_info = Tieba_Service::Call('forum', 'getForumDir', $forum_dir_input);
		if(false === $forum_dir_info)
		{
			Bingo_Log::warning("fail to call forum->getForumDir!");
			return false;
		}
		Bingo_Log::debug('forum_dir_info : '. print_r($forum_dir_info, true));
		if(isset($forum_dir_info['output']))
		{
			$catalog = $forum_dir_info['output'];
		}

		//��ȡ������Ϣ
		$manager_input = array('forum_id'=>$forum['id']);
		$manager_info = Tieba_Service::call('perm', 'getManagerList', $manager_input);
		if(false === $manager_info)
		{
			Bingo_Log::warning("fail to call perm->getManagerList!");
			return false;
		}
		Bingo_Log::debug('manager_info : '. print_r($manager_info, true));
		
		//�жϸ��û��Ƿ�Ϊ����������ȡ�����û���
		$manager = array();
		$isManager = false;
		if(isset($manager_info['output']) && is_array($manager_info['output']))
		{
			foreach($manager_info['output'] as $manager_v)
			{
				$manager[] = $manager_v['user']['user_name'];
				if($user['id'] == $manager_v['user']['user_id'])

				{
					$isManager = true;
				}
			}
		}

		//��ȡ�û�Ȩ����Ϣ
		$post_perm_input = array(
				'forum_id'=>$forum['id'],
				'user_id'=>$user['id'],
				'user_ip'=>$this->_intUip,
				);
		$post_perm_info = Tieba_Service::call('perm', 'getPerm', $post_perm_input);
		if(false === $post_perm_info)
		{
			Bingo_Log::warning("fail to call perm->getPerm!");
			return false;

		}
		Bingo_Log::debug('post_perm_info : '. print_r($post_perm_info, true));

		//�жϸ��û��Ƿ���Է���
//		if(!isset($post_perm_info['output']['perm']['can_post'])
//		 || false === $post_perm_info['output']['perm']['can_post'])
//		//if(isset($post_perm_info['output']['perm']['can_post']))
//		{
//			$this->_strTemplate = 'no_right.php';
//			return false;
//		}
		
		//�жϸ��û��Ƿ���Դ���ͶƱ
		$can_create_vote = true;
		if( !isset($post_perm_info['output']['grade']['level_id'])//�ȼ�����
		 || $post_perm_info['output']['grade']['level_id'] < 3
		 || !isset($post_perm_info['output']['perm']['can_post']) //���ܷ���
		 || false === $post_perm_info['output']['perm']['can_post'])
		{
			//$can_create_vote = false;
			$this->_strTemplate = 'no_right.php';
			return false;
		}
		$this->_arrTpl['is_can_post'] = true;
		$this->_arrTpl['manager'] = $manager;
		$this->_arrTpl['is_manager'] = $isManager;
		$this->_arrTpl['can_create_vote'] = $can_create_vote;
		$this->_arrTpl['forum'] = $forum;
		$this->_arrTpl['user'] = $user;
		$this->_arrTpl['max_option_num'] = 27; //ͶƱѡ��������
		$this->_arrTpl['catalog'] = $catalog;
		Bingo_Log::debug('_arrTpl : '. print_r($this->_arrTpl, true));
	}


}
?>
