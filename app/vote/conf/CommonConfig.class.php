<?php
/***************************************************************************
 * 
 * Copyright (c) 2009 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file CommonConfig.class.php
 * <AUTHOR>
 * @date 2009/07/08 23:32:50
 * @brief 
 *  
 **/


define ("FRAMEWORK_INIT_FALE",'bingo/common/env_init.php');
define ("LIB_VERSION_DIR","lib");

//ͳһ��֤��ɢ��½
define ('PASSPORT_SESSION_TYPE', 2);
define ('PASSPORT_CONF_PATH', '/home/<USER>/forum-php/conf/framework/');
//require_once '/home/<USER>/forum-php/framework/lib/passportsession/PassportSession.class.php';

define ("VOTE_DEF_PATH",SRC_PATH."/def");
define ("ACTION_DISPATCH_FILE",VOTE_DEF_PATH.'/ActionControllerConfig.class.php');
define ("ACTION_DISPATCH_METHOD","ActionController");
define ("VOTE_UI_PATH",SRC_PATH."/ui");
define ("VOTE_LOG_PATH",ROOT_PATH."/log/app/vote");

define ("VOTE_TEMPLATE_PATH",  SRC_PATH."/template");
define ("SMARTY_TEMPLATE_DIR", VOTE_TEMPLATE_PATH."/templates");
define ("SMARTY_COMPILE_DIR",  VOTE_TEMPLATE_PATH."/compile");
define ("SMARTY_CONFIG_DIR",   VOTE_TEMPLATE_PATH."/config");
define ("SMARTY_CACHE_DIR",    VOTE_TEMPLATE_PATH."/cache");
define ("SMARTY_PLUGIN_DIR",   VOTE_TEMPLATE_PATH."/templates/plugins");
define ("M_CURRENT_CONF","jx");

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
