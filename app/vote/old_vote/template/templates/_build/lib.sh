#!/bin/bash
func_scp(){
	local _GZ_NAME=$1
	local _SERVER=$2
	local _PATH=$3

	echo -e "\n--deploy $_GZ_NAME to $_SERVER:$_PATH"

	if [ ! $_PATH ]; then 
		echo "--ERROR !!! PATH is null, please set it"; exit 1; 
	fi
	
	
	scp $_GZ_NAME $_SERVER:~ 1> /dev/null
	ssh $_SERVER "
	if [ ! -d $_PATH ]; then
		echo -e "--Notice !!! $_PATH is not exist! create it!";
		mkdir -p $_PATH
	fi
	
	if [ -d $_PATH ]; then
		tar -xzmf ~/$_GZ_NAME -C $_PATH
		if [ -d ../compile/ ];then rm -rf ../compile/*; fi
		rm $_GZ_NAME
		echo "--deploy success!"
	fi
	"
}

#old tpl build (berg's tpl tool)
func_build_berg()                                          
(
	local curdate=tieba`date   +%Y%m%d%H%M%S`
	local _dir_path=`pwd`                    
	tar -cvvf /home/<USER>/apache/local/htdocs/template/upload/$curdate.tar . > /dev/null
	echo $curdate
	cd /home/<USER>/apache/local/htdocs/template/           
	if [ $USER != 'scmpf' ];then                          
		sh parser.sh -q $curdate.tar #-t                  
	else                                                  
		sh parser.sh $curdate.tar #-t                     
	fi                                                    
	cp upload/$curdate/ui_template.tar $_dir_path/output/ 
	cd  $_dir_path/output/                                
	tar xvvf ui_template.tar > /dev/null                  
	rm ui_template.tar                                    
	rm -rf _build

	rm -rf /home/<USER>/apache/local/htdocs/template/upload/$curdate.tar
	rm -rf /home/<USER>/apache/local/htdocs/template/upload/$curdate
	cd $_dir_path
)


#update static
func_deploy_static(){
	local _USER=$2
	local _PATH=$3
	local ST_SERVER=forum@$1
	local ST_PATH=/home/<USER>/lighttpd/htdocs/$_USER/$_PATH
	echo -e '\n----- updating static '
	if [ $_USER == 'default' ]; then
		ST_PATH=/home/<USER>/lighttpd/htdocs/$_PATH
	fi
	
	tar -czf static.tar.gz * --exclude=*.tar.gz
	func_scp static.tar.gz $ST_SERVER $ST_PATH
}

#update template
func_deploy_tpl(){
	local _USER=$3
	local _PATHS=$4
	local TPL_SERVER=$1@$2
	echo -e '\n----- updating tpl '
	if [ $_USER == 'default' ]; then
		_USER=''
	fi
	
	tar czf tpl.tar.gz * --exclude=*.tar.gz
	for temp_path in $_PATHS;
	do
		func_scp tpl.tar.gz $TPL_SERVER $temp_path/$_USER
	done
}

#restart ui
func_restart_ui(){
	echo -e '\n----- restart ui '
	local _SERVER=forum@$1
	local _shell=$2
	ssh $_SERVER "       
	$_shell
	"
}