<?php

/***************************************************************************
 * 
 * Copyright (c) 2008 Baidu.com, Inc. All Rights Reserved
 * $Id: ActionPageBuilder.class.php,v 1.30 2009/09/07 06:35:11 jipc Exp $ 
 * 
 **************************************************************************/



/**
 * @file ActionPower.class.php
 * <AUTHOR>
 * @date 2008/12/26 16:31:46
 * @version $Revision: 1.30 $ 
 * @brief ҳ��build
 *  
 **/

class ActionPageBuilder extends Action {

    /**
     * @var array
     * array('templatename', 'var1', 'var2', 'var3', ...)
     */
    private $_tpl;
    private $_var;

    /*
     * ִ�к���
     */
    public function execute($context, $actionParams = null) {

	$objSmarty = $context->getProperty(VoteInc :: KEY_SMARTY_OBJ);
	
	$status = $context->getProperty (VoteInc :: KEY_PAGE_STATUS);
	if (VoteInc :: PAGE_STATUS_NORMAL !== $status) {
		UB_LOG_WARNING (VoteInc :: VOTE_MODULE_UI, 'status invalid!statuc='.$status,__FILE__,__LINE__);
	    VoteUiFunction :: redirectUrl (VoteInc :: ERROR_PAGE_URL);
	    exit;
	}

	$this->_tpl = $context->getProperty (VoteInc :: KEY_VOTE_TPL);
	if (null === $this->_tpl) {
	    UB_LOG_WARNING (VoteInc :: VOTE_MODULE_UI,'tpl not found',__FILE__,__LINE__);
	    return false;
	}

	time_start ('page_builder');

	if($this->_tpl === 'mcpack'){
	    $rawdata = $context->getProperty(VoteInc :: KEY_JSON_DATA);
	    $newpack = mc_pack_array2pack($rawdata);
	    UB_LOG_DEBUG (VoteInc :: VOTE_MODULE_UI, "mcpack length: ".strlen($newpack), __FILE__,__LINE__);
	    echo $newpack;
	}
	else {
	    $this->_var = $context->getProperty (VoteInc :: KEY_VOTE_TPL_VAR);
	    if (null === $this->_var) {
		UB_LOG_WARNING (VoteInc :: VOTE_MODULE_UI,"template var not found, tpl ".$this->_tpl,
		    __FILE__,__LINE__);
		return false;
	    }
	    foreach ($this->_var as $var_name) {
		$var_value = $context->getProperty($var_name);
		if (null === $var_value) {
		    $strInfo = sprintf ('tpl var:%s not found tpl:%s',$var_name,$this->_tpl);
		    UB_LOG_WARNING (VoteInc :: VOTE_MODULE_UI,$strInfo,__FILE__,__LINE__);
		}
		$strCallback = $context->getProperty (VoteInc :: KEY_CALLBACK_INFO);
		if(isset($strCallback) && strlen($strCallback) > 0){
		    $var_value[VoteInc :: KEY_CALLBACK_INFO] = $strCallback;
		}
		$objSmarty->assign($var_name, $var_value);
	    }
            UB_LOG_DEBUG (VoteInc :: VOTE_MODULE_UI, "in the pagebuilder the tpl is [".$this->_tpl."]",
			 __FILE__,__LINE__);
	    $objSmarty->display($this->_tpl);
	}
	
	time_end ('page_builder');
	return true;
    }
}
?>
