<?php

class ValveInterface {
	public static $get_captcha_cmd_no = 1;
	public static $check_captcha_cmd_no = 8;
	public static $captcha_product_id = 0;
	public static $captcha_ocode_len = 4;
	
	private static function _talkWithVcode($arrInput,$method)
	{
		$arrOutput = array();
        $str = sprintf('the fvcode input');
        UB_LOG_DEBUG(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__,0,null,$arrInput);

        $arrRes= Rpc :: rpcCall(VoteInc :: SERVER_FVCODE, $method, $arrInput, $arrOutput);
		if (false === $arrRes) {
			$str = 'rpcCall fvcode action failed';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			return false;
		}
		return $arrOutput;
	}
	
	//�ɹ�������vcode�������򷵻�false
	public static function getCaptchaVcode($vote_id,$user_ip)
	{
		$input = array();
		$input['captcha_cmd_no'] = ValveInterface::$get_captcha_cmd_no;
		$input['captcha_product_id'] = ValveInterface::$captcha_product_id;
		$input['captcha_ocode_len'] = ValveInterface::$captcha_ocode_len;
		$input['captcha_appkey'] = $vote_id.$user_ip;
		$arrRes = ValveInterface::_talkWithVcode($input,'genCode');
		if ($arrRes === false)
		{
			UB_LOG_WARNING (VoteInc::VOTE_MODULE_UI, "getCaptchaVcode error.",
				 __FILE__, __LINE__);
			return false;
		}
		if ($arrRes['captcha_err_no'] !== 0) {
			$str = 'getCaptchaVcode error.reason:'.$arrRes['captcha_str_reason'];
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			return false;
		}
		return $arrRes['captcha_vcode_str'];
	}
	
	//�ɹ���true�����򷵻�false
	public static function checkCaptchaVcode($vcode_str,$vcode_input,$vote_id,$user_ip)
	{
        if (!isset($vcode_str))
            $vcode_str = "";
        if (!isset($vcode_input))
            $vcode_input = "";

        $input = array();
		$input['captcha_cmd_no'] = ValveInterface::$check_captcha_cmd_no;
		$input['captcha_submit_product_id'] = ValveInterface::$captcha_product_id;
		$input['captcha_vcode_str'] = $vcode_str;
		$input['captcha_appkey'] = $vote_id.$user_ip;
		$input['captcha_input_str'] = $vcode_input;
		
		$arrRes = ValveInterface::_talkWithVcode($input,'verify');
		if ($arrRes === false)
		{
			UB_LOG_WARNING (VoteInc::VOTE_MODULE_UI, "checkCaptchaVcode error.",
				 __FILE__, __LINE__);
			return true;//����ʱ�򣬽���ʧ�ܷ���true�����Լ��
		}
		if ($arrRes['captcha_err_no'] !== 0) {
			$str = 'getCaptchaVcode error.reason:'.$arrRes['captcha_str_reason'];
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			return false;
		}
		return true;
	}
	//����Ƿ�ﵽ��֤������ȣ��ﵽ�Ļ�������֤�봮�����򣬷���true
	public static function checkNeedVcode($user,$vote_id = 0) {
		
		$arrRes = array();
		$arrInput = array(
			'ip' => $user['uip'],
			'user_id' => $user['uid'],
			'command_no' => 1,
			'product' => 1,
			'vote_id' => $vote_id
		);
		$arrOutput = array();
		$arrRes= Rpc :: rpcCall(VoteInc :: SERVER_ACTS_CTRL_QUERY, 'query', $arrInput, $arrOutput);
		if (false === $arrRes) {
			$str = 'rpcCall acts_ctrl query action failed';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			return true;//������ʧ�ܣ���Ϊ����û�дﵽ
		}
		$str = sprintf('the acts_ctrl result');
		UB_LOG_DEBUG(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__,0,null,$arrOutput);
		
		//CHECK THE FVCODEE
		if($arrOutput['need_vcode'] !== 0 && VoteInc::OPEN_VCODE_CHECK === true)
		{
			$res = ValveInterface::getCaptchaVcode($vote_id,$user['uip']);
			if ($res === false){
				$str = 'getCaptchaVcode the vcode_str error!';
				UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
				return true;//������ʧ�ܣ���Ϊ����û�дﵽ
            }else {
		        return $res;//��Ҫ��֤��
            }
        }
        return true;//����Ҫ��֤��
    }
	
}

?>
