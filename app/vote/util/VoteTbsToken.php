<?php

class VoteTbsToken {
	
	//ͶƱ��toke����������
	private static $_voteToken = 'vote@tbs_token';
	
	//����vote��token��
	public static function genVoteToken($user){
		$user_id = $user['uid'];
		$baidu_uid = $user['cookie_id'];
		$md5_str = '';
		
		if ($user_id > 0){
			$md5_str = $user_id.VoteTbsToken::$_voteToken;
		}else {
			$md5_str = $baidu_uid.VoteTbsToken::$_voteToken;
		}
		return md5($md5_str);
		
	}
	
	//��֤vote��token��
	public static function checkVoteToken($user,$tbs){
		$user_id = $user['uid'];
		$baidu_uid = $user['cookie_id'];
		$md5_str = '';
		
		if ($user_id > 0){
			$md5_str = $user_id.VoteTbsToken::$_voteToken;
		}else {
			$md5_str = $baidu_uid.VoteTbsToken::$_voteToken;
		}
		$md5_result = md5($md5_str);
		if ($md5_result == $tbs) {
			return true;
		}
		return false;
	}
}

?>