<?php

class CmVoteConfig {
	
	public static $config = array(

	// add_vote
	'add_vote'		=> array(
		'class' => 'CmVote', 
		'hander' => 'newVote', 
		'post_need' => array('title', 'expire_time', 'item_type', 'max_select_num', 
						'perm'
		),
		'get_need' => array(),
		'crypt_need' => array(),
		'power' => array(),
	),
	//add_vote_relation
	'add_vote_relation'		=> array(
		'class' => 'CmVote', 
		'hander' => 'newRelation', 
		'post_need' => array('product_name', 'id_1', 'id_2', 'vote_id', 'sign_id'),
		'get_need' => array(),
		'crypt_need' => array(),
		'power' => array(),
		),
	//post_vote
	'post_vote'		=> array(
		'class' => 'CmVote', 
		'hander' => 'addVote', 
		'post_need' => array('vote_id', 'select_items', 'sign_id'),
		'get_need' => array(),
		'crypt_need' => array(),
		'power' => array(),
		),
	);
	
	
}
?>
