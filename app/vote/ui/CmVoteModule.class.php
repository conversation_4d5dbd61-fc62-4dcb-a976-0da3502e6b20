<?php
/***************************************************************************
 * 
 * Copyright (c) 2009 Baidu.com, Inc. All Rights Reserved
 * $Id: CmVoteModule.class.php,v 1.97 2010/01/25 07:26:20 jipc Exp $ 
 * 
 **************************************************************************/
 
/**
 * @file CmVoteModule.class.php
 * <AUTHOR>
 * @date 2009/01/07 11:46:49
 * @version $Revision: 1.97 $ 
 * @brief 
 *  
 **/

class CmVote{
	private static $_voteInfo;
	// confilter dict group
    const CGROUP_STRONG = 10;
    const CGROUP_LATER_AUDIT = 11;
    const CGROUP_WHITE_URL = 12;
    
    const CRESULT_OK = 0;
    const CRESULT_FILTER = 1;
    const CRESULT_LATER_AUDIT = 2;
    const CRESULT_URL_FILTER = 3;
    const CRESULT_ERROR = 4;
    
    const MIS_LATER_AUDIT_TYPE_TEXT = 1;
    const MIS_LATER_AUDIT_TYPE_IMG = 2;
    
    const ACTS_CTRL_NEW_VOTE_COMMAND_NO = 0;
    const ACTS_CTRL_ADD_VOTE_COMMAND_NO = 1;
    const ACTS_CTRL_NEW_RELATION_COMMAND_NO = 2;
	
	public static function getVoteId($vote_id_sign) {
		
		$coder = fcrypt_hstr_2id(VoteInc :: FCRYPT_KEY_VOTE, $vote_id_sign);
		if (false === $coder || $coder[0] != VoteInc::FCRYPT_KEY_VOTE_ID ) {
			$str = 'check the vote_id_sign error.';
			echo 'check the vote_id_sign error.';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['common']['CLIENT_ERR'];
			return $arrRes;
		} else {
			echo " the vote_id is $coder[1]";
		}
	}
	
	public static function newRelation($user, $post) {
		#self::getVoteId('c43c787d673576bce7153b13');
		//check is_login
		$str = sprintf('User Status:the user status is uid[%d] uname[%s]',
			$user['uid'],$user['un']);
		UB_LOG_DEBUG(VoteInc::VOTE_MODULE_UI,$str,__FILE__,__LINE__);
		if (true !== $user['is_login'])
		{
			$str = sprintf('the user is not logined,while newRelation.');
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['vote']['NO_LOGIN'];
			return $arrRes;
		}
		$arrRes = array();
		//trim all the post data
		foreach ($post as $key => $value) {
			$post[$key] = trim($value);
		}
		
		//check product_name
		$strProductName = mb_strtoupper($post['product_name']);
		if(isset(VoteInc:: $product_name[$strProductName]) === false){
			$str = sprintf('the product_name is wrong,[%s] while newRelation.',$strProductName);
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['vote_product_name']['NO_EXSIT'];
			return $arrRes;
		}
		$strProductName = VoteInc :: $product_name[$strProductName];
		$str = sprintf('newRelation: the product_name [%s]',$strProductName);
		UB_LOG_DEBUG(VoteInc :: VOTE_MODULE_UI, $str, __FILE__, __LINE__);
		//check vote_id
		$vote_id_sign = $post['vote_id'];
		$coder = fcrypt_hstr_2id(VoteInc :: FCRYPT_KEY_VOTE, $vote_id_sign);
		if (false === $coder || $coder[0] != VoteInc::FCRYPT_KEY_VOTE_ID ) {
			$str = 'check the vote_id_sign error.';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['common']['CLIENT_ERR'];
			return $arrRes;
		} else {
			$post['vote_id'] = $coder[1];
		}
		
		//check sign_id
		$strFcryptPrefix = VoteInc :: MD5_KEY_VOTE;
		$strTmp = sprintf("%s%s%s%s", $strFcryptPrefix, $post['vote_id'], $post['id_1'], $post['id_2']);
		$strMd5Sign = md5($strTmp);

		if($strMd5Sign !== $post['sign_id']){
			$str = sprintf('check the sign error.strMd5Sign[%s] post.sign[%s]',
				$strMd5Sign,$post['sign_id']);
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['common']['CLIENT_ERR'];
			return $arrRes;
		}
		//set id_2
		$id_2 = 0;
		if ($strProductName === VoteInc :: $product_name['FORUM']) {
			$id_2 = 0;
		}else {
			$id_2 = $post['id_2'];
		}
		
		//���ȿ���
		if ( false === self::checkAnti($user,self::ACTS_CTRL_NEW_RELATION_COMMAND_NO,$post['vote_id'])) {
			$str = sprintf('can not pass the acts_ctrl while newRelation');
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_ACT_ANTI'];
			$arrRes['errmsg'] = VoteInc :: $error['vote']['FREQ'];
			return $arrRes;
		}
		// get vote_info add for the itieba
		$vote_info = self::getVoteInfo($post['vote_id']);
		if (false === $vote_info) {
			$str = 'getVoteInfo error while addVote.';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			//header(VoteInc :: SERVER_ERR_HEADER_STATUS);
			//$arrRes = VoteInc::$errno['HTTP_VOTE_SERVICE'];
			//$arrRes['errmsg'] = VoteInc :: $error['common']['SERVER_ERR'];
			//return $arrRes;
		//�޸ģ����û�в�ѯ����Ϣ���Ͳ�Ϊ����ϵͳ�ṩ��Ϣ��
			$vote_info['title'] = '';
			$vote_info['commit_uname'] = '';
			$vote_info['commit_uid'] = 0;
					}
		$forum_name = '';
		foreach ($vote_info['vote_properties'] as $attr) {
			if ($attr['property_key'] === 'forum_name') {
				$forum_name = $attr['property_value'];
				break;
			}
		}
		//talk with the rpc
		$arrInput = array(
			'product_id' => VoteInc :: $product_map[$strProductName],
			'vote_id' => $post['vote_id'],
			'id_1' => $post['id_1'],
			'id_2' => $id_2,
			'op_time'  => time(),
			//add for the itieba
			'itieba_id' => $post['id_2'],//forum_id
			'thread_id' => $post['id_1'],//thread_id
			'post_id' => 0,
			'title' => $vote_info['title'],
			'user_name' => $vote_info['commit_uname'],
			'user_id' => $vote_info['commit_uid'],
			'forum_name' => $forum_name,
		);
		$arrOutput = array();
		$bolRes = Rpc :: rpcCall(VoteInc :: SERVER_VOTE_DAL, 'newRelation', $arrInput, $arrOutput);
		if (false === $bolRes) {
			$str = 'rpcCall add new relation failed';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_RPC, $str, __FILE__,__LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_SERVICE'];
			$arrRes['errmsg'] = VoteInc:: $error['common']['SERVER_ERR'];
			return $arrRes;
		}
		$arrResult = array();
		$arrResult['errno'] = VoteInc::$errno['HTTP_VOTE_SUCCESS']['errno'];
		$arrResult['error'] = VoteInc::$errno['HTTP_VOTE_SUCCESS']['error'];
		return $arrResult;
	}

	public static function newVote($user, $post) {
		
		//check is_login
		$str = sprintf('User Status:the user status is uid[%d] uname[%s]',
			$user['uid'],$user['un']);
		UB_LOG_DEBUG(VoteInc::VOTE_MODULE_UI,$str,__FILE__,__LINE__);
		if (true !== $user['is_login'])
		{
			$str = sprintf('the user is not logined,while newVote.');
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['vote']['NO_LOGIN'];
			return $arrRes;
		}
		
		$arrRes = array();
		//trim all the post data
		foreach ($post as $key => $value) {
			$post[$key] = trim($value);
		}
		
		//get item and atr data
		$item = array();
		for ($i = 1; $i <= VoteInc::VOTE_MAX_SELECT_ITEM; $i++) {
			$item_title = 'item_title_'.$i;
			$item_content = 'item_content_'.$i;
			if (isset($post[$item_title])) {
				$item[$i]['title'] = $post[$item_title];
				$item[$i]['content'] = $post[$item_content];
			}
		}
		$attr = array();
		for ($i = 1; $i <= VoteInc::VOTE_MAX_ITEM_NUM; $i++) {
			$attr_key = 'attr_key_'.$i;
			$attr_value = 'attr_value_'.$i;
			if (isset($post[$attr_key])) {
				$attr[$i]['key'] = $post[$attr_key];
				$attr[$i]['value'] = $post[$attr_value];
			}
		}		
		$post['item'] = $item;
		$post['attr'] = $attr;
		$str = sprintf('the num of item is [%d] attr is [%d]',count($post['item'])
			,count($post['attr']));
		UB_LOG_DEBUG(VoteInc::VOTE_MODULE_UI,$str,__FILE__,__LINE__);
				
		//check product name
		if(!isset($post['product_name'])) {
			$str = sprintf('the product_name is NULL,while newVote.');
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['vote_product_name']['EMPTY'];
			return $arrRes;
		}
		$product_name = mb_strtoupper($post['product_name']);
		if (!isset(VoteInc::$product_name[$product_name])) {
			$str = sprintf('the product_name is wrong,[%s] while newVote.',$product_name);
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['vote_product_name']['NO_EXSIT'];
			return $arrRes;
		}
		$product_name = VoteInc::$product_name[$product_name];
		//check product_id for product_name
		if (!isset(VoteInc::$product_map[$product_name])) {
			$str = sprintf('not find the product_id for the product_name[%s], while newVote.',$product_name);
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['vote_product_id']['NO_EXSIT'];
			return $arrRes;
		}
		$product_id = VoteInc::$product_map[$product_name];
		//check title
		if (strlen($post['title']) > VoteInc:: VOTE_MAX_TITLE_LEN) {
			// title too long
			$str = sprintf('In newVote, the vote title[%s] len[%d] larger than VOTE_MAX_TITLE_LEN[%d]', 
				$post['title'], strlen($post['title']), VoteInc::VOTE_MAX_TITLE_LEN);
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['vote_title']['TOO_LONG'];
			return $arrRes;
		}
		else if (strlen($post['title']) <= 0) {
			// title empty
			$str = sprintf('in newVote, the vote title[%s] len[%d] is 0.', 
				$post['title'], strlen($post['title']));
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['vote_title']['EMPTY'];
			return $arrRes;
		}
		$bolRes = gbk_is_valid_graphstr($post['title']);
		if (true !== $bolRes) {
			$str = sprintf('Unusual title (gbk) while newVote [name:%s]', $post['title']);
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['vote_title']['UNUSUAL'];
			return $arrRes;
		}
		
		// check content
		if (isset($post['content']) && strlen($post['content']) >  VoteInc :: VOTE_MAX_CONTENT_LEN) {
			// content too long
			$str = sprintf('too long content while newVote [content:%s]', $post['content']);
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc:: $error['vote_content']['TOO_LONG'];
			return $arrRes;
		}
		if (!isset($post['content'])) {
			$post['content'] = '';
		}
		
		$bolRes = gbk_is_valid_graphstr($post['content']);
		if (true !== $bolRes) {
			$str = sprintf('Unusual content (gbk) while newVote [content:%s]', $post['content']);
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['vote_content']['UNUSUAL'];
			return $arrRes;
		}
		//check show_result
		if (!isset($post['show_result'])) {
			$post['show_result'] = VoteInc :: $show_result['show'];	
		}
		//check expire_time
		$cur_time = time();
		$expire_time = strtotime($post['expire_time']);
		if (intval($expire_time) <= $cur_time) {
			$str = sprintf('the expire_time is wrong. exprie_time[%s] cur_time[%s]', 
				$expire_time,$cur_time);
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc:: $error['vote_expire_time']['TOO_SHORT'];
			return $arrRes;
		}
		$str = sprintf('the expire_time is [%d]',$expire_time);
		UB_LOG_DEBUG(VoteInc::VOTE_MODULE_UI,$str,__FILE__,__LINE__);
		
		//check item_type
		$check_item_type = false;
		foreach (VoteInc::$item_type as $key => $value) {
			if ($value === intval($post['item_type'])) {
				$check_item_type = true;
				break;
			}
		}
		if (!$check_item_type) {
			$str = sprintf('the item_type is wrong while newVote. item_type[%d]',
				$post['item_type']);
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc:: $error['vote_item_type']['NO_EXSIT'];
			return $arrRes;
		}
		//check max_select_num
		$max_select_item = 0;
		if (isset($post['max_select_num'])) {
			$max_select_item = intval($post['max_select_num']);
			if ($max_select_item > VoteInc :: VOTE_MAX_SELECT_ITEM) {
				$max_select_item = VoteInc :: VOTE_MAX_SELECT_ITEM;
			}
		} else {
			$max_select_item = 1;
		}
		//check perm
		$check_perm = false;
		foreach (VoteInc::$perm as $key => $value) {
			if ($value === intval($post['perm'])) {
				$check_perm = true;
				break;
			}
		}
		if (!$check_perm) {
			$str = sprintf('the perm is wrong while newVote. perm[%d]',
				$post['perm']);
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc:: $error['vote_perm']['ERROR'];
			return $arrRes;
		}
		//check item
		$new_vote_items = array();
		$str = sprintf('the vote_post_item_num[%d]',count($post['item']));
		UB_LOG_DEBUG(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
		if (count($post['item']) > 0) {
			foreach ($post['item'] as $item) {
				if (strlen($item['title']) >  VoteInc:: VOTE_MAX_ITEM_TITLE_LEN) {
					// item too long
					$str = sprintf('too long while newVote item.title.len[%d]',strlen($item['title']));
					UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
					$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
					$arrRes['errmsg'] = VoteInc :: $error['vote_item']['TOO_LONG'];
					return $arrRes;
				}
				$bolRes = gbk_is_valid_graphstr($item['title']);
				$str = sprintf('##### test the item_title [name:%s]', $item['title']);
				UB_LOG_DEBUG(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
				if (true !== $bolRes) {
					$str = sprintf('Unusual item_title (gbk) while newVote [name:%s]', $item['title']);
					UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
					$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
					$arrRes['errmsg'] = VoteInc :: $error['vote_item']['UNUSUAL'];
					return $arrRes;
				}
				if (isset($item['content']) && strlen($item['content']) > 0) {
					if (!VoteUiFunction :: isImgUrl($item['content'])) {
						//the content is not the img url
						$str = sprintf('the item[content] is not img url while newVote item.content[%s]',
							$item['content']);
						UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
						$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
						$arrRes['errmsg'] = VoteInc :: $error['vote_item']['NOT_IMG_URL'];
						return $arrRes;						
					}
				}
				else {
					$item['content'] = '';
				}
				if (strlen($item['title']) > 0) {
					$new_vote_items[] = $item;
					$str = sprintf('##### test the item_content [name:%s]', $item['content']);
					UB_LOG_DEBUG(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
				}
			}
		}
		if (count($new_vote_items) < 2) {
			$str = sprintf('too few item (<2) while newVote [item] num[%d]',count($new_vote_items));
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['vote_item']['TOO_FEW'];
			return $arrRes;			
		}
		if (count($new_vote_items) > VoteInc:: VOTE_MAX_ITEM_NUM) {
			$str = sprintf('too more item  while newVote [item] num[%d]',count($new_vote_items));
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['vote_item']['TOO_MUCH'];
			return $arrRes;	
		}
		$post['item'] = $new_vote_items;
		//check attr
		$new_vote_attrs = array();
		if (count($post['attr']) > 0) {
			foreach ($post['attr'] as $attr) {
				if (strlen($attr['key']) >  VoteInc:: VOTE_MAX_ATTR_KEY_LEN) {
					// attr key too long
					$str = sprintf('too long while newVote attr.key.len[%d]',strlen($attr['key']));
					UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
					$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
					$arrRes['errmsg'] = VoteInc :: $error['vote_attr']['TOO_LONG'];
					return $arrRes;
				}
				if (strlen($attr['value']) > VoteInc::VOTE_MAX_ATTR_VALUE_LEN) {
					// attr value too long
					$str = sprintf('too long while newVote attr.value.len[%d]',strlen($attr['value']));
					UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
					$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
					$arrRes['errmsg'] = VoteInc :: $error['vote_attr']['TOO_LONG'];
					return $arrRes;
				}
				if (strlen($attr['key']) > 0 && strlen($attr['value']) > 0) {
					$new_vote_attrs[] = $attr;
				}
			}
		}
		if (count($new_vote_attrs) > VoteInc:: VOTE_MAX_ATTR_NUM) {
			$str = sprintf('too more attrs  while newVote [attrs] num[%d]',count($new_vote_attrs));
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['vote_attr']['TOO_MUCH'];
			return $arrRes;
		}
		$post['attr'] = $new_vote_attrs;
		
		//����Ƿ��ܹ��½�ͶƱ���Ƿ�ﵽ����
		$bolRes = self::checkCanNewVote($user['uid']);
		if (false === $bolRes) {
			$str = sprintf('can not new post while newVote');
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['vote']['TOO_MUCH'];
			return $arrRes;
		}
		//���ݹ���
		$errInfo = array();
		$resConfilter = self ::checkConfilter($post,$errInfo);
		if (self::CRESULT_FILTER === $resConfilter) {
			$str = sprintf('has some strong filter word while newVote');
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_CNT_ANTI'];
			foreach ($errInfo as $errIndex) {
				$arrRes['errmsg'][] = array($errIndex+1,VoteInc :: $error['vote']['FILTER']);
			}
			return $arrRes;
		}
		//���ȿ���
		if ( false === self::checkAnti($user,self::ACTS_CTRL_NEW_VOTE_COMMAND_NO)) {
			$str = sprintf('can not pass the acts_ctrl while newVote');
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_ACT_ANTI'];
			$arrRes['errmsg'] = VoteInc :: $error['vote']['FREQ'];
			return $arrRes;
		}

		//�����itieba��ͶƱ��ǿ��Ϊ��¼�û���Ͷ
		if($product_id === VoteInc :: $product_map['itieba']){
			$post['perm'] = VoteInc :: $perm['LOGIN_USER'];
		}

		//�ͺ��ģ�齻��
		$arrInput = array (
			'product_id' => $product_id,
			'title' => $post['title'],
			'content' => $post['content'],
			'item_type' => $post['item_type'],
			'show_result' => $post['show_result'],
			'commit_time' => time(),
			'expire_time' => intval($expire_time),
			'commit_ip' => $user['uip'],
			'commit_uid' => $user['uid'],
			'commit_uname' => $user['un'],
			'max_select_num' => $max_select_item,
			'perm' => intval($post['perm']),
			'items' => $new_vote_items,
			'properties' => $new_vote_attrs,
			
		);
		// talk with rpc, get the result
		$arrOutput = array();
		$bolRes = Rpc :: rpcCall (VoteInc :: SERVER_VOTE_DAL,'newVote', $arrInput, $arrOutput);
		if (false === $bolRes) {
			$str = 'rpcCall Vote fail while newVote';
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			header(VoteInc :: SERVER_ERR_HEADER_STATUS);
			$arrRes = VoteInc::$errno['HTTP_VOTE_SERVICE'];
			$arrRes['errmsg'] = VoteInc:: $error['common']['SERVER_ERR'];
			return $arrRes;
		}
		//make up the vote_id
		if (!isset($arrOutput['vote_id'])){
			$str = 'rpcCall not exsit the vote_id while newVote';
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_SERVICE'];
			$arrRes['errmsg'] = VoteInc:: $error['common']['SERVER_ERR'];
			return $arrRes;
		}
		$vote_id = $arrOutput['vote_id'];
		$arrOutput['vote_id'] = fcrypt_id_2hstr(VoteInc :: FCRYPT_KEY_VOTE, VoteInc::FCRYPT_KEY_VOTE_ID, $vote_id);
		
		//talk with mis
		self::checkLaterAudit($resConfilter,$post,$user,$vote_id);
		
		//make up the sign_id
		$strFcryptPrefix = VoteInc::MD5_KEY_VOTE;
		$strTmp = sprintf("%s%s%s%s", $strFcryptPrefix, $vote_id, 
			$new_vote_attrs[0]['value'],$new_vote_attrs[1]['value']);
		$strMd5Sign = md5($strTmp);
		$arrOutput['sign_id'] = $strMd5Sign;
		
		$str = sprintf('after new_vote, the md5_string[%s] md5_result[%s]',$strTmp,$strMd5Sign);
		UB_LOG_DEBUG(VoteInc :: VOTE_MODULE_UI,$str,__FILE__,__LINE__);

		header(VoteInc :: OK_HEADER_STATUS);
		$arrOutput['errno'] = VoteInc::$errno['HTTP_VOTE_SUCCESS']['errno'];
		$arrOutput['error'] = VoteInc::$errno['HTTP_VOTE_SUCCESS']['error'];
		return $arrOutput;

	}
	
	//����confilter�ķ���ֵ��ȷ���ȷ��������
	private static function checkLaterAudit($resConfilter,$post,$user,$vote_id) {
		if ($resConfilter === self::CRESULT_OK) {
			$str = sprintf('The confilter result is OK,no need to later_audit');
			UB_LOG_DEBUG(VoteInc :: VOTE_MODULE_UI,$str,__FILE__,__LINE__);
			return true;
		}
		$later_audit_type = 0;
		if ($resConfilter === self::CRESULT_LATER_AUDIT) {
			$later_audit_type = self::MIS_LATER_AUDIT_TYPE_TEXT;
		}else if ($resConfilter === self::CRESULT_URL_FILTER) {
			$later_audit_type = self::MIS_LATER_AUDIT_TYPE_IMG;
		}else {
			$later_audit_type = self::MIS_LATER_AUDIT_TYPE_TEXT;
		}
		
		$arrInput = array (
			'vote_id' => $vote_id,
			'title' => $post['title'],
			'content' => $post['content'],
			'type' => $later_audit_type,
			'commit_time' => time(),
			'commit_ip' => $user['uip'],
			'commit_uid' => $user['uid'],
			'commit_uname' => $user['un'],
		);
		$bolRes = Rpc :: rpcCall (VoteInc :: SERVER_VOTE_MIS_DAL,'addMisVote', $arrInput, $arrOutput);
		if (false === $bolRes) {
			$str = sprintf('talk with mis error.later_audit_type[%d]',$later_audit_type);
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI,$str,__FILE__,__LINE__);
			return false;
		}
		$str = sprintf('talk with mis success.later_audit_type[%d]',$later_audit_type);
		UB_LOG_DEBUG(VoteInc :: VOTE_MODULE_UI,$str,__FILE__,__LINE__);
		return true;
	}
	/*
	 * ��confilter�������ж��û������������Ƿ��ǹ��˴�
	 */
	private static function checkConfilter($post, &$errInfo) {
		
		$textGroup = array(self::CGROUP_STRONG,self::CGROUP_LATER_AUDIT);
		$urlGroup = array(self::CGROUP_WHITE_URL);
		$filterInfo = array();
		$filterIndex = array();
		$textNum = 0;
		$urlNum = 0;
		//fill the req 
		$filterIndex['content'] = $post['title'];
		$filterIndex['group'] = $textGroup;
		$filterInfo[] = $filterIndex;
		$textNum ++;
		$filterIndex['content'] = $post['content'];
		$filterIndex['group'] = $textGroup;
		$filterInfo[] = $filterIndex;
		$textNum ++;
		foreach ($post['item'] as $item) {
			$filterIndex['content'] = $item['title'];
			$filterIndex['group'] = $textGroup;
			$filterInfo[] = $filterIndex;
			$textNum ++;
		}
		foreach ($post['item'] as $item) {
			if (strlen($item['content']) > 0) {
				$filterIndex['content'] = $item['content'];
				$filterIndex['group'] = $urlGroup;
				$filterInfo[] = $filterIndex;
				$urlNum ++;
			}
		}
		//talk with the confilter
		$arrResult = AntiLib :: filterContentExt ($filterInfo);
		if (false == $arrResult) {
			$str = sprintf('Talk with the confilter error!!');
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI,$str,__FILE__,__LINE__);
			return true;//����ʧ�ܣ���Ҫ����ͨ������
		}
		//UB_LOG_DEBUG(VoteInc :: VOTE_MODULE_UI,'## confilter_req ##',__FILE__,__LINE__,NULL,0,$filterInfo);
		//UB_LOG_DEBUG(VoteInc :: VOTE_MODULE_UI,'## confilter_result ##',__FILE__,__LINE__,NULL,0,$arrResult);

		//find the filter result
		if ($urlNum + $textNum !== count($arrResult)) {
			$str = sprintf('The confilter result num[%d] not equal textNum[%d]+urlNum[%d]',
				count($arrResult),$textNum,$urlNum);
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI,$str,__FILE__,__LINE__);
			return self :: CRESULT_ERROR;
		}
		$resultIndex = 0;
		$resStrong = 0;
		$resLaterAudit = 0;
		$resNotWhiteUrl = 0;
		foreach ($arrResult as $itemResult) {
			if ($resultIndex < $textNum) {
				if ($itemResult['spaminfo'][self::CGROUP_STRONG]['num'] > 0) {
					$str = sprintf('the content[%s] is the strong_filter_word',$itemResult['content']);
					UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI,$str,__FILE__,__LINE__);
					$resStrong ++;
					$errInfo[] = $resultIndex;
				}
				if ($itemResult['spaminfo'][self::CGROUP_LATER_AUDIT]['num'] > 0) {
					$str = sprintf('the content[%s] is the later_audit_filter_word',$itemResult['content']);
					UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI,$str,__FILE__,__LINE__);
					$resLaterAudit ++;
				}
				$resultIndex ++;
			}else {
				if ($itemResult['spaminfo'][self::CGROUP_WHITE_URL]['num'] > 0) {
					$str = sprintf('the content[%s] is the whilte url',$itemResult['content']);
					UB_LOG_DEBUG(VoteInc :: VOTE_MODULE_UI,$str,__FILE__,__LINE__);
				}else {
					$str = sprintf('the content[%s] is not the whilte url',$itemResult['content']);
					UB_LOG_DEBUG(VoteInc :: VOTE_MODULE_UI,$str,__FILE__,__LINE__);
					$resNotWhiteUrl ++;
				}
			}
		}
		if ($resStrong > 0){
			return self::CRESULT_FILTER;
		}else if ($resLaterAudit > 0) {
			return self::CRESULT_LATER_AUDIT;
		}else if ($resNotWhiteUrl > 0) {
			return self::CRESULT_URL_FILTER;
		}else {
			return self::CRESULT_OK; 
		}
	}
	/*
	 * ��acts_ctrl�������ж��û��Ƿ�ﵽ���ȿ���
	 */
	private static function checkAnti($user,$command_no,$vote_id = 0) {
		
		$arrInput = array(
			'ip' => $user['uip'],
			'user_id' => $user['uid'],
			'command_no' => $command_no,
			'product' => 1,
			'vote_id' => $vote_id
		);
		$arrOutput = array();
		$arrRes= Rpc :: rpcCall(VoteInc :: SERVER_ACTS_CTRL, 'check', $arrInput, $arrOutput);
		if (false === $arrRes) {
			$str = 'rpcCall acts_ctrl check action failed';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			return true;//������ʧ�ܣ���Ϊ����û�дﵽ
		}
		if ($arrOutput['err_no'] !== 0) {
			$str = 'rpcCall acts_ctrl check action failed';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			return false;
		}
		$str = sprintf('the acts_ctrl result');
		UB_LOG_DEBUG(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__,0,null,$arrOutput);
		return true;
	}
/*
	 * ��acts_ctrl�������ж��û��Ƿ�ﵽ���ȿ��ƣ��Լ���֤��
	 */
	private static function checkAntiVcode($user,$command_no,$vcode_str,$vcode_input,$vote_id = 0) {
		
		$arrRes = array();
		$arrInput = array(
			'ip' => $user['uip'],
			'user_id' => $user['uid'],
			'command_no' => $command_no,
			'product' => 1,
			'vote_id' => $vote_id
		);
		$arrOutput = array();
		$arrRes= Rpc :: rpcCall(VoteInc :: SERVER_ACTS_CTRL, 'check', $arrInput, $arrOutput);
		if (false === $arrRes) {
			$str = 'rpcCall acts_ctrl check action failed';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			return true;//������ʧ�ܣ���Ϊ����û�дﵽ
		}
		//acts-ctrol ʧ��
		if ($arrOutput['err_no'] !== 0) {
			$str = 'rpcCall acts_ctrl check action failed';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_ACT_ANTI'];
			$arrRes['errmsg'] = VoteInc :: $error['vote']['ADD_VOTE_ANTI'];
			return $arrRes;
		}
		$str = sprintf('the acts_ctrl result');
		UB_LOG_DEBUG(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__,0,null,$arrOutput);
		
		//CHECK THE FVCODEE
		if($arrOutput['need_vcode'] !== 0 && VoteInc::OPEN_VCODE_CHECK === true)
		{
			$bolRes = ValveInterface::checkCaptchaVcode($vcode_str,$vcode_input,
				$vote_id,$user['uip']);
			if ($bolRes === false){
				$str = 'check the vcode error!';
				UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
				$arrRes = VoteInc::$errno['HTTP_VOTE_VCODE_ERROR'];
				$arrRes['errmsg'] = VoteInc :: $error['vote']['ADD_VOTE_VCODE'];
				return $arrRes;
			} 
		}
		return true;
	}
	/*
	 * ����û������½�ͶƱ�Ĵ����Ƿ�ﵽϵͳ���õ�����
	 */
	private static function checkCanNewVote($user_id) {
		
		$arrInput = array (
			'uid'  =>  $user_id,
			'begin' => strtotime(date('Y-m-d')),
			'end' => time(),
		);
		$arrOutput = array();
		$create_num = Rpc :: rpcCall(VoteInc :: SERVER_VOTE_DAL, 'getUserCreateNum', $arrInput, $arrOutput);
		if (false === $create_num) {
			$str = 'rpcCall getUserCreateNum failed';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			return false;
		}
		$create_num = $arrOutput; 
		//get global conf
		$arrInput = array();
		$arrOutput = array();
		$arrRes= Rpc :: rpcCall(VoteInc :: SERVER_VOTE_DAL, 'getGlobalControl', $arrInput, $arrOutput);
		$str = sprintf('the user global creat vote num [%d] has create[%d]',
			$arrOutput['global_create_num'],$create_num);
		UB_LOG_DEBUG(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
		if (intval($create_num) >= intval($arrOutput['global_create_num'])) {
			$str = sprintf('the user[%d] has create_num[%d] more than[%d]',$user_id,
				$create_num,$arrOutput['global_create_num']);
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			return false;
		}
		
		return true;
	}
	/*
	 * �õ�ͶƱ����ϸ��Ϣ
	 */
	private static function getVoteInfo($intVoteID) {
		
		$arrInput = array (
			'vote_id'  =>  $intVoteID
		);
		$arrOutput = array();
		$bolRes = Rpc :: rpcCall(VoteInc :: SERVER_VOTE_DAL, 'getVoteDetail', $arrInput, $arrOutput);
		if (false === $bolRes) {
			$str = 'rpcCall getVoteDetail failed';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			return false;
		}
		
		return $arrOutput;
	}
	/*
	 * add for the itieba 
	 * �õ�ͶƱ�Ĺ�����ϵ
	 */
	private static function getVoteRelation($intVoteID) {
		
		$arrInput = array (
			'vote_id'  =>  $intVoteID
		);
		$arrOutput = array();
		$bolRes = Rpc :: rpcCall(VoteInc :: SERVER_VOTE_DAL, 'getRelationById', $arrInput, $arrOutput);
		if (false === $bolRes) {
			$str = 'rpcCall getRelationById failed';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			return false;
		}
		
		return $arrOutput;
	}
	
	private static function checkCanAddVote($user, $vote_info) {

		$arrInput = array (
			'perm' => $vote_info['perm'],
			'vote_id'  =>  $vote_info['vote_id'],
			'cookie_id' => $user['cookie_id'],
			'post_ip' => $user['uip'],
			'post_uid' => $user['uid'],
			'cookie_limit' => $vote_info['cookie_limit'],
			'ip_limit' => $vote_info['ip_limit'],
			'user_limit' => $vote_info['user_limit'],
		);
		$intRet = 0;
		$bolRes = Rpc :: rpcCall(VoteInc :: SERVER_VOTE_DAL, 'checkIsVotable', $arrInput, $intRet);
		if (true === $bolRes && $intRet !== VoteInc :: $vote_refuse['ok']) {
			$str = 'rpcCall checkIsVotable failed';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			return false;
		}
		return true;
	}
	
	/*
	 * �û�ͶƱ
	 */
	public static function addVote($user, $post) {
		
		$arrRes = array();
		//trim all the post data
		foreach ($post as $key => $value) {
			$post[$key] = trim($value);
		}
		//check product id
		if (!isset($post['product_id'])) {
			$str = sprintf('not find the product_id , while addVote.');
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['vote_product_id']['NO_EXSIT'];
			return $arrRes;
		}
		$product_id = $post['product_id'];
		$str = sprintf('while addVote, product_id[%d]',
			$product_id);
		UB_LOG_DEBUG(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);


		//check vote_id
		$vote_id_sign = $post['vote_id'];
		$coder = fcrypt_hstr_2id(VoteInc :: FCRYPT_KEY_VOTE, $vote_id_sign);
		if (false === $coder || $coder[0] != VoteInc::FCRYPT_KEY_VOTE_ID ) {
			$str = 'check the vote_id_sign error.';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['common']['CLIENT_ERR'];
			return $arrRes;
		} else {
			$post['vote_id'] = $coder[1];
		}
		//check the tbs token
		if (false === VoteTbsToken::checkVoteToken($user,$post['tbs']) && 
			VoteInc::OPEN_TBS_CHECK === true){
				$str = 'check the tbs token error.';
				UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
				$arrRes = VoteInc::$errno['HTTP_VOTE_TBS_ERROR'];
				$arrRes['errmsg'] = VoteInc :: $error['vote']['CHECK_VOTE_TBS'];
				return $arrRes;
			}
		// get vote_info
		$vote_info = self::getVoteInfo($post['vote_id']);
		if (false === $vote_info) {
			$str = 'getVoteInfo error while addVote.';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			header(VoteInc :: SERVER_ERR_HEADER_STATUS);
			$arrRes = VoteInc::$errno['HTTP_VOTE_SERVICE'];
			$arrRes['errmsg'] = VoteInc :: $error['common']['SERVER_ERR'];
			return $arrRes;
		}
		
		//check can add vote

		$can_add_vote = self::checkCanAddVote($user, $vote_info);
		//$can_add_vote = $vote_info['is_votable'];
		if (false === $can_add_vote) {
			$str = 'checkCanAddVote error while addVote.';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['vote']['ONCE'];
			return $arrRes;
		}

		//check sign_id
		if(intval($vote_info['perm']) === VoteInc::$perm['POWER_USER']){
			$strFcryptPrefix = VoteInc::MD5_KEY_VOTE;
			$strTmp = sprintf("%s%s%s%s", $strFcryptPrefix, $post['vote_id'], $user['uid'],$user['uip']);
			$strMd5Sign = md5($strTmp);
			$str = sprintf('while addVote, strMd5Sign[%s] md5_str[%s]',
					$strMd5Sign,$strTmp);
			UB_LOG_DEBUG(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			
			//if($strMd5Sign !== $post['sign_id']){
			if(strcasecmp($strMd5Sign,$post['sign_id'])!== 0){
				$str = sprintf('check the sign error. strMd5Sign[%s] post.sign[%s]',
					$strMd5Sign,$post['sign_id']);
				UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
				$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
				$arrRes['errmsg'] = VoteInc :: $error['common']['CLIENT_ERR'];
				return $arrRes;
			}
			$str = sprintf('check the sign success. strMd5Sign[%s] post.sign[%s]',
					$strMd5Sign,$post['sign_id']);
			UB_LOG_DEBUG(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
		}

		// get the max_select_item
		$max_select_item = intval($vote_info['max_select_num']);
		if ($max_select_item > VoteInc :: VOTE_MAX_SELECT_ITEM) {
			$max_select_item = VoteInc :: VOTE_MAX_SELECT_ITEM;
		}
		
		//check the item
		$strTmp = str_replace(array(',','��'), array (',',','), $post['select_items']);
		$arrTmp = explode(',', $strTmp);
		$arrOut = array();
		if (count($arrTmp) > 0) {
			foreach ($arrTmp as $strPer) {
				$strItem = trim($strPer);
				if (strlen($strItem) > 0 && !in_array($strItem, $arrOut)) {
					$arrOut[] = intval($strItem);
				}
			}
		}
		if (count($arrOut) <= 0) {
			$str = 'have not select item while addVote';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			header(VoteInc :: CLIENT_ERR_HEADER_STATUS);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['common']['CLIENT_ERR'];
			return $arrRes;
		}
		else if (count($arrOut) > $max_select_item) {
			$str = sprintf('select too much[%d] (more than %d) item while addVote', 
				count($arrOut),$max_select_item);
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['common']['CLIENT_ERR'];
			return $arrRes;
		}
		//���ȿ���
		$arrRes = self::checkAntiVcode($user,self::ACTS_CTRL_ADD_VOTE_COMMAND_NO,
				$post['vcode_str'],$post['vcode_input'],$post['vote_id']);
				
		if ( true !== $arrRes) {
			$str = sprintf('can not pass the acts_ctrl or vcode check, while addVote');
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI,$str, __FILE__, __LINE__);
			return $arrRes;
		}
		
		//get vote relation data (add for the itieba)
		$vote_relation = self::getVoteRelation($post['vote_id']);
		if (false === $vote_relation) {
			$str = 'getVoteRelation error while addVote.';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_UI, $str, __FILE__,__LINE__);
			header(VoteInc :: SERVER_ERR_HEADER_STATUS);
			$arrRes = VoteInc::$errno['HTTP_VOTE_SERVICE'];
			$arrRes['errmsg'] = VoteInc :: $error['common']['SERVER_ERR'];
			return $arrRes;
		}
		$forum_name = '';
		$forum_id = 0;
		foreach ($vote_info['vote_properties'] as $attr) {
			if ($attr['property_key'] === 'forum_name') {
				$forum_name = $attr['property_value'];
			}
			if ($attr['property_key'] === 'forum_id') {
				$forum_id = $attr['property_value'];
			}
		}
		if(isset($user['un']) === false || isset($user['uid']) === false){
			$user['un'] = '';
			$user['uid'] = 0;
		}
		if (isset($vote_relation[0]['id_1']) === false) {
			$vote_relation[0]['id_1'] = 0;
		}
		//call the addVote rpc
		$arrInput = array (
			'product_id' => $product_id,
			'post_ip' => $user['uip'],
			'cookie_id' => $user['cookie_id'],
			'post_uid' => $user['uid'],
			'vote_id' => $post['vote_id'],
			'selected_items' => $arrOut,
			//add for the itieba
			'itieba_id' => $forum_id,	//forum_id
			'thread_id' => $vote_relation[0]['id_1'],
			'post_id' => 0,
			'title' => $vote_info['title'],
			'user_name' => $user['un'],
			'user_id' => $user['uid'],
			'forum_name' => $forum_name,			
		);
		$arrOutput = array();
		$bolRes = Rpc :: rpcCall(VoteInc :: SERVER_VOTE_DAL, 'postVote', $arrInput, $arrOutput);
		if (false === $bolRes) {
			$str = 'rpcCall add vote failed';
			UB_LOG_WARNING(VoteInc :: VOTE_MODULE_RPC, $str, __FILE__,__LINE__);
			$arrRes = VoteInc::$errno['HTTP_VOTE_SERVICE'];
			$arrRes['errmsg'] = VoteInc :: $error['common']['SERVER_ERR'];
			return $arrRes;
		}
		$arrResult = array();
		$arrResult['errno'] = VoteInc::$errno['HTTP_VOTE_SUCCESS']['errno'];
		$arrResult['error'] = VoteInc::$errno['HTTP_VOTE_SUCCESS']['error'];
		$arrResult['tid'] = $vote_relation[0]['id_1'];
		return $arrResult;
	}
}

/* vim: set ts=4 sw=4 sts=4 tw=100 noet: */
?>
