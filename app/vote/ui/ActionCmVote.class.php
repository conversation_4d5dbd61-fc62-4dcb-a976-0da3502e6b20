<?php
/***************************************************************************
 * 
 * Copyright (c) 2009 Baidu.com, Inc. All Rights Reserved
 * $Id: ActionCmVote.class.php,v 1.17 2009/09/07 08:27:33 dule Exp $ 
 * 
 **************************************************************************/
 
 
 
/**
 * @file ActionCmVote.class.php
 * <AUTHOR>
 * @date 2009/08/03 11:45:48
 * @version $Revision: 1.17 $ 
 * @brief 
 *  
 **/

require_once (dirname(__FILE__).'/CmVoteConfig.class.php');
require_once (dirname(__FILE__).'/CmVoteModule.class.php');
require_once (dirname(__FILE__).'/common/VoteUiFunction.class.php');

class ActionCmVote extends Action {

	private $_action_key;/*action type*/
	private $_action_map;/*action attr:class��Method��paramNeed��power��cryptneed*/
	private $_context;/*global context*/

	private $_user_stats;
	private $_user_privilege;

	private $_var_post;
	private $_var_get;
	
	private $_retType;/*�������ͣ�ֻ֧��json��mcpack��html*/
	private $_retProduct;/*�ص�*/
	
	const RET_TYPE_NAME = 'alt';
	const RET_CALLBACK_NAME = 'callback';
	const DEFAULT_RET_TYPE = 'json';
	const RET_TYPE_JSON    = 'json';
	const RET_TYPE_MCPACK  = 'mcpack';
	const RET_TYPE_HTML    = 'html';
	const CALLBACK_TPL = 'jsonCallBack.tpl';
	const HTML_TPL = "html/htmlCallBack.tpl";
	const HTML_CALLBACK_TPL = "html/htmlCallBack.tpl";

	public function execute($context, $actionParams = null) {
		
		$this->_context = $context;
		if (false === $this->__init($context)) {
			return false;
		}

		$res = $this->__check();
		if (true !== $res) {
			$strInfo = sprintf('check error, while %s::%s [un:%s uid:%d uip:%d]',
				$this->_action_map['class'], $this->_action_map['hander'], 
				$this->_user_stats['un'], $this->_user_stats['uid'], $this->_user_stats['uip']);
			UB_LOG_WARNING (VoteInc::VOTE_MODULE_UI, $strInfo, __FILE__, __LINE__, null, 0, $res);
			$arrRes = VoteInc::$errno['HTTP_VOTE_DATA'];
			$arrRes['errmsg'] = VoteInc :: $error['common']['CLIENT_ERR'];
			return $arrRes;
		}

		$this->__dispatch();
		return true;
	}

	private function __init() {
		
		//check the url
		$url = $this->_context->getProperty(VoteInc :: KEY_URL);
		if (!isset($url[3]) || strlen($url[3]) <= 0) {
			UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI, "ERROR: the url wrong. url[3]:[$url[3]]",
				__FILE__, __LINE__);
			return false;
		}
		//set the action_key
		$this->_action_key = $url[3];
		if (!isset(CmVoteConfig :: $config[$this->_action_key])) {
			UB_LOG_WARNING (VoteInc::VOTE_MODULE_UI, "ERROR: couldn't find the action_key[$this->_action_key]",
				__FILE__, __LINE__);
			return false;
		}
		//set the action_map
		$this->_action_map = CmVoteConfig :: $config[$this->_action_key];

		/* get data */
		$this->_var_post = $this->_context->getProperty(VoteInc :: KEY_VAR_POST);
		$this->_var_get = $this->_context->getProperty(VoteInc :: KEY_VAR_GET);
		$this->_user_stats = $this->_context->getProperty(VoteInc :: KEY_USER_STATUS);
		
		UB_LOG_DEBUG(VoteInc::VOTE_MODULE_UI,'***the user status***',
			__FILE__,__LINE__,0,NULL,$this->_user_stats);
		
		//ȷ���������� json
		if(isset($this->_var_get[self :: RET_TYPE_NAME]) === true){
			$this->_retType = $this->_var_get[self :: RET_TYPE_NAME];
			if($this->_retType !== self :: RET_TYPE_JSON && $this->_retType !== self :: RET_TYPE_MCPACK
				&& $this->_retType !== self :: RET_TYPE_HTML){
				$strInfo = sprintf("ret type [%s] not valid", $this->_var_get[self :: RET_TYPE_NAME]);
				UB_LOG_WARNING (VoteInc :: VOTE_MODULE_UI, $strInfo, __FILE__, __LINE__, null, 0, null);
				$this->_retType = self :: DEFAULT_RET_TYPE;
			}
		}
		else{
			$this->_retType = self :: DEFAULT_RET_TYPE;
		}

		//�ж��Ƿ���callback����
		if(isset($this->_var_get[self :: RET_CALLBACK_NAME]) === true){
			$this->_retProduct = $this->_var_get[self :: RET_CALLBACK_NAME];
		}
		else{
			$this->_retProduct = '';
		}
		$this->_context->setProperty (VoteInc :: KEY_CALLBACK_INFO, $this->_retProduct);
		
		return true;
	}

	private function __check() {

		$post_need = $this->_action_map['post_need'];
		$get_need = $this->_action_map['get_need'];
		$post_data = $this->_var_post;
		$get_data = $this->_var_get;
		
		//check the need_post
		UB_LOG_DEBUG(VoteInc::VOTE_MODULE_UI, "******TEST: the post data",
					__FILE__, __LINE__,NULL,0,$post_data);
		foreach ($post_need as $arrPost) {
			if (!isset($post_data[$arrPost])) {
				UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI, "check the post_need error. [$arrPost]",
					__FILE__, __LINE__);
				return false;
			}
		}
		//check get 
		foreach ($get_need as $arrGet) {
			if (!isset($get_data[$arrGet])) {
				UB_LOG_WARNING(VoteInc::VOTE_MODULE_UI, "check the get_need error. [$arrGet]",
					__FILE__, __LINE__);
				return false;
			}
		}
		return true;
	}

	private function __dispatch() {
		$strClass = $this->_action_map['class'];
		$strHandle = $this->_action_map['hander'];
		
		time_start('cm_vote');
		//$bolRes = call_user_func("$strClass::$strHandle", $this->_user_stats, $this->_var_post);
		$bolRes = call_user_func(array($strClass,$strHandle), $this->_user_stats, $this->_var_post);
		time_end('cm_vote');
		
		/*if (isset ($this->_action_map['tpl'])) {
			$strTpl = $this->_action_map['tpl'];
		}
		else {
			//ǰֻ֧��json��ʽ��ģ��
			$strTpl = 'json/json.tpl';
		}*/
		
		if($this->_retType === self :: RET_TYPE_JSON){
			if($this->_retProduct ===''){
				$strTpl = sprintf("%s/%s", self :: RET_TYPE_JSON, self :: RET_TYPE_JSON.".tpl");
			}
			else{
				$strTpl = sprintf("%s/%s", self :: RET_TYPE_JSON, self :: CALLBACK_TPL);
			}
			header("Content-type: text/javascript; charset=GBK");
			
		}
		else if($this->_retType === self :: RET_TYPE_HTML){
			if($this->_retProduct ===''){
				$strTpl = self::HTML_TPL;
			}
			else{
				$strTpl = self::HTML_CALLBACK_TPL;
			}
			header("Content-type: text/html; charset=GBK");
		}
		else{
			$strTpl = self :: DEFAULT_RET_TYPE;
		}
		$this->_context->modifyProperty (VoteInc :: KEY_VOTE_TPL,$strTpl);
		UB_LOG_DEBUG(VoteInc::VOTE_MODULE_UI, "the tpl name is [$strTpl]",
					__FILE__, __LINE__);
		
		//����������ģ�岿��
		if (isset ($this->_action_map['var'])) {
			$var = $this->_action_map['var'];
			$this->_context->modifyProperty (VoteInc :: KEY_VOTE_TPL_VAR,$var);
		}
		else {
			$this->_context->setProperty ('json_data', $bolRes);
			
			$var = array ('json_data');
			$this->_context->modifyProperty (VoteInc :: KEY_VOTE_TPL_VAR,$var);
		}
		return true;
	}
}


/* vim: set ts=4 sw=4 sts=4 tw=100 noet: */
?>
