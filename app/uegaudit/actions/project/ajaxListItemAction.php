<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [ajaxListItemsAction.php description]
 * <AUTHOR>
 * @DateTime 18/3/12 19:08
 * @brief
 */

class AjaxListItemAction extends Lib_BaseAction {
    protected $_boolCheckPerm = true;

    protected function run()
    {
        $intProjectId = intval(Bingo_Http_Request::getNoXssSafe("project_id", 0));
        $intPage    = intval(Bingo_Http_Request::getNoXssSafe("page", 1));
        $intLimit    = Bingo_Http_Request::getNoXssSafe("limit", 20);
        $intType = intval(Bingo_Http_Request::getNoXssSafe("type", 0));

        $intTotal = 0;
        $arrItems = array();
        $arrSort = array(
            'field' => 'id',
            'order' => 'asc',
        );
        if ($intType == 0){
            $arrConditions = array(
                array(
                    'status', 
                    '=',
                    Lib_Def::ITEM_STATUS_TOAUDIT,
                )
            );
        } else {
            $arrConditions = array(
                array(
                    'status', 
                    'in',
                    array(Lib_Def::ITEM_STATUS_AUDITED, Lib_Def::ITEM_STATUS_REVIEWED)
                )
            );
            $arrSort = array(
                'field' => 'id',
                'order' => 'desc',
            );
        }
        $arrRes = Dl_Es_Es::count_es($intProjectId,$arrConditions);
        if ($arrRes === false && $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call es count_es failed');
            $this->_setXssSafe(false);
            $this->_retjson(Tieba_Errcode::ERR_SUCCESS);
            return ;
        }
        $intTotal = $arrRes['output'];
        if ($intTotal > 0) {
            $append = "limit ".($intPage-1)*$intLimit.",".$intLimit;
            $arrRes = Dl_Es_Es::select_es_order($intProjectId, "*", $arrConditions, $arrSort, $append);
            if ($arrRes === false && $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call uegaudit selectEs failed');
                $this->_setXssSafe(false);
                $this->_retjson(Tieba_Errcode::ERR_SUCCESS);
                return ;
            }
            $arrItems = $arrRes['output'];
        }
        $bdPicUrlCrypt = new Bd_Pic_UrlCrypt();
        $baseno = ($intPage-1)*$intLimit;
        foreach ($arrItems as $itemKey=>&$item) {
            $item['no'] = ++$baseno;
            $item["id"] = $item["id"];
            $item["project_id"] = $intProjectId;
            if ($item['task_op_tag'] == "0") {
                // 设置为默认选中tag
                $item['task_op_tag'] = "1";
            }
            if (!is_array($item["data"])) {
                $item["data"] = json_decode($item["data"], true);
            }
            $item["columns"] = array();
            foreach ($item["data"] as $key => $value) {
                $item["columns"][] = array(
                    "key" => $key,
                    "value" => $value
                );
            }

            if ($item['data']['content']){
                preg_match_all('/<img\s.*?>/', $item['data']['content'],$arrImgMatch);
                if ($arrImgMatch){
                    $pic_ids = array();
                    $pic_urls = array();
                    foreach ($arrImgMatch[0] as $strImg) {
                        preg_match('/<img.+src=\"?([^ "]*)\"?.+>/i', $strImg, $matchSrc);
                        $pos = strripos($matchSrc[1],"/");
                        if($pos) {
                            $str = substr($matchSrc[1],$pos+1,-4);
                            $pic_id = $bdPicUrlCrypt->decode_pic_url_crypt($str);
                            if ($pic_id){
                                $pic_urls[] = $matchSrc[1];
                                $pic_ids[] = $pic_id;
                            }

                        }
                    }
                    if ($pic_ids){
                        $pid2Url = array();
                        $req = array( 'cmd_no' =>1014,'pic_ids'=>$pic_ids);
//                        $ret = Tieba_Ral::call('space','nothing',$req,rand());
                        $ret = Space_Imgquery::queryMultiPictureInfo($pic_ids); // hiphotos升级 update zgw2014 20191011
                        if (isset($ret['pic_list']) && $ret['pic_list']){
                            foreach ($ret['pic_list'] as $picIndex=>$picItem){
                                if ($picItem['width'] > 580){
                                    $w = 580;
                                } else if ($picItem['width'] > 1){
                                    $w = $picItem['width'] - 1;
                                }
                                $pid2Url[] = array(
                                    "pic_id" =>$pic_ids[$picIndex],//图片id，64位无符号整型
                                    "product_name" =>"forum",//产品线名称
                                    "domain" => "imgsa.baidu.com", // hiphotos的domain地址
                                    "pic_spec" =>"w={$w}",//图片处理参数，表示将宽压缩到200，高压缩到300
                                );
                            }
                        }
                        if ($pid2Url){
                            $urls = Bd_Pic::pid2Url($pid2Url);
                            if (isset($urls['resps']) && $urls['resps']){
                                foreach ($urls['resps'] as $urlIndex=>$urlItem){
                                    $item['data']['content'] = str_replace($pic_urls[$urlIndex], $urlItem, $item['data']['content']);
                                }
                            }
                        }
                    }
                }
            }
        }

        $this->_setXssSafe(false);
        $this->_retjson(Tieba_Errcode::ERR_SUCCESS, array(
            "items" => $arrItems,
            "total" => $intTotal,
            "page" => $intPage,
            "limit" => $intLimit,
            "total_page" => ceil($intTotal/$intLimit)
        ));
    }
}

