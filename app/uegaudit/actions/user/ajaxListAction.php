<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [ajaxListAction.php description]
 * <AUTHOR>
 * @DateTime 18/3/12 08:48
 * @brief
 */

class AjaxListAction extends Lib_BaseAction {
    protected $_boolCheckPerm = true;
    /**
     * [run description]
     * <AUTHOR>
     * @param
     * @return
     */
    protected function run() {

        $intPage    = Bingo_Http_Request::getNoXssSafe("page", 1);
        $intLimit    = Bingo_Http_Request::getNoXssSafe("limit", 9);
        $strFilterKeyword = trim(Bingo_Http_Request::getNoXssSafe("keyword", ""));


        $arrConditions = array();
        {
            if (!empty($strFilterKeyword)) {
                $arrConditions[] = array("user_name", "like", "%".$strFilterKeyword."%");
            }
            if (empty($arrConditions)) {
                $arrConditions = null;
            }
        }

        $userModel = new Dl_Model_User();

        $intTotal = $userModel->selectCount($arrConditions);
        if ($intTotal > 0) {
            $append = "order by create_time desc limit ".($intPage-1)*$intLimit.",".$intLimit;
            $arrUsers = $userModel->select("*", $arrConditions, null, $append);

            $arrUids = Lib_Array::column($arrUsers, "user_id");
            $userRoleModel = new Dl_Model_UserRole();
            $arrRes = $userRoleModel->select("*", array(array("user_id", "in", $arrUids)));
            foreach($arrRes as $val) {
                $arrRoleInfo[$val['user_id']][$val['role_id']][$val['id']] = $val['role_type'];
            }

            foreach ($arrUsers as &$user) {
                if (isset($arrRoleInfo[$user["user_id"]])) {
                    $user["roles"] = $arrRoleInfo[$user["user_id"]];
                } else {
                    $user["roles"] = array();
                }
            }

        } else {
            $arrUsers = array();
        }

        $this->_retjson(Tieba_Errcode::ERR_SUCCESS, array(
            "items" => $arrUsers,
            "total" => $intTotal,
            "page" => $intPage,
            "pagesize" => $intLimit,
            "total_page" => ceil($intTotal/$intLimit),
        ));

    }
}
