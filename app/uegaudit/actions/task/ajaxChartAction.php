<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [ajaxChartAction.php description]
 * <AUTHOR>
 * @DateTime 18/3/14 19:15
 * @brief
 */

class AjaxChartAction extends Lib_BaseAction {
    protected $_boolCheckPerm = true;

    protected function run()
    {
        $intProjectId = intval(Bingo_Http_Request::getNoXssSafe("project_id"));
        $intTaskId = intval(Bingo_Http_Request::getNoXssSafe("task_id"));
        $arrConditions = array(
            array(
                'task_id',
                '=',
                $intTaskId
            )
        );

        $arrRes = Dl_Es_Es::group_by_count_es($intProjectId, 'task_op_tag', $arrConditions);
        if ($arrRes === false) {
            Bingo_Log::warning('call es group_by_count_es failed');
            $this->_retjson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return ;
        }
        $arrTagNum = array();
        $intAllTotal = 0;
        foreach($arrRes as $val) {
            $arrTagNum[$val['key']] = $val['doc_count'];
            $intAllTotal += $val['doc_count'];
        }
        $arrTagId = array_keys($arrTagNum);
        $tagModel = new Dl_Model_Tag();
        $arrConditions = array(
            array(
                'project_id',
                '=',
                $intProjectId
            )
        );
        $arrRes = $tagModel->select('*', $arrConditions);
        if ($arrRes === false) {
            Bingo_Log::warning('select tag failed, sql = ' . $tagModel->getLastSql());
            $this->_retjson(Tieba_Errcode::ERR_SUCCESS);
            return ;
        }
        $arrTagInfo = array();
        foreach($arrRes as $val) {
            $arrTagInfo[$val['id']] = $val;
        }

        $arrCategorys = array();
        $intTotal = 0;

        $arrParentChild = array();
        foreach($arrTagInfo as $intTagId => $tag) {
            $tmpNum = $arrTagNum[$intTagId];
            if ($tag['parent_id'] == 0) {
                if (!isset($arrCategorys[$intTagId])) {
                    $arrCategorys[$intTagId] = array(
                        'id' => $intTagId,
                        'name' => $tag['name'],
                        'childs' => array(),
                        'count' => 0,
                    
                    );
                }
            } else {
                $intParentId = $tag['parent_id'];
                if (!isset($arrCategorys[$intParentId])) {
                    $arrCategorys[$intParentId] = array(
                        'id' => $intParentId,
                        'name' => $arrTagInfo[$intParentId]['name'],
                        'childs' => array(
                            $intTagId => array(
                                'id' => $intTagId,
                                'name' => $tag['name'],
                                'count' => $arrTagNum[$intTagId], 
                            )
                        ),
                        'count' => $arrTagNum[$intTagId],
                    );
                }
                $arrCategorys[$intParentId]['childs'][$intTagId] = array(
                    'id' => $intTagId,
                    'name' => $tag['name'],
                    'count' => $arrTagNum[$intTagId],
                );
                $arrCategorys[$intParentId]['count'] += $arrTagNum[$intTagId];
            }
        }

        $this->_retjson(Tieba_Errcode::ERR_SUCCESS, array(
            "categorys" => $arrCategorys,
            "total" => $intAllTotal,
            "alltotal" => $intAllTotal,
        ));
    }
}
