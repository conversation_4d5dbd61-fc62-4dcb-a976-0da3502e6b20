<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [ajaxUpdateItemAction.php description]
 * <AUTHOR>
 * @DateTime 18/3/12 22:21
 * @brief
 */

class AjaxUpdateItemAction extends Lib_BaseAction {
    protected $_boolCheckPerm = true;

    protected function run()
    {
        $intProjectId = Bingo_Http_Request::getNoXssSafe("project_id", 0);
        $intTaskId =  Bingo_Http_Request::getNoXssSafe("task_id", 0);
        $arrPost = Bingo_Http_Request::getNoXssSafe("posts", array());

        $arrPost = json_decode($arrPost, true);
        $arrPost = Util_Tag::setSpecificProjectDefaultTag($intProjectId,$arrPost);
        
        $arrInput = array(
            "project_id" => $intProjectId,
            "task_id" => $intTaskId,
            "posts" => $arrPost,
            "op_uid" => $this->_getUserId(),
            "op_uname" => $this->_getUserName(),
            "op_group" => 1,
        );

        $arrOutput = Tieba_Service::call("uegaudit", "submit", $arrInput,  null, null, 'post', 'php', 'utf-8');
        Bingo_Log::notice("AjaxUpdateItemAction arrInput: ". serialize($arrInput) . " arrOutput: ". serialize($arrOutput));

        if ($arrOutput  ==  false || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("AjaxUpdateItemAction failed arrInput: ". serialize($arrInput) . " arrOutput: ". serialize($arrOutput));
            $this->_retjson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
         $this->_stlog($intProjectId, $arrPost);
        
        $this->_retjson(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }
    protected function _stlog($intProjectId, $arrPost) {
        if (count($arrPost) == 0) {
            return ;
        }
        $realName = '';
        $userName = '';
        $userRole = '';
        $userMode = new Dl_Model_User();
        $arrRes = $userMode->query('select v1_user_role.user_id, v1_role.name,v1_user.user_name,v1_user.real_name from v1_user_role  left join v1_role on v1_user_role.role_id=v1_role.id left join v1_user on v1_user_role.user_id=v1_user.user_id  where v1_user_role.user_id =' . $this->_getUserId());
        if ($arrRes === false) {
            Bingo_Log::warning('get user info failed, sql = ' . $userMode->getLastSql());
        } else {
            foreach ($arrRes as $val) {
                $userRole .= $val['name'].',';
            }
            $realName = $arrRes[0]['real_name'];
            $userName = $arrRes[0]['user_name'];
        }
        $projectModel= new Dl_Model_Project();
        $arrConditions = array(
            array(
                'id',
                '=',
                $intProjectId
            )
        );
        $arrRes = $projectModel->select('title', $arrConditions);
        $projectName = $arrRes[0]['title'];

        $arrConditions = array(
            array(
                'id',
                'in',
                array_keys($arrPost)
            )
        );
        $arrRes = Dl_Es_Es::select_es($intProjectId, '*', $arrConditions);
        $arrItems = $arrRes['output'];
        $totalTime = 0;
        $totalNum = count($arrItems);
        $threadNum = 0;
        $postNum = 0;
        foreach($arrItems as $item) {
            $totalTime += time() - strtotime($item['create_time']);
            $data = $item['data'];
            if (!is_array($data)) {
                $data = json_decode($data, true);
            }
            intval($data['command_no']) == 1 ? $threadNum ++ : $postNum ++;
        }
        $time = ceil($totalTime / $totalNum);

        Tieba_Stlog::addNode('obj_id', $projectName);
        Tieba_Stlog::addNode('obj_name', $userName);
        Tieba_Stlog::addNode('obj_param1', $realName);
        Tieba_Stlog::addNode('obj_param2', $threadNum);
        Tieba_Stlog::addNode('obj_param3', $postNum);
        Tieba_Stlog::addNode('obj_duration', $time);
        Tieba_Stlog::addNode('obj_source', $userRole);
        Tieba_Stlog::addNode('uid', $this->_getUserId()); 
    }
}
