<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [ajaxListUserAction.php description]
 * <AUTHOR>
 * @DateTime 18/3/12 08:44
 * @brief
 */

class AjaxListUserAction extends Lib_BaseAction {
    protected $_boolCheckPerm = true;

    /**
     * [run description]
     * <AUTHOR>
     * @param
     * @return
     */
    protected function run()
    {
        $intRoleId = intval(Bingo_Http_Request::getNoXssSafe("id", 0));
        
        $UserRoleModel = new Dl_Model_UserRole();
        $arrConditions = array(
            "role_id" => $intRoleId
        );
        $arrUsers = $UserRoleModel->select("*", $arrConditions);
        $this->_retjson(Tieba_Errcode::ERR_SUCCESS, $arrUsers);
    }
}