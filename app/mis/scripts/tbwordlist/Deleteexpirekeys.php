<?php
define('DEBUG',false);


//�������б�
//�������
ini_set("memory_limit", "2G");

//ɾ�����ڵ�key


require_once(dirname(__FILE__).'/../../script_init.php'); //����ű���ʼ���ļ�
init_script_env('tbwordlist');                         //���ýű�����ģ��


$strPreCmd = HOME_PHP_PATH.' -f '.dirname(__FILE__).'/Deleteexpirekeys.handelPerTable.php %s %s';


//get all tables
//foreach per item
//if expire then del
//delter user is script01

$app_name = $_SERVER['argv'][1];
if(empty($app_name)){
	echo "usage: ".$_SERVER['PHP_SELF']." \$your_app_name"."\n";
	echo "err. empty app_name"."\n";
	Bingo_Log::warning('err. empty app_name');
	exit();//û�д�app_name����
}

$intResNum = 30;//ÿ������ȡ30������


$intStart = 0;

$strServiceName = 'wordlist';
$strMethod 		= 'queryWL';

for ($i=1;;$i++) {//ÿ������ȡ30������,ֱ��ȡ����������

	$intStart = ($i - 1)*$intResNum;
	if($intStart <= 0){
		$intStart = 0;
	}
	$intOffset = $intResNum;

	$arrInputPara = array(
						  'app_name'  => $app_name,
						 );
	$arrInput = array(
					  'query_info'    => $arrInputPara,
					  'start'     => $intStart,
					  'offset'    => $intOffset,
					 );
	$arrOutput = Tieba_Service::call($strServiceName,$strMethod, $arrInput);
	if ( isset($arrOutput['errno']) && (0 == intval($arrOutput['errno'])) ) {
		if(0 == count($arrOutput['ret'])){
			Bingo_Log::debug('[end of get all wordlist]');
			break;//��ȡ����������
		}

		foreach($arrOutput['ret'] as $arrTmp){
			Bingo_Log::getModule()->flush();
			$strWordlistName = $arrTmp['name'];
			$strCmd = sprintf($strPreCmd, $app_name, $strWordlistName);
			Bingo_Log::warning('[start system cmd:'.$strCmd.']');
			if(defined('DEBUG') && true === DEBUG){
				echo $strCmd."\n";
			}else{
				system($strCmd);
			}
			Bingo_Log::warning('[end system cmd:'.$strCmd.']');
			sleep(1);
		}


	} else {
		Bingo_Log::warning(sprintf('get table list error. Err to call servicename:[%s] method:[%s] [input:%s] [output:%s]',$strServiceName,$strMethod,serialize($arrInput),serialize($arrOutput)));
		exit();	//��������,�˳�.  �漰�����дʱ������ϲ��԰�ȫЩ
	}

}
