<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
require_once(dirname(__FILE__).'/../../script_init.php'); //����ű���ʼ���ļ�
init_script_env('pmcmis');
//$ret0=Util_Pmcmis_BlockAndMask::banBlockAndMask(200072959);
//var_export($ret0);
//$ret1=  Util_Pmcmis_BlockAndMask::getUserBlockAndMaskInfo(200072959);
//var_export($ret1);
//$ret2=  Util_Pmcmis_BlockAndMask::unBanBlockAndMask(200072959);
//var_export($ret2);
//$ret3=  Util_Pmcmis_BlockAndMask::getUserBlockAndMaskInfo(200072959);
//var_export($ret3);

//$ret0=Util_Pmcmis_Block::forumUnbanId(35, 200072959, 'sdzyl01');
//var_export($ret0);
//$ret1=  Util_Pmcmis_Block::forumUnbanId(35, 200072959, 'sdzyl01');
//var_export($ret1);


$ret0=  Util_Pmcmis_Mask::unMaskUser(200072959);
var_export($ret0);
//$ret0=Util_Pmcmis_UserState::unsetUserState(1496,35);
//$ret=Util_Pmcmis_UserState::setUserState('abc',1496, 1496, 0, 3600*24, 0, 'test',1);
//var_export($ret);
//$ret3=  Util_Pmcmis_UserState::getUserStateHistory(1496);
//var_export($ret3);
//$ret4= Util_Pmcmis_UserState::getUserState(1496);
//var_export($ret4);

?>
