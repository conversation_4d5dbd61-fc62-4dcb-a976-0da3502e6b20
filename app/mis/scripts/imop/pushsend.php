<?php

ini_set('display_errors', 1);
error_reporting(E_ERROR);
define(MIS_DATA_PATH, dirname(__FILE__));
require_once(dirname(__FILE__) . '/../../script_init.php');
require_once(dirname(__FILE__) . '/bcs.class.php');
init_script_env('imop');


class Scripts_Imop_Pushsend {

    const BATCH_SIZE = 100;

    public static function run($strFilePath, $blockTotal, $blockIndex, $strMsgId, $strTaskId) {

        $chunks = Lib_Filereader::getChunkInfo($strFilePath, $blockTotal);
        $targetChunk = $chunks[$blockIndex - 1];
//        Bingo_Log::warning('===================> chunk'.json_encode($targetChunk));

        if ($targetChunk == null) {
            return;
        }

        $pos = $targetChunk['pos'];
        do {
            $result = Lib_Filereader::getRowsFromFile($strFilePath, $pos, self::BATCH_SIZE, $targetChunk['range']);
            $pos = $result['pos'];

            $arrQnames = $result['rows'];
//            Bingo_Log::warning('===================>'.json_encode($result));

            self::process($arrQnames, $strMsgId, $strTaskId);
        } while ($result['feof'] == 0);


    }

    private static function process($arrRows, $strMsgId, $strTaskId) {
        $arrQNames = array();
        foreach ($arrRows as $strRow) {
            $arrParts = explode("::", $strRow);
            $uid = array_shift($arrParts);
            foreach ($arrParts as $strPart) {
                if (strlen($strPart) > 0) {
                    $arrQName = explode(';', $strPart);
                    array_push($arrQNames, array(
                        'user_id' => $uid,
                        'cuid' => $arrQName[0],
                        'gid' => $arrQName[1],
                        'client_version' => $arrQName[2],
                        'client_type' => $arrQName[3],
                    ));
                }

            }
        }

        $arrInput = array(
            'qname'    => $arrQNames,
            'msg_id'   => $strMsgId,
            'task_id'  => $strTaskId,
            'from_platform_type'  => 10,
        );


        Bingo_Log::warning("call opmsg:pushMsg[".json_encode($arrInput)."]");
        $ret = Tieba_Service::call('opmsg','pushMsg',$arrInput, null, null, 'post', 'php', 'utf-8');
        Bingo_Log::warning("opmsg:pushMsg#output [".json_encode($ret)."]");
    }
}

$strFilePath = $argv[1];
$blockTotal = $argv[2];
$blockIndex = $argv[3];
$strMsgId = $argv[4];
$strTaskId = $argv[5];
Scripts_Imop_Pushsend::run($strFilePath, $blockTotal, $blockIndex, $strMsgId, $strTaskId);

?>
