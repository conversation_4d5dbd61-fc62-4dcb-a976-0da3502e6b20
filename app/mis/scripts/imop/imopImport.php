<?php

/*
 * send message @luantao02
 */
ini_set ( 'display_errors', 1 );
error_reporting ( E_ERROR );
define(MIS_DATA_PATH,  dirname ( __FILE__ ));
require_once (dirname ( __FILE__ ) . '/../../script_init.php'); // 引入脚本初始化文件
require_once (dirname ( __FILE__ ) . '/bcs.class.php'); // 引入脚本初始化文件

init_script_env ( 'imop' );
class Scripts_Imop_ImopImport {

    private static $ORP_TMP = array(
        'serverUrl' => 'http://*************:8123', //线下配置
        //'serverUrl' => 'http://orp.baidu.com',
        'token' => "nFpq5thU-H8Ga34yiwhiim9T6BPv1TY3",
        'fileOrpDir' => "/app/aladdin/",
    );
	private static $BD_CLOUD = array(
	    'bucket' => 'tieba-livegroup',
			'ak' => 'XyOLwCtGO4qs1MYaSZa9Spwk9JXJ4',
			'sk' => '2RNFdAIMGWGwF70dXS4yQMeDwcNJm',
	);
	public static function run() {
		
		$arrInput['page_num'] = 1 ; //
		$arrInput['res_num']  = 1; //conduct one per time
		$arrInput ['condtion']['status'] = 4;
		$arrInput ['condtion']['send_type'] = 3; //按吧ID导入的需要把吧ID入库，然后数据组从表里面LOAD数据
		$strService = 'imop';
		$strMethod = 'getOpMsgListForDt';
		
		//Bingo_Log::warning(json_encode($arrInput));
	  $arrRet = Service_Mis::call($strService, $strMethod, $arrInput);
	  //Bingo_Log::warning(json_encode($arrRet));
		if($arrRet===false || $arrRet['errno']!=Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning(sprintf("call [service:%s] [method:%s] error. [input:%s]"), $strService, $strMethod, serialize($arrInput));
		    return false;
		}
		if( !empty($arrRet['data'])){
			foreach ($arrRet['data'] as $opmsg){
			    $arrRet = self::downlloadFileFromOrp($opmsg);	   
			    //$arrRet = self::uploadFileFromBdCloudToServer($opmsg);

			    //if file is exist , send to messager server
			    if( $arrRet['errno']!=Tieba_Errcode::ERR_SUCCESS ){
			    	//Bingo_Log::warning('no file need to process' .  $opmsg ['msg_id'] );
			    	//return false;
			    	continue;
			    }else{
			    	//Bingo_Log::warning('call processFileToDb input opmsg :' .  json_encode($opmsg) );
			    	$arrRet = self::processFileToDb($opmsg,$arrRet['file_path']);
			    	//Bingo_Log::warning('call processFileToDb return :' .  json_encode($arrRet) );
			    	if( $arrRet['errno'] == Tieba_Errcode::ERR_SUCCESS ){
			    		  //Bingo_Log::warning('process success :' . json_encode($opmsg) );
			    		  //return true;
			    		  continue;
			    	}else{
			    	    Bingo_Log::warning('process failed :' . json_encode($opmsg) );	
			    	}
				}
			}
			
		}
	}
	private static function processFileToDb( $arrOpMsg , $strFilePath  ){
			if (!file_exists ( $strFilePath )) {
			   return false;
		  }
			$arrUserId = array ();
			/*
			$fp = fopen ( $strFilePath, "r" );
			if ($fp) {
				for($i = 1; ! feof ( $fp ); $i ++) {
					// echo "行".$i." : ".fgets($fp). "<br />";
					Bingo_Log::warning ('read line:'.  trim(fgets ( $fp ));
					$arrUserId [] = trim(fgets ( $fp ));
				}
			}
			fclose ( $fp );
			*/
			$arrUserId = file($strFilePath);
			//Bingo_Log::warning ('read line:'.  json_encode($arrUserId));
			//unlink ( $strFilePath );
			if(empty($arrUserId)){
				 //Bingo_Log::warning ('file is null '.  $strFilePath . 'for process' . json_encode($arrOpMsg));
				 return false;
			}
			$strService = 'imop';
			$strMethod = 'saveOpmsgFileForDT';
			$arrInput = array(
					'msg_id' => $arrOpMsg['msg_id'],
					'user_id' => $arrUserId,
			);
			//Bingo_Log::warning(json_encode($arrInput));
			return Service_Mis::call($strService, $strMethod, $arrInput);
		
	}
	
	/**
	 * @brief get opmsgcontent by msg_id
	 * @arrInput: msg_id
	 *
	 * @return : $arrOutput
	 *        
	 */
	public static function downlloadFileFromOrp($arrInput) {
		if (! file_exists ( MIS_DATA_PATH )) {
			mkdir ( MIS_DATA_PATH );
		}
		$strPath = MIS_DATA_PATH . "/../../data/app/mis/groupmsgandroid";
		if (file_exists ( $strPath ) && ! is_dir ( $strPath )) {
			unlink ( $strPath );
			mkdir ( $strPath );
		} else if (! file_exists ( $strPath )) {
			mkdir ( $strPath );
		}
		
		$strTempName = $arrInput ['send_content']  ;
		$strFilePath = $strPath . "/" . $strTempName . '.txt';
		if (file_exists ( $strFilePath )) {
			unlink ( $strFilePath );
		}
		//echo '上传完成';
		//fastcgi_request_finish ();
		$strUrl = self::$ORP_TMP['serverUrl']. '/openapi/call/storage/get?token=' . self::$ORP_TMP['token'] . '&path=/app/aladdin/' . $strTempName . '&productName=tieba';
		//Bingo_Log::warning ($strUrl);
		$strGetFileCmd = 'wget "' . $strUrl. '" -O ' . $strFilePath ;
		//$strGetFileCmd = 'wget ftp://cp01-forum-dm00.cp01.baidu.com/home/<USER>/tbdc/data/tieba_rpt_sendmessage_uid_file/tieba_rpt_sendmessage_uid_file_1_20141125' .  ' -O ' .  $strFilePath;
		exec ( $strGetFileCmd, $arrOutput, $intErrNo );
		//Bingo_Log::warning ( "exec log: [cmd:" . $strGetFileCmd . "] [output:" . serialize ( $arrOutput ) . "] [errno:" . $intErrNo . "]" );
		if (0 !== $intErrNo) { // exec failed
			Bingo_Log::warning ( "get file failed: [cmd:" . $strGetFileCmd . "] [output:" . serialize ( $arrOutput ) . "] [errno:" . $intErrNo . "]" );
			return Tieba_Errcode::ERR_MO_PARAM_INVALID;
		}
		if (! file_exists ( $strFilePath )) {
			//Bingo_Log::warning ( "process file failed: [input:" . serialize($arrInput) . "] [output:" . serialize ( $arrOutput ) . "] [errno:" . $intErrNo . "]" );
			return Tieba_Errcode::ERR_MO_PARAM_INVALID;
		}
		//Bingo_Log::warning ( 'process file successed: ' . $strFilePath . ' for input'  .  json_encode($arrInput) );
		
		$arrRet['errno'] = Tieba_Errcode::ERR_SUCCESS;
		$arrRet['file_path'] = $strFilePath;
		return $arrRet;
	}
	private static function _errRet($errno) {
		return array (
				'errno' => $errno,
				'errmsg' => Tieba_Error::getErrmsg ( $errno )
		);
	}
	/**
	 * @brief get opmsgcontent by msg_id
	 * @arrInput: msg_id
	 *
	 * @return : $arrOutput
	 *        
	 */
	public static function uploadFileFromBdCloudToServer($arrInput) {
		if (! file_exists ( MIS_DATA_PATH )) {
			mkdir ( MIS_DATA_PATH );
		}
		$strPath = MIS_DATA_PATH . "/../../data/app/mis/groupmsgandroid";
		if (file_exists ( $strPath ) && ! is_dir ( $strPath )) {
			unlink ( $strPath );
			mkdir ( $strPath );
		} else if (! file_exists ( $strPath )) {
			mkdir ( $strPath );
		}
		
		$strTempName = $arrInput ['send_content'];
		$strFilePath = $strPath . "/" . $strTempName;
		if (file_exists ( $strFilePath )) {
			unlink ( $strFilePath );
		}
		//echo '上传完成';
		//fastcgi_request_finish ();
		/*
		$strUrl = self::$ORP_TMP['serverUrl']. '/openapi/call/storage/get?token=' . self::$ORP_TMP['token'] . '&path=/app/aladdin/' . $strTempName . '&productName=tieba';
		$strGetFileCmd = 'wget "' . $strUrl. '" -O ' . $strFilePath ;
		exec ( $strGetFileCmd, $arrOutput, $intErrNo );
		Bingo_Log::warning ( "exec log: [cmd:" . $strGetFileCmd . "] [output:" . serialize ( $arrOutput ) . "] [errno:" . $intErrNo . "]" );
		if (0 !== $intErrNo) { // exec failed
			Bingo_Log::warning ( "get file failed: [cmd:" . $strGetFileCmd . "] [output:" . serialize ( $arrOutput ) . "] [errno:" . $intErrNo . "]" );
			return self::_errRet(Tieba_Errcode::ERR_MO_PARAM_INVALID);
		}
		*/ 		
    $baiduBCS = new BaiduBCS( self::$BD_CLOUD['ak'], self::$BD_CLOUD['sk']);
    $opt = array (  "fileWriteTo" => $strFilePath );
    $response = $baiduBCS->get_object ( self::$BD_CLOUD['bucket'], '/' . $strTempName, $opt );
    if (! $response->isOK ()) {
     Bingo_Log::warning  ( "Download object failed." . $strTempName);
     return self::_errRet(Tieba_Errcode::ERR_MO_PARAM_INVALID);
    }		
		
		if (! file_exists ( $strFilePath )) {
			return self::_errRet(Tieba_Errcode::ERR_MO_PARAM_INVALID);
		}
		$arrRet['errno'] = Tieba_Errcode::ERR_SUCCESS;
		$arrRet['file_path'] = $strFilePath;
		return $arrRet;
	}
}
Scripts_Imop_ImopImport::run ();
?>
