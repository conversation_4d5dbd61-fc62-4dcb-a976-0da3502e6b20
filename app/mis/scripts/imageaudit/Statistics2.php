<?php
/**
 ***********************************************************************
 * @Copyright www.Baidu.com
 * <AUTHOR>
 * @DESC ͼƬ���MIS,ͳ�ƽű�
 ************************************************************************
 */
require_once dirname ( __FILE__ ) . '/../../script_init.php';
class Scripts_Imageaudit_Statistics2 {
	 protected static $_date = 0;
	 public static $_endDate = 0;
	 protected static $_statData = array();

    /**
     * process
     */
    public static function process(){
        self::$_date    = self::$_endDate  - 86400;
        echo self::$_date.", ".self::$_endDate."\n";

        Bingo_Log::notice("imageaudit start to statistics! ".self::$_date.", ".self::$_endDate);

        Bingo_Timer::start("deleteData");
        self::_deleteData();
        Bingo_Timer::end("deleteData");

        Bingo_Timer::start("statBase");
        self::_statBase();
        Bingo_Timer::end("statBase");

        Bingo_Timer::start("statDel");
        self::_statPostDelRate();
        Bingo_Timer::end("statDel");

        Bingo_Timer::start("statErr");
        self::_statErrRate();
        Bingo_Timer::end("statErr");

        Bingo_Timer::start("statPiledup");
        self::_statPiledup();
        Bingo_Timer::end("statPiledup");

        Bingo_Timer::start("statPiledupDup");
        self::_statPiledupDup();
        Bingo_Timer::end("statPiledupDup");

        Bingo_Timer::start("statExmpt");
        self::_statExmpt();
        Bingo_Timer::end("statExmpt");

        Bingo_Timer::start("saveData");
        self::_saveData();
        Bingo_Timer::end("saveData");

        Bingo_Log::notice("imageaudit statistics finish!");
    }

    /**
     * @return bool
     */
    private static function _deleteData(){
        $conds = array(
                'stat_date =' => self::$_date,
                'stat_type >=' =>3
        );
        $ret = Dl_Imageaudit_Imageaudit::delete('image_statis', $conds, 10000);
        if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            exit(255);
        }
        return true;
    }

    /**
     * @return bool
     */
    private static function _saveData(){
        $tryTime = 3;
        while ($tryTime > 0){
            $dbRet = Dl_Imageaudit_Imagestatis::insertStatistics(self::$_statData);
            if($dbRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                $tryTime--;
                if($tryTime === 0){
                    exit(255);
                }
                sleep(2);
                continue;
            }
            break;
        }
        return true;
    }

    /**
     * @return bool
     */
    private static function _statPostDelRate(){
        $tryTime = 3;
        //���ݿ����ʧ������3�Σ�ʧ����־��¼��dl��
        while ($tryTime > 0){
            $dbRet = Dl_Imageaudit_Imageauditlog::statImagePost(self::$_date , self::$_endDate);
            if($dbRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                $tryTime--;
                if($tryTime === 0){
                    exit(255);
                }
                sleep(2);
                continue;
            }
            foreach ($dbRet[ret] as $row){
                $stat = array(
                        'stat_date' => self::$_date,
                        'stat_type' => 11,
                        'op_uid'    => $row['audit_uid'],
                        'op_uname'  => $row['audit_uname'],
                        'monitor'   => $row['monitor'],
                        'audit_area'=> $row['audit_area'],
                        'base_type' => $row['base_type'],
                        'total'     => $row['post_total'],
                );
                self::$_statData[] = $stat;
                $stat['stat_type'] = 12;
                $stat['total'] = $row['deleted_post'];
                self::$_statData[] = $stat;
            }
            break;
        }
        return true;
    }

    /**
     * @return bool
     */
    private static function _statErrRate(){
        $tryTime = 3;
        //���ݿ����ʧ������3�Σ�ʧ����־��¼��dl��
        while ($tryTime > 0){
            $dbRet = Dl_Imageaudit_Imageauditlog::statData(self::$_date, self::$_endDate);
            if($dbRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                $tryTime--;
                if($tryTime === 0){
                    exit(255);
                }
                sleep(2);
                continue;
            }
            foreach ($dbRet[ret] as $row){
                $stat = array(
                        'stat_date' => self::$_date,
                        'stat_type' => 14,
                        'op_uid'    => $row['audit_uid'],
                        'op_uname'  => $row['audit_uname'],
                        'monitor'   => $row['monitor'],
                        'audit_area'=> $row['audit_area'],
                        'base_type' => $row['base_type'],
                        'total'     => $row['err_count'],
                );
                self::$_statData[] = $stat;
                $stat['stat_type'] = 15;
                $stat['total']     = $row['audit_count'];
                self::$_statData[] = $stat;
            }
            break;
        }
        return true;
    }

    /**
     * @return bool
     */
    private static function _statBase(){
        $tryTime = 3;
        //���ݿ����ʧ������3�Σ�ʧ����־��¼��dl��
        while ($tryTime > 0){
            $dbRet = Dl_Imageaudit_Imageauditlog::statBase(self::$_date, self::$_endDate);
            if($dbRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                $tryTime--;
                if($tryTime === 0){
                    exit(255);
                }
                sleep(2);
                continue;
            }
            foreach ($dbRet[ret] as $row){
                $stat = array(
                        'stat_date' => self::$_date,
                        'stat_type' => 18, //ͼ�⵱���������ͳ��
                        'op_uid'    => $row['op_uid'],
                        'op_uname'  => $row['op_uname'],
                        'monitor'   => 0,
                        'audit_area'=> 0,
                        'base_type' => $row['base_type'],
                        'total'     => $row['total'],
                );
                self::$_statData[] = $stat;
            }
            break;
        }
        return true;
    }

    /**
     * @return bool
     */
    private static function _statPiledup(){
        $tryTime = 3;
        //���ݿ����ʧ������3�Σ�ʧ����־��¼��dl��
        while ($tryTime > 0){
            $dbRet = Dl_Imageaudit_Imagequeue::statPiledup(self::$_endDate);
            if($dbRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                $tryTime--;
                if($tryTime === 0){
                    exit(255);
                }
                sleep(2);
                continue;
            }
            foreach ($dbRet[ret] as $row){
                $stat = array(
                        'stat_date' => self::$_date,
                        'stat_type' => 3,
                        'op_uid'    => 0,
                        'op_uname'  => 'sys',
                        'monitor'   => $row['monitor'],
                        'audit_area'=> $row['audit_area'],
                        'base_type' => 0,
                        'total'     => $row['total'],
                );
                self::$_statData[] = $stat;
            }
            break;
        }
        return true;
    }

    /**
     * @return bool
     */
    private static function _statPiledupDup(){
        $tryTime = 3;
        //���ݿ����ʧ������3�Σ�ʧ����־��¼��dl��
        while ($tryTime > 0){
            $dbRet = Dl_Imageaudit_Imageduplicate::statPiledupDup(self::$_endDate);
            if($dbRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                $tryTime--;
                if($tryTime === 0){
                    exit(255);
                }
                sleep(2);
                continue;
            }
            foreach ($dbRet[ret] as $row){
                $stat = array(
                    'stat_date' => self::$_date,
                    'stat_type' => 4,
                    'op_uid'    => 0,
                    'op_uname'  => 'sys',
                    'monitor'   => $row['monitor'],
                    'audit_area'=> $row['audit_area'],
                    'base_type' => 0,
                    'total'     => $row['total'],
                );
                self::$_statData[] = $stat;
            }
            break;
        }
        return true;
    }

    /**
     * @return bool
     */
    private static function _statExmpt(){
        $tryTime = 3;
        //���ݿ����ʧ������3�Σ�ʧ����־��¼��dl��
        while ($tryTime > 0){
            $dbRet = Dl_Imageaudit_Imageexmpt::statExmpt(self::$_date, self::$_endDate);
            if($dbRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                $tryTime--;
                if($tryTime === 0){
                    exit(255);
                }
                sleep(2);
                continue;
            }
            foreach ($dbRet[ret] as $row){
                $stat = array(
                    'stat_date' => self::$_date,
                    'stat_type' => 5,
                    'op_uid'    => 0,
                    'op_uname'  => 'sys',
                    'monitor'   => $row['monitor'],
                    'audit_area'=> $row['audit_area'],
                    'base_type' => 0,
                    'total'     => $row['exmpt_count'],
                );
                self::$_statData[] = $stat;
            }
            break;
        }
        return true;
    }
}

if (isset($argv[1]) && strtotime($argv[1]) > 0){
    Scripts_Imageaudit_Statistics2::$_endDate = strtotime($argv[1]);
} else {
    Scripts_Imageaudit_Statistics2::$_endDate = strtotime("today");
}

Scripts_Imageaudit_Statistics2::process();