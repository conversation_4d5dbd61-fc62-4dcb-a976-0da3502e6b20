<?php
include_once "includes/header.php";
include_once "includes/navbar.php";
?>

<p>
JAMA is a proposed standard matrix class for Java. The JAMA introduction 
describes "JAMA : A Java Matrix Package" in this way:
</p> 

<blockquote>
JAMA is a basic linear algebra package for Java. It provides user-level classes for 
constructing and manipulating real, dense matrices.  It is meant to provide sufficient 
functionality for routine problems, packaged in a way that is natural and understandable 
to non-experts.  It is intended to serve as the standard matrix class for Java, and 
will be proposed as such to the Java Grande Forum and then to Sun.  A straightforward 
public-domain reference implementation has been developed by the MathWorks and NIST as 
a strawman for such a class.  We are releasing this version in order to obtain public 
comment. There is no guarantee that future versions of JAMA will be compatible with this one.  
</blockquote>

<p>
The development team below has successfully ported the JAMA API to PHP.  You can explore 
this site to learn more about this project and it's current development status.
</p>

<?php
include_once "includes/credits.php";
include_once "includes/footer.php";	
?>