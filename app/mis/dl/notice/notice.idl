struct get_notice_out
{
	uint32_t id;
	uint32_t type;
	uint32_t flag;
	string   name;
	string   link;
	uint32_t online_time;
	uint32_t offline_time;
	uint32_t op_uid;
	string   op_uname;
	uint32_t op_time;
	uint32_t is_overwrite;
	uint32_t op_offline;
	uint32_t op_offline_uid;
	string   op_offline_uname;
	uint32_t op_offline_time;
};

service notice{
	void addNotice(uint32_t type, uint32_t flag, string name, string title, string link, uint32_t online_time, uint32_t offline_time, uint32_t op_uid, string op_uname, uint32_t op_time);
	void getNotice(uint32_t is_online, uint32_t page, out get_notice_out result[], out uint32_t count);
	void offlineNotice(uint32_t id, uint32_t op_offline_uid, uint32_t op_offline_uname, uint32_t op_offline_time);
};
