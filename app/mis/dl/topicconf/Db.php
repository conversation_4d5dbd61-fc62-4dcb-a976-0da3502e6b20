<?php
    class Dl_Topicconf_Db {
        private static $_db;

        const DB_RAL_SERVICE_NAME = "db_dl_notice_notice";
        
        protected static $product_type = '';
        /*
         * ���ò�Ʒ������
         */
        public static function setProductType($product_type){
            self::$product_type = $product_type;
        }
        /*
         * ��ȡ��Ʒ������
         */
        public static function getProductType(){
            return self::$product_type;
        }
        /*
         * ��ȡ���ݿ�����
         */
        static function getConn() {
    		if (is_null(self::$_db)) {
                self::$_db = new Bd_DB();
                if(self::$_db == null){
                    Bingo_Log::warning("new bd_db fail.");        
                    return null;
                }
                Bingo_Timer::start('dbinit');
                $r = self::$_db->ralConnect(self::DB_RAL_SERVICE_NAME);
                Bingo_Timer::end('dbinit');
                if(!$r){
                    Bingo_Log::warning("mis-topicconf, bd db ral connect fail.");       
                    self::$_db = null;
                    return null;
                }
			}
			return self::$_db;
        }
        //����ƴ��sql
	    static function generateValueFormat($value) {
		    if (is_null($value)) {
			    return "NULL";
		    }
		    if (is_string($value)) {
			    $value = str_replace("\\", "\\\\", $value);
			    $value = str_replace("'", "\\'", $value);
			    return "'$value'";
		    }
		    return $value;
	    }	
	}
