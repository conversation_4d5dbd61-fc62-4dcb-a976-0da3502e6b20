<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-01-05 14:23:53
 * @version
 */
class recommendAction extends Util_Actionbase{


	function init(){
		self::setUiAttr('COMMIT_UI');
		if (false === parent::init()){
			if(0 === $this->_intErrorNo){
				$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}


	public function process(){       

		$strAction = trim(Bingo_Http_Request::get("action",''));
		$strVersionNo = trim(Bingo_Http_Request::get("version_no",''));
		$strModule = 'recommend';


		if(strlen($strVersionNo)>0){
			//pass
		}else{
			Bingo_Log::warning("empty version_no. [action:$strAction] [version_no:$strVersionNo] [module:$strModule]");
			return false;
		}


		$arrOutput = false;
		$arrOutput = self::recommendProcess($strAction,$strVersionNo,$strModule);

		if(true){
			$this->_arrTplVar['errno']  = $arrOutput['errno'];
			$this->_arrTplVar['errmsg'] = $arrOutput['errmsg'];
			unset($arrOutput['errno']);
			unset($arrOutput['errmsg']);
			unset($arrOutput['ie']);
			if(false === $arrOutput['res'] || null === $arrOutput['res']){
				//pass
			}else{
				$this->_arrTplVar['data']   = $arrOutput['res'];
			}
		}else{//������
		
			$this->_arrTplVar['errno']  = $arrOutput['error']['errno'];
			$this->_arrTplVar['errmsg'] = $arrOutput['error']['errmsg'];
			unset($arrOutput['error']);
			$this->_arrTplVar['data']   = $arrOutput;
		}


		Bingo_Page::getView()->setOnlyDataType("json");
		Bingo_Page::setTpl("simpledata/Page.php");///home/<USER>/tieba-odp/template/mis/control/simpledata/Page.php
		return true;

	}



	protected static function recommendProcess($strAction,$strVersionNo,$strModule){

		$strCurUserName = Util_Actionbaseext::getCurUserName();// �� zhouping01
		$intCurUserId   = Util_Actionbaseext::getCurUserId();// �� 63851

		if('get' == $strAction){
			//���ݰ汾�ţ� ��� �Ƽ����б�  --�첽 action=get&version_no=xxx&module=recommend array( array(��id���ɱ���), array(��id�� �ɱ���))

			$arrInput = array(
							  'version_no' => $strVersionNo,
							 );
			$arrOutput = Actions_Verticalapp_Lib_Recommend::getRecommendList($arrInput);
		}


		if('add' == $strAction){

			//����ĳ������(�Ƽ����б�)(����һ���Ѵ��ڵİɣ���������İ��������ڣ����޸�ʧ�ܡ� )
			//--�첽 action=update&version_no=xxx&module=recommend&forum_name=xxx,  array( data(old_forum_id,new_forum_name, new_forum_id))

			$strForumName  = trim(Bingo_Http_Request::getNoXssSafe("forum_name",''));
			if(strlen($strForumName)>0){
				$arrInput = array(
								  'version_no' => $strVersionNo,
								  'forum_name' => $strForumName,
								  'user_id'    => $intCurUserId,
								  'user_name'  => $strCurUserName,
								 );
				$arrOutput = Actions_Verticalapp_Lib_Recommend::addRecommendForum($arrInput);
			}

		}

		if('del' == $strAction){

			//ɾ��ĳ������ --�첽 action=del&version_no=xxx&module=recommend&forum_ids = ('forum_id1,'forum_id2',...)
			//return succ_num, fail_num

			//$strForumIds = trim(Bingo_Http_Request::get("forum_ids",'')); 
			//$arrForumIds = Bingo_String::json2array($strForumIds);
			$arrForumIds = $_POST['forum_ids'];
			if(count($arrForumIds) > 0){
				$arrInput = array(
								  'version_no' => $strVersionNo,
								  'forum_ids' => $arrForumIds,
								  'user_id'    => $intCurUserId,
								  'user_name'  => $strCurUserName,
								 );
				$arrOutput = Actions_Verticalapp_Lib_Recommend::delRecommendForumIds($arrInput);

			}

		}

		if(false == $arrOutput){
			Bingo_Log::warning("get service false. [action:$strAction] [version_no:$strVersionNo] [module:$strModule] [input:".serialize($arrInput)."] [get:".serialize($_GET)."] [post:".serialize($_POST)."]");

		}elseif(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){

			Bingo_Log::warning("get service error. [action:$strAction] [version_no:$strVersionNo] [module:$strModule] [input:".serialize($arrInput)."] [output:".serialize($arrOutput)."] [get:".serialize($_GET)."] [post:".serialize($_POST)."]");
		}


		return $arrOutput;
	}


}

?>
