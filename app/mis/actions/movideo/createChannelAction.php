<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2016/12/10
 * @version
 * 新增一个频道
 */
class createChannelAction extends Util_Actionbase{

	CONST OP_TYPE= 'create';
    /**
     * @return bool
     */
    function init(){
        self::setUiAttr('COMMIT_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * @return bool
     */
    public function process(){

        $strChannelName = trim(strval(Bingo_Http_Request::getNoXssSafe("channel_name")));
        $strChannelType = trim(strval(Bingo_Http_Request::getNoXssSafe("channel_type")));
        $strComment = trim(strval(Bingo_Http_Request::getNoXssSafe("comment")));
        //$strUserName=strval(Bingo_Http_Request::get("user_name"));
        $intUserId = intval(Bingo_Http_Request::get("user_id"));
		$strDescription='';
		$strChannelCover='';
		$strChannelAvatar='';
		
        if(''=== $strChannelName || '' === $strChannelType || empty($intUserId)){
            Bingo_Log::warning("input params invalid.");
            return Util_Movideo_Common::outputJson(Tieba_Errcode::ERR_PARAM_ERROR);
        }
		
		$arrServiceInput = array(
            'user_id' => $intUserId,
        );
        $strServiceName = "user";
        $strServiceMethod = "getUnameByUids";
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,'post',null,'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__. "::". __FUNCTION__."call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return Util_Movideo_Common::outputJson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
		$strUserName=$arrOutput['output']['unames'][0]['user_name'];

		//判断用户是否存在
		if(''==$strUserName||'forum'==$strUserName){
			return Util_Movideo_Common::outputJson(Tieba_Errcode::ERR_DIFANG_USER_NOT_EXIST);
		}


		//判断设定的用户是否已经是频道主
		$bolUserIsChannelRole=Util_Movideo_Common::userIsChannelRole($intUserId);
		if(false==$bolUserIsChannelRole){
			return Util_Movideo_Common::outputJson(Tieba_Errcode::BZC_ALREADY_THIS_ROLE);
		}
		
		
		//判断用户是否有权限操作
		$bolUserValid=Util_ActionBaseExt::isValidUser();
		if(false === $bolUserValid){
			 Bingo_Log::warning("user invalid.");		 
            return Util_Movideo_Common::outputJson(Tieba_Errcode::ERR_MIS_NO_POWER);
			
		}
		$arrUserInfo=Util_ActionBaseExt::getUserInfo();
		$strOpUserName=$arrUserInfo['user_name'];
        $arrServiceInput = array(
            'channel_name' => $strChannelName,
            'user_id'   => $intUserId,
            'channel_type' => $strChannelType,
            'comment' => $strComment,			
            'op_user_name' => $strOpUserName,			
            'description' => $strDescription,
            'channel_cover' => $strChannelCover,
            'channel_avatar' => $strChannelAvatar,
			'user_name'=>$strUserName,
        );


        $strServiceName = "video";		
        $strServiceMethod = "createChannel";		
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,'post',null,'utf-8');
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__. "::". __FUNCTION__."call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);

            return Util_Movideo_Common::outputJson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
		$intChannelId=$arrOutput['data']['channel_id'];
		$arrInput=array(
			'channel_id'=>$intChannelId,
			'channel_name'=>$strChannelName,
			'user_name'   => $strUserName,
			'op_user_name'=>$strOpUserName,
			'op_type'=>self::OP_TYPE,
			'op_comment'=>$strComment,
			'op_time'=>time(),
		);

		$bolRet=Util_Movideo_Common::recordBehavior($arrInput);
		if(false===$bolRet){
			//如果记录失败，打印日志,记录操作者和其行为和频道id
			Util_Movideo_Common::recordLog($strOpUserName,self::OP_TYPE,$intChannelId);
		}
        return Util_Movideo_Common::outputJson(Tieba_Errcode::ERR_SUCCESS);

    }
	
	
  


}