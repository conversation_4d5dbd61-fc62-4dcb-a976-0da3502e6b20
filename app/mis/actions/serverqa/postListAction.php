<?php

/**
 * Index file to set the template name
 * User: bijianxin
 * Date: 2016-08-03
 * Time: 14:03
 * Descrption: 输入tid和pn, 查询回帖的信息，可以查询被删除的帖子
 */
class postListAction extends Util_Actionbase
{
	const PAGE_SIZE = 30;
	/**
	 * @param array
	 * @return arrInput
	 */
	function init(){
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * @param array 
     * @return arrInput
     */
    public function process()
    {
    	$thread_id = intval(Bingo_Http_Request::getNoXssSafe('thread_id', 0));
    	$pn = intval(Bingo_Http_Request::getNoXssSafe('pn', 0));
    	if($thread_id == 0 || $pn == 0){
    		Bingo_Log::warning('input param error, thread_id='.$thread_id.', pn=['.$pn.']');
    		$this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
    		return true;
    	}
    	
    	$offset = ($pn - 1) * self::PAGE_SIZE;
    	$input = array(
    		"thread_id" => $thread_id,
    		"post_id" => 0,
    		"offset" =>  $offset,
    	 	"res_num" => self::PAGE_SIZE,
    	 	"has_comment" => 0,
    	 	"need_post_num" => 1,
    	);
    	//全量帖子浏览，包括删除的帖子
    	$res = Tieba_Service::call('post', 'getFullPostsByThreadId', $input, null, null, 'post', 'php', 'utf-8');
    	Bingo_Log::warning(print_r($res, true));
    	if(!$res || $res['errno'] !== 0 ||$res['output']['output'][0]['err_no'] !== 0||count($res['output']['output'][0]['post_infos']) == 0) {
    		Bingo_Log::warning('call post getFullPostsByThreadId error thread_id = ['.$thread_id.'] arroutput = ['. serialize($res).']');
    		$this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
    		return true;
    	}
    	$info = $res['output']['output'][0];
    	unset($info['err_no']);
    	unset($info['start_no']);
    	unset($info['is_thread_deleted']);
    	unset($info['is_thread_exceed']);
    	unset($info['is_thread_mask']);
    	
    	$post_ids = array();
    	foreach($info['post_infos'] as $k => &$item){
    		$post_ids[] = $item['post_id'];
    		//设置post_type
    		$strType = $this->_getPostType($item['post_type']);
    		$item['ptype'] = $strType;
    		$item['now_time'] = date("Y-m-d H:i:s", $item['now_time']);
    		unset($item['is_vote']);
    		unset($item['post_refer']);
    		unset($item['phone_type']);
    		unset($item['openid']);
    		unset($item['thread_type']);
    		unset($item['code_type']);
    		unset($item['from_thread_id']);
    		unset($item['sign_id']);
    		if(isset($item['imgWaterInfo'])){
    			unset($item['imgWaterInfo']);
    		}
    		if(isset($item['ext_attr'])){
    			unset($item['ext_attr']);
    		}
    	}
    	
    	//查询这些pid是否已被删除
    	$input = array(
    		'input' => array(
    			'thread_ids' => array(
    				0 => 1,
    			),
    			'post_ids' => $post_ids,
    		),
    	);
    	$res = Tieba_Service::call('post', 'getMaskInfo', $input, null, null, 'post', 'php', 'utf-8');
    	if(!res || $res['errno'] !== 0 || $res['output']['err_no'] !== 0 || count($res['output']['posts_mask_status']) === 0){
    		Bingo_Log::warning('call post getMaskInfo error post_ids = ['.implode(",", $post_ids).'] arroutput = ['. serialize($res).']');
    	}else{
    		$mask_info = $res['output']['posts_mask_status'];
    		foreach($info['post_infos'] as $k => &$item){
    			$item['delete_info'] = $mask_info[$item['post_id']];
    		}
    	}
    	$this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, $info);
		return true;
    }
    
    /**
     * 设置post_type
     * @param unknown $post_type
     * @return string
     */
    protected  function _getPostType($post_type){
    	$strType = "normal or undefined in base lib";
    	$intPostType = intval(trim($post_type));
    	$res = Tieba_Type_PType::$uniq_type[$intPostType];
    	if ($resType != null){
    		$strType = $res;
    	}
    	return $strType;
    }
    
    /**
     * inherit from parent and do nothing
     * @param array
     * @return bool
     */
    protected function _jsonRet($errno, $arrData=array()){
    	$arrRet = array(
    		'errno' => $errno,
    		'errmsg' => Tieba_Error::getErrmsg($errno),
    		'data' => $arrData,
    	);
    	echo json_encode($arrRet);
    }
}
