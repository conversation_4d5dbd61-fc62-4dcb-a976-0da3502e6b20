<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-11-05 14:23:53
 * @version
 * ������ MIS FILTER ��ѯ�ӿ�
 */
class aldFilterQueryAction extends Util_Actionbase{
	function init(){
		self::setUiAttr('BROWSE_UI');
		if (false === parent::init()){
			if(0 === $this->_intErrorNo){
				$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}

	public function process()
	{
		require_once('libs/Redis.php');
		require_once('libs/util.php');
		require_once('libs/Alading.php');
		
		$strDirName = strval(Bingo_Http_Request::get('dir_name',''));
        $strDirName = Bingo_String::xssDecode($strDirName);
	    $intTab = intval(Bingo_Http_Request::get('tab',3));
		
		$aldRet = Libs_Alading::getHashInfo('wordFilterHash', '');		
		if ($aldRet === false)
		{
			$errno = Tieba_Errcode::ERR_RPC_CALL_FAIL;
			$errmsg = Tieba_Error::getErrmsg($errno);
			Bingo_Log::warning('HGET redis fail,the hash is wordFilterHash');
			$this->_setRet($errno, $errmsg);
			return false;
		}
	
		$arrOutput = $this->_getOutput($aldRet,$strDirName);
		$arrOutputInfo['tab'] = $intTab;
		$arrOutputInfo['list'] = $arrOutput;
		$arrOutputInfo['total_count'] = count($arrOutput);
		$errno = Tieba_Errcode::ERR_SUCCESS;
		$errmsg = Tieba_Error::getErrmsg($errno);
		$this->_setRet($errno, $errmsg, $arrOutputInfo);
		return true;
	}
	
	private function  _getOutput($arrInput,$strQueryDirName)
	{
		$arrOutput = array();
		
		foreach($arrInput as $key => $value)
		{
			$strDirName = $key;			
			$arrWords = unserialize($value);
            
			if (is_null($arrWords))
			{
				continue;
			}
			
			if ('' !== $strQueryDirName)
			{
				if ($strQueryDirName == $strDirName)
				{
					foreach($arrWords as $strWord)
					{
						$arrOutput[] = array('dir_name' => $strDirName, 'filter_word' => $strWord);
					}
				}
			}
			else
			{
				foreach($arrWords as $strWord)
				{
					$arrOutput[] = array('dir_name' => $strDirName, 'filter_word' => $strWord);
				}
			}							
		}
        //var_dump($arrOutput);		
		return $arrOutput;
	}


	private  function _setRet($errno, $errmsg, $arrOutInfo = null)
	{
		$this->_arrTplVar['no'] = $errno;
		$this->_arrTplVar['msg'] = $errmsg;

		if (null !== $arrOutInfo)
		{
			$this->_arrTplVar['data'] = $arrOutInfo;
		}

		//Bingo_Page::setOnlyDataType("json");
		Bingo_Page::setTpl("alading/index.php");
	}

}
