<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * <AUTHOR>
 * @date 
 * @brief 
 *  
 **/
class delTuiAction extends Util_Actionbase 
{
    private $_arrCartoon = array();
    /**
    * @brief 
    * @param null
    * @return null
    **/
    function init() 
    {
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()) 
        {
            if (0 === $this->_intErrorNo ) 
            {
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = 'init return error!';
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }
    
    /**
    * @brief 
    * @param array
    * @return array
    **/
    public function process() 
    {
        $tuiId= intval(Bingo_Http_Request::get('tui_id', 0));

        if($tuiId<=0){
            Bingo_Log::warning("param error.");
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR,'param error');
        }

        // 一次拉取完
        $arrInput=array(
            "tui_id"=>$tuiId,
        );

        //获取推荐列表
        $arrRet = Tieba_Service::call('encourage','delTui',$arrInput,null,null,'post','php','utf-8');
        if(!$arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'])
        {
            Bingo_Log::warning("call encourage::delTui fail,input=[".serialize($arrInput)."] output=[".serialize($arrRet)."]");
            $this->_jsonRet($intErrNo,'Tieba_Service_Call_Encourage_delTui_Fail.[input]'.json_encode($arrInput).'[out]['.json_encode($arrRet).']');
            return false;
        }
        $this->_jsonRet($intErrNo,'success',$arrRet['data']);
        return true;
    }

    /**
    * @param array
    * @return array
    **/
    protected function _jsonRet($errno, $errmsg='', $arrExtData=array())
    {
        $arrRet = array(
            'no'=>intval($errno),
            'errMsg'=>strval($errmsg),
            'data'=>$arrExtData,
        );
        Bingo_Log::pushNotice("errno",$errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet,'UTF-8');
    } 
}
?>
