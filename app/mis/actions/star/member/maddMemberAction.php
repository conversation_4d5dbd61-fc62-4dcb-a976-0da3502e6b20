<?php
/**
 *Author: shiyibo
 *Filename: maddMember.php
 *Date: 2014-08-07
 *Desc: 
 */
class maddMemberAction extends Util_Actionbase{
	private $_arrParams = array(
		'real_name' => '',
		'address' => '',
		'region' => '',
		'province' => '',
		'birthday' => '',
		'identify' => '',
		'email'    => '',
	);
    private $_official_id = 0;
	protected $_errno     = 0;
	protected $_errmsg    = 'success';
	protected $_data      = array();

    const BIRTHDAY_FORMAT_LENGTH = 8;//生日的格式长度，例如19880907

    function init() {
        self::setUiAttr('COMMIT_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo ) {
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = 'init return error!';
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

	public function process(){
		$bolSucc = true;
		if (isset($_FILES['file']['tmp_name'])){
			$arrContents = file($_FILES['file']['tmp_name'],FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
			foreach ($arrContents as $strLine){
				if (!strlen(trim($strLine))){
					continue;
				}
				$arrLine = explode(':',$strLine);
				$ret = $this->check($arrLine);
				if (false === $ret){
					$bolSucc = false;
					Bingo_Log::warning($strLine.' is invalid');
					break;;
				}

				$ret = $this->addMember($arrLine);
				if (false === $ret){
					Bingo_Log::warning($strLine.' add fails');
					$bolSucc = false;
					break;
				}
			}
			if (false === $bolSucc){
				$this->_errno   = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
				return $this->_finish();
			}
		}else {
			$this->_errno = Tieba_Errcode::ERR_PARAM_ERROR;
			$this->_errmsg = 'file upload fail';
			return $this->_finish();
		}

		return $this->_finish();
	}

	protected function _finish(){
		$this->_arrTplVar['no']     = $this->_errno;
		$this->_arrTplVar['errmsg'] = $this->_errmsg;
		$this->_arrTplVar['data']   = $this->_data;
		echo $this->_errmsg;
		return true;

	}

    public function addMember($arrInput){
		$intOfficialId = intval(trim($arrInput[0]));
		$intUserId     = intval($arrInput[1]);
		$arrUserInfo   = array(
							'real_name'  => trim(strval($arrInput[2])),
							'address'    => trim(strval($arrInput[8])),
							'region'     => trim(strval($arrInput[7])),
							'province'   => trim(strval($arrInput[7])),
							'birthday'   => trim(strval($arrInput[6])),
							'identify'   => trim(strval($arrInput[3])),
							'email'      => trim(strval($arrInput[4])),
							);
        $arrParam  = array(
            'user_id' => $intUserId,
            'official_id' =>$intOfficialId,
            'user_info' => $arrUserInfo,
        );
        $arrOut = Tieba_Service::call('star','addNewOfficialMember',$arrParam);
        if($arrOut == false || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']){
            Bingo_Log::warning('add member fail with input '.serialize($arrParam).' output '.serialize($arrOut));
			$this->_errno = $arrOut['errno'];
			$strMsg = empty($arrOut['errmsg']) ? ' execute fails ' : $arrOut['errmsg'];
			$this->_errmsg = '['.$arrParam['user_id'].'] '.$strMsg;
			return false;
        }
        return true;
    }

	public function check($arrInput){
		$arrParams   = array(
							'real_name'  => trim(strval($arrInput[2])),
							'address'    => trim(strval($arrInput[8])),
							'region'     => trim(strval($arrInput[7])),
							'province'   => trim(strval($arrInput[7])),
							'birthday'   => trim(strval($arrInput[6])),
							'identify'   => trim(strval($arrInput[3])),
							'email'      => trim(strval($arrInput[4])),
							);

        $intOfficialId = intval(trim($arrInput[0]));
		/*$arrInput=array('forum_id'=>$intOfficialId);
		$arrRet=Tieba_Service::call('forum','getForumAttr',$arrInput);
	    if(isset($arrRet['output']['star_official'])) {
			$register_time=intval($arrRet['output']['star_official']['register_time']);
			if(time()<$register_time) {
				Bingo_Log::warning('now can not register');
				throw new Util_Exception(Tieba_Errcode::ERR_PARAM_ERROR);
			}
		}
		else {
			   Bingo_Log::warning('it is not the star_official forum');
			   //throw new Util_Exception(Tieba_Errcode::ERR_PARAM_ERROR);
			   //return false;
		}*/
        foreach($arrParams as $value){
            if($value == ''){
				Bingo_Log::warning('param error '.serialize($arrParams));
				$this->_errno = Tieba_Errcode::ERR_PARAM_ERROR;
				$this->_errmsg='['.$arrInput[1].'] param error';
				return false;
            }
        }
        if($intOfficialId <= 0){
			Bingo_Log::warning('official_id invalid '.$intOfficialId);
			$this->_errno = Tieba_Errcode::ERR_PARAM_ERROR;
			$this->_errmsg='['.$arrInput[1].'] param error';
			return false;
        }
        if(strlen($arrParams['birthday']) != self::BIRTHDAY_FORMAT_LENGTH){
			Bingo_Log::warning('birthday invalid '.$arrParams['birthday']);
			$this->_errno = Tieba_Errcode::ERR_PARAM_ERROR;
			$this->_errmsg='['.$arrInput[1].'] param error';
			return false;
        }
	}
}
