<?php
/**
 * Created by PhpStorm.
 * User: huxiaomei
 * Date: 2018/12/19
 * Time: 下午6:50
 */
class listAction extends Util_Actionbase{

    const PAGE_SIZE = 30;

    /**
     * @return bool
     */
    function init(){
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }
    /**
     * @return bool
     */
    public function process(){
        $strCurUserName = Util_Actionbaseext::getCurUserName();
        $intPn = intval(Bingo_Http_Request::getNoXssSafe('pn', 1));
        $intSize = intval(Bingo_Http_Request::getNoXssSafe('size', self::PAGE_SIZE));
        $strFname = strval(Bingo_Http_Request::getNoXssSafe('forum_name', ''));
        $intFid = intval(Bingo_Http_Request::getNoXssSafe('forum_id', 0));
        $reqs = Bingo_Http_Request::getNoXssSafe('reqs', array());
        $type = Bingo_Http_Request::getNoXssSafe('type', '');
        $arrOut = array(
            'list' => array(),
            'page' => array(
                'current_pn' => 1,
                'total_pn' => 0,
                'total_count' => 0,
            ),
            'reqs' => $reqs
        );

        if (!empty($intFid) || !empty($strFname)) {
            if (!empty($intFid)){
                $arrFids = array($intFid);
            }else{
                $intFid = self::_getFidByFname($strFname);
                if ($intFid && intval($intFid) > 0){
                    $arrFids = array($intFid);
                }else{
                    return self::buildRes(1,'无效的吧名',array());
                }
            }
            $intTotal = 1;
        }else{
            $arrInput = array(
                'offset' => ($intPn - 1)*$intSize,
                'count' => $intSize
            );
            $arrRet  = Tieba_Service::call('bawu', 'getMisPriforumInfo', $arrInput);
            if( false == $arrRet || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS ){
                Bingo_Log::warning('call bawu getMisPriforumInfo fail input[' . serialize($arrInput) . "] out[" . serialize($arrRet). "]");
                return self::buildRes(1,'数据获取失败',array());
            }
            if( empty($arrRet['data']['list']) || $arrRet['data']['total_count'] == 0 || $arrRet['data']['total'] == 0){
                return self::buildRes(Tieba_Errcode::ERR_SUCCESS,'success',$arrOut);
            }
            $arrFids = array();
//        $arrRet['data']['list'] = Bingo_Encode::convert($arrRet['data']['list'], Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
            foreach( $arrRet['data']['list'] as $key => $value ){
                $arrFids[] = intval($value['forum_id']);
//            $arrFnames[$value['forum_id']] = $value['forum_name'];
            }
        }
        $arrForumInfo = self::_mgetForumAttrsByFid($arrFids);
        $arrMemberInfo = self::_mgetForumMemberNum($arrFids);
        $arrPostInfo = self::_mgetForumPostNum($arrFids);
        $arrBawuInfo = self::_mgetBawuNum($arrFids);
        if(!$arrForumInfo){
            Bingo_Log::warning('call _mgetForumAttrsByFid fail');
            return self::buildRes(1,'数据获取失败',array());
        }
        $arrList = array();
        foreach( $arrFids as $arrFid ){
            if ($arrForumInfo[$arrFid]['attrs']['is_private_forum'] != 1 || !isset($arrForumInfo[$arrFid]['forum_name']['forum_name']) || empty($arrForumInfo[$arrFid]['forum_name']['forum_name'])){
                continue;
            }
            $list['forum_id'] = intval($arrFid);
            $list['forum_name'] = isset($arrForumInfo[$arrFid]['forum_name']['forum_name']) && !empty($arrForumInfo[$arrFid]['forum_name']['forum_name']) ? $arrForumInfo[$arrFid]['forum_name']['forum_name'] : '';
            $list['forum_ctime'] = isset($arrForumInfo[$arrFid]['attrs']['forum_ctime']) ? date('Y-m-d H:i:s',$arrForumInfo[$arrFid]['attrs']['forum_ctime']) : 0;
            $list['member_count'] = isset($arrMemberInfo[$arrFid]['member_count']) && intval($arrMemberInfo[$arrFid]['member_count']) ? intval($arrMemberInfo[$arrFid]['member_count']) : 0;
            $list['post_info'] = array(
                'thread_num' => isset($arrPostInfo[$arrFid]['thread_num']) && intval($arrPostInfo[$arrFid]['thread_num']) ? intval($arrPostInfo[$arrFid]['thread_num']) : 0,
                'post_num' => isset($arrPostInfo[$arrFid]['post_num']) && intval($arrPostInfo[$arrFid]['post_num']) ? intval($arrPostInfo[$arrFid]['post_num']) : 0
            );
            $list['bawu_info'] = array(
                'manager_num' => isset($arrBawuInfo[$arrFid]['manager_num']) && intval($arrBawuInfo[$arrFid]['manager_num']) ? intval($arrBawuInfo[$arrFid]['manager_num']) : 0,
                'pri_manage_assist_num' => isset($arrBawuInfo[$arrFid]['pri_manage_assist_num']) && intval($arrBawuInfo[$arrFid]['pri_manage_assist_num']) ? intval($arrBawuInfo[$arrFid]['pri_manage_assist_num']) : 0,
                'pri_content_assist_num' => isset($arrBawuInfo[$arrFid]['pri_content_assist_num']) && intval($arrBawuInfo[$arrFid]['pri_content_assist_num']) ? intval($arrBawuInfo[$arrFid]['pri_content_assist_num']) : 0,
            );
            $list['forum_detail'] = "当前吧主数：".$list['bawu_info']['manager_num']."\r\n 当前管理吧务数：".$list['bawu_info']['pri_manage_assist_num']."\r\n 当前内容吧务数：".$list['bawu_info']['pri_content_assist_num']."\r\n 申请时间：".$list['forum_ctime'];
            $list['forum_info'] = "贴吧会员数：".$list['member_count']."\r\n 帖子数：".$list['post_info']['thread_num']."\r\n 回复数：".$list['post_info']['post_num'];
            $list['forum_detail'] = Bingo_Encode::convert($list['forum_detail'], Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
            $list['forum_info'] = Bingo_Encode::convert($list['forum_info'], Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
            $arrList[] = $list;
        }
        $intTotal = isset($arrRet['data']['total_count']) ? $arrRet['data']['total_count'] : $intTotal;
        if ($reqs && $intFid){
            $arrList[0]['reqs'] = $reqs;
        }
        if ($type && $intFid){
            $arrList[0]['type'] = $type;
        }
        $arrOut = array(
            'list' => $arrList,
            'page' => array(
                'current_pn' => $intPn,
                'total_pn' => ceil($intTotal / $intSize),
                'total_count' => $intTotal,
            )
        );
        return self::buildRes(0,'success',$arrOut);
    }
    /**
     * @param $intErrno
     * @param $strErrmsg
     * @param $arrData
     * @return bool
     */
    public static function buildRes($intErrno,$strErrmsg,$arrData){

        $arrRet = array(
            'no'    => $intErrno,
            'error' => Bingo_Encode::convert($strErrmsg, Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8),
            'data'  => $arrData,
        );
        $strRet = Bingo_String::array2json($arrRet);
        echo $strRet;
        return true;
    }

    /**
     * @param $arrForumNames
     * @return bool
     */
    private static function _getFidByFname($forumName) {
        $arrInput = array(
            "query_words" => array( //吧名
                0 => $forumName //吧名
            )
        );
        $arrOutput = Tieba_Service::call('forum', 'getFidByFname', $arrInput);
        if ($arrOutput === false || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call forum getForumAttr fail input[' . serialize($arrInput) . "] out[" . serialize($arrOutput). "]");
            return false;
        }
        $arrRet = array();
        if (isset($arrOutput['forum_id'][0]['forum_id']) && intval($arrOutput['forum_id'][0]['forum_id'])){
            $forumId = $arrOutput['forum_id'][0]['forum_id'];
        }else{
            $forumId = 0;
        }
        return $forumId;
    }

    /**
     * @param $arrForumIds
     * @return bool
     */
    private static function _mgetForumAttrsByFid($arrForumIds) {
        $arrInput = array(
            'forum_id' => (array)$arrForumIds,
        );
        $arrOutput = Tieba_Service::call('forum', 'mgetBtxInfo', $arrInput);
        if ($arrOutput === false || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call forum getForumAttr fail input[' . serialize($arrInput) . "] out[" . serialize($arrOutput). "]");
            return false;
        }
        $arrRet = array();
        if (isset($arrOutput['output']) && !empty($arrOutput['output'])){
            $arrRet = $arrOutput['output'];
        }
        return $arrRet;
    }

    /**
     * @param $arrForumIds
     * @return bool
     */
    private static function _mgetForumMemberNum($arrForumIds) {
        $arrInput = array(
            'forum_ids' => $arrForumIds,
        );
        $arrOutput = Tieba_Service::call('perm', 'mgetForumMemberInfo', $arrInput);
        if ($arrOutput === false || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call forum getForumAttr fail input[' . serialize($arrInput) . "] out[" . serialize($arrOutput). "]");
            return false;
        }
        $arrRet = array();
        if (!isset($arrOutput['output']) || empty($arrOutput['output'])){
            Bingo_Log::warning('call forum getForumAttr fail input[' . serialize($arrInput) . "] out[" . serialize($arrOutput). "]");
            return false;
        }
        return $arrOutput['output'];
    }

    /**
     * @param $arrForumIds
     * @return bool
     */
    private static function _mgetForumPostNum($arrForumIds) {
        $arrOutput = array();
        $multiName = 'mgetForumPostNum';
        $input = array();
        $multiServiceInfo = array();
        foreach ($arrForumIds as $forumId) {
            $input[$forumId] = array(
                "need_abstract" => 0,
                "forum_id" => $forumId,
                "need_photo_pic" => 0,
                "need_user_data" => 0,
                "offset" => 0,
                "res_num" => 0,
                "forum_name" => "",
                "need_forum_name" => 0, //是否获取吧名
                "only_forum_num" => 1
            );
            $multiServiceInfo[$forumId] = array(
                'serviceName' => 'post',
                'method' => 'getFrs',
                'input' => $input[$forumId],
                'ie' => 'utf-8',
            );
        }
        $multiResult = self::multiCall($multiName,$multiServiceInfo);
        foreach ($arrForumIds as $forumId) {
            $postInfo = $multiResult[$forumId];
            if( false === $postInfo || $postInfo['errno']  != Tieba_Errcode::ERR_SUCCESS || !isset($postInfo['output']['forum_info']) || empty($postInfo['output']['forum_info'])){
                Bingo_Log::warning("call post::getFrs fail. input[".serialize($input[$forumId])."] output[".serialize($postInfo)."]");
                continue;
            }else{
                $forumInfo = $postInfo['output']['forum_info'];
                $arrOutput[$forumId] = array(
                    'thread_num' => isset($forumInfo['thread_num']) && intval($forumInfo['thread_num']) ? $forumInfo['thread_num'] : 0,
                    'post_num' => isset($forumInfo['post_num']) && intval($forumInfo['post_num']) ? $forumInfo['post_num'] : 0
                );
            }
        }

        return $arrOutput;
    }


    /**
     * [_mgetBawuNum description]
     * @return [type] [description]
     */
    private static function _mgetBawuNum($arrForumIds) {
        $arrRes = array();
        $multiName = 'mgetBawuNum';
        $input = array();
        $multiServiceInfo = array();
        foreach ($arrForumIds as $forumId) {
            $input[$forumId] = array(
                "forum_id" => $forumId,
            );
            $multiServiceInfo[$forumId] = array(
                'serviceName' => 'perm',
                'method' => 'getBawuList',
                'input' => $input[$forumId],
                'ie' => 'utf-8',
            );
        }
        $multiResult = self::multiCall($multiName,$multiServiceInfo);

        foreach ($arrForumIds as $forumId) {
            $arrOut = $multiResult[$forumId];
            $arrRes[$forumId] = array(
                'manager_num' => 0,
                'pri_manage_assist_num' => 0,
                'pri_content_assist_num' => 0
            );
            if(false === $arrOut) {
                Bingo_Log::fatal("call perm:getBawuList failed. input=".serialize($input[$forumId])." output=".serialize($arrOut));
                continue;
            }else if(Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']){
                Bingo_Log::warning("call perm:getBawuList errno not 0. input=".serialize($input[$forumId])." output=".serialize($arrOut));
                continue;
            }else{
                $arrRes[$forumId] = array(
                    'manager_num' => isset($arrOut['output']['manager']) && !empty($arrOut['output']['manager']) && is_array($arrOut['output']['manager']) ? count($arrOut['output']['manager']) : 0,
                    'pri_manage_assist_num' => isset($arrOut['output']['pri_manage_assist']) && !empty($arrOut['output']['pri_manage_assist']) && is_array($arrOut['output']['pri_manage_assist']) ? count($arrOut['output']['pri_manage_assist']) : 0,
                    'pri_content_assist_num' => isset($arrOut['output']['pri_content_assist']) && !empty($arrOut['output']['pri_content_assist']) && is_array($arrOut['output']['pri_content_assist']) ? count($arrOut['output']['pri_content_assist']) : 0
                );
            }
        }
        return $arrRes;
    }

    /**
     * [multiCall ]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    private static function multiCall($multiName,$multiService){
        $objRalMulti = new Tieba_Multi($multiName);
        foreach ($multiService as $k => $v){
            $objRalMulti->register($k, new Tieba_Service($v['serviceName']), $v);
        }
        $objRalMulti->call();
        foreach ($multiService as $k => $v){
            $arrResult[$k] = $objRalMulti->getResult($k);
        }
        return $arrResult;
    }

}