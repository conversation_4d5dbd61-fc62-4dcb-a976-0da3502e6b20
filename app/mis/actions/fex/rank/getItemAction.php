<?php
/**
 * <AUTHOR>
 * @desc 获取作品信息，作品分为图片作品与视频作品两种：
 * 公共字段为：thread_id，title，user_name，thread_type和thumbnail_url，其中thread_type为'picture'或'video'
 * 图片作品独有字段为：pic_url数组，保存10个以内图片url
 * 视频作品独有字段为：video_url和video_duration
 * @since 20170508
 *
 */
class getItemAction extends Util_Fex_ActionBase {

    const CORE_MULTI_KEY = 'mis_fex_rank_multi';
    private static $_objRalMulti = null;
    protected $arrIntRequired = array('thread_id');

    /**
     * @brief process
     * @return: true if success. warning if fail.
     **/
    public function process() {
        self::$_objRalMulti = new Util_Fex_Service( self::CORE_MULTI_KEY );
        if(self::$_objRalMulti == null) {
            $this->printOut();
            return;
        }
        $intTid = intval($this->get('thread_id', 0));
        $arrOutput = null;
        if ( 0 !== $intTid ) {
            // 查询帖子信息
            $arrInput = array(
                'thread_ids'        =>  array(
                    0   =>  $intTid
                ),
                'need_abstract'     =>  1,
                'forum_id'          =>  0,
                'need_photo_pic'    =>  1,
                'need_user_data'    =>  1,
                'icon_size'         =>  1,
                'need_forum_name'   =>  0,
                'call_from'         =>  'mis_fex_rank',
            );
            $arrMultiInput = array(
                'serviceName'   =>  'post',
                'method'        =>  'mgetThread',
                'input'         =>  $arrInput,
                'ie'            =>  'utf-8',
                'httpMethod'    =>  'post',
            );
            self::$_objRalMulti->register( 'mgetThread', $arrMultiInput );

            // 查询作品内容
            $arrInput = array(
                'thread_id'            =>  $intTid,
                'offset'                =>  0,
                'res_num'               =>  15,
                'see_author'            =>  1,
                'has_comment'           =>  0,
                'has_mask'              =>  0,
                'has_ext'               =>  1,
                'need_set_pv'           =>  0,
                'structured_content'    =>  1,
            );
            $arrMultiInput = array(
                'serviceName'   =>  'post',
                'method'        =>  'getPostsByThreadId',
                'input'         =>  $arrInput,
                'ie'            =>  'utf-8',
                'httpMethod'    =>  'post',
            );
            self::$_objRalMulti->register( 'getPostsByThreadId', $arrMultiInput );

            $ret = array();
            Bingo_Timer::start('rank_multi_call');
            self::$_objRalMulti->call();
            $ret = self::$_objRalMulti->getAllResult();
            Bingo_Timer::end('rank_multi_call');
            $arrThreadInfo = $ret['mgetThread'];
            $arrPostInfo = $ret['getPostsByThreadId'];
            if ( false === $arrThreadInfo || Tieba_Errcode::ERR_SUCCESS !== $arrThreadInfo['errno'] ) {
                $arrOutput = array(
                    'status'    =>  $arrThreadInfo['errno'],
                    'msg'       =>  'mgetThread: '.$arrThreadInfo['errmsg'],
                    'data'      =>  array(),
                );
            } else if ( false === $arrPostInfo || Tieba_Errcode::ERR_SUCCESS !== $arrPostInfo['errno'] ) {
                $arrOutput = array(
                    'status'    =>  $arrPostInfo['errno'],
                    'msg'       =>  'getPostsByThreadId: '.$arrPostInfo['errmsg'],
                    'data'      =>  array(),
                );
            } else {
                $arrOutput = array(
                    'status'    =>  Tieba_Errcode::ERR_SUCCESS,
                    'msg'       =>  'success',
                    'data'      =>  array(),
                );
                // 录入帖子信息
                $arrOutput['data']['thread_id'] = $intTid;
                $arrOutput['data']['title'] = $arrThreadInfo['output']['thread_list'][$intTid]['title'];
                $arrOutput['data']['user_id'] = $arrThreadInfo['output']['thread_list'][$intTid]['user_id'];
                $intUid = $arrThreadInfo['output']['thread_list'][$intTid]['user_id'];
                $arrOutput['data']['user_name'] = $arrThreadInfo['output']['thread_user_list'][$intUid]['user_name'];
                // 添加帖子类型字段
                $intThreadTypes = $arrThreadInfo['output']['thread_list'][$intTid]['thread_types'];
                $arrThreadType = Tieba_Type_Thread::getTypeArray($intThreadTypes);
                if ( true === $arrThreadType['is_pic'] ) {
                    $arrOutput['data']['thread_type'] = 'picture';
                    $arrRawMedia = reset( $arrThreadInfo['output']['thread_list'][$intTid]['raw_abstract_media'] );
                    $arrOutput['data']['thumbnail_url'] = $arrRawMedia['big_pic'];
                    $arrMedia = $arrThreadInfo['output']['thread_list'][$intTid]['media']; 
                    if ( !empty($arrMedia) ) {
                        $arrPicInfo = reset($arrMedia);
                        $arrOutput['data']['thumbnail_width'] = $arrPicInfo['picInfo']['big']['width'];
                        $arrOutput['data']['thumbnail_height'] = $arrPicInfo['picInfo']['big']['height'];
                    } else {
                        $arrOutput['data']['thumbnail_width'] = null;
                        $arrOutput['data']['thumbnail_height'] = null;
                    }
                    $intCount = 10;
                    foreach ( $arrPostInfo['output']['output'][0]['post_infos'] as $post ) {
                        foreach ( $post['content'] as $content ) {
                            if ( 'img' === $content['tag']) {
                                $arrOutput['data']['pic_url'][] = array(
                                    'url'   =>  $content['src'],
                                    'width' =>  $content['width'],
                                    'height'=>  $content['height'],
                                );
                                --$intCount;
                                if( 0 === $intCount ) {
                                    break;
                                }
                            }
                        }
                        if( 0 === $intCount ) {
                            break;
                        }
                    }
                } else if ( true === $arrThreadType['is_movideo'] ) {
                    $arrRawVideoInfo = $arrThreadInfo['output']['thread_list'][$intTid]['video_info'];
                    $arrVideoInfo = array(
                        'thread_type'       =>  'video',
                        'thumbnail_url'     =>  $arrRawVideoInfo['thumbnail_url'],
                        'thumbnail_width'   =>  $arrRawVideoInfo['thumbnail_width'],
                        'thumbnail_height'  =>  $arrRawVideoInfo['thumbnail_height'],
                        'video_url'         =>  $arrRawVideoInfo['video_url'],
                        'video_width'       =>  $arrRawVideoInfo['video_width'],
                        'video_height'      =>  $arrRawVideoInfo['video_height'],
                        'video_duration'    =>  $arrRawVideoInfo['video_duration'],
                    );
                    $arrOutput['data'] = array_merge( $arrOutput['data'], $arrVideoInfo );
                } else {
                    $arrOutput['status'] = 1;
                    $arrOutput['msg'] = '帖子类型错误（非图片贴或视频贴）';
                    $arrOutput['data'] = '';
                }
            }
        }
        $this->printOut($arrOutput);
    }
}
