<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2017/4/6
 * Time: 16:14
 */
class getRealtimeStatAction extends Util_Sala_ActionBase {

    static $_arrStatName = array(
        'online_count' => '在线人数',
        'matching_count' => '匹配人数',
        'session_count' => '会话人数',
        'comm_quality' => '通信质量',
    );

    /**
     * @brief
     * @param
     */
    public function process(){
        $arrRet = Util_Sala_Rpc::callService('salastat', 'loadRealtimeStat');
        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call service salastat::loadRealtimeStat fail, ret='.serialize($arrRet));
            return;
        }

        $arrData = array();
        foreach($arrRet['data'] as $k => $v) {
            if(isset(self::$_arrStatName[$k])) {
                $arrData[] = array(
                    'name' => self::$_arrStatName[$k],
                    'value' => $v,
                );
            }
        }

        self::_displayJson(Tieba_Errcode::ERR_SUCCESS, $arrData);
    }

}