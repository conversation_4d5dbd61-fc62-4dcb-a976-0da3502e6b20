<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-01-05 14:23:53
 * @version
 */
class addtaskAction extends Util_Actionbase{
	function init(){
		self::setUiAttr('BROWSE_UI');
		if (false === parent::init()){
			if(0 === $this->_intErrorNo){
				$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}

	public function process(){
	    $send_uid = intval(Bingo_Http_Request::getNoXssSafe('send_uid', ''));
	    $send_uname = Bingo_Http_Request::getNoXssSafe('send_uname', '');   
	    $task_type = intval(Bingo_Http_Request::getNoXssSafe('task_type', 5));  
	    $task_content = Bingo_Http_Request::getNoXssSafe('content', '');  
	    $task_ext = Bingo_Http_Request::getNoXssSafe('task_ext', '');  
	    $receive_type = intval(Bingo_Http_Request::getNoXssSafe('receive_type', 1));  
	    $task_start_time = Bingo_Http_Request::getNoXssSafe('task_start_time', '');    
	    $task_invalid_time = Bingo_Http_Request::getNoXssSafe('task_invalid_time', '');   
	    $md5 = Bingo_Http_Request::getNoXssSafe('md5', '');  
		$title = Bingo_Http_Request::getNoXssSafe('task_title', ''); 
		$image = Bingo_Http_Request::getNoXssSafe('task_image', '');
		$link = Bingo_Http_Request::getNoXssSafe('task_link', '');
		$msg_type = Bingo_Http_Request::getNoXssSafe('msg_type', 0);
        $task_push_count = 0;
		
        $task_content = self::convertToUTF8($task_content);
        
	    $arrInput = array();
	    $ret = Tieba_Service::call ( "msgpush", "messageIdAlloc", $arrInput, NULL,NULL,'post','php','utf-8');
	    if ($ret['errno'] !== 0) {
	        Bingo_log::warning("messageIdAlloc fail".var_export($ret, true));
	        //header("location: http://static.tieba.baidu.com/tb/error.html?ErrType=0");
	        //return ;
	    }
	    $task_id = $ret['id'];
	    
	    if ($task_type !== 7 && $receive_type == 4) { //组播需要传文件
    	    $ret = self::processfile($task_type, $task_id, $md5);
    	    if ($ret['error'] !== 0) {
    	        Bingo_log::warning("processfile".var_export($ret, true));
    	        echo json_encode($ret);
    	        header("location: http://static.tieba.baidu.com/tb/error.html?ErrType=0");
    	        return ;
    	    }
	    }
	    
	    if ($task_type == 6 && $receive_type == 1) {  //暂不支持
	        header("location: http://static.tieba.baidu.com/tb/error.html?ErrType=0");
	    }
	    
	    if ($receive_type == 1) {  //如果是广播  需要获取总的推送量
	        $task_push_count = self::getPushCount($task_type);
	    }	   

	    $arrInput = array(
	        'task_id' => $task_id,
	    	'send_uid'  => $send_uid,
            'send_uname'  => $send_uname,
            'task_type'  => $task_type,
            'task_content'  => $task_content,
            'task_ext'  => $task_ext,
	    	'task_status'  => 0,
            'receive_type'  => $receive_type,
            'task_start_time'  => $task_start_time,
	    	'task_invalid_time'  => $task_invalid_time,
	        'task_md5' => $md5,
			'task_bk_3' => $msg_type,
	        'task_push_count' => $task_push_count,
	    );
		
		if($task_type == 6){
			$pattern = "tieba.baidu.com";
			$newLink = "";
			if(strpos($link, $pattern) !== false){
				if(strpos($link, "kw=") !== false){
					$subStr = explode('=', $link);
					$prefix = "frs:";
					$newLink = sprintf("%s%s", $prefix, $subStr[2]);
				}else{
					$prefix = "pb:";
					$subStr = explode('/', $link);
					$newLink = sprintf("%s%s", $prefix, $subStr[4]);
				}
			}else{
				$prefix = "web:";
				$newLink = sprintf("%s%s", $prefix, $link);
			}
			$send_content = array(
				'eventId' => 109,
				'userMsg' => $task_content,
				'eventParam' => array(
					'groupId'    => 0,
					'groupImage' => $image,
					'groupName'  => $title,
					'title'      => $msg_type,
					'eventLink'  => $newLink,
				),
			);
			$arrInput['task_content'] = json_encode($send_content);
		}
		Bingo_log::warning("call addPushTask input" . var_export($arrInput,true));
	    $ret = Tieba_Service::call ( "msgpush", "addPushTask", $arrInput, NULL,NULL,'post','php','utf-8');
	    if ($ret['errno'] !== 0) {
	        Bingo_log::warning("addPushTask fail".var_export($ret, true));
	        header("location: http://static.tieba.baidu.com/tb/error.html?ErrType=0");
	        return ;
	    }  
	    
	    echo json_decode($ret);
		 if($task_type == 5) {
			 header("location: /newclientpush/pmsg");
		 }elseif($task_type == 6) {
			 header("location: /newclientpush/groupdynamic");
		 }
        return true;
	}
	
	public static function processfile($task_type, $task_id, &$md5){
        Bingo_log::warning("process input file ".var_export($_FILES['id_file'], true));
        $bscp_key = self::getDataId($task_type);
	    $task_type = self::gettypestr($task_type);
	    if (empty($_FILES) || !isset($_FILES['id_file']) || $_FILES["id_file"]["error"] > 0){
	        Bingo_log::warning("==============".var_export($_FILES['id_file'], true));
            $arrOut = array(
                'error' => -1,
                'errmsg' => '文件传输错误',
            );
            return $arrOut;
            //echo json_encode($arrOut);
        }
    	//将用户上传文件的文件保存在mis的data目录下
        $path = dirname(__FILE__) . "/data";
        $strPath = "$path/$task_type";
	
        $ret = self::_mkdirIfNotExists($strPath);
        if (!$ret) {
            Bingo_log::warning("=======$strPath=======".var_export($_FILES['id_file'], true));
            $arrOut = array(
                'error' => -1,
                'errmsg' => '文件传输错误',
            );
            return $arrOut;
        }

		$md5 = md5_file($_FILES['id_file']['tmp_name']);
		
		$strFileName = "$strPath"."/".$md5.".id.txt";
		if(file_exists($strFileName)){
		    unlink($strFileName);
		}
		
		$ret = move_uploaded_file($_FILES['id_file']['tmp_name'], $strFileName);
		if(!$ret){
		    copy($_FILES['id_file']['tmp_name'], $strFileName);
		}
		if (!file_exists($strFileName)) {
		    Bingo_log::warning("=======$strFileName=======".var_export($_FILES['id_file'], true));
            $arrOut = array(
                'error' => -1,
                'errmsg' => '文件传输错误',
            );
            return $arrOut;
		}
		
	    //实际发往推送模块的文件名
		$strScpFileName = "$strPath/$task_id.$md5.id.txt";
		$ret = copy("$strFileName", "$strScpFileName");
		if ($ret === false) {
		    Bingo_log::warning("=======$strScpFileName=======".var_export($ret, true));
            $arrOut = array(
                'error' => -1,
                'errmsg' => '文件传输错误',
            );
            return $arrOut;
		}
		
		//发送文件
		// online
		$local_machine = exec("hostname -i | head -n 1");
		$cmd = "bscp --setinfo ftp://$local_machine/$strScpFileName $bscp_key && echo 0 || echo 1";
		//$cmd = "noahdt add $bscp_key bscp://$strScpFileName && echo 0 || echo 1";
		// FIXME: offline
		//$cmd = "cp $strScpFileName /home/<USER>/scripts/gpush/data/$task_type/new_task && echo 0 || echo 1";
	
		$ret = exec($cmd);
		if ($ret == 1) {
		    Bingo_log::warning("===$cmd===".var_export($ret, true));
            $arrOut = array(
                'error' => -1,
                'errmsg' => '文件传输错误',
            );
            return $arrOut;
		}
		
        $arrOut = array(
            'error' => 0,
        );
        return $arrOut;
	}
	
    private static function _mkdirIfNotExists($dir) {
        if (file_exists($dir)) {
            if (!is_dir($dir)) {
                if (!unlink($dir)) {
                    Bingo_Log::warning("fail to unlink: $dir");
                    return false;
                }
                if (!mkdir($dir)) {
                    Bingo_Log::warning("fail to mkdir: $dir");
                    return false;
                }
                return true;
            } else {
                return true;
            }
        } else {
            if (!mkdir($dir, 0777, true)) {
                Bingo_Log::warning("fail to mkdir: $dir");
                return false;
            }
            return true;
        }
    }
    
	public static function getPushCount($task_type){
		$service = '';
		$method = '';

	    if ($task_type == 5) {
		    $service = 'msgpush';
		    $method = 'getMaxIdForBroadCast';
	    }
	    elseif ($task_type == 6) {
	        return 0;
		    $service = 'msgpush';
		    $method = '';
	    }
	    else {
	        return 0;
	    }
	    
	    $arrInput = array();
		$ret = Tieba_Service::call($service, $method, $arrInput);
		if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
		    Bingo_Log::warning("call $service/$method fail, input: ".serialize($arrInput)." ret: ".serialize($ret));
		    return 0;
		}
		
		return $ret['output'];
	}
	
	public static function gettypestr($task_type){
	    if ($task_type == 5) {
	        $typestr = 'new_pletter_gpush';
	    }
	    elseif ($task_type == 6) {
	        $typestr = 'group_dynamic_gpush';
	    }
	    return $typestr;
	}
	
	public static function getDataId($task_type){
	    $dataid = '';
	    if ($task_type == 5) {
	        $dataid = 'data://data/cd81f2db57834b379dd16fdfe6fade7d';
	    }
	    elseif ($task_type == 6) {
	        $dataid = 'data://data/cc452ad142df4719a744e54be3cab39e';
	    }
	    
/*        if ($task_type == 5) {
	        $dataid = '/bj/FORUM/new_pletter_gpush';
	    }
	    elseif ($task_type == 6) {
	        $dataid = '/bj/FORUM/group_dynamic_gpush';
	    }*/
	    return $dataid;
	}
	
    public static function convertToUTF8($str){
    	if (empty($str)){
    		return '';
    	}    	
    	$strOut = $str;
    	$objEncode = new Bingo_Encode_Uconv();
    	if (!$objEncode->isUtf8($str))
    	{
    		$strOut = mb_convert_encoding($str,'UTF-8','GBK');
    	}
    	return $strOut;
    }
}

?>
