<?php
    /**
     * Created by PhpStorm.
     * User: he<PERSON><PERSON><PERSON>
     * Date: 2018/10/9
     * Time: 下午12:14
     */
    class delForumConfigAction extends Util_Actionbase {

        /**
         * init
         * @return boolean
         */
        function init() {
            self::setUiAttr('COMMIT_UI');
            return true;
        }

        /**
         * @return boolean
         */
        public function process()
        {
            $id = intval(Bingo_HTTP_Request::getNoXssSafe('id', 0));

            if (empty($id)) {
                Bingo_Log::warning('param error, input:['.serialize(Bingo_HTTP_Request::getGet()).']');
                $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
                return false;
            }

            $arrInput = array(
                'id'       => $id,
            );

            $arrRet = Tieba_Service::call('smartapp', 'delForumConfig', $arrInput);
            if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                Bingo_Log::warning(__METHOD__ . ' call smartapp:delForumConfig failed! input:[' . serialize($arrInput) . ']' . 'output:[' . serialize($arrRet) . ']');
                return $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }

            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS);
            return true;
        }


        /**
         * @param $intErrno
         * @param array $arrData
         */
        private function _jsonRet($intErrno, $arrData = array()){
            $arrRet = array(
                'errno' => $intErrno,
                'errmsg' => Tieba_Error::getErrmsg($intErrno),
                'data' => $arrData,
            );
            foreach($arrRet as $k=>$v){
                Bingo_Page::assign($k,$v);
            }

            Bingo_Page::setOnlyDataType("json");
            Bingo_Http_Response::contextType('application/json');
        }

    }