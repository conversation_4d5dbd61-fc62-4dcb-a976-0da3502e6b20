<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file listAction.php
 * <AUTHOR>
 * @date 2016/09/26 18:35:28
 * @brief 
 *  
 **/


class downloadAction extends Util_Actionbase {

    const DEFAULT_PN = 1;
    const DEFAULT_RN = 20;
    const DOWNLOAD_FILENAME = "bzfuncapply_";
    private static $_status_map = array(
    	'open'   => 1,
    	'refuse' => 2,
    	'wait'   => 3,
    	'auto_pass'   => 4,
    	'manual_pass' => 5,
    );
    private static $_func_map = array(
        1 => '头图',
        2 => '第三条置顶',
        3 => '发帖前缀',
        4 => '话题贴',
        5 => '验证马',
        6 => '静止蛙',
        7 => '扩充小吧',
        8 => '右侧边栏',
    );
    private static $_assist_map = array(
        1 => 15,
        2 => 20,
        3 => 30,
    );
    private static $_arrFdirs = array();
    private static $_arrForumId = array();
    private static $_excel_head = array(
        '吧名',
        '功能',
        '审核人',
        '值',
    );
	/**
	 * @param 
	 * @return bool
	 */
	function init(){
		self::setUiAttr('BROWSE_UI');
		if (false === parent::init()){
			if(0 === $this->_intErrorNo){
				$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}
	/**
	 * @param 
	 * @return array
     */
    public function process() {
    	$intErrno  = Tieba_Errcode::ERR_SUCCESS;
        $intPn     = (int)Bingo_Http_Request::get('pn', self::DEFAULT_PN);
        $intRn     = (int)Bingo_Http_Request::get('rn', self::DEFAULT_RN);
        $strFdir   = Bingo_Http_Request::getNoXssSafe('fdir','');
        $intFuncId = (int)Bingo_Http_Request::get('func_id',-1);
        $strStatus = Bingo_Http_Request::getNoXssSafe('status','');
   		$strOpUname = Bingo_Http_Request::getNoXssSafe('op_uname','');
   		$strTimeType = Bingo_Http_Request::getNoXssSafe('time_type','');
   		$intStartTime = (int)Bingo_Http_Request::get('start_time',-1);
   		$intEndTime = (int)Bingo_Http_Request::get('end_time',-1);
   		
   		$arrInput  = array(
            'orderby' => array(
            	'field' => 'apply_id',
            	'sort'  => 'ASC',
            ),

        );
        if($intFuncId >0){
        	$arrInput['func_id'] = $intFuncId;
        }
        if('' !== $strFdir){
        	$arrInput['first_dir'] = $strFdir;
        }
        if('' !== $strStatus){
        	$intStatus = (int)self::$_status_map[$strStatus];
        	if(0 !== $intStatus){
        		$arrInput['status'] = $intStatus;
        	}
        }
        if('' !== $strOpUname){
        	$arrInput['audit_uname'] = $strOpUname;
        }
        if('' !== $strTimeType){
        	if($intStartTime > 0){
	        	$arrInput[$strTimeType]['min'] = $intStartTime;
        	}
        	if($intEndTime > 0){
        		$arrInput[$strTimeType]['max'] = $intEndTime;
        	}
        }
        $arrOutput = Tieba_Service::call('bzfuncapply', 'getApply', $arrInput);
        if (!$arrOutput || !isset($arrOutput['errno']) || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call service bzcommunity getApply fail, input = " . serialize($arrInput) . "; output = " . serialize($arrOutput));
            return self::buildRes(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrList = self::_joinMore($arrOutput['data']);
        self::_echoResult($arrList);
        //$arrRes['list'] = $arrList;
        return true;//self::buildRes($intErrno, '', $arrRes);
    }
    /**
     * [_joinMore description]
     * @param  [type] $arrList [description]
     * @return [type]          [description]
     */
    public static function _joinMore($arrList){
        foreach($arrList as $k => $list){
            $ext = $list['ext'];
            $ext = Bingo_Encode::convert($ext,"UTF-8","GBK");
            $ext = unserialize($ext);
            $reason = $ext['reason'];
            $arrList[$k]['ext'] = $ext;
            $arrList[$k]['v'] = $ext['v'];
            if(7 == $list['func_id']){
                $arrList[$k]['v'] = self::$_assist_map[$ext['v']];
            }
            if(self::$_status_map['refuse'] == $list['status']){
                $arrList[$k]['reason'] = $reason;
            }else{
                $arrList[$k]['reason'] = 'pass';
            }
            $arrTempForumIds []= (int)$list['forum_id'];
        }

        if (!empty($arrTempForumIds)) {
            self::_mgetForumNames($arrTempForumIds);
            self::_mgetFdir($arrTempForumIds);
        }
        $arrNewList = array();
        foreach($arrList as $k => $list){
            $intFid = (int)$list['forum_id'];
            $arrList[$k]['fname'] = self::$_arrForumId[$intFid];
            $arrList[$k]['second_dir'] = self::$_arrFdirs[$intFid]['level_2_name'];
            $arrNewList []= array(
                Bingo_Encode::convert(self::$_arrForumId[$intFid],'UTF-8',"GBK"),//这个乱码才能看到别的
                $list['apply_id'],
                self::$_arrForumId[$intFid],
                self::$_func_map[$list['func_id']],
                //Bingo_Encode::convert(self::$_func_map[$list['func_id']], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8),
                Bingo_Encode::convert($list['audit_uname'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8),
                $list['v'],
            );
        }
    	return $arrNewList;
    }/**
     * @brief 
     *
     * @param $arrInput
     *
     * @return 
     */
    private function _echoResult($arrInput){
        $arrLines   = array();
        //$arrLines[] = implode('</td><td style="border:1px solid #ddd">',Bingo_Encode::convert($this->_excel_head, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8));
        foreach($arrInput as $key => $val){
            $arrLines[] = implode('</td><td style="border:1px solid #ddd">', $val);
        }
        $strOutput = implode('</td></tr><tr><td style="border:1px solid #ddd">', $arrLines);
        $strContent = sprintf('<table ><tr><td style="border:1px solid #ddd">%s</td></tr></table>',$strOutput);

        $strFilename = self::DOWNLOAD_FILENAME . date('Ymd',time());
        header("Content-Type: application/vnd.ms-excel; charset=GBK");
        header("Content-Disposition: inline; filename=\"" . $strFilename . ".xls\"");
        echo $strContent;
        return true;
    }
    /**
     * @param $arrForumIds
     * @return bool
     */
    private static function _mgetForumNames($arrForumIds) {
        $arrInput = array(
            'forum_id' => (array)$arrForumIds,
        );
        $arrOutput = Tieba_Service::call('forum', 'getFnameByFid', $arrInput,null,null,'post','php','utf-8');
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call forum getFnameByFid fail input[' . serialize($arrInput) . "] out[" . serialize($arrOutput) . "]");
            return false;
        }
        if (!empty($arrOutput['forum_name'])) {
            $arrForumList = $arrOutput['forum_name'];
            foreach ($arrForumList as $intKey => $arrForumInfo) {
                if (!empty($arrForumInfo['exist']) && !empty($arrForumInfo['forum_name'])) {
                    self::$_arrForumId[$intKey] = strval($arrForumInfo['forum_name']);
                }
            }
        }

        return true;
    }
    /**
     * @param $arrForumIds
     * @return bool
     */
    private static function _mgetFdir($arrForumIds) {
        $arrInput = array(
            'forum_id' => $arrForumIds,
        );
        $arrOutput = Tieba_Service::call('forum', 'mgetForumDir', $arrInput);
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call user getUnameByUids fail input[' . serialize($arrInput) . "] out[" . serialize($arrOutput) . "]");
            return false;
        }
        $arrForumDirs = $arrOutput['output'];
        if (!empty($arrForumDirs)) {
            foreach ($arrForumDirs as $intKey => $arrDir) {
                self::$_arrFdirs[$intKey] = $arrDir;
            }
        }
        return true;

    }
	/**
	 * @param 
	 * @return array
     */
    public static function buildPage($intPn, $intRn, $intTotalNum) {
        return array(
            'current_pn' => $intPn,
            'total_count' => $intTotalNum,
            'total_pn' => ceil($intTotalNum / $intRn),
        );
    }
    
	/**
	 * @param 
	 * @return array
	 */
	public static function buildRes($intErrno, $strError = '', $arrData = null) {

		$arrRet = array(
			'no'	=> $intErrno,
			'error'	=> empty($strError) ? Tieba_Error::getErrmsg($intErrno) : $strError,
			'data'	=> $arrData,
		);

		$strRet = Bingo_String::array2json($arrRet);
		echo $strRet;
        if(Tieba_Errcode::ERR_SUCCESS !== $intErrno){
            return false;
        }
		return true;
	}
}






/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
