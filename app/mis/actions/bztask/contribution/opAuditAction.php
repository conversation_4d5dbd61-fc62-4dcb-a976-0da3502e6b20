<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Action.php
 * <AUTHOR>
 * @date 2016/03/02 16:00:19
 * @brief 
 *  
 **/
class opAuditAction extends Util_Actionbase {

    const DEFAULT_PN = 1;
    const DEFAULT_RN = 20;
    public static $SURVEY_ADD_KEY = array(
        'require'   => array(
            'auditid'   => -1,
            'opinion'   => -1,
            'userid'    => -1,
            'taskid'    => -1,
        ),
        'optional'  => array(
            'response'  => '',
        ),
    );
	/**
	 * @param 
	 * @return bool
	 */
	function init(){
		self::setUiAttr('BROWSE_UI');
		if (false === parent::init()){
			if(0 === $this->_intErrorNo){
				$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}

	/**
	 * @param 
	 * @return array
     */
    public function process() {

        $arrInput = array(
            'opuname'   => Util_Actionbaseext::getCurUserName(),
            'optime'    => time(),
        );


        foreach (self::$SURVEY_ADD_KEY['require'] as $key => $default) {
            if (is_string($default)) {
                $arrInput[$key] = trim(Bingo_Http_Request::get($key, $default));
                if (empty($arrInput[$key])) {
                    return self::buildRes(Tieba_Errcode::ERR_PARAM_ERROR, "$key is invalid");
                }
            } else {
                $arrInput[$key] = Bingo_Http_Request::get($key, $default);
                if ($arrInput[$key] < 0) {
                    return self::buildRes(Tieba_Errcode::ERR_PARAM_ERROR, "$key is invalid");
                }
            }
        }

        foreach (self::$SURVEY_ADD_KEY['optional'] as $key => $default) {
            if (is_string($default)) {
                $arrInput[$key] = trim(Bingo_Http_Request::get($key, $default));
                if (empty($arrInput[$key])) {
                    unset($arrInput[$key]);
                }
            } else {
                $arrInput[$key] = Bingo_Http_Request::get($key, $default);
                if ($arrInput[$key] < 0) {
                    unset($arrInput[$key]);
                }
            }
        }
        if ($arrInput['opinion'] == 1) {// pass
            $arrInput['status'] = 1;
        } else if ($arrInput['opinion'] == 2) {//fail
            $arrInput['status'] = 2;
            $arrInput['reason'] = $arrInput['response'];
        } else {
            Bingo_Log::warning('param invalid');
            return self::buildRes(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        unset($arrInput['opinion']);
        unset($arrInput['response']);


        $strMethod = 'updateAuditEssay';
        $arrOutput = Tieba_Service::call('bztask', $strMethod, $arrInput);
        if (!$arrOutput || !isset($arrOutput['errno']) || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call service bztask $strMethod fail, input = " . serialize($arrInput) . "; output = " . serialize($arrOutput));
            return self::buildRes(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::buildRes(Tieba_Errcode::ERR_SUCCESS);

    }
	/**
	 * @param 
	 * @return array
     */
    public static function getPostId($strPBUrl) {
        $arrTmp = explode("tieba.baidu.com/p/", $strPBUrl);
        if (intval($arrTmp[1]) <= 0) {
            return false;
        }
        $strPBUrl = "http://tieba.baidu.com/p/" . intval($arrTmp[1]);
        return $strPBUrl;
    }

	/**
	 * @param 
	 * @return array
     */
    public static function buildPage($intPn, $intRn, $intTotalNum) {
        return array(
            'current_pn' => $intPn,
            'total_count' => $intTotalNum,
            'total_pn' => ceil($intTotalNum / $intRn),
        );
    }
    
	/**
	 * @param 
	 * @return array
	 */
	public static function buildRes($intErrno, $strError = '', $arrData = null) {

		$arrRet = array(
			'no'	=> $intErrno,
			'error'	=> empty($strError) ? Tieba_Error::getErrmsg($intErrno) : $strError,
			'data'	=> $arrData,
		);

		$strRet = Bingo_String::array2json($arrRet);
		echo $strRet;
		return true;
	}
}






/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
