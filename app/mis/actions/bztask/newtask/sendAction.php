<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Action.php
 * <AUTHOR>
 * @date 2016/03/02 16:00:19
 * @brief 
 *  
 **/
class sendAction extends Util_Actionbase {

    const DEFAULT_PN = 1;
    const DEFAULT_RN = 20;
    const MAX_FORUM_NUM = 200;
    const MULTI_GET_BAWU_LIST = 20;// pre multi-call num, 20 -> 1000qps, 10 -> 500qps
    const MULTI_GET_FID_BY_DIR = 32;//pre multi-call num, 3 每次得到600个吧,目的:控制getBawuList的qps
    const MAX_LIMIT_FID = 1000;
    const ORP_SMALLAPP_TOKEN = "v-u3J8de49oY3U4mwaQFeoM-JnQ99MkO";
    const BAZHU_UID_FILE_NAME = 'BAZHU_UID';
    const ORP_PATH = '/app/client-mis/broadcast';

    public static $TASK_ADD_KEY = array(
        'require'   => array(
            'title'         => '',
            'taskbrief'     => '',
            'taskfrom'      => '',
            'taskdesc'      => '',
            'texttpl'       => '',
            'goal'          => '',
            'starttime'     => -1,
            'limitgoalnum'  => -1,
            'endtime'       => -1,
            'allowall'      => -1,
            'taskid'        => -1,
            'canmaster'     => -1,
            'canslaver'     => -1,
            'caneditor'     => -1,
            'itemtotalnum'  => -1,
            'itemgoalnum'   => -1,
        ),
        'optional'  => array(
            'limitFdir'     => '',
            'limitFid'      => '',
            'minlevel'      => -1,
            'maxlevel'      => -1,
            'nopost'        => -1,
        ),
    );
	/**
	 * @param 
	 * @return bool
	 */
	function init(){
		self::setUiAttr('COMMIT_UI');
		if (false === parent::init()){
			if(0 === $this->_intErrorNo){
				$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}

	/**
	 * @param 
	 * @return array
     */
    public function process() {
        $arrParam = self::_checkParam();
        if(!$arrParam){
            return false;
        }

        $arrMsg = self::getMsg($arrParam);
        $arrParam['msg_info'] = $arrMsg;
        $arrRes = Actions_Bztask_Lib_Msg::pushMsg($arrParam);
        if ($arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call Actions_Bztask_Lib_Msg::pushMsg fail');
            return self::buildRes($arrRes['errno'], $arrRes['error']);
        }

        //update pushstatus
        $arrInput = array(
            'taskid'        => $arrParam['taskid'],
            'pushstatus'    => 1,//send
        );
		$strCurUserName = Util_Actionbaseext::getCurUserName();//如 返回值为 zhouping01
        $arrInput['optime'] = time();
        $arrInput['opuname'] = $strCurUserName;

        $strMethod = 'updatePushStatus';
        $arrOutput = Tieba_Service::call('bztask', $strMethod, $arrInput);
        if (!$arrOutput || !isset($arrOutput['errno']) || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call service bztask $strMethod fail, input = " . serialize($arrInput) . "; output = " . serialize($arrOutput));
            return self::buildRes(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::buildRes(Tieba_Errcode::ERR_SUCCESS);
    }
    /**
     * [_checkParam description]
     * @param
     * @return [type] [description]
     */
    private static function _checkParam(){
        $arrParam = array();

        foreach (self::$TASK_ADD_KEY['require'] as $key => $default) {
            if (is_string($default)) {
                $arrParam[$key] = trim(Bingo_Http_Request::get($key, $default));
                if (empty($arrParam[$key])) {
                    return self::buildRes(Tieba_Errcode::ERR_PARAM_ERROR, "$key is invalid");
                }
            } else {
                $arrParam[$key] = Bingo_Http_Request::get($key, $default);
                if ($arrParam[$key] < 0) {
                    return self::buildRes(Tieba_Errcode::ERR_PARAM_ERROR, "$key is invalid");
                }
            }
        }

        foreach (self::$TASK_ADD_KEY['optional'] as $key => $default) {
            if (is_string($default)) {
                $arrParam[$key] = trim(Bingo_Http_Request::get($key, $default));
                if (empty($arrParam[$key])) {
                    unset($arrParam[$key]);
                }
            } else {
                $arrParam[$key] = Bingo_Http_Request::get($key, $default);
                if ($arrParam[$key] < 0) {
                    unset($arrParam[$key]);
                }
            }
        }
        if ($arrParam['starttime'] <= time()) {
            Bingo_Log::warning('starttime invalid');
            return self::buildRes(Tieba_Errcode::ERR_PARAM_ERROR, 'start time must be greater than the current time');
        }
        if ($arrParam['starttime'] >= $arrParam['endtime']) {
            Bingo_Log::warning('starttime invalid');
            return self::buildRes(Tieba_Errcode::ERR_PARAM_ERROR, 'start time must be less than end time');
        }
        if ($arrParam['maxlevel'] < $arrParam['minlevel']) {
            Bingo_Log::warning('maxlevel or minlevel invalid');
            return self::buildRes(Tieba_Errcode::ERR_PARAM_ERROR, 'min level must be less than max level');
        }
        if (isset($arrParam['limitFdir'])) {
            $strDir = $arrParam['limitFdir'];
//            $arrDir = self::getDirInfo($strDir);
            $arrDir = self::checkDirInfo($strDir);
            if ($arrDir['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_log::warning('limitFdir invalid');
                return self::buildRes($arrDir['errno'], $arrDir['error']);
            }
            $arrParam['limitFdir'] = $arrDir['data'];
        } else {
            $arrParam['limitFdir'] = array();
        }

        if (isset($arrParam['limitFid'])) {
            $strFname = $arrParam['limitFid'];
            $arrFid = self::getFids($strFname);
            if ($arrFid['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_log::warning('limitFid invalid');
                return self::buildRes($arrFid['errno'], $arrFid['error']);
            }
            $arrParam['limitFid'] = $arrFid['data'];
        } else {
            $arrParam['limitFid'] = array();
        }
        return $arrParam;
    }
	/**
	 * @param 
	 * @return array
     */
    private static function getFids($strFname) {
        $arrFname = explode(" ", $strFname);
        if (count($arrFname) <= 0) {
            return self::buildServiceRes(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrInput = array(
            'query_words'   => $arrFname,
        );
        $arrOutput = Tieba_Service::call('forum', 'getFidByFname', $arrInput);
        if (false === $arrOutput || !isset($arrOutput['errno']) || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call forum getAllDir fail, output = ' . serialize($arrOutput));
            return false;
        }
        $arrForumInfo = $arrOutput['forum_id'];
        $arrFids = array();
        $arrFailFname = array();
        foreach ($arrForumInfo as $forumInfo) {
            if ($forumInfo['has_forum_id'] != 1 || $forumInfo['is_forbidden'] == 1){
                $arrFailFname[] = $forumInfo['qword'];
            }
            $arrFids[] = $forumInfo['forum_id'];
        }
        if (count($arrFailFname) > 0) {
            return self::buildServiceRes(Tieba_Errcode::ERR_PARAM_ERROR, 'forum name invalid : ' . serialize($arrFailFname));
        }
        return self::buildServiceRes(Tieba_Errcode::ERR_SUCCESS, 'success', $arrFids);
    }

    /**
     * @param
     * @return array
     */
    private static function checkDirInfo($strDir) {
        $strDir =Bingo_Encode::convert($strDir,'UTF-8','GBK');
        $arrDir = explode(",", $strDir);
        if (count($arrDir) <= 0) {
            return self::buildServiceRes(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrAllDir = Tieba_Service::call('forum', 'getAllDir', array(),null,null,'post','php','utf-8');
        if (false === $arrAllDir || !isset($arrAllDir['errno']) || $arrAllDir['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call forum getAllDir fail, output = ' . serialize($arrAllDir));
            return self::buildServiceRes($arrAllDir['errno']);
        }
        $arrAllDir = $arrAllDir['output']['all_dir'];
        $arrDirTotal = array();
        foreach ($arrAllDir as $level1) {
            $arrDirTotal[$level1['level_1_name']] =  $level1['level_2_name'];
        }

        foreach ($arrDir as $key => $itemDir){
            $arrItem = explode("@",$itemDir);
            if (count($arrItem == 1)){
                if (! key_exists($arrItem[0],$arrDirTotal)){
                    return self::buildServiceRes(Tieba_Errcode::ERR_PARAM_ERROR, 'invalid dir : ' . serialize($itemDir));
                }
            } elseif (count($arrItem == 2)){
                if (!in_array($arrItem[1],$arrDirTotal[$arrItem[0]])){
                    return self::buildServiceRes(Tieba_Errcode::ERR_PARAM_ERROR, 'invalid dir : ' . serialize($itemDir));
                }
            }
            $arrDir[$key] = Bingo_Encode::convert($itemDir,'GBK','UTF-8');
        }

        return self::buildServiceRes(Tieba_Errcode::ERR_SUCCESS, 'success', $arrDir);
    }
    /**
	 * @param 
	 * @return array
     */
    private static function getUids($arrUname) {
        if (empty($arrUname)) {
            return self::buildServiceRes(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrInput = array(
            'user_name' => $arrUname,
        );
        $arrOutput = Tieba_Service::call('user', 'getUidByUnames', $arrInput);
        if (!$arrOutput || !isset($arrOutput['errno']) || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call user getUidByUnames fail, input = ' . serialize($arrInput) . ',output = ' . serialize($arrOutput));
            return self::buildServiceRes($arrOutput['errno'], 'get user id by name fail'); 
        }
        $arrUserInfo = $arrOutput['output']['uids'];
        $arrUid = array();
        foreach ($arrUserInfo as $value) {
            $arrUid[] = $value['user_id'];
        }
        return self::buildServiceRes(Tieba_Errcode::ERR_SUCCESS, 'success', $arrUid);
    }

    
    /**
	 * @param 
	 * @return array
     */
    private static function getMsg($arrInput) {
        $arrMsg = array(
            'type'       => 3,//     1：公告，2：申诉，3：活动
            'taskid'     => $arrInput['taskid'],
            'title'      => $arrInput['title'],
            'desc'       => $arrInput['taskbrief'],
            'time'       => time(),
            'tasktype'   => $arrInput['type'],//1、征文，2、售卖，3、调研 ……
            'notify_type' => 4,//1：不提醒，2：声音，3：震动，4：声音+震动
        );
        return $arrMsg;
    }

	/**
	 * @param 
	 * @return array
     */
    public static function buildPage($intPn, $intRn, $intTotalNum) {
        return array(
            'current_pn' => $intPn,
            'total_count' => $intTotalNum,
            'total_pn' => ceil($intTotalNum / $intRn),
        );
    }
    
	/**
	 * @param 
	 * @return array
	 */
	public static function buildRes($intErrno, $strError = '', $arrData = null) {

		$arrRet = array(
			'no'	=> $intErrno,
			'error'	=> empty($strError) ? Tieba_Error::getErrmsg($intErrno) : $strError,
			'data'	=> $arrData,
		);

		$strRet = Bingo_String::array2json($arrRet);
		echo $strRet;
		return true;
	}

	/**
	 * @param 
	 * @return array
	 */
	public static function buildServiceRes($intErrno, $strError = '', $arrData = null) {

		$arrRet = array(
			'errno'	=> $intErrno,
			'error'	=> empty($strError) ? Tieba_Error::getErrmsg($intErrno) : $strError,
			'data'	=> $arrData,
		);
        return $arrRet;
	}

}






/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
