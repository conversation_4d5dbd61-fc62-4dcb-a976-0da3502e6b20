<?php
/**
 * Created by PhpStorm.
 * User: ligengyong
 * Date: 2017/6/12
 * Time: 15:25
 */
class onlineAction extends Util_Meme_ActionBase {
    const PKG_STATUS_ONLINE = 1;
    /**
     *
     */
    public function process() {

        $intId = (int)Bingo_Http_Request::get('id');

        if($intId <= 0) {
            Bingo_Log::warning("param error, id=$intId");
            self::_displayJson(Tieba_Errcode::ERR_PARAM_ERROR);
            return;
        }

        $arrParams = array(
            'id' => $intId,
            'status' => self::PKG_STATUS_ONLINE,
        );
        $arrRet = Util_Meme_Rpc::callService('meme', 'updatePackage', $arrParams);
        if(Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning("call service meme::updatePackage fail, errno={$arrRet['errno']}");
            self::_displayJson($arrRet['errno']);
            return;
        }
        self::_displayJson(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

}