<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2017-01-05 14:23:53
 * @version
 */
class delAction extends Util_Actionbase
{


    /**
     * @return bool
     */
    function init() {
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * @return bool
     */
    public function process() {

        $intId = Bingo_Http_Request::get("id");
        $strUsername = Bingo_Http_Request::get("user_name");
        $strStarttime = Bingo_Http_Request::get('start_time');
        $strEndtime = Bingo_Http_Request::get('end_time');
        $strSequence = Bingo_Http_Request::get('sequence');



        $arrServiceInput = array(
            'id' => $intId,
            'operator' => Util_Actionbaseext::getCurUserName(),
            'user_name' =>  $strUsername,
            'sequence' => $strSequence,
            'start_time' => $strStarttime,
            'end_time' => $strEndtime,
        );


        $strServiceName = "ala";
        $strServiceMethod = "delYunyingLivesForMis";
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,'post',null,'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__. "::". __FUNCTION__."call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return  $this->outputJson($arrOutput);
        }

        return  $this->outputJson($arrOutput);;

    }

    /**
     * @param $arrRet
     */
    public function outputJson($arrRet){
        header("Content-type: application/json");
        echo json_encode($arrRet);
    }
}

?>
