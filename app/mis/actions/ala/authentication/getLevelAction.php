<?php
/**
 * Created by PhpStorm.
 * User: caowu
 * Date: 2016/8/15
 * Time: 17:19
 */
class getLevelAction extends Util_Actionbase {

    /**
     * @return bool
     */
    function init() {
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }
    /**
     * @return bool
     */
    public function process()
    {
        $arrRet = Tieba_Service::call('alamis', 'getAlamisLevel', array(), null, null, 'post', 'php', 'utf-8');
        if(false === $arrRet || Alalib_Conf_Error::ERR_SUCCESS != $arrRet['errno']) {
            $strLog = __CLASS__."::".__FUNCTION__. " call alamis service::getAlamisLevel fail. input:["."]; output:[".serialize($arrRet)."]";
            Bingo_Log::fatal($strLog);
            return Util_Ala_Common::jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $arrData = $arrRet['data'];
        $arrRet['data']["gittest"] = time();

        return Util_Ala_Common::jsonRet(Alalib_Conf_Error::ERR_SUCCESS,$arrData);
    }
}