<?php
/**
 * Created by PhpStorm.
 * User: caowu
 * Date: 2016/8/15
 * Time: 17:18
 */
class getAuthenticationAuditListAction extends Util_Actionbase {

    /**
     * @return bool
     */
    function init() {
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }
    /**
     * @return bool
     */
    public function process() {
        //获取参数
        $start_time = intval(Bingo_Http_Request::get('start_time',0));
        $end_time = intval(Bingo_Http_Request::get('end_time', 0));
        $intPn = intval(Bingo_Http_Request::get('pn',0));
        $intPs = intval(Bingo_Http_Request::get('ps',0));
        $intUserId = intval(Bingo_Http_Request::get('user_id',0));
        $strOperator = strval(Bingo_Http_Request::get('operator',''));
//        if((empty($statuses)) || (!empty($statuses) && !is_array($statuses)) || (empty($intPs))) {
//            Bingo_Log::warning("input param erro");
//            return Util_Ala_Common::jsonRet(Alalib_Conf_Error::ERR_PARAM_ERROR);
//        }

        if(!empty($intUserId)){
            $arrServiceInput = array(
                'user_id' => $intUserId,
                "audit_status" => 0,
            );
            $arrRet = Tieba_Service::call('alamis', 'getUserAuthenticationAuditList', $arrServiceInput, null, null, 'post', 'php', 'utf-8');
            if(false === $arrRet || Alalib_Conf_Error::ERR_SUCCESS != $arrRet['errno']) {
                $strLog = __CLASS__."::".__FUNCTION__. " call alamis service::getUserAuthenticationAuditList fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrRet)."]";
                Bingo_Log::fatal($strLog);
                return Util_Ala_Common::jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
            }
            $arrData = array(
                "list" => $arrRet['data']["list"],
            );

            return Util_Ala_Common::jsonRet(Tieba_Errcode::ERR_SUCCESS,$arrData);
        }

        if(!$intPn){
            $intPn = 0;
        }
        if(!$intPs){
            $intPs = 3;
        }
        $arrServiceInput = array(
            'start_time' => $start_time,
            'end_time'   => $end_time,
            'offset'         => $intPn * $intPs,
            'limit'         => $intPs,
        );
        if($strOperator){
            $arrServiceInput["operator"] = $strOperator;
        }
        $arrRet = Tieba_Service::call('alamis', 'getAuthenticationAuditList', $arrServiceInput, null, null, 'post', 'php', 'utf-8');
        if(false === $arrRet || Alalib_Conf_Error::ERR_SUCCESS != $arrRet['errno']) {
            $strLog = __CLASS__."::".__FUNCTION__. " call alamis service::getAuthgetAuthenticationAuditListenticationInfo fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrRet)."]";
            Bingo_Log::fatal($strLog);
            return Util_Ala_Common::jsonRet(Alalib_Conf_Error::ERR_CALL_SERVICE_FAIL);
        }
        $arrData = array(
            "list" => $arrRet['data']["list"],
            "page" => array(
                "count" => $arrRet['data']["page"]['count'],
                "pn" => $intPn,
                "ps" => $intPs,
            ),
        );

        return Util_Ala_Common::jsonRet(Tieba_Errcode::ERR_SUCCESS,$arrData);
    }



}

