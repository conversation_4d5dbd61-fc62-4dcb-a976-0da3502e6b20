<?php
/**
 * Created by PhpStorm.
 * User: caowu
 * Date: 17/5/17
 * Time: 下午2:38
 */

class getBannerExcelAction extends Util_Actionbase {


    /**
     * @return bool
     */
    function init() {
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * @return bool
     */
    public function process() {

        $arrServiceInput = array(
            'keywords'   => '',
            'search_type' => 0,
            'pn' => 1,
            'ps' => 100000,
            'order_by' => 'active_id',
            'order_dir' => 'asc',
        );
        $arrRet = Tieba_Service::call('ala', 'searchMisLiveBanner', $arrServiceInput, null, null, 'post', 'php', 'utf-8');

        header("Content-type: text/html; charset=gbk");
        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:filename=直播页面banner后台配置.xls");
        self::createExcel($arrRet);

    }

    /**
     * @param $retArr
     * @return null
     */
    private static function createExcel($retArr) {
        $retArr = $retArr['data']['rows'];
        echo iconv("UTF-8", "GBK", "活动ID\t");
        echo iconv("UTF-8", "GBK", "活动名\t");
        echo iconv("UTF-8", "GBK", "活动描述\t");
        echo iconv("UTF-8", "GBK", "banner图片\t");
        echo iconv("UTF-8", "GBK", "开始生效时间\t");
        echo iconv("UTF-8", "GBK", "失效时间\t");
        echo iconv("UTF-8", "GBK", "配置内容\t");
        echo iconv("UTF-8", "GBK", "操作人\t");
        echo iconv("UTF-8", "GBK", "备注\t\n");
        foreach ($retArr as $value) {
            echo iconv("UTF-8", "GBK", $value['active_id'] . "\t");
            echo iconv("UTF-8", "GBK", $value['active_name'] . "\t");
            echo iconv("UTF-8", "GBK", $value['active_desc'] . "\t");
            echo iconv("UTF-8", "GBK", $value['banner_pic'] . "\t");
            echo iconv("UTF-8", "GBK", date("Y-m-d H:i:s", $value['begin_time']) . "\t");
            echo iconv("UTF-8", "GBK", date("Y-m-d H:i:s", $value['end_time']) . "\t");
            echo iconv("UTF-8", "GBK", $value['content'] . "\t");
            echo iconv("UTF-8", "GBK", $value['operator'] . "\t");
            echo iconv("UTF-8", "GBK", $value['comment'] . "\t\n");
        }
    }

}