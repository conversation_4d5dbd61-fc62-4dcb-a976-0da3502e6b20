<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-01-05 14:23:53
 * @version
 */
class addRecommendAction extends Util_Actionbase{


    /**
     * @return bool
     */
    function init(){
        self::setUiAttr('COMMIT_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * @return bool
     */
    public function process(){

        $channel_id = Bingo_Http_Request::get("channel_id");

        if(!isset($channel_id)){
            Bingo_Log::warning("input params invalid.");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrServiceInput = array(
            'channel_id' => $channel_id,
        );
        $strServiceName = "ala";
        $strServiceMethod = "channelAddRecommendChannel";
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,'post',null,'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__. "::". __FUNCTION__."call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            return self::outputJson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrInput = array(
            'operator' => Util_Actionbaseext::getCurUserName(),
            'channel_id' => $channel_id,
            'operation' => '添加精选',
            'op_type' => 1,
        );
        $arrOutput = Tieba_Service::call("alamis","addChannelOpLog",$arrInput,null,null,'post',null,'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = __CLASS__. "::". __FUNCTION__."call alamis addChannelOpLog fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::fatal($strLog);
            //return self::outputJson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::outputJson(Tieba_Errcode::ERR_SUCCESS);

    }
    public static function outputJson($errno,$data = false){
        $arrData = array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno)
        );
        if($data !== false){
            $arrData['data'] = $data;
        }
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrData, Bingo_Encode::ENCODE_UTF8);
    }

}