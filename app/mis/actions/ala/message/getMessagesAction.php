<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-01-05 14:23:53
 * @version
 */
class getMessagesAction extends Util_Actionbase
{


    /**
     * @return bool
     */
    function init() {
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * @return bool
     */
    public function process() {
        $arrMessage = Tieba_Service::call("alamis","getMessages",array(),  null, null, 'post', 'php','utf-8');
        
        $this->outputJson($arrMessage);
        return true;

    }

    /**
     * @param $arrRet
     */
    public function outputJson($arrRet){
        header("Content-type: application/json");
        echo json_encode($arrRet);
    }
}

?>
