<?php
/**
 * User: qifeng02
 * Date: 20170113
 * content source ui
 */
define("BINGO_ENCODE_LANG", 'utf-8');
class delSourceContentAction extends Util_Actionbase{
	/**
	 * inherit from parent and do nothing
	 * @return bool
	 */

    public function init(){
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
             }
        }
        return true;
    }
	/**
	 * @param bool
	 * @return bool
	 */
    public function process(){

        try {
            //参数获取
        	$intCreatedDate = intval(Bingo_Http_Request::get('created_time', 0));
        	$intResoureId = intval(Bingo_Http_Request::get('resource_id', 0));
            $intSourceId = intval(Bingo_Http_Request::get('source_id', 0));
            $intOpUid = intval(Util_ActionbaseExt::getCurPassId());
            $strOpUname = strval(Util_ActionbaseExt::getCurPassName());
            if($intOpUid <= 0 || empty($strOpUname)){
            	Bingo_Log::warning('modifyTeamAction invalid login.OpUid:['.$intOpUid.'] opuname:['.$strOpUname.']');
            	return $this->_jsonRet(Tieba_Errcode::ERR_USER_NOT_LOGIN, '用户未登录，请先登录。');
            }
            $arrInput = array(
            	'created_time' => $intCreatedDate,
            	'resource_id' => $intResoureId,
            	'source_id' => $intSourceId,
            );
            $arrRet = Tieba_Service::call('common', 'contentDelSourceContent', $arrInput);
            if($arrRet['errno'] != 0){
            	Bingo_Log::warning('call service common::contentDelSourceContent fail, input:['.serialize($arrInput).'],output:['.serialize($arrRet).']');
               	$this->_jsonRet($arrRet['errno'], $arrRet);
                return false;
            }
		    $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,$arrRet['data']);
        } catch (Exception $e) {
            Bingo_Log::warning( "errno =".$e->getCode() ." msg=".$e->getMessage() );
            $this->_jsonRet($e->getCode(), '未知错误');
        }
    }
	
	/**
	 * inherit from parent and do nothing
     * @param array
     * @return bool
	 */
	protected function _jsonRet($errno, $arrData=array()){
		$arrRet = array(
			'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
			'data' => $arrData,
		);
		echo json_encode($arrRet);
	}
}



?>
