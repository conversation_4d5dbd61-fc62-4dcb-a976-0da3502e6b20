<?php
/**
 * Created by PhpStorm.
 * User: yincong
 * Date: 2021/11/12
 * Time: 上午10:23
 */
class updateAddressAction extends Util_Actionbase
{
    /**
     * 初始化函数
     * @return bool
     */

    public function init()
    {
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * 更改地址信息
     * @param null
     * @return boolean
     */
    public function process()
    {
        $userId = Bingo_Http_Request::get('user_id', '');
        $phoneNum = Bingo_Http_Request::get('phone_num', 0);
        $tname = Bingo_Http_Request::get('tname', '');
        $address = Bingo_Http_Request::get('address', '');
        $taskId = Bingo_Http_Request::get('task_id', 1);

        $arrInput = array(
            'user_id' => $userId,
            'phone_num' => $phoneNum,
            'tname' => $tname,
            'address' => $address,
            'task_id' => $taskId,
        );

        //获取地址
        $arrRes = Tieba_Service::call('common', 'updateUserAddress', $arrInput, null, null, 'post', 'php', 'gbk');
        if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning("call service fail ! input = " . serialize($arrRes) . " output = " . serialize($arrRes));
            $this->_jsonRet(empty($arrRes['errno']) ? Tieba_Errcode::ERR_CALL_SERVICE_FAIL : $arrRes['errno']);
            return true;
        }

        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, $arrRes['data']);
        return true;
    }

    /**
     * 返回数据函数
     * @param array
     * @return void
     */
    protected function _jsonRet($errno, $arrData = array())
    {
        $arrRet = array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $arrData,
        );
        echo json_encode($arrRet);
    }

}