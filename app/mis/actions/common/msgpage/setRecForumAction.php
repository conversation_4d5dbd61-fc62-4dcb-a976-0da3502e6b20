<?php
/**
 * <AUTHOR>
 * @date 2021/7/16
 */

define("BINGO_ENCODE_LANG", 'utf-8');
class setRecForumAction extends Util_Actionbase {
    private $_arrForum = array();

    /**
     * @brief 初始化
     * @return bool
     */
    public function init()
    {
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * @brief 主运行函数
     * @return string
     */
    public function process()
    {
        $rawArrForum = Bingo_Http_Request::get('forum_list');
        // 初始化吧id列表
        foreach ($rawArrForum as $fid) {
            $intFid = intval($fid);
            if ($intFid <= 0) {
                continue;
            }
            $this->_arrForum []= $intFid;
        }

        if (false == $this->_setForum()) {
            return self::jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::jsonRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 设置推荐吧
     */
    private function _setForum() {
        $arrInput = array(
            'forum_list' => $this->_arrForum,
        );
        $arrRet = Tieba_Service::call('common', 'setMsgRecForum', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false == $arrRet || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::falal('fail to call common::setMsgRecForum. input[' . serialize($arrInput) . '] output[' . serialize($arrRet) . ']');
            return false;
        }
        return true;
    }

    /**
     * @brief 返回给amis平台的返回函数,内容结构符合amis要求
     * @return string
     */
    public static function jsonRet($errno, $arrData = array())
    {
        $arrRet = array(
            'status' => $errno,
            'msg'    => Tieba_Error::getErrmsg($errno),
            'data'   => $arrData,
        );
        echo json_encode($arrRet);
    }
}
