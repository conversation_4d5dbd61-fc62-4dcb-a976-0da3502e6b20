<?php

/***************************************************************************
 *
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file misTagOpAction.php
 * <AUTHOR>
 * @date 2020/11/22 9:29 下午
 * @brief
 **/

class misTagOpAction extends Util_Actionbase
{
    /**
     * @brief 初始化
     * @return bool
     */
    public function init()
    {
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * @brief 主运行函数
     * @return string
     */
    public function process()
    {
        $intId   = intval(Bingo_Http_Request::get('id', 0));
        $intOp = Bingo_Http_Request::get('op', ''); //1.恢复 2.删除

        if (empty($intId) && empty($intOp)) {
            return self::jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrInput = array(
            'id'   => $intId,
            'op' => $intOp,
        );
        $arrOutput = Tieba_Service::call('common', 'opTags', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (!$arrOutput || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf("call common::deleteItemEntity failed! input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return self::jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::jsonRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
     * @brief 返回给amis平台的返回函数,内容结构符合amis要求
     * @return string
     */
    public function jsonRet($errno, $arrData = array(), $errmsg = false)
    {
        $arrRet = array(
            'status' => $errno,
            'msg'    => $errmsg === false ? Tieba_Error::getErrmsg($errno) : $errmsg,
            'data'   => $arrData,
        );
        echo json_encode($arrRet);
    }
}


