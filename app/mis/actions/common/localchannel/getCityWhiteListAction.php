<?php

class getCityWhiteListAction extends Util_Actionbase{  
    /**
     * @brief 初始化
     * @return bool
     */
    
    public function init()
    {
        self::setUiAttr('BROWSE_UI');
         if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }
    
    /**
     * @brief 主运行函数 
     * @return string
     */
    public function process(){
        $arrInput = array(
            'table_name' => 'tb_wordlist_redis_channel_config',
            'key'        => 'city_white_list',
            'ie'         => 'utf-8',
        );

        $arrOutput = Tieba_Service::call('wordlist', 'queryWLItemDirectly', $arrInput, null, null, 'post', 'php', 'gbk' );
        if (false == $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning('call service getCityWhiteList fail . output:[' . serialize($arrOutput) . ']');
            return self::jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrCityName = array(
            array(
                "label" => "全部",
                "value" => "",
            ),
        );

        $arrCityList = unserialize($arrOutput['data']['city_white_list']);
        foreach($arrCityList as $cityName){
            $tmp = array (
                "label" => $cityName . '市',
                "value" => $cityName . '市',
            );
            $arrCityName[] = $tmp;
        }

        $arrRet['options'] = $arrCityName;

        return self::jsonRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);           
    }
    
    /**
     * @brief 返回给amis平台的返回函数,内容结构符合amis要求
     * @return string
     */
    public static function jsonRet($errno, $arrData = array())
    {
        $arrRet = array(
            'status' => $errno,
            'msg'    => Tieba_Error::getErrmsg($errno),
            'data'   => $arrData,
        );
        echo json_encode($arrRet);
    }
}



