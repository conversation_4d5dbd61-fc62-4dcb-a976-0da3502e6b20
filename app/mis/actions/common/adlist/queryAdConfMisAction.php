<?php

class queryAdConfMisAction extends Util_Actionbase {
    private $_arrClientType = array(
        'client'   =>  '客户端',
        'pc'       =>  'PC端',
        'wap'      =>  '智能版',
    );
    private $_arrStatusMapping = array(
        1   =>  '线上运行',
        2   =>  '已下线',
        3   =>  '删除中',
    );
    private $_arrStatusMisMapping = array(
        0   =>  '',
        1   =>  '数据处理中，请稍后',
        2   =>  '数据处理中，请稍后',
        3   =>  '数据处理中，请稍后',
    );

    /**
     * [init description]
     * @return [type] [description]
     */
    public function init(){
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * [execute description]
     * @return [type] [description]
     */
    public function process() {
        $intConfId = Bingo_Http_Request::get('conf_id', 0);
        $strTimeRange = Bingo_Http_Request::get('time_range', '');
        $strClientType = Bingo_Http_Request::get('client_type', '');
        $strLevel1Name = Bingo_Http_Request::get('level_1_name', '');
        $strLevel2Name = Bingo_Http_Request::get('level_2_name', '');
        $strForumName = Bingo_Http_Request::get('forum_name', '');
        $strEntryType = Bingo_Http_Request::get('entry_type', '');
        $intStatus = Bingo_Http_Request::get('status', 0);
        $intRn = Bingo_Http_Request::get('perPage', '');
        $intPn = Bingo_Http_Request::get('page', '');

        $arrInput = array(
            'rn'            =>  $intRn,
            'offset'        =>  $intRn * ($intPn - 1),
        );
        if (0 != $intConfId) {
            $arrInput['conf_id'] = $intConfId;
        }
        if (!empty($strTimeRage)) {
            $arrTime = explode(',', $strTimeRange);
            $arrInput['start_time'] = $arrTime[0];
            $arrInput['end_time'] = $arrTime[1];
        }
        if (!empty($strClientType)) {
            $arrInput['client_type'] = $strClientType;
        }
        if (!empty($strEntryType)) {
            $arrInput['entry_type'] = $strEntryType;
        }
        if (!empty($strEntryName)) {
            $arrInput['entry_name'] = $strEntryName;
        }
        if (0 != $intStatus) {
            $arrInput['status'] = $intStatus;
        }
        if (!empty($strForumName)) {
            $arrInput['kw_type'] = 3;
            $arrInput['kw_list'] = explode(';', $strForumName);
        } else if (!empty($strLevel2Name)) {
            $arrInput['kw_type'] = 2;
            $arrInput['kw_list'] = explode(',', $strLevel2Name);
        } else if (!empty($strLevel1Name)) {
            $arrInput['kw_type'] = 1;
            $arrInput['kw_list'] = array(
                $strLevel1Name,
            );
        }
        if (!empty($arrInput['kw_list'])) {
            $arrInput['kw_list'] = array_filter($arrInput['kw_list']);
            $arrInput['kw_list'] = array_unique($arrInput['kw_list']);
        }

        $arrOutput = Tieba_Service::call('common', 'getAdConfMis', $arrInput);
        if (false == $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            Bingo_Log::warning('call common getAdConfMis failed. [input: '.serialize($arrInput).' ] [output: '.serialize($arrOutput).' ]');
            $this->_jsonRet($arrOutput['errno']);
            return false;
        }
        $arrRows = array();
        foreach ($arrOutput['data']['ad_conf_list'] as $arrData) {
            $arrData['client_type'] = $this->_arrClientType[$arrData['client_type']];
            $arrKw = explode(';', $arrData['kws']);
            $arrKw = array_filter($arrKw);
            $arrKw = array_values($arrKw);
            $arrKwKey = array(
                1   =>  'level_1_name',
                2   =>  'level_2_name',
                3   =>  'forum_name',
            );
            $strKw = $arrKw[0];
            if (count($arrKw) > 1) {
                $strKw .= '等';
            }
            $arrData[$arrKwKey[$arrData['kw_type']]] = $strKw;

            $arrData['status'] = $this->_arrStatusMapping[$arrData['status']];
            $strStatusMis = $this->_arrStatusMisMapping[$arrData['status_mis']];
            if (!empty($strStatusMis)) {
                $arrData['status'] .= ('-('.$strStatusMis.')');
            }
            $arrRows[] = $arrData;
        }
        $arrResult = array(
            'count' =>  $arrOutput['data']['count'],
            'rows'  =>  $arrRows,
        );
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, $arrResult);
        return true;
    }

    /**
     *  inherit from parent and do nothing
     * @param unknown $errno
     * @param unknown $arrData
     * @return string
     */
    protected function _jsonRet($errno, $arrData=array()){
        $arrRet = array(
            'status' => $errno,
            'msg' => Tieba_Error::getErrmsg($errno),
            'data' => $arrData,
        );
        echo json_encode($arrRet);
    }
}
