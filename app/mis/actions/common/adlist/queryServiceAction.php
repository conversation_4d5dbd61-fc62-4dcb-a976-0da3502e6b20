<?php

class queryServiceAction extends Util_Actionbase {
    private $_intStartTime = 0;
    private $_intEndTime = 0;
    private $_strClientType = 0;
    private $_strServiceName = '';
    private $_intRn = 10;
    private $_intPn = 1;
    private $_arrClientType = array(
        '1' =>  '客户端',
        '2' =>  'PC',
        '3' =>  '智能版',
    );

    /**
     * [init description]
     * @return [type] [description]
     */
    public function init(){
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * [_getInput description]
     * @param
     * @return
     */
    private function _getInput() {
        $strTimeRange = Bingo_Http_Request::get('s', '');
        if (!empty($strTimeRage)) {
            $arrTime = explode(',', $strTimeRange);
            $this->_intStartTime = $arrTime[0];
            $this->_intEndTime = $arrTime[1];
        } else {
            $this->_intStartTime = 0;
            $this->_intEndTime = time();
        }
        $this->_strClientType = Bingo_Http_Request::get('client_type', '');
        $this->_strServiceName = Bingo_Http_Request::get('service_name', '');
        $this->_intRn = Bingo_Http_Request::get('perPage', '');
        $this->_intPn = Bingo_Http_Request::get('page', '');
    }

    /**
     * [execute description]
     * @return [type] [description]
     */
    public function process(){
        $this->_getInput();
        $arrInput = array(
            'start_time'    =>  $this->_intStartTime,
            'end_time'      =>  $this->_intEndTime,
            'rn'            =>  $this->_intRn,
            'offset'        =>  $this->_intRn * ($this->_intPn - 1),
        );
        if (!empty($this->_strClientType)) {
            $arrInput['client_type'] = $this->_strClientType;
        }
        if (!empty($this->_strServiceName)) {
            $arrInput['service_name'] = $this->_strServiceName;
        }
        $arrOutput = Tieba_Service::call('common', 'getAdService', $arrInput);
        if (false == $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            Bingo_Log::warning('call common getAdService failed. [input: '.serialize($arrInput).' ] [output: '.serialize($arrOutput).' ]');
            $this->_jsonRet($arrOutput['errno']);
            return false;
        }
        $arrRows = array();
        foreach ($arrOutput['data']['service_list'] as $arrData) {
            $arrData['client_type'] = $this->_arrClientType[$arrData['client_type']];
            $arrData['update_time'] = date('Y/m/d H:i:s', $arrData['update_time']);
            $arrRows[] = $arrData;
        }
        $arrResult = array(
            'count' =>  $arrOutput['data']['count'],
            'rows'  =>  $arrRows,
        );
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, $arrResult);
        return true;
    }

    /**
     *  inherit from parent and do nothing
     * @param unknown $errno
     * @param unknown $arrData
     * @return string
     */
    protected function _jsonRet($errno, $arrData=array()){
        $arrRet = array(
            'status' => $errno,
            'msg' => Tieba_Error::getErrmsg($errno),
            'data' => $arrData,
        );
        echo json_encode($arrRet);
    }
}
