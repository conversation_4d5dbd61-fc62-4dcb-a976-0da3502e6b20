<?php

/**
 * content source ui
 */

class showVoiceRoomListAction extends Util_Actionbase{

    const ZHIBO_ING = 1;

    /**
     * @brief 初始化
     * @return bool
     */

    public function init()
    {
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * @brief 主运行函数 获取热贴榜数据
     * @return string
     */
    public function process(){

        $pn = intval(Bingo_Http_Request::get('pn', 1));
        $rn = intval(Bingo_Http_Request::get('rn', 10));
        $status = Bingo_Http_Request::get('status', 1);
        $offset = ($pn - 1) * $rn;
        $size = $rn;
        $arrInput = array(
            'cond' =>array(
                'status' => '('.$status.')',
            ),
            'offset' => $offset,
            'limit' => $size,
        );
        $day = Bingo_Http_Request::get('day', 0);
        if(!empty($day)){
            $nowTime = time();
            $arrInput['minTime'] = $nowTime - $day * 3600 * 24;
            $arrInput['maxTime'] = $nowTime;
        }
        $result =  Tieba_Service::call('voiceroom','getZhiboRoomList',$arrInput,null,null,'post','php','utf-8');

        if (false == $result || $result['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call voiceroom:getZhiboRoomList failed! output: [' . serialize($result) . ']');
            return self::jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL,$result);
        }

        if(empty($result['data'])){
            return self::jsonRet(Tieba_Errcode::ERR_SUCCESS,array());
        }
        $arrOut = array(
            'rows' => $result['data']['data'],
            'count' => !empty($result['data']['count']) ? intval($result['data']['count']) : 0,
        );
        return self::jsonRet(Tieba_Errcode::ERR_SUCCESS,$arrOut);

    }



    /**
     * @brief 返回给amis平台的返回函数,内容结构符合amis要求
     * @return string
     */
    public static function jsonRet($errno, $arrData = array())
    {
        $arrRet = array(
            'status' => $errno,
            'msg'    => Tieba_Error::getErrmsg($errno),
            'data'   => $arrData,
        );
        echo json_encode($arrRet);
    }

}
