<?php

/**
 * content 获取二级吧目录
 */

class querySecForumAction extends Util_Actionbase{

    /**
     * @brief 初始化
     * @return bool
     */

    public function init()
    {
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * @brief 主运行函数
     * @return string
     */
    public function process(){

        $cateId = strval(Bingo_Http_Request::get('cate_id', ''));
        if (empty($cateId)) {
            Bingo_Log::warning("invalid params : cate_id:$cateId");
            return self::jsonRet(Tieba_Errcode::ERR_MO_PARAM_INVALID,array());
        }
        $arrInput = array(
			"cate_id" => $cateId,
		);
		$arrRes = Tieba_Service::call('common', 'condQueryCateForumDir', $arrInput, null, null, 'post', 'php', 'utf-8');
		if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
			Bingo_Log::warning("call condQueryCateForumDir error input[".serialize($arrInput)."] output [".serialize($arrRes)."]");
            return self::jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL,array());
		}
        $arrOut = array(
            'items' => $arrRes['data'],
        );
        
        //Bingo_Log::warning("*9999*".var_export($arrOut, 1));
        return self::jsonRet(Tieba_Errcode::ERR_SUCCESS, $arrOut);
    }


    /**
     * @brief 返回给amis的返回函数,内容结构符合amis要求
     * @return string
     */
    public static function jsonRet($errno, $arrData = array())
    {
        $arrRet = array(
            'status' => $errno,
            'msg'    => Tieba_Error::getErrmsg($errno),
            'data'   => $arrData,
        );
        echo json_encode($arrRet);
    }

}
