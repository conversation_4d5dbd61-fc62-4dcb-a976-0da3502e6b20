<?php
/**
 * 用户得分操作
 * @authors tanxinyun
 * @date    2015-03-26 17:49:43
 */

class addUserScoreAction extends Actions_Gameplayer_ActionBase {

    /**
     * service方法与参数定义
     * @return [array] [参数结构体]
     */
    public function getServiceCallParam() {
        // 前三个必选，后面的可选
        $attrs = array(
            'user_id',
            'user_name',
            'score',
            'remark', // 备注信息
            'item_type',
            'item',
            'boost_factor',
        );

        $params = array(
            'service' => 'gameplayer',
            'method' => 'addUserScore',
            'attrs' => $attrs,
        );

        return $params;
    }
}