<?php
/**
 * 设置脚本需要读取的多酷日志文件的日期
 * @authors tanxinyun
 * @date    2015-03-26 17:49:43
 */

class setDuokuGameLogDateAction extends Actions_Gameplayer_ActionBase {

    /**
     * service方法与参数定义
     * @return [array] [参数结构体]
     */
    public function getServiceCallParam() {
        $attrs = array(
            'date', // 20150505格式
            'seconds', // 缓存时间
        );

        $params = array(
            'service' => 'gameplayer',
            'method' => 'setDuokuGameLogDate',
            'attrs' => $attrs,
        );

        return $params;
    }
}