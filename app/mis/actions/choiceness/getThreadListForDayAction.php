<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-01-06 10:01:08
 * @version
 */

 class getThreadListForDayAction extends Util_Actionbase{
	 public function init(){ 
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }       
        }       
        return true;
    }
	
	public function errRet($errno){
		Bingo_Log::warning('errno='.$errno);
		$arrErrno = array(
			'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
		);
		echo json_encode($arrErrno);
		return true;
	}

	public function process(){
		$intTime = trim(Bingo_Http_Request::getNoXssSafe("time", 0));
		$arrReq = array('time' => $intTime);
		$arrRet = Tieba_Service::call('choiceness', 'getThreadListForDay', $arrReq, NULL, NULL,'post','php','utf-8');
		if($arrRet===false || $arrRet['errno']!=Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning(sprintf("call choiceness::getThreadList error.input:%s", serialize($arrReq)));
			$this->errRet($arrRet['errno']);
			return false;
		}

		$arrData = array(
			'errno' => $arrRet['errno'],
			'errmsg' => $arrRet['errmsg'],
			'data' => array(
				'list' => $arrRet['thread'],
			),
		);
		echo json_encode($arrData);
		return true;
	}
 }
