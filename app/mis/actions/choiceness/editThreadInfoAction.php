<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-01-06 10:01:08
 * @version
 */

 class editThreadInfoAction extends Util_Actionbase{
	 public function init(){ 
        self::setUiAttr('COMMIT_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }       
        }       
        return true;
    }
	
	public function errRet($errno){
		Bingo_Log::warning('errno='.$errno);
		$arrErrno = array(
			'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
		);
		echo json_encode($arrErrno);
		return true;
	}

	public function process(){
		
		$intFtid 					= trim(Bingo_Http_Request::getNoXssSafe("ftid", 0));
		$strTitle 					= trim(Bingo_Http_Request::getNoXssSafe("title", ''));
		$strUname					= trim(Bingo_Http_Request::getNoXssSafe("uname", ''));
		$arrThumbnail 				= Bingo_Http_Request::getNoXssSafe("thumbnail", array());
		$strAbstract				= trim(Bingo_Http_Request::getNoXssSafe("abstract", ''));
		$strContent 				= trim(Bingo_Http_Request::getNoXssSafe("content", ''));
		$strTag 					= trim(Bingo_Http_Request::getNoXssSafe("tag", ''));
		$strOriginal_thread_addr  	= trim(Bingo_Http_Request::getNoXssSafe("original_thread_addr", ''));
		$strOptUser					= Util_Actionbaseext::getCurUserName(); 
		$intPublishTime 	 	    = trim(Bingo_Http_Request::getNoXssSafe("publish_time", time()));//��ȷ��Сʱ
		$strTagAbstract 	 		= trim(Bingo_Http_Request::getNoXssSafe("tagabstract", ''));
		$strAuthorRemark 	 		= trim(Bingo_Http_Request::getNoXssSafe("authorremark", ''));
		 
		
		if ($intFtid == 0 || $strTitle=='' || $strUname == '' || $strAbstract == '' || $strContent == ''  || $strOptUser == '') {
			Bingo_Log::warning(sprintf("param error.input:%s", Tieba_Errcode::ERR_PARAM_ERROR));
			$this->errRet(Tieba_Errcode::ERR_PARAM_ERROR);
			return false;
		}
		
		$arrReq = array(
				'ftid'					=> 	$intFtid,
				'title'					=>	$strTitle,
				'uname'					=>	$strUname,
				'thumbnail'				=>	$arrThumbnail,
				'abstract'				=>	$strAbstract,
				'content'				=>	$strContent,
				'tag'					=>	$strTag,
				'original_thread_addr'	=>	$strOriginal_thread_addr,
				'operator_name'			=> 	$strOptUser,
		        'publish_time'		=> $intPublishTime,
				'tagabstract'           =>  $strTagAbstract,
				'authorremark'          =>  $strAuthorRemark,
		);
		$arrRet = Tieba_Service::call('choiceness', 'editThreadInfo', $arrReq);
		if($arrRet===false || $arrRet['errno']!=Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning(sprintf("call choiceness::getThreadList error.input:%s", serialize($arrReq)));
			$this->errRet($arrRet['errno']);
			return false;
		}
		echo json_encode($arrRet);
		return true;
	}
 }
