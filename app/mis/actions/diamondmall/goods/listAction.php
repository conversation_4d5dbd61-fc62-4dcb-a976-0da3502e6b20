<?php
/**
 * 商品列表
 * <AUTHOR>
 */
class listAction extends Bingo_Action_Abstract {

	/**
	 * 输出响应
	 * @param int $errno
	 * @param array|string $data
	 */
	private static function _errRet($errno, $data = "") {
		$errmsg = Tieba_Error::getErrmsg ( $errno );
		$arrRet = array ('no' => $errno, 'errMsg' => $errmsg );
		if ($data !== "") {
			$arrRet ['data'] = $data;
		}
		Bingo_Log::pushNotice ( "errno", $errno );
		echo Bingo_String::array2json($arrRet,'utf-8');
        Bingo_Http_Response::contextType('application/json');
	}

	public function execute(){

		$page = Bingo_Http_Request::getNoXssSafe('page', 1);
		$limit = Bingo_Http_Request::getNoXssSafe('limit', 10);
		$id = Bingo_Http_Request::getNoXssSafe('id', '');

		$arrInput = array(
            'page' => $page, 
            'limit' => $limit, 
			'id' => $id,
		);

		$ret = Tieba_Service::call ( 'diamondmall', 'listGoods', $arrInput, null, null, 'post', 'php', 'utf-8');

		if ($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
			return;
		}

		$responseData = $ret['data'];
		$responseData['page'] = array(
			'current_pn' => $page,
			'total_count' => $responseData['total'],
			'total_pn' => ceil($responseData['total'] / $limit),
		);
		unset($responseData['total']);
		self::_errRet(Tieba_Errcode::ERR_SUCCESS, $responseData);

		return;
	}
}
