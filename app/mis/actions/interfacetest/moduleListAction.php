<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-06-25
 * @version 
 * @structs & methods(copied from idl.)
 * @brief 全部模块列表页面
*/

class moduleListAction extends Util_Actionbase{
    protected static $_ui_ie = 'utf-8';
    /**
     * @param null
     * @return true
     * */
    function init(){
        self::setUiAttr('BROWSE_UI');
        if (false == parent::init()){
            if(0 == $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }
    /**
     * @param null
     * @return true
     * */
    public function process(){
    /*    $this->_getUserInfo();
        $user_id = empty($this->_arrUserInfo['user_id'])? 0 : $this->_arrUserInfo['user_id'];
        Bingo_Log::pushNotice("ispv", 1);
      */  
        $pn = intval(Bingo_Http_Request::getNoXssSafe('pn',1));
        $rn = intval(Bingo_Http_Request::getNoXssSafe('rn',20));
        $strKeyword = strval(Bingo_Http_Request::getNoXssSafe('keyword',''));
        $version_id = intval(Bingo_Http_Request::getNoXssSafe('version_id',0));
	$project_id = intval(Bingo_Http_Request::getNoXssSafe('project_id',0));
        $strOpUname   = strval(Util_ActionbaseExt::getCurUserName());// 操作人id	
        if($version_id>0){
            $arrInput = array(
            'pn' => $pn,
            'rn' => $rn,
            'version_id' => $version_id,
            );
            $arrRet = Tieba_Service::call('interfacetest','getModuleByVersion', $arrInput, null, null, 'post', 'php', 'utf-8');
        }
        else if ($project_id>0){
            $arrInput = array(
                'pn' => $pn,
                'rn' => $rn,
                'project_id' => $project_id,
            );
            $arrRet = Tieba_Service::call('interfacetest','getModuleByProject', $arrInput, null, null, 'post', 'php', 'utf-8');
        }
        else{
        $arrInput = array(
            'pn' => $pn,
            'rn' => $rn,
            'keyword' => $strKeyword
        );
        $arrRet = Tieba_Service::call('interfacetest','getModuleByKeyword', $arrInput, null, null, 'post', 'php', 'utf-8');
        }
        if (!$arrRet || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('service interface.getModuleByKeyword error. [input='.serialize($arrInput).'][out='.serialize($arrRet).']');
        }
//        Bingo_Log::warning(var_export());
        $arrModules = $arrRet['data'];
            
        $count = intval($arrRet['page']['total_count']);
        $arrRet['data'] = array(
            'rows' => $arrModules,
            'count' => $count
        );
/*
        $arrRet = array(
            'no' => $errno,
            'error' => Tieba_Error::getErrmsg($errno),
            'data' => $arrData,
        );
*/
        echo json_encode($arrRet);
  //      $this->_displayJson($arrRet['errno'],$arrRet['data']);
    }
}
