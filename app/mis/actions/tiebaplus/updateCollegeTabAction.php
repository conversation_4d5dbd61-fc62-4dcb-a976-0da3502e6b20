<?php
    /**
     * Created by PhpStorm.
     * User: hubin13
     * Date: 2021/11/16
     * Time: 下午3:51
     */
class updateCollegeTabAction extends Util_Actionbase {

    /**
     * @return bool
     */
    function init() {
        self::setUiAttr('COMMIT_UI');
        return true;
    }

    /**
     * @return boolean
     */
    public function process(){
        $intId = intval(Bingo_HTTP_Request::getNoXssSafe('id', 0));
        $strName    = strval(Bingo_HTTP_Request::getNoXssSafe('name', ''));
        $intSort = intval(Bingo_HTTP_Request::getNoXssSafe('sort', 0));

        $arrInput = array(
            'fields' => array(
                "name"=>$strName,
                "sort"=>$intSort,
            ), //更改对字段
            'conds' => array("id="=>$intId), //条件id
        );
        Bingo_Log::warning('modifyTiebaplusCollegeTabById, input:['.serialize($arrInput).']');

        $arrOut  =  Tieba_Service::call('adsense', 'modifyTiebaplusCollegeTabById', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (!$arrOut|| $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call adsense::modifyTiebaplusCollegeTabById fail, input:['.serialize($arrInput).'],output:['.serialize($arrOut).']');
            $this->_jsonRet($arrOut['errno'], $arrOut);
            return false;
        }
        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, $arrOut);
        return true;
    }


    /**
     * @param $intErrno
     * @param array $arrData
     */
    protected function _jsonRet($intErrno, $arrData = array()){
        $arrRet = array(
            'errno' => $intErrno,
            'errmsg' => Tieba_Error::getErrmsg($intErrno),
            'data' => $arrData,
        );
        echo Bingo_String::array2json($arrRet, 'UTF-8');

    }

}