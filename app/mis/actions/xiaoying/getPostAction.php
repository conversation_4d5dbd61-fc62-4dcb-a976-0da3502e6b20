<?php
/**
 * <AUTHOR>
 * @desc Xiaoying video shenhe mis
 * @since 20150818
 *
 */
define('BINGO_ENCODE_LANG', 'UTF-8');

class getPostAction extends Util_Actionbase {
	
    /**
     * @brief init
     * @return: true if success. warning if fail.
     **/
	public function init(){
		self::setUiAttr('BROWSE_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }       
        }       
        return true;
	}
	/**
	 * @brief process
	 * @return: true if success. warning if fail.
	 **/
    public function process(){


        $op_uid = intval(Util_ActionbaseExt::getCurPassId());
        $op_uname = trim(strval(Util_ActionbaseExt::getCurPassName()));
        $op_ip = intval(Util_ActionbaseExt::getUserIp());

        if($op_uid <= 0 || $op_uname == '') {
            Bingo_Log::warning('invalid user login info, uid='.$op_uid.',uname='.$op_uname);
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR,Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR));
        }

        /* $pageno = Bingo_Http_Request::get('pageno', 0);
        $count = Bingo_Http_Request::get('count', 0); */
        //按播放次数或者时间排序，播放次数估计不做了
        $arrGetData = Bingo_Http_Request::get('data', 0);

        $arrInput = array();
        $arrGetPostList = array();
        if ($arrGetData[0]['post_id'] > 0) {
            foreach ($arrGetData as $key => $value) {
                $arrGetPostList[$value['post_id']] = $value;
                $arrInput['post_id'][] = $value['post_id'];
            }
            //Bingo_Log::warning(var_export($arrGetData,true));
            $arrOutput = Service_Xiaoying_Xiaoying::getPost($arrInput);
            if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                Bingo_Log::warning('xiaoying getThread failed~,input:['.serialize($arrInput).'].output:['.serialize($arrOutput).']');
                return self::_jsonRet($arrOutput['errno'],$arrOutput['errmsg']);
            }
            $arrServicePostList = $arrOutput['output'];
            $arrPostList = array();

        	foreach ($arrServicePostList as $key => $value) {
            	
                	$value['xiaoying_mp4'] = Bingo_String::json2array( self::getXiaoyingMp4($arrGetPostList[$value['post_id']]['xiaoying_url']));
	                if (!empty($value['xiaoying_mp4']['stream'])) {
    	                $value['xiaoying_mp4'] = $value['xiaoying_mp4']['stream']['url'];
        	        }
                    $value['content'] = $arrGetPostList[$value['post_id']]['abstract'];
            	    //$value['xiaoying_mp4'] = 'http://v2.xiaoying.tv/20150803/43o0k/3fqfyU137.mp4';
                	$arrPostList[] = array_merge($value,$arrGetPostList[$value['post_id']]);
			}
        } else { // 针对先审后发帖子处理，没有tid和pid
            foreach ($arrGetData as $key => $value) {
                $arrGetPostList[$value['video_audit_id']] = $value;
                $arrInput['video_audit_id'][] = $value;
            }
            //Bingo_Log::warning(var_export($arrGetData,true));
            $arrOutput = Service_Xiaoying_Xiaoying::getPostFromMis($arrInput);
            if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
                Bingo_Log::warning('xiaoying getPostFromMis failed~,input:['.serialize($arrInput).'].output:['.serialize($arrOutput).']');
                return self::_jsonRet($arrOutput['errno'],$arrOutput['errmsg']);
            }
            $arrServicePostList = $arrOutput['output'];
            $arrPostList = array();

            foreach ($arrServicePostList as $key => $value) {
                
//                	$value['xiaoying_mp4'] = Bingo_String::json2array( self::getXiaoyingMp4($arrGetPostList[$value['video_audit_id']]['video_url']));
//                	if (!empty($value['xiaoying_mp4']['stream'])) {
//                    	$value['xiaoying_mp4'] = $value['xiaoying_mp4']['stream']['url'];
//                	}

                	//$value['xiaoying_mp4'] = 'http://v2.xiaoying.tv/20150803/43o0k/3fqfyU137.mp4';
                	$tempParam = array(
                    	'content' =>$arrGetPostList[$value['video_audit_id']]['abstract'],
                    	'word' =>$arrGetPostList[$value['video_audit_id']]['forum_name'],
                    	'username' =>$value['user_name'],
                    	'now_time' => $value['create_time'],
                    	'video_info' => $value['video_info'],
                	);
                	$arrPostList[] = array_merge($value, $tempParam);
            }
        }


        return self::_jsonRet($arrOutput['errno'],$arrOutput['errmsg'],$arrPostList);
    }
    /**
     * @brief process
     * @param $url
     * @return: true if success. warning if fail.
     **/
    public function getXiaoyingMp4($arrParam){

        $strUrl = 'http://weibo.api.xiaoying.co/api/getdata?url=' . urlencode($arrParam);
        //Bingo_Log::warning(var_export($arrParam,true));
        $objOrpFetchUrl = Orp_FetchUrl::getInstance(
            array(
                'timeout' =>1000,
                'conn_timeout' =>1000,
                'max_response_size'=> 10485760,
            )   
        );  
         
        $ret=$objOrpFetchUrl->get($strUrl,array(),$_COOKIE);   
        
        return $ret;
    }

    /**
     * @brief 通过url获取md5
     * @param $url
     * @return: md5.
     **/
    private function getMd5FromUrl($input) {
        $output = explode("_", $input);
        $strMd5 = explode(".", end($output));
        return $strMd5[0];
    }

    /**
     * @brief _jsonRet
     * @param errno,errmsg,data
     * @return:  0.
     **/
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array()){
        $arrRet = array(
            'no'=>intval($errno),
            'error'=>strval($errmsg),
            'data'=>$arrExtData,
        );
        Bingo_Log::pushNotice("errno",$errno);
        Bingo_Http_Response::contextType('application/json');

        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8 );
        return 0;
    }
	
	
}
