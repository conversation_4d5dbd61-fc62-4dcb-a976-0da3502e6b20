<?php
/**
 * <AUTHOR>
 * @desc Xiaoying video shenhe mis
 * @since 20150818
 *
 */
define('BINGO_ENCODE_LANG', 'UTF-8');

class analysisBaseAction extends Util_Actionbase {
	
    /**
     * @brief init
     * @return: true if success. warning if fail.
     **/
	public function init(){
		self::setUiAttr('BROWSE_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }       
        }       
        return true;
	}
	
	/**
	 * @brief process
	 * @return: true if success. warning if fail.
	 **/
    public function process(){

        $op_uid = intval(Util_ActionbaseExt::getCurPassId());
        $op_uname = trim(strval(Util_ActionbaseExt::getCurPassName()));
        $op_ip = intval(Util_ActionbaseExt::getUserIp());

        if($op_uid <= 0 || $op_uname == '') {
            Bingo_Log::warning('invalid user login info, uid='.$op_uid.',uname='.$op_uname);
            return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR,Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR));
        }

        
        $pageno = Bingo_Http_Request::get('pn', 0);
        $count = Bingo_Http_Request::get('cn', 0);
        $start_time = Bingo_Http_Request::get('from', 0);
        $end_time = Bingo_Http_Request::get('to', 0);
       
        $arrInput = array(
            'pageno' => $pageno,
            'count' => $count,
            'start_time' => $start_time,
            'end_time' => $end_time,
 
        );
        if (empty($arrInput['pageno'])){
            $arrInput['pageno'] = 0;
        } else {
            $arrInput['pageno'] --;
        }
        if (empty($arrInput['count'])){
            $arrInput['count'] = 30;
        }
        //self::$MisService->call('notice','addNotice', $arrInput);


        $arrOutput = Service_Xiaoying_Xiaoying::getAnalysisBaseList($arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            Bingo_Log::warning(' xiaoying  analysisManager~,input:['.serialize($arrInput).'].output:['.serialize($arrOutput).']');
            return self::_jsonRet($arrOutput['errno'],$arrOutput['errmsg']);
        }
        $arrOutput['output'] = Bingo_Encode::convert($arrOutput['output'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
        $arrRet = array(
            "list" => $arrOutput['output']['list'],
        );
        $arrRet = array_merge($arrInput, $arrRet);
        //Bingo_Log::warning(var_export($arrOutput,true));
        $arrRet['page'] = array(
            'current_pn' => ++ $arrInput['pageno'],
            'total_pn' => ceil($arrOutput['output']['count']/$arrInput['count']),
        );


        return self::_jsonRet($arrOutput['errno'],$arrOutput['errmsg'],$arrRet);
    }

    /**
     * @brief _jsonRet
     * @param errno,errmsg,data
     * @return:  0.
     **/
    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array()){
        $arrRet = array(
            'no'=>intval($errno),
            'error'=>strval($errmsg),
            'data'=>$arrExtData,
        );
        Bingo_Log::pushNotice("errno",$errno);
        Bingo_Http_Response::contextType('application/json');
        $arrRet = Bingo_Encode::convert ($arrRet, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);

        echo Bingo_String::array2json($arrRet);
        return 0;
    }
	
}