<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-11-09
 * @version
 * @structs & methods(copied from idl.)
 */
class delpushTopicAction extends Util_Actionbase{

    /**
     * @param null
     * @return bool
     */
    public function init(){
        self::setUiAttr('COMMIT_UI');
        if(false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = 'init return error!';
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * @param null
     * @return bool
     */
    public function process(){

        $forum_id = intval(Bingo_Http_Request::getNoXssSafe('forum_id', ''));
        $thread_id = intval(Bingo_Http_Request::getNoXssSafe('thread_id', 0));

        if($forum_id <= 0){
            Bingo_Log::warning('param error. forum_ids='.$forum_id);
            self::_displayJson(Tieba_Errcode::ERR_PARAM_ERROR, 'forum_id');
            return false;
        }
        if($thread_id <= 0){
            Bingo_Log::warning('param error. thread_id='.$thread_id);
            self::_displayJson(Tieba_Errcode::ERR_PARAM_ERROR, 'thread_id');
            return false;
        }

        $arrInput = array(
            'forum_id' => $forum_id,
            'thread_id' => $thread_id,
        );
        $arrOut = Tieba_Service::call('topicthread', 'delTopicToForumByFid', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(false === $arrOut){
            Bingo_Log::warning('call topicthread:delTopicToForumByFid fail. arrInput='.serialize($arrInput));
            self::_displayJson(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return false;
        }
        if(0 !== $arrOut['errno']){
            Bingo_Log::warning('call topicthread:delTopicToForumByFid err. arrInput='.serialize($arrInput));
            self::_displayJson($arrOut['errno'],$arrOut);
            return false;
        }

        self::_displayJson($arrOut['errno'],$arrOut);
        return true;
    }

}