<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file 
 * <AUTHOR>
 * @date 2014/09/01 10:47:31
 * @brief
 *    查看审核列表
 *  
 **/
class delCommentAction extends Util_Toms_Audit_ActionBase {
    /**
     * @desc
     * @param
     * @return
     */
    public function init() {
        self::setUiAttr('COMMIT_UI');
        $this->_arrReq['operate'] = Util_Toms_Audit_Conf::OPERATOR_DEL_COMMENT;
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo ) {
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = 'init return error!';
                Bingo_Log::warning($this->_strErrorMsg);
                throw new exception($this->_strErrorMsg, $this->_intErrorNo);
            }
        }

        return true;
    }
}

