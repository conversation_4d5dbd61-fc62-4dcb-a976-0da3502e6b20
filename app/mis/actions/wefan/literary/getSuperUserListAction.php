<?php

/***************************************************************************
 *
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * Author: <EMAIL>
 * Date: 2016/9/9
 * Time: 16:22
 * Brief:
 *
 ***************************************************************************/

class getSuperUserListAction extends Util_Actionbase {

    /**
     * @return bool
     */
    public function init()
    {
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = 'init return error!';
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }


    /**
     * @return bool
     */
    public function process()
    {
        $intPn = intval(Bingo_Http_Request::getNoXssSafe('pn', 1));
        $intRn = intval(Bingo_Http_Request::getNoXssSafe('rn', 20));
        $intUserId = intval(Bingo_Http_Request::getNoXssSafe('search_user_id', 0));
        if($intPn < 1){
            $intPn = 1;
        }
        if($intRn < 1){
            $intRn = 20;
        }
        $arrInput = array_merge(Bingo_Http_Request::getGetAllNoXssSafe(), Bingo_Http_Request::getPostAllNoXssSafe());
        $arrInput['pn'] = $intPn;
        $arrInput['rn'] = $intRn;
        if($intUserId > 0 ){
            $arrInput['user_id'] = $intUserId;
            $arrRet = Tieba_Service::call('ntgroup', 'getGroupsOfSuperUserList', $arrInput, null, null, 'post', 'php', 'utf-8');
            $intErrNo = (isset($arrRet['errno'])) ? $arrRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            if (!empty($arrRet) && $intErrNo == $arrRet['errno']) {
                $this->_format($arrRet);
                $this->setErroInfo($intErrNo, 'success', $arrRet['data']);
                return true;
            }
            return false;
        }
        else{
            $arrRet = Tieba_Service::call('ntgroup', 'getSuperUserList', $arrInput, null, null, 'post', 'php', 'gbk');
            $intErrNo = (isset($arrRet['errno'])) ? $arrRet['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            if (empty($arrRet)|| Tieba_Errcode::ERR_SUCCESS != $intErrNo){
                Bingo_Log::warning(sprintf("call getWhiteUserList error  %s, %s"), serialize($arrInput), serialize($arrRet));
                $this->setErroInfo($intErrNo, $arrRet['errmsg']);
                return false;
            }
            $this->_format($arrRet);
            $this->setErroInfo($intErrNo, 'success', $arrRet['data']);
            return true;
        }
    }

    /**
     * @param $arrData
     */
    private function _format(&$arrData){
        if(is_array($arrData['data']['list'])){
            foreach ($arrData['data']['list'] as $key=>$value){
                $arrUserId[] = $value['user_id'];
                $arrGroupId[] = $value['group_id'];
            }
            $arrUserInfos = $this->_getUserInfos($arrUserId);
            $arrGroupInfos = $this->_getGroupInfo($arrGroupId);
            $arrTmp = array();
            foreach ($arrData['data']['list'] as $key=>$value){
                $value['user_name'] = $arrUserInfos[$value['user_id']]['nickname'];
                $value['group_name'] = $arrGroupInfos[$value['group_id']]['group_name'];
                $arrTmp[] = $value;
            }
            $arrData['data']['list'] = $arrTmp;
        }

    }


    /**
     * @brief 根据用户id获取用户名
     * @param $arrUserId
     * @return false if service call failed, user_name if success
     */
    private function _getUserInfos($arrUserId){
        $arrInput = array(
            'user_ids' => $arrUserId,
        );
        $ret = Tieba_Service::call('ntuser', 'getUserInfos', $arrInput, null, null, 'post', 'php', 'gbk');
        if(empty($ret) || Tieba_Errcode::ERR_SUCCESS != $ret['errno']){
            Bingo_Log::warning(sprintf("_getUserInfos getUserInfos failed input[%s], output[%s]", serialize($arrInput), serialize($ret)));
        }
        return $ret['data']['user_infos'];
    }

    /**
     * @brief 根据用户id获取用户名
     * @param $intUserId
     * @return false if service call failed, user_name if success
     */
    private function _getUserInfo($intUserId){
        $arrInput = array(
            'user_id' => $intUserId,
        );
        $ret = Tieba_Service::call('ntuser', 'getUserInfo', $arrInput, null, null, 'post', 'php', 'gbk');
        if(empty($ret) || Tieba_Errcode::ERR_SUCCESS != $ret['errno']){
            Bingo_Log::warning(sprintf("_getUserInfo getUserInfo failed input[%s], output[%s]", serialize($arrInput), serialize($ret)));
        }
        return $ret['data']['user_info'];
    }


    /**
     * @brief 获取群组信息
     * @param $arrGroupId
     * @return array
     */
    private function _getGroupInfo($arrGroupId){
        if(empty($arrGroupId)){
            return array();
        }
        $arrInput = array(
            'group_ids' => $arrGroupId,
        );
        $ret = Tieba_Service::call('ntgroup', 'mgetGroupInfo', $arrInput, null, null, 'post', 'php', 'gbk');
        if(empty($ret) || Tieba_Errcode::ERR_SUCCESS != $ret['errno']){
            Bingo_Log::warning(sprintf("_getGroupInfo mgetGroupInfo failed input[%s], output[%s]", serialize($arrInput), serialize($ret)));
        }
        $arrGroupInfo = array();
        if(is_array($ret['data'])){
            foreach($ret['data'] as $key=>$value){
                $arrGroupInfo[$value['group_id']] = $value;
            }
        }

        return $arrGroupInfo;
    }


    /**
     * @param int $errno
     * @param string $errmsg
     * @param array $data
     * @return bool
     */
    public function setErroInfo($errno=-1, $errmsg='', $data=array())
    {
        $intErrno = $errno;
        $strError = $errmsg;
        if ($errno != 0) {
            Bingo_Log::warning('no:'.$intErrno.' errmsg:'.$strError.' data:'.serialize($data));
        } else {
            Bingo_Log::debug('no:'.$intErrno.'errmsg:'.$strError.'data:'.serialize($data));
        }
        Bingo_Page::assign('no', $intErrno);
        Bingo_Page::assign('error', $strError);
        Bingo_Page::assign('data', $data);
        Bingo_Page::setOnlyDataType("json");
        return true;
    }

}