<?php
/**
 * Created by PhpStorm.
 * User: GaoMingcheng
 * Date: 16/11/16
 * Time: 19:55
 */

use Elasticsearch\ClientBuilder;

require_once __DIR__ . "/../vendor/autoload.php";

class addRecommendTopicAction extends Util_Actionbase {
    const PORT = '8002';
    protected static $_redis = array();
    protected static $_redisQueryCounter = 0;
    protected static $_default_redis = 'tbnt';
    /**
     * @brief
     * @param
     * @return
     */
    public function init(){
        self::setUiAttr('BROWSE_UI');
        parent::init();
        return true;
    }
    /**
     * @brief
     * @param
     * @return
     */
    public function process(){
        if($this->_check()){
            $arrOutput = $this->_process();
            echo Bingo_String::array2json($arrOutput);
        }
    }

    /**
     * @brief
     * @param
     * @return
     */
    public function _check(){
        return true;
    }

    /**
     * @brief
     * @param
     * @return
     */
    public function _process(){
        $topic_id = Bingo_Http_Request::get( 'topic_id', 0);
//        Bingo_Log::warning('topic_id : '. $topic_id);
        if ($topic_id <= 0) {
            return array();
        }
        $arrRet = Tieba_Service::call( 'nttopic', 'recommendTopic', compact('topic_id'), null, null, 'post', 'php', 'utf-8' );
        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            return self::_errRet($arrRet['errno']);
        }

        $arrInput = array(
            'topic_id' => $topic_id,
        );
        $arrRet = Tieba_Service::call('nttopic', 'getTopicInfo', $arrInput, null, null, 'post', 'php', 'utf-8');
        $title = $arrRet['data'][$topic_id]['title'];
        $arrCover = (array)($arrRet['data'][$topic_id]['cover']);
        $small_url = $arrCover['small_url'];
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
            Bingo_Log::warning('call nttopic getTopicInfo is fail;nput['.serialize($arrInput).']output['.serialize($arrRet).']');
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $user_id = $arrRet['data'][$topic_id]['user_id'];
        if ($user_id > 0){
            $arrRet = Tieba_Service::call( 'ntuser', 'addUserRecommendNum', compact('user_id'), null, null, 'post', 'php', 'utf-8' );
            if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
                Bingo_Log::warning('errno : '. $arrRet['errno']. ' errmsg : '. $arrRet['errmsg']);
            }
        }
//        Bingo_Log::warning(print_r($arrRet, 1));

        $arrRet = Tieba_Service::call( 'nttag', 'addRecomTopic', compact('topic_id'), null, null, 'post', 'php', 'utf-8' );
        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            return self::_errRet($arrRet['errno']);
        }
        Bingo_Log::warning(print_r($arrRet, 1));
        Bingo_Log::warning('errno : '. $arrRet['errno']. ' errmsg : '. $arrRet['errmsg']);

        $hosts  = [
            '10.' . '94.213.106' . ':' . self::PORT, // IP + Port
        ];
        $client = ClientBuilder::create()->setHosts( $hosts )
            ->build();
        $params = [
            'index' => 'nt',
            'type'  => 'topic',
            'id'    => (int)$topic_id,
            'body'  => [
                'doc' => [
                    'is_recommend'          => 1,
                ],
            ],
        ];
        $res    = $client->update( $params );
        
        $arrContent = array(
            'type' => 1,
            'content_message' => '您的文章被推荐到首页啦! 普大喜奔',
            'topic_title' => $title,
            'small_url'   => $small_url,
            'topic_id'    => $topic_id,
        );
        $strContent = json_encode($arrContent);
        $strContent = urlencode($strContent);
        $param = array(
            /**
             * 0: 有新的群聊消息（反拉模式，客户端需要当前的msg_id反拉消息）
             * 1: 群话题点赞
             * 2: 群话题点踩
             * 3: 群话题下墙
             * 4: 群主召唤粉丝消息
             * 5: 回复的点赞通知
             * 6: 发表的话题有新回复
             * 7：新粉丝通知
             * 8：关注的话题有新回复（接回复命令）
             * 9：新评论
             * 10：@消息
             * 11: 后台运营消息
             * 12: 聊天室有用户发了新话题, 需要更新话题排队数(带上群主标识)
             * 13：群主有话题上墙
             * 14: 群主强制话题下墙
             * 15: 有新的群聊消息（直推模式，客户端不用反拉消息）
             * 16：群话题被删除
             */
            'user_id' => $user_id,
            'msg' => array(
                'from_uid' => 2713219541,
                'msg_type' => 11,
                'sub_msg_type' => 1,
                'content' => '恭喜您,您的帖子《'. $title. '》'. '已被推荐到首页!',
                'user_id' => $user_id,
                'recommend_info' => $strContent,
            ),
        );

        Bingo_Log::warning('input param : ');
        Bingo_Log::warning(print_r($param, 1));
        $arrRet = Tieba_Service::call( 'ntgroup', 'sendUserMsg', $param, null, null, 'post', 'php', 'utf-8' );
        Bingo_Log::warning('output param : ');
        Bingo_Log::warning(print_r($arrRet, 1));
        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            return self::_errRet($arrRet['errno']);
        }

        $todayDataBegin = date("Y-m-d");
        $key = 'NT_RECOMMEND_DATA_'. $todayDataBegin;
        $input = array(
            'key' => $key,
        );
        $arrRet = $this->redisQuery('tbnt', 'GET', $input, 0);
        $intVal = intval($arrRet['ret'][$key]);
        $intVal++;
        $arrInput = array(
            'key' => $key,
            'value' => strval($intVal),
        );
        $this->redisQuery('tbnt', 'SET', $arrInput, 0);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief
     * @param
     * @return
     */
    public static function _errRet($errno,$errmsg='',array $arrExtData=array()){
        if($errmsg == ''){
            $errmsg = Bingo_Encode::convert(Tieba_Error::getErrmsg($errno), Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        }
        $arrRet = array(
            'no' =>intval($errno),
            'error'=>strval($errmsg),
            'data'  =>$arrExtData,
        );
        return $arrRet;
    }

    /**
     * @brief  get redis instance
     * @param  $strRedisName
     * @return bool|mixed
     */
    private static function _getRedis($strRedisName)
    {

        if (!$strRedisName) {
            $strRedisName = self::$_default_redis;
        }

        if (self::$_redis[$strRedisName]) {
            return self::$_redis[$strRedisName];
        }
        Bingo_Timer::start('redisinit_' . $strRedisName);

        $objRedis = new Bingo_Cache_Redis($strRedisName);

        Bingo_Timer::end('redisinit' . $strRedisName);
        if (!$objRedis->isEnable()) {
            Bingo_Log::warning("init redis fail.");
            return false;
        }
        self::$_redis[$strRedisName] = $objRedis;
        return self::$_redis[$strRedisName];
    }

    /**
     * @brief redis query
     * @param $strRedisName
     * @param $strFunc
     * @param array $arrParams
     * @param int $intRetryTimes
     * @return bool
     */
    public static function redisQuery($strRedisName, $strFunc, array $arrParams, $intRetryTimes = 0){
        $intRetryTimes = intval($intRetryTimes);
        $mixRedis = self::_getRedis($strRedisName);
        if(!$mixRedis){
            return false ;
        }

        self::$_redisQueryCounter++;
        $strTimerKey = "redisquery_".$strRedisName."_".$strFunc."_".self::$_redisQueryCounter;

        do {
            Bingo_Timer::start($strTimerKey);
            $arrRet = $mixRedis->$strFunc($arrParams);
            Bingo_Timer::end($strTimerKey);

            Bingo_Log::debug("$strTimerKey ret=".var_export($arrRet,true));

            if($arrRet['err_no'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning("call redis query error.[redisname=$strRedisName][func=$strFunc][input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
            }else{
                break;
            }
        }while($intRetryTimes-- > 0);
        return $arrRet;
    }
}