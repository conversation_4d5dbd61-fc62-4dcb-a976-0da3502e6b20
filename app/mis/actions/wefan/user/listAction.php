<?php
/**
 * @author: lihuan08
 * @date: 2016-08-15
 * @file: listAction.php
 * @description:
 */

class listAction extends Util_Actionbase{

    protected static $_nList = 20;
    
    public function init(){
        self::setUiAttr('BROWSE_UI');
        parent::init();
        return true;
    }
    public function process(){
        if($this->_check()){
            $arrOutput = $this->_process();
            echo Bingo_String::array2json($arrOutput);
        }
    }
    public function _check(){
        return true;
    }
    private function _process(){
        $pn = intval(Bingo_Http_Request::get('pn',1) );
        $ps = intval(Bingo_Http_Request::get('rn'));
        $user_type = intval( Bingo_Http_Request::get('user_type',1) );
        $search = intval(Bingo_Http_Request::get('user_id'));
        $user_official = intval( Bingo_Http_Request::get('user_official',0) );
        $user_nickname = strval( Bingo_Http_Request::get('user_nickname','') );
        $user_username = strval( Bingo_Http_Request::get('user_username','') );




        $pn = $pn <= 0 ? 1 : $pn;
        if(empty($ps)){
            $ps = self::$_nList;
        }

        if(!empty($user_username) || !empty($user_nickname)){
            $arrInput = array(
                'user_name' => $user_username,
                'nickname' => $user_nickname,
                'need_ext' => 1,
            );
            $ret = Tieba_Service::call('ntuser', 'getUserInfoByName', $arrInput, null, null, 'post', 'php', 'gbk');
            if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("get ntuser::getUserInfoByName failed. output[".serialize($ret)."]");
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $arrRet['list'] = array($ret['data']['user_info']);
        }


        if($search > 0){
            $param = array(
                'user_id' => $search,
                'need_ext' => 1,
            );
            $ret = Tieba_Service::call('ntuser', 'getUserInfo', $param, null, null, 'post', 'php', 'gbk');
            if ($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("get ntuser::getUserInfo failed. output[".serialize($ret)."]");
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $arrRet['list'] = array($ret['data']['user_info']);
        }

        if(empty($search) && empty($user_username) && empty($user_nickname)){
            //用户基本信息、扩展信息
            $param = array(
                'pn' => $pn,
                'rn' => $ps,
                'user_type' => $user_type,
                'user_official' => $user_official,
            );
            $ret = Tieba_Service::call('ntuser', 'getUserList', $param,null, null, 'post', 'php', 'gbk');
            if($ret == false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('call ntuser::getUserList fail, input='.serialize($param).'output='.serialize($ret));
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            if( !empty($ret['data']['list'])){
                foreach ($ret['data']['list'] as $key => $value) {
                    $arrUids[] = $value['user_id'];           
                }
                if( !empty($arrUids) ){
                    $param = array(
                        'user_ids' => $arrUids,
                    );
                    $arrUserExt = Tieba_Service::call('ntuser', 'mgetUserExtAttr', $param,null, null, 'post', 'php', 'gbk');
                    if($arrUserExt == false || $arrUserExt['errno'] != Tieba_Errcode::ERR_SUCCESS){
                        Bingo_Log::warning('call ntuser::mgetUserExtAttr fail, input='.serialize($param).'output='.serialize($arrUserExt));
                        //return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                    }
                    foreach ( $ret['data']['list'] as $key => &$value) {
                        if( !empty($arrUserExt['data'][$value['user_id']]) ){
                            $value = array_merge($arrUserExt['data'][$value['user_id']],$value);
                        }

                    }
                }               

            }
            $arrRet['page'] = array(
                'current_pn' => $ret['data']['current_pn'],
                'total_pn' => $ret['data']['page_num'],
            );
            $arrRet['list'] = $ret['data']['list'];
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,'',$arrRet);
    }
    /**
     * @brief
     * @param
     * @return
     */
    public static function _errRet($errno,$errmsg='',array $arrExtData=array()){
        if($errmsg == ''){
            //$errmsg = Tieba_Error::getErrmsg($errno);
            $errmsg = Bingo_Encode::convert(Tieba_Error::getErrmsg($errno), Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        }
        $arrRet = array(
            'no' =>intval($errno),
            'error'=>strval($errmsg),
            'data'  =>$arrExtData,
        );
        return $arrRet;
    } 
}

