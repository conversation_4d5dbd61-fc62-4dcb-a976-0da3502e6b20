<?php
/**
 *  获取乐此不疲移动聚合页某个列表（或按照游戏名称查找某个游戏）
 * @authors tanxinyun
 * @date    2015-03-26 17:49:43
 */

class listAction extends Actions_Game_Lcbp_ActionBase {

    /**
     * service方法与参数定义
     * @return [array] [参数结构体]
     */
    public function getServiceCallParam() {
        // 下面参数均为可选
        $attrs = array(
            'list_type',
            'pn',
            'rn',
            'game_name',
            'game_type',
        );

        $params = array(
            'service' => 'game',
            'method' => 'listLcbpGame',
            'attrs' => $attrs,
        );

        return $params;
    }
}