<?php
class getRankByNameAction extends Util_Actionbase{
	/**
	 * @see Util_Actionbase::init()
	 * @param
	 * @return bool
	 */
	public function init(){ 
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }       
        }       
        return true;
    }
	/**
     * @param
     * @return bool
     */
	public function process(){
		$strRankName = Bingo_Http_Request::getNoXssSafe('rank_name','');
		if (empty($strRankName)){
			echo self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
			return false;
		}
		$arrInput = array(
			'rank_name' => $strRankName,
		);
		$arrOut = Tieba_Service::call('game', 'getRankByName', $arrInput, null, null, 'post', 'php', 'utf-8');
		if (false == $arrOut){
			echo self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
			return false;
		}
		elseif (Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']){
			echo self::errRet($arrOut['errno']);
			return false;
		}
		$arrData = array(
			'rank_name' => $strRankName,
			'game_names' => $arrOut['data'],
		);
		echo self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
		return true;
	}
	/**
     * @param int $errno
     * @return json
     */
	private function errRet($errno, $data = array()){
		$arrOut['no'] = $errno;
        $arrOut['errMsg'] = Tieba_Error::getUserMsg($errno);
        $arrOut['data'] = $data;
		return json_encode($arrOut);
	}
}