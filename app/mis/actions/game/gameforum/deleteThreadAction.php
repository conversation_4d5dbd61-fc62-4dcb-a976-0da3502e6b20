<?php

class deleteThreadAction extends Bingo_Action_Abstract {
	
	private static function _errRet($errno, $data = "") {
		$errmsg = Tieba_Error::getErrmsg ( $errno );
		$arrRet = array ('no' => $errno, 'errMsg' => $errmsg );
		if ($data !== "") {
			$arrRet ['data'] = $data;
		}
		return Bingo_String::array2json ( $arrRet, 'utf-8' );
	}
	
	public function execute(){
	    $intId = intval(Bingo_Http_Request::getNoXssSafe('thread_id',0));
		//参数校验
	    if (! isset ( $intId ) || $intId<0) {
			Bingo_Log::warning ( "input params invalid " );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		
		$arrInput = array ('thread_id' => $intId );
		
		$arrOut = Tieba_Service::call ( 'game', 'deleteThread', $arrInput );
		
		//结果判断
		if (false === $arrOut) {
			echo self::_errRet ( Tieba_Errcode::ERR_UNKOWN );
			return false;
		} else if (Tieba_Errcode::ERR_SUCCESS == $arrOut ['errno']) {
			echo self::_errRet ( Tieba_Errcode::ERR_SUCCESS, $arrOut['data'] );
			return true;
		} else {
			echo self::_errRet ( $arrOut ['errno'] );
			return false;
	    }
	}
}
