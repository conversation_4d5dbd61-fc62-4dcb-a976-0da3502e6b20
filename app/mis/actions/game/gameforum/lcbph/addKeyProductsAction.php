<?php
class addKeyProductsAction extends Bingo_Action_Abstract {
	private $gameName4Fir = '';
	private $smallImage4Fir = '';
	private $gameName4Sec = '';
	private $smallImage4Sec = '';
	private $gameName4Thi = '';
	private $smallImage4Thi = '';
	
	/**
	 * (non-PHPdoc)
	 * @see Bingo_Action_Abstract::execute()
	 */
	public function execute() {
		$this->gameName4Fir = Bingo_Http_Request::getNoXssSafe ( 'game_name_fir', '' );
		$this->smallImage4Fir = Bingo_Http_Request::getNoXssSafe ( 'small_image_fir', '' );
		$this->gameName4Sec = Bingo_Http_Request::getNoXssSafe ( 'game_name_sec', '' );
		$this->smallImage4Sec = Bingo_Http_Request::getNoXssSafe ( 'small_image_sec', '' );
		$this->gameName4Thi = Bingo_Http_Request::getNoXssSafe ( 'game_name_thi', '' );
		$this->smallImage4Thi = Bingo_Http_Request::getNoXssSafe ( 'small_image_thi', '' );
		
		$arrInput = array (
			'game_name_fir' => $this->gameName4Fir,
			'small_image_fir' => $this->smallImage4Fir,
			'game_name_sec' => $this->gameName4Sec,
			'small_image_sec' => $this->smallImage4Sec,
			'game_name_thi' => $this->gameName4Thi,
			'small_image_thi' => $this->smallImage4Thi,
		);
		
		$arrOut = Tieba_Service::call ( 'game', 'addKeyProducts', $arrInput, null, null, 'post', 'php', 'gbk' );
		if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut ['errno']) {
			$arrOut ['errno'] = isset ( $arrOut ['errno'] ) ? $arrOut ['errno'] : Tieba_Errcode::ERR_UNKOWN;
			Bingo_Log::pushNotice ( 'errno', $arrOut ['errno'] );
			echo json_encode ( self::_errRet ( $arrOut ['errno'] ) );
			return false;
		}
		
		echo self::_errRet( Tieba_Errcode::ERR_SUCCESS );
		return true;
	}
	
	/**
	 * 数据返回函数
	 * @param unknown $errno
	 * @param string $data
	 * @return Ambigous <string, multitype:, unknown, multitype:Ambigous <multitype:, unknown> >
	 */
	private static function _errRet($errno, $data = "") {
		$errmsg = Tieba_Error::getErrmsg ( $errno );
		$arrRet = array ('errno' => $errno, 'errMsg' => $errmsg );
		if ($data !== "") {
			$arrRet ['data'] = $data;
		}
		Bingo_Log::pushNotice ( "errno", $errno );
		return Bingo_String::array2json($arrRet);
	}
}
