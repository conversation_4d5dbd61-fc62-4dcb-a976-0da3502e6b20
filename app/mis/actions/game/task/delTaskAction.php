<?php
class delTaskAction extends Util_Actionbase{
	/**
	 * @see Util_Actionbase::init()
	 * @param
	 * @return bool
	 */
	public function init(){ 
        self::setUiAttr('COMMIT_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }       
        }       
        return true;
    }
	/**
     * @param
     * @return bool
     */
	public function process(){
		
		$task_id = Bingo_Http_Request::getNoXssSafe('task_id',0);
		$arrInput = array(
			'task_id' => $task_id,
			'op_name' => Util_Actionbaseext::getCurPassName()
		);
		$arrOut = Tieba_Service::call('game', 'delGameTaskInfo', $arrInput, null, null, 'post', 'php', 'utf-8');
		if (false == $arrOut){
			self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
			return false;
		}
		elseif (Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']){
			self::errRet($arrOut['errno']);
			return false;
		}
		self::errRet(Tieba_Errcode::ERR_SUCCESS);
		return true;
	}
	/**
     * @param int $errno
     * @return json
     */
	private function errRet($errno, $data = array()){
		$arrOut['no'] = $errno;
        $arrOut['errMsg'] = Tieba_Error::getUserMsg($errno);
        $arrOut['data'] = $data;
		echo Bingo_String::array2json($arrOut);
	}
}
