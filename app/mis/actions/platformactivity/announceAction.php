<?php

class announceAction extends Util_Actionbase {
    static public $errno    = 0;
    static public $errmg    = '';
    static public $data     = array ();
    protected $_arrReq      = array();

    public $intActivityId   = 0;
    public $intThreadId     = 0;
    const ERR_PARAM_MSG = '参数错误';


    function init () {
        self::setUiAttr('COMMIT_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = 'init return error!';
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        $result = true;

        return $result;
    }

    public function process () {
        if (false === $this->input()) {
            return false;
        }
        // 拉取photoshow的数据
        $arrInput = array (
            array(
                'activity_id' => $this->_arrReq['activity_id'],
            ),
        );
        $arrOutput = Actions_Platformactivity_Lib_Util::getActivityInfoByActivityIds($arrInput);

        $intThreadId = 0;
        $arrLocationList = $arrOutput[1]['activity_info']['location_list'];
        foreach($arrLocationList as $val) {
            $intLocationType = (int)$val['location_type'];
            if (Actions_Platformactivity_Lib_Util::LOCATION_TYPE_THREAD) {
                $intThreadId = $val['location_related_id'];
            }
        }

        if (0 >= $intThreadId) {
            $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID, self::ERR_PARAM_MSG);
            return false;
        }

        $arrComponentList = current($arrOutput[1]['activity_info']['component_list']);
        $intActivityId = (int)$arrComponentList['component_related_id'];
        $arrInput = array(
            'activity_id'   => $intActivityId,
            'thread_id'     => $intThreadId,
        );
        $arrRet = Tieba_Service::call('photoshow', 'publishLuckyUser', $arrInput);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            $strMsg = sprintf('call photoshow::publishLuckyUser fail with input [%s] output [%s]', serialize($arrInput), serialize($arrRet));
            Bingo_Log::warning($strMsg);
            $this->setErroInfo(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, $arrRet['errmsg']);
            $this->finish();
            return false;
        }

        $arrInput['pic_num'] = 50; // 默认取票数最高50张
        $arrRet = Tieba_Service::call('photoshow', 'setVoteSortToPb', $arrInput);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            $strMsg = sprintf('call photoshow::setVoteSortToPb fail with input [%s] output [%s]', serialize($arrInput), serialize($arrRet));
            Bingo_Log::warning($strMsg);
            $this->setErroInfo(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, $arrRet['errmsg']);
            $this->finish();
            return false;
        }
        
        self::$data = $arrRet;
        $this->finish();

        return true;
    }

    public function input () {
        $this->_arrReq['activity_id'] = intval(Bingo_Http_Request::getNoXssSafe('activity_id')); // 活动ID
        if (($this->_arrReq['activity_id'] <= 0)) {
            $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID, self::ERR_PARAM_MSG);
            $this->finish();

            return false;
        }

        return true;
    }

    public function printLog () {
        if (self::$errno != 0) {
            Bingo_Log::warning(self::$errmg);

            return;
        } else {
            Bingo_Log::notice(self::$errmg);
        }
    }

    public function finish () {
        $this->printLog();
        self::$errmg = Bingo_Encode::convert(self::$errmg, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
        Bingo_Page::assign('no', self::$errno);
        Bingo_Page::assign('errMsg', self::$errmg);
        Bingo_Page::assign('data', self::$data);
        Bingo_Page::setOnlyDataType("json");

        return true;
    }

    public function setErroInfo ($errno = -1, $errmsg = '', $data = array ()) {
        self::$errno = $errno;
        self::$errmg = $errmsg;
        self::$data  = $data;

        return;
    }
}
