<?php

class Actions_Platformactivity_Lib_Category_Base {
    const SUBTYPE_PREFIX       = 'Actions_Platformactivity_Lib_Category_';
    const SUBTYPE_DEFAULT      = 'Default';

    const SUBTYPE_CROWDFUNDING = 1;

    protected  static $_arrSubTypeMap = array(
        self::SUBTYPE_CROWDFUNDING => 'Crowdfunding',
    );

    protected static $_intErrno   = 0;
    protected static $_strErrmsg  = 'success';

    /**
     * @desc 
     * @param
     * @return
     */
    public static function init() {
        return true;
    }

    // 活动相关
    /** 
     * @desc 根据活动类别，组织活动的入参形式
     * @param
     * @return
     * @NOTE: 默认不做处理
     */
    public static function getAddActivityInput($arrInput) {
        return $arrInput;
    }

    /**
     * @desc 
     * @param
     * @return
     */
    public static function buildAddActivityOutput($arrInput) {
        return $arrInput;
    }
    
    /**
     * @desc 添加活动
     * @param
     * @return
     * @NOTE: 这个必须手动实现
     */
    public  static function addActivity($arrInput) {
    }

    /**
     * @desc 组织修改活动的入参形式
     * @param
     * @return
     * @NOTE: 理论上，跟getAddActivityInput相同
     */
    public  static function getModifyActivityInput($arrInput) {
        return $arrInput;
    }

    /**
     * @desc 修改活动
     * @param
     * @return
     */
    public  static function modifyActivity($arrInput) {
    }
    
    /**
     * @desc 获取活动
     * @param
     * @return
     */
    public  static function getActivityInfo($arrInput) {
        return self::buildReturn(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @desc 提交活动
     * @param
     * @return
     */
    public  static function commitActivity($arrInput) {
    }
        
    
    /**
     * @desc 组织活动返回格式
     * @param
     * @return
     */
    public  static function buildActivityData($arrInput) {
        return $arrInput;
    }

    /**
     * @desc 删除活动
     * @param
     * @return
     */
    public  static function deleteActivity($arrInput) {
        
    }

    /**
     * @desc 复活活动
     * @param
     * @return
     */
    public  static function recoverActivity($arrInput) {
        
    }

    /**
     * @desc 重启活动
     * @param
     * @return
     */
    public  static function reOpenActivity($arrInput) {
        
    }

    // 组件相关
    
    /**
     * @desc
     * @param
     * @return
     */
    public  static function getAddComponentInput($arrInput) {
        return $arrInput;
    }

    /**
     * @desc 添加活动
     * @param
     * @return
     */
    public  static function addComponent($arrInput) {
        
    }

    /**
     * @desc 组织修改活动的入参形式
     * @param
     * @return
     * @NOTE: 理论上，跟getAddActivityInput相同
     */
    public  static function getModifyComponentInput($arrInput) {
        return $arrInput;
    }

    /**
     * @desc 修改活动
     * @param
     * @return
     */
    public  static function modifyComponent($arrInput) {
        
    }
    
    /**
     * @desc 获取活动
     * @param
     * @return
     */
    public  static function getComponnetInfo($arrInput) {
       return $arrInput; 
    }
    
    /**
     * @desc 组织活动返回格式
     * @param
     * @return
     */
    public  static function buildComponnetData($arrInput) {
        return $arrInput;
    }

    /**
     * @desc 有时候需要从activity_info中抽取出组件信息
     * @param
     * @return
     */
    public  static function buildComponnetDataFromActivityInfo($arrInput) {
        return $arrInput;
    }

    // 奖品相关
    /** 
     * @desc 根据活动类别，组织活动的入参形式
     * @param
     * @return
     */
    public  static function getAddAwardInput($arrInput) {
        return $arrInput;
    }

    /**
     * @desc 添加活动
     * @param
     * @return
     */
    public  static function addAward($arrInput) {
        
    }
    
    /**
     * @desc 追加奖品
     * @param
     * @return
     */
    public  static function appendAward($arrInput) {
        
    }

    /**
     * @desc 组织修改活动的入参形式
     * @param
     * @return
     * @NOTE: 理论上，跟getAddActivityInput相同
     */
    public  static function getModifyAwardInput($arrInput) {
        
    }

    /**
     * @desc 修改活动
     * @param
     * @return
     */
    public  static function modifyAward($arrInput) {
        
    }
    
    /**
     * @desc 获取活动
     * @param
     * @return
     */
    public  static function getAwardInfo($arrInput) {
        return $arrInput;
    }
    
    /**
     * @desc 组织活动返回格式
     * @param
     * @return
     */
    public  static function buildAwardData($arrInput) {
        return $arrInput;
    }

    /**
     * @desc 有时候需要从activity_info中抽取奖品信息
     * @param
     * @return
     */
    public  static function buildAwardDataFromActivityInfo($arrInput) {
        return $arrInput;
    }

    /**
     * @desc 删除活动
     * @param
     * @return
     */
    public  static function deleteAward($arrInput) {

    }
    

    /**
     * @desc 检测分享活动的时间和业务方的时间是否一致 
     * @param [in] subType : uint32_t : 子业务方
     * @param [in] other   : mix      : 其他数据
     * @return
     */
    public  static function checkParam($arrInput) {
        $intSubType = (int)($arrInput['act_info']['extra_info']['related_info']['type']);
        $strSubType = self::_getSubType($intSubType);
        $strMethod  = 'checkParam';
        return call_user_func(array($strSubType, $strMethod), $arrInput);
    }
    
    /**
     * @desc 获取添加活动时的入参
     * @param [in] subType : uint32_t : 子业务方
     * @param [in] other   : mix      : 其他数据
     * @return
     */
    public  static function getInputParam($arrInput) {
        $intSubType = (int)($arrInput['act_info']['extra_info']['related_info']['type']);
        $strSubType = self::_getSubType($intSubType);
        $strMethod  = 'getInputParam';
        return call_user_func(array($strSubType, $strMethod), $arrInput);
    }

    /**
     * @desc 添加活动
     * @param [in] subType : uint32_t : 子业务方
     * @param [in] other   : mix      : 其他数据
     */
    /*public  static function addActivity($arrInput) {
        $intSubType = (int)($arrInput['act_info']['extra_info']['related_info']['type']);
        $strSubType = self::_getSubType($intSubType);
        $strMethod  = 'addActivity';
        return call_user_func(array($strSubType, $strMethod), $arrInput);
    }*/
    
    /**
     * @desc 添加组件
     * @param [in] subType : uint32_t : 子业务方
     * @param [in] other   : mix      : 其他数据
     */
    /*public  static function addComponent($arrInput) {
        $intSubType = (int)($arrInput['act_info']['extra_info']['related_info']['type']);
        $strSubType = self::_getSubType($intSubType);
        $strMethod  = 'addComponent';
        return call_user_func(array($strSubType, $strMethod), $arrInput);
    }*/
    
    /**
     * @desc 修改奖品
     * @param [in] subType : uint32_t : 子业务方
     * @param [in] other   : mix      : 其他数据
     * @return
     */
    public  static function updateAward($arrInput) {
        $intSubType = (int)($arrInput['act_info']['extra_info']['related_info']['type']);
        $strSubType = self::_getSubType($intSubType);
        $strMethod  = 'updateAward';
        return call_user_func(array($strSubType, $strMethod), $arrInput);
    }

    /**
     * @desc 添加奖品
     * @param [in] subType : uint32_t : 子业务方
     * @param [in] other   : mix      : 其他数据
     * @return
     */
    /*public  static function addAward($arrInput) {
        $intSubType = (int)($arrInput['act_info']['extra_info']['related_info']['type']);
        $strSubType = self::_getSubType($intSubType);
        $strMethod  = 'addAward';
        return call_user_func(array($strSubType, $strMethod), $arrInput);
    }*/
    
    /**
     * @desc 追加奖品
     * @param [in] subType : uint32_t : 子业务方
     * @param [in] other   : mix      : 其他数据
     * @return
     */
    /*public  static function appendAward($arrInput) {
        $intSubType = (int)($arrInput['act_info']['extra_info']['related_info']['type']);
        $strSubType = self::_getSubType($intSubType);
        $strMethod  = 'appendAward';
        return call_user_func(array($strSubType, $strMethod), $arrInput);
    }*/

    /**
     * @desc 删除奖品
     * @param [in] subType : uint32_t : 子业务方
     * @param [in] other   : mix      : 其他数据
     * @return
     */
    public  static function delAward($arrInput) {
        $intActivityId = (int)($arrInput['act_info']['activity_id']);
        $arrParam      = array(
            'activity_id' => $intActivityId,
        );
        $arrRet = Tieba_Service::call('genesis', 'getActivityInfo', $arrParam);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            $strMsg = sprintf('call genesis::getActivityInfo fail with input [%s] output [%s]', serialize($arrParam), serialize($arrRet));
            Bingo_Log::warning($strMsg);
            return $arrRet;
        }
        $intSubType = (int)($arrRet['output']['activity_info']['extra_info']['related_info']['type']);
        $strSubType = self::_getSubType($intSubType);
        $arrData = current($arrRet['output']['activity_info']['component_list']);
        $arrAwardInfo = $arrData['component_data']['award_info'];
        $strMethod  = 'delAward';
        foreach($arrAwardInfo as $key => $arrItem) {
            $arrOut = call_user_func(array($strSubType, $strMethod), $arrItem);
            if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                return $arrOut;
            }
        }

        return $arrOut;
    }
    
    /**
     * @desc 提交活动
     * @param [in] subType : uint32_t : 子业务方
     * @param [in] other   : mix      : 其他数据
     * @return
     */
    /*public  static function buildAwardData($arrInput) {
        $intSubType = (int)($arrInput['extra_info']['related_info']['type']);
        $strSubType = self::_getSubType($intSubType);
        $strMethod  = 'buildActivityData';
        return call_user_func(array($strSubType, $strMethod), $arrInput);
    }*/

    /**
     * @desc 提交活动
     * @param [in] subType : uint32_t : 子业务方
     * @param [in] other   : mix      : 其他数据
     * @return
     */
    public  static function getAward($arrInput) {
        $intActivityId   = (int)($arrInput['activity_id']);
        $intActivityType = (int)($arrInput['activity_type']);
        $arrParam  =array(
            array(
                'activity_id' => $intActivityId,
            ),
        );
        $arrRes = (Actions_Platformactivity_Lib_Util::getActivityInfoByActivityIds($arrParam));
        $arrActivityInfo = $arrOutput[1]['activity_info'];
        $intSubType = (int)($arrActivityInfo['extra_info']['related_info']['type']);
        $strSubType = self::_getSubType($intSubType);
        $strMethod  = 'buildActivityData';
        return call_user_func(array($strSubType, $strMethod), $arrInput);
    }

    /**
     * @desc 提交活动
     * @param [in] subType : uint32_t : 子业务方
     * @param [in] other   : mix      : 其他数据
     * @return
     */
    public  static function commit($arrInput) {
        $intSubType = (int)($arrInput['act_info']['extra_info']['related_info']['type']);
        $strSubType = self::_getSubType($intSubType);
        $strMethod  = 'commit';
        return call_user_func(array($strSubType, $strMethod), $arrInput);
    }
    
    /**
     * @desc 提交活动
     * @param [in] subType : uint32_t : 子业务方
     * @param [in] other   : mix      : 其他数据
     * @return
     */
    public  static function getActivity($arrInput) {
        $intActivityId   = (int)($arrInput['activity_id']);
        $intActivityType = (int)($arrInput['activity_type']);
        $arrParam  =array(
            array(
                'activity_id' => $intActivityId,
            ),
        );
        $arrRes = (Actions_Platformactivity_Lib_Util::getActivityInfoByActivityIds($arrParam));
        $arrActivityInfo = $arrOutput[1]['activity_info'];
        $intSubType = (int)($arrActivityInfo['extra_info']['related_info']['type']);
        $strSubType = self::_getSubType($intSubType);
        $strMethod  = 'buildActivityData';
        return call_user_func(array($strSubType, $strMethod), $arrInput);
    }
    
    /**
     * @desc 提交活动
     * @param [in] subType : uint32_t : 子业务方
     * @param [in] other   : mix      : 其他数据
     * @return
     */
    /*public  static function buildActivityData($arrInput) {
        $intSubType = (int)($arrInput['extra_info']['related_info']['type']);
        $strSubType = self::_getSubType($intSubType);
        $strMethod  = 'buildActivityData';
        return call_user_func(array($strSubType, $strMethod), $arrInput);
    }*/

    /**
     * @desc 根据子类型获取子类型处理类
     * @param [in] sub_type : uint32_t : 子分类
     * @return 
     */
    protected  static function _getClassName($sub_type) {
        $strSubType = isset(self::$_arrSubTypeMap[$sub_type]) ? self::$_arrSubTypeMap[$sub_type] : self::SUBTYPE_DEFAULT;

        $strSubType = sprintf("%s%s", self::SUBTYPE_PREFIX, $strSubType);

        return $strSubType;
    }

    /**
     * @desc 
     * @param
     * @return
     */
    protected function _fastCall($type, $method, $arrInput) {
        $strClass = self::_getClassName($type);
        $method   = $method;
        return call_user_func(array($strClass, $method), $arrInput);
    }
}
