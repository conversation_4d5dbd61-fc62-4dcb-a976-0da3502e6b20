<?php
/**
 * @author: tangyan
 * @date: Thu Aug 21 16:01:09 CST 2014
 * @file: modifyPathAction.php
 * @description:
 */

class cancelProductAction extends Util_Actionbase{
	/**
		*
		*	初始化
		* 	@param
		* 	@return
		*
	 */
    public function init(){
        //self::setUiAttr('SCRIPT_UI');
        self::setUiAttr('COMMIT_UI');
        parent::init();
        return true;
    }
	/**
		*
		*	执行函数
		* 	@param
		* 	@return
		*
	 */
    public function process(){
        if($this->_check()){
            $this->_process();
        }
        $this->_build();
    }
	/**
		*
		*	check 函数
		* 	@param
		* 	@return
		*
	 */
    private function _check(){
        return true;
    }
	/**
		*
		*	私有执行
		* 	@param
		* 	@return
		*
	 */
    private function _process(){
        
        //$param   = array_map(array('Bingo_Http_Request','_stripslashesDeep'), $_POST);
		$ids= Bingo_Http_Request::getNoXssSafe("ids",'');
		$arrParam	= array(
			'sql'	=> 'update product_content set status = 1 where id in ('.$ids.');',
		);
		$arrRet	= Tieba_Service::call('yule','queryDb',$arrParam);

		$this->_intErrorNo = $arrRet['errno'];

    }

	/**
		*
		*	构造返回值
		* 	@param
		* 	@return
		*
	 */
    private function _build(){
        $arrData = array(
            'no' => $this->_intErrorNo,
            'error' => Tieba_Error::getErrmsg($this->_intErrorNo),
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrData);
    }
    
	/**
		*	由id获得用户名
		*
		* 	@param
		* 	@return
		*
	 */
    public static function getUidByUnames($arr_user_name) {
        $arrInput = array( 'user_name' => $arr_user_name );
        Bingo_Timer::start(__METHOD__);
        $arrOutput = Tieba_Service::call('user', 'getUidByUnames', $arrInput,  null, null, 'post', 'php','utf-8');
        Bingo_Log::warning(print_r($arrOutput,1));
        Bingo_Timer::end(__METHOD__);
        return $arrOutput['output']['uids'];
    }
    
}
