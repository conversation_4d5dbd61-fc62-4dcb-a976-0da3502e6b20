<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file conflictAction.php
 * <AUTHOR>
 * @date 2015/10/21 11:32:39
 * @brief 
 *  
 **/
class conflictAction extends Actions_Adsense_Gdsys_GdBaseAction
{

    /**
    *  @brief
    *
    *  @return bool 
    */
    public function process() {
       $arrInput = array();
       $arrRet = Tieba_Service::call('adsense', 'gdsys_conflict', $arrInput, null, null, 'post','php', 'utf-8', null);
       if (!isset($arrRet['errno']) || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
           if (!isset($arrRet['errno'])) {
               $arrRet['errno']  = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
               $arrRet['errmsg'] = "Unknown error, call service failed";
           }
           $this->_jsonRet($arrRet['errno'], 'service error:' . $arrRet['errmsg'], $arrInput);
           return false;
       }
       $this->_jsonRet($arrRet['errno'], $arrRet['errmsg'], $arrRet['data'], array('count' => $arrRet['count']));
       return true; 
    }

}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
