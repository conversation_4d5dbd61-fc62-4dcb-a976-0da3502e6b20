<?php
/*
 * <AUTHOR>
 * @date 20150515
 * @desc 导出各个端、页面、目录的配置，格式为：
 *     一级目录 二级目录    投放平台/终端   投放页面    广告位置    接入DSP1    接入DSP2    接入DSP3    接入DSP4    接入DSP-N   优先级/概率设定详情
 *     
 * @notation this file is utf-8 encoded
 */
class exportConfigAction extends Actions_Adsense_Grayscale_GrayscaleBase {
    function init() {
		self::setUiAttr('BROWSE_UI');
		if (false === parent::init()) {
			if (0 === $this->_intErrorNo) {
				$this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = 'init err!';
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
    }

    public function process() {
		//参数获取
		$strFdirs = strval(Bingo_Http_Request::getNoXssSafe('first_dirs', ''));

        //如果前端传入了则只查询相关的一级目录
        $strFdirs = self::_replaceComma($strFdirs);
        $arrFdirs = explode(',', $strFdirs);
        $arrValidFdirs = array(); 
        foreach ($arrFdirs as $strFdir) {
            if (strlen($strFdir) == 0) {
                continue;
            }
            $arrValidFdirs[$strFdir] = 1;
        }

        //建立dsp_code_name与dsp_name之间的映射
        $arrRet = self::getDspCodeNameMap();
        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
			self::_jsonRet($arrRet['errno'], 'call getDspCodeNameMap error:' .
			        $arrRet['errmsg']);
            return false;
        }
        $arrDspCodeName2DspName = $arrRet['data']['dcn_2_dn'];
        $intDspNum              = intval($arrRet['data']['dsp_num']);

        $arrColTpl = array();
        for ($i = 0; $i < $intDspNum + 6; ++$i) {
            $arrColTpl[$i] = '0';
        }

        //获取位置编号及位置对应描述
        $arrRet = self::getPosNameMap();
        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
			self::_jsonRet($arrRet['errno'], 'call getPosNameMap error:' .
			        $arrRet['errmsg']);
            return false;
        }
        $arrPosName2PosDesc = $arrRet['data']['pn_2_pd'];

        //获取贴吧所有的目录
        $arrSInput = array();
        $arrRet = Tieba_Service::call('forum', 'getAllDir', $arrSInput);
        if (Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            print_r('call service failed');
            Bingo_Log::warning("call redis failed");
            return false;
        }
        $arrAllDirs = $arrRet['output']['all_dir'];

        //单独抽取出所有的二级目录
        $arrAllSecDirs = array();
        foreach ($arrAllDirs as $arrDirs) {
            $arrAllSecDirs = array_merge($arrAllSecDirs, 
                    $arrDirs['level_2_name']);
        }

        //打印表头,设置dsp对应的列
        echo "<pre>";
        $arrTopCol = $arrColTpl;
        $arrTopCol[0] = self::_cvt('一级目录');
        $arrTopCol[1] = self::_cvt('二级目录');
        $arrTopCol[2] = self::_cvt('投放平台/终端');
        $arrTopCol[3] = self::_cvt('投放页面');
        $arrTopCol[4] = self::_cvt('广告位置');
        $arrTopCol[5] = self::_cvt('优先级/概率设定详情');
        $arrDspCodeNameIndex = array();
        $i = 6;
        foreach ($arrDspCodeName2DspName as $strDcn => $strDn) {
            $arrDspCodeNameIndex[$strDcn] = $i;
            $arrTopCol[$i]                = $strDn;
            ++$i;
        }
        echo implode("\t", $arrTopCol) . "\n";

        //循环client_type
        $arrClientTypes     = array('PC', 'WAP', 'APP', 'SDK', 'MINIAPP');
        $arrDeviceTypes = array(1, 2);
        foreach ($arrClientTypes as $strClientType) {
            $intCnt = 0;
            //循环device_type
            foreach ($arrDeviceTypes as $intDeviceType) {
                if ($strClientType == 'PC' && ++$intCnt >= 2) {
                    break;
                }
                $strDevice = Tbapi_Ad_Midl_Conf_Base::getDevice($strClientType, 
                        $intDeviceType);
        
                //循环查看页面
                $arrPageNames = array('FRS', 'PB', 'INDEX', 'RECOMM');
                foreach ($arrPageNames as $strPageName) {
                    //调用search方法拉出所有二级目录的配置
                    $arrSInput = array(
                        'devices'           => $strClientType,
                        'device_types'      => $intDeviceType,
                        'page_names'        => $strPageName,
                        'forum_second_dirs' => implode(',', $arrAllSecDirs),
                        'status'            => 3,
                    );
                    $arrRet = Tieba_Service::call('adsense', 
                            'searchGsOperation', $arrSInput);
                    if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                        Bingo_Log::warning("errno:[{$arrRet['errno']}],"
                                . "errmsg:[{$arrRet['errmsg']}]");
                        print_r("search_operation_{$strClientType}_"
                                . "{$intDeviceType}_{$strPageName}_error\n");
                        continue;
                    }

                    //记录二级目录与operation_id的对应关系        
                    $arrOpIds       = array();
                    $arrSecDir2OpId = array();
                    foreach ($arrRet['data'] as $arrOp) {
                        $op_id = $arrOp['operation_id'];
                        $arrOpIds[] = $op_id;
                        foreach ($arrOp['search_target'] as $arrRange) {
                            if ($arrRange['range_type'] != 'forum_second_dir') {
                                continue;
                            }
                            $arrSearchSecDirs = explode(',', 
                                    $arrRange['range_ids']);
                            foreach ($arrSearchSecDirs as $strSearchSecDir) {
                                $arrSecDir2OpId[$strSearchSecDir] = $op_id;
                            }
                        }
                    }

                    if (empty($arrOpIds)) {
                        echo self::_cvt("$strDevice 在$strPageName 没有对应的配置\n");
                        continue;
                    }

                    //拉取所有的operation_id相关配置
                    $arrSInput = array(
                        'operation_ids' => $arrOpIds,
                        'ret_type'      => 2,
                    );
                    $arrRet = Tieba_Service::call('adsense', 'getGsRules', 
                            $arrSInput);
                    if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                        Bingo_Log::warning("errno:[{$arrRet['errno']}],"
                                . "errmsg:[{$arrRet['errmsg']}]");
                        print_r("get_rules_{$strClientType}_{$intDeviceType}_" 
                                . "{$strPageName}_error\n");
                        continue;
                    }
                    $arrSplitOps = $arrRet['data'];
                    unset($arrRet);
                                                                    
                    //循环查看一级目录                              
                    foreach ($arrAllDirs as $arrDirs) {
                        $strFirName = $arrDirs['level_1_name'];
                        if (!empty($arrValidFdirs) 
                            && !isset($arrValidFdirs[$strFirName])) {
                            continue;
                        }

                        //循环查看二级目录
                        foreach ($arrDirs['level_2_name'] as $strSecName) {
                            $intTargetOpId = $arrSecDir2OpId[$strSecName];

                            //取相关operation的楼层详细配置
                            $arrDetails = array();
                            foreach ($arrSplitOps[$intTargetOpId][$strDevice][$strPageName] as 
                                $strRangeType => $arrRangeConf) {

                                foreach ($arrRangeConf as 
                                    $strRangeId => $arrRangeIdConf) {

                                    $arrDetails = $arrRangeIdConf;
                                    break; //一个operation下的conf是一样的
                                } //range id level
                                break;
                            } //range type level

                            //遍历位置
                            foreach ($arrDetails as $pos => $pos_conf) {
                                $arrLine = $arrColTpl;
                                $arrLine[0] = $strFirName;
                                $arrLine[1] = $strSecName;
                                $arrLine[2] = $strDevice;
                                $arrLine[3] = $strPageName;
                                $arrLine[4] = $arrPosName2PosDesc[$pos];

                                $arrStrategy = 
                                        self::getStrategy($pos_conf['config']);
                                $arrLine[5] = 'unknown';
                                if ($arrStrategy['type'] == 'priority') {
                                    $arrLine[5] = self::_cvt('按优先级');
                                } else if ($arrStrategy['type'] == 'chance') {
                                    $arrLine[5] = self::_cvt('按概率');
                                } else if ($arrStrategy['type'] == 'merge') {
                                    $arrLine[5] = self::_cvt('按优先级混合');
                                }

                                foreach ($arrStrategy['content'] as 
                                        $strDsp => $weight) {
                                    $arrLine[$arrDspCodeNameIndex[$strDsp]] 
                                            = $weight;
                                }

                                echo implode("\t", $arrLine) . "\n";
                            } //遍历位置 end
                        } //二级目录 end
                    } //一级目录 end
                } //页面 end
            } //device_type end
        } //client_type end
        echo "</pre>";

        return true;
    }

    /**
     * <AUTHOR>
     * @param
     * @return
            array $arrOutput:
                int 'errno'
                str 'errmsg'
                array 'data':
                    int 'dsp_num' //dsp的数量
                    array 'dcn_2_dn':
                        str 'self'  '自营app' //key为dsp_code_name, value为dsp名
                        str 'domob' '多盟'
                        ...
     * @desc 返回dsp_code_name与dsp_name的映射表
     */
    static public function getDspCodeNameMap() {
        $arrRet = Tieba_Service::call('adsense', 'getDspList', array());
        if (!isset($arrRet['errno']) || 
                Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            if (!isset($arrRet['errno'])) {
                $arrRet['errno']  = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
                $arrRet['errmsg'] = "Unknown error, call service failed";
            }
            return $arrRet;
		}
        $intDspNum = count($arrRet['data']['list']);
        $arrDspCodeName2DspName = array();
        foreach ($arrRet['data']['list'] as $map) {
            $arrDspCodeName2DspName[$map['dsp_code_name']] = $map['dsp_name'];
        }

        $arrOutput = array(
            'errno'  => Tieba_Errcode::ERR_SUCCESS,
            'errmsg' => 'success',
            'data'   => array(
                'dsp_num'  => $intDspNum,
                'dcn_2_dn' => $arrDspCodeName2DspName,
            ),
        );
        return $arrOutput;
    }

    /**
     * <AUTHOR>
     * @param
     * @return
            array $arrOutput:
                int 'errno'
                str 'errmsg'
                array 'data':
                    int 'pos_num'
                    array 'pn_2_pd':
                        str '3' 'pc端frs页feed流3层' //key为位置编号，value为位置描述
                        str 'NAV_TOP' '导航条顶部'
                        ...
     * @desc 获取位置编号与位置描述的对应表
     */
    public static function getPosNameMap() {
        $arrSInput = array();
        $arrRet = Tieba_Service::call('adsense', 'getRules', $arrSInput);
        if (!isset($arrRet['errno']) || 
                Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            if (!isset($arrRet['errno'])) {
                $arrRet['errno']  = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
                $arrRet['errmsg'] = "Unknown error, call service failed";
            }
            return $arrRet;
		}

        $intPosNum = count($arrRet['data']);
        $arrPosName2PosDesc = array();
        foreach ($arrRet['data'] as $arrRule) {
            $arrPosName2PosDesc[$arrRule['pos_name']] = $arrRule['pos_desc'];
        }

        $arrOutput = array(
            'errno'  => 0,
            'errmsg' => 'success',
            'data'   => array(
                'pos_num' => $intPosNum,
                'pn_2_pd' => $arrPosName2PosDesc,
            ),
        );

        return $arrOutput;
    }

    /**
     * <AUTHOR>
     * @param
            str $str //UTF-8编码字符串
     * @return
            str //GBK编码字符串
     * @desc 将UTF-8编码的内容转换为GBK编码
     */
    protected static function _cvt($str) {
        return Bingo_Encode::convert($str, Bingo_Encode::ENCODE_GBK, 
                Bingo_Encode::ENCODE_UTF8);
    }

    /**
     * <AUTHOR>
     * @param
            string 'priority' //按优先级配置，例如game;app|1;2
            string 'ratio'    //按比例配置，例如game;app|1;2
            string 'chance'   //按概率配置，例如game;app|1;2
            string 'merge'    //混合配置
            string 'config_extra'
     * @return
            array :
                string 'type'  //策略的类型，'priority','ratio','chance','merge'
                string 'config_extra' //额外信息
                array  'content' :
                    int <选择1> //选择1对应的权重 
                    int <选择2> //选择2对应的权重 
                    ...
                    int <选择n> //选择n对应的权重 
     * @desc 解析策略内容
     */
    public static function getStrategy($arrInput) {
        $type = '';
        foreach (array('priority', 'ratio', 'chance', 'merge') as $strategy) {
            if (isset($arrInput[$strategy]) && !empty($arrInput[$strategy])) {
                $type = $strategy;
                break;
            }
        }
        $parts            = explode('|', $arrInput[$type]);
        $arrContentKeys   = explode(';', $parts[0]);
        $arrContentValues = explode(';', $parts[1]);

        $arrContent = array();
        for ($i = 0; $i < count($arrContentKeys); ++$i) {
            $arrContent[$arrContentKeys[$i]] = $arrContentValues[$i] + 0.0; //转换为float
        }

        $arrOutput['content'] = $arrContent;
        $arrOutput['type']    = $type;
        $arrOutput['config_extra']   = $arrInput['config_extra'];
        return $arrOutput;
    }
}
