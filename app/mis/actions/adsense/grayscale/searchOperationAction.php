<?php
/*
 * <AUTHOR>
 * @date 20141013
 * @desc 根据条件筛选operation
 *       当没有指定吧、目录等范围时，操作是筛选操作
 *       当指定了吧、目录等范围时，操作是查找这些范围当前应用的配置是什么
 */
class searchOperationAction extends Actions_Adsense_Grayscale_GrayscaleBase {
	function init() {
		self::setUiAttr('BROWSE_UI');
		if (false === parent::init()) {
			if (0 === $this->_intErrorNo) {
				$this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = 'init err!';
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}

    public function process() {
        //参数获取
		$strDevice	   = strval(Bingo_Http_Request::getNoXssSafe('device', ''));
		$strDeviceType = strval(Bingo_Http_Request::getNoXssSafe('device_type', ''));
		$strPageNames  = strval(Bingo_Http_Request::getNoXssSafe('page_names', ''));
		$intCoverAll   = intval(Bingo_Http_Request::getNoXssSafe('cover_all', 0));
		$strFnames	   = strval(Bingo_Http_Request::getNoXssSafe('forum_names', ''));
		$strFdirs	   = strval(Bingo_Http_Request::getNoXssSafe('forum_dirs', ''));
		$strFsdirs	   = strval(Bingo_Http_Request::getNoXssSafe('forum_second_dirs', ''));
		$strFvdirs	   = strval(Bingo_Http_Request::getNoXssSafe('forum_vdirs', ''));
        $intStatus     = intval(Bingo_Http_Request::getNoXssSafe('status', 0));
        $strDsp        = strval(Bingo_Http_Request::getNoXssSafe('dsp', ''));
        $strOpUser     = strval(Bingo_Http_Request::getNoXssSafe('op_user', ''));
        $strOperationName = strval(Bingo_Http_Request::getNoXssSafe('operation_name', ''));

        $intPn	   = intval(Bingo_Http_Request::getNoXssSafe('current_pn', 1));
        $intRn	   = intval(Bingo_Http_Request::getNoXssSafe('rn', 200));

        //参数容错
        $strDevice     = self::_replaceComma($strDevice);
        $strDeviceType = self::_replaceComma($strDeviceType);
		$strPageNames  = self::_replaceComma($strPageNames);
		$strFnames	   = self::_replaceComma($strFnames);
		$strFdirs	   = self::_replaceComma($strFdirs);
		$strFsdirs	   = self::_replaceComma($strFsdirs);
		$strFvdirs	   = self::_replaceComma($strFvdirs);

		$strDefaults  = NULL;
		$arrPageNames = explode(',', $strPageNames);
		if (1 === $intCoverAll) {
			$strDefaults = '1';
		}

        $arrInput = array(
			'devices'		 	=> $strDevice,
			'device_types'		=> $strDeviceType,
			'page_names'	 	=> $strPageNames,
			'forum_names'	 	=> $strFnames,
			'forum_dirs'	 	=> $strFdirs,
			'forum_second_dirs' => $strFsdirs,
			'forum_vdirs'	    => $strFvdirs,
			'defaults'			=> $strDefaults,
            'status'            => $intStatus,
            'dsp'               => $strDsp,
            'op_user'           => $strOpUser,
            'operation_name'    => $strOperationName,
            'rn' => $intRn,
            'pn' => $intPn,
        );
        $arrRet = Tieba_Service::call('adsense', 'searchGsOperation', 
                $arrInput);

		if (!isset($arrRet['errno']) || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            if (!isset($arrRet['errno'])) {
                $arrRet['errno']  = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
                $arrRet['errmsg'] = "Unknown error, call service failed";
            }
			self::_jsonRet($arrRet['errno'], 'service error:' . $arrRet['errmsg'], $arrInput);
			return false;
		}

        //将列表按操作时间降序排序
        usort($arrRet['data'], array($this, "sort_op_time"));
        $arrList = array_slice($arrRet['data'],($intPn-1) * $intRn, $intRn);
        $total_count = count($arrRet['data']);
        $total_pn = ($total_count % $intRn) ? ($total_count / $intRn + 1) : ($total_count / $intRn);
        $arrOutput = array (
            'list' => $arrList,
            'current_pn' => intval($intPn),
            'total_pn' => intval($total_pn),
            'total_count' => intval($total_count),
        );
		self::_jsonRet($arrRet['errno'], $arrRet['errmsg'], $arrOutput);
		return true;
    }


}
