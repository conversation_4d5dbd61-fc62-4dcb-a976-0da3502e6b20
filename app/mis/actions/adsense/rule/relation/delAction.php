<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-06-20 16:15:29
 * @comment 删除规则配置
 * @version
 */
class delAction extends Util_Actionbase {

    /**
     * 初始化
     * @return bool
     */
    function init() {
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        
        return true;
    }

    /**
     * 执行
     * @return bool
     */
    public function process() {
    
        //参数获取
        $ruleid = strval(Bingo_Http_Request::getNoXssSafe('ruleid', ''));
        $configid = strval(Bingo_Http_Request::getNoXssSafe('configid', ''));

        // 掉用Service
        $arrParams = array(
            'ruleid' => $ruleid,
            'configid' => $configid,
        );
        $arrRet = Tieba_Service::call('adsense', 'delRulesConfigRelation', $arrParams, NULL, NULL, 'post', 'php', 'utf-8', NULL);
        
        // 检查调用结果
        if (!isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            $strLog = 'Service call failed. The service name is addRulesConfigRelation, params is ' . serialize($arrParams) . ']';
            Bingo_Log::warning($strLog);
        }

        // 默认成功返回值
        $this->_sendJson($arrRet);
        return true;
    }
    
    /**
     * 发送Json
     * @param $arrInput array 内容
     * @return bool
     */
    private function _sendJson($arrInput) {
        header("Content-Type: text/html; charset=utf-8");
        header("Content-type: application/json");
        echo json_encode($arrInput);
    }
}
