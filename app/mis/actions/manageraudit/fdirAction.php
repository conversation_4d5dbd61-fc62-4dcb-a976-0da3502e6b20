<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file fdirAction.php
 * <AUTHOR>
 * @date 2016/10/09 18:22:03
 * @brief 
 *  
 **/
class fdirAction extends Util_Actionbase {
	/**
	 * @param 
	 * @return bool
	 */
	function init(){
		self::setUiAttr('BROWSE_UI');
		if (false === parent::init()){
			if(0 === $this->_intErrorNo){
				$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}

	/**
	 * @param 
	 * @return array
     */
	public function process(){
		$arrLevels = array();
        $arrRes = Tieba_Service::call('forum','getAllDir',array(),null,null,'post','php','utf-8');
        if (false === $arrRes || $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call forum getAllDir fail");
        	return self::buildRes(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, '', array());
        }
        $arrTemp = $arrRes['output']['all_dir'];
        $arrLevels[] = array(
            'label' => '默认目录',
            'value' => '',
        );
        $arrLevels[] = array(
        	'label' => '无目录',
        	'value' => 'none',
        );
        if (!empty($arrTemp)) {
            foreach($arrTemp as $arrDir) {
                $strFirstLevel = strval($arrDir['level_1_name']);
                if($strFirstLevel == '高等院校' || $strFirstLevel == '地区'){
                    continue;
                }
                $arrLevels[] = array(
                	'label' => $strFirstLevel,
                	'value' => $strFirstLevel,
                );
            }
        }

        return self::buildRes(Tieba_Errcode::ERR_SUCCESS, '', array('options' =>$arrLevels));
	}
	/**
     * @param
     * @return array
     */
    public static function buildRes($intErrno, $strError = '', $arrData = null) {

        $arrRet = array(
            'no'    => $intErrno,
            'error' => empty($strError) ? Tieba_Error::getErrmsg($intErrno) : $strError,
            'data'  => $arrData,
        );

        $strRet = Bingo_String::array2json($arrRet);
        echo $strRet;
        if(Tieba_Errcode::ERR_SUCCESS !== $intErrno){
            return false;
        }
        return true;
    }
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
