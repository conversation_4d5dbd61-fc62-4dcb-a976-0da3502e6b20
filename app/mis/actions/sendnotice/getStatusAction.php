<?PHP
class getStatusAction extends Util_Actionbase{
	function init(){
		self::setUiAttr('BROWSE_UI');
		$id = intval(trim(Bingo_Http_Request::getNoXssSafe("id",0)));
		if (false === parent::init() || $id <= 0){
			if(0 === $this->_intErrorNo){
				$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
				Bingo_Page::setOnlyDataType ( "json" );
			}
		}
		return true;
	}

	public function process(){		 
		//�����ݿ��л�ȡ����״̬
		$id = intval(trim(Bingo_Http_Request::getNoXssSafe("id",0)));
		$arrInput = array(
			'batch_id' => $id,
		);
		$misService = new Service_Mis();
		$arrOutput= $misService->call('sendnotice','getSendNoticeNum',$arrInput);
		if ( $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS ){
			Bingo_Log::warning('fail to call addNotice');
			$this->_arrTplVar['errno']  = $arrOutput['errno'];
			Bingo_Page::setOnlyDataType ( "json" );
			return false;
		}
		$this->_arrTplVar['errno']  = Tieba_Errcode::ERR_SUCCESS;
		$this->_arrTplVar['total_count'] = intval($arrOutput['output']['total_count']);
		$this->_arrTplVar['send_count'] = intval($arrOutput['output']['send_count']);
		Bingo_Page::setOnlyDataType ( "json" );
		return true;
		 
	}
	
	 
}
?>
