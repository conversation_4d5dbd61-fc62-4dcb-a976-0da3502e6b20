<?php

/**
 *
 * User: huangling02
 * Date: 2017/3/25
 * Time: 17:19
 */
class forumCommitAction extends Util_Actionbase
{
    const SUCCESS_STATUS = 1;
    const FAIL_STATUS = 0;
    private $_intOpType;
    private $_arrFmap;
    private $_strFDir;
    private $_strSDir;
    private static $_objRalMulti;
    private static $_multiKey = "catalog_";
    private static $_arrMap = array();
    /**
     * @return bool
     */
    public function init() {
        self::setUiAttr('COMMIT_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = 'init return error!';
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * @return bool
     */
    public function process() {
        if(!$this->_initParam()){
            return self::buildRes(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrFids = array_keys($this->_arrFmap);
        $arrInput = array(
            'forum_id'        => $arrFids,
            'level_1_name'    => $this->_strFDir,
            'level_2_name'    => $this->_strSDir,
        );
        $arrOutput = Tieba_Service::call('forum', 'managerPassSecDir', $arrInput , null , null , 'post' , null , 'utf-8');
        if(false === $arrOutput|| Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call forum::addFirDir failed input[%s] output[%s]",serialize($arrInput), serialize($arrOutput)));
            return self::buildRes(-1, 'change failed !');
        }
        $this->_checkPlatformForum();
        $this->_removeForumCache();
        return self::buildRes(Tieba_Errcode::ERR_SUCCESS);
    }
    /**
     * @param
     * @return bool
     */
    private function _checkPlatformForum() {
        $arrInput = array(
            'cond' => array(
                'level_1_name=' => $this->_strFDir,
                'level_2_name=' => $this->_strSDir,
            ),
        );
        $arrOutput = Tieba_Service::call('official', 'getPtSecondDirInfo', $arrInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning('call official::getPtSecondDirInfo error input[' . serialize($arrInput) . ']_[' . serialize($arrPtDirRes).']' );
            return false;
        }
        $intStatus  = (int)$arrOutput['data']['list'][0]['status'];
        $intPtTotal = (int)$arrOutput['data']['total'];

        if ($intPtTotal > 0 && 0 !== $intStatus) {
            $strMethod = 'setForumAttr';
        } else {
            $strMethod = 'delForumAttr';
        }

        foreach($this->_arrFmap as $intFid => $strFname){
            $arrInput = array(
                'forum_id' => $intFid,
                'attr_name' => 'pt_operation_center',
                'attr_value' => array(
                    'type' => 'pt_common',
                ),
            );
            $arrOutput = Tieba_Service::call('forum', $strMethod, $arrInput);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
                Bingo_Log::warning(sprintf("call forum $strMethod error  input[%s] output[%s]", serialize($arrInput) , serialize($arrOutput) ));
            }
        }
        return true;
    }

    /**
     * @param
     * @return bool
     */
    private function _removeForumCache() {
        //清理cache
        $objRalMulti = new Tieba_Multi('catalog_cache_remove');
        $strKey = "catalog_cache_remove_";
        foreach($this->_arrFmap as $intFid => $strFname){
       
            $arrMultiInput = array(
                'serviceName' => 'forum',
                'method' => 'delcache_getForumInfoByName',
                'input' => array(
                    'forum_name' => $strFname,
                ),
            );
            $objRalMulti->register($strKey . $intFid, new Tieba_Service('forum'), $arrMultiInput);
            
        }
        $arrOut = $objRalMulti->call();
        foreach($this->_arrFmap as $intFid => $strFname){
            $strTempKey = $strKey . $intFid;
            $arrOutput = $arrOut[$strTempKey];
            if ($arrOutput === false || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning(sprintf("call forum::delcache_getForumInfoByName fail fid=$intFid fname=$strFname output=[%s]", serialize($arrOutput) ));
                continue;
            }
        
        }
        return true;
    }
    /**
     * [_getForumid description]
     * @param  [type] $strFname [description]
     * @return [type]           [description]
     */
    private static function _mgetForumid($arrFnames) {

        $arrInput = array(
            'query_words' => $arrFnames,
        );
        $arrOutput = Tieba_Service::call('forum', 'getFidByFname', $arrInput, null, null, 'post', 'php', 'utf-8');
        //Bingo_Log::warning(print_r($arrOutput,1));
        if (false === $arrOutput||$arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call forum getFnameByFid fail input[' . serialize($arrInput) . "] out[" . serialize($arrOutput) . "]");
            return false;
        }

        $arrFmap = array();
        foreach($arrOutput['forum_id'] as $k => $v){
            $intFid   = (int)$v['forum_id'];
            if(0 !== $intFid){
                $strFname = $v['forum_name'];
                $arrFmap[$intFid] = $strFname;
                
            }
        }
        return $arrFmap;
    }
    /**
     * [_initParam description]
     * @param
     * @return [type] [description]
     */
    private function _initParam(){
        $this->_strFDir = Bingo_Http_Request::getNoXssSafe('fdir', '');
        $this->_strSDir = Bingo_Http_Request::getNoXssSafe('sdir', '');
        $strForumNames = Bingo_Http_Request::getNoXssSafe('forum_names', '');
        //Bingo_Log::warning(print_r($strForumNames,1));
        $arrForumNames = explode("\n", $strForumNames);
        //Bingo_Log::warning(print_r($arrForumNames,1));
        if('' ===$this->_strFDir || '' === $this->_strSDir){
            return false;
        }
        $arrFmap = self::_mgetForumid($arrForumNames);
        if(false === $arrFmap|| empty($arrFmap)){
            return false;
        }

        $this->_arrFmap = $arrFmap;
        //Bingo_Log::warning(print_r($this,1));
        return true;
    }
    /**
     * @param
     * @return array
     */
    public static function buildRes($intErrno, $strError = '', $arrData = null) {

        $arrRet = array(
            'no'    => $intErrno,
            'error' => empty($strError) ? Tieba_Error::getErrmsg($intErrno) : $strError,
            'data'  => $arrData,
        );

        $strRet = Bingo_String::array2json($arrRet);
        echo $strRet;
        if(Tieba_Errcode::ERR_SUCCESS !== $intErrno){
            return false;
        }
        return true;
    }
}
