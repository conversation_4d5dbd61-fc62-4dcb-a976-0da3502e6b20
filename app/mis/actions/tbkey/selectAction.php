<?php
define('BINGO_ENCODE_LANG', 'UTF-8');

class selectAction extends Util_Actionbase {
	
	
	public function init(){
		parent::init();
		return true;
	}
	
	public function initParam() {
		$this->_checkInput("pn",-1,"_pn","intval",true);
		$this->_checkInput("cn",0,"_cn","intval",true);
	}
	
	public function execute(){
		try {
			self::initParam();		
			$activity_id = Bingo_Http_Request::get('activity_id');
			$limit = $this->_cn > 0 ? $this->_cn : 10;
			$offset = ($this->_pn - 1) * $limit;
			$offset = $offset > 0 ? $offset : 0;
			$arrParams = array(
					"offset" => $offset,
					"limit" => $limit,
					"get_total_num" => 1,
					"activity_id" => intval($activity_id),
			);
			$arrRet = Tieba_Service::call("tbkey","getUserAwardRecordForMis",$arrParams, NULL, NULL, 'post', 'php', 'utf-8');
			if($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				throw new Exception($arrRet['errmsg'],$arrRet['errno']);
			}
			$data = $arrRet['data'];
			$ret = array(
					"page" => array(
							"current_pn" => $this->_pn,
							"total_pn" => intval(ceil($data['tn']/$this->_cn)),
							"total_count" => $data['tn'],
					),
					"list" => $data['data'],
			);
            $this->_jsonRet(0, 'success' , $ret);
        }catch(Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            $this->_jsonRet($e->getCode(), 'unknown error');
        }
	}
	
	protected function _checkInput($key,$defaultValue,$classVar,$convertFunc="",$force=false,$errMsg=""){
		if($convertFunc === ""){
			$this->$classVar = Bingo_Http_Request::getNoXssSafe($key, $defaultValue);
		}else{
			$this->$classVar = $convertFunc(Bingo_Http_Request::getNoXssSafe($key, $defaultValue));
		}
		Bingo_Log::pushNotice("input_key:$key",$this->$classVar);
		if($this->$classVar === $defaultValue&&$force){
			$errInfo = $errMsg===""?"$key loss":$errMsg;
			throw new Exception($errInfo,Tieba_Errcode::ERR_PARAM_ERROR);
		}
	}
	
	protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array()){
		$arrRet = array(
				'no'=>intval($errno),
				'error'=>strval($errmsg),
				'data'=>$arrExtData,
		);
		Bingo_Log::pushNotice("errno",$errno);
		Bingo_Http_Response::contextType('application/json');
		echo Bingo_String::array2json($arrRet,'UTF-8');
	}
}