<?php
	class  goodsBrowse {
		public static function getData(){
			$arrOut = array(
							'pn'         => 1,
							'pz'         => 10,
							'page_name'    => 'frs',
							'good_name'   => '',
							'good_desc'   => '',
							'good_type'   => '',
							'pos_name'     => 4,
							'template_name'=> 'frs_4',
							'prjclass'    => '游戏',
							'property_1'   => 'a',
							);
			$arrOut = Bingo_Encode::convert($arrOut,Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
			return $arrOut;
		}
	}
?>
