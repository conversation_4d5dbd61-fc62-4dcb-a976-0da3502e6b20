<?php

class Util_Orpstorage {

    //orp文件存储wiki：http://fe.baidu.com/doc/orp/Storage/lib-api.text
    const ORP_STORAGE_TOKEN = "5Pm4xjGMUml9OBFk1EsOsJJra765jUYn";
    const ORP_STORAGE_PRODUCT = "tieba";

    const ORP_STORAGE_PATH_PREFIX = "/mis/easy/";

    public $_storage;
    private static $_instance;
    private function __construct() {
        $this->_storage = new Orp_Storage(self::ORP_STORAGE_TOKEN, self::ORP_STORAGE_PRODUCT);
    }
    public static function getInstance() {
        if (is_null(self::$_instance) || !isset(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    //封装Orp_Storage的api，自动加上路径前缀
    public function get($filePath) {
        return $this->_storage->get(self::ORP_STORAGE_PATH_PREFIX.$filePath);
    }
    public function upload($file, $dstPath='') {
        return $this->_storage->upload($file, self::ORP_STORAGE_PATH_PREFIX.$dstPath);
    }
    public function delete($filePath) {
        return $this->_storage->delete(self::ORP_STORAGE_PATH_PREFIX.$filePath);
    }
    public function getFileInfo($filePath) {
        return $this->_storage->getFileInfo(self::ORP_STORAGE_PATH_PREFIX.$filePath);
    }
    public function listDir($path) {
        return $this->_storage->listDir(self::ORP_STORAGE_PATH_PREFIX.$path);
    }
}