<?php
/**
 * @brief : 会员中心二级卡片mis配置
 * <AUTHOR> gaoweizhen01<[<email address>]>
 * @date : 2017-09-11
 */

class getAllSubCardListAction extends Util_Actionbase {


	/**
	 * @param null
	 * @return bool [<description>]
	 */
	function init() {
		self::setUiAttr('BROWSE_UI');
		if (false == parent::init()) {
			if (0 == $this->_intErrorNo) {
				$this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}

	/**
	 * @brief : 执行入口
	 * @param : array [<description>]
	 * @return : array [<description>]
	 */
	public function process() {
		$arrInput = array();
        
        $strCardName = strval(Bingo_Http_Request::get('card_name', ''));
        if ("" != $strCardName) {
            $input = array(
                "card_name" => $strCardName,
            );
            $arrRes = Tieba_Service::call('member', 'getSubCardByCName', $input, null, null, 'post', 'php', 'utf-8');
            if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']) {
                Bingo_Log::warning("func getAllCardList failed. input: [" . serialize($arrInput) . "]output: [" . serialize($arrRes) . "]");
                self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                return;
            }
            $arrRet['list'] = $arrRes['results'][0]; 
            $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, 'success', $arrRet['list']);
            return true;

        } else {
		    $arrRes = Tieba_Service::call('member', 'getAllSubCardList', $arrInput, null, null, 'post', 'php', 'utf-8');
            if (false == $arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']) {
			    Bingo_Log::warning("func getAllSubCardList failed. input: [" . serialize($arrInput) . "]output: [" . serialize($arrRes) . "]");
				self::_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
				return;
		    }
			$arrRet['list'] = $arrRes['results'][0]; 
			//self::_displayJson(Tieba_Errcode::ERR_SUCCESS, $arrRes['results']);
			$this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, 'success', $arrRet['list']);
			return true;
        }
	}

	/**
    * @param array
    * @return array
    **/
    protected function _jsonRet($errno, $errmsg='', $arrExtData=array())
    {
        $arrRet = array(
            'no'=>intval($errno),
            'errMsg'=>strval($errmsg),
            'data'=>$arrExtData,
        );
        Bingo_Log::pushNotice("errno",$errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet,'UTF-8');
    }
}

































































