<?php

class getModuleAction extends Util_Actionbase{
	function init(){
		self::setUiAttr('BROWSE_UI');
		if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }       
        }       
        return true;
	}

	private function _retError($errno){
		$arrRet = array(
			'no' => $errno,
			'errMsg' => Tieba_Error::getErrmsg($errno),
		);

		echo json_encode($arrRet);
	}

	public function process(){
		$arrInput = array(
			'id' => 1 ,//must have one param	
		);
		$arrRet = Service_Mis::call('cfound','getAllModule',$arrInput);
		if($arrRet===false || $arrRet['errno']!=Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning(sprintf("call cfound_addModule error. [input:%s].[ouput:%s]"), serialize($arrInput),serialize($arrRet));
			return $this->_retError($arrRet['errno']);
		}
        $errno = Tieba_Errcode::ERR_SUCCESS;
        $arrRet = array(
            'no' => $errno, 
            'errMsg' => Tieba_Error::getErrmsg($errno),
            'data' => $arrRet['ret'],
        );
		echo json_encode($arrRet);
	}
}
