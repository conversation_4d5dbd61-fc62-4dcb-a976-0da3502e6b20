<?php
/**
 * Created by PhpStorm.
 * User: jiajint<PERSON> 
 * Date: 2016/11/11
 * Time: 22:48
 */
class addBookAction extends Util_Actionbase {

    private $_arrValidParams = array(
        'cp_id' => 's',
        'book_type' => 'i',
        'title' => 's',
        'author' => 's',
    );

    /**
     * init
     * @return bool
     */
    public function init() {
        self::setUiAttr('COMMIT_UI');
        parent::init();
        return true;
    }

    public function process() {
        $cp_id = trim(Bingo_Http_Request::getNoXssSafe("cp_id", ""));
        $book_type = intval(Bingo_Http_Request::getNoXssSafe("book_type", 0));
        $title = trim(Bingo_Http_Request::getNoXssSafe("title", ""));
        $author = trim(Bingo_Http_Request::getNoXssSafe("author", ""));
        $intOpUid = intval(Util_ActionbaseExt::getCurPassId());
        $strOpUname = strval(Util_ActionbaseExt::getCurPassName());

		$arrParams = array(
			'cp_id' => $cp_id,
			'book_type' => $book_type,
			'title' => $title,
			'author' => $author,	
		);

        if(empty($cp_id) || $book_type <= 0 || empty($title) || empty($author)) {
            Bingo_Log::warning('param error, input='.serialize($arrParams));
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return;
        }
        $arrParams['op_uid'] = $intOpUid;
        $arrParams['op_uname'] = $strOpUname;

        $arrRet = Tieba_Service::call('manuallyread', 'addBookInfo', $arrParams);
        if(Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning('call service addBookInfo fail, ret='.serialize($arrRet));
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, null, $arrRet['errmsg']);
            return;
        }

        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $errno
     * @param $data
     */
    protected function _jsonRet($errno, $data = null, $msg = "" ){
        $arrOutput = array(
            'no' => $errno,
            'msg' => !empty($msg) ? $msg : Tieba_Error::getErrmsg($errno),
        );
        if($data !== null){
            $arrOutput['data'] = $data;
        }
        Bingo_Page::setOnlyDataType('json');
        $this->_arrTplVar = $arrOutput;
    }
}
