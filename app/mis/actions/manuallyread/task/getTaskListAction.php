<?php
/**
 * Created by PhpStorm.
 * User: jiajintao
 * Date: 2016/11/11
 * Time: 19:37
 */
class getTaskListAction extends Util_Actionbase {

    /**
     * [init description]
     * @return [type] [description]
     **/
    function init(){
        self::setUiAttr('COMMIT_UI');
        parent::init();
        return true;
    }
        
    public function process() {
        $book_id = intval(Bingo_Http_Request::getNoXssSafe("book_id", 0));
        $pn = intval(Bingo_Http_Request::getNoXssSafe("pn", 1));
        $rn = intval(Bingo_Http_Request::getNoXssSafe("rn", 20));

		if ($pn <= 0 || $rn <= 0 || $book_id <= 0) {
        	Bingo_Log::warning("input params invalid.book_id[$book_id] pn[$pn] rn[$rn]"); 
        	$this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
        	return;
		}
		$arrParams = array(
			'book_id' => $book_id,
			'rn' => $rn,
			'pn' => $pn,	
		);

        //Bingo_Log::warning(var_export($arrParams, 1));
        $arrRet = Tieba_Service::call('manuallyread', 'mgetTaskInfo', $arrParams);
        if($arrRet === false || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning('call service mgetTaskInfo fail, ret='.serialize($arrRet));
            $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, null, $arrRet['errmsg']);
            return;
        }

        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
    }

    /**
     * @param $errno
     * @param $data
     */
    protected function _jsonRet($errno, $data = null, $msg = "" ){
        $arrOutput = array(
            'no' => $errno,
            'msg' => !empty($msg) ? $msg : Tieba_Error::getErrmsg($errno),
        );
        if($data !== null){
            $arrOutput['data'] = $data;
        }
        Bingo_Page::setOnlyDataType('json');
        $this->_arrTplVar = $arrOutput;
    }
}
