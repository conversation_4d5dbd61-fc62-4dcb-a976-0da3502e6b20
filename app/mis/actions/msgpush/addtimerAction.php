<?php
class addtimerAction extends Bingo_Action_Abstract
{
    public function execute(){
        $task_info = Bingo_Http_Request::get('task_info', 'testtesttest');
        $day_push_time = Bingo_Http_Request::get('day_push_time', '0');
        $week_push_time = Bingo_Http_Request::get('week_push_time', '0');
        $month_push_time = Bingo_Http_Request::get('month_push_time', '0');
        $operator = Bingo_Http_Request::get('operator', 'xiaojianxin');
        $arrInput = array(
            'task_info' => $task_info,
            'day_push_time' => $day_push_time,
            'week_push_time' => $week_push_time,
            'month_push_time' => $month_push_time,
            'operator' => $operator,
            'create_time' => time(),
        );
        $ret = Tieba_Service::call("opmsg","addTimer",$arrInput);
        
        $result = array(
            "status" => $ret["errno"],
            "msg" => $ret["errmsg"],
       );
        echo Bingo_String::array2json($result);
        return true;
    }
    
}
?>
