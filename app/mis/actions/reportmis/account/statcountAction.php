<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @desc 用户举报MIS查询待审核数量
 * @date 2017-11-04
 */
class statcountAction extends Util_Actionbase
{
    protected static $_pageSize = 10;

    /**
     * 初始化函数
     * @return bool
     */
    function init()
    {
        self::setUiAttr('BROWSE_UI');//设置UI的类型
        //基类初始化，初始化失败且未设置错误代码设置错误代码为参数设置错误
        if (false == parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = Tieba_Errcode::$codes[$this->_intErrorNo];
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * 主函数
     * @return bool
     */
    public function process()
    {
        $reportType = Bingo_Http_Request::getNoXssSafe('report_type', '');
        if (!array_key_exists($reportType, Util_Reportmis_Const::$complaint_types)) {
            $this->_arrTplVar['errno'] = Tieba_Errcode::ERR_MO_PARAM_INVALID;
            $this->_arrTplVar['errmsg'] = Tieba_Errcode::$codes[Tieba_Errcode::ERR_MO_PARAM_INVALID];
            Bingo_Page::setOnlyDataType("json");
            return false;
        }
        $reportType = Util_Reportmis_Const::$complaint_types[$reportType];
        foreach (Util_Reportmis_Const::$vipType as $vk => $isVip) {
            foreach (Util_Reportmis_Const::$ReportTimes as $tk => $reportTimes) {
                foreach (Util_Reportmis_Const::$pageStatus as $key => $status) {
                    //已完成数量统计意义不大，直接返回0，页面已经不在显示
                    if ($key === 'done' || $key === 'auto') {
                        $data[$vk][$tk][$key] = 0;
                        continue;
                    }
                    $data[$vk][$tk][$key] = self::getCount($reportTimes, $reportType, $isVip, $status);
                }
            }
        }
        $this->_arrTplVar['data'] = $data;
        $this->_arrTplVar['errno'] = $this->_intErrorNo;
        $this->_arrTplVar['errmsg'] = $this->_strErrorMsg;
        Bingo_Page::setOnlyDataType("json");
        return true;
    }

    /**
     * 获取记录个数
     * @param $reportTimes
     * @param $reportType
     * @param $isVip
     * @param $status
     * @return int
     */
    private function getCount($reportTimes, $reportType, $isVip, $status)
    {
        $arrInput = array(
            'status' => $status,
            'complaint_type' => $reportType,
            'is_vip' => $isVip,
            'report_times' => $reportTimes,
        );
        if ($status === -1) {
            $arrInput['lock_uid'] = Util_Actionbaseext::getCurUserId();
        }
        $dealCount = Service_Reportmis_Account_Audit::getReportCount($arrInput);
        return intval($dealCount['ret']);
    }
}

?>