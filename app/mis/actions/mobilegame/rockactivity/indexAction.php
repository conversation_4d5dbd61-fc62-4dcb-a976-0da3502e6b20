<?php
/**
 * 娱乐后台MIS入口
 * 
 * @authors wangjunsheng
 * @date    2015-12-22 
 */

class indexAction extends Util_Actionbase {

    /**
     * [init description]
     * @return [type] [description]
     */
	function init(){
        self::setUiAttr('COMMIT_UI');
        
        if (false === parent::init()) {
            if(0 === $this->_intErrorNo) {
                $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }

        return true;
    }

    /**
     * [process description]
     * @return [type] [description]
     */
    public function process() { 
        Bingo_Log::warning($strPageBaseDir);
        Bingo_Page::setTpl("yule-lottery.php"); 
        return true;
    }
}
