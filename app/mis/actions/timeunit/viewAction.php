<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-01-05 14:23:53
 * @version
 */
class viewAction extends Util_Actionbase{


const TIME_PAGE_ID = 1; //timeunit��btx page_id
const TIME_MODULE_ID = 6; //timeunit��btx module_id

const EXIT_SUCESS = 0;

function init(){
	self::setUiAttr('BROWSE_UI');
	if (false === parent::init()){
		if(0 === $this->_intErrorNo){
			$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
			$this->_strErrorMsg = "init return error!";
			Bingo_Log::warning($this->_strErrorMsg);
		}
	}
	return true;
}

public function process(){       

	$task_id = intval(trim(Bingo_Http_Request::getNoXssSafe("taskid", '')));	//��ȡ����ID

	$task_info = array(
		'stime' => 0,
		'etime' => 0,
		'ftime' => 0,
		'type' => 0,
		'addtype' => 0,
		'itle' => '',
		'text' => '',
		'link' => '',
		'icontext' => '',
		'taskid' => $task_id,
		'forumname' => array(),
		
	);

	$arrInput = array( 'taskid' => $task_id );
	$arrOutput = Tieba_Service::call('timeunit', 'getOneForumIDByTaskID', $arrInput, NULL, NULL, 'post', 'json', 'gbk');

	if( self::EXIT_SUCESS === $arrOutput['errno'] ){

		$forum_id = $arrOutput['data'];

		//$Data = Tbapi_Btx_Midl_Btx::getForumStyle($forum_id, self::TIME_PAGE_ID);
//		$arrInput = array(
//				'forum_id' => $forum_id,
//				'page_id' => self::TIME_PAGE_ID,
//		);
//        $arrOutput = Tieba_Service::call('forum', 'getForumStyle', $arrInput ,NULL, NULL, 'post', 'json', 'gbk');
//        if($arrOutput['errno'] !== 0){
//            Bingo_Log::warning('getForumStyle failed. input ['.serialize($arrInput).'] output ['.serialize($arrOutput).']');
//        }
//		foreach( $arrOutput['output'] as $value ){
//			if( self::TIME_MODULE_ID === $value['module_id'] && $task_id == $value['style_id']){
//				$task_info = unserialize($value['style_name']);
//			}
//		}
		$arrInput = array(
				'forum_id' => $forum_id,
				'attr_name' => 'timeunit_p1',
		);
		$arrOutput = Tieba_Service::call('forum', 'getForumAttr', $arrInput ,NULL, NULL, 'post', 'json', 'gbk');
		if($arrOutput['errno'] !== 0){
			Bingo_Log::warning('getForumStyle failed. input ['.serialize($arrInput).'] output ['.serialize($arrOutput).']');
		}
        if( self::TIME_MODULE_ID === intval($arrOutput['output']['timeunit_p1']['module_id']) &&
                $task_id === intval($arrOutput['output']['timeunit_p1']['style_id'])){
            //$task_info = unserialize($arrOutput['output']['timeunit_p1']['style_name']);
//            $task_info = $arrOutput['output']['timeunit_p1']['style_name'];
            $task_info = $arrOutput['output']['timeunit_p1'];
        }

	}

	$arrInput = array( 'taskid' => $task_id );
	$arrOutput = Tieba_Service::call('timeunit', 'getForumNameByTaskID', $arrInput, NULL, NULL, 'post', 'json', 'gbk');
	$task_info['forumname'] = $arrOutput;
	$task_info['taskid'] = $task_id;

	$this->_arrTplVar['errno'] = Tieba_Errcode::ERR_SUCCESS;
	$this->_arrTplVar['errmsg'] = 'success';
	$this->_arrTplVar['taskinfo']['data'] = $task_info;

	//Bingo_Page::getView()->setOnlyDataType("json");
	Bingo_Page::setTpl("timeunitedit.php");///home/<USER>/tieba-odp/template/mis/control/page.php
	return true;

}

}

