<?php
/***************************************************************************
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 **************************************************************************/

/**
 * <AUTHOR>
 * @date   2017/8/6
 **/
class renameLabelAction extends Util_Actionbase
{
	const NO_HOT_CATEGORY_FOUND = 1;
	
	/**
	 * @return bool
	 */
	public function init()
	{
		self::setUiAttr('COMMIT_UI');
		if(false === parent::init()){
			if(0 === $this->_intErrorNo){
				$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = 'init return error!';
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		
		return true;
	}
	
	/**
	 * @return array
	 */
	public function process()
	{
		$forum_id       = intval(Bingo_Http_Request::getNoXssSafe('forum_id', 0));
		$id             = intval(Bingo_Http_Request::getNoXssSafe('id', 0));
		$sub_label_name = strval(Bingo_Http_Request::getNoXssSafe('sub_label_name', ''));
		$strCurUname    = Util_Actionbaseext::getCurUserName();
		$intCurUid      = Util_Actionbaseext::getCurUserId();
		$forum_name     = trim(strval(Bingo_Http_Request::getNoXssSafe('forum_name', '')));
		if(empty($forum_id) && !empty($forum_name)){
			$input    = array(
				"query_words" => array(
					0 => $forum_name //吧名
				),
			);
			$res      = Tieba_Service::call('forum', 'getFidByFname', $input, null, null, 'post', 'php', 'utf-8');
			$forum_id = intval($res['forum_id'][0]['forum_id']);
		}
		if($forum_id <= 0 || empty($sub_tab_id) || empty($sub_label_name)){
			Util_Beyond_Spec_Common::outputJson(Tieba_Errcode::ERR_UNKOWN, '参数错误');
			
			return false;
		}
		$arrServiceInput  = array(
			'forum_id'       => $forum_id,
			'id'             => $id,
			'sub_label_name' => $sub_label_name,
			'op_uname'       => $strCurUname,
		);
		$strServiceName   = "game";
		$strServiceMethod = "updateGameSubLabel";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "gbk");
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			$arrOutput["data"] = array();
			Util_Beyond_Spec_Common::outputJson(Tieba_Errcode::ERR_UNKOWN, Util_Beyond_Spec_Common::ERR_SERVICE_ERROR);
			
			return false;
		}
		Util_Beyond_Spec_Common::outputJson(Tieba_Errcode::ERR_SUCCESS, '', $arrOutput["data"]);
		
		return true;
	}
	
	/**
	 * @return array
	 */
	public function checkParam()
	{
		return true;
	}
}