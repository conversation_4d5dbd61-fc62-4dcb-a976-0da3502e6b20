<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/
/**
 * <AUTHOR>
 * @date 2016/1/24
 * 
 **/
 
class deleteAction extends Util_Actionbase {
    /**
     * @return bool
     */
    public function init() {
        self::setUiAttr('COMMIT_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = 'init return error!';
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * @return bool
     */
    public function process() {
        $arrCheckRes = $this->checkParam();
        if ($arrCheckRes['no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf('param errors %s', serialize($arrCheckRes)));
            Util_Beyond_Spec_Common::output($arrCheckRes);
            return false;
        }
        $arrParam = $arrCheckRes['data'];
        $arrRes = Tieba_Service::call('beyond', 'deleteFeedById', $arrParam, null, null, 'post', 'php', 'gbk');
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning(sprintf('call beyond deleteFeedById failed %s %s'
                , serialize($arrParam), serialize($arrRes)));
            Util_Beyond_Spec_Common::outputJson(Tieba_Errcode::ERR_UNKOWN
                , Util_Beyond_Spec_Common::ERR_SUCCESS_MSG);
            return false;
        }
        Util_Beyond_Spec_Common::outputJson(Tieba_Errcode::ERR_SUCCESS
            , Util_Beyond_Spec_Common::ERR_SUCCESS_MSG);
        return true;
    }

    /**
     * @return array
     */
    public function checkParam() {
        $arrRes = Util_Beyond_Spec_Common::getErrRet(Tieba_Errcode::ERR_SUCCESS);
        $intId = (int)Bingo_Http_Request::getNoXssSafe('id', -1);

        if ($intId < 0) {
            return Util_Beyond_Spec_Common::getErrRet(Tieba_Errcode::ERR_PARAM_ERROR
                , Util_Beyond_Spec_Common::ERR_PARAM_ERROR);
        }
        $arrUserInfo = Util_Actionbaseext::getUserInfo();
        $arrInfo = $this->getInfoArr($intId);
        $arrExt = $arrInfo['feed_ext'];
        $arrExt['opUserName'] = $arrUserInfo['user_name'];
        $arrData = array(
            'id' => $intId,
            'status' => Util_Beyond_Spec_Common::BEYOND_STATUS_DELETE,
            'del_time' => time(),
            'feed_ext' => $arrExt,
            'forum_id' => $arrInfo['forum_id'],
            'thread_id' => $arrInfo['thread_id'],
        );
        $arrRes['data'] = $arrData;
        return $arrRes;
    }

    /**
     * @param $intIsHot
     * @return 
     */
    public function getInfoArr($intId) {
        $strMethod = 'getFeedByFeedId';
        $strExtName = 'feed_ext';
        $arrParam = array(
            'id' => $intId,
        );
        $arrRes = Tieba_Service::call('beyond', $strMethod, $arrParam, null, null, 'post', 'php', 'gbk');
        if ($arrRes === false || $arrRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf('get ext info failed hot:%s id:%s', $intIsHot, $intId));
            return array();
        }
        return $arrRes['data'][0];
    }
}