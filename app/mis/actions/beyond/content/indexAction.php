<?php
/***************************************************************************
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 **************************************************************************/

/**
 * <AUTHOR>
 * @date   2016/1/22
 **/
class indexAction extends Util_Actionbase
{
	/**
	 * @return bool
	 */
	public function init()
	{
		self::setUiAttr('COMMIT_UI');
		if(false === parent::init()){
			if(0 === $this->_intErrorNo){
				$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = 'init return error!';
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		
		return true;
	}
	
	/**
	 * @return bool
	 */
	public function process()
	{
		$arrCheckParam = $this->checkParam();
		if($arrCheckParam['no'] !== Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning(sprintf('param errors %s', serialize($arrCheckParam)));
			Util_Beyond_Spec_Common::output($arrCheckParam);
			
			return false;
		}
		// 判断是否有热点类目
		$intHasHot   = 0;
		$arrGetInput = array(
			'id' => (int)$arrCheckParam['data']['type'],
		);
		$arrGetRes   = Tieba_Service::call('beyond', 'getHotCategoryByCateId', $arrGetInput, null, null, 'post', 'php', 'utf-8');
		if(false === $arrGetRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetRes['errno']){
			Bingo_Log::warning(sprintf('call beyond getHotCategoryByCateId failed %s %s', serialize($arrGetInput), serialize($arrGetRes)));
			Util_Beyond_Spec_Common::outputJson(Tieba_Errcode::ERR_UNKOWN, Util_Beyond_Spec_Common::ERR_SERVICE_ERROR);
			
			return false;
		}
		if(count($arrGetRes['data']) > 0){
			$intHasHot = 1;
		}
		// 获取数据
		$arrParam = $arrCheckParam['data'];
		$arrRes   = Tieba_Service::call('beyond', 'getFeedListByCondation', $arrParam, null, null, 'post', 'php', 'gbk');
		if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
			Bingo_Log::warning(sprintf('call beyond getFeedListByCondation failed %s %s', serialize($arrParam), serialize($arrRes)));
			Util_Beyond_Spec_Common::outputJson(Tieba_Errcode::ERR_UNKOWN, Util_Beyond_Spec_Common::ERR_SERVICE_ERROR);
			
			return false;
		}
		$arrData = $this->transDb2Fe($arrRes['data'], $intHasHot);
		Util_Beyond_Spec_Common::outputJson(Tieba_Errcode::ERR_SUCCESS, 'success', $arrData);
		
		return true;
	}
	
	/**
	 * @return array
	 */
	public function checkParam()
	{
		$arrResult = Util_Beyond_Spec_Common::getErrRet(Tieba_Errcode::ERR_SUCCESS);
		$intType    = (int)Bingo_Http_Request::getNoXssSafe('type', -1);
		$intSubType = (int)Bingo_Http_Request::getNoXssSafe('subType', -1);
		$intStatus  = (int)Bingo_Http_Request::getNoXssSafe('status', -1);
		$strKeyWord = Bingo_Http_Request::getNoXssSafe('keyword', '');
		$intPn      = (int)Bingo_Http_Request::getNoXssSafe('pn', Util_Beyond_Spec_Common::DEFAULT_PN);
		$intSz      = (int)Bingo_Http_Request::getNoXssSafe('sz', Util_Beyond_Spec_Common::DEFAULT_SZ);
		$mixUserId = false;
		$mixUserId = $this->getBaiduUserIdByNick($strKeyWord);
		$arrData = array(
			'type'     => $intType,
			'sub_type' => $intSubType,
			'pn'       => $intPn,
			'sz'       => $intSz,
		);
		if($intSubType === Util_Beyond_Spec_Common::BEYOND_SUBTYPE_FEED){
			$arrData['status'] = $intStatus;
		}else{
			$arrData['status'] = Util_Beyond_Spec_Common::BEYOND_STATUS_PASS;
		}
		if($mixUserId !== false){
			$arrData['create_uid'] = $mixUserId;
		}
		if(!empty($strKeyWord)){
			$arrData['title']    = $strKeyWord;
			$arrData['feed_ext'] = $strKeyWord;
		}
		$arrResult['data'] = $arrData;
		
		return $arrResult;
	}
	
	/**
	 * @param  string $strEmailPrefix
	 *
	 * @return array
	 */
	public function getMisUserIdByEmailPrefix($strEmailPrefix)
	{
		$arrParam = array(
			'email_prefix' => $strEmailPrefix,
		);
		$arrRes   = Tieba_Service::call('Mis', 'getUserInfoByEmailPrefix', $arrParam, null, null, 'post', 'php', 'gbk');
		if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
			Bingo_Log::warning(sprintf('get mis uid failed %s %s', serialize($arrParam), serialize($arrRes)));
			
			return false;
		}
		$mixUserId = false;
		if(isset($arrRes['data']['user_id'])){
			$mixUserId = $arrRes['data']['user_id'];
		}
		
		return $mixUserId;
	}
	
	/**
	 * @param  string $strNickname
	 *
	 * @return array
	 */
	public function getBaiduUserIdByNick($strNickname)
	{
		$arrParam = array(
			'user_name' => array(
				$strNickname,
			),
		);
		$arrParam = Bingo_Encode::convert($arrParam, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
		$arrRes   = Tieba_Service::call('user', 'getUidByUnames', $arrParam, null, null, 'post', 'php', 'utf-8');
		if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
			Bingo_Log::warning(sprintf('get mis uid failed %s %s', serialize($arrParam), serialize($arrRes)));
			
			return false;
		}
		$mixUserId = false;
		if(isset($arrRes['output']['uids'][0]['user_id'])){
			$mixUserId = $arrRes['output']['uids'][0]['user_id'];
		}
		
		return $mixUserId;
	}
	
	/**
	 * @param  array $arrData
	 *
	 * @return array
	 */
	public function transDb2Fe($arrData, $intHasHot)
	{
		$arrList     = array();
		$arrIds      = array();
		$arrTids     = array();
		$arrUids     = array();
		$arrForumIds = array();
		$strStatus = Util_Beyond_Spec_Common::BEYOND_STATUS_AUDIT;
		foreach($arrData['list'] as $value){
			$arrExt  = $value['feed_ext'];
			$arrTemp = array(
				'id'               => $value['id'],
				'title'            => $value['title'],
				'threadId'         => $value['thread_id'],
				'postTime'         => date('Y-m-d', $value['post_time']),
				'createTime'       => date('Y-m-d', $value['create_time']),
				'auditTime'        => date('Y-m-d', $value['audit_time']),
				'opUserName'       => isset($arrExt['opUserName']) ? $arrExt['opUserName'] : '',
				'thumb'            => isset($arrExt['thumb']) ? $arrExt['thumb'] : '',
				'hasImg'           => (int)$value['has_photo'],
				'delTime'          => date('Y-m-d', $value['del_time']),
				'focusTime'        => date('Y-m-d', $value['focus_time']),
				'isRcmd'           => (int)$value['is_rcmd'],
				'readNum'          => isset($arrExt['initPv']) ? (int)$arrExt['initPv'] : 0,
				'replyNum'         => 0,
				'user_type'        => $value['user_type'],
				'create_uid'       => $value['create_uid'],
				'forum_id'         => $value['forum_id'],
				'url'              => isset($arrExt['url']) ? $arrExt['url'] : '',
				'delay_audit_time' => (int)$value['delay_time'],
				'type'             => (int)$value['type'],
				'sub_type'         => (int)$value['sub_type'],
				'is_order'         => (int)$value['is_order'],
			);
			if($value['sub_type'] == Util_Beyond_Spec_Common::BEYOND_SUBTYPE_FOCUS){
				$arrTemp['title'] = $arrExt['title'];
			}
			$strStatus = $value['status'];
			if(isset($value['thread_id']) && $value['thread_id'] != 0){
				$arrTemp['url'] = 'http://tieba.baidu.com/p/'.$value['thread_id'];
			}
			$arrIds[]      = $value['id'];
			$arrTids[]     = $value['thread_id'];
			$arrForumIds[] = $value['forum_id'];
			if($value['user_type'] == Util_Beyond_Spec_Common::BEYOND_USER_TYPE_BAIDU){
				$arrUids[] = $value['create_uid'];
			}
			$arrList[] = $arrTemp;
		}
		if($strStatus != Util_Beyond_Spec_Common::BEYOND_STATUS_AUDIT && $strStatus != Util_Beyond_Spec_Common::BEYOND_STATUS_UNPASS){
			$arrReplyNum = $this->mgetThread($arrTids);
			$arrReadNums = $this->getReadNums($arrIds);
		}
		$arrUnames     = $this->getUnameByUids($arrUids);
		$arrForumNames = $this->getFnameByFid($arrForumIds);
		foreach($arrList as $key => $value){
			if(isset($arrReplyNum[$value['threadId']]['replyNum'])){
				$arrList[$key]['replyNum'] = (int)$arrReplyNum[$value['threadId']]['replyNum'];
			}
			if(($value['user_type'] == Util_Beyond_Spec_Common::BEYOND_USER_TYPE_BAIDU) && isset($arrUnames[$value['create_uid']])){
				$arrList[$key]['userName'] = $arrUnames[$value['create_uid']];
			}
			if(isset($arrForumNames[$value['forum_id']])){
				$arrList[$key]['forumName'] = $arrForumNames[$value['forum_id']];
			}
			if(isset($arrReadNums[$value['id']])){
				$arrList[$key]['readNum'] = $arrReadNums[$value['id']] - $arrList[$key]['readNum'];
			}
		}
		$arrData['canRcmd'] = $intHasHot;
		$arrData['list']    = $arrList;
		
		return $arrData;
	}
	
	/**
	 * @param  $arrIds
	 *
	 * @return
	 */
	public function getReadNums($arrIds)
	{
		if(empty($arrIds)){
			return array();
		}
		$arrResult = array();
		$arrParam  = array(
			'res_ids' => $arrIds,
		);
		$arrRes    = Tieba_Service::call('beyond', 'mgetReadNumByResId', $arrParam, null, null, 'post', 'php', 'utf-8');
		if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
			return $arrResult;
		}
		
		return $arrRes['data'];
	}
	
	/**
	 * @param  array $arrTids
	 *
	 * @return array
	 */
	public function mgetThread($arrTids)
	{
		$arrResult = array();
		$arrParam  = array(
			'thread_ids'      => $arrTids,
			'need_abstract'   => 0,
			'forum_id'        => 0,
			'need_photo_pic'  => 1,
			'need_user_data'  => 0,
			'icon_size'       => 1,
			'need_forum_name' => 0,
		);
		$arrRes    = Tieba_Service::call('post', 'mgetThread', $arrParam, null, null, 'post', 'php', 'gbk');
		if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
			return $arrResult;
		}
		foreach($arrRes['output']['thread_list'] as $value){
			$arrResult[$value['thread_id']] = array(
				'replyNum' => $value['post_num'],
			);
			if(isset($value['media'][0])){
				$arrResult[$value['thread_id']]['thumb'] = $value['media'][0]['small_pic'];
			}
		}
		
		return $arrResult;
	}
	
	/**
	 * @param  array $arrUids
	 *
	 * @return array
	 */
	public function getUnameByUids($arrUids)
	{
		$arrResult = array();
		$arrParam  = array(
			'user_id' => $arrUids,
		);
		$arrRes    = Tieba_Service::call('user', 'getUnameByUids', $arrParam, null, null, 'post', 'php', 'gbk');
		if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
			return $arrResult;
		}
		foreach($arrRes['output']['unames'] as $value){
			$arrResult[$value['user_id']] = $value['user_name'];
		}
		
		return $arrResult;
	}
	
	/**
	 * @param  array $arrForumIds
	 *
	 * @return array
	 */
	public function getFnameByFid($arrForumIds)
	{
		$arrResult = array();
		$arrParam  = array(
			'forum_id' => $arrForumIds,
		);
		$arrRes    = Tieba_Service::call('forum', 'getFnameByFid', $arrParam, null, null, 'post', 'php', 'gbk');
		if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
			return $arrResult;
		}
		foreach($arrRes['forum_name'] as $value){
			$arrResult[$value['forum_id']] = $value['forum_name'];
		}
		
		return $arrResult;
	}
	
}

