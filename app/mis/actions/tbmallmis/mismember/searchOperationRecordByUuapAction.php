<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file searchOperationRecordByUuapAction.php
 * <AUTHOR>
 * @date 2016/10/20 17:39:18
 * @brief 
 *  
 **/

class searchOperationRecordByUuapAction extends Util_Actionbase{

    private $_arrUserInfo = array();
    /**
     * *      * [init description]
     * *           * @return [type] [description]
     * *                */
    function init(){
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        //check user
        $this->_arrUserInfo = Util_Actionbaseext::getUserInfo();
        if (empty($this->_arrUserInfo)) {
                $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = 'check user error!';
                Bingo_Log::warning($this->_strErrorMsg);
                return false;
        }
        return true;
    }
        

    /**
     * 主处理函数
     * @return [type] [description]
     **/
    public function process(){
        try {
            $member_type = intval(Bingo_Http_Request::getNoXssSafe("member_type", 0));
            $pn = intval(Bingo_Http_Request::getNoXssSafe("pn", 1));
            $ps = intval(Bingo_Http_Request::getNoXssSafe("ps", 20));
            //$uuap_name = $this->_arrUserInfo['user_name'];
            if(/*empty($uuap_name) ||*/ $pn <= 0 || $ps <= 0 ){ 
                Bingo_Log::warning("uuap_name[$uuap_name] is invalid. "); 
                $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
                return;
            }   
            $arrParams = array(
                //"uuap_name" => $uuap_name,
                'pn' => $pn,
                'ps' => $ps, 
                "member_type" => $member_type,
            );  
            $arrRet = Tieba_Service::call("member","getOperationRecordByUuap",$arrParams);
            if($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                throw new Exception($arrRet['errmsg'],$arrRet['errno']);
            }   

            $this->_jsonRet(0, 'success', $arrRet['data']);
        }catch(Exception $e){
            Bingo_Log::warning( "errno=" . $e->getCode() . " msg=".$e->getMessage());
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), $e->getMessage()); 
        }   
    }   


    protected function _jsonRet($errno, $errmsg="", array $arrExtData = array()){
        $arrRet = array(
            'no'=>intval($errno),
            'error'=>empty($errmsg) ? Tieba_Error::getErrmsg($errno) : $errmsg,
            'data'=>$arrExtData,
        );
        Bingo_Log::pushNotice("errno",$errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet);
    }

}










/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
