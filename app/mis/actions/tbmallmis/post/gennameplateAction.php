<?php
/**
 * Created by PhpStorm.
 * User: xuke02
 * Date: 13-12-18
 * Time: 下午2:25
 */
define('BINGO_ENCODE_LANG', 'UTF-8');

class gennameplateAction extends Util_Actionbase {

    const TBMALL_ADD_NAMEPLATE = 'tbmall_add_nameplate';
    const MULTI_KEY_PREFIX = 'nameplate_';

//    public static $filter = array();


    private function _mbStringToArray($string){
        $strlen = mb_strlen($string);
        while ($strlen) {
            $array[] = mb_substr($string,0,1,"UTF-8");
            $string = mb_substr($string,1,$strlen,"UTF-8");
            $strlen = mb_strlen($string);
        }
        return $array;
    }

    private function _mbStringReverse($string){
        $array = self::_mbStringToArray($string);
        $array = array_reverse($array);
        $string = '';
        foreach($array as $char){
            $string .= $char;
        }
        return $string;
    }

    private function _addCharTypeToPatternArray($mixData, $intNum, $intType){
        $arrOutput = $mixData;
        for($i = 0; $i < $intNum; ++$i){
            $arrOutput[] = $intType;
        }
        return $arrOutput;
    }

    private function _genPattern($strSubject, $strAuxiliaryWord, $strUnitWord){
        $arrPattern = array();
        $arrPattern = self::_addCharTypeToPatternArray($arrPattern, count(self::_mbStringToArray($strSubject)), 1);
        $arrPattern = self::_addCharTypeToPatternArray($arrPattern, count(self::_mbStringToArray($strAuxiliaryWord)), 2);
        $arrPattern = self::_addCharTypeToPatternArray($arrPattern, count(self::_mbStringToArray($strUnitWord)), 3);
        return $arrPattern;
    }


//    public static function _selfFilter($_v){
//        $arrRepeatNameplate = $this->filter;
//        if(in_array($_v['title'], $arrRepeatNameplate)){
//            return true;
//        }
//        return false;
//    }


    public function init(){
//        $this->_arrTitle = Bingo_Http_Request::getNoXssSafe('nameplate', 0);
        //$this->_arrTitle = Bingo_Encode::convert($this->_arrTitle, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        return true;
    }

    public function execute(){
        try {
            $strSubject = trim(Bingo_Http_Request::getNoXssSafe('subject', ''));
            $boolIsPrefix= Bingo_Http_Request::getNoXssSafe('is_prefix', 1);
            $arrOptionalWord = Bingo_Http_Request::getNoXssSafe('optional_word', '');
            $arrUnitWord = Bingo_Http_Request::getNoXssSafe('unit_word', '');
            $strFirstDir = trim(Bingo_Http_Request::getNoXssSafe('first_dir', ''));
            $strSecondDir = trim(Bingo_Http_Request::getNoXssSafe('second_dir', ''));
            $intNum = intval(Bingo_Http_Request::getNoXssSafe('num', 0));

            //输入参数 GBK to UTF-8
            $strSubject = Bingo_Encode::convert($strSubject, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
            $strFirstDir = Bingo_Encode::convert($strFirstDir, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
            $strSecondDir = Bingo_Encode::convert($strSecondDir, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
            $arrUnitWord = Bingo_Encode::convert($arrUnitWord, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
            $arrOptionalWord = Bingo_Encode::convert($arrOptionalWord, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);

            if($intNum <= 0){
                throw new Exception("input can't less than 0!", Tieba_Errcode::ERR_PARAM_ERROR);
            }

            if(!$boolIsPrefix){//subject为后缀
                $strSubject = self::_mbStringReverse($strSubject);
                foreach($arrUnitWord  as $_k => $strUnitWord){
                    $arrUnitWord[$_k] = self::_mbStringReverse($strUnitWord);
                }
            }

            $arrTmp = array(
                'title' => '',
                'optional_word' => array(),
                'pattern' => array(),
                'first_dir' => '',
                'second_dir' => '',
            );
            $arrOptionalWord = ('' === $arrOptionalWord) ? array() : $arrOptionalWord;
            $strAuxiliaryWord = empty($arrOptionalWord[0]) ? '' : $arrOptionalWord[0];
            $arrNamplate = array();
            foreach($arrUnitWord as $strUnitWord){
                $strTitle = $strSubject . $strAuxiliaryWord . $strUnitWord;
                if( count(self::_mbStringToArray($strTitle)) > 6 ){
                    continue;
                }
                $arrTmp = array(
                    'title' => $strTitle,
                    'optional_word' => $arrOptionalWord,
                    'pattern' => self::_genPattern($strSubject, $strAuxiliaryWord, $strUnitWord),
                    'first_dir' => $strFirstDir,
                    'second_dir' => $strSecondDir,
                );
                $arrNamplate[] = $arrTmp;
            }

            if(!$boolIsPrefix){//subject为后缀
                foreach($arrNamplate as $_k => $arrTmp){
                    $arrTmp['title'] = self::_mbStringReverse($arrTmp['title']);
                    $arrTmp['pattern'] = array_reverse($arrTmp['pattern']);
                    $arrNamplate[$_k] = $arrTmp;
                }
            }

            $arrTitle = array();
            foreach($arrNamplate as $_v){
                $arrTitle[] = $_v['title'];
            }
            $arrInput = array(
                'title' => $arrTitle,
            );
            $arrOutput = Tieba_Service::call('tbmall', 'getUniqueNameplate', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
            if(!isset($arrOutput['errno']) || Tieba_Errcode::ERR_SUCCESS !== intval($arrOutput['errno'])){//中间某个出现失败
                throw new Exception("service call fail!", Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $arrRepeatNameplate = $arrOutput['data'];
//            $this->filter = $arrRepeatNameplate;
//            $arrNamplate = array_filter($arrNamplate, "self::_selfFilter");
            $data = array();
            foreach($arrNamplate as $_v){
                if(in_array($_v['title'], $arrRepeatNameplate)){
                    continue;
                }
                $data[] = $_v;
                if(--$intNum <= 0){
                    break;
                }
            }

            $data = Bingo_Encode::convert($data, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);

            $errno = Tieba_Errcode::ERR_SUCCESS;
            Bingo_Log::pushNotice("no", $errno);
            $this->_jsonRet($errno, 'successed', $data);

        }catch(Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            //数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
            $this->_jsonRet($e->getCode(), 'unknow error');
        }
    }

    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array()){
        $arrRet = array(
            'no'=>intval($errno),
            'error'=>strval($errmsg),
            'data'=>$arrExtData,
        );
        Bingo_Log::pushNotice("errno",$errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet);
    }
}