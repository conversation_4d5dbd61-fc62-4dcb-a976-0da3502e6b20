<?php
/**
 * <AUTHOR>
 * @copyright 
 */
class adListAction extends Util_Actionbase
{
	
	static public $errno =0;
	static public $errmg = '';
	static public $data = array();
	static public $arrInput = array();
	
	const game_pic_split = "_split_";
	const app_name = 'tb_wordlist_redis';
	const work_list = 'game_center_ad';
	const token_tb_wordlist = 'tb_wordlist_redissmU4r8Wwcx';
	const default_rn_no = 10;  //Ĭ�Ϸ����������
	
	const service_wordlist_false_errno = 310004;     //service wordlist return false
	const service_wordlist_queryWLItem_error = 310005; //service wordlist->queryWLItem errno!=0
	
	static public $have_next = false;
	static public $total_count = 0;
	
    /**
     * ��ʼ������
     *
     *��Ҫ�ǵ��û����init����
     *
     *@return  boolean $result
     *<AUTHOR>
     */
	function init()
	{
        self::setUiAttr('COMMIT_UI');
        if ( false === parent::init() )
        {
            if ( 0 === $this->_intErrorNo )
            {
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = 'init return error!';
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        $result = true;
        return $result;
    }
    	
    /**
     * ��Ҫ���?��������ô˺���
     * 
     * ��Ҫ����service
     * 
     * <AUTHOR>
     * @return 
     * 
     */
    public function process()
    {
    	$is_pass = $this->getParam();
    	if(false === $is_pass){
    		$this->finish();
    		return false;
    	}
    	$res = $this->getAdList();
    	if(false === $res){
    		$this->finish();
    		return false;
    	}
    	
    	$have_next = false; 
    	if(!empty($res) && count($res)>self::$arrInput['page_size']){
    		$res = array_pop($res);
    		$have_next = true;
    	}
    	
    	$list = array();
    	foreach($res as $value ){
    		$list[$value['sort']] = $value;
    	}
    	ksort($list);
        $arrReturn = array();
    	foreach($list as $key=>$value){
    		$arrReturn[] = $value;
    	}
    	$arrData['list']=$arrReturn;
    	$arrData['page_no'] = self::$arrInput['page_no'];
    	$arrData['page_size'] = self::$arrInput['page_size'];
    	$arrData['total_count'] = self::$total_count;
    	$arrData['have_next'] = $have_next;
    	
    	$this->setErroInfo(Tieba_Errcode::ERR_SUCCESS,'success',$arrData);
    	$this->finish();
    	return true;
    }

    public function getParam(){
    	self::$arrInput['page_no'] = intval(Bingo_Http_Request::getNoXssSafe('page_no',''));
    	self::$arrInput['page_size'] = intval(Bingo_Http_Request::getNoXssSafe('page_size',0));
    	
    	if(self::$arrInput['page_no'] <= 0){
    		self::$arrInput['page_no'] = 1;
    	}
    	if(self::$arrInput['page_size'] <= 0){
    		self::$arrInput['page_size'] = 10;
    	}
    	return true;
    }
    
    public function getAdList(){
    	$start = self::$arrInput['page_size']*(self::$arrInput['page_no']-1);
        $arrResInput = array(
	        'memo'                => 'add new data',
	        'app_name'            => self::app_name,
	        'wordlist_name'       => self::work_list,
            'start'               => $start,
            'offset'              => self::$arrInput['page_size']+1,
        );
    	$arrData = Tieba_Service::call('wordlist','queryWLItem',$arrResInput);
    	if(false === $arrData){
    		$str = "service wordlist false input[".serialize($arrResInput)."]";
    		$this->setErroInfo(self::service_wordlist_false_errno,$str,array(), __LINE__);
    		return false;
    	}elseif($arrData['errno'] !=  Tieba_Errcode::ERR_SUCCESS){
    		$str = "service wordlist queryWLItem call return errno not zero  input[".serialize($arrResInput)."] output[".serialize($arrData)."]";
    		$this->setErroInfo(self::service_wordlist_queryWLItem_error,$str,array(), __LINE__);
    		return false;
    	}
    	
        $arrGameList = array();
    	if(isset($arrData['ret']) && !empty($arrData['ret']) ){
    		$arrList = $arrData['ret'];
    		foreach($arrList as $key =>$value){
    			$temp = array();
    			if('' != $value['value']){
    				$temp  = unserialize($value['value']);
    			}
    			if(!empty($temp)){
    				//$temp['game_pic'] = $this->getArrayGamepicByStr($temp['game_pic']);
    				$arrGameList[] = $temp;
    			}
    			
    		}
    	}
    	self::$total_count = count($arrGameList);
    	$arrGameList = $this->changeEncode($arrGameList);
    	return $arrGameList;
    }
    public function createGameId(){
    	$time = time();
    	$game_id = $time.rand(1,100);
    	return $game_id;
    }
    /**
     * ��ӡ��־����
     * <AUTHOR>
     * @return void
     */
    public function printLog()
    {
    	if(self::$errno !=0)
    	{
    		Bingo_Log::warning(self::$errmg);
    		return;
    	}else
    	{
    	    Bingo_Log::notice(self::$errmg);
    	}
    }
    /**
     * 	�������ֵ
     * 
     */
    public function finish()
    {
    	$this->printLog();
    	
    	$is_json = intval(Bingo_Http_Request::getNoXssSafe('is_json',0));
    	if($is_json){
    		Bingo_Page::assign('no',   self::$errno);
	        Bingo_Page::assign('error', self::$errmg);
	        Bingo_Page::assign('data',  self::$data);
	        Bingo_Page::setOnlyDataType("json");
	        return true;
    	}
    	$this->_arrTplVar['errno'] = self::$errno;
        $this->_arrTplVar['errmsg'] = self::$errmg;
        $this->_arrTplVar['data'] = self::$data;
        Bingo_Page::setTpl("platforum.php");
        return true;
    }
    /**
     * 
     * �������ú���
     * 
     * ���õ�ֵ$errno��$errmg��$data ��󷵻�
     * 
     * @param int $errno
     * @param int $errmsg
     * @param array $data
     * @return void
     * <AUTHOR>
     */
    public function setErroInfo($errno=-1,$errmsg='',$data=array(),$line_no=0)
    {
    	if($line_no != 0){
    		$errmsg = $errmsg.' lineno['.$line_no.']';
    	}
    	self::$errno  = $errno;
    	self::$errmg  = $errmsg;
    	self::$data   = $data;
    	return;
    }
public function changeEncode($input){
    	if(is_array($input)){
    		foreach($input as $key => $value ){
    			$input[$key] = $this->changeEncode($value);
    		}
    	}else{
    		$input = Bingo_Encode::convert($input, Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
    	}
    	return $input;
    }
}
?>