<?php
class gameRecommendListAction extends Bingo_Action_Abstract{
	
	private function _errSuccess($data){
		$errno = Tieba_Errcode::ERR_SUCCESS;
		$result = array(
		          'no'    => $errno,
		          'error' => Tieba_Error::getErrmsg($errno),
		          'data'  => $data,
		);
		return Bingo_String::array2json($result,'gbk');
	}
	
	private function _errFail($errno){
		$result = array(
		          'no'    => $errno,
		          'error' => Tieba_Error::getErrmsg($errno),
		);
		return Bingo_String::array2json($result,'gbk');
	}
	
	public function execute(){
       $platform = Bingo_Http_Request::get('game_platform',0);
	   $page_num = intval(Bingo_Http_Request::get('page_no',0));
	   $isClient = intval(Bingo_Http_Request::get('isClient',0));
	   
	   if(empty($isClient)){
	      $isClient = 0;
	   }

	   //参数校验
	   if(empty($platform) || intval($platform)<1 || intval($platform>2) || $isClient<0 || $isClient>1){
          Bingo_Log::warning('invalid params');
		  echo self::_errFail(Tieba_Errcode::ERR_PARAM_ERROR);
		  return false;
	   }

	   $arrInput = array(
		           'game_platform'=>$platform,
				   'page_no'=>$page_num,
	               'isClient' => $isClient,
	              );
	   $arrOut = Tieba_Service::call('gamecenter','recommendList',$arrInput,NULL,NULL,'post','php','gbk');
	   if(!isset($arrOut['errno'])||Tieba_Errcode::ERR_SUCCESS!=$arrOut['errno']){
           echo self::_errFail(Tieba_Errcode::ERR_UNKOWN);
		   return false;
	   }
       echo self::_errSuccess($arrOut);
	 //  echo self::_errSuccess($this->changeEncode($arrOut));
	   return true;
	}

    public function changeEncode($input){
    	if(is_array($input)){
    		foreach($input as $key => $value ){
    			$input[$key] = $this->changeEncode($value);
    		}
    	}else{
    		$input = Bingo_Encode::convert($input, Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
    	}
    	return $input;
    }


}
