<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-27 18:33:16
 * @version
 */
class indexAction extends Util_Actionbase{

	function init() {
            self::setUiAttr('BROWSE_UI');
            if (false === parent::init()) {
                if (0 === $this->_intErrorNo) {
                    $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                    $this->_strErrorMsg = 'init() error!';
                    Bingo_Log::warning($this->_strErrorMsg);
                }
            }
            return true;
	}
	public function process() {
		//get param
		$modName = trim(Bingo_Http_Request::getRouterParam('mod_name',''));
		$tplName = trim(Bingo_Http_Request::getRouterParam('tpl_name',''));
		Bingo_Log::debug('mod_name: '.$modName.' tpl_name: '.$tplName);
	    if($modName === '' || $tplName === ''){
	    	header('location:http://imis.tieba.baidu.com');
	    	return false;
	    }
	    Bingo_Page::setTpl($tplName.'.php');
	}
}
?>