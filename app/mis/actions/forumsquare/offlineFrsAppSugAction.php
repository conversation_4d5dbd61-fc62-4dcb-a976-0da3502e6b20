<?php
class offlineFrsAppSugAction extends Util_Actionbase {
	function init() {
		self::setUiAttr ( 'BROWSE_UI' );
		if (false === parent::init ()) {
			if (0 == $this->_intErrorNo) {
				$this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = 'init return error';
				Bingo_Log::warning ( $this->_strErrorMsg );
			}
		}
		return true;
	}
	private function _jsonRet() {
		$arrRet = array (
				'errno' => intval ( $this->_intErrorNo ), 
				'errmsg' => strval ( $this->_strErrorMsg ) );
		Bingo_Page::assign ( $arrRet );
		Bingo_Page::setOnlyDataType ( "json" );
		Bingo_Http_Response::contextType ( 'application/json' );
	}
	public function process() {
		$this->_intErrorNo = 0;
		$this->_strErrorMsg = "offline success";
		
		$arrInput ['app_id'] = Bingo_Http_Request::get ( 'app_id' );
		
		$arrInput = Bingo_Encode::convertGet ( $arrInput, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8 );
		
		$errorParamsNames = array ();
		$checkParamsFlag = true;
		
		if (empty ( $arrInput ['app_id'] )) {
			$checkParamsFlag = false;
			$errorParamsNames [] = 'app_id';
		}
		
		if ($checkParamsFlag === false) {
			$this->_intErrorNo = 1;
			$this->_strErrorMsg = 'input params error:[' . implode ( ',', $errorParamsNames ) . ']';
			$this->_jsonRet ();
			return false;
		}
		
		$arrInput ['record_status'] = 4;
		
		$input = array_merge ( $arrInput, array (
				'op_user' => Util_Actionbaseext::getCurUserName (), 
				'op_time' => time () ) );
		
		$res = Tieba_Service::call ( 'modata', 'changeFrsAppSugStatus', $input );
		if ($res ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			$this->_intErrorNo = 1;
			$this->_strErrorMsg = "change status failed, errmsg = " . $res ['errmsg'];
			$this->_jsonRet ();
			return false;
		}
		$this->_jsonRet ();
		return true;
	}
}
