<?php
class insertAction extends Util_UIAbstract {

    protected $_arrRequest = array('forum_list_recommend' => array('format' => 'j'));
    protected $_strService = 'modata';
    protected $_strMethod = 'insertForumListRecommend';

    function _init()
    {
        self::setUiAttr('COMMIT_UI');
        return parent::_init();
    }


	public function execute()
    {
        $res = parent::execute();
        echo Bingo_String::array2json($res);
        return true;
	}
}
