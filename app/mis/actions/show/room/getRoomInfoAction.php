<?php
/**
 * Author: pan<PERSON><PERSON><PERSON><PERSON>
 * Date: 14-8-8
 * Time: 2014-08-08
 * Desc: show列表
 */
class getRoomInfoAction extends Util_Actionbase {
	public function init() {
		self::setUiAttr ( 'BROWSE_UI' );
		parent::init ();
		return true;
	}
	public function process() {
		$room_id = intval ( Bingo_Http_Request::getNoXssSafe( 'room_id' ) );
		//$room_id = 10120295;
		$arrInput = array('room_id' => $room_id);
		$arrOut = Tieba_Service::call('show','getRoomInfo',$arrInput);
		return Util_Show_Tools::_jsonRet($arrOut ['errno'],$arrOut ['errmsg'],array('list' => $arrOut ['data']));
	}
	
}
