<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-01-17 13:35:49
 * @comment 词条列表，词条检索;  json接口;
 * @version
 */
class tablelistAction extends Util_Actionbase {

	private static $_allowSearchBy = array(
										   'wordlist_name' 			=> true,
										   'op_username'	=> true,
										   'memo'			=> true,
										   );

	function init(){
		self::setUiAttr('BROWSE_UI');
		if (false === parent::init()){
			if(0 === $this->_intErrorNo){
				$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}

	public function process(){       
		//参数获取
		$app_name 		= strval(Bingo_Http_Request::getNoXssSafe('app_name', Actions_Tbwordlist_Lib_Define::APP_NAME_TB_WORDLIST));
		$dept 			= strval(Bingo_Http_Request::getNoXssSafe('dept',Actions_Tbwordlist_Lib_Define::DEPT));
		$page_num 		= strval(Bingo_Http_Request::getNoXssSafe('page_num',''));
		$res_num 		= strval(Bingo_Http_Request::getNoXssSafe('res_num',Actions_Tbwordlist_Lib_Define::RES_NUM));
		$search_by 		= strval(Bingo_Http_Request::getNoXssSafe('search_by',''));
		$search_value 	= strval(Bingo_Http_Request::getNoXssSafe('search_value',''));
		if(empty($page_num)){
			$page_num = 1;
		}


		//参数转换
		$arrPara = array(
						 'app_name'			=> $app_name,
						 'dept'				=> $dept,
						 'page_num'			=> $page_num,
						 'res_num'			=> $res_num,
						 'search_by'		=> $search_by,
						 'search_value'		=> $search_value,
						);
		
		$arrData = array();

		$arrAppInfo = Actions_Tbwordlist_Lib_Define::getAppInfo($arrPara['app_name'],$arrPara['dept']);
		if(false == $arrAppInfo){//获取失败
			$arrData['app_info'] = array();
		}else{
			$arrData['app_info'] = Actions_Tbwordlist_Lib_Define::appInfoToTpl($arrAppInfo);
		}


		$arrRet = self::_getList($arrPara);
		if(false == $arrRet){//获取失败
			$arrData['list'] = array();
		}else{
			$strStoreType = $arrData['app_info']['store_type_str'];
			$arrRet = Actions_Tbwordlist_Lib_Define::wordlistInfoconvertToTpl($arrRet,$strStoreType);
			$arrData['list'] = $arrRet;
		}

		$arrData['page'] = array(
							  'page_num'	=> $arrPara['page_num'],
							  );


		$this->_arrTplVar['errno'] = Tieba_Errcode::ERR_SUCCESS;
		$this->_arrTplVar['errmsg'] = 'success';
		$this->_arrTplVar['data'] = $arrData;


		Bingo_Page::getView()->setOnlyDataType("json");
		Bingo_Page::setTpl("simpledata/Page.php");///home/<USER>/tieba-odp/template/mis/control/simpledata/Page.php
		return true;

	}




	private static function _getList($arrPara){
		extract($arrPara);

		$intStart = ($page_num - 1)*$res_num;
		if($intStart <= 0){
			$intStart = 0;
		}
		$intOffset = $res_num;

		$arrInputPara = array(
							  'app_name'  => $app_name,
							  'dept'      => $dept,
							 );
		if(strlen($search_by)>0 && strlen($search_value)>0 ){

			if('wordlist_name' == $search_by){
				$search_value =  Actions_Tbwordlist_Lib_Define::getWordlistNameForPara($app_name,$search_value);
			}
			if(true == self::$_allowSearchBy[$search_by]){
				$arrInputPara[$search_by] = $search_value;
			}
		}

		$arrInput = array(
						  'query_info'    => $arrInputPara,
						  'start'     => $intStart,
						  'offset'    => $intOffset,
						 );
		$arrOutput =  Tieba_Service::call("wordlist","queryWL",$arrInput);

		if (false === $arrOutput ) {
			//Bingo_Log::warning(sprintf('Failed to call servicename:[%s] method:[%s][user_name:%s]',	$strServiceName,$strMethod, serialize($arrInput) ));
			Bingo_Log::fatal('Tieba_Service_call_wordlist_'.$strMethod.'_error_'.serialize($arrInput));
			return false;
		}
		//check err_no
		if ( isset($arrOutput['errno']) && (0 == intval($arrOutput['errno'])) ) {
			//success nothing to do
			$arrRet = $arrOutput;
			return $arrRet['ret'];
		} else {
			//failure,print log
			//Bingo_Log::warning(sprintf('Err to call servicename:[%s] method:[%s] [input:%s] [output:%s]',$strServiceName,$strMethod,serialize($arrInput),serialize($arrOutput)));
			Bingo_Log::fatal('Tieba_Service_call_wordlist_'.$strMethod.'_error_'.serialize($arrOutput).'_'.serialize($arrInput));
			return false;
		}

	}

}
?>
