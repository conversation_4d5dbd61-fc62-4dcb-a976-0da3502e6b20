<?php
/**
 * 语料模型自助上线，进入调试状态
 * <AUTHOR> 
 * @date 2013-11-20
 */
class debugAction extends Util_Actionbase {
	function init() {
		self::setUiAttr('BROWSE_UI');
		if(false == parent::init()){
			if(0 === $this->_intErrorNo){
				$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = Tieba_Errcode::$codes[$this->_intErrorNo];
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}
	public function process() {
		$modelType = (int)Bingo_Http_Request::get('model_type');
		$ret = Service_Sample_Sample::getSampleStep(array('model_type' => $modelType));
		if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			$this->_intErrorNo  = $ret['errno'];
			$this->_strErrorMsg = $ret['errmsg'];
			return $this->_pageRet();
		}
		$step = $ret['ret'][0];
		if(intval($step['status']) !== Util_Sample_Const::SAMP_STATUS_PM_EVALUATE){
			$this->_intErrorNo  = Tieba_Errcode::ERR_UNKOWN;
			$this->_strErrorMsg = 'can not debug now, the status is invalid!';
		}
		
		
		$arrInput = array(
				'row'     => array('status' => Util_Sample_Const::SAMP_STATUS_DEBUG),
				'conds'   => array(
						'status'     => array(Util_Sample_Const::SAMP_STATUS_PM_EVALUATE),
						'model_type' => $modelType,
				),	
		);
		$ret = Service_Sample_Sample::updateSampleStep($arrInput);
		if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			$this->_intErrorNo  = $ret['errno'];
			$this->_strErrorMsg = $ret['errmsg'];
		}
		
		
		
		$data['status'] = Util_Sample_Const::SAMP_STATUS_DEBUG;
		return $this->_pageRet();
	}
	private function _pageRet(){
		$this->_arrTplVar['errno']  = $this->_intErrorNo;
		$this->_arrTplVar['errmsg'] = $this->_strErrorMsg;
		Bingo_Page::setOnlyDataType ( "json" );
		return true;
	}
}
?>
