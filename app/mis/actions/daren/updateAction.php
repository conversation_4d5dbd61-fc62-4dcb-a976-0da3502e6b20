<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-07-11 16:19:19
 * @version
 */
class updateAction extends Util_Actionbase {

	private $uid;
	private $uname;
	private $dir_name;
	private $fname;
	private $fdesc;
	private $true_uname;
	private $qq;
	private $phone;
	private $email;
	private $daren_desc;
	function init(){
		self::setUiAttr('COMMIT_UI');
		if (false === parent::init()){
			if(0 === $this->_intErrorNo){
				$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}
		
    public function execute(){
        $uid = (int)Bingo_Http_Request::getNoXssSafe("uid",0);
        $uname = trim(Bingo_Http_Request::getNoXssSafe('uname',''));
        $dir_name = trim(Bingo_Http_Request::getNoXssSafe('dir_name',''));
        $fname = trim(Bingo_Http_Request::getNoXssSafe('fname',''));
        $fdesc = trim(Bingo_Http_Request::getNoXssSafe('fdesc',''));
        $true_uname = trim(Bingo_Http_Request::getNoXssSafe('true_uname',''));
        $qq = trim(Bingo_Http_Request::getNoXssSafe('qq',''));
        $phone = trim(Bingo_Http_Request::getNoXssSafe('phone',''));
        $email = trim(Bingo_Http_Request::getNoXssSafe('email',''));
        $daren_desc = trim(Bingo_Http_Request::getNoXssSafe('daren_desc',''));
        
        // ��У��
        if($uid <= 0 || empty($uname) || empty($dir_name)){
        	$arrOutput['errno'] = '1';
        	$arrOutput['error'] = '$uname or $dir_name is empty';
        	Bingo_Log::warning('$uname or $dir_name is empty');
        	echo Bingo_String::array2json($arrOutput);
        	return false;
        }
        
        $darenInfo = array (
        		'uid' => $uid,
        		'uname' => $uname,
        		'dir_name' => $dir_name,
        		'fname' => $fname,
        		'fdesc' => $fdesc,
        		'true_uname' => $true_uname,
        		'qq' => $qq,
        		'phone' => $phone,
        		'email' => $email,
        		'daren_desc' => $daren_desc
        		 );
        $arrInput['darenInfo'] = $darenInfo;
        $commitRet = self::$MisService->call('daren', 'updateDaren', $arrInput);
        
        if ( $commitRet == false || !isset($commitRet['errno']) || $commitRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
        	return false;
        }else{
            Bingo_Page::assign('uid',$uid);
            Bingo_Page::assign('uname',$uname);
        }
        Bingo_Page::assign('errno',$commitRet['errno']);
        Bingo_Page::assign('errmsg',$commitRet['errmsg']);

        Bingo_Page::setOnlyDataType("json");
        return true;
    }
}
?>