<?php
/**
 * Author: ji<PERSON><PERSON><PERSON>
 * Date: 13-9-30
 * Time: 2013-09-30
 * Desc: ��Ӹ�����Ϣ
 */

class addWelfareAction extends  Util_Actionbase{
    private $_intForumId = 0;
    private $_intType = 0;
    private $_strName = '';
    private $_strTitle = '';
    private $_strCoverPic = '';
    private $_strSummary = '';
    private $_arrPicList = array();
    private $_intDefaultPic = 1;
    const SEPARATE_SYMBOL = ',';//�и��ַ���
    public function init(){
        self::setUiAttr('COMMIT_UI');
        parent::init();
        return true;
    }
    public function process(){
        if($this->_check()){
            $this->_process();
        }
        $this->_build();
    }
    private function _check(){
        $this->_intForumId = intval(Bingo_Http_Request::get('forum_id',0));
        $this->_strTitle = Bingo_Http_Request::get('title','');
        $this->_strCoverPic = Bingo_Http_Request::get('cover_pic','');
        $this->_strSummary = Bingo_Http_Request::get('summary','');
        $this->_arrPicList = $this->_getPicList();
        $this->_strName = Bingo_Http_Request::get('name','');
        $this->_intDefaultPic = Bingo_Http_Request::get('default_pic_num',1);
        $this->_intType = intval(Bingo_Http_Request::get('type',0));
        if($this->_strTitle == ''||$this->_strCoverPic == ''||$this->_strSummary == ''
            ||empty($this->_arrPicList) || $this->_intType <=0 || $this->_strName == ''){
            Bingo_Log::warning('request param error!');
            $this->_intErrorNo = Tieba_Errcode::ERR_PARAM_ERROR;
            $this->_strErrorMsg = Tieba_Error::getErrmsg($this->_intErrorNo);
            return false;
        }
        return true;
    }
    private function _getPicList(){
        $strPicList = Bingo_Http_Request::get('pic_list','');
        if($strPicList == ''){
            return array();
        }
        $arrPicList = explode(self::SEPARATE_SYMBOL,$strPicList);
        return $arrPicList;
    }
    private function _process(){
        $arrInput = array(
            'forum_id' => $this->_intForumId,
            'type' => $this->_intType,
            'name' => $this->_strName,
            'title' => $this->_strTitle,
            'summary' => $this->_strSummary,
            'cover_pic' => $this->_strCoverPic,
            'default_pic_num' => $this->_intDefaultPic,
            'pic_list' => $this->_arrPicList,
        );
        $arrOut = Tieba_Service::call('fstar','addClubWelfare',$arrInput);
        if($arrOut === false){
            $this->_intErrorNo = Tieba_Errcode::ERR_UNKOWN;
            $this->_strErrorMsg = Tieba_Error::getErrmsg($this->_intErrorNo);
        }else{
            if($arrOut['errno'] === Tieba_Errcode::ERR_SUCCESS){
                return true;
            }else{
                $this->_intErrorNo = $arrOut['errno'];
                $this->_strErrorMsg = $arrOut['errmsg'];
            }
        }
        return false;
    }

    private function _build(){
        $arrData = array(
            'errno' => $this->_intErrorNo,
            'error' => $this->_strErrorMsg,
        );
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrData);
    }
}