<?php
/**
 * Author: ji<PERSON><PERSON><PERSON>
 * Date: 14-3-4
 * Desc: �鿴�޸���ʷ
 */

class updateHistoryAction extends Util_Actionbase{
    private $_arrData = array();
    public function init(){
        self::setUiAttr('BROWSE_UI');
        parent::init();
        return true;
    }
    public function process(){
        if($this->_check()){
            $this->_process();
        }
        $this->_build();
    }
    private function _check(){
        return true;
    }
    private function _process(){
		$arrInput = array(
            'limit' => 400,
            'pn' => 1
        );
		$arrOut = self::$MisService->call('upFname','getUpFnameHistory',$arrInput);
		if($arrOut == false || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning('get updaet histroy fail');
			return false;
		}
		$this->_arrData = $arrOut['data']['history_list'];
        return true;
    }

    private function _build(){
        Bingo_Page::assign('history_list',$this->_arrData);
        Bingo_Page::setTpl('fstar/updateHistory.php');
    }
}
