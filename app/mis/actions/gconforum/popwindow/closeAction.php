<?php
/* 
 * @copyright Copyright (c) www.baidu.com
 * @brief 关闭某个吧的广告投放
 * <AUTHOR>
 * @date 2014/11/11
 */
class closeAction extends Util_Actionbase {
    //task_id
    private $_id;
    private $_strForums;
    private $_intStatus;
    //op user name
    private $_close_user_name;
    public function init() {
        self::setUiAttr('COMMIT_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo ) {
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = 'init return error!';
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }
    
    public function process() {
        $this->_id                = intval(Bingo_Http_Request::getNoXssSafe('id', 0));
        $this->_close_user_name = Util_Actionbaseext::getCurUserName();
        $this->_strForums        = trim(Bingo_Http_Request::getNoXssSafe('forums', ''));
        $this->_intStatus         = intval(Bingo_Http_Request::getNoXssSafe('status', '-1'));
        if (0 === $this->_id) {
            Bingo_Log::warning('this task id is error');
            $arrOutput = $this->errRet(100001, 'this task id is error');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }
        if (-1 === $this->_intStatus) {
            Bingo_Log::warning('this status is error');
            $arrOutput = $this->errRet(100002, 'this status is error');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }
        //has forum name
        if ($this->_strForums) {
            $arrInput = array(
                'query_words' => array($this->_strForums,),
            );
            $res = Tieba_Service::call('forum', 'getFidByFname', $arrInput);
            if (false === $res || Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
                Bingo_Log::warning("Tieba_Service_call_forum_getFidByFname_error input[" . serialize($arrInput) . "] output [" . serialize($res) . "]");
                $arrOutput = $this->errRet(100003, 'get forum getFidByFname fail');
                echo Bingo_String::array2json($arrOutput);
                return false;
            }
            
            if (1 !== $res['forum_id'][0]['has_forum_id']) {
                Bingo_Log::warning("this forum has no forum id forum_info:". serialize($value));
                $arrOutput = $this->errRet(100004, 'this forum has no forum_id');
                echo Bingo_String::array2json($arrOutput);
                return false;
            }
            $arrInput = array(
                'id'                => $this->_id,
                'forum_id'          => intval($res['forum_id'][0]['forum_id']),
                'forum_name'        => $res['forum_id'][0]['forum_name'],
                'close_user_name'	=> $this->_close_user_name,
            );
            $closeRes = Tieba_Service::call('vertical', 'closePopTaskByFid', $arrInput);
            if (false === $closeRes || Tieba_Errcode::ERR_SUCCESS != $closeRes['errno']) {
                Bingo_Log::warning('call vertical  closePopTaskByFid fail param:'. serialize($arrInput). ' output:' .  serialize($closelRes));
                $arrOutput = $this->errRet(100005, 'call vertical closePopTaskByFid fail');
                echo Bingo_String::array2json($arrOutput);
                return false;
            } else {
                $arrOutput = $this->errRet(0, 'close task info success');
                echo Bingo_String::array2json($arrOutput);
                return true;
            }
        }
    }
    
    public function errRet($no, $error, $data) {
        return array ('no' => $no, 'errmsg' => $error, 'data' => $data);
    }
}