<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file setOfficialPtAttr.php
 * <AUTHOR>
 * @date 2015/01/29 10:20:06
 * @brief 
 *  
 **/

 
@set_time_limit(0);
@ini_set("memory_limit", "1G");

$strDBName = 'forum_gcon';
$objDB = DB::_getDB($strDBName);
$sql = 'select forum_id,type from forumOfficial where is_online = 1';
$arrOfficialRes = $objDB->query($sql);
if (empty($arrOfficialRes)) {
    var_dump('forumofficial is empty');
}

foreach ($arrOfficialRes as $value) {
    $intForumId = intval($value['forum_id']);
    $intType = intval($value['type']);
    //get forum attr
    $arrInput = array(
        'forum_id' => $intForumId,
    );
    $arrGetAttrRes = Tieba_Service::call('forum', 'getForumAttr', $arrInput);
    if (false === $arrGetAttrRes || Tieba_Errcode::ERR_SUCCESS !== $arrGetAttrRes['errno']) {
        Bingo_Log::warning('call getForumAttr fail '.serialize($arrInput).'_'.serialize($arrGetAttrRes));
        //return false;
        var_dump('getForumAttr fail forum_id:' . $intForumId);
    }
    if (isset($arrGetAttrRes['output']['pt_operation_center'])) {
        $official = $arrGetAttrRes['output']['pt_operation_center'];
        $arrPtAttrVal = array(
            'type' => array(
                'pt_official' => array(
                    'sub_type' => $intType,
                ),
            ),
        );
        $diff = array_diff($arrPtAttrVal, $official);
        if (!is_null($diff) || !empty($diff)) {  
            var_dump('continue forum_id:'.$intForumId);
            continue;
        }
    }
  
    $arrPtAttrVal = array(
        'type' => array(
            'pt_official' => array(
                'sub_type' => $intType,
            ),
        ),
    );
    $arrInput = array(
        'forum_id'   => $intForumId,
        'attr_name'  => 'pt_operation_center',
        'attr_value' => $arrPtAttrVal,
    );
    $intSetAttrRes = _setForumAttr($arrInput);
    if (false === $intSetAttrRes) {
        var_dump('set pt_operation_center fail forum_id:' . $intForumId);
    }
}

function _setForumAttr($arrInput) {
    $arrSetAttrRes = Tieba_Service::call('forum', 'setForumAttr', $arrInput);
    if (false === $arrSetAttrRes || Tieba_Errcode::ERR_SUCCESS !== $arrSetAttrRes['errno']) {
        Bingo_Log::warning('call setForumAttr fail '.serialize($arrInput).'_'.serialize($arrSetAttrRes));
        return false;
    }
    return true;
}

function _getForumAttrNameByType($intType) {
    return true;
}


class DB {
    public static function _getDB($dbname) {
        $dbName = $dbname;
        $objDB = Tieba_Mysql::getDB($dbName);
        if ($objDB && $objDB->isConnected()) {
            return $objDB;
        } else {
            return NULL;
        }
    }
}
 
 