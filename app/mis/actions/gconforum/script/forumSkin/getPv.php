<?php
	ini_set("memory_limit", "512M"); 
    define ('ROOT_PATH', dirname(__FILE__) . "/../..");
    define ('HOME_PHP_PATH', realpath(ROOT_PATH.'/php/bin/php'));
    define('IS_ORP_RUNTIME', true);
    if (!defined('REQUEST_ID')){
        define('REQUEST_ID', Bingo_Log::getLogId());
    }
     
    if (function_exists('camel_set_logid')) {
        camel_set_logid(REQUEST_ID);
    }
    Tieba_Init::init('gconforum');
	$strApiType = 'businessskin';
	$date = date('Ymd',time());
	//var_dump($date);
	//exit;
	$strSign = md5('@123$@)098!'.$date);
	//$strDataUrl = "http://data.tieba.baidu.com/api/tiebadataapi/?apitype=$strApiType&date=$date&sign=$strSign"; //线上
	$strDataUrl = "http://cq01-rdqa-dev020.cq01.baidu.com:8080/api/tiebadataapi/?apitype=$strApiType&date=$date&sign=$strSign";   //线下
	$strRes = file_get_contents($strDataUrl);
	if (!isset($strRes)) {
		Bingo_Log::warning("get file content fail input[" . serialize($strDataUrl) . "] output [" . serialize($strRes) . "]");
		return false;
	}
	$arrRes = json_decode($strRes);
	if ($arrRes['total'] <= 0) {
		Bingo_Log::warning('file is empty');
		return false;
	}
	foreach ($arrRes['rows'] as $row) {
		$arrInput = array (
			'table' => 'forumSkin',
			'where' => array (
				'id' => $row['skinid'],
			),
		);
		$res = Tiaba_Service::call('gconforum', 'getForumSkinByTaskId', $arrInput);
		if (false === $res || $res['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('call gconforum::getForumSkinByTaskId fail input ['.serialize($arrInput) . '] output ['.serialize($res) . ']');
			return false;
		}
		$intPv = $res['pv'];
		$intClickPv = $res['click_num'];
		
		$arrForumNames = explode(';', $res['forum_names']);
		$arrInput = array ( 
			'query_words' => $arrForumNames,
		);
		$res = Tieba_Service::call('forum', 'getFidByFname', $arrInput);
		if (false === $res || $res['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning("Tieba_Service_call_forum_getFidByFname_error input[" . serialize($arrInput) . "] output [" . serialize($res) . "]");
			return false;
		}
		
		$arrForumId = $res['forum_id'];
		foreach ($arrForumId as $value) {
			foreach ($arrRes['rows'] as $rowValue) {
				if ($rowValue['skinid'] === $row['skinid'] && $value['forum_id'] === $rowValue['fid']) {
					$intPv += $rowValue['pv'];
					$intClickPv += $rowValue['clickpv'];
				}
			}		
		}
		$arrInput = array (
			'table' => 'forumSkin',
			'where' => array (
				'id' => $row['skinid'],
			),
			'data' => array (
				'pv' => $intPv,
				'click_num' => $intClickPv,
				'click_per' => $intClickPv/(float)$intPv,
			),
		);
		$res = Tiaba_Service::call('gconforum', 'updateForumSkin', $arrInput);
		if (false === $res || $res['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('call gconforum::updateForumSkin fail input ['.serialize($arrInput) . '] output ['.serialize($res) . ']');
			return false;
		}
		
	}
	//var_dump($strRes);
	
?>




