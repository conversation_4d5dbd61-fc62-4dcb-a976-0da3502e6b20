<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file agencyAction.php
 * <AUTHOR>
 * @date 2014/11/24 9:58:06
 **/
 
class agencyAction extends Util_Actionbase {
    const AGENCY_IN_CHECK = 0;
    const AGENCY_PASS = 1;
    
    //agency verify atatus
    const STATUS_AGENCY_VERIFY_SUBMIT = 0;
    const STATUS_AGENCY_VERIFY_MIS_AUDIT_PASS = 1;
    const STATUS_AGENCY_VERIFY_EXAM_PASS = 2;  //������
    const STATUS_AGENCY_VERIFY_MIS_AUDIT_NOT_PASS = 3;
    const STATUS_AGENCY_VERIFY_EXAM_NOT_PASS = 4;
    const STATUS_AGENCY_VERIFY_OFFLINE = 5;
    const STATUS_AGENCY_VERIFY_EXPIRED = 6;
    const STATUS_AGENCY_VERIFY_AUDIT_PASS = 7;  //������ͨ��
    const STATUS_AGENCY_VERIFY_TEST = 10000;
    
    const STATUS_RELATION_VERIFY_MIS_PROTOCOL_PASS = 3;
    
    public static $intUid;
    public static $strUname;
    public static $intTime;
    private static $_intStatus = 0;
    private static $_intRn = 50;
    private static $_intPn = 1;
    private static $_intTopNum = 6;
    private static $_strQueryWord;
    private static $_intTag = 0;
    
    //income append
    private static $_intType;
    private static $_intStartTime;
    private static $_intEndTime;
    private static $_strSortField;
    private static $_intSortOrder;
    
    public function init() {
        self::setUiAttr('COMMIT_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = 'init return error!';
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }
    
    public function process() {
        $this->_initParams();
        $arrInput = array(
            'field' => array('agency_id', 'agency_name', 'create_user_id', 'create_user_name', 'firm_type', 'firm_license', 'license_num', 'official_letter', 'contacts', 'reason', 'status', 'create_time', 'org_level',  'op_time', 'exams_score'),
            'offset' => ($this->_intPn - 1) * $this->_intRn,
            'limit' => $this->_intRn,           
            'append'   => array(
                'order by op_time desc',
            ),
        );
        if (self::STATUS_AGENCY_VERIFY_MIS_AUDIT_PASS === $this->_intStatus) {
            if (!isset($this->_intTag) || $this->_intTag == 0) {
                $arrInput['cond'] = array(
                    'status' => array(
                        self::STATUS_AGENCY_VERIFY_MIS_AUDIT_PASS,         
                        self::STATUS_AGENCY_VERIFY_EXAM_PASS,              
                        self::STATUS_AGENCY_VERIFY_EXAM_NOT_PASS,  
                        // add by sunhuahua
                        self::STATUS_AGENCY_VERIFY_AUDIT_PASS,  //2 ������ͨ��
                        self::STATUS_AGENCY_VERIFY_OFFLINE,  //5 ������
                    ),
                );
            } else {
                switch ($this->_intTag) {
                    case 1:
                        $arrInput['cond'] = array(
                            'status=' => self::STATUS_AGENCY_VERIFY_MIS_AUDIT_PASS,
                        );
                        break;
                    case 2:
                        $arrInput['cond'] = array(
                            'status=' => self::STATUS_AGENCY_VERIFY_AUDIT_PASS,
                        );
                        break;
                    case 3:
                        $arrInput['cond'] = array(
                            'status=' => self::STATUS_AGENCY_VERIFY_EXAM_NOT_PASS,
                        );
                        break;
                    case 4:
                        $arrInput['cond'] = array(
                            'status=' => self::STATUS_AGENCY_VERIFY_EXAM_PASS,
                        );
                        break;
                    case 5:
                        $arrInput['cond'] = array(
                            'status=' => self::STATUS_AGENCY_VERIFY_OFFLINE,
                        );
                        break;
                    default:
                        $arrInput['cond'] = array(
                            'status' => array(
                                self::STATUS_AGENCY_VERIFY_MIS_AUDIT_PASS,         
                                self::STATUS_AGENCY_VERIFY_EXAM_PASS,              
                                self::STATUS_AGENCY_VERIFY_EXAM_NOT_PASS,  
                                self::STATUS_AGENCY_VERIFY_AUDIT_PASS,  //2 ������ͨ��
                                self::STATUS_AGENCY_VERIFY_OFFLINE,  //5 ������
                            ),
                        );
                        break;
                }
            }
        } else {
            $arrInput['cond'] = array(
                'status=' => $this->_intStatus,
            );
        }
        if (self::STATUS_AGENCY_VERIFY_SUBMIT === $this->_intStatus) {
            $arrInput['append'] = array(
                'order by create_time asc',
            );
        }
        if (self::STATUS_AGENCY_VERIFY_EXPIRED === $this->_intStatus) {
            $arrInput['append'] = array(
                'order by op_time desc',
            );
        }
        
        if ($this->_strQueryWord) {
            $arrInput['word'] = $this->_strQueryWord;
        }
        $arrOut = Tieba_Service::call('official', 'getAgencyList', $arrInput);   
        if (Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']) {
            Bingo_Log::fatal('Tieba_Service_call_official_getAgencyList_error_'.serialize($arrOut).'_'.serialize($arrInput));
            return false;
        }
        
        foreach ($arrOut['data']['list'] as &$value) {
            $intAgencyId = $value['agency_id'];
            $arrInput = array(
                'agency_id' => $intAgencyId,
                'status'    => self::STATUS_RELATION_VERIFY_MIS_PROTOCOL_PASS,                
                'append' => array(
                    'order by create_time desc',
                ),
            );
            
            $arrRes = Tieba_Service::call('official', 'getRelationByAid', $arrInput);           
            $arrForums = array();
            if ($arrRes['data']['total'] > 0) {
                foreach ($arrRes['data']['list'] as $val) {
                    $arrForums[] = $val['forum_name'];
                }
            }
            $value['agency_forums'] = $arrForums;
            //income data
            
        }
        
        //��Ӷ���ҳ�ö� add by cuishichao 20140519
        if (self::STATUS_AGENCY_VERIFY_EXAM_PASS === $this->_intStatus) {
            $arrInput = array(
                'field' => array(
                    'agency_id', 
                    'agency_name', 
                    'address', 
                    'firm_website', 
                    'material',
                    'contacts', 
                    //'display_forums',
                    'weight',
                ),
                'cond' => array(
                    'status=' => self::STATUS_AGENCY_VERIFY_EXAM_PASS,
                    'weight!=' => 0,
                ),
                'append' => array(
                    'order by weight',
                ),
            ); 
            $arrRes = Tieba_Service::call('official', 'getTopAgencyInfo', $arrInput);
            if (Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
                Bingo_Log::warning('call official::getTopAgencyInfo false!');
                return false;
            }
            $arrTopAgency = array();
            for ($i = 0; $i < self::$_intTopNum; ++$i) {
                $arrTopAgency[$i] = '';
            }
            foreach($arrRes['data'] as $arrInfo) {
                if (isset($arrTopAgency[$arrInfo['weight']-1])) {
                    if ($arrInfo['weight'] > 0) {
                        $arrTopAgency[$arrInfo['weight']-1] = $arrInfo['agency_name'];
                    }
                }
            }
            Bingo_Page::assign('top_list', $arrTopAgency); 
        }
        //add by cuishichao end
        
        Bingo_Page::assign('status', $this->_intStatus);
        Bingo_Page::assign('queryWord', $this->_strQueryWord);
        Bingo_Page::assign('pn', $this->_intPn);
        Bingo_Page::assign('data', $arrOut['data']['list']);
        Bingo_Page::assign('total', $arrOut['data']['total']);
        Bingo_Page::setTpl('platform_agent/agency.php');
    }
    
    private function _initParams() {
        $this->intUid       = Util_Actionbaseext::getCurUserId();
        $this->strUname     = Util_Actionbaseext::getCurUserName();
        $this->intTime      = time();
        $this->_intStatus      = intval(Bingo_Http_Request::getNoXssSafe('status', 0));
        $this->_intTag         = intval(Bingo_Http_Request::getNoXssSafe('tag', 0));
        $this->_intRn          = intval(Bingo_Http_Request::getNoXssSafe('rn', 50));
        $this->_intPn          = intval(Bingo_Http_Request::getNoXssSafe('pn', 1));
        $this->_strQueryWord   = strval(trim(Bingo_Http_Request::getNoXssSafe('query_word', '')));
        //income append
        $this->_intType = intval(Bingo_Http_Request::getNoXssSafe('type', 0));
        $this->_intStartTime = intval(Bingo_Http_Request::getNoXssSafe('start_time', 0));
        $this->_intEndTime = intval(Bingo_Http_Request::getNoXssSafe('end_time', 0));
        $this->_strSortField = strval(Bingo_Http_Request::getNoXssSafe('sort_field', ''));
        $this->_intSortOrder = intval(Bingo_Http_Request::getNoXssSafe('sort_order', 1));
        
        
        return true;
    }
    
}
