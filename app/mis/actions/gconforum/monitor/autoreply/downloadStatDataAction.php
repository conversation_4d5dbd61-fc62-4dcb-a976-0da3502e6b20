<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/



/**
 * @file listStatDataAction.php
 * <AUTHOR>
 * @date 2014/11/04 14:54:31
 * @brief 自动回复统计数据列表
 *  
 **/

class downloadStatDataAction extends Util_Actionbase{
    //常量设置区
    const PARAM_IS_INT      = 0;
    const PARAM_IS_STR      = 1;
    const PARAM_IS_ARR      = 2;
    const PARAM_IS_BASIC    = true;

    //默认单页数
    const LIST_PER_NUM      = 20;
    //页码
    private $_intPn;
    private $_intSz;

    //自动回复模板状态
    const STATUS_WAIT_AUDIT     = 0;
    const STATUS_PASS_OFFLINE   = 1;
    const STATUS_PASS_ONLINE    = 2;
    const STATUS_REJECT         = 3;
    const STATUS_DELETED        = 4;

    const ONE_DAY_SEC           = 86400;

    const DOWNLOAD_FILENAME     = 'tpoint_statistics_';

    //全贴吧监控的标记值
    const FLAG_WHOLE_TIEBA = 66666666666;

    //参数区
    private $arrKeyFields;
    private $arrOptFields;
    private $arrReqInfo;

    //全局的错误号和错误信息
    private $_strLocalErrMsg;
    private $_intLocalErrNo;

    // 1st line of Excel file
    private $_excel_head;

    // default starttime
    private $_default_starttime;

    /**
     * Set Necessary fields 
     * @return [type] [description]
     */
    function init(){
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }

        //必要字段和非必要字段
        $this->arrKeyFields = array(
            //'template_ids' => array(self::PARAM_IS_ARR, 'template_ids'),
            'realReplyStat' => array(self::PARAM_IS_ARR, 'realReplyStat'),
        );
        $this->arrOptFields = array(
            'start_time'    => array(self::PARAM_IS_STR, 'start_time'),
            'end_time'      => array(self::PARAM_IS_STR, 'end_time'),
        );

        $this->_excel_head = array();

        $arrTmpHead = array('申请吧', '关键词', '模板id', '总回复量', '实际回复量', );
        foreach($arrTmpHead as $tmpHead){
            $this->_excel_head[] = Bingo_Encode::convert($tmpHead, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
        }
        // 2015-08-06 is the only fake data in Palo
        $this->_default_starttime = strtotime('2015-08-19');
        return true;
    }

    /**
     * [process description]
     * @return [type] [description]
     */
    public function process(){
        if(!Actions_Gconforum_Monitor_Lib_Util::checkSmallFlow()) {
            //读取参数
            if(false === $this->_initParams()){
                $this->_jsonRet($this->_intLocalErrNo, $this->_strLocalErrMsg);
                return false;
            }

            $arrTemplateIds = array();
            $arrTemplateWords = array();
            foreach($this->arrReqInfo['realReplyStat'] as $value){
                $intTemplateId      = intval($value['template_id']);
                $arrTemplateIds[]   = $intTemplateId;
                if(!isset($arrTemplateWords[$intTemplateId])){
                    $arrTemplateWords[$intTemplateId] = array($value['imw']);
                }else{
                    $arrTemplateWords[$intTemplateId][] = $value['imw'];
                }
            }

            $arrStatus = array(self::STATUS_PASS_ONLINE, );
            //调用服务
            $strServiceName = 'downloadAutoreplyPostData';
            $arrInput = array();
            $arrInput['req'] = array();

            $intTodaySec    = strtotime(date("Y-m-d")); //当天的0秒
            $intYesteday    = strtotime(date("Y-m-d",time() - self::ONE_DAY_SEC));//昨天的0秒
            $intStarttime   = strtotime($this->arrReqInfo['start_time']);
            $intEndtime     = strtotime($this->arrReqInfo['end_time']);
            if(false === $intStarttime){
                //$intStarttime   = $intTodaySec;
                $intStarttime   = $this->_default_starttime;
            }
            if(false === $intEndtime){
                //$intEndtime     = $intStarttime+self::ONE_DAY_SEC-1;
                $intEndtime     = $intYesteday;
            }
            else{
                $intEndtime     = $intEndtime+self::ONE_DAY_SEC-1;
            }

            $arrInput['req']['start_time']   = $intStarttime;
            $arrInput['req']['end_time']     = $intEndtime;
            $arrInput['req']['template_ids'] = $arrTemplateIds;

            $arrOutput = Tieba_Service::call('opmonitor', $strServiceName, $arrInput, null, null, 'post', 'php', 'gbk');


            if(false === $arrOutput){
                $this->_intLocalErrNo   = Tieba_Errcode::ERR_NVOTE_CALL_SERVICE_ERROR;
                $this->_strLocalErrMsg  = Tieba_Error::getErrmsg($this->_intLocalErrNo);
                Bingo_Log::warning("$strServiceName : ". $this->_strLocalErrMsg);
                $this->_jsonRet($this->_intLocalErrNo, $this->_strLocalErrMsg);
                return false;
            }

            if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
                //直接透传           
                $this->_strLocalErrMsg  = $arrOutput['errmsg'];
                Bingo_Log::warning("$strServiceName : ". $this->_strLocalErrMsg);
                $this->_jsonRet($arrOutput['errno'], $this->_strLocalErrMsg);
                return false;
            }

            //构建返回的列表
            $arrRetData = array();
            foreach($arrOutput['res'] as $value){
                $intTemplateId = (int)$value['template_id'];
                $item = array();
                $item[0] = $value['forum_name'];
                $item[2] = $intTemplateId;
                $item[3] = intval($value['tpoint_post']);
                $item[4] = intval($value['tpoint_post']) - intval($value['tpoint_delete']);
                foreach($value['word'] as $word){
                    if(in_array($word, $arrTemplateWords[$intTemplateId])){
                        $item[1] = $word;
                        ksort($item, SORT_STRING);
                        $arrRetData[] = $item;
                    }

                }
            }
            $this->_echoResult($arrRetData);        
            return true;
        } else {
            try {
                //todo
            } catch (Exception $e) {
                $errno = $e->getCode();
                $errmsg = $e->getMessage();
                $line = $e->getLine();
                $errmsg = sprintf("line[%d]: %s", $line, $errmsg);
                Bingo_Log::warning($errmsg);
                $this->_jsonRet($errno, $errmsg);
                return false;
            }
        }
    }
    /**
     * Return the results as Excel
     * @param  [type] $fields  [description]
     * @return [type] [description]
     */
    private function _echoResult($arrInput){
        $arrLines   = array();
        $arrLines[] = implode('</td><td style="border:1px solid #ddd">',$this->_excel_head);
        foreach($arrInput as $val){
            $arrLines[] = implode('</td><td style="border:1px solid #ddd">', $val);
        }
        $strOutput = implode('</td></tr><tr><td style="border:1px solid #ddd">', $arrLines);
        $strContent = sprintf('<table ><tr><td style="border:1px solid #ddd">%s</td></tr></table>',$strOutput);

        $strFilename = self::DOWNLOAD_FILENAME . date('Ymd',time());
        header("Content-Type: application/vnd.ms-excel; charset=GBK");
        header("Content-Disposition: inline; filename=\"" . $strFilename . ".xls\"");
        echo $strContent;
        return true;
    }
    /**
     * [_initParams description]
     * @param  [type] $fields  [description]
     * @return [type] [description]
     */
    private function _initParams(){
        if(false === $this->_getParams($this->arrKeyFields, self::PARAM_IS_BASIC)){
            return false;
        }
        $this->_getParams($this->arrOptFields, !self::PARAM_IS_BASIC);
        return true;
    }
    /**
     * [_buildData description]
     * @param  [type] $fields  [description]
     * @param  [type] $content [description]
     * @return [type]          [description]
     */
    private function _buildData($fields, $content){
        $arrRet = array();
        foreach($fields as $strKey => $strVal){
            if(!is_null($content[$strKey])){
                $arrRet[$strVal] = $content[$strKey];
            }
        }
        return $arrRet;
    }
    /**
     * [_buildListData description]
     * @param  [type] $fields  [description]
     * @param  [type] $content [description]
     * @return [type]          [description]
     */
    private function _buildListData($fields, $content){
        $arrRet     = array();
        $intIndex   = 0;
        foreach($content as $arrList){
            foreach($fields as $arrKey => $arrVal){
                $arrRet[$intIndex][$arrVal] = $arrList[$arrKey];  
            }
            $intIndex++;
        }
        return $arrRet;
    }
    /**
     * [_jsonRet description]
     * @param  [type] $intErrno  [description]
     * @param  [type] $strErrmsg [description]
     * @param  [type] $arrData   [description]
     * @return [type]            [description]
     */
    private function _jsonRet($intErrno, $strErrmsg, $arrData)
    {
        $arrRet = array();
        $arrRet['no']       = intval($intErrno);
        $arrRet['error']    = strval($strErrmsg);
        if(0 < count($arrData)){
            $arrRet['data']     = $arrData;
        }
        echo Bingo_String::array2json($arrRet);
    }

    /**
     * [_getParams description]
     * @param  [type] $fields [description]
     * @param  [type] $level  [description]
     * @return [type]         [description]
     */
    private function _getParams($fields, $level)
    {
        foreach($fields as $strField => $arrVal){
            $val = Bingo_Http_Request::getNoXssSafe($strField);
            if(!is_null($val)){
                if(self::PARAM_IS_INT === $arrVal[0]){
                    $this->arrReqInfo[$arrVal[1]] = intval($val);        
                }
                else if(self::PARAM_IS_STR === $arrVal[0]){
                    if('' !== strval($val)){
                        $this->arrReqInfo[$arrVal[1]] = strval($val);        
                    }
                }
                else if(self::PARAM_IS_ARR == $arrVal[0]){
                    $this->arrReqInfo[$arrVal[1]] = $val;
                }
                else{
                    Bingo_Log::warning("null param type is not allowed...");
                    return false;
                }
            }
            else{
                if(self::PARAM_IS_BASIC === $level){
                    $this->_intLocalErrNo   = Tieba_Errcode::ERR_MIS_PARAM_EMPTY;
                    $this->_strLocalErrMsg  = "Key field [$strField] is empty!";
                    Bingo_Log::warning($this->_strLocalErrMsg);
                    return false;
                }
                else{
                    ;
                }
            }
        }
    }

    //class end
}


/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
