<?php
/**
 * <AUTHOR>
 * @date 20170629
 * */

class modAction extends Util_Actionbase {
    const STATUS_ONLINE = 1;
    const STATUS_OFFLINE = 2;

	private $_intType = 0;
	private $_intStartTime = 0;
	private $_intOpUid = 0;
	private $_strOpUname = '';
	private $_strTitle = '';
    private $_arrParam = array();
    private static $_arrActions = array(
        'del' => self::STATUS_OFFLINE,
        'add' => self::STATUS_ONLINE,
        'readd' => self::STATUS_ONLINE,
    );    

	/**
	 * @param
	 * @return [type] [description]
	 */
    public function init(){
        self::setUiAttr('COMMIT_UI');
        if (false === parent::init()){
            if(0 === $this->_intErrorNo){
                $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }
    /**
     * @param 
     * @return [type] [description]
     */
    private function initParam(){
        $this->_intOpUid   = Util_Actionbaseext::getCurUserId();
        $this->_strOpUname = Util_Actionbaseext::getCurUserName();
        
        $strAction = Bingo_Http_Request::getNoXssSafe('action', '');
        if('' === $strAction || !isset(self::$_arrActions[$strAction])){
            Bingo_Log::warning("no action or action wrong");
            return false;
        }
        $intTime = time();
        $this->_intStatus = self::$_arrActions[$strAction];
        $intSingle = (int)Bingo_Http_Request::getNoXssSafe('single', 0);
        if('del' === $strAction || 'readd' === $strAction){
            if($intSingle){
                $intId = (int)Bingo_Http_Request::getNoXssSafe('id', 0);
                $intFid = (int)Bingo_Http_Request::getNoXssSafe('forum_id', 0);
                $this->_intGid = (int)Bingo_Http_Request::getNoXssSafe('game_id',-1);
                if($this->_intGid <=0){
                    Bingo_Log::warning('game_id not exist');
                    return false;
                }
                $this->_arrParam []= array(
                    'id'       => $intId,
                    'game_id'  => $this->_intGid,
                    'forum_id' => (int)$intFid,
                    'status'   => $this->_intStatus,
                    'op_uname' => $this->_strOpUname,
                    'op_uid'   => $this->_intOpUid,
                    'op_time'  => $intTime,
                    'action'   => $strAction,
                );
            }else{
                
                $arrParam = Bingo_Http_Request::getNoXssSafe('rows','');
                
                foreach($arrParam as $k => $v){
                    $intId = (int)$v['id'];
                    $intFid = (int)$v['forum_id'];
                    $this->_intGid = (int)$v['game_id'];
                    $this->_arrParam []= array(
                        'id' => $intId,
                        'game_id' => $this->_intGid,
                        'forum_id' => (int)$intFid,
                        'status'   => $this->_intStatus,
                        'op_uname' => $this->_strOpUname,
                        'op_uid'   => $this->_intOpUid,
                        'op_time'  => $intTime,
                        'action'   => $strAction,
                    );
                }
            }
        }else if('add' === $strAction){
            $this->_intGid = (int)Bingo_Http_Request::getNoXssSafe('game_id',-1);
            if($this->_intGid <=0){
                Bingo_Log::warning('game_id not exist');
                return false;
            }
            $strFns = Bingo_Http_Request::getNoXssSafe('forum_names','');
            if('' === $strFns){
                Bingo_Log::warning('adding forum, but no fids');
                return false;
            }
            if(strstr($strFns,"\n")){//支持每行一个的提交(回车符分隔)
                $arrFns = explode("\n",$strFns);
            }else if(strstr($strFns,"\t")){//支持每行一个的提交(回车符分隔)
                $arrFns = explode("\t",$strFns);
            }else if(strstr($strFns," ")){//支持每行一个的提交(回车符分隔)
                $arrFns = explode(" ",$strFns);
            }else{
                $arrFns = array($strFns);
            }
            //Bingo_Log::warning(print_r($arrFns,1));
            $arrFns = array_unique($arrFns);
            $arrFids = self::getFids($arrFns);
            //Bingo_Log::warning(print_r($arrFids,1));
            foreach($arrFids as $intFid){
                if(0 == $intFid){
                    continue;
                }
                $this->_arrParam []= array(
                    'id'      => 0,
                    'forum_id' => (int)$intFid,
                    'game_id' => $this->_intGid,
                    'status'   => $this->_intStatus,
                    'op_uname' => $this->_strOpUname,
                    'op_uid'   => $this->_intOpUid,
                    'op_time'  => $intTime,
                );
            }

        }else{
            Bingo_Log::warning('action wrong');
            return false;
        }
        
        if(empty($this->_arrParam)){
        	Bingo_Log::warning('empty param');
            return false;
        }
        return true;

    }
    /**
     * [process description]
     * @param  [type] $arrInupt [description]
     * @return [type]           [description]
     */
    public function process($arrInupt){
    	if(!$this->initParam()){
    		Bingo_Log::warning("param error");
            $this->errRet(-1, '参数错误');
    		return false;
    	}
    	foreach($this->_arrParam as $k => $arrInput){
            $strService = 'video';
            $strMethod = 'modifyGameForum4Mis';
            $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
            if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
                Bingo_Log::warning(sprintf("call $strService::$strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
                continue;
            }
        }
    	
		$this->errRet(0, 'success');

    }
    /**
     * [getUids description]
     * @param  [type] $arrUnames [description]
     * @return [type]            [description]
     */
    public static function getFids($arrFnames){
        if(empty($arrFnames)){
            return array();
        }
        $arrInput = array(
            'query_words' => $arrFnames,
        );
        $strService = 'forum';
        $strMethod = 'getFidByFname';
        $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        Bingo_Log::warning(print_r($arrOutput,1));
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call $strService::$strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return array();
        }
        $arrFids = array();
        foreach($arrOutput['forum_id'] as $k => $v){
            $intFid = (int)$v['forum_id'];
            if($v['has_forum_id'] && $v['is_exist'] && !$v['is_forbiden']){
                $arrFids[]=$intFid;
            }
        }
        return $arrFids;
    }
    /**
     * @param $no
     * @param $error
     * @param array $data
     * @return array
     */
    public function errRet($no, $error, $data = array()) {
        $arrRet = array(
            'errno' => $no,
            //'errmsg' => $error,
            'errmsg' => Bingo_Encode::convert($error, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8),
            'data' => $data,
        );
        //Bingo_Log::warning(print_r($arrRet,1));
        echo Bingo_String::array2json($arrRet);
        return true;
    }

}
