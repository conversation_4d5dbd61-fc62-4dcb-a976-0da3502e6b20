<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2016-02-24 14:41:52
 * @version
 * 获取列表
 */
class updatewhiteAction extends Util_Actionbase
{
    /**
     * 初始化
     * @return  boolean
     */
    function init()
    {
        self::setUiAttr('BROWSE_UI');
        if (false === parent::init()) {
            if (0 === $this->_intErrorNo) {
                $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    /**
     * 真正处理过程
     * @return 给前端返回数据
     */
    public function process()
    {
        $id = Bingo_Http_Request::getNoXssSafe('id');
        $user_name = Bingo_Http_Request::getNoXssSafe('user_name');
        $permission = json_decode(Bing<PERSON>_Http_Request::getNoXssSafe('permission'));
        $permission_name = json_decode(Bingo_Http_Request::getNoXssSafe('permission_name'));
        $telephone = Bingo_Http_Request::getNoXssSafe('telphone');
        $qq = Bingo_Http_Request::getNoXssSafe('qq');
        $note = Bingo_Http_Request::getNoXssSafe('note');
        $status = Bingo_Http_Request::getNoXssSafe('status');

        if (intval($id) <= 0 || intval($status) < 0) {
            $_intErrorNo = Tieba_Errcode::ERR_PARAM_ERROR;
            $_strErrorMsg = Tieba_Errcode::$codes[$this->_intErrorNo];
            $this->_error($_intErrorNo, Tieba_Error::getErrmsg($_strErrorMsg));
        }

        if ($permission_name && is_array($permission_name)) {
            $permission_name = Bingo_Encode::convert($permission_name, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
            $arrRet = Tieba_Service::call('forum', 'getFidByFname', array('query_words' => $permission_name),
                null, null, 'post', 'php', 'gbk');
            if ($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("get forum id failed. errno[{$arrRet['errno']}]");
                $this->_error($arrRet['errno'], Tieba_Error::getErrmsg($arrRet['errno']), $arrRet);
                return false;
            }
            foreach ($arrRet['forum_id'] as $item) {
                if (!$item['forum_id'] || $item['forum_id'] == 0) {
                    continue;
                }
                $permission[] = $item['forum_id'];
            }
        }

        if (!is_array($permission)) {
            $permission = null;
        }
        $arrInput = array(
            'id' => $id,
            'user_name' => urlencode($user_name),
            'permission' => $permission,
            'telephone' => $telephone,
            'qq' => $qq,
            'note' => urlencode($note),
            'status' => $status,
            'op_name' => Util_Actionbaseext::getCurUserName(),
        );

        $arrRet = Tieba_Service::call('tbhigh', 'updateWhiteStatus', $arrInput,
            null, null, 'post', 'php', 'gbk');
        if ($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("get audit list failed. errno[{$arrRet['errno']}]");
            $this->_error($arrRet['errno'], Tieba_Error::getErrmsg($arrRet['errno']));
            return false;
        }
        $arrOutput = $arrRet['data'];
        $this->_arrTplVar = $arrOutput;

        Bingo_Page::getView()->setOnlyDataType("json");
        Bingo_Page::setTpl("tbhigh/tbhigh.php");
        return true;
    }

    /**
     * 返回json
     * @param unknown $errno
     * @param unknown $errmsg
     * @param unknown $data
     * @return boolean
     */
    private function _error($errno, $errmsg, $data = array())
    {
        $this->_arrTplVar ['no'] = $errno;
        $this->_arrTplVar ['error'] = iconv("UTF-8", "GBK", $errmsg);
        $this->_arrTplVar ['data'] = $data;

        Bingo_Page::getView()->setOnlyDataType("json");
        Bingo_Page::setTpl("tbhigh/tbhigh.php");
        return true;
    }
}

?>
