<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-01-05 14:23:53
 * @version
 */
class findpeoplexunrenAction extends Util_Actionbase{


	function init(){
		self::setUiAttr('BROWSE_UI');
		if (false === parent::init()){
			if(0 === $this->_intErrorNo){
				$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}

	public function process(){       

		$bol = Util_Actionbaseext::isValidUser();
		if(!$bol){
			//Ȩ����֤ûͨ������init������	
			$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;//��������
			$this->_strErrorMsg = "not a valid user";
			return false;
		}
		$arrUserInfo = Util_Actionbaseext::getUserInfo();

		$keyword=Bingo_Http_Request::get("keyword","");

		self::$MisService = new Service_Mis();
		$arrInput = array(
			"p"=>Bingo_Http_Request::get("pn",1),
			"search"=>$keyword,
			"status"=>3,
			"pcount"=>20 //num per page
		);

		$arrRet = self::$MisService->call("findpeople","getXunrenList",$arrInput);
		
		$page["total"] = $arrRet["count"] ;
		$page['record_pre_page'] = 20;
		$page['curPage'] = Bingo_Http_Request::get("pn",1);

		$arrRet["page"] = $page;
		$arrRet["keyword"]= $keyword;
		$this->_arrTplVar['data'] = $arrRet;
		
		Bingo_Page::setTpl("findpeoplexunren.php");///home/<USER>/tieba-odp/template/mis/control/page.php
      
		if(isset($_GET["debug"]))
		{
	  
			echo "<pre>";
			print_r($this->_arrTplVar);
	  }

	}

}

?>
