<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-01-05 14:23:53
 * @version
 */
class onlineBatchDelAction extends Util_Actionbase{
	function init(){
		self::setUiAttr('COMMIT_UI');
		if (false === parent::init()){
			if(0 === $this->_intErrorNo){
				$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}

	public function process()
	{ 
	//	echo "aaaaaaaaaaaaaaaaaaa";exit;
		require_once('data/Dianbo.php');
		$intOpId	= Util_Actionbaseext::getCurUserId(); 
		$strOpName	= Util_Actionbaseext::getCurUserName();
        //$strTemp = Bingo_Http_Request::getPost();
	    $strInfo = Bingo_Http_Request::get('info','');
	
		//var_dump($strInfo);exit;
		$arrInfo = Bingo_String::json2array($strInfo);
	    //var_dump($arrInfo);exit;  
		if ((false === $arrInfo) || (0 === count($arrInfo)))
		{
			$errno = Tieba_Errcode::ERR_PARAM_ERROR;
			$errmsg = Tieba_Error::getErrmsg($errno);
			//$this->_setRet($errno, $errmsg);
			$this->_arrTplVar['errno'] = $errno;
			$this->_arrTplVar['errmsg'] = $errmsg;
			return;
		}
		
		//var_dump($arrInfo);exit;		
		$ret = Data_Dianbo::delBatchDianboConf($arrInfo, $strOpName, $intOpId);	    
	    //var_dump($ret);
		$this->_arrTplVar['errno'] = $ret['errno'];
		$this->_arrTplVar['errmsg'] = $ret['errmsg'];
		Bingo_Page::setOnlyDataType("json");
		//Bingo_Page::setTpl("dianbo/onlineBatchDel.php");///home/<USER>/tieba-odp/template/mis/control/simpledata/Page.php
		return true;
	}
	
		
	

}

?>
