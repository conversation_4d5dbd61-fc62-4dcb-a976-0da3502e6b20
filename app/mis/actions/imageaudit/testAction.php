<?php
/**
 * ���ģ��
 * <AUTHOR>
 *
 */
class testAction extends Util_Actionbase {
	const PAGESIZE = 50;
	function init() {
		self::setUiAttr('BROWSE_UI');//����UI������
		//�����ʼ������ʼ��ʧ����δ���ô���������ô������Ϊ�������ô���
		if(false == parent::init()){
			if(0 === $this->_intErrorNo){
				$this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strErrorMsg = Tieba_Errcode::$codes[$this->_intErrorNo];
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}		
		return true;
	}
	public function process() {	
		$url = Bingo_Http_Request::getNoXssSafe('url');
		$sid = (int)Bingo_Http_Request::getNoXssSafe('sid',0);
		if(empty($url) || empty($sid)){
			$url = 'http://imgsrc.baidu.com/forum/cp%3Dtieba%2C10%2C408%3Bap%3D%BD%A3%B5%C0%B6%C0%D7%F0%B0%C9%2C90%2C416/sign=c11b3d53d309b3defff2ec2ca5da0ee0/15fb83119313b07e5dd5ca330ed7912397dd8caf.jpg';
			$sid = 1653379;
		}
		$ret = Service_Imageaudit_Similarity::sendSimilarity($sid, $url);
		var_dump($ret);
	}	
}
?>