<?php
/**
 * @author: lihuan08
 * @date: 2016-12-05
 * @file: deleteSysRecommendAction.php
 * @description:
 */
class deleteSysRecommendAction extends Util_Actionbase{
    /**
     * @brief
     * @param
     * @return
     */
    public function init(){
        self::setUiAttr('COMMIT_UI');
        parent::init();
        return true;
    }
    /**
     * @brief
     * @param
     * @return
     */
    public function process(){
        if($this->_check()){
            $arrOutput = $this->_process();
            echo Bingo_String::array2json($arrOutput);
        }
    }
    /**
     * @brief
     * @param
     * @return
     */
    public function _check(){
        return true;
    }
    /**
     * @brief
     * @param
     * @return
     */
    public function _process(){
        $intId = intval(Bingo_Http_Request::get('id',0));

        if( $intId <= 0 ){
            Bingo_Log::warning('params invalid, arrInput='.serialize($_GET));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $strOpUser = Util_Actionbaseext::getCurUserName(); //todo
        $param = array(
            'id' => $intId,
            'op_user'=> $strOpUser,
        );
        $ret = Tieba_Service::call('bottle', 'deleteSysRecommend', $param, null, null, 'post', 'php', 'utf-8');
        if($ret == false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call bottle::deleteSysRecommend fail, input='.serialize($param).'output='.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }         
        
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS); 
    }
    /**
     * @brief
     * @param
     * @return
     */
    public static function _errRet($errno,$errmsg='',array $arrExtData=array()){
        if($errmsg == ''){
            //$errmsg = Tieba_Error::getErrmsg($errno);
            $errmsg = Bingo_Encode::convert(Tieba_Error::getErrmsg($errno), Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        }
        $arrRet = array(
            'no' =>intval($errno),
            'msg'=>strval($errmsg),
            'data'  =>$arrExtData,
        );
        return $arrRet;
    }   
        
}
