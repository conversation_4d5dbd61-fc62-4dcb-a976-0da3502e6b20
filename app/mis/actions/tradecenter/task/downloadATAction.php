<?php
class downloadATAction extends Util_Actionbase
{
    const ORP_STORAGE_PATH    = '/app/client-mis/platform/klose';

    const ORP_STORAGE_TOKEN   = 'v-u3J8de49oY3U4mwaQFeoM-JnQ99MkO';
    const ORP_STORAGE_PRODUCT = "tieba";

    // offline
    //const ORP_STORAGE_TOKEN   = 'GEPFYSmP8xeLmRNcChDKO6DqDw892-qz';
    //const ORP_STORAGE_PRODUCT = 'storage';

    private $_orpStorage = null;

    function init() {
        self::setUiAttr('COMMIT_UI');
        if ( false === parent::init() ) {
            if ( 0 === $this->_intErrorNo) {
                $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = 'init error!';
                Bingo_Log::warning($this->_strErrorMsg);
                return false;
            }
        }
        
        //check user
        $this->_arrUserInfo = Util_Actionbaseext::getUserInfo();
        if (empty($this->_arrUserInfo) || empty($this->_arrUserInfo)) {
            $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
            $this->_strErrorMsg = 'check user error!';
            Bingo_Log::warning($this->_strErrorMsg);
            return false;
        }
        //check perm
        return true;
    }

    public function process() {
        $intTaskId = intval(Bingo_Http_Request::get('id'));
        if ($intTaskId <= 0) {
            return false;
        }

        $arrInput = array(
            'task_id' => $intTaskId,
        );
        $arrOutput = Tieba_Service::call('klose', 'getTask', $arrInput);

        if ($arrOutput['errno'] !== 0) {
            return false;
        }

        $strFileName = $arrOutput['output']['task_res'];

        $strPrefix   = self::ORP_STORAGE_PATH;

        if (!$this->_initStorage()) {
            Bingo_Log::warning('init orp storage failed');
            Util_Tradecenter_Common::jsonOutput(-4, 'init orp storage failed');
            return false;
        }
        $strPath = sprintf("%s/%s", $strPrefix, $strFileName);

        $objFile = $this->_orpStorage->get($strPath);
        if (is_object($objFile))
        {
            $strRealName = $objFile->getTmpFilePath();
            header("Content-Disposition: attachment; filename=$strFileName");
            echo file_get_contents($strRealName);
        }

        return true;
    }

    private function _initStorage()
    {
        $this->_orpStorage = new Orp_Storage(self::ORP_STORAGE_TOKEN, self::ORP_STORAGE_PRODUCT);
        if (!is_object($this->_orpStorage)) {
            return false;
        }

        return true;
    }
}
?>
