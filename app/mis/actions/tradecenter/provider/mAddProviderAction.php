<?php
class mAddProviderAction extends Util_Actionbase 
{
    private $_arrProvidersInfo = array();
    private $_arrLevelInfo  = array();
    
    const CHECK_PERM_NAME        = "has_adforum_manage";
    const ERROR_NO_PERM          = 1;
    const ERROR_SOME_FORUM_NAME  = 2;
    const ERROR_SOME_USER_NAME   = 3;
    const ERROR_CALL_SERVICE     = 4;
    const ERROR_SOME_INSERT_FAIL = 5;
    const ERROR_UPLOAD_FILE      = 6;
    const ERROR_CLASS_ID         = 7;
    const ERROR_MAX_ITEMS        = 8;
    
    function init() {
        self::setUiAttr('COMMIT_UI');
        if ( false === parent::init() ) {
            if ( 0 === $this->_intErrorNo) {
                $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strErrorMsg = 'init error!';
                Bingo_Log::warning($this->_strErrorMsg);
                return false;
            }
        }
        
        //check user
        $this->_arrUserInfo = Util_Actionbaseext::getUserInfo();
        if (empty($this->_arrUserInfo) || empty($this->_arrUserInfo)) {
            $this->_intErrorNo = Tieba_Errcode::ERR_MO_PARAM_INVALID;
            $this->_strErrorMsg = 'check user error!';
            Bingo_Log::warning($this->_strErrorMsg);
            return false;
        }
        //check perm
        return true;
    }

    public function process() {
        //check perm
        if (!Util_Tradecenter_Common::checkPerm($this->_arrUserInfo['user_id'], self::CHECK_PERM_NAME)) {
            Util_Tradecenter_Common::jsonOutput(self::ERROR_NO_PERM, "no perm"); 
            return false;
        }
        //read file
        $strFileName = $_FILES['commonfile']['tmp_name'];
        if (empty($strFileName)) {
            Util_Tradecenter_Common::jsonOutput(self::ERROR_UPLOAD_FILE, "file error!"); 
            return false;
        }
        if (!$this->_readFileFromTempDir($strFileName)) {
            return false;
        }

        if (count($this->_arrProvidersInfo) === 0 ) {
            Util_Tradecenter_Common::jsonOutput(self::ERROR_MAX_ITEMS, "no items or file illegal"); 
            return false;
        }
        if (count($this->_arrProvidersInfo) > 1000) {
            Util_Tradecenter_Common::jsonOutput(self::ERROR_MAX_ITEMS, "max provider items is 1000"); 
            return false;
        }

        $arrProInfos = array();
        //get forum id
        $arrTempForumName = array_keys($this->_arrProvidersInfo);
        $arrForumName = array();
        foreach ($arrTempForumName as $strForumName) {
            $arrForumName[] = (string)$strForumName;
        }
        $arrForumMap = $this->_getForumIDByName($arrForumName);
        if (false === $arrForumMap || empty($arrForumMap)){
            Util_Tradecenter_Common::jsonOutput(self::ERROR_CALL_SERVICE, "get forum id by name error!"); 
            return false;
        }
        $arrErrorFName = array();
        foreach ($arrForumName as $strForumName) {
            if (is_numeric($strForumName)) {
                $strForumName = (int)$strForumName;
            }
            if (!isset($arrForumMap[$strForumName]) || $arrForumMap[$strForumName] === 0) {
                $arrErrorFName[] = $strForumName;
                continue;
            }
            $arrProInfos[$arrForumMap[$strForumName]]['forum_name'] = (string)$strForumName;
        }
        if (!empty($arrErrorFName)) {
            Bingo_Log::warning("some forum don't exists ".serialize($arrErrorFName));
            Util_Tradecenter_Common::jsonOutput(self::ERROR_SOME_FORUM_NAME, "some forum don't exist", $arrErrorFName);
            return false;
        }
        $arrClassInfo = Tieba_Service::call('klose', 'getAllClassSchedule', array());
        if (false === $arrClassInfo || $arrClassInfo['errno'] !== 0) {
            Bingo_Log::warning("call klose::getAllClassSchedule fail ".serialize($arrClassInfo));
            Util_Tradecenter_Common::jsonOutput(self::ERROR_CALL_SERVICE, "get class info error!"); 
            return false;
        }
        $arrClassID = array();
        foreach ($arrClassInfo['output'] as $value) {
            $arrClassID[$value['class_id']] = $value['class_id'];
        }
        foreach ($this->_arrLevelInfo as $intClassID) {
            if (!isset($arrClassID[$intClassID])) {
                Util_Tradecenter_Common::jsonOutput(self::ERROR_CLASS_ID, "class info error!");
                return false;
            }
        }
        
        foreach ($arrProInfos as $key => $value) {
            $arrProInfos[$key]['class_id']   = $this->_arrProvidersInfo[$value['forum_name']]['level'];
            $arrProInfos[$key]['has_interface'] = intval($this->_arrProvidersInfo[$value['forum_name']]['has_interface']);
            
            if (intval($this->_arrProvidersInfo[$value['forum_name']]['start_time']) === 0) {
                $arrProInfos[$key]['start_time'] = **********;
                $arrProInfos[$key]['end_time']   = **********;
            }
            else {
                $arrProInfos[$key]['start_time'] = $this->_arrProvidersInfo[$value['forum_name']]['start_time'];
                $arrProInfos[$key]['end_time']   = $arrProInfos[$key]['start_time'] +  86400*365;
            }
            if (intval($this->_arrProvidersInfo[$value['forum_name']]['has_interface']) === 0) {
                $arrProInfos[$key]['start_time'] = time();
                $arrProInfos[$key]['end_time']   = **********;
            }
        }
        
        //call service
        $arrInput = array(
            'provide_infos' => $arrProInfos,
            'op_uuap_id'    => $this->_arrUserInfo['user_id'],
            'op_uuap_name'  => $this->_arrUserInfo['user_name'],
        );
        $arrOut = Tieba_Service::call('klose', 'mAddProvider', $arrInput);
        if (false === $arrOut || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call klose mAddProvider error '.serialize($arrOut).'_'.serialize($arrInput));
            Util_Tradecenter_Common::jsonOutput(self::ERROR_CALL_SERVICE, "call service error");
            return false;
        }
        if (!empty($arrOut['output'])) {
            Util_Tradecenter_Common::jsonOutput(self::ERROR_SOME_INSERT_FAIL, "some insert failed!", $arrOut['output']);
            return false;
        }
        Util_Tradecenter_Common::jsonOutput(0, "success");
        return true;
    }
    
    private function _getForumIDByName($arrForumName) {
        if (!empty($arrForumName)) {
            $arrInput = array(
                'query_words'=>$arrForumName,
            );
            $arrOut = Tieba_Service::call('forum', 'getFidByFname', $arrInput);
            if (false === $arrOut || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("call forum::getFidByFname error ".serialize($arrOut)."_".serialize($arrInput));
                return false;
            }
            
            foreach ($arrOut['forum_id'] as $arrForumInfo) {
                $arrForumMap[$arrForumInfo['qword']] = $arrForumInfo['forum_id'];
            }
        }
        return $arrForumMap;
    }

    /*
     * read file
     * forum_name, level, has_interface, start_time
     */
    private function _readFileFromTempDir($strFileName) {
        $objFd = fopen($strFileName, 'r');
        $arrProvidesInfo = array();
        while (!feof($objFd)){
            $strBuffer = trim(fgets($objFd));
            if (empty($strBuffer)) {
                continue;
            }
            $arrBuffer = explode("\t", $strBuffer);
            if (count($arrBuffer) !== 4 || empty($arrBuffer[0]) || intval($arrBuffer[1])<0 || intval($arrBuffer[2]) <0 || intval($arrBuffer[2]) > 1) {
                Bingo_Log::warning("buffer error! ".$strBuffer);
                Util_Tradecenter_Common::jsonOutput(self::ERROR_UPLOAD_FILE, "file not correct !", $strBuffer);
                fclose($objFd);    
                return false;
            }
            $arrBuffer[3] = strtotime($arrBuffer[3]);
            $arrProvidesInfo[$arrBuffer[0]] = array(
				'level'         => intval($arrBuffer[1]),
				'has_interface' => intval($arrBuffer[2]),
                'start_time'    => $arrBuffer[3],
            );
            $this->_arrLevelInfo[]= intval($arrBuffer[1]);
        }
        if(empty($arrProvidesInfo)) {
            Util_Tradecenter_Common::jsonOutput(self::ERROR_UPLOAD_FILE, "file is empty!");
            return false;
        }
        fclose($objFd);
        $this->_arrProvidersInfo = $arrProvidesInfo;
        return true;
    }
}
