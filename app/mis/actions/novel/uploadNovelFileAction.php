<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2015/5/6
 * Time: 15:15
 */
define('BINGO_ENCODE_LANG', 'UTF-8');

class uploadNovelFileAction extends Util_Actionbase {

    private $_files = array();

    const MAX_CHAPTER_COUNT = 5000;

    public function init(){
        self::setUiAttr('BROWSE_UI');
        parent::init();
        header("Content-type: text/html; charset=utf-8");
        return true;
    }

    public function process(){
//        $bolLogin   = (boolean)Tieba_Session_Socket::isLogin();
//        if(!$bolLogin) {
//            echo ('必须登录才能操作!');
//            //header("Refresh:5;url={$_SERVER['HTTP_REFERER']}");
//            exit;
//        }
//        $op_uid  = intval(Tieba_Session_Socket::getLoginUid());
//        $op_uname  = strval(Tieba_Session_Socket::getLoginUname());

        $op_uid = intval(Util_ActionbaseExt::getCurPassId());
        $op_uname = trim(strval(Util_ActionbaseExt::getCurPassName()));
        $op_ip = intval(Util_ActionbaseExt::getUserIp());

        if($op_uid <= 0 || $op_uname == '') {
            Bingo_Log::warning('invalid user login info, uid='.$op_uid.',uname='.$op_uname);
            echo ("登录信息不合法! id($op_uid), name($op_uname)");
            exit;
        }

        $op_ip = Bingo_Http_Ip::ip2long(Bingo_Http_Ip::getConnectIp());
        $forum_id = Bingo_Http_Request::get('forum_id', 0);

        if($forum_id <= 0) {
            Bingo_Log::warning('invalid forum id:'.$forum_id.'!');
            echo ("吧ID($forum_id)不合法!");
            //header("Refresh:5;url={$_SERVER['HTTP_REFERER']}");
            exit;
        }

        $this->_files = $_FILES['file'];

        if(0 != $this->_files['error']) {
            Bingo_Log::warning('upload error! errno:'.$this->_files['error']);
            echo("上传失败! 错误号({$this->_files['error']})");
            //$this->_showFileInfo();
            //header("Refresh:5;url={$_SERVER['HTTP_REFERER']}");
            exit;
        }

        $file_dir = dirname(__FILE__) . '/../../../../data/app/mis/novel';
        if(!is_dir($file_dir)) {
            mkdir($file_dir);
        }

        $rand_num = mt_rand(10000,99999) . time();
        $file_name = $file_dir .'/'.$rand_num.$this->_files['name'];
        move_uploaded_file($this->_files['tmp_name'], $file_name);

        $file_content = file_get_contents($file_name);
        $novel_info = Bingo_String::json2array($file_content, Bingo_Encode::ENCODE_UTF8);
        if($novel_info == false) {
            Bingo_Log::warning("'not a valid json file!");
            echo('不是一个合法的文件，请确保是没有多余字符的原始JSON格式!');
            //$this->_showFileInfo();
            //header("Refresh:5;url={$_SERVER['HTTP_REFERER']}");
            exit;
        }

        if(/*!isset($novel_info['bookid']) || !isset($novel_info['bookname'])
            || */!is_array($novel_info['chapterlist'])) {
            Bingo_Log::warning('invalid novel content! keys:'
                . serialize(array_keys($novel_info)));
            echo('文件内容不合法，缺少章节列表内容!');
            //header("Refresh:5;url={$_SERVER['HTTP_REFERER']}");
            exit;
        }

        //Bingo_Log::warning(var_export($novel_info['chapterlist'], 1));
        $count = count($novel_info['chapterlist']);
        if($count <= 0) {
            echo("章节数为{$count}，无需上传!");
            exit;
        }

        if($count > self::MAX_CHAPTER_COUNT) {
            Bingo_Log::warning('too big a novel! count:'.$count);
            //Bingo_Log::warning(var_export($chapter, 1));
            echo("小说章节数($count)太大!");
            $this->delUploadProcess($forum_id,$file_content);
            //header("Refresh:5;url={$_SERVER['HTTP_REFERER']}");
            exit;
        }

        $start = time();

        $last_process = $this->getUploadProcess($forum_id,$file_content);
        Bingo_Log::warning('last process: ' . $last_process);

        //每10章有一次重试机会?
        $left_count = max(0, $count - $last_process);
        $retry_times_max = min(5, $left_count/10);
        $retry_times = 0;

        for($index = $last_process; $index < $count; ++$index) {
            $chapter = $novel_info['chapterlist'][$index];
            if(!isset($chapter['chapterno']) || !isset($chapter['title'])
                || !isset($chapter['content'])) {
                Bingo_Log::warning('invalid chapter content! index:'.$index
                    . ', keys:' . serialize(array_keys($chapter)));
                //Bingo_Log::warning(var_export($chapter, 1));
                echo("第($index)个章节内容不合法，缺少章节号/标题/内容!");
                $this->delUploadProcess($forum_id,$file_content);
                //header("Refresh:5;url={$_SERVER['HTTP_REFERER']}");
                exit;
            }

            $arrInput = array(
                'op_uid' => $op_uid,
                'op_uname' => $op_uname,
                'forum_id' => $forum_id,
                'chapter_id' => $chapter['chapterno'],
                'inner_id' => isset($chapter['id']) ? $chapter['id'] : 0,
                'title' => Bingo_String::xssDecode($chapter['title']),
                'content' => $chapter['content'],
                'op_ip' => $op_ip,
            );

            do {
                $ret = Tieba_Service::call('novel', 'addNovelThread', $arrInput, null, null, 'post', 'php', 'utf-8');
                if (false !== $ret && Tieba_Errcode::ERR_SUCCESS == $ret['errno']) {
                    $this->setUploadProcess($forum_id, $file_content, $index+1);
                    break;
                }

                sleep(1);
                ++$retry_times;
            } while($retry_times <= $retry_times_max);

            if($retry_times > $retry_times_max) {
                Bingo_Log::warning("add novel thread fail, chapter_id:{$chapter['chapterno']}");
                echo "在发布章节{$chapter['chapterno']}的时候遇到问题，请重试!";
                exit;
            }

            usleep(10000);
        }

        $end = time();
        $consume = $end - $start;

        //echo "upload finished! ";
        //$this->_showFileInfo();
        //echo "bookid: {$novel_info['bookid']}<br/>";
        //echo "bookname: {$novel_info['bookname']}<br/>";
        //echo "chapter num: " .count($novel_info['chapterlist']). "<br/>";
        echo "发布小说结束! 一共有".count($novel_info['chapterlist'])."章,本次上传耗时{$consume}秒";
        //header("Refresh:5;url={$_SERVER['HTTP_REFERER']}");
    }

    protected function _showFileInfo() {
        echo 'name: ' . $this->_files['name'] . '<br/>';
        echo 'type: ' . $this->_files['type'] . '<br/>';
        echo 'size: ' . $this->_files['size'] . '<br/>';
        echo 'error: ' . $this->_files['error'] . '<br/>';
    }

    protected function _jsonRet($errno, $errmsg = '', array $arrExtData = array()){
        $arrRet = array(
            'no'=>intval($errno),
            'error'=>strval($errmsg),
            'data'=>$arrExtData,
        );
        Bingo_Log::pushNotice("errno",$errno);
        Bingo_Http_Response::contextType('application/json');
        echo Bingo_String::array2json($arrRet, Bingo_Encode::ENCODE_UTF8);
        return 0;
    }

    /**
     * 批量上传发帖容易失败, 增加续传功能
     */
    const REDIS_NAME = 'tbapp';
    const REDIS_KEY_PREFIX = 'Mis_Novel_UpProc';
    private  $_redis = null;
    private function getRedis()
    {
        if(is_null($this->_redis)) {
            $redis = new Bingo_Cache_Redis(self::REDIS_NAME);
            if (!$redis || !$redis->isEnable()) {
                Bingo_Log::warning("init redis fail.");
                return null;
            }
            $this->_redis = $redis;
        }
        return $this->_redis;
    }

    private function getUploadProcess($forum_id, $file_content)
    {
        $redis = $this->getRedis();
        if(is_null($redis)) {
            return 0;
        }

        $file_md5 = md5($file_content);
        $key = self::REDIS_KEY_PREFIX.'_'.$forum_id.'_'.$file_md5;

        $arrParam = array(
            'key' => $key,
        );

        $ret = $redis->GET($arrParam);
        if(!$ret || $ret['err_no'] !== 0) {
            return 0;
        }

        if(is_null($ret['ret'][$key])) {
            return 0;
        }

        return intval($ret['ret'][$key]);
    }

    private function setUploadProcess($forum_id, $file_content, $process) {
        $redis = $this->getRedis();
        if(is_null($redis)) {
            return;
        }

        $file_md5 = md5($file_content);
        $key = self::REDIS_KEY_PREFIX.'_'.$forum_id.'_'.$file_md5;

        $arrParam = array(
            'key' => $key,
            'value' => $process,
        );

        $ret = $redis->SET($arrParam);
        if(!$ret || $ret['err_no'] !== 0) {
            return;
        }

        $arrParam = array(
            'key' => $key,
            'seconds' => 12960000,
        );

        $redis->EXPIRE($arrParam);
    }

    private function delUploadProcess($forum_id, $file_content)
    {
        $redis = $this->getRedis();
        if(is_null($redis)) {
            return;
        }

        $file_md5 = md5($file_content);
        $key = self::REDIS_KEY_PREFIX.'_'.$forum_id.'_'.$file_md5;

        $arrParam = array(
            'key' => $key,
        );

        $redis->DEL($arrParam);
    }

}
