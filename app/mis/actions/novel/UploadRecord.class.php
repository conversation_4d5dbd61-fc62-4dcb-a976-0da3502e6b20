<?php
/**
 * Created by PhpStorm.
 * User: shangshuai02
 * Date: 2015/5/27
 * Time: 11:54
 */
define('UPLOAD_STATUS_OK', 0);
define('UPLOAD_STATUS_ERR', 1);
define('UPLOAD_STATUS_STOP', 2);

class UploadRecord {
    private static $_redis = null;

    const REDIS_NAME = 'tbapp';

    const REDIS_KEY = 'Mis_Novel_UpStat_';

    private function _init()
    {
        if(!self::$_redis || !self::$_redis->isEnable()) {
            self::$_redis = new Bingo_Cache_Redis(self::REDIS_NAME);
        }
    }

    public function setUploadStatus($forum_id, $arrStatus)
    {
        $this->_init();
        if(!self::$_redis->isEnable()) {
            //echo("get redis fail!\n");
            return false;
        }

        $key = self::REDIS_KEY . $forum_id;

        $arrParams = array(
            'key' => $key,
            'value' => serialize($arrStatus),
        );

        $ret = self::$_redis->SET($arrParams);
        if(!$ret || $ret['err_no'] != 0) {
            //echo("set up status to redis fail!\n");
            return false;
        }

//        $arrParams = array(
//            'key' => $key,
//            'seconds' => 12960000,
//        );
//
//        self::$_redis->EXPIRE($arrParams);
        return true;
    }

    public function getUploadStatus($forum_id, &$arrStatus)
    {
        $this->_init();
        if(!self::$_redis->isEnable()) {
            //echo("get redis fail!");
            return false;
        }

        $key = self::REDIS_KEY . $forum_id;

        $arrParams = array(
            'key' => $key,
            'value' => serialize($arrStatus),
        );

        $ret = self::$_redis->GET($arrParams);
        if(!$ret || $ret['err_no'] != 0 || is_null($ret['ret'][$key])) {
            //echo("get up status from redis fail!\n");
            return false;
        }

        $arrStatus = unserialize($ret['ret'][$key]);
        return true;
    }
}
