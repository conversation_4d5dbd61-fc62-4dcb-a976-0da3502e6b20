<?php
/**
 *Author: ji<PERSON><PERSON>bin
 *Filename: getSecondDimListAction 
 *Date: 2014-12-24
 *Desc: 下拉列表
 */
class getSecondDimListAction extends Util_Actionbase{
    private $_arrFilterKey = array(
        0,'0',1,'1','set','app','c','file','http','string','yy'
    );
    public function init(){
        self::setUiAttr('BROWSE_UI');
        parent::init();
        return true;
    }
    public function process(){
        $type = Bingo_Http_Request::get('type','all');
        $arrDimList = Util_Appmonitor_Data::getDimList();
        if(!isset($arrDimList[$type])){
            Bingo_Log::warning('type param error,the type is[' . $type .']');
            $this->_displayJson(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        if($type == 'all'){
            $data = array(
                'all' => '全部'
            );
            $this->_displayJson(Tieba_Errcode::ERR_SUCCESS,$data);
            return true;
        }
        $data = Util_Appmonitor_Data::getNodeData($type);
        switch($type){
            case 'app_class2':
                break;
            case 'task':
                break;
            case 'frpage':
                break;
            case 'scene_id':
                break;
        }

        $time = time();
        $arrList = array();
        foreach($data as $value){
            $arrItem = explode('@',$value);
            if(count($arrItem) == 2 && !Bingo_Array::in_array($arrItem[1],$this->_arrFilterKey)){
                //$arrList[$arrItem[1]] = $arrItem[1];
                $arrList[] = $arrItem[1];
            }
        }
		$this->_displayJson(Tieba_Errcode::ERR_SUCCESS,$arrList);
    }
    private static function _getSceneIdInfo($arrSceneIdList = array()){
        $arrList = array();
        foreach($arrSceneIdList as $key=>$value){
            //过滤掉一些没用的scene_id,scene_id都必须是7位
            if($value > 1000000){
               $arrList[] = intval($value);
            }
        }
//        $arrRet = Tieba_Service::call();
    }
    private static function _getAppClass2Info($arrRet){

    }

}
