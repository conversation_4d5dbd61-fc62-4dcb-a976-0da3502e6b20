<?php
/*
 * �޸�����ϸ�������ݣ���forum_consume�в�����ݣ�����forumstyle
 * @author: cuishichao
 */
require_once 'dbManager.php';
require_once 'configRW.php';
require_once 'timeTools.php';
require_once 'crawlerLog.php';

//autoload
function __autoload($strClassName) {
	require_once str_replace ( '_', '/', $strClassName ) . '.php';
}
spl_autoload_register ( '__autoload' );

set_time_limit ( 0 );
ini_set ( 'memory_limit', '2048M' );

$path = '/home/<USER>/orp001/php/phplib/';
set_include_path ( get_include_path () . PATH_SEPARATOR . $path );

class updateStyleName{
	private $forumMap = array();//map  id=>name
	private $arrConnArr = array();
	/*
	 * ��ʼ�������������ļ���ÿ�ν���ץȡ�н��ж�ȡ
	 */
	public function init($strDBConfigFileName)
	{
		//if(self::getDBInfoList($strDBConfigFileName)===FALSE){
		//	crawlerLog::getInstance()->writeLog("$strDBConfigFileName ���ݿ������ļ�����ȷ", 2);
		//	die();
		//}
		return true;
    }
	/*
	 * ��ȡforum_fname_list�ļ����ҵ�name��Ӧ��id�������map��ʽ
	 */
	private function getForumInfo($strForumFileName,$strForumNamesFile){
		return TRUE;
	}
	/*
	 * ���������ļ���ȡ���ݿ��������Ϣ��PS����ֻһ�����ݿ�,����ֵ��һ����άarray
	 */
	private function getDBInfoList($strFileName){
		if(!is_file($strFileName)){
			return FALSE;
		}
		$fd = fopen($strFileName, 'r');
		if($fd === FALSE){
			return FALSE;
		}
		$arrKeys = array(CONFIG_DBHOST,CONFIG_DBPORT,CONFIG_USERNAME,CONFIG_PASSWORD,CONFIG_DBNAME);
		$arrConn = array();//���ݿ�������Ϣ����
		$arrValueOfConn = array();//һ�����ݿ�������Ϣ
		$arrPos = -1;
		while(!feof($fd)){
			$buffer = fgets($fd);
			$buffer = trim($buffer);
			if(strpos($buffer, "#") === 0){
				continue;
			}
			$arrKV = explode(":", $buffer);
			if(count($arrKV) == ARRAY_LEN){
				if(in_array($arrKV[0], $arrKeys)){
					if($arrKV[0] === CONFIG_DBHOST)
					{
						if($arrPos == -1){
							$arrPos++;
						}
						else{
							$arrConn[$arrPos++] = $arrValueOfConn;
							$arrValueOfConn = array();
						}
					}
					$arrValueOfConn[$arrKV[0]] = $arrKV[1];
					continue;
				}
			}
		}
		if($arrPos != -1){
			$arrConn[$arrPos++] = $arrValueOfConn;
			$this->arrConnArr = $arrConn;
		}
		else{
			return FALSE;
		}
		//check  �ǲ�������������,ÿ�����ݿ�������Ϣ�Ƿ�����
		foreach ($this->arrConnArr as $con){
			if(!isset($con[CONFIG_DBHOST])||!isset($con[CONFIG_DBNAME])||!isset($con[CONFIG_DBPORT])||!isset($con[CONFIG_USERNAME])||!isset($con[CONFIG_PASSWORD])){
				return FALSE;
			}
		}
		return TRUE;
	}
	/**
	 * ��ʼͳ�����ݿ��е�����  
	 */
	public function crawler()
	{
		echo "\nstart to crawler.......\n";
		$startTime = time();
        $strSQL1 = "select forum_id, description from forum_card_table where forum_id = ";
        $strSQL2 = " and status=1 order by examine_time desc limit 0,1";
        $fd = fopen('forum_id', 'r');
		$fd_bak = fopen('forum_style_bak', 'w');
        $arrForumId = array();
        while(!feof($fd)){
            $strBuffer = intval(fgets($fd));
            if($strBuffer <=0){
              continue;
            }
            $arrForumId[] = $strBuffer;
        }
		//foreach ($this->arrConnArr as $arr)
		{
			$arr = array(
			  'db_host'=>'***********',
			  'db_name'=>'forum_consume',
			  'db_port'=>6027,
			  'username'=>'sandbox',
			  'password'=>'BjfcMlc',
			);
			var_dump($arr);
            $oDbManager = new dbManager($arr);
			$arrForumDescMap = array();
            foreach ($arrForumId as $forumId)
			{
		      $res = $oDbManager->excuteQuery($strSQL1.$forumId.$strSQL2);
              while($row = mysql_fetch_array($res))
			  {
                  $desc = $row['description'];
				  $arrForumDescMap[$forumId] = $desc;
              }
            }
			$oDbManager = null;
			$times = 0;
			foreach($arrForumDescMap as $key=>$value)
			{
			   if($times < 100){
			       $times++;	   
			   }else{
			       $times = 0;
			       sleep(1);
			   }
		       $ret = Tieba_Service::call("forum", "getForumAttr", array('forum_id'=>$key));
			   if($ret['errno'] != 0 || $ret == false)
			   {
			         crawlerLog::getInstance()->writeLog($key." :�ðɲ�ѯ����Ƭ��Ϣʧ��", 1);
		       }else
			   {
		           if(isset($ret['output']['card_p1']))
				   {
				       //��ȡ����ʽ��Ϣ
				       $jsonStyle = $ret['output']['card_p1']['style_name'];
				       $arrStyle = json_decode($jsonStyle, true);
					   if($arrStyle['slogan'] != $arrStyle['desc'])
					   {
                         crawlerLog::getInstance()->writeLog($forumId." ����Ҫ���и���", 0);	
                         continue;				   
					   }
				       //�����޸�
				       fwrite($fd_bak, $key."\t".$jsonStyle."\n");
				       $arrStyle['desc'] = mb_convert_encoding($value, 'UTF-8', 'GBK');
					   //���ð���ϸ����
					   $jsonRes = json_encode($arrStyle);
					   $input_arr = array(
							'forum_id' => intval($key),
    						'attr_name' => 'card_p1',
							'attr_value' => array(
												  'module_id' => 5,
												  'style_id' => 1,
												  'style_name' => json_encode($arrStyle),
												 ),
					    );
	 				    $res = Tieba_Service::call('forum', 'setForumAttr', $input_arr);
					    if($res['errno'] != 0 || $res === false)
						{
						   crawlerLog::getInstance()->writeLog($value." call forum-setForumStyle failed! ".serialize($res), 1);
			            }else
						{
						   crawlerLog::getInstance()->writeLog($value." call forum-setForumStyle success! ".$key, 0);
					    }
				    }
				    else
				    {
				       crawlerLog::getInstance()->writeLog($value." not set fcard ".$key, 1);
				    }
			 }
			}
		   fclose($fd);
		   fclose($fd_bak);
		}
		$endTime = time();
		echo "end cralwer, use ".($endTime-$startTime)."s\n";
		return;
	}
	public function __destruct(){
		unset($this->arrConnArr);
		unset($this->forumMap);
		$this->arrConnArr=null;
		$this->forumMap=null;
	}
}

/*
 * ���߼�
 */
{
	crawlerLog::getInstance ()->init (); //��ʼ����־�ļ�
	$fC = new updateStyleName ();
	$fC->init ("conf/db.conf");
	$fC->crawler();
}

