<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @version 1.1
 * 添加参与榜单明星吧
 */
class delActivityAction extends Util_Actionbase {

    const REDIS_ACTIVITY_STAR_FORUM_WORDLIST = 'tb_wordlist_redis_star_rank_list';
    const REDIS_ACTIVITY_KEY = 'star_activity';

    /**
     * init
     * @return boolean
     */
    function init() {
        self::setUiAttr('COMMIT_UI');
        return true;
    }

    /**
     * @return boolean
     */
    public function process(){
        $intActId = intval(Bingo_HTTP_Request::getNoXssSafe('activity_id', 0));

        // 校验吧id
        if ($intActId <= 0) {
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            return true;
        }

        $handleWordServer = Wordserver_Wordlist::factory();
        $strTableName = self::REDIS_ACTIVITY_STAR_FORUM_WORDLIST;
        $strKey = self::REDIS_ACTIVITY_KEY;

        // 添加白名单词表
        $ret = $handleWordServer->getValueByKeys(array($strKey), $strTableName);
        if (!isset($ret)
            || $ret['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('query wordlist key error. input: '. $strKey);
            $this->_jsonRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
            return false;
        }
        $arrItemInfo = Bingo_String::json2array($ret[$strKey]);
        if ($intActId == end(array_keys($arrItemInfo))) {
            $this->_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR, Bingo_Encode::convert('as you know，不得删除最后一条配置！', 'gbk', 'utf-8'));
            return false;
        }
        unset($arrItemInfo[$intActId]);

        // 添加白名单词表
        $arrInput = array(
            'table' => $strTableName,
            'key' => self::REDIS_ACTIVITY_KEY,
            'value' => Bingo_String::array2json($arrItemInfo),
            'replaced' => true,
        );

        $arrItemInfo = $handleWordServer->addKey($arrInput);
        if (!isset($arrItemInfo)
            || $arrItemInfo['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('add wordlist key error. input: ' . var_export($arrInput, true));
            $this->_jsonRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
            return false;
        }

        $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS);
        return true;
    }


    /**
     * @param $intErrno
     * @param $strErrmsg
     * @param array $arrData
     */
    private function _jsonRet($intErrno, $strErrmsg = '', $arrData = array()){
        $arrRet = array(
            'errno' => $intErrno,
            'errmsg' => empty($strErrmsg) ? Tieba_Error::getErrmsg($intErrno) : $strErrmsg,
            'data' => $arrData,
        );
        foreach($arrRet as $k=>$v){
            Bingo_Page::assign($k,$v);
        }

        Bingo_Page::setOnlyDataType("json");
        Bingo_Http_Response::contextType('application/json');
    }

}