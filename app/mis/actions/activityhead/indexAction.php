<?php
class indexAction extends Util_Actionbase{

	function init() {
            self::setUiAttr('BROWSE_UI');
            if (false === parent::init()) {
                if (0 === $this->_intErrorNo) {
                    $this->_intErrorNo  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                    $this->_strErrorMsg = 'init() error!';
                    Bingo_Log::warning($this->_strErrorMsg);
                }
            }
            return true;
	}
	
	 public function process() {
	 	 Bingo_Page::setTpl('activity_head.php');
	 }
}