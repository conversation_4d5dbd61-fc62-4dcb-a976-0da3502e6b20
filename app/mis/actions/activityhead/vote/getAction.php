<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file addAction.php
 * <AUTHOR>
 * @date 2014/06/30 11:53:19
 * @brief 
 *  
 **/


class getAction extends Util_Activityhead_BaseAction {
    protected function _input() {
        $this->_param['head_id'] = intval(Bingo_Http_Request::get('id', 0));
        if (empty($this->_param['head_id'])) {
            return false;
        }
        return true;
    }

    protected function _execute() {
        $arrInput = array(
            'head_id'=>$this->_param['head_id'],
            'src_format'=>'mis',
        );

        $arrOut = Tieba_Service::call('activityhead', 'getActHead', $arrInput);
        if ($arrOut['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal('get act head info failed.');
            $this->_setErr(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return false;
        }
        $act_head = $arrOut['data'];
        //var_dump($act_head);
        $this->_buildRes($act_head);
        return true;
    }

}



/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
