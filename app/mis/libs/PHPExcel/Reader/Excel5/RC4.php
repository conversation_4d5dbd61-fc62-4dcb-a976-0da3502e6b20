<?php
/**
 * PHPExcel
 *
 * Copyright (c) 2006 - 2014 PHPExcel
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
 *
 * @category   PHPExcel
 * @package    PHPExcel_Reader_Excel5
 * @copyright  Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)
 * @license    http://www.gnu.org/licenses/old-licenses/lgpl-2.1.txt	LGPL
 * @version    1.8.0, 2014-03-02
 */

/**
 * PHPExcel_Reader_Excel5_RC4
 *
 * @category	PHPExcel
 * @package		PHPExcel_Reader_Excel5
 * @copyright	Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)
 */
class PHPExcel_Reader_Excel5_RC4
{
	// Context
	var $s = array();
	var $i = 0;
	var $j = 0;

	/**
	 * RC4 stream decryption/encryption constrcutor
	 * 
	 * @param string $key Encryption key/passphrase
	 */
	public function __construct($key)
	{
		$len = strlen($key);

		for ($this->i = 0; $this->i < 256; $this->i++) {
			$this->s[$this->i] = $this->i;
		}

		$this->j = 0;
		for ($this->i = 0; $this->i < 256; $this->i++) {
			$this->j = ($this->j + $this->s[$this->i] + ord($key[$this->i % $len])) % 256;
			$t = $this->s[$this->i];
			$this->s[$this->i] = $this->s[$this->j];
			$this->s[$this->j] = $t;
		}
		$this->i = $this->j = 0;
	}

	/**
	 * Symmetric decryption/encryption function
	 * 
	 * @param string $data Data to encrypt/decrypt
	 * 
	 * @return string
	 */
	public function RC4($data)
	{
		$len = strlen($data);
		for ($c = 0; $c < $len; $c++) {
			$this->i = ($this->i + 1) % 256;
			$this->j = ($this->j + $this->s[$this->i]) % 256;
			$t = $this->s[$this->i];
			$this->s[$this->i] = $this->s[$this->j];
			$this->s[$this->j] = $t;

			$t = ($this->s[$this->i] + $this->s[$this->j]) % 256;

			$data[$c] = chr(ord($data[$c]) ^ $this->s[$t]);
		}
		return $data;
	}
}
