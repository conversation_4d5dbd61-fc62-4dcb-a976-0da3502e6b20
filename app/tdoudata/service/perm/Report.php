<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013:10:29 23:35:24
 * @version 
 * @structs & methods(copied from idl.)
*/

class Service_Perm_Report extends Service_Libs_Base{
    
    public static function getReportMenu($arrInput){
        if (!isset($arrInput['user_id']) ){
            Bingo_Log::warning ( "input params invalid. [" . serialize ( $arrInput ) . "]" );
            return Service_Libs_Base::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        
        $user_id = self::_getIntParams('user_id', $arrInput);
        
        if ( !Service_Perm_Permconf::checkAdmin( $user_id ) ) {
            Bingo_Log::warning( 'user not auth' );
            return Service_Libs_Base::_errRet( Tieba_Errcode::ERR_FORBID_OPERATION );
        }
        
        //所有核心报告菜单
       
        $all_service_type = array_keys(Service_Show_Conf::$service_report);
        
        $arrOut = Service_Show_Report::getReportByType(array('service_types' => $all_service_type));
        //Bingo_Log::warning('arrOut'.print_r($arrOut,1));
        if ($arrOut['errno']!== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('db fail.');
            return Service_Libs_Base::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL );
        }
        $retData = array();
        ksort($arrOut['data']);
        $retData[0] = array(
                'report_type' =>  1 ,
                'report_name' => 'T豆消费业务',
            
                );
        /*
        foreach ($arrOut['data'] as $service_type => $info) {
            $retData[0]['service_list'][] = array(
                    'service_type' => $service_type,
                    'service_name' => Service_Show_Conf::$service_report[$service_type]['title'],
                    'app_list' => $info,
                    );
        }*/
        
        foreach (Service_Show_Conf::$service_report as $service_type => $info) {
            $retData[0]['service_list'][] = array(
                    'service_type' => $service_type,
                    'service_name' => $info['title'],
                    'app_list' => $arrOut['data'][$service_type],
            );
        }
        
        

        
        return Service_Libs_Base::_errRet(Tieba_Errcode::ERR_SUCCESS,$retData);
        
    }
    
    
    
}
	
