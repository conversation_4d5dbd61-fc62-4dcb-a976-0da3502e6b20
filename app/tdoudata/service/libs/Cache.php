<?php
/**
 *  **/
class Service_Libs_Cache{

    protected static $_confPath = '';
    protected static $_confFile = 'CacheConf.php';

    const DAL_CACHE_TYPE = 'memcached';
    const EXPIRE_TIME = 3600;

    protected static $_objCache = null;

    protected static function _getCache () {
        if (null == self::$_objCache) {
            Bingo_Timer::start('cacheinit'); 
            self::$_objCache = new Bingo_Cache_Memcached('forum_common');
            Bingo_Timer::end('cacheinit');
        }
        return self::$_objCache;
    }

    public static function init () {
        self::_getCache();
        return true;
    }

    public static function get ($strKey, $boolUseCache = true) {
        if(!$boolUseCache){
            return null;
        }
        $objCache = self::_getCache();
        if (is_null ($objCache)) {
            Bingo_Log::warning('can not get object catch!');
            return null;
        }
        Bingo_Timer::start ('cache_get_'.$strKey);
        $res = $objCache ->get ($strKey);
        Bingo_Timer::end ('cache_get_'.$strKey);
        if (null === $res) {
            $strLog = sprintf ('not hit cache, key[%s]', $strKey);
            Bingo_Log::warning ($strLog, LOG_CACHE);
            return null;
        } else {
            $strLog = sprintf ('hit cache, key[%s] val[%s]', $strKey, $res);
            Bingo_Log::trace ($strLog, LOG_CACHE);
            return $res;
        }
    }

    public static function add ($strKey, $strVal,$expireTime=0) {
        $expireTime = intval($expireTime);
        Bingo_Timer::start ('cache_set_'.$strKey);
        $objCache = self::_getCache();
        if (is_null ($objCache)) {
            Bingo_Log::warning('can not get object catch!');
            return false;
        }
        if($expireTime<=0)
        {
            $expireTime = self::EXPIRE_TIME;
        }

        $res = $objCache->add($strKey, $strVal, $expireTime);
        Bingo_Timer::end ('cache_set_'.$strKey);
        if (0 === $res) {
            $strLog = sprintf ('add catch success, key[%s] val[%s]', $strKey, $strVal);
            Bingo_Log::trace($strLog, LOG_CACHE);
            return true;
        } else {
            $strLog = sprintf ('add catch fail, key[%s] val[%s]', $strKey, $strVal);
            Bingo_Log::warning($strLog, LOG_CACHE);
            return false;
        }
    }

    public static function del ($strKey) {
        $objCache = self::_getCache();
        if (is_null ($objCache)) {
            Bingo_Log::warning('can not get object catch!');
            return false;
        }

        $res = $objCache->remove($strKey);
        Bingo_Timer::start ('cache_del_'.$strKey);
        $res = self::_getCache() -> remove($strKey);
        Bingo_Timer::end ('cache_del_'.$strKey);
        if (0 === $res) {
            $strLog = sprintf ('del cache success, key[%s]', $strKey);
            Bingo_Log::trace ($strLog, LOG_CACHE);
            return true;
        } else {
            $strLog = sprintf ('del cache fail, key[%s]', $strKey);
            Bingo_Log::warning ($strLog, LOG_CACHE);
            return false;
        }
    }

}
/* vim: set noexpandtab ts=4 sw=4 sts=4 tw=100: */
