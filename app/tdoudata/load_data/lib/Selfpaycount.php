<?php


    class Lib_Selfaycount {

        public static function loadData($day_time){
            
  
            //支付会员
		    $sql = "select sum(db_money) total_db_money,count(user_id) num,count(distinct(user_id)) num_uid from dubi_order where from_unixtime(finish_time,\"%Y%m%d\") = \"$day_time\" and result_code = 10000 and packet_id !=100002";
            $dbOut = Lib_Dbop::getDbData($sql);
            
            
            $pay_data = $dbOut[0];
            print_r($pay_data);
           
            $sql = "insert into reportdata set create_date='$day_time' ,";
            
            
            /*
            foreach (Data_Conf::$pay_conf as $key=>$val){
                $sql .= " $key = '".$arrtmp[$val]. "' ,";
                
            }
            $sql = substr($sql, 0, -1);
            
            Lib_Dbop::DbQuery($sql);
            
            echo "$sql " . serialize($arrOut) ."\n";
            */
            
        }




    }



