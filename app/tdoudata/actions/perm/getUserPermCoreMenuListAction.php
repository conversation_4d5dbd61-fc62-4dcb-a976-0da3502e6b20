<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-03-07 10:04:11
 * @comment 模版接口
 * @version
 */
class getUserPermCoreMenuListAction extends Util_Base {

    public function _execute(){
    	try {
            //参数获取
    	    if($this->_arrUserInfo['is_login'] !== true ){
    	        throw new Util_Exception("user need login!",Tieba_Errcode::ERR_USER_NOT_LOGIN);
    	    }
    	    $this->_tbs_check = 0;
            $user_id = intval($this->_arrUserInfo['user_id']);
            $perm_user_name =  Bingo_Http_Request::getNoXssSafe('perm_user_name',0);
            $arrInput = array(
                    'user_id'  => $user_id, 
                    'perm_user_name' => $perm_user_name,

                    );
            $arrOut = Tieba_Service::call('tdoudata', 'getUserPermCoreMenuList', $arrInput, NULL, NULL, 'post', 'php', 'utf-8','local');
			
            
            //日志输出
            Bingo_Log::pushNotice("ispv",1);
            //$this->_arrUserInfo['portrait'] = Tieba_Ucrypt::encode($this->_arrUserInfo['user_id'], $this->_arrUserInfo['user_name']);
            //$data['scores'] = isset($this->_arrUserInfo['Parr_scores']) ? $this->_arrUserInfo['Parr_scores'] : array();
            //$data['user'] = $this->_arrUserInfo;
            //$data['tbs'] = Tieba_Tbs::gene($this->_arrUserInfo['is_login']);
            $data['menu_data'] = $arrOut['data'];


            //$this->_assginAll($data);
            //Bingo_Page::setTpl("home.php");
            $this->_jsonRet($arrOut['errno'],Tieba_Error::getUserMsg(Tieba_Errcode::ERR_SUCCESS),$data);

        }catch(Util_Exception $e){
            Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
            Bingo_Page::setTpl("index.php");
            //也可以跳转302
            //Tieba_Error::fetalError302();
            
        }
    }

}
?>