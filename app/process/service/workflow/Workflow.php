<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:06:09 12:00:27
 * @structs & methods(copied from idl.)
*/
define("MODULE","Process_service");
class Service_Workflow_Workflow {
    const SERVICE_NAME = "Service_Workflow_Workflow";
    public static $intTime = '';
    protected static $_arrConf = array();
    private static $_objRalMulti = '';
    private static $_strMultiKey = 'process_wf_multi';

    /**
     * @brief init
     * @return: true if success. false if fail.
    **/
    public static function init() {
        self::$intTime = time();
        if (self::$_arrConf == null) {
            self::$_arrConf = Bd_Conf::getConf("/app/process/service_process_workflow");
            if (false === self::$_arrConf) {
                Bingo_Log::warning("init get process workflow conf fail.");
                return false;
            }
        }
        return true; 
    }
    
    private static function _getMultiObj() {
        if (is_null(self::$_objRalMulti)) {
            self::$_objRalMulti = new Tieba_Multi(self::$_strMultiKey);
        }
        return self::$_objRalMulti;
    }

    public static function preCall($arrInput) {
        // pre-call hook
    }

    public static function postCall($arrInput) {
        // post-call hook
    }
    
    //get workFlow list
    public function getWorkFlowList($arrInput = array()) {
        //must change product
        $arrNeedField = array('product');
        $boolCheckRes = Util_Function::checkArrMustField($arrInput, $arrNeedField);
        if (false === $boolCheckRes) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!self::init()) {
            return false;
        }
        if (!isset($arrInput['flag'])) {
            $arrInput['flag'] = (int)self::$_arrConf['WORKFLOW_DEFAULT_FLAG'];
        }
        if (!isset($arrInput['pn'])) {
            $arrInput['pn'] = (int)self::$_arrConf['DEFAULT_PN_NUM'];
        }
        if (!isset($arrInput['per_num'])) {
            $arrInput['per_num'] = (int)self::$_arrConf['PER_PAGE_NUM'];
        }
        //get workFlow list from db
        $arrList = Dl_Dbprocess_Dbworkflow::getWorkFlowList($arrInput);
        if (false === $arrList || Tieba_Errcode::ERR_SUCCESS !== $arrList['errno']) {
            Bingo_Log::warning('call workflow list from db fail param:' . serialize($arrInput) . ' output:' . serialize($arrList));
            return Util_Function::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrList['data']);
    }
    
    public function addWorkFlow($arrInput = array()) {
        $arrNeedField = array('product', 'teamId', 'flowName', 'flowDesc', 'createUser', 'node_list',);
        $boolCheckRes = Util_Function::checkArrMustField($arrInput, $arrNeedField);
        if (false === $boolCheckRes) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        //weight need communicate to fe
        foreach ($arrInput['node_list'] as $arrVal) {
            if (!isset($arrVal['nodeId']) || !isset($arrVal['weight'])) {
                Bingo_Log::warning("node_list param invalid [" . serialize($arrInput) . "]");
                return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
        }
        if (!self::init()) {
            return false;
        }
        if (!isset($arrInput['flag'])) {
            $arrInput['flag'] = (int)self::$_arrConf['WORKFLOW_DEFAULT_FLAG'];
        }
        $arrInput['createTime'] = self::$intTime;
        //check same flowName
        $arrCheckInput = array(
            'cond' => array(
                'product=' => trim($arrInput['product']),
                'teamId='  => (int)$arrInput['teamId'],
                'flag='    => (int)$arrInput['flag'],
                'flowName=' => trim($arrInput['flowName']),
            ),
        );
        $arrCheckRes = Dl_Dbprocess_Dbworkflow::selectWorkFlowByCond($arrCheckInput);
        if (false === $arrCheckRes || Tieba_Errcode::ERR_SUCCESS !== $arrCheckRes['errno']) {
            Bingo_Log::warning('check workflow same from db fail param:' . serialize($arrCheckInput) . ' output:' . serialize($arrCheckRes));
            return Util_Function::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        if (!empty($arrCheckRes['data'])) {
            Bingo_Log::warning('has same flowName in your product param:' . serialize($arrCheckInput) . ' output:' . serialize($arrCheckRes));
            return Util_Function::errRet(Tieba_Errcode::ERR_SAME_WORKFLOW);
        }
        $arrAddRes = Dl_Dbprocess_Dbworkflow::addWorkFlow($arrInput);
        if (false === $arrAddRes || Tieba_Errcode::ERR_SUCCESS !== $arrAddRes['errno']) {
            Bingo_Log::warning('add workflow to db fail param:' . serialize($arrInput) . ' output:' . serialize($arrAddRes));
            return Util_Function::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
    }
    
    public function editWorkFlow($arrInput = array()) {
        $arrNeedField = array('flowId', 'product', 'teamId', 'updateUser',);
        $boolCheckRes = Util_Function::checkArrMustField($arrInput, $arrNeedField);
        if (false === $boolCheckRes) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!self::init()) {
            return false;
        }
        $intFlowId = (int)$arrInput['flowId'];
        $arrInput['flag'] = (int)self::$_arrConf['WORKFLOW_DEFAULT_FLAG'];
        $arrInput['del_flag'] = (int)self::$_arrConf['WORKFLOW_DEL_FLAG'];
        $arrInput['updateTime'] = (int)self::$intTime;
        if (isset($arrInput['flowName'])) {
            //check same flowName
            $arrCheckInput = array(
                'cond' => array(
                    'product=' => trim($arrInput['product']),
                    'teamId='  => (int)$arrInput['teamId'],
                    'flag='    => (int)$arrInput['flag'],
                    'flowName=' => trim($arrInput['flowName']),
                ),
            );
            $arrCheckRes = Dl_Dbprocess_Dbworkflow::selectWorkFlowByCond($arrCheckInput);
            if (false === $arrCheckRes || Tieba_Errcode::ERR_SUCCESS !== $arrCheckRes['errno']) {
                Bingo_Log::warning('check workflow same from db fail param:' . serialize($arrCheckInput) . ' output:' . serialize($arrCheckRes));
                return Util_Function::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            if (!empty($arrCheckRes['data']) && $intFlowId !== (int)$arrCheckRes['data'][0]['flowId']) {
                Bingo_Log::warning('has same flowName in your product param:' . serialize($arrCheckInput) . ' output:' . serialize($arrCheckRes));
                return Util_Function::errRet(Tieba_Errcode::ERR_SAME_WORKFLOW);
            }
        }
        $arrEditRes = Dl_Dbprocess_Dbworkflow::editWorkFlow($arrInput);
        if (false === $arrEditRes || Tieba_Errcode::ERR_SUCCESS !== $arrEditRes['errno']) {
            Bingo_Log::warning('edit workFlow info to db fail param:' . serialize($arrInput) . ' output:' . serialize($arrEditRes));
            return Util_Function::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
    }
    
    public function deleteWorkFlow($arrInput = array()) {
        $arrNeedField = array('flowId', 'updateUser');
        $boolCheckRes = Util_Function::checkArrMustField($arrInput, $arrNeedField);
        if (false === $boolCheckRes) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!self::init()) {
            return false;
        }
        $arrInput['updateTime'] = (int)self::$intTime;
        $arrInput['flag']       = (int)self::$_arrConf['WORKFLOW_DEFAULT_FLAG'];
        $arrInput['del_flag']   = (int)self::$_arrConf['WORKFLOW_DEL_FLAG'];
        $arrDelRes = Dl_Dbprocess_Dbworkflow::deleteWorkFlow($arrInput);
        if (false === $arrDelRes || Tieba_Errcode::ERR_SUCCESS !== $arrDelRes['errno']) {
            Bingo_Log::warning('edit workFlow info to db fail param:' . serialize($arrInput) . ' output:' . serialize($arrDelRes));
            return Util_Function::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    public function updateWorkFlow($arrInput = array()) {
        $arrNeedField = array('field', 'cond');
        $boolCheckRes = Util_Function::checkArrMustField($arrInput, $arrNeedField);
        if (false === $boolCheckRes) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrUpdateInput = array(
            'field' => Util_Db::formatDbInput($arrInput['field']),
            'cond'  => Util_Db::formatDbInput($arrInput['cond']),
        );
        $arrUpdateRes = Dl_Dbprocess_Dbworkflow::updateWorkFlowCond($arrUpdateInput);
        if (false === $arrUpdateRes || Tieba_Errcode::ERR_SUCCESS !== $arrUpdateRes['errno']) {
            Bingo_Log::warning('update workFlow info to db fail param:' . serialize($arrUpdateInput) . ' output:' . serialize($arrUpdateRes));
            return Util_Function::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
    }
  
    public function getWorkFlowByFid($arrInput = array()) {
        $arrNeedField = array('flowId',);
        $boolCheckRes = Util_Function::checkArrMustField($arrInput, $arrNeedField);
        if (false === $boolCheckRes) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrNeedField) . "]");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!self::init()) {
            return false;
        }
        $arrInput['status'] = self::$_arrConf['WORKFLOW_DEFAULT_FLAG'];
        //get workflow info 
        $arrSelectInput = array(
            'cond' => array(
                'flowId=' => (int)$arrInput['flowId'],
                'flag='   => (int)self::$_arrConf['WORKFLOW_DEFAULT_FLAG'],
            ),
        );
        $arrSelectRes = Dl_Dbprocess_Dbworkflow::selectWorkFlowByCond($arrSelectInput);
        if (false === $arrSelectRes || Tieba_Errcode::ERR_SUCCESS !== $arrSelectRes['errno']) {
            Bingo_Log::warning('select workFlow info from db fail param:' . serialize($arrSelectInput) . ' output:' . serialize($arrSelectRes));
            return Util_Function::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        if (empty($arrSelectRes['data'])) {
            Bingo_Log::warning('there has no this workFlow from db fail param:' . serialize($arrSelectInput) . ' output:' . serialize($arrSelectRes));
            return Util_Function::errRet(Tieba_Errcode::NOT_EXIST_THIS_WORKFLOW);
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrSelectRes['data'][0]);
    }
    //get workNode by flowId
    public function getWorkNodeByFlowId($arrInput = array()) {
        $arrNeedField = array('flowId',);
        $boolCheckRes = Util_Function::checkArrMustField($arrInput, $arrNeedField);
        if (false === $boolCheckRes) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!self::init()) {
            return false;
        }
        $arrSelectInput = array(
            'cond'  => array(
                'flowId=' => (int)$arrInput['flowId'],
                'flag='   => (int)self::$_arrConf['WORKFLOW_DEFAULT_FLAG'],
            ),
            'append' => array(
                "order by weight asc"
            ),
        );
        $arrSelectRes = Dl_Dbprocess_Dbworkflow::selectWorkFlowNodeByCond($arrSelectInput);
        if (false === $arrSelectRes || Tieba_Errcode::ERR_SUCCESS !== $arrSelectRes['errno']) {
            Bingo_Log::warning('select workFlowNode list from db fail param:' . serialize($arrSelectInput) . ' output:' . serialize($arrSelectRes));
            return Util_Function::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        if (empty($arrSelectRes['data'])) {
            Bingo_Log::warning('this workflow has no workFlowNode param:' . serialize($arrSelectInput) . ' output:' . serialize($arrSelectRes));
            return Util_Function::errRet(Tieba_Errcode::NOT_EXIST_THIS_WORKFLOWNODE);
        }
        $arrNodeIds = $arrNodeList = array();
        foreach ($arrSelectRes['data'] as $arrNode) {
            $arrNodeList[$arrNode['nodeId']] = $arrNode;
            $arrNodeIds['node_ids'][] = $arrNode['nodeId'];
        }
        $strQuery = implode(',', $arrNodeIds['node_ids']);
        $arrNodeCond = array(
            'cond' => array(
                'nodeId' => $arrNodeIds['node_ids'],
            ),
            'append' => array(
                " order by field(nodeId, $strQuery) "
            ),
        );
        $arrNodeRes = Service_Worknode_Worknode::getWorkNodeByCond($arrNodeCond);
        if (false === $arrNodeRes || Tieba_Errcode::ERR_SUCCESS !== $arrNodeRes['errno']) {
            Bingo_Log::warning('select workFlowNode detail info from db fail param:' . serialize($arrNodeCond) . ' output:' . serialize($arrNodeRes));
            return Util_Function::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        //get node comp
        $arrCompInput = array(
            'cond' => array(
                'nodeId' => $arrNodeIds['node_ids'],
            ),
        );
        $arrCompRes = Dl_Dbprocess_Dbworknode::getComponentsByCond($arrCompInput);
        if (false === $arrCompRes || Tieba_Errcode::ERR_SUCCESS !== $arrCompRes['errno']) {
            Bingo_Log::warning('call getComponentsByCond info from db fail param:' . serialize($arrCompInput) . ' output:' . serialize($arrCompRes));
            return Util_Function::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrCompList = array();
        foreach ($arrCompRes['data'] as $arrCompInfo) {
            $arrCompList[$arrCompInfo['nodeId']][] = $arrCompInfo['componentId'];
        }
        //make output format data
        foreach ($arrNodeRes['data'] as &$arrNodeDetail) {
            $arrNodeDetail['flowNodeId'] = (int)$arrInput['flowNodeId'];
            $arrNodeDetail['flowId']     = (int)$arrInput['flowId'];
            $arrNodeDetail['weight']     = (int)$arrNodeList[$arrNodeDetail['nodeId']]['weight'];
            if (isset($arrCompList[$arrNodeDetail['nodeId']])) {
                $arrNodeDetail['componentIds'] = implode(',', $arrCompList[$arrNodeDetail['nodeId']]);
            } else {
                $arrNodeDetail['componentIds'] = '';
            }
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrNodeRes['data']);
    }
    
    public function geAllWorkFlow($arrInput = array()) {
        $arrNeedField = array('product', 'teamId');
        $boolCheckRes = Util_Function::checkArrMustField($arrInput, $arrNeedField);
        if (false === $boolCheckRes) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!self::init()) {
            return false;
        }
        if (!isset($arrInput['flag'])) {
            $arrInput['flag'] = (int)self::$_arrConf['WORKFLOW_DEFAULT_FLAG'];
        }
        $arrSelectInput = array(
            'cond'   => array(
                'product=' => trim($arrInput['product']),
                'teamId='  => (int)$arrInput['teamId'],
                'flag='    => $arrInput['flag'],
            ),
            'append' => array(
                "order by createTime desc",
            ),
        );
        $arrSelectRes = Dl_Dbprocess_Dbworkflow::selectWorkFlowByCond($arrSelectInput);
        if (false === $arrSelectRes || Tieba_Errcode::ERR_SUCCESS !== $arrSelectRes['errno']) {
            Bingo_Log::warning('geAllWorkFlow info from db fail param:' . serialize($arrSelectInput) . ' output:' . serialize($arrSelectRes));
            return Util_Function::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrSelectRes['data']);
    }
}
