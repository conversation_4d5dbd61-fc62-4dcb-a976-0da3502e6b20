<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:07:21
 * @structs & methods(copied from idl.)
*/
define("MODULE", "Process_dl");
class Dl_Dbprocess_Dbteam {
    const TEAM_TABLE         = 'team';
    const TEAM_MEMBER_TABLE  = 'teamMember';
    const USER_ROLE_TABLE    = 'userRole';
    const LEADER_MEMBER_TYPE = 0;
    const TEAM_ADMIN_ROLE_ID = 2;
    const DEFAULT_PRODUCT    = 'tieba';

    public function addTeam($arrInput = array()) {
        $objDb = Util_Db::getDB();
        $objDb->startTransaction();
        $arrParams = array(
            'teamName'      => trim($arrInput['teamName']),
            'teamLeader'    => trim($arrInput['teamLeader']),
            'teamAdmin'     => trim($arrInput['teamAdmin']),
            'teamMailGroup' => trim($arrInput['teamMailGroup']),
            'teamDescrip'   => trim($arrInput['teamDescrip']),
            'product'       => trim($arrInput['product']),
        );
        $arrAddInput = array(
            'table' => self::TEAM_TABLE,
            'field' => $arrParams,
            'db'    => $objDb,
        );
        $arrAddRes = Util_Db::insert($arrAddInput);
        if (false === $arrAddRes['errno'] && Tieba_Errcode::ERR_SUCCESS !== $arrAddRes['errno']) {
            $arrRollRes = $objDb->rollback();
            if (false === $arrRollRes) {
                Bingo_Log::warning("add team fail param:" . serialize($arrInput) . 'output:' . serialize($arrRollRes));
            }
            return $arrAddRes;
        }
        $intTeamId = (int)$arrAddRes['data'];
        $arrTeamInput = array(
            'table' => self::TEAM_MEMBER_TABLE,
            'field' => array(
                'teamId'         => $intTeamId,
                'teamMemberName' => trim($arrInput['teamLeader']),
                'teamMemberType' => self::LEADER_MEMBER_TYPE,
            ),
            'db'    => $objDb,
        );
        $arrMemberRes = Util_Db::insert($arrTeamInput);
        if (false === $arrMemberRes['errno'] && Tieba_Errcode::ERR_SUCCESS !== $arrMemberRes['errno']) {
            $arrRollRes = $objDb->rollback();
            if (false === $arrRollRes) {
                Bingo_Log::warning("add team member fail param:" . serialize($arrTeamInput) . 'output:' . serialize($arrMemberRes));
            }
            return $arrMemberRes;
        }
        $arrRoleParam = self::_getRoleInput($arrInput, $intTeamId);
        $arrRoleInput = array(
            'table' => self::USER_ROLE_TABLE,
            'field' => $arrRoleParam['field'],
            'data'  => $arrRoleParam['data'],
        );
        $arrRoleRes = Util_Db::multiInsert($arrRoleInput);
        if (false === $arrRoleRes['errno'] && Tieba_Errcode::ERR_SUCCESS !== $arrRoleRes['errno']) {
            $arrRollRes = $objDb->rollback();
            if (false === $arrRollRes) {
                Bingo_Log::warning("add team member role fail param:" . serialize($arrRoleInput) . 'output:' . serialize($arrRoleRes));
            }
            return $arrRoleRes;
        }
        if (false === $objDb->commit()) {
            Bingo_Log::fatal("add team commit fail param:" . serialize($arrInput));
            $arrRollRes = $objDb->rollback();
            if (false === $arrRollRes) {
                Bingo_Log::warning("rollback add team fail param:" . serialize($arrInput) . 'output:' . serialize($arrRollRes));
            }
            return $arrRollRes;
        }
        return $arrRoleRes;
    }
    
    private function _getRoleInput($arrInput = array(), $intTeamId = null) {
        $intTime = time();
        return array(
            'field' => array('userName', 'roleId', 'product', 'teamId', 'createUser', 'updateUser', 'createTime', 'updateTime',),
            'data'  => array(
                array(
                    trim($arrInput['teamAdmin']),
                    self::TEAM_ADMIN_ROLE_ID,
                    self::DEFAULT_PRODUCT,
                    $intTeamId,
                    trim($arrInput['createUser']),
                    trim($arrInput['createUser']),
                    $intTime,
                    $intTime,
                ),
                array(
                    trim($arrInput['teamLeader']),
                    self::TEAM_ADMIN_ROLE_ID,
                    self::DEFAULT_PRODUCT,
                    $intTeamId,
                    trim($arrInput['createUser']),
                    trim($arrInput['createUser']),
                    $intTime,
                    $intTime,
                ),
            ),
        );
    }

    public function deleteTeam($arrInput = array()) {
		$arrDelInput = array(
			'table' => self::TEAM_TABLE,
			'cond'  => $arrInput['cond'],
		);
		$arrDelRes = Util_Db::delete($arrDelInput);
		return $arrDelRes;
	}
	
	public function getTeamList($arrInput = array()) {
	    $arrGetInput = array(
	        'table' => self::TEAM_TABLE,
	        'field' => $arrInput['field'],
	        'cond'  => $arrInput['cond'],
		);
	    $arrGetRes = Util_Db::select($arrGetInput);
	    return $arrGetRes;
	}
	
	public function updateTeam($arrInput = array()) {
	    $arrUpdateInput = array(
	        'table' => self::TEAM_TABLE,
	        'field' => $arrInput['field'],
	        'cond'  => $arrInput['cond'],
		);
	    $arrUpdateRes = Util_Db::update($arrUpdateInput);
	    return $arrUpdateRes;
	}
	
	public function selectTeamByCond($arrInput = array()) {
	    $arrNeedField = array('cond',);
	    $boolCheckRes = Util_Function::checkArrMustField($arrInput,$arrNeedField);
	    if (false === $boolCheckRes) {
	        Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . "]");
	        return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
	    }
	    if (!isset($arrInput['field'])) {
	        $arrInput['field'] = array('*');
	    }
	    $selectInput = array(
	        'table' => self::TEAM_TABLE,
	        'field' => $arrInput['field'],
	        'cond'  => $arrInput['cond'],
	    );
	    if (isset($arrInput['append'])) {
	        $selectInput['append'] = $arrInput['append'];
	    }
	    $arrSelectRes = Util_Db::select($selectInput);
	    return $arrSelectRes;
	}
	
	//Team member
	public function addTeamMember($arrInput = array()) {
	    $arrAddInput = array(
	        'table' => self::TEAM_MEMBER_TABLE,
	        'field' => $arrInput,
	    );
	    $arrAddRes = Util_Db::insert($arrAddInput);
	    return $arrAddRes;
	}
	
	public function deleteTeamMember($arrInput = array()) {
	    $arrDelInput = array(
	        'table' => self::TEAM_MEMBER_TABLE,
	        'cond'  => $arrInput['cond'],
		);
	    $arrDelRes = Util_Db::delete($arrDelInput);
	    return $arrDelRes;
	}
	
	public function getTeamMember($arrInput = array()) {
	    $arrGetInput = array(
	        'table'  => self::TEAM_MEMBER_TABLE,
	        'field'  => $arrInput['field'],
	        'cond'   => $arrInput['cond'],
	    );
	    if(isset($arrInput['append'])){
	        $arrGetInput['append'] = $arrInput['append'];
	    }
		$arrGetRes = Util_Db::select($arrGetInput);
	    return $arrGetRes;
	}
	
	public function updateTeamMember($arrInput = array()) {
	    $arrUpdateInput = array(
	        'table' => self::TEAM_MEMBER_TABLE,
	        'field' => $arrInput['field'],
	        'cond'  => $arrInput['cond'],
		);
	    $arrUpdateRes = Util_Db::update($arrUpdateInput);
	    return $arrUpdateRes;
	}
}
