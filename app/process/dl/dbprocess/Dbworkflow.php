<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:06:09 12:00:27
 * @structs & methods(copied from idl.)
*/
define("MODULE", "Process_dl");
class Dl_Dbprocess_Dbworkflow {
    const WORK_FLOW_TABLE         = 'workFlow';
    const WORK_FLOW_NODE_TABLE    = 'workFlowNode';
    protected static $_arrConf    = null;
    private static $_arrNodeField = array(
        'flowId', 'nodeId', 'flag', 'weight', 'createUser', 'createTime', 'updateUser', 'updateTime',
    );
    
    public function init() {
        if (self::$_arrConf == null) {
            self::$_arrConf = Bd_Conf::getConf("/app/process/service_process_workflow");
            if (self::$_arrConf == false) {
                Bingo_Log::warning("init service_process_workflow conf fail on db file");
                return false;
            }
        }
        return true; 
    }
    
    public function selectWorkFlowByCond($arrInput = array()) {
        $arrNeedField = array('cond',);
        $bolCheck = Util_Function::checkArrMustField($arrInput, $arrNeedField);
        if (false === $bolCheck) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!isset($arrInput['field'])) {
            $arrInput['field'] = array('*');
        }
        $selectInput = array(
            'table' => self::WORK_FLOW_TABLE,
            'field' => $arrInput['field'],
            'cond'  => $arrInput['cond'],
        );
        if (isset($arrInput['append'])) {
            $selectInput['append'] = $arrInput['append'];
        }
        $arrSelectRes = Util_Db::select($selectInput);
        return $arrSelectRes;
    }
    
    public function selectWorkFlowNodeByCond($arrInput = array()) {
        $arrNeedField = array('cond',);
        $bolCheck = Util_Function::checkArrMustField($arrInput, $arrNeedField);
        if (false === $bolCheck) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!isset($arrInput['field'])) {
            $arrInput['field'] = array('*');
        }
        $selectInput = array(
            'table' => self::WORK_FLOW_NODE_TABLE,
            'field' => $arrInput['field'],
            'cond'  => $arrInput['cond'],
        );
        if (isset($arrInput['append'])) {
            $selectInput['append'] = $arrInput['append'];
        }
        $arrSelectRes = Util_Db::select($selectInput);
        return $arrSelectRes;
    }
    
    public function updateWorkFlowCond($arrInput = array()) {
        $arrNeedField = array('field', 'cond');
        $bolCheck = Util_Function::checkArrMustField($arrInput, $arrNeedField);
        if (false === $bolCheck) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrUpdateInput = array(
            'table' => self::WORK_FLOW_TABLE,
            'field' => $arrInput['field'],
            'cond'  => $arrInput['cond'],
        );
        $arrUpdateRes = Util_Db::update($arrUpdateInput);
        return $arrUpdateRes;
    }
    
    public function addWorkFlow($arrInput = array()) {
        $objDb = Util_Db::getDB();
        $objDb->startTransaction();
        $arrFields = array(
            'product'    => trim($arrInput['product']),
            'teamId'     => (int)$arrInput['teamId'],
            'flag'       => (int)$arrInput['flag'],
            'flowName'   => trim($arrInput['flowName']),
            'flowDesc'   => trim($arrInput['flowDesc']),
            'createUser' => trim($arrInput['createUser']),
            'updateUser' => trim($arrInput['createUser']),
            'createTime' => (int)$arrInput['createTime'],
            'updateTime' => (int)$arrInput['createTime'],
        );
        $arrAddInput = array(
            'table' => self::WORK_FLOW_TABLE,
            'field' => $arrFields,
            'db'    => $objDb,
        );
        $arrInsertRes = Util_Db::insert($arrAddInput);
        if (false === $arrInsertRes['errno'] && Tieba_Errcode::ERR_SUCCESS !== $arrInsertRes['errno']) {
            $arrRollRes = $objDb->rollback();
            if (false === $arrRollRes) {
                Bingo_Log::warning("add workflow fail param:" . serialize($arrInput) . 'output:' . serialize($arrInsertRes));
            }
            return $arrNodeRes;
        }
        $intFlowId = (int)$arrInsertRes['data'];
        $arrNodeData = self::_getWorkFlowNodeInput($intFlowId, $arrInput);
        $arrNodeInput = array(
            'table' => self::WORK_FLOW_NODE_TABLE,
            'field' => $arrNodeData['field'],
            'data'  => $arrNodeData['data'],
            'db'    => $objDb,
        );
        $arrNodeRes = Util_Db::replace($arrNodeInput);        
        if (false === $arrNodeRes['errno'] && Tieba_Errcode::ERR_SUCCESS !== $arrNodeRes['errno']) {
            $arrRollRes = $objDb->rollback();
            if (false === $arrRollRes) {
                Bingo_Log::warning("rollback add workflow fail param:" . serialize($arrInput) . 'output:' . serialize($arrRollRes));
            }
            return $arrNodeRes;
        }
        if (false === $objDb->commit()) {
            Bingo_Log::fatal("add workflow fail param:" . serialize($arrInput) . 'output:' . serialize($arrNodeRes));
            $arrRollRes = $objDb->rollback();
            if (false === $arrRollRes) {
                Bingo_Log::warning("rollback add workflow fail param:" . serialize($arrInput) . 'output:' . serialize($arrRollRes));
            }
            return $arrNodeRes;
        }
        return $arrNodeRes;
    }
    
    public function getWorkFlowList($arrInput = array()) {
        $arrNeedField = array('product',);
        $bolCheck = Util_Function::checkArrMustField($arrInput, $arrNeedField);
        if (false === $bolCheck) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrQuery = $arrCond = array();
        $arrSql = self::_getWorkFlowSql($arrInput);
        $arrQuery = array(
            'cond'   => $arrSql['cond'],
            'append' => $arrSql['append'],
        );
        $arrList = self::selectWorkFlowByCond($arrQuery);
        if (false === $arrList || Tieba_Errcode::ERR_SUCCESS !== $arrList['errno']) {
            Bingo_Log::warning('get workflow list exec query fail param:' . serialize($arrQuery) . ' output:' . serialize($arrList));
            return $arrList;
        }
        $arrSql = self::_getWorkFlowSql($arrInput, 'total');
        $arrTotalQuery = array(
            'field'  => $arrSql['field'],
            'cond'   => $arrSql['cond'],
        );
        $arrTotal = self::selectWorkFlowByCond($arrTotalQuery);
        if (false === $arrTotal || Tieba_Errcode::ERR_SUCCESS !== $arrTotal['errno']) {
            Bingo_Log::warning('get workflow total num exec query fail param:' . serialize($arrTotalQuery) . ' output:' . serialize($arrTotal));
            return $arrList;
        }
        $intTotalNum = count($arrTotal['data']);
        $intPerNum = (int)$arrInput['per_num']; 
        $arrOut = array(
            'errno' => Tieba_Errcode::ERR_SUCCESS,
            'data'  => array(
                'list'       => $arrList['data'],
                'total'      => $intTotalNum,
                'current_pn' => $arrInput['pn'],
                'total_pn'   => ceil($intTotalNum/$intPerNum),
            ),
        );
        return $arrOut;
    }
    
    private function _getWorkFlowSql($arrInput = array(), $type = 'list') {
        $arrOut = array();
        $arrCond = array(
            'product=' => trim($arrInput['product']),
        );
        if (isset($arrInput['teamId'])) {
            $arrCond['teamId='] = (int)$arrInput['teamId'];
        }
        if (isset($arrInput['flag'])) {
            $arrCond['flag='] = (int)$arrInput['flag'];
        }
        //get my create workFLow
        if (isset($arrInput['createUser'])) {
            $arrCond['createUser='] = trim($arrInput['createUser']);
        }
        $arrOut['cond'] = $arrCond;
        if ('list' === $type) {
            $intPn = (int)$arrInput['pn'];
            $intPerNum = (int)$arrInput['per_num'];
            $intOffset = ($intPn - 1) * $intPerNum;
            $arrOut['append'] = array(
                "order by createTime desc",
                "limit $intOffset, $intPerNum",
            );
        } else if ('total' === $type) {
            $arrOut['field'] = array('flowId');
        }
        return $arrOut;
    }
    
    private function _getWorkFlowNodeInput($intFlowId = null, $arrInput = array()) {
        $arrNodeData = array();
        foreach ($arrInput['node_list'] as $arrNode) {
            $arrNodeData[] = array(
                $intFlowId,
                (int)$arrNode['nodeId'],
                (int)$arrInput['flag'],
                (int)$arrNode['weight'],
                trim($arrInput['createUser']),
                (int)$arrInput['createTime'],
                trim($arrInput['createUser']),
                (int)$arrInput['createTime'],
            );
        }
        return $arrOutput = array(
            'field' => self::$_arrNodeField,
            'data'  => $arrNodeData,
        );
    }
    //need to check db handle
    public function editWorkFlow($arrInput = array()) {
        $objDb = Util_Db::getDB();
        $objDb->startTransaction();
        $intFlowId = (int)$arrInput['flowId'];
        $arrNode = array();
        $boolTrans = false;
        if (isset($arrInput['node_list'])) {
            //need Trans
            $boolTrans = true;
            $arrNodeDetail = self::_getPreNodeData($arrInput);
            $arrMakeRes = self::_makeFlowNodeData($arrNodeDetail, $arrInput);
        }
        $arrFlowUpdate = self::_makeFlowUpdateData($arrInput);
        $arrFlowUpdate['db'] = $objDb;
        $arrEditRes = Util_Db::update($arrFlowUpdate);
        if (false === $arrEditRes || Tieba_Errcode::ERR_SUCCESS !== $arrEditRes['errno']) {
            Bingo_Log::fatal("edit workflow fail param:" . serialize($arrInput) . 'output:' . serialize($arrEditRes));
            $arrRollRes = $objDb->rollback();
            if (false === $arrRollRes) {
                Bingo_Log::warning("rollback edit workflow fail param:" . serialize($arrInput) . 'output:' . serialize($arrEditRes));
            }
            return $arrRollRes;
        }
        if ($boolTrans) {
            //del pre record
            $arrDelInput = array(
                'table' => self::WORK_FLOW_NODE_TABLE,
                'cond'  => array(
                    'flowId=' => $intFlowId,
                    'flag='   => (int)$arrInput['flag'],
                ),
                'db' => $objDb,
            );
            $arrDelRes = Util_Db::delete($arrDelInput);
            if (false === $arrDelRes || Tieba_Errcode::ERR_SUCCESS !== $arrDelRes['errno']) {
                Bingo_Log::fatal("del workflow worknode fail param:" . serialize($arrInput) . 'output:' . serialize($arrEditRes));
                $arrRollRes = $objDb->rollback();
                if (false === $arrRollRes) {
                    Bingo_Log::warning("rollback del workflow worknode fail param:" . serialize($arrInput) . 'output:' . serialize($arrEditRes));
                }
                return $arrRollRes;
            }
            foreach ($arrMakeRes as $arrDetail) {
                $arrReplaceData[] = array_values($arrDetail);
            }
            $arrReplaceInput = array(
                'table' => self::WORK_FLOW_NODE_TABLE,
                'field' => array_unshift(self::$_arrNodeField, 'flowNodeId'),
                'data'  => $arrReplaceData,
                'db'    => $objDb,
            );
            $arrReplaceRes = Util_Db::replace($arrReplaceInput);
            if (false === $arrReplaceRes || Tieba_Errcode::ERR_SUCCESS !== $arrReplaceRes['errno']) {
                Bingo_Log::fatal("replace workflow worknode fail param:" . serialize($arrInput) . 'output:' . serialize($arrReplaceRes));
                $arrRollRes = $objDb->rollback();
                if (false === $arrRollRes) {
                    Bingo_Log::warning("rollback replace workflow worknode fail param:" . serialize($arrInput) . 'output:' . serialize($arrReplaceRes));
                }
                return $arrRollRes;
            }
        }
        if (false === $objDb->commit()) {
            Bingo_Log::fatal("edit workflow fail param:" . serialize($arrInput) . 'output:' . serialize($arrCommitRes));
            $arrRollRes = $objDb->rollback();
            if (false === $arrRollRes) {
                Bingo_Log::warning("rollback edit workflow fail param:" . serialize($arrInput) . 'output:' . serialize($arrRollRes));
            }
            return $arrRollRes;
        }
        return $arrEditRes;
    }
    
    private function _makeFlowUpdateData($arrInput = array()) {
        $arrFlowInput = $arrFlowUpdate = array();
        $arrFlowInput['field'] = array(
            'updateUser=' => trim($arrInput['updateUser']),
            'updateTime=' => (int)$arrInput['updateTime'],
        );
        if (isset($arrInput['flowName'])) {
            $arrFlowInput['field']["flowName="] = trim($arrInput['flowName']);
        }
        if (isset($arrInput['flowDesc'])) {
            $arrFlowInput['field']["flowDesc="] = trim($arrInput['flowDesc']);
        }
        $arrFlowInput['cond'] = array(
            "flowId=" => (int)$arrInput['flowId'],
        );
        $arrFlowUpdate = array(
            'table' => self::WORK_FLOW_TABLE,
            'field' => $arrFlowInput['field'],
            'cond'  => $arrFlowInput['cond'],
        );
        return $arrFlowUpdate;
    }

    private function _getPreNodeData($arrInput = array()) {
        foreach ($arrInput['node_list'] as $arrNode) {
                //record id
                if (isset($arrNode['flowNodeId'])) {
                    $arrFlowNodeId[] = $arrNode['flowNodeId'];
                }
            }
            $arrNodeInput = array(
                'cond' => array(
                    'flowNodeId' => $arrFlowNodeId,
                    'flag='      => (int)$arrInput['flag'],
                ),
            );
            $arrNodeList = self::selectWorkFlowNodeByCond($arrNodeInput);
            if (false === $arrNodeList || Tieba_Errcode::ERR_SUCCESS !== $arrNodeList['errno']) {
                Bingo_Log::warning('get workflownode from db fail param:' . serialize($arrNodeInput) . ' output:' . serialize($arrNodeList));
                return $arrNodeList;
            }
            $arrNodeDetail = array();
            foreach ($arrNodeList['data'] as $arrNodeInfo) {
                $arrNodeInfo['updateUser'] = trim($arrInput['updateUser']);
                $arrNodeInfo['updateTime'] = (int)$arrInput['updateTime'];
                $arrNodeDetail[$arrNodeInfo['flowNodeId']] = $arrNodeInfo;
            }
            return $arrNodeDetail;
    }

    private function _makeFlowNodeData($arrPreData = array(), $arrInput = array()) {
        $intWeight = 1;
        $intFlowId = (int)$arrInput['flowId'];
        $strProduct = trim($arrInput['product']);
        $intFlag = (int)$arrInput['flag'];
        $strUname = trim($arrInput['updateUser']);
        $intTime = (int)$arrInput['updateTime'];
        $arrFlowNode = $arrOutput = array();
        foreach ($arrInput['node_list'] as $arrNode) {
            $arrOutput[] = array(
                'flowNodeId' => isset($arrNode['flowNodeId']) ? (int)$arrNode['flowNodeId'] : '',
                'flowId'     => $intFlowId,
                'nodeId'     => (int)$arrNode['nodeId'],
                'flag'       => $intFlag,
                'weight'     => $intWeight,
                'createUser' => isset($arrNode['flowNodeId']) ? $arrPreData[$arrNode['flowNodeId']]['createUser'] : $strUname,
                'createTime' => isset($arrNode['flowNodeId']) ? $arrPreData[$arrNode['flowNodeId']]['createTime'] : $intTime,
                'updateUser' => isset($arrNode['flowNodeId']) ? $arrPreData[$arrNode['flowNodeId']]['updateUser'] : $strUname,
                'updateTime' => isset($arrNode['flowNodeId']) ? $arrPreData[$arrNode['flowNodeId']]['updateTime'] : $intTime,
            );
            $intWeight++;
        }
        return $arrOutput;
    }
    
    public function deleteWorkFlow($arrInput = array()) {
        $objDb = Util_Db::getDB();
        $objDb->startTransaction();
        $arrUpdateInput = array(
            'table' => self::WORK_FLOW_TABLE,
            'field' => array(
                'flag='       => (int)$arrInput['del_flag'],
                'updateUser=' => trim($arrInput['updateUser']),
                'updateTime=' => (int)$arrInput['updateTime'],
            ),
            'cond' => array(
                'flowId=' => (int)$arrInput['flowId'],
            ),
            'db' => $objDb,
        );
        $arrUpdateRes = Util_Db::update($arrUpdateInput);
        $arrNodeInput = array(
            'table' => self::WORK_FLOW_NODE_TABLE,
            'field' => array(
                'flag='       => (int)$arrInput['del_flag'],
                'updateUser=' => trim($arrInput['updateUser']),
                'updateTime=' => (int)$arrInput['updateTime'],
            ),
            'cond'  => array(
                'flowId=' => (int)$arrInput['flowId'],
                'flag='   => (int)$arrInput['flag']
            ),
            'db' => $objDb,
        );
        $arrNodeRes = Util_Db::update($arrNodeInput);        
        if (Tieba_Errcode::ERR_SUCCESS === $arrUpdateRes['errno'] && Tieba_Errcode::ERR_SUCCESS === $arrNodeRes['errno']) {
            if (false === $objDb->commit()) {
                Bingo_Log::fatal("del workflow fail param:" . serialize($arrInput) . 'output:' . serialize($arrNodeRes));
                $arrRollRes = $objDb->rollback();
                if (false === $arrRollRes) {
                    Bingo_Log::warning("rollback del workflow fail param:" . serialize($arrInput) . 'output:' . serialize($arrRollRes));
                }
                return $arrNodeRes;
            }
        } else {
            $arrRollRes = $objDb->rollback();
            if (false === $arrRollRes) {
                Bingo_Log::warning("rollback del workflow fail param:" . serialize($arrInput) . 'output:' . serialize($arrRollRes));
            }
            return $arrNodeRes;
        }
        return $arrNodeRes;
    }
}

