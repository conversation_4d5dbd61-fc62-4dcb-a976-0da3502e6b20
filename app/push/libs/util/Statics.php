<?php

/**
 * @autor: <PERSON><PERSON><PERSON><PERSON>
 * Date: 16/1/24
 * Time: 上午11:50
 * ping站长平台失败率统计
 */
class Libs_Util_Statics {

    public static $natureNmqPs = 'natureNmqPs'; // 自然搜索接nmq命令ping
    public static $InnerNmqPs = 'InnerNmqPs'; // 吧内搜索接nmq命令ping
    public static $AladingPs = 'AladingPs'; // 阿拉丁定时任务调度ping

    public static $UpdateDA = 'UpdateDA'; // DA词典更新

	public static $callNlpFail = 'CallNlpFail'; // nlp服务异常

    private static $arrStaticsKey = array(
        'natureNmqPs' => array(
            'title' => '[natural search] receive nmq order and ping webmaster platform failed',
            'content' => 'ping webmaster platform failed for %s times continuously',
        ),
        'InnerNmqPs' => array(
            'title' => '[inner search]receive nmq order and ping webmaster platform failed',
            'content' => 'ping webmaster platform failed for %s times continuously',
        ),
        'AladingPs' => array(
            'title' => '[aladdin]timed task ping webmaster platform failed',
            'content' => 'ping webmaster platform failed for %s times continuously',
        ),
        'UpdateDA' => array(
            'title' => '[aladdin]DA dict update alarm',
            'content' => 'the count of DA dict fall %s% than yesterday, lower than minimum threshold %s%',
        ),
	    'CallNlpFail' => array(
		    'title' => '[aladdin]call nlp service fail',
		    'content' => 'call nlp service failed for %s times continuously',
	    ),
    );

    private static $_redis = null;
    // 报警阈值，连续失败次数
    private static $consistentFailNum = 20;
    // DA词典记录数下跌阈值
    private static $intDAFlowRate = 10;
    // redis实例
    const REDIS_NAME = 'push';

    /**
     * @return Bingo_Cache_Redis|null
     */
    private static function _getRedis(){
        if(self::$_redis){
            return self::$_redis ;
        }
        self::$_redis = new Bingo_Cache_Redis(self::REDIS_NAME);

        if(!self::$_redis || !self::$_redis->isEnable()){
            Bingo_Log::warning("init redis fail.".serialize(self::$_redis));
            self::$_redis = null;
            return null;
        }
        return self::$_redis;
    }

    /**
     * ping站长平台失败
     * @param $key
     * @return bool
     */
    public static function pingFail($key) {
        if (self::_get($key) > self::$consistentFailNum) {
            Libs_Util_Alarm::sendAlarm(self::_preparePingReq($key));
            // 计零重新计数
            self::pingSuccess($key);
            return true;
        }
        self::_incr($key);
    }

    /**
     * ping站长平台成功
     * @param $key
     * @return bool
     */
    public static function pingSuccess($key) {
        $arrInput = array(
            'key' => $key,
            'value' => 0,
        );
        self::_set($arrInput);
    }

    /**
     * 更新DA记录数
     * @param $count
     * @return bool
     */
    public static function updateDARecord($count) {
        // 更新当日DA记录数
        $arrInput = array(
            'key' => self::$UpdateDA . date('Ymd', time()),
            'value' => $count,
        );
        self::_set($arrInput);
        // 对比昨日DA记录数
        $intYesterdayCount = self::_get(self::$UpdateDA . date('Ymd', strtotime('-1 day')));
        if ($count < $intYesterdayCount) {
            $floatFlowRate = (floatval($intYesterdayCount) - floatval($count)) / floatval($intYesterdayCount) * 100;
            $floatFlowRate = round($floatFlowRate, 2);
            // 日环比下跌超过阈值
            if ($floatFlowRate > self::$intDAFlowRate) {
                Libs_Util_Alarm::sendAlarm(self::_prepareDAReq($floatFlowRate));
            }
        }

    }

    /**
     * 获取key
     * @param $key
     * @return bool|int
     */
    private static function _get($key) {
        $redis = self::_getRedis();
        if(false === $redis) {
            return false;
        }
        $arrInput = array(
            'key' => $key,
        );
        $res = $redis->GET($arrInput);
        if(false === $res) {
            return false;
        }
        return intval($res['ret'][$key]);
    }

    /**
     * set
     * @param $arrInput
     * @return bool|int
     */
    private static function _set($arrInput) {
        $redis = self::_getRedis();
        if(false === $redis) {
            return false;
        }
        $res = $redis->SET($arrInput);
        if(false === $res) {
            return false;
        }
        return true;
    }

    /**
     * _incr
     * @param $key
     * @return bool|int
     */
    private static function _incr($key) {
        $redis = self::_getRedis();
        if(false === $redis) {
            return false;
        }
        $arrInput = array(
            'key' => $key,
        );
        $res = $redis->INCR($arrInput);
        if(false === $res) {
            return false;
        }
        return true;
    }

    /**
     * 准备ping失败报警方法参数
     * @param $key
     * @return array
     */
    private static function _preparePingReq($key) {
        $arrStaticsParam = self::$arrStaticsKey[$key];
        $content = sprintf($arrStaticsParam['content'], self::$consistentFailNum);
        $arrParam = array(
            'alarmTitle' => $arrStaticsParam['title'],
            'alarmContent' => $content,
            'emails' => Libs_Util_Alarm::$arrEmailByKey[$key],
            'phones' => Libs_Util_Alarm::$arrMessageByKey[$key],
        );
        return $arrParam;
    }

    /**
     * 准备DA记录日环比低于阈值报警方法参数
     * @param $floatFlowRate
     * @return array
     */
    private static function _prepareDAReq($floatFlowRate) {
        $arrStaticsParam = self::$arrStaticsKey[self::$UpdateDA];
        $content = sprintf($arrStaticsParam['content'], $floatFlowRate, self::$intDAFlowRate);
        $arrParam = array(
            'alarmTitle' => $arrStaticsParam['title'],
            'alarmContent' => $content,
        );
        return $arrParam;
    }

    /**
     * 声明为public,供外部类调用
     * 
     * @param $key
     * @return bool
     */
    public static function incr($key) {
        self::_incr($key);
    }

}