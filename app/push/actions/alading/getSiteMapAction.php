<?php
/**
 * ��ȡ������������Ϣ
 * <AUTHOR>
 *
 */
class getSiteMapAction extends Bingo_Action_Abstract
{
	private static $PASSWD='123456tieba!!!!!!';

	/**
	 * ��ȡ������������Ϣ
	 * @param null
	 * @return site map xml
	 *
	 */
	public function execute()
	{
		$strFileName = strval(Bingo_Http_Request::get('file_name', ''));
		
		if ('' === $strFileName)
		{
			$errno = Tieba_Errcode::ERR_PARAM;
			$errmsg = Tieba_Error::getErrmsg($errno);
			self::_jsonRet($errno, $errmsg);
			return;
		}

		//$strRemotePathPre = "http://storage.orp.baidu.com/openapi/call/storage/get?token=av1ot3lBadZ2_pqqf7IjqcCL0HPairTr&productName=tieba&path=/app/aladdin/sitemap/";
		$strRemotePathPre = "/app/aladdin/sitemap/";
		$strRemoteFile = $strRemotePathPre.$strFileName; 
		Bingo_Log::notice("remote_path=".$strRemoteFile);
		$file = Libs_Util_File::getOrpDataHandle($strRemoteFile);
		if(false === $file)
		{
			Bingo_Log::fatal("getOrpDataHandle failed! sitemap_file=$strFileName, all path=$strRemoteFile");
			return false;
		}
		while(!$file->eof())
		{
			$str .= $file->fgets();
		}
		$len = strlen($str);
		header("Content-Type:text/xml");
		header("Content-length:$len");
		echo $str;		
	}

	/**
	 * ������Ϣ
	 * @param unknown_type $errno
	 * @param unknown_type $errmsg
	 * @param array $arrExtData
	 */
	protected static function _jsonRet($errno,$errmsg,array $arrExtData=array()){
		$arrRet = array(
			'error_code'=>intval($errno),
			'error_msg'=>strval($errmsg),
				//'data'=>$arrExtData,
		);

		if (!empty($arrExtData))
		{
			$arrRet['data'] = $arrExtData;
		}

		foreach($arrRet as $k=>$v){
			Bingo_Page::assign($k,$v);
		}
		Bingo_Page::setOnlyDataType("json");
		Bingo_Http_Response::contextType('application/json');
	}
}
