<?php
// ����������ʵ��

class aldExpandAction extends Bingo_Action_Abstract
{

	private $arrKeyPathMap = array(
		'1' => 'forum_set_key1_201709181112',
		'2' => 'forum_set_key2_201709181112',
	);

	/**
	 * @param
	 * @return
	 */
	public function checkPrivate() {
		if (Bingo_Http_Request::getGet('from','') != 'alading_expand') {
			Bingo_Log::warning("param check fail");
			echo "403<br>";

			return false;
		}

		return true;
	}

	/**
	 * @param
	 * @return
	 */
	public function execute()
	{
		// У��
		if (!$this->checkPrivate()) {
			return false;
		}

		// $remotePath = "/app/aladdin/". date("Ymd")."/alading-quick";
		$remotePath = "/app/aladdin/alading-quick";
		$index = Bingo_Http_Request::getGet('index', 1);

		//��ȡorp���
		$remote_path = $remotePath."/".$this->arrKeyPathMap[$index];
		Bingo_Log::notice("remote_path=".$remote_path);
		$file = Libs_Util_File::getOrpDataHandle($remote_path);
		if(false === $file)
		{
			Bingo_Log::fatal("getOrpDataHandle failed! input[$remote_path]");
			return false;
		}
		while(!$file->eof())
		{
			$str .= $file->fgets();
		}
		$len = strlen($str);
		header("Content-Type:text/xml");
		header("Content-length:$len");
		echo $str;
	}
}
/* vim: set noexpandtab ts=4 sw=4 sts=4 tw=100: */
