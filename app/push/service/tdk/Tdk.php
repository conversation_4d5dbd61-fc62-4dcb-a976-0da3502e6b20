<?php
// tdk 优化相关

define("MODULE","push_service");
define('MODULE_NAME', 'push');
class Service_Tdk_Tdk{

	const SERVICE_NAME = "Service_Tdk_Tdk";
	protected static $_conf = null;

    private static $objDb = null;
	const DATABASE_NAME = "forum_aldstar";
    private static $_redis = null;

    /**
     * [_getDB description]
     * @param  string $dbname [description]
     * @return [type]         [description]
     */
    private static function _getDB($dbname='') {
        if (null == self::$objDb) {
            if($dbname<>"")
            {
                $objMysql = Tieba_Mysql::getDB($dbname);
            }
            else
            {
                $objMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
            }
            if($objMysql && $objMysql->isConnected()) {
                $objMysql->query('set names utf8');
                self::$objDb = $objMysql;
            } else {
                Bingo_Log::warning("db connect fail.");
                return null;
            }
        }
        return self::$objDb;
    }

    /**
     * @return
     */
    private static function _getRedis() {
        if(is_null(self::$_redis)) {
            $redisConf = Bd_Conf::getConf('app/push/redis'); //TODO
            self::$_redis = new Libs_Rpc_Redis($redisConf);
        }
        if(false == self::$_redis) {
            Bingo_Log::warning("get redis failed!");
        }
        return self::$_redis; 
    }

    /**
     * @param
     * @return
     */
    public static function redisQuery($arrInput) {
        $objRedis = self::_getRedis();
        if (null == $objRedis) {
            Bingo_Log::warning("init redis fail.");
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $strCall = $arrInput['call'];
        $arrParam = $arrInput['param'];
        if (empty($strCall) || empty($arrParam)) {
            Bingo_Log::warning(sprintf("param error.[%s]",serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $res = $objRedis->call($strCall,$arrParam);

        if (false == $res) {
            Bingo_Log::warning(sprintf("redisQuery fail.[$strCall] [%s] [%s]",serialize($arrParam),serialize($res)));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$res['ret']);
    }

	/**
	 * @return  void
	 **/
	private static function _init(){

		//add init code here. init will be called at every public function beginning.
		//not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
		//init should be recalled for many times.

		if(self::$_conf == null){
			self::$_conf = Bd_Conf::getConf("/app/push/service_alading_alading");
			if(self::$_conf == false){
				Bingo_Log::warning("init get conf fail.");
				return false;
			}

		}
		return true;
	}

	/**
	 * @param [in/out] req  :
     * @param
	 * @return  void
	 **/
	private static function _errRet($errno,$data=null){
		return array(
			'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
            'data'=>$data,
		);
	}

	/**
	 * @brief 前置钩子
	 * @param [in/out] req  :
	 * @return  void
	 * @retval
	 * @see
	 * @note
	 * <AUTHOR>
	 * @date 2013/09/24 15:36:46
	 **/
	public static function preCall($arrInput){
		// pre-call hook
	}

	/**
	 * @brief 后置钩子
	 * @param [in/out] req  :
	 * @return  void
	 * @retval
	 * @see
	 * @note
	 * <AUTHOR>
	 * @date 2013/09/24 15:36:46
	 **/
	public static function postCall($arrInput){
		// post-call hook
    }

    /**
     * @param
     * @return
     */
    public static function notifyPbTdkRecord($arrInput) {
        
        if ($arrInput['is_multi'] == 1) {
            $arrRedisInput = array(
                'call' => "DEL",
                'param' => array(
                    'key' => 'tdk_multi_flag',
                ),
            );
            return self::redisQuery($arrRedisInput);
        }

        $intCount = 50; // 默认去50条
        if (isset($arrInput['notify_cnt']) && intval($arrInput['notify_cnt']) > 0) {
            $intCount = intval($arrInput['notify_cnt']);
        }

        // 1.读词表
        // 2.写入表
        // 3.删除数据
        $handleWordServer = Wordserver_Wordlist::factory();
        $arrParam = array(
            'table' => 'tb_wordlist_redis_pbtdk_single', // 单条数据
            'start' => 0,
            'stop'  => $intCount,
        );

        $arrRet = $handleWordServer->getTableContents($arrParam);
        if ($arrRet == false || $arrRet['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf("wordserver get table content fail. [%s] [%s]",serialize($arrParam),serialize($arrRet)));
            $arrOut = array(
                'errno'=>isset($arrRet['errno'])?$arrRet['errno']:Tieba_Errcode::ERR_CALL_SERVICE_FAIL,
                'errmsg'=>'fail',
            );
            return $arrOut;
        }

        // 错误号
        $intErrno = Tieba_Errcode::ERR_SUCCESS;
        $strErrmsg = "";

        $arrTids = array();
        foreach($arrRet['ret'] as $key=>$value) {
            $value = unserialize($value);
            $arrTids[] = $value[0]; 
        }
        $arrPostParam = array(
            'thread_ids'=>$arrTids,
            'forum_id'=>0,
            'need_abstract'=>0,
            'need_photo_pic'=>0,
            "need_forum_name" => 1,
        );
        $arrPostRet = Tieba_Service::call('post', 'mgetThread', $arrPostParam, null, null, 'post', 'php', 'utf-8');
        if (false == $arrRet || $arrRet['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf("call post mgetThread fail.[%s] [%s]",serialize($arrPostParam),serialize($arrRet)));
        }
        // $strTitle = $arrPostRet['output']['thread_list'][$tid]['title'];

        foreach($arrRet['ret'] as $key => $value){
            $value = unserialize($value);
            $arrDelParam = array(
                'table' => 'tb_wordlist_redis_pbtdk_item',
                'key' => $key,
            );
            if (count($value) < 4) {
                Bingo_Log::warning(sprintf("wordserver value is invalid.[%s] ",serialize($value)));
                $strErrmsg .= "format_error:[$key] ";
                continue;
            }
            // 原始title
            $tid = intval($value[0]);
            $strRawTitle = strval($arrPostRet['output']['thread_list'][$tid]['title']);
            $strTdkTitle = strval($value[1]);
            // 吧名
            $strForumName = strval($arrPostRet['output']['thread_list'][$tid]['forum_name']);

            $strTitle = $strRawTitle."_".$strTdkTitle."_".$strForumName."_"."百度贴吧";
            $arrParam = array(
                'url'=>'http://tieba.baidu.com/p/'.$value[0],
                'extid' => $value[0],
                'type' => 1,
                'tdk_desc' => $value[2],
                'tdk_keywords'=>$value[3],
                'tdk_title'=>$strTitle,
            );

            $arrRet = Service_Tdk_Tdk::addTdkRecord($arrParam);
            if ($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning(sprintf("fail to insert tdk_update_record.[%s] [%s]",serialize($arrParam),serialize($arrRet)));
                $intErrno = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                $strErrmsg .= "insert_db:[$key] ";
                // continue;
            }

            // 删除记录
            // $arrDelRet = $handleWordServer->deleteKey($arrDelParam);
            // Bingo_Log::warning("word_server_in:".var_export($arrDelParam,1));
            // Bingo_Log::warning("word_server_ret:".var_export($arrDelRet,1));
        }
        $arrOut = array(
            'errno'=>$intErrno,
            'errmsg'=> empty($strErrmsg) ? "success":$strErrmsg,
        );
        return $arrOut;
    }

	/*
	 * @brief
	 * @param [in/out] req  : $arrInput
	 * @return
	 * @desc
	 **/
    public static function addTdkRecord($arrInput) {
        if(!isset($arrInput['url']) || !isset($arrInput['extid']) || !isset($arrInput['type'])
            || !isset($arrInput['tdk_title']) || !isset($arrInput['tdk_desc'])|| !isset($arrInput['tdk_keywords'])) {
            Bingo_Log::warning(sprintf("param error.[%s]",serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 获取数据
        $strUrl = $arrInput['url'];
        $intExtid = $arrInput['extid'];
        $intType = $arrInput['type'];
        $strTdkDesc = $arrInput['tdk_desc'];
        $strTdkKeywords = $arrInput['tdk_keywords'];
        $strTdkTitle = $arrInput['tdk_title'];

        $intPriority = 9;
        if (isset($arrInput['priority'])) {
            $intPriority = intval($arrInput['priority']);
        }

        $objDb = self::_getDB();
        if (!$objDb) {
            Bingo_Log::warning("get db fail");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        // 生成语句
        $strSql = sprintf("INSERT INTO tdk_update_record(url,extid,tdkdesc,tdkkws,tdktitle,ctime,utime,type,priority) VALUES ('%s',%d,'%s', '%s', '%s',%d,%d,%d,%d)",
            $strUrl,$intExtid,$strTdkDesc,$strTdkKeywords,$strTdkTitle,time(),time(),$intType,$intPriority);
		$arrRes = $objDb->query($strSql);
		if ($arrRes === false)
		{
			Bingo_Log::warning('fail to insert tdk_record with sql:'.$objDb->getLastSql().' and errno '.$objDb->error());
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}

        // 调用vipping服务
        self::_vippingPbMod($intExtid);

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @豪哥 封装一个rts库的接口
     * @param
     * @return 
     */
    public static function vippingPs($arrInput) {
        if (!isset($arrInput['ping_type']) || !isset($arrInput['op_type']) || !isset($arrInput['thread_id'])) {
            Bingo_Log::warning("param error [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $ping_type = Service_Alading_Lib_VippingNew::PING_TYPE_NORMAL;
        if ($arrInput['ping_type'] == 'rts') {
            $ping_type = Service_Alading_Lib_VippingNew::PING_TYPE_RECOMMAND;
        }
        $arrTransPack = array(
            'command_no' => Service_Alading_Alading::THREAD_COMMIT_CMD,
            'thread_id' => $arrInput['thread_id'],
            'op_type' => $arrInput['op_type'],
            'ping_type' => $ping_type,
            'from_type' => Service_Alading_Lib_VippingNew::VIPPING_FROM_TYPE_PBTDK,
        );
        $ret = Service_Alading_Lib_VippingNew::sendVipping($arrTransPack);
        if (false == $ret) {
            Bingo_Log::warning(sprintf("vipping call fail.[%s]",serialize($arrTransPack)));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param
     * @return 
     */
    private static function _vippingPbMod($tid,$mod = "MOD") {
        $arrTransPack = array(
            'command_no' => Service_Alading_Alading::THREAD_COMMIT_CMD,
            'thread_id' => intval($tid),
            'op_type' => $mod,
            'ping_type' => Service_Alading_Lib_VippingNew::PING_TYPE_NORMAL,
            'from_type' => Service_Alading_Lib_VippingNew::VIPPING_FROM_TYPE_PBTDK,
        );
        $ret = Service_Alading_Lib_VippingNew::sendVipping($arrTransPack);
        if (false == $ret) {
            Bingo_Log::warning(sprintf("vipping call fail.[%s]",serialize($arrTransPack)));
            return false;
        }
        return true;
    }

	/*
	 * @brief
	 * @param [in/out] req  : $arrInput
	 * @return
	 * @desc
	 **/
    public static function getTdkRecordList($arrInput) {
        $intOffset = 0;
        $intLimit = 20;
        if (isset($arrInput['offset'])) {
            $intOffset = intval($arrInput['offset']);
            if ($intOffset < 0) {
                $intOffset = 0;
            }
        }
        if (isset($arrInput['limit'])) {
            $intLimit = intval($arrInput['limit']);
            if ($intLimit < 0) {
                $intLimit = 20;
            }
        }

        $objDb = self::_getDB();
        if (!$objDb) {
            Bingo_Log::warning("get db fail");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $strSql = sprintf("SELECT url,extid,type,tdktitle,tdkdesc,tdkkws,priority,querywords FROM tdk_update_record WHERE isdel = 0 ORDER BY utime LIMIT %d,%d ",$intOffset,$intLimit);
        $arrRes = $objDb->query($strSql);
		if ($arrRes === false)
		{
			Bingo_Log::warning('fail to get tdk_record with sql:'.$objDb->getLastSql().' and errno '.$objDb->error());
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes);
    }

    /*
     * @brief
     * @param [in/out] req  : $arrInput
     * @return
     * @desc
     **/
    public static function getTdkRecord($arrInput) {
		// retrun empty
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,'');

        if (!isset($arrInput['extid']) || !isset($arrInput['type'])) {
            Bingo_Log::warning(sprintf("param error.[%s]",serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        
        // 调用aggregate服务判断是否存在tdk描述
        $arrParam = array(
            'thread_id' => $arrInput['extid'],
        );
        $arrRet = Tieba_Service::call('aggregate', 'getTdkConfByTid', $arrParam, null, null, 'post', 'php', 'utf-8');
        if ($arrRet['errno'] == Tieba_Errcode::ERR_SUCCESS && !empty($arrRet['data'][0])) {
            $arrOut = array(
                'extid'    => $arrRet['data'][0]['thread_id'],
                'type'     => 1,
                'tdktitle' => $arrRet['data'][0]['page_title'],
                'tdkdesc'  => $arrRet['data'][0]['page_desc'],
                'tdkkws'   => $arrRet['data'][0]['page_kws'],
            );
            Bingo_Log::pushNotice("is_new_tdk",1);
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrOut);
        }
        Bingo_Log::pushNotice("is_new_tdk",0);

        $objDb = self::_getDB();
        if (!$objDb) {
            Bingo_Log::warning("get db fail");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $strSql = sprintf("SELECT url,extid,type,tdktitle,tdkdesc,tdkkws,priority,querywords FROM tdk_update_record WHERE extid=%d AND type=%d AND isdel=0 ORDER BY priority ASC LIMIT 1",intval($arrInput['extid']),intval($arrInput['type']));
        $arrRes = $objDb->query($strSql);
		if ($arrRes === false)
		{
			Bingo_Log::warning('fail to insert tdk_record with sql:'.$objDb->getLastSql().' and errno '.$objDb->error());
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes[0]);
    }

	/*
	 * @brief
	 * @param [in/out] req  : $arrInput
	 * @return
	 * @desc
	 **/
    public static function updateTdkRecord($arrInput) {
        if(!isset($arrInput['tdk_title']) || !isset($arrInput['tdk_desc'])|| !isset($arrInput['tdk_keywords']) || !isset($arrInput['id']) || !isset($arrInput['extid'])) {
            Bingo_Log::warning(sprintf("param error.[%s]",serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $objDb = self::_getDB();
        if (!$objDb) {
            Bingo_Log::warning("get db fail");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $strSql = sprintf("UPDATE tdk_update_record SET tdktitle=%s,tdkkws=%s,tdkdesc=%s WHERE id=%d",$arrInput['tdk_title'],$arrInput['tdk_keywords'],$arrInput['tdk_desc'],$arrInput['id']);
        $res = $objRDb->query($strSql);
        if(false==$res) {
            Bingo_Log::warning("update sql fail.");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        self::_vippingPbMod($arrInput['extid']);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

	/*
	 * @brief
	 * @param [in/out] req  : $arrInput
	 * @return
	 * @desc
	 **/
    public static function delTdkRecord($arrInput) {
        $objDb = self::_getDB();
        if (!$objDb) {
            Bingo_Log::warning("get db fail");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $strSql = sprintf("UPDATE tdk_update_record SET isdel=1 WHERE extid=%d AND type=%d AND priority=%d",intval($arrInput['extid']),intval($arrInput['type']),intval($arrInput['priority']));
        $arrRes = $objDb->query($strSql);
		if ($arrRes === false)
		{
			Bingo_Log::warning('fail to insert tdk_record with sql:'.$db->getLastSql().' and errno '.$db->error());
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
        self::_vippingPbMod($arrInput['extid'],"DEL");
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes);
    }
    /**
     * [getUserInfoToRedpacket description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getUserInfoToRedpacket($arrInput) {
        if (!isset($arrInput['uid']) || !isset($arrInput['tablename'])) {
            Bingo_Log::warning(sprintf("param error.[%s]",serialize($arrInput)));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $objDb = self::_getDB('forum_webshow');
        if (!$objDb) {
            Bingo_Log::warning("get db fail");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $strSql = sprintf("SELECT id,uid FROM %s WHERE uid = %d ",$arrInput['tablename'],$arrInput['uid']);
        $arrRes = $objDb->query($strSql);
        if ($arrRes === false)
        {
            Bingo_Log::warning('fail to select redpacketuidtable with sql:'.$objDb->getLastSql().' and errno '.$objDb->error());
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes);
    }
}
?>
