<?php
// 热搜词

class Service_Hotquery_Word_Word {

	protected static $_conf = null;

	/**
	 * @param [in/out] req  :
     * @param
	 * @return  void
	 **/
	public static function _errRet($errno,$data=null){
		return array(
			'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
            'data'=>$data,
		);
	}

	/**
	 * @brief
	 * @param [in/out] req  : $arrInput
	 * @return
	 * @desc
	 **/
    public static function addQueryWord($arrInput) {
        if (!isset($arrInput['query_word']) || !isset($arrInput['group_id']) || !isset($arrInput['query_count']) || !isset($arrInput['is_manual'])) {
            Bingo_Log::warning("param error.");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $strOperator = 'system';
        $intHotLevel = 0;
        $intOrderNum = 0;

        if (isset($arrInput['operator'])) {
            $strOperator = $arrInput['operator'];
        }

        if (isset($arrInput['hot_level'])) {
            $intHotLevel = $arrInput['hot_level'];
        }

        if (isset($arrInput['order_num'])) {
            $intOrderNum = $arrInput['order_num'];
        }

        $objDb = Service_Hotquery_Libs_Util::getDB();
        if (!$objDb) {
            Bingo_Log::fatal("get objDb fail");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $strSql = sprintf("INSERT INTO hot_query_query_word(group_id,query_word,query_count,create_time,update_time,operator,hot_level,order_num,is_manual) VALUES (%d,'%s',%d,%d,%d,'%s',%d,%d,%d)",$arrInput['group_id'],$arrInput['query_word'],$arrInput['query_count'],time(),time(),$strOperator,$intHotLevel,$intOrderNum,$arrInput['is_manual']);
        $arrRes = $objDb->query($strSql);

        if ($arrRes === false) {
            Bingo_Log::warning('DB insert white word fail. sql:'.$objDb->getLastSql().' and errno '.$objDb->error());
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes);
    }

    /**
     * @param
     * @return
     */
    public static function queryDB($arrInput) {
        if(!isset($arrInput['sql'])) {
            Bingo_Log::warning("param error.");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $objDb = Service_Hotquery_Libs_Util::getDB();
        if (!$objDb) {
            Bingo_Log::fatal("get objDb fail");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $strSql = $arrInput['sql'];
        $arrRes = $objDb->query($strSql);

        Bingo_Log::warning('>>>>> fyw sql: ['.$objDb->getLastSql().']');

		if ($arrRes === false) {
			Bingo_Log::warning('DB DEL query word fail. sql:'.$objDb->getLastSql().' and errno '.$objDb->error());
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes);
    }

	/**
	 * @brief
	 * @param [in/out] req  : $arrInput
	 * @return
	 * @desc
	 **/
    public static function mAddQueryWord($arrInput) {
        if (!isset($arrInput['reqs']) || !is_array($arrInput['reqs'])) {
            Bingo_Log::warning("param error.");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $strOperator = 'system';
        $intHotLevel = 0;
        $intOrderNum = 0;

        if (isset($arrInput['operator'])) {
            $strOperator = $arrInput['operator'];
        }

        $objDb = Service_Hotquery_Libs_Util::getDB();
        if (!$objDb) {
            Bingo_Log::fatal("get objDb fail");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $strSql = "INSERT INTO hot_query_query_word(group_id,query_word,query_count,create_time,update_time,operator,hot_level) VALUES"; 
        $arrRows = array();

        foreach ($arrInput['reqs'] as $arrParam) {
            $arrRows[] = sprintf("(%d,'%s',%d,%d,%d,'%s',%d)",$arrParam['group_id'],$arrParam['query_word'],$arrParam['query_count'],time(),time(),$strOperator,$arrParam['hot_level']);
        }

        $strSql = $strSql . join(",",$arrRows);
        $arrRes = $objDb->query($strSql);

        if ($arrRes === false) {
            Bingo_Log::warning('DB insert white word fail. sql:'.$objDb->getLastSql().' and errno '.$objDb->error());
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes);
    }

    /**
     * @param
     * @return
     */
    public static function updateQueryWordByGidAndWord($arrInput) {
        if (!isset($arrInput['group_id']) || !isset($arrInput['query_word']) || !isset($arrInput['hot_level'])) {
            Bingo_Log::warning("param error.");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $objDb = Service_Hotquery_Libs_Util::getDB();
        if (!$objDb) {
            Bingo_Log::fatal("get objDb fail");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $arrParam = array(
            0 => "update_time=".time(),
        );

        $intGroupId = $arrInput['group_id'];
        $strQueryWord = $arrInput['query_word'];

        if(isset($arrInput['hot_level'])) {
            $arrParam[] = "hot_level=".$arrInput['hot_level'];
        }

        if(isset($arrInput['query_count'])) {
            $arrParam[] = "query_count=".$arrInput['query_count'];
        }

        $strSql = sprintf("UPDATE hot_query_query_word SET %s WHERE group_id = %d AND query_word='%s'",join(",",$arrParam),$intGroupId,$strQueryWord);

        $arrRes = $objDb->query($strSql);

        Bingo_Log::warning('>>>>> fyw sql: ['.$objDb->getLastSql().']');

		if ($arrRes === false) {
			Bingo_Log::warning('DB update query_word fail. sql:'.$objDb->getLastSql().' and errno '.$objDb->error());
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes);
    }

	/**
	 * @brief
	 * @param [in/out] req  : $arrInput
	 * @return
	 * @desc
	 **/
    public static function updateQueryWord($arrInput) {
        if (!isset($arrInput['id'])) {
            Bingo_Log::warning("param error.");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $objDb = Service_Hotquery_Libs_Util::getDB();
        if (!$objDb) {
            Bingo_Log::fatal("get objDb fail");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $arrParam = array();
        if(isset($arrInput['status'])) {
            $arrParam[] = "status=".$arrInput['status'];
        }
        if(isset($arrInput['order_num'])) {
            $arrParam[] = "order_num=".$arrInput['order_num'];
        }
        if(isset($arrInput['operator'])) {
            $arrParam[] = "operator='".$arrInput['operator']."'";
        }

        if(isset($arrInput['hot_level'])) {
            $arrParam[] = "hot_level=".$arrInput['hot_level'];
        }

        $strSql = sprintf("UPDATE hot_query_query_word SET %s WHERE id = %d",join(",",$arrParam),$arrInput['id']);
        $arrRes = $objDb->query($strSql);
		if ($arrRes === false) {
			Bingo_Log::warning('DB update query_word fail. sql:'.$objDb->getLastSql().' and errno '.$objDb->error());
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes);
    }

    /**
     * @param
     * @return
     */
    public static function delQueryWord($arrInput) {
        if (!isset($arrInput['id'])) {
            Bingo_Log::warning("param error.");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $objDb = Service_Hotquery_Libs_Util::getDB();
        if (!$objDb) {
            Bingo_Log::fatal("get objDb fail");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $strSql = sprintf("DELETE FROM hot_query_query_word WHERE id = %d",$arrInput['id']);
        $arrRes = $objDb->query($strSql);
		if ($arrRes === false) {
			Bingo_Log::warning('DB DEL query word fail. sql:'.$objDb->getLastSql().' and errno '.$objDb->error());
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes);
    }

	/**
	 * @brief
	 * @param [in/out] req  : $arrInput
	 * @return
	 * @desc
	 **/
    public static function getQueryWordList($arrInput) {
        if (!isset($arrInput['cond'])) {
            Bingo_Log::warning("param error.");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $objDb = Service_Hotquery_Libs_Util::getDB();
        if (!$objDb) {
            Bingo_Log::fatal("get objDb fail");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $arrOut = array();
        if (isset($arrInput['need_page'])) {
            // 查询count 分页使用
            $strSql = sprintf("SELECT COUNT(1) as count FROM hot_query_query_word WHERE %s",$arrInput['cond']);
            $arrRes = $objDb->query($strSql);

            if ($arrRes === false) {
                Bingo_Log::warning('DB query query_word fail. sql:'.$objDb->getLastSql().' and errno '.$objDb->error());
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }

            $arrOut['page']['count'] = $arrRes[0]['count'];
        }

        $strSql = sprintf("SELECT id,group_id,query_word,query_count,create_time,update_time,operator,hot_level,order_num,is_manual,status FROM hot_query_query_word WHERE %s",$arrInput['cond']." ".$arrInput['need_page']);
        $arrRes = $objDb->query($strSql);

		if ($arrRes === false) {
			Bingo_Log::warning('DB query query_word fail. sql:'.$objDb->getLastSql().' and errno '.$objDb->error());
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
        $arrOut['list'] = $arrRes;
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrOut);
    }

	/**
	 * @brief
	 * @param [in/out] req  : $arrInput
	 * @return
	 * @desc
	 **/
    public static function getQueryWord($arrInput) {
        if (!isset($arrInput['group_id']) || !isset($arrInput['query_word'])) {
            Bingo_Log::warning("param error.");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $objDb = Service_Hotquery_Libs_Util::getDB();
        if (!$objDb) {
            Bingo_Log::fatal("get objDb fail");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $strSql = sprintf("SELECT * FROM hot_query_query_word WHERE group_id=%d AND query_word='%s'",$arrInput['group_id'],$arrInput['query_word']);

        $arrRes = $objDb->query($strSql);
		if ($arrRes === false) {
			Bingo_Log::warning('DB query query_word fail. sql:'.$objDb->getLastSql().' and errno '.$objDb->error());
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes);
    }
}
?>
