<?php
class Service_Hotquery_Libs_Util {
    private static $_db;
    private static $_redis;

    const DATABASE_NAME = "forum_aldstar";

    /**
     * @return
     */
    public static function getDB() {
        if (null == self::$_db) {
            $objMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
            if($objMysql && $objMysql->isConnected()) {
                $objMysql->query('set names utf8');
                self::$_db = $objMysql;
            } else {
                Bingo_Log::warning("db connect fail.");
                return null;
            }
        }
        return self::$_db;
    }

    /**
     * @return
     */
    public static function getRedis() {
        if(is_null(self::$_redis)) {
            $redisConf = Bd_Conf::getConf('app/push/redis'); //TODO
            self::$_redis = new Libs_Rpc_Redis($redisConf);
        }
        if(false == self::$_redis) {
            Bingo_Log::warning("get redis failed!");
        }
        return self::$_redis; 
    }

    /**
     * @param
     * @param
     * @param
     * @return
     */
    function genGroupId($time,$min = 5,$last=1) {
        $s = $min * 60 * $last;
        $t = $time - $s;
        $t = $t - $t%$s;
        return date('YmdHi',$t);
    }
}
