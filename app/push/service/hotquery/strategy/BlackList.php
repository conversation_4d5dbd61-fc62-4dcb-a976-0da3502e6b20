<?php
// 黑名单

class Service_Hotquery_Strategy_BlackList {
	protected static $_conf = null;
	/**
	 * @param [in/out] req  :
     * @param
	 * @return  void
	 **/
	public static function _errRet($errno,$data=null){
		return array(
			'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
            'data'=>$data,
		);
	}

	/**
	 * @brief
	 * @param [in/out] req  : $arrInput
	 * @return
	 * @desc
	 **/
    public static function addBlackWord($arrInput) {
        if (!isset($arrInput['query_word']) || !isset($arrInput['expire_time']) || !isset($arrInput['order_num']) || !isset($arrInput['operator'])) {
            Bingo_Log::warning("param error.");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $objDb = Service_Hotquery_Libs_Util::getDB();
        if (!$objDb) {
            Bingo_Log::fatal("get objDb fail");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $strSql = sprintf("INSERT INTO hot_query_black_list(query_word,create_time,update_time,expire_time,operator,order_num) VALUES ('%s',%d,%d,%d,'%s',%d)",$arrInput['query_word'],time(),time(),$arrInput['expire_time'],$arrInput['operator'],$arrInput['order_num']);
        $arrRes = $objDb->query($strSql);

        if ($arrRes === false) {
            Bingo_Log::warning('DB insert white word fail. sql:'.$objDb->getLastSql().' and errno '.$objDb->error());
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes);
    }

	/**
	 * @brief
	 * @param [in/out] req  : $arrInput
	 * @return
	 * @desc
	 **/
    public static function delBlackWord($arrInput) {
        if (!isset($arrInput['id'])) {
            Bingo_Log::warning("param error.");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $objDb = Service_Hotquery_Libs_Util::getDB();
        if (!$objDb) {
            Bingo_Log::fatal("get objDb fail");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $strSql = sprintf("DELETE FROM hot_query_black_list WHERE id = %d",$arrInput['id']);
        $arrRes = $objDb->query($strSql);
		if ($arrRes === false) {
			Bingo_Log::warning('DB DEL white word fail. sql:'.$objDb->getLastSql().' and errno '.$objDb->error());
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes);
    }

	/**
	 * @brief
	 * @param [in/out] req  : $arrInput
	 * @return
	 * @desc
	 **/
    public static function getBlackList($arrInput) {
        if (!isset($arrInput['time_stamp'])) {
            Bingo_Log::warning("param error.");
            // return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $objDb = Service_Hotquery_Libs_Util::getDB();
        if (!$objDb) {
            Bingo_Log::fatal("get objDb fail");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $strSql = sprintf("SELECT id,query_word,create_time,update_time,expire_time,operator,order_num FROM hot_query_black_list WHERE expire_time > %d ORDER BY order_num",$arrInput['time_stamp']);
        $arrRes = $objDb->query($strSql);
		if ($arrRes === false) {
			Bingo_Log::warning('DB DEL white word fail. sql:'.$objDb->getLastSql().' and errno '.$objDb->error());
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes);
    }
}
?>
