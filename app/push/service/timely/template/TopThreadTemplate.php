<?php
/**
 * get top thread xml template 
 *
 */
class Service_Timely_Template_TopThreadTemplate
{
	const THREAD_URL = 'http://tieba.baidu.com/p/%d?fr=timely1&query=%s';
	const SHOW_URL = 'tieba.baidu.com';
	const MORE_LINK_TXT = '�鿴��������>>';
	const SHOW_LEFT_TXT = '�ٶ�����';
	//const MORE_LINK_URL = 'http://tieba.baidu.com/f?kw=%c8%c8%b5%e3&fr=timely1&query=';
    const MORE_LINK_URL = 'http://tieba.baidu.com/f/search/res?qw=%s&fr=timely1';
	
	/**
	 * get xml template
	 * @param $arrTopThreadInfo search ps result thread list 
	 * @return xml template
	 */
	public static function getXml($arrTopThreadInfo)
	{
		$arrItem = self::getTemplate($arrTopThreadInfo);
	        //var_dump($arrItem);
		if (false === $arrItem)
		{
			Bingo_Log::warning("get timely template is fail");
			return false;
		}
		
	    $itemXml = Libs_Util_Array2xml::createXML('item', $arrItem);
		$rootNode = $itemXml->getElementsByTagName('item');
		if ($rootNode->length > 0)
	    {
			$strXml = $itemXml->saveXML($rootNode->item(0));
			//echo $strXml;
			return $strXml;
		}
		
		return false;
	}

        /**
	*
	* @param $arrTopThreadInfo  get top thread info 
	* @return filter thread info 
	*
	 */	
	public static function getTemplate($arrTopThreadInfo)
	{
		if (!is_array($arrTopThreadInfo) || empty($arrTopThreadInfo))
		{
			Bingo_Log::warning("execute input param is error,the param is".print_r($arrTopThreadInfo, true));
			return false;
		}
		
		$strQuery = isset($arrTopThreadInfo['query']) ? strval($arrTopThreadInfo['query']) : '';
		
		if ('' === $strQuery)
		{
			Bingo_Log::warning("execute input param is error,the param is".print_r($arrTopThreadInfo, true));
			return false;
		}
		
		$item = array();
		$item['key'] = $strQuery; 
		$arrDisplay = self::_buildDisplay($arrTopThreadInfo);
		$item['display'] = $arrDisplay;
		$item['thread_ids'] = self::_buildThreadList($arrTopThreadInfo['thread_list']);
		return $item;
	}
	
	/**
	 *
	 * @param thread info
	 * @return display array
	 */
	private static function _buildDisplay($arrTopThreadInfo)
	{
		$arrDisplay = array();
		$strQuery = isset($arrTopThreadInfo['query']) ? strval($arrTopThreadInfo['query']) : '';
		
		if ('' === $strQuery)
		{
			Bingo_Log::warning("execute input param is error,the param is".print_r($arrTopThreadInfo, true));
			return false;
		}
		$strUrlQuery = urlencode($strQuery);
		$arrThreadList = $arrTopThreadInfo['thread_list'];
		$firstThreadInfo = isset($arrThreadList[0]) ? $arrThreadList[0] : array();
		$secondThreadInfo = isset($arrThreadList[1]) ? $arrThreadList[1] : array();
		$thirdThreadInfo = isset($arrThreadList[2]) ? $arrThreadList[2] : array();
		$fourthThreadInfo = isset($arrThreadList[3]) ? $arrThreadList[3] : array();
		
		$intFirstThreadId = isset($firstThreadInfo['thread_id']) ? intval($firstThreadInfo['thread_id']) : 0;
		
		$strTitle = isset($firstThreadInfo['title']) ? strval($firstThreadInfo['title']) : '';
		$strAbstract = isset($firstThreadInfo['abstract']) 
			               ? strval($firstThreadInfo['abstract']) : '';
		$strTitle = Libs_Util_String::delInvalidChar($strTitle);
		$strTitle = Libs_Util_String::filterNonGBKChar($strTitle);
		$strAbstract = Libs_Util_String::delInvalidChar($strAbstract);
		$strAbstract = Libs_Util_String::filterNonGBKChar($strAbstract);	
		$strUrl = sprintf(self::THREAD_URL, $intFirstThreadId, $strUrlQuery);
		$date = date("Y-m-d");
		
		$arrDisplay['title'] = $strTitle;
		$arrDisplay['url'] = $strUrl;
		$arrDisplay['brief'] = $strAbstract;
		$arrDisplay['date'] = $date;
		$arrDisplay['showurl'] = self::SHOW_URL;

		$arrLink = array();

                if (! empty($secondThreadInfo))
                {
                        $arrLink[] = self::_buildLink($secondThreadInfo, $strUrlQuery);
                }

		if (! empty($thirdThreadInfo))
		{
			$arrLink[] = self::_buildLink($thirdThreadInfo, $strUrlQuery);
		}

                if (! empty($fourthThreadInfo))
                {
			$arrLink[] = self::_buildLink($fourthThreadInfo, $strUrlQuery);
                }
		
		if (! empty($arrLink))
                {
			$arrDisplay['link'] = $arrLink;
		}
		$strUrlMoreLink = sprintf(self::MORE_LINK_URL,$strUrlQuery);
		$arrDisplay['morelink'] = array(
			'@attributes' => array(
				'txt' => self::MORE_LINK_TXT, 
				'url'=> $strUrlMoreLink,
			),
		);
		$arrDisplay['showLeftText'] = self::SHOW_LEFT_TXT;
		return $arrDisplay;		
	}

	/**
	 * build link note
	 * @param $arrThreadInfo thread info 
	 * @return link array
	 * 
	 */	
	private static function _buildLink($arrThreadInfo, $strUrlQuery = '')
	{
		$intThreadId = isset($arrThreadInfo['thread_id']) ? intval($arrThreadInfo['thread_id']) : 0;
		$strTitle = isset($arrThreadInfo['title']) ? strval($arrThreadInfo['title']) : '';
		$strTitle = Libs_Util_String::delInvalidChar($strTitle);
		$strTitle = Libs_Util_String::filterNonGBKChar($strTitle);		
		$strUrl = sprintf(self::THREAD_URL, $intThreadId, $strUrlQuery);
		$arrLink = array(
			'@attributes' => array(
				'txt' => $strTitle, 
				'url'=> $strUrl,
			),
		);
		return $arrLink;
	}
	
	/**
	 * ���������б�
	 * @param unknown_type $arrThreadList
	 * @return string
	 */
	private static function _buildThreadList($arrThreadList)
	{
		$strThreadIds = '';
		$arrThreadIds = array();
		foreach($arrThreadList as $arrThreadInfo)
		{
			$intThreadId = isset($arrThreadInfo['thread_id']) ? intval($arrThreadInfo['thread_id']) : 0;
			
			if ($intThreadId > 0)
			{
				$arrThreadIds[] = $intThreadId;
			}
		}
		
		$strThreadIds = implode("\n", $arrThreadIds);
		$strThreadIds = "\n$strThreadIds\n";
                return $strThreadIds;
	}
}
