<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Thread.php
 * <AUTHOR>
 * @date 2013/06/07 19:58:43
 * @brief 阿拉丁扩量抓取贴子策略
 *  
 **/
define("MODULE","service_push");
define('MODULE_NAME', 'alading');

class Service_Alading_Ext_Thread{
    private $_type;
    private $_file;

    /**
     * [__construct description]
     * @param [type] $type [description]
     * @param [type] $file [description]
     */
    public function __construct($type,$file) {
        $this->_type = $type;
        $this->_file = $file;
    }

    /**
     * [_checkInput 参数校验]
     * @param  [type] $line [description]
     * @return [type]       [description]
     */
    private function _checkInput($line){
        if (empty($line)){
            return array(
                'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
                'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR),
            );
        }
        Bingo_Log::notice($line);
        $arrLine = explode(" ",$line);
        if (count($arrLine) != 2){
            return array(
                'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
                'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR),
            );
        }
        $strQuery = strval($arrLine[0]);
        $intFourmId = intval($arrLine[1]);
        if (empty($strQuery) || $intFourmId <= 0){
            return array(
                'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
                'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_PARAM_ERROR),
            );
        }
        return array(
            'errno' => Tieba_Errcode::ERR_SUCCESS,
            'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
            'forum_id' => $intFourmId,
            'query' => $strQuery,
        );
    }

    /**
     * [_normalization description]
     * @param  [type] $arrScore [description]
     * @return [type]           [description]
     */
    private function _normalization($arrScore){
        $ret = array();
        $intMaxScore = max($arrScore);
        $intMinScore = min($arrScore);
        foreach($arrScore as $tid => $score){
            if ($intMaxScore > $intMinScore){
                $value = round(($score - $intMinScore)/($intMaxScore - $intMinScore),2);
                $ret[$tid] = $value;
            }
            else {
                $ret[$tid] = $intMinScore;
            }
        }
        return $ret;
    }
    /**
     * [get_feature description]
     * @param  [type] $tid [description]
     * @return [type]      [description]
     */
    private function _getFeature($arrTids){
        $objRalMulti = new Tieba_Multi('feature');
        foreach ($arrTids as $tid) {
            $input = array(
                "thread_id" => $tid, //帖子id
                "offset" => 0,
                "res_num" => 100,
                "see_author" => 0,
                "has_comment" => 0,
                "has_mask" =>1,
                "has_ext" => 0,
                "need_set_pv" => 0,
                "structured_content" => 1,
            );
            $arrMultiInput = array(
                'serviceName' => 'post',
                'method' => 'getPostsByThreadId',
                'input' => $input,
                'ie' => 'utf-8',
            );
            $objRalMulti->register($tid,new Tieba_Service('post'),$arrMultiInput);
        }
        $objRalMulti->call();
        $arrScore = array();
        $arrRet = $objRalMulti->results;
        foreach ($arrTids as $tid) {
            $arrScore['image'][$tid] = 0;
            $arrScore['good'][$tid] = 0;
            $arrScore['first'][$tid] = 0;
            $res = $arrRet[$tid];
            if ($res['output']['output'][0]['errno'] == 0){
                if ($res['output']['output'][0]['is_thread_deleted'] != 0){
                    continue;
                }
                if ($res['output']['output'][0]['is_thread_visible'] != 0){
                    continue;
                }
                foreach($res['output']['output'][0]['post_infos'] as $post){
                    if ($post['is_post_visible'] == 1){
                        continue;
                    }  
                    if ($post['post_no'] == 1){
                        $intAuthorId = $post['user_id'];
                    }
                    $strContent = "";
                    foreach($post['content'] as $info){
                        if ($info['tag'] == "plainText"){
                            $strContent .= $info['value'];
                        }
                        if ($info['tag'] == 'img' && $info['class'] != 'BDE_Smiley' && $intAuthorId == $post['user_id']){
                            $arrScore['image'][$tid] += 1;
                        }
                    }
                    if (strlen($strContent) >= 60){
                        $arrScore['good'][$tid] += 1;
                    }
                    if ($post['post_no'] == 1 && strlen($strContent) >= 10){
                        $arrScore['first'][$tid]  = log10(strlen($strContent));
                    }   
                }
            }
        }
        $arrScore['good'] = $this->_normalization($arrScore['good']);
        $arrScore['image'] = $this->_normalization($arrScore['image']);
        $arrScore['first'] = $this->_normalization($arrScore['first']);
        $arrRets = array();
        foreach($arrTids as $tid){
            $arrRets[$tid] = round(0.12*$arrScore['first'][$tid]+0.42*$arrScore['good'][$tid]+0.64*$arrScore['image'][$tid],2);
        }
        $intMaxQuality = max(array_values($arrRets));
        $intMinQuality = min(array_values($arrRets));
        foreach($arrRets as $tid => $score){
            if ($intMaxQuality <= $intMinQuality){
                $arrRets[$tid] = $intMinQuality;
                continue;
            }
            $arrRets[$tid] = floatval(($score-$intMinQuality)/($intMaxQuality-$intMinQuality));
        }
        return $arrRets;
    }
   
    /**
     * [spiderThread description]
     * @return [type] [description]
     */
    public function spiderThread(){
        if ($this->_type == 1){
            echo "自然搜索方案\n";
        }
        else {
            echo "吧内搜索方案\n";
        }
        
        $fp = fopen($this->_file, 'r');
        $rn = 20;
        while (!feof($fp)) {
            $line = fgets($fp, 1024);
            $param = $this->_checkInput($line);
            $intFourmId = $param['forum_id'];
            $strQuery = $param['query'];
            $objForum = new Service_Alading_Ext_Forum($intFourmId);
            $ret = $objForum->getForumName();
            if ($ret['errno'] != Tieba_Errcode::ERR_SUCCESS || empty($ret['forum_name']) || empty($strQuery)){
                Bingo_Log::warning("param check error,line:$line");
                continue;
            }
            $strForumName = $ret['forum_name'];
            echo "forum_name:$strForumName\n";
            $objSpider = new Service_Alading_Ext_Spider($this->_type);
            $pn = 0;
            $arrThreadTitle = array();
            $arrThreadTime = array();
            while($pn < 100){
                $res = $objSpider->getUrlResponse($strQuery,$strForumName,$pn,$rn);
                if ($res['errno'] == Tieba_Errcode::ERR_SUCCESS){
                    $res = $objSpider->parseResult();
                    if ($res['errno'] == Tieba_Errcode::ERR_SUCCESS && !empty($res['data'])){
                        $res = $objForum->getThreadInfo($res['data']);
                        var_export($res);
                        if ($res['errno'] == Tieba_Errcode::ERR_SUCCESS && !empty($res['title']) && !empty($res['time'])){
                            foreach($res['title'] as $tid=>$title){
                                $arrThreadTitle[$tid] = $title;   
                            }
                            foreach($res['time'] as $tid=>$time){
                                $arrThreadTime[$tid] = $time;   
                            }
                            if (count($arrThreadTime) >= 100 || count($arrThreadTitle) >= 100){
                                break;
                            }
                        }
                    }
                }
                $pn += $rn;
            }
            echo "$strQuery\t$intFourmId:\n";
            if (!empty($arrThreadTitle) && !empty($arrThreadTime)){
                $arrScore = array();
                //贴子热度打分
                $intMaxTime = max(array_values($arrThreadTime));
                $intMinTime = min(array_values($arrThreadTime));
                $arrTimeScore = array();
                foreach($arrThreadTime as $tid => $time){
                    if ($intMaxTime <= $intMinTime){
                        $arrTimeScore[$tid] = $intMinTime;
                        continue;
                    }
                    $arrTimeScore[$tid] = floatval(($time-$intMinTime)/($intMaxTime-$intMinTime));
                }

                //贴子质量打分
                $arrQualityScore = $this->_getFeature(array_keys($arrThreadTime));
                //特征加权
                foreach($arrTimeScore as $tid => $score){
                    if ($arrQualityScore[$tid] == 0){
                        continue;
                    }
                    //$arrScore[$tid] = $arrNlpScore[$tid]*0.2 + $arrTimeScore[$tid]*0.4 + $arrQualityScore[$tid]*0.4;
                    $arrScore[$tid] = $arrTimeScore[$tid]*0.5 + $arrQualityScore[$tid]*0.5;
                }
                arsort($arrScore);
                $count = 0;
                foreach ($arrScore as $tid => $score) {
                    echo "$tid\t".$arrThreadTitle[$tid]."\n";
                    $count++;
                    if ($count >=10 ){
                        break;
                    }
                }
            }
            echo "\n";
        }
    }
    
    /**
     * @param
     * @param
     * @return
     */
    public function spiderThreadByFidAndQuery($intFourmId,$strQuery) {
        if ($intFourmId <=0 || $strQuery == '') {
            return false;
        }

        $arrOut = array();

        $objForum = new Service_Alading_Ext_Forum($intFourmId);
        $ret = $objForum->getForumName();
        if ($ret['errno'] != Tieba_Errcode::ERR_SUCCESS || empty($ret['forum_name']) || empty($strQuery)){
            Bingo_Log::warning("param check error,line:$line");
            continue;
        }
        $strForumName = $ret['forum_name'];
        echo "forum_name:$strForumName\n";
        $objSpider = new Service_Alading_Ext_Spider($this->_type);
        $pn = 0;
        $rn = 20;
        $arrThreadTitle = array();
        $arrThreadTime = array();
        $arrThreadInfo = array();
        $arrOut = array();
        //echo "$strQuery\t$intFourmId:\n";
        while($pn < 200) {
            $res = $objSpider->getUrlResponse($strQuery,$strForumName,$pn,$rn);
            if ($res['errno'] == Tieba_Errcode::ERR_SUCCESS) {
                $res = $objSpider->parseResult();
                if ($res['errno'] == Tieba_Errcode::ERR_SUCCESS && !empty($res['data'])){
                    $res = $objForum->getThreadInfo($res['data']);
                    if ($res['errno'] == Tieba_Errcode::ERR_SUCCESS && !empty($res['title']) && !empty($res['time'])){
                        foreach($res['title'] as $tid=>$title){
                            $arrThreadTitle[$tid] = $title;   
                        }
                        foreach($res['time'] as $tid=>$time){
                            $arrThreadTime[$tid] = $time;   
                        }
                        $arrThreadInfo = $arrThreadInfo + $res['thread_info'];
                        if (count($arrThreadTime) >= 100 || count($arrThreadTitle) >= 100){
                            break;
                        }
                    }
                }
            }
            $pn += $rn;
        }
        if (!empty($arrThreadTitle) && !empty($arrThreadTime)){
            $arrScore = array();
            //贴子热度打分
            $intMaxTime = max(array_values($arrThreadTime));
            $intMinTime = min(array_values($arrThreadTime));
            $arrTimeScore = array();
            foreach($arrThreadTime as $tid => $time){
                if ($intMaxTime <= $intMinTime){
                    $arrTimeScore[$tid] = $intMinTime;
                    continue;
                }
                $arrTimeScore[$tid] = floatval(($time-$intMinTime)/($intMaxTime-$intMinTime));
            }

            //贴子质量打分
            $arrQualityScore = $this->_getFeature(array_keys($arrThreadTime));
            //特征加权
            foreach($arrTimeScore as $tid => $score){
                if ($arrQualityScore[$tid] == 0){
                    continue;
                }
                //$arrScore[$tid] = $arrNlpScore[$tid]*0.2 + $arrTimeScore[$tid]*0.4 + $arrQualityScore[$tid]*0.4;
                $arrScore[$tid] = $arrTimeScore[$tid]*0.5 + $arrQualityScore[$tid]*0.5;
            }
            arsort($arrScore);
            $count = 0;
            foreach ($arrScore as $tid => $score) {
                //echo "$tid\t".$arrThreadTitle[$tid]."\n";
                $arrOut[] = $arrThreadInfo[$tid];
                $count++;
                if ($count >=10 ){
                    break;
                }
            }
        }
        // echo "\n";
        return array("errno"=>Tieba_Errcode::ERR_SUCCESS,"data"=>$arrOut);
    }

}



?>
