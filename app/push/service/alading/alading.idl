//@description: 提供阿拉丁推送
//@author:  xiongweibupt
struct cm_res{
            uint32_t errno;//错误号
            string errmg;  //错误信息
            string data;    //返回数据
};


struct ueg_res{
            uint32_t errno;//错误号
            string errmg;  //错误信息
};

struct uegDelete{
            uint32_t thread_ids[];//帖子id数组
            uint32_t time;  //时间
            string   time_token;  //时间token
};

service Alading {

        /**
         * @brief alading接nmq转发命令的处理
         *
     * @param [in] req  :mc_pack_text : nmq发过来的二进制包
         * @return  void 
         * @retval   
         * @see 
         * @note  
         * <AUTHOR>
         * @date 2013/09/08 15:36:46
         **/
        void aladingNmqRequest(mc_pack_text req, out cm_res res);


    /**
     * @brief 接收ueg提交的删帖命令
     *
     * @return  void 
     * @retval   
     * @see 
     * @note 
     * <AUTHOR>
     * @date 2013/09/08 20:49:54
    **/



    /**
     * @brief 
     *
     * @param [in/out] req   : uegDelete  : ueg提供参数
     * @param [in/out] res   : out ueg_res : ueg返回结果 
     * @return  void 
     * @retval   
     * @see 
     * @note 
     * <AUTHOR>
     * @date 2013/09/08 20:53:50
    **/
    void uegDeleteVippingPs(uegDelete req, out ueg_res res)

}