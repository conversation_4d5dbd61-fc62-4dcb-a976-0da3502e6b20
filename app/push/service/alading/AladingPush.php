<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file AladingPush.php
 * <AUTHOR>
 * @date 2013/06/07 19:58:43
 * @update <EMAIL>
 * @brief �����������������ӵ�service
 *  
 **/

class Service_Alading_AladingPush {
    
    private static $_starlist = array();

    // ����������ʽmint�洢ͨ·С��������
    const NEWSTYLE_MINT_FID_ARRAY_KEY = 'newstyle_mint_fid_array_key';
    private static $_newstyleMintFidArray = array();

    /**
     * �������������鿨������
     */
    private static function _getStarlist()
    {
        // var_dump(dirname(__file__) . "/../../script/new/data/star_whitelist.txt");
        $star_fn = dirname(__file__) . "/../../script/new/data/star_whitelist_remote.txt";
        $handle = fopen($star_fn, "r");
        if (!$handle) {
            $star_fn = dirname(__file__) . "/../../script/new/data/star_whitelist.txt";
            $handle = fopen($star_fn, "r");
            if (!$handle) {
                Bingo_Log::warning("read two star whitelist [$star_fn] all failed.");
                return false;
            }
        }

        // �ļ����� utf-8 ����
        while (($line = fgets($handle)) !== false) {
            $line = str_replace("\n", "", $line);
            // var_export($line);
            self::$_starlist[] = $line;
        }

        fclose($handle);
        // var_export(self::$_starlist);
        Bingo_Log::notice("read star whitelist num: " . count(self::$_starlist));
    }

    private static function _isStarForum($fname){
        // var_export($fname);
        foreach(self::$_starlist as $key => $star){
            if ($fname == $star){
                return true;
            }
        }

        return false;
    }

    private static function _getNewStyleMintFidArray(){
        $handleWordServer = Wordserver_Wordlist::factory();
        $arrItemInfo = $handleWordServer->getValueByKeys(array(self::NEWSTYLE_MINT_FID_ARRAY_KEY), 'tb_wordlist_redis_natural_search_tuku');
        self::$_newstyleMintFidArray = unserialize($arrItemInfo[self::NEWSTYLE_MINT_FID_ARRAY_KEY]);

        // var_export(self::$_newstyleMintFidArray);
        Bingo_Log::notice("get newstyle mint fid num: " . count(self::$_newstyleMintFidArray));
    }

    /**
     * ���ݴʱ��ж��Ƿ�ִ�а���������ʽmintͨ·�����߼�
     * 1������ʱ��һ�� < 0����ͳһ������ͨ·���£��������������֤����������ȫʱ��Ҫ�������ߣ�
     * 2������ʱ�Ϊ�գ���ȷƥ��ʱ������ݣ�ƥ��ʱ��ִ������ʽmintͨ·��
     */
    private static function _hintNewStyleMint($fid) {
        // return true;
        if (is_array(self::$_newstyleMintFidArray) && count(self::$_newstyleMintFidArray) > 0 && self::$_newstyleMintFidArray[0] < 0) {
            return true;
        }
        foreach(self::$_newstyleMintFidArray as $key => $mintFid){
            if ($fid == $mintFid){
                return true;
            }
        }
        return false;
    }


    /** �����յ�ɾ������󣬽���һϵ�д���
     * @brief 
     *
     * @param [in/out] req  :
     * @return  void 
     * @retval   
     * @see 
     * @note  
     * <AUTHOR>
     * @date 2013/06/14 15:36:46
     **/
    public static function delPush($intForumId, $type) 
    {
    	//������id�ڲ���forumHash��,���û�У��Ͳ��䵽forumHash��
        if(!self::isExist($intForumId)) 
        {
            return true;
        }

		//������id�ڲ���forumSet��
        if(null == Dl_Alading_Alading::zscore('forumSet',$intForumId))
        {
            return true;
        }

        //����aldForumHash��frs�;�Ʒ��������Ϣ�������²���
        $ret = Service_Alading_Forum::updateThreadList($intForumId, null);
        if(false === $ret) 
        {
            Bingo_Log::warning("update ThreadList failed! input[$intForumId]");
        }
        //ɾ������ʽ����set��delSortedSort��
        $ret = Dl_Alading_Alading::addSortedSet(Dl_Alading_Alading::DEL_SORTEDSET, $intForumId);
        if(false === $ret) 
        {
            Bingo_Log::warning("add forumId into delSortedSet failed! input[$intForumId]");
            return false;
        }
        return true;
    }

    /**
     * [genPushXmlNew description]
     * @param 
     * @return  [description]
     */
    public static function genPushXmlNew($arrForumList,$arrConfig=array()) 
    {
        $arrXmlNew = array();
        foreach($arrForumList as $intForumId) 
        {
            $res_novel = Dl_Alading_Alading::getHashInfo('push_alading_novel_2018','',array($intForumId,));
            if (isset($res_novel[$intForumId]) && !empty($res_novel[$intForumId])){
                $arrNovel = json_decode($res_novel[$intForumId],true);
                if ($arrNovel['forumId'] == $intForumId){
                    $arrItemXml = Service_Alading_AladingXmlTemplateFrame::getXml($arrNovel,array('novel_extend' => 1));
                    $arrXmlNew = array_merge($arrXmlNew, $arrItemXml);
                }
            }

            
            /*$handleWordServer = Wordserver_Wordlist::factory();
            $wordItem = $handleWordServer->getValueByKeys(array($intForumId),"aladdin_forum_change");
            $bolchange = false;
            //����һ���ǰѰ���ǿ���滻��һ���߼�
            if(isset($wordItem[$intForumId]) && intval($wordItem[$intForumId])>0)
            {
                $bolchange = true;
                $arrConfig['topic'] = false;
                $forum_name_change = Libs_Rpc_Forum::getFnameByFid($intForumId);
                $intForumId = intval($wordItem[$intForumId]);
            }
            //��ȡ��xml��������������Ϣ
            $arrPushInfo = Service_Alading_AladingThreadFrame::getPushInfo($intForumId, $arrConfig);
            if(false === $arrPushInfo) 
            {
                Bingo_Log::warning("getPushInfo failed! input [$intForumId]");
                continue;
            }
            
            if($bolchange)
            {
                $arrPushInfo['forum']['forum_name_change'] = $forum_name_change;
            }
            $arrPushInfo['forum']['forum_id'] = $intForumId;
            $strForumName = isset($arrPushInfo['forum']['forum_name'])?strval($arrPushInfo['forum']['forum_name']):'';            
            $forumNameItem = $handleWordServer->getValueByKeys(array($strForumName),"aladdin_forum_name_change");
            
            if (isset($forumNameItem[$strForumName]) && strlen($forumNameItem[$strForumName]) > 0)
            {
                $arrPushInfo['forum']['forum_name'] = strval($forumNameItem[$strForumName]);
            }
            //��ȡ�ɸ���xml
            $arrItemXml = array();
            $arrItemXml = Service_Alading_AladingXmlTemplateFrame::getXml($arrPushInfo,$arrConfig);
            if(empty($arrItemXml)) 
            {
                Bingo_Log::warning("genItemXml failed! input [$intForumId]");
                continue;
            }
            //����������Ϣ
            $ret = Service_Alading_Forum::updatePushInfo($intForumId, $arrPushInfo);
            if(false === $ret) {
                Bingo_Log::warning("update PushInfo failed! input[$intForumId]");
                //continue;
            }
            $arrXmlNew = array_merge($arrXmlNew, $arrItemXml); */
        }
        return $arrXmlNew;
    }

    /** 
     * @brief  ���ɰɸ�������xml,service�ӿ�genPushXml
     *
     * @param [in/out] $arrForumList  : $arrForumList ��ID�б�
     * @param [in/out] $topic         : �Ƿ�֧�������
     * @return  void 
     * @retval   
     * @see 
     * @note  
     * <AUTHOR>
     * @date 2014/02/14 15:36:46
     **/
    public static function genPushXml($arrForumList,$arrConfig=array()) 
    {
        self::_getStarlist();
        self::_getNewStyleMintFidArray();

        $arrXml = array();
        //ͨ���ʱ�������߰�����
        $arrDelForumId = array();
        $handleWordServer = Wordserver_Wordlist::factory();
        $arrItemInfo = $handleWordServer->getValueByKeys(array('delForumId'), 'tb_wordlist_redis_aladdin_delForum');
        $arrTmp = unserialize($arrItemInfo['delForumId']);
        foreach ($arrTmp as $key => $value) {
            $arrDelForumId[] = intval($value);
        }
        Bingo_Log::notice("skip forum list=".Bingo_String::array2json($arrDelForumId));

        foreach($arrForumList as $intForumId) 
        {
        	$testNmqList = array(0,3,35,271650,303336,);
            if(in_array($intForumId, $testNmqList))
            {
                continue;
            }
            if(in_array($intForumId, $arrDelForumId)){
                Bingo_Log::notice("skip forum id=".$intForumId);
                continue;
            }
            //�ѻ�����
            $handleWordServer = Wordserver_Wordlist::factory();
            $strTmp = $handleWordServer->getValueByKeys(array('youhuaId'), 'tb_wordlist_redis_aladdin_youhua_switch');
            $switch = intval($strTmp['youhuaId']);//switch��ʾҪ�л������ɰ������İ�id,Ϊ0ʱȫ���л������ɰ�����
            if($switch && $intForumId != $switch){
                $res = Dl_Alading_Alading::getHashInfo('push_alading_youhua_2018','',array($intForumId,));
                if (isset($res[$intForumId]) && !empty($res[$intForumId]) ){
                    $arrYouhua = Bingo_String::json2array($res[$intForumId]);
                    if ($arrYouhua['forumId'] == $intForumId){
                        $arrItemXml = Service_Alading_AladingXmlTemplateFrame::getXml($arrYouhua,array('youhua_extend' => 1));
                        Bingo_Log::notice("youhua forum list=".$switch);
                        $arrXml = array_merge($arrXml, $arrItemXml);
                        continue;
                    }
                }
            }



            $handleWordServer = Wordserver_Wordlist::factory();
        	$wordItem = $handleWordServer->getValueByKeys(array($intForumId),"aladdin_forum_change");
        	$bolchange = false;
        	//����һ���ǰѰ���ǿ���滻��һ���߼�
        	if(isset($wordItem[$intForumId]) && intval($wordItem[$intForumId])>0)
        	{
        		$bolchange = true;
        		$arrConfig['topic'] = false;
        		$forum_name_change = Libs_Rpc_Forum::getFnameByFid($intForumId);
        		$intForumId = intval($wordItem[$intForumId]);
        	}
            //��ȡ��xml��������������Ϣ
            $arrPushInfo = Service_Alading_AladingThreadFrame::getPushInfo($intForumId, $arrConfig);
            if(false === $arrPushInfo) 
            {
                Bingo_Log::warning("getPushInfo failed! input [$intForumId]");
                continue;
            }

            // Note��ע�⵽���൱�����İ����� xml ʧ�ܻ� xml schema У��ʧ������Ϊ arrPushInfo.thread ���������ɵ���
            // ���������ǰ contine��Ҳ�����Ų���� xml ����/У��ʧ������Ϊʲôԭ���¡�
            if (!is_array($arrPushInfo['thread']) || count($arrPushInfo['thread']) < 3) {
                // Bingo_Log::warning("ald_push fid[$intForumId] thread less 3, info : " . serialize($arrPushInfo['thread']));
                Bingo_Log::warning("ald_push fid[$intForumId] thread less 3");
                continue;
            }

            // var_export($arrPushInfo['forum']);
            // ����������ʽ���ߴ洢
            // $fnameUtf8 = Service_Alading_StarGroupCard_SiteAggregate::strToUTF8($arrPushInfo['forum']['forum_name']);
            // if (self::_isStarForum($fnameUtf8) === false){
            //     // Service_Alading_StarGroupCard_SiteAggregate::AladdinNewStyle($intForumId, $fnameUtf8, $arrPushInfo);
            // } 


            // Note: �����ټ��ϴʱ��ذɣ����㰢��������ʽ����mint�洢ͨ·��С�������顣
            $arrNewStyleXmlInfo = null;
            if (self::_hintNewStyleMint($arrPushInfo['forum']['forum_id'])) {
                Bingo_Log::notice('ald_newstyle begin add xmltemplate, fid: ' . $arrPushInfo['forum']['forum_id']);
                $arrNewstyleInfo = Service_Alading_Xmltemplate_Wise_NewStyleTemplate::AladdinNewStyle($arrPushInfo);

                if (!empty($arrNewstyleInfo)) {
                    $arrNewStyleXmlInfo = Service_Alading_Xmltemplate_Wise_NewStyleTemplate::buildNewStyleTemplate($arrNewstyleInfo);
                }

            }

            //����ʵ��ģ��
            $sample = array();

            if($arrPushInfo['sample']){

                $sample = Service_Alading_Xmltemplate_Wise_SampleTemplate::execute($arrPushInfo);

            }

            if($bolchange)
            {
            	$arrPushInfo['forum']['forum_name_change'] = $forum_name_change;
            }
            $arrPushInfo['forum']['forum_id'] = $intForumId;
            $strForumName = isset($arrPushInfo['forum']['forum_name'])?strval($arrPushInfo['forum']['forum_name']):'';            
            $forumNameItem = $handleWordServer->getValueByKeys(array($strForumName),"aladdin_forum_name_change");
            
            if (isset($forumNameItem[$strForumName]) && strlen($forumNameItem[$strForumName]) > 0)
            {
            	$arrPushInfo['forum']['forum_name'] = strval($forumNameItem[$strForumName]);
            }
            
            //��ȡ�ɸ���xml
            $arrItemXml = array();
            $dir1 = isset($arrPushInfo['forum']['dir1'])?$arrPushInfo['forum']['dir1']:'';
            $intIsFirmForum = isset($arrPushInfo['forum']['is_online_firm_forum'])?intval($arrPushInfo['forum']['is_online_firm_forum']):0;


            // $arrNewStyleXmlInfo = null;
            $arrItemXml = Service_Alading_AladingXmlTemplateFrame::getXml($arrPushInfo,$arrConfig, $arrNewStyleXmlInfo,$sample);
            if(empty($arrItemXml)) 
            {
                Bingo_Log::warning("genItemXml failed! input [$intForumId]");
                continue;
            }

            // var_export($arrItemXml);
            // Bingo_Log::warning(var_export($arrItemXml, true));
            //����������Ϣ
            $ret = Service_Alading_Forum::updatePushInfo($intForumId, $arrPushInfo);
            if(false === $ret) {
                Bingo_Log::warning("update PushInfo failed! input[$intForumId]");
                //continue;
            }
            $arrXml = array_merge($arrXml, $arrItemXml);
        }
        //Bingo_Log::notice("xml=".Bingo_String::array2json($arrXml));
        return $arrXml;
    }

    /** 
     * @brief ����ȡ������xml
     *
     * @param [in/out] req  : $arrForumList ��ID�б�
     * @return  void 
     * @retval   
     * @see 
     * @note  
     * <AUTHOR>
     * @date 2013/06/14 15:36:46
     **/
    public static function genCancelPushXml($arrForumList) {
        $strXml = "";
        foreach($arrForumList as $intForumId) {
            //��ȡ������Ϣ
            $arrPushInfo = Service_Alading_Forum::getCancelPushInfo($intForumId);
            if(false === $arrPushInfo) {
                Bingo_Log::warning("get Cancel PushInfo failed! input [$intForumId], error:".
                                    Tieba_Errcode::ERR_ALADING_PUSHINFO);
                continue;
            }
            //����xml
            $strTmpXml = Service_Alading_XmlOutput::genCancelItemXml($arrPushInfo);
            if(strlen($strTmpXml) == 0) {
                continue;
            }

            $checkRes = Service_Alading_AladingXmlTemplateFrame::_checkXmlSchema($strTmpXml, $intForumId);
            if($checkRes === false){
                continue;
            }

            $hashRet = Dl_Forumhash::delHash($intForumId);
            if(false == $hashRet)
            {
                Bingo_Log::warning("del Forumhash fail forumId:".$intForumId);
                continue;
            }
            Bingo_Log::notice("del Forumhash success forumId:".$intForumId);

            //����������Ϣ
            $ret = Service_Alading_Forum::updateCancelPushInfo($intForumId);
            if(false === $ret) {
                Bingo_Log::warning("update cancelPushInfo failed! input[$intForumId], error:".
                                Tieba_Errcode::ERR_ALADING_PUSHINFO);
                continue;
            }
            $strXml .= $strTmpXml;
        }
        if(strlen($strXml) > 0) {
            $strXml = Service_Alading_XmlOutput::getXml($strXml);
        }
        return $strXml; 
    }

    /** 
     * @brief  �ж�forumHash�Ƿ��иð�,���޲��������id��Ϣ��forumhash
     * @return  void 
     * @retval   
     * @see 
     * @note  
     * <AUTHOR>
     * @date 2013/06/14 15:36:46
     **/
    public static function isExist($intForumId) {
        $bolIsExist = Service_Alading_Forum::isExist($intForumId);
        if($bolIsExist) {
            return true;
        }
        $ret = Service_Alading_Forum::updateForum($intForumId);
        if(false === $ret) {
            Bingo_Log::warning("forum is not existed in forumHash! input[$intForumId]");
        }
        return false;
    }

    /** 
     * @brief  �ж��Ƿ�Ҫ���͸ð�
     *
     * @param [in/out] req  : $intForumId ��ID
     * @return  void 
     * @retval   
     * @see 
     * @note  
     * <AUTHOR>
     * @date 2013/06/14 15:36:46
     **/
    public static function isPush($intForumId) {
        return Service_Alading_Forum::isPush($intForumId);
    }
    
    /** 
     * @brief �Ѿ����͵İɲ��������Ͳ���ȡ������
     *
     * @param [in/out] req  : $intForumId ��ID
     * @return  void 
     * @retval   
     * @see 
     * @note  
     * <AUTHOR>
     * @date 2013/06/07 15:36:46
     **/
    public static function cancelPush($intForumId) {
        //����Ѿ������ͣ���ȡ������
        /*$isPushed = Service_Alading_Forum::isPushed($intForumId);
        if($isPushed) {
            */
            //����ȡ�����Ͱ�set��
            $ret = Dl_Alading_Alading::addSortedSet(Dl_Alading_Alading::DEL_FORUM_SORTSET, $intForumId);
            
            if(false === $ret) {
                Bingo_Log::warning("add cancelPush forum failed! input[$intForumId]");
                return false;
            }
            Bingo_Log::trace("add cancelPush forum success! input[$intForumId]");
        //}
        return true;
    }

    /** 
     * @brief  �ӵ�maskUser�����Ĵ���
     *
     * @return  void 
     * @retval   
     * @see 
     * @note  
     * <AUTHOR>
     * @date 2013/06/14 15:36:46
     **/
    public static function maskUser($intUserId) {
        //��ӵ�maskUserSet��
        /*
        $ret = Dl_Alading_Alading::set(Dl_Alading_Alading::MASKUSER_SET. $intUserId, 1);
        if(false === $ret) {
            Bingo_Log::warning("add maskUser into maskUserSet failed! input[$intUserId], error:".
                                Tieba_Errcode::ERR_ALADING_TALK_REDIS);
            return false;
        }
        */
        //����ȥ�������ӷ���û���user list��ÿ��10������uidȡģ��
        $input = array(
                'key' => "maskUserlistnew".date("Ymd"). $intUserId%10,
                'value' => $intUserId,
            );
        /*$res_all = Dl_Alading_Alading::rangeList($input['key'],0,-1);
        if(in_array($intUserId,$res_all)==false)
        {
            Bingo_Log::warning("set maskUserredislist".var_export($input,true));
            $ret2 = Dl_Alading_Alading::pushList($input['key'], $input['value']);
            if(false === $ret2) {
                Bingo_Log::warning("set redis maskUserlist failed! ");
                return false;
            }
        }*/
        Libs_Util_Statics::incr("inner_search_push_stat_maskUser_push_". date("Ymd",time()));
        $ret2 = Dl_Alading_Alading::addSet($input['key'],$input['value']);
        if(false === $ret2) {
            Bingo_Log::warning("set redis maskUserlistnew failed! ");
            return false;
        }
        //���͸��û����ӵİ���Ҫ��������
        //$arrRet = Dl_Alading_Alading::getSetMembers(Dl_Alading_Alading::USER_FORUM_SET.$intUserId);
        //if(false === $arrRet) {
        //    Bingo_Log::warning("get maskUser pushed forum failed! input[$intUserId]");
        //    return false;
        //}
        //foreach($arrRet as $intForumId) {
        //    $ret = self::delPush($intForumId, Service_Alading_AladingConfig::DEL_PUSH);
        //    if(false === $ret) {
        //        Bingo_Log::warning("updatePush failed! input[forum:".$intForumId."\tuser:$intUserId"."]");
        //        return false;
        //    }
        //}
        return true;
    }
    
    /** 
     * @brief  �ӵ�unMaskUser�����Ĵ���
     *
     * @param [in/out] req  : $arrForumList ��ID�б�
     * @return  void 
     * @retval   
     * @see 
     * @note  
     * <AUTHOR>
     * @date 2013/06/14 15:36:46
     **/
    public static function unMaskUser($intUserId) {
        //��maskUserSet��ɾ�����û�
        Libs_Util_Statics::incr("inner_search_push_stat_unMaskUser_push_". date("Ymd",time()));
        $ret = Dl_Alading_Alading::delKey(Dl_Alading_Alading::MASKUSER_SET. $intUserId);
        if(false == $ret) {
            Bingo_Log::warning("remove maskUser from maskUserSet failed! input[$intUserId]");
        }
        return $ret;
    }
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
