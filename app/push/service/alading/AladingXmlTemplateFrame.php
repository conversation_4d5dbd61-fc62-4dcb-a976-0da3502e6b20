<?php
/**
 * XML�������ģ�� 
 * <AUTHOR>
 *
 */
class Service_Alading_AladingXmlTemplateFrame
{
	public static function getXml($arrPushInfo, $arrConfig = array(), $arrNewStyleInfo = null,$sample = array())
	{
		
		if (isset($arrConfig['youhua_extend']) && $arrConfig['youhua_extend'] == 1){
			$arrItem = Service_Alading_Xmltemplate_Wise_YouhuaTemplate::execute($arrPushInfo);
			$arrExtend = Service_Alading_Xmltemplate_PC_YouhuaTemplate::execute($arrPushInfo);
			$arrItem['display']['extend']['pcextattr'] = $arrExtend;

			$itemXml = self::_buildItemXml($arrItem);
			$checkRes = self::_checkXmlSchema($itemXml, 0);
			$arrItemXml = array();
			if (false !== $itemXml && false !== $checkRes)
			{
				$arrItemXml[] = $itemXml;
			}
			return $arrItemXml;
		}

        if (isset($arrConfig['novel_extend']) && $arrConfig['novel_extend'] == 1){
            $arrItem = Service_Alading_Xmltemplate_Wise_NovelTemplate::execute($arrPushInfo);
            $arrExtend = Service_Alading_Xmltemplate_PC_NovelpcTemplate::execute($arrPushInfo);
            $arrPushInfo['extattr'] = 'wise';
            $arrWiseExtend = Service_Alading_Xmltemplate_PC_NovelpcTemplate::execute($arrPushInfo);
            $arrItem['display']['extend']['pcextattr'] = $arrExtend; 
            $arrItem['display']['extend']['wiseextattr'] = $arrWiseExtend;
            $itemXml = self::_buildItemXml($arrItem);
            $checkRes = self::_checkXmlSchema($itemXml, 0);
            $arrItemXml = array();
            if (false !== $itemXml && false !== $checkRes)
            {
                $arrItemXml[] = $itemXml;
            }
            return $arrItemXml;
        }

		if (!is_array($arrPushInfo) || empty($arrPushInfo) 
			|| !isset($arrPushInfo['forum']) || !is_array($arrPushInfo['forum'])
			|| empty($arrPushInfo['forum']))
		{
			Bingo_Log::warning("getXml input param error,the param is".print_r($arrPushInfo,true));
			return false;
		}

		// ���ɻ�����Ϣ
		$arrItem = Service_Alading_Xmltemplate_CommonTemplate::execute($arrPushInfo);
		//var_dump($arrItem);
        // Bingo_Log::notice(var_export($arrPushInfo, true));
		
		$arrForumInfo = $arrPushInfo['forum'];
		
		// �ж�PC��չ��Ϣ
		$arrPCCallBackFun = self::_pcTemplateAdapter($arrForumInfo, $arrConfig);
		Bingo_Log::pushNotice("pc_strategy_name", $arrPCCallBackFun);
		//echo $arrCallBackFun;
		if (false !== $arrPCCallBackFun)
		{
			// ִ��ģ�壬��ȡ������Ϣ
			$arrPCFun = array($arrPCCallBackFun, "execute");
			$arrPCExtend = call_user_func($arrPCFun, $arrPushInfo);			
		    
			if (false !== $arrPCExtend)
			{
				$arrItem['display']['extend']['pcextattr'] = $arrPCExtend;
			}			
		}
		
		// �ж�WISE��չ��Ϣ
		$arrWiseCallBackFun = self::_wiseTemplateAdapter($arrForumInfo, $arrConfig);
		Bingo_Log::pushNotice("wise_strategy_name", $arrWiseCallBackFun);
		//echo $arrCallBackFun;
		if (false !== $arrWiseCallBackFun)
		{
			// ִ��ģ�壬��ȡ������Ϣ
			$arrWiseFun = array($arrWiseCallBackFun, "execute");
			$arrWiseExtend = call_user_func($arrWiseFun, $arrPushInfo);
		
			if (false !== $arrWiseExtend)
			{
				$arrItem['display']['extend']['wiseextattr'] = $arrWiseExtend;
			}
		}

		// var_export($arrItem);
		
		$arrItemXml = array();	
		//��ȡ�����
		$arrTopicWord = array();
		if(true === $arrConfig["topic"])
		{
			if(isset($arrForumInfo['topic_words']) && strlen($arrForumInfo['topic_words']) > 0) 
			{
				$arrTopicWord = explode("\t", $arrForumInfo['topic_words']);
			}
			if(isset($arrForumInfo['query_words']) && strlen($arrForumInfo['query_words']) > 0) 
			{
				$arrQueryWord = explode("\t", $arrForumInfo['query_words']);
				$arrTopicWord = array_unique(array_merge($arrTopicWord, $arrQueryWord));
			}		
		}
		
		$arrTopicWord[] = isset($arrForumInfo['forum_name_change'])?$arrForumInfo['forum_name_change']:$arrForumInfo['forum_name'];
		//��Ǯ���ɱ��ϲ����ٶ���Ǯ������ʱӦ��������Ǯ������Ϣ. PM wangzixing
		if($arrForumInfo['forum_id'] == 18891909){
			$arrTopicWord[] = Molib_Util_Encode::convertUTF8ToGBK("��Ǯ��");
			Bingo_Log::Warning('push youqianhua') ;
		}
		foreach($arrTopicWord as $word) 
		{
			$itemChange = self::_changeKeyAndTitle($arrItem, $word);
			
			if (false === $itemChange)
			{
				continue;
			}

            if(!empty($sample)){

                $itemChange['display']['sample'] = $sample['sample'];

			}

			if (!empty($arrNewStyleInfo)) {
				$itemChange['display']['other_tpl_data'] = $arrNewStyleInfo;
			}

			$itemXml = self::_buildItemXml($itemChange);
            // Bingo_Log::warning(var_export($itemChange, true));
        	// Bingo_Log::warning(var_export($itemXml, true));
			$checkRes = self::_checkXmlSchema($itemXml, $arrForumInfo['forum_id']);

			if (false !== $itemXml && false !== $checkRes)
			{
				$arrItemXml[] = $itemXml;
			}
		}
		//var_dump($arrItemXml);
		return $arrItemXml;
	}

	/**
	 * @param string
	 * @return bool
	 */
	public static function _checkXmlSchema($itemXml, $forum_id){

		if(!$itemXml){
			Bingo_Log::warning('param error, itemXml is null, forum_id: '.$forum_id);
			//email
			$arrInput['content'] = 'param error, itemXml is null, forum_id: '.$forum_id;
			$arrInput['title'] = 'AladingXmlTemplateFrame';
			Libs_Util_Report::sendMail($arrInput);
			return false;
		}
		$itemXml = Service_Alading_XmlOutput::getXml($itemXml);

		$xmlSchemaPath = dirname(__File__).'/tieba2.xsd';
		// Bingo_Log::notice("xmlpath: $xmlSchemaPath");

		$xml = new DOMDocument();
		libxml_use_internal_errors(true);
		$xml->loadXML($itemXml);
		if(!$xml->schemaValidate($xmlSchemaPath)){
			self::libxml_display_errors($forum_id);
			return false;
		}

		return true;
	}

	/**
	 * @param string
	 * @return string
	 */
	private static function libxml_display_error($error)
	{
		$return = "";
		switch ($error->level) {
			case LIBXML_ERR_WARNING:
				$return .= "Warning $error->code: ";
				break;
			case LIBXML_ERR_ERROR:
				$return .= "Error $error->code: ";
				break;
			case LIBXML_ERR_FATAL:
				$return .= "Fatal $error->code: ";
				break;
		}
		$return .= trim($error->message);
		/*if ($error->file) {
			$return .=    " in $error->file";
		}  */
		$return .= " on line $error->line";
		$return .= " column $error->column";

		return $return;
	}
	/**
	 * @param string
	 * @return bool
	 */
	private static function libxml_display_errors($forum_id) {
		$errors = libxml_get_errors();
		foreach ($errors as $error) {
			$warning =  self::libxml_display_error($error);
			Bingo_Log::warning('forum_id: '.$forum_id.'  error info: '.$warning);
			$arrInput['content'] .=  'forum_id: '.$forum_id.'  error info: '.$warning."\n";
		}
		//email
		$arrInput['title'] = 'AladingXmlTemplateFrame';
		//Libs_Util_Report::sendMail($arrInput);
		libxml_clear_errors();
		return true;
	}
		
	/**
	 * ��ȡ�ɶ�Ӧ��PCģ������
	 * @param unknown_type $arrForumInfo  ����Ϣ
	 * @param unknown_type $arrConfig  ���ò�������ʱ����
	 * @return boolean|string
	 */
	private static function _pcTemplateAdapter($arrForumInfo, $arrConfig = array())
	{
		//var_dump($arrForumInfo);
		if (!is_array($arrForumInfo) || empty($arrForumInfo))
		{
			Bingo_Log::warning("input param is error");
			return false;
		}
		
		$firmFun = self::_firmTemplateAdapter($arrForumInfo);
		
		if (false !== $firmFun)
		{
			return $firmFun;
		}
		
		/*
		$pcCommonNew = self::_pcCommonNewForumTemplateAdapter($arrForumInfo);
		
		if (false !== $pcCommonNew)
		{
			return $pcCommonNew;
		}		
	    */
	
		$strDir1 = isset($arrForumInfo['dir1'])?strval($arrForumInfo['dir1']):'';
		$strategyFun = self::_pcDir1TemplateAdapter($strDir1);
		return $strategyFun;
	}
	
	/**
	 * ��ȡ�ɶ�Ӧ��wiseģ������
	 * @param unknown_type $arrForumInfo  ����Ϣ
	 * @param unknown_type $arrConfig  ���ò�������ʱ����
	 * @return boolean|string
	 */
	private static function _wiseTemplateAdapter($arrForumInfo, $arrConfig = array())
	{
		//var_dump($arrForumInfo);
		if (!is_array($arrForumInfo) || empty($arrForumInfo))
		{
			Bingo_Log::warning("input param is error");
			return false;
		}
	
		$strDir1 = isset($arrForumInfo['dir1'])?strval($arrForumInfo['dir1']):'';
		$strategyFun = self::_wiseDir1TemplateAdapter($strDir1);
		return $strategyFun;
	}
	
	/**
	 * �ٷ��ɲ���
	 * @param unknown_type $arrForumInfo
	 * @return boolean|string
	 */
	private static function _firmTemplateAdapter($arrForumInfo)
	{
		if (!is_array($arrForumInfo) || empty($arrForumInfo))
		{
			Bingo_Log::warning("input param is error");
			return false;
		}
		
		$intOnlineFirmForum = isset($arrForumInfo['is_online_firm_forum'])?intval($arrForumInfo['is_online_firm_forum']):0;
		
		$intBlueIcon = isset($arrForumInfo['is_blueicon'])?intval($arrForumInfo['is_blueicon']):0;
		//����yunting��������󣬹ٷ�����ʽ��Ҫͬʱ������Ӫ�̵�״̬�Ϳ�����V��2014/12/29
		if (1===$intOnlineFirmForum && 1===$intBlueIcon)
		{
			return "Service_Alading_Xmltemplate_PC_FirmTemplate";
		}
		
		return false;
	}
	
	/**
	 * ��Ŀ¼ά�Ȼ�ȡģ��������
	 * @param unknown_type $strDir1
	 * @return string
	 */
	private static function _pcDir1TemplateAdapter($strDir1)
	{
		// ����ͨ�ò���
		if ('' === $strDir1)
		{
			return "Service_Alading_Xmltemplate_PC_Dir1CommonNewTemplate";
		}
		
		// ��������
		if (in_array($strDir1, Service_Alading_Strategy_ForumStgyConfig::$STAR_DIR1_LIST))
		{
			return "Service_Alading_Xmltemplate_PC_Dir1StarTemplate";
		}
	
		// ��ѧ
		if (in_array($strDir1, Service_Alading_Strategy_ForumStgyConfig::$STORY_DIR1_LIST))
		{
			return "Service_Alading_Xmltemplate_PC_Dir1StoryTemplate";
		}
				
		return "Service_Alading_Xmltemplate_PC_Dir1CommonNewTemplate";
	}
	
	private static function _pcCommonNewForumTemplateAdapter($arrForumInfo)
	{
		$strForumName = isset($arrForumInfo['forum_name']) 
		    ? strval($arrForumInfo['forum_name']) : '';
		
		if (in_array($strForumName, Service_Alading_Strategy_ForumStgyConfig::$COMMON_NEW_TEST_FORUM_LIST))
		{
			return "Service_Alading_Xmltemplate_PC_Dir1CommonNewTemplate";
		}

		return false;
	}
	
	/**
	 * ��Ŀ¼ά�Ȼ�ȡģ��������
	 * @param unknown_type $strDir1
	 * @return string
	 */
	private static function _wiseDir1TemplateAdapter($strDir1)
	{
		// ����ͨ�ò���
		if ('' === $strDir1)
		{
			return false;
		}
	
		// ��������
		if (in_array($strDir1, Service_Alading_Strategy_ForumStgyConfig::$STAR_DIR1_LIST))
		{
			return "Service_Alading_Xmltemplate_Wise_Dir1StarTemplate";
		}

        return "Service_Alading_Xmltemplate_Wise_DirTempTemplate";
//		return false;
	}
	
	/**
	 * ��������ʽ�key��title����
	 * @param unknown_type $arrItem
	 * @param unknown_type $key
	 * @return boolean|multitype:unknown
	 */
	private static function _changeKeyAndTitle($arrItem, $key)
	{
		if (!is_array($arrItem) || empty($arrItem) || '' === $key)
		{
			Bingo_Log::warning('_changeKeyAndTitle input param error'.print_r($arrItem,true).print_r($key, true));
			return false;
		}
        $key = Libs_Util_String::transAscii2Char($key);
		$arrItem['key'] = array('@cdata' => $key);
		$arrItem['display']['title'] = array('@cdata' => $key);
		
		if (isset($arrItem['display']['extend']['pcextattr']['forum']['title']))
		{
			$arrItem['display']['extend']['pcextattr']['forum']['title'] = array('@cdata' => $key);
		}
		
		return $arrItem;
	}
	
	/**
	 * ����XML�ַ���
	 * @param unknown_type $arrItem
	 * @return boolean|string
	 */
	private static function _buildItemXml($arrItem)
	{
		if (!is_array($arrItem) || empty($arrItem))
		{
			Bingo_Log::warning('_buildItemXml input param error,'.print_r($arrItem, true));
			return false;
		}
		
		try
		{
			$itemXml = Libs_Util_Array2xml::createXML('item', $arrItem);
			$rootNode = $itemXml->getElementsByTagName('item');
			if ($rootNode->length > 0)
			{
				$strXml = $itemXml->saveXML($rootNode->item(0));
				//echo $strXml;
				return $strXml;
			}
			else
			{
				Bingo_Log::warning("_buildItemXml xml fail, the length is 0");
				return false;
			}
		}
		catch (Exception $e)
		{
			Bingo_log::warning("_buildItemXml exception,".print_r($e,r));
			return false;
		}
		
		return false;
	}
}
