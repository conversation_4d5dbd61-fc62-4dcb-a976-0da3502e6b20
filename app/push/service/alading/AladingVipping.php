<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file AladingVipping.php
 * <AUTHOR>
 * @date 2013/08/29 19:58:43
 * @brief 
 *  
 **/

class Service_Alading_AladingVipping {

	/**
	 * @brief ƴװredis key  extra��Ϊ��ģ��ʮ������ķ�ʽ����ʾ������ϵ
	 *
	 * @param [in/out] req  :
	 * @return  void
	 * @retval
	 * @see
	 * @note
	 * <AUTHOR>
	 * @date 2013/08/29 15:36:46
	 **/
	private static function  _getVippingSetKey($command_no,$thread_id,$post_id,$forum_id)
	{
		$key = "";
		$extraList ="";
		switch (intval($command_no)) {
			case Service_Alading_Alading::POST_COMMIT_CMD: //���ظ�
			    $key = $thread_id."_".Service_Alading_Alading::POST_COMMIT_CMD."_".$forum_id; 
			    $extraList = Dl_Alading_Alading::VIPPING_DELAY_POST_LIST_PREFIX.intval($thread_id);
				break;
			case Service_Alading_Alading::THREAD_COMMIT_CMD://������
				$key = $thread_id."_".Service_Alading_Alading::THREAD_COMMIT_CMD."_".$forum_id;
				break;
			case Service_Alading_Alading::DELETE_THREAD_CMD: //ɾ���ظ���ɾ�����ⲻ�����ﴦ��,
				if(intval($post_id)==0)
				{
					$key = false;
				}else {
					//����ֻ����ɾ���ظ���ɾ���ظ�����Ҫpost_id,��������ط���ɿӣ�
					$key = $thread_id."_".Service_Alading_Alading::DELETE_THREAD_CMD."_".$forum_id;
				}
				break;
			case Service_Alading_Alading::RECOVER_THREAD_CMD:
				if($post_id!=0)//������ָ��ظ�
				{
					$key = false;
				}else{
					$key = $thread_id."_".Service_Alading_Alading::RECOVER_THREAD_CMD."_".$forum_id;
				}
				break;
			case Service_Alading_Alading::CREATEFORUM_CMD:
				$key = $thread_id."_".Service_Alading_Alading::CREATEFORUM_CMD;
				break;
			default:
				;
			break;
		}
		Bingo_Log::pushNotice("setkey",$key);
		return array($key,$extraList);
	}
	
	
	/** 
	 * @brief ��ɾ�������첽���д���
	 *
	 * @param [in/out] req  :
	 * @return  void
	 * @retval
	 * @see
	 * @note
	 * <AUTHOR>
	 * @date 2013/08/29 15:36:46
	 **/
	public static function addVippingAsync($arrInput)
	{
		//// 2014.02.12 haoyunfeng Ϊ���µ���Ϣ�ӳ�5���ӣ��������첽�洢��
		return true;
		///////////////////////////////////////////////////
		
		/*$command_no = intval($arrInput['command_no']);
		$thread_id  = intval($arrInput['thread_id']);
		$post_id  = intval($arrInput['post_id']);
		$forum_id  = intval($arrInput['forum_id']);
		$forum_name = strval($arrInput['word']);
		
		Bingo_Log::pushNotice("command", $command_no);
		Bingo_Log::pushNotice("thread_id", $thread_id);
		Bingo_Log::pushNotice("post_id", $post_id);
		Bingo_Log::pushNotice("forum_id", $forum_id);*/
		
		
		/**
		 * ����У�飺�����ֻ������0,1,12,13��Χ�ڣ�����id�������0�����˽��ɵ�ʱ�򣬰����������
		 */
		/*if(!in_array($command_no,Service_Alading_AladingConfig::$arrVippingDelayCmd) ||intval($thread_id)<=0)
		{
			Bingo_Log::warning("addVippingAsync params check error!");
			return false;
		}*/
		
		//ɾ�����ⲻ�����ﴦ���ָ��ظ�������
		/*if(($command_no == 12 && $post_id==0) || ($command_no == 13 && $post_id!=0))
		{
			return true;
		}*/
		
		//��ȡsorted�洢�� key��
		/*list($key,$extraList)= self::_getVippingSetKey($command_no,$thread_id,$post_id,$forum_id);
		if(false === $key)
		{
			Bingo_Log::warning("get redis delay_sorted_set key failed!");
			return false;
		}  */
		//����sorted set ��ʱ��˳��
		/*$res = Dl_Alading_Alading::addSortedSet(Dl_Alading_Alading::VIPPING_DELAY_SORTED_SET, $key);
		if(false == $res)
		{
			Bingo_Log::warning(sprintf("addSortedSet failed,set [%s] key [%s]",Dl_Alading_Alading::VIPPING_DELAY_SORTED_SET,$key));
			return false;
		}*/
		//����Ƿ��ظ��Ļ�����Ҫ��ʮ������Ŷ
		/*if(!empty($extraList))
		{
			//�����key�����ڣ��ͼӹ���ʱ��
			//if(!Dl_Alading_Alading::isExistKey($extraList))
			//{
			//	$res = Dl_Alading_Alading::pushList($extraList, $post_id);
			//	Dl_Alading_Alading::expireKey($extraList);
			//}else{
			$res = Dl_Alading_Alading::pushList($extraList, $post_id);
			//}
		}*/
		/*if(false == $res)
		{
			Bingo_Log::warning(sprintf("addSortedSet extraList failed,set [%s] key [%s] post_id [%d]",Dl_Alading_Alading::VIPPING_DELAY_SORTED_SET,$key,$post_id));
			return false;
		}
		return true;*/
	}
	
	/**
	 * @brief ɾ������ͬ������
	 *
	 * @param [in/out] req  :
	 * @return  void
	 * @retval
	 * @see
	 * @note
	 * <AUTHOR>
	 * @date 2013/08/29 15:36:46
	 **/
	public static function doDeleteThreadSync($arrInput)
	{
		$thread_id   = intval($arrInput['thread_id']);
		$command_no  = intval($arrInput['command_no']);
		$forum_id    = intval($arrInput['forum_id']);
		$forum_name  = strval($arrInput['word']);
		$bolPs = true;
		
		/**
		 * ����У�飺�����ֻ������0,1,12,13��Χ�ڣ�����id�������0�����˽��ɵ�ʱ�򣬰����������
		 */
		if($command_no!=Service_Alading_Alading::DELETE_THREAD_CMD ||intval($thread_id)<=0)
		{
			Bingo_Log::warning("doDeleteThreadSync params check error!");
			return false;
		}
		
		$arrRemove = array();
		//����DELAY���������0,1,2,12,13
		foreach((array)Service_Alading_AladingConfig::$arrVippingDelayCmd as $cmd)
		{
			$key = $thread_id."_".$cmd;
			$isExists = Dl_Alading_Alading::zscore(Dl_Alading_Alading::VIPPING_DELAY_SORTED_SET,$key);
			//�������set�д������thread_id����������ɾ�������д��ڷ������Ͳ���ps
			if(!is_null($isExists) && intval($isExists)>=0)
			{
				//����з�������ok����������ps��
				if($cmd == Service_Alading_Alading::THREAD_COMMIT_CMD)
				{
					$bolPs = false;
				}
				$arrRemove[] = $key;
			}
		}
		$res = Dl_Alading_Alading::delKey(Dl_Alading_Alading::VIPPING_DELAY_POST_LIST_PREFIX.$thread_id);
		if(false === $res)
		{
			Bingo_Log::warning(sprintf("delete extra key %s failed! ",Dl_Alading_Alading::VIPPING_DELAY_POST_LIST_PREFIX.$thread_id));
		}
		//�����ӳٶ���
		if(is_array($arrRemove) && !empty($arrRemove))
		{
			Dl_Alading_Alading::zrem(Dl_Alading_Alading::VIPPING_DELAY_SORTED_SET, $arrRemove);
		}
		//ִ��ɾ��vippingʵʱ����
		//����֤���Ѿ���vipping_delay_b�ж�ɾ�����д�������µ����߼�
		/*if(true === $bolPs)
		{
			$res = Service_Alading_Lib_Vipping::sendVipping($command_no,$thread_id,0,$forum_id,$forum_name);
			if(false === $res)
			{
				Bingo_Log::warning("vipping send ps failed!");
			}
		}*/
		return true;
	}
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
