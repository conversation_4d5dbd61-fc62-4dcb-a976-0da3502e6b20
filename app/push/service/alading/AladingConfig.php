<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file AladingConfig.php
 * <AUTHOR>
 * @date 2013/06/07 20:08:11
 * @brief 
 *  
 **/

class Service_Alading_AladingConfig 
{
	//ɾ��ʽ������������
    const DEL_PUSH = 'delPush';
    //ѭ��ʽ������������
    const UPDATE_PUSH = 'updatePush';
    //���������ٴ�����������
    const UPDATE_QUICK_PUSH = 'updateQuickPush';
    //ɾ�ɴ�����������
    const DEL_FORUM_PUSH = 'delForumPush';
	
    //�ļ�����·��
    const DEL_PUSH_PATH = '/app/push/script/data/alading/delPush';
    const UPDATE_PUSH_PATH = '/app/push/script/data/alading/updatePush';
    const UPDATE_QUICK_PUSH_PATH = '/app/push/script/data/alading/updateQuickPush';
    const DEL_FORUM_PUSH_PATH = '/app/push/script/data/alading/delForumPush';

	//��ʱ����
    const PING_MAX_TIMEOUT = 35000;
    const PING_MAX_CONN_TIMEOUT = 1000;
    const UPDATE_TIMES = 50;
	
	//���ɲ�Ʒ���������url
    //const PING_URL = 'http://open.baidu.com/data/ms/service/resourcePing/ownProductPing?token=%s&url=%s';
    //�µ�ping�ӿڣ�Ǩ�ƺ��ȶ��Ը���
    const PING_URL = 'http://alaplat.baidu.com/ws/site/updateSitebyUrl?url=%s';

    //ɾ��ʽ����������������
    const DEL_URL = 'http://tieba.baidu.com/alading/pushDel?index=';
    const DEL_COUNT = 10;
    const DEL_PER_LIMIT = 500;
    const DEL_LIMIT_TOTAL = 10000;
    
    //ѭ��ʽ����������������
    const UPDATE_URL = 'http://tieba.baidu.com/alading/pushUpdate?index=';
    const UPDATE_COUNT = 20;
    const UPDATE_PER_LIMIT = 500;
    const UPDATE_LIMIT_TOTAL = 10000;
    
    //���������ٴ���������������
    const UPDATE_QUICK_URL = 'http://tieba.baidu.com/alading/pushUpdate?index=';
    const UPDATE_QUICK_COUNT = 10;
    const UPDATE_QUICK_PER_LIMIT = 50;
    const UPDATE_QUICK_LIMIT_TOTAL = 10000;
    
    //ɾ�ɴ���������������
    const DEL_FORUM_URL = 'http://tieba.baidu.com/alading/pushDelForum';
    const DEL_FORUM_COUNT = 1;
    const DEL_FORUM_PER_LIMIT = 500;
    const DEL_FORUM_LIMIT_TOTAL = 10000;
	
	const XML_SPLIT_SIZE = 9454105;
	const GEN_XML_TIMEOUT = 1100;

	//�Ƿ�������ģʽ
    const PING_TEST = false;

    //���͸�ͼ�ѵ�ɾ������ʧ�ܣ���Ҫ����redis.list�������ԣ��������tid��pid���м���_�ָ�
    const RELAY_SEND_IMG_SEGMENT = '_';
    //ftpץȡ��ַ
    public static $XML_FTP_PATH = 'ftp://db-orp-spre00.db01.baidu.com//home/<USER>/orp001';

    public static $DEL_PUSH_DATA_PATH; 
    public static $UPDATE_PUSH_DATA_PATH;
    public static $UPDATE_QUICK_PUSH_DATA_PATH;
    public static $DEL_FORUM_PUSH_DATA_PATH;

    public static $delPushToken;
    public static $updatePushToken;
    public static $updateQuickPushToken;
    public static $delForumPushToken;

    public static $testXmlFile;
    
    //vippingר��start
    /**
     * �ӳٶ���������������
     * 0  ���ظ���
     * 1  ��������
     * 12 ɾ���ظ���(!ɾ����������ͬ����)
     * 13 �ظ�������
     * @var unknown
     */
    public static $arrVippingDelayCmd = array(0,1,2,12,13,55009,9999999,10013);
    /**
     * vipping���Դ���
     * @var unknown
     */
    const VIPPING_SEND_RETRY = 3;
    
    //vippingר��end
    
    //�����������ʾʱ��,36000��ʾÿ��ʮ��report
    const REPORT_TIME = 36000;
}

Service_Alading_AladingConfig::$testXmlFile = ROOT_PATH.'/app/push/data/xml.test';

Service_Alading_AladingConfig::$DEL_PUSH_DATA_PATH = ROOT_PATH.Service_Alading_AladingConfig::DEL_PUSH_PATH;
Service_Alading_AladingConfig::$UPDATE_PUSH_DATA_PATH = ROOT_PATH.Service_Alading_AladingConfig::UPDATE_PUSH_PATH;
Service_Alading_AladingConfig::$UPDATE_QUICK_PUSH_DATA_PATH = ROOT_PATH.Service_Alading_AladingConfig::UPDATE_QUICK_PUSH_PATH;
Service_Alading_AladingConfig::$DEL_FORUM_PUSH_DATA_PATH = ROOT_PATH.Service_Alading_AladingConfig::DEL_FORUM_PUSH_PATH;

//ɾ��ʽ��������token
Service_Alading_AladingConfig::$delPushToken = array(
	1 => '99e28e8f75ac1faada5c', 
	2 => '44688fad41803b9a97e7',
	3 => 'd124550d04b28402ba03',
	4 => '727f711e92df3143de9d',
	5 => '52e7f204f6c0bd98610f',
	6 => '40172ade98be358659cb',
	7 => '8667da3f114fb23a46b8',
	8 => 'f1a4b6f76b12f15184e9',
	9 => 'd378f3e5f91504a99d8d',
	10 => '4190b4f927050261152b',
);
//ѭ��ʽ����������������
Service_Alading_AladingConfig::$updatePushToken = array(
    1 => 'fbc65652edc5433af335',
    2 => 'e2bcaf77b8ed0e866779',
    3 => 'dc89366511d234689a18',
    4 => 'fc58d637620446762223',
    5 => 'a3be689bb911dc182cb5',
    6 => 'fa15a421e3476eb469d5',
    7 => '5fd42d82eea6649e4491',
    8 => '4fee6060c384085acf97',
    9 => 'd05812d68301fd3a496d',
    10 => 'e80896e16896ca5b5f73',
    11 => '78340fc1785ab9f25de2',
    12 => 'ebd0a2ed190d4481fd59',
    13 => 'f9bd0bdd7a9bf8341602',
    14 => '3d3ffaf4305a4a2e6787',
    15 => '8716b1626c410eef58b1',
    16 => '16cf0979672b1d636108',
    17 => '4073c41a2d01251d2570',
    18 => '4c80386426994bbc157d',
    19 => 'd1e630da1b2dea41f510',
    20 => '14b0947b3511f6c6b6ee',
);
//���������ٴ���������������
Service_Alading_AladingConfig::$updateQuickPushToken = array(
	1 => 'fbc65652edc5433af335',
	2 => 'e2bcaf77b8ed0e866779',
	3 => 'dc89366511d234689a18',
	4 => 'fc58d637620446762223',
	5 => 'a3be689bb911dc182cb5',
	6 => 'fa15a421e3476eb469d5',
	7 => '5fd42d82eea6649e4491',
	8 => '4fee6060c384085acf97',
	9 => 'd05812d68301fd3a496d',
	10 => 'e80896e16896ca5b5f73',
	11 => '78340fc1785ab9f25de2',
	12 => 'ebd0a2ed190d4481fd59',
	13 => 'f9bd0bdd7a9bf8341602',
	14 => '3d3ffaf4305a4a2e6787',
	15 => '8716b1626c410eef58b1',
	16 => '16cf0979672b1d636108',
	17 => '4073c41a2d01251d2570',
	18 => '4c80386426994bbc157d',
	19 => 'd1e630da1b2dea41f510',
	20 => '14b0947b3511f6c6b6ee',
);
//ɾ�ɴ���������������
Service_Alading_AladingConfig::$delForumPushToken = array(
	1 => 'e571b665056871f9badb'
);


/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
