<?php

/**
 * ����ͨ��ģ�� 
 * <AUTHOR>
 ���ӣ�
 <item>
<key><![CDATA[������]]></key>
<display>
<title><![CDATA[������]]></title>
<url>http://tieba.baidu.com/f?kw=%BD%F0%D0%E3%EC%C5&amp;fr=ala0</url>
<fname><![CDATA[������]]></fname>
<showurl>tieba.baidu.com/������?fr=ala0</showurl>
<thread1url>http://tieba.baidu.com/p/2940518109?fr=ala0</thread1url>
<thread1><![CDATA[������̨���˿����� ������]]></thread1>
<thread2url>http://tieba.baidu.com/p/2936192659?fr=ala0</thread2url>
<thread2><![CDATA[Soo Hyun �140322ֱ������������Ѳ̨��վֱ��¥]]></thread2>
<mthread1 title="������̨���˿����� ������" url="http://tieba.baidu.com/p/2940518109?fr=ala0&amp;pstaala=1"/>
<mthread2 title="<PERSON> Hyun �140322ֱ������������Ѳ̨��վֱ��¥" url="http://tieba.baidu.com/p/2936192659?fr=ala0&amp;pstaala=2"/>
<mthread3 title="0321��԰�����ӻ�������Ƶһ��" url="http://tieba.baidu.com/p/2934468673?fr=ala0&amp;pstaala=3"/>
</display>
</item>

ע�⣺ƴarray �ڵ���Ⱥ�˳���������xml���Ⱥ�˳��,ps���xml���Ⱥ�˳��Ҳ����У�飬���˳�򲻶ԣ��ᵼ��ģ��У��ʧ��

 */
class Service_Alading_Xmltemplate_Wise_NovelTemplate
{

	/**
	 * [execute description]
	 * @param  [type] $arrPushInfo [description]
	 * @param  array  $arrConfig   [description]
	 * @return [type]              [description]
	 */
	public static function execute($arrPushInfo, $arrConfig = array())
	{
        //Bingo_Log::warning("NovelTemplate---------------".var_export($arrPushInfo,true));
		$item = self::_buildTemplate($arrPushInfo,$arrConfig);	
		return $item;
	}
	
	/**
	 * ȥ�������е���Ч�ַ�
	 * @param unknown_type $value
	 * @return mixed
	 */
	public static function delInvalidChar($value)
	{
		return str_replace(array("]]>"),"",$value);
	}
	
	
	/**
	 * ����itemģ��
	 * @param unknown_type $arrPushInfo
	 * @param unknown_type $arrConfig
	 * @return boolean|multitype:unknown
	 */
	private static function _buildTemplate($arrPushInfo, $arrConfig = array())
	{
		$item = array();
        $arrPushInfo['forumName'] = iconv('UTF-8','GBK',$arrPushInfo['forumName']);	
		$key = isset($arrPushInfo['forumName'])?$arrPushInfo['forumName']:"";
        $key = Libs_Util_String::transAscii2Char($key);
		if ('' === $key)
		{
			Bingo_Log::warning("build template key is null");
			return false;
		}
		
		// <key>
		$item['key'] = array('@cdata'=>$key);
		
		// <display>
        //Bingo_Log::warning("eeeeeeeeeeeeeeeeeee".var_export($arrPushInfo,true));
		$display = self::_buildDisplay($arrPushInfo);
		if (false === $display)
		{
			Bingo_Log::fatal("_buildDisplay exec failed");
			return false;
		}
		$item['display'] = $display;
        return $item;		
	}
	
	/**
	 * ���� display����
	 * @param unknown_type $arrPushInfo
	 * @return boolean
	 */	
	private static function _buildDisplay($arrPushInfo)
	{
        //Bingo_Log::warning("qqqqqqqqqqqqqqqqqqqq".var_export($arrPushInfo,true));
		$display = array();
		$forumName = isset($arrPushInfo['forumName']) ? $arrPushInfo['forumName'] : '';
		if ('' === $forumName)
		{
			Bingo_Log::warning("build forumInfo forum name is null");
			return false;
		}
        $forumName = Libs_Util_String::transAscii2Char($forumName);

        $title = isset($arrPushInfo['title']) ? $arrPushInfo['title'] : '';
		if ('' === $title)
		{
			Bingo_Log::warning("build forumInfo forum name is null");
			return false;
		}
        $title = iconv('UTF-8','GBK',$title);
        $title = Libs_Util_String::transAscii2Char($title);

		// <title>
		$display['title'] = array('@cdata'=>$forumName);
		$display['url'] = "http://tieba.baidu.com/f?kw=".$forumName."&fr=ala0";
        //Bingo_Log::warning("adsdff".$title);
		$display['fname'] = array('@cdata'=> $title);
        //Bingo_Log::warning("eeeeeeeeeeeeee".var_export($display['fname'],true));
		$display['showurl'] = $arrPushInfo['domain'];
		$display['date'] = array('@cdata' => date("Y-m-d"));
		

		$display['threadnum'] = array('@cdata' => '');
		$display['postnum'] = array('@cdata' => 0);
		$display['membernum'] = array('@cdata' => 0);
		$display['membername'] = array('@cdata' => '');

		
		$display = self::_buildThreadInfoOld($arrPushInfo, $display);
		if (false === $display)
		{
			Bingo_Log::warning("build thread old template fail");
			return false;
		}
		
		$display['forumintro'] = array('@cdata' => '');
		

		$display = self::_buildThreadInfoNew($arrPushInfo, $display);
		if (false === $display)
		{
			Bingo_Log::warning("build thread new template fail");
			return false;
		}
		$display['dir1'] = array('@cdata' => '');
		$display['dir2'] = array('@cdata' => '');
		//$display['forumsummary'] = array('@cdata' => $leadingWords);
		$display['hastalk'] = array('@cdata' => 0);
		return $display;		
	}
	
	/**
	 * @param unknown $arrThreadInfo
	 * @param unknown $display
	 * @param number $fid
	 * @return boolean|Ambigous <boolean, unknown>
	 */
	private static function _buildThreadInfoOld($arrPushInfo, $display)
	{
		$thread0 = isset($arrPushInfo['childGroup'][0])?$arrPushInfo['childGroup'][0]:array();
		$thread1 = isset($arrPushInfo['childGroup'][1])?$arrPushInfo['childGroup'][1]:array();
				
		if (empty($thread0) || empty($thread1))
		{
			Bingo_Log::warning("build template thread info is empty.");
			return false;
		}
		
		$display = self::_buildOldThread($thread0, 1, $display);
		if (false === $display)
		{
			Bingo_Log::warning("build old thread 1 fail");
			return false;
		}
		
		$display = self::_buildOldThread($thread1, 2, $display);
		if (false === $display)
		{
			Bingo_Log::warning("build old thread 2 fail");
			return false;
		}
				
		return $display;
	}
	
	/**
	 * @param unknown $arrThreadInfo
	 * @param unknown $display
	 * @param number $fid
	 * @return boolean|Ambigous <boolean, unknown>
	 */
	private static function _buildThreadInfoNew($arrPushInfo, $display)
	{
		$thread0 = isset($arrPushInfo['childGroup'][0])?$arrPushInfo['childGroup'][0]:array();
		$thread1 = isset($arrPushInfo['childGroup'][1])?$arrPushInfo['childGroup'][1]:array();
		$thread2 = isset($arrPushInfo['childGroup'][2])?$arrPushInfo['childGroup'][2]:array();	
		if (empty($thread0) || empty($thread1) || empty($thread2))
		{
			Bingo_Log::warning("build template thread info is empty.");
			return false;
		}
			
		$display = self::_buildNewThread($thread0, 1, $display);
		if (false === $display)
		{
			Bingo_Log::warning("build new thread 1 fail");
			return false;
		}
	
		$display = self::_buildNewThread($thread1, 2, $display);
		if (false === $display)
		{
			Bingo_Log::warning("build new thread 2 fail");
			return false;
		}
	
		$display = self::_buildNewThread($thread2, 3, $display);
		if (false === $display)
		{
			Bingo_Log::warning("build new thread 3 fail");
			return false;
		}
	
		return $display;
	}
	
	/**
	 * @param unknown $thread
	 * @param unknown $index
	 * @param unknown $display
	 * @param number $fid
	 * @return boolean|unknown
	 */
	private static function _buildOldThread($thread, $index, $display)
	{
		$strThreadTitle = $thread['title'];
        //Bingo_Log::warning("1111111111111".mb_detect_encoding($strThreadTitle));
        $strThreadTitle = iconv('UTF-8','GBK',$strThreadTitle);
		$strThreadTitle = Libs_Util_String::filterNonGBKChar($strThreadTitle);
        //Bingo_Log::warning("2222222222222222".mb_detect_encoding($strThreadTitle));
		$strThreadTitle = self::delInvalidChar($strThreadTitle);
		
		// <thread%dur>
		$urlTag = sprintf("thread%durl",$index);
		$display[$urlTag] = "http://tieba.baidu.com/p/".$thread['thread_id'];
		
		// <thread%d>
		$tag = sprintf("thread%d",$index);
		$display[$tag] = array('@cdata' => $strThreadTitle);
		return $display;
	}
	
	/**
	 * @param unknown $thread
	 * @param unknown $index
	 * @param unknown $display
	 * @param number $fid
	 * @return boolean|unknown
	 */
	private static function _buildNewThread($thread, $index, $display)
	{
	    //Bingo_Log::warning(var_export($thread,true));
    	$strThreadTitle = isset($thread['title'])?$thread['title']:'';
        $strThreadTitle = iconv('UTF-8','GBK',$strThreadTitle);
		$strGBKTitle = Libs_Util_String::filterNonGBKChar($strThreadTitle);

        $summary = isset($thread['abstract'])?$thread['abstract']:'';
        $summary = iconv('UTF-8','GBK',$summary);
        $strGBKSummary = Libs_Util_String::filterNonGBKChar($summary);        
        if($thread['is_ntitle'] ==1)
        {
            $strGBKSummary = '';
        }

		$strUrl = "http://tieba.baidu.com/p/".$thread['thread_id'];
		
		$tag = sprintf("mthread%d", $index);
		$display[$tag] = array('@attributes' => 
			array(
				'title' => $strGBKTitle."&z&s$".$strGBKSummary, 
				'url'=> $strUrl,
				'replaynumber' => $thread['comment_num'],
				'threadid' => $thread['thread_id'],
				'isgood' => 0,
				'type' => 0,
				'lastreplytime' => $thread['last_modified_time'],
//                'isvideo' => $thread['abstract'],
				'clicknum' => 0,
			),
		);
		return $display;		
	}
	
}
