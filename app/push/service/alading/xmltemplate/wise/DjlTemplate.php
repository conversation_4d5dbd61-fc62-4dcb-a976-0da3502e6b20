<?php


/**
 * 贴吧大家聊，生成 cdata 模板和离线 xml；
 */
class Service_Alading_Xmltemplate_Wise_DjlTemplate
{
    /**
     * Note: 大家聊好几种卡片在离线数据建库。其中大家聊卡的内容挖掘服务返回信息是 utf8 编码；
     * 而10号卡 tieba_newxml 内容挖掘返回的信息是 gbk 编码。千万注意！
     */
    public static function execute($arrData, $query, $is_multi_forum = false)
    {
        $cdataRet = self::buildCDataItem($arrData, $query, $is_multi_forum);
        if ($is_multi_forum) {
            $display = $cdataRet['multi'];
        } else {
            $display = $cdataRet['single'];
        }

        if (empty($display)) {
            return false;
        }

        $itemData = array(
            'key' => array(
                '@cdata' => $query,
            ),
            'display' => $display,
        );

        // 只有大家聊单吧卡需要把 10 号旧卡展现样式对应数据在离线建库
        if (!$is_multi_forum) {
            $tpl10 = self::buildTemplateFor10($query);
            if ($tpl10 !== false) {
                $itemData['display']['newxml10_content'] = $tpl10;
            }
            // var_dump(__FILE__ . __LINE__ . ' $itemData: '); var_export($itemData);
        }

        $xmldata = self::_buildItemXml($itemData);
        $checkRes = self::_checkXmlSchema($xmldata, $query, $is_multi_forum);
        if ($checkRes == false) {
            return false;
        }

        // var_dump(__FILE__ . __LINE__ . ' $xmldata: '); var_export($xmldata);
        return $xmldata;
    }

    /**
     * 构建XML字符串
     * @param unknown_type $arrItem
     * @return boolean|string
     */
    private static function _buildItemXml($arrItem)
    {
        if (!is_array($arrItem) || empty($arrItem)) {
            Bingo_Log::warning('_buildItemXml input param error,' . print_r($arrItem, true));
            return false;
        }

        try {
            $itemXml = Libs_Util_Array2xml::createXML('item', $arrItem);
            $rootNode = $itemXml->getElementsByTagName('item');
            if ($rootNode->length > 0) {
                $strXml = $itemXml->saveXML($rootNode->item(0));
                //echo $strXml;
                return $strXml;
            } else {
                Bingo_Log::warning("_buildItemXml xml fail, the length is 0");
                return false;
            }
        } catch (Exception $e) {
            Bingo_log::warning("_buildItemXml exception," . print_r($e, true));
            return false;
        }

        return false;
    }

    /**
     * @param string
     * @return bool
     */
    private static function _checkXmlSchema($itemXml, $word, $is_multi_forum)
    {
        if (!$itemXml) {
            Bingo_Log::warning('param error, itemXml is null, word: ' . $word);
            // send email
            $arrInput['content'] = 'param error, itemXml is null, word: ' . $word;
            $arrInput['title'] = 'DjlTemplate';
            // Libs_Util_Report::sendMail($arrInput);
            return false;
        }
        $itemXml = self::getXml($itemXml);

        if ($is_multi_forum) {
            $xmlSchemaPath = dirname(__File__) . '/../../djl_duoba_utf8.xsd';
        } else {
            $xmlSchemaPath = dirname(__File__) . '/../../djl_danba_utf8.xsd';
        }
        // Bingo_Log::notice("xmlpath: $xmlSchemaPath");

        $xml = new DOMDocument();
        libxml_use_internal_errors(true);
        $xml->loadXML($itemXml);
        if (!$xml->schemaValidate($xmlSchemaPath)) {
            self::libxml_display_errors($word);
            return false;
        }

        $badir = "danba";
        if ($is_multi_forum) {
            $badir = "duoba";
        }
        // Bingo_Log::warning("word: $word, $badir, xml validate succ");
        return true;
    }

    public static function getXml($strXml, $encoding = 'utf-8')
    {
        return  '<?xml version="1.0" encoding="utf-8" ?>' . "\n" . '<DOCUMENT>' . "\n"  . $strXml . '</DOCUMENT>';
        /*
        if ($encoding == 'utf-8') {
            return  '<?xml version="1.0" encoding="utf-8" ?>' . "\n" . '<DOCUMENT>' . "\n"  . $strXml . '</DOCUMENT>';
        } elseif ($encoding == 'gbk') {
            return  '<?xml version="1.0" encoding="gbk" ?>' . "\n" . '<DOCUMENT>' . "\n"  . $strXml . '</DOCUMENT>';
        }
        */
    }

    /**
     * @param string
     * @return bool
     */
    private static function libxml_display_errors($word)
    {
        $errors = libxml_get_errors();
        foreach ($errors as $error) {
            // $warning =  self::libxml_display_error($error);
            Bingo_Log::warning('word: ' . $word . '  error info: ' . print_r($error, true));
            // $arrInput['content'] .=  'forum_id: ' . $forum_id . '  error info: ' . $warning . "\n";
        }
        // send email
        // $arrInput['title'] = 'AladingXmlTemplateFrame';
        // Libs_Util_Report::sendMail($arrInput);
        libxml_clear_errors();
        return true;
    }

    public static function buildCDataItem($data, $query, $is_multi_forum = false)
    {
        // var_dump(__FILE__ . __LINE__ . ' $data: '); var_export($data);
        $cdataRet = array();
        if ($is_multi_forum == false) {
            $cdataRet['single'] = self::_buildSingleTemplate($data['single'], $query);
        } else {
            $multi = $data['multi'];
            if (empty($multi)) {
                $cdataRet['multi'] = false;
                return $cdataRet;
            }

            foreach ($multi['forum_list'] as $key => $aforum) {
                $tmpForumRet = self::_buildMultiAForumTemplate($aforum, $query);
                if ($tmpForumRet === false) {
                    continue;
                }

                // TODO: 上线后记得这行代码去掉
                // $aforum['frs_url'] = 'http://tieba.baidu.com/p/7566333254?fid=10865432';

                $cdataRet['multi']['url'] = $aforum['frs_url'];
                $cdataRet['multi']['ba_url'] = $aforum['frs_url'];
                $cdataRet['multi']['ba_list'][] = $tmpForumRet;

                // 最多六个吧
                if (count($cdataRet['multi']['ba_list']) >= 6) {
                    break;
                }
            }
            // 最少二个吧
            if (count($cdataRet['multi']['ba_list']) < 2) {
                $cdataRet['multi'] = false;
            }
        }

        // var_dump(__FILE__ . __LINE__ . ' $cdataRet: '); var_export($cdataRet);
        return $cdataRet;
    }

    public static function _buildSingleTemplate($single, $query)
    {
        if (empty($single)) {
            Bingo_Log::warning("djl_single query: $query get empty data: " . Bingo_String::array2json($single));
            return false;
        }
        $forumInfo = $single['forum'];
        if (self::checkArrayParams($forumInfo, array(
            'frs_url', 'icon', 'forum_name',
        )) === false) {
            Bingo_Log::warning("djl_single query: $query get invalid forum info: " . Bingo_String::array2json($forumInfo));
            return false;
        }

        // TODO: 上线后记得这行代码去掉
        // $forumInfo['frs_url'] = 'http://tieba.baidu.com/p/7566333254?fid=10865432';

        $arrRet = array(
            'url' => $forumInfo['frs_url'],

            // Note: 补充阿拉丁真实 title 主链相关信息
            'title' => array(
                '@cdata' => $query,
            ),
            // 阿拉丁真实 title 的 suppinfo 信息没有合适字段填充，不传吧
            // Note: 补充阿拉丁真实 title 子链相关信息，这个字段先要设置，把位置占住，否则字段顺序和 schema 不一样，也会导致 xml 校验失败。
            'list' => array(),
            // ----- end 阿拉丁真实 title 信息填充

            'forum' =>
            array(
                'frs_url' => $forumInfo['frs_url'],
                'member_num' => array(
                    '@cdata' => isset($forumInfo['member_num']) ? $forumInfo['member_num'] : 0,
                ),
                'post_num' => array(
                    '@cdata' => isset($forumInfo['post_num']) ? $forumInfo['post_num'] : 0,
                ),
                'icon' => $forumInfo['icon'],
                'fname' => array(
                    '@cdata' => $forumInfo['forum_name'],
                ),
                // 'thread' => self::_buildThread($aForumInfo['thread']),
            ),
        );

        foreach ($single['thread_list'] as $key => $thread) {
            $tmpThread = self::_buildThread($thread, $query, false);
            if ($tmpThread == false) {
                continue;
            }

            // Note: 阿拉丁真实 title 子链信息
            $arrRet['list'][] = array(
                'title' => array(
                    '@cdata' => $thread['title'],
                ),
                'sub_url' => $thread['url'],
            );
            // ----- end 阿拉丁真实 title 子链信息

            $arrRet['thread_list'][] = $tmpThread;
            // 单吧模板每个吧最多六个帖子
            if (count($arrRet['thread_list']) >= 6) {
                break;
            }
        }
        // 单吧模板每个吧最少2个帖子
        if (count($arrRet['thread_list']) < 2) {
            Bingo_Log::warning("djl_single query: $query get thread less 2. single data: " . Bingo_String::array2json($single));
            return false;
        }
        return $arrRet;
    }

    public static function _buildMultiAForumTemplate($aForumInfo, $query)
    {
        if (self::checkArrayParams($aForumInfo, array(
            'frs_url', 'icon', 'forum_name',
        )) === false) {
            return false;
        }
        $arrRet = array(
            'frs_url' => $aForumInfo['frs_url'],
            'member_num' => array(
                '@cdata' => isset($aForumInfo['member_num']) ? $aForumInfo['member_num'] : 0,
            ),
            'post_num' => array(
                '@cdata' => isset($aForumInfo['post_num']) ? $aForumInfo['post_num'] : 0,
            ),
            'icon' => $aForumInfo['icon'],
            'fname' => array(
                '@cdata' => $aForumInfo['forum_name'],
            ),
            // 'thread' => self::_buildThread($aForumInfo['thread']),
        );

        $tmpThread = self::_buildThread($aForumInfo['thread'][0], $query, true);
        if ($tmpThread === false) {
            return false;
        }
        $arrRet['thread'] = $tmpThread;
        return $arrRet;
    }

    public static function _buildThread($thread, $query, $is_multi_forum)
    {
        if (self::checkArrayParams($thread, array(
            'user_nickname', 'portrait', 'title', 'content', 'url',
        )) === false) {
            $perfix = 'djl_single';
            if ($is_multi_forum){
                $perfix = 'djl_multi';
            }
            Bingo_Log::warning("$perfix query: $query get invalid thread: " . Bingo_String::array2json($thread));
            return false;
        }
        $arrRet = array(
            'user_nickname' => array(
                '@cdata' => $thread['user_nickname'],
            ),
            'post_num' => array(
                '@cdata' => isset($thread['post_num']) ? $thread['post_num'] : 0,
            ),
            // last_modified_time、abstract、create_time 这几个字段发现存在为 null 的情形，导致 xml 校验不通过；
            // 为了避免出现这种情况，当出现为 null 情形时把他们设置为默认值。
            'last_modified_time' => array(
                '@cdata' => isset($thread['last_modified_time']) ? $thread['last_modified_time'] : time(),
            ),
            'portrait' => $thread['portrait'],
            // 'portrait' => array(
            //     '@cdata' => $thread['portrait'],
            // ),
            'title' => array(
                '@cdata' => $thread['title'],
            ),
            'content' => array(
                '@cdata' => $thread['content'],
            ),
            'focus_num' => array(
                '@cdata' => isset($thread['focus_num']) ? $thread['focus_num'] : 0,
            ),
            // 'like_num' => array(
            //     '@cdata' => $thread['like_num'],
            // ),
            'url' => $thread['url'],
            'media' => self::_buildMediaTemplate($thread['media']),

            // 'thread_id' => array(
            //     '@cdata' => $thread['thread_id'],
            // ),
            // 'user_id' => array(
            //     '@cdata' => $thread['user_id'],
            // ),
            // 'post_num' => array(
            //     '@cdata' => $thread['post_num'],
            // ),
            // 'good_types' => array(
            //     '@cdata' => $thread['good_types'],
            // ),
            // 'abstract' => array(
            //     '@cdata' => isset($thread['abstract']) ? $thread['abstract'] : '',
            // ),
            // 'create_time' => array(
            //     '@cdata' => isset($thread['create_time']) ? $thread['create_time'] : time(),
            // ),
        );

        // $arrRet['media'] = self::_buildMediaTemplate($thread['media']);
        return $arrRet;
    }

    private static function _buildMediaTemplate($media)
    {
        // FE 说media多媒体字段需要传，当没有图片或者视频时，都传空或者0就行
        return array(
            'type' => isset($media['type']) ? $media['type'] : '',
            'pic_num' => array(
                '@cdata' => isset($media['pic_num']) ? $media['pic_num'] : 0,
            ),
            'video_duration' => array(
                '@cdata' => isset($media['video_duration']) ? $media['video_duration'] : 0,
            ),
            'thumbnail_url' => isset($media['thumbnail_url']) ? $media['thumbnail_url'] : '',
            // 'video_url' => isset($media['video_url']) ? $media['video_url'] : '',
        );
    }

    /**
     * 检查 $arrInfo 参数是否具有 $needParams 的字段列表
     */
    private static function checkArrayParams($arrInfo, $needParams)
    {
        foreach ($needParams as $key => $argv) {
            if (is_null($arrInfo[$argv])) {
                Bingo_Log::warning("check param error, need:[$argv]");
                return false;
            }
        }
        return true;
    }

    /**********************
     * 
     * Note: 这之后，是在大家聊离线数据中生成 10 号旧卡展现样式对应离线数据的逻辑
     * 
     **********************/
    private static function buildTemplateFor10($query)
    {
        $fid = Libs_Rpc_Forum::getFidByFnameNew($query);
        if ($fid === false) {
            Bingo_Log::warning("djl query[$query] get fid error.");
            return false;
        }

        $arrPushInfo = Service_Alading_AladingThreadFrame::getPushInfo($fid, array("topic" => true, "type" => Service_Alading_AladingConfig::UPDATE_PUSH));
        $forum = self::_buildForumTemplateFor10($arrPushInfo, $query);
        $threadList = self::_buildThreadTemplateFor10($arrPushInfo, $query, $fid);
        // 10号旧卡吧信息、三条子链都是必需，否则无法建库
        if ($forum === false || $threadList === false) {
            Bingo_Log::warning("djl query[$query] build newxml10 error, arrPushInfo: " . serialize($arrPushInfo));
            return false;
        }
        $tpl = array(
            'forum' => $forum,
            'thread_list' => $threadList,
        );
        // Bingo_Log::notice('tpl_10: ' . var_export($tpl, true));
        return $tpl;
    }

    private static function _buildForumTemplateFor10($arrPushInfo, $query)
    {
        $forumInfo = $arrPushInfo['forum'];
        $fname = self::_toUTF8($forumInfo['forum_name']);
        // 校验获取 arrPushInfo 吧信息相关必需字段是否成功
        if (empty($fname) || is_null($forumInfo['forum_card'])) {
            Bingo_Log::warning("query[$query] arrPushInfo invalid forum info: " . serialize($forumInfo));
            return false;
        }

        $forumEnc = urlencode($fname);
        $queryEnc = urlencode($query);
        $arrRet = array(
            // 'frs_url' => "http://tieba.baidu.com/f?kw=" . urlencode($fname),
            'frs_url' => "http://tieba.baidu.com/f?kw=$forumEnc&fr=dajialiao&refer=baidualaddin&query=$queryEnc",
            'member_num' => array(
                '@cdata' => isset($forumInfo['member_num']) ? $forumInfo['member_num'] : 0,
            ),
            'post_num' => array(
                '@cdata' => isset($forumInfo['post_num']) ? $forumInfo['post_num'] : 0,
            ),
            'icon' => $forumInfo['forum_card'],
            'fname' => array(
                '@cdata' => $fname,
            ),
        );

        // Bingo_Log::notice("utf8 fname: " . var_export($fname, true));
        // Bingo_Log::notice("gbk fname: " . var_export($forumInfo['forum_name'], true));
        // Bingo_Log::notice("arrRet: " . var_export($arrRet, true));
        return $arrRet;
    }

    private static function _buildThreadTemplateFor10($arrPushInfo, $query, $fid)
    {
        $arrRet = array();
        $threadArr = $arrPushInfo['thread'];
        foreach ($threadArr as $key => $_thread) {
            $user_name = "";
            $user_nickname = Service_Alading_StarGroupCard_SiteAggregate::getUserNickname($_thread['user_id'], $user_name);
            if (empty($user_nickname)) {
                // 如果获取用户nickname失败，就用username兜底
                $user_nickname = $user_name;
            }
            $portrait = Tieba_Ucrypt::encode($_thread['user_id'], $user_name);
            if (empty($portrait) || is_null($user_nickname)) {
                Bingo_Log::warning("djl get protrait[$portrait] or user_nickname[$user_nickname] empty.");
                continue;
            }

            $media = Service_Alading_StarGroupCard_SiteAggregate::getMediaInfo($_thread);
            $tid = $_thread['thread_id'];
            $threadCdata = array(
                'user_nickname' => array(
                    '@cdata' => $user_nickname,
                ),
                'post_num' => array(
                    '@cdata' => isset($_thread['post_num']) ? $_thread['post_num'] : 0,
                ),
                // last_modified_time、abstract、create_time 这几个字段发现存在为 null 的情形，导致 xml 校验不通过；
                // 为了避免出现这种情况，当出现为 null 情形时把他们设置为默认值。
                'last_modified_time' => array(
                    '@cdata' => isset($_thread['last_modified_time']) ? $_thread['last_modified_time'] : time(),
                ),
                'portrait' => $portrait,
                'title' => array(
                    '@cdata' => self::_toUTF8($_thread['title']),
                ),
                'content' => array(
                    '@cdata' => self::_toUTF8($_thread['title']),
                ),
                'focus_num' => array(
                    '@cdata' => isset($_thread['focus_num']) ? $_thread['focus_num'] : 0,
                ),
                'url' => "http://tieba.baidu.com/p/$tid?fid=$fid&fr=dajialiao&pstaala=1&refer=baidualaddin&query=" . urlencode($query),
                // 'url' => "http://tieba.baidu.com/p/" . $_thread['thread_id'],
                'media' => self::_buildMediaTemplate($media),
            );

            // Bingo_Log::notice("thread: " . var_export($arrRet, true));
            $arrRet[] = $threadCdata;
            if (count($arrRet) == 3) {
                break;
            }
        }

        // 不足3条子链，无法展现 10 号卡旧模板
        if (count($arrRet) != 3) {
            return false;
        }

        // Bingo_Log::notice("arrRet: " . var_export($arrRet, true));
        return $arrRet;
    }

    /**
     * 10号旧卡 arrPushInfo 数据都是 GBK编码，需要转换成 utf8
     */
    private static function _toUTF8($str)
    {
        $utf8 = Bingo_Encode::convert($str, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        return $utf8;
    }
}
