<?php
class Service_Alading_Xmltemplate_Wise_Dir1StarTemplate
{
	// ��������ģ���
	const TMP_NUM = 2;

	// ����������ɼ��Ĭ��ֵ
	const STAR_DEF_FORUM_INTRO = "%d��%s�ķ�˿������ۼ�";
	const STAR_DEF_FORUM_ICON = 'http://imgsrc.baidu.com/forum/pic/item/562c11dfa9ec8a130893d800f203918fa1ecc041.jpg';

	public static function execute($arrPushInfo, $arrConfig = array())
	{
		if (!is_array($arrPushInfo) || empty($arrPushInfo))
		{
			Bingo_Log::warning("execute input param is error,the param is".print_r($arrPushInfo, true));
			return false;
		}
	
		$pcextattr = array();
		$arrTmpNum = array('@cdata' => self::TMP_NUM,);

		$arrForumInfo = isset($arrPushInfo['forum'])?$arrPushInfo['forum']:array();
		$arrThreadInfo = isset($arrPushInfo['thread'])?$arrPushInfo['thread']:array();
		$forum = self::_buildForum($arrForumInfo);
	  
		if (false === $forum)
		{
			Bingo_Log::fatal("_buildForum exec failed");
			return false;
		}
	  
		$thread = self::_buildThread($arrForumInfo,$arrThreadInfo);
		//echo "ext thread \n";
		//var_dump($thread);
		if (false === $thread)
		{
			Bingo_Log::fatal("_buildThread exec failed");
			return false;
		}

		$pcextattr['tmpnum'] = $arrTmpNum;
		$pcextattr['forum'] = $forum;
		$pcextattr['thread'] = $thread;		
		return $pcextattr;
	}

	/**
	 * ����������Ϣ
	 * @param unknown_type $arrForumInfo
	 * @return boolean|multitype:string multitype:unknown  multitype:Ambigous <boolean, string>  Ambigous <boolean, multitype:multitype:multitype:Ambigous, multitype:multitype:multitype:Ambigous <multitype:, multitype:string multitype:unknown  >   multitype:Ambigous <string, unknown>  >
	 */
	private static function _buildForum($arrForumInfo)
	{
		$forum = array();
		if (!is_array($arrForumInfo) || empty($arrForumInfo))
		{
			Bingo_Log::warning("_buildForum input param error");
			return false;
		}

		$forumName = isset($arrForumInfo['forum_name_change']) ? $arrForumInfo['forum_name_change'] : $arrForumInfo['forum_name'];

		if ('' === $forumName)
		{
			Bingo_Log::warning("build forumInfo forum name is null");
			return false;
		}

		$forumId = isset($arrForumInfo['forum_id'])?$arrForumInfo['forum_id']:0;

		if ($forumId <= 0)
		{
			Bingo_Log::warning("build forumInfo forum id is less 0");
			return false;
		}

		$urlForumName = urlencode($forumName);
		/**
		 * url��utf-8��
		 * add by xiashanshan 20161121
		 **/
//		$urlForumName = urlencode(Bingo_Encode::convert($forumName, 'utf8', 'gbk'));
		$title = isset($arrForumInfo['key'])?$arrForumInfo['key']:$forumName;

		// <title>
		$forum['title'] = array('@cdata'=>$title);

		// <url>
		$forum['url'] = sprintf(Service_Alading_Xmltemplate_CommonTemplate::TIEBA_URL, $urlForumName);

		// <fname>
		$forum['fname'] = array('@cdata'=> $forumName);

		// <showurl>		
		$date = date("Y-m-d");
		// $showurl = sprintf(Service_Alading_Xmltemplate_CommonTemplate::SHOW_URL, $forumName)." $date";
		$showurl = Service_Alading_Xmltemplate_CommonTemplate::SHOW_URL;
		$forum['showurl'] = $showurl;
		// <forumicon>
		$forumicon = isset($arrForumInfo['forum_card'])?str_replace(PHP_EOL, '', $arrForumInfo['forum_card']):'';

		$intBaidu = stripos($forumicon, 'baidu.com');
		$intStatic = stripos($forumicon, 'bdstatic.com');

		//  �����ͷͼ����512����ʹ��Ĭ��ֵ���������������
		if (strlen($forumicon) >= 512 || (false === $intBaidu && false === $intStatic))
		{
			$forumicon = self::STAR_DEF_FORUM_ICON;
		}

		$forum['forumicon'] = $forumicon;
		
		// <forumintro>
		$forumintro = self::_buildForumIntro($arrForumInfo);

		if (false === $forumintro)
		{
			Bingo_Log::warning("_buildForumIntro exec failed");
			return false;
		}
		$forum['forumintro'] = array('@cdata'=> $forumintro);
		return $forum;
	}

	/**
	 * ����������Ϣ
	 * @param unknown_type $arrThreadInfo
	 * @param unknown_type $arrExtInfo
	 * @return boolean
	 */
	private static function _buildThread($arrForumInfo,$arrThreadInfo)
	{
		if (!is_array($arrThreadInfo) || empty($arrThreadInfo) || !is_array($arrForumInfo) || empty($arrForumInfo))
		{
			Bingo_Log::warning("_buildThread input param error".print_r($arrThreadInfo, true));
			return false;
		}

		$thread = array();
		$fid = $arrForumInfo['forum_id'];
		foreach ($arrThreadInfo as $key => $value)
		{
			$index = $key+1;
			$threadItem = self::_buildThreadItem($value, $index, $fid);
				
			if (false !== $threadItem)
			{
				$nodeName = sprintf("mthread%d", $index);
				$thread[$nodeName] = $threadItem;
			}
		}

		return $thread;
	}

	/**
	 * ��ȡ�ɽ���
	 * @param unknown_type $arrForumInfo
	 * @return boolean|string
	 */
	private static function _buildForumIntro($arrForumInfo)
	{
		$forum_name = isset($arrForumInfo['forum_name'])?$arrForumInfo['forum_name']:'';
		if ('' === $forum_name)
		{
			Bingo_Log::warning("_buildForumIntro input param error,the parma is".print_r($arrForumInfo,true));
			return false;
		}

		// membernum
		$member_num = isset($arrForumInfo['member_num'])?$arrForumInfo['member_num']:0;
		if ($member_num >= 10000)
		{
			$member_num = floor($member_num/10000).'��';
		}
		
		$strForumIntro = sprintf(self::STAR_DEF_FORUM_INTRO, $member_num, $forum_name);
		return $strForumIntro;
	}

	/**
	 * ������������
	 * @param unknown_type $arrForumInfo
	 * @return boolean|multitype:multitype:multitype:Ambigous <multitype:, multitype:string multitype:unknown  >   multitype:Ambigous <string, unknown>
	 */
	private static function _buildFriendForum($arrForumInfo)
	{
		if (!is_array($arrForumInfo) || empty($arrForumInfo))
		{
			Bingo_Log::warning("_buildFriendForum input param error");
			return false;
		}
		 
		$strZyqFriend = isset($arrForumInfo['zyq_friend'])?$arrForumInfo['zyq_friend']:'';
		 
		if ('' === $strZyqFriend)
		{
			Bingo_Log::warning("_buildFriendForum has not friend forum");
			return false;
		}
		 
		$arrZyqFriend = unserialize($strZyqFriend);
		 
		if (!is_array($arrZyqFriend) || empty($arrZyqFriend))
		{
			Bingo_Log::warning("_buildFriendForum input param error");
			return false;
		}
		 
		$dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';

		$friendforum = array();
		$friendlist = array();
		foreach($arrZyqFriend as $value)
		{
			$friend = array();
			if ('' !== $value)
			{
				$urlValue = urlencode($value);
				$strFriendUrl = sprintf(Service_Alading_Xmltemplate_CommonTemplate::TIEBA_URL,$urlValue);
				$friend = array('friendname' => array('@cdata'=> $value),
						'friendurl' => $strFriendUrl,);
				$friendlist[] = array('firend' => $friend);
			}
		}
		 
		$handleWordServer = Wordserver_Wordlist::factory();
		$dir1TableName = Service_Alading_Xmltemplate_CommonTemplate::TABLE_ALD_FRIEND_FORUM_INTRO;
		$friendIntroRet = $handleWordServer->getValueByKeys(array($dir1),$dir1TableName);
		 
		if (isset($friendIntroRet[$dir1]))
		{
			$strFriendIntro = $friendIntroRet[$dir1];
		}
		else
		{
			$strFriendIntro = 'ϲ�����ɵ��˻��ڹ䣺';
		}
		 
		$friendforum['intro'] = array('@cdata'=> $strFriendIntro);
		$friendforum['friendlist'] = $friendlist;
		return $friendforum;
	}

	/**
	 * ����һ����������
	 * @param unknown_type $thread
	 * @param unknown_type $index
	 * @param unknown_type $threadTmpl
	 * @return boolean|multitype:multitype:number  multitype:string  multitype:Ambigous <number, unknown>  multitype:unknown  multitype:void  Ambigous <boolean, multitype:multitype:unknown, multitype:multitype:unknown  >
	 */
	private static function _buildThreadItem($thread, $index , $fid=0)
	{
		if (!is_array($thread) || empty($thread))
		{
			Bingo_Log::warning("_buildThreadItem input param is error");
			return false;
		}
		$intThreadId = 0;
		$strThreadTitle = '';
		 
		$intThreadId = isset($thread['thread_id'])?$thread['thread_id']:0;
		$strThreadTitle = isset($thread['title'])?$thread['title']:'';
		 
		if ($intThreadId <= 0 || '' == $strThreadTitle)
		{
			Bingo_Log::warning('build thread info is error'.print_r($thread,true));
			return false;
		}
		 
		$strGBKTitle = Libs_Util_String::filterNonGBKChar($strThreadTitle);
		$strUrl = sprintf(Service_Alading_Xmltemplate_CommonTemplate::MTHREAD_URL_FID, $intThreadId, $index, $fid);
		// added by xiashanshan
		// add god logic
		if ($index == 1) {
			if ($thread['push_type'] == 'god') {
				$strUrl .= '&isgod=1';
			} else {
				$strUrl .= '&isgod=0';
			}
		}
		$intPostNum = isset($thread['post_num'])?$thread['post_num']:0;
		$isGood = (($thread['thread_types'] & 0x1)!=0)?1:0;
		$threadType = Service_Alading_Xmltemplate_CommonTemplate::getThreadType($thread);
		$lastModifiedTime = isset($thread['last_modified_time'])?$thread['last_modified_time']:time();
		$clickNum = isset($thread['freq_num'])?$thread['freq_num']:0;
	
                $node = array(); 	
		$node['title'] = array('@cdata'=> $strGBKTitle);
		$node['url'] = $strUrl;
		$node['replaynumber'] = array('@cdata'=> $intPostNum);
		$node['threadid'] = array('@cdata'=> $intThreadId);
		$node['isgood'] = array('@cdata'=> $isGood);
		$node['type'] = array('@cdata'=> $threadType);
		$node['lastreplytime'] = array('@cdata'=> $lastModifiedTime);
		$node['clicknum'] = array('@cdata'=> $clickNum);
		
		if (1 == $index)
		{
			$summary = isset($thread['summary'])?$thread['summary']:'';
			$summary = htmlspecialchars_decode($summary,ENT_QUOTES);
			//echo "summary = $summary \n";
			if ('' == $summary)
			{
				$node['summary'] = array('@cdata'=> $strGBKTitle);
			}
			else
			{
				$node['summary'] = array('@cdata' => $summary);
			}
			 
			$imgUrl = isset($thread['recommend_img_url'])?$thread['recommend_img_url']:'';
			if ('' !== $imgUrl)
			{
				$node['imgUrl'] = $imgUrl;
			}
		}       
		
		 
		$tagList = array();
		if (isset($thread['taglist']))
		{
			$tagList = $thread['taglist'];
		}
				 
		if (!empty($tagList))
		{
			$node['taglist']['tag'] = self::_buildThreadTag($tagList);
		}
		 
		return $node;
	}

	/**
	 * �������ӱ�ǩ
	 * @param unknown_type $arrTagList
	 * @return boolean|multitype:multitype:unknown
	 */
	private static function _buildThreadTag($arrTagList)
	{
		if (empty($arrTagList))
		{
			Bingo_Log::warning("_buildThreadTag input param error");
			return false;
		}
		 
		$taglistNode = array();
		foreach($arrTagList as $tag)
		{
			$taglistNode[] = array('@cdata' => $tag);
		}
		 
		return $taglistNode;
	}	
}