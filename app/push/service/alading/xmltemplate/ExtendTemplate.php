<?php

/**
 * ����ͨ��ģ�� 
 * <AUTHOR>
 ���ӣ�
 <item>
<key><![CDATA[������]]></key>
<display>
<title><![CDATA[������]]></title>
<url>http://tieba.baidu.com/f?kw=%BD%F0%D0%E3%EC%C5&amp;fr=ala0</url>
<fname><![CDATA[������]]></fname>
<showurl>tieba.baidu.com/������?fr=ala0</showurl>
<thread1url>http://tieba.baidu.com/p/2940518109?fr=ala0</thread1url>
<thread1><![CDATA[������̨���˿����� ������]]></thread1>
<thread2url>http://tieba.baidu.com/p/2936192659?fr=ala0</thread2url>
<thread2><![CDATA[Soo Hyun �140322ֱ������������Ѳ̨��վֱ��¥]]></thread2>
<mthread1 title="������̨���˿����� ������" url="http://tieba.baidu.com/p/2940518109?fr=ala0&amp;pstaala=1"/>
<mthread2 title="<PERSON> Hyun �140322ֱ������������Ѳ̨��վֱ��¥" url="http://tieba.baidu.com/p/2936192659?fr=ala0&amp;pstaala=2"/>
<mthread3 title="0321��԰�����ӻ�������Ƶһ��" url="http://tieba.baidu.com/p/2934468673?fr=ala0&amp;pstaala=3"/>
</display>
</item>

ע�⣺ƴarray �ڵ���Ⱥ�˳���������xml���Ⱥ�˳��,ps���xml���Ⱥ�˳��Ҳ����У�飬���˳�򲻶ԣ��ᵼ��ģ��У��ʧ��

 */
class Service_Alading_Xmltemplate_ExtendTemplate
{

	/**
	 * [execute description]
	 * @param  [type] $arrPushInfo [description]
	 * @param  array  $arrConfig   [description]
	 * @return [type]              [description]
	 */
	public static function execute($arrPushInfo, $arrConfig = array())
	{
		$item = self::_buildTemplate($arrPushInfo,$arrConfig);	
		return $item;
	}
	
	/**
	 * ȥ�������е���Ч�ַ�
	 * @param unknown_type $value
	 * @return mixed
	 */
	public static function delInvalidChar($value)
	{
		return str_replace(array("]]>"),"",$value);
	}
	
	
	/**
	 * ����itemģ��
	 * @param unknown_type $arrPushInfo
	 * @param unknown_type $arrConfig
	 * @return boolean|multitype:unknown
	 */
	private static function _buildTemplate($arrPushInfo, $arrConfig = array())
	{
		$arrPushInfo = array(
			'forum' => array(
				'forum_name' => '�ػʵ��е�����ѧ',
				'url' => 'http://www.qhdsz.com.cn',
			),
			'thread' => array(
				array(
					'url' => 'http://www.qhdsz.com.cn',
					'title' => '�ػʵ��е�����ѧ����',
				),
				array(
					'url' => 'http://www.ziyuanku.com/xuexiao/383.html',
					'title' => '�ػʵ��е�����ѧ��̬',
				),
				array(
					'url' => 'http://www.dianping.com/shop/12686078/review_all',
					'title' => '�ػʵ��е�����ѧ�ĵ���',
				),
			),
		);
		
		$item = array();		
		$arrForumInfo = $arrPushInfo['forum'];
		$arrThreadInfo = $arrPushInfo['thread'];
		$key = isset($arrForumInfo['key'])?$arrForumInfo['key']:$arrForumInfo['forum_name'];
        $key = Libs_Util_String::transAscii2Char($key);
		if ('' === $key)
		{
			Bingo_Log::warning("build template key is null");
			return false;
		}
		
		// <key>
		$item['key'] = array('@cdata'=>$key);
		
		// <display>
		$display = self::_buildDisplay($arrPushInfo);
		
		if (false === $display)
		{
			Bingo_Log::fatal("_buildDisplay exec failed");
			return false;
		}
		
		$item['display'] = $display;
        return $item;		
	}
	
	/**
	 * ���� display����
	 * @param unknown_type $arrPushInfo
	 * @return boolean
	 */	
	private static function _buildDisplay($arrPushInfo)
	{
		$arrForumInfo = $arrPushInfo['forum'];
		$arrThreadInfo = $arrPushInfo['thread'];
		$display = array();
		$display = self::_buildForumInfo1($arrForumInfo, $display);
		if (false === $display)
		{
			Bingo_Log::warning("build forum1 template fail");
			return false;
		}
		
		$display = self::_buildThreadInfoOld($arrThreadInfo, $display);
		if (false === $display)
		{
			Bingo_Log::warning("build thread old template fail");
			return false;
		}
		
		$display['forumintro'] = array('@cdata' => '');
		

		$display = self::_buildThreadInfoNew($arrThreadInfo, $display);
		if (false === $display)
		{
			Bingo_Log::warning("build thread new template fail");
			return false;
		}
		$display['dir1'] = array('@cdata' => '');
		$display['dir2'] = array('@cdata' => '');
		$display['hastalk'] = array('@cdata' => 0);
		return $display;		
	}
	
	/**
	 * ���������ǰ�벿��ģ��
	 * @param unknown_type $arrForumInfo
	 * @param unknown_type $display
	 * @return boolean|multitype:Ambigous <string, unknown>
	 */
	private static function _buildForumInfo1($arrForumInfo, $display)
	{
		if (!is_array($arrForumInfo) || empty($arrForumInfo) || !is_array($display))
		{
			Bingo_Log::warning("build forum input param error");
			return false;
		}
		
		$forumName = isset($arrForumInfo['forum_name_change']) ? $arrForumInfo['forum_name_change'] : $arrForumInfo['forum_name'];
		
		if ('' === $forumName)
		{
			Bingo_Log::warning("build forumInfo forum name is null");
			return false;
		}
        $forumName = Libs_Util_String::transAscii2Char($forumName);
	
		/**
		 * url��utf-8��
		 * add by xiashanshan 20161121
		 **/
//		$urlForumName = urlencode(Bingo_Encode::convert($forumName, 'utf8', 'gbk'));
		$title = isset($arrForumInfo['key'])?$arrForumInfo['key']:$forumName;
        $title = Libs_Util_String::transAscii2Char($title);
		// <title>
		$display['title'] = array('@cdata'=>$title);
		
		// <url>
		$display['url'] = $arrForumInfo['url'];
		
		// <fname>
		$display['fname'] = array('@cdata'=> $forumName);
		
		// <showurl>
		// $display['showurl'] = sprintf(self::SHOW_URL, $forumName);
		$display['showurl'] = "www.qhdsz.com.cn";
		
		// <date>
		$display['date'] = array('@cdata' => date("Y-m-d"));

		$display['threadnum'] = array('@cdata' => '');
		$display['postnum'] = array('@cdata' => '');
		$display['membernum'] = array('@cdata' => '');
		$display['membername'] = array('@cdata' => '');

		return $display;
	}
	


	
	/**
	 * @param unknown $arrThreadInfo
	 * @param unknown $display
	 * @param number $fid
	 * @return boolean|Ambigous <boolean, unknown>
	 */
	private static function _buildThreadInfoOld($arrThreadInfo, $display,$fid=0)
	{
		$thread0 = isset($arrThreadInfo[0])?$arrThreadInfo[0]:array();
		$thread1 = isset($arrThreadInfo[1])?$arrThreadInfo[1]:array();
				
		if (empty($thread0) || empty($thread1))
		{
			Bingo_Log::warning("build template thread info is empty ".print_r($arrThreadInfo,true));
			return false;
		}
		
		$display = self::_buildOldThread($thread0, 1, $display,$fid);
		if (false === $display)
		{
			Bingo_Log::warning("build old thread 1 fail");
			return false;
		}
		
		$display = self::_buildOldThread($thread1, 2, $display,$fid);
		if (false === $display)
		{
			Bingo_Log::warning("build old thread 2 fail");
			return false;
		}
				
		return $display;
	}
	
	/**
	 * @param unknown $arrThreadInfo
	 * @param unknown $display
	 * @param number $fid
	 * @return boolean|Ambigous <boolean, unknown>
	 */
	private static function _buildThreadInfoNew($arrThreadInfo, $display,$fid=0)
	{
		$thread0 = isset($arrThreadInfo[0])?$arrThreadInfo[0]:array();
		$thread1 = isset($arrThreadInfo[1])?$arrThreadInfo[1]:array();
		$thread2 = isset($arrThreadInfo[2])?$arrThreadInfo[2]:array();
	
		if (empty($thread0) || empty($thread1) || empty($thread2))
		{
			Bingo_Log::warning("build template thread info is empty ".print_r($arrThreadInfo,true));
			return false;
		}
			
		$display = self::_buildNewThread($thread0, 1, $display,$fid);
		if (false === $display)
		{
			Bingo_Log::warning("build new thread 1 fail");
			return false;
		}
	
		$display = self::_buildNewThread($thread1, 2, $display,$fid);
		if (false === $display)
		{
			Bingo_Log::warning("build new thread 2 fail");
			return false;
		}
	
		$display = self::_buildNewThread($thread2, 3, $display,$fid);
		if (false === $display)
		{
			Bingo_Log::warning("build new thread 3 fail");
			return false;
		}
	
		return $display;
	}
	
	/**
	 * @param unknown $thread
	 * @param unknown $index
	 * @param unknown $display
	 * @param number $fid
	 * @return boolean|unknown
	 */
	private static function _buildOldThread($thread, $index, $display, $fid=0)
	{
		$strThreadTitle = $thread['title'];
		$strThreadTitle = Libs_Util_String::filterNonGBKChar($strThreadTitle);
		$strThreadTitle = self::delInvalidChar($strThreadTitle);
		
		// <thread%dur>
		$urlTag = sprintf("thread%durl",$index);
		$display[$urlTag] = $thread['url'];
		
		// <thread%d>
		$tag = sprintf("thread%d",$index);
		$display[$tag] = array('@cdata' => $strThreadTitle);
		return $display;
	}
	
	/**
	 * @param unknown $thread
	 * @param unknown $index
	 * @param unknown $display
	 * @param number $fid
	 * @return boolean|unknown
	 */
	private static function _buildNewThread($thread, $index, $display, $fid = 0)
	{
		$strThreadTitle = isset($thread['title'])?$thread['title']:'';
		$strGBKTitle = Libs_Util_String::filterNonGBKChar($strThreadTitle);
		$strUrl = $thread['url'];
		
		$tag = sprintf("mthread%d", $index);
		$display[$tag] = array('@attributes' => 
			array(
				'title' => $strGBKTitle, 
				'url'=> $strUrl,
				'replaynumber' => '',
				'threadid' => '',
				'isgood' => 0,
				'type' => 0,
				'lastreplytime' => time(),
				'clicknum' => '',
			),
		);
		return $display;		
	}
	
}
