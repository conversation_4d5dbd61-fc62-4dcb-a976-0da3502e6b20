<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
    <xsd:element name="DOCUMENT">
<xsd:annotation>
    <xsd:documentation>
        需要大写，标记整个文档的开始和结束
    </xsd:documentation>
</xsd:annotation>
<xsd:complexType>
<xsd:sequence>
<xsd:element name="item" maxOccurs="unbounded" type="tItem"/>
</xsd:sequence>
<xsd:attribute name="content_method">
<xsd:annotation>
    <xsd:documentation>
        XML全量、增量还是删除
    </xsd:documentation>
</xsd:annotation>
<xsd:simpleType>
<xsd:restriction base="xsd:string">
<xsd:enumeration value="full"/>
<xsd:enumeration value="inc"/>
<xsd:enumeration value="dec"/>
</xsd:restriction>
</xsd:simpleType>
</xsd:attribute>
</xsd:complexType>
</xsd:element>
<xsd:complexType name="tItem">
<xsd:annotation>
    <xsd:documentation>
        标记每条信息的开始和结束
    </xsd:documentation>
</xsd:annotation>
<xsd:sequence>
<xsd:element name="key" type="tKey"/>
<xsd:element name="display" type="tDisplay"/>
</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="tKey">
<xsd:annotation>
    <xsd:documentation>
        key关键词，目前一个key标签中只支持输入一个关键词，不支持罗列关键词。当用户在百度检索此关键词时，即可检索到。建议不要包含空格和一些特殊符号（如圆点等符号），字段内容全局唯一，不能重复
    </xsd:documentation>
</xsd:annotation>
<xsd:restriction base="xsd:string">
<xsd:maxLength value="1024"/>
<xsd:minLength value="1"/>
</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="tDisplay">
<xsd:annotation>
    <xsd:documentation>
        display表示该关键词要显示的搜索结果信息，包含以下的url、title等标签。display标签中的文本长度上限为2
    </xsd:documentation>
</xsd:annotation>
<xsd:sequence>
<xsd:element name="url" type="tUrl"/>
<xsd:element name="ba_url" minOccurs="1" maxOccurs="1" type="tBa_url"/>
<xsd:element name="ba_list" minOccurs="2" maxOccurs="6" type="tBa_list"/>
</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="tUrl">
<xsd:annotation>
    <xsd:documentation>
        资源的链接地址
    </xsd:documentation>
</xsd:annotation>
<xsd:restriction base="xsd:anyURI">
<xsd:whiteSpace value="collapse"/>
<xsd:maxLength value="1024"/>
<xsd:minLength value="1"/>
<xsd:pattern value="(https?://)(.+)"/>
</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="tBa_url">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="tBa_list">
<xsd:sequence>
<xsd:element name="frs_url" minOccurs="1" maxOccurs="1" type="tFrs_url"/>
<xsd:element name="member_num" minOccurs="1" maxOccurs="1" type="tMember_num"/>
<xsd:element name="post_num" minOccurs="1" maxOccurs="1" type="tPost_num"/>
<xsd:element name="icon" minOccurs="1" maxOccurs="1" type="tIcon"/>
<xsd:element name="fname" minOccurs="1" maxOccurs="1" type="tFname"/>
<xsd:element name="thread" minOccurs="1" maxOccurs="1" type="tThread"/>
</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="tFrs_url">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="tMember_num">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="tPost_num">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="tIcon">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="tFname">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="tThread">
<xsd:sequence>
<xsd:element name="user_nickname" minOccurs="1" maxOccurs="1" type="tUser_nickname"/>
<xsd:element name="post_num" minOccurs="1" maxOccurs="1" type="t1Post_num"/>
<xsd:element name="last_modified_time" minOccurs="1" maxOccurs="1" type="tLast_modified_time"/>
<xsd:element name="portrait" minOccurs="1" maxOccurs="1" type="tPortrait"/>
<xsd:element name="title" minOccurs="1" maxOccurs="1" type="tTitle"/>
<xsd:element name="content" minOccurs="1" maxOccurs="1" type="tContent"/>
<xsd:element name="focus_num" minOccurs="1" maxOccurs="1" type="tFocus_num"/>
<xsd:element name="like_num" minOccurs="0" maxOccurs="1" type="tLike_num"/>
<xsd:element name="url" minOccurs="1" maxOccurs="1" type="t1Url"/>
<xsd:element name="media" minOccurs="1" maxOccurs="1" type="tMedia"/>
</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="tUser_nickname">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="t1Post_num">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="tLast_modified_time">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="tPortrait">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="tTitle">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="tContent">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="tFocus_num">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="tLike_num">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="t1Url">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="tMedia">
<xsd:sequence>
<xsd:element name="type" minOccurs="1" maxOccurs="1" type="tType"/>
<xsd:element name="pic_num" minOccurs="1" maxOccurs="1" type="tPic_num"/>
<xsd:element name="video_duration" minOccurs="1" maxOccurs="1" type="tVideo_duration"/>
<xsd:element name="thumbnail_url" minOccurs="1" maxOccurs="1" type="tThumbnail_url"/>
</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="tType">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="tPic_num">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="tVideo_duration">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="tThumbnail_url">
<xsd:restriction base="xsd:string">
</xsd:restriction>
</xsd:simpleType>
</xsd:schema>