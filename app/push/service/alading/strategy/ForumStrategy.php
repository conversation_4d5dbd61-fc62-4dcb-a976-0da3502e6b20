<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file ForumStrategy.php
 * <AUTHOR>
 * @date 2013/06/07 19:58:43
 * @brief 
 *  
 **/
class Service_Alading_Strategy_ForumStrategy extends Service_Alading_Strategy_StrategyBase {
    private $intForumId = NULL;
    private $strForumName = NULL;
    private $arrForumInfo = array();
    private $isValid = true;

    public function __construct($intForumId) {
        $this->intForumId = $intForumId;
        $this->strForumName = Dl_Alading_Alading::getForumName($intForumId);
        if(false === $this->strForumName || strlen($this->strForumName) == 0) 
        {
        	$strForumName = Libs_Rpc_Forum::getFnameByFid($intForumId);
        	
        	if (false === $strForumName || 0 == strlen($strForumName))
        	{
        		Bingo_Log::warning("get forumname failed! input[$this->intForumId]. output[$this->strForumName]");
        		$this->isValid = false;
        	}
        	else 
        	{
        		$this->strForumName = $strForumName;
        	}           
        }
    }

    public function isValid() {
        return $this->isValid;
    }
    
    /** 
     * @brief �Ƿ�ΪʱЧ�԰�
     *
     * @param [in/out] req  : $arrConfig 
     * @return  void 
     * @retval   
     * @see 
     * @note  
     * <AUTHOR>
     * @date 2013/06/07 15:36:46
     **/
    public function isTimingWord($arrConfig) {
    	if(isset($arrConfig['from']) &&
    	$arrConfig['from'] == Service_Alading_Strategy_ForumStgyConfig::FROM_CUPID) {
    		return Service_Alading_Forum::isTimingWord($this->intForumId,$this->strForumName);
    	}
        return Service_Alading_Lib_Vocab::isTimingWord($this->strForumName, $arrConfig['file']); 
    }

    /** 
     * @brief ���������
     *
     * @param [in/out] req  : $arrConfig 
     * @return  void 
     * @retval   
     * @see 
     * @note  
     * <AUTHOR>
     * @date 2013/06/07 15:36:46
     * 
     * note by haoyunfeng ���������Ҫ�����ˣ��µ�ʹ�� Service_Alading_Strategy_Forum_ForumStrategy::getWeight
     **/
     public function weight($arrConfig) {
        $weight = 0;
        
        $arrForumInfo = Service_Alading_AladingThreadFrame::getForumInfo($this->intForumId);
        
        if (false == $arrForumInfo)
        {
        	Bingo_Log::warning("getForumInfo fail,the forum id is ".$this->intForumId);
        	return false;
        }
             
        $this->arrForumInfo = $arrForumInfo; //����Ϣ
        $strDir1 = $arrForumInfo['dir1']; //TODO һ��Ŀ¼
        $intManagerNum = intval($arrForumInfo['manager_num']);
        $intFourthManagerNum = isset($arrForumInfo['fourth_manager_num'])?intval($arrForumInfo['fourth_manager_num']):0;
        $intMemberNum = intval($arrForumInfo['member_num']);
        $intPostNum = intval($arrForumInfo['post_num']);
        $intThreadNum = intval($arrForumInfo['thread_num']);
        $intRate = $intThreadNum > 0 ? $intPostNum/$intThreadNum : 0;
        $isInDirList1 = in_array($strDir1, Service_Alading_Strategy_ForumStgyConfig::$FILTERDIR_LIST1) ? true : false;
        $isInDirList2 = in_array($strDir1, Service_Alading_Strategy_ForumStgyConfig::$FILTERDIR_LIST2) ? true : false;
        
        
        //������ֲ���
        if($isInDirList1 && ($intManagerNum >= 1 || $intFourthManagerNum >=1) && $intRate >= 3 && $intThreadNum >= 30) 
        {
            $weight = 1;
        }
        elseif(!$isInDirList1 && !$isInDirList2) 
        {
            if(($intManagerNum >= 1 || $intFourthManagerNum >=1) && $intRate >= 2.5 && $intThreadNum >= 30) 
            {
                if($intThreadNum >= 300 && ($intMemberNum >= 30 && $intRate >= 5)) 
                {
                    $weight = 2; 
                } 
                else 
                {
                    $weight = 1;
                }
            } 
            elseif($intFourthManagerNum >= 1)
            {
            	$weight = 1;
            }
            elseif($intManagerNum == 0 && $intRate >= 3 && $intThreadNum >= 100) 
            {
                $weight = 1;
            }
        }
       
        //����redis��������
        //$arrFields = array(array('field' => 'weight', 'value' => $weight));
        //$arrRet = Dl_Alading_Alading::updateHashInfo(Dl_Alading_Alading::ALDFORUM_HASH, $this->intForumId, $arrFields);
                
        $arrInput = array('weight' => $weight);
        $ret = Dl_Forumhash::setForumWeight($this->intForumId, $arrInput);
        if(false === $ret)
        {
        	Bingo_Log::warning("update HashInfo failed! input[".$this->intForumId.serialize($arrInput)."]");
        }
        //if(in_array($weight, $arrConfig['weight'])) {
        //    return true;
        //}
        return true;
    }
    
    
    
    /** 
     * @brief ����û�а���������
     *
     * @param [in/out] req  : $arrConfig 
     * @return  void 
     * @retval   
     * @see 
     * @note  
     * <AUTHOR>
     * @date 2013/06/07 15:36:46
     **/
    public function maskNoManager($arrConfig) {
        $intManagerNum = 0;
        if(!isset($this->arrForumInfo['manager_num']) && !isset($this->arrForumInfo['fourth_manager_num'])) 
        { 
        	$arrForumInfo = Service_Alading_AladingThreadFrame::getForumInfo($this->intForumId);
        	
        	if (false == $arrForumInfo)
        	{
        		Bingo_Log::warning("getForumInfo fail,the forum id ".$this->intForumId);
        	}
        	
        	$this->arrForumInfo = $arrForumInfo;
        } 
               
        $intManagerNum = $this->arrForumInfo['manager_num'];
        $intFourthManagerNum = $this->arrForumInfo['fourth_manager_num'];
        
        if($intManagerNum == 0 && $intFourthManagerNum == 0) 
        {
            return true;
        }
        return false;
    }

    /** 
     * @brief �Ƿ�ΪPM���õ����ΰ�
     *
     * @param [in/out] req  : $arrConfig 
     * @return  void 
     * @retval   
     * @see 
     * @note  
     * <AUTHOR>
     * @date 2013/06/07 15:36:46
     **/
    public function isMaskForum($arrConfig) {
    	if(isset($arrConfig['from']) &&
    	$arrConfig['from'] == Service_Alading_Strategy_ForumStgyConfig::FROM_CUPID) {
            return Service_Alading_Forum::isMaskForum($this->intForumId,$this->strForumName);   
        }
       return Service_Alading_Lib_Vocab::isMaskForum($this->strForumName, $arrConfig['file']);
    }

    /** 
     * @brief �Ƿ�Ϊueg���õ����ΰ�
     *
     * @param [in/out] req  : $arrConfig 
     * @return  void 
     * @retval   
     * @see 
     * @note  
     * <AUTHOR>
     * @date 2013/06/07 15:36:46
     **/
    public function isUegMaskForum($arrConfig) {
        if(isset($arrConfig['from']) && 
            $arrConfig['from'] == Service_Alading_Strategy_ForumStgyConfig::FROM_CUPID) {
            return Service_Alading_Forum::isUegMaskForum($this->intForumId,$this->strForumName);
        }
        return Service_Alading_Lib_Vocab::isUegMaskForum($this->strForumName, $arrConfig['file']);
    }

    public function getStrategyConfig($attr) {
        $ref = new ReflectionClass('Service_Alading_Strategy_ForumStgyConfig');
        $staticProperties = $ref->getStaticProperties();
        if(!is_null($staticProperties) && isset($staticProperties[$attr])) {
            return $staticProperties[$attr];
        }
        return null;
    }
}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
