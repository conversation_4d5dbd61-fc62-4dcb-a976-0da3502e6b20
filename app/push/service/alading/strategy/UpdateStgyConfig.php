<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file UpadateStgyConfig.php
 * <AUTHOR>
 * @date 2013/11/15 20:08:11
 * @brief 
 *  
 **/
class Service_Alading_Strategy_UpdateStgyConfig {
 	public static $arrUpdateFields = null;
}

Service_Alading_Strategy_UpdateStgyConfig::$arrUpdateFields = array(

									 			//��Ⱥ�����ͳһ��
										        'im_relate' => array(
										        		'update_cycle' => 43200 ,
										        		'update_callback'=>array("Service_Alading_Strategy_UpdateStrategy","updateHashImCount"),
										        ),
										        
										        //�ɼ��һ�����һ��
										        'forum_relate' => array(
										        		'update_cycle' => 86400 ,
										        		'update_callback'=>array("Service_Alading_Strategy_UpdateStrategy","updateHashForumRelate"),
										        ),
										        
										        //�����������
										        'thread_relate' => array(
										        		'update_cycle' => 1800 ,
										        		'update_callback'=>array("Service_Alading_Strategy_UpdateStrategy","updateHashThreadRelate"),
										        ),
);





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
