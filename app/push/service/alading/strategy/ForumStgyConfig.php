<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file StrategyConfig.php
 * <AUTHOR>
 * @date 2013/06/07 20:08:11
 * @brief 
 *  
 **/
class Service_Alading_Strategy_ForumStgyConfig {
    //������������ 
    const PUSH_THREAD_NUM = 3;
    
    const FROM_REDIS = 'redis';
    
    const FROM_CUPID = 'cupid';

    //ɸѡ����
    public static $selectStrategy;
    
    //ɸѡ�ʱ�
    public static $selectVocab; 

    //���δʱ�
    public static $maskVocab;

    //���β���
    public static $maskStrategy;

    public static $FILTERDIR_LIST1 = array(
        '��ҵ����', '����', '��Ӱ','���Ӿ�'
    );

    public static $FILTERDIR_LIST2 = array(
        'B��','����A��','����','������֯����','�ڻ�/Ȩ֤','�������ڷ��񼰲�Ʒ','����A��', '��������'
    );
    
    public static $PRIOR_DIR1_LIST = array('����', '��ѧ', '������Ȼ', '�Ӿ�', '��Ӱ', '���ӽ�Ŀ',
    		                               '����', '�������ǡ����������ʷ����','��ѧ��������������','��Ϸ');
    
  
    public static $LOW_DIR1_LIST = array('���Ѿ��ֲ�','����');
    
    // PM���õ�Ŀ¼����
    public static $GAME_DIR1_LIST = array('��Ϸ',);
    
    public static $STORY_DIR1_LIST = array('��ѧ');
    
    public static $TV_DIR1_LIST = array('���ӽ�Ŀ','���Ӿ�',);
    
    public static $LIFE_DIR1_LIST = array('���Ѿ��ֲ�','����','���',);
    
    public static $SPORT_DIR1_LIST = array('����',);
    
    public static $IT_DIR1_LIST = array('��������',);
    
    public static $STAR_DIR1_LIST = array('��������',);
    
    public static $CARTOON_DIR1_LIST = array('����',);
    
    public static $AREA_DIR1_LIST = array('����',);
    
    public static $MOVIE_DIR1_LIST = array('��Ӱ',);
    
    public static $HOT_THREAD_DIR1_LIST = array('���Ѿ��ֲ�', '����', '����', '����', '��Ϸ', '�ߵ�ԺУ');
    
    public static $FILTER_HOT_THREAD_DIR1_LIST = array('����', '����', '��Ϸ',);   

    public static $SCHOOL_DIR1_LIST = array('�ߵ�ԺУ',);
}

Service_Alading_Strategy_ForumStgyConfig::$selectVocab = array(
    'timing' => array(
        'func' => 'isTimingWord',
        'from' => 'cupid',
    ),
);

Service_Alading_Strategy_ForumStgyConfig::$selectStrategy = array(
    'weightStrategy' => array(
        'weight' => array(1, 2),
        'func' => 'weight',
    ),
);

Service_Alading_Strategy_ForumStgyConfig::$maskVocab = array (
    'pmMaskForum' => array(
        'func' => 'isMaskForum',
        'from' => 'cupid',
    ),
    'uegMaskForum' => array(
        'func' => 'isUegMaskForum',
        'from' => 'cupid',
    ),
);

Service_Alading_Strategy_ForumStgyConfig::$maskStrategy = array(
    'managerStrategy' => array(
        'func' => 'maskNoManager',
    ),
);





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
