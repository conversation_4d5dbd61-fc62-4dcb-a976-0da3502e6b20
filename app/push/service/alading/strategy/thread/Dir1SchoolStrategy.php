<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-09-23
 * @version
 * @structs & methods(copied from idl.)
 * �ߵ�ԺУ��
 * 1.�˹���Ԥ��
 * 2.�������hotthread
 * 3.��Ʒ��
 * 4.��ͨ��
 * ͨ�ò��ԣ�
 * 1. ���ظ�ʱ��1����
 * 2. �ظ�������10
 * 3. �ؼ��ʣ���ѯ|����|����|���|����|�߿�|����|����|ר��|�ؿ�|����|����|����|��ֹ|���|����|����|����|ָ��|�²�|
 *           ��ӭ|��Ϣ|�ٷ�|רҵ|����|����|����|��ҵ|����|���ʱش�|����|����|����|ӭ��|��ѧ��֪
 */
class Service_Alading_Strategy_Thread_Dir1SchoolStrategy{

    /**
     * @param $arrInput
     * @return array
     */
    public static function execute($arrInput){

        $arrOutput = array();
        if (!is_array($arrInput) || empty($arrInput)){
            Bingo_Log::warning("input param error, the param is ".serialize($arrInput));
            return $arrOutput;
        }

        //����Դ����
        $arrForumInfoSrc      = isset($arrInput['forum_info'])?$arrInput['forum_info']:array();
        if (empty($arrForumInfoSrc)){
            Bingo_Log::warning("input param error, the param is ".serialize($arrInput));
            return $arrOutput;
        }
        $arrGoodThreadListSrc = isset($arrInput['good_thread_list'])?$arrInput['good_thread_list']:array();
        $arrCommonThreadListSrc     = isset($arrInput['common_thread_list'])?$arrInput['common_thread_list']:array();
        $arrSuperListSrc      = isset($arrInput['super_list'])?$arrInput['super_list']:array();
        $arrHotListSrc      = isset($arrInput['hot_thread_list'])?$arrInput['hot_thread_list']:array();
        $arrTopListObj = self::_selectOffice($arrForumInfoSrc, $arrCommonThreadListSrc);
	$intOfficeTopNum = count($arrTopListObj);
	$arrGoodThreadListObj = array();
        $arrHotListObj = array();
        $intHotNum = 0;
        
        if (!empty($arrHotListSrc))
        {
        	$fltFilterNum = 0.1;
        	$intLimit = 3;
        	$strForumName = isset($arrForumInfoSrc['forum_name']) ? strval($arrForumInfoSrc['forum_name']) : '';
        	$arrHotListObj = Service_Alading_Strategy_ThreadStrategy::filterThreadByClickSim($arrHotListSrc,
        			$strForumName, $fltFilterNum, $intLimit);
                $arrHotListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrHotListObj, 'hot');
        	$intHotNum = count($arrHotListObj);
        }
        
        //�����������ͣ�������־��׷������
        $arrSuperListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrSuperListSrc, 'super');
        // $arrHotListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrHotListSrc, 'hot');
        // $arrGoodListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrGoodThreadListSrc, 'good');
        // $arrCommonListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrCommonThreadListSrc, 'common');

        $intGoodNum   = 0;
        $intCommonNum = 0;
        $intSuperNum  = count($arrSuperListObj);
        $intHotNum    = count($arrHotListObj);
        $intSuperHotNum = $intSuperNum + $intHotNum;


        if($intSuperHotNum + $intHotNum < Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM){

            $arrGoodThreadListObj = self::_selectGoodThread($arrForumInfoSrc, $arrGoodThreadListSrc, Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM);
            $intFirstGoodNum = isset($arrGoodThreadListObj['first_list']) ? count($arrGoodThreadListObj['first_list']) : 0;
            $intSecondGoodNum = isset($arrGoodThreadListObj['second_list']) ? count($arrGoodThreadListObj['second_list']) : 0;
            $intThreeGoodNum = isset($arrGoodThreadListObj['three_list']) ? count($arrGoodThreadListObj['three_list']) : 0;
            $intGoodNum = $intFirstGoodNum + $intSecondGoodNum + $intThreeGoodNum;
        }

        if($intSuperHotNum + $intHotNum < Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM){
            $arrCommonThreadListObj = self::_selectCommonThread($arrForumInfoSrc, $arrCommonThreadListSrc, Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM);
        }

       
        // ע��һ��Ҫ�����˳����뵽$arrThreadInfos���ϲ���ʱ��ᰴ�����˳��ϲ�
        $arrThreadInfos[] = $arrSuperListObj;
        
        if ($intOfficeTopNum > 0)
        {
        	$arrThreadInfos[] = $arrTopListObj;
        }
        
        $arrThreadInfos[] = $arrHotListObj;
        
        if (isset($arrGoodThreadListObj['first_list']))
        {
        	$arrThreadInfos[] = $arrGoodThreadListObj['first_list'];
        }
        
        if (isset($arrGoodThreadListObj['second_list']))
        {
        	$arrThreadInfos[] = $arrGoodThreadListObj['second_list'];
        }
        
        if (isset($arrGoodThreadListObj['three_list']))
        {
        	$arrThreadInfos[] = $arrGoodThreadListObj['three_list'];
        }
        
        if (isset($arrCommonThreadListObj['first_list']))
        {
        	$arrThreadInfos[] = $arrCommonThreadListObj['first_list'];
        }
        
        if (isset($arrCommonThreadListObj['second_list']))
        {
        	$arrThreadInfos[] = $arrCommonThreadListObj['second_list'];
        }
        
        if (isset($arrCommonThreadListObj['three_list']))
        {
        	$arrThreadInfos[] = $arrCommonThreadListObj['three_list'];
        }

        $arrThreadOutput = Service_Alading_Strategy_ThreadStrategy::multiUniMerageThread($arrThreadInfos, Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM);
        $arrOutput['thread_list'] = $arrThreadOutput;
        return $arrOutput;

    }

    /**
     * @param $arrForumInfo
     * @param $arrGoodThreadList
     * @param int $intMaxThreadNum
     * @return array
     */
    private static function _selectGoodThread($arrForumInfo, $arrGoodThreadList, $intMaxThreadNum = 3){

        $arrData = array();

        if (!is_array($arrForumInfo) || !is_array($arrGoodThreadList) || empty($arrGoodThreadList)){
            Bingo_Log::warning("input param error");
            return $arrOutput;
        }

        $dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';
        $strForumName = isset($arrForumInfo['forum_name']) ? strval($arrForumInfo['forum_name']) : '';
        $firstNum   = 0;   // ��һ��������
        $secondNum  = 0;   // �ڶ���������

        $arrFirstData = array();
        $arrSecondData = array();

        foreach ($arrGoodThreadList as $arrGoodThreadItem)
        {
            $title = isset($arrGoodThreadItem["title"])?$arrGoodThreadItem["title"]:'';
            //�����Ƽ����������治�ܰ����ɹ�Ͱ�������δ�
            if(true === Service_Alading_Strategy_ThreadStrategy::hasFilterWord($title,$dir1)){
                continue;
            }
            $title = Libs_Util_String::filterNonGBKChar($title);

            $bolTitle = strlen($title) > 10 ? true : false;
            if (false === $bolTitle){
                continue;
            }

            // �ж��Ƿ��йؼ���
            $bolIsHasKey = self::_isHasSchoolKey($arrGoodThreadItem);
            // �ж����ظ������Ƿ�С��7��
            $day7 = 604800;
            $bolIsLessSeven = Service_Alading_Strategy_ThreadStrategy::isNearDays($arrGoodThreadItem, $day7);
            $arrGoodThreadItem['push_type'] = 'good';
            $arrGoodThreadItem['taglist'][] = '��';
            
            if ($bolIsHasKey && $bolIsLessSeven)
            {
                $arrFirstData[] = $arrGoodThreadItem;               
            }
            elseif($bolIsLessSeven)
            {
                $arrSecondData[] = $arrGoodThreadItem;                
            }           
        }

        $intMaxSortNum = 20;
        $arrFirstDataSort = Service_Alading_Strategy_ThreadStrategy::arsortByField($arrFirstData, 'post_num', 'int');
        $arrFirstDataSort = array_slice($arrFirstDataSort, 0, $intMaxSortNum);
        
        $intFirstNum = count($arrFirstDataSort);
        if ($intFirstNum > $intMaxThreadNum)
        {
        	$arrData['first_list'] = $arrFirstDataSort;
        	return $arrData;
        }
        
        $arrData['first_list'] = $arrFirstDataSort;
        $fltRestrict = 0.1;
        $intLimit = 3;
        $arrSecondDataSort = Service_Alading_Strategy_ThreadStrategy::arsortByField($arrSecondData, 'post_num', 'int');
        $arrSecondDataSim = Service_Alading_Strategy_ThreadStrategy::filterThreadByClickSim($arrSecondDataSort, $strForumName, $fltRestrict, $intLimit);
        
        if (count($arrSecondDataSim) > $intMaxThreadNum)
        {
        	$arrData['second_list'] = $arrSecondDataSim;
        	return $arrData;
        }
        
        $arrData['three_list'] = $arrSecondDataSort;
        return $arrData;
    }

    /**
     * @param $arrForumInfo
     * @param $arrGoodThreadList
     * @param int $intMaxThreadNum
     * @return array
     */
    private static function _selectCommonThread($arrForumInfo, $arrCommonThreads, $intMaxThreadNum = 3){

        $arrFirstData = array();       // �ظ���>9  ���ⳬ��6���ֽڣ�����һ�ؼ��ʿ��йؼ��ʵ�����
        $arrSecondData = array(); // �ظ���>9  ���ⳬ��6���ֽڵ�������
        $arrThreeData = array();  // �������

        if (!is_array($arrCommonThreads)){

            return array();
        }
        //��ʼɸѡ����,�ظ���>9  ���ⳬ��6���ֽڣ�����һ�ؼ��ʿ��йؼ��ʵ���������
        $firstNum=0;            // �ظ���>9  ���ⳬ��6���ֽڣ�����һ�ؼ��ʿ��йؼ��ʵ�������
        $secondNum = 0;    // �ظ���>9  ���ⳬ��6���ֽڵ�������
        $threeNum = 0;     // �������
        $dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';
        $strForumName = isset($arrForumInfo['forum_name']) ? strval($arrForumInfo['forum_name']) : '';

        foreach ($arrCommonThreads as $arrCommonThreadItem)
        {
            $title = isset($arrCommonThreadItem["title"])?$arrCommonThreadItem["title"]:'';
            $intPostNum = isset($arrCommonThreadItem['post_num'])?intval($arrCommonThreadItem['post_num']):0;
            //�����Ƽ����������治�ܰ����ɹ�Ͱ�������δ�
            if(true === Service_Alading_Strategy_ThreadStrategy::hasFilterWord($title,$dir1)){

                continue;
            }

            $title = Libs_Util_String::filterNonGBKChar($title);
            $bolTitle = strlen($title) > 10 ? true : false;

            if (false === $bolTitle){

                continue;
            }

            $bolPostNum = $intPostNum > 9 ? true : false;
            $bolHasKey = self::_isHasSchoolKey($arrCommonThreadItem);
            // �ж����ظ������Ƿ�С��7��
            $day7 = 604800;
            $bolIsLessSeven = Service_Alading_Strategy_ThreadStrategy::isNearDays($arrCommonThreadItem, $day7);
            $arrCommonThreadItem['push_type'] = 'common';
            
            if(true === $bolHasKey && true === $bolIsLessSeven)
            {                
                $arrFirstData[] = $arrCommonThreadItem;
            }
            elseif(true === $bolIsLessSeven)
            {                
                $arrSecondData[] = $arrCommonThreadItem;                
            }           
        }

        $intMaxSortNum = 20;
		$arrFirstDataSort = Service_Alading_Strategy_ThreadStrategy::arsortByField($arrFirstData, 'post_num', 'int');
		$arrFirstDataSort = array_slice($arrFirstDataSort, 0, $intMaxSortNum);
		
		$intFirstNum = count($arrFirstDataSort);
		if ($intFirstNum > $intMaxThreadNum)
		{
			$arrData['first_list'] = $arrFirstDataSort;
			return $arrData;
		}
		
		$arrData['first_list'] = $arrFirstDataSort;
		$fltRestrict = 0.1;
		$intLimit = 3;
		$arrSecondDataSort = Service_Alading_Strategy_ThreadStrategy::arsortByField($arrSecondData, 'post_num', 'int');
		$arrSecondDataSim = Service_Alading_Strategy_ThreadStrategy::filterThreadByClickSim($arrSecondDataSort, $strForumName, $fltRestrict, $intLimit);
		if (count($arrSecondDataSim) > $intMaxThreadNum)
		{
			$arrData['second_list'] = $arrSecondDataSim;
			return $arrData;
		}
		
		$arrData['three_list'] = $arrSecondDataSort;
		return $arrData;
    }

    /**
     * @param $arrThread
     * @return bool
     */
    private static function _isHasSchoolKey($arrThread){

        $title = isset($arrThread["title"])?$arrThread["title"]:'';
        $strPreg = "/��ѯ|����|����|���|����|�߿�|����|����|ר��|�ؿ�|����|����|����|��ֹ|���|����|����|����|ָ��|�²�|��ӭ|��Ϣ|�ٷ�|רҵ|����|����|����|��ҵ|����|���ʱش�|����|����|����|ӭ��|��ѧ��֪/i";
        $res = preg_match($strPreg, $title);
        if($res > 0){
            return true;
        }else{
            return false;
        }
    }
    
    /**
     * ��ȡ��ֱ����Ӫ�ɵ��˹���Ԥ����
     * @param unknown_type $arrForumInfo
     * @param unknown_type $arrThreadList
     * @param unknown_type $intMaxThreadNum
     * @return multitype:|multitype:unknown
     */
    private static function _selectOffice($arrForumInfo, $arrThreadList, $intMaxThreadNum = 3)
    {
    	$arrData = array();
    
    	if (!is_array($arrForumInfo) || empty($arrForumInfo)
    			|| !is_array($arrThreadList) || empty($arrThreadList))
    	{
    		Bingo_Log::warning("input param error");
    		return $arrData;
    	}
    
    	$dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';
    	$strForumName = isset($arrForumInfo['forum_name']) ? strval($arrForumInfo['forum_name']) : '';
    	$intIsOfficeAldThread = isset($arrForumInfo['is_ald_thread']) ? intval($arrForumInfo['is_ald_thread']) : 0;
    	//$intIsOfficeAldThread = 1; // test
    	if (0 === $intIsOfficeAldThread)
    	{
    		return $arrData;
    	}
    
    	foreach ($arrThreadList as $key => $arrThreadItem)
    	{
    		$title = isset($arrThreadItem["title"])?$arrThreadItem["title"]:'';
    		$title = Libs_Util_String::filterNonGBKChar($title);
    
    		$intIsDelete = isset($arrThreadItem['is_deleted']) ? intval($arrThreadItem['is_deleted']) : 0;
            $intIsPartialVisible = isset($arrThreadItem['is_partial_visible']) ? intval($arrThreadItem['is_partial_visible']) : 0;
            
    		if (1 == $intIsDelete || 1 == $intIsPartialVisible)
    		{
    			continue;
    		}
    
    		//�����Ƽ����������治�ܰ����ɹ�Ͱ�������δ�
    		if(true === Service_Alading_Strategy_ThreadStrategy::hasFilterWord($title,$dir1))
    		{
    			continue;
    		}
    		$bolTitle = strlen($title) > 10 ? true : false;
    
    		if (false === $bolTitle)
    		{
    			continue;
    		}
    		$intIsTop =  isset($arrThreadItem['top_types']) ? intval($arrThreadItem['top_types']) : 0;
    		$intIsGood = isset($arrThreadItem['good_types']) ? intval($arrThreadItem['good_types'])	: 0;
    
    		if (1 == $intIsTop)
    		{
    			$arrData[] = $arrThreadItem;
    		}
    	}
    	$arrData = Service_Alading_Strategy_ThreadStrategy::setPushType($arrData, 'top');
    	return $arrData;
    }
}
