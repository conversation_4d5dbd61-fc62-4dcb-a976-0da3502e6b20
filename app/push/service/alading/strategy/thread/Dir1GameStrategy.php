<?php

/**
 * ��Ϸ����
 * 
 * 1��	��Ʒ���ڰ����ؼ��ʿ⣨�總������һ�ؼ��ʵ����ӣ������ظ�ʱ����һ�����ڣ�
   2��	��ȡ��Ʒ����ǰ3�������ɹ桱�������񡱾�Ʒ����������¾�Ʒ�������ظ�ʱ�䳬��7�죻
   3��	Sep�ھ����������
   4��	��ͨ�����ԣ��ظ���>9  ���ⳬ��6���ֽڣ��ظ�ʱ�����µ����ӣ�����һ�ؼ��ʿ��йؼ��ʵ��������ȡ�
   ����|���|����|����|����|©��|������|��Ƶ|����|����Ƭ|ֱ��|����|����|����|������|�ܱ�|��˵|�汾|����|�ĵ�|����|����
 * <AUTHOR>
 *
 */
class Service_Alading_Strategy_Thread_Dir1GameStrategy
{
	public static function execute($arrInput)
	{
		$arrOutput = array();
		if (!is_array($arrInput) || empty($arrInput))
		{
			Bingo_Log::warning("input param error, the param is ".serialize($arrInput));
			return $arrOutput;
		}
		
		$arrForumInfoSrc      = isset($arrInput['forum_info'])?$arrInput['forum_info']:array();
		$arrGoodThreadListSrc = isset($arrInput['good_thread_list'])?$arrInput['good_thread_list']:array();
		$arrThreadListSrc     = isset($arrInput['common_thread_list'])?$arrInput['common_thread_list']:array();
		$arrSepListSrc        = isset($arrInput['sep_list'])?$arrInput['sep_list']:array();
		$arrSuperListSrc      = isset($arrInput['super_list'])?$arrInput['super_list']:array();
		
		$arrSuperListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrSuperListSrc, 'super');
		$arrSepListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrSepListSrc, 'sep');
		
		if (empty($arrForumInfoSrc))
		{
			Bingo_Log::warning("input param error, the param is ".serialize($arrInput));
			return $arrOutput;
		}
		
		$arrHotListSrc        = isset($arrInput['hot_thread_list'])?$arrInput['hot_thread_list']:array();
		$arrHotListObj = array();
		$intHotNum = 0;
		
		if (!empty($arrHotListSrc))
		{
			$fltFilterNum = 0.1;
			$intLimit = 3;
			$strForumName = isset($arrForumInfoSrc['forum_name']) ? strval($arrForumInfoSrc['forum_name']) : '';
			$arrHotListObj = Service_Alading_Strategy_ThreadStrategy::filterThreadByClickSim($arrHotListSrc,
					$strForumName, $fltFilterNum, $intLimit);
			$arrHotListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrHotListObj, 'hot');
			$intHotNum = count($arrHotListObj);
		}
		
		$arrGoodThreadListObj = array();
		$arrCommonThreadListObj = array();	

		$intGoodNum = 0;
		$intSepNum = count($arrSepListObj);
		$intSuperNum = count($arrSuperListObj);	
		
		if ($intSuperNum + $intHotNum <= Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM)
		{
			$intRemainder = Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM;
			$arrGoodThreadListObj = self::_selectGoodThread($arrForumInfoSrc, $arrGoodThreadListSrc, $intRemainder);
			$intFirstGoodNum = isset($arrGoodThreadListObj['first_list']) ? count($arrGoodThreadListObj['first_list']) : 0;
			$intSecondGoodNum = isset($arrGoodThreadListObj['second_list']) ? count($arrGoodThreadListObj['second_list']) : 0;
			$intThreeGoodNum = isset($arrGoodThreadListObj['three_list']) ? count($arrGoodThreadListObj['three_list']) : 0;
			$intGoodNum = $intFirstGoodNum + $intSecondGoodNum + $intThreeGoodNum;
		}
		
		if ($intGoodNum + $intSuperNum + $intHotNum <= Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM)
		{
			$intRemainder = Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM;
			$arrCommonThreadListObj = self::_selectCommonThread($arrForumInfoSrc, $arrThreadListSrc, $intRemainder);
		}	

		// ע��һ��Ҫ�����˳����뵽$arrThreadInfos���ϲ���ʱ��ᰴ�����˳��ϲ�
		$arrThreadInfos[] = $arrSuperListObj;
		$arrThreadInfos[] = $arrHotListObj;
		if (isset($arrGoodThreadListObj['first_list']))
		{
			$arrThreadInfos[] = $arrGoodThreadListObj['first_list'];
		}
		
		if (isset($arrGoodThreadListObj['second_list']))
		{
			$arrThreadInfos[] = $arrGoodThreadListObj['second_list'];
		}
		
		if (isset($arrGoodThreadListObj['three_list']))
		{
			$arrThreadInfos[] = $arrGoodThreadListObj['three_list'];
		}

		if (isset($arrCommonThreadListObj['first_list']))
		{
			$arrThreadInfos[] = $arrCommonThreadListObj['first_list'];
		}
		
		if (isset($arrCommonThreadListObj['second_list']))
		{
			$arrThreadInfos[] = $arrCommonThreadListObj['second_list'];
		}
		
		if (isset($arrCommonThreadListObj['three_list']))
		{
			$arrThreadInfos[] = $arrCommonThreadListObj['three_list'];
		}

		$arrThreadOutput = Service_Alading_Strategy_ThreadStrategy::multiUniMerageThread($arrThreadInfos, Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM);
		$arrThreadOutput = self::_changeTitle($arrThreadOutput, $arrForumInfoSrc);
		$arrOutput['thread_list'] = $arrThreadOutput;
		return $arrOutput;		
	}
	
	/**
	 * ��ȡ��Ʒ�����б�
	 * @param unknown_type $arrForumInfo
	 * @param unknown_type $arrGoodThreadList
	 * @param unknown_type $intMaxThreadNum
	 * @return multitype:|string
	 */
	private static function _selectGoodThread($arrForumInfo, $arrGoodThreadList, $intMaxThreadNum = 3)
	{
		$arrData = array();
		$arrFirstData = array();
		$arrSecondData = array();
		
		if (!is_array($arrForumInfo) || empty($arrForumInfo) 
		|| !is_array($arrGoodThreadList) || empty($arrGoodThreadList))
		{
			Bingo_Log::warning("input param error");
			return $arrData;
		}
		
		$dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';
		$strForumName = isset($arrForumInfo['forum_name']) ? strval($arrForumInfo['forum_name']) : '';
		
		//��ʼɸѡ����
		$num=0;
		foreach ($arrGoodThreadList as $arrGoodThreadItem)
		{
			$title = isset($arrGoodThreadItem["title"])?$arrGoodThreadItem["title"]:'';
			//�����Ƽ����������治�ܰ����ɹ�Ͱ�������δ�
			if(true === Service_Alading_Strategy_ThreadStrategy::hasFilterWord($title,$dir1))
			{
				continue;
			}
			
			$title = Libs_Util_String::filterNonGBKChar($title);
			$bolTitle = strlen($title) > 10 ? true : false;
			
			if (false === $bolTitle)
			{
				continue;
			}
		
			// �ж��Ƿ��йؼ���
			$bolIsHasKey = self::_isHasGameKey($arrGoodThreadItem);
		
			// �ж����ظ������Ƿ�С��7��
			$day7 = 604800;
			$day30 = 86400 * 30;
			$bolIsLessSeven = Service_Alading_Strategy_ThreadStrategy::isNearDays($arrGoodThreadItem, $day7);
			$bolIsCreateLessThirty = Service_Alading_Strategy_ThreadStrategy::isCreateNearDays($arrGoodThreadItem, $day30);
			$arrGoodThreadItem['push_type'] = 'good';
			$arrGoodThreadItem['taglist'][] = '��';
			
			if (true === $bolIsHasKey && true === $bolIsLessSeven && true === $bolIsCreateLessThirty)
			{				
				$arrFirstData[] = $arrGoodThreadItem;
			}
			else
			{
				$arrSecondData[] = $arrGoodThreadItem;
			}
		}
		 
		$intMaxSortNum = 20;
		$arrFirstDataSort = Service_Alading_Strategy_ThreadStrategy::arsortByField($arrFirstData, 'post_num', 'int');
		$arrFirstDataSort = array_slice($arrFirstDataSort, 0, $intMaxSortNum);
		
		$intFirstNum = count($arrFirstDataSort);
		if ($intFirstNum > $intMaxThreadNum)
		{
			$arrData['first_list'] = $arrFirstDataSort;
			return $arrData;
		}
		
		$arrData['first_list'] = $arrFirstDataSort;		
		$fltRestrict = 0.1;
		$intLimit = 3;
		$arrSecondDataSort = Service_Alading_Strategy_ThreadStrategy::arsortByField($arrSecondData, 'post_num', 'int');
		$arrSecondDataSim = Service_Alading_Strategy_ThreadStrategy::filterThreadByClickSim($arrSecondDataSort, $strForumName, $fltRestrict, $intLimit);
		
		if (count($arrSecondDataSim) > $intMaxThreadNum)
		{
			$arrData['second_list'] = $arrSecondDataSim;
			return $arrData;
		}
		
		$arrData['three_list'] = $arrSecondDataSort;
		return $arrData;
	}
	
	/**
	 * ��ȡ��Ϸ��ͨ��
	 * @param unknown_type $arrForumInfo
	 * @return multitype:unknown
	 */
	private static function _selectCommonThread($arrForumInfo, $arrCommonThreads, $intMaxThreadNum = 3)
	{
		$arrData=array(); // ���������������б�
		$arrPostData = array(); // �洢�ظ���>=9������
		$arrFirstData = array();
		$arrSecondData = array();	 
				 
		// ����������ݸ�ʽ ��ֱ�ӷ��ؿ�
		if (!is_array($arrCommonThreads))
		{
			return array();
		}
					
		//��ʼɸѡ���� ��ͨ�����ԣ��ظ���>9  ���ⳬ��6���ֽڣ�����һ�ؼ��ʿ��йؼ��ʵ���������
		$num=0;
		$dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';
		$strForumName = isset($arrForumInfo['forum_name']) ? strval($arrForumInfo['forum_name']) : '';
		
		foreach ($arrCommonThreads as $arrCommonThreadItem)
		{
			$title = isset($arrCommonThreadItem["title"])?$arrCommonThreadItem["title"]:'';
			//�����Ƽ����������治�ܰ����ɹ�Ͱ�������δ�
			if(true === Service_Alading_Strategy_ThreadStrategy::hasFilterWord($title,$dir1))
			{
				continue;
			}
			
			$title = Libs_Util_String::filterNonGBKChar($title);
			$bolTitle = strlen($title) > 10 ? true : false;
					
			if (false === $bolTitle)
			{
				continue;
			}
			
			// �ж��Ƿ��йؼ���
			$bolIsHasKey = self::_isHasGameKey($arrCommonThreadItem);
			
			if (true === $bolIsHasKey)
			{
				$arrFirstData[] = $arrCommonThreadItem;
			}
			else
			{
				$arrSecondData[] = $arrCommonThreadItem;
			}
		}

		$intMaxSortNum = 20;
		$arrFirstDataSort = Service_Alading_Strategy_ThreadStrategy::arsortByField($arrFirstData, 'post_num', 'int');
		$arrFirstDataSort = array_slice($arrFirstDataSort, 0, $intMaxSortNum);
		
		$intFirstNum = count($arrFirstDataSort);
		if ($intFirstNum > $intMaxThreadNum)
		{
			$arrData['first_list'] = $arrFirstDataSort;
			return $arrData;
		}
		
		$arrData['first_list'] = $arrFirstDataSort;
		$fltRestrict = 0.1;
		$intLimit = 3;
		$arrSecondDataSort = Service_Alading_Strategy_ThreadStrategy::arsortByField($arrSecondData, 'post_num', 'int');
		$arrSecondDataSim = Service_Alading_Strategy_ThreadStrategy::filterThreadByClickSim($arrSecondDataSort, $strForumName, $fltRestrict, $intLimit);
		
		if (count($arrSecondDataSim) > $intMaxThreadNum)
		{
			$arrData['second_list'] = $arrSecondDataSim;
			return $arrData;
		}
		
		$arrData['three_list'] = $arrSecondDataSort;
		return $arrData;
	}
	
	/**
	 * ���ӱ������Ƿ��йؼ���
	 * @param unknown_type $arrThread
	 * @return boolean
	 */
	private static function _isHasGameKey($arrThread)
	{
		$title = isset($arrThread["title"])?$arrThread["title"]:'';
		$strPreg = "/����|���|����|����|����|©��|������|��Ƶ|����|����Ƭ|����|����|����|������|�ܱ�|��˵|�汾|����|�ĵ�|����|����/i";
		$ret = preg_match($strPreg, $title);
		if ($ret > 0)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
	
	/**
	 * �ж��Ƿ���һ��������������ͨ����
	 * @param unknown_type $arrThread
	 * @return number  0 �����йؼ��ʣ�1 ֻ���йؼ��ʣ�2 ֻ�ظ������ڵ��� 9��3 ���йؼ��ʲ��һظ������ڵ���9
	 */
	private static function _isGoodCommonGameThread($arrThread)
	{
		// ����ֵ ȡֵ��Χ˵�����£� 0 �����йؼ��ʣ�1 ֻ���йؼ��ʣ�2 ֻ�ظ������ڵ��� 9��3 ���йؼ��ʲ��һظ������ڵ���9
		$intNone = 0;
		$intHasKey = 1;
		$intHasPost = 2;
		$intKeyAndPost = 3;
		$intGoodPostNum = 9; // �ظ�������9������Ϊ��ͨ�����еĺ�����
		$intGoodTitleNum = 6; // ���ӱ����>6���ֽ�		 
		$intPostNum = isset($arrThread['post_num'])?intval($arrThread['post_num']):0;
		$title = isset($arrThread["title"])?$arrThread["title"]:'';
		 
		if (strlen($title) < $intGoodTitleNum)
		{
			return $intNone;
		}
		 
		$isHasKey = self::_isHasGameKey($arrThread);
		 
		if ($intPostNum  >= $intGoodPostNum)
		{
			if (true === $isHasKey)
			{
				return $intKeyAndPost;
			}
			else
			{
				return $intHasPost;
			}
		}
		else
		{
			if (true === $isHasKey)
			{
				return $intHasKey;
			}
			else
			{
				return $intNone;
			}
		}
	}
	
	/**
	 * ��������ǰ׺
	 * @param unknown_type $arrThreadInfo
	 * @param unknown_type $arrForumInfo
	 * @return boolean|multitype:Ambigous <false, multitype:, array, boolean, mixed, string, unknown_type>
	 */
	private static function _changeTitle($arrThreadInfo, $arrForumInfo)
	{
		if (empty($arrThreadInfo))
		{
			return false;
		}
	
		$arrData = array();
	
		foreach($arrThreadInfo as $thread)
		{			
			$thread = self::_updateTitlePre($thread, $arrForumInfo);
			$arrData[] = $thread;
		}
	
		return $arrData;
	}
	
	
	/**
	 *  ��������ǰ׺
	 * @param array $arrThread
	 * @param array $arrForumInfo
	 * @return false|array
	 *
	 */
	private static function _updateTitlePre($arrThread, $arrForumInfo)
	{
		$strForumName = isset($arrForumInfo['forum_name']) ? strval($arrForumInfo['forum_name']) : '';
		$strTitle = isset($arrThread['title']) ? strval($arrThread['title']) : '';
		$intTitleLen = strlen($strTitle);
		$intMaxLen = 34;
		$intKeyPos = strpos($strTitle, $strForumName);
		//echo "len = $intTitleLen \n";
		//echo "pos = $intKeyPos \n";
		if (false === $intKeyPos && $intTitleLen < $intMaxLen)
		{
			$arrThreadRet = Service_Alading_Strategy_ThreadStrategy::updateTitlePrefix($arrThread, $strForumName);
			//echo "change title = ".$arrThreadRet['title']."\n";
			if (false === $arrThreadRet)
			{
				return $arrThread;
			}
			else
			{
				return $arrThreadRet;
			}
		}
			
		return $arrThread;
	}
}
