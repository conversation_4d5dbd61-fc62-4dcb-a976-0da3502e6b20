<?php
/**
 * ��ѧĿ¼����ǰ�������ӵĲ��Բ��䣬���������ӵĲ����Ż����£�
 * ȡ��Ʒ�������к����¡����ڣ�����ǰ������Ϊ��Сд���ֵ����ӣ���һ�����Ӽ��׷����ڶ������Ӽ��ִ�
1��	ȡ��Ʒ�����ӱ�������ؼ��ʿ⣨���£���һ�ؼ��ʵ����ӣ������ظ�ʱ�䲻����7�죻 
2��	��ͨ�����ԣ��ظ���>9 �� ���ⳬ��6���ֽڣ�����һ�ؼ��ʿ��йؼ��ʵ��������ȣ�
3��	��ͨ�������
�ؼ��ʣ�ԭ�����������ۡ����ۡ��������ں����²⡢Ԥ�⡢��֡���β������

 * <AUTHOR>
 *
 */
class Service_Alading_Strategy_Thread_Dir1StoryStrategy
{
	//С˵������������Ӻ�׺
	private static $_arrStoryExtra = array("���׷���","���ִ�");	 
	private static $_arrLimitWordA = array("һ","��","��","��","��","��","��","��","��","ʮ","��","00","01","02","03","04","05","06","07","08","09");
	private static $_arrLimitWordB = array('1','2','3','4','5','6','7','8','9');
	
	public static function execute($arrInput)
	{
		$arrOutput = array();
		if (!is_array($arrInput) || empty($arrInput))
		{
			Bingo_Log::warning("input param error, the param is ".serialize($arrInput));
			return $arrOutput;
		}
		
		$arrForumInfoSrc      = isset($arrInput['forum_info'])?$arrInput['forum_info']:array();
		$arrGoodThreadListSrc = isset($arrInput['good_thread_list'])?$arrInput['good_thread_list']:array();
		$arrThreadListSrc     = isset($arrInput['common_thread_list'])?$arrInput['common_thread_list']:array();
		$arrSepListSrc        = isset($arrInput['sep_list'])?$arrInput['sep_list']:array();
		$arrSuperListSrc      = isset($arrInput['super_list'])?$arrInput['super_list']:array();

		$arrSuperListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrSuperListSrc, 'super');
		$arrSepListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrSepListSrc, 'sep');

		if (empty($arrForumInfoSrc))
		{
			Bingo_Log::warning("input param error, the param is ".serialize($arrInput));
			return $arrOutput;
		}

		$arrGoodThreadListObj = array();
		$arrCommonThreadListObj = array();	
		
		$intGoodNum = 0;
		$intSepNum = count($arrSepListObj);
		$intSuperNum = count($arrSuperListObj);
				
		if ($intSuperNum <= Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM)
		{
			$intRemainder = Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM;
			$arrGoodThreadListObj = self::_selectGoodThread($arrForumInfoSrc, $arrGoodThreadListSrc, $intRemainder);
			$intGoodNum = count($arrGoodThreadListObj);
		}
                
        if ($intGoodNum <= Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM)
		{
			$intRemainder = Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM;
			$arrCommonThreadListObj = self::_selectCommonThread($arrForumInfoSrc, $arrThreadListSrc, $intRemainder);
		}	
		
		// ע��һ��Ҫ�����˳����뵽$arrThreadInfos���ϲ���ʱ��ᰴ�����˳��ϲ�
		$arrThreadInfos[] = $arrSuperListObj;
		$arrThreadInfos[] = $arrSepListObj;
		$arrThreadInfos[] = $arrGoodThreadListObj;		
		$arrThreadInfos[] = $arrCommonThreadListObj;
		$arrThreadOutput = Service_Alading_Strategy_ThreadStrategy::multiUniMerageThread($arrThreadInfos, Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM);
		$arrOutput['thread_list'] = $arrThreadOutput;
		
		// �����Ʒ��������0��˵��С˵�����п��ܺ��С��¡��ڡ�����
		if ($intGoodNum > 0)
		{
			$intSltChapterNum = 3;
			$arrChapter = self::_selectChapter($arrForumInfoSrc, $arrGoodThreadListSrc, $intSltChapterNum);
		    if (count($arrChapter) > 0)
		    {
		    	$arrOutput['ext_info']['chapter'] = $arrChapter;
		    }
		}
		
		return $arrOutput;
	}
	
	/**
	 * ��ȡ��Ʒ��
	 * @param unknown_type $arrForumInfo
	 * @earam unknown_type $arrGoodThreadList
	 * @param unknown_type $intMaxThreadNum
	 * @return multitype:|string
	 */
	private static function _selectGoodThread($arrForumInfo, $arrGoodThreadList, $intMaxThreadNum = 3)
	{
		$arrData = array();
		$arrThreeData = array();
	
		if (!is_array($arrForumInfo) || empty($arrForumInfo)
				|| !is_array($arrGoodThreadList) || empty($arrGoodThreadList))
		{
			Bingo_Log::warning("input param error");
			return $arrData;
		}
	
		$dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';
			
		$num=0;        // ǰ����������
		$threeNum = 0; // ������������
		foreach ($arrGoodThreadList as $arrGoodThreadItem)
		{
			$title = isset($arrGoodThreadItem["title"])?$arrGoodThreadItem["title"]:'';
			//�����Ƽ����������治�ܰ����ɹ�Ͱ�������δ�
			if(true === Service_Alading_Strategy_ThreadStrategy::hasFilterWord($title,$dir1))
			{
				continue;
			}
			
			$title = Libs_Util_String::filterNonGBKChar($title);
			$bolTitle = strlen($title) > 6 ? true : false;
				
			if (false === $bolTitle)
			{
				continue;
			}
			
			//����ƥ��ʮ�� 10�� ���Ƶ����ӣ����������,
			$strNumPrefix = "��|һ|��|��|��|��|��|��|��|��|ʮ|��|ǧ|��|��|��|00|01|02|03|04|05|06|07|08|09|1|2|3|4|5|6|7|8|9|��|-|~";
                        $strFlagPrefix = "��|��|��|��|��|��|��";
                        $strPreg = '/(?:'.$strNumPrefix.')[\s]*(?:'.$strFlagPrefix.')/i';
                        $pos = preg_match($strPreg, $title,$out);
                        if ($pos > 0 && $num < 2)
                        {
				++$num;
				//���Ӻ�׺������$num����
				$title .= self::$_arrStoryExtra[$num-1];
				$arrGoodThreadItem["title"] =  $title;
				$arrGoodThreadItem['push_type'] = 'good';
				$arrGoodThreadItem['taglist'][] = '��';
				$arrData[] = $arrGoodThreadItem;
			}
			else
			{
				// �ж��Ƿ��йؼ���
				$bolIsHasKey = self::_isHasGameKey($arrGoodThreadItem);
				// �ж����ظ������Ƿ�С��7��
				$day7 = 604800;
				$bolIsLessSeven = Service_Alading_Strategy_ThreadStrategy::isNearDays($arrGoodThreadItem, $day7);
				if (true === $bolIsHasKey && true === $bolIsLessSeven)
				{
					$arrGoodThreadItem['push_type'] = 'good';
					$arrGoodThreadItem['taglist'][] = '��';
					$arrThreeData[] = $arrGoodThreadItem;
					++$threeNum;
				}
									
				// ������ȡһ��
				if (($threeNum >= 3) && ($num >= 2))
				{
					break;
				}
			}
		}
		$intRemainNum = $intMaxThreadNum - $num;
		//echo "data \n";
                //var_dump($arrData);
                //echo "three data \n";
                //var_dump($arrThreeData);	
		if ($intRemainNum > 0)
		{
			foreach($arrThreeData as $key => $arrThread)
			{
				$arrThread['push_type'] = 'good';
				$arrData[] = $arrThread;
					
				if ($key+1 === $intRemainNum)
				{
					break;
				}
			}
		}
		
		return $arrData;
	}
	
	/**
	 * ѡ����չ�½�����
	 * @param unknown_type $arrForumInfo
	 * @param unknown_type $arrGoodThreadList
	 * @param unknown_type $intMaxThreadNum
	 * @return multitype:
	 */
	private static function _selectChapter($arrForumInfo, $arrGoodThreadList, $intMaxThreadNum = 3)
	{
		$arrData = array();
		$arrThreeData = array();
		
		if (!is_array($arrForumInfo) || empty($arrForumInfo)
				|| !is_array($arrGoodThreadList) || empty($arrGoodThreadList))
		{
			Bingo_Log::warning("input param error");
			return $arrData;
		}
		
		$dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';			
		$num=0;        // ǰ����������
		
		foreach ($arrGoodThreadList as $arrGoodThreadItem)
		{
			$title = isset($arrGoodThreadItem["title"])?$arrGoodThreadItem["title"]:'';
			//�����Ƽ����������治�ܰ����ɹ�Ͱ�������δ�
			if(true === Service_Alading_Strategy_ThreadStrategy::hasFilterWord($title,$dir1))
			{
				continue;
			}
			
			$title = Libs_Util_String::filterNonGBKChar($title);
			$bolTitle = strlen($title) > 6 ? true : false;
				
			if (false === $bolTitle)
			{
				continue;
			}
			
			//����ƥ��ʮ�� 10�� ���Ƶ����ӣ����������,
			$strNumPrefix = "��|һ|��|��|��|��|��|��|��|��|ʮ|��|ǧ|��|��|00|01|02|03|04|05|06|07|08|09|0|1|2|3|4|5|6|7|8|9|��|-";
			$strFlagPrefix = "��|��|��|��|��|��";
			$strPreg = '/��[\s]*(?:'.$strNumPrefix.')*[\s]*(?:'.$strFlagPrefix.')/i';
			$pos = preg_match_all($strPreg, $title,$out);
			if ($pos > 0)
			{
			    $intOutLen = count($out[0]);
				++$num;
				if ($num <= 2)
				{
					continue;
				}
				elseif($num <= 2 + $intMaxThreadNum && $num > 2)
				{
					//���Ӻ�׺������$num����
					$title = isset($out[0][$intOutLen-1])?strval($out[0][$intOutLen-1]):'';
					if ($title == '')
					{
						continue;
					}
					$arrGoodThreadItem["title"] =  $title;
					$arrGoodThreadItem['push_type'] = 'chapter';
					$arrData[] = $arrGoodThreadItem;
				}				
			}		
		}
		
		return $arrData;
	}
	
	
	/**
	 * ��ȡ��ѧ��ͨ��
	 * @param unknown_type $arrForumInfo
	 * @return multitype:unknown
	 */
	private static function _selectCommonThread($arrForumInfo, $arrCommonThreads, $intMaxThreadNum = 3)
	{
		$arrData = array();       // �ظ���>9  ���ⳬ��6���ֽڣ�����һ�ؼ��ʿ��йؼ��ʵ�����
		$arrSecondData = array(); // �ظ���>9  ���ⳬ��6���ֽڵ�������
		$arrThreeData = array();  // �������
			
		// ����������ݸ�ʽ ��ֱ�ӷ��ؿ�
		if (!is_array($arrCommonThreads))
		{
			return array();
		}
			
		//��ʼɸѡ����,�ظ���>9  ���ⳬ��6���ֽڣ�����һ�ؼ��ʿ��йؼ��ʵ���������
		$num=0;            // �ظ���>9  ���ⳬ��6���ֽڣ�����һ�ؼ��ʿ��йؼ��ʵ�������
		$secondNum = 0;    // �ظ���>9  ���ⳬ��6���ֽڵ�������
		$threeNum = 0;     // �������		
		$dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';
		
		foreach ($arrCommonThreads as $arrCommonThreadItem)
		{
			$title = isset($arrCommonThreadItem["title"])?$arrCommonThreadItem["title"]:'';
			$intPostNum = isset($arrCommonThreadItem['post_num'])?intval($arrCommonThreadItem['post_num']):0;
			//�����Ƽ����������治�ܰ����ɹ�Ͱ�������δ�
			if(true === Service_Alading_Strategy_ThreadStrategy::hasFilterWord($title,$dir1))
			{
				continue;
			}
			
			$bolTitle = strlen($title) > 6 ? true : false;
				
			if (false === $bolTitle)
			{
				continue;
			}		

			$bolPostNum = $intPostNum > 9 ? true : false;
			$bolHasKey = self::_isHasGameKey($arrCommonThreadItem);
			
		    if(true === $bolTitle && true === $bolPostNum && true === $bolHasKey)
		    {
		    	$arrCommonThreadItem['push_type'] = 'common';
		    	$arrData[] = $arrCommonThreadItem;
		    	$num++;
		    }
		    elseif(true === $bolTitle && true === $bolPostNum)
		    {
		    	$arrCommonThreadItem['push_type'] = 'common';
		    	$arrSecondData[] = $arrCommonThreadItem;
		    	$secondNum++;
		    }
		    else 
		    {
		    	$arrCommonThreadItem['push_type'] = 'common';
		    	$arrThreeData[] = $arrCommonThreadItem;
		    	$threeNum++;
		    }
		    
		    if ($num >= $intMaxThreadNum)
		    {
		    	break;
		    }   
		}
			
		$intRemainNum = $intMaxThreadNum - $num;
		
		if ($intRemainNum > 0)
		{
			foreach($arrSecondData as $key => $arrThread)
			{
				$arrThread['push_type'] = 'common';
				$arrData[] = $arrThread;
		
				if ($key+1 === $intRemainNum)
				{
					break;
				}
			}
			
			$intRemainNum = $intMaxThreadNum - count($arrData);
			
			if ($intRemainNum > 0)
			{
				foreach($arrThreeData as $key => $arrThread)
				{
					$arrThread['push_type'] = 'common';
					$arrData[] = $arrThread;
						
					if ($key+1 === $intRemainNum)
					{
						break;
					}
				}
			}
		}
		 
		return $arrData;
	}
	
	
	/**
	 * ���ӱ������Ƿ�����ѧ�ؼ���
	 * @param unknown_type $arrThread
	 * @return boolean
	 */
	private static function _isHasGameKey($arrThread)
	{
		$title = isset($arrThread["title"])?$arrThread["title"]:'';
		$strPreg = "/ԭ��|��������|����|����|�ں�|�²�|Ԥ��|���|��β|����/i";
		$ret = preg_match($strPreg, $title);
		if ($ret > 0)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
}
