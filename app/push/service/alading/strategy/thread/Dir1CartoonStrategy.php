<?php
/**
 * ����Ŀ¼��
1��	ȡ��Ʒ�������к��л����������ڣ�����ǰ������Ϊ��Сд���ֵ����ӣ����������Գ�������չ���ϣ������ͱ��������ɡ����������ۡ������������ȡһ������
2��	ȡ��Ʒ�����ӱ�������ؼ��ʿ⣨���£���һ�ؼ��ʵ����ӣ������ظ�ʱ�䲻����7��
3��	��Ʒ�����ظ�ʱ��һ�����ڣ�
4��	ȡsep�ھ��������
5��	��ͨ�����ԣ��ظ���>9 �� ���ⳬ��6���ֽڣ�����һ�ؼ��ʿ��йؼ��ʵ��������ȣ�
6��	��ͨ�������
�ؼ��ʣ�Ԥ�桢�̵㡢���������ۡ�Ԥ�⡢�·���ֱ����ԭ������ֽ����ͼ����ͼ��cos����͸���ֻ桢cp��ͬ�ˡ���������ӳ���糡�桢����

 * <AUTHOR>
 *
 */
class Service_Alading_Strategy_Thread_Dir1CartoonStrategy
{
	//����������������Ӻ�׺
	private static $_arrStoryExtra = array("���������ۡ�",);	 
	private static $_arrLimitWordA = array("һ","��","��","��","��","��","��","��","��","ʮ","��","00","01","02","03","04","05","06","07","08","09");
	private static $_arrLimitWordB = array('1','2','3','4','5','6','7','8','9');
	
	public static function execute($arrInput)
	{
		$arrOutput = array();
		if (!is_array($arrInput) || empty($arrInput))
		{
			Bingo_Log::warning("input param error, the param is ".serialize($arrInput));
			return $arrOutput;
		}

		$arrForumInfoSrc      = isset($arrInput['forum_info'])?$arrInput['forum_info']:array();
		$arrGoodThreadListSrc = isset($arrInput['good_thread_list'])?$arrInput['good_thread_list']:array();
		$arrThreadListSrc     = isset($arrInput['common_thread_list'])?$arrInput['common_thread_list']:array();
		$arrSepListSrc        = isset($arrInput['sep_list'])?$arrInput['sep_list']:array();
		$arrSuperListSrc      = isset($arrInput['super_list'])?$arrInput['super_list']:array();

		$arrSuperListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrSuperListSrc, 'super');
		$arrSepListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrSepListSrc, 'sep');

		if (empty($arrForumInfoSrc))
		{
			Bingo_Log::warning("input param error, the param is ".serialize($arrInput));
			return $arrOutput;
		}
		
		$arrHotListSrc        = isset($arrInput['hot_thread_list'])?$arrInput['hot_thread_list']:array();
		//$arrHotListSrc = array();
                $arrHotListObj = array();
		$intHotNum = 0;

		if (!empty($arrHotListSrc))
		{
			$fltFilterNum = 0.1;
			$intLimit = 3;
			$strForumName = isset($arrForumInfoSrc['forum_name']) ? strval($arrForumInfoSrc['forum_name']) : '';
			$arrHotListObj = Service_Alading_Strategy_ThreadStrategy::filterThreadByClickSim($arrHotListSrc, 
					$strForumName, $fltFilterNum, $intLimit);
                        $arrHotListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrHotListObj, 'hot');
                        $arrHotListObj = array_slice($arrHotListObj, 0, 2);
			$intHotNum = count($arrHotListObj);
		}
		

		$arrGoodThreadListObj = array();
		$arrCommonThreadListObj = array();	
		
		$intGoodNum = 0;
		$intSepNum = count($arrSepListObj);
		$intSuperNum = count($arrSuperListObj);
		$arrTopThreadListObj = self::_selectTopThreadList($arrForumInfoSrc, $arrThreadListSrc);		
		
		if ($intSuperNum + $intHotNum <= Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM)
		{
			$intRemainder = Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM;
			$arrGoodThreadListObj = self::_selectGoodThread($arrForumInfoSrc, $arrGoodThreadListSrc, $intRemainder);
			$intFirstGoodNum = isset($arrGoodThreadListObj['first_list']) ? count($arrGoodThreadListObj['first_list']) : 0;
			$intSecondGoodNum = isset($arrGoodThreadListObj['second_list']) ? count($arrGoodThreadListObj['second_list']) : 0;
			$intThreeGoodNum = isset($arrGoodThreadListObj['three_list']) ? count($arrGoodThreadListObj['three_list']) : 0;
			$intGoodNum = $intFirstGoodNum + $intSecondGoodNum + $intThreeGoodNum;
		}
		
		if ($intSuperNum + $intHotNum + $intGoodNum <= Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM)
		{
			$intRemainder = Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM;
			$arrCommonThreadListObj = self::_selectCommonThread($arrForumInfoSrc, $arrThreadListSrc, $intRemainder);
		}	
				
		// ע��һ��Ҫ�����˳����뵽$arrThreadInfos���ϲ���ʱ��ᰴ�����˳��ϲ�
		if ($intSuperNum > 0)
                {
                    $arrThreadInfos[] = $arrSuperListObj;
                }

		if (count($arrTopThreadListObj) > 0)
		{
			$arrThreadInfos[] = $arrTopThreadListObj;
		}
		
		if (isset($arrGoodThreadListObj['first_list']))
		{
			$arrThreadInfos[] = $arrGoodThreadListObj['first_list'];
		}
                //var_dump($arrThreadInfos);
		if (count($arrThreadInfos) > 0)
                {
                     $arrThreadInfosNew = Service_Alading_Strategy_ThreadStrategy::multiUniMerageThread($arrThreadInfos, 2);
                     $arrThreadInfos = array();
                     $arrThreadInfos[] = $arrThreadInfosNew;
                }
	        //var_dump($arrThreadInfos);exit; 
		$arrThreadInfos[] = $arrHotListObj;
		
		if (isset($arrGoodThreadListObj['second_list']))
		{
			$arrThreadInfos[] = $arrGoodThreadListObj['second_list'];
		}
		
		if (isset($arrGoodThreadListObj['three_list']))
		{
			$arrThreadInfos[] = $arrGoodThreadListObj['three_list'];
		}
		
		if (isset($arrCommonThreadListObj['first_list']))
		{
			$arrThreadInfos[] = $arrCommonThreadListObj['first_list'];
		}
		
		if (isset($arrCommonThreadListObj['second_list']))
		{
			$arrThreadInfos[] = $arrCommonThreadListObj['second_list'];
		}
                //var_dump($arrThreadInfos);
		$arrThreadOutput = Service_Alading_Strategy_ThreadStrategy::multiUniMerageThread($arrThreadInfos, Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM);
		$arrOutput['thread_list'] = $arrThreadOutput;
		return $arrOutput;
	}
	
	/**
	 * ��ȡ��Ʒ��
	 * @param unknown_type $arrForumInfo
	 * @param unknown_type $arrGoodThreadList
	 * @param unknown_type $intMaxThreadNum
	 * @return multitype:|string
	 */
	private static function _selectGoodThread($arrForumInfo, $arrGoodThreadList, $intMaxThreadNum = 3)
	{
		$arrOutput = array();
	
		if (!is_array($arrForumInfo) || empty($arrForumInfo)
				|| !is_array($arrGoodThreadList) || empty($arrGoodThreadList))
		{
			Bingo_Log::warning("input param error");
			return $arrOutput;
		}
	
		$dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';
			
		$firstNum=0;        // ��һ��������
		$secondNum = 0;// �ڶ���������
		$threeNum = 0; // ������������
		$simThreadNum = 20; // ���ƶ��������������
		
		$arrFirstData = array();
		$arrSecondData = array();
		$arrThreeData = array();
		
		$arrOutput = array();
		
		foreach ($arrGoodThreadList as $arrGoodThreadItem)
		{
			$title = isset($arrGoodThreadItem["title"])?$arrGoodThreadItem["title"]:'';
			//�����Ƽ����������治�ܰ����ɹ�Ͱ�������δ�
			if(true === Service_Alading_Strategy_ThreadStrategy::hasFilterWord($title,$dir1))
			{
				continue;
			}
			
			$title = Libs_Util_String::filterNonGBKChar($title);
			$bolTitle = strlen($title) > 10 ? true : false;
				
			if (false === $bolTitle)
			{
				continue;
			}
			
			// �ж��Ƿ��йؼ���
			$bolIsHasKey = self::_isHasGameKey($arrGoodThreadItem);
			// �ж����ظ������Ƿ�С��7��
			$day7 = 604800;
			$bolIsLessSeven = Service_Alading_Strategy_ThreadStrategy::isNearDays($arrGoodThreadItem, $day7);
			
			//����ƥ��ʮ�� 10�� ���Ƶ����ӣ������һ��,
            $strNumPrefix = "��|һ|��|��|��|��|��|��|��|��|ʮ|��|ǧ|��|��|��|00|01|02|03|04|05|06|07|08|09|1|2|3|4|5|6|7|8|9|0|-|~|��";
            $strFlagPrefix = "��|��|��|��|��|��|��|��";
            $strPreg = "/(?:".$strNumPrefix.')[\s]*(?:'.$strFlagPrefix.")/i";
            $pos = preg_match($strPreg, $title,$out);
            $arrGoodThreadItem['push_type'] = 'good';
            $arrGoodThreadItem['taglist'][] = '��';
            
            if ($pos > 0 && $firstNum < 2)
            {
				++$firstNum;
				//���Ӻ�׺������$num����
				$title .= self::$_arrStoryExtra[$firstNum-1];
				$arrGoodThreadItem["title"] =  $title;
				
				$arrFirstData[] = $arrGoodThreadItem;
			}
			elseif(true === $bolIsHasKey && true === $bolIsLessSeven)
			{				
				$arrSecondData[] = $arrGoodThreadItem;				
			}
			elseif (true === $bolIsLessSeven)
			{				
				$arrThreeData[] = $arrGoodThreadItem;				
			}
		}
		
		$arrSecondData = Service_Alading_Strategy_ThreadStrategy::arsortByField($arrSecondData, 'post_num', 'int');
		$arrThreeData = Service_Alading_Strategy_ThreadStrategy::arsortByField($arrThreeData, 'post_num', 'int');
                $arrThreeData = array_slice($arrThreeData, 0, 10);
                $arrThreeDataSim = array();
		
		if (!empty($arrThreeData))
		{
			$fltFilterNum = 0.1;
			$intLimit = 6;
			$strForumName = isset($arrForumInfo['forum_name']) ? strval($arrForumInfo['forum_name']) : '';
			$arrThreeDataSim = Service_Alading_Strategy_ThreadStrategy::filterThreadByClickSim($arrThreeData,
					$strForumName, $fltFilterNum, $intLimit);			
		}
		
		$arrOutput['first_list'] = $arrFirstData;
		$arrOutput['second_list'] = $arrSecondData;
		if (!empty($arrThreeDataSim))
		{
			$arrOutput['three_list'] = $arrThreeDataSim;
		}
		else 
		{
			$arrOutput['three_list'] = $arrThreeData;
		}
		
		return $arrOutput;
	}
	
	
	/**
	 * ��ȡ��ͨ��
	 * @param unknown_type $arrForumInfo
	 * @return multitype:unknown
	 */
	private static function _selectCommonThread($arrForumInfo, $arrCommonThreads, $intMaxThreadNum = 3)
	{
		$arrData = array();       // �ظ���>9  ���ⳬ��6���ֽڣ�����һ�ؼ��ʿ��йؼ��ʵ�����
		$arrSecondData = array(); // �ظ���>9  ���ⳬ��6���ֽڵ�������
		$arrThreeData = array();  // �������
		$arrCommonTmp = array();  // ��ͨ������ʱ�洢
			
		// ����������ݸ�ʽ ��ֱ�ӷ��ؿ�
		if (!is_array($arrCommonThreads))
		{
			return array();
		}
			
		//��ʼɸѡ����,�ظ���>9  ���ⳬ��6���ֽڣ�����һ�ؼ��ʿ��йؼ��ʵ���������
		$num=0;            // �ظ���>9  ���ⳬ��6���ֽڣ�����һ�ؼ��ʿ��йؼ��ʵ�������
		$secondNum = 0;    // �ظ���>9  ���ⳬ��6���ֽڵ�������
		$threeNum = 0;     // �������		
		$dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';
		$strForumName = isset($arrForumInfo['forum_name']) ? strval($arrForumInfo['forum_name']) : '';
		
		foreach ($arrCommonThreads as $arrCommonThreadItem)
		{
			$title = isset($arrCommonThreadItem["title"])?$arrCommonThreadItem["title"]:'';
			$intPostNum = isset($arrCommonThreadItem['post_num'])?intval($arrCommonThreadItem['post_num']):0;
			//�����Ƽ����������治�ܰ����ɹ�Ͱ�������δ�
			if(true === Service_Alading_Strategy_ThreadStrategy::hasFilterWord($title,$dir1))
			{
				continue;
			}
			
			$title = Libs_Util_String::filterNonGBKChar($title);
			$bolTitle = strlen($title) > 10 ? true : false;
				
			if (false === $bolTitle)
			{
				continue;
			}				
			
			$arrCommonThreadItem['push_type'] = 'common';
			$arrCommonTmp[] = $arrCommonThreadItem;
		}

		$arrCommonSortRet = Service_Alading_Strategy_ThreadStrategy::arsortByField($arrCommonTmp, 'post_num', 'int');
		$arrCommonSortRet = array_slice($arrCommonSortRet, 0, 10);
		$arrCommonSim = array();
		
		if (!empty($arrCommonSortRet))
		{
			$fltFilterNum = 0.1;
			$intLimit = 6;
			$arrCommonSim = Service_Alading_Strategy_ThreadStrategy::filterThreadByClickSim($arrCommonSortRet,
					$strForumName, $fltFilterNum, $intLimit);
		}
		
		if (count($arrCommonSim) >= $intMaxThreadNum)
		{
			$arrData['first_list'] = $arrCommonSim;
			return $arrData;
		}
		
		$arrData['first_list'] = $arrCommonSim;
		$arrData['second_list'] = $arrCommonSortRet;		
		return $arrData;
	}
	
	
	/**
	 * ���ӱ������Ƿ�����ѧ�ؼ���
	 * @param unknown_type $arrThread
	 * @return boolean
	 */
	private static function _isHasGameKey($arrThread)
	{
		$title = isset($arrThread["title"])?$arrThread["title"]:'';
		$strPreg = "/Ԥ��|�̵�|����|����|���|����|Ԥ��|�·�|ֱ��|ԭ��|��ֽ|��ͼ|��ͼ|cos|��͸|����|�缯|�ֻ�|cp|NTR|ͬ��|����|����|����|����|��Ƭ|��Դ|����|ȫ��|��ȫ|����|��Ƶ|��ӳ|�糡��|����/i";
		$ret = preg_match($strPreg, $title);
		if ($ret > 0)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
	
	/**
	 * get top thread list
	 * @param $arrForumInfo array forum info
	 * @param $arrThreadList array thread list
	 * @param $intMaxThreadNum int return number
	 * @return array
	 */
	private static function _selectTopThreadList($arrForumInfo, $arrThreadList, $intMaxThreadNum = 3)
	{
		$arrOutput = array();
		if (!is_array($arrForumInfo) || empty($arrForumInfo)
				|| !is_array($arrThreadList) || empty($arrThreadList))
		{
			Bingo_Log::warning("input param error");
			return $arrOutput;
		}
		
		$dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';		
		$arrThreadList = array_slice($arrThreadList, 0, 10);
		
		foreach ($arrThreadList as $arrThreadItem)
		{
			$intIsTop =  isset($arrThreadItem['top_types']) ? intval($arrThreadItem['top_types']) : 0;
			
			// �������
			if (1 !== $intIsTop)
			{
				continue;
			}
			
			$title = isset($arrThreadItem["title"]) ? $arrThreadItem["title"]:'';
			//�����Ƽ����������治�ܰ����ɹ�Ͱ�������δ�
			if(true === Service_Alading_Strategy_ThreadStrategy::hasFilterWord($title,$dir1))
			{
				continue;
			}
				
			$title = Libs_Util_String::filterNonGBKChar($title);
			$bolTitle = strlen($title) > 10 ? true : false;
		
			if (false === $bolTitle)
			{
				continue;
			}
				
			// �ж����ظ������Ƿ�С��7��
			$day7 = 604800;
			$bolIsLessSeven = Service_Alading_Strategy_ThreadStrategy::isNearDays($arrThreadItem, $day7);
				
			//����ƥ��ʮ�� 10�� ���Ƶ����ӣ������һ��,
			$strNumPrefix = "��|һ|��|��|��|��|��|��|��|��|ʮ|��|ǧ|��|��|��|00|01|02|03|04|05|06|07|08|09|1|2|3|4|5|6|7|8|9|0|-|~|��";
			$strFlagPrefix = "��|��|��|��|��|��|��|��";
			$strPreg = "/(?:".$strNumPrefix.')[\s]*(?:'.$strFlagPrefix.")/i";
			$pos = preg_match($strPreg, $title,$out);
			//$arrThreadItem['push_type'] = 'top';
			//$arrThreadItem['taglist'][] = '��';
			$thread_types = isset($arrThreadItem['thread_types']) ? intval($arrThreadItem['thread_types']) : 0;
			$arrThreadType = Tieba_Type_Thread::getTypeArray($thread_types);
			
			if ($pos > 0)
			{				
				$arrOutput[] = $arrThreadItem;
			}
			
			
		}
                $arrOutput = Service_Alading_Strategy_ThreadStrategy::setPushType($arrOutput, 'top');			
                return $arrOutput;
	}
    
}
