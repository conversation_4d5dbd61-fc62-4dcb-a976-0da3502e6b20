<?php
/**
 * ����Ŀ¼����
 * 1��	��Ʒ���ڰ����ؼ��ʿ���һ�ؼ��ʣ��¸��������ӣ������ظ�ʱ����һ�����ڣ�
2��	��ͨ���ڰ����ؼ��ʿ���һ�ؼ��ʣ��¸��������ӣ��ظ���>9  ���ⳬ��6���ֽڣ��ظ�ʱ�����µ����ӣ������ظ�ʱ����һ�����ڣ�
3��	��Ʒ�����ظ�ʱ��һ�����ڣ�
4��	Sep�ھ����������
5��	��ͨ�����ԣ��ظ���>9  ���ⳬ��6���ֽڣ�
6��	��ͨ�������
�ؼ��ʣ�Ԥ�桢�г̡����š����¡������ᡢ���񡢻��������塢̽�ࡢ���ġ�ֱ�����ݳ��ᡢ��ͼ����͸�����ա�ר�á����ҡ�����ᡢ��Ϸ����Ϸ�����
չ���ϣ����������ǰɵ�ͳһ�İ���ǰ׺��ȥ������������ͼƬ����5���������ͱ��������ɡ�����ͼ����������

 * <AUTHOR>
 *
 */
class Service_Alading_Strategy_Thread_Dir1StarStrategy
{
    public static function execute($arrInput)
	{
		$arrOutput = array();
		if (!is_array($arrInput) || empty($arrInput))
		{
			Bingo_Log::warning("input param error, the param is ".serialize($arrInput));
			return $arrOutput;
		}

		$arrForumInfoSrc      = isset($arrInput['forum_info'])?$arrInput['forum_info']:array();
		$arrGoodThreadListSrc = isset($arrInput['good_thread_list'])?$arrInput['good_thread_list']:array();
		$arrThreadListSrc     = isset($arrInput['common_thread_list'])?$arrInput['common_thread_list']:array();
		$arrSepListSrc        = isset($arrInput['sep_list'])?$arrInput['sep_list']:array();
		$arrSuperListSrc      = isset($arrInput['super_list'])?$arrInput['super_list']:array();

		$arrSuperListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrSuperListSrc, 'super');
		$arrSepListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrSepListSrc, 'sep');

		if (empty($arrForumInfoSrc))
		{
			Bingo_Log::warning("input param error, the param is ".serialize($arrInput));
			return $arrOutput;
		}

		$arrGoodThreadListObj = array();
		$arrCommonThreadListObj = array();	
		
		$intFirstGoodNum = 0;
		$intSepNum = count($arrSepListObj);
		$intSuperNum = count($arrSuperListObj);
				
		if ($intSuperNum <= Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM)
		{
			$intRemainder = Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM;
			$arrGoodThreadListObj = self::_selectGoodThread($arrForumInfoSrc, $arrGoodThreadListSrc, $intRemainder);
			$intFirstGoodNum = isset($arrGoodThreadListObj['first_list'])?count($arrGoodThreadListObj['first_list']):0;
		}
		
		if ($intFirstGoodNum <= Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM)
		{
			$intRemainder = Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM;
			$arrCommonThreadListObj = self::_selectCommonThread($arrForumInfoSrc, $arrThreadListSrc, $intRemainder);
		}

		$arrFirstGoodList = isset($arrGoodThreadListObj['first_list'])?$arrGoodThreadListObj['first_list']:array();
		$arrSecondGoodList = isset($arrGoodThreadListObj['second_list'])?$arrGoodThreadListObj['second_list']:array();
		$arrFirstCommonList = isset($arrCommonThreadListObj['first_list'])?$arrCommonThreadListObj['first_list']:array();
		$arrSecondCommonList = isset($arrCommonThreadListObj['second_list'])?$arrCommonThreadListObj['second_list']:array();
		$arrThreeCommonList = isset($arrCommonThreadListObj['three_list'])?$arrCommonThreadListObj['three_list']:array();		
		
		// ע��һ��Ҫ�����˳����뵽$arrThreadInfos���ϲ���ʱ��ᰴ�����˳��ϲ�
		$arrThreadInfos[] = $arrSuperListObj;
		$arrThreadInfos[] = $arrFirstGoodList;
		$arrThreadInfos[] = $arrFirstCommonList;
		$arrThreadInfos[] = $arrSecondGoodList;
		$arrThreadInfos[] = $arrSepListObj;
		$arrThreadInfos[] = $arrSecondCommonList;
		$arrThreadInfos[] = $arrThreeCommonList;
		$arrThreadOutput = Service_Alading_Strategy_ThreadStrategy::multiUniMerageThread($arrThreadInfos, Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM);		
		$arrThreadOutput = self::_changeTitle($arrThreadOutput);		
		$arrOutput['thread_list'] = $arrThreadOutput;
		return $arrOutput;
	}
	
	/**
	 * ��ȡ��Ʒ��
	 * @param unknown_type $arrForumInfo
	 * @param unknown_type $arrGoodThreadList
	 * @param unknown_type $intMaxThreadNum
	 * @return multitype:|string
	 */
	private static function _selectGoodThread($arrForumInfo, $arrGoodThreadList, $intMaxThreadNum = 3)
	{
		$arrData = array();
		$arrDataTemp = array();
		$arrOutput = array();
	
		if (!is_array($arrForumInfo) || empty($arrForumInfo)
				|| !is_array($arrGoodThreadList) || empty($arrGoodThreadList))
		{
			Bingo_Log::warning("input param error");
			return $arrData;
		}
	
		$dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';
			
		$num=0;        // ȡ��Ʒ�����ӱ�������ؼ��ʿ���һ�ؼ��ʵ����ӣ������ظ�ʱ�䲻����7��
		$secondNum = 0; // �ڶ���������
		foreach ($arrGoodThreadList as $arrGoodThreadItem)
		{
			$title = isset($arrGoodThreadItem["title"])?$arrGoodThreadItem["title"]:'';
			//�����Ƽ����������治�ܰ����ɹ�Ͱ�������δ�
			if(true === Service_Alading_Strategy_ThreadStrategy::hasFilterWord($title,$dir1))
			{
				continue;
			}
				
			// �ж��Ƿ��йؼ���
			$bolIsHasKey = self::_isHasGameKey($arrGoodThreadItem);
			// �ж����ظ������Ƿ�С��7��
			$day7 = 604800;
			$bolIsLessSeven = Service_Alading_Strategy_ThreadStrategy::isNearDays($arrGoodThreadItem, $day7);
			if (true === $bolIsHasKey && true === $bolIsLessSeven)
			{
				$arrGoodThreadItem['push_type'] = 'good';
				$arrGoodThreadItem['taglist'][] = '��';
				$arrData[] = $arrGoodThreadItem;
				++$num;
			}
			elseif(true === $bolIsLessSeven)
			{	
				$arrGoodThreadItem['push_type']	= 'good';
				$arrGoodThreadItem['taglist'][] = '��';
				$arrDataTemp[] = $arrGoodThreadItem;
				++$secondNum;
			}
				
			// ȡ����
			if ($num >= 3)
			{
				break;
			}			
		}
		
		$arrOutput['first_list'] = $arrData;
		$arrOutput['second_list'] = $arrDataTemp;			
		return $arrOutput;
	}
	
	
	/**
	 * ��ȡ��ѧ��ͨ��
	 * @param unknown_type $arrForumInfo
	 * @return multitype:unknown
	 */
	private static function _selectCommonThread($arrForumInfo, $arrCommonThreads, $intMaxThreadNum = 3)
	{
		$arrData = array();       // �ظ���>9  ���ⳬ��6���ֽڣ�����һ�ؼ��ʿ��йؼ��ʵ�����
		$arrSecondData = array(); // �ظ���>9  ���ⳬ��6���ֽڵ�������
		$arrThreeData = array();  // �������
		$arrOutput = array();
			
		// ����������ݸ�ʽ ��ֱ�ӷ��ؿ�
		if (!is_array($arrCommonThreads))
		{
			return array();
		}
			
		//��ʼɸѡ����,�ظ���>9  ���ⳬ��6���ֽڣ�����һ�ؼ��ʿ��йؼ��ʵ���������
		$num=0;            // �ظ���>9  ���ⳬ��6���ֽڣ�����һ�ؼ��ʿ��йؼ��ʵ�������
		$secondNum = 0;    // �ظ���>9  ���ⳬ��6���ֽڵ�������
		$threeNum = 0;     // �������
		$dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';
	
		foreach ($arrCommonThreads as $arrCommonThreadItem)
		{
			$title = isset($arrCommonThreadItem["title"])?$arrCommonThreadItem["title"]:'';
			$intPostNum = isset($arrCommonThreadItem['post_num'])?intval($arrCommonThreadItem['post_num']):0;
			//�����Ƽ����������治�ܰ����ɹ�Ͱ�������δ�
			if(true === Service_Alading_Strategy_ThreadStrategy::hasFilterWord($title,$dir1))
			{
				continue;
			}
	
			$bolTitle = strlen($title) > 6 ? true : false;
			$bolPostNum = $intPostNum > 9 ? true : false;
			$bolHasKey = self::_isHasGameKey($arrCommonThreadItem);
				
			if(true === $bolTitle && true === $bolPostNum && true === $bolHasKey)
			{
				$arrCommonThreadItem['push_type'] = 'common';
				$arrData[] = $arrCommonThreadItem;
				$num++;
			}
			elseif(true === $bolTitle && true === $bolPostNum)
			{
				$arrCommonThreadItem['push_type'] = 'common';
				$arrSecondData[] = $arrCommonThreadItem;
				$secondNum++;
			}
			else
			{
				$arrCommonThreadItem['push_type'] = 'common';
				$arrThreeData[] = $arrCommonThreadItem;
				$threeNum++;
			}
	
			if ($num >= $intMaxThreadNum)
			{
				break;
			}
		}
			
		$arrOutput['first_list'] = $arrData;
		$arrOutput['second_list'] = $arrSecondData;
		$arrOutput['three_list'] = $arrThreeData;		
		return $arrOutput;
	}
	
	
	/**
	 * ���ӱ������Ƿ��йؼ���
	 * @param unknown_type $arrThread
	 * @return boolean
	 */
	private static function _isHasGameKey($arrThread)
	{
		$title = isset($arrThread["title"])?$arrThread["title"]:'';
		$strPreg = "/Ԥ��|��͸|����|����|����|Ӱ��|ֱ��|���|������|�ݳ���|̽��|ר��|����|����|����|����|����|��Ƶ|��ͼ|��ͼ|ͼƬ|��Ƭ|����|����|����|Ƭ��|ɱ��|���߹ۿ�|����|����|�ϼ�|ȫ��|����|��Դ/i";
		$ret = preg_match($strPreg, $title);
		if ($ret > 0)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
	
	private static function _changeTitle($arrThreadInfo)
	{
                //echo "into change title \n";
		if (empty($arrThreadInfo))
		{
			return false;
		}
		
		$arrData = array();
		
		foreach($arrThreadInfo as $thread)
		{
			$intPicNum = Service_Alading_Strategy_ThreadStrategy::getThreadPhotoNum($thread);
						
			if ($intPicNum >= 5)
			{
				$strTitle = isset($thread['title'])?strval($thread['title']):'';
				$strTitle = $strTitle."����ͼ��";
				$thread['title'] = $strTitle;
			}

            $threadRet = Service_Alading_Strategy_ThreadStrategy::removeTitlePrefix($thread);
			
			if (false === $threadRet)
			{
				$arrData[] = $thread;
			}
			else 
			{
				$arrData[] = $threadRet;
			}		
		}
		
		return $arrData;
	}
		
}
