<?php
/**
 *  ���Ѿ��ֲ�,����,����Ŀ¼����
 *  Sep�ھ����������
 *  ȡ��Ʒ�����ظ�ʱ�䲻����7������ӣ�
	��ͨ�����ԣ��ظ���>9  ���ⳬ��6���ֽڣ�
	��ͨ�������
 * <AUTHOR>
 *
 */
class Service_Alading_Strategy_Thread_Dir1LifeStrategy
{
	public static function execute($arrInput)
	{
		$arrOutput = array();
		if (!is_array($arrInput) || empty($arrInput))
		{
			Bingo_Log::warning("input param error, the param is ".serialize($arrInput));
			return $arrOutput;
		}
                //var_dump($arrInput);exit;                
		$arrForumInfoSrc      = isset($arrInput['forum_info'])?$arrInput['forum_info']:array();
		$arrGoodThreadListSrc = isset($arrInput['good_thread_list'])?$arrInput['good_thread_list']:array();
		$arrThreadListSrc     = isset($arrInput['common_thread_list'])?$arrInput['common_thread_list']:array();
		$arrSepListSrc        = isset($arrInput['sep_list'])?$arrInput['sep_list']:array();
		$arrSuperListSrc      = isset($arrInput['super_list'])?$arrInput['super_list']:array();

	    $arrHotListSrc        = isset($arrInput['hot_thread_list'])?$arrInput['hot_thread_list']:array();
		$arrHotListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrHotListSrc, 'hot');
		
		$arrSuperListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrSuperListSrc, 'super');
		$arrSepListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrSepListSrc, 'sep');

		$arrArchHotListSrc    = isset($arrInput['arch_hot_thread_list'])?$arrInput['arch_hot_thread_list']:array();
		$arrArchHotListSrc   = self::_selectArchHotThread($arrForumInfoSrc, $arrArchHotListSrc, 3);
		$arrArchHotListObj = Service_Alading_Strategy_ThreadStrategy::setPushType($arrArchHotListSrc, 'arch_hot');
		
		
		if (empty($arrForumInfoSrc))
		{
			Bingo_Log::warning("input param error, the param is ".serialize($arrInput));
			return $arrOutput;
		}
	
		$arrTopListObj = self::_selectOffice($arrForumInfoSrc, $arrThreadListSrc);
		$intOfficeTopNum = count($arrTopListObj);
        $intHotNum = count($arrHotListObj);
        $intArchHotNum = count($arrArchHotListSrc);

		if ($intArchHotNum > 0)
		{
			$arrHotListObj = array_slice($arrHotListObj, 0, 2);
		}
		$arrArchHotListObj = array_slice($arrArchHotListObj, 0, 1);
		$arrGoodThreadListObj = array();
		$arrCommonThreadListObj = array();	
		
		$intGoodNum = 0;
		$intSepNum = count($arrSepListObj);
		$intSuperNum = count($arrSuperListObj);
				
		if ($intSuperNum + $intHotNum + $intArchHotNum <= Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM)
		{
			$intRemainder = Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM;
			$arrGoodThreadListObj = self::_selectGoodThread($arrForumInfoSrc, $arrGoodThreadListSrc, $intRemainder);
			$intFirstGoodNum = isset($arrGoodThreadListObj['first_list']) ? count($arrGoodThreadListObj['first_list']) : 0;
			$intSecondGoodNum = isset($arrGoodThreadListObj['second_list']) ? count($arrGoodThreadListObj['second_list']) : 0;
			$intGoodNum = $intFirstGoodNum + $intSecondGoodNum;
		}
		
		if ($intSuperNum + $intHotNum <= Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM)
		{
			$intRemainder = Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM;
			$arrCommonThreadListObj = self::_selectCommonThread($arrForumInfoSrc, $arrThreadListSrc, $intRemainder);
		}
				
		// ע��һ��Ҫ�����˳����뵽$arrThreadInfos���ϲ���ʱ��ᰴ�����˳��ϲ�
		$arrThreadInfos[] = $arrSuperListObj;

		if ($intOfficeTopNum > 0)
		{
			$arrThreadInfos[] = $arrTopListObj;
		}
		
		$arrThreadInfos[] = $arrHotListObj;
		$arrThreadInfos[] = $arrArchHotListObj;
		$arrThreadInfos[] = $arrSepListObj;
		$arrThreadInfos[] = $arrGoodThreadListObj;	
		if (isset($arrGoodThreadListObj['first_list']))
		{
			$arrThreadInfos[] = $arrGoodThreadListObj['first_list'];
		}
		
		if (isset($arrGoodThreadListObj['second_list']))
		{
			$arrThreadInfos[] = $arrGoodThreadListObj['second_list'];
		}
		$arrThreadInfos[] = $arrCommonThreadListObj;		
		$arrThreadOutput = Service_Alading_Strategy_ThreadStrategy::multiUniMerageThread($arrThreadInfos, Service_Alading_Strategy_ForumStgyConfig::PUSH_THREAD_NUM);		
		$arrOutput['thread_list'] = $arrThreadOutput;
                return $arrOutput;
	}
		
	/**
	 * ��ȡ��Ʒ��
	 * @param unknown_type $arrForumInfo
	 * @param unknown_type $arrGoodThreadList
	 * @param unknown_type $intMaxThreadNum
	 * @return multitype:|string
	 */
	private static function _selectGoodThread($arrForumInfo, $arrGoodThreadList, $intMaxThreadNum = 3)
	{
		$arrData = array();	
		$arrDataTmp = array();
	
		if (!is_array($arrForumInfo) || empty($arrForumInfo)
				|| !is_array($arrGoodThreadList) || empty($arrGoodThreadList))
		{
			Bingo_Log::warning("input param error");
			return $arrData;
		}
	
		$dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';			
		$num=0;        // ȡ��Ʒ�����������ظ�ʱ�䲻����7��
		
	
		foreach ($arrGoodThreadList as $arrGoodThreadItem)
		{
			$title = isset($arrGoodThreadItem["title"])?$arrGoodThreadItem["title"]:'';
			//�����Ƽ����������治�ܰ����ɹ�Ͱ�������δ�
			if(true === Service_Alading_Strategy_ThreadStrategy::hasFilterWord($title,$dir1))
			{
				continue;
			}
			
			$title = Libs_Util_String::filterNonGBKChar($title);
			$bolTitle = strlen($title) > 6 ? true : false;
			
			if (false === $bolTitle)
			{
				continue;
			}
			
			// �ж����ظ������Ƿ�С��7��
			$day7 = 604800;
			$bolIsLessSeven = Service_Alading_Strategy_ThreadStrategy::isNearDays($arrGoodThreadItem, $day7);
			if (true === $bolIsLessSeven)
			{
				$arrGoodThreadItem['push_type'] = 'good';
				$arrGoodThreadItem['taglist'][] = '��';
				$arrDataTmp[] = $arrGoodThreadItem;				
			}			
		}
		
		$arrDataTmp = Service_Alading_Strategy_ThreadStrategy::arsortByField($arrDataTmp, 'post_num', 'int');
		$arrDataTmp = array_slice($arrDataTmp, 0, $intMaxThreadNum);
		$arrData['first_list'] = $arrDataTmp;
		return $arrData;
	}
	
	
	/**
	 * ��ȡ��ͨ��
	 * @param unknown_type $arrForumInfo
	 * @return multitype:unknown
	 */
	private static function _selectCommonThread($arrForumInfo, $arrCommonThreads, $intMaxThreadNum = 3)
	{
		$arrData = array();       // �ظ���>9  ���ⳬ��6���ֽڣ�����һ�ؼ��ʿ��йؼ��ʵ�����
		$arrSecondData = array(); // �������
					
		// ����������ݸ�ʽ ��ֱ�ӷ��ؿ�
		if (!is_array($arrCommonThreads))
		{
			return array();
		}
			
		//��ʼɸѡ����,�ظ���>9  ���ⳬ��6���ֽڣ�����һ�ؼ��ʿ��йؼ��ʵ���������
		$num=0;            // �ظ���>9  ���ⳬ��6���ֽڣ�
		$secondNum = 0;    // �������
		
		$dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';
	
		foreach ($arrCommonThreads as $arrCommonThreadItem)
		{
			$title = isset($arrCommonThreadItem["title"])?$arrCommonThreadItem["title"]:'';
			$intPostNum = isset($arrCommonThreadItem['post_num'])?intval($arrCommonThreadItem['post_num']):0;
			//�����Ƽ����������治�ܰ����ɹ�Ͱ�������δ�
			if(true === Service_Alading_Strategy_ThreadStrategy::hasFilterWord($title,$dir1))
			{
				continue;
			}
			
			$title = Libs_Util_String::filterNonGBKChar($title);
			$bolTitle = strlen($title) > 10 ? true : false;
			
			if (false === $bolTitle)
			{
				continue;
			}
	
			$arrCommonThreadItem['push_type'] = 'common';
			$arrData[] = $arrCommonThreadItem;		
		}
			
		$arrData = Service_Alading_Strategy_ThreadStrategy::arsortByField($arrData, 'post_num', 'int');
		$arrData = array_slice($arrData, 0, $intMaxThreadNum);				
		return $arrData;
	}
	
	/**
	 * ��ȡarch hot�����б�
	 * @param unknown_type $arrForumInfo
	 * @param unknown_type $arrThreadList
	 * @param unknown_type $intMaxThreadNum
	 * @return multitype:|string
	 */
	private static function _selectArchHotThread($arrForumInfo, $arrThreadList, $intMaxThreadNum = 3)
	{
		$arrOutput = array();
		$dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';
		$num = 0;
		
		foreach ($arrThreadList as $arrThreadItem)
		{
			$day7 = 86400 * 7;
			$title = isset($arrThreadItem["title"]) ? $arrThreadItem["title"] : '';
			//�����Ƽ����������治�ܰ����ɹ�Ͱ�������δ�
			if(true === Service_Alading_Strategy_ThreadStrategy::hasFilterWord($title, $dir1))
			{
				continue;
			}
			
			$isCreateTiming = Service_Alading_Strategy_ThreadStrategy::isCreateNearDays($arrThreadItem, $day7);
			
			if (false === $isCreateTiming)
			{
				// ��ȥ��7������ʱ��
				//continue;
			}
			
			$arrOutput[] = $arrThreadItem;
			$num++;
			
			if ($num >= $intMaxThreadNum)
			{
				break;
			}
		}
		
		return $arrOutput;
	}
	
	
	/**
	 * ��ȡ��ֱ����Ӫ�ɵ��˹���Ԥ����
	 * @param unknown_type $arrForumInfo
	 * @param unknown_type $arrThreadList
	 * @param unknown_type $intMaxThreadNum
	 * @return multitype:|multitype:unknown
	 */
	private static function _selectOffice($arrForumInfo, $arrThreadList, $intMaxThreadNum = 3)
	{
		$arrData = array();
	
		if (!is_array($arrForumInfo) || empty($arrForumInfo)
				|| !is_array($arrThreadList) || empty($arrThreadList))
		{
			Bingo_Log::warning("input param error");
			return $arrData;
		}
	
		$dir1 = isset($arrForumInfo['dir1'])?$arrForumInfo['dir1']:'';
		$strForumName = isset($arrForumInfo['forum_name']) ? strval($arrForumInfo['forum_name']) : '';
		$intIsOfficeAldThread = isset($arrForumInfo['is_ald_thread']) ? intval($arrForumInfo['is_ald_thread']) : 0;
		//$intIsOfficeAldThread = 1; // test
		if (0 === $intIsOfficeAldThread)
		{
			return $arrData;
		}
	
		foreach ($arrThreadList as $key => $arrThreadItem)
		{
			$title = isset($arrThreadItem["title"])?$arrThreadItem["title"]:'';
			$title = Libs_Util_String::filterNonGBKChar($title);
	
			$intIsDelete = isset($arrThreadItem['is_deleted']) ? intval($arrThreadItem['is_deleted']) : 0;
			$intIsPartialVisible = isset($arrThreadItem['is_partial_visible']) ? intval($arrThreadItem['is_partial_visible']) : 0;
			
			if (1 == $intIsDelete || 1 == $intIsPartialVisible)
			{
				continue;
			}
	
			//�����Ƽ����������治�ܰ����ɹ�Ͱ�������δ�
			if(true === Service_Alading_Strategy_ThreadStrategy::hasFilterWord($title,$dir1))
			{
				continue;
			}
			$bolTitle = strlen($title) > 10 ? true : false;
	
			if (false === $bolTitle)
			{
				continue;
			}
			$intIsTop =  isset($arrThreadItem['top_types']) ? intval($arrThreadItem['top_types']) : 0;
			$intIsGood = isset($arrThreadItem['good_types']) ? intval($arrThreadItem['good_types'])	: 0;
	
			if (1 == $intIsTop)
			{
				$arrData[] = $arrThreadItem;
			}
		}
		$arrData = Service_Alading_Strategy_ThreadStrategy::setPushType($arrData, 'top');
		return $arrData;
	}
}
