drop table if exists spider_post_record_0;
CREATE TABLE `spider_post_record_0` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_1;
CREATE TABLE `spider_post_record_1` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_2;
CREATE TABLE `spider_post_record_2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_3;
CREATE TABLE `spider_post_record_3` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_4;
CREATE TABLE `spider_post_record_4` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_5;
CREATE TABLE `spider_post_record_5` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_6;
CREATE TABLE `spider_post_record_6` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_7;
CREATE TABLE `spider_post_record_7` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_8;
CREATE TABLE `spider_post_record_8` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_9;
CREATE TABLE `spider_post_record_9` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_10;
CREATE TABLE `spider_post_record_10` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_11;
CREATE TABLE `spider_post_record_11` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_12;
CREATE TABLE `spider_post_record_12` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_13;
CREATE TABLE `spider_post_record_13` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_14;
CREATE TABLE `spider_post_record_14` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_15;
CREATE TABLE `spider_post_record_15` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_16;
CREATE TABLE `spider_post_record_16` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_17;
CREATE TABLE `spider_post_record_17` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_18;
CREATE TABLE `spider_post_record_18` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_19;
CREATE TABLE `spider_post_record_19` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_20;
CREATE TABLE `spider_post_record_20` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_21;
CREATE TABLE `spider_post_record_21` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_22;
CREATE TABLE `spider_post_record_22` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_23;
CREATE TABLE `spider_post_record_23` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_24;
CREATE TABLE `spider_post_record_24` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_25;
CREATE TABLE `spider_post_record_25` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_26;
CREATE TABLE `spider_post_record_26` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_27;
CREATE TABLE `spider_post_record_27` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_28;
CREATE TABLE `spider_post_record_28` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_29;
CREATE TABLE `spider_post_record_29` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_30;
CREATE TABLE `spider_post_record_30` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_31;
CREATE TABLE `spider_post_record_31` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_32;
CREATE TABLE `spider_post_record_32` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_33;
CREATE TABLE `spider_post_record_33` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_34;
CREATE TABLE `spider_post_record_34` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_35;
CREATE TABLE `spider_post_record_35` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_36;
CREATE TABLE `spider_post_record_36` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_37;
CREATE TABLE `spider_post_record_37` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_38;
CREATE TABLE `spider_post_record_38` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_39;
CREATE TABLE `spider_post_record_39` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_40;
CREATE TABLE `spider_post_record_40` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_41;
CREATE TABLE `spider_post_record_41` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_42;
CREATE TABLE `spider_post_record_42` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_43;
CREATE TABLE `spider_post_record_43` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_44;
CREATE TABLE `spider_post_record_44` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_45;
CREATE TABLE `spider_post_record_45` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_46;
CREATE TABLE `spider_post_record_46` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_47;
CREATE TABLE `spider_post_record_47` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_48;
CREATE TABLE `spider_post_record_48` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_49;
CREATE TABLE `spider_post_record_49` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_50;
CREATE TABLE `spider_post_record_50` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_51;
CREATE TABLE `spider_post_record_51` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_52;
CREATE TABLE `spider_post_record_52` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_53;
CREATE TABLE `spider_post_record_53` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_54;
CREATE TABLE `spider_post_record_54` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_55;
CREATE TABLE `spider_post_record_55` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_56;
CREATE TABLE `spider_post_record_56` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_57;
CREATE TABLE `spider_post_record_57` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_58;
CREATE TABLE `spider_post_record_58` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_59;
CREATE TABLE `spider_post_record_59` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_60;
CREATE TABLE `spider_post_record_60` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_61;
CREATE TABLE `spider_post_record_61` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_62;
CREATE TABLE `spider_post_record_62` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_63;
CREATE TABLE `spider_post_record_63` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_64;
CREATE TABLE `spider_post_record_64` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_65;
CREATE TABLE `spider_post_record_65` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_66;
CREATE TABLE `spider_post_record_66` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_67;
CREATE TABLE `spider_post_record_67` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_68;
CREATE TABLE `spider_post_record_68` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_69;
CREATE TABLE `spider_post_record_69` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_70;
CREATE TABLE `spider_post_record_70` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_71;
CREATE TABLE `spider_post_record_71` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_72;
CREATE TABLE `spider_post_record_72` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_73;
CREATE TABLE `spider_post_record_73` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_74;
CREATE TABLE `spider_post_record_74` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_75;
CREATE TABLE `spider_post_record_75` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_76;
CREATE TABLE `spider_post_record_76` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_77;
CREATE TABLE `spider_post_record_77` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_78;
CREATE TABLE `spider_post_record_78` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_79;
CREATE TABLE `spider_post_record_79` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_80;
CREATE TABLE `spider_post_record_80` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_81;
CREATE TABLE `spider_post_record_81` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_82;
CREATE TABLE `spider_post_record_82` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_83;
CREATE TABLE `spider_post_record_83` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_84;
CREATE TABLE `spider_post_record_84` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_85;
CREATE TABLE `spider_post_record_85` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_86;
CREATE TABLE `spider_post_record_86` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_87;
CREATE TABLE `spider_post_record_87` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_88;
CREATE TABLE `spider_post_record_88` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_89;
CREATE TABLE `spider_post_record_89` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_90;
CREATE TABLE `spider_post_record_90` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_91;
CREATE TABLE `spider_post_record_91` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_92;
CREATE TABLE `spider_post_record_92` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_93;
CREATE TABLE `spider_post_record_93` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_94;
CREATE TABLE `spider_post_record_94` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_95;
CREATE TABLE `spider_post_record_95` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_96;
CREATE TABLE `spider_post_record_96` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_97;
CREATE TABLE `spider_post_record_97` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_98;
CREATE TABLE `spider_post_record_98` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
drop table if exists spider_post_record_99;
CREATE TABLE `spider_post_record_99` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `thread_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_id',
  `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'forum_id',
  `p_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'post_user_id',
  `t_uid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'thread_user_id',
  `command_no` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'nmq_command_no',
  `last_push_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:add,1:mod,2:del',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'create_time',
  `last_modify_time` int(11) NOT NULL DEFAULT '0' COMMENT 'modify_time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_id` (`post_id`),
  KEY `idx_pid_type` (`post_id`,`last_push_type`),
  KEY `idx_tid_type` (`thread_id`,`last_push_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='spider_post_record';
