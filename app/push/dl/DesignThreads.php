<?php
/***************************************************************************
 * 
 * Copyright (c) 2012 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
/**
 * SEP����������
 * <AUTHOR>
 *
 */
class Dl_DesignThreads
{
	//ral��DB������ΪDB_forum_aldstar
	const SERVICE_NAME = "forum_aldstar";
	
	//���ݿ���
	protected static $_db = null;
	
	private static function __getDB($balance = false)
	{
		if(self:: $_db){
			return self:: $_db ;
		}
		self:: $_db = Tieba_Mysql::getDB(self::SERVICE_NAME);
		if(!self:: $_db){
			Bingo_Log::warning("get db resource fail.".self::SERVICE_NAME." ".$balance);
			return false;
		}
		return self:: $_db;
	}
	
	/**
	 * @brief ��ʼ�����ݿ�
	 * @param [in/out] req  :
	 * @return  void
	 * @retval
	 * @see
	 * @note
	 * <AUTHOR>
	 * @date 2013/09/24 15:36:46
	 **/
	public static function init()
	{
		if(self::$_db || self:: __getDB()){
			return true;
		}else{
			return false;
		}
	}
	
	/**
	 * @brief ��ȡSEP��������
	 * @param [in/out] req  :
	 * @return  void
	 * @retval
	 * @see
	 * @note
	 * <AUTHOR>
	 * @date 2013/09/24 15:36:46
	 **/
		public static function getDesigThread($forum_id) 
        {
                if(!self::init())
                {
                	Bingo_Log::warning("init DB failed");
                    return false;
                }
                $time = time();
                $forum_id = intval($forum_id);
                //�ó�����û�й��ڵĵ��˹����õ����ӣ��˹����õ�������ping_threads��type��ʾΪ1
                $sql = "select thread_id1,thread_id2,thread_id3,thread_info1,thread_info2,thread_info3,type,forum_id,forum_name,expire_time,db_time from design_threads where forum_id='".$forum_id."' limit 1";
                $res = self::__getDB()->query($sql);
                if(false === $res)
                {
                        Bingo_Log::warning(sprintf("getDesigThread failed! sql[%s]",$sql));
                        return false;
                }
                //var_dump($res); 
                if (empty($res))
                {
                    Bingo_Log::warning("get result is null");
                    return false;
                }
                //�ж��Ƿ����
                if(!isset($res[0]['db_time']) ||!isset($res[0]['expire_time']) || (intval($res[0]['db_time'])+intval($res[0]['expire_time']))<time())
                {
                    Bingo_Log::warning("expire time less db time");
                	return false;
                }
                
                $arrDesignThread = array();
                foreach($res as $data)
                {
                        $forum_name = $data['forum_name'];
                        if(!empty($data['thread_info1']) && false!== Libs_Rpc_Frs::getMaskInfo($data['thread_id1']))
                        {
                        	$strThreadInfo = unserialize($data['thread_info1']);
                        	if (false !== $strThreadInfo)
                        	{
                        		$arrDesignThread[] = $strThreadInfo;
                        	}                            
                        }
                        if(!empty($data['thread_info2']) && false!== Libs_Rpc_Frs::getMaskInfo($data['thread_id2']))
                        {
                            $strThreadInfo = unserialize($data['thread_info2']);
                        	if (false !== $strThreadInfo)
                        	{
                        		$arrDesignThread[] = $strThreadInfo;
                        	}
                        }
                        if(!empty($data['thread_info3']) && false!== Libs_Rpc_Frs::getMaskInfo($data['thread_id3']))
                        {
                            $strThreadInfo = unserialize($data['thread_info3']);
                        	if (false !== $strThreadInfo)
                        	{
                        		$arrDesignThread[] = $strThreadInfo;
                        	}
                        }
                }
                return $arrDesignThread;
        }
	
        
     /**
	 * @brief ����sep��������
	 * @param [in/out] req  :
	 * @return  void
	 * @retval
	 * @see
	 * @note
	 * <AUTHOR>
	 * @date 2013/09/24 15:36:46
	 **/
		public static function addDesignThread($arrInput) 
        {
                if(!self::init())
                {
                	Bingo_Log::warning("init DB failed");
                    return false;
                }
                $time = time();
                $forum_id = intval($forum_id);
                $thread_id1 = isset($arrInput['thread_id1'])?intval($arrInput['thread_id1']):0;
				$thread_id2 = isset($arrInput['thread_id2'])?intval($arrInput['thread_id2']):0;
				$thread_id3 = isset($arrInput['thread_id3'])?intval($arrInput['thread_id3']):0;
				$thread_info1 = isset($arrInput['thread_info1'])?strval($arrInput['thread_info1']):'';
				$thread_info2 = isset($arrInput['thread_info2'])?strval($arrInput['thread_info2']):'';
				$thread_info3 = isset($arrInput['thread_info3'])?strval($arrInput['thread_info3']):'';
				$forum_id    =  isset($arrInput['forum_id'])?intval($arrInput['forum_id']):0;
				$forum_name = isset($arrInput['forum_name'])?strval($arrInput['forum_name']):'';
				$expire_time = isset($arrInput['expire_time'])?intval($arrInput['expire_time']):0;
				$dbTime = time();
				
				$strSql = sprintf("replace into design_threads(thread_id1,  
										thread_id2,   
										thread_id3,   
										thread_info1,
										thread_info2,
										thread_info3,
										forum_id,     
										forum_name,   
										expire_time,  
										db_time)
						                values
						                (%d,
						                 %d,
						                 %d,
						                '%s',
						                '%s',
						                '%s',
						                 %d,
						                 '%s',
						                 %d,
						                 %d)",
						                 $thread_id1,
						                 $thread_id2,
						                 $thread_id3,
						                 addslashes($thread_info1),
						                 addslashes($thread_info2),
						                 addslashes($thread_info3),
						                 $forum_id,
						                 $forum_name,
						                 $expire_time,
						                 $dbTime);
                $res = self::__getDB()->query($strSql);
                if(false === $res)
                {
                    Bingo_Log::warning(sprintf("addDesignThread failed! sql[%s]",$sql));
                    return false;
                }
				return true;
        }
	

}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
