<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file doPing.php
 * <AUTHOR>
 * @date 2013/11/02 16:35:26
 * @brief ping����ʵ�ʷ���ping�ű�,��PingControl����
 */

require_once dirname(__file__)."/Init.php";
script::init("doPing");

Bingo_Timer::start("total");

//ping_schedule�е�����id
$id = $argv[1];
//ping ps��url
$strUrl = $argv[2];
//ping ps��token
$token  = $argv[3];

//����url
$strUrl = sprintf(Service_Alading_AladingConfig::PING_URL, $token, $strUrl);
Bingo_Log::warning('ping aladdin url is :'. $strUrl);

Bd_Conf::setIdc("jx");
$conf = array(
		'timeout'=>60000,
		'conn_timeout' =>5000
);	
$obj = orp_FetchUrl::getInstance($conf);

$resCurl = $obj->get($strUrl);
if(!$resCurl)
{
	Bingo_Log::warning(sprintf("orp_FetchUrl connect  failed! db_id[$id] input[$strUrl], http_code:[%d],err[%s]",$obj->http_code(),$obj->errmsg()));
}else{
	$arrRes =  Bingo_String::json2array($resCurl);           
	if ($arrRes['code'] == 0) 
	{    
	    Bingo_Log::trace("ping alading ok! db_id[$id] input[$strUrl], msg: " .serialize($arrRes));
        // ping�ɹ�
        Libs_Util_Statics::pingSuccess(Libs_Util_Statics::$AladingPs);
    }else{
        Bingo_Log::warning("ping alading error! db_id[$id] input[$strUrl], msg: " .serialize($arrRes));
        // ͳ��pingʧ���ʣ�ȥ��ping����Ƶ���Ĵ���
        if (intval($arrRes['code']) !== -3) {
            Libs_Util_Statics::pingFail(Libs_Util_Statics::$AladingPs);
        }
    }
}

Bingo_Timer::end("total");
Bingo_Log::pushNotice('doPingTime', Bingo_Timer::toString());
Bingo_Log::buildNotice("do ping script run successful");

exit(0);


/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
