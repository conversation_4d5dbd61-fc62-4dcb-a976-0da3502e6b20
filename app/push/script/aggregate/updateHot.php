<?php


/**
 * @file updateHot.php
 * <AUTHOR>
 * @date 2017/10/16 16:35:26
 * @brief 负责生成高校中间页热门8条贴子（阿拉丁nlp推荐）
 */


// define ('BASEPATH',dirname(__FILE__));
// define ('CONFPATH',BASEPATH."/conf/");
require_once dirname(__file__)."/../Init.php";
script::init("updateHot");

run();      

/**
 * @param unknown $fileName
 * @return multitype:multitype:string multitype:unknown
 */
function run(){
	
	$limit = 8;
	$arrLog = array();
	$redis = new Bingo_Cache_Redis('push');
	$key = 'aggregate_page_forum_set';
	
	//online redis
	/*$input = array(
	    		array('forum_id' => 8751,'forum_name' => '武汉大学'),
	    		array('forum_id' => 2753,'forum_name' => '清华大学'),
	    		array('forum_id' => 93952,'forum_name' => '安徽理工大学'),
	);
	$value = Bingo_String::array2json($input,'UTF-8');
	$arrInput = array('func' => 'SET', 'param' => array('key' => $key,'value' => $value));
	//$arrInput = array('func' => 'DEL', 'param' => array('key' => $key));
	$res = Tieba_Service::call('push', 'dlQuery', $arrInput, null, null, 'post', 'php', 'utf-8');*/
	
	//获取上传时缓存的高校吧集合
	$res = $redis->GET(array('key' => $key));
	$arrForumInfo = Bingo_String::json2array($res['ret'][$key],'UTF-8');
	if (empty($arrForumInfo)){
		Bingo_Log::warning("高校中间页吧集合为空!");
		exit(1);
	}
	$res = array();
	foreach ($arrForumInfo as $index => $value) {
		//获取每个指定吧的热门贴  3-8条
		$res[$value['forum_name']] = Service_Alading_Strategy_ThreadStrategy::uniCollegeMidStrategyThread($value, $limit,array('time'=>0.2,'nlp'=>0.4,'pv'=>0.4));
		$key = "aggregate_page_".$value['forum_name'];
    	$input = array(
    		"key" => $key,
    	);
    	//获取上一次调用时缓存的吧信息
    	$resForumConf = $redis->GET($input);
    	$arrForumConf = Bingo_String::json2array($resForumConf['ret'][$key],'UTF-8');
    	if (empty($arrForumConf)){
    		Bingo_Log::warning(Tieba_Error::getErrmsg(Tieba_Errcode::ERR_MIS_FORUM_NAME_NOT_EXIST));
    		exit(1);
    	}
    	$arrForumConf['tag']['本吧热门']['thread_list'] = $res[$value['forum_name']];
    	//本次调用更新热门贴
    	Dl_Alading_Alading::set('aggregate_page_'.$value['forum_name'],Bingo_String::array2json($arrForumConf,'UTF-8'));
    	//推送给大搜
    	$url = 'http://tieba.baidu.com/t/f/'.$value['forum_id'];
	    $arrTransPack = array(
	        'command_no' => 9999999,
	        'url' => $url,
	        'push_level' => 4,
	        'op_type' => 'ADD',
	        'from_type' => 99,
	        'thread_id' => 99999,
	    );
	    //上线前再放开注释，防止测试时推给大搜造成死链影响。。。
    	$vippingRet = Service_Alading_Lib_VippingNew::sendVipping($arrTransPack);
		if(false === $vippingRet) 
		{
			$error = Tieba_Errcode::ERR_ALADING_SERVICE;
			Bingo_Log::warning("send vipping_college_mid failed,the thread_id is $intThreadId, the post_id is $intPostId");
		} 
		else 
		{
			Bingo_Log::pushNotice("send_vipping_college_mid_post_id",$intPostId);
			$error = Tieba_Errcode::ERR_SUCCESS;
		}
	}
	
}
exit(0);

?>
