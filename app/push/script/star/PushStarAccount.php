<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file PushStarAccount.php
 * <AUTHOR>
 * @date 2017/12/27 15:46:20
 * @brief 网红类全网帐号
 */
require_once dirname(__file__)."/../excellib/PHPExcel.php";
require_once dirname(__file__)."/Base.php";
class getStarAccount extends Base{

    /**
     * [excute description]
     * @return [type] [description]
     * @param
     */
    public function excute(){
        $fileName = 'webstar.xlsx';//线下测试
        if(self::isOrpBns()){
            $fileName = $this->load("webstar.xlsx");
        }
        $this->parse($fileName);
        $this->save("account.0.xml");        
    }

    /**
     * [parse description]
     * @return [type] [description]
     * @param
     */
    public function parse($fileName){
        $objReader = PHPExcel_IOFactory::createReaderForFile($fileName);
        $objPHPExcel = $objReader->load($fileName);
        $objPHPExcel->setActiveSheetIndex(1);
        $objSheet = $objPHPExcel->getActiveSheet();
        $vaildRowNum = $objSheet->getHighestRow();
        $vaildColNum = $objSheet->getHighestColumn();
        if($vaildRowNum == 1){
            Bingo_Log::warning("got excel is null! filename: account.xlsx");
            return false;
        }
        $arrRet = array();
        for ($i=2; $i <= $vaildRowNum ; $i++) { 
            $tmpUrl = "";
            for ($j='A'; $j <= $vaildColNum; $j++) { 
                $urlIndex = ord($j)+1;
                if($j=='A'){
                    $strStarName = trim((string)$objSheet->getCell("$j$i")->getValue());
                    if(empty($strStarName)){
                        Bingo_Log::warning("bxs-internet-star ==== key is null : row = $i");
                        break;
                    }
                    $res = $this->queryInternetStarWhiteList($strStarName);
                    if(empty($res)){
                        break;
                    }
                    $arrRet['item'][$i-2]['key'] = trim((string)$objSheet->getCell("$j$i")->getValue());
                    $arrRet['item'][$i-2]['display']['url'] = 'http://baidu.com';
                    $arrRet['item'][$i-2]['display']['title'] = '全网账号';
                }else{
                    $cell = trim((string)$objSheet->getCell("$j$i")->getValue());
                    if(!empty($cell)){
                        if($j=='B'){
                            $arrRet['item'][$i-2]['display']['list'][ord($j)-ord('B')]['url'] = urlencode(urldecode($cell."&bxs=bxswh_tbgrid"));
                            $arrRet['item'][$i-2]['display']['list'][ord($j)-ord('B')]['title'] = trim((string)$objSheet->getCell("{$j}1")->getValue());
                            $arrRet['item'][$i-2]['display']['list'][ord($j)-ord('B')]['abstract'] = '持续更新中';
                            $arrName = array();
                            $arrQuery = $this->getUrlQuery($cell);
                            if(isset($arrQuery['id'])){
                                $portrait = $arrQuery['id'];
                            }elseif(isset($arrQuery['un'])){
                                $arrName[] = urldecode($arrQuery['un']);
                                $input = array(
                                    "names" => $arrName,//获取用户id
                                );
                                Bingo_Timer::start("getUidByNames");
                                $res = Tieba_Service::call('user', 'getUidByNames', $input, null, null, 'post', 'php', 'utf-8');
                                if(empty($res) || $res['errno'] != Tieba_Errcode::ERR_SUCCESS){
                                    Bingo_Log::warning("Failed to call service:[user], method:[getUidByNames],  param:[".serialize($arrInput)."]");
                                    Bingo_Log::warning("bxs-internet-star ==== portrait is null : row = $i");
                                    unset($arrRet['item'][$i-2]['display']['list'][ord($j)-ord('B')]);
                                    continue;
                                }
                                Bingo_Timer::end("getUidByNames");
                                $arrUser = $res['data'][$arrName[0]][0];
                                if($arrUser['type'] == 1){
                                    $portrait = Tieba_Ucrypt::encode($arrUser['user_id'],$arrUser['user_nickname']);
                                }else{
                                    $portrait = Tieba_Ucrypt::encode($arrUser['user_id'],$arrUser['user_name']);
                                }
                            }else{
                                Bingo_Log::warning("bxs-internet-star ==== portrait is null : row = $i");
                                unset($arrRet['item'][$i-2]['display']['list'][ord($j)-ord('B')]);
                                continue;
                            }
                            $tmpUrl = "http://tb.himg.baidu.com/sys/portrait/item/".$portrait;
                            $arrRet['item'][$i-2]['display']['list'][ord($j)-ord('B')]['poster'] = $tmpUrl;
                        }elseif($j=='C'){
                            $arrRet['item'][$i-2]['display']['list'][ord($j)-ord('B')]['url'] = urlencode(urldecode($cell."&bxs=bxswh_tbid"));
                            $arrRet['item'][$i-2]['display']['list'][ord($j)-ord('B')]['title'] = trim((string)$objSheet->getCell("{$j}1")->getValue());
                            $arrRet['item'][$i-2]['display']['list'][ord($j)-ord('B')]['abstract'] = '持续更新中';
                            $arrRet['item'][$i-2]['display']['list'][ord($j)-ord('B')]['poster'] = $tmpUrl;
                        }else{
                            
                            //$tmp = explode("<->",$cell);
                            /*var_dump("{$urlIndex}$i");var_dump(chr($urlIndex));var_dump($i);
                            exit;*/
                            $cellUrl = trim((string)$objSheet->getCell(chr($urlIndex)."$i")->getValue());
                            $arrRet['item'][$i-2]['display']['list'][ord($j)-ord('B')]['url'] = urlencode(urldecode($cellUrl));
                            $arrRet['item'][$i-2]['display']['list'][ord($j)-ord('B')]['title'] = $cell;
                            $arrRet['item'][$i-2]['display']['list'][ord($j)-ord('B')]['abstract'] = '持续更新中';
                            $arrRet['item'][$i-2]['display']['list'][ord($j)-ord('B')]['poster'] = $tmpUrl;
                            $j++;
                        }

                    }
                }

            }
            if(empty($arrRet['item'][$i-2]['key'])){
                unset($arrRet['item'][$i-2]);
                Bingo_Log::warning("unset account: row = ".$i);
            }
        }

        $outside_account_count = 0;
        $arrOutside = $this->getOutside();
        foreach ($arrOutside['item'] as $value) {

            $res = $this->queryInternetStarWhiteList($value['key']);
            if(empty($res)){
                continue;
            }  
            $value['display']['url'] = 'http://baidu.com';
            foreach ($value['display']['list'] as $key => $list) {
                if($list['title'] == "百度贴吧" || $list['title'] == "贴吧"){
                    $value['display']['list'][$key]['url'] = urlencode(urldecode( $list['url']."&bxs=bxswh_tbid"));
                }
                if(isset($list['comment'])){
                    unset($value['display']['list'][$key]['comment']);
                } 
            }
            //var_dump($value);
            $arrRet['item'][] = $value;
            $outside_account_count++;
        }
        Bingo_Log::notice("gen outside-account , num:".$outside_account_count);
        $xml = Libs_Util_Array2xml::createXML('DOCUMENT', $arrRet);
        $strXml = $xml->saveXML();
        $xmlFile = $this->get_tmp_path() . 'account.0.xml';
        file_put_contents($xmlFile, $strXml);
        Bingo_Log::notice("gen success: ".$xmlFile." num:".count($arrRet['item'])  );
        return true;
    }

    /**
     * [parse description] 获取站外数据
     * @return [array] [description]
     * @param str
     */
    public function getOutside(){

        $arrOutput = array();
        $url = self::$arrOutSiteApi['iQiYi']['account'];
        $res = $this->fetchUrlDataFromOrp($url);
        if(false == $res || 0 != $res['errno']){
            Bingo_Log::warning("call service fail :type [bsx-outsite-account], url:$url , res:".serialize($res));
        }else{
            $objXml = simplexml_load_string($res['data']);
            $arrOutput = json_decode(json_encode($objXml),true);
        }
        return $arrOutput;
    }


}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
