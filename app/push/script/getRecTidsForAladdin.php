<?php
/***************************************************************************
 *
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * <AUTHOR>
 * @date 2021/09/17 10:35:26
 * @brief
 *
 *
 **/

require_once dirname(__file__)."/Init.php";
script::init("getRecTidsForAladdin");
define('PATH', './data/alading/');
define('EXPIRE',604800 );//7天


/**
 * @param unknown $uid
 * @return
 */
function run($total,$num,$current,$day){

    Bingo_Log::notice('script start');
    $intStartTime = time();
    $fileName = 'final_res.'.$day;

    $keyTid = Dl_Alading_Alading::ALADDIN_REC.'_';//存储tid的key
    $keySuccess = Dl_Alading_Alading::ALADDIN_REC.'_'.$num.'_'.date("Ymd", time());//计数的key
    $currentKey = 'current_'.$total.'_'.$num.'_'.$day;


    //$key = $keyTid.'210377';
    //$res=Dl_Alading_Alading::get($key);var_dump($res);exit;
    $intCurrent = intval(Dl_Alading_Alading::get($currentKey));
    if($current > 0){
        $intCurrent = $current;
    }
    Bingo_Log::notice("current is ".$intCurrent);
    $res = getRecFile($fileName);
    if(!$res)
    {
        Bingo_Log::warning("get file: $fileName failed");
        //email
        $arrInput['content'] = 'file is null ! url of file is  afs://szth.afs.baidu.com:9902/user/tb_ubdev/recomData/'.$fileName ;
        $arrInput['title'] = 'getRecTidsForAladdin excute fail';
        Libs_Util_Report::sendMail($arrInput);
        exit;
    }
    $fileName = PATH.$fileName;
    $fp = fopen($fileName,'r');
    $i = 1;
    if(!$fp){
        Bingo_Log::warning("open file: $fileName failed");
        exit;
    }
    $msetInput = array();
    $mSetExpireInput = array();



    while(!feof($fp) || !empty($msetInput))
    {

        $line = strval(trim(fgets($fp)));
        if((empty($line) || $intCurrent > $i || $i % $total != $num ) && empty($msetInput)){
            $i++;
            continue;
        }

        $arrForum = json_decode($line,true);

        $intFid = $arrForum['fid'];
        $arrTid = $arrForum['tids'];
        $key = $keyTid.$intFid;
        $value = json_encode($arrTid);

        $tmp['key'] = $key;
        $tmp['value'] = $value;
        $msetInput[] = $tmp;

        $tmpExpire['key'] = $key;
        $tmpExpire['seconds'] = 604800;
        $mSetExpireInput['reqs'][] = $tmpExpire;


        if(count($msetInput) == 20 || (feof($fp) && !empty($msetInput))){

            $res =  Dl_Alading_Alading::mset($msetInput);
            if(!$res){
                Bingo_Log::warning("call redis failed,method [mset],key [".serialize($msetInput)."],value [".$res."]");
                $res =  Dl_Alading_Alading::mset($msetInput);
                if(!$res){
                    Bingo_Log::warning("call redis failed again ,method [mset],key [".serialize($msetInput)."],value [".$res."]");
                }
            }

            $res =  Dl_Alading_Alading::mSetExpire($mSetExpireInput);
            if(!$res){
                Bingo_Log::warning("call redis failed,method [mSetExpire],key [".serialize($mSetExpireInput)."],value [".$res."]");
                $res =  Dl_Alading_Alading::mSetExpire($mSetExpireInput);
                if(!$res){
                    Bingo_Log::warning("call redis failed again ,method [mSetExpire],key [".serialize($mSetExpireInput)."],value [".$res."]");
                }
            }
            $msetInput = array();
            $mSetExpireInput = array();
            Dl_Alading_Alading::set($currentKey,$i);


            $successNum = intval(Dl_Alading_Alading::get($keySuccess)) + count($msetInput);
            $resSucc = Dl_Alading_Alading::set($keySuccess,$successNum );
            if($resSucc != true) {
                Bingo_Log::warning("call redis failed ,methond[set],key [$keySuccess],value [" . $successNum . "] , output " . $resSucc);

            }
        }

        if($i%1000 == 0){
            echo $i." ";
            Bingo_Log::notice("num of line is $i");
        }

        $intEndTime = time();
        //确保每个小时重启一次脚本，防止脚本运行时间长导致异常崩掉
        if($intEndTime - $intStartTime >= 3400){
            $numSucc = Dl_Alading_Alading::get($keySuccess);
            Bingo_Log::notice("script end by hour! ");
            exit;
        }
        $i++;


    }

    $numSucc = Dl_Alading_Alading::get($keySuccess);
    $rmFilename =  PATH.'final_res.'.date('Ymd',time()-1296000);
    $cmd = "rm -rf output; rm $rmFilename; rm  -rf output.tar.gz";

    system($cmd, $errno);
    Bingo_Log::notice("exec success errno: $errno,cmd:[$cmd]");
    Bingo_Log::notice('script finish ! the num of success is '.$numSucc);

}



function getRecFile($fileName)
{


    $cmd = "wget -O output.tar.gz --no-check-certificate --header \"IREPO-TOKEN:ba0f1707-1392-4f9f-a820-9e14a6bbcd7e\" \"https://irepo.baidu-int.com/rest/prod/v3/baidu/inf/afs-api/releases/1.9.3.3182/files\";";
    $cmd = $cmd."tar -zxvf output.tar.gz;";
    $cmd = $cmd."./output/bin/afsshell --username=ns-tieba --password=ns_tieba_123 get afs://szth.afs.baidu.com:9902/user/tb_ubdev/recomData/$fileName ".PATH.";";
    $errno = -1;
    system($cmd, $errno);
    if (0 != $errno ) {
        Bingo_Log::warning("exec fail errno: $errno,cmd:[$cmd]");
        return false;
    }
    Bingo_Log::notice("exec success errno: $errno,cmd:[$cmd]");
    return true;

}
//第一个参数用来表示总脚本个数
$total = intval($argv[1]);
//第二个参数用来标识脚本
$num = intval($argv[2]);
//第三个参数用来指定从文本的某一行开始进行读取
$current = intval($argv[3]);
//第四个参数用来指定文件的时间后缀
$day = intval($argv[4])?intval($argv[4]):date("Ymd", time());


run($total,$num,$current,$day);

exit(0);
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
