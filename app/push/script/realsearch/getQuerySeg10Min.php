<?php

define ('BASEPATH',dirname(__FILE__));
define ('DAT<PERSON>ATH',BASEPATH."/data/");
require_once dirname(__file__)."/../Init.php";
require_once dirname(__file__)."/lib.php";
script::init("getQuerySeg10Min");
mkdir(DATAPATH);
run();

/**
 * @param
 * @return
 */
function run(){
	$arrQueryInfo = queryMatch();
	$t = time() - 600;
	$t = $t - $t%600 ;
	$strDate=date('YmdHi',$t);
	$key = 'real_search_tid_10min_'.$strDate;
	$res = Dl_Alading_Alading::rpopList($key);
	while($res != null){
		$tid = intval($res);
		match($arrQueryInfo,$tid,RETRY_30MIN);
		$res = Dl_Alading_Alading::rpopList($key);
	}
	Dl_Alading_Alading::del<PERSON>ey($key);
}

?>
