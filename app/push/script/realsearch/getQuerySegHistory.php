<?php

define ('BASEPATH',dirname(__FILE__));
define ('DAT<PERSON><PERSON><PERSON>',BASEPATH."/data/");
require_once dirname(__file__)."/../Init.php";
require_once dirname(__file__)."/lib.php";
script::init("getQuerySegHistory");
mkdir(DATAPATH);
run();

/**
 * @param
 * @return
 */
function run(){
	$t = time();
	$t = $t - $t%300 -600;
	$strDate=date('YmdHi',$t);
	$fileName = "hot_history_" . $strDate . ".txt";
	$arrQueryInfo = queryMatch($fileName);
	$key = 'real_search_tid_list_history';
	$res = Dl_Alading_Alading::rpopList($key);
	while($res != null){
		$tid = intval($res);
		match($arrQueryInfo,$tid,false);
		$res = Dl_Alading_Alading::rpopList($key);
	}
	Dl_Alading_Alading::del<PERSON><PERSON>($key);
}

?>
