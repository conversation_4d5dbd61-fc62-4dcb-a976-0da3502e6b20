<?php
/**
 * 推送工具类
 */

/**
 * 格式化HIVE数据
 *
 * @param 
 * @return
 */
function formatHiveData($strLine) {
    $strLine = trim($strLine);
    if (empty($strLine)) {
        return array();
    }

    $arrLine = explode("\t",$strLine);
    if(count($arrLine)<=1) {
        echo "line data format error\n";
        return array();
    }

    $threadId = intval($arrLine[0]);

    // 解析帖子信息
    $strThread = $arrLine[1];
    $strThread = Bingo_Encode::convert($strThread,"UTF-8","GBK");
    $arrThread = json_decode($strThread,1);

    if ( isset($arrThread['create_time'] )) {
        $strCreateTime = $arrThread['create_time'];
    }

    // echo "$threadId, $strCreateTime\n";

    if( $threadId>0 && strlen($strCreateTime)>0) {
        return array($threadId,$strCreateTime);
    }

    echo "line data format error too.\n";
    return array();
}
        
function mylog() {
}

/**
 * 弥补PHP is_file函数无法判断文件为空
 *
 * @param 
 * @return
 */
function isFileOrEmpty($strPath) {
    if (strlen($strPath)<=0) {
        echo "file path is empty\n";
        return false;
    }

    $dirName = dirname($strPath);
    $baseName = basename($strPath);
    
    $strCmd = "ls -hl $dirName | grep $baseName | awk '{print $5}'";
    
    $strRet = exec($strCmd);

    if ( false == $strRet || 0 == $strRet) {
        return false;
    }
    return true;
}
