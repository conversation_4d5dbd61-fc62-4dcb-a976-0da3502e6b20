<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
define ('BASEPATH',dirname(__FILE__));
define ('DATAPATH',BASEPATH."/data/vipping");
require_once dirname(__file__)."/Init.php";
script::init("voteFirst3");
run($argv);
/**
 * @param null
 * @return boolean
 */
function run($argv)
{
    //先从词表取期数phase
        $handleWordServer = Wordserver_Wordlist::factory();
        $strTableName= "tb_wordlist_redis_sendsms";
        $arrInput = array (
            'table' => $strTableName, //英文表名
            'start' => 0,
            'stop' => 1,
        );
        $arrOutput = $handleWordServer->getTableContents ( $arrInput );
        if($arrOutput['ret']['beauty_pk_phase']>0 && $arrOutput['ret']['beauty_pk_phase']<=3)
        {
            $phase = $arrOutput['ret']['beauty_pk_phase'];
        }
        else
        {
            $phase = 1;
        }
        
        $phase = 333;

        //从词表取期数phase结束
        $arrUser = array('葛乔华','薇熙','陈禹今');
        foreach($arrUser as $k => $v)
        {
            $intStep = rand(10,20);
            //存网红投票结果
            $arrInput = array(
                'key'   =>  "beautyvotenums_".$phase,
                'field' =>  $v,
                'step'  =>  $intStep,
            );
            print_r($arrInput);
            $ret = _callRedis('twlive', 'HINCRBY', $arrInput);
            echo $ret['err_msg']."\n";
        }
        /*$arrInput = array(
            'key'   =>  "beautyvotenums_".$phase,
        );
        $arrOutput = _callRedis('twlive', 'HGETALL', $arrInput, 2);
        print_r($arrOutput); */
}

/**
     * @desc 获取Redis实例
     * @param
     * @return void
     */
    function _getRedis($strRedisName) {
        if($_arrRedis[$strRedisName]) {
            return $_arrRedis[$strRedisName];
        }
        $objRedis = new Bingo_Cache_Redis($strRedisName);
        if(!$objRedis || !$objRedis->isEnable()) {
            Bingo_Log::warning("init redis fail.");
            return false;
        }
        $_arrRedis[$strRedisName] = $objRedis;
        return $_arrRedis[$strRedisName];
    }
        
    /**
     * @desc 调用Redis
     * @param
     * @return void
     */
    function _callRedis($strRedisName, $strFunc, $arrParams, $intRetryTimes = 0) {
        $intRetryTimes = intval($intRetryTimes);
        $mixRedis = _getRedis($strRedisName);
        if(!$mixRedis){
            return false ;
        }

        do {
            $arrRet = $mixRedis->$strFunc($arrParams);

            if($arrRet['err_no'] !== 0 ){
                Bingo_Log::warning("call redis queryerror.[redisname=$strRedisName][func=$strFunc][input=".serialize($arrParams)."][output=".serialize($arrRet)."]");
            }else{
                break;
            }
        }while($intRetryTimes-- > 0);

        return $arrRet;
    }
exit(0);
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
