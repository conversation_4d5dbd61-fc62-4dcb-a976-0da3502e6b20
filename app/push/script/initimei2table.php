<?php
/**
 * Created by PhpStorm.
 * User: liguang03
 * Date: 19/1/2
 * Time: 6:51 ����
 */

define ('BASEPATH',dirname(__FILE__));
define ('DATAPATH',BASEPATH."/data/imei");
require_once dirname(__file__)."/Init.php";
script::init("initimei2table");
echo "script start~\n";
run($argv);
echo "script end~\n";

/**
 * @param $argv
 */

function run($argv) {

    $fileRemotePath = 'ftp://cp01-forum-dm01-05.cp01.baidu.com/home/<USER>/qifeng02/online/imei/';

    if (!is_dir(DATAPATH)) {
        mkdir(DATAPATH);
    }

    $step = 10;
    $group = intval($argv[1]);
    $index = intval($argv[2]);

    $start = $group * $step + $index;
    $end   = ($group + 1) * $step;

    for ($start ; $start < $end; $start++) {
        $fileName = 'imei_' . $start . '.u';
        $cmd = 'cd ' . DATAPATH. "; wget " . $fileRemotePath . $fileName;
        exec($cmd);

        $fileLocatPath = DATAPATH . "/" . $fileName;

        //���ļ�����
        if (!file_exists($fileLocatPath)) {
            echo 'file not exists';
            continue;
        }

        $fp = fopen($fileLocatPath, 'r');

        if ($fp == false) {
            echo 'open file failed';
            continue;
        }
        //��ʼ��
        $n = 0;
        $insert_val = '';

        while (!feof($fp)) {

            $line = trim(fgets($fp));
            $arr = explode("\t" , $line);
            if (count($arr) < 4) {
                echo "bad data\n";
                continue;
            }
            $timestamp = strtotime($arr[3]);
            $insert_val .= '("' . $arr[0] . '",' . $arr[1]. ',' . $timestamp . ',' . $arr[2] . ',' . $timestamp . '),' ;
            $n++;
            if ($n >= 200) {
                if($insert_val<>'')
                {
                    $ret = insertRecord($insert_val, $start);
                }
                $n = 0;
                $insert_val = '';
                usleep(100000);
            }
        }

        if($n >= 0 && $insert_val <> "")
        {
            $ret = insertRecord($insert_val, $start);
        }

        //�ر��ļ�
        fclose($fp);
        //��ʼ����ɺ�ɾ���ļ�
        $cmd = 'rm  ' . $fileLocatPath;
        exec($cmd);

    }


}

/**
 * @param
 * @return
 */
function insertRecord($insert_val, $table_index) {
    $tableName = 'client_info_imei_'.$table_index;
    $strSql = 'insert into ' .$tableName. ' (`imei`, `client_type`,`last_time`, `subapp_type`, `update_time`) values'.$insert_val;
    if(substr($strSql,-1)==",")
    {
        $strSql = substr($strSql,0,-1);
    }
    $arrOutput = DBProxy::_queryDB($strSql);
    //var_dump($arrOutput);
    if($arrOutput === false || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS){
        outLogLine('query_failed :'. $strSql .serialize($arrOutput));
        return false;
    }
    return true;
}


/**
 * ��־�д�ӡ
 * @param $strMsg
 * @param bool $bolLineInfo
 * @return null
 */
function outLogLine($strMsg, $type = 'w', $bolEcho = false, $bolLineInfo = false, $bolFlush = true)
{
    $strFileName = '';
    $intLineNo = 0;
    if ($bolLineInfo) {
        if (defined('DEBUG_BACKTRACE_IGNORE_ARGS')) {
            $arrTrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        } else {
            $arrTrace = debug_backtrace();
        }
        if (!empty($arrTrace)) {
            $strFileName = basename($arrTrace[0]['file']);
            $intLineNo = intval($arrTrace[0]['line']);
            //$strMsg = $strFileName . ":Line($intLineNo):" . $strMsg;
        }
    }
    if ('n' == $type) {
        Bingo_Log::notice($strMsg, '', $strFileName, $intLineNo);
    } else if ('w' == $type) {
        Bingo_Log::warning($strMsg, '', $strFileName, $intLineNo);
    } else if ('f' == $type) {
        Bingo_Log::fatal($strMsg, '', $strFileName, $intLineNo);
    } else {
        Bingo_Log::warning($strMsg, '', $strFileName, $intLineNo);
    }
    if($bolFlush){
        $logObj = Bingo_Log::getModule();
        if(false !== $logObj && null != $logObj && !empty($logObj)){
            $logObj->flush();
        }
    }
    if ($bolEcho) {
        $strLogPrefix = '';
        if(!empty($strFileName)){
            $strLogPrefix = $strFileName;
        }
        if(0 < $intLineNo){
            $strLogPrefix = $strFileName.'['.$intLineNo.']';
        }
        if(!empty($strLogPrefix)){
            $strMsg = $strLogPrefix.' '.$strMsg;
        }
        echo $strMsg . PHP_EOL;
    }
}


class DBProxy{
    const DB_NAME = 'forum_client';
    private static $_objDB = null;

    /**
     * @param
     * @return
     */
    public static function _queryDB($strSql, $strTimer = 'db_query') {
        $objDB = self::_getDB();
        if ( !$objDB ){
            outLogLine('fail to get db. sql:'.$strSql);
            return false;
        }
        Bingo_Timer::start($strTimer);
        $arrRet = $objDB->query($strSql);
        Bingo_Timer::end($strTimer);
        if ( $arrRet === false ){
            outLogLine("execute sql error sql:$strSql");
        }
        return $arrRet;
    }

    /**
     * @param
     * @return
     */
    private static function _getDB() {
        if ( self::$_objDB && self::$_objDB->isConnected(true)){
            return self::$_objDB;
        }

        if (self::$_objDB) {
            self::$_objDB->close();
        }

        Bingo_Timer::start('db_init');
        self::$_objDB = Tieba_Mysql::getDB(self::DB_NAME);
        outLogLine('db_init');
        Bingo_Timer::end('db_init');
        if ( self::$_objDB && self::$_objDB->isConnected(true)){
            self::$_objDB->charset('utf8');
            return self::$_objDB;
        }
        else{
            outLogLine('fail to connect db');
            return null;
        }
    }
}