<?php
/**
 * 向大搜推送热议帖子数据
 */

// 1. 调用hottopic接口获取数据
// 2. 调用本地数据库服务，存储
// 3. 调用推送服务

require_once dirname(__file__)."/Init.php";

// 脚本初始化
script::init("PushHottopicThread");

/**
 * 执行函数
 * @param
 * @param
 * @return 
 */
function run($argv,$argc) {
    $intLimit = 20;
    $arrSubList = array(
        'yule',
        'game',
        'sport',
        'wenxue',
        'carton', // 无力吐槽 ...
    );
    // 贴吧id列表
    $arrThreadId = array();
    
    // 主榜单
    $arrInput = array(
        'req' => array(
            're_locate' => 2,
            'num' => $intLimit,
        ),
    );
    $arrOut = Tieba_Service::call('hottopic', 'getSearchRecommend', $arrInput, null, null, 'post', 'php', 'utf-8');
    if (false == $arrOut || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
        echo "get hottopic_main_list fail.[".serialize($arrOut)."]\n";
        exit(1);
    }
    foreach ($arrOut['ret'] as $hottopic) {
        if (!empty($hottopic['topic_info']['good_tids'])) {
            $arrThreadId = array_merge($arrThreadId,$hottopic['topic_info']['good_tids']);
        }
    }
    // 分榜单 
    foreach ($arrSubList as $sub) {
        $arrInput = array(
            'req' => array(
                're_locate' => 9,
                'num' => $intLimit,
                'bang_tab' => $sub,
            ),
        );
        usleep(50000);
        $arrOut = Tieba_Service::call('hottopic', 'getSearchRecommend', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false == $arrOut || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            echo "get hottopic_sub_list fail.[$sub] [".serialize($arrOut)."]\n";
            usleep(100000);
            continue;
        }
        foreach ($arrOut['ret'] as $hottopic) {
            if (!empty($hottopic['topic_info']['good_tids'])) {
                $arrThreadId = array_merge($arrThreadId,$hottopic['topic_info']['good_tids']);
            }
        }
    }

    // 去除重复
    $arrThreadId = array_unique($arrThreadId);
    $arrPage = array_chunk($arrThreadId,5);

    foreach ($arrPage as $arrTids) {

        $arrParam = array(
            "tids"=>$arrTids,
            "type"=>Service_Alading_Lib_VippingNew::VIPPING_FROM_TYPE_HOTTOPIC, // 这里耦合了...
        );

        usleep(50000);
        $arrRet = Tieba_Service::call('push','checkTidRTSStatus',$arrParam,null,null,'post','php','utf-8');

        if ($arrRet == false || $arrRet['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            echo "insert thread into local DB fail. ".serialize($arrRet)."\n";
            // 防止sevice雪崩
            usleep(100000); // 100ms
            continue;
        }

        if (count($arrRet['data']) < 1) {
            echo "check_ret is empty.\n";
            continue;
        }

        foreach ($arrRet['data'] as $tid) {

            // 推送至RTS库
            $arrTransPack = array(
                'command_no' => Service_Alading_Alading::THREAD_COMMIT_CMD,
                'thread_id' => intval($tid),
                'from_type' => Service_Alading_Lib_VippingNew::VIPPING_FROM_TYPE_HOTTOPIC,
                'ping_type' => Service_Alading_Lib_VippingNew::PING_TYPE_RECOMMAND,
            );
            
            usleep(50000);
            $ret = Service_Alading_Lib_VippingNew::sendVipping($arrTransPack);
            if (!$ret) {
                echo "call sending vip fail [$tid]\n";
                usleep(100000); // 100ms
            }
        }
        echo ".";
    }
}


// ******************* 执行逻辑 ********************

Bingo_Timer::start("main");
run(null,null);
Bingo_Timer::end("main");
$totalTime = Bingo_Timer::calculate("main");
echo "main cost total_time : $totalTime.\n";
