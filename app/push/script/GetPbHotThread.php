<?php
/**
 * 更新每个吧的相关热帖
 * Created by PhpStorm.
 * User: liguang03
 * Date: 18/12/18
 * Time: 下午2:18
 */

ini_set ( "memory_limit", "-1" );
define('MODULE_NAME', 'push');

date_default_timezone_set ( "Asia/Chongqing" );

// 定义相关路径信息
define ( 'APP_NAME', 'push' );
define ( 'SCRIPT_NAME', 'GetPbHotThread.php' );
define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../../..' );
define ( 'SCRIPT_ROOT_PATH', ROOT_PATH . '/app/' . APP_NAME . '/script' );
define ( 'SCRIPT_LOG_PATH', ROOT_PATH . '/log/app/' . APP_NAME );
define ( 'SCRIPT_CONF_PATH', ROOT_PATH . '/conf/app/' . APP_NAME );
define ( 'REDIS_FID_HOTTHREAD_POOL', 'redis_fid_hotthread_pool_%s');
define ('BASEPATH',dirname(__FILE__));
define ('DATAPATH',BASEPATH."/data/");

/**
 * @param $strClassName
 * @return true
 */
function __autoload($strClassName) {
	$strNewClassName = str_replace ( '_', '/', $strClassName . ".php" );
	$arrClass = explode ( '/', $strNewClassName );
	$intPathLen = count ( $arrClass );
	$strLastName = $arrClass [$intPathLen - 1];
	$strTmp = strtolower ( $strNewClassName );
	$intPreLen = strlen ( $strTmp ) - strlen ( $strLastName );
	$strNewClassName = substr ( $strTmp, 0, $intPreLen ) . $strLastName;
	$strClassPath = ROOT_PATH . '/app/' . APP_NAME . '/' . $strNewClassName;
	require_once $strClassPath;
}
spl_autoload_register ( '__autoload' );

// 设置logid
if (! defined ( 'REQUEST_ID' )) {
	$requestTime = gettimeofday ();
	define ( 'REQUEST_ID', (intval ( $requestTime ['sec'] * 100000 + $requestTime ['usec'] / 10 ) & 0x7FFFFFFF) );
}

if (function_exists ( 'camel_set_logid' )) {
	camel_set_logid ( REQUEST_ID );
}

Bingo_Log::init ( array (
	LOG_SCRIPT => array (
		'file' => SCRIPT_LOG_PATH . '/' . SCRIPT_NAME . '/' . SCRIPT_NAME . '.log',
		'level' => 0x01 | 0x02 | 0x04 | 0x08 ,
	),
), LOG_SCRIPT );

$ret = GetPbHotThread::execute($argv[1]);
$oldFileName = date('Ymd', strtotime('-2 day'));
$cmd = "cd ". DATAPATH . ";rm -rf pb_hotthread_fail".$oldFileName.";rm -rf pb_hotthread_succ".$oldFileName;
exec($cmd);
if (!$ret) {
	exit(-1);
}
exit(0);


class GetPbHotThread
{
    const DATABASE = 'tiebadata';
    const HOSTNAME = 'palo-yqa.baidu.com';
    const PORTNUMB = '9030';
    const USERNAME = 'ns_tieba_off';
    const PASSWORD = 'ns_tieba_off_123';
    const SPLITNUM = 100;
    const MULTINUM = 30;
    // redis expire time
    const EXPIRE_TIME = 172800; // 2d
    const REDIS_KEY = "pbhotrecommentnew";
    protected static $_db = null;
    protected static $_redis = null;
    protected static $_eventday = '';

    /**
     * @return bool
     */
    protected static function _init()
    {
        self::$_db = mysql_connect(self::HOSTNAME . ':' . self::PORTNUMB, self::USERNAME, self::PASSWORD);
        if (self::$_db == null) {
            var_dump('db connecte fail.');
            return false;
        }
        mysql_select_db(self::DATABASE);

        self::$_redis = new Bingo_Cache_Redis('push');
        if (self::$_redis == null) {
            echo 'redis init fail';
            return false;
        }
        self::$_eventday = date('Ymd', strtotime('-2 day'));
        return true;
    }

    /**
     * @return bool
     */
    public static function execute($snum)
    {
        if (!self::_init()) {
            var_dump('init failed!');
            return false;
        }

        //取出总行数
        $sql = sprintf("select count(tid) from tieba_mds_bhv_thread_day where pv>50 and day=%d and client_type='wap_smart' ", self::$_eventday);
        $result = mysql_unbuffered_query($sql,self::$_db);
        $row = mysql_fetch_array($result);
        mysql_free_result($result);
        echo "yuantotalnum=".$row[0]."\n";
        $recordNum = ceil($row[0]/10);//总数/10并向上取整
        
        echo "total_num:" . $recordNum . "\n";
        $step = 100;//10000;
        $map = array();
        //echo ceil($recordNum/$step);
        for ($i = 0; $i < ceil($recordNum/$step); $i++) 
        {
            // 获取热帖
            $sql = sprintf("select tid from tieba_mds_bhv_thread_day where pv>50 and day=%d and client_type='wap_smart' order by tid DESC limit %s, %s ", self::$_eventday, $snum*$recordNum+$i*$step, $step );
            echo $sql . "\n";
            $arrRes = mysql_unbuffered_query($sql, self::$_db);
            $ins = array();
            while (true) {
                $row = mysql_fetch_array($arrRes, MYSQL_NUM);
                if (false === $row) {
                    break;
                }
                //var_dump($row[0]);
                $res = self::GetCorThread($row[0]);
                //相关帖多于2条才做推荐
                if (!empty($res) && count($res)>2) 
                {
                    $reInput = array(
                        'field' => $row[0],
                        'value' => serialize($res),
                    );
                    array_push($ins, $reInput);
                }
            }
            
            //写入redis
            $out = array(
                'key' => self::REDIS_KEY,
                'fields' => $ins,
            );

            $ret_redis = self::$_redis->HMSET($out);
            //print_r($ret_redis);
            //如果存储失败，则写入日志；当存储成功时，$ret_redis的值为ok
            if($ret_redis['err_no']!=0)
            {
                file_put_contents(DATAPATH . 'pb_hotthread_fail' . self::$_eventday,  json_encode($out) . "\n", FILE_APPEND);
                Bingo_Log::warning("HMSET redis fail.Redis input==".serialize($out));
            }
            else
            {
                file_put_contents(DATAPATH . 'pb_hotthread_succ' . self::$_eventday,  json_encode($out) . "\n", FILE_APPEND);
            }
            echo "\n-------------\n";
        }
        return true;
    }

    public static function GetCorThread($thread_id)
    {
        $tid_input = array(
            "thread_id" => $thread_id, //帖子id
            "offset" => 0,
            "res_num" => 1,
            "see_author" => 0,
            "has_comment" => 0,
            "has_mask" => 0,
            "has_ext" => 0,
            "need_set_pv" => 0,
            "structured_content" => 0
        );
        $resg   = Tieba_Service::call('post', 'getPostsByThreadId', $tid_input, NULL, NULL, 'post', 'php', 'utf-8');
        //var_dump($resg);
        $title = $resg["output"]["output"][0]["post_infos"][0]["title"];
        //echo $title."\n"; 
        //根据title获取该帖子的相关贴
        $title_input = array(
            'only_thread' => 1,
            'page_num' => 1,
            'res_num' => 20,
            'query_word' => iconv("UTF-8", "GB2312//IGNORE", $title),
            'query_type' => 0,
            'sort_mode' => 2,
            'need_vip' => 0,
            'vip_word' => $title,
            'tbt_min' => 0,
            'tbt_max' => 0,
            'call_from' => 'client_search',
            'isZhonghePage' => true,
            'no_filter' => 1,
        );
        //print_r($title_input);
        $tem = array();
        $output = Tieba_Service::call('search', 'searchPost', $title_input);
        //print_r($output);
        if($output["data"]["post_list"] != null)
        {
            foreach ($output["data"]["post_list"] as $value){
                if($value["tid"] != $thread_id && !in_array($value["tid"],$tem,true)){
                    array_push($tem, $value["tid"]);
                }
                else
                    continue;
            }
        }
        return $tem;
    }


    /**
     * @param $redis
     * @param $method
     * @param $input
     * @return mixed
     */
    private static function redis_recall($redis,$method, $input){
        $ret = $redis->$method($input);
        $retry = 0;
        while($retry < 3 && ($ret['err_no'] != 0 || !$ret)){
            sleep(1);
            $retry++;
            $ret = $redis->$method($input);
        }
        return $ret;
    }
}

