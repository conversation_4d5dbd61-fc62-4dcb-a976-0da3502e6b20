<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
/**
 * @file PingVip.php
 * <AUTHOR>
 * @date 2017/4/02 16:35:26
 * @brief ɾredis������
 */

require_once dirname(__file__)."/Init.php";
script::init("DelUserMaskRedis");

$user_id = intval($argv[1]);
if ($user_id == 0){
	//��redis��ȡ,��redisû�����ݣ���1��ʼ
	$res = Dl_Alading_Alading::get('user_mask_del_key');
	if (!$res){
		sleep(1);
		$res = Dl_Alading_Alading::get('user_mask_del_key');
		if (!$res){
			$user_id = 1;
		}
	}
	else {
		$user_id = $res;
	}
}
$begin_time = time();
$end_time = $begin_time;
$arrKeys = array();
while (($end_time - $begin_time) <= 3500){
	if (rand(0,999) == 0){
		$end_time = time();
		$res = Dl_Alading_Alading::set('user_mask_del_key',$user_id);
		if (!$res){
			sleep(1);
			Dl_Alading_Alading::set('user_mask_del_key',$user_id);
		}
	}
	$arrKeys[] = array(
		'key' => Dl_Alading_Alading::MASKUSER_SET.$user_id++,
	);
	if (count($arrKeys) >= 10){
		$ret = Dl_Alading_Alading::mDelKeys($arrKeys);
		if ($ret == true){
			Bingo_Log::notice("del success: $user_id");
		}
		else {
			Bingo_Log::notice("del failed: $user_id");
		}
		$arrKeys = array();
	}
}
if (!empty($arrKeys)){
	Dl_Alading_Alading::mDelKeys($arrKeys);
}
$res = Dl_Alading_Alading::set('user_mask_del_key',$user_id);
if (!$res){
	sleep(1);
	Dl_Alading_Alading::set('user_mask_del_key',$user_id);
}
exit(0);
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
