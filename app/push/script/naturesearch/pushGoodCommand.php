<?php
/**
 * 操作：天级库延时推送nmq收到的优质评论
 * 目的：为优质评论页累计楼中楼和互动数据
 * 数据范围：以每10分钟为时间区间，圈定数据作为推送数据集（去重）
 * 脚本执行：每5分钟执行一次，取累积的时间区间数据set中的某一个，并且加锁，如果取不到时间区间- 则退出
 * eg：数据时间为9：01，区间key为XXX_9：00,则执行时间为14：01
 */

ini_set ( "memory_limit", "-1" );
date_default_timezone_set ( "Asia/Chongqing" );

define ( 'APP_NAME', 'push' );
define ( 'SCRIPT_NAME', 'pushGoodCommand' );
define ('BASEPATH',dirname(__FILE__));
define ('DATAPATH',BASEPATH."/data/");
define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../../..' );
define ( 'SCRIPT_ROOT_PATH', ROOT_PATH . '/app/' . APP_NAME . '/script' );
define ( 'SCRIPT_LOG_PATH', ROOT_PATH . '/log/app/' . APP_NAME . '/script' );
const intScriptExecuteTime = 270; //脚本执行时间为4.5分钟，超出则退出
require_once dirname(__file__)."/../Init.php";
script::init("pushGoodCommand");
mkdir(DATAPATH);
run();

function run(){
    echo 'script start';

    $intStartTime = time();
    $strBeginTime = date ('YmdHi', strtotime("-2 days"));
    $strTimePre5Hour = date('YmdHi', time() - Service_Vipping_Vipping::DELAY_TIME_THREAD_PUSH);//202101261612
    $arrTimeKeyList = Dl_Alading_Alading::zrangebyscore(Service_Vipping_Vipping::PRE_REDIS_KEY_COMMAND_NMQ, $strBeginTime, $strTimePre5Hour);
    foreach ($arrTimeKeyList as $strTimeKey) {
        //判断是否被其他脚本执行，否则加锁执行
        $keyRedisLock = $strTimeKey.'lock';
        if (Dl_Alading_Alading::get($keyRedisLock)){
            continue;
        }
        if(!Dl_Alading_Alading::set($keyRedisLock,1)){
            continue;
        }
        Dl_Alading_Alading::expireKey($keyRedisLock,Service_Vipping_Vipping::DATA_BLOCK_TIME_THREAD_PUSH);
        $arrFiledList = Dl_Alading_Alading::hKeys($strTimeKey);
        if ($arrFiledList ===false){
            Bingo_Log::warning("get Redis faile, key is ".$strTimeKey);
            continue;
        }
        if (empty($arrFiledList)){
            Dl_Alading_Alading::zrem(Service_Vipping_Vipping::PRE_REDIS_KEY_COMMAND_NMQ, $strTimeKey);
            Dl_Alading_Alading::delKey($strTimeKey);    
            continue;
        }
        $arrTmpTid[] = current($arrFiledList);
        $boolAllFinish = true;
        while (true){
            $itemNextField =next($arrFiledList);

            if (count($arrTmpTid)>=30  || false===$itemNextField){
                $arrPushThreadInfo = Dl_Alading_Alading::getHashInfo($strTimeKey,'',$arrTmpTid);
                $arrFinishedIndex = booMultiPushThreadInfo($strTimeKey,$arrPushThreadInfo);
                $boolPartFinish = count($arrFinishedIndex) == count($arrTmpTid);
                $boolAllFinish = ($boolAllFinish && $boolPartFinish) ? true : false;
                $arrTmpTid = [];
            }
            //判断超时，设定4.5分钟结束当前脚本
            if (isTimeOut($intStartTime)){
                Dl_Alading_Alading::delKey($keyRedisLock);
                echo 'script is all done~';
                return true;
            }
            if (false===$itemNextField){
                break;
            }
            $arrTmpTid[]=$itemNextField;

        }
        //若当前时间区间内的数据全部操作完成，则删除该区间的key
        if ($boolAllFinish === true) {
            Dl_Alading_Alading::zrem(Service_Vipping_Vipping::PRE_REDIS_KEY_COMMAND_NMQ, $strTimeKey);
            Dl_Alading_Alading::delKey($strTimeKey);
        }
        Dl_Alading_Alading::delKey($keyRedisLock);
    }
    echo 'script is all done~';

}



//批量发送贴子信息至大搜，并删除当前时间段中的 filed list
function booMultiPushThreadInfo($strTimeKey,$arrThreadInfo){
    $arrFinishedIndex = [];
    $beginTime = microtime(true) * 1000;
    $_objMulti = new Tieba_Multi('delay_push_command_info');
    foreach ($arrThreadInfo as $key => $strItem) {
        $info = json_decode($strItem, true);
        if ( !$info['otherInfo']['bolAsk']){
        $bolJudge = judge($info);
        }
        if ($info['otherInfo']['bolAsk'] || $bolJudge === true) {
            $arrInput = array(
                'thread_id' => $info['thread_id'],
                'post_id' => $info['post_id'],
                'otherInfo' => array('forum_name' => $info['otherInfo']['forum_name']),
            );
            $arrMulInput = array(
                'serviceName' => 'push',
                'method' => 'pushPostToVipping',
                'input' => $arrInput,
                'ie' => 'utf-8',
            );
            $mulKey = 'pushCommentInfo-' . $key;
            $_objMulti->register($mulKey, new Tieba_Service($mulKey), $arrMulInput);
        }elseif($bolJudge === false){
            continue;
        }
        else{
            $arrFinishedIndex[] = $key;
            Bingo_Log::notice('command is filter. tid & option = '.$key.' push_info is '.$strItem);
        }
    }

    Bingo_Timer::start('multi');
    $_objMulti->call();
    Bingo_Timer::end('multi');
    foreach ($arrThreadInfo as $key => $strItem) {
        $arrMulOut = $_objMulti->getResult('pushCommentInfo-'.$key);
        if (false == $arrMulOut || Tieba_Errcode::ERR_SUCCESS != $arrMulOut['errno']) {
            Bingo_Log::warning('call push:pushPostToVipping fail.input:['.$strItem. 'output: [' . serialize($arrMulOut) . ']');
            continue;
        }else{
            $arrFinishedIndex[] = $key;
            Bingo_Log::notice('delay_push_success. tid & option = '.$key.' push_info is '.$strItem . 'output is '.json_encode($arrMulOut));
            $info = json_decode($strItem, true);

            //存入库里，去重
            $arrStore['thread_id'] = $info['thread_id'];
            $arrStore['post_id'] = $info['post_id'];
            $arrStore['forum_id'] = $info['forum_id'];
            $arrStore['p_uid'] = $info['otherInfo']['p_uid'];
            $arrStore['t_uid'] = $info['otherInfo']['t_uid'];
            $arrStore['command_no'] = $info['command_no'];
            $arrStore['create_time'] = $arrStore['last_modify_time'] = time();
            $arrStore['last_push_type'] = 1;

            $ret = Service_Posts_Posts::addPostRecords($arrStore);
            if($ret == false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning("failed to call addPostRecords:,input [".serialize($arrStore)."],output [".serialize($ret));
            }
        }
    }
    Dl_Alading_Alading::hDelFiled($strTimeKey,$arrFinishedIndex);
    $endTime = microtime(true) * 1000;
    $processTime = $endTime - $beginTime;
    if ($processTime < 500){
        usleep((500-$processTime)*1000);
    }
    return $arrFinishedIndex;
}

function judge($arrInput){
    $handleWordServer = Wordserver_Wordlist::factory();
    $arrItemInfo = $handleWordServer->getValueByKeys(array('good_cor_limit', 'good_score_limit', 'good_weight', 'count_limit'), 'tb_wordlist_redis_naturesearch_pushcomment');

    $pid = $arrInput['post_id'];
    $tid = $arrInput['thread_id'];
    $pLen = $arrInput['otherInfo']['pLen'];
    $tLen = $arrInput['otherInfo']['tLen'];

    $objMultiCall = new Tieba_Multi('reduceRedis');
    //获取帖子内容
    $input = array(
        "thread_ids" => array($tid),
        "need_abstract" => 0,
        "forum_id" => 0,
        "need_photo_pic" => 0,
        "need_user_data" => 0,
        "icon_size" => 0,
        "need_mask_info" => 0,
        "need_forum_name" => 1, //是否获取吧名
        "call_from" => "pc_frs", //上游模块名
        "need_post_content" => 1,
        "structured_content" => 1,
    );
    $arrMultiInputThread = array(
        'serviceName' => 'post',
        'method' => 'mgetThread',
        'input' => $input,
        'ie' => 'utf-8',
    );
    $objMultiCall->register('mgetThread',new Tieba_Service('reduce'),$arrMultiInputThread);

    $input = array(
        'thread_id' => $tid,
        'post_id' => $pid,
        'comment_id' => 0,
        'res_num' => 1,
        'see_author' => 0,
        'has_comment' => 0,
        'has_mask' => 1,
        'has_ext' =>1,
        'structured_content' =>1,

    );
    $arrMultiInputPost = array(
        'serviceName' => 'post',
        'method' => 'getCommentSrchByThreadId',
        'input' => $input,
        'ie' => 'utf-8',
    );
    $objMultiCall->register('getCommentSrchByThreadId',new Tieba_Service('comment'),$arrMultiInputPost);

    $input = array(
        'thread_id' => $tid,
        'post_id' => $pid,
        'comment_id' => 0,
        'offset' => 0,
        'res_num' => 1,
        'status' => 0
    );
    $arrMultiInputPost = array(
        'serviceName' => 'post',
        'method' => 'getCommentList',
        'input' => $input,
        'ie' => 'utf-8',
    );
    $objMultiCall->register('getCommentList',new Tieba_Service('comment'),$arrMultiInputPost);

    $objMultiCall->call();
    $arrRalRet = $objMultiCall->results;
    if(false === $arrRalRet){
        Bingo_Log::warning("multicall fail. method[mgetThread, getBtxInfo, getCommentSrchByThreadId ], thread_id = $tid ");
        return false;
    }

    //获取帖子内容
    if($arrRalRet['mgetThread'] === false || $arrRalRet['mgetThread']['errno'] !== Tieba_Errcode::ERR_SUCCESS){
        Bingo_Log::warning("failed to call service:[post], method:[mgetThread],input [".serialize($arrMultiInputThread)."],output [".serialize($arrRalRet['mgetThread']));
        return false;
    }
    $threadInfo = $arrRalRet['mgetThread']['output']['thread_list'][$tid];
    $threadContent = $threadInfo['post_content'];
    $thrContent = $threadInfo['title'];
    foreach ($threadContent as $content){
        if($content['tag'] == 'plainText'){
            $thrContent .= $content['value'];
        }
    }

    //获取楼中楼个数
    if($arrRalRet['getCommentList'] === false || $arrRalRet['getCommentList']['errno'] !== Tieba_Errcode::ERR_SUCCESS){
        Bingo_Log::warning("failed to call service:[post], method:[getCommentList],input [".serialize($arrMultiInputThread)."],output [".serialize($arrRalRet['getCommentList']));
        return false;
    }
    $comment_num = $arrRalRet['getCommentList']['output']['comment_num'];

    //获取回复内容
    if($arrRalRet['getCommentSrchByThreadId'] === false || $arrRalRet['getCommentSrchByThreadId']['errno'] !== Tieba_Errcode::ERR_SUCCESS){
        Bingo_Log::warning("failed to call service:[post], method:[getCommentSrchByThreadId],input [".serialize($arrMultiInputPost)."],output [".serialize($arrRalRet['getCommentSrchByThreadId']));
        return false;
    }
    $arrContent = $arrRalRet['getCommentSrchByThreadId']['output']['output'][0]['post_infos'][0]['content'];
    $strContent = '';
    foreach ($arrContent as $content){
        if($content['tag'] == 'plainText'){
            $strContent .= $content['value'];
        }
    }
    
    $tContent = escapeshellarg(str_replace("\"","\\\"",$thrContent));
    $pContent = escapeshellarg(str_replace("\"","\\\"",$strContent));
    $tContent = str_replace('\'','',$tContent);
    $pContent = str_replace('\'','',$pContent);

    $cmd = "curl -d '{\"qtype\":4, \"qori\":{\"query1\":\"".$tContent."\",\"query2\":\"".$pContent."\", \"scw_out_flag\":255,\"langid\":1,\"lang_para\":129}, \"qseg\":{\"query1\":[],\"query2\":[]}, \"qsign\":{\"query1\":[],\"query2\":[]}}' \"http://bj01.nlpc.baidu.com/nlpc_clicksim_107?username=chenshiqiang&app=nlpc_2021030110480615282&access_token=159ef3105a5080ef41a95e38253f117e&encoding=utf8\"";
    $errno = 0;
    $ret = system($cmd, $errno);
    $ret = json_decode($ret,true);

    if (empty($ret['click_sim']) || !isset($ret['click_sim'])){
        Bingo_Log::warning("pushGood comment nlp click_sim empty tid:[$tid] pid:[$pid]");
        return 'jump';
    }

    if ($ret['click_sim'] < $arrItemInfo['good_cor_limit']) {
        return 'jump';
    }

    $weightList = explode(',', $arrItemInfo['good_weight']);
    $score = $tLen*$weightList[0] + $pLen*$weightList[1] + $comment_num*$weightList[2];
    if ($score < $arrItemInfo['good_score_limit']){
        return 'jump';
    }

    return true;
}

function isTimeOut($intStartTime){
    if ((time()-$intStartTime)>intScriptExecuteTime){
        Bingo_Log::warning('script is timeout. cost '.time()-$intStartTime);
        return true;
    }
}

?>

