<?php
/**
 * @brief ���������ͽű�
 * <AUTHOR>
 * @desc
 */
ob_start ();
ini_set("memory_limit","-1");
date_default_timezone_set ( "Asia/Chongqing" );

/**
 * ����ģ�������Ϣ
 * 
 * @var unknown
 */
define ( 'APP_NAME', 'push' );
define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../..' );
define ( 'SCRIPT_ROOT_PATH', ROOT_PATH. '/app/'. APP_NAME.'/script');
define ( 'SCRIPT_IMPL_PATH', SCRIPT_ROOT_PATH . '/impl' );
define ( 'SCRIPT_DATA_PATH', ROOT_PATH . '/data/app/'.APP_NAME );
define ( 'SCRIPT_LOG_PATH', ROOT_PATH . '/log/app/'.APP_NAME );
define ( 'SCRIPT_CONF_PATH', ROOT_PATH . '/conf/app/'.APP_NAME );
define ( 'SCRIPT_LIBS_PATH', ROOT_PATH . '/app/'.APP_NAME.'/libs' );
define ( 'SCRIPT_SERVICES_PATH', ROOT_PATH . '/conf/app/'.APP_NAME.'/services' );


if( isset($argv[2])&& strval($argv[2]) == 'test')
{
	echo "��Ŀ¼:" . realpath(ROOT_PATH) . "\n";
	echo "�ű���Ŀ¼:" . realpath(SCRIPT_ROOT_PATH) . "\n";
	echo "�ű�ִ���ļ�Ŀ¼:" . realpath(SCRIPT_IMPL_PATH) . "\n";
	echo "����Ŀ¼:" . realpath(SCRIPT_DATA_PATH) . "\n";
	echo "��־Ŀ¼:" . realpath(SCRIPT_LOG_PATH) . "\n";
	echo "����Ŀ¼:" . realpath(SCRIPT_CONF_PATH) . "\n";
	echo "��Ŀ¼:" . realpath(SCRIPT_LIBS_PATH) . "\n";
	echo "����Ŀ¼:" . realpath(SCRIPT_SERVICES_PATH) . "\n";
	exit;
}


/**
 * @desc ����lib service impl�ļ�
 */
$obj = Bingo_Load::getInstance(SCRIPT_DATA_PATH);
$obj->add(SCRIPT_SERVICES_PATH);
$obj->add(SCRIPT_LIBS_PATH);
$obj->add(SCRIPT_IMPL_PATH);

/**
 * ����logid
 */
if (!defined('REQUEST_ID')){
	$requestTime = gettimeofday();
	define('REQUEST_ID', (intval($requestTime['sec'] * 100000 + $requestTime['usec'] / 10) & 0x7FFFFFFF));
}
if (function_exists('camel_set_logid')) {
	camel_set_logid(REQUEST_ID);
}

/**
 * ��ʼ���սű����ݵĲ���
 */
$strScriptName = strval($argv[1]);
$strScriptPath = SCRIPT_IMPL_PATH.'/'.$strScriptName.'.php'; 
if(empty($strScriptName) || !file_exists($strScriptPath) || !class_exists($strScriptName) )
{
	echo "script run failed";
	exit(1);
}

/**
 * ��ʼ����־
 */
define ('SCRIPT_NAME', $strScriptName);
define ('LOG_SCRIPT', $strScriptName);
define ('LOG_FRAME', 'FRAME');
Bingo_Log::init(array(
			LOG_FRAME => array(
				'file'	=> SCRIPT_LOG_PATH . '/'.APP_NAME.'.log',
				'level'	=> 0xFF,
				),
			LOG_SCRIPT => array(
				'file'	=> SCRIPT_LOG_PATH . '/'.SCRIPT_NAME.'/'.SCRIPT_NAME.'.log',
				'level'	=> 0xFF,
				),
		     ), LOG_SCRIPT);


Bingo_Log::pushNotice("Scriptname",$strScriptName);
Bingo_Log::pushNotice("Scriptpath",$strScriptPath); 


$objRun = new $strScriptName;
Bingo_Timer::start($strScriptName);
try{
	$objRun->run();
}catch(Exception $e){
	Bingo_Log::warning(sprintf('main process ,error[%s]file[%s]line[%s]', $e->getMessage(), $e->getFile(), $e->getLine()));
	exit;
}
Bingo_Timer::end($strScriptName);
Bingo_Log::pushNotice('Cost',Bingo_Timer::toString());
Bingo_Log::buildNotice("run successfule",LOG_FRAME);
exit(0);






