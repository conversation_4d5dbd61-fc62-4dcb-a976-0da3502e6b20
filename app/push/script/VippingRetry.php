<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
/**
 * @file VippingRetry.php
 * @date 2015/05/18 20:49:26
 * <AUTHOR>
 * @brief 负责定时读取Vipping推送失败的数据列表,从新推送,保证整体推送成功率三个9
 */


require_once dirname(__file__)."/Init.php";
script::init("VippingRetry");
Bingo_Timer::start("VippingRetry");
process();
Bingo_Timer::end("VippingRetry");
Bingo_Log::pushNotice('Time', Bingo_Timer::toString());
Bingo_Log::buildNotice("VippingRetry script run successful");
exit(0);//正常退出


function process()
{
    $list = Dl_Alading_Alading::VIPPING_RETEY_LIST;
    $maxLimit = 10;
    doList($list, $maxLimit);
    return true;
}

/**
 * @desc
 * @param unknown $list
 * @param unknown $limit [每次读取的上限，数据很多时候，一次只读这么多]
 * @return multitype:
 */
function doList($list, $maxLimit)
{
    $arrXml = array();
    
    $length = Dl_Alading_Alading::getListLength($list);
    Bingo_Log::warning("vipping_retry_list length: ". $length);
    echo "list : ".$list.", length : ".$length."\n";
    if($length < $maxLimit)
    {
        $maxLimit = $length;
        $bolRes = sendList($list, $maxLimit);
        if ($bolRes) {
             echo "send list success, length:$maxLimit \n";
             Bingo_Log::notice("send list success, length:$maxLimit");
        }
    } else {
        $intTime = intval($length/$maxLimit);
        while ($intTime--) {
            $bolRes = sendList($list, $maxLimit);
            if ($bolRes) {
                echo "send list success, length:$maxLimit \n";
                Bingo_Log::notice("send list success, length:$maxLimit");
            }
        }
    }
    return true;
}
/**
 * @desc
 * @param unknown $list
 * @param unknown $limit [每次读取的上限，数据很多时候，一次只读这么多]
 * @return multitype:
 */
function sendList($strList, $intLength)
{
    $arrInfor = Dl_Alading_Alading::rangeList($strList, 0, $intLength-1);
    if(is_array($arrInfor))
    {
        if(empty($arrInfor))
        {
            return true;
        }
        foreach ($arrInfor as $strItem) {
            $arrTransPack = unserialize($strItem);
            $arrTransPack['from_type'] = Service_Alading_Lib_VippingNew::VIPPING_FROM_TYPE_RETRY;
            $intCommandNo = isset($arrTransPack['command_no'])?intval($arrTransPack['command_no']):-1;
            $intThreadId = isset($arrTransPack['thread_id'])?intval($arrTransPack['thread_id']):0;
            $intForumId = isset($arrTransPack['forum_id'])?intval($arrTransPack['forum_id']):0;
            $intPostId = isset($arrTransPack['post_id'])?intval($arrTransPack['post_id']):0;
            $strForumName = isset($arrTransPack['forum_name'])?strval($arrTransPack['forum_name']):'';
            $vippingRet = Service_Alading_Lib_VippingNew::sendVipping($arrTransPack);
            if (false === $vippingRet) {
                echo "fail. commandNo:$intCommandNo,Tid:$intThreadId,Fid:$intForumId,Pid:$intPostId.\n";
                Bingo_Log::warning("fail. commandNo:$intCommandNo,Tid:$intThreadId,Fid:$intForumId,Pid:$intPostId.");
            } else {
                echo "success. commandNo:$intCommandNo,Tid:$intThreadId,Fid:$intForumId,Pid:$intPostId.\n";
                Bingo_Log::notice("success. commandNo:$intCommandNo,Tid:$intThreadId,Fid:$intForumId,Pid:$intPostId.");
            }
            if ($arrTransPack['ping_type'] == Service_Alading_Lib_VippingNew::PING_TYPE_GOOD){
                $arrTransPack['is_deliver'] = 1;
                $arrTransPack['is_success'] = (false === $vippingRet) ? 0 : 1;
                Tieba_Service::call('common','updateDeliverThreadPush',$arrTransPack);
            }
        }
        $result = Dl_Alading_Alading::trimList($strList, $intLength, -1);
        return $result;
    }
    else
    {
        echo "rangeList failed! list : $strList\n";
        Bingo_Log::notice("rangeList failed! list : $strList");
        return false;
    }
}
