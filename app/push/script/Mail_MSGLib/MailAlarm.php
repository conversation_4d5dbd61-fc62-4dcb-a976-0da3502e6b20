<?php
require_once('PHPMailerAutoload.php');
class MailAlarm {
	
	static $host = 'email.baidu.com';
	private $_userName = '';
	private $_passwd = ''; 
	private $_mail = null;    
	/**
	 * [__construct description]
	 * @param [type] $userName [description]
	 * @param [type] $passwd   [description]
	 * @return [type] [description]
	 */
	public function __construct($userName, $passwd){
		if (null === $this->_mail){
			$this->_mail = new PHPMailer();
		}
		$this->_mail->isSMTP();
		$this->_mail->Host = MailAlarm::$host;
		$this->_mail->SMTPAuth = true;
		$this->_mail->Username = $userName;
		$this->_mail->Password = $passwd;
		$this->_mail->SMTPSecure = 'tls';
		$this->_mail->isHTML(true);
		$this->_mail->From = $userName.'@baidu.com';
		$this->_mail->FromName = $userName;
		$this->_mail->CharSet="UTF-8";
		

	}
	/**
	 * [setSendTo description]
	 * @param [type] $arrSendTo [description]
	 * @return [type] [description]
	 */
	public function setSendTo($arrSendTo){
		foreach($arrSendTo as $sendTo){
			$this->_mail->addAddress($sendTo);
		}
	}
	/**
	 * [setCc description]
	 * @param [type] $arrCc [description]
	 * @return [type] [description]
	 */
	public function setCc($arrCc){
		foreach($arrCc as $cc){
			$this->_mail->addCC($cc);
		}
	}
	/**
	 * [addAttachment description]
	 * @param [type] $attachmentPath [description]
	 * @return [type] [description]
	 */
	public function addAttachment($attachmentPath){
		$this->_mail->addAttachment($attachmentPath);
	}
	
	/**
	 * [setSubject description]
	 * @param [type] $subject [description]
	 * @return [type] [description]
	 */
	public function setSubject($subject){
		$this->_mail->Subject = $subject;
	}

	/**
	 * [setBody description]
	 * @param [type] $body [description]
	 * @return [type] [description]
	 */
	public function setBody($body){
		$this->_mail->Body = $body;
	}
	/**
	 * [send description]
	 * @return [type] [description]
	 * @return [type] [description]
	 */
	public function send(){
		$result = array();
		$result['errno'] = 0;
		$result['errMsg'] = 'success';
		$ret = $this->_mail->send();               
		if (!$ret){
			$result = array(
				'errno' => -1,
				'errMsg' => $this->_mail->ErrorInfo,
			);
		}
		return $result;
	}
}
?>
