<?php
/**
 * @desc��ʱ�ű���ȡ�������������
 * <AUTHOR>
 * �ű���ȡ��������������redis����
 * forum_id		        ��Id
 * forum_name		����
 * cookie_num		�»�Ծ��
 * post_num	    	 ������
 * dir1		                 һ��Ŀ¼
 * dir2		                 ����Ŀ¼
 * is_star	                 ���ǰ�
 * pic_num		         ͼƬ��
 * video_num		 ��Ƶ��
 * music_num		 ������
 * mv_num		     MV��
 * good_num		         ��Ʒ��
 * member_num		 ��Ա��
 * member_name		 ��Ա��
 * manager_num		 ������
�������forum_hash���������
 */


class UpdateAldForumTopicRedis
{
	/**
	 * @desc �洢��id����ṹ
	 * @var unknown
	 */
	const REDIS_TABLE_LIST = 'forum_list';
	
	/**
	 * @desc ��forum_idΪkey�İ���Ϣ�ۺ�hash
	 * @var unknown
	 */
	const REDIS_TABLE_HASH = 'aldForumHash';
	
	const FILE_NAME = 'topic_words';
	
	/**
	 * redis��������
	 * @var unknown
	 */
	public $_redis = null;
	
	/**
	 * @desc ����redis����
	 * @throws Exception
	 */
	public function __construct()
	{
		$this->_redis = new Libs_Rpc_Redis();
		if(null == $this->_redis)
		{
			throw new Exception("get redis object failed");
		}
		$PID = 'tieba';
		$TK = 'tieba';
		$APP = 'push';
		$this->_redis = Bd_RalRpc::create('Ak_Service_Redis', array(
					'pid'=>"$PID",  // ����
					'tk'=>"$TK",    // ����
                                        'app' => "$APP", // ���û�У����Բ��ø���
                                        ));
	}
	
	
	/**
	 * @desc ��ʱ�ű�ִ��run���������ڸ���ȫ���ɵ���������
	 */
	public function run()
	{
			$fp = fopen(SCRIPT_IMPL_PATH.'/'.self::FILE_NAME,'r');
			$i=0;
			while(!feof($fp))
			{
				$i++;
				$forum_name = strval(trim(fgets($fp,1024)));
				$arr =preg_split("/\s+/",$forum_name);
				$forum_name = $arr[0];
				if(empty($forum_name))
				{
					continue;
				}
				$forum_id = (Libs_Rpc_forum::getFidByFname($forum_name));

				$this->_updateAldForumHash($forum_id);
			}
	}
	
	/**
	 * ��ȡlist��������������Ҳ���ǰ��������Ͱɵ�����
	 * @return unknown
	 */
	private function getTotalCountList()
	{
		$input = array(
				'key' => self::REDIS_TABLE_LIST,
					
		);
		$res = $this->_redis->call('LLEN',$input);
		if(null == $res ||!isset($res['ret']['forum_list'])|| intval(intval($res['ret']['forum_list']))<0)
		{
			throw new Exception("exec  getTotalCountList failed");
		}
		return  intval($res['ret']['forum_list']);
	}
	
	
	/**
	 * ������ȥȡĳ�����������
	 * @param unknown $start
	 * @param unknown $incr
	 * @return unknown
	 */
	private function getRangeFromList($start,$incr)
	{
		$input = array(
				'key'   => self::REDIS_TABLE_LIST,
				'start' => $start,
				'stop'   => $start + $incr,
		);
		$res = $this->_redis->call('LRANGE',$input);
		if(null == $res ||!isset($res['ret']['forum_list'])|| intval(intval($res['ret']['forum_list']))<0)
		{
			throw new Exception("exec  getRangeFromList failed");
		}
		return  (array)$res['ret'];
	}
	
	/**
	 * ������forum_idΪkey��forum_hash
	 * @param unknown $arrData
	 */
	public function _updateAldForumHash($forum_id)
	{
		echo $forum_id."\n";
		$input = array(
				'key'    => self::REDIS_TABLE_HASH . $forum_id,
				'field' => 'isTopicWord',
				'value' =>1,
			      );
		$res = $this->_redis->HSET($input);
		return $res;

	}
}
