<?php
ini_set('memory_limit', '2048M');
define ('BASEPATH',dirname(__FILE__));
include BASEPATH."/../../libs/util/Bos.php";
define('LOG', 'log');
define('ROOT_PATH', dirname(__FILE__) . '/../../../');
define('SCRIPT_NAME', basename(__FILE__, ".php"));
define('HADOOP', dirname(__FILE__) . '/hadoop-client/hadoop/bin/hadoop');

Bingo_Log::init(array(
    LOG => array(
        'file' => ROOT_PATH . "/log/push/script/" . SCRIPT_NAME . ".log",
        'level' => 0x0f,
    ),
), LOG);
Bingo_Log::notice('start pushHistoryThread...');

$type = $argv[1];
if ($type == 1){
    $cmd = "curl https://tieba-ares.su.bcebos.com/goodHistory.txt -O; mv goodHistory.txt goodHistory_1.txt";
    $errno = -1;
    $ret = system($cmd, $errno);
    if(false === $ret || 0 != $errno){
        Bingo_Log::warning("get file 1 failed");
        exit(1);
    }
}elseif ($type == 2){
    //hadoop客户端安装
    $cmd = 'wget -O output.tar.gz --no-check-certificate --header "IREPO-TOKEN:ca142dfe-e510-4c72-91f7-575f82609b0b" "https://irepo.baidu-int.com/rest/prod/v3/baidu/inf/hadoop-client/releases/*******/files";';
    $cmd = $cmd.'tar -zxf output.tar.gz; rm output.tar.gz;';
    $cmd = $cmd.'cd output && tar -zxf *;';
    $cmd = $cmd.'mv hadoop-client ../; cd ../';
    $ret = system($cmd, $errno);

    $date = date("Ymd", time());
    $cmd = HADOOP. ' fs -D hadoop.job.ugi="tieba-ubdev,tieba-ubdev" -D fs.default.name=hdfs://yq01-heng-hdfs.dmop.baidu.com:54310 -get /app/ns/tieba/recommend/dongxuchen/search/results/res_'.escapeshellarg($date).' ./';
    $cmd = $cmd."; rm -rf hadoop-client; rm -rf output";
    $cmd = $cmd."; mv res_".escapeshellarg($date)." goodHistory_2.txt";
    $ret = system($cmd, $errno);
    if(false === $ret || 0 != $errno){
        Bingo_Log::warning("get file 2 failed");
        exit(1);
    }
}else{
    Bingo_Log::warning("param error...");
    exit(1);
}

$date = date('Ymd', time());
$bosFileName = 'goodHistory_'.$date.'.txt';
$bosFile = fopen($bosFileName, 'a');
$filename = "./goodHistory_".$type.".txt";
$fh = fopen($filename, 'r');
if(empty($fh)){
    Bingo_Log::warning("The file not exit!");
    exit(1);
}

$threadList = array();
while (!feof($fh)){
    $line = fgets($fh);
    if ($type == 1) {
        $url = explode(" ", $line)[0];
        $tid = explode("/", $url)[2];
    }elseif ($type == 2){
        $patt = '/\s{1,}/';
        $line = preg_replace($patt, ' ', $line);
        $tid = explode(' ', $line)[2];
    }
    $threadList[] = $tid;

    if (count($threadList) < 30){
        continue;
    }

    $beginTime = microtime(true) * 1000;
    $objMulti = new Tieba_Multi('pushHistoryThread');
    foreach ($threadList as $tid){
        $arrInput = array(
            'serviceName' => 'push',
            'method' => 'pushThreadToVipping',
            'input' => array(
                'tid' => $tid,
                'level' => 1,
            ),
            'ie' => 'utf-8',
        );
        $key = 'pushThreadToVipping_'.$tid;
        $objMulti->register($key, new Tieba_Service($key), $arrInput);
    }
    $objMulti->call();
    foreach($threadList as $tid){
        $key = 'pushThreadToVipping_'.$tid;
        $arrOut = $objMulti->getResult($key);
        if(!checkOut($arrOut)){
            Bingo_Log::warning("tid:%s push to vip fail!");
        }elseif (empty($arrOut['data'])){
            $outline = "http://tieba.baidu.com/p/".$tid."\n";
            fwrite($bosFile, $outline);
        }
    }

    //控制qps
    $endTime = microtime(true) * 1000;
    $processTime = $endTime - $beginTime;
    if ($processTime < 1000){
        usleep((1000-$processTime)*1000);
    }
    $threadList = array();
}

function checkOut($arrOut){
    if(empty($arrOut) || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
        return false;
    }
    return true;
}

//文件上传Bos
$arrInput = array();
$arrInput['objName'] = $bosFileName;
$arrInput['filePath'] = BASEPATH."/".$arrInput['objName'];
$arrInput['bucketName'] = 'tieba-ares';
$arrInput['content_type'] = 'text/xml';

$arrOption = array();
$arrOption['content_type'] = 'text/xml';
$fp = fopen($arrInput['filePath'],'r');
$fileContent = fread($fp,filesize($arrInput['filePath']));
$ref = null;
$ret = libs_util_Bos::saveObjectFromString($arrInput['objName'],$fileContent,$ref,$arrInput['bucketName'] , $arrOption);

$cmd = 'rm '.$bosFileName.';rm '.$filename;
$cmd = escapeshellarg($cmd);
$ret = system($cmd, $errno);