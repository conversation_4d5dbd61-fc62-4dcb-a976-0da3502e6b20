<?php
ini_set('memory_limit', '2048M');
define ('BASEPATH',dirname(__FILE__));
define('LOG', 'log');
define('ROOT_PATH', dirname(__FILE__) . '/../../../');
define('SCRIPT_NAME', basename(__FILE__, ".php"));
define('HADOOP', dirname(__FILE__) . '/hadoop-client/hadoop/bin/hadoop');

$date = date('Ymd', time());
Bingo_Log::init(array(
    LOG => array(
        'file' => ROOT_PATH . "/log/push/script/" . SCRIPT_NAME . ".log.".$date,
        'level' => 0x0f,
    ),
), LOG);

$cmd = "wget http://tieba-ares.su.bcebos.com/tidList.txt";
system($cmd, $errno);

$filename = "./tidList.txt";
$fh = fopen($filename, 'r');
$nextdate = date('Ymd', time()+86400);
$fileDelete = './tidDelete_'.$nextdate.'.txt';
$fileNotD = './tidListNew.txt';
$dfh = fopen($fileDelete, 'a');
$nfh = fopen($fileNotD, 'a');
if(empty($fh)){
    Bingo_Log::warning("The file not exit!");
    exit(1);
}
$threadList = array();
$goodNum = 0;
while (!feof($fh)){
    $line = fgets($fh);
    $tid = str_replace(array("\r\n", "\r", "\n"), "", $line);

    $threadList [] = $tid;

    if (count($threadList) < 450){
        continue;
    }

    $tag = 0;
    $count = 0;
    $tidlist = array();
    $beginTime = microtime(true) * 1000;
    $objMulti = new Tieba_Multi('getMaskInfo');
    foreach ($threadList as $tid){
        if ($count < 30){
            $tidlist[$tag][] = $tid;
            $count++;
        }else{
            $count = 0;
            $tag++;
            $tidlist[$tag][] = $tid;
            $count++;
        }
    }
    
    foreach ($tidlist as $tag => $tlist){
        $arrInput = array(
                'serviceName' => 'post',
                'method' => 'getMaskInfo',
                'input' => array(
                    'input' => array(
                        'thread_ids' => $tlist,
                )
            ),
            'ie' => 'utf-8',
        );
        $key = "getMaskInfo_".$tag;
        $objMulti->register($key, new Tieba_Service($key), $arrInput);

    }
    
    $objMulti->call();

    foreach($tidlist as $tag => $tlist){
        $key = "getMaskInfo_".$tag;
        $arrOutT = $objMulti->getResult($key);
        foreach ($tlist as $tid){
            $deleteInfo = $arrOutT['output']['threads_mask_status'][$tid];
            $outline = $tid."\n";
            if ($deleteInfo['is_key_deleted'] || $deleteInfo['is_key_mask'] || $deleteInfo['is_key_visible']){
                fwrite($dfh, $outline);
            }else{
                fwrite($nfh, $outline);
            }
        }
    }
    $endTime = microtime(true) * 1000;
    $processTime = $endTime - $beginTime;
    if ($processTime < 1000){
        usleep((1000-$processTime)*1000);
    }
    $threadList = array();
}


$cmd = 'rm tidList.txt; mv tidListNew.txt tidList.txt; ../../../../hhvm/bin/hhvm uploadBos.php tidList.txt; ../../../../hhvm/bin/hhvm uploadBos.php tidDelete_'.$nextdate.'.txt';
$ret = system($cmd, $errno);
