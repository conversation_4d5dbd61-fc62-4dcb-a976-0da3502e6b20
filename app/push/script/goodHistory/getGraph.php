<?php
$cmd = "wget -O output.tar.gz --no-check-certificate --header \"IREPO-TOKEN:ba0f1707-1392-4f9f-a820-9e14a6bbcd7e\" \"https://irepo.baidu-int.com/rest/prod/v3/baidu/inf/afs-api/releases/1.9.3.3182/files\";";
$cmd = $cmd."tar -zxvf output.tar.gz;";
$cmd = $cmd."./output/bin/afsshell --username=tb_ubdev --password=tb_ubdev get afs://szth.afs.baidu.com:9902/user/tb_ubdev/pingo/result/funnel/query_tids  ./;";
$cmd = $cmd."cat query_tids/part* > ./queryTidList.txt;";
$ret = system($cmd, $errno);

$filename = "./queryTidList.txt";
$fh = fopen($filename, 'r');
$rfilename = "./fidGraph.txt";
$rfh = fopen($rfilename,'a');

$threadList = array();
$map = array();
$index = 0;
while (!feof($fh)){
    $line = fgets($fh);
    $line = str_replace(array("\r\n", "\r", "\n"), "", $line);
    $line = str_replace(array("\t"),"###$$",$line);
    $data = explode('###$$', $line);
    $tids = $data[1];
    $tidList = explode(',',$tids);
    if (count($tidList) < 2){
        continue;
    }
    
    foreach ($tidList as $tid){
        $map[$tid] = $index;
        $threadList[] = $tid;
    }

    $index++;

    if (count($threadList) < 1000){
        continue;
    }

    $index = 0;
    $tag = 0;
    $count = 0;
    $tidlist = array();
    $beginTime = microtime(true) * 1000;
    $objMulti = new Tieba_Multi('getInfo');
    foreach ($threadList as $tid){
        if ($count < 50){
            $tidlist[$tag][] = $tid;
            $count++;
        }else{
            $count = 0;
            $tag++;
            $tidlist[$tag][] = $tid;
            $count++;
        }
    }

    foreach ($tidlist as $tag => $tlist){
        $arrInput = array(
            'serviceName' => 'post',
            'method' => 'mgetThreadStaticInfo',
            'input' => array(
                'thread_ids' => $tlist,
            ),
            'ie' => 'utf-8',
        );
        $key = "getThreadInfo_".$tag;
        $objMulti->register($key, new Tieba_Service($key), $arrInput);
    }

    $res = $objMulti->call();

    $mapList = array();
    foreach($tidlist as $tag => $tlist){
        $key = "getThreadInfo_".$tag;
        $arrOutT = $objMulti->getResult($key);
        foreach ($tlist as $tid){
            if (!empty($arrOutT['output'][$tid])){
                $fid = $arrOutT['output'][$tid]['forum_id'];
                if (!in_array($fid, $mapList[$map[$tid]]) && $fid > 0){
                    $mapList[$map[$tid]][] = $fid;
                }
                //$outline = $fid."\n";
                //fwrite($rfh, $outline);
            }
        }
    }

    foreach($mapList as $graphList){
        sort($graphList);
        for ($i = 0; $i < count($graphList); $i++){
            for ($j = $i+1; $j < count($graphList); $j++){
                $outline = $graphList[$i]."\t".$graphList[$j]."\n";
                //echo $outline;
                fwrite($rfh, $outline);
            }
        }
    }

    $endTime = microtime(true) * 1000;
    $processTime = $endTime - $beginTime;
    if ($processTime < 1000){
        usleep((1000-$processTime)*1000);
    }
    
    $threadList = array();
    $map = array();
    $index = 0;
}

$cmd = "cat fidGraph.txt | sort | uniq -c | sort -rn | awk '{print $2,$3,$1}' > countGraph.txt;";
$cmd = $cmd."rm -rf output*;rm queryTidList.txt;rm -rf query_tids;rm fidGraph.txt;";
$ret = system($cmd, $errno);

$objdb = getDB();
if (!$objdb){
    return false;
}
$filename = "./countGraph.txt";
$fh = fopen($filename, 'r');
while (!feof($fh)){
    $line = fgets($fh);
    $data = explode(' ', $line);
    $sql = sprintf("INSERT INTO forum_graph (point_fid1,point_fid2,score) VALUES (%s,%s,%d) ON DUPLICATE KEY UPDATE score = score + %d", $data[0], $data[1], $data[2], $data[2]);
    $res = $objdb->query($sql);
    if (!$res){
        $res = $objdb->query($sql); //重试一次
    }
}

function getDB(){
    $_db = new Bd_DB();
    if ($_db == null){
        echo "get db failed.\n";
    }
    //$r = $_db->connect('10.11.80.36','root','root','forum_content','3346');
    $r = $_db->ralConnect("DB_forum_content");
    if(! $r){
        echo "db failed, bd db connect fail.\n";
        return false;
    }
    $_db->query("SET NAMES utf8mb4");
    return $_db;
}


