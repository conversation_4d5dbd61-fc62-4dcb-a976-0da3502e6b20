<?php
ini_set('memory_limit', '2048M');
define ('BASEPATH',dirname(__FILE__));
include_once(BASEPATH."/../../libs/util/Bos.php");

$arrInput = array();
$arrInput['objName'] = $argv[1];
$arrInput['filePath'] = BASEPATH."/".$arrInput['objName'];
$arrInput['bucketName'] = 'tieba-ares';
$arrInput['content_type'] = 'text/xml';
print_r($arrInput);

$arrOption = array();
$arrOption['content_type'] = 'text/xml';
$fp = fopen($arrInput['filePath'],'r');
$fileContent = fread($fp,filesize($arrInput['filePath']));
$ref = null;
$ret = libs_util_Bos::saveObjectFromString($arrInput['objName'],$fileContent,$ref,$arrInput['bucketName'] , $arrOption);
print_r($ret);

