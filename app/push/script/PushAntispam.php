<?php
/***************************************************************************
 *
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/
/**
 * @file PushAntispam.php
 * <AUTHOR>
 * @date 2020/11/13 10:35:33
 * @brief 给大搜反作弊团队提供结构化数据,
 * 共计1.3E数据，拆分5个文件进行推送
 */


require_once dirname(__file__)."/Init.php";
script::init("PushAntispam");

class PushAntispam {

    private  $_intFileIndex;
    private  $_intTotal;
    private  $_intMod;
    private  $_strRedisKeyRre = 'PushAntispam';
    private  $_strRedisKey;
    private  $_strFilenamePre = 'xx0';
    private  $_strFilename;
    private  $_strFilepath = "ftp://tieba01:<EMAIL>//home/<USER>/wangquanxiang/antispam/";
    private  $_strLocalpath;
    private  $_intCurrent = "";


    /**
     * [__construct description]
     * @param integer $templateId [description]
     */
    function __construct($index = 0,$total=1,$mod=0){

        $this->_intFileIndex = $index;
        $this->_intTotal = $total;
        $this->_intMod = $mod;

    }

    /**
     * [excute description]
     * @return [type] [description]
     * @param
     */
    public function excute(){

        $ret = $this->_load();
        if(!$ret){
            exit(-1);
        }
        $this->_process();

    }


    /**
     * [excute description]
     * @return [type] [description]
     * @param
     */
    private function _load(){

        $this->_strRedisKey = $this->_strRedisKeyRre.":".$this->_intFileIndex.":".$this->_intTotal.":".$this->_intMod;
        $this->_strFilename = $this->_strFilenamePre.$this->_intFileIndex;
        $res = Dl_Alading_Alading::get($this->_strRedisKey);
        if($res === null){
            Bingo_Log::warning("failed to call redis[Redis_forum_push] : get ".$this->_strRedisKey." is null");
            $res = Dl_Alading_Alading::get($this->_strRedisKey);
        }
        if($res === null){
            Bingo_Log::warning("failed to call redis[Redis_forum_push] : get ".$this->_strRedisKey." is null again");
            $res = 1;
        }
        //文件中的行数，用于断点续接
        $this->_intCurrent = $res;
        if($this->_intCurrent == -1){
            Bingo_Log::warning("this batch of data has been done.[".$this->_strFilename."]");
            return false;
        }

        $this->_strLocalpath = dirname(__file__).'/data/'.$this->_strFilename;
        $ftpUrl = $this->_strFilepath.$this->_strFilename;
        $bolIsDownload = true;
        $handler = opendir('data/');
        while( ($filename = readdir($handler)) !== false ) {

            if($filename == $this->_strFilename && filesize($this->_strLocalpath) != 0){
                $bolIsDownload = false;
                self::_noticeLog('PushAntispam',$this->_strFilename." has exist!");
                break;
            }
        }

        if($bolIsDownload){

            self::_noticeLog('PushAntispam',$this->_strFilename." begin download!");
            exec('wget '.escapeshellarg($ftpUrl).' -O '.escapeshellarg($this->_strLocalpath));
            self::_noticeLog('PushAntispam',$this->_strFilename." download success!");
        }

        if(filesize($this->_strLocalpath) == 0){

            Bingo_Log::warning("file is null.[".$this->_strFilename."]");
            return false;
        }
        return true;

    }


    /**
     * [excute description]
     * @return [type] [description]
     * @param
     */
    private function _process(){

        $arrTids = array();
        $i = 1;
        //一次调用mgetthread输入30个tid
        $batchTidCount = 30;
        //一次批量注册30个进行调用
        $batchMethodCount = 1;
        //ral_multi调用注册计数
        $intRalRegister = 0;
        $objRalMulti = new Tieba_Multi('antispam');

        $intTidCount = 0;
        $intMethodCount = 0;
        $intSuccess = 0;

        //文件末尾,结尾一次性提交
        $bolFlag = false;
        $bolEnd = false;
        $arrTids = array();

        $myfile = fopen($this->_strLocalpath,'r');
        self::_noticeLog('PushAntispam',$this->_strFilename."_".$this->_intTotal."_".$this->_intMod." start from $this->_intCurrent");
        echo $this->_strFilename."_".$this->_intTotal."_".$this->_intMod."\tstart:".date("Y-m-d H:i:s")."\n";

        $intStartTime = time();
        while (!($bolEnd = feof($myfile)) || !empty($arrTids )) {

            if($bolEnd && !empty($arrTids ) ){
                $bolFlag = true;
            }
            if($this->_intCurrent > $i || $i % $this->_intTotal != $this->_intMod){
                $i++;
                continue;
            }


            $line = trim(fgets($myfile));
            $tmp1 = explode('/p/',$line);
            $tmp2 = explode('?',$tmp1[1]);
            $tid = $tmp2[0];

            if($intTidCount == $batchTidCount){
                $intTidCount = 0;
                $intMethodCount ++;
            }
            if($tid){
                $arrTids[$intMethodCount][$intTidCount] = $tid;
            }


            if(($intMethodCount == $batchMethodCount - 1 && $intTidCount == $batchTidCount - 1 )|| $bolFlag){
                //注册、调用
                $intTidCount = 0;
                $intMethodCount = 0;
                Bingo_Timer::start("multi-get");
                $objMultiCall = new Tieba_Multi('post');

                $input = array(
                    "thread_ids" => $arrTids[0],
                    "need_abstract" => 1,
                    "forum_id" => 0,
                    "need_photo_pic" => 0,
                    "need_user_data" => 0,
                    "icon_size" => 0,
                    "need_mask_info" => 0,
                    "need_forum_name" => 0, //是否获取吧名
                    "call_from" => "pc_frs", //上游模块名
                );
                $arrMultiInput = array(
                    'serviceName' => 'post',
                    'method' => 'mgetThread',
                    'input' => $input,
                    'ie' => 'utf-8',
                );
                $objMultiCall->register('mgetThread',new Tieba_Service('antispam'),$arrMultiInput);

                foreach ($arrTids[0] as $key => $tid) {
                    $input = array(
                        "thread_id" => $tid, //帖子id
                        "offset" => 0,
                        "res_num" => 30,
                        "see_author" => 0,
                        "has_comment" => 1,
                        "has_mask" => 0,
                        "has_ext" => 0,
                        "need_set_pv" => 0,
                        "structured_content" => 1
                    );
                    $arrMultiInput = array(
                        'serviceName' => 'post',
                        'method' => 'getPostsByThreadId',
                        'input' => $input,
                        'ie' => 'utf-8',
                    );
                    $objMultiCall->register($tid,new Tieba_Service('antispam'),$arrMultiInput);

                }

                $objMultiCall->call();
                $arrRet = $objMultiCall->results;
                Bingo_Timer::end("multi-get");
                if(empty($arrRet)){
                    Bingo_Log::warning("failed to call service:[post], method:[mgetThread ,getPostsByThreadId:all]");
                    continue;//获取不到贴子信息
                }

                //校验接口调用成功与否
                foreach ($arrRet as $key => $methodValue) {

                    if($key == 'mgetThread'){
                        if(!isset($arrRet['mgetThread']['output']) ||  $arrRet['mgetThread']['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                            Bingo_Log::warning("failed to call service:[post], method:[mgetThread],input [".serialize($arrTids[0])."]");
                            continue;
                        }

                    }else{
                        if(!isset($arrRet[$key]['output']['output'][0]) ||  $arrRet[$key]['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                            Bingo_Log::warning("failed to call service:[post], method:[getPostsByThreadId:$key]");
                            continue;
                        }
                    }
                }

                foreach ($arrRet['mgetThread']['output']['thread_list'] as $tid => $threadInfo) {

                    $arrInput['url'] = "http://tieba.baidu.com/p/".$threadInfo['thread_id'];
                    $arrInput['page_classify'] = "forum_page";
                    $arrInput['lastmod_date'] = time();

                    $arrInput['general_data']['real_title'] = $threadInfo['title'];
                    $arrInput['general_data']['description'] = $threadInfo['abstract'];
                    $arrInput['general_data']['pub_date'] = $threadInfo['create_time'];
                    $arrInput['general_data']['lr_date'] = $threadInfo['last_modified_time'];
                    $arrInput['general_data']['pc_url'] = "http://tieba.baidu.com/p/".$threadInfo['thread_id'];
                    $arrInput['general_data']['wap_url'] = "http://tieba.baidu.com/p/".$threadInfo['thread_id'];
                    $arrInput['general_data']['from_src'] = "tieba";
                    $arrInput['general_data']['comment_count'] = $threadInfo['post_num'];
                    $arrInput['general_data']['is_deleted'] = $arrRet[$tid]['output']['output'][0]['is_thread_deleted'] ;
                    $arrInput['page_data']['article_info']['upvote_num'] = isset($threadInfo['agree_num'])?$threadInfo['agree_num']:0;
                    $arrInput['page_data']['article_info']['view_num'] = $threadInfo['freq_num'];
                    $arrInput['page_data']['article_info']['share_num'] = isset($threadInfo['share_num'])?$threadInfo['share_num']:0;
                    $arrInput['page_data']['article_info']['thread_type'] = 0;
                    $arrThreadType = Tieba_Type_Thread::getTypeArray ($threadInfo['thread_types']);
                    if(isset($arrThreadType['is_good']) && true === $arrThreadType['is_good']){
                        $arrInput['page_data']['article_info']['thread_type'] = 1;
                    }
                    $arrInput['page_data']['article_info']['community_id'] = $threadInfo['forum_id'];
                    $arrInput['page_data']['article_info']['comment_info'] = self::_buildForCommentInfo($arrRet[$tid]);

                    //接口封装
                    $data['method'] = 'run';
                    $data['params']['page']['data']['open_tieba_dump'] = '1';
                    $data['params']['page']['data']['text'] = Bingo_String::array2json($arrInput,'UTF-8');

                    $jsonInput = Bingo_String::array2json($data,'UTF-8');

                    if($intRalRegister < 30){
                        $arrMultiInput = array(
                            'caller' => new Tieba_Multi_Http(),
                            'strServer' => 'service_antispam',
                            'strUrl' => '/1',
                            'strMethod' => 'post',
                            'arrData' => $jsonInput,
                        );
                        $objRalMulti->register('antispam_'.$tid,new Tieba_Multi_Http(), $arrMultiInput);
                        $intRalRegister ++;
                    }

                    if($intRalRegister == 30){

                        $objRalMulti->call();
                        $arrRalRet = $objRalMulti->results;
                        $intRalRegister = 0;
                        $objRalMulti = new Tieba_Multi('antispam');
                        foreach ($arrRalRet as $keyRal => $valueRal) {
                            $tmp = json_decode($valueRal,true);
                            if($tmp['result']['_ret'] != 1){
                                file_put_contents($this->_strFilename.'_error.txt', $keyRal."\t".$tmp['result']['_ret']."\n",FILE_APPEND);
                                Bingo_Log::warning("failed to call ral:[service_antispam], method:[$keyRal],value:[".$valueRal.']');
                                continue;
                            }
                            $intSuccess ++;
                            if($intSuccess % 1000 == 0){

                                self::_noticeLog('PushAntispam',$this->_strFilename."_".$this->_intTotal."_".$this->_intMod." ralSunccessNo 1000");
                            }
                            Dl_Alading_Alading::set($this->_strRedisKey,$i+1);
                        }

                    }

                }
                file_put_contents($this->_strFilename."_".$this->_intTotal."_".$this->_intMod."_current.txt", $i);
            }else{
                $intTidCount ++;
            }

            //复原二维数组
            $arrTids = array();
            $bolFlag = false;

            $intEndTime = time();
            //确保每个小时重启一次脚本，防止脚本运行时间长导致异常崩掉
            if($intEndTime - $intStartTime >= 3500){
                self::_noticeLog('PushAntispam',$this->_strFilename."_".$this->_intTotal."_".$this->_intMod." end by hour!");
                exit;
            }

            if($i % 1000 == 0){
                self::_noticeLog('PushAntispam',$this->_strFilename."_".$this->_intTotal."_".$this->_intMod." lineNo $i");
            }
            $i++;

        }
        fclose($myfile);
        self::_noticeLog('PushAntispam',$this->_strFilename."_".$this->_intTotal."_".$this->_intMod." end finally!");
        Dl_Alading_Alading::set($this->_strRedisKey,-1);
        Dl_Alading_Alading::expireKey($this->_strRedisKey,604800);//一周有效期
        exec('rm '.escapeshellarg($this->_strLocalpath));
        self::_noticeLog('PushAntispam',"rm $this->_strFilename");

    }

    /**
     * brief 每个贴子获取30层回复
     * @param
     * @return
     */
    private static function _buildForCommentInfo($inputPosts){

        $arrRet = array();
        $arrPosts = $inputPosts['output']['output'][0];
        $arrTotalOutput = $arrPosts['post_infos'];
        $intLzUid = $arrPosts['first_post_userid'];
        $intForumId = $arrPosts['forum_id'];
        $arrUserLevel = array();

        //获取前30层作者在该贴所属吧的等级，若为个人中心发贴，等级为0
        if($intForumId > 0){
            $arrInput = array();
            foreach ($arrTotalOutput as $key => $value) {
                $arrInput['req'][$key]['user_id'] = $value['user_id'];
                $arrInput['req'][$key]['forum_id'] = $intForumId;

            }

            $res = Tieba_Service::call('perm', 'mgetUserForumLevel', $arrInput, null, null, 'post', 'php', 'utf-8');
            if($res === false || $res['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning("failed to call service:[perm], method:[mgetUserForumLevel],input [".serialize($arrInput)."]");
            }else{

                foreach ($res['score_info'] as $value) {
                    $arrUserLevel[$value['user_id']] = $value['level_id'];
                }

            }

        }


        foreach ($arrTotalOutput as $key => $post) {
            //var_dump($post);exit;
            $portrait = Tieba_Ucrypt::encode($post['user_id'],$post['username']);
            $arrRet[$key]['author_url'] =  "http://tieba.baidu.com/home/<USER>".$portrait;
            $arrRet[$key]['pub_data'] =  $post['now_time'];

            $arrContent = array();
            $i = 0;
            //首楼区分是否为视频类型
            if($post['post_no'] == 1 && isset($post['video_info'] )){
                $arrContent[$i]['content']['video_info']['content_url'] = $post['video_info']['origin_video_url'];
                $arrContent[$i]['content']['video_info']['content_size'] = $post['video_info']['video_size'];
                $arrContent[$i]['content']['video_info']['duration'] = $post['video_info']['video_duration'];
                $arrContent[$i]['content']['video_info']['thumbnail']['content_url'] = $post['video_info']['thumbnail_url'];
                $arrContent[$i]['content']['video_info']['encoding_format'] = $post['video_info']['video_format'];
                $arrContent[$i]['content']['type'] = 'video';
                $arrContent[$i]['type'] = 'paragraph';
                $i++;
            }else{

                foreach ($post['content'] as $content) {

                    if($content['tag'] == 'plainText'){
                        $arrContent[$i]['content']['text'] = $content['value'];
                        $arrContent[$i]['content']['type'] = 'text';
                        $arrContent[$i]['type'] = 'paragraph';
                        $i++;
                    }elseif($content['tag'] == 'img' && $content['class'] == 'BDE_Image'){
                        $arrContent[$i]['content']['content_url'] = $content['src'];
                        $arrContent[$i]['content']['height'] = intval($content['height']);
                        $arrContent[$i]['content']['type'] = 'image';
                        $arrContent[$i]['content']['width'] = intval($content['width']);
                        $arrContent[$i]['type'] = 'paragraph';
                        $i++;
                    }

                }
            }

            $arrRet[$key]['content'] =  $arrContent;
            $arrRet[$key]['floor_num'] =  $post['post_no'];
            $arrFellowList = array();
            if(isset($post['comment_info'])){
                foreach ($post['comment_info'] as $index => $comment) {
                    $portrait = Tieba_Ucrypt::encode($comment['user_id'],$post['username']);
                    $arrFellowList[$index]['author_url'] =  "http://tieba.baidu.com/home/<USER>".$portrait;
                    $arrFellowList[$index]['pub_data'] = $comment['now_time'];
                    $j = 0;
                    $arrCommentContent = array();
                    foreach ($comment['content'] as $content) {
                        if($content['tag'] == 'plainText'){
                            if($content['value'] == '回复 '){
                                continue;
                            }
                            $arrCommentContent[$j]['content']['text'] = $content['value'];
                            $arrCommentContent[$j]['content']['type'] = 'text';
                            $arrCommentContent[$j]['type'] = 'paragraph';
                            $j++;
                        }
                    }
                    $arrFellowList[$index]['content'] = $arrCommentContent;
                    if($index >= 4){//取5层楼中楼
                        break;
                    }
                }
            }

            $arrRet[$key]['follow_list'] =  $arrFellowList;
            $arrRet[$key]['is_host'] =  ($intLzUid == $post['user_id'])? 1 : 0;
            $arrRet[$key]['author_community_level'] =  isset($arrUserLevel[$post['user_id']])?$arrUserLevel[$post['user_id']]:0;
        }

        return $arrRet;

    }

    /**
     * @param unknown $scriptName
     * @param unknown $log
     * @return
     */
    public static function _noticeLog($scriptName,$log){
        $strLogPath = ROOT_PATH . "/log/app/push/".$scriptName."/".$scriptName.".log";
        @mkdir(dirname($strLogPath));
        $log = "NOTICE " . Bd_Log::genLogID()." ". strftime('%Y%m%d %H:%M:%S')." " .$scriptName.".php ".$log."\n";
        file_put_contents($strLogPath, $log, FILE_APPEND);
    }

    /**
     * 文件下载失败报警
     */
    public function sendAlarm() {
        $arrInput = array(
            'alarmTitle' => '[antispam] get '.$this->_strFilename.' failed',
            'alarmContent' => 'the len of '.$this->_strFilename.' is null',
        );
        $arrInput['emails'] = array('<EMAIL>');
        Libs_Util_Alarm::sendAlarm($arrInput);
    }

}


/**
 * [run description]
 * @return [type] [description]
 * @param int $num,int $current
 */
function run($index,$total,$mod){
    $object = new PushAntispam($index,$total,$mod);
    $object->excute();
}

//第一个参数表示文件后缀
$index = $argv[1];
//第二个参数表示并行脚本个数，用于取余
$total = $argv[2];
//第三个参数表示取余结果
$mod = $argv[3];

if ($index < 0 || $total <= 0 || $mod < 0 || $mod >= $total) {
    Bingo_Log::warning("param error");
    echo "param error";
    exit(-1);
}
//脚本执行
run($index,$total,$mod);



/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
