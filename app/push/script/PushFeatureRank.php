<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
/**
 * <AUTHOR>
 * @date 2017/11/4 16:35:26
 * @brief �������ṩ�ˡ��ɡ�����������rank����ʹ��
 * 
 **/

require_once dirname(__file__)."/Init.php";
require_once dirname(__file__)."/UserState.php";
script::init("PushFeatureRank");

/**
 * @param unknown $uid
 * @return 
 */
function run(){

	//��ȡ���������id
	$thread_key = 'push_thread_feature_list_'.date("Ymd",time()-86400);
	$res = Dl_Alading_Alading::rpopList($thread_key);
	//$res = 5419969078;
	while($res != null){
		$thread_id = intval($res);
		Bingo_Log::warning($thread_key." tid=".$thread_id);
		if ($thread_id > 0){
			$frs_res=Tieba_Service::call('push','vippingGetThreadInfo',array('thread_id'=> $thread_id));
			$str = $frs_res['data']['feature'];
			$str = str_replace("\001",'',$str);
			noticeLog("PushFeatureRank",$str);
			//Bingo_Log::notice($frs_res['data']['feature']);
			//��ȡuser_id
			$uid = $frs_res['data']['user_id'];
			if ($uid > 0){
				$arrMaskHis = UserState::getMaskHis($uid);
			    $blocked = $arrMaskHis['blocked'];
			    $deblocked = $arrMaskHis['deblocked'];
				$res=Tieba_Service::call('push','vippingGetUserInfo',array('uid'=> $uid));
				$str = $res['data'];
				$str = substr($str, 0,strlen($str)-1);
				$str = 	$str."\tuser_blocked_count=".$blocked.//�û���������
    					"\tuser_deblocked_count=".$deblocked."\n";//�û����������
				$str = str_replace("\001",'',$str);
				noticeLog("PushUserInfo",$str);
				//Bingo_Log::notice($res['data']);
			}
			//��ȡforum_id
			$forum_id = $frs_res['data']['forum_id'];
			if ($forum_id > 0){
				$frs_res=Tieba_Service::call('push','vippingGetBarInfo',array('forum_id'=> $forum_id));
				$str = $frs_res['data'];
				$str = str_replace("\001",'',$str);
				noticeLog("PushBarFeature",$str);
				//Bingo_Log::notice($frs_res['data']);
			}
		}
		$res = Dl_Alading_Alading::rpopList($thread_key);
	}

	//��ȡ���������id
	$thread_key = 'push_thread_feature_list_'.date("Ymd");
	$res = Dl_Alading_Alading::rpopList($thread_key);
	//$res = 5419969078;
	while($res != null){
		$thread_id = intval($res);
		Bingo_Log::warning($thread_key." fid=".$thread_id);
		if ($thread_id > 0){
			$frs_res=Tieba_Service::call('push','vippingGetThreadInfo',array('thread_id'=> $thread_id));
			$str = $frs_res['data']['feature'];
			$str = str_replace("\001",'',$str);
			noticeLog("PushFeatureRank",$str);
			//Bingo_Log::notice($frs_res['data']['feature']);
			//��ȡuser_id
			$uid = $frs_res['data']['user_id'];
			if ($uid > 0){
				$arrMaskHis = UserState::getMaskHis($uid);
			    $blocked = $arrMaskHis['blocked'];
			    $deblocked = $arrMaskHis['deblocked'];
				$res=Tieba_Service::call('push','vippingGetUserInfo',array('uid'=> $uid));
				$str = $res['data'];
				$str = substr($str, 0,strlen($str)-1);
				$str = 	$str."\tuser_blocked_count=".$blocked.//�û���������
    					"\tuser_deblocked_count=".$deblocked."\n";//�û����������
				$str = str_replace("\001",'',$str);
				noticeLog("PushUserInfo",$str);
				//Bingo_Log::notice($res['data']);
			}
			//��ȡforum_id
			$forum_id = $frs_res['data']['forum_id'];
			if ($forum_id > 0){
				$frs_res=Tieba_Service::call('push','vippingGetBarInfo',array('forum_id'=> $forum_id));
				$str = $frs_res['data'];
				$str = str_replace("\001",'',$str);
				noticeLog("PushBarFeature",$str);
				//Bingo_Log::notice($frs_res['data']);
			}
		}
		$res = Dl_Alading_Alading::rpopList($thread_key);
	}

	//ɾ��3��ǰ������id
	$thread_key = 'push_thread_feature_list_'.date('Ymd',time()-86400*3);
	Dl_Alading_Alading::delKey($thread_key);
	/*
	//ɾ��3��ǰ�İ�id
	$forum_key = 'push_forum_list_feature_'.date('Ymd',time()-86400*3);
	Dl_Alading_Alading::delKey($forum_key);
	//ɾ��3��ǰ���û�id
	$user_key = 'push_user_feature_list_'.date('Ymd',time()-86400*3);
	Dl_Alading_Alading::delKey($user_key);
	*/
}

run();

/**
 * @param unknown $scriptName
 * @param unknown $log
 * @return 
 */
function noticeLog($scriptName,$log){
	$strLogPath = ROOT_PATH . "/log/app/push/".$scriptName."/".$scriptName.".log";
	@mkdir(dirname($strLogPath));
	$log = "NOTICE " . Bd_Log::genLogID()." ". strftime('%Y%m%d %H:%M:%S')." " .$scriptName.".php ".$log."\n";
	file_put_contents($strLogPath, $log, FILE_APPEND);
}


exit(0);
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
