<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @date 2017/6/14
 * @brief
 */
define ('BASEPATH',dirname(__FILE__));
define ('DATAPATH',BASEPATH."/data/vipping");
require_once dirname(__file__)."/Init.php";
script::init("PingVip_addhistoryforum");
run();

/**
 *@param
 *@return
 */
function run(){
    //初始化redis 
    $redis = new Bingo_Cache_Redis('twlive');
    if (null == $redis){
        echo 'call redis fail.';
        //redis初始化失败，记日志
        Bingo_Log::warning("call redis failed!!");
        exit();
    }
    $key_list_prefix = 'push_history_frs_list_';
    $last_forumid_key = 'push_history_last_forumid';
    $date = date("Ymd");
    $forum_cnt = getHistoryForumCnt();
    $file_path = 'app/push/data/forum_history_id.txt';
    $fp = fopen($file_path, 'r');
    if($fp==false){
        echo "forum id file not exist!\n";
        exit();
    }
    $forum_id = 0;
    $last_forum_id = 0;
    
    $input = array(
        'key' => $last_forumid_key,
    );
    $redis_ret = redis_recall($redis,'GET',$input);
    if( !isset($redis_ret['err_no']) || $redis_ret['err_no'] != 0  ) {
        Bingo_Log::warning("SET redis error. arrRes:[".serialize($redis_ret)."] arrReq:[".serialize($input)."]");
        continue;
    }
    if(!empty($redis_ret['ret'][$input['key']])){
        $last_forum_id = intval($redis_ret['ret'][$input['key']]);
    }
    echo "get last forum id:\n".$last_forum_id."\n";
    if($last_forum_id != 0){
        while(!feof($fp)){
            $forum_id = fgets($fp);
            if(intval(trim($forum_id)) == $last_forum_id){
                break;
            }
        }
    }
    
    $cnt_hit = 0;
    while(!feof($fp)){
        if($forum_cnt-- <= 0 ){
            $cnt_hit = 1;
            break;
        }
        $forum_id = fgets($fp);
        if(empty($forum_id)){
            continue;
        }
        $forum_id = intval(trim($forum_id));
        $input = array(
            'key' => $key_list_prefix.$date,
            'value' => $forum_id,
        );
        $redis_ret = redis_recall($redis,'LPUSH',$input);
        if( !isset($redis_ret['err_no']) || $redis_ret['err_no'] != 0  ) {
            Bingo_Log::warning("LPUSH redis error. arrRes:[".serialize($redis_ret)."] arrReq:[".serialize($input)."]");
            continue;
        }
    }
    
    echo "last forum_id:\n".$forum_id."\n";
    $input = array(
        'key' => $last_forumid_key,
        'value' => strval($forum_id),
    );
    $redis_ret = redis_recall($redis,'SET',$input);
    if( !isset($redis_ret['err_no']) || $redis_ret['err_no'] != 0  ) {
        Bingo_Log::warning("SET redis error. arrRes:[".serialize($redis_ret)."] arrReq:[".serialize($input)."]");
        continue;
    }
    fclose($fp);
}


/**
 * 判断本吧是否开放
 * @param  [type]  $intForumId [description]
 * @return boolean             [description]
 */
function getHistoryForumCnt() {
    $handleWordServer = Wordserver_Wordlist::factory();
    $strTableName = 'tb_wordlist_redis_pushconfig';
    $arrInput = array('frs_push_history_cnt');
    $arrConf = $handleWordServer->getValueByKeys($arrInput, $strTableName);
    return intval($arrConf['frs_push_history_cnt']);
}

/**
 *@desc 带重试次数的redis调用
 *@param
 *@return
 */
function redis_recall($redis,$method,$input){
    $ret = $redis->$method($input);
    $retry = 0;
    while($retry < 3 && ($ret['err_no'] != 0 || !$ret)){
        sleep(1);
        $retry++;
        $ret = $redis->$method($input);
    }
    return $ret;
}

?>
