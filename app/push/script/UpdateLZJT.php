<?php

// 楼主静态化

/**
 *
 * <AUTHOR> @desc
 */
define ('BASEPATH',dirname(__FILE__));
define ('DATAPATH',BASEPATH."/data/vipping");
require_once dirname(__file__)."/Init.php";
require_once dirname(__file__)."/Mail_MSGLib/SendMail.php";

script::init("UpdateLZJT");

// 地址文件
define ( 'SCRIPT_DATA_PATH', BASEPATH);
define ( 'TDK_MULTI_FLAG' , "lzjt_multi_flag");

// 加载excel库
echo date('H:i:s')," ROOT_PATH:".ROOT_PATH."\n";
echo date('H:i:s')," BASE_PATH:".BASE_PATH."\n";

include_once BASEPATH . '/excellib/PHPExcel.php';
include_once BASEPATH . '/excellib/PHPExcel/IOFactory.php';

$requestTime = gettimeofday ();
if (! defined ( 'REQUEST_ID' )) {
    define ( 'REQUEST_ID', (intval ( $requestTime ['sec'] * 100000 + $requestTime ['usec'] / 10 ) & 0x7FFFFFFF) );
}

if (function_exists ( 'camel_set_logid' )) {
    camel_set_logid ( REQUEST_ID );
}


$totalStartTime = microtime(true);

// 1. 读取词表
$handleWordServer = Wordserver_Wordlist::factory();
$intCount = 10;
$arrParam = array(
    'table' => 'tb_wordlist_redis_lzjt_cibiao', // 单条数据
    'start' => 0,
    'stop'  => $intCount,
);

$arrRet = $handleWordServer->getTableContents($arrParam);
if ($arrRet == false || $arrRet['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
    echo "get wordlist fail.\n [".json_encode($arrRet)."]";
    exit(1);
}

// 获取处理标志
$arrFlag = array();
getMultiFlag($arrFlag);

// mkdir
@exec("mkdir -p ".SCRIPT_DATA_PATH); 

foreach ($arrRet['ret'] as $key=>$value) {
    if ($arrFlag[$key] == 2) {
        echo "all ready done.[$key]\n";
        continue;
    } 

    echo "key:$key,value:".json_encode($value)."\n";
    setDoingFlag($key);
    $strUrl = $value;
    if (strlen($strUrl) < 5) {
        echo "url path error.[$strUrl]\n";
        continue;
    }
    // 下载文件
    $option  = array(
        'max_response_size' => 10485760, //10M
    );
    $objFetch = Orp_FetchUrl::getInstance($option);
    $strContent = $objFetch->get($strUrl);

    // 保存文件
    echo "url:[$strUrl]\n";
    $strPath = SCRIPT_DATA_PATH."/".basename($strUrl);
    file_put_contents($strPath,$strContent);
    echo "save file in [$strPath]\n";

    $arrData = array();
    // 解析文件
    loadExcel($strPath,$arrData);
    // var_export($arrData);
    // 存储
    foreach ($arrData as $tdkItem) {
        echo ".";
        $arrParam = array(
            'thread_id'=>intval($tdkItem['A']),
            'forum_id'=>intval($tdkItem['B']),
            'page_title'=>strval($tdkItem['C']),
            'page_desc'=>strval($tdkItem['D']),
            'page_kws'=>strval($tdkItem['E']),
        );
        usleep(50000); // 50 ms
        $arrRet = Tieba_Service::call('aggregate', 'addConf', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (false == $arrRet || $arrRet['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            echo(sprintf("call aggregate::addConf fail.[%s] [%s]\n",serialize($arrParam),serialize($arrRet)));
            usleep(50000); // 50 ms
            $arrRet = Tieba_Service::call('aggregate', 'addConf', $arrParam, null, null, 'post', 'php', 'utf-8');
            if (false == $arrRet || $arrRet['errno']!==Tieba_Errcode::ERR_SUCCESS) {
                echo(sprintf("call aggregate::addConf fail final.[%s] [%s]\n",serialize($arrParam),serialize($arrRet)));
                continue;
            }
        }

        // 推送
        $url = "http://tieba.baidu.com/t/l/".$arrParam['thread_id'];
        $arrTransPack = array(
            'command_no' => 9999999,
            'url' => $url,
            'push_level' => 4,
            'op_type' => 'ADD',
            'from_type' => 99,
            'thread_id' => 99999,
        );
        $arrRet = Service_Alading_Lib_VippingNew::sendVipping($arrTransPack);
        if (false == $arrRet) {
            echo(sprintf("call push fail.[%s] [%s]\n",serialize($arrParam),serialize($arrRet)));
            usleep(50000); // 50 ms
            $arrRet = Service_Alading_Lib_VippingNew::sendVipping($arrTransPack);
            if (false == $arrRet) {
                echo(sprintf("call push fail final.[%s] [%s]\n",serialize($arrParam),serialize($arrRet)));
                continue;
            }
        }
    }

    // 删除文件
    @exec("rm $strPath");
    setDoneFlag($key);
}
$totalEndTime = microtime(true);
echo 'total cost time: ' , sprintf('%.4f',$callTime) , " seconds\n";
echo "all done.\n";
exit(0);

/**
 * @param
 * @return 
 */
function getMultiFlag(&$arrFlag) {
    $arrRedisParam = array(
        "call" => "HGETALL",
        "param" => array(
            'key'=>TDK_MULTI_FLAG,
        ),
    );
    $redisRet = Tieba_Service::call('push', 'redisQuery', $arrRedisParam, null, null, 'post', 'php', 'gbk');
    if (false == $redisRet) {
        echo "redis query fail.\n";
        return false;
    }
    foreach ($redisRet['data'][TDK_MULTI_FLAG] as $key =>$value) {
        $arrFlag[$value['field']] = $value['value'];
    }
    // var_export($arrFlag);
}

/**
 * @param
 * @return
 */
function setDoingFlag($key) {
    $arrRedisParam = array(
        'call'=>'HSET',
        'param' => array(
            'key' => TDK_MULTI_FLAG,
            'field' => $key,
            'value' => 1,
        )
    );
    $redisRet = Tieba_Service::call('push', 'redisQuery', $arrRedisParam, null, null, 'post', 'php', 'gbk');
    // var_export($redisRet);
    if (false == $redisRet ) {
        echo "fail to set redis [".serialize($arrRedisParam)."] [".json_encode($redisRet)."]\n";
    }
}

/**
 * @param
 * @return 
 */
function setDoneFlag($key) {
    $arrRedisParam = array(
        'call'=>'HSET',
        'param' => array(
            'key' => TDK_MULTI_FLAG,
            'field' => $key,
            'value' => 2,
        )
    );
    // var_export($arrRedisParam);
    $redisRet = Tieba_Service::call('push', 'redisQuery', $arrRedisParam, null, null, 'post', 'php', 'gbk');
    if (false == $redisRet ) {
        echo "fail to set redis [".serialize($arrRedisParam)."] [".json_encode($redisRet)."]\n";
        $redisRet = Tieba_Service::call('push', 'redisQuery', $arrRedisParam, null, null, 'post', 'php', 'gbk');
    }
}

/**
 * 读取excel
 * @param
 * @param
 * @return 
 */
function loadExcel($strPath,&$arrData) {
    // echo date('H:i:s') , " Load from Excel2007 file\n";
    $callStartTime = microtime(true);
    $objPHPExcel = PHPExcel_IOFactory::load($strPath);
    $callEndTime = microtime(true);
    $callTime = $callEndTime - $callStartTime;
    echo 'Call time to read Workbook was ' , sprintf('%.4f',$callTime) , " seconds\n";
    // Echo memory usage
    // echo date('H:i:s') , ' Current memory usage: ' , (memory_get_usage(true) / 1024 / 1024) , " MB\n";

    // 读取内容
    $objPHPExcel->setActiveSheetIndex(0);
    // 遍历
    $objWorksheet = $objPHPExcel->getActiveSheet();
    $arrData = $objPHPExcel->getActiveSheet()->toArray(null,false,false,true); 
    // var_export($arrData);
}
