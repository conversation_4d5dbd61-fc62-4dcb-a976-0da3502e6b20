<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file PushHighQualityTop4.php
 * <AUTHOR>
 * @date 2018/7/27 10:46:20
 * @brief 优质前四存量pb推送，约5亿优质贴
 */
require_once dirname(__file__)."/../star/Base.php";
require_once dirname(__file__)."/../Init.php";
script::init("PushHighQualityTop4");

class PushHighQualityTop4 extends Base{
    
    private $_num;
    private $_current;


    /**
     * [__construct description]
     * @param integer $templateId [description]
     */
    function __construct($num =0,$current=0){
        $this->_num = $num;
        $this->_current = $current;
    }

    /**
     * [excute description]
     * @return [type] [description]
     * @param
     */
    public function excute(){

        $post_data = array();
        
        //测试接口
        //$url = 'http://bjyz-charyxiao.epc.baidu.com:8080/ProEntryService/ProEntry';
        //测试账号
        /*$post_data['header']['user_id'] = 200;
        $post_data['header']['user_password'] = "password";*/

        
        //线上账号
        $post_data['header']['user_id'] = 1008;
        $post_data['header']['user_password'] = "D1bItzJy";

        $post_data['body']['op_type'] = 1;
        $post_data['body']['url'] = "";
        $post_data['body']['json_data'] = "";

        //获取优质贴子id
        if($this->_num === null){
            Bingo_Log::warning("param is error: num is null!");
            exit;
        }

        $strFileName = "x00".$this->_num;
        $ftpUrl = "ftp://tc-forum-cupid01.tc.baidu.com:/home/<USER>/ftp/wangquanxiang/".$strFileName;
        $bolIsDownload = true;
        $handler = opendir(dirname(__file__));
        while( ($filename = readdir($handler)) !== false ) {
            if($filename == $strFileName){
                $bolIsDownload = false;
                self::_noticeLog('PushHighQualityTop4',$strFileName." has exist!");
                break;
            }
        }

        if($bolIsDownload){
            $cmd = "wget $ftpUrl -O $strFileName";
            self::_noticeLog('PushHighQualityTop4',$strFileName." begin download!");
            exec($cmd);
            self::_noticeLog('PushHighQualityTop4',$strFileName." download success!");
        }
        
        $arrTids = array();
        if(isset($this->_current)){
            $intCurrent = $this->_current;
        }else{
            $myfile = fopen($strFileName."_current.txt",'r');
            $intCurrent = fgets($myfile);
            fclose($myfile);
        }
        
        if($intCurrent == 0){
            $intCurrent = 1;
        }
        //文件中的行数，用于断点续接
        $i = 1;
        //一次调用mgetthread输入80个tid,arch同学说最大为100
        $batchTidCount = 80;
        //一次批量注册30个进行调用
        $batchMethodCount = 30;
        //ral_multi调用注册计数
        $intRalRegister = 0;
        $objRalMulti = new Tieba_Multi('highQuality');

        $intTidCount = 0;
        $intMethodCount = 0;
        $intSuccess = 0;
        if(filesize($strFileName) == 0){
            //self::_noticeLog('PushHighQualityTop4',$strFileName." is null!");
            Bingo_Log::warning($strFileName." is null!");
            exit;
        }
        $myfile = fopen($strFileName,'r');
        self::_noticeLog('PushHighQualityTop4',$strFileName." start from $intCurrent");
        echo $strFileName."\tstart:".date("Y-m-d H:i:s")."\n";
        $intStartTime = time();
        while (!feof($myfile)) {
            $line = trim(fgets($myfile));
            if($intCurrent > $i){
                $i++;
                continue;
            }

            if(false !== strpos($line,'tieba.baidu.com/p/')){
                //echo $line."\n";
                //去除query
                $tmp = explode(" ", $line);
                $tmp = explode('?', trim($tmp[0]));
                $uri = $tmp[0];
                //获取tid
                $tmp = explode('tieba.baidu.com/p/', $uri);
                $tid = end($tmp); 
                if($intTidCount == $batchTidCount){
                    $intTidCount = 0;
                    $intMethodCount ++;
                   /* if($intMethodCount = $batchMethodCount){
                        $intMethodCount = 0;
                    }*/
                }
                
                $arrTids[$intMethodCount][$intTidCount] = $tid;

                if($intMethodCount == $batchMethodCount - 1 && $intTidCount == $batchTidCount - 1){
                    //注册、调用
                    $intTidCount = 0;
                    $intMethodCount = 0;
                    Bingo_Timer::start("multi-mgetThread");  
                    $objMultiCall = new Tieba_Multi('post');
                    foreach ($arrTids as $key => $arrTid) {
                        
                        $input = array(
                            "thread_ids" => $arrTid,
                            "need_abstract" => 0,
                            "forum_id" => 0,
                            "need_photo_pic" => 0,
                            "need_user_data" => 0,
                            "icon_size" => 0,
                            "need_mask_info" => 0,
                            "need_forum_name" => 0, //是否获取吧名
                            "call_from" => "pc_frs", //上游模块名
                        );
                        $arrMultiInput = array(
                            'serviceName' => 'post',
                            'method' => 'mgetThread',
                            'input' => $input,
                            'ie' => 'utf-8',
                        );
                        $objMultiCall->register('mgetThread_'.$key,new Tieba_Service('highQualityTop4'),$arrMultiInput);

                    }

                    $objMultiCall->call();
                    $arrRet = $objMultiCall->results;
                    Bingo_Timer::end("multi-mgetThread");
                    if(empty($arrRet)){
                        Bingo_Log::warning("failed to call service:[post], method:[mgetThread :all]");
                        return false;//获取不到贴子信息
                    }

                    foreach ($arrRet as $key => $methodValue) { 
                        
                        if(!isset($arrRet[$key]['output']) ||  $arrRet[$key]['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                            Bingo_Log::warning("failed to call service:[post], method:[$key]");
                            continue;
                        }
                        foreach ($methodValue['output']['thread_list'] as $tid => $threadInfo) {
                            
                            $arrInput['url'] = "http://tieba.baidu.com/p/".$threadInfo['thread_id'];
                            $arrInput['page_classify'] = "forum_page";
                            $arrInput['lastmod_date'] = time();
                            $arrInput['general_data']['comment_count'] = $threadInfo['post_num'];
                            $arrInput['general_data']['from_src'] = "tieba";
                            $arrInput['article_info']['upvote_num'] = isset($threadInfo['agree_num'])?$threadInfo['agree_num']:0;
                            $arrInput['article_info']['view_num'] = $threadInfo['freq_num'];
                            $arrInput['article_info']['share_num'] = isset($threadInfo['share_num'])?$threadInfo['share_num']:0;
                            $post_data['body']['json_data'] = Bingo_String::array2json($arrInput);
                            $post_data['body']['url'] = "tieba.baidu.com/p/".$threadInfo['thread_id'];
                            
                            /*$header = array(
                                'pathinfo'   => "/ProEntryService/ProEntry",
                                //'content-type'=>"text/json",
                                'encoding' => '', 
                                'Expect' => '',
                            );*/
                            //$extra = array();
                            

                            $jsonInput = Bingo_String::array2json($post_data);
                           
                            if($intRalRegister < 30){
                                $arrMultiInput = array(
                                    'caller' => new Tieba_Multi_Http(),
                                    'strServer' => 'service_high_quality',
                                    'strUrl' => '/ProEntryService/ProEntry',
                                    'strMethod' => 'post',
                                    'arrData' => $jsonInput,
                                );
                                $objRalMulti->register('highQuality_'.$tid,new Tieba_Multi_Http(), $arrMultiInput);
                                $intRalRegister ++;
                            }
                           
                            if($intRalRegister == 30){
                                $objRalMulti->call();
                                $arrRalRet = $objRalMulti->results;
                                $intRalRegister = 0;
                                $objRalMulti = new Tieba_Multi('highQuality');
                                foreach ($arrRalRet as $keyRal => $valueRal) {
                                    $tmp = json_decode($valueRal,true);
                                    if($tmp['err_no'] != 'SUCCESS'){
                                        file_put_contents($strFileName.'_error.txt', $keyRal."\t".$tmp['err_no']."\n",FILE_APPEND);
                                        Bingo_Log::warning("failed to call ral:[service_high_quality], method:[$keyRal],value:[".$tmp['err_no'].']');
                                        continue;
                                    }
                                    $intSuccess ++;
                                    if($intSuccess % 1000 == 0){
                                        self::_noticeLog('PushHighQualityTop4',$strFileName." ralSunccessNo 1000");
                                    }
                                    
                                }
                                
                                
                            }
                            
                            

                        }
                       
                    }
                    
                    
                    file_put_contents($strFileName."_current.txt", $i);
                }else{
                    $intTidCount ++;
                }
                $intEndTime = time();
                //确保每个小时重启一次脚本，防止脚本运行时间长导致异常崩掉
                if($intEndTime - $intStartTime >= 3400){
                    self::_noticeLog('PushHighQualityTop4',$strFileName." end by hour!");
                    exit; 
                }
            }
            $i++;
            if($i % 10000 == 0){
                self::_noticeLog('PushHighQualityTop4',$strFileName." lineNo $i");
            }
             
        }
        fclose($myfile);
        self::_noticeLog('PushHighQualityTop4',$strFileName." end finally!");
       

    }

    /**
     * @param unknown $scriptName
     * @param unknown $log
     * @return 
     */
    public static function _noticeLog($scriptName,$log){
        $strLogPath = ROOT_PATH . "/log/app/push/".$scriptName."/".$scriptName.".log";
        @mkdir(dirname($strLogPath));
        $log = "NOTICE " . Bd_Log::genLogID()." ". strftime('%Y%m%d %H:%M:%S')." " .$scriptName.".php ".$log."\n";
        file_put_contents($strLogPath, $log, FILE_APPEND);
    }

}



/**
 * [run description]
 * @return [type] [description]
 * @param int $num,int $current
 */
function run($num,$current){
    $object = new PushHighQualityTop4($num,$current);
    $object->excute();
}

//第一个参数为切分后的文件表示，如x00{$num}
$num = $argv[1];
//第二个参数用来指定从文本的某一行开始进行读取
$current = $argv[2];
//脚本执行
run($num,$current);



/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
