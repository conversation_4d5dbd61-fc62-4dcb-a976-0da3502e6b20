<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
/**
 * <AUTHOR>
 * @date 2019/12/03 14:49:26
 * @brief 逐年遍历历史贴，推送add入库
 * 
 **/

require_once dirname(__file__)."/Init.php";
script::init("PushHisAddCcdb");

define("TMP_PATH",dirname(__file__)."/tmp/");

/**
 * @param unknown $uid
 * @return 
 **/
function run($strPath,$strFileName,$from){
   
    $ftpUrl = "ftp://cp01-tieba-ftp01.cp01.baidu.com/home/<USER>/wangquanxiang/unMask/output/".$strPath.$strFileName;
    $redisKey = "PushHisAddCcdb_".$strFileName;
    if($from == 0){

        $res = Dl_Alading_Alading::get($redisKey);
        if($res === null){
            Bingo_Log::warning("failed to call redis[Redis_forum_push] : get ".$redisKey." is null");
            $res = Dl_Alading_Alading::get($redisKey);
        }
        if($res === null){
            Bingo_Log::warning("failed to call redis[Redis_forum_push] : get ".$redisKey." is null again");
            $res = 1;
        }
    }else{
        $res = $from;
    }
    //文件中的行数，用于断点续接
    $intCurrent = $res;
    if($intCurrent == -1){
        Bingo_Log::warning("this batch of data has been done");
        exit(0);
    }

    $bolIsDownload = true;
    $handler = opendir(dirname(__file__));
    while( ($filename = readdir($handler)) !== false ) {
        if($filename == $strFileName && filesize($strFileName) != 0){
            $bolIsDownload = false;
            _noticeLog('PushHisAddCcdb',$strFileName." has exist!");
            break;
        }
    }

    if($bolIsDownload){
        $cmd = "wget $ftpUrl -O $strFileName";
        _noticeLog('PushHisAddCcdb',$strFileName." begin download!");
        exec($cmd);
        _noticeLog('PushHisAddCcdb',$strFileName." download success!");
    }
    
    if(filesize($strFileName) == 0){
        _sendAlarm($strFileName);
        echo $strFileName." is null!";
        Bingo_Log::warning($strFileName." is null!");
        exit(-1);
    }
    $myfile = fopen($strFileName,'r');
    _noticeLog('PushHisAddCcdb',$strFileName." start from $intCurrent");
    echo $strFileName."\tstart:".date("Y-m-d H:i:s")."\n";


    $intStartTime = time();
    while(!feof($myfile)){
        $intThreadId = trim(strval(fgets($myfile)));
        if($i < $intCurrent){
            $i++;
            continue;
        }
        
        $url = 'http://tieba.baidu.com/p/'.$intThreadId; 
        $arrTransPack = array(
            'command_no' => 9999999,
            'url' => $url,
            'push_level' => 0,
            'op_type' => 'ADD',
            'from_type' => 999,
            'thread_id' => 999998,
        );

        $vippingRet = Service_Alading_Lib_VippingNew::sendVipping($arrTransPack);
        
        if(false === $vippingRet) 
        {
            file_put_contents($strFileName.'_error.txt', $intThreadId,FILE_APPEND);
        }
        
        Dl_Alading_Alading::set($redisKey,$i+1);
        $i++;
        if($i % 100 == 0){
            _noticeLog('PushHisAddCcdb',$strFileName." lineNo $i");
        }

        $intEndTime = time();
        //确保每个小时重启一次脚本，防止脚本运行时间长导致异常崩掉
        if($intEndTime - $intStartTime >= 3400){
            _noticeLog('PushHisAddCcdb',$strFileName." end by hour!");
            exit(0); 
        }

    }
    fclose($myfile);
    _noticeLog('PushHisAddCcdb',$strFileName." end finally!");
    Dl_Alading_Alading::set($redisKey,-1);
    Dl_Alading_Alading::expireKey($redisKey,5184000);
    system("rm $strFileName");
    _noticeLog('PushHisAddCcdb',"rm $strFileName");
    
}

    /**
     * @param unknown $scriptName
     * @param unknown $log
     * @return 
     */
    function _noticeLog($scriptName,$log){
        $strLogPath = ROOT_PATH . "/log/app/push/".$scriptName."/".$scriptName.".log";
        @mkdir(dirname($strLogPath));
        $log = "NOTICE " . Bd_Log::genLogID()." ". strftime('%Y%m%d %H:%M:%S')." " .$scriptName.".php ".$log."\n";
        file_put_contents($strLogPath, $log, FILE_APPEND);
    }

    /**
     * 文件下载失败报警
     */
    function _sendAlarm($strFileName) {
        $arrInput = array(
            'alarmTitle' => '[历史贴推送] get '.$strFileName.' failed',
            'alarmContent' => 'the len of file is null',
        );
        Libs_Util_Alarm::sendAlarm($arrInput);
    }


//文件地址
$strPath = strval($argv[1]);
//文件名称
$strFileName = strval($argv[2]);
//从哪行开始跑
$from = intval($argv[3]);

if (empty($strPath) || empty($strFileName )|| $from < 0 ) {
    Bingo_Log::warning("param error");
    exit(-1);
}


run($strPath,$strFileName,$from);
exit(0);
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
