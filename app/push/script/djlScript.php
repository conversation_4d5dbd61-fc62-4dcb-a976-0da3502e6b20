<?php

/**
 * 执行方式形如： 
 * ~/orp/hhvm/bin/hhvm djlScript.php medical 10
 * ~/orp/hhvm/bin/hhvm djlScript.php game 5
 * 
 */

define('BASEPATH', dirname(__FILE__));
require_once BASEPATH . "/../dl/alading/Alading.php";
require_once dirname(__file__) . "/Init.php";
script::init("djlScript");

class Dajialiao
{
    const DANBA = "danba";
    const DUOBA = "duoba";
    const BOS_PRODUCT = 'tieba-ares';

    const GAME_INDEX = 'djl:game:index';
    const MEDICAL_INDEX = 'djl:medical:index';
    const GAME_SETNX_KEY = 'djl:game:setnx_index';
    const MEDICAL_SETNX_KEY = 'djl:medical:setnx_index';

    const TYPE_GAME = 'game';
    const TYPE_MEDICAL = 'medical';

    // 多少个query合并生成一个xml，不考虑单个query内容挖掘失败，xml校验失败情形。
    // 1500 个query 合并生成一个 xml 的文件有点大，弄成 1000 试一试
    const QUERY_NUM_OF_EACH_XML = 1000;

    // 存放游戏垂类query的list
    // const DJL_GAME_QUERYLIST_KEY = 'DAJIALIAO_LIST:' . self::TYPE_GAME;
    const DJL_GAME_QUERYLIST_KEY = 'DAJIALIAO_LIST:game';
    // 存放医疗垂类query的list
    // const DJL_MEDICAL_QUERYLIST_KEY = 'DAJIALIAO_LIST:' . self::TYPE_MEDICAL;
    const DJL_MEDICAL_QUERYLIST_KEY = 'DAJIALIAO_LIST:medical';


    // 脚本执行时用户指定的垂类后，相关的参数
    private static $_resourceCategroy = '';
    // 对于指定的 category，xml 分片数量
    private static $_xmlPageNum = 0;
    private static $_setnxKey = '';
    private static $_indexKey = '';
    private static $_listKey = '';

    // Note: 这个参数从php命令行获取，如果没有设置，则默认为15
    // 代表调用内容挖掘服务的并发度，如果该值小于 1 或者为负数代表为串行调用内容挖掘服务
    private static $_forumMiningConcurrency = 15;

    public function __construct($category, $concurrency = 15)
    {
        self::_init($category, $concurrency);
    }

    public static function buildSitemapXml($category, $is_multi_forum, $xmlNum)
    {
        $sitemapArr = array();
        $badir = self::DANBA;
        if ($is_multi_forum) {
            $badir = self::DUOBA;
        }
        for ($index = 0; $index < $xmlNum; $index++) {
            $sitemapArr['sitemap'][] = array(
                "loc" => "http://tieba-ares.su.bcebos.com/djl/$badir/$category-data_$index.xml",
                "lastmod" => date("Y-m-d"),
            );
        }

        // var_export($sitemapArr);
        $xml = Libs_Util_Array2xml::createXML('sitemapindex', $sitemapArr);
        $xmldata = $xml->saveXML();
        // var_export($xmldata);

        $objFileName = "djl/$badir/$category-sitemap.xml";
        self::saveXmlToBos($objFileName, $xmldata);
    }

    public static function saveXmlToBos($objFileName, $xmldata)
    {
        $ref = null;
        $bosRet = libs_util_Bos::saveObjectFromString($objFileName, $xmldata, $ref, self::BOS_PRODUCT, array('content_type' => 'text/xml'));

        if ($bosRet !== true) {
            Bingo_Log::warning(sprintf("objFileName: $objFileName, ref: %s, bosRet: %s", var_export($ref, true), var_export($bosRet, true)));
        } else {
            Bingo_Log::notice(sprintf("objFileName: $objFileName, succ"));
        }
        // libs_util_Bos::delObject("djl/danba/game_sitemap.xml", 'tieba-ares');
    }

    /**
     * 线下开发测试使用，只输出xml，不把xml推送到线上。
     */
    public function genXmlTest($wordList)
    {
        self::Process($wordList, 20, true);
        // self::Process($wordList, 20, false);
    }

    /**
     * 单独更新某个 index 单吧和多吧xml
     */
    public function buildIndexXml($index, $is_multi_forum)
    {
        $wordList = $this->redisRangeByIndex($index);
        // $wordList = array(
        //     '高血压_测试数据' => '高血压',
        //     '1型糖尿病_测试数据' => '1型糖尿病',
        //     '艾滋病_测试数据' => '艾滋病',
        // );
        self::Process($wordList, $index);
        // exit;
    }

    /**
     * 根据index获取对应垂类的query列表
     */
    public function redisRangeByIndex($index)
    {
        $listKey = self::$_listKey;
        $start = self::QUERY_NUM_OF_EACH_XML * $index;
        if ($index + 1 == self::$_xmlPageNum) {
            $end = -1;
        } else {
            $end = $start + self::QUERY_NUM_OF_EACH_XML - 1;
        }
        $wordList = Dl_Alading_Alading::rangeList($listKey, $start, $end);

        // var_export($wordList);
        Bingo_Log::notice("listKey: $listKey, index: $index, start: $start, end: $end. range return num: " . count($wordList));
        return $wordList;
    }

    public function getIndex($category)
    {
        $setnxKey = self::$_setnxKey;
        $indexKey = self::$_indexKey;

        $res = Dl_Alading_Alading::setnx($setnxKey, 'val');
        if ($res === false) {
            // 获取分布式锁失败
            return false;
        }

        $index = Dl_Alading_Alading::get($indexKey);
        if (is_null($index)) {
            $index = 0;
        } else {
            $index = intval($index);
        }

        $nextIndex = ($index + 1) % self::$_xmlPageNum;
        // 设置下一次更新xml文件的index信息。
        Dl_Alading_Alading::set($indexKey, $nextIndex);
        Dl_Alading_Alading::delKey($setnxKey);
        return $index;
    }

    /**
     * 注意：如果参数 isGenXmlTest 为真，那么只单纯在控制台输出xml信息。这个参数只应用来再线下测试使用。
     */
    private static function Process($allWordList, $index, $isGenXmlTest = false)
    {
        $succDanbaKeyNum = 0;  // 单吧每个 index 索引建库数量
        $succDuobaKeyNum = 0;  // 多吧每个 index 索引建库数量
        $danbaXml = '';
        $duobaXml = '';
        $mwordArr = array();
        foreach ($allWordList as $_key => $word) {
            if (self::$_forumMiningConcurrency > 1) {
                // 并行调用内容挖掘服务
                // $mwordArr[$_key] = $word;
                $mwordArr[] = $word;
                if (count($mwordArr) < self::$_forumMiningConcurrency) {
                    continue;
                }

                $arrMultiOut = self::mgetData($mwordArr);
                foreach ($arrMultiOut as $keyWord => $arrData) {
                    $itemXml = self::getXmlData($keyWord, $arrData, $succDanbaKeyNum, $succDuobaKeyNum);
                    $danbaXml = $danbaXml . $itemXml['single'];
                    $duobaXml = $duobaXml . $itemXml['multi'];
                }

                $mwordArr = array();
            } else {
                // 串行调用
                $input =  array(
                    'query' => $word,
                );
                $arrData = Tieba_Service::call('push', 'forumMining', $input, null, null, 'get', 'php');

                $itemXml = self::getXmlData($word, $arrData, $succDanbaKeyNum, $succDuobaKeyNum);
                $danbaXml = $danbaXml . $itemXml['single'];
                $duobaXml = $duobaXml . $itemXml['multi'];
            }
        }
        if (self::$_forumMiningConcurrency > 1 && count($mwordArr) > 0) {
            $arrMultiOut = self::mgetData($mwordArr);
            foreach ($arrMultiOut as $keyWord => $arrData) {
                $itemXml = self::getXmlData($keyWord, $arrData, $succDanbaKeyNum, $succDuobaKeyNum);
                $danbaXml = $danbaXml . $itemXml['single'];
                $duobaXml = $duobaXml . $itemXml['multi'];
            }
            $mwordArr = array();
        }

        $danbaXml = Service_Alading_Xmltemplate_Wise_DjlTemplate::getXml($danbaXml);
        $duobaXml = Service_Alading_Xmltemplate_Wise_DjlTemplate::getXml($duobaXml);

        if ($isGenXmlTest) {
            // 线下开发测试输出产出的 xml
            var_dump(__FILE__ . __LINE__ . ' $danbaXml: '); var_export($danbaXml);
            // var_dump(__FILE__ . __LINE__ . ' $duobaXml: '); var_export($duobaXml);
        } else {
            $objFileName = sprintf("djl/danba/%s-data_$index.xml", self::$_resourceCategroy);
            self::saveXmlToBos($objFileName, $danbaXml);
            $objFileName = sprintf("djl/duoba/%s-data_$index.xml", self::$_resourceCategroy);
            self::saveXmlToBos($objFileName, $duobaXml);

            Bingo_Log::notice(sprintf("category: %s, index: $index update xml succ. danbaKeyNum: $succDanbaKeyNum, duobaKeyNum: $succDuobaKeyNum", self::$_resourceCategroy));

            // 更新 sitemap，所有的xml分片都已经生成/更新时再更新sitemap
            if ($index == self::$_xmlPageNum - 1) {
                self::buildSitemapXml(self::$_resourceCategroy, 0, self::$_xmlPageNum);
                self::buildSitemapXml(self::$_resourceCategroy, 1, self::$_xmlPageNum);
                // exit;
            }

            // 线上正常运行生成xml时，才进行监控统计
            self::_monitorAlatraceOffline($index, $succDanbaKeyNum, $succDuobaKeyNum);
        }
    }

    /**
     * 大家聊离线数据监控，当前线上展现只有单吧，因此建库监控也只关注单吧
     */
    private static function _monitorAlatraceOffline($index, $succDanbaKeyNum, $succDuobaKeyNum)
    {
        // 单吧上一轮离线建库成功的数量
        $danbaKeyLast = 'djl:medical:danba:xmlsucc:last';
        // 单吧当前这轮离线建库成功的数量
        $danbaKeyCur = 'djl:medical:danba:xmlsucc:current';

        $redis = new Bingo_Cache_Redis('push');
        if (!$redis || !$redis->isEnable()) {
            Bingo_Log::warning("init redis fail. index: $index, succDanbaKeyNum: $succDanbaKeyNum, succDuobaKeyNum: $succDuobaKeyNum");
            return false;
        }
        $arrParam = array(
            'key' => $danbaKeyCur,
            'step' => $succDanbaKeyNum,
        );
        $arrRet = $redis->INCRBY($arrParam);
        if ($arrRet['err_no'] !== 0) {
            Bingo_Log::warning("incy error, key: [ $danbaKeyCur ], ret: " . var_export($arrRet, true));
        } else {
            // Bingo_Log::notice("incy succ, key: [ $danbaKeyCur ], ret: " . var_export($arrRet, true));
        }

        // 最后一个 xml 也生成成功，此时新的一轮所有的建库 xml 生成完成了。据此分析离线建库是否存在异常
        // if (true) {
        if ($index == self::$_xmlPageNum - 1) {
            $arrRet = $redis->GET(array('key' => $danbaKeyCur));
            if ($arrRet['err_no'] !== 0) {
                Bingo_Log::warning("set error, key: [ $danbaKeyCur ], ret: " . var_export($arrRet, true));
                return;
            }
            $curSuccNum = $arrRet['ret'][$danbaKeyCur];

            $arrRet = $redis->GET(array('key' => $danbaKeyLast));
            if ($arrRet['err_no'] !== 0) {
                Bingo_Log::warning("set error, key: [ $danbaKeyLast ], ret: " . var_export($arrRet, true));
                return;
            }
            $lastSuccNum = $arrRet['ret'][$danbaKeyLast];

            $alarmRatio = self::_getAlarmRatio();

            // $curSuccNum = 0;
            if ($curSuccNum < $lastSuccNum && $lastSuccNum - $curSuccNum > $lastSuccNum * $alarmRatio) {
                $arrInput['content'] = "大家聊离线建库异常，建库成功数量下降较多，请保持关注!\n上一次建库量: $lastSuccNum, 本次建库量:$curSuccNum,  报警阈值:$alarmRatio.";
                $arrInput['title'] = '大家聊建库异常提醒';
                Libs_Util_Report::sendMail($arrInput, true);
                Bingo_Log::notice('send mail succ');
            }
            Bingo_Log::notice("lastSucc: $lastSuccNum, curSucc: $curSuccNum, ratio: $alarmRatio");

            // $curSuccNum = 10;
            // 最后更新相关记录，便于下一轮监控统计
            // 上一次的建库成功量更新为当前这一轮的。
            $input = array(
                'key' => $danbaKeyLast,
                'value' => $curSuccNum,
                'seconds' => 1 * 24 * 3600,
            );
            $arrRet = $redis->SETEX($input);
            if ($arrRet['err_no'] !== 0) {
                Bingo_Log::warning("set error, key: [ $danbaKeyLast ], ret: " . var_export($arrRet, true));
            }

            // 当前这一轮建库成功量清0
            $input = array(
                'key' => $danbaKeyCur,
                'value' => 0,
                'seconds' => 1 * 24 * 3600,
            );
            $arrRet = $redis->SETEX($input);
            if ($arrRet['err_no'] !== 0) {
                Bingo_Log::warning("set error, key: [ $danbaKeyCur ], ret: " . var_export($arrRet, true));
            }
        }
    }

    private static function _getAlarmRatio()
    {
        $handleWordServer = Wordserver_Wordlist::factory();
        $arrInfo = $handleWordServer->getValueByKeys(array('alarm_ratio'), 'tb_wordlist_redis_dajialiao');
        $alarmRatio = 0.05;
        if (isset($arrInfo['alarm_ratio']) && floatval($arrInfo['alarm_ratio']) > 0) {
            $alarmRatio = floatval($arrInfo['alarm_ratio']);
        }
        // Bingo_Log::notice("get alarm_ratio: " . var_export([$arrInfo, $alarmRatio], true));
        return $alarmRatio;
    }

    /**
     * 并发的调用内容挖掘服务
     */
    public static function mgetData($wordList)
    {
        $objRalMulti = new Tieba_Multi('forumMining');
        foreach ($wordList as $_key => $word) {
            // $word = '王者荣耀';
            $input = array(
                'serviceName' => 'push',
                'method'      => 'forumMining',
                'input'       => array(
                    'query' => $word,
                ),
                'format'      => 'php',
                // Note: 白浩提醒，内容挖掘服务比较耗时，因此提高超时时间，保证内容挖掘接口成功率。
                // 10s 太夸张，先设置为 5s 试一试吧。
                "extra"       => array(
                    'rtimeout' => 5000,
                ),
            );
            $objRalMulti->register($word, new Tieba_Service(), $input);
            // $objRalMulti->register($_key, new Tieba_Service(), $input);
        }

        $arrMultiOut = $objRalMulti->call();
        // var_dump(__FILE__.__LINE__ . ' $arrMultiOut: '); var_export($arrMultiOut);        
        return $arrMultiOut;
    }

    /**
     * 根据搜索词对应的内容挖掘服务返回信息，生成单吧和多吧对应的xml；
     */
    public static function getXmlData($query, $arrData, &$succDanbaKeyNum, &$succDuobaKeyNum)
    {
        // var_dump(__FILE__.__LINE__ . ' $xmldata: '); var_export($arrData);        
        if (empty($arrData) || $arrData['errno'] != 0) {
            Bingo_Log::warning("query: $query forumMining fail. arrData: " . Bingo_String::array2json($arrData));
            return array(
                'single' => '',
                'multi' => '',
            );
        }

        $singleDisplay = Service_Alading_Xmltemplate_Wise_DjlTemplate::execute($arrData['data'], $query);
        $multiDisplay = Service_Alading_Xmltemplate_Wise_DjlTemplate::execute($arrData['data'], $query, true);
        if ($singleDisplay == false) {
            $singleDisplay = '';
        } else {
            $succDanbaKeyNum++;
        }
        if ($multiDisplay == false) {
            $multiDisplay = '';
        } else {
            $succDuobaKeyNum++;
        }

        return array(
            'single' => $singleDisplay,
            'multi' => $multiDisplay,
        );
    }

    public static function _init($category, $concurrency = 10)
    {
        self::$_forumMiningConcurrency = intval($concurrency);
        if ($category == self::TYPE_GAME) {
            self::$_listKey = self::DJL_GAME_QUERYLIST_KEY;

            self::$_setnxKey = self::GAME_SETNX_KEY;
            self::$_resourceCategroy = self::TYPE_GAME;
            self::$_indexKey = self::GAME_INDEX;
        } elseif ($category == self::TYPE_MEDICAL) {
            self::$_listKey = self::DJL_MEDICAL_QUERYLIST_KEY;

            self::$_setnxKey = self::MEDICAL_SETNX_KEY;
            self::$_resourceCategroy = self::TYPE_MEDICAL;
            self::$_indexKey = self::MEDICAL_INDEX;
        } else {
            self::$_resourceCategroy = '';
            Bingo_Log::warning("unknown category: $category");
            return false;
        }
        return true;
    }

    public function run()
    {
        // 检查 category 
        if (empty(self::$_resourceCategroy)) {
            return;
        }

        $category = self::$_resourceCategroy;
        $concurrency = self::$_forumMiningConcurrency;
        $listKey = self::$_listKey;
        $listLen = Dl_Alading_Alading::getListLength($listKey);
        if ($listLen <= 0) {
            Bingo_Log::warning("list: $listKey is empty");
            return;
        }
        self::$_xmlPageNum = ceil($listLen / self::QUERY_NUM_OF_EACH_XML);
        Bingo_Log::notice(sprintf("begin run, concurrency: $concurrency, category: $category, listLen: $listLen, listKey: %s, setnxKey: %s, indexKey: %s", $listKey, self::$_setnxKey, self::$_indexKey));


        // 这里再加上执行耗时控制
        $beginTime = time();
        $getLockFailCnt = 0;
        while (true) {
            $nowTime = time();
            if ($nowTime - $beginTime > 60 * 13) {
                // 脚本设置15分钟执行一次，每次执行13分钟
                Bingo_Log::notice("script time to exit, begin[$beginTime], end[$nowTime]");
                break;
            }

            // 多个进程同时执行并更新index信息，所以要加分布式锁
            $index = $this->getIndex($category);
            // $index = 14;
            if ($index === false) {
                // 获取分布式锁失败
                $getLockFailCnt++;
                if ($getLockFailCnt > 20) {
                    // 连续多次加锁失败，可以认为有定时脚本挂掉，导致没有解锁。所以手动清除分布式锁，不然程序永远没法向后执行了。
                    Dl_Alading_Alading::delKey(self::$_setnxKey);
                    Bingo_Log::warning("delete setnx key manual: " . self::$_setnxKey);
                    sleep(1);
                } else {
                    // 暂时性的获取分布式锁失败，等待一会
                    Bingo_Log::warning("get setnx key failed num[$getLockFailCnt], wait for next...");
                    sleep(2);
                }
                continue;
            }
            // 设置连续获取锁失败次数为0.
            $getLockFailCnt = 0;

            $start = self::QUERY_NUM_OF_EACH_XML * $index;
            if ($index + 1 == self::$_xmlPageNum) {
                $end = -1;
            } else {
                $end = $start + self::QUERY_NUM_OF_EACH_XML - 1;
            }

            $wordList = Dl_Alading_Alading::rangeList($listKey, $start, $end);
            if (count($wordList) <= 0) {
                Bingo_Log::warning("listKey: $listKey, index: $index, start: $start, end: $end range empty");
                sleep(2);
                continue;
                // break;
            }
            Bingo_Log::notice("listKey: $listKey, index: $index, start: $start, end: $end. range return num: " . count($wordList));
            // var_dump("listKey: $listKey, index: $index, start: $start, end: $end. range return num: " . count($wordList));
            self::Process($wordList, $index);
            // break;
        }
    }
}

function _readFile($category)
{
    $lineArr = array();

    // 文件内容 utf-8 编码
    $fn = __DIR__ . "/new/data/djl_$category.txt";
    $handle = fopen("$fn", "r");
    if (!$handle) {
        Bingo_Log::warning("open file: $fn failed.");
        return false;
    }

    while (($line = fgets($handle)) !== false) {
        $line = str_replace("\n", "", $line);
        $lineArr[] = $line;

        var_dump($line);
    }

    fclose($handle);
    Bingo_Log::notice(sprintf("read file: $fn succ, num: %d", count($lineArr)));
    return $lineArr;
}

function initQueryList($category)
{
    $queryList = _readFile($category);
    var_export($queryList);

    if ($category == Dajialiao::TYPE_GAME) {
        $listKey = Dajialiao::DJL_GAME_QUERYLIST_KEY;
    } else {
        $listKey = Dajialiao::DJL_MEDICAL_QUERYLIST_KEY;
    }
    // 初始化 list 之前，先清除旧的list内容
    Dl_Alading_Alading::delKey($listKey);
    foreach ($queryList as $_k => $query) {
        Dl_Alading_Alading::rpushList($listKey, $query);
        if ($_k % 50 == 0) {
            var_dump($query, Dl_Alading_Alading::getListLength($listKey));
        }
    }
}

// 其他测试类代码，线上不会执行，用于线下给QA mock数据时，手动更新sitemap索引，手动更新建库xml信息，有一定作用，因此予以保留。
function _offlineTest()
{
    $obj = new Dajialiao('medical', 10);

    $content = file_get_contents('../medical-data_10.xml');
    var_export($content);
    // 手动线下编辑 xml 文件文件，然后更新 bos 存储，用于线下数据mock
    $obj->saveXmlToBos('djl/danba/medical-data_10.xml', $content);

    // 手动造长尾query，然后调用内容挖掘服务，并更新 xml bos存储
    $obj->buildIndexXml(10, 0);

    // 手动更新 sitemap 索引
    $obj->buildSitemapXml('medical', 0, 11);
    $obj->buildSitemapXml('medical', 1, 11);

    // 手动更新 redis index 索引
    Dl_Alading_Alading::set(Dajialiao::GAME_INDEX, 0);
    // Dl_Alading_Alading::set(Dajialiao::MEDICAL_INDEX, 0);
}


/**
 * 【重要: 2021年11月18日变更说明】
 *  1、最初的设计，按照医疗单吧、医疗多吧、游戏单吧、游戏多吧设计。一共四个sitemap；
 *     执行方式如：
 *      ~/orp/hhvm/bin/hhvm djlScript.php medical 10 或者
 *      ~/orp/hhvm/bin/hhvm djlScript.php game 10
 *  2、由于三级单审查没过，需要将垂类合并，即：单吧，多吧；每个单/多吧本身不再按照垂类进行区分；
 *  3、游戏单吧、医疗单吧合并为一个单吧后，使用医疗单吧的资源号：33724
 *  4、因此，按照最小变更原则：将医疗类query、游戏类query都合并到 医疗的list即可。
 *     redis key为：'DAJIALIAO_LIST:medical'
 *     其他的变更方式，代码变更都较大，需要qa介入，且容易出错。
 *  5、合并之后，执行方式为：
 *     ~/orp/hhvm/bin/hhvm djlScript.php medical 10/Num
 *  6、另外，程序脚本加一个执行逻辑：更新 redis list（由于 query 需要扩量等）。执行方式为：
 *     ~/orp/hhvm/bin/hhvm djlScript.php medical updatelist
 *     可直接 orp 线下执行。
 */

var_export($argv);
if ($argv[2] == 'updatelist') {
    // 线下更新 redis list，执行方式：~/orp/hhvm/bin/hhvm djlScript.php medical updatelist
    var_dump(Dl_Alading_Alading::getListLength(Dajialiao::DJL_GAME_QUERYLIST_KEY));
    var_dump(Dl_Alading_Alading::getListLength(Dajialiao::DJL_MEDICAL_QUERYLIST_KEY));

    // 都合并到医疗垂类了，直接更新医疗query list即可。
    // initQueryList('game');
    initQueryList('medical');
    exit;
} elseif ($argv[2] == 'genxmltest') {
    // 线下测试生成 xml，执行方式：~/orp/hhvm/bin/hhvm djlScript.php medical genxmltest
    $wordList = array(
        '高血压', '艾滋病', '原神',
        '冒险', '蒋介石之死', '烽火', '黑域',
        // '单眼皮女生',
        // '天津市南开医院',
        // '逆生长',
        // '幼儿矮小',
        // '成都现代医院',
        // '木虱图片',
        // '丰太阳穴',
        // '很困',
        // '西伯利亚冷空气',
        // '腰腹吸脂',
        // '韩式',
        // '柠檬草的味道',
        // '人群恐惧症',
        // '阿达木单抗注射液',
        // '牙龈刮治',
        // '去头屑',
        // '宫颈糜烂治疗',
        // '缩阴方法',
        // '希玛眼科',
        // '湘潭职业技术学院',
        // '邯郸市妇幼保健院',
        // '爱丽丝综合症',
        // '铁五院',
        // '肾宝糖浆',
        // '种头发价格',
        // '养身',
        // '莓茶的功效与作用',
        // '卫生标语',
        // '咖酚伪麻片',
        // '奇亚子',
        // '承光穴',
        // '鹿茸的价格',
        // '大庆吧',
        // '肾不好怎么办',
    );
    $obj = new Dajialiao('medical', 10);
    $obj->genXmlTest($wordList);
    exit;
}

// 更新离线建库主逻辑，由于垂类合并，之前argv[1]指定垂类的参数忽略，固定写死成 medical 即可
// 执行方式：~/orp/hhvm/bin/hhvm djlScript.php medical 10/Num
// $obj = new Dajialiao($argv[1], $argv[2]);
$obj = new Dajialiao('medical', $argv[2]);
$obj->run();
// $obj->buildSitemapXml('medical', 0, 21);
