<?php

/**
 * @brief ����site map �����ļ�
 * <AUTHOR>
 * @desc
 */
ini_set ( "memory_limit", "-1" );
define('MODULE_NAME', 'push');
date_default_timezone_set ( "Asia/Chongqing" );
// �������·����Ϣ
define ( 'APP_NAME', 'push' );
define ( 'SCRIPT_NAME', 'sitemap' );
define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../../..' );
define ( 'SCRIPT_ROOT_PATH', ROOT_PATH . '/app/' . APP_NAME . '/script' );
define ( 'SCRIPT_LOG_PATH', ROOT_PATH . '/log/app/' . APP_NAME );
define ( 'SCRIPT_CONF_PATH', ROOT_PATH . '/conf/app/' . APP_NAME );
define ( 'DATA_DIR', SCRIPT_ROOT_PATH . "/data/alading/" . SCRIPT_NAME . "/" );
define ( 'REMOTE_PATH', "/app/aladdin" );

/**
 * @param $strClassName ����
 * @return ����ֵ
 *
*/
function __autoload($strClassName) {
	$strNewClassName = str_replace ( '_', '/', $strClassName . ".php" );
	$arrClass = explode ( '/', $strNewClassName );
	$intPathLen = count ( $arrClass );
	$strLastName = $arrClass [$intPathLen - 1];
	$strTmp = strtolower ( $strNewClassName );
	$intPreLen = strlen ( $strTmp ) - strlen ( $strLastName );
	$strNewClassName = substr ( $strTmp, 0, $intPreLen ) . $strLastName;
	$strClassPath = ROOT_PATH . '/app/' . APP_NAME . '/' . $strNewClassName;
	require_once $strClassPath;
}
spl_autoload_register ( '__autoload' );

// ����logid
if (! defined ( 'REQUEST_ID' )) {
	$requestTime = gettimeofday ();
	define ( 'REQUEST_ID', (intval ( $requestTime ['sec'] * 100000 + $requestTime ['usec'] / 10 ) & 0x7FFFFFFF) );
}

if (function_exists ( 'camel_set_logid' )) {
	camel_set_logid ( REQUEST_ID );
}

Bingo_Log::init ( array (
	LOG_SCRIPT => array (
		'file' => SCRIPT_LOG_PATH . '/' . SCRIPT_NAME . '/' . SCRIPT_NAME . '.log',
		'level' => 0x01 | 0x02 | 0x04 | 0x08,
	),
),
LOG_SCRIPT );

$strInDirName = isset($argv[1]) ? strval($argv[1]) : '';
$strOutFileName = isset($argv[2]) ? strval($argv[2]) : '';

if ('' == $strInDirName || '' == $strOutFileName)
{
	echo "input param error \n";
    var_dump($argv);
    exit;	
}
echo "input dir name = $strInDirName \n";
echo "output firle = $strOutFileName \n";
$ret = BuildSiteMapIndex::exec($strInDirName, $strOutFileName);
echo "build site map ret=$ret \n";

class BuildSiteMapIndex
{
	/**
	 * get site map index 
	 * @param unknown_type $strDirName
	 * @return site map index 
	 */
	public static function exec($strDirName, $strOutFileName)
	{
	    $listRet = self::_getFileList($strDirName);
        //var_dump($listRet);
	    if (false === $listRet || empty($listRet))
	    {
	    	echo "get file list fail \n";
	    	return false;
	    }
	    
	    $templateRet = self::_buildSiteMapIndexTemplate($listRet);
	    //var_dump($templateRet);
	    if (false === $templateRet)
	    {
	    	echo "get template info fail \n";
	    	return false;
	    }
	
        $fileRet = self::_buildSiteMapIndexFile($strOutFileName, $templateRet);
        
        if (false === $fileRet)
        {
        	echo "get site map index file fail \n";
        	return false;
        }
        
        return true;
	}
	
	/**
	 * ��ȡ�ļ����б�
	 * @param unknown_type $strDirName
	 * @return boolean|multitype:unknown
	 */
	private static function _getFileList($strDirName)
	{
		if (!is_dir($strDirName))
		{
			echo "input dir name is not dir, the input=$strDirName \n";
			return false;
		}
		
		$arrOutput = array();
		$dir = dir($strDirName);
		
		while (($file = $dir->read()) !== false)
        {
            //echo "filename: " . $file . "\n";
            if ('' !== $file)
            {
            	$arrOutput[] = $file;
            }            
        }

        $dir->close(); 
        return $arrOutput;
	}
	
	/**
	 * ��ȡģ����Ϣ
	 * @param unknown_type $arrFileList
	 * @return Ambigous <multitype:, string>
	 */
	private static function _buildSiteMapIndexTemplate($arrFileList)
	{
		$strTiebaPre = "http://tieba.baidu.com/";
		$arrOutput = array();
		
		foreach ($arrFileList as $strFileName)
		{
			$strLoc = $strTiebaPre.$strFileName;
			$day = date("Y-m-d");
			$arrSiteMap['loc'] = $strLoc;
			$arrSiteMap['lastmod'] = $day;
			$arrOutput['sitemap'][] = $arrSiteMap;
		}
		return $arrOutput;
	}
	
	/**
	 * ��ȡ����ļ�
	 * @param unknown_type $strOutFileName
	 * @param unknown_type $arrSiteMapTemplate
	 * @return boolean
	 */
	private static function _buildSiteMapIndexFile($strOutFileName, $arrSiteMapTemplate)
	{
		try
		{			
			$itemXml = Libs_Util_Array2xml::createXML('sitemapindex', $arrSiteMapTemplate);
			$strXml = $itemXml->saveXML();
			file_put_contents($strOutFileName, $strXml);
		}
		catch (Exception $e)
		{
		    var_dump($e);
		    //Bingo_log::warning("_buildItemXml exception,".print_r($e,r));
		    return false;
		}
		
		return true;
	}
}

