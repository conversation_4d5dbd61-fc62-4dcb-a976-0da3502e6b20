<?php

/**
 * @brief ��������ɾ������������ͬ��
* <AUTHOR>
* @desc
*/
ini_set ( "memory_limit", "-1" );
define('MODULE_NAME', 'push');
date_default_timezone_set ( "Asia/Chongqing" );
// �������·����Ϣ
define ( 'APP_NAME', 'push' );
define ( 'SCRIPT_NAME', 'sitemap' );
define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../../..' );
define ( 'SCRIPT_ROOT_PATH', ROOT_PATH . '/app/' . APP_NAME . '/script' );
define ( 'SCRIPT_LOG_PATH', ROOT_PATH . '/log/app/' . APP_NAME );
define ( 'SCRIPT_CONF_PATH', ROOT_PATH . '/conf/app/' . APP_NAME );
define ( 'DATA_DIR', SCRIPT_ROOT_PATH . "/data/alading/" . SCRIPT_NAME . "/" );
define ( 'REMOTE_PATH', "/app/aladdin" );

/**
 * @param $strClassName ����
 * @return ����ֵ
 *
 */
function __autoload($strClassName) {
	$strNewClassName = str_replace ( '_', '/', $strClassName . ".php" );
	$arrClass = explode ( '/', $strNewClassName );
	$intPathLen = count ( $arrClass );
	$strLastName = $arrClass [$intPathLen - 1];
	$strTmp = strtolower ( $strNewClassName );
	$intPreLen = strlen ( $strTmp ) - strlen ( $strLastName );
	$strNewClassName = substr ( $strTmp, 0, $intPreLen ) . $strLastName;
	$strClassPath = ROOT_PATH . '/app/' . APP_NAME . '/' . $strNewClassName;
	require_once $strClassPath;
}
spl_autoload_register ( '__autoload' );

// ����logid
if (! defined ( 'REQUEST_ID' )) {
	$requestTime = gettimeofday ();
	define ( 'REQUEST_ID', (intval ( $requestTime ['sec'] * 100000 + $requestTime ['usec'] / 10 ) & 0x7FFFFFFF) );
}

if (function_exists ( 'camel_set_logid' )) {
	camel_set_logid ( REQUEST_ID );
}

Bingo_Log::init ( array (
	LOG_SCRIPT => array (
		'file' => SCRIPT_LOG_PATH . '/' . SCRIPT_NAME . '/' . SCRIPT_NAME . '.log',
		'level' => 0x01 | 0x02 | 0x04 | 0x08,
	),
), 
LOG_SCRIPT );

$strInFileName = isset($argv[1]) ? strval($argv[1]) : '';
$strOutFileName = isset($argv[2]) ? strval($argv[2]) : '';
$strOutDirName = isset($argv[3]) ? strval($argv[3]) : '';

if ('' == $strInFileName || '' == $strOutFileName || '' == $strOutDirName)
{
	echo "input param fail \n";
	var_dump($argv);
	exit;
}

echo "input file name = $strInFileName \n";
echo "output file name = $strOutFileName \n";
echo "output dir = $strOutDirName \n";
$ret = BuildSiteMap::exec($strInFileName, $strOutFileName, $strOutDirName);
echo "build site map ret=$ret \n";

class BuildSiteMap
{
	const SITEMAP_FILE_PRE = "sitemap_";	
	
	/**
	 * ��ȡsitemap���
	 * @param unknown_type $strInFileName
	 * @param unknown_type $strOutFileName
	 * @param unknown_type $strOutDirName
	 * @return boolean
	 */
	public static function exec($strInFileName, $strOutFileName, $strOutDirName)
	{
		echo "input file is $strInFileName \n";
		$siteInfoRet = self::buildSiteInfo($strInFileName);
		//var_dump($siteInfoRet);exit;
		if (false === $siteInfoRet)
		{
			$errmsg = "build site info fail \n";
			echo $errmsg;
			return false;
		}

		$siteTemplate = self::buildSiteTemplate($siteInfoRet);
        //var_dump($siteTemplate);exit;
		if (false === $siteTemplate)
		{
			$errmsg = "build site map template fail \n";
			echo $errmsg;
		}

		$siteFile = self::buildSiteMapFile($strOutDirName, $strOutFileName, $siteTemplate);
		if (false === $siteFile)
		{
			$errmsg = "build site xml file fail \n";
			echo $errmsg;
		}

		return true;
	}
		
	/**
	 * ����site map ��Ϣ
	 * @param unknown_type $strFileName
	 * @return boolean|Ambigous <multitype:, string>
	 */
	public static function buildSiteInfo($strFileName)
	{
		$fp = fopen($strFileName, 'r');
		
		if(!$fp)
		{
			$errmsg = "input dir1 file failed,open file: $strFileName failed";
			Bingo_Log::warning($errmsg);
			echo $errmsg."\n";
			return false;
		}
		$intForunItemNum = 6;
		$arrOutput = array();
		$intNum = 0;
		$intBatch = 0;
		// ��վ��sitemap�ļ�Ҫ�󣬵��ļ�����ܳ���50000����¼
		$intMaxNum = 40000;
		
		while(!feof($fp))
		{
			$strLine = trim(fgets($fp,1024));
			
			if ('' === $strLine)
			{
				continue;
			}
			
			$arrForumInfo = explode(" ", $strLine);
			$intCount = count($arrForumInfo);
			
			if ($intCount < $intForunItemNum)
			{
				$errmsg = "forum info item num less 9, $strLine";
				Bingo_Log::warning($errmsg);
				echo $errmsg."\n";
				continue;
			}
			
			$strForumName = isset($arrForumInfo[1]) ? strval($arrForumInfo[1]) : '';
			
			if ('' == $strForumName || '0' == $strForumName)
			{
				continue;
			}
			
			$strDir1 = isset($arrForumInfo[2]) ? strval($arrForumInfo[2]) : '0';

			if ('0' === $strDir1)
			{
				$intDayThreadNum = isset($arrForumInfo[5]) ? intval($arrForumInfo[5]) : 0;
				$intDayPostNum = isset($arrForumInfo[6]) ? intval($arrForumInfo[6]) : 0;
			}
			else
			{
				$intDayThreadNum = isset($arrForumInfo[6]) ? intval($arrForumInfo[6]) : 0;
				$intDayPostNum = isset($arrForumInfo[7]) ? intval($arrForumInfo[7]) : 0;
			}
			
			
			$fltPriority = 0;
			$fltPriority = self::_priority($intDayThreadNum, $intDayPostNum);
			$strChangeFreq = self::_changeFreq($intDayThreadNum, $intDayPostNum);
			$arrItem['forum_name'] = $strForumName;
			$arrItem['priority'] = $fltPriority;
  			$arrItem['change_freq'] = $strChangeFreq;
  			$intNum++;  			
  			$arrOutput[$intBatch][] = $arrItem;
  			
  			if ($intNum >= $intMaxNum)
  			{
				$intBatch++;
				$intNum = 0;
  			}			
		}
		
		return $arrOutput;
	}
	
	/**
	 * ����վ��ģ����Ϣ
	 * @param unknown_type $arrSiteInfo
	 * @return multitype:string
	 */
	public static function buildSiteTemplate($arrSiteInfo)
	{
		$strTiebaPre = "http://tieba.baidu.com/";
		$arrOutput = array();
		foreach ($arrSiteInfo as $arrBatch)
		{
			$arrUrlSet = array();
			foreach($arrBatch as $arrItem)
			{
				$strForumName=isset($arrItem['forum_name']) ? strval($arrItem['forum_name']) : '';
				$strPriority = isset($arrItem['priority']) ? strval($arrItem['priority']) : '0.1';
				$strChangeFreq= isset($arrItem['change_freq']) ? strval($arrItem['change_freq']) : 'daily';
				$strUrl = urlencode($strForumName);
				$day = date('Y-m-d');
				$arrUrl['loc'] = $strTiebaPre.$strUrl;
				$arrUrl['lastmod'] = $day;
				$arrUrl['changefreq'] = $strChangeFreq;
				$arrUrl['priority'] = $strPriority;
				//$arrUrlItem['url'] = $arrUrl;
				$arrUrlSet['url'][] = $arrUrl;
			}
			$arrOutput[] = $arrUrlSet;
		}
		
		return $arrOutput;
	}
	
	/**
	 * ����site map xml�ļ�
	 * @param unknown_type $strOutDirName
	 * @param unknown_type $strFileRoot
	 * @param unknown_type $arrSiteMapList
	 * @return boolean
	 */
	private static function buildSiteMapFile($strOutDirName, $strFileRoot, $arrSiteMapList)
	{
		echo "$strFileRoot \n";
		echo "$strOutDirName \n";
		//var_dump($arrSiteMapList);
		if (empty($arrSiteMapList))
		{
			return false;
		}
		
		$arrOutput = array();
		
	    foreach ($arrSiteMapList as $index => $arrSiteMap)
	    {
	    	try
			{
				echo "index=$index \n";
				$itemXml = Libs_Util_Array2xml::createXML('urlset', $arrSiteMap);				
	    		$strXml = $itemXml->saveXML();
	    		$strFile = $strOutDirName.self::SITEMAP_FILE_PRE.$strFileRoot."_$index.xml";
				echo "$strFile n";
				//echo $strXml;
				file_put_contents($strFile, $strXml);		    		
	    	}
	    	catch (Exception $e)
			{
				var_dump($e);
	    		//Bingo_log::warning("_buildItemXml exception,".print_r($e,r));
	    		return false;
	    	}	    	
	    }
	    
	    return true;
	}
	
	/**
	 * ��ȡ���Ӵ��
	 * @param unknown_type $intThreadNum
	 * @param unknown_type $intPostNum
	 * @return number
	 */
	private static function _priority($intThreadNum, $intPostNum)
	{
		$fltPriority = 0.1;
		$fltRate = 0;
		if ($intThreadNum !== 0)
		{
			$fltRate = $intPostNum/$intThreadNum;
		}
		
		if ($intThreadNum > 10)
		{
			$fltPriority = 0.5;
		}
		
		if ($intThreadNum > 20)
		{
			$fltPriority = 0.8;
		}
		
		if ($intThreadNum > 100)
		{
			$fltPriority = 0.9;
		}
		
		return $fltPriority;
	}
	
	/**
	 * ���Ƶ��
	 * @param unknown_type $intThreadNum
	 * @param unknown_type $intPostNum
	 * @return string
	 */
	private static function _changeFreq($intThreadNum, $intPostNum)
	{
		$strChangeFreq = 'weekly';
		if ($intThreadNum > 100)
		{
			$strChangeFreq = "always";
			return $strChangeFreq;
		}
		
		if ($intThreadNum > 50)
		{
			$strChangeFreq = "hourly";
			return $strChangeFreq;
		}
		
		if ($intThreadNum > 0)
		{
			$strChangeFreq = "daily";
			return $strChangeFreq;
		}
		
		return $strChangeFreq;
	}
	
	
}
