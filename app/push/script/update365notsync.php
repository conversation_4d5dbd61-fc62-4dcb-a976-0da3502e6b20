<?php
/**
 * Created by PhpStorm.
 * User: liguang03
 * Date: 19/1/2
 * Time: 6:51 ����
 */

define ('BASEPATH',dirname(__FILE__));
define ('DATAPATH',BASEPATH."/data/notsync");
require_once dirname(__file__)."/Init.php";
script::init("update356notsync");
echo "script start~\n";
run($argv);
echo "script end~\n";

/**
 * @param $argv
 */

function run($argv) {

    $yesterday = date("Ymd", strtotime("-1 day"));
    $beforeyesterday = date("Ymd", strtotime("-2 day"));

    $fileRemoteName = "ftp://tieba01:<EMAIL>/download_tieba_mds_native_lite_dau_not_sync_day/native_lite_dau_not_sync_".$yesterday.".txt";

    if (!is_dir(DATAPATH)) {
        mkdir(DATAPATH);
    }
    $strFileName = "native_lite_dau_not_sync_" . $yesterday . ".txt";

    $cmd = 'cd ' . DATAPATH. "; wget " . $fileRemoteName . " -O $strFileName";
    exec($cmd);
    $fileLocatPath = DATAPATH . "/" . $strFileName;

    //���ļ�����
    if (!file_exists($fileLocatPath)) {
        echo 'file not exists';
        return;
    }

    $fp = fopen($fileLocatPath, 'r');

    if ($fp == false) {
        echo 'open file failed';
        return;
    }

    //��ʼ��
    $n = 0;
    $pattern = "/\'|\"|\<|\>|\{|\=|\}|\&|\\\|\%|\#/";

    while (!feof($fp)) {

        $line = trim(fgets($fp));
        $arr = explode("\t", $line);
        if (count($arr) < 5) {
            outLogLine("bad data :$line");
            continue;
        }
        if(preg_match($pattern, $line)) {
            outLogLine('hackinfo:'. $line);
            continue;
        }
        $cuid = $arr[0];
        $imei = $arr[1];
        $userid = $arr[2];
        $brand = $arr[3];
        $subapp = $arr[4];
        $initTime = strtotime(date("Y-m-d",strtotime("-1 day")));

        $cuidTableIndex = crc32($cuid) % 100;
        $imeiTableIndex = crc32($imei) % 100;
        $userTableIndex = crc32($userid . $brand) % 100;

        //��sync�ӿ��е�cuid,imei uid brand ֻ��ʱ�����,�������� ,����������
        $cuidUpsql = "set update_time=$initTime  where cuid='$cuid' and subapp_type=$subapp and update_time<$initTime";
        $imeiUpsql = "set update_time=$initTime  where imei='$imei' and subapp_type=$subapp and update_time<$initTime";
        $userUpsql = "set update_time=$initTime  where user_id=$userid and subapp_type=$subapp and brand='$brand' and update_time<$initTime";

        //cuid
        updateCuidRecord($cuidUpsql, $cuidTableIndex);
        //imei
        updateImeiRecord($imeiUpsql, $imeiTableIndex);
        //user
        updateUserRecord($userUpsql, $userTableIndex);
        $n++;
        if ($n % 100 == 0) {
            outLogLine("count-n:" . $n . " line: " . $line);
        }

    }

    //�ر��ļ�
    fclose($fp);
    //��ʼ����ɺ�ɾ�������ļ�
    $cmd = 'rm  ' . $fileLocatPath;
    exec($cmd);


}

/**
 * @param
 * @return
 */
function updateCuidRecord($upSql, $table_index) {
    $tableName = 'client_info_cuid_'.$table_index;
    $sql = "update $tableName " . $upSql;
    $arrOutput = DBProxy::_queryDB($sql);
    if($arrOutput === false || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS){
        return false;
    }
    return true;
}


/**
 * @param
 * @return
 */
function updateImeiRecord($upSql, $table_index) {
    $tableName = 'client_info_imei_'.$table_index;
    $sql = "update $tableName " . $upSql;
    $arrOutput = DBProxy::_queryDB($sql);
    if($arrOutput === false || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS){
        return false;
    }
    return true;
}

/**
 * @param
 * @return
 */
function updateUserRecord($upSql, $table_index) {
    $tableName = 'client_info_user_'.$table_index;
    $sql = "update $tableName " . $upSql;
    $arrOutput = DBProxy::_queryDB($sql);
    if($arrOutput === false || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS){
        return false;
    }
    return true;
}


/**
 * ��־�д�ӡ
 * @param $strMsg
 * @param bool $bolLineInfo
 * @return null
 */
function outLogLine($strMsg, $type = 'w', $bolEcho = false, $bolLineInfo = false, $bolFlush = true)
{
    $strFileName = '';
    $intLineNo = 0;
    if ($bolLineInfo) {
        if (defined('DEBUG_BACKTRACE_IGNORE_ARGS')) {
            $arrTrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        } else {
            $arrTrace = debug_backtrace();
        }
        if (!empty($arrTrace)) {
            $strFileName = basename($arrTrace[0]['file']);
            $intLineNo = intval($arrTrace[0]['line']);
        }
    }
    if ('n' == $type) {
        Bingo_Log::notice($strMsg, '', $strFileName, $intLineNo);
    } else if ('w' == $type) {
        Bingo_Log::warning($strMsg, '', $strFileName, $intLineNo);
    } else if ('f' == $type) {
        Bingo_Log::fatal($strMsg, '', $strFileName, $intLineNo);
    } else {
        Bingo_Log::warning($strMsg, '', $strFileName, $intLineNo);
    }
    if($bolFlush){
        $logObj = Bingo_Log::getModule();
        if(false !== $logObj && null != $logObj && !empty($logObj)){
            $logObj->flush();
        }
    }
    if ($bolEcho) {
        $strLogPrefix = '';
        if(!empty($strFileName)){
            $strLogPrefix = $strFileName;
        }
        if(0 < $intLineNo){
            $strLogPrefix = $strFileName.'['.$intLineNo.']';
        }
        if(!empty($strLogPrefix)){
            $strMsg = $strLogPrefix.' '.$strMsg;
        }
        echo $strMsg . PHP_EOL;
    }
}


class DBProxy{
    const DB_NAME = 'forum_client';
    private static $_objDB = null;

    /**
     * @param
     * @return
     */
    public static function _queryDB($strSql, $strTimer = 'db_query') {
        $objDB = self::_getDB();
        if ( !$objDB ){
            outLogLine('fail to get db. sql:'.$strSql);
            return false;
        }
        Bingo_Timer::start($strTimer);
        $arrRet = $objDB->query($strSql);
        Bingo_Timer::end($strTimer);
        if ( $arrRet === false ){
            outLogLine("execute sql error sql:$strSql");
        }
        return $arrRet;
    }

    /**
     * @param
     * @return
     */
    private static function _getDB() {
        if ( self::$_objDB && self::$_objDB->isConnected(true)){
            return self::$_objDB;
        }
        if (self::$_objDB) {
            self::$_objDB->close();
        }

        Bingo_Timer::start('db_init');
        self::$_objDB = Tieba_Mysql::getDB(self::DB_NAME);
        outLogLine('db_init');
        Bingo_Timer::end('db_init');
        if ( self::$_objDB && self::$_objDB->isConnected(true) ){
            self::$_objDB->charset('utf8');
            return self::$_objDB;
        }
        else{
            outLogLine('fail to connect db');
            return null;
        }
    }
}