<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file PingHistoryClean.php
 * <AUTHOR>
 * @date 2013/11/02 16:35:26
 * @brief 
 * 
 * 
 */

require_once dirname(__file__)."/Init.php";
//��ʼ���ű�
script::init("PingHistoryClean");
define ( 'MODULE_NAME', 'alading');
Bingo_Timer::start("total");

/**
 * ------------------------------------------------------------------------------
 * ����orp��ʷ�����Լ�ping schedule�ϵĻ�������,//���ٱ���һ�������
 * ------------------------------------------------------------------------------
 */
$time = time()-86400*3;
$getDoneConfig = array(
		"status" => "1",
		"extra"  => "update_time < $time",
		"limit"  => 4000,
);
$arrDoneTask = Libs_Util_Db::getPingSchedule($getDoneConfig);
if(false!==$arrDoneTask || !empty($arrDoneTask))
{
	foreach($arrDoneTask as $task)
	{
		$id = intval($task["id"]);
		$key = strval($task["remote_path"]);
		$res = Libs_Util_Db::deletePingSchedule($id);
		if(false === $res)
		{
			Bingo_Log::warning("deletePingSchedule failed! id[$id]");
		}
		$res = Libs_Util_File::deleteOrpData($key);
		if(false === $res)
		{
			Bingo_Log::warning("deletePingSchedule failed! key[$key]");
		}
	}
}

Bingo_Timer::end("total");
Bingo_Log::pushNotice('PingHistoryClean', Bingo_Timer::toString());
Bingo_Log::buildNotice("PingHistoryClean script run successful");
exit(0);//�����˳�






/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
