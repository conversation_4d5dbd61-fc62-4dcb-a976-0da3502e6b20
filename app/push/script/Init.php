<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file init.php
 * <AUTHOR>
 * @date 2013/11/2 16:35:26
 * @brief �ű���ʼ����
 *  
 **/

class script{
	
	/**
	 * @brief script��������ͷ
	 *
	 * @param [in/out] req  :
	 * @return  void
	 * @retval
	 * @see
	 * @note
	 * <AUTHOR>
	 * @date 2013/08/29 15:36:46
	 **/
	public static function init($appname)
	{
		ini_set("memory_limit","-1");
		date_default_timezone_set ( "Asia/Chongqing" );
		define ( 'APP_NAME', 'push' );
		define ( 'MODULE_NAME', 'alading');
		define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../..' );
		define ( 'SCRIPT_ROOT_PATH', ROOT_PATH. '/app/'. APP_NAME.'/script');
		define ( 'SCRIPT_IMPL_PATH', SCRIPT_ROOT_PATH . '/impl' );
		define ( 'SCRIPT_DATA_PATH', SCRIPT_ROOT_PATH . '/data/'. MODULE_NAME);
		define ( 'SCRIPT_LOG_PATH', ROOT_PATH . '/log/app/'.APP_NAME );
		define ( 'SCRIPT_CONF_PATH', SCRIPT_ROOT_PATH . '/conf/' );
		define ( 'APP_LIBS_PATH', ROOT_PATH . '/app/'.APP_NAME.'/libs' );
		define ( 'APP_SERVICES_PATH', ROOT_PATH . '/app/'.APP_NAME.'/service' );
		define ( 'APP_DL_PATH', ROOT_PATH . '/app/'.APP_NAME.'/dl' );
		define ( 'PHP_PATH', ROOT_PATH . '/php/bin/php');
	
		/**
		 * Bingo1 method autoload class
		*/
		$obj = Bingo_Load::getInstance(SCRIPT_DATA_PATH);
		$obj->add(APP_SERVICES_PATH);
		$obj->add(APP_LIBS_PATH);
		$obj->add(APP_DL_PATH);
		$obj->add(SCRIPT_IMPL_PATH);
	
		/**
		 * ����logid
		*/
		if (!defined('REQUEST_ID')){
			$requestTime = gettimeofday();
			define('REQUEST_ID', (intval($requestTime['sec'] * 100000 + $requestTime['usec'] / 10) & 0x7FFFFFFF));
		}
		if (function_exists('camel_set_logid')) {
			camel_set_logid(REQUEST_ID);
		}
		
		define ('SCRIPT_NAME', $appname);
		define ('LOG_SCRIPT', $appname);
		define ('LOG_FRAME', 'frame');
		Bingo_Log::init(array(
		LOG_FRAME => array(
		'file'  => SCRIPT_LOG_PATH . '/'.APP_NAME.'.log',
		'level' => 0x01|0x02|0x04|0x08,
		),
		LOG_SCRIPT => array(
		'file'  => SCRIPT_LOG_PATH . '/'.SCRIPT_NAME.'/'.SCRIPT_NAME.'.log',
		'level' => 0x01|0x02|0x04|0x08,
		),
		), LOG_SCRIPT);
		
		return true;
	}
}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
