<?php
require_once dirname(__file__)."/../Init.php";
require_once dirname(__file__) ."/../../libs/util/Bos.php";
define("OFFLINE_PATH","ftp://cp01-rd-forum-001.cp01.baidu.com/home/<USER>/crontab_rd/item/");
define("TMP_PATH",dirname(__file__)."/tmp/");

class Base{

	const TEMPLATE_ITEM_EDU = 1;
	
	public static $arrMapTemplatePath = array(
		self::TEMPLATE_ITEM_EDU => "edu",
	);

	protected $_templateId;
	

	/**
	 * [__construct description]
	 * @param integer $templateId [description]
	 */
	function __construct($templateId = 0){
		$this->_templateId = $templateId;
		
	}

	/**
	 * [excute description]
	 * @return [type] [description]
	 * @param 
	 */
	public function excute(){
		//自己在子类实现
		return true;
	}

	/**
	 * [isOrpBns description]
	 * @param  [type]  $host [description]
	 * @return boolean       [description]
	 */
	public static function isOrpBns(){
        $url = "http://127.0.0.1:794/instance";
		$arr_bns_info = json_decode(file_get_contents($url),true);
		$arr_first = $arr_bns_info['data'][0];
		if (empty($arr_first['belongToGroups']) && isset($arr_first['tag'])){
		    $tag = json_decode($arr_first['tag'],true);
		    if (isset($tag['tag']) && $tag['tag'] == "otp"){
		        echo "offline\n";
		        Bingo_Log::warning("offline");
		        return false;
		    }
		}
		echo "online\n";
		Bingo_Log::warning("online");
		return true;
    }

	/**
	 * [get_tmp_path description]
	 * @param  integer $templateId [description]
	 * @return [type]              [description]
	 */
	public function get_tmp_path($templateId = 0){
		if ($templateId == 0){
			$templateId = $this->_templateId;
		}
		if (!array_key_exists($templateId,self::$arrMapTemplatePath)){
			Bingo_Log::warning("param error templateId:$templateId");
			return "this dir not exist";
		}
		mkdir(TMP_PATH . self::$arrMapTemplatePath[$templateId],0777,true);
		return TMP_PATH. self::$arrMapTemplatePath[$templateId] . "/";
	}

	/**
	 * [load description]
	 * @param  [type] $templateId [description]
	 * @param  [type] $file       [description]
	 * @return [type]             [description]
	 */
	public function load($file,$templateId = 0){
		if (empty($file)){
			//不需要执行load操作
			Bingo_Log::warning("file is null");
			return "";
		}
		if ($templateId == 0){
			$templateId = $this->_templateId;
		}
		if (!array_key_exists($templateId,self::$arrMapTemplatePath)){
			Bingo_Log::warning("param error templateId:$templateId file:$file");
			return false;
		}
		$remotePath = OFFLINE_PATH . self::$arrMapTemplatePath[$templateId] . "/" . $file;
		$localPath = TMP_PATH . self::$arrMapTemplatePath[$templateId] . "/". $file;
		mkdir(TMP_PATH . self::$arrMapTemplatePath[$templateId],0777,true);
		Bingo_Log::notice("remotePath = $remotePath ,localPath = $localPath ");
		exec('wget '.escapeshellarg($remotePath).' -O '.escapeshellarg($localPath));
		return TMP_PATH . self::$arrMapTemplatePath[$templateId] . "/". $file;
	}

	/**
	 * [save description]
	 * @param  [type] $templateId [description]
	 * @param  [type] $localPath  [description]
	 * @return [type]             [description]
	 */
	public function save($file,$templateId = 0){
		if (empty($file) || $this->isOrpBns() == false){
			//不需要执行save操作
			return "";
		}
		if ($templateId == 0){
			$templateId = $this->_templateId;
		}
		if (!array_key_exists($templateId,self::$arrMapTemplatePath)){
			Bingo_Log::warning("param error templateId:$templateId file:$file");
			return false;
		}
		$remotePath = REMOTE_PATH . self::$arrMapTemplatePath[$templateId];
		$localPath = TMP_PATH . self::$arrMapTemplatePath[$templateId] . "/". $file;
		if(!file_exists($localPath)) 
		{
			Bingo_Log::warning("cant't find ".$localPath);
			return false;
		}
		//$res = Libs_Util_File::registerDataToOrp($localPath,$remotePath);
		//todo upload to bos
		/*if(false === $res)
		{
			Bingo_Log::warning("orp upload file failed! remote_path[$remotePath] local_path[$localPath]");
			return false;
		}*/
		return true;
	}


    /**
	 * 传可变参数
	 * 该函数只校验是否存在该值，对具体的值有效性不做校验
	 * @param unknown $arrInput, x, xx, xxx,
	 * @return  string $key
	 */
	public  function  checkParam(){

		$args = func_get_args();
		if(null == $args || empty($args) || null == $args[0] || empty($args[0]) || !is_array($args[0])){
			return false;
		}
		$arrInput = $args[0];
		$count = count($args);
		for ($i = 2; $i < $count; $i++){
			$j = $i-1;
			if(!isset($arrInput[$args[$i]]) || empty($arrInput[$args[$i]])){
				Bingo_Log::warning("cell is null ,row:[".$args[1]."], col:[".$j."]");
				return false;
			}
		}
		return true;
	}

	/**
     * 文件下载、上传失败报警
     */
    public function sendAlarm($file,$type) {
        $arrInput = array(
            'alarmTitle' => '[item] get '.$file.' failed',
            'alarmContent' => 'the len of '.$file.' is null',
        );

        if($type = 'get'){
            $arrInput = array(
                'alarmTitle' => '[item] get '.$file.' failed',
                'alarmContent' => 'the len of file is null',
            );
        }elseif($type = 'upload'){
            $arrInput = array(
                'alarmTitle' => '[item] upload '.$file.' failed',
                'alarmContent' => 'upload faild',
            );
        }
        $arrInput['emails'] = array('<EMAIL>');
        Libs_Util_Alarm::sendAlarm($arrInput);
    }



	/**
	 * brief 存储至bos
	 * @param
	 * @return
	 */
    public function saveToBos($arrInput){

    	if ($this->isOrpBns() == false){
			//不需要执行save操作
			Bingo_Log::notice('offline,can not save');
			return false;
		}
        if(empty($arrInput['objName']) || empty($arrInput['filePath'])){
            Bingo_Log::warning("param error. input=".serialize($arrInput));
            return false;
        }
        if(filesize($arrInput['filePath']) == 0){
            $this->sendAlarm($arrInput['filePath'],'get');
            Bingo_Log::warning($arrInput['filePath']." is null!");
            return false;
        }
        $arrOption = array();
        if(!empty($arrInput['content_type'])){
        	$arrOption['content_type'] = $arrInput['content_type'];
        }
        $fp = fopen($arrInput['filePath'],'r');
        $fileContent = fread($fp,filesize($arrInput['filePath']));
        $ref = null;
        $ret = libs_util_Bos::saveObjectFromString($arrInput['objName'],$fileContent,$ref,$arrInput['bucketName'] , $arrOption);
        if(false == $ret){
        	Bingo_Log::warning('call saveObjectFromString fail. objName = ['.$arrInput['objName'].'] , output ['.serialize($ret).']');
            $ret = libs_util_Bos::saveObjectFromString($arrInput['objName1'],$fileContent,$ref,$arrInput['bucketName'] , $arrOption);
        }
        if(false == $ret){
            $this->sendAlarm($objName,'upload');
            Bingo_Log::warning($objName." upload fail!");
            return false;
        }
        return true;

    }


}
