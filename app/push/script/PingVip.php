<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file PingVip.php
 * <AUTHOR>
 * @date 2017/4/02 16:35:26
 * @brief ������ṩ����������
 */

require_once dirname(__file__)."/Init.php";
script::init("PingVip");

/**
 * @param null
 * @return boolean
 */
function run(){
	$max_id = 0;
	$intRn = 50;
	$currentDate = date('Ymd',time());
	$has_more = 1;
	while($has_more){
		$arrInput = array(
			'id' => $max_id,
			'rn' => $intRn,
			'create_date' => $currentDate,
		);
		$arrResult = Tieba_Service::call('common','getDeliverThreadPush',$arrInput);
		Bingo_Log::notice(Bingo_String::array2json($arrResult));
		if ($arrResult['errno'] != 0){
			Bingo_Log::warning(Bingo_String::array2json($arrResult));
			return false;
		}
		$has_more = $arrResult['data']['has_more'];
		foreach($arrResult['data']['threadpush'] as $thread){
			if ($thread['id'] > $max_id){
				$max_id = $thread['id'];
			}
			$arrTransPack = array(
				'id' => $thread['id'],
				'command_no' => Service_Alading_Alading::THREAD_COMMIT_CMD,
				'thread_id' => $thread['thread_id'],
				'forum_id' => $thread['forum_id'],
				'ping_type' => Service_Alading_Lib_VippingNew::PING_TYPE_GOOD,
                'from_type' => Service_Alading_Lib_VippingNew::VIPPING_FROM_TYPE_PUGONGYING,
			);
			$ret = Service_Alading_Lib_VippingNew::sendVipping($arrTransPack);
			Bingo_Log::notice(Bingo_String::array2json($arrTransPack) .' result='. $ret);
			$thread['is_deliver'] = 1;
			$thread['is_success'] = (false === $ret) ? 0 : 1;
			$ret = Tieba_Service::call('common','updateDeliverThreadPush',$thread);
			if ($ret['errno'] != 0){
				Bingo_Log::warning(Bingo_String::array2json($ret));
			}
		}
	}
}

run();

exit(0);
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
