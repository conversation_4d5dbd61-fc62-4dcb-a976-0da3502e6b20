<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
/**
 * <AUTHOR>
 * @date 2017/11/22 16:35:26
 * @brief 每天上午十点对近两日阿拉丁生成的xml和大搜成功抓取的xml数量等进行统计
 * 
 **/

require_once dirname(__file__)."/Init.php";
script::init("ReportAlading");

/**
 * @param unknown $uid
 * @return 
 */
function run(){

	$danger = 0.05;//报警阈值

	$genNewKey = Service_Alading_AladingConfig::UPDATE_PUSH."_gen_xml_".date('Ymd',time()-86400);//昨天普通更新数据的key
 	$genOldKey = Service_Alading_AladingConfig::UPDATE_PUSH."_gen_xml_".date('Ymd',time()-86400*2);//前天普通更新数据的key
	$fetchNewKey = Service_Alading_AladingConfig::UPDATE_PUSH."_fetch_xml_".date('Ymd',time()-86400);//昨天普通更新抓取数据的key
 	$fetchOldKey = Service_Alading_AladingConfig::UPDATE_PUSH."_fetch_xml_".date('Ymd',time()-86400*2);//前天普通更新抓取数据的key
 	$genQuickNewKey = Service_Alading_AladingConfig::UPDATE_QUICK_PUSH."_gen_xml_".date('Ymd',time()-86400);//昨天快速更新数据的key
 	$genQuickOldKey = Service_Alading_AladingConfig::UPDATE_QUICK_PUSH."_gen_xml_".date('Ymd',time()-86400*2);//前天快速更新数据的key
	$fetchQuickNewKey = Service_Alading_AladingConfig::UPDATE_QUICK_PUSH."_fetch_xml_".date('Ymd',time()-86400);//昨天快速更新抓取数据的key
 	$fetchQuickOldKey = Service_Alading_AladingConfig::UPDATE_QUICK_PUSH."_fetch_xml_".date('Ymd',time()-86400*2);//前天快速更新抓取数据的key

	//online redis
	/*$input = array(
	    		array('forum_id' => 8751,'forum_name' => '武汉大学'),
	    		array('forum_id' => 2753,'forum_name' => '清华大学'),
	    		array('forum_id' => 93952,'forum_name' => '安徽理工大学'),
	);
	
	$arrInput = array('func' => 'SET', 'param' => array('key' => $fetchOldKey,'value' => 17));
	//$arrInput = array('func' => 'DEL', 'param' => array('key' => $key));
	$res = Tieba_Service::call('push', 'dlQuery', $arrInput, null, null, 'post', 'php', 'utf-8');*/


 	$genNewNum = intval(Dl_Alading_Alading::get($genNewKey));
	$genOldNum = intval(Dl_Alading_Alading::get($genOldKey));
	$fetchNewNum = intval(Dl_Alading_Alading::get($fetchNewKey));
	$fetchOldNum = intval(Dl_Alading_Alading::get($fetchOldKey));
	$genQuickNewNum = intval(Dl_Alading_Alading::get($genQuickNewKey));
	$genQuickOldNum = intval(Dl_Alading_Alading::get($genQuickOldKey));
	$fetchQuickNewNum = intval(Dl_Alading_Alading::get($fetchQuickNewKey));
	$fetchQuickOldNum = intval(Dl_Alading_Alading::get($fetchQuickOldKey));

 	/*$genNewNum = 11;
	$genOldNum = 11;
	$fetchNewNum = 10;
	$fetchOldNum = 10;
	$genQuickNewNum = 15;
	$genQuickOldNum = 15;
	$fetchQuickNewNum = 14;
	$fetchQuickOldNum = 14;*/

	//普通更新生成xml  start
	$diff = $genNewNum-$genOldNum;
	$diffPercent = 0;
	$isDanger = false;
	if($genOldNum == 0){
		$isDanger = true;
	}else{
		$diffPercent = $diff/$genOldNum;
	}
	if($isDanger === true || $diffPercent > $danger || $diffPercent < -$danger){
		$arrReport['alarmTitle'] = date('Ymd',time()-86400*2).'10-'.date('Ymd',time()).'09'."阿拉丁普通更新推送异常报警";
		$str = "普通更新生成xml概况：<br><br>";
	 	$str .= date('Ymd',time()-86400*2).'10-'.date('Ymd',time()-86400).'09普通更新生成xml：'.$genOldNum."<br>";
	 	$str .= date('Ymd',time()-86400).'10-'.date('Ymd',time()).'09普通更新生成xml：'.$genNewNum."<br><br>";
	 	$str .= "日同比增长:    $diffPercent  波动超过阈值$danger";
	 	$arrReport['alarmContent'] = $str;
	 	Libs_Util_Alarm::sendAlarm($arrReport);
	}
	//普通更新生成xml  end

	//普通更新抓取xml  start
	$diff = $fetchNewNum-$fetchOldNum;
	$diffPercent = 0;
	$isDanger = false;
	if($fetchOldNum == 0){
		$isDanger = true;
	}else{
		$diffPercent = $diff/$fetchOldNum;
	}
	if($isDanger === true || $diffPercent > $danger || $diffPercent < -$danger){
		$arrReport['alarmTitle'] = date('Ymd',time()-86400*2).'10-'.date('Ymd',time()).'09'."阿拉丁普通更新推送异常报警";
		$str = "普通更新抓取xml概况：<br><br>";
	 	$str .= date('Ymd',time()-86400*2).'10-'.date('Ymd',time()-86400).'09普通更新成功抓取xml：'.$fetchOldNum."<br>";
 		$str .= date('Ymd',time()-86400).'10-'.date('Ymd',time()).'09普通更新成功抓取xml：'.$fetchNewNum."<br><br>";
	 	$str .= "日同比增长:    $diffPercent  超过阈值$danger";
	 	$arrReport['alarmContent'] = $str;
	 	Libs_Util_Alarm::sendAlarm($arrReport);
	}
	//普通更新抓取xml  end

	//普通更新  生成xml-抓取xml 报警 start
	$diff = $genNewNum-$fetchNewNum;
	$diffPercent = 0;
	$isDanger = false;
	if($genNewNum == 0){
		$isDanger = true;
	}else{
		$diffPercent = $diff/$genNewNum;
	}
	if($isDanger === true || $diffPercent > $danger || $diffPercent < -$danger){
		$arrReport['alarmTitle'] = date('Ymd',time()-86400).'10-'.date('Ymd',time()).'09'."阿拉丁普通更新推送异常报警";
		$str = "普通更新概况：<br><br>";
	 	$str .= date('Ymd',time()-86400).'10-'.date('Ymd',time()).'09普通更新生成xml：'.$genNewNum."<br>";
 		$str .= date('Ymd',time()-86400).'10-'.date('Ymd',time()).'09普通更新成功抓取xml：'.$fetchNewNum."<br><br>";
	 	$str .= "失败占比:    $diffPercent  波动超过阈值$danger";
	 	$arrReport['alarmContent'] = $str;
	 	Libs_Util_Alarm::sendAlarm($arrReport);
	}
	//普通更新  生成xml-抓取xml 报警 end


	//快速更新生成xml  start
	$diff = $genQuickNewNum-$genQuickOldNum;
	$diffPercent = 0;
	$isDanger = false;
	if($genQuickOldNum == 0){
		$isDanger = true;
	}else{
		$diffPercent = $diff/$genQuickOldNum;
	}
	if($isDanger === true || $diffPercent > $danger || $diffPercent < -$danger){
		$arrReport['alarmTitle'] = date('Ymd',time()-86400*2).'10-'.date('Ymd',time()).'09'."阿拉丁快速更新推送异常报警";
		$str = "快速更新生成xml概况：<br><br>";
	 	$str .= date('Ymd',time()-86400*2).'10-'.date('Ymd',time()-86400).'09快速更新生成xml：'.$genQuickOldNum."<br>";
 		$str .= date('Ymd',time()-86400).'10-'.date('Ymd',time()).'09快速更新生成xml：'.$genQuickNewNum."<br><br>";
	 	$str .= "日同比增长:    $diffPercent  波动超过阈值$danger";
	 	$arrReport['alarmContent'] = $str;
	 	Libs_Util_Alarm::sendAlarm($arrReport);
	}
	//快速更新生成xml  end

	//快速更新抓取xml  start
	$diff = $fetchQuickNewNum-$fetchQuickOldNum;
	$diffPercent = 0;
	$isDanger = false;
	if($fetchQuickOldNum == 0){
		$isDanger = true;
	}else{
		$diffPercent = $diff/$fetchOldNum;
	}
	if($isDanger === true || $diffPercent > $danger || $diffPercent < -$danger){
		$arrReport['alarmTitle'] = date('Ymd',time()-86400*2).'10-'.date('Ymd',time()).'09'."阿拉丁快速更新推送异常报警";
		$str = "快速更新抓取xml概况：<br><br>";
	 	$str .= date('Ymd',time()-86400*2).'10-'.date('Ymd',time()-86400).'09快速更新成功抓取xml：'.$fetchQuickOldNum."<br>";
 		$str .= date('Ymd',time()-86400).'10-'.date('Ymd',time()).'09快速更新成功抓取xml：'.$fetchQuickNewNum."<br><br>";
	 	$str .= "日同比增长:    $diffPercent  波动超过阈值$danger";
	 	$arrReport['alarmContent'] = $str;
	 	Libs_Util_Alarm::sendAlarm($arrReport);
	}
	//快速更新抓取xml  end


	//快速更新  生成xml-抓取xml 报警 start
	$diff = $genQuickNewNum-$fetchQuickNewNum;
	$diffPercent = 0;
	$isDanger = false;
	if($genNewNum == 0){
		$isDanger = true;
	}else{
		$diffPercent = $diff/$genQuickNewNum;
	}
	if($isDanger === true || $diffPercent > $danger || $diffPercent < -$danger){
		$arrReport['alarmTitle'] = date('Ymd',time()-86400).'10-'.date('Ymd',time()).'09'."阿拉丁快速更新推送异常报警";
		$str = "快速更新概况：<br><br>";
	 	$str .= date('Ymd',time()-86400).'10-'.date('Ymd',time()).'09普通更新生成xml：'.$genQuickNewNum."<br>";
 		$str .= date('Ymd',time()-86400).'10-'.date('Ymd',time()).'09普通更新成功抓取xml：'.$fetchQuickNewNum."<br><br>";
	 	$str .= "失败占比:    $diffPercent  波动超过阈值$danger";
	 	$arrReport['alarmContent'] = $str;
	 	Libs_Util_Alarm::sendAlarm($arrReport);
	}
	//快速更新  生成xml-抓取xml 报警 end

}
run();

exit(0);
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
