<?php

// pb tdk 修改离线脚本
// 从词表读取excel文件，解析文件
// 修改pb动态属性，添加tdk描述
// 回写词表，解析标识
// 回写redis，记录文件执行记录 TODO 可迁到mysql，暂时不做

/**
 *
 * <AUTHOR>
 * @desc
 */
ini_set ( "memory_limit", "-1" );
define('MODULE_NAME', 'push');
date_default_timezone_set ( "Asia/Chongqing" );

define ( 'APP_NAME', 'push' );
define ( 'SCRIPT_NAME', 'PbTdkUpdate' );
define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../..' );
define ( 'SCRIPT_ROOT_PATH', ROOT_PATH . '/app/' . APP_NAME . '/script' );
define ( 'SCRIPT_LOG_PATH', ROOT_PATH . '/log/app/' . APP_NAME );
define ( 'SCRIPT_CONF_PATH', ROOT_PATH . '/conf/app/' . APP_NAME );
define('BASE_PATH', dirname(__FILE__));
// 地址文件
define ( 'SCRIPT_DATA_PATH', ROOT_PATH . '/data/app/' . APP_NAME );

define ('TDK_MULTI_FLAG','tdk_multi_flag');

set_time_limit(0);

// 加载excel库
echo date('H:i:s')," ROOT_PATH:".ROOT_PATH."\n";
echo date('H:i:s')," BASE_PATH:".BASE_PATH."\n";

include_once BASE_PATH . '/excellib/PHPExcel.php';
include_once BASE_PATH . '/excellib/PHPExcel/IOFactory.php';

$requestTime = gettimeofday ();
if (! defined ( 'REQUEST_ID' )) {
    define ( 'REQUEST_ID', (intval ( $requestTime ['sec'] * 100000 + $requestTime ['usec'] / 10 ) & 0x7FFFFFFF) );
}

if (function_exists ( 'camel_set_logid' )) {
    camel_set_logid ( REQUEST_ID );
}

// Bingo_Log::init ( array (
//     LOG_SCRIPT => array (
//         'file' => SCRIPT_LOG_PATH . '/' . SCRIPT_NAME . '/' . SCRIPT_NAME . "_$requestTime.log",
//         'level' => 0x01 | 0x02 | 0x04 | 0x08,
//     ),
// ),
//     LOG_SCRIPT );


$totalStartTime = microtime(true);

// 1. 读取词表
$handleWordServer = Wordserver_Wordlist::factory();
$intCount = 10;
$arrParam = array(
    'table' => 'tb_wordlist_redis_pbtdk_multi', // 单条数据
    'start' => 0,
    'stop'  => $intCount,
);

$arrRet = $handleWordServer->getTableContents($arrParam);
if ($arrRet == false || $arrRet['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
    echo "get wordlist fail.\n [".json_encode($arrRet)."]";
    exit(1);
}

// 获取处理标志
$arrFlag = array();
getMultiFlag($arrFlag);

// mkdir
@exec("mkdir -p ".SCRIPT_DATA_PATH); 

foreach ($arrRet['ret'] as $key=>$value) {
    if ($arrFlag[$key] == 2) {
        echo "all ready done.[$key]\n";
        continue;
    } 

    echo "key:$key,value:".json_encode($value)."\n";

    setDoingFlag($key);
    $strUrl = $value;
    if (strlen($strUrl) < 5) {
        echo "url path error.[$strUrl]\n";
        continue;
    }
    // 下载文件
    $option  = array(
        'max_response_size' => 10485760, //10M
    );
    $objFetch = Orp_FetchUrl::getInstance($option);
    $strContent = $objFetch->get($strUrl);

    // 保存文件
    echo "url:[$strUrl]\n";
    $strPath = SCRIPT_DATA_PATH."/".basename($strUrl);
    file_put_contents($strPath,$strContent);
    echo "save file in [$strPath]\n";

    $arrData = array();
    // 解析文件
    loadExcel($strPath,$arrData);
    // var_export($arrData);
    // 存储
    foreach ($arrData as $tdkItem) {
        $arrPostParam = array(
            'thread_ids'=>array($tdkItem['A']),
            'forum_id'=>0,
            'need_abstract'=>0,
            'need_photo_pic'=>0,
            "need_forum_name" => 1,
        );
        $arrPostRet = Tieba_Service::call('post', 'mgetThread', $arrPostParam, null, null, 'post', 'php', 'utf-8');
        if (false == $arrPostRet || $arrPostRet['errno']!==Tieba_Errcode::ERR_SUCCESS) {
            echo(sprintf("call post mgetThread fail.[%s] [%s]\n",serialize($arrPostParam),serialize($arrPostRet)));
            continue;
        }

        // 原始title
        $tid = intval($tdkItem['A']);
        $strRawTitle = strval($arrPostRet['output']['thread_list'][$tid]['title']);
        $strTdkTitle = strval($tdkItem['B']);
        // 吧名
        $strForumName = strval($arrPostRet['output']['thread_list'][$tid]['forum_name']);

        $strTitle = $strRawTitle."_".$strTdkTitle."_".$strForumName."_"."百度贴吧";
        $arrTdkParam = array(
            'url'=>'http://tieba.baidu.com/p/'.$tdkItem['A'],
            'extid' => $tdkItem['A'],
            'type' => 1,
            'tdk_desc' => $tdkItem['C'],
            'tdk_keywords'=>$tdkItem['D'],
            'tdk_title'=>$strTitle,
        );

        usleep(50000); // 50ms
        // var_export($arrTdkParam);
        $arrRet = Tieba_Service::call('push', 'addTdkRecord', $arrTdkParam, null, null, 'post', 'php', 'gbk');
        echo ".";
    }
    echo "\n";

    // 删除文件
    @exec("rm $strPath");

    setDoneFlag($key);
}
$totalEndTime = microtime(true);
echo 'total cost time: ' , sprintf('%.4f',$callTime) , " seconds\n";
echo "all done.\n";
exit(0);

/**
 * @param
 * @return 
 */
function getMultiFlag(&$arrFlag) {
    $arrRedisParam = array(
        "call" => "HGETALL",
        "param" => array(
            'key'=>TDK_MULTI_FLAG,
        ),
    );
    $redisRet = Tieba_Service::call('push', 'redisQuery', $arrRedisParam, null, null, 'post', 'php', 'gbk');
    if (false == $redisRet) {
        echo "redis query fail.\n";
        return false;
    }
    foreach ($redisRet['data'][TDK_MULTI_FLAG] as $key =>$value) {
        $arrFlag[$value['field']] = $value['value'];
    }
    // var_export($arrFlag);
}

/**
 * @param
 * @return
 */
function setDoingFlag($key) {
    $arrRedisParam = array(
        'call'=>'HSET',
        'param' => array(
            'key' => TDK_MULTI_FLAG,
            'field' => $key,
            'value' => 1,
        )
    );
    $redisRet = Tieba_Service::call('push', 'redisQuery', $arrRedisParam, null, null, 'post', 'php', 'gbk');
    // var_export($redisRet);
    if (false == $redisRet ) {
        echo "fail to set redis [".serialize($arrRedisParam)."] [".json_encode($redisRet)."]\n";
    }
}

/**
 * @param
 * @return 
 */
function setDoneFlag($key) {
    $arrRedisParam = array(
        'call'=>'HSET',
        'param' => array(
            'key' => TDK_MULTI_FLAG,
            'field' => $key,
            'value' => 2,
        )
    );
    // var_export($arrRedisParam);
    $redisRet = Tieba_Service::call('push', 'redisQuery', $arrRedisParam, null, null, 'post', 'php', 'gbk');
    if (false == $redisRet ) {
        echo "fail to set redis [".serialize($arrRedisParam)."] [".json_encode($redisRet)."]\n";
        $redisRet = Tieba_Service::call('push', 'redisQuery', $arrRedisParam, null, null, 'post', 'php', 'gbk');
    }
}

/**
 * 读取excel
 * @param
 * @param
 * @return 
 */
function loadExcel($strPath,&$arrData) {
    // echo date('H:i:s') , " Load from Excel2007 file\n";
    $callStartTime = microtime(true);
    $objPHPExcel = PHPExcel_IOFactory::load($strPath);
    $callEndTime = microtime(true);
    $callTime = $callEndTime - $callStartTime;
    echo 'Call time to read Workbook was ' , sprintf('%.4f',$callTime) , " seconds\n";
    // Echo memory usage
    // echo date('H:i:s') , ' Current memory usage: ' , (memory_get_usage(true) / 1024 / 1024) , " MB\n";

    // 读取内容
    $objPHPExcel->setActiveSheetIndex(0);
    // 遍历
    $objWorksheet = $objPHPExcel->getActiveSheet();
    $arrData = $objPHPExcel->getActiveSheet()->toArray(null,false,false,true); 
    // var_export($arrData);
}
