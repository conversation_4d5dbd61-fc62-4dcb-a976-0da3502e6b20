<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file PingVip.php
 * <AUTHOR>
 * @date 2017/4/02 16:35:26
 * @brief ��ش���
 */
define ('BASEPATH',dirname(__FILE__));
define ('DATAPATH',BASEPATH."/data/vipping");
require_once dirname(__file__)."/Init.php";
require_once dirname(__file__)."/Mail_MSGLib/SendMail.php";
script::init("PingVip_omp");
run($argv);//$argv[1]:"type@MOD";$argv[2]:"qps_hour_1";$argv[3]:�켶20/Сʱ��40;$argv[4]:�켶86400/Сʱ��3600
/**
 * @param null
 * @return boolean
 */
function run($argv)
{
    $argv[1] = $argv[1]."|from@1"; 
    //�����ֻ���
    $msg_mobile = array('15901032674',);
    //����
    $arrReceiver = array(
        0 => '<EMAIL>',
        //1 => '<EMAIL>',
    	//2 => '<EMAIL>',
    );
    /*
    �켶��أ��ӵ�ǰʱ�����ǰȡ24Сʱ���������ܺͣ�����ʷ7�조ͬ1ʱ��������ƽ��ֵ���ȶԣ���/��������ֵ20%�򱨾���
    Сʱ����أ��ӵ�ǰʱ�����ǰȡ1Сʱ���������ܺͣ�����ʷ7�조ͬ1ʱ��������ƽ��ֵ���ȶԣ���/��������ֵ40%�򱨾�
    */
    //ȡ��ǰʱ�����ǰȡ24Сʱ���������ܺ�
    $url_stime = time()-$argv[4];
    $url_etime = time();
    $url_key = $argv[1];//"type@ADD";
    $url_value = $argv[2];//"qps_hour_1";
    echo date("Y-m-d H:i:s",$url_stime)."~~".date("Y-m-d H:i:s",$url_etime)."\n";
    $result_today = getData($url_stime,$url_etime,$url_key,$url_value);
    $result_today = $result_today['data'][$url_key.":".$url_value];
    echo "\n".$result_today."\n";
    //ȡ��ʷ7�조ͬ1ʱ��������ƽ��ֵ��
    $sum = 0;
    for($i=1;$i<=7;$i++)
    {
        $url_stime = time()-86400*$i-$argv[4];
        $url_etime = time()-86400*$i;
        $url_key = $argv[1];//"object@1";
        $url_value = $argv[2];//"qps_hour_1";
        echo "\n".date("Y-m-d H:i:s",$url_stime)."~~".date("Y-m-d H:i:s",$url_etime)."\n";
        $result_preday = getData($url_stime,$url_etime,$url_key,$url_value); 
        //print_r($result_preday['data']);
        echo "\n".$result_preday['data'][$url_key.":".$url_value];
        $sum += $result_preday['data'][$url_key.":".$url_value];
    }
    $avg = $sum/7 ;
    echo "\navg===".$avg."\n";//��ʷ7�조ͬ1ʱ��������ƽ��ֵ��
    $chazhi = ($result_today > $avg ? $result_today - $avg : $avg - $result_today)/$avg;
    $chazhi = round($chazhi * 100);//"%"
    echo "diffrate==".$chazhi."%\n";
    if($argv[3]==null)
    {
        $argv[3] = 20;
    }
    //var_dump($argv[3]);//Сʱ����20�����Ӽ���40
    if($chazhi>$argv[3])
    {
        $msg_text = ($result_today > $avg ? '+' : '-');
        $title = array("20"=>"day","40"=>"hour");
        //������
        $arrInput['msg_content'] = "(".$title[$argv[3]].")".date("Y-m-d H:i")." ".$url_key."::".$url_value.$msg_text.$chazhi."%(".$argv[3]."%)";
        foreach($msg_mobile as $key => $val)
        {
            $arrInput['user_phone'] = $val;
            $msg_ret = sendMsgByUserPhone($arrInput); 
            var_dump($msg_ret['message']);
        }
        $email_text = ($result_today > $avg ? '�����' : '������');
        //���ʼ�
        $arrEmail = array (
                'sender' => '<EMAIL>',
                'receiver' => $arrReceiver[0],
                'title' => 'push����omp���'.$title[$argv[3]].'����__'.$url_key.":".$url_value.'['.date("Y-m-d H:i:s")."]",
                'mail_content' => date("Y-m-d H:i:s")."�۲쵽<br><br>".$url_key.":".$url_value."����ʷ7��ͬ1ʱ��������ƽ��ֵ�ȶԣ�".$email_text.$chazhi."%��<br><br>�����ֵΪ".$argv[3]."%����".$title[$argv[3]]."������",
        );
        $htmlText = "text"; //����html��ʽ����mailTextΪ��
        $mail = new SendMail ();
        $mail->setTo ( $arrEmail ['receiver'] );
        $mail->setFrom ( $arrEmail ['sender'] );
        $mail->setCharset ( "gbk" );
        $mail->setSubject ( $arrEmail ['title'] );
        $mail->setText ( $htmlText );
        $mail->setHTML ( $arrEmail['mail_content'] );
        $result = $mail->send ();
        echo "sendMail result=";
        var_dump($result);
        if ($result == false) {
            Bingo_Log::warning ( "send mail fail.mailcontent:[".$strMailContent.']');
            var_dump ( "send mail fail." );
        }
    }
}

/**
 * [getData description]
 * @param  [type] $url_stime [description]
 * @param  [type] $url_etime [description]
 * @param  [type] $url_key   [description]
 * @param  [type] $url_value [description]
 * @return [type]            [description]
 */
function getData($url_stime,$url_etime,$url_key,$url_value)
{
    $url = "http://omp.baidu.com/ompui/api/commondata?method=get_stats&module=tieba|pc_push|service_push&key=".$url_key."&type=10s&value=".$url_value."&stime=".$url_stime."&etime=".$url_etime."&merge=true";
    echo "key=".$url_key."&type=10s&value=".$url_value."&stime=".$url_stime."&etime=".$url_etime."&merge=true";
    $conf = array(
            'timeout'=>1000,
    ); 
    $obj = orp_FetchUrl::getInstance($conf);
    $response_today = $obj->get($url);
    $result_today = json_decode($response_today,true);
    $retry = 0;
    while($retry < 3 && ($result_today['errno'] != 0 || !$result_today)){
        sleep(1);
        $retry++;
        $obj = orp_FetchUrl::getInstance($conf);
        $response_today = $obj->get($url);
        $result_today = json_decode($response_today,true);
    }
    return $result_today;
}

/**
 * [sendMsgByUserPhone description]
 * @param  [type] $arrInput [description]
 * @return [type]           [description]
 */
function sendMsgByUserPhone($arrInput) 
{
    print_r($arrInput);
    if (!isset($arrInput['user_phone']) || !isset($arrInput['msg_content'])) {
        Bingo_Log::warning("param error.[".serialize($arrInput)."]");
        return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
    }

    // �绰���� & ��������
    $intPhone = strval($arrInput['user_phone']);
    $strContent = strval($arrInput['msg_content']);

    //$url = "http://emsgtest.baidu.com/api/v1/sendSms.json";
    $url = "http://emsg.baidu.com/api/v1/sendSms.json";

    $bussCode = 'tb_mes_DL';
    $username = 'deeplink';
    $password = '890980';

    $bodyStr = '{"msgDest":'. $intPhone .',"msgContent":"' . $strContent . '"}';
    $sign = md5($bussCode . $username. $password. $bodyStr);
    $header = array(
        'Content-Type:application/json',
        'signature:'.$sign,
        'busscode:'.$bussCode,
        'username:'.$username,
    );
    $ch = curl_init();
    curl_setopt($ch,CURLOPT_HTTPHEADER,$header);
    curl_setopt($ch, CURLOPT_URL, $url);

    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_NOBODY, 0);

    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $bodyStr);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); //��curl����ֵ��ֵ������
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_VERBOSE, 0);
    // curl

    Bingo_Log::warning("header:".var_export($header,1));
    Bingo_Log::warning("body:".$bodyStr);

    Bingo_Timer::start("msg_platform_query");
    $ret = curl_exec($ch);
    Bingo_Timer::end("msg_platform_query");

    Bingo_Log::warning("ret_raw:".$ret);
    $ret = json_decode($ret, true);
    Bingo_Log::warning("ret:".var_export($ret,1));

    // �ر���Դ
    curl_close($ch);

    if( $ret == false || $ret['result'] != 1000 ) {
        Bingo_Log::warning("call sendMsg fail.".var_export($ret,1));
        return $ret;
    }
    return $ret;
}
exit(0);
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
