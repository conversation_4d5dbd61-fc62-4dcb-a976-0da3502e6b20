<?php

// function __autoload($strClassName)
// {
//     require_once str_replace('_', '/', $strClassName) .'.php';
// }



$baseUrl = "http://rmb.chinacqgd.com/baidurxzjphp/userdatequeryt.php";
$api_key = "3d6da266013d8492c28801117b373e98";
$secret_key = "8aa6fdb33d6da266013d7b373e980112";
$server_id = "8001";
$timestamp = "2015-04-21 21:46:53";
$user_id = 169662199;
$str = $secret_key."api_key".$api_key."server_id".$server_id."timestamp".$timestamp."user_id".$user_id;

$md5 = md5($str);

// $md5 = bin2hex($md5);

echo $str;
echo "\n";
echo $md5;
echo "\n";
echo  date('Y-m-d H:i:s',"14946397810");
// $arrInput = array(
//     'baseUrl' => $baseUrl,
//     'sign' =>$md5
// );

// $ret = Tieba_Service::call("wanba","role",$arrInput);

// var_dump($ret);