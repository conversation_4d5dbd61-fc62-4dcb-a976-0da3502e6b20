<?php
/**
 * 游戏用户申领等级勋章通用入口
 * @authors shiyuxin
 * @date    2015-01-30 11:25:00
 * @version $Id$
 */

class ajaxOrderAction extends Util_Base {

   const ACTIVITY_END_TIME = '2015-02-10 00:00:00';

    const ORDER_URL = "http://tieba.baidu.com/wanba/submit/order/succesRet?tb_third_order_id=";


    public function execute() {
        $third_order_id = Bingo_Http_Request::get('tb_third_order_id',"");

        $ret = array();

        //get order info
        $arrOrderInput = array(
            "bank_oid" =>  $third_order_id,
            "pay_name" => Util_WanbaCode::ORDER_PAY_NAME_TDOU,
        );
        $orderInfoRet = Tieba_Service::call('wanba','findOrderByBankOid',$arrOrderInput,null,null,'php','post','utf-8','local');

        if (false != $orderInfoRet && isset($orderInfoRet["data"])) {
            $order = $orderInfoRet["data"];
            if ($order["orderStatus"] == Util_WanbaCode::ORDER_STATUS_SUCCESS) {
                $ret['orderStatus'] = $order["orderStatus"];
                $ret['orderUrl'] = self::ORDER_URL.$third_order_id;
                return $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,false,$ret);
            } else {
                $ret['orderStatus'] = $order["orderStatus"];
                $ret['orderUrl'] = self::ORDER_URL.$third_order_id;
                return $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS,false,$ret);

            }
        } else {
            $ret['orderStatus'] = 0;
            $ret['orderUrl'] = self::ORDER_URL.$third_order_id;
            return $this->_jsonRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL,false,$ret);
        }

    }
    
    
}