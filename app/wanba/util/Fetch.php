<?php
class Util_Fetch {

    public static function callGamePlat($requestUrl,$input){

        $proxy = Orp_FetchUrl::getInstance(array(
            'timeout' => 2000,
            'conn_timeout' => 2000,
            'max_response_size' => 2048000,
        ));

        $ret = $proxy->post($requestUrl,$input);
        
        Bingo_log::warning("--------ret".print_r($ret, true));
        
        if($ret == false){
            Bingo_Log::warning('call game fail ,the method = '.$method. ' the input'.serialize($input));
            return false;
        }
        $out = json_decode($ret,true);
        $out = Bingo_Encode::convert($out, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
        if($out === false){
            Bingo_Log::warning('json decode fail , the out = '.$out);
            return false;
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'output' => $ret,
        );
        return $arrOutput;
    }
}
