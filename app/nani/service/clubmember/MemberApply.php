<?php
/**
 * Created by PhpStorm.
 * User: wangyalong02
 * Date: 2018/7/19
 * Time: 下午4:36
 */

class Service_ClubMember_MemberApply extends Service_Libs_Base
{

    /**
     * @param array
     * @return array
     */
    const CLUB_APPLY_HSET_KEY = 'CLUB_APPLY_HSET_KEY';

    const APPLY_STATUS_SUCCESS = 0;  // 申请成功
    const APPLY_STATUS_REAPPLY = 1;  // 重复申请
    const APPLY_STATUS_MEMBER = 2; // 已在社团

    const AUDIT_STATUS_SUCCESS = 1; //审核通过
    const AUDIT_STATUS_REJECT = 2; //拒绝申请
    const AUDIT_STATUS_PENDING = 0; //申请未处理
    const AUDIT_STATUS_AUTO_EXPIRE = 3; // 已经过期
    const AUDIT_STATUS_AUTO_PASS = 4; //公开社团,自动通过

    /**
     * @param $arrInput
     * @return array
     */

    public static function applyGroupMember($arrInput)
    {

        // check the param
        if (intval($arrInput['user_id']) <= 0 || intval($arrInput['club_id']) <= 0) {
            Bingo_Log::warning('param error, input = ' . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 默认申请成功
        $arrOutput = array(
            'status' => self::APPLY_STATUS_SUCCESS,  // 0 申请成功, 1重复申请, 2已在社团
        );

        // 首先判断是不是已经在社团
        $arrClubMember = array(
            'cond' => array(
                'club_id' => $arrInput['club_id'],
                'user_id' => $arrInput['user_id'],
            ),
        );

        $arrDbOutput = Dl_Clubmember_ClubMember::select($arrClubMember);
        if (false === $arrDbOutput || Tieba_Errcode::ERR_SUCCESS != $arrDbOutput['errno']) {
            Bingo_Log::warning("call Dl_Club_ClubMember::select failed, input = " . serialize($arrClubMember) . ' output = ' . serialize($arrDbOutput));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        // 已经在社团
        if (count($arrDbOutput['data']) > 0) {
            $arrOutput['status'] = self::APPLY_STATUS_MEMBER;
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
        }

        // 判断是不是重复申请
        $arrClubMemberApply = array(
            'field' => array(
                'count(1)'
            ),
            'cond' => array(
                'club_id' => $arrInput['club_id'],
                'user_id' => $arrInput['user_id'],
                'create_time' => array(
                    'val' => time() - 3 * 86400, // 3天之内
                    'opt' => '>',
                ),
                'status' => 0,
            ),
        );
        $arrDbOutput = Dl_Clubmember_MemberApply::select($arrClubMemberApply);
        if (false === $arrDbOutput || Tieba_Errcode::ERR_SUCCESS != $arrDbOutput['errno']) {
            Bingo_Log::warning("call Dl_Club_MemberApply::select failed, input = " . serialize($arrClubMemberApply) . ' output = ' . serialize($arrDbOutput));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        // 重复申请
        if (intval($arrDbOutput['data'][0]['count(1)']) > 0) {
            $arrOutput['status'] = self::APPLY_STATUS_REAPPLY;
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
        }

        //不满足上面两个条件，即申请成功
        $arrDbInput = array(
            'field' => array(
                'club_id' => $arrInput['club_id'],
                'user_id' => $arrInput['user_id'],
                'apply_content' => strval($arrInput['apply_content']),
                'create_time' => time(),
                'update_time' => time(),
            )
        );

        $arrDbOutput = Dl_Clubmember_MemberApply::insert($arrDbInput);
        if (false === $arrDbOutput || Tieba_Errcode::ERR_SUCCESS != $arrDbOutput['errno']) {
            Bingo_Log::warning("call Dl_Club_ClubMember::insert failed, input = " . serialize($arrDbInput) . ' output = ' . serialize($arrDbOutput));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrOutput['apply_id'] = $arrDbOutput['data'];
        //插入一条消息记录
        $arrMsgInput = array(
            'field' => array(
                'club_id'     => $arrInput['club_id'],
                'apply_uid'   => $arrInput['user_id'],
                'create_time' => time(),
            )
        );
        $arrDbOutput = Dl_ClubApplyMsg_ClubApplyMsg::insert($arrMsgInput);
        if (false === $arrDbOutput || Tieba_Errcode::ERR_SUCCESS != $arrDbOutput['errno']) {
            Bingo_Log::warning("call Dl_Club_ClubMember::insert failed, input = " . serialize($arrMsgInput) . ' output = ' . serialize($arrDbOutput));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function applyJoinPublicClub($arrInput)
    {

        // check the param
        if (intval($arrInput['user_id']) <= 0 || intval($arrInput['club_id']) <= 0) {
            Bingo_Log::warning('param error, input = ' . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 默认申请成功
        $arrOutput = array(
            'status' => self::APPLY_STATUS_SUCCESS,  // 0 申请成功, 1重复申请, 2已在社团
        );

        // 首先判断是不是已经在社团
        $arrClubMember = array(
            'cond' => array(
                'club_id' => $arrInput['club_id'],
                'user_id' => $arrInput['user_id'],
            ),
        );

        $arrDbOutput = Dl_Clubmember_ClubMember::select($arrClubMember);
        if (false === $arrDbOutput || Tieba_Errcode::ERR_SUCCESS != $arrDbOutput['errno']) {
            Bingo_Log::warning("call Dl_Club_ClubMember::select failed, input = " . serialize($arrClubMember) . ' output = ' . serialize($arrDbOutput));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        // 已经在社团
        if (count($arrDbOutput['data']) > 0) {
            $arrOutput['status'] = self::APPLY_STATUS_MEMBER;
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
        }

        // 判断是不是重复申请
        $arrClubMemberApply = array(
            'field' => array(
                'count(1)'
            ),
            'cond' => array(
                'club_id' => $arrInput['club_id'],
                'user_id' => $arrInput['user_id'],
                'create_time' => array(
                    'val' => time() - 3 * 86400, // 3天之内
                    'opt' => '>',
                ),
                'status' => 0,
            ),
        );
        $arrDbOutput = Dl_Clubmember_MemberApply::select($arrClubMemberApply);
        if (false === $arrDbOutput || Tieba_Errcode::ERR_SUCCESS != $arrDbOutput['errno']) {
            Bingo_Log::warning("call Dl_Club_MemberApply::select failed, input = " . serialize($arrClubMemberApply) . ' output = ' . serialize($arrDbOutput));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        // 重复申请
        if (intval($arrDbOutput['data'][0]['count(1)']) > 0) {
            $arrOutput['status'] = self::APPLY_STATUS_REAPPLY;
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
        }
        
        //不满足上面两个条件，即申请成功

        // 申请公开社团,有 4 个 步骤,前 3 个 需要写个事务

        // 开启事务
        if (!Dl_Club_ClubInfo::startTransaction()) {
            Bingo_Log::warning('call Dl_Club_ClubInfo::startTransaction fail, input:[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $intErrorNo = Tieba_Errcode::ERR_SUCCESS;
        $intApplyId = 0;
        do {
            // 1、往申请表写一条申请记录,状态是 自动通过,审核人员写星主
            $arrDbInput = array(
                'field' => array(
                    'club_id'       => $arrInput['club_id'],
                    'user_id'       => $arrInput['user_id'],
                    'apply_content' => strval($arrInput['apply_content']),
                    'create_time'   => time(),
                    'update_time'   => time(),
                    'op_uid'        => $arrInput['create_uid'],
                    // 审核人员是星主
                    'status'        => self::AUDIT_STATUS_AUTO_PASS,
                ),
            );

            $arrDbOutput = Dl_Clubmember_MemberApply::insert($arrDbInput);
            if (false === $arrDbOutput || Tieba_Errcode::ERR_SUCCESS != $arrDbOutput['errno']) {
                Bingo_Log::warning("call Dl_Clubmember_MemberApply::insert failed, input = " . serialize($arrDbInput) . ' output = ' . serialize($arrDbOutput));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            $intApplyId = $arrDbOutput['data'];

            // 2、往申请记录表写一条记录,
            $arrMsgInput = array(
                'field' => array(
                    'club_id'     => $arrInput['club_id'],
                    'apply_uid'   => $arrInput['user_id'],
                    'create_time' => time(),
                )
            );
            $arrDbOutput = Dl_ClubApplyMsg_ClubApplyMsg::insert($arrMsgInput);
            if (false === $arrDbOutput || Tieba_Errcode::ERR_SUCCESS != $arrDbOutput['errno']) {
                Bingo_Log::warning("call Dl_ClubApplyMsg_ClubApplyMsg::insert failed, input = " . serialize($arrMsgInput) . ' output = ' . serialize($arrDbOutput));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            // 3、把用户加到成员表中

            // 申请成功，拉入正式表

            $arrParam = array(
                'field' => array(
                    'club_id' => $arrInput['club_id'],
                    'user_id' => $arrInput['user_id'],
                    'create_time' => time(),
                ),
            );

            $arrRet = Dl_Clubmember_ClubMember::insert($arrParam); // 搞成唯一索引
            if(false === $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                Bingo_Log::warning("call Dl_Club_ClubMember::insert failed, input = ".serialize($arrParam).' output = '.serialize($arrRet));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            // add user club
            $arrParam = array(
                'field' => array(
                    'club_id'     => $arrInput['club_id'],
                    'user_id'     => $arrInput['user_id'],
                    'is_creator'  => 0,
                    'create_time' => time(),
                ),
            );
            $arrRet  = Dl_Club_UserClub::insert($arrParam); // 搞成唯一索引
            if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                Bingo_Log::warning(sprintf("insert the data to user club failed. [input = %s][output = %s]", serialize($arrParam), serialize($arrRet)));
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

            // 提交事务
            if (!Dl_Club_ClubInfo::commit()) {
                Bingo_Log::warning('call Dl_Club_ClubInfo::commit fail, input:[' . serialize($arrInput) . ']');
                $intErrorNo = Tieba_Errcode::ERR_DB_QUERY_FAIL;
                break;
            }

        } while (0);

        if (Tieba_Errcode::ERR_SUCCESS !== $intErrorNo) {
            if (!Dl_Club_ClubInfo::rollback()) {
                Bingo_Log::warning('call Dl_Club_ClubInfo::rollback fail, input:[' . serialize($arrInput) . ']');
            }

            return self::_errRet($intErrorNo);
        }

        // update the club member
        Service_Club_ClubInfo::updateClubMemberNum($arrInput['club_id']);

        // 4、全都OK 了,需要发个 push

        // 获取到所有的管理员

        $arrRetRole = Tieba_Service::call('nani', 'getClubRole', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRetRole['errno'] || $arrRetRole == false) {
            Bingo_Log::warning(sprintf("call nani:getClubRole failed. [input = %s][output = %s]", serialize($arrInput), serialize($arrRetRole)));
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);

        }
        $arrRole = $arrRetRole['data'];

        $arrAllManager = array();

        if (!empty($arrRole['manager'])) {
            $arrAllManager = array_merge($arrAllManager,$arrRole['manager']);
        }
        if (!empty($arrRole['vice_manager'])) {
            $arrAllManager = array_merge($arrAllManager,$arrRole['vice_manager']);
        }

        if(!empty($arrAllManager)){

            $tmpClubName = $arrInput['club_name'];
            $tmpClubId = $arrInput['club_id'];
            $tmpUserId = $arrInput['user_id'];

            foreach ($arrAllManager as $manager) {
                $arrPushInput = array(
                    'user_id'         => $manager,
                    'op_uid'          => 3559714525,
                    // 官方uid
                    'msg_type'        => 10,
                    'op_time'         => time(),
                    'msg_title'       => sprintf("加入了%s伙星", $tmpClubName),
                    'msg_content'     => sprintf("加入了%s伙星", $tmpClubName),
                    'close_nani_push' => 1,
                    'ext2'            => json_encode(array(
                        'club_id'    => $tmpClubId,
                        'apply_id'   => $intApplyId,
                        'member_uid' => $tmpUserId,
                    )),
                );
                $arrPushOut   = Tieba_Service::call('video', 'pushHudongMsg', $arrPushInput, null, null, 'post', 'php', 'utf-8');
                if (false == $arrPushOut || $arrPushOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('call video:pushHudongMsg false. input[' . serialize($arrPushInput) . '] output[' . serialize($arrPushOut) . ']');
                    continue;
                }
            }


        }


        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }
    /**
     * @brief 进入社团审核
     * @param array
     * @return array
     */
    public static function auditGroupMember($arrInput)
    {

        // dl update must check id
        if (!isset($arrInput['apply_id'])) {
            Bingo_Log::warning('auditGroupMember param error ' . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 更新申请状态
        $arrDbInput = array(
            'field' => array(
                'op_uid' => $arrInput['user_id'], // 审核人员
                'status' => $arrInput['status'],
                'refuse_content' => strval($arrInput['refuse_content']),
            ),
            'cond' => array(
                'id' => $arrInput['apply_id'],
            )
        );

        $arrDbOutput = Dl_Clubmember_MemberApply::update($arrDbInput);// 更新申请表的状态
        if (false === $arrDbOutput || Tieba_Errcode::ERR_SUCCESS != $arrDbOutput['errno']) {
            Bingo_Log::warning("call Dl_Club_ClubMember::update failed, input = " . serialize($arrDbInput) . ' output = ' . serialize($arrDbOutput));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }


        // 通过apply id 获得申请数据
        $arrMemberInput = array(
            'cond' => array(
                'id' => $arrInput['apply_id'],
            )
        );
        $arrApplyInfo = Dl_Clubmember_MemberApply::select($arrMemberInput);
        if (false === $arrApplyInfo || Tieba_Errcode::ERR_SUCCESS != $arrApplyInfo['errno']) {
            Bingo_Log::warning("call Dl_Clubmember_MemberApply::select failed, input = " . serialize($arrMemberInput) . ' output = ' . serialize($arrApplyInfo));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        //如果申请成功，将其拉入正式表
        if ($arrInput['status'] == self::AUDIT_STATUS_SUCCESS) { // 申请通过

            // 申请成功，拉入正式表
            $arrClubMember = array(
                'club_id' => $arrApplyInfo['data'][0]['club_id'],
                'user_id' => $arrApplyInfo['data'][0]['user_id'],
            );

            $arrClubOutput = Service_ClubMember_ClubMember::addGroupMember($arrClubMember);
            if (false === $arrClubOutput || Tieba_Errcode::ERR_SUCCESS != $arrClubOutput['errno']) {
                Bingo_Log::warning("call Dl_Clubmember_MemberApply::select failed, input = " . serialize($arrClubMember) . ' output = ' . serialize($arrClubOutput));
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }
        //如果拒绝，需更新redis
        if ($arrInput['status'] == self::AUDIT_STATUS_REJECT) {
            $arrClubMemberApply = array(
                'field' => array(
                    'count(1)'
                ),
                'cond' => array(
                    'club_id' => $arrApplyInfo['data'][0]['club_id'],
                    'user_id' => $arrApplyInfo['data'][0]['user_id'],
                    'create_time' => array(
                        'val' => time() - 7 * 86400, // 7天之内
                        'opt' => '>',
                    ),
                    'status' => self::AUDIT_STATUS_REJECT,
                ),
            );
            $arrDbOutput = Dl_Clubmember_MemberApply::select($arrClubMemberApply);
            if (false === $arrDbOutput || Tieba_Errcode::ERR_SUCCESS != $arrDbOutput['errno']) {
                Bingo_Log::warning("call Dl_Club_MemberApply::select failed, input = " . serialize($arrClubMemberApply) . ' output = ' . serialize($arrDbOutput));
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }

            // 7天之内，拒绝超过5次,redis 记录 30 天内不可加入
            if (intval($arrDbOutput['data'][0]['count(1)']) >= 5) {  // 时间可能不准，但是可以接受
                $intClubId = $arrApplyInfo['data'][0]['club_id'];
                $intUserId = $arrApplyInfo['data'][0]['user_id'];
                $arrRedisInput = array(
                    'key'    => "$intClubId:$intUserId",
                    'value'  => time(),
                    'expire' => 30 * 86400,   // 过期时间
                );
                $arrRedisOut = Util_Redis::setToRedis($arrRedisInput);
                if (false === $arrRedisOut) {
                    Bingo_Log::warning("Call Redis Failed, input = " . serialize($arrRedisInput) . "output = " . serialize($arrRedisOut));
                }

            }
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @brief 获取申请信息
     * @param array
     * @return array
     */
    public static function getApplyInfo($arrInput)
    {

        //  must check id
        if (!isset($arrInput['apply_id'])) {
            Bingo_Log::warning('auditGroupMember param error ' . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $strApplyId = $arrInput['apply_id'];
        if (is_array($strApplyId)) {
            $strApplyId = implode(',', $arrInput['apply_id']);
        }
        $arrApplyInfo = array(
            'cond' => array(
                'id' => array(
                    'opt' => 'in',
                    'val' => "($strApplyId)",
                    'quotes' => 0,
                )
            ),

        );

        $arrDbOutput = Dl_Clubmember_MemberApply::select($arrApplyInfo);

        if (false === $arrDbOutput || Tieba_Errcode::ERR_SUCCESS != $arrDbOutput['errno']) {
            Bingo_Log::warning("call Dl_Clubmember_MemberApply::select failed, input = " . serialize($arrApplyInfo) . ' output = ' . serialize($arrDbOutput));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $intCurrStamp = time();
        foreach ($arrDbOutput['data'] as &$item) {
            $intStamp = $intCurrStamp - $item['create_time'];
            if ($item['status'] == 0 && $intStamp > 3 * 86400) {
                $item['status'] = self::AUDIT_STATUS_AUTO_EXPIRE;     // 申请过期
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrDbOutput['data']);

    }

    /**
     * @param $arrInput
     * @return array
     */

    public static  function getApplyUserStatus($arrInput)
    {
        $intClubId = intval($arrInput['club_id']);
        $intUserId = intval($arrInput['user_id']);

        //  must check id
        if ($intClubId <= 0 || $intUserId <= 0) {
            Bingo_Log::warning('param error ' . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // get apply record from db
        $arrApplyInfo = array(
            'cond' => array(
                'club_id' => $intClubId,
                'user_id' => $intUserId,
            ),
            'append'  => "order by id desc limit 1"
        );
        $arrDbOutput = Dl_Clubmember_MemberApply::select($arrApplyInfo);
        if (false === $arrDbOutput || Tieba_Errcode::ERR_SUCCESS != $arrDbOutput['errno']) {
            Bingo_Log::warning("call Dl_Clubmember_MemberApply::select failed, input = " . serialize($arrApplyInfo) . ' output = ' . serialize($arrDbOutput));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        // -1 无记录 0 未审核 1 审核通过 2 审核拒绝 3 申请过期
        $intStatus = -1;
        if (!empty($arrDbOutput['data'])) {
            $intStatus     = intval($arrDbOutput['data'][0]['status']);
            $intCreateTime = intval($arrDbOutput['data'][0]['create_time']);
            $intCurrStamp  = time();
            $intStamp      = $intCurrStamp - $intCreateTime;
            if ($intStatus == 0 && $intStamp > 3 * 86400) {
                $intStatus = 3;     // 申请过期
            }
        }

        $arrOutput = array(
            'status' => $intStatus,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }
}