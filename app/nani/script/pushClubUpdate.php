<?php
/**
 * @brief 用户关注的伙星当天有内容更新的时候在每天的下午20：00提示用户查看更新。
 *
 */

require_once "./BaseScript.php";
require_once ROOT_PATH. "/app/video/lib/define/Nanipush.php";
define ( 'ROOT_PATH', dirname ( __FILE__ ) . '/../../../' );
define ( 'APP_NAME', 'nani' );
define ( 'SCRIPT_NAME', 'pushClubUpdate' );
define ( 'SCRIPT_LOG_PATH', ROOT_PATH . 'log/app/' . APP_NAME );

Bingo_Log::init(array(
    LOG_SCRIPT => array(
        'file'  => SCRIPT_LOG_PATH . '/' . SCRIPT_NAME . '/' . SCRIPT_NAME . '.log',
        'level' => 0x01 | 0x02 | 0x04 | 0x08,
    ),
), LOG_SCRIPT);

$objClubUpdatePush = new pushClubUpdate();
$objClubUpdatePush->execute();

class pushClubUpdate{
    static private $_redis = null;
    const REDIS_NAME = "ala";
    const CLUB_STATUS_PASS = 2;
    const CLUB_ID_KEY = 'nani_club_need_to_push';
    const CLUB_USER_ID_KEY = 'nani_club_member_need_to_push';
    private $intStartTime = 0;
    private $intEndTime = 0;
    private $objRedis = null;
    private $arrClubIds = array();

    /**
     * @brief 执行入口
     * @return bool
     */
    public function execute()
    {
        $intStart = 0;
        $intRn = 50;
        $this->intStartTime = strtotime(date('Y-m-d'), time());
        $this->intEndTime = $this->intStartTime + 86400;

        //初始化Redis
        $this->objRedis = self::getRedis();
        if (!$this->objRedis) {
            Bingo_Log::warning("init the redis failed!.");
            $this->objRedis = self::getRedis();
            if (!$this->objRedis) {
                Bingo_Log::warning("init the redis failed again!.");
                return false;
            }
        }

        //获取所有的社团ID
        $this->getAllClubId();

        while(true)
        {
            $arrParam = array(
                'key'   => self::CLUB_ID_KEY,
                'start' => $intStart,
                'stop'  => $intStart+$intRn,
            );
            $arrRet = $this->objRedis->LRANGE($arrParam);
            if (0 != $arrRet['err_no'] || false == $arrRet) {
                Bingo_Log::warning("LRANGE from redis failed:" . serialize($arrRet));
                continue;
            }

            if (empty($arrRet['ret'][self::CLUB_ID_KEY])) {
                Bingo_Log::notice("get the last club id!");
                break;
            }
            $arrClubIdList = $arrRet['ret'][self::CLUB_ID_KEY];
            $arrParam = array(
                'club_ids' => $arrClubIdList,
            );
            $this->arrClubIds = array();

            //判断伙星是否有新的视频
            $boolRet = $this->getClubVideoUpdateNum($arrParam);
            if(false == $boolRet){
                Bingo_Log::warning(sprintf("get club update video num failed retry.[input=%s],[output=%s].",serialize($arrClubIdList),serialize($arrRet)));
                continue;
            }
            if(!empty($this->arrClubIds))
            {
                foreach($this->arrClubIds as $intClubId)
                {
                    $boolRet = $this->getAllUserId($intClubId);
                    if(false == $boolRet){
                        Bingo_Log::warning(sprintf("get club member num failed retry.[input=%s],[output=%s].",serialize($intClubId),serialize($boolRet)));
                        continue;
                    }
                }
            }
            $intStart = $intStart+$intRn+1;
        }
        //给所有的成员推送
        $this->pushNotice();
        //设置过期时间
        $arrParam = array(
            'key' => self::CLUB_ID_KEY,
            'second' => 86400,
        );
        $this->objRedis->EXPIRE($arrParam);
        $arrParam = array(
            'key' => self::CLUB_USER_ID_KEY,
            'second' => 86400,
        );
        $this->objRedis->EXPIRE($arrParam);
        return true;
    }

    /**
     * @param $arrParam
     * @return bool
     */
    private function getClubVideoUpdateNum($arrParam)
    {
        $arrClubIds = $arrParam['club_ids'];
        //check param
        if (empty($arrClubIds) || !is_array($arrClubIds)) {
            Bingo_Log::warning(sprintf("param error. [input = %s]", serialize($arrClubIds)));
            return false;
        }
        foreach ($arrClubIds as $intClubId) {
            if ((int)$intClubId <= 0) {
                Bingo_Log::warning(sprintf("param error, club id contains none integer [input = %s]", serialize($arrClubIds)));
                return false;
            }
        }

        //获取社团的视频更新
        $arrParam = array(
            'club_ids' => $arrClubIds,
            'start_time' => $this->intStartTime,
            'end_time' => $this->intEndTime,
        );
        $arrRet = Tieba_Service::call('nani', 'mgetClubVideoNum', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] || false == $arrRet) {
            Bingo_Log::warning(sprintf("call nani::mgetClubVideoNum service failed! [input=%s],[output = %s]", serialize($arrParam), serialize($arrRet)));
            return false;
        }
        $arrClubVideoNum = $arrRet['data'];
        if(!empty($arrClubVideoNum)){
            foreach ($arrClubVideoNum as $key => $item) {
                if (intval($item) > 0) {
                    $this->arrClubIds[] = intval($key);
                }
            }
        }
        return true;
    }

    /**
     * @param $intClubId
     * @return bool
     */
    private function getAllUserId($intClubId)
    {
        if ((int)$intClubId <= 0) {
            Bingo_Log::warning(sprintf("param error, club id contains none integer [input = %s]", serialize($intClubId)));
            return false;
        }
        $arrParam = array(
            'club_id' => $intClubId,
            'pn' => 1,
            'rn' => 100,
        );
        $strKey = self::CLUB_USER_ID_KEY;
        while (true) {
            $arrRet = Tieba_Service::call('nani', 'getClubMemberList', $arrParam, null, null, 'post', 'php', 'utf-8');
            if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] || false == $arrRet) {
                Bingo_Log::warning(sprintf("call nani::getClubMemberList service failed![input=%s] [output = %s]", serialize($arrParam), serialize($arrRet)));
                usleep(5000);
                continue;
            }
            if (empty($arrRet['data']['list'])) {
                Bingo_Log::notice("get all member!");
                break;
            }

            foreach ($arrRet['data']['list'] as $item) {
                $intUserId = intval($item['user_id']);
                $arrInput = array(
                    'key' => $strKey,
                    'member' => $intUserId,
                    'score' => $intUserId,
                );
                $arrRet = $this->objRedis->ZADD($arrInput);
                if (false == $arrRet || $arrRet['err_no'] !== 0) {
                    Bingo_Log::warning("call redis error.[" . serialize($arrRet) . "]");
                    continue;
                }
            }
            $arrParam['pn'] = $arrParam['pn'] + 1;
        }
        return true;
    }

    /**
     * @brief 给每个需要推送的用户推送
     * @return bool
     */
    private function pushNotice() {
        $strKey = self::CLUB_USER_ID_KEY;
        $intStart = 0;
        $intRn = 50;
        while(true)
        {
            $arrInput  = array(
                'key'   => $strKey,
                'start' => $intStart,
                'stop'  => $intStart+$intRn,
            );
            $arrRet = $this->objRedis->ZREVRANGEWITHSCORES($arrInput);
            if($arrRet == false || $arrRet['err_no'] !== 0)
            {
                Bingo_Log::warning("call redis error.[".serialize($arrRet)."]");
                continue;
            }

            if (empty($arrRet['ret'][$strKey])) {
                Bingo_Log::notice("get the last user id!");
                break;
            }

            if (!empty($arrRet['ret'][$strKey]))
            {
                $arrUserList = $arrRet['ret'][$strKey];
                foreach($arrUserList as $item) {
                    $intUserId = intval($item['member']);
                    $boolRet = $this->pushMsg($intUserId);
                    if(false == $boolRet)
                    {
                        Bingo_Log::warning("call redis error.[user id is ".serialize($intUserId)."]");
                        continue;
                    }
                }
            }
            $intStart = $intStart+$intRn+1;
        }
        return true;
    }

    /**
     * @brief 获取所有的CLUB ID
     * @param null
     * @return bool
     */
    private  function getAllClubId(){
        $arrInput = array(
            'status'     => self::CLUB_STATUS_PASS,
            'rn'         => 100,
            'pn'         => 0,
        );

        while(true){
            $arrRet = Tieba_Service::call('nani', 'getNeedPushClubData', $arrInput, null, null, 'post', 'php', 'utf-8');
            if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] || false == $arrRet) {
                Bingo_Log::warning(sprintf("call nani::getNeedPushClubData service failed![input = %s] [output = %s]",serialize($arrInput), serialize($arrRet)));
                usleep(5000);
                continue;
            }

            if (empty($arrRet['data']) && Tieba_Errcode::ERR_SUCCESS == $arrRet['errno']) {
                Bingo_Log::notice("get all club id!");
                break;
            }

            //store club id to redis
            $arrClubDataList = $arrRet['data'];
            foreach ($arrClubDataList as $key => $val) {
                $arrSetParam = array(
                    'key'    => self::CLUB_ID_KEY,
                    'value'  => intval($val['club_id']),
                );
                $arrRedisRet = $this->objRedis->RPUSH($arrSetParam);
                if (0 != $arrRedisRet['err_no']) {
                    Bingo_Log::warning(sprintf("rpush to redis failed: [input=%s] [output=%s]",serialize($arrSetParam),serialize($arrRedisRet)));
                    continue;
                }
            }
            $arrInput['pn'] = intval($arrInput['pn'])+100;
        }
        return true;
    }

    /**
     * @brief 推送消息
     * @param $intUserId
     * @return bool
     */
    private function pushMsg($intUserId){
        $arrPushInput = array(
            'user_id'         => $intUserId, //test
            'op_uid'          => 3559714525, // 官方uid
            'msg_type'        => 11,
            'msg_title'       => '伙星向您发来邀请',
            'op_time'         => time(),
            'msg_content'     => '您关注的伙星更新了内容，快来查看吧~',
            'close_nani_push' => 0,
            'ext2' => serialize(array(
                'button_type' => 3,
                'button_data' => array(
                    'rank_url'=>'com.baidu.nani://index?tab=1&cate_id=1001',
                ),
            )),
            'need_insert'     => false,
        );

        $arrPushOut = Tieba_Service::call('video', 'pushHudongMsg', $arrPushInput, null, null, 'post', 'php', 'utf-8');
        if (false == $arrPushOut || $arrPushOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call video:pushHudongMsg false. input[' . serialize($arrPushInput) . '] output[' . serialize($arrPushOut) . ']'
            );
            return false;
        }
        return true;
    }

    /**
     * @brief
     * @param null
     * @return null
     */
    private static function getRedis(){
        if (self::$_redis) {
            return self::$_redis;
        }
        Bingo_Timer::start('redis_init');
        self::$_redis = new Bingo_Cache_Redis(self::REDIS_NAME);
        Bingo_Timer::end('redis_init');
        if(!self::$_redis || !self::$_redis->isEnable()){
            Bingo_Log::warning("init redis fail.");
            self::$_redis = null;
            return null;
        }
        return self::$_redis;
    }
}