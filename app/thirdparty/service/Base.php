<?php
/**
 * created by xiaof<PERSON>.
 * email: <EMAIL>
 * file name: Base.php
 * create time: 2019-09-06 19:04:01
 * describe: 基础类
 */

class Service_Base {

    /**
     * 生成返回内容
     * @param $errno
     * @param null $data
     * @return array
     */
    protected static function makeReturn($errno, $data=null) {
        $ret = array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
        if($data !== null) {
            $ret['output'] = $data;
        }
        return $ret;
    }
}