<?php
class Service_Baijiahao_Bjhmsg {

    // 类型定义
    const OP_TYPE_ADD           = 1;
    const OP_TYPE_RECOVER       = 2;
    const OP_TYPE_DELETE        = 3;
    const DATA_TYPE_POST        = 1;
    const DATA_TYPE_AT          = 2;

    // 申请 post_id
    const IDALLOC_POST_ID       = 'gpost_id';
 
    //********************** db 相关操作 start **********************
    /**
     * 查询操作
     * @param
     * @param
     */
    private static function getDataByPostid($arrInput) {
        if (empty($arrInput['post_ids'])) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $getMsgInput = array(
            'function'  => 'getBjhMsgDataByPostid',
            'post_ids'  => strval($arrInput['post_ids']),
        );
        $getMsgOutput = Dl_Db_Bjhmsg::execSql($getMsgInput);
        if (false === $getMsgOutput || Tieba_Errcode::ERR_SUCCESS != $getMsgOutput['errno']) {
            Bingo_Log::fatal('call db getBjhMsgDataByPostid failed. input[' . serialize($getMsgInput) . '], output[' . serialize($getMsgOutput) . ']');
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $getMsgOutput['results'][0]);
    }

    /**
     * 通过百家号信息获取数据
     * @param
     * @param
     */
    private static function getDataByBjhInfo($arrInput) {
        if (empty($arrInput['msg_type']) || empty($arrInput['tid']) || empty($arrInput['bjh_type'])) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $getMsgInput = array(
            'function'      => 'getBjhMsgDataByBjhInfo',
            'msg_type'      => intval($arrInput['msg_type']),
            'bjh_tid'       => intval($arrInput['tid']),
            'bjh_pid'       => intval($arrInput['pid']),
            'bjh_type'      => intval($arrInput['bjh_type']),
        );
        $getMsgOutput = Dl_Db_Bjhmsg::execSql($getMsgInput);
        if (false === $getMsgOutput || Tieba_Errcode::ERR_SUCCESS != $getMsgOutput['errno']) {
            Bingo_Log::fatal('call db getBjhMsgDataByBjhInfo failed. input[' . serialize($getMsgInput) . '], output[' . serialize($getMsgOutput) . ']');
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $getMsgOutput['results'][0]);
    }

    /**
     * 添加操作
     * @param
     * @param
     */
    private static function addData($arrInput) {
        if (empty($arrInput['thread_id']) || empty($arrInput['post_id'])) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $addMsgInput = array(
            'function'      => 'addBjhMsgData',
            'nid'           => strval($arrInput['nid']),
            'bjh_tid'       => intval($arrInput['tid']),
            'bjh_vid'       => intval($arrInput['vid']),
            'bjh_pid'       => intval($arrInput['pid']),
            'bjh_uid'       => intval($arrInput['uid']),
            'bjh_type'      => intval($arrInput['bjh_type']),
            'thread_id'     => intval($arrInput['thread_id']),
            'post_id'       => intval($arrInput['post_id']),
            'is_lzl'        => intval($arrInput['is_lzl']),
            'msg_time'      => intval($arrInput['msg_time']),
            'msg_type'      => intval($arrInput['msg_type']),
            'create_time'   => time(),
        );
        $addMsgOutput = Dl_Db_Bjhmsg::execSql($addMsgInput);
        if (false === $addMsgOutput || Tieba_Errcode::ERR_SUCCESS != $addMsgOutput['errno']) {
            Bingo_Log::fatal('call db addData failed. input[' . serialize($addMsgInput) . '], output[' . serialize($addMsgOutput) . ']');
            return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 更新操作
     * @param
     * @param
     */
    private static function setData($arrInput) {
        if (empty($arrInput['tid'])) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (intval($arrInput['msg_type']) == self::DATA_TYPE_POST && empty($arrInput['pid'])) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $setMsgInput = array(
            'function'      => 'setBjhAtData',
            'is_delete'     => intval($arrInput['is_delete']),
            'bjh_tid'       => intval($arrInput['tid']),
            'bjh_type'      => intval($arrInput['bjh_type']),
            'msg_type'      => intval($arrInput['msg_type']),
            'update_time'   => time(),
        );

        switch (intval($arrInput['msg_type'])) {
            case self::DATA_TYPE_POST:
                $setMsgInput['function'] = 'setBjhPostData';
                $setMsgInput['bjh_pid']  = intval($arrInput['pid']);
                $setMsgOutput = Dl_Db_Bjhmsg::execSql($setMsgInput);
                if (false === $setMsgOutput || Tieba_Errcode::ERR_SUCCESS != $setMsgOutput['errno']) {
                    Bingo_Log::fatal('call db setData post failed. input[' . serialize($setMsgInput) . '], output[' . serialize($setMsgOutput) . ']');
                    return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                }
                break;
            case self::DATA_TYPE_AT:
                $setMsgInput['function'] = 'setBjhAtData';
                $setMsgOutput = Dl_Db_Bjhmsg::execSql($setMsgInput);
                if (false === $setMsgOutput || Tieba_Errcode::ERR_SUCCESS != $setMsgOutput['errno']) {
                    Bingo_Log::fatal('call db setData at failed. input[' . serialize($setMsgInput) . '], output[' . serialize($setMsgOutput) . ']');
                    return self::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                }
                break;
            default:
                break;
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    //********************** db 相关操作 end **********************

    /**
     * 返回整理
     * @param
     * @param
     */
    private static function errRet($errno, $data = null) {
        $arrRet = array(
            'errno'     => $errno,
            'errmsg'    => Tieba_Error::getErrmsg($errno),
        );
        if($data !== null) {
            $arrRet['data'] = $data;
        }
        return $arrRet;
    }

    /**
     * 生成贴吧post_id
     * input:null
     * @return int
     */
    private static function getPostId(){

        $ids = Tieba_Idalloc::alloc(self::IDALLOC_POST_ID);

        Bingo_Log::notice("[third-party] gpost_id :" .json_encode($ids));

        if (empty($ids[0])) {
            Bingo_Log::warning(sprintf('failed to get gpost_id .'));
            return 0;
        }else{
            return intval($ids[0]);
        }
    }

    /**
     * 获取thread_id
     * input:null
     * @return int
     */
    private static function getThreadId($input){
        // 逻辑上看这个时候是有thread_id，若查不到则说明有问题
        $arrNids = array(
            'nids' => array($input['nid']),
        );
        $arrTidInfo = Service_Odyssey_Odyssey::mgetThreadIdByNid($arrNids);
        if (false === $arrTidInfo || Tieba_Errcode::ERR_SUCCESS != $arrTidInfo['errno']) {
            Bingo_Log::warning('call Service_Odyssey_Odyssey::mgetThreadIdByNid fail, input:[' . serialize($arrNids) . '],output:[' . serialize($arrTidInfo) . ']');
            return 0;
        }
        if (!empty($arrTidInfo['data'][$input['nid']])) {
            return intval($arrTidInfo['data'][$input['nid']]);
        } else {
            return 0;
        }
    }

    /**
     * 同步回复提醒信息
     * input:null
     * @return int
     */
    private static function syncReplyRemind($arrInput, $arrUserList){
        if (empty($arrInput['post_id']) || empty($arrInput['thread_id']) || empty($arrInput['op_type'])) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (empty($arrUserList)) {
            Bingo_Log::warning('user_list is nothing.');
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        $input = array(
            'user_ids'  => $arrUserList,
            'post_id'   => $arrInput['post_id'],
            'thread_id' => $arrInput['thread_id'],
        );
        if (!empty($arrInput['op_time'])) {
            $input['op_time'] = $arrInput['op_time'];
        }
        if ($arrInput['op_type'] == self::OP_TYPE_ADD || $arrInput['op_type'] == self::OP_TYPE_RECOVER) {
            $input['op_type']   = 'add';
        }
        if ($arrInput['op_type'] == self::OP_TYPE_DELETE) {
            $input['op_type']   = 'delete';
        }
        Bingo_Timer::start("bjhReplyRemind");
        $output = Tieba_Commit::commit('shoubai', 'bjhReplyRemind', $input);
        Bingo_Timer::end("bjhReplyRemind");
        if($output === false || $output['err_no'] !== Tieba_Errcode::ERR_SUCCESS ) {
            Bingo_Log::warning('call commit::bjhReplyRemind fail, input:[' . serialize($input) . '],output:[' . serialize($output) . ']');
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 同步艾特提交信息
     * input:null
     * @return int
     */
    private static function syncAtCommit($arrInput, $arrUserList){
        if (empty($arrInput['uid']) || empty($arrInput['post_id']) || empty($arrInput['thread_id'])) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (empty($arrUserList)) {
            Bingo_Log::warning('user_list is nothing.');
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        // 获取uname
        $userInput = array(
            'user_id' => array($arrInput['uid'],),
        );
        $userOutput = Tieba_Service::call('user', 'getUnameByUids', $userInput, null, null, 'post', null, 'utf-8');
        if (false === $userOutput || Tieba_Errcode::ERR_SUCCESS != $userOutput['errno']) {
            Bingo_Log::warning('call user::getUnameByUids fail, input:[' . serialize($userInput) . '],output:[' . serialize($userOutput) . ']');
        }
        $strUname = !empty($userOutput['output']['unames'][0]['user_name']) ? strval($userOutput['output']['unames'][0]['user_name']) : $arrInput['uid'];

        $input = array(
            'at_uids'   => $arrUserList,
            'now_time'  => empty($arrInput['op_time']) ? $arrInput['op_time'] : time(),
            'user_id'   => $arrInput['user_id'],
            'user_name' => $strUname,
            'thread_id' => $arrInput['thread_id'],
            'post_id'   => $arrInput['post_id'],
        );
        Bingo_Timer::start("bjhAtCommit");
        $output = Tieba_Commit::commit('shoubai', 'bjhAtCommit', $input);
        Bingo_Timer::end("bjhAtCommit");
        if($output === false || $output['err_no'] !== Tieba_Errcode::ERR_SUCCESS ) {
            Bingo_Log::warning('call commit::bjhAtCommit fail, input:[' . serialize($input) . '],output:[' . serialize($output) . ']');
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 同步艾特删除信息
     * input
     * @return
     */
    private static function syncAtDelete($arrInput, $arrUserList){
        if (empty($arrInput['post_id']) || empty($arrInput['thread_id'])) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (empty($arrUserList)) {
            Bingo_Log::warning('user_list is nothing.');
            return self::errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $input = array(
            'user_ids'  => $arrUserList,
            'post_id'   => $arrInput['post_id'],
            'thread_id' => $arrInput['thread_id'],
        );
        Bingo_Timer::start("bjhAtDelete");
        $output = Tieba_Commit::commit('shoubai', 'bjhAtDelete', $input);
        Bingo_Timer::end("bjhAtDelete");
        if($output === false || $output['err_no'] !== Tieba_Errcode::ERR_SUCCESS ) {
            Bingo_Log::warning('call commit::bjhAtDelete fail, input:[' . serialize($input) . '],output:[' . serialize($output) . ']');
            return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /*
     * @description 添加消息记录
     * @param op_uid 操作人uid
     * @param op_time 操作时间
     * @param op_type 操作类型（1:新增，2:恢复）
     * @param message_type 消息类型（1:回复，2:艾特）
     * @param notify_ulist 通知用户列表
     * @param source_from 来源信息
     * @param data_info 详细信息
     *      tid  百家号内容首楼tid
     *      vid  百家号内容首楼vid
     *      nid  百家号内容首楼nid
     *      pid  百家号内容首楼pid
     *      uid  百家号内容uid（内容作者uid）
     *      msg_time  百家号时间（艾特时是发帖时间、回复和楼中楼时是回复时间）
     *      bjh_type  百家号内容首楼tid对应的type
     *      is_lzl  楼中楼标记
     * @return
     */
    public static function addBjhMessage($arrInput) {
        if (empty($arrInput['op_uid']) || empty($arrInput['op_type']) || empty($arrInput['op_time']) || empty($arrInput['message_type']) || empty($arrInput['source_from']) || empty($arrInput['data_info'])) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrDataInfo = $arrInput['data_info'];
        if (empty($arrDataInfo['tid']) || empty($arrDataInfo['nid']) || empty($arrDataInfo['uid']) || empty($arrDataInfo['msg_time']) || !isset($arrDataInfo['bjh_type']) || !isset($arrDataInfo['is_lzl'])) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (empty($arrDataInfo['pid']) && $arrDataInfo['message_type'] == self::DATA_TYPE_POST) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrDataInfo['msg_type'] = intval($arrInput['message_type']);
        $arrDataInfo['op_type']  = intval($arrInput['op_type']);
        $arrDataInfo['op_time']  = intval($arrInput['op_time']);

        Bingo_Log::notice('addBjhMessage input params[' . serialize($arrInput) . ']');
        switch (intval($arrInput['op_type'])) {
            case self::OP_TYPE_ADD:
                // 获取对应的贴吧thread_id、post_id
                $arrDataInfo['thread_id']   = self::getThreadId($arrDataInfo);
                if (empty($arrDataInfo['thread_id'])) {
                    Bingo_Log::warning('no thread_id params invalid. [' . serialize($arrDataInfo) . ']');
                    return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
                $arrDataInfo['post_id']     = self::getPostId();

                // 添加操作
                $arrRet = self::addData($arrDataInfo);
                if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
                    Bingo_Log::warning('call addData fail, input:[' . serialize($arrDataInfo) . '],output:[' . serialize($arrRet) . ']');
                    return $arrRet;
                }
                break;
            case self::OP_TYPE_RECOVER:
                // 恢复操作
                $arrDataInfo['is_delete'] = 0;
                $arrRet = self::setData($arrDataInfo);
                if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
                    Bingo_Log::warning('call setData fail, input:[' . serialize($arrDataInfo) . '],output:[' . serialize($arrRet) . ']');
                    return $arrRet;
                }
                break;
            default:
                break;
        }

        // 同步数据到底层
        if (empty($arrDataInfo['thread_id']) || empty($arrDataInfo['post_id'])) {
            $output = self::getDataByBjhInfo($arrDataInfo);
            if (false === $output || Tieba_Errcode::ERR_SUCCESS != $output['errno']) {
                Bingo_Log::warning('call getDataByBjhInfo fail, input:[' . serialize($arrDataInfo) . '],output:[' . serialize($output) . ']');
                return $output;
            }
            if (empty($output['data'][0]['thread_id']) || empty($output['data'][0]['post_id'])) {
                Bingo_Log::warning('call getDataByBjhInfo fail, input:[' . serialize($arrDataInfo) . '],output:[' . serialize($output) . ']');
                return self::errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL); 
            }
            $arrDataInfo['thread_id']   = intval($output['data'][0]['thread_id']);
            $arrDataInfo['post_id']     = intval($output['data'][0]['post_id']);
        }
        if (!empty($arrInput['notify_ulist'])) {
            // // 添加白名单，白名单内用户下发通知（线上回归需要）--start
            // $arrWhiteList = array(
            //     731683028,
            //     733651768,
            //     593018045,
            //     837222183,
            //     2796203545,
            //     3026626940,
            //     664115122,
            // );
            // foreach ($arrInput['notify_ulist'] as $key => $value) {
            //     if (!in_array(intval($value), $arrWhiteList)) {
            //         unset($arrInput['notify_ulist'][$key]);
            //     }
            // }
            // Bingo_Log::notice('addBjhMessage sync params[' . serialize($arrDataInfo) . '], user_list[' . serialize($arrInput['notify_ulist']) . ']');
            // // 添加白名单，白名单内用户下发通知（线上回归需要）--end
            if ($arrDataInfo['msg_type'] == self::DATA_TYPE_POST) {
                $output = self::syncReplyRemind($arrDataInfo, $arrInput['notify_ulist']);
            }
            if ($arrDataInfo['msg_type'] == self::DATA_TYPE_AT) {
                $output = self::syncAtCommit($arrDataInfo, $arrInput['notify_ulist']);
            }
        }

        if (isset($output) && Tieba_Errcode::ERR_SUCCESS != $output['errno']) {
            Bingo_Log::warning('call commit fail, input1:[' . serialize($arrDataInfo) . '], input2:[' . serialize($arrInput) . '],output:[' . serialize($output) . ']');
            return $output;
        }
        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /*
     * @description 删除消息记录（标记删除）
     * @param op_uid 操作人uid
     * @param op_time 操作时间
     * @param message_type 消息类型（1:回复，2:艾特）
     * @param source_from 来源信息
     * @param data_info 详细信息
     *      tid  百家号内容首楼tid
     *      vid  百家号内容首楼vid
     *      nid  百家号内容首楼nid
     *      pid  百家号内容首楼pid
     *      bjh_type  百家号内容首楼tid对应的type
     * @return
     */
    public static function delBjhMessage($arrInput) {
        if (empty($arrInput['op_time']) || empty($arrInput['message_type']) || empty($arrInput['source_from']) || empty($arrInput['data_info'])) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrDataInfo = $arrInput['data_info'];
        if (empty($arrDataInfo['tid']) || empty($arrDataInfo['nid']) || empty($arrDataInfo['bjh_type'])) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrDataInfo['msg_type']    = intval($arrInput['message_type']);
        $arrDataInfo['op_time']     = intval($arrInput['op_time']);
        $arrDataInfo['is_delete']   = 1;
        $arrRet = self::setData($arrDataInfo);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call delBjhMessage::setData failed. input[' . serialize($arrDataInfo) . '], output[' . serialize($arrRet) . ']');
            return $arrRet;
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /*
     * @description 查询消息记录
     * @param post_id 帖子post_id
     * @return data
     *      tid  百家号内容首楼tid
     *      vid  百家号内容首楼vid
     *      nid  百家号内容首楼nid
     *      pid  百家号内容首楼pid
     *      bjh_type  百家号内容首楼tid对应的type
     */
    public static function getBjhMessage($arrInput) {
        if (empty($arrInput['post_ids']) || count($arrInput['post_ids']) > 200) {
            Bingo_Log::warning('input params invalid. [' . serialize($arrInput) . ']');
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $strPostIds = '';
        foreach ($arrInput['post_ids'] as $post_id) {
            if (!empty($strPostIds)) {
                $strPostIds .= ',';
            }
            $strPostIds .= $post_id;
        }
        $input = array(
            'post_ids' => $strPostIds,
        );
        $output = self::getDataByPostid($input);
        if (false === $output || Tieba_Errcode::ERR_SUCCESS != $output['errno']) {
            Bingo_Log::warning('call getDataByPostid failed. input[' . serialize($input) . '], output[' . serialize($output) . ']');
            return $output;
        }

        $arrOutput = array();
        if (!empty($output['data'])) {
            foreach ($output['data'] as $key => $value) {
                if (intval($value['is_delete']) == 1) {
                    continue;
                }
                $arrOutput[intval($value['post_id'])] = array(
                    'nid'       => $value['nid'],
                    'tid'       => $value['bjh_tid'],
                    'vid'       => $value['bjh_vid'],
                    'pid'       => $value['bjh_pid'],
                    'uid'       => $value['bjh_uid'],
                    'bjh_type'  => $value['bjh_type'],
                    'is_lzl'    => $value['is_lzl'],
                    'is_delete' => $value['is_delete'],
                );
            }
        }

        return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }
}
