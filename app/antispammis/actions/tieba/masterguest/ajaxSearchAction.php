<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [ajaxSearchAction.php 主客态mpmis]
 * <AUTHOR>
 * @DateTime 17/3/16 17:31
 * @brief
 */

class ajaxSearchAction extends Lib_Mis_BaseAction{

    /**
     * [run description]
     * <AUTHOR>
     * @param
     * @return
     */
    protected function run()
    {
        $strStatus      = Bingo_Http_Request::getGetNoXssSafe("status", "pending");
        $strDealer      = Bingo_Http_Request::getGetNoXssSafe("dealername", "");
        $strDimension   = Bingo_Http_Request::getGetNoXssSafe("search_dimension", "");
        $strData        = Bingo_Http_Request::getGetNoXssSafe("search_data", "");
        $strType        = Bingo_Http_Request::getGetNoXssSafe("search_type", "");
        $intNoDealername      = Bingo_Http_Request::getGetNoXssSafe("no_dealername", 0);
        $intPage        = Bingo_Http_Request::getGetNoXssSafe("page", 1);
        $intSize        = 10;
        $intFrom        = ($intPage-1) * $intSize;

        $arrConditions = array();

        if(!empty($strDimension) && !empty($strData)){
            switch ($strDimension){
                case "keyword":
                    $arrConditions['content ='] = $strData;
                    break;
                case "forum_name":
                    $arrConditions['forum_name ='] = $strData;
                    break;
                case "forum_id":
                    $arrConditions['forum_id ='] = $strData;
                    break;
                case "user_name":
                    $arrConditions['user_name ='] = $strData;
                    break;
                case "user_id":
                    $arrConditions['user_id ='] = $strData;
                    break;
                case "ip":
                    $arrConditions['ip ='] = ip2long($strData);
                    break;
            }
        }

        if(!empty($strType) && in_array($strType, array('thread', 'post'))){
            switch ($strType){
                case 'thread':
                    $arrConditions['topic ='] = 1;
                    break;
                case 'post' :
                    $arrConditions['topic = '] = 0;
                    break;
            }
        }

        if ($intNoDealername == 1){
            $arrConditions['dealername = '] = '';
        } else if(!empty($strDealer)){
            $arrConditions[] = array("dealername", "in", explode(",", $strDealer));
        }

        switch (strtolower($strStatus)){
            case Data_tieba_Masterguest_Util::OP_STATUS_PENDING:
                $arrResult = Data_tieba_Masterguest_Util::listPending($arrConditions, $intPage, $intSize);
                break;
            case Data_tieba_Masterguest_Util::OP_STATUS_DONE:
                $arrResult = Data_tieba_Masterguest_Util::listDone($arrConditions, $intPage, $intSize);
                break;
            default:
                $arrResult = Data_tieba_Masterguest_Util::listPending($arrConditions, $intPage, $intSize);
        }

        $this->_setXssSafe(false);
        $this->_retjson ( Tieba_Errcode::ERR_SUCCESS, $arrResult );

    }
}