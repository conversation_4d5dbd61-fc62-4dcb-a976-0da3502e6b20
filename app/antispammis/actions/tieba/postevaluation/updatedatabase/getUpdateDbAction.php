<?php
/**
 * @Class getPostListAction
 * @Create By <PERSON><PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Time 2021/5/7 2:11 下午
 */

class getUpdateDbAction extends Lib_Mis_BaseAction
{
    protected function run()
    {
        /**
         * 1:清理info_id库
         * 2:插入info_id数据
         *
         */

        $type = Bingo_Http_Request::getGetNoXssSafe("type", "");
        // 判断项目是否存在
        if ($type == "insert") {
            $arr = array(
                array('id' => '1', 'info' => "监管指令", 'busbute' => 1, 'priority' => 1),
                array('id' => '2', 'info' => "公安大文档", 'busbute' => 1, 'priority' => 1),
                array('id' => '3', 'info' => "网信办通知", 'busbute' => 1, 'priority' => 1),
                array('id' => '4', 'info' => "外部舆情", 'busbute' => 1, 'priority' => 1),
                array('id' => '5', 'info' => "捉虫项目", 'busbute' => 2, 'priority' => 2),
                array('id' => '6', 'info' => "二道防线", 'busbute' => 2, 'priority' => 2),
                array('id' => '7', 'info' => "收作弊case群", 'busbute' => 2, 'priority' => 2),
                array('id' => '8', 'info' => "审核业务-涉政", 'busbute' => 2, 'priority' => 2),
                array('id' => '9', 'info' => "小程序", 'busbute' => 2, 'priority' => 2),
                array('id' => '10', 'info' => "审核业务", 'busbute' => 3, 'priority' => 3),
                array('id' => '11', 'info' => "帖子举报", 'busbute' => 3, 'priority' => 3),
                array('id' => '12', 'info' => "关键词回扫", 'busbute' => 3, 'priority' => 3),
                array('id' => '13', 'info' => "网警群", 'busbute' => 3, 'priority' => 3),
                array('id' => '14', 'info' => "贴吧志愿者", 'busbute' => 3, 'priority' => 3),
                array('id' => '15', 'info' => "氛围评估", 'busbute' => 3, 'priority' => 3),
                array('id' => '16', 'info' => "吧主团队", 'busbute' => 3, 'priority' => 3),
                array('id' => '17', 'info' => "评估业务", 'busbute' => 3, 'priority' => 3),
                array('id' => '18', 'info' => "评论违规", 'busbute' => 3, 'priority' => 3),
                array('id' => '19', 'info' => "用户举报", 'busbute' => 3, 'priority' => 3),
                array('id' => '20', 'info' => "内部需求", 'busbute' => 3, 'priority' => 2),
                array('id' => '21', 'info' => "海南团队", 'busbute' => 3, 'priority' => 1)
            );

            $db = new DL_TieBa_PostEvaluation_UpdateDatabase_updateDb();
            foreach ($arr as $line) {
                $res = $db->insertInfo($line);
            }
            echo json_encode(array('status' => 0, 'msg' => $res));
        } elseif ($type == "clear") {
            $db = new DL_TieBa_PostEvaluation_UpdateDatabase_updateDb();
            $res = $db->clearInfo();
            echo json_encode(array('status' => 0, 'msg' => $res));
        } elseif ($type == "delete") {
            $db = new DL_TieBa_PostEvaluation_UpdateDatabase_updateDb();
            $res = $db->deleteInfo();
            echo json_encode(array('status' => 0, 'msg' => $res));
        } elseif ($type == "update") {
            echo json_encode(array('status' => 0, 'msg' => "提交成功"));
            fastcgi_finish_request();
            // Pid
            $pid = new DL_Tieba_Efficiency_CollectPid();
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='1', busbute='1' WHERE (info = '1')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='1', busbute='1' WHERE (info = '2')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='1', busbute='1' WHERE (info = '3')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='1', busbute='1' WHERE (info = '4')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='2', busbute='2' WHERE (info = '5')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='2', busbute='2' WHERE (info = '6')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='2', busbute='2' WHERE (info = '7')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='2', busbute='2' WHERE (info = '8')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='2', busbute='2' WHERE (info = '9')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='3', busbute='3' WHERE (info = '10')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='3', busbute='3' WHERE (info = '11')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='3', busbute='3' WHERE (info = '12')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='3', busbute='3' WHERE (info = '13')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='3', busbute='3' WHERE (info = '14')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='3', busbute='3' WHERE (info = '15')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='3', busbute='3' WHERE (info = '16')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='3', busbute='3' WHERE (info = '17')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='3', busbute='3' WHERE (info = '18')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='3', busbute='3' WHERE (info = '19')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='3', busbute='2' WHERE (info = '20')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_pid SET priority='3', busbute='1' WHERE (info = '21')";
            $pid->query($sql);
            // Content
            $pid = new DL_Tieba_Efficiency_CollectContent();
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='1', busbute='1' WHERE (info = '1')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='1', busbute='1' WHERE (info = '2')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='1', busbute='1' WHERE (info = '3')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='1', busbute='1' WHERE (info = '4')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='2', busbute='2' WHERE (info = '5')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='2', busbute='2' WHERE (info = '6')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='2', busbute='2' WHERE (info = '7')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='2', busbute='2' WHERE (info = '8')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='2', busbute='2' WHERE (info = '9')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='3', busbute='3' WHERE (info = '10')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='3', busbute='3' WHERE (info = '11')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='3', busbute='3' WHERE (info = '12')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='3', busbute='3' WHERE (info = '13')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='3', busbute='3' WHERE (info = '14')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='3', busbute='3' WHERE (info = '15')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='3', busbute='3' WHERE (info = '16')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='3', busbute='3' WHERE (info = '17')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='3', busbute='3' WHERE (info = '18')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='3', busbute='3' WHERE (info = '19')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='3', busbute='2' WHERE (info = '20')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_collect_content SET priority='3', busbute='1' WHERE (info = '21')";
            $pid->query($sql);
            // Expand
            $pid = new DL_Tieba_Efficiency_ExpandData();
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='1', busbute='1' WHERE (info = '1')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='1', busbute='1' WHERE (info = '2')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='1', busbute='1' WHERE (info = '3')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='1', busbute='1' WHERE (info = '4')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='2', busbute='2' WHERE (info = '5')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='2', busbute='2' WHERE (info = '6')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='2', busbute='2' WHERE (info = '7')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='2', busbute='2' WHERE (info = '8')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='2', busbute='2' WHERE (info = '9')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='3', busbute='3' WHERE (info = '10')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='3', busbute='3' WHERE (info = '11')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='3', busbute='3' WHERE (info = '12')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='3', busbute='3' WHERE (info = '13')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='3', busbute='3' WHERE (info = '14')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='3', busbute='3' WHERE (info = '15')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='3', busbute='3' WHERE (info = '16')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='3', busbute='3' WHERE (info = '17')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='3', busbute='3' WHERE (info = '18')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='3', busbute='3' WHERE (info = '19')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='3', busbute='2' WHERE (info = '20')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_expand SET priority='3', busbute='1' WHERE (info = '21')";
            $pid->query($sql);
            // clear
            $pid = new DL_Tieba_Efficiency_Cleard();
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='1', busbute='1' WHERE (info = '1')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='1', busbute='1' WHERE (info = '2')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='1', busbute='1' WHERE (info = '3')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='1', busbute='1' WHERE (info = '4')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='2', busbute='2' WHERE (info = '5')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='2', busbute='2' WHERE (info = '6')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='2', busbute='2' WHERE (info = '7')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='2', busbute='2' WHERE (info = '8')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='2', busbute='2' WHERE (info = '9')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='3', busbute='3' WHERE (info = '10')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='3', busbute='3' WHERE (info = '11')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='3', busbute='3' WHERE (info = '12')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='3', busbute='3' WHERE (info = '13')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='3', busbute='3' WHERE (info = '14')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='3', busbute='3' WHERE (info = '15')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='3', busbute='3' WHERE (info = '16')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='3', busbute='3' WHERE (info = '17')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='3', busbute='3' WHERE (info = '18')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='3', busbute='3' WHERE (info = '19')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='3', busbute='2' WHERE (info = '20')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_clear SET priority='3', busbute='1' WHERE (info = '21')";
            $pid->query($sql);

            // clear
            $pid = new DL_Tieba_Efficiency_Fkong();
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='1', busbute='1' WHERE (info = '1')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='1', busbute='1' WHERE (info = '2')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='1', busbute='1' WHERE (info = '3')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='1', busbute='1' WHERE (info = '4')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='2', busbute='2' WHERE (info = '5')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='2', busbute='2' WHERE (info = '6')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='2', busbute='2' WHERE (info = '7')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='2', busbute='2' WHERE (info = '8')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='2', busbute='2' WHERE (info = '9')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='3', busbute='3' WHERE (info = '10')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='3', busbute='3' WHERE (info = '11')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='3', busbute='3' WHERE (info = '12')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='3', busbute='3' WHERE (info = '13')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='3', busbute='3' WHERE (info = '14')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='3', busbute='3' WHERE (info = '15')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='3', busbute='3' WHERE (info = '16')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='3', busbute='3' WHERE (info = '17')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='3', busbute='3' WHERE (info = '18')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='3', busbute='3' WHERE (info = '19')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='3', busbute='2' WHERE (info = '20')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_fkong SET priority='3', busbute='1' WHERE (info = '21')";
            $pid->query($sql);

            // clear
            $pid = new DL_Tieba_Efficiency_Quality();
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='1', busbute='1' WHERE (info = '1')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='1', busbute='1' WHERE (info = '2')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='1', busbute='1' WHERE (info = '3')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='1', busbute='1' WHERE (info = '4')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='2', busbute='2' WHERE (info = '5')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='2', busbute='2' WHERE (info = '6')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='2', busbute='2' WHERE (info = '7')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='2', busbute='2' WHERE (info = '8')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='2', busbute='2' WHERE (info = '9')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='3', busbute='3' WHERE (info = '10')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='3', busbute='3' WHERE (info = '11')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='3', busbute='3' WHERE (info = '12')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='3', busbute='3' WHERE (info = '13')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='3', busbute='3' WHERE (info = '14')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='3', busbute='3' WHERE (info = '15')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='3', busbute='3' WHERE (info = '16')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='3', busbute='3' WHERE (info = '17')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='3', busbute='3' WHERE (info = '18')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='3', busbute='3' WHERE (info = '19')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='3', busbute='2' WHERE (info = '20')";
            $pid->query($sql);
            $sql = "UPDATE tieba_fengkong_badcase_quality SET priority='3', busbute='1' WHERE (info = '21')";
            $pid->query($sql);
        } else {
            echo json_encode(array('status' => 0, 'msg' => "参数有误"));
        }


    }
}