<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

class ajaxOffTaskAction extends Lib_Mis_BaseAction
{
    public static $TASK_TYPES = array(
        "blockmask" => 0,
        'forbidden' => 1,
        'delAllPost' => 2,
        "recoverPost" => 3,
        "setMainStat" => 4,
        "unsetMainStat" => 5,
        "unblockmask" => 6,
        "unforbidden" => 7
    );

    /**
     * 主函数
     * @return array
     */
    protected function run()
    {
        $violate_type = (int)Bingo_Http_Request::getNoXssSafe("pop_violate_type", -1);
        $pop_deal_remark = Bingo_Http_Request::getNoXssSafe("pop_deal_remark", '');
        $pop_deal_types = Bingo_Http_Request::getNoXssSafe("pop_deal_types", "");
        $pop_dispose_days = (int)Bingo_Http_Request::getNoXssSafe("pop_dispose_days", 0);
        $pop_sync_sources = Bingo_Http_Request::getNoXssSafe("pop_sync_sources", "");
        $task_id = Bingo_Http_Request::getNoXssSafe("task_id", "");

        Bingo_Log::notice(json_decode($pop_sync_sources));

        // 参数检查
        if ($violate_type == -1 || trim($pop_deal_remark) == '' || $pop_deal_types == "") {
            Bingo_Log::warning("invalid params");
            return self::_errRet(Tieba_Errcode::ERR_NVOTE_INVALID_PARAMS);
        }

        // 批量任务的搜索关键词不能为空
        $search_data = Bingo_Http_Request::getNoXssSafe("search_data", "");
        $scope_fname = Bingo_Http_Request::getNoXssSafe("scope_fname", "");
        $image_url = Bingo_Http_Request::getNoXssSafe("image_url", "");
        $op_uname = Bingo_Http_Request::getNoXssSafe("op_uname", "");
        $monitor_type = Bingo_Http_Request::getNoXssSafe("monitor_type", "");
        if (trim($search_data) == '' && trim($scope_fname) == '' && $image_url == ''
            && $op_uname == '' && $monitor_type = "") {
            Bingo_Log::warning("too broad search conditions");
            return self::_errRet2(Tieba_Errcode::ERR_NVOTE_INVALID_PARAMS,
                "搜索范围太大，请添加搜索条件");
        }
        $task = Service_ImageSearch_Task::getTask($task_id);
        $condition = json_decode($task["query_conditions"], true);

        if ($condition == false) {
            return self::_errRet(Tieba_Errcode::ERR_TBKEY_USER_NOT_EXISTS);
        }

        // 生成多个任务
        $pop_deal_types = explode(",", $pop_deal_types);
        foreach (self::$TASK_TYPES as $k => $v) {

            // 是否为规定的处理类型
            if (!in_array(self::$TASK_TYPES[$k], $pop_deal_types)) {
                continue;
            } else {
                $task_type = $v;
            }
            $tmp_pop_dispose_days = $pop_dispose_days;
            $final_condition = $condition;
            $create_extra = array("original_task_id" => $task_id);
            // 封禁时间
            if ($task_type == 0 || $task_type == 1) {
                $create_extra['dispose_days'] = $tmp_pop_dispose_days;
            } elseif ($task_type == 2) {
                $create_extra['dispose_days'] = 0;
            }

            // 添加任务
            $task = array(
                "query_conditions" => json_encode($final_condition),
                "task_type" => $task_type,
                "uid" => $this->_getUserId(),
                "uname" => $this->_getUserName(),
                "create_time" => time(),
                "create_extra" => json_encode($create_extra),
                "op_audit_type" => $violate_type,
                "op_remark" => $pop_deal_remark,
                "fail_list" => "",
            );
            Bingo_Log::warning(json_encode($task));
            $ret = Service_ImageSearch_Task::addTask($task);
            if ($ret == false || (isset($ret["errno"]) && $ret["errno"] > 0)) {
                Bingo_Log::warning("call Service_Quentin_Task::addTask fail, input="
                    . serialize($task) . ", output=" . serialize($ret));
            }

            // 添加同步源的衍生任务
            if ($pop_sync_sources != "") {
                $pop_sync_sources = explode(",", $pop_sync_sources);
                foreach ($pop_sync_sources as $src) {
                    $create_extra['sync_src'] = $src;
                    $task = array(
                        "es_conditions" => json_encode($final_condition),
                        "task_type" => $task_type,
                        "uid" => $this->_getUserId(),
                        "uname" => $this->_getUserName(),
                        "create_time" => time(),
                        "create_extra" => json_encode($create_extra),
                        "op_audit_type" => $violate_type,
                        "op_remark" => $pop_deal_remark,
                        "fail_list" => "",
                    );
                    Bingo_Log::warning(json_encode($task));
                    $ret = Service_ImageSearch_Task::addTask($task);
                    if ($ret == false || (isset($ret["errno"]) && $ret["errno"] > 0)) {
                        Bingo_Log::warning("call Service_Quentin_Task::addTask fail, input="
                            . serialize($task) . ", output=" . serialize($ret));
                    }
                }
            }

        }

        return self::_succRet(true);
    }
}