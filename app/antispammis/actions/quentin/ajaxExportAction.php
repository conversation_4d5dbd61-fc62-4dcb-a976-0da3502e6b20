<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @desc 举报mis导出数据
 * @date 2013-10-11
 */

class ajaxExportAction extends Lib_Mis_BaseAction
{
    /**
     * 主函数 - 旧导出函数，保留(已废弃)
     * @return bool
     */
    protected function runOld()
    {
        $count = intval(Bingo_Http_Request::getNoXssSafe('count', 3000));

        $condition = Lib_Quentin_Util::getEsConditions();
        if ($condition == false) {
            return self::_errRet(Tieba_Errcode::ERR_TBKEY_USER_NOT_EXISTS);
        }
        $condition['from'] = 0;
        $condition['size'] = $count;
        Bingo_Log::warning(json_encode($condition));

        $arrOutput = Lib_Quentin_Util::queryEs($condition);
        if ($arrOutput == false) {
            Bingo_Log::warning("query es fail, input="
                . json_encode($condition) . ", output=" . json_encode($arrOutput));
            return self::_errRet(Tieba_Errcode::SERVICE_CANNOT_BE_USED);
        }

        // 填充IP
        $final_hits = array();
        foreach ($arrOutput['hits']['hits'] as $post_obj) {
            if (!array_key_exists('ip', $post_obj)) {
                $input = array(
                    "post_ids" => array($post_obj['_source']['post_id']),
                    "ueg_ip" => 1
                );
                $res = Tieba_Service::call('post', 'getPostInfo', $input, NULL, NULL, 'post', 'php', 'utf-8');
                if ($res == false || $res['errno'] > 0) {
                    Bingo_Log::warning("call post::getPostInfo fail, input=" . json_encode($input) . ", output=" . json_encode($res));
                    $ip = '127.0.0.1';
                } else {
                    $ip = $res['output'][0]['ip'];
                    $ip = Lib_Quentin_Util::long2ip(intval($ip));
                }
                $post_obj['_source']['ip'] = $ip;
            }
            $final_hits[] = $post_obj;
        }
        self::outToExcel($final_hits);
        return true;
    }

    /**
     * 主函数
     * update zgw2014 20190506 优化
     * @return bool|array
     */
    protected function run()
    {
        // 测试导出10000条数据无压力,需要查询接口获取ip达到1600左右，es耗时12899ms，接口拼接ip耗时1.3489999771118s，总共耗时14s左右
        $count     = intval(Bingo_Http_Request::getNoXssSafe('count', 6000));
        $condition = Lib_Quentin_Util::getEsConditions();
        if ($condition == false) {
            return self::_errRet(Tieba_Errcode::ERR_TBKEY_USER_NOT_EXISTS);
        }
        $condition['size'] = $count;
        $condition['sort'] = array("_doc"); // 修改排序规则
        unset($condition['highlight']); // 去掉不必要的查询条件
        Bingo_Log::warning(json_encode($condition));

        Bingo_Log::notice('Step1: select es start_time：'.microtime(true).']');
        $arrOutput = Lib_Quentin_Util::queryEs($condition);
        Bingo_Log::notice('Step1: select es end_time：'.microtime(true).']');
        if ($arrOutput == false) {
            Bingo_Log::warning("query es fail, input="
                . json_encode($condition) . ", output=" . json_encode($arrOutput));
            return self::_errRet(Tieba_Errcode::SERVICE_CANNOT_BE_USED);
        }

        // 填充IP 接口处理一次最短0.03秒，最长0.09秒，计算3000个都需要查询ip，则是5.4秒可以全部查询完毕，可接受
        $i         = 0; // 统计需要填充补全ip的个数
        $post_list = array();
        Bingo_Log::notice('Step2: getPostInfo ip start_time：'.microtime(true).'] es output took ['.$arrOutput['took'].']');
        foreach ($arrOutput['hits']['hits'] as $post_scroll=>$post_obj) {
            if (!array_key_exists('ip', $post_obj['_source']) || empty($post_obj['_source']['ip'])) {
                $i++;
                $post_id = intval($post_obj['_source']['post_id']);
                $post_list[$post_id] = $post_scroll;
                $count = count($post_list);
                if($count!=0 && $count%50==0)
                {
                    $input = array(
                        "post_ids" => array_keys($post_list),
                        "ueg_ip"   => 1
                    );
                    $res = Tieba_Service::call('post', 'getPostInfo', $input, NULL, NULL, 'post', 'php', 'utf-8');
                    if ($res == false || $res['errno'] > 0) {
                        Bingo_Log::warning("call post::getPostInfo fail, input=" . json_encode($input) . ", output=" . json_encode($res));
                    } else {
                        foreach ($res['output'] as $post_intface_info)
                        {
                            $ip = $post_intface_info['ip'];
                            $ip = Lib_Quentin_Util::long2ip(intval($ip));
                            if(isset($post_list[$post_intface_info['post_id']]) && $arrOutput['hits']['hits'][$post_list[$post_intface_info['post_id']]])
                            {
                                $arrOutput['hits']['hits'][$post_list[$post_intface_info['post_id']]]['_source']['ip'] = $ip;
                            }
                        }
                    }

                    $post_list = array();
                }
            }
        }

        // 补全数据
        if(!empty($post_list))
        {
            $input = array(
                "post_ids" => array_keys($post_list),
                "ueg_ip"   => 1
            );
            $res = Tieba_Service::call('post', 'getPostInfo', $input, NULL, NULL, 'post', 'php', 'utf-8');
            if ($res == false || $res['errno'] > 0) {
                Bingo_Log::warning("call post::getPostInfo fail, input=" . json_encode($input) . ", output=" . json_encode($res));
            } else {
                foreach ($res['output'] as $post_intface_info)
                {
                    $ip = $post_intface_info['ip'];
                    $ip = Lib_Quentin_Util::long2ip(intval($ip));
                    if(isset($post_list[$post_intface_info['post_id']]) && $arrOutput['hits']['hits'][$post_list[$post_intface_info['post_id']]])
                    {
                        $arrOutput['hits']['hits'][$post_list[$post_intface_info['post_id']]]['_source']['ip'] = $ip;
                    }
                }
            }
        }

        Bingo_Log::notice('Step2: getPostInfo ip end_time：'.microtime(true).'] Need to fill ip number ['.$i.']');
        self::outToExcel($arrOutput['hits']['hits']);
        return true;
    }

    /**
     * 导出为excel
     * @param $arrData
     */
    public static function outToExcel($arrData)
    {
        $xlsFile = new Lib_Quentin_Excel();
        $arrDataTitle = array(
            'thread_id' => 'thread_id',
            'post_id' => 'post_id',
            'user_id' => 'user_id',
            'user_name' => 'user_name',
            'forum_name' => 'forum_name',
            'title' => 'title',
            'content' => 'content',
            'create_time' => 'create_time',
            'ip' => 'ip',
            'is_deleted' => 'is_deleted',
            'op_uid' => 'delete_op_uid',
            'op_uname' => 'delete_op_uname',
            'op_time' => 'delete_op_time',
            'call_from' => 'delete_call_from',
            'opgroup' => 'delete_opgroup',
            'monitor_type' => 'monitor_type'
        );
        $data = array();
        foreach ($arrData as $post) {
            $row = array();
            foreach ($arrDataTitle as $field_k => $field_v) {
                if ($field_k == 'create_time' || $field_k == 'op_time') {
                    if(!empty($post['_source'][$field_k])) {
                        $row[$field_k] = date("Y-m-d H:i:s", strtotime($post['_source'][$field_k]));
                    }
                } else {
                    $row[$field_k] = strval(iconv("UTF-8",
                        "GBK", $post['_source'][$field_k]));
                }
            }
            $data[] = $row;
        }
        array_unshift($data, $arrDataTitle);
        Bingo_Log::notice('导出的表头：'.json_encode($data['0']));
        $xlsFile->addArray($data);
        $xlsFile->generateXML('report_' . date('Ymd'));
        exit();
    }


    /**
     * 导出为csv
     * @param $head
     * @param $sql_all
     * @param $fileName
     */
    public static function putCsv($head, $sql_all, $fileName)
    {
        set_time_limit(0);
        ini_set('memory_limit', '128M');
        header('Content-Type: application/vnd.ms-execl');
        header('Content-Disposition: attachment;filename="' . $fileName);

        //每次只从数据库取100000条以防变量缓存太大
        $sqlLimit = 1000;
        // 每隔$limit行，刷新一下输出buffer，不要太大，也不要太小
        $limit = 1000;
        // 数据库连接
        $db = self::_getDB();
        $cnt = 0;

        //打开php标准输出流,以写入追加的方式打开
        $fp = fopen('php://output', 'a');

        // 写标题
        fputcsv($fp, $head);

        foreach ($sql_all as $item) {
            $item_count = $item['count'];
            $item_sql = $item['sql'];

            if ($item_count <= 0) {
                continue;
            }

            // 获取页数amis
            $pageSize = intval($sqlLimit);
            $total = intval($item_count);
            if ($total % $pageSize == 0) {
                $pageTotal = $total / $pageSize;
            } else {
                $pageTotal = floor($total / $pageSize) + 1;
            }

            for ($i = 0; $i < $pageTotal; $i++) {
                $dataArr = $db->query($item_sql . " limit " . $i * $sqlLimit . "," . $sqlLimit);
                foreach ($dataArr as $a) {
                    $cnt++;
                    if ($limit == $cnt) {
                        //刷新一下输出buffer，防止由于数据过多造成问题
                        ob_flush();
                        flush();
                        $cnt = 0;
                    }
                    $aa = array();
                    foreach ($a as $k => $v) {
                        $aa[$k] = iconv("UTF-8", "GBK", $v);
                    }
                    fputcsv($fp, $aa);
                }
            }
        }
    }
}

?>
