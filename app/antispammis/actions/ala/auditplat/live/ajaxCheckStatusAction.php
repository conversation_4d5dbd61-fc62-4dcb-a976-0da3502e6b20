<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [ajaxCheckStatusAction.php ]
 * <AUTHOR>
 * @DateTime 16/8/19 16:48
 * @brief
 */

class ajaxCheckStatusAction extends Lib_Mis_BaseAction{
    protected function run(){
        $liveids = Bingo_Http_Request::getGetNoXssSafe('liveids', array());

        $result = array();

        if(!empty($liveids)){
            $input=array(
                'live_ids'=> $liveids,
                // 'user_id' => 123,//主播id,如果是需要获取推流session,需要带有主播的id，为了保护推流地址
                'fetch_fields' => array("user_id","user_name"), //需要返回的数据，不写默认全返回
                'need_user' => 1, // 需要返回的数据，如果需要全部返还，写1即可('need_user' => 1)，
            );


            $arrAlaRes = Tieba_Service::call('ala', 'liveGetInfo', $input, null, null, 'post', 'php', 'utf-8');

            if($arrAlaRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
                $this->_throwException(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }



            foreach($arrAlaRes['data'] as $val){
                $result[$val['live_info']['live_id']] = $val['live_info']['live_status'];
                //$result[$val['live_info']['live_id']] = 2;
            }

        }

        $this->_retjson(Tieba_Errcode::ERR_SUCCESS, $result);
    }
}
 