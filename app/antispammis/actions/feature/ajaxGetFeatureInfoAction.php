<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [ajaxGetFeatureMetaAction.php 获取特征元数据]
 * <AUTHOR>
 * @DateTime 17/01/03 14:35
 * @brief
 */

class ajaxGetFeatureInfoAction extends Lib_Mis_BaseAction{
	/**
	 * [run description]
	 * <AUTHOR>
	 * @param
	 * @return
	 */
    protected function run()
    {
        // TODO: Implement run() method.
    	$type =  Bingo_Http_Request::getNoXssSafe ( 'type', '' );
    	$name = Bingo_Http_Request::getNoXssSafe ( 'name', '' );
    	$page = Bingo_Http_Request::getNoXssSafe ( 'page', 0 );
    	$pageSize = 200;
    	$result = array();
    	$arrInput = array(
    		'type' => $type,
    		'name' => $name,
    		'like' => true,
    	);
    	$ret = Dl_Feature_Feature::queryFeatureCount($arrInput);
    	if($ret['errno'] != 0 ) {
    		Bingo_Log::warning('update feature failed'.serialize($arrInput).serialize($ret));
    		$this->_throwException(Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL);
    	}
    	if($ret['ret'] == 0) {
    		$result['total'] = 0;
    		$this->_retjson(Tieba_Errcode::ERR_SUCCESS,$result);
    	}
    	else {
    		$count = intval($ret['ret']);
    		$arrInput = array(
    			'type' => $type,
    			'name' => $name,
    			'like' => true,
    			'offset' => $page*$pageSize,
    			'count' => $pageSize,
    		);
    		$ret = Dl_Feature_Feature::queryFeature($arrInput);
    		if($ret['errno'] != 0 ) {
    			Bingo_Log::warning('update feature failed'.serialize($arrInput).serialize($ret));
    			$this->_throwException(Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL);
    		}
    		Bingo_Log::warning(var_export($ret,true));
    		$result['total'] = $count;
    		$result['records'] = $ret['ret'];
    		$result['page'] = $page;
    		$result['size']	= $pageSize;
    		$final = array(
    			'errno' => 0,
    			'errmsg' =>'success',
    			'output' => $result,
    		);
    		header('Content-Type: application/json');
    		echo json_encode($final);
    		/* var_dump($result);
    		$this->_retjson(Tieba_Errcode::ERR_SUCCESS,$result); *///这个玩意做了转码
    	}
    }
}
