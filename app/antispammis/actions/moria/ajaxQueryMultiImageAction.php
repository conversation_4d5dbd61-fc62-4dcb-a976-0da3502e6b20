<?php

class ajaxQueryMultiImageAction extends Lib_Mis_BaseAction
{

    protected function run()
    {
        $arrInput = array();
        $arrInput['image_url'] = explode(",", trim(Bingo_Http_Request::getNoXssSafe("image_url", "")));
        $arrInput['type'] = intval(Bingo_Http_Request::getNoXssSafe("type", 0));
//        Bingo_Log::notice("call ".__METHOD__.' input ['.serialize($arrInput).']');

        // 验证参数
        $verifyRes = self::_checkParams($arrInput);
        if(true !== $verifyRes)
        {
            return self::_displayJson($verifyRes);
        }

        // 处理外网url转存到内网url
        $result = array(
            'result_data' => '',
        );
        if(1 == $arrInput['type'])
        {
            // 批量转存url
            $output = Lib_moria_Util::queryImageOuterUrl2TiebaUrl($arrInput['image_url']);
            $result['result_data'] = "以下是批量转存url结果，数据格式直接是转存后的url\n";
            Bingo_Log::notice('call '.__METHOD__.' Lib_moria_Util::queryImageOuterUrl2TiebaUrl output ['.json_encode($output).']');
        }

        // 查询sign值
        if(2 == $arrInput['type'])
        {
            $output = Lib_moria_Util::getMultiImgSign($arrInput['image_url']);
            $result['result_data'] = "以下是批量查询图片sign值结果，数据格式(采用\\t制表符分割)：image_url、image_sign\n";
        }
//        Bingo_Log::notice("call ".__METHOD__." getMultiImgSign output = ".json_encode($output));
        // 处理结果
        if(empty($output))
        {
            return self::_displayJson(array(
                'errno' => Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL,
                'errmsg' => "查询失败，请稍后再试"
            ));
        }

        // 针对结果进行格式化
        foreach ($output as $item)
        {
            if(2 == $arrInput['type'])
            {
                $result['result_data'] .= trim($item['image_url'])."\t".trim($item['image_result'])."\n";
            }

            if(1 == $arrInput['type'])
            {
                $result['result_data'] .= trim($item)."\n";
            }
        }

        $result['result_data'] = <<<EOF
{$result['result_data']}
EOF;
        // 最终统一处理结果
//        Bingo_Log::notice('call '.__METHOD__.' sign ['.json_encode($result).']');
        $this->_setXssSafe(false);
        return $this->_retjson(Tieba_Errcode::ERR_SUCCESS, $result);
    }



    /***
     * 验证参数的合理性
     * @param $arrInput
     * @return array|bool
     */
    private static function _checkParams(&$arrInput)
    {
        if(empty($arrInput['image_url']))
        {
            return array(
                'errno' => Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL,
                'errmsg' => "请输入图片url"
            );
        }

        // 未选择任务类型
        if(!$arrInput['type'])
        {
            return array(
                'errno' => Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL,
                'errmsg' => "请选择查询类型"
            );
        }

        // 如果选择的是查询sign值
        if(2 == $arrInput['type'] && count($arrInput['image_url']) > Lib_moria_Util::$queryMultiImageConfig['getSign'])
        {
            return array(
                'errno' => Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL,
                'errmsg' => "请输入少于".Lib_moria_Util::$queryMultiImageConfig['getSign'].'张图片进行查询sign值'
            );
        }

        // 如果选择的是url转存
        if(1 == $arrInput['type'] && count($arrInput['image_url']) > Lib_moria_Util::$queryMultiImageConfig['urlByurl'])
        {
            return array(
                'errno' => Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL,
                'errmsg' => "请输入少于".Lib_moria_Util::$queryMultiImageConfig['urlByurl'].'张图片进行url转存'
            );
        }

        return true;
    }
}