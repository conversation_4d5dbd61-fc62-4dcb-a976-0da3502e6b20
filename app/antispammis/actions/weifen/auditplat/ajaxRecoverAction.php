<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * [ajaxListAction.php 恢复]
 * <AUTHOR>
 * @DateTime 16/8/31 16:35
 * @brief
 */

class ajaxRecoverAction extends Lib_Mis_BaseAction{
    protected function run()
    {
        $ids = Bingo_Http_Request::getGetNoXssSafe('ids', '');
        if($ids == '' )
        {
            $this->_throwException(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrInput['ids'] = $ids;
        $arrInput['auditer'] = $this->_getLogin();
        $ret = Tieba_Service::call('uegnewpro','newproWefanRecoverComplain',array('req'=>$arrInput), null, null, 'post', 'php', 'utf-8');
        Bingo_Log::notice("newproWefanRecoverComplain input=".serialize($arrInput)." ret=".serialize($ret));
        if($ret['errno'] != Tieba_Errcode::ERR_SUCCESS){
            $this->_throwException(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $this->_retjson(Tieba_Errcode::ERR_SUCCESS,$ret);
    
    }
}
