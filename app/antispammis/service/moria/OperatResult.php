<?php
/**
 * Created by PhpStorm.
 * User: zgw2014
 * Date: 2019/11/13
 * Time: 13:40
 */

class Service_Moria_OperatResult extends Lib_Mis_BaseService
{
    const SERVICE_NAME = "Service_Moria_OperatResult";
    protected static $_conf = null;

    /**
     * 获取任务结果列表
     * @param $arrInput
     * @return array
     */
    public static function listResult($arrInput)
    {
        Bingo_Log::notice('call '.__METHOD__.' input  ['.json_encode($arrInput).']');
        $taskModel = new Dl_Moria_ImageResultModel();
        $field     = !isset($arrInput['field']) || empty($arrInput['field']) ? '*' : $arrInput['field'];
        $page      = $arrInput['page'];
        $pageSize  = $arrInput['page_size'];
        unset($arrInput['page']);
        unset($arrInput['page_size']);
        unset($arrInput['field']);

        // 计算总数
        $arrInput["1="] = 1;
        $ret = $taskModel->selectCount($arrInput);
        if ($ret === false) {
            $sql = $taskModel->getLastSQL();
            Bingo_Log::warning("selectCount fail, sql=" . $sql);
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        } else {
            $total = intval($ret);
        }

        // 计算分页
        $pageSize  = intval($pageSize);
        $pageTotal = ceil($total / $pageSize);

        // 获取当前分页数据
        $offset = ($page - 1) * $pageSize;
        $append = ' ORDER BY id desc ';
        $append .= "limit {$offset},{$pageSize} ";
        $tasks  = $taskModel->select($field, $arrInput, null, $append);

        if ($tasks === false) {
            $taskModel->getError();
            Bingo_Log::warning("list task fail, " . $taskModel->getError());
            $sql = $taskModel->getLastSQL();
            Bingo_Log::warning("list task fail, sql=" . $sql);
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        } else {
            $data = array(
                "count" => $total,
                "rows" => $tasks,
                "total" => $pageTotal,
            );
            return self::_succRet($data);
        }
    }

}