<?php
/**
 * Created by PhpStorm.
 * User: v_zhaoyu10
 * Date: 2021/6/7
 * Time: 10:51 AM
 */

class Service_Tieba_Efficiency_Project_Createproject
{
    /**
     * @name <EMAIL>
     * @title 项目文件入库
     * @return mixed
     */
    public static function createProjectData($data){
        $objDB = new DL_Tieba_Efficiency_CollectPro();
        $res = $objDB->insert($data);
        return $res;

    }
    /**
     * @name <EMAIL>
     * @title 项目文件入库
     * @return mixed
     */
    public static function editPronameDataById($data,$id){
        $objDB = new DL_Tieba_Efficiency_CollectPro();
        $res = $objDB->update($data,array(array("id","=",$id)));
        return $res;
    }

    /**
     * @name <EMAIL>
     * @title 获取项目所有字段
     * @return mixed
     */
    public static function getProjectDataAll($arrInput){
        $objDB = new DL_Tieba_Efficiency_CollectPro();

        $appends = "ORDER BY id DESC limit " . ($arrInput['page'] - 1) * $arrInput['pageSize'] . ", " . $arrInput['pageSize'];

        if ($arrInput['status'] == 99){
            $conds[] = array("status","!=","99");
        }else{
            if ($arrInput['status'] == "开启"){

                $conds[] = array("status","=","0");
            }else if ($arrInput['status'] == "关闭"){

                $conds[] = array("status","=","1");
            }else{

                $conds[] = array("status","!=","99");
            }
        }

        if ($arrInput['search'] == 99){
            $arrInput['search'] = "";
        }else{
            if (!empty($arrInput['search'])) {
                if (strlen($arrInput['text1']) == 0){
                    $arrInput['text1'] = null;
                }
                if (isset($arrInput['text1'])){
                    switch ($arrInput['search']) {
                        case "pro_name":
                            $conds[] = array('pro_name', 'like', '%' . $arrInput['text1'] . '%');
                            break;
                        case "pro_id":
                            $conds[] = array('id', '=', $arrInput['text1']);
                            break;
                        case "create_name":
                            $conds[] = array('create_name', '=', $arrInput['text1']);
                            break;
                    }
                }
            }
        }

        $numpage = $objDB->selectCount($conds);
        $fields = "id,pro_name,create_time,create_name,edit_time,edit_name,status";
        $ret = $objDB->select($fields,$conds,null,$appends);

        if ( false === $ret ) {
            $res['errno'] = Tieba_Errcode::ERR_DB_QUERY_FAIL;
            return $res;
        }
        $res['total'] = $numpage;
        $res['res'] = $ret;
        return $res;

    }

    /**
     * 根据pid获取贴子信息的方法
     * @<NAME_EMAIL>
     * @Time 2021/2/7 16:35
     */
    public static function getPostInfo($id, $product)
    {
        $idData = array();
        if ($product == "贴吧") {
            $idData = Service_Tieba_Efficiency_Conllectpid_Efficiencypid::getPostInfoAPI($id);
        } else {
            // 请求其他业务线的接口,目前不处理

        }
        return $idData;
    }

    /**
     * 用户名&用户ID互转
     * @<NAME_EMAIL>
     * @Time 2021/2/7 18:28
     */
    public static function getUserInfo($uid, $uname, $product)
    {
        $idData = array();
        if ($product == "贴吧") {
            if (!empty($uid)) {
                $idData = Service_Tieba_Efficiency_Conllectuser_Efficiencyuser::getUnameByUid($uid);
            } else {
                $id = Service_Tieba_Efficiency_Conllectuser_Efficiencyuser::getUidByUname($uname);
                $idData = Service_Tieba_Efficiency_Conllectuser_Efficiencyuser::getUnameByUid($id);
            }
        } else {
            // 请求其他业务线的接口,目前不处理

        }
        return $idData;
    }

    /**
     * 吧名&吧ID互转
     * @<NAME_EMAIL>
     * @Time 2021/2/18 18:23
     */
    public static function getForumInfo($fid, $fname, $product)
    {
        $idData = array();
        if ($product == "贴吧") {
            if (!empty($fid)) {
                $idData = Service_Tieba_Efficiency_Conllectuser_Efficiencyuser::getFnameByFid($fid);
            } else {
                $idData = Service_Tieba_Efficiency_Conllectuser_Efficiencyuser::getFidByFname($fname);
            }
        } else {
            // 请求其他业务线的接口,目前不处理
        }
        return $idData;
    }

    /**
     * @name <EMAIL>
     * @title 将仅拓展处理完的数据进行入库操作
     * @return mixed
     */
    public static function getByPidListUserInfoDatastorage($data,$type){

        $defaultArr = array(
            "task_name" => $data["task_name"],
            "release_time" => $data["release_time"],
            "lssued" => $data["lssued"],
            "feedback_time" => $data["feedback_time"],
            "info" => $data["info"],
            "expand_status" => $data['expand_status'],
            "status" => $data['status'],
            "load_time" => $data['load_time'],
            "busbute" => 1,
            "priority" => 1,
            "busline" => $data["busline"],
            "content" => "",
            "first" => 1,
            "second" => 1,
            "remarks" => $data["remarks"],
            "bulletin_time" => "0",
            "find_time" => "0",
            "tid" => 0,
            "fname" => "",
            "uname" => "",
            "uid" => 0,
            "tinick" => "",
            "title" => "",
            "create_time" => "0",
            "del_time" => "0",
        );


        if ($type == "post"){
            // 获取贴子
            $defaultArr["pid"] = $data['content'];
            $pid = $defaultArr["pid"];
            $pidInfo  = self::getPostInfo($pid, "贴吧");
            $defaultArr["tid"] = !empty($pidInfo["tid"]) ? $pidInfo["tid"] : $defaultArr["tid"];
            $defaultArr["uid"] = !empty($pidInfo["uid"]) ? $pidInfo["uid"] : $defaultArr["uid"];
            $defaultArr["uname"] = !empty($pidInfo["uname"]) ? $pidInfo["uname"] : $defaultArr["uname"];
            $defaultArr["fid"] = !empty($pidInfo["fid"]) ? $pidInfo["fid"] : $defaultArr["fid"];
            $defaultArr["fname"] = !empty($pidInfo["fname"]) ? $pidInfo["fname"] : $defaultArr["fname"];
            $defaultArr["title"] = !empty($pidInfo["title"]) ? $pidInfo["title"] : $defaultArr["title"];
            $defaultArr["content"] = !empty($pidInfo["content"]) ? $pidInfo["content"] : $defaultArr["content"];
            $defaultArr["create_time"] = !empty($pidInfo["create_time"]) ? $pidInfo["create_time"] : $defaultArr["create_time"];
            // 通过贴吧uid获取用户昵称
            $userInfo = self::getUserInfo($defaultArr["uid"], $defaultArr["uname"], "贴吧");
            $defaultArr["tinick"] = !empty($userInfo["nickname"]) ? $userInfo["nickname"] : "";
        }else if ($type == "forum"){
            $defaultArr["fid"] = $data['content'];
            $fid = $defaultArr["fid"];
            $forumInfo  = self::getForumInfo($fid, null,"贴吧");
            $defaultArr["fid"] = !empty($forumInfo["fid"]) ? $forumInfo["fid"] : $defaultArr["fid"];
            $defaultArr["fname"] = !empty($forumInfo["fname"]) ? $forumInfo["fname"] : $defaultArr["fname"];
        }else if ($type == "user"){
            $defaultArr["uid"] = $data['content'];
            $userInfo = self::getUserInfo($defaultArr["uid"], $defaultArr["uname"], "贴吧");
            $defaultArr["uid"] = !empty($userInfo["uid"]) ? $userInfo["uid"] : $defaultArr["uid"];
            $defaultArr["uname"] = !empty($userInfo["uname"]) ? $userInfo["uname"] : $defaultArr["uname"];
            $defaultArr["tinick"] = !empty($userInfo["nickname"]) ? $userInfo["nickname"] : "";
        }


        $newArr = $defaultArr;
        return $newArr;
    }

    /**
     * @name <EMAIL>
     * @title 将仅拓展处理完的数据进行入库操作
     * @return mixed
     */
    public static function getByPidListUserInfoDatastorageAll($data,$type){

        $defaultArr = array(
            "task_name" => $data["task_name"],
            "release_time" => $data["release_time"],
            "lssued" => $data["lssued"],
            "feedback_time" => $data["feedback_time"],
            "info" => $data["info"],
            "expand_status" => $data['expand_status'],
            "status" => $data['status'],
            "load_time" => $data['load_time'],
            "busbute" => 1,
            "priority" => 1,
            "busline" => $data["busline"],
            "content" => "",
            "first" => 1,
            "second" => 1,
            "remarks" => "",
            "bulletin_time" => "0",
            "find_time" => "0",
            "tid" => 0,
            "fname" => "",
            "uname" => "",
            "uid" => 0,
            "tinick" => "",
            "title" => "",
            "create_time" => "0",
            "del_time" => "0",
        );


        if ($type == "post"){
            // 获取贴子
            $defaultArr["pid"] = $data['content'];
            $pid = $defaultArr["pid"];
            $pidInfo  = self::getPostInfo($pid, "贴吧");
            $defaultArr["tid"] = !empty($pidInfo["tid"]) ? $pidInfo["tid"] : $defaultArr["tid"];
            $defaultArr["uid"] = !empty($pidInfo["uid"]) ? $pidInfo["uid"] : $defaultArr["uid"];
            $defaultArr["uname"] = !empty($pidInfo["uname"]) ? $pidInfo["uname"] : $defaultArr["uname"];
            $defaultArr["fid"] = !empty($pidInfo["fid"]) ? $pidInfo["fid"] : $defaultArr["fid"];
            $defaultArr["fname"] = !empty($pidInfo["fname"]) ? $pidInfo["fname"] : $defaultArr["fname"];
            $defaultArr["title"] = !empty($pidInfo["title"]) ? $pidInfo["title"] : $defaultArr["title"];
            $defaultArr["content"] = !empty($pidInfo["content"]) ? $pidInfo["content"] : $defaultArr["content"];
            $defaultArr["create_time"] = !empty($pidInfo["create_time"]) ? $pidInfo["create_time"] : $defaultArr["create_time"];
            // 通过贴吧uid获取用户昵称
            $userInfo = self::getUserInfo($defaultArr["uid"], $defaultArr["uname"], "贴吧");
            $defaultArr["tinick"] = !empty($userInfo["nickname"]) ? $userInfo["nickname"] : "";
        }else if ($type == "forum"){
            $defaultArr["fid"] = $data['content'];
            $fid = $defaultArr["fid"];
            $forumInfo  = self::getForumInfo($fid, null,"贴吧");
            $defaultArr["fid"] = !empty($forumInfo["fid"]) ? $forumInfo["fid"] : $defaultArr["fid"];
            $defaultArr["fname"] = !empty($forumInfo["fname"]) ? $forumInfo["fname"] : $defaultArr["fname"];
        }else if ($type == "user"){
            $defaultArr["uid"] = $data['content'];
            $userInfo = self::getUserInfo($defaultArr["uid"], $defaultArr["uname"], "贴吧");
            $defaultArr["uid"] = !empty($userInfo["uid"]) ? $userInfo["uid"] : $defaultArr["uid"];
            $defaultArr["uname"] = !empty($userInfo["uname"]) ? $userInfo["uname"] : $defaultArr["uname"];
            $defaultArr["tinick"] = !empty($userInfo["nickname"]) ? $userInfo["nickname"] : "";
        }


        $newArr = $defaultArr;
        return $newArr;
    }

}