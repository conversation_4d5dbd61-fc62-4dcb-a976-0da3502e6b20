<?php
/*
 * ����ٶ��ڲ�Ӧ�ã��ṩ�����µ�access token��һ�ֹ��ڲ�Ӧ�÷���˵���Open APIʱʹ�ã�һ�ֹ��ڲ�Ӧ�û���������ĳ���ʹ�á�
 * ������access token�������Ի���һ���Ĺ��򣬽�������֤��
 * ��SDK��Ŀ�ģ��Ǿ����ܵķ����ڲ���Ʒ�����ɴ���access token��ʹ���ϸ�ڶԲ�Ʒ��͸����
 *
 * ���У��������������Ӧ��ʹ�õ�access token����Ҫ�ɴ�Ӧ�õķ�����������access token��Ȼ�󴫸�������ͻ��ˣ���������档
 * ���������cookie��ҳ��ˢ��ʱ���������������token�������ͻ��ˡ�
 *
 * ʹ��������token��Ҫ������Ȩ���ɲο���
 * 	http://wiki.babel.baidu.com/twiki/bin/view/Ecmp/OpenPlatform/Openapi_faq#��������ڲ�tokenȨ��
 *
 * <code>
 * require_once 'InnerToken.php';
 * $appid = '8333446';
 * $uid = '67890';
 * $sk = 'x3yjA5YPqBRfCFeG1DoTcaDhzdXDkOZw';
 * $it = new InnerToken(11);
 * $token = $it->generateToken($appid, $uid, $sk);
 * if ($token === false) {
 * 	//������
 * }
 * </code>
 *
 * <AUTHOR>
 * @note If you need C/C++ SDK, do not hesitate to let me know.
 */
class Lib_InnerToken {
    /**
     * ���������Open APIʱ��ʹ�õ�access token�����ͣ�����
     * @var const int
     */
    const TOKEN_TYPE_SERVER = 11;

    /**
     * ����������ĳ�������Open APIʱ��ʹ�õ�access token�����ͣ�����
     * @var const int
     */
    const TOKEN_TYPE_BROWSER = 10;

    /**
     * ��������access token������
     * @var int
     */
    protected $token_type;

    /**
     * token����ʱ��ʱ���������ǩ��
     * @var int
     */
    protected $timestamp;

    /**
     * ���캯��
     * @param int $token_type
     */
    public function __construct($token_type)
    {
        switch ((int)$token_type) {
            case self::TOKEN_TYPE_SERVER:
                $this->token_type = self::TOKEN_TYPE_SERVER;
                break;
            case self::TOKEN_TYPE_BROWSER:
                $this->token_type = self::TOKEN_TYPE_BROWSER;
                break;
            default:
                $this->token_type = 0;//invalid
        }

        $this->timestamp = time();
    }

    /**
     * ǩ������
     * browser oriented: sign=md5(timestamp+cookie_BAIDUID+appid+sk)
     * server oriented: sign=md5(timestamp+passport_ID+appid+sk)
     * @param int $appid Ӧ�õ�ID
     * @param int $id (�������tokenʱ��Ϊpassport ID) or string (�������tokenʱ��Ϊcookie baiduid)
     * @param string $sk Ӧ�õ�secret key
     * @return string or bool false
     */
    protected function sign($appid, $id, $sk)
    {
        if ((int)$appid < 1 || !isset($sk[31]) || !ctype_alnum($sk))
            return false;

        switch ($this->token_type) {
            case self::TOKEN_TYPE_SERVER:
                //passport ID
                if (!ctype_digit($id))
                    return false;
                return md5($this->timestamp . $id . $appid . $sk);
            case self::TOKEN_TYPE_BROWSER:
                //cookie BAIDUID
                if (empty($id))
                    return false;
                return md5($this->timestamp . $id . $appid . $sk);
            default:
                return false;
        }
    }

    /**
     * token���ɺ���
     * @param int $appid Ӧ�õ�ID
     * @param int $id (�������tokenʱ, Ϊpassport ID, δ��¼ʱ��0��ʾ) or string (�������tokenʱ, Ϊcookie BAIDUID)
     * @param string $sk Ӧ�õ�secret key
     * @return string or bool false
     */
    public function generateToken($appid, $id, $sk)
    {
        if (!$this->token_type)
            return false;

        $sign = $this->sign($appid, $id, $sk);
        if (!$sign)
            return false;

        switch ($this->token_type) {
            case self::TOKEN_TYPE_SERVER:
                if (!ctype_digit($id))
                    return false;
                return self::TOKEN_TYPE_SERVER . ".$sign.{$this->timestamp}.$id-$appid";
            case self::TOKEN_TYPE_BROWSER:
                return self::TOKEN_TYPE_BROWSER . ".$sign.{$this->timestamp}.$appid";
            default:
                return false;
        }
    }
}