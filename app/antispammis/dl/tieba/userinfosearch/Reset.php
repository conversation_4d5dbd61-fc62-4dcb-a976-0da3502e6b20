<?php

class Dl_Tieba_UserInfoSearch_Reset extends Dl_Mis_Model
{

    /**
     * Notes: 重置用户信息
     * @param $uid
     * @param $type
     * @param $nickname
     * @return bool
     */
    public function resetAttr($uid, $type, $nickname)
    {
        $arrInput = array(
            'user_id' => $uid,
            'attr_name' => $type,
            'attr_value' => $nickname,
        );
        $arrOutput = Tieba_Service::call('user', 'setUserAttr', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (!$arrOutput) {
            $arrOutput = Tieba_Service::call('user', 'setUserAttr', $arrInput, null, null, 'post', 'php', 'utf-8');
        }
        if (is_array($arrOutput) && isset($arrOutput['errmsg']['errno']) && $arrOutput['errmsg']['errno'] == 0) {
            return true;
        } else {
            Bingo_Log::warning(__FUNCTION__ . " call setUserAttr err input[" . json_encode($arrInput) . "] output[" . json_encode($arrOutput) . "]");
            return false;
        }
    }

    public function ralResetServer($uid, $type, $val)
    {
        $arrInput = array(
            "user_id" => $uid,
            "attr_name" => $type,
            "attr_value" => $val,
        );

        $ret = Tieba_Service::call('post', 'setUserAttr', $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($ret == false) {
            Bingo_Log::warning(__FUNCTION__ . " call ralResetServer err input[" . json_encode($arrInput) . "] output[" . json_encode($ret) . "]");
            return false;
        }
        if ($ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(__FUNCTION__ . " call ralResetServer failed input[" . json_encode($arrInput) . "] output[" . json_encode($ret) . "]");
            return false;
        }

        Bingo_Log::warning(__FUNCTION__ . " call ralResetServer success input[" . json_encode($arrInput) . "] output[" . json_encode($ret) . "]");
        return true;
    }

    /**
     * Notes: 重置图片
     * @param $uid
     * @return bool
     */
    public function resetImg($uid)
    {
        //pass 接口
        ral_set_header("Host: passport.baidu.com");
        ral_set_pathinfo('/sys/delete');
        ral_set_querystring('');

        $params = array('user_id' => $uid);
        $res = Tieba_Ral::call('headphotoedit', 'post', $params, rand());
        if (!$res) {
            $res = Tieba_Ral::call('headphotoedit', 'post', $params, rand());
        }
        if ($res == false || !isset($res['errno']) || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(__FUNCTION__ . " call ralResetServer err input[" . json_encode($params) . "] output[" . json_encode($res) . "]");
            return false;
        }
        return true;
    }

    public function ralResetAvatar($uid)
    {
        // 回调pass 接口
        $params = array(
            'user_id' => $uid,
//            'picture_id' => $picid,
            'action' => 'delete',
        );
        ral_set_pathinfo('/sys/delete');
        ral_set_header('Host:passport.baidu.com');
        $arrRet = ral('passport_name', 'post', $params, rand());
        if (!$arrRet) {
            // retry
            $arrRet = ral('passport_name', 'post', $params, rand());
        }
        if ($arrRet == false) {
            Bingo_Log::warning(__FUNCTION__ . " call ralResetServer err input[" . json_encode($params) . "] output[" . json_encode($arrRet) . "]");
            return false;
        }
        if ($arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(__FUNCTION__ . " call ralResetServer failed input[" . json_encode($params) . "] output[" . json_encode($arrRet) . "]");
            return false;
        }

        Bingo_Log::warning(__FUNCTION__ . " call ralResetServer success input[" . json_encode($params) . "] output[" . json_encode($arrRet) . "]");
        return true;
    }

}