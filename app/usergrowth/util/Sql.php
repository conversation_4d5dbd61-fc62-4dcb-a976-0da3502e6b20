<?php
/**
 * Util_Sql
 * hubin13
 */

class Util_Sql {
	
	/**
	 * 生成in条件
	 * @param 
	 * @return 
	 */
	public static function createInConditon($value){
		
		if(empty($value)||!is_array($value)){
			return $value;
		}
		$conditon = '';
		if(is_numeric($value[0]) ){
			$strValueList = join(',', $value);
			$condition = ' ('.$strValueList.') ';
		}else{
			$arrTrans = array();
			foreach ($value as $each){
				$temp = '\''.$each.'\'';
				$arrTrans[] = $temp;
			}
			$strValueList = join(',', $arrTrans);
			$condition = ' ('.$strValueList.') ';
		}
		
		return $condition;
	}
	
}