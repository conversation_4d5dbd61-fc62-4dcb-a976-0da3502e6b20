<?php
/**
 * 功能PUSH， 触达：1.通知 2.聊天IM
 * User: liguang03
 * Date: 2020/05/28
 * Time: 下午10:46
 */
class Service_Tool_Functionpush {


    /**
     * 传可变参数
     * 该函数只校验是否存在该值，对具体的值有效性不做校验
     * @param unknown $arrInput, x, xx, xxx,
     * @return  string $key
     */
    private static function  _checkParam(){
        $args = func_get_args();
        if(null == $args || empty($args) || null == $args[0] || empty($args[0]) || !is_array($args[0])){
            return false;
        }
        $arrInput = $args[0];
        $count = count($args);
        for ($i = 1; $i < $count; $i++){
            if(!isset($arrInput[$args[$i]])){
                return false;
            }
        }
        return true;
    }

    //发push的通用方法
    public static function pushMsgbyUid($arrInput){
        if (!self::_checkParam($arrInput,'title','link','pic_url','abstract','from_id','from_name','to_id')){
            Bingo_Log::warning(__METHOD__ . ' input params error :'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //加版控
        $varrInput = array(
            'user_id' => $arrInput['to_id'],
        );
        $verRet = Tieba_Service::call('impusher', 'getCidsByUid', $varrInput, null, null, 'post', null, 'utf8');
        if (false == $verRet || $verRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf("call impusher getCidsByUid fail. input [%s] output [%s]",serialize($varrInput),serialize($verRet)));
            return self::_errRet($verRet['errno']);
        }
        $data = $verRet['data'][$arrInput['to_id']];
        array_multisort(array_column($data,'update_time'),SORT_DESC,$data);
        $version = $data[0]['version'];
        if ($data[0]['client_type'] == 1){
            $packetConfigVersion = '11.6.0';
        }else{
            $packetConfigVersion = '11.6.0.0';
        }
        if(!(Molib_Util_Version::compare($packetConfigVersion, $version) >= 0)) {
            Bingo_Log::warning(sprintf("client version too low. input [%s] output [%s]",serialize($varrInput),serialize($verRet)));
            return self::_errRet(Tieba_Errcode::ERR_CLIENT_VERSION_TOO_LOW);
        }
        $logId = Bingo_Log::getLogId();

        $content = array (
            'log_id' => strval($logId),
            'pri_key' => '000000',
            'topic_id' => '000000',
            'tids' => '12345',
            'title' => $arrInput['title'],
            'url' => $arrInput['link'],
            'src' => $arrInput['pic_url'],
            'subSrc' => $arrInput['pic_url'],
            'abstract' => $arrInput['abstract'],
            'text' => $arrInput['abstract'],
            'ext' =>
                array (
                    'from' => $arrInput['from_id'],
                    'pushtype' => 1,
                    'tp' => '',
                    'url' => $arrInput['link'],
                    'tid' => '',
                    'subtp' => 1,
                ),
        );
        $push_content = Bingo_String::array2json($content,Bingo_Encode::ENCODE_UTF8);

        $account_info = array (
            'user_id' => $arrInput['from_id'],
            'user_name' => $arrInput['from_name'],
            'user_type' => 4,
            'group_type' => 30,
        );

        $req = array(
            'push_content' => $push_content,
            'target_users'=> json_encode(array($arrInput['to_id'])),
            'account_info' => Bingo_String::array2json($account_info,Bingo_Encode::ENCODE_UTF8),
            'push_conf'=> json_encode(array(
                'client_type'=>'3',
                'push_type'=>'1',
                "notify_bar" => '1',
                "red_point" => '1',
                "msglogic" => '1',
                'msg_type'=>  '7',
                'data_type'=>'uid',
                'app_name'=>'tieba',
            )),
        );
        ral_set_pathinfo("/pushMsg");
        ral_set_logid($logId);

        $sendOutput = Tieba_Safemsg::ral("push-core",'post',$req,null);
        $sendOutput = json_decode($sendOutput, true);
        if (false === $sendOutput || '210000' != $sendOutput['errno']){
            Bingo_Log::warning(__METHOD__.'-call-sendMuticastPersonalMsg fail input:'.serialize($req).' output:'.json_encode($sendOutput));
            return self::_errRet($sendOutput['errno']);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }
	
	/**
	 *
	 * @param unknown $errno
	 * @param unknown $arrData
	 * @param string $retKey
	 * @return multitype:unknown
	 */
	private static function _errRet($errno = Tieba_Errcode::ERR_SUCCESS, $errmsg = '', $arrData = array(),  $retKey = 'data'){
		return array(
				'errno' => $errno,
				'msg' => empty($errmsg) ? Tieba_Error::getErrmsg($errno) : $errmsg,
				 $retKey => $arrData,
		);
	}
}