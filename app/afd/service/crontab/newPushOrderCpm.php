<?php
ini_set('memory_limit','512M');
define('ROOT_PATH', dirname(__FILE__). '/../../');

require_once ROOT_PATH . 'service/crontab/Mail_MSGLib/Message.php';


set_include_path(get_include_path() . PATH_SEPARATOR. ROOT_PATH);
if (!empty($argv[1])) {
    define('MAIL_TO', $argv[1]);
    define('CC_TO', $argv[1]);
} else {
    define('MAIL_TO', '<EMAIL>');
    define('CC_TO', '<EMAIL>,<EMAIL>');
}

define('L_DEBUG' , 1);
define('L_ERROR' , 2);
define('L_WARNING' , 4);
define('L_EVENT' , 7);

$mailAllstr = '';

$indexObj = new newPushOrderCpm();

// 记录时间，每间隔30秒执行一次
$crontabStartTime = time();
$runStartTime = 0;

_LOG("run script start time:" . time(), L_DEBUG);
while (true) {
    $runNowTime = time();

    // 定时脚本每5分钟执行一次，如果不足50秒，则不再执行, 先行返回，等待下次脚本触发再次执行
    if (($runNowTime - $crontabStartTime) >= 250) {
        break;
    }

    // 如果是首次或者距离上次执行已超过10秒，停止执行
    if ($runStartTime == 0 || ($runNowTime - $runStartTime) >= 30) {

        _LOG('>>>>>>>>>> start run pushOrderCpm >>>>>>>>>>', L_DEBUG);
        $indexObj->run();
        _LOG('<<<<<<<<<< end run pushOrderCpm <<<<<<<<<<', L_DEBUG);
        $runStartTime = time();
    }

    sleep(1);
}
_LOG("run script end time1:" . time(), L_DEBUG);

//mail_report('end run pushOrderCpm', 'end run pushOrderCpm');
//mail_report('run pushOrderCpm info', $mailAllstr);

_LOG("run script end time2:" . time(), L_DEBUG);
class newPushOrderCpm
{
    const LOCK_KEY = "GDCPM_lock";
    const UNIT_CPM_PREFIX = "GD_UNIT_CPM_";

    const KEY_EXPIRE = 60;
    const CUT_CPM_NUM = 3;
    const CUT_QUERY_CPM_NUM = 50;
    const IDEA_GROUP = 2;

    public $db = null;
    public $reids = null;
    public $t1 = array();
    //public $place_ids = array();

    // 缓存按天控量数据
    //private static $_arrUnitCpmBudget = array();

    public function __construct() {
        $db = Service_Libs_Gd_Db::getDB();
        if ($db == null) {
            _LOG('get db handle error!', L_ERROR);
            mail_report('统计CPM连接数据库失败', 'get db error');
            exit;
        } else {
            $this->db = $db;
        }

        $redis = Service_Libs_Gd_Redis::getRedis();
        if ($redis == null) {
            _LOG('get redis handle error!', L_ERROR);
            mail_report('统计CPM连接redis失败', 'get redis error');
            exit;
        } else {
            $this->redis = $redis;
        }

        /*$place_data = fetchUrlGet(Util_Gd_Const::$sspPlaceUrl, 1500, 1500);
        if($place_data == false) {
            _LOG('fetchurl:'.Util_Gd_Const::$sspPlaceUrl, L_ERROR);
            mail_report('统计CPM获取广告位失败', Util_Gd_Const::$sspPlaceUrl);
            exit;
        } else {
            $place_arr = json_decode($place_data, true);
            if(!empty($place_arr['data'])) {
                foreach($place_arr['data'] as $k => $value) {
                    $this->place_ids[] = $value['placeId'];
                }
            }
        }*/
    }

    /**
     * @param $key string
     */
    public function starttime($key) {
        $this->t1[$key] = microtime(true);
    }

    /**
     * @param $key string
     */
    public function endtime($key) {
        $exe_time = round(microtime(true)-$this->t1[$key], 3);
        _LOG($key. " time: ".$exe_time, L_DEBUG);
    }

    /**
     * @return array()
     */
    public function run() {
        $this->starttime("pushOrderCpm");

        // 检查实时流最近更新时间，如果更新时间存在问题，则停止投放
        $checkMaxDate = $this->getShowDataMaxDate();
        if (false == $checkMaxDate) {
            mail_report('实时流数据延时', 'GD实时流数据库数据延迟，请及时处理！');
            sendMessage("GD实时流延时，请尽快处理!");
            _LOG("unlegal max date in db, stop do it!!!", L_WARNING);
            return -1;
        }

        //获取有效计划
        $this->starttime("getValidPlanIds");
        $planIdsArr = $this->getValidPlanIds();
        $this->endtime("getValidPlanIds");
        if(empty($planIdsArr)) {
            return -1;
        }

        //获取有效单元
        $this->starttime("getValidUnit");
        $unitArr = $this->getValidUnit($planIdsArr);
        $this->endtime("getValidUnit");
        
        if(empty($unitArr)) {
            return -1;
        }

        //再根据其他条件过滤有效订单,并返回单元id
        $totalUnitDataArr = $this->filterByValidUnit($unitArr);	
        $totalUnitIdsArr = $totalUnitDataArr['ids'];
        _LOG("valid unitIds after filter:".implode("," , $totalUnitIdsArr), L_DEBUG);
        if(empty($totalUnitIdsArr)) {
            return -1;
        }

        //获取目前投放广告的广告位，按广告位依次处理
        $placeUnitDataArr = $this->splitUnitByPlaceId($totalUnitDataArr);
        foreach ($placeUnitDataArr as $placeId => $placeUnitDatas) {
            _LOG("process place_id=" . $placeId, L_DEBUG);
            $unitIdsArr = $placeUnitDatas['ids'];
            _LOG("place unitIds:" . implode(",", $unitIdsArr), L_DEBUG);
            $unitDataArr = $placeUnitDatas['data'];

            //_LOG("process unit_ids=" . var_dump($unitIdsArr), L_DEBUG);
            //获取有效创意
            $this->starttime("getValidIdea");
            $ideaArr = $this->getValidIdea($unitIdsArr);
            $this->endtime("getValidIdea");
            if(empty($ideaArr)) { 
                continue;
            }

            //过滤有效创意
            $ideaDataArr = $this->filterByValidIdea($ideaArr);	

            //过滤存在有效创意，且设置了控量与控速的单元
            $arrUnitIds = $this->buildOnlineData($placeUnitDatas, $ideaDataArr) ;

            if (empty($arrUnitIds)) {
                continue;
            }

            $this->starttime("getOrderCpmData");
            $arrOrderCpms = $this->getOrderCpmData($arrUnitIds, $unitDataArr);

            _LOG("process OrderCpms:".json_encode($arrOrderCpms), L_DEBUG);
            if (empty($arrOrderCpms)) {
                continue;
            }
            $this->endtime("getOrderCpmData");

            //保存到redis
            $i = 2;
            while ($i > 0) {
                $this->starttime("pushToRedis");
                //获取分布式锁
                if($this->lock()) {
                    $ret = $this->pushToRedis($placeId, $arrOrderCpms);
                }
                $this->unlock();
                $this->endtime("pushToRedis");

                if($ret != -1) {
                    _LOG("redis success!", L_DEBUG);
                    break;
                } else {
                    _LOG("redis error!".$i, L_ERROR);
                }

                $i--;
            }

            if ($i <= 0) {
                _LOG("retry 3 fail!", L_ERROR);
                mail_report('统计CPM数据更新redis失败', 'set redis 3 times error');
                continue;
            }
        }


        $this->endtime("pushOrderCpm");
    }

    /**
     * @return int
     */
    public function lock() {
        $time = time();
        $params = array(
            'key'=>self::LOCK_KEY,
            'value'=>$time,
        );
        $retry=5;

        while($retry>0) {
            $ret = $this->redis->SETNX($params);
            if($ret["ret"][self::LOCK_KEY]) {
                _LOG("get nx lock", L_DEBUG);
                return 1;
            } else {
                _LOG("lock by nx", L_DEBUG);
                //判断是否过期
                $retGet = $this->redis->GET($params);
                if($retGet["ret"][self::LOCK_KEY]) {
                    $lockTime1  = $retGet["ret"][self::LOCK_KEY];	
                    if($time - $lockTime1 >= 20) {
                        $retGetSet = $this->redis->GETSET($params);
                        _LOG("lock time out", L_DEBUG);
                        if($retGetSet["ret"][self::LOCK_KEY]) {
                            $lockTime2  = $retGetSet["ret"][self::LOCK_KEY];
                            if($lockTime1 == $lockTime2) {
                                _LOG("get set lock", L_DEBUG);
                                return 1;
                            } 			
                        }

                    }
                }
                sleep(20);
                _LOG("retry:".$retry, L_DEBUG);  		
                $retry--;
            }
        }
        return 0;
    }

    /**
     * @return int
     */
    public function unlock() {
        $params = array(
            'key'=>self::LOCK_KEY,
        );
        $this->redis->DEL($params);
        _LOG("del nx lock", L_DEBUG);
        return 1;
    }

    /**
     * @param $onlineIndexArr array()
     * @return array()
     */
    public function pushToRedis($placeId, $arrOrderCpms) {
        // 按广告位，将对应广告位的信息存入到redis对应的key中
        $redisInput = array();
        $inputParams = array();

        $cmpInfos = json_encode($arrOrderCpms);
        for ($index = 1; $index <= 10; $index++) {
            $key = self::UNIT_CPM_PREFIX . $placeId . "_" . $index;
            $inputParams[$key] = array(
                'key' => $key,
                'value' => $cmpInfos,
                'seconds' => self::KEY_EXPIRE,    
            );
        }

        $key_num = count($inputParams);
        $number = ceil($key_num / self::CUT_CPM_NUM);
        _LOG('store key num:'.$key_num.",cut:".$number, L_DEBUG);
        $redisKeyArr = array();
        for ($j = 0; $j < $number; $j++ ) {
            $redisInput['reqs'] = array();
            $start = $j * self::CUT_CPM_NUM;
            $redisKeyArr = array_slice($inputParams, $start, self::CUT_CPM_NUM);

            $redisInput['reqs'] = array_values($redisKeyArr);
            $ret = $this->redis->SETEX($redisInput);

            //mail_report("push redis key ", "key:" . var_export($redisInput['reqs'], true));
            if ($ret['err_no'] != 0) {
                _LOG('redis mset error!'.json_encode($redisInput), L_ERROR);
                return -1;
            } else {
                foreach ($ret['ret'] as $k => $v) {
                    if ($v !='OK') {
                        _LOG("redis key:$k not ok!".json_encode($redisInput), L_ERROR);
                        return -1;
                    }
                }
                _LOG('redis mset success!', L_DEBUG);
            }
        }

        return 0;
    }

    /**
     * @param  array()
     * @return array()
     */
    public function splitUnitByPlaceId($arrUnitData) {
        if (empty($arrUnitData) || empty($arrUnitData['data'])) {
            return array();
        }

        $placeUnitDatas = array();

        foreach ($arrUnitData['data'] as $unitInfo) {
            $strPlaceIds = $unitInfo['place_id'];
            //广告位合并后导致place_id以逗号分隔
            $arrPlaceId = explode(',', $strPlaceIds);
            foreach ($arrPlaceId as $placeId) {
                $unitId = $unitInfo['id'];
                if (!isset($placeUnitDatas[$placeId])) {
                    $placeUnitDatas[$placeId] = array();
                    $placeUnitDatas[$placeId]['data'] = array();
                    $placeUnitDatas[$placeId]['ids'] = array();
                }
                //修正当前unit的place_id
                $newUnitInfo = $unitInfo;
                $newUnitInfo['place_id'] = $placeId;

                $placeUnitDatas[$placeId]['data'][$unitId] = $newUnitInfo;
                $placeUnitDatas[$placeId]['ids'][] = $unitId;    
            }
        }

        return $placeUnitDatas;
    }

    /**
     * @param $arrUnitIds
     * @param $arrUnitDatas
     * @return array
     */
    public function getOrderCpmData($arrUnitIds, $arrUnitDatas) {
        //存放订单投放量信息
        $arrId2RtCpm = array();

        // 因为存在一个广告位同时投放的订单太多的可能性， 所以将查询控量信息分多步骤进行
        $key_num = count($arrUnitIds);
        $number = ceil($key_num / self::CUT_QUERY_CPM_NUM);
        _LOG('query order cpm key num:' . $key_num . " cut:" . $number, L_DEBUG);

        for ($j = 0; $j < $number; $j++) {
            $start = $j * self::CUT_QUERY_CPM_NUM;
            $splitUnitIds = array_slice($arrUnitIds, $start, self::CUT_QUERY_CPM_NUM);

            $splitUnitIds = array_values($splitUnitIds);
            
            $splitCpmData = $this->splitGetOrderCpmData($splitUnitIds, $arrUnitDatas);
            if (!empty($splitCpmData))  {
                foreach ($splitCpmData as $key => $value) {
                    $arrId2RtCpm[$key] = $value;
                }
            }
        }

        return $arrId2RtCpm;
    }

    /**
     * @param $arrUnitIds
     * @param $arrUnitDatas
     * @return array
     */
    public function splitGetOrderCpmData($arrUnitIds, $arrUnitDatas) {
        //存放订单投放量信息
        $arrId2RtCpm = array();

        // 根据设置了控量和匀速的订单ID，来获取订单的投放总量预计今日投放总量
        $idList = implode(',', $arrUnitIds);
        $currentDate = date('Ymd', time());
        $stratTime = intval($currentDate . '0000');
        $endTime = intval($currentDate . '2359');
        if (null === ($cmpDb = Service_Libs_Gd_Db::getDB())) {
            _LOG("get order cpm db fail.", L_ERROR);
            return $arrId2RtCpm;
        }

        // 查询今日之前总曝光量
        $sql = "select unit_id, sum(exposure) as all_cpm from gd_charge_daily where unit_id in({$idList}) and date < $currentDate group by unit_id";
        $resAll = $cmpDb->query($sql);

        _LOG('cpmallsql ' . $cmpDb->getLastSQL(), L_DEBUG);
        if (false === $resAll) {
            _LOG("db query error! [output:".serialize($resAll)."error:".$cmpDb->error()."sql:".$cmpDb->getLastSQL()."]", L_ERROR);
            return $arrId2RtCpm;
        }

        // 查询今日曝光量
        $day_cpm = array();
        for ($idx = 0; $idx < 6; $idx++) {
            for ($h = 0+$idx*4; $h < 4+$idx*4; $h++) {
                $strDate = $currentDate.str_pad($h, 2, 0, STR_PAD_LEFT);
                for($m = 0; $m < 60; $m++) {
                    $arrDate[] = $strDate.str_pad($m, 2, 0, STR_PAD_LEFT);
                }
            }
            $strDateCond = implode(',', $arrDate);
            $arrDate = array();

            $sqlQuery = "SELECT unit_id, sum(exposure) as day_cpm FROM gd_charge WHERE date in ($strDateCond) and unit_id in({$idList}) GROUP BY unit_id";
            $arrRes = $cmpDb->query($sqlQuery);
            if (false === $arrRes){
                _LOG("db query error! [output:".serialize($arrRes)."error:".$cmpDb->error()."sql:".$cmpDb->getLastSQL()."]", L_ERROR);
                return $arrId2RtCpm;
            }

            if (!is_array($arrRes) || count($arrRes) <= 0) {
                continue;
            }

            foreach ($arrRes as $dataItem) {
                $day_cpm[$dataItem['unit_id']] += $dataItem['day_cpm'];
            }
        }

        // 计算今日应曝光量
        $resBudget = array();
        if (!empty($arrUnitIds) && count($arrUnitIds) > 0) {
            // 计算今日之前的所有曝光量
            $arrPreCpm = array();
            foreach ($resAll as $cpmItem) {
                $arrPreCpm[$cpmItem['unit_id']] = $cpmItem['all_cpm'];
                if ($arrPreCpm[$cpmItem['unit_id']] < 0 ) {
                    $arrPreCpm[$cpmItem['unit_id']] = 0;
                }
            }

            // 计算今日应投放的量
            foreach ($arrUnitIds as $key => $id) {
                if (empty($arrUnitDatas[$id]['cpm_control'])) { // 兼容旧单元数据格式
                    $arrBudget = array(
                        'day_budget' => -1,
                    );
                    $resBudget[$id] = $arrBudget;
                } elseif ($arrUnitDatas[$id]['cpm_supply'] == 1){ // 按天控量且跨天补量
                    $totalBudgetCpm = 0;
                    $arrControlData = json_decode($arrUnitDatas[$id]['cpm_control'], true);
                    foreach ($arrControlData as $tm => $val) {
                        if (date('Ymd', $tm) <= $currentDate) {
                            $totalBudgetCpm += $val;
                        }
                    }
                    // 今天的投放量 = 单元到今天（含）应投的量 - 单元到昨日（含）所有已投放量
                    if (empty($arrPreCpm[$id])) {
                        $day_budget = $totalBudgetCpm * 1000;
                    } else {
                        $day_budget = ($totalBudgetCpm * 1000) - $arrPreCpm[$id];
                    }
                    if ($day_budget < 0) {
                        $day_budget = 0;
                    }
                    $arrBudget = array(
                        'day_budget' => $day_budget,
                    );
                    $resBudget[$id] = $arrBudget;

                } else { // 按天控量,但不跨天补量
                    $arrControlData = json_decode($arrUnitDatas[$id]['cpm_control'], true);
                    foreach ($arrControlData as $tm => $val) {
                        if (date('Ymd', $tm) == $currentDate) {
                            $arrBudget = array(
                                'day_budget' => $val * 1000,
                            );
                            $resBudget[$id] = $arrBudget;
                            break;
                        }
                    }
                }
            }
        }

        // !!!此处需要加上判断记录最近更新时间在10分钟以内的数据才是有效的, 避免实时流有问题时超量投放
        foreach ($resAll as $item) {
            $arrId2RtCpm[$item['unit_id']] = array(
                'day_cpm'       => isset($day_cpm[$item['unit_id']]) ? $day_cpm[$item['unit_id']] : 0,
                'all_cpm'       => $item['all_cpm'] + (isset($day_cpm[$item['unit_id']]) ? $day_cpm[$item['unit_id']] : 0),
                'day_budget'    => isset($resBudget[$item['unit_id']]['day_budget']) ? $resBudget[$item['unit_id']]['day_budget'] : 0,
            );
        }

        // 对于新开始投放的订单，查询总量和天没有数据，会造成无法缓存存在大量检索请求
        foreach ($arrUnitIds as $id) {
            if (!isset($arrId2RtCpm[$id])) {
                $arrId2RtCpm[$id] = array(
                    'day_cpm' => isset($day_cpm[$id]) ? $day_cpm[$id] : 0,
                    'all_cpm' => isset($day_cpm[$id]) ? $day_cpm[$id] : 0,
                    'day_budget' => isset($resBudget[$id]['day_budget']) ? $resBudget[$id]['day_budget'] : 0,
                );
            }
        }

        return $arrId2RtCpm;
    }

    /**
     * @param  array()
     * @return:
     *  false: 暂时不进行投放
     *  true: 继续投放
     */
    public function getShowDataMaxDate() {
        $currentTime = date('Ymd', time());
        $stratTime = intval($currentTime . '0000');
        $endTime = intval($currentTime . '2359');
        if (null === ($cmpDb = Service_Libs_Gd_Db::getDB())) {
            _LOG("get max date db fail.", L_ERROR);
            _LOG("stop load cpm date to redis dayTime:" . $currentTime . "  nowTime:" . time(), L_WARNING);
            return false;
        }

        // 查询总曝光量
        $sql = "select max(date) as date from gd_charge";
        $resAll = $cmpDb->query($sql);

        _LOG('querymaxdatesql ' . $cmpDb->getLastSQL(), L_DEBUG);
        if (false === $resAll || empty($resAll) || !isset($resAll[0]['date'])) {
            _LOG("db query error! [output:".serialize($resAll)."error:".$cmpDb->error()."sql:".$cmpDb->getLastSQL()."]", L_ERROR);
            // 在出错的情况下只有十分之一的时间进行投放, 因为redis过期时间是1分钟
            return false;
        }

        $nowTime = time();
        $maxDate = $resAll[0]['date'];
        if (!empty($maxDate)) {
            $year = substr($maxDate, 0, 4);
            $month = substr($maxDate, 4, 2);
            $day = substr($maxDate, 6, 2);
            $hour = substr($maxDate, 8, 2);
            $minute = substr($maxDate, 10, 2);

            $dayTime = mktime($hour, $minute, 0, $month, $day, $year);

            if (($nowTime - $dayTime) > 300) {
                _LOG("dateTime error, nowTime: $nowTime, dayTime: $dayTime", L_DEBUG);
                return false;
            }
        }

        return  true;
    }

    /**
     * @param $IdeaDataArr array()
     * @param $unitDataArr array()
     * @return array()
     */
    public function buildOnlineData($unitDataArr, $IdeaDataArr) {
        $validUnitIds = array();

        foreach($unitDataArr['data'] as $key => $unitInfo) {
            // 如果存在有效的创意
            if(isset($IdeaDataArr[$key])) {
                // 如果设置了控速或者控量
                if (!empty($unitInfo['cpm_control'])) {
                    $validUnitIds[] = $key;
                } else if ($unitInfo['cpmbyday'] != 0 || $unitInfo['cpmbyall'] != 0) {
                    $validUnitIds[] = $key;
                } else if ($unitInfo['speed_control'] != 1 || $unitInfo['cpmbyall'] == 0) {
                    $validUnitIds[] = $key;
                }
            }
        }

        return $validUnitIds;
    }

    /**
     * @param $ideaArr array()
     * @return array()
     */
    public function filterByValidIdea($ideaArr) {
        $ideaDataArr = array();
        foreach ($ideaArr as $key => $ideaData) {
            $ideaId = $ideaData['id'];
            if ($this->checkIsNowHour($ideaData['valid_time'])) {
                //_LOG("hit-> unitid:".$ideaArr['unit_id'].",idea:{$ideaId}", L_DEBUG);
            } else {
                //_LOG("hit-> unitid:".$ideaArr['unit_id'].",idea:{$ideaId}", L_DEBUG);
                continue;
            }

            $ideaDataArr[$ideaData['unit_id']][] = $ideaData;
        }

        return $this->randIdea($ideaDataArr);
    }

    /**
     * @param  
     *	array(unitid=>array(array()...));
     * @return array()
     */
    public function randIdea($ideaDataArr) {
        if(empty($ideaDataArr))  {
            return array();
        }

        foreach($ideaDataArr as $unitId => $ideaArr) {
            if(count($ideaArr) > self::IDEA_GROUP) {
                $tempArr = array_rand($ideaArr,self::IDEA_GROUP);	
                $newIdeaArr = array();
                foreach($tempArr as $k => $v) {
                    $newIdeaArr[] = $ideaArr[$v];
                    _LOG("rand-> unitid:".$ideaArr[$v]['unit_id'].",idea:{$ideaArr[$v]['id']}", L_DEBUG);
                }
                $ideaDataArr[$unitId] =  $newIdeaArr;

            } else {
                continue;
            }
        }

        return $ideaDataArr;
    }

    /**
     * @param $unitDataArr array() 
     * 			array('data', 'ids');
     * @return array()
     */
    public function getValidIdea($unitIdsArr) {
        if (empty($unitIdsArr)) {
            return array();
        }

        $unitIds = implode("," , $unitIdsArr);
        _LOG($unitIds, L_DEBUG);

        $online_status = Util_Gd_Const::VALID_STATUS;
        $current_time = time();	
        $sql = "select id, unit_id, valid_time from gd_ideas where  unit_id in({$unitIds}) and status = '{$online_status}' and start_time <= $current_time and end_time > $current_time ";
        _LOG($sql, L_DEBUG);
        $rows = $this->db->query($sql);
        if(empty($rows)) {
            _LOG($sql, L_WARNING);
            _LOG("valid idea empty", L_WARNING);
            return array();
        } 

        return $rows;
    }

    /**
     * @param $multTimeHour string
     * @return array()
     */
    public function checkIsNowHour($multTimeHour) {
        if(empty($multTimeHour)) {
            return 0;
        }
        $multTimeHourArr = explode(',',$multTimeHour);
        $week = date("w");
        if($week == 0) {
            $week = 7;
        }
        $hour = strval(date("G"));
        $maskTimeHour = strval($multTimeHourArr[$week-1]);
        $hourPow = pow(2,$hour);
        $week_hour_str = "week:".$week.",hour:".$hour.",masktimeHour:".$maskTimeHour.",hourPow:".$hourPow;
        //_LOG($week_hour_str, L_DEBUG);
        $hitRet = $hourPow & $maskTimeHour;
        if($hitRet === $hourPow) {
            return 1;
        } else {
            return 0;
        }

    }

    /**
     * @param $unitArr array()
     * @return array()
     */
    public function filterByValidUnit($unitList) {
        $unitDataArr = array();
        foreach ($unitList as $key => $unitArr) {
            if ($unitArr['charge_mode'] == Util_Gd_Const::CPT_CHARGEMODE) {
                // 针对CPT订单不需要获取量相关数据
                continue;	
            } elseif ($unitArr['charge_mode'] == Util_Gd_Const::CPM_CHARGEMODE) {
                $unitId = $unitArr['id'];
                if($this->checkIsNowHour($unitArr['valid_time'])) {
                    //_LOG("cpm hit hour:id={$unitId}", L_DEBUG);
                } else {
                    //_LOG("cpm no hit hour:id={$unitId}", L_DEBUG);
                    continue;
                }
            }

            $unitDataArr['data'][$unitArr['id']] = $unitArr;
            $unitDataArr['ids'][] = $unitArr['id'];
        }

        return $unitDataArr;
    }

    /**
     * @param $planIdsArr array()
     * @return array()
     */
    public function getValidUnit($planIdsArr) {
        $planIds = implode("," , $planIdsArr);
        _LOG("valid planIds:".$planIds, L_DEBUG);

        $online_status = Util_Gd_Const::VALID_STATUS;
        $current_time = time();
        $sql = "select id, valid_time, charge_mode, cpmbyday, cpmbyall, speed_control, place_id, cpm_control, cpm_supply 
            from gd_units 
            where  plan_id in({$planIds}) 
            and status = {$online_status}
            and start_time <= $current_time 
            and end_time > $current_time ";
        _LOG($sql, L_DEBUG);
        $rows = $this->db->query($sql);
        if(empty($rows)) {
            _LOG($sql, L_WARNING);
            _LOG("valid unit empty", L_WARNING);
            return array();
        } 

        return $rows;
    }


    /**
     * @param 
     * @return array()
     */
    public function getValidPlanIds() {
        $online_status = Util_Gd_Const::VALID_STATUS;
        $current_time = time();	
        $sql = "select id from gd_plans where status = '{$online_status}' and start_time <= $current_time and end_time > $current_time";
        _LOG($sql, L_DEBUG);
        $rows = $this->db->query($sql);
        if(empty($rows)) {
            _LOG($sql, L_WARNING);
            _LOG("valid planid empty", L_WARNING);
            return array();
        } 
        $planIdsArr = array();
        foreach($rows as $key => $value) {
            $planIdsArr[] = $value['id'];
        }
        return $planIdsArr;
    }
}

/**
 * @param $str string
 * @param $bit int
 * @return array()
 */
function _LOG($str, $bit) {
    switch($bit) {
        case L_DEBUG:
        $preStr = "DEBUG:";
        break;
        case L_WARNING:
        $preStr = "WARNING:";
        break;
        case L_ERROR:
        $preStr = "ERROR:";
        break;
    }
    if($bit&L_EVENT) {
        echo $preStr.":(".$str.")"."\r\n";
    }

    $GLOBALS['mailAllstr'] .= $preStr.":(".$str.")"."\r\n";

    if($bit == L_ERROR) {
        //mail_report('push index error!', $str);
    }
}

/**
 * @param $title string
 * @param $content int
 * @return bool
 */
function mail_report($title , $content) {
    $content = str_replace("\n", "<br/>", $content);
    $mail_subject  =  "【重要】" . $title;
    $mail_subject  = '=?UTF-8?B?' . base64_encode($mail_subject) . '?=';
    $mail_headers  = "MIME-Version: 1.0\r\n";
    $mail_headers .= "Content-type: text/html; charset=utf-8\r\n";
    $mail_headers .= "From: tieba_ad <<EMAIL>>\r\n";
    $mail_headers .= 'CC: ' . CC_TO . "\r\n";
    $ret = mail(MAIL_TO, $mail_subject, $content, $mail_headers);
    return $ret;
}

/**
 * 短息发送
 * @param $msg
 * @return null
 */
function sendMessage($msg) {
    $userlist = array (
        '18514530081',
        '18519303770',
        '15202209119',
        '17801051103',
        '13810316452',
        '18610962551',
        '18210416036',
    );
    
    $sendmeg = new Lib_SendSmsp ();
    $arrUserPhone = $userlist;
    $sendmeg->setTo ( $arrUserPhone );
    $msg = mb_convert_encoding($msg, "GBK", "UTF-8");
    $sendmeg->setMes ( $msg );
    $sendmeg->send ();
}

/**
 * @param $url string
 * @param $ int
 * @param $ int
 * @return bool
 */
function fetchUrlGet($url, $intReadTimeOut=100, $intConnTimeOut=50) {
    $httpproxy = Orp_FetchUrl::getInstance(array('timeout' =>$intReadTimeOut,'conn_timeout' =>$intConnTimeOut, 'max_response_size' => 1024000,'conn_retry' => 3));
    $res = $httpproxy->get($url);
    $err = $httpproxy->errmsg();
    $http_code = $httpproxy->http_code();
    if ($err) {
        _LOG('fetchurlget:'.$err.',url:'.$url, L_ERROR);
        return false;
    } else {
        $header = $httpproxy->header();
        if( $http_code == 200 ){
            return $res;
        }
    }
    return false;
}
