<?php
/***************************************************************************
 *
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file AspInterface.php
 * <AUTHOR>
 * @date: 2016/10/21 11:12
 * @brief:
 *
 */

class Service_Asp_AspInterface {
    const EXT_TMP_EID = 10001062;
    /**
     * @params
     * @return
     * @desc 手百feed流直连asp,http+json
     */
    public static function getAspAdvInfo($arrInput){
        $arrInput['param']['search_id'] = $arrInput['reqId'];
        $arrInput['param']['channel_page'] = $arrInput['channel_page'];
        if (isset($arrInput['param']['service_control']['streamdata'])) {
            unset($arrInput['param']['service_control']['streamdata']);
        }
        $arrInputToJson = json_encode($arrInput['param'],JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $objCaller = new Tieba_Multi_Http();
        $objCaller->addCookie($_COOKIE);

        $arrCpInput = array(
            'key'    => 'fengchaoimage',
            'caller' => $objCaller,
            'input'  => array(
                'strServer' => 'service_fcimage',
                'strUrl'    => "/asp?&chn=" . $arrInput['channel_page'],
                'arrData'   => $arrInputToJson,
                'strMethod' => 'post',
            ),
        );
        $objMulti = new Tieba_Multi('asp_parallel');
        $objMulti->register(
            $arrCpInput['key'],
            $arrCpInput['caller'],
            $arrCpInput['input']
        );
        //并发调用cp
        Bingo_Timer::start('multi_call_for_asp');
        $objMulti->call();
        Bingo_Timer::end('multi_call_for_asp');
        $arrMultiRes = $objMulti->getResult($arrCpInput['key']);
        $arrOut = self::handleRes($arrMultiRes);
        return Util_Util::buildReturn(Tieba_Errcode::ERR_SUCCESS,$arrOut);
    }
    /**
     * @params
     * @return
     * @desc 手百feed流直连asp,nshead + pb格式
     */
    public static function getAspAdvpbInfo($arrInput){
        $arrInput['param']['search_id'] = $arrInput['reqId'];
        $arrInput['param']['channel_page'] = $arrInput['channel_page'];
        if (in_array(self::EXT_TMP_EID, $arrInput['param']['experiment_id_list']) === false) {
            $arrInput['param']['experiment_id_list'][] = self::EXT_TMP_EID;
        }

        //手百明暗投拆分小流量实验
        if (Util_Const::$shoubai_exp_switch) {
            $arrInput = self::_shoubaiExp($arrInput);
        }
 
        Bingo_Log::pushNotice('eid', serialize($arrInput['param']['experiment_id_list']));

        $strPb = Util_Proto::pack($arrInput['param'],'AspRequest', 'asp', 'data');
        $ret = ral ( 'service_fc_shoubai', 'query', $strPb, rand ());
        $arrMultiRes = Util_Proto::unpack($ret,'AspResponse', 'asp', 'data');
        $arrOut = self::handlePbRes($arrMultiRes);
        return Util_Util::buildReturn(Tieba_Errcode::ERR_SUCCESS,$arrOut);
    }
    
    /**
     * @desc   手百明暗投拆分进行小流量实验代码
     * @param  arrInput : array : 请求入参
     * @return  arrInput : array : 修改后的请求入参
     */
    private static function _shoubaiExp($arrInput) {
        //读取实验配置文件
        $strPath = '/app/afd/baichuanSplit';
        Bingo_Timer::start("read_conf_baichuanSplit");
        $arrEidConf = Bd_Conf::getConf($strPath);
        Bingo_Timer::end("read_conf_baichuanSplit"); 

        if (false == $arrEidConf) {
            Bingo_Log::warning('real baichuanSplit.conf fail');
        }
        
        //获取hash后的int值 
        $intHashType = $arrEidConf['app']['hash_type'];   
        switch ($intHashType) {
            case 0:
                $intThreshVal = Service_Rtb_Util_BaseFunc::randomHash();
                break;
            case 2:
                $cuid = $arrInput['param']['wise_req_info']['cuid'];
                $intThreshVal = Service_Rtb_Util_BaseFunc::md5Hash($cuid);
                break;
            default:
                Bingo_Log::warning("invalid hashType: $intHashType");
        }
        
        //根据int值判断hash属于哪个区间范围
        $intMaxValue = 0;
        foreach ($arrEidConf['app']['eid'] as $eidNum => $eidConf) {
            $intMaxValue += $eidConf['flow_percent'];
            if ($intThreshVal <= $intMaxValue) {
                //找到配置区间，进行channel_page修改与实验ID添加
                $arrInput['param']['experiment_id_list'][] = $eidNum;
                if ($eidConf['eid_type'] == 0) {
                    //对照eid，不修改channel_page
                } else if ($eidConf['eid_type'] == 1) {
                    //实验eid，修改channel_page
                    $arrInput['param']['channel_page'] = Service_Rtb_Conf_Const::APP_SHOUBAI_FEED_AFD_CHANNELPAGE_NEW;
                    $arrInput['channel_page'] = Service_Rtb_Conf_Const::APP_SHOUBAI_FEED_AFD_CHANNELPAGE_NEW;
                }
                break;
            }
        } 
        
        return $arrInput;
    }

    /**
     * @desc
     * @param
     * @return
     */
    public static function handlePbRes($arrRes) {
        if (empty($arrRes) || $arrRes['err_no'] != 0) {
            if(isset($arrRes['err_msg'])){
                Bingo_Log::warning("error_from_app_zhangbai_feed_err_msg=".$arrRes['err_msg']);
            }else{
                Bingo_Log::warning("error_from_app_zhangbai_feed");
            }
            return $arrRes;
        }
        $intSearchId = $arrRes['search_id'];
        $arrAdPlaceList = $arrRes['ad_place_list'];
        if (empty($arrAdPlaceList)) {
            Bingo_Log::warning('empty ad from app_zhangbai_feed.' . $intSearchId);
            return $arrRes;
        }
        return $arrRes;
    }
    /**
     * @desc
     * @param
     * @return
     */
    public static function handleRes($arrRes) {
        $arrRes = json_decode($arrRes, true);
        if (empty($arrRes) || $arrRes['err_no'] != 0) {
            if(isset($arrRes['err_msg'])){
                Bingo_Log::warning("error_from_app_zhangbai_feed_err_msg=".$arrRes['err_msg']);
            }else{
                Bingo_Log::warning("error_from_app_zhangbai_feed");
            }
            return $arrRes;
        }
        $intSearchId = $arrRes['search_id'];
        $arrAdPlaceList = $arrRes['ad_place_list'];
        if (empty($arrAdPlaceList)) {
            Bingo_Log::warning('empty ad from app_zhangbai_feed.' . $intSearchId);
            return $arrRes;
        }
        foreach ($arrAdPlaceList as $arrAdPlaceEntry) {
            $arrAdPlaceData = json_decode($arrAdPlaceEntry['ad_place_data'], true);
            if (empty($arrAdPlaceData)) {
                Bingo_Log::warning('empty ad_place data from app_zhangbai_feed.' . $intSearchId);
                continue;
            }
            $arrAdvs = $arrAdPlaceData['ad_item'];
            if (empty($arrAdvs)) {
                Bingo_Log::warning('empty ads from app_zhangbai_feed.' . $intSearchId);
                continue;
            }
        }
        Bingo_Log::pushNotice('fc_res_ad_count', count($arrAdvs));
        return $arrRes;
    }
}
