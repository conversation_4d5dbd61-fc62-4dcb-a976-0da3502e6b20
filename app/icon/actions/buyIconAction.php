<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR> @date 2014-04-23 15:53:56
 * @comment 模版接口
 * @version
 */
class buyIconAction extends Util_Base {
	
	const SCENE_ID = 2000012;
	const SERVICE_ID_BUY_ICON_PC = 20000150;
	const AUTH_KEY_BUY_ICON_PC = '1388ca85560d1315';//8b9949aa341c11d2
	const AUTH_KEY = '752795d43c80736c';

	// ret_status接口状态，用于打点统计业务逻辑失败率
	const RET_STATUS_SUCC = 1;
	const RET_STATUS_FAIL = 0;

	// source_type 用于确认失败原因，暂时使用固定值，为了扩展性保留
	const SOURCE_TYPE = 1;

    public function execute(){
		try{
			//参数获取
			$uid = intval($this->_arrUserInfo['user_id']);
			$icon_name = strval(Bingo_Http_Request::get('icon_name',0));  
			$level = intval(Bingo_Http_Request::get('level',0));
			$arrOutput = array();

			if($uid <= 0)
			{
				throw new Exception("user need login!",Tieba_Errcode::ERR_USER_NOT_LOGIN);

			}
			$isNotConverted = Molib_Util_Yylive::isNotConverted($uid); // 用户是否没有转换
			$switchOpened = Molib_Util_Yylive::isSwitchOpened($uid); // y币切换开关
			// $isNotConverted = false;
			// $switchOpened = true;
			// 判断y币切换开关打开时用户t豆是否已经转换y币，没有转换不能继续操作
			if ($isNotConverted == true &&  $switchOpened == true) {
				//兜底开关打开后，可以让用户消费T豆但不能充值
				if (Molib_Util_Yylive::isPcBackToTdou() == false) {
					throw new Exception("服务器正在T豆升级Y币中，请前往至T豆钱包确认转换Y币，即可使用该功能～", 100000001);
				}
			}

			if($icon_name == '')
			{
				Bingo_Log::warning('buyIcon param error');
				throw new Exception("input params error!",Tieba_Errcode::ERR_PARAM_ERROR);
			}
			if( $uid == 907274510 ){
				//调用接口
				$arrInput = array(
						'user_id' => $uid,
						'name' => $icon_name,
						'level' => $level,
						'scene_id' => self::SERVICE_ID_BUY_ICON_PC,
						'auth_key' => self::AUTH_KEY_BUY_ICON_PC,
				);
			}else{
				//调用购买槽位接口
				$arrInput =array(
						'user_id' => $uid,
						'name' => $icon_name,
						'level' => $level,
						'scene_id' => self::SCENE_ID,
						'auth_key' => self::AUTH_KEY,
				);
			}
    		// 判断用户t豆是否已经转换y币并且y币切换开关打开
			if ($isNotConverted == false && $switchOpened == true) {
				$arrInput['pay_type'] = 50; // 支付类型y币
			}
			
			$arrRet = Tieba_Service::call('icon', 'placeIconOrder', $arrInput,  NULL, NULL, 'post', 'php', 'utf-8');
		
			if( $arrRet === false) {
				Bingo_Log::warning("call service icon placeIconOrder error. [input=".serialize($arrInput).'][output='.serialize($arrRet).']');
				throw new Exception("service call fail!",Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
			}
			if( $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS ){
				$this->_stLog(self::RET_STATUS_FAIL, self::SOURCE_TYPE);
				Bingo_Log::warning("call service icon placeIconOrder error. [input=".serialize($arrInput).'][output='.serialize($arrRet).']');
				return $this->_jsonRet($arrRet['errno'], $arrRet['errmsg'], $arrRet['data']);
			}
			
			$arrRet['data'] = Bingo_Encode::convert($arrRet['data'], Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
			$this->_stLog(self::RET_STATUS_SUCC, self::SOURCE_TYPE);
			$this->_jsonRet($arrRet['errno'], $arrRet['errmsg'], $arrRet['data']);
		} catch(Exception $e){
			$this->_stLog(self::RET_STATUS_FAIL, self::SOURCE_TYPE);
			Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage() );
			//数据接口一般对外提供，错误信息不能对外暴露，默认以'未知错误'代之，可以自行修改
			if ($e->getCode() == 100000001) {
				$this->_jsonRet($e->getCode(), mb_convert_encoding($e->getMessage(), "GBK", "UTF-8"));
			} else {
				$this->_jsonRet($e->getCode(), '未知错误');
			}
		}
    }

	private function _stLog($retStatus, $sourceType){
		// 业务数据打点
		Tieba_Stlog::addNode('ret_status', $retStatus);  // todo 提交结果使用ret_status来表示？
		Tieba_Stlog::addNode('source_type', $sourceType);
	}

}
?>
