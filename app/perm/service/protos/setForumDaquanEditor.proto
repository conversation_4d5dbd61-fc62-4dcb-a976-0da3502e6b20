import "common.proto";

//setForumDaquanEditor input 
message SetForumDaquanEditorReq{
   optional uint32 forum_id = 1;
   optional uint64 user_id = 2;
   optional string user_name = 3;
   optional uint64 op_user_id = 4;
   optional string op_user_name = 5;
   optional string forum_name = 6;
   optional uint32 need_memo = 7;
   optional uint32 resource_from = 8;
   optional string role_name = 9;          
}

//setForumDaquanEditor output
message SetForumDaquanEditorRes{
   required uint32 errno = 1;
   required string errmsg = 2;   
}