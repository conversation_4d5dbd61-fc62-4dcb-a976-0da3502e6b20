<?php

function addChannel(){
    $arrInput = array();
    $arrInput['cname'] = "多盟";
    $arrInput['ctype'] = 1;
    $arrInput['purchase_mode'] = 2;
    $arrInput['ad_req_url'] = 'http://www.baidu.com';
    $arrInput['ad_callback_url'] = 'http://www.baidu.com';
    $arrInput['month_deposit'] = 4;
    $arrInput['deposit_leverage'] =5;
    $arrInput['contact'] = "嘿嘿";
    $arrInput['contact_phone'] = '18910443712';
    $arrInput['contact_email'] = '<EMAIL>';
    $arrInput['uname'] = 'jingchuan';
    $res   = Tieba_Service::call('beco', 'addChannel', $arrInput, null, null, 'post', 'php', 'utf-8');
    var_dump($res);
}

function updateChannel(){
    $arrInput = array();
    $arrInput['cid'] = "1000";
    $arrInput['cname'] = "渠道名";
    $arrInput['ctype'] = 1;
    $arrInput['purchase_mode'] = 2;
    $arrInput['ad_req_url'] = 'http://www.baidu.com';
    $arrInput['ad_callback_url'] = 'http://www.baidu.com';
    $arrInput['month_deposit'] = 4;
    $arrInput['deposit_leverage'] =5;
    $arrInput['contact'] = "嘿嘿";
    $arrInput['contact_phone'] = '18910443712';
    $arrInput['contact_email'] = '<EMAIL>';
    $arrInput['uname'] = 'jincghuan@update';
    $res   = Tieba_Service::call('beco', 'updateChannel', $arrInput, null, null, 'post', 'php', 'utf-8');
    var_dump($res);
}

function listChannel(){
    $arrInput = array();
    $res   = Tieba_Service::call('beco', 'listChannel', $arrInput, null, null, 'post', 'php', 'utf-8');
    var_dump($res);
}

function changeChannelStatus(){
    $arrInput = array();
    $arrInput['cid'] = 10000;
    $arrInput['status'] = 3;
    $arrInput['uname'] = "jingchuan@change";
    $arrInput['used_money'] = 111;
    $res   = Tieba_Service::call('beco', 'changeChannelStatus', $arrInput, null, null, 'post', 'php', 'utf-8');
    var_dump($res);
}

function saveChannelPlaceInfo(){
    $arrInput = array();
    $arrInput['cid'] = 1004;
    $arrInput['platforms'] = array(30,21,22,12,11);
    $arrInput['products'] = array(2,9,17);
    $arrInput['uname'] = "jingchuan@place";
    $arrInput['purchase_mode'] = 2;
    $arrInput['place_datas'] = array(
        array(
            'place_id' => 1453093904902,
            'cpm_price' => 3.3,
            'first_catalogs' => "网友俱乐部",
            'second_catalogs' => "网友俱乐部",
            'status' => 1,
            'qps_control' => 1,
            'qps_percent' => 1.0,
            'platform_id' => 30,
            'product_id' => 2,
        ),
    );
    $res   = Tieba_Service::call('beco', 'saveChannelPlaceInfo', $arrInput, null, null, 'post', 'php', 'utf-8');
    var_dump($res);
}

function getPlaceInfoByParams(){
    $arrInput = array();
    $arrInput['platforms'] = array(30,21,22,12,11);
    $arrInput['products'] = array(2,9,17);
    $arrInput['cid'] = 2;
    $res   = Tieba_Service::call('beco', 'getPlaceInfoByParams', $arrInput, null, null, 'post', 'php', 'utf-8');
    var_dump($res);
}

function getChannelInfo(){
    $arrInput = array();
    $arrInput['cid'] = 1000;
    $res   = Tieba_Service::call('beco', 'getChannelInfo', $arrInput, null, null, 'post', 'php', 'utf-8');
    var_dump($res);
}

function getChannelByPlaceId(){
    $arrInput = array();
    $arrInput['place_id'] = 1453093904902;
    $arrInput['first_catalogs'] = "网友俱乐部";
    $arrInput['second_catalogs'] = "网友俱乐部";
    $res   = Tieba_Service::call('beco', 'getChannelByPlaceId', $arrInput, null, null, 'post', 'php', 'utf-8');
    var_dump($res);
}


//addChannel();
//updateChannel();
//listChannel();
changeChannelStatus();
//saveChannelPlaceInfo();
//getPlaceInfoByParams();
//getChannelInfo();
//getChannelByPlaceId();
?>
