<?php
/**
 * Created by PhpStorm.
 * User: fanyunyang
 * Date: 2016/2/19
 * Time: 18:26
 */


class Service_Module_Gdsys_Plan extends Service_Module_Gdsys_ElementBase{
    const PLAN_SUM_PERGET = 60;

    /**
     * @param $arrParams array 主要描述对应命令需要检查的KEY
     * @param $arrInput array 前端原始输入，待检查部分
     * @return array
     * @desc  统一入参检查
     */
    private function _checkInput($arrParams, $arrInput){
        //$arrCheckParams = $this->arrDbFields;

        $arrLengthParams = array(
            'name' => 50,
            'advertiser' => 100,
            'contract' => 100,
            'account_name' => 100,
            'op_name' => 30,
            'creater' => 30,
        );

        if(isset($arrParams['id']) && empty($arrInput['id'])){
            $errmsg = "id cannot be empty.";
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'id 不能为空!');
        }

        //name 检查
        if(isset($arrParams['name'])){
            if(empty($arrInput['name'])){
                $errmsg = "name cannot be empty.";
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '名称不能为空');
            }
            if(self::_utf8_strlen($arrParams['name']) > $arrLengthParams['name']){
                $errmsg = "name exceed the length limit as ".$arrLengthParams['name'].". value: ".$arrParams['name'];
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '名称长度不能超过'.$arrLengthParams['name']);
            }
        }
        //advertiser 检查
        if(isset($arrParams['advertiser'])){
            if(empty($arrInput['advertiser'])){
                $errmsg = "advertiser cannot be empty.";
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '广告主不能为空');
            }
            if(self::_utf8_strlen($arrParams['advertiser']) > $arrLengthParams['advertiser']){
                $errmsg = "advertiser exceed the length limit as ".$arrLengthParams['advertiser'].". value: ".$arrParams['advertiser'];
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '广告主长度不能超过'.$arrLengthParams['advertiser']);
            }
        }
        //contract 检查
        if($arrParams['plan_type'] != Util_Const::OPERATE_PLANTYPE && $arrParams['account_type'] == Util_Const::ACCOUNTTYPE_KA){
            if(empty($arrInput['contract'])){
                $errmsg = "contract cannot be empty.";
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '合同号不能为空');
            }
            if(self::_utf8_strlen($arrParams['contract']) > $arrLengthParams['contract']){
                $errmsg = "contract exceed the length limit as ".$arrLengthParams['contract'].". value: ".$arrParams['contract'];
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '合同号长度不能超过'.$arrLengthParams['contract']);
            }
        }

        // account_name 检查
        if(isset($arrParams['account_name'])){
            if(empty($arrInput['account_name'])){
                $errmsg = "account_name cannot be empty.";
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '帐户名不能为空');
            }
            if(self::_utf8_strlen($arrParams['account_name']) > $arrLengthParams['account_name']){
                $errmsg = "account_name exceed the length limit as ".$arrLengthParams['account_name'].". value: ".$arrParams['account_name'];
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '账户名长度不能超过'.$arrLengthParams['account_name']);
            }
        }

        //合同金额检查
        if ($arrParams['plan_type'] != 3 && isset($arrParams['plan_amount'])) {
            if (empty($arrInput['plan_amount']) && strlen($arrInput['plan_amount']) == 0) {
                $errmsg = "plan_amount is empty.";
                Service_Libs_Gd_Debug::gdWarning(__METHOD__ . " " . "line:" . __LINE__ . " " . $errmsg);
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '合同金额不能为空');
            }
        }

        //op_name
        if(isset($arrParams['op_name'])){
            if(empty($arrParams['op_name'])){
                $errmsg = "op_name cannot be empty.";
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '操作人不能为空');
            }
            if((self::_utf8_strlen($arrParams['op_name']) > $arrLengthParams['op_name'])){
                $errmsg = "op_name exceed the length limit as ".$arrLengthParams['op_name'].". value: ".$arrParams['op_name'];
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '操作人长度不能超过'.$arrLengthParams['op_name']);
            }
        }

        //creater
        if(isset($arrParams['creater'])){
            if(empty($arrParams['creater'])){
                $errmsg = "creater cannot be empty.";
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '创建人不能为空');
            }
            if((self::_utf8_strlen($arrParams['creater']) > $arrLengthParams['creater'])){
                $errmsg = "creater exceed the length limit as ".$arrLengthParams['creater'].". value: ".$arrParams['creater'];
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '创建人长度不能超过'.$arrLengthParams['creater']);
            }
        }

        //投放日期检查
        if(isset($arrParams['start_time']) && isset($arrParams['end_time'])){
            if($arrParams['start_time'] > $arrParams['end_time']){
                $errmsg = "start_time ".$arrParams['start_time']." is bigger than end_time ".$arrParams['end_time'];
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, "结束时间必须大于开始时间!");
            }
            if(($arrParams['start_time']+8640000) < $arrParams['end_time']){
                $errmsg = "the gap of start_time and end_time exceeds 100 days. st:".$arrParams['start_time']." et:".$arrParams['end_time'];
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, "结束时间不能超过开始时间100天.");
            }
        }

        //idList格式检查
        if(isset($arrParams['idList']) && !preg_match('/^([0-9]+,?)+$/',$arrParams['idList'])){
            $errmsg = "idList format error. value: ".$arrParams['idList'];
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, "idList格式错误.");
        }

        //account_type参数检查
        if (isset($arrParams['account_type']) && !in_array($arrParams['account_type'], array_keys(Util_Const::$arrAccountType))) {
            $errmsg = "account_type param error. value: ".$arrParams['account_type'];
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, "账户类型参数错误.");
        }
        
        return self::_successRet();
    }

    /**
     * @param mixed $arrInput
     * @return array
     * @desc 计划添加
     */
    protected function add($arrInput) {
        //参数获取
        $arrParams['name'] = strval($arrInput['name']);
        $arrParams['advertiser'] = strval($arrInput['advertiser']);
        $arrParams['start_time'] = intval($arrInput['start_time']);
        $arrParams['end_time'] = intval($arrInput['end_time']);
        $arrParams['contract'] = strval($arrInput['contract']);
        $arrParams['plan_amount'] = strval($arrInput['plan_amount']);
        $arrParams['plan_type'] = strval($arrInput['plan_type']);
        $arrParams['op_name'] = strval($arrInput['user_name']);
        $arrParams['creater'] = strval($arrInput['user_name']);
        $arrParams['account_name'] = strval($arrInput['account_name']);
        $arrParams['account_type'] = isset($arrInput['account_type']) ? intval($arrInput['account_type']) : 1;
        
        //参数检查
        $arrRet = $this->_checkInput($arrParams, $arrInput);
        if( !isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "_checkInput fail. errmsg:".$arrRet['errmsg']);
            return $this->_errRet($arrRet['errno'], $arrRet['errmsg']);
        }

        //由于创意审核依赖uc账户id，因此放开运营计划不验证uc账户限制
        $objFinanceApi = null;
        switch (intval($arrParams['account_type'])) {
            case 1: // Ka
                $objFinanceApi = new Service_Module_Financeapi_FinanceApisKa();
                break;
            case 2: // 直销
            case 3: // 大渠道
                $objFinanceApi = new Service_Module_Financeapi_FinanceApisMs();
                break;
            default:
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, "账户类型未知,无法执行相关操作");
                break;
        }
        if ($objFinanceApi === null) {
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN, "创建对象失败: FinanceApis");
        }
        $arrGetNameRet = $objFinanceApi->svcGetUserIdByName($arrParams['account_name']);
        if ( !isset($arrGetNameRet['errno']) || $arrGetNameRet['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "svcGetUserIdByName fail. errmsg:".$arrGetNameRet['errmsg'].", output: ".serialize($arrGetNameRet));
            return self::_errRet( $arrGetNameRet['errno'], "获取账户UID失败:".$arrGetNameRet['errmsg'] );
        }
        $arrParams['account_userid'] = $arrGetNameRet['data'];

        if (empty($arrParams['account_userid'])) {
            $errMsg = "can't get valid account_userid by account_name: " . $arrParams['account_name'];
            Service_Libs_Gd_Debug::gdWarning(__METHOD__ . " " . "line:" . __LINE__ . " " . "_checkInput fail. errmsg: $errMsg");
            return $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR, "无法取得有效账户ID,请检查账户名是否正确");
        }
        
        $time = time();
        $arrParams['ctime'] = $time;
        $arrParams['mtime'] = $time;

        //状态获取和检验
        $arrParams['status'] = Service_Module_Gdsys_Status::getNewStatus($this);
        if($arrParams['status'] === false){
            $errmsg = "status or cmd error. input: ".serialize($this->input);
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, "当前状态不支持该操作.");
        }

        //数据库更新
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".   "get db fail." );
            return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL, "数据库操作失败" );
        }
        Service_Libs_Gd_Db::startTransaction($db, $this->elem);
//        $arrPlanRet = $db->insert($this->tableName, $arrParams);
        $arrPlanRet = Service_Log_Lib::insertWithLog($db,$this->tableName, $arrParams);
        if($arrPlanRet === false){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($arrPlanRet)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL, "数据库操作失败" );
        }
//        $planId = $db->getInsertID();
        $planId = Service_Log_Lib::getInsertID($db);
        if(!$planId) {
            Service_Libs_Gd_Db::rollback($db, $this->elem);
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "getInsertID error!");
            return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL, "数据库操作失败" );
        }

        $arrParams['id'] = $planId;
        if ($arrParams['plan_type'] != 3 && in_array($arrParams['account_type'], array(2, 3))) {
            //接入中小客户,中小客户先冻结资金
            $arrFreezeRet = Service_Finance_Finance::freezePlanFunds($arrParams);
            if ( !isset($arrFreezeRet['errno']) || $arrFreezeRet['errno'] !== Tieba_Errcode::ERR_SUCCESS ) {
                Service_Libs_Gd_Db::rollback($db, $this->elem);
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "freezeFunds fail. errmsg:".$arrFreezeRet['errmsg'].", output: ".serialize($arrFreezeRet));
                return self::_errRet( $arrFreezeRet['errno'], "冻结资金失败:".$arrFreezeRet['errmsg'] );
            }
        }
        Service_Libs_Gd_Db::commit($db, $this->elem);
        return self::_successRet(Tieba_Errcode::ERR_SUCCESS, '', array('id' => $planId));
    }

    /**
     * @param mixed $arrInput
     * @return array
     * @desc 计划编辑
     */
    protected function modify($arrInput){
        //参数获取
        $time = time();
        $arrParams['id'] = intval($arrInput['id']);
        $arrParams['name'] = strval($arrInput['name']);
        if($this->arrElementData[0]['status'] == Util_Const::DRAFT_STATUS){
            $arrParams['advertiser'] = strval($arrInput['advertiser']);
            $arrParams['contract'] = strval($arrInput['contract']);
        }
        if($this->arrElementData[0]['start_time'] > $time){
            $arrParams['start_time'] = intval($arrInput['start_time']);
        }else{
            $arrParams['start_time'] = intval($this->arrElementData[0]['start_time']);
        }
        $arrParams['plan_amount'] = strval($arrInput['plan_amount']);
        $arrParams['plan_type'] = strval($arrInput['plan_type']);
        $arrParams['end_time'] = intval($arrInput['end_time']);
        $arrParams['op_name'] = strval($arrInput['user_name']);

        if(!empty($arrInput['account_type'])){
            $arrParams['account_type'] = intval($arrInput['account_type']);
        }
        
        //参数检查
        $arrRet = $this->_checkInput($arrParams, $arrInput);
        if( !isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "_checkInput fail. errmsg:".$arrRet['errmsg']);
            return $this->_errRet($arrRet['errno'], $arrRet['errmsg']);
        }
        $arrParams['mtime'] = $time;

        //状态获取和检验
        $arrParams['status'] = Service_Module_Gdsys_Status::getNewStatus($this);
        if($arrParams['status'] === false ){
            $errmsg = "status or cmd error. input: ".serialize($this->input);
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR,  "当前状态不支持该操作.");
        }

        //数据库操作
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".   "get db fail." );
            return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL, "数据库操作失败" );
        }
        $strConds = 'id='.$arrParams['id'];
        //由于可能出现单元联动 开启事务
        Service_Libs_Gd_Db::startTransaction($db, $this->elem);
//        $arrPlanRet = $db->update($this->tableName, $arrParams, $strConds);
        $arrPlanRet =  Service_Log_Lib::updateWithLog($db,$this->tableName, $arrParams, $strConds);

        if($arrPlanRet === false){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($arrPlanRet)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            Service_Libs_Gd_Db::rollback($db, $this->elem);
            return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL, "数据库操作失败" );
        }

        //子单元暂停
        $curStatus = $this->arrElementData[0]['status'];
        if($curStatus == Util_Const::VALID_STATUS){
            //unit get 获取计划ID下的单元IDs
            $unitIdList = Service_Module_Gdsys_Unit::getUnitIds($arrParams['id']);
            if($unitIdList === false){
                Service_Libs_Gd_Db::rollback($db, $this->elem);
                $errmsg = "get linkage units error.";
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                return $this->_errRet(Tieba_Errcode::ERR_UNKOWN , "获取联动单元ID失败");
            }
            if(!empty($unitIdList)){
                $cmd        = Util_Const::PAUSE_ACTION;
                $element    = Util_Const::UNIT;
                $data       = array('idList' => $unitIdList, 'user_name' => $arrParams['op_name']);
                $data['extra']['flag'] = self::TRANSFORM_LINKAGE;

                $arrUnitInput = array(
                    'cmd'   =>  $cmd,
                    'elem'  =>  $element,
                    'data'  =>  $data,
                );
                $arrRet = Service_Module_Gdsys_GdCmdHandler::gdCmdHandle($arrUnitInput);
                if( !isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                    Service_Libs_Gd_Db::rollback($db, $this->elem);
                    $errmsg = "units linkage error under plan ".Util_Const::$arrActions[$cmd]." cmd. unitIdList: ".$unitIdList;
                    Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                    return $this->_errRet($arrRet['errno'], "单元联动失败");
                }
            }
        }

        Service_Libs_Gd_Db::commit($db, $this->elem);
        return self::_successRet();
    }

    /**
     * @param int $cmd
     * @param mixed $arrInput
     * @return array
     * @desc 计划状态转换
     */
    protected function transform($cmd, $arrInput){
        //参数获取
        $arrParams['op_name'] = strval($arrInput['user_name']);
        $arrParams['idList'] = strval($arrInput['idList']);

        //参数检查
        $arrRet = $this->_checkInput($arrParams, $arrInput);
        if( !isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "_checkInput fail. errmsg:".$arrRet['errmsg']);
            return $this->_errRet($arrRet['errno'], $arrRet['errmsg']);
        }

        //有效状态和IDLIST拉取
        $arrParams['status'] = Service_Module_Gdsys_Status::getNewStatus($this);
        if($arrParams['status'] === false){
            $errmsg = "status or cmd error. input: ".serialize($this->input);
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, "当前状态不支持该操作.");
        }
        $idList = Service_Module_Gdsys_Status::getValidIdlist($this);
        if(empty($idList)){
            $errmsg = "idList error. input: ".serialize($this->input);
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, "没有有效的ID");
        }

        // 恢复投放时,需要check是否已冻结资金
        if ($cmd == Util_Const::RESUME_ACTION) {
            $arrIds = explode(",", $idList);
            foreach ($arrIds as $planId) {
                if (!Service_Module_Financeapi_DataServer::isFrozeSuccess(Util_Const::PLAN, $planId)) {
                    return $this->_errRet(Tieba_Errcode::ERR_UNKOWN, "计划[{$planId}]未冻结资金,拒绝投放");
                }
            }
        }

        $time = time();
        $arrParams['mtime'] = $time;

        //数据库更新
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".   "get db fail." );
            return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL, "数据库操作失败" );
        }
        //批量计划实现 状态 信任与否 是否需要验证 TODO
        $strConds = 'id in('.$idList.')';
        /*$arrParams['status'] = Service_Module_Gdsys_Status::getNewStatus($this);
        if($arrParams['status'] === false || empty($idList)){
            $errmsg = "status or idList error";
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, $errmsg);
        }*/
        Service_Libs_Gd_Db::startTransaction($db, $this->elem);
        unset($arrParams['idList']);
//        $arrPlanRet = $db->update($this->tableName, $arrParams, $strConds);
        if($arrParams['status'] ==  Util_Const::DEL_STATUS){
            $arrPlanRet =  Service_Log_Lib::updateStatusFromDeleteActionWithLog($db,$this->tableName, $arrParams, $strConds);
        }else{
            $arrPlanRet =  Service_Log_Lib::updateStatusWithLog($db,$this->tableName, $arrParams, $strConds);
        }
        if($arrPlanRet === false){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($arrPlanRet)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            Service_Libs_Gd_Db::rollback($db, $this->elem);
            return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL, "数据库操作失败" );
        }

        //子单元联动 注意事务的静态标记或define
        //unit get 获取计划ID下的单元IDs
        $unitIdList = Service_Module_Gdsys_Unit::getUnitIds($idList);
        if($unitIdList === false){
            Service_Libs_Gd_Db::rollback($db, $this->elem);
            $errmsg = "get linkage units error.";
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
            return $this->_errRet(Tieba_Errcode::ERR_UNKOWN , "获取联动单元ID失败");
        }
        if(!empty($unitIdList)){
            $element    = Util_Const::UNIT;
            $data       = array('idList' => $unitIdList, 'user_name'=>$arrParams['op_name']);
            $data['extra']['flag'] = self::TRANSFORM_LINKAGE;

            $arrUnitInput = array(
                'cmd'   =>  $cmd,
                'elem'  =>  $element,
                'data'  =>  $data,
            );
            $arrRet = Service_Module_Gdsys_GdCmdHandler::gdCmdHandle($arrUnitInput);
            if( !isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                Service_Libs_Gd_Db::rollback($db, $this->elem);
                $errmsg = "units linkage error under plan ".Util_Const::$arrActions[$cmd]." cmd. unitIdList: ".$unitIdList;
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                return $this->_errRet($arrRet['errno'], "单元联动失败");
            }
        }

        Service_Libs_Gd_Db::commit($db, $this->elem);

        /*
                //单计划实现
                foreach($arrPlanIds as $planId){

                    $strConds = 'id='.$planId;
                    $curStatus = Service_Module_Gdsys_Status::getCurStatus(Util_Const::PLAN, $planId);
                    if($curStatus === false){
                        $errmsg = "status error";
                        Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                        return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, $errmsg);
                    }
                    $arrParams['status'] = Service_Module_Gdsys_Status::getNewStatus(Util_Const::PLAN, $cmd ,$curStatus);
                    if($arrParams['status'] === false){
                        $errmsg = "status error";
                        Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
                        return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, $errmsg);
                    }
                    $db->startTransaction();
                    $arrPlanRet = $db->update(self::$tableName, $arrParams, $strConds);
                    if($arrPlanRet === false){
                        Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($arrPlanRet)."error:".$db->error()."sql:".$db->getLastSQL()."]");
                        $db->rollback();
                        return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL, "数据库操作失败" );

                    }

                    //子单元联动
                    //unit get 获取计划ID下的单元IDs
                    //TODO


                    $unitIdList = '';
                    $element    = Util_Const::UNIT;
                    $data       = array('idList' => $unitIdList);

                    $arrUnitInput = array(
                        'cmd'   =>  $cmd,
                        'elem'  =>  $element,
                        'data'  =>  $data,
                    );
                    $arrRet = Service_Module_Gdsys_GdCmdHandler::gdCmdHandle($arrUnitInput);
                    if( !isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                        $db->rollback();
                        return $this->_errRet($arrRet['errno'], $arrRet['errmsg']);
                    }


                    $db->commit();
                }
        */
        return self::_successRet();
    }

    /**
     * @param mixed $arrInput
     * @return array
     * @desc 计划下线
     */
    protected function offline($arrInput){
        $arrRet = self::transform(Util_Const::OFFLINE_ACTION, $arrInput);
        if( !isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "offline fail. errmsg:".$arrRet['errmsg']);
            return $this->_errRet($arrRet['errno'], $arrRet['errmsg']);
        }
        return self::_successRet();
    }

    /**
     * @param mixed $arrInput
     * @return array
     * @desc 计划暂停
     */
    protected function pause($arrInput){
        $arrRet = self::transform(Util_Const::PAUSE_ACTION, $arrInput);
        if( !isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "pause fail. errmsg:".$arrRet['errmsg']);
            return $this->_errRet($arrRet['errno'], $arrRet['errmsg']);
        }
        return self::_successRet();
    }

    /**
     * @param mixed $arrInput
     * @return array
     * @desc 计划恢复
     */
    protected function resume($arrInput){
        $arrRet = self::transform(Util_Const::RESUME_ACTION, $arrInput);
        if( !isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "resume fail. errmsg:".$arrRet['errmsg']);
            return $this->_errRet($arrRet['errno'], $arrRet['errmsg']);
        }
        return self::_successRet();
    }

    /**
     * @param mixed $arrInput
     * @return array
     * @desc 计划删除
     */
    protected function delete($arrInput){
        $arrRet = self::transform(Util_Const::DELETE_ACTION, $arrInput);
        if( !isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "delete fail. errmsg:".$arrRet['errmsg']);
            return $this->_errRet($arrRet['errno'], $arrRet['errmsg']);
        }
        return self::_successRet();
    }

    /**
     * @param mixed $arrInput
     * @return array
     * @desc 计划搜索列表
     */
    protected function search($arrInput){
        //参数获取
        $time = time();
        $user_name = strval($arrInput['user_name']);
        $arrParams['id'] = intval($arrInput['id']);
        $arrParams['name'] = strval($arrInput['name']);
        $arrParams['start_time'] = intval($arrInput['start_time']);
        $arrParams['end_time'] = intval($arrInput['end_time']);
        $arrParams['status'] = intval($arrInput['status']);
        $arrParams['pn'] = intval($arrInput['pn']);
        $arrParams['count'] = intval($arrInput['count']);

        //搜索跨度不能超过100天
        if(($arrParams['end_time'] - $arrParams['start_time']) > 8640000){
            $errmsg = "the gap of start_time and end_time exceeds 100 days. st:".$arrParams['start_time']." et:".$arrParams['end_time'];
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  $errmsg);
            return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL, "搜索跨度不能超过100天." );
        }

        //数据查询
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".   "get db fail." );
            return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL, "数据库操作失败" );
        }

        //查询条件拼接 & 操作人权限限制
        $strWhere = " status >= ".Util_Const::DEL_STATUS;
        /*if(!Util_User::isSuperUser($user_name)){
            $strWhere .= " and creater = '{$user_name}'";
        }*/
        if(!isset($arrInput['role_id'])){
            $user_name = explode(',' ,$arrInput['user_name']);
            $len = count($user_name);
            if ($len > 1) {
                $user_name = implode("','", $user_name);
                $strWhere .= " and creater in ('".$user_name."')";
            } else {
                $strWhere .= " and creater = '{$arrInput['user_name']}'";
            }
        }
        if(!empty($arrInput['id'])){
            $strWhere .= " and id like '%{$arrParams['id']}%'";
        }
        if(!empty($arrInput['name'])){
            $strWhere .= " and name like '%{$arrParams['name']}%'";
        }
        if(isset($arrInput['status'])){
            //已下线包括手动下线和已过期
            if($arrInput['status'] == Util_Const::OFFLINE_STATUS){
                $strWhere .= " and ( '{$time}' > end_time";
                $strWhere .= " or status='{$arrParams['status']}')";
            }
            else if($arrInput['status'] == Util_Const::NOTSTARTED_STATUS){
                $status = Util_Const::VALID_STATUS;
                $strWhere .= " and '{$time}' < start_time";
                $strWhere .= " and status='{$status}'";
            }
            else if($arrInput['status'] == Util_Const::VALID_STATUS){
                $status = Util_Const::VALID_STATUS;
                $strWhere .= " and '{$time}' >= start_time";
                $strWhere .= " and status='{$status}'";
            }
            else{
                $status = $arrParams['status'];
                $strWhere .= " and status='{$status}'";
            }
        }
        if (!empty($arrParams['start_time']) && !empty($arrParams['end_time'])) {
            $startTime = $arrParams['start_time'];
            $endTime = $arrParams['end_time'];
            $strWhere .= " and (({$startTime} <= start_time and {$endTime} >= end_time) or ({$startTime} >= start_time and {$startTime} <= end_time) or ({$endTime} >= start_time and {$endTime} <= end_time))";
        }


        //分页实现
        $pn = intval($arrInput['pn']);
        $cn = intval($arrInput['count']);
        if($pn <= 0) {
            $pn = 1;
        }
        if($cn <= 0) {
            $cn = 30;
        }
        $offset = ($pn-1) * $cn;

        //select域
     /*   $arrFields = array(
            'id',
            'name',
            'start_time',
            'end_time',
            'status',
            'op_name',
        );*/

        //计划总数拉取
        $strSql = 'select count(*) as num from '.$this->tableName .' where '.$strWhere;
        $ret = $db->query($strSql);
        if (false === $ret){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL, "数据库操作失败" );
        }
        $total_num = $ret[0]['num'];

        //限制单次最多取60条，避免单次广告过多而超时
        $step = self::PLAN_SUM_PERGET;
        $arrPlanRet = array();
        for($curOffset = $offset; $curOffset < $offset + $cn; $curOffset += $step){
            $count = ($offset + $cn - $curOffset) > $step ? $step : ($offset + $cn - $curOffset);
            $strAppends = " order by mtime desc limit $curOffset, $count";
            $strConds = $strWhere.$strAppends;
            $arrRet = $db->select($this->tableName,$this->arrDbFields,$strConds);
            if (false === $arrRet){
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($arrPlanRet)."error:".$db->error()."sql:".$db->getLastSQL()."]");
                return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL, "数据库操作失败" );
            }
            $arrPlanRet = array_merge($arrPlanRet, $arrRet);
        }

        //计数服务调用 注意取input中的排期时间 todo
        $arrRet = Service_Module_Dataserver_GdDataServer::getReportData($arrPlanRet, Util_Const::PLAN, $arrParams['start_time'], $arrParams['end_time']);
        if (!isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "report fail. errmsg:".$arrRet['errmsg']);
            return $this->_errRet($arrRet['errno'], '报表数据拉取失败');
        }
        $arrStatsInfo = $arrRet['data'];
        //输出整理
        $arrPlanList = array();
        foreach($arrPlanRet as $arrSinglePlan){
            $arrSinglePlan['status'] = Service_Module_Gdsys_Status::getShowStatus($arrSinglePlan['status'], $arrSinglePlan['start_time'], $arrSinglePlan['end_time']);

            $ctr = 0;
            if(!empty($arrStatsInfo[$arrSinglePlan['id']]['exposure'])){
                $ctr = $arrStatsInfo[$arrSinglePlan['id']]['click'] / $arrStatsInfo[$arrSinglePlan['id']]['exposure'];
                $ctr = number_format(floatval(100 * $ctr), 2, '.', '');
                $ctr .= '%';
            }
            //排期 取数据库里拿出的时间
            $arrSinglePlan['period'] = date('Y-m-d', $arrSinglePlan['start_time'])."至".date('Y-m-d', $arrSinglePlan['end_time']);
            //数据报表 todo
            $arrSinglePlan['exposure'] = intval($arrStatsInfo[$arrSinglePlan['id']]['exposure']);
            $arrSinglePlan['click'] = intval($arrStatsInfo[$arrSinglePlan['id']]['click']);
            $arrSinglePlan['ctr'] = $ctr;
            $arrSinglePlan['cost'] = number_format(floatval($arrStatsInfo[$arrSinglePlan['id']]['cost']), 2, '.', '');
            // 时间进度和投放进度
            $process_time = floor(100 * ((time() - $arrSinglePlan['start_time']) / ($arrSinglePlan['end_time'] - $arrSinglePlan['start_time'])));
            if ($process_time > 100) {
                $process_time = 100;
            } else if ($process_time < 0) {
                $process_time = 0;
            }
            $arrSinglePlan['process_time'] = $process_time;
            $retProcessCpm = Service_Module_Dataserver_GdDataServer::getCpmProcess(Util_Const::PLAN, array($arrSinglePlan));
            if (!isset($retProcessCpm['errno']) || $retProcessCpm['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "getCpmProcess fail. errmsg:".$retProcessCpm['errmsg']);
                $arrSinglePlan['process_cpm'] = -1;
            } else {
                $arrSinglePlan['process_cpm'] = $retProcessCpm['data']['process'];
            }
            $arrPlanList[] = $arrSinglePlan;
        }


        return self::_successRet(Tieba_Errcode::ERR_SUCCESS, '', array( 'list' => $arrPlanList, 'total_num' => $total_num));
    }

    /**
     * @param mixed $arrInput
     * @return array
     * @desc 计划详细信息查询
     */
    public function get($arrInput){
        if(!empty($this->arrElementData[0])){
            return self::_successRet(Tieba_Errcode::ERR_SUCCESS, '', $this->arrElementData[0]);
        }
        $planId= $arrInput['id'];
        if(empty($planId)){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "id cannot be empty.");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, "id 不能为空.");
        }

        //数据查询
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".   "get db fail." );
            return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL, "数据库操作失败" );
        }
        $strConds = 'id='.$planId;
        $arrRet = $db->select($this->tableName,$this->arrDbFields,$strConds);
        if (false === $arrRet){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($arrRet)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL, "数据库操作失败" );
        }
        //格式整理

        return self::_successRet(Tieba_Errcode::ERR_SUCCESS, '', $arrRet[0]);
    }

    /**
     * @param mixed $arrInput
     * @return array
     * @desc 下载计划报表
     */
    protected function report($arrInput){
        //进行列表查询
        $arrRet = self::getPlanList($arrInput['idList']);
        //格式整理
        $arrCsvData = array(
            array(
                '日期',
                '计划id',
                '计划名称',
                '账户类型',
                '展现',
                '点击',
                '点击率',
                '花费',
                '合同消耗',
                '创建者',
            ),
        );
        foreach($arrRet as $singlePlan){
            //处理计划的每天数据
            $arrData = Service_Module_Dataserver_GdDataServer::getUnitReportData(0, $singlePlan['id'], '', '', $singlePlan['start_time'], $singlePlan['end_time'], $singlePlan['plan_amount']);
            if (!isset($arrData['errno']) || $arrData['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "report fail. errmsg:".$arrData['errmsg']);
                return self::_successRet(Tieba_Errcode::ERR_PARAM_ERROR, '参数错误', $arrData);
            }
//            $plan_amount = round($singlePlan['plan_amount']/count($arrData['data']), 2);
            foreach ($arrData['data'] as $item) {
                $arrCsvData[] = array(
                    $item['date'],
                    $singlePlan['id'],
                    $singlePlan['name'],
                    Util_Const::$arrAccountType[$singlePlan['account_type']],
                    $item['exposure'],
                    $item['click'],
                    $item['ctr'],
                    $item['cost'],
                    $item['order_amount'],
                    $singlePlan['creater'],
                );
            }

        }

        Service_Log_Lib::logBatch(
            Service_Log_Conf::MODULE_PLAN,
            Service_Log_Conf::ACTION_DOWNLOAD,
            explode(',',$arrInput['idList']),
            $arrInput['user_name'],
            1,
            ''
        );

        //$arrCsvData = Bingo_Encode::convert($arrCsvData, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
        return self::_successRet(Tieba_Errcode::ERR_SUCCESS, '', $arrCsvData);
    }
    /**
     * @param mixed $arrInput
     * @return array
     * @desc 下载财务报表
     */
    protected function amount($arrInput){
        //进行列表查询
        $arrRet = self::getPlanList($arrInput['idList']);
        //格式整理
        $arrCsvData = array(
            array(
                '日期',
                '计划ID',
                '计划名称',
                '计划类型',
                '账户类型',
                '广告主',
                '合同号',
                '合同消耗',
            ),
        );
        $arrPlanIds = array();
        $arrplanInfo = array();
        foreach($arrRet as $singlePlan){
            $arrPlanIds[] = $singlePlan['id'];
            $arrplanInfo[$singlePlan['id']]['advertiser'] = $singlePlan['advertiser'];
            $arrplanInfo[$singlePlan['id']]['contract'] = $singlePlan['contract'];
            $arrplanInfo[$singlePlan['id']]['plan_type'] = $singlePlan['plan_type'];
            $arrplanInfo[$singlePlan['id']]['account_type'] = $singlePlan['account_type'];
            $arrData = Service_Module_Dataserver_GdDataServer::getUnitReportData(0, $singlePlan['id'], '', '', $singlePlan['start_time'], $singlePlan['end_time'], $singlePlan['plan_amount']);
            if (!isset($arrData['errno']) || $arrData['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "report fail. errmsg:".$arrData['errmsg']);
                return self::_successRet(Tieba_Errcode::ERR_PARAM_ERROR, '参数错误', $arrData);
            }
            foreach ($arrData['data'] as $item) {
                $arrCsvData[] = array(
                    $item['date'],
                    $singlePlan['id'],
                    $singlePlan['name'],
                    Util_Const::$arrPlanTypeName[$singlePlan['plan_type']],
                    Util_Const::$arrAccountType[$singlePlan['account_type']],
                    $singlePlan['advertiser'],
                    $singlePlan['contract'],
                    $item['order_amount'],
                );
            }

        }

        //格式整理
        $arrUnitData = array(
            array(
                '日期',
                '单元ID',
                '单元名称',
                '计划类型',
                '账户类型',
                '广告主',
                '合同号',
                '产品线',
                '端',
                '广告位',
                '展现',
                '点击',
                '花费',
                '订单消耗',
            ),
        );
        $res = self::getUnitList($arrRet);
        foreach ($res as $singleUnit) {
            //处理单元的每天数据
            $arrRet = Service_Module_Dataserver_GdDataServer::getUnitReportData(1, $singleUnit['id'], $singleUnit['price'], $singleUnit['charge_mode'], $singleUnit['start_time'], $singleUnit['end_time'], '');
            if (!isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "report fail. errmsg:".$arrRet['errmsg']);
                return self::_successRet(Tieba_Errcode::ERR_PARAM_ERROR, '参数错误', $arrRet);
            }
//            $unit_amount = round($singleUnit['unit_amount']/count($arrRet['data']), 2);
            $placeNames = Service_Module_Gdsys_Unit::getPlaceNames($singleUnit['place_id']);
            foreach ($arrRet['data'] as $item) {
                $arrUnitData[] = array(
                    $item['date'],
                    $singleUnit['id'],
                    $singleUnit['name'],
                    Util_Const::$arrPlanTypeName[$arrplanInfo[$singleUnit['plan_id']]['plan_type']],
                    Util_Const::$arrAccountType[$arrplanInfo[$singleUnit['plan_id']]['account_type']],
                    $arrplanInfo[$singleUnit['plan_id']]['advertiser'],
                    $arrplanInfo[$singleUnit['plan_id']]['contract'],
                    Util_Const::$arrDownloadProductId[$singleUnit['product_id']],
                    Util_Const::$arrClientType[$singleUnit['client_type']],
                    $placeNames,
                    $item['exposure'],
                    $item['click'],
                    $item['cost'],
                    $item['order_amount'],
                );
            }
        }
        $arrReportData['contract'] = $arrCsvData;
        $arrReportData['amount'] = $arrUnitData;

        Service_Log_Lib::logBatch(
            Service_Log_Conf::MODULE_PLAN,
            Service_Log_Conf::ACTION_DOWNLOAD_AMOUNT,
            explode(',',$arrInput['idList']),
            $arrInput['user_name'],
            1,
            ''
        );

        //$arrCsvData = Bingo_Encode::convert($arrCsvData, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
        return self::_successRet(Tieba_Errcode::ERR_SUCCESS, '', $arrReportData);
    }

    /**
     * @param $arrData
     * @return array
     */
    public static function getUnitList($arrData) {
        $arrPlanIds = array();
        foreach($arrData as $singlePlan){
            $arrPlanIds[] = $singlePlan['id'];
        }
        //获取计划下的所有单元
        $idList = implode(',', $arrPlanIds);
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".   "get db fail." );
            return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL, "数据库操作失败" );
        }
        $strConf = "plan_id in({$idList})";
        $res = $db->select(Util_Const::$_tableName[Util_Const::UNIT], Util_Const::$arrElemDbFields[Util_Const::UNIT], $strConf);
        if (false === $res){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($res)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL, "数据库操作失败" );
        }
        return $res;
    }

    /**
     * @param $idList
     * @return array
     */
    public static function getPlanList($idList) {
        if (empty($idList)) {
            return self::_successRet(Tieba_Errcode::ERR_PARAM_ERROR, '参数错误');
        }
        $arrField = Util_Const::$arrElemDbFields[Util_Const::PLAN];
        $table    = Util_Const::$_tableName[Util_Const::PLAN];
        
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".   "get db fail." );
            return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL, "数据库操作失败" );
        }
        $strConf = "id in ({$idList})";
        $arrPlan = $db->select($table, $arrField, $strConf);
        if (false === $arrPlan){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($arrPlan)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, "数据库操作失败");
        }
        return $arrPlan;
    }
    
    /**
     * 一站式服务页面获取计划详情
     * @param string $accountName 账户名称
     * @param int    $pn          查询页
     * @param int    $count       查询总数
     * @param int    $status      查询状态 1:投放中 2:已下线 3:待上线
     * @return array
     */
    public static function getPlanInfo($accountName, $pn, $count, $status) {
        if (empty($accountName)) {
            return self::_successRet(Tieba_Errcode::ERR_PARAM_ERROR, '参数错误');
        }
        $result = $planIds = array();
        $arrField = array(
            'id',
            'name',
            'start_time',
            'account_name',
            'plan_amount',
        );
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".   "get db fail." );
            return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL, "数据库操作失败" );
        }
        $time = time();
        $start      = ($pn - 1) * $count;
        $strWhere   = "account_name ='{$accountName}'";
        //查询状态 0:全部 1:投放中 2:已下线 3:待上线
        if ($status == 0){
            $strWhere .= " AND status in (2, 3) ";
        } elseif($status == 1){
            $strWhere .= " AND status = 2 AND start_time <= {$time} ";
        } elseif ($status == 2){
            $strWhere .= " AND status = 3 ";
        } elseif ($status == 3){
            $strWhere .= " AND status = 2 AND start_time > {$time} ";
        }  
        $strAppends = "ORDER BY id DESC LIMIT {$start},{$count}";
        $strConds   = $strWhere.$strAppends;
        $arrPlan    = $db->select('gd_plans', $arrField, $strConds);
        if (false === $arrPlan){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($arrPlan)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, "数据库操作失败");
        }
        $planCount    = $db->selectCount('gd_plans', $strWhere);
        if (false === $planCount){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($planCount)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, "数据库操作失败");
        }
        foreach ($arrPlan as $plan){
            array_push($planIds, $plan['id']);
            $result[$plan['id']] = $plan;
        }
        unset($arrPlan);
        if($planIds){
            $strPlanIds = implode(',', $planIds);
            $sql = "SELECT plan_id, SUM(cpmbyall) AS sumcpm, GROUP_CONCAT(DISTINCT charge_mode ORDER BY charge_mode ASC) AS modes FROM gd_units WHERE plan_id IN ({$strPlanIds}) GROUP BY plan_id";
            $arrUnit = $db->query($sql);
            if (false === $arrUnit){
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($arrUnit)."error:".$db->error()."sql:".$db->getLastSQL()."]");
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, "数据库操作失败");
            }
            foreach ($arrUnit as $k => $unit){
                $result[$unit['plan_id']] = array_merge($result[$unit['plan_id']], $unit);
            }
            krsort($result);
        }
        return array( 'list' => array_values($result), 'total_num' => $planCount);
    }
    
    /**
     * 计算单元金额 和 计划金额
     * @param int $planId 计划ID
     * @param int $unitId 单元ID
     * @return array
     */
    public static function getPlanTotalAmount($planId, $unitId = 0){
        $result = array('planAmount' => 0, 'unitTotalAmount' => 0);
        $arrField = array(
            'id',
            'name',
            'start_time',
            'account_name',
            'plan_amount',
        );
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".   "get db fail." );
            return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL, "数据库操作失败" );
        }
        $strConds = "id = {$planId}";
        $arrPlan    = $db->select('gd_plans', $arrField, $strConds);
        if (false === $arrPlan){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($arrPlan)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, "数据库操作失败");
        }
        //获取计划合同金额
        $result['planAmount']      = $arrPlan[0]['plan_amount'];//单位是元
        $result['unitTotalAmount'] = 0;
        
        //获取所有计划下单元数据
        $arruUnit = self::getUnitList(array(array('id' => $planId)));
        //单元ID对应单元金额
        $arrUnitPrice = $arrUnitAmount = array();
        foreach ($arruUnit as $unit){
            if($unit['id'] == $unitId){
                continue;
            }
            if ($unit['charge_mode'] == Util_Const::CPM_CHARGEMODE){
                //CPM
                $unitAmount = 0;
                if(!empty($unit['cpm_control'])){
                    $arrCpmControl = json_decode($unit['cpm_control']);
                    foreach ($arrCpmControl as $cpmday){
                        $unitAmount += $cpmday * ($unit['price']/100);
                    }
                }elseif (!empty($unit['cpmbyall'])){
                    $unitAmount = $unit['cpmbyall'] * ($unit['price']/100);
                }elseif (!empty($unit['cpmbyday'])){
                    $unitAmount = ceil(($unit['end_time'] - $unit['start_time']) / 86400) * $unit['cpmbyday'] * ($unit['price']/100);
                }
                if($unit['status'] == Util_Const::OFFLINE_STATUS){
                    //获取单元ID和金额对应关系
                    $arrUnitPrice[$unit['id']]  = $unit['price'];
                    $arrUnitAmount[$unit['id']] = $unitAmount;
                }else{
                    $result['unitTotalAmount'] += $unitAmount;
                }
            }
        }
        if(!empty($arrUnitPrice)){
            $arrData = Service_Module_Dataserver_GdDataServer::getOfflineUnitCost($arrUnitPrice);
            if(!empty($arrData['data']) && is_array($arrData['data'])){
                foreach ($arrData['data'] as $unitId => $cost){
                    if($arrUnitAmount[$unitId] > $cost){
                        $arrUnitAmount[$unitId] = $cost;
                    }
                    $result['unitTotalAmount'] += $arrUnitAmount[$unitId];
                }
            }
        }
        
        return $result;
    }
}


