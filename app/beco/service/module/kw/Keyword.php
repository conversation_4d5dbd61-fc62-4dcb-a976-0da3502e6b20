<?php
/**
 * User: gechunchao
 * Date: 2017/4/12
 * Time: 15:26
 */

class Service_Module_Kw_Keyword extends Util_BaseService {
    
    const TAG_TABLE = 'gd_kw_tag';
    
    /**
     * 新增开物词包数据
     * @param string $tag_name 账户名称
     * @param string $keywords 关键词
     * @param string $creater  查询页
     * @return array
     */
    public static function addKwTag($tagName, $keywords, $creater, $matchType){
        $time = time();
        $data = array(
            'tag_name'    => $tagName,
            'description' => $tagName,
            'keywords'    => $keywords,
            'match_type'  => $matchType,
            'ctime'       => $time,
            'mtime'       => $time,
            'op_name'     => $creater,
            'creater'     => $creater,
        );
        //获取数据库对象
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".   "get db fail." );
            return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL, "数据库连接失败" );
        }
        $res = Service_Log_Lib::insertWithLog($db,self::TAG_TABLE, $data,$creater,Service_Log_Conf::ACTION_UPLOAD);
        if($res === false){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($data)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL, "数据库操作失败" );
        }
        $id = Service_Log_Lib::getInsertID($db);
        return self::_successRet(Tieba_Errcode::ERR_SUCCESS, '', array('id' => $id));
    }
    
    /**
     * 获取开物上传词包数据
     * @param string $creater 创建者
     * @param int    $pn      查询页
     * @param int    $count   查询总数
     * @return array
     */
    public static function getKwTags($creater, $pn, $count){
        $fields = array(
            'id',
            'tag_name',
            'tag_id',
            'tag_status',
            'ctime',
        );
        $conds = null;
        if(!empty($creater)){
            $conds = array(
                'creater = ' => $creater,
            );
        }
        $start = ($pn - 1) * $count;
        $appends = array(
            "ORDER BY id DESC LIMIT {$start},{$count}",
        );
        //获取数据库对象
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".   "get db fail." );
            return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL, "数据库连接失败" );
        }
        $count = $db->selectCount(self::TAG_TABLE, $conds);
        $res   = $db->select(self::TAG_TABLE, $fields, $conds, null, $appends);
        if($res === false){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($res)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL, "数据库操作失败" );
        }
        return self::_successRet(Tieba_Errcode::ERR_SUCCESS, '', array( 'list' => $res, 'total_num' => $count));
    }
    
    /**
     * 获取关键词的词包ID
     * @param string $creater  创建者
     * @param string $keywords 关键词
     * @return array
     */
    public static function getKeywordsPackageIds($creater, $keywords){
        $redisKey = $result = array();
        //获取redis对象
        if (null === ($redis = Service_Libs_Gd_Redis::getRedis())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." "."getRedis redis error.");
        }
        if (!empty($keywords)){
            $arrKeywords = explode(',', $keywords);
            foreach ($arrKeywords as $keyword){
                $preKey = Util_Const::KWS_PREFIX;
                $redisKey[] = array('key'=> $preKey.$keyword);
            }
            $redisInput['reqs'] = $redisKey;
            $arrRet = $redis->GET($redisInput);
            if($arrRet===false || $arrRet['err_no'] !== 0){
                Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." "."call redis error.[".serialize($redisInput)."]");
            }else{
                if(!empty($arrRet['ret'])){
                    foreach ($arrRet['ret'] as $packageIds){
                        $arrPackageIds = explode(',', $packageIds);
                        $result = array_merge($result, $arrPackageIds);
                    }
                }
                //删除重复数据
                $result = array_unique($result);
                //最多返回500个词包ID
                if(count($result) > 500){
                    $result = array_slice($result, 0, 500);
                }
                $result = implode(',',array_unique($result));
            }
        }
        return self::_successRet(Tieba_Errcode::ERR_SUCCESS, '', $result);
    }
}