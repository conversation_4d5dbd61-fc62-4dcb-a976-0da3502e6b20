<?php

/**
 * @name Service_Pega_Page
 * @desc 产品线
 * @date 2017-05-03 10:56:30
 * <AUTHOR>
 */
class Service_Product_Page {

    /**
     * 添加产品线
     * 
     * @param array $arrInput 请求参数
     * @return array
     */
    public static function addPage($arrInput) {
        // 获取请求参数
        $pageName = strval(trim($arrInput['pageName']));
        $opUser = strval(trim($arrInput['opUser']));

        //验证参数
        if (empty($pageName) || empty($opUser) ){
            Bingo_Log::warning('invalid input[' . serialize($arrInput) . ']');
            return Util_Utility::arrRet(Tieba_Errcode::ERR_PARAM_ERROR,'param error');
        }

        //检查名字是否重复
        $ret = Dl_Product_Page::checkPageName($pageName);
        if (false === $ret) {
            Bingo_Log::warning('call Dl_Page_Page::checkPageName fail with input[' . serialize($pageName)  . '] and ouput[' . serialize($ret) . ']');
            return Util_Utility::arrRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'dl call fail');
        }
        if(!empty($ret)) {
            return Util_Utility::arrRet(Tieba_Errcode::ERR_PARAM_ERROR,'页面名已存在');
        }

        $opTime = date("Y-m-d H:i:s");
        $arrParams = array(
            'pageName' => $pageName,
            'opUser' => $opUser,
            'opTime' => $opTime,
        );

        $arrRet = Dl_Product_Page::addPage($arrParams);
        if (false === $arrRet) {
            Bingo_Log::warning('call Dl_Page_Page::addPage fail with input[' . serialize($arrParams)  . '] and ouput[' . serialize($arrRet) . ']');
            return Util_Utility::arrRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'dl call fail');
        }

        //记录操作日志
        $arrParams['insertId'] = $arrRet;
        Bingo_Log::pushNotice('add_page', serialize($arrParams));

        return Util_Utility::arrRet(Tieba_Errcode::ERR_SUCCESS,'success');
    }

    /**
     * 修改页面
     * 
     * @param array $arrInput 请求参数
     * @return array
     */
    public static function editPage($arrInput) {
        // 获取请求参数
        $pageId = !empty($arrInput['pageId']) || '0' === $arrInput['pageId'] ? intval(trim($arrInput['pageId'])) : '';
        $opUser = isset($arrInput['opUser']) ? strval(trim($arrInput['opUser'])) : '';
        $pageName = isset($arrInput['pageName']) ? strval(trim($arrInput['pageName'])) : '';


        //验证参数
        if( '' === $pageId || empty($opUser)){
            Bingo_Log::warning('invalid input[' . serialize($arrInput) . ']');
            return Util_Utility::arrRet(Tieba_Errcode::ERR_PARAM_ERROR,'参数不正确');            
        }

        if(empty($pageName)){
            Bingo_Log::warning('invalid input[' . serialize($arrInput) . ']');
            return Util_Utility::arrRet(Tieba_Errcode::ERR_PARAM_ERROR,'参数不能为空');             
        }

        //检查名字是否重复
        $ret = Dl_Product_Page::checkPageName($pageName);
        if (false === $ret) {
            Bingo_Log::warning('call Dl_Page_Page::checkPageName fail with input[' . serialize($pageName)  . '] and ouput[' . serialize($ret) . ']');
            return Util_Utility::arrRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'dl call fail');
        }
        if(!empty($ret) && intval($ret[0]['pageId']) !== $pageId) {
            return Util_Utility::arrRet(Tieba_Errcode::ERR_PARAM_ERROR,'页面名已存在');
        }

        $opTime = date("Y-m-d H:i:s");
        $arrParams = array(
            'pageId' => $pageId,
            'pageName' => $pageName,
            'opUser' => $opUser,
            'opTime' => $opTime,
        );

        $arrRet = Dl_Product_Page::editPage($arrParams);
        if (false === $arrRet) {
            Bingo_Log::warning('call Dl_Page_Page::editPage fail with input[' . serialize($arrParams)  . '] and ouput[' . serialize($arrRet) . ']');
            return Util_Utility::arrRet(Tieba_Errcode::ERR_DL_CALL_FAIL, '参数不正确');
        }

        //记录操作日志
        Bingo_Log::pushNotice('edit_page', serialize($arrParams));

        return Util_Utility::arrRet(Tieba_Errcode::ERR_SUCCESS,'success');       

    }

    /**
     * 获取页面列表
     * 
     * @param array $arrInput 请求参数
     * @return array
     */
    public static function getPageList($arrInput) {
        // 获取参数
        $pn = isset($arrInput['pn']) ? intval(trim($arrInput['pn'])) : 0;
        $rn = isset($arrInput['rn']) ? intval(trim($arrInput['rn'])) : 0;

        // 校验参数
        if($pn < 0 || $rn < 0) {
            Bingo_Log::warning('invalid input[' . serialize($arrInput) . ']');
            return Util_Utility::arrRet(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
        }

        $arrParams = array(
            'pn' => $pn,
            'rn' => $rn,
        );

        $arrRet = Dl_Product_Page::getPageList($arrParams);
        if (false === $arrRet) {
            Bingo_Log::warning('call Dl_Page_Page::getPageList fail with input[' . serialize($arrParams)  . '] and ouput[' . serialize($arrRet) . ']');
            return Util_Utility::arrRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'dl call fail');
        }
        return Util_Utility::arrRet(Tieba_Errcode::ERR_SUCCESS,'success',$arrRet);                
     }

    /**
     * 获取单个页面
     * 
     * @param array $arrInput 请求参数
     * @return array
     */
    public static function getPageById($arrInput) {

        //获取参数
        $pageId = !empty($arrInput['pageId']) || '0' === $arrInput['pageId'] ? intval(trim($arrInput['pageId'])) : '';
        //参数校验
        if( '' === $pageId ){
            Bingo_Log::warning('invalid input[' . serialize($arrInput) . ']');
            return Util_Utility::arrRet(Tieba_Errcode::ERR_PARAM_ERROR,'param error');           
        }

        $arrParams = array(
            'pageId' => $pageId,
        );

        $arrRet = Dl_Product_Page::getPageById($arrParams);
        if (false === $arrRet) {
            Bingo_Log::warning('call Dl_Page_Page::getPageById fail with input[' . serialize($arrParams)  . '] and ouput[' . serialize($arrRet) . ']');
            return Util_Utility::arrRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'dl call fail');
        }
        return Util_Utility::arrRet(Tieba_Errcode::ERR_SUCCESS,'success',$arrRet);        
    }

    /**
     * 删除页面
     * 
     * @param array $arrInput 请求参数
     * @return array
     */
    public static function deletePage($arrInput){

        //获取参数
        $pageId = !empty($arrInput['pageId']) || '0' === $arrInput['pageId'] ? intval(trim($arrInput['pageId'])) : '';
        $opUser = isset($arrInput['opUser']) ? strval(trim($arrInput['opUser'])) : '';

        // 校验参数是否为空
        if ( '' === $pageId || empty($opUser)) {
            Bingo_Log::warning('invalid input[' . serialize($arrInput) .']');
            return Util_Utility::arrRet(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
        }
        $arrRet = Dl_Place_Place::getPlaceCountByPageId($pageId);
        if ($arrRet === false) {
            Bingo_Log::warning('call Dl_Place_Place::getPlaceCount fail with input[' . serialize($arrParams)  . '] and ouput[' . serialize($arrRet) . ']');
            return Util_Utility::arrRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'dl call fail');
        }
        if ($arrRet['placeCount'] > 0){
            Bingo_Log::warning('invalid input[' . serialize($arrInput) .' can`t be delete]');
            return Util_Utility::arrRet(Tieba_Errcode::ERR_PARAM_ERROR, '该页面下有广告位不能被删除');
        }
        $arrParams = array(
            'pageId' => $pageId,
            'opUser' => $opUser,
            'opTime' => date("Y-m-d H:i:s"),
        );
        $arrRet = Dl_Product_Page::deletePage($arrParams);
        if (false === $arrRet) {
            Bingo_Log::warning('call Dl_Page_Page::deletePage fail with input[' . serialize($arrParams)  . '] and ouput[' . serialize($arrRet) . ']');
            return Util_Utility::arrRet(Tieba_Errcode::ERR_DL_CALL_FAIL, '页面不存在');
        }

        //记录操作日志
        Bingo_Log::pushNotice('delete_Page', serialize($arrParams));

        return Util_Utility::arrRet(Tieba_Errcode::ERR_SUCCESS,'success');        
    } 

}