<?php
/**
 * Created by PhpStorm.
 * User: fanyunyang
 * Date: 2016/2/19
 * Time: 18:26
 */

class Service_WhiteList_WhiteList extends Util_BaseService{

    /**
     * 获取白名单列表
     * @param $arrInput
     * @return array
     */
    public function getWhiteList($arrInput) {

        $userId = $arrInput['user_id'];
        $planId = $arrInput['plan_id'];
        $page = $arrInput['page'];
        $pageSize = $arrInput['page_size'];

        if((null !== $userId && !is_numeric($userId))
            || ( null !== $planId && !is_numeric($planId))
            || ( null !== $page && !is_numeric($page))
            || ( null !== $pageSize && !is_numeric($pageSize))
        ){
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '参数错误');
        }
        $arrRet = Service_Module_Gdsys_WhiteList::getWhiteList($userId,$planId,$page,$pageSize);
        if (!isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "report fail. errmsg:".$arrRet['errmsg']);
            return self::_errRet($arrRet['errno'],$arrRet['errmsg'] );
        }
        return self::_successRet(Tieba_Errcode::ERR_SUCCESS, '', $arrRet['data']);
    }

    /**
     * 添加白名单
     * @param $arrInput
     * @return array
     */
    public function addWhiteList($arrInput) {

        $userId = $arrInput['user_id'];
        $planId = $arrInput['plan_id'];
        $creator = $arrInput['user_name'];
        if(!is_numeric($userId) || !is_numeric($planId) || empty($creator)){
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '参数错误');
        }
        $arrRet = Service_Module_Gdsys_WhiteList::addWhiteList($userId,$planId);

        //操作日志
        $id = isset($arrRet['data']) ? $arrRet['data'] : 0;
        Service_Log_Lib::addWhiteList($id,$userId,$planId,$creator,$arrRet['errno'] === Tieba_Errcode::ERR_SUCCESS);

        if ($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "report fail. errmsg:".$arrRet['errmsg']);
            return self::_errRet($arrRet['errno'],$arrRet['errmsg'] );
        }
        return self::_successRet(Tieba_Errcode::ERR_SUCCESS, '', $arrRet['data']);
    }

    /**
     * 激活白名单
     * @param $arrInput
     * @return array
     */
    public function enableWhiteList($arrInput) {

        $id = $arrInput['id'];
        $creator = $arrInput['user_name'];
        if(!is_numeric($id) || empty($creator)){
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '参数错误');
        }
        $arrRet = Service_Module_Gdsys_WhiteList::updateWhiteList($id,Service_Module_Gdsys_WhiteList::ACTIVE_STATUS);

        Service_Log_Lib::enableWhiteList($id,$creator,$arrRet['errno'] === Tieba_Errcode::ERR_SUCCESS);

        if (!isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "report fail. errmsg:".$arrRet['errmsg']);
            return self::_errRet($arrRet['errno'],$arrRet['errmsg'] );
        }
        return self::_successRet(Tieba_Errcode::ERR_SUCCESS, '');
    }

    /**
     * 删除白名单
     * @param $arrInput
     * @return array
     */
    public function deleteWhiteList($arrInput) {

        $id = $arrInput['id'];
        $creator = $arrInput['user_name'];

        if(!is_numeric($id) || empty($creator)){
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '参数错误');
        }

        $arrRet = Service_Module_Gdsys_WhiteList::getWhiteListById($id);
        if ($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "report fail. errmsg:".$arrRet['errmsg']);
            return self::_errRet($arrRet['errno'],$arrRet['errmsg'] );
        }
        $detail = $arrRet['data'];

        $arrRet = Service_Module_Gdsys_WhiteList::deleteWhiteList($id);

        //操作日志
        Service_Log_Lib::deleteWhiteList($id,$creator,$detail,$arrRet['errno'] === Tieba_Errcode::ERR_SUCCESS);

        if ($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "report fail. errmsg:".$arrRet['errmsg']);
            return self::_errRet($arrRet['errno'],$arrRet['errmsg'] );
        }
        return self::_successRet(Tieba_Errcode::ERR_SUCCESS, '');
    }
}
