<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file    gdManualDefreeze.php
 * <AUTHOR>
 * @date    2017/10/10 11:38
 * @version
 * @brief
 **/

/**
 * log
 */
define('L_DEBUG' , 1);
define('L_ERROR' , 2);
define('L_WARNING' , 4);
define('L_EVENT' , 7);

define('MODULE_NAME', 'beco');
define('SCRIPTNAME', basename(__FILE__,".php"));   //定义脚本名

/**
 * @param $title string
 * @param $content int
 * @return bool
 */
function mail_report($title , $content) {
    $mailSubjectStr = "#GD手动解冻通知#";

    $isOnline = true;
    if ( $isOnline == true) {
        $toUserStr  = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>';
        $ccUserStr  = '<EMAIL>';
    } else {
        $toUserStr  = '<EMAIL>';
        $ccUserStr  = '<EMAIL>';
        $mailSubjectStr = '【线下测试】'.$mailSubjectStr;
    }

    $content = str_replace("\n", "<br/>", $content);
    $mail_subject  = $mailSubjectStr.$title;
    $mail_subject  = '=?UTF-8?B?' . base64_encode($mail_subject) . '?=';
    $mail_headers  = "MIME-Version: 1.0\r\n";
    $mail_headers .= "Content-Transfer-Encoding: base64\r\nContent-type: text/html; Charset=\"utf-8\"\r\n";
    $mail_headers .= "From:auto_send<<EMAIL>>\r\n";
    $mail_headers .= 'CC: ' . $ccUserStr . "\r\n";
    $ret = mail($toUserStr, $mail_subject, rtrim(chunk_split(base64_encode($content))), $mail_headers);
    return $ret;
}

/**
 * @param $str string
 * @param $bit int
 * @return array()
 */
function _LOG($str, $bit) {
    switch($bit) {
        case L_DEBUG:
            $preStr = "DEBUG:";
            break;
        case L_WARNING:
            $preStr = "WARNING:";
            break;
        case L_ERROR:
            $preStr = "ERROR:";
            break;
    }
    if($bit&L_EVENT) {
        echo $preStr.":(".$str.")"."\r\n";
    }

    $GLOBALS['mailAllstr'] .= $preStr.":(".$str.")"."\r\n";

    if($bit == L_ERROR) {
        mail_report('run error!', $str);
    }
}

/**
 * @param $url
 * @param $post
 * @param $timeout
 * @return mixed
 */
function http_post($url, $post, $timeout) {
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_POST, 1);
    curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($post));
    curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    // curl_setopt($curl, CURLOPT_HTTPHEADER, array('pub_env: 0'));
    curl_setopt($curl, CURLOPT_HEADER, true);
    //curl_setopt($curl, CURLOPT_COOKIE, "pub_env=1");
    $output = curl_exec($curl);
    $code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    print "\n- http error code: $code\n";
    // 获得响应结果里的：头大小
    $headerSize = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
    // 根据头大小去获取头信息内容
    $header = substr($output, 0, $headerSize);
    $content = substr($output, $headerSize);
    echo "- output:\n";
    print serialize($output);
    curl_close($curl);
    return json_decode($content, true);
}

/**
 * @desc 获取导出邮件模板
 * @param null
 * @return tpl
 **/
function get_export_email_tpl() {
    $project_attr = array (
        '计划ID',
        '账户名',
        '账户类型',
        '解冻金额',
        '是否成功',
        '备注',
    );
    return '<table width="1000" border="1"> <tr bgcolor="#006699" style="color: #fff;"><td>' . implode('</td><td>', $project_attr) . '</td></tr>';
}


// -------------------------------
echo "\n----- start -----\n";
$idList = $argv[1];
_LOG("idList: $idList", L_DEBUG);

$arrIds = explode(',', $idList);
$arrReport = array();
foreach ($arrIds as $planId) {
    $arrParams = array();
    $arrParams['data']['plan_id'] = $planId;

    // online
    $arrRet = http_post('http://bjyz-bac-orp-feed-ads-65007.bjyz.baidu.com:8210/service/beco?method=gdManualDefreeze&format=json&ie=utf-8', $arrParams, 600);
    // offline
    // $arrRet = http_post('http://lhf.service.tieba.otp.baidu.com/service/beco?method=gdManualDefreeze&format=json&ie=utf-8', $arrParams, 600);

    echo "\n -----";
    echo "\n >>> errno: {$arrRet['errno']}";
    echo "\n >>> errmsg: {$arrRet['errmsg']}";
    echo "\n<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n";

    $report['planId'] = $planId;
    $report['errno'] = $arrRet['errno'];
    $report['errmsg'] = $arrRet['errmsg'];
    if (is_array($arrRet['data'])) {
        $report = array_merge($report, $arrRet['data']);
    }
    $arrReport[] = $report;
}

// 邮件通报
$content = get_export_email_tpl();
foreach ($arrReport as $k => $arrInfo) {
    if ($arrInfo['errno'] == 0) {
        $arrInfo['is_success'] = '成功';
    } else {
        $arrInfo['is_success'] = '失败';
    }

    $arrFileds = array(
        'planId',
        'account_name',
        'account_type',
        'defreeze_amount',
        'is_success',
        'errmsg',
    );
    $content .= '<tr height="60">';
    foreach ($arrFileds as $filed) {
        if (!isset($arrInfo[$filed])) {
            $arrInfo[$filed] = '-';
        }
        $content .= '<td>' . strval($arrInfo[$filed]) . '</td>';
    }
    $content .= '</tr>';
}
$content .= '</table>';
$title = " 日期: ".date('Y-m-d');
mail_report($title, $content);

echo "\n-----  end  -----\n";