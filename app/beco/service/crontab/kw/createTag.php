<?php
/**
 * User: gechunchao
 * Date: 2017/2/24
 * Time: 上午10:07
 */
ini_set('memory_limit','128M');
define('IS_ORP_RUNTIME',true);
define('ROOT_PATH', dirname(__FILE__). '/../../../');
define('L_DEBUG' , 1);
define('L_ERROR' , 2);
define('L_WARNING' , 4);
define('L_EVENT' , 7);
define('MODULE_NAME', 'beco');
define('SCRIPT_NAME', basename(__FILE__,".php"));   //定义脚本名

set_include_path(get_include_path() . PATH_SEPARATOR. ROOT_PATH);

/**
 * @param $strClassName    string
 * @return
 */
function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');

Bingo_Log::init(
    array(
        'log' => array(
            'file'  => dirname(__FILE__).'/../../../../../log/'.MODULE_NAME.'/scripts/'.SCRIPT_NAME.'.log',
            'level' => 0xFF,
        ),
    ),
    'log'
);


class createTag extends Util_BaseService {
    private $db        = null;
    private $redis     = null;
    private $startTime = null;
    const CREATE_TAG          = 'http://************:8080/api/create_tag';
    const CREATE_TAG_LOCK_KEY = "gd_create_tag_lock";//todo
    
    /**
     * @desc 主运行方法
     */
    public function run() {
        try {
            $this->_before();
            $this->_execute();
            $this->_after();
        } catch (Exception $e) {
            
        }
    }

    /**
     * @desc 实例化
     * @param
     * @return
     */
    public static function newInstance() {
        return new self();
    }

    /**
     * @desc 初始化Dao类
     */
    private function _initObjs() {
        try {
            $this->db = Service_Libs_Gd_Db::getDB();
            if($this->db == null ) {
                _LOG('get db handle error!', L_ERROR);
                exit;
            }
            $this->redis = Service_Libs_Gd_Redis::getRedis();
            if($this->redis == null ) {
                _LOG('get redis handle error!', L_ERROR);
                exit;
            }
        } catch (Exception $e) {
            _LOG('initObjs error!', L_ERROR);
        }
    }

    /**
     * @desc 开始
     */
    private function _before() {
        set_time_limit(0);
        $this->startTime = intval(microtime(true) * 1000);
        _LOG('startTime is ' . $this->startTime, L_DEBUG);
        $this->_initObjs();
    }
     
    /**
     * @desc 业务处理主方法
     */
    private function _execute() {
        $resGet = $this->redis->GET(array('key'=>self::CREATE_TAG_LOCK_KEY));
        if ($resGet['ret'][self::CREATE_TAG_LOCK_KEY]){
            //如果超过一小时自动删除
            if (time() - $resGet['ret'][self::CREATE_TAG_LOCK_KEY] > 60*60){
                $this->redis->DEL(array('key'=>self::CREATE_TAG_LOCK_KEY));
                _LOG('other process executed an hour,delete the process!', L_DEBUG);
            }else{
                _LOG('other process is running!', L_DEBUG);
                return;
            }
        }else{
            $resSet = $this->redis->SETNX(array('key'=>self::CREATE_TAG_LOCK_KEY, 'value'=> time()));
            if(!$resSet){
                _LOG('redis setnx error!', L_DEBUG);
                return;
            }
        }
        $tagInfos = $this->_getGdKwTags();
        if($tagInfos){
            foreach ($tagInfos as $tagInfo){
                $this->_updateKwTagStatus($tagInfo['id'], 2);
                $data = array(
                    'application'         => 'open_tag',
                    'description'         => $tagInfo['tag_name'],
                    'frequency_threshold' => 0,
                    'input_words'         => strtr($tagInfo['keywords'], array("," => "\n")),
                    'match_type'          => $tagInfo['match_type'],
                    'rule_source'         => -1,
                    'rule_type'           => 'QUERY',
                    'tag_name'            => $tagInfo['tag_name'],
                    'valid_date'          => 30,
                    'user_name'           => 'gd'
                );
                $createTagRes =  $this->_curl(self::CREATE_TAG, $data, 'POST');
                $arrTagRes = json_decode($createTagRes, true);
                $updateDate = array(
                    'tag_id'     => $arrTagRes['tag_id'],
                    'tag_status' => 3,
                    'mtime'      => time(),
                    'res'        => $createTagRes,
                );
                $this->_updateKwTagInfo($tagInfo['id'], $updateDate);
                _LOG('createTagRes is ' . $createTagRes, L_DEBUG);
            }
        }
        $resDel = $this->redis->DEL(array('key'=>self::CREATE_TAG_LOCK_KEY));
        if(!$resDel){
            _LOG('redis del error!', L_DEBUG);
            return;
        }
    }

    /**
     * @desc  获取开物关键词包数据
     * @param
     * @return array()
     */
    private function _getGdKwTags()
    {
        $sql = "SELECT id,tag_name,keywords,tag_id,tag_status,match_type,ctime,op_name FROM `gd_kw_tag` WHERE tag_status IN (1, 2) OR tag_id = '-1'";
        _LOG($sql, L_DEBUG);
        $rows = $this->db->query($sql);
        return $rows;
    }
    
    /**
     * @desc 更新标签状态
     * @param
     * @return array()
     */
    private function _updateKwTagStatus($id, $status)
    {
//        $sql = "update gd_kw_tag set tag_status = '{$status}' where id = '{$id}'";
//        _LOG($sql, L_DEBUG);
//        $rows = $this->db->query($sql);

        $strConds = "id=".$id;
        $rows = Service_Log_Lib::updateStatusWithLog($this->db,'gd_kw_tag', array('tag_status' => $status), $strConds,'beco_robot');
        return $rows;
    }
    
    /**
     * @desc 更新标签信息
     * @param $id
     * @param $data array 
     * @return array()
     */
    private function _updateKwTagInfo($id, $data)
    {
//        $conds = array (
//            'id=' => $id
//        );
//        $rows = $this->db->update( 'gd_kw_tag', $data, $conds );
//        _LOG($this->db->getLastSQL(), L_DEBUG);

        $conds = 'id='.$id;
        $rows = Service_Log_Lib::updateStatusWithLog($this->db,'gd_kw_tag',$data,$conds,'beco_robot');
        _LOG(Service_Log_Lib::getLastSQL($this->db), L_DEBUG);
        return $rows;
    }
    
    /**
     * @desc 结束
     */
    private function _after() {
        $endTime = intval(microtime(true) * 1000);
        _LOG('endTime is ' . $endTime, L_DEBUG);
    }

    /**
     * @param $url    string
     * @param $data   array
     * @param $method string
     * @return
     */
    private function _curl($url, $data, $method){
        $ch = curl_init();                                 //1.初始化
        curl_setopt($ch, CURLOPT_URL, $url);               //2.请求地址
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);  //3.请求方式
        curl_setopt($ch, CURLOPT_TIMEOUT,300);             //4.超时时间为 5分钟
        if($method=="POST"){                               //5.post方式的时候添加数据
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $tmpInfo = curl_exec($ch);
        if (curl_errno($ch)) {
            return curl_error($ch);
        }
        curl_close($ch);
        return $tmpInfo;
    }
    
}

/**
 * @param $str string
 * @param $bit int
 * @return array()
 */
function _LOG($str, $bit) {
    switch($bit) {
        case L_DEBUG:
            $preStr = "DEBUG:";
            break;
        case L_WARNING:
            $preStr = "WARNING:";
            break;
        case L_ERROR:
            $preStr = "ERROR:";
            break;
    }
    if($bit & L_EVENT) {
        Bingo_Log::notice($str);
        echo $preStr.":(".$str.")"."\r\n";
    }

}

// 执行
createTag::newInstance()->run();