<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2017/5/19
 * Time: 下午22:35
 * Desc: 更新物料读取进度为初始值
 */
ini_set('memory_limit','128M');
define('IS_ORP_RUNTIME',true);
define('ROOT_PATH', dirname(__FILE__). '/../../../');
define('L_DEBUG' , 1);
define('L_ERROR' , 2);
define('L_WARNING' , 4);
define('L_EVENT' , 7);
define('MODULE_NAME', 'beco');
define('SCRIPT_NAME', basename(__FILE__,".php"));   //定义脚本名

set_include_path(get_include_path() . PATH_SEPARATOR. ROOT_PATH);

/**
 * @desc 自动加载文件
 * @param 类名
 * @return
 **/
function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');

Bingo_Log::init(
    array(
        'log' => array(
            'file'  => dirname(__FILE__).'/../../../../../log/'.MODULE_NAME.'/scripts/'.SCRIPT_NAME.'.log',
            'level' => 0xFF,
        ),
    ),
    'log'
);

class resetReview extends Util_BaseService {
    protected static $_objRedis = null;//redis接口
    /**
     * @param
     * @return 
     */
    public static function run($arrSubPoint) {
        if (empty($arrSubPoint)) {
            Bingo_Log::warning('sub point is empty');
            return false;
        }
        //初始化Redis
        if (is_null(self::$_objRedis)) {
            Service_Libs_Redis::initRedis();
            self::$_objRedis = new Service_Libs_Redis();
        }

        foreach ($arrSubPoint as $key => $value) {
            $res = self::$_objRedis->set($key, $value);
            if ($res['err_no'] === Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::notice("time=".time()." key=".$key." value=".$value." set redis success");
                echo "time=".time()." key=".$key." value=".$value." set redis success\n";
            } else {
                Bingo_Log::notice("time=".time()." key=".$key." value=".$value." set redis error, data=".json_encode($res));
                echo "time=".time()." key=".$key." value=".$value." set redis error, data=".json_encode($res)."\n";
            }
        }
    }
}

/*
当一个参数时，而且为-1时，表示将当前64个pipelet_id同时设置为最新进度-1
当两个参数时，第一参数为pipelet_id，第二个参数为该bipelet_id的读取进度
*/

$arrSubPoint = array();
$startPipeletId = 0;
$endPipeletId = 63;

if ($argc == 2 && intval($argv[1]) == -1) {
    $pipeletId = $startPipeletId;
    while ($pipeletId <= $endPipeletId) {
        $arrSubPoint['GD_BIGPIPE_SUB_INFO_KEY_'.$pipeletId] = -1;
        $pipeletId++;
    }
} elseif ($argc == 3 && intval($argv[1]) >= $startPipeletId && intval($argv[1]) <= $endPipeletId && !empty($argv[2])) {
    $arrSubPoint['GD_BIGPIPE_SUB_INFO_KEY_'.intval($argv[1])] = intval($argv[2]);
} else {
    echo "param error!\n";
    exit;
}

// 执行
resetReview::run($arrSubPoint);
