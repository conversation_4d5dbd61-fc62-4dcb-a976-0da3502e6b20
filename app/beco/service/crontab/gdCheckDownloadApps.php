<?php
/**
 * Created by PhpStorm.
 * User: lihuifeng01
 * Date: 2017/5/21
 * Time: 21:22
 */

ini_set('memory_limit','128M');
define('ROOT_PATH', dirname(__FILE__). '/../../');
set_include_path(get_include_path() . PATH_SEPARATOR. ROOT_PATH);

define('L_DEBUG' , 1);
define('L_ERROR' , 2);
define('L_WARNING' , 4);
//日志level
define('L_EVENT' , 7);

_LOG(">>>>>>>>>> start execution >>>>>>>>>>", L_DEBUG);
$offlineObject = new checkDownloadApps();
$offlineObject->execute();
_LOG("<<<<<<<<<< execution completed <<<<<<<<<<", L_DEBUG);

class checkDownloadApps{
    public $planIdList;
    public $unitIdList;
    public $ideaIdList;

    /**
     * @param void
     * @return bool
     */
    public function getAppDownloadList(){
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Bingo_Log::warning ( "get db fail." );
            return false;
        }
        
        $arrFields = array(
            'id',
            'unit_id',
            'goods_info',
        );

        $strConds = "status =" . Util_Const::VALID_STATUS;
        $strConds .= " and template_id in ('BC0073','BC0082','BC0086')";

        //获取过期计划
        $arrRet = $db->select(Util_Const::$_tableName[Util_Const::IDEA], $arrFields, $strConds);
        if($arrRet === false){
            _LOG("db query fail to select app download ideas", L_ERROR);
            return false;
        }

        return $arrRet;
    }

    /**
     * @param void
     * @return bool|void
     */
    public function pauseIdea(){
        $arrAppDownloadIdeas = $this->getAppDownloadList();
        if (empty($arrAppDownloadIdeas)) {
            _LOG("no download app idea at this time", L_DEBUG);
            return true;
        }

        _LOG("getAppDownloadList res: " . json_encode($arrAppDownloadIdeas), L_DEBUG);

        // 获取30分钟内下线的app
        $arrParams = array(
            'source_type'   => 2,
            'event_type'    => 1,
            'start_time'    => time() - 1800,
        );
        $offlineAppRes = Service_Downloadapp_Downloadapp::getAppidByEventType($arrParams);
        if( !isset($offlineAppRes['errno']) || $offlineAppRes['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            _LOG("get offline apps fail, result: ". print_r($offlineAppRes, 1), L_WARNING);
            _LOG("fail errmsg: ".$offlineAppRes['errmsg'], L_WARNING);
            return false;
        }
        if (empty($offlineAppRes['data']['appid_list'])) {
            _LOG("no app be offline in 30mins, get offline apps res:" . json_encode($offlineAppRes), L_DEBUG);
            return true;
        }

        _LOG("apps offline in 30mins:" . json_encode($offlineAppRes['data']['appid_list']), L_DEBUG);

        $arrIdeaIdToPause = array();
        foreach ($arrAppDownloadIdeas as $ideaData) {
            $arrGoodsInfo = json_decode($ideaData['goods_info'], true);
            if (empty($arrGoodsInfo[0]['jumpurl'])) {
                _LOG("no android app download idea, idea_info: " . json_encode($ideaData), L_DEBUG);
                continue;
            }

            foreach ($offlineAppRes['data']['appid_list'] as $appInfo) {
                if ($arrGoodsInfo[0]['jumpurl'] == $appInfo['docid']) {
                    $arrIdeaIdToPause[] = $ideaData['id'];
                    _LOG("!!! idea_id: {$ideaData['id']}'s app has offline, need to pause" , L_DEBUG);
                }
            }
        }

        if (empty($arrIdeaIdToPause)) {
            _LOG("no download app idea to be pause at this time", L_DEBUG);
            return true;
        }

        $this->ideaIdList = implode(',', $arrIdeaIdToPause);
        if(empty($this->ideaIdList)){
            _LOG("no download app idea to be pause at this time", L_DEBUG);
            return true;
        }

        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Bingo_Log::warning ( "get db fail." );
            return false;
        }

        _LOG("idea list to pause:".$this->ideaIdList, L_DEBUG);
        $time = time();
        $arrUpdateParams = array(
            'status'    => Util_Const::PAUSE_STATUS,
            'mtime'     => $time,
            'op_name'   => 'beco_robot',
        );

        //批量操作,更新数据库
        $strConds = 'id in('.$this->ideaIdList.')';
        $arrUpdateRet = $db->update(Util_Const::$_tableName[Util_Const::IDEA], $arrUpdateParams, $strConds);
        if($arrUpdateRet === false){
            _LOG("db query fail to update idea status", L_ERROR);
            return false;
        }
        _LOG("idea pause success, idList:".$this->ideaIdList, L_DEBUG);
        return true;
    }

    /**
     * @param void
     * @return void
     */
    public function execute(){
        $this->pauseIdea();
    }
}

/**
 * @param $str string
 * @param $bit int
 * @return array()
 */
function _LOG($str, $bit) {
    switch($bit) {
        case L_DEBUG:
            $preStr = "DEBUG:";
            break;
        case L_WARNING:
            $preStr = "WARNING:";
            break;
        case L_ERROR:
            $preStr = "ERROR:";
            break;
    }
    if($bit&L_EVENT) {
        echo $preStr.":(".$str.")"."\r\n";
    }
}