<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR> 
 * @date 2016-10-17 16:49
 * @version
 * @structs & methods(copied from idl.)
*/

class Service_Channel_Channel{

    const STATUS_RUN = 1;
    const STATUS_STOP = 2;
    const STATUS_EXHAUST = 3;
    const STATUS_ULIMIT = -1; //-1为qps不限制
    const STATUS_ON = 1;
    const STATUS_OFF = 2;
    const QPS_CONTROL_UNLIMIT = 1;
    const QPS_CONTROL_LIMIT = 2;
    const THE_OTHER_CATALOGS = "无目录吧";
    const THE_SHOUBAI_DEFAULT_CATALOGS = "推荐";
    const CHANNEL_PURCHASE_RTB = 1;
    const CHANNEL_PURCHASE_CPM = 2;
    const MAX_STRLEN = 100;

    const CACHE_TYPE = 1;// cache use  1 redis or 2 memcache
    const CACHE_TYPE_REDIS = 1;// cache use  1 redis or 2 memcache
    const CACHE_TYPE_MEMCACHED = 2;// cache use  1 redis or 2 memcache
    const CACHE_EXPIRE = 600;// cache expire time
    const APC_EXPIRE = 60;// Apc expire time one day
    
    const OP_TOKEN_KEY = 'op_xuntian_token_list';

    public static $SHOUBAI_TAB_MAP = array(
        1  => '推荐',
        2  => '视频',
        3  => '娱乐',
        4  => '体育',
        5  => '时尚',
        6  => '国际',
        7  => '图集',
        8  => '新热',
        9  => '动图',
        10 => '本地',
        11 => '军事',
    );
    public static $PAGE_NAME = array();

    public static $PLACE_TYPE = array();

    //新系统的平台在旧系统里分为平台和系统两部分，这里拼凑新平台id
    //PC 30,App-Andorid 22,App-iOs 12,wap-Android 21, wap-iOs 11
    //平台类型( PC  WAP  APP ) 0 PC 1 wap 2 APP
    //操作系统，wap 和APP 时使用  1. IOS 2. Android 3 other
    
    public static $PLATFORM_TYPE = array(
        30 => 'PC',
        21 => 'wap-Android',
        11 => 'wap-iOs',
        22 => 'app-Android',
        12 => 'app-iOs',
    );

    public static $PRODUCT_TYPE = array();
    //目前只有贴吧 以及手机百度-贴吧框结合版有内容定向 新增手百
    public static $PRODUCT_TYPE_HAS_CONTENT = array(2,8,17);
    public static $PRODUCT_TYPE_TIEBA = array(2,17);
    public static $PRODUCT_TYPE_SHOUBAI = 8;


    public static $CATALOGS_CONTENT_ID = null;


    /**
     * @desc 产品线从ssp迁移之后从这里获取产品线 
     * @param arrInput
     * @return arrRes
     */
    private static function getProductList() {
        // body...
        if (empty(self::$PRODUCT_TYPE)) {
            $arrRes = Service_Product_Product::getProductList();
            if (!empty($arrRes) && $arrRes['errno'] == Service_Channel_Errcode::ERR_SUCCESS) {
                self::$PRODUCT_TYPE = array();
                foreach ($arrRes['data']['items'] as $value) {
                    $productId = intval($value['productId']);
                    self::$PRODUCT_TYPE[$productId] = $value['productName'];
                }
            }
        }
    }

    /**
     * @desc 广告位迁移后从这里后取广告位类型 
     * @param null 
     * @return null 
     */
    private static function getPlaceTypeList() {
        if (empty(self::$PLACE_TYPE)) {
            $arrRes = Service_Place_Place::getPlaceTypeList();
            if (!empty($arrRes) && $arrRes['errno'] == Service_Channel_Errcode::ERR_SUCCESS) {
                self::$PLACE_TYPE = array();
                foreach ($arrRes['data'] as $value) {
                    $id = intval($value['id']);
                    self::$PLACE_TYPE[$id] = $value['name'];
                }
            }
        }
    }

    /**
     * @desc 页面迁移后从这里后取页面 
     * @param null 
     * @return null
     */
    private static function getPageList() {
        if (empty(self::$PAGE_NAME)) {
            $arrDlInput = array(
                'function' => 'getPageList',
            );
            $arrRes = Dl_Channel_Place::execSql($arrDlInput);
            if (!empty($arrRes) && $arrRes['errno'] == Service_Channel_Errcode::ERR_SUCCESS) {
                self::$PAGE_NAME = array();
                foreach ($arrRes['results'][0] as $value) {
                    $id = intval($value['page_id']);
                    self::$PAGE_NAME[$id] = $value['page_name'];
                }
            }
        }
    }

    /**
     * @desc  
     * @param arrInput
     * @return arrRes
     */

    /**
     * Apctest 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function serviceTest($arrInput){
        $res   = Tieba_Service::call('beco', 'test', $arrInput, null, null, 'post', 'php', 'utf-8');
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS,$res);
    }
    
    /**
     * Apctest 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function nmqTest($arrInput){
        $arrInput = array();
        $arrInput['cid'] = 10000;
        $arrInput['uname'] = "jingchuan@change";
        $arrInput['used_money'] = 111;
        $res = Native_Commit::commit('als', 'changeChannel', $arrInput);
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS,$res);
    }


    /**
     * 检测是否重名 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function isNameDuplicated($arrInput){
        $cname = strval($arrInput['cname']);
        $cid = intval($arrInput['cid']);
        if(empty($cname)){
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
        if (empty($cid)) {
             //从数据库获取name
            $arrDlInput = array(
                'function' => 'selectChannelByName',
                'cname' => $cname,
            );

            $arrDlOutput = Dl_Channel_Channel::execSql($arrDlInput);

            if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
                return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
            }

            //如果存在结果则命名重复
            if (count($arrDlOutput['results'][0]) !== 0) {
                Bingo_Log::warning('cname already exist! output:['.serialize($arrDlOutput).']');
                return self::_errRet(Service_Channel_Errcode::ERR_MO_UNAME_CHECK_DUPLICATE_NOSUG);
            }
       
        
        }else {
             //从数据库获取cname 或 cid
            $arrDlInput = array(
                'function' => 'selectChannelByNameOrCid',
                'cname' => $cname,
                'cid' => $cid,
            );

            $arrDlOutput = Dl_Channel_Channel::execSql($arrDlInput);

            if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
                return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
            }
            
            //如果存在两条数据说明，修改的名字有重名
            if (count($arrDlOutput['results'][0]) >= 2) {
                Bingo_Log::warning('cname already exist! output:['.serialize($arrDlOutput).']');
                return self::_errRet(Service_Channel_Errcode::ERR_MO_UNAME_CHECK_DUPLICATE_NOSUG);
            }

        }
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS);
    }

    /**
     * 添加渠道 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function addChannel($arrInput){
        $cname = strval($arrInput['cname']);
        $ctype = intval($arrInput['ctype']);
        $purchase_mode = intval($arrInput['purchase_mode']);
        $ad_req_url = strval($arrInput['ad_req_url']);
        $ad_callback_url= strval($arrInput['ad_callback_url']);
        $month_deposit = intval($arrInput['month_deposit']);
        $deposit_leverage = floatval($arrInput['deposit_leverage']);
        $contact = strval($arrInput['contact']);
        $contact_phone = strval($arrInput['contact_phone']);
        $contact_email= strval($arrInput['contact_email']);
        $uname = strval($arrInput['uname']);
        if( '1' == $arrInput['deposit_ulimit']){
            $month_deposit = -1;
            $deposit_leverage = -1;
        }

        if(empty($uname) ||empty($cname) || empty($ctype) || empty($purchase_mode) || empty('ad_req_url') || empty('month_deposit') || empty('deposit_leverage')){ 
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }

        //从数据库获取name
        $arrDlInput = array(
            'function' => 'selectChannelByName',
            'cname' => $cname,
        );

        $arrDlOutput = Dl_Channel_Channel::execSql($arrDlInput);

        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }

        //如果存在结果则命名重复
        if (count($arrDlOutput['results'][0]) !== 0) {
            Bingo_Log::warning('cname already exist! output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_MO_UNAME_CHECK_DUPLICATE_NOSUG);
        }
        //插入数据
        $arrDlInput = array(
            'function' => 'insertChannel',
            'values' => array(
                array(
                    'cname' => $cname,
                    'ctype' => $ctype,
                    'purchase_mode' => $purchase_mode,
                    'ad_req_url' => $ad_req_url,
                    'ad_callback_url' => $ad_callback_url,
                    'month_deposit' => $month_deposit,
                    'deposit_leverage' => $deposit_leverage,
                    'contact' => $contact,
                    'contact_phone' => $contact_phone,
                    'contact_email' => $contact_email,
                    'status' => self::STATUS_RUN, //插入成功默认启用渠道
                    'create_uname' => $uname,
                    'create_time' => time(),
                ),
            ),
        );

        $arrDlOutput = Dl_Channel_Channel::execSql($arrDlInput);

        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }

        // 获取新插入的cid
        $arrDlInput = array(
            'function' => 'getMaxId',
        );
        $arrDlOutput = Dl_Channel_Channel::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        $res = array('cid' => $arrDlOutput['results'][0][0]['max(cid)']); 
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS,$res);
    }

    /**
     * 获取渠道基本信息 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function getChannelBasicInfo($arrInput){
        $cid = intval($arrInput['cid']);
        if(empty($cid)){
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
         //从数据库获取渠道信息
        $arrDlInput = array(
            'function' => 'getChannelBasicInfo',
            'cid' => $cid,
        );

        $arrDlOutput = Dl_Channel_Channel::execSql($arrDlInput);

        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        if (count($arrDlOutput['results'][0]) == 0 ) {
            Bingo_Log::warning('empty results of select '.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_EMPTY_RESULT);
        }
        $arrRes = $arrDlOutput['results'][0][0];
        $arrRes['canRtbToCpm'] = true;
        if ($arrRes['purchase_mode'] == self::CHANNEL_PURCHASE_RTB) {
            $res = self::canRtbToCpm(array('cid' => $arrRes['cid']));
            if ($res['errno'] == Service_Channel_Errcode::ERR_SUCCESS){
                $arrRes['canRtbToCpm'] = $res['data']['canRtbToCpm'];
            }
        }
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS,$arrRes);    
    }


    /**
     * 修改渠道 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function updateChannel($arrInput){
        $cid = intval($arrInput['cid']);
        $cname = strval($arrInput['cname']);
        $ctype = intval($arrInput['ctype']);
        $purchase_mode = intval($arrInput['purchase_mode']);
        $ad_req_url = strval($arrInput['ad_req_url']);
        $ad_callback_url= strval($arrInput['ad_callback_url']);
        $month_deposit = intval($arrInput['month_deposit']);
        $deposit_leverage = floatval($arrInput['deposit_leverage']);
        $contact = strval($arrInput['contact']);
        $contact_phone = strval($arrInput['contact_phone']);
        $contact_email= strval($arrInput['contact_email']);
        if( '1' == $arrInput['deposit_ulimit']){
            $month_deposit = -1;
            $deposit_leverage = -1;
        }
        $uname = strval($arrInput['uname']);
        if(empty($uname) || empty($cid) || empty($cname) || empty($ctype) || empty($purchase_mode) || empty('ad_req_url') || empty('month_deposit') || empty('deposit_leverage')){
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
        //从数据库获取cname 或 cid
        $arrDlInput = array(
            'function' => 'selectChannelByNameOrCid',
            'cname' => $cname,
            'cid' => $cid,
        );

        $arrDlOutput = Dl_Channel_Channel::execSql($arrDlInput);

        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        
        //如果存在两条数据说明，修改的名字有重名
        if (count($arrDlOutput['results'][0]) >= 2) {
            Bingo_Log::warning('cname already exist! output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_MO_UNAME_CHECK_DUPLICATE_NOSUG);
        }
        
        //如果保证金或保证金杠杆有变化，且状态为保证金已用完则将状态改为启用，并清除cache
        $results = $arrDlOutput['results'][0][0];
        $status = $results['status'];
        if (round($results['month_deposit'] * $results['deposit_leverage']) < round($month_deposit * $deposit_leverage) && $results['status'] == self::STATUS_EXHAUST) {
            $status = self::STATUS_RUN;
        
        }

        //如果将采购方式从RTB变为CPM,需先将所有广告位关闭
        if($results['purchase_mode'] == self::CHANNEL_PURCHASE_RTB && $purchase_mode == self::CHANNEL_PURCHASE_CPM){
            $arrDlInput = array(
                'function' => 'getOnPlaceIdByChannelId',
                'cid' => $cid,
            );

            $arrDlOutput = Dl_Channel_Place::execSql($arrDlInput);

            if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
                return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
            }
            
            if( count($arrDlOutput['results'][0]) > 0 ) {
                Bingo_Log::warning('purchase_mode rtb to cpm with some  placeid on :'.serialize($arrDlOutput['results'][0]));
                return self::_errRet(Service_Channel_Errcode::ERR_PLACE_NOT_OFF);
            } 
        }

        //更新数据库
        $arrDlInput = array(
            'function' => 'updateChannel',
            'cname' => $cname,
            'ctype' => $ctype,
            'purchase_mode' => $purchase_mode,
            'ad_req_url' => $ad_req_url,
            'ad_callback_url' => $ad_callback_url,
            'month_deposit' => $month_deposit,
            'deposit_leverage' => $deposit_leverage,
            'contact' => $contact,
            'contact_phone' => $contact_phone,
            'contact_email' => $contact_email,
            'status' => $status, //插入成功默认启用渠道
            'cid' => $cid,
            'update_uname' => $uname,
            'update_time' => time(),
        );

        $arrDlOutput = Dl_Channel_Channel::execSql($arrDlInput);

        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }

        //清除缓存
        $arrPlaceIds= self::getPlaceIdsByChannelId($arrInput);
        if (!$arrPlaceIds|| $arrPlaceIds['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('get cid by placeid faild:['.serialize($arrPlaceIds).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrClearRes = self::clearCache($arrPlaceIds['data']);
        if (!$arrClearRes || $arrClearRes['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('clear cache faild:['.serialize($arrClearRes).']');
            return self::_errRet(Service_Channel_Errcode::ERR_CACHE_DEL_FAILED);
        } 
        
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS);
    }

    /**
     * 通过广告位获取渠道id
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function getPlaceIdsByChannelId($arrInput){
        $cid = intval($arrInput['cid']);
        if(empty($cid)){
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'function' => 'getPlaceIdByChannelId',
            'cid' => $cid,
        );
        $arrDlOutput = Dl_Channel_Place::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrRes = array();
        foreach ($arrDlOutput['results'][0] as $value) {
            $arrRes[] = $value['place_id']; 
        }
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS,$arrRes);

    }

    /**
     * 渠道列表 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function listChannel($arrInput){
        $cname = strval($arrInput['cname']);
        $status = intval($arrInput['status']);
        // 获取新插入的cid
        $cond = "cname like'%".$cname."%'";
        if ($status != 0 ) {
            $cond = $cond . 'and status = '.$status;
        }
        $arrDlInput = array(
            'function' => 'listChannel',
            'cond' => $cond,
        );
        $arrDlOutput = Dl_Channel_Channel::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS,$arrDlOutput['results'][0]);
        
    }

    /**
     * 批量变更渠道状态 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function batchChangeChannelStatus($arrInput){
        $data = $arrInput['data'];
        $uname = strval($arrInput['uname']);
        if (empty($uname) || !is_array($data)) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
        $arrRes = array('success'=>array(),'failed'=>array());
        //兼容前端传1个数据时[{}] => {}的情况
        if(isset($data['cid'])) {
            $data = array($data);
        }
        foreach ($data as $value) {
            $arrInput = array();
            $arrInput['uname'] = $uname;
            $cid = intval($value['cid']);
            $arrInput['cid'] = $cid;
            $arrInput['status'] = $value['status'];
            $res = self::changeChannelStatus($arrInput);
            if($res['errno'] == Service_Channel_Errcode::ERR_SUCCESS) {
                $arrRes['success'][] = $cid;
            } else {
                $arrRes['failed'][] = $value['cid'];
            }
        }
        if(count($arrRes['failed']) == 0) {
            $arrRes['flag'] = true;
        } else {
            $arrRes['flag'] = false;
        }
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS,$arrRes);
    }


    
    
    /**
     * 变更渠道状态 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function changeChannelStatus($arrInput){
        if(isset($arrInput['from_nmq']) && $arrInput['from_nmq']){
            $fromNmq = true;
            $strInputData = file_get_contents("php://input");
            $strTransMcpack  = mc_pack_text2pack(substr($strInputData,5));
            if($strTransMcpack === false || empty($strTransMcpack)){
                Bingo_Log::warning("mc_pack_text2pack error. [".$_SERVER['REQUEST_URI']."]");
                return Service_Base_Live::errRet(Tieba_Errcode::ERR_MCPACK_ERR);
            }
            $arrTransPack = mc_pack_pack2array($strTransMcpack);
            if($arrTransPack === false || empty($arrTransPack)){
                Bingo_Log::warning("mc_pack_pack2array error. [".$_SERVER['REQUEST_URI']."]");
                return Service_Base_Live::errRet(Tieba_Errcode::ERR_MCPACK_ERR);
            }
            $arrInput = $arrTransPack;
        }
        $cid = intval($arrInput['cid']);
        $status = intval($arrInput['status']);
        $uname = strval($arrInput['uname']);
        if (empty($cid) || empty($uname) || $status <= 0 || $status > self::STATUS_EXHAUST ) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
        Bingo_Log::pushNotice("cid",$cid);
        Bingo_Log::pushNotice("status",$status);
        Bingo_Log::pushNotice("opname",$uname);
        $arrDlInput = array(
            'function' => 'changeChannelStatus',
            'cid' => $cid,
            'status' => $status,
            'update_uname' => $uname,
            'update_time' => time(),
        );
        $arrDlOutput = Dl_Channel_Channel::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        
        //清除缓存
        $arrPlaceIds= self::getPlaceIdsByChannelId($arrInput);
        if (!$arrPlaceIds|| $arrPlaceIds['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('get cid by placeid faild:['.serialize($arrPlaceIds).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrClearRes = self::clearCache($arrPlaceIds['data']);
        if (!$arrClearRes || $arrClearRes['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('clear cache faild:['.serialize($arrClearRes).']');
            return self::_errRet(Service_Channel_Errcode::ERR_CACHE_DEL_FAILED);
        }
        
        if ($status == self::STATUS_STOP) {
            Bingo_Timer::start("sendMail");
            Util_SendMail::sendFreezeMail($arrInput); 
            Bingo_Timer::end("endMail");
        } else if ($status == self::STATUS_EXHAUST) {
            Bingo_Timer::start("sendMail");
            Util_SendMail::sendExhaustMail($arrInput); 
            Bingo_Timer::end("sendMail");
        }
         
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS,$arrDlOutput['results'][0]);
    }


    /**
     * 保存渠道广告位信息 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function saveChannelPlaceInfo($arrInput){
        $cid = intval($arrInput['cid']);
        $platforms = $arrInput['platforms'];
        $products = $arrInput['products'];
        $max_qps = intval($arrInput['max_qps']);
        $place_datas = $arrInput['place_datas'];
        $uname = strval($arrInput['uname']);
        $purchase_mode = intval($arrInput['purchase_mode']);
        if ( $cid <= 0 || !is_array($place_datas) || !is_array($platforms) || !is_array($products) || empty($purchase_mode)) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
        foreach ($platforms as $key => $value) {
            $platforms[$key] = intval($value);
            if ($platforms[$key] <= 0) {
                Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
                return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
            }
        }
        foreach ($products as $key => $value) {
            $products[$key] = intval($value);
            if ($products[$key] <= 0) {
                Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
                return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
            }
        }
        $product_ids = serialize($products);
        $platform_ids = serialize($platforms);
        $place_cpms = array();
        $create_time = time();
        for ($i = 0; $i < count($place_datas); $i++) {
            $place_datas[$i]['place_id'] = intval($place_datas[$i]['place_id']); 
            $place_datas[$i]['cpm_price'] = floatval($place_datas[$i]['cpm_price']); 
            $place_datas[$i]['first_catalogs'] = strval($place_datas[$i]['first_catalogs']); 
            $place_datas[$i]['second_catalogs'] = strval($place_datas[$i]['second_catalogs']); 
            $place_datas[$i]['status'] = intval($place_datas[$i]['status']); 
            $place_datas[$i]['qps_control'] = intval($place_datas[$i]['qps_control']); 
            $place_datas[$i]['qps_percent'] = floatval($place_datas[$i]['qps_percent']); 
            $place_datas[$i]['platform_id'] = intval($place_datas[$i]['platform_id']); 
            $place_datas[$i]['product_id'] = intval($place_datas[$i]['product_id']); 
            $place_datas[$i]['create_uname'] = $uname; 
            $place_datas[$i]['create_time'] = $create_time; 
            $place_datas[$i]['cid'] = $cid; 

            //不对cpm_price做校验，rtb模式不care此字段
            if($place_datas[$i]['place_id'] <= 0 ||
                $place_datas[$i]['status'] <= 0 ||
                $place_datas[$i]['qps_control'] <= 0 ||
                $place_datas[$i]['platform_id'] <= 0 ||
                $place_datas[$i]['product_id'] <= 0
            ){
                Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
                return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
            }

            //对状态是开的做校验
            if ( $place_datas[$i]['status'] == self::STATUS_ON ) { 
                $place_cpms[$place_datas[$i]['place_id']] = $place_datas[$i]['cpm_price'];
            }
        }

        //从 adp_admin数据库中获取placeId对应的底价CPM
        if( self::CHANNEL_PURCHASE_CPM == $purchase_mode && !empty($place_cpms)) {
            $arrDlInput = array(
                'function' => 'getCPMByCid',
                'place_id' => implode(",",array_keys($place_cpms)),
            );
            $arrDlOutput = Dl_Channel_Place::execSql($arrDlInput);
            if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
                return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
            }
            
            $arrCpmTooLow = array();
            foreach($arrDlOutput['results'][0] as $value ){
                $low_price = floatval($value['fix_cpm_low_price'] / 100); //转化为元
                $place_id = intval($value['place_id']);
                if ( $place_cpms[$place_id] < $low_price) {
                    $arrCpmTooLow[] = array(
                        'place_id' => $place_id,
                        'place_cpm' => $place_cpms[$place_id],
                        'fix_cpm_low_price' => $low_price,
                    );
                }
            }
            if ( 0 !== count($arrCpmTooLow)) {
                Bingo_Log::warning('cpm lower than fix_cpm_low_price place_cpms info: ['.serialize($arrCpmTooLow).']');
                return self::_errRet(Service_Channel_Errcode::ERR_CPM_TOO_LOW,$arrCpmTooLow);
            }
        }
        $arrPlaceIds= self::getPlaceIdsByChannelId($arrInput);
        if (!$arrPlaceIds|| $arrPlaceIds['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('get cid by placeid faild:['.serialize($arrPlaceIds).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        //合并改动的placeId,一会儿好清除缓存
        $arrPlaceIds = array_merge($arrPlaceIds['data'],array_keys($place_cpms));
         

        //删除cid已有的placeId信息
        Dl_Channel_Place::startTransaction();
        $arrDlInput = array(
            'function' => 'delPlaceByCid',
            'cid' => $cid,
        );
        $arrDlOutput = Dl_Channel_Place::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }

        //增加新的记录
        $arrDlInput = array(
            'function' => 'insertPlaceInfo',
            'values' => $place_datas,
        );
        $arrDlOutput = Dl_Channel_Place::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            Dl_Channel_Place::rollback(); 
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }

        //变更渠道记录
        $arrDlInput = array(
            'function' => 'changePlatformProduct',
            'platform_ids' => $platform_ids,
            'product_ids' => $product_ids,
            'max_qps' => $max_qps,
            'update_uname' => $uname,
            'update_time' => $create_time,
            'cid' => $cid,
        );
        $arrDlOutput = Dl_Channel_Channel::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            Dl_Channel_Place::rollback(); 
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        
        if ( true !== Dl_Channel_Place::commit()) {
            Bingo_Log::warning('commit fail!');
            Dl_Channel_Place::rollback(); 
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }

        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS);
    }


    /**
     * 从根据产品及平台获取广告位渠道信息
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function getPlaceInfoByParams($arrInput) {
        $cid = intval($arrInput['cid']);
        $platforms = $arrInput['platforms'];
        $products = $arrInput['products'];
        if ( $cid <= 0 || !is_array($platforms) || !is_array($products)) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
        if (empty($products) || empty($platforms)) {
            return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS,array());
        }
        foreach ($platforms as $key => $value) {
            $platforms[$key] = intval($value);
            if ($platforms[$key] <= 0) {
                Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
                return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
            }
        }
        foreach ($products as $key => $value) {
            $products[$key] = intval($value);
            if ($products[$key] <= 0) {
                Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
                return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
            }
        }

        $arrDlInput = array(
            'function' => 'getPlaceInfoByParams',
            'cond' => self::genPlatFormProductCond($platforms, $products),
        );
        $arrDlOutput = Dl_Channel_Place::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrRes = array();
        $arrUnSetKeys = array(); // 没有设置的key,方便一会儿整理
        self::getPageList();
        self::getPlaceTypeList();
        self::getProductList();
        foreach ($arrDlOutput['results'][0] as $value) {
            $res = array();
            $place_id = intval($value['place_id']);
            $arrUnSetKeys[$place_id] = 1;
            $res['place_id'] = $place_id;
            $res['place_name'] = $value['place_name'];
            $res['product_id'] = intval($value['product_id']);
            $res['has_catalogs'] = false ;
            if(in_array($res['product_id'],self::$PRODUCT_TYPE_HAS_CONTENT)){
                $res['has_catalogs'] = true;
            } 
            $res['platform_id'] = self::getPlatformByOsPlatform($value['platform_type'],$value['os_type']);
            $res['page_name'] = self::$PAGE_NAME[intval($value['page_id'])];
            $res['place_type'] = self::$PLACE_TYPE[intval($value['place_type'])];
            $res['fix_cpm_low_price'] = floatval($value['fix_cpm_low_price'] / 100); //转化为元
            //初始化一些参数
            $res['cpm_price'] = 0;
            $res['first_catalogs'] = '';
            $res['second_catalogs'] = '';
            $res['first_catalogs_ids'] = '';
            $res['second_catalogs_ids'] = '';
            $res['failed_catalogs'] = '';
            $res['status'] = self::STATUS_OFF;
            $res['qps_control'] = self::QPS_CONTROL_UNLIMIT;
            $res['qps_percent'] = 0;
            $arrRes[$place_id] = $res;
        }
        
        //从beco中获取已保存信息
        $arrDlInput = array(
            'function' => 'getPlaceInfoByCond',
            'cid' => $cid,
            'products' => implode(',',$products),
            'platforms' => implode(',',$platforms),
        );
        $arrDlOutput = Dl_Channel_Place::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrSetOnKeys = array(); //有设置开的keys
        $arrSetOffKeys = array(); //有设置关的keys
        foreach ($arrDlOutput['results'][0] as $value) {
            // 将一些参数变为新的值
            $place_id = intval($value['place_id']);
            $arrRes[$place_id]['cpm_price'] = floatval($value['cpm_price']);
            $arrRes[$place_id]['first_catalogs'] = $value['first_catalogs'];
            $arrRes[$place_id]['second_catalogs'] = $value['second_catalogs'];
            // 自定义目录的id
            $arrContentIdRes = self::getContentIdByContent($value);
            if($arrContentIdRes) {
                $arrRes[$place_id]['first_catalogs_ids'] = $arrContentIdRes['first_catalogs_ids'];
                $arrRes[$place_id]['second_catalogs_ids'] = $arrContentIdRes['second_catalogs_ids'];
                $arrRes[$place_id]['failed_catalogs'] = $arrContentIdRes['failed_catalogs'];
            }
            $status = intval($value['status']);
            $arrRes[$place_id]['status'] = $status;
            if ($status == self::STATUS_ON) {
                $arrSetOnKeys[$place_id] = 1;
            } else {
                $arrSetOffKeys[$place_id] = 1;
            }
            unset($arrUnSetKeys[$place_id]);

            $arrRes[$place_id]['qps_control'] = intval($value['qps_control']);
            $arrRes[$place_id]['qps_percent'] = floatval($value['qps_percent']);
        }

        $arrShiftRes = array();  
        //改变结果顺序，置为开的返回在前面，未设置的返回在后面
        foreach ($arrSetOnKeys as $key => $value) {
            $arrShiftRes[$key] = $arrRes[$key];
        }
        foreach ($arrSetOffKeys as $key => $value) {
            $arrShiftRes[$key] = $arrRes[$key];
        }
        foreach ($arrUnSetKeys as $key => $value) {
            $arrShiftRes[$key] = $arrRes[$key];
        }

        $arrNewRes = array('place_data' => array());
        foreach ($arrShiftRes as $key => $value) {
            $value['product_str'] = self::getProductStr($value['product_id']);
            $value['platform_str'] = self::getPlatformStr($value['platform_id']);
            $arrNewRes['place_data'][] = $value;
        }
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS,$arrNewRes);
    }





    /**
     * 从根据渠道id获取渠道信息
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function getChannelInfo($arrInput){
        $cid = intval($arrInput['cid']);
        if(empty($cid)){
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
        //从数据库获取渠道信息
        $arrDlInput = array(
            'function' => 'selectChannelDetailByCid',
            'cid' => $cid,
        );

        $arrDlOutput = Dl_Channel_Channel::execSql($arrDlInput);

        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRes = array();
        $arrRes['cname'] = $arrDlOutput['results'][0][0]['cname'];
        $arrRes['ctype'] = $arrDlOutput['results'][0][0]['ctype'];
        $arrRes['max_qps'] = $arrDlOutput['results'][0][0]['max_qps'];
        $arrRes['purchase_mode'] = $arrDlOutput['results'][0][0]['purchase_mode'];
        $platform_ids = unserialize($arrDlOutput['results'][0][0]['platform_ids']);
        $product_ids = unserialize($arrDlOutput['results'][0][0]['product_ids']);
        $arrRes['platforms'] = self::getStatus($platform_ids, self::$PLATFORM_TYPE);
        self::getProductList();
        $arrRes['products'] = self::getStatus($product_ids, self::$PRODUCT_TYPE);
        $arrInput = array();
        $arrInput['cid'] = $cid;
        $arrInput['platforms'] = $platform_ids;
        $arrInput['products'] = $product_ids;
        if($platform_ids == false ){
            $arrInput['platforms'] = array();
        }
        if($product_ids == false) {
            $arrInput['products'] = array();
        }
        //获取广告位信息
        $placeInfo = self::getPlaceInfoByParams($arrInput);
        if(!$placeInfo || $placeInfo['errno'] != 0) {
            Bingo_Log::warning('call getPlaceInfoByParams fail, input:['.serialize($arrInput).'],output:['.serialize($placeInfo).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrRes['place_data'] = $placeInfo['data']['place_data'] ;
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS,$arrRes);

    }

    /**
     * 从根据广告位获取渠道RTB信息
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function getChannelByPlaceId($arrInput) {
        $place_id = intval($arrInput['place_id']);
        $first_catalogs = $arrInput['first_catalogs'];
        $second_catalogs = $arrInput['second_catalogs'];
        if(empty($place_id)){
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
        Bingo_Log::pushNotice("place_id",$place_id);
        Bingo_Log::pushNotice("first_catalogs",$first_catalogs);
        Bingo_Log::pushNotice("second_catalogs",$second_catalogs);
        Bingo_Timer::start('getfromapc');
        $arrRes = self::getApcCache($arrInput);
        Bingo_Timer::end('getfromapc');
        if(!$arrRes) {
            Bingo_Timer::start('getfromcache');
            $arrRes = self::getCache($arrInput);
            Bingo_Timer::end('getfromcache');
            if(!$arrRes) {
                Bingo_Timer::start('getfromdb');
                $arrRes = self::getRTBInfoFromDB($arrInput);
                Bingo_Timer::end('getfromdb');
                if(!$arrRes || $arrRes['errno'] != 0) {
                    Bingo_Log::warning('getRTBInfoFromDB still failed  input:['.serialize($arrInput).'],output:['.serialize($arrRes).']' );
                    return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
                }
                $arrRes = $arrRes['data'];
                $arrCacheInput = array(
                    "result" => $arrRes,
                    "place_id" => $place_id,
                );
                Bingo_Timer::start('addcache');
                self::addCache($arrCacheInput);
                Bingo_Timer::end('addcache');
            } else {
                Bingo_Log::pushNotice("hitcache","success");
            }
            $arrCacheInput = array(
                "result" => $arrRes,
                "place_id" => $place_id,
            );
            self::addApcCache($arrCacheInput);
        } else {
            Bingo_Log::pushNotice("hitlocalcache","success");
        }
        //整合结果
        $arrNewRes = array(); 
        $arrNewRes['place_infos'] = $arrRes['priceInfo'];
        $arrNewRes['channel_infos'] = array();
        if($first_catalogs == null && $second_catalogs == null) {
            $new_first_catalogs = self::THE_OTHER_CATALOGS;
            $new_second_catalogs = self::THE_OTHER_CATALOGS.':'.self::THE_OTHER_CATALOGS;
        } else {
            $new_first_catalogs = $first_catalogs;
            $new_second_catalogs = $first_catalogs.':'.$second_catalogs;
        }
        if (!empty($arrRes['placeInfo']) && $arrRes['placeInfo'][0]['product_id'] == self::$PRODUCT_TYPE_SHOUBAI) {
            if ($new_first_catalogs == self::THE_OTHER_CATALOGS) {
                $new_first_catalogs = self::THE_SHOUBAI_DEFAULT_CATALOGS;
            } 
        }
        foreach ($arrRes['placeInfo'] as $value) {
            //有内容定向的产品，需要检查是否内容定向
            if(in_array($value['product_id'],self::$PRODUCT_TYPE_HAS_CONTENT)) {
                $arrFirstCatalogs = explode(',',$value['first_catalogs']);
                $arrSecondCatalogs = explode(',',$value['second_catalogs']);
                if(!in_array($new_first_catalogs,$arrFirstCatalogs) && !in_array($new_second_catalogs,$arrSecondCatalogs)){
                    continue;
                }
            }
            $cid = $value['cid'];
            //渠道状态不为Run 则跳过
            if ( empty($arrRes['channelInfo'][$cid]) || $arrRes['channelInfo'][$cid]['status'] != self::STATUS_RUN ) {
                continue;
            }
            $temp = array() ;
            $temp['channel_id'] = $cid ;
            $temp['channel_name'] = $arrRes['channelInfo'][$cid]['cname'];
            $temp['max_qps'] = $arrRes['channelInfo'][$cid]['max_qps'];
            $temp['purchase_mode'] = $arrRes['channelInfo'][$cid]['purchase_mode'];
            $temp['ad_req_url'] = $arrRes['channelInfo'][$cid]['ad_req_url'];
            $temp['ad_callback_url'] = $arrRes['channelInfo'][$cid]['ad_callback_url'];
            $temp['month_deposit'] = $arrRes['channelInfo'][$cid]['month_deposit'];
            $temp['deposit_leverage'] = $arrRes['channelInfo'][$cid]['deposit_leverage'];
            $temp['qps_percent'] = self::STATUS_ULIMIT; 
            if ($value['qps_control'] != self::QPS_CONTROL_UNLIMIT) {
                $temp['qps_percent'] = $value['qps_percent']; 
            }
            if ($temp['purchase_mode'] == self::CHANNEL_PURCHASE_RTB) {
                $temp['cpm_price'] = -1; //当采购方式是rtb是cpm_price是-1
            } else {
                $temp['cpm_price'] = $value['cpm_price'];
            }
            $arrNewRes['channel_infos'][] = $temp;
        }

        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS,$arrNewRes);
    }


    /**
     * 根据内容获取内容id
     * @param [type] $errno [description]
     * @return [type] [description]
     */
    public static function getContentIdByContent($arrInput){
        if(!in_array($arrInput['product_id'],self::$PRODUCT_TYPE_HAS_CONTENT)){
            return false;
        }
        $productId = $arrInput['product_id'];
        if(self::$CATALOGS_CONTENT_ID[$productId] == null) {
            $res = self::getContentByProductId($arrInput);
            if(!$res || $res['errno'] != Service_Channel_Errcode::ERR_SUCCESS) {
                return false;
            }
            self::$CATALOGS_CONTENT_ID[$productId] = array( 
                'first_catalogs' => array(), 
                'second_catalogs' => array(),
            );
            foreach($res['data'] as $value) {
                self::$CATALOGS_CONTENT_ID[$productId]['first_catalogs'][$value['dirName']] = $value['dirId'];
                if ($value['children'] != null) {
                    foreach ($value['children'] as $childrenValue) {
                        self::$CATALOGS_CONTENT_ID[$productId]['second_catalogs'][$value['dirName'].':'.$childrenValue['dirName']] = $childrenValue['dirId'];
                    }
                }
            }
        }
        $arrFirstCatalogs = explode(',',$arrInput['first_catalogs']);
        $arrSecondCatalogs = explode(',',$arrInput['second_catalogs']);
        $arrFirstCatalogsId = array(); 
        $arrSecondCatalogsId  = array();
        $arrFailed = array();
        foreach ($arrFirstCatalogs as $value) {
            if(isset(self::$CATALOGS_CONTENT_ID[$productId]['first_catalogs'][$value])){
                $arrFirstCatalogsId[] = self::$CATALOGS_CONTENT_ID[$productId]['first_catalogs'][$value]; 
            } else {
                $arrFailed[] = $value;    
            }
        }
        foreach ($arrSecondCatalogs as $value) {
            if(isset(self::$CATALOGS_CONTENT_ID[$productId]['second_catalogs'][$value])){
                $arrSecondCatalogsId[] = self::$CATALOGS_CONTENT_ID[$productId]['second_catalogs'][$value]; 
            } else {
                $arrFailed[] = $value;    
            }
        }
        $arrRes = array(
            'first_catalogs_ids' => implode(',',$arrFirstCatalogsId),
            'second_catalogs_ids' => implode(',',$arrSecondCatalogsId),
            'failed_catalogs' => implode(',',$arrFailed),
        ); 
        return $arrRes;
    }


    /**
     * 根据产品线获取内容定向
     * @param [type] $errno [description]
     * @return [type] [description]
     */
    public static function getContentByProductId($arrInput) {
        $productId = intval($arrInput['product_id']);
        if(empty($productId)) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
        $arrRes = array();
        if(in_array($productId,self::$PRODUCT_TYPE_TIEBA)){
            $arrRes = Tieba_Service::call('forum', 'getAllDir', array(), null, null, 'post', 'php', 'utf-8');
            if(!$arrRes || $arrRes['errno'] != Service_Channel_Errcode::ERR_SUCCESS){
                Bingo_Log::warning('getAllDir failed! res :'.serialize($arrRes));
                return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
            }
            $arrRes = $arrRes['output'];
            //返回结果加上其他2
            $arrRes['all_dir'][] = array(
                'level_1_name' => self::THE_OTHER_CATALOGS,
            );
            $arrNewRes = array();
            $firstId = 1;
            foreach ($arrRes['all_dir'] as $value) {
                $temp = array();
                $strFirstId = strval($firstId);
                $temp['dirId'] = $strFirstId;
                $temp['dirName'] = $value['level_1_name'];
                $secondId = 1;
                $children = array();
                foreach ($value['level_2_name'] as $secondValue) {
                    $strSecondId = strval($secondId);
                    $children[] = array(
                        'dirId' => $strFirstId.'.'.$strSecondId,
                        'dirName' => $secondValue,
                        'children' => null,
                    );
                    $secondId++;
                }
                if(empty($children)){
                    $temp['children'] = null;
                } else {
                    $temp['children'] = $children;
                }
                $firstId++;
                $arrNewRes[] = $temp;
            }
            $arrRes = $arrNewRes;

        } 
        if ($productId == self::$PRODUCT_TYPE_SHOUBAI) {
            foreach (self::$SHOUBAI_TAB_MAP as $key =>$value) {
                $arrRes[] = array(
                    'dirId' => $key,
                    'dirName' => $value,
                    'children' => null,
                );
            }
        }
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS,$arrRes);
    }

    /**
     * 从DB获取渠道rtb信息
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function getRTBInfoFromDB($arrInput) {
        $place_id = intval($arrInput['place_id']);
        if(empty($place_id)){
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }

        //从native_channel_place表中获取Cid及其他相关信息
        $arrDlInput = array(
            'function' => 'getChannelIdByPlaceId',
            'place_id' => $place_id,
        );

        $arrDlOutput = Dl_Channel_Place::execSql($arrDlInput);

        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrCid = array();
        $arrPlaceInfo = $arrDlOutput['results'][0];
        foreach ($arrPlaceInfo as $value) {
            $arrCid[] = $value['cid']; 
        }

        // 如果广告位没有对应的渠道则直接返回
        $arrChannelInfo = array();
        if( 0 != count($arrCid)){
            // 从native_channel 表中获取渠道信息
            $arrDlInput = array(
                'function' => 'selectChannelDetailByCids',
                'cids' => implode(",",$arrCid),
            );

            $arrDlOutput = Dl_Channel_Channel::execSql($arrDlInput);

            if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
                return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
            }
            foreach($arrDlOutput['results'][0] as $value){
                $arrChannelInfo[$value['cid']] = $value;     
            }
        }
        // 从adp_admin 中获取lowest_cpm_price,lowest_cpc_price
        $arrDlInput = array(
            'function' => 'getFixPrice',
            'place_id' => $place_id,
        );

        $arrDlOutput = Dl_Channel_Place::execSql($arrDlInput);

        
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS ) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrPriceInfo = $arrDlOutput['results'][0][0];
        $arrNewPriceInfo = array(
            'lowest_cpm_price' => floatval($arrPriceInfo['fix_cpm_low_price'] / 100), //转化为元
        );
        $arrRes = array();
        $arrRes['placeInfo'] = $arrPlaceInfo;
        $arrRes['channelInfo'] = $arrChannelInfo;
        $arrRes['priceInfo'] = $arrNewPriceInfo;
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS,$arrRes);
    }

    /**
     * 获取渠道是否能从RTB状态变为CPM 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function canRtbToCpm($arrInput){
        $cid = intval($arrInput['cid']);
        if (empty($cid)) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'function' => 'getOnPlaceIdByChannelId',
            'cid' => $cid,
        );

        $arrDlOutput = Dl_Channel_Place::execSql($arrDlInput);

        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }

        $arrRes = array( 'canRtbToCpm' => true);
        if( count($arrDlOutput['results'][0]) > 0 ) {
            Bingo_Log::warning('purchase_mode rtb to cpm with some  placeid on :'.serialize($arrDlOutput['results'][0]));
            $arrRes['canRtbToCpm'] = false;
        }
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS,$arrRes);
    }


    /**
     * 获取产品或平台状态描述
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    private static function getStatus($arrId,$arrType){
        $arrRes = array();
        foreach ($arrType as $key => $value) {
            $temp = array();
            $temp['type'] = $value;
            $temp['value'] = $key;
            if (in_array($key,$arrId)) {
                $temp['status'] = self::STATUS_ON;
            } else {
                $temp['status'] = self::STATUS_OFF;
            }
            $arrRes[] = $temp;
        }
        return $arrRes;       
    }

    /**
     * 获取产品描述
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    private static function getProductStr($id){
        return self::$PRODUCT_TYPE[$id]; 
    }

    /**
     * 获取平台描述
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    private static function getPlatformStr($id){
        return self::$PLATFORM_TYPE[$id];
    }

    /**
     * 获取平台id 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    private static function getPlatformByOsPlatform($platformId,$osId) {
        return ($osId * 10 + $platformId);
    }

    /**
     * 生成platform,products相关sql语句
     * @param  array,array 
     * @return string 
     */
    private static function genPlatFormProductCond($platforms, $products) {
        $res = array();
        foreach ($platforms as $platform) {
            $os = intval($platform / 10);
            $platform = $platform % 10;
            $res[] = "( platform_type = $platform and os_type = $os)";
        }
        $sql = implode($res,'or');
        if (empty($sql)) {
            $sql = 'product_id in ( ' . implode($products, ','). ' )';
        } else {
            $sql = '(' . $sql . ') and product_id in ( ' . implode($products, ','). ' )';
        }
        return $sql;
    }

     
    /**
     * 清除cache 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function clearCache($arrInput){
        if(isset($arrInput['keys'])) {
            $arrInput = $arrInput['keys'];
        }
        $arrInput = array_unique($arrInput);
        if(count($arrInput) == 0) {
            return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS);
        }
        if (self::CACHE_TYPE == self::CACHE_TYPE_REDIS) {
            return Dl_Channel_Redis::removeCache($arrInput); 
        } else {
            return Dl_Channel_Memcache::removeCache($arrInput); 
        }
    }

    /**
     * 获取apcCache 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function getApcCache($arrInput){
        $key = $arrInput['place_id'];
        $res = Dl_Channel_Apc::apcFetch($key);
        return $res;
    }

    /**
     * 获取cache 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function getCache($arrInput){
        $key = $arrInput['place_id'];
        if (self::CACHE_TYPE == self::CACHE_TYPE_REDIS) {
            $res = Dl_Channel_Redis::getCache($key);
        } else {
            $res = Dl_Channel_Memcache::getCache($key);
        }
        if ( !$res && $res['errno'] !=0 ) {
            return false;
        }
        return unserialize($res['data']);
    }
    
    /**
     * 添加cache 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    private static function addApcCache($arrInput) {
        $key = $arrInput['place_id'];
        $value = $arrInput['result'];
        Dl_Channel_Apc::apcStore($key,$value,self::APC_EXPIRE);
    }

    /**
     * 添加cache 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    private static function addCache($arrInput) {
        $key = $arrInput['place_id'];
        $value = serialize($arrInput['result']);
        if (self::CACHE_TYPE == self::CACHE_TYPE_REDIS) {
            Dl_Channel_Redis::addCache($key,$value,self::CACHE_EXPIRE);
        } else {
            Dl_Channel_Memcache::addCache($key,$value,self::CACHE_EXPIRE);
        }
    }    

    /**
     * 获取ssp产品线
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function getSSPProduct($arrInput) {
        $arrDlInput = array(
            'function' => 'getSSPProduct',
        );
        $arrDlOutput = Dl_Channel_Oldplace::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS, $arrDlOutput);
    }
    
    /**
     * 添加ssp产品线
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function addSSPProduct($arrInput) {
        if (empty($arrInput['dictionary_id']) || 
            empty($arrInput['dictionary_name']) ||
            empty($arrInput['dictionary_level']) ||
            empty($arrInput['search_code']) ||
            empty($arrInput['parent_id']) ||
            empty($arrInput['dictionary_value']) || 
            empty($arrInput['dictionary_order']) || 
            empty($arrInput['del_flag']) || 
            empty($arrInput['dictionary_desc']) 
        ) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
        $arrInput['create_time'] = date('Y-m-d H:i:s');
        $arrDlInput = array(
            'function' => 'addSSPProduct',
            'values' => array(
                array(
                    'dictionary_id' => $arrInput['dictionary_id'],
                    'dictionary_name' => $arrInput['dictionary_name'],
                    'dictionary_level' => $arrInput['dictionary_level'],
                    'search_code' => $arrInput['search_code'],
                    'parent_id' => $arrInput['parent_id'],
                    'dictionary_value' => $arrInput['dictionary_value'],
                    'dictionary_order' => $arrInput['dictionary_order'],
                    'del_flag' => $arrInput['del_flag'],
                    'dictionary_desc' => $arrInput['dictionary_desc'],
                    'create_time' => $arrInput['create_time'],
                ),
            ),
        );
        $arrDlOutput = Dl_Channel_Oldplace::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS, "insert success! if something wrong please use delSSPProduct del it! ");
    }    

    /**
     * 添加ssp产品线
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function delSSPProduct($arrInput) {
        if(empty($arrInput['id'])) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'function' => 'delSSPProduct',
            'id' => $arrInput['id'], 
        );
        $arrDlOutput = Dl_Channel_Oldplace::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS, "del success! please use getSSPProduct to check it! ");

    }

    /**
     * 添加ssp产品线
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function updateSSPProduct($arrInput) {
        if(empty($arrInput['name']) & empty($arrInput['id'])) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'function' => 'updateSSPProduct',
            'dictionaryName' => $arrInput['dictionaryName'], 
            'id' => intval($arrInput['id']), 
        );
        $arrDlOutput = Dl_Channel_Oldplace::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS, "update success! please use getSSPProduct to check it! ");

    }


    /**
    * @desc op tools 设置广告位及渠道qps百分比
    * @param arrInput
    * @return arrRes
    */
    public static function setPlaceIdQpsPercent($arrInput) {
        // body...
        $idc = $arrInput['idc'];
        $pid = $arrInput['pid'];
        $percent = intval($arrInput['per']);
        $cpid = isset($arrInput['cpid']) ? $arrInput['cpid'] : 1001; //默认调节明投
        $uname = $arrInput['uname'];
        $token = $arrInput['token']; 
        if (empty($pid) || $percent > 100 || $percent < 0 || empty($uname) || empty($token)) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
        Bingo_Log::warning('param input is:['.serialize($arrInput).']');
        if (false === self::checkOpToken($uname, $token)) {
            return self::_errRet(Service_Channel_Errcode::ERR_OP_ERROR_TOKEN);
        }
        
        $qpsControl = self::QPS_CONTROL_LIMIT; 
        if ($percent == 100) {
            $qpsControl = self::QPS_CONTROL_UNLIMIT;
        }
        $arrDlInput = array(
            'function' => 'setPlaceIdQpsPercent',
            'qps_control' => $qpsControl,
            'qps_percent' => $percent,
            'update_uname' => $uname,
            'update_time' => time(),
            'cid' => $cpid,
            'place_id' => $pid,
        );
        $arrDlOutput = Dl_Channel_Place::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Channel_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS);
    }

    /**
    * @desc 设置token 
    * @param arrInput
    * @return arrRes
    */
    public static function setOpToken($arrInput) {
        // body...
        $uname = $arrInput['uname'];
        $token = $arrInput['token'];
        if (empty($arrInput['uname']) || empty($arrInput['token'])) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
        $redis = new Bingo_Cache_Redis('adsense');
        $arrInput = array(
            'key' => self::OP_TOKEN_KEY,
            'field' => $uname,
            'value' => $token,
        );
        if (!$redis || !$redis->isEnable()) {
            Bingo_Log::warning('redis init filded!');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        $ret = $redis->hset($arrInput);
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS, $ret);
    }

    /**
    * @desc 删除 token 
    * @param arrInput
    * @return arrRes
    */
    public static function delOpToken($arrInput) {
        $uname = $arrInput['uname'];
        if (empty($arrInput['uname'])) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Channel_Errcode::ERR_PARAM_ERROR);
        }
        $redis = new Bingo_Cache_Redis('adsense');
        $arrInput = array(
            'key' => self::OP_TOKEN_KEY,
            'field' => $uname,
        );
        if (!$redis || !$redis->isEnable()) {
            Bingo_Log::warning('redis init filded!');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        $ret = $redis->hdel($arrInput);
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS, $ret);

    }
    
    /**
    * @desc token 列表 
    * @param arrInput
    * @return arrRes
    */
    public static function listOpToken($arrInput) {
        $redis = new Bingo_Cache_Redis('adsense');
        $arrInput = array(
            'key' => self::OP_TOKEN_KEY,
        );
        if (!$redis || !$redis->isEnable()) {
            Bingo_Log::warning('redis init filded!');
            return self::_errRet(Service_Channel_Errcode::ERR_DB_QUERY_FAIL);
        }
        $ret = $redis->hgetall($arrInput);
        return self::_errRet(Service_Channel_Errcode::ERR_SUCCESS, $ret);
    }

    /**
    * @desc 查看是否匹配token 
    * @param uname, token
    * @return boolean
    */
    private static function checkOpToken($uname ,$token) {
        $redis = new Bingo_Cache_Redis('adsense');
        $arrInput = array(
            'key' => self::OP_TOKEN_KEY,
            'field' => $uname,
        );
        if (!$redis || !$redis->isEnable()) {
            Bingo_Log::warning('redis init filded!');
            return false;
        }
        $ret = $redis->hget($arrInput);
        if (false !== $ret && isset($ret['ret']) && $ret['ret'][self::OP_TOKEN_KEY] === $token) {
            return true;
        }
        return false;
    }

    /**
     * 错误信息
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    private static function _errRet($errno, $data = array()) {
        return array(
            'errno' => $errno,
            'errmsg' => Service_Channel_Errcode::getErrmsg($errno),
            'data' => $data,
        );
    }

}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
