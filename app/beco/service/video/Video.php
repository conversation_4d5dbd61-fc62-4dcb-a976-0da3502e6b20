<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file Interface.php
 * <AUTHOR>
 * @date: 2017/04/22 17:00
 * @brief:
 *
 */

class Service_Video_Video {
    static private $_vod = null;

    /**
     * @brief : 接口
     * @param : $arrInput
     * @return : $arrOutput
     **/
    private static function _getVodInstance(){
        if(null == self::$_vod){
            self::$_vod = new Libs_Bce_VodClient();
        }
        return self::$_vod;
    }
    /**
     * @brief
     * @param
     * @return
     */
    public static function uploadVideo() {
        $arrGet = Bingo_Http_Request::getGetNoXssSafe();
        $arrPost = Bingo_Http_Request::getPostNoXssSafe();
        $arrReq = array_merge($arrGet,$arrPost);
        $needParam = array(
            'userId',
        );
        if (!self::_checkRequired($arrReq, $needParam)) {
            return self::_return_with_error_code(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        foreach ($_FILES as $key => $value) {
            if (isset($value['name']) || isset($value['tmp_name'])) {
                $filePath = $value['tmp_name'];
                $title = $value['name'];
            }
        }
        $vod = self::_getVodInstance();
        if ($vod == null) {
            Bingo_Log::warning("input param error,the input is " . serialize($filePath));
            return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $res = $vod->createMediaFromFile($filePath, $title);
        if ($res === false || empty($res['mediaId'])) {
            return self::_return_with_error_code(Tieba_Errcode::ERR_CHUNK_UPLOAD_FAIL);
        }
        $mediaInfo = self::getMedia($res['mediaId']);
        if (empty($mediaInfo['status'])) {
            return self::_return_with_error_code(Tieba_Errcode::ERR_CHUNK_UPLOAD_FAIL);
        }
        $arrInput =  array(
            'userId' => $arrReq['userId'],
            'mediaId' => $res['mediaId'],
            'mediaStatus' => $mediaInfo['status'],
            'mediaExt' => '',
        );
        $arrOut = Service_Idea_Idea::addMedia($arrInput);
        return $arrOut;
    }

    /**
     * @brief
     * @param
     * @return
     */
    public static function getVideoList() {
        $arrGet = Bingo_Http_Request::getGetNoXssSafe();
        $arrPost = Bingo_Http_Request::getPostNoXssSafe();
        $arrReq = array_merge($arrGet,$arrPost);
        $needParam = array(
            'userId',
        );
        if (!self::_checkRequired($arrReq, $needParam)) {
            return self::_return_with_error_code(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrRes = Service_Idea_Idea::getMediaList($arrReq);
        if ($arrRes == false || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            return self::_return_with_error_code(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $mediaInfo = $arrRes['data'];
        $arrOut = array();
        foreach ($mediaInfo as $mediaData) {
            $mediaInfo = self::getMedia($mediaData['media_id']);
            $arrOut[] = array(
                'media_id' => $mediaInfo['media_id'],
                'media_status' => $mediaInfo['status'],
                'media_url' => $mediaInfo['urlList'],
                'thumbnailList' => $mediaInfo['thumbnailList'],
            );
        }
        return self::_return_with_error_code(Tieba_Errcode::ERR_SUCCESS, $arrOut);
    }

    /**
     * @brief
     * @param
     * @return
     */
    public static function getMedia($mediaId) {
        $vod = self::_getVodInstance();
        $res = $vod->getMedia($mediaId);
        if ($res == false || empty($res)) {
            return false;
        }
        return $res;
    }
    /**
     * @brief
     * @param
     * @return
     */
    public static function getMediaInfo($arrInput) {
        return self::getMediaUrl($arrInput['media_id']);
    }
    /**
     * @brief
     * @param
     * @return
     */
    public static function getMediaUrl($mediaId) {
        $vod = self::_getVodInstance();
        $res = $vod->getPlayableUrl($mediaId);
        if ($res == false || empty($res)) {
            return false;
        }
        return $res;
    }

    /**
     * @brief
     * @param
     * @return
     */
    private static function _checkRequired($arrInput, $arrFields)
    {
        foreach ($arrFields as $checkField) {
            if (!isset($arrInput[$checkField])) {
                Bingo_Log::warning('Required Field [' . $checkField . '] Not Find In Input [' . serialize($arrInput) . ']');
                return false;
            }
        }
        return true;
    }
    /**
     * @brief
     * @param
     * @return
     */
    private static function _return_with_error_code($errorCode, $outData = null)
    {
        return array(
            'errno' => $errorCode,
            'errmsg' => Tieba_Error::getErrmsg($errorCode),
            'data' => $outData,
        );
    }
}