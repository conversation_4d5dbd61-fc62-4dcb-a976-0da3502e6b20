<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file VideoFilter.php
 * <AUTHOR>
 * @date 2016.07.18
 * @brief 按请求类型对视频类广告做过滤
 **/

class Service_Libs_VideoFilter {
    const VIDEO_TYPE = 7;
    /**
     * @desc 按输入参数对广告做过滤,如果不是从客户端过来的请求或者客户端版本号低于7.8.0,则过滤视频类广告
     * @param $arrInput : array : 广告请求入参
     * @param $advOrder : array : 广告信息
     * @return bool
     */
    public static function filterOrder($arrInput, $advOrder) {
        $bolRet = false;
        $strClientType = $arrInput['client_type'];
        $strClientVersion = $arrInput['version'];
        $intGoodsStyple = $advOrder['goods_info'][0]['goods_style'];
        if (self::VIDEO_TYPE == $intGoodsStyple) {
            if ('APP' == $strClientType && (Molib_Util_Version::compare('7.8.0', $strClientVersion) < 0)) {
                $bolRet = true;
            }
        }

        return $bolRet;
    }
    
}
?>
