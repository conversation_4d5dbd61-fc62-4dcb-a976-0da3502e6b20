<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Db.php
 * <AUTHOR>
 * @date 2016/02/16 14:28:47
 * @brief 
 *  
 **/
class Service_Libs_Gd_Db {

    const DATABASE_NAME = "forum_beco";
    private static $_transactionFlag = false;   //为了便于元素联动 采用事务标记，其中 0 FALSE; 1 PLAN; 2 UNIT; 3 IDEA;
    /**
     * @param $dbName 库名
     * @return Tieba_Mysql | null
     *
     */
    public  static function getDB($dbName = self::DATABASE_NAME){
        $objTbMysql = Tieba_Mysql::getDB($dbName);
        if ($objTbMysql && $objTbMysql->isConnected()) {
            $objTbMysql->charset('utf8');
            return $objTbMysql;
        } else {
            Bingo_Log::warning("db connect fail.");
            return null;
        }
    }

    /**
     * @param $db
     * @param $flag
     * @desc 开启事务
     */
    public static function startTransaction($db, $flag){
        if(self::$_transactionFlag === false){
            $db->startTransaction();
            self::$_transactionFlag = $flag;
        }
    }

    /**
     * @param $db
     * @param $flag
     * @desc 提交并关闭事务
     */
    public static function commit($db, $flag){
        if(self::$_transactionFlag === $flag){
            $db->commit();
            self::$_transactionFlag = false;
        }
    }

    /**
     * @param $db
     * @param $flag
     * @desc 回滚并关闭事务
     */
    public static function rollback($db, $flag){
        if(self::$_transactionFlag == $flag){
            $db->rollback();
            self::$_transactionFlag = false;
        }
    }
}



