<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file Util.php
 * <AUTHOR>
 * @date: 2017/06/05 15:44
 * @brief:
 *
 */

class Service_Util_Util {
    const VIDEO_DURATION_PREFIX = "afd_video_duration_";
    public static $redis = null;
    /**
     * @brief 视频详情页明投没有视频时长字段，afd提供接口配置时长
     * @param
     * @return
     */
    public static function setVideoDurationToRedis($arrAdvInfo) {
        $arrSetData = array();
        if (is_null(self::$redis)) {
            Service_Libs_Redis::initRedis();
            self::$redis = new Service_Libs_Redis();
        }
        foreach ($arrAdvInfo as $adv) {
            if (!empty($adv['idea_id']) && !empty($adv['duration'])) {
                $arrSetData[] = array(
                    'key' => self::VIDEO_DURATION_PREFIX . $adv['idea_id'],
                    'value' => intval($adv['duration']),
                );
            }
        }
        $res = self::$redis->mset($arrSetData);
        if ($res['err_no'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("set video duration redis data error key =>.".serialize($res));
            return self::_return_with_error_code(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
    }
    /**
     * @brief
     * @param
     * @return
     */
    private static function _return_with_error_code($errorCode, $outData = null)
    {
        return array(
            'errno' => $errorCode,
            'errmsg' => Tieba_Error::getErrmsg($errorCode),
            'data' => $outData,
        );
    }
}