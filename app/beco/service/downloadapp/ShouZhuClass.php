<?php
class Service_Downloadapp_ShouZhuClass {
    public static $shouZhuClass = array(
   	    '其他软件',
        '系统工具',
        '主题壁纸',
        '社交通讯',
        '生活实用',
        '资讯阅读',
        '影音播放',
        '办公学习',
        '拍摄美化',
        '旅游出行',
        '理财购物',
        '其他游戏',
        '休闲益智',
        '角色扮演',
        '动作射击',
        '模拟辅助',
        '体育竞技',
        '赛车竞速',
        '棋牌桌游',
        '经营养成', 
    );
    
    public static $shouZhuClassId = array(
        500 => '其他软件',
        501 => '系统工具',
        502 => '主题壁纸',
        503 => '社交通讯',
        504 => '生活实用',
        505 => '资讯阅读',
        506 => '影音播放',
        507 => '办公学习',
        508 => '拍摄美化',
        509 => '旅游出行',
        510 => '理财购物',
        400 => '其他游戏',
        401 => '休闲益智',
        402 => '角色扮演',
        403 => '动作射击',
        404 => '模拟辅助',
        405 => '体育竞技',
        406 => '赛车竞速',
        407 => '棋牌桌游',
        408 => '经营养成', 
    );

    /**
    * @desc 获取手助APP分类Id 
    * @param arrInput
    * @return arrRes
    */
    public static function getDownLoadId() {
       return self::getDirIdByItera(self::$shouZhuClass); 
    }

    /**
    * @desc 递归获取手助APP分类Id 
    * @param arrInput
    * @return arrRes
    */
    public static function getDirIdByItera($arrInput,$dirPre = '',$dirNamePre = '') {
        $dirId = 0;
        $arrRes = array();
        foreach($arrInput as $key => $value){
            $dirId += 1;
            $strDirId = strval($dirId);
            if(!empty($dirPre)) {
                $strDirId = "$dirPre.$strDirId";
            }
            if(is_array($value)){
                $dirName = $key;
                if(!empty($dirNamePre)) {
                    $dirName = "$dirNamePre:$dirName";
                }
                $children = self::getDirIdByItera($value,$strDirId,$dirName);
            } else {
                $dirName = $value;
                if(!empty($dirNamePre)) {
                    $dirName = "$dirNamePre:$dirName";
                }
                $children = null;
            }
            $arrRes[$strDirId] = $dirName;
            if (!empty($children)) {
                $arrRes = $arrRes + $children;
            }
        }
        return $arrRes;

    }


    /**
    * @desc 获取手助APP分类 
    * @param arrInput
    * @return arrRes
    */
    public static function getDownLoadClass(){
        return self::getClassByItera(self::$shouZhuClass);
    }

    /**
    * @desc 递归获取手助APP分类 
    * @param arrInput
    * @return arrRes
    */
    private static function getClassByItera($arrInput,$dirPre = ''){
        $dirId = 0;
        $arrRes = array();
        foreach($arrInput as $key => $value){
            $dirId += 1;
            $strDirId = strval($dirId);
            if(!empty($dirPre)) {
                $strDirId = "$dirPre.$strDirId";
            }
            if(is_array($value)){
                $dirName = $key;
                $children = self::getClassByItera($value,$strDirId);
            } else {
                $dirName = $value;
                $children = null;
            }
            $arrRes[$dirName] = array(
                'dirId' => $strDirId,
                'dirName' => $dirName,
                'children' => $children,
            );
        }
        return $arrRes;
    }
}

?>
