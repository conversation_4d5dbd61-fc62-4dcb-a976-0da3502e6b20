<?php
/**
 * @copyright Copyright (c)  www.baidu.com
 * <AUTHOR>
 * @date 2017-04-18 10:34
 * @version
 */
require_once(dirname(__FILE__) . "/../../third-party/Hessian/HessianClient.php");
class Service_Downloadapp_Downloadapp {

    const SPLIT_CHAR = ':';
    const OFFLINE_SVC = "http://cas-off.baidu.com:8008/services/"; // offline
    const ONLINE_SVC = "http://10.42.3.152:8008/services/"; // online
    const IS_ONLINE = true;
    const SVC_SERVICE_UCID2DEVID = 'PassService';
    const SVC_SERVICE_UNAME2UCID = 'AcctService';

    const SOURCE_RTB_TYPE = 1;
    const SOURCE_GD_TYPE = 2;
    const WHITE_LIST_TYPE = 1;
    const BLACK_LIST_TYPE = 2;

    const EVENT_TYPE_ON = 0;
    const EVENT_TYPE_OFF = 1;
    const EVENT_TYPE_NOTICE = 2;
    
    const APC_EXPIRE = 14400;

    const ANDOROID_CAN_DOWNLOAD = 0;
    const ANDOROID_IN_BLACKLIST = 1;
    const ANDOROID_DOCID_NOT_EXIST  = 2;
    
    private static $_key_cn = array(
        'id' => '编号',
        'app_name' => '应用名',
        'app_pack' => '应用包名',
        'app_store_url' => 'AppStore地址',
        'update_uname' => '操作人',
        'update_time' => '操作时间',
    );

    /**
    * @desc 获取下载类白名单 
    * @param arrInput
    * @return arrRes
    */
    public static function getDownLoadClassWhiteList($arrInput) {
        // body...
        $shouZhuClass = Service_Downloadapp_ShouZhuClass::getDownLoadClass();
        $arrDlInput = array(
            'function' => 'selectAppClass',
        );

        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
        }
        $selectedClass = $arrDlOutput['results'][0];
        $selectedClassId = array();
        foreach ($selectedClass as $value) {
            $arrClass = explode(self::SPLIT_CHAR,$value['class_name']);
            $tempClass = $shouZhuClass;
            foreach ($arrClass as $class) {
                if (isset($tempClass[$class])) {
                    $tempClass = $tempClass[$class];
                } else {
                    $tempClass = $tempClass['children'][$class];
                } 
            }
            $dirId = $tempClass['dirId'];
            if(!empty($dirId)) {
                $selectedClassId[] = $dirId;
            }
        }

        $shouZhuClass = self::removeKeysByItera($shouZhuClass);
        return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS,array('allClass' => $shouZhuClass,'selectedClass' => $selectedClassId));
    }

    
    /**
    * @desc this is a function to remove key by itera
    * @param arrInput
    * @return arrRes
     */
    private static function removeKeysByItera($arrInput) {
        // body...
        $arrRes = array();
        foreach ($arrInput as $value) {
            if(is_array($value['children'])) {
                $value['children'] = self::removeKeysByItera($value['children']);
            }
            $arrRes[] = $value;
        }
        return $arrRes;
    }

    /**
    * @desc 保存下载分类白名单 
    * @param arrInput
    * @return arrRes
    */
    public static function saveDownLoadClassWhiteList($arrInput) {
        $opName = $arrInput['update_uname'];
        $selectedClass = $arrInput['selectedClass'];
        if (empty($opName) && !is_array($selectedClass)) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_PARAM_ERROR);
        }
        if ( true !== self::delDownLoadClassWhiteList() ) {
            return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
        }
        $shouZhuClass = Service_Downloadapp_ShouZhuClass::getDownLoadId();
        $arrDlInput = array(
            'function' => 'insertIntoAppClass',
            'values' => array(),
        );

        $nowTime = time();
        foreach ($selectedClass as $id) {
            if(isset($shouZhuClass[$id])){
                $arrDlInput['values'][] = array(
                    'class_name' => $shouZhuClass[$id],
                    'update_time' => $nowTime,
                    'update_uname' => $opName,
                );
            }
        }
        if (!empty($arrDlInput['values'])) {
            $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
            if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
                return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
            }
        }
        return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS);

    }

    /**
    * @desc 删除下载分类白名单,这里没有做容错,因为保存失败后,用户肯定会在点击保存的 
    * @param arrInput
    * @return arrRes
    */
    private static function delDownLoadClassWhiteList() {
        $arrDlInput = array(
            'function' => 'delAppClass',
        );
        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return false;
        }
        return true;
         
    }

    /**
    * @desc 保存应用白名单,程序中验证app_pack,app_store_url不能重复 
    * @param arrInput
    * @return arrRes
    */
    public static function saveDownLoadAppBlackList($arrInput) {
        $appPack = $arrInput['app_pack'];
        $appName = $arrInput['app_name'];
        $appStoreUrl = $arrInput['app_store_url']; 
        $opName = $arrInput['update_uname'];
        if(empty($opName) || empty($appName) || (empty($appPack) && empty($appStoreUrl))) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_PARAM_ERROR);
        }
        if (!empty($appPack)) {
            $arrDlInput = array(
                'function' => 'getAppByAppPackAndStoreUrl',
                'app_pack' => $appPack,
                'app_store_url' => $appStoreUrl,
            );
            if(!self::checkUniqApp($arrDlInput)) {
                Bingo_Log::warning('appPack repeat error app_Pack is:['.$appPack.']');
                return self::_errRet(Service_Downloadapp_Errcode::ERR_APP_PACK_REPEAT);
            }
        }
        $arrDlInput = array(
            'function' => 'insertIntoApp',
            'values' => array(
                array(
                    'app_name' => $appName,
                    'app_pack' => $appPack,
                    'app_store_url' => $appStoreUrl,
                    'update_time' => time(),
                    'update_uname' => $opName,
                )
            ),
        );
        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS);

    }

    /**
    * @desc 更新下载类APP白名单 
    * @param arrInput
    * @return arrRes
    */
    public static function updateDownLoadAppBlackList($arrInput) {
        $id = intval($arrInput['id']);
        $appPack = $arrInput['app_pack'];
        $appName = $arrInput['app_name'];
        $appStoreUrl = $arrInput['app_store_url']; 
        $opName = $arrInput['update_uname'];
        if( empty($id) || empty($opName) || empty($appName) || (empty($appPack) && empty($appStoreUrl))) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_PARAM_ERROR);
        }
        if (!empty($appPack)) {
            $arrDlInput = array(
                'function' => 'getAppByAppPackAndStoreUrlAndId',
                'app_pack' => $appPack,
                'app_store_url' => $appStoreUrl,
                'id' => $id,
            );
            if(!self::checkUniqApp($arrDlInput)) {
                Bingo_Log::warning('appPack repeat error app_Pack is:['.$appPack.']');
                return self::_errRet(Service_Downloadapp_Errcode::ERR_APP_PACK_REPEAT);
            }
        }
        $arrDlInput = array(
            'function' => 'updateApp',
            'app_name' => $appName,
            'app_pack' => $appPack,
            'app_store_url' => $appStoreUrl,
            'update_time' => time(),
            'update_uname' => $opName,
            'id' => $id,
        );
        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS);


    }

    /**
     * @desc 检验是否是唯一的APP 
     * @param arrInput
     * @return arrRes
     */
    private static function checkUniqApp($arrDlInput){
        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return false;
        }
        if(!empty($arrDlOutput['results'][0])) {
            return false;
        } 
        return true;
    }

    /**
     * @desc 删除下载应用白名单 
     * @param arrInput
     * @return arrRes
     */
    public static function delDownLoadAppBlackList($arrInput){
        $opName = $arrInput['update_uname'];
        $ids = $arrInput['ids'];
        if(empty($opName) || empty($ids)){
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'function' => 'delAppByIds',
            'ids' => implode(',',$ids),
        );
        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS);
   }

    /**
     * @desc 获取下载类白名单 
     * @param arrInput
     * @return arrRes
     */
    public static function getDownLoadAppBlackList($arrInput) {
        // body...
        $condition = '';
        $query = $arrInput['query'];
        $pn = intval($arrInput['pn']);
        $rn = intval($arrInput['rn']);
        if (!empty($query)) {
            $condition = "where app_name like '%" . $query ."%'";  
        }
        $condition = $condition . ' order by update_time desc';  
        if (!empty($pn) && !empty($rn)) {
            $pnSql = $pn-1;
            $condition = $condition .' limit ' . strval($pnSql*$rn) . ',' . strval($rn);
        }
        $arrDlInput = array(
            'function' => 'selectApp',
            'condition' => $condition,
        );
        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
        }
        $totalCount = self::getDownloadAppTotalCount($query);
        if (false === $totalCount) {
            return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrRes = array(
            'appList' => $arrDlOutput['results'][0],
            'pn' => $pn,
            'totalCount' => intval($totalCount),
        );
        return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS,$arrRes);
    }

    /**
     * @desc 获取下载应用白名单总数 
     * @param arrInput
     * @return arrRes
     */
    private static function getDownloadAppTotalCount($query) {
        $arrDlInput = array(
            'function' => 'getAppTotalCount',
            'query' => "%$query%",
        );
        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return false; 
        }
        return $arrDlOutput['results'][0][0]['count(*)'];
    }


    /**
     * @desc 下载下载类应用白名单 
     * @param arrInput
     * @return arrRes
     */
	public static function downloadAppBlackList($arrInput){
        $arrDlInput = array(
            'function' => 'selectApp',
            'condition' => '',
        );
        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
        }
        self::_exportToExcel($arrDlOutput['results'][0],'应用白名单列表');
	}

	/**
     * @desc dump data into excel file
     * @param $arrData : array : data to be dumped
     * @param $strFileName : string : excel file name
     * @return
     */
    private static function _exportToExcel($arrData, $strFileName){
        require_once('../../util/Excel_XML.class.php');
        $xlsFile = new Excel_XML();

        if (!empty($arrData)) {
            $arrDataTitle = array();
            $arrDatakey = array_keys($arrData[0]);

            foreach($arrDatakey as $key ){
                $arrDataTitle[$key] = isset(self::$_key_cn[$key]) ? self::$_key_cn[$key] : $key;
            }
            $arrDataTitle = Bingo_Encode::convert($arrDataTitle, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
            $arrData = Bingo_Encode::convert($arrData, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);

            array_unshift($arrData, $arrDataTitle);
        }

        $xlsFile->addArray($arrData);
        $xlsFile->generateXML($strFileName);
        exit();
    }

    /**
     * @desc 接受手助推送应用上下线信息 
     * @param arrInput
     * @return arrRes
     */
    public static function shouZhuReceiver($arrInput) {
        $event = $arrInput['event'];
        $arrApp = json_decode($arrInput['app'],true);
        $developerId = $arrApp['developerid'];
        $appId = $arrApp['app_id'];
        $docid = $arrApp['docid'];
        $name = $arrApp['name'];
        $pushTime = $arrApp['update_time'];
        $packageName = $arrApp['package_name'];
        $catId= $arrApp['cat_id'];
        $source = $arrApp['source'];
        $version = $arrApp['version'];
        $version_id = $arrApp['version_id'];
        if ( null === $event       ||
             empty($developerId) ||
             empty($appId)       ||
             empty($docid)       ||
             empty($name)        ||
             empty($pushTime)    ||
             empty($packageName) ||
             empty($catId)       ||
             empty($version)     ||
             empty($version_id)  ||
             empty($source)
        ){
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_PARAM_ERROR);
        }

        // 匹配yuangshengjingjia 01 ~ 200
        $regSourceRtb = '/http:\/\/yuanshengjingjia(0[1-9]|1\d{2}|[1-9]\d?|200).baidu.com/';
        // 匹配yuangshengheyue 01 ~ 200
        $regSourceGd = '/http:\/\/yuanshengheyue(0[1-9]|1\d{2}|[1-9]\d?|200).baidu.com/';
        
        if( 1 === preg_match($regSourceRtb, $source) ) {
            $type = self::SOURCE_RTB_TYPE;
        } else if ( 1 === preg_match($regSourceGd, $source) ) {
            $type = self::SOURCE_GD_TYPE;
        } else {
            Bingo_Log::warning('invalid source, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_SHOUZHU_SOURCE);
        }
        $arrDlInput = array(
            'function' => 'insertShouZhuAppList',
            'values' => array(
                array(
                   'event_type' => intval($event), 
                   'developer_id' => intval($developerId), 
                   'app_id' => intval($appId), 
                   'docid' => intval($docid), 
                   'app_name' => $name, 
                   'push_time' => intval($pushTime), 
                   'update_time' => time(), 
                   'package_name' => $packageName, 
                   'cat_id' => intval($catId), 
                   'source_type' => $type, 
                   'version' => $version, 
                   'version_id' => intval($version_id), 
                ),
            ),
        );
        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS);
        
    }

    /**
     * @desc 更新应用docid 
     * @param arrInput
     * @return arrRes
     */
    public static function updateAppDocid($arrInput) {
        $appId = intval($arrInput['app_id']);
        $sourceType= intval($arrInput['source_type']);
        if ($appId <= 0 || $sourceType <= 0) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_PARAM_ERROR);
        }
        $docid = self::getLastDocid($appId,$sourceType);
        if(false === $docid) {
            return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
        }
        if(null === $docid) {
            return self::_errRet(Service_Downloadapp_Errcode::ERR_UPDATE_DOCID);
        }
        $arrRes = array(
            'docid' => $docid,
            'url' => self::genLandpageUrl($docid),
            'download_url' => self::genDownloadUrl($docid),
        );
        return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS,array('docid' => $docid));
       
    }

    /**
     * @desc 检查是否需要更新docid 
     * @param arrInput
     * @return arrRes
     */
    public static function checkNeedUpdateApp($arrInput) {
        $appId = intval($arrInput['app_id']);
        $sourceType= intval($arrInput['source_type']);
        $docid = intval($arrInput['docid']);
        if ($appId <= 0 || $sourceType <= 0 || $docid <= 0) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_PARAM_ERROR);
        }
        $lastDocid = self::getLastDocid($appId,$sourceType);
        if(false === $lastDocid) {
            return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
        }
        if(null === $lastDocid) {
            return self::_errRet(Service_Downloadapp_Errcode::ERR_UPDATE_DOCID);
        }
        $arrRes = array(
            'need_update' => true,
        );
        if ($docid === $lastDocid) {
            $arrRes['need_update'] = false; 
        } 
        return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS,$arrRes);

    }

    /**
     * @desc 获取最新的docid 
     * @param arrInput
     * @return arrRes
     */
    private static function getLastDocid($appId,$sourceType) {
        // body...
        $arrDlInput = array(
            'function' => 'getLastDocid',
            'app_id' => $appId,
            'source_type' => $sourceType,
        );
        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return false;
        }
        return $arrDlOutput['results'][0][0]['docid'];
    }

    /**
     * @desc 获取详情页地址 
     * @param arrInput
     * @return arrRes
     */
    private static function genLandpageUrl($docid) {
        return 'https://mobile.baidu.com/item?docid='.$docid;
    }

    /**
     * @desc 获取下载页地址 
     * @param arrInput
     * @return arrRes
     */
    private static function genDownloadUrl($docid) {
        return 'http://cover.baidu.com/cover/deeplink_android?downloadUrl='.$docid;
    
    }
    
    /**
     * @desc 获取下载页地址 
     * @param arrInput
     * @return arrRes
     */
    private static function getCategoryById($id) {
        // body...
        return Service_Downloadapp_ShouZhuClass::$shouZhuClassId[intval($id)];
    }

    /**
     * @desc 检查appstoreid是否在白名单 
     * @param arrInput
     * @return arrRes
     */
    public static function checkValidAppStoreId($arrInput) {
        $appStoreId = $arrInput['app_store_id']; 
        if (empty($appStoreId)) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'function' => 'checkValidAppStoreId',
            'app_store_url' => $appStoreId,
        );
        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrRes = array(
            'is_valid' => empty($arrDlOutput['results'][0][0]),
        );
        return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS,$arrRes);
 
    }

    /**
     * @desc 获取APP动态下载url 
     * @param arrInput
     * @return arrRes
     */
    public static function getAppDownloadUrl($arrInput) {
        $docid = intval($arrInput['docid']);
        if (empty($docid)) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_PARAM_ERROR);
        }
        Bingo_Log::pushNotice('docid', $docid);
        $apcKey = self::getApcKey($docid);
        $apcValue = self::getApcCache($apcKey);
        if(!empty($apcValue)){
            Bingo_Log::pushNotice('hit_apc',1);
            $res = array(
                'download_url' => $apcValue,
            );
            return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS,$res);
        }
        $arrParams = array(
            'docid' => $docid,
            'action' => 'search',
            'from' => '1020184a',
            'token' => 'ystgsy',
            'type' => 'app',
        );
        $url = 'm.baidu.com/api?' . http_build_query($arrParams);
        Bingo_Timer::start("fetchUrl");
        $res = self::fetchUrlGet($url,2000,100);
        Bingo_Timer::end("fetchUrl");
        if( $res === false ) { 
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_PARAM_ERROR);
        }
        $xmlparser = xml_parser_create();
        xml_parse_into_struct($xmlparser,$res,$values);
        xml_parser_free($xmlparser);
        foreach ($values as $value) {
            if($value['tag'] == 'DOWNLOAD_URL') {
                $res = array(
                    'download_url' => $value['value']
                );
                Bingo_Log::pushNotice('url', $value['value']);
                self::setApcCache($apcKey,$value['value']);
                return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS,$res);
            }
        }
        return self::_errRet(Service_Downloadapp_Errcode::ERR_NO_DOWNLOAD_URL);
    }

    /**
     * 获取Apc key名
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    private static function getApcKey($docid){
        return 'download_docid_'.$docid;   
    }

    /**
     * 获取apcCache 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    private static function getApcCache($key){
        $res = Dl_Channel_Apc::apcFetch($key);
        return $res;
    }
    
    /**
     * 获取apcCache 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    private static function setApcCache($key,$value){
        Dl_Channel_Apc::apcStore($key,$value,self::APC_EXPIRE);
    }



    /**
     * @desc 用Orp_Fetch获取外网接口
     * @param $notify_url
     * @return  $arr
     */
    private static function fetchUrlGet($url, $intReadTimeOut=100, $intConnTimeOut=50 ) {
        $httpproxy = Orp_FetchUrl::getInstance(array('timeout' =>$intReadTimeOut,'conn_timeout' =>$intConnTimeOut, 'max_response_size' => 1024000,'conn_retry' => 0));
        $res = $httpproxy->get($url);
        $err = $httpproxy->errmsg();
        $http_code = $httpproxy->http_code();
        if ($err) {
            Bingo_Log::warning('fetchurlget:'.$url.' error! error:'.$err);
            return false;
        } else {
            if( $http_code == 200 ){
                return $res;
            }
        }
        return false;
    }

    /**
     * @desc 根据uc帐号获取应用列表 
     * @param arrInput
     * @return arrRes
     */
    public static function getUcApplist($arrInput) {
        // body...
        $ucId = intval($arrInput['uc_id']); 
        $ucName = $arrInput['uc_name']; 
        $sourceType = intval($arrInput['source_type']);
        if ((empty($ucId) && empty($ucName))|| empty($sourceType) ) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_PARAM_ERROR);
        }
        $devId = empty($ucId) ? self::getDevIdByUcname($ucName) : self::getDevIdByUcid($ucId);

        $res = array('applist' => array());
        if ( false === $devId ) {
            Bingo_Log::warning('get devId failed , input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_UCID_NOT_MATCH_DEVID,$res);
        }
        $arrDlInput = array(
            'function' => 'getApplistByDevid',
            'source_type' => $sourceType,
            'developer_id' => $devId,
        );
        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrDlRes = $arrDlOutput['results'][0];
        $filter = array();
        foreach ($arrDlRes as $value) {
            $filter[] = array(
                'app_id' => $value['app_id'],
                'docid' => $value['docid'],
                'app_name' => $value['app_name'],
                'app_pack' => $value['package_name'],
                'category' => self::getCategoryById($value['cat_id']),
                'url' => self::genLandpageUrl($value['docid']),
                'download_url' => self::genDownloadUrl($value['docid']),
                'version' => $value['version'],
                'version_id' => $value['version_id'],
                'is_white_list' => false,
            );
        }
        if(!empty($filter)) {
            $appList = self::checkAndroidAppInWhiteList($filter);
            //$appFilter = self::checkAndroidAppInWhiteList($filter);
            // 不走类过滤
            //$classFilter = self::checkAndroidClassInWhiteList($appFilter['black_list']);
            //$appList = array();
            //$appList['white_list'] = array_merge($appFilter['white_list'], $classFilter['white_list']);
            //$appList['black_list'] = $classFilter['black_list'];
        }
        $listType = intval($arrInput['list_type']);
        $appIds = $arrInput['app_ids'];
        //cpc特殊需求app_id过滤,黑白明单仅返回一种类型
        if(!empty($listType) || !empty($appIds)) {
            if(!empty($appIds) ) {
                $arrTemp = array();
                foreach ($appList['white_list'] as $value) {
                    if(in_array(strval($value['app_id']), $appIds)) {
                        $arrTemp[] = $value;
                    }    
                }
                $appList['white_list'] = $arrTemp;
                $arrTemp = array();
                foreach ($appList['black_list'] as $value) {
                    if(in_array(strval($value['app_id']), $appIds)) {
                        $arrTemp[] = $value;
                    }
                }
                $appList['black_list'] = $arrTemp;

            }
            if ($listType == self::WHITE_LIST_TYPE) {
                $appList['black_list'] = array();
            } else if($listType == self::BLACK_LIST_TYPE) {
                $appList['white_list'] = array();
            }
        }
        $appListMerge = array_merge($appList['white_list'], $appList['black_list']);
        $res['applist'] = $appListMerge; 
        return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS,$res);
    }
    
    /**
     * @desc 检测是否在安卓应用白名单 
     * @param arrInput
     * @return arrRes
     */
    private static function checkAndroidAppInWhiteList($arrInput) {
        $appPacks = array();
        foreach ($arrInput as $value) {
            $arrPacks[] = "'".$value['app_pack']."'";
        }
        $arrDlInput = array(
            'function' => 'checkAndroidAppInWhiteList',
            'condition' => implode(',',$arrPacks),
        );
        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        $arrOutput = array(
            'white_list' => array(),
            'black_list' => array(),
        );
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            $arrOutput['black_list'] = $arrInput;
            return $arrOutput;
        }
        $arrRes = $arrDlOutput['results'][0];
        $arrPacks = array();
        foreach($arrRes as $value){
            $arrPacks[] = $value['app_pack'];
        }
        foreach ($arrInput as &$value) {
            if (!in_array($value['app_pack'],$arrPacks)) {
                $value['is_white_list'] = true;
                $arrOutput['white_list'][] = $value;
            } else {
                $arrOutput['black_list'][] = $value;
            }
        }
        return $arrOutput;        
    }

    /**
     * @desc 检测是否在分类白名单 
     * @param arrInput
     * @return arrRes
     */
    private static function checkAndroidClassInWhiteList($arrInput) {
        $arrDlInput = array(
            'function' => 'selectAppClass',
        );
        $arrOutput = array(
            'white_list' => array(),
            'black_list' => array(),
        );
        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            $arrOutput['black_list'] = $arrInput;
            return $arrOutput;
        }
        $arrRes = $arrDlOutput['results'][0];
        $arrPacks = array();
        foreach($arrRes as $value){
            $arrPacks[] = $value['class_name'];
        }
        foreach ($arrInput as &$value) {
            if (in_array($value['category'],$arrPacks)) {
                $value['is_white_list'] = true;
                $arrOutput['white_list'][] = $value;
            } else {
                $arrOutput['black_list'][] = $value;
            }
        }
        return $arrOutput;
    }

    /**
     * @desc 获取svchost
     * @param arrInput
     * @return arrRes
     */
    private static function getSvcHost($service) {
        // body...
        $host = self::IS_ONLINE ? self::ONLINE_SVC : self::OFFLINE_SVC;
        return $host . $service .'.php';
    }

    /**
     * @desc 通过uc用户名获取开发者帐号 
     * @param arrInput
     * @return arrRes
     */
    private static function getDevIdByUcname($uc_name) {
        // body...
        try {
            $host = self::getSvcHost(self::SVC_SERVICE_UNAME2UCID);
            $proxy = new HessianClient($host); // offline
            $obj = $proxy->getIdByName($uc_name); // 如果只是推广的   那么第二个参数为3  第三个参数为6 @liqinglong
        } catch (Exception $e) {
            Bingo_Log::warning('call svc getPsidByUcid error :'.serialize($e));
            return false;
        }
        $uc_id = intval($obj);
        if(!empty($uc_id)) {
            return self::getDevIdByUcid($uc_id);
        }
        return false;
        // body...

    }

    /**
     * @desc 通过uc帐号获取开发者帐号 
     * @param arrInput
     * @return arrRes
     */
    private static function getDevIdByUcid($ucid) {
        // body...
        try {
            $host = self::getSvcHost(self::SVC_SERVICE_UCID2DEVID);
            $proxy = new HessianClient($host); // offline
            $obj = $proxy->getPsidByUcid($ucid,0,8); // 如果只是推广的   那么第二个参数为3  第三个参数为6 @liqinglong
        } catch (Exception $e) {
            Bingo_Log::warning('call svc getPsidByUcid error :'.serialize($e));
            return false;
        }
        if ($obj['errno'] == Service_Downloadapp_Errcode::ERR_SUCCESS && !empty($obj['result'])) {
            return $obj['result']['psid'];   
        }
        return false;
    }

    /**
     * @desc 获取SvcService的magic方法 
     * @param arrInput
     * @return arrRes
     */
    public static function getMagicSvcService($arrInput) {
        $method = $arrInput['api'];
        $data = $arrInput['data'];
        $service = $arrInput['service'];
        try {
            $host = self::getSvcHost($service);
            $proxy = new HessianClient($host);
            $obj = call_user_func_array(array($proxy, $method),$data); 
        } catch (Exception $e) {
            Bingo_Log::warning('call svc '. $method .'  error :'.serialize($e));
        	$obj = $e;
            return self::_errRet(Service_Downloadapp_Errcode::ERR_EXCEPTION,$obj);
		}
        return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS,$obj);
        
    }

    /**
     * @desc 获取已下线应用 
     * @param arrInput
     * @return arrRes
     */
    public static function getAppidByEventType($arrInput) {
        $sourceType = intval($arrInput['source_type']);
        $eventType = intval($arrInput['event_type']);
        $startTime = intval($arrInput['start_time']);
        if(empty($sourceType)) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'function' => 'getAppidByEventType',
            'source_type' => $sourceType,
            'event_type' => $eventType,
            'update_time' => $startTime,
        );

        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrAppid = array();

        foreach ($arrDlOutput['results'][0] as $key => $value) {
            $arrAppInfo = array(
                'app_id' => $value['app_id'],
                'docid'  => $value['docid'],
                'app_name' => $value['app_name'],
                'package_name' => $value['package_name'],
                'update_time' => $value['update_time'],
            );
            $developerId = $value['developer_id'];
            $ucList = self::getMagicSvcService(array(
                'api' => 'getAllUcidByPsid',
                'data' => array($developerId,0,8),
                'service' => 'PassService',
            ));
            if($ucList['errno'] == Service_Downloadapp_Errcode::ERR_SUCCESS && !empty($ucList['data']['result']['ucid'])){
                $ucList = $ucList['data']['result']['ucid'];
                foreach($ucList as $ucid){
                    $arrAppInfo['uc_list'][] = $ucid['ucid'];
                }
            }
            $arrAppid[] = $arrAppInfo;
            Bingo_Log::pushNotice('developerId_'.$key,$developerId);
        }
        return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS,array('appid_list' => $arrAppid));
    }

    /**
     * @desc 根据appid设置event_type
     * @param arrInput
     * @return arrRes
     */
    public static function setEventTypeByAppid($arrInput) {
        $sourceType = $arrInput['source_type'];
        $appidList = $arrInput['appid_list'];
        $docidList = $arrInput['docid_list'];
        $eventType = isset($arrInput['event_type']) ? intval($arrInput['event_type']) : self::EVENT_TYPE_NOTICE;
        if(empty($sourceType) || (!is_array($appidList) && !is_array($docidList))) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_PARAM_ERROR);
        }
        $appidList[] = -1;
        $docidList[] = -1;
        foreach($appidList as &$value){
            $value = intval($value);
        }
        foreach($docidList as &$value){
            $value = intval($value);
        }
        $arrDlInput = array(
            'function' => 'setEventTypeByAppid',
            'source_type' => $sourceType,
            'event_type' => $eventType,
            'appid_list' => implode(',',$appidList),
            'docid_list' => implode(',',$docidList),
        );

        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS);

    }

    /**
     * @desc 校验安卓是否在白名单是否可下载
     * @param arrInput
     * @return arrRes
     */
    public static function checkValidAndroidApp($arrInput) {
        $docid = intval($arrInput['docid']);
        $appPack = $arrInput['app_pack'];
        $sourceType = intval($arrInput['source_type']);
        if(empty($docid) || empty($appPack) || empty($sourceType)) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_PARAM_ERROR);
        }
        $arrDlInput = array(
            'function' => 'getAppidByDocid',
            'source_type' => $sourceType,
            'docid' => $docid,
            'package_name' => $appPack,
        );

        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
        }
        $res = array(
            'status' => self::ANDOROID_CAN_DOWNLOAD,
        );
        if(empty($arrDlOutput['results'][0])) {
            $res['status'] = self::ANDOROID_DOCID_NOT_EXIST;
            Bingo_Log::pushNotice("status",self::ANDOROID_DOCID_NOT_EXIST);
            return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS,$res);
        }
        $arrDlInput = array(
            'function' => 'getAppByAppPack',
            'app_pack' => $appPack,
        );

        $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
        if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
        }
        if(!empty($arrDlOutput['results'][0])) {
            $res['status'] = self::ANDOROID_IN_BLACKLIST;
        }
        Bingo_Log::pushNotice("status",$res['status']);
        return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS,$res);

    }

    /**
     * @desc 批量校验是否在黑名单
     * @param arrInput
     * @return arrRes
     */
    public static function batchCheckAppBlackList($arrInput) {
        // body...
        $arrAppPacks = $arrInput['app_packs'];
        $arrAppStoreIds = $arrInput['app_store_id'];
        $arrAppPacks = is_array($arrAppPacks) ? $arrAppPacks : array();
        $arrAppStoreIds = is_array($arrAppStoreIds) ? $arrAppStoreIds : array();
        if (empty($arrAppPacks) && empty($arrAppStoreIds)) {
            Bingo_Log::warning('param error, input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_PARAM_ERROR);
        }
        if (count($arrAppPacks) >= 200 || count($arrAppStoreIds) >= 200) {
            Bingo_Log::warning('param is too large! input:['.serialize($arrInput).']');
            return self::_errRet(Service_Downloadapp_Errcode::ERR_PARAM_TOO_LARGE);
        }
        $checkListSql = '';
        foreach ($arrAppPacks as $value) {
            $value = mysql_escape_string($value);
            $arrStrAppPacks[] = '"' . $value . '"';
        }
        foreach ($arrAppStoreIds as $value) {
            $value = mysql_escape_string($value);
            $arrStrAppStoreIds[] = '"' . $value . '"';
        }
        $arrBlackPacks = array();
        $arrBlackStoreIds = array();
        if (!empty($arrStrAppPacks)) {
            $checkListSql =  ' app_pack in ( ' . implode(',', $arrStrAppPacks) . ' ) ';
            $arrDlInput = array(
                'function' => 'batchCheckAppBlackList',
                'check_list' => $checkListSql,
            );

            $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
            if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
                return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
            }
            foreach ($arrDlOutput['results'][0] as $value) {
                $arrBlackPacks[] = $value['app_pack'];
            }
        }

        if (!empty($arrStrAppStoreIds)) {
            $checkListSql = ' app_store_url in ( ' . implode(',', $arrStrAppStoreIds) . ' ) '; 
            $arrDlInput = array(
                'function' => 'batchCheckAppBlackList',
                'check_list' => $checkListSql,
            );

            $arrDlOutput = Dl_Downloadapp_Downloadapp::execSql($arrDlInput);
            if (!$arrDlOutput || $arrDlOutput['errno'] !== Service_Downloadapp_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call db fail, input:['.serialize($arrDlInput).'],output:['.serialize($arrDlOutput).']');
                return self::_errRet(Service_Downloadapp_Errcode::ERR_DB_QUERY_FAIL);
            }
            foreach ($arrDlOutput['results'][0] as $value) {
                $arrBlackStoreIds[] = $value['app_store_url'];
            }
        }
        $arrBlackPacks = array_unique($arrBlackPacks);
        $arrBlackStoreIds = array_unique($arrBlackStoreIds);
        return self::_errRet(Service_Downloadapp_Errcode::ERR_SUCCESS, array(
            'black_list' => array(
                'app_packs' => $arrBlackPacks,
                'app_store_ids' => $arrBlackStoreIds,
            ),
        ));
    }

    /**
     * 错误信息
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    private static function _errRet($errno, $data = array()) {
        return array(
            'errno' => $errno,
            'errmsg' => Service_Downloadapp_Errcode::getErrmsg($errno),
            'data' => $data,
        );
    }


}
?>
