<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR> 
 * @date  2017-04-06
 * @version 1.0
*/

class Service_Downloadapp_Errcode{
    const ERR_SUCCESS = 0;
    const ERR_PARAM_ERROR = 1;
    const ERR_DB_QUERY_FAIL = 2;
    const ERR_APP_PACK_REPEAT = 3;
    const ERR_APP_STORE_URL_REPEAT = 4;
    const ERR_SHOUZHU_SOURCE = 5;
    const ERR_UPDATE_DOCID = 6;
    const ERR_CALL_SHOUZHU = 7;
    const ERR_NO_DOWNLOAD_URL = 8;
    const ERR_UCID_NOT_MATCH_DEVID = 9;
    const ERR_EXCEPTION = 10;
    const ERR_PARAM_TOO_LARGE= 11;
    
    public static $codes = array(
        self::ERR_SUCCESS => "成功!", 
        self::ERR_PARAM_ERROR => "参数错误!", 
        self::ERR_DB_QUERY_FAIL => "数据库调用失败!", 
        self::ERR_APP_PACK_REPEAT => "包名重复或AppStore地址重复!", 
        self::ERR_APP_STORE_URL_REPEAT => "AppStore地址重复!", 
        self::ERR_SHOUZHU_SOURCE => "非法的Source!", 
        self::ERR_UPDATE_DOCID => "无法更新docid,应用或已下线!", 
        self::ERR_CALL_SHOUZHU => "手助接口调用失败!", 
        self::ERR_NO_DOWNLOAD_URL => "没有获取到下载连接!", 
        self::ERR_UCID_NOT_MATCH_DEVID => "ucid 获取不到关联的 devid!", 
        self::ERR_PARAM_TOO_LARGE => "参数过大，请少于200条提交!", 
    );

    /**
     * 获取获取错误信息 
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    public static function getErrmsg($errno){
        $errmsg = self::$codes[$errno];
        if($errmsg == null){
            Bingo_Log::error("errno not found! err_info : errno[$errno].");
            $errmsg = "errno [$errno] not found.";
        }
        return $errmsg;
    }
}
