<?php

/**
 * @name Service_Exp_Exp
 * @desc 实验id配置
 * @date 2017-03-16 14:02
 * <AUTHOR>
 */
class Service_Exp_Exp {

    const REDIS_KEY = 'exp_id_conf';

    /**
     * 添加实验id
     * 
     * @param array $arrInput 请求参数
     * @return array
     */
    public function addExpId($arrInput) {

        // 获取请求参数
        $expId  = strval(trim($arrInput['id']));
        $opName = strval(trim($arrInput['opName']));

        // 验证参数
        if (empty($expId) || empty($opName)) {
            Bingo_Log::warning('invalid input[' . serialize($arrInput) . ']');
            return self::_arrRet(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
        }

        // 获取redis中的配置
        $objRedis = new Service_Libs_Redis();
        $ret      = $objRedis->get(self::REDIS_KEY);

        if ($ret['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("get exp id from redis fail, errno[{$ret['err_no']}], errmsg[{$ret['err_msg']}], key[" . self::REDIS_KEY . ']');
            return self::_arrRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL, 'get data failed');
        }

        // id不在当前列表中，则更新
        $expIdList = empty($ret['ret'][self::REDIS_KEY]) ? array() : json_decode($ret['ret'][self::REDIS_KEY], true);
        if (!in_array($expId, $expIdList)) {
            $expIdList[] = $expId;
            $ret = $objRedis->set(self::REDIS_KEY, json_encode($expIdList));

            if ($ret['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("set exp id to redis fail, errno[{$ret['err_no']}], errmsg[{$ret['err_msg']}], key[" . self::REDIS_KEY . '], value[' . json_encode($expIdList) . ']');
                return self::_arrRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL, 'set data failed');
            }
        }

        // 记录日志
        $log = array(
            'addId'     => $expId,
            'opName'    => $opName,
            'expIdList' => $expIdList,
        );
        Bingo_Log::pushNotice('add_exp_id', json_encode($log));

        return self::_arrRet(Tieba_Errcode::ERR_SUCCESS, 'success');
    }

    /**
     * 删除实验id
     * 
     * @param array $arrInput 请求参数
     * @return array
     */
    public function removeExpId($arrInput) {

        // 获取请求参数
        $expId  = strval(trim($arrInput['id']));
        $opName = strval(trim($arrInput['opName']));

        // 验证参数
        if (empty($expId) || empty($opName)) {
            Bingo_Log::warning('invalid input[' . serialize($arrInput) . ']');
            return self::_arrRet(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
        }

        // 获取redis中的配置
        $objRedis = new Service_Libs_Redis();
        $ret      = $objRedis->get(self::REDIS_KEY);

        if ($ret['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("get exp id from redis fail, errno[{$ret['err_no']}], errmsg[{$ret['err_msg']}], key[" . self::REDIS_KEY . ']');
            return self::_arrRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL, 'get data failed');
        }

        // 若id存在于当前列表中，则删除
        $expIdList = json_decode($ret['ret'][self::REDIS_KEY], true);
        if (!empty($expIdList)) {
            $key = array_search($expId, $expIdList);
            if ($key !== false) {
                unset($expIdList[$key]);
                $ret = $objRedis->set(self::REDIS_KEY, json_encode($expIdList));

                if ($ret['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning("set exp id to redis fail, errno[{$ret['err_no']}], errmsg[{$ret['err_msg']}], key[" . self::REDIS_KEY . '], value[' . json_encode($expIdList) . ']');
                    return self::_arrRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL, 'set data failed');
                }
            }
        }

        // 记录日志
        $log = array(
            'removeId'  => $expId,
            'opName'    => $opName,
            'expIdList' => $expIdList,
        );
        Bingo_Log::pushNotice('remove_exp_id', json_encode($log));

        return self::_arrRet(Tieba_Errcode::ERR_SUCCESS, 'success');
    }

    /**
     * 获取实验id列表
     * 
     * @param array $arrInput 请求参数
     * @return array
     */
    public function getExpIdList($arrInput) {

        // 获取redis中的配置
        $objRedis = new Service_Libs_Redis();
        $ret      = $objRedis->get(self::REDIS_KEY);

        if ($ret['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("get exp id from redis fail, errno[{$ret['err_no']}], errmsg[{$ret['err_msg']}], key[" . self::REDIS_KEY . ']');
            return self::_arrRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL, 'get data failed');
        }

        return self::_arrRet(Tieba_Errcode::ERR_SUCCESS, 'success', json_decode($ret['ret'][self::REDIS_KEY], true));
    }

    /**
     * 构造返回信息
     * 
     * @param int    $errno   状态码
     * @param string $errmsg  信息
     * @param array  $data    数据
     * @return array
     */
    private static function _arrRet($errno = Tieba_Errcode::ERR_SUCCESS, $errmsg = '', $data = null) {

        $arrRet = array(
            'errno' => $errno,
            'errmsg' => $errmsg,
        );

        if ($data !== null) {
            $arrRet['data'] = $data;
        }

        return $arrRet;
    }
}