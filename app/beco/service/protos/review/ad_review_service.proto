// Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved 
// @brief �����˷��������� 

package aka;

import "fc_ad_field.proto";
import "general_ad_field.proto";

// @brief ö���г�����Ĳ�Ʒ��id����appid,һ�������ȥ�Ͳ������,�����Ĳ�Ʒ����ӵĺ���
enum ProductID {
    INNER_ID                                       = 10000; // �ڲ�id 
    FENGCHAO                                       = 0;   // pc�ﳲ
    BEIDOU                                         = 1;   // ����
    WENDA                                          = 2;   // �ʴ�Ӫ��
    PRODUCTADS                                     = 3;   // �ﳲ��Ͷ
    MOBILE_DSP                                     = 4;   // Mobile DSP(��ʵ)
    LU_DSP                                         = 5;   // LinkUnit DSP
    BES                                            = 6;   // BES
    NEWDSP                                         = 7;   // NEWDSP
    FENGCHAO_IP                                    = 8;   // �ﳲ����ʽ
    FENGCHAO_STRUCTED                              = 9;   // �ﳲ�ṹ������
    FENGCHAO_XIJING                                = 10;  // �ﳲ�辶
    FENGCHAO_WISE                                  = 11;  // ���߷ﳲ
    FENGCHAO_DYMICIDEA                             = 12;  // �ﳲ��̬����
    FENGCHAO_MXIJING                               = 13;  // �ﳲ�����辶
    HUNQING_ZHIXIN                                 = 14;  // ����֪��
    ZERO_DSP                                       = 15;  // 0-DSP
    SHANGMAO_ZHIXIN                                = 16;  // ��ó֪��
    FENGCHAO_ZTC                                   = 17;  // �ﳲֱͨ��
    FENGCHAO_MZTC                                  = 18;  // �ﳲ����ֱͨ��
    ACTIONADS                                      = 19;  // ��ҽ��
    FENGCHAO_SPECIAL_IDEA                          = 20;  // ��ҳͶ��, URL������(PC)
    FENGCHAO_DSA_URL                               = 21;  // ��ҳͶ����ؼ���
    MATERIAL_LIBRARY                               = 22;  // �ﳲ�زĿ�
    FENGCHAO_ACTIONADS                             = 23;  // �ﳲActionAds
    FENGCHAO_IP_COMMON                             = 24;  // �ﳲ����,��Ʒ��
    FENGCHAO_AUTO_TARGETING                        = 25;  // �ﳲ�ôʿ�Ͷ
    FENGCHAO_FEED_TUWEN                            = 26;  // �ﳲ��Ϣ��ͼ��
    FENGCHAO_FEED_APP                              = 27;  // �ﳲ��Ϣ��APP����
    FENGCHAO_FEED_TIEBA_TUWEN                      = 28;  // �ﳲ��Ϣ������ͼ��
    FENGCHAO_DYNAMIC_SEGMENT                       = 29;  // �ﳲͼƬ�زĿ�
    SHANTOU                                        = 30;  // ��Ͷ
    FENGCHAO_SEGMENT_TITLE_LINK                    = 31;  // �ﳲͼƬ�زĿ���������
    SHANTOU_ECOMMERCE_ACTIVE_PLAN                  = 32;  //  ��Ͷ�����ƹ�-��ƻ�
    SHANTOU_ECOMMERCE_API_PLAN                     = 33;  //  ��Ͷ�����ƹ�-API�Խ�
    SHANTOU_ECOMMERCE_PRODUCT_PLAN                 = 34;  //  ��Ͷ�����ƹ�-��Ʒ�ƻ�
    FENGCHAO_SHARE_PIC                             = 35;  // ����ͼƬ��
    MOBILE_MARKETING_LP                            = 36;  // �ƶ�Ӫ��ҳ
    FENGCHAO_BDZTC                                 = 37;  // �ﳲ����ֱͨ��
    FENGCHAO_FEED_EB                               = 38;  // �ﳲԭ��API
    FENGCHAO_SHARE_STORE                           = 39;  // �ﳲ�ŵ��ƹ�
    FENGCHAO_FEED_SHOUBAI_TUWEN                    = 40;  // �ְ�ͼ��
    FENGCHAO_FEED_BROWSER_APP                      = 41;  // �����APP
    FENGCHAO_FEED_BROWSER_TUWEN                    = 42;  // �����ͼ��
    FENGCHAO_INDUSTRY_URL                          = 43;  // ��ҵ��Ͷ
    FENGCHAO_FEED_COMMODITY                        = 44;  // ��Ͷ��Ʒ�ƻ�
    SHANTOU_NAVIGATION                             = 45;  // ��Ͷ����
    FENGCHAO_FEED_IDEATITLE                        = 46;  // �ﳲ��Ϣ���������ʰ�
    ACTIONADS_NEW_MEDICAL                          = 47;  // �ﳲҽ�ư�׼��Ϣ�ռ�
    FENGCHAO_EIGHT_PERMIT                          = 48;  // �ﳲ��׼
    BAITONG_INAPP                                  = 49;  // ��ͨINAPP
    VIDEOADSPLATFORM                               = 50;  // ��Ƶ���ƽ̨
    FENGCHAO_JINGXIU                               = 51;  // �ﳲ����
    MARKETING_MATERIAL_LIBRARY                     = 52;  // Ӫ���زĿ�
    CUMSTOMERFORM                                  = 53;  // �Զ����(����ͨ)
    BAIDU_FEED_DETAIL_PAGES                        = 54;  // �ٶ���Ϣ������ҳ(mdsp)
    FENGCHAO_FEED_SHOUBAI_APP                      = 55;  // �ְ�APP
    BAIDU_CLOUD                                    = 56;  // �ٶ�����ҳ����,�ļ�����
    FENGCHAO_FEED_NATIVE_GD_SHOUBAI_TUWEN          = 57;  // GD�ֻ��ٶ�ͼ��
    FENGCHAO_FEED_NATIVE_GD_TIEBA_PC               = 58;  // GD����pc
    FENGCHAO_FEED_NATIVE_GD_BROWSER                = 59;  // GD�ٶ������
    FENGCHAO_FEED_NATIVE_GD_SHOUBAI_VIDEO_TUJI     = 60;  // GD�ְ���Ƶ,ͼ��
    FENGCHAO_FEED_NATIVE_GD_SHOUBAI_DOWNLAOD       = 61;  // GD�ְ�app����
    FENGCHAO_FEED_NATIVE_GD_LEGO                   = 62;  // GD�ְٳ���ʽ���
    FENGCHAO_FEED_NATIVE_GD_TIEBA_WISE             = 63;  // GD����wap+app������
    FENGCHAO_FEED_NATIVE_GD_TIEBA_DOWNLOAD         = 64;  // GD��������
}

// @brief ���ģʽ, �������˶˽���ά��������ProductID��AKA�˽��м�¼
enum AuditMode {                                      
    kAd_MACHINE                                     = 1;   // ֻ�������
    kAd_HUMAN                                       = 2;   // ֻ�˹���� 
}

// @brief ��˷��ص�״̬
enum StatusCode {
    kStatusOK                                      = 0;   // ����ɹ�
    kStatusError                                   = 1;   // ���������������Ϣ
    kStatusRetry                                   = 2;   // ����æ�������ԣ�����ǰ�� sleep һ��ʱ��
}

// @brief �����˷���ӿ�
message AdReviewService {
    required ProductID product_id                  = 1;   // Ӧ�÷�Ψһ��ʶ
    optional uint32 audit_mode                     = 2[default = 3];  // ���ģʽ,AuditMode���,Ĭ��kAd_MACHINE | kAd_HUMAN

    optional GeneralAdField general_ad_field       = 3;
    optional FcAdField fc_ad_field                 = 4;

    // ��˷��ص�״̬�������岻Ҫ��䣬�첽��������ע
    optional StatusCode status_code                = 5[default = kStatusOK];  // ���״̬
    optional bytes status_msg                      = 6;   // ������Ϣ
    optional uint32 sub_app_id                     = 7;   // Ӧ�÷����ӱ�ʶ���˹�ƽ̨���������Ӳ�Ʒ�ߣ�����͸��
    optional uint32 product_id_inner               = 8;   // product_id�����ͱ�ʾ, ����ڲ�ʹ�ã��������ι���Ƶ������
}
