<?php
/**
 * Created by PhpStorm.
 * User: fanyunyang
 * Date: 2016/3/2
 * Time: 17:31
 */


class gdQueryCptCostAction extends Util_BaseAction{

    /**
     * @return   boolean
     * @desc 初始化
     */
    public function init(){
        return true;
    }

    /**
     * @param $planId
     * @param $date
     * @return array
     */
    public function execute(){

        $date  = strval(Bingo_Http_Request::getNoXssSafe('date',''));
        $arrInput = array(
            'date' => $date,
        );

        $arrRet = Tieba_Service::call('afd', 'getCptUnitDailyCost', $arrInput, null, null, 'post', 'php', 'utf-8', 'local');
        if( !isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("service call getCptUnitDailyCost fail. input: ".serialize($arrInput));
            $this->_errRet((empty($arrRet['errno']) ? Tieba_Errcode::ERR_UNKOWN: $arrRet['errno']), $arrRet['errmsg'], $arrRet['debug']);
            return false;
        }

        $outdata = json_encode($arrRet, JSON_UNESCAPED_SLASHES);
        echo $outdata;
    }
}
