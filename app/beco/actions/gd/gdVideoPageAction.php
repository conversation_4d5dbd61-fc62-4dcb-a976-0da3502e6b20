<?php

/**
 * Created by PhpStorm.
 * User: weeform
 * Date: 2017/3/2
 * Time: 下午12:01
 */
class gdVideoPageAction extends Util_BaseAction
{
    /**
     * @return   boolean
     * @desc 初始化
     */
    public function init(){
        return true;
    }

    /**
     * @return bool
     * @desc action 执行函数
     */
    public function execute(){
        $strIdeaID = strval(Bingo_Http_Request::getNoXssSafe('id',''));
        $sign = strval(Bingo_Http_Request::getNoXssSafe('sign',''));
        $callback = Bingo_Http_Request::getNoXssSafe('callback','');

        $jsonRet = "";
        if ($sign != md5($strIdeaID."8374250918e2cc55ad281d01230edb8d")) {
            echo $callback."($jsonRet)";
            $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'sign校验失败.');
            return false;
        }

        $ideaID = intval(Util_Crypt::decrypt($strIdeaID, '09ce17b38fc0a7b9e55f9d21b9b68d62'));
        if (empty($ideaID)) {
            Bingo_Log::warning("Param errors. id: ".json_encode($ideaID));
            $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '参数错误.');
            return false;
        }

        $arrInput = array(
            'cmd'   =>  Util_Const::GET_ACTION,
            'elem'  =>  Util_Const::IDEA,
            'data'  =>  array(
                'id'    => $ideaID,
            ),
        );
        $arrRet = Tieba_Service::call('beco', 'gdCmdHandle', $arrInput, null, 'service_adsense_gd', 'post', 'php', 'utf-8');

        // 以下写法是为解决跨域问题
        $jsonRet = json_encode($arrRet);
        echo $callback."($jsonRet)";
        return true;
    }
}