<?php
/**
 * Created by PhpStorm.
 * User: fanyunyang
 * Date: 2016/4/12
 * Time: 19:43
 */

class gdSetPvPercentAction extends Util_BaseAction{

    const PV_PERCENT_CRYPT_KEY = 'gd&pvPercent';

    /**
     * @return   boolean
     * @desc 初始化
     */
    public function init(){
        return true;
    }

    /**
     * @return bool
     * @desc action 执行函数
     */

    public function execute(){
        $place_id = strval(Bingo_Http_Request::get('place_id',''));
        $percents = strval(Bingo_Http_Request::get('percents',''));
        $sign = strval(Bingo_Http_Request::get('sign',''));

        //参数验证
        if(empty($place_id) || empty($percents) || empty($sign) || !preg_match("/^([0-9.]{1,10},){47}[0-9.]{1,10}$/", $percents) || !preg_match("/^[0-9a-zA-Z]+$/", $place_id)){
            Bingo_Log::warning("Param errors. percents ".$percents);
            $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '参数错误.');
            return false;
        }

        //签名验证
        $arrInput = array(
            'place_id' => $place_id,
            'percents' => $percents,
            'key' => self::PV_PERCENT_CRYPT_KEY,
        );
        $inputSign = md5(Bingo_String::array2json($arrInput,Bingo_Encode::ENCODE_UTF8));
        if($sign != $inputSign){
            Bingo_Log::warning("Data incomplete. src_sign ".$sign.", tar_sign ".$inputSign);
            $this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR, '签名验证失败.');
            return false;
        }

        //call pv set service
        $arrRet = Tieba_Service::call('beco', 'setPvPercent', $arrInput, null, 'service_adsense_gd', 'post', 'php', 'utf-8');
        if( !isset($arrRet['errno']) || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("service call setPvPercent fail. input: ".serialize($arrInput));
            $this->_errRet($arrRet['errno'], $arrRet['errmsg'], $arrRet['debug']);
            return false;
        }

        $this->_jsonRet($arrRet['errno'], $arrRet['errmsg'], $arrRet['data']);
        return true;
    }


}



