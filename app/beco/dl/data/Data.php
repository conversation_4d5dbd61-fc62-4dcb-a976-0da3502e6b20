<?php

/**
 * @name Dl_Data_Data
 * @desc 广告位数据获取
 * @date 2017-07-10 14:56
 * <AUTHOR>
 */

class Dl_Data_Data {

    /**
     * @param $startTime int 开始时间戳
     * @param $endTime int 结束时间戳
     * @return array|bool
     */
    public static function getUnits($startTime,$endTime){
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".   "get db fail." );
            return false;
        }

        $tableUnit = Util_Const::$_tableName[Util_Const::UNIT];
        $tablePlan = Util_Const::$_tableName[Util_Const::PLAN];

        $sql= "select
            p.id as plan_id,
            p.plan_type,
            u.id as unit_id,
            u.price,
            u.place_id,
            u.product_id,
            p.account_userid,
            p.account_name,
            p.account_type,
            p.contract,
            u.start_time,
            u.end_time,
            u.charge_mode,
            p.plan_amount
            from $tableUnit as u LEFT JOIN $tablePlan as p ON u.plan_id = p.id WHERE u.start_time <= $startTime AND u.end_time >= $endTime";
        $arrRet = $db->query($sql);
        if (false === $arrRet){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($arrRet)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return false;
        }
        return $arrRet;
    }

    /**
     * @param $unitIds array 单元id数组
     * @param $startTime int 开始时间戳
     * @param $endTime int 结束时间戳
     * @return array|bool
     */
    public static function getIdeas($unitIds,$startTime,$endTime){
        if(count($unitIds) === 0){
            return false;
        }
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".   "get db fail." );
            return false;
        }
        $tableIdea = Util_Const::$_tableName[Util_Const::IDEA];
        $sql = "select unit_id, template_id  from $tableIdea where unit_id in(".implode(',',$unitIds).") and start_time <= $startTime and end_time >= $endTime";
        $arrRet = $db->query($sql);
        if (false === $arrRet){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($arrRet)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return false;
        }
        return $arrRet;
    }

    /**
     * @desc 根据天查询曝光点击等数据
     * @param $date int 日期：20170709
     * @return array|bool
     */
    public static function getChargeByDate($date){
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".   "connect db fail." );
            return false;
        }
        $sql  = "select plan_id, unit_id, date,sum(click) as cli, sum(exposure) as exp, sum(cost) as cos from gd_charge_daily where exposure > 0 and date = {$date} group by unit_id";
        $units = $db->query($sql);
        if (false === $units){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($units)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return false;
        }

        $ret = array();
        foreach($units as $unit){
            $ret[$unit['plan_id']][$unit['unit_id']] = $unit;
        }
        return $ret;
    }

    /**
     * @desc 根据计划id，获取计划列表信息
     * @param $planIds array 计划id数组
     * @return array|bool
     */
    public static function getPlans($planIds){
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".   "connect db fail." );
            return false;
        }
        $strPlanIds = implode(',', $planIds);
        $sql = "select id, plan_type, plan_amount, account_userid, start_time, end_time from gd_plans where id in({$strPlanIds})";
        $plans = $db->query($sql);
        if (false === $plans){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($plans)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return false;
        }

        $ret = array();
        foreach($plans as $plan){
            $ret[$plan['id']] = $plan;
        }
        return $ret;
    }

    /**
     * @desc 根据单元id数组，获取单元详情信息列表
     * @param $unitIds array 单元id数组
     * @return array|bool
     */
    public static function getUnitsDetail($unitIds){
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".   "connect db fail." );
            return false;
        }
        $strUnitIds = implode(',', $unitIds);

        $sql = "select id, place_id, price, charge_mode from gd_units where id in ({$strUnitIds})";
        $units = $db->query($sql);
        if (false === $units){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($units)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return false;
        }
        $ret = array();
        foreach($units as $unit){
            $ret[$unit['id']] = $unit;
        }
        return $ret;
    }

    /**
     * @desc 根据单元id数组获取历史点击等数据
     * @param $unitIds array 单元id数组
     * @return array|bool
     */
    public static function getChargeHistoryByUnitIds($unitIds){
        if (null === ($db = Service_Libs_Gd_Db::getDB())) {
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".   "connect db fail." );
            return false;
        }
        $strUnitIds = implode(',',$unitIds);
        $sql = "select date, plan_id, unit_id, sum(cost) as cos from gd_charge_daily where unit_id in ({$strUnitIds}) group by unit_id, date";
        $chargeInfos = $db->query($sql);
        if (false === $chargeInfos){
            Service_Libs_Gd_Debug::gdWarning(__METHOD__." "."line:".__LINE__." ".  "db query error! [output:".serialize($chargeInfos)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return false;
        }
        $ret = array();
        foreach($chargeInfos as $chargeInfo){
            $ret[$chargeInfo['plan_id']][$chargeInfo['date']][$chargeInfo['unit_id']] = $chargeInfo['cos'];
        }
        return $ret;
    }

    /**
     * @desc 根据历史点击等数据数组获取单元id列表
     * @param $chargeByDate array 历史数据
     * @return array
     */
    private static function getUnitIds($chargeByDate){
        $ret = array();
        foreach($chargeByDate as $planId => $units){
            $ret = array_merge($ret,array_keys($units));
        }
        return $ret;
    }

    /**
     * @param $date int 日期,如：20170710
     * @return array|bool
     */
    public static function getCharge($date){
        $chargeByDate = self::getChargeByDate($date);
        $planIds = array_keys($chargeByDate);
        $unitIds = self::getUnitIds($chargeByDate);
        $chargeHistory = self::getChargeHistoryByUnitIds($unitIds);
        $plansTable = self::getPlans($planIds);
        $unitsTable = self::getUnitsDetail($unitIds);

        //超出预算时的花费列表
        $lastAmount = array();
        foreach($chargeHistory as  $planId => $charges){
            $arrOrderAmount = self::getOrderAmount($plansTable[$planId]['plan_amount'],$charges);
            if($arrOrderAmount['date'] == 0){
                //不存在超出预算的时间，即没有超出预算
                continue;
            }elseif($arrOrderAmount['date'] < $date){
                //过了超出预算的那天后，消耗值都为0
                foreach($arrOrderAmount['amounts'] as $unitId => $amount) {
                    $lastAmount[$unitId] = 0;
                }
            }elseif($arrOrderAmount['date'] == $date){
                //在超出预算的当天，根据真实消耗额的比例来分配每个单元的消耗额
                foreach($arrOrderAmount['amounts'] as $unitId => $amount) {
                    $lastAmount[$unitId] = $amount;
                }
            }
        }

        $arrResult = array();
        foreach($chargeByDate as $planId => $units){
            $planAmount = $plansTable[$planId]['plan_amount'];
            foreach($units as $unitId => $charge){
                $i = $unitId;
                $arrResult[$i]['plan_id'] = $planId;
                $arrResult[$i]['unit_id'] = $unitId;
                $arrResult[$i]['user_id'] = $plansTable[$planId]['account_userid'];
                $arrResult[$i]['place_id'] = $unitsTable[$unitId]['place_id'];
                $arrResult[$i]['charge_mode'] = $unitsTable[$unitId]['charge_mode'];
                if ($unitsTable[$unitId]['charge_mode'] == Util_Const::CPM_CHARGEMODE) {
                    $cost = round(($charge['exp']/1000)*($unitsTable[$unitId]['price']/100), 2);
                    if (isset($lastAmount[$unitId])) {
                        $arrResult[$i]['order_amount'] = $lastAmount[$unitId];
                    } elseif($planAmount == 0){//当计划现金为0的时候
                        $arrResult[$i]['order_amount'] = 0;
                    } else {
                        $arrResult[$i]['order_amount'] = $cost;
                    }
                    $arrResult[$i]['cost'] = $cost;
                } else {
                    $arrResult[$i]['order_amount'] = $arrResult[$i]['cost'] = round($charge['cos']/100, 2);
                }

                $arrResult[$i]['exposure'] = $charge['exp'];
                $arrResult[$i]['click'] =   $charge['cli'];
            }
        }

        return $arrResult;
    }

    /**
     * @param $planAmount float 计划总预算
     * @param $dates2unitIds2Costs array 历史消费$[date][unit]=cost
     * @return array|bool
     */
    private static function getOrderAmount($planAmount,$dates2unitIds2Costs){
        $historyCharge = array();
        $totalSum = 0;
        $dateOfOver = 0;
        $lastAmount = array();
        foreach($dates2unitIds2Costs as $date => $unitIds2Costs){
            $sum = 0;
            foreach($unitIds2Costs as $unitId => $cost){
                $sum += $cost;
            }
            $historyCharge[$date] = $sum;
            if($totalSum + $sum > $planAmount ){
                //超出预算的时间点
                $dateOfOver = $date;

                $amountLeft = $planAmount - $totalSum;
                foreach($unitIds2Costs as $unitId => $cost){
                    $lastAmount[$unitId] = $cost/$sum * $amountLeft;
                }

                break;
            }
            $totalSum += $sum;
        }

        return array(
            'date' => $dateOfOver,//超出预算的时间点
            'amounts' => $lastAmount
        );
    }
}