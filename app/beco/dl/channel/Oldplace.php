<?php
/***************************************************************************
 *
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file Oldplace.php
 * <AUTHOR>
 * @date  2016-10-17 18:10
 * @brief
 *
 **/
define('BINGO_ENCODE_LANG', 'UTF-8');
define("MODULE","Beco_dl");
class Dl_Channel_Oldplace{
    const SERVICE_NAME = "dl_channel_oldplace";
    const MODULE_NAME = 'beco';
    const DATABASE_NAME = "forum_native";
    const DB_CHARSET = "utf8";

    protected static $_db = null;
    protected static $_conf = null;
    protected static $_use_split_db = false;

    /**
     * @brief get mysql obj.
     * @return: obj of Bd_DB, or null if connect fail.
     **/
    private static function _getDB() {
        if(self::$_db) {
            return self::$_db;
        }
        Bingo_Timer::start(self::SERVICE_NAME.'_initdb');
        self::$_db = Tieba_Mysql::getDB(self::DATABASE_NAME);
        Bingo_Timer::end(self::SERVICE_NAME.'_initdb');
        if(self::$_db == null || !self::$_db->isConnected()) {
            Bingo_Log::warning('db connect fail.');
            return null;
        }
        return self::$_db;
    }

    /**
     * @brief init
     * @return: true if success. false if fail.
     **/
    private static function _init() {
        if(self::_getDB() == null) {
            Bingo_Log::warning("init db fail.");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        if(self::$_conf == null) {
            $dlConfFile = '/app/' . self::MODULE_NAME . '/'. strtolower(self::SERVICE_NAME);
            self::$_conf = Bd_Conf::getConf($dlConfFile);
            if(self::$_conf == false) {
                Bingo_Log::warning('init get conf fail.' . $dlConfFile);
                return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
            }
        }
        return true;
    }

    /**
     * exec sql method
     * @param $arrInput
     * @return output
     */
    public static function execSql($arrInput) {
        if(!isset($arrInput['function'])) {
            Bingo_Log::warning('input params invalid: function is empty. [' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $ret = self::_init();
        if($ret !== true) {
            return $ret;
        }
        Bingo_Timer::start(self::SERVICE_NAME.'_initlib');
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end(self::SERVICE_NAME.'_initlib');
        if($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        Bingo_Timer::start(self::SERVICE_NAME.'_execSql');
        $arrOut = $mdb->execSql($arrInput);
        Bingo_Timer::end(self::SERVICE_NAME.'_execSql');
        return $arrOut;
    }

    /**
     * [_errRet description]
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    private static function _errRet($errno){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
