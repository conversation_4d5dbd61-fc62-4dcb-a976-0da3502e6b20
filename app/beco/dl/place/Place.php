<?php

/**
 * @name Dl_Place_Place
 * @desc 广告位数据获取
 * @date 2017-04-14 14:56
 * <AUTHOR>
 */
class Dl_Place_Place {

    // 表名
    const PLACE_TABLE      = 'native_place';
    const PLACE_TPL_TABLE  = 'native_place_tpl_ref';
    const PLACE_FLOW_TABLE = 'native_place_flow_control';

    // 状态
    const STATUS_FALSE = 0;
    const STATUS_TRUE  = 1;

    // 接入方式
    const CONN_SDK = 0;
    const CONN_API = 1;
    const CONN_JS  = 2;

    // 请求方式
    const REQ_SYNC  = 1;
    const REQ_ASYNC = 2;

    /**
     * 获取广告位列表
     * 
     * @param int $productId 产品线id
     * @param int $pn        页数
     * @param int $rn        每页条数
     * @return array
     */
    public static function getPlaceList($arrInput) {

        // 获取db对象
        $objDB = Libs_DB::getInstance();
        if ($objDB === null) {
            return false;
        }

        // 拼装sql
        if ($arrInput['pn'] === null && $arrInput['rn'] === null) {
            // 获取广告位下拉筛选列表
            $sql = 'SELECT `place_id` AS placeId, `place_name` AS placeName FROM `' . self::PLACE_TABLE . '`';
            $ret = $objDB->query($sql);
        } else {
            // 获取广告位列表
            $sql = 'SELECT SQL_CALC_FOUND_ROWS `id`, `place_id` AS placeId, `place_name` AS placeName, ' .
                   '`product_id` AS productId, `page_id` AS pageId, `platform_type` AS platformType, ' .
                   '`os_type` AS osType, `place_type` AS placeType, `fix_cpm_low_price` AS cpm, `status` ' .
                   'FROM `' . self::PLACE_TABLE . '` ';

            if (!is_null($arrInput['productId'])) {
                $sql .= "WHERE `product_id` = {$arrInput['productId']} ";
            }

            $offset = ($arrInput['pn'] - 1) * $arrInput['rn'];
            $sql .= "LIMIT {$offset}, {$arrInput['rn']}";

            // 获取当前页数据和总数
            $ret = $objDB->query($sql);
            $retCount = $objDB->query('SELECT FOUND_ROWS() AS count');
            if ($ret === false || $retCount === false) {
                return false;
            }

            $ret = array(
                'total' => intval($retCount[0]['count']),
                'items' => $ret,
            );
        }

        return $ret;
    }

    /**
     * 获取GD广告位列表
     * 
     * @param int $productId 产品线id
     * @param int $pn        页数
     * @param int $rn        每页条数
     * @return array
     */
    public static function getPlaceListForGd($arrInput) {

        // 获取db对象
        $objDB = Libs_DB::getInstance();
        if ($objDB === null) {
            return false;
        }

        // 获取广告位列表
        $sql = 'SELECT SQL_CALC_FOUND_ROWS `place_id` AS placeId, `place_name` AS placeName ' .
               'FROM `' . self::PLACE_TABLE . '` where status = '.self::STATUS_TRUE.' ';

        if (!is_null($arrInput['productId'])) {
            $sql .= "AND `product_id` = {$arrInput['productId']} ";
        }

        if (!is_null($arrInput['platformType'])) {
            $sql .= "AND `platform_type` = {$arrInput['platformType']} ";
        }

        if (!is_null($arrInput['osType'])) {
            $sql .= "AND `os_type` = {$arrInput['osType']} ";
        }

        $ret = $objDB->query($sql);

        if ($ret === false) {
            return false;
        }

        if (!empty($ret)) {
            //只返回GD支持的广告位信息，并且广告位处于有效状态
            $arrPlaceIds = array();
            foreach ($ret as $key => $itemPlaceInfo) {
                $arrPlaceIds[] = $itemPlaceInfo['placeId'];
            }
            $sql = "select place_id as placeId from ". self::PLACE_FLOW_TABLE . " where gd_show = 0 and gd_status = 0 and place_id in (". implode(',', $arrPlaceIds) .")";
            $arrNoGdRet = $objDB->query($sql);
            if ($arrNoGdRet === false) {
                return false;
            }

            foreach ($arrNoGdRet as $itemNoGD) {
                foreach ($ret as $key => $itemPlaceInfo) {
                    if ($itemPlaceInfo['placeId'] == $itemNoGD['placeId']) {
                        //非GD产品线
                        unset($ret[$key]);
                    }
                }
            }
        }

        if ($arrInput['isMerge'] == 1 && is_null($arrInput['osType']) && !empty($ret)) {
            //广告位合并处理
            $arrMergeRet = array();
            foreach ($ret as $info) {
                //先将广告位名称拆分
                $arrPlace = explode('_', $info['placeName']);
                $arrNewPlaceName = array();
                foreach ($arrPlace as $value) {
                    //用安卓+ios判断是否合并广告位
                    $strLowerName = strtolower($value);
                    if ($strLowerName != 'android' && $strLowerName != 'ios') {
                        $arrNewPlaceName[] = $value;
                    }
                }
                $strNewPlaceName = implode('_', $arrNewPlaceName);
                if (!isset($arrMergeRet[$strNewPlaceName])) {
                    $arrMergeRet[$strNewPlaceName] = $info;
                } else {
                    //合并的广告位只处理placeId，逗号分隔，其他保持一致
                    $arrMergeRet[$strNewPlaceName]['placeId'] = $arrMergeRet[$strNewPlaceName]['placeId'].",".$info['placeId'];
                    $arrMergeRet[$strNewPlaceName]['placeName'] = $strNewPlaceName;
                }
            }
            $ret = array();
            foreach ($arrMergeRet as $value) {
                $ret[] = $value;
            }
        }

        $ret = array(
            'total' => count($ret),
            'items' => $ret,
        );

        return $ret;
    }

    /**
     * 启用/暂停广告位
     * 
     * @param int    $id      主键id
     * @param int    $status  状态
     * @param string $opUser  操作人
     * @param string $opTime  操作时间
     * @return array
     */
    public static function enablePlace($arrInput) {

        // 获取db对象
        $objDB = Libs_DB::getInstance();
        if ($objDB === null) {
            return false;
        }

        // 拼装sql
        $opUser = $objDB->escape($arrInput['opUser']);
        $sql = 'UPDATE `' . self::PLACE_TABLE . "` SET `status` = {$arrInput['status']}, `update_user` = '{$opUser}', " .
               "`update_time` = '{$arrInput['opTime']}' WHERE `id` = {$arrInput['id']}";
        $ret = $objDB->query($sql);

        return $ret;
    }

    /**
     * 根据广告位名称查询
     * 
     * @param string $placeName 广告位名称
     * @return array
     */
    public static function getPlaceByName($arrInput) {

        // 获取db对象
        $objDB = Libs_DB::getInstance();
        if ($objDB === null) {
            return false;
        }

        // 拼装sql
        $placeName = $objDB->escape($arrInput['placeName']);
        $sql = 'SELECT `id` FROM `' . self::PLACE_TABLE . "` WHERE `place_name` = '{$placeName}'";
        $ret = $objDB->query($sql);

        return ($ret === false) ? false : $ret[0];
    }

    /**
     * 编辑广告位
     * 
     * @param int    $id           主键id
     * @param string $placeId      广告位id
     * @param string $placeName    广告位名称
     * @param int    $productId    产品线id
     * @param int    $pageId       页面id
     * @param int    $platformType 所属平台id
     * @param int    $placeType    广告位类型
     * @param int    $connType     接入方式
     * @param int    $reqType      请求类型
     * @param int    $sizeId       尺寸id
     * @param string $tplId        关联模板id
     * @param int    $isDynamic    是否动态广告位
     * @param string $dynamicFloor 动态楼层配置
     * @param double $cpm          cpm底价
     * @param string $opUser       操作人
     * @param string $opTime       操作时间
     * @param string $gdControl    gd流量配置
     * @param string $rtbControl   rtb流量配置
     * @return array
     */
    public static function savePlace($arrInput) {

        // 获取db对象
        $objDB = Libs_DB::getInstance();
        if ($objDB === null) {
            return false;
        }

        // sql过滤
        $placeName = $objDB->escape($arrInput['placeName']);
        $opUser    = $objDB->escape($arrInput['opUser']);

        if (empty($arrInput['id'])) {
            // 新建广告位
            $sql = 'INSERT INTO `' . self::PLACE_TABLE . '` (`place_id`, `place_name`, `product_id`, `page_id`, `platform_type`, ' .
                   '`os_type`, `place_type`, `size_id`, `req_type`, `conn_type`, `is_dynamic`, `dynamic_floor`, ' .
                   '`fix_cpm_low_price`, `status`, `create_user`, `update_user`, `create_time`, `update_time`) VALUES ' .
                   "('{$arrInput['placeId']}', '{$placeName}', {$arrInput['productId']}, {$arrInput['pageId']}, " .
                   "{$arrInput['platformType']}, {$arrInput['osType']}, {$arrInput['placeType']}, {$arrInput['sizeId']}, " .
                   "{$arrInput['reqType']}, {$arrInput['connType']}, {$arrInput['isDynamic']}, '{$arrInput['dynamicFloor']}', " .
                   "{$arrInput['cpm']}, " . self::STATUS_FALSE . ", '{$opUser}', '{$opUser}', '{$arrInput['opTime']}', '{$arrInput['opTime']}')";
        } else {
            // 修改广告位
            $sql = 'UPDATE `' . self::PLACE_TABLE . "` SET `place_name` = '{$placeName}', `conn_type` = {$arrInput['connType']}, " .
                   "`req_type` = {$arrInput['reqType']}, `size_id` = {$arrInput['sizeId']}, `is_dynamic` = {$arrInput['isDynamic']}, " .
                   "`dynamic_floor` = '{$arrInput['dynamicFloor']}', `fix_cpm_low_price` = {$arrInput['cpm']}, " .
                   "`update_user` = '{$opUser}', `update_time` = '{$arrInput['opTime']}' WHERE `id` = {$arrInput['id']}";
        }

        // 开启事务
        if ($objDB->startTransaction() === false) {
            return false;
        }

        // 更新广告位表
        if ($objDB->query($sql) === false) {
            $objDB->rollback();
            return false;
        }

        // 若为编辑广告位，则删除原来和模板的对应关系
        if (!empty($arrInput['id'])) {
            $sql = 'DELETE FROM `' . self::PLACE_TPL_TABLE . "` WHERE `place_id` = '{$arrInput['placeId']}'";
            if ($objDB->query($sql) === false) {
                $objDB->rollback();
                return false;
            }
        }

        // 更新广告位和模板关系表
        if (!empty($arrInput['tplId'])) {
            $sql = 'INSERT INTO `' . self::PLACE_TPL_TABLE . '` (`place_id`, `tpl_id`) VALUES ';
            foreach ($arrInput['tplId'] as $tplId) {
                $tplId = $objDB->escape($tplId);
                $sql .= "('{$arrInput['placeId']}', '{$tplId}'),";
            }

            if ($objDB->query(trim($sql, ',')) === false) {
                $objDB->rollback();
                return false;
            }
        }

        $insertPlaceFlowSql = '';
        // 更新流量配置表
        if (empty($arrInput['id'])) {
            // 初始化流量配置数据，勾选了才会添加
            $dynamicFloor = json_decode($arrInput['dynamicFloor'], true);
            $flowControl  = array('value' => 0);
            foreach ($dynamicFloor as $key => $item) {
                if ($item['isChecked']) {
                    $flowControl[$key] = array(
                        'valid'              => self::STATUS_FALSE,
                        'freshCountAllValid' => self::STATUS_FALSE,
                        'freshCount'         => array(),
                    );
                }
            }

            $flowControl = json_encode($flowControl);
            $insertPlaceFlowSql = 'INSERT INTO `' . self::PLACE_FLOW_TABLE . '` (`place_id`, `gd_show`, `gd_status`, `gd_control`, `rtb_status`, `rtb_control`, ' .
                   "`create_user`, `update_user`, `create_time`, `update_time`) VALUES ('{$arrInput['placeId']}', 0, 0, '{$flowControl}', 0, " .
                   "'{$flowControl}', '{$opUser}', '{$opUser}', '{$arrInput['opTime']}', '{$arrInput['opTime']}')";
        } else if ($arrInput['isDynamic']) {
            // 更新流量控制数据
            $insertPlaceFlowSql = 'UPDATE `' . self::PLACE_FLOW_TABLE . "` SET `gd_control` = '{$arrInput['gdControl']}', `rtb_control` = '{$arrInput['rtbControl']}' " .
                   "WHERE `place_id` = '{$arrInput['placeId']}'";
        }

        if(!empty($insertPlaceFlowSql)){
            if ($objDB->query($insertPlaceFlowSql) === false) {
                $objDB->rollback();
                return false;
            }
        }
        return $objDB->commit();
    }

    /**
     * 根据广告位id获取广告位信息
     * 
     * @param int $placeId 广告位id
     * @return array
     */
    public static function getPlaceById($arrInput) {

        // 获取db对象
        $objDB = Libs_DB::getInstance();
        if ($objDB === null) {
            return false;
        }

        $placeId = $objDB->escape($arrInput['placeId']);
        $sql = 'SELECT `id`, `place_id` AS placeId, `place_name` AS placeName, `product_id` AS productId, `page_id` AS pageId, `platform_type` AS platformType, ' .
               '`os_type` AS osType, `place_type` AS placeType, `size_id` AS sizeId, `req_type` AS reqType, `conn_type` AS connType, `is_dynamic` AS isDynamic, ' .
               '`dynamic_floor` AS dynamicFloor, `fix_cpm_low_price` AS cpm FROM `' . self::PLACE_TABLE . "` WHERE `place_id` = '{$placeId}'";
        $ret = $objDB->query($sql);

        return ($ret === false) ? false : $ret[0];
    }

    /**
     * 根据广告位id查询流量控制信息
     * 
     * @param string $placeId 广告位id
     * @return array
     */
    public static function getPlaceFlowControl($arrInput) {

        // 获取db对象
        $objDB = Libs_DB::getInstance();
        if ($objDB === null) {
            return false;
        }

        $sql = 'SELECT `id`, `place_id` AS placeId, `gd_status` AS gdStatus, `gd_control` AS gdControl, `rtb_status` AS rtbStatus, `rtb_control` AS rtbControl FROM `' .
               self::PLACE_FLOW_TABLE . '` ';

        // 可通过id或placeId查询
        if (isset($arrInput['id'])) {
            $sql .= "WHERE `id` = {$arrInput['id']}";
        } else if (isset($arrInput['placeId'])) {
            $sql .= "WHERE `place_id` = '{$arrInput['placeId']}'";
        } else {
            return false;
        }

        $ret = $objDB->query($sql);

        return ($ret === false) ? false : $ret[0];
    }

    /**
     * 获取流量控制列表
     * 
     * @param int    $productId 产品线id
     * @param string $placeId   广告位id
     * @param int    $pn        页数
     * @param int    $rn        每页条数
     * @return array
     */
    public static function getPlaceFlowControlList($arrInput) {

        // 获取db对象
        $objDB = Libs_DB::getInstance();
        if ($objDB === null) {
            return false;
        }

        // 拼装sql
        $sql = 'SELECT SQL_CALC_FOUND_ROWS a.`id`, a.`place_id` AS placeId, a.`gd_show` AS gdShow, a.`gd_status` AS gdStatus, ' .
               'a.`gd_control` AS gdControl, a.`rtb_status` AS rtbStatus, a.`rtb_control` AS rtbControl, b.`product_id` AS productId, ' .
               'b.`place_name` AS placeName, b.`is_dynamic` AS isDynamic FROM `' . self::PLACE_FLOW_TABLE . '` a LEFT JOIN `' .
               self::PLACE_TABLE . '` b on ' . 'a.`place_id` = b.`place_id`';

        $arrConds = array();
        if (!empty($arrInput['placeId'])) {
            $placeId = $objDB->escape($arrInput['placeId']);
            $arrConds[] = "a.`place_id` = '{$placeId}'";
        }

        if (!is_null($arrInput['productId'])) {
            $arrConds[] = "b.`product_id` = {$arrInput['productId']}";
        }

        if (!empty($arrConds)) {
            $strConds = implode(' AND ', $arrConds);
            $sql .= " WHERE {$strConds}";
        }

        $offset = ($arrInput['pn'] - 1) * $arrInput['rn'];
        $sql .= " LIMIT {$offset}, {$arrInput['rn']}";

        $ret = $objDB->query($sql);
        $retCount = $objDB->query('SELECT FOUND_ROWS() AS count');
        if ($ret === false || $retCount === false) {
            return false;
        }

        return array(
            'total' => intval($retCount[0]['count']),
            'items' => $ret,
        );
    }

    /**
     * 编辑广告位流量控制
     * 
     * @param int    $id         主键id
     * @param int    $gdStatus   gd开关状态
     * @param string $gdControl  gd流量控制
     * @param int    $rtbStatus  rtb开关状态
     * @param string $rtbControl rtb流量控制
     * @param string $opUser     操作人
     * @param string $opTime     操作时间
     * @return array
     */
    public static function editPlaceFlowControl($arrInput) {

        // 获取db对象
        $objDB = Libs_DB::getInstance();
        if ($objDB === null) {
            return false;
        }

        $opUser = $objDB->escape($arrInput['opUser']);
        if ($arrInput['type'] == Service_Place_Place::GD) {
            $sql = 'UPDATE `' . self::PLACE_FLOW_TABLE . "` SET `gd_status` = {$arrInput['status']}, `gd_control` = '{$arrInput['flowControl']}', " .
                   "`update_user` = '{$opUser}', `update_time` = '{$arrInput['opTime']}' WHERE `id` = {$arrInput['id']}";
        } else {
            $sql = 'UPDATE `' . self::PLACE_FLOW_TABLE . "` SET `rtb_status` = {$arrInput['status']}, `rtb_control` = '{$arrInput['flowControl']}', " .
                   "`update_user` = '{$opUser}', `update_time` = '{$arrInput['opTime']}' WHERE `id` = {$arrInput['id']}";
        }
        $ret = $objDB->query($sql);

        return $ret;
    }

    /**
     * 获取广告位和模板关联关系
     * 
     * @param string $placeId 广告位id
     * @return array
     */
    public static function getPlaceTpl($arrInput) {

        // 获取db对象
        $objDB = Libs_DB::getInstance();
        if ($objDB === null) {
            return false;
        }

        $sql = 'SELECT `tpl_id` AS tplId FROM `' . self::PLACE_TPL_TABLE . "` WHERE `place_id` = '{$arrInput['placeId']}'";
        $ret = $objDB->query($sql);

        return $ret;
    }

    /**
     * 设置广告位gd是否展示
     * 
     * @param int    $id     主键id
     * @param int    $show   是否展示
     * @param string $opUser 操作人
     * @param string $opTime 操作时间
     * @return array
     */
    public static function setPlaceGdShow($arrInput) {

        // 获取db对象
        $objDB = Libs_DB::getInstance();
        if ($objDB === null) {
            return false;
        }

        $opUser = $objDB->escape($arrInput['opUser']);
        $sql = 'UPDATE `' . self::PLACE_FLOW_TABLE . "` SET `gd_show` = {$arrInput['show']}, `update_user` = '{$opUser}', " .
               "`update_time` = '{$arrInput['opTime']}' WHERE `id` = {$arrInput['id']}";
        $ret = $objDB->query($sql);

        return $ret;
    }

    /**
     * 获取广告位和流量控制信息
     * 
     * @param int $placeId 广告位id
     * @return array
     */
    public static function getPlaceAndFlowControl($arrInput) {

        // 获取db对象
        $objDB = Libs_DB::getInstance();
        if ($objDB === null) {
            return false;
        }

        $sql ='SELECT a.`place_id` AS placeId, a.`product_id` AS productId, a.`place_type` AS placeType, a.`is_dynamic` AS isDynamic, ' .
              'a.`dynamic_floor` AS dynamicFloor, a.`status` AS status, b.`gd_status` AS gdStatus, b.`gd_control` AS gdControl, ' .
              'b.`rtb_status` AS rtbStatus, b.`rtb_control` AS rtbControl FROM `' . self::PLACE_TABLE . '` a LEFT JOIN `' .
              self::PLACE_FLOW_TABLE . '` b on a.`place_id` = b.`place_id` WHERE a.`place_id` = "' . $arrInput['placeId'] . '"';
        $ret = $objDB->query($sql);
        
        return ($ret === false) ? false : $ret[0];
    }

    /**
     * 根据产品线id和页面id获取广告位个数
     * 
     * @param int $productId 产品线id
     * @param int $pageId    页面id
     * @return array
     */
    public static function getPlaceCount($arrInput) {

        // 获取db对象
        $objDB = Libs_DB::getInstance();
        if ($objDB === null) {
            return false;
        }

        // 拼装sql
        $sql = 'SELECT `product_id` AS productId, `page_id` AS pageId, count(`id`) AS placeCount FROM `' . self::PLACE_TABLE . '`';

        $arrConds  = array();
        $arrGroups = array();
        if (isset($arrInput['productId']) && !is_null($arrInput['productId'])) {
            $arrConds[]  = "`product_id` = {$arrInput['productId']}";
            $arrGroups[] = '`product_id`';
        }

        if (isset($arrInput['pageId']) && !is_null($arrInput['pageId'])) {
            $arrConds[]  = "`page_id` = {$arrInput['pageId']}";
            $arrGroups[] = '`page_id`';
        }

        if (!empty($arrConds)) {
            $strConds  = implode(' AND ', $arrConds);
            $strGroups = implode(', ', $arrGroups);
            $sql      .= " WHERE {$strConds} GROUP BY {$strGroups}";
        }

        // 查询数据库
        $ret = $objDB->query($sql);

        return ($ret === false) ? false : $ret[0];
    }

    /**
     * @desc  : 根据页面id获取广告位的个数
     * @param : arrInput
     * @return: array
     */
    public static function getPlaceCountByPageId($pageId) {
        // 获取db对象
        $objDB = Libs_DB::getInstance();
        if ($objDB === null) {
            return false;
        }
        $sql = 'select `page_id` as pageId, count(`id`) as placeCount from ' . self::PLACE_TABLE . " where page_id = $pageId ";
        // 查询数据库
        $ret = $objDB->query($sql);

        return ($ret === false) ? false : $ret[0];
    }
}