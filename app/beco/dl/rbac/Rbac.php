<?php
/**
 * @file Rbac.php
 * <AUTHOR>
 * @date 2017/2/6 16:18:30
 * @brief aop权限系统
 **/

class Dl_Rbac_Rbac {
    const ROLE_ROOT_ROLENAME = "general_role_root";

    const ROLE_TYPE_ROOT = 1;
    const ROLE_TYPE_ACT = 2;
    const ROLE_TYPE_VIR = 3;

    const ROLE_PARRENT_LIST_SIZE = 4;

    const DB_DATABASE = "forum_beco";
    //const DB_DATABASE = "forum_gcon";

    const DB_TABLE_ROLE = "rbac_role_info";
    const DB_TABLE_PERM = "rbac_perm_info";
    const DB_TABLE_USER_ROLE_RELATION = "rbac_user_role_relation";

    const DB_MYSQL_DUP = 1046;

    const CONST_TREE_MAX_DEPTH = 2;

    const CONST_RET_TYPE_JSON = 1;
    const CONST_RET_TYPE_ARRAY = 2;
    /**
     * @desc get mysql obj
     * @param null
     * @return db object
     */
    public static function getDB() {
        $objTbMysql = Tieba_Mysql::getDB(self::DB_DATABASE);
        if ($objTbMysql && $objTbMysql->isConnected()) {
            $objTbMysql->charset('utf8');
            return $objTbMysql;
        } else {
            Bingo_Log::warning('db connect fail');
            return null;
        }
    }
    /**
     * @param   int  $errno
     * @param   string  $errmsg
     * @param   array  $data
     * @return  array  $arrRet
     * @desc 后端服务返回格式化
     */
    protected static function _successRet($errno=Tieba_Errcode::ERR_SUCCESS, $errmsg='', $data = array(), $data_key='data'){
        $errmsg = empty($errmsg) ? Tieba_Error::getErrmsg($errno) : $errmsg;
        $arrRet = array(
            'errno' => $errno,
            'errmsg' => $errmsg,
        );
        if(!empty($data)) {
            $arrRet[$data_key] = $data;
        }

        return $arrRet;
    }
    /**
     * @param $errno
     * @param string $errmsg
     * @return array
     */
    protected static function _errRet($errno=Tieba_Errcode::ERR_PARAM_ERROR, $errmsg=''){
        $errmsg = empty($errmsg) ? Tieba_Error::getErrmsg($errno) : $errmsg;
        $errRet = array(
            'errno' => $errno,
            'errmsg' => $errmsg,
        );

        return $errRet;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
