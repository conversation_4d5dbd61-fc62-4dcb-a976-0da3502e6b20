<?php
/**
 * Created by PhpStorm.
 * User: chenlei24
 * Date: 2016/11/28
 * Time: 15:05
 */

class Dl_Media_Forum {
    const DATABASE_NAME = "forum_beco";
    const MAX_SHIELD_FORUM_NUM = 10000;
    const ERR_REACH_UPPER_LIMIT = 1;
    private static $offline_shield = 0;
    private static $online_shield  = 1;
    /**
     * @desc get mysql obj
     * @param null
     * @return db object
     */
    private static function _getDB() {
        $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if ($objTbMysql && $objTbMysql->isConnected()) {
            $objTbMysql->charset('utf8');
            return $objTbMysql;
        } else {
            Bingo_Log::warning('db connect fail');
            return null;
        }
    }
    /**
     * @desc 进行格式化返回
     * @param errno  : int    : 错误号
     * @param errmsg : string : 错误信息
     * @param data   : array  : 错误详细信息数组
     * @return error return
     */
    private static function _errRet($errno, $errmsg, $data=array()) {
        $arrRet = array(
            'errno'     => $errno,
            'errmsg' => $errmsg,
            'data'   => $data,
        );
        return $arrRet;
    }
    /**
     * @desc 添加吧屏蔽
     * @param arrInput['forum_name']        : array  : 屏蔽吧名
     * @param arrInput['shield_start_time'] : int    : 开始屏蔽时间
     * @param arrInput['shield_end_time']   : int    : 结束屏蔽时间，-1表示不限
     * @param arrInput['client_types']      : string : 屏蔽端，若屏蔽多端，以逗号分隔，0：pc, 1: wap-ios, 2: wap-android, 3: app-ios, 4: app-android
     * @param arrInput['page_types']        : string : 屏蔽页面，若屏蔽多个，以逗号分隔，3:frs, 4: pb, 6: picture
     * @param arrInput['reason']            : string : 屏蔽原因
     * @param arrInput['op_name']           : string : 操作人名称
     * @return 错误返回
     */
    public static function addForumShield($arrInput) {
        $db = self::_getDB();
        if (null === $db) {
            Bingo_Log::warning('get db fail');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'init dl fail');
        }

        //开启事物
        if (false === $db->startTransaction()) {
            Bingo_Log::warning("db start transaction fail");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'db start transaction fail');
        }

        //先将相同吧名插入数据解除屏蔽
        $strForumNames = '';
        foreach ($arrInput['forum_name'] as $forum_name) {
            $strForumNames .= "'$forum_name',";
        }
        $strForumNames = substr($strForumNames, 0, strrpos($strForumNames, ','));
        $intDeleteStatus = self::$offline_shield;
        $intValidStatus = self::$online_shield;
        $strOpName = $arrInput['op_name'];
        $intOpTime = time();
        $sql = "update forum_shield_tbl set `status`=$intDeleteStatus, `op_name`='$strOpName', `op_time`=$intOpTime where `forum_name` in ($strForumNames) and status=$intValidStatus";
        $res = $db->query($sql);
        if (false === $res) {
            $db->rollback();
            Bingo_Log::warning('update db fail. sql[' . $sql . ']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'update db fail');
        }
        $sql = "select count(id) as valid_shield_num from forum_shield_tbl where status=1";
        $ret = $db->query($sql);
        if (false === $ret) {
            $db->rollback();
            Bingo_Log::warning("db query fail. sql[$sql]");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'db query fail');
        }

        $intCurShieldNum = $ret[0]['valid_shield_num'];
        $intCurInsertNum = count($arrInput['forum_name']);
        $intTotalShieldNum = $intCurShieldNum + $intCurInsertNum;
        if (self::MAX_SHIELD_FORUM_NUM < $intTotalShieldNum) {
            $db->rollback();
            Bingo_Log::warning('reach highest limit of shield num. current num[' . $intCurShieldNum . ']. insert num[' . $intCurInsertNum . ']');
            return self::_errRet(self::ERR_REACH_UPPER_LIMIT, 'reach highest limit of shield num');
        }

        //进行数据插入
        $insertVal = '';
        foreach ($arrInput['forum_name'] as $forum_name) {
            $shield_start_time = $arrInput['shield_start_time'];
            $shield_end_time = $arrInput['shield_end_time'];
            $client_types = $arrInput['client_types'];
            $page_types = $arrInput['page_types'];
            $reason = $arrInput['reason'];
            $status = self::$online_shield;
            $op_name = $arrInput['op_name'];
            $op_time = time();
            $insertVal .= "('$forum_name',$shield_start_time,$shield_end_time,'$client_types','$page_types','$reason',$status,'$op_name',$op_time),";
        }
        $insertVal = substr($insertVal, 0, strrpos($insertVal, ','));
        $sql = "insert into forum_shield_tbl (`forum_name`, `shield_start_time`, `shield_end_time`, `client_types`, `page_types`, `reason`, `status`,
`op_name`, `op_time`) values $insertVal";
        $ret = $db->query($sql);
        if (false === $ret) {
            $db->rollback();
            Bingo_Log::warning('insert db fail. sql[' . $sql . ']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'insert db fail');
        }

        //事物提交
        $db->commit();

        $data = array(
            'total_shield_num' => $intTotalShieldNum,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, 'success', $data);
    }

    /**
     * @desc 进行吧解除的db操作
     * @param ids      : string : 需要进行吧接触的所有屏蔽id，多个的话用逗号进行分隔
     * @param op_name  : string : 操作人
     * @return 错误返回
     */
    public static function removeForumShield($arrInput) {
        $db = self::_getDB();
        if (null === $db) {
            Bingo_Log::warning('init db fail');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'init db fail');
        }

        $op_name = $arrInput['op_name'];
        $op_time = time();
        $status = self::$offline_shield;
        $offlineIds = $arrInput['ids'];
        $sql = "update forum_shield_tbl set `op_name`='$op_name',`op_time`=$op_time,`status`=$status where id in ($offlineIds)";
        $ret = $db->query($sql);
        if (false === $ret) {
            Bingo_Log::warning("update db fail. sql[$sql]");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'update db fail');
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, 'succes');
    }
    /**
     * @desc 获取吧广告屏蔽列表
     * @param forum_name  : string : 吧名，若不为空进行模糊匹配，否则正常匹配
     * @param pn          : int    : 页面序号，从1开始
     * @param sz          : int    : 页面条数
     * @return pn         : int    : 页面序号，从1开始
     * @return totalCount : int    : 满足条件的总查询数目
     * @reutrn forumList  : array  : 屏蔽吧信息，具体内容参考接口文档：http://agroup.baidu.com/finishedfish/md/article/170998
     */
    public static function getForumShieldList($arrInput) {
        $db = self::_getDB();
        if (null === $db) {
            Bingo_Log::warning('init db fail');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'init db fail');
        }
        //满足条件的总查询数目
        $intValidShield = self::$online_shield;
        $sql = "select count(id) as total_num from forum_shield_tbl where status=$intValidShield";
        if (!empty($arrInput['forum_name'])) {
            $strForumName = $arrInput['forum_name'];
            $sql .= " and `forum_name` like '%$strForumName%'";
        }
        $ret = $db->query($sql);
        if (false === $ret) {
            Bingo_Log::warning('query fail. sql[' . $sql . ']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'db query fail');
        }
        $intTotalCount = $ret[0]['total_num'];
        //具体吧屏蔽信息查询
        $sql = "select id, forum_name as forumName, shield_start_time as shieldStartTime, shield_end_time as shieldEndTime, client_types as clientTypes, page_types as pageTypes, reason, op_name as opName, op_time as opTime from forum_shield_tbl where status=$intValidShield";
        if (!empty($arrInput['forum_name'])) {
            $strForumName = $arrInput['forum_name'];
            $sql .= " and `forum_name` like '%$strForumName%'";
        }

        $intOffset = ($arrInput['pn'] - 1) * $arrInput['sz'];
        $intNum = $arrInput['sz'];
        $sql .= " order by id desc limit $intOffset, $intNum";
        $ret = $db->query($sql);
        if (false === $ret) {
            Bingo_Log::warning('query fail. sql[' . $sql . ']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'db query fail');
        }

        $arrForumList = $ret;
        //进行吧格式话返回

        $data = array(
            'pn'         => $arrInput['pn'],
            'totalCount' => $intTotalCount,
            'forumList'  => $arrForumList,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, 'success', $data);
    }
    /**
     * @desc 获取所有吧屏蔽列表
     * @param null
     * @return 错误返回
     */
    public static function getAllForumShieldList($arrInput) {
        $db = self::_getDB();
        if (null === $db) {
            Bingo_Log::warning('init db fail');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'init db fail');
        }
        //具体吧屏蔽信息查询
        $intOffset = 0;
        $intCount = 1000;
        $intValidShield = self::$online_shield;
        $arrForumList = array();
        while (true) {
            $sql = "select id, forum_name as forumName, shield_start_time as shieldStartTime, shield_end_time as shieldEndTime, client_types as clientTypes, page_types as pageTypes, reason, op_name as opName, op_time as opTime from forum_shield_tbl where status=$intValidShield order by id asc limit $intOffset, $intCount";
            $ret = $db->query($sql);
            if (false === $ret) {
                Bingo_Log::warning('query fail. sql[' . $sql . ']');
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, 'db query fail');
            }

            $arrForumList = array_merge($arrForumList, $ret);
            if (count($ret) < $intCount) {
                break;
            } else {
                $intOffset += count($ret);
            }
        }
        //进行吧格式话返回
        $data = array(
            'forumList'  => $arrForumList,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, 'success', $data);
    }
}
