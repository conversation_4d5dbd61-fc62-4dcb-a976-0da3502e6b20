create database if not exists forum_beco default charset utf8;

use forum_beco;

CREATE TABLE IF NOT EXISTS `gd_cpm_finance_fundpool` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'unique id',
  `plan_id` bigint(20) NOT NULL COMMENT 'plan_id',
  `user_id` bigint(20) NOT NULL COMMENT 'Advertisers uid',
  `contract` varchar(100) NOT NULL DEFAULT '' COMMENT 'contract number',
  `match_code` varchar(20) NOT NULL DEFAULT '' COMMENT 'finance match code',
  `total_freeze` FLOAT(10,2) NOT NULL DEFAULT 0 COMMENT 'The total amount of freeze',
  `balance` FLOAT(10,2) NOT NULL DEFAULT 0.0 COMMENT 'balance of the fund pool',
  `freeze_unit` varchar(512) NOT NULL DEFAULT '' COMMENT 'json string, save finance api result',
  `op_log` varchar(1024) NOT NULL DEFAULT '' COMMENT 'json string, save operate log',
  `ext1` varchar(100) NOT NULL DEFAULT '' COMMENT 'extension field',
  `ext2` varchar(100) NOT NULL DEFAULT '' COMMENT 'extension field',
  PRIMARY KEY (`id`),
  UNIQUE KEY (`plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='gd cpm finance fundpool record' AUTO_INCREMENT=1;

INSERT INTO `gd_cpm_finance_fundpool`  (`plan_id`,`user_id`,`contract`,`match_code`,`total_freeze`,`balance`,`freeze_unit`,`op_log`)
  VALUES ($planId,$userId,$contract,$matchCode,$totalFreeze,$balance,$freezeUnit,$opLog);
SELECT `id`,`user_id`,`match_code`,`balance`,`op_log`
  FROM `gd_cpm_finance_fundpool`
    WHERE plan_id = $planid;
UPDATE `gd_cpm_finance_fundpool`
  SET `balance`=$balance,`freeze_unit`=$freezeUnit,`op_log`=$opLog
    WHERE plan_id=$planId;


CREATE TABLE IF NOT EXISTS `gd_cpm_finance_charge` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'unique id',
  `plan_id` bigint(20) NOT NULL COMMENT 'plan_id',
  `date` bigint(8) NOT NULL COMMENT 'Date of delivery and deduction',
  `should_deduct` float(10,2) NOT NULL DEFAULT 0  COMMENT 'should deduct amount',
  `actual_deduct` float(10,2) NOT NULL DEFAULT 0 COMMENT 'actual deduct amount',
  `is_succeed` tinyint NOT NULL DEFAULT 0 COMMENT 'is succeed',
  `result` VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'json string, save finance api result',
  `ext1` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'extension field',
  `ext2` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'extension field',
  PRIMARY KEY (`id`),
  UNIQUE KEY (`plan_id`,`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='gd cpm finance charge record' AUTO_INCREMENT=1;

INSERT INTO `gd_cpm_finance_charge` (`plan_id`,`date`,`should_deduct`,`actual_deduct`,`is_succeed`,`result`)
  VALUES ($planId,$date,$shouldDeduct,$actualDeduct,$isSucceed,$result);
SELECT `id`,`plan_id`,`date`,`should_deduct`,`actual_deduct`,`is_succeed`,`result`
  FROM `gd_cpm_finance_charge`
    WHERE `plan_id`=$planId
      AND `date`=$date;
SELECT `id`,`plan_id`,`date`,`should_deduct`,`actual_deduct`,`is_succeed`,`result`
  FROM `gd_cpm_finance_charge`
    WHERE `plan_id`=$planId;
SELECT `id`,`plan_id`,`date`,`should_deduct`,`actual_deduct`,`is_succeed`,`result`
  FROM `gd_cpm_finance_charge`
    WHERE `date`=$date;


CREATE TABLE IF NOT EXISTS `gd_charge_daily`(
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'primary id',
  `plan_id` bigint(10) NOT NULL default 0 COMMENT 'plan id',
  `unit_id` bigint(10) NOT NULL default 0 COMMENT 'unit id',
  `idea_id` bigint(10) NOT NULL default 0 COMMENT 'idea id',
  `exposure` int(10) NOT NULL DEFAULT 0 COMMENT 'exposure',
  `click` int(10) NOT NULL DEFAULT 0 COMMENT 'click',
  `cost` int(10) NOT NULL DEFAULT 0 COMMENT 'cost',
  `date` BIGINT(10) NOT NULL DEFAULT 0 COMMENT 'date',
  `op_time` int(10) NOT NULL DEFAULT 0 COMMENT 'operator  time',
  `ext_info1` varchar(1024) NOT NULL DEFAULT '' COMMENT 'ext info1',
  `ext_info2` varchar(1024) NOT NULL DEFAULT '' COMMENT 'ext info2',
  PRIMARY KEY (`id`),
  KEY `unit_id` (`unit_id`),
  UNIQUE KEY `idea_date` (`idea_id`, `date`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COMMENT 'gd_charge_daily';

SELECT `unit_id`, sum(`exposure`) AS all_cpm
  FROM gd_charge_daily
    WHERE unit_id IN($IdList)
      GROUP BY unit_id;
SELECT `unit_id`, `exposure` AS day_cpm
  FROM gd_charge_daily
    WHERE date = $current_date
      AND unit_id IN($IdList);
