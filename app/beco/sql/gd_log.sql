use forum_beco;
CREATE TABLE IF NOT EXISTS `gd_log` (
    `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'primary key',
    `module_id` INT(10) UNSIGNED NOT NULL COMMENT 'module id',
    `object_id` VARCHAR(32)  NOT NULL COMMENT 'object id',
    `action_type` INT (10) UNSIGNED NOT NULL COMMENT 'action type',
    `user_id` VARCHAR(24) NOT NULL COMMENT 'user id',
    `status` tinyint NOT NULL DEFAULT 0 COMMENT 'action is success, 0 for fail ,1 for success ',
    `detail` text NOT NULL DEFAULT '' COMMENT 'detail',
    `ip` INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'source ip',
    `create_time` INT(10) UNSIGNED NOT NULL COMMENT 'created time',
    PRIMARY KEY `id` (`id`),
    KEY `module_id` (`module_id`,`object_id`),
    KEY `user_id` (`user_id`),
    KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT 'gd_log';
