DROP TABLE `rbac_role_info`;

DROP TABLE `rbac_user_role_relation`;

DROP TABLE `rbac_perm_info`;

CREATE TABLE IF NOT EXISTS `rbac_role_info` (
    `role_id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'primary key',
    `role_name` VARCHAR(100) NOT NULL COMMENT 'role_name',
    `create_time` INT(10) UNSIGNED NOT NULL COMMENT 'time',
    `op_user` VARCHAR(100) NOT NULL COMMENT 'op_user',
    `op_time` INT(10) UNSIGNED NOT NULL COMMENT 'time',
    `parrent_role_id` BIGINT(20) UNSIGNED NOT NULL COMMENT 'parrent role id',
    `parrent_role_name` VARCHAR (100) NOT NULL COMMENT 'parrent role name',
    `parrent_role_list` VARCHAR(200) NOT NULL COMMENT 'string',
    `perm_list` VA<PERSON>HA<PERSON>(2000) NOT NULL COMMENT 'allow more than 100 perm',
    `ext_info` VARCHAR(1000) NOT NULL DEFAULT '' COMMENT 'extra info',
    PRIMARY KEY `role_id` (`role_id`),
    UNIQUE KEY `basic_index` (`parrent_role_id`, `role_name`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT 'rbac_role_info';

CREATE TABLE IF NOT EXISTS `rbac_user_role_relation` (
    `user_id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'primary key',
    `user_name` VARCHAR(100) NOT NULL COMMENT 'user_name',
    `role_id` INT(10) UNSIGNED NOT NULL COMMENT 'role_id',
    `create_time` INT(10) UNSIGNED NOT NULL COMMENT 'time',
    `op_user` VARCHAR(100) NOT NULL COMMENT 'op_user',
    `op_time` INT(10) UNSIGNED NOT NULL COMMENT 'time',
    `ext_info` VARCHAR(1000) NOT NULL DEFAULT '' COMMENT 'extra info',
    PRIMARY KEY `user_id` (`user_id`),
    KEY `role_id` (`role_id`),
    UNIQUE KEY `user_name` (`user_name`, `role_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT 'rbac_user_role_relation';

CREATE TABLE IF NOT EXISTS `rbac_perm_info` (
    `perm_id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'primary key',
    `perm_name` VARCHAR(100) NOT NULL COMMENT 'perm_name',
    `module_name` VARCHAR(20) NOT NULL COMMENT 'module_name',
    `media_name` VARCHAR(20) NOT NULL COMMENT 'media_name',
    `operator_name` VARCHAR(20) NOT NULL COMMENT 'operator_name',
    `create_time` INT(10) UNSIGNED NOT NULL COMMENT 'time',
    `op_user` VARCHAR(100) NOT NULL COMMENT 'op_user',
    `op_time` INT(10) UNSIGNED NOT NULL COMMENT 'time',
    `ext_info` VARCHAR(1000) NOT NULL DEFAULT '' COMMENT 'extra info',
    PRIMARY KEY `perm_id` (`perm_id`),
    UNIQUE KEY `perm_name` (`perm_name`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT 'rbac_perm_info';


DROP TABLE `user_info`;
CREATE TABLE IF NOT EXISTS `user_info` (
`user_id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'primary key',
`user_name` VARCHAR(100) NOT NULL COMMENT 'user_name',
`create_time` INT(10) UNSIGNED NOT NULL COMMENT 'time',
`op_user` VARCHAR(100) NOT NULL COMMENT 'op_user',
`op_time` INT(10) UNSIGNED NOT NULL COMMENT 'time',
`ext_info` VARCHAR(1000) NOT NULL DEFAULT '' COMMENT 'extra info',
PRIMARY KEY `user_id` (`user_id`),
UNIQUE KEY `user_name` (`user_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT 'user_info';

