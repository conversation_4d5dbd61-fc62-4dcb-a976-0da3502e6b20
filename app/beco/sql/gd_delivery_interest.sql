use forum_beco;
CREATE TABLE IF NOT EXISTS `gd_delivery_interest` (
    `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'primary key',
    `plan_id` BIGINT(20) NOT NULL DEFAULT 0 COMMENT 'plan id',
    `unit_id` BIGINT(20) NOT NULL DEFAULT 0 COMMENT 'unit id',
    `event_day` BIGINT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'date:20170612',
    `interest_id` BIGINT(12) NOT NULL DEFAULT 0 COMMENT 'interest id',
    `name_l1` varchar(64) NOT NULL DEFAULT '' COMMENT 'interest name level 1',
    `name_l2` varchar(64) NOT NULL DEFAULT '' COMMENT 'interest name level 2',
    `shows` BIGINT(15) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'show times',
    `clicks` BIGINT(15) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'click times',
    <PERSON>IMAR<PERSON> KEY `id` (`id`),
    KEY `plan_id_date` (`plan_id`, `event_day`),
    KEY `unit_id_date` (`unit_id`, `event_day`),
    KEY `interest_id` (`interest_id`),
    KEY `name_l1` (`name_l1`),
    KEY `name_l2` (`name_l2`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT 'gd_delivery_interest';


INSERT INTO
  `gd_delivery_interest`
  (`plan_id`, `unit_id`, `event_day`, `interest_id`, `name_l1`, `name_l2`, `shows`, `clicks`)
VALUES
  ($plan_id, $unit_id, $event_day, $int_id, $int_l1, $int_l2, $shows, $clicks);


SELECT
  `interest_id`,SUM(`shows`) as total_show, SUM(`clicks`) as total_click
FROM
  `gd_delivery_interest`
WHERE `plan_id` = $plan_id
AND `event_day` >= $day_start
AND `event_day` <= $day_end
GROUP BY `interest_id`;


SELECT
  `interest_id`,SUM(`shows`) as total_show, SUM(`clicks`) as total_click
FROM
  `gd_delivery_interest`
WHERE `unit_id` = $unit_id
AND `event_day` >= $day_start
AND `event_day` <= $day_end
GROUP BY `interest_id`;


SELECT
  `interest_id`,SUM(`shows`) as total_show, SUM(`clicks`) as total_click
FROM
  `gd_delivery_interest`
WHERE `plan_id` = $plan_id
AND `event_day` >= $day_start
AND `event_day` <= $day_end
AND `interest_id` in ($id1, $id2, $id3)
GROUP BY `interest_id`;


SELECT
  `interest_id`,SUM(`shows`) as total_show, SUM(`clicks`) as total_click
FROM
  `gd_delivery_interest`
WHERE `unit_id` = $unit_id
AND `event_day` >= $day_start
AND `event_day` <= $day_end
AND `interest_id` in ($id1, $id2, $id3)
GROUP BY `interest_id`;


SELECT
  `name_l1`,SUM(`shows`) as total_show, SUM(`clicks`) as total_click
FROM
  `gd_delivery_interest`
WHERE `plan_id` = $plan_id
AND `event_day` >= $day_start
AND `event_day` <= $day_end
AND `name_l1` in($name1, $name2, $name3)
GROUP BY `name_l1`;


SELECT
  `name_l1`,SUM(`shows`) as total_show, SUM(`clicks`) as total_click
FROM
  `gd_delivery_interest`
WHERE `unit_id` = $unit_id
AND `event_day` >= $day_start
AND `event_day` <= $day_end
AND `name_l1` in($name1, $name2, $name3)
GROUP BY `name_l1`;


SELECT
  `name_l2`,SUM(`shows`) as total_show, SUM(`clicks`) as total_click
FROM
  `gd_delivery_interest`
WHERE `plan_id` = $plan_id
AND `event_day` >= $day_start
AND `event_day` <= $day_end
AND `name_l2` in($name1, $name2, $name3)
GROUP BY `name_l2`;


SELECT
  `name_l2`,SUM(`shows`) as total_show, SUM(`clicks`) as total_click
FROM
  `gd_delivery_interest`
WHERE `unit_id` = $unit_id
AND `event_day` >= $day_start
AND `event_day` <= $day_end
AND `name_l2` in($name1, $name2, $name3)
GROUP BY `name_l2`;