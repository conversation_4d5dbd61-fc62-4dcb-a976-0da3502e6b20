create database if not exists forum_beco default charset utf-8;

use forum_beco;

CREATE TABLE IF NOT EXISTS `idea_pending_shield_tbl` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `channel_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '渠道id',
    `idea_key` varchar(100) NOT NULL DEFAULT '' COMMENT '物料id',
    `idea_title` varchar(150) NOT NULL DEFAULT '' COMMENT '物料标题',
    `idea_url` varchar(500) NOT NULL DEFAULT '' COMMENT '物料URL',
    `op_time` int(11) NOT NULL DEFAULT 0 COMMENT '操作时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='待下线badcase物料表';