create database if not exists forum_beco default charset utf8;

use forum_beco;

CREATE TABLE IF NOT EXISTS `idea_list` (
    `idea_id` BIGINT(20) unsigned NOT NULL auto_increment COMMENT "落地页ID",
    `status` TINYINT(4) unsigned NOT NULL COMMENT "落地页状态",
    `idea_name` VARCHAR(64) NOT NULL COMMENT "创意名称",
    `ext` VARCHAR(128) NOT NULL DEFAULT '' COMMENT "页面扩展字段",
    `idea_data` VARCHAR(16384) NOT NULL COMMENT "页面数据",
    `user_id` VARCHAR(64) NOT NULL COMMENT "最近操作用户",
    `create_user_id` VARCHAR(64) NOT NULL COMMENT "创建用户",
    `user_level` TINYINT(4) unsigned NOT NULL COMMENT "用户等级",
    `op_time` bigint NOT NULL DEFAULT 0 COMMENT "最近操作时间",
    `user_name` VARCHAR(32) NOT NULL COMMENT "用户名",
    PRIMARY KEY(`idea_id`),
    <PERSON>EY `query_user_id`(`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET='utf8' COMMENT='idea_list';

CREATE TABLE IF NOT EXISTS `user_media_list` (
    `resource_id` BIGINT(20) unsigned NOT NULL auto_increment COMMENT "资源ID",
    `user_id` VARCHAR(64) NOT NULL COMMENT "用户",
    `media_id` VARCHAR(128) NOT NULL COMMENT "媒体资源",
    `media_status` VARCHAR(64) NOT NULL COMMENT "媒体状态",
    `media_ext` VARCHAR(1024) NOT NULL COMMENT "扩展字段",
    `op_time` bigint NOT NULL DEFAULT 0 COMMENT "最近操作时间",
    PRIMARY KEY(`resource_id`),
) ENGINE=InnoDB DEFAULT CHARSET='utf8' COMMENT='user_media_list';

CREATE TABLE IF NOT EXISTS `idea_op_record` (
    `record_id` BIGINT(20) unsigned NOT NULL auto_increment COMMENT "记录ID",
    `record_data` VARCHAR(16384) NOT NULL COMMENT "记录数据",
    `record_time` bigint NOT NULL COMMENT "记录时间",
    `record_user_id` BIGINT(20) unsigned NOT NULL COMMENT "记录用户ID",
    `record_user_name` VARCHAR(32) NOT NULL COMMENT "记录用户名",
    PRIMARY KEY(`record_id`)
) ENGINE=InnoDB DEFAULT CHARSET='utf8' COMMENT="操作记录";