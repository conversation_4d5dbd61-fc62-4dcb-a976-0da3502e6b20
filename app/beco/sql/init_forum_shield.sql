create database if not exists forum_beco default charset utf-8;

use forum_beco;

CREATE TABLE IF NOT EXISTS `forum_shield_tbl` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'forum shield id',
    `forum_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'forum name',
    `shield_start_time` INT(10) NOT NULL DEFAULT 0 COMMENT 'shield start time',
    `shield_end_time` INT(10) NOT NULL DEFAULT 0 COMMENT 'shield end time',
    `client_types` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'shield client types, 0: pc, 1: wap-ios, 2: wap-android, 3: app-ios, 4: app-android, implode with ,',
    `page_types` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'shield page types, 3: frs, 4: pb, 6: picture, implode with ,',
    `reason` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'shield reason',
    `status` TINYINT(4) NOT NULL DEFAULT 0 COMMENT '0: delete, 1: valid',
    `op_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'operate user name',
    `op_time` INT(10) NOT NULL DEFAULT 0 COMMENT 'operate time',
    PRIMARY KEY (`id`),
    KEY `status_forum` (`status`, `forum_name`)
) ENGINE=InnoDB DEFAULT CHARSET='utf8' COMMENT='forum shield table';
