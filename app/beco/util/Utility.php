<?php

/**
 * @name Util_Utility
 * @desc 公用工具类
 * @date 2017-04-14 16:13
 * <AUTHOR>
 */
class Util_Utility {

    /**
     * 构造返回信息
     * 
     * @param int    $errno   状态码
     * @param string $errmsg  信息
     * @param array  $data    数据
     * @return array
     */
    public static function arrRet($errno = Tieba_Errcode::ERR_SUCCESS, $errmsg = '', $data = null) {

        $arrRet = array(
            'errno' => $errno,
            'errmsg' => $errmsg,
        );

        if ($data !== null) {
            $arrRet['data'] = $data;
        }

        return $arrRet;
    }
    
    /**
     * 获得数组的列
     *
     * @param array  $arr 数组
     * @param string $key 需要获取的列名
     * @return array
     */
    public static function getColumn($arr, $key) {

        $ret = array();
        foreach ($arr as $item) {
            $ret[] = $item[$key];
        }

        return $ret;
    }
    
    /**
     * 构建关联数组，对于一个key，只设置一个值
     * 
     * @param array  $arr 数字
     * @param string $key 设置的键
     * @param string $val 设置的值
     * @return array
     */
    public static function arrayAssoc($arr, $key, $val = null) {

        $ret = array();
        foreach ($arr as $item) {
            if ($val === null) {
                $ret[$item[$key]] = $item;
            } else {
                $ret[$item[$key]] = $item[$val];
            }
        }
        
        return $ret;
    }
}