<?php
/**
* brief of sample.php:
* 
* <AUTHOR> @date 2009/07/26 21:03:27
* @version $Revision: 1.2 $ 
* @todo 
*/
require_once("../ConnectionMan.class.php");
$arrServersSock = array(
    0 => array('host'=>'127.0.0.1', 'port'=>9000),
    1 => array('host'=>'*********', 'port'=>9000),
    2 => array('host'=>'*********', 'port'=>9000),
);
$arrServersMysql = array(
    0 => array('host'=>'127.0.0.1', 'port'=>3306),
    1 => array('host'=>'*********', 'port'=>3306),
    2 => array('host'=>'*********', 'port'=>3333),
    3 => array('host'=>'*********', 'port'=>3344),
    4 => array('host'=>'*********', 'port'=>3355),
    5 => array('host'=>'*********', 'port'=>3366),
    6 => array('host'=>'*********', 'port'=>3377),
    7 => array('host'=>'*********', 'port'=>3388),
    8 => array('host'=>'*********', 'port'=>3399),
);

$arrAuth = array(
    'dbuser' => 'read',
    'dbpass' => 'read',
    'dbname' => 'misd',
);

$arrStrategy = array('name'=>'State');
/*
$arrStrategyConfig = array(
    'StrategyState_MIN_SCORE' => 900,
    'StrategyState_MAX_SCORE' => 1010,
    'StrategyState_SCORE_ADD' => 5,
    'StrategyState_SCORE_SUB' => 50,
    'StrategyState_TIME_INTERVAL_SEC' => 60,
);
$arrStrategy['config'] = $arrStrategyConfig;
*/


$selServer = false;
//$fd = ConnectionMan::getMysqli($selServer, $arrServersMysql, $arrAuth, 100, $arrStrategy);
//$fd = ConnectionMan::getMysqli($selServer, $arrServersMysql, $arrAuth);
//$fd = ConnectionMan::getSocket($selServer, $arrServersSock, 1000, $arrStrategy);
//$fd = ConnectionMan::getSocket($selServer, $arrServersSock);

echo "connect result ------------------------>\n";
var_dump($fd);
var_dump($selServer);

echo "set unavailable-------------------->\n";
//$bol = ConnectionMan::setUnavailable($selServer);
//var_dump($bol);

echo "end------------------->\n";

?>
