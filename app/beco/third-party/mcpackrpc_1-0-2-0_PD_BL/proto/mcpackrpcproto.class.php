<?php
/***************************************************************************
 * 
 * Copyright (c) 2009 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file mcpackrpcproto.class.php
 * <AUTHOR>
 * @date 2009/07/23 08:47:28
 * @brief 
 *  
 **/
require_once(dirname(__FILE__).'/../rpcerror.class.php');
require_once(dirname(__FILE__).'/socket.class.php');

require_once(dirname(__FILE__).'/../../phpconnectpool/ConnectionMan.class.php');

class McpackRpcProto
{
    private static $_errInfo;

    public static function getLastError()
    {
        return self::$_errInfo;
    }

    public static function setLastError($err)
    {
        self::$_errInfo = $err;
    }

    private static $_id;
    public static function setId($id)
    {
        self::$_id = $id;
    }

    public static function getId()
    {
        return self::$_id;
    }
    /**
     * ��ȡ�������ת��Ϊ��Ӧ�������
     *
     * ����Ϊfalse��ʾת��ʧ��, true��ʾ�ɹ�
     **/
    public static function Serializer($arrInput, &$send, 
        $rpcServer = null, $rpcMethod = null, $arrConf = null)
    {

        //���ݰ�
        $id = rand();
        $arrPack =  array(
            'header' => array(
                    'content_type' => 'McpackRpc',
                ),
            'content' => array(
                array(
                    'method' => $rpcMethod,
                    'id' => "$id",
                ),
            ),   
        );
        self::setId($id);

        if ($arrInput != null && !empty($arrInput)) {
            $arrPack['content'][0]['params'] = $arrInput;
        }

        if (isset($arrConf['output'])) 
        {
            $arrResultParams = array();
            foreach($item as $arrConf['output'])
            {
                $arrResultParams[$item] = true;

            }
            if (!empty($arrResultParams)) {
                $arrPack['content'][0]['result_params'] = $arrResultParams;
            }
        }


        if (isset($arrConf['version'])) {
            $arrPack['content']['version'] = $arrConf['version'];
        }

        //mcpack���ݴ��
        if (isset($arrConf['McpackVersion']) && isset($arrConf['McpackSize'])) {
            $arr = mc_pack_array2pack($arrPack, $arrConf['McpackSize'], $arrConf['McpackVersion']);
        } else if (isset($arrConf['McpackVersion'])) {
            $arr = mc_pack_array2pack($arrPack, $arrConf['McpackVersion']);
        } else if (isset($arrConf['McpackSize'])) {
            $arr = mc_pack_array2pack($arrPack, $arrConf['McpackSize']);
        } else {
            $arr = mc_pack_array2pack($arrPack);
        }
        if (!$arr) {
            return false;
        }
        $bodylen = strlen($arr);
        $logid = 0;
        if (isset($arrConf['logid'])) {
            $logid = $arrConf['logid'];
        } else {
            $logid = rand();
        }
        $provider = McpackRpc::MCPACK_RPC_NAME;
        if (isset($arrConf['provider'])) {
            $provider = $arrConf['provider'];
        }


        $nshead = pack("SSIa16III", 
            0, //id 
            McpackRpc::MCPACK_RPC_VERSION, //version
            $logid, //logid
            $provider, //provider
            0xfb709394, //magic_num
            0, //reserved
            $bodylen);
        $send = $nshead.$arr;
        return true;
    }

    /**
     * ����ȡ��������ת��Ϊ$Output ��array
     **/ 
    public static function Deserializer($recv, &$Output, &$result,
       $rpcServer = null, $rpcMethod = null, $arrConf = null)
    {
        if (strlen($recv) == 0) {
            self::setLastError(McpackRpcError::$INVALID_RES);
            return false;
        }
        $head_arr = unpack("Sid/Sversion/Ilogid/a16provider/Imagic_num/Ireserved/Ibody_len",
            $recv);
        if (!$head_arr) {
            return false;
        }
        $arr = mc_pack_pack2array(substr($recv, 36, $head_arr['body_len']));
        if (!$arr) {
            self::setLastError(McpackRpcError::$PARSE_ERROR);
            return false;
        }

        if (!isset($arr['content'])) {
            self::setLastError(McpackRpcError::$CONTENT_NOT_FOUND);
            return false;
        }

        $arrResult = $arr['content'][0];
        if (!isset($arrResult['id'])) {
            //UB_LOG_WARNING("McpackRpc Error id is not found");
            self::setLastError(McpackRpcError::$ID_NOTFOUND);
            return false;
        }
        if ($arrResult['id'] != self::getId()) {
            self::setLastError(McpackRpcError::$ID_NOTMATCH);
            return false;
        }

        //error
        if (isset($arrResult['error'])) {
            if (isset($arrResult['error']['message'])) {
                /*
                UB_LOG_WARNING("McpackRpc Error %s %s",
                    $arrResult['error']['message'],
                    $arrResult['error']['data']);
                 */
                self::setLastError($arrResult['error']);
            } else {
                self::setLastError(McpackRpcError::$UNKNOW_ERROR);
            }
            return false;
        }

        if (isset($arrResult['result'])) {
            $result = $arrResult['result'];
        } else {
            $result = null;
        }
        if (isset($arrResult['result_params'])) {
            $Output = $arrResult['result_params'];
        } else {
            $Output = null;
        }

        return true;
    }

    public static function TalkWith($send, &$recv, $mac)
    {
        $read_timeout_ms = 1000;
        $write_timeout_ms = 1000;
        $connect_timeout_ms  = 1000;
        if (isset($mac['talk']['read_timeout_ms'])) {
            $read_timeout_ms = $mac['talk']['read_timeout_ms'];
        }
        if (isset($mac['talk']['write_timeout_ms'])) {
            $write_timeout_ms = $mac['talk']['write_timeout_ms'];
        }
        if (isset($mac['talk']['connect_timeout_ms'])) {
            $connect_timeout_ms = $mac['talk']['connect_timeout_ms'];
        }

        $socketfd = null;
        //����Ѿ�����connectpool
        if (isset($mac['machine'])) {
            if (isset($mac['machine']['socket'])) {
                $socketfd = $mac['machine']['socket'];
            }
        }

        //��connectpool��connectpool
        $objSock = new CPHPRPCSocketWrapper ($read_timeout_ms, $write_timeout_ms, $connect_timeout_ms, $socketfd);
        $arrErr = null;
        if (null == $socketfd) {
            $retry = 1;
            if (isset($mac['talk']['retry'])) {
                $retry = $mac['talk']['retry'];
            }
            $arrErr = null; 
            for ($i = 0; $i < $retry; $i++) {
                $arrErr = null; 
                if ($objSock->connect($mac['machine']['host'], $mac['machine']['port']) == false) {
                    $arrErr = $objSock->getLastError();
                } else {
                    /*
                    if ($i > 0) {
                        //UB_LOG_WARNING("connect try time is %d", $i+1);
                    }*/
                    break;
                }
            }
        }

        if ($arrErr === null && $objSock->send($send, strlen($send)) == false) {
            $arrErr = $objSock->getLastError();
        }
        if ($arrErr === null) {

            //nshead 36
            $nshead = $objSock->receive(36);
            if ($nshead === false) 
            {
                $arrErr = $objSock->getLastError();
            } else {
                $recv = $nshead;
                $head_arr = unpack("Sid/Sversion/Ilogid/a16provider/Imagic_num/Ireserved/Ibody_len",
                    $nshead);
                $data = $objSock->receive($head_arr['body_len']);
                if ($data) 
                {
                    $recv = $recv.$data;
                }
            }
        }
        if ($arrErr) {
            self::setLastError(
                array(
                    'message' => $arrErr[1],
                )
            );
            return false;
        }
        return true;
 
    } 
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100 */
?>
