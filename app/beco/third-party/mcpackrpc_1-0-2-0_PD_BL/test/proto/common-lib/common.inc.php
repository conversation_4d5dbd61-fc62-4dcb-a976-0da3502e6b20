<?php
/**
 * @brief common include header, require this first
 *
 * do global defines, logid/client ip/...
 * @version for php 5+
**/

class CCommon // used as a namespace
{
    public static function getLogID()
    {
        $arr = gettimeofday();
        return ((($arr['sec']*100000 + $arr['usec']/10) & 0x7FFFFFFF) |
                0x80000000);
    }

    public static function getClientIP()
    {
        if ($_SERVER["HTTP_X_FORWARDED_FOR"]) {
            $ip = $_SERVER["HTTP_X_FORWARDED_FOR"];
        } elseif ($_SERVER["HTTP_CLIENT_IP"]) {
            $ip = $_SERVER["HTTP_CLIENT_IP"];
        } elseif ($_SERVER["REMOTE_ADDR"]) {
            $ip = $_SERVER["REMOTE_ADDR"];
        } elseif (getenv("HTTP_X_FORWARDED_FOR")) {
            $ip = getenv("HTTP_X_FORWARDED_FOR");
        } elseif (getenv("HTTP_CLIENT_IP")) {
            $ip = getenv("HTTP_CLIENT_IP");
        } elseif (getenv("REMOTE_ADDR")) {
            $ip = getenv("REMOTE_ADDR");
        } else {
            $ip = "0.0.0.0";
        }

        $pos = strpos($ip, ',');
        if ($pos > 0) $ip = substr($ip, 0, $pos);
        return trim($ip);
    }

    public static function getHostname()
    {
        return $_ENV['HOSTNAME'];
    }

    public static function numToIP($num)
    {
        $tmp = (double)$num;
        return sprintf('%u.%u.%u.%u', $tmp & 0xFF, (($tmp >> 8) & 0xFF),
                (($tmp >> 16) & 0xFF), (($tmp >> 24) & 0xFF));
    }

    public static function ipToNum($ip)
    {
        if (!preg_match('/^([0-9]+)\.([0-9]+)\.([0-9]+)\.([0-9]+)$/is', $ip)) {
            return 0;
        }
        $n = ip2long($ip);
        /** convert to network order */
        $n =       (($n & 0xFF) << 24)
                | ((($n >> 8) & 0xFF) << 16)
                | ((($n >> 16) & 0xFF) << 8)
                | (($n >> 24) & 0xFF);
        return $n;
    }

    public static function padString($str, $len, $bolCut, $ch = "\0")
    {
        if (strlen($str) >= $len) {
            $str = $bolCut ? substr($str, 0, $len-1) . $ch : substr($str, 0, $len);
        } else {
            $str = str_pad($str, $len, $ch);
        }
        return $str;
    }

    public static function getQidFromUrl($strUrl)
    {
        if (preg_match(QB_URL_PATTERN, $strQUrl, $arrMatched) > 0) {
            return intval($arrMatched[3]);
        }
        return 0;
    }

    // example: column#1002031 /image/c/001/002/031.jpg
    public static function getImagePath($strImagePath, $intImageID, $intType)
    {
        $chType = ($intType == 1) ? 's' : 'c';
        $strPath = $strImagePath . $chType . '/';
        $intImageID = floatval($intImageID);
        $len = strlen($intImageID);
        $len = (0 == $len % 3) ? 0 : 3 - $len % 3;
        $strImageID = str_repeat('0', $len) . $intImageID;
        for ($i = 0, $len = strlen($strImageID) / 3; $i < $len - 1; $i++) {
            $strPath .= substr($strImageID, $i*3, 3) . '/';
        }
        $strPath .= substr($strImageID, $len*3 - 3, 3) . '.jpg';
        return $strPath;
    }

    public static function getUserImageUrl($strUrlPattern, $arrUserInfo)
    {
        $intTitle   =   intval($arrUserInfo['title']);
        if ($intTitle < 0 || $intTitle > 4) {
            $intTitle = 0;
        }
        $intSex     =   intval($arrUserInfo['sex']);
        if ($intSex < 0 || $intSex > 2) {
            $intSex = 0;
        }
        $intLevel   =   intval($arrUserInfo['iklevel']);
        if ($intLevel < 1 || $intLevel > 18) {
            $intLevel = 1;
        }
        return sprintf($strUrlPattern, $intTitle, $intSex, $intLevel);
    }

    public static function getTmpImagePath($strTmpImagePath, $intImageID)
    {
        return sprintf('%s%d.jpg', $strTmpImagePath, $intImageID);
    }

    public static function getIknowTitle($intLevel, $intTitle)
    {
        require_once ABS_PATH . 'common-lib/conf/scoreconf.inc.php';
        return $GLOBALS['IKNOW_TITLE_CONF'][$intLevel][$intTitle];
    }

    public static function getIknowLevel($intLevel)
    {
        require_once ABS_PATH . 'common-lib/conf/scoreconf.inc.php';
        return $GLOBALS['IKNOW_LEVEL_CONF'][$intLevel];
    }

    public static function getIknowIntLevel($intScore)
    {
        require_once ABS_PATH . 'common-lib/conf/scoreconf.inc.php';
        foreach ($GLOBALS['IKNOW_SCORE_CONF'] as $key => $value) {
            if ($intScore <= $value) return $key;
        }
        return 1;
    }

    public static function getMasterIntLevel($intScore)
    {
        require_once ABS_PATH . 'common-lib/conf/scoreconf.inc.php';
        foreach ($GLOBALS['MASTER_SCORE_CONF'] as $key => $value) {
            if ($intScore <= $value) return $key;
        }
        return 1;
    }

    public static function getUserTotalScore($intUid, $strUserName, $arrUscoreConf)
    {
        require_once    ABS_PATH . 'common-lib/struct/uscore.class.php';

        $intUid     =   intval($intUid);
        $strUserName=   trim($strUserName);
        if (0 == $intUid && $strUserName == '') {
            CLog::warning(sprintf('%s: try get uscore without uid or username',
                __METHOD__));
            return false;
        }

        $intToTry   =   count($arrUscoreConf);
        $intTried   =   0;
        $intIndex   =   ($intUid > 0) ? $intUid : ord($strUserName{0});
        $intIndex   =   $intIndex % $intToTry;
        $arrConf    =   $arrUscoreConf[$intIndex];

        while ($intTried < $intToTry) {
            $res = CUscore::getTotalScore($intUid, $strUserName, $arrConf['host'],
                $arrConf['port'], $arrConf['timeout']);
            if ($res instanceof CUscoreRes
                && $res->isValid() && 0 == $res->intErrorNo) {
                return $res;
            } else {
                $intTried++;
                CLog::warning(sprintf(
                    '%s: error getting uscore of (%s:%s) from %s:%s, ' .
                    '%sth try of %s total',
                    __METHOD__, $intUid, $strUserName, $arrConf['host'],
                    $arrConf['port'], $intTried, $intToTry));
                $intIndex   =   ($intIndex + 1) % $intToTry;
                $arrConf    =   $arrUscoreConf[$intIndex];
            }
        }

        return false;
    }

    public static function getUserDetailScore($intUid, $strUserName, $arrUscoreConf,
        $bolNeedSex = false, $intIID = false)
    {
        require_once    ABS_PATH . 'common-lib/struct/uscore.class.php';

        $intUid     =   intval($intUid);
        $strUserName=   trim($strUserName);
        if (0 == $intUid && $strUserName == '') {
            CLog::warning(sprintf('%s: try get uscore without uid or username',
                __METHOD__));
            return false;
        }

        $intToTry   =   count($arrUscoreConf);
        $intTried   =   0;
        $intIndex   =   ($intUid > 0) ? $intUid : ord($strUserName{0});
        $intIndex   =   $intIndex % $intToTry;
        $arrConf    =   $arrUscoreConf[$intIndex];

        while ($intTried < $intToTry) {
            if ($intIID === false) {
                $res = CUscore::getDetailScore($intUid, $strUserName, $arrConf['host'],
                    $arrConf['normalport'], $arrConf['timeout'], $bolNeedSex);
            } else {
                $res = CUscore::getDetailScore($intUid, $strUserName, $arrConf['host'],
                    $arrConf['normalport'], $arrConf['timeout'], $bolNeedSex, $intIID);
            }
            if ($res instanceof CUscoreDetailRes
                && $res->isValid() && 0 == $res->intErrorNo) {
                $arrDetailScore = $res->getMasterScore();
                $arrDetailScore['total']    =   $res->intTotalScore;
                $arrDetailScore['score']    =   $res->intScore;
                $arrDetailScore['sex']      =   $res->intSex;
                $arrDetailScore['level']    =   CCommon::getMasterIntLevel($res->intScore);
                $arrDetailScore['iklevel']  =   CCommon::getIknowIntLevel($res->intTotalScore);
                $arrDetailScore['titlestr'] =   sprintf('%s&nbsp;&nbsp;%s',
                    CCommon::getIknowTitle(
                        $arrDetailScore['iklevel'], $arrDetailScore['title']
                    ),
                    CCommon::getIknowLevel($arrDetailScore['iklevel']));
                $arrDetailScore['detail']   =   $res->arrDetailScores;
                return $arrDetailScore;
            } else {
                $intTried++;
                CLog::warning(sprintf(
                    '%s: error getting uscore of (%s:%s) from %s:%s, ' .
                    '%sth try of %s total',
                    __METHOD__, $intUid, $strUserName, $arrConf['host'],
                    $arrConf['port'], $intTried, $intToTry));
                $intIndex   =   ($intIndex + 1) % $intToTry;
                $arrConf    =   $arrUscoreConf[$intIndex];
            }
        }

        return array('titlestr' =>  sprintf('%s&nbsp;&nbsp;%s',
            CCommon::getIknowTitle(1, 0), CCommon::getIknowLevel(1)));
    }

    public static function getUserInfo($intUid, $strUserName, $arrUinfoConf)
    {
        require_once    ABS_PATH . 'common-lib/struct/uinfo.class.php';

        $intUid     =   intval($intUid);
        $strUserName=   trim($strUserName);
        if (0 == $intUid && $strUserName == '') {
            CLog::warning(sprintf('%s: try get uinfo without uid or username',
                __METHOD__));
            return false;
        }

        $intToTry   =   count($arrUinfoConf);
        $intTried   =   0;
        $intIndex   =   ($intUid > 0) ? $intUid : ord($strUserName{0});
        $intIndex   =   $intIndex % $intToTry;
        $arrConf    =   $arrUinfoConf[$intIndex];

        while ($intTried < $intToTry) {
            $res = CUinfo::getUserInfo($intUid, $strUserName, $arrConf['host'],
                $arrConf['port'], $arrConf['timeout']);
            if ($res instanceof CUinfoRes &&
                $res->isValid() && 0 == $res->intErrorNo) {
                return array(
                    'uid'   =>  $res->intUserID,
                    'user'  =>  $res->strUserName,
                    'sex'   =>  $res->intSex,
                );
            } else {
                $intTried++;
                CLog::warning(sprintf(
                    '%s: error getting uinfo of (%s:%s) from %s:%s, ' .
                    '%sth try of %s total',
                    __METHOD__, $intUid, $strUserName, $arrConf['host'],
                    $arrConf['port'], $intTried, $intToTry));
                $intIndex   =   ($intIndex + 1) % $intToTry;
                $arrConf    =   $arrUinfoConf[$intIndex];
            }
        }

        return false;
    }

    public static function getLoginUserInfo()
    {
        require_once ABS_PATH . 'common-lib/iknowlogin.class.php';

        $objILogin = new CIknowLogin(SESS_ID);

        $uinfo = $objILogin->getUserInfo($GLOBALS['SESSION'], $GLOBALS['QUSER'],
                $GLOBALS['QCM'], $GLOBALS['USCORE']);
        if (!$objILogin->isLoggedIn() || !is_object($uinfo)) {
            return array('uid'  =>  0);
        } else {
            $intLevel = CCommon::getIknowIntLevel($uinfo->totalscore);
            return array(
                'uid'           =>  $uinfo->uid,
                'user'          =>  $uinfo->username,
                'userEnc'       =>  urlencode($uinfo->username),
                'usertitle'     =>  CCommon::getIknowTitle($intLevel, $uinfo->title) .
                                        "&nbsp;&nbsp;" .
                                        CCommon::getIknowLevel($intLevel),
                'totalscore'    =>  $uinfo->totalscore,
                'masterscore'   =>  $uinfo->masterscore,
                'masterlevel'   =>  CCommon::getMasterIntLevel($uinfo->masterscore),
                'power'         =>  $uinfo->power,
            );
        }
    }

    public static function checkAdminPower($intUid, $strUserName, $intUip, $arrQuserConf)
    {
        require_once ABS_PATH . 'common-lib/struct/quser.class.php';

        $intUid     =   intval($intUid);
        $strUserName=   trim($strUserName);
        if (0 == $intUid && $strUserName == '') {
            CLog::warning(sprintf('%s: try get power without uid or username',
                __METHOD__));
            return false;
        }

        $intToTry   =   count($arrQuserConf);
        $intTried   =   0;
        $intIndex   =   ($intUid > 0) ? $intUid : ord($strUserName{0});
        $intIndex   =   $intIndex % $intToTry;
        $arrConf    =   $arrQuserConf[$intIndex];

        while ($intTried < $intToTry) {
            $res = CQuser::getUserInfo($intUid, $strUserName, $intUip,
                $arrConf['host'], $arrConf['port'], $arrConf['timeout']);
            if ($res instanceof CQuserRes && $res->isValid()) {
                if ($res->power != IKNOW_PRIV_ADMIN) {
                    CLog::warning(sprintf('%s: want power %s but %s',
                        __METHOD__, IKNOW_PRIV_ADMIN, $res->power));
                    return false;
                } else {
                    return true;
                }
            } else {
                $intTried++;
                CLog::warning(sprintf(
                    '%s: error getting quser res of (%s) from %s:%s, ' .
                    '%sth try of %s total',
                    __METHOD__, $intUid, $strUserName, $arrConf['host'],
                    $arrConf['port'], $intTried, $intToTry));
                $intIndex   =   ($intIndex + 1) % $intToTry;
                $arrConf    =   $arrQuserConf[$intIndex];
            }
        }

        return false;
    }

    public static function checkUserPower($intUid, $strUserName, $intUip, $arrQuserConf)
    {
        require_once ABS_PATH . 'common-lib/struct/quser.class.php';

        $intUid     =   intval($intUid);
        $strUserName=   trim($strUserName);
        if (0 == $intUid && $strUserName == '') {
            CLog::warning(sprintf('%s: try get power without uid or username',
                __METHOD__));
            return false;
        }

        $intToTry   =   count($arrQuserConf);
        $intTried   =   0;
        $intIndex   =   ($intUid > 0) ? $intUid : ord($strUserName{0});
        $intIndex   =   $intIndex % $intToTry;
        $arrConf    =   $arrQuserConf[$intIndex];

        while ($intTried < $intToTry) {
            $res = CQuser::getUserInfo($intUid, $strUserName, $intUip,
                $arrConf['host'], $arrConf['port'], $arrConf['timeout']);
            if ($res instanceof CQuserRes && $res->isValid()) {
                if ($res->errno != 0) {
                    CLog::warning(sprintf('%s: errno %s from quser for uid %s',
                        __METHOD__, $res->errno, $intUid));
                    return false;
                }
                if (0 == ($res->power & IKNOW_PRIV_NORMAL)
                    && $res->power != IKNOW_PRIV_ADMIN) {
                    CLog::warning(sprintf('%s: want power %s but %s',
                        __METHOD__, IKNOW_PRIV_NORMAL, $res->power));
                    return false;
                } else {
                    return true;
                }
            } else {
                $intTried++;
                CLog::warning(sprintf(
                    '%s: error getting quser res of (%s) from %s:%s, ' .
                    '%sth try of %s total',
                    __METHOD__, $intUid, $strUserName, $arrConf['host'],
                    $arrConf['port'], $intTried, $intToTry));
                $intIndex   =   ($intIndex + 1) % $intToTry;
                $arrConf    =   $arrQuserConf[$intIndex];
            }
        }

        return false;
    }

    public static function myHtmlSpecialChars($strData, $bolNeedBR = true, $bolNeedSQuot = true)
    {
        require_once    ABS_PATH . 'common-lib/datafilter.class.php';
        $strData = htmlspecialchars($strData);
        for ($i = 0, $cnt = strlen($strData); $i < $cnt; $i++) {
            $ch = $strData[$i];
            if (ord($ch) > 0x80) {
                $ch2 = $strData[$i+1];
                if ($ch2 == '\\' || $ch2 == '\'') {
                    $strTmp =   iconv('gbk', 'utf-8//IGNORE', $ch . $ch2);
                    $strRet .=  CCommon::unUTF8($strTmp);
                } else {
                    $strRet .=  $ch . $ch2;
                }
                $i++;
            } else {
                if ($bolNeedBR) {
                    if ($ch == "\r") {
                        continue;
                    }
                    if ($ch == "\n") {
                        $strRet .= "<br>";
                        continue;
                    }
                }
                if ($bolNeedSQuot) {
                    if ($ch == "'") {
                        $strRet .= "&#39;";
                        continue;
                    } else if ($ch == "\\") {
                        $strRet .= "&#92;";
                        continue;
                    }
                }
                $strRet .= $ch;
            }
        }
        return CDataFilter::transHtmlSpecialToNormal($strRet);
    }

    public static function StringToHex($strData)
    {
        $strData    =   strval($strData);
        $strHex =   '';
        for ($i=0, $cnt=strlen($strData); $i<$cnt; $i++) {
            $intVal =   ord($strData{$i});
            $low    =   $intVal % 16;
            $hi     =   ($intVal - $low) / 16;
            $strHex .=  chr($hi > 9 ? 55 + $hi : 48 + $hi)
                . chr($low > 9 ? 55 + $low : 48 + $low);
        }
        return $strHex;
    }
    
    public static function unUTF8($chUTF8)
    {
        $ch1 = ord($chUTF8[0]);
        $ch2 = ord($chUTF8[1]);
        $ch3 = ord($chUTF8[2]);
        $val = ((0x1F & $ch1) << 12) + ((0x7F & $ch2) << 6) + (0x7F & $ch3);
        return sprintf('&#%u;', $val);
    }
}

define('LOG_ID',        CCommon::getLogID());
define('CLIENT_IP',     CCommon::getClientIP());
define('CLIENT_IP_NUM', CCommon::ipToNum(CLIENT_IP));
define('HOSTNAME',      CCommon::getHostname());
?>
