<?php
/**
 * @name index
 * @desc 入口文件
 * <AUTHOR>
 */
$objApplication = Bd_Init::init();
$g_bdOmp = new Bd_Omp(APP);
$g_bdOmp->start();
$arrMap = array(
    'Beco_Action' => APP_PATH . '/' . APP . '/actions/Beco.php',
);
Bd_Autoloader::addClassMap($arrMap);
Beco_Timer::start('total');
register_shutdown_function('shutdown');
try {
    $objResponse = $objApplication->bootstrap()->run();
} catch (Ap_Exception_LoadFailed $e) {
    echo "load fail\n";
} catch (Ap_Exception $e) {
    echo $e->getMessage() . "\n";
}
function shutdown() {
    global $g_bdOmp;

    fastcgi_finish_request();
    $g_bdOmp->stop();
    Beco_Timer::end('total');
    Beco_Log::pushNotice('mmused', memory_get_usage());
    $notice = Beco_Log::buildNotice();
    $timer = Beco_Timer::toString('=', ' ');
    Beco_Log::flush($timer . ' ' . $notice);
}
