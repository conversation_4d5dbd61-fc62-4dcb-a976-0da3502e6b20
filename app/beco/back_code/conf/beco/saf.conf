#SESSION类型为SESSION1
session_type:1
#passport存储长度，默认32
passport_save_len:128

#需要转为GB2312
cgi_oe:UTF-8
#接收CGI为UTF-8格式
cgi_ie:UTF-8

#通用业务Action配置，支持可裁剪，值为0和1，1表示Action执行，0表示裁剪掉
#cgi流程为接收与处理Get Post 以及AP的Rewrite与Regex路由中设置的Params参数
#adapt流程为客户端适配流程
#log流程为ODP环境notice日志打印流程
#pulic流程为SAF内部数据字典服务流程
#session流程为处理passport相关的BDUSS/SSID等数据与pass交互进行校验的流程
#uuap流程为验证内网是否登录流程
[action]
cgi : 1
adapt : 0
log : 1
public : 0
session : 0
uuap : 0

#session流程中是否开启passport半自动账号验证功能
# 开启设置为 1 , 不开启设置为 0
[passport]
incomplete_user : 0

#对应通用业务Action的Hook配置，值为0或不配表示不加载Hook，值为对应Hook类名，表示执行对应Hook
[hook]
#示例Hook
#cgi : 0
#adapt : 0
#log : 0
#public : 0
#public : Saf_Hook_SampleHook
#session : 0
#uuap : 0

#以上配置项支持APP域的配置，App域配置将覆盖全局配置
[GLOBAL]
#自定义钩子名称
#支持App域配置，先执行全局Hook，再执行App的Hook
#hook_name:Saf_Base_Testhook

#产品线需要修改这里的配置
#改为产品线的基础库名
#api_lib:wiki

#与adapt流程对应的controller设置
#key : value格式
#key与WISE返回的版式信息一一对应，不可更改
#value表示对应APP的controller,首字母大写
[controller]
#wml : Wml
#big : Big
#iphone : Iphone
#utouch : Utouch
#pad : Pad
#middle : Middle
