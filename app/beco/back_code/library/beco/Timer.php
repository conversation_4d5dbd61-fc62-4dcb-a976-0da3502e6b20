<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file library/afd/Timer.php
 * <AUTHOR>
 * @date 2016/01/28 15:06:44
 * @brief 
 *  
 **/
class Beco_Timer {

    protected static $_arrData = array();

    protected static $_arrExecutionTime = array();
    
    protected static $_intTimeS = null;

    protected static $_prefix = "t_";

    /**
     * @param string $strName
     *
     * @return
     */
    public static function start($strName)
    {
        $strName = self::$_prefix . $strName;
        self::$_arrData[$strName]['end'] = self::$_arrData[$strName]['start'] = gettimeofday();
    }

    /**
     * @param string $strName
     *
     * @return
     */
    public static function end($strName)
    {
        $strName = self::$_prefix . $strName;
        self::$_arrData[$strName]['end'] = gettimeofday();
    }

    /**
     * @param string $strName
     *
     * @return mixed
     */
    public static function calculate($strName='')
    {
        if (!empty($strName)) {
            return self::_calculateOne($strName);
        } else {
            return self::_calculateAll();
        }
    }
    
    /**
     * @return string
     * 
     */
    public static function getTimes()
    {
        return self::$_arrExecutionTime;
    }
    
    /**
     * @return string
     * 
     */
    public static function getNowTime()
    {
        if (is_null(self::$_intTimeS)) {
            self::$_intTimeS = time();
        }
        return self::$_intTimeS;
    }

    /**
     * @param array $intStart
     * @param array $intEnd
     *
     * @return float
     */
    public static function getUtime($intStart, $intEnd)
    {
        return ($intEnd['sec'] - $intStart['sec'])*1000*1000 + ($intEnd['usec'] - $intStart['usec']);
    }
    
    /**
     * @param string $chr1
     * @param string $chr2
     *
     * @return float
     */
    public static function toString($chr1=':', $chr2=' ')
    {
        $arrRet = self::calculate();
        if (empty($arrRet)) {
            return '';
        }
        $strRet = '';
        foreach ($arrRet as $strName => $strTimer) {
            $strRet .= sprintf('%s%s%s%s',$strName, $chr1, $strTimer, $chr2);
        }
        $strRet = rtrim($strRet, $chr2);
        return $strRet;
    }
    
    /**
     * @return string
     *
     */
    protected static function _calculateAll()
    {
        if (empty(self::$_arrData)) {
            return array();
        }
        foreach (self::$_arrData as $strName => $arrTime) {
            if (!isset(self::$_arrExecutionTime[$strName])) {
                self::$_arrExecutionTime[$strName] = self::_getUtime($arrTime['start'], $arrTime['end']);
            }
        }
        return self::$_arrExecutionTime;
    }
    
    /**
     * @param string $strName
     *
     * @return float
     */
    protected static function _calculateOne($strName)
    {
        $strName = self::$_prefix . $strName;
        if (isset(self::$_arrExecutionTime[$strName])) {
            return self::$_arrExecutionTime[$strName];
        }       
        if (isset(self::$_arrData[$strName])) {
            self::$_arrExecutionTime[$strName] = self::_getUtime(self::$_arrData[$strName]['start'], self::$_arrData[$strName]['end']);
            return self::$_arrExecutionTime[$strName];
        }
        return 0;
    }
    
    /**
     * @param array $time1
     * @param array $time2
     *
     * @return float
     */
    protected static function _getUtime($time1,$time2)
    {
        return ($time2['sec'] - $time1['sec']) * 1000 * 1000 + ($time2['usec'] - $time1['usec']);
    }
}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
