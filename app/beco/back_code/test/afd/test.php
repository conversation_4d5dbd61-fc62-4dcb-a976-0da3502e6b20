<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/



/**
 * @file test.php
 * <AUTHOR>
 * @date 2016/05/11 11:35:48
 * @brief 
 *  
 **/
Bd_Init::init();
$strKey = "1459156413733";
$value = array(
    'id' => '1459156413733',
    'floor' => '',
    'upInterval' => 7,
    'upBeginFloor' => 3,
    'downInterval' => 7,
    'downBeginFloor' => 3,
    'dynamic' => 2,
    'placeType' => 3,
    'config' => array(
        0 => array(
            'statusType' => 2,
            'moduleType' => 1,
            'configType' => 1,
            'value' => 100,
            'freshConfig' => array(
                0 => array(
                    'freshType' => 2,
                    'valid' => 1,
                    'freshCount' => array(),
                    'freshCountAllValid' => 1, 
                ),
                1 => array(
                    'freshType' => 3,
                    'valid' => 1,
                    'freshCount' => array(),
                    'freshCountAllValid' => 1, 
                ),
                2 => array(
                    'freshType' => 4,
                    'valid' => 2,
                    'freshCount' => array(1),
                    'freshCountAllValid' => 1, 
                ),
            ),
        ),
        1 => array(
            'statusType' => 2,
            'moduleType' => 2,
            'configType' => 1,
            'value' => 100,
            'freshConfig' => array(
                0 => array(
                    'freshType' => 2,
                    'valid' => 1,
                    'freshCount' => array(),
                    'freshCountAllValid' => 1, 
                ),
                1 => array(
                    'freshType' => 3,
                    'valid' => 2,
                    'freshCount' => array(),
                    'freshCountAllValid' => 2, 
                ),
                2 => array(
                    'freshType' => 4,
                    'valid' => 2,
                    'freshCount' => array(),
                    'freshCountAllValid' => 2, 
                ),
            ),
        ),
    ),
); 
//$ret = Service_Afd_SspHelp::updateByPlaceId($strKey, $value);
//$ret = Service_Afd_SspHelp::getByPlaceId($strKey);
//$token = md5(json_encode(array($value)));
//$ret = Service_Afd_SspHelp::parse(json_encode(array('ssp' => array($value), 'token' => $token)));
//$ret = Service_Afd_SspHelp::valid($ret);
$value = array('updateTS' => '123123123123', 'app' => array('com.sina.com.cn', 'com.baidu.browser'));
$token = md5(json_encode($value));
$value = array('applist' => $value, 'token' => $token);
$ret = Service_Afd_ApplistHelp::updateApplist($value['applist']);
$ret = Service_Afd_ApplistHelp::getApplist();
$ret = Service_Afd_ApplistHelp::parse(json_encode($value));
print_r($ret);
$ret = Service_Afd_ApplistHelp::valid($ret);
var_dump($ret);





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
