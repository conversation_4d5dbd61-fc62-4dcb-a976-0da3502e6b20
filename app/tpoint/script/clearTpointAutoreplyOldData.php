<?php
/***************************************************************************
 *
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

define('MODULE_NAME', 'tpoint');
define('LOG', 'log');
define('SCRIPT_LOG_NAME', dirname(__FILE__) . '/../../../log/app/' . MODULE_NAME . '/script.log');
require_once('../util/Redis.php');
require_once('../util/Const.php');

$arrLogConf = array(
    'file'  => SCRIPT_LOG_NAME,
    'level' => 0x02|0x04,
);

Bingo_Log::init(array(LOG => $arrLogConf), LOG);

class clearTpointAutoreplyOldData {
    /**
     * @desc get a to be delete poast
     * @param null
     * @return $intPost : int : post to be deleted
     */
    private static function getDelPost() {
        $arrInput = array(
            'key' => Util_Const::REDIS_CLEAR_AUTOREPLY_POST_KEY,
        );
        $command = 'SPOP';
        $ret = Util_Redis::command($command, $arrInput);
        if (false == $ret) {
            var_dump('call redis smembers fail with input[' . serialize($arrInput) . '] and output[' . serialize($ret) . ']');
            //throw new Exception('call redis operation fail', Tieba_Errcode::ERR_REDIS_CALL_FAIL);
            return false;
        }

        $intPost = $ret['ret'][Util_Const::REDIS_CLEAR_AUTOREPLY_POST_KEY];
        //Bingo_Log::warning($intPost);
        return $intPost;
    }

    /**
     * @desc delete a post
     * @param $intPost
     * @return true/false
     */
    private static function delPost($intPostId) {
        //直接走UEG专用删帖接口,这块就不要了
        /*
        //必须先获取发帖用户的用户ID和用户名,否则,调用删帖接口会有权限问题
        $input = array(
            "post_ids" => array(
                0 => $intPostId,
            ),
        );
        $res   = Tieba_Service::call('post', 'getPostInfo', $input, null, null, 'post', 'php', 'utf-8');
        if (false == $res || Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            Bingo_Log::warning('call post::getPostInfo fail with input[' . serialize($input) . '] and output[' . serialize($res) . ']');
            throw new Exception('call service fail', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $intUserId = $res['output'][0]['user_id'];
        $strUserName = $res['output'][0]['username'];
        $input = array(
            "req" => array( //帖子输入
                "op_uid" => $intUserId, //操作人ID
                "op_uname" => $strUserName, //操作人名
                "post_id" => $intPostId, //回复id
                "call_from" => 'tieba_pc',
                "op_ip" => 1, //用户ip
            ),
        );
        $res   = Tieba_Service::call('post', 'deletePost', $input, null, null, 'post', 'php', 'utf-8');
        if (false == $res || Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            Bingo_Log::warning('call post::deletePost fail with input[' . serialize($input) . '] and output[' . serialize($res) . ']');
            throw new Exception('call service fail', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }*/

        //这是UEG的专用删帖接口,只能删除触点帖
        $input = array(
            "req" => array( //帖子输入
                "post_id"   => $intPostId, //回复id
                "call_from" => "tpoint",
            ),
        );

        //五次重试策略
        $intCount = 5;
        while($intCount) {
            $res   = Tieba_Service::call('unihandle', 'deletePostForTpoint', $input, null, null, 'post', 'php', 'utf-8');
            if (false == $res || Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
                Bingo_Log::warning('call unihandle::deletePostForTpoint fail with input[' . serialize($input) . '] and output[' . serialize($res) . ']');
                Bingo_Log::warning('left retry time: ' . --$intCount);
            } else {
                break;
            }
        }

        return true;
    }

    /**
     * @desc get num in redis
     * @param null
     * @return postNum
     */
    private static function getRedisPostNum() {
        $arrInput = array(
            'key' => Util_Const::REDIS_CLEAR_AUTOREPLY_POST_KEY,
        );
        $command = 'SCARD';
        $ret = Util_Redis::command($command, $arrInput);
        if (false == $ret) {
            Bingo_Log::warning('call redis srem fail with input[' . serialize($arrInput) . '] and output[' . serialize($ret) . ']');
            //throw new Exception('call redis operation fail', Tieba_Errcode::ERR_REDIS_CALL_FAIL);
            return false;
        }

        $intPostNum = $ret['ret'][Util_Const::REDIS_CLEAR_AUTOREPLY_POST_KEY];
        var_dump($ret);
        Bingo_Log::warning("total num in redis: $intPostNum");
        return $intPostNum;
    }

    /**
     * @desc main loop
     * @param null
     * @return true/false
     */
    public static function main() {
        try {
            self::getRedisPostNum();
            while(null != ($intPostId = self::getDelPost())) {
                self::delPost($intPostId);
            }

            return true;
        } catch (Exception $e) {
            Bingo_Log::warning($e->getMessage() . "in " . $e->getFile() . ":" . $e->getLine());
            return false;
        }
    }
}

clearTpointAutoreplyOldData::main();

?>