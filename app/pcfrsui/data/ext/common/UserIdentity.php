<?php
/**
 * 用户身份扩展
 */

class Ext_Common_UserIdentity extends Util_DataProvider {

    public function init($arrContextData) {
        $this->_addDep('Core_User');
        $this->arrOutData = array(
            'is_business_agent'     => 0,
            'is_tiebaplus_agent'    => 0,
        );
        return true;
    }

    public function process($arrDependData, $arrContextData) {

        $intUid = $arrDependData['Core_User']['user_id'];
        if ($intUid <= 0) {
            $this->setData($this->arrOutData);
            $this->_ready();
            return;
        }
        Bingo_Log::notice(sprintf('Ext_Common_UserIdentity::process. uid:[%s]', $intUid));
        $this->strAgentKey = 'agent_'.$intUid;
        $arrMultiInput[] = array(
            'service'   => 'wordlist',
            'method'    => 'queryWLItemDirectly',
            'input'     => array(
                'table_name'    => 'tb_wordlist_redis_BusinessAccount_Agent',
                'key'           => array(
                    $this->strAgentKey,
                ),
                'ie'            => 'utf-8',
            ),
            'ie'        => 'utf-8',
        );
        $arrMultiInput[] = array(
            'service'   => 'wordlist',
            'method'    => 'queryWLItemDirectly',
            'input'     => array(
                'table_name'    => 'tb_wordlist_redis_Tiebaplus_Agent',
                'key'           => array(
                    $this->strAgentKey,
                ),
                'ie'            => 'utf-8',
            ),
            'ie'        => 'utf-8',
        );

        $this->_registerService($arrMultiInput, 'userIdentityCallback');
    }

    public function userIdentityCallback($arrOutput)
    {
        $arrResBusinessAgent = $arrOutput[0];
        if (!$arrResBusinessAgent || $arrResBusinessAgent['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf('call wordlist::queryWLItemDirectly fail. table:[tb_wordlist_redis_BusinessAccount_Agent], key:[%s], output:[%s]', $this->strAgentKey, serialize($arrResBusinessAgent)));
        } elseif (!empty($arrResBusinessAgent['data'][$this->strAgentKey])) {
            $this->arrOutData['is_business_agent'] = 1;
        }
        $arrResTpAgent = $arrOutput[1];
        if (!$arrResTpAgent || $arrResTpAgent['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(sprintf('call wordlist::queryWLItemDirectly fail. table:[tb_wordlist_redis_Tiebaplus_Agent], key:[%s], output:[%s]', $this->strAgentKey, serialize($arrResTpAgent)));
        } elseif (!empty($arrResTpAgent['data'][$this->strAgentKey])) {
            $this->arrOutData['is_tiebaplus_agent'] = 1;
        }

        Bingo_Log::notice(sprintf('Ext_Common_UserIdentity::userIdentityCallback. strAgentKey:[%s], outdata:[%s]', $this->strAgentKey, serialize($this->arrOutData)));
        $this->setData($this->arrOutData);
        $this->_ready();
    }


    public function getCacheKey($arrDependData, $arrContextData) {
        return false;
    }
}