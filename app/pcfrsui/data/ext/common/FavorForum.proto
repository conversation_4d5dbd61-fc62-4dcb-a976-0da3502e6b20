package pcfrsui.Ext.Common.FavorForum;  

//import 相对路径为 orp/app/pcfrsui/data/
//import 'core/Session.proto'; 

message Forum
{
	optional uint64 user_id=1;
	optional uint64 forum_id=2;
	optional string forum_name=3;
	optional uint64 is_black=4;
	optional uint64 is_top=5;
	optional uint64 in_time=6;
	optional uint64 level_id=7;
	optional uint64 cur_score=8;
	optional uint64 score_left=9;
	optional string level_name=10;
	optional uint64 forum_type=11;
}

message TplVar
{
	repeated Forum forum=1;
}

message FavorForum
{
	required TplVar tpl_var=1;
}


message Res //(必须有）存在的Message，表示返回
{
	optional FavorForum favor_forum=1;
}