<?php
/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/6/9
 * Time: 14:14
 */
class Ext_Common_Stock extends Util_DataProvider {
    const PAGE_PER_NUM = 50;
    const MAX_LIKE_FORUM_COUNT = 5;
    const FORUM_STOCK_ATTR = 'forum_stock';
    private static $_bolIsLogin = false;
    public function init($arrContextData) {
        $this->_addDep('Core_Forum');
        $this->_addDep('Core_Session');
    }

    public function process($arrDependData, $arrContextData) {
        $arrForum = $arrDependData['Core_Forum'];
        $arrSessionInfo = $arrDependData['Core_Session'];
        $arrForumInfo = $arrForum['forum_info'];
        $intForumId = $arrForumInfo['forum_id'];
        $strForumName = $arrForumInfo['forum_name'];
        $intUserId = $arrSessionInfo['user_id'];
        self::$_bolIsLogin = $arrSessionInfo['is_login'];
        $arrStockAttr = $arrForum['attrs'][self::FORUM_STOCK_ATTR];
        if (1 !== intval($arrStockAttr['is_stock_forum'])) {
            $this->setData(array());
            $this->_ready();
            return true;
        }
        $arrInput = array(
            'forum_id' => $intForumId,
            'forum_name' => $strForumName,
        );
        $arrMultiInput = array();
        $arrMultiInput[] = array(
            'service' => 'stock',
            'method' => 'getFrsData',
            'input' => $arrInput,
        );
        //not logged in
        if(0 !== $intUserId){
            $arrInput = array(
                'user_id' => $intUserId,
            );
            $arrMultiInput[] = array(
                'service' => 'user',
                'method' => 'getMyFavorForum',
                'input' => $arrInput,
            );
        }
        $this->_registerService($arrMultiInput, 'stockCallBack');
    }

    public function stockCallBack ($arrOutPut) {
        $arrRet = array();
        $arrData = array();
        $arrStockData = $arrOutPut[0];
        $arrLikeForum = $arrOutPut[1];
	
        if (false === $arrStockData || 0 !== intval($arrStockData['errno'])) {
            Bingo_Log::warning('get stock data failed, ' . serialize($arrStockData));
            $this->setData($arrRet);
            $this->_ready();
            return true;
        }

        if(isset($arrLikeForum)){
            if (0 !== intval($arrLikeForum['errno'])) {
                 Bingo_Log::warning('get like forum failed, ' . serialize($arrLikeForum));
            }else{
                $intLikeForumCount = 0;
                foreach ($arrLikeForum['concern_forum']['forum'] as $concern_forum => $val) {
                    if ($intLikeForumCount >= self::MAX_LIKE_FORUM_COUNT) {
                        break;
                    }

                    $arrData['right_aside']['focus_forum'][] = array(
                        'forum_name'    => $concern_forum,
                    );
                    $intLikeForumCount ++;
                }
            }
        }
        $arrData['head']['snapshot']          = $arrStockData['output']['snapshot'];
        $arrData['head']['news']              = $arrStockData['output']['news'];
        $arrData['head']['reports']           = $arrStockData['output']['reports'];

        $arrData['right_aside']['concepts']   = $arrStockData['output']['concepts'];
        $arrData['right_aside']['indexs']     = $arrStockData['output']['indexs'];
        $arrData['right_aside']['industry']   = $arrStockData['output']['industry'];
        $arrData['right_aside']['evaluation'] = $arrStockData['output']['evaluation'];
        $arrRet['stock'] = $arrData;
        $this->setData($arrRet);
        $this->_ready();
        return true;
    }

    public function getCacheKey($arrDependData, $arrContextData) {
        return false;
    }
}
