package pcfrsui.Ext.Common.BusinessPromot;
import 'core/Common.proto';
import 'core/Forum.proto';
import 'core/User.proto';

message BusinessPromotCommentList {
        //内容
        optional string title = 1;
        //用户名
        optional string username = 2;
        //uid
        optional int64 uid = 3;
        //是否是楼主：1-是；0-否
        optional int32 is_lz = 4;
}

message Res
{
        //文案
        optional string title = 1;
        //banner物料图
        optional string img = 2;
        //弹层物料图
        optional string img_popup = 3;
        //类型：1-置顶热议帖；2-预约；3-下载
        optional int32 type = 4;
        //type为1时才会有值，为对应置顶热议贴的贴子id
        optional string thread_id = 5;
        //活动位点击跳转的url，如果是热议贴的话，则返回贴子pb页url
        optional string url = 6;
        //参与人数
        optional int32 join_num = 7;
        //热议列表
        repeated BusinessPromotCommentList comment_list = 8;
}
