<?php
/**
 * BusinessPromot.php
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 19/7/24 下午2:23
*/

class Ext_Common_BusinessPromot extends Util_DataProvider
{
	private $_arrOutData = array();
	
	public function init($arrContextData)
	{
		$this->_addDep('Core_Forum');
		$this->_addDep('Core_User');
	}
	
	public function process($arrDependData, $arrContextData)
	{
		$arrForum   = $arrDependData['Core_Forum'];
        $intForumId = $arrForum['forum_info']['forum_id'];

        $arrInputParam = array(
            'forum_id'  => $intForumId,
        );

        $arrMultiCall = array(
            0 => array(
                'service' => 'tbmall',
                'method'  => 'getScheduleResourceByForumId',
                'ie'      => 'utf-8',
                'input'   => $arrInputParam,
            ),
        );

        $this->_registerService($arrMultiCall, 'resourceCallback');
	}

	public function resourceCallback($arrOutput)
    {
        $arrRet = $arrOutput[0];

        // fe沙盒测试mock data
//        $arrRet = array(
//            'errno' => Tieba_Errcode::ERR_SUCCESS,
//            'data'  => array(
//                'title' => '企业品牌大使测试投放',
//                'type'  => 1,
//                'resource_info' => array(
//                    'pc_img' => 'https://imgsa.baidu.com/forum/pic/item/c8fcc3cec3fdfc03163cedaada3f8794a4c2265f.jpg',
//                ),
//                'link_info' => array(
//                    'thread_id' => 6202441351,
//                ),
//            )
//        );

        if (false === $arrRet || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call tbmall getScheduleResourceByForumId fail, output = '.serialize($arrRet));
            $this->setData($this->_arrOutData);
            $this->_ready();
            return false;
        }

        $arrBusinessPromot = $arrRet['data'];

        //该物料是否投放了当前终端类型 eg:客户可能只投了iOS
        //兼容之前老的模式是全类型投放 以下字段未设定时为旧版模式 全类型投放
        $isCurrentOSValid = true;
        if(!empty($arrBusinessPromot['resource_info']['ua_type'])){
            if (false===strpos($arrBusinessPromot['resource_info']['ua_type'],'PC')) {
                $isCurrentOSValid = false;
            }
        }
        if (empty($arrBusinessPromot) || !$isCurrentOSValid) {
            // 不存在返回
            $this->setData($this->_arrOutData);
            $this->_ready();
            return true;
        }

        $img = $arrBusinessPromot['resource_info']['pc_img'];
        $url = $arrBusinessPromot['link_info']['pc_url'];

        $thread_id = (int)$arrBusinessPromot['link_info']['thread_id'];

        $this->_arrOutData = array(
            'id'        => $arrBusinessPromot['id'],
            'title'     => $arrBusinessPromot['title'],
            'img'       => $img,
            'type'      => $arrBusinessPromot['type'],
            'thread_id' => $thread_id,
            'url'       => $url,
            'join_num'  => $arrBusinessPromot['resource_info']['join_num'],
        );

        if (empty($thread_id)) {
            // 没有配置热议帖，直接返回
            $this->setData($this->_arrOutData);
            $this->_ready();
            return true;
        }

        // 如果配置了置顶热议帖，获取贴子相关信息
        if (empty($this->_arrOutData['url'])) {
            $this->_arrOutData['url'] = 'https://tieba.baidu.com/p/' . $thread_id;
        }

        // 获取贴子信息
        $arrThreadInput = array(
            'thread_ids'         => array($thread_id),
            'need_abstract'      => 0,
            'forum_id'           => 0,
            'need_photo_pic'     => 0,
            'need_user_data'     => 0,
            'icon_size'          => 1,
            'need_forum_name'    => 0,
            'call_from'          => 'pc_frs',
            'need_post_content'  => 0,
            'structured_content' => 0,
        );

        // 热门回复
        $arrHotInput = array(
            'thread_id' => $thread_id,
            'offset'    => 0,
            'count'     => 10,
        );

        $arrMultiCall = array(
            0 => array(
                'service' => 'post',
                'method'  => 'mgetThread',
                'ie'      => 'utf-8',
                'input'   => $arrThreadInput,
            ),
            1 => array(
                'service' => 'agree',
                'method'  => 'getPbHotPostList',
                'ie'      => 'utf-8',
                'input'   => $arrHotInput,
            ),
        );

        $this->_registerService($arrMultiCall, 'threadCallback');
    }
	
	public function threadCallback($arrOutput)
	{
        $thread_id = $this->_arrOutData['thread_id'];

        // 贴子信息
        $arrThreadOutput = $arrOutput[0];

        if ($arrThreadOutput === false || $arrThreadOutput['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . '::' . __FUNCTION__ . " call post::mgetThread failed.  output[" . serialize($arrThreadOutput) . ']';
            Bingo_Log::warning($strLog);
            $this->setData(array());
            $this->_ready();
            return true;
        }

        $intAuthorId = $arrThreadOutput['output']['thread_list'][$thread_id]['user_id'];

        $intPostNum     = (int)$arrThreadOutput['output']['thread_list'][$thread_id]['post_num'];
        $intClickNum    = (int)$arrThreadOutput['output']['thread_list'][$thread_id]['freq_num'];
        $intAgreeNum    = (int)$arrThreadOutput['output']['thread_list'][$thread_id]['agree_num'];
        $intDisAgreeNum = (int)$arrThreadOutput['output']['thread_list'][$thread_id]['disagree_num'];
        $intShareNum    = (int)$arrThreadOutput['output']['thread_list'][$thread_id]['share_num'];
        $intJoinNum     = $intPostNum + $intClickNum + $intAgreeNum + $intDisAgreeNum + $intShareNum;

        Bingo_Log::warning('join_num:'.$intJoinNum.' $arrThreadOutput:'.serialize($arrThreadOutput));

        $this->_arrOutData['join_num'] = $intJoinNum;

        // 热门回复
        $arrHotOutput = $arrOutput[1];

        if ($arrHotOutput === false || $arrHotOutput['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
            $strLog = __CLASS__ . '::' . __FUNCTION__ . " call agree::getPbHotPostList failed. output[" . serialize($arrHotOutput) . ']';
            Bingo_Log::warning($strLog);
            $this->setData(array());
            $this->_ready();
            return true;
        }

        $arrCommentList = array();

        if (!empty($arrHotOutput['data'])) {
            $arrHotPids = array_merge($arrHotOutput['data']['branch_a'], $arrHotOutput['data']['branch_b']);
            $arrHotPids = array_unique($arrHotPids);
            $arrHotPids = array_slice($arrHotPids, 0, 10);

            $arrInput = array(
                'post_ids' => $arrHotPids,
            );

            $strService = 'post';

            $strMethod  = 'getPostInfo';

            $arrOutput = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', null, 'utf-8');

            if ($arrOutput === false || $arrOutput['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
                $strLog = __CLASS__ . '::' . __FUNCTION__ . " call $strService::$strMethod failed. input[" . serialize($arrInput) . '] output[' . serialize($arrOutput) . ']';
                Bingo_Log::warning($strLog);
                $this->setData(array());
                $this->_ready();
            }

            Bingo_Log::warning(" call $strService::$strMethod success. input[" . serialize($arrInput) . '] output[' . serialize($arrOutput) . ']');

            if (!empty($arrOutput['output'])) {
                // 有人回复
                $arrHotList = $arrOutput['output'];

                foreach ($arrHotList as $v) {
                    if (isset($v['is_post_deleted']) && $v['is_post_deleted'] != 0) {
                        // 贴子被删，热帖更新延迟，手动过滤
                        continue;
                    }

                    $strContent = strip_tags($v['content']);

                    if (empty($strContent)) {
                        continue;
                    }

                    $is_lz = $v['user_id'] == $intAuthorId ? 1 : 0;

                    $arrCommentList[] = array(
                        'title'    => $strContent,
                        'username' => $v['username'],
                        'uid'      => $v['user_id'],
                        'is_lz'    => $is_lz,
                    );
                }

                $this->_arrOutData['comment_list'] = $arrCommentList;
            }
        }

        $this->setData($this->_arrOutData);
        $this->_ready();
        return true;
	}

	public function getCacheKey($arrDependData, $arrContextData)
	{
		return false;
	}
}