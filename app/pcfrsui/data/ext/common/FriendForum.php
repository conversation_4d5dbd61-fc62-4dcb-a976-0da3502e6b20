<?php

/**
 *
 * <AUTHOR>
 *
 */
class Ext_Common_FriendForum extends Util_DataProvider
{
	const ZYQ_FRIEND_NAME  = 'zyqfriend';
	private static $arrNzyq; // 资源区静态存储

	public function init($arrContextData)
	{
		$this->_addDep('Core_Forum');
	}

	public function process($arrDependData, $arrContextData)
	{
		self::$arrNzyq = array();
		$arrForumAttr = $arrDependData['Core_Forum']['attrs'];
		$strForumName = $arrDependData['Core_Forum']['forum_info']['forum_name'];
		$arrNzyq = array();

		$arrFriendName = isset($arrForumAttr[self::ZYQ_FRIEND_NAME]) ? $arrForumAttr[self::ZYQ_FRIEND_NAME] : array();

		$arrMultiInput = array();
		if (! empty($arrFriendName))
		{
			foreach ($arrFriendName as $strFriendName)
			{
				if ('' === trim($strFriendName))
				{
					continue;
				}

				$arrParam = array(
						'forum_name'   => $strFriendName,
				);
					
				$arrMultiInput[] = array(
						'service'  => 'forum',
						'method'        => 'getBtxInfoByName',
						'input'         => $arrParam,
				);
			}
			$this->_registerService($arrMultiInput,'callBack');
				
		}
		else
		{
			$this->_ready();
		}
	}

	public function callBack($arrOutput)
	{
		$arrFriendInfo = array();
		foreach ($arrOutput as $arrOut)
		{
			if (isset($arrOut['errno']) && Tieba_Errcode::ERR_SUCCESS === $arrOut['errno'])
			{
				$strForumName = isset($arrOut['forum_id']['forum_name']) ? trim(strval($arrOut['forum_id']['forum_name'])) : '';

				if ('' === $strForumName)
				{
					continue;
				}

				$arrNew  = array(
						'forum_name'  => $strForumName,
						'avatar'      =>
						'http://gss3.bdstatic.com/84oSdTum2Q5BphGlnYG/timg?wapp&quality=80&size=b150_150&subsize=20480&cut_x=0&cut_w=0&cut_y=0&cut_h=0&sec=1369815402&srctrace&di=041f99acc41ef943ddd5096f9d0f42ae&wh_rate=null&src=http%3A%2F%2Fimgsrc.baidu.com%2Fforum%2Fabpic%2Fitem%2Fbc338f3df8dcd1003c3d6b69738b4710bb122fcd.jpg',
				);

				if (isset($arrOut['card']['avatar']) && strlen($arrOut['card']['avatar']) > 0)
				{
					$arrNew['avatar'] = $arrOut['card']['avatar'];
				}

				$arrFriendInfo[] = $arrNew;
			}
		}

		if (!empty($arrFriendInfo))
		{
			self::$arrNzyq['friend'] = $arrFriendInfo;
		}

		$arrOutData['friend_forum']['tpl_var'] = self::$arrNzyq;
		$this->setData($arrOutData);
		$this->_ready();
	}

	public function getCacheKey($arrDependData, $arrContextData)
	{
		$strForumName = $arrDependData['Core_Forum']['forum_info']['forum_name'];

		if ('' === $strForumName)
		{
			return false;
		}

		return $strForumName;
	}
}

