package pcfrsui.Ext.Common.Customize;  

//import 相对路径为 orp/app/pcfrsui/data/
//import 'core/Forum.proto'; 
//import 'core/User.proto'; 

message Log
{
	required string src=1;
	required string href=2;
}

message TplVar
{
	required bool open=1;
	required Log log=2;
	required bool is_customize_pm=3;
}

message Customize
{
	required TplVar tpl_var=1;
}


message Res //(必须有）存在的Message，表示返回
{
	optional Customize customize=1;
}