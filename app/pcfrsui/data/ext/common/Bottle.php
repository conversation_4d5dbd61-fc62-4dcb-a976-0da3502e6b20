<?php

class Ext_Common_Bottle extends Util_DataProvider {

    //入口是否显示
    const ENTRANCE_DISPLAY_ON = 1; //是
    const ENTRANCE_DISPLAY_OFF = 0; //否
    //全局开关
    const GLOBAL_SWITCH_ON = 'on';//开启
    const GLOBAL_SWITCH_OFF = 'off';//关闭
    //黑白名单
    const BWL_WHITE = 1; //白名单
    const BWL_BLACK = 2; //黑名单
    const BWL_UNKNOW = 3; //未设置

    /**
     * @param $arrContextData
     */
    public function init($arrContextData) {
        $this->_addDep('Core_Session');
        $this->_addDep('Core_Forum');
    }

    /**
     * @param $arrDependData
     * @param $arrContextData
     */
    public function process($arrDependData, $arrContextData) {
        $userId = isset($arrDependData['Core_Session']['user_id']) ? $arrDependData['Core_Session']['user_id'] : 0;
        $forumId = isset($arrDependData['Core_Forum']['forum_info']['forum_id']) ? $arrDependData['Core_Forum']['forum_info']['forum_id'] : 0;
        $this->setData($this->getBottleEntrance($userId, $forumId));
        $this->_ready();
    }

    /**
     * @param $arrDependData
     * @param $arrContextData
     * @return bool
     */
    public function getCacheKey($arrDependData, $arrContextData) {
        return false;
    }


    /**
     * @param $userId
     * @return mixed
     */
    private function getBottleEntrance($userId, $forumId) {

        //初始化数据
        $threadId = 0;

        //参数中包含吧ID时 查询吧黑白名单策略
        $forumBWL = $forumId ? $this->_getForumBWL($forumId) : self::BWL_WHITE;

        //获取全局漂流瓶开关
        $globalSwitch = $this->_getGlobalSwitch();

        //判断是否显示入口
        $displayEntrance = $this->_getDisplayStatus($forumBWL, $globalSwitch);
        //不显示入口 不需要继续获取漂流瓶
        if ($displayEntrance === self::ENTRANCE_DISPLAY_OFF) {
            goto returnRes;
        }

        //获取漂流瓶
        $threadId = $this->_getBottleThreadId($userId);

        returnRes:

        $res['display_entrance'] = (int)$displayEntrance;
        $res['bottle_count'] = $threadId ? 1 : 0;
        $res['thread_id'] = $threadId;
        return $res;
    }

    /**
     * 获取全局漂流瓶开关
     * @return bool
     */
    private function _getGlobalSwitch() {
        $wordList = Wordserver_Wordlist::factory();
        $keyword = 'switch';
        $ret = $wordList->getValueByKeys(array($keyword), 'tb_wordlist_redis_BottleGlobalSwitch');
        $value = isset($ret[$keyword]) ? $ret[$keyword] : '';

        return $value === self::GLOBAL_SWITCH_ON ? self::GLOBAL_SWITCH_ON : self::GLOBAL_SWITCH_OFF;
    }

    /**
     * 获取 漂流瓶的 帖子ID
     * @param $userId
     * @return int
     */
    private function _getBottleThreadId($userId) {
        $input['user_id'] = $userId;
        $res = Tieba_Service::call('bottle', 'getLatestThreadId', $input, null, null, 'post', 'php', 'utf-8');

        if (!isset($res['errno']) || $res['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call user::getUserData error " . serialize($input) . "_" . serialize($res));
            return 0;
        }

        if (!isset($res['data']['thread_id'])) {
            return 0;
        }

        return $res['data']['thread_id'];
    }

    /**
     * 判断是否显示入口
     * @param $forumBWL
     * @param $globalSwitch
     * @return bool
     */
    private function _getDisplayStatus($forumBWL, $globalSwitch) {
        //全局开关关闭 不显示入口
        if ($globalSwitch === self::GLOBAL_SWITCH_OFF) {
            return false;
        }

        //只有吧把名单才显示
        return $forumBWL === self::BWL_WHITE;
    }

    /**
     * 获取 吧 黑白名单
     * @param $forumId
     * @return bool|mixed|multitype
     */
    private function _getForumBWL($forumId) {
        $input = array(
            "forum_id" => $forumId //吧id
        );
        $res = Tieba_Service::call('forum', 'getForumAttr', $input, null, null, 'post', 'php', 'utf-8');

        if (!isset($res['errno']) || $res['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call forum::getForumAttr error " . serialize($input) . "_" . serialize($res));
            return self::BWL_UNKNOW;
        }

        if (!isset($res['output']['bottle_baw'])) {
            return self::BWL_UNKNOW;
        }

        $baw = (int)$res['output']['bottle_baw'];

        if ($baw !== self::BWL_WHITE && $baw !== self::BWL_BLACK) {
            return self::BWL_UNKNOW;
        }

        return $baw;
    }
}