<?php
/**
 * 
 * <AUTHOR>
 *
 */
class Ext_Common_PostAuditTool extends Util_DataProvider
{
	public function init($arrContextData)
	{
		$this->_addDep('Core_Forum');
        $this->_addDep('Core_User');
	}

	public function process($arrDependData, $arrContextData)
	{
		$arrPerm = $arrDependData['Core_Forum']['perm'];
        $arrIcon = $arrDependData['Core_User']['new_iconinfo'];		
		$bolHasPostAudit = (isset($arrContextData['cupid'][238]) || isset($arrPerm[106])) ? true : false;


		// start: 针对网警 紧急隐藏帖子管理工具入口 huangling02
		$intIsNetPolice = 0;
        foreach($arrIcon as $icon){
            if('net_police' === $icon['name']){
                $intIsNetPolice = 1;
                break;
            }
        }
        if($intIsNetPolice){
            $bolHasPostAudit = false;
        }
        // end: 针对网警隐藏帖子管理工具入口


        $arrData = array('has_postaudit' => $bolHasPostAudit);
		$arrOutData['post_audit_tool']['tpl_var'] = $arrData;
		$this->setData($arrOutData);
		$this->_ready();
	}


	public function getCacheKey($arrDependData, $arrContextData)
	{
		return false; 
	}
}

