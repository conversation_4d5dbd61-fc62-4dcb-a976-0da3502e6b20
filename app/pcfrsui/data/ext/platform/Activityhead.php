<?php
/***************************************************************************
 *
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file Activityhead.php
 * <AUTHOR>
 * @date 2016/02/23 12:04:00
 * @brief
 *
 **/

class Ext_Platform_Activityhead extends Util_DataProvider {
    const FRS_GCON_MID = 134;
    const FRS_GCON_MID_NAME = 'official_p1';
    const FRS_GCON_MID_NAME_NEW = "has_activityhead";
    const FRS_COUNTDOWN_MID_NAME = 'countdownhead';
    const FRS_OFFICIAL = 'official';

    //const SHOUJI = 153;
    //const ANDORID = 1458134;
    const ZHONGXIN = 6695709;
    const OPPO = 913304;
    const LIYIFENG = 1253525;
    const TB_WORDLIST_STAR_PV = 'star_face_pv';
    const TB_WORDLIST_KEY = 'startup_pv';
    const PV_COEFFICIENT = 7;

    private static $_objRedis = null;
    private static $_intResId = 0;

    const STARTUP_FID = 103992;
    const REDIS_PID = 'gconforum';
    private static $_arrStartupFid = array(103992,41219,7282,2613755,1430656,195742,651559,230211,2525359,823188,13694170,2496,315455,911599,516538,817130,64100,243147,250883,4775,321211,197313,495983,307617,1550262,14487,24463,83292,);

    private $arrForum;
    private $arrUser;

    // afd在并发请求中的位置，读取使用, -1不请求afd
    private static $_afdDataIndex = -1;
    private static $_strLevel1DirName = '';
    private static $_strLevel2DirName = '';

    /**
     * @param   $arrContextData
     * @return 
     */
    public function init($arrContextData) {
        $this->_addDep('Core_Forum');
        $this->_addDep('Core_User');
    }

    /**
     * @param  $arrDependData
     * @param  $arrContextData
     * @return bool
     */
    public function process($arrDependData, $arrContextData) {
        $this->arrForum = $arrDependData['Core_Forum'];
        $this->arrUser = $arrDependData['Core_User'];
        $intForumId = $this->arrForum['forum_info']['forum_id'];
        // Core_Forum中的attr获取失败时会去调用service取
        if (count(($this->arrForum['attrs'])) == 2) {
            Bingo_Log::warning("Ext_Platform_Activityhead: fid:". $intForumId ." Core_Forum attr is empty.".serialize($this->arrForum));
            $arrInput = array(
                "forum_id" => array($intForumId)
            );
            $arrRet = Tieba_Service::call('forum', 'getForumAttr', $arrInput);
            if (false == $arrRet || $arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS || empty($arrRet['data'])) {
                Bingo_Log::warning("getForumAttr fail. input[".serialize($arrInput)."] output[".serialize($arrRet)."]");
            }
            Bingo_Log::warning("Ext_Platform_Activityhead, attr:".json_encode($arrRet['output'], true));
            $this->arrForum['attrs'] = $arrRet['output'];
        }
        $arrForumStyle = $this->arrForum['attrs'];
        $intForumId = $this->arrForum['forum_info']['forum_id'];
        $arrDir = $arrDependData['Core_Forum']['dir'];

        self::$_strLevel1DirName = $strLevel1Name = $arrDir['level_1_name'];
        self::$_strLevel2DirName = $strLevel2Name = $arrDir['level_2_name'];

        $arrMultiInput = array();

        if (35 === $intForumId) {
            $param = array(
                'uid' => isset($this->arrUser["user_id"]) ? $this->arrUser["user_id"] : 0,
                'uname' => isset($this->arrUser["user_name"]) ? urlencode($this->arrUser["user_name"]) : "noname",
            );
            $tmpId = $intForumId;
            $uri_str = sprintf("%d,%s", $tmpId, implode("|", $param));
            ral_set_pathinfo("/lua_content?".$uri_str);
            $ret = ral("tpoint_stream", 'post', array('kafka_msg' => $uri_str, ), rand());
        }

        // 魅族单case PC端不展示
        //if(358325 === $intForumId) {
        //    $this->_ready();
        //    return true;
        //}

        if ($arrForumStyle[self::FRS_GCON_MID_NAME]['has_act'] == 1 ||
            $arrForumStyle[self::FRS_COUNTDOWN_MID_NAME] == 1 ||
            $arrForumStyle[self::FRS_GCON_MID_NAME_NEW]['has_act'] == 1
        ) {
            /*if (in_array($intForumId, self::$_arrStartupFid)){
                $arrInput = array(
                    'forum_id' => self::STARTUP_FID,
                );
            } else {*/
            $arrInput = array(
                'forum_id' => $intForumId,
            );
            //}
            $arrMultiInput[] = array(
                'service' => 'activityhead',
                'method' => 'getOnlineActHeadByFid',
                'input' => $arrInput,
            );
            //拿到afd入参的下标
            self::$_afdDataIndex = 2;
        } else if(in_array($intForumId , self::$_arrStartupFid)) {
            $arrInput = array(
                'forum_id' => self::STARTUP_FID,
            );
            $arrMultiInput[] = array(
                'service' => 'activityhead',
                'method' => 'getOnlineActHeadByFid',
                'input' => $arrInput,
            );
            //拿到afd入参的下标
            self::$_afdDataIndex = 2;
        }else{
            //拿到afd入参的下标
            self::$_afdDataIndex = 1;
        }

        $arrMultiInput[] = array(
            'service' => 'resource',
            'method' => 'getResourceList',
            'input' => array(),
        );

        // 不投放asp广告时，不请求afd   +  过滤官方吧、品牌吧
        $_isBrandForum = empty($arrForumStyle['special_forum']['is_brand_forum']) ? 0 : intval($arrForumStyle['special_forum']['is_brand_forum']);
        if (!$this->canThrowAspAd() || isset($arrForumStyle[self::FRS_OFFICIAL]) || (1 == $_isBrandForum)) {
            // -1 表示不请求afd
            self::$_afdDataIndex = -1;
        }else {
            // 注册afd广告请求参数
            $afdInput = Tieba_Service_Ad_Afd::getAfdPcMultiInputOnce(Tieba_Service_Ad_Afd::PC_FRS, $arrDependData, $arrContextData);
            Bingo_Log::warning("activityHead skin_afdInput: " . json_encode($afdInput));
            if (!empty($afdInput)) {
                $arrMultiInput[] = $afdInput;
            }
        }
        //吧头图请求量标记
        Tieba_Stlog::addNode('req_get_activityhead', 1);
        //区分吧id标记
        Tieba_Stlog::addNode('fid',  $this->arrForum['forum_info']['forum_id']);
        $this->_registerService($arrMultiInput, 'ActivityheadCallBack');
    }

    /**
     * @param $arrMultOut
     * @return array
     */
    public function ActivityheadCallBack($arrMultiOut) {
        $arrRet = array();
        $arrActHead = array();
        $forumStyle = $this->arrForum['attrs'];
        $fname = $this->arrForum['forum_info']['forum_name'];
        $intFid = $this->arrForum['forum_info']['forum_id'];

        //取asp广告
        $afdRet = array();
        $pid = Tieba_Service_Ad_Afd::AD_PLACE_PC_FRS_HEAD;
        $page = Tieba_Service_Ad_Afd::PC_FRS;
        $afdRetTmp = Tieba_Service_Ad_Afd::getAfdMaterialByPid($pid, $page, $arrMultiOut, self::$_afdDataIndex);
        if (!empty($afdRetTmp)) {
            $afdRet['activityhead']['head_imgs'][0] = $afdRetTmp['head_img'];
            if (!empty($afdRetTmp['head_img']['jump_url'])) {
                $afdRet['activityhead']['head_imgs'][0]['pc_url'] = $afdRetTmp['head_img']['jump_url'];
            }
            unset($afdRet['activityhead']['head_imgs'][0]['jump_url']);
            $afdRet['activityhead']['id'] = $afdRetTmp['mcid'];
            $afdRet['activityhead']['showurl'] = $afdRetTmp['showurl'];
            if (isset($afdRetTmp['adtype'])) {
                //若asp数据有问题，则不展示
                $afdRet['activityhead']['activity_type'] = 0;
            }
            $afdRet['activityhead']['adtype'] = 'asp';
        } else {
            $afdRet['activityhead']['adtype'] = 'mis';
        }

        $res = $arrMultiOut[0];

        $arrUserBase = $this->arrUser;

        //check forum attr
        if ($forumStyle[self::FRS_GCON_MID_NAME]['has_act'] == 1 ||
            $forumStyle[self::FRS_COUNTDOWN_MID_NAME] == 1 ||
            $forumStyle[self::FRS_GCON_MID_NAME_NEW]['has_act'] == 1
            || in_array($intFid , self::$_arrStartupFid)
        ) {
            $res = $arrMultiOut[0];
            if ($res == false) {
                Tieba_Stlog::addNode("req_acthead_success", 0);
            } else {
                Tieba_Stlog::addNode("req_acthead_success", 1);
            }
            if (empty($res) || Tieba_Errcode::ERR_SUCCESS !== $res['errno']) {
                $strLog = 'getMultiInput return error [' . serialize($res) . ']';
                Bingo_Log::warning($strLog);
                $this->_ready();
                //将afd结果产出
                $this->setData($afdRet);
                return true;
            }
            if (empty($res['data'])) {
                $this->_ready();
                //将afd结果产出
                $this->setData($afdRet);
                return true;
            };
            //display info
            $resData = $res['data'];
            if (!isset($resData['display_info']) || !isset($resData['type']) || trim($resData['display_info']) == '') {
                Bingo_Log::warning('activityhead data error !' . serialize($resData));
                $this->_ready();
                //将afd结果产出
                $this->setData($afdRet);
                return true;
            }

            switch ($resData['type']) {
                case 0:

                    $info = $resData['display_info'];
                    if (false === $info) {
                        Bingo_Log::warnig("display info error! " . serialize($resData['display_info']));
                        $this->_ready();
                        //将afd结果产出
                        $this->setData($afdRet);
                        return true;
                    }
                    if ($info['pc_selected'] && isset($info['pc_head'])) {
                        $arrActHead['activity_type'] = 0;
                        $arrActHead['head_imgs'] = $info['pc_head'];
                        $arrActHead['activity_title'] = $info['activity_title'];
                    } else {
                        $this->_ready();
                        //将afd结果产出
                        $this->setData($afdRet);
                        return true;
                    }
                    break;
                case 2:
                    $arrInput = array(
                        'head_id' => $resData['head_id'],
                        'src_format' => 'frs',
                    );
                    $arrOut = Tieba_Service::call('activityhead', 'getActHead', $arrInput, null, null, 'post', 'php', 'utf-8');
                    if ($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                        Bingo_Log::warning('call activityhead:getActHead failed. input[' . serialize($arrInput) . '] output[' . serialize($arrOut) . ']');
                        $this->_ready();
                        //将afd结果产出
                        $this->setData($afdRet);
                        return true;
                    }
                    $arrActHead = $arrOut['data'];
                    /*
                    if ($arrForumBase['forum_id'] == self::SHOUJI || $arrForumBase['forum_id'] == self::ANDORID || $arrForumBase['forum_id'] == self::ZHONGXIN){
                        $arrActHead['temp'] = 1;
                    }
                     */
                    //var_dump($arrActHead);
                    $arrInput = array(
                        'exkey_act_id' => $resData['activity_info']['exkey_act_id'],
                        'user_id' => $arrUserBase['user_id'],
                    );
                    $arrOut = Tieba_Service::call('exkey', 'getUserExKey', $arrInput, null, null, 'post', 'php', 'utf-8');
                    if ($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                        Bingo_Log::warning('call exkey:getUserKey failed. input[' . serialize($arrInput) . '] output[' . serialize($arrOut) . ']');
                        $this->_ready();
                        //将afd结果产出
                        $this->setData($afdRet);
                        return true;
                    }
                    $keys = $arrOut['data']['keys'];
                    if (!empty($keys)) {
                        $arrActHead['order_info']['my_code'] = $keys[0]['key'];
                    }
                    if (in_array($intFid , self::$_arrStartupFid)) {
                        $arrActHead['temp'] = 3;
                        $handleWordServer = Wordserver_Wordlist::factory();
                        $strTableName = 'tb_wordlist_redis_' . self::TB_WORDLIST_STAR_PV;
                        $strKey = self::TB_WORDLIST_KEY;
                        $arrKeys = array($strKey);
                        $arrItemInfo = $handleWordServer->getValueByKeys($arrKeys,$strTableName);
                        $intPv = intval($arrItemInfo[$strKey]);
                        $arrActHead['order_info']['cur_num'] += $intPv * $arrActHead['order_info']['rise_ratio'];

                        if (isset($arrUserBase['is_login']) && $arrUserBase['is_login'] == 1 && self::_getObjRedis()){
                            $strRedisKey = 'litspanner_' . $arrActHead['id'] . '_' . $arrUserBase['user_id'];
                            $arrReidsInput = array(
                                'key'   => $strRedisKey,
                            );
                            $arrVoteNum = self::$_objRedis->GET($arrReidsInput);
                            if ($arrVoteNum['err_no'] === 0) {
                                $arrActHead['vote_num'] = isset($arrVoteNum['ret'][$strRedisKey]) ? intval($arrVoteNum['ret'][$strRedisKey]) : 0;
                            }
                        }

                    }
                    /*
                    if ($arrForumBase['forum_id'] == self::SHOUJI || $arrForumBase['forum_id'] == self::ANDORID ||
                        $arrForumBase['forum_id'] == self::OPPO || $arrForumBase['forum_id'] == self::LIYIFENG) {
                        $arrActHead['temp'] = 2;

                        $handleWordServer = Wordserver_Wordlist::factory();
                        $strTableName = 'tb_wordlist_redis_' . self::TB_WORDLIST_STAR_PV;
                        $strKey = self::TB_WORDLIST_KEY;
                        $arrKeys = array($strKey);
                        $arrItemInfo = $handleWordServer->getValueByKeys($arrKeys,$strTableName);
                        $intPv = intval($arrItemInfo[$strKey]);
                        $arrActHead['order_info']['cur_num'] += $intPv * $arrActHead['order_info']['rise_ratio'];

                        if (isset($arrUserBase['is_login']) && $arrUserBase['is_login'] == 1){
                            $arrActHead['forum_message']['user_id'] = $arrUserBase['user_id'];
                            $arrActHead['forum_message']['forum_id'] = self::OPPO;
                            $arrActHead['forum_message']['forum_name'] = 'oppo';
                            $arrActHead['forum_message']['is_like'] = false;
                            $arrInput = array(
                                    'forum_id' => self::OPPO, //鍚??id
                                    'user_id' => $arrUserBase['user_id'], //鐢ㄦ埛id
                                    'user_ip' => 0,
                            );
                            $arrIsLike = Tieba_Service::call('perm', 'getPerm', $arrInput, null, null, 'post', 'php', 'utf-8');
                            if ($arrIsLike === false || $arrIsLike['errno'] != 0) {
                                Bingo_Log::warning('call perm getPerm fail. input = ' . serialize($arrInput) . ', output = ' . serialize($arrIsLike));
                            }
                            if ($arrIsLike['output']['grade']['is_like'] == 1){
                                $arrActHead['forum_message']['is_like'] = true;
                            }
                        }
                    }
                     */
                    break;

                case 3://
                    $arrActHead['commit_date'] = $resData['op_time'];
                    $arrActHead['start_date'] = $resData['start_time'];
                    $arrActHead['end_date'] = $resData['other_time'];
                    $arrActHead['finish_date'] = $resData['end_time'];
                    $arrActHead['activity_type'] = $resData['type'];
                    $arrDisplayInfo = $resData['display_info'];
                    if (false === $arrDisplayInfo) {
                        Bingo_Log::warning('display info error!' . serialize($resData['display_info']));
                        $this->_ready();
                        //将afd结果产出
                        $this->setData($afdRet);
                        return true;
                    }
                    if (isset($arrDisplayInfo['before_btn_url']) && isset($arrDisplayInfo['after_btn_url'])
                        && isset($arrDisplayInfo['before_bk_src']) && isset($arrDisplayInfo['after_bk_src'])
                    ) {
                        $arrActHead['before_btn_url'] = $arrDisplayInfo['before_btn_url'];
                        $arrActHead['before_btn_text'] = $arrDisplayInfo['before_btn_text'];
                        $arrActHead['before_background_src'] = $arrDisplayInfo['before_bk_src'];

                        $arrActHead['after_btn_url'] = $arrDisplayInfo['after_btn_url'];
                        $arrActHead['after_btn_text'] = $arrDisplayInfo['after_btn_text'];
                        $arrActHead['after_background_src'] = $arrDisplayInfo['after_bk_src'];

                    } else {
                        $this->_ready();
                        //将afd结果产出
                        $this->setData($afdRet);
                        return true;
                    }
                    break;
                case 4:
                    $arrInput = array(
                        'head_id' => $resData['head_id'],
                        'src_format' => 'frs',
                    );
                    $arrOut = Tieba_Service::call('activityhead', 'getActHead', $arrInput, null, null, 'post', 'php', 'utf-8');
                    if ($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                        Bingo_Log::warning('call activityhead:getActHead failed. input[' . serialize($arrInput) . '] output[' . serialize($arrOut) . ']');
                        $this->_ready();
                        //将afd结果产出
                        $this->setData($afdRet);
                        return true;
                    }
                    $arrActHead = $arrOut['data'];
                    break;
                case 5:
                    $arrInput = array(
                        'head_id' => $resData['head_id'],
                        'src_format' => 'frs',
                    );
                    $arrOut = Tieba_Service::call('activityhead', 'getActHead', $arrInput, null, null, 'post', 'php', 'utf-8');
                    if ($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                        Bingo_Log::warning('call activityhead:getActHead failed. input[' . serialize($arrInput) . '] output[' . serialize($arrOut) . ']');
                        $this->_ready();
                        //将afd结果产出
                        $this->setData($afdRet);
                        return true;
                    }
                    $arrTemp = $arrOut['data']['act_head'];
                    if(!empty($arrTemp)) {
                        $arrActHead = array(
                            'activity_type' => intval($arrTemp['head_type']),
                            'iframe_url' => strval($arrTemp['display_info']['iframe_url']),
                            'iframe_height' => intval($arrTemp['display_info']['iframe_height']),
                        );
                    }
                    break;

                default:
                    $this->_ready();
                    //将afd结果产出
                    $this->setData($afdRet);
                    return true;
            }
            $this->_generateLog($arrMultiOut[1]['data'], $arrMultiOut[0]['data']);
            $arrActHead['res_id'] = self::$_intResId;
            $arrActHead['id'] = $resData['head_id'];
            if(!empty($resData['task_id'])) {
                $arrActHead['id'] = intval($resData['task_id']);
	            Tieba_Stlog::addNode('ret_activityhead', 'pc');
	            Tieba_Stlog::addNode('forum_id',  $this->arrForum['forum_info']['forum_id']);
            }
            $arrRet['activityhead'] = $arrActHead;
            //            $arrRet['activityhead'] = $arrActHead;
            //amis配置物料优先于afd物料
            $afdRet = array();
            $afdRet['activityhead'] = $arrActHead;
            $afdRet['activityhead']['adtype'] = 'mis';
        }else{
            if(!empty($afdRetTmp)) {
                Tieba_Stlog::addNode("req_acthead_asp_success", 1);
                Tieba_Stlog::addNode("req_acthead_asp_id_success", $afdRetTmp['head_img']['mcid']);
            }
        }
        $this->_ready();
        //将afd结果产出
        $this->setData($afdRet);
        return true;
    }

    /**
     * 检查是否能投放asp广告，true能，false不能
     * @return bool
     */
    private function canThrowAspAd()
    {
        return Tieba_Service_Ad_Afd::canLevel1ThrowAd(self::$_strLevel1DirName);
    }

    /**
     * @param  array $arrResources    [description]
     * @param  array $arrActivityHead [description]
     * @return
     */
    private function _generateLog($arrResources, $arrActivityHead) {
        $arrForumBase = $this->arrForum;
        $arrForumDir = $this->arrForum['dir'];
        $strFristDir = $arrForumDir['level_1_name'];
        $strSecondDir = $arrForumDir['level_2_name'];
        $strForumId = $arrForumBase['forum_info']['forum_id'];
        $strForumName = $arrForumBase['forum_info']['forum_name'];

        $arrActivityType = array(
            '0' => '贴吧顶部高级头图',
            '3' => '贴吧运营配置头部特型-倒计时',
            '5' => '贴吧头部iframe特型-定制',
        );
        $arrResourceName2Id = array();
        if (isset($arrResources)) {
            foreach ($arrResources as $value) {
                $arrResourceName2Id[$value['name']] = $value['id'];
            }
        }
        $strResourceId = '';
        if (isset($arrResourceName2Id[$arrActivityType[strval($arrActivityHead['type'])]])) {
            $strResourceId = $arrResourceName2Id[$arrActivityType[strval($arrActivityHead['type'])]];
        }
        self::$_intResId = intval($strResourceId);
        $strActivityId = '';
        if (isset($arrActivityHead['head_id'])) {
            if(!empty($arrActivityHead['task_id']) && !empty($arrActivityHead['flag'])) {
                $strActivityId = $arrActivityHead['task_id'];
            } else {
                $strActivityId = $arrActivityHead['head_id'];
            }
        }
        $strId = $strActivityId;
        $arrLog = array(
            'pro'         => 'tieba',
            'mid'         => 'frs',
            'urlkey'      => 'pt/res',

            'line'        => 'PT',
            'page'        => 'FRS',
            'locate'      => 'p0249',
            'action_type' => 'VIEW',
            'task'        => 'ad_plat',
            'obj_id'      => $strResourceId . '_' . $strId,
            'client_type' => 'PC_WEB',
            'uname'       => Tieba_Session_Socket::getLoginUname(),
            'fid'         => $strForumId,
            'fname'       => $strForumName,
            'first_dir'   => $strFristDir,
            'second_dir'  => $strSecondDir,
            'tid'         => '',
        );
        Tbapi_Platform_Stlog_Stlog::addStLog($arrLog);

        //add pb log
        $arrLogData = array(
            'base' => array(
                'pro'         => Tbapi_Platform_Pblog_PblogDef::$arrPro['PC_WEB'],
                'mid'         => Tbapi_Platform_Pblog_PblogDef::$arrMid['FRS'],
                'urlkey'      => Tbapi_Platform_Pblog_PblogDef::URLKEY_EFFECT,
                'callfrom'    => Tbapi_Platform_Pblog_PblogDef::CALLFROM_EFFECT,
                'page'        => Tbapi_Platform_Pblog_PblogDef::$arrPageType['FRS'],
                'locate'      => Tbapi_Platform_Pblog_PblogDef::$arrLocatePrefix['PC_WEB'] . Tbapi_Platform_Pblog_PblogDef::$arrLocateNo['activityhead'],
                'action_type' => Tbapi_Platform_Pblog_PblogDef::$arrActionType['VIEW'],
                'res_id'      => $strResourceId,
                'client_type' => Tbapi_Platform_Pblog_PblogDef::$arrClientType['PC_WEB'],
                'fid'         => $strForumId,
                'fname'       => $strForumName,
                'first_dir'   => $strFristDir,
                'second_dir'  => $strSecondDir,
            ),
            'objs' => array(
                array(
                    'task_id' => $strId,
                ),
            ),
        );
        $bolRes = Tbapi_Platform_Pblog_Pblog::addLog($arrLogData, Tbapi_Platform_Pblog_PblogDef::PROTO_MESSAGE_PLAT_AD);
        if (false === $bolRes) {
            Bingo_Log::warning('add pb log fail_' . serialize($arrLogData));
        }
    }

    /**
     * @param  $arrDependData
     * @param  $arrContextData
     * @return bool
     */
    public function getCacheKey($arrDependData, $arrContextData) {
        return false;
    }

    /**
     * @return obj
     */
    public function _getObjRedis() {
        if (self::$_objRedis != null) {
            return self::$_objRedis;
        }

        Bingo_Timer::start('redisinit');
        self::$_objRedis = new Bingo_Cache_Redis(self::REDIS_PID);
        Bingo_Timer::end('redisinit');
        if (!self::$_objRedis || !self::$_objRedis->isEnable()) {
            Bingo_Log::warning('init redis fail.');
            self::$_objRedis = null;
            return null;
        }
        return self::$_objRedis;
    }
}
