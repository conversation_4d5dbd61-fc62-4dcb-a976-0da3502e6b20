package pcfrsui.Ext.Platform.Skin; 
import 'core/Common.proto'; 
import 'core/Forum.proto'; 

message skinTpl
{
    required uint64 id=1;
    required uint64 put_type=2;
    optional string task_desc=3;
    required uint64 status=4;
    required uint64 start_time=5;
    required uint64 end_time=6;
    required uint64 op_time=7;
    required uint64 create_time=8;
    optional string put_content=9;
    optional bytes except_forums=10;  
    required uint64 page=11;
    optional bytes background_info=12;
    required uint64 has_couplet=13;
    optional bytes couplet_info=14;
    required uint64 pv=15;
    required uint64 uv=16;
    required uint64 click_pv=17;
    required uint64 click_uv=18;
    required uint64 op_user_id=19;
    optional string op_user_name=20;
    optional string del_user_name=21;
    required uint64 del_time=22;
}  
    
message Res
{
    optional skinTpl forumskin=1;  
}