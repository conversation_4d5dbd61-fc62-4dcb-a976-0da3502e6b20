<?php

// @brief 地方活动帖相关

class Ext_Platform_LocalActivity extends Util_DataProvider {
    public static $_forum = array();
    public static $_user = array();

    const OFFICIAL_ATTR           = 'official';
    const OFFICIAL_SWITCH         = 'official_common_switch';
    const OFFICIAL_LOCAL_ACTIVITY = 'official_local_activity';
    const ACTIVITY_URL_FIELD      = 'class_id';
    const SERVER_VAR_URI          = 'REQUEST_URI';
    const SEG_QUERY_STRING        = '?';
    const CATEGORY_URI            = '/f/activity';
    const TAB = "gathering";
    private static $_strUir       = '';
    private static $_arrTid       = array();

    /**
     * @brief 
     *
     * @param $arrContextData
     *
     * @return 
     */
    public function init($arrContextData){
        $this->_addDep('Core_Forum');
        $this->_addDep('Core_User');
        $this->_addDep('Core_BaseThreadList');
    }

    /**
     * @brief 
     *
     * @param $arrDependData
     * @param $arrContextData
     *
     * @return 
     */
    public function process($arrDependData, $arrContextData) {
        self::$_forum = $arrDependData['Core_Forum'];
        self::$_user = $arrDependData['Core_User'];
        $intUid = (int)self::$_user['user_id'];
        $arrStyle = $arrDependData['Core_Forum']['attrs'];
        if(!isset($arrStyle[self::OFFICIAL_ATTR][self::OFFICIAL_SWITCH][self::OFFICIAL_LOCAL_ACTIVITY]) || 1 != $arrStyle[self::OFFICIAL_ATTR][self::OFFICIAL_SWITCH][self::OFFICIAL_LOCAL_ACTIVITY]) {
            $this->_ready();
            return true;
        }
        /*
        $strUri = Bingo_Http_Request::getServer(self::SERVER_VAR_URI);
        $intPos = strpos($strUri, self::SEG_QUERY_STRING);
        if (false !== $intPos) {
            self::$_strUir = substr($strUri, 0, $intPos);
            if (self::CATEGORY_URI == self::$_strUir) {
                $this->_ready();
                return true;
            }
        } 
         */
        $strTab = Bingo_Http_Request::get('tab', '');
        if (self::TAB === $strTab) {
        } else {
            // 如果不是活动 tab。直接退出
            // 但是看逻辑貌似又不是的.
            //$this->_ready();
            //return true;
        }
        $arrThreadInfo = $arrDependData['Core_BaseThreadList'];
        $intFid = (int)self::$_forum['forum_info']['forum_id'];
        $arrTid = array();
        $intType = Tieba_Type_Thread::LOCAL_ACTIVITY;
        foreach ($arrThreadInfo['thread_list'] as $arrInfo) {
            $arrThreadBaseInfo = $arrInfo['base_info'];
            if (isset($arrThreadBaseInfo['thread_types']) && intval($arrThreadBaseInfo['thread_id'])>0) {
                $intThreadType = intval($arrThreadBaseInfo['thread_types'])>>32;
                if ($intType == $intThreadType) {
                    $arrTid[] = (int)$arrThreadBaseInfo['thread_id'];
                }
            }
        }
        $arrMultiInput = array();
        if (empty($arrTid)) {
            $this->_ready();
            return true;
        } else {
            self::$_arrTid = $arrTid;
            $strMethod = 'getActivityInforByTids';
            $arrInput = array(
                'forum_id' => $intFid,
                'tids' => $arrTid,
            );
            $arrMultiInput[] = array(
                'service' => 'vertical',
                'method' => $strMethod,
                'input' => $arrInput,
            );
        }
        if (0 < $intUid) {
            $strMethod = 'getUserApplyInforByTid';
            $arrInput = array(
                'user_id' => $intUid,
                'forum_id' => $intFid,
                'tids' => $arrTid,
            );
            $arrMultiInput[] = array(
                'service'  => 'vertical',
                'method' => $strMethod,
                'input' => $arrInput,
            );
        }
        $this->_registerService($arrMultiInput, 'Callback');
        return true;
    }

    /**
     * @brief 
     *
     * @param $arrMultiOut
     *
     * @return 
     */
    public function Callback($arrMultiOut) {
        $intUid = (int)self::$_user['user_id'];
        $arrApplyRes = $arrMultiOut[0];
        if (false === $arrApplyRes || Tieba_Errcode::ERR_SUCCESS !== $arrApplyRes['errno']) {
            Bingo_Log::warning("call vertical getActivityInforByTids fail!");
            $this->_ready();
            return array();
        }
        if (empty($arrApplyRes['data'])) {
            $this->_ready();
            return array();
        }
        $arrApplyInfor = array();
        if (0 < $intUid) {
            $arrRes = $arrMultiOut[1];
            if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
                Bingo_Log::warning("call vertical getUserApplyInforByTid fail!");
            } else if (!empty($arrRes['data'])){
                foreach ($arrRes['data'] as $arrInfor) {
                    $arrApplyInfor[$arrInfor['tid']]  = $arrInfor;
                }
            }
        }
        $arrData = array();
        $intNow = time();
        foreach ($arrApplyRes['data'] as $arrInfo) {
            $arrRow = array();
            $arrRow['tid'] = $arrInfo['tid'];
            $arrRow['id'] = $arrInfo['id'];
            $arrRow['isTop'] = $arrInfo['is_top'];
            $arrRow['pic'] = $arrInfo['pic'];
            $arrRow['actType'] = $arrInfo['act_type'];
            $arrRow['isLongAct'] = $arrInfo['is_long_act'];
            if ($arrRow['isLongAct'] > 0) {
                $arrRow['time'] = date('Y年n月j日',$arrInfo['start_time']).'-'.date('Y年n月j日',$arrInfo['end_time']);
            } else {
                $arrRow['time'] = date('Y年n月j日',$arrInfo['start_time']);
            }
            $arrRow['startTime'] = $arrTheadInfo['start_time'];
            $arrRow['endTime'] = $arrTheadInfo['end_time'];
            $arrRow['address'] = $arrInfo['address'];
            $arrRow['postId'] = $arrInfo['post_id'];
            $arrRow['entryNum'] = $arrInfo['passed_num'];
            $intSatus = 1;
            if ($arrInfo['end_time'] < $intNow) {
                $intSatus = 3;
            } else if (isset($arrApplyInfor[$arrInfo['tid']])) {
                $intSatus = 2;
            } else {
                $intSatus = 1;
            }
            $arrRow['status'] = $intSatus;
            $arrData[$arrInfo['tid']] = $arrRow;
        }
        $arrTplVar = array(
            'thread_activity' => $arrData,
        );
        $this->setData($arrTplVar);
        $this->_ready();
        return true;
    }

    /**
     * @brief 
     *
     * @param $arrDependData
     * @param $arrContextData
     *
     * @return 
     */
    public function getCacheKey($arrDependData, $arrContextData) {
        return false;
    }

}
