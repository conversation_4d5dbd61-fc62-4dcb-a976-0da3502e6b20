<?php
/***************************************************************************
 *
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file Official.php
 * <AUTHOR>
 * @date 2016/02/17 12:04:00
 * @brief
 *
 **/

class Ext_Platform_Official extends Util_DataProvider {
    const FORUM_STYLE_OFFICIAL_NAME   = 'official';
    const FORUM_STYLE_RIGHT_NAME      = 'official_right';
    const FORUM_STYLE_RIGHT_INFO_NAME = 'official_right_info';

    const MALL_FORUM_NAME = '双十一';

    const CUPID_ID_MALL = 317;

    const PREFIX_OFFICIAL_QRCODE_URL = 'http://tieba.baidu.com/mo/q/topic_page/qrcode_app_starter?mod=splatform#';
    const OFFICIAL_QRCODE_WIDTH = 170;
    const OFFICIAL_QRCODE_MARGIN = 15; 

    //金鸡百花奖投票
    const BASE_REDIS_PID = 'gconforum';
    private static $_baseRedis  = null;

    const VOTE_FORUM_ID = 571549;
    const VOTE_SHOW     = 'jjbh_show';

    public static $prizeList = array(
        'best_movie',
        'best_adaptor',
        'best_director',
        'best_man_star',
        'best_woman_star',
        'best_man_costar',
        'best_woman_costar',
        'best_new_star',
    );

    // flap
    private static $_flapForumList = array(
        1840079 => true,
        2197134 => true,
    );

    protected static $_fakeFlapNumPre = 'flap_fake_num_';
    protected static $_fakeTransNumPre = 'flap_fake_trans_num_';

    static $arrQaForumList = array(
        11676   => true,
        366368  => true,
    );

    private static $_intForumId;
    private static $_strForumName;
    private static $_intIsShowRightInfo;

    private static $_arrDependData;

    /**
     * @brief 
     *
     * @param $arrMultiOut
     *
     * @return 
     */
    public function officialCallBack($arrMultiOut) {
        $arrForum = self::$_arrDependData['Core_Forum'];
        $forumStyle = $arrForum['attrs'];
        self::$_intForumId = $arrForum['forum_info']['forum_id'];
        self::$_strForumName = $arrForum['forum_info']['forum_name'];

        $strQaTab = trim(Bingo_Http_Request::get('tab', ''));

        $arrOfficialData = array();

        //qa 
        if (isset(self::$arrQaForumList[$intFid]) && $strQaTab == 'qa') {
            $arrRet['gconforum']['log_var']['qa'] = 1;
        } else {
            $arrRet['gconforum']['log_var']['qa'] = 0;
        }
        $frsgcon = array();

        //official
        if(isset($forumStyle[self::FORUM_STYLE_OFFICIAL_NAME])) {
            //$arrOfficialData = array();
            if (isset($arrMultiOut[0]['data']['base_info']['material'])){
                $arrOfficialData['base'] = $arrMultiOut[0]['data']['base_info']['material'];
                $arrOfficialData['ext']  = self::_getCommonSwitchData($forumStyle);
                if (0 === intval($arrMultiOut[0]['data']['base_info']['style_type'])) {
                    $arrOfficialData['ext']['timeline'] = $arrMultiOut[0]['data']['timeline'];
                }
                if (isset($forumStyle[self::FORUM_STYLE_OFFICIAL_NAME]['official_special_switch']['official_qa']) && 1 === intval($forumStyle[self::FORUM_STYLE_OFFICIAL_NAME]['official_special_switch']['official_qa'])) {
                    $arrOfficialData['ext']['right']['official_qa'] = array();
                    if (isset($arrMultiOut[0]['data']['qa_list'])) {
                        $arrOfficialData['ext']['right']['official_qa'] = $arrMultiOut[0]['data']['qa_list'];
                    }
                }
                //hide foruthman
                if (isset($forumStyle[self::FORUM_STYLE_OFFICIAL_NAME]['official_common_switch']['official_hide_fourthman'])
                    && 1 === intval($forumStyle[self::FORUM_STYLE_OFFICIAL_NAME]['official_common_switch']['official_hide_fourthman'])) {
                        // todo
                        // 不确定这里的逻辑是否还有用
                        /*
                        $arrBawuList = $objCoreData->getForum('bawu');
                        $arrBawuList['fourthmanager'] = array();
                        $objCoreData->setForum('bawu', $arrBawuList);
                         */
                    }
            }
            if (false === $arrMultiOut[0]) {
                Bingo_Log::warning("frsui call official::getOfficialStyle fail");
            }

            if (isset($arrMultiOut[1])) {
                //Ext qrcode
                $strExtName = 'qrcode';
                $strQrcode = self::_getOfficialOtherExt($strExtName, $arrMultiOut[1]);
                if (!empty($strQrcode)) {
                    $arrOfficialData['ext']['qrcode'] = $strQrcode;
                }
            }
            //右侧资讯类
            if (self::$_intIsShowRightInfo) {
                $arrRightInfo = $arrMultiOut[2];
                $arrOfficialData['ext']['right']['info'] = $arrRightInfo['data'];
            }
            //官方吧pv
            $arrRet['gconforum']['log_var']['frs_gconforum'] = 1;
            $arrRet['gconforum']['log_var']['fid_type'] = 'platform';
        }

        //flap
        if(isset(self::$_flapForumList[self::$_intForumId])) {
            if (self::_getBaseRedisObj()) {
                $flapNumKey = self::$_fakeFlapNumPre.self::$_intForumId;
                $transNumKey = self::$_fakeTransNumPre.self::$_intForumId;
                $arrInput = array(
                    'reqs' => array(
                        array('key' => $flapNumKey), 
                        array('key' => $transNumKey),
                    ),
                );
                $ret = self::$_baseRedis->GET($arrInput);
                if($ret['err_no'] === 0) {
                    if (isset($ret['ret'][$flapNumKey])) {
                        $arrOfficialData['flip_info']['flip_num'] = intval($ret['ret'][$flapNumKey]);
                    } else {
                        $arrOfficialData['flip_info']['flip_num'] = 1;
                    }
                    if (isset($ret['ret'][$transNumKey])) {
                        $arrOfficialData['flip_info']['transmit_num'] = intval($ret['ret'][$transNumKey]);
                    } else {
                        $arrOfficialData['flip_info']['transmit_num'] = 1;
                    }    
                }
            }
        }  

        $arrTplVar = array(
            'official' => $arrOfficialData,
        );
        $this->_ready();
        $this->setData($arrTplVar);
        return true;
    }

    /**
        * @brief 
        *
        * @param $forumStyle
        *
        * @return 
     */
    private static function _getCommonSwitchData($forumStyle) {

        $arrExtData = array();
        if(!isset($forumStyle[self::FORUM_STYLE_OFFICIAL_NAME]['official_common_switch']) || empty($forumStyle[self::FORUM_STYLE_OFFICIAL_NAME]['official_common_switch'])){
            return $arrExtData;
        }
        $arrSwitch = $forumStyle[self::FORUM_STYLE_OFFICIAL_NAME]['official_common_switch'];
        if (!is_array($arrSwitch)) {
            return $arrExtData;
        }
        $arrExtData['right'] = self::_getOfficialRightData($forumStyle);

        return $arrExtData;
    }
    /**
     * @brief 
     *
     * @param $forumStyle
     *
     * @return 
     */
    private static function _getOfficialRightData($forumStyle) {

        $arrRightData  = array();
        $arrRightStyle = isset($forumStyle[self::FORUM_STYLE_RIGHT_NAME])?$forumStyle[self::FORUM_STYLE_RIGHT_NAME]:array();
        $arrSwitch = $forumStyle[self::FORUM_STYLE_OFFICIAL_NAME]['official_common_switch'];

        //判断是否开通公告功能
        if (isset($arrSwitch['official_notice']) && intval($arrSwitch['official_notice']) === 1) {
            if (!empty($arrRightStyle) && isset($arrRightStyle['notice']) && !empty($arrRightStyle['notice'])) {
                $arrRightData['notice'] = $arrRightStyle['notice'];
            } else {
                $arrRightData['notice'] = array();
            }
        }
        //判断是否开通图片功能
        if (isset($arrSwitch['official_pic']) && intval($arrSwitch['official_pic']) === 1) {
            if (!empty($arrRightStyle) && isset($arrRightStyle['pic']) && !empty($arrRightStyle['pic'])) {
                $arrRightData['pic'] = $arrRightStyle['pic'];
            } else {
                $arrRightData['pic'] = array();
            }
        }            
        //判断是否开通视频功能
        if (isset($arrSwitch['official_video']) && intval($arrSwitch['official_video']) === 1) {
            if (!empty($arrRightStyle) && isset($arrRightStyle['video']) && !empty($arrRightStyle['video'])) {
                $arrRightData['video'] = $arrRightStyle['video'];
            } else {
                $arrRightData['video'] = array();
            }
        }
        //排序
        if (!empty($arrRightStyle['order']) && is_array($arrRightStyle['order'])) {
            foreach ($arrRightStyle['order'] as $key=>$value) {
                if(isset($arrRightData[$value])) {
                    $arrRightData['order'][] = $value; 
                }
            }
        } else {
            //default order
            if(isset($arrRightData['notice'])) {
                $arrRightData['order'][] = 'notice';
            }
            if(isset($arrRightData['pic'])) {
                $arrRightData['order'][] = 'pic';
            }            
            if(isset($arrRightData['video'])) {
                $arrRightData['order'][] = 'video';
            }
        }

        return $arrRightData;
    }

    /**
        * @brief 
        *
        * @param $strExtName
        * @param $arrDwzRes
        *
        * @return 
     */
    private static function _getOfficialOtherExt($strExtName, $arrDwzRes) {
        switch ($strExtName) {
            case 'qrcode':
                $arrCard = self::$_arrDependData['Core_Forum']['card'];
                $arrInput = array(
                    'avatar' => $arrCard['avatar'],
                    'long_url' => self::PREFIX_OFFICIAL_QRCODE_URL.self::$_strForumName,
                    'width'    => self::OFFICIAL_QRCODE_WIDTH,
                    'margin'   => self::OFFICIAL_QRCODE_MARGIN,
                    'is_share' => true,
                );
                $strQrcode = self::_getOfficialQrcode($arrInput, $arrDwzRes);
                return $strQrcode;
                break;
            }
        //return true;
    }

    /**
        * @brief 
        *
        * @param $arrInput
        * @param $arrDwzRes
        *
        * @return 
     */
    private static function _getOfficialQrcode($arrInput, $arrDwzRes) {
        $strUrl = '';
        if (isset($arrDwzRes['ret']['tinyurl'])) {
            $strUrl = $arrDwzRes['ret']['tinyurl'];
        } else {
            $strUrl = $arrInput['long_url'];
        }
        $arrQrcodeInput = array(
            'data'    => $strUrl,
            'logo'    => $arrInput['avatar'],
            'width'   => $arrInput['width'],
            'margin'  => $arrInput['margin'],
            'Fid'     => self::$_intForumId, 
            'isShare' => $arrInput['is_share'],
        );
        $arrQrcodeRes = Tieba_Service::call('official', 'getQrcodeUrl', $arrQrcodeInput, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrQrcodeRes['errno'] || !isset($arrQrcodeRes['ret']['picUrl'])) {
            Bingo_Log::warning('call official getQrcodeUrl error '.serialize($arrQrcodeInput).'_'.serialize($arrQrcodeRes));
            return '';
        }
        return $arrQrcodeRes['ret']['picUrl'];            
    }

    /**
        * @brief 
        *
        * @return 
     */
    private static function _getBaseRedisObj() {
        if (self::$_baseRedis) {
            return self::$_baseRedis;
        }
        Bingo_Timer::start('redisinit');
        self::$_baseRedis = new Bingo_Cache_Redis(self::BASE_REDIS_PID);
        Bingo_Timer::end('redisinit');    
        if (!self::$_baseRedis || !self::$_baseRedis->isEnable()) {
            Bingo_Log::warning("init redis fail.");
            self::$_baseRedis = null;
            return null;
        }
        return self::$_baseRedis;
    }

    /**
        * @brief 
        *
        * @param $arrForumStyle
        *
        * @return 
     */
    private static function _isShowRightInfo($arrForumStyle) {
        if (isset($arrForumStyle[self::FORUM_STYLE_OFFICIAL_NAME]['official_special_switch']['official_right_info'])  && 1 === intval($arrForumStyle[self::FORUM_STYLE_OFFICIAL_NAME]['official_special_switch']['official_right_info'])) {
            if (isset($arrForumStyle[self::FORUM_STYLE_RIGHT_INFO_NAME])) {
                $intOnlineTime = intval($arrForumStyle[self::FORUM_STYLE_RIGHT_INFO_NAME]['online_time']);
                $intOfflineTime = intval($arrForumStyle[self::FORUM_STYLE_RIGHT_INFO_NAME]['offline_time']);
                $intCur = time();
                if ($intCur >= $intOnlineTime && $intCur <= $intOfflineTime) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
        * @brief 
        *
        * @param $arrDependData
        * @param $arrContextData
        *
        * @return 
     */
    public function getCacheKey($arrDependData, $arrContextData) {
        return false;
    } 

    /**
     * @brief 
     *
     * @param $arrContextData
     *
     * @return 
     */
    public function init($arrContextData){
        $this->_addDep('Core_Forum');
    }

    /**
     * @brief 
     *
     * @param $arrDependData
     * @param $arrContextData
     *
     * @return 
     */
    public function process($arrDependData, $arrContextData){
        self::$_arrDependData = $arrDependData;
        $arrForum = $arrDependData['Core_Forum'];
        $forumStyle = $arrForum['attrs'];
        $intFid = $arrForum['forum_info']['forum_id'];
        $fname = $arrForum['forum_info']['forum_name'];

        if(!isset($forumStyle[self::FORUM_STYLE_OFFICIAL_NAME]) && $fname !== self::MALL_FORUM_NAME) {
            $this->_ready();
            return true;
        }

        //official
        $strIp = Bingo_Http_Ip::getUserClientIp();
        $intIp = Bingo_Http_Ip::ip2long($strIp);
        $arrInput = array(
            'forum_id' => $intFid,
            'ip_int'   => $intIp,
        );

        if (isset($forumStyle[self::FORUM_STYLE_OFFICIAL_NAME])) {
            $arrMultiInput[] = array(
                'service' => 'official',
                'method' => 'getOfficialStyle',
                'input' => $arrInput,
            );
            $arrDwzInput = array(
                'longUrl' => self::PREFIX_OFFICIAL_QRCODE_URL.$fname,
                'forum_id' => $intFid,
            );
            $arrMultiInput[] = array(
                'service' => 'official',
                'method' => 'getBdDwz',
                'input' => $arrDwzInput,
            );
            //右侧资讯类
            self::$_intIsShowRightInfo = self::_isShowRightInfo($forumStyle);
            if (self::$_intIsShowRightInfo) {
                $arrInput = array(
                    'forum_id' => $intFid,
                );
                $arrMultiInput[] = array(
                    'service' => 'official',
                    'method' => 'getOfficialForumRightInfo',
                    'input' => $arrInput,
                );
            }
        }

        $this->_ready();
        $this->_registerService($arrMultiInput, 'officialCallBack');
    }

}
