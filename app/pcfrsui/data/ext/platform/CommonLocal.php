<?php

class Ext_Platform_CommonLocal extends Util_DataProvider {

    const FORUM_STYLE_OFFICIAL_NAME = 'official';
    const COMMON_AD_ATTR_NAME       = 'common_place_ad';
    const NO_TASK_ID                = -1;
    const NO_LOCAL_ID               = -2;

    private $arrForum;
    private $arrUser;

    /**
     * @param   $arrContextData
     * @return 
     */
    public function init($arrContextData) {
        $this->_addDep('Core_Forum');
    }

    /**
     * @param  $arrDependData
     * @param  $arrContextData
     * @return bool
     */
    public function process($arrDependData, $arrContextData) {
        $curTime = time();
        $arrForumBase = $arrDependData['Core_Forum'];
        $this->arrForum = $arrForumBase;

        $arrStyle = $arrForumBase['attrs'];
        $intFid = intval($arrForumBase['forum_id']);
        $arrMultiInput = array();
        if (isset($arrStyle[self::COMMON_AD_ATTR_NAME]) && !isset($arrStyle[self::FORUM_STYLE_OFFICIAL_NAME])) {
            $arrInput = array(
                'forum_id' => $intFid,
            );
            $method = 'getPlaceAdByFid';
            $arrMultiInput[] = array(
                'service'  => 'vertical',
                'method'   => $method,
                'input'    => $arrInput,
            );
        }
        if (empty($arrMultiInput)) {
            $this->_ready();
            return true;
        }
        $this->_registerService($arrMultiInput, 'CommonLocalCallBack');
    }

    /**
     * @param $arrMultiOut
     * @return array
     */
    public function CommonLocalCallBack($arrMultiOut) {
        $rightRes = $arrMultiOut[0];
        if (false === $rightRes || Tieba_Errcode::ERR_SUCCESS !== $rightRes['errno']) {
            Bingo_Log::warning("call vertical getPlaceAdByFid fail param:". serialize($arrInput));
            $this->_ready();
            return true;
        }
        $localArr = $taskIdArr = array();
        foreach ($rightRes['data'] as $val) {
            if (!empty($val)) {
                self::_getLogData($val, $localArr, $taskIdArr);
            }
        }
        $output['local_aside'] = $rightRes['data'];
        $arrRet['common_local_ad'] = $output;
        $localStr = $taskIdStr = '';
        if ($localArr && $taskIdArr) {
            $localStr  = implode(',', $localArr);
            $taskIdStr = implode(',', $taskIdArr);
            Tieba_Stlog::addNode('common_local_ad_local_id_str', $localStr);
            Tieba_Stlog::addNode('common_local_ad_task_id_str', $taskIdStr);
        }
        $this->_ready();
        $this->setData($arrRet);
        return true;
    }

    /**
     * @param  $inputData
     * @param  &$localArr
     * @param  &$taskIdArr
     * @return array
     */
    private function _getLogData($inputData, &$localArr, &$taskIdArr) {
        foreach ($inputData as $val) {
            $localArr[] = isset($val['local_id']) ? $val['local_id'] : self::NO_LOCAL_ID;
            if (isset($val['is_not_track']) && $val['is_not_track']) {
                $taskIdArr[] = self::NO_TASK_ID;
            } else {
                $taskIdArr[] =  isset($val['task_id']) ? $val['task_id'] : self::NO_TASK_ID;
            }
        }
    }

    /**
     * @param  $arrDependData
     * @param  $arrContextData
     * @return bool
     */
    public function getCacheKey($arrDependData, $arrContextData) {
        return false;
    }
}
