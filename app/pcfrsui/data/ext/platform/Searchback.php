<?php
class Ext_Platform_Searchback extends Util_DataProvider {

//    const MAX_QUERY_COUNT = 3;
    const CACHE_LIFE_TIME = 30; //cacheʱ��̫����Ӱ���첽�������ݵ�չʾ����һ�ι��ڲ�ȡ���첽����

    private static $_cache   = null;
    private static $_keyPre  = 'search_fid_';
    private $_intForumId = 0;
    private $_strForumName = '';

	/**
	 * @param
	 * @return
	 */
    public function init($arrContextData) {
        $this->_addDep('Core_Forum');
    }
	/**
	 * @param
	 * @return
	 */
    public function process($arrDependData, $arrContextData)
    {
        $flist = self::getConf("tb_wordlist_redis_sendsms","filterforumlist");
        //Bingo_Log::warning($arrForumBase['forum_name']."filterforumlistzlzlzl".var_export($flist,true));
        $arrForumBase = $arrDependData['Core_Forum']['forum_info'];
        if(in_array(strval($arrForumBase['forum_name']),$flist))
        {
            Bingo_Log::warning(strval($arrForumBase['forum_name']) ." in ".var_export($flist,true));
            return false;
        }
        $this->_strForumName = strval($arrForumBase['forum_name']);
        $this->_intForumId = intval($arrForumBase['forum_id']);
        //$strLever1Name = $objParam->getForumParam('level_1_name');
        //$strLever2Name = $objParam->getForumParam('level_2_name');
        
        $arrDir = $arrDependData['Core_Forum']['dir'];
        $strLevel1Name = $arrDir['level_1_name'];

        if (null==$this->_strForumName)
        {
            Bingo_Log::warning("input param is Invalid, Fname:$this->_strForumName");
			$this->setData();
			$this->_ready();
            return false;
        }

        $arrPostListOut = array();
        $cache = self::getCache();
        if ($cache && $cache->isEnable())
        {
            $arrKey = self::getSearchCacheKey($this->_intForumId);
            $cacheValue = $cache->get($arrKey);
			$realVal = array();
            if (!empty($cacheValue) && false != $cacheValue)
            {
				$realVal = unserialize($cacheValue);
				if(empty($realVal) || !is_array($realVal)){
					$realVal = array();
				}

				$back_urls = self::generateUrls($realVal);
				//��notice��־����������ͬѧͳ������
				$arrOut['search_back']= $back_urls;

				$this->setData($arrOut);
				$this->_ready();
				return true;
			}
		}
		else
        {
            Bingo_Log::warning("getCache fail! cache name:[ forum_nginx]");
        }

		$page_url = $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        // ����������ѯ
        $arrInput = array();
        $arrInput['forum_name'] = $this->_strForumName;
        $arrInput['url'] = $page_url;
        $arrInput['type'] = 'frs';
        //$arrInput['thread_id'] = $intTid;
        $arrInput['dir_1'] = $strLevel1Name;
        //$arrInput['post_list'] = $arrPostList; //����ÿҳǰ��¥
        //$arrInput['pid_list'] = $arrPids;    //����pids
        $arrMultiInput[] = array(
            'service'  => 'push',
            'method'        => 'getSearchbackWords',
            'input'         => $arrInput,
            'ie'            => 'gbk',
        );
        $this->_registerService($arrMultiInput, 'SearchbackCallback');
        return true;
    }

	/**
	 * @param
	 * @return
	 */
    public function SearchbackCallback($arrMultiOut)
    {
        $queryRet = $arrMultiOut[0];
        if (false===$queryRet || isset($queryRet['errno'])&&$queryRet['errno']!=0)
        {
            Bingo_Log::warning("get data with psnlp::getLinkContent error!");
			$this->setData();
			$this->_ready();
			return false;
        }
        elseif (empty($queryRet['data']))
        {
            $arrOut['search_back'] = array();
			$this->setData($arrOut);
			$this->_ready();
			return true;
        }

        //$intTid = $objParam->getPostParam('thread_id');
        //use https, if user_agent is /chrome|firefox|safari|msie 10|rsv:11|msie [89]/

        $arrInCache = array();
		if(!isset($queryRet['data']) || empty($queryRet['data']) || !is_array($queryRet['data']))
        {
			$arrOut['search_back']= array();
			$this->setData($arrOut);
			$this->_ready();
			return true;
		}else{
            $arrInCache = array(
                'key' => self::$_keyPre.strval($this->_intForumId),
                //'value' => serialize(Bingo_Encode::convert($queryRet['data'], Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK)),
                'value' => serialize($queryRet['data']),
                'expire' => self::CACHE_LIFE_TIME,
                );
		}

        $cache = self::getCache();
        if ($cache && $cache->isEnable())
        {
            //$cache->add($arrInCache);
            $cache->add($arrInCache['key'],$arrInCache['value'],$arrInCache['expire']);
        }
        else
        {
            Bingo_Log::warning("getCache fail! cache name:[ forum_nginx ]");
        }
        
		$back_urls = self::generateUrls($queryRet['data']);
        $arrOut['search_back']= $back_urls;
		$this->setData($arrOut);
		$this->_ready();
		return true;
    }

	/**
	 * @param
	 * @return
	 */
    protected static function getCache() {
        if (is_null(self::$_cache)) {
            self::$_cache = new Bingo_Cache_Memcached("forum_nginx");
        }
        return self::$_cache;
    }

	/**
	 * @param
	 * @return
	 */
	protected static function generateUrls ($arrParam){
		$url_prefix = 'https://www.baidu.com/s?wd=';
		$url_suffix = '';
		$ret = array();
		foreach($arrParam as $word){
			//if(!is_utf8($word['term'])){
			//$url['word'] = Bingo_Encode::convert($word['term'],Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
			//}else{
			$url['word'] = $word['term'];
			//}
			$url_suffix = '&tn=SE_pctiebalist_1qeikwq3&rsv_dl=0_cont_tieba_frs_'.$word['pos'].'&rsf='.$word['feature'];
			$url['url'] = $url_prefix.urlencode($url['word']).$url_suffix;
			$ret[] = $url;
		}
		return $ret;
	}

	/**
	 * @param
	 * @return
	 */
    protected static function getSearchCacheKey ($arrParam)
    {
        $arrKey = array();
        if (!empty($arrParam))
        {
			if ($arrParam > 0)
			{
				$arrKey = self::$_keyPre . strval($arrParam);
			}
        }
        return $arrKey;
    }
	
	/**
	 * @param
	 * @return
	 */
    protected static function getPrivateCacheKey ($arrParam)
    {
        $arrKey = array();
        if (is_array($arrParam) && !empty($arrParam))
        {
            foreach ($arrParam as $value) {
                if ($value > 0)
                {
                    $arrKey[] = self::$_keyPre . strval($value);
                }
            }
        }
        return $arrKey;
    }

    /**
     * 
     * @param 
     * @return
     */
    public function getCacheKey($arrDependData, $arrContextData) {
        return false;
    }

    /**
      * [getConf description]
      * @param  [type] $strTableName [description]
      * @param  [type] $key          [description]
      * @return [type]               [description]
      */
     private static function getConf($strTableName, $key,$type=""){
        $handleWordServer = Wordserver_Wordlist::factory();
        $arrItemInfo = $handleWordServer->getValueByKeys(array($key), $strTableName);
        if ($arrItemInfo == false || !is_array($arrItemInfo) || !array_key_exists($key, $arrItemInfo)) {
            Bingo_Log::warning ( 'get wordlist tagName_data error'. var_export($arrItemInfo,true));
            return false;
        } else {
            if($type == "str")
            {
                return $arrItemInfo[$key];
            }
            else
            {
                return unserialize($arrItemInfo[$key]);
            }
        }
     }
}
