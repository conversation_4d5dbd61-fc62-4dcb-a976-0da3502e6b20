<?php
/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/3/15
 * Time: 1:32
 */
class Ext_Platform_Profession extends Util_DataProvider {
    const PROFESSION_ATTR_NAME = 'forum_profession';
    const ORDINARY_ATTR_NAME = 'forum_ordinary';
    const WAIT_CHECK = 0;
    const PASS_AUDIT = 2;
    const UNPASS_AUDIT = 3;
    const PROFESSION_FORUM_STATUS = 2;
    const USER_UN_PASS_AUDIT = 0;
    const USER_PASS_AUDIT = 1;
    const INFO_HAS_NOT_READ = 0;
    
    public function init($arrContentData) {
        $this->_addDep('Core_Forum');    
        $this->_addDep('Core_Perm');
        $this->_addDep('Core_User');
    }
    
    public function process($arrDependData, $arrContentData) {
        $arrRes = array();
        $arrForumBase = $arrDependData['Core_Forum']['forum_info'];
        $arrUserBase = $arrDependData['Core_User'];
        $arrStyle = $arrDependData['Core_Forum']['attrs'];
        $intFid = intval($arrForumBase['forum_id']);
        $intCurUid = intval($arrUserBase['user_id']);
        if (!empty($arrStyle[self::PROFESSION_ATTR_NAME])) {
            $arrProfessionInfo = $arrStyle[self::PROFESSION_ATTR_NAME];
            $intStatus = intval($arrProfessionInfo['status']);
            $intUid = intval($arrProfessionInfo['user_id']);
            $intWaitCheckStatus = intval($arrProfessionInfo['is_wait_check']);
            $intWaitAuditStatus = intval($arrProfessionInfo['is_wait_audit']);
            $intAuditStatus = isset($arrProfessionInfo['is_pass_audit'])?intval($arrProfessionInfo['is_pass_audit']) : -1;
            $intHasReadStatus = intval($arrProfessionInfo['is_has_read']);
            if ($intWaitCheckStatus && $intCurUid === $intUid) {
                $arrRes['professional_manager_apply_id'] = intval($arrProfessionInfo['task_id']);
                $arrRes['is_inviting_as_professional_manager'] = 1;
            } else if ($intCurUid === $intUid && $intWaitAuditStatus){
                ;
            } else if ($intCurUid === $intUid && $intAuditStatus === self::USER_PASS_AUDIT && $intHasReadStatus === self::INFO_HAS_NOT_READ  ) {
                $arrRes['professional_manager_apply_id'] = intval($arrProfessionInfo['task_id']);
                $arrRes['is_audit_as_professional_manager_success'] = 1;
            } else if ($intCurUid === $intUid && $intAuditStatus === self::USER_UN_PASS_AUDIT && $intHasReadStatus === self::INFO_HAS_NOT_READ) {
                $arrRes['professional_manager_apply_id'] = intval($arrProfessionInfo['task_id']);
                $arrRes['is_audit_as_professional_manager_success'] = 0;
                $arrRes['fail_reason'] = strval(trim($arrProfessionInfo['reason']));
            }
            if($intStatus === self::PROFESSION_FORUM_STATUS || $intWaitCheckStatus || $intWaitAuditStatus ) {
                $arrRes['is_profession_forum'] = 1;
            }

        }

        if (!empty($arrStyle[self::ORDINARY_ATTR_NAME])) {
            $arrOrdinaryAttr = $arrStyle[self::ORDINARY_ATTR_NAME];
            foreach($arrOrdinaryAttr as $intKey => $arrOrdinaryInfo) {
                if($intCurUid === $intKey) {
                    $intStatus = intval($arrOrdinaryInfo['status']);
                    $intHasReadStatus = intval($arrOrdinaryInfo['is_has_read']);
                    if ($intStatus === self::WAIT_CHECK && !$intHasReadStatus) {
                        $arrRes['manager_apply_id'] = intval($arrOrdinaryInfo['task_id']);
                        $arrRes['is_inviting_as_manager'] = 1;
                    } else if ($intStatus === self::PASS_AUDIT && !$intHasReadStatus ) {
                        $arrRes['manager_apply_id'] = intval($arrOrdinaryInfo['task_id']);
                        $arrRes['is_audit_as_manager_success'] = 1;
                    } else if ($intStatus === self::UNPASS_AUDIT && !$intHasReadStatus) {
                        $arrRes['manager_apply_id'] = intval($arrOrdinaryInfo['task_id']);
                        $arrRes['is_audit_as_manager_success'] = 0;
                        $arrRes['fail_reason'] = strval(trim($arrOrdinaryInfo['reason']));
                    }
                }
            }

        }
        $arrOutPut['profession'] = $arrRes;
        $this->setData($arrOutPut);
        $this->_ready();
        return true;
    }

    public function getCacheKey($arrDependData, $arrContentData) {
        return false;
    }

}