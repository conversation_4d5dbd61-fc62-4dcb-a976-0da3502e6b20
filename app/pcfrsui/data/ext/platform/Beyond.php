<?php

class Ext_Platform_Beyond extends Util_DataProvider {
        
    const BEYOND_CHANNEL_DATA_KEY = "beyond_channel_data_frs_pb_ui";  // 全部频道信息cache key
    const DEFAULT_EXPIRE = 60; // cache时间

    private static $_cache = null;
    private $_arrForumDir = array();
    private $_arrUserInfo = array();
    private $_intForumId = 0;
    
    /**
     * @brief 
     *
     * @param $arrContextData
     *
     * @return 
     */
    public function init($arrContextData){
        $this->_addDep('Core_Forum');
        $this->_addDep('Core_User');
    }

    /**
     * @brief 
     *
     * @param $arrDependData
     * @param $arrContextData
     *
     * @return 
     */
    public function process($arrDependData, $arrContextData) {
        $arrForum = $arrDependData['Core_Forum'];
        $this->_arrForumDir = $arrForum['dir'];
        $this->_arrUserInfo = $arrDependData['Core_User'];

        $this->_intForumId = $arrForum['forum_info']['forum_id'];
        
        // 获取垂类频道全集（包括吧目录和类目信息）
        $arrChannelInfo = self::_getCacheChannelData();
        if (false !== $arrChannelInfo) {
            $bol = $this->_buildResult($arrChannelInfo);
            return $bol;
        } else {
            $arrMultiInput = array();
            $strMethod = 'getChannelListForThreadPush';
            $arrInput = array();
            $arrMultiInput[] = array(
                'service' => 'beyond',
                'method' => $strMethod,
                'input' => $arrInput,
            );
            $this->_registerService($arrMultiInput, 'Callback');
            return true;
        }
    }
    
    /**
     * @brief 
     *
     * @param $arrMultiOut
     *
     * @return 
     */
    public function Callback($arrMultiOut) {
        $arrOutData = $arrMultiOut[0];
        if (false === $arrOutData || Tieba_Errcode::ERR_SUCCESS !== $arrOutData['errno']) {
            Bingo_Log::warning("call beyond getChannelListForThreadPush fail, arrMultiOut:". serialize($arrMultiOut));
            $arrChannelInfo = array();
        } else {
            $arrChannelInfo = $arrOutData['data'];
            self::_addCacheChannelData($arrChannelInfo);
        }
        $bol = $this->_buildResult($arrChannelInfo);
        return $bol;
    }

    /**
     * @brief 
     *
     * @param $arrDependData
     * @param $arrContextData
     *
     * @return 
     */
    public function getCacheKey($arrDependData, $arrContextData) {
        return false;
    }
    
    private function _buildResult($arrChannelInfo) {
        $arrRet = array(
            'thread_can_be_pushed' => false,
        );
        if (count($arrChannelInfo) > 0) {
            $arrRet['base']['channel_info'] = $arrChannelInfo;        
            //验证娱乐频道目录
            $strLever1Name = $this->_arrForumDir['level_1_name'];
            $strLever2Name = $this->_arrForumDir['level_2_name'];
            $intChannelId = -1;
            foreach ($arrChannelInfo as $value) {
                foreach ($value['forum_dir'] as $key => $elem) {
                    $strForumDir1 = (string)$elem['level_1_name'];
                    if ($elem['type'] == 1 && in_array($this->_intForumId, $elem['forum_id'])){//tyep==1为游戏单吧吧作为频道，add by jiangshuai
                        $intChannelId = (int)$value['id'];                  
                        break;
                    } elseif ($elem['type'] == 0 && $strLever1Name == $strForumDir1 && (in_array($strLever2Name, $elem['level_2_name']) || in_array("", $elem['level_2_name']))) {
                        $intChannelId = (int)$value['id'];                  
                        break;
                    }                
                }
                if (-1 != $intChannelId) {
                    break;
                }
            }
            
            // 频道id
            $arrRet['base']['channel_id'] = $intChannelId;
            
            // 类目信息
            if (-1 != $intChannelId) {            
                foreach ($arrChannelInfo as $value) {
                    if ($intChannelId == (int)$value['id']) {                    
                        $arrRet['channel_info'] = array(
                            'id' => (int)$value['id'],
                            'name' => (string)$value['name'],
                            'category' => array(),
                        );
                        foreach ($value['category'] as $val) {
                            $arrRet['channel_info']['category'][] = array(
                                'id' => (int)$val['id'],
                                'name' => (string)$val['name'],
                            );
                        }
                        break;
                    }
                } 
                // 验证是否是M or T用户
                if (isset($this->_arrUserInfo['beyond_user_info'])) {
                    $arrBeyondUserInfo = $this->_arrUserInfo['beyond_user_info'];
                    if (isset($arrBeyondUserInfo[$intChannelId])) {
                        $arrRet['thread_can_be_pushed'] = true;
                    }
                }
            } else {
                $arrRet['channel_info'] = array();
            }
        }
        $arrTplVar['beyond'] = $arrRet;
        $this->setData($arrTplVar);
        $this->_ready();
        return true;
    }
    
    /**
     * @brief 
     *
     * @param $arrDependData
     * @param $arrContextData
     *
     * @return 
     */
    private static function _getCacheChannelData(){
        if(!self::$_cache){
            Bingo_Timer::start('beyond_channel_memcached_init');
            self::$_cache = new Bingo_Cache_Conver("forum_official");
            Bingo_Timer::end('beyond_channel_memcached_init');
        }
        if(!self::$_cache){
            Bingo_Log::warning(self::MEMCACHED_NAME." init cache fail.");
            self::$_cache = null;
            return false;
        }
        $cacheKey = self::BEYOND_CHANNEL_DATA_KEY;
        Bingo_Timer::start('beyond_channel_memcached_get');
        $res = self::$_cache->get($cacheKey);
        Bingo_Timer::end('beyond_channel_memcached_get');
        if (null != $res && false != $res) {
            $data = unserialize($res);
            if(!is_array($data)){
                return false;
            }
            return $data;
        }
        return false;
    }
    
    /**
     * @brief 
     *
     * @param $arrDependData
     * @param $arrContextData
     *
     * @return 
     */
    private static function _addCacheChannelData($arrChannelData){
        if(!self::$_cache){
            Bingo_Timer::start('beyond_channel_memcached_init');
            self::$_cache = new Bingo_Cache_Conver("forum_official");
            Bingo_Timer::end('beyond_channel_memcached_init');
        }
        if(!self::$_cache){
            Bingo_Log::warning(self::MEMCACHED_NAME." init cache fail.");
            self::$_cache = null;
            return false;
        }
        $cacheKey = self::BEYOND_CHANNEL_DATA_KEY;
        $mixValue = serialize($arrChannelData);
        Bingo_Timer::start('beyond_channel_memcached_add');
        $ret = self::$_cache->add(strval($cacheKey), $mixValue, self::DEFAULT_EXPIRE );
        Bingo_Timer::end('beyond_channel_memcached_add');
        if ($ret == 0) {
            return true;
        } else {
            Bingo_Log::warning ( 'add cache err no : ' . $ret. " strKey:$cacheKey, value:$mixValue" );
            return false;
        }
    }

}
