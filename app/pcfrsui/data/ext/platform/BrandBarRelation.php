<?php
/***************************************************************************
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 **************************************************************************/

/**
 * @file   BrandBarRelation.php
 * <AUTHOR>
 * @date   2018/08/27 12:04:00
 * @brief
 **/
class Ext_Platform_BrandBarRelation extends Util_DataProvider
{
	private static $_cache = null;
	private $arrForum;
	private $brand_adsense_switch = 1;
	private $rightdown_adsense_switch = 2;
	private $arrAllowAd = array(
		'娱乐明星',
		'游戏',
		'音乐',
		'网友俱乐部',
		'体育',
		'生活',
		'社会',
		'商业服务',
		'情感',
		'其他',
		'金融',
		'动漫',
		'电影',
		'电视剧',
		'电视节目',
		'电脑数码',
		'地区',
	);
	//private $arrAllowAd = array(
	//	'情感',
	//);
	
	/**
	 * @param   $arrContextData
	 *
	 * @return
	 */
	public function init($arrContextData)
	{
		$this->_addDep('Core_Forum');
		$this->_addDep('Core_Session');
	}
	
	/**
	 * @param  $arrDependData
	 * @param  $arrContextData
	 *
	 * @return bool
	 */
	public function process($arrDependData, $arrContextData)
	{
		$this->arrForum = $arrDependData['Core_Forum'];
		$intForumId     = $this->arrForum['forum_info']['forum_id'];
		$level_1_name   = $this->arrForum['dir']['level_1_name'];
		$level_2_name   = $this->arrForum['dir']['level_2_name'];

		$strIp          = Bingo_Http_Ip::getUserClientIp();
		$config = new Tieba_Config_Get("7e0b643051d2933d98a1552481cc0abb11");
		$arrConfgRet = $config->get();
		$intSwitch   = intval($arrConfgRet['data']);
		if ($intSwitch === 1) {
			if(in_array($level_1_name, $this->arrAllowAd) || in_array($level_2_name, $this->arrAllowAd)){
				$this->brand_adsense_switch     = 1;
				$this->rightdown_adsense_switch = 1;
				//$this->brand_adsense_switch = $level_1_name.' '.$level_2_name.' '.$strIp;
				$arrLocation = $this->_getLocationByIp($strIp);
				if($arrLocation["province"] == '北京市' || $arrLocation["province"] == '上海市' || $arrLocation["city"] == '广州市' || $arrLocation["city"] == '深圳市'){
					$this->brand_adsense_switch     = 2;
					$this->rightdown_adsense_switch = 2;
				}
			}
		}

		$strFname = $this->arrForum['forum_info']['forum_name'];
        $strKey = "5f2903b14224e47a5273b443044fc6e213";
		$config = new Tieba_Config_Get($strKey);
		$arrRes = $config->get();	
		$arrFnames = $arrRes['data'];
		if (in_array($strFname, $arrFnames)) {
			$this->brand_adsense_switch     = 2;
		}

		$arrMultiInput   = array();
		$arrMultiInput[] = array(
			'service' => 'tbmall',
			'method'  => 'getRelationBarFocusInfo',
			'input'   => array('forum_id' => $intForumId),
		);

		$this->_registerService($arrMultiInput, 'BrandBarRelationCallBack');

		//相关吧内头部入口及“品牌吧入口”请求标记
		Tieba_Stlog::addNode("req_relationbar_focus_info", 1);
	}
	
	/**
	 * @param $arrMultOut
	 *
	 * @return array
	 */
	public function BrandBarRelationCallBack($arrMultiOut)
	{
		$arrRet = array(
			'brand_adsense_switch'     => $this->brand_adsense_switch,
			'rightdown_adsense_switch' => $this->rightdown_adsense_switch,
		);
		$res    = $arrMultiOut[0];
		if(empty($res) || Tieba_Errcode::ERR_SUCCESS !== $res['errno'] || empty($res['data'])){
			$strLog = 'getMultiInput return error ['.serialize($res).']';
			Bingo_Log::warning($strLog);
			$this->_ready();
			$this->setData($arrRet);
			return true;
		}
		//如果有品牌吧就不显示
		$arrRet = array(
			'brand_adsense_switch'     => 2,
			'rightdown_adsense_switch' => 2,
		);
		$arrRet ['info'] = $res['data'];
        $pic_info_num = isset($arrRet['info']['pic_info']) ? count($arrRet['info']['pic_info']) : 0;
        //相关吧头部入口返回量
        Tieba_Stlog::addNode("ret_relationbar_focus_num", $pic_info_num);
        //相关吧头部入口epv
        Tieba_Stlog::addNode("ret_relationbar_focus", 1);

        $this->_ready();
		$this->setData($arrRet);
		return true;
	}
	
	/**
	 * @param $strIP
	 *
	 * @return array|mixed
	 */
	private function _getLocationByIp($strIP)
	{
		$cache      = new Bingo_Cache_Memcached("forum_common");
		$cacheKey   = "mygetLocationByIp_{$strIP}";
		$cacheValue = $cache->get($cacheKey);
		if(!empty($cacheValue)){
			return json_decode($cacheValue, true);
		}
		$strUrl = "http://api.ip.baidu.com/ip?ip={$strIP}";
		// user:tieba, passwd:3ls8ez3gorgggccc0o00s4owk
		$arrHeader    = array(
			"Authorization: Basic dGllYmE6M2xzOGV6M2dvcmdnZ2NjYzBvMDBzNG93aw==",
		);
		$arrRet       = array(
			'province' => "",
			'city'     => "",
		);
		$arrOpts      = array(
			'timeout'           => 5000,
			'conn_timeout'      => 10000,
			'max_response_size' => 10000000,
		);
		$objHttpproxy = Orp_FetchUrl::getInstance($arrOpts);
		$ret          = $objHttpproxy->get($strUrl, $arrHeader, array());
		if($objHttpproxy->errno() != 0){
			return $arrRet;
		}
		$arrTmpRet = json_decode($ret, true);
		if(empty($arrTmpRet)){
			return $arrRet;
		}else if(intval($arrTmpRet["status"]) != 0){
			return $arrRet;
		}
		if($arrTmpRet["data"][0]["prov-full"] != "None"){
			$arrRet["province"] = strval($arrTmpRet["data"][0]["prov-full"]);
		}
		if($arrTmpRet["data"][0]["city-full"] != "None"){
			$arrRet["city"] = strval($arrTmpRet["data"][0]["city-full"]);
		}
		$cache->add($cacheKey, json_encode($arrRet), 86400);
		return $arrRet;
	}
	
	/**
	 * @param  $arrDependData
	 * @param  $arrContextData
	 *
	 * @return bool
	 */
	public function getCacheKey($arrDependData, $arrContextData)
	{
		return false;
	}
	
	/**
	 * @param
	 *
	 * @return
	 */
	protected static function getCache()
	{
		if(is_null(self::$_cache)){
			self::$_cache = new Bingo_Cache_Memcached("forum_coreuser");
		}
		return self::$_cache;
	}
}
