<?php 
class Ext_Platform_Localdeal extends Util_DataProvider {
    
    const OFFICIAL_LOCAL_DEAL = 'official_local_deal';
    const OFFICIAL_ATTR       = 'official';
    const DEAL_URL_FIELD      = 'tab';
    const ADD_DEAL_MARK       = 'newDeal';

    private $arrForum;
    private $arrUser;
    private $arrContextData;
    /**
     * @param   $arrContextData
     * @return 
     */
    public function init($arrContextData) {
        $this->_addDep('Core_Forum');
    }

    /**
     * @param  $arrDependData
     * @param  $arrContextData
     * @return bool
     */
    public function process($arrDependData, $arrContextData) {
        $arrRet = array();
        $this->arrForum = $arrDependData['Core_Forum'];
        $arrStyle = $this->arrForum['attrs'];
        $intFid = intval($this->arrForum['forum_info']['forum_id']);

        if(!isset($arrStyle[self::OFFICIAL_ATTR]['official_common_switch'][self::OFFICIAL_LOCAL_DEAL])
           || 1 != $arrStyle[self::OFFICIAL_ATTR]['official_common_switch'][self::OFFICIAL_LOCAL_DEAL]) {
            $this->_ready();
            $this->setData($arrRet);
            return true;
        }
        $method = 'getCategoryByFid';
        $arrInput = array(
            'forum_id' => $intFid,
        );
        $arrMultiInput[] = array(
            'service'  => 'vertical',
            'method'        => $method,
            'input'         => $arrInput,
        );
        $this->_registerService($arrMultiInput, 'callBack');
    }

    /**
     * @param array
     * @return array
     */
    public function callBack($arrMultiOut) {
        $arrRet = array();
        $arrCateRes = $arrMultiOut[0];
        if (false === $arrCateRes || Tieba_Errcode::ERR_SUCCESS !== $arrCateRes['errno']) {
            Bingo_Log::warning("call vertical getCategoryByFid fail param:". serialize($arrInput));
            $this->_ready();
            $this->setData($arrRet);
            return $arrRet;
        }
        // $arrRequestStr = $objCoreData->getRequest('querystring');
        $arrRequestStr = $this->arrContextData['request']['querystring'];

        Tieba_Stlog::addNode('deal_page', 'frs');
        //add stlog
        if (self::ADD_DEAL_MARK != $arrRequestStr[self::DEAL_URL_FIELD]) {
            Tieba_Stlog::addNode('is_deal_thread', 1);
        } else if (self::ADD_DEAL_MARK == $arrRequestStr[self::DEAL_URL_FIELD]) {
            Tieba_Stlog::addNode('add_deal_thread', 1);
        }
        $arrRet['deal_cate'] = $arrCateRes['data'];
        $this->_ready();
        $this->setData($arrRet);
        return true;
    }

    /**
     * @param  $arrDependData
     * @param  $arrContextData
     * @return bool
     */
    public function getCacheKey($arrDependData, $arrContextData) {
        return false;
    }
}
