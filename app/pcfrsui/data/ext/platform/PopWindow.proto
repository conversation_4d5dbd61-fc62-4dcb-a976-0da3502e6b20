package pcfrsui.Ext.Platform.PopWindow; 
import 'core/Common.proto'; 
import 'core/Forum.proto'; 
message popwindowTpl
{
    optional uint64 adId=1;
    optional uint64 type=2;
    optional uint64 subtype=3;
    optional uint64 startTime=4;
    optional uint64 endTime=5;
    optional bytes forums=6;
    optional string taskName=7;
    optional uint64 position=8;
    optional string title=9;
    optional string windowTitle=10;
    optional string content=11;
    optional string link=12;
    optional string imgUrl=13;
}        
message Res
{
    optional popwindowTpl platformPopWindow=1;
}