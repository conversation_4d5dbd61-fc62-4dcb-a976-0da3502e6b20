<?php 
class Ext_Platform_Vertical extends Util_DataProvider {
    
    const CARD_ATTR_NAME     = 'forum_card';
    const CARD_DIR_MODULE_ID = 281;
    const PUT_TYPE_FORUM     = 0;
    const DIR_TYPE_FIRST     = 1;
    const DIR_TYPE_SECOND    = 2;
    private static $_arrValidPos = array(1, 5, 10); 
    
    private $arrForum;
    private $arrUser;
    
    /**
     * @param   $arrContextData
     * @return array
     */
    public function init($arrContextData) {
        $this->_addDep('Core_Forum');
    }

    /**
     * @param  $arrDependData
     * @param  $arrContextData
     * @return bool
     */
    public function process($arrDependData, $arrContextData) {

        $this->arrForum = $arrDependData['Core_Forum'];
        $this->arrUser = $arrDependData['Core_User'];
        
        $arrForumStyle = $this->arrForum['attrs'];
        $arrForumDirStyle = $this->arrForum['dir_style'];
        $intFid = $this->arrForum['forum_info']['forum_id'];
        Bingo_Log::warning(var_export($arrForumStyle, true));
        Bingo_Log::warning(var_export($arrForumDirStyle, true));

        if(!isset($arrStyle[self::CARD_ATTR_NAME]) && !isset($arrDirStyle[self::CARD_DIR_MODULE_ID])) {
            $this->_ready();
            return $arrRet;
        }

        $arrDir2Attr = self::_getDirStyle($arrDirStyle[self::CARD_DIR_MODULE_ID], self::DIR_TYPE_SECOND);
        $arrDir1Attr = self::_getDirStyle($arrDirStyle[self::CARD_DIR_MODULE_ID], self::DIR_TYPE_FIRST);
        $arrForumAttr = $arrStyle[self::CARD_ATTR_NAME];
        $arrCard = self::_getValidCard($arrForumAttr, $arrDir2Attr, $arrDir1Attr);
        if(false === $arrCard || empty($arrCard)) {
            $this->_ready();
            return $arrRet;
        }
        $method = 'getCardInfo';
        foreach ($arrCard as $id) {
            $arrInput = array(
                'id' => $id,
            );
            $arrMultiInput[] = array(
                'service'  => 'vertical',
                'method'   => $method,
                'input'    => $arrInput,
            );
        }

        $this->_registerService($arrMultiInput, 'callBackFunc');
    }

    /**
     * @param array
     * @return array
     */
    public function callBackFunc($arrMultiOut) {
        
        $strForumName = $this->arrForum['forum_info']['forum_name'];

        $arrOutput = array();
        foreach ($arrMultiOut as $arrCard) {
            if (false === $arrCard || Tieba_Errcode::ERR_SUCCESS !== $arrCard['errno']) {
                Bingo_Log::warning("call vertical getCardInfo fail, record:" . serialize($arrCard));
                continue;
            }
            $arrData = $arrCard['data'];
            if (!empty($arrData)) {
                if(intval($arrData['type']) !== self::PUT_TYPE_FORUM) {
                    if(in_array($strForumName, $arrData['except_forums'])) {
                        continue;
                    }
                    if(in_array($strForumName, $arrData['conflict_forums'])) {
                        continue;
                    }
                }
                $intReqOs = 0x01;
                $intPlatformType = intval($arrData['platform_type']);
                if(intval($intReqOs & $intPlatformType) === 0) {
                    continue;
                }
                $arrOutput[] = $arrData;
                Tieba_Stlog::addNode('vertical_card', 1);
                Tieba_Stlog::addNode('card_id', $arrData['id']);
            }
        }
        $arrRet['vertical_card'] = $arrOutput;
        $this->_ready();
        $this->setData($arrRet);
        return $arrRet;
    }


    /**
     * @param  $arrDependData
     * @param  $arrContextData
     * @return bool
     */
    public function getCacheKey($arrDependData, $arrContextData) {
        return false;
    }

    /**
     * @param  array  $arrTaskInfo [description]
     * @return [type]              [description]
     */
    protected static function _timeValid($arrTaskInfo = array()) {
        $curTime = time();
        if ($arrTaskInfo['start_time'] <= $curTime && $arrTaskInfo['end_time'] >= $curTime) {
            return true;
        }
        return false;
    }

    /**
     * @param  array  $arrForumAttr [description]
     * @param  array  $arrDir2Attr  [description]
     * @param  array  $arrDir1Attr  [description]
     * @return [type]               [description]
     */
    protected static function _getValidCard($arrForumAttr = array(), $arrDir2Attr = array(), $arrDir1Attr = array()) {
        $arrValidTask = array();
        $intReqOs = 0x01;
        foreach (self::$_arrValidPos as $pos) {
            $bolFound = false;
            if(!empty($arrForumAttr[$pos])) {
                foreach ($arrForumAttr[$pos] as $ele) {
                    $intPlatformType = intval($ele['platform_type']);
                    if(intval($intReqOs & $intPlatformType) === 0) {
                        continue;
                    }
                    if(self::_timeValid($ele)) {
                        $arrValidTask[] = $ele['task_id'];
                        $bolFound = true;
                        break;
                    }
                }
            }
            
            if($bolFound) {
                continue;
            }
            
            if(!empty($arrDir2Attr[$pos])) {
                foreach ($arrDir2Attr[$pos] as $ele) {
                    $intPlatformType = intval($ele['platform_type']);
                    if(intval($intReqOs & $intPlatformType) === 0) {
                        continue;
                    }
                    if(self::_timeValid($ele)) {
                        $arrValidTask[] = $ele['task_id'];
                        $bolFound = true;
                        break;
                    }
                }
            }
            if($bolFound) {
                continue;
            }
            if(!empty($arrDir1Attr[$pos])) {
                foreach ($arrDir1Attr[$pos] as $ele) {
                    $intPlatformType = intval($ele['platform_type']);
                    if(intval($intReqOs & $intPlatformType) === 0) {
                        continue;
                    }
                    if(self::_timeValid($ele)) {
                        $arrValidTask[] = $ele['task_id'];
                        $bolFound = true;
                        break;
                    }
                }
            }
        }
        return $arrValidTask;
    }

    /**
     * @param  array  $arrInput   [description]
     * @param  [type] $intDirType [description]
     * @return [type]             [description]
     */
    protected static function _getDirStyle($intDirType, $arrInput = array()) {
        if(empty($arrInput)) {
            return array();
        }

        foreach ($arrInput as $val) {
            if($intDirType === self::DIR_TYPE_FIRST) {
                if($val['type'] == 'level_1') {
                    if(isset($val['style_name'])) {
                        return Bingo_String::json2array($val['style_name']);
                    }
                }
            }
            if($intDirType === self::DIR_TYPE_SECOND) {
                if($val['type'] == 'level_2') {
                    if(isset($val['style_name'])) {
                        return Bingo_String::json2array($val['style_name']);
                    }
                }
            }
        }

        return array();
    }
}