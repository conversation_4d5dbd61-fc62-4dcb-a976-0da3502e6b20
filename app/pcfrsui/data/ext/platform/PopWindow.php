<?php 
/**
 * @file popWindow
 * <AUTHOR>
 * @date 2014/09/03
 * @brief �ط������
 **/
class Ext_Platform_PopWindow extends Util_DataProvider {
    const POP_ATTR_NAME = 'forum_pop';
    const POP_LOCATION=0;
    public function init($arrContentData) {
        $this->_addDep('Core_Forum');    
    }
    public function process($arrDependData, $arrContentData) {
        $arrRet['platformPopWindow'] = array();
        $intCurTime = time();
        $arrForumBase = $arrDependData['Core_Forum']['forum_info'];
        $arrStyle = $arrDependData['Core_Forum']['attrs'];
        $intFid = intval($arrForumBase['forum_id']);
        if(!isset($arrStyle[self::POP_ATTR_NAME])) {
            $this->setData($arrRet);
            $this->_ready();
            return true;
        }
        $arrPopValue = $arrStyle[self::POP_ATTR_NAME];
        $intTaskId = false;
        foreach ($arrPopValue as $val) {
            if ($val['start_time'] <= $intCurTime && $val['end_time'] >= $intCurTime) {
                $intTaskId = intval($val['task_id']);
                if(isset($val['location'])) {
                    $strLocation = strval($val['location']);
                }
                $arrLoaction=explode(",", $strLocation);
            }
        }
        if (false === $intTaskId || !in_array(self::POP_LOCATION, $arrLoaction)) {
            $this->setData($arrRet);
            $this->_ready();
            return true;
        }
        $arrInput = array('id' => $intTaskId);
        
        $arrMultiInput[] = array(
            'service'  => 'vertical',
            'method'   => 'getPopInfo',
            'input'    => $arrInput,
        );
        $this->_registerService($arrMultiInput, 'popWidowCalllback');      
        return true;
    }
    
    public function popWidowCalllback($arrOutput) {
        $arrRet['platformPopWindow'] = array();
        $arrPopRes = $arrOutput[0];
        if (false === $arrPopRes || Tieba_Errcode::ERR_SUCCESS != $arrPopRes['errno']) {
            Bingo_Log::fatal("call vertical getPopInfo fail param:". serialize($arrPopRes));
            $this->setData($arrRet);
            $this->_ready();
            return false;
        }
        if (!empty($arrPopRes['data'])) {
            $arrOut['adId']=intval($arrPopRes['data']['id']);
            $arrOut['type']=intval($arrPopRes['data']['type']);
            $arrOut['subtype']=intval($arrPopRes['data']['sub_type']);
            $arrOut['startTime']=intval($arrPopRes['data']['start_time']);
            $arrOut['endTime']=intval($arrPopRes['data']['end_time']);
            $arrOut['forums']=$arrPopRes['data']['forum_names'];
            $arrOut['taskName']=$arrPopRes['data']['task_name'];
            $arrOut['position']=intval($arrPopRes['data']['location']);
            $arrContent=$arrPopRes['data']['content'];
            $arrOut['title']=isset($arrContent['title'])?$arrContent['title']:'';
            $arrOut['windowTitle']=isset($arrContent['windowTitle'])?$arrContent['windowTitle']:'';
            $arrOut['content']=isset($arrContent['content'])?$arrContent['content']:'';
            $arrOut['link']=isset($arrContent['link'])?$arrContent['link']:'';
            $arrOut['imgUrl']=isset($arrContent['imgUrl'])?$arrContent['imgUrl']:'';
            $arrRet['platformPopWindow'] = $arrOut;
            $arrStyle = self::getTypeInfo($arrPopRes['data']['type'],$arrPopRes['data']['sub_type']);
            Tieba_Stlog::addNode('idcode', 73);
            Tieba_Stlog::addNode('task_name', $arrPopRes['data']['task_name']);
            Tieba_Stlog::addNode('ad_id', $arrPopRes['data']['id']);
            Tieba_Stlog::addNode('type', $arrStyle);
        }
        $this->setData($arrRet);
        $this->_ready();
        return true;
    }
    
    public function getCacheKey($arrDependData, $arrContentData) {
        return false;
    }
    public static function getTypeInfo($type, $subtype) {
        $style = '';
        if(0 == $type ) {
            if(0 == $subtype) {
                $style = '���ֵ���_��ʽһ';
            }else {
                $style = '���ֵ���_��ʽ��';
            }
        }
        if( 1 == $type ) {
            $style = 'ͼƬ����';
        }
        return $style;
    }
}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
