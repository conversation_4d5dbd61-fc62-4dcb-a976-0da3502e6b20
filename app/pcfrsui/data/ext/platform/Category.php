<?php

class Ext_Platform_Category extends Util_DataProvider {
    const BUSINESS_ATTR_NAME    = 'official';
    const OFFICIAL_TYPE_DIGITAL = 5;
    const OFFICIAL_TYPE_BEAUTY  = 6;
    const OFFICIAL_TYPE_PET     = 7;

    const VERTICAL_TYPE_DIGITAL = 0;
    const VERTICAL_TYPE_BEAUTY  = 1;
    const VERTICAL_TYPE_PET     = 2;

    protected  static $arrOfficialType = array(
        self::OFFICIAL_TYPE_DIGITAL,
        self::OFFICIAL_TYPE_BEAUTY,
        self::OFFICIAL_TYPE_PET,
    );
    protected static $intVerticalType = -1;

    /**
     * @brief 
     *
     * @param $arrContextData
     *
     * @return 
     */
    public function init($arrContextData){
        $this->_addDep('Core_Forum');
        //$this->_addDep('Core_User');
        //$this->_addDep('Core_BaseThreadList');
    }

    /**
     * @brief 
     *
     * @param $arrDependData
     * @param $arrContextData
     *
     * @return 
     */
    public function process($arrDependData, $arrContextData) {
        $arrForum = $arrDependData['Core_Forum'];
        $arrMultiInput = array();
        $arrStyle = $arrForum['attrs'];
        $intFid = (int)$arrForum['forum_info']['forum_id'];

        if (isset($arrStyle[self::BUSINESS_ATTR_NAME])) {
            $intOfficialType = intval($arrStyle[self::BUSINESS_ATTR_NAME]['official_type']);
            if (!in_array($intOfficialType, self::$arrOfficialType)) {
                $this->_ready();
                return true;
            }

            switch($intOfficialType) {
                case self::OFFICIAL_TYPE_DIGITAL:
                {
                    self::$intVerticalType = self::VERTICAL_TYPE_DIGITAL;
                    break;
                }
                case self::OFFICIAL_TYPE_BEAUTY:
                {
                    self::$intVerticalType = self::VERTICAL_TYPE_BEAUTY;
                    break;
                }
                case self::OFFICIAL_TYPE_PET:
                {
                    self::$intVerticalType = self::VERTICAL_TYPE_PET;
                    break;
                }
                default:
                {
                    $this->_ready();
                    return true;
                }
            }

            $arrInput = array(
                'forum_id'      => $intFid,
                'vertical_type' => self::$intVerticalType,
            );
            $module = 'vertical';
            $arrMultiInput[] = array(
                'service'  => $module,
                'method'        => 'getVerticalFrsAdByFid',
                'input'         => $arrInput,
            );
            $this->_registerService($arrMultiInput, 'Callback');
        } else {
            $this->_ready();
            return true;
        } 
    }

    /**
     * @brief 
     *
     * @param $arrDependData
     * @param $arrContextData
     *
     * @return 
     */
    public function getCacheKey($arrDependData, $arrContextData) {
        return false;
    }

    /**
     * @brief 
     *
     * @param $arrMultiOut
     *
     * @return 
     */
    public function Callback($arrMultiOut) {
        $arrOutData = $arrMultiOut[0];
        if (false === $arrOutData || Tieba_Errcode::ERR_SUCCESS !== $arrOutData['errno']) {
            Bingo_Log::warning("call vertical getVerticalFrsAdByFid fail, arrMultiOut:". serialize($arrMultiOut));
        } else {
            $arrAdvertData = $arrOutData['data'];
        }
        switch(self::$intVerticalType) {
            case self::VERTICAL_TYPE_DIGITAL:
            {
                $arrTplVar['digital_ad'] = $arrAdvertData;
                break;
            }
            case self::VERTICAL_TYPE_BEAUTY:
            {
                $arrTplVar['beauty_ad'] = $arrAdvertData;
                break;
            }
            case self::VERTICAL_TYPE_PET:
            {
                $arrTplVar['pet_ad'] = $arrAdvertData;
                break;
            }
            default:
            {
                break;
            }
        }
        $this->setData($arrTplVar);
        $this->_ready();
        return true;
    }

}
