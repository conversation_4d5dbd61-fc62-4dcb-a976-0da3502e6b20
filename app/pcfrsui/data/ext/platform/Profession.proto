package pcfrsui.Ext.Platform.Profession; 
import 'core/Common.proto'; 
import 'core/Forum.proto'; 
import 'core/Perm.proto'; 
import 'core/User.proto'; 
 
message professionTpl
{
    optional uint64 professional_manager_apply_id=1;
    optional uint64 is_inviting_as_professional_manager=2;
    optional string fail_reason=3;
    optional uint64 is_profession_forum=4;
    optional uint64 manager_apply_id=5;
    optional uint64 is_inviting_as_manager=6;
    optional uint64 is_audit_as_manager_success=7;
} 
message Res
{
    optional professionTpl profession=1;
}
