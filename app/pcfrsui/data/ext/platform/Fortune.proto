package pcfrsui.Ext.Platform.Fortune; 
import 'core/Common.proto'; 
import 'core/Forum.proto'; 
import 'core/Perm.proto'; 
import 'core/User.proto'; 
 
message professionTpl
{
    optional uint64 is_fourth_manage=1;
    optional uint64 user_point=2;
    optional uint64 today_have_add_post=3;
    optional uint64 data_fortune_bag=4;
    optional uint64 has_fortune_bag=5;
} 
message Res
{
    optional professionTpl profession=1;
}
