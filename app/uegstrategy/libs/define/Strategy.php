<?php

class Libs_Define_Strategy
{
    /**
     * 操作状态码
     */
    const UNAUDIT                  = 0;
    const PASSED                   = 1;
    const DELETED                  = 2;
    const BANED                    = 4;
    const BANED_DELETED            = 6;
    const BANEDMASTER              = 8;
    const BANED_DELETED_DEPRECATED = 1002;
    const THREAD_ALBUM_BANED       = 16;
    const USER_ALBUM_BANED         = 32;
    const MAN_DELETED              = 64;
    const ALBUM_BANED              = 48;
    const USER_MASK                = 128;
    const USER_BAN_AND_MASK        = 256;
    const VCODE                    = 512;
    const FOREVER                  = 10000;
    const YEAR                     = 365;
    const HUNDRED                  = 100;
    const WEEK                     = 7;

    const RECALL_RANGE_TIME    = 0;
    const RECALL_RANGE_POST_ID = 1;

    /**
     * 设置策略的观察区间
     * 时间 秒
     * 贴子数 贴
     * @var array
     */
    public static $_recallRangeFeature = array(
        self::RECALL_RANGE_TIME    => 'antiCommitTime',
        self::RECALL_RANGE_POST_ID => 'postId',
    );

    /**
     * 操作状态描述
     */
    public static $_arrOp_status = array(
        "all"                    => "全部",
        self::UNAUDIT            => "未审核",
        self::PASSED             => "审核通过",
        self::DELETED            => "已删除",
        self::MAN_DELETED        => "已人工删除",
        self::BANED              => "已封禁",
        self::BANED_DELETED      => "既删除又封禁",
        self::USER_MASK          => "用户屏蔽",
        self::THREAD_ALBUM_BANED => "主题相册已封禁",
        self::USER_ALBUM_BANED   => "用户相册已封禁",
        self::ALBUM_BANED        => "主题和用户相册已封禁",
    );

    /**
     * 主题 回复
     */
    public static $_arrTopic = array(
        "all" => "全部",
        "1"   => "只看主题",
        "0"   => "只看回复",
    );

    public static $_featureFunction = array(
        'self'   => '本身属性',
        'count'  => '特征出现次数',
        'length' => '特征字符长度',
    );

    public static $_featureCompare = array(
        'larger'         => '>',
        'less'           => '<',
        'equal'          => '=',
        'not-equal'      => '!=',
        'large-or-equal' => '>=',
        'less-or-equal'  => '<=',
    );

    public static $_featureOperation = array(
        '0' => '+', '1' => '-',
    );

    public static $_featureMatch = array(
        'exist'       => '精确匹配-存在',
        'notexist'    => '精确匹配-不存在',
        'contain'     => '模糊匹配-包含',
        'pre-contain' => '模糊匹配-前缀',
        'suf-contain' => '模糊匹配-后缀',
    );

    /**
     * 客户端不再选择 默认 black
     * @var array
     */
    public static $_computeMatchType = array(
        'white' => '命中全部匹配',
        'black' => '命中一个匹配',
    );

    /**
     * 客户端不再选择 默认 black
     * @var array
     */
    public static $_wordListMatchType = array(
        'white' => '命中全部匹配',
        'black' => '命中一个匹配',
    );

    /**
     * 删帖
     * @var array
     */
    public static $_arrThreadDeleteType = array(
        0 => "不删帖",
        1 => "只删帖",
        2 => "如果是主题删整楼",
        3 => "无论如何删整楼（废弃）",
        4 => '无论如何删整楼',
    );

    /**
     * 处理开关
     * @var array
     */
    public static $_arrDisposalSwitch = array(
        0 => "关闭",
        1 => "开启",
    );

    /**
     * 处理器 暂时不用配置管理
     * @var array
     */
    public static $_filterDealer = array(
        '240' => 'defaultDealer',
        '0'   => 'forumCommitDealer',
        '1'   => 'postworkCommitDealer',
        '2'   => 'postworkDelThreadDealer',
        '3'   => 'postworkDelPostDealer',
        '4'   => 'SignTableDealer',
        '5'   => 'UpdateStatusDealer',
        '6'   => 'LikeTableDealer',
        '7'   => 'ZanTableDealer',
        '8'   => 'BadtieDealer',
        '9'   => 'DeleteTableDealer',
        '10'  => 'vcodeDealer',
        '11'  => 'forumReplaceDealer',
        '12'  => 'abstractHideDealer',
        '13'  => 'forumInsertDealer',
        '14'  => 'UpdateThreadStatusDealer',
        '15'  => 'CopyrightMonitorDealer',
        '16'  => 'userreviewDealer',
        '17'  => 'SignatureTableDealer',
        '18'  => 'SignatureTableUpdateDealer',
        '19'  => 'coreuserDealer',
        '20'  => 'userPhotoDealer',
        '21'  => 'replaceCommitDealer',
        //'22' => 'SignClusterDealer',
        //'23' => 'ImgClusterDealer',
        '24'  => 'imagePostDealer',
        '25'  => 'imageSignatureDealer',
        '26'  => 'imageHeadphotoDealer',
        '27'  => 'userDealer',
        '28'  => 'imageClusterDealer',
        '29'  => 'AudioreviewDealer',
        //'30' => 'yunMonitorDealer',
        '31'  => 'forumAntiserviceDealer',
        '32'  => 'antiserviceJggVcodeDealer',
        '33'  => 'antiserviceSsVcodeDealer',
        '34'  => 'antiserviceRejectDealer',
        '35'  => 'antiserviceBlockMaskDealer',
        '36'  => 'ChatDealer',
        '37'  => 'PaperDealer',
        '38'  => 'pushHermesDealer',
        '100' => 'commonPostDealer',
        '101' => 'commonUserDealer',
        '102' => 'commonUserPostDealer',
        '103' => 'commonAppDealer',
        //'104' => 'lbsPostCommitDealer',
        //'105' => 'lbsMonitorDealer',
        '106' => 'searchMaskDealer',
        '107' => 'urlAuditDealer',
        '108' => 'clientHeadPostDealer',
        '109' => 'imagechatDealer',
        //'110' => 'kuangPostDealer',
        //'111' => 'kuangMonitorDealer',
        //'112' => 'tbmallVcodeNormalDealer',
        //'113' => 'tbmallSugBindDealer',
        //'114' => 'tbmallMustBindDealer',
        //'115' => 'tbmallCheckPhoneDealer',
        //'116' => 'tbmallPayPwdDealer',
        //'117' => 'tbmallRefusePaymentDealer',
        //'118' => 'zhidaMonitorDealer',
        //'119' => 'lvyouMonitorDealer',
        '120' => 'livegroupDeleteDealer',
        '121' => 'livegroupSuspectDealer',
        '122' => 'imgPostOneDealer',
        '123' => 'reportmisMonitorDealer',
        '124' => 'pushMultiauditDealer',
        //'125' => 'novelMonitorDealer',
        '126' => 'testMonitorDealer',
        '127' => 'pushHappyNewYearDealer',
        //'128' => 'aladaeMonitorDealer',
        //'129' => 'mobrowserMonitorDealer',
        //'130' => 'postrecoverDoDealer',
        //'131' => 'postrecoverNotDealer',
        //'132' => 'postrecover_submitPostDealer',
        //'133' => 'tbmallBlockPaymentDealer',
        '134' => 'pushIdHackSysMsgPostDealer',
        //'135' => 'walletMonitorDealer',
        //'136' => 'mobrowserHfDealer',
        '137' => 'pushMultiImgauditDealer',
        //'138' => 'tbmallSetPassVipDealer',
        '139' => 'pcMessageDeleteDealer',
        '140' => 'pcMessagePassDealer',
        '141' => 'pcMessageRejectDealer',
        '142' => 'setBdussNeedVcodeDealer',
        '143' => 'RecoverDealer',
        //'144' => 'nuomiPostDealer',
        //'145' => 'nuomiMonitorDealer',
        //'146' => 'zhida_imPostDealer',
        //'147' => 'zhida_imMonitorDealer',
        '148' => 'newdevPostDealer',
        '149' => 'newdevMonitorDealer',
        '150' => 'deleteHeadphotoDealer',
        //'151' => 'zhongtudaoPostDealer',
        //'152' => 'zhongtudaoMonitorDealer',
        '153' => 'commonThreadDealer',
        '154' => 'bzMailDealer',
        //'155' => 'lukuangPostDealer',
        //'156' => 'lukuangMonitorDealer',
        '157' => 'accessVerifyPhoneDealer',
        '158' => 'accessVerifyMailDealer',
        '159' => 'accessBindPhoneDealer',
        '160' => 'tbHighDealer',
        '161' => 'hotTopicRubbishDealer',
        '162' => 'followDealer',
        '163' => 'addPostDealer',
        '164' => 'alaCloseMonitorDealer',
        '170' => 'headPushToPassDealer',
        '171' => 'alaPushPornMonitorDealer',
        '172' => 'alaPushHangMonitorDealer',
        '173' => 'TuYaDeleteDealer',
        '175' => 'WeFanUgcDealer',
        '176' => 'alaPushInterLiveMonitorDealer',
        '177' => 'videoFirstDealer',
        '178' => 'virusCheckDealer',
        '179' => 'blackSourceDealer',
        '180' => 'pushHermesMoreDealer',
        '181' => 'unblockMonitorDealer',
        '182' => 'dingtieDealer',
        '183' => 'dingtieDeleteDealer',
        '184' => 'antiserviceZhuketaiDealer',
        '185' => 'pushFeedDealer',
        '186' => 'realTimeUserHandleDealer',
        '187' => 'antiservice2ZhuketaiDealer',
        '188' => 'imageDhashDealer',
        '189' => 'videoImagesMonitorDealer',
        '190' => 'pushCrowdTestDealer',
        '191' => 'markNotPushSearchDealer',
        '192' => 'goodReplyMonitorDealer',
        '193' => 'denyZhuketaiDealer',
        '194' => 'auditZhuketaiDealer',
        '195' => 'goodReplyToAuditDealer',
        '196' => 'markNotPushRankDealer',
        '197' => 'clientimBlockDealer',
        '198' => 'postFoldDealer',
        '199' => 'specUserHandleDealer',
        '200' => 'pushEmotionCrowdTestDealer',
        '201' => 'setFrsFoldDealer',
        '202' => 'pushCrowdThreadDealer',
        '203' => 'userBriefDealer',
        '204' => 'pushUegAuditDealer',
        '205' => 'setNotTopStickDealer',
        '206' => 'afterZhuketaiDealer',
        '207' => 'dealPushUegAuditDealer',
        '208' => 'notDealPushUegAuditDealer',
        '209' => 'addUserAuditBlackListDealer',
        '210' => 'addUserAuditTmpBlackListDealer',
        '211' => 'addUserAuditWhiteListDealer',
        '212' => 'addUserAuditTmpWhiteListDealer',
        '213' => 'dealApealNothingDealer',
        '214' => 'pushUegAuditThreadDealer',
        '215' => 'deleteTailDealer',
        '216' => 'pushShumeiDealer',
        '217' => 'ChatToUegauditDealer',
        '218' => 'geetestVcodeDealer',
        '219' => 'passIdentityVerificationDealer',
        '220' => 'passSmsVerificationDealer',
        '221' => 'passChangePasswordDealer',
        '222' => 'passFaceVerificationDealer',
        '223' => 'indexPostFoldDealer',
        '224' => 'pushNewUegAuditDealer',
        '225' => 'blackuidyixinGeetestVcodeDealer',
        '226' => 'pushPicNewUegAuditDealer',
        '227' => 'resetNicknameDealer',
        '228' => 'backtrackDealer',
        '229' => 'afterZhuketaiNotAuditDealer',
        '230' => 'blackPhoneDealer',
        '231' => 'afterNewZhuketaiDealer',
        '232' => 'ChatToNewUegauditDealer',
        '233' => 'dingcaiPostFoldDealer',
        '234' => 'actsctrlGlobalDealer',
        '235' => 'delAgreeDealer',
        /**
         * @TODO 下面 zhidao_ 开头的是知道业务使用的dealer
         */
        //无风险dealer，对应通过处置
        '236' => 'zhidao_Dealer',
        //高风险dealer，对应删除处置
        '237' => 'zhidao_highrisDealer',
        //中风险dealer，对应先审后发处置
        '238' => 'zhidao_mediumriskDealer',
        //低风险dealer，对应先发后审处置
        '239' => 'zhidao_lowriskDealer',
        /**
         * @TODO 占位，注释不要打开，这个dealer在最上面
         * '240' => 'defaultDealer',
         */
        '241' => 'infoflow_Dealer',
        '242' => 'smartprogramPushDealer', // 智能小程序回调结果
        '243' => 'resetBdAppNickDealer', // 重置手百用户昵称
        '244' => 'resetBdAppSignDealer', // 重置手百用户签名

        //以下为flow业务使用的dealer
        '245' => 'flowDeleteDealer',  //未过审回调flow删除处置
        '246' => 'flowPushAuditDealer', //过审推送审核平台

        //'247' => 'smartprogramPushTest1Dealer', // 智能小程序test_dealer1
        //'248' => 'smartprogramPushTest2Dealer', // 智能小程序test_dealer2
        //'249' => 'smartprogramPushTest3Dealer', // 智能小程序test_dealer3

        '250' => 'infoflowImReject_Dealer', // 如流 im 消息拒绝
        '251' => 'infoflowImGamble_Dealer', // 如流 im 消息赌博提示
        '252' => 'infoflowImHeresy_Dealer', // 如流 im 消息邪教提示
        '253' => 'infoflowImSex_Dealer',    // 如流 im 消息色情提示
        '254' => 'infoflowImDefraud_Dealer',// 如流 im 消息诈骗/刷单提示

        '255' => 'forumvote_Dealer', //吧主投票反作弊处理

        '256' => 'infoflowImage_Dealer', // 如流图片召回

        '257' => 'groupinfoPass_Dealer',  //手百im群资料处理
        '258' => 'superauditNickName_Dealer', //超审昵称结果返回
        '259' => 'groupinfoReject_Dealer', //手百im群资料命中敏感词dealer

        '260' => 'positive_advertising_Dealer', //正向广告
        '261' => 'high_risk_account_Dealer', //高危账号
        '262' => 'passPortDelNameDealer', // pass name del
        '263' => 'passPortDelImgDealer', // pass img del
        '264' => 'passPortAuditNameDealer', // pass name 推审
        '265' => 'passPortAuditImgDealer', // pass img 推审

        '266' => 'flowUserInfoPass_Dealer', // flow用户信息通过
        '267' => 'flowUserInfoReject_Dealer', // flow用户信息命中敏感词dealer
        '268' => 'flowCommentPass_Dealer', // flow评论通过dealer
        '269' => 'flowCommentReject_Dealer', // flow评论未过审dealer
        '270' => 'flowUserIconPass_Dealer', // flow用户头像通过dealer
        '271' => 'flowUserIconReject_Dealer', // flow用户头像未过审dealer

        '272' => 'haokanPass_Dealer', //好看社区化内容过审dealer

        '273' => 'cloudcdnImageCallbackDealer', //百度云加速图片回调dealer
        '274' => 'cloudcdnTextCallbackDealer', //百度云加速文本回调dealer
        '275' => 'cloudcdnImageErrorCallbackDealer', //百度云加速过检图片错误回调dealer
        '276' => 'novelContentCallbackDealer', // 小说文本回调dealer

        '277' => 'immsgReject_Dealer', //手百私信实时拒绝dealer
        '278' => 'immsgIntercept_Dealer', //手百私信主态拦截dealer

        '279' => 'cloudImageCallbackDealer', //百度云加速图片回调dealer
        '280' => 'cloudImageErrorCallbackDealer', //百度云过检图片错误回调dealer
        '281' => 'novelImageCallbackDealer', // 小说图片回调dealer

        '282' => 'passPortNameSeqingDealer',
        '283' => 'passPortNameWeifaDealer',
        '284' => 'passPortNameShezhengDealer',
        '285' => 'passPortNameOtherDealer',
        '286' => 'passPortNamePassDealer',
        '287' => 'passPortImgSeqingDealer',
        '288' => 'passPortImgWeifaDealer',
        '289' => 'passPortImgShezhengDealer',
        '290' => 'passPortImgOtherDealer',
        '291' => 'passPortImgPassDealer',
        '292' => 'novelTextShieldCallbackDealer',
        '293' => 'novelTextSensitiveCallbackDealer',
        '294' => 'novelTextSuccessCallbackDealer',

        '295' => 'bokeAudioSuccCallbackDealer',  //播客音频过审成功回调dealer
        '296' => 'bokeAudioErrorCallbackDealer',  //播客音频过审失败回调dealer
    );

}