<?php

class getConvertLogByIdAction extends Util_UegStrategyBaseAction
{
    /**
     * @return array
     */
    public function _getPrivateInfo()
    {
        // 访客状态
        $arrPrivateInfo['need_login']  = true;
        $arrPrivateInfo['check_login'] = true;
        $arrPrivateInfo['check_tbs']   = true;
        $arrPrivateInfo['check_perm']  = false;

        $arrPrivateInfo['ids'] = $this->_getInput('id', 0);

        return $arrPrivateInfo;
    }

    /**
     * @return bool
     */
    public function _checkPrivate()
    {
        $ids = $this->_objRequest->getPrivateAttr('ids');
        if (empty($ids)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " param error";
            Bingo_Log::warning($strLog);
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), "版本号不能为空");
            return false;
        }
        return true;
    }

    /**
     * @return bool
     */
    public function _execute()
    {
        $Ids             = $this->_objRequest->getPrivateAttr('ids');
        $arrServiceInput = array(
            'ids' => array($Ids),
        );
        $arrOutput = DL_UegStrategy_Convert::getConvertFileById($arrServiceInput);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call DL_UegStrategy_Convert::getConvertFileById fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrRet = array(
            'data' => $arrOutput['data'][0],
        );
        return $this->errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }
}