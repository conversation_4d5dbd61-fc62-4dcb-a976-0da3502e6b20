<?php

class getGlobalInfoAction extends Util_UegStrategyBaseAction
{

    /**
     * @return array
     */
    public function _getPrivateInfo()
    {
        // 访客状态
        $arrPrivateInfo['need_login']    = true;
        $arrPrivateInfo['check_login']   = true;
        $arrPrivateInfo['check_tbs']     = true;
        $arrPrivateInfo['check_perm']    = false;
        $arrPrivateInfo['global_id'] = intval($this->_getInput('global_id', ''));
        // $arrPrivateInfo['status']                = intval($this->_getInput('status', Libs_Define_Uegstrategy::STATUS_NORMAL));

        $arrPrivateInfo['group_id'] = intval($this->_getInput('group_id', ''));

        //$this->_arrPageNeedPerm  = array(Libs_Define_Permission::PERM_SERVER_NUM_FIELD_EDIT);
        return $arrPrivateInfo;
    }

    /**
     * @return bool
     */
    public function _checkPrivate()
    {
        $groupId = $this->_objRequest->getPrivateAttr('global_id');
        if (empty($groupId)) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " param error service name: {$groupId}";
            Bingo_Log::warning($strLog);
            $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), "全局ID不能为空");
            return false;
        }
        return true;
    }

    /**
     * @return bool
     */
    public function _execute()
    {
        $groupId = $this->_objRequest->getPrivateAttr('global_id');

        $arrServiceInput  = array(
            'group_id' => $groupId,
        );
        $strServiceName   = "uegstrategy";
        $strServiceMethod = "getGlobalInfoById";

        $arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, 'post', null, 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrServiceInput) . "]; output:[" . serialize($arrOutput) . "]";
            Bingo_Log::warning($strLog);
            return $this->errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrOutput['data'] = self::_formatRetData($arrOutput['data']);
        return $this->errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
    }

    /**
     * @param $arrData
     * @return mixed
     */
    private static function _formatRetData($arrData)
    {
        foreach ($arrData as &$item) {
            $item['create_time'] = date("Y-m-d H:i:s", $item['create_time']);
            $item['online_time'] = empty($item['online_time'])?'':date("Y-m-d H:i:s", $item['online_time']);
            $item['op_time']     = date("Y-m-d H:i:s", $item['op_time']);
            $item['global_json']     = empty($item['global_json'])?[]:json_decode($item['global_json'],true);
        }
        return $arrData;
    }
}