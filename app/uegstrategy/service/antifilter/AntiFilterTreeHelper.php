<?php

/**
 * 策略树的XML生成辅助类
 * <AUTHOR>
 *
 */
class Service_AntiFilter_AntiFilterTreeHelper {

    /**
     * 常量，策略树的节点类型
     */
    const JUDGE_NODE_TYPE            = 1; // 逻辑判断节点 计数
    const PARDON_NODE_TYPE           = 2; // 豁免节点
    const RECALL_NODE_TYPE           = 3; // 召回
    const CONDITION_RECALL_NODE_TYPE = 4; // 条件召回节点
    const VIRTUAL_NODE_TYPE          = 5; // 虚拟
    const EMPTY_NODE_TYPE            = 6; // 空节点
    const ACTSCTRL_RECALL_NODE_TYPE  = 7; // 频次控制召回节点
    const CONTENT_JUDGMENT_NODE_TYPE = 8; // 内容判断节点 词表

	/**
	 * 单例
	 */
	private static $instance;


	/**
	 * XML头，没有直接使用XML的DOM工具，而是用sprintf的方法输出MXL
	 */

	protected static $_antiserverXMLNode = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n<antiserver>\n%s</antiserver>\n";
	protected static $_filterXMLNode = "<filter monitorType=\"%s\" priority=\"%s\" dealer=\"%s\">\n%s</filter>\n";
	protected static $_groupXMLNode = "<group dealpool=\"%s\" name=\"%s\">\n%s</group>\n";
	protected static $_globalXMLNode = "<global>\n%s</global>\n";

	// ====================单例部分=========================
	public static function singleton() {
		if (! isset ( self::$instance )) {
			$className = __CLASS__;
			self::$instance = new $className ();
		}
		return self::$instance;
	}

	public function increment() {
		return $this->count ++;
	}

	public function __clone() {
		trigger_error ( 'Clone is not allowed.', E_USER_ERROR );
	}

	public function __wakeup() {
		trigger_error ( 'Unserializing is not allowed.', E_USER_ERROR );
	}

	public function __construct() {

	}

	// ====================普通监控区的filter XML生成工具=========================
	/**
	 * 处理判断节点，可能是特征值判断，也可能是词表判断
	 *
	 * @param $judgeNode array
	 * @return boolean xml
	 */
	protected function _dealJudgeNode($judgeNode) {

		$nodeJson = $judgeNode ['data'] ['nodeJson'];
		if (! isset ( $nodeJson )) {
			Bingo_Log::debug ( "_dealJudgeNode: nodeJson: " . print_r ( $nodeJson, true ) );
			return false;
		}

		$nodeJsonType = $nodeJson ['nodeType'];

		$judgeFilterEntity = Service_AntiFilter_AntiFilterEntityFactory::simpleJudgeEntityFactory ( $nodeJsonType );

		if ($judgeFilterEntity === false) {
			return false;
		}

		$children = $judgeNode ['children'];

		if (! isset ( $children )) {
			return false;
		}

		$yesChild = $children [0];
		$noChild = $children [1];

		$yesChildNodeXML = self::_genFilterXMLElementFromJsonNode ( $yesChild );
		$noChildNodeXML = self::_genFilterXMLElementFromJsonNode ( $noChild );


		if ($yesChildNodeXML === false || $noChildNodeXML === false) {
			return false;
		}

		if ($yesChildNodeXML) {
			$judgeFilterEntity->addIfFragment ( $yesChildNodeXML );
		}

		if ($noChildNodeXML) {
			$judgeFilterEntity->addElseFragment ( $noChildNodeXML );
		}

		return $judgeFilterEntity->xmlGenerator ( $nodeJson );
	}

	/**
	 * 处理条件召回节点
	 *
	 * @param $conRecallNode array
	 * @return boolean xml
	 */
	protected function _dealConditionRecallNode($conRecallNode) {
		$nodeJson = $conRecallNode ['data'] ['nodeJson'];
		if (! isset ( $nodeJson )) {
			Bingo_Log::debug ( "_dealConditionRecallNode: nodeJson: " . print_r ( $nodeJson, true ) );
			return false;
		}

		$nodeJsonType = $nodeJson ['nodeType'];

		if ($nodeJsonType == Service_AntiFilter_AntiFilterEntity::ENTITY_TYPE_COUNTER) {
			$recallFilterEntity = Service_AntiFilter_AntiFilterEntityFactory::factory ( 'Counter' );
		} else if ($nodeJsonType == Service_AntiFilter_AntiFilterEntity::ENTITY_TYPE_STATIS) {
			$recallFilterEntity = Service_AntiFilter_AntiFilterEntityFactory::factory ( 'Statis' );
		} else {
			return false;
		}

		return $recallFilterEntity->xmlGenerator ( $nodeJson );
	}

	/**
	 * 频次控制召回节点
	 *
	 * @param $conRecallNode array
	 * @return boolean xml
	 */
	protected function _dealActsctrlRecallNode($conRecallNode) {
	    $nodeJson = $conRecallNode ['data'] ['nodeJson'];
	    if (! isset ( $nodeJson )) {
	        Bingo_Log::debug ( "_dealActsctrlRecallNode: nodeJson: " . print_r ( $nodeJson, true ) );
	        return false;
	    }

	    $nodeJsonType = $nodeJson ['nodeType'];

	    $recallFilterEntity = false;

	    if ($nodeJsonType ==Service_AntiFilter_AntiFilterEntity::ENTITY_TYPE_ACTSCTRL) {
	        $recallFilterEntity = Service_AntiFilter_AntiFilterEntityFactory::factory ( 'Actsctrl' );
	    } else {
	        return false;
	    }

	    return $recallFilterEntity->xmlGenerator ( $nodeJson );
	}

    /**
     * 处理直接召回节点
     * @param $recallNode
     * @return mixed
     */
	protected function _dealRecallNode($recallNode) {
		$recallFilterEntity = Service_AntiFilter_AntiFilterEntityFactory::factory ( 'Recall' );
		return $recallFilterEntity->xmlGenerator ();
	}

    /**
     * 处理豁免节点，因为豁免不需要XML内容，所以返回空字符
     * @return string
     */
	protected function _dealPardonNode() {
		return '';
	}

	/**
	 * 处理虚拟节点，将几个children顺序连接起来即可
	 *
	 * @param $virtualNode array
	 * @return boolean xml
	 */
	protected function _dealVirtualNode($virtualNode) {
		$children = $virtualNode ['children'];

		if (! isset ( $children )) {
			return false;
		}

		$virtaulNodeXML = '';

		foreach ( $children as $childNode ) {

			$childNodeXML = self::_genFilterXMLElementFromJsonNode ( $childNode );

			if ($childNodeXML === false) {
				return false;
			}
			$virtaulNodeXML .= $childNodeXML;
			if ($childNode ['data'] ['type'] == self::PARDON_NODE_TYPE || $childNode ['data'] ['type'] == self::RECALL_NODE_TYPE) {
				break;
			}
		}

		return $virtaulNodeXML;
	}

	/**
	 * 根据策略树的json描述，生成XML配置文件
	 *
	 * @param $treeNode array
	 * @return boolean xml
	 */
	protected function _genFilterXMLElementFromJsonNode($treeNode) {

		if ($treeNode === false) {
			return false;
		}
		$treeNodeXMLElements = false;
		$treeNodeType = $treeNode ['data'] ['type'];
		switch ($treeNodeType) {
			case self::JUDGE_NODE_TYPE :
				$treeNodeXMLElements = self::_dealJudgeNode ( $treeNode );
				break;
			case self::CONDITION_RECALL_NODE_TYPE :
				$treeNodeXMLElements = self::_dealConditionRecallNode ( $treeNode );
				break;
			case self::PARDON_NODE_TYPE :
				$treeNodeXMLElements = self::_dealPardonNode ( $treeNode );
				break;
			case self::RECALL_NODE_TYPE :
				$treeNodeXMLElements = self::_dealRecallNode ( $treeNode );
				break;
			case self::VIRTUAL_NODE_TYPE :
				$treeNodeXMLElements = self::_dealVirtualNode ( $treeNode );
				break;
			case self::EMPTY_NODE_TYPE :
				break;
			case self::ACTSCTRL_RECALL_NODE_TYPE:
			    $treeNodeXMLElements = self::_dealActsctrlRecallNode ( $treeNode );
			    break;
            case self::CONTENT_JUDGMENT_NODE_TYPE :
                $treeNodeXMLElements = self::_dealJudgeNode ( $treeNode );
                break;
			default :
				break;
		}
		return $treeNodeXMLElements;
	}

    /**
     * 生成一个filter的XML
     *
     * @param $monitorType int
     * @param $globalVid int
     * @param bool $filterDealer string|false
     * @return bool|string
     */
	public function genFilterXML($monitorType, $globalVid, $filterDealer = false) {
        $hasFilterArr = DL_UegStrategy_Monitor::hasFilter(array('monitor_type'=>$monitorType,'id'=>$globalVid));
        if (false === $hasFilterArr || Tieba_Errcode::ERR_SUCCESS != $hasFilterArr["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__;
            Bingo_Log::warning($strLog);
        }

        $filter = $hasFilterArr['data'][0];
		if ($filter === false) {
			Bingo_Log::debug ( "genFilterXML: filter === false" );
			return false;
		}

		$treeJson = mb_convert_encoding ( $filter['monitor_tree'], 'UTF-8', 'GBK' );

		if (! $treeJson) {
			Bingo_Log::debug ( "genFilterXML: treeJson is null: \n$treeJson" );
			return false;
		}

		$treeRootNode = json_decode ( $treeJson, true );

		if (! $treeRootNode) {
			Bingo_Log::debug ( "genFilterXML: json decode failed! " );
			return false;
		}
		Bingo_Log::debug ( "genFilterXML: begin inner gen!" );

		$filterInnerXML = self::_genFilterXMLElementFromJsonNode ( $treeRootNode );

		if (! $filterInnerXML) {
			Bingo_Log::debug ( "genFilterXML: filter $monitorType has no entities!" );
			return false;
		}

		if ($filterDealer === false) {
            $filterDealerList =Libs_Define_Strategy::$_filterDealer;
			$filterDealer = $filterDealerList [$filter['dealer']];
		}

		if ($monitorType < 0) {
			$filterXML = $filterInnerXML;
		} else {
			$filterXML = sprintf ( self::$_filterXMLNode, $filter['monitor_type'], $filter['priority'], $filterDealer, $filterInnerXML );
		}

		return mb_convert_encoding ( $filterXML, 'GBK', 'UTF-8' );
	}

	// ====================全局监控区的filter XML生成工具=========================
    /**
     * 全局豁免策略filter的XML，因为和其他监控区的不同，单独做一个方法
     * @param $jsonNode array
     * @return bool
     */
	protected function _dealGlobalJudgeNode($jsonNode) {
		if (! isset ( $jsonNode )) {
			Bingo_Log::debug ( "_dealGlobalJudgeNode: jsonNode: " . print_r ( $jsonNode, true ) );
			return false;
		}

		$nodeJsonType = $jsonNode ['nodeType'];

		$judgeFilterEntity = Service_AntiFilter_AntiFilterEntityFactory::simpleJudgeEntityFactory ( $nodeJsonType );

		if ($judgeFilterEntity === false) {
			return false;
		}

		return $judgeFilterEntity->xmlGenerator ( $jsonNode );
	}

	/**
	 * 生成全局豁免XML
	 *
	 * @param $jsonList array
	 * @return boolean string
	 */
	protected function _genGlobalXMLElementFromJsonList($jsonList) {
		if ($jsonList === false) {
			return false;
		}

		$entitiesXML = '';
		foreach ( $jsonList as $judgeJson ) {
			$entitiesXML .= self::_dealGlobalJudgeNode ( $judgeJson );
		}

		return $entitiesXML;
	}

    /**
     * 生成全局过滤器XML
     * @param $monitorType
     * @param $globalVid
     * @return bool|string
     */
	public function genGlobalFilterXML($monitorType, $globalVid) {

        $hasFilterArr = DL_UegStrategy_Monitor::hasFilter(array('monitor_type'=>$monitorType,'id'=>$globalVid));
        if (false === $hasFilterArr || Tieba_Errcode::ERR_SUCCESS != $hasFilterArr["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__;
            Bingo_Log::warning($strLog);
        }
        $filter = $hasFilterArr['data'][0];

		if ($filter === false) {
			Bingo_Log::debug ( "genGlobalFilterXML: filter === false" );
			return false;
		}

		$jsonListStr = mb_convert_encoding ( $filter['monitor_tree'], 'UTF-8', 'GBK' );

		if (! $jsonListStr) {
			Bingo_Log::debug ( "genGlobalFilterXML: treeJson is null: \n$jsonListStr" );
			return false;
		}

		$jsonList = json_decode ( $jsonListStr, true );

		if (! $jsonList) {
			Bingo_Log::debug ( "genGlobalFilterXML: json decode failed! " );
			return false;
		}
		Bingo_Log::debug ( "genGlobalFilterXML: begin inner gen!" );
		$filterInnerXML = self::_genGlobalXMLElementFromJsonList ( $jsonList );

		if (! $filterInnerXML) {
			Bingo_Log::debug ( "genGlobalFilterXML: filter $monitorType has no entities!" );
			return false;
		}

		$filterXML = $filterInnerXML;

		return mb_convert_encoding ( $filterXML, 'GBK', 'UTF-8' );
	}

	// ====================生成在线上的filters配置文件并保存=========================
    /**
     * 生成在线上的监控区filters配置文件
     * @param $machineGroupId
     * @return string
     */
	public function genOnlineFiltersConfile($machineGroupId) {
        $onlineFilterGroupsArr = DL_UegStrategy_Monitor::getFilterGroupsByMachineGroupId(array('machine_group_id'=>$machineGroupId));
        if (false === $onlineFilterGroupsArr || Tieba_Errcode::ERR_SUCCESS != $onlineFilterGroupsArr["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__;
            Bingo_Log::warning($strLog);
        }
        $onlineFilterGroups = $onlineFilterGroupsArr['data'];
		$groupXML = '';

		foreach ( $onlineFilterGroups as $group ) {
			$groupInnerXML = '';
			$globalFilterXML = '';
            $getGlobalFiltersByGroupIdArr = DL_UegStrategy_Monitor::getGlobalFiltersByGroupId(array('group_id'=>$group['groupId']));
            if (false === $getGlobalFiltersByGroupIdArr || Tieba_Errcode::ERR_SUCCESS != $getGlobalFiltersByGroupIdArr["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__;
                Bingo_Log::warning($strLog);
            }
            $globalFilter = $getGlobalFiltersByGroupIdArr['data'];

			if ($globalFilter) {
				$globalFilterInnerXML = self::genGlobalFilterXML ( $globalFilter['monitor_type'], $globalFilter['version_id'] );
				if ($globalFilterInnerXML && strlen ( $globalFilterInnerXML )) {
					$globalFilterXML = sprintf ( self::$_globalXMLNode, $globalFilterInnerXML );
				}
			}
			$groupInnerXML .= $globalFilterXML;

            $hasFilterArr = DL_UegStrategy_Monitor::hasFilter(array('group_id'=>$group['groupId'],'filter'=>true,'online_status'=>Libs_Define_Uegstrategy::STRATEGY_ONLINE));
            if (false === $onlineFilterGroupsArr || Tieba_Errcode::ERR_SUCCESS != $onlineFilterGroupsArr["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__;
                Bingo_Log::warning($strLog);
            }
            $onlineFilters = $hasFilterArr['data'];
			foreach ( $onlineFilters as $onlineFilter ) {

                $hasMonitorArr = DL_UegStrategy_Monitor::hasMonitor(array('monitor_type'=>$onlineFilter['monitor_type']));
                if (false === $onlineFilterGroupsArr || Tieba_Errcode::ERR_SUCCESS != $onlineFilterGroupsArr["errno"]) {
                    $strLog = __CLASS__ . "::" . __FUNCTION__;
                    Bingo_Log::warning($strLog);
                }
                $monitor = $hasMonitorArr['data'];

				if($monitor === false){
					Bingo_Log::warning("genOnlineFiltersConfile hasMonitor fail");
				}else{
					$cluster = intval($monitor['cluster']);
					if($cluster === 1){
						Bingo_Log::debug("new filter[{$onlineFilter['monitor_type']}");
						continue;
					}
				}//如果是新antiserver集群上的策略，不再将配置发送到老集群
				$filterXML = self::genFilterXML ( $onlineFilter['monitor_type'], $onlineFilter['id'] );
				if ($filterXML && strlen ( $filterXML )) {
					$groupInnerXML .= $filterXML;
				}
			}
			if ($groupInnerXML && strlen ( $groupInnerXML )) {
				$groupXML .= sprintf (  self::$_groupXMLNode, $group['dealpool'], $group['name'], $groupInnerXML );
			}
		}

		$antiserverXML = sprintf ( self::$_antiserverXMLNode, $groupXML );

		$dom = new DOMDocument ();
		$dom->preserveWhiteSpace = false;
		$dom->loadXML ( $antiserverXML );
		$dom->formatOutput = true;
		$antiserverXML = $dom->saveXml ();

		Bingo_Log::debug ( "genOnlineFiltersConfile: antiserverXML: \n$antiserverXML" );

		return $antiserverXML;
	}

    /**
     * 生成所有groupId都有的xml
     * @param $arrMachineGroupId
     * @return string
     */
	public function genAllXml($arrMachineGroupId){
		$strXml='';

		foreach($arrMachineGroupId as $intMachineGroupId){

			$strXml=$strXml.self::genGroupXmlPart($intMachineGroupId);

		}
		$antiserverXML = sprintf ( self::$_antiserverXMLNode, $strXml);
		$dom = new DOMDocument ();
		$dom->preserveWhiteSpace = false;

		$dom->loadXML ( $antiserverXML );

		$dom->formatOutput = true;

		$antiserverXML = $dom->saveXml ();
		Bingo_Log::debug ( "genOnlineFiltersConfile: antiserverXML: \n$antiserverXML" );

		return $antiserverXML;
	}

    /**
     * 生成group的xml，没有anserver这一块
     * @param $machineGroupId
     * @return string
     */
	public function genGroupXmlPart($machineGroupId){

        $onlineFilterGroupsArr = DL_UegStrategy_Monitor::getFilterGroupsByMachineGroupId(array('machine_group_id'=>$machineGroupId));
        if (false === $onlineFilterGroupsArr || Tieba_Errcode::ERR_SUCCESS != $onlineFilterGroupsArr["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__;
            Bingo_Log::warning($strLog);
        }

        $onlineFilterGroups = $onlineFilterGroupsArr['data'];
        $groupXML = '';

		foreach ( $onlineFilterGroups as $group ) {
			$groupInnerXML = '';
			$globalFilterXML = '';

            $getGlobalFiltersByGroupIdArr = DL_UegStrategy_Monitor::getGlobalFiltersByGroupId(array('group_id'=>$group['id']));
            if (false === $getGlobalFiltersByGroupIdArr || Tieba_Errcode::ERR_SUCCESS != $getGlobalFiltersByGroupIdArr["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__;
                Bingo_Log::warning($strLog);
            }
            $globalFilter = $getGlobalFiltersByGroupIdArr['data'];

			if ($globalFilter) {
				$globalFilterInnerXML = self::genGlobalFilterXML ( $globalFilter['monitor_type'], $globalFilter['id'] );
				if ($globalFilterInnerXML && strlen ( $globalFilterInnerXML )) {
					$globalFilterXML = sprintf ( self::$_globalXMLNode, $globalFilterInnerXML );
				}
			}
			$groupInnerXML .= $globalFilterXML;


            $hasFilterArr = DL_UegStrategy_Monitor::hasFilter(array('group_id'=>$group['id'],'filter'=>true,'online_status'=>Libs_Define_Uegstrategy::STRATEGY_ONLINE));
            if (false === $onlineFilterGroupsArr || Tieba_Errcode::ERR_SUCCESS != $onlineFilterGroupsArr["errno"]) {
                $strLog = __CLASS__ . "::" . __FUNCTION__;
                Bingo_Log::warning($strLog);
            }
            $onlineFilters = $hasFilterArr['data'];

			foreach ( $onlineFilters as $onlineFilter ) {

				$filterXML = self::genFilterXML ( $onlineFilter['monitor_type'], $onlineFilter['id'] );

				if ($filterXML && strlen ( $filterXML )) {
					$groupInnerXML .= $filterXML;
				}

			}

			if ($groupInnerXML && strlen ( $groupInnerXML )) {
				$groupXML .= sprintf ( self::$_groupXMLNode, $group['dealpool'], $group['name'], $groupInnerXML );
			}
		}

		return $groupXML;
	}
}
