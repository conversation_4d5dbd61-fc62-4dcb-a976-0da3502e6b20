<?php

define('ROOT_PATH', '/home/<USER>/orp');
ini_set('memory_limit', '1G');
ini_set('max_execution_time', '0');
Tieba_Init::init('uegstrategy');

define ('LOG', 'log');
Bingo_Log::init(array(
    LOG => array(
        'file'  => dirname(__FILE__).'/../../../../log/uegstrategy/script/AsyncNodeRate.log',
        'level' => 0xFF,
    ),
), LOG);

// 新增报警邮件，脚本新增参数
$statusInput = unserialize($argv[1]);
Bingo_Log::warning("haeh");
Bingo_Log::notice("haeh");
/**
 * @param $reason
 * @param $statusInfo
 */
function setStatus($reason, $statusInfo)
{
    // 增加开处置发邮件报警，获取当前是操作开处置还是关处置
    $monitorInfo = DL_UegStrategy_Monitor::getMonitorFilterInfoRate(array('monitor_type'=>$statusInfo['monitor_type']));

    if (isset($monitorInfo['if_dealUser']) && 1 == $monitorInfo['if_dealUser'] && isset($monitorInfo['name']) && isset($monitorInfo['priority'])) {
        // 获取操作人信息
        $op_time      = date('Y-m-d H:i:s', $statusInfo['op_time']);
        $encode       = mb_detect_encoding($statusInfo['op_uname'], array("ASCII", 'UTF-8', "GB2312", "GBK", 'BIG5'));
        $op_uname     = mb_convert_encoding($statusInfo['op_uname'], 'UTF-8', $encode);
        $monitor_name = $monitorInfo['monitor_name'];
        $title        = 'UEG策略组-' . $statusInfo['monitor_type'] . '(策略号) 开启用户封禁处置上线通知';
        $content      = <<<Eof
报警原因：{$monitorInfo['name']}集群策略开启用户封禁处置<br /><br />
策略号：{$statusInfo['monitor_type']}<br /><br />
处置：用户封禁<br /><br />
策略内容：{$monitor_name}<br /><br />
集群：{$monitorInfo['name']}<br /><br />
优先级：{$monitorInfo['priority']}<br /><br />
操作人： {$op_uname}<br /><br />
上线时间：{$op_time}<br /><br />
操作结果：{$reason}<br /><br />
请相关同学关注策略召回、误伤情况。若有问题，请及时对策略进行调整！<br /><br />
Eof;
        Service_Monitor_MonitorOnlineRecordService::actionPolicyMail($title, $content);
    }
}

/**
 * @param $strFileName
 * @param $strContent
 * @param $statusInput
 * @return bool
 */
function _writeFileOnDisk($strFileName, $strContent, $statusInput)
{
    $objFile = fopen($strFileName, 'w+');
    if (null == $objFile) {
        Bingo_Log::warning('create file not ok filename ' . $strFileName);
        setStatus('create file not ok filename ' . $strFileName, $statusInput);
        return false;
    }
    $intWriteLength = fwrite($objFile, $strContent);
    if (false == $intWriteLength) {
        Bingo_Log::warning('write file not ok filename ' . $strFileName);
        setStatus('write file not ok filename ' . $strFileName, $statusInput);
        return false;
    }
    $bolRet = fclose($objFile);
    if (false == $bolRet) {
        Bingo_Log::warning('close file not ok filename ' . $strFileName);
        setStatus('close file not ok filename ' . $strFileName, $statusInput);
        return false;
    }
    return true;
}

$strNodeRate = Service_Strategy_Monitor::createNodeRates();

if (empty ($strNodeRate)) {
    Bingo_Log::warning('execute createNodeRates fail');
    setStatus('execute createNodeRates fail', $statusInput);
    return false;
}
$fileDir = ROOT_PATH . '/data/app/antiserver/script/unihandledata/';
system('mkdir -p ' . $fileDir);
$nodeRateFilename     = 'FilterRateConfig.php';
$bolDeleteRuleFileRet = _writeFileOnDisk($fileDir . $nodeRateFilename, $strNodeRate, $statusInput);
if (false == $bolDeleteRuleFileRet) {
    Bingo_Log::warning('write delete rule file to disk fail');
    return false;
}
//$orpConf = array (
//	"serverUrl" => "http://orp.baidu.com",
//	"srcPath" => $fileDir.$nodeRateFilename,
//	"relativePath" => "data/unihandle",
//	"proName" => "tieba",
//	"appName" => "",
//	"token" => "woQxN9IBnlak2deaqaTpjVgjUYKzggcF",
//	"jobName" => "orp_anti_noderate",
//	"isRestart" => 0,
//	"isReplaceAppConf" => 0,
//	"alarmMail" => "<EMAIL>,<EMAIL>",
//);
//$objOrpScp = new Orp_OrpScp ();
//$ret = $objOrpScp->publish ( $orpConf );
//if (! isset ( $ret ['result'] ) || true !== $ret ['result']) {
//	Bingo_Log::warning ( 'publish faild' . " input:" . serialize ( $orpConf ) . " output:" . serialize ( $ret ) );
//    setStatus('publish faild' . " input:" . serialize ( $orpConf ) . " output:" . serialize ( $ret ), $statusInput);
//	return false;
//}

// add zgw2014 2019-12-06 飞线配送文件到线上，打包的文件不会被清理掉，一直挤压在线上，造成线上机器磁盘被打满
//Bingo_Timer::start('ueg_strategy_clearJobDataFile');
//$jobData = ROOT_PATH . "/data/jobdata/*";
//system("find $jobData -mtime +3 |xargs rm -rf");
//Bingo_Timer::end('ueg_strategy_clearJobDataFile');
//
//setStatus('Success', $statusInput);
return true;		
