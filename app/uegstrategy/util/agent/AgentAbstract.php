<?php

abstract class Util_Agent_AgentAbstract
{

    const PARAMETER_INT = 'int';
    const PARAMETER_STR = 'string';
    const PARAMETER_FLOAT = 'float';
    const PARAMETER_ARRARY = 'array';

    abstract protected function call();

    public function getCircuitBreaker($strServiceName)
    {
        $aentCircuitBreaker = new AgentCircuitBreaker();
        return $aentCircuitBreaker->get($strServiceName);
    }

    public function setCircuitBreaker($agent, $strServiceName)
    {
        $aentCircuitBreaker = new AgentCircuitBreaker();
        return $aentCircuitBreaker->set($agent, $strServiceName);
    }

    public function checkParameter($requestType, $strServiceName, $method, $data)
    {
        if (empty($strServiceName) || empty($method)) {
            return false;
        }
        if (!is_string($strServiceName)) {
            return false;
        }
        if ($requestType != Libs_Define_Uegstrategy::REQUEST_HTTP && empty($data)) {
            return false;
        }
        return true;
    }

    public function result($is_success, $result)
    {
        return array('is_success' => $is_success, 'result' => $result);
    }

}