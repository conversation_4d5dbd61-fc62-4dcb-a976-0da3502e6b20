<?php
/***************************************************************************
 *
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file Request.php
 * <AUTHOR>
 * @date 2013/12/25 13:50:29
 * @brief
 *
 **/

define('BINGO_ENCODE_LANG', 'UTF-8');

class Util_UegStrategyRequest
{
    private $_arrClientAttr = array();
    private $_arrBaseAttr = array();
    private $_arrPrivateAttr = array();
    private $_arrStrategy = array();
    private $_arrAttr = array();

    /**
     * @param
     * @return array
     */
    public function getAllAttr()
    {
        return $this->_arrAttr;
    }

    /**
     * @param
     * @return array
     */
    public function getCommonAttr($strKey, $mixDefalut = null)
    {
        if (isset($this->_arrBaseAttr[$strKey])) {
            return $this->_arrBaseAttr[$strKey];
        } else {
            return $mixDefalut;
        }
    }

    /**
     * @param
     * @return array
     */
    public function addCommonAttr($strKey, $strValue)
    {
        $this->_arrBaseAttr[$strKey] = $strValue;
        if (!isset($this->_arrAttr[$strKey])) {  //Private first
            $this->_arrAttr[$strKey] = $strValue;
        }
    }

    /**
     * @param
     * @return array
     */
    public function getPrivateAttr($strKey, $mixDefalut = null)
    {
        if (isset($this->_arrPrivateAttr[$strKey])) {
            return $this->_arrPrivateAttr[$strKey];
        } else {
            return $mixDefalut;
        }
    }

    /**
     * @param
     * @return array
     */
    public function getAllPrivateAttr()
    {
        return $this->_arrPrivateAttr;
    }

    /**
     * @param
     * @return array
     */
    public function addPrivateAttr($strKey, $strValue)
    {
        $this->_arrPrivateAttr[$strKey] = $strValue;
        $this->_arrAttr[$strKey]        = $strValue;
    }

    /**
     * @param
     * @return array
     */
    public function addPrivateAttrByArr($arrNewAttr)
    {
        $this->_arrPrivateAttr = array_merge($this->_arrPrivateAttr, $arrNewAttr);
        $this->_arrAttr        = array_merge($this->_arrAttr, $arrNewAttr);
    }

    /**
     * @param
     * @return array
     */
    public function getStrategy($strKey, $bolDefalut = true)
    {
        if (isset($this->_arrStrategy[$strKey])) {
            return $this->_arrStrategy[$strKey];
        } else {
            return $bolDefalut;
        }
    }

    /**
     * @param
     * @return array
     */
    public function getAllStrategy()
    {
        return $this->_arrStrategy;
    }

    /**
     * @param
     * @return array
     */
    public function setStrategy($arrConf)
    {
        if (!empty($this->_arrStrategy)) {
            $this->_arrStrategy = array_merge($this->_arrStrategy, $arrConf);
        } else {
            $this->_arrStrategy = $arrConf;
        }
    }

    /**
     * @param
     * @return array
     */
    public function addStrategy($strKey, $mixValue)
    {
        $this->_arrStrategy[$strKey] = $mixValue;
    }

}
