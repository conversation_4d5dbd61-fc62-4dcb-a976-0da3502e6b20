<?php
@set_time_limit(0);
@ini_set("memory_limit", "2G");

// wget data
$output = array();
$return_val = -1;
exec("rm ./data.txt", $output, $return_val);
exec("rm ./data.txt", $output, $return_val);
exec("wget -O ./data.txt http://cp01-rdqa-dev313.cp01.baidu.com:8888/data.txt", $output, $return_val);
if (0 != $return_val) {
    echo "wget data.txt failed\n";
    return false;
}

// process data.txt
$file = fopen("data.txt", 'r');
while (true) {
    $single_line = fgets($file);
    if (feof($file)) {
        echo "success\n";
        break;
    }
    $elems = explode("|", $single_line);
    if (count($elems) != 2) {
        file_put_contents('failFile.txt', $single_line, FILE_APPEND);
        continue;
    }
    $user_id = intval($elems[0]);
    $card_num = intval($elems[1]);
    $add_card_num = (-1)*$card_num;
    // add sign card
    $ret = addSignCard($user_id, $add_card_num);
    if (false === $ret) {
        file_put_contents('failFile.txt', $single_line, FILE_APPEND);
        continue;
    }
    // send message 不在这里发私信了，私信发送频繁会失败
    /*
    $ret = sendMsg($user_id, $card_num);
    if (false === $ret) {
        file_put_contents('failFile.txt', $single_line, FILE_APPEND);
        continue;
    }
    */
    file_put_contents('succFile.txt', $single_line, FILE_APPEND);
}

function addSignCard($uid, $card_num) {
    $input = array(
        'user_id' => $uid,
        'props_id' => 1080001,
        'use_num' => $card_num,
    );    
    for ($cycle = 0; $cycle < 10; $cycle++) {
        $arrRes = Tieba_Service::call('tbmall', 'useSignProps', $input, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRes || 0 != $arrRes['errno']) {
            if ($cycle == 9) {
                echo "can addSignCard failed. input: ".serialize($input)."_output:".serialize($arrRes)."\n";
                return false;
            }
        } else {
            return true;
        }
    }
}
function sendMsg($uid, $card_num) {
    $strContent = "您的补签卡已恢复：亲爱的吧友您好！前段时间由于系统问题所导致的断签问题已修复，目前已将您在系统故障期间所使用的".$card_num."张补签卡补回，请查阅。故障带来的不便非常抱歉！贴吧会员团队感谢您对贴吧的支持！祝您继续愉快地玩耍！";
    $opFlag = array(
        'pmsg_user' => 1,
        'cuid' => '',
        'sync' => 1,
    );
    $input = array(
        'group_id' => 0,
        'group_type' => 6,
        'msg_type' => 1,
        'user_id' => 1638369935,
        'user_type' => 1,
        'to_user_id' => $uid,
        'to_user_type' => 1,
        'content' => $strContent,
        'duration' => 0,
        'record_id' => 0,
        'cuid' => '',
        'version' => '',
        'device' => '',
        'data' => '',
        'is_friend' => 0,
        'ignore_followed_set' => 1,
        'op_flag' => json_encode($opFlag),
    );    
    for ($cycle = 0; $cycle < 5; $cycle++) {
        $arrRes = Tieba_Service::call('im', 'commitPcmsg', $input, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRes || 0 != $arrRes['errno']) {
            if ($cycle == 4) {
                echo "can sendMsg failed. input: ".serialize($input)."_output:".serialize($arrRes)."\n";
                return false;
            }
        } else {
            return true;
        }
    }
}