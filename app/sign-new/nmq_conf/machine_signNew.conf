[UbClientConfig]
[.UbClient]

#竞争的例子（不用zk时）

[..@Service]
Name  : signNew
ConnectAll :  0
DefaultConnectTimeOut  :  1000
DefaultReadTimeOut  :  1000
DefaultWriteTimeOut  :  1000
DefaultMaxConnect  :  64
#DefaultRetry  :  5
#LONG / SHORT
DefaultConnectType  :  SHORT
#DefaultLinger  :  0
#ReqBuf  :  100
#ResBuf  :  100
#DefaultAsyncWaitingNum  :  100
#声明将要使用的策略类及属性
[...CurrStrategy]
ClassName  :  UbClientStrategy
[...CurrHealthy]
ClassName  :  UbClientHealthyChecker
[...@Server]
IP : ************
Port : 80
[...@Server]
IP : ************
Port : 80
[...@Server]
IP : ***********
Port : 80
[...@Server]
IP : **********
Port : 80
[...@Server]
IP : **********
Port : 80
[...@Server]
IP : **********
Port : 80
