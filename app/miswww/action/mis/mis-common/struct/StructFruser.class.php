<?php
/**
 * brief of ziyuan.php:
 * 
 * <AUTHOR>
 * @date 2008/01/11 15:00:29
 * @version $Revision: 1.3 $ 
 * @todo useful struct in ziyuan
 */

/*
UserInfoBrowser			= 400,		// �����û������������Ϣ

typedef struct user_manager_t{
	u_int  		command_no;         				// �����
	char 		uname[NP_MAX_USERNAME_LEN];			// �û���/���Ρ���¼��ע��
	u_int		uid;
	u_int		uip;								// �����û�IP(��½ip)
	char		opuname[NP_MAX_USERNAME_LEN];		//��������������û���
	u_int		opuid;
	char		word[MAX_KEYWORD_LEN];				// ��������
	u_int		forum_id;							// ����id
	char		email[NP_MAX_EMAIL_LEN];			// �û����䣬��¼��ע��ʹ��
	u_int 		page_num;						// ����û��б���ҳ��һ���û�����š�
	u_int		res_num;						// ����û��б���ҳ��Ҫ���صĸ���

	int			status;							//��������ʱʹ�ã��Ժ��������
	int 		index;							// �������ڴ�����������������ȡ�û�������
	int			detail_len;
	u_int		reserved;						// ����
} user_manager_t;

typedef struct _user_response_t{
	int 		err_no;
	char    	uname[NP_MAX_USERNAME_LEN];       // �û���
	u_int		uid;   //�û�ID
	u_int 		power;			// ��ʾ�û�Ȩ�ޣ�ʹ��enum usr_power�ж����ֵ
	int			filter_reason;		//������½�ķ��ԭ��
	int			filter_day;		
	time_t		filter_optime;     //����ʱ��
	int			score;			//�������ã�֪��������upointϵͳ��
	int 		num;			//�������ص��û�������
	u_int		flag;
	time_t		last_logintime;
	u_int		last_ip;
	int			detail_len;
	int			reserved;		// �����ֶ�
} user_response_t;
*/

define('NP_MAX_USERNAME_LEN', 32);
define('MAX_KEYWORD_LEN', 64);
define('NP_MAX_EMAIL_LEN', 76);

define('UserInfoBrowser', 400); // �����û������������Ϣ

include_once('Struct.class.php');

class fruser extends Struct
{
    private function packInit($strStruct)
    {
        $arrParamInit = array();
        switch ($strStruct)
        {
            case "user_manager_t":
                $arrParamInit = array(
                    'command_no' => 0,
                    'uname' => "",
                    'uid' => 0,
                    'uip' => 0,
                    'opuname' => "",
                    'opuid' => 0,
                    'word' => "",
                    'forum_id' => 0,
                    'email' => "",
                    'page_num' => 0,
                    'res_num' => 0,
                    'status' => 0,
                    'index' => 0,
                    'detail_len' => 0,
                    'reserved' => 0,
                );
                break;
        }
        
        return $arrParamInit;
    }

    private function packData($strStruct, $arrParam)
    {
        $strPack = "";
        switch ($strStruct)
        {
            case "user_manager_t":
                $strPack = pack("I", $arrParam['command_no'])
                          .parent::fillString(NP_MAX_USERNAME_LEN, $arrParam['uname'])
                          .pack("I", $arrParam['uid'])
                          .pack("I", $arrParam['uip'])
                          .parent::fillString(NP_MAX_USERNAME_LEN, $arrParam['opuname'])
                          .pack("I", $arrParam['opuid'])
                          .parent::fillString(MAX_KEYWORD_LEN, $arrParam['word'])
                          .pack("I", $arrParam['forum_id'])
                          .parent::fillString(NP_MAX_EMAIL_LEN, $arrParam['email'])
                          .pack("I", $arrParam['page_num'])
                          .pack("I", $arrParam['res_num'])
                          .pack("i", $arrParam['status'])
                          .pack("i", $arrParam['index'])
                          .pack("i", $arrParam['detail_len'])
                          .pack("I", $arrParam['reserved']);
                break;
        }
        
        return $strPack;
    }
   
    private function unpackParamStruct($strStruct, $strColumn="")
    {
        $arrRet = array();
        
        switch($strStruct)
        {
            case "_user_response_t":
                $arrRet['len'] = 4 * 13 + NP_MAX_USERNAME_LEN;
                $arrRet['unpack'] = "ierr_no/C".NP_MAX_USERNAME_LEN."/Iuid/Ipower/ifilter_reason/ifilter_day/Ifilter_optime/iscore/inum/Iflag/Ilast_logintime/Ilast_ip/idetail_len/ireserved";
                break;
        }
        
        if (strlen($strColumn))
        {
            return $arrRet[$strColumn];
        }
        else
        {
            return $arrRet;
        }
    }
    
    private function unpackParam($strStruct, &$strData, $strColumn="")
    {
        $arrRet = $this->unpackParamStruct($strStruct);
        if (count($arrRet) && strlen($strData))
        {
            $arrDetail = unpack($arrRet['unpack'], substr($strData, 0, $arrRet['len']));
            $strData = substr($strData, $arrRet['len']);
            
            if (strlen($strColumn))
            {
                return $arrDetail[$strColumn];
            }
            else
            {
                return $arrDetail;
            }
        }
    }

 
    //public method
    public function printString($arrParam, $strColumn)
    {
        return parent::printString($arrParam, $strColumn);
    }

    public function packGetUserId($strUserName)
    {
        $strData = "";
        
        $arrParam = $this->packInit("user_manager_t");
        $arrParam['command_no'] = UserInfoBrowser;
        $arrParam['opuname'] = $strUserName;
    
        $strData = $this->packData("user_manager_t", $arrParam);
    
        return $strData;
    }
    
    public function unpackGetUserId($strData)
    {
        $arrResp = $this->unpackParam("_user_response_t", &$strData);
 
        return $arrResp;
    }
    
    public function autoCommit($arrSockets, $strData)
    {
        $strRes = parent::autoCommit($arrSockets, $strData);
        
        return $strRes;
    }
}
?>
