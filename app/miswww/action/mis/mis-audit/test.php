<?php
	echo "Hi";
/*	require_once("/home/<USER>/www/mis/mis-common/struct/StructZiyuan.class.php");
	require_once("/home/<USER>/www/mis/mis-common/function/socket.common.php");
	$obj = new ziyuan();
	$data = $obj->packGetForumId("test");
	$serverInfo = array(
		"host" => "*************",
		"port" => 9983,
		"timeout" => 100);
	$ret = $obj->autoCommit($serverInfo,$data);
	var_dump($obj->unpackGetForumId($ret));
	echo "test php";
*/
	$fname = "wow";
	$url = "http://m1-forum-mis01.m1.baidu.com:8090/photo-mis/getFName?fname=".$fname;
//	$url = "http://www.baidu.com";
	$curl = curl_init();

	curl_setopt($curl, CURLOPT_URL, $url);
	curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
	curl_setopt($curl, CURLOPT_HEADER, 0);

	// execute and return string (this should be an empty string '')
	$str = curl_exec($curl);

	curl_close($curl);

	// the value of $str is actually bool(true), not empty string ''
	var_dump(intval($str));

?>
