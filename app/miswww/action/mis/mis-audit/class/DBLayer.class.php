<?php
/**
 * DBLayer ��
 *
 * ���ݿ����
 * Ӧ����һ��Singleton
 * �ṩ��һ�������ݿ�����
 * ʹ��PEAR::DB package
 */

class DBLayer
{
    static private $instance;
    static private $db;
    /**
     *  ���ʵ������
     */
    static public function &getInstance ()
    {
        if (!isset(self::$instance)) {
            $class = __CLASS__;
            self::$instance = new $class;
        }
        return self::$instance;
    }
    /**
     * ���캯��
     *
     * ����һ����Singleton������һ�������ݿ�����
     * ʹ����PEAR::DB
     */
    private function __construct()
    {
        $dsn = DSN_SYS;
        $options = array('debug'=> 2);

        self::$db = & DB::connect($dsn, $options);
        if (DB::isError(self::$db)) {
            die(self::$db->getMessage());
        }
    }
    /**
     * ��������
     *
     * �Ͽ����ݿ�����
     */
    public function __destruct()
    {
        //self::$db->disconnect();
    }
    /**
     * ��ȡ���ݿ�����
     *
     * ����PEAR::DB::DB_Common�࣬
     * ����ͬһ�����ݿ�����
     * @return DB_Common �������ݿ������
     */
    static public function &getDBConnection()
    {
        return self::$db;
    }
    static public function isError($result)
    {
        return DB::isError($result);
    }
}

?>
