<?php
class Service_Card{
	private static function _errRet($errno){
    		return array(
        		'errno' => $errno,
        		'errmsg' => Tieba_Error::getErrmsg($errno),
    		);
	}

	public static function getBtxCard($input_arr)
	{
	   $forum_id      = $input_arr['forum_id'];
	   $forum_level_1 = $input_arr['forum_dir']['level_1_name'];
       $forum_card = $input_arr['forum_card'];
	   $card_json = $forum_card['card_p1']['style_name'];
	   if(!isset($forum_card) || !isset($card_json))
	   {
	   	   $conf = Bd_Conf::getConf("/app/forum/service_forum_card");
	   	   if($conf === false){
	   		   return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	   	   }
	   	  /* 
	   	   foreach($conf['ForumCard'] as $elem){
	   		if($elem['type'] == $forum_level_1){
	   			$output_arr['avatar'] = $elem['avatar']['url'];
	   		}
	   	 }
	   	 
	   	if(empty($output_arr)){
	   		$output_arr['avatar'] = $conf['ForumCard_Default']['avatar']['url'];
		}
		   */
		   $output_arr['avatar'] = $conf['Default']['url'];
	   }
	   else{
	   	    $output_arr = json_decode($card_json, true);
	   	    if($output_arr === false){
	   		     return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
	   	    }
	   	    
	   	    $output_arr['slogan'] = Bingo_Encode::convert($output_arr['slogan'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
	   	    if(isset($output_arr['desc'])){
	   		     $output_arr['desc'] = Bingo_Encode::convert($output_arr['desc'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
	   	    }
	   	
	      }
	      
	      $errno = Tieba_Errcode::ERR_SUCCESS;
	      return array(
	      		'errno' => $errno,
	      		'errmsg' => Tieba_Error::getErrmsg($errno),
	      		'output' => $output_arr
	      );
	      
	}
	public static function getForumCard($input_arr){
		$forum_id = $input_arr['forum_id'];
		$forum_level_1 = $input_arr['forum_dir']['level_1_name'];
		$forum_style = $input_arr['forum_style'];
		

		foreach($forum_style as $style){
			if($style['module_id'] == 5){
				$card_json = $style['style_name'];
			}
		}

		if(empty($card_json)){
			$conf = Bd_Conf::getConf("/app/forum/service_forum_card");
			if($conf === false){
				return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
			}
			/*
			foreach($conf['ForumCard'] as $elem){
				if($elem['type'] == $forum_level_1){
					$output_arr['avatar'] = $elem['avatar']['url'];
				}
			}
			if(empty($output_arr)){
				$output_arr['avatar'] = $conf['ForumCard_Default']['avatar']['url'];
			}*/
			$output_arr['avatar'] = $conf['Default']['url'];
		}
		else{
			$output_arr = json_decode($card_json, true);			
			if($output_arr === false){
				return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
			}
			$output_arr['slogan'] = Bingo_Encode::convert($output_arr['slogan'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
			if(isset($output_arr['desc'])){
				$output_arr['desc'] = Bingo_Encode::convert($output_arr['desc'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
			} 
		}

		$errno = Tieba_Errcode::ERR_SUCCESS;
		return array(
			'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
			'output' => $output_arr
		);
	}
}
?>
