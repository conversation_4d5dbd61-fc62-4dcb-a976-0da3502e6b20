//@description: 全局属性
//@author: leonhong


struct attr_data_t //查询接口返回的全局属性，key为属性名，value为属性内容
{
};

struct attr_value_t //全局属性值（支持uint64_t、string、array三种类型）
{
};

service globalattr
{
	/**
	* @brief : 获取所有全局属性
	* @param [out] data[] : attr_data_t : 返回全局属性信息
	**/
	void getAllGlobalAttr(out attr_data_t data[]);
	
	/**
	* @brief : 设置一个全局属性
	* @param [in] attr_name : string : 全局属性名
	* @param [in] attr_value : attr_value_t : 全局属性值（支持uint64_t、string、array三种类型）
	**/
	commit void setGlobalAttr(string attr_name, attr_value_t attr_value);
	
	/**
	* @brief : 删除一个全局属性
	* @param [in] attr_name : string : 全局属性名
	**/
	commit void deleteGlobalAttr(string attr_name);
	
};
