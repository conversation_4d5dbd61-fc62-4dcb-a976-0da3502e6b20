<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file User.php
 * <AUTHOR>
 * @date 2013/08/13 17:26:02
 * @brief functions that calls user service and tieba session
 *  
 **/

class Lib_User{
	public static function isLogin()
	{
		return Tieba_Session_Socket::isLogin();
	}

	public static function getUserId()
	{
		return Tieba_Session_Socket::getLoginUid();
	}

	public static function getUserName()
	{
		return Tieba_Session_Socket::getLoginUname();
	}
	/**
	 * [getUnameByUid description]
	 * @param  [type] $intUid [description]
	 * @return [type]         [description]
	 */
	public static function getUnameByUid($intUid)
	{
		$arrInput = array(
			'user_id' => array($intUid),
		);
		$arrOutput = Tieba_Service::call('user', 'getUnameByUids', $arrInput);
		if (false === $arrOutput || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS ){
			Bingo_Log::warning(Lib_Error::formatLog('user', 'getUnameByUids', $arrInput, $arrOutput));
			return false;
		}
		return $arrOutput['output']['unames'][0]['user_name'];
	}
	/**
	 * [getRegTime description]
	 * @param  [type] $intUid [description]
	 * @return [type]         [description]
	 */
	public static function getRegTime($intUid){
		$arrInput = array(
			'user_id' => $intUid,
			'need_pass_info' => 1,
            'extra_fields'   => array(
                'regtime',
            ),
		);
		$arrOutput = Tieba_Service::call('user', 'getUserDataEx', $arrInput);
		if (false === $arrOutput || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS ){
			Bingo_Log::warning(Lib_Error::formatLog('user', 'getUnameByUids', $arrInput, $arrOutput));
			return false;
		}
		return $arrOutput['user_info']['puserinfo']['regtime'];
	}
}

/* vim: set noet ts=4 sw=4 sts=4 tw=100: */
?>