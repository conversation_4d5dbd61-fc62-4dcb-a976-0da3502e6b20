<?php
/**
 * created by xia<PERSON><PERSON>.
 * email: <EMAIL>
 * file name: Cheat.php
 * create time: 2019-05-06 10:11:44
 * describe: 吧主作弊后台
 */

class Service_Cheat_Cheat {

    const APPLY_STATUS_VALID = 0; //在选
    const APPLY_STATUS_FINISH = 1; //已结束

    const SORT_CHEAT_RATE_ASC = 1; //作弊率升序
    const SORT_CHEAT_RATE_DESC = 2; //作弊率降序
    const SORT_CHEAT_NUM_ASC = 3; //作弊数升序
    const SORT_CHEAT_NUM_DESC = 4; //作弊数降序

    /**
     * 根据吧ID获取吧投票及作弊票信息
     * @param $arrInput
     * @return array
     */
    public static function getVoteInfoByFidAndStatus($arrInput) {
        Bingo_Log::notice(__METHOD__.' input params '.serialize($arrInput));
        $arrCheckParams = array('forum_id',);
        if (!self::_checkInput($arrCheckParams, $arrInput)) {
            Bingo_Log::warning(__METHOD__ . ' input params error :'.serialize($arrInput));
            return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //获取吧
        $intForumId = intval($arrInput['forum_id']);
        $intStatus = isset($arrInput['status']) ? intval($arrInput['status']) : Lib_NewRule::FORUM_ELECTION_STATUS_PUBLIC;
        $arrDlInput = array(
            'function' => 'getForumByFidAndStatus',
            'forum_id' => $intForumId,
            'status'   => $intStatus,
        );
        $arrDlOutput = Dl_Db_ForumElection::execSql($arrDlInput);
        if (!self::_checkRes($arrDlOutput)) {
            Bingo_Log::warning(__METHOD__.' call dl getForumByForumIdAndStatus fail. input:['.serialize($arrDlInput).'], output:['.serialize($arrDlOutput).']');
            return self::_buildReturn(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrForumInfo = $arrDlOutput['results'][0][0];
        if (empty($arrForumInfo)) {
            Bingo_Log::notice(__METHOD__.' db-res-is-empty '.serialize($arrForumInfo));
            return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, array());
        }

        //获取候选人票信息
        $arrDlInput = array(
            'function' => 'getVoteInfoByFidAndStatus',
            'forum_id' => $intForumId,
            'status'   => self::APPLY_STATUS_VALID,
        );
        $arrDlOutput = Dl_Db_Candidate::execSql($arrDlInput);
        if (!self::_checkRes($arrDlOutput)) {
            Bingo_Log::warning(__METHOD__.' call dl getVoteInfoByFidAndStatus fail. input:['.serialize($arrDlInput).'], output:['.serialize($arrDlOutput).']');
            return self::_buildReturn(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrVoteInfo = $arrDlOutput['results'][0];
        $intTotalCount = count($arrVoteInfo);
        $arrOut = array(
            'list' => self::formatForumVoteData($arrVoteInfo, array($arrForumInfo)),
            'total_count' => $intTotalCount,
        );

        return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrOut);
    }

    /**
     * 翻页获取吧投票信息
     * @param $arrInput
     * @return array
     */
    public static function getVoteInfoByPage($arrInput) {
        Bingo_Log::notice(__METHOD__.' input params '.serialize($arrInput));
        $arrCheckParams = array('num');
        if (!self::_checkInput($arrCheckParams, $arrInput)) {
            Bingo_Log::warning(__METHOD__ . ' input params error :'.serialize($arrInput));
            return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intOffset= intval($arrInput['offset']);
        $intLimit = intval($arrInput['num']);
        $intStatus = isset($arrInput['status']) ? intval($arrInput['status']) : Lib_NewRule::FORUM_ELECTION_STATUS_PUBLIC;

        if ($intOffset < 0) {
            $intOffset = 0;
        }
        if ($intLimit < 0) {
            $intLimit = 10;
        }

        $arrDlInput = array(
            'function' => 'getForumByPage',
            'offset'   => $intOffset,
            'limit'    => $intLimit,
            'status'   => $intStatus,
        );
        $arrDlOutput = Dl_Db_ForumElection::execSql($arrDlInput);
        if (!self::_checkRes($arrDlOutput)) {
            Bingo_Log::warning(__METHOD__.' call dl getForumByPage fail. input:['.serialize($arrDlInput).'], output:['.serialize($arrDlOutput).']');
            return self::_buildReturn(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrFirstForumInfo = $arrDlOutput['results'][0][0];
        if (empty($arrFirstForumInfo)) {
            Bingo_Log::notice(__METHOD__.' db-res-is-empty '.serialize($arrDlOutput));
            return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, array());
        }

        $arrForumInfos = $arrDlOutput['results'][0];
        $arrForumIds = array();
        foreach ($arrForumInfos as $arrForumInfo) {
            $arrForumIds[] = $arrForumInfo['forum_id'];
        }
        if (empty($arrForumIds)) {
            Bingo_Log::notice(__METHOD__.' db-res-is-empty '.serialize($arrForumIds));
            return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, array());
        }

        //获取公示期吧数目
        $arrDlInput = array(
            'function' => 'getForumCountByPage',
            'offset'   => $intOffset,
            'limit'    => $intLimit,
            'status'   => $intStatus,
        );
        $arrDlOutput = Dl_Db_ForumElection::execSql($arrDlInput);
        if (!self::_checkRes($arrDlOutput)) {
            Bingo_Log::warning(__METHOD__.' call dl getForumCountByPage fail. input:['.serialize($arrDlInput).'], output:['.serialize($arrDlOutput).']');
            return self::_buildReturn(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $intTotalCount = $arrDlOutput['results'][0][0]['total_count'];

        //获取候选人票信息
        $arrDlInput = array(
            'function' => 'getVoteInfoByFidsAndStatus',
            'forum_ids' => join(',', $arrForumIds),
            'status'   => self::APPLY_STATUS_VALID,
        );
        $arrDlOutput = Dl_Db_Candidate::execSql($arrDlInput);
        if (!self::_checkRes($arrDlOutput)) {
            Bingo_Log::warning(__METHOD__.' call dl getVoteInfoByFidAndStatus fail. input:['.serialize($arrDlInput).'], output:['.serialize($arrDlOutput).']');
            return self::_buildReturn(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrVoteInfo = $arrDlOutput['results'][0];
        $arrOut = array(
            'list' => self::formatForumVoteData($arrVoteInfo, $arrForumInfos),
            'total_count' => $intTotalCount,
        );
        return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrOut);
    }

    /**
     * 根据IDS获取吧投票信息
     * @param $arrInput
     * @return array
     */
    public static function getVoteInfoByForumIds($arrInput) {
        Bingo_Log::notice(__METHOD__.' input params '.serialize($arrInput));
        $arrCheckParams = array('forum_ids');
        if (!self::_checkInput($arrCheckParams, $arrInput)) {
            Bingo_Log::warning(__METHOD__ . ' input params error :'.serialize($arrInput));
            return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (!is_array($arrInput['forum_ids'])) {
            Bingo_Log::warning(__METHOD__ . ' input params error forum_ids not array:'.serialize($arrInput));
            return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrForumIds = $arrInput['forum_ids'];
        $intStatus = isset($arrInput['status']) ? intval($arrInput['status']) : Lib_NewRule::FORUM_ELECTION_STATUS_PUBLIC;

        //获取吧信息
        $arrDlInput = array(
            'function' => 'getForumInfoByForumIds',
            'forum_ids' => join(',', $arrForumIds),
            'status'   => $intStatus,
        );
        $arrDlOutput = Dl_Db_ForumElection::execSql($arrDlInput);
        if (!self::_checkRes($arrDlOutput)) {
            Bingo_Log::warning(__METHOD__.' call dl getForumInfoByForumIds fail. input:['.serialize($arrDlInput).'], output:['.serialize($arrDlOutput).']');
            return self::_buildReturn(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrForumInfos = $arrDlOutput['results'][0];
        $arrForumIds = array();
        foreach ($arrForumInfos as $arrForumInfo) {
            $arrForumIds[] = $arrForumInfo['forum_id'];
        }
        if (empty($arrForumIds)) {
            Bingo_Log::notice(__METHOD__.' db-res-is-empty '.serialize($arrForumIds));
            return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, array());
        }

        //获取候选人票信息
        $arrDlInput = array(
            'function' => 'getVoteInfoByFidsAndStatus',
            'forum_ids' => join(',', $arrForumIds),
            'status'   => self::APPLY_STATUS_VALID,
        );
        $arrDlOutput = Dl_Db_Candidate::execSql($arrDlInput);
        if (!self::_checkRes($arrDlOutput)) {
            Bingo_Log::warning(__METHOD__.' call dl getVoteInfoByFidAndStatus fail. input:['.serialize($arrDlInput).'], output:['.serialize($arrDlOutput).']');
            return self::_buildReturn(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrVoteInfo = $arrDlOutput['results'][0];

        $arrOut = array(
            'list' => self::formatForumVoteData($arrVoteInfo, $arrForumInfos),
        );
        return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrOut);
    }

    /**
     * 根据吧ID获取当前吧候选人的投票信息
     * @param $arrInput
     * @return array
     */
    public static function getForumApplyInfoByForumId($arrInput) {
        Bingo_Log::notice(__METHOD__.' input params '.serialize($arrInput));
        $arrCheckParams = array('forum_id', 'num',);
        if (!self::_checkInput($arrCheckParams, $arrInput)) {
            Bingo_Log::warning(__METHOD__ . ' input params error :'.serialize($arrInput));
            return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $intOffset= intval($arrInput['offset']);
        $intLimit = intval($arrInput['num']);
        if ($intOffset < 0) {
            $intOffset = 0;
        }
        if ($intLimit < 0) {
            $intLimit = 10;
        }

        //获取吧
        $intForumId = intval($arrInput['forum_id']);
        $intStatus = isset($arrInput['status']) ? intval($arrInput['status']) : Lib_NewRule::FORUM_ELECTION_STATUS_PUBLIC;
        $arrDlInput = array(
            'function' => 'getForumByFidAndStatus',
            'forum_id' => $intForumId,
            'status'   => $intStatus,
        );
        $arrDlOutput = Dl_Db_ForumElection::execSql($arrDlInput);
        if (!self::_checkRes($arrDlOutput)) {
            Bingo_Log::warning(__METHOD__.' call dl getForumByForumIdAndStatus fail. input:['.serialize($arrDlInput).'], output:['.serialize($arrDlOutput).']');
            return self::_buildReturn(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrForumInfo = $arrDlOutput['results'][0][0];
        //没有公示期的吧 直接返回
        if (empty($arrForumInfo)) {
            Bingo_Log::notice(__METHOD__.' db-res-is-empty '.serialize($arrForumInfo));
            return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, array());
        }

        //获取候选人票信息
        $arrDlInput = array(
            'function' => 'getVoteInfoByFidAndSort',
            'forum_id' => $intForumId,
            'status'   => self::APPLY_STATUS_VALID,
            'offset'   => $intOffset,
            'limit'    => $intLimit,
        );
        $arrDlOutput = Dl_Db_Candidate::execSql($arrDlInput);
        if (!self::_checkRes($arrDlOutput)) {
            Bingo_Log::warning(__METHOD__.' call dl getVoteInfoByFidAndSort fail. input:['.serialize($arrDlInput).'], output:['.serialize($arrDlOutput).']');
            return self::_buildReturn(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrVoteInfo = $arrDlOutput['results'][0];
        $arrFirstVoteInfo = $arrDlOutput['results'][0][0];
        if (empty($arrFirstVoteInfo)) {
            Bingo_Log::notice(__METHOD__.' db-apply-res-is-empty '.serialize($arrForumInfo));
            return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, array());
        }

        //获取总候选人数目
        $arrDlInput = array(
            'function' => 'getVoteInfoCountByFidAndSort',
            'forum_id' => $intForumId,
            'status'   => self::APPLY_STATUS_VALID,
        );
        $arrDlOutput = Dl_Db_Candidate::execSql($arrDlInput);
        if (!self::_checkRes($arrDlOutput)) {
            Bingo_Log::warning(__METHOD__.' call dl getVoteInfoCountByFidAndSort fail. input:['.serialize($arrDlInput).'], output:['.serialize($arrDlOutput).']');
            return self::_buildReturn(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $intTotalCount = intval($arrDlOutput['results'][0][0]['total_count']);
        $arrOut = array(
            'list' => self::formatApplyVoteData($arrVoteInfo),
            'total_count' => $intTotalCount,
        );
        return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrOut);
    }

    /**
     * 分页获取作弊票信息
     * @param $arrInput
     * @return array
     */
    public static function getCheatVoteInfo($arrInput) {
        Bingo_Log::notice(__METHOD__.' input params '.serialize($arrInput));
        $arrCheckParams = array('num',);
        if (!self::_checkInput($arrCheckParams, $arrInput)) {
            Bingo_Log::warning(__METHOD__ . ' input params error :'.serialize($arrInput));
            return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $intOffset= intval($arrInput['offset']);
        $intLimit = intval($arrInput['num']);
        $intStatus = isset($arrInput['status']) ? intval($arrInput['status']) : self::APPLY_STATUS_VALID;
        if ($intOffset < 0) {
            $intOffset = 0;
        }
        if ($intLimit < 0) {
            $intLimit = 10;
        }

        $arrDlInput = array(
            'function' => 'getCheatVoteInfo',
            'offset'  => $intOffset,
            'limit'   => $intLimit,
            'status'  => $intStatus,
        );
        $arrDlOutput = Dl_Db_Candidate::execSql($arrDlInput);
        if (!self::_checkRes($arrDlOutput)) {
            Bingo_Log::warning(__METHOD__.' call dl getCheatVoteInfo fail. input:['.serialize($arrDlInput).'], output:['.serialize($arrDlOutput).']');
            return self::_buildReturn(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrForumInfo = $arrDlOutput['results'][0];
        return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrForumInfo);
    }

    /**
     * 封装候选人投票信息
     * @param $arrVoteList
     * @return mixed
     */
    private static function formatApplyVoteData($arrVoteList) {
        foreach ($arrVoteList as &$arrVoteInfo) {
            $arrVoteInfo['user_name'] = '';
            $arrVoteInfo['nickname'] = '';
            $arrVoteInfo['total_vote_num'] = $arrVoteInfo['vote_num'];
            $arrVoteInfo['total_cheat_num'] = $arrVoteInfo['cheat_num'];
            $intValidVoteNum =  $arrVoteInfo['vote_num'] - $arrVoteInfo['cheat_num'];
            $arrVoteInfo['valid_vote_num'] = $intValidVoteNum;
            $arrVoteInfo['cheat_percent'] = ($arrVoteInfo['vote_num'] > 0) ? round($arrVoteInfo['cheat_num']/ $arrVoteInfo['vote_num'], 2) * 100 : 0;
            $arrVoteInfo['cheat_rate'] =  $arrVoteInfo['cheat_percent'] . '%';
        }
        unset($arrVoteInfo);
        return $arrVoteList;
    }

    /**
     * 封装吧投票信息
     * @param $arrForums 吧数据
     * @param $arrVoteList 候选人数据
     * @return array
     */
    private static function formatForumVoteData($arrVoteList, $arrForums) {
        if (empty($arrVoteList[0])) {
            return array();
        }

        $arrVoteListNew = array();
        $arrForumsNew = array();
        foreach ($arrVoteList as $arrVoteInfo) {
            $arrVoteListNew[$arrVoteInfo['forum_id']][] = $arrVoteInfo;
        }
        foreach ($arrForums as $arrForum) {
            $arrForumsNew[$arrForum['forum_id']] = $arrForum;
        }
        $arrForums = $arrForumsNew;

        $arrVoteRes = array();
        foreach ($arrVoteListNew as $intForumId => $arrForumVote) {
            $arrForumInfo = array(
                'forum_id' => $intForumId,
                'forum_name' => '',
                'total_vote_num' => 0,
                'total_cheat_num' => 0,
                'cheat_rate' => 0,
                'valid_vote_num' => 0,
            );
            foreach ($arrForumVote as $arrVote) {
                $arrForumInfo['total_vote_num'] += $arrVote['vote_num'];
                $arrForumInfo['total_cheat_num'] += $arrVote['cheat_num'];
            }
            $arrForumInfo['cheat_rate'] = ($arrForumInfo['total_vote_num'] > 0) ? round($arrForumInfo['total_cheat_num'] / $arrForumInfo['total_vote_num'], 2) * 100 : 0;
            $arrForumInfo['cheat_percent'] =  $arrForumInfo['cheat_rate'];
            $arrForumInfo['cheat_rate'] =  $arrForumInfo['cheat_rate'] . '%';
            $arrForumInfo['valid_vote_num'] = $arrForumInfo['total_vote_num'] - $arrForumInfo['total_cheat_num'];
            $arrVoteRes[$intForumId] = $arrForumInfo;
        }

        foreach ($arrForums as $intFid => &$arrForum) {
            foreach ($arrVoteRes as $intForumId => $arrVote) {
                if ($intFid == $intForumId) {
                    $arrForum['forum_name'] = isset($arrVote['forum_name']) ? $arrVote['forum_name'] : '';
                    $arrForum['total_vote_num'] = isset($arrVote['total_vote_num']) ? $arrVote['total_vote_num'] : 0;
                    $arrForum['total_cheat_num'] = isset($arrVote['total_cheat_num']) ? $arrVote['total_cheat_num'] : 0;
                    $arrForum['valid_vote_num'] = isset($arrVote['valid_vote_num']) ? $arrVote['valid_vote_num'] : 0;
                    $arrForum['cheat_percent'] = isset($arrVote['cheat_percent']) ? $arrVote['cheat_percent'] : 0;
                    $arrForum['cheat_rate'] = isset($arrVote['cheat_rate']) ? $arrVote['cheat_rate'] : '0%';
                }
            }
        }
        unset($arrForum);
        return $arrForums;
    }

    /**
     * 构建返回
     * @param $intErrno
     * @param null $arrOutput
     * @return array
     */
    private static function _buildReturn($intErrno = Tieba_Errcode::ERR_SUCCESS, $arrOutput = null)
    {
        $strErrmsg = Tieba_Error::getErrmsg($intErrno);
        $arrResult = array(
            'errno'  => $intErrno,
            'errmsg' => $strErrmsg,
        );
        if ( $arrOutput !== null ){
            $arrResult['data'] = $arrOutput;
        }

        return $arrResult;
    }

    /**
     * 检查入参
     * @param $arrArgList
     * @param $arrInput
     * @return bool
     */
    private static function _checkInput($arrArgList, $arrInput)
    {
        foreach ($arrArgList as $strArg){
            if (!isset($arrInput[$strArg]) || empty($arrInput[$strArg])){
                Bingo_Log::warning('arg ['.$strArg.'] is not existed.');
                return false;
            }
        }
        return true;
    }

    /**
     * 检查HTTP结果
     * @param $arrRes
     * @return bool
     */
    private static function _checkRes($arrRes)
    {

        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            return false;
        }
        return true;
    }
}