<?php
/**
 * created by xia<PERSON><PERSON>.
 * email: <EMAIL>
 * file name: Redis.php
 * create time: 2019-03-26 19:59:55
 * describe:
 */

class Service_Util_Redis {
    protected static $redis = null;
    protected static $tryCount = 2;

    // redis实例
    const REDIS_PID = 'bazhu';

    const PREFIX_USER_FORUM_HAS_VOTED = 'forum_user_has_voted_'; //用户在当前吧投票状态
    const PREFIX_FORUM_VOTE_NUM = 'forum_voted_num_'; //当前吧的投票数
    const PREFIX_ALL_FORUM_VOTE_NUM = 'all_forum_voted_num';  //所有吧的投票数
    const PREFIX_LEAVE_NO_BAZHU_FORUM_NUM = 'leave_no_bazhu_forum_num'; //所有剩余无吧主吧数目
    const PREFIX_APPLYING_USER = 'applying_user'; //正在申请的用户信息（跑马灯）
    const PREFIX_APPLY_SUCCESS_USER = 'apply_success_user'; //成功申请吧主的用户 (跑马灯)
    const PREFIX_APPLY_SUCCESS_USER_NUM = 'apply_success_user_num'; //成功申请吧主的人数 （跑马灯）
    const PREFIX_FORUM_APPLY_NUM = 'forum_apply_num_'; //当前吧申请人数
    const PREFIX_ALL_FORUM_APPLY_NUM = 'all_forum_apply_num'; //所有吧申请人数
    const PREFIX_USER_APPLY_FORUM_SUCCESS = 'user_apply_bazhu_success_'; //用户申请吧主成功首次进入弹窗
    const PREFIX_FORUM_RANK = 'forum_bazhu_vote_rank_'; //吧投票排行榜 forum_bazhu_vote_rank_forum_id_
    const PREFIX_USER_FORUM_HAS_APPLIED = 'forum_user_has_applied_'; //用户在当前吧申请状态


    const REDIS_EXPIRE_USER_VOTE = 604800; //7天
    const REDIS_EXPIRE_BAZHU = 1296000; // 15天

    /**
     * 记录用户在本吧投票状态
     * @param $arrInput
     * @param $strRedis
     * @return bool
     */
    public static function setUserForumVote($arrInput, $strRedis = self::REDIS_PID) {
        $intForumId = intval($arrInput['forum_id']);
        $intUserId = intval($arrInput['vote_uid']);
        $intValue = 1;
        $strKey = self::PREFIX_USER_FORUM_HAS_VOTED.$intForumId.'_'.$intUserId;
        $arrRedisInput = array(
            'key' => $strKey,
            'value' => $intValue,
            'seconds' => self::REDIS_EXPIRE_USER_VOTE,
        );
        $arrOut = self::call('setex', $arrRedisInput, $strRedis);
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['err_no']) {
            Bingo_Log::warning(__METHOD__.'-call-set redis fail input:'.serialize($arrInput).' output:'.serialize($arrOut));
            return false;
        }
        return true;
    }

    /**
     * 记录用户在当前吧申请状态
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    public static function setUserForumApplied($arrInput, $strRedis = self::REDIS_PID) {
        $intForumId = intval($arrInput['forum_id']);
        $intUserId = intval($arrInput['candidate_uid']);
        $intValue = 1;
        $strKey = self::PREFIX_USER_FORUM_HAS_APPLIED.$intForumId.'_'.$intUserId;
        $arrRedisInput = array(
            'key' => $strKey,
            'value' => $intValue,
            'seconds' => self::REDIS_EXPIRE_USER_VOTE,
        );
        $arrOut = self::call('setex', $arrRedisInput, $strRedis);
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['err_no']) {
            Bingo_Log::warning(__METHOD__.'-call-set redis fail input:'.serialize($arrInput).' output:'.serialize($arrOut));
            return false;
        }
        return true;
    }

    /**
     * 获取用户在当前吧申请状态
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    public static function getUserForumApplied($arrInput, $strRedis = self::REDIS_PID) {
        $intForumId = intval($arrInput['forum_id']);
        $intUserId = intval($arrInput['candidate_uid']);
        $strKey = self::PREFIX_USER_FORUM_HAS_APPLIED.$intForumId.'_'.$intUserId;
        $arrRedisInput = array(
            'key' => $strKey,
        );
        $arrOut = self::call('get', $arrRedisInput, $strRedis);
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['err_no']) {
            Bingo_Log::warning(__METHOD__.'-call-set redis fail input:'.serialize($arrInput).' output:'.serialize($arrOut));
            return false;
        }
        return $arrOut['ret'][$strKey];
    }

    /**
     * 设置用户申请成功
     * @param $arrInput
     * @param $strRedis
     * @return bool
     */
    public static function setUserApplySuccess($arrInput, $strRedis = self::REDIS_PID) {
        $intForumId = intval($arrInput['forum_id']);
        $intUserId = intval($arrInput['candidate_uid']);
        $strKey = self::PREFIX_USER_APPLY_FORUM_SUCCESS.$intUserId;
        $arrRedisInput = array(
            'key' => $strKey,
            'value' => $intForumId,
            'seconds' => self::REDIS_EXPIRE_BAZHU,
        );
        $arrOut = self::call('setex', $arrRedisInput, $strRedis);
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['err_no']) {
            Bingo_Log::warning(__METHOD__.'-call-set redis fail input:'.serialize($arrInput).' output:'.serialize($arrOut));
            return false;
        }
        return true;
    }

    /**
     * 设置无吧主数
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    public static function setLeaveNoBazhuNum($arrInput, $strRedis = self::REDIS_PID) {
        $intValue = $arrInput['value'];
        $strKey = self::PREFIX_LEAVE_NO_BAZHU_FORUM_NUM;
        $arrRedisInput = array(
            'key' => $strKey,
            'value' => $intValue,
        );
        $arrOut = self::call('set', $arrRedisInput, $strRedis);
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['err_no']) {
            Bingo_Log::warning(__METHOD__.'-call-set redis fail input:'.serialize($arrInput).' output:'.serialize($arrOut));
            return false;
        }
        return true;
    }

    /**
     * 获取用户在本吧投票状态
     * @param $arrInput
     * @param $strRedis
     * @return bool
     */
    public static function getUserForumVote($arrInput, $strRedis = self::REDIS_PID) {
        $intForumId = intval($arrInput['forum_id']);
        $intUserId = intval($arrInput['vote_uid']);
        $strKey = self::PREFIX_USER_FORUM_HAS_VOTED.$intForumId.'_'.$intUserId;
        $arrRedisInput = array(
            'key' => $strKey,
        );
        $arrOut = self::call('get', $arrRedisInput, $strRedis);
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['err_no']) {
            Bingo_Log::warning(__METHOD__.'-call-set redis fail input:'.serialize($arrInput).' output:'.serialize($arrOut));
            return false;
        }
        return $arrOut['ret'][$strKey];
    }

    /**
     * 增加投票数据
     * @param $arrInput
     * @param $strRedis
     * @return mixed
     */
    public static function incrForumVoteNum($arrInput, $strRedis = self::REDIS_PID) {
        $intForumId = intval($arrInput['forum_id']);
        $arrInput1 = array(
            'key' => self::PREFIX_FORUM_VOTE_NUM.$intForumId,
            'seconds' => self::REDIS_EXPIRE_USER_VOTE,
        );
        $arrInput2 = array(
            'key' => self::PREFIX_ALL_FORUM_VOTE_NUM,
            'seconds' => self::REDIS_EXPIRE_USER_VOTE,
        );
        $arrRedisInput = array(
            'reqs' => array(
                $arrInput1,
                $arrInput2,

            ),
        );

        $arrRet = self::call('INCR', $arrRedisInput, $strRedis);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            //重试
            $arrRet = self::call('INCR', $arrRedisInput, $strRedis);
            if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
                Bingo_Log::warning(sprintf(__METHOD__."-call-redis-INCR post failed. [input = %s][output = %s]", serialize($arrRedisInput), serialize($arrRet)));
                return false;
            }
        }
        //设置过期时间
        $arrRet = self::call('EXPIRE', $arrRedisInput, self::REDIS_PID);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf(__METHOD__."-call-redis-expire post failed. [input = %s][output = %s]", serialize($arrRedisInput), serialize($arrRet)));
        }

        return true;
    }

    /**
     * 获取当前吧投票数
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    public static function getCurrentForumVoteNum($arrInput, $strRedis = self::REDIS_PID) {
        $intForumId = intval($arrInput['forum_id']);
        $arrRedisInput = array(
            'key' => self::PREFIX_FORUM_VOTE_NUM.$intForumId,
        );
        $arrOut = self::call('get', $arrRedisInput, $strRedis);
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['err_no']) {
            Bingo_Log::warning(__METHOD__.'-call-get redis fail input:'.serialize($arrInput).' output:'.serialize($arrOut));
            return false;
        }
        return $arrOut['ret'][$arrRedisInput['key']];
    }


    /**
     * 获取所有吧吧投票数
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    public static function getAllForumVoteNum($arrInput, $strRedis = self::REDIS_PID) {
        $arrRedisInput = array(
            'key' => self::PREFIX_ALL_FORUM_VOTE_NUM
        );
        $arrOut = self::call('get', $arrRedisInput, $strRedis);
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['err_no']) {
            Bingo_Log::warning(__METHOD__.'-call-get redis fail input:'.serialize($arrInput).' output:'.serialize($arrOut));
            return false;
        }
        return $arrOut['ret'][$arrRedisInput['key']];
    }

    /**
     * 获取剩余无吧主吧数目
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    public static function getLeaveNoBazhuNum($arrInput, $strRedis = self::REDIS_PID) {
        $arrRedisInput = array(
            'key' => self::PREFIX_LEAVE_NO_BAZHU_FORUM_NUM
        );
        $arrOut = self::call('get', $arrRedisInput, $strRedis);
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['err_no']) {
            Bingo_Log::warning(__METHOD__.'-call-get redis fail input:'.serialize($arrInput).' output:'.serialize($arrOut));
            return false;
        }
        return $arrOut['ret'][$arrRedisInput['key']];
    }

    /**
     * 获取吧当前申请数目
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    public static function getForumApplyingNum($arrInput, $strRedis = self::REDIS_PID) {
        $intForumId= intval($arrInput['forum_id']);
        $arrRedisInput = array(
            'key' => self::PREFIX_FORUM_APPLY_NUM.$intForumId,
        );
        $arrOut = self::call('get', $arrRedisInput, $strRedis);
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['err_no']) {
            Bingo_Log::warning(__METHOD__.'-call-get redis fail input:'.serialize($arrInput).' output:'.serialize($arrOut));
            return false;
        }
        return $arrOut['ret'][$arrRedisInput['key']];
    }

    /**
     * 批量获取吧申请人数
     * @param $arrInput
     * @param string $strRedis
     * @return array|bool
     */
    public static function mgetForumApplyNum($arrInput, $strRedis = self::REDIS_PID) {
        if (empty($arrInput)) {
            return array();
        }
        $arrForumIds = $arrInput['forum_ids'];
        $arrRedisReq = array();
        foreach ($arrForumIds as $key => $intForumId) {
            $arrParams = array(
                'key' => self::PREFIX_FORUM_APPLY_NUM.$intForumId,
            );
            $arrRedisReq[] = $arrParams;

        }
        $arrRedisInput = array(
            'reqs' => $arrRedisReq
        );
        $arrOut = self::call('get', $arrRedisInput, $strRedis);
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['err_no']) {
            Bingo_Log::warning(__METHOD__.'-call-MGET redis fail input:'.serialize($arrInput).' output:'.serialize($arrOut));
            return false;
        }
        return $arrOut['ret'];
    }

    /**
     * 获取所有申请吧主人数
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    public static function getAllForumApplyingNum($arrInput, $strRedis = self::REDIS_PID) {
        $arrRedisInput = array(
            'key' => self::PREFIX_ALL_FORUM_APPLY_NUM
        );
        $arrOut = self::call('get', $arrRedisInput, $strRedis);
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['err_no']) {
            Bingo_Log::warning(__METHOD__.'-call-get redis fail input:'.serialize($arrInput).' output:'.serialize($arrOut));
            return false;
        }
        return $arrOut['ret'][$arrRedisInput['key']];
    }

    /**
     * 获取申请成功人数
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    public static function getApplySuccessNum($arrInput, $strRedis = self::REDIS_PID) {
        $arrRedisInput = array(
            'key' => self::PREFIX_APPLY_SUCCESS_USER_NUM
        );
        $arrOut = self::call('get', $arrRedisInput, $strRedis);
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['err_no']) {
            Bingo_Log::warning(__METHOD__.'-call-get redis fail input:'.serialize($arrInput).' output:'.serialize($arrOut));
            return false;
        }
        return $arrOut['ret'][$arrRedisInput['key']];
    }

    /**
     * 获取用户成功单选吧主数据，为活动页弹窗使用，请求成功后就删除该key
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    public static function getUserApplySuccess($arrInput, $strRedis = self::REDIS_PID) {
        $intForumId = intval($arrInput['forum_id']);
        $intUserId = intval($arrInput['candidate_uid']);
        $strKey = self::PREFIX_USER_APPLY_FORUM_SUCCESS.$intUserId;
        $arrRedisInput = array(
            'key' => $strKey,
        );
        $arrOut1 = self::call('get', $arrRedisInput, $strRedis);
        if (false === $arrOut1 || Tieba_Errcode::ERR_SUCCESS !== $arrOut1['err_no']) {
            Bingo_Log::warning(__METHOD__.'-call-set redis fail input:'.serialize($arrInput).' output:'.serialize($arrOut1));
            return false;
        }
        $arrOut2 = self::call('DEL', $arrRedisInput, $strRedis);
        if (false === $arrOut2 || Tieba_Errcode::ERR_SUCCESS !== $arrOut1['err_no']) {
            $arrOut2 = self::call('DEL', $arrRedisInput, $strRedis);
            if (false === $arrOut2 || Tieba_Errcode::ERR_SUCCESS !== $arrOut1['err_no']) {
                Bingo_Log::warning(__METHOD__.'-call-del redis fail input:'.serialize($arrInput).' output:'.serialize($arrOut2));
            }
        } else {
            Bingo_Log::warning(__METHOD__.'-call-del redis success input:'.serialize($arrInput).' output:'.serialize($arrOut2));
        }
        return $arrOut1['ret'][$arrRedisInput['key']];
    }

    /**
     * 删除Redis Key
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    public static function deleteKey($arrInput, $strRedis = self::REDIS_PID) {
        $strKey = strval($arrInput['key']);
        $arrRedisInput = array(
            'key' => $strKey,
        );
        $arrOut = self::call('DEL', $arrRedisInput, $strRedis);
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['err_no']) {
            Bingo_Log::warning(__METHOD__.'-call-DEL redis fail input:'.serialize($arrInput).' output:'.serialize($arrOut));
            return false;
        }
        return $arrOut;
    }

    /**
     * 吧申请人数
     * @param $arrInput
     * @param $strRedis
     * @return mixed
     */
    public static function incrForumApplyNum($arrInput, $strRedis = self::REDIS_PID) {
        $intForumId = intval($arrInput['forum_id']);
        $arrInput1 = array(
            'key' => self::PREFIX_FORUM_APPLY_NUM.$intForumId,
            'seconds' => self::REDIS_EXPIRE_USER_VOTE,
        );
        $arrInput2 = array(
            'key' => self::PREFIX_ALL_FORUM_APPLY_NUM,
            'seconds' => self::REDIS_EXPIRE_USER_VOTE,
        );
        $arrRedisInput = array(
            'reqs' => array(
                $arrInput1,
                $arrInput2,

            ),
        );

        $arrRet = self::call('INCR', $arrRedisInput, $strRedis);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            //重试
            $arrRet = self::call('INCR', $arrRedisInput, $strRedis);
            if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
                Bingo_Log::warning(sprintf(__METHOD__."-call-redis-INCR post failed. [input = %s][output = %s]", serialize($arrRedisInput), serialize($arrRet)));
                return false;
            }
        }

        //设置过期时间
        $arrRet = self::call('EXPIRE', $arrRedisInput, self::REDIS_PID);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf(__METHOD__."-call-redis-expire post failed. [input = %s][output = %s]", serialize($arrRedisInput), serialize($arrRet)));
        }

        return true;
    }

    /**
     * 成功申请人数增加，减少无吧主数
     * @param $arrInput
     * @param $strRedis
     * @return mixed
     */
    public static function incrApplySuccessNum($arrInput, $strRedis = self::REDIS_PID) {
        $intForumId = intval($arrInput['forum_id']);
        $arrInput1 = array(
            'key' => self::PREFIX_APPLY_SUCCESS_USER_NUM,
            'step' => 1,
        );
        $arrInput2 = array(
            'key' => self::PREFIX_LEAVE_NO_BAZHU_FORUM_NUM,
            'step' => -1
        );
        $arrRedisInput = array(
            'reqs' => array(
                $arrInput1,
                $arrInput2,

            ),
        );

        $arrRet = self::call('INCRBY', $arrRedisInput, $strRedis);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            //重试
            $arrRet = self::call('INCRBY', $arrRedisInput, $strRedis);
            if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
                Bingo_Log::warning(sprintf(__METHOD__."-call-redis-INCR post failed. [input = %s][output = %s]", serialize($arrRedisInput), serialize($arrRet)));
                return false;
            }
        }

        return true;
    }

    /**
     * 吧主下任  增加剩余无吧主吧数
     * @param $strRedis
     * @return mixed
     */
    public static function incrLeaveForumNum($strRedis = self::REDIS_PID) {
        $arrRedisInput = array(
            'key' => self::PREFIX_LEAVE_NO_BAZHU_FORUM_NUM,
            'step' => 1,
        );

        $arrRet = self::call('INCRBY', $arrRedisInput, $strRedis);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            //重试
            $arrRet = self::call('INCRBY', $arrRedisInput, $strRedis);
            if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
                Bingo_Log::warning(sprintf(__METHOD__."-call-redis-INCR  failed. [input = %s][output = %s]", serialize($arrRedisInput), serialize($arrRet)));
                return false;
            }
        }

        return true;
    }


    /**
     * 设置成功上任的用户集合（最大2000 跑马灯数据）
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    public static function setApplySuccessUserToSet($arrInput, $strRedis = self::REDIS_PID) {
        $intForumId = intval($arrInput['forum_id']);
        $intCandidateUid = intval($arrInput['candidate_uid']);
        $strMember = $intForumId.'_'.$intCandidateUid;

        $arrRedisInput = array(
            'score' => time(),
            'key' => self::PREFIX_APPLY_SUCCESS_USER,
            'member' =>  $strMember,
            'max_length' => 2000,
        );

        $boolRet = self::setRedisList($arrRedisInput, $strRedis);
        if(!$boolRet) {
            $boolRet = self::setRedisList($arrRedisInput, $strRedis);
            if (!$boolRet) {
                Bingo_Log::warning(sprintf(__METHOD__."-call-redis-set failed. [input = %s][output = %s]", serialize($arrRedisInput), serialize($boolRet)));
                return false;
            }
        }
        return true;
    }

    /**
     * 设置吧候选人票数榜单
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    public static function setForumVoteMemberRank($arrInput, $strRedis = self::REDIS_PID) {
        $intForumId = intval($arrInput['forum_id']);
        $intCandidateId = intval($arrInput['candidate_uid']);
        $arrRedisInput = array(
            'step' => 1,
            'key' => self::PREFIX_FORUM_RANK.$intForumId,
            'member' =>  $intCandidateId,
            'seconds' => self::REDIS_EXPIRE_USER_VOTE,
        );
        $arrRet = self::call('ZINCRBY', $arrRedisInput, $strRedis);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(__METHOD__ . sprintf("redis ZINCRBY  failed. [input = %s][output = %s]",serialize($arrRedisInput), serialize($arrRet)));
            return false;
        }

        //设置过期时间
        $arrRet = self::call('EXPIRE', $arrRedisInput, self::REDIS_PID);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf(__METHOD__."-call-redis-expire post failed. [input = %s][output = %s]", serialize($arrRedisInput), serialize($arrRet)));
            return false;
        }

        return true;
    }

    /**
     * 获取候选人投票数排名
     * @param $arrInput
     * @param string $strRedis
     * @return bool|int
     */
    public static function getUserForumVoteRank($arrInput, $strRedis = self::REDIS_PID) {
        $intForumId = intval($arrInput['forum_id']);
        $intCandidateId = intval($arrInput['candidate_uid']);
        $strKey = self::PREFIX_FORUM_RANK.$intForumId;
        $arrRedisInput = array(
            'key' => $strKey,
            'member' =>  $intCandidateId,
        );
        $arrRes = self::call('ZREVRANK', $arrRedisInput, $strRedis);
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['err_no']) {
            Bingo_Log::warning(__METHOD__ . sprintf("redis ZREVRANK  failed. [input = %s][output = %s]",serialize($arrRedisInput), serialize($arrRes)));
            return false;
        }

        $rank = $arrRes['ret'][$strKey];
        if (null === $rank) {
            $rank = 0;
        } else {
            $rank = $rank + 1;
        }
        return $rank;
    }

    /**
     * 正在申请吧主用户集合（最大2000 跑马灯数据）
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    public static function setApplyingUserToSet($arrInput, $strRedis = self::REDIS_PID) {
        $intForumId = intval($arrInput['forum_id']);
        $intCandidateUid = intval($arrInput['candidate_uid']);
        $strMember = $intForumId.'_'.$intCandidateUid;
        $arrRedisInput = array(
            'score' => time(),
            'key' => self::PREFIX_APPLYING_USER,
            'member' => $strMember,
            'max_length' => 2000,
        );

        $boolRet = self::setRedisList($arrRedisInput, $strRedis);
        if(!$boolRet) {
            $boolRet = self::setRedisList($arrRedisInput, $strRedis);
            if (!$boolRet) {
                Bingo_Log::warning(sprintf(__METHOD__."-call-redis-set failed. [input = %s][output = %s]", serialize($arrRedisInput), serialize($boolRet)));
                return false;
            }
        }
        return true;
    }

    /**
     * 设置获取成功上任的用户集合（最大2000 跑马灯数据
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    public static function getApplySuccessUserToSet($arrInput, $strRedis = self::REDIS_PID) {
        $intPn = isset($arrInput['pn']) ? $arrInput['pn'] : 1;
        $intRn = isset($arrInput['rn']) ? $arrInput['rn'] : 20;
        if (intval($intPn) <= 0) {
            $intPn = 1;
        }
        if (intval($intRn) <= 0) {
            $intRn = 20;
        }
        $intOffset = ($intPn - 1) * $intRn;
        $intCount = $intRn - 1;
        $arrParams = array(
            'key' => self::PREFIX_APPLY_SUCCESS_USER,
            'start' => $intOffset,
            'stop' => $intCount,
        );

        $arrList = self::_getRedisOrderList($arrParams, $strRedis);
        if( $arrList === false ){
            Bingo_Log::warning('call _getRedisOrderList fail input is'.serialize($arrParams).' output is '.serialize($arrList));
            return false;
        }
        $intCount = self::getSortSetCount($arrParams);
        if($intCount === false ){
            Bingo_Log::warning('call getSortSetCount fail input is'.serialize($arrParams).' output is '.serialize($intCount));
            return false;
        }
        $arrRes = array(
            'list' => $arrList,
            'total_count' => $intCount,
        );
        return $arrRes;

    }

    /**
     * 获取正在申请吧主用户集合（最大2000 跑马灯数据）
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    public static function getApplyingUserToSet($arrInput, $strRedis = self::REDIS_PID) {
        $intPn= isset($arrInput['pn']) ? $arrInput['pn'] : 1;
        $intRn = isset($arrInput['rn']) ? $arrInput['rn'] : 20;
        if (intval($intPn) <= 0) {
            $intPn = 1;
        }
        if (intval($intRn) <= 0) {
            $intRn = 20;
        }
        $intOffset = ($intPn - 1) * $intRn;
        $intCount = $intRn - 1;
        $arrParams = array(
            'key' => self::PREFIX_APPLYING_USER,
            'start' => $intOffset,
            'stop' => $intCount,
        );
        $arrList = self::_getRedisOrderList($arrParams, $strRedis);
        if( $arrList === false ){
            Bingo_Log::warning('call _getRedisOrderList fail input is'.serialize($arrParams).' output is '.serialize($arrList));
            return false;
        }
        $intCount = self::getSortSetCount($arrParams);
        if($intCount === false ){
            Bingo_Log::warning('call getSortSetCount fail input is'.serialize($arrParams).' output is '.serialize($intCount));
            return false;
        }
        $arrRes = array(
            'list' => $arrList,
            'total_count' => $intCount,
        );
        return $arrRes;
    }

    /**
     * 获取集合数目
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    private static function getSortSetCount($arrInput, $strRedis = self::REDIS_PID) {
        $strKey = $arrInput['key'];
        $arrRedisInput = array(
            'key'     => $strKey,
        );
        $arrRet = self::call('ZCARD', $arrRedisInput, $strRedis);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("mget agree from redis failed. [input = %s][output = %s]",serialize($arrRedisInput), serialize($arrRet)));
            return false;
        }
        $intCurLength = $arrRet['ret'][$strKey];
        return $intCurLength;
    }

    /**
     * 获取集合数据
     * @param $arrInput
     * @param string $strRedis
     * @return bool
     */
    private static function _getRedisOrderList($arrInput, $strRedis = self::REDIS_PID) {

        $strKey = strval($arrInput['key']);
        $start = intval($arrInput['start']);
        $stop = intval($arrInput['stop']);

        if( empty($strKey)  ){
            Bingo_Log::warning(__METHOD__ . " input param invalid. [" . serialize($arrInput) . "]");
            return false;
        }
        $arrRedisInput = array(
            'key' => $strKey,
            'start' => $start,
            'stop' =>  $stop,
        );

        $arrRet = self::call('ZREVRANGE', $arrRedisInput, $strRedis);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("mget agree from redis failed. [input = %s][output = %s]",serialize($arrRedisInput), serialize($arrRet)));
            return false;
        }
        return $arrRet['ret'][$strKey];

    }

    /**
     * 设置神回复进Redis
     * @param $arrInput
     * @param $strRedisName
     * @param $strMethod
     * @return bool
     */
    private static function setRedisList($arrInput, $strRedisName, $strMethod) {

        $intScore = floatval($arrInput['score']);
        $strKey = strval($arrInput['key']);
        $strMember = strval($arrInput['member']);
        $intManLength = intval($arrInput['max_length']);  //列表最大长度

        if($intScore <= 0 || empty($strKey) || empty($strMember) || empty($strRedisName)){
            Bingo_Log::warning(__METHOD__ . " input param invalid. [" . serialize($arrInput) . "]");
            return false;
        }

        if($intManLength > 0 ){
            $arrRedisInput = array(
                'key'     => $strKey,
            );

            $arrRet = self::call('ZCARD', $arrRedisInput, $strRedisName);
            if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
                Bingo_Log::warning($strMethod . sprintf("mget agree from redis failed. [input = %s][output = %s]",serialize($arrRedisInput), serialize($arrRet)));
                return false;
            }
            $intCurLength = $arrRet['ret'][$strKey];
            if($intCurLength >= $intManLength){
                //取最后一个进行score比较
                $arrRedisInput = array(
                    'key' => $strKey,
                    'start' => $intManLength - 1,
                    'stop' =>  $intManLength,
                );

                $arrRet = self::call('ZREVRANGEWITHSCORES', $arrRedisInput, $strRedisName);
                if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
                    Bingo_Log::warning($strMethod . sprintf("mget agree from redis failed. [input = %s][output = %s]",serialize($arrRedisInput), serialize($arrRet)));
                    return false;
                }
                $minScore = $arrRet['ret'][$strKey][0]['score'];
                $memberOfMinScore = strval($arrRet['ret'][$strKey][0]['member']);

                if($minScore >= $intScore ){
                    return true;
                } else {
                    //需要替换member，同时清除该值
                    $arrRedisInput = array(
                        'key'     => $strKey,
                        'members' => array(
                            array(
                                'score'  => $intScore,
                                'member' => $strMember,
                            ),
                        ),
                    );
                    $arrRet = self::call('ZADD', $arrRedisInput, $strRedisName);
                    if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
                        Bingo_Log::warning($strMethod . sprintf("redis zadd  failed. [input = %s][output = %s]",serialize($arrRedisInput), serialize($arrRet)));
                        return false;
                    }

                    $arrRedisInput = array(
                        'key'     => $strKey,
                        'member' => $memberOfMinScore,
                    );
                    $arrRet = self::call('ZREM', $arrRedisInput, $strRedisName);
                    if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
                        Bingo_Log::warning($strMethod . sprintf("redis ZREM  failed. [input = %s][output = %s]",serialize($arrRedisInput), serialize($arrRet)));
                        return false;
                    }

                    return true;
                }
            } else {
                $arrRedisInput = array(
                    'key'     => $strKey,
                    'members' => array(
                        array(
                            'score'  => $intScore,
                            'member' => $strMember,
                        ),
                    ),
                );
                $arrRet = self::call('ZADD', $arrRedisInput, $strRedisName);
                if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
                    Bingo_Log::warning(sprintf("redis zadd  failed. [input = %s][output = %s]",serialize($arrRedisInput), serialize($arrRet)));
                    return false;
                }
                return true;
            }
        }
        //不限定长度、长度未到达上限
        $arrRedisInput = array(
            'key'     => $strKey,
            'members' => array(
                array(
                    'score'  => $intScore,
                    'member' => $strMember,
                ),
            ),
        );
        $arrRet = self::call('ZADD', $arrRedisInput, $strRedisName);

        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning(sprintf("redis zadd  failed. [input = %s][output = %s]",serialize($arrRedisInput), serialize($arrRet)));
            return false;
        }
        return true;
    }


    /**
     * @param string $redisPid
     * @return bool
     */
    private static function initRedis($redisPid = null) {
        if (is_null($redisPid)) {
            $redisPid = self::REDIS_PID;
        }
        if (self::$redis == null) {
            self::$redis = new Bingo_Cache_Redis($redisPid);
            if (self::$redis == false) {
                Bingo_Log::warning("init redis ['.$redisPid.'] fail:" . serialize(Bd_RalRpc::get_error()));
                return false;
            }
        }
        return true;
    }

    /**
     * @param string $redisPid
     * @return bool
     */
    public static function getRedis($redisPid = null) {
        if (!self::initRedis($redisPid)) {
            return false;
        }

        return self::$redis;
    }

    /**
     * @param $method
     * @param $req
     * @param string $redisPid
     * @return bool
     */
    public static function call($method, $req, $redisPid = self::REDIS_PID) {
        $try = 0;
        if (!self::initRedis($redisPid)) {
            return false;
        }

        while ($try < self::$tryCount) {
            $res = self::$redis->$method($req);
            if (Tieba_Errcode::ERR_SUCCESS == $res['err_no']) {
                return $res;
            }
            $try++;
        }
        Bingo_Log::warning('call redis:' . $method . ' fails. error msg: ' . $res['err_msg']);
        return false;
    }

    /**
     * INCR
     * @param $arrInput
     * @return bool
     */
    public static function INCR($arrInput) {
        if (!self::initRedis()) {
            return false;
        }
        $arrRet = self::$redis->INCR($arrInput);
        if ($arrRet === false || $arrRet['err_no'] !== 0) {
            Bingo_Log::warning("call redis error.[" . serialize($arrRet) . "]");
            return false;
        }
        $data = $arrRet['ret'][$arrInput['key']];
        return $data;
    }

    /**
     * EXPIRE
     * @param $arrInput
     * @return bool
     */
    public static function EXPIRE($arrInput) {
        if (!self::initRedis()) {
            return false;
        }
        $arrRet = self::$redis->EXPIRE($arrInput);
        if ($arrRet === false || $arrRet['err_no'] !== 0) {
            Bingo_Log::warning("call redis error.[" . serialize($arrRet) . "]");
            return false;
        }
        $data = $arrRet['ret'];
        return $data;
    }
}