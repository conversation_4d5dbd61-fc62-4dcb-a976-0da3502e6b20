<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Condition.php
 * <AUTHOR>
 * @date 2016/08/30 21:55:51
 * @brief 
 *  
 **/
class Service_Condition_Condition{
	// constants
	const SERVICE_NAME = "Service_Condition_Condition";
	const DB_NAME = 'forum_managerapply';
	// DB object
	private static $_objDB = null;
    /** 
     * [clearCondition description]
     * @param [type] $arrInput [description]
     * @return
     */
    public static function clearCondition($arrInput)
    {   
        $arrArgList = array('forum_id', 'user_post_num', 'member_days');
        if ( !self::_checkInput($arrArgList, $arrInput) ){
            return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        } 
        $intForumId = intval($arrInput['forum_id']);
        $intUserPostNum = intval($arrInput['user_post_num']);
        $intMemberDays = intval($arrInput['member_days']);
        if ( $intForumId <= 0 ){
            Bingo_Log::warning("illegal forum_id [$intForumId]");
            return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }   
         
        $strSql = "REPLACE INTO apply_condition(forum_id,user_post_num,member_days) VALUE($intForumId,$intUserPostNum,$intMemberDays)";
        $arrRet = self::_queryDB($strSql, 'db_set_cond');
        if ( $arrRet === false ){
            return self::_buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        return self::_buildReturn();
    }
	
	/**
	 * [getAssistCondition description]
	 * @param  [type] $arrInput [description]
	 * @return [type]           [description]
	 */
	public static function getAssistCondition($arrInput)
	{
		$arrArgList = array('forum_id');
		if ( !self::_checkInput($arrArgList, $arrInput) ){
			return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$intForumId = $arrInput['forum_id'];
		if ( $intForumId <= 0 ){
			Bingo_Log::warning("illegal forum_id [$intForumId]");
			return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
		}

		$strSql = "SELECT `forum_id`,`condition` FROM `assist_condition` WHERE forum_id=$intForumId";
		$arrRet = self::_queryDB($strSql, 'db_get_assist_cond');
		if ( empty($arrRet) || $arrRet === false ){
			$arrOutput = array(
				'forum_id' => $intForumId,
				'condition' => mb_convert_encoding(Lib_Def::CONDITION_APPLY_ASSISTANT,"gbk","UTF-8"),
			);
			return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
		}

		return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrRet[0]);
	}


	/**
	 * [setAssistCondition description]
	 * @param [type] $arrInput [description]
	 * @return
	 */
	public static function setAssistCondition($arrInput)
	{
		$arrArgList = array('forum_id', 'condition');
		if ( !self::_checkInput($arrArgList, $arrInput) ){
			return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$intForumId = intval($arrInput['forum_id']);
		$strCondition = self::_escape(strval($arrInput['condition']));
		if ( $intForumId <= 0 ){
			Bingo_Log::warning("illegal forum_id [$intForumId]");
			return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		if ( strlen($strCondition) < Lib_Def::CONDITION_APPLY_ASSISTANT_LENGTH_MIN || strlen($strCondition) > Lib_Def::CONDITION_APPLY_ASSISTANT_LENGTH_MAX ){
			Bingo_Log::warning("illegal Condition [$strCondition]");
			return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
		}

		$strSql = "REPLACE INTO `assist_condition` (`forum_id`,`condition`) VALUES($intForumId,'".$strCondition."')";
		$arrRet = self::_queryDB($strSql, 'db_set_assist_cond');
		if ( $arrRet === false ){
			return self::_buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}

		return self::_buildReturn();
	}
	/**
	 * [getCondition description]
	 * @param  [type] $arrInput [description]
	 * @return [type]           [description]
	 */
	public static function getCondition($arrInput)
	{
		$arrArgList = array('forum_id');
		if ( !self::_checkInput($arrArgList, $arrInput) ){
			return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$intForumId = $arrInput['forum_id'];
		if ( $intForumId <= 0 ){
			Bingo_Log::warning("illegal forum_id [$intForumId]");
			return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
		}

		$strSql = "SELECT forum_id,user_post_num,member_days FROM apply_condition WHERE forum_id=$intForumId";
		$arrRet = self::_queryDB($strSql, 'db_get_cond');
		if ( $arrRet === false ){
			$arrOutput = array(
				'forum_id' => $intForumId,
				'user_post_num' => Lib_Def::AC_USER_POST_NUM_MIN,
				'member_days' => 0,
			);
			return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
		}

		return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrRet[0]);
	}
	/**
	 * [setCondition description]
	 * @param [type] $arrInput [description]
	 * @return
	 */
	public static function setCondition($arrInput)
	{
		$arrArgList = array('forum_id', 'user_post_num', 'member_days');
		if ( !self::_checkInput($arrArgList, $arrInput) ){
			return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$intForumId = intval($arrInput['forum_id']);
		$intUserPostNum = intval($arrInput['user_post_num']);
		$intMemberDays = intval($arrInput['member_days']);
		if ( $intForumId <= 0 ){
			Bingo_Log::warning("illegal forum_id [$intForumId]");
			return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		if ( $intUserPostNum < Lib_Def::AC_USER_POST_NUM_MIN || $intUserPostNum > Lib_Def::AC_USER_POST_NUM_MAX ){
			Bingo_Log::warning("illegal user_post_num [$intUserPostNum]");
			return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		if ( !isset(Lib_Def::$AC_ENUM_MEMBER_DAYS[$intMemberDays]) ){
			Bingo_log::warning("illegal member_days [$intMemberDays]");
			return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
		}

		$strSql = "REPLACE INTO apply_condition(forum_id,user_post_num,member_days) VALUE($intForumId,$intUserPostNum,$intMemberDays)";
		$arrRet = self::_queryDB($strSql, 'db_set_cond');
		if ( $arrRet === false ){
			return self::_buildReturn(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}

		return self::_buildReturn();
	}
	/**
	 * [_getDB description]
	 * @param
	 * @return [type] [description]
	 */
	private static function _getDB()
	{
		if ( self::$_objDB ){
			return self::$_objDB;
		}

		Bingo_Timer::start('db_init');
		self::$_objDB = Tieba_Mysql::getDB(self::DB_NAME);
		Bingo_Timer::end('db_init');
		if ( self::$_objDB && self::$_objDB->isConnected() ){
			return self::$_objDB;
		}
		else{
			Bingo_Log::warning('fail to connect db');
			return null;
		}
	}
	/**
	 * [_queryDB description]
	 * @param  [type] $strSql   [description]
	 * @param  string $strTimer [description]
	 * @return [type]           [description]
	 */
	private static function _queryDB($strSql, $strTimer = 'db_query')
	{
		$objDB = self::_getDB();
		if ( !$objDB ){
			Bingo_Log::warning('fail to get db');
			return false;
		}
		Bingo_Timer::start($strTimer);
		$arrRet = $objDB->query($strSql);
		Bingo_Timer::end($strTimer);
		if ( $arrRet === false ){
			$strLastError = $objDB->error();
			Bingo_Log::warning("execute sql error [$strSql] [$strLastError]");
		}
		return $arrRet;
	}
	/**
	 * [_escape description]
	 * @param  [type] $strWord [description]
	 * @return [type]          [description]
	 */
	private static function _escape($strWord)
	{
		$objDB = self::_getDB();
		if ( !$objDB ){
			Bingo_Log::warning('fail to get db');
			return false;
		}
		return $objDB->escapeString($strWord);
	}
	/**
	 * [_buildReturn description]
	 * @param  [type] $intErrno  [description]
	 * @param  [type] $arrOutput [description]
	 * @return [type]            [description]
	 */
	private static function _buildReturn($intErrno = Tieba_Errcode::ERR_SUCCESS, $arrOutput = null)
	{
		$strErrmsg = Tieba_Error::getErrmsg($intErrno);
		$arrResult = array(
			'errno'  => $intErrno,
			'errmsg' => $strErrmsg,
		);
		if ( $arrOutput !== null ){
			$arrResult['output'] = $arrOutput;
		}

		return $arrResult;
	}
	/**
	 * [_checkInput description]
	 * @param  [type] $arrArgList [description]
	 * @param  [type] $arrInput   [description]
	 * @return [type]             [description]
	 */
	private static function _checkInput($arrArgList, $arrInput)
	{
		foreach ( $arrArgList as $strArg ){
			if ( !isset($arrInput[$strArg]) ){
				Bingo_Log::warning('arg ['.$strArg.'] is not existed.');
				return false;
			}
		}
		return true;
	}
}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
