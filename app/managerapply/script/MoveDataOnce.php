<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file MoveDataOnce.php
 * <AUTHOR>
 * @date 2016/10/19 22:34:06
 * @brief 
 *  
 **/

define('NAME_SCRIPT' , 'Script_Managerapply_Movedataonce');
include_once dirname(__FILE__) . "/BaseScript.php";

Bingo_Log::init(array(
    LOG => array(
        'file'  => dirname ( __FILE__ ) . '/../../../log/app/managerapply/'.NAME_SCRIPT.'.log',
        'level' => 0x04 | 0x02,
    ),
), LOG);

class Script_Managerapply_Movedataonce extends BaseScript{
        
    const STEP = 500;
    const MAX_NUM = 2143473647;
    protected $_intOffset = 0;

    /**
     * [getUpdateSqls description]
     * @param  [type] $arrData [description]
     * @return [type]          [description]
     */
    protected function getUpdateSqls($arrData){
        var_dump($arrData);
        $intTime = time();
        $sqlInsert = "INSERT INTO mis_manager(forum_id,user_id,user_ip,user_ip6,apply_type,apply_status,apply_time,audit_time,forum_fdir,user_real_name,user_id_code,user_address,user_im_type,user_im,user_statement,post_num,all_post_num,member_num,manager_num)VALUES(%d,%d,%d,'%s',%d,%d,%d,%d,'%s','%s','%s','%s',%d,'%s','%s',%d,%d,%d,%d)";
        $sqlUpdate = "UPDATE apply_manager SET apply_status=%d,finish_time=$intTime WHERE apply_id=%d";
        $arrSqls = array();
        foreach ($arrData as $k => $v) {
            if($v['manager_num'] >=3){
                $arrSqls []= sprintf($sqlUpdate,12,$v['apply_id']);
            }else{
                $v['user_ip'] = isset($v['user_ip']) ? $v['user_ip'] : 0;
                $v['user_ip6'] = isset($v['user_ip6']) ? $v['user_ip6'] : '';
                $arrSqls []= sprintf($sqlInsert,$v['forum_id'],$v['user_id'],$v['user_ip'],$v['user_ip6'],$v['apply_type'],0,$v['apply_time'],$v['apply_time'],$v['forum_fdir'],$v['user_real_name'],$v['user_id_code'],$v['user_address'],$v['user_im_type'],$v['user_im'],$v['user_statement'],$v['post_num'],$post['all_post_num'],$v['mem_num'],$v['manager_num']);
                $arrSqls []= sprintf($sqlUpdate,11,$v['apply_id']);

            }
        }
        var_dump($arrSqls);
        return $arrSqls;
    }
    /**
     * [getNewSql description]
     * @param
     * @return [type] [description]
     */
    protected function getNewSql(){
    	$intTime = time() - 8*86400;
        $strSql  = sprintf("SELECT * FROM apply_manager WHERE apply_id > %d AND apply_status=0 AND manager_type=1 AND apply_time >%d LIMIT %d", $this->_intOffset, $intTime, self::STEP);
        return $strSql;
    }
    /**
     * [getOldSql description]
     * @param
     * @return [type] [description]
     */
    protected function getOldSql(){
        $intTime = time() - 8*86400;
        $strSql  = sprintf("SELECT * FROM apply_manager WHERE apply_id < %d AND apply_status=0 AND manager_type=1 AND apply_time >%d LIMIT %d", $this->_intOffset, $intTime, self::STEP);
        return $strSql;

    }

    /**
     * [processData description]
     * @param  [type] $arrData [description]
     * @return [type]          [description]
     */
    protected function processData($arrData){
        foreach($arrData as $key => $data){
            $intFid = (int)$data['forum_id'];
            $intUid = (int)$data['user_id'];
            $arrDirOut = Lib_Forum::getForumDir($intFid);
            $str1stDir = $arrDirOut['level_1_name'];
            $str2ndDir = $arrDirOut['level_2_name'];
            $intForumPost = (int)Lib_Post::getUserPostNum($intFid, $intUid, -1);
            $intAllPost   = (int)Lib_Post::getUserAllPostNum($intUid, -1);
            $intMemNum    = (int)Lib_Perm::getForumMemNum($intFid);
            $intMgrNum    = (int)Lib_Perm::getForumManagerNum($intFid);
            $strLog =  "aid:".$data['apply_id']."\tfid:$intFid\tuid:$intUid\t1stdir:$str1stDir\t2nddir:$str2ndDir\tfpost:$intForumPost\tapost:$intAllPost\tmem_num:$intMemNum\tmanager_num:$intMgrNum\t";
            echo $strLog;
            Bingo_Log::warning($strLog);
            if(false === $arrDirOut||$intMgrNum>3||0>$intForumPost||0>$intAllPost){
                unset($arrData);
                $this->_intOffset = $data[$this->_idfield];
                echo "jump\n";
                continue;
            }
            if($intMgrNum >=3){
                echo "full\t";
            }
            $data['post_num'] = $intForumPost;
            $data['all_post_num'] = $intAllPost;
            $data['forum_fdir'] = $str1stDir;
            $data['mem_num'] = $intMemNum;
            $data['manager_num'] = ($intMgrNum === 0 ? 1:$intMgrNum);
            $data['apply_type'] = 1;
            $arrData[$key] = $data;
            echo "\n";
        }
        return $arrData;
    }
    /**
     * [treateData description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    protected function callback($arrInput){
        // if(Lib_Def::AM_APPLY_STATUS_AUTOPASS === $arrInput['status']){
        //     //self::sendMsg($data);
        // }
        return true;
    }
    /**
     * [init description]
     * @param  [type] $strStatus [description]
     * @return [type]            [description]
     */
    public function init($strStatus){
        $arrStatus = unserialize($strStatus);
        $this->_intOffset = 0;//(int)$arrStatus['offset'];
        $today = (int)floor(time() / 86400) * 86400;
        $this->_intToday = $today;
        $this->_idfield = 'apply_id';

    }
    /**
     * [buildStatus description]
     * @return [type] [description]
     */
    protected function buildStatus(){
        $arrStatus = array(
            'offset' => $this->_intOffset,
        );
        return serialize($arrStatus);
    }
}

$obj = new Script_Managerapply_Movedataonce();
$obj->execute();
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
