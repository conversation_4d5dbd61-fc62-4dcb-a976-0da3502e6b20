<?php

/**
 * Created by PhpStorm.
 * User: wangjing01
 * Date: 18/3/12
 * Time: 下午8:13
 */
class Service_Apeallist
{
    //用户申诉db
    protected static $DB_RAL_SERVICE_NAME = 'forum_mis_pmc';

    //策略信息db
    protected static $DB_RAL_SERVICE_NAME_MONITOR = 'forum_spammis';


    //策略召回es
//    const UEGES_SERVICE = "ueges";
    const UEGDEBUGES_SERVICE = 'uegdebuges';
    const UEGES_INDICE_PREFIX_RECALL = "ueg_debug_"; //召回

    const UEGMONITORES_SERVICE = 'uegmonitores';
//    const UEGES_INDICE_PREFIX_DEAL = "ueg_monitor_"; //策略处置

    const UEGES_INDICE_PREFIX_DEAL_POST = "ueg_monitor_post_"; //删贴
    const UEGES_INDICE_PREFIX_DEAL_BLOCK = "ueg_monitor_block_"; //用户封禁
    /**
     * @param $errno
     * @param string $data
     * @return array
     */
    private static function _errRet($errno, $data = '')
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        );
    }


    /**
     * @param $arrInput
     * @param string $desc
     * @return array
     */
    public static function getApealList($arrInput, $desc = 'asc')
    {
        if (!is_numeric($arrInput ['apeal_time_start']) || !is_numeric($arrInput ['apeal_time_end'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input params.
        !$desc && $desc = 'asc';
        $apeal_time_start = $arrInput['apeal_time_start'];
        $apeal_time_end = $arrInput['apeal_time_end'];


        $ret = false;

        $db = Util_Db::getDB(self::$DB_RAL_SERVICE_NAME);
        $sql = "select apeal_id,apeal_code,apeal_uid,apeal_flag,apeal_time,apeal_type,apeal_num,apeal_reason,op_uid,op_uname,op_time,op_reason,status,is_auto,is_vip,punish_byman,punish_fid,lock_time,lock_uid,role from apeal_list";

        $sql .= " where apeal_time>=" . $apeal_time_start . " and apeal_time<" . $apeal_time_end;
        if (!$db) {
            Bingo_Log::warning("db connect failed");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }


        $ret = $db->query($sql);
        if ($ret === false) {
            Bingo_Log::warning("query db failed: $sql");
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        } else {
            $error = Tieba_Errcode::ERR_SUCCESS;
        }
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'ret' => $ret);
        return $arrOutput;

    }

    /**
     * @param $arrInput
     * @param string $desc
     * @return array
     */
    public static function getApealListbyApealcode($arrInput, $desc = 'asc')
    {
        if (!is_numeric($arrInput ['apeal_code'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input params.

        $apeal_code = $arrInput['apeal_code'];


        $ret = false;

        $db = Util_Db::getDB(self::$DB_RAL_SERVICE_NAME);
        $sql = "select apeal_id,apeal_code,apeal_uid,apeal_flag,apeal_time,apeal_type,apeal_num,apeal_reason,op_uid,op_uname,op_time,op_reason,status,is_auto,is_vip,punish_byman,punish_fid,lock_time,lock_uid,role from apeal_list";

        $sql .= " where apeal_code=" . $apeal_code;
        if (!$db) {
            Bingo_Log::warning("db connect failed");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }


        $ret = $db->query($sql);
        if ($ret === false) {
            Bingo_Log::warning("query db failed: $sql");
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        } else {
            $error = Tieba_Errcode::ERR_SUCCESS;
        }

        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'ret' => $ret);
        return $arrOutput;

    }

    /**
     * @param $arrInput
     * @return array|bool
     */

    public static function getUserApealList($arrInput)
    {
        if (!isset($arrInput ['apeal_uid']) || !isset($arrInput ['apeal_flag']) || !isset($arrInput ['ape
al_num'])
        ) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }


        //input params.
        $apealUid = intval($arrInput ['apeal_uid']);
        $apealFlag = intval($arrInput ['apeal_flag']);
        $apealNum = intval($arrInput ['apeal_num']);


        $ret = false;

        $db = Util_Db::getDB(self::$DB_RAL_SERVICE_NAME);
        if (!$db) {
            Bingo_Log::warning("db connect failed");
            return false;
        }
        $where = '';
        if (isset($arrInput ['apeal_code']) && $arrInput ['apeal_code']) {
            $where = "and apeal_code={$arrInput ['apeal_code']}";
        }
        $num = $apealUid % 128;

        $sql = "select apeal_id,apeal_code,apeal_uid,apeal_time,apeal_type,apeal_num,op_uid,op_uname,op_time,status,op_reason from 
user_apeal{$num} where apeal_uid=$apealUid and apeal_flag=$apealFlag {$where} order by apeal_time asc limit 0, $apealNum";
        $ret = $db->query($sql);
        if ($ret === false) {
            Bingo_Log::warning("execute sql fail: $sql");
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        } else {
            $error = Tieba_Errcode::ERR_SUCCESS;
        }
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'ret' => $ret);
        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     *
     */
    public static function getMonitorinfo($arrInput)
    {

        if (!isset($arrInput['monitor'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $monitorType = intval($arrInput['monitor']);
        $db = Util_Db::getDB(self::$DB_RAL_SERVICE_NAME_MONITOR);
        if (!$db) {
            Bingo_Log::warning("db connect failed");
            return false;
        }

        /**
         *
         * spammis_new_version  v
         * online_time: 2018-07-12 17:35:15
         * modify_time: 2018-07-12 17:26:16
         * modify_uname: zhangyixin02
         * online_uname: zhangyixin02
         * name: 黑名单用户封禁
         * version_id: 10
         * filter_id: 2529
         * status_online: 3
         *
         *
         * spammis_new_filter f
         * priority: 1000
         * monitor_type: 80614
         */
        $sql = "SELECT f.monitor_type as monitor_type,v.name as name,v.online_time as online_time,v.online_uname as online_uname, v.modify_time as modify_time,v.modify_uname as modify_uname,v.version_id as version_id, v.filter_id as filter_id,v.status_online as status_online,v.status_est as status_est,f.priority as priority FROM spammis_new_version v join spammis_new_filter f on v.filter_id = f.filter_id WHERE f.monitor_type=" . $monitorType . " ORDER BY modify_time desc";

        Bingo_Log::warning("execute sql: $sql");
        $ret = $db->query($sql);

        if ($ret === false) {
            Bingo_Log::warning("execute sql fail: $sql");
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        } else {
            $error = Tieba_Errcode::ERR_SUCCESS;
        }

        $retout = $ret[0];

        foreach ($ret as $item) {
            if ($item['status_online'] == 4) {
                $retout = $item;
            }
        }

        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'ret' => array($retout));
        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getMonitorDealcount($arrInput)
    {
        if (!isset($arrInput['monitor'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $monitorType = $arrInput['monitor'];
        $date = $arrInput['date'];

        $must[] = array(
            'match' => array(
                'command' => 'MaskUser',
            )
        );

        $must[] = array(
            'term' => array(
                'monitor_type' => $monitorType,
            )
        );

        $arrReq = array(
            "query" => array(
                'bool' => array(
                    'must' => $must
                )
            )
        );
        $pathinfo = self::UEGES_INDICE_PREFIX_DEAL_BLOCK . $date . '/_count';
        $arrRes = Tieba_Ral::call(self::UEGMONITORES_SERVICE, "post", $arrReq, rand(), array('Content-Type' => 'application/json',"pathinfo" => $pathinfo));

        if (!$arrRes) {
            Bingo_Log::warning(self::UEGMONITORES_SERVICE . ' get count fail! res: ' . serialize($arrRes) . ', req: ' . serialize($arrReq) .
                ', pathinfo: ' . $pathinfo);
            $intCount = 0;
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        } else {
            $intCount = $arrRes['count'];
            $error = Tieba_Errcode::ERR_SUCCESS;
        }

        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'ret' => $intCount);
        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getMonitorRecallcount($arrInput)
    {

        if (!isset($arrInput['monitor'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $monitorType = $arrInput['monitor'];
        $date = $arrInput['date'];

        $arrReq = array(
            "query" => array(
                'term' => array(
                    'monitor_type' => $monitorType
                )
            )
        );
        $pathinfo = self::UEGES_INDICE_PREFIX_RECALL . $date . '/recall/_count';


        $arrRes = Tieba_Ral::call(self::UEGDEBUGES_SERVICE, "post", $arrReq, rand(), array('Content-Type' => 'application/json',"pathinfo" => $pathinfo));

        if (!$arrRes) {
            Bingo_Log::warning(self::UEGDEBUGES_SERVICE . ' get count fail! res: ' . serialize($arrRes) . ', req: ' . serialize($arrReq) .
                ', pathinfo: ' . $pathinfo);
            $intCount = 0;
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        } else {
            $intCount = $arrRes['count'];
            $error = Tieba_Errcode::ERR_SUCCESS;
        }

        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'ret' => $intCount);
        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     * @des 获取策略召回，用户处置（未去重），数据不全，废弃
     */
    public static function getMonitorDealandRecallcount($arrInput)
    {
        if (!isset($arrInput['monitor'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $monitorType = $arrInput['monitor'];
        $date = $arrInput['date'];

        $must[] = array(
            'match' => array(
                'command' => 'MaskUser',
            )
        );

        $must[] = array(
            'term' => array(
                'monitor_type' => $monitorType,
            )
        );

        $arrReq = array(
            "query" => array(
                'bool' => array(
                    'must' => $must
                )
            )
        );
        $pathinfo = self::UEGES_INDICE_PREFIX_DEAL_BLOCK . $date . '/_count';
        $arrRes = Tieba_Ral::call(self::UEGMONITORES_SERVICE, "post", $arrReq, rand(), array('Content-Type' => 'application/json',"pathinfo" => $pathinfo));

        if (!$arrRes) {
            Bingo_Log::warning(self::UEGMONITORES_SERVICE . ' get count fail! res: ' . serialize($arrRes) . ', req: ' . serialize($arrReq) .
                ', pathinfo: ' . $pathinfo);
            $intCount = 0;
            $errordeal = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        } else {
            $intCount = $arrRes['count'];
            $errordeal = Tieba_Errcode::ERR_SUCCESS;
        }

        $ret['deal'] = $intCount;


        $arrReq = array(
            "query" => array(
                'term' => array(
                    'monitor_type' => $monitorType
                )
            )
        );
        $pathinfo = self::UEGES_INDICE_PREFIX_RECALL . $date . '/recall/_count';


        $arrRes = Tieba_Ral::call(self::UEGDEBUGES_SERVICE, "post", $arrReq, rand(), array('Content-Type' => 'application/json',"pathinfo" => $pathinfo));

        if (!$arrRes) {
            Bingo_Log::warning(self::UEGDEBUGES_SERVICE . ' get count fail! res: ' . serialize($arrRes) . ', req: ' . serialize($arrReq) .
                ', pathinfo: ' . $pathinfo);
            $intCount = 0;
            $errorrecall = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        } else {
            $intCount = $arrRes['count'];
            $errorrecall = Tieba_Errcode::ERR_SUCCESS;
        }

        $ret['recall'] = $intCount;

        if ($errordeal == Tieba_Errcode::ERR_DB_QUERY_FAIL && $errorrecall == Tieba_Errcode::ERR_DB_QUERY_FAIL) {
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        } else {
            $error = Tieba_Errcode::ERR_SUCCESS;
        }


        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'ret' => $ret);
        return $arrOutput;
    }


    /**
     * @param $arrInput
     * @return array
     * @des 获取 策略召回，用户处置及去重，贴子处置及去重，
     */
    public static function getMonitorDealandRecallcountAll($arrInput)
    {
        if (!isset($arrInput['monitor'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }


        $monitorType = $arrInput['monitor'];
        $date = $arrInput['date'];

        $result = array(
            'recall'=>0, //召回
            'maskuserall'=>0, //用户处置全部
            'maskuser'=>0, //用户处置按uid去重
            'maskdeleteall'=>0,//贴子处置全部
            'maskdelete'=>0,//贴子处置按pid，tid去重.pid=0,对tid去重，pid!=0,对post_id去重
        );

        //取全部数据
        $retRecall = self::getMonitorRecallcountNew($arrInput);
        if (isset($retRecall['errno']) && $retRecall['errno'] == 0) {
            $result['recall'] = $retRecall['ret'];
        }
        $retMaskuser = self::getMonitorMaskUsercountNew($arrInput);
        if (isset($retMaskuser['errno']) && $retMaskuser['errno'] == 0) {
            $result['maskuserall'] = $retMaskuser['ret']['count'];
            $result['maskuser'] = $retMaskuser['ret']['count_dis_uid'];
        }
        $retMasedelete = self::getMonitorMaskDeletecountNew($arrInput);
        if (isset($retMasedelete['errno']) && $retMasedelete['errno'] == 0) {
            $result['maskdeleteall'] = $retMasedelete['ret']['count'];
            $result['maskdelete'] = $retMasedelete['ret']['count_dis_pid'];
        }

        $arrOutput = array(
            'errno' => 0,
            'errmsg' => Tieba_Error::getErrmsg(0),
            'ret' => $result);
        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getMonitorRecallcountNew($arrInput)
    {

        if (!isset($arrInput['monitor'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $monitorType = $arrInput['monitor'];
        $date = $arrInput['date'];

        $arrReq = array(
            "query" => array(
                'term' => array(
                    'monitor_type' => $monitorType
                )
            )
        );
        $pathinfo = self::UEGES_INDICE_PREFIX_RECALL . $date . '/recall/_count';


        $arrRes = Tieba_Ral::call(self::UEGDEBUGES_SERVICE, "post", $arrReq, rand(), array('Content-Type' => 'application/json',"pathinfo" => $pathinfo));


        if (!$arrRes) {
            Bingo_Log::warning(self::UEGDEBUGES_SERVICE . ' get count fail! res: ' . serialize($arrRes) . ', req: ' . serialize($arrReq) .
                ', pathinfo: ' . $pathinfo);
            $intCount = 0;
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        } else {
            $intCount = $arrRes['count'];
            $error = Tieba_Errcode::ERR_SUCCESS;
        }

        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'ret' => $intCount);
        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getMonitorMaskUsercountNew($arrInput)
    {
        if (!isset($arrInput['monitor'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $monitorType = $arrInput['monitor'];
        $date = $arrInput['date'];
        $result = array(
            'count' => 0,
            'count_dis_uid' => 0,
        );

        $must[] = array(
            'match' => array(
                'command' => 'MaskUser',
            )
        );

        $must[] = array(
            'term' => array(
                'monitor_type' => $monitorType,
            )
        );


        $arrReq = array(
            "query" => array(
                'bool' => array(
                    'must' => $must,
                )
            ),
            "size" => 0,
            "aggs" => array(
                "uid_aggs" => array(
                    "cardinality" => array(
                        "field" => "user_id"
                    )
                )
            ),

        );
        $pathinfo = self::UEGES_INDICE_PREFIX_DEAL_BLOCK . $date . '/_search';
        $arrRes = Tieba_Ral::call(self::UEGMONITORES_SERVICE, "post", $arrReq, rand(), array('Content-Type' => 'application/json',"pathinfo" => $pathinfo));


        if (!$arrRes) {
            Bingo_Log::warning(self::UEGMONITORES_SERVICE . ' get count fail! res: ' . serialize($arrRes) . ', req: ' . serialize($arrReq) .
                ', pathinfo: ' . $pathinfo);
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        } else {

            $result['count'] = $arrRes['hits']['total'];
            $result['count_dis_uid'] = $arrRes['aggregations']['uid_aggs']['value'];
            $error = Tieba_Errcode::ERR_SUCCESS;
        }

        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'ret' => $result);
        return $arrOutput;
    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function getMonitorMaskDeletecountNew($arrInput)
    {

        if (!isset($arrInput['monitor'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $monitorType = $arrInput['monitor'];
        $date = $arrInput['date'];
        $result = array(
            'count' => 0,
            'count_dis_pid' => 0,
        );

        $must[] = array(
            'match' => array(
                'command' => 'MaskDelete',
            )
        );

        $must[] = array(
            'term' => array(
                'monitor_type' => $monitorType,
            )
        );

        $must_pid0 = $must;
        $must_pid0[] = array(
            'match' => array(
                'post_id' => '0'
            )
        );

        $must_not = array(
            'match' => array(
                'post_id' => '0',
            )
        );

        //pid=0,对thread_id去重
        $arrReq = array(
            "query" => array(
                'bool' => array(
                    'must' => $must_pid0,
                )
            ),
            "size" => 0,
            "aggs" => array(
                "tid_aggs" => array(
                    "cardinality" => array(
                        "field" => "thread_id"
                    )
                )
            ),

        );
        $pathinfo = self::UEGES_INDICE_PREFIX_DEAL_POST . $date . '/_search';
        $arrRespid0 = Tieba_Ral::call(self::UEGMONITORES_SERVICE, "post", $arrReq, rand(), array('Content-Type' => 'application/json',"pathinfo" => $pathinfo));


        if (!$arrRespid0) {
            Bingo_Log::warning(self::UEGMONITORES_SERVICE . ' get count fail! res: ' . serialize($arrRespid0) . ', req: ' . serialize($arrReq) .
                ', pathinfo: ' . $pathinfo);
            $errorpid0 = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        } else {

            $result['count'] = $arrRespid0['hits']['total'];
            $result['count_dis_pid'] = $arrRespid0['aggregations']['tid_aggs']['value'];
            $errorpid0 = Tieba_Errcode::ERR_SUCCESS;
        }


        //pid!=0,对post_id去重
        $arrReq = array(
            "query" => array(
                'bool' => array(
                    'must' => $must,
                    'must_not' => $must_not,
                )
            ),
            "size" => 0,
            "aggs" => array(
                "pid_aggs" => array(
                    "cardinality" => array(
                        "field" => "post_id"
                    )
                )
            ),

        );
        $pathinfo = self::UEGES_INDICE_PREFIX_DEAL_POST . $date . '/_search';
        $arrRespidnot0 = Tieba_Ral::call(self::UEGMONITORES_SERVICE, "post", $arrReq, rand(), array('Content-Type' => 'application/json',"pathinfo" => $pathinfo));


        if (!$arrRespidnot0) {
            Bingo_Log::warning(self::UEGMONITORES_SERVICE . ' get count fail! res: ' . serialize($arrRespidnot0) . ', req: ' . serialize($arrReq) .
                ', pathinfo: ' . $pathinfo);
            $errorpidnot0 = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        } else {

            Bingo_Log::warning('=====result'.print_r($result,1));
            $result['count'] = $result['count'] + $arrRespidnot0['hits']['total'];
            $result['count_dis_pid'] = $result['count_dis_pid'] + $arrRespidnot0['aggregations']['pid_aggs']['value'];

            Bingo_Log::warning('=====result1111'.print_r($result,1));

            $errorpidnot0 = Tieba_Errcode::ERR_SUCCESS;
        }

        if ($errorpid0 == Tieba_Errcode::ERR_DB_QUERY_FAIL && $errorpidnot0 == Tieba_Errcode::ERR_DB_QUERY_FAIL) {
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        } else {
            $error = Tieba_Errcode::ERR_SUCCESS;
        }

        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'ret' => $result);
        return $arrOutput;

    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function getMultiMonitorDealandRecallcount($arrInput)
    {
        if (!isset($arrInput['monitors'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $monitorTypes = $arrInput['monitors'];
        if (sizeof($monitorTypes) >= 500) {
            Bingo_Log::warning("input params invalid,size to large");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $date = $arrInput['date'];

        foreach ($monitorTypes as $monitorType) {
            $must[] = array(
                'match' => array(
                    'command' => 'MaskUser',
                )
            );

            $must[] = array(
                'term' => array(
                    'monitor_type' => $monitorType,
                )
            );

            $arrReq = array(
                "query" => array(
                    'bool' => array(
                        'must' => $must
                    )
                )
            );
            $pathinfo = self::UEGES_INDICE_PREFIX_DEAL_BLOCK . $date . '/_count';
            $arrRes = Tieba_Ral::call(self::UEGMONITORES_SERVICE, "post", $arrReq, rand(), array('Content-Type' => 'application/json',"pathinfo" => $pathinfo));

            if (!$arrRes) {
                Bingo_Log::warning(self::UEGMONITORES_SERVICE . ' get count fail! res: ' . serialize($arrRes) . ', req: ' . serialize($arrReq) .
                    ', pathinfo: ' . $pathinfo);
                $intCount = 0;
                $errordeal = Tieba_Errcode::ERR_DB_QUERY_FAIL;
            } else {
                $intCount = $arrRes['count'];
                $errordeal = Tieba_Errcode::ERR_SUCCESS;
            }

            $ret[$monitorType]['deal'] = $intCount;


            $arrReq = array(
                "query" => array(
                    'term' => array(
                        'monitor_type' => $monitorType
                    )
                )
            );
            $pathinfo = self::UEGES_INDICE_PREFIX_RECALL . $date . '/recall/_count';


            $arrRes = Tieba_Ral::call(self::UEGMONITORES_SERVICE, "post", $arrReq, rand(), array('Content-Type' => 'application/json',"pathinfo" => $pathinfo));

            if (!$arrRes) {
                Bingo_Log::warning(self::UEGMONITORES_SERVICE . ' get count fail! res: ' . serialize($arrRes) . ', req: ' . serialize($arrReq) .
                    ', pathinfo: ' . $pathinfo);
                $intCount = 0;
                $errorrecall = Tieba_Errcode::ERR_DB_QUERY_FAIL;
            } else {
                $intCount = $arrRes['count'];
                $errorrecall = Tieba_Errcode::ERR_SUCCESS;
            }

            $ret[$monitorType]['recall'] = $intCount;


        }

        if ($errordeal == Tieba_Errcode::ERR_DB_QUERY_FAIL && $errorrecall == Tieba_Errcode::ERR_DB_QUERY_FAIL) {
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        } else {
            $error = Tieba_Errcode::ERR_SUCCESS;
        }

        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'ret' => $ret);
        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     */
    public static function getCoreUserFlag($arrInput)
    {
        if (!isset($arrInput['uid'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $uid = $arrInput['uid'];

        $userInfoInput = array(
            'user_id' => array($uid),
            'need_follow_info' => 1,
            'need_pass_info' => 1,
            'get_icon' => 1);

        $userInfoRes = self::getUserInfo($userInfoInput);

        //关注，粉丝
        $followed_count = $userInfoRes[$uid]['followed_count'];//粉丝数
        $follow_count = $userInfoRes[$uid]['follow_count'];//关注数

        //超级会员，会员过期时间
        $memberinfo_level = $userInfoRes[$uid]['mParr_props']['all_level'][2]['level'];
        $memberinfo_endtime = $userInfoRes[$uid]['mParr_props']['all_level'][2]['end_time'];

        //用户主题贴，回复数据，主题回复比
        $userPostRes = self::getUserPost($uid);

        $post_num = $userPostRes['post_num'];
        $thread_num = $userPostRes['thread_num'];
        $ip_count = $userPostRes['ip_count'];
        $thread_post_rate10 = $userPostRes['thread_post_rate10'];
        $delpost = $userPostRes['delpost'];

        $iscoreuser = 0;

        if ($delpost <= 10 && $thread_post_rate10 >= 20 && $ip_count <= 2) {
            $iscoreuser = 1;
        }

        if ($post_num >= 1000 && $delpost <= 10 && $ip_count <= 2) {
            $iscoreuser = 2;
        }

        if ($followed_count >= 100 && $thread_post_rate10 >= 20 && $ip_count <= 2) {
            $iscoreuser = 3;
        }

        $ret = array(
            'user_id' => $uid,
            'is_coreuser' => $iscoreuser,
        );

        $arrOutput = array(
            'errno' => 0,
            'errmsg' => Tieba_Error::getErrmsg(0),
            'ret' => $ret);
        return $arrOutput;


    }

    /**
     * @brief 得到用户扩展属性
     * @param array : $get :
     * @return array | $userInfo
     */
    public static function getUserinfo($get)
    {
        $arrOut = Tieba_Service::call('user', 'mgetUserDataEx', $get, null, null, 'post', 'php', 'utf-8');
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
            Bingo_Log::warning('call  mgetUserDataEx failed.input:[' . serialize($get) . '].output:[' . serialize($arrOut) . '].');
            throw new Util_Exception("service call fail!", Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        if (isset($arrOut['user_info'])) {
            $userInfo = $arrOut['user_info'];
        }
        return $userInfo;
    }


    /**
     * @brief 得到用户发贴信息
     * @param array : $get :
     * @return array | $userInfo
     */
    public static function getUserPost($uid)
    {

        $postInfo = array(
            'thread_num' => 0,
            'post_num' => 0,
            'good_num' => 0,
            'photo_num' => 0,


        );
        $input = array(
            'user_id' => $uid
        );


        // ****************主题贴和回复个数****************

        /**
         *
         * uint32_t  thread_num          //帖子数
         * uint32_t  post_num            //回复数
         * uint32_t  good_num            //精品数
         * uint32_t  photo_num           //照片数
         */
        $arrOut = Tieba_Service::call('post', 'queryUserCount', $input, null, null, 'post', 'php', 'utf-8');

        Bingo_Log::warning('========queryUserCount' . print_r($arrOut, 1));
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
            Bingo_Log::warning('call  queryUserCount failed.input:[' . serialize($input) . '].output:[' . serialize($arrOut) . '].');
            //throw new Util_Exception("service call fail!", Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        if (isset($arrOut['count'])) {
            $postInfo['thread_num'] = $arrOut['count']['thread_num'];
            $postInfo['post_num'] = $arrOut['count']['post_num'];
            $postInfo['good_num'] = $arrOut['count']['good_num'];
            $postInfo['photo_num'] = $arrOut['count']['photo_num'];

        }

// ****************主题+回复, 计算删帖，ip****************
        $postInfo['thread_deleted'] = 0;//删帖占比
        $postInfo['ip_count'] = 0;//ip计数
        $iparr = array();

        //贴子详情
        $input = array(
            'input' => array(
                'user_id' => $uid,
                'offset' => 0,
                'res_num' => 200,
                'order_type' => 1,
                'delete_type' => 0,//0 not deleted 1deleted 2all
                'is_thread' => 2,//1threads 2 posts
                "need_content" => 1,
                'ueg_ip' => 1,
            )
        );

        $arrOut = Tieba_Service::call('post', 'queryUserPost', $input, null, null, 'post', 'php', 'utf-8');

        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
            Bingo_Log::warning('call  queryUserPost failed.input:[' . serialize($input) . '].output:[' . serialize($arrOut) . '].');
            //throw new Util_Exception("service call fail!", Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        if (isset($arrOut['post'])) {
            $postInfolist = $arrOut['post']['post'];
            foreach ($postInfolist as $p) {

                $thread_id = $p['thread_id'];
                if ($p['is_post_deleted'] != 0) {
                    $postInfo['thread_deleted'] = $postInfo['thread_deleted'] + 1;//删帖占比
                }


                //将 ip 转为归属地
                $cwlInput = array('command_no' => 32000, 'user_ip' => $p['ip']);
                $cwl = new Bd_Rpc_Camel ('cwl');
                $address = $cwl->call($cwlInput);
                if ($address ['err_no'] == 0) {
                    $ipBelongs = iconv("GBK", "UTF-8", $address ['province']) . '·' . iconv("GBK", "UTF-8", $address ['city']);
                } else {
//                $ipBelongs = long2ip($cwlInput['user_ip']);
                    $ipBelongs = 0;
                }


                if (!in_array($ipBelongs, $iparr)) {

                    $iparr[] = $ipBelongs;
                    $postInfo['ip_count'] = $postInfo['ip_count'] + 1;
                }

            }
        }

        // ****************主题  的回复率****************
        $postInfo['thread_post_rate10'] = 0;

        //贴子详情
        $input = array(
            'input' => array(
                'user_id' => $uid,
                'offset' => 0,
                'res_num' => 200,
                'order_type' => 1,
                'delete_type' => 2,//0 not deleted 1deleted 2all
                'is_thread' => 1,//1threads 2 posts
                "need_content" => 1,
                'ueg_ip' => 1,
            )
        );

        $arrOut = Tieba_Service::call('post', 'queryUserPost', $input, null, null, 'post', 'php', 'utf-8');
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
            Bingo_Log::warning('call  queryUserPost failed.input:[' . serialize($input) . '].output:[' . serialize($arrOut) . '].');
            // throw new Util_Exception("service call fail!", Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        //贴子对应的回复数，贴子回复率

        if (isset($arrOut['post'])) {
            $postInfolist = $arrOut['post']['post'];
            foreach ($postInfolist as $p) {

                $thread_id = $p['thread_id'];
                $input = array(
                    "thread_id" => $thread_id, //帖子id
                    "offset" => 0,
                    "res_num" => 100,
                );

                /**
                 *    uint32_t  total_post_num      //总回复数    uint32_t  valid_post_num      //有效回复
                 */
                $arrOut = Tieba_Service::call('post', 'getPostList', $input, null, null, 'post', 'php', 'utf-8');

                if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
                    Bingo_Log::warning('call  queryUserPost failed.input:[' . serialize($input) . '].output:[' . serialize($arrOut) . '].');
                    // throw new Util_Exception("service call fail!", Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }

                if (isset($arrOut['output'])) {

                    $postInfolist = $arrOut['output'][0];
                    $valid_post_num = $postInfolist['valid_post_num'];//有效回复数

                    if ($valid_post_num > 10) {
                        $postInfo['thread_post_rate10'] = $postInfo['thread_post_rate10'] + 1;
                    }

                }
            }
        }


        //=============系统删帖量
        $postInfo['delpost'] = 0;

        //贴子详情

        $input = array(
            "user_id" => $uid, //用户id
            "source" => 400, //source
            "page" => 0, //页码
            "size" => 100, //返回结果数
        );


        $arrOut = Tieba_Service::call('post', 'getUserDelpostInfo', $input, null, null, 'post', 'php', 'utf-8');
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
            Bingo_Log::warning('call  getUserDelpostInfo failed.input:[' . serialize($input) . '].output:[' . serialize($arrOut) . '].');
            // throw new Util_Exception("service call fail!", Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        //贴子对应的回复数，贴子回复率

        if (isset($arrOut['output'])) {
            $delpostlist = $arrOut['output']['delpost_list'];

            $postInfo['delpost'] = sizeof($delpostlist);


        }

        return $postInfo;
    }


    /**
     * @param $arrInput
     * @return mixed
     */
    public static function getMonitorList($arrInput)
    {

        if (!isset($arrInput['ps']) || !isset($arrInput['pn'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $ps = $arrInput['ps'];
        $pn = $arrInput['pn'];

        $sql = "select monitor_type,title,description, manager, delete_method,if_dealUser,deal_switch,title,modify_time FROM spammis_monitor limit " . $pn . "," . $ps;


        $db = Util_Db::getDB(self::$DB_RAL_SERVICE_NAME_MONITOR);
        if (!$db) {
            Bingo_Log::warning("db connect failed");
            return false;
        }

        Bingo_Log::warning("execute sql: $sql");
        $ret = $db->query($sql);

        if ($ret === false) {
            Bingo_Log::warning("execute sql fail: $sql");
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        } else {
            $error = Tieba_Errcode::ERR_SUCCESS;
        }


        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'ret' => $ret,
        );
        return $arrOutput;
    }


    /**
     * @param $arrInput
     * @return mixed
     */
    public static function getMonitorListcount()
    {

        $monitorlistcount = 0;

        $sql = "select count(1) as con FROM spammis_monitor ";
        $db = Util_Db::getDB(self::$DB_RAL_SERVICE_NAME_MONITOR);
        if (!$db) {
            Bingo_Log::warning("db connect failed");
            return false;
        }
        Bingo_Log::warning("execute sql: $sql");
        $ret = $db->query($sql);

        if ($ret === false) {
            Bingo_Log::warning("execute sql fail: $sql");
            $error = Tieba_Errcode::ERR_DB_QUERY_FAIL;
        } else {
            $error = Tieba_Errcode::ERR_SUCCESS;
        }

        if (isset($ret[0]['con'])) {
            $monitorlistcount = $ret[0]['con'];
        }
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'ret' => $monitorlistcount,
        );
        return $arrOutput;
    }

}
