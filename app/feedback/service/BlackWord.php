<?php
/**
 * Created By sublime
 * User: wangyuanzhen
 * Date: 19/12/17
 * Time: 下午5:17
 */

class Service_BlackWord{

    const DB_SERVICE = 'DB_forum_feedbackzlw';
    const TABLE = 'black_words';
    const MAX_QUERY_SIZE = 2000;

    const NOT_PUSH  = 0;
    const CHECK_ADD_REFUSE = 1; # 添加进拒绝词表
    const CHECK_ADD_AUDIT = 2;  # 添加进推审词表
    const CHECK_NO_USEFUL = 3;  # 审核无效
    const PUSH_AND_NOT_AUDIT = 4; # 推审但是未人工审核
    /**
     * 获取db实例 function
     *
     * @param [type] $dnName
     * @return void
     */
    public static function _getDB(){
        $_db = new Bd_DB ();
        if ($_db == null) {
            Bingo_Log::warning(" get db failed : new bd_db fail.");
            return null;
        }
        Bingo_Timer::start('dbinit');
        $res = $_db->ralConnect(self::DB_SERVICE);
        Bingo_Timer::end('dbinit');
        if (!$res) {
            Bingo_Log::warning(" get db failed : bd db ral connect fail.");
            return null;
        }
        $_db->charset('utf8mb4');
        return $_db;
    }

     /**
     *  获取一段时间内的黑词
     *
     * @param $para array()
     * @param $start type int unix时间戳，时间区间的开始，非必须参数
     * @param $end   type int unix时间戳，时间区间的结束，非必须参数
     * @param $query_colum 可以指定返回的字段，默认全部返回，非必须参数
       仅当start和end同时指定才生效，不指定默认获取最近1小时的数据
       单次查询最多支持2000条记录，超2000请按照时间戳分多次查询
     * @return array
     */ 
    public static function queryWords($arrInput)
    {
        // 默认查询一个小时的数据
        $start = isset($arrInput['start'])?$arrInput['start']:time()-3600;
        $end = isset($arrInput['end'])?$arrInput['end']:time();
        $db = self::_getDB();
        if(null === $db){
            $errMsg = "get db failed!";
            Bingo_Log::warning($errMsg);
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        if ($end - $start > 24 * 3600){
            $count_sql = sprintf('select count(1) from black_words where create_time>=%d and create_time=<%d' ,$start, $end);
            // 先判断下查询结果是否大于单次查询上限
            $count = $db->query($count_sql);
            if(false === $count){
                $log_info = "query sql:(" . $count_sql . " )failed!";
                Bingo_Log::warning($log_info);
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            $query_count = $count[0]['count(1)'];
            if ($query_count > self::MAX_QUERY_SIZE){ // 大于上限返回错误
                return array(
                    'errno' => 611,
                    'errmsg' => 'query count '.$query_count.' more than 2000 please split query by time.'
                );
            }
        }
        $sql = sprintf('select thread_id, post_id, black_word, create_time, check_result from black_words where create_time>=%d and create_time<=%d', $start, $end);
        if (isset($arrInput['check'])){
            $sql .= ' and check_result='.$arrInput['check'];
        }
        $db->query('set names utf8mb4');
        $res = $db->query($sql);
        if(false === $res){
            $log_info = "query sql:(" . $sql . " )failed!";
            Bingo_Log::warning($log_info);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        # 拼装请求指定的字段，作为返回值
        if(isset($arrInput['query_colum'])){
            $colum = $arrInput['query_colum'];
            $result = array();
            foreach($res as $data){
                $tmp = array();
                foreach($colum as $key){
                    $tmp[$key] = $data[$key];
                }
                $result[] = $tmp;
            }
            return self::_succRet($result);
        }
        return self::_succRet($res);
    }

    # 更新黑词的审核结果，记录黑词是否通过审核
    public static function updateCheck($arrInput){
        if (!isset($arrInput['word']) || !isset($arrInput['check'])){
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $db = self::_getDB();
        if(null === $db){
            $errMsg = "get db failed!";
            Bingo_Log::warning($errMsg);
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $word = $arrInput['word'];
        $check = intval($arrInput['check']);
        $update = sprintf('update black_words set check_result=%d where black_word="%s"', $check, $word);
        $res = $db->query($update);
        if(false === $res){
            $log_info = "query sql:(" . $update . " )failed!";
            Bingo_Log::warning($log_info);
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }else{
            return self::_succRet($res);
        }
    }

    # 清理审核未通过的词,默认清除最近三小时审核无效的词
    public static function clearWords($arrInput){
        $db = self::_getDB();
        if(null === $db){
            $errMsg = "get db failed!";
            Bingo_Log::warning($errMsg);
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }  
        if(isset($arrInput['sql'])){
            $db->query('set names utf8mb4');
            $res = $db->query($arrInput['sql']);
            return $res;
        }
        if(isset($arrInput['check'])){
            $del = 'delete from black_words where check_result='.$arrInput['check'];
        }else{
            $del = 'delete from black_words where check_result='.self::CHECK_NO_USEFUL;
        }
        if(isset($arrInput['start']) && isset($arrInput['end'])){
            $start = $arrInput['start'];
            $end = $arrInput['end'];
        }else{
            $start = time() - 3 * 3600;
            $end = time();
        }
        $del .= ' and create_time>='.$start.' and create_time<='.$end;
        $db->query('set names utf8mb4');
        $res = $db->query($del);
        if(null === $db){
            $log_info = "query sql:(" . $del . " )failed!";
            Bingo_Log::warning($log_info);
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }else{
            return self::_succRet($res);
        }
    }

    /**
     * @param $errno
     * @param string $data
     * @return array
     */
    private static function _errRet($errno, $msg=null, $data=null){
        if (!$msg){
            $msg = Tieba_Error::getErrmsg($errno);
        }
        $ret = array(
            'errno' => $errno,
            'errmsg'    => $msg,
            'data'  => $data,
        );
        return $ret;
    }

    /**
     * @param $errno
     * @param string $data
     * @return array
     */
    private static function _succRet($data = '')
    {
        return array(
            'errno' => 0,
            'errmsg' => 'success',//Tieba_Error::getErrmsg(0),
            'data' => $data,
        );
    } 
}
// $black = new Service_BlackWord();
// $arrInput = array('word' => '半月谈党政机关刊物騲伲祃', 'check' =>3);
// $res = $black->updateCheck($arrInput);
// var_dump($res);