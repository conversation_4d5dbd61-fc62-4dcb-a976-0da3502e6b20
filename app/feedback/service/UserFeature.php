<?php
/**
 * Created by PhpStorm.
 * User: sunjing20
 * Date: 2018/5/12
 * Time: 17:50
 */

class Service_UserFeature
{

    protected static $_keyWorldOfCarrier = array(
        '北京市',
        '电信通',
        '中国移动',
        '中国联通',
        '中国电信',
        '中国铁通',
        '教育网',
        '中国科技网',
        '深圳天威视讯',
        '腾讯',
        '四川广电',
        '华数数字电视有限公司',
        '其他',
    );



    /**
     * @param $errno
     * @param string $data
     * @return array
     */
    private static function _errRet($errno, $data = '')
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        );
    }


    /**
     * @param $arrInput
     * @return array
     * @des 获取反馈数据数量
     */
    public static function getFeedbackCount($arrInput)
    {
        if (!isset($arrInput['begin_time']) || intval($arrInput['begin_time']) < 0) {
            $begin_time = time() - 3600 * 24;
        } else {
            $begin_time = intval($arrInput['begin_time']);
        }

        if (!isset($arrInput['end_time']) || intval($arrInput['end_time']) < 0) {
            $end_time = time();
        } else {
            $end_time = intval($arrInput['end_time']);
        }

        $arrParamCond = array();

        if (null === ($db = Util_Db::getDB())) {
            Bingo_Log::warning("get db fail.");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $str_cond = 'create_time >= ' . $begin_time . ' and create_time <= ' . $end_time;


        if (isset($arrInput['device'])) {
            $arrParamCond['device='] = intval($arrInput['device']);
        }
        if (isset($arrInput['category']) && $arrInput['category'] != "") {
            $arrParamCond['category='] = $arrInput['category'];
        }
        if (isset($arrInput['category_sub']) && $arrInput['category_sub'] != "") {
            $arrParamCond['category_sub='] = $arrInput['category_sub'];
        }
        if (isset($arrInput['status']) && $arrInput['status'] != "") {

            if (intval($arrInput['status']) < 255) {
                $arrParamCond['status='] = intval($arrInput['status']); //取指定反馈
            } else {
                $arrParamCond['status'] = array(0,1,10);//取所有有效反馈
            }
        } else {
            $arrParamCond['status'] = array(0,1);//10 为无效反馈状态 默认取所有有效反馈
        }
        if (isset($arrInput['version']) && $arrInput['version'] != "") {
            $arrParamCond['version='] = $arrInput['version'];
        }
        if (isset($arrInput['forum_id']) && $arrInput['forum_id'] != "") {
            $arrParamCond['forum_id='] = intval($arrInput['forum_id']);
        }

        if (isset($arrInput['content']) && $arrInput['content'] != "") {
            $arrParamCond['content like'] = '%' . $arrInput['content'] . '%';
        }

        if (isset($arrInput['title']) && $arrInput['title'] != "") {
            $arrParamCond['title like'] = '%' . $arrInput['title'] . '%';
        }

        if (isset($arrInput['fbCate']) && $arrInput['fbCate'] != "") {
            $arrParamCond['fbCate='] = intval($arrInput['fbCate']);
        }

        if(isset($arrInput['carrier']) && $arrInput['carrier'] != "") {
            $arrParamCond['carrier='] = $arrInput['carrier'];
        }

        if(isset($arrInput['location']) && $arrInput['location'] != "") {
            $arrParamCond['location='] = $arrInput['location'];
        }



        foreach ($arrParamCond as $key => $value) {
            if(is_array($value)){
                $str_cond .=  ' and ' . $key . ' in ('.implode(",",$value).')' ;
            }else{
                $str_cond .= ' and ' . $key . '\'' . $value . '\'';
            }

        }

        Bingo_Log::warning('sql:'.$str_cond);
        $dbTotalCount = $db->selectCount('feedback', $str_cond, null, null);

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $dbTotalCount,
        );
        return $arrOutput;
    }


    /**
     * @param $arrInput
     * @return array
     * @des 获取反馈用户运营商特征
     */
    public static function getAllFeedbackNumByCarrier($arrInput){
        if (!isset($arrInput['begin_time']) || !isset($arrInput['end_time']) || intval($arrInput['begin_time']) < 0 || intval($arrInput['end_time']) <= 0 || intval($arrInput['begin_time']) > intval($arrInput['end_time'])) {
            Bingo_Log::warning("input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $paramCond = array();
        $paramCond['begin_time'] = intval($arrInput['begin_time']);
        $paramCond['end_time'] = intval($arrInput['end_time']);


        if (isset($arrInput['forum_id'])) {
            $paramCond['forum_id'] = intval($arrInput['forum_id']);
        }
        if (isset($arrInput['device'])) {
            $paramCond['device'] = intval($arrInput['device']);
        }

        if (isset($arrInput['version'])) {
            $paramCond['version'] = $arrInput['version'];
        }
        if (isset($arrInput['fbCate'])) {
            $paramCond['fbCate'] = intval($arrInput['fbCate']);
        }
        if (isset($arrInput['category'])) {
            $paramCond['category'] = intval($arrInput['category']);
        }
        if (isset($arrInput['category_sub'])) {
            $paramCond['category_sub'] = intval($arrInput['category_sub']);
        }
        if (isset($arrInput['title'])) {
            $paramCond['title'] = intval($arrInput['title']);
        }

        $arrFeedbackNum = array();


        //一条sql取特征的所有子类 carrier:13 locations:309 version:609......心塞
        //然后循环取子类的count
        //其实这样很有问题，运营商少可以搞定，地理位置那么多，循环下来是搞事情


        foreach (self::$_keyWorldOfCarrier as $key) {
            $paramCond['carrier'] = $key;
            $arrOutputTmp = self::getFeedbackCount($paramCond);
            $arrFeedbackNum[$key] = intval($arrOutputTmp['data']);
        }
        if (isset($arrInput['is_sort']) && intval($arrInput['is_sort']) == 1) {
            arsort($arrFeedbackNum);
        }


        /*  if (isset($arrInput['category'])) {
              $paramCond['category'] = $arrInput['category'];
              if (isset($arrInput['category_sub'])) {
                  $paramCond['category_sub'] = $arrInput['category_sub'];
              }
              $arrOutputTmp = self::getFeedbackCount($paramCond);
              if (isset($arrInput['category_sub'])) {
                  $arrFeedbackNum[$arrInput['category_sub']] = intval($arrOutputTmp['data']);
              } else {
                  $arrFeedbackNum[$arrInput['category']] = intval($arrOutputTmp['data']);
              }
          } else {
              foreach (self::$_keyWorldOfCategory as $key => $value) {
                  $paramCond['category'] = $key;
                  $arrOutputTmp = self::getFeedbackCount($paramCond);
                  $arrFeedbackNum[$key] = intval($arrOutputTmp['data']);
              }
              if (isset($arrInput['is_sort']) && intval($arrInput['is_sort']) == 1) {
                  arsort($arrFeedbackNum);
              }
          } */

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $arrFeedbackNum,
        );
        return $arrOutput;
    }

    /**
     * @param $arrInput
     * @return array
     * @des 获取反馈用户特征
     */
    public static function getAllFeatureData($arrInput){
        //不进行时间判断，取当前时间半小时前的数据
        $begin_time = time() - 3600;
        $end_time = time() - 1800;
        $paramCond = array();
        //$paramCond['begin_time'] = $begin_time;
        //$paramCond['end_time'] = $end_time;


        if (isset($arrInput['forum_id'])) {
            $paramCond['forum_id'] = intval($arrInput['forum_id']);
        }
        if (isset($arrInput['device'])) {
            $paramCond['device'] = intval($arrInput['device']);
        }

        if (isset($arrInput['version'])) {
            $paramCond['version'] = $arrInput['version'];
        }
        if (isset($arrInput['fbCate'])) {
            $paramCond['fbCate'] = intval($arrInput['fbCate']);
        }
        if (isset($arrInput['category'])) {
            $paramCond['category'] = intval($arrInput['category']);
        }
        if (isset($arrInput['category_sub'])) {
            $paramCond['category_sub'] = intval($arrInput['category_sub']);
        }
        if (isset($arrInput['title'])) {
            $paramCond['title'] = intval($arrInput['title']);
        }

        $arrFeedbackNum = array();

        if (null === ($db = Util_Db::getDB())) {
            Bingo_Log::warning("get db fail.");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $str_cond = 'create_time >= ' . $begin_time . ' and create_time <= ' . $end_time;

        foreach ($paramCond as $key => $value) {
            if(is_array($value)){
                $str_cond .=  ' and ' . $key . ' in ('.implode(",",$value).')' ;
            }else{
                $str_cond .= ' and ' . $key . '\'' . $value . '\'';
            }

        }

        //获取运营商信息
        $str_sql = "select distinct carrier from feedback where $str_cond";
        $arrCarrier = $db->query($str_sql);

        //获取版本信息
        $str_sql = "select distinct version from feedback where $str_cond";
        $arrVersion = $db->query($str_sql);

        //获取地理位置信息
        $str_sql = "select distinct location from feedback where $str_cond";
        $arrLocation = $db->query($str_sql);

        $paramCond['begin_time'] = $begin_time;
        $paramCond['end_time'] = $end_time;


        $paramCondCarrier =array();
        $paramCondCarrier = $paramCond;
        foreach ($arrCarrier as $key => $value) {
            $paramCondCarrier['carrier'] = $value['carrier'];
            $arrOutputTmp = self::getFeedbackCount($paramCondCarrier);
            $arrFeedbackNum['carrier'][$value['carrier']] = intval($arrOutputTmp['data']);
        }


        $paramCondVersion =array();
        $paramCondVersion = $paramCond;
        foreach ($arrVersion as $key => $value) {
            $paramCondVersion['version'] = $value['version'];
            $arrOutputTmp = self::getFeedbackCount($paramCondVersion);
            $arrFeedbackNum['version'][$value['version']] = intval($arrOutputTmp['data']);
        }

        $paramCondLocation =array();
        $paramCondLocation = $paramCond;
        foreach ($arrLocation as $key => $value) {
            $paramCondLocation['location'] = $value['location'];
            $arrOutputTmp = self::getFeedbackCount($paramCondLocation);
            $arrFeedbackNum['location'][$value['location']] = intval($arrOutputTmp['data']);
        }

        if (isset($arrInput['is_sort']) && intval($arrInput['is_sort']) == 1) {
            arsort($arrFeedbackNum['carrier']);
            arsort($arrFeedbackNum['version']);
            arsort($arrFeedbackNum['location']);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $arrFeedbackNum,
        );
        return $arrOutput;

    }

}


