<?php
/**
 * Created by PhpStorm.
 * User: yuanshuang01
 * Date: 18/6/5
 * Time: 下午3:51
 */
class Service_LabelRecord{
	protected static $tbName = 'label_record';
	protected static $fields = array(
		'id','label_id', 'tids', 'day',
	);

	/**
	 * 获取LabelRecord
	 * @param $arrInput
	 * @return array
	 */
	public static function getLabelRecord($arrInput){

		$cond = array();
		if (isset($arrInput['id']) && 0 !== intval($arrInput['id'])) {
			$cond['id ='] = $arrInput['id'];
		}
		if (isset($arrInput['label_id'])) {
			if(is_array($arrInput['label_id'])){//支持label_id的数组查询
				$cond['label_id '] = $arrInput['label_id'];
			}else{
				$cond['label_id ='] = $arrInput['label_id'];
			}
		}
		if (!empty($arrInput['begin_time']) ) {
			$cond['day >='] = $arrInput['begin_time'];
		}
		if (!empty($arrInput['end_time']) ) {
			$cond['day <='] = $arrInput['end_time'];
		}
		if (isset($arrInput['num'])) {
			$append= 'limit '.$arrInput['num'];
		}
		$arrParams = array(
			'table' => self::$tbName,
			'field' => self::$fields,
			'cond' => $cond,
			'append' => $append,
		);
		$arrRet = Util_Db::select($arrParams);
		if ($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning("getLabelRecord error.[input=" . serialize($arrParams) . "][output=" . serialize($arrRet) . "]");
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}

		return $arrRet;
	}

	/**
	 * 更新LabelRecord，支持修改字段label_name、tids
	 * @param $arrInput
	 * @return array
	 */
	public static function updateLabelRecord($arrInput){
		//只支持根据id更新
		if (!isset($arrInput['id']) ){
			Bingo_Log::warning("updateLabelRecord input params invalid. [" . serialize($arrInput) . "]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$cond = array();
		if (!empty($arrInput['tids']) ) {
			$cond['tids ='] = $arrInput['tids'];
		}
		$sqlcond['id='] = $arrInput['id'];
		$arrParams = array(
			'table' => self::$tbName,
			'field' => $cond,
			'cond' => $sqlcond,
		);
		$arrRet = Util_Db::update($arrParams);
		if ($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning("updateLabelRecord error.[input=" . serialize($arrParams) . "][output=" . serialize($arrRet) . "]");
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
	}

	/**
	 * 新增LabelRecord记录
	 * @param $arrInput
	 * @return array
	 */
	public static function addLabelRecord($arrInput){
		$cond = array();
		if (isset($arrInput['label_id']) && 0 !== intval($arrInput['label_id'])) {
			$cond['label_id '] = $arrInput['label_id'];
		}
		if (!empty($arrInput['tids'])) {
			$cond['tids '] = $arrInput['tids'];
		}
		if (!empty($arrInput['day']) ) {
			$cond['day '] = $arrInput['day'];
		}

		$arrParams = array(
			'table' => self::$tbName,
			'field' => $cond,
		);
		$arrRet = Util_Db::insert($arrParams);
		if ($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning("addLabelRecord error.[input=" . serialize($arrParams) . "][output=" . serialize($arrRet) . "]");
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['data']);
	}
	/**
	 * 返回方法
	 * @param $errno
	 * @param string $data
	 * @return array
	 */
	private static function _errRet($errno, $data = '')
	{
		return array(
			'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
			'data' => $data,
		);
	}
}