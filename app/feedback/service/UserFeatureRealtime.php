<?php
/**
 * Created by PhpStorm.
 * User: sunjing20
 * Date: 2018/6/12
 * Time: 17:22
 */
class Service_UserFeatureRealtime
{

    protected static $_keyWorldOfCarrier = array(
        '北京市',
        '电信通',
        '中国移动',
        '中国联通',
        '中国电信',
        '中国铁通',
        '教育网',
        '中国科技网',
        '深圳天威视讯',
        '腾讯',
        '四川广电',
        '华数数字电视有限公司',
        '其他',
    );

    /**
     * @param $errno
     * @param string $data
     * @return array
     */
    private static function _errRet($errno, $data = '')
    {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        );
    }


    /**
     * @param $arrInput
     * @return array
     * @des 获取反馈数据数量
     */
    public static function getFeedbackCount($arrInput)
    {


        if (!isset($arrInput['begin_time']) || intval($arrInput['begin_time']) < 0) {
            $begin_time = time() - 3600 * 24;
        } else {
            $begin_time = intval($arrInput['begin_time']);
        }

        if (!isset($arrInput['end_time']) || intval($arrInput['end_time']) < 0) {
            $end_time = time();
        } else {
            $end_time = intval($arrInput['end_time']);
        }

        $arrParamCond = array();

        if (null === ($db = Util_Db::getDB())) {
            Bingo_Log::warning("get db fail.");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        $str_cond = 'create_time >= ' . $begin_time . ' and create_time <= ' . $end_time;

        //if (isset($arrInput['device']) && $arrInput['device'] != "")
        if (isset($arrInput['device'])) {
            $arrParamCond['device='] = intval($arrInput['device']);
        }
        if (isset($arrInput['category']) && $arrInput['category'] != "") {
            $arrParamCond['category='] = $arrInput['category'];
        }
        if (isset($arrInput['category_sub']) && $arrInput['category_sub'] != "") {
            $arrParamCond['category_sub='] = $arrInput['category_sub'];
        }
        if (isset($arrInput['status']) && $arrInput['status'] != "") {

            if (intval($arrInput['status']) < 255) {
                $arrParamCond['status='] = intval($arrInput['status']); //取指定反馈
            } else {
                $arrParamCond['status'] = array(0,1,10);//取所有有效反馈
            }
        } else {
            $arrParamCond['status'] = array(0,1);//10 为无效反馈状态 默认取所有有效反馈
        }
        if (isset($arrInput['version']) && $arrInput['version'] != "") {
            $arrParamCond['version='] = $arrInput['version'];
        }
        if (isset($arrInput['forum_id']) && $arrInput['forum_id'] != "") {
            $arrParamCond['forum_id='] = intval($arrInput['forum_id']);
        }

        if (isset($arrInput['content']) && $arrInput['content'] != "") {
            $arrParamCond['content like'] = '%' . $arrInput['content'] . '%';
        }

        if (isset($arrInput['title']) && $arrInput['title'] != "") {
            $arrParamCond['title like'] = '%' . $arrInput['title'] . '%';
        }

        if (isset($arrInput['fbCate']) && $arrInput['fbCate'] != "") {
            $arrParamCond['fbCate='] = intval($arrInput['fbCate']);
        }

        if(isset($arrInput['carrier']) && $arrInput['carrier'] != "") {
            $arrParamCond['carrier='] = $arrInput['carrier'];
        }

        if(isset($arrInput['location']) && $arrInput['location'] != "") {
            $arrParamCond['location='] = $arrInput['location'];
        }

        foreach ($arrParamCond as $key => $value) {
            if(is_array($value)){
                $str_cond .=  ' and ' . $key . ' in ('.implode(",",$value).')' ;
            }else{
                $str_cond .= ' and ' . $key . '\'' . $value . '\'';
            }

        }


        $dbTotalCount = $db->selectCount('feedback', $str_cond, null, null);

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $dbTotalCount,
        );

        return $arrOutput;
    }



/**
 * @param $arrInput
 * @return array
 * @des 获取反馈用户特征
 */

    public static function getAllFeatureDataRealtime($arrInput){

        if (!isset($arrInput['begin_time']) || intval($arrInput['begin_time']) < 0) {
            $begin_time = time() -3600 * 24; //取当前时间前24小时，支持24小时内的实时查询
        } else {
            $begin_time = intval($arrInput['begin_time']);
        }
        //   $begin_time = time() - 3600;
        $end_time = time() - 1800;


        $str_cond = 'status in (0,1,2)' . ' and create_time >= ' . $begin_time . ' and create_time <= ' . $end_time . ' and appname = " " ';

        if (isset($arrInput['forum_id'])) {
            $str_cond .= ' and forum_id = ' . intval($arrInput['forum_id']);
        }
        if (isset($arrInput['device'])) {
            $str_cond .= ' and device = ' . intval($arrInput['device']);
        }

        if (isset($arrInput['version'])) {
            $str_cond .= ' and version = ' . $arrInput['version'];
        }
        if (isset($arrInput['fbCate'])) {
            $str_cond .= " and fbCate = '" . intval($arrInput['fbCate']) . "'";
        }
        if (isset($arrInput['category'])) {
            $str_cond .= ' and category = "' . $arrInput['category'] . '"';
        }
        if (isset($arrInput['category_sub'])) {
            $str_cond .= ' and category_sub = "' . $arrInput['category_sub'] . '"';
        }

        if (isset($arrInput['title'])) {
            $str_cond .= ' and title = ' . $arrInput['title'];
        }



        $arrFeedbackNum = array();

        if (null === ($db = Util_Db::getDB())) {
            Bingo_Log::warning("get db fail.");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }


        //按条件取时间段内数据
        $str_sql = "select carrier from feedback where $str_cond";
        $arrCarrier = $db->query($str_sql);

        $str_sql = "select count(carrier) from feedback where $str_cond";
        $intCount = $db->query($str_sql);
        $intcount = $intCount[0]['count(carrier)'];
        $arrFeedbackNum['carrier']['total'] = $intcount;


        foreach ($arrCarrier as $key => $value) {
            if (!empty($value['carrier'])) {
                if(isset($arrFeedbackNum['carrier'][$value['carrier']])) {
                    $arrFeedbackNum['carrier'][$value['carrier']]++;
                } else {
                    $arrFeedbackNum['carrier'][$value['carrier']] = 1;
                }
            }
        }

        $str_sql = "select location from feedback where $str_cond";
        $arrLocation = $db->query($str_sql);

        $str_sql = "select count(location) from feedback where $str_cond";
        $intCount = $db->query($str_sql);
        $intcount = $intCount[0]['count(location)'];
        $arrFeedbackNum['location']['total'] = $intcount;

        foreach ($arrLocation as $key => $value) {
            if (!empty($value['location'])) {
                if(isset($arrFeedbackNum['location'][$value['location']])) {
                    $arrFeedbackNum['location'][$value['location']]++;
                } else {
                    $arrFeedbackNum['location'][$value['location']] = 1;
                }
            }
        }


        $str_sql = "select version from feedback where $str_cond";
        $arrVersion = $db->query($str_sql);

        $str_sql = "select count(version) from feedback where $str_cond";
        $intCount = $db->query($str_sql);
        $intcount = $intCount[0]['count(version)'];
        $arrFeedbackNum['version']['total'] = $intcount;

        foreach ($arrVersion as $key => $value) {
            if (!empty($value['version'])) {
                if(isset($arrFeedbackNum['version'][$value['version']])) {
                    $arrFeedbackNum['version'][$value['version']]++;
                } else {
                    $arrFeedbackNum['version'][$value['version']] = 1;
                }
            }
        }

        if (isset($arrInput['is_sort']) && intval($arrInput['is_sort']) == 1) {
            arsort($arrFeedbackNum['carrier']);
            arsort($arrFeedbackNum['version']);
            arsort($arrFeedbackNum['location']);
        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $arrFeedbackNum,
        );
        return $arrOutput;

    }


}

