<?php
/**
 * @file Alarm.php
 * <AUTHOR>
 * @date 2017/07/04 14:06:52
 * @brief
 **/
ini_set('memory_limit', '2048M');
require_once dirname(__FILE__)."/Smtp.class.php";

//获取前一天二级分类反馈数据
$nowTime=time()-1800;
$beforeTime=$nowTime-86400;
$endTime=$nowTime+1800;
$threshold=5;//jialin
$url="http://tieba.baidu.com/feedback/getCompareDataByCategorySub?topNum=0&nowTime=".$nowTime."&beforeTime=".$beforeTime."&duration=1800"."&threshold=".$threshold;
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_HEADER, 0);
$output = curl_exec($ch);
curl_close($ch);
$output_array = json_decode($output,true);
if(empty($output_array['data'])){
    echo 'no data';
    return;
}
echo "时间戳：".time()."\n";
var_dump($output_array);
echo "\n\n";
//$noCareCategpry=array();

$mailAddr = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>';
$phoneNum = "13581704559,15122062955,18511870651,13488762482,18211145706,15611811651,15210832135,18810916935,15189482772,15801375043,15797745644,13436587670,18232057692,15934077585,17600030820,13716986012,18901031667";

$whiteListCategory=array("图片上传","广告","@功能","其他举报","吧举报","贴子置顶","举报","贴子违规","贴子恢复","删贴投诉","解封","封禁","吧主恢复","投诉吧主","吧主撤销","吧务团队","吧主其他");
$speCategory=array("会员其他","首页推荐");
$imCategory=array("消息推送","回复","贴子点赞");
foreach($output_array['data']['topFeedback'] as $key=>$value){
    if($value['rate_CC_LC']>=3 && $value['DCur_TCur_Num']>=8 && $value['DCur_TCur_Num']>$value['Last24H_Avg_Num'] && ($value['rate_CC_CL']>=2 || ($value['rate_CC_CL']<2 && $value['rate_CL_LL']>=3))) {
        if(in_array($key,$whiteListCategory)){
            continue;
        }
        if(in_array($key,$speCategory) && $value['DCur_TCur_Num']<15){
            continue;
        }
        //获取对应一级分类
        $urlCatagory="http://tieba.baidu.com/feedback/getCategoryBySub?category_sub=".$key;
        $chCatagory = curl_init();
        curl_setopt($chCatagory, CURLOPT_URL, $urlCatagory);
        curl_setopt($chCatagory, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($chCatagory, CURLOPT_HEADER, 0);
        $outputCatagory = curl_exec($chCatagory);
        curl_close($chCatagory);
        $outputcatagory_array = json_decode($outputCatagory,true);

        //获取一级分类对应方向的报警接收人 add by sunjing20
        $urlDirection="http://nj.service.tieba.baidu.com/service/feedback?ie=utf-8&method=getAlarmDirection&format=json&category=" . $outputcatagory_array['data']['category'];
        $chDirection = curl_init();
        curl_setopt($chDirection, CURLOPT_URL, $urlDirection);
        curl_setopt($chDirection, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($chDirection, CURLOPT_HEADER, 0);
        $outputDirection = curl_exec($chDirection);
        curl_close($chDirection);
        $outputDirection_array = json_decode($outputDirection,true);

        $directionMail = $outputDirection_array['data']['mail'];
        $directionPhone = $outputDirection_array['data']['phone'];

        if(!empty($directionMail)) {

            $needMail = implode(',',$directionMail);
            $needMail .= ',' . $mailAddr;
            $arrNeedMail = explode(',',$needMail);
            $arrNeedMail =  array_unique($arrNeedMail);
            $toAddr = implode(',',$arrNeedMail);

        }
        else {

            $toAddr = $mailAddr;
        }
        if(!empty($directionPhone)) {
            $needPhone = implode(',',$directionPhone);
            $needPhone .= ',' . $phoneNum;
            $arrNeedPhone = explode(',',$needPhone);
            $arrNeedPhone =  array_unique($arrNeedPhone);
            $phoneReceiver = implode(',',$arrNeedPhone);

        }
        else {

            $phoneReceiver = $phoneNum;
        }

        //end 获取一级分类对应方向的报警接收人 add by sunjing20


        //邮件报警
        $arrParam = array();
        $alarmNum=$value['DCur_TCur_Num'];
        $tidUrl='';
        //获取具体帖子
        $url_tids="http://tieba.baidu.com/feedback/getFeedback?begin_time=".$nowTime."&end_time=".$endTime."&category_sub=".$key;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url_tids);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        $outTidsInfo = curl_exec($ch);
        curl_close($ch);
        $outTids=substr($outTidsInfo,13,strlen($outTidsInfo)-15);
        $outTidsArr = json_decode($outTids,true);
        /*foreach($outTidsArr['data'] as $threadInfo){
            $tidUrl.="https://tieba.baidu.com/p/".$threadInfo['thread_id']."\n";
        }*/
        $uids = array();
        $alarmOrNot = 0;
        $issueNO = 0;
        foreach($outTidsArr['data'] as $threadInfo){

            //相同用户名报警去重（遗留问题：上一时段未去重，即比较的数据没有去重）
            if (in_array($threadInfo['user_id'],$uids)){
                $value['DCur_TCur_Num']--;
                if ($value['DCur_TCur_Num']<6 || $value['DCur_TCur_Num']<=$value['Last24H_Avg_Num']){
                    $alarmOrNot = 1;
                    break;
                }
            } else {
                $issueNO ++;
                array_push($uids,$threadInfo['user_id']);
                $threadIds .= $threadInfo['id'] . ',';//获取主键id，ufo反馈id入库

                //贴吧帖子拼连接，过滤掉ufo的反馈ID add by @sunjing20
                $len = mb_strlen($threadInfo['thread_id'],"utf-8");
                if ($len === 10) {
                    $tidUrl.=$issueNO.": https://tieba.baidu.com/p/".$threadInfo['thread_id']."\n".$threadInfo['title']."<br>\n";
                }
                else {
                    $tidUrl.=$issueNO.":" . $threadInfo['title']."<br>\n";
                }
                //贴吧帖子拼连接，过滤掉ufo的反馈ID END
            }
            //  $issueNO ++;
        }
        if ($alarmOrNot == 1){
            continue;
        }

        $appendInfo1='[ABTEST][反馈平台分钟级别报警]二级问题' . $key . '(对应一级问题'.$outputcatagory_array['data']['category'].')当前半小时有效反馈量比前一天同一时间段增加'.round($value['rate_CC_LC']*100,2).'%,当前半小时数量为'.$issueNO;
        $appendInfo2=',昨天同时段值为'.intval($value['DLast_TCur_Num'])."\n".',当前半小时较前一时段时增加'.round($value['rate_CC_CL']*100,2).'%,今天前一个时段值为'.$value['DCur_TLast_Num'].',昨天前一个时段值为'.$value['DLast_TLast_Num'].',前24个小时均值为'.$value['Last24H_Avg_Num'];
        $arrParam['toAddr'] = $toAddr;//'<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>';
        $arrParam['content'] = $appendInfo1.$appendInfo2."\n,反馈帖子链接为<br>".$tidUrl."\n, 详细反馈信息进一步查看:".$url;
        $arrParam['title'] = '[ABTEST|tag=3(宽策略)][反馈平台分钟级别报警]二级问题'.$key.'(对应一级问题'.$outputcatagory_array['data']['category'].')当前半小时有效反馈量比前一天同一时间段增加
'.round($value['rate_CC_LC']*100,2).'%,总数量为'.$issueNO;
        if($value['DCur_TCur_Num']>=16)
        {
            $arrParam['title'] = '[ABTEST|tag=4(严策略)][反馈平台分钟级别报警]二级问题'.$key.'(对应一级问题'.$outputcatagory_array['data']['category'].')当前半小时有效反馈量比前一天同一时间段增加'.round($value['rate_CC_LC']*100,2).'%,总数量为'.$issueNO;
        }
        //im类反馈notify to arch rd
        /*if(in_array($key,$imCategory)){
        $arrParam['toAddr'].=',<EMAIL>';
        }
          */

        $start = time() - 3600;
        $end   = time();

        $url_historyNum = 'http://nj.service.tieba.baidu.com/service/feedback?format=json&ie=utf-8&method=getAlarmHistoryCount&table_name=alarm_his&receiv_type=0&begin_time=' . $start .'&end_time=' . $end . '&category=' . $key;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url_historyNum);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        $output = curl_exec($ch);
        curl_close($ch);
        $output_historyNum = json_decode($output, true);
        if ($output['errno'] != 0) {
            echo 'get alarm history count fail';
        }

        $count = (int)$output_historyNum['data'][0]['count'];


        echo "~~~~~~~~~~~~~~count~~~~~~~~~~~~~~~\n";
        echo $count;
        echo "\n";

        if($count ===0) {

            $resEmailSend = sendEmail($arrParam);

            //记录报警历史
            $item=array();
            $item['id']=1;
            $item['name']='二级问题报警Min';
            $item['num']=$value['DCur_TCur_Num'];
            $item['frequency']=1440;
            $item['category']=$key;
            $item['receiver']=$arrParam['toAddr'];
            $item['receiv_type']=0;
            $item['receiv_path']='mail';
            $item['alarm_type']=1;
            $item['alarm_threshold']=floatval($value['rate_CC_LC']*100);
            $item['alarm_time'] = time();
            $ch = curl_init();
            $url = 'http://tieba.baidu.com/feedback/submit/addAlarmHis?format=json&id=' . $item['id'] . '&name=' . $item['name'] . '&num=' . $item['num'] . '&device=5&frequency=' . $item['frequency'] . '&category=' . $item['category'] . '&receiver=' . $item['receiver'] . '&receiv_type=' . $item['receiv_type'] . '&receiv_path=' . $item['receiv_path'] . '&alarm_type=' . $item['alarm_type'] . '&alarm_threshold=' . $item['alarm_threshold'] . '&alarm_time=' . $item['alarm_time'] . '&details=' . $threadIds;
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_HEADER, 0);
            $output = curl_exec($ch);
            curl_close($ch);
            $output_array = json_decode($output, true);
            if ($output_array['errno'] != 0) {
                echo 'add alarmHis fail';
            }


            //短信报警
            // $phoneReceiver="13716986012,13581704559,15122062955,18511870651,13488762482,18211145706,15611811651,15210832135,18810916935";
            $content="反馈所属分类【".$outputcatagory_array['data']['category']."-".$key."】,具体反馈量如下——最近半小时:".$value['DCur_TCur_Num']."(昨天同时段:".$value['DLast_TCur_Num'].");今天上上个半小时:".$value['DCur_TLast_Num']."(昨天同时段:".$value['DLast_TLast_Num']."),最近24个小时平均每半小时反馈数:".$value['Last24H_Avg_Num'];
            if(in_array($key,$imCategory)){
                $phoneReceiver.=',13520765177';
            }

            echo "----------------PhoneNum-------------\n";
            var_dump($phoneReceiver);
            echo "----------------END---------------\n";

            //电话号码一次给的太对了，导致短信无法发出去
            //这里进行一下分割，观察一下是否可以发出短信 add by sunjing20
            $arrPhoneReceiver = explode(",",$phoneReceiver);
            $count = 0;
            $arrSend = array();

            foreach($arrPhoneReceiver as $phone) {
                $arrSend[] = $phone;
                $count++;
                while ($count===8) {
                    $strSend = implode(",",$arrSend);
                    //   echo "----------------SendPhone-------------\n";
                    //     var_dump($strSend);
                    //    echo "----------------END---------------\n";
                    sendPhone($strSend,$content);
                    $count = 0;
                    $arrSend = array();
                }
            }
            if(!empty($arrSend)) {
                $strSend = implode(",",$arrSend);
                //  echo "----------------SendPhone-------------\n";
                //   var_dump($strSend);
                //   echo "----------------END---------------\n";
                sendPhone($strSend,$content);
            }
            //end 分割手机号字符串 add by sunjing20
            $threadIds = ""; // 解决join 多个分类的帖子结合在一起
        }
    }

}

/**
 * @param $arrInput
 */
function sendEmail($arrInput){
    $title = $arrInput['title'];
    $mailserver = 'mail2-in.baidu.com';
    $to_addr = $arrInput['toAddr'];
    $from_name = 'feedback';
    $to_name = 'touser';
    $content = $arrInput['content'];
    $from_mail = '<EMAIL>';
    $email_title  = iconv('utf-8','gbk',$title);

    $objSmtp = new Smtp($mailserver);
    $bolRes = $objSmtp->send ($from_name,$to_name,$from_mail,
        $to_addr,$email_title,false,$content,false);
}

/**
 * @param $mobiles
 * @param $content
 * @param string $sub_code
 * @return int
 */
function sendPhone($mobiles,$content,$sub_code= ''){
    $url = 'http://emsg.baidu.com/service/sendSms.json';
    $username= "hanxuena";
    $pppw= "hanxuena";
    $msgDest=$mobiles;
    $msgContent=$content;
    $businessCode="game";
    $signature=md5($username.$pppw.$msgDest.$msgContent.$businessCode.$sub_code);
    //echo 'signature='.$signature."\n";
    $curlPost='username='.$username.'&businessCode='.$businessCode.'&msgContent='.urlencode($msgContent).'&msgDest='.$msgDest.'&signature='.$signature;
    if ($sub_code !== ''){
        $curlPost .= '&extId='.$sub_code;
    }
    //echo "curlPost=$curlPost\n";

    $ch = curl_init();
    $this_header = array(
        "content-type: application/x-www-form-urlencoded; charset=UTF-8",
    );

    curl_setopt($ch, CURLOPT_HTTPHEADER,$this_header);
    curl_setopt($ch, CURLOPT_URL, $url);
    //        curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $curlPost);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT_MS, 5000);// 1s瓒[36;98H
    curl_setopt($ch, CURLOPT_TIMEOUT_MS, 5000); // 1s瓒[36;98H
//        curl_setopt($ch, CURLOPT_VERBOSE, true);

    $data = curl_exec($ch);
    curl_close($ch);
    if($data) {
        $result = json_decode($data,true);

        if($result['result'] == 1000) {
            return 1;
        }
        else {
            return 0;
        }
    }
    return 0;

}
