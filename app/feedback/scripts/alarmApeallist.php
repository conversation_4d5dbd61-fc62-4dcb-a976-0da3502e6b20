<?php
/**
 * Created by PhpStorm.
 * User: sunjing20
 * Date: 2018/6/29
 * Time: 16:19
 * breif: apeal list alarm / hour
 */

require("../lib/AipHttpClient.php");
require ("../util/Hi.php");

ini_set("memory_limit", "-1");
define('MODULE_NAME', 'feedback');
date_default_timezone_set("Asia/Chongqing");
define('APP_NAME', 'feedback');
define('SCRIPT_NAME', 'alarmApeallist');
define('ROOT_PATH', dirname(__FILE__) . '/../../..');
define('SCRIPT_ROOT_PATH', ROOT_PATH . '/app/' . APP_NAME . '/scripts');
define('SCRIPT_LOG_PATH', ROOT_PATH . '/log/app/' . APP_NAME);
//define('SCRIPT_CONF_PATH', ROOT_PATH . '/conf/app/' . APP_NAME);
define('BASE_PATH', dirname(__FILE__));
define('IS_ORP_RUNTIME', true);
set_time_limit(0);

Tieba_Init::init(MODULE_NAME);

Bingo_Log::init(array(
    LOG => array(
        'file' => dirname(__FILE__) . '/../../../log/app/feedback/' . SCRIPT_NAME . '.log',
        'level' => 0x04 | 0x02,
    ),
), LOG);

if (function_exists('camel_set_logid')) {
    camel_set_logid(REQUEST_ID);
}

if (!defined('REQUEST_ID')) {
    $requestTime = gettimeofday();
    define('REQUEST_ID', (intval($requestTime ['sec'] * 100000 + $requestTime ['usec'] / 10) & 0x7FFFFFFF));
}

if (function_exists('camel_set_logid')) {
    camel_set_logid(REQUEST_ID);
}


$pushProcess = new AlarmApeallist();
$pushProcess->execute();

echo "\ndone\n";

class AlarmApeallist
{

    //提交的icafe空间
    private static $icafeSpace = 'tiebaueg';

    //报警阈值
    const THRESHOLD = 30;
    const THRESHOLD_COREUSER = 10;
    const THRESHOLD_P0 = 100;
    const THRESHOLD_ERROR_P0 = 60;

    //满足策略报警使申诉量的下限
    const MAXCOUNT = 100;
    const MAXCOUNT_P0 = 500;


    //报警级别界定
    const P3 = 3; //默认P3级别
    const P2 = 2; //邮件报警
    const P1 = 1; //短信+邮件
    const P0 = 0; //短信+邮件

    // 默认邮件内容
    private static $strMailContent = "";

    //提交icafe负责人
    private static $strOwner = "v_zhangyingyue01,v_liuwenbin,v_wanyu";

    // 收件人
    private static $previewToMail = array(
        '<EMAIL>',
        "<EMAIL>",
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    );

    //短信接收人
    //private static $previewToMobile = "15010961907,18131414384,15189482772,18908219993,13522252040,15801375043,18232057692,17600030820,15797745644,15934077585,17610600169,15600640808,13146739291";

    //高优问题新增短信接收人
    //private static $addToMobile = "13488762482,15122062955,15120078955,13488749170,18810916935,18901031667,13716986012,18611436135,18813199855,18260031158,18511447517";

    //<EMAIL> 需求更改报警人
    private static $previewToMobile = "13436587670,18232057692,13311423860,17600601504,17600030820,18901031667";
    private static $addToMobile = "13436587670,18232057692,13311423860,17600601504,17600030820,18901031667";

    private static $apealLink = 'http://amis.baidu.com/group/tbfeedback/userApeal/userApeal';


    /**
     *执行操作
     * @return bool
     **/
    public function execute()
    {


        $this->AlarmJudgment();


        return true;
    }


    /**
     * @breif:判断当前一小时内申诉同比昨日时段是否超出阈值，超出则报警
     * @input:null
     * @return:(array) $ret['status']=1:超出阈值，发送报警邮件,
     *                 今日时段申诉量$ret['today_amount'],昨日同时段申诉量$ret['yesterday_amount'];
     *                 $ret['status']=0：未超出阈值，不发报警邮件;
     **/
    public function AlarmJudgment()
    {

        //当天当前时间段
        $begin_Time = time() - 3600;
        $end_Time = time();
        $today = strtotime(date("Y-m-d"), time());
        $toMail = implode(',', self::$previewToMail);
        $toMobile = self::$previewToMobile .",". self::$addToMobile;

        $apealAlarmUser=self::getApealAlarmUser(); //增加从词表获取
        if ($apealAlarmUser !==false ){
            if (isset($apealAlarmUser['alarm_apealmail']) && $apealAlarmUser['alarm_apealmail']!=""){
                $toMailorign=$apealAlarmUser['alarm_apealmail'];
                $toMail="";
                $mails=explode(',',$toMailorign);//邮箱增加后缀
                foreach ($mails as $mail){
                    $toMail=$toMail.$mail.'@baidu.com,';
                }
                $toMail=trim($toMail,',');
            }

            if (isset($apealAlarmUser['alarm_apealmobile']) && $apealAlarmUser['alarm_apealmobile'] !=""){
                $toMobile=$apealAlarmUser['alarm_apealmobile'];
            }
        }


        //昨日同时段
        $Yesterday_begin = $begin_Time - 86400;
        $Yesterday_end = time() - 86400;
        $yesterday = $today - 86400;

        $Today_amount = $this->GetApealCount($begin_Time, $end_Time);
        $Yesterday_amount = $this->GetApealCount($Yesterday_begin, $Yesterday_end);
        //获取不到数据的异常输出
        if ($Today_amount === 1101100011 || $Yesterday_amount === 1101100011) {

            echo "daily apeal alarm error!!! service call fail!";

            return true;
        }


        $level=-1;
        
        if ($Yesterday_amount > 0) {
            $ratio = ($Today_amount - $Yesterday_amount) / $Yesterday_amount;
            $ratio = 100 * round($ratio, 2);
        } elseif ($Today_amount > 0) { //昨天没有数据
            $ratio = 100;
        } else {
            $ratio = 0;//昨天和今天都没有数据
        }


        if (($ratio < 0 && abs($ratio) >= self::THRESHOLD_ERROR_P0) || $Today_amount == 0) {

            $level = self::P0;  //一小时内总流入量比上个小时下降量60%以上 ||一小时内流入量=0 ——P0——邮件+短信（申诉功能异常）
        }

        //当日申诉增量不小于定义的阈值，判定需要发邮件
        if ($ratio >= self::THRESHOLD) {
            $level = self::P2;//默认P2  一小时内总流入量大于100&同比上升30%——P2——邮件；
            if ($ratio >= self::THRESHOLD_P0) {
                $level = self::P0;   // 一小时内总流入量大于100&同比上升100%——P0——邮件+短信
            }
        }

        //所有问题进行邮件报警
        if ($level >= self::P0) {

            $arrParam = array(
                'table_name' => 'alarm_his',
                'begin_time' => time() - 3600,
                'end_time' => time(),
                'receiv_type' => 0,
                'category' => 'apeal_user',
            );

            //获取报警记录 --- 邮件去重
            $trycount = 3;
            for ($i = 0; $i < $trycount; $i++) {
                //获取报警记录 --- 邮件去重
                $arrRet = Tieba_Service::call('feedback', 'getAlarmHistoryCount', $arrParam, null, null, 'post', 'php', 'utf-8');
                if ($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning("getAlarmHistoryCount error.[input=" . serialize($arrParam) . "][output=" . serialize($arrRet) . "]");
                    continue;
                    //  return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                }
                break;
            }


            $intCount = (int)$arrRet['data'][0]['count'];

            if ($intCount === 0) {
                $arrInput['title'] = date('Y-m-d h:i:s') . "用户申诉情况";
                $arrInput['content'] = '用户申诉总流入量小时级别报警：' . "\n" . '今日' . date('Y-m-d H:i:s', $begin_Time) . ' 至 ' . date('Y-m-d H:i:s') . '申诉量：' . $Today_amount;
                $arrInput['content'] .= "\n" . '昨日同时段申诉量：' . $Yesterday_amount;
                $arrInput['content'] .= "\n" . '同比上升：' . $ratio . "%";
                $arrInput['content'] .= "\n" . '详情请访问链接：' . self::$apealLink;
                $this->sendMail($arrInput);

                //申诉日流入量 --- 邮件记录
                $arrData = array(
                    'name' => 'apeal_user',
                    'id' => 3,
                    'device' => 5,
                    'frequency' => 1440,
                    'category' => 'apeal_user',
                    'receiver' => $toMail,
                    'receiv_type' => 0, //0邮件，1短信
                    'receiv_path' => 'mail',
                    'alarm_type' => 1, //1同比，0固定阈值
                    'level' => $level,
                    'alarm_threshold' => self::THRESHOLD,
                    'num' => $Today_amount,
                    'details' => $arrInput['content'],
                    'alarm_time' => time()
                );

                $arrOut = Tieba_Service::call('feedback', 'addAlarmHis', $arrData, null, null, 'post', 'php', 'utf-8');
                if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
                    Bingo_Log::warning('call  processFeedback failed.input:[' . serialize($arrData) . '].output:[' . serialize($arrOut) . '].');
                }

            }
        }

        //level P0 进行短信报警
        if ($level === self::P0) {
            $arrParam = array(
                'table_name' => 'alarm_his',
                'begin_time' => time() - 3600,
                'end_time' => time(),
                'receiv_type' => 1,
                'category' => 'apeal_user',
            );


            //获取报警记录 --- 短信去重,重试3次
            $trycount = 3;
            for ($i = 0; $i < $trycount; $i++) {
                //获取报警记录 --- 短信去重
                $arrRet = Tieba_Service::call('feedback', 'getAlarmHistoryCount', $arrParam, null, null, 'post', 'php', 'utf-8');

                if ($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning("getAlarmHistoryCount error.[input=" . serialize($arrParam) . "][output=" . serialize($arrRet) . "]");
                    // return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                    continue;
                }
                break;
            }


            $intCount = (int)$arrRet['data'][0]['count'];

            if ($intCount === 0) {

                $arrInput_mobile['content'] = '用户申诉总流入量小时级别报警：' . "\n" . date('Y-m-d-H') . '时申诉量：' . $Today_amount;
                $arrInput_mobile['content'] .= "\n" . '昨日同时段申诉量：' . $Yesterday_amount;
                $arrInput_mobile['content'] .= "\n" . '同比上升：' . $ratio . "%";
                $this->sendPhonemulti($toMobile, $arrInput_mobile['content']);


                //申诉日流入量 --- 短信记录
                $arrData = array(
                    'name' => 'apeal_user',
                    'id' => 3,
                    'device' => 5,
                    'frequency' => 1440,
                    'category' => 'apeal_user',
                    'receiver' => $toMobile,
                    'receiv_type' => 1, //0邮件，1短信
                    'receiv_path' => 'message',
                    'alarm_type' => 1, //1同比，0固定阈值
                    'level' => $level,
                    'alarm_threshold' => self::THRESHOLD,
                    'num' => $Today_amount,
                    'details' => $arrInput_mobile['content'],
                    'alarm_time' => time()
                );

                $arrOut = Tieba_Service::call('feedback', 'addAlarmHis', $arrData, null, null, 'post', 'php', 'utf-8');
                if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
                    Bingo_Log::warning('call  processFeedback failed.input:[' . serialize($arrData) . '].output:[' . serialize($arrOut) . '].');
                }

                $alarmid=$arrOut['data'];
                $url_useful='http://tieba.baidu.com/feedback/submit/feedback/processAlarm?ie=utf-8&misinformation=1&online=1&process_status=2&id=' . $alarmid;// misinformation=1 为有效报警
                $url_useless='http://tieba.baidu.com/feedback/submit/feedback/processAlarm?ie=utf-8&misinformation=-1&online=-1&process_status=1&id=' . $alarmid;// misinformation=-1 为误报报警
                $url_monitor='http://amis.baidu.com/group/tbfeedback/userApeal/userApeal';

                $arrInput_mobile['content'] .="\n有效报警请点击:".$url_useful;
                $arrInput_mobile['content'] .="\n无效报警请点击:".$url_useless;
                $arrInput_mobile['content'] .="\n用户申诉详情查看:".$url_monitor;


                $msginput=array(
                    'msg'=>$arrInput_mobile['content'],
                    'to_group'=>1686238,// 测试1686213
                    'instance'=>'yixiu_robot_02'
                );
                Util_Hi::sendHiMsg($msginput);

            }

        }

        //=============策略报警
        $Today_Monitor = $this->GetMonitorCount($today, $end_Time);
        $yesterday_Monitor = $this->GetMonitorCount($yesterday, $Yesterday_end);



        //获取不到数据的异常输出
        if (empty($Today_Monitor)||empty($yesterday_Monitor)) {
            echo "monitor alarm error!!! service call fail!";
            return true;
        }


        foreach ($Today_Monitor as $item => $val) {

            $level = -1; // 默认-1 不报警
            //策略误报率 报警策略新加误报率的阈值限定

            if ($item=='90068' || $item=='90099'){
                continue;
            }

            $wrongRate = $this->getMonitorWrongRate($item);

            $intWrongRate=$wrongRate['intWrongRate'];
            $intCoreUserRate=$wrongRate['intCoreUserRate'];


            //新增策略的处理
            if (empty($yesterday_Monitor[$item]['apeal_count']) || $yesterday_Monitor[$item]['apeal_count'] === 0) {
                $yesterday_Monitor[$item]['apeal_count'] = 0;
                $ratio_strategy = $val['apeal_count'] - $yesterday_Monitor[$item]['apeal_count'] ;
                $ratio_strategy = 100 * round($ratio_strategy, 2);

            }else{
                $ratio_strategy = ($val['apeal_count'] - $yesterday_Monitor[$item]['apeal_count']) / $yesterday_Monitor[$item]['apeal_count'];
                $ratio_strategy = 100 * round($ratio_strategy, 2);
            }


            if ($val['apeal_count'] >= self::MAXCOUNT_P0 && $ratio_strategy >= self::THRESHOLD && !empty($item)) {
                $level = self::P0;//策略流入量大于500 && 同比上升30% — P0 + 短信 + 短信
            } elseif ($val['apeal_count'] >= self::MAXCOUNT && !empty($item)) {
                if ($ratio_strategy >= self::THRESHOLD) {
                    $level = self::P2; //策略误伤率低于30% && 流入量大于100 && 同比上升30% — P2 + 邮件 
                }

                if ($ratio_strategy >= self::THRESHOLD_P0) {
                    $level = self::P0; //策略误伤率低于30% && 流入量大于100 && 同比上升100% — P0 短信+ 邮件 
                }

                if ($intWrongRate >= self::THRESHOLD || $intCoreUserRate>=self::THRESHOLD_COREUSER) {
                    $level = self::P0;//策略流入量大于100 && 误伤率达到30% — P0 + 短信 + 邮件
                }
            }


            echo "\n=========== monitor:".$item.'======level'.$level;

            if ($level >= self::P0) {
                //一天只报一次
                $today = strtotime(date("Y-m-d"), time());

                $arrParam = array(
                    'table_name' => 'alarm_his',
                    'begin_time' => $today,
                    'end_time' => time(),
                    'receiv_type' => 0,
                    'category' => 'apeal_monitor' . $item,
                );

                //获取报警记录 --- 邮件去重
                $trycount = 3;
                for ($i = 0; $i < $trycount; $i++) {
                    //获取报警记录 --- 短信去重
                    $arrRet = Tieba_Service::call('feedback', 'getAlarmHistoryCount', $arrParam, null, null, 'post', 'php', 'utf-8');
                    if ($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                        Bingo_Log::warning("getAlarmHistoryCount error.[input=" . serialize($arrParam) . "][output=" . serialize($arrRet) . "]");
                        continue;
                        //  return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                    }
                    break;
                }

                $intCount = (int)$arrRet['data'][0]['count'];

                if ($intCount === 0) {
                    //获取策略详细信息 --- icafe以及邮件内容中使用
                    $postDetail = $this->getMonitorDetail($item);

                    $arrInput['title'] = date('Y-m-d-H:i:s') . "策略" . $item . "用户申诉情况";
                    $arrInput['content'] = '用户申诉策略日累积量报警：' . "\n" . '策略' . $item . '今日累计申诉量：' . $val['apeal_count'] . "\n";
                    $arrInput['content'] .= "\n" . '昨日同时段内策略累计申诉量：' . $yesterday_Monitor[$item]['apeal_count'] . "\n";
                    $arrInput['content'] .= "\n" . '同比上升：' . $ratio_strategy . "%" . "\n";
                    $arrInput['content'] .= "\n" . '报警时人工标示误伤率：' .$intWrongRate . "%" . "\n";
                    $arrInput['content'] .= "\n" . '报警时自动识别误伤率：' . $intCoreUserRate . "%" . "\n";
                    $arrInput['content'] .= "\n" . $postDetail['monitor_type'] . '策略详情：';
                    $arrInput['content'] .= "\n" . '策略名称 - ' . $postDetail['name'];
                    $arrInput['content'] .= "\n" . '策略上线时间 - ' . $postDetail['online_time'];
                    $arrInput['content'] .= "\n" . '策略上线人 - ' . $postDetail['online_uname'];
                    $arrInput['content'] .= "\n" . '最近修改时间 - ' . $postDetail['modify_time'];
                    $arrInput['content'] .= "\n" . '最近修改人 - ' . $postDetail['modify_uname'];
                    $arrInput['content'] .= "\n" . '策略版本id - ' . $postDetail['version_id'];
                    $arrInput['content'] .= "\n" . '策略标识filter_id - ' . $postDetail['filter_id'];
                    $arrInput['content'] .= "\n" . '策略上线状态 - ' . $postDetail['status_online'];
                    $arrInput['content'] .= "\n" . '策略评估状态 - ' . $postDetail['status_est'];
                    $arrInput['content'] .= "\n" . '策略优先级 - ' . $postDetail['priority'] . "\n";
                    $arrInput['content'] .= "\n" . '详情请访问链接：' . self::$apealLink;

                    $this->sendMail($arrInput);

                    //策略报警入库 --- 邮件记录
                    $arrData = array(
                        'name' => 'apeal_monitor',
                        'id' => 3,
                        'device' => 5,
                        'frequency' => 5,
                        'category' => 'apeal_monitor' . $item,
                        'receiver' => $toMail,
                        'receiv_type' => 0, //0邮件，1短信
                        'receiv_path' => 'mail',
                        'alarm_type' => 1, //1同比，0固定阈值
                        'level' => $level,
                        'alarm_threshold' => self::THRESHOLD,
                        'num' => $val['apeal_count'], //alarm_num
                        'details' => $arrInput['content'],
                        'alarm_time' => time()
                    );

                    $arrOut = Tieba_Service::call('feedback', 'addAlarmHis', $arrData, null, null, 'post', 'php', 'utf-8');
                    if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
                        Bingo_Log::warning('call  addAlarmHis failed.input:[' . serialize($arrData) . '].output:[' . serialize($arrOut) . '].');
                    }

                    //报警提交icafe

                    $title = '用户申诉策略日累积量报警：策略' . $item;
                    $type = "Issue";
                    $status = "新建";
                    $owner = self::$strOwner;
                    //$strDetail = implode(',',$postDetail);

                    //输出策略详情格式化
                    $strDetail = "策略号：" . $postDetail['monitor_type'];
                    $strDetail .= "\n" . '策略名称 - ' . $postDetail['name'];
                    $strDetail .= "\n" . '策略上线时间 - ' . $postDetail['online_time'];
                    $strDetail .= "\n" . '策略上线人 - ' . $postDetail['online_uname'];
                    $strDetail .= "\n" . '最近修改时间 - ' . $postDetail['modify_time'];
                    $strDetail .= "\n" . '最近修改人 - ' . $postDetail['modify_uname'];
                    $strDetail .= "\n" . '策略版本id - ' . $postDetail['version_id'];
                    $strDetail .= "\n" . '策略标识filter_id - ' . $postDetail['filter_id'];
                    $strDetail .= "\n" . '策略上线状态 - ' . $postDetail['status_online'];
                    $strDetail .= "\n" . '策略评估状态 - ' . $postDetail['status_est'];
                    $strDetail .= "\n" . '策略优先级 - ' . $postDetail['priority'];
                    //输出报警详情内容格式化
                    $strContent = '用户申诉策略日累积量报警：' . "<br>" . '策略' . $item . '今日累计申诉量：' . $val['apeal_count'];
                    $strContent .= '<br>' . '昨日同时段内策略累计申诉量：' . $yesterday_Monitor[$item]['apeal_count'] . "\n";
                    $strContent .= "<br>" . '同比上升：' . $ratio_strategy . "%" . "\n";
                    $strContent .= "<br>" . '报警时误伤率：' .$intWrongRate . "%" . "\n";
                    $strContent .= "<br>" . $postDetail['monitor_type'] . '策略详情：';
                    $strContent .= "<br>" . '策略名称 - ' . $postDetail['name'];
                    $strContent .= "<br>" . '策略上线时间 - ' . $postDetail['online_time'];
                    $strContent .= "<br>" . '策略上线人 - ' . $postDetail['online_uname'];
                    $strContent .= "<br>" . '最近修改时间 - ' . $postDetail['modify_time'];
                    $strContent .= "<br>" . '最近修改人 - ' . $postDetail['modify_uname'];
                    $strContent .= "<br>" . '策略版本id - ' . $postDetail['version_id'];
                    $strContent .= "<br>" . '策略标识filter_id - ' . $postDetail['filter_id'];
                    $strContent .= "<br>" . '策略上线状态 - ' . $postDetail['status_online'];
                    $strContent .= "<br>" . '策略评估状态 - ' . $postDetail['status_est'];
                    $strContent .= "<br>" . '策略优先级 - ' . $postDetail['priority'] . "\n";
                    $strContent .= "<br>" . '详情请访问链接：' . self::$apealLink;

                    $issues[] = array(
                        "title" => $title,
                        "detail" => $strContent,
                        "type" => $type,
                        "fields" => array(
                            "策略号" => $item,
                            "报警时误伤率" => $intWrongRate,
                            "流程状态" => $status,
                            "负责人" => $owner,
                            "报警时间" => date('Y-m-d H:i:s'),
                            "策略上线时间" => $postDetail['online_time'],
                            "策略详情" => $strDetail,
                        ),
                        "creator" => "sunjing20",
                    );

                    $input = array(
                        "username" => "sunjing20",
                        "password" => "VVVvgaUCQKzGZ9qUat8g7FljQ%3D%3D",
                        "issues" => $issues,
                    );

                    self::commitAlarmToIcafe($input);

                    //提交icafe end
                }
            }
                    if ($level === self::P0) {
                        $arrParam = array(
                            'table_name' => 'alarm_his',
                            'begin_time' => $today,
                            'end_time' => time(),
                            'receiv_type' => 1,
                            'category' => 'apeal_monitor' . $item,
                        );
                        $trycount = 3;
                        for ($i = 0; $i < $trycount; $i++) {
                            //获取报警记录 --- 短信去重
                            $arrRet = Tieba_Service::call('feedback', 'getAlarmHistoryCount', $arrParam, null, null, 'post', 'php', 'utf-8');

                            if ($arrRet === false || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                                Bingo_Log::warning("getAlarmHistoryCount error.[input=" . serialize($arrParam) . "][output=" . serialize($arrRet) . "]");
                                continue;

                                // return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
                            }
                            break;
                        }

                        $intCount = (int)$arrRet['data'][0]['count'];

                        if ($intCount === 0) {

                            $arrInput_mobile['content'] = '用户申诉策略日累积量报警：' . "\n" . '策略' . $item . '今日累计申诉量：' . $val['apeal_count'];
                            $arrInput_mobile['content'] .= "\n" . '昨日同时段内策略累计申诉量：' . $yesterday_Monitor[$item]['apeal_count'];
                            $arrInput_mobile['content'] .= "\n" . '同比上升：' . $ratio_strategy . "%";
                            $arrInput_mobile['content'] .= "\n" . '报警时误伤率：人工' . $intWrongRate . "% "."自动".$intCoreUserRate."%";
                            $this->sendPhonemulti($toMobile, $arrInput_mobile['content']);

                            //策略报警入库 --- 短信记录
                            $arrData = array(
                                'name' => 'apeal_monitor',
                                'id' => 3,
                                'device' => 5,
                                'frequency' => 5,
                                'category' => 'apeal_monitor' . $item,
                                'receiver' => $toMobile,
                                'receiv_type' => 1, //0邮件，1短信
                                'receiv_path' => 'message',
                                'alarm_type' => 1, //1同比，0固定阈值
                                'level' => $level,
                                'alarm_threshold' => self::THRESHOLD,
                                'num' => $val['apeal_count'],
                                'details' => $arrInput_mobile['content'],
                                'alarm_time' => time()
                            );

                            $arrOut = Tieba_Service::call('feedback', 'addAlarmHis', $arrData, null, null, 'post', 'php', 'utf-8');
                            if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
                                Bingo_Log::warning('call  processFeedback failed.input:[' . serialize($arrData) . '].output:[' . serialize($arrOut) . '].');
                            }


                            $alarmid=$arrOut['data'];
                            $url_useful='http://tieba.baidu.com/feedback/submit/feedback/processAlarm?ie=utf-8&misinformation=1&online=1&process_status=2&id=' . $alarmid;// misinformation=1 为有效报警
                            $url_useless='http://tieba.baidu.com/feedback/submit/feedback/processAlarm?ie=utf-8&misinformation=-1&online=-1&process_status=1&id=' . $alarmid;// misinformation=-1 为误报报警

                            $url_monitor='http://amis.baidu.com/group/tbfeedback/userApeal/userApeal';

                            $arrInput_mobile['content'] .="\n有效报警请点击:".$url_useful;
                            $arrInput_mobile['content'] .="\n无效报警请点击:".$url_useless;
                            $arrInput_mobile['content'] .="\n用户申诉详情查看:".$url_monitor;
                            $msginput=array(
                                'msg'=>$arrInput_mobile['content'],
                                'to_group'=>1686238,//短信报警快速响应群
                                'instance'=>'yixiu_robot_02'
                            );
                            Util_Hi::sendHiMsg($msginput);

                        }
                    }
                }
        return true;
    }


    /**
     * @return array()
     * @des:获取报警用户信息
     */
    public function getApealAlarmUser()
    {


        $arrOut = Tieba_Service::call('feedback', 'getApealAlarmUser', null, null, null, 'post', 'php', 'utf-8');

        //异常处理
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']) {
            Bingo_Log::warning(sprintf('service call failed! service=getApealAlarmUser, input=[%s], output=[%s]', serialize($arrOut)));
            return false;

        }

        $alarmapealdata = $arrOut['data'];

        return $alarmapealdata;

    }


    /**
     * @param $begin_time
     * @param $end_time
     * @return int
     * @des:获取固定时间段内的申诉量
     */
    public function GetApealCount($begin_time, $end_time)
    {

        $get['apeal_time_start'] = $begin_time;
        $get['apeal_time_end'] = $end_time;

        $arrOutcount = Tieba_Service::call('feedback', 'getApeallistzlwcount', $get, null, null, 'post', 'php', 'utf-8');

        //异常处理
        if (false === $arrOutcount || Tieba_Errcode::ERR_SUCCESS !== $arrOutcount['errno']) {
            Bingo_Log::warning(sprintf('service call failed! service=getApeallistzlwcount, input=[%s], output=[%s]', serialize($get), serialize($arrOutcount)));

            $arrInput['title'] = date('Y-m-d-h:i:s') . "用户申诉数据库拿不到数据啦！！！！！";
            $arrInput['content'] = date('Y-m-d-h:i:s') . "service getApeallistzlwcount call fail";
            $arrInput['content'] .= "\n" . 'output = ' . serialize($arrOutcount);
            $arrInput['me'] = "<EMAIL>";

            $this->sendMail($arrInput);

            $count = 1101100011;

            return $count;

        }

        $count = $arrOutcount['data'][0]['count'];

        return $count;

    }

    /**
     * @param $begin_time
     * @param $end_time
     * @return array
     * @des:获取申诉处罚策略号对应的申诉量
     */
    public function GetMonitorCount($begin_time, $end_time)
    {

        $get['apeal_time_start'] = $begin_time;
        $get['apeal_time_end'] = $end_time;

        $arrOut = Tieba_Service::call('feedback', 'getApeallistzlw_nopage', $get, null, null, 'post', 'php', 'utf-8');

        $monitorResult = array();

        //异常处理
        if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']) {
            Bingo_Log::warning(sprintf('service call failed! service=getApeallistzlw_nopage, input=[%s], output=[%s]', serialize($get), serialize($arrOut)));

            $arrInput['title'] = date('Y-m-d-h:i:s') . "用户申诉数据库拿不到数据啦！！！！！";
            $arrInput['content'] = date('Y-m-d-h:i:s') . "service getApeallistzlw_nopage call fail";
            $arrInput['content'] .= "\n" . 'output = ' . serialize($arrOut);
            $arrInput['me'] = "<EMAIL>";

            $this->sendMail($arrInput);

            return $monitorResult;

        }

        $data = $arrOut['data'];


        foreach ($data as $item) {
            if (isset($item['monitor'])) {

                if (isset($monitorResult[$item['monitor']])) {
                    $monitorResult[$item['monitor']]['apeal_count'] = $monitorResult[$item['monitor']]['apeal_count'] + 1;
                } else {
                    $monitorResult[$item['monitor']]['apeal_count'] = isset($monitorResult[$item['monitor']]['apeal_count']) ? $monitorResult[$item['monitor']]['apeal_count'] + 1 : 1;
                }
            }
        }

        return $monitorResult;

    }


    /**
     * @param $input
     * @return bool
     * @des 发送邮件
     */
    public function sendMail($input)
    {
        echo "=========sendmail";
//        var_dump($input);
//        return 1;
        //$boundary = '----=' . uniqid();

        if (!isset($input['me'])) {
            $to = implode(',', self::$previewToMail);
        } else {
            $to = $input['me'];
        }

        $content = $input['content'];
        //self::$strMailContent;
        $title = $input['title'];
        //$title   = date('Y-m-d-h:i:s') . "申诉情况";

        $headers = "MIME-Version: 1.0\r\n";
        $headers .= "Content-Transfer-Encoding: 8bit\r\n";
        $headers .= "From: <EMAIL>\r\n";
        //$headers .= "Content-type: multipart/mixed; boundary=\"$boundary\""  . "\r\n";
        $headers .= 'Content-type: text/plain; charset=utf-8' . "\r\n";
        //echo $to;
        $subject = $title;
        $subject = "=?UTF-8?B?" . base64_encode($subject) . "?=";

        $ret = mail($to, $subject, $content, $headers);

        return $ret;
    }


    /**
     * @param $mobiles
     * @param $content
     * @param string $sub_code
     */
    public function sendPhonemulti($mobiles, $content, $sub_code = ''){

        //电话号码一次给的太多了，导致短信无法发出去
        //这里进行一下分割，观察一下是否可以发出短信 add by sunjing20
        $arrPhoneReceiver = explode(",",$mobiles);
        $count = 0;
        $arrSend = array();

        foreach($arrPhoneReceiver as $phone) {
            $arrSend[] = $phone;
            $count++;
            if ($count===8) {
                $strSend = implode(",",$arrSend);
//                  echo "----------------SendPhone-------------\n";
//                  var_dump($strSend);
//                    echo "----------------END---------------\n";
                $this->sendPhone($strSend,$content);
                $count = 0;
                $arrSend = array();
            }
        }
    }

    /**
     * @param $mobiles
     * @param $content
     * @param string $sub_code
     * @return int
     * @des: 发送短信
     */
    public function sendPhone($mobiles, $content, $sub_code = '')
    {
        echo "========sendphone";
//        var_dump($content);
//        return 1;

        $url = 'http://emsg.baidu.com/service/sendSms.json';
        $username = "hanxuena";
        $password = "hanxuena";
        $msgDest = $mobiles;
        $msgContent = $content;
        $businessCode = "game";
        $signature = md5($username . $password . $msgDest . $msgContent . $businessCode . $sub_code);
        //echo 'signature='.$signature."\n";
        $curlPost = 'username=' . $username . '&businessCode=' . $businessCode . '&msgContent=' . urlencode($msgContent) . '&msgDest=' . $msgDest . '&signature=' . $signature;
        if ($sub_code !== '') {
            $curlPost .= '&extId=' . $sub_code;
        }
        //echo "curlPost=$curlPost\n";

        $ch = curl_init();
        $this_header = array(
            "content-type: application/x-www-form-urlencoded; charset=UTF-8",
        );

        curl_setopt($ch, CURLOPT_HTTPHEADER, $this_header);
        curl_setopt($ch, CURLOPT_URL, $url);
        //        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $curlPost);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT_MS, 5000);// 1s瓒^[[36;98H
        curl_setopt($ch, CURLOPT_TIMEOUT_MS, 5000); // 1s瓒^[[36;98H
//        curl_setopt($ch, CURLOPT_VERBOSE, true);

        $data = curl_exec($ch);
        curl_close($ch);
        if ($data) {
            $result = json_decode($data, true);

            if ($result['result'] == 1000) {
                return 1;
            } else {
                return 0;
            }
        }
        return 0;

    }

    /**
     * @param $monitor策略号
     * @return array
     * @des 获取策略详情
     */
    public function getMonitorDetail($monitor)
    {

        $arrRet = array();

        if (empty($monitor)) {
            return $arrRet;
        }

        $arrInput['monitor'] = $monitor;

        $arrRet = Tieba_Service::call('feedback', 'getMonitorinfo', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call  getMonitorinfo failed.input:[' . serialize($arrInput) . '].output:[' . serialize($arrRet) . '].');
            return $arrRet;
        }

        return $arrRet['ret'][0];

    }

    /**
     * @param $monitor 策略号
     * @return int 误伤率
     * @des 计算报警时的策略误伤
     */
    public function getMonitorWrongRate($monitor)
    {
        $wrongRate=array(
          'intWrongRate' =>0,
          'intCoreUserRate' =>0,
        );

        if (empty($monitor)) {
            return $wrongRate;
        }

        $arrParam = array();

        $arrParam['begin_day'] = date('Y-m-d');
        $arrParam['end_day'] = date('Y-m-d');
        $arrParam['type'] = 1; //用户申诉为1，帖子申诉为2
        $arrParam['monitor'] = $monitor;


        $arrRet = Tieba_Service::call('feedback', 'getApeallistMonitorday', $arrParam, null, null, 'post', 'php', 'utf-8');


        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
            Bingo_Log::warning('call  getApeallistMonitorday failed.input:[' . serialize($arrParam) . '].output:[' . serialize($arrRet) . '].');
            return $wrongRate;
        }

        $data=$arrRet['data'];

        foreach ($data as $d){
            if ($d['index_type']=='apeal_count'){
                $apeal_count=$d['index_value'];
            }

            if ($d['index_type']=='apeal_count_done'){
                $apeal_count_done=$d['index_value'];
            }
            if ($d['index_type']=='wrong'){
                $wrong=$d['index_value'];//人工识别误伤
            }
            if ($d['index_type']=='is_coreuser'){
                $is_coreuser=$d['index_value'];//优质用户误伤
            }
        }


        $wrongRate['apeal_count']=$apeal_count;
        $wrongRate['is_coreuser']=$is_coreuser;
        $wrongRate['apeal_count_done']=$apeal_count_done;
        $wrongRate['wrong']=$wrong;

        $wrongRate['intWrongRate']=100 * round($wrong / $apeal_count_done, 2);
        $wrongRate['intCoreUserRate']=100 * round($is_coreuser / $apeal_count, 2);

        return $wrongRate;
    }



    /**
     * @param $data
     * @return bool
     * @des:报警提至icafe等待处理
     */
    public function commitAlarmToIcafe($data)
    {
        echo "========commitAlarmToIcafe";
//        var_dump($data);
//        return 1;

        $data = json_encode($data);
        $url = 'http://icafe.baidu.com/api/v2/space/' . self::$icafeSpace . '/issue/new';
        $param = array();
        $headers = array('Content-Type: application/json; charset=utf-8');

        /*
        //此处为请求提交icafe的接口，可以使用基础库的post，待替换
        $options = array(
            CURLOPT_HEADER => 0,
            CURLOPT_URL => $url,
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_RETURNTRANSFER => 1,
            CURLOPT_HTTPHEADER => array('Content-Type: application/json; charset=utf-8'),
        );

        curl_setopt_array($ch, $options);
        $ret = curl_exec($ch);

        curl_close($ch);


        $ret = json_decode($ret, true);

        if ($ret === false || $ret['status'] !== 200) {

            Bingo_Log::warning('call  icafe new failed.input:[' . serialize($options) . '].output:[' . serialize($ret) . '].');

        }
        //这个方法不好，用lib库的post请求
        */

        $post = new AipHttpClient();
        //默默地改了基础库，好好地一个基础库干嘛抛异常.....摊手手，我才不接呢，不是不想接，是因为不合理
        //lib的header拼接也有问题，只改了post的，写成了经常的，之后修一下改成支持传参的吧 @sunjing20
        $result = $post->post($url, $data, $param, $headers);

        if ($result === false || $result['code'] !== 200) {

            Bingo_Log::warning('call  icafe new failed.url:[' . serialize($url) . ']input:[' . serialize($data) . '].output:[' . serialize($result) . '].');

        }

        return true;

    }



}
