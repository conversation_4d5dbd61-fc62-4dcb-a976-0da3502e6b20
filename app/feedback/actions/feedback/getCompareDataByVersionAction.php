<?php
/**
 * Created by PhpStorm.
 * User: caoyuting01
 * Date: 18/09/13
 * Time: 17:05
 */
class getCompareDataByVersionAction extends Util_Base
{

    /**
     * 主函数
     * @return array
     */
    public function _execute()
    {
        try {
            $get = Bingo_Http_Request::getGetNoXssSafe();
            if (!isset($get['nowTime']) || intval($get['nowTime'])<0 || !isset($get['beforeTime']) || intval($get['beforeTime'])<0 || !isset($get['duration']) || intval($get['duration'])<0||!isset($get['threshold']) || intval($get['threshold'])<0) {
                Bingo_Log::warning("input params invalid.");
                return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $topNum=10;
            $threshold = intval($get['threshold']);
            if(isset($get['topNum'])){
                $topNum=intval($get['topNum']);
            }

            //公共部分
            if(isset($get['forum_id'])){
                $arrCurInput['forum_id']=intval($get['forum_id']);
                $arrLastDayPreInput['forum_id']=intval($get['forum_id']);
                $arrCurDayPreInput['forum_id']=intval($get['forum_id']);
                $arrLastHourInLastDayInput['forum_id']=intval($get['forum_id']);
                $arrLast24HourAvgInput['forum_id']=intval($get['forum_id']);
            }
            if(isset($get['device'])){
                $arrCurInput['device']=intval($get['device']);
                $arrLastDayPreInput['device']=intval($get['device']);
                $arrCurDayPreInput['device']=intval($get['device']);
                $arrLastHourInLastDayInput['device']=intval($get['device']);
                $arrLast24HourAvgInput['device']=intval($get['device']);
            }

            //【1】---------从今天凌晨到现在时间段的数据 cyt
            //
            $arrLast24HourAvgInput=array(
                'begin_time'=> intval($get['beforeTime']),
                'end_time'  => intval($get['nowTime']),
            );
            $arrLast24HourAvgOut =self::getHistory($arrLast24HourAvgInput);
            if (false === $arrLast24HourAvgOut ) {
                throw new Util_Exception("service call fail!", Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            Bingo_Log::warning("CYT---今天---".print_r($arrLast24HourAvgOut,1));

            //【2】---------获取昨天同时段反馈数量  ,昨天凌晨到昨天的此时此刻cyt
            $lastdayInput=array(
                'begin_time'=> intval($get['beforeTime'])-1*24*60*60,
                'end_time'  => intval($get['nowTime'])-1*24*60*60,
            );
            $lastDayOut = self::getHistory($lastdayInput);
            if (false === $lastDayOut) {
                throw new Util_Exception("service call fail!", Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $lastDayOutNew=self::transArray($lastDayOut);
            //Bingo_Log::warning("cyt-- 昨天--".print_r($lastDayOutNew,1));

            //【3】---------获取当天前一时段值,上周同一天的数量。
            $lastWeekInput=array(
                'begin_time'=> intval($get['beforeTime'])-7*24*60*60,
                'end_time'  => intval($get['nowTime'])-7*24*60*60,
            );
            $lastWeekOut = self::getHistory($lastWeekInput);
            if (false === $lastWeekOut) {
                throw new Util_Exception("service call fail!", Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $lastWeekOutNew=self::transArray($lastWeekOut);
            //Bingo_Log::warning("cyt-- 上周---".print_r($lastWeekOutNew,1));

            $arrOut=array();
            $arrTopInfo=array();
            $arrFbTid=array();
            foreach($arrLast24HourAvgOut['data'] as $key=>$value){
                if (empty($value['version'] )){
                    continue;
                }
                //返回的反馈数,是按照类别排好序的,一旦出现某个类别的当前反馈数<阈值,则停止后边分类反馈数的分析
                if($threshold!=0 && $value['num'] < $threshold){
                    break;
                }
                $version=$value['version'];
                //将各个参考指标存储,包括当天当前时段反馈量,
                $arrTopInfo[$version][$value['category_sub']]['category_sub']=$value['category_sub'];
                $arrTopInfo[$version][$value['category_sub']]['num']=$value['num'];
                $arrTopInfo[$version][$value['category_sub']]['device']=$value['device'];

                //add  18/09/13  start
                //将各个参考指标存储,昨天同一时段反馈量,上周同一天同一时段反馈量
                $lastday_num=intval($lastDayOutNew[$version][$value['category_sub']]['num']);
                $lastweek_num=intval($lastWeekOutNew[$version][$value['category_sub']]['num']);
                $arrTopInfo[$version][$value['category_sub']]['DLast_Num']=$lastday_num;
                $arrTopInfo[$version][$value['category_sub']]['DLastWeek_Num']=$lastweek_num;




//Bingo_Log::warning("cyt!!-top-".print_r($arrTopInfo,1));
                //计算增长率,包括与昨天同时段比较的增长率,较今天前一时段的增长率,前一时段今日比昨日的增长率
                //【1】
                if($lastday_num==0){
                    $arrTopInfo[$version][$value['category_sub']]['rate_lastday']="100%";//."%";//$value['num'];
                }else{
                    $numtmp=($value['num']-$lastday_num)/$lastday_num;
                    $arrTopInfo[$version][$value['category_sub']]['rate_lastday']=round($numtmp*100,2)." %";
                }
                //【2】
                if($lastweek_num==0){
                    $arrTopInfo[$version][$value['category_sub']]['rate_lastweek']="100%";//$value['num'];
                }else{
                    $numtmp=($value['num']-$lastweek_num)/$lastweek_num;
                    $arrTopInfo[$version][$value['category_sub']]['rate_lastweek']=round($numtmp*100,2)." %";
                }
                //end 18/09/13

                //Bingo_Log::warning("cyt!!-top-".print_r($arrTopInfo,1));

            }
            $arrOut['data']['topFeedback']=$arrTopInfo;

            $arrOut['errno']=0;
            $arrOut['errmsg']='success';
            Bingo_Log::warning("cyt".print_r($arrOut,1));

            $this->_jsonRet($arrOut['errno'], $arrOut['errmsg'], $arrOut['data']);
        } catch (Exception $e) {
            Bingo_Log::warning("no=" . $e->getCode() . " msg=" . $e->getMessage());
            $this->_jsonRet($e->getCode(), "error");
        }
    }

    /**
     * 获取分版本的信息
     * @param $arrLast24HourAvgInput
     * @return bool
     */
    private  function getHistory($arrLast24HourAvgInput){
        $arrLast24HourAvgOut = Tieba_Service::call('feedback', 'getAllFeedbackNumByVersionhistory', $arrLast24HourAvgInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrLast24HourAvgOut || Tieba_Errcode::ERR_SUCCESS != $arrLast24HourAvgOut['errno']) {
            Bingo_Log::warning('call  getAllFeedbackNumByCategory failed.input:[' . serialize($arrLast24HourAvgInput) . '].output:[' . serialize($arrLast24HourAvgOut) . '].');
            return false;
        }
        return $arrLast24HourAvgOut;
    }

    /**
     * 将数组转换一下格式
     * @param $arrOut
     * @return array
     */
    private  function transArray($arrOut){
        $arrNew=array();
        foreach($arrOut['data'] as $key=>$value){
            if (empty($value['version'] )){
                continue;
            }

            $version=$value['version'];
            //将各个参考指标存储,包括当天当前时段反馈量,
            $arrNew[$version][$value['category_sub']]['category_sub']=$value['category_sub'];
            $arrNew[$version][$value['category_sub']]['num']=$value['num'];
            $arrNew[$version][$value['category_sub']]['device']=$value['device'];
        }
        return $arrNew;

    }

}
/* vim: set noexpandtab ts=4 sw=4 sts=4 tw=100: */
