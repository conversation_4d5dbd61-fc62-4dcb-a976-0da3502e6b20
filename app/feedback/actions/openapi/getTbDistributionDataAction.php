<?php
/**
 * Created by PhpStorm.
 * User: yin<PERSON><PERSON>
 * Date: 2019/11/19
 * Time: 下午7:53
 */
class getTbDistributionDataAction extends Util_Base
{

    public function _execute()
    {
        try {
            $get = Bingo_Http_Request::getGetNoXssSafe();

            if (!isset($get['cmatch']) || empty($get['cmatch']) ) {
                return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $cmatch = $get['cmatch'];
            if( !isset(Data_ConFile::$_cmatchForBusiness[$cmatch]) ){
                Bingo_Log::warning('cmatch id:'. $cmatch . ' is not exist');
                return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $positionId = Data_ConFile::$_cmatchForBusiness[$cmatch]; //业务id

            $indicatorId = isset($get['indicator_id']) ? $get['indicator_id'] : 9; //默认返回内容曝光量
            if (empty($indicatorId)) {
                $strLog = "need indicator_id";
                Bingo_Log::warning($strLog);
                return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $statDate = isset($get['stat_date']) ? $get['stat_date'] : date("Ymd");











            $arrRetJson = array(
                'errno' => 0,
                'errmsg' => 'success',
                'data' => array(
                        /**
                        'version' => '1.0.0',
                        'status' => 0,//0-上线,1-推全
                        'platform' => 'iOS',
                        'startTime' => '',
                        */
                ),
            );

            $arrRetExJson = json_encode($arrRetJson);
            echo $arrRetExJson;
        } catch (Exception $e) {
            Bingo_Log::warning("no=" . $e->getCode() . " msg=" . $e->getMessage());
            $arrRetEx = array(
                'no'=>$e->getCode(),
                'error'=>'error',
            );
            $arrRetExJson=json_encode($arrRetEx);
            echo $arrRetExJson;
        }

    }

}