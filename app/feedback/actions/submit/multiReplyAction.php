<?php
/**
 * Created by PhpStorm.
 * User: jialin04
 * Date: 17/7/20
 * Time: 18:43
 */
class multiReplyAction extends Util_Base
//class multiReplyAction extends Util_Base
{

    public function _execute()
    {
        try {
            $get = Bingo_Http_Request::getGetNoXssSafe();
            if (!isset($get['threadNum']) || !isset($get['content']) || !is_array($get['threadList'])) {
                Bingo_Log::warning("input params invalid.");
                $arrRetEx = array(
                    'no'=>Tieba_Errcode::ERR_PARAM_ERROR,
                    'error'=>'error',
                );
                $arrRetExJson=json_encode($arrRetEx);
                echo 'jsoncallbacksubmit('.$arrRetExJson.');';
                return;
                //				return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $arrOut = Service_Feedback::multiReply($get);
            if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
                Bingo_Log::warning('call  processFeedback failed.input:[' . serialize($get) . '].output:[' . serialize($arrOut) . '].');
                throw new Util_Exception("service call fail!", Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $arrRet = array(
                'no'=>intval($arrOut['errno']),
                'error'=>strval($arrOut['errmsg']),
                'data'=>isset($arrOut['data']) ? $arrOut['data'] : array(),
            );
            $arrRetJson=json_encode($arrRet);
            echo 'jsoncallbacksubmit('.$arrRetJson.');';
            //	$data = isset($arrOut['data']) ? $arrOut['data'] : array();
            //	$this->_jsonRet($arrOut['errno'], $arrOut['errmsg'], $data);
        } catch (Exception $e) {
            Bingo_Log::warning("no=" . $e->getCode() . " msg=" . $e->getMessage());
            $arrRetEx = array(
                'no'=>$e->getCode(),
                'error'=>'error',
            );
            $arrRetExJson=json_encode($arrRetEx);
            echo 'jsoncallbacksubmit('.$arrRetExJson.');';
            //$this->_jsonRet($e->getCode(), "error");
        }
    }
}

