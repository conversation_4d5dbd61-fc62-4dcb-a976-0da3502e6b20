<?php
/**
 * Created by PhpStorm.
 * User: jialin04
 * Date: 17/9/5
 * Time: 17:26
 */

class receiveUFODataAction extends Util_Base
{
    public function _execute()
    {
        try {
            $get = Bingo_Http_Request::getGetNoXssSafe();
            if (!isset($get['from_source']) || intval($get['from_source'])<0 || !isset($get['content']) || empty($get['content']) ){
                Bingo_Log::warning("input params invalid.");
                $arrRetEx = array(
                    'no'=>Tieba_Errcode::ERR_PARAM_ERROR,
                    'error'=>'error',
                );
                $arrRetExJson=json_encode($arrRetEx);
                echo 'jsoncallbackgetCategorySubDataOfOneDay('.$arrRetExJson.');';
                return;
            }

            $arrRet = Tieba_Service::call('feedback', 'receiveUFOData', $get, null, null, 'post', 'php', 'utf-8');
            if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']) {
                Bingo_Log::warning('call  receiveUFOData failed.input:[' . serialize($arrRet) . '].output:[' . serialize($arrRet) . '].');
                throw new Util_Exception("service call fail!", Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
          	$data = isset($arrRet['data']) ? $arrRet['data'] : array();
            $this->_jsonRet($arrRet['errno'], $arrRet['errmsg'], $data);
        } catch (Exception $e) {
            Bingo_Log::warning("no=" . $e->getCode() . " msg=" . $e->getMessage());
            $this->_jsonRet($e->getCode(), "error");
        }
    }
}