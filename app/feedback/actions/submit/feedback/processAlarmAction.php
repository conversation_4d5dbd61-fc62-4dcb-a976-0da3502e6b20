<?php
/**
 * Created by PhpStorm.
 * User: sunjing20
 * Date: 2018/8/23
 * Time: 11:36
 */


class processAlarmAction extends Util_Base {

    /**
     * @breif:处理报警
     * @param:null
     * @return:(array)
     *
     **/
    public function _execute()
    {
        // 获取入参
        $arrGet = Bingo_Http_Request::getGetNoXssSafe();


        //校验处理参数，报警原因可不填，其余为必填
        if ((empty($arrGet['id']) && $arrGet['id'] != 0) || (empty($arrGet['misinformation']) && $arrGet['misinformation'] != 0) || (empty($arrGet['level']) && $arrGet['level'] != 0)) {

            Bingo_Log::warning("check input ! some param empty !");
            return $this->_printOut(Tieba_Errcode::ERR_PARAM_ERROR, '参数错误');
        }
        elseif ((empty($arrGet['process_status']) && $arrGet['process_status'] != 0) || (empty($arrGet['online']) && $arrGet['online'] !=0)) {
            Bingo_Log::warning("check input ! some param empty !");
            return $this->_printOut(Tieba_Errcode::ERR_PARAM_ERROR, '参数错误');
        }

        $strDetector = Bingo_Encode::convert($arrGet['detector'], 'UTF-8', 'GBK');//据说不去空格入库出来会乱码
        $intAlarmId = (int)$arrGet['id'];
        $intMisInformation = (int)$arrGet['misinformation'];
        $intProcessStatus = (int)$arrGet['process_status'];
        $intLevel = (int)$arrGet['level'];
        $intOnline = (int)$arrGet['online'];


        $arrParam = array(
            'id' => $intAlarmId,
            'misinformation' => $intMisInformation,
            'process_status' => $intProcessStatus,
            'level' => $intLevel,
            'online' => $intOnline,
            'detector' => $strDetector,
        );

        $arrRet = Tieba_Service::call('feedback','updateAlarmHis',$arrParam,null,null,'post','php','utf-8');

        if ($arrRet === false) {

            Bingo_Log::warning("call service feedback :: getAlarmHistoryData fail,input: " . serialize($arrParam) . "output: " .  serialize($arrRet));

            return $this->_printOut(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);

        }

        return $this->_printOut(Tieba_Errcode::ERR_SUCCESS, 'success');


    }

    /**
     * @breif:处理字符串空格
     * @param:string
     * @return:(array)
     *
     **/
    public function mb_trim($string){
        // u模式符表示 字符串被当成 UTF-8处理
        return preg_replace('/(^\s+)|(\s+$)/u', '', $string);
    }


    /**
     * 输出数据, 按照amis的格式返回
     * @param  integer $errno [description]
     * @param  string  $msg   [description]
     * @param  array   $data  [description]
     * @return [type]         [description]
     */
    public function _printOut($errno = 0, $msg = '', $data = array()) {
        if ($errno !== Tieba_Errcode::ERR_SUCCESS) {
            $msg = $msg ? $msg : Bingo_Encode::convert(Tieba_Error::getUserMsg($errno), Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
            // $msg = $msg ? $msg : Tieba_Error::getUserMsg($errno);
            Bingo_Log::warning('url['.strip_tags(Bingo_Http_Request::getServer('REQUEST_URI')).'],errno['.$errno.'],msg['.$msg.']');
        }


        echo json_encode(
            array(
                'no' => $errno,
                'error' => $msg,
                'data' => $data,
            )
        );
    }

}