<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2016-09-27 13:35:22
 * @comment
 * @version
 */
class makeFbCateAmisAction extends Util_Base
{

    public function _execute()
    {
        try {
            $get = Bingo_Http_Request::getGetNoXssSafe();
            if (!isset($get['thread_id']) || !isset($get['from_source']) || !isset($get['markTag'])) {
                Bingo_Log::warning("input params invalid.");
                $arrRetEx = array(
                    'no' => Tieba_Errcode::ERR_PARAM_ERROR,
                    'error' => 'error',
                );
                $arrRetExJson = json_encode($arrRetEx);
                echo $arrRetExJson;
                return;
                //				return self::_jsonRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }

            foreach (Data_ConFile::$_fbCate as $key=>$value){
                if ($get['markTag']==$value){
                    $get['markTag']=$key;
                    break;
                }
            }
            $arrOut = Tieba_Service::call('feedback', 'markFbCate', $get, null, null, 'post', 'php', 'utf-8');
            if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
                Bingo_Log::warning('call  processFeedback failed.input:[' . serialize($get) . '].output:[' . serialize($arrOut) . '].');
                throw new Util_Exception("service call fail!", Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
            $arrRet = array(
                'status' => intval($arrOut['errno']),
                'msg' => strval($arrOut['errmsg']),
                'data' => isset($arrOut['data']) ? $arrOut['data'] : array(),
            );
            $arrRetJson = json_encode($arrRet);
            echo $arrRetJson;
            //	$data = isset($arrOut['data']) ? $arrOut['data'] : array();
            //	$this->_jsonRet($arrOut['errno'], $arrOut['errmsg'], $data);
        } catch (Exception $e) {
            Bingo_Log::warning("no=" . $e->getCode() . " msg=" . $e->getMessage());
            $arrRetEx = array(
                'no' => $e->getCode(),
                'error' => 'error',
            );
            $arrRetExJson = json_encode($arrRetEx);
            echo $arrRetExJson;
            //$this->_jsonRet($e->getCode(), "error");
        }
    }
}
/* vim: set noexpandtab ts=4 sw=4 sts=4 tw=100: */
