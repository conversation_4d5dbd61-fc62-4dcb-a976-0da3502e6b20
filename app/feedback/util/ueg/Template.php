<?php
/**
 * Created by PhpStorm.
 * User: yuanqin
 * Date: 19/6/25
 * Time: 下午4:06
 */
//动态文本聚类组件
class Util_Ueg_Template{
    public static function descript() { //返回组件的各函数实现方式说明
        $functions = array(
            'template' => array( //Key是用户使用时，用的函数名
                'stateless' => true, //是否无状态（无状态的可以进行去重及惰性计算），默认为false
            ),
        );
        return $functions;
    }
    public static function init() {
    }
    public static function template($content,$patterns){
        if(!isset($content) || !isset($patterns) ) {
            Naf_Log::warning("param error.");
            return array();
        }
        $template = Tieba_Anti_TemplateFormat::getTemplate($content,$patterns,count($patterns)-1);
        return $template;
    }

}