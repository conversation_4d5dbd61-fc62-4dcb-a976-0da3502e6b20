<?php
/**
 * ui action的基类
 **/
abstract class Util_Base extends Bingo_Action_Abstract {
	
	protected $_strStlogMid = "feedback";
	protected $_strStlogFile = "feye-stat";
	
	//tbs验证
	public static $_tbsCheck = true;
	
	// 错误号
    protected $_intErrno = 0;

    // 错误信息
    protected $_strError = 'success';
    protected $_arrUserInfo = array();
    abstract function _execute();
    public function _init() {}
    public function execute() {
    	$this->_execute();
		$this->destroy();
    }
	protected static $_arrBlockUids = array(
		45517181,
		164135984,
		231455694,
		566448476,
		754201114,
		814308583,
		891418200,
		1463113423,
		1463535621
		);

    public function init(){
    
    	//获取路由名，并打到ui日志里
        $strRouter = Bingo_Http_Request::getStrHttpRouter();
    	Bingo_Log::pushNotice("urlkey", $strRouter);
    	
    	//获取当前用户信息，并打到ui日志里
    	$this->_getUserInfo();
    	foreach($this->_arrUserInfo as $strKey => $mixVal) {
    		Bingo_Log::pushNotice($strKey, $mixVal);
    	}
		//小流量
		/*if(!isset($this->_arrUserInfo['superboy']) || 'tbmall2' != $this->_arrUserInfo['superboy']){
			Bingo_Log::warning('not superboy');
			throw new Util_Exception("superboy error!",Tieba_Errcode::ERR_INVALID_SIGN);
		}*/
		//添加用户黑名单
		if(in_array($this->_arrUserInfo['user_id'],self::$_arrBlockUids)){
			Bingo_Log::warning('block user_id:'.$this->_arrUserInfo['user_id']);
			throw new Util_Exception("block user error!",Tieba_Errcode::ERR_INVALID_SIGN);
		}

		//默认对POST请求做tbs校验，如不需要自行去掉此模块
//        if( Bingo_Http_Request::isPost() && self::$_tbsCheck ){
//        	$strTbs = strval(Bingo_Http_Request::get('tbs',''));
//            if ( false === $this->_tbsCheck($strTbs) ) {
//            	throw new Util_Exception("tbs check error!",Tieba_Errcode::ERR_INVALID_SIGN);
//            }
//        }
        return true;
    }
	
	public function destroy(){
    	$this->_noticeStlog();
    }

    protected function _tbsCheck($strTbs) {        
    	if(!Tieba_Tbs::check($strTbs, true)){
    	 	return false;
		}
		return true;
    }
    
    protected function _getUserInfo() {
        if (!empty($this->_arrUserInfo)) {
            return $this->_arrUserInfo;
        }

        $bolLogin   = (boolean)Tieba_Session_Socket::isLogin();
        $intUserId  = intval(Tieba_Session_Socket::getLoginUid());
        $strUserName    = strval(Tieba_Session_Socket::getLoginUname());
        if(empty($intUserId) || empty($strUserName)){ //amis获取用户名
			$strUserName = Bingo_Http_Request::getServer('HTTP_AMIS_USER', '');
			if(!empty($strUserName)){
				$intUserId = $this->_getIdByName($strUserName);
			}
		}
        $intUserIp      = intval(Bingo_Http_Ip::ip2long(Bingo_Http_Ip::getConnectIp()));
        $bolNoUname     = (boolean)Tieba_Session_Socket::getNo_un();
        $strMobile      = strval(Tieba_Session_Socket::getMobilephone());
        $strEmail       = strval(Tieba_Session_Socket::getEmail());

        $arrUserSInfo   = array();
        if ($intUserId > 0 ){
            //$arrUserSInfo = Service_Lib_Userinfo::getUserData($intUserId);
            $arrUserOutput = Tieba_Service::call("user", "getUserData", array('user_id' => $intUserId),null,null,'post','php','utf-8');
            if (!isset($arrUserOutput['errno']) || intval($arrUserOutput['errno']) !== 0) {
                Bingo_Log::warning('getUserPropsInfo error! output:' . serialize($arrUserOutput));
                $arrUserSInfo = false;
            }       
            if(!isset($arrUserOutput['user_info'][0])){
                Bingo_Log::warning("user_info has no data.[user_id=$intUserId]");
                $arrUserSInfo = false;
            }       
            $arrUserSInfo = $arrUserOutput['user_info'][0];
            if ($arrUserSInfo === false){ 
                $arrUserSInfo = array();
            }       
        }       
        $arrUserSInfo['is_login'] = $bolLogin;
        $arrUserSInfo['user_id'] = $intUserId;
        $arrUserSInfo['user_name_utf8'] = $arrUserSInfo['user_name'];
        $arrUserSInfo['user_name'] = $strUserName;//$arrUserSInfo['user_name'];
        $arrUserSInfo['user_ip'] = $intUserIp;
        $arrUserSInfo['is_noname'] = $bolNoUname;
        $arrUserSInfo['mobile'] = Tieba_Util::maskPhone($strMobile);
        $arrUserSInfo['email'] = Tieba_Util::maskEmail($strEmail);
        $arrUserSInfo['tbs'] = Tieba_Tbs::gene(true);
        $arrUserSInfo['portrait'] = strval(Tieba_Ucrypt::encode($intUserId));

        $this->_arrUserInfo = $arrUserSInfo;
        return $this->_arrUserInfo;
    }

    /**
     * @param $arrInput
     * @return array
     */
    protected function _jsonRet($errno,$errmsg,array $arrExtData=array(),$total=''){
        $arrRet = array(
            'no'=>intval($errno),
            'error'=>strval($errmsg),
            'data'=>$arrExtData,
            'total'=>intval($total),
        );
        foreach($arrRet as $k=>$v){
            Bingo_Page::assign($k,$v);
        }
        Bingo_Page::setOnlyDataType("json");
        Bingo_Http_Response::contextType('application/json');
    }

    protected function _serialRet($errno,$errmsg,array $arrExtData=array()){
        $arrRet = array(
            'no'=>intval($errno),
            'error'=>strval($errmsg),
            'data'=>$arrExtData,
        );
        foreach($arrRet as $k=>$v){
            Bingo_Page::assign($k,$v);
        }
        Bingo_Page::setOnlyDataType("serial");
        Bingo_Http_Response::contextType('application/json');
    }
	
	protected function _noticeStlog($strUrlkey = null){
		//获取路由名，并打到ui日志里
		if(is_null($strUrlkey)){
			$strUrlkey = Bingo_Http_Request::getStrHttpRouter();
		}
		$arrUserInfo = $this->_getUserInfo();
		Bingo_Log::pushNotice("urlkey",$strUrlkey);
		$arrBasicDefalutStlogFields   = array(
				'bduid' => Tieba_Session_Socket::getBaiduid(),
				'uid'   => $arrUserInfo['user_id'],
				'un' => $arrUserInfo['user_name'],
				'uip'    => $arrUserInfo['user_ip'],
				'no_un' => intval($arrUserInfo['is_noname']),
				'mobilephone' => $arrUserInfo['mobile'],
				'email'  => $arrUserInfo['email'],
				'url' => Bingo_Http_Request::getServer("REQUEST_URI",""),
				'refer' => Bingo_Http_Request::getServer("HTTP_REFERER",""),
				'agent' => Bingo_Http_Request::getServer("HTTP_USER_AGENT",""),
				'logid' => Bingo_Log::getLogId(),
				'is_new_user' => intval(Tieba_Cookie_UserType::isNew()),
				'optime' => time(),
				'pro' => "tieba",
				'mid' => $this->_strStlogMid,
				'cookieuid' => Tieba_Session_Socket::getCookieUid(),
				'urlkey' => $strUrlkey,
				'errno' => 0,
				'ispv' => 0,
		);
		
		$arrNodes = Bingo_Log::getNoticeNodes();
		$arrStlogFields = array_merge($arrBasicDefalutStlogFields,$arrNodes);
	
		foreach ($arrStlogFields as $key=>$val){
			if(strncmp($key,"_",1) === 0){
				continue;
			}
			Tieba_Stlog::addNode($key,$val);
		}
		
		Tieba_Stlog::setFileName($this->_strStlogFile);
		Tieba_Stlog::notice();
	}

	/**
	 * amis登录获取uid
	 * @param $username
	 * @return int
	 */
	protected function _getIdByName($username) {
		$url = "http://amis.baidu.com/api/user/uic?username=".urlencode($username);
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_TIMEOUT, 100);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($curl, CURLOPT_HTTPHEADER, array('Expect:'));
		$output = curl_exec($curl);
		$code   = curl_getinfo($curl, CURLINFO_HTTP_CODE);
		$errMsg = curl_multi_getcontent($curl);
		$err    = curl_error($curl);
		$errno  = curl_errno($curl);
		curl_close($curl);
		if ($code != 200) {
			Bingo_Log::fatal('_getIdByName code not 200,out['.serialize($output).'],code['.$code.']');
			return 0;
		}

		$arrRet = json_decode($errMsg,true);

		if($arrRet && $arrRet['status'] == 0) {
			return intval($arrRet['data']['id']);
		}

		Bingo_Log::fatal('_getIdByName error,out['.serialize($output).'],code['.$code.']');
		return 0;
	}

}
