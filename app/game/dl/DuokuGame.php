<?php
/**
 * 多酷游戏的相关接口
 * @authors tanxinyun
 * @date    2014-12-18 15:11:43
 *
 * 中文存储时都是gbk编码
 */

class Dl_DuokuGame {

    const REDIS_APP_NAME = 'bdrpecom';
    const KEY_PREFIX = 'duoku_kfb_';
    const APPLY_URL = 'http://api.m.duoku.com/openapi/common/tieba_apply_to_play';
    const APPLY_URL_TEST = 'http://itest3.client.duoku.com:10086/openapi/common/tieba_apply_to_play';

    private static $_redis = null;

    private static function _getRedis($appName) {
        if (self::$_redis == null) {
            self::$_redis = new Bingo_Cache_Redis($appName);
        }

        return self::$_redis;
    }

    private static function _doRedisQuery($method, $arrInput) {
        $redis = self::_getRedis(self::REDIS_APP_NAME);

        if (!$redis) {
            Bingo_Log::warning('failed to get redis: '.self::REDIS_APP_NAME);
            return false;
        }

        $ret = $redis->$method($arrInput);

        if (!$ret || $ret['err_no'] !== 0) {
            Bingo_Log::warning('redis '.$method.' failure, input: '.json_encode($arrInput).', ret: '.json_encode($ret));
            return false;
        }

        return $ret['ret'][$arrInput['key']];
    }

    private static function _setUnserializedValue($key, $val) {
        $input = array(
            'key' => self::KEY_PREFIX.$key,
            'value' => serialize($val),
        );

        return self::_doRedisQuery('SET', $input);
    }

    private static function _getUnserializedValue($key) {
        $input = array(
            'key' => self::KEY_PREFIX.$key,
        );

        $ret = self::_doRedisQuery('GET', $input);

        if ($ret === false) {
            return false;
        }

        return empty($ret) ? $ret : unserialize($ret);
    }

    public static function applyToPlayDuokuGame($arrInput) {
        $proxy = Orp_FetchUrl::getInstance(array(
            'timeout' => 30000,
            'conn_timeout' => 10000,
            'max_response_size' => 2048000,
        ));

        $userId = $arrInput['user_id'];
        $kaifuId = $arrInput['kaifu_id'];

        $url = self::APPLY_URL."?user_id={$userId}&kaifu_id={$kaifuId}";
        
        return $proxy->get($url);   
    }

    public static function setDuokuHotGames($arrInput) {
        return self::_setUnserializedValue('hotgames', $arrInput['hot_games']);
    }

    public static function getDuokuHotGames() {
        return self::_getUnserializedValue('hotgames');
    }

    public static function setDuokuBanners($arrInput) {
        return self::_setUnserializedValue('sp_banners', $arrInput['banners']);
    }

    public static function getDuokuBanners($arrInput) {
        return self::_getUnserializedValue('sp_banners');
    }

    public static function setDuokuRunningGames($games) {
        return self::_setUnserializedValue('running_games', $games);
    }

    public static function getDuokuRunningGames($arrInput) {
        return self::_getUnserializedValue('running_games');
    }

    public static function setDuokuTestingGames($games) {
        return self::_setUnserializedValue('testing_games', $games);
    }

    public static function getDuokuTestingGames($arrInput) {
        return self::_getUnserializedValue('testing_games');
    }

    public static function clearDuokuGameNotAvailStatus() {
        $arrInput = array('key' => self::KEY_PREFIX.'available_status');

        return self::_doRedisQuery('DEL', $arrInput);
    }

    public static function setDuokuGameNotAvailStatus($arrInput) {
        $stats = self::_getUnserializedValue('available_status');

        if ($stats === false) {
            return false;
        }

        $key = strval($arrInput['kaifu_id']);

        if (empty($stats)) {
            $stats = array(
                $key => 1,
            );
        } else {
            $stats[$key] = 1;
        }

        return self::_setUnserializedValue('available_status', $stats);
    }

    public static function getDuokuGameNotAvailStatus() {
        return self::_getUnserializedValue('available_status');
    }

    public static function setDuokuGameUserStatus($arrInput) {
        $key = 'user_'.$arrInput['user_id'];

        $stats = self::_getUnserializedValue($key);

        if ($stats === false) {
            return false;
        }

        $kaifuId = strval($arrInput['kaifu_id']);

        if (empty($stats)) {
            $stats = array(
                $kaifuId => 1,
            );
        } else {
            $stats[$kaifuId] = 1;
        }

        return self::_setUnserializedValue($key, $stats);
    }

    public static function getDuokuGameUserStatus($userId) {
        $key = 'user_'.$userId;
        return self::_getUnserializedValue($key);
    }
}