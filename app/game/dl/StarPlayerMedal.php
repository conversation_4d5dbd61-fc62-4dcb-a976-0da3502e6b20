<?php
/**
 * 多酷星玩家勋章颁发
 * @authors tanxinyun
 * @date    2015-02-08 16:14:34
 */

class Dl_StarPlayerMedal {
    const REDIS_APP_NAME = 'bdrpecom';
    const KEY_PREFIX = 'duoku_starplayer_';

    private static $_redis = null;

    private static function _getRedis($appName) {
        if (self::$_redis == null) {
            self::$_redis = new Bingo_Cache_Redis($appName);
        }

        return self::$_redis;
    }

    private static function _doRedisQuery($method, $arrInput) {
        $redis = self::_getRedis(self::REDIS_APP_NAME);

        if (!$redis) {
            Bingo_Log::warning('failed to get redis: '.self::REDIS_APP_NAME);
            return false;
        }

        $ret = $redis->$method($arrInput);

        if ($ret === false || $ret['err_no'] !== 0) {
            Bingo_Log::warning('redis '.$method.' failure, input: '.json_encode($arrInput).', ret: '.json_encode($ret));
            return false;
        }

        return $ret['ret'][$arrInput['key']];
    }

    public static function rPush($userLevels) {
        $values = array();
        foreach ($userLevels as $level) {
            $values[] = serialize($level);
        }

        $arrInput = array(
            'key' => self::KEY_PREFIX.'userlevels',
            'value' => $values,
        );

        return self::_doRedisQuery('RPUSH', $arrInput);
    }

    public static function lPop() {
        $arrInput = array(
            'key' => self::KEY_PREFIX.'userlevels',
        );

        $ret = self::_doRedisQuery('LPOP', $arrInput);

        if (empty($ret)) {
            return $ret;
        }

        return unserialize($ret);
    }

    public static function lLen() {
        $arrInput = array(
            'key' => self::KEY_PREFIX.'userlevels',
        );

        return self::_doRedisQuery('LLEN', $arrInput);
    }

    public static function lRange($start = 0, $stop = -1) {
        $arrInput = array(
            'key' => self::KEY_PREFIX.'userlevels',
            'start' => $start,
            'stop' => $stop,
        );

        $ret = self::_doRedisQuery('LRANGE', $arrInput);

        if ($ret === false) {
            return $ret;
        }

        $recs = array();
        foreach ($ret as $rec) {
            $recs[] = unserialize($rec);
        }

        return $recs;
    }

    public static function setUserLevelUpTime($userId, $time) {
        $arrInput = array(
            'key' => self::KEY_PREFIX.'level_up_'.$userId,
            'value' => $time,
        );

        return self::_doRedisQuery('SET', $arrInput);
    }

    public static function getUserLevelUpTime($userId) {
        $arrInput = array(
            'key' => self::KEY_PREFIX.'level_up_'.$userId,
        );

        return self::_doRedisQuery('GET', $arrInput);
    }

    public static function clearUserLevelUpTime($userId) {
        $arrInput = array(
            'key' => self::KEY_PREFIX.'level_up_'.$userId,
        );

        return self::_doRedisQuery('DEL', $arrInput);
    }
}