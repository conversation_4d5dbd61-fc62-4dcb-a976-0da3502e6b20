<?php
/**
 * Created by PhpStorm.
 * User: kangqinmou
 * Date: 18-3-16
 * Time: 下午4:34
 */
class Dl_Gameforum_FeatureCard{

    const DATABASE_NAME = 'forum_gameforum';
    const TABLE_NAME    = 'feature_card';           // table: 特型卡片

    // feature_card表的所有字段以及其中的字符串字段
    private static $arrFields    = array('id', 'type', 'status', 'title', 'parent_id', 'floor', 'address', 'image', 'ext', 'op_uname', 'create_time', 'update_time');
    private static $arrStrFields = array('title', 'address', 'image', 'ext', 'op_uname', 'create_time', 'update_time');


    /**
     * @brief   查询feature_card表
     * @param   array  $arrInput   查询条件(不能为空)
     * @return  array
     */
    public static function selectFeatureCard($arrInput){

        // 获取查询条件
        $arrParams = array();
        $arrFields = array_flip(self::$arrFields);
        foreach($arrInput as $key => $value){
            if(is_array($value)){
                if(isset($value[0]) && isset($arrFields[$value[0]])){
                    $arrParams[] = $value;
                }
            }elseif(isset($arrFields[$key])){
                $arrParams[$key.'='] = in_array($key, self::$arrStrFields) ? strval($value) : intval($value);
            }
        }
        if(!$arrParams){
            Bingo_Log::warning('select\'s condition is empty, input: [' . serialize($arrInput). "]. \n");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'select\'s condition is empty.');
        }

        // 页码
        $intPageNo   = (isset($arrInput['pn']) && $arrInput['pn'] >= 1) ? intval($arrInput['pn']) : 1;
        $intPageSize = (isset($arrInput['ps']) && $arrInput['ps'] >= 1) ? intval($arrInput['ps']) : 1000;
        $strAppend   = 'order by create_time desc limit ' . ($intPageNo*$intPageSize - $intPageSize) . ", {$intPageSize} ";

        // 获取数据库句柄
        $db = self::getDB();
        if(!$db){
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL, array(), 'get database handle failed.');
        }

        // 执行select
        $fields = empty($arrInput['fields']) ? self::$arrFields : $arrInput['fields'];
        $arrRet = $db->select(self::TABLE_NAME, $fields, $arrParams, null, $strAppend);
        if(!is_array($arrRet)){
            Bingo_Log::warning('select feature card failed, input: [' . serialize($arrParams). '], output: [' . serialize($arrRet) . "]. \n");
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'select feature card failed.');
        }

        // 格式化数据
        foreach($arrRet as &$item){
            if(isset($item['address'])){
                $item['address'] = json_decode($item['address'], true);
                $item['address'] = is_array($item['address']) ? $item['address'] : array();
            }
            if(isset($item['ext'])){
                $item['ext'] = json_decode($item['ext'], true);
                $item['ext'] = is_array($item['ext']) ? $item['ext'] : array();
            }
        }
        unset($item);

        // 以id为key
        if(isset($arrRet[0]['id'])){
            $arrTemp = array();
            foreach($arrRet as $item){
                $arrTemp[$item['id']] = $item;
            }
            $arrRet = $arrTemp;
            unset($arrTemp);
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }




    /**
     * @brief   通过自增id更新feature_card表
     * @param   array  $arrInput
     * @return  array
     */
    public static function updateFeatureCardById($arrInput){

        // 参数验证
        $intId = isset($arrInput['id']) ? intval($arrInput['id']) : 0;
        if($intId <= 0){
            Bingo_Log::warning('auto_increment id should be bigger than zero, input: [' . serialize($arrInput) . "]. \n");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'id should be > 0');
        }

        // 获取update参数
        $arrParams = array();
        $arrFields = array('type','parent_id','floor','status','title','ext','op_uname','address','image','create_time','update_time');
        foreach($arrFields as $strField){
            if(!isset($arrInput[$strField])){
                continue;
            }
            $arrParams[$strField] = in_array($strField, self::$arrStrFields) ? strval($arrInput[$strField]) : intval($arrInput[$strField]);
        }
        if(!$arrParams){
            Bingo_Log::warning('update\'s fields is empty, input: [' . serialize($arrInput) . "]. \n");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'update\'s fields is empty.');
        }

        // 获取数据库句柄
        $db = self::getDB();
        if(!$db){
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL, array(), 'get database handle failed.');
        }

        // 执行update
        $intRet = $db->update(self::TABLE_NAME, $arrParams, array('id=' => $intId));
        if(false === $intRet){
            Bingo_Log::warning("update feature card failed, auto_increment id is [{$intId}], input: [" . serialize($arrParams) . '], output: [' . serialize($intRet) . "]. \n");
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'update feature card failed.');
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * @brief   向feature_card表插入记录
     * @param   array  $arrInput
     * @return  array
     */
    public static function insertFeatureCard($arrInput){

        // 获取要插入的数据
        $arrParams = array();
        $arrFields = array('type','parent_id','floor','status','title','ext','op_uname','address','image','create_time','update_time');
        foreach($arrFields as $strField){
            if(!isset($arrInput[$strField])){
                continue;
            }
            $arrParams[$strField] = in_array($strField, self::$arrStrFields) ? strval($arrInput[$strField]) : intval($arrInput[$strField]);
        }
        if(!$arrParams){
            Bingo_Log::warning('insert\'s data is empty, input: [' . serialize($arrInput). "]. \n");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'insert\'s data is empty.');
        }
        if(empty($arrParams['create_time'])){
            $arrParams['create_time'] = date('Y-m-d H:i:s');
        }

        // 获取数据库句柄
        $db = self::getDB();
        if(!$db){
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL, array(), 'get database handle failed.');
        }

        // 执行insert
        $intRet = $db->insert(self::TABLE_NAME, $arrParams);
        if(false === $intRet){
            Bingo_Log::warning('insert feature_card failed, input: [' . serialize($arrParams) . '], output: [' . serialize($intRet) . "]. \n");
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'insert feature card failed.');
        }

        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, array('id' => $db->getInsertID()));
    }



    /**
     * @brief   删除feature_card中的记录
     * @param   array  $arrInput
     * @return  array
     */
    public static function deleteFeatureCard($arrInput){

        // 获取查询条件
        $arrParams = array();
        $arrFields = array_flip(self::$arrFields);
        foreach($arrInput as $key => $value){
            if(is_array($value)){
                if(isset($value[0]) && isset($arrFields[$value[0]])){
                    $arrParams[] = $value;
                }
            }elseif(isset($arrFields[$key])){
                $arrParams[$key.'='] = in_array($key, self::$arrStrFields) ? strval($value) : intval($value);
            }
        }
        if(!$arrParams){
            Bingo_Log::warning('delete\'s condition is empty, input: [' . serialize($arrInput). "]. \n");
            return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR, array(), 'delete\'s condition is empty.');
        }

        // 获取数据库句柄
        $db = self::getDB();
        if(!$db){
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL, array(), 'get database handle failed.');
        }

        // 执行delete
        $intRet = $db->delete(self::TABLE_NAME, $arrParams);
        if(false === $intRet){
            Bingo_Log::warning('delete feature card failed, input: [' . serialize($arrParams) . '], output: [' . serialize($intRet) . "]. \n");
            return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL, array(), 'delete featur card failed.');
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS);
    }





    /**
     * @brief   获取数据库句柄
     * @param   null
     * @return  object
     */
    private static function getDB(){
        $mysqlDB = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if($mysqlDB && $mysqlDB->isConnected()) {
            $mysqlDB->charset('utf8');
            return $mysqlDB;
        } else {
            Bingo_Log::warning('db connect fail.');
            return null;
        }
    }




}

























