<?php
/**
 * Hao123吧头咨询模块数据源
 * @authors tanxinyun
 * @date    2015-07-16 18:08:22
 * @version $Id$
 */

class Dl_Hao123 {
    const REDIS_APP_NAME = 'tbscore';
    const KEY_PREFIX = '16Hao123_';


    const REDIS_KEY = 'game_news';

    private static $redis = null;

    /**
     * 获取redis实例
     * @param  [string] $appName [redis库名称]
     * @return [mixed]          [false/redis对象]
     */
    private static function getRedis($appName = self::REDIS_APP_NAME) {
        if (self::$redis == null) {
            self::$redis = new Bingo_Cache_Redis($appName);
        }

        return self::$redis;
    }

    /**
     * 执行redis查询
     * @param  [string]  $method      [方法名]
     * @param  [array]  $arrInput    [参数数组]
     * @param  integer $tryTimes    [执行次数]
     * @param  boolean $isMultiCall [是否批量查询]
     * @return [mixed]               [false/结果]
     */
    public static function doRedisQuery($method, $arrInput, $isMultiCall = false, $tryTimes = 2) {
        $redis = self::getRedis();

        if (!$redis) {
            Bingo_Log::warning('failed to get redis: '.self::REDIS_APP_NAME);
            return false;
        }

        if ($isMultiCall) {
            foreach ($arrInput['reqs'] as &$req) {
                $req['key'] = self::KEY_PREFIX.$req['key'];
            }
        } else {
            $arrInput['key'] = self::KEY_PREFIX.$arrInput['key'];
        }

        $ret = false;

        while ($ret === false && $tryTimes-- > 0) {
            $ret = $redis->$method($arrInput);
        }
        
        if (!$ret || $ret['err_no'] !== 0) {
            Bingo_Log::warning('redis '.$method.' failure, input: '.serialize($arrInput).', ret: '.serialize($ret));
            return false;
        }

        if ($isMultiCall) {
            return $ret['ret'];
        } else {
            return $ret['ret'][$arrInput['key']];
        }
    }

    /**
     * [save description]
     * @param  [type] $field [description]
     * @param  [type] $value [description]
     * @return [type]        [description]
     */
    public static function saveNews($field, $value) {
        $input = array(
            'key' => self::REDIS_KEY,
            'field' => $field,
            'value' => $value,
        );

        return self::doRedisQuery('HSET', $input);
    }

    /**
     * [getNews description]
     * @return [type] [description]
     */
    public static function getNews() {
        $input = array(
            'key' => self::REDIS_KEY,
            'field' => 'forum_game_news_hao123',
        );

        return self::doRedisQuery('HGET', $input);
    }
}
