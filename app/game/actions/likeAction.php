<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file indexAction.php
 * <AUTHOR>
 * @date 2013/06/23 14:21:51
 * @brief 
 *  
 **/

require(dirname(__file__)."/../../../libs/lib/rpcIdl/RpcIdlFulike.class.php");
require(dirname(__file__)."/../../../libs/lib/rpc/Rpc.class.php");

class likeAction extends Bingo_Action_Abstract {
    const SERVICE = 'perm';
    const METHOD = 'getLikeForumList';
    const SERVICE_FORUM = 'forum';
    const GET_FID_BY_FNAME = 'getFidByFname';
    public function execute() {
        $strtime = 'game_like';
        $user_id = intval(Tieba_Session_Socket::getLoginUid()); 
        $user_name = Tieba_Session_Socket::getLoginUname(); 
        $forum_name = Bingo_Http_Request::getGet('forum_name', '');
        $forum_name = Bingo_Encode::convert($forum_name, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
        $forum_name = urldecode($forum_name);
        $forum_id = Util_Forum::getFidByFname($forum_name);
        $bolLogin = ($user_id>0)?1:0;
        $output = array();
        $res['is_login'] = $bolLogin;
        if($bolLogin){
            $req = array(
                'fid' => $forum_id['forum_id'],
                'fname' =>$forum_name,
                'uid'   =>$user_id,
                'uip'   =>Bingo_Http_Ip::ip2long(Bingo_Http_Ip::getConnectIp()),
            );
            $res['uname'] = urlencode(Tieba_Session_Socket::getLoginUname());
            $ret = self::_bolLike($req);
            $res['is_like'] = $ret;
            $res['tbs'] =Tieba_Tbs::gene($bolLogin);
        }
        $res = json_encode($res);
        echo urldecode($res);
    }
    private static function _bolLike($req){
        if(intval($req['uid']) == 0){
            Bingo_Log::warning('get user info failed');
            return false;            
        }else{
            $strtime = 'game_like';
            Bingo_Timer::start($strtime);
            $ret = self::getLikeList($req);
            Bingo_Timer::end($strtime);
            if($ret == false ){
                Bingo_Log::warning('get like info failed');
                return false;
            }else{
                return $ret['balv']['is_like'];
            }
        }
    }

    public static function getLikeList($req){
         $arr = Tbapi_Core_Server::apicall("Frsforummember","getForumUserData",$req);
        if($arr == false){
            Bingo_Log::warning('get user likeList faild');
            return false;
        }
        return $arr;
    }

}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
