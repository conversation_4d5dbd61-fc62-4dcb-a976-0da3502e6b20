<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file indexAction.php
 * <AUTHOR>
 * @date 2013/06/23 14:21:51
 * @brief 
 *  
 **/


class awardByRegionAction extends Util_Base {
	
    public function execute() {
    	$session =  Util_Session::getSessionInfo();
    	$arrInput = array(
    			'user_id' => $session['intUid'],
    			'user_name' =>$session['strUname'],
    			'type'		=> Bingo_Http_Request::getPost('type', 0),
    			'tbs'		=> Bingo_Http_Request::getPost('tbs', 0),
    	);
       	if(!Tieba_Tbs::check($arrInput['tbs'],$session['intUid'])){
    		Bingo_Log::warning('tbs check error');
    		$ret['errno'] = Util_Error::CHECK_TBS_ERROR;
    		$ret['errmsg'] = Util_Error::getErrMsg($ret['errno']);
    		echo Bingo_String::array2json($ret);
    		return ;
    	}
		if($session['intUid']<=0){
			Bingo_Log::warning('user need login');
			$ret['errno'] = Tieba_Errcode::ERR_PARAM_ERROR;
			$ret['errmsg'] =  Tieba_Error::getErrmsg ( $ret['errno']);
			echo Bingo_String::array2json($ret);
			return ;
		}else{
			Bingo_Timer::start("awardByRegion");
			$arrRet = Tieba_Service::call('game','awardByRegion',$arrInput, NULL, NULL, 'post', 'php', 'gbk', 'local');
			Bingo_Timer::end("awardByRegion");
			if($arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning('luck draw faild,the input ='.serialize($arrInput).serialize($arrRet));
				echo Bingo_String::array2json($arrRet);
			}else{
				echo Bingo_String::array2json($arrRet);
			}
		}
		
	}
}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
