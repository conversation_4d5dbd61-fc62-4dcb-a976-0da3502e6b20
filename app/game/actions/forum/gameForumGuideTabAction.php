<?php

/**
 * 游戏吧的资讯查看更多接口，包含tab结构，所有labellist，还有根据tab label查询出来帖子分页列表
 * Created by PhpStorm.
 * User: shijiwen
 * Date: 8/1 2017
 * Time: 14:45
 */
class gameForumGuideTabAction extends Util_Basenew
{
	const STATUS_PASS = 1;
	const STATUS_UNPASS = 2;
	const STATUS_DELETE = 3;
	
	public static $_GAME_FEED_SOURCE_SUB_TAB_NAME_MAP = array(
		'全部资讯' => -1,
		'小编推荐' => 0,
		'吧友分享' => 3,
		'官方推荐' => 21,
	);
	
	/**
	 * _execute
	 * @return mixed
	 */
	public function _execute()
	{
		try{
			$intForumId = intval(Bingo_Http_Request::get("forum_id", -1));
			if($intForumId <= 0){
				throw new Util_Exception('params error!', Tieba_Errcode::ERR_PARAM_ERROR);
			}
			$intPn           = intval(Bingo_Http_Request::get('pn', 0));
			$intPs           = intval(Bingo_Http_Request::get('ps', 10));
			$intSubTabId     = intval(Bingo_Http_Request::get("sub_tab_id", -1));
			$strSubTabName   = strval(Bingo_Http_Request::get("sub_tab_name", ''));
			$intSubLabelId   = intval(Bingo_Http_Request::get("sub_label_id", 0));
			$intNeedTabStuct = intval(Bingo_Http_Request::get("need_tab_stuct", 1));
			$intPosition     = intval(Bingo_Http_Request::get("position", 0));
			$intTabId        = intval(Bingo_Http_Request::get("tab_id", 1)); // 默认是1资讯
			$entry           = array();
			$strService      = 'beyond';
			$strMethod       = 'getGameForumType';
			$arrReq          = array(
				"forum_id" => $intForumId,
			);
			$arrOut          = Tieba_Service::call($strService, $strMethod, $arrReq, null, null, 'post', null);
			if($arrOut === false || $arrOut['errno'] != Alalib_Conf_Error::ERR_SUCCESS || !isset($arrOut['data'])){
				$strLog = "$strService::$strMethod call fail. input[".serialize($arrReq)."] output[".serialize($arrOut)."]";
				Bingo_Log::fatal($strLog);
			}
			$rootTabId = 0;
			if($intTabId == 4 && $arrOut['data']['guide_type_id'] > 0){
				$rootTabId   = $arrOut['data']['guide_type_id']; // =4 攻略tab
				$intSubTabId = 0; // 本接口主要给资讯用，如果资讯页面用tab_id=4来调用，说明可能是调试，暂时给玩家宝典页
			}
			if($intTabId == 1 && $arrOut['data']['info_type_id'] > 0){
				$rootTabId = $arrOut['data']['info_type_id']; // =1 资讯tab
			}
			if($rootTabId == 0){ // 没获取到有效的rootTabId，也就是feedlist表里的type,没法继续了
				Bingo_Log::fatal("Invalid rootTabId for $intForumId : $intTabId".serialize($arrOut['data']));
				throw new Util_Exception('params error!', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
			}
			if($intNeedTabStuct || $intSubTabId < 0){
				$arrReq     = array(
					"forum_id"    => $intForumId,
					"root_tab_id" => $rootTabId,
				);
				$strService = 'game';
				$strMethod  = 'findAllTabLabelByForumId';
				$arrOut     = Tieba_Service::call($strService, $strMethod, $arrReq, null, null, 'post', 'php', 'gbk');
				if($arrOut === false || $arrOut['errno'] != Alalib_Conf_Error::ERR_SUCCESS){
					$strLog = "$strService::$strMethod call fail. input[".serialize($arrReq)."] output[".serialize($arrOut)."]";
					Bingo_Log::fatal($strLog);
				}
				$arrOut['data'] = Bingo_Encode::convert($arrOut['data'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
				//Bingo_Log::warning("get tab stuct in game forum:  $intForumId" . var_export($arrOut, true));
				$entry = $arrOut['data'];
				if(!empty($entry)){
					$intSubTabId = $entry[0]['id'];
					if(!empty($entry[0]["sub_label_list"])){
						$intSubLabelId = $entry[0]["sub_label_list"][0]['id'];
					}
				}else{
					Bingo_Log::warning("no sub_tab for: ".$intForumId);
					throw new Util_Exception('no sub_tab!', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
				}
			}else{
				Tieba_Stlog::addNode("sub_tab_id", $intSubTabId);
				Tieba_Stlog::addNode("sub_label_id", $intSubLabelId);
				Tieba_Stlog::addNode("forum_id", $intForumId);
			}
			//get thread for this sub and label
			$arrThreadList = array();
			$intHasMore    = 0;
			$boolFromHot   = false;
			$arrReq        = array(
				'sub_tab_id'   => $intSubTabId,
				'sub_label_id' => $intSubLabelId,
				'status'       => self::STATUS_PASS,
				"type"         => $rootTabId, //feed_list表的type就是game_sub_tab表的root_tab_id，攻略或资讯，每个吧不一样，如王者荣耀是134
				"sub_type"     => 0, // feed
				"pn"           => $intPn + 1,
				"sz"           => $intPs,
			);
			//如果是资讯页，根据二级分类tab名称来定位source，索引数据
			if($intTabId == 1){
				$arrReq['pn'] = $intPn;
				$source       = self::$_GAME_FEED_SOURCE_SUB_TAB_NAME_MAP[$strSubTabName];
				//if(!empty($source)){
				if($source >= 0){
					$arrReq['source'] = $source;
				}
				unset($arrReq['sub_tab_id']);
				unset($arrReq['sub_label_id']);
				//}
			}
			$arrOut = Tieba_Service::call('beyond', 'getFeedListWithOrderByCondation', $arrReq, null, null, 'post', null, 'utf-8');
			if($arrOut === false || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
				$strLog = "getFeedListByCondation fail. input[".serialize($arrReq)."] output[".serialize($arrOut)."]";
				Bingo_Log::fatal($strLog);
			}else{
				$arrOut['data'] = Bingo_Encode::convert($arrOut['data'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
				//feedInfo
				$arrFeedInfo = array();
				foreach($arrOut['data']['list'] as $item){
					$arrFeedInfo[$item['thread_id']] = $item;
				}
				$intHasMore   = $arrOut['data']['page']['totalData'] >= $intPn * $intPs ? 1 : 0;
				$arrThreadIds = array_column($arrOut['data']['list'], 'thread_id');
				//source
				$arrSrcIds    = array_column($arrOut['data']['list'], 'source');
				$boolFromHot  = true;
				$arrThreadIds = array_unique($arrThreadIds);
				if($intPn == 0 && empty($arrThreadIds)){ //如果第一页就没数据，应该启用兜底热门数据
					$arrReq = array(
						'hot_status' => 0, // HOT_STATUS_PASS = 0;
						"type"       => 6, // 类目id
						"sub_type"   => 0, // feed
						"pn"         => $intPn + 1,
						"sz"         => $intPs,
					);
					$arrOut = Tieba_Service::call('beyond', 'getHotListByCondation', $arrReq, null, null, 'post', null);
					if($arrOut === false || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
						$strLog = "getHotListByCondation fail. input[".serialize($arrReq)."] output[".serialize($arrOut)."]";
						Bingo_Log::fatal($strLog);
					}else{
						$arrOut['data'] = Bingo_Encode::convert($arrOut['data'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
						$intHasMore     = $arrOut['data']['page']['totalData'] >= $intPn * $intPs ? 1 : 0;
						$arrThreadIds   = array_column($arrOut['data']['list'], 'thread_id');
						Bingo_Log::warning("getHotListByCondation: ".serialize($arrThreadIds));
					}
				}
				$arrThreadIds = array_unique($arrThreadIds);
				if(empty($arrThreadIds)){
					$arrRet = array(
						'sub_tab_list' => $entry,
						'thread_list'  => array(),
						'has_more'     => 0,
					);
					Bingo_Log::warning("Empty $arrThreadIds for: $intForumId : $intSubTabId : $intSubLabelId");
					
					return $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, "ok", $arrRet);
				}
				$arrReq     = array(
					'thread_ids'      => $arrThreadIds,
					'need_abstract'   => 1,
					'forum_id'        => $intForumId,
					'need_photo_pic'  => 1,
					'need_user_data'  => 0,
					'icon_size'       => 1,
					'need_forum_name' => 1,
					'call_from'       => 'client_frs',
				);
				$strService = 'post';
				$strMethod  = 'mgetThread';
				$arrOut     = Tieba_Service::call($strService, $strMethod, $arrReq, null, null, 'post', null, 'utf-8');
				if($arrOut === false || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
					$strLog = "$strService::$strMethod call fail. input[".serialize($arrReq)."] output[".serialize($arrOut)."]";
					Bingo_Log::fatal($strLog);
					throw new Util_Exception('no sub_tab!', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
				}
				//$arrOut['output'] = Bingo_Encode::convert($arrOut['output'],Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
				//Bingo_Log::warning("mgetThread out===> " . var_export($arrOut['output']['thread_list'], true));
				$arrThreadList = isset($arrOut['output']['thread_list']) ? array_values($arrOut['output']['thread_list']) : array();
				//对每条资讯写入数据来源
				$arrFeedInfo = Bingo_Encode::convert($arrFeedInfo, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
				foreach($arrThreadList as $key => &$value){
					//给每条资讯添加审核时间,同时用feed表中的标题和图片取代帖子的标题和封面
					$value['audit_time'] = intval($arrFeedInfo[$value['thread_id']]['audit_time']);
					$value['title']      = $arrFeedInfo[$value['thread_id']]['title'];
					if(isset($value['media'][0]['water_pic']) && isset($arrFeedInfo[$value['thread_id']]['feed_ext']['thumb'])){
						$value['media'][0]['water_pic'] = $arrFeedInfo[$value['thread_id']]['feed_ext']['thumb'];
					}
					if($arrSrcIds[$key] > Util_Define::GAME_FEED_SOURCE_OFFICIAL_START){
						$value['tieba_game_information_source'] = Util_Define::$_GAME_FEED_SOURCE_MAP[self::GAME_FEED_SOURCE_OFFICIAL_START];
					}else{
						$value['tieba_game_information_source'] = Util_Define::$_GAME_FEED_SOURCE_MAP[$arrSrcIds[$key]];
					}
				}
				$arrRet = array();
				unset($value);
				foreach($arrThreadList as $key => $value){
					if($value['is_deleted'] == 1){
						continue;
					}
					$arrRet[$key] = $value;
				}
				$arrThreadList = $arrRet;
				array_multisort(array_column($arrThreadList, 'audit_time'), SORT_DESC, $arrThreadList);
				$arrThreadList = Bingo_Encode::convert($arrThreadList, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
				/*if (!$boolFromHot){
					foreach ($arrThreadList as &$item) {
						$item["sub_tab_id"] = $intSubTabId ;
						$item["sub_label_id"] = $intSubLabelId ;
					}
				}*/
			}
			$arrRet = array(
				'sub_tab_list' => $entry,
				'thread_list'  => $arrThreadList,
				'has_more'     => $intHasMore,
			);
			
			return $this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, "ok", $arrRet);
		}catch(Util_Exception $e){
			$errno  = $e->getCode();
			$errmsg = $e->getMessage();
			Bingo_Log::warning('errno='.$errno.' msg='.$errmsg);
			$this->_jsonRet($errno, $errmsg);
		}
	}
}