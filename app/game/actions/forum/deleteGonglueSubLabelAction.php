<?php
/***************************************************************************
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
 **************************************************************************/

/**
 * @file   deleteGonglueSubLabelAction.php
 * <AUTHOR>
 * @date   2017-08-04
 * @brief  吧主添加攻略子label
 **/
class deleteGonglueSubLabelAction extends Util_Basenew
{
	
	/**
	 * @param :null
	 *
	 * @return:null
	 **/
	public function _getPrivateInfo()
	{
		$arrPrivateInfo                = array();
		$arrPrivateInfo['check_login'] = true;
		$arrPrivateInfo['need_login']  = true;
		$arrPrivateInfo['ispv']        = 0;
		
		return $arrPrivateInfo;
	}
	
	/**
	 * @param :null
	 *
	 * @return:null
	 **/
	public function _checkPrivate()
	{
		return true;
	}
	
	/**
	 * @param :null
	 *
	 * @return:null
	 **/
	public function _execute()
	{
		try{
			//参数获取
			$forum_id  = intval(Bingo_Http_Request::get('forum_id', 0));
			$id        = intval(Bingo_Http_Request::get('id', 0));
			$user_id   = intval($this->_arrUserInfo['user_id']);
			$user_name = strval($this->_arrUserInfo['user_name']);
			//            if(self::barHostChecker(array('forum_id' => $forum_id, 'user_id' => $user_id)) == false){
			//                //参数校验
			//                throw new Util_Exception('user is not allow!', Tieba_Errcode::ERR_PARAM_ERROR);
			//            }
			if($forum_id <= 0){
				//参数校验
				throw new Util_Exception('params error!', Tieba_Errcode::ERR_PARAM_ERROR);
			}
			$arrServiceInput  = array('forum_id' => $forum_id,
			                          'id'       => $id,
			                          'status'   => 2,
			                          'op_uname' => $user_name,);
			$strServiceName   = "game";
			$strServiceMethod = "updateGameSubLabel";
			$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "gbk");
			if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
				$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
				Bingo_Log::fatal($strLog);
				$arrOutput["data"] = array();
				throw new Util_Exception('params error!', Tieba_Errcode::ERR_PARAM_ERROR);
			}
			$this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, 'success', array());
		}catch(Util_Exception $e){
			$errno  = $e->getCode();
			$errmsg = $e->getMessage();
			Bingo_Log::warning('errno='.$errno.' msg='.$errmsg);
			//$this->_jsonRet($errno, $errmsg);
			$this->_jsonRet($errno, $errmsg);
		}
	}
	
	/**
	 * @param       $intErrno
	 * @param       $strErrmsg
	 * @param array $arrRet
	 *
	 * @return bool
	 */
	private function _returnErr($intErrno, $strErrmsg, $arrRet = array())
	{
		$this->_error($intErrno, $strErrmsg);
		$this->_objResponse->setOutData($arrRet);
		
		return false;
	}
	
	/**
	 * @param $errno
	 * @param null $data
	 *
	 * @return array
	 */
	private static function barHostChecker($arrInput)
	{
		if(empty($arrInput['user_id']) || empty($arrInput['forum_id'])){
			return false;
		}
		$arrServiceInput  = array('forum_id' => intval($arrInput['forum_id']),);
		$strServiceName   = "perm";
		$strServiceMethod = "getManagerAndAssistList";
		$arrOutput        = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", null, "utf-8");
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
			$strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
			Bingo_Log::fatal($strLog);
			$arrOutput["data"] = array();
		}
		foreach($arrOutput['output']['assist'] as $assistInfo){
			if($assistInfo['user']['user_id'] == $arrInput['user_id']){
				return true;
			}
		}
		foreach($arrOutput['output']['manager'] as $managerInfo){
			if($managerInfo['user']['user_id'] == $arrInput['user_id']){
				return true;
			}
		}
		
		return false;
	}
	
	/**
	 * @brief 参数校验
	 *
	 * @param str
	 *
	 * @return arr
	 **/
	protected function _checkParam()
	{
		$arrData = array();
		
		return $arrData;
	}
	
}
