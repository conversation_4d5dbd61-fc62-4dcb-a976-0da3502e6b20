<?php

/**
 * Created by PhpStorm.
 * User: caowu
 * Date: 17/9/18
 * Time: 下午3:20
 */
class getYinhuaAction extends Alalib_Action_BaseAction {

    const YINHUA_NUMBER = 20;

    /**
     * 获取参数
     * @param null
     * @return {Array} $arrPrivateInfo
     * */
    public function _getPrivateInfo() {
        $arrPrivateInfo['forum_id'] = intval(Bingo_Http_Request::get('forum_id', 0));
        $arrPrivateInfo['check_login'] = true;
        $arrPrivateInfo['check_tbs'] = false;
        $this->_objRequest->addStrategy('check_sign', false);
        return $arrPrivateInfo;
    }

    /**
     * 参数校验
     * @param null
     * @return true
     * */
    public function _checkPrivate(){
        return true;
    }

    /**
     * @return bool
     */
    public function _execute() {

        //参数获取
        $intUserId = $this->_objRequest->getCommonAttr('user_id');
        $intForumId = $this->_objRequest->getPrivateAttr('forum_id');
        if(empty($intUserId)) {
            Bingo_Log::warning("error param, empty user_id");
            return $this->errRet(Tieba_Errcode::ERR_USER_NOT_LOGIN);
        }
        if(empty($intForumId)) {
            Bingo_Log::warning("error param, empty forum_id");
            return $this->errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrServiceInput = array(
            'user_id' => $intUserId,
        );
        $strServiceName = "game";
        $strServiceMethod = "checkUserHasGetYinhua";
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,"post",null,"gbk");
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = "call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            $arrOutput['errno'] = isset($arrOutput['errno']) ? $arrOutput['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            return $this->errRet($arrOutput['errno']);
        }
        $boolHasGet = intval($arrOutput['data']);
        if(!empty($boolHasGet)) {
            Bingo_Log::warning("user has got yinhua this day");
            return $this->errRet(999999, array(), "每天只能领一次,今日已经领取");
        }

        //发印花
        $arrServiceInput = array(
            'user_id' => $intUserId,
            'forum_id' => $intForumId,
            'yinhua_num' => self::YINHUA_NUMBER,
            'meilan_type' => 1,
        );
        $strServiceName = "fortune";
        $strServiceMethod = "dealMeilanYinhua";
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,"post",null,"utf-8");
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = "call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            $arrOutput['errno'] = isset($arrOutput['errno']) ? $arrOutput['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            return $this->errRet($arrOutput['errno']);
        }

        //记录已经领取了
        $arrServiceInput = array(
            'user_id' => $intUserId,
        );
        $strServiceName = "game";
        $strServiceMethod = "markUserHasGetYinhua";
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,"post",null,"gbk");
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = "call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
        }
        return $this->errRet(Tieba_Errcode::ERR_SUCCESS);
    }
}