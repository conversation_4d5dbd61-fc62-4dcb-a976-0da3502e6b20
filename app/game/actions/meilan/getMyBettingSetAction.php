<?php

/**
 * Created by PhpStorm.
 * User: caowu
 * Date: 17/9/18
 * Time: 下午9:53
 */
class getMyBettingSetAction extends Alalib_Action_BaseAction {

    /**
     * 获取参数
     * @param null
     * @return {Array} $arrPrivateInfo
     * */
    public function _getPrivateInfo() {
        $arrPrivateInfo['pn'] = intval(Bingo_Http_Request::get('pn', 1));
        $arrPrivateInfo['ps'] = intval(Bingo_Http_Request::get('ps', 30));
        $arrPrivateInfo['check_login'] = true;
        $arrPrivateInfo['check_tbs'] = false;
        $this->_objRequest->addStrategy('check_sign', false);
        return $arrPrivateInfo;
    }

    /**
     * 参数校验
     * @param null
     * @return true
     * */
    public function _checkPrivate(){
        return true;
    }

    /**
     * @return bool
     */
    public function _execute() {

        //参数获取
        $intUserId = $this->_objRequest->getCommonAttr('user_id');
        $intPn = $this->_objRequest->getPrivateAttr('pn');
        $intPs = $this->_objRequest->getPrivateAttr('ps');
        if(empty($intUserId)) {
            Bingo_Log::warning("error param, empty user_id");
            return $this->errRet(Tieba_Errcode::ERR_USER_NOT_LOGIN);
        }

        $arrServiceInput = array(
            'pn' => $intPn,
            'ps' => $intPs,
            'cond' => " user_id=$intUserId ",
        );
        $strServiceName = "game";
        $strServiceMethod = "selectMeilanBetInfoFromDB";
        $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,"post",null,"gbk");
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
            $strLog = "call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);
            $arrOutput['errno'] = isset($arrOutput['errno']) ? $arrOutput['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            return $this->errRet($arrOutput['errno']);
        }
        $arrRetData = $arrOutput['data'];
        $arrRet = $arrOutput['data']['rows'];

        $arrQueryWords = array();
        foreach ($arrRet as $value) {
            $strTeamA = $value['team_a'];
            $strTeamB = $value['team_b'];
            if(!in_array($strTeamA, $arrQueryWords)) {
                $arrQueryWords[] = $strTeamA;
            }
            if(!in_array($strTeamB, $arrQueryWords)) {
                $arrQueryWords[] = $strTeamB;
            }
        }

        if(!empty($arrQueryWords)) {
            $arrServiceInput = array(
                'query_words' => $arrQueryWords,
            );
            $strServiceName = "forum";
            $strServiceMethod = "getFidByFname";
            $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,"post",null,"gbk");
            if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
                $strLog = "call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
                Bingo_Log::warning($strLog);
                $arrOutput['errno'] = isset($arrOutput['errno']) ? $arrOutput['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
                return $this->errRet($arrOutput['errno']);
            }

            $arrWordMapForumId = array();
            $arrForumIds = array();
            foreach ($arrOutput['forum_id'] as $value) {
                $arrWordMapForumId[$value['qword']] = $value['forum_id'];
                $arrForumIds[] = $value['forum_id'];
            }

            if(!empty($arrForumIds)) {
                $arrServiceInput = array(
                    'forum_id' => $arrForumIds,
                );
                $strServiceName = "forum";
                $strServiceMethod = "mgetBtxInfo";
                $arrOutput = Tieba_Service::call($strServiceName,$strServiceMethod,$arrServiceInput,null,null,"post",null,"gbk");
                if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]){
                    $strLog = "call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
                    Bingo_Log::warning($strLog);
                    $arrOutput['errno'] = isset($arrOutput['errno']) ? $arrOutput['errno'] : Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
                    return $this->errRet($arrOutput['errno']);
                }
            }

            $arrWordMapAvatar = array();
            foreach ($arrQueryWords as $word) {
                $fid = intval($arrWordMapForumId[$word]);
                if(isset($arrOutput['output'][$fid]['card']['avatar'])) {
                    $arrWordMapAvatar[$word] = $arrOutput['output'][$fid]['card']['avatar'];
                }
                else {
                    $arrWordMapAvatar[$word] = 'http://tb1.bdstatic.com/tb/r/image/2013-11-18/0dbb3d89d98c497ef6ef92486fd59d81.png';
                }
            }

            foreach ($arrRet as $key => &$value) {
                $strTeamA = $value['team_a'];
                $strTeamB = $value['team_b'];
                $strTeamBet = $value['bet_team_name'];
                $intResult = intval($value['result']);
                if(($strTeamA == $strTeamBet && 1 == $intResult) || ( $strTeamB == $strTeamBet && 3 == $intResult)) {
                    $value['bet_result'] = 1;
                }
                else if(1 != $intResult && 3 != $intResult) {
                    $value['bet_result'] = 0;
                }
                else {
                    $value['bet_result'] = 3;
                }

                $value['team_a_avatar'] = $arrWordMapAvatar[$strTeamA];
                $value['team_b_avatar'] = $arrWordMapAvatar[$strTeamB];
            }
        }

        $arrRetData['rows'] = $arrRet;

        return $this->errRet(Tieba_Errcode::ERR_SUCCESS, array('data' => $arrRetData));
    }
}