<?php
/**
 * 手游先锋吧头游戏区
 * @authors tanxinyun
 * @date    2015-11-18 11:05:48
 * @version $Id$
 */

class gameAction extends Util_Base {

    const TYPE_FRESH = 'fresh';
    const TYPE_APPOINTING = 'appointing';

    /**
     * 系统调用
     * @return [type] [description]
     */
    private function makeCalls() {
        $results = array();

        $multi = new Tieba_Multi('syxf_game');

        $multi->register(
            self::TYPE_FRESH,
            new Tieba_Service('game'),
            array(
                'serviceName' => 'game',
                'method' => 'listSYXFGame',
                'input' => array(
                    'category' => 1,
                    'status' => 1,
                    'pn' => 1,
                    'rn' => 30,
                ),
            )
        );

        $multi->register(
            self::TYPE_APPOINTING,
            new Tieba_Service('game'),
            array(
                'serviceName' => 'game',
                'method' => 'listSYXFGame',
                'input' => array(
                    'category' => 2,
                    'status' => 1,
                    'pn' => 1,
                    'rn' => 30,
                ),
            )
        );

        $rets = $multi->call();

        foreach ($rets as $name => $ret) {
            if (!$ret || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("$name call failed, ret: ".serialize($ret));
                $results[$name] = null;
                continue;
            }

            $results[$name] = $ret['data']['list'];
        }

        return $results;
    }

    /**
     * 主逻辑
     * @return [array] [description]
     */
    public function execute() {
    	$results = $this->makeCalls();

    	$this->_jsonRet(Tieba_Errcode::ERR_SUCCESS, 'success', $results);
    }
}
