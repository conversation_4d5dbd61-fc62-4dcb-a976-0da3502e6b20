<?php
class giveMemberCheckCodeAction extends Bingo_Action_Abstract{
	
	public function execute() {
		$member_code = strval ( Bingo_Http_Request::get ( 'member_code', '' ) );
		$user_id = intval(Tieba_Session_Socket::getLoginUid());
		$forum_id = 366500;
		
		if (empty($member_code) || empty($user_id)) {
			Bingo_Log::warning ( 'invalid params!' );
			$errno = Tieba_Errcode::ERR_PARAM_ERROR;
			$result = array ('errno'  => $errno, 
			                 'errmsg' => Tieba_Error::getErrmsg ( $errno ) 
			          );
			echo Bingo_String::array2json($result,'utf-8');
		}
		
		$arrInput = array ('member_code' => $member_code,
		                   'user_id'     => $user_id,
		                   'forum_id'    => $forum_id,
		            );
		$arrOut = Tieba_Service::call ( 'game', 'checkMemberCode', $arrInput, NULL, NULL, 'post', 'php', 'utf-8' );
		
		if (false == $arrOut || Tieba_Error::ERR_SUCCESS!=$arrOut['errno']) {
			Bingo_Log::warning ( 'checkMemberCode method fail' );
			$errno = Tieba_Errcode::ERR_UNKOWN;
			$result = array ('errno'  => $errno, 
			                 'errmsg' => Tieba_Error::getErrmsg ( $errno ) 
			          );
			echo Bingo_String::array2json($result,'utf-8');
		}
		
	
		$errno = Tieba_Error::ERR_SUCCESS;
		$result = array ('errno'  => $errno, 
		                   'errmsg' => Tieba_Error::getErrmsg ( $errno ),
			        );
		echo Bingo_String::array2json($result,'utf-8');
   }
}