<?php
class Data_Member{
	const APP_NAME = 'bdrpecom';
		
	private static $_redis = null;	
	
	private static function init() {
        if(self::$_redis == null) {
            self::$_redis = new Bingo_Cache_Redis(self::APP_NAME);
            if(self::$_redis == false) {
                Bingo_Log::warning("init redis init fail:" . var_export(Bd_RalRpc::get_error(), true));
                return false;
            }
        }
        return true;
    }
	
	private static function _errRet($errno){
		return array(
			'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
		);
	}
	
	public static function pushMemberCode($arrInput){
		if (empty($arrInput['member_codes']) || empty($arrInput['key'])){
			Bingo_Log::warning('param error.game_name:'.$arrInput['member_codes']);
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrCodes = Tieba_Service::getArrayParams($arrInput, 'member_codes');
		if (!is_array($arrCodes) || empty($arrCodes)){
			Bingo_Log::warning('member_codes param error');
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		if (!self::init()){
			Bingo_Log::warning('init fail');
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		$arrReqs = array();
		foreach ($arrCodes as $strCode){
			$input = array(
		        'field' => trim($strCode),
		        'value' => 1,
		    );		       
			$arrReqs[] = $input;
		}
		$strKey = trim($arrInput['key']);
		$arrInput =  array(
			'key' => $strKey,
        	'fields' => $arrReqs,
    	);
    	$ret = self::$_redis->HMSET($arrInput);
    	if (!$ret || $ret['err_no'] != 0){
    		Bingo_Log::warning('set game member codes fail'.serialize($ret));
    		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
    	}

		$arrInput =  array(
        	'key' => $strKey,//'member_code_game',
    	);
    	$ret = self::$_redis->HLEN($arrInput);
    	if (!$ret || $ret['err_no'] != 0){
    		Bingo_Log::warning('get game member codes fail'.serialize($ret));
    	}
    	return array(
    		'errno' => Tieba_Errcode::ERR_SUCCESS,
			'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
    		'count' => intval($ret['ret'][$strKey]),
    	);
	}
	public static function getMemberCode($arrInput){
		if (empty($arrInput['key'])){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		if (!self::init()){
			Bingo_Log::warning('init fail');
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		$strKey = trim($arrInput['key']);
		$arrInput =  array(  
            'key' => $strKey,
        );      
        $ret = self::$_redis->HLEN($arrInput);
        if (!$ret || $ret['err_no'] != 0){
            Bingo_Log::warning('del common key fail'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }       
        $code = self::$_redis->HKEYS($arrInput);
        return array(
            'errno' => Tieba_Errcode::ERR_SUCCESS,
            'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
            'count' => intval($ret['ret'][$strKey]),
        	'codes' => $code['ret'][$strKey],
        );   
	}
	public static function delMemberCode($arrInput){
		if (empty($arrInput['key'])){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		if (!self::init()){
			Bingo_Log::warning('init fail');
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		$strKey = trim($arrInput['key']);
		$arrInput =  array(  
            'key' => $strKey,
        );      
        $ret = self::$_redis->DEL($arrInput);
        if (!$ret || $ret['err_no'] != 0){
            Bingo_Log::warning('del common key fail'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }       
        $ret = self::$_redis->HLEN($arrInput);
        return array(
            'errno' => Tieba_Errcode::ERR_SUCCESS,
            'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
            'count' => intval($ret['ret'][$strKey]),
        );   
	}
	public static function getCode($arrInput){
		if(!isset($arrInput['code']) || !isset($arrInput['forum_id'])){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		if (!self::init()){
			Bingo_Log::warning('init fail');
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		$arrReq =  array(  
            'key' => 'member_code_game_'.$arrInput['forum_id'],
			'field' => $arrInput['code'],
        );      
        $ret = self::$_redis->HGET($arrReq);
        if (!$ret || $ret['err_no'] != 0){
            Bingo_Log::warning('get key fail'.serialize($ret));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }       
        return array(
            'errno' => Tieba_Errcode::ERR_SUCCESS,
            'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
        	'codes' => $ret['ret'],
        );   
	}
	public static function checkMemberCode($arrInput) {
		$member_code = $arrInput['member_code'];
		$user_id     = $arrInput['user_id'];
		$forum_id    = $arrInput['forum_id'];
		
		if (empty($member_code) || empty($user_id)||empty($forum_id)) {
			Bingo_Log::warning ( 'param error: $member_code,$user_id,$forum_id ');
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		if (! self::init ()) {
			Bingo_Log::warning ( 'init fail' );
			return self::_errRet ( Tieba_Errcode::ERR_DL_CALL_FAIL );
		}
		
		//判断当前用户是否具有指定吧（此处为ff14）的吧特权
		$arrInput = array(
		       'user_id' => $user_id,
		       'forum_id' => $forum_id,
	           'type' => 1,
		);
		
		$ret = Tieba_Service::call('tbmall','isForumMember', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
		
		 // 0：用户没有该吧特权    ；  1： 用户拥有该吧会员
		 //无需购买  ERR_DONT_NEET_BUY  错误号:2270001
		if(Tieba_Errcode::ERR_SUCCESS==$ret['errno'] && 1==$ret['data']['is_member']){
			return array ('errno' => Tieba_Errcode::ERR_DONT_NEET_BUY, 'errmsg' => Tieba_Error::getErrmsg ( Tieba_Errcode::ERR_DONT_NEET_BUY ));
		}else if(false==$ret){
            return self::_errRet(Tieba_Errcode::ERR_NO_RECORD);
		}
	
		//查询该激活码
		$input_get = array ('key' => 'member_code_game_'.$forum_id, 
		                    'field' => $member_code ,
		             );
		             
		$ret_get = self::$_redis->HGET ( $input_get );
		
		//  ERR_NO_RECORD  记录不存在   错误号：2270015
		if (false == $ret_get || '1' != strval($ret_get['ret']['member_code_game_'.$forum_id])){
			Bingo_Log::warning ( 'get member_code from db fail' );
			return self::_errRet ( Tieba_Errcode::ERR_NO_RECORD );
		}

		//删除该激活码
	    $input_del = array ('key' => 'member_code_game_'.$forum_id, 
	                      'field' => array($member_code),
	                 );	
		$ret_del = self::$_redis->HDEL ( $input_del );
		if (false == $ret_del || Tieba_Errcode::ERR_SUCCESS!=$ret_del['errno']) {
			Bingo_Log::warning ( 'del member_code from db fail' );
			return self::_errRet ( Tieba_Errcode::ERR_DL_CALL_FAIL ); //删除失败
	    }
		
	    //删除成功则送会员
	    $arrInput = array ('props_id' => '3'.$forum_id, 
			                    'user_id' => $user_id, 
			                    'buy_num' => 1, 
			                    'ff14'    => 1, 
			                    'props_scores' => 0,
			                  );                  
	    $arrOut = Tieba_Service::call ( 'tbmall', 'buyOneProps', $arrInput, NULL, NULL, 'post', 'php', 'utf-8' );	
	    //若送会员失败，则redis重新set该激活码
	    if( !isset($arrOut['errno'])||Tieba_Errcode::ERR_SUCCESS!=$arrOut['errno']){
	         $arrInput = array ('key'   => 'member_code_game_'.$forum_id, 
	         	                'field' => $member_code,
	         	                'value' => 1,
	         	          );
		     $ret = self::$_redis->HSET($arrInput);
		     Bingo_Log::warning('give member fail , reset the member_code');
		     return self::_errRet ( Tieba_Errcode::ERR_UNKOWN );
	     }
	  
		return array(
			        'errno'=>Tieba_Errcode::ERR_SUCCESS,
					'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
					'data' => array(
						           'end_time'=>$arrOut['data']['end_time'],
						     ),
		      	);
	}

}
