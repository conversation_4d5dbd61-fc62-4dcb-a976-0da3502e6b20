<?php
class Data_Clickzan {
	private static $_redis = null;
	
	const APP_NAME = 'bdrpecom';
	const PRE_NAME = 'Clickzan';
	private static function _getRedis($strAppname){
		if(null === self::$_redis){
			$redis = new Bingo_Cache_Redis($strAppname);
			self::$_redis = $redis;
		}
		return self::$_redis;
	}
	

	private static function _init(){
		return true;
	}

	private static function _errRet($errno){
		return array(
				'errno' => $errno,
				'errmsg' => Tieba_Error::getErrmsg($errno),
			);
	}
	private static function _getKeyName($strKeyName){
		$strRet = self::PRE_NAME.'_'.$strKeyName;
		return $strRet;
	}

	//通过thread_id获取帖子点赞数
	public static function getZan($arrInput){
		$redis = self::_getRedis(self::APP_NAME);
		if(!$redis || !$redis -> isEnable()){
			Bingo_Log::warning('get redis fail');
			return false;
		}
		if(empty($arrInput['thread_id']) || $arrInput['thread_id'] < 0){
			Bingo_Log::warning('input param error.input is :'.serialize($arrInput));
			return false;
		}
		$thread_id = $arrInput['thread_id'];
		$key = self::_getKeyName($thread_id);
		$RedisInput = array(
			'key' => $key,
			);
		
		$ret = $redis->GET($RedisInput);

		if(!$ret || $ret['err_no'] !== 0){
			Bingo_Log::warning('getZan get fail . result is :'.serialize($ret));
			return false;
		}
		
		$arrRet = array(
			'errno' => $ret['err_no'],
			'errmsg' => $ret['err_msg'],
			);

		//如果当前thread_id未设定对应的value，那么设置value
		if($ret['ret'][$key] === null){
			$RedisInput = array(
				'key' => $key,
				'value' => '0',
				);
			
			$ret = $redis->SET($RedisInput);
			if(!$ret || $ret['err_no'] !== 0){
				Bingo_Log::warning('getZan set fail . result is :'.serialize($ret));
				return false;
			}		
			$arrRet['data'] = 0;
		}else{
			$arrRet['data'] = $ret['ret'][$key];
		}
		return $arrRet;

	}
	//点赞数增加1
	public static function ClickZan($arrInput){
		$redis = self::_getRedis(self::APP_NAME);
		if(!$redis || !$redis -> isEnable()){
			Bingo_Log::warning('get redis fail');
			return false;
		}

		if(empty($arrInput['thread_id']) || $arrInput['thread_id'] < 0){
			Bingo_Log::warning('input param error.input is :'.serialize($arrInput));
			return false;
		}
		$thread_id = $arrInput['thread_id'];
		$key = self::_getKeyName($thread_id);

		$RedisInput = array(
			'key' => $key,
			);

		$ret = $redis->INCR($RedisInput);

		if(!$ret || $ret['err_no'] !== 0){
			Bingo_Log::warning('getZan get fail . result is :'.serialize($ret));
			return false;
		}
		$arrRet = array(
			'errno' => $ret['err_no'],
			'errmsg' => $ret['err_msg'],
			'data' => $ret['ret'][$key],
			);

		return $arrRet;
	}

}
