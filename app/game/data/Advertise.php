<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2011-11-18
 * @version 
 */
class Data_Advertise {

    const SERVER_NAME = 'post';
    const METHOD = 'mgetThread';
    const SERVER_USER = 'user';
    const METHOD_USER = 'mgetUserInfo';
	const THREAD_RELAY_CACHEKEY = "relay_count_version";
	const TABLE_NAME = "tieba_soft_thread";
	const THREAD_CONST=1;   //�̶�λ��
	const THREAD_SLOW_DOWN =0;   //����
	const EXPIRE_TIME = 86400;
	const APP_NAME = 'bdrpecom';
	const BUSINESS_DIFANG_AD_THREAD = 'zufangAdvertise';
	const DIFANG_FLOOR = 2;  //ͣ����3¥
	protected static $_conf = null;
	protected static $_cache = null;	
	
	private static $_redis = null;
	private static function _getCache(){
		if(self::$_cache){
			return self::$_cache ;
		}
		Bingo_Timer::start('cacheinit');
		//$cacheConf = Bd_Conf::getConf('cache/cache_service_game');
		//if(!$cacheConf){
		//	Bingo_Log::warning("get cache config fail.[cache/cache_service_game]");
		//	return null;
		//}
		//self::$_cache = new Bingo_Cache_Memcached(array(
		//		'conf' => $cacheConf,
		//));
		self::$_cache = new Bingo_Cache_Memcached('forum_common');
	
		Bingo_Timer::end('cacheinit');
	
		if(!self::$_cache || !self::$_cache->isEnable()){
			Bingo_Log::warning("init cache fail.");
			self::$_cache = null;
			return null;
		}
		return self::$_cache;
	}
	


	//redis �ӵ���ģʽ
	private static function _getReids($strAppname){
	
		if(null === self::$_redis){
			$redis = new Bingo_Cache_Redis ( $strAppname);
			self::$_redis = $redis;
		}
		return self::$_redis;
	}
	
	
	/**
	 * @brief init
	 * @return: true if success. false if fail.
	
	 **/
	private static function _init(){
	
		//add init code here. init will be called at every public function beginning.
		//not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
		//init should be recalled for many times.
	
		if(self::$_conf == null){
			self::$_conf = Bd_Conf::getConf("/app/game/service_game");
			if(self::$_conf == false){
				Bingo_Log::warning("init get conf fail.");
				return false;
			}
	
		}
		return true;
	}
	
	private static function _errRet($errno){
		return array(
				'errno' => $errno,
				'errmsg' => Tieba_Error::getErrmsg($errno),
		);
	}

	private static function _errDefineRet($errno){
		return array(
				'errno' => $errno,
				'errmsg' => Util_Error::getErrmsg($errno),
		);
	}
	
	private static function _getThreadInfo($thread_id){
		$forum_data = NULL;
		$cache = self::_getCache();
		$cacheKey = 'game_ad_thread'.$thread_id;
		$strtime = 'cache_time';
		Bingo_Timer::start($strtime);
		if ($cache) $forum_data = $cache->get($cacheKey);
		Bingo_Timer::end($strtime);
		if ($forum_data !== NULL && $forum_data !== false){
			$forum_data = unserialize($forum_data);
		} else {
			$arrReq = array ('thread_ids' => array ($thread_id ), 'need_abstract' => 1, 'forum_id' => $thread_input ['forum_id'], 'need_photo_pic' => 1 );
			$arrThreadRet = Tieba_Service::call ( self::SERVER_NAME, self::METHOD, $arrReq );
			if ($arrThreadRet === false || $arrThreadRet ['errno'] != 0) {
				Bingo_Log::warning ( 'get thread info fail.req=' . serialize ( $arrReq ) );
				return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
			}
			$forum_data = $arrThreadRet ['output'] ['thread_list'] [$thread_id];
			$arrRet = self::_getUserInfo ( $forum_data );
			if (is_array ( $arrRet )) {
				$forum_data = array_merge ( $arrRet, $forum_data );
			}
			if ($forum_data === false || $cache === NULL || ($cache->add($cacheKey, serialize($forum_data),self::EXPIRE_TIME)) === false){
				Bingo_Log::warning($errStr.'set cache failed.cache = '.serialize($cache).serialize($forum_data));
				$errno = Util_Error::SET_CACHE_FAIL;
				return self::_errDefineRet($errno);
			}
		}
		return $forum_data;		
	}
	
	public static function getAdvList($arrInput){
		if(!isset($arrInput['forum_name']) || !isset($arrInput['thread_list'])){
			Bingo_Log::warning ( "input params invalid" . serialize ( $arrInput ) );
			return self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
		}
		$thread_list = $arrInput['thread_list'];
		$handleWordServer = Wordserver_Wordlist::factory ();
		$tableName = self::TABLE_NAME;
		$forum_name = $arrInput ['forum_name'];
		$threadRet = $handleWordServer->getValueByKeys ( array ($forum_name ), $tableName );
		if ($threadRet == false ) {
			Bingo_Log::warning ( 'call Wordserver_Wordlist queryKey error: ' . $handleWordServer->get_error () . 'input:' . serialize ( $forum_name ) . serialize ( $tableName ) );
			return self::_errDefineRet ( Util_Error::GET_WORD_LIST_FAIL );
		}
		if( $threadRet [$forum_name] === NULL){
			return self::_errDefineRet ( Util_Error::AD_NO_THREAD );
		}
		$value = unserialize ( $threadRet [$forum_name] );
		$now = time();
		if($value['start_time']>$now || $value['end_time']<$now){
			return self::_errDefineRet ( Util_Error::AD_TIME_OUT );
		}
		$thread_id = intval ( $value ['thread_id'] );
		$threadInfo = self::_getThreadInfo($thread_id);	
		$floor = intval ( $value ['floor_pos'] )-1;
		$num = count ( $thread_list );
		if($value['floor_flag'] == self::THREAD_CONST){
			$flag=0;
			for($i=0;$i<$num;$i++){
				if($thread_list[$i]['thread_id'] == $thread_id){
					$flag=1;
					break;
				}
			}
			if($flag == 1){
				$tmp = $thread_list[$i];
				if($i<$floor){
					for($j=$i;$j<$floor&&$j<$num;$j++){
						$thread_list [$j] = $thread_list [$j+1];
					}
				}else{
				     for($j=$i;$j>$floor;$j--){
                             $thread_list [$j] = $thread_list [$j-1];
                     }
				}
				$thread_list[$floor] = $tmp;
				$thread_list[$floor]['last_time'] = $thread_list[$floor+1]['last_time'];
			}else{
				$threadInfo ['last_time'] = $thread_list [$floor] ['last_time'];
				array_splice($thread_list, $floor, 0, array($threadInfo));
			}
		}else{
			if(self::_checkIsExist($thread_id,$thread_list)){
				return false;
			}
			$threadInfo ['last_time'] = $thread_list [$floor] ['last_time'];
			array_splice($thread_list, $floor, 0, array($threadInfo));
		}
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array (
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg ( $error ),
				'output' => $thread_list,
		);
		return $arrOutput;
	}
	private static function _checkIsExist($thread_id,$thread_input){
		foreach($thread_input as $key=>$value){
			if($value['thread_id'] == $thread_id){
				return true;
			}
		}
		return false;
	}
	
	private static function _checkDifangIsExist($thread_id,$thread_input){
		foreach($thread_input as $key=>$value){
			if($value['thread_id'] == $thread_id){
				return $key;
			}
		}
		return false;
	}
	private static function _getUserInfo($threadInfo){
		$arrReq = array(
				'user_id' =>array($threadInfo['user_id'],$threadInfo['last_user_id'])
		);
		$strTime = 'ad_user_info';
		Bingo_Timer::start($strTime);
		$arrRet = Tieba_Service::call(self::SERVER_USER,self::METHOD_USER,$arrReq);
		Bingo_Timer::end($strTime);
		if($arrRet === false || $arrRet['errno'] !=0){
			Bingo_Log::warning('get user info fail.req='.serialize($arrReq));
			return false;
		}
		$author =$arrRet['user_info'][$threadInfo['user_id']];
		$output['user_name'] = $author['user_name'];
		$output['author'] = array(
				'name'  => $author['user_name'],
				'name_show' => $author['user_nickname'],
				'portrait'  =>  Tieba_Ucrypt::encode($threadInfo['user_id'], $author['user_name']),
		);
		$last_user = $arrRet['user_info'][$threadInfo['last_user_id']];
		$output['last_user_name'] = $last_user['user_name'];
		$output['last_replyer'] = array(
				'name'  => $last_user['user_name'],
				'name_show' => $last_user['user_nickname'],
				'portrait'  =>  Tieba_Ucrypt::encode($threadInfo['last_user_id'], $last_user['user_name']),
		);
		return $output;
	}
	
	public static function getAdvertiseInfo($arrInput){
		if(!isset($arrInput['task_id'])){
			Bingo_Log::warning("input params invalid. [".print_r($arrInput,true)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$cache = Tieba_Service::getCache();
		if (!is_null($cache))
		{
			$in_key = array(
					'task_id' => $arrInput['task_id'],
					'format'=>$arrInput['format'],
					'ie'	=>$arrInput['ie']
			);
			$cache_key = Tieba_Service::genCacheKey($in_key,'game',$arrInput['method']);
			Bingo_Log::pushNotice("ckey" , $cache_key);
			if ($cache_key != false)
			{
				$cache_data = $cache->get($cache_key);
				$array_data = unserialize($cache_data);
				if(is_array($array_data))
				{
					Bingo_Log::pushNotice("hitc" , 1);
					return $array_data;
				}else{
					$arrInput = Bingo_Encode::convert($arrInput, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
					$arrReq = array(
							'task_id' => $arrInput['task_id'],
					);
					$info = Dl_Advertise_Advertise::getAdverviseInfo($arrReq);
					if($info == false || Tieba_Errcode::ERR_SUCCESS!=$info['errno']){			
						$errno = Util_Error::GET_TASK_FAIL;
						return self::_errDefineRet($errno);												
					}else{
						$game_data = serialize($info);
						$expire_time = $info['output']['end_time']-time();
						if($expire_time < 0){
							$expire_time = 0;
						}
						$ret = $cache->add($cache_key, $game_data, $expire_time);
						return $info;
					}
				}
			}else{
				Bingo_Log::warning('game gen cache key failed.req='.serialize($arrInput));
				$errno = Util_Error::GET_CACHE_KEY_FAIL;
				return self::_errDefineRet($errno);
			}
		}else{
			Bingo_Log::warning('get cache fail ');
			$errno = Util_Error::INIT_CACHE_FAIL;
			return self::_errDefineRet($errno);
		}
	}
	
	public static function _setDifangThreadList($arrInput,$maskFlag){
		$thread_list = $arrInput['thread_list'];
		$threadInfo = $arrInput['thread_info'];
		$floor = self::DIFANG_FLOOR;
		$threadInfo ['last_time'] = $thread_list [$floor] ['last_time'];
		$num = count ( $thread_list );
		$arrThread = array ();
		if($maskFlag === true){
			foreach($thread_list as $key=>$value){
				$arrTypes = Tieba_Type_Thread::getTypeArray($value['thread_types']);
				if($arrTypes['is_difang_ad_thread']){
					unset($thread_list[$key]);
				}
			}
		}else{
			$isExist = self::_checkDifangIsExist ( $threadInfo['thread_id'], $thread_list );
			if($arrInput['pn'] ==0){
				if ($isExist === false ) {
					for($i = $num; $i > $floor; $i --) {
						$thread_list [$i] = $thread_list [$i - 1];
					}
					$thread_list [$i] = $threadInfo;
				} else {
					if ($isExist > $floor) {
						for($i = $isExist; $i > $floor; $i --) {
							$thread_list [$i] = $thread_list [$i - 1];
						}
						$thread_list [$i] = $threadInfo;
					} else {
						for($i = $isExist; $i < $floor; $i ++) {
							$thread_list [$i] = $thread_list [$i + 1];
						}
						$thread_list [$floor] = $threadInfo;
					}
				}
			}
		}

		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array (
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg ( $error ),
				'output' => $thread_list,
		);
		return $arrOutput;		
	}
	
	public static function updateAdvertiseInfo($arrInput){
		if(!isset($arrInput['task_id']) ){
			Bingo_Log::warning("input params invalid. [".print_r($arrInput,true)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$cache = Tieba_Service::getCache();
		if (!is_null($cache))
		{
			$in_key = array(
					'task_id' => $arrInput['task_id'],
					'format'=>$arrInput['format'],
					'ie'	=>$arrInput['ie']
			);
			$method = 'getAdvertiseInfo';
			$cache_key = Tieba_Service::genCacheKey($in_key,'game',$method);
			Bingo_Log::pushNotice("ckey" , $cache_key);
			if ($cache_key != false)
			{
				$ret = $cache->remove($cache_key);
				if($ret === false){
					$errno = Util_Error::SET_CACHE_FAIL;
					Bingo_Log::warning ( 'call redis errro , the req = ' . serialize ( $arrInput ) );
					return self::_errDefineRet ( $errno );
				}else{
					$errno = Tieba_Errcode::ERR_SUCCESS;
					$output = array(
							'errno'=>$errno,
							'errmsg'=>Tieba_Error::getErrmsg ( $errno ),
					);
					return $output;
				}
			}else{
				Bingo_Log::warning('game gen cache key failed.req='.serialize($arrInput));
				$errno = Util_Error::GET_CACHE_KEY_FAIL;
				return self::_errDefineRet($errno);
			}
		}else{
			Bingo_Log::warning('get cache fail ');
			$errno = Util_Error::INIT_CACHE_FAIL;
			return self::_errDefineRet($errno);
		}
	}
	
	public static function setSepInfo($input){
		$redis = new Bingo_Cache_Redis ( self::APP_NAME );
		if (! $redis->isEnable ()) {
			Bingo_Log::warning ( "init redis failed. [" . print_r ( $redis ) . "]" );
			return self::_errDefineRet ( Util_Error::INIT_REDIS_FAIL );
		}
	    foreach($input['reqs'] as $key=>$value){
    		$arrParams = array(
    				'key'=>$value['key'],
    				'value'=>serialize($value['value']),
    		);
    		$arrReq[] = $arrParams;
    	}
    	$arrReq = array('reqs' => $arrReq);
		$arrRet = $redis->Set( $arrReq );
		if ($arrRet === false || $arrRet ['err_no'] !== 0) {
			$errno = Util_Error::CALL_REDIS_FAIL;
			Bingo_Log::warning ( 'call redis errro , the req = ' . serialize ( $arrReq ) );
			return self::_errDefineRet ( $errno );
		}	
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array (
				'errno' => $error,
				'errmsg' => Tieba_Error::getErrmsg ( $error ),
		);
		return $arrOutput;
	}
	

	public static function cancelDifangAd($arrInput){
		if(!isset($arrInput['baiduid'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		//���ж�payno�����ȿ��ƣ� ����������ȿ���(û���ظ��ύ)
		$req=array(
				'req'=>array(
						'rulegroup'	=>	array('app'),
						'app'	=>	'business',
						'cmd'		=>	self::BUSINESS_DIFANG_AD_THREAD,
						'baiduid'		=>	$arrInput['baiduid'],
				),
		);
		$arrOut = self::_errRet(Tieba_Errcode::ERR_SUCCESS);
		$res = Tieba_Service::call('anti','antiActsctrlSubmit',$req);
		if($res == false){
			Bingo_Log::warning('call anti fail.input='.serialize($req));
			$arrOut['is_open_difang_thread'] = false;
		}
		if($res['res']['err_no'] != 0){//�ظ��ύ
			$arrOut['is_open_difang_thread'] = true;
		}else{
			$arrOut['is_open_difang_thread'] = false;
		}
		return $arrOut;		
	}
	
	public static function getDifangAdvertise($arrInput){
		if(!isset($arrInput['baiduid']) || !isset($arrInput['thread_list'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$req=array(
				'req'=>array(
						'rulegroup'	=>	array('app'),
						'app'	=>	'business',
						'cmd'		=>	self::BUSINESS_DIFANG_AD_THREAD,
						'baiduid'		=>	$arrInput['baiduid'],
				),
		);
		$res = Tieba_Service::call('anti','antiActsctrlQuery',$req);
		if($res == false ){
			Bingo_Log::warning ( __FUNCTION__." error. [input:".serialize($input)."] [ret_redis:" . serialize( $retRedis) . "]" );
			$arrRet = self::_errRet( Tieba_Errcode::ERR_SUCCESS);
			return $arrRet;
		}else if($res['res']['err_no'] != 0 ){
			$arrRet = self::_setDifangThreadList($arrInput,true);
			return $arrRet;
		}else{
			$arrRet = self::_setDifangThreadList($arrInput,false);
			return $arrRet;
		}
	}
}


