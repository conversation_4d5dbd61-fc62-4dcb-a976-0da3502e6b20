<?php
class Data_Gamenews{
	const APP_NAME = 'bdrpecom';
	const FORUM_ATTR_NAME = 'game_news';
	const FORUM_IFRAME_KEY = 'game_forum_iframe_';
	const FORUM_COMMON_KEY = 'game_forum_common_';
	const DIR_COMMON_KEY = 'game_dir_common_';
	const GAME_TOP_NEWS = 'game_top_news_';
	const FORUM_NUM_MAX = 100;
	const FORUM_BATCH_NUM = 20;
	const TABLE_NAME =  'tb_wordlist_redis_u9_blacklist';
	
	private static $arrGameTyps = array(1,2,3); //1端游；2页游；3手游
	private static $arrStyleTypes = array(0,1,2); //0默认；1iframe；2特定数据
	private static $arrSecondDirs = array(''); //二级目录白名单

	// 在APP端要展现的非手机游戏类目的特殊吧：dota, dota2
	private static $APP_SPECIAL_FORUMIDS = array(
		248112, 1627732,
	);

	private static $_cache = null;
	private static $_redis = null;	
	
	private static function init() {
        if(self::$_redis == null) {
            self::$_redis = new Bingo_Cache_Redis(self::APP_NAME);
            if(self::$_redis == false) {
                Bingo_Log::warning("init redis init fail:" . var_export(Bd_RalRpc::get_error(), true));
                return false;
            }
        }
        return true;
    }
	
	private static function _getCache(){
		if(self::$_cache){
			return self::$_cache ;
		}
		Bingo_Timer::start('cacheinit');
		self::$_cache = new Bingo_Cache_Memcached('forum_common');
	
		Bingo_Timer::end('cacheinit');
	
		if(!self::$_cache || !self::$_cache->isEnable()){
			Bingo_Log::warning("init cache fail.");
			self::$_cache = null;
			return null;
		}
		return self::$_cache;
	}

    private static function _doRedisQuery($method, $arrInput) {
        if (self::$_redis == null) {
            self::$_redis = new Bingo_Cache_Redis(self::APP_NAME);
        }

        $redis = self::$_redis;

        if (!$redis) {
            Bingo_Log::warning('failed to get redis: '.self::APP_NAME);
            return false;
        }

        $ret = $redis->$method($arrInput);

        if (!$ret || $ret['err_no'] !== 0) {
            Bingo_Log::warning('redis '.$method.' failure, input: '.json_encode($arrInput).', ret: '.json_encode($ret));
            return false;
        }

        return $ret['ret'];
    }

	private static function _errRet($errno, $arrInvalid = array()){
		$ret = array(
			'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
		);

		if (!empty($arrInvalid)) {
			$ret['errmsg'] .= '. Invalid forum_ids: '.json_encode($arrInvalid);
		}

		return $ret;
	}
	
	public static function setCustomized($arrInput){
		if (empty($arrInput['forum_ids']) || empty($arrInput['game_name']) || empty($arrInput['top_iframe']) || empty($arrInput['right_iframe'])){
			Bingo_Log::warning('param error.game_name:'.$arrInput['game_name'].' top:'.$arrInput['top_iframe'].' right:'.$arrInput['right_iframe']);
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrForumIds = explode(',',trim($arrInput['forum_ids']));
		if (!is_array($arrForumIds) || empty($arrForumIds)){
			Bingo_Log::warning('forum_ids param error');
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		if (count($arrForumIds) > self::FORUM_NUM_MAX){
			Bingo_Log::warning('forum_ids num more than'.self::FORUM_NUM_MAX);
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		//检查吧id有效性
		$arrOut = self::_getFnameByFid($arrForumIds);
		if ($arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
			return self::_errRet($arrOut['errno']);
		}
		$arrForumIds = $arrOut['output']['exist'];
		$arrValue = array(
			'game_name' => trim($arrInput['game_name']),
			'top_iframe' => trim($arrInput['top_iframe']),
			'right_iframe' => trim($arrInput['right_iframe']),
		);
		$strValue = Bingo_String::array2json($arrValue,'gbk');
		
		if (!self::init()){
			Bingo_Log::warning('init fail');
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		$arrReqs = array();
		foreach ($arrForumIds as $intForumId){
			$input = array(
				'key' => self::FORUM_IFRAME_KEY.$intForumId,
				'value' => $strValue,
			);
			$arrReqs[] = $input;
		}
		$arrInput =  array(
        	'reqs' => $arrReqs,
    	);
    	$ret = self::$_redis->SET($arrInput);
    	if (!$ret || $ret['err_no'] != 0){
    		Bingo_Log::warning('set game forum iframe fail'.serialize($ret));
    		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
    	}
		//set 吧属性
		$arrRet = self::_setForumAttr($arrForumIds, 1);
		if ($arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning('msetForumAttr fail.attr_value:1');
			return self::_errRet($arrRet['errno']);
		}
		//删除key
		$arrReqs = array();
		foreach ($arrForumIds as $intForumId){
			$input = array(
				'key' => self::FORUM_COMMON_KEY.$intForumId,
			);
			$arrReqs[] = $input;
		}
		$arrInput =  array(
        	'reqs' => $arrReqs,
    	);
    	$ret = self::$_redis->DEL($arrInput);
    	if (!$ret || $ret['err_no'] != 0){
    		Bingo_Log::warning('del common key fail'.serialize($ret));
    	}
    	return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOut['output']['invalid']);
	}

	/**
	 * 20150311, 将被下面的setU9Content代替，分别设置三部分内容
	 * @param [array] $arrInput [description]
	 * @return [array] $ret [执行结果结构体]
	 */
	public static function setForumNews($arrInput){
		if (empty($arrInput['forum_ids']) || empty($arrInput['top_code']) || empty($arrInput['news_list']) || empty($arrInput['right_code']) || empty($arrInput['right_more'])){
			Bingo_Log::warning('param error.');
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrForumIds = explode(',',trim($arrInput['forum_ids']));
		if (!is_array($arrForumIds) || empty($arrForumIds)){
			Bingo_Log::warning('forum_ids param error');
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
	    if (count($arrForumIds) > self::FORUM_NUM_MAX){
			Bingo_Log::warning('forum_ids num more than'.self::FORUM_NUM_MAX);
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		//检查吧id有效性
		$arrOut = self::_getFnameByFid($arrForumIds);
		if ($arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
			return self::_errRet($arrOut['errno']);
		}
		$arrForumIds = $arrOut['output']['exist'];
		$arrValue = array(
			'top_code' => trim($arrInput['top_code']),
			'news_list' => trim($arrInput['news_list']),
			'right_code' => trim($arrInput['right_code']),
			'right_more' => trim($arrInput['right_more']),
		);
		$strValue = Bingo_String::array2json($arrValue,'gbk');
		
		if (!self::init()){
			Bingo_Log::warning('init fail');
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		$arrReqs = array();
		foreach ($arrForumIds as $intForumId){
			$input = array(
				'key' => self::FORUM_COMMON_KEY.$intForumId,
				'value' => $strValue,
			);
			$arrReqs[] = $input;
		}
		$arrInput =  array(
        	'reqs' => $arrReqs,
    	);
    	$ret = self::$_redis->SET($arrInput);
    	if (!$ret || $ret['err_no'] != 0){
    		Bingo_Log::warning('set game forum iframe fail'.serialize($ret));
    		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
    	}
    	//set 吧属性
		$arrRet = self::_setForumAttr($arrForumIds, 2);
		if ($arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning('msetForumAttr fail.attr_value:2');
			return self::_errRet($arrRet['errno']);
		}
		//删除key
		$arrReqs = array();
		foreach ($arrForumIds as $intForumId){
			$input = array(
				'key' => self::FORUM_IFRAME_KEY.$intForumId,
			);
			$arrReqs[] = $input;
		}
		$arrInput =  array(
        	'reqs' => $arrReqs,
    	);
    	$ret = self::$_redis->DEL($arrInput);
    	if (!$ret || $ret['err_no'] != 0){
    		Bingo_Log::warning('del iframe key fail'.serialize($ret));
    	}
    	return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOut['output']['invalid']);
	}

	/**
	 * 单独设置某一项U9推送的内容
	 * @param [array] $arrInput [forum_ids/second_dir, item_keys, ...]
	 * @return [array] $ret [执行结果结构体]
	 */
	public static function setU9Content($arrInput) {
		if (empty($arrInput) || empty($arrInput['item_keys'])
			|| (empty($arrInput['forum_ids']) && empty($arrInput['second_dir']))){
			Bingo_Log::warning('setU9Content param error.');
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}

		$keys = $arrInput['item_keys'];

		foreach ($keys as $key) {
			if (empty($arrInput[$key])) {
				Bingo_Log::warning('setU9Content param error, missing '.$key);
				return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
			}
		}

		if (!empty($arrInput['forum_ids'])) {
			return self::setItemByForumIds($arrInput, $keys);
		} else {
			return self::setItemBySecondDir($arrInput, $keys);
		}
	}

	/**
	 * 设置各分项内容：top_code，news_list，right_code/right_more
	 * @param [array] $arrInput [传递过来的数据]
	 * @param [array] $keys     [需要设置的具体项]
	 * @return [array] [结果结构体]
	 */
	private static function setItemByForumIds($arrInput, $keys) {
		$arrForumIds = explode(',', trim($arrInput['forum_ids']));

		if (!is_array($arrForumIds) || empty($arrForumIds) || count($arrForumIds) > self::FORUM_NUM_MAX){
			Bingo_Log::warning('forum_ids param error: empty or more than '.self::FORUM_NUM_MAX);
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}

		//检查吧id有效性
		$arrOut = self::_getFnameByFid($arrForumIds);
		if ($arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
			return self::_errRet($arrOut['errno']);
		} else {
			$arrForumIds = $arrOut['output']['exist'];
		}

		$input = self::makeRedisInput(self::FORUM_COMMON_KEY, $arrForumIds);
		$ret = self::_doRedisQuery('GET', $input);
		if ($ret === false){
			Bingo_Log::warning('get prev u9 content fail: '.serialize($ret));
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}

		$newVals = array();
		foreach ($ret as $k => $v) {
			if (empty($v)) {
				$data = array();
			} else {
		     	$data = Bingo_String::json2array($v, 'utf-8');
	     	}

	    	foreach ($keys as $key) {
	    		$data[$key] = Bingo_Encode::convert(trim($arrInput[$key]), Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
	    	}

	    	$newVals[] = array(
	    		'key' => $k,
	    		'value' => Bingo_String::array2json($data, 'utf-8'),
	    	);
		}

		$input = array('reqs' => $newVals);
		$ret = self::_doRedisQuery('SET', $input);
		if ($ret === false){
			Bingo_Log::warning('set u9 content fail: '.serialize($ret));
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}

    	//set 吧属性
		$arrRet = self::_setForumAttr($arrForumIds, 2);
		if ($arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning('msetForumAttr fail.attr_value:2');
			return self::_errRet($arrRet['errno']);
		}
		//删除key
		$input = self::makeRedisInput(self::FORUM_IFRAME_KEY, $arrForumIds);
		$ret = self::_doRedisQuery('DEL', $input);
		if ($ret === false){
			Bingo_Log::warning('del iframe key fail: '.serialize($ret));
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}

    	return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOut['output']['invalid']);
	}

	/**
	 * 构造redis输入
	 * @param  [string] $keyPre   [redis key前缀]
	 * @param  [array] $forumIds [吧id数组]
	 * @param  [array] $val      [key对应的值]
	 * @return [array]           [结果数组]
	 */
	private static function makeRedisInput($keyPre, $forumIds, $val = null) {
		$arrReqs = array();

		foreach ($forumIds as $intForumId){
			$input = array(
				'key' => $keyPre.$intForumId,
			);

			if (!empty($val)) {
				$input['value'] = $val;
			}

			$arrReqs[] = $input;
		}

		return array(
        	'reqs' => $arrReqs,
    	);
	}

	/**
	 * 通过二级目录设置数据项内容
	 * @param [array] $arrInput [输入数组]
	 * @param [array] $keys     [需要设置的具体内容项]
	 * @return [array] [结果数组]
	 */
	private static function setItemBySecondDir($arrInput, $keys) {
		$strSecondDir = trim($arrInput['second_dir']);

		$input = array(
			'key' => self::DIR_COMMON_KEY.$strSecondDir,
		);

		$ret = self::_doRedisQuery('GET', $input);
		if ($ret === false){
			Bingo_Log::warning('get prev u9 content fail: '.serialize($ret));
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}

		$prevVal = $ret[$input['key']];
		if (empty($prevVal)) {
			$newVal = array();
		} else {
		 	$newVal = Bingo_String::json2array($prevVal, 'utf-8');
		}

		foreach ($keys as $key) {
			$newVal[$key] = Bingo_Encode::convert(trim($arrInput[$key]), Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
		}

		$input['value'] = Bingo_String::array2json($newVal, 'utf-8');
		$ret = self::_doRedisQuery('SET', $input);
		if ($ret === false){
			Bingo_Log::warning('set u9 content fail: '.serialize($ret));
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}

		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}

	public static function setDirNews($arrInput){
		if (empty($arrInput['second_dir']) || empty($arrInput['top_code']) || empty($arrInput['news_list']) || empty($arrInput['right_code']) || empty($arrInput['right_more'])){
			Bingo_Log::warning('param error.');
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$strSecondDir = trim($arrInput['second_dir']);
		/*************/
//		$strDir = '其他游戏及话题';
//		if ($strSecondDir != $strDir){
//			Bingo_Log::warning('$strSecondDir param error.');
//			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
//		}
		/*************/
		$arrValue = array(
			'top_code' => trim($arrInput['top_code']),
			'news_list' => trim($arrInput['news_list']),
			'right_code' => trim($arrInput['right_code']),
			'right_more' => trim($arrInput['right_more']),
		);
		$strValue = Bingo_String::array2json($arrValue,'gbk');
		
		if (!self::init()){
			Bingo_Log::warning('init fail');
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		
		$arrInput = array(
			'key' => self::DIR_COMMON_KEY.$strSecondDir,
			'value' => $strValue,
		);
			
    	$ret = self::$_redis->SET($arrInput);
    	if (!$ret || $ret['err_no'] != 0){
    		Bingo_Log::warning('set game forum iframe fail'.serialize($ret));
    		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
    	}
    	return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	public static function setTopNews($arrInput){
		if (empty($arrInput['game_type']) || empty($arrInput['news_list'])){
			Bingo_Log::warning('param error.');
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$intGameType = intval($arrInput['game_type']);
		if (!Bingo_Array::in_array($intGameType, self::$arrGameTyps)){
			Bingo_Log::warning('game_type error'.$intGameType);
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrValue = array(
			'news_list' => trim($arrInput['news_list']),
		);
		$strValue = Bingo_String::array2json($arrValue,'gbk');
		
		if (!self::init()){
			Bingo_Log::warning('init fail');
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		
		$arrInput = array(
			'key' => self::GAME_TOP_NEWS.$intGameType,
			'value' => $strValue,
		);
			
    	$ret = self::$_redis->SET($arrInput);
    	if (!$ret || $ret['err_no'] != 0){
    		Bingo_Log::warning('set game forum iframe fail'.serialize($ret));
    		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
    	}
    	return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	public static function delForumStyle($arrInput){
		if (empty($arrInput['forum_ids'])){
			Bingo_Log::warning('param error.');
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrForumIds = explode(',',trim($arrInput['forum_ids']));
		if (!is_array($arrForumIds) || empty($arrForumIds)){
			Bingo_Log::warning('forum_ids param error');
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		if (count($arrForumIds) > self::FORUM_NUM_MAX){
			Bingo_Log::warning('forum_ids num more than'.self::FORUM_NUM_MAX);
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		if (!self::init()){
			Bingo_Log::warning('init fail');
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		//set 吧属性
		while ( count($arrForumIds) > 0 ) {
	    	$batchData = array();
	        $batchData = array_splice($arrForumIds, 0, self::FORUM_BATCH_NUM); 
			$arrRet = self::_setForumAttr($batchData, 0);
			if ($arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
				Bingo_Log::warning('msetForumAttr fail.attr_value:0');
				return self::_errRet($arrRet['errno']);
			}
			//删除key					
			$arrReqs = array();
			foreach ($batchData as $intForumId){
				$input = array(
					'key' => self::FORUM_IFRAME_KEY.$intForumId,
				);
				$arrReqs[] = $input;
				$input = array(
					'key' => self::FORUM_COMMON_KEY.$intForumId,
				);
				$arrReqs[] = $input;
			}
			$arrInput =  array(
	        	'reqs' => $arrReqs,
	    	);
	    	$ret = self::$_redis->DEL($arrInput);
	    	if (!$ret || $ret['err_no'] != 0){
	    		Bingo_Log::warning('set game forum iframe fail'.serialize($ret));
	    		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	    	}
		}
    	return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}

	/**
	 * 设置吧属性
	 * @param array  $arrForumIds [description]
	 * @param int $intValue    [description]
	 * @return  array [<description>]
	 */
	private static function _setForumAttr($arrForumIds, $intValue = 0){
		if (empty($arrForumIds) || !Bingo_Array::in_array($intValue, self::$arrStyleTypes)){
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrReqs = array();
		foreach ($arrForumIds as $intForumId){
			$input = array(
				'forum_id' => $intForumId,
				'attr_name' => self::FORUM_ATTR_NAME,
				'attr_value' => strval($intValue),
			);
			$arrReqs[] = $input;
		}
		$arrInput = array(
			'input' => $arrReqs,
		);
		$arrRet = Tieba_Service::call('forum','msetForumAttr',$arrInput);
		if (false === $arrRet || !isset($arrRet['errno'])){
			Bingo_Log::warning('msetForumAttr fail.attr_value:'.$intValue);
			return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
		}
		elseif ($arrRet['errno'] != Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning('msetForumAttr fail, input: '.json_encode($arrInput).', ret: '.json_encode($arrRet));
			return self::_errRet($arrRet['errno']);
		}
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}
	private static function _getFnameByFid($arrForumIds){
		//正式上线后删除
	/*	$arrForums = array(
			5904666 => '乱三	',
			5903876 => 'q战术',
			13232820 => '银河辅助',
		);
		$flag = false;
		foreach ($arrForumIds as $intForumId){
			if (array_key_exists($intForumId, $arrForums)){
				$flag = true;
				break;
			}
		}
		if (!$flag){
			return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
		}*/
		//正式上线后删除
		$arrInput = array(
			'forum_id' => $arrForumIds,
		);
		$arrOut = Tieba_Service::call('forum', 'getFnameByFid', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
		if (false === $arrOut || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning('get forum info fail.'.serialize($arrOut));
			return self::_errRet(Tieba_Errcode::ERR_FORUM_NEW_FNAME_INVALID);
		}
		$arrExistForums = array();
		foreach ($arrOut['forum_name'] as $arrTmp){
			if ($arrTmp['exist'] == 1){
				$arrExistForums[] = $arrTmp['forum_id'];
			}
		}
		if (empty($arrExistForums)){
			Bingo_Log::warning('get forum info fail.'.serialize($arrOut));
			return self::_errRet(Tieba_Errcode::ERR_FORUM_NEW_FNAME_INVALID);
		}
		$arrLeftForums = array_diff($arrForumIds, $arrExistForums);
		return array(
			'errno' => Tieba_Errcode::ERR_SUCCESS,
			'errmsg' => Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
			'output' => array(
				'exist' => $arrExistForums,
				'invalid' => $arrLeftForums,
			),
		);
	}

	/**
	 * 获取定制吧的展现数据
	 * @param  [array] $arrInput [forum_id, is_pc]
	 * @return [array]           [description]
	 */
	public static function getCustomized($arrInput){
		if (empty($arrInput['forum_id'])){
			Bingo_Log::warning('param error.forum_id:'.$arrInput['forum_id']);
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}

		$intForumId = intval($arrInput['forum_id']);
		$handleWordServer = Wordserver_Wordlist::factory();
        $arrItemInfo = $handleWordServer->getValueByKeys(array($intForumId), self::TABLE_NAME);
        if(isset($arrItemInfo) && $arrItemInfo[$intForumId] == $intForumId) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }//不展示u9的吧
        
        if (empty($arrInput['is_pc']) && in_array($intForumId, self::$APP_SPECIAL_FORUMIDS)) {
        	// 如果是来自 APP 的特殊吧请求，则不检查吧目录限制
        	// 否则需要判断吧目录是否为“手机游戏”
        } else {
	        $arrOut = Tieba_Service::call('forum', 'getBtxInfo', array('forum_id' => $intForumId), null, null, 'post', 'php', 'utf-8');
	        if (!isset($arrOut['dir']['level_2_name']) || $arrOut['dir']['level_2_name'] != '手机游戏'){
	        	return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	        }//不展示u9的目录
	    }

		if (!self::init()){
			Bingo_Log::warning('init fail');
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		
		$arrInput =  array(
        	'key' => self::FORUM_IFRAME_KEY.$intForumId,
    	);
    	$ret = self::$_redis->GET($arrInput);
    	if (!$ret || $ret['err_no'] != 0 || !isset($ret['ret'][self::FORUM_IFRAME_KEY.$intForumId])){
    		Bingo_Log::warning('get game forum iframe fail'.serialize($ret));
    		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
    	}
    	
    	$arrData = Bingo_String::json2array($ret['ret'][self::FORUM_IFRAME_KEY.$intForumId],'utf-8');
    	$errno = Tieba_Errcode::ERR_SUCCESS;
    	
    	return array(
    		'errno' => $errno,
    		'errmsg' => Tieba_Error::getErrmsg($errno),
    		'output' => $arrData,
    	);
	}

	/**
	 * 获取U9推送的游戏信息
	 * @param  [array] $arrInput [forum_id, game_type, [pb_right, is_pc]]
	 * @return [array]           [结果结构体]
	 */
	public static function getForumNews($arrInput){
		if (empty($arrInput['forum_id']) || empty($arrInput['game_type'])){
			Bingo_Log::warning('getForumNews param error. forum_id:'.$arrInput['forum_id'].' game_type:'.$arrInput['game_type']);
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		
		if (!self::init()){
			Bingo_Log::warning('init fail');
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}

		//不展示u9的吧
		$intForumId = intval($arrInput['forum_id']);
		$handleWordServer = Wordserver_Wordlist::factory();
        $arrItemInfo = $handleWordServer->getValueByKeys(array($intForumId), self::TABLE_NAME);
        if(isset($arrItemInfo) && $arrItemInfo[$intForumId] == $intForumId) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        
        if (empty($arrInput['is_pc']) && in_array($intForumId, self::$APP_SPECIAL_FORUMIDS)) {
        	// 如果是来自 APP 的特殊吧请求，则不检查吧目录限制
        	// 否则需要判断吧目录是否为“手机游戏”
        } else {
	        $arrForumInfo = Tieba_Service::call('forum', 'getBtxInfo', array('forum_id' => $intForumId), null, null, 'post', 'php', 'utf-8');
	        if (empty($arrForumInfo) || !isset($arrForumInfo['dir']['level_2_name'])
	        	|| $arrForumInfo['dir']['level_2_name'] != '手机游戏'){
	        	return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	        }
	    }
		
		$intGameType = intval($arrInput['game_type']);
		$input = array(
			'key' => self::FORUM_COMMON_KEY.$intForumId,
		);
		$arrReq[] = $input;
		
		//pb页不展示头条
		if (!isset($arrInput['pb_right'])){
			$input = array(
				'key' => self::GAME_TOP_NEWS.$intGameType,
			);
			$arrReq[] = $input;
		}

		$arrParam =  array(
        	'reqs' => $arrReq,
    	);
    	$ret = self::$_redis->GET($arrParam);
    	
    	if (empty($ret) || $ret['err_no'] != 0 || empty($ret['ret'][self::FORUM_COMMON_KEY.$intForumId])){
    		Bingo_Log::warning('get game forum common fail: '.serialize($ret));
    		return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
    	}

    	if(!isset($arrInput['pb_right']) && empty($ret['ret'][self::GAME_TOP_NEWS.$intGameType])){
			Bingo_Log::warning('get game forum top_news fail'.serialize($ret));
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
    	}
    	
     	$arrOtherData = Bingo_String::json2array($ret['ret'][self::FORUM_COMMON_KEY.$intForumId],'utf-8');

     	// 吧数据缺失某一项时，用目录数据中的对应项来填充
	    $itemKeys = array('top_code', 'news_list', 'right_code', 'right_more');
	    foreach ($itemKeys as $key) {
	    	if (empty($arrOtherData[$key])) {
	    		$secondDirGBK = Bingo_Encode::convert($arrForumInfo['dir']['level_2_name'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
	    		$redisKey = self::DIR_COMMON_KEY.$secondDirGBK;
	    		$input = array('key' => $redisKey);
	    		$dirData = self::$_redis->GET($input);

	    		if (empty($dirData) || $dirData['err_no'] != 0 || empty($dirData['ret'][$redisKey])) {
					Bingo_Log::warning('get game forum dir content fail: '.serialize($dirData));
					return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
	    		}

	    		$arrDirData = Bingo_String::json2array($dirData['ret'][$redisKey], 'utf-8');
	    		break;
	    	}
	    }
	    foreach ($itemKeys as $key) {
	    	if (empty($arrOtherData[$key])) {
	    		$arrOtherData[$key] = $arrDirData[$key];
	    	}
	    }

	    //默认取top_code
    	$arrTopGame = Bingo_String::json2array($arrOtherData['top_code'],'utf-8');
    	if (!isset($arrInput['is_pc'])){
    		// 判断不是pc访问，是移动端访问时，执行
			// 若推送中有活动，则该topcode字段is_preferred一定为1

			$tempArray = array();
    		foreach ($arrTopGame as $topGame){
    			if (isset($topGame['is_preferred']) && $topGame['is_preferred'] == 1){
    				array_push($tempArray, $topGame);
    			}
    		}
    		if (!empty($tempArray)){
    			$arrTopGame = $tempArray;
    		}
    	}
		else {
			// pc访问时，需要将推送的活动过滤掉活动内容
			// topcode中get_type字段为3时，表示活动
			$tempArray = array();
			foreach ($arrTopGame as $topGame){
				if (isset($topGame['get_type']) && $topGame['get_type'] != 3) {
					array_push($tempArray, $topGame);
				}
			}
			if (!empty($tempArray)) {
				$arrTopGame = $tempArray;
			}
		}

    	$arrData = array(
    		'top' => array(
    			'top_code' => $arrTopGame,
    			'news_list' => Bingo_String::json2array($arrOtherData['news_list'],'utf-8'),
    		),
    		'right' => array(
    			'right_code' => Bingo_String::json2array($arrOtherData['right_code'],'utf-8'),
    			'more_link' => $arrOtherData['right_more'],
    		),
    	);

		if(!isset($arrInput['pb_right'])){
    		 $arrTopNews = Bingo_String::json2array($ret['ret'][self::GAME_TOP_NEWS.$intGameType],'utf-8');
 			 $arrData['top']['top_news'] = Bingo_String::json2array($arrTopNews['news_list'],'utf-8');
    	}

    	$errno = Tieba_Errcode::ERR_SUCCESS;
    	return array(
    		'errno' => $errno,
    		'errmsg' => Tieba_Error::getErrmsg($errno),
    		'output' => $arrData,
    	);
	}

	public static function getDirNews($arrInput){
		if (empty($arrInput['second_dir']) || empty($arrInput['game_type'])){
			Bingo_Log::warning('param error.second_dir:'.$arrInput['second_dir'].' game_type:'.$arrInput['game_type']);
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		if (!empty($arrInput['forum_id'])){
			$intForumId = intval($arrInput['forum_id']);
			$handleWordServer = Wordserver_Wordlist::factory();
            $arrItemInfo = $handleWordServer->getValueByKeys(array($intForumId), self::TABLE_NAME);
            if(isset($arrItemInfo) && $arrItemInfo[$intForumId] == $intForumId) {
                return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
            }
		}
		$strDir = Bingo_Encode::convert('手机游戏', 'gbk', 'utf-8');
		if ($arrInput['second_dir'] != $strDir){
        	return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }//不展示u9的目录
		if (!self::init()){
			Bingo_Log::warning('init fail');
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		$strSecondDir = strval($arrInput['second_dir']);
		$intGameType = intval($arrInput['game_type']);
		$input = array(
			'key' => self::DIR_COMMON_KEY.$strSecondDir,
		);
		$arrReq[] = $input;
		//pb页不展示头条
		if (!isset($arrInput['pb_right'])){
			$input = array(
				'key' => self::GAME_TOP_NEWS.$intGameType,
			);
			$arrReq[] = $input;
		}	
		$arrParam =  array(
        	'reqs' => $arrReq,
    	);
    	$ret = self::$_redis->GET($arrParam);
  	
	    if (!$ret || $ret['err_no'] != 0){
    		Bingo_Log::warning('get game dir common fail'.serialize($ret));
    		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
    	}
	    if (!isset($ret['ret'][self::DIR_COMMON_KEY.$strSecondDir])){
    		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    	} 	
		elseif(!isset($arrInput['pb_right'])){
    		 if(!isset($ret['ret'][self::GAME_TOP_NEWS.$intGameType])){
    			 return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    		 }
    	}

    	$arrOtherData = Bingo_String::json2array($ret['ret'][self::DIR_COMMON_KEY.$strSecondDir],'utf-8');
    	//默认取top_code
    	$arrTopGame = Bingo_String::json2array($arrOtherData['top_code'],'utf-8');
    	if (!isset($arrInput['is_pc'])){
    		// 判断不是pc访问，是移动端访问时，执行
			// 若推送中有活动，则该topcode字段is_preferred一定为1
    		$tempArray = array();
    		foreach ($arrTopGame as $topGame){
    			if (isset($topGame['is_preferred']) && $topGame['is_preferred'] == 1){
    				array_push($tempArray, $topGame);
    			}
    		}
    		if (!empty($tempArray)){
    			$arrTopGame = $tempArray;
    		}
    	}
		else {
			// pc访问时，需要将推送的活动过滤掉活动内容
			// topcode中get_type字段为3时，表示活动
			$tempArray = array();
			foreach ($arrTopGame as $topGame) {
				if (isset($topGame['get_type']) && $topGame['get_type'] != 3) {
					array_push($tempArray, $topGame);
				}
			}
			if (!empty($tempArray)) {
				$arrTopGame = $tempArray;
			}
		}
    	$arrData = array(
    		'top' => array(
    			'top_code' => $arrTopGame,
    			'news_list' => Bingo_String::json2array($arrOtherData['news_list'],'utf-8'),
    		),
    		'right' => array(
    			'right_code' => Bingo_String::json2array($arrOtherData['right_code'],'utf-8'),
    			'more_link' => $arrOtherData['right_more'],
    		),
    	);
		if(!isset($arrInput['pb_right'])){
    		 $arrTopNews = Bingo_String::json2array($ret['ret'][self::GAME_TOP_NEWS.$intGameType],'utf-8');
 			 $arrData['top']['top_news'] = Bingo_String::json2array($arrTopNews['news_list'],'utf-8');
    	}
    	
    	$errno = Tieba_Errcode::ERR_SUCCESS;
    	return array(
    		'errno' => $errno,
    		'errmsg' => Tieba_Error::getErrmsg($errno),
    		'output' => $arrData,
    	);
	}
}
