<?php
/**
 * mis数据通用存储
 * 
 * @authors tanxinyun
 * @date    2015-11-25 16:24:54
 * @version $Id$
 */

class Data_MisStorage {
    
    const MIS_DEFAULT_SIZE = 20;
    const UI_DEFAULT_SIZE = 30;
    const MAX_SIZE = 40;

    /**
     * 设置kv数据
     * @param [type] $arrInput [description]
     * @return [type]           [description]
     */
	public static function saveItem($arrInput) {
		$attrs = array(
			'item_key',
			'item_value',
		);

		$ret = Util_Param::isAllAttrsValid($arrInput, $attrs);

		if ($ret !== true) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR, $ret);
		}

    	$ret = Dl_MisStorage::setStrVal($arrInput['item_key'], $arrInput['item_value']);

    	if ($ret === false) {
    		return Util_Ret::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
    	}

    	return Util_Ret::errRet(Tieba_Errcode::ERR_SUCCESS);
	}

	/**
	 * 获取kv类型的数据
	 * @param  [type] $arrInput [description]
	 * @return [type]           [description]
	 */
	public static function getItem($arrInput) {
		$attrs = array(
			'item_key',
		);

		$ret = Util_Param::isAllAttrsValid($arrInput, $attrs);

		if ($ret !== true) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR, $ret);
		}

    	$ret = Dl_MisStorage::getStrVal($arrInput['item_key']);

    	if ($ret === false) {
    		return Util_Ret::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
    	}

    	return Util_Ret::errRet(Tieba_Errcode::ERR_SUCCESS, $ret);
	}

	/**
	 * 保存item到列表类型数据中
	 * @param  [type] $arrInput [description]
	 * @return [type]           [description]
	 */
	public static function saveListItem($arrInput) {
		$attrs = array(
			'list_key',
			'item_value',
		);

		// 'item_id' 存在时则进行更新操作

		$ret = Util_Param::isAllAttrsValid($arrInput, $attrs);

		if ($ret !== true) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR, $ret);
		}

        $rec = $arrInput['item_value'];

        if (!is_array($rec)) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'item_value must be an object');
        }

        if ($arrInput['item_id']) {
            $rec['item_id'] = $arrInput['item_id'];
        }
        
        $rec['op'] = $arrInput['op'];
        $rec['update_time'] = time();

    	$ret = Dl_MisStorage::saveListItem($arrInput['list_key'], $rec);

    	if ($ret === false) {
    		return Util_Ret::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
    	}

    	return Util_Ret::errRet(Tieba_Errcode::ERR_SUCCESS);
	}

    /**
     * 列表
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getList($arrInput) {
		$attrs = array(
			'list_key',
		);

		$ret = Util_Param::isAllAttrsValid($arrInput, $attrs);

		if ($ret !== true) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR, $ret);
		}

        $listKey = $arrInput['list_key'];
    	$fromMis = $arrInput['from_mis'];
    	$ranges = self::getRanges($arrInput);

		$ret = Dl_MisStorage::listList($listKey, $ranges['start'], $ranges['stop']);

		if ($ret === false) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
		}

		$total = $ret['total'];
		$list = $ret['list'];

        usort($list, array(__CLASS__, 'listDesc'));

        if (!$fromMis) {
            self::removeMisFieldsOfList($list);
        }

		$data = array(
            'page' => array(
                'current_pn' => $ranges['pn'],
                'current_rn' => $ranges['rn'],
                'total_pn' => ceil($total / $ranges['rn']),
                'total_count' => $total,
            ),
            'list' => $list,
        );

        return Util_Ret::errRet(Tieba_Errcode::ERR_SUCCESS, $data);
    }

    /**
     * 计算rn参数
     * @param  [type] $arrInput [description]
     * @return [type]          [description]
     */
    private static function getRanges($arrInput) {
    	$fromMis = $arrInput['from_mis'];

		$pn = empty($arrInput['pn']) ? 1 : intval($arrInput['pn']);
		
        if (empty($arrInput['rn'])) {
            $rn = $fromMis ? self::MIS_DEFAULT_SIZE : self::UI_DEFAULT_SIZE;
        } else {
            $rn = intval($arrInput['rn']);
        }

		$rn = ($rn > self::MAX_SIZE) ? self::MAX_SIZE : $rn;

		return array(
			'pn' => $pn,
			'rn' => $rn,
			'start' => ($pn - 1) * $rn,
			'stop' => $pn * $rn - 1,
		);
    }

    /**
     * 比较函数，按item的修改时间降序排列
     * @param  [array] $a [description]
     * @param  [array] $b [description]
     * @return [bool]    [description]
     */
    private static function listDesc($a, $b) {
        return $a['update_time'] < $b['update_time'];
    }

    /**
     * 去除mis字段
     * @param  [type] &$list [description]
     * @return [type]        [description]
     */
    private static function removeMisFieldsOfList(&$list) {
    	foreach ($list as &$rec) {
    		self::removeMisFields($rec);
    	}
    }

    /**
     * 去除mis字段
     * @param  [type] &$rec [description]
     * @return [type]       [description]
     */
    private static function removeMisFields(&$rec) {
    	if (!is_array($rec)) {
    		return;
    	}

    	$fields = array(
    		'op',
    		'update_time',
    	);

    	foreach ($fields as $field) {
    		unset($rec[$field]);
    	}
    }

    /**
     * 获取列表类型数据中的一个元素内容
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
	public static function getListItem($arrInput) {
		$attrs = array(
			'list_key',
			'item_id',
		);

		$ret = Util_Param::isAllAttrsValid($arrInput, $attrs);

		if ($ret !== true) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR, $ret);
		}

		$ret = Dl_MisStorage::getListItem($arrInput['list_key'], $arrInput['item_id']);

		if ($ret === false) {
			return Util_Ret::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
		}

        self::removeMisFields($ret);

		return Util_Ret::errRet(Tieba_Errcode::ERR_SUCCESS, $ret);
	}

    /**
     * 删除列表类型数据中的一个元素
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function delListItem($arrInput) {
        $attrs = array(
            'list_key',
            'item_id',
        );

        $ret = Util_Param::isAllAttrsValid($arrInput, $attrs);

        if ($ret !== true) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_PARAM_ERROR, $ret);
        }

        $ret = Dl_MisStorage::delListItem($arrInput['list_key'], $arrInput['item_id']);

        if ($ret === false) {
            return Util_Ret::errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        return Util_Ret::errRet(Tieba_Errcode::ERR_SUCCESS);
    }
}
