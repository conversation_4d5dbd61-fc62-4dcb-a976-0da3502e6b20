<?php
/**
 * 多酷星玩家勋章
 * @authors tanxinyun
 * @date    2015-02-08 12:12:41
 */

class Data_StarPlayerMedal {
    const ERR_SUCCESS = 0;
    const ERR_NOT_STAR_PLAER = 1;
    const ERR_LEVEL_NOT_MATCH = 2;
    const ERR_INTERNAL = 3;
    const ERR_APPLIED_BEFORE = 4;

    const QUERY_URL = 'http://youban.m.duoku.com/mobileassistant/getUserInfoForTieBa?params=';
    const QUERY_URL_TEST = 'http://itest.client.duoku.com:9528/mobileassistant/getUserInfoForTieBa?params=';

    const MEDAL_ID_MIN = 1230034;
    const MEDAL_ID_MAX = 1230039;
    // 勋章对应的用户等级，需要倒序排列，以保证下面能获取到可获取的最大勋章id
    private static $_medal_min_levels = array(
        1230039 => 33,
        1230038 => 25,
        1230037 => 17,
        1230036 => 9,
        1230035 => 1,
        1230034 => 0,
    );

    private static function _errRet($errno, $data = null){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        );
    }

    /**
     * 申领勋章
     * @param  [array] $arrInput [user_id, bduss, medal_id]
     * @return [array] $ret [申请结果]
     */
    public static function claim($arrInput) {
        if (empty($arrInput['user_id']) || empty($arrInput['bduss']) || empty($arrInput['medal_id'])
            || $arrInput['medal_id'] < self::MEDAL_ID_MIN || $arrInput['medal_id'] > self::MEDAL_ID_MAX) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $ret = self::_qualify($arrInput);
        // 此时$ret是错误码
        if ($ret < self::MEDAL_ID_MIN) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, array('no' => $ret, 'medal_id' => 0));
        }

        $minMedalId = intval($arrInput['medal_id']);
        $maxMedalId = $ret;
        for ($i = $minMedalId; $i <= $maxMedalId; $i++) {
            $arrInput['medal_id'] = $i;
            $result = self::doGrantMedal($arrInput);
        }

        // 只取最大勋章的发放结果
        // 前面的勋章即使有问题，后面打开星玩家页面时也会自动补领
        if ($result['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            return $result;
        }

        $data = array(
            'no' => $result['data'],
            'medal_id' => $maxMedalId,
        );

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $data);
    }

    /**
     * 调用多酷接口进行等级查询，然后判断等级是否足够申领
     * @param  [array] $arrInput [user_id, bduss]
     * @return [int] retCode/medalId [错误码，或者可授予的最大勋章id]
     */
    private static function _qualify($arrInput) {
        $userInfo = self::_queryUser($arrInput);

        if (empty($userInfo)) {
            return self::ERR_INTERNAL;
        }

        if (empty($userInfo['is_starplayer'])) {
            return self::ERR_NOT_STAR_PLAER;
        }

        if (!isset($userInfo['level'])) {
            return self::ERR_INTERNAL;
        }

        $maxMedalId = 0;
        foreach (self::$_medal_min_levels as $medalId => $minLevel) {
            if ($userInfo['level'] >= $minLevel) {
                $maxMedalId = $medalId;
                break;
            }
        }

        if ($maxMedalId < intval($arrInput['medal_id'])) {
            return self::ERR_LEVEL_NOT_MATCH;
        }

        return $maxMedalId;
    }

    private static function _queryUser($arrInput) {
        $proxy = Orp_FetchUrl::getInstance(array(
            'timeout' => 2000,
            'conn_timeout' => 2000,
            'max_response_size' => 2048000,
        ));

        $url = self::_makeUrl($arrInput);
        $output = $proxy->get($url);   

        if (empty($output)) {
            Bingo_Log::warning('query star player failure, url: '.$url.', ret empty');
            return false;
        }

        $ret = json_decode($output, true);
        if (!isset($ret) || $ret['error_code'] !== 0 || !isset($ret['data'])) {
            Bingo_Log::warning('query star player failure, url: '.$url.', ret: '.$output);
            return false;
        }

        return $ret['data'];
    }

    private static function _makeUrl($arrInput) {
        $ts = time();
        $sign = md5('starplayer_'.$arrInput['user_id'].$arrInput['bduss'].$ts);

        $url = self::QUERY_URL;
        $url .= '{user_id:'.$arrInput['user_id'];
        $url .= ',bduss:'.$arrInput['bduss'];
        $url .= ',timestamp:'.$ts;
        $url .= ',sign:'.$sign.'}';

        return $url;
    }

    // 多酷传递用户信息过来，给这些用户自动颁发勋章
    public static function autoGrantMedal($arrInput) {
        if (empty($arrInput) || empty($arrInput['user_id']) 
            || !isset($arrInput['level']) || empty($arrInput['medal_id'])) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        return self::doGrantMedal($arrInput);
    }

    private static function doGrantMedal($arrInput) {
        // 先获取用户之前已有的勋章
        $ret = self::_getPrevMedals($arrInput);
        if ($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        // 如果之前已领过，就直接返回
        if (self::_hasGotTheMedal($ret['data'], $arrInput)) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, self::ERR_APPLIED_BEFORE);
        }

        // 授予勋章
        $ret = self::_grantMedal($arrInput);

        if ($ret === false || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('failed to grant medal, input: '.json_encode($arrInput).', ret: '.json_encode($ret));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        
        // 激活勋章
        $ret = self::_enableMedal($arrInput);

        if ($ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('failed to enable medal, input: '.json_encode($arrInput).', ret: '.json_encode($ret));
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, self::ERR_SUCCESS);
    }

    // 获取用户之前已有的勋章，返回的是道具id数组
    private static function _getPrevMedals($arrInput) {
        $input = array(
            'user_id' => $arrInput['user_id'],
        );

        return Tieba_Service::call('tbmall', 'getUserOwnPropIdList', $input);
    }

    // 检查用户之前是否已拥有此勋章
    private static function _hasGotTheMedal($prevMedals, $arrInput) {
        $propId = $arrInput['medal_id'];

        if (is_array($prevMedals)) {
            if (in_array($propId, $prevMedals)) {
                return true;
            }
        }

        return false;
    }

    // 给用户发放勋章
    private static function _grantMedal($arrInput) {
        $input = array(
            'user_id' => $arrInput['user_id'],
            'props_id' => $arrInput['medal_id'],
            'buy_num' => 1,
            'order_type' => 0,
            'props_scores' => 0,
        );

        return Tieba_Service::call('tbmall', 'buyOneProps', $input);
    }

    // 激活本勋章为默认展示勋章
    private static function _enableMedal($arrInput) {
        $input = array(
            'user_id' => $arrInput['user_id'],
            'props_id' => $arrInput['medal_id'],
        );

        return Tieba_Service::call('tbmall', 'openProps', $input);
    }

    // 调用此接口，暂时先保存多酷发来的用户等级信息到队列中
    public static function pushUserLevel($arrInput) {
        if (empty($arrInput) || !is_array($arrInput['user_level']) || empty($arrInput['user_level'])) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $userLevels = array($arrInput['user_level']);
        $ret = Dl_StarPlayerMedal::rPush($userLevels);

        if ($ret === false) {
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, $level['user_id']);
        } else {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
    }

    // 定时脚本调用此接口拿到必要信息，给用户颁发相应勋章
    public static function popOneUserLevel() {
        $userLevel = Dl_StarPlayerMedal::lPop();

        if ($userLevel === false) {
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        if (!empty($userLevel)) {
            self::_appendMedalId($userLevel);

            $userId = $userLevel['user_id'];
            Dl_StarPlayerMedal::setUserLevelUpTime($userId, time());
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $userLevel);
    }

    private static function _appendMedalId(&$userLevel) {
        $toGrantMedalId = 0;
        foreach (self::$_medal_min_levels as $medalId => $minLevel) {
            if ($userLevel['level'] >= $minLevel) {
                $toGrantMedalId = $medalId;
                break;
            }
        }

        $userLevel['medal_id'] = $toGrantMedalId;
    }

    public static function getQueueSize() {
        $len = Dl_StarPlayerMedal::lLen();

        if ($len === false) {
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        } else {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $len);
        }
    }

    public static function getUserLevels($arrInput) {
        $start = isset($arrInput['start']) ? isset($arrInput['start']) : 0;
        $stop = isset($arrInput['stop']) ? isset($arrInput['stop']) : -1;

        $recs = Dl_StarPlayerMedal::lRange($start, $stop);

        if ($recs === false) {
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        foreach ($recs as &$rec) {
            self::_appendMedalId($rec);
        }
            
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $recs);
    }

    // 进吧时检查用户是否被多酷通过后台接口升级
    // 如果有升级，则需要告知用户
    public static function getUserLevelUpTime($arrInput) {
        if (empty($arrInput) || empty($arrInput['user_id'])) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $levelUpTime = 0;
        $userId = $arrInput['user_id'];

        $ret = Dl_StarPlayerMedal::getUserLevelUpTime($userId);
        if (!empty($ret)) {
            $levelUpTime = intval($ret);

            Dl_StarPlayerMedal::clearUserLevelUpTime($userId);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $levelUpTime);
    }
}