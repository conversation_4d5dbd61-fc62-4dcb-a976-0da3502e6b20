<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2011-11-18
 * @version 
 */
class Data_Gamescore {
	
	protected static $_conf = null;
	protected static $_cache = null;
	const RANK_LENGTH = 20;
	const MAX_LENGTH = 20;
	/**
	 * @brief init
	 * @return: true if success. false if fail.
	
	 **/
	private static function _init(){
		if(self::$_conf == null){
			self::$_conf = Bd_Conf::getConf("/app/game/service_game");
			if(self::$_conf == false){
				Bingo_Log::warning("init get conf fail.");
				return false;
			}
		}
		return true;
	}
	
	private static function _errRet($errno){
		return array(
				'errno' => $errno,
				'errmsg' => Tieba_Error::getErrmsg($errno),
		);
	}

	//获取用户游戏最高分
	public static function getUserMaxGameScore($arrInput){
		if(!isset($arrInput['user_id'])||!isset($arrInput['game_id'])){
			Bingo_Log::warning("input params invalid. [".print_r($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrInput['user_id']=intval($arrInput['user_id']);
		$arrInput['game_id']=intval($arrInput['game_id']);
		$ret = 	Dl_Gamescore_Gamescore::getUserGameScore($arrInput);
		if($ret === false){
			Bingo_Log::warning("getUserMaxGameScore : call dl getUserGameScore error. [".print_r($arrInput)."]");
			$errno = Tieba_Errcode::ERR_NVOTE_CALL_DL_ERROR;
			return self::_errRet($errno);
		}
		if(empty($ret)){
			Bingo_Log::warning("user not found");
			$output=array();
		}
//		$maxScore = 0;
//		foreach ($ret as $value){
//			if($value['score'] > $maxScore){
//				$output['score'] = $value['score'];
//				$output['user_id'] = $value['user_id'];
//				$output['forum_id'] = $value['forum_id'];
//				$maxScore = $output['score'];
//			}
//		}
		
		$arrOutput = array(
				'errno' => 0,
				'errmsg' => 'sucess',
				'output' => $ret[0],
		);
		return $arrOutput;
	}
	
	//获取贴吧排行
	public static function getForumRank($arrInput){
		if(empty($arrInput['game_id'])){
			Bingo_Log::warning("input params invalid. [".print_r($arrInput['game_id'])."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrInput['game_id']=intval($arrInput['game_id']);
		$ret = 	Dl_Gamescore_Gamescore::getQueue($arrInput['game_id']."_forum",self::RANK_LENGTH);
		if($ret === false){
			Bingo_Log::warning("getForumRank : call dl getQueue error. [".print_r($arrInput['game_id'])."]");
			$errno = Tieba_Errcode::ERR_NVOTE_CALL_DL_ERROR;
			return self::_errRet($errno);
		}
		$errno = 0;
		    $index=0;
        foreach ($ret as $value){
           $ret[$index]['member']=Bingo_String::json2array($value['member']);
           $ret[$index]['score']=$value['score'];
		   $index++;
        }
		$arrOutput = array(
				'errno' => $errno,
				'errmsg' => Tieba_Error::getErrmsg($errno),
				'output' =>array_reverse($ret),
		);
		return $arrOutput;
	}
    //获取用户排行
	public static function getUserRank($arrInput){
		if(empty($arrInput['game_id'])){
			Bingo_Log::warning("input params invalid. [".print_r($arrInput['game_id'])."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrInput['game_id']=intval($arrInput['game_id']);
		$ret = 	Dl_Gamescore_Gamescore::getQueue($arrInput['game_id']."_uid",self::RANK_LENGTH);
		if($ret === false){
			Bingo_Log::warning("getUserRank : call dl getQueue error. [".print_r($arrInput['game_id'])."]");
			$errno = Tieba_Errcode::ERR_NVOTE_CALL_DL_ERROR;
			return self::_errRet($errno);
		}
		$errno = 0;
	    $index=0;
        foreach ($ret as $value) {
           $ret[$index]['member']=Bingo_String::json2array($value['member']);
           $ret[$index]['score']=$value['score'];
		   $index++;
        }
		$arrOutput = array(
				'errno' => $errno,
				'errmsg' => Tieba_Error::getErrmsg($errno),
				'output' =>array_reverse($ret),
		);
		return $arrOutput;
	}

    //更新用户最高分   
    public static function setUserMaxGameScore($arrInput){
		if(!isset($arrInput['user_id'])||!isset($arrInput['game_id'])||!isset($arrInput['forum_id'])||!isset($arrInput['score'])){
			Bingo_Log::warning("input params invalid. [".print_r($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrInput['user_id']=intval($arrInput['user_id']);
		$arrInput['game_id']=intval($arrInput['game_id']);
		$arrInput['forum_id']=intval($arrInput['forum_id']);
		$arrInput['score']=intval($arrInput['score']);
		//更新数据库表
		$ret = 	Dl_Gamescore_Gamescore::getGameScore($arrInput);
		if($ret === false){
			Bingo_Log::warning("setUserMaxGameScore : call dl getGameScore error. [".print_r($arrInput)."]");
			$errno = Tieba_Errcode::ERR_NVOTE_CALL_DL_ERROR;
			return self::_errRet($errno);
		}
		else {
			if(empty($ret)){
				$ret = 	Dl_Gamescore_Gamescore::insertUserMaxGameScore($arrInput);
				if($ret === false){
					Bingo_Log::warning("setUserMaxGameScore : call dl insertUserMaxGameScore error. [".print_r($arrInput)."]");
					$errno = Tieba_Errcode::ERR_NVOTE_CALL_DL_ERROR;
					return self::_errRet($errno);
				}
			}
			else {
				foreach ($ret as $value){
					if($value['score'] <= $arrInput['score']){
						$ret = 	Dl_Gamescore_Gamescore::updateUserMaxGameScore($arrInput);
						if($ret === false){
							Bingo_Log::warning("setUserMaxGameScore : call dl updateUserMaxGameScore error. [".print_r($arrInput)."]");
							$errno = Tieba_Errcode::ERR_NVOTE_CALL_DL_ERROR;
							return self::_errRet($errno);
						}
					}
					break;
				}
			}

			//更新贴吧redis队列
			$arrForumInput = $arrInput;
			$arrForumInput['queue']='forum';
			$arrForumInput['game_id'] = $arrForumInput['game_id'].'_forum';
			$ret = self::_updateQueue($arrForumInput);
			if($ret['errno']!=0){
					Bingo_Log::warning("_updateQueue  error. [".print_r($arrForumInput)."]");
					return $ret;
			}
			//更新用户redis队列
			$arrUidInput = $arrInput;
			$arrUidInput['queue']='uid';
			$arrUidInput['game_id'] = $arrUidInput['game_id'].'_uid';
			$ret = self::_updateQueue($arrUidInput);
			if($ret['errno']!=0){
					Bingo_Log::warning("_updateQueue  error. [".print_r($arrUidInput)."]");
					return $ret;
			}
		}

		$errno = 0;
		$arrOutput = array(
				'errno' => $errno,
				'errmsg' => Tieba_Error::getErrmsg($errno),
		);
		return $arrOutput;
    }
    
    /*更新队列中元素*/
       private  static function _updateQueue($arrInput){
            //查找队列中是已有相应记录
			$ret = Dl_Gamescore_Gamescore::getQueue($arrInput['game_id'],self::MAX_LENGTH);
			if($ret === false){
				Bingo_Log::warning("_updateQueue : call dl getQueue error. [".print_r($arrInput['game_id'])."]");
				$errno = Tieba_Errcode::ERR_NVOTE_CALL_DL_ERROR;
				return self::_errRet($errno);
			}
			
			$flag = 0;//0表示队列中没有对应的记录
			$count = 0;
			
			$member = array(
				'user_id' => $arrInput['user_id'],
				'forum_id' => $arrInput['forum_id'],
			);
			$arrInput['member']=Bingo_String::array2json($member);
			if($arrInput['queue']=='uid'){
				foreach ($ret as $value) {
					$value['member']=Bingo_String::json2array($value['member']);
					if($value['member']['user_id']==$arrInput['user_id']){
						$flag = 1;//1表示队列中有对应记录，但是不需更新最大值
						if($value['score'] <= $arrInput['score']){//如果队列中已经有user_id，更新最大值
							$flag = 2;//2表示队列中各有对应记录，而且需要更新最大值
						}
						break; 
					}
					$count++;//count表示需要更新的是第几条记录
				}
			}
			else {
				foreach ($ret as $value) {
					$value['member']=Bingo_String::json2array($value['member']);
					if($value['member']['forum_id']==$arrInput['forum_id']){
						$flag = 1;//1表示队列中有对应记录，但是不需更新最大值
						if($value['score'] <= $arrInput['score']){//如果队列中已经有user_id，更新最大值
							$flag = 2;//2表示队列中各有对应记录，而且需要更新最大值
						}
						break;
					}
					$count++;//count表示需要更新的是第几条记录
				}
			}
			if($flag == 2){//更新队列（删除旧记录，插入新纪录）
				//删除需要更新的那条元素
				$ret = Dl_Gamescore_Gamescore::delFromQueue($arrInput,$count);
				if($ret === false){
					Bingo_Log::warning("_updateQueue : call dl delFromQueue error. [".print_r($arrInput)."]");
					$errno = Tieba_Errcode::ERR_NVOTE_CALL_DL_ERROR;
					return self::_errRet($errno);
				}
				
				$ret = Dl_Gamescore_Gamescore::pushToQueue($arrInput);
				if($ret === false){
					Bingo_Log::warning("_updateQueue : call dl pushQueue error. [".print_r($arrInput)."]");
					$errno = Tieba_Errcode::ERR_NVOTE_CALL_DL_ERROR;
					return self::_errRet($errno);
				}
				
			}
			else if($flag == 1){
				//不需要处理
			}
			else if($flag == 0){//判断元素个数+队列中最小值
				$intQueueLength = Dl_Gamescore_Gamescore::getQueueLength($arrInput['game_id']);
				if($intQueueLength === false){
					Bingo_Log::warning("_updateQueue : call dl getQueueLength error. [".print_r($arrInput['game_id'])."]");
					$errno = Tieba_Errcode::ERR_NVOTE_CALL_DL_ERROR;
					return self::_errRet($errno);
				}
				if($intQueueLength < self::MAX_LENGTH){//如果队列元素不到MAX_LENGTH，直接插入到队列
					$ret = Dl_Gamescore_Gamescore::pushToQueue($arrInput);
					if($ret === false){
						Bingo_Log::warning("getQueue : call dl pushToQueue error. [".print_r($arrInput)."]");
						$errno = Tieba_Errcode::ERR_NVOTE_CALL_DL_ERROR;
						return self::_errRet($errno);
					}
				}
				else{//查找最小值是否大于当前值，并决定是否需要更新最小值
					$ret = Dl_Gamescore_Gamescore::getQueue($arrInput['game_id'],1);
					if($ret === false){
						Bingo_Log::warning("_updateQueue : call dl getQueue error. [".print_r($arrInput)."]");
						$errno = Tieba_Errcode::ERR_NVOTE_CALL_DL_ERROR;
						return self::_errRet($errno);
					}
					foreach ($ret as $value) {//??
						if($value['score'] <= $arrInput['score']){
							//删除需要更新的那条元素
							$ret = Dl_Gamescore_Gamescore::delFromQueue($arrInput,0);
							if($ret === false){
								Bingo_Log::warning("_updateQueue : call dl delFromQueue error. [".print_r($arrInput)."]");
								$errno = Tieba_Errcode::ERR_NVOTE_CALL_DL_ERROR;
								return self::_errRet($errno);
							}
							//添加新记录
							$ret = Dl_Gamescore_Gamescore::pushToQueue($arrInput);
							if($ret === false){
								Bingo_Log::warning("_updateQueue : call dl pushToQueue error. [".print_r($arrInput)."]");
								$errno = Tieba_Errcode::ERR_NVOTE_CALL_DL_ERROR;
								return self::_errRet($errno);
							}
							
						}
						break;
					}
				}
			}
			$errno = 0;
			$arrOutput = array(
				'errno' => $errno,
				'errmsg' => Tieba_Error::getErrmsg($errno),
			);
			return $arrOutput;
       }
}
