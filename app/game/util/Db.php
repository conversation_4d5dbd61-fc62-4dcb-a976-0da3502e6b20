<?php

class Util_Db
{
	const DATABASE_NAME = "forum_gameforum";
	private static $_dbs = array();
	
	/**
	 * @param null $dbname
	 * @param string $charset
	 *
	 * @return null
	 */
	public static function getDB($dbname = null, $charset = 'utf8')
	{
		if(is_null($dbname)){
			$dbname = self::DATABASE_NAME;
		}
		$objTbMysql = Tieba_Mysql::getDB($dbname);
		if($objTbMysql && $objTbMysql->isConnected()){
			$objTbMysql->charset($charset);
			
			return $objTbMysql;
		}else{
			Bingo_Log::warning("db connect fail.");
			
			return null;
		}
	}
	
	/**
	 * @param null $dbname
	 * @param string $charset
	 *
	 * @return mixed|null
	 */
	public static function getDBWithCache($dbname = null, $charset = 'utf8')
	{
		if(is_null($dbname)){
			$dbname = self::DATABASE_NAME;
		}
		if(isset(self::$_dbs[$dbname])){
			return self::$_dbs[$dbname];
		}
		$db                  = self::getDB($dbname, $charset);
		self::$_dbs[$dbname] = $db;
		
		return $db;
	}
	
	/**
	 * @param $arrInput
	 *
	 * @return array
	 */
	public static function insert($arrInput)
	{
		if(!isset($arrInput['table']) || !isset($arrInput['field']) || !is_array($arrInput['field'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			
			return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$table = $arrInput['table'];
		if(isset($arrInput['db'])){
			$db = $arrInput['db'];
		}else{
			$db = Util_Db::getDB();
		}
		if(is_null($db)){
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$fields   = $arrInput['field'];
		$strOnDup = null;
		if(isset($arrInput['onDup']) && is_array($arrInput['onDup'])){
			foreach($arrInput['onDup'] as $key => $value){
				if($strOnDup !== null){
					$strOnDup .= ",";
				}
				$key      = mysql_escape_string(trim($key));
				$value    = mysql_escape_string(trim($value));
				$strOnDup .= "$key '$value' ";
			}
		}
		$ret = $db->insert($table, $fields, null, $strOnDup);
		if($ret <= 0){
			Bingo_Log::warning("[output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
			
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$insertId = $db->getInsertID();
		
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $insertId);
	}
	
	/**
	 * @param $arrInput
	 *
	 * @return array
	 */
	public static function update($arrInput)
	{
		if(!isset($arrInput['table']) || !isset($arrInput['field']) || !is_array($arrInput['field'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			
			return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$table    = $arrInput['table'];
		$strField = null;
		foreach($arrInput['field'] as $key => $value){
			if($strField !== null){
				$strField .= ",";
			}
			$key      = mysql_escape_string(trim($key));
			$value    = mysql_escape_string(trim($value));
			$strField .= "$key'$value' ";
		}
		$strCond = null;
		if(isset($arrInput['cond']) && is_array($arrInput['cond'])){
			foreach($arrInput['cond'] as $cond => $value){
				if($strCond !== null){
					$strCond .= " and ";
				}
				if(is_array($value)){
					$value   = array_map(array("Util_Db", "_addQuote"), $value);
					$strCond .= "$cond in (".implode(",", $value).") ";
				}else{
					$key     = mysql_escape_string(trim($cond));
					$value   = mysql_escape_string(trim($value));
					$strCond .= "$key '$value' ";
				}
			}
		}
		$append = null;
		if(isset($arrInput['append'])){
			$append = $arrInput['append'];
		}
		if(isset($arrInput['db'])){
			$db = $arrInput['db'];
		}else{
			$db = Util_Db::getDB();
		}
		if(is_null($db)){
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$ret = $db->update($table, $strField, $strCond, null, $append);
		if($ret === false){
			Bingo_Log::warning("[output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
			
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$affectedRows = $db->getAffectedRows();
		
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $affectedRows);
	}
	
	/**
	 * @param $str
	 *
	 * @return string
	 */
	private static function _addQuote($str)
	{
		return "'$str'";
	}
	
	/**
	 * @param $arrInput
	 *
	 * @return array
	 */
	public static function query($arrInput)
	{
		if(isset($arrInput['db'])){
			$db = $arrInput['db'];
		}else{
			$db = Util_Db::getDB();
		}
		if(is_null($db)){
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$ret = $db->query($arrInput['sql']);
		if($ret === false){
			Bingo_Log::warning("[output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
			
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $ret);
	}
	
	/**
	 * @param $arrInput
	 *
	 * @return array
	 */
	public static function select($arrInput)
	{
		if(!isset($arrInput['table']) || !isset($arrInput['field']) || !is_array($arrInput['field'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			
			return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$table     = $arrInput['table'];
		$arrFields = $arrInput['field'];
		$strCond   = null;
		if(isset($arrInput['cond']) && is_array($arrInput['cond'])){
			foreach($arrInput['cond'] as $cond => $value){
				if($strCond !== null){
					$strCond .= " and ";
				}
				if(is_array($value)){
					$value   = array_map(array("Util_Db", "_addQuote"), $value);
					$strCond .= "$cond in (".implode(",", $value).") ";
				}else{
					$key     = mysql_escape_string(trim($cond));
					$value   = mysql_escape_string(trim($value));
					$strCond .= "$key '$value' ";
				}
			}
		}
		$strOr = null;
		if(isset($arrInput['or']) && is_array($arrInput['or'])){
			foreach($arrInput['or'] as $cond => $value){
				if($strOr !== null){
					$strOr .= ' or ';
				}
				if(is_array($value)){
					$value = array_map(array('Util_Db', '_addQuote'), $value);
					$strOr .= "$cond in (".implode(', ', $value).") ";
				}else{
					$key   = mysql_escape_string(trim($cond));
					$value = mysql_escape_string(trim($value));
					$strOr .= "$key '$value' ";
				}
			}
		}
		if($strOr !== null){
			$strCond .= 'and ('.$strOr.' ) ';
		}
		if($strCond === null){
			$strCond = $strOr;
		}
		$strAppend = null;
		if(isset($arrInput['append'])){
			$strAppend = $arrInput['append'];
		}
		if(isset($arrInput['db'])){
			$db = $arrInput['db'];
		}else{
			$db = Util_Db::getDB();
		}
		if(is_null($db)){
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$ret = $db->select($table, $arrFields, $strCond, null, $strAppend);
		if($ret === false){
			Bingo_Log::warning("[output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
			
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $ret);
	}
	
	/**
	 * @param array $arrInput
	 *
	 * @return array
	 */
	public function replace($arrInput = array())
	{
		if(!isset($arrInput['table']) || !isset($arrInput['field']) || !is_array($arrInput['data'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			
			return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$table = $arrInput['table'];
		$field = $arrInput['field'];
		$data  = $arrInput['data'];
		if(isset($arrInput['db'])){
			$db = $arrInput['db'];
		}else{
			$db = Util_Db::getDB();
		}
		if(is_null($db)){
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$sql = self::replaceSql($table, $field, $data);
		$res = $db->query($sql);
		if(false === $res){
			Bingo_Log::warning("[output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
			
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$affectedRows = $db->getAffectedRows();
		
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $affectedRows);
	}
	
	/**
	 * @param $table
	 * @param $field
	 * @param $data
	 *
	 * @return string
	 */
	public function replaceSql($table, $field, $data)
	{
		$sql      = "replace into {$table} ";
		$fieldArr = $valArr = $dataStr = '';
		foreach($field as $fval){
			$fval       = mysql_escape_string(trim($fval));
			$fieldArr[] = "`$fval`";
		}
		$sql     .= " (".implode(',', $fieldArr)." )";
		$dataArr = array();
		foreach($data as $tmp){
			$valArr = array();
			foreach($tmp as $value){
				$value    = mysql_escape_string(trim($value));
				$valArr[] = "'$value'";
			}
			$dataArr[] = " (".implode(',', $valArr).") ";
		}
		$dataStr = implode(',', $dataArr);
		$sql     .= " values ".$dataStr;
		
		return $sql;
	}
	
	/**
	 * @param $arrInput
	 *
	 * @return array
	 */
	public static function delete($arrInput)
	{
		if(!isset($arrInput['table']) || !isset($arrInput['cond']) || !is_array($arrInput['cond'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			
			return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$table   = $arrInput['table'];
		$strCond = null;
		if(isset($arrInput['cond']) && is_array($arrInput['cond'])){
			foreach($arrInput['cond'] as $cond => $value){
				if($strCond !== null){
					$strCond .= " and ";
				}
				if(is_array($value)){
					$value   = array_map(array("Util_Db", "_addQuote"), $value);
					$strCond .= "$cond in (".implode(",", $value).") ";
				}else{
					$key     = mysql_escape_string(trim($cond));
					$value   = mysql_escape_string(trim($value));
					$strCond .= "$key '$value' ";
				}
			}
		}
		$append = null;
		if(isset($arrInput['append'])){
			$append = $arrInput['append'];
		}
		if(isset($arrInput['db'])){
			$db = $arrInput['db'];
		}else{
			$db = Util_Db::getDB();
		}
		if(is_null($db)){
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$ret = $db->delete($table, $strCond, null, $append);
		if($ret === false){
			Bingo_Log::warning("[output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
			
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$affectedRows = $db->getAffectedRows();
		
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $affectedRows);
	}
	
	/**
	 * @param $arrInput
	 *
	 * @return array
	 */
	public static function multiInsert($arrInput)
	{
		if(!isset($arrInput['table']) || !isset($arrInput['field']) || !is_array($arrInput['field']) || !isset($arrInput['data']) || !is_array($arrInput['data'])){
			Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
			
			return Util_Function::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		if(isset($arrInput['db'])){
			$db = $arrInput['db'];
		}else{
			$db = Util_Db::getDB();
		}
		if(is_null($db)){
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
		$sql = self::multiInsertSql($arrInput['table'], $arrInput['field'], $arrInput['data']);
		$ret = $db->query($sql);
		if($ret <= 0){
			Bingo_Log::warning("[output:".serialize($ret)."error:".$db->error()."sql:".$db->getLastSQL()."]");
			
			return Util_Function::errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		$insertId = $db->getInsertID();
		
		return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $insertId);
	}
	
	/**
	 * @param $table
	 * @param $field
	 * @param
	 *
	 * @return string
	 */
	public function multiInsertSql($table, $field, $data)
	{
		$sql      = "insert into {$table} ";
		$fieldArr = $valArr = $dataStr = '';
		foreach($field as $fval){
			$fval       = mysql_escape_string(trim($fval));
			$fieldArr[] = "`$fval`";
		}
		$sql     .= " (".implode(',', $fieldArr)." )";
		$dataArr = array();
		foreach($data as $tmp){
			$valArr = array();
			foreach($tmp as $value){
				$value    = mysql_escape_string(trim($value));
				$valArr[] = "'$value'";
			}
			$dataArr[] = " (".implode(',', $valArr).") ";
		}
		$dataStr = implode(',', $dataArr);
		$sql     .= " values ".$dataStr;
		
		return $sql;
	}
	
	/**
	 * @param $arrElements
	 * @param $arrValue
	 * @param $arrConds
	 *  添加相等条件
	 *
	 * @return mixed
	 */
	public static function addCondEquals($arrElements, $arrValue, $arrConds)
	{
		$ret = $arrConds;
		foreach($arrElements as $value){
			if(isset($arrValue[$value])){
				$ret["$value".'='] = $arrValue[$value];
			}
		}
		
		return $ret;
	}
	
	/**
	 * @param $arrElements
	 * @param $arrValue
	 * @param $arrConds
	 *  添加大于条件
	 *
	 * @return mixed
	 */
	public static function addCondLager($arrElements, $arrValue, $arrConds)
	{
		$ret = $arrConds;
		foreach($arrElements as $value){
			if(isset($arrValue[$value])){
				$ret["$value".'>'] = $arrValue[$value];
			}
		}
		
		return $ret;
	}
	
	/**
	 * @param $arrElements
	 * @param $arrValue
	 * @param $arrConds
	 *  添加小于条件
	 *
	 * @return mixed
	 */
	public static function addCondSmaller($arrElements, $arrValue, $arrConds)
	{
		$ret = $arrConds;
		foreach($arrElements as $value){
			if(isset($arrValue[$value])){
				$ret["$value".'<'] = $arrValue[$value];
			}
		}
		
		return $ret;
	}
}