<?php

class Util_Log{

	/**
	 * ��־��ӡ�� ����stlog��ҵ����־�е�notice
	 *
	 * @param unknown_type $key
	 * @param unknown_type $value
	 **/
    CONST secret_key = 'LWPG4Dqc50Ga';
	public static function pushNotice($key,$value){

		Tieba_Stlog::addNode ($key,$value);
		Bingo_Log::pushNotice ($key,$value);
	}


	public static function buildNotice(){
		Tieba_Stlog::notice();
		Bingo_Log::buildNotice();
	}
	
	public static function setB2Log($obj, $arrInput){
		$obj->setLogContext($arrInput['common']);
		$obj->set('pro','tieba');
		$obj->set('mid','game');			
		$obj->set('line',2);
		$obj->set('task','商业化游戏推广');
		$obj->set('action_type',1);
		$obj->set('urlkey', Bingo_Controller_Front::getInstance()->getDispatchRouter());
		$obj->set('page',$arrInput['page']);
		$obj->set('locate',$arrInput['locate']);
	}

}
