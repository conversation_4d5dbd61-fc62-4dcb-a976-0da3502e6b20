<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2017-08-08
 * @version
 */

class Util_Memcached {

    private static $_cache;
        
    //cache的pid
    const CACHE_PID = 'forum_ala';
    
    //cache配置总开关，方便测试
	const SWITCH_OF_CACHE = true;
	
	//所有cache的key的前缀，修改前缀即可失效现有所有cache
	const PREFIX_ALL_KEY = 'ala_';

    //初始化cache
    /**
     * @param unknown 
     * @return {}
     */
    public static function initCache(){
    	if(false === self::SWITCH_OF_CACHE) {
    		return null;
    	}
        if(self::$_cache){
            return self::$_cache ;
        }
        
        Bingo_Timer::start('memcached_init');
        self::$_cache = new Bingo_Cache_Memcached(self::CACHE_PID);
        Bingo_Timer::end('memcached_init');

        if(!self::$_cache || !self::$_cache->isEnable()){
            Bingo_Log::warning("init cache fail.");
            self::$_cache = null;
            return null;
        }
        return self::$_cache;
    }
    //获取单个cache
    /**
     * @param unknown 
     * @return {}
     */
	public static function getCache($strKey){
    	if(false === self::SWITCH_OF_CACHE) {
    		return null;
    	}
        if(!self::$_cache){
        	self::initCache();
        	if(null === self::$_cache || !self::$_cache->isEnable()) {
        		return null;
        	}
        }
        $strKey = self::PREFIX_ALL_KEY.$strKey;
        Bingo_Timer::start('memcached_get');
        $mixRes = self::$_cache->get($strKey);
        Bingo_Timer::end('memcached_get');
        //Bingo_Log::warning("getCache".var_export($strKey,true) );
        return $mixRes;
	}
    //批量获取cache
    /**
     * @param unknown 
     * @return {}
     */
	public static function mgetCache($arrKey){
    	if(false === self::SWITCH_OF_CACHE) {
    		return null;
    	}
        if(!self::$_cache){
        	self::initCache();
        	if(null === self::$_cache || !self::$_cache->isEnable()) {
        		return null;
        	}
        }
        $arrKeysTrans = array();
        foreach ($arrKey as $strKey) {
        	$arrKeysTrans[] = self::PREFIX_ALL_KEY.$strKey;
        }
        Bingo_Timer::start('memcached_mget');
        $mixRes = self::$_cache->multipleGet($arrKeysTrans);
        Bingo_Timer::end('memcached_mget');
        //Bingo_Log::warning("mgetCachesuccess".serialize($arrKeysTrans) );

		$arrRes = array();
		foreach ($mixRes as $key => $res) {
			$key = substr($key, strlen(self::PREFIX_ALL_KEY));
			$arrRes[$key] = $res;
		}
        return $arrRes;
	}	
	//删除cache
	/**
     * @param unknown 
     * @return {}
     */
	public static function removeCache($strKey){
    	if(false === self::SWITCH_OF_CACHE) {
    		return true;
    	}
        if(!self::$_cache){
        	self::initCache();
        	if(null === self::$_cache || !self::$_cache->isEnable()) {
        		return false;
        	}
        }

        $strKey = self::PREFIX_ALL_KEY.$strKey;
        Bingo_Timer::start('memcached_del');
        $mixRes = self::$_cache->remove(strval($strKey));
        Bingo_Timer::end('memcached_del');
        if($mixRes === CACHE_OK){
            Bingo_Log::warning("remove cache success".serialize($strKey) );
        	return true;
        }
        else{  
        	Bingo_Log::warning('remove cache err no : '.$mixRes." key[$strKey]");
        	return false;
        }
	}
	//设置cache
	/**
     * @param unknown 
     * @return {}
     */
	public static function addCache($strKey, $mixValue, $intLifeTime){
    	if(false === self::SWITCH_OF_CACHE) {
    		return true;
    	}

        if(!self::$_cache){
        	self::initCache();
        	if(null === self::$_cache || !self::$_cache->isEnable()) {
        		return false;
        	}
        }
        $strKey = self::PREFIX_ALL_KEY.$strKey;
        $intLifeTime = intval($intLifeTime);
        Bingo_Timer::start('memcached_add');
        $mixRes = self::$_cache->add(strval($strKey), $mixValue, $intLifeTime);
        Bingo_Timer::end('memcached_add');
        if($mixRes === CACHE_OK){
            //Bingo_Log::warning("add cache success".serialize($strKey) );
        	return true;
        }else{  
        	Bingo_Log::warning('add cache err no : '.$mixRes." key[$strKey] val[".
        		serialize($mixValue)."] time[$intLifeTime]");
        	return false;
        }
	}
	
	//设置mcache
	/**
     * @param unknown 
     * @return {}
     */
	public static function maddCache($arrValues){
	    if(false === self::SWITCH_OF_CACHE) {
	        return true;
	    }
	
	    if(!self::$_cache){
	        self::initCache();
	        if(null === self::$_cache || !self::$_cache->isEnable()) {
	            return false;
	        }
	    }
	    $arrValuesTrans = array();
	    foreach($arrValues as $v){
	        $v["key"] =  self::PREFIX_ALL_KEY.$v["key"];
	        $arrValuesTrans[] = $v;
	    }
	    
	    Bingo_Timer::start('memcached_multipleAdd');
	    $mixRes = self::$_cache->multipleAdd($arrValuesTrans);
	    Bingo_Timer::end('memcached_multipleAdd');
	    foreach ($mixRes as $key=>$val)
	    {
	        $errno = intval($val);
	        if (0 !== $errno)
	        {
	            Bingo_Log::warning("madd cache key=$key, val=".serialize($val));
	        }else {
	            //Bingo_Log::warning("madd cache success".serialize($key) );
	        }
	    }
	    
	    return true;
	}

	/**
	 * msetcache
	 * @param $arrValues
	 * @return bool
	 */
	public static function msetCache($arrValues){
		if(false === self::SWITCH_OF_CACHE) {
			return true;
		}

		if(!self::$_cache){
			self::initCache();
			if(null === self::$_cache || !self::$_cache->isEnable()) {
				return false;
			}
		}
		$arrValuesTrans = array();
		foreach($arrValues as $v){
			$v["key"] =  self::PREFIX_ALL_KEY.$v["key"];
			$arrValuesTrans[] = $v;
		}

		Bingo_Timer::start('memcached_multipleSet');
		$mixRes = self::$_cache->multipleSet($arrValuesTrans);
		Bingo_Timer::end('memcached_multipleSet');
		foreach ($mixRes as $key=>$val)
		{
			$errno = intval($val);
			if (0 !== $errno)
			{
				Bingo_Log::warning("mset cache fail key=$key, val=".serialize($val));
			}else {
				//Bingo_Log::warning("madd cache success".serialize($key) );
			}
		}

		return true;
	}

	//清mcache
	/**
     * @param unknown 
     * @return {}
     */
	public static function mremoveCache($arrKey){
	    if(false === self::SWITCH_OF_CACHE) {
	        return true;
	    }
	
	    if(!self::$_cache){
	        self::initCache();
	        if(null === self::$_cache || !self::$_cache->isEnable()) {
	            return false;
	        }
	    }
	    $arrKeysTrans = array();
	    foreach($arrKey as $v){
	        $arrKeysTrans[] =  self::PREFIX_ALL_KEY.$v;
	         
	    }
	    Bingo_Timer::start('memcached_multipleRemove');
	    $mixRes = self::$_cache->multipleRemove($arrKeysTrans);
	    Bingo_Timer::end('memcached_multipleRemove');
	    foreach ($mixRes as $key=>$val)
	    {
	        $errno = intval($val);
	        if (0 !== $errno)
	        {
	            Bingo_Log::warning("multipleRemove cache key=$key, val=".serialize($val) );
	        } else {
	           // Bingo_Log::warning("multipleRemove cache success".serialize($key) );
	        }
	    }
	    
	    return true;
	}
	
}
