<?php

class addRank {
	
	const REDIS_WORLDCUP_USER_RANK_LIST = 'redis_worldcup_user_rank_list';   //�û���ǩ��������
	const REDIS_WORLDCUP_TEAM_RANK_LIST = 'redis_worldcup_teamscore_rank_list';   //��ӱ����ܴ�������
	
	private static $UserRankList = array(
			'ba1111year' =>     271,
			'agenting停停停'=>      262,
			'backpksmml'   =>       153,
			'肉丝茄子加鸡蛋' => 109,
			'h_a_ofcb'   =>   97,
			'chelseaclub' =>    77,
			'liveuefalive' =>    64,
			'cc爱问tveee'    => 52,
			'快乐的小小小白领'   =>  50,
			'秋天的bigbox'   =>   41,
	);
	
	private static $teamRankList = array(
			'巴西'  => 543,
			'西班牙' =>  471,
			'乌拉圭' =>  467,
			'法国'  =>   437,
			'阿根廷' =>    398,
			'德国'  =>     316,
			'哥伦比亚' =>  286,
			'英格兰'  =>    214,
			'意大利'  =>   139,
			'尼日利亚' =>   98,
	);
	
	public static function setRank(){
		$redis = new Bingo_Cache_Redis ( 'bdrpecom');
		foreach(self::$UserRankList as $key=>$value){
			$strRedisKey = self::REDIS_WORLDCUP_USER_RANK_LIST;   //�û����е��ܴ���
			$arrReq = array(
					'key' => $strRedisKey,
					'member' =>$key,
					'step' =>$value,
			);
			$arrParams[] = $arrReq;
		}
		foreach(self::$teamRankList as $key=>$value){
			$strRedisKey = self::REDIS_WORLDCUP_TEAM_RANK_LIST;   //��ӱ����д���
			$arrReq = array(
					'key' => $strRedisKey,
					'member' =>$key,
					'step' =>$value,
			);
			$arrParams[] = $arrReq;
		}		
		$input['reqs'] = $arrParams;
		$retRedis = $redis->ZINCRBY($input);
		var_dump($input);
		var_dump($retRedis);
	}

}

$rank = new addRank();
$rank->setRank();


