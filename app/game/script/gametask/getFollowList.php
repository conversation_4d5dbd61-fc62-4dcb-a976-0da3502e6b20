<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file getFollowList.php
 * <AUTHOR>
 * @date 2015/04/09 21:54:17
 * @brief 
 *  
 **/
$url = "http://imis.tieba.baidu.com/game/followForum/getFollowForum?flag=main";
$url =
"http://service.tieba.baidu.com/service/game?method=getMcomStore&format=json&app=follow_forum&pn=0&rn=100&ie=utf-8";
$url =
"http://service.tieba.baidu.com/service/game?method=getMcomStore&app=follow_forum&pn=0&rn=100";
$objOrp = Orp_FetchUrl::getInstance();
$ret = $objOrp->get($url);
$ret = unserialize($ret);
$arrFollowList = array();
foreach($ret['data']['list'] as $key=>$value){
    $game_list = array();
    foreach($value['game_conf'] as $item){
        $game_list[] = $item['game_name'];
    }
    $arrFollowList[] = $tmp;
    $arrInput = array(
        'task_name' =>'关注浮层_'. $value['second_class'],
        'start_time' => time(),
        'end_time' => time() + 86400*365*50,
        'op_name' => 'jiangzhibin',
        'first_dirs' => '',
        'second_dirs' => $value['second_class'],
        'virtual_dirs' => '',
        'forum_names' => '',
        'black_names' => '',
        'pos_name' => 'follow_pop',
        'page_name' => 'frs',
        'ext' => array(
            'game_list' => $game_list   
        )
    );
    $arrRet =
    Tieba_Service::call('game','addNewGameTaskInfo',$arrInput,null,null,'POST','php','utf-8');
    var_dump($arrRet);
}






/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
