<?php
/**
 * 
 * 22308802
*/
ini_set ( "memory_limit", "-1" );
define('MODULE_NAME', 'game');
date_default_timezone_set ( "Asia/Chongqing" );
$arrSubTab   = array("全部资讯","小编推荐","吧友分享","官方推荐");
$strForumIds = "15032583,120508,22308802,16027621,16310934,23270241,23671251,2337850,2940165,2293,4868745,7826685,7500954,4777675,5187899,2293,7803030,1018532,327627,309955,12342493,672678,17537508,16791220,22353267,707597,2862817,23910389,81570,113444,728957,24551955,19168483,1701120,14823520,23985471,837839";
$arrForumIds = explode(",", $strForumIds);
//var_dump($arrForumIds);exit;
$strKw = "我的世界";
$strForumName = "";
$arrRes = array(
    'info_type_id'  => 65,
    'guide_type_id' => 64,
);
foreach ($arrForumIds as $k => $intForumId) {
    //echo $intForumId;
    $intForumId = 22308802;
    $arrRet = Tieba_Service::call('beyond', 'getGameForumType', array('forum_id' => $intForumId), null, null, 'post', 'php', 'gbk');
    //资讯ID
    $intInfoId = intval($arrRet['data']['info_type_id']);var_dump($intInfoId);

   
    $arr_post_data = array(
        "forum_id"    => $intForumId,
        "root_tab_id" => $intInfoId,
    );
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "http://tc.service.tieba.baidu.com/service/game?method=findAllTabLabelByForumId");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);  
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $arr_post_data);
    $output = curl_exec($ch);
    curl_close($ch);
    $arrOut = unserialize($output);var_dump($arrOut);
    //$arrOut['data'] = Bingo_Encode::convert($arrOut['data'],Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
    $arrTmpTabs = array();
    if (count($arrOut['data']) < 4) {
        echo $intForumId . "\n";
    } else {
        continue;
    }
    
    //var_dump($arrOut['data']);
    foreach ($arrOut['data'] as $key => $value) {
        if (in_array($value['sub_tab_name'] , $arrSubTab)) {
            $arrTmpTabs[] = $value['sub_tab_name'];
        }
    }
    foreach ($arrSubTab as $key => $value) {
        if (in_array($value, $arrTmpTabs)) {

            continue;
        }

        if ($value == "全部资讯") {
            $source = -1;
            $sub_source = 0;
        } elseif ($value == "小编推荐") {
            $sub_source = 1;
            $source = 0;
        } elseif ($value == "吧友分享") {
            $sub_source = 2;
            $source = 3;
        } elseif ($value == "官方推荐") {
            $sub_source = 3;
            $source = 21;
        }
        //获取贴子

        $arrParam = array(
            "pn" => 1,
            "sz" => 1,
            "type" => $intInfoId,
            "sub_type" => 0,
            "status"   => 1,
            "source"   => $source,
        );
        if ($source < 0) {
            unset($arrParam["source"]);
        }
        $arrRes = Tieba_Service::call('beyond', 'getFeedListByCondation', $arrParam, null, null, 'post', 'php', 'utf-8');
        if ($arrRes === false || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            echo ("call service beyond->getFeedListByCondation error! output:".serialize($arrRes) . "input: " . serialize($arrParam));
            //return false;
        }
        $count = count($arrRes['data']['list']);
        $status = 2;
        if ($count > 0 ) {
            $status = 0;
        }

        $arrParams = array(
            'sub_tab_name' => $value,
            'forum_id' => $intForumId,
            'root_tab_id' => $intInfoId,
            'position'   => $key,
            'source' => $sub_source,
            'status' => $status,
            'op_uname' => 'bozhihao',
        );
        var_dump($arrParams);
        
        //$arrRet = Tieba_Service::call('game', 'addSubTab', $arrParams, null, null, 'post', 'php', 'gbk');
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "http://tc.service.tieba.baidu.com/service/game?method=addSubTab");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);  
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $arrParams);
        $stroutput = curl_exec($ch);
        curl_close($ch);
        $arrAddOut = unserialize($stroutput);
        var_dump($arrAddOut);exit;
        usleep(300);
    }
    //exit;
    sleep(1);
}
exit;
//get forum attr
$input = array(
    "forum_id" => $intForumId, //吧id
);
$res = Tieba_Service::call('forum', 'getForumAttr', $input, null, null, 'post', 'php', 'utf-8');
if ($res === null || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
    echo ('getForumAttr. input: ' . serialize($input) . '_output: ' . serialize($res));
}

$strForumAttr = "game_forum_beyond_info";
$arrGameForumType = isset($res['output'][$strForumAttr]) ? $res['output'][$strForumAttr] : array();

$ret = setForumAttr($intForumId, $arrRes);
echo $ret;
/**
 * @param
 * @return
*/
function setForumAttr($intForumId, $arrValue) {

    //开关
    /*$val = self::getForumAttrVal($forumId);

    if ($val === false) {
        return false;
    }*/

    $input = array(
        'forum_id' => $intForumId,
        'attr_name' => "game_forum_beyond_info",
        'attr_value' => $arrValue,
    );

    $ret = Tieba_Service::call('forum', 'setForumAttr', $input);
    var_dump($ret);
    if (empty($ret) || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS) {
        echo (__METHOD__.' failed to set forum attr, ret: '.serialize($ret));
        return false;
    }

    return true;
}

		
