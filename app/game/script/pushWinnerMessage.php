<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file DQuery.php
 * <AUTHOR>
 * @date 2013/04/17 17:52:18
 * @brief 
 *  
 **/

define('ROOT_PATH', dirname(__FILE__).'/../../..');
define('DATA_PATH', ROOT_PATH.'/data/app/game');
define('WINNER_INFO_FNAME', DATA_PATH.'/winner_info_list.');


class PushWinnerMessage {
	const SERVICE_NAME = 'game';
	const METHOD_NAME = 'getWinnerInfo';
	const MESSAGE_PAPER_TYPE = 17;
	const METHOD_CLEAR = 'clearWinnerInfo';
	const TITLE = '%CC%F9%B0%C9%B2%CA%C6%B1%D6%D0%BD%B1';
    const CONTENT ="%D3%C3%BB%A7%A3%AC%C4%FA%BA%C3%A3%BA%B9%A7%CF%B2%C4%FA%D4%DA%CC%F9%B0%C9%B2%CA%C6%B1%D6%D0%BD%B1%A3%AC%B2%E9%D1%AF%BD%B1%BD%F0%C7%EB%B5%E3%BB%F7%A3%BAhttp%3A%2F%2Ftieba.lecai.com%2Fuser%2Forder%2Fwin%2F%A1%A3";
	private static function _dump($arrInput,$file){
		foreach($arrInput as $key=>$value){
			$str = $value."\n";
			file_put_contents($file,$str,FILE_APPEND);
		}
	}
	
	public  static function pushMessage($file,$fileraw){
		$ret = Tieba_Service::call(self::SERVICE_NAME,self::METHOD_NAME,null);
		if($ret === false || !isset($ret['output'])){
			Bingo_Log::warning('get winner info fail ');
			return false;
		}
		self::_dump($ret,$fileraw);
		foreach($ret['output'] as $key=>$value){
			$arrInput = array(
				'req' => array(
					'category_id' => 1,
					'user_id'     => $value,
					'title'       => urldecode(self::TITLE),
					'content'     => urldecode(self::CONTENT),
					'call_from'   => 'game',
				),
			);
            $arrRet = Tieba_Service::call("sysmsg", "sendSysmsg", $arrInput);
			if((false === $arrRet)) {
				Bingo_Log::warning("Tieba_Commit::commit error for qipao.output=[".serialize($arrRet)."]��input=".serialize($arrParams));
				continue;
			}
			$uids[] = $value;
		}	
		usleep(100000);
		self::_dump($uids,$file);
		$arrRet = Tieba_Service::call(self::SERVICE_NAME,self::METHOD_CLEAR,null);
		if($ret === false || !isset($ret['output'])){
			Bingo_Log::warning('clear winner info fail ');
			return false;
		}		
	}
}

function dirInit() {
	$dir = DATA_PATH;
	if (!is_dir($dir) && !file_exists($dir))
		mkdir($dir);
}
function pushWinnerInfo() {
	dirInit();
	$date = date('Ymd', strtotime(PVUV_INFO_DELAY));
	$filename = WINNER_INFO_FNAME.$date;
	$fileraw = WINNER_INFO_FNAME."_raw".$date;
	$strTimer = 'push_winner_info';
	Bingo_Timer::start($strTimer);
	try {
		$file = fopen($filename, 'w');
		$fileraw = fopen($fileraw, 'w');
		if (!$file || !$fileraw) throw new Exception('create file error');
		PushWinnerMessage::pushMessage($file,$fileraw);
	} catch (Exception $e) {
		Bingo_Log::warning(sprintf('count pv uv info error: [%s] [in file %s] [on line %d]', $e->getMessage(), $e->getFile(), $e->getLine()));
	}
	if ($file) fclose($file);
	Bingo_Timer::end($strTimer);
	//Bingo_Log::notice('push winner info message done: '.Bingo_Timer::toString());
}

pushWinnerInfo();






/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
