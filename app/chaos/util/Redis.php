<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014-07-17 12:00:19
 * @version
 */

class Util_Redis {

    private static $_cache;
        
    //cache的pid
    const CACHE_PID = 'official';
    
    //cache配置总开关，方便测试
    const SWITCH_OF_CACHE = true;

    //所有cache的key的前缀，修改前缀即可失效现有所有cache
    const PREFIX_ALL_KEY = 'official_';

    //初始化cache
    public static function initCache() {
    	if (false === self::SWITCH_OF_CACHE) {
            return null;
    	}
        if (self::$_cache) {
            return self::$_cache ;
        }
        
        Bingo_Timer::start('redis_init');
        self::$_cache = new Bingo_Cache_Redis(self::CACHE_PID);
        Bingo_Timer::end('redis_init');

        if (!self::$_cache || !self::$_cache->isEnable()) {
            Bingo_Log::warning("init cache fail.");
            self::$_cache = null;
            return null;
        }
        return self::$_cache;
    }
    //获取单个cache
    public static function getCache($strKey) {
        if (false === self::SWITCH_OF_CACHE) {
            return null;
        }
        //add your code

    }
    //批量获取cache
    public static function mgetCache($arrKey) {
        if (false === self::SWITCH_OF_CACHE) {
            return null;
        }
        //add your code
    }
    //删除cache
    public static function removeCache($strKey) {
        if (false === self::SWITCH_OF_CACHE) {
            return true;
        }
        //add your code
    }
    //设置cache
    public static function addCache($strKey, $mixValue, $intLifeTime) {
        if (false === self::SWITCH_OF_CACHE) {
            return true;
        }
        //add your code
    }
    
    public static function hset($key = null, $field = null, $value = null) {
        if (!self::$_cache) {
            self::initCache();
            if (null == self::$_cache) {
                return null;
            }
        }
        $redisInput = array(
            'key'   => $key,
            'field' => $field,
            'value' => $value,
        );        
        Bingo_Timer::start('redis_hset');
        $redisRes = self::$_cache->HSET($redisInput);
        Bingo_Timer::end('redis_hset');
        return $redisRes;
    }
    
    public static function hget($key = null, $field = null) {
        if (!self::$_cache) {
            self::initCache();
            if (null == self::$_cache) {
                return null;
            }
        }
        $redisInput = array(
            'key'   => $key,
            'field' => $field,
        );        
        Bingo_Timer::start('redis_hget');
        $redisRes = self::$_cache->HGET($redisInput);
        Bingo_Timer::end('redis_hget');
        return $redisRes;
    }
    
    public function hdel($key = null, $field = null) {
        if (!self::$_cache) {
            self::initCache();
            if (null == self::$_cache) {
                return null;
            }
        }
        $redisInput = array(
            'key'   => $key,
            'field' => $field,
        );        
        Bingo_Timer::start('redis_hdel');
        $redisRes = self::$_cache->HDEL($redisInput);
        Bingo_Timer::end('redis_hdel');
        return $redisRes;
    }
    
    public function hincrby($key = null, $field = null, $step = 1) {
        if (!self::$_cache) {
            self::initCache();
            if (null == self::$_cache) {
                return null;
            }
        }
        $redisInput = array(
            'key'   => $key,
            'field' => $field,
            'step'  => $step,
        );        
        Bingo_Timer::start('redis_hincrby');
        $redisRes = self::$_cache->HINCRBY($redisInput);
        Bingo_Timer::end('redis_hincrby');
        return $redisRes;
    }
    
    public function set($key = null, $value = null) {
        if (!self::$_cache) {
            self::initCache();
            if (null == self::$_cache) {
                return null;
            }
        }
        $redisInput = array(
            'key'   => $key,
            'value' => $value,
        );        
        Bingo_Timer::start('redis_set');
        $redisRes = self::$_cache->SET($redisInput);
        Bingo_Timer::end('redis_set');
        return $redisRes;
    }
    
    /**
     * @param  $key
     * @param  $value
     * @param  $secs
     * @return array
     */
    public function setex($key, $value, $secs)
    {
        if (!self::$_cache) {
            self::initCache();
            if (null == self::$_cache) {
                return null;
            }
        }
        $redisInput = array(
            'key'   => $key,
            'value' => $value,
            'seconds' => $secs,
        );        
        Bingo_Timer::start('redis_set');
        $redisRes = self::$_cache->SETEX($redisInput);
        Bingo_Timer::end('redis_set');
        return $redisRes;
    }


    /**
     * @param
     * @return
     */
    public function get($key = null) {
        if (!self::$_cache) {
            self::initCache();
            if (null == self::$_cache) {
                return null;
            }
        }
        $redisInput = array(
            'key'   => $key,
        );        
        Bingo_Timer::start('redis_get');
        $redisRes = self::$_cache->GET($redisInput);
        Bingo_Timer::end('redis_get');
        return $redisRes;
    }

    /**
     * @param 
     *   array(
     *       array(
     *           'key' => '',
     *       ),
     *       array(
     *           'key' => '',
     *       ),
     *   )
     * @return
     */
    public function mget($arrKeys = array()) {
        if (!self::$_cache) {
            self::initCache();
            if (null == self::$_cache) {
                return null;
            }
        }
        $redisInput = array(
            'reqs'   => $arrKeys,
        );        
        Bingo_Timer::start('redis_get');
        $redisRes = self::$_cache->GET($redisInput);
        Bingo_Timer::end('redis_get');
        return $redisRes;
    }

    /**
     * @param  $strkey   
     * @param  $intVal 
     * @return        
     */
    public function incrby($strKey, $intVal)
    {
        if (!self::$_cache) {
            self::initCache();
            if (null == self::$_cache) {
                return null;
            }
        }
        $redisInput = array(
            'key' => $strKey,
            'step' => $intVal,
        );
        Bingo_Timer::start('redis_incrby');
        $redisRes = self::$_cache->INCRBY($redisInput);
        Bingo_Timer::end('redis_incrby');
        return $redisRes;
    }


    /**
    * @desc 拉取有序列表($key)中起始位置(offset)后的(res_num)个数据
    * @param [in] key     : string   : 列表的名称
    * @param [in] offset  : uint32_t : 起始位置
    * @param [in] res_num : uint32_t : 数据的个数
    * @param [in] order   : uint32_t : 1: 升序 2：降序
    * @return
    */
    public static function zrangewithscores($key = null, $offset=0, $res_num=10, $order=1) {
        if (!self::$_cache) {
            self::initCache();
            if (null == self::$_cache) {
                return null;
            }
        }
        if (null === $key || empty($key)) {
            return null;
        }

        $redisInput = array(
            'key'   => $key,
            'start' => $offset,
            'stop'  => $res_num,
        );        
        $intOrder = (int)($order);
        Bingo_Timer::start('redis_zrange');
        if (1 === $intOrder) {
            $redisRes = self::$_cache->ZRANGEWITHSCORES($redisInput);
        } else {
            $redisRes = self::$_cache->ZREVRANGEWITHSCORES($redisInput);
        }
        Bingo_Timer::end('redis_zrange');
        return $redisRes;
    }
    
    /**
    * @desc 获取指定数据的排行 批量接口
    * @param [in] arrInput 
    * @param         key    : string : 键值
    * @param         member : string : 成员名(必须是string，int类型会有问题)
    * @return 
    */
    public static function mzrank($arrInput) {
        if (!self::$_cache) {
            self::initCache();
            if (null == self::$_cache) {
                return null;
            }
        }
        if (empty($arrInput)) {
            return null;
        }

        Bingo_Timer::start('redis_mrank');
        $redisRes = self::$_cache->ZRANK($arrInput);
        Bingo_Timer::end('redis_mrank');

        if (null === $redisRes || Tieba_Errcode::ERR_SUCCESS !== $redisRes['err_no']) {
            $strMsg = sprintf('call  zrank fail with input [%s] output [%s]', serialize($arrInput), serialize($redisRes));
            Bingo_Log::warning($strMsg);
            return null;
        }

        // 吐槽一下 zrank的返回值，当入参数组是一个和多个时，返回的居然不同
        $arrData = array();
        if (count($arrInput['reqs']) === 1)  {
            $intFid = (int)$arrInput['reqs'][0]['member'];
            $strKey = $arrInput['reqs'][0]['key'];
            $arrData[$intFid] = null !== $redisRes['ret'][$strKey] ? (int)$redisRes['ret'][$strKey] + 1 : 2000;
        } else {
            $strKey = $arrInput['reqs'][0]['key'];
            foreach($redisRes['ret'][$strKey] as $intFid => $intOrder) {
                $arrData[$intFid] = null !== $intOrder ? (int)$intOrder + 1 : 2000;
            }
        }

        $arrOut = array(
            'err_no'  => Tieba_Errcode::ERR_SUCCESS,
            'err_msg' => 'success',
            'ret'     => $arrData,
            
        );
        return $arrOut;
    }

    /**
    * @desc 更新一个有序列表
    * @param [in] key     : string  ： shu ru fa huai le 
    * @param [in] member  : string  :  cheng yuan
    * @param [in] score   : double  :  bu hao xian zhi wei int, jiu xuan double lei xing ba zheng zhi (+) zeng jia (-) jian shao 
    * @return
    * @NOTE: bu cun zai dui ying de key.member mao shi ye hui fan hui cheng gong
    *        pin ying bu hao cou he zhe kan  
    */
    public static function zincrby($arrInput) {
        if (!self::$_cache) {
            self::initCache();
            if (null == self::$_cache) {
                return null;
            }
        }
        if (empty($arrInput)) {
            return null;
        }

        Bingo_Timer::start('redis_zincrby');
        $redisRes = self::$_cache->ZINCRBY($arrInput);
        Bingo_Timer::end('redis_zincrby');

        return $redisRes;
    }

    /**
    * @desc 更新一个有序列表
    * @param [in] key    : string : 
    * @param [in] member : string : 
    * @param [in] score  : double : 
    * @return
    * @NOTE: zhe ge he  incrby de qu bie:
    *        zadd shi set mo shi
    *        zincrby shi incr if exists add if non exists
    */
    public static function zadd($arrInput) {
        if (!self::$_cache) {
            self::initCache();
            if (null == self::$_cache) {
                return null;
            }
        }
        if (empty($arrInput)) {
            return null;
        }

        Bingo_Timer::start('redis_zincrby');
        $redisRes = self::$_cache->ZADD($arrInput);
        Bingo_Timer::end('redis_zincrby');

        return $redisRes;
    }

    /**
    * @desc 更新一个有序列表
    * @param [in] key    : string : 
    * @param [in] member : string :
    * @return 
    */
    public static function zrem($arrInput) {
        if (!self::$_cache) {
            self::initCache();
            if (null == self::$_cache) {
                return null;
            }
        }
        if (empty($arrInput)) {
            return null;
        }

        Bingo_Timer::start('redis_zincrby');
        $redisRes = self::$_cache->ZREM($arrInput);
        Bingo_Timer::end('redis_zincrby');

        return $redisRes;
    }
}
?>
