<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 17/5/4
 * Time: 下午6:15
 */

class Libs_Redis {
    protected static $redis = null;
    protected static $tryCount = 2;

    // redis实例
    const REDIS_PID = 'rank';

    /**
     * @return bool
     */
    private static function initRedis() {
        if(self::$redis == null) {
            self::$redis = new Bingo_Cache_Redis(self::REDIS_PID);
            if(self::$redis == false) {
                Bingo_Log::warning("init redis [".self::REDIS_PID."] fail:" . serialize(Bd_RalRpc::get_error()));
                return false;
            }
        }
        return true;
    }

    /**
     * @param $method
     * @param $req
     * @return bool
     */
    public static function call($method, $req) {
        $try = 0;
        if (!self::initRedis()){
            return false;
        }

        while ($try < self::$tryCount) {
            $res = self::$redis->$method($req);
            if(<PERSON>ieba_Errcode::ERR_SUCCESS == $res['err_no']){
                return $res;
            }
            $try++;
        }
        Bingo_Log::warning('call redis:'.$method.' fails. error msg: '. $res['err_msg']);
        return false;
    }
}
