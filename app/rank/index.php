<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR> 
 * @date 2013-10-29 23:35:14
 * @version
 */

 /*
    注意这里涉及到rd与fe的模块名，如果提前已经协商好统一模块名例如都叫 rank，那么这里就不要传第二个参数，默认即可：
        Tieba_Init::init("rank");
    但如果没协商，比如rd的模块名叫 rank，fe的模块名叫 rank_fe，那么这里就应当是（fe这个模块名用于 ROOT_PATH/template/ 下的文件夹名）
        Tieba_Init::init("rank","rank_fe");
    同理，也可以自定义omp模块名，默认同样使用 rank：
        Tieba_Init::init("rank",null,"rank_omp");
 */

DEFINE ('LOG_CACHE',	'CACHE');
define ('MODULE_NAME', 'rank');
 Tieba_Init::init("rank");

 $objFrontController = Bingo_Controller_Front::getInstance(array(
    "actionDir" => MODULE_ACTION_PATH,
    "httpRouter"		 => new Util_Router(),
    "defaultRouter" => "index",
    "notFoundRouter" => "error",
    "beginRouterIndex" => 1
 ));


 Bingo_Timer::start('total');
 Bingo_Page::init(array(
    "baseDir" => MODULE_VIEW_PATH,
    "debug" => false,
    "outputType" => ".",
    "isXssSafe" => true,
    "module" => "rank",
    "useTbView" => true,
    "viewRootpath" => MODULE_VIEW_PATH . "/../",
    //"catchPath" => "../../data/app/rank",
 ));
try{
    $objFrontController->dispatch();
}catch(Util_Exception $e){
    Bingo_Log::warning( "errno=".$e->getCode() ." msg=".$e->getMessage()." data=".serialize($e->getData()) );
    Util_Function::jsonRetUi($e->getCode(), Tieba_Error::getUserMsg($e->getCode()), $e->getData());	

}catch(Exception $e){
    //出错处理，直接转向到错误页面
    Bingo_Log::warning(sprintf('main process failure!HttpRouter=%s,error[%s]file[%s]line[%s]',
    Bingo_Http_Request::getStrHttpRouter(), $e->getMessage(), $e->getFile(), $e->getLine()));
    Bingo_Page::setErrno($e->getCode());
    header('location:http://static.tieba.baidu.com/tb/error.html');
 }

 Bingo_Timer::start('build_page');
 Bingo_Page::buildPage();
 Bingo_Timer::end('build_page');
 Bingo_Timer::end('total');
 
 $strTimeLog = Bingo_Timer::toString();
 Bingo_Log::pushNotice('Timer','['.$strTimeLog.']');
 Bingo_Log::buildNotice(); 

?>
