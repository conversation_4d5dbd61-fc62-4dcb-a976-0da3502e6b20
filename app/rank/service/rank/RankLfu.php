<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file RankLfu.php
 * <AUTHOR>
 * @date 2017/04/19 14:26:36
 * @modified by <PERSON><PERSON><PERSON><PERSON>
 * @brief
 *
 **/
class Service_Rank_RankLfu extends Service_Rank_Rank
{

	const MSET_MAX_ITEM_LIMIT	= 200;
    /**
     * 接删帖、取消删帖命令更新sortedset存储
     * @param $arrInput
     * @return array
    public static function dealNmqIncrMember($arrInput) {

        // 获取参数
        $arrData = Tieba_Service::getArrayParams($arrInput, 'data');
        if (empty($arrData)){
            Bingo_Log::warning(__METHOD__ . " input param invalid. [" . serialize($arrInput) . "]");
            return self::_buildRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 命令号
        $intCommandNo = isset($arrData['command_no']) ? intval($arrData['command_no']) : -1;

        if($intCommandNo < 0 || !isset($arrData['thread_id']) || intval($arrData['thread_id']) <= 0){
            Bingo_Log::warning(__METHOD__ . " input param invalid. [" . serialize($arrInput) . "]");
            return self::_buildRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrKey = $arrData['rank_keys'];
        $intOrder = $arrData['rank_order'];
        $intLimit = $arrData['rank_limit'];

        return self::_buildRet(Tieba_Errcode::ERR_SUCCESS);
    }
     **/

    /**
     * 获得module
     * @param $arrInput
     * @return array
     */
	protected static function _getModule($arrInput){

	}


    /**
     * 插入榜单注册信息表
     * @param $arrInput
     * @return array
     */
    public static function addRankInfo($arrInput)
    {
        if (empty($arrInput['rank_name']) || empty($arrInput['descending'])
            || empty($arrInput['prev']) || empty($arrInput['rank_limit'])
            || empty($arrInput['all_dimension']) || !is_array($arrInput['all_dimension'])) {
            Bingo_Log::warning("input params invalid. input[" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        // 生成dimension值
        $intDimension = Libs_Tools::genDimensionNo($arrInput['all_dimension']);
        $arrInput['dimension'] = $intDimension;
        unset($arrInput['all_dimension']);

        return Service_Db_Db::addRankInfo($arrInput);
    }

    /**
     * 更新榜单注册信息表
     * @param $arrInput
     * @return array
     */
    public static function updateRankInfo($arrInput)
    {
        if (empty($arrInput['rank_name']) || empty($arrInput['descending'])
            || empty($arrInput['prev']) || empty($arrInput['rank_limit'])
            || empty($arrInput['all_dimension']) || !is_array($arrInput['all_dimension'])) {
            Bingo_Log::warning("input params invalid. input[" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        // 生成dimension值
        $intDimension = Libs_Tools::genDimensionNo($arrInput['all_dimension']);
        $arrInput['dimension'] = $intDimension;
        unset($arrInput['all_dimension']);

        return Service_Db_Db::updateRankInfo($arrInput);
    }

    /**
     * 更新榜单状态
     * @param $arrInput
     * @return array
     */
    public static function updateRankInfoStatus($arrInput)
    {
        return Service_Db_Db::updateRankInfoStatus($arrInput);
    }

    /**
     * 批量更新榜单状态
     * @param $arrInput
     * @return array
     */
    public static function batchUpdateRankInfoStatus($arrInput)
    {
        return Service_Db_Db::batchUpdateRankInfoStatus($arrInput);
    }


    /**
     * 通过排名key增加排名分数
     * @param
     *   array(
     *       'prev'  => 'test',  //前缀
     *       'member'    => '7518505',   //排序key
     *       'score' => 1,   //排序value
     *       );
     * @option:
     *       'dimension' => 'week',
     *       //维度，支持单维度添加,传数组的话，可选维度添加，不填则为设置的维度添加
     *       'prev_ext' => 39066,
     *       // 前缀扩展，作为rank_key字段
     * @return array
     *   array(
     *       'errno' => 0,
     *       'errmsg'    => 'sucess',
     *       );
     *
     */
    public static function incrByMember($arrInput)
    {
        if (empty($arrInput['prev']) || empty($arrInput['member']) || empty($arrInput['score'])) {
            Bingo_Log::warning('param is error.input:[' . serialize($arrInput) . '].');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        //参数初始化
        $strMember = $arrInput['member'];
        $intScore = intval($arrInput['score']);

        // 获取排名key和排行榜信息
        $arrRet = self::getRankKeysAndInfo($arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
            return $arrRet;
        }

        $arrKey = $arrRet['data']['rank_keys'];
        $arrRankInfo = $arrRet['data']['rank_info'];

        // 构造请求参数
        foreach ($arrKey as $key) {
            $arrDbReq['input'][$key] = array($strMember);

            $arrRedisReq['reqs'][] = array(
                'key' => $key,
                'member' => $strMember,
            );
        }

        //插入db
        $arrDbReq['step'] = $intScore;
        $arrRet = Service_Db_Db::batchIncrRankScore($arrDbReq);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning('get key error.rank_info:[' . serialize($arrRankInfo) . '].');
            return self::_errRet($arrRet['errno']);
        }

        //判断对应key是否在Redis中存在
        $arrRet = Libs_Redis::call('ZSCORE', $arrRedisReq);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
            Bingo_Log::warning('get key error.rank_info:[' . serialize($arrRedisReq) . '].');
            return self::_errRet($arrRet['errno']);
        }

        $arrNoExistKey = array();
        $arrExistKey = array();
        foreach ($arrRet['ret'] as $key => $score) {
            if (is_null($score)) {
                $arrNoExistKey[] = $key;
            } else {
                $arrExistKey[] = $key;
            }
        }

        // redis中没有key-member，add操作
        if (!empty($arrNoExistKey)) {
            //从Db中获取值
            $arrRet = Service_Db_Db::batchQueryRank($arrDbReq);
            if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
                Bingo_Log::warning('get key error.rank_info:[' . serialize($arrRankInfo) . '].');
                return self::_errRet($arrRet['errno']);
            }


            $arrRedisAddReq = array();
            foreach ($arrNoExistKey as $key) {
                $arrRedisAddReq['reqs'][] = array(
                    'key' => $key,
                    'members' => array(
                        array(
                            'member' => $strMember,
                            'score' => $arrRet['data'][$key][$strMember]['rank_score'],
                        ),
                    ),
                );
            }
            //设置Redis
            Libs_Redis::call('ZADD', $arrRedisAddReq);
        }

        // redis中存在key-member
        if (!empty($arrExistKey)) {
            $arrRedisIncrReq = array();
            foreach ($arrExistKey as $key) {
                $arrRedisIncrReq['reqs'][] = array(
                    'key' => $key,
                    'member' => $strMember,
                    'step' => $intScore,
                );
            }
            // incr操作
            Libs_Redis::call('ZINCRBY', $arrRedisIncrReq);
        }

        // 提交nmq命令
//        $arrCommitInput = array(
//            'rank_keys' => $arrKey,
//            'prev' => $strPrev,
//            'member' => $strMember,
//            'step' => $intScore,
//            'rank_order' => intval($arrRankInfo['descending']),
//            'rank_limit' => intval($arrRankInfo['rank_limit']),
//        );
//        $arrRet = Tieba_Commit::commit('rank', 'incrMember', $arrCommitInput);
//        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['err_no']) {
//            Bingo_Log::warning('commit agree:execAgree failed! input:[' . serialize($arrInput) .']' . 'output:[' . serialize($arrRet) .']');
//            return self::_buildRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
//        }

        // 时间恰好被30整除时，删除redis榜单的多余数据（不是所有请求都执行）
        if (time() % 30) {
            Libs_Tools::remExtraRedisRanking($arrKey, intval($arrRankInfo['descending']), intval($arrRankInfo['rank_limit']));
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * 获取排行榜列表
     * @param $arrInput
     *   array(
     *       'prev'  => 'test',  //前缀
     *       );
     * @option:
     *       'dimension' => 'week',
     *       //维度，支持单维度添加,传数组的话，可选维度添加，不填则为设置的维度添加
     *       'prev_ext' => 39066,
     *       // 前缀扩展，作为rank_key字段
     *       'offset' => 0,
     *       // 偏移量
     *       'limit' => 10,
     *       // 长度
     * @return array
     *   array(
     *       'errno' => 0,
     *       'errmsg' => 'sucess',
     *       'data' => array(
     *               'week' => array(
     *                  array(
     *                       'member' => '7518505',
     *                       'score' => 101,
     *                  ),
     *                  array(
     *                       'member' => '7518505',
     *                       'score' => 101,
     *                  ),
     *              ),
     *         ),
     *       );
     *
     */
    public static function getRankList($arrInput)
    {
        if (empty($arrInput['prev'])) {
            Bingo_Log::warning('param is error.input:[' . serialize($arrInput) . '].');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        // 参数初始化
        $intStart = $intOffset = isset($arrInput['offset']) ? intval($arrInput['offset']) : 0;
        $intLimit = isset($arrInput['limit']) ? intval($arrInput['limit']) : 0;

        // 获取排名key和排行榜信息
        $arrRet = self::getRankKeysAndInfo($arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
            return $arrRet;
        }

        $arrKey = $arrRet['data']['rank_keys'];
        $arrRankInfo = $arrRet['data']['rank_info'];


        $intOrder = intval($arrRankInfo['descending']);
        // 榜单长度
        if ($intLimit == 0) {
            $intLimit = intval($arrRankInfo['rank_limit']);
        }
        $intStop = $intStart + $intLimit - 1;
        foreach ($arrKey as $dimension => $key) {
            $arrKeyInfo[$dimension] = array(
                'key' => $key,
                'start' => $intStart,
                'stop' => $intStop,
            );
        }
        $arrList = Libs_Tools::getRankListByKeys($arrKeyInfo, $intOrder);
        $arrRankLen = Libs_Tools::getRankLenByKeys($arrKeyInfo);

        if (false == $arrList) {
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        $arrData = array();
        foreach ($arrList as $dimension => $list) {
            $arrData[$dimension] = array(
                'list' => $list,
                'count' => intval($arrRankLen[$dimension]) < intval($arrRankInfo['rank_limit']) ? intval($arrRankLen[$dimension]) : intval($arrRankInfo['rank_limit']),
            );
        }


        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
    }


    /**
     * 获取排行榜列表
     * @param $arrInput
     *   array(
     *       'prev'  => 'test',  //前缀
     *       'member' => 124232, // member
     *       );
     * @option:
     *       'dimension' => 'week',
     *       //维度，支持单维度添加,传数组的话，可选维度添加，不填则为设置的维度添加
     *       'prev_ext' => 39066,
     *       // 前缀扩展，如果申请时不为空，这个必需，作为rank_key字段
     * @return array
     *   array(
     *       'errno' => 0,
     *       'errmsg' => 'sucess',
     *       'data' => array(
     *               'week' => array(
     *                  'member' => '7518505',
     *                  'score' => 101,
     *                  'rank' => 1,
     *              ),
     *         ),
     *       );
     *
     */
    public static function getRankItem($arrInput)
    {
        if (empty($arrInput['prev']) || empty($arrInput['member'])) {
            Bingo_Log::warning('param is error.input:[' . serialize($arrInput) . '].');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        // 参数初始化
        $strMember = $arrInput['member'];

        // 获取排名key
        Bingo_Timer::start('rank_key_info');
        $arrRet = self::getRankKeysAndInfo($arrInput);
        Bingo_Timer::end('rank_key_info');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
            return $arrRet;
        }
        $arrKey = $arrRet['data']['rank_keys'];
        $arrRankInfo = $arrRet['data']['rank_info'];

        $intOrder = intval($arrRankInfo['descending']);


        // 获取member榜单排名值
        foreach ($arrKey as $dimension => $key) {
            $arrKeyMember[] = array(
                'key' => $key,
                'member' => $strMember,
            );
        }
        $arrRedisRank = Libs_Tools::mgetRedisRank($arrKeyMember, $intOrder);
//        Bingo_Log::warning(var_export($arrRedisRank, true));

        if (false == $arrRedisRank) {
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        // 奇怪的约定，为啥我们要存业务的score？
        $arrDbInput = array();
        $arrRedisInput = array();
        $boolRedis = true;
        foreach ($arrKey as $key) {
            if (empty($arrRedisRank[$key])) {
                $boolRedis = false;
            }
            $arrRedisInput[] = array(
                'key' => $key,
                'member' => $strMember,
            );
            $arrDbInput['input'][$key] = array($strMember);
        }

//        Bingo_Log::warning(var_export($boolRedis, true));

        // 如果当前key都在redis榜单中，直接请求redis，减少响应时间
        if ($boolRedis) {
            $arrRedisScore = Libs_Tools::mgetRedisScore($arrRedisInput);
            if (false == $arrRedisScore) {
                return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
            }
        } else {
            $arrDbRank = Service_Db_Db::batchQueryRank($arrDbInput);
            if ($arrDbRank['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                return $arrDbRank;
            }
        }
//        Bingo_Log::warning(var_export($arrRedisScore, true));
//        Bingo_Log::warning(var_export($arrDbRank, true));

        $arrOut = array();
        foreach ($arrKey as $dimension => $key) {
            $score = $boolRedis ? $arrRedisScore[$key] : $arrDbRank['data'][$key][$strMember]['rank_score'];
            $arrOut[$dimension] = array(
                'member' => $strMember,
                'score' => $score,
            );
            if (!empty($arrRedisRank[$key])) {
                $arrOut[$dimension]['rank'] = intval($arrRedisRank[$key]);
            }
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOut);
    }


    /**
     * 以某个member为基点获取排行榜列表
     * @param $arrInput
     *   array(
     *       'prev'  => 'test',  //前缀
     *       'member' => 124232, // member
     *       'offset' => -1, // 偏移量,正数取靠后n名，负数取靠前n名
     *       );
     * @option:
     *       'dimension' => 'week',
     *       //维度，支持单维度添加,传数组的话，可选维度添加，不填则为设置的维度添加
     *       'prev_ext' => 39066,
     *       // 前缀扩展，如果申请时不为空，这个必需，作为rank_key字段
     * @return array
     *   array(
     *       'errno' => 0,
     *       'errmsg' => 'sucess',
     *       'data' => array(
     *               'week' => array(
     *                  array(
     *                       'member' => '7518505',
     *                       'score' => 101,
     *                       'rank'  => 1,   //排名
     *                  ),
     *                  array(
     *                       'member' => '7518505',
     *                       'score' => 101,
     *                       'rank'  => 1,   //排名
     *                  ),
     *              ),
     *         ),
     *       );
     *
     */
    public static function getRankListByMember($arrInput)
    {
        if (empty($arrInput['prev']) || empty($arrInput['member']) || empty($arrInput['offset'])) {
            Bingo_Log::warning('param is error.input:[' . serialize($arrInput) . '].');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        // 参数初始化
        $strMember = $arrInput['member'];
        $intOffset = intval($arrInput['offset']);

        // 获取排名key
        $arrRet = self::getRankKeysAndInfo($arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']){
            return $arrRet;
        }
        $arrKey = $arrRet['data']['rank_keys'];
        $arrRankInfo = $arrRet['data']['rank_info'];

        $intOrder = intval($arrRankInfo['descending']);

        foreach ($arrKey as $dimension => $key) {
            $arrKeyMember[] = array(
                'key' => $key,
                'member' => $strMember,
            );
        }
        $arrRedisRet = Libs_Tools::mgetRedisRank($arrKeyMember, $intOrder);
        if (false == $arrRedisRet) {
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }


        // 奇怪的约定，为啥我们要存业务的score？不在排行榜list内的score值，应该不返回
        $arrDbInput = array();
        $arrRedisInput = array();
        foreach ($arrKey as $dimension => $key) {
            if ($arrRedisRet[$key] !== null) {
                if ($intOffset > 0) {
                    $intStart = intval($arrRedisRet[$key]) - 1;
                    $intStop = $intStart + $intOffset;
                } else {
                    $intStop = intval($arrRedisRet[$key]) - 1;
                    $intStart = $intStop + $intOffset;
                    $intStart = $intStart < 0 ? 0 : $intStart;
                }
                $arrRedisInput[$dimension] = array(
                    'key' => $key,
                    'start' => $intStart,
                    'stop' => $intStop,
                );
            } else {
                $arrDbInput['input'][$key] = array($strMember);
            }

        }


        // 查询DB获取score值?
        if (!empty($arrDbInput)) {
            $arrDbRank = Service_Db_Db::batchQueryRank($arrDbInput);
            if ($arrDbRank['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                return $arrDbRank;
            }
        }

        // 获取redis榜单列表
        if (!empty($arrRedisInput)) {
            $arrRedisRank = Libs_Tools::getRankListByKeys($arrRedisInput, $intOrder);
            if (false == $arrRedisRank) {
                return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
            }
        }

        $arrOut = array();
        foreach ($arrKey as $dimension => $key) {
            if (!empty($arrDbRank['data'][$key])) {
                $arrOut[$dimension]['list'] = array(
                    'member' => $strMember,
                    'score' => $arrDbRank['data'][$key][$strMember]['rank_score'],
                );
            } else if (!empty($arrRedisRank[$dimension])) {
                $arrOut[$dimension]['list'] = $arrRedisRank[$dimension];
            }
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOut);
    }

    /**
     * 根据输入prev/prev_ext获取排行榜rank_keys和rank_info
     * @param $arrInput
     * @return array|bool
     */
    public static function getRankKeysAndInfo($arrInput) {

        if (empty($arrInput['prev'])) {
            Bingo_Log::warning('param is error.input:[' . serialize($arrInput) . '].');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        // 查询cache
        $arrCacheRet = Libs_Memcached::getCache($arrInput['prev']);
        if ($arrCacheRet) {
            $arrRankInfo = unserialize($arrCacheRet);
            Bingo_Log::warning('hit cache, cache ret: '. serialize($arrCacheRet));
        } else {
            // 未命中cache
            $arrRet = Service_Db_Db::getRankInfoByPrev($arrInput);
            if ($arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                return $arrRet;
            }
            $arrRankInfo = $arrRet['data'];
            // add cache
            if (!empty($arrRankInfo)) {
                Libs_Memcached::addCache($arrInput['prev'], serialize($arrRankInfo), 3600);
            }
        }


        //判断上线状态
        if (empty($arrRankInfo)) {
            Bingo_Log::warning('This rank does not exist. Rank info:[' . serialize($arrRankInfo) . '].');
            return self::_errRet(Tieba_Errcode::ERR_DB_MISS_DATA);
        }

        //判断上线状态
        if (Libs_Define::RANK_INFO_STATUS_ONLINE != $arrRankInfo['status']) {
            Bingo_Log::warning('This rank is no online.Rank info:[' . serialize($arrRankInfo) . '].');
            return self::_errRet(Tieba_Errcode::ERR_STATUS_ERROR);
        }

        // prev_ext注册不为空，但传入参数prev_ext为空
        if (strlen($arrRankInfo['prev_ext']) > 0 && empty($arrInput['prev_ext'])) {
            Bingo_Log::warning('param is error.input:[' . serialize($arrInput) . '].');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrRankInfo['prev_all'] = $arrRankInfo['prev'];
        //如果有扩展
        if (strlen($arrRankInfo['prev_ext']) > 0) {
            $arrRankInfo['prev_all'] = Libs_Tools::getPrevAll($arrInput['prev'], $arrRankInfo['prev_ext'], $arrInput['prev_ext']);
        }

        //获取key
        if (isset($arrInput['dimension'])) {
            $arrKey = Libs_Tools::getRankKeyByDimensionNames($arrRankInfo, array($arrInput['dimension']));
        } else {
            $arrKey = Libs_Tools::getRankKeyByDimensionNo($arrRankInfo, $arrRankInfo['dimension']);
        }

        if (false === $arrKey) {
            Bingo_Log::warning('get key error.rank_info:[' . serialize($arrRankInfo) . '].');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrOut = array(
            'rank_keys' => $arrKey,
            'rank_info' => $arrRankInfo,
        );

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOut);
    }

    /**
     * @param $errno
     * @param null $data
     * @return array
     */
    private static function _errRet($errno, $data = null)
    {
        $arrRet = array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        );
        return $arrRet;
    }


	/**
		*
		*	msetScoresByMember 批量更新排行榜数据
		* 	@param	array	: reqs
		* 	array	:
		* 		string	:	prev
		* 		string	:	member
		* 		int		: 	scores
		* 	@option
		* 	@return
		*
	 */
	public static function msetScoresByMember($arrInput){
		//this function support only demension all.It will update all rank list date
		if (empty($arrInput['reqs']) || count($arrInput['reqs']) <= 0 || count($arrInput) > self::MSET_MAX_ITEM_LIMIT ){
            Bingo_Log::warning('param is error.input:[' . serialize($arrInput) . '].');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrReqs	= $arrInput['reqs'];
		//call redis
		$arrRet	= self::callRedisForZaddMulti($arrInput);

		if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
			Bingo_Log::warning("call callRedisForZaddMulti failed.input:[".serialize($arrParam)."].output:[".serialize($arrRet)."].");
			return $arrRet;
		}

		//call db
		$arrRet	= Service_Db_Db::callDbForUpdateRankScoreMulti($arrInput);
		if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
			Bingo_Log::warning("call callDbForUpdateRankScoreMulti failed.input:[".serialize($arrParam)."].output:[".serialize($arrRet)."].");
			return $arrRet;
		}
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}

	/**
		*
		*	build multi redis 
		* 	@param	
		* 		array	:	
		* 			array	:
		* 				string	:	key
		* 				string	:	member
		* 				int		:	step
		* 	@option
		* 	@return	array
		*
	 */
	protected static function _buildMultiZaddRedisParam($arrReqs){
		$arrRedisOut	= array();
		foreach($arrReqs as $arrItem){
			if	(empty($arrItem['prev']) || empty($arrItem['member'])){
				Bingo_Log::warning('param is error.input:[' . serialize($arrInput) . '].');
				return false;
			}
			$arrKey	= self::getRankKeysAndInfo($arrItem);
			$strMember	= $arrItem['member'];
			$intScore	= $arrItem['score'];
			foreach($arrKey['data']['rank_keys'] as $dimension => $key){
				$arrRedisOut[]	=array(
					'key'	=> $key,
					'member'	=> $strMember,
					'score'	=> $intScore,
				);
			}
		}
		$arrOut	= array(
			'reqs'	=> $arrRedisOut,
		);

		return $arrOut;
	}

	/**
		*
		*	call redis for muti
		* 	@param
		* 	@option
		* 	@return
		*
	 */
	public static function callRedisForZaddMulti($arrInput){
		if ( false == ($arrRedisReqs = self::_buildMultiZaddRedisParam($arrInput['reqs'])) ){
			Bingo_Log::warning('call redis for zadd param error.input:['.serialize($arrInput).'].');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrRet	= Libs_Redis::call('ZADD', $arrRedisReqs);
		if(false == $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['err_no']){
			Bingo_Log::warning('call redis in callRedisForZaddMulti failed.input:['.serialize($arrRedisReqs).'].output:['.serialize($arrRet).'].');
			return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
		}
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}
}
