<?php
/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file unittest/getRankList.php
 * <AUTHOR>
 * @date 2017/05/4 15:41:05
 * @brief 
 *  
 **/
define('APP_NAME', 'rank' );
define('ROOT_PATH', dirname(__FILE__) . '/../../..');
define('SCRIPT_LOG_PATH', ROOT_PATH . '/log/app/'.APP_NAME);
define ('SCRIPT_NAME', 'rank');
define ('LOG_SCRIPT',  'rank');
define ('LOG_FRAME', 'FRAME');

/**
 * @param $strClassName
 */
function __autoload($strClassName) {
    $strNewClassName = str_replace ( '_', '/', $strClassName . ".php" );
    $arrClass = explode ( '/', $strNewClassName );
    $intPathLen = count ( $arrClass );
    $strLastName = $arrClass [$intPathLen - 1];
    $strTmp = strtolower ( $strNewClassName );
    $intPreLen = strlen ( $strTmp ) - strlen ( $strLastName );
    $strNewClassName = substr ( $strTmp, 0, $intPreLen ) . $strLastName;
    $strClassPath = ROOT_PATH . '/app/' . APP_NAME . '/' . $strNewClassName;
    require_once $strClassPath;
}
spl_autoload_register ( '__autoload' );

Bingo_Log::init(array(
    LOG_FRAME => array(
        'file'  => SCRIPT_LOG_PATH . '/'.APP_NAME.'.log',
        'level' => 0x01|0x02|0x04|0x08,
    ),
    LOG_SCRIPT => array(
        'file'  => SCRIPT_LOG_PATH . '/'.SCRIPT_NAME.'/'.SCRIPT_NAME.'.log',
        'level' => 0x01|0x02|0x04|0x08,
    ),
), LOG_SCRIPT);


$arrParam = array(
    'prev' => 'forum_agree',
    'prev_ext' => 3307,
    'member' => 7518901,
//    'dimension' => 'week',
);
$arrRet = Service_Rank_Rank::getRankItem($arrParam);
var_dump($arrRet);



/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
