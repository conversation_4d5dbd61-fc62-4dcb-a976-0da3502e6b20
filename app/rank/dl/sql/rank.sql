create database forum_rank;

use forum_rank;
drop table if exists `rank_info`;
CREATE TABLE `rank_info` (
  `id` bigint NOT NULL auto_increment COMMENT 'id',
  `rank_name` varchar(64) NOT NULL COMMENT 'rank_name',
  `descending` tinyint unsigned NOT NULL default 1 COMMENT 'asc = 0 or desc = 1',
  `prev` varchar(32) NOT NULL COMMENT 'prefix',
  `prev_ext` varchar(32) NOT NULL default '' COMMENT 'prefix extend',
  `dimension` bigint unsigned NOT NULL default 0 COMMENT 'dimension, have 64bit, 32bit cumulation, 32bit mutex',
  `rank_limit` int(11) unsigned NOT NULL default 0 COMMENT 'The upper limit for ranking',
  `ext` varchar(1024) NOT NULL default '' COMMENT 'extend field',
  `status` tinyint unsigned NOT NULL default 0 COMMENT 'status 0-offline 1-online',
  `expire_time` int(11) unsigned NOT NULL default 0 COMMENT 'expire_time',
  `begin_time` int(11) unsigned NOT NULL default 0 COMMENT 'On line time, when the line is cleared, re line reset, offline delete data asynchronously',
  `create_time` int(11) unsigned NOT NULL default 0 COMMENT 'create_time',
  `update_time` int(11) unsigned NOT NULL default 0 COMMENT 'update_time',
  PRIMARY KEY  (`id`),
  UNIQUE KEY `idx_prev` (`prev`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='ok';

## 排行榜打分排名表
CREATE TABLE `rank` (
  `id` bigint NOT NULL auto_increment COMMENT 'id',
  `rank_key` varchar(64) NOT NULL COMMENT 'rank_key',
  `rank_member` varchar(64) NOT NULL COMMENT 'rank_member',
  `rank_score` bigint(20) NOT NULL default 0 COMMENT 'rank_score',
  `rank_number` bigint(20) unsigned NOT NULL default 0 COMMENT 'rank_number',
  PRIMARY KEY  (`id`),
  UNIQUE KEY `idx_key_member` (`rank_key`, `rank_member`),
  KEY `idx_score` (`rank_score`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='rank';




