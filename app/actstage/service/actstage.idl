struct cmRes{
	uint32_t errno;
	string errmg;
	string data;
};

struct _AwardInfoReq{
	string award_name;		//��Ʒ����
	string award_desc;		//��Ʒ����
	string award_cover;		//��Ʒ����
	uint32_t award_type;		//��Ʒ����
	uint32_t award_post;		//��Ʒ��������
	double award_precision;	//��Ʒ�н���
	uint32_t award_num;		//��Ʒ����
	uint32_t begin_time;		//��Ʒ��ʼ�齱ʱ��
	uint32_t end_time;			//��Ʒֹͣ�齱ʱ��
};


//��rule_idΪ����
struct _AwardRule{
	uint32_t rule_type;		//�ʸ��������
	uint32_t chance_num;		//�ʸ񷢷�����
};

struct _ActivtyReq{
	uint32_t forum_id;			//��id
	string	 forum_name;		//����
	uint32_t create_user_id;	//�����û�id
	string create_user_name;					//�����û�
	uint32_t create_user_ip; //�����û�ip
	uint32_t begin_time;	//���ʼʱ��
	uint32_t end_time;		//�����ʱ��
	string activity_title;	//�����
	string activity_remark; //���ע
	string activity_desc;	//�����
	string activity_icon;	//�ͼ��
	uint32_t activity_type;	//�����
	uint32_t activity_award_num; //�ÿ�շ�������
};

struct ActInfoReq{
	_ActivtyReq	act_info;
	_AwardInfoReq award_info[]; //��Ʒ��Ϣ
	_AwardRule award_rule[];
};

struct ActTotalRes{
	uint32_t errno;
	string errmg;
	ActInfoReq data[];	
};

struct ActInfoRes{
	uint32_t errno;
	string errmg;
	_ActInfoRes data;
};

struct PhotoVoteReq{
    uint32_t user_id;
    string user_name;
    uint32_t user_ip;
    uint32_t activity_id;
    uint32_t thread_id;
    uint32_t post_id;
);

struct _ActInfoRes{
	_ActivtyReq	act_info;			//���Ϣ
	_AwardInfoReq award_info[]; 		//��Ʒ��Ϣ
	_AwardRule award_rule[];			//�ʸ����
	uint32_t act_num;				//��ʸ���
	uint32_t total_num;				//��������
	_ActAwardInfoRes user_award;	//�û���
	_ActAwardInfoRes total_award;	//���top10	
};

struct ActAwardInfoReq{
	uint32_t user_id;		//�齱�û�id
	string user_name;		//�齱�û���
	uint32_t user_ip;   	//�齱�û�ip
	uint32_t activity_id; 	//�齱�id
	uint32_t forum_id;		//�齱��id
	uint64_t thread_id;		//�齱��thread_id
};

struct ActMInfoRes{
	uint32_t errno;
	string errmg;
	_ActMInfoRes data[];
};

struct _ActMInfoRes{
	_ActivtyReq	act_info;			//���Ϣ
	_AwardRule award_rule[];			//�ʸ����
	uint32_t total_num;				//��������	
};

struct ActThreadInfo{         
	uint64_t thread_id;
	uint64_t forum_id;
};

struct ActMReplyInfoRes{          //��ת�����Ϣ
	uint32_t errno;
	string errmg;
	_ActMReplyInfoRes data[];
};

struct _ActMReplyInfoRes{
	_ActivtyReq	act_info;			//���Ϣ
	_AwardInfoReq award_info[]; 		//��Ʒ��Ϣ
};
struct ActRelayInfoRes{
	uint32_t errno;
	string errmg;
	_ActRelayInfoRes data;
};

struct _ActRelayInfoRes{
	_ActivtyReq	act_info;			//���Ϣ
	_AwardInfoReq award_info[]; 		//��Ʒ��Ϣ
	uint32_t total_num;				//��������
	_ActAwardInfoRes total_award;	//���top10	
};

struct ActFrsInfoRes{
	uint32_t errno;
	string errmg;
	_ActFrsInfoRes data[];	
};

struct _ActFrsInfoRes{
	_ActivtyReq	act_info;			//���Ϣ
	_AwardInfoReq award_info[]; 		//��Ʒ��Ϣ
	_AwardRule award_rule[];			//�ʸ����	
};

struct ActAwardInfoRes{
	uint32_t errno;
	string errmg;
	_ActAwardInfoRes data;
};

struct _ActAwardInfoRes{
	uint32_t award_id;		//��Ʒid
	uint32_t user_name;		//�м��û�
	uint32_t create_time;	//�н�ʱ��
	string	award_name;		//��Ʒ����
};

struct AddressInfo{
	string name;
	string phone_num;
	string address;
	string address_num;
}

struct ActAwardAddressInfoReq{
	uint32_t user_id;		//�û�id
	uint32_t activity_id;	//�id
	uint32_t award_id;		//��Ʒid
	AddressInfo	address_info;	//��ַ��Ϣ
};

struct awardPresion{
	string award_name;
	float award_precision;
}

struct activitySimpleInfo{
	_ActivtyReq	act_info;			//���Ϣ
	uint32_t total_num;				//��������	
}

struct activtyAwardUserInfo{
	uint32_t user_id;		//�û�id
	string user_name;		//�û���
	uint32_t activity_id;	//�id
	uint32_t award_id;		//��Ʒid
	string award_name;		//��Ʒ����
	AddressInfo	address_info;	//��ַ��Ϣ	
}

service actstage
{
    /**
     * @brief
     *
     * @param [in/out] res			: out cmRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  actstageϵͳnmq�첽���µĴ��� 
     * <AUTHOR>
     * @date 2013/05/06 14:36:46
     **/
	void cmNmqRequest(out cmRes res);
	
    /**
     * @brief
     *
     * @param [in/out] cmActInfo		: ActInfoReq  �����	 
     *
     * @param [in/out] res			: out cmRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  ���������� 
     * <AUTHOR>
     * @date 2013/05/06 14:36:46
     **/
	void cmInsertActivity(ActInfoReq cmActInfo, out cmRes res);

    /**
     * @brief
     *
     * @param [in] pn		: uint32_t  ���ҳ��
	 * @param [in] rn		: uint32_t	�����¼
     *
     * @param [out] res			: out ActTotalRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  ��������̨��� 
     * <AUTHOR>
     * @date 2013/05/06 14:36:46
     **/
	void bwActivityByAll(uint32_t pn, uint32_t rn, out ActTotalRes res);	
	
    /**
     * @brief
     *
	 * @param [in] activity_id		: uint32_t	�id
	 * @param [in] user_id			: uint32_t	�û�id
	 * @param [in] user_ip			: uint32_t	�û�ip
	 * @param [in] user_name		: string	�û���
	 * @param [in] thread_id		: uint64_t  ����id
	 * @param [in] status           : string    ���״̬ 4��ͨ��,5ͨ��
     *
     * @param [out] res			: out ActTotalRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  ���ͨ�� 
     * <AUTHOR>
     * @date 2013/05/06 14:36:46
     **/
	void cmActivityThrow(uint32_t activity_id, uint32_t user_id, uint32_t user_ip, string user_name, uint64_t thread_id, uint64_t forum_id, out cmRes res);

	/**
     * @brief
     *
	 * @param [in] activity_id		: uint32_t	�id
     *
     * @param [out] res			: out ActTotalRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  ɾ�� 
     * <AUTHOR>
     * @date 2013/05/06 14:36:46
     **/
	void cmActivityDelete(uint32_t activity_id, uint32_t user_id, uint32_t user_ip, string user_name, uint64_t thread_id, uint32_t forum_id, out cmRes res);
	
    /**
     * @brief
     *
     * @param [in] thread_id		: uint64_t  ��ȡ���Ϣ
	 * @param [in] user_id			: uint32_t  �û���¼��Ϣ
	 * @param [in] forum_id			: uint32_t  �û���¼��Ϣ
     *
     * @param [out] res			: out ActInfoRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  pbҳ��������Ϣչʾ 
     * <AUTHOR>
     * @date 2013/05/06 14:36:46
     **/
	void bwGetActivity(uint64_t thread_id, uint32_t user_id, uint32_t forum_id, out ActInfoRes res);	
	
   /**
     * @brief
     *
     * @param [in/out] thread_id		: uint64_t  ��ȡ���Ϣ	 
     * @param [in] forum_id				: uint32_t  ��id	
     * @param [in/out] res			: out ActMInfoRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  frsҳ��������Ϣչʾ 
     * <AUTHOR>
     * @date 2013/05/06 14:36:46
     **/
	void bwMGetActivity(uint64_t thread_id[], uint32_t forum_id, out ActMInfoRes res);	
	
    /**
     * @brief
     *
     * @param [in/out] cmActAwardInfo		: ActAwardInfoReq  �û��齱��Ϣ	 
     *
     * @param [in/out] res			: out ActAwardInfoRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  �û��齱�ύ
     * <AUTHOR>
     * @date 2013/05/06 14:36:46
     **/
	void cmInsertAward(ActAwardInfoReq cmActAwardInfo, out ActAwardInfoRes res);    

    /**
     * @brief
     *
     * @param [in/out] cmActAwardAddressInfo		: ActAwardAddressInfoReq  �û��񽱸��˵�ַ��Ϣ��д	 
     *
     * @param [in/out] res			: out cmRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  �û���ַ��Ϣ��¼
     * <AUTHOR>
     * @date 2013/05/06 14:36:46
     **/
	void cmInsertAddress(ActAwardAddressInfoReq cmActAwardAddressInfo, out cmRes res);
 
    /**
     * @brief
     *
	 * @param [in] activity_id		: uint32_t	�id
	 * @param [in] user_id			: uint32_t	�û�id
	 * @param [in] user_ip			: uint32_t	�û�ip
	 * @param [in] user_name		: string	�û���
	 * @param [in] thread_id		: uint64_t  ����id
	 * @param [in] 
     *
     * @param [out] res			: out ActTotalRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  ���½�Ʒ�н�����Ϣ 
     * <AUTHOR>
     * @date 2013/05/06 14:36:46
     **/
	void cmUpdateAcitivityInfo(uint32_t activity_id, uint32_t user_id, uint32_t user_ip, string user_name, uint64_t thread_id, uint64_t forum_id, awardPresion award_info, out cmRes res);
    
	/**
     * @brief
     *
     * @param [in/out] thread_id		: uint64_t  ����id	 
     * @param [in] thread_info				: uint32_t  ��id	
     * @param [in/out] res			    : out ActMReplyInfoRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  frsת����б� 
     * <AUTHOR>
     * @date 2013/09/23 14:36:46
     **/
	void bwMGetRelayActivity(ActThreadInfo thread_info[], out ActMReplyInfoRes res);

	/**
     * @brief
     *
     * @param [in] thread_id		: uint64_t  ��ȡ���Ϣ
	 * @param [in] user_id			: uint32_t  �û���¼��Ϣ
	 * @param [in] forum_id			: uint32_t  �û���¼��Ϣ
     *
     * @param [out] res			    : out ActRelayInfoRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  ת��pbҳ��Ϣչʾ 
     * <AUTHOR>
     * @date 2013/09/23 14:36:46
     **/
	void bwGetRelayActivity(uint64_t thread_id, uint32_t user_id, uint32_t forum_id, out ActRelayInfoRes res);
	
    /**
     * @brief
     *
     * @param [in] forum_id		: uint32_t  ��id
	 * @param [in] thread_id	: uint32_t	����id
     *
     * @param [out] res			: out activitySimpleInfo  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  �����ʹ��
     * <AUTHOR>
     * @date 2013/05/06 14:36:46
     **/
	void bwGetActivitySimple(uint32_t forum_id, uint32_t thread_id, out activitySimpleInfo res);		

    /**
     * @brief
     *
     * @param [in] forum_id		: uint32_t  ��id
	 * @param [in] thread_id	: uint32_t	����id
     *
     * @param [out] res			: out activtyAwardUserInfo  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  ��ȡ��Ļ���Ϣ
     * <AUTHOR>
     * @date 2013/05/06 14:36:46
     **/
	void bwGetTotalActivityAward(uint32_t forum_id, uint32_t thread_id, out activtyAwardUserInfo res);		

	/**
     * @brief
     *
     * @param [in] pn       : uint32_t  ���ҳ��
     * @param [in] rn       : uint32_t  �����¼
     * @param [in] status   : uint32_t  ���״̬
     * @param [in] forum_id : uint32_t  ��id
     
     * @param [in] activity_id : uint32_t  �id
     *
     * @param [out] res         : out ActTotalRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  ��������̨��������ݰ�id���� (��pn,zn,status,forum_id),����ݻid��������activty_id��
     * <AUTHOR> @date 
     **/
    void bwActivityById(uint32_t pn, uint32_t rn, uint32_t status, uint32_t forum_id, uint32_t activity_id, uint32_t op_type, out ActTotalRes res);
    
    /**
     * @brief   
     * @param [in] activity_id : uint32_t  �id
     *
     * @param [out] res         : out ActTotalRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  ����activity_id��ȡ����������Լ�ʣ�ཱƷ��
     * <AUTHOR> @date 
     **/
    void getActivityAndAwardNum(uint32_t activity_id, out ActTotalRes res);
    
    /**
     * @brief
     *
     * @param [in/out] cmActInfo        : ActInfoReq  �����   
     *
     * @param [in/out] res          : out cmRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  �༭���������Ϣ 
     **/
    void cmUpdateOnlyAct(ActInfoReq cmActInfo, out cmRes res);
    
    /**
     * @brief
     *
     * @param [in/out] cmActInfo        : ActInfoReq  ��ѯ����,status  
     *
     * @param [in/out] res          : out cmRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  ��ȡ�����
     **/
    void getTotalActivityNum(uint32_t status, uint32_t forum_id, out ActTotalRes res);
    
     /**
     * @brief
     *
     * @param [in/out] cmActInfo        : ActInfoReq  ��ѯ����,status  
     *
     * @param [in/out] res          : out cmRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  �����ݿ�����ɾ�������е�����
     **/
    void cmDeleteById(uint32_t forum_id, uint32_t activity_id, out cmDel res);
    
    
    /**
    * @brief
    *
    * @param [in/out] cmActInfo : ActInfoReq  ��ѯ����,activity_id
    * @param [in/out] res          : out cmRes  ���ؽ��
    * @return  void 
    * @retval   
    * @see 
    * @note ͬ����Ʒ��Ϣ
    void insertAllAward(uint32_t activity_id, out cmDel res);
    
   
    /**
     * @brief
     *
     * @param [in/out] PhotoVoteReq        : ActInfoReq  �ύ����  
     *
     * @param [in/out] res          : out cmRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  �ύͶƱ
     **/
    void cmPhotoVote(PhotoVoteReq cmPhotoInfo, out cmRes res);
    
    /**
     * @brief
     *
     * @param [in/out] PhotoVoteReq        : ActInfoReq  �ύ����  
     *
     * @param [in/out] res          : out cmRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  set pb
     **/
    void cmSetSortToPb(uint32_t activity_id, uint32_t thread_id, uint32_t pic_num, out cmRes res);
    
    /**
     * @brief
     *
     * @param [in/out] activity_id       : uint32_t  �ύ����  
     *
     * @param [in/out] res          : out cmRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  ����ʤ������Ա
     **/
    void getAwardPhoto(uint32_t activity_id, out cmRes res);
    
    /**
     * @brief
     *
     * @param [in/out] activity_id       : uint32_t  �ύ����  
     *
     * @param [in/out] res          : out cmRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  �鿴ɹͼ��񽱵���Ա����
     **/
    void getAwards(uint32_t activity_id, out cmRes res);
    
     /**
     * @brief
     *
     * @param [in/out] activity_id       : uint32_t  �id  
                       thread_id         : uint32_t  ����id
     *
     * @param [in/out] res          : out cmRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note  ɹͼ��������pbҳ
     **/
    void cmAnnouncePhotoAward(uint32_t activity_id, out cmRes res);
    
    /**
     * @brief
     *
     * @param [in/out] activity_id       : uint32_t  �id  
                       pn                : uint32_t  ҳ��
                       rn                : uint32_t  ƫ����
     * @param [in/out] res          : out cmRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note ��ȡ�μ�ɹͼ������ͼƬ��Ϣ
     **/
     void getPhotoInfoById(uint32_t activity_id, uint32_t pn, uint32_t rn);  
     
    /**
     * @brief
     *
     * @param [in/out] activity_id       : uint32_t  �id  
                       post_id           : uint32_t  ¥��id
                       vote_num          : uint32_t  �޸ĺ��ͶƱ��
     * @param [in/out] res          : out cmRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note �޸�ͶƱ��                  
     void cmChangeVoteNum(uint32_t activity_id��uint32_t post_id, uint32_t vote_num);                 
    
     /**
     * @brief
     *
     * @param [in/out] activity_id       : uint32_t  �id  
                       post_id           : uint32_t  ¥��id
                       award_level       : uint32_t  �޸ĺ�Ľ���
                       
     * @param [in/out] res          : out cmRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note �޸Ľ���
     void cmChangeAwardLevel(uint32_t activity_id��uint32_t post_id, uint32_t award_level);
     
      /**
     * @brief
     *
     * @param [in/out] lottery_num        : uint32_t �齱���ʾ��������
                       photo_num          : uint32_t ɹͼ���ʾ�������� 
     * @param [in/out] res          : out cmRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note ��ҳ��㳡����
     void getActSquare(uint32_t lottery_num, uint32_t photo_num);
     
    /**
     * @brief
     *
     * @param [in/out] activity_id       : uint32_t  �id  
                       activity_type     : uint32_t  �����
                       activity_weight   : uint32_t  �Ȩ��  1 �ö�  0��ͨ
     * @param [in/out] res          : out cmRes  ���ؽ��
     * @return  void 
     * @retval   
     * @see 
     * @note �����Ȩ�أ��ö���ȡ���ö�    
     void cmChangeActivityWeight(uint32_t activity_id, uint32_t activity_weight, uint32_t activity_type);
};
