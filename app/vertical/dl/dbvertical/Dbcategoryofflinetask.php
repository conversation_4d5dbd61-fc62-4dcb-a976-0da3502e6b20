<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015.09.29
 * @version 
 * @structs & methods(copied from idl.)
*/
define("MODULE", "Vertical_dl");
class Dl_Dbvertical_Dbcategoryofflinetask{
    const DATABASE_NAME               = 'forum_vertical';
    const CATEGORY_OFFLINE_TASK_TABLE = 'category_offline_task';
    const SECOND_HAND_CATEGORY_TABLE  = 'second_hand_category';
    const PER_PAGE_NUM                = 20;
    const CHARSET                     = 'utf8';
    const WAIT_TASK                   = 0;
    const TAKING_TASK                 = 1;
    const OK_TASK                     = 2;
    private static $_db               = null;
    protected static $_conf           = null;
    public static $arrNeedSerial      = array(
        'extra'   => true,
    );

    /**
     * @brief init
     * @return: true if success. false if fail.
    **/
    private static function _init() {
        if (self::$_conf == null) {
            self::$_conf = Bd_Conf::getConf("/app/vertical/service_vertical_vertical");
            if (self::$_conf == false) {
                Bingo_Log::warning("init vertical advert conf fail on db file");
                return false;
            }
        }
        return true; 
    }
    /**
     * @access public
     * @param array
     * @return array
     */
    public function initCataTaskStatus($arrInput) {
        $intNow = time();
        $db = Util_Db::getDB(self::DATABASE_NAME,self::CHARSET);
        $db->startTransaction();
        $intFid = intval($arrInput['forum_id']);
        $selectInput = array(
            'db'    => $db,
            'table' => self::SECOND_HAND_CATEGORY_TABLE,
            'field' => array('*'),
            'cond'  => array(
                'forum_id=' => $intFid,
            ),
        );
        $selectRes = Util_Db::select($selectInput);
        if (false === $selectRes || Tieba_Errcode::ERR_SUCCESS !== $selectRes['errno']) {
            if (false === $db->rollback()) {
                Bingo_Log::warning("rollback fail param:". serialize($selectInput));
            }
            return false;
        } elseif (empty($selectRes['data'])) {
            $arrField = array(
                'forum_id',
                'level',
                'category_name',
                'weight',
                'create_time',
                'create_user_name',
            );
            $arrAddData = array(
                array($intFid,1,'出售',1,$intNow,$arrInput['create_user']),
                array($intFid,1,'求购',2,$intNow,$arrInput['create_user']),
                array($intFid,2,'母婴用品',1,$intNow,$arrInput['create_user']),
                array($intFid,2,'交通出行',2,$intNow,$arrInput['create_user']),
                array($intFid,2,'服饰鞋帽',3,$intNow,$arrInput['create_user']),
                array($intFid,2,'书籍音像',4,$intNow,$arrInput['create_user']),
                array($intFid,2,'家用电器',5,$intNow,$arrInput['create_user']),
                array($intFid,2,'文玩收藏',6,$intNow,$arrInput['create_user']),
                array($intFid,2,'箱包皮具',7,$intNow,$arrInput['create_user']),
                array($intFid,2,'文体办公',8,$intNow,$arrInput['create_user']),
                array($intFid,2,'首饰手表',9,$intNow,$arrInput['create_user']),
                array($intFid,2,'卡券票务',10,$intNow,$arrInput['create_user']),
                array($intFid,2,'电脑数码',11,$intNow,$arrInput['create_user']),
                array($intFid,2,'家居建材',12,$intNow,$arrInput['create_user']),
                array($intFid,2,'日用百货',13,$intNow,$arrInput['create_user']),
                array($intFid,2,'美容健身',14,$intNow,$arrInput['create_user']),
                array($intFid,2,'其他宝贝',15,$intNow,$arrInput['create_user']),
            );
            $insertInput = array(
                'db'    => $db,
                'table' => self::SECOND_HAND_CATEGORY_TABLE,
                'field' => $arrField,
                'data'  => $arrAddData,
            );
            $insertRes = Util_Db::replace($insertInput);
            if (false === $insertRes || Tieba_Errcode::ERR_SUCCESS !== $insertRes['errno']) {
                Bingo_Log::fatal("add second_hand_category fail param:". serialize($insertInput));
                if (false === $db->rollback()) {
                    Bingo_Log::warning("rollback apply list fail param:". serialize($arrInput));
                }
                return false;
            }
        }
        $selectInput = array(
            'db'    => $db,
            'table' => self::CATEGORY_OFFLINE_TASK_TABLE,
            'field' => array('*'),
            'cond'  => array(
                'forum_id=' => $intFid,
            ),
        );
        $selectRes = Util_Db::select($selectInput);
        if (false === $selectRes || Tieba_Errcode::ERR_SUCCESS !== $selectRes['errno']) {
            if (false === $db->rollback()) {
                Bingo_Log::warning("rollback fail param:". serialize($selectInput));
            }
            return false;
        } elseif (empty($selectRes['data'])) {
            $arrField = array(
                'forum_id',
                'status',
                'create_time',
                'open_cate',
                'open_deal',
            );
            $arrAddData = array(
                $intFid,
                0,
                $intNow,
                $arrInput['open_cate'],
                $arrInput['open_deal'],
            );
            $insertInput = array(
                'db'    => $db,
                'table' => self::CATEGORY_OFFLINE_TASK_TABLE,
                'field' => $arrField,
                'data'  => array($arrAddData),
            );
            $insertRes = Util_Db::replace($insertInput);
            if (false === $insertRes || Tieba_Errcode::ERR_SUCCESS !== $insertRes['errno']) {
                Bingo_Log::fatal("add second_hand_category fail param:". serialize($insertInput));
                if (false === $db->rollback()) {
                    Bingo_Log::warning("rollback apply list fail param:". serialize($arrInput));
                }
                return false;
            }
        } else {
            $arrField = array();
            if ($arrInput['open_cate']>0) {
                $arrField = array(
                    'open_cate=' => 1,
                );
            }
            if ($arrInput['open_deal']>0) {
                $arrField = array(
                    'open_deal=' => 1,
                );
            }
            if (!empty($arrField)) {
                $updateInput = array(
                    'db'    => $db,
                    'table' => self::CATEGORY_OFFLINE_TASK_TABLE,
                    'field' => $arrField,
                    'cond'  => array(
                        'forum_id=' => $intFid,
                    ),
                );
                $updateRes = Util_Db::update($updateInput);
                if (false === $updateRes || Tieba_Errcode::ERR_SUCCESS !== $updateRes['errno']) {
                    Bingo_Log::fatal("update fail param:". serialize($updateInput));
                    if (false === $db->rollback()) {
                        Bingo_Log::warning("rollback apply list fail param:". serialize($updateInput));
                    }
                    return false;
                }
            }
        }

        if (false === $db->commit()) {
            Bingo_Log::fatal("init apply infor fail param:". serialize($dbInput));
            if (false === $db->rollback()) {
                Bingo_Log::warning("rollback fail param:". serialize($dbInput));
            }
            return false;
        }
        return Util_Function::errRet(Tieba_Errcode::ERR_SUCCESS, $selectRes['data']);
    }
    /**
     * @access public
     * @param array
     * @return array
     */
    public function selectApplyListByCon($arrInput = array()) {
        $needFieldArr = array('cond');
        $checkRes = Util_Function::checkArrMustField($arrInput, $needFieldArr);
        if (false === $checkRes) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!isset($arrInput['field'])) {
            $arrInput['field'] = array('*');
        }
        $selectInput = array(
            'table' => self::CATEGORY_OFFLINE_TASK_TABLE,
            'field' => $arrInput['field'],
            'cond'  => $arrInput['cond'],
        );
        if (isset($arrInput['db'])) {
            $selectInput['db'] = $arrInput['db'];
        }
        if (isset($arrInput['append'])) {
            $selectInput['append'] = $arrInput['append'];
        }
        $selectRes = Util_Db::select($selectInput);
        return $selectRes;
    }
    /**
     * @access public
     * @param array
     * @return array
     */
    public function getOpenCateList($arrInput = array()) {
        $db = Util_Db::getDB(self::DATABASE_NAME, self::CHARSET);
        $strApp = ' order by create_time asc ';
        if (isset($arrInput['rn'])) {
            $strApp .= ' limit '.intval($arrInput['rn']);
        }
        $arrSelectInput = array(
            'db' => $db,
            'cond' => array(
                'status=' => self::WAIT_TASK,
            ),
            'append' => $strApp,
        );
        $arrRes = self::selectApplyListByCon($arrSelectInput);
        return $arrRes;
    }
    /**
     * @access public
     * @param array
     * @return array
     */
    public function getInHandTask($arrInput = array()) {
        $db = Util_Db::getDB(self::DATABASE_NAME, self::CHARSET);
        $strApp = ' order by create_time asc ';
        if (isset($arrInput['rn'])) {
            $strApp .= ' limit '.intval($arrInput['rn']);
        }
        $arrSelectInput = array(
            'db' => $db,
            'cond' => array(
                'status=' => self::TAKING_TASK,
            ),
            'append' => $strApp,
        );
        $arrRes = self::selectApplyListByCon($arrSelectInput);
        return $arrRes;
    }
    /**
     * @access public
     * @param array
     * @return array
     */
    public function updateTaskStatusByCon($arrInput = array()) {
        $needFieldArr = array('field', 'cond');
        $checkRes = Util_Function::checkArrMustField($arrInput, $needFieldArr);
        if (false === $checkRes) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $field = $cond = array();
        foreach ($arrInput['field'] as $fkey => $fval) {
            if (isset(self::$arrNeedSerial[$fkey])) {
                $field[$fkey."="] = Bingo_String::array2json($fval);
            } else {
                $field[$fkey."="] = $fval;
            }
        }
        foreach ($arrInput['cond'] as $ckey => $cval) {
            if (is_array($cval)) {
                $cond[$ckey] = $cval;
            } else {
                $cond[$ckey."="] = $cval;
            }
        }
        $table = self::CATEGORY_OFFLINE_TASK_TABLE;
        $updateInput = array(
            'table' => $table,
            'field' => $field,
            'cond'  => $cond,
        );
        if (isset($arrInput['db'])) {
            $updateInput['db'] = $arrInput['db'];
        }
        $updateRes = Util_Db::update($updateInput);
        return $updateRes;
    }
}

