drop table `digital_product`;
CREATE TABLE IF NOT EXISTS `digital_product` (
    `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'primary',
    `product_name` VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'varchar',
    `product_title` VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'varchar',
    `product_type` TINYINT(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'tinyint',
    `status` TINYINT(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'status',
    `create_uid` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'uid',
    `create_time` INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'time',
    `op_uname` VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'name',
    `op_uid` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'uid',
    `op_time` INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'time',
    `start_time` INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'time',
    `end_time` INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'time',
    `info` VARCHAR(10000) NOT NULL DEFAULT '' COMMENT 'serialize info',
    PRIMARY KEY `id` (`id`),
    KEY `normal_query` (`status`, `op_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT 'digital product';

DROP `digital_product_thread`;
CREATE TABLE IF NOT EXISTS `digital_product_thread` (
    `id` BIGINT(20) UNSIGNED NOT NULL COMMENT 'primary',
    `thread_id` BIGINT(20) UNSIGNED NOT NULL COMMENT 'thread_id',
    PRIMARY KEY `id` (`id`),
    UNIQUE KEY `qid` (`thread_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT 'digital thread';

DROP `digital_product_forum`;
CREATE TABLE IF NOT EXISTS `digital_product_forum` (
    `id` BIGINT(20) UNSIGNED NOT NULL COMMENT 'primary',
    `forum_id` BIGINT(20) UNSIGNED NOT NULL COMMENT 'forum_id',
    PRIMARY KEY `id` (`id`),
    UNIQUE KEY `qid` (`forum_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT 'digital forum';

DROP table digital_comment;
CREATE TABLE IF NOT EXISTS `digital_comment` (
    `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'primary',
    `related_thread_id` BIGINT(20) UNSIGNED NOT NULL COMMENT 'id',
    `comment_title` VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'varchar',
    `thread_id` BIGINT(20) UNSIGNED NOT NULL COMMENT 'id',
    `forum_id` BIGINT(20) UNSIGNED NOT NULL COMMENT 'id',
    `post_id` BIGINT(20) UNSIGNED NOT NULL COMMENT 'id',
    `source_type` TINYINT(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'tinyint',
    `source_id` BIGINT(20) UNSIGNED NOT NULL COMMENT 'id',
    `status` TINYINT(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'status',
    `op_time` INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'time',
    `op_uid` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'uid',
    `op_uname` VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'name',
    `info` VARCHAR(10000) NOT NULL DEFAULT '' COMMENT 'serialize info',
    PRIMARY KEY `id` (`id`),
    KEY `normal_query` (`related_thread_id`, `op_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT 'digital comment';
