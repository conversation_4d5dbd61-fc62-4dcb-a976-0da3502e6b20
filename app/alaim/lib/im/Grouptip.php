<?php
/***************************************************************************
 *
 * Copyright (c) 2010 Baidu.com, Inc. All Rights Reserved
 * $Id$
 *
 **************************************************************************/

class Lib_Im_Grouptip{
    
    const CREATE_NORMAL_GROUP_GRADE = 3;    //创建吧内普通群的用户最低等级
    
    public static function createGroup($arrPerm){
        $arrGroupPerm = array();
        $bolCreateNormalPermFlag = false;
            
        //判断建普通群的等级权限
        if($arrPerm['can_create_normal_group_perm'] !== 1){
            $arrGroupPerm['canCreateNormal'] = 0;
            $arrGroupPerm['canCreateNormalNum'] = 0;
            $arrGroupPerm['createNormalTip'] = '本吧'.self::CREATE_NORMAL_GROUP_GRADE.'级以上成员才能在吧内建群哦!';
            $bolCreateNormalPermFlag = true;
        }
        
        /*
        //判断私有群满额
        if($arrPerm['can_create_personal_group_num'] <= 0){
            $arrGroupPerm['canCreatePersonal'] = 0;
            $arrGroupPerm['createPersonalTip'] = '你已创建'.$arrPerm['already_create_personal_group_num'].
                                                        '个群，达到上限';
        }else{
            $arrGroupPerm['canCreatePersonal'] = 1;
            $arrGroupPerm['createPersonalTip'] = '';
        }
        */
        //当前策略是用户的个人群与吧内普通群的名额共享，这两类群对用户来说是一类，所以要统一处理
        $intCanCreateNotOfficailGroupNum = $arrPerm['can_create_personal_group_num'];    //此时可创建个人群与普通群个数应该相同
        $intAlreadyCreateNotOfficailGroupNum = $arrPerm['already_create_personal_group_num']+$arrPerm['already_create_normal_group_num'];
        if($intCanCreateNotOfficailGroupNum <= 0){
            $arrGroupPerm['canCreatePersonal'] = 0;
            $arrGroupPerm['canCreatePersonalNum'] = 0;
            $arrGroupPerm['createPersonalTip'] = '你已创建'.$intAlreadyCreateNotOfficailGroupNum.
                                                        '个群，达到上限';
        }else{
            $arrGroupPerm['canCreatePersonal'] = 1;
            $arrGroupPerm['canCreatePersonalNum'] = $intCanCreateNotOfficailGroupNum;
            $arrGroupPerm['createPersonalTip'] = '';
        }
        
        //判断普通用户的建群权限
        if($arrPerm['is_forum_manager'] !== 1){
            $arrGroupPerm['isForumManager'] = 0;
            //普通群满额
            if ($bolCreateNormalPermFlag !== true){
                if($intCanCreateNotOfficailGroupNum <= 0){
                    $arrGroupPerm['canCreateNormal'] = 0;
                    $arrGroupPerm['canCreateNormalNum'] = 0;
                    $arrGroupPerm['createNormalTip'] = '你已创建'.$intAlreadyCreateNotOfficailGroupNum.
                                                        '个群，达到上限';
                }else{
                    $arrGroupPerm['canCreateNormal'] = 1;
                    $arrGroupPerm['canCreateNormalNum'] = $intCanCreateNotOfficailGroupNum;
                    $arrGroupPerm['createNormalTip'] = '';
                }
            }
        }
        //判断吧主的建群权限
        else{
            $arrGroupPerm['isForumManager'] = 1;
            if ($bolCreateNormalPermFlag !== true){
            //普通群提示
                if ($intAlreadyCreateNotOfficailGroupNum == 0){
                    $arrGroupPerm['canCreateNormal'] = 1;
                    $arrGroupPerm['canCreateNormalNum'] = $intCanCreateNotOfficailGroupNum;
                    $arrGroupPerm['createNormalTip'] = '每个用户可创建'.$intCanCreateNotOfficailGroupNum.
                                                            '个普通群，成员上限30人';
                }
                else if($intCanCreateNotOfficailGroupNum > 0){
                    $arrGroupPerm['canCreateNormal'] = 1;
                    $arrGroupPerm['canCreateNormalNum'] = $intCanCreateNotOfficailGroupNum;
                    $arrGroupPerm['createNormalTip'] = '你已创建'.$intAlreadyCreateNotOfficailGroupNum.
                                                            '个普通群，还可以创建'.
                                                            $intCanCreateNotOfficailGroupNum.'个';
                }
                else{
                    $arrGroupPerm['canCreateNormal'] = 0;
                    $arrGroupPerm['canCreateNormalNum'] = 0;
                    $arrGroupPerm['createNormalTip'] = '你已创建'.$intAlreadyCreateNotOfficailGroupNum.
                                                        '个普通群，达到上限';
                }
            }
            //官方群提示
            if ($arrPerm['forum_already_create_official_group_num'] == 0){
                $arrGroupPerm['canCreateOfficial'] = 1;
                $arrGroupPerm['canCreateOfficialNum'] = $arrPerm['forum_can_create_official_group_num'];
                $arrGroupPerm['createOfficialTip'] = '每个吧可创建'.$arrPerm['forum_can_create_official_group_num'].
                                                        '个官方群，成员上限500人';
            }
            else if($arrPerm['forum_can_create_official_group_num'] > 0){
                $arrGroupPerm['canCreateOfficial'] = 1;
                $arrGroupPerm['canCreateOfficialNum'] = $arrPerm['forum_can_create_official_group_num'];
                $arrGroupPerm['createOfficialTip'] = '本吧已有'.$arrPerm['forum_already_create_official_group_num'].
                                                        '官方群，还可以创建'.
                                                        $arrPerm['forum_can_create_official_group_num'].'个';
            }
            else{
                $arrGroupPerm['canCreateOfficial'] = 0;
                $arrGroupPerm['canCreateOfficialNum'] = 0;
                $arrGroupPerm['createOfficialTip'] = '本吧已有'.$arrPerm['forum_already_create_official_group_num'].
                                                        '个官方群，达到上限';
            }
        }


        return $arrGroupPerm;
    }
}
