<?php
/**
 * DuApp.php 手百IM
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 18/7/17 下午2:15
 */

define("MODULE", "util_service");
define("MODULE_NAME", "util_open");

class Service_Util_Open_DuApp
{
    /**
     * 手百定义的贴吧用的APP_ID
     */
    const APP_ID = 405384;

    const APP_KEY = '25FkEno6LKc68RUgG1ZxjnPl';

    const APP_SECRET_KEY = 'aCfvehXUEczw4lScKoe3BQ5WmIP0Fgpq';

    const API_HOST = 'http://cp01-ocean-749.epc.baidu.com:8882';

    const API_HOST_BLOCK = 'http://cp01-caozhiyuan.epc.baidu.com:8090';

    const API_HOST_UNBAN = 'http://cp01-zhouwei123.epc.baidu.com:8084';

    /**
     * RAL服务调用对应host方法名
     * @var array
     */
    private static $_arrRalFuncMaps = array(
        self::API_HOST       => array('Alalib_Util_ShouBaiSdk', 'imCall'),
        self::API_HOST_UNBAN => array('Alalib_Util_ShouBaiSdk', 'call'),
        self::API_HOST_BLOCK => array('Alalib_Util_ShouBaiSdk', 'uegCall'),
    );

    /**
     * 查询直播用户黑名单接口
     */
    const API_IS_BLOCK = '/content/1.0/isblock';

    /**
     * 接口地址：用户IM消息
     */
    const API_USER_SEND_MSG = '/api/1.0/users.sendMsgEx';

    /**
     * 接口地址：IM业务消息
     */
    const API_ROOM_SEND_SERVICE = '/api/1.0/rooms.sendServiceInfoEx';

    /**
     * 接口地址：IM系统消息
     */
    const API_ROOM_SEND_SYS_MSG = '/api/1.0/rooms.sendSysMsgEx';

    /**
     * 接口地址：IM点赞消息
     */
    const API_USER_FEEDBACK = '/api/1.0/users.feedbackEx';

    /**
     * 接口地址：解封用户
     */
    const API_UNBAN_USER = '/liveshow/unban_user';

    /**
     * 是否开启nmq异步调用
     * 线下测试默认关闭，直接调用api
     * 上线后修改为true
     */
    const NMQ_SWITCH = true;

    /**
     * 是否使用ORP的fetch_url服务
     * 线下测试默认关闭，使用curl服务代替
     */
    const ORP_FETCH_URL_SWITCH = false;

    /**
     * 使用ral服务调用
     */
    const RAL_SWITCH = true;

    /**
     * mcast_type 0 主播室
     */
    const MCAST_TYPE_ANCHOR = 0;

    /**
     * mcast_type 1 聊天室
     */
    const MCAST_TYPE_USER = 1;

    /**
     * 手百IM业务消息类型
     */
    const IM_TYPE_OPERATION = 107;

    /**
     * 手百IM系统消息类型
     */
    const IM_TYPE_SYS = 108;

    /**
     * 手百系统消息Service类型
     */
    const SERVICE_TYPE_SYS = 10012;

    /**
     * 手百业务消息Service类型
     */
    const SERVICE_TYPE_OPERATION = 10013;

    /**
     * 手百系统消息Service类型
     */
    const SERVICE_TYPE_GIFT = 10024;

    /**
     * 手百分享消息Service类型
     */
    const SERVICE_TYPE_SHARE = 10028;

    /**
     * 手百礼物消息Service类型
     */
    const SERVICE_TYPE_ZAN = 10125;

    /**
     * 贴吧msg_type与手百service_type的映射
     * @var array
     */
    public static $arrMsgTypeToServiceTypeMap = array(
        Lib_Im_Command::MSG_TYPE_LIVE_SYSTEM => self::SERVICE_TYPE_SYS,
        Lib_Im_Command::MSG_TYPE_LIVE_NOTICE => self::SERVICE_TYPE_OPERATION,
        Lib_Im_Command::MSG_TYPE_ALERT       => self::SERVICE_TYPE_OPERATION,
        Lib_Im_Command::MSG_TYPE_GIFT        => self::SERVICE_TYPE_GIFT,
        Lib_Im_Command::MSG_TYPE_LIVE_SHARE  => self::SERVICE_TYPE_SHARE,
        Lib_Im_Command::NEW_MSG_TYPE_ZAN     => self::SERVICE_TYPE_ZAN,
    );

    /**
     * 消息优先级，默认为0，0低 1中 2高
     */
    const PRIORITY_HIGH = 2;

    const PRIORITY_MIDDLE = 1;

    const PRIORITY_LOW = 0;

    /**
     * 调用方标识，0默认手百直播业务；1贴吧；
     */
    const CALLER_FLAG_TIEBA = 1;

    /**
     * 该直播间是否同步到手百
     * @var array
     */
    private static $_arrRoomSyncedToDuAppInfo = array();

    /**
     * _sendMsgEx
     * @param $arrInput
     * @return bool
     */
    private static function _sendMsgEx($arrInput) {
        $intUserId = $arrInput['user_id'];
        $intRoomId = $arrInput['room_id'];
        $intMsgKey = $arrInput['msg_id'];

        $intNow = Bingo_Timer::getNowtime();

        $strContent = $arrInput['content'];

        $strUserName = $arrInput['user_info']['user_name'];

        $strPortrait = $arrInput['user_info']['bd_portrait'];
        if (empty($strPortrait)){
            $strPortrait = 'https://himg.bdimg.com/sys/portrait/item/'.$arrInput['user_info']['portrait']. '.jpg';
        }

        $arrData = $arrInput;

        // 手百格式转换
        $strContent = Service_Util_Open_DuIm::transFormat($intUserId, $strUserName, $strPortrait, $intRoomId, Service_Util_Open_DuIm::MSG_TYPE_PLAIN, $strContent, $arrData);

        $arrParams = array(
            'appid'       => self::APP_ID,
            'uid'         => $intUserId,
            'room_id'     => $intRoomId,
            'mcast_type'  => self::MCAST_TYPE_USER,
            'content'     => $strContent,
            'msg_key'     => $intMsgKey,
            'caller_flag' => 1,              // 调用方标识，0默认手百直播业务；1贴吧；
            'timestamp'   => $intNow,
            'apikey'      => self::APP_KEY,
        );

        $arrParams['sign'] = self::_genSign($arrParams);

        $strApiUrl = self::_getApiUrl(self::API_USER_SEND_MSG);

        return self::_callApi($strApiUrl, $arrParams);
    }

    /**
     * syncMsg
     * @param $arrInput
     * @return bool
     */
    public static function syncMsg($arrInput) {
        // 如果是手百App传递过来的消息，则不再同步给手百
        //if (isset($arrInput['subapp_type']) && Alalib_Conf_Sdk::isSdk($arrInput['subapp_type']) && 1 == intval($arrInput['is_shoubai_im_sync_tieba'])) {
        if ( 1 == intval($arrInput['is_shoubai_im_sync_tieba'])) {
            Bingo_Log::warning("need not sync to shoubai im...Service_Util_Open_DuApp::syncMsg return directly");
            return true;
        }

        if (empty($arrInput['room_id'])) {
            Bingo_Log::warning('No room id. input:'.serialize($arrInput));
            return false;
        }

        $intMsgType = $arrInput['msg_type'];
        $intOpenType  = isset($arrInput['open_type']) ? intval($arrInput['open_type']) : 0;
        switch ($intMsgType) {
            // 普通消息
            case Lib_Im_Command::MSG_TYPE_MESSAGE:
                return self::_sendMsgEx($arrInput);
            // 系统消息
            case Lib_Im_Command::MSG_TYPE_LIVE_SYSTEM:
                return self::_sendSysMsgEx($arrInput);
            // 18/8/18 gaojingjing03 分享到贴吧的im消息，PM确认不用同步给手百
            case Lib_Im_Command::MSG_TYPE_LIVE_SHARE:
                return false;
            // 业务消息、礼物消息
            case Lib_Im_Command::MSG_TYPE_LIVE_NOTICE:
                $arrContent = json_decode($arrInput['content'],true);
                $content_type = $arrContent['content_type'];
                if (intval($arrInput['anchor_id']) > 0) {
                    $arrRet = Tieba_Service::call('ala', 'getLiveCustom', array('anchor_id'=>intval($arrInput['anchor_id'])), null, null, 'post', null, 'utf-8');
                    if(!empty($arrRet['data'])) {
                        $bolIsHot = intval($arrRet['data']['live_info']['ishot']);
                        if ($bolIsHot === 1 && $content_type != "multi_session") {
                            return false;
                        }
                    }
                }
                
                // content.content_type = share的分享消息，不用同步给手百

                // zan_rmb 沸点直播间屏蔽
                if ('share' == $arrContent['content_type']) {
                    return false;
                }
                //暂定系统消息不入库，否则影响回放的字幕，open_type=100为百家号开播 不能保存进场消息
                if ($intOpenType == 100 && 'enter_live' == $arrContent['content_type']) {
                    //disable_persist 消息是否入库字段，1为否
                    $arrInput['disable_persist'] = 1;
                }
                //暂定系统消息不入库，否则影响回放的字幕
                if ('zan_rmb' == $arrContent['content_type'] || 'follow_anchor' == $arrContent['content_type'] || 'multi_session' == $arrContent['content_type'] || 'introduce_goods' == $arrContent['content_type']) {
                    //disable_persist 消息是否入库字段，1为否
                    $arrInput['disable_persist'] = 1;
                    /*$arrLimitUser = array(
                    );
                    if (in_array($arrInput['anchor_id'], $arrLimitUser)) {
                        $strKey = 'reids_ala_daihuo_zhibo_dianlian_qps_' . $arrInput['anchor_id'];
                        $arrRedisInput = array(
                            'key'   => $strKey,
                            'value' => json_encode($arrInput),
                        );
                        $redis  = Lib_Util_Redis::_getRedis('ala');
                        $key = $strKey.'_forincr';
                        $num = $redis->incr(array('key' => $key));
                        if ($num['ret'][$key] > 1000) {
                            return false;
                        }
                        $arrRedisInput = array(
                            'key'   => $strKey,
                            'value' => json_encode($arrInput),
                        );
                        $arrRet = $redis->LPUSH($arrRedisInput);
                        if($arrRet === false || $arrRet['err_no'] !== 0){
                            Bingo_Log::warning("commitPushNotifyGroupMsg call redis error.[".serialize($arrRet)."]");
                        }
                        return false;
                    }*/
                }
                return self::_sendServiceInfoEx($arrInput);
            case Lib_Im_Command::MSG_TYPE_ALERT:
                return self::_sendServiceInfoEx($arrInput);
            case Lib_Im_Command::MSG_TYPE_GIFT:
                // 修复同步到秀场的媒体直播间，在秀场送礼时，礼物im在媒体展示用户名为null问题。
                if (isset($arrInput['user_info'])) {
                    $arrInput['user_name'] = $arrInput['user_info']['user_name'];
                    $arrInput['portrait'] = $arrInput['user_info']['bd_portrait'];
                }
                return self::_sendServiceInfoEx($arrInput);
            // 点赞消息
            case Lib_Im_Command::NEW_MSG_TYPE_ZAN:
                return self::_feedbackEx($arrInput);
            default:
                Bingo_Log::warning('Not support msg_type. input:'.serialize($arrInput));
                return false;
        }
    }

    /**
     * sendServiceInfoEx
     * @param $arrInput
     * @return bool
     */
    public static function sendServiceInfoEx($arrInput) {
        $intRoomId  = $arrInput['room_id'];
        $intMsgType = $arrInput['msg_type'];
        $intMsgExpires = intval($arrInput['msg_expires']);
        $intPersist = intval($arrInput['disable_persist']);     //消息是否入库字段，1为否

        $intServiceType = isset(self::$arrMsgTypeToServiceTypeMap[$intMsgType]) ? self::$arrMsgTypeToServiceTypeMap[$intMsgType] : 0;

        if (!$intServiceType) {
            Bingo_Log::warning('No mapping service type. input:'.serialize($arrInput));
            return array('errno' => 21009 , 'errmsg' => 'param error');
        }

        $intNow = Bingo_Timer::getNowTime();

        $intIsReliable = 0;
        $intPriority = self::PRIORITY_MIDDLE;

        if (Lib_Im_Command::MSG_TYPE_GIFT == $intMsgType) {
            // 礼物消息，必达
            $intIsReliable = 1;
            $intPriority = self::PRIORITY_HIGH;
        }

        $arrParams = array(
            'room_id'      => $intRoomId,
            'service_info' => json_encode($arrInput),
            'service_type' => (string)$intServiceType,
            'ts'           => $intNow,
            'apikey'       => self::APP_KEY,
            'priority'     => $intPriority,
            'is_reliable'  => $intIsReliable,
            'caller_flag'  => self::CALLER_FLAG_TIEBA,
        );

        if ($intMsgExpires > 0){
            $arrParams['msg_expires'] = $intMsgExpires;
        }

        if ($intPersist > 0){
            $arrParams['disable_persist'] = $intPersist;
        }

        $arrParams['sign'] = self::_genSign($arrParams);

        $strApiUrl = self::_getApiUrl(self::API_ROOM_SEND_SERVICE);

        self::_callApi($strApiUrl, $arrParams);
        return array('errno' => 0 , 'errmsg' => 'success');
    }


    /**
     * _sendServiceInfoEx
     * @param $arrInput
     * @return bool
     */
    private static function _sendServiceInfoEx($arrInput) {
        $intRoomId  = $arrInput['room_id'];
        $intMsgType = $arrInput['msg_type'];
        $intMsgExpires = intval($arrInput['msg_expires']);
        $intPersist = intval($arrInput['disable_persist']);     //消息是否入库字段，1为否


        $intServiceType = isset(self::$arrMsgTypeToServiceTypeMap[$intMsgType]) ? self::$arrMsgTypeToServiceTypeMap[$intMsgType] : 0;

        if (!$intServiceType) {
            Bingo_Log::warning('No mapping service type. input:'.serialize($arrInput));
            return false;
        }

        $intNow = Bingo_Timer::getNowTime();

        $intIsReliable = 0;
        $intPriority = self::PRIORITY_MIDDLE;

        if (Lib_Im_Command::MSG_TYPE_GIFT == $intMsgType) {
            // 礼物消息，必达
            $intIsReliable = 1;
            $intPriority = self::PRIORITY_HIGH;
        }

        $arrParams = array(
            'uid'          => intval($arrInput['user_id']),
            'room_id'      => $intRoomId,
            'service_info' => json_encode($arrInput),
            'service_type' => (string)$intServiceType,
            'ts'           => $intNow,
            'apikey'       => self::APP_KEY,
            'priority'     => $intPriority,
            'is_reliable'  => $intIsReliable,
            'caller_flag'  => self::CALLER_FLAG_TIEBA,
        );

        if ($intMsgExpires > 0){
            $arrParams['msg_expires'] = $intMsgExpires;
        }

        if ($intPersist > 0){
            $arrParams['disable_persist'] = $intPersist;
        }

        $arrParams['sign'] = self::_genSign($arrParams);

        $strApiUrl = self::_getApiUrl(self::API_ROOM_SEND_SERVICE);

        return self::_callApi($strApiUrl, $arrParams);
    }

    /**
     * _sendSysMsgEx
     * @param $arrInput
     * @return bool
     */
    private static function _sendSysMsgEx($arrInput) {
        $intRoomId  = $arrInput['room_id'];
        $intMsgType = $arrInput['msg_type'];
        $intMsgExpires = intval($arrInput['msg_expires']);

        $intServiceType = isset(self::$arrMsgTypeToServiceTypeMap[$intMsgType]) ? self::$arrMsgTypeToServiceTypeMap[$intMsgType] : 0;

        if (!$intServiceType) {
            Bingo_Log::warning('No mapping service type. input:'.serialize($arrInput));
            return false;
        }

        $intNow = Bingo_Timer::getNowTime();

        $intPriority = self::PRIORITY_HIGH;

        $arrMsg = array(
            'type' => self::IM_TYPE_SYS,
            'data' => array(
                'service_type' => (string)$intServiceType,
                'service_info' => $arrInput,
            ),
        );

        $strMsg = json_encode($arrMsg);

        $arrParams = array(
            'room_id'     => $intRoomId,
            'msg'         => $strMsg,
            'ts'          => $intNow,
            'apikey'      => self::APP_KEY,
            'priority'    => $intPriority,
            'caller_flag' => self::CALLER_FLAG_TIEBA,
        );

        if ($intMsgExpires > 0){
            $arrParams['msg_expires'] = $intMsgExpires;
        }

        $arrParams['sign'] = self::_genSign($arrParams);

        $strApiUrl = self::_getApiUrl(self::API_ROOM_SEND_SYS_MSG);

        Bingo_Log::warning("function::_sendSysMsgEx::_callApi::params::".var_export($arrParams,true));
        return self::_callApi($strApiUrl, $arrParams);
    }

    /**
     * _feedbackEx
     * @param $arrInput
     * @return bool
     */
    private static function _feedbackEx($arrInput) {
        $intRoomId = $arrInput['room_id'];
        $intUserId = $arrInput['user_id'];

        $intNow = Bingo_Timer::getNowTime();

        $arrParams = array(
            'room_id'     => $intRoomId,
            'number'      => 1,
            'ts'          => $intNow,
            'uid'         => $intUserId,
            'source_type' => 0,
            'apikey'      => self::APP_KEY,
            'caller_flag' => self::CALLER_FLAG_TIEBA,
        );

        $arrParams['sign'] = self::_genSign($arrParams);

        $strApiUrl = self::_getApiUrl(self::API_USER_FEEDBACK);

        return self::_callApi($strApiUrl, $arrParams);
    }

    /**
     * isBlock
     * @param $intUserId
     * @param $intMCastId
     * @return bool
     */
    public static function isBlock($intUserId, $intMCastId) {
	    $intNow = Bingo_Timer::getNowTime();

        $arrParams = array(
            'target_id' => (int)$intUserId,
            'source_id' => (int)$intMCastId,
            'timestamp' => $intNow,
        );

        $arrSignParams = array(
            'room_id'   => (int)$intMCastId,
            'timestamp' => $intNow,
        );

        $arrParams['sign'] = self::_genSign($arrSignParams);

        $strApiUrl = self::_getApiUrl(self::API_IS_BLOCK, self::API_HOST_BLOCK);

        $strRet = self::_callApi($strApiUrl, $arrParams, false, 'JSON');

        if (false === $strRet) {
            Bingo_Log::warning('call api fail. Ret:'.serialize($strRet));
            return false;
        }

        $arrRet = json_decode($strRet, true);

        if ($arrRet['in_black_list'] == true) {
            Bingo_Log::warning('call api success. Input:['.$intUserId.','.$intMCastId.'] Ret:'.serialize($strRet));
            return true;
        }

        return false;
    }

    /**
     * unBanUser
     * @param $intUserId
     * @param $intRoomId
     * @return bool
     */
    public static function unBanUser($intUserId, $intRoomId) {
        $arrParams = array(
            'uid'     => $intUserId,
            'room_id' => $intRoomId,
            'ak'      => Alalib_Conf_Sdk::SHOU_BAI_AK,
        );

        if (self::RAL_SWITCH == false) {
            // ral开启的话，底层封装的ral的call方法会自动增加sign，重复增加的话会导致sign计算错误
            $arrParams['sign'] = Alalib_Util_ShouBaiSdk::genSign($arrParams, Alalib_Conf_Sdk::SHOU_BAI_SK);

            $arrParams = array(
                'params' => $arrParams,
            );
        }

        $strApiUrl = self::_getApiUrl(self::API_UNBAN_USER, self::API_HOST_UNBAN);

        $strRet = self::_callApi($strApiUrl, $arrParams, false);

        if (false === $strRet) {
            Bingo_Log::warning('call unban user fail. input:'.serialize($arrParams));
            return false;
        }

        $arrRet = json_decode($strRet, true);

        if (0 != $arrRet['errno']) {
            Bingo_Log::warning('call unban user fail. input:'.serialize($arrParams).' output:'.serialize($strRet));
            return false;
        }

        Bingo_Log::warning('call unban user success. input:'.serialize($arrParams).' output:'.serialize($strRet));

        return true;
    }

    /**
     * callApiFromNmq 在NMQ中调用API
     * @param $strApiUrl
     * @param $arrParams
     * @return bool
     */
    public static function callApiFromNmq($strApiUrl, $arrParams) {
        return self::_callApi($strApiUrl, $arrParams, false);
    }

    /**
     * _callApi
     * @param $strApiUrl
     * @param $arrParams
     * @param bool $isUseNmq
     * @param string $format
     * @return bool
     */
    private static function _callApi($strApiUrl, $arrParams, $isUseNmq = self::NMQ_SWITCH, $format = 'FORM') {
        if ($isUseNmq) {
            // nmq调用
            return self::_callNmq($strApiUrl, $arrParams);
        }

        if (self::RAL_SWITCH) {
            return self::_callApiByRal($strApiUrl, $arrParams, $format);
        }

        if (self::ORP_FETCH_URL_SWITCH) {
            return self::_callApiByFetchUrl($strApiUrl, $arrParams, $format);
        }

        return self::_callApiByCURL($strApiUrl, $arrParams, $format);
    }

    /**
     * _callApiByRal
     * @param $strApiUrl
     * @param $arrParams
     * @param string $format
     * @return bool
     */
    private static function _callApiByRal($strApiUrl, $arrParams, $format = 'FORM') {
        $arrUrl = parse_url($strApiUrl);

        $strPath = $arrUrl['path'];
        $strOrigin = $arrUrl['scheme'].'://'.$arrUrl['host'];

        if (!empty($arrUrl['port'])) {
            $strOrigin .= ':'.$arrUrl['port'];
        }

        if ('JSON' == $format) {
            // json格式post的方式，直接以字符串格式传递
            // ral配置里converter是json了，所以不用再次json_encode
//            $arrParams = json_encode($arrParams);
        }

        if (isset(self::$_arrRalFuncMaps[$strOrigin])) {
            $strRet = call_user_func_array(self::$_arrRalFuncMaps[$strOrigin], array($strPath, $arrParams));
        } else {
            Bingo_Log::warning('ral host or func not exists. input:['.$strApiUrl.','.serialize($arrParams).'] url:'.serialize($arrUrl));
            return false;
        }

        if (false === $strRet) {
            Bingo_Log::warning('ral call fail. input:['.$strPath.','.serialize($arrParams).']');
            return false;
        }

        Bingo_Log::warning('ral call success. input:['.$strPath.','.serialize($arrParams).'] output:'.serialize($strRet));

        return json_encode($strRet);
    }

    /**
     * _callApiByFetchUrl
     * @param $strApiUrl
     * @param $arrParams
     * @param string $format
     * @return bool
     */
    private static function _callApiByFetchUrl($strApiUrl, $arrParams, $format = 'FORM') {
        $arrOptions = array(
            'timeout'      => 1000,
            'conn_timeout' => 2000,
        );
        $objHttpProxy = Orp_FetchUrl::getInstance($arrOptions);

        $header = array(
            "useragent"    => "request",
            'content-type' => "application/x-www-form-urlencoded",
        );

        if ('JSON' == $format) {
            $header['content-type'] = 'application/json';
            // json格式post的方式，直接以字符串格式传递
            $arrParams = json_encode($arrParams);
        }

        $cookie = array();

        Bingo_Timer::start(__CLASS__."::".__FUNCTION__."_fetch_url");
        $ret = $objHttpProxy->post($strApiUrl, $arrParams, $header, $cookie);
        Bingo_Timer::end(__CLASS__."::".__FUNCTION__."_fetch_url");

        $intErrno = $objHttpProxy->errno();

        // 这个错误码是curl的错误码,0是成功
        $strErrmsg = $objHttpProxy->errmsg();

        if (false === $ret || $intErrno != 0) {
            // 判断访问是否成功
            Bingo_Log::warning(__CLASS__.'::'.__FUNCTION__.' fetch url fail. input['.$strApiUrl.', '.serialize($arrParams).'], '."errno:[$intErrno],errmsg:[$strErrmsg],ret:[".serialize($ret).']');
            return false;
        }

        Bingo_Log::warning(__CLASS__.'::'.__FUNCTION__.' fetch url success. input['.$strApiUrl.', '.serialize($arrParams).'], '."errno:[$intErrno],errmsg:[$strErrmsg],ret:[".serialize($ret).']');

        return $ret;
    }

    /**
     * _callApiByCURL
     * @param $url
     * @param array $post_vars
     * @param string $format
     * @return bool|mixed
     */
    private static function _callApiByCURL($url, $post_vars = array(), $format = 'FORM') {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_NOBODY, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        $header[] = 'Host: tieba.baidu.com';
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);

        $strParam = '';

        if ('FORM' == $format) {
            $strParam = http_build_query($post_vars, 'param_');
        } else if ('JSON' == $format) {
            $strParam = json_encode($post_vars);
        }

        curl_setopt($ch, CURLOPT_POSTFIELDS, $strParam);

        if (defined('CURLOPT_CONNECTTIMEOUT_MS')) {
            curl_setopt($ch, CURLOPT_NOSIGNAL, 1);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT_MS, 2000);
            curl_setopt($ch, CURLOPT_TIMEOUT_MS, 2000);
        } else {
            $intTimeout = ceil(2000 / 1000);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $intTimeout);

            $intTimeout = ceil(2000 / 1000);
            curl_setopt($ch, CURLOPT_TIMEOUT, $intTimeout);
        }

        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $data = curl_exec($ch);
        curl_close($ch);

        if (curl_errno($ch)) {
            Bingo_Log::warning(__CLASS__.'::'.__FUNCTION__.' curl fail. param['.$strParam.'], input['.$url.', '.serialize($post_vars).'], '."Curl error:".curl_error($ch));
        }

        if ($data === false) {
            Bingo_Log::warning(__CLASS__.'::'.__FUNCTION__.' curl fail. param['.$strParam.'], input['.$url.', '.serialize($post_vars).'], '."ret:[".serialize($data).']');
            return false;
        }

        Bingo_Log::warning(__CLASS__.'::'.__FUNCTION__.' curl success. param['.$strParam.'], input['.$url.', '.serialize($post_vars).'], '."ret:[".serialize($data).']');

        return $data;
    }

    /**
     * _callNmq
     * @param $strApiUrl
     * @param $arrParams
     * @return bool
     */
    private static function _callNmq($strApiUrl, $arrParams) {
        $arrNmqInput = array(
            'url'    => $strApiUrl,
            'params' => $arrParams,
        );

        $strNmpTopic = Lib_Im_Nmq::ALA_NMQ_TOPIC;
        $strNmqCmd = Lib_Im_Nmq::ALA_NMQ_CMD_PUSH_TO_DUAPP;

        $nmqRet = Tieba_Commit::commit($strNmpTopic, $strNmqCmd, $arrNmqInput);

        if (false === $nmqRet || Tieba_Errcode::ERR_SUCCESS !== $nmqRet['err_no']) {
            Bingo_Log::warning("send nmq fail, nmq topic: ".$strNmpTopic." nmq cmd: $strNmqCmd"."nmq input is ".serialize($arrNmqInput).'nmq output is'.serialize($nmqRet));
            return false;
        }

        return true;
    }

    /**
     * _genSign
     * @param $arrParams
     * @return string
     */
    private static function _genSign($arrParams) {
        $ts = '';

        // 手百IM的两个接口，一个是ts，一个是timestamp。。。
        if (isset($arrParams['ts'])) {
            $ts = $arrParams['ts'];
        } else if (isset($arrParams['timestamp'])) {
            $ts = $arrParams['timestamp'];
        }

        return md5($arrParams['room_id'].$ts.self::APP_SECRET_KEY);
    }

    /**
     * _getApiUrl
     * @param $strPath
     * @return string
     */
    private static function _getApiUrl($strPath, $strHost = self::API_HOST) {
        return $strHost.$strPath;
    }
	
	
    /**
     * isRoomSyncedToDuApp
     * @param $intRoomId
     * @param int $intAppFrom
     * @return bool
     */
	public static function isRoomSyncedToDuApp($intRoomId, $intAppFrom = 1)
	{
		$sdk_unified = true; // sdk全打通，需要将贴吧的消息同步给全民好看以及手百，以前只是部分同步手百(白名单)
		if ($sdk_unified) {
			return true; // tieba ->quanmin/haokan/shoubai
		}
        // 除了手百、贴吧的用户，其他全部同步，如好看、全民等
		$intAppFrom = empty($intAppFrom) ? 1 : $intAppFrom;
		if(Alalib_Conf_Sdk::getFromApp(Alalib_Conf_Sdk::SUBAPP_SHOUBAI) != $intAppFrom && Alalib_Conf_Sdk::getFromApp(Alalib_Conf_Sdk::SUBAPP_TIEBA) != $intAppFrom){
			return true;
		}

        if (isset(self::$_arrRoomSyncedToDuAppInfo[$intRoomId])) {
            return self::$_arrRoomSyncedToDuAppInfo[$intRoomId];
        }

        $arrRoomReq = array(
            'room_ids'   => array($intRoomId),
            'need_cache' => 1,
        );

        $strServiceName   = 'ala';
        $strServiceMethod = 'mgetLiveRoomDispatchInfoByRoomIds';

        $arrRoomRes = Tieba_Service::call($strServiceName, $strServiceMethod, $arrRoomReq, null, null, "post", null, "utf-8");

        if (false === $arrRoomRes || Tieba_Errcode::ERR_SUCCESS != $arrRoomRes["errno"]) {
            $strLog = __CLASS__ . "::" . __FUNCTION__ . " call $strServiceName $strServiceMethod fail. input:[" . serialize($arrRoomReq) . "]; output:[" . serialize($arrRoomRes) . "]";
            Bingo_Log::warning($strLog);
            return false;
        }

        $arrRoomInfo = $arrRoomRes['data'][$intRoomId];

        if (empty($arrRoomInfo)) {
            self::$_arrRoomSyncedToDuAppInfo[$intRoomId] = false;
            return false;
        }

        // 未同步到手百侧
        if ($arrRoomInfo['to_feed'] == 0 && $arrRoomInfo['to_channel'] == 0 && $arrRoomInfo['to_slide'] == 0) {
            self::$_arrRoomSyncedToDuAppInfo[$intRoomId] = false;
            return false;
        }

        // 手百单侧关播了
        if ($arrRoomInfo['live_status'] != 1) {
            self::$_arrRoomSyncedToDuAppInfo[$intRoomId] = false;
            return false;
        }

        self::$_arrRoomSyncedToDuAppInfo[$intRoomId] = true;

        return true;
    }
}
