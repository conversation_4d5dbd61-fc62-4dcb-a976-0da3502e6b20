<?php

/**
 * 发送拉取消息通知
 * <AUTHOR>
 *
 */
class Service_Msg_Impl_SendNotifyGroupMsg extends Service_Msg_Impl_Base
{
	/**
	 * 
	 * @param unknown_type $arrInput
	 * @return Ambigous <{Array}, multitype:{Int} {String} {Array} NULL >
	 */
	public static function commitGroupMsg($arrInput)
	{
		$intUserId = intval($arrInput['user_id']);
		$intGroupId = intval($arrInput['group_id']);
		$intMsgType = intval($arrInput['msg_type']);
		$strUserName = isset($arrInput['user_name']) ? $arrInput['user_name'] : '';
		$intSysNotify = isset($arrInput['sys_notify']) ? intval($arrInput['sys_notify']) : 0;
		$content  = $arrInput['content'];
		$intNeedShield = 0;
		
		Lib_Util_Log::pushNotice('user_id', $intUserId);
		Lib_Util_Log::pushNotice('group_id', $intGroupId);
		Lib_Util_Log::pushNotice('msg_type', $intMsgType);
		Lib_Util_Log::pushNotice('user_name', $strUserName);
		Lib_Util_Log::debug('content='.$content);
		
		if (isset(Alalib_Util_WhiteListLog::$arrWhiteUserIds[$intUserId]))
		{
			Lib_Util_Log::warning('content='.$content);
		}
		
		$intMsgId = Lib_Util_IdAlloc::allocId(Lib_Util_IdAlloc::TYPE_ALAIM_MSG_ID);
		if (false === $intMsgId)
		{
			Lib_Util_Log::warning('alloc msg id fail');
			return self::errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
		}
		
		Lib_Util_Log::pushNotice('msg_id', $intMsgId);
		$arrOutput = array('group_id' => $intGroupId,
				'msg_id' => $intMsgId,
				'needshield' => $intNeedShield,
		);

	    $contentLenCheckRet = Service_Msg_Strategy_ContentFilter::checkMsgContentLen($arrInput);
					
		if (true !== $contentLenCheckRet)
		{
			Lib_Util_Log::warning("commitGroupMsg_conent is too length. ");
			return self::errRet(Tieba_Errcode::MSG_CONTENT_TOO_LONG, $arrOutput);
		}
		
		// 群组合法性检查
		Bingo_Timer::start('check_group_info');
		$intGroupMemberCount = Service_Msg_Strategy_GroupValid::getLiveGroupMemberCount($arrInput);
		Bingo_Timer::end('check_group_info');
		
		if ($intGroupMemberCount < 1)
		{
			Lib_Util_Log::warning("group info is error, input is ".serialize($arrInput));
			return self::errRet(Tieba_Errcode::ERR_IM_GROUP_NOT_EXIST, $arrOutput);
		}
		Lib_Util_Log::pushNotice('grp_cnt', $intGroupMemberCount);

		if (!$intSysNotify && $intUserId > 0){
			// 检查用户是否被禁言
			Bingo_Timer::start('check_group_user_block');
			$intIsBlockIm = Service_Msg_Strategy_GroupValid::isIMBlock($intGroupId, $intUserId);
			Bingo_Timer::end('check_group_user_block');
			Lib_Util_Log::pushNotice('block_im', $intIsBlockIm);
			
		}
		
		if ($intIsBlockIm > 0)
		{
			Lib_Util_Log::warning("group user is im block, input is ".serialize($arrInput));
			return self::errRet(Alalib_Conf_Error::ERR_IM_GROUP_USER_BLOCK, $arrOutput);
		}
				
		// 检查用户是否被全局禁言
		Bingo_Timer::start('check_global_user_block');
		$intIsGlobalBlockIm = Service_Msg_Strategy_GroupValid::isIMGlobalBlock($intUserId);
		Bingo_Timer::end('check_global_user_block');
		Lib_Util_Log::pushNotice('global_block_im', $intIsGlobalBlockIm);
		
		if (true === $intIsGlobalBlockIm)
		{
			Lib_Util_Log::warning("user is global im block, input is ".serialize($arrInput));
			return self::errRet(Alalib_Conf_Error::ERR_IM_GROUP_USER_BLOCK_ALL, $arrOutput);
		}
		
		Bingo_Timer::start('anti_check');
		$arrAntiRet = Lib_Service_Anti::antiAlaImCheckBeforeCommit($content, $intUserId, $intGroupId);
		Bingo_Timer::end('anti_check');
		
		// 判断黄反
		$confilterErrno = isset($arrAntiRet['res']['confilter']) ? intval($arrAntiRet['res']['confilter']) : 0;
		// 粒度控制
		$actsctrlErrno = isset($arrAntiRet['res']['actsctrl']) ? intval($arrAntiRet['res']['actsctrl']) : 0;
		// 判断IM内容是否需要替换
		$isReplace = isset($arrAntiRet['res']['textnorm']['need_replace']) ? $arrAntiRet['res']['textnorm']['need_replace'] :false;
			
		if (0 !== $confilterErrno && Tieba_Errcode::ERR_ANTI_POST_LATERAUDIT !== $confilterErrno)
		{
			Bingo_Log::warning('anti confilter strategy hit, input: '.serialize($arrInput).' output is '.serialize($arrAntiRet));
			Lib_Util_Log::pushNotice('need_shield', 1);
			$arrOutput['needshield'] = 1;
			return self::errRet(Alalib_Conf_Error::ERR_IM_UEG_SENSITIVE_FAILE, $arrOutput);
		}
		
		if (0 !== $actsctrlErrno)
		{
			Lib_Util_Log::warning('send group message too more');
			return self::errRet(Alalib_Conf_Error::ERR_IM_UEG_OFFEN_FAILE, $arrOutput);
		}

        // textnorm 这个很早以前ksarch的一个模块，现在应该也没人维护了 @杨磊
        // 用户uid：3152640310替换成***2640310了，导致端无法解析
        /*
        if (true === $isReplace)
		{
			Bingo_Log::pushNotice('test_is_rpl',1);
			$strReplaceContent = isset($arrAnti Ret['res']['textnorm']['replace_content']) ? strval($arrAntiRet['res']['textnorm']['replace_content']) : '';
			Bingo_Log::warning("after replace content is $strReplaceContent");
		
			if ('' === $strReplaceContent)
			{
				return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
			}
			else
			{
				$content = $strReplaceContent;
				$arrInput['content'] = $content;
			}
		}
        */
	
			
	    
		if($intUserId > 0)
		{
			$arrUserInfoRet = Lib_Service_User::mgetUserInfo(array($intUserId));
			$arrUserInfo = isset($arrUserInfoRet['data'][$intUserId]) ? $arrUserInfoRet['data'][$intUserId] : array();
			if (false === $arrUserInfoRet || empty($arrUserInfo))
			{
				Lib_Util_Log::warning('send message user is not exist, the user id is '.$intUserId);
				return self::errRet(Alalib_Conf_Error::ERR_USER_NOT_EXIST, $arrOutput);
			}
			
			// 如果是系统消息则用户信息直接从请求内容
			if (isset(Lib_Im_Command::$ALA_SYS_MSG_TYPES[$intMsgType]))
			{
			    if ('' === $strUserName)
			    {
			        Lib_Util_Log::warning('input user_name is null ');
			        //return self::errRet(Tieba_Errcode::ERR_PARAM_ERROR);
			    }
			    elseif($arrUserInfo['user_name'] !== $strUserName)
			    {
			        $arrUserInfo['user_name'] = $strUserName;
			    }
			}
		}

        $arrUserInfo['user_name'] = empty($arrUserInfo['user_name']) ? strval($arrUserInfo['user_nickname']) : $arrUserInfo['user_name'];  //user_name 为空,用user_nickname显示
		
		$arrInput['msg_id'] = $intMsgId;
		$arrInput['user_info'] = $arrUserInfo;
		$arrInput['create_time'] = time();
		Bingo_Timer::start('store_group_msg');
		$storeRet = self::_storeGroupMsg($arrInput);
		Bingo_Timer::end('store_group_msg');
		
		if (false === $storeRet)
		{
			Lib_Util_Log::error("msg store to db fail");
			return self::fatal(Tieba_Errcode::ERR_DL_CALL_FAIL, $arrOutput);
		}
		
		Bingo_Timer::start('store_msg_chain');
		$storeMsgChainRet = self::_storeGroupMsgChain($arrInput);
        Bingo_Timer::end('store_msg_chain');
        
        if (false === $storeMsgChainRet)
        {
        	Lib_Util_Log::error("msg chain store to db fail");
        	return self::errRet(Tieba_Errcode::ERR_DL_CALL_FAIL, $arrOutput);
        }
		
		Bingo_Timer::start('build_lcs_push_msg');
		$arrMsgInfo = self::_buildMsgInfo($arrInput);
		$arrInput['msg_info_list'] = $arrMsgInfo;
		$strLcsPushMsg = self::_buildLcsPushMsg($arrInput);
		$arrInput['lcs_content'] = $strLcsPushMsg;
		Bingo_Timer::end('build_lcs_push_msg');	
		$arrInput['lcs_cmd'] = Lib_Im_Command::ALA_CMD_PUSH_NOTIFY;
		Lib_Util_Log::pushNotice('lcs_cmd', Lib_Im_Command::ALA_CMD_PUSH_NOTIFY);
		
		if (isset(Alalib_Util_WhiteListLog::$arrWhiteUserIds[$intUserId]))
		{
			$arrLog = $arrInput;
			unset($arrLog['lcs_content']);
			Lib_Util_Log::warning(serialize($arrLog));
		}
		
		if ($intGroupMemberCount < Lib_Im_Const::ALA_GROUP_MSG_SYNC_MAX_NUM)
		{
			Bingo_Timer::start('send_lcs_push_msg');
			$sendRet = Service_Msg_Strategy_SendMsg::sendGroupLcsMsgByPush($arrInput);
			Bingo_Timer::end('send_lcs_push_msg');
			
			if (false === $sendRet)
			{
				Lib_Util_Log::warning("send msg fail");
				return self::errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL, $arrOutput);
			}
			
			return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
		}
		else
		{				
		    Bingo_Timer::start('send_to_nmq');	
			// 直推的群聊消息走普通的NMQ，后续可以根据不同的消息类型，判断走不同的nmq通道
			$arrInput['nmq_cmd'] = Lib_Im_Nmq::ALA_NMQ_CMD_PUSH_LIVE_GROUP_VIP_MSG;
			$arrInput['grp_cnt'] = $intGroupMemberCount;
		    $nmqRet = Service_Msg_Strategy_SendMsg::sendGroupLcsMsgByNmq($arrInput);
		    Bingo_Timer::end('send_to_nmq');
		    
		    if (false === $nmqRet)
		    {
		    	Lib_Util_Log::warning('send to nmq fail');
		    	return self::errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL, $arrOutput);
		    }
		}
		
		return self::errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutput);
	}
	
	
	
	/**
	 * 将消息存储到DB
	 * @param unknown_type $arrInput
	 * @return boolean
	 */
	private static function _storeGroupMsg($arrInput)
	{
		$intGroupId = isset($arrInput['group_id']) ? intval($arrInput['group_id']) : 0;
	    $intMsgId = isset($arrInput['msg_id']) ? intval($arrInput['msg_id']) : 0;
	    $intMsgType = isset($arrInput['msg_type']) ? intval($arrInput['msg_type']) : 0;
	    $intUserId = isset($arrInput['user_id']) ? intval($arrInput['user_id']) : 0;
	    $strContent = isset($arrInput['content']) ? strval($arrInput['content']) : '';   
	    $intDuration = isset($arrInput['duration']) ? intval($arrInput['duration']) : 0;
	    $intStatus = isset($arrInput['status']) ? intval($arrInput['status']) : 0;
	    $intCreateTime = isset($arrInput['create_time']) ? intval($arrInput['create_time']) : time();
	    
	    $arrDlInput = array('group_id' => $intGroupId,
	    		'msg_id' => $intMsgId,
	    		'msg_type' => $intMsgType,
	    		'user_id' => $intUserId,
	    		'content' => $strContent,
	    		'duration' => $intDuration,
	    		'status' => $intStatus,
	    		'create_time' => $intCreateTime, 
	    		);
	    
	    $arrDlRet = Dl_Gmsg_Gmsg::addGroupNotifyMsg($arrDlInput);
	    
	    if (Tieba_Errcode::ERR_SUCCESS !== $arrDlRet['errno'])
	    {
	    	Lib_Util_Log::error('call dl fail, the output is '.serialize($arrDlRet));
	    	return false;
	    }
	    		
	    return true;
	}
	
	/**
	 * 将需要拉取的消息放到消息拉链中，这个拉链是有生命周期的，如果3天内没有新的
	 * @param unknown_type $arrInput
	 */
	private static function _storeGroupMsgChain($arrInput)
	{
		$intGroupId = isset($arrInput['group_id']) ? intval($arrInput['group_id']) : 0;
		$intMsgId = isset($arrInput['msg_id']) ? intval($arrInput['msg_id']) : 0;
		
		$arrDlInput = array('group_id' => $intGroupId,
				'msg_id' => $intMsgId,
				);
			
		$arrDlRet = Dl_Gmsg_Gmsg::zaddLiveGid2MidRedis($arrDlInput);
		
		if (Tieba_Errcode::ERR_SUCCESS !== $arrDlRet['errno'])
		{
			Lib_Util_Log::warning('call dl fail, the output is '.serialize($arrDlRet));
			return false;
		}
		 
		return true;
	}
	    	
	/**
	 * 
	 * @param unknown_type $arrInput
	 * @return multitype:unknown string
	 */
	private static function _buildMsgInfo($arrInput)
	{
		$arrMsgInfo = array();
		$arrMsgInfo['groupId'] = $arrInput['group_id'];
		$arrMsgInfo['msgId'] = $arrInput['msg_id'];
		$arrMsgInfo['type'] = $arrInput['msg_type'];
		$arrMsgInfo['content'] = $arrInput['content'];
		
		if (isset($arrInput['groupType']))
		{
			$arrMsgInfo['groupType'] = $arrInput['groupType'];
		}
		
        $arrMsgInfo['pushTime'] = $arrInput['create_time'];
         
        if (isset($arrInput['et']))
        {
        	$arrMsgInfo['et'] = strval($arrInput['et']);
        }

        if (isset($arrInput['task_id']))
        {
        	$arrMsgInfo['taskId'] = $arrInput['task_id'];
        }
	    //Bingo_Log::warning('hyf_debug'.print_r($arrMsgInfo, true));
		return $arrMsgInfo;
	}
	
	/**
	 * 构建要推送的消息
	 * @param unknown_type $arrInput
	 * @return string
	 */
	private static function _buildLcsPushMsg($arrInput)
	{
		//Bingo_Log::warning('hyf_debug_lcsinput'.print_r($arrInput, true));
		$arrMsgInfos = isset($arrInput['msg_info_list']) ? $arrInput['msg_info_list'] : array();
		$intPushTime = isset($arrInput['create_time']) ? $arrInput['create_time'] : time();
		$intGroupId = isset($arrInput['group_id']) ? $arrInput['group_id'] : 0;

		$errno = Tieba_Errcode::ERR_SUCCESS;
		$errInfo = array(
				'errorno' => $errno,
				'usermsg' => Tieba_Error::getErrmsg($errno),
				'logid' => Bingo_Log::getLogId(),
				'time' => time(),
		);
		
		$arrPushMsg = array(
				'cmd' => Lib_Im_Command::ALA_CMD_PUSH_NOTIFY,
				'data' => $arrMsgInfos,
		);
				
		$arrLCSDataRes = array(
				'pushTime' => $intPushTime,
				'multiMsg' => array($arrPushMsg,),
				);
		//Bingo_Log::debug(serialize($arrLCSDataRes));
		
		$intUserId = isset($arrInput['user_id']) ? $arrInput['user_id'] : 0;
		
		if (isset(Alalib_Util_WhiteListLog::$arrWhiteUserIds[$intUserId]))
		{
			Lib_Util_Log::warning(serialize($arrLCSDataRes));
		}
		
		$packedContent = Lib_Util_Pack::array2pack($arrLCSDataRes, 'msgpack');
		return $packedContent;
	}
	
}