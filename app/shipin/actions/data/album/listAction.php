<?php
/**
 * ���ݽӿڣ������Ƶ�ͱ༭��Ƶʱʹ��
 **/
class listAction extends Shipin_Action_View {

	public function init() {
		$this->_urlkey = 'data_album_list';
		return parent::init();
	}

	protected function _process () {
		// no
		Shipin_Dict::set('no', 0);
		// error
		Shipin_Dict::set('error','');
		// data
		$intReqnum = Shipin_Conf::get('data_max_album_num', 100);
		$_arrAlbums = Shipin_Data_Video::getAlbumList(Shipin_Dict::$intFid, 0, $intReqnum);
		$arrAlbum = Shipin_Logic_Common::buildSimpleAlbumList($_arrAlbums);
		$arrData = array (
			'alist'	=> $arrAlbum,
		);
		Shipin_Dict::set('data', $arrData);
	}
}
/* vim: set noexpandtab ts=4 sw=4 sts=4 tw=100: */