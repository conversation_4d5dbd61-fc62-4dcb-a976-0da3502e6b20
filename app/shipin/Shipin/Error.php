<?php
/**
 * ��Ƶ ui ���쳣�ֻ࣬��������벻��ִ����ȥ��ʱ������쳣
 **/
class Shipin_Error extends Exception {

	protected $_errno	= 0;

	protected $_errmsg	= '';

	public function __construct ($intErrno, $strErrstr='') {
		$this->_errno	= $intErrno;
		$this->_errmsg	= Shipin_Errno::$errMsg[$intErrno];
		
		//Bingo_Log::warning($intErrno . ' ' . $strErrstr );
		/*if ($strErrstr!='') {
			Bingo_Log::warning($strErrstr, LOG_STATUS);
		}*/
	}

	public function getNo () {
		return $this->_errno;
	}

	public function getMsg () {
		return $this->_errmsg;
	}
}
/* vim: set noexpandtab ts=4 sw=4 sts=4 tw=100: */