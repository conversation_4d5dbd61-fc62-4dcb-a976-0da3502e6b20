<?php
    /**
     * Created by PhpStorm.
     * User: he<PERSON><PERSON><PERSON>
     * Date: 2019/7/24
     * Time: 11:34 AM
     */
    class Service_Attach_Attach
    {
        /**
         * 添加记录
         * @param $arrInput
         * @return array
         */
        public function addAttachRecords($arrInput) {
            if (empty($arrInput['tids']) || !is_array($arrInput['tids'])) {
                Bingo_Log::warning('tids error');
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, null, 'tids error');
            }
            if (empty($arrInput['swan_id']) || empty($arrInput['swan_desc']) || empty($arrInput['swan_path'])
                || empty($arrInput['swan_pic']) || empty($arrInput['swan_show_at']) || empty($arrInput['swan_name'])) {
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }

            $arrValue = array(
                'tid'           => '',
                'swan_id'       => $arrInput['swan_id'],
                'swan_desc'     => $arrInput['swan_desc'],
                'swan_path'     => $arrInput['swan_path'],
                'swan_pic'      => $arrInput['swan_pic'],
                'swan_show_at'  => $arrInput['swan_show_at'],
                'swan_name'     => $arrInput['swan_name'],
                'client_type'   => intval($arrInput['client_type']),
                'create_time'   => time(),
                'update_time'   => time(),
            );
            $arrDlInput = array(
                'function'  =>  'addAttachRecords',
                'values'    =>  array(),
            );
            foreach ($arrInput['tids'] as $tid) {
                $arrValue['tid']        = $tid;
                $arrDlInput['values'][] = $arrValue;
            }
            $arrRes = Dl_Smartapp_Smartapp::execSql($arrDlInput, true);
            if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
                Bingo_Log::fatal(__METHOD__ . ' call DB error! input:[' . serialize($arrDlInput) .']'. 'ret: ['. serialize($arrRes). ']');
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        /**
         * 删除记录
         * @param $arrInput
         * @return array
         */
        public function delAttachRecord($arrInput) {
            if (empty($arrInput['tid'])) {
                Bingo_Log::warning('tid error');
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR, null, 'tid error');
            }
            $arrValue = array(
                'tid'           => $arrInput['tid'],
                'function'      => 'delAttachRecord',
            );

            $arrRes = Dl_Smartapp_Smartapp::execSql($arrValue);
            if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
                Bingo_Log::fatal(__METHOD__ . ' call DB error! input:[' . serialize($arrValue) .']'. 'ret: ['. serialize($arrRes). ']');
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }


        /**
         * 删除记录
         * @param $arrInput
         * @return array
         */
        public function getAttachRecord($arrInput) {
            $limit = empty($arrInput['rn']) ? 10 : intval($arrInput['rn']);
            $offset = empty($arrInput['offset']) ? 0 : intval($arrInput['offset']);
            $cond = '';

            if (!empty($arrInput['tid'])) {
                $cond = ' where tid='.intval($arrInput['tid']).' ';
            }
            $arrValue = array(
                'limit'     => $limit,
                'offset'    => $offset,
                'cond'      => $cond,
                'function'  => 'getAttachRecord',
            );

            $arrRes = Dl_Smartapp_Smartapp::execSql($arrValue);
            if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
                Bingo_Log::fatal(__METHOD__ . ' call DB error! input:[' . serialize($arrValue) .']'. 'ret: ['. serialize($arrRes). ']');
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }


            $arrValue = array(
                'cond'      => $cond,
                'function'  => 'getTotalAttachRecord',
            );

            $total = Dl_Smartapp_Smartapp::execSql($arrValue);
            if (false === $total || Tieba_Errcode::ERR_SUCCESS !== $total['errno']) {
                Bingo_Log::fatal(__METHOD__ . ' call DB error! input:[' . serialize($arrValue) .']'. 'ret: ['. serialize($total). ']');
                //  return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            $arrRet = array(
                'rows'  => $arrRes['results'][0],
                'count' => $total['results'][0][0]['total'],
            );
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
        }

        /**
         * @param $errno
         * @param null $data
         * @param string $errmsg
         * @return array
         */
        protected static function _errRet($errno, $data = null, $errmsg = '')
        {
            $arrRet = array(
                'errno' => $errno,
                'errmsg' => empty($errmsg) ? Tieba_Error::getErrmsg($errno) : $errmsg,
            );
            if (!empty($data)) {
                $arrRet['data'] = $data;
            }
            return $arrRet;
        }
    }
