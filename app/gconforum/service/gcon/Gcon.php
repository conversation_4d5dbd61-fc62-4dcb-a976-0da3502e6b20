<?php
/* 
 * gconforum 模块的基础库代码
 * <AUTHOR>
 */
define("MODULE","gconforum_service");
class Service_Gcon_Gcon {
    const SERVICE_NAME = "Service_Gcon_Gcon";
    protected static $_conf = null;
    protected static $_use_split_db = false;
    
    public static function _errRet($errno) {
       return array(
           'errno'  => $errno,
           'errmsg' => Tieba_Error::getErrmsg($errno),
       );
    }
     //通用查询接口
    public function select($arrInput) {
        if (!isset($arrInput['table'])) {
            Bingo_Log::warning("there is no table. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!isset($arrInput['where'])) {
            Bingo_Log::warning("there is no table. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!isset($arrInput['cols'])) {
            $arrInput['cols'] = array('*');
        }
        $table = $arrInput['table'];
        $where = $arrInput['where'];
        $cols  = $arrInput['cols'];
        $row = Dl_Dbgconforum_Dbgconforum::selectData($table, $where, $cols);
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'row'    => $row,
        );
        return $arrOutput;
    }
    //通用插入记录接口
    public function insert($arrInput = array()) {
        if (!isset($arrInput['table'])) {
            Bingo_Log::warning("there is no table. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!isset($arrInput['data'])) {
            Bingo_Log::warning("there is no table. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $table = $arrInput['table'];
        $data  = $arrInput['data'];
        $row = Dl_Dbgconforum_Dbgconforum::addData($table, $data);
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'row'    => $row,
        );
        return $arrOutput;
    }
    //通用更新接口
    public function update($arrInput) {
        if (!isset($arrInput['table'])) {
            Bingo_Log::warning("there is no table. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!isset($arrInput['where'])) {
            Bingo_Log::warning("there is no where. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!isset($arrInput['data'])) {
            Bingo_Log::warning("there is no where. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $table = $arrInput['table'];
        $where = $arrInput['where'];
        $data = $arrInput['data'];
        $row = Dl_Dbgconforum_Dbgconforum::updateData($table, $where, $data);
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'row'    => $row,
        );
        return $arrOutput;
    }
    //通用replace接口
    public function replace($arrInput = array()) {
        if (!isset($arrInput['table'])) {
            Bingo_Log::warning("there is no table. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!isset($arrInput['data'])) {
            Bingo_Log::warning("there is no table. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $table = $arrInput['table'];
        $data  = $arrInput['data'];
        $row = Dl_Dbgconforum_Dbgconforum::replaceData($table, $data);
        if (!$row) {
            Bingo_Log::warning("fail to replace data!");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'row'    => $row,
        );
        return $arrOutput;
    }
    //通用删除接口
    public function delete($arrInput) {
        if (!isset($arrInput['table'])) {
            Bingo_Log::warning("there is no table. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!isset($arrInput['where'])) {
            Bingo_Log::warning("there is no table. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $table = $arrInput['table'];
        $where = $arrInput['where'];
        $row = Dl_Dbgconforum_Dbgconforum::deleteData($table, $where);
        if (!$row) {
            Bingo_Log::warning("fail to delete data!");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'row'    => $row,
        );
        return $arrOutput;
    }
    public function getForumFans() {
        $row = Dl_Dbgconforum_Dbgconforum::getForumFans();
        if (!$row) {
            Bingo_Log::warning("fail to get forum follower");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'row'    => $row,
        );
        return $arrOutput;
    }
    
    public function changeUserIcon($arrInput) {
        return Service_Gcon_Fourth_Manager::changeUserIcon($arrInput);
    }
    
    public function changeUserNickname($arrInput) {
        return Service_Gcon_Fourth_Manager::changeUserNickname($arrInput);
    }
    
    public function getCheckDayData($arrInput) {
        return Service_Gcon_Check_Forum::getCheckDayData($arrInput);
    }
    
    public function getCheckMonthData($arrInput) {
        return Service_Gcon_Check_Forum::getCheckMonthData($arrInput);
    }
    
    public function getCheckRedData($arrInput) {
        return Service_Gcon_Check_Forum::getCheckRedData($arrInput);
    }
    
    public function getMonthCheckForum() {
        return Service_Gcon_Check_Forum::getMonthCheckForum();
    }
    
    public function getCheckWeekData($arrInput) {
        return Service_Gcon_Check_Forum::getCheckWeekData($arrInput);
    }
    
    public function selectCheck($arrInput) {
        return Service_Gcon_Check_Forum::selectCheck($arrInput);
    }
    
    public function insertCheck($arrInput) {
        return Service_Gcon_Check_Forum::insertCheck($arrInput);
    }
    
    public function updateCheck($arrInput) {
        return Service_Gcon_Check_Forum::updateCheck($arrInput);
    }
    
    public function getForumNoticeByFid($arrInput) {
        return Service_Gcon_Notice_Notice::getForumNoticeByFid($arrInput);
    }
    
    public function getLastInsertData($arrInput) {
        return Service_Gcon_Notice_Notice::getLastInsertData($arrInput);
    }
    
    public function getNoticeList($arrInput) {
        return Service_Gcon_Notice_Notice::getNoticeList($arrInput);
    }
    
    public function addNotice($arrInput) {
        return Service_Gcon_Notice_Notice::addNotice($arrInput);
    }
    
    public function getSomeTimeData($arrInput) {
        return Service_Gcon_Check_Forum::getSomeTimeData($arrInput);
    }
    
    public function getDownloadData($arrInput) {
        return Service_Gcon_Check_Forum::getDownloadData($arrInput);
    }
    
    public function getDownloadRedData($arrInput) {
        return Service_Gcon_Check_Forum::getDownloadRedData($arrInput);
    }
    
    public function getDownloadDayData($arrInput) {
        return Service_Gcon_Check_Forum::getDownloadDayData($arrInput);
    }
    
    public function getDownloadWeekData($arrInput) {
        return Service_Gcon_Check_Forum::getDownloadWeekData($arrInput);
    }
    
    public function deleteCheck($arrInput) {
        return Service_Gcon_Check_Forum::deleteCheck($arrInput);
    }
    
    public function getUserOrder($arrInput) {
        return Service_Gcon_Base_Base::getUserOrder($arrInput);
    }
    
    public function getForumOrderStatus($arrInput) {
        return Service_Gcon_Base_Base::getForumOrderStatus($arrInput);
    }
    
    public function removeForumOrder($arrInput) {
        return Service_Gcon_Base_Base::removeForumOrder($arrInput);
    }
    
    public function updateAutoReplyStatus($arrInput) {
        return Service_Gcon_Platform_Platform::updateAutoReplyStatus($arrInput);
    }
    public function commitMsg($arrInput) {
        return Service_Gcon_Platform_Platform::commitMsg($arrInput);
    }
    
    public function setReply($arrInput) {
        return Service_Gcon_Platform_Platform::setReply($arrInput);
    }
    
    public function sentLikeForumMsg($arrInput) {
        return Service_Gcon_Platform_Platform::sentLikeForumMsg($arrInput);
    }
    
    public function platformReply($arrInput) {
        return Service_Gcon_Platform_Platform::platformReply($arrInput);
    }
    
    public function getForumMatList($arrInput) {
        return Service_Gcon_Platform_Platform::getForumMatList($arrInput);
    }
    
    public function getForumMsgList($arrInput) {
        return Service_Gcon_Platform_Platform::getForumMsgList($arrInput);
    }
    
    public function getForumNewMsg($arrInput) {
        return Service_Gcon_Platform_Platform::getForumNewMsg($arrInput);
    }
    
    public function getPlatformMsg($arrInput) {
        return Service_Gcon_Platform_Platform::getPlatformMsg($arrInput);
    }
    
    public function cleanForumReply($arrInput) {
        return Service_Gcon_Platform_Platform::cleanForumReply($arrInput);
    }
    
    public function replyUserMsg($arrInput) {
        return Service_Gcon_Platform_Platform::replyUserMsg($arrInput);
    }
    
    public function sendMsgToUser($arrInput) {
        return Service_Gcon_Platform_Platform::sendMsgToUser($arrInput);
    }

	//qrcode service
	public function getQrcodeUrl($arrInput) {
        return Service_Gcon_Platform_Platform::getQrcodeUrl($arrInput);
    }
	
	//dwz service
	public function getBdDwz($arrInput) {
        return Service_Gcon_Platform_Platform::getBdDwz($arrInput);
    }
    public function delForumManager($arrInput) {
        return Service_Gcon_Fourth_Manager::delForumManager($arrInput);
    }
    
    public function delForumFourthManager($arrInput) {
        return Service_Gcon_Fourth_Manager::delForumFourthManager($arrInput);
    }
    
    public function addFirm($arrInput){
    	return Service_Gcon_Agency_Verify::addFirm($arrInput);
    }
    /** by xuruiqi */
    public function mgetMonthCheckStatusByFid($arrInput) {
            return Service_Gcon_Check_Forum::mgetMonthCheckStatusByFid($arrInput);
    }
    /** by xuruiqi */
    public function getForumInfoByAgencyCreateUid($arrInput) {
            return Service_Gcon_Agency_Forum::getForumInfoByAgencyCreateUid($arrInput);
    }
    /** by xuruiqi */
    public function getForumInfoByCreateUserId($arrInput) {
            return Service_Gcon_Agency_Forum::getForumInfoByCreateUserId($arrInput);
    }
    /** by xuruiqi */
    public function getRelationInfoByAgencyCreateUid($arrInput) {
            return Service_Gcon_Agency_Forum::getRelationInfoByAgencyCreateUid($arrInput);
    }
    /** by xuruiqi */
    public function getRelationInfoByCreateUserId($arrInput) {
            return Service_Gcon_Agency_Forum::getRelationInfoByCreateUserId($arrInput);
    }
    /** by xuruiqi */
    public function getAgency($arrInput) {
            return Service_Gcon_Agency_Forum::getAgency($arrInput);
    }
    /** by xuruiqi */
    public function getRelationCountByAgencyCreateUid($arrInput) {
            return Service_Gcon_Agency_Forum::getRelationCountByAgencyCreateUid($arrInput);
    }
    /** by xuruiqi */
    public function setAgencyDescByAgencyCreateUid($arrInput) {
            return Service_Gcon_Agency_Verify::setAgencyDescByAgencyCreateUid($arrInput);
    }
    /** by xuruiqi */
    public function setFirmDescByCreateUserId($arrInput) {
            return Service_Gcon_Agency_Verify::setFirmDescByCreateUserId($arrInput);
    }
    /** by xuruiqi */
    public function removeCache($arrInput) {
            return Service_Gcon_Agency_Verify::removeCache($arrInput);
    }
	
    public function getFirm($arrInput){
    	return Service_Gcon_Agency_Verify::getFirm($arrInput);
    }
    
    public function getAgencyNames($arrInput){
    	return Service_Gcon_Agency_Verify::getAgencyNames($arrInput);
    }
    
    public function getAgencyInfoByAgencyid($arrInput){
    	return Service_Gcon_Agency_Verify::getAgencyInfoByAgencyid($arrInput);
    }

    public function getFirmRelationInfo($arrInput){
        return Service_Gcon_Agency_Verify::getFirmRelationInfo($arrInput);
    }

    public function setFirmStatus($arrInput){
        return Service_Gcon_Agency_Verify::setFirmStatus($arrInput);
    }

    public function getAgencyInfo($arrInput){
        return Service_Gcon_Agency_Verify::getAgencyInfo($arrInput);
    }

    public function getAgencyForumName($arrInput){
        return Service_Gcon_Agency_Verify::getAgencyForumName($arrInput);
    }

    public function setAgencyInfoCommit($arrInput){
        return Service_Gcon_Agency_Verify::setAgencyInfoCommit($arrInput);
    }

    public function addFirmRelation($arrInput){
            return Service_Gcon_Agency_Verify::addFirmRelation($arrInput);
    }

    public function changeFirmStatus($arrInput){
        return Service_Gcon_Agency_Verify::changeFirmStatus($arrInput);
    }

    public function checkForumIsGcon($arrInput) {
        return Service_Gcon_Check_Forum::checkForumIsGcon($arrInput);
    }
    
    public function getActOrderData($arrInput) {
        return Service_Gcon_Base_Base::getActOrderData($arrInput);
    }
    
    //通用查询接口，where, order by, limit，只限mis或内部脚本使用
    public function selectFull($arrInput){
        return Service_Gcon_Agency_Verify::selectFull($arrInput);
    }

    //获取代运公司列表
    public function getAgencyList($arrInput){
        return Service_Gcon_Agency_Verify::getAgencyList($arrInput);
    }
    
    public function getForumRssMenu($arrInput) {
        return Service_Gcon_Platform_Platform::getForumRssMenu($arrInput);
    }

    public function removeForumRssCache($arrInput) {
        return Service_Gcon_Platform_Platform::removeForumRssCache($arrInput);
    }
	public function removeQrcodeCache($arrInput) {
        return Service_Gcon_Platform_Platform::removeQrcodeCache($arrInput);
    }
	public function removeDwzCache($arrInput) {
        return Service_Gcon_Platform_Platform::removeDwzCache($arrInput);
    }
    //客户端点击请求，发送消息接口
    public function getMenuContent($arrInput) {
        return Service_Gcon_Platform_Platform::getMenuContent($arrInput);
    }

    public function getForumMenu($arrInput) {
        return Service_Gcon_Platform_Platform::getForumMenu($arrInput);
    }

    public function getForumDayLight($arrInput) {
        return Service_Gcon_Check_Forum::getForumDayLight($arrInput);
    }
    //add by cuishichao start
    //批量添加渠道号
    public function mAddChannel($arrInput) {
        return Service_Gcon_Channel_Channel::mAddChannel($arrInput);
    }
    //删除渠道号
    public function delChannelById($arrInput){
        return Service_Gcon_Channel_Channel::delChannelById($arrInput);
    }
    //查询渠道号接口
    public function getChannelList($arrInput){
       return Service_Gcon_Channel_Channel::getChannelList($arrInput);
    }
    //传入渠道号列表获取重复的渠道号
    public function getOnlineRepeatChannel($arrInput){
       return Service_Gcon_Channel_Channel::getOnlineRepeatChannel($arrInput);
    }
    //获取置顶公司信息
    public function getTopAgencyInfo($arrInput){
       return Service_Gcon_Agency_Verify::getTopAgencyInfo($arrInput);
    }
    public function mSetTopAgencyWeight($arrInput){
       return Service_Gcon_Agency_Verify::mSetTopAgencyWeight($arrInput);
    }
    public function getAllAgencyListByWeight($arrInput){
       return Service_Gcon_Agency_Verify::getAllAgencyListByWeight($arrInput);
    }
    //add by cuishichao end
    //获取代运公司信息
    public function getAgencyByCreateUid($arrInput) {
        return Service_Gcon_Agency_Verify::getAgencyByCreateUid($arrInput);
    }
    //获取考试题
    public function getAgencyExams($arrInput) {
        return Service_Gcon_Agency_Verify::getAgencyExams($arrInput);
    }
    //提交代运公司认证信息
    public function addAgency($arrInput) {
        return Service_Gcon_Agency_Verify::addAgency($arrInput);
    }
    //批量获取考试答案
    public function mgetExamsAnswer($arrInput) {
        return Service_Gcon_Agency_Verify::mgetExamsAnswer($arrInput);
    }
    public function getAgencyIDByAgencyName($arrInput) {
        return Service_Gcon_Agency_Verify::getAgencyIDByAgencyName($arrInput);
    }
    public function mGetAgencyIDByForumID($arrInput) {
        return Service_Gcon_Agency_Verify::mGetAgencyIDByForumID($arrInput);
    }
    public function mGetAgencyNameByAgencyID($arrInput) {
        return Service_Gcon_Agency_Verify::mGetAgencyNameByAgencyID($arrInput);
    }
    public function mGetAgencyNameByUserID($arrInput) {
        return Service_Gcon_Agency_Verify::mGetAgencyNameByUserID($arrInput);
    }
    //获取gyr吧列表
    public function getGyrForumList($arrInput) {
        return Service_Gcon_Check_Forum::getGyrForumList($arrInput);
    }
    /***************舆情监控**************/
    public function addMonitorScope($arrInput) {
        return Service_Gcon_Monitor_Monitor::addMonitorScope($arrInput);
    }
    public function multiAddMonitorScopes($arrInput) {
        return Service_Gcon_Monitor_Monitor::multiAddMonitorScopes($arrInput);
    }
    public function getMonitorScopesByLimit($arrInput) {
        return Service_Gcon_Monitor_Monitor::getMonitorScopesByLimit($arrInput);
    }
    public function getMonitorScopesByFid($arrInput) {
        return Service_Gcon_Monitor_Monitor::getMonitorScopesByFid($arrInput);
    }
    public function setMonitorScopeStatus($arrInput) {
        return Service_Gcon_Monitor_Monitor::setMonitorScopeStatus($arrInput);
    }
    public function multiSetMonitorScopeStatus($arrInput) {
        return Service_Gcon_Monitor_Monitor::multiSetMonitorScopeStatus($arrInput);
    }
    public function getMonitorDirsByLimit($arrInput) {
        return Service_Gcon_Monitor_Monitor::getMonitorDirsByLimit($arrInput);
    }
    public function editMornitorDirInfo($arrInput) {
        return Service_Gcon_Monitor_Monitor::editMornitorDirInfo($arrInput);
    }
    public function editMornitorDirForumInfo($arrInput) {
        return Service_Gcon_Monitor_Monitor::editMornitorDirForumInfo($arrInput);
    }
    public function deleteMonitorDirById($arrInput) {
        return Service_Gcon_Monitor_Monitor::deleteMonitorDirById($arrInput);
    }
    public function getMonitorDirByLevel($arrInput) {
        return Service_Gcon_Monitor_Monitor::getMonitorDirByLevel($arrInput);
    }
    public function deleteMonitorScopeByFid($arrInput) {
        return Service_Gcon_Monitor_Monitor::deleteMonitorScopeByFid($arrInput);
    }
    public function getMonitorDirForumsByDirId($arrInput) {
        return Service_Gcon_Monitor_Monitor::getMonitorDirForumsByDirId($arrInput);
    }
    public function deleteMonitorScopeById($arrInput) {
        return Service_Gcon_Monitor_Monitor::deleteMonitorScopeById($arrInput);
    }
    //代运监控获取后台记录
    public function getOpRecord($arrInput) {
        return Service_Gcon_Agency_Forum::getOpRecord($arrInput);
    }
    public function getMonitorAgencyList($arrInput) {
        return Service_Gcon_Agency_Forum::getMonitorAgencyList($arrInput);
    }
    /***************小微官方吧代理**************/
    public function editMicroAgency($arrInput) {
        return Service_Gcon_Agency_Microgcon::editMicroAgency($arrInput);
    }
    public function getMicroAgencyByLimit($arrInput) {
        return Service_Gcon_Agency_Microgcon::getMicroAgencyByLimit($arrInput);
    }
    public function setMicroAgencyStatus($arrInput) {
        return Service_Gcon_Agency_Microgcon::setMicroAgencyStatus($arrInput);
    }
    public function checkValidMicroAgency($arrInput) {
        return Service_Gcon_Agency_Microgcon::checkValidMicroAgency($arrInput);
    }
    public function checkValidMicroGconForum($arrInput) {
        return Service_Gcon_Agency_Microgcon::checkValidMicroGconForum($arrInput);
    }
    public function getMicroForumsByAgencyUid($arrInput) {
        return Service_Gcon_Agency_Microgcon::getMicroForumsByAgencyUid($arrInput);
    }
    public function getMicroForumsByLimit($arrInput) {
        return Service_Gcon_Agency_Microgcon::getMicroForumsByLimit($arrInput);
    }
    public function addAuditingMicroForum($arrInput) {
        return Service_Gcon_Agency_Microgcon::addAuditingMicroForum($arrInput);
    }
    public function getAuditingMircoForumsByLimit($arrInput) {
        return Service_Gcon_Agency_Microgcon::getAuditingMircoForumsByLimit($arrInput);
    }
	public function getMicroManagerAuthorityById($arrInput) {
		return Service_Gcon_Agency_Microgcon::getMicroManagerAuthorityById($arrInput);
    }
	public function getAuditingMircoForumsById($arrInput) {
		return Service_Gcon_Agency_Microgcon::getAuditingMircoForumsById($arrInput);
    }
    public function onlineMircoGconForum($arrInput) {
        return Service_Gcon_Agency_Microgcon::onlineMircoGconForum($arrInput);
    }
    public function offlineMircoGconForum($arrInput) {
        return Service_Gcon_Agency_Microgcon::offlineMircoGconForum($arrInput);
    }
    public function reOnlineMircoGconForum($arrInput) {
        return Service_Gcon_Agency_Microgcon::reOnlineMircoGconForum($arrInput);
    }
    public function offlineMircoGconIdentify($arrInput) {
        return Service_Gcon_Agency_Microgcon::offlineMircoGconIdentify($arrInput);
    }
    public function getMicroAgencyById($arrInput) {
        return Service_Gcon_Agency_Microgcon::getMicroAgencyById($arrInput);
    }
    //addConsumeInfoCollection service
    public function addConsumeInfoCollection($arrInput) {
        return Service_Gcon_Platform_Platform::addConsumeInfoCollection($arrInput);
    }
    //getConsumeInfoCollection service
    public function getConsumeInfoCollection($arrInput) {
        return Service_Gcon_Platform_Platform::getConsumeInfoCollection($arrInput);
    }
    //商业皮肤 start
    public static function getForumSkinByCond($arrInput) {	
        return Service_Gcon_Skin_Skin::getForumSkinByCond($arrInput);
    }
    public function getForumSkinDetail($arrInput) {
        return Service_Gcon_Skin_Skin::getForumSkinDetail($arrInput);
    }
    public static function insertForumSkin($arrInput) {	
        return Service_Gcon_Skin_Skin::insertForumSkin($arrInput);
    }
    
    //insert
    public static function insertForumSkinData($arrInput) {	
        return Service_Gcon_Skin_Skin::insertForumSkinData($arrInput);
    }
    
    
    public static function updateForumSkin($arrInput) {	
        return Service_Gcon_Skin_Skin::updateForumSkin($arrInput);
    }
    
    //update
    public static function updateForumSkinData($arrInput) {	
        return Service_Gcon_Skin_Skin::updateForumSkinData($arrInput);
    }
    
    public static function getForumSkinByTimes($arrInput) {
        return Service_Gcon_Skin_Skin::getForumSkinByTimes($arrInput);
    }
    
    //getForumSkinByTimesAndPutType
    public static function getForumSkinByTimesAndPutType($arrInput) {
        return Service_Gcon_Skin_Skin::getForumSkinByTimesAndPutType($arrInput);
    }
    
    public static function getOnlineForumSkinByFname($arrInput) {
        return Service_Gcon_Skin_Skin::getOnlineForumSkinByFname($arrInput);
    }
    public static function getForumSkinByTaskId($arrInput) {
        return Service_Gcon_Skin_Skin::getForumSkinByTaskId($arrInput);
    }
    public function insertSkinRelation($arrInput) {
        return Service_Gcon_Skin_Skin::insertSkinRelation($arrInput);
    }
    public function delSkinRelation($arrInput) {
        return Service_Gcon_Skin_Skin::delSkinRelation($arrInput);
    }
    public function replaceSkinRelation($arrInput) {
        return Service_Gcon_Skin_Skin::replaceSkinRelation($arrInput);
    }
    public function getForumSkin($arrInput) {
        return Service_Gcon_Skin_Skin::getForumSkin($arrInput);
    }
    public function delForumSkinCache($arrInput) {
        return Service_Gcon_Skin_Skin::delForumSkinCache($arrInput);
    }
    public function getSkinDataByTaskId($arrInput) {
        return Service_Gcon_Skin_Skin::getSkinDataByTaskId($arrInput);
    }
    public function getSkinDataByDate($arrInput) {
        return Service_Gcon_Skin_Skin::getSkinDataByDate($arrInput);
    }
    public function getAllDataByTaskIds($arrInput) {
        return Service_Gcon_Skin_Skin::getAllDataByTaskIds($arrInput);
    }
    public function getSkinDataList($arrInput) {
        return Service_Gcon_Skin_Skin::getSkinDataList($arrInput);
    }

    //商业皮肤 end
    public function updateAgencyForumCount($arrInput) {
        return Service_Gcon_Agency_Verify::updateAgencyForumCount($arrInput);
    }
}

