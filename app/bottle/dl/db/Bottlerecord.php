<?php
/**
 * User: lihuan08
 * Date: 2017.02.09
 */

class Dl_Db_Bottlerecord extends Dl_Db_Model{
    protected static $_db_table = 'throw_bottle_record';
    protected static $_db_name = 'forum_bottle';
    private static $_arrDB;
    private static $_instance;
    public static $arrField = array(
        'id',
        'thread_id',
        'user_id',
        'status',
        'type',
        'update_time',
        'create_time',
    );

    /**
     * @brief :
     * @param : $arrInput
     * @return : $arrOutput
     **/
    public static function getModel($db_name = null,$db_table = null){
        if (!$db_name){
            $db_name = self::$_db_name;
        }

        if (!$db_table){
            $db_table = self::$_db_table;
        }

        $arrKey = $db_name.$db_table;

        if(isset(self::$_arrDB[$arrKey])){
            return self::$_arrDB[$arrKey];
        }else{
            $class_name = __CLASS__;
            self::$_arrDB[$arrKey] = new $class_name($db_table,$db_name);
            return self::$_arrDB[$arrKey];
        }
    }

    /**
     * @brief :
     * @param : $arrInput
     * @return : $arrOutput
     **/
    protected function _after_select() {
        return true;
    }

    /**
     * @brief :
     * @param : $arrInput
     * @return : $arrOutput
     **/
    protected function _before_select() {
        return true;
    }

    /**
     * @brief :
     * @param : $arrInput
     * @return : $arrOutput
     **/
    public static function getAffectedRows($arrInput = array()){
        if(!isset($arrInput['db_name']) || empty($arrInput['db_name'])){
            $arrInput['db_name'] = self::$_db_name;
        }
        if(!isset($arrInput['db_table']) || empty($arrInput['db_table'])){
            $arrInput['db_table'] = self::$_db_table;
        }
        self::$_instance = self::getModel($arrInput['db_name'],$arrInput['db_table']);
        return self::$_instance->baseGetAffectedRows($arrInput);
    }

    /**
     * @brief :
     * @param : $arrInput
     * @return : $arrOutput
     **/
    public static function startTransaction($arrInput = array()){
        if(!isset($arrInput['db_name']) || empty($arrInput['db_name'])){
            $arrInput['db_name'] = self::$_db_name;
        }
        if(!isset($arrInput['db_table']) || empty($arrInput['db_table'])){
            $arrInput['db_table'] = self::$_db_table;
        }
        self::$_instance = self::getModel($arrInput['db_name'],$arrInput['db_table']);
        return self::$_instance->baseStartTransaction($arrInput);
    }

    /**
     * @brief :
     * @param : $arrInput
     * @return : $arrOutput
     **/
    public static function commit($arrInput = array()){
        if(!isset($arrInput['db_name']) || empty($arrInput['db_name'])){
            $arrInput['db_name'] = self::$_db_name;
        }
        if(!isset($arrInput['db_table']) || empty($arrInput['db_table'])){
            $arrInput['db_table'] = self::$_db_table;
        }
        self::$_instance = self::getModel($arrInput['db_name'],$arrInput['db_table']);
        return self::$_instance->baseCommit($arrInput);
    }

    /**
     * @brief :
     * @param : $arrInput
     * @return : $arrOutput
     **/
    public static function rollback($arrInput = array()){
        if(!isset($arrInput['db_name']) || empty($arrInput['db_name'])){
            $arrInput['db_name'] = self::$_db_name;
        }
        if(!isset($arrInput['db_table']) || empty($arrInput['db_table'])){
            $arrInput['db_table'] = self::$_db_table;
        }
        self::$_instance = self::getModel($arrInput['db_name'],$arrInput['db_table']);
        return self::$_instance->baseRollBack($arrInput);
    }

    /**
     * @brief :
     * @param : $arrInput
     * @return : $arrOutput
     **/
    public static function select($arrInput){
        if(!isset($arrInput['db_name']) || empty($arrInput['db_name'])){
            $arrInput['db_name'] = self::$_db_name;
        }
        if(!isset($arrInput['db_table']) || empty($arrInput['db_table'])){
            $arrInput['db_table'] = self::$_db_table;
        }
        self::$_instance = self::getModel($arrInput['db_name'],$arrInput['db_table']);
        if(!isset($arrInput['field']) || empty($arrInput['field'])){
            $arrInput['field'] = self::$arrField;
        }
        $arrOutput = self::$_instance->baseSelect($arrInput);
        return $arrOutput;
    }

    /**
     * @brief :
     * @param : $arrInput
     * @return : $arrOutput
     **/
    public static function selectTotal($arrInput){
        if (!isset($arrInput['db_name']) || empty($arrInput['db_name'])) {
            $arrInput['db_name'] = self::$_db_name;
        }
        if (!isset($arrInput['db_table']) || empty($arrInput['db_table'])) {
            $arrInput['db_table'] = self::$_db_table;
        }
        self::$_instance = self::getModel($arrInput['db_name'],$arrInput['db_table']);
        if (!isset($arrInput['field']) || empty($arrInput['field'])) {
            $arrInput['field'] = array("count(1)");
        }
        $arrOutput = self::$_instance->baseSelect($arrInput);
        if (isset($arrOutput['data'][0]['count(1)'])) {
            $arrOutput['data'] = intval($arrOutput['data'][0]['count(1)']);
        } else {
            $arrOutput['data'] = 0;
        }
        return $arrOutput;
    }

    /**
     * @brief :
     * @param : $arrInput
     * @return : $arrOutput
     **/
    public static function update($arrInput){
        if(!isset($arrInput['db_name']) || empty($arrInput['db_name'])){
            $arrInput['db_name'] = self::$_db_name;
        }
        if(!isset($arrInput['db_table']) || empty($arrInput['db_table'])){
            $arrInput['db_table'] = self::$_db_table;
        }
        self::$_instance = self::getModel($arrInput['db_name'],$arrInput['db_table']);

        $ret = self::$_instance->baseUpdate($arrInput);
        return $ret;
    }

    /**
     * @brief :
     * @param : $arrInput
     * @return : $arrOutput
     **/
    public static function insert($arrInput){
        if(!isset($arrInput['db_name']) || empty($arrInput['db_name'])){
            $arrInput['db_name'] = self::$_db_name;
        }
        if(!isset($arrInput['db_table']) || empty($arrInput['db_table'])){
            $arrInput['db_table'] = self::$_db_table;
        }
        self::$_instance = self::getModel($arrInput['db_name'],$arrInput['db_table']);
        return self::$_instance->baseInsert($arrInput);
    }

     /**
     * @brief :
     * @param : $arrInput
     * @return : $arrOutput
     **/
    public static function multiInsert($arrInput){
        if(!isset($arrInput['db_name']) || empty($arrInput['db_name'])){
            $arrInput['db_name'] = self::$_db_name;
        }
        if(!isset($arrInput['db_table']) || empty($arrInput['db_table'])){
            $arrInput['db_table'] = self::$_db_table;
        }
        self::$_instance = self::getModel($arrInput['db_name'],$arrInput['db_table']);
        return self::$_instance->baseMultiInsert($arrInput);
    }

    /**
     * @brief :
     * @param : $arrInput
     * @return : $arrOutput
     **/
    public static function delete($arrInput){
        if(!isset($arrInput['db_name']) || empty($arrInput['db_name'])){
            $arrInput['db_name'] = self::$_db_name;
        }
        if(!isset($arrInput['db_table']) || empty($arrInput['db_table'])){
            $arrInput['db_table'] = self::$_db_table;
        }
        self::$_instance = self::getModel($arrInput['db_name'],$arrInput['db_table']);
        $ret = self::$_instance->baseDelete($arrInput);
        return $ret;
    }

}
