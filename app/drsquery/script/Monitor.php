<?php
/***************************************************************************
 *
* Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
*
**************************************************************************/


/**
 * @file monitor.php
* <AUTHOR>
* @date 2014/05/20 16:12:20
* @brief
*
**/




//mkdir("../../../log/drsquery");

mkdir("../../../log/drsquery");
define ('LOG', 'log');
Bingo_Log::init(array(
          LOG => array(
                  'file'  => '../../../log/drsquery/script_monitor.log',
                 // 'file'  => dirname(__FILE_).'/script_getstatus.log',
                  'level' => 0x02|0x04,
          ),
), LOG);
require_once '../lib/SendMail.php';
require_once '../lib/SendSmsp.php';
class Script_Monitor{
	private static $delay  = array(//延时时间，单位分钟
			'jx'=> 3,//
			'tc'=> 3,
			'nj'=> 3,);
	private static $dbServicename=array(
			'jx'=>'db_forum_drsjx',
			'tc'=>'db_forum_drstc',
			'nj'=>'db_forum_drsnj',);
	private static $tag = null;
	protected static $ABSOLUTE_CHANGE_VALUE = array(
			'COST' => 5,
	);
	protected static $DEFAULT_THRESHOLD = array(
			'HTTP_OTHER' => 50,
			'HTTP_499' => 50,);//默认报警阀值，高过报警阀值即报警
	protected static $QPS_THRESHOLD = 100;
	protected static $SPECIAL_THRESHOLD = array(
			/*'jx:post:queryThreadStoreType:unknow:QPS'  => 130,
			'tc:post:queryThreadStoreType:unknow:QPS'  => 130,
			'nj:post:queryThreadStoreType:unknow:QPS'  => 130,*/
	);//特殊阀值，key 为 tag:service:method:call_from:alarm_item
	protected static $TABLE_ALARM_INFO = 'drs_alarm';
	protected static $TABLE_DRS_VALUETABLE = 'drs_valuetable';
	protected static $TABLE_MERGEVALUE = 'drs_mergevalue';
	protected static $TIME_INTERVAL = 10;
	protected static $TIME_ALARM = 120;
	protected static $BEFORE_DAY_INTERVAL = 600;
	
	protected static $ALARM_ITEM = array('COST','HTTP_OTHER','HTTP_499',);
	protected static $CHECK_AFTER_NUM = 3;//检查的后面的点的数量
	protected static $CHECK_BEFORE_NUM = 5;//检查前面的点的数量
	protected static $DEFAULT_GROWTH_TIME =  array(
			'COST'=> 2.5,
			'HTTP_OTHER' => 2.5,
			'HTTP_499' => 2.5,);//增长倍数，超过增长倍数才报警
	protected static $DEFAULT_DECLINE_TIME = 0.8;//下降倍数，后面如果到下降倍数不会报警
	protected static $_db = null;
	protected static $_smsp = null;
	protected static $_mail = null;
	protected static $messageTo = array(//短信接收号码
				'15801652576',//仝晔
				'15801552378',//郭栋
				//'13121886812',//王桐云
				'13488811475',//陈建森
	);
	protected static $mailTo = array(
				'<EMAIL>',//仝晔 
				'<EMAIL>',//郭栋
				//'<EMAIL>',//王桐云
				'<EMAIL>',//陈建森
				//'<EMAIL>',//arch
				//'<EMAIL>',
				'<EMAIL>',
				'<EMAIL>',
				'<EMAIL>',
				'<EMAIL>',
	);
	protected static $mailFrom = "<EMAIL>";
	protected static $checkTime = 0;
	public static function setTag($para){
		self::$tag = $para;
	}
	public static function run(){
		if(self::$checkTime === 0){
			$check_minute = time() - 60*self::$delay[self::$tag];
			self::$checkTime = $check_minute - $check_minute % 60 ;
		}else{
			self::$checkTime += self::$TIME_INTERVAL;
		}
		$hashTab = self::getHashtable();//存放servie,call,method的名字
		if($hashTab === false)
			return false;
		
		$para = array(
				'start_time'  => self::$checkTime - self::$TIME_INTERVAL + 1,
				'end_time' => self::$checkTime,);
		$arryTest = self::getDataPeriodOfTime($para);
		if($arryTest === false){
			Bingo_Log::warning(__FUNCTION__ . __LINE__."error");
			return false;
		}
		if(count($arryTest) === 0){
			return ;
		}
		$timeTest = $arryTest[0]['timestamp'];
		//Bingo_Log::warning("timeTest".$timeTest);
		$arryTest = self::arrayToHash($arryTest);
		
		$para = array(
				'start_time'  => $timeTest - self::$CHECK_BEFORE_NUM * self::$TIME_INTERVAL,
				'end_time' => $timeTest - self::$TIME_INTERVAL,
				'timestamp_order' => 'desc' ,);
		$arryBefore = self::getDataPeriodOfTime($para);
		if($arryBefore === false){
			Bingo_Log::warning(__FUNCTION__ . __LINE__."error");
			return false;
		}
		$arryBefore = self::arrayToHashEx($arryBefore);
		
		$para = array(
				'start_time'  => $timeTest + self::$TIME_INTERVAL,
				'end_time' => $timeTest + self::$TIME_INTERVAL * self::$CHECK_AFTER_NUM,
				'timestamp_order' => 'asc');
		$arryAfter = self::getDataPeriodOfTime($para);
		if($arryAfter === false){
			Bingo_Log::warning(__FUNCTION__ . __LINE__."error");
			return false;
		}
		$arryAfter = self::arrayToHashEx($arryAfter);
			
		
		$arryBeforeDay = self::getDataBeforeDay($timeTest);
		if($arryBeforeDay === false){
			Bingo_Log::warning(__FUNCTION__ . __LINE__."error");
			return false;
		}
		$arryBeforeDay = self::arrayToHash($arryBeforeDay);
		
		$arryAlarm = self::getAlarmData($timeTest - self::$TIME_ALARM, $timeTest-self::$TIME_INTERVAL);
		if($arryAlarm === false){
			Bingo_Log::warning(__FUNCTION__ . __LINE__."error");
			return false;
		}
		$arryAlarm = self::arrayToHashAlarm($arryAlarm);
		$excCon = self::getExecConditon($hashTab, $arryAfter, $arryTest, $arryBefore, $arryBeforeDay);
		self::sendInfo($excCon,$arryAlarm);
	}
	public static function sendInfo($excCon,$arryAlarm){
		/*if(self::$_smsp === null){
			self::$_smsp =  new Lib_SendSmsp();
		}
		if(self::$_smsp === null){
			Bingo_Log::warning("init smsp error");
			return false;
		}
		self::$_smsp->setTo(self::$messageTo);*/
		$arryMail = array();
		foreach($excCon as $key => $value){
			$alarmKey = $value['service'].":".$value['method'].":".$value['call_from'].":".$value['alarm_item'];
			if(count($arryAlarm[$alarmKey])===0){
				$value['send_mail'] = 1;
				self::insertAlarm($key,$value);
				$message = "容灾监控报警 ：%s : %s : %s : %s : %s 从 %.2f (%s) 增长到  %.2f (%s) , 增长了%.2f倍 ,昨天同时段为 %.2f ";
				$grow_rate = ($value['before_val']==0) ? 1000: $value['alarm_val']/$value['before_val'];
				$before_time = date("Y-m-d H:i:s",$value['before_time']);
				$alarm_time = date("Y-m-d H:i:s",$value['alarm_time']);
				$message = sprintf($message, self::$tag, $value['service'], $value['method'], $value['call_from'], $value['alarm_item']
						,$value['before_val'], $before_time, $value['alarm_val'], $alarm_time, $grow_rate ,$value['day_before_val']);
				//self::$_smsp->setMes($message);
				//$reM = self::$_smsp->send();
				$split = explode('_', $value['service']);//ui为前缀的合并到一起
				$arryMail[$split[0]][] = $value;
				//Bingo_Log::warning("message".$message);
				
			}else{
				$value['send_mail'] = 0;
				self::insertAlarm($key,$value);
			}
			//Bingo_Log::warning("resM".$reM);
		}
		self::sendMailbyService($arryMail);
	}
	private static function sendMailbyService($arryMail){
		if(self::$_mail === null){
			self::$_mail =  new Lib_SendMail();
		}
		if(self::$_mail === null){
			Bingo_Log::warning("init mail error");
			return false;
		}
		self::$_mail->setTo(self::$mailTo);
		self::$_mail->setCharset("utf-8");
		self::$_mail->setFrom(self::$mailFrom);
		foreach ($arryMail as $serviceK => $serviceV){
			self::$_mail->setSubject("容灾监控报警");	
			$totalMes = "容灾监控报警  \n\n" ;
			foreach ($serviceV as $value){
				$singleMes = "";
				if( isset(self::$DEFAULT_THRESHOLD[$value['alarm_item']]) ){
					$singleMes = "%s : %s : %s : %s : %s 达到  %.2f (%s) , 昨天同时段为 %.2f\n\n";
					$alarm_time = date("Y-m-d H:i:s",$value['alarm_time']);
					$singleMes = sprintf($singleMes, self::$tag, $value['service'], $value['method'], $value['call_from'], $value['alarm_item']
							,$value['alarm_val'], $alarm_time, $value['day_before_val']);
				}else{
					$singleMes = "%s : %s : %s : %s : %s 从 %.2f (%s) 增长到  %.2f (%s) , 增长了%.2f倍 ,昨天同时段为 %.2f\n\n";
					$grow_rate = ($value['before_val']==0) ? 1000: $value['alarm_val']/$value['before_val'];
					$before_time = date("Y-m-d H:i:s",$value['before_time']);
					$alarm_time = date("Y-m-d H:i:s",$value['alarm_time']);
					$singleMes = sprintf($singleMes, self::$tag, $value['service'], $value['method'], $value['call_from'], $value['alarm_item']
							,$value['before_val'], $before_time, $value['alarm_val'], $alarm_time, $grow_rate ,$value['day_before_val']);
				}
				$totalMes .= $singleMes;
			}
			self::$_mail->setText($totalMes);
			$reM = self::$_mail->send();
		}
	}
	private static function insertAlarm($key, $value){
		$columns = " (service, method, call_from ,alarm_item , alarm_val, before_val, day_before_val, after_val, 
				alarm_time ,before_time ,day_before_time ,after_time ,send_mail)";	
		$sql = "insert into " .self::$TABLE_ALARM_INFO . $columns . " values ('%s','%s', '%s', '%s', %f, %f, %f, %f, %d, %d ,%d ,%d ,%d);";
		$sql = sprintf($sql, $value['service'], $value['method'], $value['call_from'], $value['alarm_item'],
				 $value['alarm_val'], $value['before_val'], $value['day_before_val'], $value['after_val'], 
				$value['alarm_time'], $value['before_time'], $value['day_before_time'], $value['after_time'],$value['send_mail']);
		//Bingo_Log::warning('sql'.$sql);
		$db = self::_getDB();
		if( null === $db ){
			Bingo_Log::warning('ERR[get db failed] IN['.__FUNCTION__.']');
			return false;
		}
		$re = $db->query($sql);
		if( $re === false ){
			Bingo_Log::warning('ERR[query db failed] IN['.__FUNCTION__.'] RES['.$db->errno().']['.$db->error().'] SQL['.$sql.']');
			return false ;
		}
	}
	public static function getExecConditon($hashTab,$arryAfter,$arryTest,$arryBefore,$arryBeforeDay){
		$excCon = array();
		foreach ($arryTest as $key => $value){
			foreach(self::$ALARM_ITEM as $item){
				/*$speThresholdKey = self::$tag .":" .$hashTab[$key]['strkey'] .":" .$item;
				$threshold = isset(self::$SPECIAL_THRESHOLD[$speThresholdKey]) ? self::$SPECIAL_THRESHOLD[$speThresholdKey] : self::$DEFAULT_THRESHOLD[$item];*/
				//Bingo_Log::warning("threshold" .$threshold);
				if($value[$item] == 0 && (!isset($arryBefore[$key]))){
					continue;
				}
				$beforeTimeStart = $arryTest[$key]['timestamp'] - self::$TIME_INTERVAL;
				$checkBeforeRe = self::checkBefore($beforeTimeStart, $arryBefore[$key], $key, $value, $item);
				if($checkBeforeRe['re'] ===false){
					continue;
				}
				if(!isset($arryBeforeDay[$key][$item])){//和昨天同时间段进行比较
					continue;
				}	
				//Bingo_Log::warning("yes2");
				if($arryBeforeDay[$key][$item] == 0 || 
						$value[$item] / $arryBeforeDay[$key][$item] < self::$DEFAULT_GROWTH_TIME[$item]){
					continue;
				}
				//Bingo_Log::warning("yes3");
				if((!isset($arryAfter[$key])) || 
						self::checkAfter($arryAfter[$key],$key,$value,$item) === false){
					continue;
				}
				//Bingo_Log::warning("yes4");
		        $k = $key. ":" . $item;
		        $after_time = $arryTest[$key]['timestamp'] + self::$CHECK_AFTER_NUM * self::$TIME_INTERVAL;  
				$v = array(
						'service' => $hashTab[$key]['service'],
						'method' => $hashTab[$key]['method'],
						'call_from' => $hashTab[$key]['call_from'],
						'alarm_item' => $item,
						'alarm_val'  => $value[$item],
						'alarm_time' => $value['timestamp'],
						'before_val' => $checkBeforeRe['beforePoint'][$item],
						'before_time' => $checkBeforeRe['beforePoint']['timestamp'],
						'day_before_val'=> $arryBeforeDay[$key][$item],
						'day_before_time' =>$arryBeforeDay[$key]['timestamp'],
						'after_val' => $arryAfter[$key][$after_time][$item],
						'after_time' => $arryAfter[$key][$after_time]['timestamp'],
					);
				$excCon[$k] = $v;
				/*Bingo_Log::warning('exc test'. $value[$item]);
				Bingo_Log::warning('exc after'.  $arryAfter[$key][$item]);
				Bingo_Log::warning('exc before'.  $arryBefore[$key][$item]);
				Bingo_Log::warning('exc beforeDay'.  $arryBeforeDay[$key][$item]);
				Bingo_Log::warning('grow_rate'. $v['grow_rate']);
				Bingo_Log::warning('room:service:method:call:item'.self::$tag.": " .$hashTab[$key]['strkey'].":" .$item);
				Bingo_Log::warning('timestamp:'. $value['timestamp']);
				Bingo_Log::warning('hashkey:'. $key);
				Bingo_Log::warning('before_timestamp:'. $arryBefore[$key]['timestamp']);*/
			}
		}
		return $excCon;
	}
	private static function checkBefore($startTime, $arryBefore, $key ,$value ,$item){
		$arrOutput = array();
		$timestamp = $startTime;
		$val  =  $value[$item];
		//Bingo_Log::warning(count($arryBefore));
		if(isset(self::$DEFAULT_THRESHOLD[$item])){
			if($value[$item] >= self::$DEFAULT_THRESHOLD[$item]
				&& isset($arryBefore[$timestamp]) && $arryBefore[$timestamp]['QPS']>= self::$QPS_THRESHOLD){
				$arryOutput['re'] = true;
				$arryOutput['beforePoint'] = $arryBefore[$timestamp];
				//Bingo_Log::warning("before return ".var_export($arryOutput,1));
				return $arryOutput;
			}
			else{
				return array('re' => false);
			}
		}
		$getGrows = false ;
		for($i = 0; $i<= self::$CHECK_BEFORE_NUM - 1 ; $i++){
			//Bingo_Log::warning("arryBefore" .var_export($arryBefore[$timestamp],1));
			if(!isset($arryBefore[$timestamp])){//没有值
				return array('re' => false);
			}if($arryBefore[$timestamp][$item] > $val){//必须是一直递增
				return array('re' => false);
			}else if($arryBefore[$timestamp][$item] == 0 || $value[$item] / $arryBefore[$timestamp][$item] >= self::$DEFAULT_GROWTH_TIME[$item]){
				$getGrows = true;
				break;
			}
			$val = $arryBefore[$timestamp][$item];
			$timestamp -= self::$TIME_INTERVAL;
			
		}
		$absChangeValue = isset(self::$ABSOLUTE_CHANGE_VALUE[$item])?self::$ABSOLUTE_CHANGE_VALUE[$item]:0;
		if($getGrows === true  && isset($arryBefore[$timestamp]['QPS'])
				&& $arryBefore[$timestamp]['QPS']>= self::$QPS_THRESHOLD && $value[$item] - $arryBefore[$timestamp][$item]>= $absChangeValue){
			$arryOutput['re'] = true;
			$arryOutput['beforePoint'] = $arryBefore[$timestamp];
			//Bingo_Log::warning("before return ".var_export($arryOutput,1));
			return $arryOutput;
		}else{ 
			return array('re' => false);
		}
		
	}
	private static function checkAfter($arryAfter ,$key, $value ,$item){
		/*if(count($arryAfter) != self::$CHECK_AFTER_NUM)
			return false;*/
		//Bingo_Log::warning("countAll ".count($arryAfter));
		/*Bingo_Log::warning("value ". var_export($value,1). "     item ". $item);
		Bingo_Log::warning("valuetime" .date('Ymd H:i:s'.$value['timestamp']));*/
		if(count($arryAfter) !== self::$CHECK_AFTER_NUM){
			return false;
		}
		foreach($arryAfter as $after){
			//Bingo_Log::warning("after   ". var_export($after,1));
			//Bingo_Log::warning("aftertime".date("Ymd H:i:s".$after[$key]['timestamp']));
			if( (!isset($after[$item])) || $after[$item] / $value[$item] < self::$DEFAULT_DECLINE_TIME){
				return false;
			}
		}
		return true;
	}
	private static function _getDB(){
		if(self::$_db){
			return self::$_db;
		}
		self::$_db = new Bd_DB();
		if(self::$_db == null){
			Bingo_Log::warning("new bd_db fail.");
			return null;
		}
		$r = self::$_db->ralConnect(self::$dbServicename[self::$tag]);
		if(!$r){
			Bingo_Log::warning("bd db ral connect fail.");
			self::$_db = null;
			return null;
		}
		self::$_db->charset('utf8');
		return self::$_db;
	}
	private static function getHashtable(){
		$sql='select strkey,hashkey from drs_hashtable;';
		$db = self::_getDB();
	  	if( null === $db ){
	  		Bingo_Log::warning('ERR[get db failed] IN['.__FUNCTION__.']');
			return false;
	  	}
	  	
	  	$resDb = $db->query($sql);
	  	if( false === $resDb ){
	  		Bingo_Log::warning('ERR[query db failed] IN['.__FUNCTION__.'] RES['.$db->errno().']['.$db->error().'] SQL['.$sql.']');
	  		return false;
	  	}
		$arr=array();
		foreach ($resDb as $value)
		{
			$info = explode(":", $value['strkey']);
			$value['service'] = $info[0];
			$value['method'] = $info[1];
			$value['call_from'] = $info[2];
			$arr[$value['hashkey']] = $value;
		}
		return $arr;
	}
	public static function arrayToHashAlarm($arryInput){
		$arryOutput = array();
		foreach ($arryInput as $v){
			$key = $v['service'].":".$v['method'].":".$v['call_from'].":".$v['alarm_item'];
			$arryOutput[$key][$v['alarm_time']] = $v;
		}
		//Bingo_Log::warning("hash_alarm   " .var_export($arryOutput,1));
		return $arryOutput;
	}
	private static function arrayToHash( $arryInput ){//建立hashmap
		$arryOutput = array();
		foreach ($arryInput as $v){
			$qps = $v['QPS'] / 100;
			$cost = ($v['QPS']!==0)?$v['COST'] / $v['QPS']:0;
			$v['COST'] = $cost;
			$v['QPS'] = $qps;
			$v['HTTP_499'] = $v['HTTP_499'] / 100;
			$v['HTTP_OTHER'] = $v['HTTP_OTHER'] / 100;
			$arryOutput [$v['hashkey'] ] = $v;
		}
		return $arryOutput;
	}
	private static function arrayToHashEx($arryInput){
		$arryOutput = array();
		foreach ($arryInput as $v){
			$qps = $v['QPS'] / 100;
			$cost = ($v['QPS']!==0)?$v['COST'] / $v['QPS']:0;
			$v['COST'] = $cost;
			$v['QPS'] = $qps;
			$v['HTTP_499'] = $v['HTTP_499'] / 100;
			$v['HTTP_OTHER'] = $v['HTTP_OTHER'] / 100;
			$arryOutput [$v['hashkey'] ][$v['timestamp']] = $v;
		}
		//Bingo_Log::warning('hashEx count' . count($arryOutput));
		return $arryOutput;
	}
	private static function getAlarmData($startTime, $endTime){
		$sql = "select * from %s where alarm_time >= %s and alarm_time <= %s and send_mail = 1 order by alarm_time";
		$sql = sprintf($sql ,self::$TABLE_ALARM_INFO, $startTime, $endTime);
		//Bingo_Log::warning("alarm sql   ".$sql);
		$db = self::_getDB();
		if( null === $db ){
			Bingo_Log::warning('ERR[get db failed] IN['.__FUNCTION__.']');
			return false;
		}
		$arryOutput = array();
		$arryOutput = $db->query($sql);
		if( $arryOutput === false ){
			Bingo_Log::warning('ERR[query db failed] IN['.__FUNCTION__.'] RES['.$db->errno().']['.$db->error().'] SQL['.$sql.']');
			return false ;
		}
		//Bingo_Log::warning("alarm num".count($arryOutput));
		return $arryOutput;
	}
	private static function getDataBeforeDay($timestamp){
		$timestamp = $timestamp - 24*60*60;
		$diff = $timestamp % self::$BEFORE_DAY_INTERVAL;
		$half = self::$BEFORE_DAY_INTERVAL / 2;
		$left = $timestamp - $diff;
		$dbTime = ($diff <= $half)? $left : $left + self::$BEFORE_DAY_INTERVAL;
		//$table_name = self::$TABLE_MERGEVALUE;
		$sql = "select timestamp, hashkey ,COST, QPS ,HTTP_200,HTTP_301,HTTP_302,HTTP_499,HTTP_500,HTTP_501,HTTP_502,HTTP_503,HTTP_504,HTTP_OTHER,RETCODE_NOT0,RETCODE_0  from  %s where timestamp = %d  order by timestamp;";
		$sql =  sprintf($sql,self::$TABLE_MERGEVALUE,$dbTime);
		//Bingo_Log::warning('sql'.$sql);
		$db = self::_getDB();
		if( null === $db ){
			Bingo_Log::warning('ERR[get db failed] IN['.__FUNCTION__.']');
			return false;
		}
		$arryOutput = array();
		$arryOutput = $db->query($sql);
		if( $arryOutput === false ){
			Bingo_Log::warning('ERR[query db failed] IN['.__FUNCTION__.'] RES['.$db->errno().']['.$db->error().'] SQL['.$sql.']');
			return false ;
		}
		//Bingo_Log::warning("beforeday:  " . date("Y-m-d H:i:s",$dbTime) . "  " .date("Y-m-d H:i:s",$dbTime)." " .count($arryOutput) );
		return $arryOutput;
	}
	private static function getDataPeriodOfTime($para){
		$start_time = $para['start_time'];
		$end_time = $para['end_time'];	
		$y = date("Y");
		$m = date("m");
		$d = date("d");
		$day_start =  mktime(0,0,0,$m,$d,$y);//当天开始时间，数据按天存放在不同的数据库表
		$arryOutput = array();
		$table_name = self::$TABLE_DRS_VALUETABLE . date("Ymd", $start_time);
		$sql = "select timestamp, hashkey ,COST, QPS ,HTTP_200,HTTP_301,HTTP_302,HTTP_499,HTTP_500,HTTP_501,HTTP_502,HTTP_503,HTTP_504,HTTP_OTHER,RETCODE_NOT0,RETCODE_0  from  %s where timestamp>=%d and timestamp<=%d order by timestamp";
		if(isset($para['timestamp_order'])){
			$order = " ".$para['timestamp_order'];
			$sql .= $order;		
		}
		$sql =  sprintf($sql.";",$table_name,$start_time,$end_time);
		//Bingo_Log::warning('sql'.$sql);	
		$db = self::_getDB();
		if( null === $db ){
			Bingo_Log::warning('ERR[get db failed] IN['.__FUNCTION__.']');
			return false;
		}
		$arryOutput = $db->query($sql);
		if( $arryOutput === false ){
			Bingo_Log::warning('ERR[query db failed] IN['.__FUNCTION__.'] RES['.$db->errno().']['.$db->error().'] SQL['.$sql.']');
			return false ;
		}
		if($day_start > $start_time){
 			$table_name = self::$TABLE_DRS_VALUETABLE . date("Ymd", $start_time);
 			$sql = "select timestamp, hashkey ,COST, QPS ,HTTP_200,HTTP_301,HTTP_302,HTTP_499,HTTP_500,HTTP_501,HTTP_502,HTTP_503,HTTP_504,HTTP_OTHER,RETCODE_NOT0,RETCODE_0  from  %s where timestamp>=%d and timestamp<=%d order by timestamp;";
 			$sql =  sprintf($sql, $table_name, $start_time, $end_time);
			$db = self::_getDB();
			if( null === $db ){
				Bingo_Log::warning('ERR[get db failed] IN['.__FUNCTION__.']');
				return false;
			}
			$arryOutput = $db->query($sql);
			if( $arryOutput === false ){
				Bingo_Log::warning('ERR[query db failed] IN['.__FUNCTION__.'] RES['.$db->errno().']['.$db->error().'] SQL['.$sql.']');
				return false ;
			}
 		}
		//Bingo_Log::warning(date("Y-m-d H:i:s",$start_time) ."  ".date("Y-m-d H:i:s",$end_time). "  ".self::$tag. count($arryOutput));
		return $arryOutput;
	}
}
?>
