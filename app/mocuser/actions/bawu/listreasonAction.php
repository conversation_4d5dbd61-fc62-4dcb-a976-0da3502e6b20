<?php
class listreasonAction extends CapiAction
{
	private $_result =  array();
	private $_bawulist =  array();
	const  dabazhu_type  =1;   //大吧主封禁类型
	const  xiaobazhu_type =0;  //小吧主封禁类型
    protected function _execute()
    {
        if (!CapiRequest::$bolLogin) {
            $this->_error(CapiErrno::USER_NOT_LOGIN,CapiError::USER_NOT_LOGIN,array(),false);
            return false;
        }
        $this->_input();
        $this->_process();
        $this->_build();
    }
    private function _input()
    {
        $this->_req['forum_id'] = Bingo_Http_Request::get('forum_id', 0);
        $this->_req['user_id'] = Bingo_Http_Request::get('user_id', 0);
        return true;
    }
    private function _process()
    {
        $this->_result = Tbapi_Core_Server::mocall('antispam', 'listreason', $this->_req);
        if(is_numeric($this->_req['user_id'])&& 0 != $this->_req['user_id']){
        	$this->_bawulist = Tbapi_Core_Server::mocall('forum', 'get_bawu_list', $this->_req);
        }
    }
    private function _build()
    {
        if (!isset($this->_result['error']['errno']) || Tieba_Errcode::ERR_SUCCESS != $this->_result['error']['errno']) {
            switch(intval($this->_result['error']['errno'])) {
                case Tieba_Errcode::ERR_MO_PARAM_INVALID:
                    $this->_error(CapiErrno::PARAM_NOT_ENOUGH,CapiError::PARAM_NOT_ENOUGH,array(),false);
                    break;
                case Tieba_Errcode::ERR_MO_INTERNAL_ERROR:
                    $this->_error(CapiErrno::INTERNAL_ERROR,CapiError::INTERNAL_ERROR);
                    break;
                default:
                    $this->_error($this->_result['error']['errno'],$this->_result['error']['errmsg'],array(),false);
            }
        }
        if (isset($this->_result['list_reason']['ret']) && !empty($this->_result['list_reason']['ret'])){
        	foreach ($this->_result['list_reason']['ret'] as $template){
        		$this->arrData['reason'][] = $template['template_text'];
        	}
        }else {
        	$this->arrData['reason'] = array();
        }
        
        
        $type = self::xiaobazhu_type; 
	    if($this->_bawulist === false){
	    	Bingo_Log::warning("perm_getBawuList_errno_false input");
	    }elseif($this->_bawulist['error']!=Tieba_Errcode::ERR_SUCCESS){
	    	Bingo_Log::warning("perm_getBawuList_errno_not_zero input[".serialize($this->_req)."] output[".serialize($this->_bawulist)."]");
	    }else{
	    	$is_manager = $this->checkForumManage($this->_bawulist['bawulist']['manager'],$this->_req['user_id']);
	    	if($is_manager){
	    		$type = self::dabazhu_type;
	    	}
	    }
	    $this->arrData['type'] = $type;
    }
    
    public  function checkForumManage($arr_manager,$user_id){
    	if(!is_array($arr_manager) || 0 == $user_id){
    		return false;
    	}
    	foreach ($arr_manager as $value){
    		if(isset($value['user']['user_id']) && $value['user']['user_id'] == $user_id){
    			return true;
    		}
    		continue;
    	}
    	return false;
    }
}
