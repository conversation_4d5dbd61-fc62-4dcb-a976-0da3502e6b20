<?php
class replymeAction extends CapiAction
{
    protected $bolCheckUserLogin = true;
    protected $bolNeedUserLogin = true;
    private $_req = array();
    private $_res = array();
    const VOICE_SHOW_TEXT = 2; // 显示语音为文本
    
    protected function _input()
    {
        $pn = intval(Bingo_Http_Request::get('pn', 1));
        if($pn <1){
        	$pn = 1;
        }
        $this->_req['rn'] = Conf::get('feed_rn', 20);
        $this->_req['pn'] = $pn;
        $this->_req['offset'] = ( $pn-1) * $this->_req['rn'];
        $this->_req['user_id'] = CapiRequest::$intUid;
        $this->_req['not_support_floor'] = !(FloorPbService::isClientVersionSupportFloorPb());
        $this->_req['user_portrait'] = CapiRequest::$strPortrait;
        $this->_req['show_voice'] = Conf::get('voice_style', 0);
        if ($this->_req['show_voice'] != 0){
            $this->_req['show_voice']= self::VOICE_SHOW_TEXT;// 显示语音文字 0 无 1 语音按钮 2 语音文字
        }
        return true;
    }
    
    protected function _execute()
    {
        $this->_input();
        
        $ret = $this->_process();
        if($ret === false){
        	return false;
        }
        
        $this->_build();
    }
    
    protected function _process()
    {
    	$arrOut = Tbapi_Core_Server::mocall('post', 'replyme', $this->_req);
    	$this->_res = $arrOut;
    	if ($this->_res === false) {
            //hongliang 调用service失败
            $this->_error(Tieba_Errcode::ERR_CLIENT_CALL_POST, CapiError::INTERNAL_ERROR);
            return false;
        } elseif (Tieba_Errcode::ERR_SUCCESS != $this->_res['error']['errno']) {
            //hongliang 透传mos错误号（ERR_MO_PARAM_INVALID和ERR_MO_REPLYME_FAIL）
            $this->_error($this->_res['error']['errno'], CapiError::PARAM_NOT_ENOUGH, array(), false);
            return false;
        }
		return true;
    }
    
    private function _build()
    {
        $arrRes = $this->_res;
        
        $arrPage = array(
    	    'current_page' => $this->_req['pn'],
            'has_more' => false,
            'has_prev' => false,
        );
        if($this->_req['pn'] > 1){
        	$arrPage['has_prev'] = true;
        }
        if(isset($arrRes['page']['has_more'])){
        	$arrPage['has_more'] = $arrRes['page']['has_more'];
        }
        $this->arrData['page'] = $arrPage;
        
        $this->arrData['reply_list'] = array();
        if (!empty($arrRes['reply_list'])) {
            $this->arrData['reply_list'] = $arrRes['reply_list'];
        }
        
        $arrMessage = array(
            'fans' => 0,
            'replyme' => 0,
            'atme' => 0,
        );
        $this->arrData['message'] = $arrMessage;
    	if (!empty($arrRes['message'])) {
            $this->arrData['message'] = array_merge($arrRes['message'],$this->arrData['message']);
        }
    }
    
}
