<?php
/**
 * padthreadstoreAction 
 * 
 * @package pad-client帖子收藏浏览，需要富文本处理，单独新开一个接口，不走通用ui
 * @version $Id: 2013-3-25$
 * @copyright Copyright 2010-2012 baidu.com
 * <AUTHOR>
 * @license 
 */
class padthreadstoreAction extends CapiAction
{
	protected $bolCheckUserLogin = true;
    protected $bolNeedUserLogin = true;
    private $_req = array();
    private $_res = array();

    protected function _check()
    {
    	return true;
	}

    protected function _execute()
    {
        $this->_input();
        $this->_process();
        $this->_build();
    }
    private function _input()
    {
        $this->_req= $_REQUEST;
        return true;
    }
    private function _process()
    {
        $this->_res = Tbapi_Core_Server::mocall('post', 'get_store_thread', $this->_req);
        return true;
    }
    private function _build()
    {
    	//富文本处理
    	if (intval($this->_req['is_detail']) === 1) {
			$condition = new RichTextParserCondition();
        	$condition->intNewLineCount = 1;
        	$parser = new RichTextParser();
        	foreach($this->_res['store_thread'] as $item) {        		
        		$objResult = $parser->process($condition, $item['post_list'][0]['content']);            
            	$content = $objResult->arrContent;
            	$strDealcontent = '';
				foreach ($content as $tmp) {//解析文本形式给前端            
            		if(intval($tmp['type']) === 0) {
            			$strDealcontent= $tmp['text'];
						break;
            		}
            	}
            	$item['post_list'][0]['content'] = $strDealcontent;           
            	$this->_afterDeal['store_thread'][] = $item;           
       		}
		}
        $this->_afterDeal['error'] = $this->_res['error'];           
        $this->arrData = $this->_afterDeal;
    }
}
