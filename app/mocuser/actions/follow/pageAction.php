<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * @date: 2012-01-08
 * 个人关注页，支持非当前用户查询
 * @version
 */
class pageAction extends CapiAction {
    protected $bolCheckUserLogin = false;
    private $pn = 0;
    private $rn = 0;
    private $offset = 0;
    private $arrRequest = null;
    private $arrUserList = null;
    private $arrOutPage = null;
    private $arrUserInfo = array();

    protected function _execute() {
        $ret = $this->_input();
        if($ret == false) {
            return false;
        }

        $ret = $this->_process();
        if($ret == false) {
            return false;
        }

        $this->_build();
    }

    private function _input() {
        $this->pn = intval(Bingo_Http_Request::get('pn', 1));
        $this->rn = intval(Bingo_Http_Request::get('rn', 20));
        //$this->rn = intval(Conf::get('fans_rn', 20));
        $this->offset = ($this->pn-1) * $this->rn;
        $intUid = intval(Bingo_Http_Request::get('uid', CapiRequest::$intUid));
        if($intUid <= 0) {
            Bingo_Log::trace('mocuser/follow/pageAction::_input() uid is empty');
            //$this->_error(CapiErrno::PARAM_NOT_ENOUGH, CapiError::PARAM_NOT_ENOUGH);
            $this->_error(CapiErrno::PARAM_NOT_ENOUGH, CapiError::PARAM_NOT_ENOUGH, array(), false);
            return false;
        }
        if($intUid == CapiRequest::$intUid) {
            if(CapiRequest::$bolLogin == false) {                 //已登录，返回用户自身数据
            	Bingo_Log::trace('mocuser/follow/pageAction::_input() user not login');
                //$this->_error(CapiErrno::USER_NOT_LOGIN, CapiError::USER_NOT_LOGIN);
                $this->_error(CapiErrno::USER_NOT_LOGIN, CapiError::USER_NOT_LOGIN, array(), false);
                return false;
            }
        }

        $this->arrRequest = array(
            'uid' => $intUid,
            'offset' => $this->offset,
            'rn' => $this->rn,
        	'type' => 2,//2标识关注
        );

        return true;
    }

    private function _process() {
        $arrOut = Tbapi_Core_Server::mocall('user', 'user_follow_or_fans_list', $this->arrRequest, 0);
        if ($arrOut === false || $arrOut['error']['errno']) {
			$this->arrData['error_code'] = CapiErrno::INTERNAL_ERROR;
			$this->arrData['error_msg'] = CapiError::INTERNAL_ERROR;
			Tieba_Stlog::addNode('errno',$this->arrData['error_code']);
			Bingo_Log::trace('errno:'.$this->arrData['error_code'].',errmsg:'.$this->arrData['error_msg']);

            return false;
        }
        $this->arrUserList = $arrOut['user_list'];
        $this->arrOutPage = $arrOut['page'];
        //批量获取用户简介
        if (empty($arrOut['user_list'])) {
            Bingo_Log::trace('/c/u/follow/page return user_list is empty');
            return true;
        }
        if ((CapiRequest::$intClientType == CapiDef::CLIENT_TYPE_IPHONE 
            && Molib_Util_Version::compare('4.0.0',CapiRequest::$strClientVersion) >= 0) ||
            (CapiRequest::$intClientType == CapiDef::CLIENT_TYPE_ANDROID 
            && Molib_Util_Version::compare('4.1.0',CapiRequest::$strClientVersion) >= 0)){
            $strUserIds = '';
            foreach ($arrOut['user_list'] as $arrItem){
                $strUserIds .= $arrItem['id'] . ',';
            }
            
            $strUserIds = trim($strUserIds, ',');
            $arrOut = Tbapi_Core_Server::mocall('user', 'm_user_info', array('user_id' => $strUserIds));
            if(empty($arrOut['user_info'])) {
				Bingo_Log::trace('mocuser/follow/pageAction::_process() user_info is empty');
                return true;
            }
            $this->arrUserInfo = $arrOut['user_info'];
        }
        return true;
    }

    private function _build(){
        $objPage = PageService::computePage($this->rn, intval($this->arrOutPage['total_count']));
        $this->arrData['page'] = $objPage->toArray();

        $arrRetUser = array();
        if(!empty($this->arrUserList)) {
            foreach ($this->arrUserList as $arrUser) {
                $arrUser['intro'] = '';
                $arrItem = $this->arrUserInfo[$arrUser['id']];             
                if (isset($arrItem) && isset($arrItem['puserinfo']['userdetail'])) {
                    $arrUser['intro'] = $arrItem['puserinfo']['userdetail'];
                }
                $tmp = PageService::formatUser($arrUser);
    	        $tmp['is_followed'] = 1;//ipad客户端使用
      		    $arrRetUser[] = $tmp;
            }
        }
        $this->arrData['user_list'] = $arrRetUser;
    }
}
