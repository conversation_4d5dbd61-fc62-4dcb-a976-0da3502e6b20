<?php
class search_double_followsAction extends CapiAction
{
    protected function _execute()
    {
        $this->_input();
        $arrUnameList = array();
        $arrRequest = array('uid' => $this->_intUserId, 'query' => $this->_strQuery);
        if (!empty($this->_strQuery) && !empty($this->_intUserId)){
            $arrOut = Tbapi_Core_Server::mocall('user','user_sug',$arrRequest);   
            if ($arrOut === false){
                Warning::callRpcReturnFalse('user','user_sug');
            }else {
                if (is_array($arrOut['user_list'])){
                    foreach ($arrOut['user_list'] as $arrUser){
                        $arrUnameList[] = $arrUser['name'];
                        $arrRequest['user_name'][] = $arrUser['name'];
                    }   
                }   
                $arrOut = Tieba_Service::call('user', 'getUidByUnames', $arrRequest, NULL, NULL, 'post', 'php', 'utf-8');
                foreach ($arrOut['output']['uids'] as $arrItem) {
                    $arrRequest['user_id'][] = $arrItem['user_id'];
                }
                $arrRes = Molib_Tieba_Service::call('user', 'mgetUserData', $arrRequest);
                foreach ($arrRes['user_info'] as $intKey => $arrItem) {
                    $intPortraitTime = intval($arrItem['portrait_time']);
                    $strPortrait = Tieba_Ucrypt::encode(intval($intKey), $arrItem['user_name'], $intPortraitTime);
                    $arrUser = array();
                    $arrUser['user_id'] = $intKey;
                    $arrUser['user_name'] = $arrItem['user_name'];
                    $arrUser['portrait'] = $strPortrait;
                    $arrUser['sex'] = $arrItem['user_sex'];
                    $this->arrData['res_user_infos'][] = $arrUser;
                }
            }
        }
        $this->arrData['error']['errno'] = Tieba_Errcode::ERR_SUCCESS;
        $usermsg = Tieba_Error::getUserMsg($arrRes['errno']);
        $usermsg = Bingo_Encode::convert($usermsg,Bingo_Encode::ENCODE_UTF8,Bingo_Encode::ENCODE_GBK);
        $this->arrData['error']['errmsg'] = '';
        $this->arrData['error']['usermsg'] = $usermsg;
    }

    protected function _input()
    {
        $this->_intUserId = intval(Bingo_Http_Request::get('user_id', CapiRequest::$intUid));
        $this->_intLimit = intval(Bingo_Http_Request::get('limit', 100));
        $this->_intOffset = intval(Bingo_Http_Request::get('offset', 0));
        $strQuery = strval(Bingo_Http_Request::get('user_name',''));
        $this->_strQuery = Encode::convertGBKToUTF8($strQuery);
    }

}
