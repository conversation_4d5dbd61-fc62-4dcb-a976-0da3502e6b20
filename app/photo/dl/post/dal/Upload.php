<?php

class Dl_Post_Dal_Upload{
	
	public static function addUploadInfo($arrInput){
		
		$strSql = "insert into upload_info (forum_id, user_id, user_name, upload_count) values ({forum_id:n}, {user_id:n}, ".
							"{user_name:s}, {upload_count:n}) on duplicate key update upload_count = upload_count + {upload_count:n}";
		
		$arrInputParam = array(
				"forum_id"			=> $arrInput['forum_id'],
				"user_id"				=> $arrInput['create_user_id'],
				"user_name"			=> $arrInput['create_user_name'],
				"upload_count"	=> $arrInput['post_picture_count'],
		);
		
		$res = Dl_Post_Post::$_db->queryData($strSql, $arrInputParam);
		if($res === false){
			//����Ҫ����
			Bingo_Log::debug("ignore the sql : input[" . $strSql . "]");
			return true;	
		}
		return $res;					
		
	}
		
}

?>