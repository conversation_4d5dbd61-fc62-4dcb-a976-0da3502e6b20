<?php
/**
 * ʱ������࣬���ڶ�ʱ�������
 * @author:cuishichao
 */
require_once('crawlerLog.php');

class timeTools{
	/*
	 * ��ȡһ��ʱ�������һ����1��0��0�ֵ�����
	 */
	public static function getSecondsToNextMonth($strNowDate)
	{
		$intNow = strtotime($strNowDate);
		$arrDateTime = explode(" ", $strNowDate);
		$arrDate = explode("-", $arrDateTime[0]);
		$year = intval($arrDate[0]);
		$month = intval($arrDate[1]);
		$day = intval($arrDate[2]);
		//��ȡ��ǰ�µ�����
		$lastday = date("t", strtotime($strNowDate));
		$strNextDate = date("Y-m-d G:i:s",mktime(0,0,0,$month, $lastday+1, $year));
		echo "�´θ��µ�ʱ��Ϊ��".$strNextDate."\n";
		//$nextDate = strtotime($strNextDate);
		return (strtotime($strNextDate) - $intNow);
	}

	/*
	 * �ж�ʱ���Ƿ��ǵ�ǰ��
	 */
	public static function isThisMonth($strDateTime)
	{
		$intNow = strtotime($strDateTime);
		$arrDateTime = explode(" ", $strDateTime);
		$arrDate = explode("-", $arrDateTime[0]);
		$year = intval($arrDate[0]);
		$month = intval($arrDate[1]);
		$nowYear = intval(date("Y"));
		$nowMonth = intval(date("m"));
		if($month == $nowMonth && $year==$nowYear){
			return TRUE;
		}
		return FALSE;
	}
}
?> 
