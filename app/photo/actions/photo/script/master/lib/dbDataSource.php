<?
class dbDataSource {
    public static $dataCache = array();
    public static $haveDone  = 0;
    
    public static $mysqli = null;
    
    public static $querySql  = '';
    
    public function __destruct(){
    }
    
    //��ʼ�����������ݼ���
    public static function init($processInfo=array()){
        $objConfig = $processInfo['objConfig'];
        
        if(self::$mysqli == null ){
            
            self::$querySql = $objConfig->get('querysql');
            if(self::$querySql == false){
                Bingo_Log::warning('querysql is not config');
                return false;
            }
            
            self::$mysqli = mysqli_init();
            mysqli_options (self::$mysqli,MYSQLI_OPT_CONNECT_TIMEOUT,5);
            
            $strHost  = $objConfig->get('dbhost','127.0.0.1');
            $username = $objConfig->get('dbuser','root');
            $password = $objConfig->get('dbpwd','root');
            $database = $objConfig->get('dbbase','test');
            $intPort  = $objConfig->get('dbport','3306');
            Bingo_Log::trace("db connecting: $strHost:$intPort $database $username $password");
            $bolRes = mysqli_real_connect (self::$mysqli,$strHost,$username,$password,$database,$intPort);
        
            if (mysqli_connect_errno()) {
                $strInfo = sprintf ("db mysqli_real_connect %s %d failed [errno : %d] [error %s]",$strHost,$intPort,mysqli_connect_errno (),mysqli_connect_error ());
                Bingo_Log::warning($strInfo);
                return false;
            }
            self::$mysqli->autocommit(true);
            Bingo_Log::trace("db connected: $strHost $intPort");
        }
        return true;
    }
    
    public static function hastask($processInfo=array()){
       return true;
    }
    
    //�ַ�����
    public static function nexttask($processInfo=array()){
        $pid  = $processInfo['nowProcessId'];
        $pnum = $processInfo['processNum'];
        
        $tasksql = sprintf(self::$querySql,$pid,$pnum);
        $result = self::$mysqli->query($tasksql);
        if($result === false){
            Bingo_Log::warning('exec sql fail [' . self::$mysqli->errno . '] [' .  self::$mysqli->error . '] [' . $tasksql . ']');
            return false;
        }
        
        if($row = $result->fetch_assoc()){
           ++self::$haveDone;
           return $row;
        }
        return array();
    }
    
    public static function progress($processInfo=array()){
        return self::$haveDone;
    }
}
