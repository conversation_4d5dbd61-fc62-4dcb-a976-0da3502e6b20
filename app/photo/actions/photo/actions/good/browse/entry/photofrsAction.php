<?php 
/**
 *   ��ͼfrs���
 *
 */
class  photofrsAction extends photo_Public_BrowseAction{
    	                          
    public function init(){
        $this->_actionConf = conf_ActionConf::$actionConf["good_browse_entry_photofrsAction"];
        if (false === parent::init()){
            if($this->_intErrorNo === 0 ){
                $this->_intErrorNo  = conf_ErrorNo::errInitFaile;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        
        return true;
    }
     
    public function process(){
    	
    	//������Ϣ
    	$this->_arrTplVar['params'] = photo_Public_Util::getQueryString();	
		
        //tbs
        $this->_arrTplVar['tbs']=array('common'=>photo_Public_Request::$strTbs);     
        
        $albumInfos = photo_Data_GoodAlbum::getGoodAlbumByForum(photo_Public_Product::getProductId(), 
        					                                    photo_Public_Request::$intFid,
        					                                    0,
        					                                    0,
        					                                    5,
        					                                    conf_Include::GET_ALBUM_LAST_UPDATE,
        					                                    conf_Include::CREATE_ALBUM_TYPE
        					                                    );
        if($albumInfos === false){
            $this->_intErrorNo = conf_ErrorNo::errDaoFaile;
            $this->_strErrorMsg = "get album info by forum faile!product_id=".photo_Public_Product::getProductId()."|fid=".photo_Public_Request::$intFid;
            //Bingo_Log::warning($this->_strErrorMsg);
            Bingo_Log::fatal('call_getGoodAlbumByForum_in_photo_good_entry_photofrs '.$this->_strErrorMsg);
            return false;
        }                                            
        //build album
        $ViewAlbumInfo = photo_Logic_Photo::buildAlbumInfo($albumInfos,
                                                           photo_Logic_Photo::BUILD_ALBUM_COUNT_POWER,
                                                           0,
                                                           0,
                                                           true,
                                                           true);
        $strUndefinedClass = photo_Logic_Common::encodeId(0, photo_Public_Request::$intFid);                                                   

        //var_dump($ViewAlbumInfo);exit;
        foreach ($ViewAlbumInfo as $key =>$album) {        
        	if($strUndefinedClass === $album['tag_id']) {
        		$ViewAlbumInfo[$key]['tag_type'] = 'undefined';
        	}
        }         
        $album_area_info = array();
        $album_area_info['album_list'] = $ViewAlbumInfo;
        
        //��ȡ��������Ϣ
        $albumCount = photo_Logic_Photo::getAlbumCount(photo_Public_Product::getProductId(), 
                                                 photo_Public_Request::$intFid);
        $album_area_info['album_amount'] = $albumCount;
        
        $this->_arrTplVar = array_merge($this->_arrTplVar,$album_area_info);
        //var_dump($this->_arrTplVar);exit;
    }    
}