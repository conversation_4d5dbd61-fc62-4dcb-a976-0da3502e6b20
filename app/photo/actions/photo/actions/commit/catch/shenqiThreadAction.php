<?php
/*
 * ʹ����ͼ��������������
 */
class shenqiThreadAction extends photo_Public_CommitAction{
	const INT_MEDIUM_WIDTH_NUM  = 560;
	const INT_MEDIUM_HEIGHT_NUM = 300;
	const MAX_PIC_LIST_NUM = 10;
        //shenqi pinterest tuzhong
 protected function getContentByImglist($arrImageInfo,$strOriginalUrl,$strWords,$platform=''){
                static $objScript = null;
                if($objScript === null ){
                        $objScript = Bingo_View_Script::getInstance();
                        $objScript->setBaseDir(MODULE_VIEW_PATH);
                }
                $tplValue = array();
                $tplValue['arrImageInfo'] = $arrImageInfo;
                $tplValue['strOriginalUrl'] = $strOriginalUrl;
                if(empty($strOriginalUrl)){
                    $strWords="<br/>".$strWords;
                }
                $tplValue['strWords']=$strWords;
                if(empty($platform)){
                    $tplValue['pin_type'] = "shenqi";
                }
                else{
                    $tplValue['pin_type']=$platform;
                }
                $objScript->assign($tplValue);
                return $objScript->template("tpl_pb_imgs.php");
        }
	public function init(){
		$this->_actionConf = conf_ActionConf::$actionConf['commit_catch_shenqiThreadAction'];
		if (false === parent::init()){
			if($this->_intErrorNo === 0 ){
				$this->_intErrorNo  = conf_ErrorNo::errInitFaile;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}
        
        public function checkString($strString){
            $arrIllChars=array('*','<','>',' ','"',"'","\\");
            foreach ($arrIllChars as $strChar){
                if(strstr($strString,$strChar)!==false){
                    return false;
                }
            }
            return true;
        }
	public function process(){
                if(false===$this->checkString(photo_Public_Request::$arrInputParam['src'])){
                    return false;
                }
                if(false===$this->checkString(photo_Public_Request::$strFname)){
                    return false;
                }
                if(false===$this->checkString(photo_Public_Request::$arrInputParam['platform'])){
                    return false;
                }
		$inputParam=array();
		$inputParam['title']=strip_tags(photo_Public_Request::$arrInputParam['title']);
		$inputParam['pics']=photo_Public_Request::$arrInputParam['pics'];
		$inputParam['MAX_PIC_LIST_NUM']=self::MAX_PIC_LIST_NUM;
		$inputParam['content']=strip_tags(trim(Bingo_Http_Request::get('content','')));
		$inputParam['src']=strip_tags(photo_Public_Request::$arrInputParam['src']);
		$inputParam['intFid']=photo_Public_Request::$intFid;
                $inputParam['strFname']=strip_tags(photo_Public_Request::$strFname);
                $inputParam['platform']=  strip_tags(photo_Public_Request::$arrInputParam['platform']);
                $arrPictureInfo=array();
                foreach($inputParam['pics'] as $arrEachPic){
                       $arrPictureInfo[]=array('medium'=>array('url'=>$arrEachPic['url'],'width'=>$arrEachPic['width'],'height'=>$arrEachPic['height']));
                }
		$content=$this->getContentByImglist($arrPictureInfo,
				photo_Public_Request::$arrInputParam['src'],
				trim(Bingo_Http_Request::getPostRaw ( 'content', '' )),$inputParam['platform']);
		$inputParam = array(
				'_user_client_ip'     => photo_Public_Request::$strUip,
				'_inner_secure_key'   => photo_Public_Conf::get('inner_type_key'),
				'fid'                 => photo_Public_Request::$intFid,
				'kw'                  => photo_Public_Request::$strFname,
				'title'               => photo_Public_Request::$arrInputParam['title'],
				'tbs'                 => Bingo_Http_Request::get("tbs",'0'),
				'content'             => $content,
				'postContent'         => '%s',
				'anonymous'           => '0',
				'vcode_flag'          => 0,
				'vcode'               => Bingo_Http_Request::get("vcode"),
				'vcode_md5'           => Bingo_Http_Request::get("vcode_md5"),
				'tag'                 => Bingo_Http_Request::get('tag',11),
                'new_vcode'                   => Bingo_Http_Request::get('new_vcode',1),
                //'shenqi_src'          => photo_Public_Request::$arrInputParam['src'], 
		//		'rich_text'           => 1, 
                //'thread_type'         => conf_Include::PHOTO_THREAD_TYPE,
	            'thread_type'         =>0,
				'source'							=> photo_Public_Request::$arrInputParam['src'],
				'source_code'					=> substr(md5(photo_Public_Request::$arrInputParam['src'].conf_Include::ENCODE_STR),0,16),	                        
		);
                $arrReq=  $this->genPostCommitServiceArgs();
                $arrReq['req']['source']=photo_Public_Request::$arrInputParam['src'];
                $arrReq['req']['source_code']=substr(md5(photo_Public_Request::$arrInputParam['src'].conf_Include::ENCODE_STR),0,16);
                $arrReq['req']['content']=$content;
                $arrReq['req']['postContent']='%s';
                $arrReq['req']['thread_type']=0;//conf_Include::PHOTO_THREAD_TYPE;
		$arrReq['req']['user_name']=mb_convert_encoding ($arrReq['req']['user_name'],'UTF-8','GBK');
                $arrRet = Tieba_Service::call('post','addThread',$arrReq);
                if (false === $arrRet){
                	Bingo_Log::fatal('call_service_addThread_in_photo_commit_catch_shenqiThread with input '.serialize($arrReq));
                }
                $arrRet['no']=$arrRet['errno'];
                $arrRet['error']=Tieba_Error::getErrmsg($arrRet['errno']);
                $arrRet['data']=$arrRet['res'];
                $arrRet['data']['vcode']=$arrRet['res'];
                $arrRet['data']['res']=$arrRet['res'];
                if ($arrRet['no'] == 40) {
                    $this->_intErrorNo = $arrRet['no'];
			$this->_strErrorMsg = $arrRet['error'];
			$this->_arrTplVar=$arrRet;
			return true;
		}
		else if($arrRet['no']!=Tieba_Errcode::ERR_SUCCESS){
                    $this->_intErrorNo = $arrRet['errno'];
                    $this->_strErrorMsg = Tieba_Error::getErrmsg($arrRet['errno']);
		    Bingo_Log::warning($this->_strErrorMsg);
		    $this->_arrTplVar=$arrRet;
	            return false;	
                }
		$this->_arrTplVar=$arrRet;
	}	
}
