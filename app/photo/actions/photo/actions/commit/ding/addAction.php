<?php
class addAction extends photo_Public_CommitAction{
	public function init(){
 		$this->_actionConf = conf_ActionConf::$actionConf['commit_ding_addAction'];
		if (false === parent::init()){
			if($this->_intErrorNo === 0 ){
				$this->_intErrorNo  = conf_ErrorNo::errInitFaile;
				$this->_strErrorMsg = "init return error!";
				Bingo_Log::warning($this->_strErrorMsg);
			}
		}
		return true;
	}
	
	public function process(){
	    
	    //�ӿ���������
	    if(true){
    	    $this->_intErrorNo = conf_ErrorNo::errNotSupport;
    	    $this->_strErrorMsg = 'picture ding is not support!';
    	    Bingo_Log::warning($this->_strErrorMsg);
    	    return false;
	    }
	    
	    
		$picture_id = photo_Public_Request::$arrInputParam['pic_id'];
		$album_id   = photo_Public_Request::$arrInputParam['alb_id'];
		if($album_id != 0 ){  //ͼ�ᶥ
			//...nothing do
		}else if($picture_id != 0 ){   //ͼƬ��
			//checkͼƬ
			$pictureId = photo_Public_Request::$arrInputParam['pic_id'];
			$pictureInfos = photo_Data_Picture::getMutiPicById(photo_Public_Product::getProductId(), 
			                                                   photo_Public_Request::$intFid, 
			                                                   array($pictureId));
			if($pictureInfos === false ){
				$this->_intErrorNo = conf_ErrorNo::errNoRight;
			    $this->_strErrorMsg = "get picture info error!! pic_id=".photo_Public_Request::$arrInputParam['pic_id'];
			    Bingo_Log::warning($this->_strErrorMsg);
			    return false;
			}
			if(empty($pictureInfos)){
			    $this->_intErrorNo = conf_ErrorNo::errPictureIdInvaild;
			    $this->_strErrorMsg = "picture not exist!! pic_id=".photo_Public_Request::$arrInputParam['pic_id'];
			    Bingo_Log::warning($this->_strErrorMsg);
			    return false;
			}
			$pictureInfo = $pictureInfos[0];
		
			//���Ӷ�����
			$arrInput = array(
			                     'product_id'     => photo_Public_Product::getProductId(),
        		                 'forum_id'       => photo_Public_Request::$intFid,
        		                 'picture_id'     => $picture_id,
			                     'album_id'       => $pictureInfo['album_id'],
        		                 'add_click_num'  => 0,
        		                 'add_ding_num'   => 1,
        		                 'add_comment_num'=> 0,
			                 );
			$ret = photo_Data_Picture::addPicCountById(array($arrInput));
			if($ret === false ){
			    $this->_intErrorNo  = conf_ErrorNo::errDaoFaile;
			    $this->_strErrorMsg = "add picture ding num failed![$picture_id]".serialize($ret);
				Bingo_Log::warning($this->_strErrorMsg);
				return false;
			}
			
			//����cm
			$cmInput = array(
		                  'command_no'        => conf_Command::pictureDing,
		                  'product_id'        => photo_Public_Product::getProductId(),
			              'forum_id'          => photo_Public_Request::$intFid,
		                  'picture_id'        => $picture_id,
		                  'picture_user_id'   => $pictureInfo['create_user_id'],
		                  'picture_user_ip'   => $pictureInfo['create_user_ip'],
		                  'picture_user_name' => $pictureInfo['create_user_name'],
	                      'user_id'           => photo_Public_Request::$intUid,  
		                  'user_name'         => photo_Public_Request::$strUname, 
		                  'user_ip'           => photo_Public_Request::$intUip,
		               );
		   $cmRet = photo_Logic_Common::callCm($cmInput);
		   if($cmRet === false ){
		   	   Bingo_Log::fatal('call_callCm_in_photo_commit_ding_add fail withi input '.serialize($cmInput));
		   		//Bingo_Log::warning('call cm faile!['.serialize($cmRet).']['.serialize($cmInput).']');
		   }
		}else {
			$this->_intErrorNo  = conf_ErrorNo::errMissingParams;
			$this->_strErrorMsg = "picture_id&album_id empty!";
			Bingo_Log::warning($this->_strErrorMsg);
		}
		return true;
	}
	
	public  function setActsCtrlParam(){
	    if(photo_Public_Request::$arrInputParam['alb_id'] != 0){
	        $this->actsParam['id']   =photo_Public_Request::$arrInputParam['alb_id'] ;
	        $this->actsParam['id_type'] = 0 ;
	    }else if(photo_Public_Request::$arrInputParam['pic_id'] != 0 ){
	        $this->actsParam['id']   = photo_Public_Request::$arrInputParam['pic_id'] ;
	        $this->actsParam['id_type'] = 1 ;
	    }
	    else {
	        $this->actsCmdNo = 0 ;
	    }
	}
}
