<?php

/*
 * ��ͼfrsҳ����չ��¥������������ͼƬ����������һ¥��
 * <EMAIL>
 */

class threadsInfoAction extends photo_Public_BaseDataAction {

    public function init() {
        $this->_actionConf = conf_ActionConf::$actionConf['browse_photo_threadsInfoAction'];
        if (false === parent::init()) {
            if ($this->_intErrorNo === 0) {
                $this->_intErrorNo = conf_ErrorNo::errInitFaile;
                $this->_strErrorMsg = "init return error!";
                Bingo_Log::warning($this->_strErrorMsg);
            }
        }
        return true;
    }

    public function process() {
        //$arrThreadList = Bingo_String::json2array(photo_Public_Request::$arrInputParam['threadsList']);        
        $arrThreadList = photo_Public_Request::$arrInputParam['threadsList'];
        $needPicNum = photo_Public_Request::$arrInputParam['needPicNum'];
        $arrRet = array();
        
        $intReturnNumber = min($needPicNum, Conf_Include::FRS_PIC_NUM);
        
        $arrCacheKey = array();
        $arrCacheThread = array();
        foreach ($arrThreadList as $key => $value) {//thread_id  thread_louzhu_id
          //û�лظ�������
          /*if(isset($value['post_num']) && $value['post_num'] == 0){
          	$arrRet[$value['thread_id']] = array('louzhu_pic_counts' => 0, 'louzhu_pics' => array());	
          	continue;
          }*/
          //tidС��1500000000������
          if(isset($value['thread_id']) && $value['thread_id'] < Conf_Include::MIN_THREAT_NUM){
          	$arrRet[$value['thread_id']] = array('louzhu_pic_counts' => 0, 'louzhu_pics' => array());	
          	continue;
          }
                   
          $strCacheKey = "frs_".$value['thread_id'];  	
          $arrCacheKey[] = $strCacheKey;
          $arrCacheThread[] = $value;
          //$arrOutput = photo_Public_Cache::get($strCacheKey, Conf_Include::FRS_CACHE);
          /*
          //���¹���Ĳ����ǣ�ֱ��ȡcache������
          if($arrOutput === null){
          	if(isset($value['post_update_time']) && 
          		(photo_Public_Request::$intRequestTime - $value['post_update_time'])< Conf_Include::POST_UPDATE_TIME){
          		$arrRet[$value['thread_id']] = array('louzhu_pic_counts' => 0, 'louzhu_pics' => array());	
          	}else{
          		if(Conf_Include::QUERY_DB){
	          		$ret = $this->getThreadPics(array('thread_id' => $value['thread_id'], 'lz_user_id' => $value['thread_louzhu_id'],
	              	'need_pic_num' => $intReturnNumber, 'order_type' => conf_Include::GET_PICTURE_ORDER_BY_TIME_AES));
	            	photo_Public_Cache::add($strCacheKey, $ret, Conf_Include::CACHE_INACTIVE_TIME,Conf_Include::FRS_CACHE);
	            	$intDbUser ++;
	            	$arrRet[$value['thread_id']] = $ret;
	            }else{
	            	$arrRet[$value['thread_id']] = array('louzhu_pic_counts' => 0, 'louzhu_pics' => array());	
	            }             	          		
          	}
          	continue;          				
          }
	        $arrRet[$value['thread_id']] = $arrOutput;*/										
        }
        
        if(empty($arrCacheKey)){
        	Bingo_Log::debug("no data get in cache");
        	$this->_arrTplVar['thread_pic_list'] = $arrRet;
        	return true;	
        }
        
        Bingo_Timer::start("MultiCache");
        $arrCacheRes = photo_Public_Cache::mutiget($arrCacheKey, Conf_Include::FRS_CACHE);
				Bingo_Timer::end("MultiCache");
        
        $intDbUser = 0;        
        foreach($arrCacheThread as $key => $value){
        	$strCacheKey = "3:frs_cache:frs_".$value['thread_id'];	
        	if(isset($arrCacheRes[$strCacheKey])){
        		$arrRet[$value['thread_id']] = $arrCacheRes[$strCacheKey];
        	}else{
          	if(isset($value['post_update_time']) && 
          		(photo_Public_Request::$intRequestTime - $value['post_update_time'])< Conf_Include::POST_UPDATE_TIME){
          		$arrRet[$value['thread_id']] = array('louzhu_pic_counts' => 0, 'louzhu_pics' => array());	
          	}else{
          		if(Conf_Include::QUERY_DB){
	          		$ret = $this->getThreadPics(array('thread_id' => $value['thread_id'], 'lz_user_id' => $value['thread_louzhu_id'],
	              	'need_pic_num' => $intReturnNumber, 'order_type' => conf_Include::GET_PICTURE_ORDER_BY_TIME_AES));
	            	photo_Public_Cache::add("frs_".$value['thread_id'], $ret, Conf_Include::CACHE_INACTIVE_TIME,Conf_Include::FRS_CACHE);
	            	$intDbUser ++;
	            	$arrRet[$value['thread_id']] = $ret;
	            }else{
	            	$arrRet[$value['thread_id']] = array('louzhu_pic_counts' => 0, 'louzhu_pics' => array());	
	            }             	          		
          	}        			
        	}
        }
        
        Tieba_Stlog::addNode('no_hit_cache', $intDbUser);
        $this->_arrTplVar['thread_pic_list'] = $arrRet;
    }

    function getThreadPics($arrInput) {
        $albumIdInfos = photo_Data_Album::getAlbumByThreadId(
                        photo_Public_Product::getProductId(), photo_Public_Request::$intFid, $arrInput['thread_id'],0,true,true
        );
        if ($albumIdInfos === false) {
            $this->_intErrorNo = conf_ErrorNo::errDaoFaile;
            $this->_strErrorMsg = "get album info error! album_id=" . photo_Public_Request::$arrInputParam['alb_id'];
            //Bingo_Log::warning($this->_strErrorMsg);
            Bingo_Log::fatal('call_getAlbumByThreadId_in_photo_browse_photo_threadsInfo fail '.$this->_strErrorMsg);
            return array('louzhu_pic_counts' => 0, 'louzhu_pics' => array());
        }
        if (empty($albumIdInfos) || empty($albumIdInfos[0]['album_id'])||0==$albumIdInfos[0]['create_user_id']) {
            //�е�����һ��ͼƬ��û�У�û�н�ͼ�ᣬ���������������ʷ���ݴ����û�idû��
            return array('louzhu_pic_counts' => 0, 'louzhu_pics' => array());
        }
        $albumInfos=  photo_Data_Album::getAlbumByAbumId( photo_Public_Product::getProductId(), photo_Public_Request::$intFid,array($albumIdInfos[0]['album_id']));
        //��ȡ����ͼƬ�б����Ϣ
        $intPicCount = min(intval($albumInfos[0]['picture_count']), intval($albumInfos[0]['all_picture_count'])); //ͼ�ᾫѡ�Ĳ���������
        if ($intPicCount <= 0||0==$albumIdInfos[0]['create_user_id']||0==$albumIdInfos[0]['thread_id']) {
            return array('louzhu_pic_counts' => 0, 'louzhu_pics' => array());
        }
        $briefPicInfos = photo_Data_Picture::getPicByALbumId(photo_Public_Product::getProductId(), photo_Public_Request::$intFid, $albumInfos[0]['album_id'],
                0, $arrInput['need_pic_num']*2,
                conf_Include::GET_PICTURE_ORDER_BY_TIME_AES,//conf_Include::GET_PICTURE_ORDER_BY_ORDER_DESC,
                0,//$arrInput['lz_user_id'],
                false,
                true
        );
        $arrPicList = array();
        $i = 0;
        foreach ($briefPicInfos as $key => $picture) {
            if($picture['create_user_id']!=$arrInput['lz_user_id']){
                continue;
            }
            $arrPicList[$i]['type'] = 'pic';
            $arrPicList[$i]['small_pic'] = photo_Public_Util::pic2url($picture['picture_id'], $picture['forum_id'],  conf_Include::SPACE_PIC_SMALL_IMG_SPEC);
            if (isset($picture['picture_resource']) && !empty($picture['picture_resource'])) {
                $ret = unserialize($picture['picture_resource']);
                if ($ret === false) {
                    $this->_strErrorMsg = "unserialize the water info error , picture_id=" . $picture['picture_id'] . " water info=" . $picture['picture_resource'];
                    Bingo_Log::debug($this->_strErrorMsg);
                    $arrPicList[$i]['water_pic'] = photo_Public_Util::pic2url($picture['picture_id'], $picture['forum_id'], 'w=' . conf_Include::BIG_PICTURE_WIDTH);
                } else {
                    if (isset($ret['waterurl']) && !empty($ret['waterurl'])&&'pic'!=$ret['waterurl']) {
                        $pos1=  strpos($ret['waterurl'], 'ap=')+  strlen('ap=');
                        $pos2=  strpos($ret['waterurl'], '��,');
                        $fname='';
                        if(false!==$pos1&&false!==$pos2&&$pos2>$pos1){
                            $fname=  substr($ret['waterurl'], $pos1, $pos2-$pos1);
                        }
                        $strWaterInfo = image_waterUrl::createWaterInfo(array('width'=>$picture['width'],'height'=>$picture['height'],
                            'time'=>time(),'format'=>$picture['format'],'forum'=>$fname,'intStaticWidth'=>conf_Include::BIG_PICTURE_WIDTH));
                        $arrPicList[$i]['water_pic'] = photo_Public_Util::pic2url($picture['picture_id'], $picture['forum_id'], 'w=' . conf_Include::BIG_PICTURE_WIDTH.';'.$strWaterInfo);
                    } else {
                        $arrPicList[$i]['water_pic'] = photo_Public_Util::pic2url($picture['picture_id'], $picture['forum_id'], 'w=' . conf_Include::BIG_PICTURE_WIDTH);
                    }
                }
            } else {
                $arrPicList[$i]['water_pic'] = photo_Public_Util::pic2url($picture['picture_id'], $picture['forum_id'], 'w=' . conf_Include::BIG_PICTURE_WIDTH);
            }
            $arrPicList[$i]['big_pic'] = photo_Public_Util::pic2url($picture['picture_id'], $picture['forum_id'], 'w=' . conf_Include::BIG_PICTURE_WIDTH);
            $arrPicList[$i]['picInfo'] = $this->getPicInfo($picture);
            $i++;
        }
        return array('louzhu_pic_counts' => $intPicCount, 'louzhu_pics' => $arrPicList);
    }

	public function getPicInfo($arrInput){
		$arrOutput["orignal"]["width"]	= intval($arrInput["width"]);
		$arrOutput["orignal"]["height"]	= intval($arrInput["height"]);
			
		if($arrInput["width"] > conf_Include::BIG_PICTURE_WIDTH){
				$arrOutput["big"]["width"]	= intval(conf_Include::BIG_PICTURE_WIDTH);
				$arrOutput["big"]["height"]	= intval($arrInput["height"] * conf_Include::BIG_PICTURE_WIDTH / $arrInput["width"]);
		}else{
				$arrOutput["big"]["width"]	= intval($arrInput["width"]);
				$arrOutput["big"]["height"]	= intval($arrInput["height"]);					
		}
			$intWitdhRadio = $arrInput["width"]/conf_Include::SMALL_PIC_WIDTH;
			$intHeightRadio = $arrInput["height"]/conf_Include::SMALL_PIC_HEIGHT;
			
			$intMaxRadio = max($intWitdhRadio, $intHeightRadio);
			if($intMaxRadio > 1){
				if($intWitdhRadio > $intHeightRadio){
					$arrOutput["small"]["width"]	= intval(conf_Include::SMALL_PIC_WIDTH);
					$arrOutput["small"]["height"]	= intval($arrInput["height"] * conf_Include::SMALL_PIC_WIDTH / $arrInput["width"]);					
				}else{
					$arrOutput["small"]["width"]	= intval($arrInput["width"] * conf_Include::SMALL_PIC_HEIGHT / $arrInput["height"]);
					$arrOutput["small"]["height"]	= intval(conf_Include::SMALL_PIC_HEIGHT);							
				}
			}else{
				$arrOutput["small"]["width"]	= intval($arrInput["width"]);
				$arrOutput["small"]["height"]	= intval($arrInput["height"]);					
			}			
		return $arrOutput;			
	}
}