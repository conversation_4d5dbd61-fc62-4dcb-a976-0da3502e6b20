<?php
/**
 * ����ȫ������
 * <AUTHOR>
 *
 */
class photo_Public_Request
{ 
	//�������
    public static $arrInputParam = array();
    
    //����ʱ��
    public static $intRequestTime = 0 ; 
    
    //��־��һ����Ŀ¼
    public static $arrFdirInfo = array();
    
    
	//�û�id
	public static $intUid 		= 0;
	public static $strUname		= '';
	
	//�û�ip
    public static $intUip       = 0;
    public static $strUip       = '';
	
    //��id
    public static $intFid       = 0;
    
    //����
    public static $strFname     = '';
    
    //�Ƿ��Ѿ���½
    public static $bolUserLogin = false;
    
    //�û�email
    public static $strEmail        ='';
    
    //�û��ֻ���
    public static $strMobilephone  = '';
    
    //�Ƿ�����username�û���0�����ǣ�1����
    public static $intNoUn         = 0;
    
 	//�Ựid
    public static $strSid = 0;
    
    //�û����ܴ�
    public static $strUserCrypt = '';
    
    //�ٶ�uid
    public static $strBdUid = '';  
    
    //��Ʒ��
    /*
    public static $ProductName = '';
    public static $ProductId   = 0 ;
    */
    
    //tbs
    public static $strTbs = '';
    
    //�Ƿ���cache
    public static $isOpenCache = null; 
    
    //�Ƿ������ϻ���
    public static $isOnlineEnv = null; 

    //tac����
    public static $tacConf = null;
    
    //��������
    public static $ie = null;
    
    //�Ƿ������Զ���ͨͼƬ����
    public static $bolAutoOpenPhoto = null;
    
    //ͼƬ������Ϣ
    public static $arrPhotoForumInfo = null;
    
    //�Ʒ���������
    public static $arrConfilerGroup = null;
    
    //��ʽ����
    public static $strAlt = '';
    
    //ɾ��ͼƬ��
    public static $delPostPicture = 0;
    
    //frs����ժҪ����
    public static $arrFrsAbt = array();
    
    public static function init(){
    	self::$arrInputParam = array();
    	self::$intUid = 0 ;
    	self::$strUname = '';
    	self::$intUip = 0 ;
    	self::$strUip = '';
    	self::$intFid = 0 ;
    	self::$strFname = '';
    	self::$bolUserLogin = false;
    	self::$strSid = 0 ;
    	self::$strUserCrypt ='';
    	self::$strBdUid ='';
    	/*
    	self::$ProductName ='Tieba';
    	self::$ProductId = 1;
    	*/
    	self::$strTbs = '';
    	self::$ie = Bingo_Encode::ENCODE_GBK;
    	self::$bolAutoOpenPhoto = null;
    	self::$arrPhotoForumInfo = null;
    	self::$strAlt='';
    	
    	self::$strEmail = '';
    	self::$strMobilephone ='';
    	self::$intNoUn=0;
    }
    
    public static function serialize(){
    	$strLog = "";
    	foreach(get_class_vars(__CLASS__) as $key=>$value)
        {
        	if($key =='arrPhotoForumInfo') continue;
            if(isset(self::$$key))
            {
            	$strLog .= " $key =[".serialize($value)."] ";
            }
        }
        return $strLog;   
    }
}
