<?php
/**
 * Created by PhpStorm.
 * User: huxiaomei
 * Date: 2019/4/4
 * Time: 上午10:39
 */
class Lib_NewRule {
    //fail_type 配置
    CONST AIRBON_OFF_TYPE = 'airbon_off';
    CONST CHECK_ADD_SCORE = 'add_score';
    public static $complainOffFailType = array(
        'complain_off_illegal_delpost',//	投诉下任	警告类型：违规封禁删帖
        'complain_off_illegal_addpost',//	投诉下任	警告类型：违规发帖操作
        'complain_off_illegal_interest',//	投诉下任	警告类型： 涉嫌谋私获利
        'complain_off_illegal_post_info',//	投诉下任	警告类型： 大量违规信息
        'complain_off_sex',//	投诉下任	警告类型： 黄色等违法信息
        'complain_off_else',//	投诉下任	警告类型： 其他违规行为
        'complain_off_nologin',//	投诉下任	警告类型： 长期未登录
    );

    public static $warFailType = array(
        'complain_war_illegal_delpost',//	投诉警告	警告类型：违规封禁删帖
        'complain_war_illegal_addpost',//	投诉警告	警告类型：违规发帖操作
        'complain_war_illegal_interest',//	投诉警告	警告类型： 涉嫌谋私获利
        'complain_war_illegal_post_info',//	投诉警告	警告类型： 大量违规信息
        'complain_war_sex',//	投诉警告	警告类型： 黄色等违法信息
        'complain_war_else',//	投诉警告	警告类型： 其他违规行为
        'complain_war_nologin',//	投诉警告	警告类型： 长期未登录
    );
    public static $taskFailType = array(
        'sign_task',
        'post_task',
        'more_task',
        'bawu_task_check',     //考核任务未完成
        'try_bawu_task_check', //试运行任务未完成
    );
    public static $otherFailType = array(
        'add_score'
    );

    CONST OP_OFF_UNAME = 'check_bawu';
    CONST OP_OFF_UID = '3447387038'; //上下任吧主使用的账号

    //词表配置
    const NEW_BAWU_RULE_TABLE = 'new_bawu_rule';//新吧务制度词表
    const NEW_MANAGERAPPLY_WHITELIST_TABLE = 'new_managerapply_whitelist';//新吧务制度豁免白名单
    const BAWU_OFFLINE_WHITELIST_TABLE = 'manager_offline_whitelist';//吧主下任公示+招募豁免白名单
    const CHECK_NEW_MANGERAPPLY_KEY = 'is_new_managerapply';//是否开启考核
    const CHECK_BAWUTASK_KEY = 'is_check_bawutask';//是否开启考核
    const CHECK_BAZHU_SCROE_KEY = 'check_bazhu_score';//考核吧主总分
    const CHECK_BAWU_SCROE_KEY = 'check_bawu_score';//考核吧务总分
    const CHECK_POST_NUM_KEY = 'check_post_num';//每周考核帖子数量
    const CHECK_SIGN_NUM_KEY = 'check_sign_num';//每周考核帖子数量

    public static $offlineStatus = array(
        'init_status' => 0,
        'notice_status' => 1,
        'open_status' => 2,
        'cancel_top_status' => 3,
        'end_status' => 4
    );

    public static $noCheckForum = array(//不考核的吧
        615140 //戒赌
    );

    public static $postCmd = array(
        'postCommit' => 0,
        'threadCommit' => 1,
        'forumCommit' => 2,
        'forumPreCommit' => 3,
        'maskDelete' => 12,
        'calMaskDel' => 13,
        'realDelFrm' => 33,
        'commentAddClick' => 3301,
        'commentSubClick' => 3302,
        'maskGood' => 17,
        'calMaskGood' => 18,
        'maskTop' => 25,
        'calMaskTop' => 26,
        'delGoodClass' => 35,
        'resortGoodClass' => 36,
        'addGoodClass' => 37,
        'reNameGoodClass' => 38,
        'iMaskRelyRemind' => 30303,
        'iStoreThrDel' => 30321,
        'iStoreThrChange' => 30322,
        'iStoreThrRead' => 30323,
        'setUserAtMe' => 2720,
        'iStoreThrAdd' => 30320,
        'iStoreThrClear' => 30325,
        'iStoreThrTagSet' => 30326,
        'upRecPost' => 52001,
        'addRecPost' => 52002,
        'delRecPost' => 52003,
        'frtCanelMskDel' => 39,
        'setTopThrNum' => 41,
        'lottery' => 42,
        'updateFname' => 34,
        'addMaskThrList' => 47,
        'delMaskThrList' => 48,
        'setThrFexClass' => 49,
        'makeThrUp' => 50,
        'maskUserThr' => 44,
        'calMaskUserThr' => 45,
        'bwSaveMemo' => 50009,
        'setThrAttr' => 10000,
        'delThrAttr' => 10001,
        'addMainStatThr' => 10002,
        'delMainStatThr' => 10003,
        'addMainStatPost' => 10004,
        'delMainStatPost' => 10005,
        'clearThreadMask' => 10006,
        'setThreadMask' => 10007,
        'transThrCommit' => 10008,
        'delTransThr' => 10009,
        'setPostAttr' => 10010,
        'addFeedThread' => 10011,
        'addFeedPost' => 10012,
        'personThrCommit' => 10013,
        'calDelTransThr' => 10014,
        'addVirtualTids' => 10015,
        'trnMainStateThr' => 10016,
        'setUserPrivacy' => 10017,
        'clearForumsData' => 10018,
        'hadReadSign' => 10019,
        'setMainStThr'      => 10020,   // 设置主态帖
        'setMainStPost'     => 10021,   // 设置主态回复
        'unsetMainStThr'    => 10022,   // 取消设置主态帖
        'unsetMainStPost'   => 10023,   // 取消设置主态回复
        'stealThread'       => 10024,   // 品牌吧，跨吧转帖
        'PostMask'  => 46,  // 帖子屏蔽
        'CancelPostMask'  => 47,    // 帖子解屏蔽
        'maskDelAppraise' => 10032,//帖子吧内屏蔽
        'calMaskDelAppr'  => 10033,  //恢复吧内屏蔽
    );

    public static $permCmd = array(
        'lockThread' => 901,
        'unlockThread' => 900,
        'updateApply' => 2101,
        'SetAuthUser' => 192,
        'misCmitWordItem' => 30070,
        'mdyUserType' => 30062,
        'setfrmMgr' => 200,
        'delfrmMgr' => 201,
        'volDelFrmMgr' => 206,
        'addApply' => 2100,
        'mgrAudit' => 2102,
        'pmAudit' => 2103,
        'upAppInfo' => 2105,
        'cmtMisProcess' => 2106,
        'setFrmMem' => 2603,
        'modDoorValue' => 2613,
        'setFrmUserRole' => 2660,
        'delFrmUserRole' => 2661,
        'setMemRealName' => 21037,
        'setFrmVideoAdm' => 2601,
        'delFrmVideoAdm' => 2602,
        'setFrmPicAdm' => 2001,
        'delFrmPicAdmin' => 2002,
        'delFrmAssistant' => 228,
        'setFrmPubEditor' => 2636,
        'delFrmPubEditor' => 2637,
        'setFrmDQ' => 2638,
        'delFrmDQ' => 2639,
        'setfrmDQEdit' => 2640,
        'delfrmDQdit' => 2641,
        'newTuShu' => 26201,
        'setfrmMemAlias' => 2631,
        'setfrmAssistant' => 227,
        'setfrmFibType' => 2630,
        'gradeLike' => 3400,
        'gradeUnlike' => 3401,
        'gradeSetBlack' => 3402,
        'gradeUnsetBlack' => 3403,
        'gradeDelTip' => 3404,
        'gradeAddScore' => 3405,
        'gradeAddThread' => 3406,
        'gradeSetScore' => 3408,
        'newPageAddScore' => 30400,
        'gradeAntiThrMrk' => 3409,
        'gradeSetLelName' => 3413,
        'gradeLevelUp' => 3414,
        'setfrmPub' => 2634,
        'applicationResg' => 244,
        'delfrmPublic' => 2635,
        'setVip' => 194,
        'delVip' => 195,
        'setTopSetter' => 196,
        'delTopSetter' => 197,
        'setPostDeleter' => 198,
        'delPostDeleter' => 199,
        'threadShare' => 3410,
    );

    /**
     * [checkTotalBawuTask 吧主考核总开关权限判断]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function checkTotalBawuTask(){
        /**词表判断
         * start
         **/
        $handleWordServer = Wordserver_Wordlist::factory();
        $arrRes = array(
            'is_new_managerapply' => 0,
            'is_check_bawutask' => 0,
            'check_bazhu_score' => 0,
            'check_bawu_score' => 0,
            'check_post_num' => 0,
            'check_sign_num' => 0
        );
        /**开关判断：
         * 吧务任务考核开启开关，
         * 吧主考核分数，
         * 吧务考核分数
         * 新吧主申请制度开关
         **/
        $wordList = array(self::CHECK_NEW_MANGERAPPLY_KEY,self::CHECK_BAWUTASK_KEY,self::CHECK_BAZHU_SCROE_KEY,self::CHECK_BAWU_SCROE_KEY,self::CHECK_SIGN_NUM_KEY,self::CHECK_POST_NUM_KEY);
        $arrItemInfo      = $handleWordServer->getValueByKeys(array_values($wordList), self::NEW_BAWU_RULE_TABLE);
        $isNewManagerapply = $arrItemInfo[self::CHECK_NEW_MANGERAPPLY_KEY] ? 1 : 0;
        $checkBazhuScore = $arrItemInfo[self::CHECK_BAZHU_SCROE_KEY] && intval($arrItemInfo[self::CHECK_BAZHU_SCROE_KEY])>0 ? intval($arrItemInfo[self::CHECK_BAZHU_SCROE_KEY]) : 0;
        $checkBawuScore = $arrItemInfo[self::CHECK_BAWU_SCROE_KEY] && intval($arrItemInfo[self::CHECK_BAWU_SCROE_KEY])>0 ? intval($arrItemInfo[self::CHECK_BAWU_SCROE_KEY]) : 0;
        $isCheckBawuTask = $arrItemInfo[self::CHECK_BAWUTASK_KEY] && ($checkBazhuScore >0 || $checkBawuScore) >0 ? 1 : 0;
        $checkPostNum = $arrItemInfo[self::CHECK_POST_NUM_KEY] && intval($arrItemInfo[self::CHECK_POST_NUM_KEY])>0 ? intval($arrItemInfo[self::CHECK_POST_NUM_KEY]) : 0;
        $checkSignNum = $arrItemInfo[self::CHECK_SIGN_NUM_KEY] && intval($arrItemInfo[self::CHECK_SIGN_NUM_KEY])>0 ? intval($arrItemInfo[self::CHECK_SIGN_NUM_KEY]) : 0;

        $arrRes = array(
            'is_new_managerapply' => $isNewManagerapply,
            'is_check_bawutask' => $isCheckBawuTask,
            'check_bazhu_score' => $checkBazhuScore,
            'check_bawu_score' => $checkBawuScore,
            'check_post_num' => $checkPostNum,
            'check_sign_num' => $checkSignNum
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes);
    }

    /**
     * [checkForumBawuTask 单个吧吧主考核权限判断]
     * @param  [array] $arrInput [description]
     * @return [json]           [description]
     */
    public static function checkForumBawuTask($arrInput){
        /**词表判断
         * start
         **/
        $handleWordServer = Wordserver_Wordlist::factory();
        if (isset($arrInput['forum_id']) && intval($arrInput['forum_id']) > 0){
            $intFid = $arrInput['forum_id'];
            $arrRes = array(
                'is_newrule_white_forum' => 0,
                'is_bawuoff_white_forum' => 0
            );

            //是否在新吧务制度豁免白名单 from 词表
//            $wordList = array($intFid);
//            $arrItemInfo      = $handleWordServer->getValueByKeys(array_values($wordList), self::NEW_MANAGERAPPLY_WHITELIST_TABLE);
//            if ($arrItemInfo[$intFid]){
//                $arrRes['is_newrule_white_forum'] = 1;
//            }

            //是否是新吧主制度豁免白名单 from db
            $arrReq = array(
                'forum_id' => $intFid
            );
            $arrRet = Tieba_Service::call('managerapply','isNewruleWhiteForum',$arrReq);
            if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS != $arrRet['errno']){
                Bingo_Log::warning('call managerapply::isNewruleWhiteForum fail! [input]'.serialize($arrReq).'[output]'.serialize($arrRet));
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL,$arrRes);
            }
            $isNewRuleWhiteForum = isset($arrRet['data']['is_newrule_white_forum']) && $arrRet['data']['is_newrule_white_forum'] ? 1 : 0;
            if ($isNewRuleWhiteForum){
                $arrRes['is_newrule_white_forum'] = 1;
            }

            //是否是吧主下任公示招募豁免白名单
            $wordList = array($intFid);
            $arrItemInfo      = $handleWordServer->getValueByKeys(array_values($wordList), self::BAWU_OFFLINE_WHITELIST_TABLE);
            if ($arrItemInfo[$intFid]){
                $arrRes['is_bawuoff_white_forum'] = 1;
            }
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS,$arrRes);
        }else{
            return false;
        }
    }

    /**
     * [_errRet description]
     * @param  [type] $errno [description]
     * @param  array  $data  [description]
     * @return [type]        [description]
     */
    private static function _errRet($errno,$data=array()){
        $arrOutput = array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data'   => $data,
        );
        return $arrOutput;
    }

}