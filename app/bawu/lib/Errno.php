<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2013-04-17 21:12:10
 */

class Lib_Errno {
    
    const BLOCK_MAX_DAY = 11;
    const ASSIST_BLOCK_MAX_DAY = 12;
    const BLOCK_MAX_NUM = 13;
    const UNKOWN_ERROR = 99;
    
    static public $codes = array (
            
        11 => '����������ܳ���10��',
        12 => 'С��������������ܳ���1��',
        13 => 'ÿ�η����Ŀ���ܳ���50��',
        14 => '�û�Ȩ�޴���',
        15 => '����û�������',
        16 => '��ȡ�����Ϣʧ��',
        21 => '�û�������',
        22 => '�û��ȼ�����',
        23 => '�û��Ѿ��߱���������Ȩ��',
        24 => '�û��Ѿ��ǰ���',
        25 => '�û��Ѿ���������ɫ����',
        26 => '�û���ɫ�Ѿ�����',
        27 => '���ʧ��',
        28 => '������ʧ��',

        100 => '��û��Ȩ�޽��д˲���',
        101 => '��Ҫ�����ֻ���֤��',
        102 => '��Ҫ������֤��',
        103 => '�ֻ���֤��δ��֤ͨ��',
        104 => '������δ��֤ͨ��',
        201 => '��ȡ����Ȩ��ʧ��',
        202 => '��ȡ�����б�ʧ��',
        203 => '��ȡ',
        99 => 'δ֪����',

    );
    
    public static function getErrmsg($errno) {
        if (in_array($errno, array_keys(self::$codes))) {
            return array (
                'errno'  => $errno,
                'errmsg' => self::$codes[$errno],
            );
        }
        
        if (in_array($errno, array_keys(Tieba_Errcode::$usercodes))) {
            return array (
                'errno' => $errno,
                'errmsg' => Tieba_Errcode::$usercodes[$errno],
            );
        }
        
        if (in_array($errno, array_keys(Tieba_Errcode::$codes))) {
            return array (
                'errno' => $errno,
                'errmsg' => Tieba_Errcode::$usercodes[$errno],
            );
        }
        
        return array(
            'errno' => self::UNKOWN_ERROR,
            'errmsg' => $this->codes[self::UNKOWN_ERROR],
        );
    }
}