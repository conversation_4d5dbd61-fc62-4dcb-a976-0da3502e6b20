<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
/**
 * @file BawuStrategy.php
 * <AUTHOR>
 * @date 20140826
 * @brief bawu strategy can be specified
 *  
 **/

class Lib_BawuStrategyCancelUserBlock extends Lib_BawuStrategy {

    static public function checkParams($req) {
        if (empty($req['forum_id']) || empty($req['op_userid']) || !isset($req['op_userip']) || empty($req['user_ids'])) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        } else {
            return self::_succRet(true);
        }
    }

    /**
     * ���perm
     */
    static public function checkBawuPerm($req) {
        $arrInput = array(
            'forum_id' => intval($req['forum_id']),
            'user_id' => intval($req['op_userid']),
            'user_ip' => intval($req['op_userip']),
        );
        $arrPerm = Util_Perm::getUserPerm($arrInput);
        if (!$arrPerm) {
            return Lib_Errno::getErrmsg(Tieba_Errcode::ERR_USER_NO_PERM);
        }
        
        if ($arrPerm['can_type3_audit_post'] || $arrPerm['can_type2_audit_post']) {
            return self::_succRet(true);
        }
        
        return Lib_Errno::getErrmsg(100);
    }
    
}