<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file Conf.php
 * <AUTHOR>
 * @date 20140826
 * @brief conf can be specified
 *  
 **/

class Lib_BawuConf {

    const ADD_GOOD = 'addgood';
    const ADD_TOP = 'addtop';
    const ADD_USER_BLACK = 'adduserblack';
    const ADD_USER_ROLE = 'adduserrole';
    const ADD_USER_BLOCK = 'adduserblock';
    
    const CANCEL_GOOD = 'cancelgood';
    const CANCEL_TOP = 'canceltop';
    const CANCEL_USER_BLACK = 'canceluserblack';
    const CANCEL_USER_ROLE = 'canceluserrole';
    const CANCEL_USER_BLOCK = 'canceluserblock';
    
    const BW_STRATEGY = 'bwstrategy';
    const BW_TRANSFIELD = 'bwtransfield';
    //���������Ӧ�Ĵ�������
    /**
        'forum_id'        => 'int',  //��id
        'op_user'         => 'str',  //�������û���
        'op_userid'       => 'int',  //�����û�id
        'op_role'         => 'str',  //�����û���ɫ
        'op_time'         => 'int',  //����ʱ��
        'op_ip'           => 'int',  //����ip
        'op_ip6'          => 'str',
        'oo_user'         => 'str',  //�������û�
        'oo_userid'       => 'int',  //�������û�id
        'oo_time'         => 'int',  //�������û��ϴδ���ʱ��
        'oo_threadid'     => 'int',  //������id�����ڶ����Ӳ���
        'oo_postid'       => 'int',  //�ظ���id�����ڶ����Ӳ���
        'oo_title'        => 'str',  //�����ظ�����
        'memo'            => 'str',  //�����Ӧ�ı�ע����
        'accept_module'   => 'str',  //���ܽӿ�ģ�����ƣ�Ĭ����bawu
        'accept_function' => 'str',  //���ܽӿ�����
        'from_module'     => 'str',  //���ýӿ�ģ������
        'from_function'   => 'str',  //���ýӿ�����
     */
    public static $opAction = array(
        self::ADD_GOOD => array(
            self::BW_STRATEGY => array(
                //array('Lib_BawuStrategy', 'checkTbs'),
                array('Lib_BawuStrategyAddGood', 'checkParams'),
            ),
            self::BW_TRANSFIELD => array(
                'forum_id' => 'forum_id',   //forum_id : cint
                'op_user' => 'user_name',   //user_name : cstr
                'op_userid' => 'user_id',   //user_id : cint
                'op_ip' => 'user_ip',   //user_ip : cint
                'op_ip6' => 'user_ip6',   //user_ip6 : cstr
                'oo_threadid' => 'thread_id',  //thread_id : cint
            	'from_module' => 'from_module',
            	'from_function' => 'from_function',
            ),
        ),
        self::CANCEL_GOOD => array(
            self::BW_STRATEGY => array(
                //array('Lib_BawuStrategy', 'checkTbs'),
                array('Lib_BawuStrategyCancelGood', 'checkParams'),
            ),
            self::BW_TRANSFIELD => array(
                'forum_id' => 'forum_id',   //forum_id : cint
                'op_user' => 'user_name',   //user_name : cstr
                'op_userid' => 'user_id',   //user_id : cint
                'op_ip' => 'user_ip',   //user_ip : cint
                'op_ip6' => 'user_ip6',   //user_ip6 : cstr
                'oo_threadid' => 'thread_id',  //thread_id : cint
            ),
        ),
        self::ADD_TOP => array(
            self::BW_STRATEGY => array(
                array('Lib_BawuStrategyAddTop', 'checkParams'),
            ),
            self::BW_TRANSFIELD => array(
                'forum_id' => 'forum_id',   //forum_id : cint
                'op_user' => 'user_name',   //user_name : cstr
                'op_userid' => 'user_id',   //user_id : cint
                'op_ip' => 'user_ip',   //user_ip : cint
                'op_ip6' => 'user_ip6',   //user_ip6 : cstr
                'oo_threadid' => 'thread_id',  //thread_id : cint
            ),
        ),
        self::CANCEL_TOP => array(
            self::BW_STRATEGY => array(
                array('Lib_BawuStrategyCancelTop', 'checkParams'),
            ),
            self::BW_TRANSFIELD => array(
                'forum_id' => 'forum_id',   //forum_id : cint
                'op_user' => 'user_name',   //user_name : cstr
                'op_userid' => 'user_id',   //user_id : cint
                'op_ip' => 'user_ip',   //user_ip : cint
                'op_ip6' => 'user_ip6',   //user_ip6 : cstr
                'oo_threadid' => 'thread_id',  //thread_id : cint
            ),
        ),
        self::ADD_USER_BLACK => array(
            self::BW_STRATEGY => array(
                array('Lib_BawuStrategyAddUserBlack', 'checkParams'),
            ),
            self::BW_TRANSFIELD => array(
                'forum_id' => 'forum_id',   //forum_id : cint
                'op_userid' => 'op_uid',    //user_id : cint
                'op_ip' => 'op_uip',
                'op_ip6' => 'op_uip6',
                'oo_userid' => 'user_id',   //
            ),
        ),
        self::CANCEL_USER_BLACK => array(
            self::BW_STRATEGY => array(
                array('Lib_BawuStrategyCancelUserBlack', 'checkParams'),
            ),
            self::BW_TRANSFIELD => array(
                'forum_id' => 'forum_id',   //forum_id : cint
                'op_user' => 'user_name',   //user_name : cstr
                'op_userid' => 'user_id',   //user_id : cint
                'op_ip' => 'user_ip',   //user_ip : cint
                'op_ip6' => 'user_ip6',   //user_ip6 : cstr
            ),
        ),
        
        self::ADD_USER_ROLE => array(
            self::BW_STRATEGY => array(
                array('Lib_BawuStrategyAddUserRole', 'checkParams'),
                array('Lib_BawuStrategyAddUserRole', 'checkPhoneVerified'),
                array('Lib_BawuStrategyAddUserRole', 'checkBawuPerm'),
                array('Lib_BawuStrategyAddUserRole', 'checkUserPerm'),
            ),
            self::BW_TRANSFIELD => array(
                'forum_id' => 'forum_id',    //forum_id : cint
                'op_userid' => 'op_user_id', //user_id : cint
                'oo_userid' => 'user_id', 
                'op_ip' => 'op_uip',         //user_ip : cint
                'op_ip6' => 'op_uip6',         //user_ip : cstr
                'memo' => 'role_name', 
            ),
        ),
        
        self::CANCEL_USER_ROLE => array(
            self::BW_STRATEGY => array(
                array('Lib_BawuStrategyCancelUserRole', 'checkParams'),
                array('Lib_BawuStrategyCancelUserRole', 'checkPhoneVerified'),
                array('Lib_BawuStrategyCancelUserRole', 'checkBawuPerm'),
            ),
            self::BW_TRANSFIELD => array(
                'forum_id' => 'forum_id',    //forum_id : cint
                'op_userid' => 'op_user_id', //user_id : cint
                'oo_userid' => 'user_id', 
                'op_ip' => 'op_uip',         //user_ip : cint
                'op_ip6' => 'op_uip6',         //user_ip : cstr
                'memo' => 'role_name',
            ),
        ),

        self::ADD_USER_BLOCK => array(
            self::BW_STRATEGY => array(
                array('Lib_BawuStrategyAddUserBlock', 'checkParams'),
                array('Lib_BawuStrategyAddUserBlock', 'checkDay'),
                array('Lib_BawuStrategyAddUserBlock', 'checkBawuPerm'),
            ),
        ),
        
        self::CANCEL_USER_BLOCK => array(
            self::BW_STRATEGY => array(
                array('Lib_BawuStrategyCancelUserBlock', 'checkParams'),
                array('Lib_BawuStrategyCancelUserBlock', 'checkBawuPerm'),
            ),
        ),
    );

}