<?php
/**
 * @file StarConf.php
 * <AUTHOR>
 * @date 20150803
 * @brief conf can be specified
 *  
 **/

class Lib_StarConf {
	const ERR_UNKNOWN                 = -1;     // 未知错误
	const ERR_FORUM_WITH_CROWDFUNDING = 1;      // 本吧已经有在众筹的活动
    const ERR_TIME_INVALID            = 2;      // 给出的时间有误
    const ERR_BUS_PIC_NUM_INVALID     = 3;      // 大巴图片个数有误
	const ERR_TIME_BUS_NOT_MATCH      = 4;      // 大巴个数和时间区间不匹配
    const ERR_FORUM_CONFLICT          = 5;      // 巡游吧时间冲突
	const ERR_START_TIME_INVALID      = 6;      // 开始时间小于当前时间
	const ERR_END_TIME_INVALID        = 7;      // 结束时间小于开始时间
	const ERR_TRAVEL_TIME_INVALID     = 8;      // 巡游时间小于结束时间
	const ERR_LOAD_CONF_FAIL          = 9;      // 读取配置文件失败
	const ERR_URL_INVALID             = 10;     // 活动说明贴地址出错
	const ERR_TARGET_URL_INVALID      = 11;	    // 目标宣传页地址出错

	const     STATUS_AUDIT             = 1;          // 待审核
    const     STATUS_CROWDFUNDING_PRE  = 2;          // 应援等待中
    const     STATUS_REFUSE            = 3;          // 审核未通过
    const     STATUS_TRAVEL_DONE       = 4;          // 巡游结束
    const     STATUS_CROWDFUNDING_FAIL = 5;          // 应援失败
    const     STATUS_CROWDFUNDING_ING  = 6;          // 应援中
    const     STATUS_TRAVEL_PRE        = 7;          // 巡游倒计时
    const     STATUS_TRAVEL_ING        = 8;          // 巡游中

    private static $_conf;
    protected static $_arrCategory = array();

	public static $arrErrorMap = array(
		self::ERR_UNKNOWN                 => 'unknown error',
		self::ERR_FORUM_WITH_CROWDFUNDING => 'forum with crowdfunding',
		self::ERR_TIME_INVALID            => 'time invalid',
		self::ERR_BUS_PIC_NUM_INVALID     => 'bus pic num invalid',
		self::ERR_TIME_BUS_NOT_MATCH      => 'time and bus num not match',
		self::ERR_FORUM_CONFLICT          => 'forum time conflict',
		self::ERR_START_TIME_INVALID      => 'start_time must be a future time',
		self::ERR_END_TIME_INVALID        => 'end_time must be bigger than start_time',
		self::ERR_TRAVEL_TIME_INVALID     => 'travel_time must be bigger than end_time',
		self::ERR_LOAD_CONF_FAIL          => 'load conf fail',
		self::ERR_URL_INVALID             => 'url invalid',
		self::ERR_TARGET_URL_INVALID      => 'target_url invalid',
	);

	/**
	 * @desc 读取配置文件
	 * @param [in] strFileName : string : 配置文件名
	 * @return 
	 */
	public static function getConf($strFileName='bawu_pluto_pluto'){
        if(self::$_conf){
            return self::$_conf ;
        }

		$strPath = sprintf('/app/pluto/%s', $strFileName);
        self::$_conf = Bd_Conf::getConf($strPath);

        if(false === self::$_conf){
            Bingo_Log::warning("read conf fail");
            return null;
        }
        
        return self::$_conf ;
    }

    /**
     * @desc 读取策略
     * @param [in] type : uint32_t : 1：默认策略 2：粉丝节定制策略
     * @return
     */
    public static function getCategory($type) {
        // 如果已经读取过策略的话，直接返回
        if (isset(self::$_arrCategory[$type])) {
            return self::$_arrCategory[$type];
        }

        $arrCategory = array();
        $arrParam = array(
            'type' => array($type,),
        ); 
        Bingo_Timer::start('pluto_getCategoryByType');
        $arrRet = Tieba_Service::call('pluto', 'getCategoryByType', $arrParam, null, null, 'post', 'php', 'utf-8');
        Bingo_Timer::end('pluto_getCategoryByType');
        Bingo_Log::warning(print_r($arrParam, true));
        Bingo_Log::warning(print_r($arrRet, true));
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            $strMsg = sprintf('call pluto::getCategoryByType fail with input [%s] output [%s]', serialize($arrParam), serialize($arrRet));
            Bingo_Log::warning($strMsg);
            return false;
        }

        // 存起来，以备后续查用
        $arrCategory = $arrRet['data'][$type];
        self::$_arrCategory[$type] = $arrCategory;

        return $arrCategory;
    }
}


