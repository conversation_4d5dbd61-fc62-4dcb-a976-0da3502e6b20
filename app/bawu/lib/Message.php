<?php
/**
 * Created by PhpStorm.
 * User: huxiaomei
 * Date: 2019/4/4
 * Time: 下午4:52
 */
class  Lib_Message {
    //给被撤吧主的模板
    //在吧里发公告的模板
    //no html in the Text
    public static $offNoticeTitle = "[公告]关于撤销 %s 吧主管理权限的说明";

    public static $noticeBazhuComOffManager = "接到用户举报，经核实吧主%s 违反《百度贴吧吧主制度》https://tieba.baidu.com/mo/q/newapply/rule?from=task，无法在建设 %s吧 内容上、言论导向上发挥应有的模范带头作用。故撤销其吧主管理权限。百度贴吧管理组" ;
    //public static $noticeTaskOffManager = "吧主%s 由于本月在本吧未完成考核任务（周活跃考核和违规行为考核未达标累计到3次）， 已违反《百度贴吧吧主制度》https://tieba.baidu.com/mo/q/newapply/rule?from=task，故撤销其吧主管理权限。百度贴吧管理组";
    public static $noticeTaskOffManager = "经核实吧主%s 未通过普通吧主考核。违反《百度贴吧吧主制度》第八章规定http://tieba.baidu.com/tb/system.html#cnt08 ，无法在建设 %s吧 内容上、言论导向上发挥应有的模范带头作用。故撤销其吧主管理权限。百度贴吧管理组";

    //发送系统通知及push的通知 模版
    public static $tplTaskWarManager = "亲爱的%s吧吧主，您好！由于您上周没有完成吧主活跃考核，将从“本吧考核”分数中扣1分，“本吧考核”剩余%d分（详情可到“吧务中心”的”本吧考核”查看具体规则），长期不登陆不活跃的吧主将失去吧主身份。贴吧官方希望与您一起为建设更活跃更正能量的贴吧生态而努力，感谢您的理解。百度贴吧管理组";
    public static $tplBazhuWarManager = "亲爱的%s吧吧主，您好！由于近期接到贴吧用户对您的投诉，经百度贴吧管理组核实，您在吧内有无故封禁用户、删贴等违规行为。将从“本吧考核”分数中扣1分，“本吧考核”剩余%d分（详情可到“吧务中心”的”本吧考核”查看具体规则），长期有违规操作的吧主将失去吧主身份。请您在今后的贴吧管理中进行改正并加强管理。贴吧官方希望与您一起为建设更活跃更正能量的贴吧生态而努力，感谢您的理解。百度贴吧管理组";
    public static $tplTaskOffManager = "亲爱的%s吧吧主，您好！由于您上周没有完成吧主活跃考核，将从“本吧考核”分数中扣1分，“本吧考核”剩余0分（详情可到“吧务中心”的”本吧考核”查看具体规则），长期不登陆不活跃的吧主将失去吧主身份。现已对您的吧主职务进行撤销处理，感谢您对贴吧的支持，如对此处理有异议，请在24小时内至贴吧反馈中心进行申诉，链接https://ufosdk.baidu.com/?m=Client&a=postViewTieba&appid=222251%s百度贴吧管理组" ;
    public static $tplBazhuWarOffManager = "亲爱的%s吧吧主，您好！由于近期接到贴吧用户对您的投诉，经百度贴吧管理组核实，您在吧内有无故封禁用户、删贴等违规行为。将从“本吧考核”分数中扣1分，“本吧考核”剩余0分（详情可到“吧务中心”的”本吧考核”查看具体规则），长期有违规操作的吧主将失去吧主身份。现已对您的吧主职务进行撤销处理，感谢您对贴吧的支持，如对此处理有异议，请在24小时内至贴吧反馈中心进行申诉，链接https://ufosdk.baidu.com/?m=Client&a=postViewTieba&appid=222251%s 百度贴吧管理组";
    public static $tplBazhuComOffManager = "亲爱的%s吧吧主，您好！由于近期接到贴吧用户对您的投诉，经百度贴吧管理组核实，您在吧内确实存在违反吧主协议的相关操作或未达到吧主协议的相关标准，现已对您的吧主职务进行撤销处理，感谢您对贴吧的支持，如对此处理有异议，请在24小时内至贴吧反馈中心进行申诉，链接https://ufosdk.baidu.com/?m=Client&a=postViewTieba&appid=222251%s 百度贴吧管理组";
    public static $tplLeftTime = '亲爱的%s吧吧主，您好！距离本周结束还有不到24小时，您本周还未完成吧主活跃考核，赶快进入吧内完成吧（详情可到"吧务中心"的"本吧考核"查看具体规则）。 百度贴吧管理组';

    public static $tplBawuWarManager = '亲爱的%s吧吧主，您好！由于近期接到贴吧用户对您的吧务投诉，经百度贴吧管理组核实，您的吧务团队在吧内多次出现违规操作或者发违规贴。鉴于您在贴吧管理中做出的贡献，此次我们对您及吧务团队的行为做出警告（将从“吧务行为考核”分数中扣1分，“吧务行为考核”剩余%d分，点击这里查看考核规则），请您在今后的贴吧管理中进行改正，并加强吧务团队管理。如果此后管理员再次接到同类用户投诉并核查属实，将按照规定对您进行相应处理，谢谢！ 百度贴吧管理组';
    public static $tplBawuWarOffManager = "亲爱的%s吧吧主，您好！由于近期接到贴吧用户对您的吧务投诉，经百度贴吧管理组核实，您的吧务团队在吧内多次出现无故封禁用户或无故删贴行为。（将从“我的考核”分数中扣1分，“我的考核”剩余0分，可到“吧务中心”的”本吧考核”下查看具体规则），现已对您的吧主职务进行撤销处理，感谢您对贴吧的支持，如对此处理有异议，请在24小时内至贴吧反馈中心进行申诉，链接https://ufosdk.baidu.com/?m=Client&a=postViewTieba&appid=222251%s 百度贴吧管理组";
    public static $tplBawuComOffManager = "亲爱的%s吧吧主，您好！由于近期接到贴吧用户对您的吧务投诉，经百度贴吧管理组核实，您的吧务团队在吧内多次出现无故封禁用户或无故删贴行为。（将从“我的考核”分数中扣1分，“我的考核”剩余0分，可到“吧务中心”的”本吧考核”下查看具体规则），现已对您的吧主职务进行撤销处理，感谢您对贴吧的支持，如对此处理有异议，请在24小时内至贴吧反馈中心进行申诉，链接https://ufosdk.baidu.com/?m=Client&a=postViewTieba&appid=222251%s 百度贴吧管理组";

    //发送pc端通知 模版
    public static $sysmsgTitleManager    = "吧主考核通知";
    public static $tplPcTaskWarManager = "亲爱的%s吧吧主，您好！由于您上周没有完成吧主活跃考核，将从“本吧考核”分数中扣1分，“本吧考核”剩余%d分（详情可到“吧务中心”的”本吧考核”查看具体规则），长期不登陆不活跃的吧主将失去吧主身份。贴吧官方希望与您一起为建设更活跃更正能量的贴吧生态而努力，感谢您的理解。百度贴吧管理组";
    public static $tplPcBazhuWarManager = "亲爱的%s吧吧主，您好！由于近期接到贴吧用户对您的投诉，经百度贴吧管理组核实，您在吧内有无故封禁用户、删贴等违规行为。将从“本吧考核”分数中扣1分，“本吧考核”剩余%d分（详情可到“吧务中心”的”本吧考核”查看具体规则），长期有违规操作的吧主将失去吧主身份。请您在今后的贴吧管理中进行改正并加强管理。贴吧官方希望与您一起为建设更活跃更正能量的贴吧生态而努力，感谢您的理解。百度贴吧管理组";
    public static $tplPcTaskOffManager = "亲爱的%s吧吧主，您好！由于您上周没有完成吧主活跃考核，将从“本吧考核”分数中扣1分，“本吧考核”剩余0分（详情可到“吧务中心”的”本吧考核”查看具体规则），长期不登陆不活跃的吧主将失去吧主身份。现已对您的吧主职务进行撤销处理，感谢您对贴吧的支持，如对此处理有异议，请在24小时内至贴吧反馈中心进行申诉，链接<a target=\"_blank\" href=\"http://tieba.baidu.com/hermes/feedback\">http://tieba.baidu.com/hermes/feedback</a></a><br> \n百度贴吧管理组" ;
    public static $tplPcBazhuWarOffManager = "亲爱的%s吧吧主，您好！由于近期接到贴吧用户对您的投诉，经百度贴吧管理组核实，您在吧内有无故封禁用户、删贴等违规行为。将从“本吧考核”分数中扣1分，“本吧考核”剩余0分（详情可到“吧务中心”的”本吧考核”查看具体规则），长期有违规操作的吧主将失去吧主身份。现已对您的吧主职务进行撤销处理，感谢您对贴吧的支持，如对此处理有异议，请在24小时内至贴吧反馈中心进行申诉，链接<a target=\"_blank\" href=\"http://tieba.baidu.com/hermes/feedback\">http://tieba.baidu.com/hermes/feedback</a><br> \n百度贴吧管理组";
    public static $tplPcBazhuComOffManager = "亲爱的%s吧吧主，您好！由于近期接到贴吧用户对您的投诉，经百度贴吧管理组核实，您在吧内确实存在违反吧主协议的相关操作或未达到吧主协议的相关标准，现已对您的吧主职务进行撤销处理，感谢您对贴吧的支持，如对此处理有异议，请在24小时内至贴吧反馈中心进行申诉，链接<a target=\"_blank\" href=\"http://tieba.baidu.com/hermes/feedback\">http://tieba.baidu.com/hermes/feedback</a><br> \n百度贴吧管理组";

    public static $tplPcBawuWarManager = "亲爱的%s吧吧主，您好！由于近期接到贴吧用户对您的吧务投诉，经百度贴吧管理组核实，您的吧务团队在吧内多次出现违规操作或者发违规贴。鉴于您在贴吧管理中做出的贡献，此次我们对您及吧务团队的行为做出警告（将从“我的考核”分数中扣1分，“我的考核”剩余%d分，可到“吧务中心”的”本吧考核”下查看具体规则），请您在今后的贴吧管理中进行改正，并加强吧务团队管理。如果此后管理员再次接到同类用户投诉并核查属实，将按照规定对您进行相应处理，谢谢！ 百度贴吧管理组";
    public static $tplPcBawuWarOffManager = "亲爱的%s吧吧主，您好！由于近期接到贴吧用户对您的吧务投诉，经百度贴吧管理组核实，您的吧务团队在吧内多次出现无故封禁用户或无故删贴行为。（将从“我的考核”分数中扣1分，“我的考核”剩余0分，可到“吧务中心”的”本吧考核”下查看具体规则），现已对您的吧主职务进行撤销处理，感谢您对贴吧的支持，如对此处理有异议，请在24小时内至贴吧反馈中心进行申诉，链接<a target=\"_blank\" href=\"http://tieba.baidu.com/hermes/feedback\">http://tieba.baidu.com/hermes/feedback</a><br> \n百度贴吧管理组";
    public static $tplPcBawuComOffManager = "亲爱的%s吧吧主，您好！由于近期接到贴吧用户对您的吧务投诉，经百度贴吧管理组核实，您的吧务团队在吧内多次出现无故封禁用户或无故删贴行为。（将从“我的考核”分数中扣1分，“我的考核”剩余0分，可到“吧务中心”的”本吧考核”下查看具体规则），现已对您的吧主职务进行撤销处理，感谢您对贴吧的支持，如对此处理有异议，请在24小时内至贴吧反馈中心进行申诉，链接<a target=\"_blank\" href=\"http://tieba.baidu.com/hermes/feedback\">http://tieba.baidu.com/hermes/feedback</a><br> \n百度贴吧管理组";

    //6月发送通知模版
    //pc+客户端模版
    public static $tpl6thBazhuWarOffManager = "亲爱的%s吧吧主，您好！由于近期接到贴吧用户对您的投诉，经百度贴吧管理组核实，您在吧内多次出现无故封禁用户或无故删贴行为。将从“本吧考核”分数中扣1分，“本吧考核”剩余0分，按考核规则，吧主身份将被下任（详情可到“吧务中心“的“本吧考核“查看具体规则）。考虑到您为贴吧的长期贡献，官方决定对您本次处罚进行豁免。从7月1号开始，重新进入正常考核期，请您留意吧主考核条款，贴吧官方希望可以与您共建一个活跃的吧内生态。";
    public static $tpl6thBazhuTaskOffManager = "亲爱的%s吧吧主，您好！由于您上周没有完成吧主活跃考核，将从“本吧考核”分数中扣1分，“本吧考核”剩余0分，按考核规则，吧主身份将被下任（详情可到“吧务中心“的“本吧考核“查看具体规则）。考虑到您为贴吧的长期贡献，官方决定对您本次处罚进行豁免。从7月1号开始，重新进入正常考核期，请您留意吧主考核条款，贴吧官方希望可以与您共建一个活跃的吧内生态。";

    //发送短信模版
    public static $smsTaskOff = "亲爱的吧主，您好！由于您上周没有完成签到次数或者发／回贴次数，现已对您的吧主职务进行撤销处理，感谢您对贴吧的支持，如对此处理有异议，请在24小时内至贴吧反馈中心进行申诉，链接https://ufosdk.baidu.com/?m=Client&a=postViewTieba&appid=222251%s";
    public static $smsBawuComplainOff = "亲爱的吧主，您好！由于近期接到贴吧用户对您的吧务投诉，经百度贴吧管理组核实，您的吧务团队在吧内多次出现无故封禁用户或无故删贴行为，现已对您的吧主职务进行撤销处理，感谢您对贴吧的支持，如对此处理有异议，请在24小时内至贴吧反馈中心进行申诉，链接https://ufosdk.baidu.com/?m=Client&a=postViewTieba&appid=222251%s";
    public static $smsBazhuComplainOff = "亲爱的吧主，您好！由于近期接到贴吧用户对您的投诉，经百度贴吧管理组核实，您在吧内多次出现无故封禁用户或无故删贴行为，现已对您的吧主职务进行撤销处理，感谢您对贴吧的支持，如对此处理有异议，请在24小时内至贴吧反馈中心进行申诉 ，链接https://ufosdk.baidu.com/?m=Client&a=postViewTieba&appid=222251%s";

    public static $smsTaskWarManager = "亲爱的%s吧吧主，您好！由于您上周没有完成吧主活跃考核，将从“本吧考核”分数中扣1分，“本吧考核”剩余%d分（详情可到“吧务中心”的”本吧考核”查看具体规则），长期不登陆不活跃的吧主将失去吧主身份。贴吧官方希望与您一起为建设更活跃更正能量的贴吧生态而努力，感谢您的理解。百度贴吧管理组";
    public static $smsBazhuComplainWar = "亲爱的%s吧吧主，您好！由于近期接到贴吧用户对您的投诉，经百度贴吧管理组核实，您在吧内有无故封禁用户、删贴等违规行为。将从“本吧考核”分数中扣1分，“本吧考核”剩余%d分（详情可到“吧务中心”的”本吧考核”查看具体规则），长期有违规操作的吧主将失去吧主身份。请您在今后的贴吧管理中进行改正并加强管理。贴吧官方希望与您一起为建设更活跃更正能量的贴吧生态而努力，感谢您的理解。百度贴吧管理组";

    CONST PUBLIC_USER_ID = 3222425470;
    CONST BAZHU_USER_ID = 5044059141;
    CONST SYSMSG_CATEGORY_ID = 1;
    CONST BAZHU_CATEGORY_ID = 2;

    const NEW_BAWU_RULE_TABLE = 'new_bawu_rule';//新吧务制度词表
    const SEND_SMS_KEY = 'is_send_sms';//是否开启考核

    public static $intAnnoucementUid = 167570067;
    public static $strAnnoucementUname = "贴吧吧主小管家";
    public static $intAddtopUid =  1565127810;
    public static $strAddtopUname = "贴吧平台化01";
    public static $strDelRoleOpUname = "bazhuscript";
    public static $intDelRoleOpUid = 3447387038;
    public static $intAddtopIp = 195397437;

    const USER_NAME = 'tiebaship01';
    const USER_PWD = 'tiebaship001';
    const BUSS_CODE = 'tiebaship';

    const TEST_SMS_URL = 'http://emsgtest.baidu.com/api/v1/sendSmsByPass.json';//短息发送测试地址
    const SMS_URL = 'http://emsg.baidu.com/api/v1/sendSmsByPass.json';//短息发送地址

    const SUB_STR = '，回复TD退订';

    /**
     * 发系统消息 支持批量用户同一文案
     * @param $arrInput
     * @return array
     */
    public static function sendMessage($arrInput) {
        Bingo_Log::notice(__METHOD__ . '-input params:'.serialize($arrInput));
        $arrCheckParams = array('to_user_ids' , 'content', 'pc_content');
        if (!self::_checkInput($arrCheckParams, $arrInput)) {
            Bingo_Log::warning(__METHOD__ . ' input params error :'.serialize($arrInput));
            return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        if (!is_array($arrInput['to_user_ids'])) {
            Bingo_Log::warning(__METHOD__ . ' input params error to_user_ids not array :'.serialize($arrInput));
            return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrToUserIds = $arrInput['to_user_ids'];
        foreach ($arrToUserIds as $intToUserId) {
            $arrParams = array(
                'to_user_id' => $intToUserId,
                'content' => $arrInput['content']
            );
            //发系统消息
            $arrOut = self::sendSystemMessage($arrParams);
            if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
                Bingo_Log::warning(__METHOD__ . '-send-system-message fail input:' . serialize($arrParams) . ' output:' . serialize($arrOut));
            }
            //发pc消息
            $categoryId = self::BAZHU_CATEGORY_ID;
            $title = self::$sysmsgTitleManager;
            $pcConent = $arrInput['pc_content'];
            $arrOut = self::sendSysmsg($intToUserId,$categoryId,$title,$pcConent);
            if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
                Bingo_Log::warning(__METHOD__ . '-send-pc-system-message fail input:' . serialize($arrParams) . ' output:' . serialize($arrOut));
            }

            //6月不发送短信，7月后才发送短信，需加上词表判断
            if (time() >= 1561910400){
                //短信开关判断
                $handleWordServer = Wordserver_Wordlist::factory();
                $wordList = array(self::SEND_SMS_KEY);
                $arrItemInfo      = $handleWordServer->getValueByKeys(array_values($wordList), self::NEW_BAWU_RULE_TABLE);
                $isSendSms = $arrItemInfo[self::SEND_SMS_KEY] && intval($arrItemInfo[self::SEND_SMS_KEY])>0 ? 1 : 0;
                if ($isSendSms && isset($arrInput['sms_content']) && !empty($arrInput['sms_content'])){
                    $arrParams = array(
                        'to_user_id' => $intToUserId,
                        'content' => $arrInput['sms_content']
                    );
                    $arrOut = self::sendSms($arrParams);
                    if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
                        Bingo_Log::warning(__METHOD__ . '-send-sms-message fail input:' . serialize($arrParams) . ' output:' . serialize($arrOut));
                    }
                }
            }
        }
        return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, array());
    }

    /**
     * 客户端发系统通知+push
     * @param $arrInput
     * @return array
     */
    public static function sendSystemMessage($arrInput) {
        Bingo_Log::notice(__METHOD__ . '-input params:'.serialize($arrInput));
        $arrCheckParams = array('to_user_id' , 'content',);
        if (!self::_checkInput($arrCheckParams, $arrInput)) {
            Bingo_Log::warning(__METHOD__ . ' input params error :'.serialize($arrInput));
            return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intToUserId = intval($arrInput['to_user_id']);
        $strContent = $arrInput['content'];
        $userId = isset($arrInput['user_id']) && $arrInput['user_id'] > 0 ? $arrInput['user_id'] : Lib_Message::PUBLIC_USER_ID;
        $arrSendInput = array(
            'user_id'    => $userId,  // 贴吧官方
            'to_user_id' => $intToUserId,
            'msg_type'   => 1,
            'duration'   => 0,
            'content'    => $strContent,
            'user_type'  => 4,
            'record_id'  => -1,
            'is_preview' => 0
        );
        $sendOutput = Tieba_Service::call('msgpush', 'sendMuticastPersonalMsg', $arrSendInput, null, null, 'post', null, 'utf-8');
        if (!self::_checkRes($sendOutput)) {
            Bingo_Log::warning(__METHOD__.'-call-sendMuticastPersonalMsg fail input:'.serialize($arrInput).' output:'.json_encode($sendOutput));
            return self::_buildReturn(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, $sendOutput);
        }
        return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, $sendOutput);
    }

    /**
     * 
     */
    public static function sendBazhuMessage($arrInput) {
        Bingo_Log::notice(__METHOD__ . '-input params:'.serialize($arrInput));
        $arrCheckParams = array('to_user_id' , 'content', 'url', 'img_url');
        if (!self::_checkInput($arrCheckParams, $arrInput)) {
            Bingo_Log::warning(__METHOD__ . ' input params error :'.serialize($arrInput));
            return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $userId = isset($arrInput['user_id']) && $arrInput['user_id'] > 0 ? $arrInput['user_id'] : Lib_Message::BAZHU_USER_ID;
        $title = isset($arrInput['title']) ? $arrInput['title'] : '吧主通知给你发了消息';
        $arrSendInput = array(
            'title' => strval($title),
            'content'   => strval($arrInput['content']),
            'url'       => strval($arrInput['url']),
            'uid'       => array(strval($arrInput['to_user_id'])),
            'image_url'     => strval($arrInput['img_url']),
            'user_id'   => strval($userId),
            'user_name' => '吧主通知',
            'task_id'   => '000000',
            'service_id'    => '106',
            'user_type' => 4,
            'msg_type'  => 7,
        );
        if($arrInput['to_user_id'] == 5320195642){
            Bingo_Log::warning(serialize($arrSendInput));
        }
        // if($ret['push_switch_type'] == 1 || $arrThreadInfo['user_id'] == '1867988846'|| $arrThreadInfo['user_id'] == '4814660556'|| $arrThreadInfo['user_id'] == '3322026'|| $arrThreadInfo['user_id'] == '850478934'){
        self::_newPipePush($arrSendInput);
        self::_newPushMsg($arrSendInput);
        
        return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS);
    }

    public static function getWordValue(){
        $handleWordServer = Wordserver_Wordlist::factory();
        $arrKeys = array('push_switch_type','rand_min', 'rand_max');
        $arrRet = $handleWordServer->getValueByKeys($arrKeys, 'tb_wordlist_redis_ForumRule_Switch');
        if (empty($arrRet)) {
            Bingo_Log::warning(sprintf('call wordlist fail. tbname[%s] keys[%s]', self::$tbWordName, serialize($arrKeys)));
            return false;
        }
        return $arrRet;
    }

    public static function _newPipePush($arrInput) {
        if (!self::_checkParam($arrInput, 'task_id', 'title', 'content', 'url', 'uid', 'user_id', 'user_name', 'user_type', 'msg_type')){
            Bingo_Log::warning(__METHOD__ . ' input params error :'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $input = array(
            "service_id"   => isset($arrInput['service_id']) ? $arrInput['service_id'] : 0,
            'task_id'      => $arrInput['task_id'],
            "title"     => $arrInput['title'],
            "body"      => $arrInput['content'],
            "url"       => $arrInput['url'],
            "image_url" => $arrInput['image_url'],
            "target_uid" => $arrInput['uid'],
            "user_id"    =>  $arrInput['user_id'],
            "user_name"  =>  $arrInput['user_name'],
            "user_type"  =>  $arrInput['user_type'],
            "group_type" =>  $arrInput['group_type'],
        );
        ral_set_idc("nj");//目前只有南京的机器
        $res = Tieba_Service::call('tieba_push', 'newPipePushForPhpByUid', $input, null, null, 'post', 'json', 'utf-8');
        if (!is_array($res)) {
            $res = json_decode(trim($res,'[]'), true);
        }
        if (false == $res || Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            Bingo_Log::warning(__METHOD__ . " get tieba_push:newPipePushForPhp error, input=" . serialize($input) . " and output=" . serialize($res));
            return false;
        }
        return true;
    }
    private static function _getGroupIdByUid($uids) {

        if (!empty($uids) && is_array($uids)) {
            $input = array(
                "user_ids"      =>  $uids,
                'need_pmsg' => 0,
            );
            $res = Tieba_Service::call('im', 'queryPlatformGroupByUid', $input, null, null, 'post', 'php', 'utf-8');
            if ($res == false || empty($res['groups'])) {
                Bingo_Log::warning(__METHOD__ . " call im:queryPlatformGroupByUid failed, input=" .serialize($input). " and output=" . serialize($res));
                return false;
            }
            return array_column($res['groups'], 'group_id');
        }
        return false;
    }
    public static function _newPushMsg($arrInput) {
        $groupIds = self::_getGroupIdByUid($arrInput['uid']);
        if (false == $groupIds) {
            Bingo_Log::warning(__METHOD__ . " get groupIds by uids error, input=" . serialize($arrInput['uid']) . " and output=" . serialize($groupIds));
            return false;
        }
        $strGroupIds = array();
        foreach ($groupIds as $groupId){
            $strGroupIds[] = strval($groupId);
        }
        $input = array(
            "task_id" => $arrInput['task_id'],
            "service_id" => isset($arrInput['service_id']) ? $arrInput['service_id'] : 0,
            "title" => $arrInput['title'],
            "text" => $arrInput['content'],
            "url" => $arrInput['url'],
            "image_url" => $arrInput['image_url'],
            "group_id" => $strGroupIds,
            "user_id" => $arrInput['user_id'],
            "user_name" => $arrInput['user_name'],
            "user_type" => $arrInput['user_type'],
            "msg_type" => $arrInput['msg_type'],
        );
        ral_set_idc("nj");//目前只有南京的机器
        $res = Tieba_Service::call('tieba_push', 'pushMsgLogicForPhp', $input, null, null, 'post', 'json', 'utf-8');
        if (!is_array($res)) {
            $res = json_decode(trim($res,'[]'), true);
        }
        if (false == $res || Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            Bingo_Log::warning(__METHOD__ . " get tieba_push:pushMsgLogicForPhp error, input=" . serialize($input) . " and output=" . serialize($res));
            return false;
        }
        return true;
    }

    public static function pushToUeg($delReason,$arrThreadInfo,$arrUserInfo){
        $arrDateItem = array(
            'title' => $arrThreadInfo['title'],
            'thread_id' => $arrThreadInfo['thread_id'],
            'user_id' => $arrThreadInfo['user_id'],
            'user_name' => $arrUserInfo['user_name'],
            'content' => $arrThreadInfo['post_content'],
            'delete_reason'=>$delReason,
            'des' => $delReason,
            'userNickName' => $arrUserInfo['user_nickname'],
            'user_icon'  => isset($arrUserInfo['iconinfo'][0]['icon'])? $arrUserInfo['iconinfo'][0]['icon'] : '',
            'forum_name' => $arrThreadInfo['forum_name'],
            'forum_id' => $arrThreadInfo['forum_id']
        );
        $arrInput = array(
            'project_id' => 2505,
            'datas' => array($arrDateItem)
        );
        $arrOutput = Tieba_Service::call('uegnaudit', 'mImportPostData', $arrInput,null, null, 'post', 'php', 'utf-8');
        if (false == $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            Bingo_Log::warning(__METHOD__ . " uegnaudit mImportPostData error");
            return false;
        }
    }

    /**
     * pc端发信息
     * @param uint64_t $userId 接收消息用户id
     * @param uint32_t $categoryId 通知分类ID  1:系统消息，2:吧主消息 3:T豆消息 4:活动通知
     * @param string $title 通知标题
     * @param string $content 通知内容，支持富文本
     * @param string $callFrom 发送来源 如uegmis_postrecover
     * @param string $ie
     * @return bool
     */
    public static function sendSysmsg($userId, $categoryId, $title, $content){
        $arrInput = array(
            'req' => array(
                'category_id' => $categoryId, // 通知分类ID  1:系统消息，2:吧主消息 3:T豆消息 4:活动通知
                'user_id'     => $userId,
                'title'       => $title,
                'content'     => $content,
                'call_from'   => 'check_bawutask',
            ),
        );
        $ret = Tieba_Service::call('sysmsg', 'sendSysmsg', $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($ret === false || (isset($ret['errno']) && intval($ret['errno']) !== Tieba_Errcode::ERR_SUCCESS)) {
            Bingo_Log::warning(__FUNCTION__." call service failed : sendSysmsg, input =".serialize($arrInput)." output =".serialize($ret));
            return false;
        }
        return $ret['ret'];
    }

    /**
     * 给用户发短信
     * @param $arrInput
     * @return array
     */
    public static function sendSms($arrInput) {
        Bingo_Log::notice(__METHOD__ . '-input params:'.serialize($arrInput));
        $arrCheckParams = array('to_user_id' , 'content',);
        if (!self::_checkInput($arrCheckParams, $arrInput)) {
            Bingo_Log::warning(__METHOD__ . ' input params error :'.serialize($arrInput));
            return self::_buildReturn(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $intToUserId = intval($arrInput['to_user_id']);
        $strContent = $arrInput['content'];
        if (false === strpos($strContent, self::SUB_STR)) {
            $strContent = $strContent . self::SUB_STR; // 营销内容要加上'回复TD退订',否则发不出去了
        }
        $extId = $arrInput['extId'];

        $strUserName = self::USER_NAME;
        $strPassword = self::USER_PWD;
        $msgDest = $intToUserId;
        $msgContent = $strContent;
        $bussCode = self::BUSS_CODE;

        $arrPost = array(
            'msgDest' => $msgDest, //passport user_id
            'msgContent' => $msgContent, //内容
            //'scheduledDate' => date('Y-m-d H:i:s', time()),
        );
        if (!empty($extId)) {
            $arrPost['extId'] = $extId;
        }
        $strPost = json_encode($arrPost);
        $signature = md5($bussCode. $strUserName . $strPassword . $strPost);

        $ch = curl_init();
        $arrThisHeader = array(
            "content-type: application/json",
            'username: '.$strUserName,
            'bussCode: '.$bussCode,
            'signature: '.$signature,
        );
        Bingo_Log::notice(__METHOD__.'curl body ' . $strPost);
        Bingo_Log::notice(__METHOD__. '-curl header '.json_encode($arrThisHeader));

        curl_setopt($ch, CURLOPT_HTTPHEADER, $arrThisHeader);
        curl_setopt($ch, CURLOPT_URL, self::SMS_URL);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $strPost);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_VERBOSE, true);

        $result = curl_exec($ch);
        Bingo_Log::notice(__METHOD__.'-curl result '.json_encode($result));
        curl_close($ch);

        if(false === $result || empty($result)) {
            Bingo_Log::warning(__METHOD__."call-send_sms fail");
            return self::_buildReturn(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, $result);
        }
        $arrResult = Bingo_String::json2array($result);
        if (1000 != $arrResult['result']) {
            Bingo_Log::warning(__METHOD__."call-send_sms fail");
            return self::_buildReturn(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, $arrResult);
        }

        return self::_buildReturn(Tieba_Errcode::ERR_SUCCESS, $arrResult);
    }

    /**
     * 发公告到相应的吧里面
     * @param unknown_type $arrInput
     * @return true false
     */
    public static function sendNotice($arrInput){
        $needParams = array('user_name','user_id','forum_id','forum_name','title','notice');
        $noEmptyParams = array('id','user_id','forum_id','forum_name','fail_type');
        foreach ($needParams as $k => $v){
            if (!array_key_exists($v,$arrInput)){
                Bingo_Log::warning("call bawu::noticeManagerapply fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }else if (in_array($v,$noEmptyParams) && empty($arrInput[$v])){
                Bingo_Log::warning("call bawu::noticeManagerapply fail Tieba_Errcode::ERR_PARAM_ERROR. input[".serialize($arrInput)."]");
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
        }

        if(intval($arrInput['user_id'])<=0 || intval($arrInput['forum_id'])<=0 || empty($arrInput['user_name']) || empty($arrInput['forum_name'])){
            Bingo_Log::warning('param error:'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        Bingo_Log::pushNotice('user_id', $arrInput['user_id']);
        Bingo_Log::pushNotice('forum_id',      $arrInput['forum_id']);
        Bingo_Log::pushNotice('manager_uname', $arrInput['user_name']);
        $title = $arrInput['title'];
        $content = $arrInput['notice'];

        $ret = self::addThread(self::$intAnnoucementUid, self::$strAnnoucementUname, 1, $arrInput['forum_id'], $arrInput['forum_name'], $title,$content);
        $intCnt = 0;
        while(($intCnt++ < 2) && (false === $ret)){
            $ret = self::addThread(self::$intAnnoucementUid, self::$strAnnoucementUname, 1, $arrInput['forum_id'], $arrInput['forum_name'], $title,$content);
        }
        if(false === $ret){
            Bingo_Log::warning('post annoucement failed!');
            return false;
        }
        $intTid = $ret['thread_id'];
        Bingo_Log::pushNotice('thread_id',$intTid);
        $ret = self::addTop($intTid, self::$intAddtopUid, self::$strAddtopUname, $arrInput['forum_id'], $arrInput['forum_name']);
        $intCnt = 0;
        while(($intCnt++ < 2) && (false === $ret)){
            usleep(10);
            $ret = self::addTop($intTid, self::$intAddtopUid, self::$strAddtopUname, $arrInput['forum_id'], $arrInput['forum_name']);
        }
        if(false === $ret){
            Bingo_Log::warning('post annoucement failed!');
            //return false;
        }
        return $intTid;
    }

    /**
     * 发主题帖
     * @param [type] $intOpUid   [description]
     * @param [type] $strOpUname [description]
     * @param [type] $intOpUip   [description]
     * @param [type] $intFid     [description]
     * @param [type] $strFname   [description]
     * @param [type] $title      [description]
     * @param [type] $content    [description]
     * @param string $ie         [description]
     * @return
     */
    public static function addThread($intOpUid, $strOpUname, $intOpUip, $intFid, $strFname, $title, $content,$ie = 'utf-8'){
        $arrInput = array(
            'req' => array( //提交输入
                'product_private_key' => 'pc_frs', //产品标识
                'forum_id'   => $intFid,
                'forum_name' => $strFname, //吧名称
                'user_name'  => $strOpUname, //用户名称
                'user_id'    => $intOpUid,
                'user_ip'    => $intOpUip,
                'title'      => $title, //标题
                'content'    => $content, //内容
                'vcode_free_gate' => 1, //验证码
                'call_from'  => 'pc_frs',
            ),
        );
        $arrOutput = Tieba_Service::call('post', 'addThread', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            $strMsg = sprintf('call %s failed,input[%s]output[%s]','post:addThread',serialize($arrInput),serialize($arrOutput));
            Bingo_Log::warning($strMsg);
            return false;
        }
        return $arrOutput['res'];
    }
    /**
     * 置顶
     * @param [type] $intThreadId [description]
     * @param [type] $intOpUid    [description]
     * @param [type] $strOpUname  [description]
     * @param [type] $intFid      [description]
     * @param [type] $strFname    [description]
     * @param string $ie          [description]
     * @return
     */
    public static function addTop($intThreadId, $intOpUid, $strOpUname, $intFid, $strFname, $ie = 'utf-8'){
        $arrInput = array(
            'req' => array(
                'user_id'    => $intOpUid,
                'user_ip'    => self::$intAddtopIp,
                'user_name'  => $strOpUname,
                'forum_id'   => $intFid,
                'forum_name' => $strFname,
                'thread_id'  => $intThreadId,
            ),
        );
        $arrOutput = Tieba_Service::call('post', 'addTop',$arrInput, null, null, 'post', 'php', 'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            $strMsg = sprintf('call %s failed,input[%s]output[%s]','post:addTop',serialize($arrInput),serialize($arrOutput));
            Bingo_Log::warning($strMsg);
            //echo $strMsg;
            return false;
        }
        return true;
    }

    /**
     * 构建返回
     * @param $intErrno
     * @param null $arrOutput
     * @return array
     */
    private static function _buildReturn($intErrno = Tieba_Errcode::ERR_SUCCESS, $arrOutput = null)
    {
        $strErrmsg = Tieba_Error::getErrmsg($intErrno);
        $arrResult = array(
            'errno'  => $intErrno,
            'errmsg' => $strErrmsg,
        );
        if ( $arrOutput !== null ){
            $arrResult['data'] = $arrOutput;
        }

        return $arrResult;
    }

    /** [sendMsg 发送通知]
     * @param [type] $arrInput [description]
     * @return
     */
    public static function sendMsg($toUserId,$msgContent,$pcMsgContent='',$smsContent=''){
        //参数校验
        if (empty($toUserId) || empty($msgContent) || empty($pcMsgContent)){
            Bingo_Log::warning("sendMsg input params invalid. [".serialize($toUserId).'_'.$msgContent.'_'.$pcMsgContent."]");
            return false;
        }
        //批量发送系统通知和push、短信
        $arrReq = array(
            'to_user_ids' => $toUserId,
            'content' => $msgContent,
            'pc_content' => $pcMsgContent
        );
        if (!empty($smsContent)){
            $arrReq['sms_content'] = $smsContent;
        }
        $arrRes = Lib_Message::sendMessage($arrReq);
        return $arrRes;
    }

    /** [getMsgConent 获取通知内容]
     * @param [type] $arrInput [description]
     * @return
     */
    public static function getMsgContent($failType,$role,$isOffline=0,$leftScore=0,$forumName='贴',$ufoUrl=''){
        if (in_array($failType,Lib_NewRule::$taskFailType)){
            $contentMsg = $isOffline ? Lib_Message::$tplTaskOffManager : Lib_Message::$tplTaskWarManager;
            $pcContentMsg = $isOffline ? Lib_Message::$tplPcTaskOffManager : Lib_Message::$tplPcTaskWarManager;
        }else if (in_array($failType,Lib_NewRule::$warFailType)){
            if ('manager' == $role){
                $contentMsg = $isOffline ? Lib_Message::$tplBazhuWarOffManager : Lib_Message::$tplBazhuWarManager;
                $pcContentMsg = $isOffline ? Lib_Message::$tplPcBazhuWarOffManager : Lib_Message::$tplPcBazhuWarManager;
            }else{
                $contentMsg = $isOffline ? Lib_Message::$tplBawuWarOffManager : Lib_Message::$tplBawuWarManager;
                $pcContentMsg = $isOffline ? Lib_Message::$tplPcBawuWarOffManager : Lib_Message::$tplPcBawuWarManager;
            }
        }else if (in_array($failType,Lib_NewRule::$complainOffFailType)){
            if ('manager' == $role){
                $contentMsg = Lib_Message::$tplBazhuComOffManager;
                $pcContentMsg = Lib_Message::$tplPcBazhuComOffManager;
            }else{
                $contentMsg = $isOffline ? Lib_Message::$tplBawuComOffManager : Lib_Message::$tplBawuWarManager;
                $pcContentMsg = $isOffline ? Lib_Message::$tplPcBawuComOffManager : Lib_Message::$tplPcBawuWarManager;
            }
        }

        $smsContentMsg = '';
        $forumName = !empty($forumName) ? $forumName : '贴';
        if (!$isOffline){
            $contentMsg = sprintf($contentMsg,$forumName,$leftScore);
            $pcContentMsg = sprintf($pcContentMsg,$forumName,$leftScore);
            //还剩1分才有短信
            if (intval($leftScore) === 1){
                if (in_array($failType,Lib_NewRule::$taskFailType)){
                    $smsContentMsg = sprintf(Lib_Message::$smsTaskWarManager,$forumName,$leftScore);
                }else if (in_array($failType,Lib_NewRule::$complainOffFailType) || in_array($failType,Lib_NewRule::$warFailType)){
//                    $smsContentMsg = 'manager' == $role ? Lib_Message::$smsBazhuComplainOff : Lib_Message::$smsBawuComplainOff;
                    $smsContentMsg = sprintf(Lib_Message::$smsBazhuComplainWar,$forumName,$leftScore);
                }
            }
        }else{
            $ufoUrl = '';//todo list 暂时先取消url后面的参数
            $pcContentMsg = sprintf($pcContentMsg,$forumName,$leftScore);
            $contentMsg = sprintf($contentMsg,$forumName,$ufoUrl);
        }
        $msgArr = array(
            'msg_content' => $contentMsg,
            'pc_msg_content' => $pcContentMsg,
            'sms_content' => $smsContentMsg
        );
        return $msgArr;
    }

    /** [getNoticeContent 获取公示内容]
     * @param [type] $arrInput [description]
     * @return
     */
    public static function getNoticeContent($failType,$userName,$forumName){
        if (in_array($failType,Lib_NewRule::$taskFailType)){
            $contentMsg = Lib_Message::$noticeTaskOffManager;
        }else if (in_array($failType,Lib_NewRule::$warFailType) || in_array($failType,Lib_NewRule::$complainOffFailType)){
            $contentMsg = Lib_Message::$noticeBazhuComOffManager;
        }
        $contentMsg = sprintf($contentMsg,$userName,$forumName);
        $msgArr = array(
            'notice_content' => $contentMsg,
        );
        return $msgArr;
    }

    /**
     * 检查入参
     * @param $arrArgList
     * @param $arrInput
     * @return bool
     */
    private static function _checkInput($arrArgList, $arrInput)
    {
        foreach ($arrArgList as $strArg){
            if (!isset($arrInput[$strArg]) || empty($arrInput[$strArg])){
                Bingo_Log::warning('arg ['.$strArg.'] is not existed.');
                return false;
            }
        }
        return true;
    }

    /**
     * 检查HTTP结果
     * @param $arrRes
     * @return bool
     */
    private static function _checkRes($arrRes)
    {

        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            return false;
        }
        return true;
    }

    private static function  _checkParam(){
        $args = func_get_args();
        if(null == $args || empty($args) || null == $args[0] || empty($args[0]) || !is_array($args[0])){
            return false;
        }
        $arrInput = $args[0];
        $count = count($args);
        for ($i = 1; $i < $count; $i++){
            if(!isset($arrInput[$args[$i]])){
                return false;
            }
        }
        return true;
    }

    /**
	 * [_errRet description]
	 * @param  [type] $errno [description]
	 * @return [type]        [description]
	 */
	private static function _errRet($errno){
	    return array(
	        'errno' => $errno,
	        'errmsg' => Tieba_Error::getErrmsg($errno),
	    );
	}
}