<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2013-04-17 21:12:10
 */
class Util_Const {
	/**
	 *����ɾ������
	 *
	 */
	public static $postDealStatusWaiting = 1;
	public static $postDealStatusSuccess  = 2;
	public static $postDealStatusFail  = 3;
	public static $postDealStatusNotEffect = 4;//�ѱ��˴����
	public static $errorPostIsRecover = 100;
	
	public static $bawuPostWaitApply = 1;//��ǰ̨����
	public static $bawuPostDealing = 2;
	public static $bawuPostFail = 3;
	
	public static $bawuPostRestoreDealing = 1;
	public static $bawuPostRestoreFail = 3;
	
	/**
	 *ɾ��ԭ�� 
	 * 
	 */
	public static $starBaReasonCode = 6;
	public static $disuReasonCode = 7;
	public static $jianzhiReasonCode = 8;
	public static $wafenReasonCode = 9;
	public static $dailiReasonCode = 10;
	public static $zhengrongReasonCode = 11;
	public static $minganReasonCode = 12;
	public static $zhaohuanReasonCode = 13;
	public static $jiqiReasonCode = 14;
	/**
	 * �����ڳ���
	 */
	public static $protectUnBySelf = 0;
	public static $protectBySelf = 1;
	public static $protectServiceTypeVcode = 'vcode';//��֤��
	public static $protectServiceTypeMask = 'mask';//����
	public static $protectServiceTypeBlockmask = 'blockmask';//�������
	public static $protectServiceTypeBlock	= 'gblock';//���
	public static $protectServiceTypeAll	= 'all';//����������
	public static $vcodePreventMakeup	= 'pvcode';//acts_ctrl��ȫվ����֤��
	public static $opGroupSystem = 'system';
	public static $opGroupBawu = 'bawu';
	public static $opGroupAdmin = 'admin';
	public static $addProtectCallFrom = 'forum_pmc';
	public static $addProtectCmdNo	= 55500;
	
	/**
	 * ������δ���op_id 
	 * 
	 */
	public static $maskBlockOpId = 1095748887; 
	/**
	 * ���ȿ��Ƴ���
	 */
	public static $actsCtrlName = 'ueg';
	public static $actsCtrlCmdAddPhone = 'addphone';
	public static $actsCtrlCmdMarkImg = 'markimage';
	public static $actsCtrlMaxAddPhone = 2;
	public static $actsCtrlMaxMarkImg = 3;
	//�ͻ�����֤�����ȿ���
	public static $actsCtrlCaptcha = 'CaptchaTask';
	/**
	 * ���߹�������
	 */
	public static $appealTypePhone = 0;
	public static $appealTypeMarkimg = 1;
	public static $appealTypeManual = 2;
	public static $appealTypeResetPwd = 3;
	public static $appealStatusUnAppeal = 0;
	public static $appealStatusAppealling = 1;
	public static $appealStatusSucc = 2;
	public static $appealStatusFail = 3;
	public static $appealStatusException = 4;
	public static $appealStatusAutoDealed = 5;
	public static $opUserTypeMachine = 0;
	public static $opUserTypePm = 1;
	public static $opUserTypeForumManager = 2;
	public static $markimgMaxHours = 4;
	public static $manualMaxHours = 24;
	public static $appealUnRead = 0;
	public static $appealReaded = 1;
	/**
	 * �ֻ���֤��س���
	 */
	public static $phoneTokenKey = 's1s*d?d0()762s';
	public static $phoneTpl = 'tb';
	public static $phoneCheckPostUrl = 'http://10.26.7.109:8200/v2/?securityforceverify';
	public static $phoneSendMethod = 'send';
	public static $phoneCheckMethod = 'checkvcode';
	public static $phoneSendCodeTimeLast = 60;
	
	/**
	 * �˹�������
	 */
	public static $manuelStatusAppealling = 0;
	public static $manuelStatusSucc = 1;
	public static $manuelStatusFail = 2;
	public static $manuelStatusInterrupted = 3;
	public static $manuelStatusException = 4;
	public static $appealManualNewParamAppealCode = 'apeal_code';
	public static $appealManualNewParamAppealUid = 'apeal_uid';
	public static $appealManualNewParamAppealType = 'apeal_type';
	public static $appealManualNewParamAppealNum = 'apeal_num';
	public static $appealManualNewParamAppealReason = 'apeal_reason';
	public static $appealManualNewParamAppealFlag = 'apeal_flag';
	public static $appealManualNewParamPunishUid = 'punish_uid';
	public static $appealManualNewParamPunishUname = 'punish_uname';
	public static $appealManualNewParamPunishReason = 'punish_reason';
	public static $appealManualNewParamPunishStartTime = 'punish_start_time';
	public static $appealManualNewParamPunishEndTime = 'punish_end_time';
	public static $appealManualNewParamPunishFid = 'punish_fid';
	public static $appealManualNewParamPunishByman = 'punish_byman';
	public static $appealManualParamAppealCode = 'appeal_code';
	public static $appealManualParamUser = 'user';
	public static $appealManualParamUserId = 'user_id';
	public static $appealManualParamUserIp = 'user_ip';
	public static $appealManualParamForum = 'forum';
	public static $appealManualParamPunishType = 'appeal_type';
	public static $appealManualParamContent = 'reason';
	public static $appealManualParamResult = 'result';
	public static $appealManualParamMessage = 'message';
	public static $appealManualParamPostIds = 'pids';
	public static $appealManualRalService = 'tousu_old';
	public static $appealManualUrl = '/tousu-mis/tousu/appeal?action=add';
	public static $appealManualLoginUrl = '<a href="http://passport.baidu.com/passchange" target="_blank">�ʺ������޸�</a>';
	public static $appealManualAppealUrl = 'http://tieba.baidu.com/pmc/manual?msgid=';
	public static $appealBawuManualAppealUrl = 'http://tieba.baidu.com/pmc/manual?bawu=1&msgid=';
	public static $appealManualLoginUrlStr = '{%loginurl%}';
	public static $appealManualAppealUrlStr = '{%appealurl%}';
	public static $appealBawuManualAppealUrlStr = '{%bawuappealurl%}';
	public static $appealManualMaxTimes = 99;
	
	/**
	 * ��ѯ����
	 */
	public static $queryMsgByTime = 0;
	public static $queryMsgByStatus = 1;
	public static $queryMsgPageSize = 10;
	/**
	 * ��ͼ��س���
	 */
	public static $imageListCount = 50;
	public static $blackImageMinRate = 0.2;
	public static $blackImageMaxRate = 0.33;
	public static $minWhiteAccRate = 0.7;
	public static $minBlackAccRate = 0.6;
	public static $userMarkImgGradeLimit = 8;
	public static $baseTypeWhite = 0;
	public static $baseTypeBlack = 1;
	public static $markImgStatusNew = 0;
	public static $markImgStatusSucc = 1;
	public static $markImgStatusFail = 2;
	public static $markImgStatusInterrupted = 3;
	public static $markImgStatusFinished = 4;
	public static $markTypeUnMark = 0;
	public static $markTypeMarkGood = 1;
	public static $markTypeMarkBad = 2;
	public static $markPass = 0;
	public static $markDeny = 1;
	
	/**
	 * �������
	 */
	public static $upcPunishTypeBan = 0;
	public static $upcPunishTypeMask = 1;
	public static $upcPunishTypeDelete = 2;
	public static $upcPunishTypeValve = 3;
	public static $punishTypeBanID = 1;
	public static $punishTypeMask = 2;
	public static $punishTypeUnBanID = 3;
	public static $punishTypeUnMask = 4;
	public static $punishTypeSetUserState = 5;
	public static $punishTypeUnSetUserState = 6;
	public static $punishTypeBanAndMask = 7;
	public static $punishTypeUnBanAndUnMask = 8;
	public static $unBanOpraterId = 831066185;
	public static $unBanOpraterName = '�����������';
	public static $appealAuthMobile = 1;
	public static $appealAuthMarkImg = 2;
	public static $appealAuthManual = 4;
	public static $appealAuthBawu = 8;
	public static $punishRangeAll = 'ȫ��';
	public static $punishRangeEmpty = '��';
	public static $appealExpiredMark = 4;
	public static $appealExpiredManual = 12;
	
	/**
	 * ������������
	 */
	public static $filterUser = 202;
	public static $cancelFilterUser = 203;
	public static $filterForumUser = 213;
	public static $cancelFilterForumUser = 214;
	
	//Ͷ�߰��������ߴ�������
	public static $appealForumMangerOfManualTimesLimit = 3;
	
	//================����վ-ϵͳɾ�����ӻָ�-BEGIN==========================
	/**
	 * ����վ-���ӹ���-ϵͳɾ��
	 * �����˹��ָ��еļ���״̬
	 */
	public static $applyRestorePostWait  = 0;
	public static $applyRestorePostAgain = 1;
	public static $applyRestorePostDelete= 2;
	public static $applyRestorePostConcat = 3;
	public static $applyRestorePostAutoAgain = 64;
	
	/**
	 * ���� �ֻ����ͼ�ָ��ļ���״̬
	 */
	public static $restorePostStatusSuccess = 2;
	public static $restorePostStatusFailed  = 3;

	/**
	 * ��ͼ�ָ����� �ȴ��ָ���
	 */
	public static $markImgRestorePostWait = 4;
	
	/**
	 * ��ѯ״̬ʧ�ܣ�û�й������ӻָ��к��˹��ָ�ʧ�ܵļ�¼)
	 */
	public static $applyRestorePostNone	= -1;
	
	/**
	 * ����ָ����ӵ����ȿ�������
	 */
	public static $actsCtrCmdRestorePostByPhone = 'restorePostByPhone';
	public static $actsCtrCmdRestorePostByMarkImg = 'restorePostByMarkImg';
	public static $actsCtrCmdRestorePostByManual = 'restorePostByManual';
	public static $actsCtrMaxRestorePostByPhone = 5;
	public static $actsCtrMaxRestorePostByMarkImg = 5;
	public static $actsCtrMaxRestorePostByManual = 1;
	
	public static $postLinkPrefix = "http://tieba.baidu.com/p/";
	public static $writeMsgLinkPrefix = 'http://msg.baidu.com/msg/writing?un=';
	
	//�µ�ɾ����ѯ�ӿڵ�source��Ӧ��ϵ
	public static $delPostSourceMap = array(
		'self'	=> 100,
		'lz'	=> 200,
		'bawu'	=> 300,
		'system'=> 400,
		'admin' => 500
	);
	public static $sourceTypeSelf = 'self';
	public static $sourceTypeLz	  = 'lz';
	public static $sourceTypeBawu = 'bawu';
	public static $sourceTypeSystem = 'system';
	public static $sourceTypeAdmin = 'admin';


	/**
	 * ϵͳɾ�� ����Ŀǰ���Ӳ�ͨ״̬�ĳ���
	 */
	public static $restorePostStatusWaitApply = 0;
	public static $restorePostStatusWaitMarkImg = 1;
	public static $restorePostStatusManualFail = 2;
	public static $restorePostStatusWaitManual = 3;
	public static $restorePostStatusDenyRestoreDueToDuplex = 4;
	
	//�ύ�˹�����ָ����ӵ�URL
	public static $restorePostManualAppealUrl = 'http://tieba.baidu.com/tousu/new/commit';
	
	//�û������߷�ʽ�ı�־λ
	public static $restorePostWayByManual = 4;
	public static $restorePostWayByMarkimg= 2;
	public static $restorePostWayByPhone  = 1;
	
	//��Ȩ��������
	public static $vcodePreventWordTableName = 'vcode_prevent';
	public static $vcodePreventRemark	= '�����û�ȫվ������Ȩ����';
	
	//�����˻��޸����� ����
	public static $resetSignInvalid = 10;
	public static $cbSignInvalid	= 11;
	public static $cbParamsInvalid  = 12;
	public static $cbAppealNotFound	= 13;
	
	public static $resetPwdAppealSuccess = 1;
	public static $resetPwdAppealFailed  = 2;
	
	public static $resetPwdTaskUnRead	= 0;
	public static $resetPwdTaskRead	= 1;

	public static $resetSignInvalidMsg = '��ȫ���ʧ�ܣ������������ʺŻָ�';
	public static $resetPwdAppealSucessMsg = '�����޸ĳɹ���ϵͳ���ڻָ������˺ţ���5���Ӻ�鿴';
	public static $resetPwdAppealFailedMsg = '��Ǹ��ϵͳ���ϣ��ʺŻָ�ʧ��~�볢���ٴ�����';
	
	public static $resetPwdAppealSucessRemind = '�����û��޸�����ɹ����ʺŻָ��ɹ�';
	public static $resetPwdAppealFailedRemind = '����ϵͳԭ������ʺŻָ�ʧ�ܣ�������';
	
	public static function getResetPwdRemindMap ( ) {
		return array(
			self::$resetSignInvalid			=> self::$resetSignInvalidMsg,
			self::$resetPwdAppealSuccess	=> self::$resetPwdAppealSucessMsg,
			self::$resetPwdAppealFailed		=> self::$resetPwdAppealFailedMsg
		);
	}
	
	public static function getResetPwdDetailMap ( ) {
		return array(
			self::$appealStatusSucc	=> self::$resetPwdAppealSucessRemind,
			self::$appealStatusFail	=> self::$resetPwdAppealFailedRemind
		);
	}

	public static $accountResetPwdMonitor = array(//��������������Ĳ�����
		90021,
	);
	//ֻ֧���˹����߷�ʽ�İ�ID
	public static $restorePostAllowManualOnlyForumIds = array(
			14,
			69,
			840,
			970,
			2230,
			2284,
			6638,
			7023,
			7715,
			8594,
			8656,
			11772,
			11869,
			12133,
			14115,
			14911,
			15378,
			16202,
			22022,
			25274,
			25694,
			26001,
			38721,
			40790,
			70579,
			82726,
			100729,
			104521,
			123509,
			134400,
			136162,
			141901,
			146914,
			152464,
			167139,
			171023,
			174195,
			179781,
			181646,
			187339,
			200503,
			200804,
			210290,
			274452,
			277356,
			278321,
			287329,
			299438,
			308237,
			317908,
			322732,
			331919,
			348431,
			363059,
			404698,
			429477,
			464768,
			485820,
			497168,
			501133,
			503206,
			510421,
			512139,
			515943,
			541438,
			558940,
			560742,
			569332,
			574694,
			631163,
			640541,
			648262,
			649787,
			666465,
			689647,
			706939,
			758969,
			865438,
			918862,
			1007580,
			1038204,
			1062041,
			1067513,
			1089593,
			1100992,
			1111175,
			1111734,
			1154529,
			1211534,
			1235760,
			1238114,
			1243404,
			1291980,
			1370025,
			1381821,
			1415005,
			1474277,
			1504961,
			1525417,
			1546493,
			1560023,
			1587816,
			1603607,
			1640327,
			1662687,
			1691446,
			1701120,
			1701831,
			1702210,
			1702980,
			1703809,
			1705262,
			1712224,
			1739976,
			1766852,
			1818894,
			1820423,
			1829631,
			1890591,
			1892460,
			1943986,
			1956505,
			1957731,
			1991115,
			2053913,
			2075003,
			2093450,
			2133550,
			2133728,
			2301360,
			2349378,
			2358322,
			2362905,
			2432662,
			2432903,
			2508948,
			2573878,
			2582366,
			2783017,
			2794097,
			2827019,
			2848409,
			2866941,
			2883101,
			2920781,
			2952883,
			2986579,
			3041509,
			3059492,
			3114634,
			3131953,
			3236787,
			3312930,
			3452483,
			3681803,
			3820633,
			3859381,
			4098022,
			4099163,
			4271904,
			4324763,
			4342849,
			4345164,
			4528066,
			4984224,
			5353548,
			5780441,
			6886857,
			7018563,
			7566759,
			7699123,
			8514626,
			9202409,
			892, 
			1766852, 
			200804, 
			666465, 
			1238114, 
			7699123, 
			4984224, 
			640541, 
			2053913, 
			1154529, 
			503206, 
			569332, 
			317908, 
			363059, 
			52031, 
			558940, 
			134400, 
			51309, 
			540276, 
			3859381, 
			2920781, 
			2432662, 
			1662687, 
			2349378,
			59099,
			73787,
			280050,
			81570,
			728957,
			1587816,
			1007580,
			3131953,
			11772,
			860907,
			653705,
			6868042,
			5353548,
			46,
			1243404,
			1370025,
			1291980,
			572738,
			2350936,
			248112,
			110019,
			2144953,
			7018563,
			309955,
			85022,
			1062041,
			541383,
			2883101,
			3312930,
			1956505,
			274452,
			24,
			512139,
			1211534,
			2358322,
			4324763,
			2284,
			43927,
			3059492,
			50,
			6886857,
			3452483,
			631163,
			2274303,
			1391285,
			965051,
			2582366,
			1603607,
			6331,
			515943,
			711567,
			1218295,
			123509,
			1739976,
			3820633,
			1089593,
			1525417,
			949868,
			2093450,
			6807900,
			497168,
			1185508,
			26001, 
			4099163, // ����ϵ��й�
			1474277, // new add ���Ͱ�
			706939,
			1627732,
			5937,
			15752, // ���籭��
	
	);
	//================����վ-ϵͳɾ�����ӻָ�-END==========================
	//�ص��
	public static $arrFocusForumId = array(
		59099,	//����
		73787,	//ħ������
		280050, //lol
		81570,	//���³�����ʿ
		728957,	//��Խ����
		1587816,//�������ɴ�
		1007580,//����Ұ����
		3131953,//��������
		11772,	//������
		860907,	//·����һֻ
		653705	//2012
	);
	
	/**
	 * ���м���İ�
	 */
	public static $arrAppealMsgCrumb = array(
			0 => array('appealCol'=>'�ʻ�����','appealType'=>'ϵͳ�����¼','appealPageType'=>1),
			2 => array('appealCol'=>'�ʻ�����','appealType'=>'�������¼','appealPageType'=>2)
		);

		/**
	 * ���β��������
	 */
	public static $maskUser = 55001;
	public static $unMaskUser = 55002;
	
	public static $maskAndBlockCmdNo = 55001;   //�µķ�����ε������(�������ε������)
	public static $unMaskAndBlockCmdNo  = 55002;//�µķ�����ε������(�������ε������)
	
	public static $setUserState = 55003;
	public static $unsetUserState = 55004;
	public static $banAndMaskUser = 55202;
	public static $unBanAndUnMaskUser = 55203;
	public static $sysMessageBan = '�´�����������Ϊ״̬�쳣���뵽�����ʺŹ������ģ���ַ��http://tieba.baidu.com/pmc/system?fr=msgc���鿴���飬�����ύ��������Ϊ�쳣״̬��';
	public static $sysMessageMask = '�´�����������Ϊ״̬�쳣���뵽�����ʺŹ������ģ���ַ��http://tieba.baidu.com/pmc/system?fr=msgc���鿴���飬�����ύ��������Ϊ�쳣״̬��';
	public static $sysMessageValve = '�´�����������Ϊ״̬�쳣���뵽�����ʺŹ������ģ���ַ��http://tieba.baidu.com/pmc/system?fr=msgc���鿴���飬�����ύ��������Ϊ�쳣״̬��';
	public static $sysMessageBanAndMask = '�´�����������Ϊ״̬�쳣���뵽�����ʺŹ������ģ���ַ��http://tieba.baidu.com/pmc/system?fr=msgc���鿴���飬�����ύ��������Ϊ�쳣״̬��';
	public static $sysManualAppealResultPass = '�Ѵ��������쳣״̬������룬�뵽�ʺŹ������ģ���ַ��http://tieba.baidu.com/pmc/system?fr=msgc���鿴���������������ʣ�����������������ɽ��з�����лл������IDΪϵͳID������ظ���';
	public static $sysManualAppealResultRefused = '���Ľ�����뱻�ܾ����뵽�����ʺŹ������ģ���ַ��http://tieba.baidu.com/pmc/system?fr=msgc���˽�ܾ�ԭ��';
	//public static $forumMessageBan = '�´�����������Ϊ״̬�쳣���뵽�����ʺŹ������ģ���ַ��http://tieba.baidu.com/pmc/bawu?fr=msgc���鿴���飬�����ύ��������Ϊ�쳣״̬��';
	/*
	 * ע�� $forumMessageBan ���и� %s ������ע�����������
	 */
	public static $forumMessageBan = "�´������������������������Ϊ��%s�������Ե�http://tieba.baidu.com/pmc/bawu����ָ���";
	public static $forumManualAppealResultPass = '�Ѵ��������쳣״̬������룬�뵽�ʺŹ������ģ���ַ��http://tieba.baidu.com/pmc/bawu?fr=msgc���鿴���������������ʣ�����������������ɽ��з�����лл������IDΪϵͳID������ظ���';
	public static $forumManualAppealResultRefused = '���Ľ�����뱻�ܾ����뵽�����ʺŹ������ģ���ַ��http://tieba.baidu.com/pmc/bawu?fr=msgc���˽�ܾ�ԭ��';
	
	/**
	 * ��ʾ�İ�
	 */
	public static $alertMessageBan = "������Ϊ���쳣���ʺű�����������޷�����������ͨ���������쳣״̬Ŷ~";
	public static $alertMessageMask = "������Ϊ���쳣���ʺű����Σ��������ӱ����أ�����ͨ���������쳣״̬Ŷ~";
	public static $alertMessageValue = "������Ϊ���쳣���ʺ���ȫ�ɷ���ʱ��������֤�룬������������״̬Ŷ~";
	public static $alertMessageBanAndMask = "������Ϊ���쳣���ʺű�������Σ������޷������ҷ�����Ϣ�����أ�������������״̬Ŷ~";
	public static $alertMessageMerge = "������Ϊ�ж����쳣��¼������ͨ���������쳣״̬Ŷ~";
	public static $alertMessageNomal = "������Ϊû���쳣Ŷ~";
	
	public static $qinglangxingdong_opId = '*********';
	
	public static $machineAccounts = array (
			*********, 
			*********, 
			*********, 
			********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			*********, 
			776588370, 
			776588371, 
			776588372, 
			776588373, 
			776588374, 
			776588377, 
			776588378, 
			776588379, 
			776588380, 
			776588381, 
			776588382, 
			776588383, 
			776588384, 
			776588385, 
			776588386, 
			776588387, 
			776588389, 
			776588390, 
			776588391, 
			776588392, 
			776588393, 
			776588394, 
			776588395, 
			776588396, 
			776588397, 
			776588398, 
			776588399, 
			776588400, 
			776588401, 
			92282071, 
			303874995, 
			303874997, 
			776588402, 
			776588404, 
			776588405, 
			776588407, 
			776588408, 
			776588409, 
			776588411, 
			303875007, 
			303875009, 
			303875018, 
			776588412, 
			776588414, 
			776588415, 
			776588416, 
			776588417, 
			776588418, 
			776588419, 
			776588421, 
			776588423, 
			776588426, 
			776588427, 
			776588428, 
			776588429, 
			776588430, 
			776588431, 
			776588433, 
			776588434, 
			776588435, 
			776588437, 
			776588438, 
			776588440, 
			776588441, 
			776588442, 
			776588443, 
			776588444, 
			776588445, 
			776588446, 
			776588447, 
			776588448, 
			776588449, 
			776588450, 
			776588451, 
			776588452, 
			776588453, 
			776588455, 
			776588457, 
			776588458, 
			776588459, 
			776588460, 
			776588461, 
			776588462, 
			776588464, 
			776588465, 
			776588466, 
			776588467, 
			776588468, 
			776588470, 
			776588471, 
			776588472, 
			776588473, 
			776588474, 
			776588475, 
			776588476, 
			776588478, 
			776588480, 
			776588481, 
			776588482, 
			776588483, 
			776588484, 
			776588485, 
			776588486, 
			805143562, 
			805143563, 
			805143564, 
			805143565, 
			805143566, 
			805143558, 
			805143559, 
			805143560, 
			810138072, 
			810138074, 
			810138075, 
			831066184, 
			831066185, 
			831067529, 
			831066187, 
			831066188, 
			831066189, 
			831882860, 
			831882861, 
			856265220, 
			856268304, 
			856269775, 
			856270748, 
			856270749, 
			856265221, 
			92282075, 
			303875000, 
			97901606, 
			303875001, 
			109053831, 
			97901784, 
			97901778, 
			354149333, 
			354149335, 
			303874980, 
			354149336, 
			556091618, 
			303874986, 
			327528985, 
			915069382, 
			915079260, 
			897793021,
			966856805, // ���ɿͻ��˽��
			977475434, // �������Զ����
			1247827377,
			1337450822,
			179714704,
			1045259536,
			1254185845,
			1310824879,
			109053831,
	 );
	
	//ֻ�ܽ����˹����ߵ�monitor_type
	/*
	 * �ֻ���֤�������ָ������ӣ��õ�
	 */
	public static $manualMonitorType = array 
	(
	  0 => 7003,
	  1 => 7005,
	  2 => 7038,
	  3 => 7202,
	  4 => 16006,
	  5 => 7304,
	  6 => 7305,
	  7 => 7205,
	  8 => 20039,
	  9 => 7110,
	  10 => 34001,
	  11 => 96002,
	  12 => 96010,
	  13 => 96013,
	  14 => 96014,
	  15 => 96020,
	  16 => 96022,
	  17 => 96035,
	  18 => 96043,
	  19 => 96062,
	  20 => 96063,
	  21 => 96067,
	  22 => 96068,
	  23 => 96072,
	  24 => 96076,
	  25 => 96084,
	  26 => 96085,
	  27 => 96086,
	  28 => 75001,
	  29 => 75002,
	  30 => 75003,
	  31 => 96090,
	  32 => 47002,
	  33 => 47005,
	  34 => 96094,
	  35 => 96095,
	  36 => 47007,
	  37 => 47008,
	  38 => 87001,
	  39 => 47009,
	  40 => 7206,
	  41 => 87003,
	  42 => 7404,
	  43 => 40001,
	  44 => 7207,
	  45 => 40004,
	  46 => 47010,
	  47 => 40005,
	  48 => 87004,
	  49 => 47012,
	  50 => 7208,
	  51 => 7209,
	  52 => 47013,
	  53 => 47014,
	  54 => 7210,
	  55 => 7212,
	  56 => 47016,
	  57 => 96100,
	  58 => 96101,
	  59 => 47018,
	  60 => 40012,
	  61 => 72001,
	  62 => 72002,
	  63 => 87005,
	  64 => 92001,
	  65 => 96088,
	  66 => 16001,
	  67 => 16004,
	  68 => 16006,
	  69 => 20016,
	  70 => 20019,
	  71 => 4023,
	  72 => 47001,
	  73 => 47002,
	  74 => 47003,
	  75 => 47004,
	  76 => 47005,
	  77 => 47006,
	  78 => 47007,
	  79 => 47008,
	  80 => 47009,
	  81 => 47010,
	  82 => 47011,
	  83 => 47012,
	  84 => 47013,
	  85 => 47014,
	  86 => 47015,
	  87 => 47016,
	  88 => 7008,
	  89 => 7019,
	  90 => 7014,
	  91 => 7202,
	  92 => 7003,
	  93 => 7038,
	  94 => 96022,
	  95 => 96040,
	  96 => 96060,
	  97 => 7003,
	  98 => 7038,
	  99 => 47002,
	  100 => 47007,
	  101 => 47008,
	  102 => 47009,
	  103 => 47010,
	  104 => 47012,
	  105 => 47013,
	  106 => 47014,
	  107 => 47016,
	  108 => 75001,
	  109 => 75002,
	  110 => 75003,
	  111 => 96100,
	  112 => 16006,
	  113 => 20039,
	  114 => 96002,
	  115 => 96063,
	  116 => 96084,
	  117 => 96086,
	  118 => 7202,
	  119 => 7209,
	  120 => 7304,
	  121 => 7404,
	  122 => 34001,
	  123 => 40001,
	  124 => 96043,
	  125 => 96085,
	  126 => 7005,
	  127 => 7110,
	  128 => 7206,
	  129 => 7208,
	  130 => 7210,
	  131 => 40004,
	  132 => 40005,
	  133 => 47005,
	  134 => 96022,
	  135 => 96035,
	  136 => 96076,
	  137 => 96095,
	  138 => 96085,
	  139 => 28025,
	);
	
	public static $accountManualMonitor = array(//ֻ�ܽ��� ���˺� ���˹����ߵĲ��������ض��Ĳ������������û����˺ţ����ܽ��������ָ�
		19998,
		90015,
		28012,
		90021,
		90006,
		29002,
		29003,
		29004,
		29005,
		29006,
		29007,
		29008,
		46001,
		90008,
		90031,
		91064,		
	);
	
	public static $deletMonitorClusters = array(//ɾ�����Լ�Ⱥ
		'tieba68',
		'tieba69',
		'baobaMonitor',
		'noWhiteUser',
	);
	
	public static $managerType = array(
		'manager',
		'assist',
	);
	
	//Ͷ������
	const COMPLAINT_TYPE_ELSE = 0;//����
	CONST COMPLAINT_TYPE_LONG_NO_LOGIN = 1;//����δ��½
	CONST COMPLAINT_TYPE_ILLEGAL_OPERATE = 2;//Υ�����
	CONST COMPLAINT_TYPE_SEX = 3;//ɫ�鷴��
	public static $complaintType = array(
		'else' => self::COMPLAINT_TYPE_ELSE,
		'nologin' => self::COMPLAINT_TYPE_LONG_NO_LOGIN,
		'illegal' => self::COMPLAINT_TYPE_ILLEGAL_OPERATE,
		'sex' => self::COMPLAINT_TYPE_SEX,
		
	);
	public static $complaintTypeENtoCHS = array(
		self::COMPLAINT_TYPE_ELSE => '����',
		self::COMPLAINT_TYPE_LONG_NO_LOGIN => '����δ��¼',
		self::COMPLAINT_TYPE_ILLEGAL_OPERATE => '����Υ��',
		self::COMPLAINT_TYPE_SEX => 'ɫ�顢����',
	);
	public static $officialManagerConfilterTable = 'tousu_special_uo_word'; 
	public static $forumManagerType = array(
		'is_forum_assist',
		'is_forum_manager',
	);
	CONST COMPLAINT_STATUS_UN_AUDIT = 0;//����� 
	CONST COMPLAINT_STATUS_DONE = 255;//�����
	CONST IS_OFFICIAL_UNCONFIRM = 0;
	CONST IS_NOT_OFFICIAL_MANAGER = 1;
	const IS_OFFICIAL_MANAGER = 2;
	
	/************************��֤������******************************/
	
	/**
	 * ��֤������״̬
	 */
	public static $captchaTaskStatus = array(
			'processing' => 0,
			'successed'  => 1,
			'failed'     => 2,
			'expired'    => 3, // ��ʱû���õ�
	);
	
	/**
	 * ��֤����������
	 */
	const CAPTCHA_TASK_TOTAL = 7;
	
	/**
	 * ��֤������ɹ���Ҫ�ɹ�������֤��Ĵ���
	 */
	const CAPTCHA_TASK_SUCC_COUNT = 5;
	
	/**
	 * ��֤������׼��ʧ�ܵĴ���
	 */
	const CAPTCHA_TASK_ALLOW_ERR_COUNT = 2;
	
	const CAPTCHA_TASK_EXPIRED_SECONDS = 86400;
	
	const CALL_FROM = 'forum_pmc';
	
	const ONKEY_RECOVERY_CMD   = 55500;
	public static $recoverUser = array(
			'op_uid'   => 966856805,
			'op_uname' => '���ɿͻ��˽��',
	);
	
	/*
	 * �⺯����Ĭ�Ϸ���'' ���������
	 * 
	 */
	public static function opUserType2Opgroup ( $opUserType ) {
		$arrMap = array(
			self::$opUserTypeMachine	=> self::$opGroupSystem,
			self::$opUserTypePm			=>	self::$opGroupAdmin,
			self::$opUserTypeForumManager=>	self::$opGroupBawu
		);
		if ( isset($arrMap[$opUserType]) ) {
			return $arrMap[$opUserType];
		}
		return '';
	}
	
	public static function opgroup2OpUserType ( $opGroup ) {
		$arrMap = array(
			self::$opGroupSystem => self::$opUserTypeMachine,
			self::$opGroupAdmin	 => self::$opUserTypePm,
			self::$opGroupBawu   => self::$opUserTypeForumManager	
		);
		if ( isset($arrMap[$opGroup]) ) {
			return $arrMap[$opGroup];
		}
		return self::$opGroupSystem;
	}
}

?>
