<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-05-25 14:08:58
 * @version
 */

class Util_Post {
	const MAX_IMG_HEIGHT = 130;
	const MAX_VEDIO_HEIGHT = 400;
	public static $_arrThreadUser = array();
        public static function formatPostContent($strContent)
        {
                $intImg = preg_match_all('/<img\s.*?>/', $strContent, $arrImgMatch);
		$intVedio = preg_match_all('/<embed\s.*?>/', $strContent, $arrVedioMatch);
		foreach ($arrImgMatch[0] as $strImg) {
			preg_match('/<img.+width=\"?(\d*)\"?.+>/i', $strImg, $match);
                        $intWidth = intval($match[1]);
                        preg_match('/<img.+height=\"?(\d*)\"?.+>/i', $strImg, $match);
                        $intHeight = intval($match[1]);
                        if ($intHeight > self::MAX_IMG_HEIGHT) {
                            $intWidth = self::MAX_IMG_HEIGHT*$intWidth/$intHeight;
                                        $strNewImg = preg_replace('/(<img.+height=\"?)(\d+)(\"?.+>)/i', "\${1}".self::MAX_IMG_HEIGHT."\${3}" , $strImg);
                                        $strNewImg = preg_replace('/(<img.+width=\"?)(\d+)(\"?.+>)/i', "\${1}".$intWidth."\${3}" , $strNewImg);
                            $strContent = str_replace($strImg, $strNewImg, $strContent);
                        }
		}
		foreach ($arrVedioMatch[0] as $strVedio) {
                        preg_match('/<embed.+width=\"?(\d*)\"?.+>/i', $strVedio, $match);
                        $intWidth = intval($match[1]);
                        preg_match('/<embed.+height=\"?(\d*)\"?.+>/i', $strVedio, $match);
                        $intHeight = intval($match[1]);
                        if ($intWidth > self::MAX_VEDIO_HEIGHT) {
                            $intHeight = intval(self::MAX_VEDIO_HEIGHT)*$intHeight/$intWidth;
                            $strNewVedio = preg_replace('/(<embed.+width=\"?)(\d+)(\"?.+>)/i', "\${1}".self::MAX_VEDIO_HEIGHT."\${3}" , $strVedio);
                            $strNewVedio = preg_replace('/(<embed.+height=\"?)(\d+)(\"?.+>)/i', "\${1}".$intHeight."\${3}" , $strNewVedio);
                            Bingo_Log::debug($strNewVedio);
                            $strContent = str_replace($strVedio, $strNewVedio, $strContent);
                        }
		}
                $strContent=  preg_replace('/(<br\/?>\s*)+/i','<br/>',$strContent);
                return $strContent;
        }

        public static function getPostAbstract($strContent)
        {
		$arrImg = array();
		$arrAbstract = array();
                $intImg = preg_match_all('/<img\s.*?>/', $strContent, $arrImgMatch);
		$intEmbed = preg_match_all('/<embed\s.*?>/', $strContent, $arrEmbedMatch);
		$strContent = preg_replace('/<embed\s.*?>/', '', $strContent);
		$strContent = preg_replace('/<img\s.*?>/', '', $strContent);
		$strContent=  preg_replace('/(<br\/?>\s*)+/i', ' ',$strContent);
		$strContent=  preg_replace('/<[^>]*>/', '',$strContent);
		foreach ($arrImgMatch[0] as $strImg) {
			preg_match('/<img.+src=\"?([^ "]*)\"?.+>/i', $strImg, $match);
                        $strSrc = $match[1];
			preg_match('/<img.+class=\"?([^ "]*)\"?.+>/i', $strImg, $match);
                        $strClass = $match[1];
			$arrItems[] = array(
				'src' => $strSrc,
				'class' => $strClass,
			);
		}
		foreach ($arrEmbedMatch[0] as $strEmbed) {
			preg_match('/<embed.+src=\"?([^ "]*)\"?.+>/i', $strEmbed, $match);
                        $strSrc = $match[1];
			preg_match('/<embed.+class=\"?([^ "]*)\"?.+>/i', $strEmbed, $match);
                        $strClass = $match[1];
			$arrItems[] = array(
				'src' => $strSrc,
				'class' => $strClass,
			);
		}
		$arrAbstract = array(
			'text' => mb_substr($strContent, 0, 400),
			'ext' => $arrItems,
		);
                return $arrAbstract;
        }

	public static function getPostContent($arrPids) {
        	$arrContent = array(); 
		$arrInfo = array();
        	if(!empty($arrPids) && count($arrPids) > 0) { 
                	$arrInput = array( 
                        	'post_ids' => $arrPids, 
                        ); 
			Bingo_Timer::start('service_post_getPostInfo');
                	$arrOut = Tieba_Service::call('post', 'getPostInfo', $arrInput); 
			Bingo_Timer::end('service_post_getPostInfo');
                	if($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) { 
                        	Bingo_Log::warning('call service post::getPostInfo failed, errno[' . $arrOut['errno'] . '],errmsg[' .$arrOut['errmsg'] . ']'); 
                	}
			$arrInfo = $arrOut['output']; 
        	} 
        	if(count($arrInfo) > 0) {
                	foreach($arrInfo as $arrEach) {
				$arrEach['content'] = self::getPostAbstract($arrEach['content']);
                        	$arrContent[$arrEach['post_id']] = $arrEach;
                	}   
        	} 
		return $arrContent;  
	}

	/**
	 * 
	 * @param unknown_type $arrTids
	 * @param unknown_type $fid
	 * @return multitype:
	 */
	public static function getThreadAbstract($arrTids, $fid = 0) {
        	$arrAbstract = array();
        	if(!empty($arrTids) && count($arrTids) > 0) {
                	$arrInput = array(
                        	'thread_ids' => $arrTids,
                        	'need_abstract' => 1,
                        	'forum_id' => $fid,
                        	'need_photo_pic' => 0,
                        );
                	if ( $fid > 0 ){
                		$arrInput['need_user_data'] = 1;
                	}
					Bingo_Timer::start('service_post_mgetThread');
                	$arrOut = Tieba_Service::call('post', 'mgetThread', $arrInput);
					Bingo_Timer::end('service_post_mgetThread');
                	if($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                        	Bingo_Log::warning('call service mgetThread failed, errno[' . $arrOut['errno'] . '],errmsg[' .$arrOut['errmsg'
] . ']');
                	}
                	$arrAbstract = $arrOut['output']['thread_list'];
                	self::$_arrThreadUser = $arrOut['output']['thread_user_list'];
        	}
		return $arrAbstract;
	}

	public static function getForumFrs($arrInput) {
		if(empty($arrInput)) {
			Bingo_Log::warning('getForumFrs parameters is empty');
			return false;
		}
		Bingo_Timer::start('service_post_getFrs');
                $arrOut = Tieba_Service::call('post', 'getFrs', $arrInput);
		Bingo_Timer::end('service_post_getFrs');
                if($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                	Bingo_Log::warning('call service getFrs failed, errno[' . $arrOut['errno'] . '],errmsg[' .$arrOut['errmsg'
] . ']');
              	}
		return $arrOut;
	}
	
	public static function getUserForumCount($arrInput){
		if(empty($arrInput)) {
			Bingo_Log::warning('getUserForumCount parameters is empty');
			return false;
		}
		Bingo_Timer::start('service_post_queryUserForumCount');
        $arrOut = Tieba_Service::call('post', 'queryUserForumCount', $arrInput);
		Bingo_Timer::end('service_post_queryUserForumCount');
        if($arrOut ===false || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call service_post_queryUserForumCount failed, input: '.serialize($arrInput).'errno[' . $arrOut['errno'] . '],errmsg[' .$arrOut['errmsg'] . ']');
        }
		return $arrOut;
	}
	
	/**
	 * 获取帖子内容
	 */
	public static function getPostInfo($pids){
		//获取帖子内容
		$input['post_ids'] = $pids;
		Bingo_Timer::start(__FUNCTION__);
		$ret = Tieba_Service::call('post', 'getPostInfo', $input);
		Bingo_Timer::end(__FUNCTION__);
		if ($ret === false || (isset($ret['errno']) && intval($ret['errno']) !== 0)) {
			Bingo_Log::warning(__FUNCTION__." call post getPostInfo fail input =".json_encode($input)." output =".json_encode($ret));
			return false;
		}
		return $ret['output'];
	}
	/**
	 * [getThreadInfo description]
	 * @param  [type] $tids [description]
	 * @return [type]       [description]
	 */
	public static function getThreadInfo($tids){
		$arrInput = array(
            'thread_ids' => $tids,
            'forum_id' => 0,
            'need_abstract' => 0,
            'need_photo_pic' => 0,
            'need_user_data' => 0,
            'need_forum_name' => 0,
            'icon_size' => 0,
    		'call_from' => 'service_bawu',
        );
        $arrOutput = Tieba_Service::call('post', 'mgetThread', $arrInput);
        if (!isset($arrOutput['errno']) || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("post mgetThread error. input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "]");
            return false;
        }
        return $arrOutput['output']['thread_list'];
	}
}
?>
