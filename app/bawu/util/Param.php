<?php
/**
 * Created by PhpStorm.
 * User: huangling02
 * Date: 17/6/29
 * Time: 下午6:42
 */

class Util_Param {
    
    /**
     * [getInt description]
     * @param  [type] &$conds   [description]
     * @param  [type] $condKey  [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @return [type]           [description]
     */
    public static function getInteger(&$conds, $arrInput, $key) {
        if(isset($arrInput[$key])) {
            if(!is_array($arrInput[$key])){
                $conds [] = $key . ' =' . (int)$arrInput[$key];
            }else{
                $conds [] = sprintf("%s IN(%s)", $key, implode(',',$arrInput[$key]));
            }
        }
    }
    /**
     * [getString description]
     * @param  [type] &$conds   [description]
     * @param  [type] $condKey  [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @return [type]           [description]
     */
    public static function getStr(&$conds, $arrInput, $key) {
        if(isset($arrInput[$key])) {
            $conds [] = $key . '="' . trim($arrInput[$key]).'"';
        }
    }
    /**
     * [getString description]
     * @param  [type] &$conds   [description]
     * @param  [type] $condKey  [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @return [type]           [description]
     */
    public static function getString(&$conds, $arrInput, $key) {
        if(isset($arrInput[$key])) {
            $conds [$key . ' ='] = '"'.trim($arrInput[$key]).'"';
        }
    }
    /**
     * [getStringLike description]
     * @param  [type] &$conds   [description]
     * @param  [type] $condKey  [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @return [type]           [description]
     */
    public static function getStringLike(&$conds, $arrInput, $key) {
        if(isset($arrInput[$key])) {
            $conds [$key . ' LIKE '] = '%' . trim($arrInput[$key]) . '%';
        }
    }
    /**
     * [getArray description]
     * @param  [type] &$conds   [description]
     * @param  [type] $condKey  [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @param  [type] $standard [description]
     * @return [type]           [description]
     */
    public static function getArray(&$conds, $arrInput, $key, $standard) {
        if(isset($arrInput[$key])) {
            if(!is_array($arrInput[$key])) {
                $arrInput[$key] = array($arrInput[$key]);
            }
            $arrItem = array();
            foreach($arrInput[$key] as $input) {
                if(isset($standard[$input])) {
                    $arrItem []= $input;
                }
            }
            if(!empty($arrItem)) {
                $conds[] = sprintf('%s IN (%s)', $key, implode(',',$arrItem));
            }
        }
    }
    /**
     * [getIds description]
     * @param  [type] &$conds   [description]
     * @param  [type] $condKey  [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @return [type]           [description]
     */
    public static function getIds(&$conds, $arrInput, $key) {
        if(isset($arrInput[$key])) {
            if(!is_array($arrInput[$key])) {
                $conds[$condKey . ' ='] = $arrInput[$key];
                return $conds;
            } else {
                $conds[] = sprintf('%s IN (%s)', $key, implode(',',$arrInput[$key]));
                return $conds;
            }
        }
        return false;
    }
    /**
     * [getRange description]
     * @param  [type] &$conds   [description]
     * @param  [type] $condKey  [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @return [type]           [description]
     */
    public static function getRange(&$conds,$arrInput, $key) {
        if(isset($arrInput[$key])){
            if(!is_array($arrInput[$key])){
                $conds[] = $key . '=' . $arrInput[$key];
            }else{
                if(isset($arrInput[$key]['max'])){
                    $conds[] = $key . '<' . $arrInput[$key]['max'];
                }
                if(isset($arrInput[$key]['min'])){
                    $conds[] = $key . '>' . $arrInput[$key]['min'];
                }
                if(isset($arrInput[$key]['maxe'])){
                    $conds[] = $key . '<=' . $arrInput[$key]['maxe'];
                }
                if(isset($arrInput[$key]['mine'])){
                    $conds[] = $key . '>=' . $arrInput[$key]['mine'];
                }
            }
        }
    }
}
