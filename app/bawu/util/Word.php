<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2013-01-18 20:33:58
 */
class Util_Word {
	
	public static function isBanForumDir($strForumDir)
	{
		$handleWordServer = Wordserver_Wordlist::factory();
		$arrKeys = array($strForumDir);
		$strTableName = 'managerapply_ban_dir';
		$arrItemInfo = $handleWordServer->getValueByKeys($arrKeys,$strTableName);
	
		return isset($arrItemInfo[$strForumDir]) ? true : false;
	}
	
	public static function getAuditForumTypeByName($strFname)
	{
		$handleWordServer = Wordserver_Wordlist::factory();
		$arrKeys = array($strFname);
		$strTableName = 'managerapply';
		$arrItemInfo = $handleWordServer->getValueByKeys($arrKeys,$strTableName);
	
		if(isset($arrItemInfo[$strFname]))
		{
			return intval($arrItemInfo[$strFname]);
		}
		else
		{
			return 0;
		}
	}
	public static function getWordList($arrInput) {
	
		$handleWordServer = Wordserver_Wordlist::factory();
		$arrKeys = $arrInput['arr_keys'];//array
		$strTableName = $arrInput['str_table_name'];
		$arrItemInfo = $handleWordServer->getValueByKeys($arrKeys,$strTableName);
		if(false ===  $arrItemInfo)
		{
			Bingo_Log::warning("Can't getValueByKeys .Input[".serialize($arrInput)."] out[".serialize($arrItemInfo)."]");
		}
		return $arrItemInfo;
	}
}
?>
