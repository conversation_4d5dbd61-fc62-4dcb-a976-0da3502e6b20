<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 14-4-9:PM12:00
 * @version 1.0
 */
class Util_Anti
{
    private static function getParams($intUserId, $cmd)
    {
        $req = array(
            'req' => array(
                'rulegroup' => array('ueg'),
                'app' => 'bawu',
                'cmd' => $cmd,
                'user_id' => intval($intUserId),
            ),
        );
        return $req;
    }

    public static function actsctrlQuery($intUserId, $cmd)
    {
        $req = self::getParams($intUserId, $cmd);
        $res = Tieba_Service::call('anti', 'antiActsctrlQuery', $req);
        return $res;
    }

    public static function actsctrlSubmit($intUserId, $cmd)
    {
        $req = self::getParams($intUserId, $cmd);
        $res = Tieba_Service::call('anti', 'antiActsctrlSubmit', $req);
        return $res;
    }

    public static function getCaptcha($intUserId, $cmd, $captchaInfo)
    {
        $req = array_merge_recursive(self::getParams($intUserId, $cmd), array('req' => $captchaInfo));
        $req['call_from'] = 'bawu';
        $req['group'] = 'antiGetCaptcha';
        $res = Tieba_Service::call('anti', 'antiGetCaptcha', $req);
        return $res;
    }

    public static function checkCaptcha($intUserId, $cmd, $captchaInfo)
    {
        $req = array_merge_recursive(self::getParams($intUserId, $cmd), array('req' => $captchaInfo));
        $res = Tieba_Service::call('anti', 'antiCheckCaptcha', $req);
        return $res;
    }

    public static function judgeIfCheckCaptcha($intUserId, $cmd)
    {
        $actsctrlRes = Util_Anti::actsctrlQuery($intUserId, $cmd);
        if ($actsctrlRes['errno'] !== Tieba_Errcode::ERR_SUCCESS || !isset($actsctrlRes['res']['need_vcode'])) {
            throw new Exception(__FUNCTION__ . ' call Anti::actsctrlQuery error, [ret] ' . serialize($actsctrlRes), -1);
        }
        return intval($actsctrlRes['res']['need_vcode']);
    }

    public static function submitIfNeed($intUserId, $cmd)
    {
        $actsctrlRes = Util_Anti::actsctrlSubmit($intUserId, $cmd);
        if ($actsctrlRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            throw new Exception(__FUNCTION__ . ' call Anti::actsctrlSubmit error, [ret] ' . serialize($actsctrlRes), -1);
        }
        return $actsctrlRes;
    }

    public static function getCaptchaIfNeed($intUserId, $cmd, $captchaInfo)
    {
        $captchaRes = Util_Anti::getCaptcha($intUserId, $cmd, array('captcha_rand' => $captchaInfo['captcha_rand'],));
        if ($captchaRes['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            throw new Exception(__FUNCTION__ . ' call Anti::getCaptcha error, [ret] ' . serialize($captchaRes), -1);
        }
        return $captchaRes;
    }

    public static function checkCaptchaIfCorrect($intUserId, $cmd, $captchaInfo)
    {
        $captchaRes = Util_Anti::checkCaptcha($intUserId, $cmd, array('captcha_rand' => $captchaInfo['captcha_rand'],
            'captcha_vcode_str' => $captchaInfo['captcha_vcode_str'], 'captcha_input_str' => $captchaInfo['captcha_input_str'],));
        Bingo_Log::warning("CheckCaptcha.Output[".serialize($captchaRes)."]");
        if ($captchaRes['errno'] !== Tieba_Errcode::ERR_SUCCESS || !isset($captchaRes['res']['captcha_err_no'])) {
            throw new Exception(__FUNCTION__ . ' call Anti::checkCaptcha error, [ret] ' . serialize($captchaRes), -1);
        }
        return intval($captchaRes['res']['captcha_err_no']);
    }

    public static function judge2FetchCaptcha($intUserId, $rule)
    {
        try {
            $needVcode = Util_Anti::judgeIfCheckCaptcha($intUserId, $rule['cmd']);
            if ($needVcode == 1) { // if need, ready to fetch the captcha
                $captchaRes = Util_Anti::getCaptchaIfNeed($intUserId, 'CaptchaTask', $rule['getCaptcha']);
                Util_Tool::renderJsonMsg(0, 'Need to check the captcha.', array('need_vcode' => $needVcode,
                    'captcha_vcode_str' => $captchaRes['res']['captcha_vcode_str'],
                    'captcha_code_type' => $captchaRes['res']['captcha_code_type'],
                    'str_reason' => $rule['str_reason'],));
                return true;
            } else {
                Util_Tool::renderJsonMsg(0, 'Not need to check the captcha.', array('need_vcode' => $needVcode));
            }
            return false;
        } catch (Exception $e) {
            Util_Tool::renderJsonMsg($e->getCode(), $e->getMessage(), array('need_vcode' => 0));
            throw $e;
        }
    }
}