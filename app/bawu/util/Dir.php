<?php
/**
 * Created by PhpStorm.
 * User: huxiaomei
 * Date: 2021/09/01
 * Time: 15:20
 */
class Util_Dir{
    private static $oldFirstDirs = array("商业服务","生活用品","教育培训","品牌","人文自然","金融","科学技术","工农业产品","电影","电视节目","电视剧","网友俱乐部","当代人物","其他","中小学","历史人物","高等院校");
    public static function filterOldDir($arrDirs){
        foreach ($arrDirs as $index => $arrDir){
            $level1Name = self::getUtf8Str($arrDir['level_1_name']);
            if(in_array($level1Name, self::$oldFirstDirs)) {
                unset($arrDirs[$index]);
            }
        }
        $arrDirs = array_values($arrDirs);
        return $arrDirs;
    }

    /**
     * 转换utf8
     * @param string $strInput
     */
    public static function getUtf8Str($strInput){
        $charType = mb_detect_encoding($strInput, array("ASCII",'UTF-8',"GB2312","GBK",'BIG5'));
        if (strtolower($charType) != 'utf-8'){
            $strOutput = iconv('gbk','utf-8',$strInput);
        }else{
            $strOutput = $strInput;
        }
        return $strOutput;
    }

}