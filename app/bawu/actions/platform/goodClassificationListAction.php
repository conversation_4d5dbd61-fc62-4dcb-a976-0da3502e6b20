<?php

/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/5/18
 * Time: 14:12
 */
class goodClassificationListAction extends Actions_Platform_Base
{
    protected $strTemplate = "add_good.php";
    protected $arrNeedPerms = array(
        "can_type2_audit_post",
        "can_type3_audit_post",
    );
    protected $arrKeyFields = array(
        'word',
    );
    protected $bolNeedCheckPriforum = false;

    public function process() {
        $arrOutPut = array();
        $arrInput = array(
            'forum_id' => $this->intFid, //基类字段
            'forum_name' => $this->strFname,//基类字段
            'level_1_name' => $this->arrForumInfo['dir']['level_1_name'],
            'level_2_name' => $this->arrForumInfo['dir']['level_2_name'],
        );
        Page_Platform_Forum::build($arrInput);
        Page_Postaudit_User::build(array());
        $arrInput = array(
            'forum_id' => $this->intFid,
            'need_abstract' => 0,
            'offset' => 0,
            'res_num' => 1,
            'need_photo_pic' => 0,
            'icon_size' => 3,
            'forum_name' => $this->strFname,
            'only_forum_num' => 0,
            'call_forum' => 'pc_bawu',
        );
        $arrRes = Tieba_Service::call('post', 'getFrs', $arrInput);
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning("call post getFrs fail input[" . serialize($arrInput) . "]out[" . serialize($arrRes));
        }
        if (!empty($arrRes['output']['forum_info']['goodclass_show'])) {
            $arrGoodClassification = $arrRes['output']['forum_info']['goodclass_show'];
            foreach($arrGoodClassification as $arrClassificationInfo) {
                $intClassId = intval($arrClassificationInfo['id']);
                if(empty($intClassId)) {
                    continue;
                }
                $arrTemp['id'] = $arrClassificationInfo['id'];
                $arrTemp['goodClassName'] = $arrClassificationInfo['name'];
                $arrOutPut[] = $arrTemp;
            }
        }
        Bingo_Page::assign('forumGoodClassify', $arrOutPut);
    }
}