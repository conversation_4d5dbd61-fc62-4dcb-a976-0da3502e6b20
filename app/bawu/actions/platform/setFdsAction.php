<?php

/**
 * Created by PhpStorm.
 * User: ya<PERSON><PERSON><PERSON><PERSON>
 * Date: 2015/7/22
 * Time: 15:51
 */

/**
 * Class setFdsAction
 * @brief 设置超级验证马
 */
require_once ROOT_PATH."/libs/lib/rpcIdl/RpcIdlFds.class.php";
require_once ROOT_PATH."/libs/lib/rpc/Rpc.class.php";
require_once ROOT_PATH."/libs/lib/rpc/cproxy/RpcCCamelProxy.class.php";
class setFdsAction extends Actions_Platform_Base
{
    const CMD_SET_FORCE_LVCODE = 3150;
    protected $arrNeedPerms = array(
        "can_type2_audit_post",
        "can_type3_audit_post",
    );
    protected $arrKeyFields = array(
        'word',
        'tbs',
        'forumId',
        'grade',
        'hourNum'
    );

    /**
     * @brief 主要逻辑处理函数
     * @return bool
     */
    public function process() {
        if (!$this->_checkParam()) {
            return false;
        }
        if (!$this->_isOpenFds()) {
            return false;
        }
        if (!$this->_CommitNmq()) {
            return false;
        }
        $arrOut = $this->_errRet(0, "success");
        echo Bingo_string::array2json($arrOut);
        return true;
    }

    /**
     * @brief 参数检查
     * @return bool
     */
    private function _checkParam() {
        if (false === Tieba_Tbs::check($this->arrReqInfo['tbs'], true)) {
            Bingo_Log::warning("the tbs check fail");
            $arrOutput = $this->_errRet(100001, 'the tbs check fail');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }

        if (empty($this->arrReqInfo['forumId'])) {
            Bingo_Log::warning("the forum id  is empty");
            $arrOutput = $this->_errRet(100002, 'the forum id is empty');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }

        if (empty($this->arrReqInfo['grade'])) {
            Bingo_Log::warning("the grade is empty");
            $arrOutput = $this->_errRet(100003, 'the grade is empty');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }

        if (empty($this->arrReqInfo['hourNum'])) {
            Bingo_Log::warning("the hourNum is empty");
            $arrOutput = $this->_errRet(100004, 'the hourNum is empty');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }
        return true;
    }

    /**
     * @brief 判断是否开通的超级验证马
     * @return bool
     */
    private function _isOpenFds() {
        if(!RpcIdlFds::hasModule($this->arrReqInfo['forumId'])) {
            Bingo_Log::warning("the forum has not open the fds service");
            $arrOutput = $this->_errRet(100005, 'the forum has not open the fds service');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }
        return true;
    }

    /**
     * @brief 提交nmq
     * @return bool
     */
    private function _CommitNmq() {
        //许多的冗余参数，
        $intStartTime = time();
        $intEndTime = $intStartTime + 3600 * $this->arrReqInfo['hourNum'];
        $ipArr = Bingo_Http_Ip::getConnectIpExt();
        $ip = isset($ipArr['type']) && $ipArr['type'] == 'IPv4' ? intval(Bingo_Http_Ip::newip2long($ipArr['ip'])) : 0;
        $ip6 = isset($ipArr['type']) && $ipArr['type'] == 'IPv6' ? $ipArr['ip'] : '';
        $arrInput = array(
            'command_no' => self::CMD_SET_FORCE_LVCODE,
            'word' => strval($this->arrReqInfo['word']),
            'ip' => $ip,
            'ip6' => $ip6,
            'opgroup' => "bawu",
            'op_ip' => 0,
            'op_ip6' => '',
            'role' => 0,
            'op_role' => 0,
            'op_module' => 0,
            'op_reason' => 0,
            'register_time' => 0,
            'op_time' => time(),
            'op_uid' => intval($this->arrUserInfo['id']),
            'op_uname' => strval($this->arrUserInfo['name']),
            'fid' => $this->intFid,
            'op_is_pm' => 0,
            'begin_time' => $intStartTime,
            'end_time' => $intEndTime,
            'grade' => intval($this->arrReqInfo['grade']),
            'perm_flag' => 0,
            'real_ip' => $ip,
            'real_ip6' => $ip6,
            'now_time' => time(),
            'resource_from' => 8,
            'magic' => 12345,
            'op_username' => strval($this->arrUserInfo['name']),

        );
        $arrRes = Tieba_Commit::commit('anti', 'setForceLVcode', $arrInput);
        if ($arrRes === false || !isset ($arrRes ['err_no']) || $arrRes ['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("commit nmq failed");
            $arrOut = $this->_errRet(100006, "commit nmq failed");
            echo Bingo_string::array2json($arrOut);
            return false;
        }
        return true;
    }

    /**
     * @param $errno
     * @param $errmsg
     * @param array $data
     * @return array
     */
    protected function _errRet($errno, $errmsg, $data = array()) {
        return array(
            'no' => $errno,
            'errmsg' => $errmsg,
            'data' => $data,
        );
    }
}

?>
