<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-05-25 14:16:58
 * @version
 */
 
class cancelFilterAction extends Actions_Platform_Base {
	
	protected $arrKeyFields = array('word', 'tbs', 'list','type');
	protected $arrOptFields = array();
	protected $arrNeedPerms = array("can_type2_audit_post", "can_type3_audit_post");
	protected $strServiceType = "cancelFilter";
	//protected $intCupidID = 265;

	public function process(){
		$arrUids = array();
        	$arrUnames = array();
		if(false === Tieba_Tbs::check($this->arrReqInfo['tbs'], true)) {
			Bingo_Log::warning('Tbs check fail.');
                	Bingo_Page::assign('errno', -1);
                	Bingo_Page::assign('errmsg', 'tbs check fail');
			Bingo_Page::setOnlyDataType("json");
			return;
		}
		$type=intval($this->arrReqInfo['type']);
		$command_no = '';
	    if($type === 0 ) {
	    	$command_no = 214;	    	
	    } elseif ($type === 1 ) {
	    	$command_no = 217;
	    } else {
	    	Bingo_Log::warning('Input type field error:'.$type);
			$this->_jumpPage();
			return false;
	    }
		foreach($this->arrReqInfo['list'] as $arrEach) {
			if($type === 0) {
				$arrUids[] = $arrEach['user_id'];
				$arrUnames[] = $arrEach['user_name'];
			} else {
				$arrRes = fcrypt_hstr_2id('Baidu.Tieba.Bawu.2013', $arrEach['ip']);
				$arrUids[] = intval($arrRes[0]);
				//$arrUids[] = Bingo_Http_Ip::ip2long($arrEach['ip']);
			}
		}
		if(empty($arrUids)) {
			Bingo_Page::assign('errno', 0);
			Bingo_Page::assign('errmsg', '');
			Bingo_Page::setOnlyDataType("json");
			return;
		} else {
			$strUids = implode(",", $arrUids);
			$strUnames = implode(",", $arrUnames);
		}
//		$arrInput = array(
//			'command_no' =>$command_no,
//			'op_uname' => $this->arrUserInfo['name'],
//	    		'op_uid' => $this->arrUserInfo['id'],
//	    		'forum_id'  => $this->intFid,
//			'word' => strval($this->arrReqInfo['word']),
//	    		'user_ids' => $strUids,
//			'user_names' => $strUnames,
//			'need_memo' => 1,
//		);
		//check pm
		$needMemo = 1;
		if(true === $this->arrPerm['can_type3_audit_post']) {
			$needMemo = 0;
		}

        $ipArr = Bingo_Http_Ip::getConnectIpExt();
		$arrInput = array(
			'req' => array(
				'forum_id'=>$this->intFid,
				'user_id'=>$arrUids,
				'user_ip'=>0,//����0
				'user_ip6'=>'',
				'user_name'=>$arrUnames,
				'need_memo'=>$needMemo,//Ĭ��1
				'op_uname'=>$this->arrUserInfo['name'],
				'op_uid'=>$this->arrUserInfo['id'],
                'op_ip'=>isset($ipArr['type']) && $ipArr['type'] == 'IPv4' ? Bingo_Http_Ip::newip2long($ipArr['ip']) : 0,//�ش���û�пɴ�1
                'op_ip6'=>isset($ipArr['type']) && $ipArr['type'] == 'IPv6' ? $ipArr['ip'] : '',
				'from_module'=>'pc_bawu',//���÷�ģ����
				'from_function'=>'cancelFilterAction'//���÷�������
			),
		);
		Bingo_Timer::start('service_bawu_unBlockForumUser');
		$arrOut = Tieba_Service::call('bawu','unBlockForumUser',$arrInput);
		Bingo_Timer::end('service_bawu_unBlockForumUser');
		if(false === $arrOut || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('call service_bawu_unBlockForumUser failed,errno[' . serialize($arrOut) . "input:".serialize($arrInput));
			throw new Exception("call service_bawu_unBlockForumUser failed", 1);
		}
		Bingo_Page::assign('errno', $arrOut['errno']);
		Bingo_Page::assign('errmsg', $arrOut['errmsg']);
		Bingo_Page::setOnlyDataType("json");    
	}
}
?>
