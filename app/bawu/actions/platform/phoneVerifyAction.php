<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date  2013-11-18 10:45:58
 * @version
 */
class phoneVerifyAction extends Actions_Platform_Base
{

    protected $arrKeyFields = array('word');
    protected $arrOptFields = array();
    protected $arrNeedPerms = array("can_type2_audit_post", "can_type3_audit_post","can_bws_limit_bawu_log");
    protected $strServiceType = "phoneVerify";
    protected $strTemplate = "";
    protected $isJsonData = 1;

    protected $addAssistWidgetName = 'addassist_auth_mobile';
    protected $delBawuWidgetName = 'delbawu_auth_mobile';
    protected $expiredTime = 86400;

    const APPID = 1;
    const TPL = 'tb';

    public function process() {
        $widget = new Passport_Pauth_AuthWidget(self::APPID, self::TPL);
        $intUid = intval($this->arrUserInfo['id']);
        $baiduId = Tieba_Session_Socket::getBaiduid();
        $ipArr = Bingo_Http_Ip::getConnectIpExt();
        $ip = isset($ipArr['type']) && $ipArr['type'] == 'IPv4' ? $ipArr['ip'] : '0.0.0.0';
        $ip6 = isset($ipArr['type']) && $ipArr['type'] == 'IPv6' ? $ipArr['ip'] : '';

        $action_type = trim(Bingo_Http_Request::get('action_type'));
        switch ($action_type) {
            case 'addassist': //���С����
                $token = $widget->getToken($intUid, $baiduId, $this->addAssistWidgetName, $ip, array(Passport_Pauth_AuthWidget::FIELD_MOBILE), Passport_Pauth_AuthWidget::SELECT_ANY);
                $authed = $widget->setExpire($this->expiredTime)->setNames($this->addAssistWidgetName)->query($intUid, $baiduId, $ip);
                $isAuthed = $widget->isAuthed();
                break;
            case 'delbawu': //ɾ�����
                $token = $widget->getToken($intUid, $baiduId, $this->delBawuWidgetName, $ip, array(Passport_Pauth_AuthWidget::FIELD_MOBILE), Passport_Pauth_AuthWidget::SELECT_ANY);
                $authed = $widget->setExpire($this->expiredTime)->setNames($this->delBawuWidgetName)->query($intUid, $baiduId, $ip);
                $isAuthed = $widget->isAuthed();
                break;
            case 'resign':  //������ְ
            default:
                $token = $widget->getToken($intUid, $baiduId, 'auth_mobile', $ip, array(Passport_Pauth_AuthWidget::FIELD_MOBILE), Passport_Pauth_AuthWidget::SELECT_ANY);
                $isAuthed = false;
                break;
        }

        //��ȡ�ỰgetToken  
        //$token = $widget->getToken($intUid, $baiduId,'auth_mobile', $ip ,array(Passport_Pauth_AuthWidget::FIELD_MOBILE,Passport_Pauth_AuthWidget::FIELD_EMAIL),Passport_Pauth_AuthWidget::SELECT_ANY );
        //ֻ���ֻ���֤��Token

        //�ж��û��Ƿ�����֤
        //$authed = $widget->setNames('auth_mobile')->query($intUid, $baiduId, $ip);
        //$isAuthed = $widget->isAuthed();
        //ȥ���û���֤�жϣ��ܷ���false


        $arrInput = array(
            'user_name' => trim(Bingo_Http_Request::get('user_name')),
            'type' => trim(Bingo_Http_Request::get('type')),
            'forum_id' => $this->intFid,
        );
        if (isset($arrInput['user_name']) && isset($arrInput['type'])) {
            Page_Platform_PhoneVerify::build($arrInput);
        }
        //$bolHasUserInfo = strlen(strval(Tieba_Session_Socket::getMobilephone())) == 0 && strlen(strval(Tieba_Session_Socket::getEmail())) == 0 ? false : true;
        //ֻ�ж��ֻ�
        $bolHasUserInfo = strlen(strval(Tieba_Session_Socket::getMobilephone())) == 0 ? false : true;
        Bingo_Page::assign('has_phone_or_mail', $bolHasUserInfo);
        Bingo_Page::assign('has_phone', $bolHasUserInfo);
        Bingo_Page::assign('is_authed', $isAuthed);
        Bingo_Page::assign('token', $token);
        Bingo_Page::setOnlyDataType("json");
    }
}

?>