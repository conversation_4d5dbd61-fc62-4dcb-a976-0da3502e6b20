<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * 获取吧内版块详情
 * <AUTHOR> @date 2020-02-18 15:26:58
 * @version
 * http://tieba.baidu.com/bawu2/platform/listForumTab?word=%E5%B0%B1%E7%AE%97%E5%90%8D%E5%AD%97%E5%BE%88%E9%95%BF%E4%B9%9F%E4%BC%9A%E8%A2%AB%E5%8F%91%E7%8E%B0%E7%9A%84&tbs=5a85d7e6d3c8aa491596540683&ie=utf-8
 * http://mtxprod.tieba.otp.baidu.com/bawu2/platform/listForumTab?word=%E5%B0%B1%E7%AE%97%E5%90%8D%E5%AD%97%E5%BE%88%E9%95%BF%E4%B9%9F%E4%BC%9A%E8%A2%AB%E5%8F%91%E7%8E%B0%E7%9A%84&tbs=5a85d7e6d3c8aa491596540683&ie=utf-8
*/

class listForumTabAction extends Actions_Platform_Base {

	protected $arrKeyFields = array('tbs','word');
	protected $arrOptFields = array();
    protected $arrNeedPerms = array('can_type3_audit_post','can_type2_audit_post','can_type1_audit_post');
    protected $strServiceType = "listForumTab"; //todo
    protected $bolNeedCupid   = false;
    protected $needForumTabSmallFlow   = true;  //吧内版块专有逻辑
    protected $bolNeedCheckPriforum = false;

	/**
	 * @param
	 * @return boolean
	 */
	public function process(){
		
		if(false === Tieba_Tbs::check($this->arrReqInfo['tbs'], true)) {
			Bingo_Log::warning('Tbs check fail.');
			$this->_setOutput(Tieba_Errcode::ERR_TBSIG_CHECK_FAIL, 'tbs check fail');
			return false;
		}

        $fid = intval($this->intFid);
        if( $fid <= 0 ){
        	Bingo_Log::warning('fid is empty.');
			$this->_setOutput(Tieba_Errcode::ERR_PARAM_ERROR, 'param error');
			return false;
        }
  
  		//小流量吧判断
		if( !$this->bolForumTabSmallFlow ){
            $this->_setOutput(21,'auth failed',0);
            return true;
        }
        
        $bolIsPm = $this->arrPerm['can_type3_audit_post'];
		$bolIsMgr = $this->arrPerm['can_type2_audit_post'];
		$bolIsAssist = $this->arrPerm['can_type1_audit_post'] && $this->arrPerm['can_member_top'];
        
        if( !$bolIsPm && !$bolIsMgr && !$bolIsAssist ){
			$this->_setOutput(21,'auth failed',0);
			return false;
        }
       	
       	$arrRet['tab_list'] = array();

		$arrInput = array(
			'forum_id' => $fid,
		);
		
		//从吧属性取
		$arrOut = Tieba_Service::call('forum', 'getBtxInfo', $arrInput);
		if ($arrOut === false || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('call service  forum::getBtxInfo method fail. input = ' . serialize($arrInput) . 'output = ' . serialize($arrOut, true));
			$this->_setOutput(-1, 'get fail');
			return false;
		}
	
		$order = 1;
		foreach( $arrOut['attrs']['tabs_info']['display_info'] as $key => $item ){
			if( intval($item['tab_id']) <= 0 ){
				continue;
			}
			//过滤吧友好物tab 不让吧主操作
			if($arrOut['attrs']['tabs_info']['tab_info'][$item['tab_id']]['tab_type'] == 3){
                continue;
            }
			$temp['tab_id'] = $item['tab_id'];
			$temp['rank'] = $order++;
			$temp['tab_name'] = $item['tab_name'];
			$temp['tab_type'] = $arrOut['attrs']['tabs_info']['tab_info'][$item['tab_id']]['tab_type'];
			$arrRet['tab_list'][] = $temp;
		}
		
		$this->_setOutput(Tieba_Errcode::ERR_SUCCESS,'',$arrRet);
	}
	
	/**
	 * 
	 * @param unknown_type $error
	 * @param unknown_type $errmsg
	 * @return boolean
	 */
	private function _setOutput($error, $errmsg = '',$data = array()) {
		$errmsg = !empty($errmsg) ? $errmsg : Tieba_Error::getErrmsg($error);
		Bingo_Page::assign('errno', $error);
		Bingo_Page::assign('errmsg', $errmsg);
		Bingo_Page::assign('data', $data);
		Bingo_Page::setOnlyDataType("json");
		return true;
	}
}
