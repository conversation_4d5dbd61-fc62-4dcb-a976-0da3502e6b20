<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-05-25 14:16:58
 * @version
 */
 
class addFilterAction extends Actions_Platform_Base {
	
	protected $arrKeyFields = array('word', 'tbs', 'user_id','reason','day');
	protected $arrOptFields = array();
	protected $arrNeedPerms = array("can_type2_audit_post", "can_type3_audit_post");
	protected $strServiceType = "addFilter";
	protected $strPmPerm = "can_type3_audit_post";
	private   static $day_check = array(1,3,10);
	//protected $intCupidID = 265;

	public function process(){
		if(false === Tieba_Tbs::check($this->arrReqInfo['tbs'], true)) {
			Bingo_Log::warning('Tbs check fail.');
                	Bingo_Page::assign('errno', -1);
                	Bingo_Page::assign('errmsg', 'tbs check fail');
			Bingo_Page::setOnlyDataType("json");
			return false;
		}
		
		$day = $this->arrReqInfo['day'];
		$reason = $this->arrReqInfo['reason'];
		if(!in_array($day,self::$day_check) )
		{
			Bingo_Log::warning('day check fail. day['.$day.']');
			Bingo_Page::assign('errno', -1);
			Bingo_Page::assign('errmsg', 'day check fail');
			Bingo_Page::setOnlyDataType("json");
			return false;
		}
		$arrUids []= $this->arrReqInfo['user_id'];
		$arrUserDict =array();
		if(!empty($arrUids)) {
			$arrUnames = Util_User::getUnameByUids(array('user_id' =>$arrUids));
			if(false === $arrUnames) {
				Bingo_Log::warning("Can't get unames by uids .Input[".serialize($arrUids)."]");
				Bingo_Page::assign('errno', Tieba_Errcode::ERR_HOME_UI_USER_GETUN);
				Bingo_Page::assign('errmsg', 'get uname fail');
				Bingo_Page::setOnlyDataType("json");
				return false;
			} else {
				foreach($arrUnames as $arrEach) {
					$arrUserDict[$arrEach['user_id']] = $arrEach['user_name'];
				}
			}
		}
		$user_name = $arrUserDict[$this->arrReqInfo['user_id']];
		if(empty($user_name)) {
			Bingo_Log::warning('Uid not exist.');
			Bingo_Page::assign('errno', Tieba_Errcode::ERR_DIFANG_USER_NOT_EXIST);//260401
			Bingo_Page::assign('errmsg', 'no user');
			Bingo_Page::setOnlyDataType("json");
			return false;
		}
        $ipArr = Bingo_Http_Ip::getConnectIpExt();
        $ip = isset($ipArr['type']) && $ipArr['type'] == 'IPv4' ? $ipArr['ip'] : '0.0.0.0';
        $ip6 = isset($ipArr['type']) && $ipArr['type'] == 'IPv6' ? $ipArr['ip'] : '';
		$arrNMQInput = array (
				'user_id' =>  $this->arrReqInfo['user_id'],
				'user_name' => $user_name,
				'forum_id' => $this->intFid,
				'word' => $this->strFname,
				'op_uname' => $this->arrUserInfo['name'],
				'op_uid' =>$this->arrUserInfo['id'],
				'op_time' => time (),
				'op_ip'=>$ip,
                'op_ip6'=>$ip6,
				'command_no' => 213,
				'ip' => 0,
				'day_num' => $day,
				'need_memo' => $this->intPmFlag,
				'now_time' => time (),
				'other' => mc_pack_array2pack ( array ('desc' => $reason ) ),
				'opgroup'=>'bawu',
				'call_from'=>'bawu',
		 );
		
		
		$error = Tieba_Errcode::ERR_SUCCESS;
		$arrNMQRes = Tieba_Commit::commit ( 'anti', 'filterfrmUser', $arrNMQInput );
		if ($arrNMQRes === false || ! isset ( $arrNMQRes ['err_no'] ) || $arrNMQRes ['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
			$error = Tieba_Errcode::ERR_RPC_CALL_FAIL;
			Bingo_Log::warning ( " Tieba_Commit::commit fail" ."input:[".serialize ( $arrNMQInput )."] out:[" .serialize ( $arrNMQRes ) ."]");
		}
	
		Bingo_Page::assign('errno', $error);
		Bingo_Page::assign('errmsg', Tieba_Error::getErrmsg($error));
		Bingo_Page::setOnlyDataType("json"); 
		
	}
}
?>
