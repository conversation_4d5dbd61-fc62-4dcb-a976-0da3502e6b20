<?php
/**
 * Created by PhpStorm.
 * User: shiyibo
 * Date: 14-11-26
 * Time: 下午12:02
 */
class listCdDetailAction extends Actions_Platform_Base {
    protected $arrKeyFields   = array('word','id');
    protected $arrOptFields   = array();
    protected $arrNeedPerms   = array("can_type2_audit_post", "can_type3_audit_post");
    protected $strServiceType = "pluto";
    protected $strTemplate    = "bus_activities/bus_activities_detail.php";
	protected $_conf          = array();
    protected $_arrCategory   = array();

    public function process(){
		$arrForumInput = array(
			'forum_id' => $this->intFid,
			'forum_name' => $this->strFname,
			'level_1_name' => $this->arrForumInfo['dir']['level_1_name'],
            'level_2_name' => $this->arrForumInfo['dir']['level_2_name'],
		);

		Page_Platform_Forum::build($arrForumInput);
		Page_Postaudit_User::build();

        // 读取配置文件中的数据
		$this->_conf = Lib_StarConf::getConf();
		if (null === $this->_conf) {
			$this->_buildReturn(
				Lib_StarConf::ERR_LOAD_CONF_FAIL,
            	Lib_StarConf::$arrErrorMap[Lib_StarConf::ERR_LOAD_CONF_FAIL]
			);
			return false;
		}

		$arrData  = array();
		$arrParam = array(
			'id' => $this->arrReqInfo['id'],
		);
		Bingo_Timer::start('service_pluto_getCdInfoById');
		$arrRet = Tieba_Service::call('pluto', 'getCdInfoByIds', $arrParam, null, null, 'post', 'php', 'gbk');
		Bingo_Timer::end('service_pluto_getCdInfoById');
		if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
			$strMsg = sprintf('call pluto::getCdInfo fail with input [%s] output [%s]', serialize($arrParam), serialize($arrRet));
			Bingo_Log::warning($strMsg);
			$this->_buildReturn(
				Tieba_Errcode::ERR_CALL_SERVICE_FAIL,
				'call service fail'
			);
			return false;
		}
		$arrCdInfo = $arrRet['data'];

		$arrParam = array(
			'id' => array(
				$this->arrReqInfo['id'],
			),
		);
		$arrRet = Tieba_Service::call('pluto', 'getCdNumById', $arrParam, null, null, 'post', 'php', 'gbk');
		if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
			Bingo_Log::fatal("call pluto::getCdInfoByFid fail with input [".serialize($arrParam).'] output ['.serialize($arrRet).']');
			$this->_buildReturn(
				Tieba_Errcode::ERR_CALL_SERVICE_FAIL,
				'call service fail'
			);
			return false;
		}

		$arrCdInfo[0]['cur_oil_num'] = (int)$arrRet['data'][$this->arrReqInfo['id']]['oil_num'];

		$arrRet = Tieba_Service::call('pluto', 'getTravelListById', $arrParam, null, null, 'post', 'php', 'gbk');
        if(false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning("call pluto::getTravelList fail with input [".serialize($arrParam).'] output ['.serialize($arrRet).']');
			$this->_buildReturn(
				Tieba_Errcode::ERR_CALL_SERVICE_FAIL,
				'call service fail'
			);
			return false;
		}
		$arrTravelList  = $arrRet['data'][$this->arrReqInfo['id']];

		// 拉取策略
        $intCategory = $arrCdInfo[0]['category'];
        $arrParam    = array(
            'type'   => array($intCategory,),
        );
        $arrRet      = Tieba_Service::call('pluto', 'getCategoryByType', $arrParam);
        if(false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning("call pluto::getCategoryByType fail with input [".serialize($arrParam).'] output ['.serialize($arrRet).']');
			$this->_buildReturn(
				Tieba_Errcode::ERR_CALL_SERVICE_FAIL,
				'call service fail'
			);
			return false;
		}

        $this->_arrCategory = $arrRet['data'][$intCategory];

		$arrData = $this->_adjustStatus($arrCdInfo);
		$arrData[0]['travel_list'] = $arrTravelList;

		$this->_buildReturn(
			Tieba_Errcode::ERR_SUCCESS,
			'success',
			$arrData
		);
    }

    protected function _adjustStatus($arrInput) {
        $arrData = array();
        $intTime = time();
        foreach($arrInput as $key => $arrItem) {
            $intStartTime      = intval($arrItem['start_time']);
            $intEndTime        = intval($arrItem['end_time']);
            $intTravelTime     = intval($arrItem['travel_time']);
            $intStatus         = intval($arrItem['status']);
            $intBusNum         = intval($arrItem['bus_num']);
            $intTotalOil       = intval($arrItem['oil_num']);
            $intCurOil         = intval($arrItem['cur_oil_num']);
            $intForumPerBus    = intval($this->_arrCategory['travel_num']);
            $intInterval       = intval($this->_arrCategory['travel_time']);
            $intTravelInterval = $intBusNum * $intForumPerBus * $intInterval;
			$intCrowdDone      = $intTravelTime + $intTravelInterval;
            switch ($intStatus) {
                case 1:
                    // 1: 待审核，status=1
                    $arrInput[$key]['status'] = Lib_StarConf::STATUS_AUDIT;
                    break;
                case 2:
                    if ($intTime < $intStartTime) {
                        // 2: 审核通过，巡游等待
                        $arrInput[$key]['status'] = Lib_StarConf::STATUS_CROWDFUNDING_PRE;
                    }
                    if ($intStartTime <= $intTime && $intTime < $intEndTime && $intCurOil < $intTotalOil) {
                        // 6: 巡游中，判断条件 在巡游时间内，油漆没筹集满
                        $arrInput[$key]['status'] = Lib_StarConf::STATUS_CROWDFUNDING_ING;
                    }
                    if ($intStartTime <= $intTime && $intTime < $intTravelTime && $intCurOil >= $intTotalOil) {
                        // 7: 巡游倒计时 判断条件 活动开始 筹集满油漆 巡游时间没到
                        $arrInput[$key]['status'] = Lib_StarConf::STATUS_TRAVEL_PRE;
                    }
                    if ($intTravelTime <= $intTime && $intCurOil >= $intTotalOil) {
                        //8: 巡游中 判断条件 筹集满油漆 巡游时间到来
                        $arrInput[$key]['status'] = Lib_StarConf::STATUS_TRAVEL_ING;
                    }
                    if ( $intCrowdDone <= $intTime && $intCurOil >= $intTotalOil){
                        // 4: 巡游结束 判断条件 筹集满油漆 巡游开始时间 加上 需要巡游的时间 在当前时间之前
                        $arrInput[$key]['status'] = Lib_StarConf::STATUS_TRAVEL_DONE;
                    }
                    if ($intEndTime <= $intTime && $intCurOil < $intTotalOil) {
                        // 5:应援失败，判断条件 众筹结束 油漆未凑满
                        $arrInput[$key]['status'] = Lib_StarConf::STATUS_CROWDFUNDING_FAIL;
                    }
                    break;
                case 3:
                    // 审核拒绝
                    $arrInput[$key]['status'] = Lib_StarConf::STATUS_REFUSE;
                    break;
                default:
                    break;
            }
        }

        return $arrInput;
    }

	/**
	 * @desc 写一个简短的错误返回封装函数
	 * @param errno  : uint32_t : 错误号
	 * @param errmsg : errmsg   : 错误信息
	 * @param data   : mix      : 返回数据
	 * @return
	 */
	protected function _buildReturn($errno, $errmsg, $data) {
		Bingo_Page::assign('errno', $errno);
		Bingo_Page::assign('errmsg', $errmsg);
		Bingo_Page::assign('data', $data);
	}

}
?>
