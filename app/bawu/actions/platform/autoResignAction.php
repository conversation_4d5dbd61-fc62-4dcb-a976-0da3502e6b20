<?php

/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/5/16
 * Time: 22:06
 */
class autoResignAction extends Actions_Platform_Base
{
    const APPID = 1;
    const TPL = 'tb';
    const PROFESSION_FORUM_FLAG = 2;
    protected $arrKeyFields = array('word', 'tbs', 'userRole','reason');
    protected $bolNeedPerm = false;
    protected $arrOptFields = array();
    protected $addAssistWidgetName = 'auth_mobile';
    protected $expiredTime = 120;
    const ROLE_ASSIST = 'assist';
    const ROLE_PICADMIN = 'picadmin';
    const ROLE_VIDEOADMIN = 'videoadmin';
    const ROLE_VOICEADMIN = 'voiceadmin';
    const ROLE_PUBLICATION = 'publication';
    const ROLE_PUBLICATION_EDITOR = 'publication_editor';
    const ROLE_BROADCAST_ADMIN = 'broadcast_admin';
    const ROLE_MANAGER = 'manager';
    const ROLE_PROFESSION_MANAGER = 'profession_manager';


    private function _checkPhoneVerified() {
        $widget = new Passport_Pauth_AuthWidget(self::APPID, self::TPL);
        $intUid = Tieba_Session_Socket::getLoginUid();
        $baiduId = Tieba_Session_Socket::getBaiduid();
        $ipArr = Bingo_Http_Ip::getConnectIpExt();
        if( isset($ipArr['type']) && $ipArr['type'] == 'IPv6' ){
            $ip = '0.0.0.0';
            $ip6 = $ipArr['ip'];
        }else{
            $ip = $ipArr['ip'];
            $ip6 = '';
        }
        $authed = $widget->setExpire($this->expiredTime)->setNames($this->addAssistWidgetName)->query($intUid, $baiduId, $ip);
        $isAuthed = $widget->isAuthed();
        if ($isAuthed) {
            return true;
        } else {
            return false;
        }
    }
    public function process() {
        if (false === Tieba_Tbs::check($this->arrReqInfo['tbs'], true)) {
            Bingo_Log::warning("the tbs check fail");
            $arrOutput = $this->_errRet(100001, 'the tbs check fail');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }

        if (!$this->_checkPhoneVerified()) {
            Bingo_Log::warning('User Phone Verified Failed.');
            $arrOutput = $this->_errRet(100002, 'the user phone check fail');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }

        $strRoleType = strval($this->arrReqInfo['userRole']);
        $strReason = strval($this->arrReqInfo['reason']);
        /*
         * 职业吧主特殊处理逻辑
         */
        $arrForumAttr = Util_Forum::getForumAttr($this->intFid);
        if($strRoleType === self::ROLE_PROFESSION_MANAGER) {
            if (isset($arrForumAttr['forum_profession']['status']) && self::PROFESSION_FORUM_FLAG === intval($arrForumAttr['forum_profession']['status'])) {
                $arrInput = array(
                    'forum_id' => $this->intFid,
                    'user_id' => $this->arrUserInfo['id'],
                    'user_name' => $this->arrUserInfo['name'],
                    'op_uname' => $this->arrUserInfo['name'],
                );
                $arrOutput = Tieba_Service::call('profession', 'offlineManagerByForumIdAndUid', $arrInput);
                if ( false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== intval($arrOutput['errno'])) {
                    Bingo_Log::warning("call profession offlineManagerByForumIdAndUid failed input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "]");
                    $arrOutput = $this->_errRet(100005, 'resign failed');
                    echo Bingo_String::array2json($arrOutput);
                    return false;
                }
                if (isset($arrOutput['data']['role'])) {
                    $strRole = $arrOutput['data']['role'];
                    if (self::ROLE_PROFESSION_MANAGER === $strRole) {
                        $arrInput = array(
                            'forum_id' => $this->intFid,
                        );
                        $arrOutput = Tieba_Service::call('Klose', 'delProviderFromProfession', $arrInput);
                        if ( false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== intval($arrOutput['errno'])) {
                            Bingo_Log::warning("call klose delProviderFromProfession failed input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "]");
                            $arrOutput = $this->_errRet(100005, 'resign failed');
                            echo Bingo_String::array2json($arrOutput);
                            return false;
                        }
                    }
                }

            }
            //职业吧主辞职，直接将吧变为普通吧，以防用户无法申请大吧主
            if (isset($arrForumAttr['forum_profession'])){
                $arrInput = array(
                    'forum_id' => $this->intFid,
                    'attr_name' => 'forum_profession',
                );
                $res   = Tieba_Service::call('forum', 'delForumAttr', $input, NULL, NULL, 'post', 'php', 'utf-8');
                if(false === $res || Tieba_Errcode::ERR_SUCCESS !== $res['errno']){
                    Bingo_Log::warning("call forum delForumAttr failed input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "]");
                    $arrOutput = $this->_errRet(100005, 'resign failed');
                    echo Bingo_String::array2json($arrOutput);
                    return false;
                }
            }
        }
        $arrParamsList = array(
            'forum_id' => $this->intFid,
            'forum_name' => $this->strFname,
            'user_id' => $this->arrUserInfo['id'],
            'user_name' => $this->arrUserInfo['name'],
            'op_user_id' => $this->arrUserInfo['id'],
            'op_user_name' => $this->arrUserInfo['name'],
            'role_name' => $strRoleType,
            'op_uid' =>$this->arrUserInfo['id'],
            'op_uname' => $this->arrUserInfo['name'],
            'op_ip' => 1,
            'op_ip6' => '',
            'from_module' => 'pc_bawu',
            'from_function' => 'autoResign',
            'memo' => $strReason,
        );
        $arrInput = array(
            'req' => $arrParamsList
        );
        $strServiceName = 'bawu';
        $strMethodName = 'autoResign';
        $arrOutput = Tieba_Service::call($strServiceName, $strMethodName, $arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== intval($arrOutput['errno'])) {
            Bingo_Log::warning("call bawu cancelUserRole failed input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "]");
            $arrOutput = $this->_errRet(100005, 'resign failed');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }
        $arrOutput = $this->_errRet(0, 'success');
        echo Bingo_String::array2json($arrOutput);
        return true;
    }

    protected function _errRet($errno, $errmsg, $data = array()) {
        return array(
            'no' => $errno,
            'errmsg' => $errmsg,
            'data' => $data,
        );
    }
}

?>
