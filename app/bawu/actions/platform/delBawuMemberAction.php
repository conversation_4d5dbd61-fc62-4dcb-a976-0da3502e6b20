<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-05-25 14:16:58
 * @version
 */
class delBawuMemberAction extends Actions_Platform_Base
{

    protected $arrKeyFields = array('word', 'tbs', 'user_id', 'type');
    protected $arrOptFields = array();
    protected $arrNeedPerms = array("can_type2_audit_post", "can_type3_audit_post");
    protected $strServiceType = "delBawuMember";
    protected $strPmPerm = "can_type3_audit_post";
    protected $delBawuWidgetName = 'delbawu_auth_mobile';
    protected $expiredTime = 86400;
    protected $arrRoleType = array(
        'assist',
        'picadmin',
        'videoadmin',
        'voiceadmin',
        'publication',
        'publication_editor',
        'broadcast_admin',
    );
    const APPID = 1;
    const TPL = 'tb';
    const ERR_TYPE_WEBPOLICE = 1;

    //protected $intCupidID = 265;

    /**
     * 删除所有吧务时需要进行手机验证码验证
     */
    private function _checkPhoneVerified() {
        $widget = new Passport_Pauth_AuthWidget(self::APPID, self::TPL);
        $intUid = Tieba_Session_Socket::getLoginUid();
        $baiduId = Tieba_Session_Socket::getBaiduid();
        $ipArr      = Bingo_Http_Ip::getConnectIpExt();
        $ip      =  isset($ipArr['type']) && $ipArr['type'] == 'IPv4' ? $ipArr['ip'] : '0.0.0.0';
        $ip6     =  isset($ipArr['type']) && $ipArr['type'] == 'IPv6' ? $ipArr['ip'] : '';
        $authed = $widget->setExpire($this->expiredTime)->setNames($this->delBawuWidgetName)->query($intUid, $baiduId, $ip);
        $isAuthed = $widget->isAuthed();
        if ($isAuthed) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * http://tieba.baidu.com/bawu2/platform/delBawuMember
     *
     * @return bool
     * @throws Exception
     */
    public function process() {
        if (!$this->_checkPhoneVerified()) {
            Bingo_Log::warning('User Phone Verified Failed.');
            Bingo_Page::assign('errno', -1);
            Bingo_Page::assign('errmsg', 'User Phone Verified Failed');
            Bingo_Page::setOnlyDataType("json");
            return false;
        }

        if (false === Tieba_Tbs::check($this->arrReqInfo['tbs'], true)) {
            Bingo_Log::warning('Tbs check fail.');
            Bingo_Page::assign('errno', -1);
            Bingo_Page::assign('errmsg', '');
            Bingo_Page::setOnlyDataType("json");
            return false;
        }

        $intUid = intval($this->arrReqInfo['user_id']);
        $arrInput = array(
            'user_id' => array($intUid),
        );
        $arrNames = Util_User::getUnameByUids($arrInput);
        $strUname = strval($arrNames[0]['user_name']);
        if (strlen($strUname) <= 0 || $strUname === null) {
            Bingo_Log::warning('Uid not exist.');
            Bingo_Page::assign('errno', -1);
            Bingo_Page::assign('errmsg', '');
            Bingo_Page::setOnlyDataType("json");
            return false;
        }


//        $arrInput = array(
//            'forum_id' => $this->intFid,
//            'forum_name' => $this->strFname,
//            'user_id' => $intUid,
//            'user_name' => $strUname,
//            'op_user_id' => $this->arrUserInfo['id'],
//            'op_user_name' => $this->arrUserInfo['name'],
//            'need_memo' => $this->intPmFlag,
//        );
        $strType = strval(trim($this->arrReqInfo['type']));
        if (!in_array($strType,$this->arrRoleType)) {
            Bingo_Log::warning('role type is error.');
            Bingo_Page::assign('errno', -1);
            Bingo_Page::assign('errmsg', '');
            Bingo_Page::setOnlyDataType("json");
            return false;
        }
        if('assist' === $strType){
            $strErrmsg = '用户为网警小吧主，请勿下任';
            $strErrmsg = Bingo_Encode::convert($strErrmsg, Bingo_Encode::ENCODE_GBK,Bingo_Encode::ENCODE_UTF8);
            $arrInput = array(
                'table_name' => 'tb_wordlist_redis_web_police_assist',
                'key' => $intUid,
            );
            $arrOutput = Tieba_Service::call('wordlist', 'queryWLItemDirectly', $arrInput);
            if(false == $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){

                Bingo_Log::warning('read wordlist failed!');
                Bingo_Page::assign('errno', -1);
                Bingo_Page::assign('errmsg', $strErrmsg);
                Bingo_Page::setOnlyDataType("json");
                return false;
            }
            if(isset($arrOutput['data'][$intUid]) && !empty($arrOutput['data'][$intUid])){
                Bingo_Log::warning($strErrmsg);
                Bingo_Page::assign('errno', -1);
                Bingo_Page::assign('errmsg', $strErrmsg);
                Bingo_Page::assign('errtype', self::ERR_TYPE_WEBPOLICE);
                Bingo_Page::setOnlyDataType("json");
                return false;
            }
        }
        //$strPermMethod = 'delUserRole';
        // $arrInput['role_name'] = $strType;

        $ipArr      = Bingo_Http_Ip::getConnectIpExt();
        $ip      =  isset($ipArr['type']) && $ipArr['type'] == 'IPv4' ? Bingo_Http_Ip::newip2long($ipArr['ip']) : 0;
        $ip6     =  isset($ipArr['type']) && $ipArr['type'] == 'IPv6' ? $ipArr['ip'] : '';
        $input = array(
            'req' => array(
                'forum_id' => $this->intFid,
                'forum_name' => $this->strFname,
                'user_id' => $intUid,
                'user_name' => $strUname,
                'op_user_id' => $this->arrUserInfo['id'],
                'op_user_name' => $this->arrUserInfo['name'],
                'need_memo' => $this->intPmFlag,
                'role_name' => $strType,
                'op_uid' => $this->arrUserInfo['id'],
                'op_uname' => $this->arrUserInfo['name'],
                'op_ip' => $ip,
                'op_ip6' => $ip6,
                'from_module' => 'pc_bawu',
                'from_function' => 'delBawuMemberAction',
            ),
        );
        Bingo_Timer::start('service_bawu_cancelUserRole');
        $arrOut = Tieba_Service::call('bawu', 'cancelUserRole', $input);
        Bingo_Timer::end('service_bawu_cancelUserRole');
        if ($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS || $arrOut === false) {
            Bingo_Log::warning('call service_bawu_cancelUserRole failed,errno:' . serialize($arrOut) . "input:" . serialize($input));
        }
        if ($arrOut === false) {
            throw new Exception('call service_bawu_cancelUserRole failed', 1);
        }

        Bingo_Page::assign('errno', $arrOut['errno']);
        Bingo_Page::assign('errmsg', $arrOut['errmsg']);
        Bingo_Page::setOnlyDataType("json");
    }
}

?>
