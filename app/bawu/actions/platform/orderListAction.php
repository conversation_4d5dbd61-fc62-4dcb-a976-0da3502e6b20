<?php

class orderListAction extends Actions_Platform_Base
{
    protected $arrKeyFields   = array('word');
    protected $arrOptFields   = array('pn', 'query_type');
    protected $arrNeedPerms   = array('can_type2_audit_post', 'can_type3_audit_post');

    protected $strServiceType = 'orderList';
    protected $strTemplate    = 'platform_clout_man.php';

    public function process()
    {
        // check provider perm
        $arrInput = array(
            'forum_id' => $this->intFid,
        );
        $arrOutput = Tieba_Service::call('klose', 'getProviderByFid', $arrInput);
        $intAllowedUserId = intval($arrOutput['output']['user_id']);
        if ($intAllowedUserId === intval($this->arrUserInfo['id']))
        {
            $intByFlag = true;
        }
        else
        {
            $intByFlag = false;
        }
        
        if ($intByFlag !== true)
        {
            Bingo_Page::assign('no', -1);
            Bingo_Page::assign('errMsg', 'no perm');
            return false;
        }

        $intPageNum   = intval($this->arrReqInfo['pn']);
        $intQueryType = intval($this->arrReqInfo['query_type']);

        if ($intPageNum <= 0)
        {
            $intPageNum = 1;
        }

        $arrInput = array(
            'target_forum_id' => $this->intFid,
            'page_num'        => $intPageNum,
            'page_size'       => 10,
            'query_type'      => $intQueryType,
        );
        
        $arrOutput   = Tieba_Service::call('klose', 'getOrderListByTargetForumId', $arrInput);
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS)
        {
            Bingo_Page::assign('no', -2);
            Bingo_Page::assign('errMsg', 'get data failed');
            return false;
        }
        $arrPageInfo = $arrOutput['output']['page_info'];
        $arrRes = array();
        foreach ($arrOutput['output']['order_list'] as $arrOrder)
        {
            $arrItemList = array();
            foreach ($arrOrder as $arrItem)
            {
                $arrForumIds[intval($arrItem['target_forum_id'])] = 1;
                $arrItemList[] = array(
                    'order_item_id'   => $arrItem['order_item_id'],
                    'ad_forum_id'     => $arrItem['target_forum_id'],
                    'ad_type'         => $arrItem['product_type'],
                    'ad_id'           => $arrItem['product_id'],
                    'ad_target'       => $arrItem['product_target'],
                    'ad_content_id'   => $arrItem['product_content_id'],
                    'ad_title'        => $arrItem['product_content']['thread_title'],
                    'ad_content'      => $arrItem['product_content']['thread_content'],
                    'ad_start_time'   => $arrItem['start_time'],
                    'ad_end_time'     => $arrItem['end_time'] - 1,
                    'currency_type'   => $arrItem['currency_type'],

                    'normal_price'    => (double)$arrItem['price'],
                    'real_price'      => (double)$arrItem['price'] * (double)$arrItem['discount'],
                    'discount_price'  => (double)$arrItem['price'] * (double)$arrItem['discount'],
                    'provider_income' => (double)$arrItem['price'] * (double)$arrItem['discount'] * (double)$arrItem['provider_rebate'],

                    'reason'          => $arrItem['reason'],
                    'unit_price'      => $arrItem['unit_price'],
                    'token'           => md5(sprintf("klosEiFrame%dToKen%d", $arrItem['order_id'], $arrItem['product_content_id'])),
                );
            }
            $arrForumIds[intval($arrItem['forum_id'])] = 1;
            $arrRes[] = array(
                'order_id'         => $arrItem['order_id'],
                'order_num'        => sprintf("BY%08d", $arrItem['order_id']),
                'forum_id'         => $arrItem['forum_id'],
                'status'           => intval($arrItem['status']),
                'user_id'          => $arrItem['user_id'],
                'create_time'      => $arrItem['created_time'],
                'schedule_list'    => $arrItemList,
            );
        }

        $arrInput = array(
            'forum_id' => array_keys($arrForumIds),
        );
        $arrOutput = Tieba_Service::call('forum', 'getFnameByFid', $arrInput);

        foreach ($arrRes as $key => $arrNode)
        {
            $arrRes[$key]['apply_forum_name'] = $arrOutput['forum_name'][(int)$arrNode['forum_id']]['forum_name'];
            foreach ($arrNode['schedule_list'] as $subkey => $arrSubNode)
            {
                $arrRes[$key]['schedule_list'][$subkey]['ad_forum_name'] = $arrOutput['forum_name'][intval($arrSubNode['ad_forum_id'])]['forum_name'];
            }
        }

        $arrForum = array(
            'forum_id'   => $this->intFid,
            'forum_name' => $this->strFname,
        );

        Page_Platform_Forum::build($arrForum);
        Page_Postaudit_User::build();

        Bingo_Page::assign('no',         Tieba_Errcode::ERR_SUCCESS);
        Bingo_Page::assign('errMsg',     Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS));
        Bingo_Page::assign('order_list', $arrRes);
        Bingo_Page::assign('query_type', $intQueryType);
        Bingo_Page::assign('page_info',  $arrPageInfo);
        Bingo_Page::assign('tbs',        Tieba_Tbs::gene(true));

        return true;
    }
}
