<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-06 16:33:58
 * @version
 */

class introduceAction extends Actions_Platform_Base {
	
	protected $arrKeyFields = array('word');
	protected $arrOptFields = array();
	protected $arrNeedPerms = array("can_bws_limit_bawu_log","can_type2_audit_post", "can_type3_audit_post");
	protected $strServiceType = "introduce";
	protected $strTemplate = "introduce.php";
	protected $bolNeedFields   = false;     //�Ƿ���Ҫ��ò�����Ϣ
	protected $bolNeedUserInfo = false;  	//�Ƿ���Ҫ����û���Ϣ
	protected $bolNeedCupid    = false;		//�Ƿ���Ҫ����������Ϣ
	protected $bolNeedPerm     = false;		//�Ƿ���Ҫ��ò����Ȩ����Ϣ
	//protected $intCupidID = 285;

	public function process(){

	}
}
?>
