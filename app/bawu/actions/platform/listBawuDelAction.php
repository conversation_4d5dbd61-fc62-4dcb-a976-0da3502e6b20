<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-05-24 15:26:58
 * @version
 */

class listBawuDelAction extends Actions_Platform_Base {
	
	protected $arrKeyFields = array('word');
	protected $arrOptFields = array('stype', 'svalue', array('pn'=>1), array('tn'=>15), 'begin', 'end', 'op_type');
	protected $arrNeedPerms = array("can_type2_audit_post", "can_type3_audit_post");
	protected $strServiceType = "listBawuDel";
	protected $strTemplate = "del_post.php";
	//protected $intCupidID = 265;

	public function process(){
		//build page
		$arrListInput = array(
			'forum_id' => intval($this->intFid),
			'cur_pn' => intval($this->arrReqInfo['pn']),
			'offset' => (intval($this->arrReqInfo['pn'])-1)*intval($this->arrReqInfo['tn']),
			'res_num' => intval($this->arrReqInfo['tn']),
			'del_type' => 0,
		);
		if(isset($this->arrReqInfo['stype']) && !empty($this->arrReqInfo['stype'])) {
			$arrListInput['search_type'] = strval($this->arrReqInfo['stype']);
		}
		if(isset($this->arrReqInfo['svalue']) && !empty($this->arrReqInfo['svalue'])) {
			$arrListInput['search_value'] = trim(strval($this->arrReqInfo['svalue']));
		}
		if(isset($this->arrReqInfo['begin']) && !empty($this->arrReqInfo['begin'])) {
			$arrListInput['begin_time'] = intval($this->arrReqInfo['begin']);
		}
		if(isset($this->arrReqInfo['end']) && !empty($this->arrReqInfo['end'])) {
			$arrListInput['end_time'] = intval($this->arrReqInfo['end']);
		}
		Page_Platform_DelPostList::build($arrListInput);
		$arrForumInput = array(
			'forum_id' => $this->intFid,
			'forum_name' => $this->strFname,
			);
		Page_Platform_Forum::build($arrForumInput);
		Page_Postaudit_User::build($arrListInput);
		$arrTeamInput = array('forum_id' => $this->intFid,);
		Page_Platform_Team::build($arrTeamInput);
	}
}
?>
