<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-05-25 14:50:58
 * @version
 */
 
class listFilterUserAction extends Actions_Platform_Base {
	
	protected $arrKeyFields = array('word');
	protected $arrOptFields = array('stype', 'svalue', array('pn'=>1), array('tn'=>30), 'ouname', 'begin', 'end');
	protected $arrNeedPerms = array("can_type2_audit_post", "can_type3_audit_post");
	protected $strServiceType = "listFilterUser";
	protected $strTemplate = "user_account_forbidden.php";
	//protected $intCupidID = 265;

	public function process(){
		//build page
		$arrListInput = array(
			'forum_id' => intval($this->intFid),
			'intblocktype' =>1,
			'pageindex' => intval($this->arrReqInfo['pn']),
			'pagesize' => intval($this->arrReqInfo['tn']),
				
		);
		if(isset($this->arrReqInfo['stype']) && !empty($this->arrReqInfo['svalue'])) {
			// if('post_uname' === $this->arrReqInfo['stype']) {
				//搜索框粘贴不了图片emoji，这里用户名或昵称搜索接入大搜模糊搜索
		   		$strSearchVal = trim(strval($this->arrReqInfo['svalue']));
		   		$strSearchVal = Bingo_Encode::convert($strSearchVal,'UTF-8','GBK');
		   		$strUrl = 'http://search.tc.api.sc.baidu.com/v2/tieba_person_bj/search?q='.$strSearchVal.'&qOptions={}&size=50&start=0&businessId=c4c4a28f6883439b830b8349b03db091&apiKey=NWY0MTZiMjY3YjFl';
		        $ch = curl_init();
		        curl_setopt($ch, CURLOPT_URL,$strUrl);
		        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		        curl_setopt($ch, CURLOPT_BINARYTRANSFER, true);
		        $arrOut = curl_exec($ch) ;
		        $arrOut = json_decode($arrOut, true);
		       
		        if (empty($arrOut['results'])) {
		            Bingo_Log::warning("call search failed. uname:".$strSearchVal.",output:".$arrOut);
		        }
		        foreach ($arrOut['results'] as $search) {
		        	if ('post_uname' === $this->arrReqInfo['stype']) {
		        		$arrListInput['user_ids'][] = intval($search['uid']);
		        	}else if('op_uname' === $this->arrReqInfo['stype']) {
		        		$arrListInput['op_uids'][]  = intval($search['uid']);
		        	}
		        	
		        }

				// $arrUnames=array();
				// $arrUnames[] = trim(strval($this->arrReqInfo['svalue']));
				// if(!empty($arrUnames)) {
				// 	$arrUids = Util_User::getUidByNames($arrUnames);
				// 	if(false === $arrUids) {
				// 		Bingo_Log::warning("Can't get uids by unames.Input[".serialize($arrUnames)."]");
				// 	} else {
				// 		foreach($arrUids[$arrUnames[0]] as $key => $arrEach) {
				// 			$arrListInput['user_ids'][] = intval($arrEach['user_id']);
				// 		}
				// 	}
				// }
			// }
			// if('op_uname' === $this->arrReqInfo['stype']) {

				// //搜索框粘贴不了图片emoji，这里用户名或昵称搜索接入大搜模糊搜索
		  //  		$strSearchVal = strval($this->arrReqInfo['svalue']);
		  //  		$strSearchVal = Bingo_Encode::convert($strSearchVal,'UTF-8','GBK');
		  //  		$strUrl = 'http://search.tc.api.sc.baidu.com/v2/tieba_person_bj/search?q='.$strSearchVal.'&qOptions={}&size=50&start=0&businessId=c4c4a28f6883439b830b8349b03db091&apiKey=NWY0MTZiMjY3YjFl';
		  //       $ch = curl_init();
		  //       curl_setopt($ch, CURLOPT_URL,$strUrl);
		  //       curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		  //       curl_setopt($ch, CURLOPT_BINARYTRANSFER, true);
		  //       $arrOut = curl_exec($ch) ;
		  //       $arrOut = json_decode($arrOut, true);
		       
		  //       if (empty($arrOut['results'])) {
		  //           Bingo_Log::warning("call search failed. uname:".$strSearchVal.",output:".$arrOut);
		  //           return true;
		  //       }
		  //       foreach ($arrOut['results'] as $search) {
		  //       	$arrListInput['user_ids'][] = intval($search['uid']);
		  //       }


				// $arrUnames=array();
				// $arrUnames[] = trim(strval($this->arrReqInfo['svalue']));
				// if(!empty($arrUnames)) {
				// 	$arrUids = Util_User::getUidByNames($arrUnames);
				// 	if(false === $arrUids) {
				// 		Bingo_Log::warning("Can't get uids by unames.Input[".serialize($arrUnames)."]");
				// 	} else {
				// 		foreach($arrUids[$arrUnames[0]] as $key => $arrEach) {
				// 			$arrListInput['op_uids'][] = intval($arrEach['user_id']);
				// 		}
				// 	}
				// }
			// }
			$arrListInput['search_type'] = strval($this->arrReqInfo['stype']);
			$arrListInput['search_value'] = trim(strval($this->arrReqInfo['svalue']));
		}
		if(isset($this->arrReqInfo['begin']) && !empty($this->arrReqInfo['begin'])) {
			$arrListInput['timestart'] = intval($this->arrReqInfo['begin']);
		}
		if(isset($this->arrReqInfo['end']) && !empty($this->arrReqInfo['end'])) {
			$arrListInput['timeend'] = intval($this->arrReqInfo['end']);
		}
		Page_Platform_FilterUserList::build($arrListInput);
		$arrForumInput = array(
			'forum_id' => $this->intFid,
			'forum_name' => $this->strFname,
			);
		Page_Platform_Forum::build($arrForumInput);
		Page_Postaudit_User::build();
        $arrTeamInput = array('forum_id' => $this->intFid,);
        Page_Platform_Team::build($arrTeamInput);
	}
}
?>
