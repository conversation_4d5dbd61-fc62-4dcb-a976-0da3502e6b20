<?php
/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/5/21
 * Time: 17:12
 */
class getUserRoleListAction extends Actions_Platform_Base
{
    private  static $_arrUserRoleMap = array(
        'assist' => '小吧主',
        'picadmin' => '图片小编',
        'videoadmin'=>'视频小编',
        'voiceadmin' => '语音小编',
        'pm' =>'PM',
        'publication' => '吧刊小编',
        'publication_editor' => '吧刊主编',
        'broadcast_admin' => '广播小编',
        'manager' => '吧主',
        'profession_manager' => '职业吧主',
        'fourth_manager' =>'第四吧主',
    );
    protected $arrKeyFields = array(
        'word',
    );

    protected $bolNeedPerm = false;

    public function process() {
        $arrRes = array();
        $arrOutPut = array();
        $intForumId = intval($this->intFid);
        $intUserId = intval($this->arrUserInfo['id']);
        $ipArr      = Bingo_Http_Ip::getConnectIpExt();
        $intUserIp      =  isset($ipArr['type']) && $ipArr['type'] == 'IPv4' ? intval(Bingo_Http_Ip::newip2long($ipArr['ip'])) : 0;
        $intUserIp6     =  isset($ipArr['type']) && $ipArr['type'] == 'IPv6' ? $ipArr['ip'] : '';
        $strUserRoleList = Util_Perm::getUserRoleStr($intForumId,$intUserId,$intUserIp,$intUserIp6);
        if(!empty($strUserRoleList)) {
            $arrRes = explode(",",$strUserRoleList);
        }
        if(!empty($arrRes)) {
            //编码转换
            self::$_arrUserRoleMap=Bingo_Encode::convert(self::$_arrUserRoleMap,Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
            foreach($arrRes as $strRoleName) {
                $arrTemp['role_id'] = strval($strRoleName);
                $arrTemp['role_name'] = strval(self::$_arrUserRoleMap[$strRoleName]);
                $arrOutPut[] = $arrTemp;
            }
        }
        $arrResult = $this->_errRet(0, 'success',$arrOutPut);
        echo Bingo_String::array2json($arrResult);
        return true;
    }

    protected function _errRet($errno, $errmsg, $data = array()) {
        return array(
            'no' => $errno,
            'errmsg' => $errmsg,
            'data' => $data,
        );
    }
}
