<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-05-25 14:16:58
 * @version
 */
 
class cancelBlackAction extends Actions_Platform_Base {
	
	protected $arrKeyFields = array('word', 'tbs', 'list');
	protected $arrOptFields = array();
	protected $arrNeedPerms = array("can_type2_audit_post", "can_type3_audit_post");
	protected $strServiceType = "cancelBlack";
	protected $strPmPerm = "can_type3_audit_post";
	private   static $day_check = array(1,3,10);
	const  UNSETBLACK = 3403;
	//protected $intCupidID = 265;

	public function process(){
		if(false === Tieba_Tbs::check($this->arrReqInfo['tbs'], true)) {
			Bingo_Log::warning('Tbs check fail.');
                	Bingo_Page::assign('errno', -1);
                	Bingo_Page::assign('errmsg', 'tbs check fail');
			Bingo_Page::setOnlyDataType("json");
			return false;
		}
	
		$arrUids = $this->arrReqInfo['list'];
		$arrUserDict =array();
		if(!empty($arrUids)) {
			$arrUnames = Util_User::getUnameByUids(array('user_id' =>$arrUids));
			if(false === $arrUnames) {
				Bingo_Log::warning("Can't get unames by uids .Input[".serialize($arrUids)."]");
				Bingo_Page::assign('errno', -1);
				Bingo_Page::assign('errmsg', 'get uid fail');
				Bingo_Page::setOnlyDataType("json");
				return false;
			} else {
				foreach($arrUnames as $arrEach) {
					$arrUserDict[$arrEach['user_id']] = $arrEach['user_name'];
				}
			}
		}
		
		$error = Tieba_Errcode::ERR_SUCCESS;
		foreach($arrUids as $user_id)
		{
			$user_name = $arrUserDict[$user_id];
			if(empty($user_name)) {
				Bingo_Log::warning('Uid not exist.');
				Bingo_Page::assign('errno', Tieba_Errcode::ERR_DIFANG_USER_NOT_EXIST);//260401);
				Bingo_Page::assign('errmsg', 'no user');
				Bingo_Page::setOnlyDataType("json");
				return false;
			}
//			$arrNMQInput = array (
//					'user_id' =>  $user_id,
//					'username' => $user_name,
//					'forum_id' => $this->intFid,
//					'forumname' => $this->strFname,
//					'cookie_username' => $this->arrUserInfo['name'],
//					'op_uid' =>$this->arrUserInfo['id'],
//					'command_no' => self::UNSETBLACK,
//					'now_time' => time (),
//					'need_memo' => $this->intPmFlag,
//			 );
			$ipArr = Bingo_Http_Ip::getConnectIpExt();
			$arrInput = array(
	         'req' => array(
	         	'user_id' => $user_id,
	            'user_name' => $user_name,
	            'forum_id' => $this->intFid,
	            'forum_name' => $this->strFname,//ԭ��������
	            'cookie_username' => $this->arrUserInfo['name'],//ԭ��������
	            'op_uid' => $this->arrUserInfo['id'],
	            'now_time' => time(),
	            'need_memo' => $this->intPmFlag,//ԭ��������
				'op_uname'=> $this->arrUserInfo['name'], 
				'op_ip'=> $ipArr['type'] == 'IPv4' ? Bingo_Http_Ip::newip2long($ipArr['ip']) : 0,
				'op_ip6'=> $ipArr['type'] == 'IPv6' ? $ipArr['ip'] : '',
				'from_module'=>'pc_bawu',//���÷�ģ����	
				'from_function'=>'cancelBlackAction',	//���÷�������
	 			),
	        );
	    	Bingo_Timer::start('service_bawu_cancelUserBlack');
	        $arrOut = Tieba_Service::call('bawu','cancelUserBlack', $arrInput);
	        Bingo_Timer::end('service_bawu_cancelUserBlack');
//				$arrNMQRes = Tieba_Commit::commit ( 'perm', 'gradeUnsetBlack', $arrNMQInput );
//				if ($arrNMQRes === false || ! isset ( $arrNMQRes ['err_no'] ) || $arrNMQRes ['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
//					$error = Tieba_Errcode::ERR_RPC_CALL_FAIL;
//					Bingo_Log::warning ( " Tieba_Commit::commit fail" ."input:[".serialize ( $arrNMQInput )."] out:[" .serialize ( $arrNMQRes ) ."]");
//				}
			if($arrOut === false){
	        	$error = Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL;
	        }
	        elseif ($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
	            Bingo_Log::warning('call service_bawu_cancelUserBlack failed,errno:' . serialize($arrOut) . "input:".serialize($arrInput));
	            $error = $arrOut['errno'];
	        }
		}
		Bingo_Page::assign('errno', $error);
		Bingo_Page::assign('errmsg', Tieba_Error::getErrmsg($error));
		Bingo_Page::setOnlyDataType("json"); 
		
	}
}
?>
