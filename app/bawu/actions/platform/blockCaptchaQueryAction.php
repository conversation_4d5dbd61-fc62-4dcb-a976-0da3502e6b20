<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 14-4-20:PM11:56
 * @version 1.0
 */

class blockCaptchaQueryAction extends Actions_Platform_Base
{
    protected $arrKeyFields = array('word');
    protected $arrOptFields = array('is_need_check'); // captcha option
    protected $strServiceType = "blockCaptchaQuery";
    protected $strTemplate = "";
    protected $isJsonData = 1;
    protected $bolNeedPerm = false;
    protected $bolNeedCupid = false;

    public function process()
    {
        try { // Captcha Judge
            if (isset($this->arrReqInfo['is_need_check']) && 0 === intval($this->arrReqInfo['is_need_check'])) {
                $captchaRes = Util_Anti::getCaptchaIfNeed($this->arrUserInfo['id'],
                    'CaptchaTask', array('captcha_rand' => 1,));
                Util_Tool::renderJsonMsg(0, 'Need to check the captcha.', array('need_vcode' => 1,
                    'captcha_vcode_str' => $captchaRes['res']['captcha_vcode_str'],
                    'captcha_code_type' => $captchaRes['res']['captcha_code_type'],
                    'str_reason' => '��������֤����ɷ���û�����',));
            } else {
                if (true === Util_Anti::judge2FetchCaptcha($this->arrUserInfo['id'],
                        array('cmd' => 'blockUser', 'getCaptcha' => array('captcha_rand' => 1,),
                            'str_reason' => '��������֤����ɷ���û�����',))
                )
                    return true;
                else
                    Util_Anti::actsctrlSubmit($this->arrUserInfo['id'], 'blockUser');
            }
        } catch (Exception $e) {
            Bingo_Log::warning($e->getMessage());
            return false;
        }
        return true;
    }
}

?>