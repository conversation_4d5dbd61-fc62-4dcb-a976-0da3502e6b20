<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-03 10:45:58
 * @version
 */

class dataExcelAction extends Actions_Platform_Base {
	
	protected $arrKeyFields = array('word');
	protected $arrOptFields = array();
	protected $arrNeedPerms = array("can_bws_limit_bawu_log", "can_type2_audit_post", "can_type3_audit_post");
	protected $strServiceType = "dataExcel";	
	protected $intDays = 31;
    protected $bolNeedCheckPriforum = false;
	public function process(){
		
		$arrListInput = array(
			'forum_id' => intval($this->intFid),
			'days' => $this->intDays,
		);
		
		$arrInput = $arrListInput;
		$arrOut = Service_Forumdata_Forumdata::getForumData($arrInput);
		if($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('call service bawu::getForumData failed, errno[' . $arrOut['errno'] . '],errmsg[' .$arrOut['errmsg'] . ']');
			return false;
		}
		
		//build data
		$arrDetail = array();
		$arrFields = array('date','cookie_num','client_cookie_num' , 'thread_num','client_thread_num' ,  'post_num', 'client_post_num', 'sign_num','client_sign_num');
		$intIndex = 0;
		foreach($arrOut['info']['list'] as $arrEach) {
			foreach($arrFields as $strField) {
				$arrDetail[$intIndex][$strField] = $arrEach[$strField];
			}
			if(0 < $arrEach['total_member_num']) {
				$arrDetail[$intIndex]['sign_ratio'] = round($arrEach['sign_num']/$arrEach['total_member_num'], 4);
			} else {
				$arrDetail[$intIndex]['sign_ratio'] = 0;
			}
			$arrDetail[$intIndex]['new_member_num'] = $arrEach['new_member_num'];
			$arrDetail[$intIndex]['total_member_num'] = $arrEach['total_member_num'];
			$intIndex ++;
		}
		self::outToExcel($arrDetail);

	}
	public static function outToExcel($arrData){
	
		$xlsFile = new Util_Excel;
		$arrDataTitle =array();
		$arrDatakey = array_keys($arrData[0]);
		
		foreach($arrDatakey  as $key ){
			if($key == 'cookie_num'){
				$arrDataTitle[$key] = '�����û�';
			}
			if($key == 'thread_num'){
				$arrDataTitle[$key] = '������';
			}
			if($key == 'post_num'){
				$arrDataTitle[$key] = '�ظ���';
			}
			if($key == 'sign_num'){
				$arrDataTitle[$key] = 'ǩ����';
			}
			if($key == 'sign_ratio'){
				$arrDataTitle[$key] = 'ǩ����';
			}
			if($key == 'new_member_num'){
				$arrDataTitle[$key] = '������Ա��';
			}
			if($key == 'total_member_num'){
				$arrDataTitle[$key] = '�ܻ�Ա��';
			}
			if($key == 'date'){
				$arrDataTitle[$key] = 'ʱ��';
			}
			if($key == 'client_cookie_num'){
				$arrDataTitle[$key] = '�ͻ��˷�����';
			}
			if($key == 'client_thread_num'){
				$arrDataTitle[$key] = '�ͻ���������';
			}
			if($key == 'client_post_num'){
				$arrDataTitle[$key] = '�ͻ��˻ظ���';
			}
			if($key == 'client_sign_num'){
				$arrDataTitle[$key] = '�ͻ���ǩ����';
			}
		}
	
		array_unshift($arrData, $arrDataTitle);
		$xlsFile->addArray($arrData);
		$xlsFile->generateXML('bawudata_'.date('Ymd'));
		exit();
	
	}
}
?>
