<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-06-08 22:41:58
 * @version
 */

class getGoodClassifyAction extends Actions_Platform_Base {
	
	protected $arrKeyFields = array('word');
	protected $arrOptFields = array();
	protected $arrNeedPerms = array("can_type2_audit_post", "can_type3_audit_post");
	protected $strServiceType = "getGoodClassify";
	//protected $intCupidID = 265;

	public function process(){
		$arrInput = array(
			'forum_name' => $this->strFname, 
			'forum_id' => $this->intFid, 
			'need_abstract' => 0, 
			'offset' => 0,
			'res_num' => 1, 
			'need_photo_pic' => 0,
			);
		Bingo_Timer::start('service_post_getFrs');
		$arrOut = Tieba_Service::call('post', 'getFrs', $arrInput);
		Bingo_Timer::end('service_post_getFrs');
		if(false === $arrOut) {
			Bingo_Log::warning('call service post::getFrs failed, errno[' . $arrOut['errno'] . '],errmsg[' .$arrOut['errmsg'] . ']');
		} else {
			$arrGoodClassify = $arrOut['output']['forum_info']['goodclass_show'];
		}
		Bingo_Page::assign('errno', $arrOut['errno']);
		Bingo_Page::assign('errmsg', $arrOut['errmsg']);
		Bingo_Page::assign('good_classify', $arrGoodClassify);
		Bingo_Page::setOnlyDataType("json");    
	}
}
?>
