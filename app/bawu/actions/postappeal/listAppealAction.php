<?php

/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2013-01-17 19:25:58
 * @version
 *
 */
class listAppealAction extends Bingo_Action_Abstract {
	public function execute() {
		Tieba_Stlog::addNode ( 'service_type', 'list' );
		$intFid = intval ( Bingo_Http_Request::get ( "forum_id", 0 ) );
		$orderParam = Bingo_Http_Request::get ( "order", 'desc' );
		$orderby = Bingo_Http_Request::get ( "orderby", 'appeal_time' );
		$opName  = Bingo_Http_Request::get ( "op_uname" );
		//$applyTime = intval(Bingo_Http_Request::get ( "appeal_time", -1 ));
		$opStartTime = strval(Bingo_Http_Request::get ( "op_start_time" ));
		$opEndTime = strval(Bingo_Http_Request::get ( "op_end_time" ));
		$userName = Bingo_Http_Request::get ( "appealMan" );
		//$appealUser = Bingo_Http_Request::get ( "appealMan", '' );
        $ipArr = Bingo_Http_Ip::getConnectIpExt();
        $intIp = isset($ipArr['type']) && $ipArr['type'] == 'IPv4' ? Bingo_Http_Ip::newip2long($ipArr['ip']) : 0;
        $intIp6 = isset($ipArr['type']) && $ipArr['type'] == 'IPv6' ? $ipArr['ip'] : '';
		$isDeal = intval(Bingo_Http_Request::get ( "status", 1 ));
		$page      = (int)Bingo_Http_Request::get('page', 1);
		$pageSize  = (int)Bingo_Http_Request::get('page_size', 10);
		
		if($isDeal===1){
			$status = Util_Const::$postDealStatusWaiting;
		}else{ 
			$status = array(Util_Const::$postDealStatusSuccess,
							Util_Const::$postDealStatusFail
			);
		}
		$arrUserInfo = Util_Session::getUserSessionInfo ();
		if (trim ( $orderParam ) == 'asc')
			$order = 'asc';
		else
			$order = 'desc';
		$bolLogin = $arrUserInfo ['bolLogin'];
		$intOpUid = $arrUserInfo ['intUid'];
		$strOpUname = $arrUserInfo ['strUname'];
		if ($bolLogin !== true) {
			Bingo_Log::warning ( 'no login' );
			Bingo_Page::assign ( 'errno', 20 );
			Bingo_Page::assign ( 'errmsg', 'no login' );
			Bingo_Page::setOnlyDataType ( "json" );
			return true;
		}
		
		//��֤��С�������
		$arrInput = array (
				'forum_id' => $intFid, 
				'user_id' => $intOpUid, 
				'user_ip' => $intIp,
				'user_ip6' => $intIp6
		);
		$arrOut = Util_Perm::getUserPerm ( $arrInput );
		if ($arrOut === false) {
			Bingo_Log::warning ( 'talk to perm failed' );
			Bingo_Page::assign ( 'errno', 21 );
			Bingo_Page::assign ( 'errmsg', 'auth failed' );
			Bingo_Page::setOnlyDataType ( "json" );
			return true;
		}
		else {
			$bolIsPm = $arrOut ['can_type3_audit_post'];
			$bolIsMgr = $arrOut ['can_type2_audit_post'];
			$bolIsAssist = $arrOut ['can_type1_audit_post'];
		}
		if ($bolIsPm === false && $bolIsMgr === false) {
			Bingo_Log::warning ( 'auth failed' );
			Bingo_Page::assign ( 'errno', 21 );
			Bingo_Page::assign ( 'errmsg', 'auth failed' );
			Bingo_Page::setOnlyDataType ( "json" );
			return true;
		}
        //��֤�Ƿ�˽�л��ɣ�˽�л��ɲ�֧��PC��
        if ($intFid){
            $isPrivateForum = Util_Forum::isPrivateForum($intFid);
            if ($isPrivateForum === true){
                Bingo_Log::warning($intFid.' is private forum ');
                header("Location:http://".$_SERVER['HTTP_HOST']."/bawu2/errorPage");
                return false;
            }
        }
		$listInput = array (
				'forum_id' => $intFid,
				'op_status' => $status, 
				'order' => $order, 
				'orderby' => $orderby, 
				'offset'     => $page > 1 ? ($page - 1) * $pageSize : 0,
				'count'		 => $pageSize,
		);
		if($opStartTime){
			$listInput['op_start_time'] = strtotime($opStartTime) ;
		}
		if($opEndTime){
			$listInput['op_end_time'] = strtotime($opEndTime);
		}
		$arrUidsByName = array();
		if($opName){
			$arrUnames[] = strval($opName);
    		$arrUids = Util_User::getUidByNames($arrUnames);
    		if(false === $arrUids) {
        		Bingo_Log::warning("Can't get uids by names.Input[".serialize($arrUnames)."]");
        		Bingo_Page::assign ( 'errno', 10 );
        		Bingo_Page::assign ( 'errmsg', 'system error' );
        		Bingo_Page::assign ( 'page', $page );
        		Bingo_Page::setOnlyDataType ( "json" );
        		return false;
    		}
    		foreach($arrUids[$opName] as $key => $arrEach) {
        		if(intval($arrEach['user_id']) > 0 ){
            			$arrUidsByName[] = intval($arrEach['user_id']);
        		}
    		}
    		if( count($arrUidsByName) > 0 ){
        		$listInput['op_user_ids'] = $arrUidsByName;
    		}
		}
	
		if($userName){
			$userName = urldecode(trim($userName));
			$arrUnames[] = strval($userName);
    		$arrUids = Util_User::getUidByNames($arrUnames);
    		if(false === $arrUids) {
        		Bingo_Log::warning("Can't get uids by names.Input[".serialize($arrUnames)."]");
        		Bingo_Page::assign ( 'errno', 10 );
        		Bingo_Page::assign ( 'errmsg', 'system error' );
        		Bingo_Page::assign ( 'page', $page );
        		Bingo_Page::setOnlyDataType ( "json" );
        		return false;
    		}
    		foreach($arrUids[$userName] as $key => $arrEach) {
        		if(intval($arrEach['user_id']) > 0 ){
            			$arrUidsByName[] = intval($arrEach['user_id']);
        		}
   			}
    		if( count($arrUidsByName) > 0 ){
        		$listInput['user_ids'] = $arrUidsByName;
    		}
		}
		$ret = Service_Postappeal_Postappeal::getPostAppealList ( $listInput );
		if (Tieba_Errcode::ERR_SUCCESS !== $ret ['errno']) {
			Bingo_Log::warning ( 'call service failed: Service_Postrecover_Postrecover::getPostAppealList failed:' . serialize ( $ret ) );
			Bingo_Page::assign ( 'errno', 10 );
			Bingo_Page::assign ( 'errmsg', 'system error' );
			Bingo_Page::assign ( 'page', $page );
			Bingo_Page::setOnlyDataType ( "json" );
		}
		/*foreach ( $listOut['ret'] as $row ) {
			$row['appeal_time'] = date ( 'Y-m-d H:i:s', $row ['appeal_time'] );
			$row['punish_start_time'] = date ( 'Y-m-d H:i:s', $row ['punish_start_time'] );
			$row['punish_end_time']   = date ( 'Y-m-d H:i:s', $row ['punish_end_time'] );
			$appealRecordList[] = $row;
		}*/
		//�����ҳ
		/*$pageInfo = array ();
		$pageInfo ['recordCounter'] = $listOut['total'];
		$pageInfo ['currentPage'] = $page;
		$pageInfo ['recordNumberPerPage'] = $pageSize;
		$pageTotal = 1;
		if ($pageInfo ['recordCounter'] % $pageSize == 0) {
			$pageTotal = floor ( $pageInfo ['recordCounter'] / $pageSize );
		}
		else {
			$pageTotal = floor ( $pageInfo ['recordCounter'] / $pageSize ) + 1;
		}
		$pageInfo ['totalPage'] = $pageTotal;*/
		
		Tieba_Stlog::addNode ( 'fid', $intFid );
		Tieba_Stlog::addNode ( 'un', $strOpUname );
		Tieba_Stlog::addNode ( 'uid', $intOpUid );
		
		Bingo_Page::assign ( 'errno', 0 );
		Bingo_Page::assign ( 'errmsg', 'success' );
		$data = self::_formatData($ret['ret']['data']);
		Bingo_Page::assign ( 'data', $data);
		Bingo_Page::assign ( 'appeal_num', $ret['ret']['total']);
		$pageInfo = self::_paging($page, $ret['ret']['total'], $pageSize);
		Bingo_Page::assign ( 'page_info', $pageInfo );
		Bingo_Page::setOnlyDataType ( "json" );
		return true;
	}
	private function _formatData($arrInput){
		$arrUids = array();
    	foreach($arrInput as $key => $arrEach){
    		if( intval($arrEach['user_id']) > 0 ){
        		$arrUids[] = intval($arrEach['user_id']);
    		}
			if( intval($arrEach['del_uid']) > 0 && !in_array($arrEach['del_uid'], $arrUids)){
                $arrUids[] = intval($arrEach['del_uid']);
            }
            if( intval($arrEach['op_uid']) > 0 && !in_array($arrEach['op_uid'], $arrUids)){
                $arrUids[] = intval($arrEach['op_uid']);
            }
    	}
    	if (empty($arrUids)) {
    		return $arrInput;
    	}
    	$arrRes = Tieba_Service::call('user', 'mgetUserData', array('user_id'=>$arrUids,), null, null, 'post', 'php', 'utf-8');
    	if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== intval($arrRes['errno'])) {
        		Bingo_Log::warning("call user info fail input[" . serialize($arrUids) . "] out[" . serialize($arrRes) . "]");
        		Bingo_Page::assign ( 'errno', 10 );
        		Bingo_Page::assign ( 'errmsg', 'system error' );
        		Bingo_Page::assign ( 'page', $page );
        		Bingo_Page::setOnlyDataType ( "json" );
        		return false;
    	}
    	$arrUserExtInfo = $arrRes['user_info'];
		foreach ($arrInput as &$v){
			$v['appeal_nickname'] =  strval($arrUserExtInfo[$v['user_id']]['user_nickname']);
			//emojiתͼƬ���û����⣬����Ҫ�����Ķ�����ת��
			$v['appeal_nickname'] = Molib_Util_Nickname::emojiConverter($v['appeal_nickname']);
			$v['appeal_nickname'] = Bingo_Encode::convert($v['appeal_nickname'],'GBK', 'UTF-8');
			$v['show_nickname']   = Molib_Util_User::getShowNickname($arrUserExtInfo[$v['user_id']]);
            $v['show_nickname']   = Molib_Util_Nickname::emojiConverter($v['show_nickname']);
            $v['show_nickname']   = Bingo_Encode::convert($v['show_nickname'],'GBK', 'UTF-8');
			$v['show_del_nickname'] = Molib_Util_User::getShowNickname($arrUserExtInfo[$v['del_uid']]);
            $v['show_del_nickname'] = Molib_Util_Nickname::emojiConverter($v['show_del_nickname']);
            $v['show_del_nickname'] = Bingo_Encode::convert($v['show_del_nickname'],'GBK', 'UTF-8');
			$v['appealMan'] = $v['user_name'];
			$v['del_time'] =  date ( 'Y-m-d H:i:s', $v['del_time'] );
			$v['op_time'] =  date ( 'Y-m-d H:i:s', $v['op_time'] );
			$v['appeal_time'] =  date ( 'Y-m-d H:i:s', $v['appeal_time'] );
			if( intval($arrEach['op_uid']) > 0 ){
                $v['show_op_nickname'] = Molib_Util_User::getShowNickname($arrUserExtInfo[$v['op_uid']]);
                $v['show_op_nickname'] = Molib_Util_Nickname::emojiConverter($v['show_op_nickname']);
                $v['show_op_nickname'] = Bingo_Encode::convert($v['show_op_nickname'],'GBK', 'UTF-8');
            }
		}
		return $arrInput;
	}
	private function _paging($page, $total, $pageSize = 10){
		$pageSize = intval($pageSize);
		if($pageSize <=0 ){
			$pageSize = 10;
		}
		$total = intval($total);
		$totalPage = ceil($total / $pageSize);
		return array(
				'current'   => $page,
				'page_num'  => $totalPage,
				'page_size' => $pageSize,
				'total'     => $total,
		);
	}
}

?>
