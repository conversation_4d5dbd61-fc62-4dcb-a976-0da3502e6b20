<?php

/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2013-01-17 19:25:58
 * @version
 *
 */
// demo ģ��
class indexAction extends Bingo_Action_Abstract {
	/*
	 * public function init(){ // ��ʼ������ }
	 */
	public function execute() {
		Tieba_Stlog::addNode ( 'service_type', 'appeal' );
		$strFname =  strval( Bingo_Http_Request::get ( "kw", '' ));
		$strFname = Bingo_String::xssDecode($strFname);
		//$strFname = Bingo_Encode::convert($strFname, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
		$pageType = strval ( Bingo_Http_Request::get ( "type", 'grid' ) );
		$appealId = Bingo_Http_Request::get ( 'appeal_id', 0 );
        $ipArr = Bingo_Http_Ip::getConnectIpExt();
        $intIp = isset($ipArr['type']) && $ipArr['type'] == 'IPv4' ? intval(Bingo_Http_Ip::newip2long($ipArr['ip'])) : 0;
        $intIp6 = isset($ipArr['type']) && $ipArr['type'] == 'IPv6' ? $ipArr['ip'] : '';
		$intOut = Util_Forum::getFidByFname ( $strFname );
		$intFid = $intOut;
		if ($intOut === false) {
			$strJumpUrl = "http://tieba.baidu.com";
			header ( "Location:" . $strJumpUrl );
			return false;
		}
		$intFid = $intOut;
		if ($pageType == 'grid') {
			$pageType = 'grid';
		}
		else {
			$pageType = 'detail';
		}
		$arrUserInfo = Util_Session::getUserSessionInfo ();
		$bolLogin = $arrUserInfo ['bolLogin'];
		$intOpUid = $arrUserInfo ['intUid'];
		$strOpUname = $arrUserInfo ['strUname'];
		if ($bolLogin !== true) {
			Bingo_Log::warning ( "login fail!" );
			$strJumpUrl = "http://tieba.baidu.com";
			header ( "Location:" . $strJumpUrl );
			return false;
		}
		$tbs = Tieba_Tbs::gene ( true );
		Bingo_Page::assign ( 'tbs', $tbs );
		//��֤��С�������
		$arrInput = array (
				'forum_id' => $intFid, 
				'user_id' => $intOpUid, 
				'user_ip' => $intIp,
				'user_ip6' => $intIp6
			);
		$arrOut = Util_Perm::getUserPerm ( $arrInput );
		if ($arrOut === false) {
			$strJumpUrl = "http://tieba.baidu.com";
			header ( "Location:" . $strJumpUrl );
			return false;
		}
		else {
			$bolIsPm = $arrOut ['can_type3_audit_post'];
			$bolIsMgr = $arrOut ['can_type2_audit_post'];
			$bolIsAssist = $arrOut ['can_type1_audit_post'];
		}
		if ($bolIsPm === false && $bolIsMgr === false) {
			Bingo_Log::warning ( 'no perm!' );
			$strJumpUrl = "http://tieba.baidu.com";
			header ( "Location:" . $strJumpUrl );
			return false;
		}
		/*$countInput = array('forum_id'=> $intFid,
						  'op_status'=> Util_Const::$postDealStatusWaiting,
		);
		$num = Service_Postappeal_Postappeal::getPostAppealCount($countInput);
		Bingo_Page::assign ( 'post_appeal_num', $num['ret']['total'] );*/

        //��֤�Ƿ�˽�л��ɣ�˽�л��ɲ�֧��PC��
        if ($intFid || $strFname){
            $isPrivateForum = Util_Forum::isPrivateForum($intFid,$strFname);
            if ($isPrivateForum === true){
                Bingo_Log::warning($intFid.' is private forum '.$strFname);
                header("Location:http://".$_SERVER['HTTP_HOST']."/bawu2/errorPage");
                return false;
            }
        }

		Page_Postaudit_User::build ();
		$arrForumInput = array (
				'forum_id' => $intFid, 
				'forum_name' => $strFname );
		Page_Platform_Forum::build ( $arrForumInput );
		Tieba_Stlog::addNode ( 'fname', $strFname );
		Tieba_Stlog::addNode ( 'fid', $intFid );
		Tieba_Stlog::addNode ( 'un', $strOpUname );
		Tieba_Stlog::addNode ( 'uid', $intOpUid );
		// ģ�����
		Bingo_Page::assign ( 'is_login', true );
		Bingo_Page::assign ( 'appeal_id', $appealId );
		Bingo_Page::assign ( 'type', $pageType );
		Bingo_Page::setTpl ( "post_appeal.php" );
	}
}

?>
