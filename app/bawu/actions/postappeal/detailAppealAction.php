<?php

/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2013-01-17 19:25:58
 * @version
 *
 */
class detailAppealAction extends Bingo_Action_Abstract {
	public function execute() {
		Tieba_Stlog::addNode ( 'service_type', 'detail' );
		$intFid = intval ( Bingo_Http_Request::get ( "forum_id", 0 ) );
		$appealId = intval(Bingo_Http_Request::get ( 'appeal_id', 0 ));
        $ipArr = Bingo_Http_Ip::getConnectIpExt();
        $intIp = isset($ipArr['type']) && $ipArr['type'] == 'IPv4' ? intval(Bingo_Http_Ip::newip2long($ipArr['ip'])) : 0;
        $intIp6 = isset($ipArr['type']) && $ipArr['type'] == 'IPv6' ? $ipArr['ip'] : '';
		$arrUserInfo = Util_Session::getUserSessionInfo ();
		$bolLogin = $arrUserInfo ['bolLogin'];
		$intOpUid = $arrUserInfo ['intUid'];
		$strOpUname = $arrUserInfo ['strUname'];
		if ($bolLogin !== true) {
			Bingo_Log::warning ( 'no login' );
			//������(TODO)
			Bingo_Page::assign ( 'errno', 20 );
			Bingo_Page::assign ( 'errmsg', 'no login' );
			Bingo_Page::setOnlyDataType ( "json" );
			return true;
		}
		
		//��֤��С�������
		$arrInput = array (
				'forum_id' => $intFid, 
				'user_id' => $intOpUid, 
				'user_ip' => $intIp,
                'user_ip6' => $intIp6
			);
		$arrOut = Util_Perm::getUserPerm ( $arrInput );
		if ($arrOut === false) {
			Bingo_Log::warning ( 'talk to perm failed' );
			Bingo_Page::assign ( 'errno', 21 );
			Bingo_Page::assign ( 'errmsg', 'auth failed' );
			Bingo_Page::setOnlyDataType ( "json" );
			return true;
		}
		else {
			$bolIsPm = $arrOut ['can_type3_audit_post'];
			$bolIsMgr = $arrOut ['can_type2_audit_post'];
			$bolIsAssist = $arrOut ['can_type1_audit_post'];
		}
		if ($bolIsPm === false && $bolIsMgr === false) {
			Bingo_Log::warning ( 'auth failed' );
			Bingo_Page::assign ( 'errno', 21 );
			Bingo_Page::assign ( 'errmsg', 'auth failed' );
			Bingo_Page::setOnlyDataType ( "json" );
			return true;
		}
        //��֤�Ƿ�˽�л��ɣ�˽�л��ɲ�֧��PC��
        if ($intFid){
            $isPrivateForum = Util_Forum::isPrivateForum($intFid);
            if ($isPrivateForum === true){
                Bingo_Log::warning($intFid.' is private forum ');
                header("Location:http://".$_SERVER['HTTP_HOST']."/bawu2/errorPage");
                return false;
            }
        }
		$listInput = array ('appeal_id' => $appealId );
		$ret = Service_Postappeal_Postappeal::getPostAppealDetail ( $listInput );
		$ret['ret']['appeal_reason'] = Bingo_Encode::convert($ret['ret']['appeal_reason'],"GBK","UTF-8");//utf-8ת��gbk
		if (Tieba_Errcode::ERR_SUCCESS !== $ret ['errno']) {
			Bingo_Log::warning ( 'call service failed: Service_Postrecover_Postrecover::getBawuAppealDetail failed:' . serialize ( $ret ) );
			Bingo_Page::assign ( 'errno', 10 );
			Bingo_Page::assign ( 'errmsg', 'system error' );
			Bingo_Page::setOnlyDataType ( "json" );
		}
		/*if(!empty($appealUserDetailInfo)){
			$appealUserDetailInfo ['punish_start_time'] = date ( 'Y-m-d H:i:s', $appealUserDetailInfo['punish_start_time'] );
			$appealUserDetailInfo ['punish_end_time'] = date ( 'Y-m-d H:i:s', $appealUserDetailInfo['punish_end_time'] );
			$appealUserDetailInfo ['appeal_time'] = date ( 'Y-m-d H:i:s', $appealUserDetailInfo['appeal_time'] );
		}*/
		Bingo_Page::assign ( 'errno', 0 );
		Bingo_Page::assign ( 'errmsg', 'success' );
		$data = self::_formatData($ret['ret']);
		Bingo_Page::assign ( 'data', $data );
		Bingo_Page::setOnlyDataType ( "json" );
		
		Tieba_Stlog::addNode ( 'fid', $intFid );
		Tieba_Stlog::addNode ( 'un', $strOpUname );
		Tieba_Stlog::addNode ( 'uid', $intOpUid );
		
		return true;
	}
	private function _formatData($data){
		$data['del_time'] = date ( 'Y-m-d H:i:s', $data['del_time'] );
		$data['op_time'] = date ( 'Y-m-d H:i:s', $data['op_time'] );
		$data['appeal_time'] = date ( 'Y-m-d H:i:s', $data['appeal_time'] );
		$data['now_time'] = date ( 'Y-m-d H:i:s', $data['now_time'] );
		$data['appealMan'] = $data['user_name']; 
		return $data;
	}
	
	
}

?>
