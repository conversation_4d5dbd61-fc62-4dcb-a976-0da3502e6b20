<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-05-24 15:26:58
 * @version
 */
 
class Actions_Star_BaseAsyn extends Bingo_Action_Abstract {
    const LEVEL1_NAME                = '��������';
    const WORDLIST_SCHEDULE_CALENDAR = 'tb_wordlist_redis_ScheduleCalendar';
    const OP_BAWU                    = 0;
    const SCHEDULE_STATUS            = 1;
    const TOP_TIME                   = 604800;   //�ö�ʱ���Ϊ7��
    
    const UI_ERR_PARAM_ERROR                          = 1;
    const UI_ERR_TBS_ERROR                            = 2;
    const UI_ERR_USER_NO_PERMISSION                   = 3;
    const UI_ERR_FORUM_NO_PERMISSION                  = 4;
    const UI_ERR_TOP_SCHEDULE_EXSIT                   = 5;
    const UI_ERR_INVALID_SCHEDULE                     = 6;
    const UI_ERR_MODIFY_NO_PERMISSION                 = 7;
    const UI_ERR_WRONG_URL                            = 8;
    const UI_ERR_INVALID_PID                          = 9;
    const UI_ERR_USER_NO_CHANCE                       = 10;
    const UI_ERR_ILLEGAL_INPUT                        = 11;
    const UI_ERR_TOPENDTIME_EARLIER_THAN_TOPSTARTTIME = 12;
    const UI_ERR_TOPENDTIME_EARLIER_THAN_CURRENTTIME  = 13;
    const UI_ERR_TOPTIME_EXCEEDS_SEVEEN_DAY           = 14;

    protected $strFname;                        //����
    protected $intFid;                          //��id
    protected $arrUserInfo          = array();  //��¼�û�����Ϣ
    protected $arrForumInfo         = array();  //�ɵ���ϸ��Ϣ
    protected $intIp;                           //��¼�û���ip
    protected $intIpv6;                           //��¼�û���ipv6
    protected $arrPerm              = array();  //��¼�û���Ȩ��
    protected $arrReqInfo           = array();  //���յĲ���
    protected $intPmFlag;                       //�Ƿ���pm,0���ǣ�1����һ������memo
    protected $arrKeyFields         = array();  //�ش��Ĳ����б�
    protected $arrOptFields         = array();  //ѡ���Ĳ����б�
    protected $arrFieldsCheck       = array();  //���봫��Ĳ���
    protected $arrNeedPerms         = array();  //Ȩ���б�����ЩȨ�޲��ܷ��ʸ�ҳ��
    protected $strServiceType;                  //һ����ļ���һ�£�����־��
    protected $strTemplate;                     //fe��ģ������
    protected $strPermFlag;                     //�û��ܽ����ҳ��Ȩ������
    protected $intCupidID;                      //�����id,��û����ʱ��check
    protected $arrStrategy;                     //�������Ϣ
    protected $bolNeedFields        = true;     //�Ƿ���Ҫ��ò�����Ϣ
    protected $bolNeedUserInfo      = true;     //�Ƿ���Ҫ����û���Ϣ
    protected $bolNeedCupid         = true;     //�Ƿ���Ҫ����������Ϣ
    protected $bolNeedPerm          = true;     //�Ƿ���Ҫ��ò����Ȩ����Ϣ
    protected $bolNeedForumPerm     = true;     //�жϵ�ǰ�ɵ�Ȩ��
    protected $isJsonData           = 0;        //�ӿ��Ƿ�Ϊ�첽�ӿڣ������$isJsonData=1(ֱ�ӷ��ش����룬������302��ת)
    protected $_arrPersonas;
    
    /**
     * @desc ��ִ�г���
     * @param
     * @return 
     */
    public function execute(){
        //check and get url
        if(true === $this->bolNeedFields )
        {
            $this->arrReqInfo = $this->_getReqParam();
            if(false === $this->arrReqInfo) {
                Bingo_Log::warning('Check params fail.');
                $arrRet = $this->_buildReturn(self::UI_ERR_PARAM_ERROR, 'param error!');
                $this->_jumpPage($arrRet);
                return false;
            }
    
            if(!empty($this->arrReqInfo['word'])) {
                $this->strFname = $this->arrReqInfo['word'];
                $strFname = Bingo_Encode::convert($this->strFname, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
                $this->arrForumInfo = Util_Forum::getForumInfo($strFname);
                $this->arrForumInfo = Bingo_Encode::convert($this->arrForumInfo, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
//                var_dump($this->arrForumInfo);
//                var_dump($this->strFname);
                if(false === $this->arrForumInfo) {
                    Bingo_Log::warning('call get forum info.'. serialize($this->strFname). serialize($this->arrForumInfo));
                    $arrRet = $this->_buildReturn(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail!');
                    $this->_jumpPage($arrRet);
                    return false;
                }
                $this->intFid = $this->arrForumInfo['forum_id']['forum_id'];
//                var_dump($this->intFid);
            } else {
                Bingo_Log::warning('Check params fail.');
                $arrRet = $this->_buildReturn(self::UI_ERR_PARAM_ERROR, 'param error!');
                $this->_jumpPage($arrRet);
                return false;
            }
        }
        if(true === $this->bolNeedUserInfo )
        {
            //get user info
            $this->arrUserInfo = Util_User::getUserInfo();
            $this->arrUserInfo = Bingo_Encode::convert($this->arrUserInfo, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
            if(false === $this->arrUserInfo) {
                Bingo_Log::warning("Can't get user info.");
                $arrRet = $this->_buildReturn(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail!');
                $this->_jumpPage($arrRet);
                return false;
            }
        }
        
        if(true === $this->bolNeedPerm )
        {
            //get perm
            $ipArr = Bingo_Http_Ip::getConnectIpExt();
            $intIp = isset($ipArr['type']) && $ipArr['type'] == 'IPv4' ? intval(Bingo_Http_Ip::newip2long($ipArr['ip'])) : 0;
            $intIp6 = isset($ipArr['type']) && $ipArr['type'] == 'IPv6' ? $ipArr['ip'] : '';
            $arrInput = array(
                "forum_id" => $this->intFid,
                "user_id" => $this->arrUserInfo['id'],
                "user_ip" => $intIp,
                "user_ip6" => $intIp6
            );
            if ($arrInput['user_id'] <=0) {
                Bingo_Log::warning("User id is invalid ".serialize($arrInput)."]");
                $arrRet = $this->_buildReturn(self::UI_ERR_PARAM_ERROR, 'param error!');
                $this->_jumpPage($arrRet);
                return false;
            }
            $this->arrPerm = Util_Perm::getUserPerm($arrInput);
            if(false === $this->arrPerm) {
                Bingo_Log::warning("Can't get user perm.Input[".serialize($arrInput)."]");
                $arrRet = $this->_buildReturn(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, 'call service fail!');
                $this->_jumpPage($arrRet);
                return false;
            }
            //check perm
            $bolPass = $this->_checkPerm($this->arrPerm);
            if(false === $bolPass) {
                Bingo_Log::warning("Check user perm fail.");
                $arrRet = $this->_buildReturn(self::UI_ERR_USER_NO_PERMISSION, 'user have no permission!');
                $this->_jumpPage($arrRet);
                return false;
            }
        }

        if(true === $this->bolNeedForumPerm) {
            $bolPass = $this->_checkForumContent($this->arrForumInfo['dir']);
            if (false === $bolPass) {
                Bingo_Log::warning('check forum dir fail. Input['.serialize($this->arrForumInfo['dir']). ']');
                $arrRet = $this->_buildReturn(self::UI_ERR_FORUM_NO_PERMISSION, 'forum have no permission!');
                $this->_jumpPage($arrRet);
                return false;
            } 

            $bolPass = $this->_checkForumInList($this->arrForumInfo['forum_id']['forum_id']);
            if (false === $bolPass) {
                Bingo_Log::warning('check forum in wordlist fail. forum_id: '.serialize($this->arrForumInfo['forum_id']['forum_id']));
                $arrRet = $this->_buildReturn(self::UI_ERR_FORUM_NO_PERMISSION, 'forum have no permission!');
                $this->_jumpPage($arrRet);
                return false;
            }
        }

        //process main logic
        $arrRet = $this->process();
        $this->_jumpPage($arrRet);

        //build log
        $this->_buildLog();
    }

    /**
     * @desc ��ȡ������Ϣ
     * @param 
     * @return 
     */
    private function _getReqParam() {
        $arrRet = array();
        foreach($this->arrKeyFields as $each) {
            $val = Bingo_Http_Request::get($each);
            if(is_null($val)) {
                Bingo_Log::warning('Empty Key Field:'.$each);
                return false;
            }
	        if ($each == 'word') {//xss decode,add by fengzhen,2014-12-30
	            $val = Bingo_String::xssDecode($val);
	        }
            $arrRet[$each] = $val;
        }
        foreach($this->arrOptFields as $each) {
            if(is_array($each)) {
                $val = Bingo_Http_Request::get(key($each));
                if(!is_null($val)) {
                     $arrRet[key($each)] = $val;
                } else {
                    $arrRet[key($each)] = $each[key($each)];
                }
                continue;
            }
            $val = Bingo_Http_Request::get($each);
            if(!is_null($val)) {
                $arrRet[$each] = $val;
            }
        }
        foreach($this->arrFieldsCheck as $key=>$value) {
            if(array_key_exists($key, $arrRet)) {
                if(is_array($value)) {
                    if(!in_array($arrRet[$key], $value)) {
                        Bingo_Log::warning('Field Check Fail:'.$key."=>".$value);
                        return false;
                    }
                }
            }
        }
        return $arrRet;
    }

    /**
     * @desc �������
     * @param 
     * @return
     */
    protected function _jumpPage($arrRet) {
//       var_dump($arrRet['data']); 
        $arrRet = Bingo_Encode::convert($arrRet, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
        Bingo_Page::assign('errno', $arrRet['errno']);
        Bingo_Page::assign('errmsg', $arrRet['errmsg']);
        Bingo_Page::assign('data', $arrRet['data']);
        Bingo_Page::getView()->setOnlyDataType(Tieba_Api::getApiType());
        Bingo_Page::setOnlyDataType("json");
//        Tbapi_Platform_Star_Dict::set('errno',0);                
//        Tbapi_Platform_Star_Dict::set('errmsg','success');
//        Tbapi_Platform_Star_Dict::set('data', $arrRet['data']);
//        Tbapi_Platform_Star_Dict::assignJson();
        return true;
    }

    /**
     * @desc �����û�Ȩ����֤
     * @param �û����е�Ȩ��
     * @return ture/false
     */
    private function _checkPerm($arrPerm) {
        $bolRet = false;
        $this->intPmFlag = 1;
        foreach($this->arrNeedPerms as $each) {
            if(isset($arrPerm[$each]) && true === $arrPerm[$each]) {
                $bolRet = true;
                $this->strPermFlag = $each;
                if(isset($this->strPmPerm) && true === $arrPerm[$this->strPmPerm]) {
                    $this->intPmFlag = 0;
                    break;
                }
            }
        }
        return $bolRet;
    }

    /**
     * @desc ����־
     * @param
     * @return 
     */
    private function _buildLog() {
        Tieba_Stlog::addNode('service_type', $this->strServiceType);
        Tieba_Stlog::addNode('forum_id', $this->intFid);
        Tieba_Stlog::addNode('forum_name', $this->strFname);
        Tieba_Stlog::addNode('op_id', $this->arrUserInfo['id']);
        Tieba_Stlog::addNode('op_name', $this->arrUserInfo['name']);
        foreach($this->arrReqInfo as $key=>$value) {
            Tieba_Stlog::addNode(strval($key), $value);
        }
    }

    /**
     ** @desc��֤��Ȩ�ޱ���������Ŀ¼�µİ�
     ** @param $arrForumDir ��ǰ����Ϣ
     ** @return true/false
     **/ 
    private static function _checkForumContent($arrForumDir) {
        $bolRet = false;
        if (empty($arrForumDir)) {
            Bingo_Log::warning('forum dir empty'. serialize($arrForumDir));
            return $bolRet;
                                        
        }
                 
        $strDesContent = Bingo_Encode::convert(self::LEVEL1_NAME, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        $strSourceContent = $arrForumDir['level_1_name'];
        if (strcmp($strDesContent, $strSourceContent) === 0) {
            $bolRet = true;
                                        
        }
        return $bolRet;
    }

    /**
     ** @desc ��֤��ǰ���ڴʱ���,�Ҷ�ӦֵΪ1
     ** @param $intForumId ��ǰ��id
     ** @return true/false
     **/
    private static function _checkForumInList($intForumId) {
        $bolRet = false;
        $handleWordServer = Wordserver_Wordlist::factory();
        if (null === $handleWordServer) {
            Bingo_Log::warning('call wordserver_wordlist failed. ');
            return $bolRet;
        }                 
                                          
        $strTableName = self::WORDLIST_SCHEDULE_CALENDAR;
        $strForumId   = strval($intForumId);    //�ʱ��д洢�Ķ����ַ���,�����Ҫ��������ת�����ַ���
        $strOpened    = 'is_open';
        $arrKeys      = array(
            $strForumId,
            $strOpened,
        );                
        $arrItemInfo  = $handleWordServer->getValueByKeys($arrKeys, $strTableName);
        $intIsOpen = intval($arrItemInfo[$strOpened]);
        if (2 === $intIsOpen) { //��ʾ��ʹ�ôʱ�����,ֱ�ӷ���true;
            return true;
        }                 
        $bolRet = intval($arrItemInfo[$strForumId]) === 1 ? true : false;
        return $bolRet;                  
    }           
    
    /**                        
     * @desc ���췵�ؽ��       
     * @param [in] errno  : uint32_t : �����
     * @param [in] errmsg : string   : ������
     * @param [in] data   : mix      : ���ص�����
     * @return                  
     */                         
    protected function _buildReturn($errno, $errmsg, array $data = array()) {                                         
        $arrRet = array(       
            'errno'     => $errno,
            'errmsg'    => $errmsg,
        );                     
        if (!is_null($data) || !empty($data)) {
            $arrRet['data'] = $data;
        }                      
        return $arrRet;        
    }

    /**
     * @desc ��ȡUEG���ȿ��Ʋ��Բ���
     * @param $intFid: ��ID
     * @param $intUid: �û�id
     * @return $arrReq
     */
    protected function _getUegParam($intFid, $intUid) {
        $arrReq = array(
            'req' => array(
                'rulegroup' => array('app'),
                'app'       => 'star',
                'cmd'       => 'schedule',
                'forum_id'  => intval($intFid),
                'uid'       => intval($intUid),
            ),
        );

        return $arrReq;
    } 

    /**
     * @desc �����û����ȿ��ƴ�����ѯ
     * @param $intFid ��ID
     * @param $intUid �û�ID
     * @return true/false
     */
    protected function _callAntisctrlQuery($intFid, $intUid) {
        $arrReq = $this->_getUegParam($intFid, $intUid);
        $arrRet = Tieba_Service::call('anti','antiActsctrlQuery',$arrReq, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] && Tieba_Errcode::ERR_ANTI_ACTS_REFUSE !== $arrRet['errno']) {
            $strMsg = sprintf('call anti::antiActsctrlQuery fail with input[%s] and output[%s]', serialize($arrReq), serialize($arrRet));
            Bingo_Log::warning($strMsg);
            throw new Exception('call service fail.', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            
            return false;
        }

        if (Tieba_Errcode::ERR_ANTI_ACTS_REFUSE === $arrRet['errno']) { //�û�û���ճ����ӻ���
            $strMsg = sprintf('user have no chance');
            Bingo_Log::warning($strMsg);
            throw new Exception('user have no chance', self::UI_ERR_USER_NO_CHANCE);
            
            return false;
        }

        return true;
    }

    /**
     * @desc �����û����ȿ��ƴ�����ѯ
     * @param $intFid ��ID
     * @param $intUid �û�ID
     * @return true/false
     */
    protected function _callAntisctrlSubmit($intFid, $intUid) {
        $arrReq = $this->_getUegParam($intFid, $intUid);
        $arrRet = Tieba_Service::call('anti','antiActsctrlSubmit',$arrReq, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno'] && Tieba_Errcode::ERR_ANTI_ACTS_REFUSE !== $arrRet['errno']) {
            $strMsg = sprintf('call anti::antiActsctrlSubmit fail with input[%s] and output[%s]', serialize($arrReq), serialize($arrRet));
            Bingo_Log::warning($strMsg);
            throw new Exception('call service fail.', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            
            return false;
        }

        return true;
    }

    /**
     * @desc UEG���ƿ���
     * @param $strTitle
     * @param $strContent
     * @return true/false
     */
    protected function _callAntisctrlConfilter($strTitle, $strContent) {
        $arrRequest = array(
            'req' => array(
                '(raw)rawdata' => $strContent . "\0",
                'title' => $strTitle,
                'detail_len' => strlen($strContent) + 1,
                'confilter_type' => 'forum_confilter',
            ),  
        );

        $arrRequest = Bingo_Encode::convert($arrRequest, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
        $ret = Tieba_Service::call('anti', 'antiConfilter', $arrRequest);

        if ($ret === false) {
            $strMsg = sprintf('call anti:antiConfilter fail with input[%s] and output[%s]', serialize($arrRequest), serialize($ret));
            Bingo_Log::warning($strMsg);
            throw new Exception('call service fail', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);

            return false;
        } 
          
        if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            $strMsg = sprintf('commit anti-yellow strategy with input[%s] and output[%s]', serialize($arrRequest), serialize($ret));
            Bingo_Log::warning($strMsg);
            throw new Exception('illegal input', self::UI_ERR_ILLEGAL_INPUT);

            return false;
        } 

        return true;
    }

    /**
     * @desc ����URLУ��
     * @param $strUrl : string : url��ַ
     * @return true/false;
     */
    protected function _checkUrl($strUrl) {
        //��У��url��ͷΪhttp://tieba.baidu.com/p/
        if (0 === strlen($strUrl)) {  //ֵΪ0����Ϊ�û�URLδ��,������У��
            return true;
        }
        $ret = strpos($strUrl, 'http://tieba.baidu.com/p/');
        if (false === $ret) {
            $strMsg = sprintf('wrong input url: %s', $strUrl); 
            Bingo_Log::warning($strMsg);
            throw new Exception('wrong input url.', self::UI_ERR_WRONG_URL);

            return false;
        } 

        //����,У��PIDΪ����
        $pId = substr($strUrl, strlen('http://tieba.baidu.com/p/'));
        if(is_numeric($pId) !== true && is_int(intval($pId)) !== true) {
            $strMsg = sprintf('wrong input url: %s', $strUrl); 
            Bingo_Log::warning($strMsg);
            throw new Exception('wrong input url.', self::UI_ERR_WRONG_URL);

            return false;
        } 

        //���,У��������Ч,δ��ɾ��
        $input = array( 
            "input" => array(
                "thread_ids" => array( //����id
                    0 => $pId, //����id
                ),  
                "post_ids" => array( //�ظ�id
                    0 => $pId, //�ظ�id
                ),  
                "comment_infos" => array( //¥��¥�ظ�id
                    0 => array(
                        "thread_id" => $pId, //����id
                        "post_id" => $pId, //�ظ�id
                        "comment_id" => $pId, //¥��¥�ظ�id
                    ), 
                ),
            ),
        );
        $res  = Tieba_Service::call('post', 'getMaskInfo', $input, null, null, 'post', 'php', 'utf-8');
        if (1 === intval($res['output']['threads_mask_status'][$pId]['is_key_deleted'])) {
            $strMsg = sprintf('deleted pid in input url: %s', $strUrl); 
            Bingo_Log::warning($strMsg);
            throw new Exception('invalid pid.', self::UI_ERR_INVALID_PID);

            return false;
        }

        return true;
    }

    /**
     * @desc tbsУ��
     * @param null
     * @return true/false
     */
    protected function _checkTbs() {
        if(!Tieba_Tbs::check($this->arrReqInfo['tbs'], true)) {
            Bingo_Log::warning('tbs check fail!');
            throw new Exception('tbs check fail.', self::UI_ERR_TBS_ERROR);
            return false;
        }

        return true;
    }

    /**
     * @desc �����ö���֤
     * @param null
     * @return true/false
     */
    protected function _checkTopSchedule() {
        $arrInput = array(
            'forumId' => $this->intFid,
            'status'  => self::SCHEDULE_STATUS,
        );
        $arrRet = Tieba_Service::call('pluto', 'getTopScheduleListByForumId', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning('call pluto::getTopScheduleListByForumId error!'. serialize($arrInput). serialize($arrRet));
            throw new Exception('call service fail!', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return false;
        }
        if (!empty($arrRet['data'])) {
             Bingo_Log::warning('top schedule is already exsits.');
             throw new Exception('top schedule is already exsits.', self::UI_ERR_TOP_SCHEDULE_EXSIT);
             return false;
        }
        return true;
    }

    /**
     * @desc �����ö�ʱ����֤
     * @param $intTopStartTime
     * @param $intTopEndTime
     * @return true/false
     */
    protected function _checkTopTime($intTopStartTime, $intTopEndTime) {
        $intTopStartTime = intval($intTopStartTime);
        $intTopEndTime = intval($intTopEndTime);

        //��֤�ö�����ʱ�������ö���ʼʱ��
        if ($intTopEndTime <= $intTopStartTime) {
            Bingo_Log::warning('set top end time earlier than top start time!');
            throw new Exception('top end time earlier than top start time', self::UI_ERR_TOPENDTIME_EARLIER_THAN_TOPSTARTTIME);
            
            return false;
        }

        //��֤�ö�����ʱ�����ڵ�ǰʱ��
        if ($intTopEndTime <= time()) {
            Bingo_Log::warning('set top end time earlier than current time!');
            throw new Exception('top end time earlier than curretn time', self::UI_ERR_TOPENDTIME_EARLIER_THAN_CURRENTTIME);

            return false;
        }

        //��֤�ö�ʱ�䲻����7��
        if (($intTopEndTime - $intTopStartTime) >= self::TOP_TIME) {
            Bingo_Log::warning('top schedule time exceeds 7 day');
            throw new Exception('top schedule time exceeds 7 day', self::UI_ERR_TOPTIME_EXCEEDS_SEVEEN_DAY);

            return false;
        } 
        return true;
    }

    /**
     * @desc ���е�ǰ�ճ�id����֤
     * @param null
     * @return schedule info if succes or false if error exists
     */
    protected function _checkScheduleId() {
        $arrInput = array(
            'id' => $this->arrReqInfo['id'],
        );
        $arrRet = Tieba_Service::call('pluto', 'getScheduleById', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning('call pluto::getScheduleById error!'. serialize($arrInput). serialize($arrRet));
            throw new Exception('call service fail!', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return false;
        }

        //��ǰ�ճ���Ч
        if (empty($arrRet['data']) || 0 === intval($arrRet['data'][0]['status'])) {
            $strMsg = sprintf('invalid schedule with input[%s] and output[%s]', serialize($arrInput), serialize($arrRet));
            Bingo_Log::warning($strMsg);
            throw new Exception('schedule id not exsits.', self::UI_ERR_INVALID_SCHEDULE);
            return false;
        }
        //������Ȩ�޸�mis�ٷ���Ա���õ��ճ�
        if (1 === intval($arrRet['data'][0]['op_type'])) {
            $strMsg = sprintf('can not modify the schedule operated by mis op');
            Bingo_Log::warning($strMsg);
            throw new Exception('have no permission to modify this schedul.', self::UI_ERR_MODIFY_NO_PERMISSION);
            return false;
        }

        //������Ȩ�޸������ɵ��ճ�
        if (intval($this->intFid) !== intval($arrRet['data'][0]['fid'])) {
            $strMsg = sprintf('can not modify the schedule operated by mis op');
            throw new Exception('have no permission to modify this schedul.', self::UI_ERR_MODIFY_NO_PERMISSION);
            return false;
        }
        return $arrRet['data'][0];
    }
}
?>
