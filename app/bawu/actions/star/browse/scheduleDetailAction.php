<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-11-21 04:26:58
 * @version
 */
class scheduleDetailAction extends Actions_Star_BaseAsyn
{
    protected $arrKeyFields      = array('word', 'id');
    protected $arrNeedPerms      = array("can_op_as_4thmgr", "can_op_as_profession_manager","can_type2_audit_post");
    protected $arrFieldsCheck    = array('word','tbs');
    protected $strServiceType    = "deleteCalendar";

    /**
     * @desc 主函数
     * @param null
     * @return true/false
     */
    public function process() {
        try{
            //进行当前日程id验证,正确则返回日程信息
            $arrDetail = $this->_checkScheduleId();
            //进行日程信息的转换,转成FE需要的格式
            $arrDetail = $this->_convert($arrDetail);
            return $this->_buildReturn(Tieba_Errcode::ERR_SUCCESS, 'success', $arrDetail);
        } catch (Exception $e) {
            return $this->_buildReturn($e->getCode(), $e->getMessage());
        }
    }

    /**
     * @desc 获取日程参数
     * @param null
     * @return 日程参数
     */
    private function _getParam() {
        $arrInput = array(
            'id'        => $this->arrReqInfo['id'],
        );

        return $arrInput;
    }

    /**
     * @desc 将得到的日程信息转换成前端展示的字段
     * @param $arrInput : array : 日程信息
     * @return $arrOutput : array : 转换后的日程信息
     */
    private function _convert($arrInput) {
        $arrOutput = array();
        foreach ($arrInput as $key => $value) {
            if ($key === 'id') {
                $arrOutput['id'] = (int) $value;
            }
            if ($key === 'top_status') {
                $arrOutput['top'] = (bool) $value;
            }
            if ($key === 'start_time') {
                $arrOutput['time'] = (int) $value;
                $arrOutput['dateType'] = (int) $arrInput['hour_min_status'];
            }
            if ($key === 'location') {
                $arrOutput['address'] = strval($value);
            }
            if ($key === 'type') {
                $arrOutput['type'] = strval($value);
            }
            if ($key === 'title') {
                $arrOutput['title'] = strval($value);
            }
            if ($key === 'ext_info') {
                $extInfo = unserialize($value);
                $arrOutput['postUrl'] = strval($extInfo['url']);
            }
            if ($key === 'bawu_uname') {
                $arrOutput['operationUserName'] = strval($value);
            }
            if ($key === 'bawu_time') {
                $arrOutput['operationTime'] = $value;
            }
            if ($key === 'top_start_time') {
                $arrOutput['topStartTime'] = $value;
            }                                                                                                         
            if ($key === 'top_end_time') {
                $arrOutput['topEndTime'] = $value;
            }
        }

        return $arrOutput;
    }

     /**
     * @desc 进行当前日程id的验证,并返回日程信息
     * @param null
     * @return schedule info if succes or false if error exists
     */
    protected function _checkScheduleId() {
        $arrInput = array(
            'id' => $this->arrReqInfo['id'],
        );
        $arrRet = Tieba_Service::call('pluto', 'getScheduleById', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning('call pluto::getScheduleById error!'. serialize($arrInput). serialize($arrRet));
            throw new Exception('call service fail!', Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            return false;
        }

        //当前日程无效
        if (empty($arrRet['data']) || 0 === intval($arrRet['data'][0]['status'])) {
            $strMsg = sprintf('invalid schedule with input[%s] and output[%s]', serialize($arrInput), serialize($arrRet));
            Bingo_Log::warning($strMsg);
            throw new Exception('schedule id not exsits.', self::UI_ERR_INVALID_SCHEDULE);
            return false;
        }

        return $arrRet['data'][0];
    }
}
?>
