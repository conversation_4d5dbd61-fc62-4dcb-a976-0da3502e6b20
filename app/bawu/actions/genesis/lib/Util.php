<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/



/**
 * @file Util.php
 * <AUTHOR>
 * @date 2015/01/05 18:40:29
 * @brief 
 *  
 **/

class Actions_Genesis_Lib_Util {

    public static function jsonRet($errno, $errmsg, $data = array()) {
        $arrOutput = array(
            'errno'     => $errno,
            'errmsg'    => $errmsg,
        );

        if (!empty($data)) {
            $arrOutput['data'] = $data;
        }

        echo Bingo_String::array2json($arrOutput);
        return true;
    }


    public static function getActivityInfoByActivityIds(&$arrActivityIds) {
        $caller     = new Actions_Genesis_Lib_MultiCaller('genesis', 'getActivityInfo');
        $arrOutput  = $caller->call($arrActivityIds, 0, 'output', true);
        return $arrOutput;
    }


    public static function getFakeInfo($arrAdvFea, $isInit) {
        if (empty($arrAdvFea)) {
            $strCalcType = 'incr_random_by_ratio';
            $mixRatio = array(
                1, 1,
            );
        }
        else {
            $strCalcType = 'incr_random_by_ratio';
            $participants_ratio_lower_limit = (int)$arrAdvFea['participants_ratio_lower_limit'];
            $participants_ratio_upper_limit = (int)$arrAdvFea['participants_ratio_upper_limit'];

            if ($participants_ratio_lower_limit === 0 && $participants_ratio_upper_limit === 0) {
                $mixRatio = array(
                    1,1,
                );
            }
            else {
                $mixRatio = array(
                    $participants_ratio_lower_limit,
                    $participants_ratio_upper_limit,
                );
            }
        }

        $arrRet = array(
            Actions_Genesis_Lib_Def::FAKECOUNT_LOTTERY => array(
                'ratio'         => $mixRatio,
                'calc_type'     => $strCalcType,
            ),
        );

        if ($isInit) {
            $arrRet[Actions_Genesis_Lib_Def::FAKECOUNT_LOTTERY]['init_value']   = 0;
        }

        return $arrRet;
    }


    public static function getThreadIdFromLocationList(&$arrLocationList) {
        $intRet = 0;
        foreach($arrLocationList as $val) {
            $intLocationType = (int)$val['location_type'];
            if ($intLocationType === Actions_Genesis_Lib_Def::LOCATION_THREAD) {
                $intRet = (int)$val['location_related_id'];
                return $intRet;
            }
        }
        return $intRet;
    }

    /**
     * @return     void
     * <AUTHOR>
     * @brief      transfer genesis to bawu original
     **/
    public static function genesis2oriAct(&$arrActInfo) {
        //var_dump($arrActInfo);
        $arrRet = array();
        $arrRet['activity_id']      = $arrActInfo['activity_id'];
        $arrRet['begin_time']       = $arrActInfo['start_time'];
        $arrRet['end_time']         = $arrActInfo['end_time'];

        $arrRet['activity_title']   = $arrActInfo['activity_name'];
        $arrRet['activity_desc']    = $arrActInfo['activity_desc'];
        $arrRet['activity_icon']    = $arrActInfo['extra_info']['cover_pic'];

        $arrRet['name']             = $arrActInfo['activity_name'];
        $arrRet['desc']             = $arrActInfo['activity_desc'];
        $arrRet['type']             = $arrActInfo['activity_type'];
        $arrRet['status']           = $arrActInfo['activity_status'];

        foreach ($arrActInfo['extra_info'] as $key => $val) {
            $arrRet[$key] = $val;
        }

        if (isset($arrActInfo['component_list'])) {
            $arrCompList = current($arrActInfo['component_list']);
            $arrRet['component_id']     = $arrCompList['component_id'];
            $arrRet['award_act_id']     = $arrCompList['component_data']['award_act_info']['award_act_id'];
        }

        return $arrRet;
    }


    public static function genesis2oriAwi(&$arrInput) {
        //var_dump($arrInput);
        $arrRet = array();
        $arrTmp = current($arrInput)['component_data']['award_info'];
        foreach ($arrTmp as $val) {
            $item = array();
            $item['award_id']       = $val['award_id'];
            $item['award_name']     = $val['award_name'];

            //rd和fe的奖品类型的转换
            $intAwardType           = (int)$val['award_type'];
            //$intAwardType--;
            $item['award_type']     = $intAwardType;
            // award current count
            $item['award_num']      = $val['award_cur_count'];
            $item['award_ratio']    = $val['calc_rule']['probability']/100;
            $item['award_desc']     = $val['desc_text'];
            $item['award_pic']      = $val['desc_pic'];

            $arrExtInfo = $val['ext_info'];
            $item['name']           = $arrExtInfo['person_info']['name'];
            $item['address']        = $arrExtInfo['person_info']['address'];
            $item['phone']          = $arrExtInfo['person_info']['phone'];
            $item['postcode']       = $arrExtInfo['person_info']['postcode'];
            if (!is_null($arrExtInfo['person_info']['user_define'])) {
                $item['user_define']    = $arrExtInfo['person_info']['user_define'];
            }
            $item['award_method']   = $arrExtInfo['award_method'];
            $item['award_link']     = $arrExtInfo['award_link'];
            //$item['exkey_act_id']   = $arrExtInfo['exkey_act_id'];
            $arrRet [] = $item;
        }
        return $arrRet;
    }


    public static function genesis2oriAwr(&$arrInput) {
        $arrAwdRule = current($arrInput)['extra_info']['activity_rule'];
        if (1 === (int)$arrAwdRule['regular']['no_permit_repeat_award']) {
            $arrAwdRule['regular']['no_permit_repeat_award'] = true;
        }
        else {
            $arrAwdRule['regular']['no_permit_repeat_award'] = false;
        }
        return $arrAwdRule;
    }


    public static function feRule2rdRule (&$arrInput) {
        $arrRet = array();
        /*
         *
         $arrRet[Actions_Genesis_Lib_Def::CHANCE_INIT]['chance_num']       = (int)$arrInput['todo'];
        $arrRet[Actions_Genesis_Lib_Def::CHANCE_ADDSIGN]['chance_num']    = (int)$arrInput['todo'];
        $arrRet[Actions_Genesis_Lib_Def::CHANCE_ADDPOST]['chance_num']    = (int)$arrInput['todo'];
        $arrRet[Actions_Genesis_Lib_Def::CHANCE_ADDRELAY]['chance_num']   = (int)$arrInput['todo'];
        $arrRet[Actions_Genesis_Lib_Def::CHANCE_LIMIT]['chance_num']      = (int)$arrInput['todo'];
         */
        $arrRet = $arrInput;
        return $arrRet;
    }


    /**
     * @return     void
     * <AUTHOR>
     * @brief      generate the tyche style data
     **/
    public function getTycheStyleAwardData ($arrInput, $intAwardActId) {
        $arrRet = array();

        $arrFields = array('award_name'=>1, 'desc_pic'=>1, );
        $intIndex = 0;
        $intExkeyAwardCount = 0;
        foreach ($arrInput as $val) {
            $arrRet[$intIndex]['award_act_id']  = $intAwardActId;
            $intAwardId = (int)$val['award_id'];
            if (0 < $intAwardId) {
                $arrRet[$intIndex]['award_id']  = $intAwardId;
                $arrRet[$intIndex]['award_append_count']   = (int)($val['award_num']);
            }else{
                $arrRet[$intIndex]['award_count']   = (int)($val['award_num']);
            }
            $arrRet[$intIndex]['award_name']    = self::getParamIfExist($val, 'award_name', 'str', null, Actions_Genesis_Lib_Def::AWARD_MAX_NAME);
            $arrRet[$intIndex]['desc_text']     = self::getParamIfExist($val, 'award_desc', 'str', null, Actions_Genesis_Lib_Def::AWARD_MAX_DESC);
            $arrRet[$intIndex]['desc_pic']      = self::getParamIfExist($val, 'award_pic', 'str', null, Actions_Genesis_Lib_Def::AWARD_MAX_COVER);
            //var_dump($arrRet[$intIndex]['desc_pic']);

            //check award type
            $intAwardType = (int)$val['award_type'];
            //fe传来的是0(实物),1(虚拟)，修正一下
            //FE已经修改了
            //$intAwardType++;
            if (Actions_Genesis_Lib_Def::AWARD_TYPE_EXKEY === $intAwardType) {
                $intExkeyAwardCount ++;
            }
            $arrRet[$intIndex]['award_type']    = $intAwardType;

            $arrRet[$intIndex]['calc_type']     = 0;
            $intProb = (double)$val['award_ratio']*100;
            $arrRet[$intIndex]['calc_rule']     = array('probability' => $intProb, );

            //exkey keys
            $arrTmpList = explode("\n", $val['vaward_list']);
            $arrKeys = array();
            foreach ($arrTmpList as $key) {
                $key = trim($key);
                if(0 < strlen($key)) {
                    $arrKeys[] = $key;
                }
            }
            if (Actions_Genesis_Lib_Def::AWARD_MAX_EXKEY_KEYS_COUNT < count($arrKeys)) {
                return false;
            }

            if (!empty($arrKeys)) {
                $arrRet[$intIndex]['keys'] = $arrKeys;
            }

            $arrExtInfo = isset($val['ext_info']) ? unserialize($val['ext_info']) : array();
            $arrPersonInfo = array(
                'name'          => (int)$val['name'],
                'phone'         => (int)$val['phone'],
                'address'       => (int)$val['address'],
                'postcode'      => (int)$val['postcode'],
                'user_define'   => isset($val['user_define']) ? $val['user_define'] : '',
            );

            $arrExtInfo['person_info']  = $arrPersonInfo;
            //$arrExtInfo['award_code']   = $val['award_code'];
            $arrExtInfo['award_method'] = isset($val['award_method']) ? $val['award_method'] : '';
            $arrExtInfo['award_link']   = isset($val['award_link']) ? $val['award_link'] : '';
            if (isset($val['exkey_act_id'])) {
                $arrExtInfo['exkey_act_id'] = (int)$val['exkey_act_id'];
            }
            $arrRet[$intIndex]['ext_info']      = $arrExtInfo;

            //check
            if(!self::postCheckParam($arrRet[$intIndex], $arrFields, false)) {
                return false;
            }
            $intIndex++;
        }

        if (Actions_Genesis_Lib_Def::AWARD_MAX_EXKEY_COUNT < $intExkeyAwardCount) {
            Bingo_Log::warning('exkey award reach the max count!');
            return false;
        }
        return $arrRet;
    }


    /**
     * @return     void
     * <AUTHOR>
     * @brief      post check
     **/
    public static function postCheckParam(&$arrInput, &$arrFields, $bolNoZero = true) {
        foreach ($arrInput as $key => $val) {
            if (self::isEmpty($val, $bolNoZero)) {
                if (isset($arrFields[$key])) {
                    Bingo_Log::warning("key field $key has some problem!");
                    return false;
                }
                else{
                    //unset($arrInput[$key]);
                }
            }
        }
        return true;
    }


    /**
     * @return     void
     * <AUTHOR>
     * @brief      check param if empty
     **/
    public static function isEmpty(&$mixInput, $bolNoZero = true) {
        if ($bolNoZero) {
            if (empty($mixInput) && 0 != $mixInput) {
                return true;
            }
            else {
                return false;
            }
        }
        else {
            if (empty($mixInput)) {
                return true;
            }
            else {
                return false;
            }
        }
        return false;
    }


    /**
     * @return     void
     * <AUTHOR>
     * @brief      get param if exist
     **/
    public static function getParamIfExist(&$arrInput, $strField, $strType, $intMin=null, $intMax=null) {
        if ('' === $strField) {
            $mixTmp = $arrInput;
        }
        else {
            $mixTmp = $arrInput[$strField];
        }
        if ('str' === $strType) {
            if (is_array($mixTmp)) {
                $mixTmp = serialize($mixTmp);
            }
            else {
                $mixTmp = (string)$mixTmp;
                if (!is_null($intMin)) {
                    if (strlen($mixTmp) < $intMin) {
                        return false;
                    }
                }
                if (!is_null($intMax)) {
                    if (strlen($mixTmp) > $intMax) {
                        return false;
                    }
                }
                if (empty($mixTmp)) {
                    return false;
                }
            }
        }
        else if ('int' === $strType) {
            $mixTmp = (int)$mixTmp;
            if (!is_null($intMin)) {
                if ($mixTmp < (int)$intMin) {
                    return false;
                }
            }
            if (!is_null($intMax)) {
                if ($mixTmp > (int)$intMax) {
                    return false;
                }
            }
            if (empty($mixTmp) && 0 != $mixTmp) {
                return false;
            }
        }
        else if ('json' === $strType) {
            if (!is_array($mixTmp) || empty($mixTmp)) {
                return false;
            }
            else{
                $arrTmp = array();
                foreach($mixTmp as $key=>$val) {
                    if (!empty($val) || 0 === $val) {
                        $arrTmp[$key] = $val;
                    }
                }
                $mixTmp = Bingo_String::array2json($arrTmp);
            }
        }
        else if ('strtime' === $strType) {
            $mixTmp = strtotime($mixTmp);
            if (0 >= $mixTmp){
                return false;
            }
        }
        else {
            //todo 
        }

        return $mixTmp;
    }


}


/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
