<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file get_activity.php
 * <AUTHOR>
 * @date 2015-01-10 ������ 
 * @brief 
 *  
 **/

require_once('../fileHelper.php');
require_once('../multiCaller.php');

$intTmpMax =5000;
for ($i = 1001; $i < $intTmpMax; $i++) {
    $arrInput[] = array(
        'activity_id'   => $i,
    );
}

//var_dump($arrInput);

function checkEmpty(&$arrInput) {
    if (!isset($arrInput['list'])) {
        return true;
    }
    else {
        return false;
    }
}


$caller = new MultiCaller('actstage', 'bwActivityById');
$caller->setEmptyCb('checkEmpty');
$arrOut = $caller->call($arrInput, 0, 'data', true);

echo 'there are ' . $caller->getErrorCount() . ' error(s)'. "\n\r";
echo 'there are ' . $caller->getEmptyCount() . ' empty result(s)'. "\n\r";

$file = new fileHelper();
$file->set("data_actstage_activity_info", "write_new");

$i = 0;
foreach ($arrOut as $val) {
    if ($i%2 === 0) {
        $file->write_single_line($val);
    }
    else {
        $file->write_single_line($val['list'][0]);
    }
    $i++;
}

if (0 < $caller->getErrorCount()) {
    $error = new FileHelper();
    $error->set('actstage_activity_error', 'write_append');

    foreach ($caller->getErrInput() as $val) {
        $error->write_single_line($val);
    }
}

echo "Total time is ". $caller->getLastCallTime();


/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
