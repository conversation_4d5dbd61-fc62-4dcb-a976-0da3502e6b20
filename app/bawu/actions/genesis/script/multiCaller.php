<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/



/**
 * @file multi_caller.php
 * <AUTHOR>
 * @date 2015/01/09 16:33:16
 * @brief 其中，返回的结果奇数列是输入，偶数列是输出
 *  
 **/

class MultiCaller {

    const MULTI_CALL_MAX_COUNT = 30;

    private $_strService    = '';
    private $_strModule     = '';
    private $_intStarttime  = 0;
    private $_intEndtime    = 0;

    private $_mixTime       = null;
    private $_arrResult     = array();
    private $_arrEmptyRes   = array();
    private $_arrErrorRes   = array();

    //empty check callback
    private $_strEmptyCb    = null;

    public function __construct($strModule, $strService) {
        echo "start excute the script\n\r";
        $this->_strModule   = $strModule;
        $this->_strService  = $strService;
    }


    /**
     * @return     void
     * <AUTHOR>
     * @brief      set empty check callback
     **/
    public function setEmptyCb($callback) {
        if (0 >= strlen($callback)) {
            echo 'wrong callback function!';
            return false;
        }
        $this->_strEmptyCb = $callback;
    }


    /**
     * @return     void
     * <AUTHOR>
     * @brief      MULTI CALL
     **/
    public function call($arrInput, $intSleepTime = null, $strDataFieldName = 'data', $bolNeedInput = true) {
        $this->_intStarttime = time();
        $intMaxInput = count($arrInput);
        $strRalKey = $this->_strModule . "_" . $this->_strService; 
        for ($i = 0; $i < $intMaxInput; $i++) {

            $objRalMulti = new Tieba_Multi($strRalKey);
            for ($j = 0; $j < self::MULTI_CALL_MAX_COUNT && $i < $intMaxInput; $i++, $j++) {
                $arrMultiInput = array(
                    'serviceName'   => $this->_strModule,
                    'method'        => $this->_strService,
                    'input'         => $arrInput[$i],
                );

                $strRalSingleKey = $this->_strModule . "_" . $this->_strService . "_" . $i;
                $objRalMulti->register($strRalSingleKey, new Tieba_Service($this->_strModule), $arrMultiInput);
            }
            $objRalMulti->call();
            echo "reach the $i th input \n\r";
            $this->_intEndtime = time();
            echo "Now cost " . $this->getLastCallTime() . "\n\r";

            //process data
            foreach ($objRalMulti->results as $key => $val) {
                $arrTmp = explode('_', $key);
                $intInputIndex  = (int)$arrTmp[count($arrTmp)-1];
                if (0 != $val['errno']) {
                    $this->_arrErrorRes [] = $arrInput[$intInputIndex];
                }
                else {
                    if (!is_null($this->_strEmptyCb) && call_user_func($this->_strEmptyCb, $val[$strDataFieldName])) {
                        $this->_arrEmptyRes [] = $arrInput[$intInputIndex];
                    }
                    else if (empty($val[$strDataFieldName])) {
                        $this->_arrEmptyRes [] = $arrInput[$intInputIndex];
                    }
                    else {
                        if ($bolNeedInput) {
                            $this->_arrResult [] = $arrInput[$intInputIndex];
                        }
                        $this->_arrResult [] = $val[$strDataFieldName];
                    }
                }
                
            }

            if (!is_null($intSleepTime && 0 != $intSleepTime)) {
                sleep($intSleepTime);
            }

        }

        $this->_intEndtime = time();
        return $this->_arrResult;
    }


    /**
     * @return     void
     * <AUTHOR>
     * @brief      introduction
     **/
    private function _calculateTime($type) {
        $intTime = $this->_intEndtime - $this->_intStarttime;
        $intPerMin = 60;
        $intMin = (int)($intTime/$intPerMin);
        $intSec = $intTime - $intMin*60;

        if ('m-s' === $type) {
            $this->_mixTime = "$intMin minite(s) , $intSec second(s) \n\r";
        }
        else {
            //todo
        }
    }


    public function getLastCallTime($type = 'm-s') {
        $this->_calculateTime($type);
        return $this->_mixTime;
    }

    public function getErrorCount() {
        return count($this->_arrErrorRes);
    }

    public function getEmptyCount() {
        return count($this->_arrEmptyRes);
    }


    public function getErrInput() {
        return $this->_arrErrorRes;
    }

}


/*
 *
 * http://cq01-forum-rdtest04.vm.baidu.com/
            $this->_handle


/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
