<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/



/**
 * @file file.php
 * <AUTHOR>
 * @date 2015/01/10 08:46:55
 * @brief 处理各种文件操作
 *  
 **/



class FileHelper {

    const       SEPERATOR = "\n";
    private     $_type = '';
    private     $_handler;
    private     $_line_num = 0;

    public function __construct() { 
        $this->_handler = null;
    }


    public function __destruct() {
        if (!is_null($this->_handler)) {
            fclose($this->_handler);
        }
    }


    public function set($filename, $type = 'read', $location = 'local') {
        if (0 >= strlen($filename)) {
            echo 'empty filename!';
        }

        switch ($type) {
        case 'read':
            $this->_handler     = fopen($filename, 'rb');
            if (!$this->_handler) {
                echo "open file fail! \n\r";
            }
            break;
        case 'write_append':
            $this->_handler     = fopen($filename, 'ab');
            if (!$this->_handler) {
                echo "open file fail! \n\r";
            }
            break;
        case 'write_new':
            $this->_handler     = fopen($filename, 'wb');
            if (!$this->_handler) {
                echo "open file fail! \n\r";
            }
            break;
        }
    }

    /**
     * @return     void
     * <AUTHOR>
     * @brief      read line, and move the pointer
     **/

    public function read_single_line($type = 'base64') {
        if (feof($this->_handler)) {
            return false;
        }
        $data = fgets($this->_handler);
        if (false === $data) {
            //echo 'get file content failed!';
            return false;
        }
        else {
            $this->_line_num ++;
            if ('base64' === $type) {
                return unserialize(base64_decode($data));
            }
            else if ('normal' === $type) {
                return trim($data);
            }
            else {
                return unserialize($data);
            } 
        }

    }

    public function write_single_line($data, $type = 'base64') {
        if (empty($data) && 0 != $data) {
            echo 'data is empty!';
                return -1;
        }
        if ('base64' === $type ) {
            fwrite($this->_handler, base64_encode(serialize($data)). self::SEPERATOR);
        }
        else if ('normal' === $type) {
            fwrite($this->_handler, $data . self::SEPERATOR);
        }
        else {
            fwrite($this->_handler, serialize($data) . self::SEPERATOR);
        }
    }


    public function getReadLineCount() { 
        return $this->_line_num;
    }

}


/*
$objFile = new FileHelper();
$objFile->set('test');

while ($ret = $objFile->read_single_line()) {
    var_dump($ret);
}

 */






/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
