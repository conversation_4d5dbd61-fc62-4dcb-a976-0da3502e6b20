<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file downloadAction.php
 * <AUTHOR>
 * @date 2015/01/08 00:44:08
 * @brief download user award list by excel
 *  
 **/

class downloadAction extends Actions_Genesis_Lib_Base {
    
    protected $arrKeyFields     = array('word', );
    protected $arrOptFields     = array('pn', 'rn', );
    protected $strServiceType   = 'download';

    public function process() {
        
        $intActivityId = Bingo_Http_Request::get('activity_id', 0);

        if (0 >= $intActivityId) {
            $this->strErrmsg = 'param invalid, wrong acitivity id!';
            Bingo_Log::warning($this->strErrmsg);
            $this->_jsonRet(Tieba_Errcode::ERR_MO_PARAM_INVALID, $this->strErrmsg);
            return false;
        }

        $arrInput = array(
            array(
                'activity_id' => $intActivityId,
            ),
        );

        $arrOutput = Actions_Genesis_Lib_Util::getActivityInfoByActivityIds($arrInput);
        $arrComponentList = current($arrOutput[1]['activity_info']['component_list']);
        $intAwardActId = (int)$arrComponentList['component_related_id'];
        
        $strInputModule     = 'tyche';
        $arrInput[$strInputModule]['award_act_id']    = $intAwardActId;
        $arrInput[$strInputModule]['offset']          = isset($this->arrReqInfo['pn']) ? (int)$this->arrReqInfo['pn']-1 : Actions_Genesis_Lib_Def::DOWNLOAD_DEFAULT_PN-1;
        $arrInput[$strInputModule]['count']           = isset($this->arrReqInfo['rn']) ? (int)$this->arrReqInfo['rn'] : Actions_Genesis_Lib_Def::DOWNLOAD_DEFAULT_RN;

        $strModule  = 'tyche';
        $strService = 'dumpAllUserAward';
        //var_dump($arrInput[$strInputModule]);
        $arrOutput = Tieba_Service::call($strModule, $strService, $arrInput[$strInputModule]);
        if (false === $arrOutput || 0 != $arrOutput['errno']) {
            $this->intErrno     = $arrOutput['errno'];
            $this->strErrmsg    = "call service $strModule/$strService failed, ". $arrOutput['errmsg'];
            $this->_jsonRet($this->intErrno, $this->strErrmsg);
            return false;
        }
        if (!$this->_toExcel($arrOutput['data']['all_user_awards'], $intAwardActId)) {
            return false;
        }

        return true;
    }

    
    /**
     * @return     void
     * <AUTHOR>
     * @brief      data to excel
     **/
    private function _toExcel(&$arrInput, $intAwardActId) {
        if (empty($arrInput)) {
            echo '当前没有中奖用户！';
            //$this->_jsonRet(0, 'success, but no user award');
            return false;
        }
        
        $arrAwardInfoHash = $this->_getAwardInfoHash($intAwardActId);
        $arrDetail  = array();
        $intIndex   = 1;
        
        /**
         * test
        $arrTmp = array(
            'address' => 'zhangye',
        );
        $arrInput = array(
            array(
                'award_id' => 285,
                'user_id'   => 1123,
                'user_info'     => Bingo_String::array2json($arrTmp),
            )
        );
         */

        //build excel head
        $arrExcelHead = array();
        $arrExcelHead[] = '中奖用户名';
        $arrExcelHead[] = '奖品名称';
        $arrExcelHead[] = '获奖时间';
        $arrExcelHead[] = '用户个人信息';
        $arrExcelHead[] = '虚拟码';

        $arrNewExcelHead = array();
        foreach ($arrExcelHead as $val) {
            $arrNewExcelHead[] = Bingo_Encode::convert($val, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
        }
        $arrDetail[] = $arrNewExcelHead;
        
        $arrUids = array();
        foreach ($arrInput as $row) {
            $arrContent = array();
            $arrContent[] = $row['user_id'];
            $arrUids [] = $row['user_id'];
            //award info
            $intAwardId = (int)$row['award_id'];
            $arrContent[] = $arrAwardInfoHash[$intAwardId]['award_name'];

            $arrContent[] = date("Y-m-d H:i:s", $row['award_time']);

            $arrUserInfo = $row['user_info'];
            $strUserInfo = '';
            foreach ($arrUserInfo as $key => $val) {
                if ($key == 'user_define') {
                    continue;
                }
                $strUserInfo .= "  $key : $val  ";
            }
            $arrContent[] = $strUserInfo;

            $arrAwardDesc = $row['award_desc'];
            if(isset($arrAwardDesc['key'])) {
                $arrContent[] = $arrAwardDesc['key'];
            }
            else {
                $arrContent[] = ' ';
            }
            
            $arrDetail[$intIndex++] = $arrContent;
        }

        $arrUidHash = $this->_getUnames($arrUids);
        //var_dump($arrUidHash);
        $i = 1;
        for(; $i <count($arrDetail); $i++){
            $user_id = $arrDetail[$i][0];
            $arrDetail[$i][0] = $arrUidHash[$user_id];
        }

        header('Content-Type:application/vnd.ms-excel; charset=GBK');
        $xlsFile = new Util_Excel;
        $xlsFile->addArray($arrDetail);
        $xlsFile->generateXML('award_act_id_' . $intAwardActId);
        return true;
    }

    
    private function _getUnames(&$arrUids) {
        $arrOutput = Tieba_Service::call('user', 'getUnameByUids', array('user_id' => $arrUids));
        
        $arrRet = array();
        foreach($arrOutput['output']['unames'] as $val) {
            $user_id = (int)$val['user_id'];
            $arrRet[$user_id] = $val['user_name'];
        }

        return $arrRet;
    }


    private function _getAwardInfoHash($intAwardActId) {
        $strInputModule     = 'tyche';
        $strModule          = 'tyche';
        $strService         = 'getAwardAct';
        $arrInput[$strInputModule] = array(
            'award_act_id'  => $intAwardActId,
        );
        //var_dump($arrInput[$strInputModule]);
        $arrOutput = Tieba_Service::call($strModule, $strService, $arrInput[$strInputModule]);
        //var_dump($arrOutput);
        if (false === $arrOutput || 0 != $arrOutput['errno']) {
            $this->strErrmsg    = "call service $strModule/$strService failed, ". $arrOutput['errmsg'];
            Bingo_Log::warning($this->strErrmsg);
            return false;
        }

        $arrRet = array();
        foreach ($arrOutput['data']['award_info'] as $val) {
            $arrRet[$val['award_id']] = $val;
        }
        return $arrRet;
    }


    private function _jsonRet($intErrno, $strErrmsg, $arrData) {
        $arrRet = array(
            'errno'     => $intErrno,
            'errmsg'    => $strErrmsg,
        );
        if (!empty($arrData) || 0 === $arrData) {
            $arrRet['data'] = $arrData;
        }       
        echo Bingo_String::array2json($arrRet);
    }


}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
