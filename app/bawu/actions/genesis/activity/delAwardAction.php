<?php

class delAwardAction extends Actions_Genesis_Lib_Base {
    
    protected $arrKeyFields = array('tbs', 'word', );
    protected $arrOptFields = array();

    private $_strErrmsg = '';
    private $_intErrno = 0;

    public function process() {
        $intAwdActId    = (int)Bingo_Http_Request::get('award_act_id', 0);
        $intAwdId       = (int)Bingo_Http_Request::get('award_id', 0);

        if (0 === $intAwdId || 0 === $intAwdActId) {
            $this->_intErrno = 0;
            $this->_strErrmsg = "empty award_id or award_act_id!";
            Bingo_Log::warning($this->_strErrmsg);
            Actions_Genesis_Lib_Util::jsonRet($this->_intErrno, $this->_strErrmsg);
            return false;
        }
            
        $arrInput = array(
            'award_act_id'  => $intAwdActId,
            'award_id'      => $intAwdId,
        );

        $arrOutput = Tieba_Service::call('tyche', 'deleteAward', $arrInput);

        if (false === $arrOutput || 0 !== $arrOutput['errno']) {
            $this->_intErrno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            $this->_strErrmsg = "deleteAward failed!";
            Bingo_Log::warning($this->_strErrmsg);
            Actions_Genesis_Lib_Util::jsonRet($this->_intErrno, $this->_strErrmsg);
            return false;
        }

        Actions_Genesis_Lib_Util::jsonRet(0, 'success');
        return true;
    }
    
}
