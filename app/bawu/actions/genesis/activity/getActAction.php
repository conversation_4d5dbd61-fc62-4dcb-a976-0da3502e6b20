<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file getActAction.php
 * <AUTHOR>
 * @date 2015/01/08 00:18:18
 * @brief 
 *  
 **/

class getActAction extends Actions_Genesis_Lib_Base{
    
    protected $arrKeyFields = array('activity_id', 'word', );
    protected $arrOptFields = array('tbs',);
    protected $strServiceType = 'getActAction';

    public function process() {
        echo 12;
        exit();
        $this->intActivityId = (int)$this->arrReqInfo['activity_id'];
        $strType = $this->arrReqInfo['type'];

        $arrInput = array(
            array(
                'activity_id'   => $this->intActivityId,
            ),
        );

        $arrOutput = Actions_Genesis_Lib_Util::getActivityInfoByActivityIds($arrInput);
        if (false === $arrOutput[1]) {
            $strErrmsg  = 'fail to get activity_info!';
            $intErrno   = Tieba_Errcode::ERR_MO_PARAM_INVALID;
            $this->_jsonRet($intErrno, $strErrmsg);
            return false;
        }
        
        return $arrRet;
    }


    private function _jsonRet($intErrno, $strErrmsg, $arrData) {
        $arrRet = array(
            'errno'     => $intErrno,
            'errmsg'    => $strErrmsg,
        );
        if (!empty($arrData) || 0 === $arrData) {
            $arrRet['data'] = $arrData;
        }
        echo Bingo_String::array2json($arrRet);
        return true;
    }


}




/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
