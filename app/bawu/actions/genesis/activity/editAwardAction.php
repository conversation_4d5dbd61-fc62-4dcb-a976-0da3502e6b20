<?php

class editAwardAction extends Actions_Genesis_Lib_Base {
    
    protected $arrKeyFields = array('tbs', 'word', );
    protected $arrOptFields = array();

    private $_strErrmsg = '';
    private $_intErrno = 0;

    public function process() {
        $arrAwdInfo     = Bingo_Http_Request::get('award_info', array());
        $intAwdActId    = (int)Bingo_Http_Request::get('award_act_id', 0);
        $intAwdId       = (int)$arrAwdInfo['award_id'];
        $arrAwdInfo     = Bingo_String::xssDecode($arrAwdInfo);

        if (empty($arrAwdInfo) || 0 === $intAwdActId || 0 === $intAwdId) {
            $this->_intErrno = 0;
            $this->_strErrmsg = "empty param!";
            Bingo_Log::warning($this->_strErrmsg);
            Actions_Genesis_Lib_Util::jsonRet($this->_intErrno, $this->_strErrmsg);
            return false;
        }
        
        $arrAwdInfo = Actions_Genesis_Lib_Util::getTycheStyleAwardData(array($arrAwdInfo, ), $intActId);
        $arrInput = array(
            'award_act_id' => $intAwdActId,
        );

        $arrAwdInfo[0]['award_cur_count'] = $arrAwdInfo[0]['award_append_count'];
        unset($arrAwdInfo[0]['award_append_count']);
        $arrInput['award_info'] = $arrAwdInfo;

        $arrOutput = Tieba_Service::call('tyche', 'modifyAward', $arrInput);

        if (false === $arrOutput || 0 !== $arrOutput['errno']) {
            $this->_intErrno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            $this->_strErrmsg = "modifyAward failed!";
            Bingo_Log::warning($this->_strErrmsg);
            Actions_Genesis_Lib_Util::jsonRet($this->_intErrno, $this->_strErrmsg);
            return false;
        }

        Actions_Genesis_Lib_Util::jsonRet(0, 'success');
        return true;
    }
    
}
