<?php
/*
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR> @date 2020-11-18 19:25:58
 * @version
 */
class getBawuForumManagerInfoAction extends Bingo_Action_Abstract {
	private static $_forumId = 0;
	private static $_uid = 0;
	private static $_arrManagerAndAssist = array();

	/**
	 * [execute]
	 * @param
	 * @return bool
	 */
	public function execute(){
		$arrUserInfo    = Util_Session::getUserSessionInfo();
		self::$_uid = $arrUserInfo['intUid'];
		$forum_name = strval(Bingo_Http_Request::get("forum_name", ''));
		if(empty($forum_name) || !isset($forum_name)){
			Bingo_Log::warning("no forum_name found.");
			$this->_errRet(Tieba_Errcode::ERR_PARAM_ERROR, 'the forum_name is empty');
			return false;
		}
		//get forum_id
		$arrInput = array(
			"query_words" => array(
				0 => $forum_name, //吧名
			)
		);
		$arrRes = Tieba_Service::call('forum', 'getFidByFname', $arrInput, null, null, 'post', 'php', 'utf-8');
		if(empty($arrRes['forum_id'][0]['forum_id']) || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
			Bingo_Log::warning("call forum::getFidByFname error. input:" . serialize($arrInput) . " output: " . serialize($arrRes));
			$this->_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
			return false;
		}
		self::$_forumId = $arrRes['forum_id'][0]['forum_id'];

		$arrInput = array(
			"forum_id" => self::$_forumId,
		);
		$arrRes = Tieba_Service::call('perm', 'getBawuList', $arrInput, null, null, 'post', 'php', 'utf-8');
		if(empty($arrRes['output']) || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
			Bingo_Log::warning("call perm::getBawuList error. input:" . serialize($arrInput) . " output: " . serialize($arrRes));
			$this->_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
			return false;
		}
		//处理吧主
		if (!empty($arrRes['output']['manager'])) {
			foreach ($arrRes['output']['manager'] as $manager) {
				self::$_arrManagerAndAssist[] = $manager['user']['user_id'];
			}
		}
		//处理小吧主
		if (!empty($arrRes['output']['assist'])) {
			foreach ($arrRes['output']['assist'] as $assist) {
				self::$_arrManagerAndAssist[] = $assist['user']['user_id'];
			}
		}
		//不是吧主或吧务是空的，空的返回
		if (empty(self::$_arrManagerAndAssist) || !in_array(self::$_uid, self::$_arrManagerAndAssist)) {
			$this->_errRet(Tieba_Errcode::ERR_SUCCESS, 'user is not manager');
			return false;
		}
		$arrDeletedReasonInfo = array(
			"is_grays_cale_forum" => '0', //非灰度吧
			"is_boomgrow" => '0', //不是爆吧时期
			'has_forum_rule' => '0', //是否有吧规
		);
		//multicall
		$multiName = 'bawuForumManagerInfo';
		$arrInput = array(
			'forum_id'              =>	self::$_forumId,
			'need_rule_detail'      => 0,
			'structured_content'    => 1,
			'now_time' 				=> time(),
		);
		$multiServiceInfo = array(
			'getAuditForumRule' => array(
				'serviceName'  => 'bawu',
				'method'       => 'getAuditForumRule',
				'input'        => $arrInput,
				'ie'           => 'gbk',
			),
			'queryPopforum' => array(
				'serviceName'  => 'bawu',
				'method'       => 'queryPopforum',
				'input'        => $arrInput,
				'ie'           => 'gbk',
			),
		);
		$multiResult = self::multiCall($multiName, $multiServiceInfo);
		//check bawu task
		$arrQueryPopforumRes = $multiResult['queryPopforum'];
		if (empty($arrQueryPopforumRes['ret']) || Tieba_Errcode::ERR_SUCCESS != $arrQueryPopforumRes['errno']) {
			Bingo_Log::warning ( 'call service bawu::queryPopforum failed,errno[' . $arrQueryPopforumRes ['errno'] . '],errmsg[' . $arrQueryPopforumRes ['errmsg'] . ']' );
		}
		$arrDeletedReasonInfo['is_boomgrow'] = !empty($arrQueryPopforumRes['ret']['is_popforum']) ? $arrQueryPopforumRes['ret']['is_popforum'] : 0;

		//查灰度吧
		$objWordServer = Wordserver_Wordlist::factory();
		$arrInput  = array(
			'forum_list',
			'switch_type',
		);
		$arrFidRes   = $objWordServer->getValueByKeys($arrInput, 'tb_wordlist_redis_ForumRule_Switch');
		$arrFidList = unserialize($arrFidRes['forum_list']);
		$intSwitchType = intval($arrFidRes['switch_type']);
		if (!empty($arrFidList) && in_array(self::$_forumId, $arrFidList)) {
			$isShowForumRule = true;
		}
		//0:全站关闭,所有吧都不是灰度吧
		if ($intSwitchType == 0) {
			$arrDeletedReasonInfo['is_grays_cale_forum'] = 0;
			$isShowForumRule = false;
		}
		//1:使用白名单控制,白名单上的是灰度吧
		if ($intSwitchType == 1) {
			$arrDeletedReasonInfo['is_grays_cale_forum'] = !$isShowForumRule ? 0 : 1;
		}
		//2:全站开启,所有吧都是灰度吧
		if ($intSwitchType == 2) {
			$arrDeletedReasonInfo['is_grays_cale_forum'] = 1;
			$isShowForumRule = true;
		}
		$arrOutData['forum']['deleted_reason_info'] = $arrDeletedReasonInfo;

		//是否有吧规
		if (!$isShowForumRule) {
			$arrDeletedReasonInfo['has_forum_rule'] = 0;
		} else {
			$arrForumRuleRes = $multiResult['getAuditForumRule'];
			if (false === $arrForumRuleRes || Tieba_Errcode::ERR_SUCCESS != $arrForumRuleRes['errno']) {
				Bingo_Log::warning ( 'call service bawu::getAuditForumRule failed,errno[' . $arrForumRuleRes ['errno'] . '],errmsg[' . $arrForumRuleRes ['errmsg'] . ']' );
			}
			$arrDeletedReasonInfo['has_forum_rule'] = $arrForumRuleRes['data']['has_forum_rule'] ? 1 : 0;
		}

		$this->_errRet(Tieba_Errcode::ERR_SUCCESS, '', $arrDeletedReasonInfo);
		return false;

	}

	/**
	 * [multiCall]
	 * @param  [string] $multiName [description]
	 * @param  [array] $multiService [description]
	 * @return [array] $arrResult [description]
	 */
	private static function multiCall($multiName, $multiService) {
		$objRalMulti = new Tieba_Multi($multiName);
		foreach ($multiService as $k => $v){
			$objRalMulti->register($k, new Tieba_Service($v['serviceName']), $v);
		}
		$objRalMulti->call();
		foreach ($multiService as $k => $v){
			$arrResult[$k] = $objRalMulti->getResult($k);
		}
		return $arrResult;
	}

	/**
	 * [_errRet]
	 * @param  [int] $errno [description]
	 * @param  [string] $errmsg [description]
	 * @param  [array] $data [description]
	 * @return
	 */
	protected function _errRet($errno, $errmsg = '', $data = array()) {
		if($errmsg === ''){
			$errmsg = Tieba_Error::getErrmsg($errno);
		}
		$arrOut = array(
			'errno' => $errno,
			'errmsg' => $errmsg,
			'data' => $data,
		);
		foreach($arrOut as $k => $v){
			Bingo_Page::assign($k, $v);
		}
		Bingo_Page::setOnlyDataType("json");
	}
}
?>
