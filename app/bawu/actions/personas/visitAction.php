<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file visitAction.php
 * <AUTHOR>
 * @date 2015/07/22 18:06:06
 * @brief 
 *  
 **/
 
class visitAction extends Util_Personas_Base {
    protected $_arrReqKeyFields = array('forum_id', 'start_time', 'end_time');
    protected $_arrReqOptFields = array('client_type');
    protected $_strServiceType = 'visitAction';
    
    public function preProcess() {
        return true;
    }
    
    public function process() {
        if (false === $this->_checkParams()) {
            return false;
        }
        for ($intDeltaDay = 0; $intDeltaDay < Util_Def::MAX_DELTA_DAY; $intDeltaDay++) {
            $intStart = strtotime("-$intDeltaDay day", strtotime($this->_arrReqParam['start_time']));
            $intEnd = strtotime("-$intDeltaDay day", strtotime($this->_arrReqParam['end_time']));
            $arrInput = array(
                'page_type'   => 1,
                'forum_id'    => intval($this->_arrReqParam['forum_id']),
                'start_time'  => date('Ymd', $intStart),
                'end_time'    => date('Ymd', $intEnd),
                'client_type' => intval($this->_arrReqParam['client_type']),
                'page_type'   => 1,
            );
            $arrPageRes = Tieba_Service::call('personas', 'getPageTopTen', $arrInput);
            if (false === $arrPageRes || Tieba_Errcode::ERR_SUCCESS !== $arrPageRes['errno']) {
                Bingo_Log::warning('call personas getPageTopTen fail ' . serialize($arrInput) . '_' . serialize($arrPageRes));
                $intErr = $arrPageRes['errno'];
                if (!isset($arrPageRes['errno'])) {
                    $intErr = -1;
                }
                //$this->_jsonOutput($intErr, 'get page top ten info fail');
                continue;
            }
            if (empty($arrPageRes['data'])) {
                continue;
            }
            $arrData = $this->_getRetData($arrPageRes['data']);
            $this->_jsonOutput(Tieba_Errcode::ERR_SUCCESS, 'success', $arrData);
            return true;
        } 
    }
    
    private function _getRetData($arrData) {
        $arrRet = array();
        if (false === self::_init()) {
            Bingo_Log::warning("init base conf fail");
            return false;
        }
        /*$intTotal = 0;
        foreach ($arrData as $value) {
            $intTotal += intval($value['avg_uv']);
        }*/
        $intStartTime = strtotime($this->_arrReqParam['start_time']);
        $intEndTime = strtotime($this->_arrReqParam['end_time']);
        $intDayDelta = ($intEndTime - $intStartTime) / (24 * 3600);
        foreach ($arrData as $value) {
            $strPage = $value['page'];
            $intSumUv = intval($value['sum_uv']);
            $arrTmp = array(
                'name'       => $strPage,
                'count'      => intval($intSumUv / $intDayDelta),
                //'percentage' => $intAvgUv / $intTotal,
            );
            $arrRet[] = $arrTmp;
        }
        return $arrRet;
    }
    
  
    private function _checkParams() {
        if (0 === intval($this->_arrReqParam['start_time']) 
            || 0 === intval($this->_arrReqParam['end_time'])) { 
            Bingo_log::warning('start_time or end_time error!');
            $this->_jsonOutput(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        if ($this->_intForumId <= 0) {
            Bingo_log::warning('forum_id error!');
            $this->_jsonOutput(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        if (!isset($this->_arrReqParam['client_type']) 
            || intval($this->_arrReqParam['client_type']) < 0) {
            $this->_arrReqParam['client_type'] = 0;
        }
        return true;
    }
}

