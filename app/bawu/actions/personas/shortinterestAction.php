<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file shortinterestAction.php
 * <AUTHOR>
 * @date 2015/07/22 14:51:06
 * @brief 
 *  
 **/
 
class shortinterestAction extends Util_Personas_Base {
    protected $_arrReqKeyFields = array('forum_id', 'start_time', 'end_time');
    protected $_arrReqOptFields = array('client_type');
    protected $_strServiceType = 'shortinterestAction';
    
    public function preProcess() {
        return true;
    }
    
    public function process() {
        if (false === $this->_checkParams()) {
            return false;
        }
        for ($intDeltaDay = 0; $intDeltaDay < Util_Def::MAX_DELTA_DAY; $intDeltaDay++) {
            $intStart = strtotime("-$intDeltaDay day", strtotime($this->_arrReqParam['start_time']));
            $intEnd = strtotime("-$intDeltaDay day", strtotime($this->_arrReqParam['end_time']));
            $arrInput = array(
                'forum_id'    => intval($this->_arrReqParam['forum_id']),
                'start_time'  => date('Ymd', $intStart),
                'end_time'    => date('Ymd', $intEnd),
                'client_type' => intval($this->_arrReqParam['client_type']),
            );
            $arrRelatedRes = Tieba_Service::call('personas', 'getRelatedForumInfo', $arrInput);
            if (false === $arrRelatedRes || Tieba_Errcode::ERR_SUCCESS !== $arrRelatedRes['errno']) {
                Bingo_Log::warning('call personas getRelatedForumInfo fail ' . serialize($arrInput) . '_' . serialize($arrRelatedRes));
                $intErr = $arrRelatedRes['errno'];
                if (!isset($arrRelatedRes['errno'])) {
                    $intErr = -1;
                }
               // $this->_jsonOutput($intErr, 'get short interest info fail');
                continue;
            }
            if (empty($arrRelatedRes['data'])) {
                continue;
            }
            $arrData = $this->_getRetData($arrRelatedRes['data']);
            $this->_jsonOutput(Tieba_Errcode::ERR_SUCCESS, 'success', $arrData);
            return true;
        }      
    }
    
    private function _getRetData($arrData) {
        $arrRet = array();
        if (false === self::_init()) {
            Bingo_Log::warning("init base conf fail");
            return false;
        }
        $arrForumId = array();
        foreach ($arrData as $value) {
            $arrForumId[] = intval($value['related_fid']);
        }
        $intIndex = 0;
        $arrBtxInput = array(
            'forum_id' => $arrForumId,
        );
        $arrBtxRes = Tieba_Service::call('forum', 'mgetBtxInfo', $arrBtxInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrBtxRes['errno']) {
            Bingo_Log::warning('call forum mgetBtxInfo fail ' . serialize($arrForumId) . '_' . serialize($arrBtxRes));
            return false;
        }
        $intStartTime = strtotime($this->_arrReqParam['start_time']);
        $intEndTime = strtotime($this->_arrReqParam['end_time']);
        $intDayDelta = ($intEndTime - $intStartTime) / (24 * 3600);
        foreach ($arrData as $value) {
            $intRelatedFid = intval($value['related_fid']);
            $intSumUv = intval($value['sum_uv']);
            $arrTmp = array(
                'index'        => $intIndex++,
                'name'         => $arrBtxRes['output'][$intRelatedFid]['forum_name']['forum_name'],
                'count'        => intval($intSumUv / $intDayDelta),
                'forum_id'     => $intRelatedFid,
                'forum_avatar' => $arrBtxRes['output'][$intRelatedFid]['card']['avatar'],
            );
            $arrRet[] = $arrTmp;
        }
        return $arrRet;
    }
    
    private function _checkParams() {
        if (0 === intval($this->_arrReqParam['start_time']) 
            || 0 === intval($this->_arrReqParam['end_time'])) { 
            Bingo_log::warning('start_time or end_time error!');
            $this->_jsonOutput(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        if ($this->_intForumId <= 0) {
            Bingo_log::warning('forum_id error!');
            $this->_jsonOutput(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        if (!isset($this->_arrReqParam['client_type']) 
            || intval($this->_arrReqParam['client_type']) < 0) {
            $this->_arrReqParam['client_type'] = 0;
        }
        return true;
    }
    
    private function _getOfficialType($intForumId) {
        //get official info
        $arrInput = array (
            'forum_id' => $intForumId,
        );
        $arrOfficialInfoRes = Tieba_Service::call('official', 'getOfficialInfo', $arrInput);
        if (false === $arrOfficialInfoRes || Tieba_Errcode::ERR_SUCCESS !== $arrOfficialInfoRes['errno']) {
            Bingo_Log::warning('call getForumCateByFids fail '.serialize($arrInput).'_'.serialize($arrOfficialInfoRes));
            return false;
        }
        return intval($arrOfficialInfoRes['data']['base']['type']);
    }
}

