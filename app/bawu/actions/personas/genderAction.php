<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file genderAction.php
 * <AUTHOR>
 * @date 2015/07/17 12:49:06
 * @brief 
 *  
 **/
 
class genderAction extends Util_Personas_Base {
    protected $_arrReqKeyFields = array('forum_id', 'start_time', 'end_time');
    protected $_arrReqOptFields = array('client_type');
    protected $_strServiceType = 'genderAction';
    
    public function preProcess() {
        return true;
    }
    
    public function process() {
        if (false === $this->_checkParams()) {
            return false;
        }
        for ($intDeltaDay = 0; $intDeltaDay < Util_Def::MAX_DELTA_DAY; $intDeltaDay++) {
            $intStart = strtotime("-$intDeltaDay day", strtotime($this->_arrReqParam['start_time']));
            $intEnd = strtotime("-$intDeltaDay day", strtotime($this->_arrReqParam['end_time']));
            $arrInput = array(
                'forum_id'    => intval($this->_arrReqParam['forum_id']),
                'start_time'  => date('Ymd', $intStart),
                'end_time'    => date('Ymd', $intEnd),
                'client_type' => intval($this->_arrReqParam['client_type']),
            );
            $arrGenderRes = Tieba_Service::call('personas', 'getGenderInfo', $arrInput);
            if (false === $arrGenderRes || Tieba_Errcode::ERR_SUCCESS !== $arrGenderRes['errno']) {
                Bingo_Log::warning('call personas getGenderInfo fail ' . serialize($arrInput) . '_' . serialize($arrGenderRes));
                $intErr = $arrGenderRes['errno'];
                if (!isset($arrGenderRes['errno'])) {
                    $intErr = -1;
                }
                //$this->_jsonOutput($intErr, 'get gender info fail');
                continue;
            }
            if (empty($arrGenderRes['data'])) {
                continue;
            }
            $arrData = $this->_getRetData($arrGenderRes['data']);
            $this->_jsonOutput(Tieba_Errcode::ERR_SUCCESS, 'success', $arrData);
            return true;
        }
        
    }
    
    private function _getRetData($arrData) {
        $arrRet = array();
        if (false === self::_init()) {
            Bingo_Log::warning("init base conf fail");
            return false;
        }
        $intStartTime = strtotime($this->_arrReqParam['start_time']);
        $intEndTime = strtotime($this->_arrReqParam['end_time']);
        $intDayDelta = ($intEndTime - $intStartTime) / (24 * 3600);
        /*$intTotal = 0;
        foreach ($arrData as $value) {
            $intTotal += intval($value['avg_uv']);
        }*/
        foreach ($arrData as $value) {
            $intGender = intval($value['gender']);
            $intSumUv = intval($value['sum_uv']);
            $strGender = Bingo_Encode::convert(self::$_conf['gender_type_map'][$intGender]['type'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
            $arrTmp = array(
                'name'       => $strGender,
                'count'      => intval($intSumUv / $intDayDelta),
               // 'percentage' => $intAvgUv / $intTotal,
            );
            $arrRet[] = $arrTmp;
        }
        return $arrRet;
    }
    
    /**
     * @brief 
     * @param 
     * @return boolean 
     */
    /*private function _initParam() {
        $this->_intStartTime = intval(Bingo_Http_Request::get('start_time', 0));
        $this->_intEndTime = intval(Bingo_Http_Request::get('end_time', 0));
        $this->_intClientType = intval(Bingo_Http_Request::get('client', 0));
        $this->_intForumId    = intval(Bingo_Http_Request::get('forum_id', 0));
        return true;
    }*/
    private function _checkParams() {
        if (0 === intval($this->_arrReqParam['start_time']) 
            || 0 === intval($this->_arrReqParam['end_time'])) { 
            Bingo_log::warning('start_time or end_time error!');
            $this->_jsonOutput(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        if ($this->_intForumId <= 0) {
            Bingo_log::warning('forum_id error!');
            $this->_jsonOutput(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        if (!isset($this->_arrReqParam['client_type']) 
            || intval($this->_arrReqParam['client_type']) < 0) {
            $this->_arrReqParam['client_type'] = 0;
        }
        return true;
    }
    
    private function _getOfficialType($intForumId) {
        //get official info
        $arrInput = array (
            'forum_id' => $intForumId,
        );
        $arrOfficialInfoRes = Tieba_Service::call('official', 'getOfficialInfo', $arrInput);
        if (false === $arrOfficialInfoRes || Tieba_Errcode::ERR_SUCCESS !== $arrOfficialInfoRes['errno']) {
            Bingo_Log::warning('call getForumCateByFids fail '.serialize($arrInput).'_'.serialize($arrOfficialInfoRes));
            return false;
        }
        return intval($arrOfficialInfoRes['data']['base']['type']);
    }
}

