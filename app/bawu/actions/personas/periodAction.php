<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file periodAction.php
 * <AUTHOR>
 * @date 2015/07/21 16:46:06
 * @brief 
 *  
 **/
 
class periodAction extends Util_Personas_Base {
    protected $_arrReqKeyFields = array('forum_id', 'start_time', 'end_time');
    protected $_arrReqOptFields = array('client_type');
    protected $_strServiceType = 'periodAction';
    
    public function preProcess() {
        return true;
    }
    
    public function process() {
        if (false === $this->_checkParams()) {
            return false;
        }
        for ($intDeltaDay = 0; $intDeltaDay < Util_Def::MAX_DELTA_DAY; $intDeltaDay++) {
            $intStart = strtotime("-$intDeltaDay day", strtotime($this->_arrReqParam['start_time']));
            $intEnd = strtotime("-$intDeltaDay day", strtotime($this->_arrReqParam['end_time']));
            $arrInput = array(
                'forum_id'    => intval($this->_arrReqParam['forum_id']),
                'start_time'  => date('Ymd', $intStart),
                'end_time'    => date('Ymd', $intEnd),
                'client_type' => intval($this->_arrReqParam['client_type']),
            );
            $arrPeriodRes = Tieba_Service::call('personas', 'getPeriodInfo', $arrInput);
            if (false === $arrPeriodRes || Tieba_Errcode::ERR_SUCCESS !== $arrPeriodRes['errno']) {
                Bingo_Log::warning('call personas getPeriodInfo fail ' . serialize($arrInput) . '_' . serialize($arrPeriodRes));
                $intErr = $arrPeriodRes['errno'];
                if (!isset($arrPeriodRes['errno'])) {
                    $intErr = -1;
                }
               // $this->_jsonOutput($intErr, 'get period info fail');
                continue;
            }
            if (empty($arrPeriodRes['data'])) {
                continue;
            }
            $arrData = $this->_getRetData($arrPeriodRes['data']);
            $this->_jsonOutput(Tieba_Errcode::ERR_SUCCESS, 'success', $arrData);
            return true;
        }
        
    }
    
    private function _getRetData($arrData) {
        $arrRet = array();
        if (false === self::_init()) {
            Bingo_Log::warning("init base conf fail");
            return false;
        }
        $intStartTime = strtotime($this->_arrReqParam['start_time']);
        $intEndTime = strtotime($this->_arrReqParam['end_time']);
        $intDayDelta = ($intEndTime - $intStartTime) / (24 * 3600);
        $arrTmpData = array();
        foreach ($arrData as $value) {
            $strClientType = $value['client_type'];
            $strClientType = Bingo_Encode::convert(self::$_conf['client_type_map'][$strClientType]['type'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
            $intSumUv = intval($value['sum_uv']);
            $strHour = $value['hour'];
            //$arrTmpData[$strClientType][$strHour] = $intSumUv / $intDayDelta; 
            $arrTmpData[$strClientType][] = intval($intSumUv / $intDayDelta); 
        }
        
        foreach ($arrTmpData as $key => $value) {
            $arrTmp = array(
                'name' => $key,
                'data' => $value,     
            );
            $arrRet[] = $arrTmp;
        }
        return $arrRet;
    }
    
    private function _checkParams() {
        if (0 === intval($this->_arrReqParam['start_time']) 
            || 0 === intval($this->_arrReqParam['end_time'])) { 
            Bingo_log::warning('start_time or end_time error!');
            $this->_jsonOutput(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        if ($this->_intForumId <= 0) {
            Bingo_log::warning('forum_id error!');
            $this->_jsonOutput(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        if (!isset($this->_arrReqParam['client_type']) 
            || intval($this->_arrReqParam['client_type']) < 0) {
            $this->_arrReqParam['client_type'] = 0;
        }
        return true;
    }
}

