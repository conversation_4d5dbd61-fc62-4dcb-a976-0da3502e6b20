<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file loginAction.php
 * <AUTHOR>
 * @date 2015/07/22 10:05:06
 * @brief 
 *  
 **/
 
class loginAction extends Util_Personas_Base {
    protected $_arrReqKeyFields = array('forum_id', 'start_time', 'end_time');
    protected $_arrReqOptFields = array('client_type');
    protected $_strServiceType = 'loginAction';
    
    public function preProcess() {
        return true;
    }
    
    public function process() {
        if (false === $this->_checkParams()) {
            return false;
        }
        for ($intDeltaDay = 0; $intDeltaDay < Util_Def::MAX_DELTA_DAY; $intDeltaDay++) {
            $intStart = strtotime("-$intDeltaDay day", strtotime($this->_arrReqParam['start_time']));
            $intEnd = strtotime("-$intDeltaDay day", strtotime($this->_arrReqParam['end_time']));
            $arrInput = array(
                'forum_id'    => intval($this->_arrReqParam['forum_id']),
                'start_time'  => date('Ymd', $intStart),
                'end_time'    => date('Ymd', $intEnd),
                'client_type' => intval($this->_arrReqParam['client_type']),
            );
            $arrLoginIRes = Tieba_Service::call('personas', 'getLoginInfo', $arrInput);
            if (false === $arrLoginIRes || Tieba_Errcode::ERR_SUCCESS !== $arrLoginIRes['errno']) {
                Bingo_Log::warning('call personas getLoginInfo fail ' . serialize($arrInput) . '_' . serialize($arrLoginIRes));
                $intErr = $arrLoginIRes['errno'];
                if (!isset($arrLoginIRes['errno'])) {
                    $intErr = -1;
                }
               // $this->_jsonOutput($intErr, 'get login info fail');
                continue;
            }
            if (empty($arrLoginIRes['data']['login']) || empty($arrLoginIRes['data']['logged_in'])) {
                continue;
            }
            $arrData = $this->_getRetData($arrLoginIRes['data']);
            $this->_jsonOutput(Tieba_Errcode::ERR_SUCCESS, 'success', $arrData);
            return true;
        }
        
    }
    
    private function _getRetData($arrData) {
        $arrRet = array();
        $arrLoginData = $arrData['login'];
        $arrLoggedInData = $arrData['logged_in'];
        if (false === self::_init()) {
            Bingo_Log::warning("init base conf fail");
            return false;
        }
        $intStartTime = strtotime($this->_arrReqParam['start_time']);
        $intEndTime = strtotime($this->_arrReqParam['end_time']);
        $intDayDelta = ($intEndTime - $intStartTime) / (24 * 3600);
        /*$intLoginTotal = 0;
        foreach ($arrLoginData as $value) {
            $intLoginTotal += intval($value['avg_uv']);
        }
        $intLoggedInTotal = 0;
        foreach ($arrLoggedInData as $value) {
            $intLoggedInTotal += intval($value['avg_uv']);
        }*/
        
        foreach ($arrLoginData as $value) {
            $intLoginType = intval($value['login_type']);
            $intSumUv = intval($value['sum_uv']);
            $strLoginType  = Bingo_Encode::convert(self::$_conf['login_type_map'][$intLoginType]['type'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
            $arrTmp = array(
                'name'       => $strLoginType,
                'count'      => intval($intSumUv / $intDayDelta),
                //'percentage' => $intAvgUv / $intTotal,
            );
            $arrRet['all'][] = $arrTmp;
        }
        foreach ($arrLoggedInData as $value) {
            $intFansType = intval($value['fans_type']);
            $intSumUv = intval($value['sum_uv']);
            $strFansType  = Bingo_Encode::convert(self::$_conf['fans_type_map'][$intFansType]['type'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
            $arrTmp = array(
                'name'       => $strFansType,
                'count'      => intval($intSumUv / $intDayDelta),
                //'percentage' => $intAvgUv / $intTotal,
            );
            $arrRet['loggedin'][] = $arrTmp;
        }
        return $arrRet;
    }
    
    /**
     * @brief 
     * @param 
     * @return boolean 
     */
    /*private function _initParam() {
        $this->_intStartTime = intval(Bingo_Http_Request::get('start_time', 0));
        $this->_intEndTime = intval(Bingo_Http_Request::get('end_time', 0));
        $this->_intClientType = intval(Bingo_Http_Request::get('client', 0));
        $this->_intForumId    = intval(Bingo_Http_Request::get('forum_id', 0));
        return true;
    }*/
    private function _checkParams() {
        if (0 === intval($this->_arrReqParam['start_time']) 
            || 0 === intval($this->_arrReqParam['end_time'])) { 
            Bingo_log::warning('start_time or end_time error!');
            $this->_jsonOutput(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        if ($this->_intForumId <= 0) {
            Bingo_log::warning('forum_id error!');
            $this->_jsonOutput(Tieba_Errcode::ERR_PARAM_ERROR);
            return false;
        }
        if (!isset($this->_arrReqParam['client_type']) 
            || intval($this->_arrReqParam['client_type']) < 0) {
            $this->_arrReqParam['client_type'] = 0;
        }
        return true;
    }
    
    private function _getOfficialType($intForumId) {
        //get official info
        $arrInput = array (
            'forum_id' => $intForumId,
        );
        $arrOfficialInfoRes = Tieba_Service::call('official', 'getOfficialInfo', $arrInput);
        if (false === $arrOfficialInfoRes || Tieba_Errcode::ERR_SUCCESS !== $arrOfficialInfoRes['errno']) {
            Bingo_Log::warning('call getForumCateByFids fail '.serialize($arrInput).'_'.serialize($arrOfficialInfoRes));
            return false;
        }
        return intval($arrOfficialInfoRes['data']['base']['type']);
    }
}

