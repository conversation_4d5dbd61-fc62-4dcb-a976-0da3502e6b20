<?php
/* 
 *@brief ��������
 *<AUTHOR>
 */
class prizeEditAction extends Actions_Fortune_Base {
    const SHORT_DAY = 7;
    const LONG_DAY  = 30;
    const K_LINE_DAY = 14;
    const DATA_INFO_MOD = 500;
    //���봫�ؼ���
    protected $arrKeyFields = array('word');
    protected $strServiceType = "prizeEdit";
    //ģ�����
    protected $strTemplate = "platform_bless_set.php";

    public function process() {
        
    	$prize_id = 	Bingo_Http_Request::get("prize_id",0);
    	if($prize_id)
    	{
    	  $inputdata = array(
            'prize_id' => $prize_id,
      	  );
   		  $fortune = Tieba_Service::call('fortune', 'bawugetPrizeDetail', $inputdata);
   		  if ($fortune == false || $fortune['errno'] !== 0) {
                Bingo_Page::assign('errno', -1);
                Bingo_Page::assign('errmsg', 'get_prize_fail!');
                Bingo_Page::setOnlyDataType("json");
   		      Bingo_Log::fatal('fortune get prize info error. call fortune::bawugetPrizeDetail failed');
   		      return false;
   		  }
   		  if($this->intFid != $fortune['data']['forum_id'])
   		  {
   			throw new Exception('no perm!');
		  }
   		  
    	}
    	
        if(Bingo_Http_Request::get("test", false)) {
            var_dump($fortune);
        }

        //��ģ�渳ֵ
         $platformLight = $bawuIndexData['row']['light'];
        $arrForumInput = array(
            'forum_id'         => $this->intFid,
            'forum_name'       => $this->strFname,
        );
        //���ݹٷ��ɱ�־
        Page_Platform_Forum::build($arrForumInput);
        Page_Postaudit_User::build();
        Page_Platform_Perm::build($this->strPermFlag);
        
        Bingo_Page::assign('fortune', $fortune);
    }
}
