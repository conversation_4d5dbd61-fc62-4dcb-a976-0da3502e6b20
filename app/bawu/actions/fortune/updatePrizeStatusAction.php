<?php
/* 
 *@brief �����޸��ύ
 *<AUTHOR>
 */
class updatePrizeStatusAction extends Actions_Fortune_Base {
    
    //���봫�ؼ���
    protected $arrKeyFields = array('word');
    protected $strServiceType = "updatePrizeStatus";
   
    public function process() {
        
    	$status = 	Bingo_Http_Request::get("status",'');
		$prize_id = $id = 	Bingo_Http_Request::get("prize_id",0);
    	$tbs = 	Bingo_Http_Request::get("tbs",'');
    	if(false === Tieba_Tbs::check($tbs, true)) {
			Bingo_Log::warning('Tbs check fail.'.$tbs);
			Bingo_Page::assign('errno', -1);
			Bingo_Page::assign('errmsg', 'tbs check fail');
			Bingo_Page::setOnlyDataType("json");
			return false;
		}
    	if($prize_id)
    	{
    	  $inputdata = array(
            'prize_id' => $prize_id,
      	  );
   		  $fortune = Tieba_Service::call('fortune', 'bawugetPrizeDetail', $inputdata);
   		  if ($fortune == false || $fortune['errno'] !== 0) {
                Bingo_Page::assign('errno', -1);
                Bingo_Page::assign('errmsg', 'get_prize_fail!');
                Bingo_Page::setOnlyDataType("json");
   		      Bingo_Log::fatal('fortune update prize status error. call fortune::bawugetPrizeDetail failed');
   		      return false;
   		  }
   		  if($this->intFid != $fortune['data']['forum_id'])
   		  {
   			throw new Exception('no perm!');
		  }
   		  
    	}
		else
		{
			throw new Exception('param error!');
		
		}
		
        $inputdata = array(
            'id'       => $id,
            'status'   => $status,
            'forum_id' => $this->intFid,
        );
   		$fortune = Tieba_Service::call('fortune', 'updatePrizeStatus', $inputdata);
		if ($fortune == false || $fortune['errno'] !== 0) {
			Bingo_Page::assign('errno', -1);
			Bingo_Page::assign('errmsg', 'update_status_fail!');
			Bingo_Page::setOnlyDataType("json");
			Bingo_Log::fatal('fortune update prize status error. call fortune::updatePrizeStatus failed');
			return false;
		}
   		Bingo_Page::assign('errno', $fortune['errno']);
		Bingo_Page::assign('errmsg', $fortune['errmsg']);
		Bingo_Page::setOnlyDataType("json");   
   		
    }
}
