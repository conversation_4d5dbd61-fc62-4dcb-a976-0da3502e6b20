<?php
/* 
 *@brief ��������
 *<AUTHOR>
 */
class prizeListAction extends Actions_Fortune_Base {
    const SHORT_DAY = 7;
    const LONG_DAY  = 30;
    const K_LINE_DAY = 14;
    const DATA_INFO_MOD = 500;
    //���봫�ؼ���
    protected $arrKeyFields = array('word');
    protected $strServiceType = "przeList";
    //ģ�����
    protected $strTemplate = "platform_bless_list.php";

    public function process() {
        
    	$pn = 	Bingo_Http_Request::get("pn",1);
    	$pcount = 	Bingo_Http_Request::get("pcount",20);
       $inputdata = array(
            'forum_id' => $this->intFid,
       		'pn'	=> $pn,
       		'pcount'	=> $pcount
        );
   		$fortune = Tieba_Service::call('fortune', 'bawuPrizeList', $inputdata);
   		  if ($fortune == false || $fortune['errno'] !== 0) {
                Bingo_Page::assign('errno', -1);
                Bingo_Page::assign('errmsg', 'get_prize_list_fail!');
                Bingo_Page::setOnlyDataType("json");
   		      Bingo_Log::fatal('fortune get prize list error. call fortune::bawuPrizeList failed');
   		      return false;
   		  }
		foreach($fortune['data'] as $key => $value)
		{
			if($fortune['data'][$key]['start_time'])$fortune['data'][$key]['start_time'] = date("Y-m-d H",$fortune['data'][$key]['start_time']);
			if($fortune['data'][$key]['end_time'])
			{
				$fortune['data'][$key]['end_time'] = date("Y-m-d H",$fortune['data'][$key]['end_time']);
			}
			if($fortune['data'][$key]['create_time'])$fortune['data'][$key]['create_time'] = date("Y-m-d H",$fortune['data'][$key]['create_time']);
			
		}
        //���ݹٷ��ɱ�־
        Page_Platform_Forum::build($arrForumInput);
        Page_Postaudit_User::build();
        Page_Platform_Perm::build($this->strPermFlag);
        //��ģ�渳ֵ
        if(Bingo_Http_Request::get("test", false)) {
            var_dump($fortune);
        }

    	$platformLight = $bawuIndexData['row']['light'];
        $arrForumInput = array(
            'forum_id'         => $this->intFid,
            'forum_name'       => $this->strFname,
        );
        //���ݹٷ��ɱ�־
        Page_Platform_Forum::build($arrForumInput);
        Page_Postaudit_User::build();
        Page_Platform_Perm::build($this->strPermFlag);
        
        
        Bingo_Page::assign('fortune', $fortune);
        
        //Bingo_Page::setOnlyDataType("json"); 
    }
}
