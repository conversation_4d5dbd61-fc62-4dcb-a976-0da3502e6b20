<?php
/*
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-01-17 19:25:58
 * @version
 */
class getIpTbsAction extends Bingo_Action_Abstract {
	private static $_privateKey = 'baiduTieBa123&*($';
	/**
	 * [execute description]
	 * @param
	 * @return [type] [description]
	 */
    public function execute(){
    	$strTbsBanUser = self::encode(Tieba_Cmd::FilterForumUser);
        $arrRet = array(
            'tbs_ban_user' => $strTbsBanUser,
        	'ip' => '*.*.*.*', 
            'ip_int' => '',
            'tbs_ban_ip' => '',
            'is_phone_ip' => false, 
            'ip_secure_str' => '',
        );
        $strJson = Bingo_String::array2json($arrRet);
        echo $strJson;
        return true;
    }
    /**
     * [encode description]
     * @param  [type]  $cm  [description]
     * @param  integer $tid [description]
     * @param  integer $pid [description]
     * @return [type]       [description]
     */
    public static function encode($cm,$tid=0,$pid=0){
        $uid   = Tieba_Session_Socket::getLoginUid();
        $word  = Bingo_Http_Request::get('word','');
        $uname = Tieba_Session_Socket::getLoginUname();
        $fid   = (int)Bingo_Http_Request::get('fid',0);
        $str = sprintf('cm=%d&word=%s&fid=%d&uname=%s&uid=%d&tid=%d&pid=%d&key=%s',$cm,$word,$fid,$uname,$uid,$tid,$pid,self::$_privateKey);
        $strMd5 = md5($str);
        return $strMd5;
    }

}
?>