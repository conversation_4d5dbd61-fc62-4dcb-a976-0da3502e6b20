<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-05-24 15:26:58
 * @version
 */
 
class Actions_Actstage_Base extends Bingo_Action_Abstract {
/*
    public function init(){
    }
 */  
    protected $intErrno = 0;
    protected $strErrmsg = '';
    protected $intActivityId = 0;
    protected $strFname;
    protected $intFid;
    protected $arrUserInfo = array();
    protected $arrForumInfo = array();
    protected $intIp;
    protected $intIp6;
    protected $arrPerm = array();
    protected $arrReqInfo =array();
    protected $intPmFlag;
    protected $arrKeyFields = array();
    protected $arrOptFields = array();
    protected $arrNeedPerms = array('can_op_as_4thmgr');
    protected $strServiceType;
    protected $strTemplate;
    protected $strPermFlag;
//    protected $arrStrategy;

    public function execute(){
        //check and get url
        $this->arrReqInfo = $this->_getReqParam();
        if(false === $this->arrReqInfo) {
            Bingo_Log::warning('Check params fail.');
            $this->_jumpPage();
            return false;
        }

        if(!empty($this->arrReqInfo['word'])) {
            $this->strFname = $this->arrReqInfo['word'];
            $this->arrForumInfo = Util_Forum::getForumInfo($this->strFname);
            if(false === $this->arrForumInfo) {
                Bingo_Log::warning("Can't get forum info.Input[fname:".$this->strFname."]");
                $this->_jumpPage();
                return false;
            }
            $this->intFid = $this->arrForumInfo['forum_id']['forum_id'];
        } else {
            Bingo_Log::warning('');
            $this->_jumpPage();
            return false;
        }
                
        //get user info
        $this->arrUserInfo = Util_User::getUserInfo();
        if(false === $this->arrUserInfo) {
            Bingo_Log::warning("Can't get user info.");
            $this->_jumpPage();
            return false;
        }

        //get perm
        $ipArr = Bingo_Http_Ip::getConnectIpExt();
        $this->intIp = isset($ipArr['type']) && $ipArr['type'] == 'IPv4' ? intval(Bingo_Http_Ip::newip2long($ipArr['ip'])) : 0;
        $this->intIp6 = isset($ipArr['type']) && $ipArr['type'] == 'IPv6' ? $ipArr['ip'] : '';
        $arrInput = array(
            "forum_id" => $this->intFid,
            "user_id" => $this->arrUserInfo['id'],
            "user_ip" => $this->intIp,
            "user_ip6" => $this->intIp6
        );
        $this->arrPerm = Util_Perm::getUserPerm($arrInput);
        if(false === $this->arrPerm) {
            Bingo_Log::warning("Can't get user perm.Input[".serialize($arrInput)."]");
            $this->_jumpPage();
            return false;
        }
        //check perm
        $bolPass = $this->_checkPerm($this->arrPerm);
        if(false === $bolPass) {
            Bingo_Log::warning("Check perm fail.");
            $this->_jumpPage();
            return false;
        }
        if (Util_Forum::isMicroGconForum($this->intFid)) { //xiao wei ba return
            Bingo_Log::warning("Check isMicroGconForum fail.");
            $this->_jumpPage();
            return false;
        }
        //process main logic
        $bolSuc = $this->process();
        if(false === $bolSuc) {
            Bingo_Log::warning('Main process fail.');
        }
        //set template
        if(!empty($this->strTemplate)) {
            Bingo_Page::setTpl($this->strTemplate);
        }

        //build log
        $this->_buildLog();
        return $bolSuc;
    }

    private function _getReqParam() {
        $arrRet = array();
        foreach($this->arrKeyFields as $each) {
            $val = Bingo_Http_Request::get($each);
            if(is_null($val)) {
                Bingo_Log::warning('Empty Key Field:'.$each);
                return false;
            }
            $arrRet[$each] = $val;
        }
        foreach($this->arrOptFields as $each) {
            if(is_array($each)) {
                $val = Bingo_Http_Request::get(key($each));
                if(!is_null($val)) {
                     $arrRet[key($each)] = $val;
                } else {
                    $arrRet[key($each)] = $each[key($each)];
                }
                continue;
            }
            $val = Bingo_Http_Request::get($each);
            if(!is_null($val)) {
                $arrRet[$each] = $val;
            }
        }
        $arrRet = Bingo_String::xssDecode($arrRet);
//        foreach($this->arrFieldsCheck as $key=>$value) {
//            if(array_key_exists($key, $arrRet)) {
//                if(is_array($value)) {
//                    if(!in_array($arrRet[$key], $value)) {
//                        Bingo_Log::warning('Field Check Fail:'.$key."=>".$value);
//                        return false;
//                    }
//                }
//            }
//        }
        return $arrRet;
    }

    private function _jumpPage() {
        header("Location:http://tieba.baidu.com/f?kw=".$this->strFname);
        return;
    }
    private function _checkPerm($arrPerm) {
        $bolRet = false;
        $this->intPmFlag = 1;
        foreach($this->arrNeedPerms as $each) {
            if(isset($arrPerm[$each]) && true === $arrPerm[$each]) {
                $bolRet = true;
                $this->strPermFlag = $each;
                if(isset($this->strPmPerm) && true === $arrPerm[$this->strPmPerm]) {
                    $this->intPmFlag = 0;
                    break;
                }
            }
        }
        return $bolRet;
    }
    private function _buildLog() {
        Tieba_Stlog::addNode('service_type', $this->strServiceType);
        Tieba_Stlog::addNode('forum_id', $this->intFid);
        Tieba_Stlog::addNode('forum_name', $this->strFname);
        Tieba_Stlog::addNode('op_id', $this->arrUserInfo['id']);
        Tieba_Stlog::addNode('op_name', $this->arrUserInfo['name']);
                foreach($this->arrReqInfo as $key=>$value) {
                        Tieba_Stlog::addNode(strval($key), $value);
                }
    }
}
?>
