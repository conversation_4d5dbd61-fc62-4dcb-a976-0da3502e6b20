<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-11-19 14:46:58
 * @version
 */


class actAddAction extends Actions_Actstage_Base  {
	
	protected $arrKeyFields = array('word','step');
    protected $arrOptFields = array('activity_id','type','tbs','forum_id');
    protected $strServiceType = 'actAdd';
    protected $strTemplate = "activity/activity_add.php";
    protected $activityNum = 0;
    const activity_title_lenght  = 60;   //�������ֽڳ���
    const activity_desc_lenght = 400;  //������ĳ���
    const activity_icon_lenght   = 128;  //�icon��ַ���ֽڳ���
     
    const award_name_lenght = 40;       //��Ʒ�����ֽڳ���
    const award_desc_lenght = 200;      //��Ʒ�����ֽڳ���
    const award_cover_lenght = 128;     //��Ʒ�����ֽڳ���
    const virtul_award_max_line = 10000; //���⽱Ʒ��Ʒ����������
    
    const STATUS_IN_CREATION = 5;     //�����е�status״̬
    const STATUS_NOT_PASS = 4;        //��˲�ͨ����״̬
    const IN_CREATION_NUM = 50;       //�����еĻ����

    public function process(){
    	$this->intActivityId = intval($this->arrReqInfo['activity_id']);
    	if (1 === intval($this->arrReqInfo['step'])){
    		$this->createActivity();
    		return true;
    	}
    	//�ڶ���֮��activity_id������
    	if(0 <= $this->intActivityId){
	    	if (2 === intval($this->arrReqInfo['step'])){
	            $this->editRule();
	        }
	        elseif (3 === intval($this->arrReqInfo['step'])){
	            $this->preview();
	        }else {
	            return false;
	        }
	        return true;
    	}
    	else{
    		$this->build('tpl');
    		return true;
    	}
    }
    //��һ���������ҳ��
    public function createActivity(){
    	//�ύ����
    	if('json' === (strval($this->arrReqInfo['type']))){
    		    if($this->checkIncreateNum()){
    		    	$this->addActivity();
    		    }
	    		else{	
	    			$this->setErroInfo(Tieba_Errcode::ERR_ACTSTAGE_NUM_LIMIT,'create_num_is:'.$this->activityNum);
	    		}
    			$this->build('json');
    	}
    	
    	//����������״ν��룬���ߵ����һ�������ҳ��
    	else{
    		if($this->checkIncreateNum()){
    			if(0 < $this->intActivityId){
                   $this->getActivityInfo();
    			}
    		}
    		else{
    			$this->setErroInfo(Tieba_Errcode::ERR_ACTSTAGE_NUM_LIMIT,'create_num_is:'.$this->activityNum);
    		}
    		 	
    		$this->build('tpl');	
    	}
    }
    
    //�ڶ��������ý�Ʒ����ҳ��
    public function editRule(){
        //�ύ����
        if('json' === (strval($this->arrReqInfo['type']))){  
            if($this->checkIncreateNum()){
                $this->editAward(); 
            }
            else{
            	$this->setErroInfo(Tieba_Errcode::ERR_ACTSTAGE_NUM_LIMIT,'create_num_is:'.$this->activityNum);
            }
            $this->build('json');  
        }
        //����������״ν��룬���ߵ����һ�������ҳ��
        else{
        	$this->getActivityInfo();
           //�����ݿ�ȡ���ݣ����todo
            $this->build('tpl');          
        }
    }
    
    public function addActivity(){
    	$arr_act_info   = Bingo_Http_Request::get('act_info');
        $arr_act_info = Bingo_String::xssDecode($arr_act_info);
        if('' == $arr_act_info || empty($arr_act_info))
        {
        	$this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param act_info invalid['.serialize($arr_act_info).']');
            return false;
        }
        if(false === $this-> checkStep1Param($arr_act_info)){
            return false;
        }
        
        $arrParam['act_info'] = $arr_act_info;
        $arrInputs= $arrParam;
        $arrInputs['act_info']['create_user_id']   = intval($this->arrUserInfo['id']);
        $arrInputs['act_info']['create_user_name'] = $this->arrUserInfo['name'];
        $arrInputs['act_info']['create_user_ip']   = $this->intIp;
        $arrInputs['act_info']['status'] = self::STATUS_IN_CREATION;
        
        
        //�����жϲ�����
        if(0 < $this->intActivityId){
        	$arrInputs['act_info']['activity_id'] = $this->intActivityId;
        	$arrInput['cmActInfo'] = $arrInputs;
            $arrData = Tieba_Service::call('actstage','cmUpdateOnlyAct',$arrInput);
        }
        if(0 === $this->intActivityId){   	
        	$arrInput['cmActInfo'] = $arrInputs;
        	$arrData = Tieba_Service::call('actstage','cmInsertActivity',$arrInput);
        }
       
        $intErrNo = (isset($arrOut['errno'])) ? $arrOut['errno'] : -1;
        if($arrData === false)
        {
            $this->setErroInfo($intErrNo,'Tieba_Service_call_actstage_false['.$arrData.']');
            return false;
        }
        if(empty($arrData) || Tieba_Errcode::ERR_SUCCESS != $arrData['errno'])
        {
            $this->setErroInfo($intErrNo,'Tieba_Service_call_actstage_no_not_zero['.serialize($arrData).']');
            return false;
        }
        $this->intActivityId = $arrData['data']['activity_id'];
        $this->setErroInfo(Tieba_Errcode::ERR_SUCCESS,'insert_activity_success['.serialize($arrData).']');
        return true;
        
    }
    
    public function editAward(){
    	$arrParam = array();
        $arr_award_info = Bingo_Http_Request::get('award_info');
        $arr_award_rule = Bingo_Http_Request::get('award_rule');
        $arr_award_info = Bingo_String::xssDecode($arr_award_info);
        $arr_award_rule = Bingo_String::xssDecode($arr_award_rule);
//        if('' == $arr_award_info || empty($arr_award_info))
//        {
//            $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param award_info invalid['.serialize($arr_award_rule).']');
//            return false;
//        }
        if(false === $this->checkStep2Param($arr_award_info,$arr_award_rule)){
            return false;
        }
        
        $arrParam['award_rule'] = $arr_award_rule;
        $arrParam['award_info'] = $arr_award_info;
        if((!isset($arrParam['award_rule'][3]['chance_num'])) || (0 >= $arrParam['award_rule'][3]['chance_num'])){
        	$arrParam['award_rule'][3]['chance_num'] = 1000;  //ÿ��Ĭ�ϳ齱����Ϊ1000
        }
        // diff award info
        $arrInput = array(
            'activity_id' => $this->intActivityId,
        );
        $arrOut = Tieba_Service::call('actstage', 'bwActivityById', $arrInput);
        if ($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS)
        {
            $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param award_info invalid['.serialize($arrOut).']');
            return false;
        }
        $arrExtra = unserialize($arrOut['data']['list'][0]['act_info']['activity_extra']);
        $intTycheId = (int)$arrExtra['award_act_id'];
        $arrInput = array(
            'award_act_id' => $intTycheId,
        );
        $arrOut = Tieba_Service::call('tyche', 'getAwardAct', $arrInput);
        if ($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS)
        {
            $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param award_info invalid['.serialize($arrOut).']');
            return false;
        }
        $arrAwardInfo = $arrOut['data']['award_info'];
        $intAwardNum = count($arrAwardInfo);
        $intId = 1;
        foreach ($arr_award_info as $intKey => $arrAward)
        {
            if (isset($arrAward['award_id']) && $arrAward['award_type'] == 2)
            {
                $arr_award_info[$intKey]['exkey_act_id'] = $arrAwardInfo[$arrAward['award_id']-1]['ext_info']['exkey_act_id'];
                $arr_award_info[$intKey]['award_count'] = $arrAward['award_num'];
            }
            $arr_award_info[$intKey]['award_id'] = $intId++;
        }
        $arrParam['award_info'] = $arr_award_info;

        // pass to service
        $arrInput = array(
	        'activity_id'=>$this->intActivityId,
            'forum_id' => $this->intFid,
	        'user_id'=>$this->arrUserInfo['id'],
	        'user_name'=>$this->arrUserInfo['name'],
	        'user_ip'=>$this->intIp,
	        'award_info'=>$arrParam['award_info'],
            'activity_award_num' => intval($arrParam['award_rule'][3]['chance_num']),
	        'activity_rule'=>$arrParam['award_rule'],
        );
        
        $arrData = Tieba_Service::call('actstage','updateActivityByBawu',$arrInput);
        $intErrNo = (isset($arrOut['errno'])) ? $arrOut['errno'] : -1;
        if($arrData === false)
        {
            $this->setErroInfo($intErrNo,'Tieba_Service_call_actstage_cmUpdateAcitivity_false['.$arrData.']');
            return false;
        }
        if(empty($arrData) || Tieba_Errcode::ERR_SUCCESS != $arrData['errno'])
        {
            $this->setErroInfo($intErrNo,'Tieba_Service_call_actstage_cmUpdateAcitivity_no_not_zero['.serialize($arrData).']');
            return false;
        }
        $this->setErroInfo(Tieba_Errcode::ERR_SUCCESS,'update_activity_success output['.serialize($arrData).']input['.serialize($arrData).']');
        return true;
    }
    
    public function preview(){
        $this->getActivityInfo();
    	$this->build('tpl');
    }
    
    public function setErroInfo($errno=-1,$errmsg='',$data=array())
    {
        $this->intErrno  = $errno;
        $this->strErrmsg  = $errmsg;
        Bingo_Log::debug('errno:'.$this->intErrno.' errmsg:'.$this->strErrmsg.' data:'.serialize($data));
        return;
    }
    public function build($type){
    	//����
    	if('tpl' === $type){
    		$arrForumInput = array(
              'forum_id' => $this->intFid,
              'forum_name' => $this->strFname,
    		  'step' => $this->arrReqInfo['step'],
              'activity_id' => $this->intActivityId,
    		  'data' => $this->arrData,
    		  'errno' => $this->intErrno,
	        );
	        Page_Platform_Forum::build($arrForumInput);
	        Page_Postaudit_User::build();
	        $arrTeamInput = array('forum_id' => $this->intFid,);
	        Page_Platform_Team::build($arrTeamInput);
    	}
    	if('json' === $type){
    		Bingo_Page::assign('errno', $this->intErrno);
            Bingo_Page::assign('errmsg', $this->strErrmsg);
            $arrData = array(
                'forum_id' => $this->intFid,
                'forum_name' => $this->strFname,
                'step' => intval($this->arrReqInfo['step']),
                'activity_id' => $this->intActivityId,
            );
            Bingo_Page::assign('data',$arrData);
            Bingo_Page::setOnlyDataType("json");
    	}
	    
    }
    
    //ȡԤ������
    public function getActivityInfo(){
    	$arrInput = array(
    	   'activity_id' => $this->intActivityId,
    	);
    	$arrData = Tieba_Service::call('actstage','bwActivityById',$arrInput);
    	$intErrNo = (isset($arrOut['errno'])) ? $arrOut['errno'] : -1;
        if($arrData === false)
        {
            $this->setErroInfo($intErrNo,'Tieba_Service_call_actstage_bwActivityById_false['.$arrData.']');
            return false;
        }
        if(empty($arrData) || Tieba_Errcode::ERR_SUCCESS != $arrData['errno'])
        {
            $this->setErroInfo($intErrNo,'Tieba_Service_call_actstage_cmUpdateAcitivityInfo_no_not_zero['.serialize($arrData).']');
            return false;
        }
        //ֻ�ܱ༭���ɴ����еĻ����ֹ�Ƿ�����
        if((self::STATUS_IN_CREATION != $arrData['data']['list'][0]['act_info']['status'] &&
           self::STATUS_NOT_PASS != $arrData['data']['list'][0]['act_info']['status'])||
           $this->intFid != $arrData['data']['list'][0]['act_info']['forum_id']){
        	$this->setErroInfo(Tieba_Errcode::ERR_UNKOWN, 'illegality op, status is :'.$arrData['data']['list'][0]['act_info']['status']);
        	return false;
        }
        $this->arrData = $arrData['data'];
    }
    
    //�жϴ����еĻ�����Ƿ񳬹�����
    public function checkIncreateNum(){
    	$arrInput = array(
    	   'status' => self::STATUS_IN_CREATION,
    	   'forum_id' => $this->intFid,
    	);
    	$arrData = Tieba_Service::call('actstage','getTotalActivityNum',$arrInput);
    	$intErrNo = (isset($arrOut['errno'])) ? $arrOut['errno'] : -1;
        if($arrData === false)
        {
            $this->setErroInfo($intErrNo,'Tieba_Service_call_actstage_getTotalActivityNum_false['.$arrData.']');
            return false;
        }
        if(empty($arrData) || Tieba_Errcode::ERR_SUCCESS != $arrData['errno'])
        {
            $this->setErroInfo($intErrNo,'Tieba_Service_call_actstage_getTotalActivityNum_no_not_zero['.serialize($arrData).']');
            return false;
        }
        $this->activityNum = $arrData['data']['total'];
        if(self::IN_CREATION_NUM  > $this->activityNum){
        	return true;
        }
        else{
        	return false;
        }
    }
    
    //У��step1�����ݸ�ʽ
    public function checkStep1Param(&$arr_act_info){
    	if(false === Tieba_Tbs::check($this->arrReqInfo['tbs'], true)) {
            $this->setErroInfo(Tieba_Errcode::ERR_TBSIG_CHECK_FAIL,'tbs check fail:'.$this->arrReqInfo['tbs']);
            return false;
    	}

        $arr_act_info['forum_id'] = intval($this->intFid);
        $arr_act_info['forum_name'] = strval($this->strFname);

        if(!isset($arr_act_info['activity_type']) || !is_numeric($arr_act_info['activity_type']))
        {
            $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param activity_type invalid['.$arr_act_info['activity_type'].']');
            return false;
        }
        $arr_act_info['activity_type'] = intval($arr_act_info['activity_type']);
        if(!isset($arr_act_info['activity_title']) || '' == $arr_act_info['activity_title'] || strlen($arr_act_info['activity_title'])>self::activity_title_lenght)
        {
            $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param activity_title invalid['.$arr_act_info['activity_title'].']');
            return false;
        }
       if(!isset($arr_act_info['activity_desc']) || '' == $arr_act_info['activity_desc'] || strlen($arr_act_info['activity_desc'])>self::activity_desc_lenght)
        {
            $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param activity_desc  invalid['.$arr_act_info['activity_desc'].']');
            return false;
        }
        if(!isset($arr_act_info['activity_icon']) || '' == $arr_act_info['activity_icon'] || strlen($arr_act_info['activity_icon'])>self::activity_icon_lenght)
        {
            $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param activity_icon invalid['.$arr_act_info['activity_icon'].']');
            return false;
        }
        
        if(!isset($arr_act_info['begin_time']) || !is_numeric(strtotime($arr_act_info['begin_time'])))
        {
            $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param begin_time invalid['.$arr_act_info['begin_time'].']');
            return false;
        }else
        {
            $arr_act_info['begin_time'] = strtotime($arr_act_info['begin_time']);
        }
        if(!isset($arr_act_info['end_time']) || !is_numeric(strtotime($arr_act_info['end_time'])))
        {
            $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param end_time invalid['.$arr_act_info['end_time'].']');
            return false;
        }else
        {
            $arr_act_info['end_time'] = strtotime($arr_act_info['end_time']);
        }
        return true;
    }
     //У��step2�����ݸ�ʽ
    public function checkStep2Param(&$arr_award_info,&$arr_award_rule){
        if(false === Tieba_Tbs::check($this->arrReqInfo['tbs'], true)) {
            $this->setErroInfo(Tieba_Errcode::ERR_TBSIG_CHECK_FAIL,'tbs check fail:'.$this->arrReqInfo['tbs']);
            return false;   
        }
        if(!empty($arr_award_info)){
        	//�ܽ�Ʒ�����ܳ���7. by xuruiqi
        	if (count($arr_award_info) > 15) {
	            $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'Num of awards exceeds 15, too large.');
        		return false;
        	}
        	
        	$index = 1;
        	//$intRealAwardNum = 0;
        	$intVirtualAwardNum = 0;
	        foreach($arr_award_info as $key=> $value )
	        {
	            if(!isset($value['award_name']) || '' == $value['award_name'] || strlen($value['award_name'])>self::award_name_lenght)
	            {
	                $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param award_name invalid key['.$key.']value['.$value['award_name'].']');
	                return false;
	            }
	            if(!isset($value['award_cover']) || '' == $value['award_cover'] || strlen($value['award_cover'])>self::award_cover_lenght)
	            {
	                $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param award_cover invalid key['.$key.']value['.$value['award_cover'].']');
	                return false;
	            }
	            if(!isset($value['award_type']) || !is_numeric($value['award_type']))
	            {
	                $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param award_type invalid['.$value['award_type'].']');
	                return false;
	            } else {
	            	//���⽱Ʒ���Ʋ��ܳ���3������Ʒ���л��������ܳ���1w. by xuruiqi
	            	//$intRealAwardNum    += ($value['award_type'] == 1 ? 1 : 0);
	            	$intVirtualAwardNum += ($value['award_type'] == 2 ? 1 : 0);
	            	if ($intVirtualAwardNum > 3) {
	                	$this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'Num of virtual awards excceeds 3, too large.');
	            		return false;
	            	}
	            	
	            	$intLineCnt = 0;
	            	for($i = 0; $i < strlen($value['vaward_list']); ++$i) {
	            		if ($value['vaward_list'][$i] == "\n" && (++$intLineCnt > self::virtul_award_max_line)) {
	                		$this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'Line num of virtual award code excceeds' . self::virtul_award_max_line . ', too large.');
	            			return false;
	            		}
	            	}
	            	//���⽱Ʒ���Ʋ��ܳ���3������Ʒ�л��������ܳ���1w. by xuruiqi end
	            }
	            $arr_award_info[$key]['award_type'] = intval($value['award_type']);
	            if(!isset($value['award_precision']) || '' == $value['award_precision'])
	            {
	                $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param award_precision invalid key['.$key.']value['.$value['award_precision'].']');
	                return false;
	            }
	            if(!isset($value['award_num']) || !is_numeric($value['award_num']))
	            {
	                $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param award_num invalid['.$value['award_num'].']');
	                return false;
	            }
	            $arr_award_info[$key]['award_num'] = intval($value['award_num']);
	            if(!isset($value['award_post']) || !is_numeric($value['award_post']))
	            {
	                $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param award_post invalid['.$value['award_post'].']');
	                return false;
	            }
	            $arr_award_info[$key]['award_post'] = intval($value['award_post']);
	            
	            //ȥ����Ʒ�ĳ���ʱ���Լ�����ʱ�� by xuruiqi
	            /*
	            if(!isset($value['award_begin_time']) || !is_numeric(strtotime($value['award_begin_time'])))
	            {
	                $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param begin_time invalid['.$value['begin_time'].']');
	                return false;
	            }else
	            {
	                $arr_award_info[$key]['award_begin_time'] = strtotime($value['award_begin_time']);
	            }
	            if(!isset($value['award_end_time']) || !is_numeric(strtotime($value['award_end_time'])))
	            {
	                $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param end_time invalid['.$value['end_time'].']');
	                return false;
	            }else
	            {
	                $arr_award_info[$key]['award_end_time'] = strtotime($value['award_end_time']);
	            }
	            */
	            //ȥ����Ʒ�ĳ���ʱ���Լ�����ʱ�� by xuruiqi end

	            //$arr_award_info[$key]['award_id'] = $index;
	            $index++;
	        }
        }
        
        $arrParam['award_info'] = $arr_award_info;
        //-------check award_info --------------------end ----  
        
        //-------check award_rule ---------------------start------
        if('' == $arr_award_rule || empty($arr_award_rule))
        {
            $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param award_rule invalid['.serialize($arr_award_rule).']');
            return false;
        }
        $award_rule = array();
        foreach($arr_award_rule as $key=> $value )
        {
            $value = intval($value);
            if($value==0)
            {
                unset($arr_award_rule[$key]);
            }
            if(!isset($value) || !is_numeric($value))
            {
                $this->setErroInfo(Tieba_Errcode::ERR_MO_PARAM_INVALID,'param award_rule invalid['.$value.']');
                return false;
            }
            $award_rule[$key]= array('rule_type'=>0,'chance_num'=>$value);
        }
        $arr_award_rule = $award_rule;
    }
}


?>
