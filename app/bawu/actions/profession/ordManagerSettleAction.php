<?php

/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/3/15
 * Time: 19:18
 */
class ordManagerSettleAction extends Actions_Platform_Base
{
    const ORDINARY_ATTR_NAME = 'forum_ordinary';
    const WAIT_CHECK = 0;
    protected $strTemplate = "ord_manager_settle.php";
    protected $arrKeyFields = array(
        'word',
        'tbs',
        'apply_id',
    );
    protected $arrOptFields = array();
    protected $bolNeedCupid = false;
    protected $bolNeedPerm = false;
    private $_intTaskId;

    public function process() {
        //基本的模板变量
        $arrInput = array(
            'forum_id' => $this->intFid, //基类字段
            'forum_name' => $this->strFname,//基类字段
            'level_1_name' => $this->arrForumInfo['dir']['level_1_name'],//基类字段
            'level_2_name' => $this->arrForumInfo['dir']['level_2_name'],//基类字段
        );
        Page_Platform_Forum::build($arrInput);
        Page_Postaudit_User::build(array());
        Page_Platform_Team::build($arrInput);
        //自定义模板变量
        $this->_initParam();
        $arrInput = array(
            'forum_id' => $this->intFid,
        );
        $arrRes = Tieba_Service::call('forum', 'getForumAttr', $arrInput);
        if ($arrRes === false || Tieba_Errcode::ERR_SUCCESS !== intval($arrRes['errno'])) {
            Bingo_Log::warning("call forum getForumAttr fail" . serialize($arrInput));
            $this->_jumpPage();
            return false;
        }
        if (!empty($arrRes['output'][self::ORDINARY_ATTR_NAME])) {
            $arrTemp = $arrRes['output'][self::ORDINARY_ATTR_NAME];
            $intUid = intval($this->arrUserInfo['id']);
            foreach($arrTemp as $intKey => $arrAttr) {
                if($intUid === intval($intKey)) {
                    $intStatus = intval($arrAttr['status']);
                    $intTaskId = intval($arrAttr['task_id']);
                    if($intTaskId === $this->_intTaskId) {
                        $arrOrdinaryManagerInfo = array(
                            'task_id' => $this->_intTaskId,
                            'commitStatus' => $intStatus,
                            'errmsg' => !empty($arrAttr['reason']) ? strval($arrAttr['reason']) : '',
                        );
                        Bingo_Page::assign('OrdinaryManagerInfo', $arrOrdinaryManagerInfo);
                        return true;
                    }
                }
            }

        }
        $this->_jumpPage();
    }

    public function _initParam() {
        $this->_intTaskId = intval($this->arrReqInfo['apply_id']);
    }

    private  function _checkParam() {
        if (false === Tieba_Tbs::check($this->arrReqInfo['tbs'], true)) {
            Bingo_Log::warning('Tbs check fail.');
            Bingo_Page::assign('errno', -1);
            Bingo_Page::assign('errmsg', 'tbs check fail');
            Bingo_Page::setOnlyDataType("json");
            return false;
        }
        return true;
    }

    private  function  _checkUserPerm() {
        return true;
    }

    protected  function _jumpPage() {
        header("Location:http://tieba.baidu.com/f?kw=".$this->strFname);
        return;
    }
}