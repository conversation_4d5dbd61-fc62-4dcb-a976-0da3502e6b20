<?php

/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/3/15
 * Time: 19:18
 */
class addOrdinaryManagerInfoAction extends Actions_Platform_Base
{
    const ORDINARY_FORUM_ATTR = 'forum_ordinary';
    const PASS_AUDIT = 2;
    const WAIT_AUDIT = 1;
    const WAIT_CHECK = 0;
    protected $arrKeyFields = array(
        'word',
        'apply_id',
        'user_name',
        'phone_num',
        'email',
        'location',
        'tbs',
    );
    protected $arrOptFields = array(
        'qq',
        'household_location',
        'home_address',
    );
    protected $bolNeedCupid = false;
    protected $bolNeedPerm = false;
    private $_intCurrentUserId = 0;
    private $_strCurrentUserName = '';
    private $_strUserName;
    private $_arrUserInfo;
    private $_intTaskId;
    private $_strPhoneNum;
    private $_strQQ;
    private $_strEmail;
    private $_strLocation;
    private $_strHouseholdLocation;
    private $_strHomeAddress;

    public function  process() {
        $this->_initParam();
        if (!$this->_checkUserPerm()) {
            return false;
        }
        if (!$this->_checkParam()) {
            return false;
        }

        if (!$this->_checkUserName()) {
            return false;
        }

        $arrInput = array(
            'task_id' => intval($this->_intTaskId),
            'forum_id' => intval($this->intFid),
            'forum_name' => strval(trim($this->strFname)),
            'user_id' => intval($this->_arrUserInfo['user_id']),
            'user_name' => strval(trim($this->_arrUserInfo['user_name'])),
        );
        $arrExtraInfo = array(
            'phone_num' => $this->_strPhoneNum,
            'qq' => $this->_strQQ,
            'email' => $this->_strEmail,
            'location' => $this->_strLocation,
            'household_location' => $this->_strHouseholdLocation,
            'home_address' => $this->_strHomeAddress,
        );
        $arrInput['extra_info'] = $arrExtraInfo;
        $arrRes = Tieba_Service::call('profession', 'initOrdinaryManagerInfo', $arrInput,null,null,'post','php','gbk');
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== intval($arrRes['errno'])) {
            Bingo_Log::warning("call profession initOrdinaryManagerInfo fail input[" . serialize($arrInput) . "]" . "out[" . serialize($arrRes) . "]");
            $errno = 100008;
            if(100022 === $arrRes['errno']){
                $errno = $arrRes['errno'];
            }
            $arrOut = $this->_errRet($errno, "call profession initOrdinaryManagerInfo fail");
            echo Bingo_string::array2json($arrOut);
            return false;
        }
        $arrOut = $this->_errRet(0, "success");
        echo Bingo_string::array2json($arrOut);
        return true;

    }

    private function _initParam() {
        $this->_intCurrentUserId = intval($this->arrUserInfo['id']);
        $this->_intTaskId = intval($this->arrReqInfo['apply_id']);
        $this->_strCurrentUserName = $this->arrUserInfo['name'];
        $this->_strUserName = strval($this->arrReqInfo['user_name']);
        $this->_strPhoneNum = strval($this->arrReqInfo['phone_num']);
        $this->_strQQ = strval($this->arrReqInfo['qq']);
        $this->_strEmail = strval($this->arrReqInfo['email']);
        $this->_strLocation = strval($this->arrReqInfo['location']);
        $this->_strHouseholdLocation = strval($this->arrReqInfo['household_location']);
        $this->_strHomeAddress = strval($this->arrReqInfo['home_address']);
    }

    private function _checkParam() {
        if (false === Tieba_Tbs::check($this->arrReqInfo['tbs'], true)) {
            Bingo_Log::warning("the tbs check fail");
            $arrOutput = $this->_errRet(100005, 'the tbs check fail');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }
        if (empty($this->strFname)) {
            Bingo_Log::warning("the forum name is empty");
            $arrOutput = $this->_errRet(100000, 'the forum name is empty');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }

        if (empty($this->_strUserName)) {
            Bingo_Log::warning("the user name is empty");
            $arrOutput = $this->_errRet(100001, 'the user name is empty');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }

        if (empty($this->_strPhoneNum)) {
            Bingo_Log::warning("the user name is empty");
            $arrOutput = $this->_errRet(100002, 'the phone num is empty');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }

        if (empty($this->_strEmail)) {
            Bingo_Log::warning("the user email is empty");
            $arrOutput = $this->_errRet(100003, 'the user email is empty');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }
        if (empty($this->_strLocation)) {
            Bingo_Log::warning("the  location is empty");
            $arrOutput = $this->_errRet(100004, 'the location is empty');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }

        return true;
    }

    private function _checkUserName() {
        $arrInput = array(
            'user_name' => (array)$this->_strUserName,
        );
        $arrRes = Tieba_Service::call('user', 'getUidByUnames', $arrInput);
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning("call user getUidByUnames failed with input[" . serialize($arrInput) . "]");
            $arrOutput = $this->_errRet(100006, ' call user getUidByUnames failed');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }
        $arrUserInfos = $arrRes['output']['uids'];
        if (!empty($arrUserInfos)) {
            foreach ($arrUserInfos as $userInfo) {
                if (0 === intval($userInfo['user_id'])) {
                    Bingo_Log::warning("the user has no uid");
                    $arrOutput = $this->_errRet(100007, 'the user has no uid');
                    echo Bingo_String::array2json($arrOutput);
                    return false;
                }
                $this->_arrUserInfo = $userInfo;
            }
        }
        return true;
    }

    private function _checkUserPerm() {
        $arrInput = array(
            'forum_id' => intval($this->intFid),
        );
        $arrRes = Tieba_Service::call('forum', 'getForumAttr', $arrInput);
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning("call forum getForumAttr input[" . serialize($arrInput) . "]");
            $arrOutput = $this->_errRet(100019, ' call forum getForumAttr failed');
            echo Bingo_String::array2json($arrOutput);
            return false;
        }
        if(!empty($arrRes['output'][self::ORDINARY_FORUM_ATTR])) {
            $arrTemp = $arrRes['output'][self::ORDINARY_FORUM_ATTR];
            foreach($arrTemp as $intKey => $arrAttr) {
                if(intval($intKey) === $this->_intCurrentUserId) {
                    $intStatus = intval($arrAttr['status']);
                    $intTaskId = intval($arrAttr['task_id']);
                    if($intStatus === self::WAIT_CHECK && $intTaskId === $this->_intTaskId) {
                        return true;
                    }
                }
            }
        }
        $arrOutput = $this->_errRet(100022, 'user is invalid');
        echo Bingo_String::array2json($arrOutput);
        return false;
    }

    protected function _errRet($errno, $errmsg, $data = array()) {
        return array(
            'no' => $errno,
            'errmsg' => $errmsg,
            'data' => $data,
        );
    }


}
