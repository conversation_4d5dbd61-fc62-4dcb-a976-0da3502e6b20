<?php

/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/3/15
 * Time: 3:43
 */
class proManagerSettleAction extends Actions_Platform_Base
{
    const PROFESSION_ATTR_NAME = 'forum_profession';
    const ORDINARY_ATTR_NAME = 'forum_ordinary';
    const WAIT_CHECK = 0;
    const WAIT_AUDIT = 1;
    const PASS_AUDIT = 2;
    const UNPASS_AUDIT = 3;
    protected $strTemplate = "pro_manager_settle.php";
    protected $arrKeyFields = array(
        'word',
        'tbs',
        'apply_id',
    );
    protected $arrOptFields = array();
    protected $bolNeedCupid = false;
    protected $bolNeedPerm = false;
    private $_intTaskId;

    public function process() {
        $arrInput = array(
            'forum_id' => $this->intFid, //基类字段
            'forum_name' => $this->strFname,//基类字段
            'level_1_name' => $this->arrForumInfo['dir']['level_1_name'],
            'level_2_name' => $this->arrForumInfo['dir']['level_2_name'],
        );
        Page_Platform_Forum::build($arrInput);
        Page_Postaudit_User::build(array());
        Page_Platform_Team::build($arrInput);
        $this->_initParam();
        $arrInput = array(
            'forum_id' => $this->intFid,
        );
        $arrRes = Tieba_Service::call('forum', 'getForumAttr', $arrInput);
        if ($arrRes === false || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning("call forum getForumAttr fail" . serialize($arrInput));
            $this->_jumpPage();
            return false;
        }
        if (!empty($arrRes['output'][self::PROFESSION_ATTR_NAME])) {
            $arrTemp = $arrRes['output'][self::PROFESSION_ATTR_NAME];
            $intWaitCheckStatus = intval($arrTemp['is_wait_check']);
            $intWaitAuditStatus = intval($arrTemp['is_wait_audit']);
            $intAuditStatus = $arrTemp['is_pass_audit'];
            $intUserId = intval($arrTemp['user_id']);
            $intTaskId = intval($arrTemp['task_id']);
            if($intWaitCheckStatus && $intUserId === intval($this->arrUserInfo['id']) && $intTaskId === $this->_intTaskId) {
                $arrProfessionalManagerInfo = array(
                    'task_id' => $this->_intTaskId,
                    'commitStatus' => self::WAIT_CHECK,
                    'errmsg' => !empty($arrTemp['reason']) ? strval($arrTemp['reason']) : '',
                );
                Bingo_Page::assign('ProfessionalManagerInfo', $arrProfessionalManagerInfo);
                return true;
            }
            if($intWaitAuditStatus && $intUserId === intval($this->arrUserInfo['id']) && $intTaskId === $this->_intTaskId) {
                $arrProfessionalManagerInfo = array(
                    'task_id' => $this->_intTaskId,
                    'commitStatus' => self::WAIT_AUDIT,
                    'errmsg' => !empty($arrTemp['reason']) ? strval($arrTemp['reason']) : '',
                );
                Bingo_Page::assign('ProfessionalManagerInfo', $arrProfessionalManagerInfo);
                return true;
            }
            if(isset($intAuditStatus) && $intUserId === intval($this->arrUserInfo['id']) && $intTaskId === $this->_intTaskId) {
                if(intval($intAuditStatus) === 0) {
                    $arrProfessionalManagerInfo = array(
                        'task_id' => $this->_intTaskId,
                        'commitStatus' => self::UNPASS_AUDIT,
                        'errmsg' => !empty($arrTemp['reason']) ? strval($arrTemp['reason']) : '',
                    );
                    Bingo_Page::assign('ProfessionalManagerInfo', $arrProfessionalManagerInfo);
                    return true;
                } else {
                    $arrProfessionalManagerInfo = array(
                        'task_id' => $this->_intTaskId,
                        'commitStatus' => self::PASS_AUDIT,
                        'errmsg' => !empty($arrTemp['reason']) ? strval($arrTemp['reason']) : '',
                    );
                    Bingo_Page::assign('ProfessionalManagerInfo', $arrProfessionalManagerInfo);
                    return true;
                }

            }
        }
        $this->_jumpPage();
    }

    public function _initParam() {
        $this->_intTaskId = intval($this->arrReqInfo['apply_id']);
    }

    public function _checkParam() {
        if (false === Tieba_Tbs::check($this->arrReqInfo['tbs'], true)) {
            Bingo_Log::warning('Tbs check fail.');
            Bingo_Page::assign('errno', -1);
            Bingo_Page::assign('errmsg', 'tbs check fail');
            Bingo_Page::setOnlyDataType("json");
            return false;
        }
        return true;
    }
    protected  function _jumpPage() {
        header("Location:http://tieba.baidu.com/f?kw=".$this->strFname);
        return;
    }


}