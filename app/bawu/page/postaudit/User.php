<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2013-01-17 19:25:58
 * @version
 *
 *
 *
 *
 *
 */
class Page_Postaudit_User extends Page_Base {
	public static function build($pageInput) {
		$arrUserInfo = Util_User::getUserInfo ();
		$arrUserInfo ['tbs'] = Tieba_Tbs::gene ( $arrUserInfo ['is_login'] );
		
		$userForumInfo = Util_Perm::getUserBawuForum ( $arrUserInfo ['id'], true );
		
		if (! empty ( $userForumInfo )) {
			$arrFids = array ();
			foreach ( $userForumInfo as $tmpForum ) {
				$arrFids [] = $tmpForum ['forum_id'];
			}
			$arrFids = array_unique ( $arrFids );
			$forumNames = Util_Forum::mgetFnamesByFids ( $arrFids );
			
			if (! empty ( $forumNames )) {
				foreach ( $userForumInfo as &$tmpForum ) {
					$tmpForum ['forum_name'] = $forumNames [$tmpForum ['forum_id']];
				}
			}
			foreach ( $userForumInfo as $key => $tmpVal ) {
				if (! isset ( $tmpVal ['forum_name'] )) {
					unset ( $userForumInfo [$key] );
				}
			}
		}
		$arrUserInfo ['search'] = array(
			'stype'   => $pageInput['search_type'],
        	'svalue'  => $pageInput['search_value'],
        	'begin'   => $pageInput['op_start'],
			'end'     => $pageInput['op_end'],
			'op_type' => $pageInput['cmd_no'],
		);
        $arrUserInfo ['emoji_list']    = Util_User::getEmojiList();
		$arrUserInfo ['manage_forums'] = $userForumInfo;
		Bingo_Page::assign ( 'user', $arrUserInfo );
		return true;
	}
}
?>
