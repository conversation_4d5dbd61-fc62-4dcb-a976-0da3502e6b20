<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-10-30 22:25:58
 * @version
 */
class Page_Postaudit_Charge extends Page_Base {
	
	public static function build($pageInput){	
		$charge = array(
				'isHit' 	  => 0,
				'month_bills' => 0,
		);
		$arrInput = array(
				'forum_id'   => $pageInput['forum_id'],
				'forum_name' => $pageInput['forum_name'],
		);
		$arrOut = Tieba_Service::call('bawu', 'postAuditCuqid', $arrInput);
		if (Tieba_Errcode::ERR_SUCCESS === $arrOut['errno']) {
			$charge['isHit'] = intval($arrOut['ret']);
		}
		$arrInput = array(
				'forum_id'   => $pageInput['forum_id'],
				'user_id'    => $pageInput['user_id'],
		);
		$arrOut = Tieba_Service::call('bawu', 'getPostAuditBills', $arrInput);
		if (Tieba_Errcode::ERR_SUCCESS === $arrOut['errno'] && isset($arrOut['ret'])) {
			$charge['month_bills'] = $arrOut['ret'];
		}		
		Bingo_Page::assign('charge', $charge);
		return true;
	}
	
}