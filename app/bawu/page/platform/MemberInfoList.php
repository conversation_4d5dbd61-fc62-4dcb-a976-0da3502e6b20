<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-05-25 14:55:58
 * @version
 */
class Page_Platform_MemberInfoList extends Page_Base {

    	public static function build($pageInput){
		if (empty($pageInput)) {
			Bingo_Log::warning('parameters is empty');
			return false;
		}
	
		$arrList = array();
		$arrInput = $pageInput;
		
		$arrInput['page_type'] = 2;//grade.idl // 1-��Ա�б�,Ĭ��, 2-����ҳ

		Bingo_Timer::start('service_perm_getMemberList');
		$arrOut = Tieba_Service::call('perm','getMemberList',$arrInput);
		Bingo_Timer::end('service_perm_getMemberList');
		if(false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']) {
			Bingo_Log::warning("Can't get service_perm_getMemberList info.Input[".serialize($arrInput)."] out[".serialize($arrOut)."]");
			return false;
		}
		if(empty($arrOut['output'])) {
			$arrList['total'] = 0;
			$arrList['ret_num'] = 0;
		} else {
			$arrData = $arrOut['output']['member_list'];
			$arrList['total'] = $arrOut['output']['member_count'];
			$arrList['ret_num'] = count($arrData);
		}
		$arrList['cur_pn'] = intval($pageInput['page_no']);
		$arrList['page_size'] = intval($pageInput['page_size']);
		
		$arrUids = array();
		if(!empty($arrData)) {
			foreach($arrData as $arrEach) {
				$arrUids[] = $arrEach['user_id'];
			}
		}
		//Get UserInfo
		if (!empty($arrUids)) {
			$arrUserDict = Util_User::getUserData(array('user_id' => $arrUids));
			if(false === $arrUserDict) {
				Bingo_Log::warning("Can't get uids by unames.Input[".serialize($arrUids)."]");
			}
		}
		//Build Result
		foreach($arrData as &$arrEach) {
			$arrEach['user_name'] = $arrUserDict[$arrEach['user_id']]['user_name'];
			$arrEach['user_name'] = Bingo_Encode::convert($arrEach['user_name'],'GBK', 'UTF-8');
			$arrEach['show_nickname'] = Molib_Util_User::getShowNickname($arrUserDict[$arrEach['user_id']]);
			$arrEach['show_nickname'] = Molib_Util_Nickname::emojiConverter(strval($arrEach['show_nickname']));
            $arrEach['show_nickname'] = Bingo_Encode::convert($arrEach['show_nickname'],'GBK', 'UTF-8');
			$arrEach['portrait'] = strval(Tieba_Ucrypt::encode($arrEach['user_id'], $arrEach['user_name']));
		}
		$arrMemberInput = array('forum_id'=>$pageInput['forum_id']);
		$resMember = Util_Perm::getForumMember($arrMemberInput);
		if(false === $resMember) {
			Bingo_Log::warning("Can't get getForumMember.Input[".serialize($arrMemberInput)."]");
		}
		$arrList['member_nick_name'] = $resMember['output']['member_name'];
		$arrList['member_list'] = $arrData;
        Bingo_Page::assign('member_all_list', $arrList);
        return true;
	}
}
?>
