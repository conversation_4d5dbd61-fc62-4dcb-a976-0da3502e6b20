<?php

/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-06-06 16:02:58
 * @version
 */
class Page_Platform_BawuTeamList extends Page_Base
{
    public static function build($pageInput) {
        if (empty($pageInput)) {
            return false;
        }

        $arrList = array();
        $arrMgrAndAssist = array();
        $arrEditor = array();
        $arrInput = array(
            'forum_id' => $pageInput['forum_id'],
        );

        $arrTeam = array();
        $arrOut = Util_Perm::getBawuList($arrInput);
        if (false === $arrOut) {
            Bingo_Log::warning("Can't get bawu list.Input[" . serialize($arrInput) . "]");
            return false;
        } else {
            $arrTeam = $arrOut['output'];
        }
        $arrList['team'] = $arrTeam;

        //get forum_attr
        $arrForumAttr = Util_Forum::getForumAttr($pageInput['forum_info']['forum_id']['forum_id']);
        //get position type
        $arrType = array(
            //'has_pic' => $arrForumInfo['type']['is_photo'],
            'has_pic' => true,
            'has_video' => isset($arrForumAttr['video_p1']) ? true : false,
            'has_bakan' => isset($arrForumAttr['bakan_p1']) ? true : false,
            'has_broadcast' => !empty($arrForumAttr['broadcast'])?true:false,
            //isset($pageInput['forum_info']['type']['has_bakan'])?$pageInput['forum_info']['type']['has_bakan']:true,
        );
        $arrList['type'] = $arrType;
        Bingo_Page::assign('list', $arrList);
        return true;
    }
}

?>
