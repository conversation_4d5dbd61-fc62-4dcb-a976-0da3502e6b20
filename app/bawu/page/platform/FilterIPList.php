<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-05-25 14:55:58
 * @version
 */
class Page_Platform_FilterIPList extends Page_Base {
    	public static function build($pageInput){
		if (empty($pageInput)) {
			return false;
		}
	
		$arrList = array();
		$arrInput = $pageInput;
		$arrOut = Util_Block::getForumBlock($arrInput);
		if(false === $arrOut) {		
			Bingo_Log::warning("Can't get forum block info.Input[".serialize($arrInput)."]");
			return false;			
		}
		
		$arrList['total'] = $arrOut['total'];
		$arrList['cur_pn'] = intval($pageInput['pageindex']);
		$arrList['ret_num'] = $arrOut['ret_num'];
        	$arrList['search'] = array(
                	'stype' => $arrInput['search_type'],
                	'svalue' => $arrInput['search_value'],
                	'begin' => $arrInput['timestart'],
                	'end' => $arrInput['timeend'],
        	);	

		$arrListInfo = array();
		$arrBlockData=$arrOut['result'];
		if(!empty($arrBlockData)) {
			foreach($arrBlockData as $arrEach) {
				$intBlockTime = intval($arrEach['blocktime'])/3600/24;
				$arrListInfo[] = array(
					'duration' => $intBlockTime,
					'mask_ip' => Util_Tool::formatIp(Bingo_Http_Ip::newlong2ip($arrEach['user_id']), 3),
					//'filter_ip' => Util_Tool::formatIp(Bingo_Http_Ip::long2ip($arrEach['user_id']), 3),
					//'filter_ip' => Bingo_Http_Ip::long2ip($arrEach['user_id']),
					'filter_ip' => fcrypt_id_2hstr('Baidu.Tieba.Bawu.2013', $arrEach['user_id'], 0),
					'op_uname' => $arrEach['op_uname'],
					'op_uid' => $arrEach['op_uid'],
					'op_time' => intval($arrEach['start_time'])					
				);

			}
		}
		
		$arrList['members'] = $arrListInfo;
        	Bingo_Page::assign('list', $arrList);
        	return true;
	}
}
?>
