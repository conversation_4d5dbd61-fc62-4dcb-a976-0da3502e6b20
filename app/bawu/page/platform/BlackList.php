<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-05-25 14:55:58
 * @version
 */
class Page_Platform_BlackList extends Page_Base {

    	public static function build($pageInput){
		if (empty($pageInput)) {
			return false;
		}
	
		$arrList = array();
		$arrInput = $pageInput;
		if(isset($pageInput['user_name']))
		{
	  		$arrUnames   = array();
	  		$user_name   = urldecode(trim(strval($pageInput['user_name'])));
	  		$arrUnames[] = $user_name;
	  		if (!empty($arrUnames)) {
	  			$arrUserData = Util_User::getUidByNames($arrUnames);
		        if(false === $arrUserData) {
		        	Bingo_Log::warning("Can't get uids by names.Input[".serialize($arrUnames)."]");
		        	// return false;
		        }

		        $arrUidByName = array();
		        foreach ($arrUserData[$user_name] as $search) {
		        	if (intval($search['user_id']) > 0) {
		        		$arrUidByName[$user_name] = intval($search['user_id']);
		        	}
		        }	
	  		}
	  		$user_id = $arrUidByName[$user_name];
	  		if( $user_id > 0)
			{
				$arrInput['user_id'] =$user_id;

			}else {
				Bingo_Log::warning('Uid not exist.');
				return false;
			}
		}
		$arrInput['page_type'] = 2;//grade.idl   1-»áÔ±ÁÐ±í,Ä¬ÈÏ, 2-¹ÜÀíÒ³
		Bingo_Timer::start('service_perm_getBlackList');
		$arrOut = Tieba_Service::call('perm','getBlackList',$arrInput);
		Bingo_Timer::end('service_perm_getBlackList');
		if(false === $arrOut || Tieba_Errcode::ERR_SUCCESS !== $arrOut['errno']) {
			Bingo_Log::warning("Can't get service_perm_getBlackList info.Input[".serialize($arrInput)."] out[".serialize($arrOut)."]");
			return false;
		}
		if(empty($arrOut['output'])) {
			$arrList['total'] = 0;
			$arrList['ret_num'] = 0;
		} else {
			$arrData = $arrOut['output']['member_list'];
			$arrList['total'] = $arrOut['output']['member_count'];
			$arrList['ret_num'] = count($arrData);
			
		}
		$arrList['cur_pn'] = intval($pageInput['page_no']);
		$arrList['page_size'] = intval($pageInput['page_size']);
        	$arrList['search'] = array(
                	'stype' => $arrInput['search_type'],
                	'svalue' => $arrInput['search_value'],
        	);
		$arrUids = array();
		$arrOpUids = array();
		if(!empty($arrData)) {
			foreach($arrData as $arrEach) {
				$arrUids[] = $arrEach['user_id'];
				$arrOpUids[] = $arrEach['op_user_id'];
			}
		}
		//get user_id  by username
		if (!empty($arrUids)) {
			$arrUserDict = Util_User::getUserData(array('user_id' => $arrUids));
			if(false === $arrUserDict) {
				Bingo_Log::warning("Can't get uids by unames.Input[".serialize($arrUids)."]");
			}
		}

	    //get op_uid by username
	    if (!empty($arrOpUids)) {	
			$arrOpUserDict = Util_User::getUserData(array('user_id' => $arrOpUids));
			if(false === $arrOpUserDict) {
				Bingo_Log::warning("Can't get uids by unames.Input[".serialize($arrOpUids)."]");
			}
	    }
		//Build Result
		foreach($arrData as &$arrEach) {
			$arrEach['user_name'] = empty($arrUserDict[$arrEach['user_id']]['user_name']) ? '' : $arrUserDict[$arrEach['user_id']]['user_name'];
			//°ÉÎñºóÌ¨È¥ÓÃ»§Ãû£¬emojiêÇ³Æ×ªÎªimgÍ¼Æ¬ºóÔÚ×ªÂëgbk
			$arrEach['user_name'] = Bingo_Encode::convert($arrEach['user_name'],'GBK', 'UTF-8');
			$arrEach['nick_name'] = empty($arrUserDict[$arrEach['user_id']]['user_nickname']) ? '' : $arrUserDict[$arrEach['user_id']]['user_nickname'];
			$arrEach['nick_name'] = Molib_Util_Nickname::emojiConverter($arrEach['nick_name']);
			$arrEach['nick_name'] = Bingo_Encode::convert($arrEach['nick_name'],'GBK', 'UTF-8');
			$arrEach['show_nickname'] = Molib_Util_User::getShowNickname($arrUserDict[$arrEach['user_id']]);
            $arrEach['show_nickname'] = Molib_Util_Nickname::emojiConverter($arrEach['show_nickname']);
            $arrEach['show_nickname'] = Bingo_Encode::convert($arrEach['show_nickname'],'GBK', 'UTF-8');
			$arrEach['op_user_name'] = empty($arrOpUserDict[$arrEach['op_user_id']]['user_name']) ? '' : $arrOpUserDict[$arrEach['op_user_id']]['user_name'];
			$arrEach['op_user_name'] = Bingo_Encode::convert($arrEach['op_user_name'],'GBK', 'UTF-8');
			$arrEach['op_nickname'] = empty($arrOpUserDict[$arrEach['op_user_id']]['user_nickname']) ? '' : $arrOpUserDict[$arrEach['op_user_id']]['user_nickname'];
			$arrEach['op_nickname'] = Molib_Util_Nickname::emojiConverter($arrEach['op_nickname']);
			$arrEach['op_nickname'] = Bingo_Encode::convert($arrEach['op_nickname'],'GBK', 'UTF-8');
			$arrEach['show_op_nickname'] = Molib_Util_User::getShowNickname($arrOpUserDict[$arrEach['op_user_id']]);
            $arrEach['show_op_nickname'] = Molib_Util_Nickname::emojiConverter($arrEach['show_op_nickname']);
            $arrEach['show_op_nickname'] = Bingo_Encode::convert($arrEach['show_op_nickname'],'GBK', 'UTF-8');
			$arrEach['portrait'] = strval(Tieba_Ucrypt::encode($arrEach['user_id'], $arrEach['user_name']));
		}
		$arrList['black_list'] = $arrData;
        Bingo_Page::assign('list', $arrList);
        return true;
	}
}
?>
