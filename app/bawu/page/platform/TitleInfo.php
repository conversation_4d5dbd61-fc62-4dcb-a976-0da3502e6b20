<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-05-25 14:55:58
 * @version
 */
class Page_Platform_TitleInfo extends Page_Base {

	const AM_AUDIT_TYPE_BAN_FORUM = 1;
    	public static function build($arrInput){
		if (empty($arrInput)) {
			Bingo_Log::warning('parameters is empty');
			return false;
		}
		$arrList = array();		
		$forum_id = $arrInput['forum_id'];		
		$tab = $arrInput['tab'];
		
		$arrMemberInput= array('forum_id' => $forum_id);
		$resMember = Util_Perm::getForumMember($arrMemberInput);
		if(false === $resMember) {
			Bingo_Log::warning("Can't get getForumMember.Input[".serialize($arrMemberInput)."]");
		}
		
		$arrFid=array($forum_id);
		$forumInfoEx = Util_Forum::mgetBtxInfoEx($arrFid);
		if(false === $forumInfoEx) {
			Bingo_Log::warning("Can't get getForumMember.Input[".serialize($arrFid)."]");
		}
		
		$forumInfo = $forumInfoEx['output'][$forum_id];
		
		$arrBawuInput = array(
				'forum_id' => $forum_id,
		);
		$arrOut = Util_Perm::getBawuList($arrBawuInput);
		if(false === $arrOut) {
			Bingo_Log::warning("Can't get bawu list.Input[".serialize($arrBawuInput)."]");
		} 
		$mgrNum = count($arrOut['output']['manager']);
		
		// all forum is keyOp, as intern manager is not used anymore
		/*$bolIsKeyOp      = Util_Forum::isOpForum($forum_id) === 1 ? true : false;
		$bolIsAuditForum = false;
		$bolIsBanIntern  = Util_Word::isBanForumDir($forumInfo['dir']['level_1_name']);
		$intType         = Util_Word::getAuditForumTypeByName($forumInfo['forum_name']['forum_name']);
		$bolIsBan        = ($intType === self::AM_AUDIT_TYPE_BAN_FORUM ? true : false);
		if ( $intType > 1 ){
			$bolIsAuditForum = true;
		}
		// audit forum and key op forum is rather similar, here we treat audit forum as key op.
		$bolIsKeyOp = $bolIsAuditForum || $bolIsKeyOp || $bolIsBanIntern;
		 */
		$bolIsKeyOp = true;
		$arrList['forum'] = array(
				'is_key_op'    =>$bolIsKeyOp,
				'mgr_num'      => $mgrNum,
				'forum_name'   => $forumInfo['forum_name']['forum_name'],
				'forum_id'     => $forumInfo['forum_name']['forum_id'],
				'member_num'   => $resMember['output']['member_count'],
				'post_num'     => $forumInfo['statistics']['post_num'] + $forumInfo['statistics']['thread_num'],
				'first_class'  => $forumInfo['dir']['level_1_name'],
				'second_class' => $forumInfo['dir']['level_2_name'],
				'is_brand_forum' => intval($forumInfo['attrs']['special_forum']['is_brand_forum']),
		);
		$arrList['forumex'] = $forumInfo['card'];
		$arrList['page'] = array (
				'tab' => $tab,   //????????tab?
		);
		$arrUserInfo = Util_User::getUserInfo();
		$arrUserInfo['tbs'] = Tieba_Tbs::gene($arrUserInfo['is_login']);
		
		$forumUser=array();
		if($arrUserInfo['is_login'] ===true)
		{
			//get perm
			$ipArr = Bingo_Http_Ip::getConnectIpExt();
			$intIp = isset($ipArr['type']) && $ipArr['type'] == 'IPv4' ? intval(Bingo_Http_Ip::newip2long($ipArr['ip'])) : 0;
            $intIp6 = isset($ipArr['type']) && $ipArr['type'] == 'IPv6' ? $ipArr['ip'] : '';
			$arrInput = array(
					"forum_id" =>$arrInput['forum_id'],
					"user_id" => $arrUserInfo['id'],
					"user_ip" => $intIp,
                	"user_ip6" => $intIp6,
			);
			$arrPerm = Util_Perm::getPerm($arrInput);
			if(false === $arrPerm) {
				Bingo_Log::warning("Can't get user perm.Input[".serialize($arrInput)."]");
			}
			
			$forumUser['is_firstlike'] = 0;
			$forumUser['has_liked'] = $arrPerm['grade']['is_like'];
			$forumUser['level_id'] = $arrPerm['grade']['level_id'];
			$forumUser['cur_score'] = $arrPerm['grade']['cur_score'];
			$forumUser['score_left'] = $arrPerm['grade']['score_left'];
			$forumUser['levelup_score'] = $arrPerm['grade']['cur_score']+$arrPerm['grade']['score_left'];
			$forumUser['level_name'] = $arrPerm['base']['level_name'];
			$intTidId = $arrPerm['tip']['tip_id'];
			if($intTidId === 1) {
					$forumUser['is_firstlike'] = 1;
			}
		}
		$arrList['forumuser']['balv'] = $forumUser;
		//var_dump($arrUserInfo);
		Bingo_Page::assign('user', $arrUserInfo);
		Bingo_Page::assign('forumuser', $arrList['forumuser']);
        Bingo_Page::assign('forum', $arrList['forum']);
        Bingo_Page::assign('forumex', $arrList['forumex']);
        Bingo_Page::assign('page', $arrList['page']);
        return true;
	}
}
?>
