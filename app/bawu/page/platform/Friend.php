<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-08-05 15:14:58
 * @version
 */
class Page_Platform_Friend extends Page_Base {
    public static function build($pageInput){
        
        if (empty($pageInput)) {
            return false;
        }

        $arrInput = array(
            'forum_id' => $pageInput['forum_id'],
        );
        
        $arrRes = array();
        Bingo_Timer::start('service_forum_getForumAttr');
        $arrOut = Tieba_Service::call('forum','getForumAttr',$arrInput);
        Bingo_Timer::end('service_forum_getForumAttr');
        if(Tieba_Errcode::ERR_SUCCESS === $arrOut['errno']){
            $arrRes = $arrOut['output']['zyqfriend'];
        }
        Bingo_Page::assign('friend', $arrRes);
        return true;
    }
}
?>