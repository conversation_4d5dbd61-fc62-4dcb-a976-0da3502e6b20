<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-07-01 13:26:58
 * @version
 */
class Page_Platform_ConditionList extends Page_Base {

    public static function build($arrInput){
    	if (empty($arrInput)) {
    		Bingo_Log::warning('parameters is empty');
    		return false;  
    	}
    	
    	//forum apply condition
    	$arrInputCond = array(
    			'forum_id' => $arrInput['forum_id'],
    	);
    	Bingo_Timer::start('service_managerapply');
    	$arrCondOut = Tieba_Service::call('managerapply', 'getCondition', $arrInputCond);
    	Bingo_Timer::end('service_managerapply');
    	if(false === $arrCondOut) {
    		Bingo_Log::warning('call service managerapply::getCondition failed,input[' . serialize($arrInputCond) . '],output[' . serialize($arrCondOut) . ']');
    	}
    	//var_dump($arrOut);
    	if(!isset($arrCondOut['output']))
    	{
    		$arrCondOut['output']['member_days'] = 0;
    		$arrCondOut['output']['user_post_num'] = 1;
    	}
    	
    	$arrCondList['manager']['member_days']   = $arrCondOut['output']['member_days'];
    	$arrCondList['manager']['user_post_num'] = $arrCondOut['output']['user_post_num'];
    	
    	
    	$arrInputAssCond = array(
    			'forum_id' => $arrInput['forum_id'],
    	);
    	$arrAssistOut = Tieba_Service::call('managerapply', 'getAssistCondition', $arrInputAssCond);
    	Bingo_Timer::end('service_managerapply');
    	if(false === $arrAssistOut) {
    		Bingo_Log::warning('call service managerapply::getAssistCondition failed,input[' . serialize($arrInputAssCond) . '],output[' . serialize($arrAssistOut) . ']');
    	}
    	$arrCondList['assist']['condition'] =  $arrAssistOut['output']['condition'];
    	
    	Bingo_Page::assign('condition_list', $arrCondList);
		return true;
    }	
    
    
}