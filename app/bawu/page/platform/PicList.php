<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-07-01 13:26:58
 * @version
 */
class Page_Platform_PicList extends Page_Base {

	const CAMEL_SERVICE_NAME = 'photo_frs_http';
	const URL= '/photo/bw/picture/query';
    public static function build($arrInput){
    	$arrList=array();
    	if(!empty($arrInput['op_user_name']))
    	{
	    	Tbapi_Core_Server::addCookie($_COOKIE);
	    	$strOut = Tbapi_Core_Midl_Http::httpcall(self::CAMEL_SERVICE_NAME, self::URL, $arrInput,'post');
	    	if(!$strOut){
	    		Bingo_Log::warning("http call fail.");
	    		return false;
	    	}
	    	$arrOut = Bingo_String::json2array($strOut);
	    	if(!$arrOut){
	    		Bingo_Log::warning("http call json_decode fail.json str:[$strOut]");
	    		return false;
	    	}
	    	
	    	$arrList['contents']=$arrOut['data']['picture_list'];
	    	$arrList['total'] = $arrOut['data']['page']['total_num'];
	    	$arrList['ret_num'] = count($arrOut['data']['picture_list']);
	    	$arrList['cur_pn'] =$arrOut['data']['page']['cur_page'];
	    	$arrList['tbs']=$arrOut['data']['tbs']["common"];
    	}
    	else 
    	{
    		$arrList['contents']=array();
    		$arrList['total'] = 0;
    		$arrList['ret_num'] = 0;
    		$arrList['cur_pn'] =1;
    	}
    	$arrList['search'] = array(
		'stype' => $arrInput['search_type'],
		'svalue' => $arrInput['op_user_name'],
		'begin' => $arrInput['op_time_begin'],
		'end' => $arrInput['op_time_end'],
	); 
	
	Bingo_Page::assign('list', $arrList);
	return true;
    }	
}
?>
