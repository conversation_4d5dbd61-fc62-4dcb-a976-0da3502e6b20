<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013-01-17 19:25:58
 * @version
 */
class Page_Platform_Team extends Page_Base {

	public static function build($pageInput){
		$arrInput = $pageInput; 
		$arrOut = Util_Perm::getBawuList($arrInput);
		if(false !== $arrOut) {
			Bingo_Page::assign('team', $arrOut['output']);
		} else {
			Bingo_Log::warning("Can't get bawu list.Input[".serialize($arrInput)."]");
			Bingo_Page::assign('team', '');
		}
		return true;
	}
}
?>