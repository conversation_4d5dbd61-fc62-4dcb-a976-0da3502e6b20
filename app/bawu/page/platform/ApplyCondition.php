<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file ApplyCondition.php
 * <AUTHOR>
 * @date 2013/11/01 10:01:04
 * @brief 
 *  
 **/

class Page_Platform_ApplyCondition extends Page_Base{
	public static function build($arrInput)
	{
		$arrInputCond = array(
			'forum_id' => $arrInput['forum_id'],
		);
		Bingo_Timer::start('managerapply_getCondition');
		$arrOutput = Tieba_Service::call('managerapply', 'getCondition', $arrInputCond);
		Bingo_Timer::end('managerapply_getCondition');
		if($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			$input  = serialize($arrInputCond);
			$output = serialize($arrOutput);
			Bingo_Log::warning("call service managerapply::getCondition failed, input[$input] output[$output]");
		}
		
		if(!isset($arrOutput['output'])){
			$arrOutput['output']['member_days']   = 0;
			$arrOutput['output']['user_post_num'] = 1;
		}
		Bingo_Page::assign('manager_apply_require', $arrOutput['output']);
		return true;
	}
}

/* vim: set noet ts=4 sw=4 sts=4 tw=100: */
?>