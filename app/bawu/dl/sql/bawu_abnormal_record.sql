CREATE TABLE `bawu_abnormal_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `forum_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '吧ID',
  `action` varchar(48) NOT NULL DEFAULT '' COMMENT '行为、流程（建吧 create_forum、下任 bawutask、吧务竞选 apply等）',
  `action_policy` bigint(20) NOT NULL DEFAULT '0' COMMENT '业务策略id',
  `stage` varchar(48) NOT NULL DEFAULT '' COMMENT '阶段',
  `abnormal_type` varchar(48) NOT NULL DEFAULT '' COMMENT '异常类型',
  `ui_trans_params` varchar(1024) NOT NULL DEFAULT '' COMMENT '业务数据',
  `op_user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人uid',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '异常状态 0未处理，1已处理，2无效报警',
  `comment` varchar(1024) NOT NULL DEFAULT '' COMMENT '说明',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_action_abnormal_type` (`action`,`abnormal_type`),
  KEY `idx_status_time` (`status`,`create_time`,`update_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='吧务异常监控表' ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=8;