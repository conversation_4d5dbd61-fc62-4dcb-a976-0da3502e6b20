CREATE TABLE `bawu_monitor_policy` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `monitor_policy_name` varchar(48) NOT NULL DEFAULT '' COMMENT '监控策略名称',
  `monitor_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '监控类型（0数据报表，1异常监控）',
  `monitor_action` varchar(48) NOT NULL DEFAULT '' COMMENT '监控行为流程（建吧 create_forum、下任 bawutask、吧务竞选 apply等）',
  `monitor_stage` varchar(48) NOT NULL DEFAULT '' COMMENT '监控流程的具体阶段',
  `function_config` text COMMENT '调用策略配置',
  `policy_ids` varchar(48) NOT NULL DEFAULT '' COMMENT '依赖的汇总策略id，英文逗号分割',
  `time_interval` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '监控时间间隔（分钟级1 小时级2 天级3 周级4 月级5）',
  `exec_time_conf` varchar(1024) NOT NULL DEFAULT '' COMMENT '调度时间配置json，key：min、hour、weekday、day，value：int[]',
  `status` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '监控状态（0未启用 1启用中）',
  `op_user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人uid',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_action_stage` (`monitor_action`,`monitor_stage`),
  KEY `idx_interval` (`time_interval`),
  KEY `idx_type_status` (`monitor_type`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT '吧务业务监控';