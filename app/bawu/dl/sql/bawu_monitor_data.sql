CREATE TABLE `bawu_monitor_data` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
 `action` varchar(48) NOT NULL DEFAULT '' COMMENT '行为、流程（建吧 create_forum、下任 bawutask、吧务竞选 apply等）',
 `stage` varchar(48) NOT NULL DEFAULT '' COMMENT '流程中的具体阶段（待审核 to_audit、试运行期 on_try_task、投票期 on_vote）',
 `policy_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '监控策略id',
 `statistics` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '统计数据',
 `event_time` int(11) NOT NULL DEFAULT '0' COMMENT '统计的时间',
 `comment` varchar(1024) NOT NULL DEFAULT '' COMMENT '说明',
 `op_user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人uid',
 `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
 `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
 PRIMARY KEY (`id`),
 KEY `idx_time_action_stage` (`event_time`,`action`,`stage`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 comment '吧务数据监控表' ROW_FORMAT=COMPRESSED KEY_BLOCK_SIZE=8;