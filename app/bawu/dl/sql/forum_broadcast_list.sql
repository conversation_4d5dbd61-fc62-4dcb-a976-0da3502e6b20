CREATE TABLE `forum_broadcast_list` (
`id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
`broadcast_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'broadcast_id',
`forum_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'forum_id',
`manager_user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'manager_user_id',
`thread_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'thread_id',
`url` varchar(200) NOT NULL DEFAULT '' COMMENT 'url',
`title` varchar(70) NOT NULL DEFAULT '' COMMENT 'title',
`image` varchar(200) NOT NULL DEFAULT '' COMMENT 'image',
`content` varchar(200) NOT NULL DEFAULT '' COMMENT 'content',
`create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '1 insert time',
`examine_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '6,10 examine time',
`send_examine_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '4 send examine time',
`broadcast_status` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '1,4,6,8,10,14',
`send_user_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'send_user_num',
`send_user_num_new_version` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'send_user_num_new_version',
`read_user_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'read_user_num',
`check_user_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'check_user_num',
`send_break_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '8 send break time',
`send_done_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '14 send done time',
`update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'update time',
`send_info` varchar(1000) NOT NULL DEFAULT '' COMMENT 'send info by json',
`job_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'this is job_id && task_id',
PRIMARY KEY (`id`),
UNIQUE KEY `idx_bid` (`broadcast_id`),
KEY `idx_fid` (`forum_id`),
KEY `idx_uid` (`manager_user_id`),
KEY `idx_ctm` (`create_time`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='forum_broadcast_list';