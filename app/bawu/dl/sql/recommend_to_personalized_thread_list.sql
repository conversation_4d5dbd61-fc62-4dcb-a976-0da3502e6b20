CREATE TABLE `recommend_to_personalized_thread_list` (
`id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
`thread_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'thread_id',
`forum_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'forum_id',
`hot_num` int(11) NOT NULL DEFAULT '0' COMMENT 'thread redu',
`create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'insert time',
`forum_create_time` int(11) NOT NULL DEFAULT '0' COMMENT 'thread create time',
`is_recommend` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '0:no,1:yes',
`recommend_user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'bazhu user id',
`recommend_time` int(11) NOT NULL DEFAULT '0' COMMENT 'recommend create time',
`current_pv` int(11) NOT NULL DEFAULT '0' COMMENT 'current thread pv',
PRIMARY KEY (`id`),
UNIQUE KEY `idx_fidtid`(`thread_id`, `forum_id`),
KEY `idx_fid` (`forum_id`),
KEY `idx_fct` (`forum_create_time`)
KEY `idx_rui` (`recommend_user_id`)
KEY `idx_rtm` (`recommend_time`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='bazhu_recommend_to_personalized';