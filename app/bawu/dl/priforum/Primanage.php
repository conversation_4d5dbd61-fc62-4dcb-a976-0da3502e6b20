<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file PriforumManage.php
 * <AUTHOR> @date 2018-12-07
 * @brief 私有化吧吧务后台
 */

class Dl_Priforum_Primanage
{
	const SERVICE_NAME  = "Dl_Priforum_Primanage";
    const MODULE_NAME   = "bawu";
    const DATABASE_NAME = "forum_priforum";
    const DB_CHARSET    = "utf8";

    protected static $_db   = null;
    protected static $_conf = null;

    /**
     * @brief get mysql obj.
     * @param [type] $[name] [<description>]
     * @return: obj of Bd_DB, or null if connect fail.
     **/
    private static function _getDB() {
        if(self::$_db) {
            return self::$_db;
        }
        self::$_db = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if(self::$_db == null || !self::$_db->isConnected()) {
            Bingo_Log::warning('db connect fail.');
            return null;
        }
        return self::$_db;
    }

    /**
     * @brief init
     * @param [type] $[name] [<description>]
     * @return: true if success. false if fail.
     **/
    private static function _init() {
        if(self::_getDB() == null) {
            Bingo_Log::warning("init db fail.");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        if(self::$_conf == null) {
            $dlConfFile = '/app/' . self::MODULE_NAME . '/'. strtolower(self::SERVICE_NAME);
            self::$_conf = Bd_Conf::getConf($dlConfFile);
            if(self::$_conf == false) {
                Bingo_Log::warning('init get conf fail.' . $dlConfFile);
                return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
            }
        }
        return true;
    }

    /**
     * @brief 错误返回方法
     * @param   $errno [<description>]
     * @return  array [<description>]
     */
    private static function _errRet($errno, $data=array()) {
        return array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data'   => $data,
        );
    }

    /**
     * @brief exec sql method
     * @param  $arrInput [<description>]
     * @return  output [<description>]
     */ 
    public static function execSql($arrInput) {
        if(empty($arrInput['function'])) {
            Bingo_Log::warning('input params invalid: function is empty. [' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }       
        $ret = self::_init();
        if($ret !== true) {
            return $ret;
        }
        Bingo_Timer::start('initlib');
        $mdb = new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
        Bingo_Timer::end('initlib');
        if($mdb == null) {
            Bingo_Log::warning('new lib_db fail.');
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $arrOut = $mdb->execSql($arrInput); 
        return $arrOut;
    }       
}