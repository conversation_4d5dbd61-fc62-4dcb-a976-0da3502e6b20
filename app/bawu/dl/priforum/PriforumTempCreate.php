<?php
/**
 * Created by PhpStorm.
 * User: huxiaomei
 * Date: 2018/11/16
 * Time: 下午2:34
 */
class Dl_Priforum_PriforumTempCreate
{
    const SERVICE_NAME = "Dl_Priforum_PriforumTempCreate";
    //这里需要修改库名
    const DB_RAL_SERVICE_NAME = "DB_forum_priforum";
    protected static $_conf = null;
    protected static $_db = null;
    protected static $_charset = 'utf8';
    protected static $tbName = 'priforum_temp_create';
    protected static $_use_split_db = false;
    protected static $arrAllFields = array(
        'id'         =>        'int',
        'user_id' =>           'int',
        'user_ip' =>           'str',
        'user_name' =>         'str',
        'invite_code' =>       'str',
        'forum_name' =>        'str',
        'tag_id' =>            'str',
        'user_real_name' =>    'str',
        'user_idcode' =>       'str',
        'picture1' =>          'str',
        'picture2' =>          'str',
        'picture3' =>          'str',
        'create_time' =>       'int',
        'update_time' =>       'int'
    );

    /////////
    //huxiaomei
    /**
     * [insertRecord description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function insertRecord($arrInput) {
        if (! self::_init ()) {
            return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
        }

        $dbObj = self::_getDB ();
        if (! $dbObj) {
            return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL );
        }
        $strPre 	= "INSERT INTO ";
        $strField 	= "(";
        $strFieldType = '(';
        $strValue 	= "(";
        $strTable   = self::$tbName;
        foreach (self::$arrAllFields as $k => $v){
            if ($k == 'id'){
                continue;
            }
            $strField  .= $k.",";
            $strFieldType .= ($v == 'int') ? '%d,' : '\'%s\',';
            if (!isset($arrInput[$k]) || empty($arrInput[$k])){
                $strValue .= ($v == 'int') ? 0 : '\'\'';
            }else{
                if ($v == 'int'){
                    $strValue .= intval($arrInput[$k]);
                }else if ($v == 'str'){
                    $strValue .=  "'$arrInput[$k]'";
                }
            }
            $strValue .= ",";
        }
        $strField 	= substr($strField, 0, -1).")";
        $strFieldType 	= substr($strFieldType, 0, -1).")";
        $strValue = substr($strValue, 0, -1).")";
//        $strSql 	= sprintf("insert into ".self::$tbName.$strField." VALUES(%d,%d,%d,'%s','%s','%s','%s','%s',%d,'%s','%s','%s',%d,%d,%d,%d,%d,%d,'%s','%s');",$strValue);
        $strSql 	= sprintf("%s %s %s VALUES%s;", $strPre, $strTable, $strField, $strValue);
        if (false === $dbObj->query ( $strSql )) {
            Bingo_Log::warning ( __FUNCTION__ . ':insert db failed, [' . $strSql . '], error:' . $dbObj->error () );
            return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL );
        }
        return self::_succRet($dbObj -> getInsertID());
    }

    /**
     * [updateRecord description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function updateRecordById($arrInput) {
        if (! self::_init ()) {
            return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
        }
        if(!isset($arrInput['id']) || !isset($arrInput['set_info'])){
            Bingo_Log::warning(__FUNCTION__ . 'input params invalid ' . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $setInfo = $arrInput['set_info'];
        $id = $arrInput ['id'];
        $dbObj = self::_getDB ();

        if (! $dbObj) {
            return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL );
        }

        foreach ($setInfo as $k => $v){
            if (!array_key_exists($k,self::$arrAllFields)){
                unset($setInfo[$k]);
            }
        }
        $setClause = self::_genSetClause ( $setInfo, $dbObj );
        $strSql = "update ".self::$tbName." $setClause where id = $id ;";
        // Bingo_Log::notice ( __FUNCTION__ . "[$strSql]" );
        $ret = $dbObj->query ( $strSql );
        if ($ret === false ) {
            Bingo_Log::warning ( __FUNCTION__ . ':update db failed, [' . $strSql . '], error:' . $dbObj->error () );
            return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL );
        }
        return self::_succRet($ret);
    }

    /**
     * [getRecordByConditions description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getRecordByConditions($arrInput) {
        if (! self::_init ()) {
            return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
        }
        /*
        if (! Util_Params::checkNum ( $arrInput, 'user_id' )) {
            self::_errRet ( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        */
        $dbObj = self::_getDB ();

        if (! $dbObj) {
            return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL );
        }

        $getCt = isset($arrInput ['get_ct']) ? $arrInput ['get_ct'] : false;
        $returnAll = isset($arrInput ['return_all']) ? $arrInput ['return_all'] : true;
        $latest = isset($arrInput['latest']) ? $arrInput['latest'] : false;
        $orderby = isset($arrInput['orderby']) ? $arrInput['orderby'] : false;
        $order = isset($arrInput['order']) ?  $arrInput['order'] : 'esc';
        $offset = isset($arrInput['offset']) ? $arrInput['offset'] : false;
        $count = isset($arrInput['count']) ? $arrInput['count'] : false;
        $selectClause = isset($arrInput['select_fileds']) && !empty($arrInput['select_fileds']) ? implode(',',$arrInput['select_fileds']) : '*';
        unset ( $arrInput ['get_ct'] );
        unset ( $arrInput ['return_all'] );
        unset ( $arrInput['latest'] );
        unset ( $arrInput ['orderby'] );
        unset ( $arrInput ['order'] );
        unset ( $arrInput['offset'] );
        unset ( $arrInput['count'] );
        unset ( $arrInput['select_fileds'] );

        $condsClause = self::_genCondsClause ( $arrInput, $dbObj );

        if ($getCt === true) {
            $strSql = "select count(*) ct from ".self::$tbName." where $condsClause ";
        }else {
            $strSql = "select ".$selectClause." from ".self::$tbName." where $condsClause ";
        }

        if($getCt === false){
            if ( $orderby !== false ) {
                $strSql .= " order by $orderby $order";
            }
            if($offset !==false && $count!==false){
                $strSql .= " limit $offset , $count";
            }else if($latest){
                $strSql .= " limit 1";
            }
        }

        Bingo_Log::debug ( __FUNCTION__ . ": sql[$strSql]" );
        if (false === ($dbRet = $dbObj->query ( $strSql ))) {
            Bingo_Log::warning ( __FUNCTION__ . ':query db failed, [' . $strSql . '], error:' . $dbObj->error () );
            return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL );
        }

        if ($getCt === true) {
            $ret = array ('total' => $dbRet [0] ['ct'] );
        }else {
            if ($returnAll === true) {
                $ret = array (
                    'data' => $dbRet
                );
            }else {
                $ret = array ('data' => $dbRet[0] );
            }
            $ret['total'] = count($ret['data']);
        }
        return self::_succRet($ret);
    }

    /**
     * [deleteRecordById description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function deleteRecordById($arrInput) {
        if (! self::_init ()) {
            return self::_errRet ( Tieba_Errcode::ERR_LOAD_CONFIG_FAIL );
        }
        if(!isset($arrInput['id'])){
            Bingo_Log::warning(__FUNCTION__ . 'input params invalid ' . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $id = $arrInput ['id'];
        $dbObj = self::_getDB ();

        if (! $dbObj) {
            return self::_errRet ( Tieba_Errcode::ERR_DB_CONN_FAIL );
        }

        $strSql = "delete from ".self::$tbName." where id = $id ;";
        // Bingo_Log::notice ( __FUNCTION__ . "[$strSql]" );
        $ret = $dbObj->query ( $strSql );
        if ($ret === false ) {
            Bingo_Log::warning ( __FUNCTION__ . ':update db failed, [' . $strSql . '], error:' . $dbObj->error () );
            return self::_errRet ( Tieba_Errcode::ERR_DB_QUERY_FAIL );
        }
        return self::_succRet($ret);
    }

    /////////

    /**
     * [_genSetClause 获取update set 语句]
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    private static function _genSetClause($arrInput, $dbObj) {
        if (is_null ( $arrInput )) {
            return 'set 1=1';
        }
        foreach ( $arrInput as $key => $value ) {
            if (is_numeric ( $value )) {
                $fields [] = "$key=$value";
            }
            elseif (is_null ( $value )) {
                $fields [] = "$key=null";
            }
            else {
                $value = $dbObj->escapeString ( $value );
                $fields [] = "$key='$value'";
            }
        }
        if (empty ( $fields )) {
            return 'set 1=1';
        }
        else {
            return 'set ' . implode ( ',', $fields );
        }
    }
    /**
     * [_genCondsClause 获取select where 语句]
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    private static function _genCondsClause($arrInput, $dbObj) {
        if (is_null ( $arrInput )) {
            return '1=1';
        }
        foreach ( $arrInput as $key => $value ) {
            if (is_numeric ( $value )) {
                $fields [] = "$key=$value";
            }
            elseif (is_null ( $value )) {
                $fields [] = "$key is null";
            }
            elseif (is_array ( $value ) && ! empty ( $value )) {
                $fields [] = "$key in (" . implode ( ',', $value ) . ")";
            }
            elseif (is_string ( $value )) {
                $value = $dbObj->escapeString ( $value );
                $fields [] = "$key='$value'";
            }
        }
        if (empty ( $fields )) {
            return '1=1';
        }
        else {
            return implode ( ' and ', $fields );
        }
    }


    /**
     * @brief get mysql obj.
     * @return: obj of Bd_DB, or null if connect fail.
     **/
    private static function _getDB() {
        if (self::$_db) {
            return self::$_db;
        }
        self::$_db = new Bd_DB ();
        if (self::$_db == null) {
            Bingo_Log::warning ( "new bd_db fail. get db failed!" );
            return null;
        }
//        if (self::$_use_split_db) {
//            $splitDBConfPath = ROOT_PATH . "/conf/db/";
//            $splitDBConfFile = "db_dl_priforum_priforum.conf";
//            $r = self::$_db->enableSplitDB ( self::DB_RAL_SERVICE_NAME, $splitDBConfPath, $splitDBConfFile );
//            if (! $r) {
//                Bingo_Log::warning ( "enable splitdb fail. get db failed!" );
//                self::$_db = null;
//                return null;
//            }
//            return self::$_db;
//        }else {
        Bingo_Timer::start ( 'dbinit' );
        $r = self::$_db->ralConnect ( self::DB_RAL_SERVICE_NAME );
        //var_dump($r);
        Bingo_Timer::end ( 'dbinit' );
        if (! $r) {
            Bingo_Log::warning ( "bd db ral connect fail. get db failed!" );
            self::$_db = null;
            return null;
        }
        /*if ($_ENV['HHVM'] == 1) {
            self::$_db->charset('gbk');
        }*/
        self::$_db->charset(self::$_charset);
        return self::$_db;
//        }
        return null;
    }

    /**
     * @brief init
     * @return: true if success. false if fail.
     **/
    private static function _init() {
        if (self::$_conf == null) {
            self::$_conf = Bd_Conf::getConf ( "/app/bawu/dl_priforum_priforum" );
            if (self::$_conf == false) {
                Bingo_Log::warning ( "init get conf fail." );
                return false;
            }
        }
        return true;
    }

    /**
     * [_succRet description]
     * @param  [type] $ret [description]
     * @return [type]      [description]
     */
    protected static function _succRet($ret){
        $error = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data'   => $ret,
        );
    }

    /**
     * [_errRet description]
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    protected static function _errRet($errno){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

}