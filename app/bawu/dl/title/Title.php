<?php

/**
 * <AUTHOR>
 * @date 2017-06-02
 */
define("MODULE","Dl_Title_Title");
class Dl_Title_Title{
	const SERVICE_NAME = "Dl_Title_Title";
    const DATABASE_NAME = "forum_bztask";
    private static $_table  = 'forum_title';
    protected static $_conf = null;
    private static $_db = null;
    private static $_fields = array(
    	'apply_id',
    	'forum_id',
    	'user_id',
        'op_user_id',
    	'op_user_name',
        //'op_from',
    	'level_1_name',
    	'apply_time',
    	'op_time',
    	'result',
    	'ext',
    );
    /**
     * [_buildApplyConds description]
     * @param  [type]  $arrInput [description]
     * @param  boolean $update   [description]
     * @return [type]            [description]
     */
    private static function _buildConds($arrInput,$update = false){
        $conds = array();
        self::getInteger($conds, $arrInput, 'apply_id');
        if(!$update){
            self::getInteger($conds, $arrInput, 'forum_id');
            self::getInteger($conds, $arrInput, 'user_id');
            self::getInteger($conds, $arrInput, 'op_user_id');
            self::getStr($conds, $arrInput, 'op_user_name');
            self::getStr($conds, $arrInput, 'level_1_name');
            self::getRange($conds, $arrInput, 'apply_time');
            self::getRange($conds, $arrInput, 'op_time');
            self::getInteger($conds, $arrInput, 'result');
            self::getStringLike($conds, $arrInput, 'ext');
        }
        if(empty($conds)){
            return ' ';
        }
        $strConds = 'WHERE ' . join(' AND ', $conds);
        return $strConds;
    }
    /**
     * [_getFields description]
     * @param  boolean $bolWithPrimaryKey [description]
     * @return [type]                     [description]
     */
    private static function _getFields($bolWithPrimaryKey = false){
        $fields =  self::$_fields;
        if(!$bolWithPrimaryKey){
                // 去掉主键
                array_shift($fields);
        }
        return $fields;
    }
    /**
     * @brief
     * @param:
     *      uint32_t user_id
     *      uint32_t offset
     * @return: $arrOutput
     *      out_info data[]
     */
    public static function select($arrInput){
        $fields = self::_getFields(true);
        $conds  = self::_buildConds($arrInput);
        $append = self::_buildAppend($arrInput);
        $strSql = sprintf("SELECT %s FROM %s %s %s", join(',', $fields), self::$_table, $conds, $append);
        $arrRet = self::_queryDB($strSql, __FUNCTION__ );
        if ( $arrRet === false ){
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_succRet($arrRet);
    }
    /**
     * @brief
     * @param:
     *      uint32_t user_id
     *      uint32_t offset
     * @return: $arrOutput
     *      out_info data[]
     */
    public static function selectCnt($arrInput){
        $fields = array("COUNT(1)  as cnt");
        $conds  = self::_buildConds($arrInput);
        $append = ' ';//self::_buildAppend($arrInput);
        $strSql = sprintf("SELECT %s FROM %s %s %s", join(',', $fields), self::$_table, $conds, $append);
        $arrRet = self::_queryDB($strSql, __FUNCTION__ );
        if ( $arrRet === false ){
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_succRet($arrRet);
    }
    /**
     * [insertApply description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function insert($arrInput){
        $fields = array(
            'forum_id',
            'user_id',
            'level_1_name',
            'apply_time',
            'ext',
        );
        $values = self::_buildInsertValue($arrInput, $fields);
        $strSql  = sprintf("INSERT INTO %s(%s) VALUES %s", self::$_table, join(',', $fields), $values);
        $arrRet = self::_queryDB($strSql, __FUNCTION__ );
        if ( $arrRet === false ){
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_succRet($arrRet);
    }
    /**
     * [updateApply description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function update($arrInput){
        $fields = self::_getFields(false);
        $conds = self::_buildConds($arrInput, true);
        $values = self::_buildUpdateValue($arrInput, $fields);
        $strSql = sprintf("UPDATE %s SET %s  %s", self::$_table, $values, $conds);
        $arrRet = self::_queryDB($strSql, __FUNCTION__ );
        if ( $arrRet === false ){
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_succRet($arrRet);
    }
    
    /**
     * [_buildInsertValue description]
     * @param  [type] $arrInput [description]
     * @param  [type] $fields   [description]
     * @return [type]           [description]
     */
    private static function _buildInsertValue($arrInput, $fields){
        $arrAll = array();
        foreach($arrInput as $input){
            $arrValues = array();
            foreach($fields as $field){
                $arrValues[] = $input[$field];
            }
            $strValue = "('" . join("','", $arrValues) . "')";
            $arrAll []= $strValue;
        }
        $strAll = join(",", $arrAll);
        return $strAll;
    }
    /**
     * [_buildUpdateValue description]
     * @param  [type] $arrInput [description]
     * @param  [type] $fields   [description]
     * @return [type]           [description]
     */
    private static function _buildUpdateValue($arrInput, $fields){
        $arrValues = array();
        foreach($fields as $field){
            if(isset($arrInput[$field])){
                $arrValues[] = " " . $field . "= '" . $arrInput[$field] . "' ";
            }
        }
        $strValue = join(',', $arrValues);
        return $strValue;
    }
    /**
     * [_buildAppend description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    private static function _buildAppend($arrInput){
        $intNeedHasMore = (int)$arrInput['need_has_more'];
        $append = ' ';
        if(isset($arrInput['rn'])){
            $intSize = (int)$arrInput['rn'];
            if(isset($arrInput['pn'])){
                $intLimit = ((int)$arrInput['pn'] - 1) * $intSize;
            }else{
                $intLimit = 0;
            }
            $append = sprintf(" LIMIT %d, %d ", $intLimit, $intSize + $intNeedHasMore);
        }
        if(isset($arrInput['orderby'])){
            $strOrderBy = ' order by '.$arrInput['orderby']['field'].' '.$arrInput['orderby']['sort'].' ';
        }
        if($strOrderBy){
            $append = $strOrderBy.$append;
        }
        return $append;
    }
    /**
     * [_queryDB description]
     * @param  [type] $strSql   [description]
     * @param  string $strTimer [description]
     * @return [type]           [description]
     */
    private static function _queryDB($strSql, $strTimer = 'db_query'){
        $strTimer = 'db_' . $strTimer .'_'.  __CLASS__ ;
        $objDB = self::_getDB();
        if ( !$objDB ){
            Bingo_Log::warning('fail to get db');
            return false;
        }
        Bingo_Timer::start($strTimer);
        $arrRet = $objDB->query($strSql);
        Bingo_Timer::end($strTimer);
        if ( $arrRet === false ){
            $strLastError = $objDB->error();
            Bingo_Log::warning("execute sql error [$strSql] [$strLastError]");
        }
        return $arrRet;
    }
    /**
     * [getInt description]
     * @param  [type] &$conds   [description]
     * @param  [type] $condKey  [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @return [type]           [description]
     */
    public static function getInteger(&$conds, $arrInput, $key) {
        if(isset($arrInput[$key])) {
            if(!is_array($arrInput[$key])){
                $conds [] = $key . '=' . (int)$arrInput[$key];
            }else{
                $conds [] = sprintf("%s IN(%s)", $key, implode(',',$arrInput[$key]));
            }
        }
    }
    /**
     * [getString description]
     * @param  [type] &$conds   [description]
     * @param  [type] $condKey  [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @return [type]           [description]
     */
    public static function getStr(&$conds, $arrInput, $key) {
        if(isset($arrInput[$key])) {
            $conds [] = $key . "='" . trim($arrInput[$key])."'";
        }
    }
    /**
     * [getStringLike description]
     * @param  [type] &$conds   [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @return [type]           [description]
     */
    public static function getStringLike(&$conds, $arrInput, $key) {
        if(isset($arrInput[$key])) {
            $conds [] = $key . " LIKE '%" . trim($arrInput[$key]) ."%'";
        }
    }
    /**
     * [getRange description]
     * @param  [type] &$conds   [description]
     * @param  [type] $condKey  [description]
     * @param  [type] $arrInput [description]
     * @param  [type] $key      [description]
     * @return [type]           [description]
     */
    public static function getRange(&$conds,$arrInput, $key) {
        if(isset($arrInput[$key])){
            if(!is_array($arrInput[$key])){
                $conds[] = $key . '=' . $arrInput[$key];
            }else{
                if(isset($arrInput[$key]['max'])){
                    $conds[] = $key . '<' . $arrInput[$key]['max'];
                }
                if(isset($arrInput[$key]['min'])){
                    $conds[] = $key . '>' . $arrInput[$key]['min'];
                }
                if(isset($arrInput[$key]['maxe'])){
                    $conds[] = $key . '<=' . $arrInput[$key]['maxe'];
                }
                if(isset($arrInput[$key]['mine'])){
                    $conds[] = $key . '>=' . $arrInput[$key]['mine'];
                }
            }
        }
    }
    /**
     * @brief get mysql obj.
     * @param
     * @return: obj of Bd_DB, or null if connect fail.
    **/
    private static function _getDB(){
        if(self::$_db){
            return self::$_db;
        }
        $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if($objTbMysql && $objTbMysql->isConnected()) {
            $objTbMysql -> charset('utf8');
            self::$_db = $objTbMysql;
            return $objTbMysql;
        } else {
            Bingo_Log::fatal("db connect fail. db=" . self::DATABASE_NAME);
            return null;
        }
        return self::$_db;
    }
    /**
     * [_succRet description]
     * @param  [type] $ret [description]
     * @return [type]      [description]
     */
    protected static function _succRet($ret){
        $error = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data'   => $ret,
        );
    }

    /**
     * [_errRet description]
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    protected static function _errRet($errno){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

}
