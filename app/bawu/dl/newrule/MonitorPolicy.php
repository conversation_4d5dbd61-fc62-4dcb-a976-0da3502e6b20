<?php
/**
 * created by zhangyuxuan02.
 * email: <EMAIL>
 * file name: BawuPolicy.php
 * create time: 2021-12-13
 * describe: 吧务策略数据
 */

class Dl_Newrule_MonitorPolicy {
    const SERVICE_NAME = 'Dl_newrule_monitorpolicy';
    const DATABASE_NAME = 'DB_forum_newbawu';
    const MODULE_NAME = 'bawu';
    const DB_CHARSET = 'gbk';

    protected static $_db = null;
    protected static $_conf = null;
    protected static $_use_split_db = false;
    protected static $mdb = null;

    /**
     * exec sql method
     * @param $arrInput
     * @return array
     */
    public static function execSql($arrInput) {
        if(!isset($arrInput['function'])) {
            Bingo_Log::warning('input params invalid: function is empty. [' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $ret = self::_init();
        if($ret !== true) {
            return $ret;
        }
        $mdb = self::_initMDB();
        if(false === $mdb) {
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $arrOut = $mdb->execSql($arrInput);
        return $arrOut;
    }

    /**
     * 获取数据库实例
     * @return bool|null
     */
    private static function _getDB() {
        if(self::$_db) {
            return self::$_db;
        }

        self::$_db = new Bd_DB();
        if(self::$_db == null) {
            Bingo_Log::warning('new bd_db fail.');
            return null;
        }

        Bingo_Timer::start('dbinit');
        $ret = self::$_db->ralConnect(self::DATABASE_NAME);
        Bingo_Timer::end('dbinit');
        if(!$ret) {
            Bingo_Log::warning('bd db ral connect fail.');
            self::$_db = null;
            return null;
        }
        self::$_db->charset(self::DB_CHARSET);
        return true;
    }

    /**
     * 初始化数据库实例及加载配置文件
     * @return array|bool true if success. false if fail.
     */
    private static function _init() {
        if(self::_getDB() == null) {
            Bingo_Log::warning("init db fail.");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        if(self::$_conf == null) {
            $dlConfFile = '/app/' . self::MODULE_NAME . '/'. strtolower(self::SERVICE_NAME);
            self::$_conf = Bd_Conf::getConf($dlConfFile);
            if(self::$_conf == false) {
                Bingo_Log::warning('init get conf fail.' . $dlConfFile);
                return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
            }
        }
        return true;
    }

    /**
     * 初始化单例db
     * @return bool|Molib_Store_DB|null
     */
    private static function _initMDB() {
        if (null === self::$mdb) {
            Bingo_Timer::start('initlib');
            self::$mdb =  new Molib_Store_DB(self::$_db, self::$_conf, self::DB_CHARSET);
            Bingo_Timer::end('initlib');
            if(!isset(self::$mdb)) {
                Bingo_Log::warning('new lib_db fail.');
                return false;
            }
        }

        return self::$mdb;
    }

    /**
     * 获取到db实例
     * @return array|bool|Molib_Store_DB|null
     */
    public static function getDBInstance() {
        // 初始化db配置
        $ret = self::_init();
        if($ret !== true) {
            return $ret;
        }
        // 初始化mdb实例
        $mdb = self::_initMDB();
        return $mdb;
    }

    /**
     * @param $errno
     * @param array $data
     * @return array
     */
    private static function _errRet($errno = 0, $data = array()) {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        );
    }
}