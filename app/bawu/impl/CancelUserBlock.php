<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/

/**
 * @file CancelUserBlock.php
 * <AUTHOR> @date 
 * @version $Revision$ 
 * @brief ȡ������û�
 * 
 **/

class Impl_CancelUserBlock extends Impl_Base {
    
    const CMD_CORE_NO = 214;
    
    public function __construct() {
        $this->intCmdCoreNo = self::CMD_CORE_NO;
    }

    public function process($arrParams) {
        if (isset($arrParams[0])) {
            foreach ($arrParams as &$val) {
                $val = $this->_buildParams($val);
            }
            $this->_setParams($arrParams, true);
        } else {
            $arrParams = $this->_buildParams($arrParams);
            $this->_setParams($arrParams);
        }
    }
    
    public function _buildParams($arrParams) {
        $rt = array();
        foreach (self::$ALL_FIELDS as $key => $val) {
            if (isset($arrParams[$key])) {
                if ($val === 'str') {
                    $rt[$key] = strval($arrParams[$key]);
                } elseif ($val === 'int') {
                    $rt[$key] = intval($arrParams[$key]);
                } else {
                }
            } else {
                $rt[$key] = ($val === 'int') ? 0 : '';
            }
        }
        return $rt; 
    }
}