<?php

if (empty($argv[1])) {
    echo '请添加文件名参数，如:'.PHP_EOL.'php deleteWhiteList.php fids.txt'.PHP_EOL;
    exit(-1);
}

$strContent = file_get_contents('fids.txt');
file_put_contents('failed_fids.txt', '');
$arrFids = explode(PHP_EOL, $strContent);
$intCount = 0;
$intTotal = count($arrFids);
foreach ($arrFids as $intFid) {
    show_status(++$intCount, $intTotal);

    if ($intFid === 0) {
        continue;
    }
    $arrInput = array(
        'forum_id' => $intFid,
    );
    $arrOutput = Tieba_Service::call('managerapply','delNewruleWhiteForumByFid',$arrInput);
    if (false === $arrOutput || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
        file_put_contents('failed_fids.txt', implode(PHP_EOL, $arrSubFids).PHP_EOL, FILE_APPEND);
        file_put_contents('deleteWhiteList.log', 'call managerapply addNewruleWhiteForum failed. [input: '.serialize($arrInput).'] [output: '.serialize($arrOutput).']'.PHP_EOL, FILE_APPEND);
        continue;
    }

    $arrReq = array(
        'forum_id' => $forumId
    );
    $arrRes = Tieba_Service::call('bawu', 'openManagerApply', $arrReq, null, null, 'post', 'php', 'utf-8');
    if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
        file_put_contents('failed_fids.txt', implode(PHP_EOL, $arrSubFids).PHP_EOL, FILE_APPEND);
        file_put_contents('deleteWhiteList.log', 'call managerapply openManagerApply failed. [input: '.serialize($arrReq).'] [output: '.serialize($arrRes).']'.PHP_EOL, FILE_APPEND);
    }
}

function show_status($done, $total, $size=30) {
    static $start_time;

    // if we go over our bound, just ignore it
    if($done > $total) return;

    if(empty($start_time)) $start_time=time();
    $now = time();

    $perc=(double)($done/$total);

    $bar=floor($perc*$size);

    $status_bar="\r[";
    $status_bar.=str_repeat("=", $bar);
    if($bar<$size){
        $status_bar.=">";
        $status_bar.=str_repeat(" ", $size-$bar);
    } else {
        $status_bar.="=";
    }

    $disp=number_format($perc*100, 0);

    $status_bar.="] $disp%  $done/$total";

    $rate = ($now-$start_time)/$done;
    $left = $total - $done;
    $eta = round($rate * $left, 2);

    $elapsed = $now - $start_time;

    $status_bar.= " remaining: ".number_format($eta)." sec.  elapsed: ".number_format($elapsed)." sec.";

    echo "$status_bar  ";

    flush();

    // when done, send a newline
    if($done == $total) {
        echo "\n";
    }

}
