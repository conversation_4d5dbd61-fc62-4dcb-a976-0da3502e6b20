<?php
/**
 * Created by PhpStorm.
 * User: huxiaomei
 * Date: 2019/6/13
 * Time: 下午8:56
 */

ini_set ( "memory_limit", "2G"  );
//define ( 'MODULE_NAME', 'postaudit' );
define ( 'ROOT_PATH', dirname ( __FILE__  ) . "/../../.."  );
define ( 'HOME_PHP_PATH', realpath ( ROOT_PATH . '/php/bin/php'  )  );
define ( 'IS_ORP_RUNTIME', true  );
Tieba_Init::init ( 'bawu');
define('NAME_SCRIPT', 'bawutask_sendUnsendSms');
Bingo_Log::init(array(
    LOG => array(
        'file'  => dirname ( __FILE__ ) . '/../../../../log/app/bawu/script/bawutask/'.NAME_SCRIPT.'_'.date('Ymd').'.log',
        'level' => 0x04 | 0x02,
    ),
), LOG);
echo dirname ( __FILE__ ) . '/../../../../log/app/bawu/script/bawutask/'.NAME_SCRIPT.'_'.date('Ymd').'.log';
if (! defined ( 'REQUEST_ID'  )) {
    define ( 'REQUEST_ID', Bingo_Log::getLogId ()  );
}
if (function_exists ( 'camel_set_logid'  )) {
    camel_set_logid ( REQUEST_ID  );
}
require_once '../../lib/Message.php';
require_once '../../lib/NewRule.php';
require_once '../../lib/Redis.php';
class Script_sendUnsendSms{
    protected static $_db = null;
    protected static $_charset = 'utf8';
    const DB_RAL_SERVICE_NAME = "DB_forum_newbawu";
    const PER_NUMBER = 10000;
    CONST OP_OFF_UNAME = 'check_bawu';
    CONST OP_OFF_UID = '3447387038'; //上下任吧主使用的账号
    /**
     * _getDB db
     * @param string fid
     * @return  array / false
     **/
    private static function _getDB() {
        if(self::$_db){
            return self::$_db ;
        }
        self::$_db = new Bd_DB();
        if(self::$_db == null){
            echo "new bd_db Fail.\n";
            return null;
        }
        Bingo_Timer::start('dbinit');
        $r = self::$_db->ralConnect(self::DB_RAL_SERVICE_NAME);
        Bingo_Timer::end('dbinit');
        if(!$r){
            echo "bd db ral connect Fail.\n";
            self::$_db = null;
            return null;
        }
        self::$_db->charset(self::$_charset);
        return self::$_db;
    }

    /**
     * getBawuOffInfo 获取BawuOffInfo
     * @param string fid
     * @return  array / false
     **/
    public static function getChecktaskRecord($db,$checkDate,$checkWeekDate,$needReadmsg,$startNum,$end1,$num)
    {
        if (!$db->isConnected) {
            $db->ralConnect(self::DB_RAL_SERVICE_NAME);
        }
        $strSql = "select id,forum_id,is_check_manager,user_id,fail_type,left_score,is_offline from bawu_checktask_record where check_date =$checkDate AND check_week_date=$checkWeekDate AND need_notice=1 and left_score=1 AND id BETWEEN $startNum AND $end1 group by user_id limit $num;";
//        echo $strSql."\n";
        Bingo_Log::warning($strSql);
        $ret1 = $db->query($strSql);
        if ($ret1 !== false) {
            Bingo_Log::warning('getChecktaskRecord_execsql '.$strSql.' execute output '.json_encode($ret1));
            $ret = array_merge($ret1);
            return $ret;
        } else {
            Bingo_Log::warning($strSql.' execute output '.json_encode($ret1));
            return false;
        }
    }

    /**
     * getChecktaskRecordMaxid 获取BawuOffInfo
     * @param string fid
     * @return  array / false
     **/
    public static function getChecktaskRecordMaxid($db,$checkDate,$checkWeekDate,$needNotice)
    {
        if (!$db->isConnected) {
            $db->ralConnect(self::DB_RAL_SERVICE_NAME);
        }
        $strSql = "select max(id) as max_id from bawu_checktask_record where check_date =$checkDate AND check_week_date=$checkWeekDate AND need_notice=$needNotice;";
//        echo $strSql."\n";
        Bingo_Log::warning($strSql);
        $ret1 = $db->query($strSql);
        if ($ret1 !== false) {
            Bingo_Log::warning('getChecktaskRecord_execsql '.$strSql.' execute output '.json_encode($ret1));
            $ret = array_merge($ret1);
            return $ret;
        } else {
            Bingo_Log::warning($strSql.' execute output '.json_encode($ret1));
            return false;
        }
    }

    /**
     * getChecktaskRecordMinid 获取BawuOffInfo
     * @param string fid
     * @return  array / false
     **/
    public static function getChecktaskRecordMinid($db,$checkDate,$checkWeekDate,$needNotice)
    {
        if (!$db->isConnected) {
            $db->ralConnect(self::DB_RAL_SERVICE_NAME);
        }
        $strSql = "select min(id) as min_id from bawu_checktask_record where check_date =$checkDate AND check_week_date=$checkWeekDate AND need_notice=$needNotice;";
//        echo $strSql."\n";
        Bingo_Log::warning($strSql);
        $ret1 = $db->query($strSql);
        if ($ret1 !== false) {
            Bingo_Log::warning('getChecktaskRecord_execsql '.$strSql.' execute output '.json_encode($ret1));
            $ret = array_merge($ret1);
            return $ret;
        } else {
            Bingo_Log::warning($strSql.' execute output '.json_encode($ret1));
            return false;
        }
    }

    /**
     * updateChecktaskRecord 更新BawuOffInfo
     * @param string fid
     * @return  array / false
     **/
    public static function updateChecktaskRecord($db,$id,$needNotice)
    {
        if (!$db->isConnected) {
            $db->ralConnect(self::DB_RAL_SERVICE_NAME);
        }
        $strSql = "update bawu_checktask_record set need_notice=$needNotice WHERE id=$id;";
        Bingo_Log::warning($strSql);
        $ret1 = $db->query($strSql);
        if ($ret1 !== false) {
            Bingo_Log::warning('getChecktaskRecord_execsql '.$strSql.' execute output '.json_encode($ret1));
            $ret = array_merge($ret1);
            return $ret;
        } else {
            Bingo_Log::warning($strSql.' execute output '.json_encode($ret1));
            return false;
        }
    }

    /**
     * @param string fid
     * @return  array / false
     **/
    public function process($start,$end) {
        $sleepTime = 300000;
        /**
         * 获取信息 start
         */
        //step1
        //获取时间
        $weekDay = date('w',time());//取星期几
        $weekDay = $weekDay == 0 ? 7 : $weekDay;
        $diffFirstDay = '-'.($weekDay - 1).' day';
        $checkWeekDate = strtotime(date('Y-m-d 00:00:00', strtotime($diffFirstDay))) - 7*86400;
        $checkEndDate = $checkWeekDate + 6*86400;
        $checkDate = strtotime(date('Y-m',$checkEndDate));
        echo "\n".checkDate.'_'.date('Y-m-d H:i:s',$checkDate)."\n";
        echo "checkWeekDate_".date('Y-m-d H:i:s',$checkWeekDate)."\n";
        $needReadmsg = 1;
        //step2 获取上周未发送的消息
        $db = self::_getDB();

        $fileName = dirname ( __FILE__ ) . '/../../../../log/app/bawu/script/bawutask/'.NAME_SCRIPT.'_'.$checkDate.'_'.$start.'_'.$end;
        $file = $fileName.'.log';
        $doneFile = $fileName.'_done.log';
        if (!file_exists($file) && !file_exists($doneFile)){
            file_put_contents($file,$start.'_'.$end."_start\n",FILE_APPEND);
        }else{
            if ($start == 0){
                echo $checkDate.'_'.$start.'_'.$end." has existed!\n";
                Bingo_Log::warning(" $file or $doneFile has existed! ");
                return false;
            }
        }
        echo $file."\n";
        //分多次取数据、防止数量特别大时超时
        $num = self::PER_NUMBER;
        echo "deal ".$checkDate.'_'.$start.'_'.$end." data start\n";
        file_put_contents($file,$start.'_'.$end."_start\n",FILE_APPEND);
        //获取上周未发送的消息
        $checkRecords = self::getChecktaskRecord($db,$checkDate,$checkWeekDate,$needReadmsg,$start,$end,$num);
        file_put_contents($file,$start."_".$end."_".$num."_start_total_count_".count($checkRecords)."\n",FILE_APPEND);
        if (false === $checkRecords){
            $k = 0;
            while($k < 2){
                $checkRecords = self::getChecktaskRecord($db,$checkDate,$checkWeekDate,$needReadmsg,$start,$end,$num);
                if (false !== $checkRecords){
                    break;
                }
                $k++;
            }
        }
        if (false === $checkRecords){
            Bingo_Log::warning('self::getChecktaskRecord fail'.$checkDate.'_'.$checkWeekDate.'_'.$needReadmsg.' output'.json_encode($checkRecords));
            echo "get check_task_records fail";
            return false;
        }else if (empty($checkRecords)){
            Bingo_Log::warning('self::getChecktaskRecord fail'.$checkDate.'_'.$checkWeekDate.'_'.$needReadmsg.' output'.json_encode($checkRecords));
            echo "get check_task_records success, no data! end!";
            return false;
        }
        $retCount = count($checkRecords);
        echo "total_count_".$retCount."\n";
        echo "deal checkRecords start! \n";
        $j = 0;
        file_put_contents($file,$start.'_'.$end."_end\n",FILE_APPEND);
        foreach ($checkRecords as $checkRecord){
            //延时
            $j++;
            if ($j % 5 == 0){
                usleep($sleepTime);
//                    sleep(1);
            }
            file_put_contents($file,$start."_".$j."\n",FILE_APPEND);
//            var_dump($checkRecord);
            $forumId = $checkRecord['forum_id'];
            $userId = $checkRecord['user_id'];
            $failType = $checkRecord['fail_type'];
            $role = $checkRecord['is_check_manager'] ? 'manager' : 'assist';
            $leftScore = $checkRecord['left_score'];
            $isOffline = $checkRecord['is_offline'];
            $id = $checkRecord['id'];
//                if ($leftScore == 1){
//                    file_put_contents($oneScoreFile,"$forumId.'_'.$userId.'_'.$id\n");
//                    continue;
//                }
            /**
             * 获取短信
             */
            //下任的则直接continue，不处理
            if ($isOffline){
                Bingo_Log::warning($failType.'_'.$role.'_'.$isOffline.'_'.$leftScore.' unsend Msg is offline!');
                continue;
            }
            //获取吧名
            //获取吧名
            $forumName = '';
            $arrReq = array(
                'forum_id' => array($forumId)
            );
            $arrRes = Tieba_Service::call('forum','getFnameByFid',$arrReq, null, null, 'post', 'php', 'utf-8');
            if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']) {
                Bingo_Log::warning('call forum::getFnameByFid fail! input[' . json_encode($arrReq) . '][output]' . json_encode($arrRes));
            } else {
                //判断吧是否存在 todo list 是否需要，前面已判断吧主是否存在，实际已判断
                        $fnameInfo = isset($arrRes['forum_name'][$forumId]) ? $arrRes['forum_name'][$forumId] : array();
                        if (empty($fnameInfo) || !isset($fnameInfo['exist']) || $fnameInfo['exist'] != 1 || !isset($fnameInfo['forum_name']) || empty($fnameInfo['forum_name'])) {
                            Bingo_Log::warning('addForumInfo_get_fname_byfid call forum::getFnameByFid ERR_FORUM_NOT_EXIST! input[' . json_encode($arrReq) . '][output]' . json_encode($arrRes));
                            continue;
                        }
                $forumName = isset($arrRes['forum_name'][$forumId]['forum_name']) && !empty($arrRes['forum_name'][$forumId]['forum_name']) ? $arrRes['forum_name'][$forumId]['forum_name'] : '';
            }
            //发送通知
            if (empty($forumName)){
                $forumName = '贴';
            }
            Bingo_Log::warning($forumName.'_'.$userId.'_'.$failType.'_'.$role.'_'.$isOffline.'_'.$leftScore.' send sms start!');
            echo $forumName.'_'.$userId.'_'.$failType.'_'.$role.'_'.$isOffline.'_'.$leftScore."\n";
            $smsContent = sprintf(Lib_Message::$smsTaskWarManager,$forumName,$leftScore);
            Bingo_Log::warning('sendUnsendMsg_smsContentMsg'.$smsContent);
            file_put_contents('uid_list.txt',$userId."\n",FILE_APPEND);
            //发送短信
            $arrParams = array(
                'to_user_id' => $userId,
                'content' => $smsContent
            );
            var_dump($arrParams);
            $arrOut = Lib_Message::sendSms($arrParams);
            file_put_contents($file,$userId."_".serialize($arrRes)."\n",FILE_APPEND);
            Bingo_log::warning('sendUnsendMsg_ret [input]'.$userId.'_'.$smsContent.'_'.'[output]'.serialize($arrRes));
            if (false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
                Bingo_Log::warning(__METHOD__ . '-send-sms-message fail input:' . serialize($arrParams) . ' output:' . serialize($arrOut));
                continue;
            }
        }
        echo "deal ".$checkWeekDate.'_'.$start.'_'.$end." data end\n";
        echo "deal checkRecords end!\n";
        return true;
    }

    /**
     * [_errRet description]
     * @param  [type] $errno [description]
     * @param  array  $data  [description]
     * @return [type]        [description]
     */
    private static function _errRet($errno,$data=array()){
        $arrOutput = array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data'   => $data,
        );
        return $arrOutput;
    }

}


if (isset($argv[1]) && intval($argv[1]) >=0 && isset($argv[2]) && intval($argv[2]) >0) {
    $startIndex = intval($argv[1]) ;
    $endIndex = intval($argv[2]) ;
    if ($startIndex <= $endIndex && $endIndex > $startIndex + 10000){
        echo $startIndex."\n";
        echo $endIndex."\n";
        $obj = new Script_sendUnsendSms();
        $ret = $obj->process($startIndex,$endIndex);
        if($ret){
            exit(0);
        }
    }
}
?>