<?php
/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/6/26
 * Time: 10:04
 * IN USE
 */

define('NAME_SCRIPT', 'proc_delete_third_top');
require_once "MakeForumData.php";

function deleteThirdTop($strDate) {
    $arrAllFlag = MakeForumData::getAllFlag($strDate);
    //是否正在运行的
    $strDeleteThirdTopOnlineFlag = "delete_third_top_online_$strDate";
    if($arrAllFlag[$strDeleteThirdTopOnlineFlag]) {
        echo "the delete third top  online flag is exist";
        return true;
    }
    //生成正在执行的标记
    MakeForumData::setFlag($strDate,$strDeleteThirdTopOnlineFlag);
    $bolRet = MakeForumData::executeDelThirdTop();
    if($bolRet === false) {
        echo "delete third top  link failed\n";
        MakeForumData::delFlag($strDate,$strDeleteThirdTopOnlineFlag);
        return false;
    }
    MakeForumData::delFlag($strDate,$strDeleteThirdTopOnlineFlag);
    echo " delete top  success \n";
}

/*--start--*/
echo "start_time: " . date("Y-m-d H:i:s") . "\n";
$strDate = date("Ymd", time() - 3600 * 24 * 2);
if (!empty($argv[1]) && $argv[1] != 'd') {
    $strDate = intval($argv[1]);
}
if (!empty($argv[1]) && $argv[1] == 'd') {
    exec("rm -rf ./*.flag");
    return 0;
}
$bolRet = true;
if (!empty($argv[2])) {
    $intDays = intval($argv[2]);
    $intTime = strtotime($strDate);
    for ($i = 0; $i < $intDays; $i++) {
        $strDate = date("Ymd", $intTime - 3600 * 24 * $i);
        $bolRet = deleteThirdTop($strDate);
    }
} else {
    $bolRet = deleteThirdTop($strDate);
}

echo "end_time: " . date("Y-m-d H:i:s") . "\n";