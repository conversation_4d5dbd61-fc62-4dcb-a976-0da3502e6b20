<?php
/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/6/16
 * Time: 23:09
 * not in use
 */
@set_time_limit(0);
@ini_set("memory_limit", "2048M");
define ('ROOT_PATH', dirname(__FILE__) . "/..");
Tieba_Init::init("bawu");
require_once "MakeForumData.php";
function getRemoteFile($strFileName) {
    exec("wget cp01-forum-dm00.cp01.baidu.com:/home/<USER>/tbdc/data/tieba_rpt_forum_data_file/$strFileName ./ 2>&1 >/dev/null");
}
function delAllData($strDate) {
    $arrAllFlag = MakeForumData::getAllFlag($strDate);
    //是否正在运行的
    $strDeleteOnlineFlag = "delete_online_$strDate";
    $strDeleteFinishFlag = "delete_finish_$strDate";
    if($arrAllFlag[$strDeleteOnlineFlag]) {
        echo "the delete data online flag is exist";
        return true;
    }
    $strCoreFile = "forumdata_$strDate";
    $strCoreFilePath = dirname(__FILE__) . "/$strCoreFile";
    if($arrAllFlag[$strDeleteFinishFlag]) {
        echo "the delete finish flag is exist";
        return true;
    }
    @unlink($strCoreFile);
    getRemoteFile($strCoreFile);
    if (!file_exists($strCoreFile)) {
        echo "$strDate core file not exist.\n";
        return false;
    }
    //生成正在执行的标记
    MakeForumData::setFlag($strDate,$strDeleteOnlineFlag);
    $bolRet = MakeForumData::execute2($strCoreFilePath,$strDate);
    if($bolRet === false) {
        echo "delete date <= $strDate all data failed";
        MakeForumData::delFlag($strDate,$strDeleteOnlineFlag);
        return false;
    }
    MakeForumData::setFlag($strDate,$strDeleteFinishFlag);
    MakeForumData::delFlag($strDate,$strDeleteOnlineFlag);
    echo "delete date <= $strDate all data success";
}

/*--start--*/
echo "start_time: " . date("Y-m-d H:i:s") . "\n";
$strDate = date("Ymd", time() - 3600 * 24 * 32);
if (!empty($argv[1]) && $argv[1] != 'd') {
    $strDate = intval($argv[1]);
}
if (!empty($argv[1]) && $argv[1] == 'd') {
    exec("rm -rf ./*.flag");
    return 0;
}
$bolRet = true;
if (!empty($argv[2])) {
    $intDays = intval($argv[2]);
    $intTime = strtotime($strDate);
    for ($i = 0; $i < $intDays; $i++) {
        $strDate = date("Ymd", $intTime - 3600 * 24 * $i);
        $bolRet = delAllData($strDate);
    }
} else {
    $bolRet = delAllData($strDate);
}

echo "end_time: " . date("Y-m-d H:i:s") . "\n";
