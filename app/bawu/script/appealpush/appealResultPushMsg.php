<?php
/**
 * Created by PhpStorm.
 * Date: 2020/11/21
 * Time: 17:35
 * IN USE
 */

define ('ROOT_PATH', dirname(__FILE__) . "/..");

define ('BASEPATH',dirname(__FILE__));
define('LOGPATH', BASEPATH.'/log');
ini_set('date.timezone','PRC');
Tieba_Init::init("bawu");
define('NAME_SCRIPT', 'appealResultPushMsg');
Bingo_Log::init(array(
    LOG => array(
        'file'  => dirname ( __FILE__ ) . '/../../../../log/app/bawu/script/appealpush/'.NAME_SCRIPT.'.log',
        'level' => 0x04 | 0x02,
    ),
), LOG);



class Script_Appealpush_AppealResultPushMsg{

    const APPEAL_LIST_STATUS_INIT = 0; //初始
    const APPEAL_LIST_STATUS_NOTPUSH = 2; //不发送
    
    const TASK_ID     = "000000";
    const SERVICE_ID  = "106";
    const BAZHU_UID   = '5044059141';
    const BAZHU_UNAME = '吧主通知';
    const MSG_TITLE   = '你有待办事项请尽快处理';
     
    public function execute(){
        
        $time = strtotime("-10 day");
        $condition = ' status = '.self::APPEAL_LIST_STATUS_INIT .' and modify_time>='.$time;
        $arrReq = array(
            'condition'  => $condition,
        );
        $arrRes = Tieba_Service::call('bawu', 'getAppealPushNum', $arrReq, null, null, 'post', 'php', 'utf-8');
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
            Bingo_Log::warning('call bawu::getAppealPushNum fail . input['.serialize($arrReq).'].output[' . serialize($arrRes));
            return false;
        }
      
        $intCount = intval($arrRes['data']['count']);
        $rn = 500; 
        $allPages = intval(ceil($intCount/$rn));
       
        //分页取出待确认数据
        $objMulti     = new Molib_Tieba_Multi('getAppealPushListMul');
        for($pn=1;$pn<=$allPages;$pn++){
            $getMulKey = 'getAppealPushList_'.$pn;
           
            $offset = ($pn-1)*$rn;
            $limit = $offset.' , '.$rn;
            $conditonSql = $condition.' order by id desc limit '.$limit;
            $arrMultiInputGet[$getMulKey] = array(
                'serviceName' => 'bawu',
                'method'      => 'getAppealPushList',
                'input'       => array(
                    'condition' => $conditonSql,
                ),
                'format'      => 'php',
                'ie'          => 'utf-8',
            );
            $objMulti->register($getMulKey, $arrMultiInputGet[$getMulKey]);
           
        }
        
        $objMulti->call();
        $arrAllResult = $objMulti->getAllResult();
        $arrAppealPushData = array();
        foreach ($arrAllResult as $strKey => $arrOutput) {
            if (!$arrOutput || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning(sprintf("call %s failed! input[%s] output[%s]", $strKey, serialize($arrMultiInputGet[$strKey]), serialize($arrOutput)));
            }
           
            $arrAppealPushData = array_merge($arrAppealPushData,$arrOutput['data']['res']); 
        }
    
        //获取文案并发送消息
        self::getWordAndPush($arrAppealPushData);

    }

    /**
     * 获取文案并发送消息
     * @param type $arrAllAppealPushData
     * @return boolean
     */
    private static function getWordAndPush($arrAllAppealPushData){
        $arrAppealPush = array_chunk($arrAllAppealPushData,10);  
        foreach ($arrAppealPush as $key=> $arrAppealPushData){
            $objMulti     = new Molib_Tieba_Multi('judgeSendMessageMul'.$key); //根据fid获取push文案
            $objMultiGet  = new Molib_Tieba_Multi('getBawuList'.$key); //根据fid获取吧主uid
            foreach($arrAppealPushData as $k => $val){
                $intForumId   = $val['forum_id'];
                $getMulKey    = 'judgeSendMessage_'.$k;
                $getUidMulKey = 'getBawuList_'.$k;
                
                $arrMultiInput[$getMulKey] = array(
                    'serviceName' => 'bawu',
                    'method'      => 'judgeSendMessage',
                    'input'       => array(
                        'forum_id'     => $intForumId,
                    ),
                    'format'      => 'php',
                    'ie'          => 'gbk',
                );
                $arrMultiInputGet[$getUidMulKey] = array(
                    'serviceName'  => 'perm',
                    'method'       => 'getBawuList',
                    'input'        => array(
                        'forum_id' => $intForumId,
                    ),
                    'format'       => 'php',
                    'ie'           => 'utf-8',
                );
                
                $objMulti->register($getMulKey, $arrMultiInput[$getMulKey]);
                $objMultiGet->register($getUidMulKey, $arrMultiInputGet[$getUidMulKey]);
                
            }
            $objMulti->call();
            $objMultiGet->call();
            
            foreach ($arrAppealPushData as $k => $val){
                //获取发送文案
                $getMulKey = 'judgeSendMessage_'.$k;
                $arrResult = $objMulti->getResult($getMulKey);
                if (!$arrResult || $arrResult['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('call bawu::judgeSendMessage fail. key='.$getMulKey. ' fid='.$val['forum_id']. ' output='. serialize($arrResult));
                    continue;
                }
                
                $strPushWord = $arrResult['ret']; 
                $arrAppealPushData[$k]['push_msg'] = !empty($strPushWord) ? $strPushWord : ''; 
                
                //获取吧主uid
                $getUidMulKey = 'getBawuList_'.$k;
                $arrRes = $objMultiGet->getResult($getUidMulKey);
                if (!$arrRes || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('call perm::getBawuList fail. key='.$getUidMulKey. ' fid='.$val['forum_id']. ' output='. serialize($arrRes));
                    continue;
                }
                $arrManager  = $arrRes['output']['manager']; 
                $arrBazhuUid = array();
                foreach ($arrManager as $kk=>$bazhu){
                    $arrBazhuUid[$kk] = strval($bazhu['user']['user_id']);
                }
                
                $arrAppealPushData[$k]['push_to_uid'] = !empty($arrBazhuUid) ? $arrBazhuUid : array(); 
            }
            
            //发送消息并更新表状态
            self::pushAndUpdateData($arrAppealPushData);
            
            sleep(10); 
        }
        return true;
    }
    
    
    private static function pushAndUpdateData($arrAppealPushData){
        
        
        foreach ($arrAppealPushData as $k => $val){
            
            //文案为空，则代表未满足发送消息的条件，将表状态值记录为2不发送
            if(empty($val['push_msg'])){
                $condition = ' id = '.$val['id'];
                $arrReq = array(
                    'status' => 2,
                    'condition'  => $condition,
                );
                $arrRes = Tieba_Service::call('bawu', 'updateAppealPushStatus', $arrReq, null, null, 'post', 'php', 'utf-8');
                if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']){
                    Bingo_Log::warning('call bawu::updateAppealPushStatus fail . input['.serialize($arrReq).'].output[' . serialize($arrRes));
                }
            }
            
            //发消息 更新表
            if(!empty($val['push_msg']) && !empty($val['push_to_uid'])){
                //发消息
                $intPushResFlag = 1;
                $arrInput = array(
                    'title' => self::MSG_TITLE,
                    'content'   => $val['push_msg'],
                    'url'       => 'flt://ForumAppealPage?_forumId='.$val['forum_id'],
                    'uid'       => $val['push_to_uid'],
                    'user_id'   => self::BAZHU_UID,
                    'user_name' => self::BAZHU_UNAME,
                    'task_id'   => self::TASK_ID,
                    'service_id' => self::SERVICE_ID,
                    'user_type' => 4,
                    'msg_type'  => 1,
                );
                $bolPushRes = self::_pushMsg($arrInput);
                if(!$bolPushRes){
                    Bingo_Log::warning('push fail. input='. serialize($arrInput).'output='.$bolPushRes);
                    $intPushResFlag = 2;
                }
                
                //更新表
                $cond = ' id = '.$val['id'];
                $arrReq = array(
                    'push_result' => $intPushResFlag,
                    'condition'   => $cond,
                );
                $arrUpdateRes = Tieba_Service::call('bawu', 'updateAppealPushResult', $arrReq, null, null, 'post', 'php', 'utf-8');
                if (false === $arrUpdateRes || Tieba_Errcode::ERR_SUCCESS != $arrUpdateRes['errno']){
                    Bingo_Log::warning('call bawu::updateAppealPushStatus fail . input['.serialize($arrReq).'].output[' . serialize($arrUpdateRes));
                }
            }
            
            sleep(1); 
        }
        return true;
    }

     
     /**
     * 发送push消息+端内消息
     * 参数：
     * 'title'     标题，必填
     * 'content'   内容，必填
     * 'url'       链接，必填
     * 'image_url' 图片，可选
     * 'uid'       触达用户uid, 必填，为数组
     * 'user_id'   发送方uid, 必填
     * 'user_name' 发送方用户名，必填
     * 'task_id'   arch分配，必填
     * 'service_id'pm分配，可选
     * 'user_type' 用户类型，一般为4，可以基础库里面看用户类型对应的code
     * 'msg_type'  消息类型，1为文字，7为图文
     * @param $arrInput
     * @return array
     */
    private static function _pushMsg($arrInput) {
        if (!self::_checkParam($arrInput, 'task_id', 'title', 'content', 'url', 'uid', 'user_id', 'user_name', 'user_type', 'msg_type')){
            Bingo_Log::warning('_pushMsg input params error :'.serialize($arrInput));
            return false;
        }
        $pipeRes = self::_newPipePush($arrInput);
        $msgRes = self::_newPushMsg($arrInput);
        
        return $pipeRes && $msgRes;
            
    }
    
    
    /**
     * 新的发push的接口
     * @param $arrInput
     * @return array
     */
    private static function _newPipePush($arrInput) {
        if (!self::_checkParam($arrInput, 'task_id', 'title', 'content', 'url', 'uid', 'user_id', 'user_name', 'user_type', 'msg_type')){
            Bingo_Log::warning(__METHOD__ . ' input params error :'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $input = array(
            "service_id"   => isset($arrInput['service_id']) ? $arrInput['service_id'] : 0,
            'task_id'      => $arrInput['task_id'],
            "title"     => $arrInput['title'],
            "body"      => $arrInput['content'],
            "url"       => $arrInput['url'],
            "image_url" => $arrInput['image_url'],
            "target_uid" => $arrInput['uid'],
            "user_id"    =>  $arrInput['user_id'],
            "user_name"  =>  $arrInput['user_name'],
            "user_type"  =>  $arrInput['user_type'],
            "group_type" =>  $arrInput['group_type'],
        );
        ral_set_idc("nj");//目前只有南京的机器
        $res = Tieba_Service::call('tieba_push', 'newPipePushForPhpByUid', $input, null, null, 'post', 'json', 'utf-8');
        if (!is_array($res)) {
            $res = json_decode(trim($res,'[]'), true);
        }
        if (false == $res || Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            Bingo_Log::warning(__METHOD__ . " get tieba_push:newPipePushForPhp error, input=" . serialize($input) . " and output=" . serialize($res));
            return false;
        }
        return true;
    }
    
    /**
     * 新的发端内消息的接口
     * @param $arrInput
     * @return mixed
     */
    public static function _newPushMsg($arrInput) {
        if (!self::_checkParam($arrInput,'task_id','title','url','uid','user_id','user_name','user_type','msg_type')){
            Bingo_Log::warning(__METHOD__ . ' input params error :'.serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $groupIds = self::_getGroupIdByUid($arrInput['uid']);
        if (false == $groupIds) {
            Bingo_Log::warning(__METHOD__ . " get groupIds by uids error, input=" . serialize($arrInput['uid']) . " and output=" . serialize($groupIds));
            return false;
        }
        $strGroupIds = array();
        foreach ($groupIds as $groupId){
            $strGroupIds[] = strval($groupId);
        }
        $input = array(
            "task_id" => $arrInput['task_id'],
            "service_id" => isset($arrInput['service_id']) ? $arrInput['service_id'] : 0,
            "title" => $arrInput['title'],
            "text" => $arrInput['content'],
            "url" => $arrInput['url'],
            "image_url" => 'https://internal-amis-res.cdn.bcebos.com/images/pic_mask_push_representation_handle-0.png', 
            "group_id" => $strGroupIds,
            "user_id" => $arrInput['user_id'],
            "user_name" => $arrInput['user_name'],
            "user_type" => $arrInput['user_type'],
            "msg_type" => 7,
        );
        ral_set_idc("nj");//目前只有南京的机器
        $res = Tieba_Service::call('tieba_push', 'pushMsgLogicForPhp', $input, null, null, 'post', 'json', 'utf-8');
        if (!is_array($res)) {
            $res = json_decode(trim($res,'[]'), true);
        }
        if (false == $res || Tieba_Errcode::ERR_SUCCESS != $res['errno']) {
            Bingo_Log::warning(__METHOD__ . " get tieba_push:pushMsgLogicForPhp error, input=" . serialize($input) . " and output=" . serialize($res));
            return false;
        }
        return true;
    }
    
    private static function _getGroupIdByUid($uids) {

        if (!empty($uids) && is_array($uids)) {
            $input = array(
                "user_ids"      =>  $uids,
                'need_pmsg' => 0,
            );
            $res = Tieba_Service::call('im', 'queryPlatformGroupByUid', $input, null, null, 'post', 'php', 'utf-8');
            return array_column($res['groups'], 'group_id');
        }
        return false;
    }
    
    private static function  _checkParam(){
        $args = func_get_args();
        if(null == $args || empty($args) || null == $args[0] || empty($args[0]) || !is_array($args[0])){
            return false;
        }
        $arrInput = $args[0];
        $count = count($args);
        for ($i = 1; $i < $count; $i++){
            if(!isset($arrInput[$args[$i]])){
                return false;
            }
        }
        return true;
    }

}

$start = time();
echo 'start running appealResultPushMsg' . PHP_EOL;
$obj = new Script_Appealpush_AppealResultPushMsg();
$res = $obj->execute();
$end = time();
$diff = $end - $start;
if ($res) {
    echo 'finish appealResultPushMsg success! res:'. $res . ' cost:' . $diff . PHP_EOL;
} else {
    echo 'finish appealResultPushMsg fail! res:'. $res . ' cost:' . $diff . PHP_EOL;
}
