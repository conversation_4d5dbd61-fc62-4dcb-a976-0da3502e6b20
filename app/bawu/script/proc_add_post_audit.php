<?php
/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/6/26
 * Time: 10:05
 * IN USE
 */

define('NAME_SCRIPT', 'proc_add_post_audit');
require_once "MakeForumData.php";

function addPotAudit($strDate) {
    $arrAllFlag = MakeForumData::getAllFlag($strDate);
    //是否正在运行的
    $strAddPostAuditOnlineFlag = "add_post_audit_online_$strDate";
    if($arrAllFlag[$strAddPostAuditOnlineFlag]) {
        echo "the add post audit online flag is exist";
        return true;
    }
    //生成正在执行的标记
    MakeForumData::setFlag($strDate,$strAddPostAuditOnlineFlag);
    $bolRet = MakeForumData::executePostAudit();
    if($bolRet === false) {
        echo "add post audit  failed\n";
        MakeForumData::delFlag($strDate,$strAddPostAuditOnlineFlag);
        return false;
    }
    MakeForumData::delFlag($strDate,$strAddPostAuditOnlineFlag);
    echo "add post audit  success \n";
}

/*--start--*/
echo "start_time: " . date("Y-m-d H:i:s") . "\n";
$strDate = date("Ymd", time() - 3600 * 24 * 2);
if (!empty($argv[1]) && $argv[1] != 'd') {
    $strDate = intval($argv[1]);
}
if (!empty($argv[1]) && $argv[1] == 'd') {
    exec("rm -rf ./*.flag");
    return 0;
}
$bolRet = true;
if (!empty($argv[2])) {
    $intDays = intval($argv[2]);
    $intTime = strtotime($strDate);
    for ($i = 0; $i < $intDays; $i++) {
        $strDate = date("Ymd", $intTime - 3600 * 24 * $i);
        $bolRet = addPotAudit($strDate);
    }
} else {
    $bolRet = addPotAudit($strDate);
}

echo "end_time: " . date("Y-m-d H:i:s") . "\n";