<?php
/***************************************************************************
 *
 *    Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 *
 ***************************************************************************/

/**
 * @file priforumMatureStatis.php
 * <AUTHOR>
 * @date 2018/11/27 11:11:11
 * @brief
 *
 **/
ini_set('memory_limit','3072M');
define('MODULE', 'bawu' );
define('ROOT_PATH', dirname(PHP_PREFIX));
define('CONF_PATH', ROOT_PATH.'/conf');
define('DATA_PATH', ROOT_PATH.'/data');
define('BIN_PATH', ROOT_PATH.'/php/bin');
define('LOG_PATH', ROOT_PATH.'/log');
define('APP_PATH', ROOT_PATH.'/app');
define('TPL_PATH', ROOT_PATH.'/template');
define('LIB_PATH', ROOT_PATH.'/php/phplib');
define('WEB_ROOT', ROOT_PATH.'/webroot');
define('PHP_EXEC', BIN_PATH.'/php');

Tieba_Init::init(MODULE);
/***
 * Class Script_priforum_mature
 * process priforum mature data
 * include main post、like、active、audito
 */
class Script_Priforum_PriforumAuditCommitTimeout{
    // db config
    const DB_SERVICE    = 'DB_forum_priforum';
    CONST DB_TABLE_NAME = 'create_priforum_record';
    const DB_CHARSET    = 'UTF8';

    // 规则
    const RULE_COMMIT_AUDIT_TIME = 1; // 项目规定吧主创建吧审核被拒绝只能有一次机会修改资料

    // 处理数据配置
    const RULE_DATA_PAGE_SIZE = 1000;

    private static $_db;

    public static function excute()
    {
        if(!self::_getDB())
        {
            Bingo_Log::warning('db connect failed!');
            echo "db connect failed!";
            return false;
        }
        // 获取需要处理的forum_id_list进行分批处理
        $output = self::getPriforumIdList();
        echo '获取需要处理的id列表:'.json_encode($output).PHP_EOL;
        Bingo_Log::warning('call '.__METHOD__.' Get all the ids that need to be processed， res ['.serialize($output).']');

        if(empty($output))
        {
            echo '获取需要处理的forumid列表为空， 直接退出'.PHP_EOL;
            Bingo_Log::warning('call '.__METHOD__.'Get the list of forumids that need to be processed as empty， Exit directly');
            return true;
        }

        // 获取需要执行的数据总页数
        $count = count($output);
        $pageTotal = ceil($count/self::RULE_DATA_PAGE_SIZE);
        echo 'a count = '.$count.'; 总共 pagetotal = '.$pageTotal.'页'.PHP_EOL;
        Bingo_Log::warning('call '.__METHOD__.'Get a total of ['.$count.'] ids to be processed, grouped by ['.self::RULE_DATA_PAGE_SIZE.'] pages, a total of ['.$pageTotal.'] pages');

        // 循环处理这些forum_id回收工作
        $processList = array(); // 记录处理的吧id
        for($i = 0; $i < $pageTotal; $i++)
        {
            $forumIdObj = array_slice($output, $i * self::RULE_DATA_PAGE_SIZE, self::RULE_DATA_PAGE_SIZE);
            $inIdLists = array();
            echo 'idlists = '.json_encode($forumIdObj).PHP_EOL;
            Bingo_Log::warning('call '.__METHOD__.' id lists ['.serialize($forumIdObj).']');
            if(empty($forumIdObj))
            {
                continue;
            }

            // 提取出需要处理的id
            foreach ($forumIdObj as $idItem)
            {
                $inIdLists[] = $idItem['forum_id'];
            }
            echo 'idlists = '.json_encode($inIdLists).PHP_EOL;

            // 获取forum_id的审核记录数据
            $forumAuditLog = self::getPriforumAuditInfo($inIdLists);
            echo '分批处理数据：'.json_encode($forumAuditLog).PHP_EOL;
            if(empty($forumAuditLog))
                continue;

            // 归类吧的审核记录(一个吧对应多条审核记录，跟业务设计有几次需改机会有关心，比如有1次修改机会则最多有2条审核记录，2次就是3条)
            $forumAuditInfo = array();
            foreach ($forumAuditLog as &$auditLogItem)
            {
                if(!isset($forumAuditInfo[$auditLogItem['forum_id']]))
                {
                    $forumAuditInfo[$auditLogItem['forum_id']] = array();
                }

                $forumAuditInfo[$auditLogItem['forum_id']][] = $auditLogItem;
            }

            echo '吧的审核记录：'.json_encode($forumAuditInfo).PHP_EOL;

            if(empty($forumAuditInfo))
                continue;

            // 筛选出需要回收的吧id
            foreach ($forumAuditInfo as $auditProcessItem)
            {
                // 获取吧的最新一条审核记录（根据审核记录的创建时间来排序）
                $auditNewestInfo = array();
                foreach ($auditProcessItem as $auditVal)
                {
                    if(empty($auditNewestInfo))
                    {
                        $auditNewestInfo = $auditVal;
                        continue;
                    }

                    if($auditNewestInfo['create_time'] < $auditVal['create_time'])
                    {
                        $auditNewestInfo = $auditVal;
                    }
                }

                // 验证状态，验证时间(审核状态为吧名通过，吧信息未通过，且审核时间大于0，且审核时间距离当前时间超过24小时，则回收)
                if($auditNewestInfo['finfo_audit_status'] == 1 && $auditNewestInfo['pinfo_audit_status'] == 2
                    && $auditNewestInfo['audit_time'] > 0 && ((int)$auditNewestInfo['audit_time'] + (24*3600)) <= time())
                {
                    $processList[] = $auditNewestInfo['forum_id'];
                    echo '需要被处理的吧信息：'.json_encode($auditNewestInfo).PHP_EOL;
                    $processRes = self::clearPriforumProcess((int)$auditNewestInfo['forum_id']);
                    Bingo_Log::warning('call '.__METHOD__.' input ['.serialize($auditNewestInfo).'] output ['.serialize($processRes).']');
                }
            }
        }

        Bingo_Log::warning('call '.__METHOD__.' Successfully process and recycle '.count($processRes).', list ['.serialize($processList).']');
        return true;
    }

    /***
     * 获取查询数据的时间(以下代码有测试数据，可提供测试，上线时需要删除,切记)
     * @return array|bool
     */
    private static function clearPriforumProcess($forumId=0)
    {
        if(!intval($forumId))
        {
            Bingo_Log::warning('call '.__METHOD__.' ');
        }

        // 获取吧主id
        $input = array(
            'forum_id' => (int)$forumId,
        );
        $output = Tieba_Service::call('perm', 'getManagerList', $input, null, null, 'post', 'php', 'utf-8');
        Bingo_Log::warning('call '.__METHOD__.' perm::getManagerList input ['.serialize($input).'] output ['.serialize($output).']');
        if (false === $output || Tieba_Errcode::ERR_SUCCESS !== $output['errno']
            || !isset($output['output']['0']['user']['user_id']) || empty($output['output']['0']['user']['user_id']))
        {
            $strMsg = sprintf('call '.__METHOD__.' perm::getManagerList fail with input [%s] output [%s]', serialize($input), serialize($output));
            Bingo_Log::warning($strMsg);
            $forumUserId = 0;
        }else{
            $forumUserId = (int)$output['output']['0']['user']['user_id'];
        }

        // 获取吧名
        $fnameInput = array(
            'forum_id' => array($forumId),
        );
        $fnameInfo = Tieba_Service::call("forum","getFnameByFid",$fnameInput,null, null, 'post', 'php', 'utf-8');
        $forumName = trim($fnameInfo['forum_name'][$forumId]['forum_name']);
        Bingo_Log::warning($forumName);
        if(false === $fnameInfo || Tieba_Errcode::ERR_SUCCESS != $fnameInfo['errno'] || !isset($fnameInfo['forum_name'][$forumId]['forum_name']))
        {
            Bingo_Log::warning('call '.__METHOD__.' forum:getFnameByFid input forum_id ['.$forumId.'] output ['.serialize($fnameInfo).']');
            return false;
        }
        // 超过24小时未提交吧资料，则直接回收
        $clearInput = array(
            'forum_id'   => (int)$forumId,
            'forum_name' => $forumName,
            'type'       => 'task',
            'op_uname'   => 'admin'
        );
        $clearRes = Tieba_Service::call('bawu', 'recyclePriForum',$clearInput,null, null, 'post', 'php');
        Bingo_Log::warning('call '.__METHOD__.' bawu::recyclePriForum input ['.serialize($clearInput).'] output ['.serialize($clearRes).']');
        if ( $clearRes['errno'] !== Tieba_Errcode::ERR_SUCCESS || $clearRes === false)
        {
            Bingo_Log::warning('call '.__METHOD__.' ce_bawu::recyclePriForum fail.');
            return false;
        }

        if(intval($forumUserId))
        {
            // 发送回收系统消息
            $sendInput = array(
                'forum_id'   => $forumId,
                'type'       => 'clear',
                'forum_name' => $forumName,
                'user_id'    => $forumUserId
            );

            $sendRes = Tieba_Service::call('bawu', 'sendMessagePriforum',$sendInput,null, null, 'post', 'php');
            Bingo_Log::warning('call '.__METHOD__.' Forum_id ['.$forumId.'] , The task is not completed, recycle it as a whole , Send system message intput ['.serialize($sendInput).'] output ['.serialize($sendRes).']');
        }
        return true;
    }

    /***
     * 获取需要处理的forum_id
     * <AUTHOR>
     * @return array | bool
     */
    public static function getPriforumIdList()
    {
        $sql = "SELECT forum_id
                FROM create_priforum_record
                WHERE forum_id IN
                    (SELECT forum_id
                    FROM priforum_info
                    WHERE run_status = 0
                            AND stage_status = 1
                            AND is_close = 0
                            AND is_clear = 0)
                GROUP BY  forum_id
                HAVING count(1) <= ".self::RULE_COMMIT_AUDIT_TIME;
        $res = self::$_db->query($sql);
        if(false === $res){
            Bingo_Log::warning("db get data failed!" . $sql);
            echo "select id failed!\n";
            return false;
        }

        if(empty($res))
        {
            Bingo_Log::warning("selece success!!! db get data empty!" . $sql);
            echo "selece success!!! db get data empty!\n";
            return false;
        }

        return $res;
    }

    /***
     * 获取需要处理的forum审核属性
     * <AUTHOR>
     * @return
     */
    public static function getPriforumAuditInfo($arrInput=array())
    {
        if(empty($arrInput) || !is_array($arrInput))
            return false;

        $sql = "SELECT forum_id, create_time, finfo_audit_status, pinfo_audit_status, audit_time
                FROM create_priforum_record
                WHERE forum_id IN(".implode(',', $arrInput).")
                ";
        $res = self::$_db->query($sql);
        if(false === $res){
            Bingo_Log::warning("db get data failed!" . $sql);
            echo "select id failed!\n";
            return false;
        }

        if(empty($res))
        {
            Bingo_Log::warning("selece success!!! db get data empty!" . $sql);
            echo "selece success!!! db get data empty!\n";
            return false;
        }

        return $res;
    }

    /***
     * db connect
     * @return Bd_DB|null
     */
    private static function _getDB() {
        if (self::$_db) {
            return self::$_db;
        }
        self::$_db = new Bd_DB ();
        if (self::$_db == null) {
            Bingo_Log::warning(" get db failed : new bd_db fail.");
            return null;
        }
        Bingo_Timer::start('dbinit');
        $r = self::$_db->ralConnect(self::DB_SERVICE);
        Bingo_Timer::end('dbinit');
        if (!$r) {
            Bingo_Log::warning(" get db failed : bd db ral connect fail.");
            self::$_db = null;
            return null;
        }
        self::$_db->charset(self::DB_CHARSET);
        return self::$_db;
    }
}

Script_Priforum_PriforumAuditCommitTimeout::excute();