<?php
/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/6/30
 * Time: 23:34
 * IN USE
 */

define('NAME_SCRIPT', 'rollback_data');
require_once "MakeForumData.php";

function checkRollbackFlag($strDate,$intPartCount) {
    $arrAllCheckFlag = MakeForumData::getAllFlag($strDate);
    for($i =0; $i<$intPartCount; $i++) {
        $strCurrentPartFileFlag = "part_data_$strDate" . "_$i.flag";
        if(!$arrAllCheckFlag[$strCurrentPartFileFlag]) {
            return false;
        }
    }
    return true;
}

function rollbackData($strDate) {
    $phpBin = "../../../php/bin/php ";
    $intPartCount = 10;
    //数据回溯在当天数据跑完时进行，否则不进行回溯
    if (!checkRollbackFlag($strDate,$intPartCount)) {
        echo "the current data is processing\n";
        return true;
    }
    $strRollBackOnlineFlag = "rollback_online_$strDate";
    MakeForumData::setFlag($strDate,$strRollBackOnlineFlag);
    $intDays = 31;//回溯一个月的数据
    $intTime = strtotime($strDate);
    for ($i = 1; $i < $intDays; $i++) {
        $strTempDate = date("Ymd", $intTime - 3600 * 24 * $i);
        $arrTempAllFlag = MakeForumData::getAllFlag($strTempDate);
        var_dump($arrTempAllFlag);
        $strTempAllFileFlag = "alldata_$strTempDate.flag";//兼容旧的回溯方式，现在是分片回溯

        if ($arrTempAllFlag[$strTempAllFileFlag]) {
            echo "the $strTempDate data is compete\n";
            continue;
        }

        if (!$arrTempAllFlag[$strTempAllFileFlag]) {
            //先check一次标记
            if(checkRollbackFlag($strTempDate,$intPartCount)) {
                echo "the $strTempDate data is compete\n";
                continue;
            }
            //删除所有的redis标记
            $strCoreFileRedisFlag = "forumdata_$strTempDate.redis.flag";
            $strBawuFileRedisFlag = "bawudata_$strTempDate.redis.flag";
            $strOtherFileRedisFlag = "otherdata_$strTempDate.redis.flag";
            MakeForumData::delFlag($strTempDate,$strCoreFileRedisFlag);
            MakeForumData::delFlag($strTempDate,$strBawuFileRedisFlag);
            MakeForumData::delFlag($strTempDate,$strOtherFileRedisFlag);
            echo "roll back the date=[" . $strTempDate . "] data start at time=[" . date("Y-m-d H:i:s") . "]\n";
            exec("$phpBin import_bawu_data.php $strTempDate");
            exec("$phpBin import_other_data.php $strTempDate");
            exec("$phpBin import_core_redis_data.php $strTempDate");
            for($j =0;$j<$intPartCount;$j++) {
                $strTempPartFileFlag = "part_data_$strTempDate" . "_$j.flag";
                if($arrTempAllFlag[$strTempPartFileFlag]) {
                    continue;
                }
                exec("$phpBin import_core_data.php $strTempDate 0 $j $intPartCount");
                sleep(180);//每个脚本之间sleep 3分钟
            }
            echo "roll back the date=[" . $strTempDate . "] data end at time=[" . date("Y-m-d H:i:s") . "]\n";
            sleep(60);
        }
    }
    MakeForumData::delFlag($strDate,$strRollBackOnlineFlag);
    return true;
}

/*--start--*/
echo "check_pre_data start_time: " . date("Y-m-d H:i:s") . "\n";
$strDate = date("Ymd", time() - 3600 * 24 * 2);
if (!empty($argv[1]) && $argv[1] != 'd') {
    $strDate = intval($argv[1]);
}
if (!empty($argv[1]) && $argv[1] == 'd') {
    exec("rm -rf ./*.flag");
    return 0;
}

$bolRet = true;
if (!empty($argv[2])) {
    $intDays = intval($argv[2]);
    $intTime = strtotime($strDate);
    for ($i = 0; $i < $intDays; $i++) {
        $strDate = date("Ymd", $intTime - 3600 * 24 * $i);
        $bolRet = rollbackData($strDate);
    }
} else {
    $bolRet = rollbackData($strDate);
}
echo "check_pre_data end_time: " . date("Y-m-d H:i:s") . "\n";