<?php
/**
 * Created by PhpStorm.
 * User: yang<PERSON><PERSON><PERSON>
 * Date: 2015/4/14
 * Time: 10:38
 */
@set_time_limit(0);
@ini_set("memory_limit", "4096M");
define ('ROOT_PATH', dirname(__FILE__) . "/../../../");
require_once dirname(__FILE__) . '/../util/Grade.php';
//Tieba_Init::init("forum_bawu");
Tieba_Init::init("bawu");
Bingo_Log::init(array(
    LOG => array(
        'file'  => dirname ( __FILE__ ) . '/../../../log/app/bawu/'.NAME_SCRIPT.'.log',
        'level' => 0x04 | 0x02,
    ),
), LOG);

class MakeForumData
{
    protected static $_db = null;
    protected static $_redis = null;
    protected static $_strForumIds = 'forum_id';//全吧id的文件
    protected static $_strDelayDataKey = 'fda_delay_date';
    public static $_strFilePath = null;
    public static $_strRedisKey = null;
    private static $_intIsBase = 0;
    private static $arrKeys = array(
        'fbd_data_core_',
        'fbd_data_bawu_',
        'fbd_data_other_',
    );
    private static $_arrDefault = array(0, 0, 0, 0);
    const MODULE_ID_DIY_LINK = 1002;
    const MODULE_NAME_DIY_LINK = 'customlink_p1';
    const STYLE_ID_DIY_LINK = 1;
    const STYLE_NAME_DIY_LINK = "define";
    const DB_RAL_SERVICE_NAME = "forum_bawu";
    //const REDIS_PID = 'commonb';
    const REDIS_PID = 'bawu';
    const REDIS_PRE = 'fbd_data_';
    const REDIS_PRE_BAWU = 'fbd_data_bawu_';
    const REDIS_PRE_OTHER = 'fbd_data_other_';
    const REDIS_PRE_CORE = 'fbd_data_core_';
    const REDIS_PRE_CUSTOM_LINK = 'fbd_link';
    const REDIS_PRE_TOP_ADD = 'fdb_add_top';
    const REDIS_PRE_TOP_DEL = 'fdb_del_top';
    const REDIS_PRE_POST_AUDIT = 'fdb_post_audit';
    const MAX_LINE_LEN = 512;
    const TABLE_COUNT = 64;
    const MAX_RECORD_COUNT = 500;
    const MAX_SQL_COUNT = 10;
    const MAX_RETRY = 10;

    private static function _getDB() {//增加重试的机制
        for ($i = 0; $i < self::MAX_RETRY; $i++) {
            self::$_db = new Bd_DB();
            if (self::$_db == null) {
                echo "new bd_db Fail.\n";
            }
            Bingo_Timer::start('dbinit');
            $r = self::$_db->ralConnect(self::DB_RAL_SERVICE_NAME);
            Bingo_Timer::end('dbinit');
            if (!$r) {
                echo "bd db ral connect Fail.\n";
                self::$_db = null;
                $time = $i * 30;
                sleep($time);
            } else {
                return self::$_db;
            }
        }
        return self::$_db;

    }

    private static function _closeDB() {
        if(!is_null(self::$_db)) {
            self::$_db->close();
            return true;
        }
        return false;
    }

    private static function _getRedisObj() {
        for ($i = 0; $i < self::MAX_RETRY; $i++) {
            self::$_redis = new Bingo_Cache_Redis(self::REDIS_PID);
            if (!self::$_redis || !self::$_redis->isEnable()) {
                echo("init redis fail.\n");
                self::$_redis = null;
                $time = $i * 30;
                sleep($time);
            } else {
                return self::$_redis;
            }
        }
        return self::$_redis;
    }
    

    private static function _getRemoteFile($strFileName) {
        if(!file_exists($strFileName)){
            $arg = escapeshellarg("ftp://tieba01:<EMAIL>/tieba_rpt_forum_data_file/$strFileName")." ./ 2>&1 >/dev/null";
            exec("wget ".$arg);
        }
    }

    public static function init($arrParam) {
        if (isset($arrParam['file_path'])) {
            self::$_strFilePath = $arrParam['file_path'];
        } else {
            return false;
        }
    }

    public static function setRedisKey($arrParam) {
        echo __CLASS__ . "::" . __FUNCTION__ . serialize($arrParam)."\n" ;
        if (isset($arrParam['redis_key'])) {
            self::$_strRedisKey = self::REDIS_PRE . $arrParam['redis_key'] . '_';
        } else {
            return false;
        }
    }

    public static function getRedisKey($key) {
        return self::REDIS_PRE . $key;
    }

    public static function setIsBase($value) {
        self::$_intIsBase = intval($value);
    }

    private static function _getDelSql($arrList, $strDate) {
        $strFids = implode(",", $arrList);
        $strSql = "DELETE FROM data_info WHERE forum_id IN ($strFids) AND date <= '$strDate'";
        return $strSql;
    }

    private static function _getRedisData($arrList, $strPreKey) {
        $arrInput = array();
        $arrData = array();
        if (self::_getRedisObj()) {
            foreach ($arrList as $val) {
                $arrInput['reqs'][] = array(
                    'key' => $strPreKey . $val[1],
                );
            }
            $arrData = self::$_redis->GET($arrInput);
            //echo __FUNCTION__ . 'GET'."-input[".serialize($arrInput) ."]output[".serialize($arrData)."]\n";
            if ($arrData === false || $arrData['err_no'] != 0 || !isset($arrData['ret'])) {
                echo "get redis data fail and key=[" . $strPreKey . "]" . "input = [" .serialize($arrList) . "]\n";
                $arrData = self::retryGetRedis($arrInput);
            }

        }
        return $arrData;
    }


    private static function _getAllRedisData($arrForumIds, $strPreKey) {
        $arrInput = array();
        $arrData = array();
        if (self::_getRedisObj()) {
            foreach ($arrForumIds as $intForumId) {
                $arrInput['reqs'][] = array(
                    'key' => $strPreKey . $intForumId,
                );
            }
            $arrData = self::$_redis->GET($arrInput);
            //echo __FUNCTION__ . 'GET'."-input[".serialize($arrInput) ."]output[".serialize($arrData)."]\n";
            if ($arrData === false || $arrData['err_no'] != 0 || !isset($arrData['ret'])) {
                echo "get redis data fail and key=[" . $strPreKey . "]" . "input = [" .serialize($arrForumIds) . "]\n";
                $arrData = self::retryGetRedis($arrInput);
            }

        }
        return $arrData;
    }

    private static function _getImportSql($arrList, $bolOtherFlag, $bolBawuFlag = false) {
        $arrRestData = array();
        // core_data or core_data+bawu_data or core_data+bawu_data+other_data
        if ($bolBawuFlag) { // bawu data 不在 redis里面的话， 只读文件写库, 存core data 防止bawu data 阻塞core data的写入
            foreach(self::$arrKeys as $key_pre){
                if(self::REDIS_PRE_CORE == $key_pre){ // self::$_strRedisKey == self::REDIS_PRE_CORE
                    continue;
                }
                if(!$bolBawuFlag && self::REDIS_PRE_BAWU == $key_pre){
                    continue;
                }
                if(!$bolOtherFlag && self::REDIS_PRE_OTHER == $key_pre){
                    continue;
                }
                $arrRes = self::_getRedisData($arrList, $key_pre);
                if (false === $arrRes) {
                    echo "get redis data fail and key =[" . $key_pre . "]\n";
                    return false;
                } else {
                    $arrRestData[$key_pre] = $arrRes;
                }
            }

        }

        foreach ($arrList as $arrEach) {
            $intGrade = self::treatGrade($arrEach);

            if ($bolBawuFlag) {
                $arrTempData = array();
                foreach ($arrRestData as $key_pre => $value) {
                    $strKey = $key_pre . $arrEach[1];
                    if ($value['ret'][$strKey]) {
                        $strTempData = $value['ret'][$strKey];
                        if (empty($strTempData)) {
                            echo "redis data is not exist and key = [" . $strKey . "]";
                            return false;
                        }
                        $arrTempData[] = explode("_", $strTempData);
                    }
                }
                unset($arrTempData[0][0]);
                unset($arrTempData[0][1]);
                unset($arrTempData[1][0]);
                unset($arrTempData[1][1]);
                if (!$bolOtherFlag) {
                    $arrEach = array_merge($arrEach, $arrTempData[0]);
                } else {
                    if (empty($arrTempData[1])) {//获取other数据失败
                        echo "the other data is null\n";
                        var_dump($arrRestData);
                        var_dump($arrTempData);
                        $arrEach = array_merge($arrEach, $arrTempData[0], self::$_arrDefault);
                    } else {
                        $arrEach = array_merge($arrEach, $arrTempData[0], $arrTempData[1]);
                    }
                }
            }
            $arrEach[] = $intGrade;
            if($intGrade > 0){
                Bingo_Log::warning("grade not 0:". join("_",$arrEach));
            }
            $strTemp = str_replace("\n", "", implode(",", $arrEach));
            $arrValue[] = "($strTemp)";
        }
        $strValue = implode(",", $arrValue);

        $arrBaseField = array(
            'date',
            'forum_id',
        );
        $arrCoreField = array(
            'cookie_num',
            'post_num',
            'user_post_num',
            'thread_num',
            'user_thread_num',
            'new_member_num',
            'sign_num',
            'total_member_num',
            'client_cookie_num',
            'client_thread_num',
            'client_post_num',
            'client_sign_num',
        );
        $arrBawuField = array(
            'member_sign_num',
            'bawu_sign_num',
            'bawu_thread_num',
            'bawu_post_num',
            'bawu_total_del_post_num',
            'bawu_client_sign_num',
            'bawu_client_thread_num',
            'bawu_client_post_num',
            'bawu_client_del_post_num',
        );
        $arrOtherField = array(
            'ueg_del_thread_num',
            'ueg_del_post_num',
            'complain_num',
            'bawu_op_complain_num',
        );
        $strBase = implode(',', $arrBaseField);
        $strCore = implode(',', $arrCoreField);
        $strBawu = implode(',', $arrBawuField);
        $strOther = implode(',', $arrOtherField);
        //sql
        if ($bolBawuFlag) {
            if ($bolOtherFlag) {
                $strSql = "REPLACE INTO data_info ($strBase,$strCore,$strBawu,$strOther,cur_grade) VALUES $strValue";
            } else {
                $strSql = "REPLACE INTO data_info ($strBase,$strCore,$strBawu,cur_grade) VALUES $strValue";
            }
        } else {
            $strSql = "REPLACE INTO data_info ($strBase,$strCore,cur_grade) VALUES $strValue";
        }

        Bingo_Log::debug($strSql);
        return $strSql;
    }
   
    /**
     * [treatGrade description]
     * @param  [type] $arrCoreData [description]
     * @return [type]              [description]
     */
    private static function treatGrade($arrCoreData){
        $arrData = array(
            'forum_id'          => $arrCoreData[0],
            'date'              => $arrCoreData[1],
            'cookie_num'        => $arrCoreData[4],
            'post_num'          => $arrCoreData[5],
            'user_post_num'     => $arrCoreData[6],
            'thread_num'        => ((0 == $arrCoreData[7] + $arrCoreData[15])?1:$arrCoreData[7]), //防止除0 
            'user_thread_num'   => $arrCoreData[8],
            'new_member_num'    => $arrCoreData[11],
            'sign_num'          => $arrCoreData[12],
            'total_member_num'  => (0 == $arrCoreData[13]?1:$arrCoreData[13]),//防止除0
            'client_cookie_num' => $arrCoreData[14],
            'client_thread_num' => $arrCoreData[15],
            'client_post_num'   => $arrCoreData[16],
            'client_sign_num'   => $arrCoreData[17],
        );
        $intGrade = Util_Grade::calculate($arrData);
        return $intGrade;
    }
    /**
     * [_getSleepSql description]
     * @return [type] [description]
     */
    private static function _getSleepSql() {
        $strSql = "SELECT SLEEP(1);";
        return $strSql;
    }
    /**
     * import_data.php: 在用
     * @param  [type] $strDate      [description]
     * @param  [type] $bolOtherFlag [description]
     * @return [type]               [description]
     */
    public static function execute($strDate, $bolOtherFlag) {
        echo __CLASS__ . "::" . __FUNCTION__ . $strData . '-' . $bolOtherFlag. '-' . $intPart . '-' . $intPartCount ."\n" ;

        if (is_null(self::$_strFilePath)) {
            echo "no file path!\n";
            return false;
        }
        $arrDataList = array();
        $intSqlNum = 0;
        $objOpenFile = fopen(self::$_strFilePath, "r");
        if (!$objOpenFile) {
            echo "open file failed!\n";
            return false;
        }
        while (!feof($objOpenFile)) {
            $strLine = stream_get_line($objOpenFile, self::MAX_LINE_LEN, "\n");
            if (empty($strLine)) {
                continue;
            }
            $arrItems = explode("\t", $strLine);
            unset($arrItems[2]);
            if (self::$_strRedisKey === self::REDIS_PRE_CORE) {
                unset($arrItems[3]);
                unset($arrItems[9]);
                unset($arrItems[10]);
                unset($arrItems[18]);
                unset($arrItems[19]);
            }
            if (self::$_strRedisKey === self::REDIS_PRE_BAWU) {
                unset($arrItems[12]);
                unset($arrItems[13]);
            }

            $arrDataList[] = $arrItems;
            if (count($arrDataList) >= self::MAX_RECORD_COUNT) {
                $db = self::_getDB();
                if ($db === null) {
                    echo "init db fail \n";
                    return false;
                }
                $strSql = self::_getImportSql($arrDataList, $bolOtherFlag);
                if ($strSql === false) {
                    echo "get the sql error";
                    return false;
                }
                $res = $db->query($strSql);
                if (false === $res) {
                    echo "Insert Failed. errno:[" . $db->errno() . "][" . $db->error() . "]sqlStr:" . $strSql . "\n";
                    sleep(1);//休眠1s
                    if (!self::retrySql($strSql)) {//重试如果也失败的话，则结束
                        return false;
                    }
                }
                $arrDataList = array();
                $intSqlNum++;
                self::_closeDB();
                continue;
            }
            if ($intSqlNum > 0 && $intSqlNum % self::MAX_SQL_COUNT == 0) {
                $intSqlNum++;
                sleep(1);
            }
        }

        $strSql = self::_getImportSql($arrDataList, $bolOtherFlag);
        if ($strSql === false) {
            echo "get the sql error";
            return false;
        }
        $db = self::_getDB();
        if ($db === null) {
            echo "init db fail \n";
            return false;
        }
        $res = $db->query($strSql);
        if (false === $res) {
            sleep(1);//休眠1s
            if (!self::retrySql($strSql)) {//重试如果也失败的话，则结束
                self::_closeDB();
                return false;
            }
        }
        return true;
    }
    /**
     * delete_all_data.php: import_data.php: 在用
     * @param  [type] $strFilePath [description]
     * @param  [type] $strDate     [description]
     * @return [type]              [description]
     */
    public static function execute2($strFilePath, $strDate) {
        echo __CLASS__ . "::" . __FUNCTION__ . $strData . '-' . $bolOtherFlag. '-' . $intPart . '-' . $intPartCount."\n"  ;
        if (is_null($strFilePath)) {
            return false;
        }
        $arrDataList = array();
        $intSqlNum = 0;
        $handle = fopen($strFilePath, "r");
        if (!$handle) {
            echo "open file failed!\n";
            return false;
        }
        while (!feof($handle)) {
            $strLine = fgets($handle, self::MAX_LINE_LEN);
            if (empty($strLine)) {
                continue;
            }
            $arrItems = explode("\t", $strLine);
            $intIndex = intval($arrItems[1]) % self::TABLE_COUNT;
            $arrDataList[$intIndex][] = intval($arrItems[1]);

            if (count($arrDataList[$intIndex]) >= self::MAX_RECORD_COUNT) {
                $strSql = self::_getDelSql($arrDataList[$intIndex], $strDate);
                $db = self::_getDB();
                if ($db === null) {
                    echo "init db fail \n";
                    return false;
                }
                $res = $db->query($strSql);
                if (false === $res) {
                    echo "Delete Failed. errno:[" . $db->errno() . "][" . $db->error() . "]sqlStr:" . $strSql . "\n";
                    if(!self::retrySql($strSql)) {
                        return false;
                    }
                }
                sleep(2);
                $arrDataList[$intIndex] = array();
                $intSqlNum++;
                self::_closeDB();
                continue;
            }
            if ($intSqlNum > 0 && $intSqlNum % self::MAX_SQL_COUNT == 0) {
                sleep(1);
                $intSqlNum++;
            }
        }
        foreach ($arrDataList as $key => $arrEach) {
            if (count($arrEach) <= 0) {
                continue;
            }
            $strSql = self::_getDelSql($arrEach, $strDate);
            //echo "$strSql\n";
            $db = self::_getDB();
            if ($db === null) {
                echo "init db fail \n";
                return false;
            }
            $res = $db->query($strSql);
            if (false === $res) {
                echo "Delete Failed. errno:[" . $db->errno() . "][" . $db->error() . "]sqlStr:" . $strSql . "\n";
                if(!self::retrySql($strSql)) {
                    self::_closeDB();
                    return false;
                }
            }
        }
        return true;
    }

    public static function executePart2($strDate, $bolOtherFlag, $bolBawuFlag, $intPart,$intPartCount) {
        echo __CLASS__ . "::" . __FUNCTION__ . $strDate . '-' . $bolBawuFlag. '-' . $bolOtherFlag. '-' . $intPart . '-' . $intPartCount ."\n" ;

        if (is_null(self::$_strFilePath)) {
            echo "no file path\n";
            return false;
        }
        $arrDataList = array();
        $intSqlNum = 0;

        $objOpenFile = fopen(self::$_strFilePath, "r");
        if (!$objOpenFile) {
            echo "open file failed\n";
            return false;
        }
        while (!feof($objOpenFile)) {
            $strLine = stream_get_line($objOpenFile, self::MAX_LINE_LEN, "\n");
            if (empty($strLine)) {
                continue;
            }
            $arrItems = explode("\t", $strLine);
            if ($intPart != $arrItems[1] % $intPartCount) {
                continue;
            }
            unset($arrItems[2]);
            if (self::$_strRedisKey === self::REDIS_PRE_CORE) {
                unset($arrItems[3]);
                unset($arrItems[9]);
                unset($arrItems[10]);
                unset($arrItems[18]);
                unset($arrItems[19]);
            }
            if (self::$_strRedisKey === self::REDIS_PRE_BAWU) {
                unset($arrItems[12]);
                unset($arrItems[13]);
            }

            $arrDataList[] = $arrItems;
            if (count($arrDataList) >= self::MAX_RECORD_COUNT) {
                $db = self::_getDB();
                if ($db === null) {
                    echo "init db fail \n";
                    return false;
                }
                $strSql = self::_getImportSql($arrDataList, $bolOtherFlag, $bolBawuFlag);
                if ($strSql === false) {
                    echo "get the sql error";
                    return false;
                }
                $res = $db->query($strSql);
                if (false === $res) {
                    echo "Insert Failed. errno:[" . $db->errno() . "][" . $db->error() . "]sqlStr:" . $strSql . "\n";
                    sleep(1);//休眠1s
                    if (!self::retrySql($strSql)) {//重试如果也失败的话，则结束
                        return false;
                    }
                }
                $arrDataList = array();
                $intSqlNum++;
                self::_closeDB();
                continue;
            }
            if ($intSqlNum > 0 && $intSqlNum % self::MAX_SQL_COUNT == 0) {
                $intSqlNum++;
                sleep(1);
            }
        }
        $strSql = self::_getImportSql($arrDataList, $bolOtherFlag, $bolBawuFlag);
        if ($strSql === false) {
            echo "get the sql error";
            return false;
        }
        $db = self::_getDB();
        if ($db === null) {
            echo "init db fail \n";
            return false;
        }
        $res = $db->query($strSql);
        if (false === $res) {
            sleep(60);//休眠一分钟
            if (!self::retrySql($strSql)) {//重试如果也失败的话，则结束
                self::_closeDB();
                return false;
            }
        }
        return true;
    }

    public static function executeAddCustomLink() { //增加用户自定义链接
        echo __CLASS__ . "::" . __FUNCTION__ . $strData . '-' . $bolOtherFlag. '-' . $intPart . '-' . $intPartCount ."\n" ;
        $intCount = 0;
        while (self::getListLen(self::REDIS_PRE_CUSTOM_LINK)) {
            $arrRes = self::removeListKey(self::REDIS_PRE_CUSTOM_LINK);
            if ($arrRes === false) {
                continue;
            }
            $intForumId = intval($arrRes['ret'][self::REDIS_PRE_CUSTOM_LINK]);
            if (empty($intForumId)) {
                continue;
            }
            $intCount++;
            if ($intCount > 500) {
                sleep(1);
                $intCount = 0;
            }
            $arrInput = array(
                'forum_id' => $intForumId,
                'attr_name' => self::MODULE_NAME_DIY_LINK,
                'attr_value' => array(
                    'module_id' => self::MODULE_ID_DIY_LINK,
                    'style_id' => self::STYLE_ID_DIY_LINK,
                    'style_name' => self::STYLE_ID_DIY_LINK,
                ),
            );
            $arrRes = Tieba_Service::call('forum', 'setForumAttr', $arrInput);
            if ($arrRes === false || intval($arrRes['errno']) != Tieba_Errcode::ERR_SUCCESS) {
                self::addListKey(self::REDIS_PRE_CUSTOM_LINK, $intForumId);
                echo "the forum_id=[" . $intForumId . "] add custom link fail \n";
                continue;
            } else {
                echo "the forum_id=[" . $intForumId . "] add custom link success \n";
            }
        }
        return true;

    }

    public static function executeAddThirdTop() { //增加第三条置顶
        echo __CLASS__ . "::" . __FUNCTION__ . $strData . '-' . $bolOtherFlag. '-' . $intPart . '-' . $intPartCount ."\n" ;
        $intCount = 0;
        while (self::getListLen(self::REDIS_PRE_TOP_ADD)) {
            $arrRes = self::removeListKey(self::REDIS_PRE_TOP_ADD);
            if ($arrRes === false) {
                continue;
            }
            $intForumId = intval($arrRes['ret'][self::REDIS_PRE_TOP_ADD]);
            if (empty($intForumId)) {
                continue;
            }
            $intCount++;
            if ($intCount > 500) {
                sleep(1);
                $intCount = 0;
            }
            $arrInput = array(
                'forum_id' => $intForumId,
                'top_num' => 3,
            );
            $arrRes = Tieba_Service::call('post', 'setTopThread', $arrInput);
            if ($arrRes === false || intval($arrRes['errno']) != Tieba_Errcode::ERR_SUCCESS) {
                self::addListKey(self::REDIS_PRE_TOP_ADD, $intForumId);
                echo "the forum_id=[" . $intForumId . "] add third top fail \n";
                continue;
            } else {
                echo "the forum_id=[" . $intForumId . "] add third top success \n";
            }
        }
        return true;

    }

    public static function executeDelThirdTop() { //删除第三条置顶
        echo __CLASS__ . "::" . __FUNCTION__ . $strData . '-' . $bolOtherFlag. '-' . $intPart . '-' . $intPartCount ."\n" ;
        $arrVipForumIds = self::_getThirdTopVIP();
        if(false === $arrVipForumIds)  {
            echo "get the third top vip forums failed \n";
            return false;
        }
        $intCount = 0;
        while (self::getListLen(self::REDIS_PRE_TOP_DEL)) {
            $arrRes = self::removeListKey(self::REDIS_PRE_TOP_DEL);
            if ($arrRes === false) {
                continue;
            }
            $intForumId = intval($arrRes['ret'][self::REDIS_PRE_TOP_DEL]);
            if (empty($intForumId) || in_array($intForumId,$arrVipForumIds)) {
                continue;
            }
            $intCount++;
            if ($intCount > 500) {
                sleep(1);
                $intCount = 0;
            }
            $arrInput = array(
                'forum_id' => $intForumId,
                'top_num' => 2,
            );
            $arrRes = Tieba_Service::call('post', 'setTopThread', $arrInput);
            if ($arrRes === false || intval($arrRes['errno']) != Tieba_Errcode::ERR_SUCCESS) {
                self::addListKey(self::REDIS_PRE_TOP_DEL, $intForumId);
                echo "the forum_id=[" . $intForumId . "] del third top fail \n";
                continue;
            } else {
                echo "the forum_id=[" . $intForumId . "] del third top success \n";
            }
        }
        return true;

    }

    public static function executePostAudit() { //删除第三条置顶
        echo __CLASS__ . "::" . __FUNCTION__ . $strData . '-' . $bolOtherFlag. '-' . $intPart . '-' . $intPartCount ."\n" ;
        $strTableName = 'mis_wordlist_post_manage_authority';
        $intCount = 0;
        while (self::getListLen(self::REDIS_PRE_POST_AUDIT)) {
            if ($intCount > 500) {
                sleep(1);
                $intCount = 0;
            }
            $arrRes = self::removeListKey(self::REDIS_PRE_POST_AUDIT);
            if ($arrRes === false) {
                continue;
            }
            $intForumId = intval($arrRes['ret'][self::REDIS_PRE_POST_AUDIT]);
            if (empty($intForumId)) {
                continue;
            }
            $intCount++;
            $arrInput = array(
                'forum_id' => (array)$intForumId,
            );
            $arrRes = Tieba_Service::call('forum', 'getFnameByFid', $arrInput);
            if ($arrRes === false || intval($arrRes['errno']) != Tieba_Errcode::ERR_SUCCESS) {
                self::addListKey(self::REDIS_PRE_POST_AUDIT, $intForumId);
                continue;
            }
            $strFname= strval($arrRes['forum_name'][$intForumId]['forum_name']);
            if(empty($strFname)) {
                continue;
            }
            $arrInput = array(
                'input' => array(
                    'table_name' => $strTableName,
                    'create_user' => 'bawuscript',
                    'create_time' => time(),
                    'value' => 1,
                    'remark' => 'bawuscript',
                    'expire' => 0,
                    'key' => Bingo_Encode::convert($strFname, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK),
                ),
            );
            $arrOutput = Tieba_Service::call('mis', 'addItem', $arrInput);
            if (Tieba_Errcode::ERR_SUCCESS !== intval($arrOutput['errno']) || false === $arrOutput) {
                self::addListKey(self::REDIS_PRE_POST_AUDIT, $intForumId);
                echo "add forum_id=[" . $intForumId . "] post audit fail\n";
                continue;
            } else {
                echo "add forum_id=[" . $intForumId . "] post audit success\n";
            }
        }
        return true;

    }

    private static function _getThirdTopVIP() {
        echo __CLASS__ . "::" . __FUNCTION__  ."\n" ;
        $db = self::_getDB();
        if($db === null) {
            echo "the db init fail \n";
            return false;
        }
        $strSql = "SELECT forum_id FROM forum_topthread;";
        $res = $db->query($strSql);
        if (false === $res) {
            echo "Select Failed. errno:[" . $db->errno() . "][" . $db->error() . "]sqlStr:" . $strSql . "\n";
            $arrRes = self::retrySql($strSql);
            if($arrRes === false) {
                return false;
            } else {
                $res = $arrRes;
            }
        }
        $arrRet = array();
        foreach ($res as $arrEach) {
            $arrRet[] = $arrEach['forum_id'];
        }
        return $arrRet;
    }

    public static function loadDataToRedis() {
        echo __CLASS__ . "::" . __FUNCTION__  ."\n" ;
        Bingo_Log::warning(__CLASS__ . "::" . __FUNCTION__ . self::$_strFilePath);
        $objCoreHandle = fopen(self::$_strFilePath, "r");
        if (!$objCoreHandle) {
            echo "load core file " . self::$_strFilePath . " fail \r\n";
            Bingo_Log::warning("load core file " . self::$_strFilePath . " fail ");
            return false;
        }
        Bingo_Log::warning("redis_key=".self::$_strRedisKey);
        $arrInput = array();
        if (self::_getRedisObj()) {
            while (!feof($objCoreHandle)) {
                $strLine = stream_get_line($objCoreHandle, self::MAX_LINE_LEN, "\n");
                if (empty($strLine)) {
                    continue;
                }
                $arrItems = explode("\t", $strLine);
                if (isset($arrItems[1])) {
                    unset($arrItems[2]);
                    if (self::$_strRedisKey === self::REDIS_PRE_CORE) {
                        unset($arrItems[3]);
                        unset($arrItems[9]);
                        unset($arrItems[10]);
                        unset($arrItems[18]);
                        unset($arrItems[19]);
                    }

                    if (self::$_strRedisKey === self::REDIS_PRE_BAWU) {
                        unset($arrItems[12]);
                        unset($arrItems[13]);
                    }
                    $strValue = implode('_', $arrItems);
                    $arrEach = array(
                        'key' => self::$_strRedisKey . intval($arrItems[1]),
                        'value' => $strValue,
                    );
                    //Bingo_Log::debug($arrEach['key']);
                    $arrInput['reqs'][] = $arrEach;
                }
                unset($arrItems);
                unset($strLine);
                if (count($arrInput['reqs']) >= 500) {
                    $arrRes = self::$_redis->SET($arrInput);
                    if (false === $arrRes || $arrRes['err_no'] != 0) {
                        echo "import redis data file and key is[" . self::$_strRedisKey . "] time[" . date("Y-m-d H:i:s") . "]";
                        if (!self::retrySetRedis($arrInput)) {
                            echo "import redis data file and key is[" . self::$_strRedisKey . "] time[" . date("Y-m-d H:i:s") . "]";
                            return false;
                        }
                    }
                    $arrInput['reqs'] = array();
                }
                Bingo_Log::getModule()->flush();
            }
            $arrRes = self::$_redis->SET($arrInput);
            if (false === $arrRes) {
                echo "import redis data file and key is[" . self::$_strRedisKey . "] time[" . date("Y-m-d H:i:s") . "]";
                if (!self::retrySetRedis($arrInput)) {
                    echo "import redis data file and key is[" . self::$_strRedisKey . "] time[" . date("Y-m-d H:i:s") . "]";
                    return false;
                }
            }
            $arrInput['reqs'] = array();
        } else {
            echo "redis obj init error!\r\n";
            return false;
        }
        return true;
    }

    public static function addListKey($key, $value) {
        $arrInput = array(
            'key' => $key,
            'value' => $value,
        );
        if (self::_getRedisObj()) {
            $arrRes = self::$_redis->RPUSH($arrInput);
            echo __FUNCTION__ . 'RPUSH'."-input[".serialize($arrInput) ."]output[".serialize($arrRes)."]\n" ;
            if ($arrRes === false || $arrRes['err_no'] != 0) {
                echo "add the key=[" . $key . "] and value=[" . $value . "]\n";
                Bingo_Log::debug("LPOP-$key-$value-failed");
                Bingo_Log::getModule()->flush();
                return false;
            }else{

                Bingo_Log::debug("LPOP-$key-$value-success");
                Bingo_Log::getModule()->flush();
            }
            return true;
        }
        //Bingo_Log::getModule()->flush();
        return false;
    }

    public static function removeListKey($key) {
        $arrInput = array(
            'key' => $key,
        );
        if (self::_getRedisObj()) {
            $arrRes = self::$_redis->LPOP($arrInput);
            echo __FUNCTION__ . 'LPOP'."-input[".serialize($arrInput) ."]output[".serialize($arrRes)."]\n";
            if ($arrRes === false || $arrRes['err_no'] != 0) {
                Bingo_Log::warning("LPOP-$key-$value-failed");
                Bingo_Log::getModule()->flush();
                return false;
            } else {
                Bingo_Log::debug("LPOP-$key-$value-success");
                Bingo_Log::getModule()->flush();
                return $arrRes;
            }
        }
        return false;
    }

    public static function getListDataByRange($key, $start, $end) {
        $arrInput = array(
            'key' => $key,
            'start' => $start,
            'stop' => $end,
        );
        if (self::_getRedisObj()) {
            $arrRes = self::$_redis->LRANGE($arrInput);
            echo __FUNCTION__ . 'LRANGE'."-input[".serialize($arrInput) ."]output[".serialize($arrRes)."]\n";
            if ($arrRes === false || $arrRes['err_no'] != 0) {
                echo "get the key=[" . $key . "] and start=[" . $start . "] and end=[ " . $end . "] failed\n";
                return false;
            }
            return $arrRes['ret'][$key];
        }
        return false;
    }

    public static function getListLen($key) {
        $arrInput = array(
            'key' => $key,
        );
        if (self::_getRedisObj()) {
            $arrRes = self::$_redis->LLEN($arrInput);
            echo __FUNCTION__ . 'LLEN'."-input[".serialize($arrInput) ."]output[".serialize($arrRes)."]\n";
            if ($arrRes === false || $arrRes['err_no'] != 0) {
                echo "get list len failed \n";

                Bingo_Log::warning("LPOP-$key-$value-fail");
                Bingo_Log::getModule()->flush();
                return 0;
            } else {

                Bingo_Log::debug("LPOP-$key-$value-success");
                Bingo_Log::getModule()->flush();
                return $arrRes['ret'][$key];
            }
        }
        return 0;
    }


    public static function setFlag($key, $value) {
        echo __CLASS__ . "::" . __FUNCTION__  .$key.':'.$value."\t" ;
        $key = self::getRedisKey($key);
        $arrInput = array(
            'key' => $key,
            'field' => $value,
            'value' => 1
        );
        if (self::_getRedisObj()) {
            $arrRes = self::$_redis->HSET($arrInput);
            echo __FUNCTION__ . 'HGET'."-input[".serialize($arrInput) ."]output[".serialize($arrRes)."]\n";
            if ($arrRes['err_no'] == 0) {
                Bingo_Log::warning("HSET-$key-$value-fail");
                Bingo_Log::getModule()->flush();
                return true;
            } else {
                Bingo_Log::debug("HSET-$key-$value-succ");
                Bingo_Log::getModule()->flush();
                return false;
            }
        }

    }

    public static function getAllFlag($key) {
        $arrOutPut = array();
        $key = self::getRedisKey($key);
        $arrInput = array(
            'key' => $key,
        );
        if (self::_getRedisObj()) {
            $arrRes = self::$_redis->HGETALL($arrInput);
            echo __FUNCTION__ . 'HGETALL'."-input[".serialize($arrInput) ."]output[".serialize($arrRes)."]\n";
            Bingo_Log::warning(__FUNCTION__ ."HGETALL-input[".serialize($arrInput) ."]output[".serialize($arrRes)."]");
                
            if ($arrRes['err_no'] == 0) {
                $arrTemp = $arrRes['ret'][$key];
                foreach ($arrTemp as $arrFlag) {
                    $arrOutPut[$arrFlag['field']] = $arrFlag['value'];
                }
            }
        }

        Bingo_Log::getModule()->flush();
        return $arrOutPut;
    }

    public static function delAllFlag($key) {
        echo __CLASS__ . "::" . __FUNCTION__  .$key."\n" ;
        $key = self::getRedisKey($key);
        $arrInput = array(
            'key' => $key,
        );
        if (self::_getRedisObj()) {
            $arrRes = self::$_redis->DEL($arrInput);
            echo __FUNCTION__ . 'DEL'."-input[".serialize($arrInput) ."]output[".serialize($arrRes)."]\n";
            Bingo_Log::warning(__FUNCTION__ ."DEL-input[".serialize($arrInput) ."]output[".serialize($arrRes)."]");
        }

        if ($arrRes['err_no'] == 0) {
            Bingo_Log::getModule()->flush();
            return true;
        } else {
            Bingo_Log::getModule()->flush();
            return false;
        }

    }

    public static function delFlag($key, $value) {
        echo __CLASS__ . "::" . __FUNCTION__  .$key.':'.$value."\n" ;
        $key = self::getRedisKey($key);
        $arrInput = array(
            'key' => $key,
            'field' => array(
                $value,
            ),
        );
        if (self::_getRedisObj()) {
            $arrRes = self::$_redis->HDEL($arrInput);
            echo __FUNCTION__ . 'HDEL'."-input[".serialize($arrInput) ."]output[".serialize($arrRes)."]\n";
            Bingo_Log::warning(__FUNCTION__ ."HDEL-input[".serialize($arrInput) ."]output[".serialize($arrRes)."]");

            
            if ($arrRes['err_no'] == 0) {
                Bingo_Log::getModule()->flush();
                return true;
            } else {
                Bingo_Log::getModule()->flush();
                return false;
            }
        }
    }

    public static function retrySql($strSql) { //重试sql
        for ($i = 0; $i < self::MAX_RETRY; $i++) {
            $db = self::_getDB();
            $arrRes = $db->query($strSql);
            if ($arrRes === false) {
                $time = $i * 30;
                sleep($time);//休眠时间与失败的次数成正比
            } else {
                echo "success execute sql after retry the times =[" . $i . "] \n";
                return $arrRes;
            }
        }
        echo "execute sql failed after" . self::MAX_RETRY . "times\n";
        return false;
    }


    public static function retrySetRedis($arrInput) { //重试写redis
        for ($i = 0; $i < self::MAX_RETRY; $i++) {
            if (self::_getRedisObj()) {
                $arrRes = self::$_redis->SET($arrInput);
                if (false === $arrRes || $arrRes['err_no'] != 0) {
                    $time = $i * 30;
                    sleep($time);//休眠时间与失败的次数成正比
                } else {
                    echo "retry the times =[" . $i . "]\n";
                    return true;
                }
            }
        }
        echo "execute redis failed after" . self::MAX_RETRY . "times\n";
        return false;
    }

    public static function retryGetRedis($arrInput) { //重试读redis
        for ($i = 0; $i < self::MAX_RETRY; $i++) {
            if (self::_getRedisObj()) {
                $arrRes = self::$_redis->GET($arrInput);
                if (false === $arrRes || $arrRes['err_no'] != 0 || !isset($arrRes['ret'])) {
                    $time = $i * 30;
                    sleep($time);//休眠时间与失败的次数成正比
                } else {
                    echo "retry the times =[" . $i . "]\n";
                    return $arrRes;
                }
            }
        }
        echo "execute redis failed after" . self::MAX_RETRY . "times\n";
        return false;
    }

    public static function clearFiles(){
        $strLastDay = date("Ymd", time() - 86400 * 7);
        $strLastMon = date("Ym", time() - 86400 * 31);
        $arrHardDelete = array(
            "2016",
            "201701",
            "201702",
            "201703",
        );
        $arrPrefix = array(
            'bawudata_',
            'forumdata_',
            'otherdata_',
        );
        foreach($arrPrefix as $strPrefix){
            $fileLastDay = $strPrefix . $strLastDay;
            exec("rm -rf ./$fileLastDay*");
            $fileLastMon = $strPrefix . $strLastMon;
            exec("rm -rf ./$fileLastMon*");
            foreach($arrHardDelete as $strLast){
                $fileLast = $strPrefix . $strLast;
                exec("rm -rf ./$fileLast*");
            }
        }
        return true;
    }

}
