<?php
/***************************************************************************
 * 
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file data_report.php
 * <AUTHOR>
 * @date 2013/08/29 10:38:46
 * @brief 
 *  	not in use
 **/
 
set_time_limit(0);
ini_set('memory_limit', '2G');

define('ROOT_PATH', dirname(__FILE__).'/..');

class DataReport
{
	const BAWU_DB_NAME = 'forum_bawu_beiku';
	const ATTR_DB_NAME = 'DB_forum_attribute';
	const GRADE_DB_NAME = 'db_dl_btx_btx';
	const CARD_DB_NAME = 'db_forum_consume';


    private static function _getDB($dbname)
    {
		$db = new Bd_DB();
		if($db == null){
			echo "new bd_db fail.\n";
			return null;
		}
		$r = $db->ralConnect($dbname);
        if(!$r){
            echo "bd db ral connect fail.";
            return null;
		}
		return $db;
    }

    private static function _queryDB($dbname, $strSql)
    {
        $db = self::_getDB($dbname);
        if ( !$db ){
            return false;
        }
        $arrRet = $db->query($strSql);
        if ( $arrRet === false ){
            echo "execute sql error [$strSql]\n";
		}
        return $arrRet;
    }
	
	private static function _getGradeCount($table)
	{
		$arrGrades = array();
		$ret = self::_queryDB(self::ATTR_DB_NAME, "SELECT fid, attr_value FROM $table WHERE attr_name='forum_grade'");
		return $ret;
	}
	
	public static function getGradeData()
	{
		$grade_data = array(0=>0,1=>0,2=>0,3=>0,4=>0,5=>0);
		for($i = 0; $i <= 99; $i ++) {
			$ret = self::_getGradeCount ( 'forum_attr' . $i );
			foreach ( $ret as $one ) {
				$grade = unserialize ( $one ['attr_value'] );
				$grade_data [intval ( $grade ['grade'] )] ++;
			}		
		}
		return $grade_data;
	}
	
	public static function getUpAndDown()
	{
		$curDate = intval(date('Ymd', time()-86400*2));
		$ret = self::_queryDB(self::BAWU_DB_NAME, "SELECT count(forum_id) total FROM data_info WHERE date=$curDate AND result=1");
		$up_num = $ret[0]['total'];
		$ret = self::_queryDB(self::BAWU_DB_NAME, "SELECT count(forum_id) total FROM data_info WHERE date=$curDate AND result=2");
		$down_num = $ret[0]['total'];
		return array(
			'up_num' => $up_num,
			'down_num' => $down_num,
		);
	}
	
	public static function getForumDirReq()
	{
		$todaytime = strtotime(date('Ymd'), time()-86400);
		$yesterdaytime = $todaytime-86400;
		$sql = "SELECT count(id) total FROM dir_mis_applydir WHERE optime>=$yesterdaytime AND optime<$todaytime";
		$ret = self::_queryDB(self::GRADE_DB_NAME, $sql);
		return $ret[0]['total'];
	}
	
	public static function getForumDirPass()
	{
		$todaytime = strtotime(date('Ymd'), time()-86400);
		$yesterdaytime = $todaytime-86400;
		$sql = "SELECT count(id) total FROM dir_mis_applydir WHERE adtime>=$yesterdaytime AND adtime<$todaytime AND status=1";
		$ret = self::_queryDB(self::GRADE_DB_NAME, $sql);
		return $ret[0]['total'];
	}
	
	public static function getForumDirFail()
	{
		$todaytime = strtotime(date('Ymd'), time()-86400);
		$yesterdaytime = $todaytime-86400;
		$sql = "SELECT count(id) total FROM dir_mis_applydir WHERE adtime>=$yesterdaytime AND adtime<$todaytime AND status=0";
		$ret = self::_queryDB(self::GRADE_DB_NAME, $sql);
		return $ret[0]['total'];
	}
	
	public static function getFcardReq()
	{
		$todaytime = strtotime(date('Ymd'), time()-86400);
		$yesterdaytime = $todaytime-86400;
		$sql = "SELECT count(id) total FROM forum_card_table WHERE application_time>=$yesterdaytime AND application_time<$todaytime";
		$ret = self::_queryDB(self::CARD_DB_NAME, $sql);
		return $ret[0]['total'];
	}
	
	public static function getFcardPass()
	{
		$todaytime = strtotime(date('Ymd'), time()-86400);
		$yesterdaytime = $todaytime-86400;
		$sql = "SELECT count(id) total FROM forum_card_table WHERE examine_time>=$yesterdaytime AND examine_time<$todaytime AND status=1";
		$ret = self::_queryDB(self::CARD_DB_NAME, $sql);
		return $ret[0]['total'];
	}
	
	public static function getFcardFail()
	{
		$todaytime = strtotime(date('Ymd'), time()-86400);
		$yesterdaytime = $todaytime-86400;
		$sql = "SELECT count(id) total FROM forum_card_table WHERE examine_time>=$yesterdaytime AND examine_time<$todaytime AND status=2";
		$ret = self::_queryDB(self::CARD_DB_NAME, $sql);
		return $ret[0]['total'];
	}
	
	public static function getThreshold()
	{
		$curDate = intval(date('Ymd', time()-86400*2));
		$ret = self::_queryDB(self::BAWU_DB_NAME, "SELECT count(*) total FROM data_info WHERE date=$curDate AND ip_num/cookie_num<0.25");
		$thres1 = $ret[0]['total'];
		$ret = self::_queryDB(self::BAWU_DB_NAME, "SELECT count(*) total FROM data_info WHERE date=$curDate AND cookie_num/post_num<5");
		$thres2 = $ret[0]['total'];
		$ret = self::_queryDB(self::BAWU_DB_NAME, "SELECT count(*) total FROM data_info WHERE date=$curDate AND member_sign_num/sign_num<0.9");
		$thres3 = $ret[0]['total'];
		return array(
			'thres1' => $thres1,
			'thres2' => $thres2,
			'thres3' => $thres3,
		);
	}
	
	public static function getCheatNum()
	{
		$curDate = intval(date('Ymd', time()-86400*2));
		$ret = self::_queryDB(self::BAWU_DB_NAME, "SELECT count(*) total FROM data_info WHERE date=$curDate AND result=5");
		$cheat_num = $ret[0]['total'];
		return $cheat_num;
	}
	
	public static function getSuspectNum()
	{
		$curDate = intval(date('Ymd', time()-86400*2));
		$ret = self::_queryDB(self::BAWU_DB_NAME, "SELECT count(*) total FROM data_info WHERE date=$curDate AND result<>5 AND ip_num/cookie_num<0.25 AND cookie_num/post_num<5 AND member_sign_num/sign_num<0.9");
		$cheat_num = $ret[0]['total'];
		return $cheat_num;
	}
	
	public static function getPartialLevel2()
	{
		$curDate = intval(date('Ymd', time()-86400*2));
		$ret = self::_queryDB(self::BAWU_DB_NAME, "SELECT count(*) total FROM data_info WHERE date=$curDate AND cur_grade<2 AND cookie_num<400 AND thread_num*5>=20 AND (thread_num+post_num)/5/thread_num>=40 AND sign_num/100/total_member_num>=2");
		$plevel2 = $ret[0]['total'];
		return $plevel2;
	}
	
	public static function getPartialLevel3()
	{
		$curDate = intval(date('Ymd', time()-86400*2));
		$ret = self::_queryDB(self::BAWU_DB_NAME, "SELECT count(*) total FROM data_info WHERE date=$curDate AND cur_grade<3 AND cookie_num<1500 AND thread_num*5>=100 AND (thread_num+post_num)/5/thread_num>=100 AND sign_num/100/total_member_num>=5");
		$plevel3 = $ret[0]['total'];
		return $plevel3;
	}
}
$start_time = time();

$fcard_req = DataReport::getFcardReq();
$fcard_pass = DataReport::getFcardPass();
$fcard_fail = DataReport::getFcardFail();

$fdir_req = DataReport::getForumDirReq();
$fdir_pass = DataReport::getForumDirPass();
$fdir_fail = DataReport::getForumDirFail();

$grade_data=DataReport::getGradeData();
$level_0_num = $grade_data[0];
$level_1_num = $grade_data[1];
$level_2_num = $grade_data[2];
$level_3_num = $grade_data[3];
$level_4_num = $grade_data[4];
$level_5_num = $grade_data[5];
$partial_level_3=DataReport::getPartialLevel3();
$partial_level_2=DataReport::getPartialLevel2();
$up_down_data=DataReport::getUpAndDown();
$up_num = $up_down_data['up_num'];
$down_num = $up_down_data['down_num'];

$cheat_num=DataReport::getCheatNum();
$thres = DataReport::getThreshold();
$thres1=$thres['thres1'];
$thres2=$thres['thres2'];
$thres3=$thres['thres3'];
$suspect_num=DataReport::getSuspectNum();

$end_time = time();
$total_time = $end_time-$start_time;

$curdate = date('Y-m-d',time()-86400*2);
$to = '<EMAIL>,<EMAIL>,<EMAIL>';
$subject =  "=?UTF-8?B?" . base64_encode("吧激励行为数据报表 $curdate") . "?=";
$content ="
<html><head>
<style type=\"text/css\">
table{
	border-left: 1px solid #aaaaaa;
	border-top: 1px solid #aaaaaa;
	border-bottom: 1px solid #aaaaaa;
	padding:0;
}
td{
	border-right: 1px solid #aaaaaa;
	color: black;
	padding-left: 4px;
	padding-right: 4px;
}
th{
	border-right: 1px solid #aaaaaa;
	border-bottom: 1px solid #aaaaaa;
	color: blue;
	padding:0;
}
</style></head>
<body>
<p>
日期 $curdate
</p>
<table>
<tr>
<th>统计项</th>
<th>结果</th>
<th>备注</th>
</tr>
<tr>
<td>提交/变更吧名片吧的请求数</td><td>$fcard_req</td><td> </td>
</tr>
<tr>
<td>通过吧名片请求数</td><td>$fcard_pass</td><td> </td>
</tr>
<tr>
<td>拒绝吧名片请求数</td><td>$fcard_fail</td><td> </td>
</tr>
<tr>
<td>提交/变更吧目录请求数</td><td>$fdir_req</td><td> </td>
</tr>
<tr>
<td>通过吧目录请求数</td><td>$fdir_pass</td><td> </td>
</tr>
<tr>
<td>拒绝吧目录请求数</td><td>$fdir_fail</td><td> </td>
</tr>
<tr>
<td>等级为5（自成一派）的吧数</td><td>$level_5_num</td><td> </td>
</tr>
<tr>
<td>等级为4（卓有成效）的吧数</td><td>$level_4_num</td><td> </td>
</tr>
<tr>
<td>等级为3（不同凡响）的吧数</td><td>$level_3_num</td><td> </td>
</tr>
<tr>
<td>等级为2（小有成就）的吧数</td><td>$level_2_num</td><td> </td>
</tr>
<tr>
<td>等级为1（广邀名帖）的吧数</td><td>$level_1_num</td><td> </td>
</tr>
<tr>
<td>等级为0（初出茅庐）的吧数</td><td>$level_0_num</td><td> </td>
</tr>
<tr>
<td>除cookie其他指标满足等级3的吧数</td><td>$partial_level_3</td><td> </td>
</tr>
<tr>
<td>除cookie其他指标满足等级2的吧数</td><td>$partial_level_2</td><td> </td>
</tr>
<tr>
<td>判定作弊的吧数</td><td>$cheat_num</td><td> </td>
</tr>
<tr>
<td>疑似弊的吧数</td><td>$suspect_num</td><td> </td>
</tr>
<tr>
<td>低于监控指标1阈值的吧数</td><td>$thres1</td><td>吧独立访问ip数/吧访问cookie数<25%</td>
</tr>
<tr>
<td>低于监控指标2阈值的吧数</td><td>$thres2</td><td>吧访问cookie数/吧回贴人数<5</td>
</tr>
<tr>
<td>低于监控指标3阈值的吧数</td><td>$thres3</td><td>吧会员签到数/吧签到数<90%</td>
</tr>
<tr>
<td>低于监控指标4阈值的吧数</td><td>待定</td><td> </td>
</tr>
<tr>
<td>低于监控指标5阈值的吧数</td><td>待定</td><td> </td>
</tr>
<tr>
<td>低于监控指标6阈值的吧数</td><td>待定</td><td> </td>
</tr>
</table>
<p>
执行总耗时: $total_time 秒
</p>
</body></html>
";

$headers = "MIME-Version: 1.0
Content-type:text/html;charset=utf-8
";
mail($to,$subject,$content,$headers);


/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
