<?php
/***************************************************************************
*   
*    Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
*     
***************************************************************************/

    /**
     * @file takeoff_lazybazhu.php
     * <AUTHOR>
     * @date 2017/06/08 16:52:00
     * @brief 
     *  根据吧名,人名卸吧主
     **/
ini_set ( "memory_limit", "2G"  );
//define ( 'MODULE_NAME', 'postaudit' );
define ( 'ROOT_PATH', dirname ( __FILE__  ) . "/../../.."  );
define ( 'HOME_PHP_PATH', realpath ( ROOT_PATH . '/php/bin/php'  )  );
define ( 'IS_ORP_RUNTIME', true  );

Tieba_Init::init ( 'bawu');
define('NAME_SCRIPT', 'removebazhu_takeofflazybazhu');               
Bingo_Log::init(array(                                                              
    LOG => array(                                                                   
         'file'  => dirname ( __FILE__ ) . '/../../../log/app/bawu/'.NAME_SCRIPT.'.log',                                       
          'level' => 0x04 | 0x02,                                                       
      ),                                                                    
  ), LOG);
if (! defined ( 'REQUEST_ID'  )) {
    define ( 'REQUEST_ID', Bingo_Log::getLogId ()  );
}


if (function_exists ( 'camel_set_logid'  )) {
    camel_set_logid ( REQUEST_ID  );
}
class Script_takeofflazyBazhu{
    /**
     * @param array
     * @return  true false
     **/
    public function process() {
        $filename   = dirname(__FILE__).'/input';
        $arrFidUid   = file( $filename ,FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES );
        $arrFid = array();
        $arrUid = array();
        foreach($arrFidUid as $value){
            $arrtmp  = explode(" ",$value ); 
            $arrFid[]=$arrtmp[0];
            $arrUid[]=$arrtmp[1];
        }
        $totalForum = count($arrFid);
        for( $i=0 ; $i< $totalForum ; $i+=50 ){ 
            $arrFidThisTurn = array_slice($arrFid,$i,50);//每次处理从第i+1个吧到第i+50个吧（不足50取后面所有）
            $arrUidThisTurn = array_slice($arrUid,$i,50);
            $arrFname=array();
            $arrUname=array();
            $arrFnametmp  =  $this->_getFnameByarrFid($arrFidThisTurn);
            $arrUnametmp  =  $this->_getUnameByarrUid($arrUidThisTurn);
            if($arrFnametmp!==false&&$arrUnametmp!==false){
                foreach($arrFnametmp as $fid =>$value){
                    $arrFname[$fid] = $value[$fid]['forum_name']; 
                }
                foreach($arrUnametmp as $value){
                    $arrUname[$value['user_id']] = $value['user_name']; 
                }
                for( $j=0 ;$j < count($arrFidThisTurn); $j++ ){
                    $info = array(
                            "forum_id"     => $arrFidThisTurn[$j], //吧id
                            "forum_name"   => $arrFname[$arrFidThisTurn[$j]], //吧名
                            "user_id"      => $arrUidThisTurn[$j], //用户id
                            "user_name"    => $arrUname[$arrUidThisTurn[$j]], //用户名
                            "op_user_id"   => 40474, //操作者的用户id
                            "op_user_name" => "majianpeng",
                            "role_name"    => "manager" //角色名
                            );
                    $result =   $this->_delbazhuRole($info);
                    if($result === true){
                        echo "delete bazhu success ,forumid=:".$arrFidThisTurn[$j]." userid=".$arrUidThisTurn[$j]."\n";   
                    }
                    else{
                        echo "delete bazhu failed ,forumid=:".$arrFidThisTurn[$j]." userid=".$arrUidThisTurn[$j]."\n";
                    }
                    usleep(100000);
                }
            }
            if($arrFnametmp  === false){
                echo "get ForumName failed,forumNums start at ". ($i+1) ."\n";
            }
            if($arrUnametmp  === false){
                echo "get UserName failed,UserNums start at ". ($i+1) ."\n";
            }
        }
    }
    /**
     * @param array
     * @return  true false
     **/
    private function _delbazhuRole($arrInfo){
        if (empty($arrInfo)){
            echo "_delbazhuRole invalid params \n";
            return false;
        }
        $arrOutput   = Tieba_Service::call('perm', 'delUserRole', $arrInfo, null, null, 'post', 'php', 'utf-8');
        if($arrOutput === false)
        {
            echo "talk with service:[perm] method:[delUserRole] error! input[" . serialize($arrInfo) . "] \n";
            return false;
        }
        if(isset($arrOutput['errno']) && $arrOutput['errno'] != 0)
        {
            echo "get data from service:[perm] method:[delUserRole] error! input[". serialize($arrInfo) . "] output[" . serialize($arrOutput) . "] \n ";
            return false;
        }
        return true;
    }
    /**
     * @param array
     * @return  array / false
     **/
    private function _getFnameByarrFid($arrFid){
        if (empty($arrFid)){
            echo "_getFnameByarrFid invalid params \n";
            return false;
        }
        $arrInput = array('forum_id' => $arrFid );
        $arrOutput = Tieba_Service::call('forum', 'getFnameByFid', $arrInput,null,null, 'post', 'php', 'utf-8' );
        if($arrOutput === false){
            echo "talk with service:[forum] method:[getFnameByFid] error! input[" . serialize($arrInput) . "] \n";
            return false;
        }
        if(isset($arrOutput['errno']) && $arrOutput['errno'] != 0){
            echo "get data from service:[forum] method:[getFnameByFid] error! input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "] \n";
            return false;
        }
        return $arrOutput['forum_name'];
    }
    /**
     * @param array
     * @return  array / false
     **/
    private function _getUnameByarrUid($arrUid){
        if (empty($arrUid)){
            echo "_getunameByarruid invalid params \n";
            return false;
        }
        $arrInput = array('user_id' => $arrUid );
        $arrOutput = Tieba_Service::call('user', 'getUnameByUids', $arrInput,null,null, 'post', 'php', 'utf-8' );
        if($arrOutput === false){
            echo "talk with service:[user] method:[getUnameByUids] error! input[" . serialize($arrInput) . "] \n";
            return false;
        }
        if(isset($arrOutput['errno']) && $arrOutput['errno'] != 0){
            echo "get data from service:[user] method:[getUnameByUids] error! input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "] \n";
            return false;
        }
        return $arrOutput['output']['unames'];
    }
}
$obj = new Script_takeofflazyBazhu();
$ret = $obj->process();
if($ret){
    exit(0);
}
exit(1);
?>
