<?php
/***************************************************************************
*   
*    Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
*     
***************************************************************************/

    /**
     * @file takeoff_bazhu.php
     * <AUTHOR>
     * @date 2017/06/01 20:44:00
     * @brief 
     *  根据吧名卸任AV原有大吧主
     **/
ini_set ( "memory_limit", "2G"  );
//define ( 'MODULE_NAME', 'postaudit' );
define ( 'ROOT_PATH', dirname ( __FILE__  ) . "/../../.."  );
define ( 'HOME_PHP_PATH', realpath ( ROOT_PATH . '/php/bin/php'  )  );
define ( 'IS_ORP_RUNTIME', true  );

Tieba_Init::init ( 'bawu');
define('NAME_SCRIPT', 'removebazhu_takeoffbazhu');               
Bingo_Log::init(array(                                                              
    LOG => array(                                                                   
         'file'  => dirname ( __FILE__ ) . '/../../../log/app/bawu/'.NAME_SCRIPT.'.log',                                       
          'level' => 0x04 | 0x02,                                                       
      ),                                                                    
  ), LOG);
if (! defined ( 'REQUEST_ID'  )) {
    define ( 'REQUEST_ID', Bingo_Log::getLogId ()  );
}


if (function_exists ( 'camel_set_logid'  )) {
    camel_set_logid ( REQUEST_ID  );
}
class Script_takeoffBazhu{

    public function process() {
        $filename   = dirname(__FILE__).'/Forumname';
        $arrFname   = file( $filename ,FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES );
        $totalForum = count($arrFname);
        for( $i=0 ; $i< $totalForum ; $i+=100 ){ 
            $arrFnameThisTurn = array_slice($arrFname,$i,100);//每次处理从第i+1个吧到第i+100个吧（不足100取后面所有）   
            $arrFid  =  $this->_getFidByarrFname($arrFnameThisTurn);
            if($arrFid!==false){
                foreach($arrFid['forum_id'] as  $forumInfo){
                    $forumId = ($forumInfo['is_forbidden']==1)?$forumInfo['forbid_forum_id']:$forumInfo['forum_id'] ;
                    $ManagerList = $this->_getManagerListByForumId($forumId);
                    if($ManagerList!==false){
                        foreach($ManagerList['output'] as $managerInfo){
                            $info = array(
                                    "forum_id"     => $forumId, //吧id
                                    "forum_name"   => $forumInfo['qword'], //吧名
                                    "user_id"      => $managerInfo['user']['user_id'], //用户id
                                    "user_name"    => $managerInfo['user']['user_name'], //用户名
                                    "op_user_id"   => 40474, //操作者的用户id
                                    "op_user_name" => "majianpeng",
                                    "role_name"    => "manager" //角色名
                                    );
                            $this->_delbazhuRole($info);
                        }
                    }
                    else{
                        echo "get ManagerList failed,forumId =  ".$forumId."\n";
                    }
                }
            }
            else{
                echo "get ForumId failed,forumNums start at ". ($i+1) ."\n";
            }
        }
    }
    /**
     * @param array
     * @return  true false
     **/
    private function _delbazhuRole($arrInfo){
        if (empty($arrInfo)){
            echo "_delbazhuRole invalid params \n";
            return false;
        }
        for($i=0;$i<10;$i++){
            $arrOutput   = Tieba_Service::call('perm', 'delUserRole', $arrInfo, null, null, 'post', 'php', 'utf-8');
            if($arrOutput === false)
            {
                echo "talk with service:[perm] method:[delUserRole] error! input[" . serialize($arrInfo) . "] \n";
                continue;
            }
            if(isset($arrOutput['errno']) && $arrOutput['errno'] != 0)
            {
                echo "get data from service:[perm] method:[delUserRole] error! input[". serialize($arrInfo) . "] output[" . serialize($arrOutput) . "] \n ";
                continue;
            }
            else {
                break;
            }
        }
        if($i===10) {
            return false;
        }
        return true;
    }
    /**
     * @param string fid
     * @return  array / false
     **/
    private function _getManagerListByForumId($strFid){
        if ($strFid<=0){
            echo "_getManagerListByForumId invalid params \n";
            return false;
        }
        $arrInput = array('forum_id' => $strFid );
        for($i=0;$i<10;$i++){
            $arrOutput  = Tieba_Service::call('perm', 'getManagerList', $arrInput, null, null, 'post', 'php', 'utf-8');
            if($arrOutput === false)
            {
                echo "talk with service:[perm] method:[getManagerList] error! input[" . serialize($arrInput) . "] \n";
                continue;
            }
            if(isset($arrOutput['errno']) && $arrOutput['errno'] != 0)
            {
                echo "get data from service:[perm] method:[getManagerList] error! input[". serialize($arrInput) . "] output[" . serialize($arrOutput) . "] \n";
                continue;
            }
            else {
                break;
            }
        }
        if($i===10) {
            return false;
        }
        return $arrOutput;
    }
    /**
     * @param array
     * @return  array / false
     **/
    private function _getFidByarrFname($arrFname){
        if (empty($arrFname)){
            echo "_getFidByarrFname invalid params \n";
            return false;
        }
        $arrInput = array('query_words' => $arrFname );
        for($i=0;$i<10;$i++){
            $arrOutput = Tieba_Service::call('forum', 'getFidByFname', $arrInput,null,null, 'post', 'php', 'utf-8' );
            if($arrOutput === false){
                echo "talk with service:[forum] method:[getFidByFname] error! input[" . serialize($arrInput) . "] \n";
                continue;
            }
            if(isset($arrOutput['errno']) && $arrOutput['errno'] != 0){
                echo "get data from service:[forum] method:[getFidByFname] error! input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "] \n";
                continue;
            }
            else{
                break;
            }
        }
        if($i===10) {
            return false;
        }
        return $arrOutput;
    }
}
$obj = new Script_takeoffBazhu();
$ret = $obj->process();
if($ret){
    exit(0);
}
exit(1);
?>
