<?
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file primanage.php
 * <AUTHOR> @date 2018-12-04
 * @version [<10.0>] [<description>]
 * @brief 私有化吧吧务后台相关service
 *  
 **/
require_once ROOT_PATH.'app/bawu/service/priforum/Priforum.php';
class Service_Primanage_Primanage {
	const SERVICE_NAME  = "Service_Primanage_Primanage";
 	const DATABASE_NAME = "forum_priforum";
 	const USE_HANE_NO_POWER = 21005;
 	const PRI_CONTENT_ASSIST = "pri_content_assist"; //内容管理吧务
    const PRI_MANAGE_ASSIST  = "pri_manage_assist";   //团队管理吧务
    const PRI_BAWU_SUM       = 10;  //私有化吧务总个数
    const PRI_BAZHU_TYPE     = 3;//私有化吧主
    const PRI_CONTENT_TYPE   = 1; //内容管理
    const PRI_MANAGER_TYPE   = 2; //管理
    const USER_MAX_PRI_BAWU  = 3; //用户担任最大私有化吧吧务数
 	/**
 	 * [_errRet description]
 	 * @param  [type] $errno [description]
 	 * @param  array  $data  [description]
 	 * @return [type]        [description]
 	 */
	private static function _errRet($errno, $data = array()){
		return array(
			'errno'  => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
			'data'   => $data,
		);
	}

	/**
	 * [getPribawuList 获取私有化吧务列表]
	 * @param [type] $[arrInput] [<description>]
	 * @return [type] [description]
	 */
	public static function getPribawuList($arrInput) {
		if (empty($arrInput['op_uid']) || empty($arrInput['forum_id']) || empty($arrInput['forum_name'])) {
			Bingo_Log::warning('invalid input param forum_id['.$arrInput['forum_id'].']user_id['.$arrInput['user_id'].']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		//判断私有化吧主的权限
        $arrParams = array(
        	'forum_name' => strval($arrInput['forum_name']),
        	'forum_id'   => intval($arrInput['forum_id']),
        	'user_id'    => intval($arrInput['op_uid']),
            'user_ip'    => intval($arrInput['user_ip']),
            'user_ip6'   => strval($arrInput['user_ip6']),
        );
        $arrOutput = Tieba_Service::call('bawu','getForumBasicInfo',$arrParams);
        if ($arrOutput == false || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('call  bawu:getForumBasicInfo fail! input: ['. serialize($arrParams).'], output:[ ' . serialize($arrOutput) . ']');
        	return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}
		$arrPermInfo = $arrOutput['data'];
		if (1 != $arrPermInfo['is_private_forum']  //0非私有化吧 1私有化吧
			|| 1 >= $arrPermInfo['private_forum_status']  // 1试运营 2正式 3分润
			|| 1 != $arrPermInfo['is_manager']) {
			Bingo_Log::warning('bazhu ho bazhu perm!');
			return self::_errRet(Tieba_Errcode::BAZHU_NO_BAZHU_PERM);
		}
		//吧务列表
        $arrBawuList = Util_Perm::getBawuList($arrInput);
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrBawuList['output']);
	}

	/**
	 * [addPribawu 私有化吧务添加]
	 * @param [type] $[name] [<description>]
	 * @param [type] $arrInput [description]
	 */
	public static function addPribawu($arrInput) {
		if ((empty($arrInput['user_name']) && empty($arrInput['user_id'])) || empty($arrInput['forum_id']) || empty($arrInput['forum_name']) || empty($arrInput['op_uid']) || empty($arrInput['op_uname']) || empty($arrInput['bawu_type'])) {
			Bingo_Log::warning('invalid input param . input:['. serialize($arrInput) . ']');
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		//增加支持通过uid加吧务 by xudanxian 2018.12.7
		if (!empty($arrInput['user_name'])) {
            $arrNames[]  = $arrInput['user_name'];
            $arrUserInfo = Util_User::getUidByNames($arrNames);
            $addUserId   = $arrUserInfo[$arrInput['user_name']][0]['user_id'];
        } else {
            $addUserId = $arrInput['user_id'];
            $arrUserName = Util_User::getNameByUids_($addUserId);
            if (!isset($arrUserName[$addUserId])) {
                Bingo_Log::warning('invalid input param user_id. input:[user_id:'. $addUserId . ']');
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $arrInput['user_name'] = $arrUserName[$addUserId];
        }

		$strBawuType = $arrInput['bawu_type'];
        if($addUserId == false){
        	Bingo_Log::warning('invalid input param forum_id['.$arrInput['forum_id'].']user_id['.$addUserId.']');
        	return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        //判断私有化吧主的权限
        $arrParams = array(
        	'forum_name' => strval($arrInput['forum_name']),
        	'forum_id'   => intval($arrInput['forum_id']),
        	'user_id'    => intval($arrInput['op_uid']),
            'user_ip'    => intval($arrInput['user_ip']),
            'user_ip6'   => strval($arrInput['user_ip6']),
        );
        $arrOutput = Tieba_Service::call('bawu','getForumBasicInfo',$arrParams,null,null,'post','php','utf8');
        if ($arrOutput == false || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('call  bawu:getForumBasicInfo fail! input: ['. serialize($arrParams).'], output:[ ' . serialize($arrOutput) . ']');
        	return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}

        $arrPermInfo = $arrOutput['data'];
        //private_forum_status: 1试运营 2正式 3分润
		if (1 >= $arrPermInfo['private_forum_status']) {
            Bingo_Log::warning('forum is not normal, fid:'.$arrInput['forum_id'].' private_forum_status='.$arrPermInfo['private_forum_status']);
            return self::_errRet(Tieba_Errcode::ERR_FORUM_NOT_NORMAL);
        }
        //0非私有化吧 1私有化吧
        if (1 != $arrPermInfo['is_private_forum']) {
            Bingo_Log::warning('forum is not private forum, fid:'.$arrInput['forum_id'].' is_private_forum='.$arrPermInfo['is_private_forum']);
            return self::_errRet(Tieba_Errcode::ERR_FORUM_NOT_PRIVATE);
        }


        if (1 != $arrPermInfo['is_manager']) {
			Bingo_Log::warning('bazhu ho bazhu perm!');
			return self::_errRet(Tieba_Errcode::BAZHU_NO_BAZHU_PERM);
		}

        //判断添加类型
        $arrType = array(
        	self::PRI_MANAGE_ASSIST,
        	self::PRI_CONTENT_ASSIST,
        );
        if(!in_array($strBawuType, $arrType)){
            Bingo_Log::warning("bawu_type invalid [".$strBawuType."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrTypeMap = array(
        	'manager'  => 'can_type2_audit_post',
        	'pri_content_assist' => 'can_type4_audit_post',
        	'pri_manage_assist'   => 'can_type5_audit_post',
        );
        //判断被任命用户是否有权限
        $arrPermInput = array(
        	'forum_id' => $arrInput['forum_id'],
        	'user_id'  => $addUserId,
        	'user_ip'  => 0,
        );
        $arrUserPerm = Util_Perm::getUserPerm($arrPermInput);
        if(!$arrUserPerm['can_tobe_pri_content_assist'] && !$arrUserPerm['can_tobe_pri_manage_assist']){
            Bingo_Log::warning('user have no perm! input=['.serialize($arrPermInput).'] output=['.serialize($arrUserPerm).']');
            return self::_errRet(Tieba_Errcode::ERR_NO_RIGHT);
        }
        //判断用户是否已经在该职位上
        if (isset($arrTypeMap[$strBawuType])) {
            $intType = $arrUserPerm[$arrTypeMap[self::PRI_CONTENT_ASSIST]] || $arrUserPerm[$arrTypeMap[self::PRI_MANAGE_ASSIST]] || $arrUserPerm[$arrTypeMap['manager']];   
        }
        if ($intType) {
        	Bingo_Log::warning('user is already bawu!');
        	return self::_errRet(Tieba_Errcode::BZC_ALREADY_THIS_ROLE);
        }

        //用户的吧务数量判断，数量需要内容和管理吧务的和不大于3，跟线上公有化吧务不冲突
        $arrRoleOut  = Util_Perm::getUserPriBawuInfo($addUserId);
        $intConUserCur  = count($arrRoleOut[self::PRI_CONTENT_ASSIST]); //用户当前内容吧务数量
        $intManUserCur  = count($arrRoleOut[self::PRI_MANAGE_ASSIST]); //用户当前管理吧务数量
        if (self::USER_MAX_PRI_BAWU <= ($intConUserCur + $intManUserCur) ) {
            Bingo_Log::warning('user is already bawu limited!');
            return self::_errRet(Tieba_Errcode::ERROR_USER_BAWU_NUM_LIMITED);
        }

        //吧务管理&内容管理角色不能超过10个
        $arrBawuList = Util_Perm::getBawuList($arrInput);
        $intContentBawuNum  = count($arrBawuList['output'][self::PRI_CONTENT_ASSIST]);
        $intManageBawuNum   = count($arrBawuList['output'][self::PRI_MANAGE_ASSIST]);
        if (self::PRI_BAWU_SUM <= ($intContentBawuNum + $intManageBawuNum)) {
        	Bingo_Log::warning('priforum bawu num is limited!');
        	return self::_errRet(Tieba_Errcode::ERROR_BAWU_NUM_LIMITED);
        }
        //添加吧务
        $arrParam['req'] = array(
            'user_id'   => intval($addUserId),
            'user_name' => strval($arrInput['user_name']),
            'forum_id'  => intval($arrInput['forum_id']),
            'op_uid'    => intval($arrInput['op_uid']),
            'op_uname'  => strval($arrInput['op_uname']),
            'op_ip'     => 1,
            'from_module'   => 'bawu',   //调用方模块名
            'from_function' => 'Service_Primanage_Primanage',  //调用方方法名
            'forum_name'    => $arrInput['forum_name'],
            'op_user_id'    => intval($arrInput['op_uid']),
            'op_user_name'  => strval($arrInput['op_uname']),
            'need_memo'     => 1,
            'role_name'     => strval($strBawuType),
        );

        Bingo_Timer::start('service_perm_setUserRole');
        $arrOut = Tieba_Service::call('bawu','addUserRole',$arrParam);
        Bingo_Timer::start('service_perm_setUserRole');
		if ($arrOut == false || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('call  bawu:addUserRole fail! output:[ ' . serialize($arrOut) . ']');
        	return self::_errRet($arrOut['errno']);
		}        
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}

	/**
	 * [revokeBawu description]
	 * @param  [type] $arrInput [description]
	 * @return [type]           [description]
	 */
	public static function revokeBawu($arrInput) {
		if (empty($arrInput['user_name']) || empty($arrInput['forum_id']) || empty($arrInput['forum_name']) || empty($arrInput['op_uid']) || empty($arrInput['op_uname']) || empty($arrInput['bawu_type'])) {
			Bingo_Log::warning('invalid input param . input:['. serialize($arrInput) . ']');
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrNames[]  = $arrInput['user_name'];
		$arrUserInfo = Util_User::getUidByNames($arrNames);
		$delUserId   = $arrUserInfo[$arrInput['user_name']][0]['user_id'];
		$strBawuType = $arrInput['bawu_type'];
        if($delUserId == false){
        	Bingo_Log::warning('invalid input param forum_id['.$arrInput['forum_id'].']user_id['.$delUserId.']');
        	return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        //判断私有化吧主的权限
        $arrParams = array(
        	'forum_name' => strval($arrInput['forum_name']),
        	'forum_id'   => intval($arrInput['forum_id']),
        	'user_id'    => intval($arrInput['op_uid']),
            'user_ip'    => intval($arrInput['user_ip']),
            'user_ip6'   => strval($arrInput['user_ip6']),
        );
        $arrOutput = Tieba_Service::call('bawu','getForumBasicInfo',$arrParams);
        if ($arrOutput == false || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('call  bawu:getForumBasicInfo fail! input: ['. serialize($arrParams).'], output:[ ' . serialize($arrOutput) . ']');
        	return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}

		$arrPermInfo = $arrOutput['data'];
		if (1 != $arrPermInfo['is_private_forum']  //0非私有化吧 1私有化吧
			|| 1 >= $arrPermInfo['private_forum_status']  // 1试运营 2正式 3分润
			|| 1 != $arrPermInfo['is_manager']) {
			Bingo_Log::warning('bazhu ho bazhu perm!');
			return self::_errRet(Tieba_Errcode::BAZHU_NO_BAZHU_PERM);
		}

        //判断添加类型
        $arrType = array(
        	self::PRI_MANAGE_ASSIST,
        	self::PRI_CONTENT_ASSIST,
        );
        if(!in_array($strBawuType, $arrType)){
            Bingo_Log::warning("bawu_type invalid [".$strBawuType."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        //罢免吧务
        $arrParam['req'] = array(
            'user_id'   => intval($delUserId),
            'user_name' => strval($arrInput['user_name']),
            'forum_id'  => intval($arrInput['forum_id']),
            'op_uid'    => intval($arrInput['op_uid']),
            'op_uname'  => strval($arrInput['op_uname']),
            'op_ip'     => 1,
            'from_module'   => 'bawu',   //调用方模块名
            'from_function' => 'Service_Primanage_Primanage',  //调用方方法名
            'forum_name'    => $arrInput['forum_name'],
            'op_user_id'    => intval($arrInput['op_uid']),
            'op_user_name'  => strval($arrInput['op_uname']),
            'need_memo'     => 1,
            'role_name'     => strval($strBawuType),
        );

        Bingo_Timer::start('service_perm_delUserRole'); 
        $arrOut = Tieba_Service::call('bawu','cancelUserRole',$arrParam);
        Bingo_Timer::start('service_perm_delUserRole');
        if ($arrOut == false || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('call  bawu:cancelUserRole fail! output:[ ' . serialize($arrOut) . ']');
        	return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}        
		return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
	}

    /**
     * [addResignRecord 吧务辞职记录]
     * @param [type] $[name] [<description>]
     * @param [type] $arrInput [description]
     */
    public static function addResignRecord($arrInput) {
        $strFname    = strval($arrInput['forum_name']); 
        $intUid      = intval($arrInput['user_id']);
        $intUip      = intval($arrInput['user_ip']);
        $strUip6     = strval($arrInput['user_ip6']);
        $intFid      = intval($arrInput['forum_id']);
        $intBawuType = intval($arrInput['bawu_type']);
        $strResignReason = $arrInput['resign_reason'];
        $intStatus       = intval($arrInput['status']); //0未审核 1未通过 2通过
        if (empty($intUid) || empty($intFid) || empty($intBawuType)) {
            Bingo_Log::warning("input param error!arrInput:[".serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        //权限校验
        $has_bazhu_perm = false;
        $has_bawu_perm  = false;
        $arrParams = array(
            'forum_name' => $strFname,
            'forum_id'   => $intFid,
            'user_id'    => $intUid,
            'user_ip'    => $intUip,
            'user_ip6'   => $strUip6,
        );
        //吧主权限
        $arrPerm = Tieba_Service::call('bawu','getForumBasicInfo',$arrParams);
        if ($arrPerm == false || $arrPerm['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call  bawu:getForumBasicInfo fail! input: ['. serialize($arrParams).'], output:[ ' . serialize($arrPerm) . ']');
            $has_bazhu_perm = false;
        }
        $arrPermInfo = $arrPerm['data'];
        //吧主权限
        if (1 == $arrPermInfo['is_private_forum']  //0非私有化吧 1私有化吧
            && 1 == $arrPermInfo['is_manager']) {
            $has_bazhu_perm = true;
        }
        //吧务权限
        if ($arrPermInfo['is_pri_content_assist'] == 1 || $arrPermInfo['is_pri_manage_assist'] == 1) {  //内容吧务
            $has_bawu_perm = true;
        }
               
        if (!$has_bazhu_perm && !$has_bawu_perm) {
            Bingo_Log::warning('user have no perm!');
            return self::_errRet(Tieba_Errcode::ERR_NO_RIGHT);
        }
        if (!empty($strResignReason)) {
            //执行插入sql
            $arrParams = array(
                'user_id'       => $intUid,
                'forum_id'      => $intFid,
                'bawu_type'     => $intBawuType,
                'status'        => $intStatus,
                'resign_reason' => $strResignReason,
                'create_time'   => time(),
                'function'     => 'addResignRecord',
            ); 
        }else {
            //执行插入sql
            $arrParams = array(
                'user_id'       => $intUid,
                'forum_id'      => $intFid,
                'bawu_type'     => $intBawuType,
                'status'        => $intStatus,
                'create_time'   => time(),
                'function'     => 'addResignInfo',
            ); 
        }
        
        $arrRet = Dl_Priforum_Primanage::execSql($arrParams);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning("bawu::addResignRecord insert fail. field:" . serialize($arrParams) . "output:" . serialize($arrRet));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS); 
    }


    /**
     * @param $arrInput
     * @return array
     */
    public static function getOwnerResignRecordById($arrInput) {
        $intId = intval($arrInput['id']);
        $strCondition = " id = $intId ";
        $arrParams = array(
            'function'  => 'getOwnerResignRecordWithCondition',
            'cond' => $strCondition,
            'offset' => 0,
            'ps' => 1,
        );
        $arrRet = Dl_Priforum_Primanage::execSql($arrParams);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning("bawu::getResignRecord select fail. field:" . serialize($arrParams) . "output:" . serialize($arrRet));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrData =$arrRet['results'][0][0];
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrData);
    }



    /**
     * [getResignRecord 获取吧主辞职记录]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getOwnerResignRecordWithCondition($arrInput) {
        $strCondition = "";
        $arrStatus = $arrInput['status'];
        if(!empty($arrStatus) && is_array($arrStatus)) {
            $strStatus = implode(',', $arrStatus);
            $strCondition = $strCondition . " status in (" . $strStatus . ") ";
        }
        else {
            $strCondition = $strCondition . " status in (0,1,2) ";
        }

        $strCondition = $strCondition . " and bawu_type = 3 ";

        $intUserId = intval($arrInput['user_id']);
        if(!empty($intUserId)) {
            $strCondition = $strCondition . " and user_id = $intUserId ";
        }
        else {
            $strCondition = $strCondition . " and user_id > 0 ";
        }

        $intForumId = intval($arrInput['forum_id']);
        if(!empty($intForumId)) {
            $strCondition = $strCondition . " and forum_id = $intForumId ";
        }
        else {
            $strCondition = $strCondition . " and forum_id > 0 ";
        }

        $intStartCreateTime = intval($arrInput['start_create_time']);
        if(!empty($intStartCreateTime)) {
            $strCondition = $strCondition . " and create_time >= $intStartCreateTime ";
        }
        $intEndCreateTime = intval($arrInput['end_create_time']);
        if(!empty($intEndCreateTime)) {
            $strCondition = $strCondition . " and create_time <= $intEndCreateTime ";
        }

        $intStartUpdateTime = intval($arrInput['start_update_time']);
        if(!empty($intStartUpdateTime)) {
            $strCondition = $strCondition . " and update_time >= $intStartUpdateTime ";
        }
        $intEndUpdateTime = intval($arrInput['end_update_time']);
        if(!empty($intEndUpdateTime)) {
            $strCondition = $strCondition . " and update_time <= $intEndUpdateTime ";
        }

        $intPn = intval($arrInput['pn']) > 0 ? intval($arrInput['pn']) : 1;
        $intPs = intval($arrInput['ps']) > 0 ? intval($arrInput['ps']) : 15;
        $intOffset = ($intPn - 1) * $intPs;

        $arrParams = array(
            'function'  => 'getOwnerResignRecordWithConditionCount',
            'cond' => $strCondition
        );
        $arrRet = Dl_Priforum_Primanage::execSql($arrParams);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning("bawu::getResignRecord select fail. field:" . serialize($arrParams) . "output:" . serialize($arrRet));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $intTotalCount = intval($arrRet['results'][0][0]['total_count']);

        $arrParams = array(
            'function'  => 'getOwnerResignRecordWithCondition',
            'cond' => $strCondition,
            'offset' => $intOffset,
            'ps' => $intPs,
        );
        $arrRet = Dl_Priforum_Primanage::execSql($arrParams);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning("bawu::getResignRecord select fail. field:" . serialize($arrParams) . "output:" . serialize($arrRet));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $arrData = (array)$arrRet['results'][0];

        $arrRet = array(
            'rows' => $arrData,
            'count' => $intTotalCount,
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

    /**
     * [getResignRecord 获取辞职记录]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getResignRecord($arrInput) {
        $intUid = intval($arrInput['user_id']);
        $intFid = intval($arrInput['forum_id']);
        $intBawuType = intval($arrInput['bawu_type']);
        $intStatus = 0; //0未审核 1未通过 2通过
        if (empty($intUid) || empty($intFid) || empty($intBawuType)) {
            Bingo_Log::warning("input param error!arrInput:[".serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        //执行插入sql
        $arrParams = array(
            'user_id'   => $intUid,
            'forum_id'  => $intFid,
            'bawu_type' => $intBawuType,
            'status'    => $intStatus,
            'function'  => 'getResignRecord',
        );
        $arrRet = Dl_Priforum_Primanage::execSql($arrParams);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning("bawu::getResignRecord select fail. field:" . serialize($arrParams) . "output:" . serialize($arrRet));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet['results'][0]); 
    }

    /**
     * [updateResignRecord 更新辞职记录]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function updateResignRecord($arrInput) {
        $intId      = intval($arrInput['id']);
        $intStatus  = intval($arrInput['status']);
        $strOpuname = strval($arrInput['op_uname']);
        if (empty($intId)) {
            Bingo_Log::warning("input param error!arrInput:[".serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        //执行插入sql
        $arrParams = array(
            'id'          => $intId,
            'status'      => $intStatus,
            'op_uname'    => $strOpuname,
            'update_time' => time(),
            'function'    => 'updateResignRecord',
        );
        $arrRet = Dl_Priforum_Primanage::execSql($arrParams);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning("bawu::updateResignRecord select fail. field:" . serialize($arrParams) . "output:" . serialize($arrRet));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS); 
    }

    /**
     * [delResignRecord 删除辞职记录]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function delResignRecord($arrInput) {
        $intUid   = intval($arrInput['user_id']);
        $strFname = strval($arrInput['forum_name']);
        $intFid   = intval($arrInput['forum_id']);
        $intBawuType = intval($arrInput['bawu_type']);
        $intStatus   = intval($arrInput['status']);
        if (empty($intUid) || empty($intFid) || empty($intBawuType)) {
            Bingo_Log::warning("input param error!arrInput:[".serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        //权限校验
        $has_perm = true;
        //吧主权限
        $arrParams = array(
            'forum_name' => $strFname,
            'forum_id'   => $intFid,
            'user_id'    => $intUid,
        );
        $arrPerm = Tieba_Service::call('bawu','getForumBasicInfo',$arrParams);
        if ($arrPerm == false || $arrPerm['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call  bawu:getForumBasicInfo fail! input: ['. serialize($arrParams).'], output:[ ' . serialize($arrPerm) . ']');
            $has_perm = false;
        }

        $arrPermInfo = $arrPerm['data'];
        if (1 != $arrPermInfo['is_private_forum']  //0非私有化吧 1私有化吧
//            || 1 >= $arrPermInfo['private_forum_status']  // 1试运营 2正式 3分润
            || 1 != $arrPermInfo['is_manager']) {
            Bingo_Log::warning('bazhu ho bazhu perm!');
            $has_perm = false;
        }

        if (!$has_perm) {
            Bingo_Log::warning('user have no perm!');
            return self::_errRet(Tieba_Errcode::BAZHU_NO_BAZHU_PERM);
        }
        //执行插入sql
        $arrParams = array(
            'user_id'   => $intUid,
            'forum_id'  => $intFid,
            'bawu_type' => $intBawuType,
            'status'    => $intStatus,
            'function'  => 'delResignRecord',
        );
        $arrRet = Dl_Priforum_Primanage::execSql($arrParams);
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning("bawu::delResignRecord select fail. field:" . serialize($arrParams) . "output:" . serialize($arrRet));
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS); 
    }

}