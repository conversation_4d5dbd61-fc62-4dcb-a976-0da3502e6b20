//@description:手机验证相关
//@author: fengzhen
//encode=utf8

struct ifneed_checkphone
{
    uint32_t user_id;         //用户id
    uint32_t forum_id;        //吧id
    string call_from;		  //来源
};

struct ifneed_checkphone_out
{
    uint32_t if_check;		  //0-不需要手机验证,1-只需要手机验证，可正常操作,2-强制手机验证，操作受限
    bool if_set_remind;		  //是否需要设置首次看到浮层的时间
};

service checkphone
{
	 /**
     * @brief :  判断用户是否需要进行手机验证以及操作是否受限
     * @param [in]  input    : ifneed_checkphone    :   输入
     * @param [out] out    : ifneed_checkphone_out    :   返回
     **/
     void ifNeedCheckPhone(ifneed_checkphone input,out ifneed_checkphone_out out);
    
};