<?php
/**
 * <AUTHOR>
 * @date 2017-09-19
 */
class Service_Notice_Notice{

    const REDIS_NAME   = 'bawuphone';
    const REDIS_PREFIX = 'notice_';
    const RETRY_TIMES  = 3; 

    const RECORD_WAITING = 0;
    const RECORD_SENDING = 1;
    const RECORD_SEND = 2;
    const RECORD_READ = 3;

    const JOB_CREATE  = 0;
    const JOB_DOING  = 1;
    const JOB_FAILED  = 2;
    const JOB_DONE  = 3;

    const SEND_PC = 1;
    const SEND_MESSAGE = 2;

    const MANAGER = 1;
    const ASSIST = 2 ;
    const VOICEADMIN = 3; 
    const PICADMIN = 4; 
    const PUBLICATION = 5; 
    const PUBLICATION_EDITOR = 6; 
    const VIDEOADMIN = 20;
    const FOURTH_MANAGER  = 21;
    //const PROFESSION_MANAGER = 21;

    private static $arrBawu2Num = array(
            'assist' => 2,
            'manager' => 1,
            'publication' => 5,
            'videoadmin' => 3,
            'picadmin' => 4,
            'publication_editor' => 6,
            'voiceadmin' => 20,
            'fourth_manager' => 21,
            );
    private static $arrNum2Bawu = array(   
            2  => '助理',
            1  => '大吧主',
            5  => '吧刊主编',
            21 => '第四吧主',
            3  => '视频主编',
            4  => '图片主编',
            6  => '吧刊小编',
            20 => '音频主编',
            );


    /**
     * 向FE提供的已读反馈service 将REDiS队列中的Record踢出，更新DB状态
     * @param [type] $arrInput [description]
     * @return
     */
    public static function updateRecordAndGetContent($arrInput){
        if( !isset($arrInput['user_id'])  ||  intval($arrInput['user_id']) <=0 || 
                !isset($arrInput['record_id'])|| intval($arrInput['record_id'])<=0 ){
            Bingo_Log::warning("param error input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUid = intval($arrInput['user_id']);
        $intRecordId =  intval($arrInput['record_id']);
        $input = array(
                'record_id' => $intRecordId,
                'status'    => self::RECORD_READ,
                'update_time' => time(),
                );
        $output = Dl_Supernotice_Record::update($input);
        if($output['errno'] !== Tieba_Errcode::ERR_SUCCESS){//Db更新失败,返回空，为了使本次通知结束；但:当进入其他frs页或者重新进入该frs页，仍然会弹出此条已读消息
            Bingo_Log::warning("db notice_record update failed. input:[". serialize($input) ."] output:[" . serialize($output) ."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array());
        }
        $key   = self::REDIS_PREFIX . $intUid;
        $input = array(
                'key' => $key,
                );
        $output = Util_Redis::redisQuery( self::REDIS_NAME, 'LPOP', $input , self::RETRY_TIMES );        
        if($output['err_no'] !== Tieba_Errcode::ERR_SUCCESS || $output['ret'][$key] == null ){
            //弹出已经读过的首节点失败,返回空，为了使本次通知结束，否则吧务会无限收到同一条消息；但:当进入其他frs页或者重新进入该frs页，仍然会弹出此条已读消息
            Bingo_Log::warning("redis lpop error. input:[". serialize($input) ."] output:[" . serialize($output) ."]");
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL,array());
        }

        $intListValue = intval($output['ret'][$key]);
        $arrInsert = array();
        while($intListValue!=$intRecordId){
            $arrInsert[]=$intListValue;
            $output = Util_Redis::redisQuery( self::REDIS_NAME, 'LPOP', $input , self::RETRY_TIMES );
            if($output['err_no'] !== Tieba_Errcode::ERR_SUCCESS){
                //弹出已经读过的首节点失败,返回空，为了使本次通知结束，否则吧务会无限收到同一条消息；但:当进入其他frs页或者重新进入该frs页，仍然会弹出此条已读消息
                Bingo_Log::warning("redis lpop error. input:[". serialize($input) ."] output:[" . serialize($output) ."]");
                return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL,array());
            }
            if($output['ret'][$key] == null ){
                break;
            }
            $intListValue = intval($output['ret'][$key]);
        }
        if(!empty($arrInsert)){
            $input = array(
                    'key' => $key,
                    'value' => $arrInsert, 
                    );
            $output = Util_Redis::redisQuery( self::REDIS_NAME, 'RPUSH', $input , self::RETRY_TIMES );
            if($output['err_no'] !== Tieba_Errcode::ERR_SUCCESS){
                Bingo_Log::warning("redis rpush error. input:[". serialize($input) ."] output:[" . serialize($output) ."]");
                return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL,array());
            }
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     *内部使用 根据uid获取所有需读消息的function
     * @param [type] $arrInput [description]
     * @return
     */    
    private static function getAllContentByUserId($arrInput){
        if( !isset($arrInput['user_id'])  || intval($arrInput['user_id'])<=0 ){
            Bingo_Log::warning("param error input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUid = intval($arrInput['user_id']);
        $key   = self::REDIS_PREFIX . $intUid;
        $input = array(
                'key'   => $key,
                'start' => 0,
                'stop' => 0,//一次取一个先
                );
        $output = Util_Redis::redisQuery( self::REDIS_NAME, 'LRANGE', $input , self::RETRY_TIMES );
        if($output['err_no'] !== Tieba_Errcode::ERR_SUCCESS){//获取首节点失败,返回空，表示没有新消息。
            Bingo_Log::warning("redis LRANGE error. input:[". serialize($input) ."] output:[" . serialize($output) ."]");
            return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL,array());
        }
        $res = array();
        if(!empty($output['ret'][$key])){//单链表仍有值的话
            $arrNextRecordId = $output['ret'][$key];
            foreach($arrNextRecordId as $intNextRecordId){
                $input = array(
                        'record_id' => $intNextRecordId,
                        );
                $outputRecord = Dl_Supernotice_Record::select($input);
                if($outputRecord['errno'] !== Tieba_Errcode::ERR_SUCCESS){//有record_id但是template_id 查询失败,仍然返回空,表示没有新消息
                    Bingo_Log::warning("db notice_Record select failed. input:[". serialize($input) ."] output:[" . serialize($outputRecord) ."]");
                    return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array());
                }
                if(!empty($outputRecord['data'])){
                    $arrRecordData = $outputRecord['data'][0];
                    $intTemplateId = intval($arrRecordData['template_id']);
                    $input  = array(
                            'template_id' => $intTemplateId,
                            );
                    $outputTemplate = Dl_Supernotice_Template::select($input);
                    //Bingo_Log::warning(var_export($outputTemplate,1));
                    if($outputTemplate['errno'] !== Tieba_Errcode::ERR_SUCCESS){//有template_id但是template_content查询失败,返回空
                        Bingo_Log::warning("db notice_template select failed. input:[". serialize($input) ."] output:[" . serialize($outputTemplate) ."]");
                        return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array());
                    }
                    $strTemplateContent = Bingo_Encode::convert ($outputTemplate['data'][0]['template_content'],'GBK','UTF-8');
                  //  $strTemplateContent = $outputTemplate['data'][0]['template_content'];
                   
                 $res[$outputRecord['data'][0]['record_id']] = array('template_content'=> $strTemplateContent , 'record' => $arrRecordData);
                    
                }
            }
        }
        return self::_succRet($res);
    }


    /**
     * 内部function 由template uid fid user-role制造出发送内容
     * @param [type] $arrInput [description]
     * @return
     */
    private static function combineContent($arrInput){
        if( empty($arrInput['template']) || 
                !isset($arrInput['uid']) || intval($arrInput['uid'])<=0  ||
                !isset($arrInput['forumId'])  || intval($arrInput['forumId'])<=0 ||
                !isset($arrInput['userRole']) || intval($arrInput['userRole'])<=0 ){
            Bingo_Log::warning("param error input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intUid = intval($arrInput['uid']);
        $intForumId  = intval($arrInput['forumId']);
        $intUserRole  = intval($arrInput['userRole']);
        $input = array(
                "forum_id" => array( 0 => $intForumId )
                );
        $res   = Tieba_Service::call('forum', 'getFnameByFid', $input, null, null, 'post', 'php', 'gbk');
        if($res['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("call service forum:getFnameByFid  failed. input:[". serialize($input) ."] output:[" . serialize($res) ."]");
        }
       // Bingo_Log::warning(var_export($res,true));
        $fname = $res['forum_name'][$intForumId]['forum_name'];
        $input = array( 
                "user_id" => array( 0 => $intUid )
                );
        $res   = Tieba_Service::call('user', 'getUnameByUids', $input, null, null, 'post', 'php', 'gbk');
        if($res['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning("call service user:getUnameByUids  failed. input:[". serialize($input) ."] output:[" . serialize($res) ."]");
        }
        $uname = $res['output']['unames'][0]['user_name'];
        $urole = Bingo_Encode::convert(self::$arrNum2Bawu[$intUserRole],'GBK','UTF-8');
        $search  =  array('%f' ,'%u' ,'%r' );
        $replace =  array($fname,$uname,$urole);
        $arrRet = str_replace( $search ,$replace ,$arrInput['template'] );

        return self::_succRet($arrRet);
    }


    /**
     * [description]  供FRS页扩展调用service,拿到一个uid该读的消息内容
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getNextContent($arrInput){
        if(!isset($arrInput['user_id'])|| intval($arrInput['user_id'])<=0){
            Bingo_Log::warning("param error input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $resContent = self::getAllContentByUserId($arrInput);
        if($resContent['errno'] !== Tieba_Errcode::ERR_SUCCESS ){
            return  self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL,array());
        }
        $data = array();
        foreach($resContent['data'] as $record_id => $res){
            $input = array(
                    'template'  => $res['template_content'],
                    'forumId'   => $res['record']['forum_id'],
                    'uid'       => $res['record']['user_id'],
                    'userRole'  => $res['record']['forum_role'],
                    );
            // Bingo_Log::warning(var_export($res,true));
            $nextContent = self::combineContent($input);
            if($nextContent['errno'] !== Tieba_Errcode::ERR_SUCCESS ){
                return  self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL,array());
            }
            //Bingo_Log::warning($nextContent);
            $data[] =array('record_id' =>$res['record']['record_id'], 'content' =>$nextContent['data']);
            $input = array(
                    'record_id' => $res['record']['record_id'],
                    'status'    => self::RECORD_SEND,
                    'update_time' => time(),
                    );
            $output = Dl_Supernotice_Record::update($input);
            if($output['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                //Db更新失败,返回空，为了使本次通知结束；但:当进入其他frs页或者重新进入该frs页，仍然会弹出此条已读消息
                Bingo_Log::warning("db notice_Record update failed. input:[". serialize($input) ."] output:[" . serialize($output) ."]");
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array());
            }
        }
        return self::_succRet($data);
    }


    /**
     * [description] 由模板id拿到模板全内容 service
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getTemplateByTemplateId($arrInput){
        if(!isset($arrInput['template_id']) || intval($arrInput['template_id'])<=0){
            Bingo_Log::warning("param error input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intTemplateId = intval($arrInput['template_id']);
        $input = array(
                'template_id' => $intTemplateId,
                );
        $output = Dl_Supernotice_Template::select($input);
        if($output['errno'] !== Tieba_Errcode::ERR_SUCCESS){//content查询失败,返回空
            Bingo_Log::warning("db notice_template select failed.input:". serialize($input) ."  output:[" . serialize($output) ."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array());
        }
        foreach($output['data'] as &$value){
            $value['template_content']=  Bingo_Encode::convert ($value['template_content'],'GBK','UTF-8');
            $value['template_name']=  Bingo_Encode::convert ($value['template_name'],'GBK','UTF-8');
            $value['create_op_uname']=  Bingo_Encode::convert ($value['create_op_uname'],'GBK','UTF-8');
            $value['update_op_uname']=  Bingo_Encode::convert ($value['update_op_uname'],'GBK','UTF-8');
        } 
        return self::_succRet($output['data']);
    }


    /**
     * [description] 由模板name拿到模板全内容 service
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getTemplateByTemplateName($arrInput){
        if(empty($arrInput['template_name'])){
            Bingo_Log::warning("param error input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $strTemplateName = $arrInput['template_name'];
        $input = array(
                'template_name' => $strTemplateName,
                );
        $output = Dl_Supernotice_Template::select($input);
        if($output['errno'] !== Tieba_Errcode::ERR_SUCCESS){//content查询失败,返回空
            Bingo_Log::warning("db notice_template select failed.input:". serialize($input) ."  output:[" . serialize($output) ."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array());
        }
        //   Bingo_Log::warning(var_export($output,1));
        foreach($output['data'] as &$value){
            $value['template_content']=  Bingo_Encode::convert ($value['template_content'],'GBK','UTF-8');
            $value['template_name']=  Bingo_Encode::convert ($value['template_name'],'GBK','UTF-8');
            $value['create_op_uname']=  Bingo_Encode::convert ($value['create_op_uname'],'GBK','UTF-8');
            $value['update_op_uname']=  Bingo_Encode::convert ($value['update_op_uname'],'GBK','UTF-8');
        } 
        return self::_succRet($output['data']);
    }


    /**
     * [description] 新建模板service
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function insertNewTemplate($arrInput){
        if( empty($arrInput['template_name']) || empty($arrInput['template_content']) || 
                empty($arrInput['create_op_uid']) || empty($arrInput['create_op_uname']) ){
            Bingo_Log::warning("param error input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }
        $strTemplateName =Bingo_Encode::convert( $arrInput['template_name'],'UTF-8','GBK');
        $intCreateUid = intval($arrInput['create_op_uid']);
        $strCreateOpName = Bingo_Encode::convert($arrInput['create_op_uname'],'UTF-8','GBK');
        $strTemplateContent= Bingo_Encode::convert($arrInput['template_content'],'UTF-8','GBK');
        $input = array();
        $input[] = array(
                'template_name' => $strTemplateName,
                'create_op_uid' => $intCreateUid,
                'create_op_uname' => $strCreateOpName,
                'template_content' => $strTemplateContent,
                'create_time' =>  time(),
                );
       // Bingo_Log::warning(var_export($input,1));
        $output = Dl_Supernotice_Template::insert($input);
        if($output['errno'] !== Tieba_Errcode::ERR_SUCCESS){//content查询失败,返回空
            Bingo_Log::warning("db notice_template insert failed.input:". serialize($input) ." output:[" . serialize($output) ."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array());
        }
        return self::_succRet($output['data']);
    }


    /**
     * [description] 修改模板service
     * @param  [type] $arrInput [description]
     * @return [type]           [description] 
     */
    public static function modifyTemplate($arrInput){
        if( empty($arrInput['template_id']) || empty($arrInput['template_name']) || empty($arrInput['template_content']) ||
                empty($arrInput['update_op_uid']) || empty($arrInput['update_op_uname']) ){
            Bingo_Log::warning("param error input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }
        $strTemplateName = Bingo_Encode::convert( $arrInput['template_name'],'UTF-8','GBK'); 
        $intUpdateOpUid = intval($arrInput['update_op_uid']);
        $intTemplateId = intval($arrInput['template_id']);
        $strUpdataOpName = Bingo_Encode::convert( $arrInput['update_op_uname'],'UTF-8','GBK');
        $strTemplateContent= Bingo_Encode::convert( $arrInput['template_content'],'UTF-8','GBK');
        $input = array(
                'template_name' => $strTemplateName,
                'template_id' => $intTemplateId,
                'update_op_uid' => $intUpdateOpUid,
                'update_op_uname' => $strUpdataOpName,
                'template_content' => $strTemplateContent,
                'update_time' =>  time(),
                );

        $output = Dl_Supernotice_Template::update($input);
        if($output['errno'] !== Tieba_Errcode::ERR_SUCCESS){//content查询失败,返回空
            Bingo_Log::warning("db notice_template update failed.input:". serialize($input) ." output:[" . serialize($output) ."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array());
        }
        return self::_succRet($output['data']);
    }


    /**
     * [description]删除模板service
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function deleteTemplate($arrInput){
        if( empty($arrInput['template_id']) ){
            Bingo_Log::warning("param error input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }
        $intTemplateId = intval($arrInput['template_id']);
        $input = array(
                'template_id' => $intTemplateId,
                );
        $output = Dl_Supernotice_Template::_delete($input);
        if($output['errno'] !== Tieba_Errcode::ERR_SUCCESS){//content查询失败,返回空
            Bingo_Log::warning("db notice_template delete failed.input:". serialize($input) ." output:[" . serialize($output) ."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array());
        }
        return self::_succRet($output['data']);
    }


    /**  
     * [description] 将发至PC端的推送写入Job
     * rules为json格式，包含fid、uid、user-role
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function createJobMission($arrInput){
        if( empty($arrInput['rules']) || empty($arrInput['template_id']) || empty($arrInput['template_name']) ||
                empty($arrInput['create_uid']) || empty($arrInput['create_uname']) ){
            Bingo_Log::warning("param error input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR,array());
        }
        $strRules = $arrInput['rules'];
        $intTemplateId = intval($arrInput['template_id']);
       // $strTemplateName =  $arrInput['template_name'];
        $strTemplateName = Bingo_Encode::convert( $arrInput['template_name'],'UTF-8','GBK');
     
      //  $strTemplateName = Bingo_Encode::convert( $arrInput['template_name'],'GBK','UTF-8');
        $intCreateUid = intval($arrInput['create_uid']);
        $strCreateUname = Bingo_Encode::convert( $arrInput['create_uname'],'UTF-8','GBK');
      //  $strCreateUname = $arrInput['create_uname'];
        
      
        $input[] = array(
                'template_id' => $intTemplateId,
                'template_name' => $strTemplateName,
                'rules' => $strRules,
                'create_uid' => $intCreateUid,
                'create_uname' =>  $strCreateUname,
                'create_time' => time(),
                'status' => self::JOB_CREATE,
                );
        $output = Dl_Supernotice_Job::insert($input);
        if($output['errno'] !== Tieba_Errcode::ERR_SUCCESS){//content查询失败,返回空
            Bingo_Log::warning("db notice_job insert failed.input:". serialize($input) ." output:[" . serialize($output) ."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL,array());
        }
        return self::_succRet($output['data']);
    }


    /** 
     *[description]根据筛选条件筛选Record service
     * @param [type] $arrInput [description]
     * @return
     */
    public static function listRecordByCondition($arrInput){
        if(  !isset($arrInput['pn']) || $arrInput['pn']<=0
                || !isset($arrInput['rn']) || $arrInput['rn']<=0 ){
            Bingo_Log::warning("param error input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intPn = (int)$arrInput['pn'];
        $intRn = (int)$arrInput['rn'];

        $arrInput['orderby'] = array(
                'field' => 'record_id',
                'sort'  => 'DESC',
                );

        $arrOutput = Dl_Supernotice_Record::select($arrInput);
        if( false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno'] ){
            Bingo_Log::warning(sprintf("call Dl_Supernotice_Record::select failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
     
        foreach($arrOutput['data'] as &$value){
                    //$value['template_content']= htmlspecialchars($value['template_content']);
                  $value['op_uname']=  Bingo_Encode::convert ($value['op_uname'],'GBK','UTF-8');
    
              }
       
        $arrList = $arrOutput['data'];    
        $arrOutput = Dl_Supernotice_Record::selectCnt($arrInput);
        if( false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno'] ){
            Bingo_Log::warning(sprintf("call Dl_Supernotice_Record::selectCnt failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $intCnt = $arrOutput['data'][0]['cnt'];
        $arrRet = array(
                'list' => $arrList,
                'page' => array(
                    'total_count' => $intCnt,
                    'current_pn'  => $intPn,
                    'total_pn'    => ceil($intCnt / $intRn),
                    ),
                );
        return self::_succRet($arrRet);
    }


    /**
     * [description] 根据筛选条件列举template service
     * @param [type] $arrInput [description]
     * @return
     */
    public static function listTemplateByCondition($arrInput){
        if(  !isset($arrInput['pn'])|| $arrInput['pn']<=0
                || !isset($arrInput['rn'])|| $arrInput['rn']<=0 ){
            Bingo_Log::warning("param error input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intPn = (int)$arrInput['pn'];
        $intRn = (int)$arrInput['rn'];

        $arrInput['orderby'] = array(
                'field' => 'template_id',
                );

        $arrOutput = Dl_Supernotice_Template::select($arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call Dl_Supernotice_Template::select failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        foreach($arrOutput['data'] as &$value){
               //$value['template_content']= htmlspecialchars($value['template_content']);
             $value['template_content']=  Bingo_Encode::convert ($value['template_content'],'GBK','UTF-8');
             $value['template_name']=  Bingo_Encode::convert ($value['template_name'],'GBK','UTF-8');
             $value['create_op_uname']=  Bingo_Encode::convert ($value['create_op_uname'],'GBK','UTF-8');
             $value['update_op_uname']=  Bingo_Encode::convert ($value['update_op_uname'],'GBK','UTF-8');
       
         }
        $arrList = $arrOutput['data'];
      //  Bingo_Log::warning(var_export($arrList,1));
        $arrOutput = Dl_Supernotice_Template::selectCnt($arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call Dl_Supernotice_Template::selectCnt failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $intCnt = $arrOutput['data'][0]['cnt'];
        $arrRet = array(
                'list' => $arrList,
                'page' => array(
                    'total_count' => $intCnt,
                    'current_pn'  => $intPn,
                    'total_pn'    => ceil($intCnt / $intRn),
                    ),
                );
        //Bingo_Log::warning(print_r($arrRet, 1));
        return self::_succRet($arrRet);
    }


    /**
     *[description] 根据筛选条件列举Job service
     * @param [type] $arrInput [description]
     * @return
     */
    public static function listJobByCondition($arrInput){
        if(  !isset($arrInput['pn'])|| $arrInput['pn']<=0
                || !isset($arrInput['rn'])|| $arrInput['rn']<=0 ){
            Bingo_Log::warning("param error input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intPn = (int)$arrInput['pn'];
        $intRn = (int)$arrInput['rn'];

        $arrInput['orderby'] = array(
                'field' => 'job_id',
                'sort' => 'DESC',    
                );

        $arrOutput = Dl_Supernotice_Job::select($arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call Dl_Supernotice_Job::select failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        foreach($arrOutput['data'] as &$value){
                   //$value['template_content']= htmlspecialchars($value['template_content']);
                  $value['create_uname']=  Bingo_Encode::convert ($value['create_uname'],'GBK','UTF-8');
                 $value['template_name']=  Bingo_Encode::convert ($value['template_name'],'GBK','UTF-8');
   
              }
        $arrList = $arrOutput['data'];
        $arrOutput = Dl_Supernotice_Job::selectCnt($arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call Dl_Supernotice_Job::selectCnt failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $intCnt = $arrOutput['data'][0]['cnt'];
        $arrRet = array(
                'list' => $arrList,
                'page' => array(
                    'total_count' => $intCnt,
                    'current_pn'  => $intPn,
                    'total_pn'    => ceil($intCnt / $intRn),
                    ),
                );
        return self::_succRet($arrRet);
    }


    /**
     *[description]把单条为开始or执行失败的任务放入Record
     * @param [type] $arrInput [description]
     * @return  改进:个别执行失败记录下来，写入json
     */
    public static function putJobIntoRecord(){
        $arrInput = array(
                'status' => array( self::JOB_CREATE ,self::JOB_FAILED),
                'pn' => 1,
                'rn' => 1//一条Job一条的put
                );
        $arrJob = Dl_Supernotice_Job::select($arrInput);
        if(false === $arrJob || Tieba_Errcode::ERR_SUCCESS !== $arrJob['errno']){
            Bingo_Log::warning(sprintf("call Dl_notice_Job::select failed input[%s] output[%s]", serialize($arrInput), serialize($arrJob)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        if(!empty($arrJob)){
            $intJobId = intval($arrJob['data'][0]['job_id']);
            $arrInput = array(
                    'job_id' => $intJobId,
                    'status' => self::JOB_DOING,
                    'update_time' => time(),
                    );
            $boolDone = true;
            $arrRes = Dl_Supernotice_Job::update($arrInput);
            if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
                Bingo_Log::warning(sprintf("call Dl_Supernotice_Job::update failed input[%s] output[%s]", serialize($arrInput), serialize($arrRes)));
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
            $arrFail = array();

            $arrRule = (array)json_decode($arrJob['data'][0]['rules']);
            // $strFormat = $arrRule['format'];
            // $strOption = explode(':',$strFormat);
            $arrInsert = array();
            foreach($arrRule['fur'] as $oneRule){
                $oneRule = (array)$oneRule;
                $intFid = intval($oneRule['fid']);
                $arrTmp = array(
                        'forum_id'   => $intFid,
                        'template_id'=> $arrJob['data'][0]['template_id'],
                        'op_uid'     => $arrJob['data'][0]['create_uid'],
                        'op_uname'   => $arrJob['data'][0]['create_uname'],
                        'status'     => self::RECORD_WAITING,
                        'send_method'=> $arrRule['send_method'],
                        'send_time'  => time(),
                        'update_time'=> 0,
                        );
                if( !empty($oneRule['uid']) && !empty($oneRule['role']) ){
                    $arrTmp['forum_role'] = $oneRule['role'];
                    $arrTmp['user_id']    = $oneRule['uid'];
                    $arrInsert[]= $arrTmp;
                }
                else{
                    $input = array(
                            "forum_id" => $intFid //吧id
                            );
                    $res   = Tieba_Service::call('perm', 'getBawuList', $input, null, null, 'post', 'php', 'utf-8');
                    if(false === $res || Tieba_Errcode::ERR_SUCCESS !== $res['errno']){
                        Bingo_Log::warning(sprintf("call service perm::getBawuList failed input[%s] output[%s]", serialize($input), serialize($res)));

                        $arrInput = array(
                                'job_id' => $intJobId,
                                'status' => self::JOB_FAILED,
                                'update_time' => time(),
                                );
                        $arrRes = Dl_Supernotice_Job::update($arrInput);
                        if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
                            Bingo_Log::warning(sprintf("call Dl_Supernotice_Job::update failed input[%s] output[%s]", serialize($arrInput), serialize($arrRes)));
                            // return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                        }
                        else{
                            $boolDone =false;
                            $arrFail[] = array('uid'=> $oneRule['uid'], 'fid'=> $oneRule['fid'], 'role'=> $oneRule['role'] );
                        }
                    }
                    else{
                        $arrflip = array_flip($arrBawu2Num);
                        if(empty($oneRule['uid'])&&!empty($oneRule['role'])) {
                            $arrUid = $res['output'][$arrflip[$oneRule['role']]];
                            foreach($arrUid as $value){
                                $arrTmp['forum_role'] = $oneRule['role'];
                                $arrTmp['user_id']    =  $value['user']['user_id'];
                                $arrInsert[]= $arrTmp;
                            }
                        }
                        else if(!empty($oneRule['uid'])&&empty($oneRule['role'])){
                            $arrOut = $res['output'];    
                            foreach($arrOut as $role => $value){
                                foreach($value as $v){
                                    if($v['user']['user_id'] == $oneRule['uid']){
                                        $arrTmp['forum_role'] = self::$arrBawu2Num[$role];
                                        $arrTmp['user_id']    = $oneRule['uid'];
                                        $arrInsert[]= $arrTmp;
                                    }
                                }
                            }
                        }
                        else{
                            $arrOut = $res['output'];    
                            foreach($arrOut as $role => $value){
                                foreach($value as $v){
                                    $arrTmp['forum_role'] = self::$arrBawu2Num[$role];
                                    $arrTmp['user_id']    = $v['user']['user_id'];
                                    $arrInsert[]= $arrTmp;
                                }
                            }
                        }
                    }
                }
            }
            for($i=0;$i<count($arrInsert);$i+=30){
                $arrSlice = array_slice($arrInsert,$i,30);
                $arrRes = Dl_Supernotice_Record::insert($arrSlice);
                if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
                    Bingo_Log::warning(sprintf("call Dl_notice_record::insert failed input[%s] output[%s]", serialize($arrSlice), serialize($arrRes)));
                    $arrInput = array(
                            'job_id' => $intJobId,
                            'status' => self::JOB_FAILED,
                            'update_time' => time(),
                            );
                    $arrRes = Dl_Supernotice_Job::update($arrInput);
                    if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
                        Bingo_Log::warning(sprintf("call Dl_Supernotice_Job::update failed input[%s] output[%s]", serialize($arrInput), serialize($arrRes)));
                    }
                    else{
                        $boolDone =false;
                        foreach ($arrSlice as $key => $value) {
                            $arrFail[] = array('uid'=> $value['user_id'], 'fid'=> $value['forum_id'], 'role'=> $value['forum_role'] );  
                        }
                    }
                }
            }
        }
        if($boolDone ==true){
            $arrInput = array(
                    'job_id' => $intJobId,
                    'status' => self::JOB_DONE,
                    'update_time' => time(),
                    );
            $arrRes = Dl_Supernotice_Job::update($arrInput);
            if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
                Bingo_Log::warning(sprintf("call Dl_Supernotice_Job::update failed input[%s] output[%s]", serialize($arrInput), serialize($arrRes)));
                // return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
        }
        else{
            $jsonRule =  array('send_method' =>$arrRule['send_method'] , 'format'=> $arrRule['format'] ,'fur' => $arrFail);
            $arrInput = array(
                    'job_id' => $intJobId,
                    'rules' => json_encode($jsonRule),
                    'update_time' => time(),
                    );
            $arrRes = Dl_Supernotice_Job::update($arrInput);
            if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
                Bingo_Log::warning(sprintf("call Dl_Supernotice_Job::update failed input[%s] output[%s]", serialize($arrInput), serialize($arrRes)));
                // return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }

        }

        return self::_succRet('success');
    }


    /**
     *[description]批量把待发送的record放入Redis
     * @param [type] $arrInput [description]
     * @return 
     */
    public static function putRecordIntoRedis($arrTmp=array()){
        $arrInput = array(
                'status' => self::RECORD_WAITING,
                'pn' => 1,
                'rn' => 50//一次处理50条
                );
        $arrRecord = Dl_Supernotice_Record::select($arrInput);
        if(false === $arrRecord || Tieba_Errcode::ERR_SUCCESS !== $arrRecord['errno']){
            Bingo_Log::warning(sprintf("call Dl_notice_record::select failed input[%s] output[%s]", serialize($arrInput), serialize($arrRecord)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        foreach($arrRecord['data'] as $arrOneRecord ){
            $key   = self::REDIS_PREFIX . $arrOneRecord['user_id'];
            $input = array(
                    'key'   => $key,
                    'value' => array(
                        $arrOneRecord['record_id'],
                        ),
                    );
            $output = Util_Redis::redisQuery( self::REDIS_NAME, 'RPUSH', $input , self::RETRY_TIMES );
            if($output['err_no'] !== Tieba_Errcode::ERR_SUCCESS){//插入首节点失败,返回空，表示没有新消息。
                Bingo_Log::warning("redis push node error. input:[". serialize($input) ."] output:[" . serialize($output) ."]");
                //return self::_errRet(Tieba_Errcode::ERR_REDIS_CALL_FAIL);
            }
            else{
                $arrInput = array(
                        'record_id' => $arrOneRecord['record_id'],
                        'status' => self::RECORD_SENDING,
                        'update_time' => time(),
                        );
                $arrRes = Dl_Supernotice_Record::update($arrInput);
                if(false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']){
                    Bingo_Log::warning(sprintf("call Dl_notice_record::update failed input[%s] output[%s]", serialize($arrInput), serialize($arrRes)));
                    return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
                }
            }
        }
        return self::_succRet('success');
    }

    /**
     * [_succRet description]
     * @param  [type] $ret [description]
     * @return [type]      [description]
     */
    protected static function _succRet($ret = null){
        $error = Tieba_Errcode::ERR_SUCCESS;
        return array(
                'errno'  => $error,
                'errmsg' => Tieba_Error::getErrmsg($error),
                'data'   => $ret,
                );
    }

    /**
     * [_errRet description]
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    protected static function _errRet($errno, $data = null){
        return array(
                'errno'  => $errno,
                'errmsg' => Tieba_Error::getErrmsg($errno),
                'data'   => $data,
                );
    }

}
