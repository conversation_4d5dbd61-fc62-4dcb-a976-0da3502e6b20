<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2013:04:09 11:50:25
 * @version 
 * @structs & methods(copied from idl.)
*/

//*************************************************************************************
// IDL CONTENT BEGIN: 
//*************************************************************************************
// 	struct application
// 	{
// 		uint64_t forum_id;
// 		uint64_t applicant_id;
// 		string avatar;
// 		string slogan;
// 		uint32_t application_time;
// 		string history[];
// 	};
// 	
// 	
// 	service card
// 	{
// 		commit void apply
// 		(
// 			uint64_t forum_id,
// 			string avatar,
// 			string slogan,
// 			uint64_t applicant_id
// 		);
// 	
// 		commit void examine
// 		(
// 			uint64_t id,
//			uint64_t examiner_id,
//			uint32_t status,
//			string reason = optional(),
// 			out bool result
// 		);
// 	
// 		void getApplicationStatus
// 		(
// 			uint64_t forum_id,
// 			out uint32_t status
// 		);
// 	
// 		void getApplication
// 		(
// 			uint32_t status = optional(),
// 			uint64_t forum_id = optional(),
// 			uint64_t applicant_id = optional(),
// 			uint64_t limit = optional(),
// 			uint64_t offset = optional(),
// 			uint64_t start = optional(),
// 			uint64_t end = optional(),
// 			string time_type = optional(),
//			bool is_count = optional(),
// 			out application application_list[]
// 		);	
//
// 		void getHistory
// 		(
// 			uint32_t status = optional(),
// 			uint64_t forum_id = optional(),
// 			uint64_t applicant_id = optional(),
// 			uint64_t limit = optional(),
// 			uint64_t offset = optional(),
// 			uint64_t start = optional(),
// 			uint64_t end = optional(),
// 			string time_type = optional(),
//			bool is_count = optional(),
// 			out application application_list[]
// 		);	
// 	};
// 	
//*************************************************************************************
// IDL CONTENT END. 
//*************************************************************************************


class Service_Card_Card{

protected static $_conf = null;
protected static $_db = null;
protected static $_use_split_db = false;
const DB_RAL_SERVICE_NAME = "db_forum_consume";
const DB_TABLE_NAME = "forum_card_table";
const EXAMINING = 0;
const SUCCESS = 1;
const FAILURE = 2;
const SCRIPT_PASS = 1;
const FORUM_CARD_UPDATE_PERIODS = 1;
/**
 * @brief get mysql obj.
 * @return: obj of Bd_DB, or null if connect fail.

**/		
private static function _getDB(){
    if(self::$_db){
        return self::$_db ;
    }
    self::$_db = new Bd_DB();
    if(self::$_db == null){
        Bingo_Log::warning("new bd_db fail.");
        return null;
    }
    if(self::$_use_split_db){
        $splitDBConfPath = ROOT_PATH."/conf/db/";
        $splitDBConfFile = "db_service_card.conf";
        $r = self::$_db -> enableSplitDB(self::DB_RAL_SERVICE_NAME, $splitDBConfPath, $splitDBConfFile);
        if(!$r){
            Bingo_Log::warning("enable splitdb fail.");
            self::$_db = null;
            return null;
        }
        return self::$_db;
    }else{
    	Bingo_Timer::start('dbinit');
        $r = self::$_db->ralConnect(self::DB_RAL_SERVICE_NAME);
        Bingo_Timer::end('dbinit');
        if(!$r){
            Bingo_Log::warning("bd db ral connect fail.".json_encode($r));
            self::$_db = null;
            return null;
       }
        if ($_ENV['HHVM'] == 1) {
            self::$_db->charset('gbk');
        }
       return self::$_db;

    }
    return null;
}

	
/**
 * @brief init
 * @return: true if success. false if fail.

**/		
private static function _init(){
	
	//add init code here. init will be called at every public function beginning.
	//not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
	//init should be recalled for many times.
	
	if(self::$_conf == null){	
		self::$_conf = Bd_Conf::getConf("/app/bawu/service_card");
		if(self::$_conf == false){
			Bingo_Log::warning("init get conf fail.");
			return false;
		}
		
	}
	return true; 
}


private static function _errRet($errno, $msg=''){
    return array(
        'errno' => $errno,
        'errmsg' => $msg != '' ? $msg : Tieba_Error::getErrmsg($errno),
    );
}

public static function preCall($arrInput){
    // pre-call hook
}

public static function postCall($arrInput){
    // post-call hook
}

public static function _genSQLParam($arrInput){
	//��������
	$forum_id = isset($arrInput['forum_id']) ? Intval($arrInput['forum_id']) : 0;
	$applicant_id = isset($arrInput['applicant_id']) ? Intval($arrInput['applicant_id']) : 0;

	//λ��
	$limit = isset($arrInput['limit']) ? Intval($arrInput['limit']) : NULL;
	$offset = isset($arrInput['offset']) ? Intval($arrInput['offset']) : NULL;

	//ʱ��
	$start = isset($arrInput['start']) ? Intval($arrInput['start']) : 0;
	$end = isset($arrInput['end']) ? Intval($arrInput['end']) : 0;
	$time_type = isset($arrInput['time_type']) ? $arrInput['time_type'] : 'application_time';

	$append_str = "ORDER BY id DESC ";
	$where_arr = NULL;
	if($limit === 0 || !empty($limit)){
		$append_str .= "LIMIT $limit ";
	}
	if($offset === 0 || !empty($offset)){
		$append_str .= "OFFSET $offset";
	}

	if($forum_id > 0){
		$where_arr['forum_id='] = $forum_id;
	}
	if($applicant_id > 0){
		$where_arr['applicant_id='] = $applicant_id;
	}

	if($start > 0){
		$where_arr[$time_type.'>='] = $start;
	}
	if($end > 0){
		$where_arr[$time_type.'<='] = $end;
	}

	return array('where' => $where_arr, 'append' => $append_str);
}
/**
 * @brief
 * @arrInput:
 * 	uint64_t forum_id
 * 	string avatar
 * 	string slogan
 * 	uint64_t applicant_id
 * @return: $arrOutput
**/
public static function apply($arrInput){
	//check the tb sig, if no need, just remove the codes below.
	if(!Tieba_Service::checkTBSig($arrInput)){
        Bingo_Log::warning("check tb sig fail. invalid request.");
        return self::_errRet(Tieba_Errcode::ERR_TBSIG_CHECK_FAIL);
    }
			
	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['forum_id']) || !isset($arrInput['avatar']) 
		|| !isset($arrInput['slogan']) || !isset($arrInput['applicant_id']))
	{	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$forum_id = intval($arrInput['forum_id']);
	$avatar = $arrInput['avatar'];
	$slogan = $arrInput['slogan'];
    //add by cuishichao 2013-08-30 start
	//$slogan = mysql_real_escape_string($slogan);
	$desc = $arrInput['desc'];
	//$desc = mysql_real_escape_string($desc);
	//add by cuishichao 2013-08-30  end
	$applicant_id = intval($arrInput['applicant_id']);

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.

	//your code here......
	$db = self::_getDB();
	if($db === null){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	$db->charset("utf8");

	$res = self::getApplicationStatus(array('forum_id' => $forum_id));
	if($res['error'] != Tieba_Errcode::ERR_SUCCESS){
		return self::_errRet(Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL);
	}
	if($res['status']['status'] != NULL && $res['status']['status'] == self::EXAMINING){
		return self::_errRet(Tieba_Errcode::ERR_UNKOWN);
	}
	$arrInsert = array(
		'forum_id' => $forum_id, 
		'avatar' => $avatar, 
		'slogan' => $slogan, 
	    'description' => $desc,  //add by cuishichao 2013-08-30
		'applicant_id' => $applicant_id, 
		'application_time' => time()
	);
	if(false === $db->insert(self::DB_TABLE_NAME, $arrInsert)){
		Bingo_Log::warning("query db failed. [sql:".$db->getLastSQL()."][err:".$db->error()."]");
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	}
	Bingo_Log::debug("[sql:".$db->getLastSQL()."]");

	//完成吧主考核任务-修改吧资料
    $arrReq = array(
        'forum_id' => $forum_id,
        'user_id' => $applicant_id,
        'task_type' => Lib_BawuTask::TASK_USE_BAWU_TOOL, //1：发回贴 2：使用吧主工具
    );
    $arrRes = Service_Bawutask_Bawutask::finishTask($arrReq);
    Bingo_Log::warning(' call Service_Bawutask_Bawutask::finishTask modify forum apply input = '.serialize($arrInput).'req = '.serialize($arrReq).'res = '.serialize($arrRes));
	
	/*
	//send msg to nmq; even if you do the update in sync mod.
	//as a new cmd, the $strCmd = __FUNCTION__ by default.
	//attention!!! the codes belows is just example, change it yourself;
	$arrNMQParams = array();
	//your code to fill params for nmq. not include command_no;
	
	$arrNMQRes = Tieba_Commit::commit('card', 'apply',$arrNMQParams);
	if (is_array($arrNMQRes)&&isset($arrNMQRes['error_no']) && (intval($arrNMQRes['error_no']) == 0)){ 
		Bingo_Log::debug("got tieba-mq-commit res ".serialize($arrNMQRes));         
	}else{
		Bingo_Log::warning("call tieba-mq-commit error [input:".serialize($arrNMQParams)."] [output:".serialize($arrNMQRes)."]");
		return self::_errRet(Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL);
	}
	*/
			
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint64_t id
 *  uint64_t examiner_id,
 *  uint32_t status,
 *  string reason,
 * @return: $arrOutput
 * 	bool result
**/
public static function examine($arrInput){
			
	//check the tb sig, if no need, just remove the codes below.
	if(!Tieba_Service::checkTBSig($arrInput)){
        Bingo_Log::warning("check tb sig fail. invalid request.");
        return self::_errRet(Tieba_Errcode::ERR_TBSIG_CHECK_FAIL);
    }
			
	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['id']) || !isset($arrInput['examiner_id']) || !isset($arrInput['status'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$id = intval($arrInput['id']);
	$examiner_id = intval($arrInput['examiner_id']);
	$status = $arrInput['status'];
	$reason = $arrInput['reason'];

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL); 
	}
	//output params.
	$result = false;

	//your code here......
	$db = self::_getDB();
	if($db === null){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	$db->charset("utf8");

	$arrUpdate = array('examiner_id' => $examiner_id, 'examine_time' => time(), 'status' => $status, 'reason' => $reason);
	if(false === ($result = $db->update(self::DB_TABLE_NAME, $arrUpdate, array('id=' => $id)))){
		Bingo_Log::warning("query db failed. [sql:".$db->getLastSQL()."][err:".$db->error()."]");
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	}
	Bingo_Log::debug("[sql:".$db->getLastSQL()."]");

	/*
	//send msg to nmq; even if you do the update in sync mod.
	//as a new cmd, the $strCmd = __FUNCTION__ by default.
	//attention!!! the codes belows is just example, change it yourself;
	$arrNMQParams = array();
	//your code to fill params for nmq. not include command_no;
	
	$arrNMQRes = Tieba_Commit::commit('card', 'examine',$arrNMQParams);
	if (is_array($arrNMQRes)&&isset($arrNMQRes['error_no']) && (intval($arrNMQRes['error_no']) == 0)){ 
		Bingo_Log::debug("got tieba-mq-commit res ".serialize($arrNMQRes));         
	}else{
		Bingo_Log::warning("call tieba-mq-commit error [input:".serialize($arrNMQParams)."] [output:".serialize($arrNMQRes)."]");
		return self::_errRet(Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL);
	} 
	*/
		
	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'result' => $result,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint64_t forum_id
 * @return: $arrOutput
 * 	uint32_t status
**/
public static function getApplicationStatus($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['forum_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$forum_id = intval($arrInput['forum_id']);
	
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$status = false;

	//your code here......
	$db = self::_getDB();
	if($db === null){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}

	if(false === ($status = $db->select(self::DB_TABLE_NAME, array('examine_time', 'status'), 
			array('forum_id=' => $forum_id), NULL, array('ORDER BY application_time DESC', 'LIMIT 1'))))
	{
		Bingo_Log::warning("query db failed. [sql:".$db->getLastSQL()."][err:".$db->error()."]");
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	}
	Bingo_Log::debug("[sql:".$db->getLastSQL()."]");

	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'status' => $status[0],
	);
	return $arrOutput;
}
/**
 * @brief   add by cuishichao 2013-08-30
 * @arrInput:
 * 	uint64_t forum_id
 * @return: $arrOutput
 *  uint64_t forum_id
 * 	string avatar
 *  string slogan
 *  string desc
**/
public static function getForumCardInfoByForumId($arrInput){

	// input params check;
	// if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
	if(!isset($arrInput['forum_id'])){	
		Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
	    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR); 
	}
		
	//input params.
	$forum_id = intval($arrInput['forum_id']);
	
	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$Output = array(
	   'forum_id' => $forum_id,
	   'avatar' => '',
	   'slogan' => '',
	   'desc' => '',
	);

	//your code here......
	$db = self::_getDB();
	if($db === null){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	if(false === ($status = $db->select(self::DB_TABLE_NAME, array('examine_time', 'status', 'avatar', 'slogan', 'description'), 
			array('forum_id=' => $forum_id), NULL, array('ORDER BY application_time DESC', 'LIMIT 1'))))
	{
		Bingo_Log::warning("query db failed. [sql:".$db->getLastSQL()."][err:".$db->error()."]");
		return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
	}
	Bingo_Log::debug("[sql:".$db->getLastSQL()."]");

	$error = Tieba_Errcode::ERR_SUCCESS;
	$Output['avatar'] = $status[0]['avatar'];
	$Output['slogan'] = $status[0]['slogan'];
	$Output['desc'] = $status[0]['description'];
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'data' => $Output,
	);
	return $arrOutput;
}
/**
 * @brief
 * @arrInput:
 * 	uint32_t status
 * 	uint64_t limit 
 * 	uint64_t offset
 * 	uint64_t forum_id,
 * 	uint64_t applicant_id,
 *      bool is_count,
 * @return: $arrOutput
 * 	application application_list[]
**/
public static function getApplication($arrInput){
	// input params check;
		
	//input params.
	$is_count = isset($arrInput['is_count']) ? (bool)$arrInput['is_count'] : false;

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$application_list = false;

	//your code here......
	$db = self::_getDB();
	if($db === null){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	//$db->charset("utf8");
	
	$param = self::_genSQLParam($arrInput);
	$where_arr = $param['where'];
	$where_arr['status='] = self::EXAMINING;
	Bingo_Log::debug("gen param.[param:".json_encode($where_arr)."]");
	$append_str = $param['append'];

	if(true === $is_count){
		if(false === ($application_list = $db->selectCount(self::DB_TABLE_NAME, $where_arr))){
			Bingo_Log::warning(
				"query db failed. [sql:".$db->getLastSQL()."][err:".$db->error()."]");
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		Bingo_Log::debug("[sql:".$db->getLastSQL()."]");
	}
	else{
		if(false === ($application_list = 
			$db->select(self::DB_TABLE_NAME, "*", $where_arr, NULL, $append_str)))
		{
			Bingo_Log::warning(
				"query db failed. [sql:".$db->getLastSQL()."][err:".$db->error()."]");
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
		Bingo_Log::debug("[sql:".$db->getLastSQL()."]");
		foreach($application_list as &$elem){
			$forum_id = $elem['forum_id'];
			$where_arr = array('status!=' => self::EXAMINING, 'forum_id=' => $forum_id);
			$history_arr = $db->select(
				self::DB_TABLE_NAME, array('examine_time', 'status'), $where_arr);
			foreach($history_arr as $history){
				if($history['status'] == self::SUCCESS){
					$status = "success";
				}
				else if($history['status'] == self::FAILURE){
					$status = "failure";
				}
				$elem['history'] .= 
					date('Y-m-d H:i:s', $history['examine_time'])." ".$status."\n";
			}
		}

	}

	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'application_list' => $application_list,
	);
	return $arrOutput;
}

    /**
     * @param $arrInput
     * @return array
     */
    public static function getApplicationByScriptStatus($arrInput){
        // input params check;

        //input params.
        $is_count = isset($arrInput['is_count']) ? (bool)$arrInput['is_count'] : false;

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.
        $application_list = false;

        //your code here......
        $db = self::_getDB();
        if($db === null){
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        //$db->charset("utf8");

        $param = self::_genSQLParam($arrInput);
        $where_arr = $param['where'];
        $where_arr['status='] = self::EXAMINING;
        $where_arr['script_status='] = self::SCRIPT_PASS;
        Bingo_Log::debug("gen param.[param:".json_encode($where_arr)."]");
        $append_str = $param['append'];

        if(true === $is_count){
            if(false === ($application_list = $db->selectCount(self::DB_TABLE_NAME, $where_arr))){
                Bingo_Log::warning(
                    "query db failed. [sql:".$db->getLastSQL()."][err:".$db->error()."]");
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            Bingo_Log::debug("[sql:".$db->getLastSQL()."]");
        }
        else{
            if(false === ($application_list =
                    $db->select(self::DB_TABLE_NAME, "*", $where_arr, null, $append_str)))
            {
                Bingo_Log::warning(
                    "query db failed. [sql:".$db->getLastSQL()."][err:".$db->error()."]");
                return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
            }
            Bingo_Log::debug("[sql:".$db->getLastSQL()."]");
            foreach($application_list as &$elem){
                $forum_id = $elem['forum_id'];
                $where_arr = array('status!=' => self::EXAMINING, 'forum_id=' => $forum_id);
                $history_arr = $db->select(
                    self::DB_TABLE_NAME, array('examine_time', 'status'), $where_arr);
                foreach($history_arr as $history){
                    if($history['status'] == self::SUCCESS){
                        $status = "success";
                    }
                    else if($history['status'] == self::FAILURE){
                        $status = "failure";
                    }
                    $elem['history'] .=
                        date('Y-m-d H:i:s', $history['examine_time'])." ".$status."\n";
                }
            }

        }

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'application_list' => $application_list,
        );
        return $arrOutput;
    }
/**
 * @brief
 * @arrInput:
 * 	uint32_t status
 * 	uint64_t limit 
 * 	uint64_t offset
 * 	uint64_t forum_id,
 * 	uint64_t applicant_id,
 *      bool is_count,
 * @return: $arrOutput
 * 	application application_list[]
**/
public static function getHistory($arrInput){
	// input params check;
		
	//input params.
	$is_count = isset($arrInput['is_count']) ? (bool)$arrInput['is_count'] : false;
	$status = isset($arrInput['status']) ? (int)$arrInput['status'] : NULL;

	if(!self::_init()){
		return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
	}
	//output params.
	$application_list = false;

	//your code here......
	$db = self::_getDB();
	if($db === null){
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}
	//$db->charset("utf8");
	
	$param = self::_genSQLParam($arrInput);
	$where_arr = $param['where'];
	if($status !== 0 && empty($status)){
		$where_arr['status!='] = self::EXAMINING;
	}
	else{
		$where_arr['status='] = $status;
	}
	$append_str = $param['append'];

	if(true === $is_count){
		if(false === ($application_list = $db->selectCount(self::DB_TABLE_NAME, $where_arr))){
			Bingo_Log::warning(
				"query db failed. [sql:".$db->getLastSQL()."][err:".$db->error()."]");
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
	}
	else{
		if(false === ($application_list = 
			$db->select(self::DB_TABLE_NAME, "*", $where_arr, NULL, $append_str)))
		{
			Bingo_Log::warning(
				"query db failed. [sql:".$db->getLastSQL()."][err:".$db->error()."]");
			return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
		}
	}
	Bingo_Log::debug("[sql:".$db->getLastSQL()."]");

	$error = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput = array(
		'errno' => $error,
		'errmsg' => Tieba_Error::getErrmsg($error),
		'application_list' => $application_list,
	);
	return $arrOutput;
}
    //add by yunting 2014.06.19
    public function updateFcardExamineTime($arrInput = array()) {
        $intFid = intval($arrInput['forum_id']);
        $db = self::_getDB();
		if ($db === null) {
				return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
		}
        $selectSql = "select id from forum_card_table where forum_id = $intFid order by application_time desc limit 1;";
        $selectRes = $db->query($selectSql);
        if (false === $selectRes) {
            Bingo_Log::warning("updateFcardExamineTime select from forum_card_table fail input:".  serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        //�޼�¼ֱ�ӷ���true
        if (empty($selectRes)) {
            $error = Tieba_Errcode::ERR_SUCCESS;
            $arrOutput = array(
                'errno'  => $error,
                'errmsg' => Tieba_Error::getErrmsg($error),
            );
            return $arrOutput;
        }
        $id = $selectRes[0]['id'];
        $sql = "update forum_card_table set examine_time = 0 where id = $id ;";
        $updateRes = $db->query($sql);
        if (false === $updateRes) {
            Bingo_Log::warning("updateFcardExamineTime failed. [sql:".$db->getLastSQL()."][err:".$db->error()."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        $error = Tieba_Errcode::ERR_SUCCESS;
		$arrOutput = array(
				'errno'  => $error,
				'errmsg' => Tieba_Error::getErrmsg($error),
		);
		return $arrOutput;
    }

    /*********************************** 私有化吧吧名片查询方法 **********************************/
    /**
     * @brief
     * @arrInput:
     * 	uint64_t forum_id
     * @return: $arrOutput
     * 	uint32_t status
     **/
    public static function getForumApplyCardInfo($arrInput){

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['forum_id'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input params.
        $forum_id = intval($arrInput['forum_id']);

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }
        //output params.
        $status = false;

        //your code here......
        $db = self::_getDB();
        if($db === null){
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $field = array('examine_time', 'status', 'avatar', 'slogan', 'description', 'id', 'application_time', 'reason', 'examiner_id', 'applicant_id');
        if(false === ($res = $db->select(self::DB_TABLE_NAME, $field,
                array('forum_id=' => $forum_id), NULL, array('ORDER BY application_time DESC', 'LIMIT 1'))))
        {
            Bingo_Log::warning("query db failed. [sql:".$db->getLastSQL()."][err:".$db->error()."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        Bingo_Log::debug("[sql:".$db->getLastSQL()."]");
        Bingo_Log::warning('call '.__METHOD__.' res['.json_encode($res).']');

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data' => $res[0],
        );
        return $arrOutput;
    }

    /**
     * @brief
     * @arrInput:
     * 	uint64_t id
     *  uint64_t examiner_id,
     *  uint32_t status,
     *  string reason,
     * @return: $arrOutput
     * 	bool result
     **/
    public static function processForumApplyCardCancel($arrInput){

        //check the tb sig, if no need, just remove the codes below.
        if(!Tieba_Service::checkTBSig($arrInput)){
            Bingo_Log::warning("check tb sig fail. invalid request.");
            return self::_errRet(Tieba_Errcode::ERR_TBSIG_CHECK_FAIL);
        }

        // input params check;
        // if receive msg from nmq/transfer in raw binary mcpack format,del the check code below.
        if(!isset($arrInput['id']) || !isset($arrInput['status'])){
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        //input params.
        $id     = intval($arrInput['id']);
        $status = $arrInput['status'];

        if(!self::_init()){
            return self::_errRet(Tieba_Errcode::ERR_LOAD_CONFIG_FAIL);
        }

        //your code here......
        $db = self::_getDB();
        if($db === null){
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $db->charset("utf8");

        $arrUpdate = array('status' => $status);
        if(false === ($result = $db->update(self::DB_TABLE_NAME, $arrUpdate, array('id=' => $id)))){
            Bingo_Log::warning("query db failed. [sql:".$db->getLastSQL()."][err:".$db->error()."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        Bingo_Log::debug("[sql:".$db->getLastSQL()."]");

        $error = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'result' => $result,
        );
        return $arrOutput;
    }

    /**
     * @desc 更新吧头像
     */
    public static function updateAvatar($arrInput)
    {
        if (
            !isset($arrInput['forum_id']) ||
            !isset($arrInput['avatar']) ||
            !isset($arrInput['applicant_id'])
        ) {
            Bingo_Log::warning("updateAvatar input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $forumID = intval($arrInput['forum_id']);
        $avatar = $arrInput['avatar'];
        $applicatID = intval($arrInput['applicant_id']);
        Bingo_Log::notice(sprintf("updateAvatar input: [%s]",serialize($arrInput)));
        return self::_updateForumCard($forumID,$applicatID,array('avatar' => $avatar));
    }

    /**
     * @desc 更新吧简介
     */
    public static function updateDesc($arrInput){
        if (
            !isset($arrInput['forum_id']) ||
            !isset($arrInput['slogan']) ||
            !isset($arrInput['desc']) ||
            !isset($arrInput['applicant_id'])
        ) {
            Bingo_Log::warning("updateAvatar input params invalid. [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $forumID = intval($arrInput['forum_id']);
        $desc = array(
            'slogan' => Bingo_Encode::convert($arrInput['slogan'], Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK),
            'desc' => Bingo_Encode::convert($arrInput['desc'], Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK),
        );
        $applicatID = intval($arrInput['applicant_id']);
        Bingo_Log::notice(sprintf("updateAvatar input: [%s]",serialize($arrInput)));
        return self::_updateForumCard($forumID,$applicatID,$desc);
    }

    /**
     * @desc 更新吧名片
     */
    private static function _updateForumCard($forumID,$uid,$card){
        $avatar = isset($card['avatar']) ? $card['avatar']: '';
        $slogan = isset($card['slogan']) ? $card['slogan']: '';
        $desc = isset($card['desc']) ? $card['desc'] : '';

        // 检查是否有正在审核中的，以及是否达到申请周期
        $res = self::getApplicationStatus(array('forum_id' => $forumID));
        if (false == $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            return self::_errRet(Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL);
        }
        if ($res['status']['status'] != null && $res['status']['status'] == self::EXAMINING) {
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN, "updateAvatar now fcard is applying");
        }
        // 审核成功后才开始计算冷却时间
        if ($res['status']['status'] != null &&
            $res['status']['status'] == 1 &&
            $res['status']['examine_time'] + self::FORUM_CARD_UPDATE_PERIODS * 86400 > time()
        ) {
            return self::_errRet(Tieba_Errcode::ERR_UNKOWN, "updateAvatar fcard is not cold down");
        }

        // 获取吧名片当前信息
        $fcard = self::_getForumCard($forumID);
        if (empty($fcard)){
            Bingo_Log::warning(sprintf("updateAvatar get forum card empty forum_id=[%d]",$forumID));
            $fcard = array();
        }

        $db = self::_getDB();
        if ($db === null) {
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $db->charset("utf8");
        $arrInsert = array(
            'forum_id' => $forumID, 
            'avatar' => $avatar != '' ? $avatar : (isset($fcard['avatar']) ? $fcard['avatar'] : ''),
            'slogan' => $slogan != '' ? $slogan : (isset($fcard['slogan']) ? $fcard['slogan'] : '') ,
            'description' => $desc != '' ? $desc : (isset($fcard['desc']) ? $fcard['desc'] : ''),
            'applicant_id' => $uid, 
            'application_time' => time()
        );

        if(false === $db->insert(self::DB_TABLE_NAME, $arrInsert)){
            Bingo_Log::warning("updateAvatar query db failed. [sql:".$db->getLastSQL()."][err:".$db->error()."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }

        //完成吧主考核任务-修改吧资料
        $arrReq = array(
            'forum_id' => $forumID,
            'user_id' => $uid,
            'task_type' => Lib_BawuTask::TASK_USE_BAWU_TOOL, //1：发回贴 2：使用吧主工具
        );
        $arrRes = Service_Bawutask_Bawutask::finishTask($arrReq);
        if (false === $arrRes || $arrRes['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(' call Service_Bawutask_Bawutask::finishTask modify forum apply req = '.serialize($arrReq).'res = '.serialize($arrRes));
        }
       return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @desc 获取当前的吧名片
     */
    private static function  _getForumCard($fid){
        $input = array(
            "forum_id" => $fid,
        );
        $res = Tieba_Service::call('forum', 'getForumAttr', $input, null, null, 'post', 'php', 'utf-8');
        if(false === $res || $res['errno'] != Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(sprintf("call service forum::getForumAttr fail, input=[%s] output=[%s]",serialize($input),serialize($res)));
            return false;
        }
        $fcard = json_decode($res['output']['card_p1']['style_name'],true);
        return $fcard;
    }
}