<?php
define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../../' );
require_once(ROOT_PATH ."app/common/util/Tools.php");
/**
 * 吧广播ueg进行审核
 * Class Service_Uegcheck_Pushtouegcheck
 */
class Service_Uegcheck_Uegcheck {

    const PROJECT_ID = 2264;
    const UNCHECK = 1;
    const CHECKING = 4;
    const FAIL = 6;
    const SUCC = 10;

    /**
     * 推送到ueg审核平台进行审核
     */
    public static function pushToUegCheck() {
        $inputArr = array(
            'broadcast_status' => self::UNCHECK,
            'offset' => 0,
            'limit'  => 10, // 实际条数待确定
        );

        $arrOutput = self::_getBroadcastList($inputArr);
        // 没有数据需要进行审核
        if (count($arrOutput) == 0) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $forums = array();
        $users = array();
        foreach ($arrOutput as $item) {
            $users[] = $item['manager_user_id'];
            $forums[] = $item['forum_id'];
        }

        if (count($users) > 0) {
            $users = self::_getUnameByUid($users);
        }
        if (count($forums) > 0) {
            $forums = self::_getFnameByFid($forums);
        }
        if ($users === false || $forums === false) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $inputData = array();
        foreach ($arrOutput as $item) {
            $inputData[] = array(
                'item_id' => $item['broadcast_id'],
                'user_id' => $item['manager_user_id'],
                'user_name' => $users[$item['manager_user_id']],
                'forum_id' => $item['forum_id'],
                'forum_name' => $forums[$item['forum_id']]['forum_name'],
                'thread_id' => $item['thread_id'],
                'title' => $item['content'],    // 标题
                'image_url' => '<img src='. $item['image'] .'>',  // 图片
                'content' => $item['title'],  // 内容
                'user_join_time' => date('Y-m-d H:i:s', $item['create_time']), // 用户创建时间
                'broadcast_status' => $item['broadcast_status'],
                'item_url' => '<a href="'.$item['url'].'" target="_blank">'.$item['url'].'</a>', // 链接,ueg的用现有的字段
                'check_msg' => '',
                'post_id' => $item['broadcast_id'],
            );
        }
        $uegInput = array(
            'project_id' => self::PROJECT_ID,
            'datas' => $inputData,
        );
        $uegOutput = Tieba_Service::call('uegnaudit', 'mImportPostData', $uegInput, null, null, 'post', 'php', 'utf-8');
        if ($uegOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("push to ueg check call anti::uegNewAuditPost error.input=" .serialize($uegInput) ." output=". serialize($uegOutput));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        // push成功之后回调，将广播状态改成审核中
        $objMulti = new Tieba_Multi('anti_ueg_check_broadcast');
        foreach ($arrOutput as $item) {
            $arrMultiInput = array(
                'serviceName' => 'bawu',
                'method' => 'editBroadcastInfo',
                'input'  => array(
                    'broadcast_id' => $item['broadcast_id'],
                    'broadcast_status' => self::CHECKING,
                ),
            );
            $objMulti -> register('edit_broadcast'.$item['broadcast_id'], new Tieba_Service('post'), $arrMultiInput);
        }
        $objMulti -> call();
        foreach ($arrOutput as $item) {
            $arrOutput = $objMulti->getResult('edit_broadcast'.$item['broadcast_id']);
            if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning("push to ueg check succ but edit broadcast status bawu::editBroadcastInfo error.input=" .serialize($item) ." output=". serialize($arrOutput));
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 吧广播审核之后回调
     * 操作：
     * 1. 更新吧广播状态
     * 2. 吧主触达，给吧主发push+端内消息
     * 3. 用户触达，给用户发吧广播消息
     *
     * 关于失败问题： （ueg重试1次）
     * 更新操作失败，ueg进行重试，如果再次失败，editBroadcast接口能保证重复更新状态能返回成功
     * 吧主触达失败，ueg进行重试
     * 用户广播push失败 ueg重试，可能造成吧主触达重复，这个跟pm确定过，能接受
     * @param $arrInput
     * @return array
     */
    public static function uegCheckCallBack($arrInput) {

        if (empty($arrInput['project_id']) || empty($arrInput['broadcast_id']) || empty($arrInput['broadcast_status'])) {
            Bingo_Log::warning("ueg check call back bawu::uegCheckCallBack error. input=" .serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $broadcastStatus = intval($arrInput['broadcast_status']);
        $checkMsg = $arrInput['check_msg'];
        if ($broadcastStatus == self::UNCHECK || $arrInput['project_id'] != self::PROJECT_ID) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if ($broadcastStatus != self::FAIL && $broadcastStatus != self::SUCC) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrInput = array(
            'broadcast_id' => $arrInput['broadcast_id'],
            'broadcast_status' => $arrInput['broadcast_status'],
        );
        $arrOutput = Tieba_Service::call('bawu', 'editBroadcastInfo', $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("ueg check call back succ but edit broadcast status bawu::editBroadcastInfo error.input=" .serialize($arrInput) ." output=". serialize($arrOutput));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $inputArr = array(
            'broadcast_ids' => array($arrInput['broadcast_id']),
            'limit'  => 1, // 实际条数待确定
        );
        $arrOutput = self::_getBroadcastList($inputArr);
        if (count($arrOutput) !== 1) {
            Bingo_Log::warning("ueg check call back succ but call bawu::getBroadcastList error, input=".serialize($arrOutput)." output=" . serialize($arrOutput));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $broadcastInfo = $arrOutput[0];
        // 成功之后调用push接口，触达吧主
        if ( !self::_pushMsg($broadcastInfo, $broadcastStatus, $checkMsg)) {
            Bingo_Log::warning("ueg check call back succ but call common::pushMsgbyUid error, broadcastInfo=".serialize($broadcastInfo));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        // 调用push接口，触达用户
        if ($broadcastStatus == self::SUCC) {
            if ( !self::_pushMsgToUser($broadcastInfo)) {
                Bingo_Log::warning("ueg check call back succ but call bawu::sendBroadcast error, broadcastInfo=".serialize($broadcastInfo));
                return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 给用户发广播消息
     * @param $broadcastInfo
     * @return bool
     */
    private static function _pushMsgToUser($broadcastInfo) {
        $inputArr = array(
            'forum_id' => intval($broadcastInfo['forum_id']),
            'msg_id'  => $broadcastInfo['broadcast_id'],
        );

        $arrOutput = Tieba_Service::call('bazhutools', 'sendBroadcast', $inputArr, null, null, 'post', 'jsonraw', 'utf-8');
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call bazhutools::sendBroadcast error. input" . serialize($inputArr) . " output=" . serialize($arrOutput));
            return false;
        }
        return true;
    }

    /**
     * 获取吧列表
     * @param $inputArr
     * @return array
     */
    private static function _getBroadcastList($inputArr) {
        $arrOutput = Tieba_Service::call('bawu', 'getBroadcastList', $inputArr, null, null, 'post', 'php', 'gbk');
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("push to ueg check call post::getBroadcastList error." . serialize($arrOutput));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return $arrOutput['data']['broadcast_list'];
    }

    /**
     * 通过uids获取用户的names
     * @param $uidArr
     * @return array|bool
     */
    private static function _getUnameByUid($uidArr) {

        $arrInput = array(
            'user_id' => $uidArr,
        );
        $arrOutput = Tieba_Service::call('user', 'getUnameByUids', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            Bingo_Log::warning("call user::getUnameByUids error. input" . serialize($arrInput) . " output=" . serialize($arrOutput));
            return false;
        }

        $arrOutput = $arrOutput['output']['unames'];
        $users = array();
        foreach ($arrOutput as $item) {
            $users[$item['user_id']] = $item['user_name'];
        }
        return $users;
    }



    /**
     * 获取吧名
     * @param $forumIdArr
     * @return array
     */
    private static function _getFnameByFid($forumIdArr) {

        $arrInput = array(
            'forum_id' => $forumIdArr,
        );
        $arrOutput = Tieba_Service::call('forum', 'getFnameByFid', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']) {
            Bingo_Log::warning("call forum::getFnameByFid error. input" . serialize($arrInput) . " output=" . serialize($arrOutput));
            return false;
        }
        return $arrOutput['forum_name'];
    }

    /**
     * 发送吧审核和端内消息
     * @param $broadcastInfo
     * @param $status
     * @param $check_msg
     * @return bool
     */
    private static function _pushMsg($broadcastInfo, $status, $check_msg) {

        $msg = '已通过审核';
        $image = "";
        if ($status == self::FAIL) {
            $msg = "未通过审核";
            $image = "http://tb1.bdstatic.com/tb/r/image/2020-07-23/212d393db2bfbe4d3e51bd8acbd093f7.png";
            if ( !empty($check_msg)) {
                $msg = $msg.',原因是'.$check_msg;
            }
        }

        if ($status == self::SUCC) {
            $msg = "已通过审核";
            $image = "http://tb1.bdstatic.com/tb/r/image/2020-07-23/9c2119f7169d3cd2d57596668297f03d.png";
        }

        // 获取吧名
        $forums = array($broadcastInfo['forum_id']);
        $forums = self::_getFnameByFid($forums);
        $forumName = $forums[$broadcastInfo['forum_id']]['forum_name'];
        // pm说都发，不用做开关的check
        $arrInput = array(
            'title' => '贴吧班主任给你发了消息',
            'link' => 'com.baidu.tieba://unidispatch/forumbroadcast/reviewed?id='.$broadcastInfo['forum_id'].'&to='.$broadcastInfo['manager_user_id'].'&name='.$forumName,
            'abstract' => '您在'.date('n月j日', $broadcastInfo['create_time']).'申请的吧广播'. $msg,
            'pic_url' => $image,
            'from_id' => '73230149',
            'from_name' => '贴吧班主任',
            'to_id' => $broadcastInfo['manager_user_id'],
        );
        $ret = Util_Tools::pushMsgbyUid($arrInput);
        if (false === $ret || Tieba_Errcode::ERR_SUCCESS != $ret['errno']) {
            Bingo_Log::warning("call common::pushMsgbyUid error. input" . serialize($arrInput) . " output=" . serialize($ret));
            return false;
        }
        return true;
    }

    /**
     * return data format
     * @param $errno
     * @param array $data
     * @return array
     */
    private static function _errRet($errno, $data = array()){
        return array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data'   => $data,
        );
    }
}