<?php
/**
 * <AUTHOR>
 * @date 2017-06-04
 */
class Service_Title_Title{
	const WORDLIST = 'tb_wordlist_redis_forum_title';
    const WORDLIST_APP = 'tb_wordlist_redis';
    const WORDLIST_NAME = 'forum_title';
    const APP_TOKEN = 'tb_wordlist_redissmU4r8Wwcx';
	const SETHONOR = 3413;
	const DIY_TITLE = 65535;
    
    const RESULT_CLAIM= 3;
	const RESULT_WAIT = 0;
	const RESULT_PASS = 1;
	const RESULT_REFUSE = 2;
	const DEFAULT_TITLE_ID = 0;
    const MAX_CLAIM_CNT = 20;	
	/**
	 * @param [type] $arrInput [description]
	 * @return
	 */
	public static function addForumTitle($arrInput){
		if(!isset($arrInput['forum_id'])|| $arrInput['forum_id'] <= 0
		|| !isset($arrInput['forum_name']) || '' === $arrInput['forum_name']
		|| !isset($arrInput['level_1_name']) || '' === $arrInput['level_1_name']
		|| !isset($arrInput['user_id'])|| $arrInput['user_id']<=0
		|| !isset($arrInput['user_name']) || '' === $arrInput['user_name']
		|| !isset($arrInput['title_id']) || $arrInput['title_id']<0 ){
			Bingo_Log::warning("param error input[".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}

		$intTitleId = (int)$arrInput['title_id'];

		if(self::DIY_TITLE === $intTitleId
		&&(!isset($arrInput['forum_titles'])|| count($arrInput['forum_titles']) !== 18)){
			Bingo_Log::warning("param error: wrong title count input[".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$intUserDefine = 0;
		$arrInput['level_1_name'] = Bingo_Encode::convert($arrInput['level_1_name'], Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);

		$arrTitles = array();
		$arrGradeIds = array();

		if(self::DIY_TITLE === $intTitleId){
			$intUserDefine = 1;
			foreach($arrInput['forum_titles'] as $k => $title){
				$utf8_title = Bingo_Encode::convert($title, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
				$arrTitles[$utf8_title] = 1;
				$arrGradeIds[$k] = array(
					'id' => $k + 1,
					'name' => $title,
				);
			}

			$arrBlackTitles = self::checkTitleBlack(array_keys($arrTitles));
			if(false === $arrBlackTitles || !empty($arrBlackTitles)){
				Bingo_Log::warning('check  black failed or there exists black titles');
				return self::_errRet(Tieba_Errcode::ERR_ANTI_CONFILTER_WORD, $arrBlackTitles);
			}

			$arrExt =  array(
				'title' => $arrTitles,
			);
			$arrInput['ext'] = serialize($arrExt);
			$arrInput['apply_time'] = time();
			$arrInput['result'] = self::RESULT_WAIT;
			$arrOutput = Dl_Title_Title::insert(array($arrInput));
			if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
				Bingo_Log::warning(sprintf("call Dl_Title_Title::select failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
				return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
			}
		}

		$arrInput['title_id'] = $intTitleId;
		$arrInput['is_user_define'] = $intUserDefine;
		$arrInput['grade'] = $arrGradeIds;
		$arrOutput = self::setForumTitle($arrInput);
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
			Bingo_Log::warning(sprintf("call Dl_Title_Title::select failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
			return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
		}
		return self::_succRet();
	}
	/**
	 * [modifyForumTitle description]
	 * @param  [type] $arrInput [description]
	 * @return [type]           [description]
	 */
	public static function modifyForumTitle($arrInput){
		if(!isset($arrInput['forum_id'])|| $arrInput['forum_id'] <= 0
		|| !isset($arrInput['forum_name']) || '' === $arrInput['forum_name']
		|| !isset($arrInput['user_id'])|| $arrInput['user_id']<=0
		|| !isset($arrInput['user_name']) || '' === $arrInput['user_name']){
			Bingo_Log::warning("param error input[".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrInput['title_id'] = 0;
		$arrInput['is_user_define'] = 0;
		$arrInput['grade'] = array();
		$arrOutput = self::setForumTitle($arrInput);
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
			Bingo_Log::warning(sprintf("call Dl_Title_Title::select failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
			return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
		}
		return self::_succRet();

	}
	/**
	 * [setForumTitle description]
	 * @param [type] $arrInput [description]
	 * @return
	 */
	private static function setForumTitle($arrInput){
		$arrNmqInput = array(
			'command_no' => self::SETHONOR,
			'forum_id'   => $arrInput['forum_id'],
			'forum_name' => $arrInput['forum_name'],
			'op_uname'   => $arrInput['user_name'],
			'op_uid'     => $arrInput['user_id'],
			'now_time'   => time(),
			'grade_title_id'          => $arrInput['title_id'],
			'grade_title_user_define' => $arrInput['is_user_define'],
			'grade_ids'  => $arrInput['grade'],
		);
		$arrNmqOutput = Tieba_Commit::commit ( 'perm', 'gradeSetLelName', $arrNmqInput );
		if ($arrNmqOutput === false || ! isset ( $arrNmqOutput['err_no'] ) || $arrNmqOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning ( " Tieba_Commit::commit fail" ."input:[".serialize($arrNmqInput)."] out:[" .serialize($arrNmqOutput) ."]");
			return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
		}
		return self::_succRet();

	}
	/**
	 * @param [type] $arrInput [description]
	 * @return
	 */
	public static function listForumTitle($arrInput){
		if(!isset($arrInput['pn'])||$arrInput['pn']<=0
		|| !isset($arrInput['rn'])|| $arrInput['rn']<=0){
			Bingo_Log::warning("param error input[".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$intPn = (int)$arrInput['pn'];
		$intRn = (int)$arrInput['rn'];
		
        $arrInput['orderby'] = array(
			'field' => 'apply_id',
			'sort' => 'DESC',
		);
		$arrOutput = Dl_Title_Title::select($arrInput);
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
			Bingo_Log::warning(sprintf("call Dl_Title_Title::select failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		$arrList = $arrOutput['data'];
		$arrOutput = Dl_Title_Title::selectCnt($arrInput);
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
			Bingo_Log::warning(sprintf("call Dl_Title_Title::select failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		$intCnt = $arrOutput['data'][0]['cnt'];
		$arrRet = array(
			'list' => $arrList,
			'page' => array(
				'total_count' => $intCnt,
				'current_pn'  => $intPn,
				'total_pn'    => ceil($intCnt / $intRn),
			),
		);
		return self::_succRet($arrRet);
		
	}
    /**
     * @param [type] $arrInput [description]
     * @return
     */
    public static function listAllClaimedForumTitleByUid($arrInput){
        
        $arrOutput = Dl_Title_Title::select($arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call Dl_Title_Title::select failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrList = $arrOutput['data'];
        $arrRet = array('list' => $arrList);

        return self::_succRet($arrRet);
    }
    /**
     * @param [type] $arrInput [description]
     * @return
     */
    public static function listForumTitleCntByUid($arrInput){
        $arrOutput = Dl_Title_Title::selectCnt($arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call Dl_Title_Title::select failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $intClaimedCnt = $arrOutput['data'][0]['cnt'];

        $arrInput['result'] =array( self::RESULT_REFUSE ,self::RESULT_PASS );
        $arrOutput = Dl_Title_Title::selectCnt($arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call Dl_Title_Title::select failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $intCheckedCnt = $arrOutput['data'][0]['cnt'];
        $arrRet = array(
                'num' => array(
                    'num_claimed'  => $intClaimedCnt,
                    'num_checked'  => $intCheckedCnt,
                    ),
                );
        return self::_succRet($arrRet);
    }


    /**
     * @param [type] $arrInput [description]
     * @return
     */
    public static function titleClaimed($arrInput){
        if(!isset($arrInput['op_user_id']) || $arrInput['op_user_id'] <=0){
            Bingo_Log::warning("param error input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        
        $arrInput['result'] =self::RESULT_CLAIM;
        $arrOutput = Dl_Title_Title::selectCnt($arrInput);
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call Dl_Title_Title::select failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $intClaimCnt = $arrOutput['data'][0]['cnt'];
        if($intClaimCnt >= self::MAX_CLAIM_CNT ){
            //认领满了不能认领了
            Bingo_Log::warning("claim cnt >=100 num=".$intClaimCnt."op_user_id = ".$arrInput['op_user_id']);
            return self::_succRet();
        }
        
        $arrInput2['result']=self::RESULT_WAIT;
        $arrInput2['orderby'] = array(
                      'field' => 'apply_id',
                      'sort' => 'ASC',
                     );
        $arrInput2['rn'] =self::MAX_CLAIM_CNT - $intClaimCnt;  
        $arrList = Dl_Title_Title::select($arrInput2);
        if(false === $arrList || Tieba_Errcode::ERR_SUCCESS !== $arrList['errno']){
            Bingo_Log::warning(sprintf("call Dl_Title_Title::select failed input[%s] output[%s]", serialize($arrInput2), serialize($arrList)));
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrDbInput['result'] = self::RESULT_CLAIM;
        $arrDbInput['op_user_id'] = (int)$arrInput['op_user_id'];
        foreach($arrList['data'] as $value){
            $arrDbInput['apply_id'] = $value['apply_id'];
            $arrOutput = Dl_Title_Title::update($arrDbInput);
            if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
                Bingo_Log::warning(sprintf("call Dl_Title_Title::update failed input[%s] output[%s]", serialize($arrDbInput), serialize($arrOutput)));
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
        }
        return self::_succRet();
    }
    /**
	 * @param [type] $arrInput [description]
	 * @return
	 */
	public static function auditTitle($arrInput){
		
		if(!isset($arrInput['apply_id']) || $arrInput['apply_id'] <=0 
		|| !isset($arrInput['forum_id'])  
		|| !isset($arrInput['forum_name']) 
		|| !isset($arrInput['op_user_id']) || $arrInput['op_user_id'] <=0 
		|| !isset($arrInput['op_user_name'])|| "" === $arrInput['op_user_name']
		|| !isset($arrInput['forum_titles'])|| count($arrInput['forum_titles']) <= 0){
			Bingo_Log::warning("param error input[".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$intApplyId = (int)$arrInput['apply_id'];
		$intFid = (int)$arrInput['forum_id'];
		$strFname = $arrInput['forum_name'];
		$intOpUid = (int)$arrInput['op_user_id'];
		$strOpUname = $arrInput['op_user_name'];
		$intResult = $arrInput['result'];
		$arrTitles = $arrInput['forum_titles'];
		
		$arrDbInput = array(
			'apply_id' => $intApplyId,
		);
		$arrOutput = Dl_Title_Title::select($arrDbInput);
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
			Bingo_Log::warning(sprintf("call Dl_Title_Title::select failed input[%s] output[%s]", serialize($arrDbInput), serialize($arrOutput)));
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		if(empty($arrOutput['data'])){
			Bingo_Log::warning('no such apply input['.serialize($arrInput).']');
			//偷懒不上错误码
			return self::_errRet(Tieba_Errcode::ERROR_MIS_WORDLIST_KEY_NOT_EXISTS);
		}

		$arrApply = $arrOutput['data'][0];
		$tmp = unserialize($arrApply['ext']);
       // Bingo_Log::warning(print_r($tmp,1));
        $arrExt=array();
        foreach($tmp['title'] as $key => $value){
         //    Bingo_Log::warning($key);
            $key= addslashes($key);
         //    Bingo_Log::warning($key);
            $key=str_replace(',','，',$key);
            $arrExt['title'][$key]=$value;
        }
        
        //$arrTitles =addslashes($arrTitles);
        //Bingo_Log::warning(print_r($arrExt,1));
        //Bingo_Log::warning(print_r($arrTitles,1));
		$arrExt['title'] = array_merge($arrExt['title'], $arrTitles);
        //Bingo_Log::warning(print_r($arrExt['title'],1));
		$arrDbInput['apply_id'] = $arrApply['apply_id'];
		$arrDbInput['ext'] = serialize($arrExt);
		$arrDbInput['result'] = $intResult;
		$arrDbInput['op_user_id'] = $intOpUid;
		$arrDbInput['op_user_name'] = $strOpUname;
		$arrDbInput['op_time'] = time();
		$arrOutput = Dl_Title_Title::update($arrDbInput);
		if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
			Bingo_Log::warning(sprintf("call Dl_Title_Title::select failed input[%s] output[%s]", serialize($arrDbInput), serialize($arrOutput)));
			return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
		}
		if((self::RESULT_REFUSE == $intResult)&&( $intFid!=0) && (!empty($strFname))){

			$arrNmqInput = array(
				'forum_id'   => $intFid,
				'forum_name' => $strFname,
				'user_name'  => $strOpUname,
				'user_id'    => $intOpUid,
				'title_id'   => 0,
				'is_user_define' => 0,
				'grade'      => array(),
			);
			$arrOutput = self::setForumTitle($arrNmqInput);
			if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
				Bingo_Log::warning(sprintf("call Dl_Title_Title::select failed input[%s] output[%s]", serialize($arrNmqInput), serialize($arrOutput)));
				return self::_errRet(Tieba_Errcode::ERR_RPC_CALL_FAIL);
			}
		}
		return self::_succRet();
	}
	/**
	 * @param [type] $arrInput [description]
	 * @return
	 */
	public static function addILegalTitle($arrInput){
		if(!isset($arrInput['title'])|| '' === $arrInput['title']
		|| !isset($arrInput['op_user_name'])|| '' === $arrInput['op_user_name']
		|| !isset($arrInput['apply_id']) || $arrInput['apply_id']<=0){
			Bingo_Log::warning("param error input[".serialize($arrInput)."]");
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$strTitle   = $arrInput['title'];
		$strOpUname = $arrInput['op_user_name'];
		$intApplyId = $arrInput['apply_id'];
		$arrInput = array(
            'table_name' => self::WORDLIST,
            'key' => $strTitle,
        );
        $arrOutput = Tieba_Service::call('wordlist', 'queryWLItemDirectly', $arrInput);
        if(false == $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call wordlist::queryWLItemDirectly input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return $arrOutput;
        }
        if(isset($arrOutput['data'][$strTitle]) && !empty($arrOutput['data'][$strTitle])){
        	Bingo_Log::warning($strTitle.' : has been in wordlist');
        	return self::_succRet();
        }

        $strMemo = '吧头衔审核MIS';
        //$strMemo = Bingo_Encode::convert($strMemo, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        $arrInput = array(
            'wl_item_info' => array(
                'key'           => $strTitle,
                'value'         => $intApplyId,
                'op_username'   => $strOpUname,
                'app_name'      => self::WORDLIST_APP,
                'wordlist_name' => self::WORDLIST_NAME,
                'app_token'     => self::APP_TOKEN,
                'expire_time'   => 0,
                'memo'          => $strMemo,
            ),
        );
        // Bingo_Log::warning(print_r($arrInput,1));
        $arrOutput = Tieba_Service::call('wordlist', 'addWLItem', $arrInput);
        if(false == $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call wordlist::addWLItem input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return $arrOutput;
        }
        return self::_succRet();

	}
	/**
     * [checkTitleBlack description]
     * @param  [type] $arrTitles [description]
     * @return [type]            [description]
     */
    public static function checkTitleBlack($arrTitles){
        $arrInput = array(
            'table_name' => self::WORDLIST,
            'key' => $arrTitles,
        );
        $arrOutput = Tieba_Service::call('wordlist', 'queryWLItemDirectly', $arrInput, null,null, 'post', 'php', 'gbk');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call bawu::addForumTitle failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput) ));
            return false;
        }
        $arrBlackTitles = array();
        foreach($arrOutput['data'] as $title => $result){
            if(! is_null($result) ){
                $arrBlackTitles []= $title;
            }
        }
        return $arrBlackTitles;
    }
	/**
     * [_succRet description]
     * @param  [type] $ret [description]
     * @return [type]      [description]
     */
    protected static function _succRet($ret = null){
        $error = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno'  => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'data'   => $ret,
        );
    }

    /**
     * [_errRet description]
     * @param  [type] $errno [description]
     * @return [type]        [description]
     */
    protected static function _errRet($errno, $data = null){
        return array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data'   => $data,
        );
    }

}
