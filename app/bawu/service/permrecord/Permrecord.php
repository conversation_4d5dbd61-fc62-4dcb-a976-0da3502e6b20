<?php

/**
 * author lidingcai
 * date 2013-04-12
 **/
class Service_Permrecord_Permrecord
{

    const SERVICE_NAME = "Service_Permrecord_Permrecord";
    protected static $_conf = null;

    /**
     * @brief init
     * @return: true if success. false if fail.
     **/
    public static function _init() {

        //add init code here. init will be called at every public function beginning.
        //not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
        //init should be recalled for many times.

        if (self::$_conf == null) {
            self::$_conf = Bd_Conf::getConf("/app/bawu/service_permrecord_permrecord");
            if (self::$_conf == false) {
                Bingo_Log::warning("init get conf fail.");
                return false;
            }
        }
        return true;
    }

    protected static $_cmd_setForumManager = 200;
    protected static $_cmd_forceDelForumManager = 201;//ǿ��ɾ��
    protected static $_cmd_unsetForumManager = 206;//������ְ

    public static function permrecord_commit($arrInput) {
        if (!isset ($arrInput['format'])) {
            $arrInput ['format'] = 'mcpack';
        }
        $strFormat = strtolower($arrInput ['format']);
        if ($strFormat !== 'mcpack' && $strFormat !== 'json') {
            Bingo_Log::warning("input params wrong format:$strFormat.");
            return array('status' => 0);
        }
        if (!isset ($arrInput ['data'])) {
            Bingo_Log::warning("input params no data.");
            return array('status' => 0);
        }
        $php_input_data = file_get_contents("php://input");
        $strCmd = substr($php_input_data, 5);
        if (empty($strCmd)) {
            Bingo_Log::warning("input params no data.");
            return array('status' => 0);
        }


        $arrCmd = array();
        if ($strFormat === 'mcpack') {
            $mcpack = mc_pack_text2pack($strCmd);
            $arrCmd = mc_pack_pack2array($mcpack);
        } else {
            $arrCmd = Bingo_String::json2array($strCmd);
        }
        if (!isset ($arrCmd ['command_no'])) {
            Bingo_Log::warning("no command no found.$strCmd");
            return array('status' => 0);
        }
        $intCmd = intval($arrCmd ['command_no']);
        Bingo_Log::pushNotice('input', Bingo_String::array2json($arrCmd));
        $intTransId = 0;
        if (isset ($arrCmd ['trans_id'])) {
            $intTransId = intval($arrCmd ['trans_id']);
        }
        $strEaccKey = "permrecord_$intCmd";
        $objEacc = new Bingo_Cache_Eacc ();
        $intLastTransId = intval($objEacc->get($strEaccKey));
        if ($intTransId !== 0 && $intTransId === $intLastTransId) {
            Bingo_Log::warning("duplicated cmd. just ignore. transid:$intTransId");
            return array('status' => 0);
        }
        Bingo_Log::pushNotice('cmdno', $intCmd);
        switch ($intCmd) {
            case self::$_cmd_setForumManager:
                $arrInput = array(
                    'user_id' => intval($arrCmd['user_id']),
                    'user_name' => strval(isset($arrCmd['username']) ? $arrCmd['username'] : ''),
                    'forum_id' => intval($arrCmd['forum_id']),
                    'forum_name' => strval(isset($arrCmd['word']) ? $arrCmd['word'] : ''),
                    'begin_time' => intval($arrCmd['now_time']),
                    'end_time' => 0,
                    'end_reason' => self::$_cmd_setForumManager,
                    'begin_op_uid' => intval($arrCmd['op_uid']),
                    'begin_op_uname' => strval(isset($arrCmd['cookie_username']) ? $arrCmd['cookie_username'] : ''),
                    'end_op_uid' => 0,
                    'end_op_uname' => '',
                );
                foreach ($arrInput as $strKey => $mixValue) {
                    Bingo_Log::pushNotice($strKey, $mixValue);
                }
                $output = self::getForumManager($arrCmd);
                if (empty($output)) {
                    self::clearManagerCondition($arrCmd);
                }
                $res = Dl_Permrecord_Permrecord::insertPermrecord($arrInput);
                break;
            case self::$_cmd_forceDelForumManager:
            case self::$_cmd_unsetForumManager://�Լ���ְ
                $arrInput = array(
                    'user_id' => intval($arrCmd['user_id']),
                    'user_name' => strval(isset($arrCmd['username']) ? $arrCmd['username'] : ''),
                    'forum_id' => intval($arrCmd['forum_id']),
                    'forum_name' => strval(isset($arrCmd['word']) ? $arrCmd['word'] : ''),
                    'end_time' => intval($arrCmd['now_time']),
                    'end_reason' => $intCmd,
                    'end_op_uid' => strval(isset($arrCmd['op_uid']) ? $arrCmd['op_uid'] : 0),
                    'end_op_uname' => strval(isset($arrCmd['cookie_username']) ? $arrCmd['cookie_username'] : ''),
                );
                foreach ($arrInput as $strKey => $mixValue) {
                    Bingo_Log::pushNotice($strKey, $mixValue);
                }
                // 由于这里只查uid, 不查fid, 因此LastBeginTime 要酱紫处理
                // getForumManagerPermRecord, only check its uid, no fid
                $arrData = self::getForumManagerPermRecord($arrInput);
                if ($arrData['errno'] != 0 || empty($arrData['data'])) {
                    $arrInput['begin_time'] = intval($arrCmd['now_time']);
                    $arrInput['begin_op_uid'] = strval($arrCmd['op_uid']);
                    $arrInput['begin_op_uname'] = strval(isset($arrCmd['cookie_username']) ? $arrCmd['cookie_username'] : '');
                    $res = Dl_Permrecord_Permrecord::insertOfflineInfoToPermRecord($arrInput);
                    Bingo_Log::pushNotice('is_syn', 1);
                } else {
                    $arrUserManagerData = $arrData['data'];
                    $intLastBeginTime = 0;
                    foreach ($arrUserManagerData as $arrRow) {
                        $intForumId = intval($arrRow['forum_id']);
                        $intBeginTime = intval($arrRow['begin_time']);
                        $intEndTime = intval($arrRow['end_time']);
                        $intEndReason = intval($arrRow['end_reason']);
                        if ($intForumId == $arrInput['forum_id'] && $intEndTime == 0 && $intBeginTime > $intLastBeginTime
                            && $intEndReason == self::$_cmd_setForumManager
                        ) {
                            $intLastBeginTime = $intBeginTime;
                        }
                    }
                    if ($intLastBeginTime == 0) {
                        $arrInput['begin_time'] = 0;//intval($arrCmd['now_time']);
                        $arrInput['begin_op_uid'] = 2016;//strval($arrCmd['op_uid']);
                        $arrInput['begin_op_uname'] = 'none';//strval(isset($arrCmd['cookie_username']) ? $arrCmd['cookie_username'] : '');
                        $res = Dl_Permrecord_Permrecord::insertOfflineInfoToPermRecord($arrInput);
                        Bingo_Log::pushNotice('is_syn', 0);
                        Bingo_Log::warning('not forum manager ~ in old standard');
                        //$res = false;
                    } else {
                        $arrInput['last_begin_time'] = $intLastBeginTime;
                        $res = Dl_Permrecord_Permrecord::updatePermrecord($arrInput);
                    }
                }
                break;
            default:
                Bingo_Log::warning("other cmd received.cmd:$intCmd");
                $res = true;
        }
        if ($res !== false && $intTransId !== 0) {
            $objEacc->set($strEaccKey, $intTransId);
        }
        if ($res !== false) {
            return array('errno' => 0, 'errmsg' => 'success');
        } else {
            header('HTTP/1.1 500 DIY retry');
            Bingo_Log::fatal(sprintf("dl return false input[%s] output[%s]",serialize($arrInput),serialize($res)));
        }
        return $res;
    }

    /**
     * [getForumManagerPermRecord description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getForumManagerPermRecord($arrInput) {
        
        $res = Dl_Permrecord_Permrecord::select($arrInput);
        if ($res !== false) {
            return array('errno' => 0, 'errmsg' => 'success', 'data' => $res['data']);
        } else {
            return array('errno' => -1, 'errmsg' => 'select database error!');
        }
    }
    /**
     * [getForumManagerPermRecordCount description]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getForumManagerPermRecordCount($arrInput) {
        
        $res = Dl_Permrecord_Permrecord::selectCnt($arrInput);
        if ($res !== false) {
            return array('errno' => 0, 'errmsg' => 'success', 'data' => $res['data']);
        } else {
            return array('errno' => -1, 'errmsg' => 'select database error!');
        }
    }

    /**
     * [getForumManager 该吧是否有吧主]
     * <AUTHOR> <[<email address>]>
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getForumManager($arrInput) {
        $arrParam = array(
            'forum_id'      => intval($arrInput['forum_id']),
        );
        $arrOutput = Tieba_Service::call('perm', 'getManagerList', $arrInput, null, null, 'post', 'php', 'gbk');
        if (false === $arrOutput || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('call managerapply:getUserApplyInfo failed, input: [' . serialize($arrInput) . '],output: ['.serialize($arrOutput) . ']');
        }
        return $arrOutput['output'];
    }

    /**
     * [clearManagerCondition 清空上一任吧主设置的条件]
     * <AUTHOR> <[<email address>]>
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function clearManagerCondition($arrInput) {
        $arrParam = array(
            'forum_id'      => intval($arrInput['forum_id']),
            'user_post_num' => 0,
            'member_days'   => 0,
        );
        $arrOutput = Tieba_Service::call('managerapply', 'clearCondition', $arrInput, null, null, 'post', 'php', 'gbk');
        if (false === $arrOutput || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning('call managerapply:clearCondition failed, input: [' . serialize($arrInput) . '],output: ['.serialize($arrOutput) . ']');
        }
        return $arrOutput['output'];
    }

}
