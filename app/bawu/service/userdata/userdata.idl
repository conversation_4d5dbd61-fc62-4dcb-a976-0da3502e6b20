#ifndef __USERDATA_IDL__
#define __USERDATA_IDL__

struct get_userdata_output{
				
    	uint32_t  id; //编号
    	string  date; //日期
    	uint32_t  forum_id;//吧id
    	uint32_t  user_id;//用户id
    	uint32_t  role_id;//角色
    	uint32_t  user_total_sign_num;//吧务全端的签到数
    	uint32_t  user_total_thread_num;//吧务全端的发主题数
    	uint32_t  user_total_post_num;//吧务全端的发帖数
    	uint32_t  user_total_del_post_num;//吧务全端的删帖数
    	uint32_t  user_client_sign_num;//吧务客户端的签到数
    	uint32_t  user_client_thread_num;//吧务客户端的发主题数
    	uint32_t  user_client_post_num;//吧务客户端的发帖数
    	uint32_t  user_client_del_post_num;//吧务客户端的删帖数

    			 
};

service userdata{

/**
         * @brief  : 获取信息
         * @param [in] 	   days :  uint32_t  :  时间
         * @param [in] 	   forum_id :  uint32_t  :  吧id
		 * @param [out]    output   : out get_userdata_output :  输出信息
		**/
	void getUserDataExcel(uint32_t forum_id, uint32_t days,out get_userdata_output output);

		/**
         * @brief  : 获取信息
         * @param [in] 	   date :  string  :  时间
         * @param [in] 	   forum_id :  uint32_t  :  吧id
         * @param [in] 	   user_id :  uint32_t  :  用户id
         * @param [in] 	   pn :  uint32_t  :  页码
		 * @param [in]	   size  :  uint32_t  :  一页的个数
		 * @param [out]    output   : out get_userdata_output :  输出信息
		**/
	void getUserData(string date, uint32_t forum_id, uint32_t user_id, uint32_t pn, uint32_t size,out get_userdata_output output);
};
#endif