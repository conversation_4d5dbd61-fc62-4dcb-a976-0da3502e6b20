<?php

class Service_Datacenter_Datacenter
{
    public function getForumDaysData($input)
    {
        if (!isset($input['forum_id']) || !isset($input['start_time']) || !isset($input['end_time'])) {
            Bingo_log::warning("param error ,input is " . serialize($input));
            return self::errorRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrReq = array(
            'function' => 'getForumDaysData',
            'forum_id' => $input['forum_id'],
            'start_date' => date("Ymd", $input['start_time']),
            'end_date' => date("Ymd", $input['end_time']),
        );

        $arrRes = Dl_Datacenter_Datacenter::execSql($arrReq);
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning("getForumScopeData exesql fail. input[" . serialize($arrReq) . "]");
            return self::errorRet(Tieba_Errcode::ERR_DB_QUERY_FAIL);
        }
        return self::successRet($arrRes['results'][0]);
    }

    /**
     * 成功时返回结构
     */
    protected static function successRet($data = array())
    {
        return array(
            'errno' => Tieba_Errcode::ERR_SUCCESS,
            'errmsg' =>  Tieba_Error::getErrmsg(Tieba_Errcode::ERR_SUCCESS),
            'data' => $data,
        );
    }

    /**
     * 失败错误时返回
     */
    protected static function errorRet($errno, $msg = '')
    {
        return array(
            'errno' => $errno,
            'errmsg' => $msg == '' ? Tieba_Error::getErrmsg($errno) : $msg,
        );
    }
}
