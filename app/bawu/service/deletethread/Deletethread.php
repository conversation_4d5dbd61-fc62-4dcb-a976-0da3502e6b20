<?php
/**
 * Created by PhpStorm.
 * User: guojian09
 * Date: 2020-11-09
 * Time: 16:03
 */
class Service_DeleteThread_DeleteThread{
    private static function _succRet($ret) {
        $error = Tieba_Errcode::ERR_SUCCESS;
        return array (
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg ( $error ),
            'ret' => $ret
        );
    }
    public static function createDeleteThread($arrInput) {
        $dlRet = Dl_Postappeal_DeleteThread::createDeleteThread ( $arrInput );
        return self::_succRet($dlRet);
    }

    public static function updateDeleteThread($arrInput) {
        //1 合理 0 不合理
        if('0' == $arrInput['callback_state']){
            $arrInput['callback_state'] = '2';
        }else if('1' == $arrInput['callback_state']){
            $arrInput['callback_state'] = '1';
        }
        $dlRet = Dl_Postappeal_DeleteThread::updateDeleteThread ( $arrInput );
        return self::_succRet($dlRet);
    }
}