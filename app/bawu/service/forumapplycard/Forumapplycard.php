<?php
/**
 * Created by PhpStorm.
 * User: zgw2014
 * Date: 2018/12/10
 * Time: 20：16
 */
class Service_Forumapplycard_Forumapplycard
{
    /**
     * 错误号
     */
    const ERR_SUCCESS = 0;
    const ERR_AUTHORIZE_FAILED = 1; //不是大吧主或PM
    const ERR_FAILURE_1 = 2; //已经有申请正在审核中
    const ERR_FAILURE_2 = 3; //已经有申请正在审核中
    const ERR_FAILURE_3 = 4; //距上次申请失败不足1个月
    const ERR_INVALIDE_PARAM = 5;
    const ERR_SERVICE_FAILURE = 6;
    private static $_errmsgs = array (
        self::ERR_SUCCESS => '提交完成',
        self::ERR_AUTHORIZE_FAILED => '您没有修改吧名片的权限',
        self::ERR_FAILURE_1 => '您提交的吧名片已经在审核了',
        self::ERR_FAILURE_2 => '上次提交的吧名片还未满三个月哦',
        self::ERR_FAILURE_3 => '一天只能提交一次哦',
        self::ERR_INVALIDE_PARAM => '参数错误',
        self::ERR_SERVICE_FAILURE => '服务异常' );

    /**
     * 吧名片状态
     */
    const EXAMINING = 0;
    const SUCCESS = 1;
    const FAILURE = 2;
    public static function checkParams($pageInput) {
        if(!isset($pageInput['forum_id']) || !is_numeric($pageInput['forum_id']))
        {
            Bingo_Page::assign ( 'ret', self::ERR_INVALIDE_PARAM );
            Bingo_Page::assign ( 'msg', self::$_errmsgs[self::ERR_INVALIDE_PARAM] );
            Bingo_Page::setOnlyDataType ( "json" );
            Bingo_Http_Response::contextType ( 'application/json' );
            Bingo_Log::warning ( "fcard check params error!" );
            return false;
        }
        return true;
    }
    public static function forumCardbuild($pageInput) {
        $forumId = (int)$pageInput ['forum_id'];
        $param   = array('forum_id' => $forumId);
        $res     = Service_Card_Card::getApplicationStatus($param);
//        $res = Tieba_Service::call ( 'bawu', 'getApplicationStatus', $param );
        Bingo_Log::warning( "call bawu-getApplicationStatus.[input_arr:" . json_encode ( $param ) . "][res:" . json_encode ( $res ) . "]" );

        if ($res ['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning ( "talk to card service failed." );
            $ret ['errno'] = self::ERR_SERVICE_FAILURE;
            $ret ['errmsg'] = self::$_errmsgs [self::ERR_SERVICE_FAILURE];
            return $ret;
        }

        $res = $res ['status'];
        $time = date ( "Y-m-d H:i:s", $res ['examine_time'] );
        if ($res ['status'] === NULL) {
            $ret ['errno'] = self::ERR_SUCCESS;
            $ret ['errmsg'] = self::$_errmsgs [self::ERR_SUCCESS];
        }
        else if ($res ['status'] == self::EXAMINING) {
            $ret ['errno'] = self::ERR_FAILURE_1;
            $ret ['errmsg'] = self::$_errmsgs [self::ERR_FAILURE_1];
        }
        else if ($res ['status'] == self::SUCCESS && time () - $res ['examine_time'] <= 3 * 30 * 86400) {
            $ret ['errno'] = self::ERR_FAILURE_2;
            $ret ['errmsg'] = self::$_errmsgs [self::ERR_FAILURE_2];
            $ret ['time'] = $time;
        }
        else if ($res ['status'] == self::FAILURE && time () - $res ['examine_time'] <= 86400) {
            $ret ['errno'] = self::ERR_FAILURE_3;
            $ret ['errmsg'] = self::$_errmsgs [self::ERR_FAILURE_3];
            $ret ['time'] = $time;
        }
        else {
            $ret ['errno'] = self::ERR_SUCCESS;
            $ret ['errmsg'] = self::$_errmsgs [self::ERR_SUCCESS];
        }

        return $ret;
    }
}