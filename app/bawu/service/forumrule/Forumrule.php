<?php
/**
 * <AUTHOR>
 * @date 2020/11/4
 */

class Service_Forumrule_Forumrule {
    const AUDIT_STATUS_UNAUDITED = 1;
    const AUDIT_STATUS_NOPASS = 2;
    const AUDIT_STATUS_PASS = 3;

    const RULE_ITEM_STATUS_VALID = 0;
    const RULE_ITEM_STATUS_ILLEGAL = 1;
    const RULE_ITEM_STATUS_DELETE = 2;

    // 吧规子条例最大数量
    const MAX_RULE_COUNT = 30;

    const REDIS_PID = 'bawu';
    // 吧规草稿key：prefix+forum_id
    const REDIS_KEY_RULE_DRAFT = 'forum_rule_draft_';
    // 吧规草稿修改lock：prefix+forum_id
    const REDIS_KEY_RULE_DRAFT_LOCK = 'forum_rule_draft_lock_';
    // 吧规缓存key prefix+fid+type+need_rule_detail+structured_content
    const REDIS_KEY_RULE_CACHE = 'forum_rule_cache_%d_%s_%d_%d';
    // 缓存大小阈值50KB，超过的话不走缓存
    const CACHE_SIZE_LIMIT = 51200;

    // 获取吧规审核记录，通过吧id拿到最近的一条审核记录
    public static function getAuditForumRule($arrInput) {
        if (empty($arrInput['forum_id'])) {
            Bingo_Log::warning("getAuditForumRule param error. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $needRuleDetail = intval($arrInput['need_rule_detail']);
        $isStructureContent = intval($arrInput['structured_content']);

        $strCacheKey = sprintf(self::REDIS_KEY_RULE_CACHE, $arrInput['forum_id'], 'audit', $needRuleDetail, $isStructureContent);
        $arrCacheResult = self::_getCache($strCacheKey);
        if ($arrCacheResult !== null) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrCacheResult);
        }

        try {
            $recentAuditRule = self::_getRecentAuditRule($arrInput['forum_id']);
        } catch (Exception $e) {
            return self::_errRet($e->getCode());
        }
        if (empty($recentAuditRule)) {
            // 避免缓存穿透
            self::_setCache($strCacheKey, array());
            Bingo_Log::warning('getAuditForumRule result empty. input['.$arrInput.']');
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }
        if ($needRuleDetail) {
            try {
                $arrRule = self::_getForumRuleText($recentAuditRule['id'], $isStructureContent);
            } catch (Exception $e) {
                return self::_errRet($e->getCode());
            }
            //v12.4，默认4条吧规可编辑，pm@zhangguangting, rd@yangtianzheng
            //旧版本默认吧规由端上写死，因此没有提交4条默认吧规
            //新版本默认吧规可编辑，因此要求server下发全部吧规
            //如果由旧版本提交，新版本浏览，将没有前4条默认吧规
            //此处兜底展示4条默认吧规
            $strCommitClientVersion = $recentAuditRule['client_version'];
            $strClientVersion = $arrInput['client_version'];
            if( ( empty($strCommitClientVersion) || //为空，即本次修改上线前提交
                (Molib_Util_Version::compare($strCommitClientVersion, '12.4.0') > 0) // <12.4旧版本提交
                ) && 
                ( 
                (Molib_Util_Version::compare('12.4.0', $strClientVersion) >= 0) // >=12.4新版本访问
                )
            ){
                $arrDefaultRule[] = array(
                    'title' => "一.禁违法信息",
                    'content' => "严禁发布违禁品、毒品、管制刀具、赌博、假证假票据、售卖考试答案、售卖个人信息相关的违法信息。",
                );
                $arrDefaultRule[] = array(
                    'title' => "二.禁色情低俗",
                    'content' => "1.严禁发布色情交易、不正当交友信息。<br/>2.严禁发布直接或隐晦表现人体性部位，暗示性行为的图片。<br/>3.严禁使用美女性感图片、性相关图片作为镇楼图。<br/>4.严禁发布带有侵犯个人隐私性质的偷拍走光漏点人体部位的图片。<br/>5.严禁发布表示对未满14周岁儿童产生性幻想、性冲动的内容。",
                );
                $arrDefaultRule[] = array(
                    'title' => "三.禁不友善言论",
                    'content' => "1.严禁以不文明语言对他人进行攻击、谩骂，或者进行负面评价。<br/>2.严禁攻击、抹黑与自身文化不同的地域、民族、国家。<br/>3.严禁不以正常交流为目的，通过恶意反复@、回复、私信等方式骚扰他人。<br/>4.严禁以不友好的方式激怒他人，迫使对方对自己的言论做出回应，故意挑起争端。",
                );
                $arrDefaultRule[] = array(
                    'title' => "四.禁恶意刷屏、垃圾广告",
                    'content' => "1.严禁在非吧主指定位置用无意义的内容恶意刷屏，影响他人浏览的行为。<br/>2.严禁在非吧主指定位置散布垃圾广告、交易信息，影响他人浏览的行为。",
                );
                foreach($arrDefaultRule as &$val){
                    $val['content'] = $isStructureContent ? Lib_ForumRule::buildStructuredContent($val['content']) : $val['content'];
                }
                unset($val);
                $arrRule = array_merge($arrDefaultRule, $arrRule);
            }
        }
        try {
            $forumRule = self::_getForumRuleByIdOrFid(0, $arrInput['forum_id']);
        } catch (Exception $e) {
            return self::_errRet($e->getCode());
        }
        $arrResult = array(
            'publish_time' => date('Y.m.d', $recentAuditRule['update_time']),
            'title' => $recentAuditRule['title'],
            'preface' => $recentAuditRule['preface'],
            'audit_status' => $recentAuditRule['status'] == self::AUDIT_STATUS_PASS ? 0 : intval($recentAuditRule['status']),
            'audit_opinion' => $recentAuditRule['opinion'],
            'has_forum_rule' => intval(!empty($forumRule)),
            'bazhu_uid' => $recentAuditRule['bazhu_uid'],
        );
        if ($needRuleDetail) {
            $arrResult['rules'] = $arrRule;
        }

        // 最长情况下，吧规所有内容加起来能达到2w多个汉字，为了避免大key，超过设置的阈值大小不再走缓存
        if (strlen(serialize($arrResult)) < self::CACHE_SIZE_LIMIT) {
            self::_setCache($strCacheKey, $arrResult);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrResult);
    }

    // 获取吧规详情，查询参数可以是吧id或吧规id，这里的审核状态恒为0
    public static function getForumRuleDetails($arrInput) {
        if (empty($arrInput['forum_rule_id']) && empty($arrInput['forum_id'])) {
            Bingo_Log::warning("getForumRule param error. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $needRuleDetail = intval($arrInput['need_rule_detail']);
        $isStructureContent = intval($arrInput['structured_content']);

        $strCacheKey = sprintf(self::REDIS_KEY_RULE_CACHE, $arrInput['forum_id'], 'passed', $needRuleDetail, $isStructureContent);
        $arrCacheResult = self::_getCache($strCacheKey);
        if ($arrCacheResult !== null) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrCacheResult);
        }

        try {
            $forumRule = self::_getForumRuleByIdOrFid($arrInput['forum_rule_id'], $arrInput['forum_id']);
        } catch (Exception $e) {
            Bingo_Log::warning($e->getMessage().". input[".serialize($arrInput)."]");
            return self::_errRet($e->getCode());
        }
        if (empty($forumRule)) {
            // 避免缓存穿透
            self::_setCache($strCacheKey, array());
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }

        if ($needRuleDetail) {
            try {
                $arrRule = self::_getForumRuleText($forumRule['audit_id'], $isStructureContent);
            } catch (Exception $e) {
                return self::_errRet($e->getCode());
            }
            //v12.4，默认4条吧规可编辑，pm@zhangguangting, rd@yangtianzheng
            //旧版本默认吧规由端上写死，因此没有提交4条默认吧规
            //新版本默认吧规可编辑，因此要求server下发全部吧规
            //如果由旧版本提交，新版本浏览，将没有前4条默认吧规
            //此处兜底展示4条默认吧规
            $auditRecord = self::_getAuditRecord(intval($forumRule['audit_id']));
            $strCommitClientVersion = $auditRecord['client_version'];
            $strClientVersion = $arrInput['client_version'];
            if( ( empty($strCommitClientVersion) || //为空，即本次修改上线前提交
                (Molib_Util_Version::compare($strCommitClientVersion, '12.4.0') > 0) // <12.4旧版本提交
                ) && 
                ( 
                (Molib_Util_Version::compare('12.4.0', $strClientVersion) >= 0) // >=12.4新版本访问
                )
            ){
                $arrDefaultRule[] = array(
                    'title' => "一.禁违法信息",
                    'content' => "严禁发布违禁品、毒品、管制刀具、赌博、假证假票据、售卖考试答案、售卖个人信息相关的违法信息。",
                );
                $arrDefaultRule[] = array(
                    'title' => "二.禁色情低俗",
                    'content' => "1.严禁发布色情交易、不正当交友信息。<br/>2.严禁发布直接或隐晦表现人体性部位，暗示性行为的图片。<br/>3.严禁使用美女性感图片、性相关图片作为镇楼图。<br/>4.严禁发布带有侵犯个人隐私性质的偷拍走光漏点人体部位的图片。<br/>5.严禁发布表示对未满14周岁儿童产生性幻想、性冲动的内容。",
                );
                $arrDefaultRule[] = array(
                    'title' => "三.禁不友善言论",
                    'content' => "1.严禁以不文明语言对他人进行攻击、谩骂，或者进行负面评价。<br/>2.严禁攻击、抹黑与自身文化不同的地域、民族、国家。<br/>3.严禁不以正常交流为目的，通过恶意反复@、回复、私信等方式骚扰他人。<br/>4.严禁以不友好的方式激怒他人，迫使对方对自己的言论做出回应，故意挑起争端。",
                );
                $arrDefaultRule[] = array(
                    'title' => "四.禁恶意刷屏、垃圾广告",
                    'content' => "1.严禁在非吧主指定位置用无意义的内容恶意刷屏，影响他人浏览的行为。<br/>2.严禁在非吧主指定位置散布垃圾广告、交易信息，影响他人浏览的行为。",
                );
                foreach($arrDefaultRule as &$val){
                    $val['content'] = $isStructureContent ? Lib_ForumRule::buildStructuredContent($val['content']) : $val['content'];
                }
                unset($val);
                $arrRule = array_merge($arrDefaultRule, $arrRule);
            }
            
        }
        $arrResult = array(
            'forum_rule_id' => $forumRule['id'],
            'publish_time' => date('Y.m.d', $forumRule['update_time']),
            'title' => $forumRule['title'],
            'preface' => $forumRule['preface'],
            'audit_status' => 0,
            'audit_opinion' => '',
            'has_forum_rule' => 1,
            'bazhu_uid' => $forumRule['bazhu_uid'],
        );
        if ($needRuleDetail) {
            $arrResult['rules'] = $arrRule;
        }

        // 最长情况下，吧规所有内容加起来能达到2w多个汉字，为了避免大key，超过设置的阈值大小不再走缓存
        if (strlen(serialize($arrResult)) < self::CACHE_SIZE_LIMIT) {
            self::_setCache($strCacheKey, $arrResult);
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrResult);
    }

    private static function _getCache($key) {
        $redisInput = array(
            'key' => $key,
        );
        $arrOut = Lib_Redis::call('GET', $redisInput, self::REDIS_PID);
        if (Tieba_Errcode::ERR_SUCCESS != $arrOut['err_no']) {
            Bingo_Log::warning("redis-get fail. key[$key]");
            // 出现错误当做缓存未设置，走db
            return null;
        }
        if ($arrOut['ret'][$key] === null) {
            return null;
        }
        $result = unserialize($arrOut['ret'][$key]);
        return $result ? $result : array();
    }

    private static function _setCache($key, $val) {
        $val = serialize($val);
        $redisInput = array(
            'key' => $key,
            'value' => $val,
            'seconds' => 300,
        );
        $arrOut = Lib_Redis::call('SETEX', $redisInput, self::REDIS_PID);
        if (Tieba_Errcode::ERR_SUCCESS != $arrOut['err_no']) {
            Bingo_Log::warning("redis-setex fail. key[$key]");
            return false;
        }
        return true;
    }

    private static function _clearCache($fid, $type) {
        $redisInput['reqs'] = array(
            // 后面0，1代表是否需要吧规详细列表和是否结构化文本
            array('key' => sprintf(self::REDIS_KEY_RULE_CACHE, $fid, $type, 0, 0)),
            array('key' => sprintf(self::REDIS_KEY_RULE_CACHE, $fid, $type, 0, 1)),
            array('key' => sprintf(self::REDIS_KEY_RULE_CACHE, $fid, $type, 1, 0)),
            array('key' => sprintf(self::REDIS_KEY_RULE_CACHE, $fid, $type, 1, 1)),
        );
        $arrOut = Lib_Redis::call('DEL', $redisInput, self::REDIS_PID);
        if (Tieba_Errcode::ERR_SUCCESS != $arrOut['err_no']) {
            Bingo_Log::warning(sprintf("redis-del fail. key[%s]", serialize($redisInput)));
            return false;
        }
        return true;
    }

    private static function _saveDraft($arrInput) {
        if (empty($arrInput['forum_id']) || empty($arrInput['uid'])) {
            Bingo_Log::warning("_saveDraft param error. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        try {
            // 只有吧主有权限操作吧规
            if (!self::_hasPerm($arrInput['forum_id'], $arrInput['uid'])) {
                Bingo_Log::warning("editForumRule user no perm. input[".serialize($arrInput)."]");
                return self::_errRet(Tieba_Errcode::ERR_USER_NO_PERM);
            }
        } catch (Exception $e) {
            return self::_errRet($e->getCode());
        }
        $arrDraftItem = array();
        $arrDraftItem[] = Bingo_String::array2json(array(
            'title' => strval($arrInput['title']),
            'preface' => strval($arrInput['preface']),
            'client_version' => strval($arrInput['client_version']),
        ), Bingo_Encode::ENCODE_UTF8);

        if (is_array($arrInput['rules'])) {
            foreach ($arrInput['rules'] as $rule) {
                $arrDraftItem[] = Bingo_String::array2json(array(
                    'title' => strval($rule['title']),
                    'content' => Lib_ForumRule::rewriteRuleContent(strval($rule['content'])),
                ), Bingo_Encode::ENCODE_UTF8);
            }
        }

        $lockKey = self::REDIS_KEY_RULE_DRAFT_LOCK . $arrInput['forum_id'];
        if (!self::_lock($lockKey, 5)) {
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $key = self::REDIS_KEY_RULE_DRAFT . $arrInput['forum_id'];
        if (!self::_clearDraft($key)) {
            self::_unlock($lockKey);
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrArgument = array(
            'key' => $key,
            'value' => $arrDraftItem,
        );
        $arrOutput = Lib_Redis::call('RPUSH', $arrArgument, self::REDIS_PID);
        self::_unlock($lockKey);
        if ($arrOutput === false || $arrOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call redis error. input:". serialize($arrArgument) . "output:" . serialize($arrOutput));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    private static function _clearDraft($key) {
        $arrArgument = array(
            'key' => $key,
        );
        $arrOutput = Lib_Redis::call('DEL', $arrArgument, self::REDIS_PID);
        if ($arrOutput === false || $arrOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call redis error. input:". serialize($arrArgument) . "output:" . serialize($arrOutput));
            return false;
        }
        return true;
    }

    public static function getForumRuleDraft($arrInput) {
        if (empty($arrInput['forum_id']) || empty($arrInput['uid'])) {
            Bingo_Log::warning("_saveDraft param error. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $isStructureContent = intval($arrInput['structured_content']);
        try {
            // 只有吧主有权限操作吧规
            if (!self::_hasPerm($arrInput['forum_id'], $arrInput['uid'])) {
                Bingo_Log::warning("editForumRule user no perm. input[".serialize($arrInput)."]");
                return self::_errRet(Tieba_Errcode::ERR_USER_NO_PERM);
            }
        } catch (Exception $e) {
            return self::_errRet($e->getCode());
        }

        $key = self::REDIS_KEY_RULE_DRAFT . $arrInput['forum_id'];
        $arrArgument = array(
            'key' => $key,
            'start' => 0,
            'stop' => -1,
        );
        $arrOutput = Lib_Redis::call('LRANGE', $arrArgument, self::REDIS_PID);
        if ($arrOutput === false || $arrOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call redis error. input:". serialize($arrArgument) . "output:" . serialize($arrOutput));
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }

        $arrEncodedDraft = $arrOutput['ret'][$key];
        if (empty($arrEncodedDraft)) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS, array());
        }

        $titleSegment = array_shift($arrEncodedDraft);
        $titleSegment = Bingo_String::json2array($titleSegment, Bingo_Encode::ENCODE_UTF8);
        $arrRule = array();
        //v12.4，默认4条吧规可编辑，pm@zhangguangting, rd@yangtianzheng
        //旧版本默认吧规由端上写死，因此没有提交4条默认吧规
        //新版本默认吧规可编辑，因此要求server下发全部吧规
        //如果由旧版本提交，新版本浏览，将没有前4条默认吧规
        //此处兜底展示4条默认吧规
        $strCommitClientVersion = $titleSegment['client_version'];
        $strClientVersion = $arrInput['client_version'];
        if( ( empty($strCommitClientVersion) || //为空，即本次修改上线前提交
              (Molib_Util_Version::compare($strCommitClientVersion, '12.4.0') > 0) // <12.4旧版本提交
            ) && 
            ( 
              (Molib_Util_Version::compare('12.4.0', $strClientVersion) >= 0) // >=12.4新版本访问
            )
        ){
            $arrRule[] = array(
                'title' => "一.禁违法信息",
                'content' => "严禁发布违禁品、毒品、管制刀具、赌博、假证假票据、售卖考试答案、售卖个人信息相关的违法信息。",
            );
            $arrRule[] = array(
                'title' => "二.禁色情低俗",
                'content' => "1.严禁发布色情交易、不正当交友信息。<br/>2.严禁发布直接或隐晦表现人体性部位，暗示性行为的图片。<br/>3.严禁使用美女性感图片、性相关图片作为镇楼图。<br/>4.严禁发布带有侵犯个人隐私性质的偷拍走光漏点人体部位的图片。<br/>5.严禁发布表示对未满14周岁儿童产生性幻想、性冲动的内容。",
            );
            $arrRule[] = array(
                'title' => "三.禁不友善言论",
                'content' => "1.严禁以不文明语言对他人进行攻击、谩骂，或者进行负面评价。<br/>2.严禁攻击、抹黑与自身文化不同的地域、民族、国家。<br/>3.严禁不以正常交流为目的，通过恶意反复@、回复、私信等方式骚扰他人。<br/>4.严禁以不友好的方式激怒他人，迫使对方对自己的言论做出回应，故意挑起争端。",
            );
            $arrRule[] = array(
                'title' => "四.禁恶意刷屏、垃圾广告",
                'content' => "1.严禁在非吧主指定位置用无意义的内容恶意刷屏，影响他人浏览的行为。<br/>2.严禁在非吧主指定位置散布垃圾广告、交易信息，影响他人浏览的行为。",
            );
            foreach($arrRule as &$val){
                $val['content'] = $isStructureContent ? Lib_ForumRule::buildStructuredContent($val['content']) : $val['content'];
            }
            unset($val);
        }
        foreach ($arrEncodedDraft as $val) {
            $rule = Bingo_String::json2array($val, Bingo_Encode::ENCODE_UTF8);
            $arrRule[] = array(
                'title' => $rule['title'],
                'content' => $isStructureContent ? Lib_ForumRule::buildStructuredContent($rule['content']) : $rule['content'],
            );
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, array(
            'title' => $titleSegment['title'],
            'preface' => $titleSegment['preface'],
            'rules' => $arrRule,
        ));
    }

    public static function editForumRule($arrInput) {
        if (intval($arrInput['is_draft'])) {
            return self::_saveDraft($arrInput);
        }
        if (empty($arrInput['forum_id']) || empty($arrInput['uid']) || empty($arrInput['title'])) {
            Bingo_Log::warning("editForumRule param error. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if (is_array($arrInput['rules'])) {
            if (count($arrInput['rules']) > self::MAX_RULE_COUNT) {
                Bingo_Log::warning("editForumRule rules over limit. input[".serialize($arrInput)."]");
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            foreach ($arrInput['rules'] as $rule) {
                if (empty($rule['title']) || empty($rule['content'])) {
                    Bingo_Log::warning("editForumRule rules param error. input[".serialize($arrInput)."]");
                    return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
                }
            }
        }

        try {
            // 只有吧主有权限操作吧规
            if (!self::_hasPerm($arrInput['forum_id'], $arrInput['uid'])) {
                Bingo_Log::warning("editForumRule user no perm. input[".serialize($arrInput)."]");
                return self::_errRet(Tieba_Errcode::ERR_USER_NO_PERM);
            }
        } catch (Exception $e) {
            return self::_errRet($e->getCode());
        }

        $arrRule = array();
        if (is_array($arrInput['rules'])) {
            foreach ($arrInput['rules'] as $rule) {
                $arrRule[] = array(
                    'title' => $rule['title'],
                    'content' => Lib_ForumRule::rewriteRuleContent($rule['content']),
                );
            }
        }

        try {
            $recentAuditRule = self::_getRecentAuditRule($arrInput['forum_id']);
        } catch (Exception $e) {
            return self::_errRet($e->getCode());
        }

        // 前一条吧规正在审核中，在审核结束前禁止更改
        if ($recentAuditRule && intval($recentAuditRule['status']) === self::AUDIT_STATUS_UNAUDITED) {
            Bingo_Log::warning("editForumRule rule unaudited, disable edit. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_FORBID_OPERATION);
        }

        $arrArgument = array(
            'forum_id' => intval($arrInput['forum_id']),
            'title' => $arrInput['title'],
            'preface' => strval($arrInput['preface']),
            'rules' => $arrRule,
            'status' => self::AUDIT_STATUS_UNAUDITED,
            'bazhu_uid' => $arrInput['uid'],
            'client_version' => $arrInput['client_version'],
        );
        try {
            self::_addRuleAuditRecord($arrArgument);
        } catch (Exception $e) {
            Bingo_Log::warning($e->getMessage().". input[".serialize($arrInput)."]");
            return self::_errRet($e->getCode());
        }

        // 提交后要主动清理审核态缓存，否则可能会有短暂的状态不一致，正式吧规缓存没必要清理
        self::_clearCache($arrInput['forum_id'], 'audit');

        $lockKey = self::REDIS_KEY_RULE_DRAFT_LOCK . $arrInput['forum_id'];
        if (!self::_lock($lockKey, 5)) {
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $key = self::REDIS_KEY_RULE_DRAFT . $arrInput['forum_id'];
        if (!self::_clearDraft($key)) {
            self::_unlock($lockKey);
            return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        self::_unlock($lockKey);

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    // 根据审核状态获取审核记录列表，主要用在amis
    public static function getAuditForumRuleList($arrInput) {
	$pn = $arrInput['pn'] ? intval($arrInput['pn']) - 1 : 0;
        $pn = $pn < 0 ? 0 : $pn;
        $rn = $arrInput['rn'] ? intval($arrInput['rn']) : 20;
        $status = $arrInput['status'] ? intval($arrInput['status']) : self::AUDIT_STATUS_UNAUDITED;
 //获取前端的输入并拼串
        $op_uid = $arrInput['op_uid'];
        $forum_id = $arrInput['forum_id'];
        $create_begin_time = $arrInput['create_begin_time'];
        $create_end_time = $arrInput['create_end_time'];
        $audit_begin_time = $arrInput['audit_begin_time'];
        $audit_end_time = $arrInput['audit_end_time'];
        $condition= 'status='.$status;
        if(!empty($forum_id)){
            $condition.=' and forum_id='.$forum_id;
	}
        if(!empty($op_uid)){
             $condition.=' and op_uid='.$op_uid;
	}
        if(!empty($create_begin_time)){
             $condition.=' and create_time>='.$create_begin_time;
	}
        if(!empty($create_end_time)){
             $condition.=' and create_time<='.$create_end_time;
	}
        if(!empty($audit_begin_time)&&$status!=1){
             $condition.=' and update_time>='.$audit_begin_time;
        }
	if(!empty($audit_end_time)&&$status!=1){
             $condition.=' and update_time<='.$audit_end_time;
	}
	$conditionForPage=$condition;
        $condition.=' limit  '.$pn*$rn.' , '.$rn;
        $arrArgumentNew = array(
            'function' => 'selectAuditRecordByCondition',
            'condition' => $condition,
        );

        $arrOutput = Dl_Forumrule_Forumrule::execSql($arrArgumentNew);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning('call db fail. input['.serialize($arrArgumentNew).'] output['.serialize($arrOutput).']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $arrAuditRecord = $arrOutput['results'][0];

//获取页面数      
        $arrArgumentNew = array(
            'function' => 'countAuditRecordByCondition',
            'condition' => $conditionForPage,
        );
        $arrOutput = Dl_Forumrule_Forumrule::execSql($arrArgumentNew);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning('call db fail. input['.serialize($arrArgumentNew).'] output['.serialize($arrOutput).']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        $count = intval($arrOutput['results'][0][0]['count']);

        $arrResult = array(
            'list' => $arrAuditRecord,
            'total_count' => $count,
        );

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrResult);
    }

    // 根据审核记录id获取所属的吧规各条例列表
    public static function getForumRuleText($arrInput) {
        if (empty($arrInput['audit_id'])) {
            Bingo_Log::warning("getForumRuleText param error. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $isStructureContent = intval($arrInput['structured_content']);

        try {
            $arrRule = self::_getForumRuleText($arrInput['audit_id'], $isStructureContent);
        } catch (Exception $e) {
            return self::_errRet($e->getCode());
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, array(
            'rules' => $arrRule,
        ));
    }

    // 吧规审核接口，主要用在amis
    public static function auditForumRule($arrInput) {
        if (empty($arrInput['audit_id']) || empty($arrInput['audit_status']) || empty($arrInput['op_uid'])
            || !in_array(intval($arrInput['audit_status']), array(self::AUDIT_STATUS_PASS, self::AUDIT_STATUS_NOPASS))) {
            Bingo_Log::warning("auditForumRule param error. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $auditOpinion = strval($arrInput['audit_opinion']);
        $arrIllegalTextId = $arrInput['illegal_text_id'] ? explode(',', $arrInput['illegal_text_id']) : array();

        // 如果标记了某些问题吧规，则不可以通过该吧规
        if ($arrIllegalTextId && intval($arrInput['audit_status']) !== self::AUDIT_STATUS_NOPASS) {
            Bingo_Log::warning("auditForumRule param error. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        try {
            $auditRecord = self::_getAuditRecord(intval($arrInput['audit_id']));
            if (intval($auditRecord['status']) !== self::AUDIT_STATUS_UNAUDITED) {
                Bingo_Log::warning("auditForumRule audit op forbidden. input[".serialize($arrInput)."]");
                return self::_errRet(Tieba_Errcode::ERR_FORBID_OPERATION);
            }
            $forumRule = self::_getForumRuleByIdOrFid(0, $auditRecord['forum_id']);
        } catch (Exception $e) {
            Bingo_Log::warning($e->getMessage().". input[".serialize($arrInput)."]");
            return self::_errRet($e->getCode());
        }
        $hasForumRule = !empty($forumRule);

        $db = Dl_Forumrule_Forumrule::getDBInstance();
        if (!$db) {
            Bingo_Log::warning("db connect fail. input[".serialize($arrInput)."]");
            return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
        }
        $db->startTransaction();

        $arrArgument = array(
            'function' => 'updateRuleAuditResult',
            'id' => intval($arrInput['audit_id']),
            'opinion' => $auditOpinion,
            'status' => intval($arrInput['audit_status']),
            'op_uid' => intval($arrInput['op_uid']),
            'update_time' => time(),
        );
        $arrOutput = Dl_Forumrule_Forumrule::execSql($arrArgument);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning(sprintf('call dl fail. input[%s] output[%s]', serialize($arrArgument), serialize($arrOutput)));
            $db->rollback();
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        if ($arrIllegalTextId) {
            $arrArgument = array(
                'function' => 'updateRuleTextStatus',
                'ids' => implode(',', $arrIllegalTextId),
                'status' => self::RULE_ITEM_STATUS_ILLEGAL,
                'update_time' => time(),
            );
            $arrOutput = Dl_Forumrule_Forumrule::execSql($arrArgument);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
                Bingo_Log::warning(sprintf('call dl fail. input[%s] output[%s]', serialize($arrArgument), serialize($arrOutput)));
                $db->rollback();
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
        }

        if (intval($arrInput['audit_status']) === self::AUDIT_STATUS_PASS) {
            $arrArgument = array(
                'forum_id' => $auditRecord['forum_id'],
                'title' => $auditRecord['title'],
                'preface' => $auditRecord['preface'],
                'audit_id' => $auditRecord['id'],
                'bazhu_uid' => $auditRecord['bazhu_uid'],
                'time' => time(),
            );
            // 根据之前是否有值判断insert还是update
            if ($hasForumRule) {
                $arrArgument['function'] = 'updateForumRuleByFid';
            } else {
                $arrArgument['function'] = 'insertForumRule';
            }
            $arrOutput = Dl_Forumrule_Forumrule::execSql($arrArgument);
            if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
                Bingo_Log::warning(sprintf('call dl fail. input[%s] output[%s]', serialize($arrArgument), serialize($arrOutput)));
                $db->rollback();
                return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
            }
        }
        $db->commit();

        $arrArgument = array(
            'forum_id' => intval($auditRecord['forum_id']),
            'msg_id' => strval($auditRecord['id']),
            'type' => 1, // 吧规push类型
        );
        $arrOutput = Tieba_Service::call('bazhutools', 'sendBroadcast', $arrArgument, null, null, 'post', 'jsonraw', 'utf-8');
        if(false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']){
            Bingo_Log::warning(sprintf("call bazhutools::sendBroadcast failed input[%s] output[%s]", serialize($arrArgument), serialize($arrOutput)));
        }

        // 主动清理下缓存，审核态缓存每次都要清理，正式吧规的缓存只有审核通过才清理
        self::_clearCache($arrInput['forum_id'], 'audit');
        if (intval($arrInput['audit_status']) === self::AUDIT_STATUS_PASS) {
            self::_clearCache($arrInput['forum_id'], 'passed');
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    private static function _addRuleAuditRecord($arrInput) {
        $db = Dl_Forumrule_Forumrule::getDBInstance();
        if (!$db) {
            throw new Exception('db connect fail', Tieba_Errcode::ERR_DB_CONN_FAIL);
        }

        $db->startTransaction();
        $arrArgument = array(
            'function' => 'insertRuleAuditRecord',
            'forum_id' => $arrInput['forum_id'],
            'title' => $arrInput['title'],
            'preface' => $arrInput['preface'],
            'status' => $arrInput['status'],
            'bazhu_uid' => $arrInput['bazhu_uid'],
            'time' => time(),
            'client_version' => $arrInput['client_version'],
        );
        $arrOutput = Dl_Forumrule_Forumrule::execSql($arrArgument);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning('call db fail. input['.serialize($arrArgument).'] output['.serialize($arrOutput).']');
            $db->rollback();
            throw new Exception("call dl fail", Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        if (!empty($arrInput['rules'])) {
            $auditId = $db->getInsertID();
            $boolRes = self::_processRuleText($auditId, $arrInput['rules']);
            if (!$boolRes) {
                $db->rollback();
                return;
            }
        }
        $db->commit();
    }

    private static function _processRuleText($auditId, $rules) {
        if (empty($rules)) {
            return true;
        }
        $time = time();
        foreach ($rules as $rule) {
            $arrRecord[] = "($auditId,'{$rule['title']}','{$rule['content']}','',0,$time,$time)";
        }
        $arrArgument = array(
            'function' => 'batchInsertRuleText',
            'records' => implode(',', $arrRecord),
        );
        $arrOutput = Dl_Forumrule_Forumrule::execSql($arrArgument);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning('call db fail. input['.serialize($arrArgument).'] output['.serialize($arrOutput).']');
            return false;
        }
        return true;
    }

    // 获取吧最新的一条吧规审核表的记录
    private static function _getRecentAuditRule($forumId) {
        $arrArgument = array(
            'function' => 'getRecentAuditRecord',
            'forum_id' => $forumId,
        );
        $arrOutput = Dl_Forumrule_Forumrule::execSql($arrArgument);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning('call db fail. input['.serialize($arrArgument).'] output['.serialize($arrOutput).']');
            throw new Exception("call dl fail", Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return $arrOutput['results'][0][0];
    }

    private static function _getForumRuleByIdOrFid($id = 0, $fid = 0) {
        if (empty($id) && empty($fid)) {
            return array();
        }
        if ($id) {
            $arrArgument = array(
                'function' => 'getForumRule',
                'id' => intval($id),
            );
        } else {
            $arrArgument = array(
                'function' => 'getForumRuleByFid',
                'forum_id' => intval($fid),
            );
        }
        $arrOutput = Dl_Forumrule_Forumrule::execSql($arrArgument);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning(sprintf('call dl fail. input[%s] output[%s]', serialize($arrArgument), serialize($arrOutput)));
            throw new Exception("call dl fail", Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return $arrOutput['results'][0][0];
    }

    private static function _getAuditRecord($id) {
        $arrArgument = array(
            'function' => 'getAuditRecord',
            'id' => $id,
        );
        $arrOutput = Dl_Forumrule_Forumrule::execSql($arrArgument);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning(sprintf('call dl fail. input[%s] output[%s]', serialize($arrArgument), serialize($arrOutput)));
            throw new Exception("call dl fail", Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return $arrOutput['results'][0][0];
    }

    private static function _getForumRuleText($auditId, $isStructureContent) {
        $arrArgument = array(
            'function' => 'selectRuleText',
            'audit_id' => intval($auditId),
        );
        $arrOutput = Dl_Forumrule_Forumrule::execSql($arrArgument);
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning('call db fail. input['.serialize($arrArgument).'] output['.serialize($arrOutput).']');
            throw new Exception('call dl fail', Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return is_array($arrOutput['results'][0]) ? self::_formatRules($arrOutput['results'][0], $isStructureContent) : array();
    }

    private static function _formatRules($rules, $isStructureContent) {
        $result = array();
        foreach ($rules as $rule) {
            $result[] = array(
                'title' => $rule['title'],
                'content' => $isStructureContent ? Lib_ForumRule::buildStructuredContent($rule['content']) : $rule['content'],
                'status' => intval($rule['status']),
                'rule_item_id' => intval($rule['id']),
                'extra' => $rule['extra'] ? Bingo_String::json2array($rule['extra'], Bingo_Encode::ENCODE_UTF8) : '',
            );
        }
        return $result;
    }

    private static function _hasPerm($forumId, $uid) {
        $arrArgument = array(
            'forum_id' => $forumId,
            'user_id' => $uid,
            'user_ip'  => 0,
        );
        $arrOutput = Tieba_Service::call('perm', 'getPerm', $arrArgument, null, null, 'post', 'php', 'utf-8');
        if (!isset($arrOutput['errno']) || $arrOutput['errno'] != Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call perm::getPerm fail. input['.serialize($arrArgument).'] output['.serialize($arrOutput).']');
            throw new Exception("call perm fail", Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
        }
        $arrPerm = $arrOutput['output']['perm'];
        return $arrPerm['can_type2_audit_post'] && $arrPerm['can_add_manager_team'];
    }

    private static function _errRet($errno,$data = null){
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data' => $data,
        );
    }

    private static function _lock($key, $secTimeout = 5) {
        $arrInput = array(
            'key' => $key,
            'value' => 1,
        );
        $output = Lib_Redis::call('SETNX', $arrInput, self::REDIS_PID);
        if (empty($output['ret'][$key])) {
            Bingo_Log::warning("call redis-setnx fail. input[$key] output[".serialize($output).']');
            return false;
        }
        $arrInput = array(
            'key' => $key,
            'seconds' => intval($secTimeout),
        );
        $output = Lib_Redis::call('EXPIRE', $arrInput, self::REDIS_PID);
        if (empty($output['ret'][$key])) {
            Bingo_Log::warning("call redis-expire fail. input[{$key}] output[".serialize($output).']');
        }
        return true;
    }

    private static function _unlock($key) {
        $arrInput = array(
            'key' => $key,
        );
        $output = Lib_Redis::call('DEL', $arrInput, self::REDIS_PID);
        if (empty($output['ret'][$key])) {
            Bingo_Log::warning("call redis-del fail. input[{$key}] output[".serialize($output).']');
            return false;
        }
        return true;
    }
}
