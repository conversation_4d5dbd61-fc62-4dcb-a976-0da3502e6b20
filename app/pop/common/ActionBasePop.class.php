<?php
/**
 * @file 
 * <AUTHOR>
 * @date 2012-05-17
 * @version
 * @brief 
 * @description  action�Ļ��࣬��������action��Ҫ��ͨ�÷����ͱ���
 **/
 class ActionBasePop extends Bingo_Action_Abstract {

	//�����
	public  static $intErrno = 0;
	
	//������Ϣ
	public static $strErrmsg = '';
	
	//������
	public static $strCmd = '';
	
	//������Ϣ
	public static $arrCwl = array(); 
	
	//�û���Ϣ
	public static $arrUserInfo = array();
	
	//����
	public static $strFromForum = '';
	
	//��id
	public static $intFid = '';
	
	//�����������0 pb��1 frs��2 itb
	public static $intPosition = 0;
	
	//frs��Ϣ
	public $arrFrsInfo = array();
	
	//fdir��Ϣ
	public static $arrFdirInfo = array();
		
	//member��Ϣ
	public $arrMemberInfo = array();
		
	//grade��Ϣ
	public $arrGradeInfo = array();
	
	//���������б�
	public $arrTaskList = array();
	
	//�������
	public $arrPopResult = array();
	
	//���ظ�ǰ�˵ı�׼���
	public $arrStdResult = array();
	
	//���url���������
	public $arrFormNeed = array();
	
	//���url���������
	public $arrForm = array();
	
	//�����Ӧʱ��
	public static $arrRspTime = array();
	public function execute() {
		return true;
	}
	
	public  static function getUserInfo() {
		//return self::$arrUserInfo;
		return self::$arrUserInfo;
	}
	
	public  static function getCwlInfo() {
		return self::$arrCwl;
	}
	
	public  static function getFromForum() {
		return self::$strFromForum;
	}
	
	public  static function getFromForumId() {
		return self::$intFid;
	}
	
	public  static function getPosition() {
		return self::$intPosition;
	}
	
	public  static function getFdirInfo() {
		return self::$arrFdirInfo;
	}
	
	public  function getStrCmd() {
		return self::$strCmd;
	}
	
	public  function setFromForumId($intFromForumId) {
		self::$intFid = $intFromForumId;
	}
	
	//���ճ�ʼ���������ݣ��������
    public function initForm(){
		if (!isset($this->arrFormNeed) || empty($this->arrFormNeed)){
			return true;
		}
		//ѭ���������б���
        foreach ($this->arrFormNeed as $strParamKey => $arrKeyConf){
            $mixVal = Bingo_Http_Request::get($strParamKey);
            if (false === $mixVal){
                if ( isset($arrKeyConf['need']) && $arrKeyConf['need'] == true){
					self::setError(Errno::NO_PARAMETER, Errmsg::NO_PARAMETER);
					$strLog = self::warningLogStr(" commit var=$strParamKey not provide , not post value ");
					Bingo_Log::warning($strLog, LOG_TYPE_SUBMIT);
                    return false;
                }else {
                    $mixDefault = isset($arrKeyConf['default'])?$arrKeyConf['default']:'';
                    $arrFormKey = isset($arrKeyConf['key'])?$arrKeyConf['key']:$strParamKey;
                    $this->arrForm[$arrFormKey] = $mixDefault;                    
                }
            }else{
                $arrFormKey = isset($arrKeyConf['key'])?$arrKeyConf['key']:$strParamKey;
                $this->arrForm[$arrFormKey] = $mixVal;
            }
        }
        return true;
    }
	
	//���ɱ�׼��������
	public function genResult($intErrno=NULL, $arrRet=array(), $strMsg=''){
		$arrStdRet = array(
			"no"		=> ($intErrno === NULL || $intErrno === '') ? self::$intErrno : $intErrno,
			"data"		=> $arrRet,
			"error"		=> ($strMsg === NULL || $strMsg === '') ? self::$strErrmsg : $strMsg,
		);
		return $arrStdRet;
	}

	//�Ƿ��д�����
	public function isError(){
		return (self::$intErrno > 0)? true : false;
	}
	
	//���ô���
	public static function setError($intErrno, $strErrmsg)
	{
		self::$intErrno = $intErrno;
		self::$strErrmsg = $strErrmsg;
	}
	
	//���ģ����ʾ
    public function renderResult($arrRet){
        $strAlt = Bingo_Http_Request::get('alt', '');
		if ($strAlt == ''){
			$strAlt = PopGlobalConfig::DEFAULT_TPL_TYPE;
		}
		$strTpl = 'index.php';
		$arrResult = self::toUtf8($arrRet);
        switch ($strAlt) {
			case 'debug':
				print_r($arrResult);
				break;
			case 'html':
			case 'xml':
        		self::render($arrResult, $strTpl);
				break;
        	case 'php':
        	    echo serialize($arrResult);
        		break;
        	case 'json':
        	default:
				echo json_encode($arrResult);
        		break;
        }
    } 
	
	//����ģ��
	public function render($arrVar, $strTpl) {        		
		$objView = new Bingo_View(array(
            'baseDir' => ROOT_PATH . '/views/pop',
        ));
        
        //add for avoid xss..
        $objView->setXssSafe(true);       
        $objView->assign($arrVar);       
        $objView->render($strTpl);
	}
	
	//GBKת���UTF8
	public function toUtf8($strCont){
		if ('' === $strCont){
			return $strCont;
		}
		return Bingo_Encode::convert($strCont, 'UTF-8', 'GBK');
	}

	//UTF8ת���GBK
	public function toGbk($strCont){
		if ('' === $strCont){
			return $strCont;
		}
		return Bingo_Encode::convert($strCont, 'GBK', 'UTF-8');
	}
	
 	//Warning��־��ӡ
	public static function warningLogStr($strExt = ''){
		$strLog = sprintf(" cmd=%s errno=%s errmsg=%s",
				self::$strCmd, self::$intErrno, self::$strErrmsg);
		//����ǵ�¼�û�
		if (1 === self::$arrUserInfo['login']) {
			$strUserInfo = sprintf(" uid=%s uname=%s",
				self::$arrUserInfo['uid'], self::$arrUserInfo['uname']);
		}
		//����Ƿǵ�¼�û�
		else {
			$strUserInfo = sprintf(" baiduid=%s",	self::$arrUserInfo['baiduid']);
		}
		$strLog = "DATA[" . $strLog . $strUserInfo . " ]" . $strExt;
		return $strLog;
	}

    //Notice��־��ӡ
	public function noticeLogStr($strExt = ''){
		$strLog = sprintf(" cmd=%s", self::$strCmd);
		//����ǵ�¼�û�
		if (1 === self::$arrUserInfo['login']) {
			$strUserInfo = sprintf(" uid=%s uname=%s",
				self::$arrUserInfo['uid'], self::$arrUserInfo['uname']);
		}
		//����Ƿǵ�¼�û�
		else {
			$strUserInfo = sprintf(" baiduid=%s",	self::$arrUserInfo['baiduid']);
		}
		$strLog = "DATA[" . $strLog . $strUserInfo . " ]" . $strExt;
		return $strLog;
	}

	//Debug��־��ӡ
	public function debugLogStr($strExt = ''){
		$strLog = sprintf(" cmd=%s", self::$strCmd);
		//����ǵ�¼�û�
		if (1 === self::$arrUserInfo['login']) {
			$strUserInfo = sprintf(" uid=%s uname=%s",
				self::$arrUserInfo['uid'], self::$arrUserInfo['uname']);
		}
		//����Ƿǵ�¼�û�
		else {
			$strUserInfo = sprintf(" baiduid=%s",	self::$arrUserInfo['baiduid']);
		}
		$strLog = "DATA[" . $strLog . $strUserInfo . " ]" . $strExt;
		return $strLog;
	}
	
    //ͳ��ר����־��ӡ
	public function statLogStr($strExt = ''){
		$strLog = sprintf(" cmd=%s", self::$strCmd);
		//����ǵ�¼�û�
		if (1 === self::$arrUserInfo['login']) {
			$strUserInfo = sprintf(" uid=%s uname=%s",
				self::$arrUserInfo['uid'], self::$arrUserInfo['uname']);
		}
		//����Ƿǵ�¼�û�
		else {
			$strUserInfo = sprintf(" baiduid=%s",	self::$arrUserInfo['baiduid']);
		}
		$strLog = "STAT[POP" . $strLog . $strUserInfo . $strExt . " ]" ;
		return $strLog;
	}
	
	//����Ƿ���post����
    public function checkIfPostMethod(){
		$strMethod = strtolower(strip_tags($_SERVER['REQUEST_METHOD']));
        if ($strMethod != 'post') { 
            self::setError(Errno::MOTHOD_NOT_POST, Errmsg::MOTHOD_NOT_POST);
			$strLog = self::warningLogStr(" the method is not post ");
			Bingo_Log::warning($strLog, LOG_TYPE_SUBMIT);
            return false;
        }
        return true;
    }
    
    //��ʼ������Ӧʱ��
	public static function time_start($strAction) {
        self::$arrRspTime[$strAction]['start'] = gettimeofday();
    }

    //����������Ӧʱ��
    public static function time_end($strAction) {
        self::$arrRspTime[$strAction]['end'] = gettimeofday();
    }
    
    //�����Ӧʱ��
    public static function get_ussecond($action) {
        $intElapseTime = self::$arrRspTime[$action];
        $intUsGone = ($intElapseTime['end']['sec'] - $intElapseTime['start']['sec']) * 1000000 +
                 $intElapseTime['end']['usec'] - $intElapseTime['start']['usec'];
        return $intUsGone;
    }
}
