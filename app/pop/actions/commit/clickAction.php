<?php

/**
 * @file 
 * <AUTHOR>
 * @date 2012-05-24
 * @version
 * @brief 
 * @description  �����رյ���
 **/

class clickAction extends ActionBasePop {
	public function execute() {
		self::mainProcess();
   		if (self::isError()) {
			$this->arrStdResult = self::genResult();
		}
		else {
			$this->arrStdResult = self::genResult(Errno::SUCCES, $this->arrPopResult, Errmsg::SUCCES);
		}
        self::renderResult($this->arrStdResult);
        self::statisticsLog();
	}
	
	public function mainProcess() {
		//����ܿ����ѹرգ�ֱ�ӷ��ؿս����ǰ��
		if (false === PopGlobalConfig::POP_IS_OPEN) {
			$this->arrPopResult = '';
			return true;
		}
		
		self::initProcess();
		if (self::isError()) {
			return false;
		}
		self::$arrUserInfo = DataPass::getSession();
        //��������cache
        $arrVal = array();
        DataRedis::setCache('con', $arrVal);
		//����ǵ�¼�û�
		if (self::$arrUserInfo['login'] > 0) {
			return self::handleLoginTask();
		}
		//����Ƿǵ�¼�û�
		else {
			return self::handleNoLoginTask();
		}
		return true;
	}
	
	//��ʼ������������
	public function initProcess() {
		self::$strCmd = 'click';		
        $this->arrFormNeed = array(
        	//����id
            'task_id' => array(
                'key' => 'task_id',
                'need' => true,
                'default' => 0,
            ),
        	//1 �رա�2 ���
            'type' => array(
                'key' => 'type',
                'need' => true,
                'default' => 1,
            ),
        	//�ѵ�������
            'count' => array(
                'key' => 'count',
                'need' => true,
                'default' => 1,
            ),
        );
		self::checkIfPostMethod();
		if (self::isError()) {
			return false;
		}
        self::initForm();
        return true;
	}
	
	//ɨβ��������ͳ����־
	public function statisticsLog(){
		$strLog = " is_stat=1 task_id=". $this->arrForm['task_id'] ." click_type=".
        	$this->arrForm['type'] ." errno=" . $this->intErrno . " used_time=".self::get_ussecond(self::$strCmd);
		$strLog .= " ".Bingo_Timer::toString();
        $strLog = self::statLogStr($strLog);
		Bingo_Log::notice($strLog, LOG_TYPE_SUBMIT);
		
		//��ӡ��·��־��ͳ����
		// ��־��ʼ���ֶ� Ĭ�� ispv Ϊ 1 ���� pv ���ھ���� action ����Ϊ 0
		Tieba_Stlog::setFileName('feye-stat');
		Tieba_Stlog::addNode('pro', 'tieba');
		Tieba_Stlog::addNode('mid', 'pop');
		Tieba_Stlog::addNode('ispv', 0);
		Tieba_Stlog::addNode("cmd",self::$strCmd);
		//����ǵ�¼�û�
		if (1 === self::$arrUserInfo['login']) {
		    Tieba_Stlog::addNode("uid",self::$arrUserInfo['uid']);
		    Tieba_Stlog::addNode("uname",self::$arrUserInfo['uname']);
		}
		//����Ƿǵ�¼�û�
		else {
		    Tieba_Stlog::addNode("baiduid",self::$arrUserInfo['baiduid']);
		}
		//task_id
        Tieba_Stlog::addNode("task_id",$this->arrForm['task_id']);
		//����ţ�0Ϊ�ɹ�
		Tieba_Stlog::addNode('errno', $this->intErrno);
		Tieba_Stlog::addNode("click_type",$this->arrForm['type']);
		//������ʱ��
		Tieba_Stlog::addNode('total_time', self::get_ussecond(self::$strCmd));
		
		Tieba_Stlog::notice();
		
	}
	
	//�����¼�û��ĵ���
	public function handleLoginTask() {
		//�������ݿ�	
		$arrDalUpdInput = array(
        	'task_id' => $this->arrForm['task_id'],
        	'uid'	  => self::$arrUserInfo['uid'],
        	'flag'    => $this->arrForm['type'],    		
        );
        return DataMysql::updateLoginUserFlagToDb($arrDalUpdInput);
	}
	
	//����ǵ�¼�û��ĵ���
	public function handleNoLoginTask() {   	
		$arrKey = array(
	    	'baiduid' 	=> self::$arrUserInfo['baiduid'],
	        'task_id'	=> $this->arrForm['task_id'],
	    );
		//���cache���˲�����Ҫ��Ϊ�˻�õ�����������ʡ��  
	    //$arrRes = DataRedis::getNologinInfoCache($arrKey);
	    
		//���cache
		DataRedis::delNologinInfoCache($arrKey);
		//��������cache	   
        $arrVal = array(
        	'flag' 	=> $this->arrForm['type'],
        	'count'	=> $this->arrForm['count'],
        	//'count'	=> $arrRes['count'],
        );
        DataRedis::setNologinInfoCache($arrKey, $arrVal);
		return true;
	}
}
