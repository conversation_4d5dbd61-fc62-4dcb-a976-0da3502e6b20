<?php

/**
 * @file 
 * <AUTHOR>
 * @date 2012-05-24
 * @version
 * @brief 
 * @description  ����cupid
 **/

class DataCupid {
	
	//��cupid������еĵ�������
	public function getCupid($forum_id) {
		$strMod = 'cupid';
		//�������cache
		//$arrCache = DataMemcache::checkDataCache($strMod);
		//����cache
		//if (1 === $arrCache['is_cache']) {
		//	return $arrCache['res'];
		//}
		$arrInput = array();
		$arrOutput = array();
		$arrFdirInfo = ActionBasePop::getFdirInfo();
		$strFromForum = ActionBasePop::getFromForum();	
		$arrUserInfo = ActionBasePop::getUserInfo();
		$arrCwl = ActionBasePop::getCwlInfo();
		$arrInput['type'] = 'pop';	//type����Ϊpop
		$arrInput['pop_from_fdir'] =  strval($arrFdirInfo['fdir_level_one']);	//ָ�����ض����൯���������ļ���
		$arrInput['pop_position'] = intval(ActionBasePop::getPosition());       //�������Ե�λ�ã�0 pb��1 frs��2 itieba
		//�������λ�ò���itieba����������������Ǵ��ĸ�������
		if (2 !== $arrInput['pop_position']){
			$arrInput['pop_from_forum'] = strval($strFromForum);	//ָ�����ض��ɵ����������ļ���
		}
		
		//����ǵ�¼�û�
		if ($arrUserInfo['login'] > 0) {
			$arrInput['pop_user_type'] = 1;	//��¼�û�
			$arrInput['pop_uid'] = intval($arrUserInfo['uid']);	//��¼�û�id�������ļ���
			//$arrInput['pop_identity'] = 0;	//0 ������1 С������2 ����û��ʹ��
			//$arrInput['pop_grade'] = 0;	//�����û��ȼ���û��ʹ��
			//$arrInput['pop_at_fdir'] = '';	//�û�����Ŀ¼�������ļ��û��ʹ��
			//$arrInput['pop_at_fname'] = '';	//�û������ɣ������ļ��û��ʹ��
		}
		//����Ƿǵ�¼�û�
		else {
			$arrInput['pop_user_type'] = 0;	//δ��¼�û�
			$arrInput['pop_province'] = $arrCwl['province'];	//ʡ
			$arrInput['pop_city'] = $arrCwl['city'];	//��
			$arrInput['pop_is_verify'] = 0;	//�Ƿ�ע��δ��֤
		}
		ActionBasePop::time_start($strMod);
		$bolCupidRs = Rpc::rpcCall('cupid_pop', 'query', $arrInput, $arrOutput);
		ActionBasePop::time_end($strMod);
        //�������cupid�Ľ��Ϊfalse��Ϊ��
        if ((false === $bolCupidRs) || ! isset($arrOutput) || empty($arrOutput)) {
			$intUsedTime = ActionBasePop::get_ussecond($strMod);
			ActionBasePop::setError(Errno::RPC_CUPID_ERR, Errmsg::RPC_CUPID_ERR);
			$strLog = ActionBasePop::warningLogStr(" err[cupid false] used_time[$intUsedTime us]");
			Bingo_Log::warning($strLog, LOG_TYPE_UI);
			return false;
        }
        $arrInput=array('forum_id'=>$forum_id);
        $arrRes=Tieba_Service::call('forum','getForumAttr',$arrInput);
        if($arrRes==false||$arrRes['errno']!=0) {
            Bingo_Log::warning("get ForumAttr in pop failed");
            return false;
        }
        if(!empty($arrRes['output'])) {
            foreach ($arrRes['output'] as $key =>$value) {
                $arrForumAttr[]=$key;
            }
        }
		$arrFilterForums=array();
		$arrFilterUids=array();
		$handleWordServer = Wordserver_Wordlist::factory();
		$strTableName = 'tb_wordlist_redis_pop_filter_forums_uids';
		$arrInput = array('fid','uid','attr');
		$arrItemInfo = $handleWordServer->getValueByKeys($arrInput,$strTableName);
		foreach($arrInput as $value) {
			if($value=='fid') {
				$arrFilterForums= unserialize($arrItemInfo[$value]);
			}
			if($value=='uid') {
				$arrFilterUids=unserialize($arrItemInfo[$value]);
			}
            if($value=='attr') {
                $arrFilterAttrs=unserialize($arrItemInfo[$value]);
            }
		}
        if(!empty($arrFilterAttrs)&&!empty($arrForumAttr)) {
            foreach( $arrFilterAttrs as $value) {
                if(in_array($value,$arrForumAttr)) {
                    return false;
                }
            }
        }
		if(!empty($arrFilterForums)||!empty($arrFilterUids)) {
			if(in_array($forum_id,$arrFilterForums)|| in_array($arrUserInfo['uid'],$arrFilterUids)) {
			return false;
			}
		}
        //���û�������κ���������������cache
        //��������������򲻴�cache����Ϊ�ᵼ�µ������󣬱���ֻ��frs��ȴ��pbҲ����
        //if ((!isset($arrOutput['res'][0]['task_id'])) || ($arrOutput['res'][0]['task_id'] < 0)) {
        //	DataMemcache::setCache('data', $arrOutput, $strMod);
        //}
        return $arrOutput;
	}
	
}
