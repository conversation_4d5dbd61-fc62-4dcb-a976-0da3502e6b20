<?php
/**
 * @author:song<PERSON><PERSON>an@baidu.çom
 * @desc:请求GRC
 */
class Libs_Base_Grc {
    const SERVER_NAME = "feed_tieba_grc";

    public $service_ie = 'utf-8';
    protected $params = array();
    protected $rankConf = array();
    protected $videoReqNum;
    protected $threadReqNum;
    protected $totalResNum;
    protected $resHotDataNum = 10;
    protected $stopCallGrc = false;

    /**
     *grc=>yuelaou
     */
    protected static $commonStruct = array(
        'cuid' => 'cuid',
        'uid' => 'user_id',
        'sid' => 'format_sids_str',
        'reqnum' => 'req_num',
    );

    protected static $deviceStruct = array(
        'network' => 'net_type',
        'app_version' => 'client_version',
        'osbranch' => 'client_type',
        'ua' => 'call_from_ua',
    );

    protected static $refreshStruct = array(
        'refresh_count' => 'req_times_num',
    );

    protected static $integerField = array(
        'refresh_count' => 1,
        'reqnum' => 1,
    );

    /**
     * Libs_Base_Grc constructor.
     * @param $rankConf
     * @param $params
     * @param string $ie
     */
    public function __construct($rankConf, $params, $ie = 'utf-8'){
        $this->rankConf = $rankConf;
        $this->params = $params;
        $this->service_ie = $ie;

        $this->videoReqNum = isset($params['video_req_num']) ? intval($params['video_req_num']) : 0;
        $this->threadReqNum = isset($params['thread_req_num']) ? intval($params['thread_req_num']) : 0;
        $this->reddot = isset($params['reddot']) ? intval($params['reddot']) : false;
        $this->need_reason = isset($params['need_reason']) ? intval($params['need_reason']) : false;

        //过滤后数据会减少，rank返回数量是否扩大比例
        if(isset($this->rankConf['ResNumRate']) && $this->rankConf['ResNumRate'] > 0){
            $rate = intval($this->rankConf['ResNumRate']) ;
            $this->videoReqNum = ceil($this->videoReqNum * $rate / 100);
            $this->threadReqNum = ceil($this->threadReqNum * $rate / 100);
        }
        $this->totalResNum = $this->videoReqNum + $this->threadReqNum;
        if(isset($this->rankConf['StopRankSwitchParam']) && isset($this->rankConf['StopRankSwitchValue'])
            && isset($this->params[$this->rankConf['StopRankSwitchParam']])
            && $this->rankConf['StopRankSwitchValue'] == $this->params[$this->rankConf['StopRankSwitchParam']]){
            $this->stopCallGrc = true;
        }
    }

    /**
     * @param $callBackData
     * @param $filterTids
     * @param array $personCacheData
     * @return array|mixed
     */
    public function process($callBackData, $filterTids, $personCacheData = array()) {
        if($this->stopCallGrc) {
            return $this->_get_hotdata($callBackData, $filterTids);
        }
        $grcInput = $this->_buildGrcReq($this->params);
        ral_set_logid(REQUEST_ID);
        ral_set_pathinfo("/feed.grc.GRCService/query");
        $strGrcInput = json_encode($grcInput);
        $strGrcInput = str_replace('"##', '', $strGrcInput);
        $strGrcInput = str_replace('##"', '', $strGrcInput);
        Bingo_Timer::start("tieba_grc");
        $strGrcOutput = ral(self::SERVER_NAME, 'post', $strGrcInput, rand());
        Bingo_Timer::end("tieba_grc");
        if (false === $strGrcOutput) {
            //call_grc_service_fail 监控字段
            if (count($personCacheData) >= $this->totalResNum) {
                Bingo_Log::pushNotice('use_person_cache', 1);
                return array_slice($personCacheData, 0, $this->totalResNum);
            }
            Bingo_Log::fatal(__FUNCTION__ . " call_grc_service_fail!output is false.output=" .serialize($strGrcOutput));
            return array_slice($callBackData, 0, $this->totalResNum);
        }
        //TODO：如果是有视频的资源，目前的返回含有GBK，需要修改GRC。
        $grcOutput = json_decode($strGrcOutput, true);
        if (false === $grcOutput) {
            //call_grc_service_fail 监控字段
            Bingo_Log::fatal(__FUNCTION__ . " call_grc_service_fail!output is not json.output=" .serialize($grcOutput));
            if (count($personCacheData) >= $this->totalResNum) {
                Bingo_Log::pushNotice('use_person_cache', 1);
                return array_slice($personCacheData, 0, $this->totalResNum);
            }
            return array_slice($callBackData, 0, $this->totalResNum);
        }
        if ($grcOutput['error_msg'] != "success") {
            //call_gr_service_fail 监控字段
            Bingo_Log::fatal(__FUNCTION__ . " call_grc_service_fail!output is not json.output=" .serialize($grcOutput));
            return array_slice($callBackData, 0, $this->totalResNum);
        }
        $result = array();
        if (!empty($grcOutput['content'])) {
            foreach ($grcOutput['content'] as $arrContent) {
                $arrItems = $arrContent['items'];
                if (empty($arrItems)) {
                    continue;
                }
                foreach ($arrItems as $item) {
                    $thread = array();
                    $thread['nid'] = $item['id_str'];
                    $thread['tid'] = $item['tid'];
                    $thread['fid'] = $item['fid'];
                    $thread['sign'] = $item['sign'];
                    if (isset($item['image_signs'])) {
                        $thread['image_signs'] = $item['image_signs'];
                    }
                    $thread['source'] = $item['display_strategy']['type'];
                    if (isset($item['p_params']) && !empty($item['p_params'])) {
                        $arrParams = json_decode($item['p_params'], true);
                        if (isset($arrParams['sec_cate'])) {
                            $thread['sec_cate'] = $arrParams['sec_cate'];
                        }
                        if (isset($arrParams['weight'])) {
                            $thread['weight'] = $arrParams['weight'];
                        }
                        if (isset($arrParams['bjh_thread_type']) && !empty($arrParams['bjh_thread_type'])) {
                            $thread['bjh_thread_type'] = $arrParams['bjh_thread_type'];
                        }
                        if (isset($arrParams['real_nid']) && !empty($arrParams['real_nid'])) {
                            $thread['nid'] = $arrParams['real_nid'];
                        }
                        if (isset($arrParams['vid']) && !empty($arrParams['vid'])) {
                            $thread['vid'] = $arrParams['vid'];
                        }
                        if(isset($arrParams['client_extra']) && !empty($arrParams['client_extra'])){
                            $thread['client_extra'] = $arrParams['client_extra'];
                        }
                        if (isset($arrParams['real_ctr'])) {
                            $thread['real_ctr'] = $arrParams['real_ctr'];
                        }
                        if(isset($arrParams['recomm_reason']) && !empty($arrParams['recomm_reason'])){
                            //UI有个转码逻辑
                            //reddot场景都是UTF8,不转为GBK
                            if (!$this->reddot && !$this->need_reason && Libs_Const::UA_CLIENT_VIDEO_TAB == $this->params['call_from_ua']) {
                                $reason = $arrParams['recomm_reason'];
                                $reason = Bingo_Encode::convert($reason, Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
                                $thread['recommend_reason']['reason'] = $reason;
                            } else {
                                $thread['recommend_reason']['reason'] = $arrParams['recomm_reason'];
                            }
                        }
                        if(isset($arrParams['recomm_fid'])){
                            $thread['recommend_reason']['fid'] = $arrParams['recomm_fid'];
                        }
                        if ($this->reddot && isset($arrParams['notify_reason']) && !empty($arrParams['notify_reason'])) {
                            $thread['notify_reason'] = $arrParams['notify_reason'];
                        }
                    }


                    if (isset($item['cs']) && !empty($item['cs'])) {
                        $arrCs = json_decode($item['cs'], true);
                        $thread['feature'] = Bingo_Encode::convert($arrCs['feature'], Bingo_Encode::ENCODE_GBK, Bingo_Encode::ENCODE_UTF8);
                    }
                    $thread['type'] = $arrContent['key'] == "news" ? 0 : 1;
                    if (isset($item['display_strategy']['gdata'])) {
                        $thread['extra'] = $item['display_strategy']['gdata'];
                    }
                    $result[] = $thread;
                }
            }
        }
        Bingo_Log::pushNotice('grc_result_cnt', count($result));
        if(empty($result)){
            $result = $this->_get_hotdata($callBackData, $filterTids);
            Bingo_Log::warning(__FUNCTION__ . "grc_service_result_empty!");
        }
        return $result;
    }

    /**
     * @param $arrInput
     * @return array
     */
    private function _buildGrcReq($arrInput) {
        $request = array();
        $commonInfo = array();
        foreach (self::$commonStruct as $key => $index){
            if (isset($arrInput[$index])) {
                if (isset(self::$integerField[$key])) {
                    $commonInfo[$key] = intval($arrInput[$index]);
                } else {
                    $commonInfo[$key] = strval($arrInput[$index]);
                }
            }
        }
        if(is_array($arrInput['mask_keyword']) && !empty($arrInput['mask_keyword'])){
            $commonInfo['mask_keyword'] = $arrInput['mask_keyword'];
        }
        $customPara = array();
        if(isset($arrInput['need_bjh'])){
            $customPara['need_bjh'] = intval($arrInput['need_bjh']);
        }
        if (isset($arrInput['need_person_cache'])) {
            $customPara['need_person_cache'] = intval($arrInput['need_person_cache']);
        }

        if (isset($arrInput['last_tids'])
            && Libs_Const::UA_CLIENT_MIDDLE_PAGE == $this->params['call_from_ua']){
            $customPara['last_tids'] = $arrInput['last_tids'];
        }
        if (isset($arrInput['is_vertical'])
            && Libs_Const::UA_CLIENT_MIDDLE_PAGE == $this->params['call_from_ua']){
            $customPara['is_vertical'] = intval($arrInput['is_vertical']);
        }
        if (isset($arrInput['pn'])
            && Libs_Const::UA_CLIENT_MIDDLE_PAGE == $this->params['call_from_ua']){
            $customPara['pn'] = intval($arrInput['pn']);
        }
        if (isset($arrInput['reddot'])){
            $customPara['reddot'] = intval($arrInput['reddot']);
        }
        if(count($customPara) > 0){
            $commonInfo['custom_para'] = json_encode($customPara, JSON_UNESCAPED_UNICODE);
        }
        $query = array();
        if (isset($arrInput['from_query'])) {
            $query['from_query'] = strval($arrInput['from_query']);
        }
        if (isset($arrInput['from_tid'])) {
            $query['from_tid'] = strval($arrInput['from_tid']);
        }
        // 中间页在grc里用了这个字段，需要传int
        if (Libs_Const::UA_CLIENT_MIDDLE_PAGE == $this->params['call_from_ua'] && isset($arrInput['from_tid'])) {
            $query['from_tid'] = intval($arrInput['from_tid']);
        }
        if (isset($arrInput['first_dir'])) {
            $query['first_dir'] = strval($arrInput['first_dir']);
        }
        if (isset($arrInput['second_dir'])) {
            $query['second_dir'] = strval($arrInput['second_dir']);
        }
        if(count($query) > 0){
            $commonInfo['query'] = json_encode($query, JSON_UNESCAPED_UNICODE);
        }
        if (isset($arrInput['sample_ids'])) {
            $arrSid = explode("-", $arrInput['sample_ids']);
            foreach ($arrSid as $item) {
                $arr = explode("_", $item);
                $commonInfo['smfw_item'][] = array(
                    'id' => strval($arr[0]),
                    'num' => strval($arr[1])
                );
            }
        }
        $commonInfo['thread_reqnum'] = $this->threadReqNum;
        $commonInfo['video_reqnum'] = $this->videoReqNum;
        $commonInfo['log_id'] = REQUEST_ID;
        $request['common_info'] = $commonInfo;

        //device_info
        $deviceInfo = array();
        foreach (self::$deviceStruct as $key => $index){
            if(isset($arrInput[$index])){
                if (isset(self::$integerField[$key])) {
                    $deviceInfo[$key] = intval($arrInput[$index]);
                } else {
                    $deviceInfo[$key] = strval($arrInput[$index]);
                }
            }
        }
        if(count($deviceInfo) > 0){
            $request['device_info'] = $deviceInfo;
        }
        //refresh_info
        $refreshInfo = array();
        foreach (self::$refreshStruct as $key => $index){
            if(isset($arrInput[$index])){
                if (isset(self::$integerField[$key])) {
                    $refreshInfo[$key] = intval($arrInput[$index]);
                } else {
                    $refreshInfo[$key] = strval($arrInput[$index]);
                }
            }
        }
        //history_info
        if(!empty($arrInput['bad_ids'])) {
            $arrTids = explode(',', $arrInput['bad_ids']);
            $nid_list = array();

            foreach ($arrTids as $row) {
                $nid = getSignByTid($row);
                $nid_list[] = array(
                    'nid' => "##" . $nid . "##",
                );
            }
            $request['history_info']['issued_items'] = $nid_list;
        }
        return $request;
    }

    /**
     * @param $callBackData
     * @param $filterTids
     * @return array
     */
    protected function _get_hotdata($callBackData, $filterTids){
        $result = array();
        $total = 0;
        foreach ($callBackData as $row){
            if(!isset($filterTids[$row['tid']])){
                $result[] = $row;
                $total++;
            }
            if($total >= $this->resHotDataNum){
                break;
            }
        }
        return $result;
    }

}