<?php
/**
 * 用户实时浏览历史
 * User: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date: 2017/11/1
 * Time: 下午1:46
 */
class Service_UserHistory_UserHistory {

    const REDIS_FRSLIVE_NAME = 'feedlive';
	const REDIS_NAME = 'recomuserhis';
    const PREFIX_USERHIS_VIDEO = 'user_his_video_';
    const PREFIX_USERHIS_POST  = 'user_his_post_';
    const PREFIX_USERHIS_FRS = 'user_his_frs_';
    const PREFIX_USERHIS_VIDEOPAGE = 'user_his_videopage_';
    const PREFIX_USEHIS_VIDEO_DURATION = 'user_his_video_duration_';
    const PREFIX_USERHIS_TW_POST = 'user_his_post_news_';//图文发帖历史
    const PREFIX_USERHIS_VIDEO_POST = 'user_his_post_video_';//视频发帖历史
    const PREFIX_USERHIS_TW_AGREE = 'user_his_agree_news_';//图文点赞历史
    const PREFIX_USERHIS_VIDEO_AGREE = 'user_his_agree_video_';//视频点赞历史

    const PREFIX_USERHIS_VIDEO_FB = 'user_his_video_fb_'; //用户负反馈历史
    const PREFIX_USERHIS_POST_FB = 'user_his_post_fb_'; //用户负反馈历史

    const PREFIX_USERHIS_USRE_FOLLOW = 'user_his_follow_';//关注人操作历史

    protected static $_redis = null;

    const PREFIX_SESSION_VIDEO = 'user_session_video_';
    const PREFIX_SESSION_NORMAL = 'user_session_';

    const SESSION_REDIS_DEFAULT_EXPIRE = 14400;

    const REDIS_NAME_USER_CTR = 'yuelaouuip';
    const PREFIX_USER_CTR_NORMAL = 'tuwen_inp_';
    const PREFIX_USER_CTR_VIDEO = 'video_tag_';

    const LIVE_CLICK_PREFIX = 'live_click_';
    const LIVE_SHOW_PREFIX = 'live_show_';
    const LIVE_EXPIRE = 86400;
    const LIVE_HISTORY_MAX_NUM = 100; // 直播点击和下发保存得最大数量

    const MASK_FRS_PREFIX = 'mask_frs_'; //frs去重实验
    const MASK_FRS_MAX = 2000;

	public static $fetureType = array(
		1 => 'user_info_real',
		2 => 'user_info',
	);
	/**
	 * 正确的返回
	 * @param $ret
	 * @return array
	 */
	protected  static function _succRet($ret) {
		$errCode = Tieba_Errcode::ERR_SUCCESS;
		$errMsg  = Tieba_Error::getErrmsg($errCode);
		return array(
			'errno' => $errCode,
			'errmsg' => $errMsg,
			'data' => $ret,
		);
	}
	/**
	 * 错误返回值
	 * @param unknown $errno
	 * @return multitype:unknown string
	 */
	protected static function _errRet($errno){
		return array(
			'errno'  => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
		);
	}
	/**
	 * 获取用户实时浏览历史
	 * @param array $arrInput
	 * array(
	 * 	'user_id' => 1,
	 *  'cuid' => '22222'
	 *  'type' => 0, //默认0:取视频+图文、1: 视频历史、2:图文历史, 3:视频中间页
	 * );
	 * @return array(
	 *   data => array(
	 *   	'video_history' => string, //json串
     *      'post_history' => string, //json串
	 * )
	 */
	public static function getUserHistory($arrInput) {
        $uid  = !empty($arrInput['user_id']) ? intval($arrInput['user_id']) : 0;
        $cuid = !empty($arrInput['cuid']) ? $arrInput['cuid'] : 0;
		if(empty($uid) && empty($cuid)) {
			Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$type = isset($arrInput['type']) ? $arrInput['type'] : 0;
		Bingo_Log::pushNotice('type', $type);
        if (!in_array($type, array(0, 1, 2, 3))) {
            Bingo_Log::warning(__FUNCTION__ . " param type is invaliad . input=" . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        if ($uid <= 0) {
            $uid = Libs_Util::convert64fromstr($cuid); //cuid 对应虚拟uid
        }

        $getCont = array(
            'video_history' => self::PREFIX_USERHIS_VIDEO . $uid,
            'post_history'  => self::PREFIX_USERHIS_POST . $uid,
            'video_history_duration' => self::PREFIX_USEHIS_VIDEO_DURATION . $uid,
            'frs_history' => self::PREFIX_USERHIS_FRS . $uid,
            'videopage_history' => self::PREFIX_USERHIS_VIDEOPAGE . $uid,
            'video_history_fb' => self::PREFIX_USERHIS_VIDEO_FB . $uid,
            'post_history_fb' => self::PREFIX_USERHIS_POST_FB . $uid,
            'user_follow_history' => self::PREFIX_USERHIS_USRE_FOLLOW . $uid,
        	'news_post_his' => self::PREFIX_USERHIS_TW_POST . $uid,
        	'video_post_his' => self::PREFIX_USERHIS_VIDEO_POST . $uid,
        	'news_agree_his' => self::PREFIX_USERHIS_TW_AGREE . $uid,
        	'video_agree_his' => self::PREFIX_USERHIS_VIDEO_AGREE . $uid,
        );
        if ($type == 1) {//video
            unset($getCont['post_history']);
            unset($getCont['frs_history']);
            unset($getCont['videopage_history']);
            unset($getCont['user_follow_history']);
            unset($getCont['news_post_his']);
            unset($getCont['news_agree_his']);
        }
        if ($type == 2) {//tuwen
            unset($getCont['video_history']);
            unset($getCont['video_history_duration']);
            unset($getCont['frs_history']);
            unset($getCont['videopage_history']);
            unset($getCont['user_follow_history']);
            unset($getCont['video_post_his']);
            unset($getCont['video_agree_his']);
        }
        if ($type == 3) {//中间页
            unset($getCont['post_history']);
            unset($getCont['video_history_duration']);
            unset($getCont['frs_history']);
            unset($getCont['user_follow_history']);
            unset($getCont['news_post_his']);
            unset($getCont['news_agree_his']);
        }
        $objRedis = new Libs_Redis(self::REDIS_NAME);
        $arrKeys  = array_values($getCont);
        $redisRet = $objRedis->mgetKv($arrKeys);
        if($redisRet === false){
           return self::_errRet(Tieba_Errcode::ERR_BUSI_USE_REDIS_ERR);
        }
        $result = array();
        foreach($getCont as $field => $redisKey) {
            $redisValue = !empty($redisRet[$redisKey]) ? $redisRet[$redisKey] : '';
            $result[$field] = $redisValue;
            if (!empty($redisValue))  {
                $unCompressValue = gzuncompress($redisValue);
                if (!empty($unCompressValue)) {
                    $result[$field] = $unCompressValue;
                }
            }
        }
		return self::_succRet($result);
	}

    /**
     * @param $arrInput
     * @return multitype
     */
    public static function setUserSession($arrInput){
        if(empty($arrInput['user_id']) || (empty($arrInput['normal']) && empty($arrInput['video']))){
            Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $redisInput = array();
        if(!empty($arrInput['normal'])){
            $redisInput['kv'][] = array(
                'key' => self::PREFIX_SESSION_NORMAL . $arrInput['user_id'],
                'value' => gzcompress($arrInput['normal']),
                'seconds' => empty($arrInput['expire']) ? self::SESSION_REDIS_DEFAULT_EXPIRE : intval($arrInput['expire']),
            );
        }
        if(!empty($arrInput['video'])){
            $redisInput['kv'][] = array(
                'key' => self::PREFIX_SESSION_VIDEO . $arrInput['user_id'],
                'value' => gzcompress($arrInput['video']),
                'seconds' => empty($arrInput['expire']) ? self::SESSION_REDIS_DEFAULT_EXPIRE : intval($arrInput['expire']),
            );
        }
        $objRedis = new Libs_Redis(self::REDIS_NAME);
        $redisRet = $objRedis->msetKv($redisInput);
        if($redisRet === false){
            return self::_errRet(Tieba_Errcode::ERR_BUSI_USE_REDIS_ERR);
        }
        return self::_succRet(true);
    }

    /**
     * @param $arrInput
     * @return multitype
     */
    public static function getUserSession($arrInput){
        if(empty($arrInput['user_id'])){
            Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $keys = array(
            'user_session_video'  => self::PREFIX_SESSION_VIDEO . $arrInput['user_id'],
            'user_session_normal' => self::PREFIX_SESSION_NORMAL . $arrInput['user_id'],
        );
        $objRedis = new Libs_Redis(self::REDIS_NAME);
        $redisRet = $objRedis->mgetKv($keys);
        if($redisRet === false){
            return self::_errRet(Tieba_Errcode::ERR_BUSI_USE_REDIS_ERR);
        }
        $result = array();

        foreach($keys as $field => $redisKey) {
            $redisValue = !empty($redisRet[$redisKey]) ? $redisRet[$redisKey] : '';
            if (!empty($redisValue))  {
                $unCompressValue = gzuncompress($redisValue);
                if (!empty($unCompressValue)) {
                    $result[$field] = $unCompressValue;
                }
            }
        }
        return self::_succRet($result);
    }

    /**
     * @desc 获取用户侧CTR 词典
     * @wiki http://agroup.baidu.com/recommendation/md/article/1467378
     * @param $arrInput
     * @return array
     */
    public static function getUserCtr($arrInput)
    {
        if (empty($arrInput['user_id'])) {
            Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $keys = array(
            'user_ctr_normal' => self::PREFIX_USER_CTR_NORMAL . $arrInput['user_id'], //最近24小时
            'user_ctr_video' => self::PREFIX_USER_CTR_VIDEO . $arrInput['user_id'],
            'user_ctr_normal_small' => self::PREFIX_USER_CTR_NORMAL . 'small_' . $arrInput['user_id'],//最近5分钟
            'user_ctr_video_small' => self::PREFIX_USER_CTR_VIDEO . 'small_' .  $arrInput['user_id'],
        );
        $objRedis = new Libs_Redis(self::REDIS_NAME_USER_CTR);
        $redisRet = $objRedis->mgetKv($keys);
        if ($redisRet === false) {
            return self::_errRet(Tieba_Errcode::ERR_BUSI_USE_REDIS_ERR);
        }
        $result = array();

        foreach ($keys as $field => $redisKey) {
            $redisValue = !empty($redisRet[$redisKey]) ? $redisRet[$redisKey] : '';
            if (!empty($redisValue)) {
                $unCompressValue = gzuncompress($redisValue);
                if (!empty($unCompressValue)) {
                    $result[$field] = $unCompressValue;
                }
            }
        }
        return self::_succRet($result);
    }

    /**
     * 直播用户点击
     * @param $arrInput
     * @return multitype
     */
    public static function userClickLive($arrInput){
        if(!isset($arrInput['user_id']) || !isset($arrInput['cuid']) || !isset($arrInput['tid']) || !isset($arrInput['live_id']) || !isset($arrInput['time'])){
            Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $userId = intval($arrInput['user_id']);
        if($userId == 0){
            //cuid和uid都为空返回错误
            if(empty($arrInput['cuid'])){
                Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $userId = Libs_Util::convert64fromstr($arrInput['cuid']);
        }
        $tid = intval($arrInput['tid']);
        $liveId = intval($arrInput['live_id']);
        $time = intval($arrInput['time']);
        if($tid === 0 || $liveId === 0){
            Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $curtime = time();
        if($time === 0){
            $time = $curtime;
        }
        //参数时间已经过期，返回参数错误
        if($curtime - $time > self::LIVE_EXPIRE){
            Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $objRedis = new Libs_Bdrp(self::REDIS_FRSLIVE_NAME);
        $key = self::LIVE_CLICK_PREFIX . $userId;
        $values = array();
        $redisVal = $objRedis->get($key);

        if(!empty($redisVal)){
            $array = json_decode($redisVal, true);
            foreach ($array as $row){
                //淘汰过期数据
                if($curtime - $row['ts'] > self::LIVE_EXPIRE){
                    continue;
                }
                $values[] = $row;
            }
        }
        $values[] = array(
            'tid' => intval($tid),
            'lid' => intval($liveId),
            'ts' => $time,
        );
        Bingo_Log::pushNotice('uid', $userId);
        $values = self::_sliceLiveHistory($values);
        $values = json_encode($values, JSON_UNESCAPED_UNICODE);
        $ret = $objRedis->set($key, $values, self::LIVE_EXPIRE);
        if($ret === false){
            Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_BUSI_USE_REDIS_ERR);
        }
        return self::_succRet(true);
    }

    /**
     * 直播用户点击
     * @param $arrInput
     * @return multitype
     */
    public static function userShowLive($arrInput){
        if(!isset($arrInput['user_id']) || !isset($arrInput['cuid']) || !isset($arrInput['items'])){
            Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $userId = intval($arrInput['user_id']);
        if($userId == 0){
            //cuid和uid都为空返回错误
            if(empty($arrInput['cuid'])){
                Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $userId = Libs_Util::convert64fromstr($arrInput['cuid']);
        }
        $curtime = time();
        $objRedis = new Libs_Bdrp(self::REDIS_FRSLIVE_NAME);
        $key = self::LIVE_SHOW_PREFIX . $userId;
        $values = array();
        $redisVal = $objRedis->get($key);

        if(!empty($redisVal)){
            $array = json_decode($redisVal, true);
            foreach ($array as $row){
                //淘汰过期数据
                if($curtime - $row['ts'] > self::LIVE_EXPIRE){
                    continue;
                }
                $values[] = $row;
            }
        }
        foreach ($arrInput['items'] as $row){
            if(!isset($row['tid']) || !isset($row['nid']) || !isset($row['live_id'])){
                Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $values[] = array(
                'tid' => intval($row['tid']),
                'lid' => intval($row['live_id']),
                'nid' => $row['nid'],
                'fid' => intval($arrInput['fid']),
                'ts'  => isset($row['time']) ? intval($row['time']) : $curtime,
            );
        }
        Bingo_Log::pushNotice('uid', $userId);
        $values = self::_sliceLiveHistory($values);
        $values = json_encode($values, JSON_UNESCAPED_UNICODE);
        $ret = $objRedis->set($key, $values, self::LIVE_EXPIRE);
        if($ret === false){
            Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_BUSI_USE_REDIS_ERR);
        }
        return self::_succRet(true);
    }


    /**
     * 获取用户直播下发历史
     * @param $arrInput
     * @return
     */
    public static function getUserShowLive($arrInput){
        if(!isset($arrInput['user_id']) || !isset($arrInput['cuid'])){
            Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $userId = intval($arrInput['user_id']);
        if($userId == 0){
            //cuid和uid都为空返回错误
            if(empty($arrInput['cuid'])){
                Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
                return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
            }
            $userId = Libs_Util::convert64fromstr($arrInput['cuid']);
        }
        $objRedis = new Libs_Bdrp(self::REDIS_FRSLIVE_NAME);
        $key = self::LIVE_SHOW_PREFIX . $userId;
        $values = array();
        $redisVal = $objRedis->get($key);
        if(!empty($redisVal)){
            $values = json_decode($redisVal, true);
        }
        return self::_succRet($values);
    }


    /**
     * @param $historys
     * @return array
     */
    protected static function _sliceLiveHistory($historys){
        $arrTid = array();
        //去掉重复数据
        foreach ($historys as $key=>$item){
            $tid = intval($item['tid']);
            if(isset($arrTid[$tid])){
                unset($historys[$key]);
            }
            $arrTid[$tid] = 1;
        }
        Bingo_Log::pushNotice('num', count($historys));
        if(count($historys) <= self::LIVE_HISTORY_MAX_NUM){
            return $historys;
        }
        Bingo_Log::pushNotice('slice', 1);
        $times = array_column($historys, 'ts');
        array_multisort($times, SORT_DESC, $historys);
        return array_slice($historys, 0, self::LIVE_HISTORY_MAX_NUM);
    }


    /**
     * 直播用户点击
     * @param $arrInput
     * @return multitype
     */
    public static function setFrsMask($arrInput){
        if(!isset($arrInput['user_id']) || !isset($arrInput['ids'])){
            Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $userId = intval($arrInput['user_id']);
        $expire = intval($arrInput['expire']);
        if($expire <= 0 ){
            $expire = 43200;
        }
        $arrTids = array();

        $objRedis = new Libs_Redis("mask");
        $key = self::MASK_FRS_PREFIX . $userId;
        $redisVal = $objRedis->getKv($key);

        if(!empty($redisVal)){
            $array =  explode(",", gzuncompress($redisVal));
            foreach ($array as $row){
                if(!isset($values[$row])) {
                    $arrTids[$row] = 1;
                }
            }
        }
        foreach ($arrInput['ids'] as $row){
            if(!isset($values[$row])) {
                $arrTids[$row] = 1;
            }
        }

        $values = array_keys($arrTids);
        if (count($values) > self::MASK_FRS_MAX) {
            $values = array_slice($values, self::MASK_FRS_MAX - count($values), count($values));
        }

        $strTids = gzcompress(implode(",", $values));
        $redisInput = array(
            'key' => $key,
            'value' => $strTids,
            'seconds' => $expire,
        );
        $ret = $objRedis->setKv($redisInput);
        if($ret === false){
            Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_BUSI_USE_REDIS_ERR);
        }
        return self::_succRet(true);
    }


    /**
     * 直播用户点击
     * @param $arrInput
     * @return multitype
     */
    public static function getFrsMask($arrInput){
        if(!isset($arrInput['user_id'])){
            Bingo_Log::warning(__FUNCTION__ . " param is invaliad . input=" . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $userId = intval($arrInput['user_id']);
        $arrTids = array();

        $objRedis = new Libs_Redis("mask");
        $key = self::MASK_FRS_PREFIX . $userId;
        $redisVal = $objRedis->getKv($key);

        if(!empty($redisVal)){
            $arrTids = explode(",", gzuncompress($redisVal));
        }
        return self::_succRet($arrTids);
    }
}