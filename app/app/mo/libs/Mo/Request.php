<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>  <EMAIL>
 * @date 2012-2-28
 * @version
 */
class Mo_Request {
    //open id
    public static $strOpenId = 'wap';
    //当次请求的URL
    public static $strReqAddr = '';
    //移动贴吧的页面版式
    public static $intBdPageType = 1;
    //移动贴吧大中小屏幕
    public static $intTiebaPageType = 1;

    //统计用户来源
    public static $intOriginid = 0;
	//是否来自百度ps
    public static $intOriginBdps = 0;
    //来自百度大搜索的关键词
    public static $strOriginBdpsWord = '';
    //联盟参数
    public static $strFrom = '';
    //wappass的ssid，标示用户登录状态
    public static $strSsid = '';
    //wappass的stoken加密串，验证产品线登录
    public static $strAuth = '';
    //是否已经登录
    public static $bolLogin = false;

    //是否通过ssid登录
    public static $bolLoginBySsid = false;//是否是通过ssid登录
    //登录的用户id
    public static $intUid = 0;
    //登录的用户名
    public static $strUname = '';
    //登录的用户名展示
    public static $strUnameShow = '';
    //name link
    public static $strUnameLink = '';


    //登录用户是否为noun
    public static $intNoUn = 0;
    //登录用户的email
    public static $strEmail = '';
    //登录用户的移动手机
    public static $strMobilePhone = '';

    //登录用户是否为半账号用户
    public static $intIsHalfUser = 0;
    //第三方来源id。1：人人网，2：新浪微博，4：腾讯微博，15：QQ
    public static $intSourceId = 0;
    //半账号用户的临时用户名
    public static $strTempName = '';
    //产品线展示的用户名，由pass传过来，pass传过来什么我们就展示什么，无需判断，优先级：username>email>phone>tempname
    public static $strDisplayName = '';


    //用户头像加密串
    public static $strPortrait = '';
    //登录的数字IP
    public static $intIp = 0;
    //登录的字符串IP
    public static $strIp = '0.0.0.0';
    //用户终端号
    public static $strPhoneImei = '';
    //是否是移动网关IP
    public static $bolIsMobileGate = false;
    //是否使用了URL path参数模式
    public static $bolHasUrlParam = false;
    //是否需要终端适配
    public static $bolNotNeedClientAdapt = false;
    //客户端的终端类型
    public static $strClientType = '';
    //手机的厂商
    public static $strMobileVendor = '';
    //手机的型号
    public static $strPhoneModel = '';  //与之前$strMobileModel的值一样
    public static $strMobileModel = ''; //wise的Model调整以后，该字段的值与$strOs一致
    //手机屏幕宽
    public static $intScreenWidth = '';
    //手机屏幕高
    public static $intScreenHeight = '';
    //浏览器类型
    //已废弃，请使用 $arrClient 字段
    public static $strBrowser = '';
    //浏览器版本
    //已废弃，请使用 $arrClient 字段
    public static $strBrowserVersion = '';
    //浏览器类型id
    //已废弃，请使用 $arrClient 字段
    public static $intBrowserId = 1;
    //浏览器的分类，贴吧特有
    //已废弃，请使用 $arrClient 字段
    public static $intBrowserType = 0; //0表示普通浏览器，1表示高版本的浏览器，2表示低版本的浏览器，需要升级
    //已废弃，请使用 $arrClient 字段
    //操作系统名称
    //已废弃，请使用 $arrClient 字段
    public static $strOs = '';
    //操作系统类型
    //已废弃，请使用 $arrClient 字段
    public static $intOsId = 0;
    //操作系统版本
    //已废弃，请使用 $arrClient 字段
    public static $strOsVersion = '2';
    //ua解析结果
    public static $arrClient = array();
    //网速
    public static $microSpeed = 0;
    //网络环境
    public static $intNetType = 0;//0表示未知，1表示wifi，2表示2G，3表示3G
    //用户自行设置终端适配信息
    public static $bolUserSetReqType = false; //用户自行指定
    //新用户
    public static $is_new_user = 0;

    //是否在webkit版本检测以及uc极速模式检测中进行重新适配回普通版,该字段
    public static $isForceToJunior=false;

    //是否支持cookie
    public static $bolIsSupportCookie = true;//用户手机是否支持cookie

    //以下是WAP特有数据
    //cookie中的wise uid
    public static $strWiseUid = '';
    //统计用LOG ID
    //public static $strLogUid = ''; //使用$strCuid字段替换
    //本次请求是否分配UID
    public static $intAssignLogUid = 0;
    //PU参数
    public static $strPu = '';
    //pinf参数
    public static $strPinf = '';
    //pinf参数内容
    public static $arrPinf = array();
    //$COOKIE[BDUSS]存在
    public static $bolHasBDUSS = false;
    //COOKIE
    public static $arrCookie = array();
    //请求数据
    public static $arrRequest = array();
    //实验用cookie
    public static $strCuid = '';
    public static $intAssignFlag = 0;

    //BAIDUID  切回 BAIDU_WISE_UID新增字段
    public static $strNewWiseUid;
    public static $strNewCuid;
    public static $intNewAssignFlag;

	//BAIDUID 并行策略
	public static $strWiseUid2;
    public static $strCuid2;
    public static $intAssignFlag2;

    //轻应用
    public static $bolIsLightApp = false;

    // 是否是4.0以下版本的android系统
    public static $bolIsAndroidUnderFour = false;

    //获取COOKIE
    public static function getCookie($strKey = null,$mixDefault = ''){
        if (empty($strKey)){
            return self::$arrCookie;
        }
        return isset(self::$arrCookie[$strKey]) ? self::$arrCookie[$strKey]:$mixDefault;
    }
    //设置cookie
    public static function setCookie($strKey = null,$mixValue = ''){
        self::$arrCookie[$strKey] = $mixValue;
    }

    //获取请求数据
    public static function get($strKey = null,$mixDefault = ''){
        if (empty($strKey)){
            return self::$arrRequest;
        }
        return isset(self::$arrRequest[$strKey]) ? self::$arrRequest[$strKey]:$mixDefault;
    }

    //修改请求数据
    public static function set($strKey = null,$mixValue = ''){
        self::$arrRequest[$strKey] = $mixValue;
    }
}
