<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>  <EMAIL>
 * @date 2012-3-8
 * @version 
 */
class Mo_Product_Webapp extends Mo_Product_All {
    public function init(){
        parent::init();
        Mo_Request::$intBdPageType = Mo_Define::BD_PAGE_TYPE_WEBAPP;
        Mo_Request::$intTiebaPageType = Mo_Define::TIEBA_PAGE_TYPE_LARGE;
        $this->_setPsParam();
        $this->_setPdParam();
    }
    //处理ps导流参数
    public function _setPsParam(){
        if(0 == Mo_Request::$intOriginid){
            if(Bingo_Http_Request::getCookie('mo_originid')){
                Mo_Request::$intOriginid = Mo_Request::getCookie('mo_originid');
            }
        }else{
            setcookie('mo_originid',Mo_Request::$intOriginid,0,'/');
        }
    }
    //处理用户来源参数
    public function _setPdParam(){
        if(Mo_Request::$arrRequest['pd']){
            setcookie('mo_pd',Mo_Request::$arrRequest['pd'],0,'/');
        }else{
            if(Bingo_Http_Request::getCookie('mo_pd')){
                Mo_Request::$arrRequest['pd'] = Bingo_Http_Request::getCookie('mo_pd');
            }
        }
    }
}