<?php
/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 13-3-12
 * Time: 下午8:21
 * Description: 获取最新的消息提醒，包括回复我的和at我的消息,目前主要是FRS页在使用，默认返回两条最新消息，先回复，在@我的
 */
class remindAction extends Mo_Core_Action{
    private $_arrReplymeList = array();//回复我的消息数组
    private $_arrAtmeList = array();//@我的消息数组
    private $_intMessageNum = 2;//默认返回的消息条数
    protected $strTplPath = 'sfrs';
    protected $strTplName = 'data_remind_list.php';
    private $_arrResult = array(//返回的数据格式
        'reply_list' => array(),//回复我的消息条数
        'at_feed' => array(),//@我的消息list
        'replyme' => 0,//回复我的未读的消息条数
        'atme' => 0//@我的未读消息条数
    );
    protected  function _execute(){
        $this->_process();
        $this->_build();
    }
    /**
     *
     * 获取消息消息的个数
     */
    private function _getMessage() {
        $arrInput = array(
            'user_portrait' => Mo_Request::$strPortrait,
        );
        $arrOut = Tbapi_Core_Server::mocall('message', 'getmessage', $arrInput);
        if (false === $arrOut){
            Mo_Log::apiwarning('message', 'getmessage', false, $arrInput);
            return false;
        }
        $this->_arrResult['replyme'] = (int)$arrOut['replyme'];
        $this->_arrResult['atme'] = (int)$arrOut['atme'];
        return true;
    }
    //获取at我的消息
    //intNum:获取的消息条数
    private  function _getAtme($intNum = 2){
        $arrInput = array(
            'offset' => 0,
            'rn' =>  (int)$intNum,
            'user_id' => Mo_Request::$intUid,
            'user_portrait' => Mo_Request::$strPortrait,
        );
        $arrOut = Tbapi_Core_Server::mocall('post','atme',$arrInput);
        if(false === $arrOut|| $arrOut['error']['errno']!=Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('tbapi mocall post::atme failed!');
            return false;
        }
        if(isset($arrOut['at_feed'])){
            $this->_arrAtmeList = $arrOut['at_feed'];
        }
    }

    //获取回复我的消息
    private  function _getReplyme($intNum = 2){
        $arrInput = array(
            'offset' => 0,
            'rn' => $intNum,
            'user_id' => Mo_Request::$intUid,
            'user_portrait' => Mo_Request::$strPortrait,
            'group' => 0
        );
        $arrOut = Tbapi_Core_Server::mocall('post','replyme',$arrInput);
        if(false === $arrOut|| $arrOut['error']['errno']!=Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('tbapi mocall post::replyme failed!');
            return false;
        }
        if(isset($arrOut['reply_list'])){
            $this->_arrReplymeList = $arrOut['reply_list'];
        }
    }

    private function _process(){
        $this->_getMessage();//获取未读的消息
        $this->_getAtme(min($this->_arrResult['atme'],$this->_intMessageNum));
        $this->_getReplyme(min($this->_arrResult['replyme'],$this->_intMessageNum));
        $intAtmeNum = count($this->_arrAtmeList);//@我的条数
        $intReplymeNum = count($this->_arrReplymeList);//回复我的条数
        if($intReplymeNum >= $this->_intMessageNum){//回复数大于需要返回的条数
            $this->_arrResult['reply_list'] = array_splice($this->_arrReplymeList,0,$this->_intMessageNum);//优先展示回复，截取回复内容
        }else{
            $this->_arrResult['reply_list'] = $this->_arrReplymeList;
            if($intReplymeNum + $intAtmeNum > $this->_intMessageNum){//回复数小于需要返回的条数，但和@我的数相加大于需要展示的回复数，对@我的消息截取
                $this->_arrResult['at_feed'] = array_splice($this->_arrAtmeList,0,$this->_intMessageNum - $intReplymeNum);
            }else{
                $this->_arrResult['at_feed'] = $this->_arrAtmeList;
            }
        }
    }
    private function _build(){
        Mo_Response::addViewData('base',Mo_Service_Wap::getTplBase());
        Mo_Response::addViewData('wreq',Mo_Service_Wap::getTplUser());
        Mo_Response::addViewData('wreq',Mo_Service_Wap::getTplWreq());
        Mo_Response::addViewData('message',$this->_arrResult);
    }

    protected  function _log(){
        Tieba_Stlog::addNode('ispv',0);
    }

}
