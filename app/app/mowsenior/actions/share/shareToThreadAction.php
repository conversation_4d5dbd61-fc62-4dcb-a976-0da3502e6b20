<?php
/**
 * 站外分享——分享到贴子页(接入百度分享，与游戏的分享的交互逻辑不太一样，希望以后能统一)
 * <AUTHOR>
 * @pm gongrui
 */

require_once ROOT_PATH.'/libs/lib/htmlparser/HtmlParser.class.php';
require_once MOBILE_UI_PATH . '/../mowsenior/libs/Mowsenior/Service/SdkUtil.php';

class shareToThreadAction extends Mo_Core_Action{
	protected $strTplName = 'share_to_thread.php';
    protected $strTplPath = 'sdk';

    private $_arrUser = array();
    private $_arrForum = array();
    private $_arrError = array();
    private $_strFname     = null;  //吧名称
    private $_strTitle     = null;  //分享title
    private $_strText      = null;  //分享文案
    private $_shareUrl = null;      //分享URL
    private $_strImgUrl    = null;   //图片链接
    private $_strSuccessRetUrl    = null;   //成功后跳转地址
    private $_strCancelRetUrl    = null;   //失败后跳转地址
    private $_arrShare = array();    //分享所有信息数组


    protected function _execute() {
        if ($this->_input()){
            $this->_process();
            $this->_build();
        }
    }
    private function _input() {
        $this->_arrUser['is_login'] = Mo_Request::$bolLogin;
        $this->_arrUser['id'] = Mo_Request::$intUid;
        if (!$this->_arrUser['is_login']) {
            $arrServer = Bingo_Http_Request::getServer();
            $wappassUrl = Conf::get('wappass_login_jump_url');
            $strUrl = rawurlencode("http://" . $arrServer['HTTP_HOST'] . $arrServer['REQUEST_URI']);
            Bingo_Http_Response::redirect($wappassUrl.$strUrl,true);
            return false;
        }
        $this->_strFname = strval(Mo_Request::get('forum_name', ''));  //默认吧名,可以为空
        $this->_strTitle = strval(Mo_Request::get('title', ''));
        $this->_strText = strval(Mo_Request::get('content', ''));
        $this->_shareUrl = strval(Mo_Request::get('link', ''));
        $this->_strImgUrl = Mo_Request::get('thumb_url', '');
        $this->_strSuccessRetUrl = Mo_Request::get('successRetUrl', '');
        $this->_strCancelRetUrl = Mo_Request::get('cancelRetUrl', '');

        return true;
    }

    private function _getLikeForumList() {
        $arrInput = array(
            'user_id'   =>  $this->_arrUser['id']
        );
        //获取关注的吧列表
        $arrOut = Tieba_Service::call('user','getMyFavorForum',$arrInput, null, null, 'post', 'php', 'utf-8');
        if($arrOut === false) {
            Bingo_Log::warning('service_user getMyFavorForum failed!');
            $this->_arrError = Mo_Sdk_Util::errRet(Tieba_Errcode::ERR_UNKOWN);
            return false;
        }
        if(($arrOut['errno'] !== 0)) {
            $this->_arrError['error_code'] = $arrOut['errno'];
            $this->_arrError['error_msg'] = $arrOut['errmsg'];
            Bingo_Log::warning('service_user getMyFavorForum failed, errno:' . $arrOut['errno'] . $arrOut['errmsg']);
        } else {
            $this->_arrError['error_code'] = 0;
            $this->_arrError['error_msg'] = 'Success';
            $strForumIds = '';
            foreach ($arrOut['concern_forum']['forum'] as $forum) {
                $arrTmp  = array();
                $arrTmp['user_id'] = $forum['user_id'];
                $arrTmp['forum_id'] = $forum['forum_id'];
                $arrTmp['forum_name'] = $forum['forum_name'];
                $arrTmp['cur_score'] = $forum['cur_score'];
                $arrTmp['level_up_score'] = $forum['cur_score'] + $forum['score_left'];
                $this->_arrForum[] = $arrTmp;
                $strForumIds .= $forum['forum_id'] . ',';
            }
            // 获取吧头像
            $strForumIds = trim($strForumIds, ',');
            $arrForumIds = explode(',', $strForumIds);
            foreach ($arrForumIds as $arrTmp) {
                $arrForumInput['forum_id'][] = $arrTmp;
            }

            $arrOut = Tieba_Service::call('forum', 'mgetBtxInfo', $arrForumInput, null, null, 'post', 'php', 'utf-8');
            if (false === $arrOut || $arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS || empty($arrOut['output'])) {
                $this->_arrError['error_code'] = $arrOut['errno'];
                $this->_arrError['error_msg'] = $arrOut['errmsg'];
                return false;
            }
            foreach($arrOut['output'] as &$arrItem){
                if (isset($arrItem['attrs']['card_p1'])) {
                    $arrItem['attrs']['card_p1']['style_name'] = Bingo_String::json2array($arrItem['attrs']['card_p1']['style_name']);
                } else {
                    if(isset($arrItem['card']['avatar'])){ //默认头像
                        $arrItem['attrs']['card_p1']['style_name']['avatar'] = $arrItem['card']['avatar'];
                    }
                }
            }
            $arrForumStyle = $arrOut['output'];
            foreach ($this->_arrForum as &$forum) {
                if ($arrForumStyle[$forum['forum_id']]['attrs']['card_p1']) {
                    $forum['avatar'] = $arrForumStyle[$forum['forum_id']]['attrs']['card_p1']['style_name']['avatar'];
                }
            }

        }
    }

    /**
     * 检查URL是否正确
     */
    private function checkUrl($strUrl) {
        if (empty($strUrl)) {
            Bingo_Log::warning("get failure!the url is empty  error!");
            return false;
        }
        if ( (strncasecmp($strUrl, 'http://', 7) == 0) || (strncasecmp($strUrl, 'https://', 8) == 0) ) {
            return true;
        }else{
            Bingo_Log::warning("get failure!the url do not contain a fix error!");
            return false;
        }
    }

    /**
     *处理图片url地址,为每个地址加上http://
     *@param $arrPicUrl
     *@return $arrOutput
     */
     public function processPicsUrl($arrPicUrl){
         $intPic = 0;
         $arrOutput = array() ;
         if (empty($arrPicUrl)){
             return $arrOutput;
         }
         foreach($arrPicUrl as $picItem){
             $arrRet = explode('.',$picItem);
             if($arrRet){
                 $strExt = $arrRet[count($arrRet)-1];
                 if( ! Bingo_Array::in_array($strExt,array('jpg', 'bmp', 'gif', 'tif', 'png', 'jpeg'), true )){
                    continue;
                 }
             }else{
                 continue;
             }
             if("http://" != substr($picItem, 0, 7)){
                 $arrOutput[] = "http://". $picItem;
             }else{
                 $arrOutput[] = $picItem;
             }
 
             if ( ++$intPic > 30){
                 return $arrOutput;
             }
         }
         return $arrOutput;
     }

     /**
      * 普通类型url地址解析
      */
    public function parserHtmlUrl($url) {
               
        $out = array(
            'real_title' => '',
            'all_img'    => '',
            'html_title' => ''
        );
        $arrShare = array(
        );
            
        $HtmlParser = new HtmlParser ( $url );
        $HtmlParser->setCurlTimeoutMs ( 2500 );
        $HtmlParser->setCurlRetry ( 0 );
        
        $onlyTitle = false; 
        Bingo_Timer::start ( 'htmlParser' );
        $res = $HtmlParser->htmlParser ( $out, $onlyTitle );
        Bingo_Timer::end ( 'htmlParser' );
        if ($res == false) {
            $err = $HtmlParser->getError ();
            if ($err !== "") {
                Bingo_Log::warning ( "getOpenShareData fail ERR:$err" );
            }
        }
        if ( empty($this->_strTitle) ){
            if(empty($out ['real_title'])){
                $arrShare ['title'] = Mo_Service_Encode::convertGBKToUTF8($out ['html_title']);
            }else{
                $arrShare ['title'] = Mo_Service_Encode::convertGBKToUTF8($out ['real_title']);
            }
        }else{
            $arrShare ['title'] = $this->_strTitle;
        }
        
        $arrShare ['title'] = Bingo_String::truncate ( $arrShare ['title'], 60 , "..." ); 
        if( empty($this->_strText) ){
            $arrShare ['content'] = Mo_Service_Encode::convertGBKToUTF8($out ['html_title']);
        }else{
            $arrShare ['content'] = $this->_strText;
        }
        
        $arrShare ['content'] = Bingo_String::truncate ( $arrShare ['content'], 200 , "..." ). $url;

        if (empty($this->_strImgUrl)){
            $arrPic = isset($out ['all_img']) && ! empty( $out ['all_img'] ) ? array_unique ( $out ['all_img'] ):array();
        }else{
            $arrPic =  explode(",",$this->_strImgUrl);
        }
        //只选择一张图片
        $arrShare['pic'] = $this->processPicsUrl($arrPic)[0];

        $arrShare['forum_name'] = $this->_strFname;
        $arrShare['fid'] = $this->checkForum();
        $arrShare['link'] = $this->_shareUrl;
        $arrShare['successRetUrl'] = $this->_strSuccessRetUrl;
        $arrShare['cancelRetUrl'] = $this->_strCancelRetUrl;
        return $arrShare;   
    }

     //获取吧信息，主要是为了拿到吧id，并判断吧是否存在
    private function checkForum() {
        $arrInput = array(
            'query_words' => array(
                $this->_strFname
            )
        );
        $arrOut = Tieba_Service::call('forum', 'getFidByFname', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        if(false === $arrOut
         || !isset($arrOut['forum_id'][0]['has_forum_id'])
         || 1 != $arrOut['forum_id'][0]['has_forum_id']) {
            Bingo_Log::fatal("fail to call forum->getFidByFname : ". serialize($arrInput));
            Mo_Response::$intErrno = Mo_Errno::ERR_FRS_FORUM_NOT_EXIST;
            Mo_Response::$strError = '吧不存在';
            return false;
        }
        return $arrOut['forum_id'][0]['forum_id'];
    }

    /**     
    * 打包 
    */ 
    public function getAllInfo($url) {      
     
        $arrRet = parse_url ( $url );       
        if(! empty($arrRet))
         $strHost = 'http://'.trim($arrRet ['host']).'/';
        else 
         $strHost = '';

        $this->_arrAllInfo = $this->parserHtmlUrl ( $url );

        return $this->_arrAllInfo;
    }    

    private function _process() {
        $bolUserExist = Mo_Sdk_Util::checkUserExist($this->_arrUser['id']);
        if ($bolUserExist) {
            $this->_getLikeForumList();
            if(!$this->checkUrl($this->_shareUrl)) {
                $this->_shareUrl = Bingo_Http_Request::getServer('HTTP_REFERER');;
            }
            $this->_arrShare = $this->getAllInfo($this->_shareUrl);
        }
    }

    private function _build() {
        Mo_Response::addViewData('base', Mo_Service_Wap::getTplBase());
        Mo_Response::addViewData('wreq', Mo_Service_Wap::getTplWreq());
        Mo_Response::addViewData('user', $this->_arrUser);
        Mo_Response::addViewData('like_forum', $this->_arrForum);
        Mo_Response::addViewData('share_info', $this->_arrShare);
        Mo_Response::addViewData('error', $this->_arrError);
    }
    protected function _log() {
        Tieba_Stlog::addNode('ispv', 1);
    }
}

?>
