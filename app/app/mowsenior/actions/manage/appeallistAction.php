<?
/**
 *  申诉列表页
 *
 *  <AUTHOR> <<EMAIL>>
 *  @version 2013-12-03
 *  
 */
require_once MOBILE_UI_PATH.'/../mowsenior/libs/Mowsenior/Service/AddCheckPhoneViewData.php';
class AppeallistAction extends Mo_Core_Action{
	protected $strTplName = 'bawuAppeal.php';
    protected $strTplPath = 'smanage';

	private $_strForumName = '';
	private $_intForumId   = 0;
	private $_intUserId	   = 0;
    private $_strWord      = '';

    private $_intPn = 1;
    private $_intRn = 20;           

    protected  function _execute(){
        $ret = $this->_input();
        if($ret) {
            $ret = $this->_process();
        }
        $this->_build($ret);
    }
   
    private function _input() {
    	$this->_strForumName = Mo_Request::get('fn' , '');
        $this->_intForumId   = Mo_Request::get('fid', 0 );
        $this->_intUserId    = Mo_Request::$intUid;

        /* 参数不全，无法进入，返回首页 */
        if(empty($this->_strForumName) || empty($this->_intForumId) || empty($this->_intUserId) ) {
            return false;
        }

        $this->_intPn   = Mo_Request::get('pn', 1);

        return true;
    }

    private function _process() {  
        $arrInput = array(
            'forum_name' => $this->_strForumName,
            'forum_id'   => $this->_intForumId,
            'user_id'    => $this->_intUserId,
            'pn'         => $this->_intPn,
            'size'       => $this->_intRn,
        );

        $arrOutput = Mo_Data::get('bawu','get_appeal_list',$arrInput);

        if($arrOutput['error']['errno'] == 0) {
            return $arrOutput;
        } else {
            return false;
        }
    }

    private function _build($ret) {
        if($ret == false) {
            Mo_Core_Controller::forward(Mo_Core_Url::ROUTER_HOME);
            return false;
        }
        Mo_Response::addViewData('pageData',$ret);
        Mo_Response::addViewData('wreq',Mo_Service_Wap::getTplWreq());
        Mo_Response::addViewData('base',Mo_Service_Wap::getTplBase());
        Mo_Response::addViewData('user',Mo_Service_Wap::getTplUser());

        Mowsenior_Service_AddCheckPhoneViewData::add();
    }

    protected function _log(){
    
    }
}
