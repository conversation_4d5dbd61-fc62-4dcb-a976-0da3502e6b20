<?php
/**
 * Author: tianwen
 * Date: 2013-09-26
 * Time: 下午17:13
 * Description: ueg帐号异常中间页
 */

class accountVcodeAction extends Mo_Core_Action{
    protected $strTplName = 'account_vcode.php';
    protected $strTplPath = 'swebview';

    private $userId = 1;
    private $_client_version = "";
    private $accountVcodeArray = array();

    private function _input() {
        $this->userId = Mo_Request::$intUid;
        $this->_client_version = Mo_Request::get('_client_version','');
        return true;
    }
    
    protected  function _execute(){
        $this->_input();
        $this->_process();
        $this->_build();
    }

    private function _process(){
        $arrOut = array();
        $arrInput = array(
            'user_id' => $this->userId,
            'source' => 'client'
        );
        $arrOut =  Mo_Data::get('antispam','queryTask',$arrInput);
        if(!empty($arrOut)) {
           $this->accountVcodeArray = $arrOut;
        }
    }
    
    protected  function _build(){
        Mo_Response::addViewData('wreq',Mo_Service_Wap::getTplWreq());
        Mo_Response::addViewData('base',Mo_Service_Wap::getTplBase());
        Mo_Response::addViewData('user',Mo_Service_Wap::getTplUser());
        Mo_Response::addViewData('account_vcode',$this->accountVcodeArray);
        Mo_Response::addViewData('_client_version',$this->_client_version);
    }

    public function _log(){

    }
}