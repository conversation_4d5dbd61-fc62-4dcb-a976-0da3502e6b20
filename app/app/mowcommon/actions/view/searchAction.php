<?php
/**
 * @author: ji<PERSON><PERSON><PERSON>
 * @date: 2013-04-24
 * @file: searchAction.php
 * @description:搜索页，包括智能版和普通版
 */
require_once MOBILE_UI_PATH.'/../mowcommon/libs/Mowcommon/Service/SearchProcessor.php';
class SearchAction extends Mo_Core_Action{
    //请求数据
    private $strKeyword = null;
    private $bolNeedSearch = true;
    private $intPn = 0;
    private $intRn = 0;
    private $intLm = 0;
    private $intPnum = 0;
    private $intTnum = 0;
    private $intMaxPnum = 0;
    private $strTn = '';
    private $objPager = null;
	
	private $strNewSearchUrl = '';

    //模板变量基础数据
    private $arrSearchPosts   = array();
    private $arrSearchResult  = array(
        'totalResults' => 0,
    );
    protected function _execute(){
        $ret = $this->_input();
        if($ret === true){
            if($this->bolNeedSearch){
                $this->_processSearch();
            }
        }
        $this->_build();
    }
    private function _build(){
        $wreq = Mo_Service_Wap::getTplWreq();
        $wreq['lm'] = $this->intLm;
        $wreq['pn'] = $this->intPn;
        $wreq['word'] = $this->strKeyword;
        Mo_Response::addViewData('wreq',$wreq);
        Mo_Response::addViewData('user',Mo_Service_Wap::getTplUser());
        Mo_Response::addViewData('base',Mo_Service_Wap::getTplBase());
        Mo_Response::addViewData('page',$this->_getTplPage());
        Mo_Response::addViewData('thread_list',$this->arrSearchPosts);
        if(Mo_Request::$intBdPageType == Mo_Define::BD_PAGE_TYPE_WEBAPP){
            $this->strTplPath = 'ssearch';
            if($this->strTn == 'data_search_list'){
                $this->strTplName = 'data_searchResult.php';
            }else{
                $this->strTplName = 'search.php';
            }
        }else{
            $this->strTplPath = 'wother';
            if($this->bolNeedSearch){
                $this->strTplName = 'searchResult.php';
            }else{//对于普通版，点击搜索的时候用户跳转到搜索页面，包括搜人，搜吧，搜贴，并不是直接展示搜索结果
                $this->strTplName = 'search.php';
            }
        }
    }

    private function _input(){	
		$strKeyWord = Mo_Request::get('word','');
        $strKeyWord = str_replace(chr(194).chr(160)," ",$strKeyWord);
        $strKeyWord = trim($strKeyWord);
		
		if(Mo_Request::$intBdPageType == Mo_Define::BD_PAGE_TYPE_WEBAPP){
			$sever = Bingo_Http_Request::getServer();	
			$this->strNewSearchUrl = 'http://'.$sever['HTTP_HOST'].'/mo/q/seekcomposite?search_redirect=1&word='.$strKeyWord;
			Bingo_Http_Response::redirect($this->strNewSearchUrl);
			return false;
		}
		
        $this->strTn = strval(Mo_Request::get('rt',''));
        if(Mo_Request::$intBdPageType == Mo_Define::BD_PAGE_TYPE_WEBAPP){
            $this->intRn = Mo_Define::POST_SEARCH_IPHONE_RN;
            $this->intMaxPnum = Mo_Define::POST_SEARCH_IPHONE_MAX_PNUM;
        }
        else{
            $this->intRn = Mo_Define::POST_SEARCH_RN;
            $this->intMaxPnum = Mo_Define::POST_SEARCH_MAX_PNUM;
        }
        $objPager = new Mo_Util_Pager($this->intRn,0,$this->intMaxPnum);
        $page = $objPager->toArray();
        $this->intPn = $page['offset'];
        $this->intTnum = $page['tnum'];
        $this->intPnum = $page['pnum'];

        Mo_Response::$intOpErrno   = intval(Mo_Request::get('tieba_err_no', 0));
        Mo_Response::$strOpError = strval(Mo_Request::get('tieba_msg', null));

        //判断是否需要检索
        $this->intLm = intval(Mo_Request::get('lm', 0));
        if ($this->intLm == 0)
        {
            $strSub3 = Mo_Request::get('sub3',false);
            $strSub5 = Mo_Request::get('sub5',false);
            $strSub7 = Mo_Request::get('sub7',false);
            if (!empty($strSub3))//按照关键字搜索
            {
                $this->intLm = Mo_Define::POST_SEARCH_LM_BY_KEYWORD;
                $this->bolNeedSearch = true;
            }
            elseif(!empty($strSub5))//按照用户名搜索
            {
                $this->intLm = Mo_Define::POST_SEARCH_LM_BY_USERNAME;
                $this->bolNeedSearch = true;
            }
            elseif (!empty($strSub7))//搜索表单页
            {
                $this->intLm = Mo_Define::POST_SEARCH_LM_BY_KEYWORD;
                $this->bolNeedSearch = false;
                if(Mo_Request::$intBdPageType == Mo_Define::BD_PAGE_TYPE_WEBAPP){
                    $this->bolNeedSearch = true;
                }
            }else{
                $this->intLm = Mo_Define::POST_SEARCH_LM_BY_KEYWORD;
                $this->bolNeedSearch = false;
            }
        }
        elseif ($this->intLm == Mo_Define::POST_SEARCH_LM_BY_KEYWORD)
        {
            $this->bolNeedSearch = true;
        }
        elseif ($this->intLm == Mo_Define::POST_SEARCH_LM_BY_USERNAME)
        {
            $this->bolNeedSearch = true;
        }
        else
        {
            $this->intLm = Mo_Define::POST_SEARCH_LM_BY_KEYWORD;
            $this->bolNeedSearch = false;
        }
        $this->strKeyword = Mo_Service_Encode::convertGBKToUTF8($strKeyWord);
        if (empty($this->strKeyword))
        {
            $this->bolNeedSearch = false;
        }
        return true;
    }
    private function _getTplPage(){
        $intTotalNum = $this->arrSearchResult['totalResults'];
        $intMaxNum = $this->intMaxPnum * $this->intRn;
        if ($intTotalNum > $intMaxNum){
            $intTotalNum = $intMaxNum;
        }
        $ret = array
        (
            'current_page' => intval($this->intPn / $this->intRn) + 1,
            'total_num'    => $intTotalNum,
        );
        return $ret;
    }
    private function _processSearch(){
        $objSearchProcessor = new Mowcommon_Service_SearchProcessor();
        $objSearchProcessor->setParam($this->strKeyword,$this->intPn,$this->intRn,$this->intLm);
        $ret = $objSearchProcessor->execute();
        if($ret){
            $this->arrSearchPosts = $objSearchProcessor->getSearchPosts();
            $this->arrSearchResult = $objSearchProcessor->getSearchResult();
        }else{
            Mo_Response::$intErrno = Mo_Errno::ERR_SEARCH_NO_DATA;
        }
        if(empty($this->arrSearchPosts)){
            Mo_Response::$intErrno = Mo_Errno::ERR_SEARCH_NO_DATA;
        }else{//检索结果不为空
            if(Mo_Request::$intBdPageType === Mo_Define::BD_PAGE_TYPE_WEBAPP){// 智能版单独策略,不做截断
                $this->_processSearchForWebapp();
            }else{// 非智能版，需要做截断
                $this->_processSearchForXHTML();
            }
        }
    }

    private function _processSearchForWebapp() {
        // 智能版对搜索结果不做文本截断
        if ($this->arrSearchPosts != null)
        {
            foreach ($this->arrSearchPosts as $key => $post)
            {
                // process title
                $tmp = $post['title'];
                $tmp = str_replace('<em>', '<span class="high light">', $tmp);
                $tmp = str_replace('</em>', '</span>', $tmp);
                $this->arrSearchPosts[$key]['title'] = $tmp;

                // process conntent
                $tmp = $post['content'];
                $tmp = str_replace('<em>', '<span class="high light">', $tmp);
                $tmp = str_replace('</em>', '</span>', $tmp);
                $this->arrSearchPosts[$key]['content'] = $tmp;
            }
        }
        return true;
    }

    private function _processSearchForXHTML() {
        // 普通版对搜索结果要做文本截断
        if ($this->arrSearchPosts != null)
        {
            // 标题和摘要内容截断长度适配
            $intTitleShowLen = 0;
            $intContentShowLen = 0;
            switch (Mo_Request::$intTiebaPageType)
            {
                case Mo_Define::TIEBA_PAGE_TYPE_LARGE :
                    $intTitleShowLen = Mo_Define::POST_SEARCH_TITLE_SHOW_LEN_BIG;
                    $intContentShowLen = Mo_Define::POST_SEARCH_CONTENT_SHOW_LEN_BIG;
                    break;
                case Mo_Define::TIEBA_PAGE_TYPE_MEDIUM :
                    $intTitleShowLen = Mo_Define::POST_SEARCH_TITLE_SHOW_LEN_MEDIUM;
                    $intContentShowLen = Mo_Define::POST_SEARCH_CONTENT_SHOW_LEN_MEDIUM;
                    break;
                default:
                    $intTitleShowLen = Mo_Define::POST_SEARCH_TITLE_SHOW_LEN_SMALL;
                    $intContentShowLen = Mo_Define::POST_SEARCH_CONTENT_SHOW_LEN_SMALL;
                    break;
            }

            // 执行截断
            foreach ($this->arrSearchPosts as $key=>$post)
            {
                // process title
                $this->arrSearchPosts[$key]['title'] = $this->_mysubstr($post['title'], $intTitleShowLen);

                // process conntent
                $this->arrSearchPosts[$key]['content'] = $this->_mysubstr($post['content'], $intContentShowLen);
            }
        }
        return true;
    }
    /*
    * 内容截断，保留完整的<em>...</em>标签对，$intMaxLen为建议长度
    */
    private function _mysubstr(
        $strOrig,
        $intMaxLen,
        $arrStripL = array( 'from' => '<em>', 'to' => '<span class="high light">' ),
        $arrStripR = array( 'from' => '</em>', 'to' => '</span>' )) {

        // 参数判断
        if (empty($strOrig) || ($intMaxLen <= 0)) {
            return $strOrig;
        }

        // 切分左标签
        $arrOuter = explode($arrStripL['from'], $strOrig);
        $intCurLen = 0; // 字符串真实长度(不包含标签长度,以及'..')
        $strRet = ''; // 返回串

        foreach($arrOuter as $strTmp) {
            if ($strTmp == '') {
                continue;
            }
            if ($intCurLen >= $intMaxLen) {
                break;
            }
            // 切分右标签
            $arrInner = explode($arrStripR['from'], $strTmp);
            $intTmpLen = mb_strlen(htmlspecialchars_decode($arrInner[0]), 'utf-8');
            if (! isset($arrInner[1])) { // 没有找到右标签
                // 这种情况应该只出现在$arrTmpL[0], $str1作为普通字符串，而非关键字处理
                if (($intCurLen + $intTmpLen) <= $intMaxLen) { // 长度不够
                    $strRet .= htmlspecialchars($arrInner[0]);
                    $intCurLen += $intTmpLen;
                } else { // 长度够
                    $strRet .= htmlspecialchars(htmlspecialchars(mb_substr(htmlspecialchars_decode($arrInner[0]), 0, $intMaxLen - $intCurLen, 'utf-8') . ".."));
                    $intCurLen = $intMaxLen;
                    break;
                }
            } else { // 找到右标签，$arrInner[0]是需要飘红的关键词(不截段)，其余不是关键词
                $strRet .= $arrStripL['to'] . htmlspecialchars($arrInner[0]) . $arrStripR['to'];
                $intCurLen += $intTmpLen;
                if ($intCurLen >= $intMaxLen) { // 长度够了
                    break;
                }

                //长度不够, 拿$str2来补
                $intTmpLen = mb_strlen(htmlspecialchars_decode($arrInner[1]), 'utf-8');
                if (($intCurLen + $intTmpLen) <= $intMaxLen) { // 长度不够
                    $strRet .= htmlspecialchars($arrInner[1]);
                    $intCurLen += $intTmpLen;
                } else { // 长度够，要截断
                    $strRet .= htmlspecialchars(htmlspecialchars(mb_substr(htmlspecialchars_decode($arrInner[1]), 0, $intMaxLen - $intCurLen, 'utf-8') . ".."));
                    $intCurLen = $intMaxLen;
                    break;
                }
            }
        }
        return $strRet;
    }
    protected function _log(){
        Mo_Log::addNode('req_search','kw',$this->strKeyword);
        Mo_Log::addNode('req_search','pn',$this->intPn);
        Mo_Log::addNode('req_search','rn',$this->intRn);
        Mo_Log::addNode('req_search','lm',$this->intLm);
        Mo_Log::addNode('req_search','pnum',$this->intPnum);
        Mo_Log::addNode('req_search','tnum',$this->intTnum);
        Mo_Log::addNode('res_search','err',Mo_Response::$intErrno);
        Mo_Log::addNode('res_search','kw',$this->strKeyword);
        Mo_Log::addNode('res_search','thdcnt',count($this->arrSearchPosts));
    }
}

