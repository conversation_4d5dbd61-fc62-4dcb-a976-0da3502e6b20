<?php
/**
 * @author: ji<PERSON><PERSON><PERSON>
 * @date: 2013-04-27
 * @file: myHistoryAction.php
 * @description:普通版我的历史记录，从首页的更多进入
 */

require_once MOBILE_UI_PATH . '/../mowcommon/libs/Mowcommon/Service/SearchProcessor.php';

class MyHistoryAction extends Mo_Core_Action{
    const HISTORY_RN = 10;
    const HISTORY_TAB = 'history';
    private $_arrThreadList = array();
    private $_arrPage = array();

    protected function _execute(){
        if($this->_input()){
            $this->_process();
        }
        $this->_build();
    }
    private function _input(){
        if(Mo_Request::$bolLogin){
            return true;
        }
        return false;
    }
    protected function _process(){
        $objPager = new Mo_Util_Pager(self::HISTORY_RN);
        $objSearchProcessor = new Mowcommon_Service_SearchProcessor();
        $objSearchProcessor->setParam(Mo_Request::$strUname,$objPager->offset,$objPager->req_num,Mo_Define::POST_SEARCH_LM_BY_USERNAME);
        $ret = $objSearchProcessor->execute();
        if($ret){
            $arrSearchPosts = $objSearchProcessor->getSearchPosts();
            $arrSearchResult = $objSearchProcessor->getSearchResult();
            $objPager->total_num = intval($arrSearchResult['totalResults']);
            $objPager->total_page = ceil($objPager->total_num/$objPager->req_num);
            if(!empty($arrSearchPosts)){
                $intTitleShowLen = 0;
                switch(Mo_Request::$intTiebaPageType){
                    case Mo_Define::TIEBA_PAGE_TYPE_LARGE:
                        $intTitleShowLen = Mo_Define::POST_SEARCH_CONTENT_SHOW_LEN_BIG;
                        break;
                    case Mo_Define::TIEBA_PAGE_TYPE_MEDIUM:
                        $intTitleShowLen = Mo_Define::POST_SEARCH_CONTENT_SHOW_LEN_MEDIUM;
                        break;
                    default:
                        $intTitleShowLen = Mo_Define::POST_SEARCH_CONTENT_SHOW_LEN_SMALL;
                        break;
                }
                foreach($arrSearchPosts as $post){
                    $tmp = $post->title;
                    $tmp = html_entity_decode($tmp,ENT_COMPAT,'utf-8');
                    if(mb_strlen($tmp,'utf-8') > $intTitleShowLen){
                        $tmp = mb_substr($tmp,0,$intTitleShowLen,'utf-8') . '..';
                        $tmp = htmlspecialchars($tmp,ENT_COMPAT,'utf-8');
                        $tmp = str_replace('&nbsp;','&#160;',$tmp);
                        $post->title = $tmp;
                    }
                }
                $this->_arrPage = $objPager->toArray();
                $this->_arrThreadList = $arrSearchPosts;
            }
        }
    }


    private function _build(){
        if(Mo_Request::$intBdPageType === Mo_Define::BD_PAGE_TYPE_WEBAPP){
            Mo_Core_Controller::forward(Mo_Core_Url::ROUTER_HOME);
        }else{
            $this->_arrPage['tab'] = self::HISTORY_TAB;
            Mo_Response::addViewData('base',Mo_Service_Wap::getTplBase());
            Mo_Response::addViewData('wreq',Mo_Service_Wap::getTplWreq());
            Mo_Response::addViewData('user',Mo_Service_Wap::getTplUser());
            Mo_Response::addViewData('thread_list',$this->_arrThreadList);
            Mo_Response::addViewData('page',$this->_arrPage);
            $this->strTplPath = 'wother';
            $this->strTplName = 'forum.php';
        }
    }
    protected function _log(){

    }
}