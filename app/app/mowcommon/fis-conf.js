/**
 * @desc 自动部署代码配置文件
 * <AUTHOR>
 * @since 2014-6-10
 */

//部署机器HOSt
var deployHost = 'tc-testing-fesandbox-forum05-vm.epc.baidu.com';
var deployPath = '/home/<USER>/orp001/app/mowcommon';

//拼接出目标机器文件接收服务
var deployServ = 'http://' + deployHost + ':8099/fis/reciever';

//部署的配置信息
fis.config.merge({
	deploy : {
		remote : [
		    {
		    	receiver : deployServ,
				from : '/', //所有文件
				to : deployPath, //代码部署路径
				replace : replaceAddrConf
		    }
		]
	},
	roadmap : {
		path : [
			{	//不发布.json文件
				reg : /\.*\.json$/i,
				release : false,
			},
		]
	}
});


//设置文件编码
fis.config.set('project.charset', 'utf8');

//这些文件不需要部署
fis.config.set('project.exclude', /^\/(output|.*\.(sh|js|json|bat))/i);

//地址替换的配置
var replaceAddrConf = {	
	from : /(tieba\.baidu\.com)|(tb[12]\.bdstatic\.com)|(static\.tieba\.baidu\.com)/g,
    to : function(m, $1, $2, $3, $4){
		if($1){
			return "fe8.tieba.baidu.com";
		}else if($2 || $3){
			return "static.fe8.tieba.baidu.com";
		}
	}
}
