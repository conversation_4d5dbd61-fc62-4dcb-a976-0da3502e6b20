<?php

/* * *************************************************************************
 *
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 *
 * ************************************************************************ */

/**
 * [addUserAction.php description]
 * <AUTHOR>
 * @DateTime 17/5/9 17:25
 * @brief
 */
class addUserAction extends Core_BaseAction {

    /**
     * [run description]
     * <AUTHOR>
     * @param
     * @return
     */
    protected function run() {
        $data['hi_name'] = Bingo_Http_Request::getPostNoXssSafe("hi_name");
        $data['real_name'] = Bingo_Http_Request::getPostNoXssSafe("real_name");
        $data['company_mail'] = Bingo_Http_Request::getPostNoXssSafe("company_mail");
        $obj = new Model_User();
        $retunInfo = $obj->addUser($data);
        $this->_displayJson($retunInfo);
    }

}
