<?php

/* * *************************************************************************
 *
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 *
 * ************************************************************************ */

/**
 * [showUserAction.php description]
 * <AUTHOR>
 * @DateTime 17/5/9 17:25
 * @brief
 */
class showUserAction extends Core_BaseAction {

    /**
     * [run description]
     * <AUTHOR>
     * @param
     * @return
     */
    protected function run() {
        $data['id'] = Bingo_Http_Request::getPostNoXssSafe("id");
        $data['hi_name'] = Bingo_Http_Request::getPostNoXssSafe("hi_name");
        $data['real_name'] = Bingo_Http_Request::getPostNoXssSafe("real_name");
        $data['company_mail'] = Bingo_Http_Request::getPostNoXssSafe("company_mail");
        $data['offset'] = Bingo_Http_Request::getPostNoXssSafe("offset");
        $data['limit'] = Bingo_Http_Request::getPostNoXssSafe("limit");
        $obj = new Model_User();
        $retunInfo = $obj->showUser("*", $data);
        $this->_displayJson($retunInfo);
    }

}
