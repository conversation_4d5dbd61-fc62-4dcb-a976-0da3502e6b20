<?php
class Util_Rpc {

    public static function checkTidMatchWithPid($intTid, $intPid) {
        $intTid = (int)$intTid;
        $intPid = (int)$intPid;
        $arrInput = array(
            "input" => array( //��������
                0 => array(
                    "thread_id" => $intTid,
                    "post_id" => $intPid,
                )
            )
        );
        $arrOutput = Tieba_Service::call('post', 'checkPostInfo', $arrInput, NULL, NULL, 'post', 'php', 'utf-8');
        if (false === $arrOutput || 0 !== (int)$arrOutput['errno']) {
            Bingo_Log::warning('call service checkPostInfo failed!');
            Bingo_Log::warning(sprintf('input: thread_id[%d], post_id[%d]', $intTid, $intPid));
            return false;
        }

        if (1 !== (int)$arrOutput['output'][0]['is_valid']) {
            return false;
        }
        return true;
    }

    public static function getUserPerm($user_id, $forum_id, $user_ip =0)
    {
        $arrParamsList = array (
            'forum_id' => $user_id,
            'user_id' => $forum_id,
            'user_ip' => $user_ip,
        );

        $arrOutput = Tieba_Service::call("perm", "getPerm", $arrParamsList);

        if (Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno'])
        {
            Bingo_Log::warning("call user::getPerm fail, the input is ".serialize($arrParamsList).' out put is '.serialize($arrOutput));
            return false;
        }

        return $arrOutput['output'];
    }

    public static function antiActsctrlQuery($user_id, $user_ip, $forum_id, 
        $thread_id, $post_id, $dst_user_id, $cmd)
    {
        $arrCallInput = array(
            'req'=>array(
                'rulegroup'=>array('app'),
                'app' => 'godthread',
                'cmd' => $cmd, // 'pbunlike'
                'uid' => intval($user_id),
                'forum_id' => intval($forum_id),
                'thread_id' => intval($thread_id),
                'post_id' => intval($post_id),
                'ip' => intval($user_ip), // ip2long($ip)
                'dstuid' => intval($dst_user_id),
            ),);

        $arrCallRet = Tieba_Service::call('anti','antiActsctrlQuery',$arrCallInput);
        return $arrCallRet;
    }

    public static function antiMutiCommit($user_id, $user_name, $user_ip, $forum_id, 
        $thread_id, $post_id,$dst_user_id,$dst_user_name, $post_content, $cmd)
    {
        $objMulti = new Tieba_Multi("godthread");

        $arrActsctrlInput = array(
            'req'=>array(
                'rulegroup'=>array('app'),
                'app' => 'godthread',
                'cmd' => $cmd, // 'pbunlike'
                'uid' => intval($user_id),
                'forum_id' => intval($forum_id),
                'thread_id' => intval($thread_id),
                'post_id' => intval($post_id),
                'ip' => intval($user_ip), // ip2long($ip)
                'dstuid' => intval($dst_user_id),
            ),);

        $objMulti->register(
            'antiActsctrlSubmit',
            new Tieba_Service('anti'),
            array(
                'serviceName' => 'anti',
                'method'      => 'antiActsctrlSubmit',
                'input'       => $arrActsctrlInput,
            )
        );

        $portrait = Tieba_Ucrypt::encode($dst_user_id, $dst_user_name);
        $headPhoto = 'http://tb.himg.baidu.com/sys/portrait/item/'.$portrait;
        $arrAntiCommitInput = array('anti_cmd'=>'recallApp',
            "req" => array(
                "user_id" => $user_id, 
                "user_name" => $user_name,
                "forum_id" => $forum_id,
                "word" => '',
                "content" => $post_content,//����������������
                "app_key" => 14,//רΪ����ҵ��ָ��
                "ip" => $user_ip,//�û����β���ip
                "thread_id" => $thread_id,//����id,û����0
                "post_id" => $post_id,//����id��û����0
                "user_id_ed" => $dst_user_id,//�����û�id
                "user_name_ed" => $dst_user_name,//�����û���
                "photo_list" => array($headPhoto), //ͷ��url�б�
            ),
        );

        $objMulti->register(
            'antiCommit',
            new Tieba_Service('anti'),
            array(
                'serviceName' => 'anti',
                'method'      => 'antiCommit',
                'input'       => $arrAntiCommitInput,
            )
        );

        $arrRet = $objMulti->call();

        $ret = true;
        foreach($arrRet as $key => $val)
        {
            if ($val['errno'] !== Tieba_Errcode::ERR_SUCCESS)
            {
                $ret = false;
                Bingo_Log::warning("call $key fail,the output is ".serialize($val));
            }
        }
        return $ret;
    }
}
