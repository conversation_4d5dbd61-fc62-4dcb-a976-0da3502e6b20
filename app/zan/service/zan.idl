//@description: 赞 service
//@author: cxzhp

struct queryIsLiked{
	uint64_t user_id;
	uint64_t post_id;
	uint64_t batch_key_name =  optinal();//批量返回结果数组中，数组的key,方便快速定位到value
	string batch_key_name =  optinal();//批量返回结果数组中，数组的key,方便快速定位到value
};


service zan
{

	void batchIsLiked(queryIsLiked input[]);
	void isLiked(uint64_t user_id, uint64_t post_id);
	void like(uint64_t post_id, uint64_t thread_id, uint64_t user_id, uint64_t author_id);
	void unlike(uint64_t post_id, uint64_t thread_id, uint64_t user_id, uint64_t author_id);
	void getListByPostId(uint64_t post_id, uint32_t start =optional(), uint32_t offset =optional(),  out uint64_t user_list[]);
	void getListByAuthorId(uint64_t user_id, uint32_t start =optional(), uint32_t offset =optional(),  out uint64_t post_list[]);
	
};
