<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014:09:05 11:27:30
 * @breif 官方吧认证相关
 * @structs & methods(copied from idl.)
*/
define("MODULE", "Official_dl");
class Dl_Dbforumgcon_Dbverify {
    private static $_db         = null;
    const SERVICE_NAME          = "Dl_Dbforumgcon_Dbverify";
    protected static $_conf     = null;
    const DATABASE_NAME         = "forum_gcon";
    const DATABASE_NAME_CHECK   = "forum_gcon_check";
    const FIRM_TABLE            = 'firm_verify';
    const AGENCY_TABLE          = 'agency_verify';
    const AGENCY_SET_TABLE      = 'agency_set';
    const FIRM_AGENCY_TABLE     = 'firm_agency_relation';
    const AGENCY_DATA_TABLE     = 'agency_forum_data';
    CONST AGENCY_INCOME_TABLE   = 'agency_income';
    const FOURTHMAN_TABLE       = 'identify';
    const AGENCY_WARNING_TABLE  = 'agency_warning';
    const AGENCY_EXAMS          = 'agencyExams';
    const OP_RECORD             = 'opRecord';

    /**
     * @brief init
     * @return: true if success. false if fail.
    **/		
    private static function _init() {
        if (self::$_conf == null) {
            self::$_conf = Bd_Conf::getConf("/app/official/dl_official");
            if (self::$_conf == false) {
                Bingo_Log::warning("init get official dl conf fail.");
                return false;
            }
        }
        return true; 
    }

    private static function _errRet($errno) {
        return array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    public static function preCall($arrInput) {
        // pre-call hook
    }

    public static function postCall($arrInput) {
        // post-call hook
    }
    private function _getAllField($table = 'firm_verify') {
        if ($table == 'firm_verify') {
            return array(
                'all_field'   => array(
                    'firm_id', 'firm_name', 'create_user_id', 'create_user_name', 'address', 'firm_type',
                    'firm_license', 'license_num', 'official_letter', 'firm_website', 'material', 'legal_person', 'contacts',
                    'status', 'reason', 'create_time', 'org_level', 'op_time', 'op_user_id', 'op_user_name', 'extra',
                ),
                'nedd_serial' => array(
                    'address', 'firm_type', 'material', 'legal_person', 'contacts',
                ),
            );
        }
        if ($table == 'agency_verify') {
            return array(
                'all_field'   => array(
                    'agency_id', 'agency_name', 'create_user_id', 'create_user_name', 'address', 'firm_type',
                    'firm_license', 'license_num', 'official_letter', 'firm_website', 'material', 'legal_person', 'contacts',
                    'status', 'reason', 'create_time', 'org_level', 'op_time', 'op_user_id', 'op_user_name', 'agency_desc',
                    'weight', 'exams_score', 'extra', 'is_key_agency', 'key_agency_time', 'medal_type',
                ),
                'nedd_serial' => array(
                    'address', 'firm_type', 'material', 'legal_person', 'contacts',
                ),   
                
            ); 
        }
        if ($table == 'firm_agency_relation') {
            return array(
                'all_field'   => array(
                    'id', 'firm_id', 'agency_id', 'forum_id', 'firm_name', 'agency_name',
                    'forum_name', 'fourth_man_uid', 'v_name', 'fourth_man_uname', 'v_explain', 'agreement', 'audit_time',
                    'expire_time', 'reason', 'status', 'create_time', 'create_user_id', 'create_user_name', 'op_time', 'op_user_id',
                    'op_user_name', 'weight', 'extra', 'is_key_forum', 'key_forum_time',
                ),
                'nedd_serial' => array(
                ),   
                
            ); 
        }
        if ($table == 'agency_set') {
            return array(
                'all_field'   => array(
                    'id', 'agency_id', 'cur_week_score', 'last_week_score', 'cheat_score',
                    'display_forums','op_time', 'op_user_id', 'op_user_name',
                ),
                'nedd_serial' => array(
                    'display_forums',
                ),   
                
            ); 
        }
        if ($table == 'agency_warning') {
            return array(
                'all_field'   => array(
                    'warn_id', 'agency_id', 'read_status', 'title', 'warning',
                    'op_time',
                ),
                'nedd_serial' => array(
                ),   
                
            ); 
        }
        if ($table == 'opRecord') {
            return array(
                'all_field'   => array(
                    'id', 'relate_type', 'relate_id', 'op_user', 'op_object', 'op_type', 'op_time', 'op_content',
                ),
                'nedd_serial' => array(),
            );
        }
        if ($table == 'agency_income') {
            return array(
                'all_field'   => array(
                    'id', 'forum_id', 'forum_name', 'agency_id', 'agency_status', 'income_time', 'income_type', 'income_sum', 'contract_no', 'status', 'create_time', 'create_user_id', 'create_user_name','op_time', 'op_user_id', 'op_user_name',
                ),
                'nedd_serial' => array(),
            );
        }
    }

    public function createFirmInfo($arrInput = array()) {
        $allField = self::_getAllField(self::FIRM_TABLE);
        foreach ($allField['nedd_serial'] as $field) {
            if (isset($arrInput["$field"])) {
                $arrInput["$field"] = serialize($arrInput["$field"]);
            }
        }
        $insertField = array();
        foreach ($allField['all_field'] as $val) {
            if (isset($arrInput["$val"])) {
                $insertField["$val"] = $arrInput["$val"];
            }
        }
        $insertInput = array(
            'table' => self::FIRM_TABLE,
            'field' => $insertField,
        );
        $res = Util_Db::insert($insertInput);
        return $res;
    }
    
    public function updateFirmInfoByFirmId($arrInput = array()) {
        $needFieldArr = array('firm_id');
        $checkRes = Util_Function::checkArrMustField($arrInput, $needFieldArr);
        if (false === $checkRes) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $allField = self::_getAllField(self::FIRM_TABLE);
        foreach ($allField['nedd_serial'] as $fval) {
            if (isset($arrInput['field'][$fval])) {
                $arrInput['field'][$fval] = serialize($arrInput['field'][$fval]);
            }
        }
        $field = array();
        foreach ($allField['all_field'] as $aval) {
            if (isset($arrInput['field'][$aval])) {
                $field[$aval.'='] = $arrInput['field'][$aval];
            }
        }
        if (empty($field)) {
            Bingo_Log::warning("input params lack field param:". serialize($arrInput));
            return false;
        }
        $updateInput = array(
            'table' => self::FIRM_TABLE,
            'field' => $field,
            'cond'  => array(
                "firm_id=" => intval($arrInput['firm_id']),
            ),
        );
        $updateRes = Util_Db::update($updateInput);
        return $updateRes;
    }
    
    public function getFirmInfoByCond($arrInput = array()) {
        $needFieldArr = array('field', 'cond');
        $checkRes = Util_Function::checkArrMustField($arrInput, $needFieldArr);
        if (false === $checkRes) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $selectInput = array(
            'table' => self::FIRM_TABLE,
            'field' => $arrInput['field'],
            'cond'  => $arrInput['cond']
        );
        if (isset($arrInput['append'])) {
            $selectInput['append'] = $arrInput['append'];
        }
        $selectRes = Util_Db::select($selectInput);
        if (!empty($selectRes['data'])) {
            $allField = self::_getAllField(self::FIRM_TABLE);
            $selectRes['data'][0] = Util_Function::unserializeArr($selectRes['data'][0], $allField['nedd_serial']);
        }
        return $selectRes;
    }
    
    public function mgetFirmInfoByCond($arrInput = array()) {
        $needFieldArr = array('field', 'cond');
        $checkRes = Util_Function::checkArrMustField($arrInput, $needFieldArr);
        if (false === $checkRes) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $selectInput = array(
            'table' => self::FIRM_TABLE,
            'field' => $arrInput['field'],
            'cond'  => $arrInput['cond'],
        );
        if (isset($arrInput['append'])) {
            $selectInput['append'] = $arrInput['append'];
        }
        $selectRes = Util_Db::select($selectInput);
        if (!empty($selectRes['data'])) {
            $allField = self::_getAllField(self::FIRM_TABLE);
            foreach ($selectRes['data'] as $key => $value) {
                $selectRes['data'][$key] = Util_Function::unserializeArr($selectRes['data'][$key], $allField['nedd_serial']);
            }
            
        }
        return $selectRes;
    }
    
    
    public function getFirmList($arrInput = array()) {
        $offset = isset($arrInput['offset']) ? intval($arrInput['offset']) : 0;
        $limit  = isset($arrInput['limit']) ? intval($arrInput['limit']) : 20;
        $cond["1="] = 1;
      
        if (isset($arrInput['cond'])) {
            $cond = $arrInput['cond'];
        }
        $totalInput = array(
            'table' => self::FIRM_TABLE,
            'field' => array(
                'firm_id',
            ),
            'cond'  => $cond,
        );
        $totalRes = Util_Db::select($totalInput);
        if (false === $selectRes || Tieba_Errcode::ERR_SUCCESS != $selectRes['errno']) {
            Bingo_Log::fatal("get forum list fail param:". serialize($totalInput));
            return false;
        }
        $selectInput = array(
            'table' => self::FIRM_TABLE,
            'field' => array(
                '*',
            ),
            'cond'  => $cond,
            'append' => array(
                "limit $offset, $limit",
            ),
        );
        if (isset($arrInput['append'])) {
            $selectInput['append'] = array_merge($arrInput['append'], $selectInput['append']);
        }
        if (isset($arrInput['field'])) {
            $selectInput['field'] = $arrInput['field'];
        }
        $selectRes = Util_Db::select($selectInput);
        if (false === $selectRes || Tieba_Errcode::ERR_SUCCESS != $selectRes['errno']) {
            Bingo_Log::fatal("get forum list fail param:". serialize($selectInput));
            return false;
        }
        $allField = self::_getAllField(self::FIRM_TABLE);
        foreach ($selectRes['data'] as &$val) {
            $val = Util_Function::unserializeArr($val, $allField['nedd_serial']);
        }
        return array(
            'total' => count($totalRes['data']),
            'list'  => $selectRes['data'],
        );
    }
    
    
    //getRelationList
    public function getRelationList($arrInput = array()) {
        $offset = isset($arrInput['offset']) ? intval($arrInput['offset']) : 0;
        $limit  = isset($arrInput['limit']) ? intval($arrInput['limit']) : 20;
        $cond = $arrInput['cond'];
        $totalInput = array(
            'table' => self::FIRM_AGENCY_TABLE,
            'field' => array(
                'id',
            ),
            'cond'  => $cond,
        );
        $totalRes = Util_Db::select($totalInput);
        if (false === $selectRes || Tieba_Errcode::ERR_SUCCESS != $selectRes['errno']) {
            Bingo_Log::fatal("get relation total fail param:". serialize($totalInput));
            return false;
        }
        $selectInput = array(
            'table' => self::FIRM_AGENCY_TABLE,
            'field' => array(
                '*',
            ),
            'cond'  => $cond,
            'append' => array(
                "limit $offset, $limit",
            ),
        );
        if (isset($arrInput['append'])) {
            $selectInput['append'] = array_merge($arrInput['append'], $selectInput['append']);
        }
        if (isset($arrInput['field'])) {
            $selectInput['field'] = $arrInput['field'];
        }
        
        $selectRes = Util_Db::select($selectInput);
        if (false === $selectRes || Tieba_Errcode::ERR_SUCCESS != $selectRes['errno']) {
            Bingo_Log::fatal("get relation list fail param:". serialize($selectInput));
            return false;
        }
        return array(
            'total' => count($totalRes['data']),
            'list'  => $selectRes['data'],
        );
    }
    
    
    
    public function createAgencyInfo($arrInput = array()) {
        $allField = self::_getAllField(self::AGENCY_TABLE);
        foreach ($allField['nedd_serial'] as $field) {
            if (isset($arrInput["$field"])) {
                $arrInput["$field"] = serialize($arrInput["$field"]);
            }
        }
        $insertField = array();
        foreach ($allField['all_field'] as $val) {
            if (isset($arrInput["$val"])) {
                $insertField["$val"] = $arrInput["$val"];
            }
        }
        $insertInput = array(
            'table' => self::AGENCY_TABLE,
            'field' => $insertField,
        );
        $res = Util_Db::insert($insertInput);
        $intAgencyId = $res['data'];
        
        $setInsertInput = array(
            'table' => self::AGENCY_SET_TABLE,
            'field' => array(
                'agency_id' => $intAgencyId,
            ),
        );
        $res = Util_Db::insert($setInsertInput);
        return $res;
    }

    public function getAgencyInfoByCond($arrInput = array()) {
        $needFieldArr = array('field', 'cond');
        $checkRes = Util_Function::checkArrMustField($arrInput, $needFieldArr);
        if (false === $checkRes) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $selectInput = array(
            'table' => self::AGENCY_TABLE,
            'field' => $arrInput['field'],
            'cond'  => $arrInput['cond']
        );
        if (isset($arrInput['append'])) {
            $selectInput['append'] = $arrInput['append'];
        }
        $selectRes = Util_Db::select($selectInput);
        if (!empty($selectRes['data'])) {
            $allField = self::_getAllField(self::AGENCY_TABLE);
            foreach ($selectRes['data'] as &$value) {
                $value = Util_Function::unserializeArr($value, $allField['nedd_serial']);
            }
        }
        return $selectRes;
    }
    
    public function getAgencySetByCond($arrInput = array()) {
        $needFieldArr = array('field', 'cond');
        $checkRes = Util_Function::checkArrMustField($arrInput, $needFieldArr);
        if (false === $checkRes) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $selectInput = array(
            'table' => self::AGENCY_SET_TABLE,
            'field' => $arrInput['field'],
            'cond'  => $arrInput['cond']
        );
        if (isset($arrInput['append'])) {
            $selectInput['append'] = $arrInput['append'];
        }
        $selectRes = Util_Db::select($selectInput);
        if (!empty($selectRes['data'])) {
            $allField = self::_getAllField(self::AGENCY_SET_TABLE);
            foreach ($selectRes['data'] as &$value) {
                $value = Util_Function::unserializeArr($value, $allField['nedd_serial']);
            }
        }
        return $selectRes;
    }
    
    public function updateAgencyInfoByAid($arrInput = array()) {
        $needFieldArr = array('agency_id');
        $checkRes = Util_Function::checkArrMustField($arrInput, $needFieldArr);
        if (false === $checkRes) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $allField = self::_getAllField(self::AGENCY_TABLE);
        foreach ($allField['nedd_serial'] as $fval) {
            if (isset($arrInput['field'][$fval])) {
                $arrInput['field'][$fval] = serialize($arrInput['field'][$fval]);
            }
        }
        $field = array();
        foreach ($allField['all_field'] as $aval) {
            if (isset($arrInput['field'][$aval])) {
                $field[$aval.'='] = $arrInput['field'][$aval];
            }
        }
        if (empty($field)) {
            Bingo_Log::warning("input params lack field param:". serialize($arrInput));
            return false;
        }
        $updateInput = array(
            'table' => self::AGENCY_TABLE,
            'field' => $field,
            'cond'  => array(
                "agency_id=" => intval($arrInput['agency_id']),
            ),
        );
        $updateRes = Util_Db::update($updateInput);
        return $updateRes;
    }
    //get all exams
    public function getAgencyExams() {
        $selectInput = array(
            'table' => self::AGENCY_EXAMS,
            'field' => array('*'),
            'cond'  => array(
                "1=" => 1,
            ),
        );
        $selectRes = Util_Db::select($selectInput);
        if ($selectRes['data']) {
            foreach ($selectRes['data'] as &$val) {
                $val = Util_Function::unserializeArr($val, array('op_list'));
            }
        }
        return $selectRes;
    }
    //get exams answer
    public function getExamsAnswerByIds($idArr = array()) {
        $selectInput = array(
            'table' => self::AGENCY_EXAMS,
            'field' => array('*'),
            'cond'  => array(
                "id" => $idArr,
            ),
        );
        $selectRes = Util_Db::select($selectInput);
        return $selectRes;
    }
    
    public function getAgencyList($arrInput = array()) {
        $offset = isset($arrInput['offset']) ? intval($arrInput['offset']) : 0;
        $limit  = isset($arrInput['limit']) ? intval($arrInput['limit']) : 20;
        $status = isset($arrInput['status']) ? intval($arrInput['status']) : 0;
        $cond["1="] = 1;
        if (isset($arrInput['status'])) {
            $cond['status='] = intval($arrInput['status']);
        }
        
        if (isset($arrInput['cond'])) {
            $cond = $arrInput['cond'];
        }
        if (isset($arrInput['word'])) {
            $word =  $arrInput['word'];
            $cond['concat(`create_user_name`, `agency_name`) like'] =  ' %'.$word.'%';
        }
        $totalInput = array(
            'table' => self::AGENCY_TABLE,
            'field' => array(
                'agency_id',
            ),
            'cond'  => $cond,
        );
        $totalRes = Util_Db::select($totalInput);
        if (false === $totalRes || Tieba_Errcode::ERR_SUCCESS != $totalRes['errno']) {
            Bingo_Log::fatal("get forum list fail param:". serialize($totalInput));
            return false;
        }
        $selectInput = array(
            'table' => self::AGENCY_TABLE,
            'field' => array(
                '*',
            ),
            'cond'  => $cond,
            'append' => array(
                "limit $offset, $limit",
            ),
        );
        if (isset($arrInput['append'])) {
            $selectInput['append'] = array_merge($arrInput['append'], $selectInput['append']);
        }
        if (isset($arrInput['field'])) {
            $selectInput['field'] = $arrInput['field'];
        }
        $selectRes = Util_Db::select($selectInput);
        if (false === $selectRes || Tieba_Errcode::ERR_SUCCESS != $selectRes['errno']) {
            Bingo_Log::fatal("get forum list fail param:". serialize($selectInput));
            return false;
        }
        if (empty($selectRes['data'])) {
            return array(
                'total' => 0,
                'list'  => array(),
            );  
        }
        //get all agency_set info
        $aidArr = array();
        foreach ($selectRes['data'] as $val) {
           $aidArr[] = $val['agency_id'];
        }
        $setInput = array(
            'table' => self::AGENCY_SET_TABLE,
            'field' => array(
                '*',
            ),
            'cond'  => array(
                'agency_id' => $aidArr,
            ),
        );
        $setRes = Util_Db::select($setInput);
        if (false === $setRes || Tieba_Errcode::ERR_SUCCESS != $setRes['errno']) {
            Bingo_Log::fatal("get agency set list fail param:". serialize($setInput));
            return false;
        }
        $setArr = array();
        foreach ($setRes['data'] as $val) {
            $setArr[$val['agency_id']] = $val;
        }
        $agencyField = self::_getAllField(self::AGENCY_TABLE);
        foreach ($selectRes['data'] as &$val) {
            $val['agency_set'] = $setArr[$val['agency_id']];
            $val = Util_Function::unserializeArr($val, $agencyField['nedd_serial']);
        }
        return array(
            'total' => count($totalRes['data']),
            'list'  => $selectRes['data'],
        );
    }

    public function getMonitorAgencyList($arrInput = array()) {
        $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if($objTbMysql === false || $objTbMysql->isConnected() === false) {
            Bingo_Log::fatal("forum_gcon db connect fail.");
            return false;
        }
        $pn  = isset($arrInput['pn']) ? intval($arrInput['pn']) : 1;
        $limit = isset($arrInput['limit']) ? intval($arrInput['limit']) : 20;
        $offset = ($pn - 1)*$limit;

        $status = implode(',', $arrInput['status']);
        $preSql = "SELECT a.*,count(r.forum_id) as forum_count from agency_verify as a
                   LEFT JOIN firm_agency_relation as r
                   ON a.agency_id = r.agency_id
                   GROUP BY r.agency_id
                   HAVING status in ($status)";
        $commonSql = "";
        //重点标识
        if (isset($arrInput['is_key_agency']) && intval($arrInput['is_key_agency']) == 1) {
            $keyAgency = intval($arrInput['is_key_agency']);
            $commonSql .= " and is_key_agency = $keyAgency ";
        }
        //不活跃公司
        if (isset($arrInput['is_inactive_agency']) && intval($arrInput['is_inactive_agency']) == 1) {
            $commonSql .= ' and count(r.forum_id)=0 ';
        }
        if (isset($arrInput['agency_name']) && trim($arrInput['agency_name'])) {
            $aname = mysql_escape_string(trim($arrInput['agency_name']));
            $commonSql .= " and `agency_name` like '%$aname%' ";
        }
        $limit = " limit $offset, $limit";
        //总数
        $totalSql = $preSql . $commonSql;
        $totalRes = $objTbMysql->query($totalSql);
        if ($totalRes === false) {
            Bingo_Log::fatal("query total agency failed, sql=:" . $totalSql);
            return false;
        }
        //Query list data.
        $listSql = $preSql . $commonSql;
        if (!isset($arrInput['is_select_all']) || 1 !== $arrInput['is_select_all']) {
            $listSql .= $limit;
        }

        $data = $objTbMysql->query($listSql);
        if (false === $data) {
            Bingo_Log::fatal("query list agency failed, sql=:" . $listSql);
            return false;
        }

        //Query week score.
        foreach ($data as $key => &$val) {
            $condScore['agency_id='] = $val['agency_id'];
            $selScoreInput = array(
                    'table' => self::AGENCY_SET_TABLE,
                    'field' => array(
                            '*',
                    ),
                    'cond'  => $condScore,
            );

            $selectScoreRes = Util_Db::select($selScoreInput);
            if (false === $selectScoreRes || Tieba_Errcode::ERR_SUCCESS != $selectScoreRes['errno']) {
                Bingo_Log::fatal("get agency score info fail param:". serialize($selScoreInput));
                return false;
            }

            $val['cur_week_score'] = $selectScoreRes['data'][0]['cur_week_score'];
            $val['last_week_score'] = $selectScoreRes['data'][0]['last_week_score'];
            $val['cheat_score'] = $selectScoreRes['data'][0]['cheat_score'];
            
            if (isset($arrInput['order_by'])) {
                $sortKey[] = $data[$key][$arrInput['order_by']];
            }
        }

        if (isset($arrInput['order_by'])) {
            if (intval($arrInput['sort_order'])==1) {
                $order = SORT_DESC;
            }
            else {
                $order = SORT_ASC;
            }
            array_multisort($sortKey, $order, $data);
        }

        return array(
                'data'  => $data,
                'total' => count($totalRes),
        );

    }

    //arrinput must need to file in ui
    public function createFirmRelation($arrInput = array()) {
        if (empty($arrInput)) {
            Bingo_Log::fatal("param is empty param:". serialize($arrInput));
            return false;
        }
        $field = array();
        foreach ($arrInput as $key =>  $val) {
            $field[$key] = $val;
        }
        $insertInput = array(
            'table' => self::FIRM_AGENCY_TABLE   ,
            'field' => $field,
        );
        $res = Util_Db::insert($insertInput);
        return $res;
    }
   
    public function getRelationByCond($arrInput) {
        $needFieldArr = array('field', 'cond');
        $checkRes = Util_Function::checkArrMustField($arrInput, $needFieldArr);
        if (false === $checkRes) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $updateInput = array(
            'table' => self::FIRM_AGENCY_TABLE,
            'field' => $arrInput['field'],
            'cond'  => $arrInput['cond']
        );
        if (isset($arrInput['append'])) {
            $updateInput['append'] = $arrInput['append'];
        }
        $updateRes = Util_Db::select($updateInput);
        return $updateRes;
    }
    
    public function getFourthManInfoByCond($arrInput) {
        $needFieldArr = array('field', 'cond');
        $checkRes = Util_Function::checkArrMustField($arrInput, $needFieldArr);
        if (false === $checkRes) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $updateInput = array(
            'table' => self::FOURTHMAN_TABLE,
            'field' => $arrInput['field'],
            'cond'  => $arrInput['cond']
        );
        if (isset($arrInput['append'])) {
            $updateInput['append'] = $arrInput['append'];
        }
        $updateRes = Util_Db::select($updateInput);
        return $updateRes;
    }
    
    public function getFourthManInfoByIds($idArr = array()) {
        $selectInput = array(
            'table' => self::FOURTHMAN_TABLE,
            'field' => array('*'),
            'cond'  => array(
                "forum_id" => $idArr,
                "status="   => 1,
            ),
        );
        $selectRes = Util_Db::select($selectInput);
        return $selectRes;
    }
    
    public function getRelationByAid($arrInput = array()) {
        $offset = isset($arrInput['offset']) ? intval($arrInput['offset']) : 0;
        $limit  = isset($arrInput['limit']) ? intval($arrInput['limit']) : 20;
        $cond["agency_id="] = intval($arrInput['agency_id']);
        if (isset($arrInput['status'])) {
            $status = $arrInput['status'];
            
            if (is_array($arrInput['status'])) {
                $cond["status"] = $status;
            } else {
                $cond["status="] = intval($status);
            }
        }
        $totalInput = array(
            'table' => self::FIRM_AGENCY_TABLE,
            'field' => array(
                'id',
            ),
            'cond'  => $cond,
        );
        $totalRes = Util_Db::select($totalInput);
        if (false === $selectRes || Tieba_Errcode::ERR_SUCCESS != $selectRes['errno']) {
            Bingo_Log::fatal("get relation by agency_id total fail param:". serialize($totalInput));
            return false;
        }
        $selectInput = array(
            'table' => self::FIRM_AGENCY_TABLE,
            'field' => array(
                '*',
            ),
            'cond'  => $cond,
            'append' => array(
                "order by create_time desc",
                "limit $offset, $limit",
            ),
        );
        if (isset($arrInput['append'])) {
            $selectInput['append'] = $arrInput['append'];
        }
        if (isset($arrInput['field'])) {
            $selectInput['field'] = $arrInput['field'];
        }
        $selectRes = Util_Db::select($selectInput);
        if (false === $selectRes || Tieba_Errcode::ERR_SUCCESS != $selectRes['errno']) {
            Bingo_Log::fatal("get relation list by agency_id fail param:". serialize($selectInput));
            return false;
        }
        return array(
            'total' => count($totalRes['data']),
            'list'  => $selectRes['data'],
        );
    }
    
    /*public function getRelationInfoByAid($arrInput = array()) {
        $offset = isset($arrInput['offset']) ? intval($arrInput['offset']) : 0;
        $limit  = isset($arrInput['limit']) ? intval($arrInput['limit']) : 20;
        $cond["agency_id="] = intval($arrInput['agency_id']);
        if (isset($arrInput['status'])) {
            $status = $arrInput['status'];
            
            if (is_array($arrInput['status'])) {
                $cond["status"] = $status;
            } else {
                $cond["status="] = intval($status);
            }
        }
        $totalInput = array(
            'table' => self::FIRM_AGENCY_TABLE,
            'field' => array(
                'id',
            ),
            'cond'  => $cond,
        );
        $totalRes = Util_Db::select($totalInput);
        if (false === $selectRes || Tieba_Errcode::ERR_SUCCESS != $selectRes['errno']) {
            Bingo_Log::fatal("get relation by agency_id total fail param:". serialize($totalInput));
            return false;
        }
        $selectInput = array(
            'table' => self::FIRM_AGENCY_TABLE,
            'field' => array(
                '*',
            ),
            'cond'  => $cond,
            'append' => array(
                "order by create_time desc",
                //"limit $offset, $limit",
            ),
        );
        if (isset($arrInput['append'])) {
            $selectInput['append'] = $arrInput['append'];
        }
        if (isset($arrInput['field'])) {
            $selectInput['field'] = $arrInput['field'];
        }
        $selectRes = Util_Db::select($selectInput);
        if (false === $selectRes || Tieba_Errcode::ERR_SUCCESS != $selectRes['errno']) {
            Bingo_Log::fatal("get relation list by agency_id fail param:". serialize($selectInput));
            return false;
        }
        return array(
            'total' => count($totalRes['data']),
            'list'  => $selectRes['data'],
        );
    }
    */
    
    public function updateRelationByCond($arrInput = array()) {
        $needFieldArr = array('field', 'cond');
        $checkRes = Util_Function::checkArrMustField($arrInput, $needFieldArr);
        if (false === $checkRes) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        
        $allField = self::_getAllField(self::FIRM_AGENCY_TABLE);
        foreach ($allField['nedd_serial'] as $fval) {
            if (isset($arrInput['field'][$fval])) {
                $arrInput['field'][$fval] = serialize($arrInput['field'][$fval]);
            }
        }
        $field = array();
        foreach ($allField['all_field'] as $aval) {
            if (isset($arrInput['field'][$aval])) {
                $field[$aval.'='] = $arrInput['field'][$aval];
            }
        }
        if (empty($field)) {
            Bingo_Log::warning("input params lack field param:". serialize($arrInput));
            return false;
        }
        
        $updateInput = array(
            'table' => self::FIRM_AGENCY_TABLE,
            'field' => $field,
            'cond'  => $arrInput['cond'],
        );
        $updateRes = Util_Db::update($updateInput);
        return $updateRes;
    }
    
    public function updateFirmByCond($arrInput = array()) {
        $needFieldArr = array('field', 'cond');
        $checkRes = Util_Function::checkArrMustField($arrInput, $needFieldArr);
        if (false === $checkRes) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        
        $allField = self::_getAllField(self::FIRM_TABLE);
        foreach ($allField['nedd_serial'] as $fval) {
            if (isset($arrInput['field'][$fval])) {
                $arrInput['field'][$fval] = serialize($arrInput['field'][$fval]);
            }
        }
        $field = array();
        foreach ($allField['all_field'] as $aval) {
            if (isset($arrInput['field'][$aval])) {
                $field[$aval.'='] = $arrInput['field'][$aval];
            }
        }
        if (empty($field)) {
            Bingo_Log::warning("input params lack field param:". serialize($arrInput));
            return false;
        }
         
        $updateInput = array(
            'table' => self::FIRM_TABLE,
            'field' => $field,
            'cond'  => $arrInput['cond'],
        );
        $updateRes = Util_Db::update($updateInput);
        return $updateRes;
    }
    
    public function updateAgencyByCond($arrInput = array()) {
        $needFieldArr = array('field', 'cond');
        $checkRes = Util_Function::checkArrMustField($arrInput, $needFieldArr);
        if (false === $checkRes) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        
        $allField = self::_getAllField(self::AGENCY_TABLE);
        foreach ($allField['nedd_serial'] as $fval) {
            if (isset($arrInput['field'][$fval])) {
                $arrInput['field'][$fval] = serialize($arrInput['field'][$fval]);
            }
        }
        $field = array();
        foreach ($allField['all_field'] as $aval) {
            if (isset($arrInput['field'][$aval])) {
                $field[$aval.'='] = $arrInput['field'][$aval];
            }
        }
        if (empty($field)) {
            Bingo_Log::warning("input params lack field param:". serialize($arrInput));
            return false;
        }
        
        $updateInput = array(
            'table' => self::AGENCY_TABLE,
            'field' => $field,
            'cond'  => $arrInput['cond'],
        );
        $updateRes = Util_Db::update($updateInput);
        return $updateRes;
    }
    
    public function updateAgencySetByCond($arrInput = array()) {
        $needFieldArr = array('field', 'cond');
        $checkRes = Util_Function::checkArrMustField($arrInput, $needFieldArr);
        if (false === $checkRes) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        
        $allField = self::_getAllField(self::AGENCY_SET_TABLE);
        foreach ($allField['nedd_serial'] as $fval) {
            if (isset($arrInput['field'][$fval])) {
                $arrInput['field'][$fval] = serialize($arrInput['field'][$fval]);
            }
        }
        $field = array();
        foreach ($allField['all_field'] as $aval) {
            if (isset($arrInput['field'][$aval])) {
                $field[$aval.'='] = $arrInput['field'][$aval];
            }
        }
        if (empty($field)) {
            Bingo_Log::warning("input params lack field param:". serialize($arrInput));
            return false;
        }
        
        $updateInput = array(
            'table' => self::AGENCY_SET_TABLE,
            'field' => $field,
            'cond'  => $arrInput['cond'],
        );
        $updateRes = Util_Db::update($updateInput);
        return $updateRes;
    }
    
    
    public function updateAgencyWarningByCond($arrInput = array()) {
        $needFieldArr = array('field', 'cond');
        $checkRes = Util_Function::checkArrMustField($arrInput, $needFieldArr);
        if (false === $checkRes) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        
        $allField = self::_getAllField(self::AGENCY_WARNING_TABLE);
        foreach ($allField['nedd_serial'] as $fval) {
            if (isset($arrInput['field'][$fval])) {
                $arrInput['field'][$fval] = serialize($arrInput['field'][$fval]);
            }
        }
        $field = array();
        foreach ($allField['all_field'] as $aval) {
            if (isset($arrInput['field'][$aval])) {
                $field[$aval.'='] = $arrInput['field'][$aval];
            }
        }
        if (empty($field)) {
            Bingo_Log::warning("input params lack field param:". serialize($arrInput));
            return false;
        }
         
        $updateInput = array(
            'table' => self::AGENCY_WARNING_TABLE,
            'field' => $field,
            'cond'  => $arrInput['cond'],
        );
        $updateRes = Util_Db::update($updateInput);
        return $updateRes;
    }
 
    public function getTopAgencyInfo($arrInput = array()) {
        $needFieldArr = array('field', 'cond');
        $checkRes = Util_Function::checkArrMustField($arrInput, $needFieldArr);
        if (false === $checkRes) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $selectInput = array(
            'table' => self::AGENCY_TABLE,
            'field' => $arrInput['field'],
            'cond'  => $arrInput['cond'],
            //'append' => arrInput['append'],
        );
        if (isset($arrInput['append'])) {
            $selectInput['append'] = $arrInput['append'];
        }
        
        $selectRes = Util_Db::select($selectInput);
        if (!empty($selectRes['data'])) {
            $allField = self::_getAllField(self::AGENCY_TABLE);
            foreach ($selectRes['data'] as &$value) {
                $value = Util_Function::unserializeArr($value, $allField['nedd_serial']);
            }
        }
        return $selectRes;
    }
    
    public function getAgencyWarningByCond($arrInput = array()) {
        $needFieldArr = array('field', 'cond');
        $checkRes = Util_Function::checkArrMustField($arrInput, $needFieldArr);
        if (false === $checkRes) {
            Bingo_Log::warning("input params invalid. [".serialize($arrInput)."]");
            return false;
        }
        $updateInput = array(
            'table' => self::AGENCY_WARNING_TABLE,
            'field' => $arrInput['field'],
            'cond'  => $arrInput['cond']
        );
        if (isset($arrInput['append'])) {
            $updateInput['append'] = $arrInput['append'];
        }
        $updateRes = Util_Db::select($updateInput);
        return $updateRes;
    }
    
    //agency income
    public function getAgencyIncomeByCond($arrInput = array()) {
        $intOffset = isset($arrInput['offset']) ? intval($arrInput['offset']) : 0;
        $intLimit = isset($arrInput['limit']) ? intval($arrInput['limit']) : 20;
        $arrTotalInput = array(
            'table' => self::AGENCY_INCOME_TABLE,
            'field' => $arrInput['field'],
            'cond' => $arrInput['cond'],
            'append' => $arrInput['append'],
        );
        $arrTotalRes = Util_Db::select($arrTotalInput);
        if (false === $arrTotalRes || Tieba_Errcode::ERR_SUCCESS != $arrTotalRes['errno']) {
            Bingo_Log::warning("get forum list fail param:". serialize($arrTotalInput));
            return false;
        }
       
        $arrSelectInput = array(
            'table' => self::AGENCY_INCOME_TABLE,
            'field' => $arrInput['field'],
            'cond'  => $arrInput['cond'],
        );
        if (isset($arrInput['append'])) {
            $arrSelectInput['append'] = $arrInput['append'];//array_merge($arrInput['append'], array("limit $intOffset, $intLimit"));
        }
        $arrSelectRes = Util_Db::select($arrSelectInput);
        return array(
            'total' => count($arrTotalRes['data']),
            'list'  => $arrSelectRes['data'],
        );
    }
    
    public function getForumIncomeByCond($arrInput = array()) {
        $intOffset = isset($arrInput['offset']) ? intval($arrInput['offset']) : 0;
        $intLimit = isset($arrInput['limit']) ? intval($arrInput['limit']) : 20;
        $arrTotalInput = array(
            'table'  => self::AGENCY_INCOME_TABLE,
            'field'  => $arrInput['field'],
            'cond'   => $arrInput['cond'],
            'append' => $arrInput['append'],
        );
        $arrTotalRes = Util_Db::select($arrTotalInput);
        if (false === $arrTotalRes || Tieba_Errcode::ERR_SUCCESS != $arrTotalRes['errno']) {
            Bingo_Log::warning("get forum list fail param:". serialize($arrTotalInput));
            return false;
        }
       
        $arrSelectInput = array(
            'table' => self::AGENCY_INCOME_TABLE,
            'field' => $arrInput['field'],
            'cond'  => $arrInput['cond'],
        );
        if (isset($arrInput['append'])) {
            //$arrSelectInput['append'] = array_merge($arrInput['append'];
            $arrSelectInput['append'] = array_merge($arrInput['append'], array("limit $intOffset, $intLimit"));
        }
        $arrSelectRes = Util_Db::select($arrSelectInput);
        return array(
            'total' => count($arrTotalRes['data']),
            'list'  => $arrSelectRes['data'],
        );
    }
    
    
    public function getAgencyListOrderByForumNum($arrInput = array()) {
        $intStartTime = isset($arrInput['start_time']) ? intval($arrInput['start_time']) : 0;
        $intEndTime = isset($arrInput['end_time']) ? intval($arrInput['end_time']) : 0;
        $intOffset = isset($arrInput['offset']) ? intval($arrInput['offset']) : 0;
        $intLimit = isset($arrInput['limit']) ? intval($arrInput['limit']) : 20;
        $strOrder  = (intval($arrInput['order']) === 1) ? 'asc' : 'desc';
        $intAgencyStatus = isset($arrInput['agency_status']) ? intval($arrInput['agency_status']) : null;
        $strWord = isset($arrInput['word']) ? $arrInput['word'] : '';
        
        $strAgencyStatus = !is_null($intAgencyStatus) ? "where agency_verify.status = $intAgencyStatus" : 'where agency_verify.status in (2,5)';
        $strQueryWord = '';
        if (!empty($strWord)) {
            if (!empty($strAgencyStatus)) {
                $strQueryWord .= " and ";
            } /*else {
                $strQueryWord .= " where ";
            }*/
            $strQueryWord .= "(agency_verify.create_user_name like '%$strWord%' or agency_verify.agency_name like '%$strWord%')";
        }
        $strOptime = '';
        if ($intStartTime !== 0) {
            $strOptime .= "and agency_verify.op_time>$intStartTime";           
        }
        if ($intEndTime !== 0) {
            if ($intStartTime !== 0) {
                $strOptime .= " and ";
            }
            $strOptime .= "agency_verify.op_time<$intEndTime";         
        }
        
        $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if($objTbMysql === false || $objTbMysql->isConnected() === false) {
            Bingo_Log::fatal("forum_gcon db connect fail.");
            return false;
        }
        
        // $strTotalSql = "select count(forum_id) as forum_num,agency_id from firm_agency_relation where agency_id in (select agency_id from agency_verify where $strAgencyStatus $strQueryWord) and $strOptime and status in (1,3,5) group by agency_id";
        $strTotalSql = "select agency_verify.agency_id,count(case when forum_id is not null then 1 end) as forum_num from agency_verify left join firm_agency_relation on agency_verify.agency_id = firm_agency_relation.agency_id and firm_agency_relation.status in (1,3,5) $strAgencyStatus $strOptime  $strQueryWord group by agency_verify.agency_id";       
        $arrAgencyTotalRes = $objTbMysql->query($strTotalSql);
        $intTotal = count($arrAgencyTotalRes);
        
        //$strSql = "select count(forum_id) as forum_num,agency_id from firm_agency_relation where agency_id in (select agency_id from agency_verify where $strAgencyStatus $strQueryWord) and $strOptime and status in (1,3,5) group by agency_id order by forum_num $strOrder limit $intOffset, $intLimit";   
        $strSql = "select agency_verify.agency_id,count(case when forum_id is not null then 1 end) as forum_num from agency_verify left join firm_agency_relation on agency_verify.agency_id = firm_agency_relation.agency_id and firm_agency_relation.status in (1,3,5) $strAgencyStatus $strOptime    $strQueryWord group by agency_verify.agency_id order by forum_num $strOrder limit $intOffset, $intLimit";        
       
        $arrAgencyListRes = $objTbMysql->query($strSql);
        
        if (false === $arrAgencyRes) {
            Bingo_Log::fatal("query list agency failed, sql=:" . $strSql);
            //return false;
        }
        return array(
            'total' => $intTotal,
            'list'  => $arrAgencyListRes,
        );      
    }
    
    public function getAgencyForumDataByAid($arrInput = array()) {
        $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME_CHECK);
        if($objTbMysql === false || $objTbMysql->isConnected() === false) {
            Bingo_Log::fatal("forum_gcon db connect fail.");
            return false;
        }
        $offset = isset($arrInput['offset']) ? intval($arrInput['offset']) : 0;
        $limit  = isset($arrInput['limit']) ? intval($arrInput['limit']) : 20;
        
        $totalInput = array(
            'table' => self::AGENCY_DATA_TABLE,
            'db'    => $objTbMysql,
            'field' => array(
                'id',
            ),
            'cond'  => $arrInput['cond'],
        );
        $totalRes = Util_Db::select($totalInput);
        if (false === $selectRes || Tieba_Errcode::ERR_SUCCESS != $selectRes['errno']) {
            Bingo_Log::fatal("get relation by agency_id total fail param:". serialize($totalInput));
            return false;
        }
        $selectInput = array(
            'table' => self::AGENCY_DATA_TABLE,
            'db'    => $objTbMysql,
            'field' => array(
                '*',
            ),
            'cond'  => $arrInput['cond'],
        );
        if (isset($arrInput['append'])) {
            $selectInput['append'] = $arrInput['append'];
        }
        if (isset($arrInput['field'])) {
            $selectInput['field'] = $arrInput['field'];
        }
        $selectRes = Util_Db::select($selectInput);
        if (false === $selectRes || Tieba_Errcode::ERR_SUCCESS != $selectRes['errno']) {
            Bingo_Log::fatal("get agency_data fail param:". serialize($selectInput));
            return false;
        }
        return array(
            'total' => count($totalRes['data']),
            'list'  => $selectRes['data'],
        );
    }
    
    
    public function getAgencyForumDataByForumId($arrInput = array()) {
        $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME_CHECK);
        if($objTbMysql === false || $objTbMysql->isConnected() === false) {
            Bingo_Log::fatal("forum_gcon db connect fail.");
            return false;
        }
        $offset = isset($arrInput['offset']) ? intval($arrInput['offset']) : 0;
        $limit  = isset($arrInput['limit']) ? intval($arrInput['limit']) : 20;
        
        $totalInput = array(
            'table' => self::AGENCY_DATA_TABLE,
            'db'    => $objTbMysql,
            'field' => array(
                'id',
            ),
            'cond'  => $arrInput['cond'],
        );
        $totalRes = Util_Db::select($totalInput);
        if (false === $selectRes || Tieba_Errcode::ERR_SUCCESS != $selectRes['errno']) {
            Bingo_Log::fatal("get relation by agency_id total fail param:". serialize($totalInput));
            return false;
        }
        $selectInput = array(
            'table' => self::AGENCY_DATA_TABLE,
            'db'    => $objTbMysql,
            'field' => array(
                '*',
            ),
            'cond'  => $arrInput['cond'],
        );
        if (isset($arrInput['append'])) {
            $selectInput['append'] = $arrInput['append'];
        }
        if (isset($arrInput['field'])) {
            $selectInput['field'] = $arrInput['field'];
        }
        $selectRes = Util_Db::select($selectInput);
        if (false === $selectRes || Tieba_Errcode::ERR_SUCCESS != $selectRes['errno']) {
            Bingo_Log::fatal("get agency_data fail param:". serialize($selectInput));
            return false;
        }
        return array(
            'total' => count($totalRes['data']),
            'list'  => $selectRes['data'],
        );
    }
    
    public function createAgencyIncome($arrInput = array()) {
        $allField = self::_getAllField(self::AGENCY_INCOME_TABLE);
        foreach ($allField['nedd_serial'] as $field) {
            if (isset($arrInput["$field"])) {
                $arrInput["$field"] = serialize($arrInput["$field"]);
            }
        }
        $insertField = array();
        foreach ($allField['all_field'] as $val) {
            if (isset($arrInput["$val"])) {
                $insertField["$val"] = $arrInput["$val"];
            }
        }
        $insertInput = array(
            'table' => self::AGENCY_INCOME_TABLE,
            'field' => $insertField,
        );
        $res = Util_Db::insert($insertInput);
        return $res;
    }
    
    public function getAgencyForumIncomeInfo($arrInput = array()) {
        $arrTotalInput = array(
            'table' => self::AGENCY_INCOME_TABLE,
            'field' => array(
                'id',
            ),
            'cond'  => $arrInput['cond'],
        );
        $arrTotalRes = Util_Db::select($arrTotalInput);
        if (false === $arrTotalRes || Tieba_Errcode::ERR_SUCCESS != $arrTotalRes['errno']) {
            Bingo_Log::warning("get agency income total fail param:". serialize($arrTotalInput));
            return false;
        }
        
        $arrSelectInput = array(
            'table' => self::AGENCY_INCOME_TABLE,
            'field' => isset($arrInput['field']) ? $arrInput['field'] : array('*'),
            'cond'  => $arrInput['cond'],
        );
        if (isset($arrInput['append'])) {
            $arrSelectInput['append'] = $arrInput['append'];
        }
        $arrSelectRes = Util_Db::select($arrSelectInput);
        return array(
            'total' => count($arrTotalRes['data']),
            'list'  => $arrSelectRes['data'],
        );
    }
    
    public function updateIncomeByIncomeId($arrInput = array()) {
        $allField = self::_getAllField(self::AGENCY_INCOME_TABLE);
        foreach ($allField['nedd_serial'] as $fval) {
            if (isset($arrInput['field'][$fval])) {
                $arrInput['field'][$fval] = serialize($arrInput['field'][$fval]);
            }
        }
        $field = array();
        foreach ($allField['all_field'] as $aval) {
            if (isset($arrInput['field'][$aval])) {
                $field[$aval.'='] = $arrInput['field'][$aval];
            }
        }
        if (empty($field)) {
            Bingo_Log::warning("input params lack field param:". serialize($arrInput));
            return false;
        }
        $updateInput = array(
            'table' => self::AGENCY_INCOME_TABLE,
            'field' => $field,
            'cond'  => array(
                "id=" => intval($arrInput['income_id']),
            ),
        );
        $updateRes = Util_Db::update($updateInput);
        return $updateRes;
    }
    
    public function mgetRelationByAids($arrInput = array()) {
        //$offset = isset($arrInput['offset']) ? intval($arrInput['offset']) : 0;
       // $limit  = isset($arrInput['limit']) ? intval($arrInput['limit']) : 20;
        /*if (is_array($arrInput['agency_id'])) {
            $arrCond['agency_id'] = $arrInput['agency_id'];
        } else {
            $arrCond['agency_id='] = intval($arrInput['agency_id']);
        }
        if (isset($arrInput['status'])) {
            $status = $arrInput['status'];          
            if (is_array($arrInput['status'])) {
                $arrCond["status"] = $status;
            } else {
                $arrCond["status="] = intval($status);
            }
        }*/
        /*$totalInput = array(
            'table' => self::FIRM_AGENCY_TABLE,
            'field' => array(
                'id',
            ),
            'cond'  => $arrCond,
        );
        $totalRes = Util_Db::select($totalInput);
        if (false === $selectRes || Tieba_Errcode::ERR_SUCCESS != $selectRes['errno']) {
            Bingo_Log::fatal("get relation by agency_id total fail param:". serialize($totalInput));
            return false;
        }*/
        $arrSelectInput = array(
            'table'  => self::FIRM_AGENCY_TABLE,
            'field'  => array('agency_id', 'forum_name','forum_id'),
            'cond'   => $arrInput['cond'],
            'append' => array(
                //'group by agency_id'
                "order by create_time desc",
            ),
        );
        if (isset($arrInput['append'])) {
            $arrSelectInput['append'] = $arrInput['append'];
        }
        if (isset($arrInput['field'])) {
            $arrSelectInput['field'] = $arrInput['field'];
        }
        $arrSelectRes = Util_Db::select($arrSelectInput);
        if (false === $arrSelectRes || Tieba_Errcode::ERR_SUCCESS != $arrSelectRes['errno']) {
            Bingo_Log::fatal("get relation list by agency_id fail param:". serialize($arrSelectInput));
            return false;
        }
        return array(
            'total' => count($arrSelectRes['data']),
            'list'  => $arrSelectRes['data'],
        );
    }
    
    
}
