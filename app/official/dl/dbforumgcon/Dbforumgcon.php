<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014:07:17 12:00:30
 * @version 
 * @structs & methods(copied from idl.)
*/
define("MODULE", "Official_dl");
class Dl_Dbforumgcon_Dbforumgcon {
    private static $_db         = null;
    const SERVICE_NAME          = "Dl_Dbforumgcon_Dbforumgcon";
    protected static $_conf     = null;
    const DATABASE_NAME         = "forum_gcon";
    const OFFICIAL_FORUM_TABLE  = 'forumOfficial';
    const FOURTHMAN_TABLE       = 'identify';
    const OFFICIAL_SWITCH_TABLE = 'forumOfficialSwitch';
    const OFFICIAL_AUDIT_TABLE  = 'forumAudit';

    /**
     * @brief init
     * @return: true if success. false if fail.
    **/		
    private static function _init() {
        if (self::$_conf == null) {
            self::$_conf = Bd_Conf::getConf("/app/official/dl_test_test");
            if (self::$_conf == false) {
                Bingo_Log::warning("init get conf fail.");
                return false;
            }
        }
        return true; 
    }


    private static function _errRet($errno) {
        return array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    public static function preCall($arrInput) {
        // pre-call hook
    }

    public static function postCall($arrInput) {
        // post-call hook
    }
    
    public static function getDB() {
        $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
        if ($objTbMysql && $objTbMysql->isConnected()) {
            return $objTbMysql;
        } else {
            Bingo_Log::warning("db connect fail.");
            return null;
        }
    }
    
    public function delOfficialSwitchByFid($intFid = null) {
        $delSwitchInput = array(
            'table' => self::OFFICIAL_SWITCH_TABLE,
            'cond'  => array(
                'forum_id=' => $intFid,
            ),
        );
        $delSwitchRes = Util_Db::delete($delSwitchInput);
        return $delSwitchRes;
    }
    
    public function delOfficialForum($intFid, $opUname) {
        $curTime = time();
        $updateInput = array(
            'table' => self::OFFICIAL_FORUM_TABLE,
            'field' => array(
                "is_online="    => 0,
                "op_uname="     => $opUname,
                "del_op_name="  => $opUname,
                "offline_time=" => $curTime,
                "update_time="  => $curTime,
                "is_defer="      => 0,
                "defer_info="    => '',
            ),
            'cond'  => array(
                "forum_id="  => $intFid,
                "is_online" => array(
                    1,
                    5,
                ),
            ),
        );
        $updateRes = Util_Db::update($updateInput);
        return $updateRes;
    }

    public function getForumInfoById($intFid = null, $isOnline = 1, $isMaster = false) {
        $db = Util_Db::getDB();
        if (null === $db) {
            Bingo_Log::warning('get db fail');
            return false;
        }
        if ($isMaster) {
            $db->startTransaction();
        }
        $selectInput = array(
            'db' => $db,
            'table' => self::OFFICIAL_FORUM_TABLE,
            'field' => array(
                '*'
            ),
            'cond'  => array(
                'forum_id='  => $intFid,
                'is_online=' => $isOnline,
            ),
        );
        $totalRes = Util_Db::select($selectInput);
        if (!empty($totalRes['data'])) {
            $totalRes['data'][0]['material'] = unserialize($totalRes['data'][0]['material']);
        }
        if ($isMaster) {
            $db->commit();
        }
        return $totalRes;
    }
    public function updateOfficialOpUserName($arrInput = array()) {
        $intFid = intval($arrInput['forum_id']);
        $field = array();
        foreach ($arrInput as $key => $val) {
            $fkey = $key."=";
            $field[$fkey] = $val;
        }
        $updateInput = array(
                'table' => self::OFFICIAL_FORUM_TABLE,
                'field' => $field,
                'cond'  => array(
                        "forum_id="  => $intFid,
                ),
        );
        $updateRes = Util_Db::update($updateInput);
        return $updateRes;
    }
    public function updateOfficialStatus($arrInput = array()) {
        $intFid = intval($arrInput['forum_id']);
        $field = array();
        foreach ($arrInput as $key => $val) {
            $fkey = $key."=";
            $field[$fkey] = $val;
        }
        $updateInput = array(
            'table' => self::OFFICIAL_FORUM_TABLE,
            'field' => $field,
            'cond'  => array(
                "forum_id="  => $intFid,
            ),
        );
        $updateRes = Util_Db::update($updateInput);
        return $updateRes;
    }
    public function updateOfficialStyle($arrInput = array()) {
        $intFid = intval($arrInput['forum_id']);
        $field = array();
        foreach ($arrInput as $key => $val) {
            $fkey = $key."=";
            $field[$fkey] = $val;
        }
        $updateInput = array(
            'table' => self::OFFICIAL_FORUM_TABLE,
            'field' => $field,
            'cond'  => array(
                "forum_id="  => $intFid,
                "is_online=" => 1,
            ),
        );
        $updateRes = Util_Db::update($updateInput);
        return $updateRes;
    }
    
    public function getForumSwitchByFid($intFid) {
         $selectInput = array(
            'table' => self::OFFICIAL_SWITCH_TABLE,
            'field' => array(
                '*'
            ),
            'cond'  => array(
                'forum_id='  => $intFid,
            ),
        );
        $totalRes = Util_Db::select($selectInput);
        return $totalRes;
    }
    
    public function insertAudit($arrInput = array()) {
        $insertInput = array(
            'table' => self::OFFICIAL_AUDIT_TABLE,
            'field' => array(
                'type'       => intval($arrInput['type']),
                'forum_id'   => intval($arrInput['forum_id']),
                'forum_name' => trim($arrInput['forum_name']),
                'user_name'  => $arrInput['user_name'],
                'audit_status' => 0,
                'create_time' => time(),
                'audit_content' => $arrInput['audit_content'],
            ),
        );
        $res = Util_Db::insert($insertInput);
        return $res;
    }
  
    public function insertOfficialDb($arrInput = array()) {
        $curTime = time();
        $arrInput = array(
            'table' => 'forumOfficial',
            'field' => array(
                'forum_id'    => $arrInput['forum_id'],
                'forum_name'  => $arrInput['forum_name'],
                'is_online'   => isset($arrInput['is_online']) ? intval($arrInput['is_online']) : 1,
                'style_type'  => isset($arrInput['style_type']) ? intval($arrInput['style_type']) : 0,
                'type'        => isset($arrInput['type']) ? intval($arrInput['type']) : 0,
                'create_name' => isset($arrInput['create_name'])?$arrInput['create_name']:$arrInput['op_uname'],
                'op_uname'    => $arrInput['op_uname'],
                'create_time' => $curTime,
                // add online time,offline time
                'online_time' => intval($arrInput['online_time']),
                'offline_time' => intval($arrInput['offline_time']),
                'update_time' => $curTime,
                'material'    => serialize($arrInput['material']),
                // add by sunhuahua
                'audit_name'  => isset($arrInput['audit_name']) ? $arrInput['audit_name'] : "",
                'audit_time'  => isset($arrInput['audit_time']) ? intval($arrInput['audit_time']) : 0,
                'contract_no'  => isset($arrInput['contract_no']) ? $arrInput['contract_no'] : "",
                //end
                'is_defer' => isset($arrInput['is_defer']) ? intval($arrInput['is_defer']) : 0,
                'defer_info' => serialize($arrInput['defer_info']),
            ),
        );
        $res = Util_Db::insert($arrInput);
        return $res;
    }
	
	// add by sunhuahua
	public function updateOfficialIsOnline($arrInput = array()) {
		
        $field = array();
		foreach($arrInput['field'] as $key => $val) {
			$strField = $key."=";
			$field[$strField] = $val;
		}
		$cond = array();
		foreach($arrInput['cond'] as $key => $val) {
			$strField = $key."=";
			$cond[$strField] = $val;
		}
        $updateInput = array(
            'table' => self::OFFICIAL_FORUM_TABLE,
            'field' => $field,
            'cond'  => $cond,
        );
        $updateRes = Util_Db::update($updateInput);
        return $updateRes;
    }
	
	// add by sunhuahua to delete forum official record
	public function delOfficialForumRecord($arrInput = array()) {
        $cond = array();
        foreach($arrInput['cond'] as $key => $val) {
            if (is_array($val)) {
                $strField = $key;
            } else {
                $strField = $key."=";
            }
            $cond[$strField] = $val;
        }
        $delInput = array(
            'table' => self::OFFICIAL_FORUM_TABLE,
            'cond'  => $cond,
        );
        $updateRes = Util_Db::delete($delInput);
        return $updateRes;
    }
	
	public function updateContractNo($arrInput = array()) {
		$intFid = intval($arrInput['forum_id']);
        $field = array(
			'contract_no=' => $arrInput['contract_no'],
		);
        $updateInput = array(
            'table' => self::OFFICIAL_FORUM_TABLE,
            'field' => $field,
            'cond'  => array(
                "forum_id="  => $intFid,
				"is_online=" => intval($arrInput['is_online']),
            ),
        );
        $updateRes = Util_Db::update($updateInput);
        return $updateRes;
    }
	
    public function replaceOfficialSwitch($arrInput = array()) {
        $data = array();
        $curTime = time();
        foreach ($arrInput as $val) {
            $data[] = array(
                intval($val['forum_id']),
                strval($val['switch_name']),
                intval($val['switch_type']),
                intval($val['status']),
                $curTime,
            );
        }
        $switchInput = array(
            'table' => self::OFFICIAL_SWITCH_TABLE,
            'field' => array(
                'forum_id',
                'switch_name',
                'switch_type',
                'status',
                'create_time',
            ),
            'data'  => $data,
        );
        $replaceRes = Util_Db::replace($switchInput);
        return $replaceRes;
    }
    
    public function delOfficialSwitch($arrInput = array()) {
        $delSwitchInput = array(
            'table' => self::OFFICIAL_SWITCH_TABLE,
            'field' => array(
                'status=' => 0,
            ),
            'cond'  => array(
                'forum_id='    => intval($arrInput['forum_id']),
                'switch_name=' => strval(trim($arrInput['switch_name'])),
            ),
        );
        $delSwitchRes = Util_Db::update($delSwitchInput);
        return $delSwitchRes;
    }
    
    public function getOfficialList($arrInput = array()) {
        $type = intval(Util_Function::getArrVal($arrInput, 'type', -1));
        $isOnline = intval(Util_Function::getArrVal($arrInput, 'is_online', 1));
        $pn   = intval(Util_Function::getArrVal($arrInput, 'pn', 1));
        $limit = intval(Util_Function::getArrVal($arrInput, 'limit', 20));
        $offset = ($pn - 1)*$limit;
        $intNow = time();
        /*
        if ($isOnline == 3) { //当查询待审核列表时候需要把合同修改的也加进去
            $objDb = self::getDB();
            $strCond = ' where (is_online = 1 and is_defer=1 or is_online = 3) and type='.$type;
            if (isset($arrInput['word'])) {
                $strCond .= ' and concat(`forum_name`, `create_name`) like %'.mysql_escape_string(trim($word)).'% ';
            }
            $strCountFiled = 'select count(id) from '.self::OFFICIAL_FORUM_TABLE;
            $strSql = $strCountFiled.$strCond;
            $totalRes = $objDb->query($strSql);
            if (false === $totalRes) {
                Bingo_Log::warning('get official total num fail sql:'. serialize($strSql));
                return false;
            }
            $output['total'] = intval($totalRes[0]["count(id)"]);
            $strPageField = 'select * from '.self::OFFICIAL_FORUM_TABLE;
            $strAppend = ' order by create_time desc limit '.$offset.','. $limit;
            $strSql = $strPageField.$strCond.$strAppend;
            $arrRes = $objDb->query($strSql);
            if (false === $arrRes) {
                Bingo_Log::warning('get official page data fail, sql:'. serialize($strSql));
                return false;
            }
            $selectRes['data'] = $arrRes;
        } else {
        */
            if ($isOnline == 1) {
                $cond['is_online='] = $isOnline;
                //$cond['online_time<'] = $intNow;
            } else if ($isOnline == 100) {  // add by sunhuahua
                $cond['is_online > '] = 0;
                $cond['is_online < '] = 6;
            } else if ($isOnline == 101) { //待上线
                $cond['is_online'] = array(
                    1,
                    5,
                );
                $cond['is_defer='] = 2;
                //$cond['online_time>='] = $intNow;
            } else if ($isOnline == 102) { //已过审
                $cond['is_online'] = array(
                    1,
                    5,
                );
                //$cond['online_time>='] = $intNow;
            } else {
                $cond['is_online='] = $isOnline;
            }
            if ($type >= 0) {
                $cond['type='] = $type;
            }
            
            if (isset($arrInput['word'])) {
                $word =  $arrInput['word'];
                $cond['concat(`forum_name`, `create_name`) like'] =  ' %'.$word.'%';
            }
            $totalInput = array(
                'table' => self::OFFICIAL_FORUM_TABLE,
                'field' => array(
                    'id'
                ),
                'cond'  => $cond,
            );
            $totalRes = Util_Db::select($totalInput);
            if (false === $totalRes) {
                Bingo_Log::warning('get official total num fail input:'. serialize($totalInput));
                return false;
            }
            $output['total'] = count($totalRes['data']);
            $selectInput = array(
                'table' => self::OFFICIAL_FORUM_TABLE,
                'field' => array(
                    '*'
                ),
                'cond'  => $cond,
                'append' => array(
                    "order by create_time desc",
                    "limit $offset, $limit",
                ),
            );
            $selectRes = Util_Db::select($selectInput);
            if (false === $selectRes) {
                Bingo_Log::warning('get official select data fail input:'. serialize($selectInput));
                return false;
            }
 //       }
        $fidArr = array();
        foreach ($selectRes['data'] as &$val) {
            $val['material'] = unserialize($val['material']);
            $fidArr[] = $val['forum_id'];
        }
		if(count($fidArr) > 0){
			$switchInput = array(
				'table' => self::OFFICIAL_SWITCH_TABLE,
				'field' => array(
					'*'
				),
				'cond'  => array(
					'forum_id' => $fidArr,
				),
			);
			$switchRes = Util_Db::select($switchInput);
			if (false === $switchRes) {
				Bingo_Log::warning('get official switch data fail input:'. serialize($switchInput));
				return false;
			}
			$forumSwitchArr = array();
			foreach ($switchRes['data'] as $wval) {
				$forumSwitchArr[$wval['forum_id']][$wval['switch_name']] = $wval;
			}
			foreach ($selectRes['data'] as &$val) {
				$val['switch'] = $forumSwitchArr[$val['forum_id']];
			}
		}
        $output['list'] = $selectRes['data'];
        return $output;
    }
	
	// add by sunhuahua
	public function getAuditOfficialByCond($arrInput = array(), $isMaster = false) {
	    $db = Util_Db::getDB();
	    if (null === $db) {
	        Bingo_Log::warning('get db fail');
	        return false;
	    }
	    if ($isMaster) {
	        $db->startTransaction();
	    }
        $selectInput = array(
            'db' => $db,
            'table' => self::OFFICIAL_FORUM_TABLE,
            'field' => array(
                '*'
            ),
            'cond'  => array(
                'forum_id='  => intval($arrInput['forum_id']),
            ),
        );
        if (isset($arrInput['is_online'])) {
            if (is_array($arrInput['is_online'])) {
                $selectInput['cond']['is_online'] = $arrInput['is_online'];
            } else {
                $selectInput['cond']['is_online='] = intval($arrInput['is_online']);
            }
            
        }
        $arrRes = Util_Db::select($selectInput);
        if ($isMaster) {
            $db->commit();
        }
        return $arrRes;
    }
    
	/**
	 * @brief getOfficialListToAdsense
	 * @param: offset,limit
	 * @return: true if success. false if fail.
	 **/		    
    public function getOfficialListToAdsense($arrInput = array()) {
        $type = intval(Util_Function::getArrVal($arrInput, 'type', 0));
        $isOnline = intval(Util_Function::getArrVal($arrInput, 'is_online', 1));
        $offset   = intval(Util_Function::getArrVal($arrInput, 'offset', 1));
        $limit = intval(Util_Function::getArrVal($arrInput, 'limit', 20));
        //$offset = ($pn - 1)*$limit;
        $cond = array(
            'is_online=' => $isOnline,
            'type'       => array(
                0,
                5,
                6,
                7,
                8,
            ),
        );
        if (isset($arrInput['word'])) {
            $word =  $arrInput['word'];
            $cond['concat(`forum_name`, `create_name`) like'] =  ' %'.$word.'%';
        }
        $totalInput = array(
            'table' => self::OFFICIAL_FORUM_TABLE,
            'field' => array(
                'id',
            ),
            'cond'  => $cond,
        );
        $totalRes = Util_Db::select($totalInput);
        if (false === $totalRes) {
            Bingo_Log::warning('get official total num fail input:'. serialize($totalInput));
            return false;
        }
        $output['total'] = count($totalRes['data']);
        $selectInput = array(
            'table' => self::OFFICIAL_FORUM_TABLE,
            'field' => array(
                'forum_id',
            ),
            'cond'  => $cond,
            'append' => array(
                "order by create_time desc",
                "limit $offset, $limit",
            ),
        );
        $selectRes = Util_Db::select($selectInput);
        if (false === $selectRes) {
            Bingo_Log::warning('get official select data fail input:'. serialize($selectInput));
            return false;
        }
        $output['list'] = $selectRes['data'];
        return $output;
    }
    
    public function getOfficialListByType($arrInput = array()) {
        $type = intval(Util_Function::getArrVal($arrInput, 'type', 0));
        $isOnline = intval(Util_Function::getArrVal($arrInput, 'is_online', 1));
        $offset   = intval(Util_Function::getArrVal($arrInput, 'offset', 1));
        $limit = intval(Util_Function::getArrVal($arrInput, 'limit', 20));
        //$offset = ($pn - 1)*$limit;
        $cond = array(
            'is_online=' => $isOnline,
            'type'       => array(
                0,
                1,
                2,
                3,
                4,
                5,
                6,
                7,
                8,
            ),
        );
        if (isset($arrInput['word'])) {
            $word =  $arrInput['word'];
            $cond['concat(`forum_name`, `create_name`) like'] =  ' %'.$word.'%';
        }
        $totalInput = array(
            'table' => self::OFFICIAL_FORUM_TABLE,
            'field' => array(
                'id',
            ),
            'cond'  => $cond,
        );
        $totalRes = Util_Db::select($totalInput);
        if (false === $totalRes) {
            Bingo_Log::warning('get official total num fail input:'. serialize($totalInput));
            return false;
        }
        $output['total'] = count($totalRes['data']);
        $selectInput = array(
            'table' => self::OFFICIAL_FORUM_TABLE,
            'field' => array(
                'forum_id',
            ),
            'cond'  => $cond,
            'append' => array(
                "order by create_time desc",
                "limit $offset, $limit",
            ),
        );
        $selectRes = Util_Db::select($selectInput);
        if (false === $selectRes) {
            Bingo_Log::warning('get official select data fail input:'. serialize($selectInput));
            return false;
        }
        $output['list'] = $selectRes['data'];
        return $output;
    }
    
    public function getAuditList($arrInput = array()) {
        //$type = intval(Util_Function::getArrVal(c, 'type', 0));
        $type = intval(Util_Function::getArrVal($arrInput, 'type', 0));
        $status = intval(Util_Function::getArrVal($arrInput, 'audit_status', 0));
        $pn   = intval(Util_Function::getArrVal($arrInput, 'pn', 1));
        $limit = intval(Util_Function::getArrVal($arrInput, 'limit', 20));
        $offset = ($pn - 1)*$limit;
        $cond['audit_status='] = $status;
        $cond['type='] = $type;
        if (isset($arrInput['word'])) {
            $word =  $arrInput['word'];
            $cond['concat(`forum_name`, `user_name`) like'] =  ' %'.$word.'%';
        }
        $totalInput = array(
            'table' => self::OFFICIAL_AUDIT_TABLE,
            'field' => array(
                'id'
            ),
            'cond'  => $cond,
        );
        $totalRes = Util_Db::select($totalInput);
        if (false === $totalRes) {
            Bingo_Log::warning('get official total num fail input:'. serialize($totalInput));
            return false;
        }
        $output['total'] = count($totalRes['data']);
        $selectInput = array(
            'table' => self::OFFICIAL_AUDIT_TABLE,
            'field' => array(
                '*'
            ),
            'cond'  => $cond,
            'append' => array(
                "order by create_time desc",
                "limit $offset, $limit",
            ),
        );
        $selectRes = Util_Db::select($selectInput);
        if (false === $totalRes) {
            Bingo_Log::warning('get official select data fail input:'. serialize($selectInput));
            return false;
        }
        $output['list'] = $selectRes['data'];
        return $output;
    }
    
    public function getAuditById($id = null) {
        $selectInput = array(
            'table' => self::OFFICIAL_AUDIT_TABLE,
            'field' => array(
                '*'
            ),
            'cond'  => array(
                'id=' => $id,
            ),
        );
        $selectRes = Util_Db::select($selectInput);
        return $selectRes;
    }
    
    public function updateAuditStatus($id = null, $auditStatus = null, $strAuditUserName = null, $intAuditUserId = null) {
        $curTime = time();
        $updateInput = array(
            'table' => self::OFFICIAL_AUDIT_TABLE,
            'field' => array(
                "audit_status="    => $auditStatus,
                "audit_time="      => $curTime,
                "audit_user_id="   => $intAuditUserId,
                "audit_user_name=" => $strAuditUserName,
            ),
            'cond'  => array(
                "id="  => $id,
            ),
        );
        $updateRes = Util_Db::update($updateInput);
        return $updateRes;
    }
    
    public function getFourthManList($arrInput) {
        $type = intval(Util_Function::getArrVal($arrInput, 'type', 0));
        $status = intval(Util_Function::getArrVal($arrInput, 'status', 1));
        $pn   = intval(Util_Function::getArrVal($arrInput, 'pn', 1));
        $limit = intval(Util_Function::getArrVal($arrInput, 'limit', 20));
        $offset = ($pn - 1)*$limit;
        $cond = array(
            'type='   => $type,
            'status=' => $status,
        );
        if (isset($arrInput['word'])) {
            $word =  $arrInput['word'];
            $cond['concat(`v_name`, `user_name`, `forum_name`) like'] =  ' %'.$word.'%';
        }
        $totalInput = array(
            'table' => 'identify',
            'field' => array(
                'id'
            ),
            'cond'  => $cond,
        );
        $totalRes = Util_Db::select($totalInput);
        if (false === $totalRes) {
            Bingo_Log::warning('get identify total num fail input:'. serialize($totalInput));           
            return false;
        }
        $output['total'] = count($totalRes['data']);
        $selectInput = array(
            'table' => 'identify',
            'field' => array(
                '*'
            ),
            'cond'  => $cond,
            'append' => array(
                "order by creat_time desc",
                "limit $offset, $limit",
            ),
        );
        $selectRes = Util_Db::select($selectInput);
        if (false === $totalRes) {
            Bingo_Log::warning('get identify select data fail input:'. serialize($totalInput));           
            return false;
        }
        $output['list'] = $selectRes['data'];
        return $output;
    }
}
