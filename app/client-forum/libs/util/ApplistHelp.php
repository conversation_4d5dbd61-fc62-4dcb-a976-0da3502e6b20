<?php
/**
 * Created by PhpStorm.
 * User: ji<PERSON><PERSON><PERSON>
 * Date: 2016/7/22
 * Time: 10:53
 */

class Libs_Util_ApplistHelp {
    public static $_strApplistKey = 'afd_cache_applist';
    /**
     *
     * @return mixed | bool
     */
    public static function getApplist() {
        $localCache = apc_fetch(self::$_strApplistKey);
        if ($localCache === false) {
            $redis = Libs_Util_Redis::getInstance();
            $redisCache = $redis->get(array('key' => self::$_strApplistKey));
            if (!isset($redisCache['ret'][self::$_strApplistKey]) || $redisCache['err_no'] !== 0) {
                return false;
            }
            $redisCache = json_decode($redisCache['ret'][self::$_strApplistKey], true);
            if (!is_array($redisCache)) {
                return false;
            }
            apc_store(self::$_strApplist<PERSON>ey, $redisCache, 300);
            $localCache = $redisCache;
            $localCache['isApc'] = false;
        } else {
            $localCache['isApc'] = true;
        }
        return $localCache;
    }
    /**
     * ����app��Ż�ȡ�����ƣ��������ID��ƥ�䲢���Ǵӻ����ȡ��applist������һ�Σ����Ա�ֱ֤�Ӵ�redis��ȡ��
     * @param array()
     * @return array()
     */
    public static function getPackageName($arrInput, $bNeedUpdate = true) {
        if (empty($arrInput) || !is_array($arrInput)) {
            return array();
        }
        $arrApplist = self::getApplist();
        if ($arrApplist === false) {
            return array();
        }
        $intCount = 0;
        $arrPackageName = array();
        foreach ($arrInput as $key => $value) {
            foreach ($arrApplist['app'] as $appKey => $appValue) {
                if ($value == $appValue['id']) {
                    $arrPackageName[] = $appValue['package'];
                    $intCount++;
                }
            }
        }
        //��ǰIDδ��ȫƥ��ɹ���applist�������Ի��棬������Ҫ����
        if ($intCount != count($arrInput) && true === $arrApplist['isApc'] && true === $bNeedUpdate) {
            apc_delete(self::$_strApplistKey);//ɾ���������ݣ���֤�´�ֱ�Ӵ�redis��ȡapplist����
            return self::getPackageName($arrInput, false);
        }
        return $arrPackageName;
    }
}