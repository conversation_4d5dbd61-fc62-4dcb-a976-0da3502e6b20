<?php
class Libs_Excellent_Afd {

	private static $_objRequest = null;
    private static $_intClientType = 0;
    private static $_strClientVersion = null;
    private static $_isNeedDeeplink = true;
    private static $_controlDeeplink = null;
    private static $_model = null;
    private static $_arrAfdInput = array();
    const PLACE_ID_SHOUYE_ANDROID = '1453093728320';
    const PLACE_ID_SHOUYE_IOS = '1453093904902';
    const CDN_IMG_DEFAULT_WIDTH = 644;
    const CDN_IMG_MIN_WIDTH     = 50;
    const TAG_NAME_URL     = 'http://e.hiphotos.baidu.com/forum/pic/item/8326cffc1e178a825dd15c02fe03738da877e8a7.jpg';
    const TAG_NAME_WH     = '75,36';
    const GOODS_STYLE_STORE = 1001; // 空订单类型
    // 职责链下沉版本
    const VERSION_REQ_AFD_12_5_0 = '********'; 
    const VERSION_REQ_AFD_12_4_30 = '12.4.30'; 
    const VERSION_REQ_AFD_12_4_8_30 = '*********'; 

    //广告过滤
    private static $_adFilterLog = array(
        'APP_FRS_PAGE_TOP_FILTER'        => 0,
        'APP_FRS_PAGE_STYLE3_FILTER'     => 0,
        'APP_FRS_PAGE_STYLE6_FILTER'     => 0,
        'APP_FRS_PAGE_STYLE7_FILTER'     => 0,
        'APP_FRS_PAGE_STYLE_NONE_FILTER' => 0,
        'APP_FRS_PAGE_STYLE_LOWER_FLOOR' => 0,
    );

    /**
     * @desc AFD广告请求初始化
     * @param objRequest : object : 请求对象
     * @return
     */
    public static function init($objRequest){
        self::$_objRequest    = $objRequest;
        self::$_intClientType =  self::$_objRequest ->getCommonAttr('client_type');
        self::$_strClientVersion = self::$_objRequest ->getCommonAttr('client_version');
        self::$_controlDeeplink = self::$_objRequest ->getStrategy('homepage_deeplink');
		self::$_model = self::$_objRequest ->getCommonAttr('model');
    }

    /**
     * @desc 获取AFD广告请求参数
     * @param
     * @return adReq : array : 广告请求参数集合
     */
    public static function getInputs($arrInputParams){
        $strPackageName = '';
        $strPlaceId = '';
        $adCount = 1;
        $osType = '0';
    	if (self::$_intClientType === Molib_Client_Define::CLIENT_TYPE_ANDROID) {
    		$strMobileOsType = 'android';
            $osType = '2';
            $strPlaceId = self::PLACE_ID_SHOUYE_ANDROID;
            $strApplist = self::$_objRequest->getCommonAttr('applist', '');
            if ($strApplist != '') {
                $arrAppIds = explode(',', $strApplist);
                $strPackageName = implode(',', Libs_Util_ApplistHelp::getPackageName($arrAppIds));
            }
    	} elseif (self::$_intClientType === Molib_Client_Define::CLIENT_TYPE_IPHONE) {
            $strPlaceId = self::PLACE_ID_SHOUYE_IOS;
    		$strMobileOsType = 'ios';
            $osType = '1';
    	} else {
    		$strMobileOsType = '';
            $osType = '0';
    	}

    	$intIsSdk = intval(self::$_objRequest->getPrivateAttr('issdk'));
    	if (1 == $intIsSdk) {
    		$strAdClientType = 'SDK';
    	} elseif (2 == $intIsSdk) {
    		$strAdClientType = 'MINIAPP';
    	} else {
    		$strAdClientType = 'APP';
    	}

    	$intUserId = self::$_objRequest->getCommonAttr('user_id', 0);

    	$intClientType    = intval(self::$_objRequest->getCommonAttr('client_type'));
    	$strClientVersion = strval(self::$_objRequest->getCommonAttr('client_version'));
    	$intIp            = self::$_objRequest->getCommonAttr('ip_int');
        $strIp            = Bingo_Http_Ip::newlong2ip($intIp);
    	$intNetType = intval(self::$_objRequest->getCommonAttr('net_type'));
    	$strImei    = strval(self::$_objRequest->getCommonAttr('phone_imei', ''));

    	$osVersion = strval(self::$_objRequest->getCommonAttr('os_version', 0)); //系统版本

    	$strCuid    = self::$_objRequest->getCommonAttr('cuid', '');
        $strSbCuid    = self::$_objRequest->getPrivateAttr('shoubai_cuid', '');
        $strSbCuidAnd    = self::$_objRequest->getPrivateAttr('cuid_galaxy2', '');
    	$strModel   = self::$_objRequest->getCommonAttr('model', '');
    	$strBaiduId = isset($_COOKIE['BAIDUID']) ? $_COOKIE['BAIDUID'] : '';

    	$daIdfa      = strval(self::$_objRequest->getPrivateAttr('da_idfa'));
        $platform    = strval(self::$_objRequest->getPrivateAttr('platform'));
        //12.3 ios升级caid, 文档：http://wiki.baidu.com/pages/viewpage.action?pageId=1348823846
        $caid = self::$_objRequest->getCommonAttr('caid', '');

        $strFreshType = '2'; //默认非下刷、非上刷，此时afd固定请求一条4楼广告，
        $intFreshCount = 0;
        $intMediaCount = 5; //默认帖子数5，5小于首楼4的广告，刚好返回一条广告

        if (isset($arrInputParams['freshType'])) {
            if ($arrInputParams['freshType'] == 1) {
                //贴吧下刷，转换为afd下刷
                $strFreshType = '4';
            } elseif ($arrInputParams['freshType'] == 2) {
                //贴吧上刷，转换为afd上刷
                $strFreshType = '3';
            }
        }
        if (Libs_Excellent_AfdExp::clientVersionControl_7N() && !empty($arrInputParams['mediaCount'])) {
            //4+7N逻辑，即出多条广告，根据请求帖子数自动计算
            $intMediaCount = $arrInputParams['mediaCount'];
        }

        // 贴吧实时嗅探参数 例如：{"iadex":"123,12321"}
        $strAdExtParams = self::$_objRequest->getPrivateAttr('ad_ext_params', '');
        $arrAdExtParams = json_decode($strAdExtParams, true);
        if ($arrAdExtParams && !empty($arrAdExtParams['iadex'])) {
            $extra[] = array('k' => 'iadex', 'v' => $arrAdExtParams['iadex']);
        }
        if (isset($arrAdExtParams['floor_info'])) {
            $extra[] = array('k' => 'floor_info', 'v' => $arrAdExtParams['floor_info']);
        }
        // 复用已有透传到render
        if (isset($arrAdExtParams['nad_core_version'])) {
            $extra[] = array('k' => 'nad_core_version', 'v' => $arrAdExtParams['nad_core_version']);
        }
        // 贴吧支持仅浏览模式
        $cmode = self::$_objRequest->getCommonAttr('cmode', '');
        if (!empty($cmode)) {
            $extra[] = array('k' => 'cmode', 'v' => $cmode);
        }

        $extra[] = array('k' => 'freshType', 'v' => $strFreshType);
        $extra[] = array('k' => 'freshCount', 'v' => $intFreshCount);
        $extra[] = array('k' => 'mediaCount', 'v' => $intMediaCount);
        $extra[] = array('k' => 'preTcount', 'v' => $arrInputParams['preTcount']);
        // 增加公共参数
        $extraList = Molib_Service_AfdService::getCommonExtraKvList(self::$_objRequest, 'personalized');
        if (!empty($extraList)) {
            $extra = array_merge($extra, $extraList);
        }
        if(self::$_intClientType === Molib_Client_Define::CLIENT_TYPE_IPHONE){
            $arrCaid = json_decode(urldecode($caid), true);
            if(empty($arrCaid) || !is_array($arrCaid)){
                Bingo_Log::warning(sprintf('caid decode error! caid:[%s], decode caid:[%s]', $caid, $arrCaid));
            }
            else{
                $extra[] = array('k' => 'caid', 'v' => $arrCaid['caid']);
                $extra[] = array('k' => 'factors_data', 'v' => $arrCaid['factors_data']);
                $extra[] = array('k' => 'caid_valid', 'v' => $arrCaid['caid_valid']);
            }
        }


        /**
         * 首页点击进大图还是进PB的实验
         * 对广告的影响，增加参数方便统计
         */
        $versionLimit = array(
            Molib_Client_Define::CLIENT_TYPE_IPHONE => '10.3.0',
            Molib_Client_Define::CLIENT_TYPE_ANDROID => '********',
        );
        $arrEid = array();

        //12.11新增afd实验ID透传
        $arrAppTransmitData = self::$_objRequest->getPrivateAttr('app_transmit_data', '');
        if ($arrAppTransmitData && !empty($arrAppTransmitData['wise_sample_id'])) {
            $arrEid[] = $arrAppTransmitData['wise_sample_id'];
        }

        if (Molib_Util_Version::compare($versionLimit[$intClientType], $strClientVersion) >= 0) {
            $uiAbTag =   self::$_objRequest->getCommonAttr('ui_abtest_tag', 'normal');
            if (in_array($uiAbTag, array("tag_164", "tag_165", "tag_168", "tag_169", "tag_170", "tag_173"))) {
                $arrEid[] = '2019090201';
            }
            if($uiAbTag == 'normal'){
                $arrEid[] = '2019081203';
            }
        }

        // 融合层实验
        $msdAbTag =   self::$_objRequest->getCommonAttr('msd_abtest_tag', 'msd_normal');
        // 实验组 - NA首页直播分发联合实验, 2019.11.21, PM: huangfubinghan, tieba00-1277
        if (in_array($msdAbTag, array('msd_tag_116', 'msd_tag_123'))) {
            $arrEid[] = '2019112221';
        }
        // 对照组 - NA首页直播分发联合实验
        if($msdAbTag == 'msd_normal') {
            $arrEid[] = '2019112222';
        }

        // 视频rank层
        $videoAbTag = self::$_objRequest->getCommonAttr('video_abtest_tag', 'video_normal');
        // 实验组 - 首页视频分发联合实验, 2019.11.21, PM: huangfubinghan, tieba00-1296
        if ($videoAbTag == 'video_tag_130') {
            $arrEid[] = '2019112231';
        }
        // 对照组 - 首页视频分发联合实验
        if ($videoAbTag == 'video_normal') {
            $arrEid[] = '2019112232';
        }

        $afdEid = self::_getAfdEid();
        if (false !== $afdEid) {
            $arrEid[] = $afdEid;
        }
        $eid = self::_getEidByCuid($strCuid);
        if (!empty($eid)) {
            $arrEid[] = $eid;
        }
        $commonEid = Molib_Service_AfdService::getCommonEidList(self::$_objRequest, Molib_Service_AfdService::FR_PERSONALIZE);
        if (!empty($commonEid)) {
            $arrEid = array_merge($arrEid, $commonEid);
        }

        Tieba_Stlog::addNode('eid' ,  implode(',', $arrEid));
        if(!empty($arrEid)) {
            $extra[] = array('k' => 'eid', 'v' => implode(',',$arrEid));
        }

        if (Molib_Util_Version::is20205Version(self::$_intClientType, self::$_strClientVersion)) {
            //获得墨卡托数据
            $mercatorData = Molib_Service_AfdService::getMercatorData(self::$_objRequest, Molib_Service_AfdService::FR_PERSONALIZE);
            if (!empty($mercatorData['gps']) && !empty($mercatorData['wgtag'])) {
                $extra[] = array(
                    'k' => 'gps',
                    'v' => $mercatorData['gps'],
                );
                $extra[] = array(
                    'k' => 'wgtag',
                    'v' => $mercatorData['wgtag'],
                );
            }
            //Bingo_Log::warning('mercator_app_pos_2:'.json_encode($mercatorData));
        }
        $extra[] = array(
            'k' => 'encoded_ua_new',
            'v' => isset($_SERVER['HTTP_USER_AGENT']) ? urlencode($_SERVER['HTTP_USER_AGENT']) : '',
        );

        $extList = array();
        //广告相关性
        $adContextList = Molib_Service_AfdService::getAdContextList(self::$_objRequest,Molib_Service_AfdService::FR_PERSONALIZE);
        if (!empty($adContextList)) {
        	//$extra[] = array('k' => 'ad_context_list', 'v' => $adContextList);
        	$extList = array_merge($extList, $adContextList);
        }

        $arrHeaders = getallheaders();
        $adReq = array(
            'ad' => array(
                'placeId' => $strPlaceId,
                'productId' => '2',
                'adCount' => $adCount, //这个字段没啥用了，真正的广告请求数由afd里面的ssp配置控制
                'extra' => $extra,
            ),
            'app' => array(
                'version' => self::$_strClientVersion,
                'name' => 'Tieba',
                'package' => 'com.baidu.tieba',
            ),
            'device' => array(
                'osType' => $osType,
                'osVersion' => $osVersion,
                'clientType' => '2',
                'flowType' => $intNetType,
                'imei' => $strImei,
                'idfa' => $daIdfa,
                'mobileModel' => self::$_model,
            ),
            'passport' => array(
                'baiduId' => $strBaiduId,
                'userId' => $intUserId,
                'cuid' => $strCuid,
                'shoubai_cuid' => $strSbCuid,
                'cuid_galaxy2' => $strSbCuidAnd,
                'ip' => $strIp,
            ),
            'service' => array(
                'ua' => $arrHeaders['User-Agent'],
                'refer' => $arrHeaders['Referer'],
            ),
            'extra' => array(
                0 => array(
                    'k' => 'applist',
                    'v' => $strApplist,
                ),
                1 => array(
                    'k' => 'f',
                    'v' => 'api',
                ),
            ),
        	'extlist' => json_encode($extList),
        );

        $bolIsIpv6 = (Bingo_Http_Ip::IP_V6 == Bingo_Http_Ip::getIpVersion($strIp));
        if ($bolIsIpv6) {
            $adReq['passport']['ipv6'] = $strIp;
            $adReq['passport']['ip'] = '';
        }

        self::$_arrAfdInput = $adReq;
    	return $adReq;
    }


    /**
     * @desc AFD请求结果整理
     * @param arrMultiOutput : array : 广告请求返回
     * @return arrAfd : array : 整理后的AFD返回广告
     */
    public static function execute($arrMultiOutput) {

        // 职责链下沉
        if ((self::$_intClientType === Molib_Client_Define::CLIENT_TYPE_ANDROID && Molib_Util_Version::compare(self::VERSION_REQ_AFD_12_4_8_30, self::$_strClientVersion) >= 0)
        || (self::$_intClientType === Molib_Client_Define::CLIENT_TYPE_IPHONE && Molib_Util_Version::compare(self::VERSION_REQ_AFD_12_4_30, self::$_strClientVersion) >= 0)) {
            return self::executeNew($arrMultiOutput);
        }

        $arrAdsense = array();
        $intImgWidth= self::_getCdnImgWidth();

        //关闭按钮
        $arrReason = array(
            0 => '已经看过',
            1 => '内容质量差',
            2 => '不感兴趣',
        );
        $arrCloseInfo = array(
            'support_close' => 1,
            'reasons'       => $arrReason,
        );

        $has_type_3    = false;
        $is_need_video = false;
        if (Molib_Util_VersionMatch::checkClient(self::$_controlDeeplink, self::$_intClientType, self::$_strClientVersion)) {
            $has_type_3    = true;
            $is_need_video = true;
        }
        $osType = '0';
        if (self::$_intClientType === Molib_Client_Define::CLIENT_TYPE_ANDROID) {
            $osType = '2';
        } elseif (self::$_intClientType === Molib_Client_Define::CLIENT_TYPE_IPHONE) {
            $osType = '1';
        } else {
            Bingo_Log::warning("got invalid clientType " . self::$_intClientType . " from request.");
            $osType = '0';
        }
        //6.9.0以及之后版本显示good_style==6的三图广告
        $has_type_3_6 = self::_checkClient6Style(self::$_strClientVersion) ? true : false;
        Bingo_Log::notice('zsy ad count:' . count($arrMultiOutput->ad) );
        //完整的物料数量返回
        $glm_c_personalized_ad_count = 0;
        //不算空订单的
        $glm_c_personalized_ad_read_count = 0;
        foreach ($arrMultiOutput->ad as $key => $ad) {
            $glm_c_personalized_ad_count++;
            $locCode = $ad->locCode;
            $adInfo = $ad->adInfo;
            $adInfo = $adInfo[0];

            $id = $adInfo->id;
            $productId = $adInfo->productId;
            $moduleType = $adInfo->moduleType;
            $urlType = $adInfo->urlType;
            $url = $adInfo->url;
            $downloadUrl = $adInfo->downloadUrl;
            $clientType = $adInfo->clientType;
            $extra = $adInfo->extra;
            $material = $adInfo->material;
            $material = $material[0];
            $floor = $adInfo->floor;
            $ad_id = $material->id;
            $apk_name = $material->apk_name;
            Tieba_Stlog::addNode("ideaid_$key", $ad_id);

            $info = json_decode($material->info, true);
            $info = $info[0];

            if (empty($apk_name)) {
                $apk_name = $info['apk_name'];
            }
            $info['id'] = $ad_id;
            foreach ($extra as $_index => $ex) {
                if($ex->k == 'actionControl'){
                    $arrCloseInfo['action_control'] = json_decode($ex->v,1);
                }
            }
            $info['close_info'] = $arrCloseInfo; //关闭按钮
            $arrBannerTmp['id'] = $ad_id;
            $arrBannerTmp['name'] = $ad_id;
            $arrBannerTmp['url_type'] = $urlType;

            if ($osType == 2) {
                if ($urlType == 3) {
                    $arrBannerTmp['apk_url'] = $downloadUrl;
                    $typeUrl = $downloadUrl;
                }
            } else {
                if ($urlType == 2) {
                    $arrBannerTmp['ios_url'] = $downloadUrl;
                    $typeUrl = $downloadUrl;
                }
            }
            $lego_card = $info['lego_card'];
            // it's lego ad, should json serialize
            if (!empty($lego_card)) {
                $arrExtra['client_type'] = "APP";
                $arrExtra['url_type'] = $urlType;
                $lego_card = self::_getLegoCardData($lego_card, $arrExtra);
                if ($lego_card !== false) {
                    $info['lego_card'] = $lego_card;
                }
            }
            $arrBannerTmp['url'] = $url;
            $info['button_url'] = isset($typeUrl) ? $typeUrl : $url;

            $arrBannerTmp['loc_code'] = $locCode;
            $arrBannerTmp['pos_name'] = $floor;
            if (isset($apk_name)) {
                $arrBannerTmp['apk_name'] = $apk_name;
            }
            $arrBannerTmp['goods_info'] = array();

            $arrBannerTmp['goods_info'][0] = $info;

            foreach ($extra as $_index => $ex) {
                if ($ex->k == 'extraParam') {
                    $arrBannerTmp['ext_info'] = $ex->v;
                } else if($ex->k == 'floorStep'){
                    $floorStep = json_decode($ex->v,1);
                }
            }

            if (isset($adInfo->advisible) && $adInfo->advisible == 0) {
                $arrBannerTmp['goods_info'][0]['goods_style'] = self::GOODS_STYLE_STORE;
                $arrBannerTmp['ext_info'] = $adInfo->ext_info;
            } else {
                $glm_c_personalized_ad_read_count++;
            }
            $good_style = intval($arrBannerTmp['goods_info'][0]['goods_style']);
            if (3 == $good_style) {
                if ($has_type_3) {
                    $arrBannerTmpNew = self::_buildDataFor3Style($arrBannerTmp);
                } else {
                    self::$_adFilterLog['APP_FRS_PAGE_STYLE3_FILTER']++;
                    continue;
                }
            } else if (6 == $good_style) {
                if ($has_type_3_6) {
                    $arrBannerTmpNew = self::_buildDataFor3Style($arrBannerTmp);
                } else {
                    self::$_adFilterLog['APP_FRS_PAGE_STYLE6_FILTER']++;
                    continue;
                }
            } else if (7 == $good_style) {
                if ($is_need_video) {
                    //精品贴视频广告的过滤
                    $is_good = self::$_objRequest->getPrivateAttr('is_good');
                    if ($is_good == 0) {
                        $arrBannerTmpNew = self::_buildDataForVideoStyle($arrBannerTmp);
                    } else {
                        self::$_adFilterLog['APP_FRS_PAGE_STYLE7_FILTER']++;
                        continue;
                    }
                } else {
                    self::$_adFilterLog['APP_FRS_PAGE_STYLE7_FILTER']++;
                    continue;
                }
            } else {
                $arrBannerTmpNew = self::_buildDataFor12Style($arrBannerTmp);
            }
            if (false == $arrBannerTmpNew) {
                self::$_adFilterLog['APP_FRS_PAGE_STYLE_NONE_FILTER']++;
                continue;
            }
            $arrBannerTmpNew['loc_code'] = $arrBannerTmp['loc_code'];
            $arrBannerTmpNew['floorStep'] = $floorStep;
            $arrAfd[] = $arrBannerTmpNew;
        }
        Tieba_Mtlog::addNode('glm_c_personalized_ad_count', $glm_c_personalized_ad_count);
        Tieba_Mtlog::addNode('glm_c_personalized_ad_read_count', $glm_c_personalized_ad_read_count);

        if (empty($arrAfd)) {
            $strServiceName = 'afd';
            $strMethod      = 'query';
            Bingo_Log::warning(sprintf('Afd returns nothing servicename:[afd:query] [input:%s] [output:%s]', $strServiceName, $strMethod, serialize(self::$_arrAfdInput), serialize($arrMultiOutput)));
        }
        return $arrAfd;
    }


    /**
     * @desc 3图广告返回整理
     * @param arrBannerTmp : array : 三图广告数据
     * @return arrBannerTmpNew : array : 整理后的三图广告数据
     */
    private static function _buildDataFor3Style($arrBannerTmp) {
        $pic1 = strval($arrBannerTmp['goods_info'][0]['thread_pic1']);
        $pic2 = strval($arrBannerTmp['goods_info'][0]['thread_pic2']);
        $pic3 = strval($arrBannerTmp['goods_info'][0]['thread_pic3']);
        $intImgWidth = self::_getCdnImgWidth();
        if (isset($pic1) && isset($pic2) && isset($pic3)) {
            $pic1 = self::_getCdnImgUrl($pic1, $intImgWidth);
            $pic2 = self::_getCdnImgUrl($pic2, $intImgWidth);
            $pic3 = self::_getCdnImgUrl($pic3, $intImgWidth);
            $arrThreadPics = array(
                array(
                    'pic' => $pic1
                ),
                array(
                    'pic' => $pic2
                ),
                array(
                    'pic' => $pic3
                ),
            );
            $deep_url = isset($arrBannerTmp['goods_info'][0]['deep_url']) ? strval($arrBannerTmp['goods_info'][0]['deep_url']) : '';
            if(Molib_Util_VersionMatch::checkClient(self::$_controlDeeplink, self::$_intClientType, self::$_strClientVersion)) {
                if(!empty($deep_url)&& (self::$_isNeedDeeplink == true) && !in_array(self::$_model, Molib_Conf_TbClient::$modelType)) {
					$deep_url = 'tieba://deeplink?jump=' . urlencode($deep_url) . '&wap=' . urlencode(strval($arrBannerTmp['url']));
				} else {
					$deep_url = '';
				}
            }
            $arrBannerTmpNew = array(
                'id'       => strval($arrBannerTmp['id']),
                'name'     => strval($arrBannerTmp['name']),
                'url_type' => intval($arrBannerTmp['url_type']),
                'url'      => strval($arrBannerTmp['url']),
                'ios_url'  => strval($arrBannerTmp['ios_url']),
                'apk_url'  => strval($arrBannerTmp['apk_url']),
                'deep_url'        => $deep_url,
                'apk_name' => strval($arrBannerTmp['apk_name']),
                'pos_name' => strval($arrBannerTmp['pos_name']),
                'first_name'  => strval($arrBannerTmp['first_name']),
                'second_name' => strval($arrBannerTmp['second_name']),
                'cpid'     => intval($arrBannerTmp['cpid']),
                'abtest'   => strval($arrBannerTmp['abtest']),
                'plan_id'  => intval($arrBannerTmp['plan_id']),
                'user_id'  => intval($arrBannerTmp['user_id']),
                'price'    => strval($arrBannerTmp['price']),
                'verify'   => strval($arrBannerTmp['verify']),
                'ext_info'   => strval($arrBannerTmp['ext_info']),
                'goods_info' => array(
                    array(
                        'id'             => intval($arrBannerTmp['goods_info'][0]['id']),
                        'user_name'      => strval($arrBannerTmp['goods_info'][0]['user_name']),
                        'user_portrait'  => strval($arrBannerTmp['goods_info'][0]['user_portrait']),
                        'thread_title'   => strval($arrBannerTmp['goods_info'][0]['thread_title']),
                        'thread_pic_list'=> $arrThreadPics,
                        'pop_window_text'=> strval($arrBannerTmp['goods_info'][0]['pop_window_text']),
                        'goods_style'    => intval($arrBannerTmp['goods_info'][0]['goods_style']),
                        'label_visible'  => intval($arrBannerTmp['goods_info'][0]['label_visible']),
                        'label_text'     => strval($arrBannerTmp['goods_info'][0]['label_title']),
                        'rank_level'     => intval($arrBannerTmp['goods_info'][0]['rank_level']),
                        'thread_type'    => strval($arrBannerTmp['goods_info'][0]['thread_type']),
                        'button_text'    => strval($arrBannerTmp['goods_info'][0]['button_text']),
						'ad_source'    => strval($arrBannerTmp['goods_info'][0]['ad_source']),
						'lego_card'    => strval($arrBannerTmp['goods_info'][0]['lego_card']),
                        'width'          => intval($arrBannerTmp['goods_info'][0]['width']),
                        'height'         => intval($arrBannerTmp['goods_info'][0]['height']),
                        'label_measure'  => intval($arrBannerTmp['goods_info'][0]['label_measure']),
                        'thread_content' => strval($arrBannerTmp['goods_info'][0]['thread_content']),
			            'tag_name' => '广告',
			            'tag_name_url' => self::TAG_NAME_URL,
			            'tag_name_wh' => self::TAG_NAME_WH,
                        'close_info'     => $arrBannerTmp['goods_info'][0]['close_info'],
                    ),
                ),
            );
            return $arrBannerTmpNew;
        } else {
            return false;
        }
    }

    /**
     * @desc 单图广告返回整理
     * @param arrBannerTmp : array : 三图广告数据
     * @return arrBannerTmpNew : array : 整理后的三图广告数据
     */
    private static function _buildDataFor12Style($arrBannerTmp)
    {
        $intImgWidth = self::_getCdnImgWidth();
        $arrBannerTmp['goods_info'][0]['thread_pic'] = self::_getCdnImgUrl($arrBannerTmp['goods_info'][0]['thread_pic'], $intImgWidth);
        $deep_url = isset($arrBannerTmp['goods_info'][0]['deep_url']) ? strval($arrBannerTmp['goods_info'][0]['deep_url']) : '';
        if(Molib_Util_VersionMatch::checkClient(self::$_controlDeeplink, self::$_intClientType, self::$_strClientVersion)) {
            if(!empty($deep_url)&& (self::$_isNeedDeeplink == true) && !in_array(self::$_model, Molib_Conf_TbClient::$modelType)) {
                $deep_url = 'tieba://deeplink?jump=' . urlencode($deep_url) . '&wap=' . urlencode(strval($arrBannerTmp['url']));
            } else {
				$deep_url = '';
			}
        }
        $arrBannerTmpNew = array(
            'id' => strval($arrBannerTmp['id']),
            'name' => strval($arrBannerTmp['name']),
            'url_type' => intval($arrBannerTmp['url_type']),
            'url' => strval($arrBannerTmp['url']),
            'ios_url' => strval($arrBannerTmp['ios_url']),
            'apk_url' => strval($arrBannerTmp['apk_url']),
            'deep_url'        => $deep_url,
            'apk_name' => strval($arrBannerTmp['apk_name']),
            'pos_name' => strval($arrBannerTmp['pos_name']),
            'first_name' => strval($arrBannerTmp['first_name']),
            'second_name' => strval($arrBannerTmp['second_name']),
            'cpid' => intval($arrBannerTmp['cpid']),
            'abtest' => strval($arrBannerTmp['abtest']),
            'plan_id' => intval($arrBannerTmp['plan_id']),
            'user_id' => intval($arrBannerTmp['user_id']),
            'price' => strval($arrBannerTmp['price']),
            'verify' => strval($arrBannerTmp['verify']),
            'ext_info' => strval($arrBannerTmp['ext_info']),
            'goods_info' => array(
                array(
                    'id' => intval($arrBannerTmp['goods_info'][0]['id']),
                    'user_name' => strval($arrBannerTmp['goods_info'][0]['user_name']),
                    'user_portrait' => strval($arrBannerTmp['goods_info'][0]['user_portrait']),
                    'thread_title' => strval($arrBannerTmp['goods_info'][0]['thread_title']),
                    'thread_pic' => strval($arrBannerTmp['goods_info'][0]['thread_pic']),
                    'pop_window_text' => strval($arrBannerTmp['goods_info'][0]['pop_window_text']),
                    'goods_style' => intval($arrBannerTmp['goods_info'][0]['goods_style']),
                    'label_visible' => intval($arrBannerTmp['goods_info'][0]['label_visible']),
                    'label_text' => strval($arrBannerTmp['goods_info'][0]['label_title']),
                    'button_text' => strval($arrBannerTmp['goods_info'][0]['button_text']),
					'ad_source'    => strval($arrBannerTmp['goods_info'][0]['ad_source']),
					'lego_card'    => strval($arrBannerTmp['goods_info'][0]['lego_card']),
                    'width' => intval($arrBannerTmp['goods_info'][0]['width']),
                    'height' => intval($arrBannerTmp['goods_info'][0]['height']),
                    'label_measure' => intval($arrBannerTmp['goods_info'][0]['label_measure']),
		            'tag_name' => '广告',
                    'tag_name_url' => self::TAG_NAME_URL,
                    'tag_name_wh' => self::TAG_NAME_WH,
                    'close_info'     => $arrBannerTmp['goods_info'][0]['close_info'],
                )
            )
        );
        return $arrBannerTmpNew;
    }

    /**
     * @desc 视频类广告返回整理
	 * @param arrBannerTmp : array : 原始广告数据
	 * @return arrBannerTmpNew : array : 整理后的广告数据
	 */
	private static function _buildDataForVideoStyle($arrBannerTmp)
	{
		$intImgWidth = self::_getCdnImgWidth();
		$arrBannerTmp['goods_info'][0]['video_info']['thumbnail_url'] = self::_getCdnImgUrl($arrBannerTmp['goods_info'][0]['video_info']['thumbnail_url'],$intImgWidth);
		$arrBannerTmpNew = array(
			'id' => strval($arrBannerTmp['id']),
			'name' => strval($arrBannerTmp['name']),
			'url_type' => intval($arrBannerTmp['url_type']),
			'url' => strval($arrBannerTmp['url']),
			'ios_url' => strval($arrBannerTmp['ios_url']),
			'apk_url' => strval($arrBannerTmp['apk_url']),
			'apk_name' => strval($arrBannerTmp['apk_name']),
			'pos_name' => strval($arrBannerTmp['pos_name']),
			'first_name' => strval($arrBannerTmp['first_name']),
			'second_name' => strval($arrBannerTmp['second_name']),
			'cpid' => intval($arrBannerTmp['cpid']),
			'abtest' => strval($arrBannerTmp['abtest']),
			'plan_id' => intval($arrBannerTmp['plan_id']),
			'user_id' => intval($arrBannerTmp['user_id']),
			'price' => strval($arrBannerTmp['price']),
			'verify' => strval($arrBannerTmp['verify']),
			'ext_info' => strval($arrBannerTmp['ext_info']),
			'goods_info' => array(
				array(
					'id' => intval($arrBannerTmp['goods_info'][0]['id']),
					'user_name' => strval($arrBannerTmp['goods_info'][0]['user_name']),
					'user_portrait' => strval($arrBannerTmp['goods_info'][0]['user_portrait']),
					'thread_title' => strval($arrBannerTmp['goods_info'][0]['thread_title']),
					'pop_window_text' => strval($arrBannerTmp['goods_info'][0]['pop_window_text']),
					'goods_style' => intval($arrBannerTmp['goods_info'][0]['goods_style']),
					'label_visible' => intval($arrBannerTmp['goods_info'][0]['label_visible']),
					'label_text' => strval($arrBannerTmp['goods_info'][0]['label_title']),
					'button_text' => strval($arrBannerTmp['goods_info'][0]['button_text']),
					'width' => intval($arrBannerTmp['goods_info'][0]['width']),
					'height' => intval($arrBannerTmp['goods_info'][0]['height']),
					'label_measure' => intval($arrBannerTmp['goods_info'][0]['label_measure']),
					'video_info' => array(
						'video_url' => strval($arrBannerTmp['goods_info'][0]['video_info']['video_url']),
						'video_duration' => strval($arrBannerTmp['goods_info'][0]['video_info']['video_duration']),
						'video_width' => strval($arrBannerTmp['goods_info'][0]['video_info']['video_width']),
						'video_height' => strval($arrBannerTmp['goods_info'][0]['video_info']['video_height']),
						'thumbnail_url' => strval($arrBannerTmp['goods_info'][0]['video_info']['thumbnail_url']),
						'thumbnail_width' => strval($arrBannerTmp['goods_info'][0]['video_info']['thumbnail_width']),
						'thumbnail_height' => strval($arrBannerTmp['goods_info'][0]['video_info']['thumbnail_height']),
					),
                    'lego_card' => strval($arrBannerTmp['goods_info'][0]['lego_card']),
					'tag_name' => '广告',
                    'tag_name_url' => self::TAG_NAME_URL,
                    'tag_name_wh' => self::TAG_NAME_WH,
                    'close_info'     => $arrBannerTmp['goods_info'][0]['close_info'],
                ),
			),
		);
		return $arrBannerTmpNew;

	}

    /**
     * @desc 获取CDN图片宽度
     * @param
     * @return
     */
    private static function _getCdnImgWidth()
    {
        if (self::$_intClientType === Molib_Client_Define::CLIENT_TYPE_IPHONE) {
            $intScreenWidth = self::$_objRequest->getPrivateAttr('scr_w');
            $intWidth = $intScreenWidth;

            if ($intWidth >= self::CDN_IMG_DEFAULT_WIDTH) {
                return self::CDN_IMG_DEFAULT_WIDTH;
            }
            if ($intWidth > self::CDN_IMG_MIN_WIDTH) { // 宽度小于这个值时，有可能是有问题的，直接走默认值了
                return $intWidth;
            } else {
                return self::CDN_IMG_DEFAULT_WIDTH;
            }
        }

        if (self::$_intClientType === Molib_Client_Define::CLIENT_TYPE_ANDROID) {

            $intScreenWidth = self::$_objRequest->getPrivateAttr('scr_w');
            $intScreenDip = self::$_objRequest->getPrivateAttr('scr_dip');

            $intWidth = $intScreenWidth * $intScreenDip;
            if ($intWidth >= self::CDN_IMG_DEFAULT_WIDTH) {
                return self::CDN_IMG_DEFAULT_WIDTH;
            }
            if ($intWidth > self::CDN_IMG_MIN_WIDTH) { // 宽度小于这个值时，有可能是有问题的，直接走默认值了
                return $intWidth;
            } else {
                return self::CDN_IMG_DEFAULT_WIDTH;
            }
        }
    }

    /**
     * @desc 获取CDN图片URL链接
     * @param
     * @return
     */
    private static function _getCdnImgUrl($strImgUrl, $intWidth, $intQuality = 80)
    {

        // 必须是imgsrc开头的, 否则原样返回
        // 会生成hiphotos域名的
        $strImgDomain = 'http://imgsrc.baidu.com/';
        if ($strImgDomain == substr($strImgUrl, 0, 24)) {
            $strUrl = Molib_Util_ClientImgLogic::generateNewUrl($strImgUrl, $useCdnUrl = true, 'q=' . $intQuality . ';w=' . $intWidth);
        } else {
            $strUrl = $strImgUrl;
        }
        return $strUrl;
    }

	    /**
     * @desc lego数据
     * @param
     * @return
     */
    private function _getLegoCardData($arrLego, $arrExtra)
    {
        if ($arrExtra['client_type'] == "APP" && isset($arrLego) && !empty($arrLego)) {
            $card_type = $arrLego['card_type'];
            if ($card_type == 12) {
                $arrLego['sp_url'] = 'http://e.hiphotos.baidu.com/forum/pic/item/8326cffc1e178a825dd15c02fe03738da877e8a7.jpg';
                $arrLego['sp_ratio'] = 0.5;
                if (!isset($arrLego['name'])) {
                    $arrLego['name'] = $arrLego['user_name'];
                }
                return json_encode($arrLego);
            } else if ($card_type == 17) {
                if ($arrExtra['url_type'] == 1 && isset($arrLego['pop_window_text'])) {
                    $tel = explode(':', urldecode($arrLego['pop_window_text']));
                    $arrLego['pop_window_text'] = $tel[1];
                    $arrLego['button_click']['scheme'] = 'tieba://button_action?scheme=' . $arrLego['button_click']['scheme'];
                }
                $arrLego['ad_source'] = '';
                $arrLego['tag_name_url'] = 'http://e.hiphotos.baidu.com/forum/pic/item/8326cffc1e178a825dd15c02fe03738da877e8a7.jpg';
                $arrLego['tag_name_wh'] = '75,36';
                return json_encode($arrLego);
            } else if ($card_type == 31 || $card_type == 99) {
                return json_encode($arrLego);
            } else {
                Bingo_Log::warning("got invalid lego type, card_type=$card_type, lego_card=" . serialize($arrLego));
            }
        }
        return false;
    }


    /**
     * 判断当前客户端版本是否大于6.9.0
     * @param $strClientVersion
     * @return true 大于当前版本 false 小于当前版本
     */
    private static function _checkClient6Style($strClientVersion){
        if(empty($strClientVersion)){
            return false;
        }

        $result = Molib_Util_Version::compare('6.9.0', $strClientVersion);
        if($result >= 0){
            return true;
        }else{
            return false;
        }
    }

    /**
     * @param $cuid
     * @return bool|string
     */
    private static function _getAfdEid()
    {
        $expTag = array('tag_166', 'tag_172');
        $tagId = Libs_Excellent_AfdExp::getAfdExpTag();
        if (!$tagId) {
            return '5008924'; //对照组eid
        }
        if (in_array($tagId, $expTag)) {
            return '5008923'; //实验组eid
        }
        return false;
    }

    /**
     * @desc  : 抽实验eid
     * @param : arrInput
     * @return: array
     */
    private static function _getEidByCuid($str) {
        $hash = 0;
        $n = strlen($str);
        for ($i = 0; $i <  $n; $i++){
            $hash = $hash*31 + ord($str[$i]);
            $hash&=0x7FFFFFFF;
        }
        $pec =$hash % 100;
        if (60 <= $pec && $pec < 80) {
            return '5006783';
        }else if ($pec >= 80) {
            return '5006784';
        }
        return '';
    }


    /**
     * @desc AFD请求结果整理
     * @param arrMultiOutput : array : 广告请求返回
     * @return arrAfd : array : 整理后的AFD返回广告
     */
    public static function executeNew($arrMultiOutput) {

        //关闭按钮
        $arrReason = array(
            0 => '已经看过',
            1 => '内容质量差',
            2 => '不感兴趣',
        );
        $arrCloseInfo = array(
            'support_close' => 1,
            'reasons'       => $arrReason,
        );

        $has_type_3    = false;
        $is_need_video = false;
        if (Molib_Util_VersionMatch::checkClient(self::$_controlDeeplink, self::$_intClientType, self::$_strClientVersion)) {
            $has_type_3    = true;
            $is_need_video = true;
        }
        //6.9.0以及之后版本显示good_style==6的三图广告
        $has_type_3_6 = self::_checkClient6Style(self::$_strClientVersion) ? true : false;
        Bingo_Log::notice('zsy ad count:' . count($arrMultiOutput->ad) );
        //完整的物料数量返回
        $glm_c_personalized_ad_count = 0;
        //不算空订单的
        $glm_c_personalized_ad_read_count = 0;
        foreach ($arrMultiOutput->ad as $key => $ad) {
            $glm_c_personalized_ad_count++;
            $locCode = $ad->locCode;
            $adInfo = $ad->adInfo;
            $adInfo = $adInfo[0];
            $extra = $adInfo->extra;
            $material = $adInfo->material;
            $material = $material[0];
            $floor = $adInfo->floor;
            $ad_id = $material->id;
            Tieba_Stlog::addNode("ideaid_$key", $ad_id);

            $info = json_decode($material->info, true);
            $info = $info[0];


            $info['id'] = $ad_id;
            foreach ($extra as $_index => $ex) {
                if($ex->k == 'actionControl'){
                    $arrCloseInfo['action_control'] = json_decode($ex->v,1);
                }
            }
            $arrBannerTmp['id'] = $ad_id;
            $arrBannerTmp['name'] = $ad_id;
            $arrBannerTmp['pos_name'] = $floor;

            $info['lego_card']['close_info']  = $arrCloseInfo;
            $arrBannerTmp['loc_code'] = $locCode;
            $arrBannerTmp['goods_info'] = array();
            $arrBannerTmp['goods_info'][0] = $info;

            foreach ($extra as $_index => $ex) {
                if ($ex->k == 'extraParam') {
                    $strExtParam = $ex->v;
                } else if($ex->k == 'floorStep'){
                    $floorStep = json_decode($ex->v,1);
                }
            }
            if (isset($adInfo->advisible) && $adInfo->advisible == 0) {
                $good_style = self::GOODS_STYLE_STORE; // 空订单
                $arrBannerTmp['goods_info'][0]['lego_card']['ad_common']['goods_style'] = $good_style;
            } else {
                $good_style = intval($arrBannerTmp['goods_info'][0]['lego_card']['ad_common']['goods_style']);
                $glm_c_personalized_ad_read_count++;
            }
            $arrBannerTmp['goods_info'][0]['goods_style'] = $good_style;
            $arrBannerTmp['goods_info'][0]['lego_card']['ad_common']['ext_info'] = $strExtParam;
            $arrBannerTmp['goods_info'][0]['lego_card']['ad_common']['pos'] = strval($floor);
            $arrBannerTmp['goods_info'][0]['lego_card'] = json_encode($arrBannerTmp['goods_info'][0]['lego_card']);
            if (3 == $good_style) {
                if ($has_type_3) {
                    $arrBannerTmpNew = $arrBannerTmp;
                } else {
                    self::$_adFilterLog['APP_FRS_PAGE_STYLE3_FILTER']++;
                    continue;
                }
            } else if (6 == $good_style) {
                if ($has_type_3_6) {
                    $arrBannerTmpNew = $arrBannerTmp;
                } else {
                    self::$_adFilterLog['APP_FRS_PAGE_STYLE6_FILTER']++;
                    continue;
                }
            } else if (7 == $good_style) {
                if ($is_need_video) {
                    //精品贴视频广告的过滤
                    $is_good = self::$_objRequest->getPrivateAttr('is_good');
                    if ($is_good == 0) {
                        $arrBannerTmpNew = $arrBannerTmp;
                    } else {
                        self::$_adFilterLog['APP_FRS_PAGE_STYLE7_FILTER']++;
                        continue;
                    }
                } else {
                    self::$_adFilterLog['APP_FRS_PAGE_STYLE7_FILTER']++;
                    continue;
                }
            } else {
                $arrBannerTmpNew = $arrBannerTmp;
            }
            if (false == $arrBannerTmpNew) {
                self::$_adFilterLog['APP_FRS_PAGE_STYLE_NONE_FILTER']++;
                continue;
            }
            $arrBannerTmpNew['loc_code'] = $arrBannerTmp['loc_code'];
            $arrBannerTmpNew['floorStep'] = $floorStep;
            $arrAfd[] = $arrBannerTmpNew;
        }
        Tieba_Mtlog::addNode('glm_c_personalized_ad_count', $glm_c_personalized_ad_count);
        Tieba_Mtlog::addNode('glm_c_personalized_ad_read_count', $glm_c_personalized_ad_read_count);
        if (empty($arrAfd)) {
            $strServiceName = 'afd';
            $strMethod      = 'query';
            Bingo_Log::warning(sprintf('Afd returns nothing servicename:[afd:query] [input:%s] [output:%s]', $strServiceName, $strMethod, serialize(self::$_arrAfdInput), serialize($arrMultiOutput)));
        }
        return $arrAfd;
    }
}
