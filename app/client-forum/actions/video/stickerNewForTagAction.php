<?php

/**
 * Created by PhpStorm.
 * User: liu<PERSON>i
 * Date: 2018/4/25
 * description nani 1.5版本以上的帖纸相关接口
 * http://agroup.baidu.com/tbvideo/md/edit/866148
 */
class stickerNewForTagAction extends Molib_Client_BaseAction {

    const WORDLIST_VIDEO_MUSIC_CONF = 'tb_wordlist_redis_video_music_conf';
    const CLIENT_TYPE_BOTH = 3;
    const WIDGET_TYPE = 1;

    const STATUS_TEST = 0;
    const STATUS_ONLINE = 1;
    const STATUS_OFFLINE = 2;
    const STATUS_DELETE = 3;
    const PN = 1;
    const RN = 99; // 客户端要求的


    const TYPE_ORI_MUSIC = 1;   // 用户上传的原声视频

    const PORTRAIT_PRE = 'http://tb.himg.baidu.com/sys/portrait/item/';

    private $_arrStickerList = array();
    private $_arrWhiteUsers = array();
    private $_intHasMore = 0;
    private $_intSwitch = 1;
    private $_intObjId = 0;
    private $_intObjType = 0;

    //老版本展示除自研贴纸以外贴纸，封面挂件暂时无用
    private $_arrStickerType = array(
        1,
        2,
        3,
        4,
        5,
        6,
    );//1:人脸贴纸 2:手势 3:人景 4:变脸 5:长贴纸 6:自研贴纸

    /**
     * @return array
     */
    public function _getPrivateInfo() {
        return array(
            'pn' => $this->_getInput('pn', self::PN),
        );
    }

    /**
     * @return bool
     */
    public function _checkPrivate() {
        return true;
    }

    /**
     * @param:null
     * @return:null
     **/
    public function _execute() {
        if ($this->_getStickerConf() && !$this->_intSwitch) {
            $arrRet = array(
                'data' => array(
                    'list' => $this->_arrStickerList,
                ),
            );
            $this->_stlog();

            return $this->_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
        }
        // getAllSticker
        if (!$this->_getAllSticker()) {
            $this->_error(Tieba_Errcode::ERR_CALL_SERVICE_FAIL, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_CALL_SERVICE_FAIL));

            return false;
        }
        $arrRet = array(
            'data' => array(
                'list'     => $this->_arrStickerList,
                'has_more' => $this->_intHasMore,
            ),
        );
        $this->_stlog();

        return $this->_errRet(Tieba_Errcode::ERR_SUCCESS, $arrRet);
    }

    /**
     * @param
     * @return
     * */
    private function _getStickerConf() {
        $handleWordServer = Wordserver_Wordlist::factory();
        $strTableName     = self::WORDLIST_VIDEO_MUSIC_CONF;
        $arrKeys          = array(
            'white_user',
            'switch',
        );
        $ret              = $handleWordServer->getValueByKeys($arrKeys, $strTableName);
        if (false == $ret || null == $ret) {
            Bingo_Log::fatal(sprintf("getTableContent table[%s] keys[%s] ret[%s]", $strTableName, serialize($arrKeys), serialize($ret)));

            return false;
        }
        $this->_arrWhiteUsers = unserialize($ret['white_user']);
        $this->_intSwitch     = $ret['switch'];

        return true;
    }

    /**
     * @param
     * @return
     * */
    private function _getAllSticker() {

       //获取所有贴纸信息 start
        $intUid        = $this->_objRequest->getCommonAttr('user_id', 0);
        $bolWhilteUser = in_array($intUid, $this->_arrWhiteUsers);
        $intClientType = (int)$this->_objRequest->getCommonAttr('client_type');
        $intPn         = (int)$this->_objRequest->getPrivateAttr('pn');
 
        if ($bolWhilteUser) {
            $strStatus = array(
                self::STATUS_ONLINE,
                self::STATUS_TEST,
            );
        } else {
            $strStatus = self::STATUS_ONLINE;
        }

        $arrInput = array(
            'client_type'  => array(
                $intClientType,
                self::CLIENT_TYPE_BOTH,
            ),
            'widget_type'  => self::WIDGET_TYPE,
            'sticker_type' => $this->_arrStickerType,
            'status'       => $strStatus,
            'pn'           => $intPn,
            'rn'           => self::RN,
        );

        $arrWidgetInfo = $this->_getWidgetInfo($arrInput);
        if (false === $arrWidgetInfo) {
            return false;
        }
        //获取所有贴纸信息end


        //获取所有tag信息start
        $arrTagInput = array(
            'status' => $strStatus,
        );
        $arrTagInfo  = $this->_getAllTagInfo($arrTagInput);
        if (false === $arrTagInfo) {
            return false;
        }
        //获取所有tag信息end


        //获取贴纸中所有musicInfo信息start
        $arrMusicIds = array();
        foreach ($arrWidgetInfo['list'] as $v) {
            $arrBindInfo   = (array)json_decode($v['sticker_bind']);
            $arrMusicIds[] = (int)$arrBindInfo['music_id'];
        }
        $arrInput = array(
            'ids' =>  $arrMusicIds,
        );
        $arrMusicInfo = $this->_mgetMusicInfo($arrInput);
        //获取贴纸中所有musicInfo信息end



        $arrList = array();
        foreach ($arrWidgetInfo['list'] as $k => $v) {
            $ext = $v['ext'];
            $ext = unserialize($ext);

            $item = array(
                'id'           => $v['id'],
                'seq'          => $v['weight'],
                'name'         => $v['name'],
                'desc'         => $v['description'],
                'img'          => $v['img'],
                'resource'     => $v['resource'],
                'tag_name'     => $arrTagInfo[$v['sticker_tag_id']]['tag_name'],
                'sticker_name' => $v['sticker_name'],
                'sticker_type' => $v['sticker_type'],
            );

            $arrBindInfo        = (array)json_decode($v['sticker_bind']);
            $arrStickInfo       = array(
                'face_num'        => (int)$arrBindInfo['face_num'],
                'stop_time'       => (int)$arrBindInfo['stop_time'],
                'filter_id'       => (string)$arrBindInfo['filter_id'],
             //   'filter_id'       => (int)$arrBindInfo['filter_id'],
                'sticker_mode'    => (int)$arrBindInfo['sticker_mode'],
                'sticker_bgcolor' => (string)$arrBindInfo['sticker_bgcolor'],
                //      'music'     => array(),
            );

           
       /*     $arrMusicInput      = array(
                'id' => (int)$arrBindInfo['music_id'],
            );*/
            $intMusicId = (int)$arrBindInfo['music_id'];
            $arrMusicInfoOutput = $this->_collectMusicInfoById($arrMusicInfo[$intMusicId]);
            if ($arrMusicInfoOutput !== false) {
                $arrStickInfo['music'] = $arrMusicInfoOutput;
            }
            $item['sticker_bind']  = $arrStickInfo;
            $item['is_need_pause'] = (int)$arrBindInfo['is_need_pause'];

            $arrList[$v['sticker_tag_id']][] = $item;
            $arrList['total'][]              = $item;
        }
        $arrRet   = array();
        $arrRet[] = array(
            'tag_name'     => '全部',
            'sticker_list' => $arrList['total'],
        );
        foreach ($arrTagInfo as $intTagId => $tagInfo) {
            if (isset($arrList[$intTagId])) {
                $arrRet[] = array(
                    'tag_name'     => $tagInfo['tag_name'],
                    'sticker_list' => $arrList[$intTagId],
                    'red_icon'     => $tagInfo['red_icon'],
                );
            }
        }

        $this->_arrStickerList = $arrRet;
        $this->_intHasMore     = (int)$arrWidgetInfo['has_more'];

        return true;
    }


    /**
     * arch方法，根据uid取用户信息
     * @param  [type] $arrUids [description]
     * @return [type]          [description]
     */
    private function _getUserDataEx($arrUids, $intNeedFollow = 1, $intNeedPass = 0) {
        $arrInput = array(
            "user_id" => $arrUids,
        );
        if (count($arrUids) == 0) {
            Bingo_Log::warning("no uids");

            return array();
        }
        if ($intNeedFollow) {
            $arrInput['need_follow_info'] = 1;
        }
        if ($intNeedPass) {
            $arrInput['need_pass_info'] = 1;
        }
        $strService = 'user';
        $strMethod  = 'mgetUserDataEx';
        $arrOutput  = Tieba_Service::call($strService, $strMethod, $arrInput, null, null, 'post', 'php', 'utf-8');
        $strLog     = sprintf("call $strService :: $strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput));
        if (false === $arrOutput) {
            Bingo_Log::fatal($strLog);

            return array();
        } else if (Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning($strLog);

            return array();
        }

        return $arrOutput['user_info'];
    }
    /**
     * [_mgetMusicInfo 批量获取音乐信息]
     * @param  [type] $arrInput [description]
     * @return [type]          [description]
     */
    private function _mgetMusicInfo($arrInput) {

        $arrOutput = Tieba_Service::call('video', 'mgetNaniMusicInfoByIds', $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning(sprintf("call video::mgetNaniMusicInfoByIds failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));
            return array();
        } 
        $arrRet = array();
        foreach ($arrOutput['data'] as  $arrMusicInfo) {
            $arrRet[$arrMusicInfo['id']] = $arrMusicInfo;
        }
        return $arrRet;
    }

    /**
     * [_collectMusicInfo 整理音乐信息]
     * @param  [type] $arrInput [description]
     * @return [type]          [description]
     */
    private function _collectMusicInfoById($arrMusicInfo) {

   /*     $arrMusicInput  = array(
            'id' => (int)$arrInput['id'],
        );
        $arrMusicOutput = Tieba_Service::call('video', 'getNaniMusicInfoById', $arrMusicInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrMusicOutput) {
            Bingo_Log::fatal(sprintf("call video::getNaniMusicInfoById failed input[%s] output[%s]", serialize($arrMusicInput), serialize($arrMusicOutput)));

            return false;
        } else if (Tieba_Errcode::ERR_SUCCESS !== $arrMusicOutput['errno']) {
            Bingo_Log::warning(sprintf("call video::getNaniMusicInfoById failed input[%s] output[%s]", serialize($arrMusicInput), serialize($arrMusicOutput)));

            return false;
        }*/

        if (!empty($arrMusicInfo)) {
         /*   $arrMusicIdInput = array(
                'id'  => $arrMusicInfo['id'],
            );
            $arrMusicIdOutput = Tieba_Service::call('video', 'getNaniMusicStringIdById', $arrMusicIdInput, null, null, 'post', 'php', 'utf-8');
            if (false === $arrMusicIdOutput || Tieba_Errcode::ERR_SUCCESS !== $arrMusicIdOutput['errno'] ) {
                Bingo_Log::warning(sprintf("call video::getNaniMusicStringIdById failed input[%s] output[%s]", serialize($arrMusicIdInput), serialize($arrMusicIdOutput)));
                return false;
            } 
            $arrMusicInfo['id'] = $arrMusicIdOutput['data']['id'];*/
            $strMusicId = '';
            if(!Molib_Util_Nani::encodeMusicId($arrMusicInfo['id'], $arrMusicInfo['type'], $strMusicId)){
                Bingo_Log::warning('decode id failed');
            }
            $arrRet       = array(
                'id'          => $strMusicId,
                'name'        => $arrMusicInfo['name'],
                'author_name' => $arrMusicInfo['author'],
                'resource'    => strval($arrMusicInfo['resource']),
                'image'       => strval($arrMusicInfo['image']),
                'duration'    => intval($arrMusicInfo['duration']),
                'is_collect'  => '-1',
                //目前统一传-1
                'music_type'  => $arrMusicInfo['type'],
                'used_time'   => $arrMusicInfo['used_time'],
            );
            //这里要强制传封面
            if ($arrMusicInfo['type'] == self::TYPE_ORI_MUSIC) {
                $intMusicUserId  = (int)$arrMusicInfo['upload_user_id'];
                $arrMusicUserMap = $this->_getUserDataEx(array($intMusicUserId), 0, 0);
                if (isset($arrMusicUserMap[$arrMusicInfo['upload_user_id']])) {
                    $music_user               = $arrMusicUserMap[$arrMusicInfo['upload_user_id']];
                    $arrUserInfo              = array(
                        'user_id'   => $music_user['user_id'],
                        'user_name' => $music_user['user_name'],
                        'name_show' => !empty($music_user['nani_nickname']) ? $music_user['nani_nickname'] : (!empty($music_user['user_nickname']) ? $music_user['user_nickname'] : $music_user['user_name']),
                        //'name_show'   => !empty($music_user['nani_nickname']) ? $music_user['nani_nickname']: '贴吧-'. (!empty($music_user['user_nickname'])?$music_user['user_nickname']:$music_user['user_name']),
                        'portrait'  => !empty($music_user['nani_headimage']) ? $music_user['nani_headimage'] : Tieba_Ucrypt::encode($music_user['user_id'], $music_user['user_name'], $music_user['portrait_time']),
                    );
                    $arrUserInfo['name_show'] = $this->_renameNameShow($arrUserInfo['name_show']);
                    $arrRet['author_name']    = $arrUserInfo['name_show'];
                    $arrRet['user_info']      = $arrUserInfo;
                    $arrRet['image']          = !empty($music_user['nani_headimage']) ? $music_user['nani_headimage'] : self::PORTRAIT_PRE . $arrUserInfo['portrait'] . '.jpg';
                }
            }

            return $arrRet;
        } else {
            return false;
        }
    }

    /**
     * [_collectMusicInfo 根据id取TagInfo]
     * @param  [type] $arrInput [description]
     * @return [type]          [description]
     */
    private function _getAllTagInfo($arrInput) {

        $arrTagInput  = array(
            'status' => (int)$arrInput['status'],
        );
        $arrTagOutput = Tieba_Service::call('video', 'getAllStickerTagInfo', $arrTagInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrTagOutput) {
            Bingo_Log::fatal(sprintf("call video::getAllStickerTagInfo failed input[%s] output[%s]", serialize($arrTagInput), serialize($arrTagOutput)));

            return false;
        } else if (Tieba_Errcode::ERR_SUCCESS !== $arrTagOutput['errno']) {
            Bingo_Log::warning(sprintf("call video::getAllStickerTagInfo failed input[%s] output[%s]", serialize($arrTagInput), serialize($arrTagOutput)));

            return false;
        }
        $arrRet = array();
        foreach ($arrTagOutput['data'] as $arrTag) {
            $arrRet[$arrTag['id']] = $arrTag;
        }

        return $arrRet;
    }

    /**
     * [_collectMusicInfo 根据id取TagInfo]
     * @param  [type] $arrInput [description]
     * @return [type]          [description]
     */
    private function _getWidgetInfo($arrInput) {

        $arrOutput = Tieba_Service::call("video", "getWidget", $arrInput, null, null, 'post', 'php', 'utf-8');
        if (false === $arrOutput) {
            Bingo_Log::fatal(sprintf("call video::getWidget failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));

            return false;
        } else if (Tieba_Errcode::ERR_SUCCESS !== $arrOutput['errno']) {
            Bingo_Log::warning(sprintf("call video::getWidget failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput)));

            return false;
        }

        return $arrOutput['data'];
    }


    /**
     * @brief  rename name show
     * @param  $strNameShow
     * @return mixed
     */
    private function _renameNameShow($strNameShow) {
        $arrNames = explode('Nani-', $strNameShow);

        return implode("", $arrNames);
    }

    /**
     * @param
     * @return
     * */
    public function _stlog() {
        //Tieba_Stlog::addNode('obj_id', $this->_intObjId);
        //Tieba_Stlog::addNode('obj_type', $this->_intObjType);
    }

    /**
     * @param $errno
     * @param array $data
     * @return bool
     */
    private function _errRet($errno, $data = array()) {
        $msg = Molib_Client_Error::getErrMsg($errno);
        if ($errno == Tieba_Errcode::ERR_PARAM_ERROR) {
            $msg = self::ERRNO_MAG_PARAM_ERROR;
        }
        $this->_error($errno, $msg);
        $this->_objResponse->setOutData($data);

        return true;
    }

}
