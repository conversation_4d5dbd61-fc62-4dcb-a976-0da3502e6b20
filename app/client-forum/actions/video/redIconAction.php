<?php
/**
 *  拉取首页视频号tab红点
 *  接口文档：http://wiki.baidu.com/pages/viewpage.action?pageId=1519083817
 */
class redIconAction extends Molib_Client_BaseAction {
    private $_intUid = 0;
    private $_strRedIconKey = 'video::getVideoTabRedIcon';
    private $_arrRedIconList = array();
    
	/**
	 * @param
	 * @return
	 */
	protected function _getPrivateInfo() {
        $this->_strCallFrom = $this->_getInput('call_from', '');
		return array(
            'call_from' => $this->_strCallFrom,
        );
	}
	
	/**
	 * @param
	 * @return
	 */
	protected function _checkPrivate() {
		return true;
	}
	
	/**
	 * @param
	 * @return
	 */
	protected function _execute() {
        if($this->_checkParam() === false){
            return false;
        }

        if($this->_getRedIcon() === false){
            return false;
        }

        $this->_setResponse();
        return true;
    }
    
    /**
     * service调用
     */
    private function _getRedIcon(){
        //get redIcon
        $intRedIconType = 0;
        if($this->_strCallFrom == 'video_tab'){
            $intRedIconType = 1;
        }
        $arrInput = array(
            'user_id' => $this->_intUid,
            'cuid' => $this->_objRequest->getCommonAttr('cuid'),
            'client_type' => $this->_objRequest->getCommonAttr('client_type'),
            'client_version' => $this->_objRequest->getCommonAttr('client_version'),
            'user_ip' => $this->_objRequest->getCommonAttr('ip_int'),
            'net_type' => $this->_objRequest->getCommonAttr('net_type'),
            'sample_id' => strval($this->_objRequest->getCommonAttr('sample_id')),
            'red_icon_type' => $intRedIconType,
        );
        Bingo_Timer::start($this->_strRedIconKey);
        $arrRedIconOut = Tieba_Service::call('video', 'getVideoTabRedIcon', $arrInput, null, null, 'post', 'php', 'utf-8'); 
        Bingo_Timer::end($this->_strRedIconKey);

        if(!$this->_checkOut($arrRedIconOut)){
            Bingo_Log::warning(sprintf('%s call %s fail! input:[%s], output:[%s]', __METHOD__, 
                $this->_strRedIconKey, json_encode($arrInput), json_encode($arrRedIconOut))
            );
            $errno = Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            $errmsg = Molib_Client_Error::getErrMsg($errno);
            $this->_error($errno, $errmsg);
            return false;
        }

        $this->_arrRedIconList = array_slice($arrRedIconOut['data']['red_icon_list'], 0, 1);
        //build thread
        $arrRawThread = array();
        foreach($this->_arrRedIconList as $arrRedIcon){
            if(empty($arrRedIcon['thread_list'])){
                continue;
            }
            $arrRawThread = array_merge($arrRawThread, $arrRedIcon['thread_list']);
        }
        $arrThreadList = array();
        if(!empty($arrRawThread)){
            $arrThreadList = $this->_buildThread($arrRawThread);
            if($arrThreadList === false){
                Bingo_Log::warning(sprintf('%s build thread fail!', __METHOD__));
                return false;
            }
            $arrThreadList = array_column($arrThreadList, null, 'tid');
        }

        //设置帖子信息
        foreach($this->_arrRedIconList as &$arrRedIcon){
            if(empty($arrRedIcon['thread_list'])){
                continue;
            }
            foreach($arrRedIcon['thread_list'] as &$arrThread){
                $arrThread = $arrThreadList[$arrThread['tid']];
            }
            unset($arrThread);
        }
        unset($arrRedIcon);

        return true;
    }

    /**
     * 构建帖子
     */
    private function _buildThread($arrRawThread){
        $arrRawThread = array_column($arrRawThread, null, 'tid');
        $arrTids = array_column($arrRawThread, 'tid');
        $arrInput = array(
            'thread_ids' => $arrTids,
            'forum_id' => 0,
            'need_abstract' => 1,
            'need_photo_pic' => 1,
            'call_from' => 'client_frs',
            'need_forum_name' => 1,
            'need_user_data' => 1,
        );
        $ret = Tieba_Service::call('post', 'mgetThread', $arrInput, null, null, 'post', 'php', 'utf-8');
        if(!$this->_checkOut($ret)){
            Bingo_Log::warning(sprintf('%s call post::mgetThread fail! input:[%s], output:[%s]', 
                __METHOD__, json_encode($arrInput), json_encode($ret))
            );
            return false;
        }
        $threadList = array();
        $userList = array();
        foreach ($ret['output']['thread_list'] as $row) {
            $tid = intval($row['thread_id']);
            if (!isset($threadList[$tid])) {
                $threadList[$tid] = $row;
            }
        }
        foreach ($ret['output']['thread_user_list'] as $row) {
            $userList[intval($row['user_id'])] = $row;
        }

        $arrInput = array(
            'request' => $this->_objRequest,
            'thread_list' => $threadList,
            'thread_user_list' => $userList,
            'need_pic_cut' => true,
            'ui_abtest_tag' => 'normal',
            'need_video_segment' => false,
        );
        $arrOut = Libs_Excellent_ThreadList::build($arrInput);
        $this->_objRequest->addCommonAttr('need_forum_info', 1);//推荐理由tag图标依赖吧信息
        $extOut = Libs_Excellent_Extension::execute($arrOut['thread_list'], $this->_objRequest);

        //构建推荐tag
        foreach($extOut as $k => $arrThread){
            $tid = $arrThread['tid'];
            if(empty($arrRawThread[$tid]['recomm_reason'])){
                continue;
            }
            $extOut[$k]['recom_reason'] = $arrRawThread[$tid]['recomm_reason'];
            $extOut[$k]['recom_tag_icon'] = $arrThread['forum_info']['avatar'];
        }

        return $extOut;
    }

    /**
     * 构建返回
     */
    private function _setResponse(){
        $arrData = array(
            'red_icon_list' => $this->_arrRedIconList//只返回一个红点，使用array保留扩容可能性
        );
        $this->_objResponse->setOutData($arrData);
        return true;
    }

    /**
     * 初始化参数
     */
    private function _checkParam(){
        $this->_intUid = $this->_objRequest->getCommonAttr('user_id', 0);
        if($this->_intUid <= 0){
            $errno = Tieba_Errcode::ERR_MO_PARAM_INVALID;
            $errmsg = Molib_Client_Error::getErrMsg($errno);
            $this->_error($errno, $errmsg);
            Bingo_Log::warning('param invalid! uid <= 0');
            return false;
        }
        return true;
    }

	/**
	 * 记录网络日志
	 * @param
	 * @return boolean
	 */
	private function _stLog(){
		return true;
    }
    
    /**
     * 通用rpc返回检查
     */
    private function _checkOut($arrOut){
        if(empty($arrOut) || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            return false;
        }

        return true;
    }
}