<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file threadstoreAction.php
 * <AUTHOR>
 * @date 2014/03/11 13:42:29
 * @brief /c/f/post/threadstore 
 *
 **/
class threadstoreAction extends Molib_Client_BaseAction
{
    private $_intUserId = 0;
    private $_intOffset = 0;
    private $_intResNum = 0;
    private $_arrBjhThread = array();
    const REPLYME_STATUS_DELETED = 1;
    private static $_objMulti = null;

    /**
     *
     * @var array  arrUserFollowed 关注数据
     */
    private $arrUserFollowed = array();

    /**
     *
     * @var array  楼主最后更新数据
     */

    private $arrAuthorLastNo = array();
    /**
     *
     * @var string 返回NA楼主最后更新信息
     */
    //const STR_LAST_POST_NO_MSG = '楼主最新更新到了%s楼内容';
        const STR_LAST_POST_NO_MSG = '楼主更新到了%s楼';

    /**
     * 用户昵称
     */
    private $arrUserNickname = array();

    /**
     * 用户信息
     */
    private $arrUserInfo = array();

    /**
     * @param void
     * @return array
     */
    public function _getPrivateInfo()
    {
        return array(
            'check_login' => true,
            'offset'      => intval(Bingo_Http_Request::get('offset', 0)),
            'result_num'  => intval(Bingo_Http_Request::get('rn', 0)),
        );
    }

    /**
     * @param void
     * @return boolean
     */
    public function _checkPrivate()
    {
        return true;
    }

     /**
     * @param void
     * @return void
     */
    public function _execute()
    {
        $this->_intUserId = intval($this->_objRequest->getCommonAttr('user_id', 0));
        $this->_intOffset = intval(Bingo_Http_Request::get('offset', 0));
        $this->_intResNum = intval(Bingo_Http_Request::get('rn', 0));

        // 兼容android 9.2.8.5 请求翻页bug
        $intClientType = intval($this->_objRequest->getCommonAttr('client_type'));
        $strClientVersion = strval($this->_objRequest->getCommonAttr('client_version'));
        if ($intClientType == Molib_Client_Define::CLIENT_TYPE_ANDROID &&
            (Molib_Util_Version::compare($strClientVersion, '9.2.8.5') == 0
            || Molib_Util_Version::compare($strClientVersion, '9.2.8.6') == 0)) {
            if ($this->_intResNum > 0) {
                $this->_intResNum += 10;
            }
        }

        $arrThreadStore = array();
        $arrThreadInfo  = array();

        if ($this->_intUserId > 0 && $this->_intResNum > 0) {
            $arrCookie = $this->_objRequest->getCommonAttr('cookie', array());
            $arrInput = array(
                'user_id' => $this->_intUserId,
                'offset'  => $this->_intOffset,
                'limit'   => $this->_intResNum,
                'bduss'   => $arrCookie['BDUSS'],
            );
            $arrOutput = Molib_Tieba_Service::call('post', 'queryStoreThreads', $arrInput);
            if ($arrOutput === false) {
                Bingo_Log::fatal('call post failed.  [sevice_name:post] [method:queryStoreThreads] [input:'.serialize($arrInput).']');
                $this->_objResponse->setOutData($this->_getError(Tieba_Errcode::ERR_CLIENT_CALL_POST));
                return false;
            }
            else if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                $strInput  = serialize($arrInput);
                $strOutput = serialize($arrOutput);
                Bingo_Log::warning("call post_queryStoreThreads failed, input[$strInput] output[$strOutput]");
                $this->_objResponse->setOutData($this->_getError($arrOutput['errno']));
                return false;
            }
            else {
                $arrThreadStore = $arrOutput['output']['replys'];
            }
        }

        $objMulti = new Molib_Tieba_Multi('threadstoreaction_key');

        if (!empty($arrThreadStore)) {

            $arrTids = array();
            $arrUids = array();

            foreach ($arrThreadStore as $arrThread) {
                $arrTids[] = intval($arrThread['thread_id']);

                // 获取楼主UID
                $arrUids[] = intval($arrThread['lz_uid']);

                // 获取楼主最后回复楼层 - - 注册
                $input = array(
                    "thread_id" => $arrThread['thread_id'], //帖子id
                    "offset" => 0,
                    "res_num" => 1,
                    "see_author" => 1,
                    "has_comment" => 0,
                    "has_mask" => 1,
                    "structured_content" => 1,
                );
                $multiInput = array(
                    "serviceName" => "post",
                    "method" => "getInvertPostsByThreadId",
                    "ie" => "utf-8",
                    "input" => $input,
                );
                $objMulti->register('post:getInvertPostsByThreadId_'.$arrThread['thread_id'], $multiInput);


                // 获取用户昵称
                $input = array(
                    "user_id" => $arrThread['lz_uid'],
                    "get_icon" => 1
                );
                $multiInput = array(
                    "serviceName" => "user",
                    "method" => "getUserData",
                    "ie" => "utf-8",
                    "input" => $input,
                );
                $objMulti->register('user:getUserData_'.$arrThread['lz_uid'], $multiInput);

            }

            // 获取主题贴信息 - 注册
            $arrInput = array(
                'thread_ids'     => $arrTids,
                'forum_id'       => 0,
                'need_abstract'  => 1,
                'need_photo_pic' => 0,
                'need_user_data' => 1,
                'call_from'      => 'client_frs',
            );
            $multiInput = array(
                "serviceName" => "post",
                "method" => "mgetThread",
                "ie" => "utf-8",
                "input" => $arrInput,
            );
            $objMulti->register('post:mgetThread', $multiInput);



            // 获取关注数据
            $arrInput = array(
                "user_id" => $this->_intUserId, //用户id
                "req_user_id" => $arrUids,
            );
            $multiInput = array(
                "serviceName" => "user",
                "method" => "getUserFollowInfo",
                "ie" => "utf-8",
                "input" => $arrInput,
            );
            $objMulti->register('user:getUserFollowInfo', $multiInput);

       
            $objMulti->call();


            // 获取关注数据
            $this->getFollowData($objMulti);

            // 获取用户信息，补齐昵称
            $this->getUserInfo($objMulti, $arrUids);


            // 获取楼主最后回复楼层
            $this->getAuthorLastNo($objMulti, $arrTids);

            // 获取主题贴信息 
            $arrOutput = $objMulti->getResult('post:mgetThread');
            if (!$arrOutput || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call service post:post_mgetThread fail');
            }


            /*
            $arrInput = array(
                'thread_ids'     => $arrTids,
                'forum_id'       => 0,
                'need_abstract'  => 1,
                'need_photo_pic' => 0,
                'need_user_data' => 1,
                'call_from'      => 'client_frs',
            );
            $arrOutput = Molib_Tieba_Service::call('post', 'mgetThread', $arrInput);
            
            if ($arrOutput['errno'] !==  Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call post_mgetThread failed');
            }
            */

            $arrThreadInfo = isset($arrOutput['output']['thread_list']) ? $arrOutput['output']['thread_list'] : array();
            // 8.9            
            $arrThreadUserInfo = isset($arrOutput['output']['thread_user_list']) ? $arrOutput['output']['thread_user_list'] : array();


            //百家号
            $nids = array();
            $types = array();
            $bjhTids = array();
            $bjhTypeNids = array();
            foreach ($arrThreadInfo as $key => $val){
                $threadInfo = $arrOutput['output']['thread_list'][$key];
                $nid = isset($threadInfo['nid']) && !empty($threadInfo['nid']) ? trim($threadInfo['nid']) : '';
                $bjhTid = isset($threadInfo['bjh_tid']) && !empty($threadInfo['bjh_tid']) ? trim($threadInfo['bjh_tid']) : 0;
                $isBjh = !empty($nid) ? 1 : 0;
                if ($isBjh){
                    $bjhTypeNid = '';
                    $arrTypes =  Tieba_Type_Thread::getTypeArray( $arrThreadInfo[$key]['thread_types']);
                    if(isset($arrTypes['is_movideo'])){//视频贴
                        $threadType = Tieba_Type_Thread::MOVIDEO_THREAD;
                        $bjhTypeNid = 'sv_'.$nid;
                        $type = 2;
                    }else if(isset($arrTypes['is_baijiahao_article'])){//文章贴
                        $threadType = Tieba_Type_Thread::BAIJIAHAO_ARTICLE_THREAD;
                        $bjhTypeNid = 'news_'.$nid;
                        $type = 1;
                    }else{//默认动态贴
                        $threadType = Tieba_Type_Thread::BAIJIAHAO_ARTICLE_THREAD;
                        $bjhTypeNid = 'dt_'.$nid;
                        $type = 1;
                    }
                    $nids[] = $nid;
                    $types[] = $type;
                    $bjhTypeNids[] = $bjhTypeNid;

                    $this->_arrBjhThread[$key] = array(
                        'nid' => $nid,
                        'bjh_tid' => $bjhTid,
                        'thread_type' => $threadType,
                        'bjh_type_nid' => $bjhTypeNid
                    );
                }
            }
            //百家号注册接口
            $arrInput = array(
                'nids' => $nids,
                'types' => $types,
                'bjh_tids' => $bjhTids,
                'type_nids' => $bjhTypeNids
            );
            $this->registerBjhMulti($arrInput);

            //图文直播搬贴改变帖子类型为图文直播贴
            Molib_Util_ThreadFilter::setThreadTypesByCopyTwZhiBo($arrThreadInfo);
        }

        $arrArranged = $this->_arrange($arrThreadStore, $arrThreadInfo, $arrThreadUserInfo);

        $arrResult = $this->_getError(Tieba_Errcode::ERR_SUCCESS);
        $arrResult['store_thread'] = $arrArranged;

        $this->_objResponse->setOutData($arrResult);
        return true;
    }

    /**
     * [registerMulti 注册函数]
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public function registerBjhMulti($arrInput) {
        self::$_objMulti = new Tieba_Multi('bjh_threadstoreaction_key');
        //百家号帖子信息
        //获取百家号主题帖信息
        $arrMultiInput = array(
            'serviceName' => 'thirdparty',
            'method' => 'getBJHThreadContent',
            'ie' => 'utf-8',
            'input' => array(
                'nids' => $arrInput['nids'],
                'types' => $arrInput['types'],
                'need_tb_format' => 1
            )
        );
        self::$_objMulti->register('getBJHThreadContent', new Tieba_Service('thirdparty'), $arrMultiInput);

        //获取百家号作者信息
//        $arrMultiInput = array(
//            'serviceName' => 'thirdparty',
//            'method' => 'mgetBJHUserInfo',
//            'input' => array(
//                'pass_ids' => $arrInput['user_ids']
//            )
//        );
//        self::$_objMulti->register('mgetBJHUserInfo', new Tieba_Service('thirdparty'), $arrMultiInput);

        //获取评论数
        foreach ($arrInput['bjh_tids'] as $bjhTid){
            $arrMultiInput = array(
                'serviceName' => 'thirdparty',
                'method' => 'getBJHCommentNum',
                'input' => array(
                    'thread_ids' => $bjhTid
                )
            );
            self::$_objMulti->register('getBJHCommentNum_'.$bjhTid, new Tieba_Service('thirdparty'), $arrMultiInput);
        }

        self::$_objMulti->call();

    }

    /**
     * 获取楼主关注数据
     *
     * @param array arrUids = array()
     * @return boolean
     */
    private function getFollowData($objMulti) {
        $arrOutput = $objMulti->getResult('user:getUserFollowInfo');
        if (false === $arrOutput || $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call service user:getUserFollowInfo fail');
        }
        if (is_array($arrOutput['res_user_infos'])) {
            foreach($arrOutput['res_user_infos'] as $arrVal ) {
                $this->arrUserFollowed[$arrVal['user_id']] = intval($arrVal['is_followed']);
            }       
        }
        return true;
    }

    /**
     * 获取用户信息
     *
     * @param array arrUids = array()
     * @return boolean
     */
    private function getUserInfo($objMulti, $arrUids) {
        foreach ($arrUids as $postId) {
            $output = $objMulti->getResult('user:getUserData_'.$postId);
            if (false === $output || $output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call service user:getUserData fail, input:['.serialize($postId).'], output:['.serialize($output).']');
            }

            $this->arrUserNickname[$postId] = isset($output['user_info'][0]['user_nickname']) ? $output['user_info'][0]['user_nickname'] : '';
            $this->arrUserInfo[$postId] = isset($output['user_info'][0]) ? $output['user_info'][0] : array();
        } 

        return true;
    }

    /**
     * 获取楼主最后回复楼层
     *
     * @param array arrUids = array()
     * @return void
     */
    private function getAuthorLastNo($objMulti,$arrTids) {
        foreach ($arrTids as $postId) {
            $output = $objMulti->getResult('post:getInvertPostsByThreadId_'.$postId);
            if (false === $output || $output['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                Bingo_Log::warning('call service post:getInvertPostsByThreadId fail, input:['.serialize($postId).'], output:['.serialize($output).']');
            }
            // $intPostNo = intval($output['output']['output'][0]['post_infos'][0]['content']['post_no']);
            $intPostNo = intval($output['output']['output'][0]['post_infos'][0]['post_no']);
            $this->arrAuthorLastNo[$postId] = $intPostNo;
        } 
            
    }


    /**
     * @param array arrThreadStore
     * @param array arrThreadInfo
     * @param array arrThreadUserInfo
     * @return array
     */
    private function _arrange($arrThreadStore, $arrThreadInfo, $arrThreadUserInfo = array())
    {
        $intClientType    = intval($this->_objRequest->getCommonAttr('client_type'));
        $strClientVersion = strval($this->_objRequest->getCommonAttr('client_version'));
        $arrList = array();
        $arrOriginTids = array();
        $arrOriginThreadInfo = array();
        $arrTidToOrigin = array();  //记录分享贴id和原贴id的对应关系
        foreach( $arrThreadInfo as $key => $item){
            if( intval($item['original_tid']) > 0 ){
                $arrOriginTids[] = intval($item['original_tid']);
                $arrTidToOrigin[$item['thread_id']] = intval($item['original_tid']);
            }
        }
        if( !empty($arrOriginTids) ){
            $arrOriginThreadInfo = Molib_Util_ShareThread::getOriginThreadInfo($arrOriginTids);
        }
        foreach ($arrThreadStore as $arrThread) {
            //获取帖子类型
            $arrTypes = Tieba_Type_Thread::getTypeArray($arrThreadInfo[$arrThread['thread_id']]['thread_types']);
            $is_twzhibo = 0;
            if (isset($arrTypes['is_twzhibo_thread'])) {//图文直播
                $is_twzhibo = 1;
            }
            if (self::REPLYME_STATUS_DELETED == $arrThread['status']) {
                $arrThread['count'] = 0;//not remind with deleted
            }
            $threadId = $arrThread['thread_id'];
            $isBjh = isset($this->_arrBjhThread[$threadId]) ? 1 : 0;
            $strNameShow =  Molib_Util_User::getUserNickNameByVersion($intClientType, $strClientVersion, $this->arrUserInfo[$arrThread['lz_uid']], $this->arrUserNickname[$arrThread['lz_uid']]  );
            $strNameShow = empty($strNameShow) ? $arrThread['lz_name'] : $strNameShow;
            $arrListItem = array(
                'thread_id' => $arrThread['thread_id'],
                'title' => trim(html_entity_decode($arrThread['title'], ENT_COMPAT, 'UTF-8')),
                'forum_name' => $arrThread['forum_name'],
                'author' => array(
                    'lz_uid' => $arrThread['lz_uid'],
                    'name' => $arrThread['lz_name'],
                    // 'name_show' => empty($this->arrUserNickname[$arrThread['lz_uid']]) ? $arrThread['lz_name'] : $this->arrUserNickname[$arrThread['lz_uid']],
                    'name_show' => $strNameShow,
                    // 8.9
                    'user_portrait' => Tieba_Ucrypt::encode($arrThread['lz_uid'], $arrThread['lz_name']),
                ),

                // 8.9
                'media' => isset($arrThreadInfo[$arrThread['thread_id']]['media']) ? $arrThreadInfo[$arrThread['thread_id']]['media'] : array(),
                'god' => intval($arrThreadUserInfo[$arrThread['lz_uid']]['god']['status']),
                'is_follow' => intval($this->arrUserFollowed[$arrThread['lz_uid']]),
                //'post_number' => intval($arrThreadInfo[$arrThread['thread_id']]['post_num']),
                'is_deleted' => intval($arrThreadInfo[$arrThread['thread_id']]['is_deleted']),
                'post_no' => $this->arrAuthorLastNo[$arrThread['thread_id']],
                'post_no_msg' => empty($this->arrAuthorLastNo[$arrThread['thread_id']]) ? '' : sprintf(self::STR_LAST_POST_NO_MSG, $this->arrAuthorLastNo[$arrThread['thread_id']]),

                'last_time' => $arrThread['time'],
                'type' => $arrThread['type'],
                'status' => $arrThread['status'],
                'max_pid' => $arrThread['max_pid'],
                'min_pid' => $arrThread['min_pid'],
                'count' => $arrThread['count'],
                'mark_pid' => $arrThread['mark_pid'],
                'mark_status' => $arrThread['mark_status'],
                'reply_num' => $this->_getReplyNum($arrThreadInfo, $arrThread['thread_id']),
                'floor_num' => 0,//收藏到第几楼
                'thread_type' => $is_twzhibo ? Tieba_Type_Thread::TWZHIBO_THREAD : 0,
                'create_time' => $arrThreadInfo[$arrThread['thread_id']]['create_time'],
            );

            //百家号信息
            //百家号认证信息
            $arrUserInfo = $this->arrUserInfo[$arrThread['lz_uid']];
            $authId = isset($arrUserInfo['bjh_v_type']) ? intval($arrUserInfo['bjh_v_type']) : 0;
            $authDesc = isset($arrUserInfo['bjh_v_intro']) ? $arrUserInfo['bjh_v_intro'] : '';
            if ($authId > 0){
                $arrListItem['author']['baijiahao_info'] = array(
                    'name' => '',
                    'avatar' => '',
                    'avatar_h' => '',
                    'brief' => '',
                    'auth_id' => $authId,
                    'auth_desc' => $authDesc,
                );
            }
            //百家号头像信息
            $intClientType    = intval($this->_objRequest->getCommonAttr('client_type'));
            $strClientVersion = strval($this->_objRequest->getCommonAttr('client_version'));
            if (isset($arrUserInfo['bjh_avatar_flag']) && 'use' == $arrUserInfo['bjh_avatar_flag'] && isset($arrUserInfo['bjh_avatar']) && !empty($arrUserInfo['bjh_avatar'])){
                //是否展示百家号的头像版本
                if (Molib_Util_Version::isRebornServerVersion($intClientType, $strClientVersion)){
                    $arrListItem['author']['baijiahao_info']['avatar'] = $arrUserInfo['bjh_avatar'];
                }
            }

            //百家号帖子处理
            if ($isBjh) {
                $arrListItem['post_no'] = 0;
                $arrListItem['post_no_msg'] = '';
                $arrListItem = $this->getBjhContent($arrListItem);
            }

            // cartoon
            $intCartoonType = Molib_Util_Cartoon::getCartoonInfoFromThreadAttr(
                $arrThreadInfo[$arrThread['thread_id']], $arrCartoonInfo);
            if (Molib_Util_Cartoon::COMIC == $intCartoonType) {
                $arrListItem['cartoon_info'] = $arrCartoonInfo;
            } else if (Molib_Util_Cartoon::ANIMATION == $intCartoonType) {
                $arrListItem['animation_info'] = $arrCartoonInfo;
            }
            //share thread
            if (intval($arrThreadInfo[$arrThread['thread_id']]['original_tid']) > 0 && $arrTypes['is_rethread'] === true) {
                $arrOrigin = $arrOriginThreadInfo[$arrTidToOrigin[$arrThread['thread_id']]];
                $arrListItem['origin_thread_info'] = empty($arrOrigin) ? array() : $arrOrigin;
                $arrListItem['is_share_thread'] = 1;
                $arrListItem['origin_thread_info']['tid'] = intval($arrThreadInfo[$arrThread['thread_id']]['original_tid']);

            }

            $arrList[] = $arrListItem;
        }
        Molib_Util_ThreadFilter::filterThread($this->_objRequest, 'twzhibo', $arrList);

        // 临时修复 android 9.2.8.5 过滤图文直播贴
        if ($intClientType == Molib_Client_Define::CLIENT_TYPE_ANDROID &&
            (Molib_Util_Version::compare($strClientVersion, '9.2.8.5') == 0
            || Molib_Util_Version::compare($strClientVersion, '9.2.8.6') == 0)) {
            foreach ($arrList as $index => $thread){
                $arrThreadTypes = Tieba_Type_Thread::getTypeArray($thread['thread_types']);
                if (isset($arrThreadTypes['is_twzhibo_thread'])){
                    unset($arrList[$index]);
                }
            }
            $arrList = array_values($arrList);
        }

        // ios过滤图文直播贴
        //Molib_Util_ThreadFilter::process($arrList, $intClientType, $strClientVersion, 'twzhibo');

        return $arrList;
    }

    /**
     * getBjhContent
     * @param unknown $arrThreadList
     * @param unknown $arrUserList
     * @return multitype:
     */
    private function getBjhContent($arrRawThreadList){
        //获取帖子内容
        $arrOutput = self::$_objMulti->getResult('getBJHThreadContent');
        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput['errno']){
            Bingo_Log::warning('call getBJHThreadContent fail '.serialize($arrOutput));
            return $arrRawThreadList;
        }
//        var_dump($arrOutput);

        $threadId = $arrRawThreadList['thread_id'];
        $nid = $this->_arrBjhThread[$threadId]['nid'];
        $bjhTid = $this->_arrBjhThread[$threadId]['bjh_tid'];
        $threadType = $this->_arrBjhThread[$threadId]['thread_type'];
        $needPic = Tieba_Type_Thread::MOVIDEO_THREAD == $threadType ? 0 : 1;
        //帖子内容
        $threadContent = $arrOutput['output'][$nid];
        $arrRawThreadList['title'] = isset($threadContent['title']) ? $threadContent['title'] : '';
        //更新title、abstract、pic、文章封面、是否原创、thread_type
        if (!empty($threadContent)){
            $arrRawThreadList['title'] = isset($threadContent['title']) ? $threadContent['title'] : '';
            //获取图片信息
            if ($needPic){
                $mediaInfo = self::_checkBjhThreadMediaInfo($threadContent);
                $arrRawThreadList['media'] = $mediaInfo['media'];
                $arrRawThreadList['media_num'] = $mediaInfo['media_num'];
            }
        }else{
            Bingo_Log::warning($nid.'_content fail '.serialize($threadContent));
        }

        //获取评论数据
        $arrOut = self::$_objMulti->getResult('getBJHCommentNum_'.$bjhTid);
        if ($arrOut == false || $arrOut['errno'] !=Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call getCommentList fail post_ids is'.serialize($bjhTid).' output is '.serialize($arrOut));
        }else{
            $arrRawThreadList['reply_num'] = isset($arrOut['output']['ret'][$bjhTid]['count']) && intval($arrOut['output']['ret'][$bjhTid]['count']) > 0 ? intval($arrOut['output']['ret'][$bjhTid]['count']) : 0;
        }
        return $arrRawThreadList;
    }

    /**
     * [_checkBjhThreadMediaInfo description]
     * @param  [type] $arrRawThreadList [description]
     * @return [type]                   [description]
     */
    private static function _checkBjhThreadMediaInfo($threadContent) {
        $media = array();
        $mediaNum = 0;
        foreach ($threadContent['__tb_format_content'] as $content){
            if ('img' == $content['tag'] && !empty($content['src'])){
                $mediaNum += 1;
                $media[] = array(
                    'type' =>"pic",
                    'width' => $content['width'],
                    'height' => $content['height'],
                    'size' => $content['size'],
                    'big_pic' => $content['src'],
                    'small_pic' => $content['src'],
                    'water_pic' => $content['src']
                );
            }
        }

        $ret = array(
            'media' => $media,
            'media_num' => array(
                'pic' => $mediaNum
            )
        );

        return $ret;
    }



    /**
     * @param array arrThreadInfo
     * @param integer intThreadId
     * @return boolean
     */
    private function _getReplyNum($arrThreadInfo, $intThreadId)
    {
        if (isset($arrThreadInfo[intval($intThreadId)]['post_num'])) {
            return $arrThreadInfo[intval($intThreadId)]['post_num'];
        }
        else {
            return 0;
        }
    }

    /**
     * @param integer intErrno
     * @return array
     */
    private function _getError($intErrno)
    {
        if ($intErrno === Tieba_Errcode::ERR_SUCCESS) {
            return array(
                'error' => array(
                    'errno'  => $intErrno,
                    'errmsg' => 'success',
                ),
            );
        }
        return array(
            'error' => array(
                'errno'   => $intErrno,
                'errmsg'  => '',
                'usermsg' => Molib_Client_Error::getErrMsg($intErrno),
            ),
        );
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
