<?php
/***************************************************************************
 * 
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/



/**
 * @file getforumlistAction.php
 * <AUTHOR>
 * @date 2014/03/04 19:27:20
 * @brief 
 *  
 **/

//获取能够一键签到的贴吧列表
class getforumlistAction extends Molib_Client_BaseAction {
    private $_param = array();

    private $_err = 0;

    private $_timeThres = array(
            'show_dialog'=>false,
            'sign_notice'=>'',
            );
    private $_likeForum = array();
    private $_likeForumIds = array();
    private $_btxInfo = array();
    private $_score = array();
    private $_valid = TRUE;

    private $_forumInfo = array();

    private $_userInfo = array();
    private static $_userInfoSingleton = array();

    private $_mCaller = NULL;

    private static $_needMemberStragegy = false;
    private static $_newNeedMemberStragegy = false;
    private static $_arrMyFavorForum = array();
    private static $_monthSignInfo = array();
    private static $_isSuperMember = false;
    private $_haveTopUserLevel = false;
    private $_arrAdvert = array();
    private $_svipStatus = 0;
    private $_svipColorMem = '';
    private $_svip2Line = '';
    private $_svipMid = '';
    private $_signNewSwitch = 0;
    private $_arrBlock = array();


    const DEF_MSIGN_LV_THRES = 5;
    const DEF_MSIGN_MAX_FNUM = 400;
    const DEF_MSIGN_TIME_THRES = 1;
    const MAX_LIKE_FORUM_NUM = 200;
    const HAVE_TOP_USER_LEVEL = 7;
    const BAT_SIGN_STEP_NUM = 50;

    CONST BTX_PER_CALL = 8;

    //文案
    const SIGN_NOTICE = '零点到一点为签到高峰期，一键签到失败机率较大，请错开高峰期再来签到！';

    const NUM_NOTICE_PRE = '你'; 
    const NUM_NOTICE_MID = '级以上的吧超过了最大限度，现在仅支持对';
    const NUM_NOTICE_SUF = '个吧进行一键签到';

    const SIGN_TEXT_PRE = '吧内等级';
    const SIGN_TEXT_COLOR = '5级';
    const SIGN_TEXT_MID = '以上才可使用哦~快去提高等级吧！';
    const SIGN_TEXT_SUF = '每日最佳签到时间  9:00--16:00 、19:00--22:00';
    const SIGN_TEXT_LV = '级';

    const SIGN_TEXT_PRE_MEM = '正在使用';
    const SIGN_TEXT_COLOR_MEM = '贴吧超级会员';
    const SIGN_TEXT_MID_MEM = '特权,当前经验加速6倍！';
    const SIGN_TEXT_SUF_MEM = '每日最佳签到时间  9:00--16:00 、19:00--22:00';

    const SIGN_TEXT_PRE_NEW = '吧内等级';
    const SIGN_TEXT_COLOR_NEW = '7级';
    const SIGN_TEXT_MID_NEW = '以上才可使用哦~快去提高等级吧！';
    const SIGN_TEST_SUF_NEW = '贴吧超级会员无吧内等级限制,年费超级会员7倍经验!';


    const SIGN_TEXT_PRE_COM = '让签到更容易些吧！加入超级会员可签200个吧';

    const SIGN_TITLE = '级以上的吧';
    const SIGN_SMEM_SIGN_PROFIX = '现在仅支持对';
    const SIGN_SMEM_SIGN_TAIL = '个吧进行一键签到';
    
    const BUTTON_CONTENT = '开通超级会员';
    const CONTENT = '贴吧超级会员一键签到6倍经验';


    const HIGHT_MEMBER_LEVEL = 2;
    const HIGHT_MEMBER_MSIGN_START_LEVEL = 5;
    const MEMBER_LEVEL = 1;
    const MEMBER_MSIGN_START_LEVEL = 6;

    private  $_vipStatus;
    private  $_vipEndtime;
    private  $_intRealSignMaxNum = 200;
    private  $_intVipLevel = 1;
    private  $_arrSignMaxNum = array(
        '1' => 200,
        '2' => 250,
        '3' => 300,
        '4' => 350,
        '5' => 400,
    );
    private $_uids = array(
        '563946698','831268735','471737743','1576005325','669135510','976785768','1736908515','1213613023','1093896819','122825354','2293546354','83407361','749107394','732964586','1134652730','1195830502','1195853553',
    );
    
    protected function _getPrivateInfo() {

        $this->_param = array(
                'msign_lv_thres'=>$this->_objRequest->getStrategy('m_sign_level', self::DEF_MSIGN_LV_THRES),
                'msign_max_fnum'=>$this->_objRequest->getStrategy('max_sign_num', self::DEF_MSIGN_LV_THRES),
                'msign_time_thres'=>$this->_objRequest->getStrategy('sign_time_threshold', self::DEF_MSIGN_LV_THRES),
                'check_login' => true,
                'need_login' => true,
                );
        return $this->_param;
    }

    protected function _checkPrivate() {
        $user_id = $this->_objRequest->getCommonAttr('user_id', 0);
        if ($user_id <= 0) { // <=0 or <0?
            $this->_buildErr(Tieba_Errcode::ERR_MO_PARAM_INVALID);
            return false;
        }

        $intClientType    = intval($this->_objRequest->getCommonAttr('client_type'));
        $strClientVersion = strval($this->_objRequest->getCommonAttr('client_version'));

        //如果是 ios>5.8， 则要把5级，6级的也列出来，并算出5级，未签吧数量。  6级，未签吧数量。 -----//
        $strConf = $this->_objRequest->getStrategy('msign_use_member_strage');
        if ( Molib_Util_VersionMatch::checkClient($strConf, $intClientType, $strClientVersion) ){
            self::$_needMemberStragegy = true;
        }

        $this->_param['msign_member_lv_thres'] = $this->_param['msign_lv_thres'];
        if(true === self::$_needMemberStragegy){
            $this->_param['msign_member_lv_thres']  =   $this->_objRequest->getStrategy('m_sign_member_level', self::DEF_MSIGN_LV_THRES);
            $user_id = $this->_objRequest->getCommonAttr('user_id', 0);
            $this->_userInfo = self::_getUserInfoByUserId($user_id); 
            $this->_param['msign_lv_thres']         =   $this->_getMsignLvWithMemberLevel($this->_userInfo['pay_member_info']['props_id']);
        }
        $this->_vipStatus = intval($this->_userInfo['vipInfo']['v_status'] );
        $this->_vipEndtime = intval($this->_userInfo['vipInfo']['e_time'] );
        //----- 如果是 ios>5.8， 则要把5级，6级的也列出来，并算出5级，未签吧数量。  6级，未签吧数量。//
        // ---version > ios 6.2 拉取200个签到吧 zhaochuanyong
		
        //判断超级会员
        if(empty($this->_userInfo)){
        	$this->_userInfo = self::_getUserInfoByUserId($this->_param['user_id']);
        }
        if(intval($this->_userInfo['pay_member_info']['props_id']) == self::HIGHT_MEMBER_LEVEL){
        	self::$_isSuperMember = true;
        }
        self::littleTest($user_id);
        if($this->_isLittleTest){
            self::_setCopywriter();
            self::signMaxNum();
        }
        $strConf = $this->_objRequest->getStrategy('msign_use_new_member_strage');
        if ( Molib_Util_VersionMatch::checkClient($strConf, $intClientType, $strClientVersion) ){
            self::$_newNeedMemberStragegy = true;
        }
        if ( self::$_newNeedMemberStragegy == true ){
            $this->_param = array(
                    'msign_lv_thres'=>$this->_objRequest->getStrategy('m_sign_level', self::DEF_MSIGN_LV_THRES),
                    'msign_max_fnum'=>$this->_objRequest->getStrategy('max_member_sign_num', self::DEF_MSIGN_LV_THRES),
                    'msign_time_thres'=>$this->_objRequest->getStrategy('sign_time_threshold', self::DEF_MSIGN_LV_THRES),
                    'check_login' => true,
                    'need_login' => true,
                    );
        }

        // ---version > ios 6.2 拉取200个签到吧 zhaochuanyong


        return true;
    }

    protected function _execute() {
        $this->_param['user_id'] = $this->_objRequest->getCommonAttr('user_id', 0);
        $this->_param['client_type'] = intval($this->_objRequest->getCommonAttr('client_type'));
        $this->_param['client_version'] = strval($this->_objRequest->getCommonAttr('client_version'));

        $this->_checkTimeThres();

        if ( self::$_newNeedMemberStragegy === true ){
            if (($error = $this->_getLikeForum()) !== Tieba_Errcode::ERR_SUCCESS) {
                $this->_buildErr($error);
                return false;
            }
        }else{
            if (($error = $this->_getFavForum()) !== Tieba_Errcode::ERR_SUCCESS) {
                $this->_buildErr($error);
                return false;
            }
        }

        $this->_mCaller = new Libs_Data_Multi('clientui_get_msign_forum_list');

        if (count($this->_likeForumIds) > 0) {
            $this->_getLvPre();
            $this->_getSignInfoPre();
            $this->_getBtxPre();
            #$this->_getMonthMissSignInfoPre();
        }
        $this->_getActsCtrlPre();
        $this->_getAdvertPre();
        $this->_getSignNewSwitchPre();
        //安卓判断大于******* ios判断大于8.8.7
        if ( (Molib_Client_Define::CLIENT_TYPE_IPHONE == $this->_param['client_type'] && Molib_Util_Version::compare('8.8.8', $this->_param['client_version']) >= 0 )
        		|| (Molib_Client_Define::CLIENT_TYPE_ANDROID == $this->_param['client_type'] && Molib_Util_Version::compare("*******", $this->_param['client_version']) >= 0 )){
        	$this->_getUserBlockPre();
        }
        Bingo_Timer::start("muti_call_time");
        $this->_mCaller->call();
        Bingo_Timer::end("muti_call_time");
        $this->_getSignNewSwitchExec();
        //安卓判断大于******* ios判断大于8.8.7
        if ( (Molib_Client_Define::CLIENT_TYPE_IPHONE == $this->_param['client_type'] && Molib_Util_Version::compare('8.8.8', $this->_param['client_version']) >= 0 )
        		|| (Molib_Client_Define::CLIENT_TYPE_ANDROID == $this->_param['client_type'] && Molib_Util_Version::compare("*******", $this->_param['client_version']) >= 0 )){
        	$this->_getUserBlockExec();
        }
        if (count($this->_likeForumIds) > 0) {
            $error = $this->_getLvExec();
            if ($error !== Tieba_Errcode::ERR_SUCCESS) {
                $this->_buildErr($error);
                return false;
            }
            $this->_getSignInfoExec();
            $this->_getBtxExec();
            #$this->_getMonthMissSignInfoExec();
            $this->_buildForumInfo();
        }
        if(true === self::$_needMemberStragegy){
            $this->_buildUserInfo();
        }
        $this->_getActsCtrlExec();
        $this->_getAdvertExec();
        
        $this->_buildResponse();
    }

    private function _checkTimeThres() {
        $bolShow = $this->_isShowDialog(); //1点之前不允许一键签到,此时签到会给出文案提醒
        $strSignNotice = '';
        if ($bolShow) {
            $strSignNotice = self::SIGN_NOTICE;
        }
        $this->_timeThres = array(
                'show_dialog' => $bolShow,
                'sign_notice' => $strSignNotice,
                );
        return true;
    }

    private function _getFavForum() {
        $arrInput = array('user_id'=>$this->_param['user_id'],);
        $arrOut = Molib_Tieba_Service::call('user', 'getMyFavorForum', $arrInput);
        if(false === $arrOut){
            Bingo_Log::fatal('call user failed.  [sevice_name:user] [method:getMyFavorForum] [input:'.serialize($arrInput).']');
            return Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
        }

        self::$_arrMyFavorForum = $arrOut;
        if ($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal('call user:getMyFavorForum failed. input['.serialize($arrInput).'] output['.serialize($arrOut).']');
            return Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
        }

        $arrConcerList = array();
        $intCount = 0;
        if(is_array($arrOut['concern_forum']['forum']) && count($arrOut['concern_forum']['forum'] > 0)) { 
            foreach($arrOut['concern_forum']['forum'] as $value ) {
                if ($value['level_id'] >= $this->_param['msign_member_lv_thres']){
                    $arrConcerList[] = $value; 
                    $this->_likeForumIds[] = $value['forum_id'];
                    $intCount++;
                    if ($intCount >= $this->_param['msign_max_fnum']){
                        break;  
                    }
                }
            }
        }
        $this->_likeForum = $arrConcerList;
        return Tieba_Errcode::ERR_SUCCESS;
    }



    private function _getLikeForum() {
    	if(intval($this->_userInfo['pay_member_info']['props_id']) == self::HIGHT_MEMBER_LEVEL){
    	    $arrInput = array(
    	        'user_id'     => $this->_param['user_id'],
    	        'page_size'   => $this->_intRealSignMaxNum,
                'check_forum' => 1,
    	    );
    	}else{
    	    $arrInput = array(
    	        'user_id'     => $this->_param['user_id'],
    	        'page_size'   => self::BAT_SIGN_STEP_NUM,
                'check_forum' => 1,
    	    );
    	}
        $arrOut = Molib_Tieba_Service::call('perm', 'getLikeForumList', $arrInput);
        if(false === $arrOut){
            Bingo_Log::fatal('call perm failed.  [sevice_name:perm] [method:getLikeForumList] [input:'.serialize($arrInput).']');
            return Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
        }

        self::$_arrMyFavorForum = $arrOut;
        if ($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::fatal('call perm:getLikeForumList failed. input['.serialize($arrInput).'] output['.serialize($arrOut).']');
            return Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
        }

        $this->_likeForum = array();
        if(is_array($arrOut['output']['member_list']) && count($arrOut['out']['member_list'] > 0)) { 
            $this->_likeForum = $arrOut['output']['member_list'];
            foreach($arrOut['output']['member_list'] as $value ) {
                $this->_likeForumIds[] = $value['forum_id'];
            }
            //重新执行第二页，小流量
            if(true === $this->_isLittleTest && $this->_intVipLevel > 1){
            if($this->_intVipLevel > 1){
                Bingo_Timer::start('little_fid');
                $arrInput = array(
                    'user_id'     => $this->_param['user_id'],
                    'page_size'   => $this->_intRealSignMaxNum,
                    'page_no'     => 2,
                    'check_forum' => 1,
                );
                $arrOutByPage = Molib_Tieba_Service::call('perm', 'getLikeForumList', $arrInput);
                if(false === $arrOutByPage){
                    Bingo_Log::fatal('call perm failed.  [sevice_name:perm] [method:getLikeForumList] [input:'.serialize($arrInput).']');
                    return Tieba_Errcode::ERR_SUCCESS;
                }
                if(is_array($arrOutByPage['output']['member_list']) && count($arrOutByPage['out']['member_list'] > 0)) {
                    foreach($arrOutByPage['output']['member_list'] as $value ) {
                        $this->_likeForumIds[] = $value['forum_id'];
                    }
                    $this->_likeForum = array_merge($this->_likeForum,$arrOutByPage['output']['member_list']);
                }
                $this->_likeForumIds = array_slice($this->_likeForumIds,0,$this->_intRealSignMaxNum);
                $this->_likeForum = array_slice($this->_likeForum,0,$this->_intRealSignMaxNum);
                Bingo_Timer::end('little_fid');
            }
            }
        }
        return Tieba_Errcode::ERR_SUCCESS;
    }

    private function _getLvPre() {

        $intCount = count($this->_likeForumIds);
        if($intCount > self::BAT_SIGN_STEP_NUM  && $intCount <= $this->_intRealSignMaxNum){
            $i = 0;
            $arrChunk = array_chunk($this->_likeForumIds, self::BAT_SIGN_STEP_NUM);
            foreach($arrChunk as $arrLikeForumIds){
                $i++;

                $arrInput = array(
                                  'user_id' => $this->_param['user_id'],
                                  'forum_ids' => $arrLikeForumIds,
                                 );
                $this->_mCaller->register('lv_'.$i, array(
                                                          'serviceName'=>'perm',
                                                          'method'=>'mgetUserLevel',
                                                          'input'=>$arrInput,
                                                         ));

            }

        }else{

            $arrInput = array(
                              'user_id' => $this->_param['user_id'],
                              'forum_ids' => $this->_likeForumIds,
                             );
            $this->_mCaller->register('lv', array(
                                                  'serviceName'=>'perm',
                                                  'method'=>'mgetUserLevel',
                                                  'input'=>$arrInput,
                                                 ));

        }

    }
    private function _getLvExec() {
        $intCount = count($this->_likeForumIds);
        if($intCount > self::BAT_SIGN_STEP_NUM  && $intCount <= $this->_intRealSignMaxNum){
            $intLoop = ceil($intCount/self::BAT_SIGN_STEP_NUM);
            for ($i = 1; $i <= $intLoop; $i++) {
                $arrOut = $this->_mCaller->getResult('lv_'.$i);

                if ($arrOut['errno'] === Tieba_Errcode::ERR_SUCCESS) {
                    foreach ($arrOut['score_info'] as $scoreInfo) {
                        $this->_score[$scoreInfo['forum_id']] = $scoreInfo;
                    }
                }
            }

            if(count($this->_score)>0){
                return Tieba_Errcode::ERR_SUCCESS;
            } else {
                Bingo_Log::warning('perm:mgetUserLevel error. forum_ids['.serialize($this->_likeForumIds).'] user_id['.$this->_param['user_id'].']');
                return Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            }

        }else{

            $arrOut = $this->_mCaller->getResult('lv');
            if ($arrOut['errno'] === Tieba_Errcode::ERR_SUCCESS) {
                foreach ($arrOut['score_info'] as $scoreInfo) {
                    $this->_score[$scoreInfo['forum_id']] = $scoreInfo;
                }
                return Tieba_Errcode::ERR_SUCCESS;
            } else {
                Bingo_Log::warning('perm:mgetUserLevel error. forum_ids['.serialize($this->_likeForumIds).'] user_id['.$this->_param['user_id'].']');
                return Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            }

        }
    }

    private function _getSignInfoPre() {
        $arrInput = array(
                'forum_id' => $this->_likeForumIds,
                'user_id' => $this->_param['user_id'],
                );
        $this->_mCaller->register('signInfo', array(
                    'serviceName'=>'sign',
                    'method'=>'getUserSignForums',
                    'input'=> $arrInput,
                    ));
    }
    private function _getSignInfoExec() {
        $arrOut = $this->_mCaller->getResult('signInfo');
        if ($arrOut['errno'] === Tieba_Errcode::ERR_SUCCESS) {
            $this->_signInfo = $arrOut['arr_user_info'];
            return Tieba_Errcode::ERR_SUCCESS;
        } else {
            Bingo_Log::warning('sign:getUserSignForums error. forum_ids['.serialize($this->_likeForumIds).'] user_id['.$this->_param['user_id'].']');
            return Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
        }
    }

    private function _getBtxPre() {
        $btxIds = array_chunk($this->_likeForumIds, self::BTX_PER_CALL);
        foreach ($btxIds as $i => $ids) {
            $this->_mCaller->register('btx'.$i, array(
                        'serviceName'=>'forum',
                        'method'=>'mgetBtxInfo',
                        'input'=>array('forum_id'=>$ids),
                        ));
        }
    }
    private function _getBtxExec() {
        for ($i = 0; $i < count($this->_likeForumIds); $i += self::BTX_PER_CALL) {
            $arrOut = $this->_mCaller->getResult('btx'.($i/self::BTX_PER_CALL));
            if ($arrOut['errno'] === Tieba_Errcode::ERR_SUCCESS) {
                $this->_btxInfo += $arrOut['output'];
            } else {
                Bingo_Log::warning('forum:mgetBtxInfo error. forum_ids['.serialize(array_slice($this->_likeForumIds, $i, self::BTX_PER_CALL)).'] output['.serialize($arrOut).']');
            }
        }
        return Tieba_Errcode::ERR_SUCCESS;
    }

    private function _getActsCtrlPre() {
    	//会员和非会员都是一天一次
    	$arrInput = array(
    			'req' => array(
    					'rulegroup' => array('app'),
    					'cmd'       => 'multisign',
    					'app' => 'msign',
    					'uid' => $this->_param['user_id'],
    			),
    	);
        $this->_mCaller->register('actsCtrl', array(
                    'serviceName'=>'anti',
                    'method'=>'antiActsctrlQuery',
                    'input'=>$arrInput,
                    ));
    }

    private function _getActsCtrlExec() {
        $arrOut = $this->_mCaller->getResult('actsCtrl');

        if(false === $arrOut){
            Bingo_Log::warning('call anti:antiActsctrlQuery failed. [user_id:'.$this->_param['user_id'].']');
            $this->_valid = FALSE;
        }

        if ($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call  anti:antiActsctrlQuery err. [userid:'.$this->_param['user_id'].'] [out:'.serialize($arrOut).']');
            $this->_valid = FALSE;
        }
        return Tieba_Errcode::ERR_SUCCESS;
    }

    private function _isShowDialog() {
        $strTime = date('G:i:s', time());
        $arrTime = explode(':',$strTime);
        $hour = $arrTime[0];
        $minute = $arrTime[1];
        $bolShow =  false;  

        if ($hour == 23 && $minute >= 59) { 
            $bolShow = true; 
        } else if ($hour >= 0 && $hour < $this->_param['msign_time_thres']) {
            $bolShow = true; 
        }       

        return $bolShow;
    }

    private function _buildForumInfo() {
        $arrOut = array();
        foreach ($this->_likeForum as $arrForum) {
            $arrTmp = array();
            $forum_id = $arrForum['forum_id'];
            $arrTmp['forum_id'] = $forum_id;
            $arrTmp['forum_name'] = $arrForum['forum_name'];
            $arrTmp['user_level'] = $this->_score[$forum_id]['level_id'];
            if ( intval($this->_score[$forum_id]['level_id']) >= self::HAVE_TOP_USER_LEVEL ){
                $this->_haveTopUserLevel = true;
            }
            $arrTmp['user_exp'] = $this->_score[$forum_id]['cur_score'];
            $arrTmp['need_exp'] = $this->_score[$forum_id]['cur_score'] + $this->_score[$forum_id]['score_left'];
            $arrTmp['is_sign_in'] = isset($this->_signInfo[$forum_id]['is_sign_in']) ? $this->_signInfo[$forum_id]['is_sign_in'] : 0;
            $arrTmp['cont_sign_num'] = isset($this->_signInfo[$forum_id]['cont_sign_num']) ? $this->_signInfo[$forum_id]['cont_sign_num'] : 0;
            
            if( isset($this->_monthSignInfo[$forum_id]) ){
                $intMissDayNum = intval(date("d",time())) - intval($this->_monthSignInfo[$forum_id]);
                $arrTmp['miss_sign_num'] = ($intMissDayNum > 0)? $intMissDayNum :0;
            }
            $arrTmp['avatar'] = '';
            if (isset($this->_btxInfo[$forum_id]['attrs']['card_p1'])) {
                $arrStyle = Bingo_String::json2array($this->_btxInfo[$arrForum['forum_id']]['attrs']['card_p1']['style_name'], Bingo_Encode::ENCODE_UTF8);
                if (isset($arrStyle['avatar'])) {
                    $arrTmp['avatar'] = $arrStyle['avatar'];
                }
            } elseif (isset($this->_btxInfo[$forum_id]['card']['avatar'])) {
                $arrTmp['avatar'] = $this->_btxInfo[$forum_id]['card']['avatar'];
            }
            $arrOut[] = $arrTmp;
        }
        $this->_forumInfo = $arrOut;
        return true;
    }

    private function _buildUserInfo(){
        if(empty($this->_userInfo)){
            $this->_userInfo = self::_getUserInfoByUserId($this->_param['user_id']); 
        }
        $this->_userInfo['unsign_info'] = $this->_getUnSignInfo();
        return $arrUserInfo;
    }


    private function _getUnSignInfo(){
        if(false === self::$_needMemberStragegy){
            return array();
        }

        //遍历从getFavo中拿到的信息，把5级，6级的数量列出来， 返回
        $arrOut = self::$_arrMyFavorForum;
        if(is_array($arrOut['concern_forum']['forum']) && count($arrOut['concern_forum']['forum'] > 0)) { 
            foreach($arrOut['concern_forum']['forum'] as $value){
                if ($value['level_id'] >= $this->_param['msign_member_lv_thres']){
                    if(1 == $this->_signInfo[$value['forum_id']]['is_sign_in']){
                        //pass 已经签过到了
                    }else{
                        $arrOutTmp[$value['level_id']][] = $value;
                    }
                }
            }
        }

        return array(
                array('level'=>self::HIGHT_MEMBER_MSIGN_START_LEVEL, 'num'=>count($arrOutTmp[self::HIGHT_MEMBER_MSIGN_START_LEVEL])),
                array('level'=>self::MEMBER_MSIGN_START_LEVEL, 'num'=>count($arrOutTmp[self::MEMBER_MSIGN_START_LEVEL])),
                );

    }


    //quite tricky adaptation for compatibility
    private function _buildErr($error) {
        $this->_err = $error;
        if ($error !== Tieba_Errcode::ERR_SUCCESS) {
            if ($error !== Tieba_Errcode::ERR_MO_PARAM_INVALID) {
                $error = Tieba_Errcode::ERR_MO_INTERNAL_ERROR;
            }
            $this->_error($error, Molib_Client_Error::getErrMsg($error));
        }
        $arrErr = array(
                'error'=>array(
                    'errno'=>$this->_err,
                    'errmsg'=>Tieba_Error::getErrmsg($this->_err),
                    'usermsg'=>Bingo_Encode::convert(Tieba_Error::getUserMsg($this->_err), Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK),
                    ),
                );
        $this->_objResponse->setOutData($arrErr);
    }

    private function _buildResponse() {
        $arrRes = array();
        $arrRes['forum_info'] = $this->_forumInfo;
        if(true === self::$_needMemberStragegy){
            $arrRes['user']         = $this->_userInfo;
            //普通用户不可用，普通会员不可用，超级会员可用
            if($arrRes['user']['pay_member_info']['props_id'] === 2){
            	$arrRes['can_use'] = 1;
            }else{
            	$arrRes['can_use'] = 0;
            }
            //add
            $arrRes['button_content'] = self::BUTTON_CONTENT;
            $arrRes['content'] = self::CONTENT;
        }
        $arrRes['show_dialog'] = $this->_timeThres['show_dialog'];
        $arrRes['sign_notice'] = $this->_timeThres['sign_notice'];
        $arrRes['title'] = $this->_param['msign_member_lv_thres'] . self::SIGN_TITLE;

        if(self::$_newNeedMemberStragegy === true){

            if( intval($this->_userInfo['pay_member_info']['props_id']) == self::HIGHT_MEMBER_LEVEL
            ){ 
                if($this->_svipStatus === 1){
                    $arrRes['text_pre'] = self::SIGN_TEXT_PRE_MEM;
                    $arrRes['text_color'] = $this->_svipColorMem;
                    $arrRes['text_mid'] = $this->_svipMid;
                    $arrRes['text_suf'] = $this->_svip2Line;
                    $svipMem = 1;
                    //$arrRes['text_mid'] = self::SIGN_TEXT_MID_MEM;
                }else{
                    $arrRes['text_pre'] = self::SIGN_TEXT_PRE_MEM;
                    $arrRes['text_color'] = self::SIGN_TEXT_COLOR_MEM;
                    $arrRes['text_mid'] = self::SIGN_TEXT_MID_MEM;
                }
            }else{
                if ( $this->_haveTopUserLevel !== true ){    
                    $arrRes['text_pre'] = self::SIGN_TEXT_PRE_NEW;
                    $arrRes['text_color'] = self::SIGN_TEXT_COLOR_NEW;
                    $arrRes['text_mid'] = self::SIGN_TEXT_MID_NEW;
                    $arrRes['text_suf'] = self::SIGN_TEST_SUF_NEW;
                    $newMem = 1;
                }else{
                    $arrRes['text_pre'] = self::SIGN_TEXT_PRE_COM;
                    $arrRes['text_color'] = '';
                    $arrRes['text_mid'] = '';
                }
            }
            if($newMem !== 1 && $svipMem !== 1){
                $arrRes['text_suf'] = self::SIGN_TEXT_SUF_MEM;
            }
            $strNumNotice = self::SIGN_SMEM_SIGN_PROFIX.self::DEF_MSIGN_MAX_FNUM.self::SIGN_SMEM_SIGN_TAIL;
        }else{
            $arrRes['text_pre'] = self::SIGN_TEXT_PRE;
            $arrRes['text_color'] = $this->_param['msign_lv_thres'] . self::SIGN_TEXT_LV;
            $arrRes['text_mid'] = self::SIGN_TEXT_MID;
            $arrRes['text_suf'] = self::SIGN_TEXT_SUF;

            $strNumNotice = self::NUM_NOTICE_PRE.$this->_param['msign_lv_thres'].self::NUM_NOTICE_MID.$this->_param['msign_max_fnum'].self::NUM_NOTICE_SUF;
        }
        $arrRes['num_notice'] = $strNumNotice;
        $arrRes['level'] = intval($this->_param['msign_lv_thres']);
        $arrRes['sign_max_num'] = $this->_param['msign_max_fnum'];
        $arrRes['valid'] = $this->_valid;
        $arrRes['msign_step_num'] = self::BAT_SIGN_STEP_NUM;
        if(!empty($this->_arrAdvert)){
            $arrRes['advert']['banner_pic'] = $this->_arrAdvert['banner_pic'];
            $arrRes['advert']['banner_url'] = $this->_arrAdvert['banner_url'];
        }
        $arrRes['sign_new'] = $this->_signNewSwitch;
        if ($this->_err === Tieba_Errcode::ERR_SUCCESS) {
            $this->_buildErr($this->_err);
        }
        $arrRes['anti_info'] = $this->_arrBlock;
        $this->_objResponse->setOutData($arrRes);
    }

    /**
     * @input
     * $intUserId = 23456;
     *
     */
    private static function _getUserInfoByUserId($intUserId){

        if(isset(self::$_userInfoSingleton[$intUserId])){
            return self::$_userInfoSingleton[$intUserId];
        }

        //Tieba_Service:call
        $strServiceName = 'user';
        $strMethod = 'getUserData';

        $arrInput = array(
                'user_id' => $intUserId,
                );

        //Bingo_Log::debug(sprintf('talk to servicename:[%s] method:[%s] input:[%s] ',$strServiceName,$strMethod, serialize($arrInput) ) );

        Bingo_Timer::start("{$strServiceName}_{$strMethod}");
        $arrOutput = Tieba_Service::call($strServiceName,$strMethod, $arrInput,null,null,'post','php','utf-8');
        Bingo_Timer::end("{$strServiceName}_{$strMethod}");

        if (false === $arrOutput ) {
            Bingo_Log::warning('Failed to call User:getUserData. [user_id:'.$intUserId.']');
            return array();
        }
        //check err_no
        if ( isset($arrOutput['errno']) && (0 == intval($arrOutput['errno'])) ) {
            //success nothing to do
            $arrRet = $arrOutput;
            $arrUserInfoTmp = $arrRet['user_info'][0];
            $arrTmp = Libs_Util_User::getUserInfo($arrUserInfoTmp);
            $arrTmp = Molib_Util_Array::fetchArray( 	$arrTmp,
                    array('pay_member_info','vipInfo')
                    );
            $arrUserInfos = $arrTmp; 
            self::$_userInfoSingleton[$intUserId] = $arrUserInfos;;
            return $arrUserInfos;

        } else {
            //err,print log
            Bingo_Log::warning('Err to call User:getUserData. [user_id:'.$intUserId.']');
            return array();
        }
    }

    private function _getMsignLvWithMemberLevel($intLevel){
        if(self::HIGHT_MEMBER_LEVEL == $intLevel){//高级会员
            return self::HIGHT_MEMBER_MSIGN_START_LEVEL; 
        }
        if(self::MEMBER_LEVEL == $intLevel){//普通会员
            return self::MEMBER_MSIGN_START_LEVEL;
        }

        $intDefaultLevel = $this->_objRequest->getStrategy('m_sign_level', self::DEF_MSIGN_LV_THRES);
        return $intDefaultLevel;
    }

    private function _getMonthMissSignInfoPre($arrParams){

        foreach($this->_likeForumIds as $intForumId){
            $arrInput = array(
                    'forum_id' => intval($intForumId),
                    'user_id' => $this->_param['user_id'],
                    'year' => date("Y",time()),
                    'month' => date("m",time()),
            );

            $this->_mCaller->register('monthSignInfo'.trim($intForumId), array(
                            'serviceName'=>'sign',
                            'method'=>'getUserMonSignInfo',
                            'input'=> $arrInput,
            ));
        }
    }
    
    private function _getMonthMissSignInfoExec() {
        foreach( $this->_likeForumIds as $intForumId ){
            $arrOut = $this->_mCaller->getResult('monthSignInfo'.trim($intForumId));
            if ($arrOut['errno'] === Tieba_Errcode::ERR_SUCCESS) {
                $this->_monthSignInfo[$intForumId] = count($arrOut['his_info']);
            } else {
                Bingo_Log::warning('sign:getUserMonSignInfo error. forum_id['.$intForumId.'] user_id['.$this->_param['user_id'].']');
            }
        }
    }
    private function _getAdvertPre(){
        $arrInput = array(
            'user_id' => $this->_param['user_id'],
            'client_type' => $this->_param['client_type'],
        );
        $this->_mCaller->register('signAdvert', array(
            'serviceName'=>'sign',
            'method'=>'sendSignAd',
            'input'=>$arrInput,
        ));
    }
    private function _getAdvertExec(){
        $arrOut = $this->_mCaller->getResult('signAdvert');
        if(false === $arrOut || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            if($arrOut['errno'] === Tieba_Errcode::ERR_NO_RECORD || $arrOut['errno'] === Tieba_Errcode::ERR_PERM_NOT_EXIST){
                return array();
            }
            Bingo_Log::warning('call sign ad is failed.  [sevice_name:sign] [method:sendSignAd] [input:'.serialize($arrInput).']output'.serialize($arrOut).']');
            return array();
        }
        $this->_arrAdvert = $arrOut['data'];
    }
    private function _setCopywriter(){
        $vipStatus = intval($this->_userInfo['vipInfo']['v_status']);
        $vipEndtime = $this->_userInfo['vipInfo']['e_time'];
        $vipLevel = intval($this->_userInfo['vipInfo']['v_level']);
        $vipSignNum = $this->_arrSignMaxNum[$vipLevel];
        if($vipStatus === 3 && $vipEndtime > time()){
            $this->_svipStatus = 1;
            $this->_svipColorMem = "贴吧年费超级会员 SVIP".$vipLevel." 级";
            $this->_svip2Line = "当前经验加速7倍，可签到".$vipSignNum."个吧";
            $this->_svipMid = "特权";
        }elseif($vipStatus === 2 && $vipEndtime > time()){
            $this->_svipStatus = 1;
            $this->_svipColorMem = "贴吧超级会员 SVIP".$vipLevel." 级";
            $this->_svip2Line = "当前经验加速6倍，可签到".$vipSignNum."个吧";
            $this->_svipMid = "特权";
        }
    }
    //获取喜欢的吧数量
    private function signMaxNum(){
        if(1 < $this->_vipStatus && time() < $this->_vipEndtime){
            $this->_intVipLevel = intval($this->_userInfo['vipInfo']['v_level']);
            if($this->_intVipLevel < 1 || $this->_intVipLevel > 5){     //当前最高为5级，最低为1级
                $this->_intVipLevel = 1;
            }
            $this->_intRealSignMaxNum = $this->_arrSignMaxNum[$this->_intVipLevel];
        }
    }
    private function littleTest($user_id){
        $this->_isLittleTest = true;
        return true;
        if(in_array($user_id,$this->_uids)){
            $this->_isLittleTest = true;
        }else{
            $this->_isLittleTest = false;
        }
    }
    private function _getSignNewSwitchPre(){
        $arrInput = array();
        $this->_mCaller->register('signNewSwitch', array(
            'serviceName'=>'sign',
            'method'=>'getclientMsignSwitch',
            'input'=>$arrInput,
        ));
    }
    private function _getSignNewSwitchExec(){
        $arrOut = $this->_mCaller->getResult('signNewSwitch');
        if(false === $arrOut || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call sign switch is failed.  [sevice_name:sign] [method:getclientMsignSwitch] [input:'.serialize($arrInput).']output'.serialize($arrOut).']');
            $this->_signNewSwitch = 0;
        }
        if($arrOut['data'] > 0){
            $this->_signNewSwitch = 1;
        }
    }
    
    /**
     * 具体的封禁状态
     * @param
     * @return
     */
    private function _getUserBlockPre() {
    	
    	$arrInput = array(
    			'req' => array(
    					'user_id' => $this->_param['user_id'],
    					'forum_id'=> 0,
    			),
    	);
    	$this->_mCaller->register('anti:antiGetStateMsg', array(
    			'serviceName'	=> 'anti',
    			'method'		=> 'antiGetStateMsg',
    			'input'			=> $arrInput,
				'ie' 			=> 'utf-8',
    	));
        $arrInput = array (
            'reqs' => array (
                'check_block' => array (
                    'service_type' => 'blockid',
                    'key' => $this->_param['user_id'],
                    'forum_id' => 0,
                ),
            )
        );
        $this->_mCaller->register('queryBlockAndAppealInfo', array(
                'serviceName'   => 'userstate',
                'method'        => 'queryBlockAndAppealInfo',
                'input'         => $arrInput,
                'ie'            => 'utf-8',
        ));


    }
    
    /**
     * 具体的封禁状态
     * @param
     * @return
     */
    private function _getUserBlockExec(){
        $arrBlock = array();
        $arrOut = $this->_mCaller->getResult('anti:antiGetStateMsg');
        if(false === $arrOut || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('callanti:antiGetStateMsg is failed.  [sevice_name:anti] [method:antiGetStateMsg] [input:user_id='.$this->_param['user_id'].']output'.serialize($arrOut).']');
        } elseif( $arrOut['res']['stateno'] > 0) {
            $arrBlock = array(
                "block_content"  => $arrOut['res']['block_errmsg'],
                "block_dealurl" => $arrOut['res']['block_dealurl'],
                "block_confirm" => $arrOut['res']['block_confirm'],
                "block_cancel"  => $arrOut['res']['block_cancel'],
            );
        }
        $arrOut = $this->_mCaller->getResult('queryBlockAndAppealInfo');
        if(false === $arrOut || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning('call userstate:queryBlockAndAppealInfo fail.[input: user_id='.$this->_param['user_id'].']output'.serialize($arrOut).']');

        }else if( isset($arrOut['res']['appeal_status']) && 1 == $arrOut['res']['appeal_status'] ){
            $arrBlock['appeal_status'] = intval($arrOut['res']['appeal_status']);
            $arrBlock['appeal_msg'] = empty($arrOut['res']['appeal_msg']) ? Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_CLIENT_BLOCK_IS_APPEALED_ERROR) : strval($arrOut['res']['appeal_msg']);
        }
        $this->_arrBlock = $arrBlock;  
    	
    }
}





/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
