<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file getBawuInfoAction.php
 * 
 * <AUTHOR>
 *         @date 2015/06/15
 *         @brief bawu team info
 *        
 *        
 */
 class getBawuInfoAction extends Molib_Client_BaseAction {

 	private $strName       = '';
	private $strNameShow   = '';
	private $_isPrivateForum = 0;
 	private $_bawuTotalNum = 0;
 	private $_bawuListInfo = array(); 	
 	private $_bawuUserInfo = array();
 	private $_bawuRoleInfo = array(
        'manager'            => '吧主',
        'assist'             => '小吧主',
        'pri_content_assist' => '内容吧务',
        'pri_manage_assist'  => '管理吧务',
        'picadmin'           => '图片小编',
        'publication_editor' => '吧刊主编',
        'publication'        => '吧刊小编',
        'voiceadmin'         => '语音小编',
        'videoadmin'         => '视频小编',
        'fourth_manager'     => '第四吧主',
        'broadcast_admin'    => '广播小编',
        'place_operator'     => '地方商业吧运营商',
        'place_editor'       => '地方商业吧小编',
        'pt_browse'          => '平台化-官方吧浏览',
        'pt_handle'          => '平台化-官方吧操作',
        'pt_pm'              => '平台化-用户运营',
        'pt_chudian'         => '平台化-触点系统运营',
        'ueg_pm'             => 'UEG-PM',
        'vertical_operator'  => '垂直化吧运营商',
        'profession_manager' => '职业吧主',
        'daquan'             => '大全小编',
        'daquan_editor'      => '大全主编',
        'disk_editor'        => '网盘小编',
        'top_setter'         => '置顶模块编辑',
        'post_deleter'       => '帖子删除员',

    );

    private $_bawuInfo;
    private $_applyInfo;

 	public function _getPrivateInfo() {
 		
 		return array(
 			'forum_id' => intval($this->_getInput('forum_id', 0)),		
 		);
 	}
 	
 	public function _checkPrivate() {
 		
 		return true;
 	}
 	
    /**
     * [_execute description]
     * @return [type] [description]
     */
 	public function _execute() {

 	    $this->_getForumAttr();
 	 	$this->_getBawuList();
        if ($this->_isPrivateForum){
            $intClientType = $this->_objRequest->getCommonAttr('client_type', 0);
            $strClientVersion = strval($this->_objRequest->getCommonAttr('client_version', ''));
            //判断是否是私有化吧版本
            if (Molib_Util_Forum::isPriforumVersion($intClientType, $strClientVersion)){
                $this->_applyInfo = array();
            }else{
                $this->_applyInfo = array(
                    'manager_left_num' => 0,
                    'manager_apply_url' => '',
                    'assist_left_num' => 0,
                    'assist_apply_url' => '',
                );
            }
        }else{
            $this->_getApplyInfo();
        }
        $this->_buildResponse();
        return true;
 	}

     /**
      * 获取吧属性
      * @return boolean
      */
     private function _getForumAttr() {

         $arrInput = array(
             'forum_id' => $this->_objRequest->getPrivateAttr('forum_id'),
         );
         $arrOut = Molib_Tieba_Service::call('forum', 'getForumAttr', $arrInput);

         if(false === $arrOut) {
             Bingo_Log::fatal("call forum:getForumAttr failed. input=".serialize($arrInput)." output=".serialize($arrOut));
             return false;
         }

         if(Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']){
             Bingo_Log::warning("call forum:getForumAttr errno not 0. input=".serialize($arrInput)." output=".serialize($arrOut));
             return false;
         }
         $this->_isPrivateForum = isset($arrOut['output']['is_private_forum']) ?  $arrOut['output']['is_private_forum'] : 0;
         return true;
     }

    /**
     * [_getBawuList description]
     * @return [type] [description]
     */
 	private function _getBawuList() {
        $intClientType = $this->_objRequest->getCommonAttr('client_type', 0);
        $strClientVersion = strval($this->_objRequest->getCommonAttr('client_version', ''));

 		$arrInput = array(
 			'forum_id' => $this->_objRequest->getPrivateAttr('forum_id'),
 		);
 		$arrOut = Molib_Tieba_Service::call('perm', 'getBawuList', $arrInput);

 		if(false === $arrOut) {
 			
 			Bingo_Log::fatal("call perm:getBawuList failed. input=".serialize($arrInput)." output=".serialize($arrOut));
 			return false;
 		}
 		
 		if(Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']){
 			
 			Bingo_Log::warning("call perm:getBawuList errno not 0. input=".serialize($arrInput)." output=".serialize($arrOut));
 			return false;
 		}
 		
        $this->_bawuInfo = $arrOut;
 		$arrUserId = array();

 		foreach ($arrOut['output'] as $role => $roleInfo) {
 			
 			if(!is_array($roleInfo) || !isset($this->_bawuRoleInfo[$role])) {
 				continue;
            }
            $this->_bawuTotalNum += count($roleInfo);
            
 			$bawuInfoTmp = array();
            $strNameShow = '';
 			foreach ($roleInfo as $num => $userInfo) {
                $strNameShow = !empty($userInfo['user']['user_nickname']) ? $userInfo['user']['user_nickname'] : $userInfo['user']['user_name'];
                $arrUserId[] = $userInfo['user']['user_id'];
 				$bawuInfoTmp[$num]['forum_id'] =  $userInfo['user']['forum_id'];
 				$bawuInfoTmp[$num]['user_id'] =  $userInfo['user']['user_id'];
 				$bawuInfoTmp[$num]['user_name'] =  !empty($userInfo['user']['user_name']) ? $userInfo['user']['user_name'] : $userInfo['user']['user_nickname'];
                $bawuInfoTmp[$num]['name_show'] = Molib_Util_User::getUserNickNameByVersion($intClientType, $strClientVersion, $userInfo['user'], $strNameShow);
                //  $bawuInfoTmp[$num]['name_show'] =  !empty($userInfo['user']['user_nickname']) ? $userInfo['user']['user_nickname'] : $userInfo['user']['user_name'];
 				$bawuInfoTmp[$num]['role_name'] =  $userInfo['user']['role_name'];
 				$bawuInfoTmp[$num]['role_id'] =  $userInfo['user']['role_id'];
 				$bawuInfoTmp[$num]['portrait'] = strval(Tieba_Ucrypt::encode($userInfo['user']['user_id'], $userInfo['user']['user_name']));

				//<NAME_EMAIL> 2015.9.15
				if ($bawuInfoTmp[$num]['role_name'] == 'profession_manager') {
					$arrInput = array(
						'user_id'          => $bawuInfoTmp[$num]['user_id'],
						'need_pass_info'   => 1,
						'need_follow_info' => 0,
						'call_from'        => 'client_home',
					);
					$arrOut = Molib_Tieba_Service::call('user', 'getUserDataEx', $arrInput);
					if ($arrOut['errno'] != Tieba_Errcode::ERR_SUCCESS) {
						Bingo_Log::warning('call user:getUserDataEx fail. input:'.serialize($arrInput).'output:'.serialize($arrOut));
						continue;
					}
					$arrUserInfo = $arrOut['user_info'];
					$this->strName = $arrUserInfo['user_name'];
					if (isset($arrUserInfo['profession_manager_nick_name']) && !empty($arrUserInfo['profession_manager_nick_name'])) {
						$this->strNameShow = $arrUserInfo['profession_manager_nick_name'];
					} else {
						$this->strNameShow = (isset($arrUserInfo['user_nickname']) && !empty($arrUserInfo['user_nickname'])) ? $arrUserInfo['user_nickname'] : $this->strName;
					}
					$bawuInfoTmp[$num]['user_name'] = $this->strNameShow;

				}//end
				$this->_bawuUserInfo[] = $bawuInfoTmp[$num];

 			}
 			$this->_bawuListInfo[] = array(
 				'role_info' => $bawuInfoTmp,
 				'role_name' => $this->_bawuRoleInfo[$role],
 			);
 			
 		}
 	 		
 		$arrUserLevelOut = $this->_mgetUserLevel();
 		if(!$arrUserLevelOut){
 			return true;
 		}

 		//获取用户百家号信息
        $this->mgetUserDataEx($arrUserId);

 		$bawuListTmp = $this->_bawuListInfo;
 		foreach ($bawuListTmp as $key => $roleInfo) {
 			
 			foreach ($roleInfo['role_info'] as $num => $userInfo) {
                $user_id = $userInfo['user_id'];
 				$this->_bawuListInfo[$key]['role_info'][$num] = array_merge($userInfo,$this->_bawuUserInfo[$user_id]);
 			}
 		}
 		
 		return true;
 	}

     /**
      * 获取用户百家号信息
      * @param $arrUserId
      * @return bool
      */
 	private function mgetUserDataEx($arrUserId) {
        $arrInput = array(
            'user_id' => $arrUserId,
            'need_follow_info' => 0,
            'need_pass_info' => 0,
            'get_icon' => 0,
        );
        $arrOut = Molib_Tieba_Service::call('user', 'mgetUserDataEx', $arrInput);
        if(false === $arrOut || Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']) {
            Bingo_Log::warning("call user:mgetUserDataEx failed. input=".serialize($arrInput)." output=".serialize($arrOut));
            return false;
        }
        $arrUserInfos = $arrOut['user_info'];
        if (!empty($this->_bawuUserInfo)) {
            foreach ($this->_bawuUserInfo as $key => $bawuUser) {
                $arrUserInfo = $arrUserInfos[$key];
                $this->_bawuUserInfo[$key]['baijiahao_info'] = array(
                    'name'     => '',
                    'brief'    => '',
                    'avatar'   => Molib_Util_User::dealBJHPortrait($arrUserInfo),
                    'avatar_h' => '',
                    'auth_id'  => intval($arrUserInfo['bjh_v_type']),
                    'auth_desc' => isset($arrUserInfo['bjh_v_intro']) ? $arrUserInfo['bjh_v_intro'] : '',
                );
            }
        }

        return true;
    }
 	
 	private function _mgetUserLevel() {
 		
 		$arrInput['req'] =  $this->_bawuUserInfo;
 		$arrOut = Molib_Tieba_Service::call('perm', 'mgetUserForumLevel', $arrInput);
 		if(false === $arrOut) {
 			
 			Bingo_Log::warning("call perm:mgetUserForumLevel failed. input=".serialize($arrInput)." output=".serialize($arrOut));
 			return false;
 		}
 		
 		if(Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']){
 			
 			Bingo_Log::warning("call perm:mgetUserForumLevel errno not 0. input=".serialize($arrInput)." output=".serialize($arrOut));
 			return false;
 		}
 		
 		$userInfoTmp = array();
 		foreach ($arrOut['score_info'] as $key => $levelInfo) {

 			$userInfoTmp[$levelInfo['user_id']] = array(
 				'level_name' => $levelInfo['level_name'],
 				'user_level' => $levelInfo['level_id'],
 			);
 		
 		}
 		$this->_bawuUserInfo = $userInfoTmp;
 		
 		return true;
 	}
 	
    /**
     * [_getApplyInfo description]
     * @return [type] [description]
     */
    private function _getApplyInfo() {
        $forumId = $this->_objRequest->getPrivateAttr('forum_id');
        $clientType = $this->_objRequest->getCommonAttr('client_type', 0);

        $managerApplyUrl = 'http://tieba.baidu.com/mo/q/managerapply/apply?forum_id='.$forumId.'&client='.$clientType;
        $assistApplyUrl = 'http://tieba.baidu.com/mo/q/managerapply/assistapply?forum_id='.$forumId.'&client='.$clientType;

        $this->_applyInfo = array(
            'manager_left_num' => 0,
            'manager_apply_url' => $managerApplyUrl,
            'assist_left_num' => 0,
            'assist_apply_url' => $assistApplyUrl,
        );

        $bawuInfo = $this->_bawuInfo;
        $assistInfo = $this->_getRoleNum();


        if ($bawuInfo && $bawuInfo['errno'] === Tieba_Errcode::ERR_SUCCESS) {
            $this->_applyInfo['manager_left_num'] = 3 - count($bawuInfo['output']['manager']);

            if ($this->_applyInfo['manager_left_num'] < 0) {
                $this->_applyInfo['manager_left_num'] = 0;
            }
        } else {
            Bingo_Log::warning('call perm:getBawuList failed, ret: '.serialize($bawuInfo));
        }

        if ($assistInfo && $assistInfo['errno'] === Tieba_Errcode::ERR_SUCCESS) {
            if ($assistInfo['output']['assist'] && $assistInfo['output']['assist']['forum_role_num']) {
                $roleNum = $assistInfo['output']['assist']['forum_role_num'];

                if (isset($roleNum['max_num']) && isset($roleNum['cur_num'])) {
                    $this->_applyInfo['assist_left_num'] = intval($roleNum['max_num']) - intval($roleNum['cur_num']);
                }

                if ($this->_applyInfo['assist_left_num'] < 0) {
                    $this->_applyInfo['assist_left_num'] = 0;
                }
            }
        } else {
            Bingo_Log::warning('call perm:getRoleNumInfo failed, ret: '.serialize($assistInfo));
        }

        return true;
    }

    /**
     * [_getRoleNum description]
     * @return [type] [description]
     */
    private function _getRoleNum() {
        $arrInput = array(
            'forum_id' => $this->_objRequest->getPrivateAttr('forum_id'),
            'user_id' => $this->_objRequest->getCommonAttr('user_id'),
            'role_name' => 'assist',
        );

        return Tieba_Service::call('perm', 'getRoleNumInfo', $arrInput);
    }

    /**
     * [_buildResponse description]
     * @return [type] [description]
     */
 	private function _buildResponse() {
 	
 		  $arrResponse = array();
          $arrResponse['bawu_team_info']['bawu_team_list'] = $this->_bawuListInfo;
          $arrResponse['bawu_team_info']['total_num'] = $this->_bawuTotalNum;

          $arrResponse['manager_apply_info'] = $this->_applyInfo;
          $arrResponse['is_private_forum'] = $this->_isPrivateForum;

          $this->_objResponse->setOutData($arrResponse);
 	}
 }
