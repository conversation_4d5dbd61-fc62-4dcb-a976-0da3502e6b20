<?php
/**
 * Created by PhpStorm.
 * User: he<PERSON><PERSON><PERSON>
 * Date: 2018/8/27
 * Time: 上午11:25
 */


class smartapplistAction extends Molib_Client_BaseAction
{

    // request variables
    private $_intForumId     = 0;
    private $_intPn          = 1;
    private $_intRn          = 10;
    private $_hasMore        = 0;
    private $_arrSmartAppList = array();
    private static $_client_type    = 0;
    private static $_client_version = '';
    private static $_cuid ;
    private static $_userip;


    /**
     * @param
     * @return array
     */
    public function _getPrivateInfo()
    {
        $arrPrivate = array(
            'fid'      => $this->_getInput('fid', 0),
            'pn'       => $this->_getInput('pn', 1),
            'rn'       => $this->_getInput('rn', 10),
        );
        return $arrPrivate;
    }

    /**
     * @param
     * @return bool
     */
    public function _checkPrivate()
    {
        return true;
    }

    /**
     * @param
     * @return bool
     */
    public function _execute()
    {
        $this->_intForumId = intval($this->_objRequest->getPrivateAttr('fid', 0));
        $this->_intPn      = intval($this->_objRequest->getPrivateAttr('pn', 0));
        $this->_intRn      = intval($this->_objRequest->getPrivateAttr('rn', 0));
        if ($this->_intForumId <= 0) {
            $this->_error(Tieba_Errcode::ERR_PARAM_ERROR, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_PARAM_ERROR));
            return false;
        }
        $intErrno = $this->_getSmartAppList();
        if ( $intErrno !== Tieba_Errcode::ERR_SUCCESS ){
            $this->_error($intErrno, Molib_Client_Error::getErrMsg($intErrno));
            return false;
        }

        // build data
        Bingo_Timer::start('build_data');
        $this->_buildData();
        Bingo_Timer::end('build_data');

        return true;
    }

    /**
     * @param
     * @return
     */
    private function _buildData()
    {
        $arrOutData = array(
            'smart_app_list'  => $this->_buildSmartAppList(),
            'has_more'        => $this->_hasMore,
        );

        $this->_objResponse->setOutData($arrOutData);
    }

    /**
     *
     * @param
     * @return array
     */
    private function _buildSmartAppList()
    {
        if (empty($this->_arrSmartAppList)) {
            return array();
        }
        //加入小程序状态的实时判断
        self::$_cuid          = $this->_objRequest->getCommonAttr('cuid');
        self::$_userip        = Bingo_Http_Ip::getConnectIpExt();
        $arrData['source']='tieba_smartapp';
        $arrData['comm_params']=array(
            "uuid"=>self::$_cuid,
            "host_app"=>"tieba",
            "host_app_ver"=>array("ver_str"=>self::$_client_version),
            "sdk_ver"=>array("ver_str"=>"2.12.5"),//端上一博给的版本号
            "framework_ver"=>array("ver_str"=>"3.125.1"),//端上一博给的版本号
            "user_ip"=> array("ip_str"=>self::$_userip['ip']),
        );
        foreach ($this->_arrSmartAppList as $item) 
        {
            $swan_item = array(
                "retrieve_key"=>$item['swan_app_key'],
                "retrieve_type"=>3,//手百给的枚举值
                "pkg_type" => 100,//手百给的枚举值
                "app_uid"=>array(
                    "app_key"=>$item['swan_app_key'],
                ),
            );
            $arrData['retrieve_params'][] = $swan_item;
            $swanidlist[] = $item['swan_app_key'];
        }
        Bingo_Log::warning("smartapplist SsproxyMetaService request array==".var_export($arrData,true));
        $strUrl = '/SsproxyMetaService/valid';
        ral_set_pathinfo($strUrl);
        $header = array(
            'Content-Type'=> 'application/json',
            'Log-id'=>time(),
        );
        Bingo_Log::warning("smartapplist SsproxyMetaService request json==".json_encode($arrData));
        $swanret = ral('service_ssproxy', 'post', json_encode($arrData), array(), $header);
        $showbai_swan_ret = json_decode($swanret,true);
        Bingo_Log::warning("smartapplist SsproxyMetaService result array==".var_export($showbai_swan_ret,true));
        $swanidlist = array_flip($swanidlist);
        Bingo_Log::warning("smartapplist swanidlist array==".var_export($swanidlist,true));


        foreach ($this->_arrSmartAppList as $item) {
            if($showbai_swan_ret['valid_resp'][$swanidlist[$item['swan_app_key']]]['err_no']==0)
            {
                $arrList[] = array(
                    'id'       => $item['swan_app_key'],
                    'avatar'   => $item['app_icon'],
                    'name'     => trim(html_entity_decode($item['app_name'], ENT_COMPAT, 'UTF-8')),
                    'abstract' => $item['app_desc'],
                    'pic'      => $item['app_icon'],
                    'h5_url'   => $item['h5_webpage_url'],
                    'link'     => $item['swan_app_index_path'],
                    'is_game'  => $item['is_game'],
                    'swan_app_id' => $item['swan_app_id'],
                );
            }
            else
            {
                Bingo_Log::warning("this swan_app_key is invalid:".var_export($item['swan_app_key'],true));
            }
        }
        return $arrList;
    }

    /**
     * @param
     * @return int
     */
    private function _getSmartAppList()
    {
        $arrInput = array(
            'cond'  => array(
                'forum_id' => $this->_intForumId,
                'status'   => 0,
                'type'     => 2,
            ),
            'pn'    => $this->_intPn,
            'rn'    => $this->_intRn,
        );
        self::$_client_type    = intval($this->_objRequest->getCommonAttr('client_type'));
        self::$_client_version = strval($this->_objRequest->getCommonAttr('client_version'));
        if((Molib_Client_Define::CLIENT_TYPE_IPHONE == self::$_client_type && Molib_Util_Version::compare('9.8.4', self::$_client_version) >= 0)
            || (Molib_Client_Define::CLIENT_TYPE_ANDROID == self::$_client_type && Molib_Util_Version::compare('*******', self::$_client_version) >= 0)) {
            unset($arrInput['cond']['type']);
        }
        $arrOutput = Molib_Tieba_Service::call('smartapp', 'getForumSmartAppConfig', $arrInput, null, null, 'post', 'php', 'utf-8');
        if ( $arrOutput === false ){
            Bingo_Log::warning('http call common failed.  [sevice_name:smartapp] [method:getForumSmartAppConfig] [input:'.serialize($arrInput).']');
            return Tieba_Errcode::SERVICE_CANNOT_BE_USED;
        }
        if ( $arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS ){
            Bingo_Log::warning(sprintf("call smartapp:getForumSmartAppConfig error, input[%s], output[%s]",
                serialize($arrInput),
                serialize($arrOutput)
            ));
            return $arrOutput['errno'];
        }

        $total = 0;
        if (isset($arrOutput['data']['total'])){
            $total = intval($arrOutput['data']['total']);
            unset($arrOutput['data']['total']);
        }else if (isset($arrOutput['total'])){
            $total = $arrOutput['total'];
        }
        $this->_hasMore  = $this->_intPn * $this->_intRn >= $total ? 0 : 1;
        $this->_arrSmartAppList = $arrOutput['data'];
        return Tieba_Errcode::ERR_SUCCESS;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */