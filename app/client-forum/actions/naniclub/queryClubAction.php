<?php
/**
 * <AUTHOR>
 * @date 2018-08-01
 *
 */

class queryClubAction extends Molib_Client_BaseAction {

    private $arrClubData = array();
    private $strNull = '';

    private static $arrDefaultRecommendClubIds = array(
        100187,
        100182,
        100197,
        100191,
        100092,
        100096,
    );

    /**
     * brief 执行入口
     * @param
     * @return [type] [description]
     */
    protected function _execute(){

        $strQueryWord = $this->_objRequest->getPrivateAttr('q', '');
        $this->arrClubData = array();

        if('' == $strQueryWord){
            // 推荐信息
            $this->buildRecommend();
        }else{
            $this->buildSearch();
            if(empty($this->arrClubData)){
                // 无结果返回推荐社团信息
                $this->buildRecommend();
            }
        }
        // output
        $this->buildOutput();
        return true;
    }

    /**
     * _checkPrivate
     * @return bool
     */
    protected function _checkPrivate()
    {
        return true;
    }
    /**
     * _getPrivateInfo
     * @return array
     */

    protected function _getPrivateInfo(){
        return array(
            'check_login' => false,
            'need_login'  => true,
            'q' => $this->_getInput('query_word',''),
        );
    }

    /**
     * buildRecommend
     * @return array
     */
    protected function buildRecommend(){
        foreach(self::$arrDefaultRecommendClubIds as &$val){
            $arrInput= array(
                'club_id' => $val,
            );
            $arrGetClubDataOutput = Tieba_Service::call('nani', 'getClubInfo', $arrInput, null, null, 'post', 'php', 'utf-8');
            if(0 !== $arrGetClubDataOutput['errno']){
                continue;
                Bingo_Log::warning("get club info error.the club id is " . $arrInput['club_id']);
            }

            if(empty($arrGetClubDataOutput['data'])){
                continue;
                Bingo_Log::warning("get club info is empty.the club id is " . $arrInput['club_id']);
            }

            $arrClubData['club_id'] = $arrGetClubDataOutput['data']['club_id'];
            $arrClubData['club_name'] = empty($arrGetClubDataOutput['data']['club_name']) ? $this->strNull :  $arrGetClubDataOutput['data']['club_name'];
            $arrClubData['club_logo'] = empty($arrGetClubDataOutput['data']['club_logo']) ? $this->strNull : $arrGetClubDataOutput['data']['club_logo'];
            $arrClubData['member_num'] = empty($arrGetClubDataOutput['data']['member_num']) ? 0: $arrGetClubDataOutput['data']['member_num'];
            $arrClubData['video_num'] = empty($arrGetClubDataOutput['data']['video_num']) ? 0 : $arrGetClubDataOutput['data']['video_num'];
            $arrClubData['club_intro'] = empty($arrGetClubDataOutput['data']['club_intro']) ? $this->strNull : $arrGetClubDataOutput['data']['club_intro'];
            $arrClubData['type'] = 0;
            $this->arrClubData['club_list'][] = $arrClubData;
        }

        usort($this->arrClubData['club_list'], function($a,$b){return ($a['video_num']==$b['video_num'] ?
            $a['member_num']<$b['member_num'] : $a['video_num']<$b['video_num']);});

        return true;
    }

    /**
     * buildSearch
     * @return bool
     */
    protected function buildSearch(){
        $strQueryWord = $this->_objRequest->getPrivateAttr('q', '');

        $strCuid = $this->_objRequest->getCommonAttr('cuid', '');

        $strService = 'nanisearch';
        $strMethod  = 'query';
        $arrInput   = array(
            'search_type' => 'club',
            'cuid'        => $strCuid,
            'word'        => $strQueryWord,
        );

        $arrOutput = self::ralCall($strService, $strMethod, $arrInput);
        $strLog = sprintf("call $strService :: $strMethod failed input[%s] output[%s]", serialize($arrInput), serialize($arrOutput));
        if(false === $arrOutput){
            Bingo_Log::fatal($strLog);
            return false;
        }else if(Tieba_Errcode::ERR_SUCCESS !== $arrOutput['err_no']){
            Bingo_Log::warning($strLog);
            return false;
        }
        $arrClubIds = $arrOutput['data']['post_list'];

        if(empty($arrClubIds)){
            Bingo_Log::warning("the search result is empty!");
            return false;
        }

        $arrClubIds = array_column($arrClubIds, 'club_id');
        if (empty($arrClubIds)) {
            return true;
        }
        $arrParam = array(
            'club_ids' => $arrClubIds,
        );
        $arrRet = Tieba_Service::call('nani', 'mgetClubInfo', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call nani mgetClubInfo failed. [input = %s][output = %s]", serialize($arrParam), serialize($arrRet)));
            return true;
        }
        $arrClubList = array();
        foreach ($arrRet['data'] as $arrClubData) {
            if ($arrClubData['status'] > 2) {
                continue;
            }
            $arrClubList[$arrClubData['club_id']] = array(
                'club_id'    => $arrClubData['club_id'],
                'club_name'  => $arrClubData['club_name'],
                'club_logo'  => $arrClubData['club_logo'],
                'member_num' => $arrClubData['member_num'],
                'video_num'  => $arrClubData['video_num'],
                'club_intro' => $arrClubData['club_intro'],
                'type'       => 1,
            );
        }

        foreach ($arrClubIds as $intClubId) {
            if (!empty($arrClubList[$intClubId])) {
                $this->arrClubData['club_list'][] = $arrClubList[$intClubId];
            }
        }

        return true;
    }

    /**
     * buildOutput
     * @return bool
     */
    protected function buildOutput(){
        $arrOutData = $this->arrClubData;

        $this->_objResponse->setOutData(array('data' => array()));
        return true;
    }

    /**
     * [ralCall description]
     * @param  [type] $strService [description]
     * @param  [type] $strMethod  [description]
     * @param  [type] $arrInput   [description]
     * @return [type]             [description]
     */
    private static function ralCall($strService,$strMethod,$arrInput){
        $header = array(
            'pathinfo' => '/service/'.$strService.'?method='.$strMethod.'&format=mcpackraw',
            'Host'     => "service.tieba.baidu.com",
        );
        $arrOutput = ral('service_'.$strService, $strMethod, $arrInput, array(), $header);
        return $arrOutput;
    }


}
