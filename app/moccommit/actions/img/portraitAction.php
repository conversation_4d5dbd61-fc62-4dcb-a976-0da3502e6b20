<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR> <EMAIL>
 * @date 2012-12-12
 * @version 
 */
class portraitAction extends CapiAction {
    protected $bolIsPv = 0; //是否是pv    
    protected $bolNeedCheckSign = false;  //是否需要检查加密串
    
    protected function _execute() {
        $ret = $this->_input();
        if ($ret === false){
        	return false;
        }
        $ret = $this->_process();
        if ($ret === false){
        	return false;
        }
        return true;
    }

    protected function _input(){
        if (empty($_FILES['pic'])){
            $intErrno = 4;
        }else{
            $intErrno = intval($_FILES['pic']['error']);            
        }        
        if ($intErrno > 0 || !isset($_FILES['pic']['tmp_name'])){
        	$this->_error(CapiErrno::PORTRAIT_EMPTY, CapiError::PORTRAIT_EMPTY);
            return false;	
        }  

        return true;
    }
    private function _process(){
    	$arrPostArgs = array(
    			'spIsBlogPicAdd' => 2,
    			'ct' => 4,
    			'spAlbumName' => '默认相册',
    			'Filename' =>'image.jpg',
    			'spRefURL' =>'',
    			'BrowserType' => 1,
    			'cm' => 1,
    			'file' => '@'.$_FILES['pic']['tmp_name'],
    			);
    	
    	$rpc = new Client_Http('portrait');
    	$rpc ->setHost(Conf::get('head_image_host',''), Conf::get('head_image_port', ''));
    	$strUrl = '/sys/upload/'.CapiRequest::$strPortrait;
    	$rpc ->call($strUrl, $_COOKIE,'post',$arrPostArgs);
    	
    	return true;
    }
}