<?php


class Base_Dbcache{
protected static $_conf = null;
protected static $_use_split_db = false;
const DATABASE_NAME = "forum_fortune";
const REDIS_NAME = "gconforum";//用的量很少
const CACHE_NAME = "forum_common";
const DB_RAL_SERVICE_NAME = 'DB_forum_fortune_new';
protected static $_cache=null;
protected static $_redis = null;
	
protected static $my_error_code = 
array(
'need_more_point'=>array('errno'=>80001,'errmsg'=>'你的印花不足以兑换本福利哦'),
'have_changed' =>array('errno'=>80002,'errmsg'=>'已达到你今天的兑换上限，请明天再来吧'),
'black_user'=>array('errno'=>80003,'errmsg'=>'由于恶意刷印花，你已被冻结无法兑换'),
'change_time_limit'=>array('errno'=>80004,'errmsg'=>'已达到可兑换上限'),
'no_prize'=>array('errno'=>80005,'errmsg'=>'本福利已被抢光，下次要快哦'),
'exchange_fail'=>array('errno'=>80005,'errmsg'=>'兑换未成功，请重新试试'),
'prize_too_old'=>array('errno'=>80005,'errmsg'=>'福利已过期，无法兑换'),
'add_point_fail'=>array('errno'=>80006,'errmsg'=>'积分操作失败'),
'add_pirze_code_pic_fail'=>array('errno'=>80007,'errmsg'=>'二维码图片生成失败！'),
'forbidden_user'=>array('errno'=>80007,'errmsg'=>'你已被封禁，无法兑换福利'),
'forum_not_open'=>array('errno'=>80008,'errmsg'=>'本吧还未开启福袋'),
'prize_num_can_not_null'=>array('errno'=>80009,'errmsg'=>'福利数不能为空'),
'prize_num_error'=>array('errno'=>80010,'errmsg'=>'福利数量不能小于用户已领数量'),
'prize_can_not_edit'=>array('errno'=>80011,'errmsg'=>'请下架商品后再进行编辑'),
'end_time_error'=>array('errno'=>80012,'errmsg'=>'结束时间不得晚于当前时间'),
'need_more_prize'=>array('errno'=>80013,'errmsg'=>'本福利剩余数量为0，无法上架，请补充数量后再上架。'),
'add_point_limit'=>array('errno'=>80014,'errmsg'=>'本吧加分已经达到每天上限。'),



'forum_name_error'=>array('errno'=>100001,'errmsg'=>'不存在的吧名'),
'user_name_error'=>array('errno'=>100001,'errmsg'=>'不存在的用户名'),
)
;

/*
TEXT_FULI_LIMIT: '已达到可兑换上限',
		TEXT_ID_LIMIT: '已达到你今天的兑换上限，请明天再来吧',
		TEXT_PRINT_LESS: '你的印花不足以兑换本福利哦',
		TEXT_FULI_LESS:'本福利已被抢光，下次要快哦',
		TEXT_BLOCK:'你已被封禁，无法兑换福利',
		TEXT_BLACK:'由于恶意刷印花，你已被冻结无法兑换',
*/


/**
 * @brief get mysql obj.
 * @return: obj of Bd_DB, or null if connect fail.

**/		
protected static function _getDB(){
    /*$objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
    
    if($objTbMysql && $objTbMysql->isConnected()) {
    	
	    $objTbMysql->charset("utf8");
		$objTbMysql->query('set names utf8');
		
        return $objTbMysql;
    } else {
    	Bingo_Log::warning("db connect fail.");
        return null;
    }*/
    $db = new Bd_DB();
    Bingo_Timer::start('dbinit');
    $r = $db->ralConnect(self::DB_RAL_SERVICE_NAME);
    Bingo_Timer::end('dbinit');
    if(!$r){
        $r = $db->ralConnect(self::DB_RAL_SERVICE_NAME);
        if (!$r) {
            Bingo_Log::warning("forum_fortune db connect fail.");
            return null;
        }
    }
    $db->charset("utf8");
    return $db;
}

/**
 * @brief get redis obj.
 * @return: obj of Bingo_Cache_Redis, or null if connect fail.

**/
protected static function _getRedis(){
	if(self::$_redis){
		return self::$_redis ;
	}
	Bingo_Timer::start('redis_init');
	self::$_redis = new Bingo_Cache_Redis(self::REDIS_NAME);
	Bingo_Timer::end('redis_init');

	if(!self::$_redis || !self::$_redis->isEnable()){
		Bingo_Log::warning("init redis fail.");
		self::$_redis = null;
		return null;
	}
	return self::$_redis;
}

	
/**
 * @brief init
 * @return: true if success. false if fail.

**/		
protected static function _init(){
	
	//add init code here. init will be called at every public function beginning.
	//not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
	//init should be recalled for many times.
	
	if(self::$_conf == null){	
		self::$_conf = Bd_Conf::getConf("/app/fortune/service_Fortune_Main");
		if(self::$_conf == false){
			Bingo_Log::warning("init get conf fail.");
			return false;
		}
		
	}
	return true; 
}

protected static function _errRet($errno){
	
	if(isset(self::$my_error_code[$errno]))
	{
		return array(
			'errno' => self::$my_error_code[$errno]['errno'],
			'errmsg' => self::$my_error_code[$errno]['errmsg'],
		);
	}
	else
	{
		return array(
			'errno' => $errno,
			'errmsg' => Tieba_Error::getErrmsg($errno),
		);
	}
}

public static function preCall($arrInput){
	// pre-call hook
}

public static function postCall($arrInput){
	// post-call hook
}

protected static function _getCache(){
	if(self::$_cache){
		return self::$_cache ;
	}
	Bingo_Timer::start('cacheinit');
	
	self::$_cache = new Bingo_Cache_Memcached(self::CACHE_NAME); 
	
	Bingo_Timer::end('cacheinit');	
	
	if(!self::$_cache || !self::$_cache->isEnable()){
		Bingo_Log::warning("init cache fail.");
		self::$_cache = null;
		return null;
	}
	return self::$_cache;
}

public static function testmemcache($arrInput){
	
	$cache = self::_getCache();
	$cache_key = self::CACHE_NAME. __CLASS__. "_testmemcache_";
	
	$add_res = $cache->add($cache_key,"test_".date("Y-m-d H:i:s"),600);
	
	echo "add result:<br>\n";
	var_dump($add_res);
	
	echo "<br>\n";
	
	
	$get_data = $cache->get($cache_key);
	
	var_dump($get_data);
	echo "<br>\n";
	if($get_data)
	{
		
		echo " get result ok , cache is ok . result:<br>\n";
		var_dump($get_data);
		echo "<br>\n";
	}
	
	else
	{
		
		echo " get result fail,you need check worker.log.wf<br>\n";
		
		
	}
	
}




}
