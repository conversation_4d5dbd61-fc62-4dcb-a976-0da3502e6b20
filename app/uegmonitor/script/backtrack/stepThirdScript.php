<?php
/**
 * 回溯第一阶段脚本
 * User: hubin13
 * Date: 2019/09/11
 * Time: 15:23
 */
define ('EASYSCRIPT_DEBUG',false);          //debug 模式
define ('EASYSCRIPT_THROW_EXEPTION',true);  //抛异常模式

define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../../' );
define ('SCRIPTNAME',basename(__FILE__,".php"));   //定义脚本名
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf");
define ('DATAPATH',BASEPATH."/data");
define ('LOGPATH',"./log/uegmonitor/script/backtrack");
define('IS_ORP_RUNTIME', true);
set_include_path(get_include_path() . PATH_SEPARATOR. BASEPATH.'/../../' . PATH_SEPARATOR. BASEPATH);

require_once(dirname(__FILE__) . "/../../lib/Mail_MSGLib/SendMail.php");
require_once(dirname(__FILE__) . "/../../lib/Mail_MSGLib/message.php");

/**
 *
 * @param：$strClassName
 * @return：string
 */
function __autoload($strClassName)
{
    $className = strtolower(str_replace('_', '/', $strClassName));
    require_once BASEPATH."/{$className}.php";
//	require_once BASEPATH.'/'.strtolower(str_replace('_', '/', $strClassName)) . '.php';
}
spl_autoload_register('__autoload');

$logid = time();
define ('LOG_ID',$logid);

/**
 * 日志行打印
 * @param $strMsg
 * @param bool $bolLineInfo
 * @return null
 */
function outLogLine($strMsg, $type='w', $bolEcho = false, $bolLineInfo = true){
	//添加logid
	$strMsg = 'logid:'.LOG_ID.'_'.$strMsg;
	
	$strFileName = '';
	$intLineNo = 0;
	if($bolLineInfo) {
		if (defined('DEBUG_BACKTRACE_IGNORE_ARGS')) {
			$arrTrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
		} else {
			$arrTrace = debug_backtrace();
		}
		if(!empty($arrTrace)) {
			$strFileName = basename($arrTrace[0]['file']);
			$intLineNo = intval($arrTrace[0]['line']);
			//$strMsg = $strFileName . ":Line($intLineNo):" . $strMsg;
		}
	}
	if('n' == $type){
		Bingo_Log::notice($strMsg, '', $strFileName, $intLineNo);
	}else if('w' == $type){
		Bingo_Log::warning($strMsg, '', $strFileName, $intLineNo);
	}else if('f' == $type){
		Bingo_Log::fatal($strMsg, '', $strFileName, $intLineNo);
	}else{
		Bingo_Log::warning($strMsg, '', $strFileName, $intLineNo);
	}
	if($bolEcho){
		echo $strMsg.PHP_EOL;
	}
}

if(null != $argv && !empty($argv) && is_array($argv) && 2== count($argv)){
}else if(null != $argv && !empty($argv) && is_array($argv) && 3== count($argv)){
}else if(null != $argv && !empty($argv) && is_array($argv) && 4== count($argv)){
}else{
}

$strScriptName = 'stepThirdScript';

define ('LOG', 'log');
Bingo_Log::init(array(
LOG => array(
'file'  => LOGPATH ."/". $strScriptName. ".log",
'level' => 0x0f,
),
), LOG);

$easyScriptObj  = false;
try{
	outLogLine('stepThirdScript ready run.', 'n', true);
	$ScriptConf = array(
			'memory_limit'   => '1024M',
			'data_path'      => DATAPATH,
			'conf_path'      => CONFPATH,
			'lock_file_name' => $strScriptName.'.lock',
			'done_file_name' => $strScriptName.'.done',
			'db_alias'       => array(
					'im'     => 'forum_content',
			),
			'conf_alias'     => array(
					'main'     => 'main.conf',
			),
	);

	$easyScriptObj = new Util_EasyScript($ScriptConf);
	//防止脚本重复执行
	if( $easyScriptObj->checkScriptIsRuning() === true ){
		outLogLine('stepThirdScript is runing.', 'n', true);
		exit;
	}
	outLogLine('stepThirdScript begin runing.', 'n', true);

	$getObj = new stepThirdScriptAction();
	$getObj->_execute();
	$easyScriptObj->runSuccess();
	outLogLine('fromPugongYingToMailScript run success.', 'n', true);
}catch(Exception $e){
	if($easyScriptObj !== false){
		outLogLine('stepThirdScript run fail!'.$easyScriptObj->getErr2String(), 'w');
	}else{
		outLogLine('stepThirdScript run fail![null]', 'w');
	}
	outLogLine('stepThirdScript fail.'.$easyScriptObj->getErr2String());
	exit;
}
outLogLine('stepThirdScript finish.', 'n', true);

class stepThirdScriptAction{
	protected static $_redis = array();
	
	public static $items = array();
	
	public static $indexTime = '';
	
	const SCRIPT_STEP_THIRD = "script_step_third";
	
	/**
	 *
	 * @param：
	 * @return：bol
	 */
	public static function  _execute(){
		$wordlistConfig = Util_ServiceHandle::UEG_TRACKBACK_SCRIPT_CONFIG;
		$arrMails = Util_ServiceHandle::getConf($wordlistConfig,'mails');
		$mailAddress = implode(",", $arrMails);
		$arrPhones = Util_ServiceHandle::getConf($wordlistConfig,'phones');
		outLogLine('mails:'.$mailAddress, 'n', true);
		outLogLine('phones:'.implode(",", $arrPhones), 'n', true);
		
		$msg = new Lib_SendSmsp();
		$mailtitle="ueg推审报警".date("Y-m-d H:i:s",$now);
		
		//获得当前时间
		$now = time();
		$today = date('Ymd',time());
		outLogLine('stepThirdScript time is:  '.date("Y-m-d H:i:s",$now).' totay is:'.$today, 'n',true);
		
		
		//已经回溯到的时间
		$intLastTime = Util_ServiceHandle::getTrackLastTime(Util_ServiceHandle::SCRIPT_STEP_THIRD);
		if($intLastTime == 0){ //重试一次
			$intLastTime = Util_ServiceHandle::getTrackLastTime(Util_ServiceHandle::SCRIPT_STEP_THIRD);
		}
		
		//脚本已经运行过
		if($intLastTime > 0){ 
			$startTime = $intLastTime;
			$endTime = $now - 60*8;
			
			//避免一次回溯太长时间的数据,最多只回溯5分钟的数据
			if($endTime-$startTime>300){
				$endTime = $startTime+300;
			}
			
			//如果回溯周期超过一个小时
			if($now-$startTime>3600){
				$timeRange = Util_TimeHandle::getOneMininueTimeRange($now,8);
				$startTime = $timeRange['start'];
				$endTime = $timeRange['end'];
				outLogLine('over 3600', 'w',true);
			}
				
			
			if($endTime<$startTime){//正常来说不可能发生
				$temp = $startTime;
				$startTime= $endTime;
				$endTime = $temp;
				outLogLine('time exchange', 'w',true);
			}
			outLogLine('a starttime is:  '.date("Y-m-d H:i:s",$startTime)." endtime is:".date("Y-m-d H:i:s",$endTime), 'n',true);
		}else{
			$timeRange = Util_TimeHandle::getOneMininueTimeRange($now,8);
			$startTime = $timeRange['start'];
			$endTime = $timeRange['end'];
			outLogLine('b starttime is:  '.date("Y-m-d H:i:s",$startTime)." endtime is:".date("Y-m-d H:i:s",$endTime), 'n',true);
		}
		
		//需要处理零界点问题，当天的查询方式和前一天的查询方式不同
		//获得当前日期零点
        //如果是零界点，本周期只回溯昨天的数据，今天的数据下个周期回溯
        $zeroTime = strtotime(date("Y-m-d"),time());
        $bolCriticalPoint = 0;
        //if($startTime<$zeroTime && $zeroTime<=$endTime){
        if($now-$zeroTime<60){
        	$bolCriticalPoint = 1;
        	$endTime = $zeroTime-1;
        	$strYesterday = date('Ymd',time()-24*60*60);
        	$today = $strYesterday;
        	outLogLine('bolCriticalPoint:'.date("Y-m-d H:i:s",$endTime)." today change:".$strYesterday, 'n',true);
        }
		
        //测试数据
        /* $startTime = strtotime('2019-09-12 00:02:03');
        $endTime = strtotime('2019-09-12 00:02:30');
        $today='20190912';   */
		
        //查询索引时间
        self::$indexTime = $today;
		//获取监控状态为0的数据
		$intFrom = 0;
		$intSize = 100;
		$total = 0;
		$output = Util_EsSql::queryByMonitorType($today, $startTime, $endTime, Util_EsSql::MONITOR_TYPE_3, $intFrom, $intSize);
		$list = array();
		$retconut=0;
		if(!empty($output)){
			$total = $output['hits']['total'];
			$list = array_merge($list,$output['hits']['hits']);
			$count = count($output['hits']['hits']);
			$retconut = count($list);
			outLogLine('monitor total:'.$total, 'n',true);
			outLogLine('monitor count:'.$count.' retconut:'.$retconut, 'n',true);
			$intFrom += $count;
		}
		//继续分页查询
		while($retconut<$total){
			$output = Util_EsSql::queryByMonitorType($today, $startTime, $endTime, Util_EsSql::MONITOR_TYPE_3, $intFrom, $intSize);
			if(!empty($output)){
				$total = $output['hits']['total'];
				if(empty($output['hits']['hits'])){
					break;
				}
				$list = array_merge($list,$output['hits']['hits']);
				$count = count($output['hits']['hits']);
				$retconut = count($list);
				$intFrom += $count;
				outLogLine('monitor count:'.$count.' retconut:'.$retconut, 'n',true);
			}else{
				//结束
				break;
			}
		}
		
		//进行回溯
		self::_doPushAudit($list);
		//更新回溯时间
		outLogLine('update lasttime:'.date("Y-m-d H:i:s",$endTime), 'n',true);
		Util_ServiceHandle::setTrackLastTime($endTime,Util_ServiceHandle::SCRIPT_STEP_THIRD);
		outLogLine('heatbeat:'.date("Y-m-d H:i:s",time()), 'n',true);
		Util_ServiceHandle::setScriptHeartbeat(Util_ServiceHandle::SCRIPT_STEP_THIRD);
		
		//如果每分钟退审数大于20报警
		if(count($list)>20){
			outLogLine('alarm m 20', 'w',true);
			$maiContent="ueg推审报警每分钟遗漏：".count($list);
			$mailRet = self::sendToMail($mailAddress, $maiContent,'',$mailtitle);
			outLogLine('mail msg ret:'.serialize($mailRet), 'n', true);
			//发送短信
			$msg ->setTo($arrPhones);
			$alarm = "ueg推审报警,每分钟遗漏:".count($list);
			$msg ->setMes(Bingo_Encode::convert($alarm, 'gbk', 'utf-8'));
			$res = $msg ->send();
			outLogLine('text msg ret:'.$res, 'n', true);
		}
	}
	
	/**
	 *
	 * @param：
	 * @return：bol
	 */
	public static function  _doPushAudit($list){
		
		$itemList = array();
		foreach ($list as $data){
			$item = self::_createItem($data);
			$itemList[] = $item;
		}
		
		//并发控制每秒钟qps,50
		$count = 1;
		foreach ($itemList as $arrInput){
				if($count%50 ==0){
					sleep(1);
				}
				$count++;
				outLogLine('uegNewAuditPost:'.$arrInput['datas']['post_id'], 'n',true);
				
				//设置主态
				self::unihandle($arrInput);
				//推审
				//$arrOutput = Tieba_Commit::commit('anti', 'uegNewAuditPost', $arrInput);
				$arrOutput = Util_Commit::commit("anti", 'uegNewAuditPost', $arrInput);
				if ($arrOutput === false || !isset ($arrOutput ['err_no']) || $arrOutput ['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
					outLogLine('anti::uegNewAuditPost, input [' . serialize($arrInput) . '] output=' . serialize($arrOutput), 'w',true);
				} else {
					outLogLine('anti::uegNewAuditPost, output=' . serialize($arrOutput), 'n',true);
				}
		}
		
	}
	
	/**
	 * threadId、postId、userId 、commandNo 推审必要字段
	 * @param：
	 * @return：bol
	 */
	private static function  _createItem($monitor){
			$post_id = intval($monitor['_source']['post_id']);
			$thread_id = intval($monitor['_source']['thread_id']);
			$user_id = intval($monitor['_source']['user_id']);
			$user_name = $monitor['_source']['user_name'];
			$monitor_type = $monitor['_source']['monitor_type'];
			$forum_id = $monitor['_source']['forum_id'];
			$forum_name = $monitor['_source']['forum_name'];
			$command = $monitor['_source']['command'];
			//extra
			$extra = $monitor['_source']['extra'];
			$req = base64_decode($extra);
			$req = mc_pack_pack2array($req);
			
			$title = $req['utf8_title'];
			$content = $req['utf8_rawdata'];
			
			$create_time = date('Y-m-d H:i:s', time());
			$arrInput = array(
					"strategy_num" => $monitor_type, //使用策略号做项目号
					"project_id"   => 1781,
					"datas"        => array(
							array(
									"thread_id"    => $thread_id,
									"post_id"      => $post_id,
									"title"        => $title,
									"content"      => $content,
									"user_id"      => $user_id,
									//"user_name"    => Bingo_Encode::convert($user_name, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK),
									"user_name"    => $user_name,
									"forum_id"     => $forum_id,
									//"forum_name"   => Bingo_Encode::convert($forum_name, Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK),
									"forum_name"   => $forum_name,
									//"user_icon"    => '',
									//"tail_content" => '',
									//"userdetail"   => '',
									//"userNickName" => '',
									'command_no'   => $command==Dl_Constant::COMMAND_POSTCOMMIT?Tieba_Cmd::PostCommit:Tieba_Cmd::ThreadCommit,
									'create_time'  => $create_time,
							)
					)
			);
			
		
		return $arrInput;
	}
	
	/**
	 * @param：
	 * @return：bol
	 */
	private static function unihandle($arrInput){
		$handleCmdInput = array(
				"req" => array(
						"command_no" => 56000,
						"op_uname" => '突发事件清理20',
						"op_uid" => 327529756,
						"call_from" => 'mis_uegaudit',
						'thread_id' => $arrInput['datas'][0]["thread_id"],
						'post_id' => $arrInput['datas'][0]["post_id"],
						"forum_id" => $arrInput['datas'][0]["forum_id"],
						'op_ip' => 0 ,
				)
		);
		$arrOutput = Tieba_Service::call('unihandle', 'handleCmd', $handleCmdInput);
		if ($arrOutput === false || $arrOutput ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			outLogLine('unihandle, input [' . serialize($handleCmdInput) . '] output=' . serialize($arrOutput), 'w',true);
		} else {
			outLogLine('unihandle, output=' . serialize($arrOutput), 'n',true);
		}
	}
	
	/**
	 *
	 * @param unknown $targetRecvMail：支持多个邮箱，以逗号隔开
	 * @param unknown $mailContent
	 * @param string $title
	 * @param string $sender
	 * @return array
	 */
	public static function sendToMail($targetRecvMail = '<EMAIL>', $mailContent,$filePath, $title = '回溯脚本报警', $sender = '<EMAIL>'){
		if(empty($targetRecvMail) || empty($mailContent)){
			Bingo_Log::warning('_sendAlarmsToMail input param invalid,targetRecvMail:['.serialize($targetRecvMail).'] mailContent:['.serialize($mailContent).']');
			return false;
		}
		$mail = new SendMail();
		$mail->setTo($targetRecvMail);
		$mail->setFrom($sender);
		$mail->setCharset("utf-8");
		$mail->setSubject($title);
		$mail->setAttachments($filePath);
		$mail->setText('text');
		$mail->setHTML($mailContent);
		$result = $mail->send();
		if ($result == false) {
			Bingo_Log::warning( "send mail fail.mailcontent:[".$mailContent.']');
			return false;
		}
		return true;
	}
	
}