<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014:11:11 23:26:50
 * @version 
 * @structs & methods(copied from idl.)
 */
define("MODULE","Forum_friend_dl");
class Dl_Common_Common{
	protected $strDataBase = null;
	protected $strTable = 'sep_recommend';
	protected $intSplitTableNum = 0;
	protected $intSplitType = 1;
	protected $arrSplitTypes = array(1, 2);			//1ȡ�� 2crt32ȡ��
	
	public function __construct($arrConfig=array()){
		if(!empty($arrConfig)){
			$this->setOptions($arrConfig);
		}
	}

	private function setOptions($arrConfig){
		if(isset($arrConfig['data_base'])){
			$this->strDataBase = strval($arrConfig['data_base']);
		}
		if(isset($arrConfig['table'])){
			$this->strTable = strval($arrConfig['table']);
		}
		
		if(isset($arrConfig['split_table_num'])){
			$this->intSplitTableNum = intval($arrConfig['split_table_num']);
		}
		
		if(isset($arrConfig['split_type'])){
			$this->intSplitType = intval($arrConfig['split_type']);
		}

		return true;
	}

	private function _getDB(){
		if($this->strDataBase == null){
			Bingo_log::warning("database is null");
			return null;
		}
		$objDb = Tieba_Mysql::getDB($this->strDataBase);
        if($objDb && $objDb->isConnected()) {
        	$objDb->charset('utf8');
            return $objDb;
        }else{
            Bingo_Log::warning("db connect fail.");
            return null;
        }
    }

	private function _queryDB($strSql) {
		$objDb = self::_getDB();
		if($objDb == null){ 
			Bingo_Log::warning("query db failed");
			return false;
		}
  
		Bingo_Timer::start("dbquery");
		$ret = $objDb->query($strSql);
		if($ret == false){
			 Bingo_Log::warning(sprintf("query sql error.[sql:%s][errno:%s][errmsg:%s]", $strSql, $objDb->getErrno(), $objDb->getError()));
		}
		Bingo_Timer::end("dbquery");

		return $ret;
	}

	public function getData($arrInput){
		if(!isset($arrInput['key'])){
			Bingo_log::warning("input param error.");
			return false;
		}
		if(!in_array($this->intSplitType, $this->arrSplitTypes)){
			Bingo_log::warning("input param error.");
			return false;
		}
		$strKey = strval($arrInput['key']);
		if($this->intSplitType == 2){
			$strKey = crc32($strKey);
		}
		$strTableName = $this->strTable;
		if($this->intSplitTableNum > 0){
			$strTableName = sprintf("%s_%d", $this->strTable, $strKey%$this->intSplitTableNum);
		}
		$strSql = sprintf("select skey, svalue, score from %s where skey='%d' order by score", $strTableName, $strKey);
		if(isset($arrInput['limit'])){
			$strSql .= sprintf('limit %d', $arrInput['limit']);
		}
		$res = self::_queryDB($strSql);
		return $res;
	}

}
