<?php
/**
 * STS.php STS助手
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 17/11/8 下午7:53
*/

/**
 * Class Util_STS
 */
class Util_STS
{
    const ENGINE_MYSQL = 'mysql';
    const ENGINE_REDIS = 'redis';

    private $mixServiceMethod = '';
    private $strEngine = '';

    private static $bolStartMulti = false;
    private static $arrMulti = array();

    private static $bolStartTransaction = false;
    private static $arrTransaction = array();
    private static $arrInstance = array();

    /**
     * Util_STS constructor.
     * @param mixed $mixServiceMethod
     * @param string $strEngine
     */
    private function __construct($mixServiceMethod, $strEngine){
        $this->mixServiceMethod = $mixServiceMethod.'STS';
        $this->strEngine        = $strEngine;
    }

    /**
     * __call
     * @param string $name
     * @param array $arguments
     * @return array|bool
     */
    public function __call($name, $arguments) {
        $strFunc = $name;

        $mixServiceMethod = $this->mixServiceMethod;

        $strEngine = $this->strEngine;

        if (self::ENGINE_MYSQL == $strEngine) {
            $arrSTSData     = $arguments[0];
            $strSTSExtra    = $arguments[1];

            return $this->_callMysql($mixServiceMethod, $strFunc, $arrSTSData, $strSTSExtra);
        }

        if (self::ENGINE_REDIS == $strEngine) {
            $arrSTSRedisInput = $arguments[0];
            return $this->_callRedis($mixServiceMethod, $strFunc, $arrSTSRedisInput);
        }

        return false;
    }

    /**
     * 调用STS的Mysql服务
     * @param mixed $mixServiceMethod
     * @return Util_STS
     */
    public static function call($mixServiceMethod) {
        if (!empty(self::$arrInstance[$mixServiceMethod][self::ENGINE_MYSQL])) {
            return self::$arrInstance[$mixServiceMethod][self::ENGINE_MYSQL];
        }

        self::$arrInstance[$mixServiceMethod][self::ENGINE_MYSQL] = new self($mixServiceMethod, self::ENGINE_MYSQL);
        return self::$arrInstance[$mixServiceMethod][self::ENGINE_MYSQL];
    }

    /**
     * startTransaction 开启事务模式
     * 开启事务模式后，在commit之前的所有调用STS服务都会进入事务，不会进行调用，只有调用commit时才调用STS服务进行事务处理
     */
    public static function startTransaction() {
        self::$bolStartTransaction = true;
    }

    /**
     * commit 提交事务
     * @return bool
     */
    public static function commit() {
        if (!self::$bolStartTransaction) {
            return false;
        }

        $arrInput = array();

        $arrInput['sts_data']['transaction'] = self::$arrTransaction;

        // 第一个事务的service method作为默认的method
        $mixServiceMethod = self::$arrTransaction[0][0];

        // 重置事务
        self::$arrTransaction = array();

        self::$bolStartTransaction = false;

        return self::_callTiebaService($mixServiceMethod, $arrInput);
    }

    /**
     * startMulti 开启Multi调用service
     * 开启Multi模式后，在commitMulti之前的所有调用STS服务都会只进行register，不会进行调用，只有调用commitMulti时才会批量异步调用STS服务
     */
    public static function startMulti() {
        self::$bolStartMulti = true;
    }

    /**
     * commitMulti 提交Multi调用Service
     * @return bool|array
     */
    public static function commitMulti() {
        if (!self::$bolStartMulti) {
            return false;
        }

        $arrMulti = self::$arrMulti;

        $strMultiKey = 'commitMulti_'.md5(serialize($arrMulti));

        $objMulti = new Tieba_Multi($strMultiKey);

        foreach ($arrMulti as $k => $v) {
            $mixServiceMethod = $v[0];
            $arrInput = $v[1];

            if (is_array($mixServiceMethod)) {
                // array('strServiceName', 'strServiceMethod');
                $strServiceName   = array_shift($mixServiceMethod);
                $strServiceMethod = array_shift($mixServiceMethod);
            } else {
                // 默认service名为ala
                $strServiceName   = 'ala';
                $strServiceMethod = $mixServiceMethod;
            }

            $arrServiceInput = array(
                'serviceName' => $strServiceName,
                'method'      => $strServiceMethod,
                'ie'          => 'utf-8',
                'input'       => $arrInput,
            );

            $strRegisterKey = "$strServiceName::$strServiceMethod::$k";

            $objMulti->register($strRegisterKey, new Tieba_Service($strServiceName), $arrServiceInput);
        }

        Bingo_Timer::start($strMultiKey);
        $arrMultiOutput = $objMulti->call();
        Bingo_Timer::end($strMultiKey);

        foreach ($arrMultiOutput as $key => $arrServiceOutput) {
            if ($arrServiceOutput == false || $arrServiceOutput['errno'] != Alalib_Conf_Error::ERR_SUCCESS) {
                $strLog = __CLASS__.'::'.__FUNCTION__." call $key fail. output:[".serialize($arrServiceOutput).']';
                Bingo_Log::warning($strLog);
                return false;
            }
        }

        $arrRetData = array();

        foreach ($arrMulti as $k => $v) {
            $mixServiceMethod = $v[0];

            if (is_array($mixServiceMethod)) {
                // array('strServiceName', 'strServiceMethod');
                $strServiceName   = array_shift($mixServiceMethod);
                $strServiceMethod = array_shift($mixServiceMethod);
            } else {
                // 默认service名为ala
                $strServiceName   = 'ala';
                $strServiceMethod = $mixServiceMethod;
            }

            $strRegisterKey = "$strServiceName::$strServiceMethod::$k";

            $arrRetData[] = $arrMultiOutput[$strRegisterKey]['data'];
        }

        return $arrRetData;
    }

    /**
     * 调用STS的Redis服务
     * @param mixed $mixServiceMethod
     * @return Util_STS
     */
    public static function callRedis($mixServiceMethod) {
        if (!empty(self::$arrInstance[$mixServiceMethod][self::ENGINE_REDIS])) {
            return self::$arrInstance[$mixServiceMethod][self::ENGINE_REDIS];
        }

        self::$arrInstance[$mixServiceMethod][self::ENGINE_REDIS] = new self($mixServiceMethod, self::ENGINE_REDIS);
        return self::$arrInstance[$mixServiceMethod][self::ENGINE_REDIS];
    }

    /**
     * 调用STS的Mysql服务
     * @param mixed $mixServiceMethod string|array('strServiceName', 'strServiceMethod');
     * @param string $strFunc
     * @param array $arrSTSData
     * @param string $strSTSExtra
     * @return array|bool
     */
    private function _callMysql($mixServiceMethod, $strFunc, $arrSTSData, $strSTSExtra = '') {
        $arrInput = array(
            'sts_data' => array(
                'engine'   => self::ENGINE_MYSQL,
                'function' => $strFunc,
                'input'    => $arrSTSData,
                'extra'    => $strSTSExtra,
            ),
        );

        // 开启事务模式
        if (self::$bolStartTransaction) {
            self::$arrTransaction[] = array($mixServiceMethod, $arrInput);
            return true;
        }

        // 开启Multi模式
        if (self::$bolStartMulti) {
            self::$arrMulti[] = array($mixServiceMethod, $arrInput);
            return true;
        }

        return self::_callTiebaService($mixServiceMethod, $arrInput);
    }

    /**
     * 调用STS的Redis服务
     * @param mixed $mixServiceMethod
     * @param string $strRedisFunc 对应Util_Redis类的方法名
     * @param array $arrSTSRedisInput
     * @return array|bool
     */
    private function _callRedis($mixServiceMethod, $strRedisFunc, $arrSTSRedisInput = array()) {
        if (!$strRedisFunc || !$arrSTSRedisInput) {
            return false;
        }

        $arrInput = array(
            'sts_data' => array(
                'engine'   => self::ENGINE_REDIS,
                'function' => $strRedisFunc,
                'input'    => $arrSTSRedisInput,
            ),
        );

        // 开启Multi模式
        if (self::$bolStartMulti) {
            self::$arrMulti[] = array($mixServiceMethod, $arrInput);
            return true;
        }

        return self::_callTiebaService($mixServiceMethod, $arrInput);
    }

    /**
     * 调用Tieba Service服务
     * @param mixed $mixServiceMethod string|array('strServiceName', 'strServiceMethod');
     * @param array $arrParamInput
     * @param string $strOutputKey
     * @return array|bool
     */
    private static function _callTiebaService($mixServiceMethod, $arrParamInput = array(), $strOutputKey = 'data') {
        if (is_array($mixServiceMethod)) {
            // array('strServiceName', 'strServiceMethod');
            $strServiceName   = array_shift($mixServiceMethod);
            $strServiceMethod = array_shift($mixServiceMethod);
        } else {
            // 默认service名为ala
            $strServiceName   = 'ala';
            $strServiceMethod = $mixServiceMethod;
        }

        $arrServiceInput = $arrParamInput;

        $arrOutput = Tieba_Service::call($strServiceName, $strServiceMethod, $arrServiceInput, null, null, "post", "php", "utf-8");

        if (false === $arrOutput || Tieba_Errcode::ERR_SUCCESS != $arrOutput["errno"]) {
            $strLog = __CLASS__."::".__FUNCTION__." call $strServiceName $strServiceMethod fail. input:[".serialize($arrServiceInput)."]; output:[".serialize($arrOutput)."]";
            Bingo_Log::warning($strLog);

            return false;
        }

        return $arrOutput[$strOutputKey];
    }
}