<?php
/**
 * @name Umpf_Action
 * @desc Action基类，所有Action类均需继承这个类
 * 集中处理一些pre操作和suf操作，比如用户验证、参数整理、renderTpl等
 * <AUTHOR>
 */
define('IS_ODP', true);
define('LOG_PATH', ROOT_PATH.'/log');
define('MODULE_NAME', 'uuapmisauth');

Bingo_Log::init ( array (
        'log' => array (
                    'file' => LOG_PATH . '/app/' . MODULE_NAME . '/ui.log',
                            'level' => 0x04 | 0x02
                                )
                            ), 'log' );
abstract class Umpf_Action extends Ap_Action_Abstract {
	
	protected $_user;
	protected $_params;
	protected $_pageParams;
	protected $_tpl;
	
	/**
	 * 检查用户登录信息
	 */
	private function _checkUserInfo() {

		//获取当前登录用户

		/*
		$username = Bd_PhpCas::isAuthenticated();
		if($username === false){
			$username = Bd_PhpCas::login ();
		}*/


		require_once(ROOT_PATH.'/libs/lib/mislib/cas/CAS.php');
		require_once(ROOT_PATH.'/libs/lib/mislib/cas/Cassession.php');

		$casconf = Bd_Conf::getConf("/app/uegmis/ui_uegmis");


		phpCAS::client ( CAS_VERSION_2_0, $casconf ['CASUrl'], intval ( $casconf ['CASPort'] ), $casconf ['CASPath'], true, 80 );
		phpCAS::setNoCasServerValidation ();
		phpCAS::forceAuthentication ();
		$username = phpCAS::getUser ();

		//$username="zhanghongcun";

		//验证 uuap misauth 权限
		$checkResult = Umpf_Auth::checkUserAuth ($username);
		$allow = $checkResult ['allow'];
		$resultno = $checkResult ['resultno'];


		//test
		//$this->_user ['uid'] = 123;
		//$this->_user ['uname'] = $username;
		//return true;

		if ($resultno != Umpf_Auth::USER_VALID) {
			$this->_go2ErrorPage ( Umpf_Auth::getUserStatusMsg ( $resultno ), $resultno, 'LoginAction' );
			return false;
		} else {
			if ($allow != Umpf_Auth::ACCESS_ALLOW) {
				$this->_go2ErrorPage ( Umpf_Auth::getAuthStatusMsg ( $allow ), $allow, 'LoginAction' );
				return false;
			}
		}
		$this->_user ['uid'] = $checkResult ['userid'];
		$this->_user ['uname'] = $username;
		$this->_user ['pid'] = $checkResult ['passid'];
		$this->_user ['pname'] = $checkResult ['passname'];
		return true;
	}
	
	/**
	 * 初始化方法
	 */
	protected function _init() {
		return true;
	}
	
	/**
	 * 初始化http参数
	 */
	protected function _initParam() {
		//$arrRequest = Saf_SmartMain::getCgi ();
		//$getInputs = $arrRequest ['get'];
		//$postInputs = $arrRequest ['post'];
		Bingo_Http_Request::init();
		$getInputs = Bingo_Http_Request::getGetAllNoXssSafe();
		$postInputs = Bingo_Http_Request::getPostAllNoXssSafe();
		$this->_params = array_merge ( $getInputs, $postInputs );
	}
	
	/**
	 * 获取http参数，如果没有该参数，将会得到false
	 *
	 * @param $key unknown_type
	 *       	 参数名
	 * @param $default unknown_type
	 *       	 如果没有这个参数，那会返回这个默认值
	 * @return unknown_type http中的参数值
	 */
	protected function _getParam($key, $default = false) {
		if (isset ( $this->_params [$key] )) {
			return $this->_params [$key];
		} else {
			return $default;
		}
	}
	
	/**
	 * 设置tpl中的一些常用变量
	 */
	protected function _setDefaultPath() {
		$this->_pageParams ['staticPath'] = STATIC_RELATIVE_PATH;
		$this->_pageParams ['webrootPath'] = WEBROOT_RELATIVE_PATH;
		
		$apConfig = Bd_Conf::getAppConf ( 'ap' );
		$this->_pageParams ['pageTitle'] = $apConfig ['name'];
	}
	
	protected function _setTpl($tpl) {
		$this->_tpl = $tpl;
	}
	
	/**
	 * 注册smarty参数
	 *
	 * @param $key unknown_type       	
	 * @param $value unknown_type       	
	 */
	protected function _assignParam($key, $value) {
		if ($key == 'staticPath' || $key == 'webrootPath' || $key == 'userinfo') {
			Bd_Log::warning ( 'The assign param could not be \'staticPath\' or \'webrootPath\'.' );
			return;
		}
		$this->_pageParams [$key] = $value;
	}
	
	/**
	 * render一个smarty的tpl
	 */
	protected function _renderTpl() {
		header("Content-Type:text/html;charset=utf-8");

		if (isset ( $this->_tpl ) && ! empty ( $this->_tpl )) {
			$tplFactory = Bd_TplFactory::getInstance ();
			if (! empty ( $this->_user )) {
				$this->_pageParams ['userinfo'] = $this->_user;
			}
			$tplFactory->assign ( $this->_pageParams );
			$tplFactory->display ( $this->_tpl );
		}
	}
	
	/**
	 * 前往默认的error页面
	 *
	 * @param $errmsg unknown_type       	
	 * @param $errno unknown_type       	
	 * @param $action unknown_type       	
	 */
	protected function _go2ErrorPage($errmsg, $errno, $action="") {
		if(empty($action)){
			$request = $this->getRequest();
			$action = $request->getModuleName()."/".$request->getControllerName()."/".$request->getActionName();
		}
		$time = date ( 'Y-m-d H:i:s' );
		$this->_assignParam ( 'error', "系统在执行 $action 时出现了一个错误：[错误号：$errno,错误信息：$errmsg,出错时间：$time]" );
		$this->_setTpl ( MODULE_NAME . '/error.tpl' );
	}
	
	/**
	 * 如果是Ajax的请求action，返回错误json
	 *
	 * @param $errmsg unknown_type       	
	 * @param $errno unknown_type       	
	 */
	protected function _outputErrorJson($errmsg, $errno) {
		$time = date ( 'Y-m-d H:i:s' );
		$errorInfo = array (
				'result' => $errno, 
				'errmsg' => "Ajax请求中出现了一个错误：错误号：$errno,错误信息：$errmsg,时间：$time" );
		echo json_encode ( $errorInfo );
		exit();
	}

	protected function _error($errmsg, $errno = -1){
		if($this->getRequest()->isXmlHttpRequest()){
			//$this->_outputErrorJson($errmsg, $errno);
			echo json_encode(array(
				'errno' => $errno,
				'errmsg' => $errmsg
			));
		}else{
			$request = $this->getRequest();
			$action = $request->getModuleName()."/".$request->getControllerName()."/".$request->getActionName();

			$time = date ( 'Y-m-d H:i:s' );
			$this->_assignParam ( 'error', "系统在执行 $action 时出现了一个错误：[错误号：$errno,错误信息：$errmsg,出错时间：$time]" );
			$this->_setTpl ( MODULE_NAME . '/error.tpl' );
			$this->_renderTpl ();
			//$this->_go2ErrorPage($errmsg, $errno);
		}
		exit();
	}
	
	/**
	 * 子类需要实现的action业务逻辑
	 */
	abstract protected function action();
	
	public function execute() {

		$needlogin = Bd_Conf::getAppConf ( 'needlogin' );
		$flag = true;

		if ($needlogin) {
			$flag = $this->_checkUserInfo ();
		}


		$this->_setDefaultPath ();

		if ($flag === true) {
			$this->_initParam ();
			$this->_init ();
			$this->action ();
		}
		$this->_renderTpl ();
	}
}

?>
