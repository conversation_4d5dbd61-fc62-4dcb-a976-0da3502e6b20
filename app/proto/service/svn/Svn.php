<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-09-10 16:31:00
 * @comment svn ci
 * @version 1.0
 */

define('SVN_LIB', dirname(__FILE__).'/../../../wholedevplatform/tools/svn1_6/lib');
define('SVN', dirname(__FILE__).'/../../../wholedevplatform/tools/svn1_6/bin/svn');
define('PROTO_DIR', '/tmp/protobuf');

class Service_Svn_Svn {

    public $service_ie = 'utf-8';
    const SUFFIX_LEN = 9;
    const USER_NAME = 'proto_svn';
    const PASSWORD  = 'svnproto2015';
    const PROTO_TYPE_TBCLIENT = 'tbclient';
    const PROTO_TYPE_TBIM = 'tbim';
    //const PROTOBUF_SVN_TBCLIENT = 'https://svn.baidu.com/app/search/forum/trunk/msgsys/protobufidl/tbclient';
    //const PROTOBUF_SVN_TBIM = 'https://svn.baidu.com/app/search/forum/trunk/msgsys/protobufidl/tbim';
    const PROTOBUF_SVN_TBCLIENT = 'https://svn.baidu.com/app/search/forum/branches/msgsys/service/msgstore/fapp_9-0-1085_BRANCH';
    const PROTOBUF_SVN_TBIM = 'https://svn.baidu.com/app/search/forum/branches/msgsys/service/msgstore/fapp_9-0-1085_BRANCH';

    /**
     * @brief: 提交proto到svn库
     * @param:
     * @return:
     */
    public static function commitFiles($arrInput) {
        if (!isset($arrInput['files']) || empty($arrInput['files'])) {
            Util_Log::warning("input params invalid. req: [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $userName = $arrInput['username'];

        if (!is_dir(PROTO_DIR)) {
            mkdir(PROTO_DIR);
        }

        if (!file_exists(PROTO_DIR . '/tbclient/tbclient.proto') ||
            !file_exists(PROTO_DIR . '/tbim/tbim.proto')) {
            exec('rm ' . PROTO_DIR . '/* -rf 2>&1', $output);

            $cmd = sprintf('cd %s/ && export LD_LIBRARY_PATH=%s; %s co %s %s/tbclient --no-auth-cache --non-interactive --trust-server-cert --username %s --password %s 2>&1', PROTO_DIR, SVN_LIB, SVN, self::PROTOBUF_SVN_TBCLIENT, PROTO_DIR, self::USER_NAME, self::PASSWORD);
            exec($cmd, $output);
            $output = implode('<br>', $output);

            if (false === strpos($output, 'Checked out revision') && !empty($output)) {
                // TODO LOG
                return self::_errRet(Tieba_Errcode::ERR_UNKNOWN);
            }

            $cmd = sprintf('cd %s/ && export LD_LIBRARY_PATH=%s; %s co %s %s/tbclient --no-auth-cache --non-interactive --trust-server-cert --username %s --password %s 2>&1', PROTO_DIR, SVN_LIB, SVN, self::PROTOBUF_SVN_TBIM, PROTO_DIR, self::USER_NAME, self::PASSWORD);
            exec($cmd, $output);
            $output = implode('<br>', $output);

            if (false === strpos($output, 'Checked out revision') && !empty($output)) {
                // TODO LOG
                return self::_errRet(Tieba_Errcode::ERR_UNKNOWN);
            }
        } else {
            $cmd = sprintf('cd %s/tbclient && export LD_LIBRARY_PATH=%s; %s update --no-auth-cache --non-interactive --trust-server-cert --username %s --password %s 2>&1', PROTO_DIR, SVN_LIB, SVN, self::USER_NAME, self::PASSWORD);
            exec($cmd, $output);

            if (strpos(json_encode($output), 'locked') !== false) {
                $cmd = sprintf('cd %s/tbclient && export LD_LIBRARY_PATH=%s; %s cleanup --no-auth-cache --non-interactive --trust-server-cert --username %s --password %s && %s update --no-auth-cache --non-interactive --trust-server-cert --username %s --password %s 2>&1', PROTO_DIR, SVN_LIB, SVN, self::USER_NAME, self::PASSWORD, SVN, self::USER_NAME, self::PASSWORD);
                exec($cmd, $output);
            }
            unset($output);

            $cmd = sprintf('cd %s/tbim && export LD_LIBRARY_PATH=%s; %s update --no-auth-cache --non-interactive --trust-server-cert --username %s --password %s 2>&1', PROTO_DIR, SVN_LIB, SVN, self::USER_NAME, self::PASSWORD);
            exec($cmd, $output);

            if (strpos(json_encode($output), 'locked') !== false) {
                $cmd = sprintf('cd %s/tbim && export LD_LIBRARY_PATH=%s; %s cleanup --no-auth-cache --non-interactive --trust-server-cert --username %s --password %s && %s update --no-auth-cache --non-interactive --trust-server-cert --username %s --password %s 2>&1', PROTO_DIR, SVN_LIB, SVN, self::USER_NAME, self::PASSWORD, SVN, self::USER_NAME, self::PASSWORD);
                exec($cmd, $output);
            }
            unset($output);
        }

        $arrSvnDir = array();
        foreach ($arrInput['files'] as $file) {
            $protoType = $file['proto_type'];
            $filePath = $file['path'];
            $fileName = substr($filePath, strrpos($filePath, '/') + 1);
            $fileDir = substr($filePath, 0, strrpos($filePath, '/'));

            if (!is_dir($fileDir)) {
                mkdir($fileDir);
            }

            if ($protoType == self::PROTO_TYPE_TBIM) {
                $arrSvnDir['im'] = 'im';
                if ($fileName == 'im.proto') {
                    $oldFilePath = sprintf('%s/tbim/tbim.proto.%s.%s', PROTO_DIR, time(), $userName);
                    copy(PROTO_DIR . '/tbim/tbim.proto', $oldFilePath);
                    copy($filePath, PROTO_DIR . '/tbim/tbim.proto');
                } else {
                    $dir = substr($fileName, 0, strlen($fileName) - self::SUFFIX_LEN);
                    $destFilePath = sprintf('%s/tbim/%s/%s', PROTO_DIR, $dir, $fileName);
                    $destFileDir = sprintf('%s/tbim/%s', PROTO_DIR, $dir);
                    if (!is_dir($destFileDir)) {
                        mkdir($destFileDir);
                    }
                    $oldFilePath = sprintf('%s.%s.%s', $destFilePath, time(), $userName);
                    copy($destFilePath, $oldFilePath);
                    copy($filePath, $destFilePath);
                }
            } else if ($protoType == self::PROTO_TYPE_TBCLIENT) {
                $arrSvnDir['client'] = 'client';
                if ($fileName == 'client.proto') {
                    $oldFilePath = sprintf('%s/tbclient/tbclient.proto.%s.%s', PROTO_DIR, time(), $userName);
                    copy(PROTO_DIR . '/tbclient/tbclient.proto', $oldFilePath);
                    copy($filePath, PROTO_DIR . '/tbclient/tbclient.proto');
                } else {
                    $dir = substr($fileName, 0, strlen($fileName) - self::SUFFIX_LEN);
                    $destFilePath = sprintf('%s/tbclient/%s/%s', PROTO_DIR, $dir, $fileName);
                    $destFileDir = sprintf('%s/tbclient/%s', PROTO_DIR, $dir);
                    if (!is_dir($destFileDir)) {
                        mkdir($destFileDir);
                    }
                    $oldFilePath = sprintf('%s.%s.%s', $destFilePath, time(), $userName);
                    copy($destFilePath, $oldFilePath);
                    copy($filePath, $destFilePath);
                }
            }
        }

        foreach ($arrSvnDir as $key => $value) {
            $dir = sprintf('%s/tb%s', PROTO_DIR, $key);

            $cmd = sprintf('cd %s && export LD_LIBRARY_PATH=%s; %s add --force . --username %s --password %s --no-auth-cache --non-interactive --trust-server-cert 2>&1', $dir, SVN_LIB, SVN, self::USER_NAME, self::PASSWORD);
            exec($cmd, $output);
            unset($output);

            $cmd = sprintf("cd %s && export LD_LIBRARY_PATH=%s; %s ci -m 'commit by %s' --no-auth-cache --non-interactive --trust-server-cert --username %s --password %s 2>&1", $dir, SVN_LIB, SVN, $userName, self::USER_NAME, self::PASSWORD);
            exec($cmd, $output);

            if (false === strpos(json_encode($output), 'Committed revision') && !empty($output)) {
                // TODO: LOG
                return self::_errRet(Tieba_Errcode::ERR_UNKNOWN);
            }
        }

        $errno = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );

        // TODO: action record

        return $arrOutput;
    }

    /**
     * @brief: 错误返回
     * @param:
     *      errno: 错误号
     * @return:
     */
    private static function _errRet($errno) {
        $errmsg = Tieba_Error::getErrmsg($errno);
        Util_Log::pushNotice('errno', $errno);
        Util_Log::pushNotice('errmsg', $errmsg);
        return array(
            'errno' => $errno,
            'errmsg' => $errmsg,
        );
    }
}
