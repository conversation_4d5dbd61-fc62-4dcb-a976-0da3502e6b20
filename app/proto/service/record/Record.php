<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015-09-09 09:30:00
 * @version 1.0
 */

class Service_Record_Record {

    public $service_ie = 'utf-8';
    public static $arrRecordFields = array(
        'op_user_name',
        'op_user_mail',
        'op_user_ip',
        'supervisor_name',
    );
    private static $_arrOptionalFields = array(
        'supervisor_name' => '',
        'message_name'    => '',
        'field_order'     => 0,
    );


    /**
     * @brief: 序列化变更字段信息
     * @param:
     * @return:
     */
    public static function buildRecord($arrInput) {
        $arrFields = array(
            'old_obj',
            'new_obj',
        );
        if (!self::_checkRequired($arrInput, $arrFields)) {
            Util_Log::warning("input params invalid. req: [" . serialize($arrInput) . "]");
            return $arrInput;
        }

        $arrInput['old_values'] = serialize($arrInput['old_obj']);
        $arrInput['new_values'] = serialize($arrInput['new_obj']);
        unset($arrInput['old_obj']);
        unset($arrInput['new_obj']);

        foreach (self::$_arrOptionalFields as $key => $value) {
            if (!isset($arrInput[$key])) {
                $arrInput[$key] = $value;
            }
        }
        
        return $arrInput;
    }

    /**
     * @brief: 记录操作日志
     * @param:
     * @return: bool
     */
    public static function recordAction($arrInput) {
        $arrFields = array(
            'op_user_name',
            'op_user_mail',
            'op_user_ip',
            'package',
            'old_obj',
            'new_obj',
        );
        if (!self::_checkRequired($arrInput, $arrFields)) {
            Util_Log::warning("input params invalid. req: [" . serialize($arrInput) . "]");
            return false;
        }

        $arrInput = self::buildRecord($arrInput);

        $arrInput['function'] = 'addActionRecord';
        $arrRes = Dl_Proto::query($arrInput);

        if (!$arrRes || Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']) {
            Util_Log::warning("call recordAction error. req: [" . serialize($arrReq) .
                "], res: [" . serialize($arrRes) . "]");
            return false;
        }
        
        return true;
    }

    /**
     * @brief: 获取操作记录
     * @param:
     * @return:
     */
    public static function getRecordAction($arrInput) {
        $arrFields = array(
            'package',
        );
        if (!self::_checkRequired($arrInput, $arrFields)) {
            Util_Log::warning("input params invalid. req: [" . serialize($arrInput) . "]");
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrInput['message_name'] = isset($arrInput['message_name']) ? $arrInput['message_name'] : '';
        $arrInput['field_order'] = isset($arrInput['field_order']) ? intval($arrInput['field_order']) : 0;
        $arrInput = array_merge($arrInput, array('function' => 'getActionRecord',));
        $arrRes = Dl_Proto::query($arrInput);

        if (!$arrRes) {
            Util_Log::warning("call getActionRecord error. req: [". serialize($arrReq) . "]");
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        } else if (Tieba_Errcode::ERR_SUCCESS != $arrRes['errno']) {
            Util_Log::warning("call getActionRecord error. req: [". serialize($arrReq) .
                "], res: [" . serialize($arrRes) . "]");
            return self::_errRet(intval($arrRes['errno']));
        }

        $arrRecord = $arrRes['results'][0];
        foreach ($arrRecord as &$record) {
            $arrOldValues = unserialize($record['old_values']);
            $arrNewValues = unserialize($record['new_values']);
            $record['diff'] = array();

            foreach ($arrNewValues as $nk => $nv) {
                if ($arrOldValues[$nk] != $nv) {
                    $record['diff'][$nk] = array($arrOldValues[$nk], $nv);
                    if (isset($arrOldValues[$nk])) {
                        unset($arrOldValues[$nk]);
                    }
                }
            }

            foreach ($arrOldValues as $ok => $ov) {
                $record['diff'][$ok] = array($ov, null);
            }

            unset($record['old_values']);
            unset($record['new_values']);
        }
        
        $errno = Tieba_Errcode::ERR_SUCCESS;
        $arrOutput = array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data'   => $arrRecord,
        );

        return $arrOutput;
    }


    /**
     * @brief: 错误返回
     * @param:
     *      errno: 错误号
     * @return:
     */
    private static function _errRet($errno) {
        $errmsg = Tieba_Error::getErrmsg($errno);
        Util_Log::pushNotice('errno', $errno);
        Util_Log::pushNotice('errmsg', $errmsg);
        return array(
            'errno' => $errno,
            'errmsg' => $errmsg,
        );
    }

    /**
     * @param $arrInput
     * @param $arrFields
     * @return bool
     */
    private static function _checkRequired($arrInput, $arrFields) {
        foreach($arrFields as $checkField) {
            if (!isset($arrInput[$checkField])) {
                return false;
            }
        }
        return true;
    }
}
