<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @date 2009-9-8
 * @version
 */
class TbMisLog {
    private static $_file = 'unknow';
    
    private static $_line = 'unknow';
    
    private static $_module = 0;
    
    private static $_instance = null;
    
    private static $_user;
    
    public static function getLogger(){
        if(self::$_instance == null){
            $class = __CLASS__;
            self::$_instance = new $class();
        }  
        return self::$_instance;      
    }
    //��ʼ�����ã�������־ģ��
    public static function setDefaultLogModule($module) {
        self::$_module = intval ( $module );
    }
    
    public static function setOpUser($arrUser){
        self::$_user = $arrUser;
    }    
    public static function notice($str, $arrParam = null,$module = 0) {
        self::_getFileAndLine ();
        $_module = self::$_module;
        if ($module != 0){
            $_module = $module;
        }
        UB_LOG_NOTICE ( $_module, $str . self::_parseParamStr ( $arrParam ), self::$_file, self::$_line, NULL, 0, 
            null );
    }
    
    public static function warning($str, $arrParam = null,$module = 0) {
        self::_getFileAndLine ();
        $_module = self::$_module;
        if ($module != 0){
            $_module = $module;
        }        
        UB_LOG_WARNING ( $_module, $str . self::_parseParamStr ( $arrParam ), self::$_file, self::$_line, NULL, 
            0, null );
    }
    public static function warning_file_line($str, $file, $line, $arrParam = null,$module = 0) {
        $_module = self::$_module;
        if ($module != 0){
            $_module = $module;
        }        
        UB_LOG_WARNING ( $_module, $str . self::_parseParamStr ( $arrParam ), $file, $line, NULL, 0, NULL );
    }
    public static function debug($str, $arrParam = null,$module = 0) {
        $_module = self::$_module;
        if ($module != 0){
            $_module = $module;
        }          
        self::_getFileAndLine ();
        UB_LOG_DEBUG ( $_module, $str . self::_parseParamStr ( $arrParam ), self::$_file, self::$_line, NULL, 0, 
            null );
    }
    private static function _parseParamStr($arrParam) {
        $str = '';
        if(!empty(self::$_user)){
            $user = self::$_user;
            $str .= "[op_uname:{$user['uname']}][op_uid:{$user['uid']}][op_ip:{$user['ip']}]";
        }
        if( ! empty ( $arrParam )) {
            $str .=  self::_getParamStr ( $arrParam );
        }
        return $str;
    }
    private static function _getParamStr($val) {
        if (is_object($val)){
            return '';
        }
        if( is_array ( $val )) {
            $arrOut = array ();
            $arrOut [] = '[';
            foreach ( $val as $key => $value ) {
                $arrOut [] = "<$key:";
                $arrOut [] = self::_getParamStr ( $value ) . '>';
            }
            $arrOut [] = ']';
            return implode ( '', $arrOut );
        }
        else {
            $val = strval($val);
            $val = substr ( $val, 0, 20 );
            return $val;
        }
    }
    private static function _getFileAndLine() {
        $trace = debug_backtrace ();
        $depth = count ( $trace ) - 1;
        if( $depth > 1)
            $depth = 1;
        self::$_file = $trace [$depth] ['file'];
        self::$_line = $trace [$depth] ['line'];
    }
}
