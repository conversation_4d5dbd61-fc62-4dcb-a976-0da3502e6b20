<?php
	class TbMisDbCommon{
	    //���ݿ����ӻ���,�����ι���
		private static $_db_cache = array();
		//ƴSQLģ���໺�����飬�����ι���
		private static $_template_cache = array();		
		const EQ = '=';
    	const LIKE = 'LIKE';
    	const RANGE = 'range'; 
		//��ö����ݿ�$key������
	    public static function getConn($key = null){
		    Bingo_Log::warning('2'.$key);   
		    Bingo_Log::getModule()->flush(); 
		if ($key == null){
	            $key = MisDbConfig::DEFAULT_MIS_DB_KEY;
	        }
		    if(!isset(MisDbConfig::$config[$key])){
			    Bingo_Log::warning('2db config');
			    Bingo_Log::getModule()->flush();
	            throw new Exception("db config $key not found");
	    }
		/*
	        if(isset(self::$_db_cache[$key])){
	            return self::$_db_cache[$key];
		}*/
	        $arrDbConfig = MisDbConfig::$config[$key];
			
			//�˴���ԭ����ֱ�������˺Ÿ�Ϊ��ral��ȡ��Ϣ
		//if($key != 'dm_monitor_new') {
				$ralInfo = ral_get_service($arrDbConfig);
				$arrDbConfig = array();
				$arrDbConfig['db_host'] = $ralInfo['server'][0]['ip'];
				$arrDbConfig['db_user'] = $ralInfo['user'];
				$arrDbConfig['db_password'] = base64_decode($ralInfo['passwd']);
				$arrDbConfig['db_name'] = $ralInfo['extra']['dbname'];
				$arrDbConfig['db_port'] = $ralInfo['server'][0]['port'];
		//}
		Bingo_Log::warning('2'.serialize($arrDbConfig));
		Bingo_Log::getModule()->flush();
	        $connect = new mysqli($arrDbConfig['db_host'],
	                                        $arrDbConfig['db_user'],
	                                        $arrDbConfig['db_password'],
	                                        $arrDbConfig['db_name'],
                                            $arrDbConfig['db_port']);

            	//self::$_template_cache[$key] = new SQLTemplate(self::$_db_cache[$key]);	                                        
	        if(mysqli_connect_errno()){
			Bingo_Log::warning('2'.mysqli_connect_errno());
		  	Bingo_Log::getModule()->flush();	
			throw new Exception(mysqli_connect_error());
	        }
			mysqli_query($connect,'set names gbk');
            Bingo_Log::warning('gbk ok!');
	        return $connect;
	    }
	    //��ȡ���ݿ�ƴװģ��
	    private static function getDbTemplate($key=null){
	    	if ($key == null){
	            $key = MisDbConfig::DEFAULT_MIS_DB_KEY;
	        }	        
	        if (!isset(self::$_template_cache[$key])) {
	        	self::getConn($key);
	        }
	        return self::$_template_cache[$key];
	    }
	    //����ģ������SQL
        public static function genSqlByTpl($strTpl,$arrData,$strDbKey = null){
            $objTemplate = self::getDbTemplate($strDbKey);
            $objTemplate->prepare($strTpl);
            $objTemplate->bindParam($arrData, NULL, true);
            $strSql = $objTemplate->getSQL();
            return $strSql;	        
	    }
	    public static function findAllBySqlTpl($strTpl,$arrData,$strDbKey = null){
            $query = self::genSqlByTpl($strTpl,$arrData,$strDbKey);
	        if ($query === false) {
	        	return false;
	        }
	        return self::findAllByQuery($query,$strDbKey);
	    }
        public static function findAllByQuery($query,$strDbKey = null){
	        return self::getAllRows(self::doQuery($query,$strDbKey));
        }

        // add by xiechao01
        // get Result & Rows
        public static function findAllAndCountsByQuery($query, $strDbKey = null){
            $ret = self::doQueryAndCounts($query,$strDbKey);
            $arrRes = Array(
                'result' => self::getAllRows($ret['result']),
                'ct' => $ret['ct'],
                'insert_id' => $ret['insert_id'],
            );
            return $arrRes;
        }

		public static function findOneBySqlTpl($strTpl,$arrData,$strDbKey = null){
            $query = self::genSqlByTpl($strTpl,$arrData,$strDbKey);
            
	        if ($query === false) {
	        	return false;
	        }
	        return self::findOneByQuery($query,$strDbKey);
	    }	    
	    public static function findOneByQuery($query,$strDbKey = null){
	        return self::getOneRow(self::doQuery($query,$strDbKey));
	    }
		public static function doSubmitBySqlTpl($strTpl,$arrData,$strDbKey = null){
	        $query = self::genSqlByTpl($strTpl,$arrData,$strDbKey);
	        if ($query === false) {
	        	return false;
	        }
	        return self::findOneByQuery($query,$strDbKey);
	    }	    
	    //ƴ������sql���
	    public static function buildSearchWhere($arrSearchMap, $arrInput,$strDbKey = null){
	    	$link = self::getConn($strDbKey);
        	$where = '';
	        foreach ($arrSearchMap as $key => $expr){
	        	if (!isset($arrInput[$key])){
                	continue;           
            	}
            	$value = $arrInput[$key];
	            switch ($expr) {
	            	case self::EQ:
            	        $value = mysqli_real_escape_string($link, $value);
	                	$where .=" AND $key = '$value'";
	                	break;                	
	                case self::LIKE:
            	        $value = mysqli_real_escape_string($link, $value);	                    
	                	$where .=" AND $key like '%$value%'";                	
	                	break;        
	                case self::RANGE:
	                    if (!is_array($value)){
	                        $value = array($value);
	                    }      	
	                    if(isset($value[0])){
	                        $tmp = trim($value[0]);
	                        $where .=" AND $key >= '{$tmp}'";
	                    }
	                    if (isset($value[1])){
	                        $tmp = trim($value[1]);
	                        $where .=" AND $key <= '{$tmp}'";
	                    }
	                default:                		
	                	break;
	            }
	        }
        	return $where;
    	}  
    	//ƴ�����sql���
    	public static function buildInsertWhere($arrInsertMap, $arrInput,$strDbKey = null){
    		$link = self::getConn($strDbKey);
        	$where = '';
	        foreach ($arrInsertMap as $key){
	        	if (!isset($arrInput[$key])){
                	continue;           
            	}
            	$value = $arrInput[$key];
            	if (strlen($value) == 0){
                	continue;
            	}
            	$value = mysqli_real_escape_string($link, $value);
	            $where .= ", $key = '$value'";
	        }
        	return $where;
    	}
    	//ƴ������sql���
    	public static function buildDeleteWhere($arrDeleteSetMap, $arrDeleteWhereMap, $arrInput,$strDbKey = null){
    		$link = self::getConn($strDbKey);
        	$where = '';
	        foreach ($arrDeleteSetMap as $key){
	        	if (!isset($arrInput[$key])){
                	continue;           
            	}
            	$value = $arrInput[$key];
            	if (strlen($value) == 0){
                	continue;
            	}
            	$value = mysqli_real_escape_string($link, $value);
	            $where .= ", $key = '$value'";
	        }
	        $where .= ' WHERE 1=1 ';
    		foreach ($arrDeleteWhereMap as $key){
	        	if (!isset($arrInput[$key])){
                	continue;           
            	}
            	$value = $arrInput[$key];
            	if (strlen($value) == 0){
                	continue;
            	}
            	$value = mysqli_real_escape_string($link, $value);
	            $where .=" AND $key = '$value'";
	        }
        	return $where;
    	}
    	//ִ��sql
    	public static function doQuery($query,$strDbKey = null){
            $link = self::getConn($strDbKey);
            $ret= mysqli_query($link, $query);
    		Bingo_Log::debug($query);

    		if ($ret === false){
    			Bingo_Log::warning($link->error."[sql:$query]");
    		}
    		return $ret;    	                
	}
	public static function doQuerySpec($query,$strDbKey = null,&$errno){
            $link = self::getConn($strDbKey);
            $ret= mysqli_query($link, $query);
                Bingo_Log::debug($query);
		$errno = mysqli_errno($link);
                if ($ret === false){
                        Bingo_Log::warning($link->error."[sql:$query]");
		}
                return $ret;    
        }
	    public static function doQueryUserResult($query,$strDbKey = null){
    		$link = self::getConn($strDbKey);
    		$ret= mysqli_query($link, $query,MYSQLI_USE_RESULT);
    		Bingo_Log::debug($query);
    		if ($ret === false){
    			Bingo_Log::warning($link->error."[sql:$query]");
    		}
    		return $ret;    	                
        }

        // add by xiechao01
        // get Rows & Counts
        public static function doQueryAndCounts($query,$strDbKey = null){
            $link = self::getConn($strDbKey);
            $ret= mysqli_query($link, $query);
            Bingo_Log::debug($query);
            if ($ret === false){
                Bingo_Log::warning($link->error."[sql:$query]");
            }
            $ct = mysqli_affected_rows($link);
            $insert_id = mysqli_insert_id($link);
            return Array('result' => $ret, 'ct' => $ct, 'insert_id' => $insert_id);
        }

    	//ִ�������sql�ӿ�
    	public static function doBrowseQuery($query,$strDbKey = null){
            return self::doQuery($query,$strDbKey);
    	}
    	//����MySql��ѯ���������fetch_assoc()
    	public static function getAllRows($sqlResult){
    	    $arrResult = array();
    	    if ($sqlResult === false){
    	        return $arrResult;
    	    }
    	    if (get_class($sqlResult) != 'mysqli_result'){
    	        return $arrResult;
    	    }
            while ($row = $sqlResult->fetch_assoc()){
    	        $arrResult[] = $row;
    	    }
    	    return $arrResult;    	    
    	}
	    public static function getOneRow($sqlResult){
    	    $arrResult = array();
    	    if ($sqlResult === false){
    	        return $arrResult;
    	    }
    	    if (get_class($sqlResult) != 'mysqli_result'){
    	        return $arrResult;
    	    }
    	    if ($row = $sqlResult->fetch_assoc()){
    	        $arrResult = $row;
    	    }
    	    return $arrResult;    	    
    	}    	
    	//ʹ��CMִ���ύ��sql�ӿ�
		public static function doCmSubmitQuery($query, $command_no){
			$arrCmInput['command_no'] = $command_no;
	        $arrCmInput['sql'] = array($query,);
	        $arrCmInput['is_procedure'] = 0;
    		return self::sendCm($arrCmInput);
    	}
    	
		private function sendCm($arrCmInput){
	    	$arrOutput = array();
			$bolRes = Rpc :: rpcCall ('clubcm', 'updateDB', $arrCmInput, $arrOutput);
			foreach($arrCmInput['sql'] as $key => $value){
			    Bingo_Log::debug("send to cm sql[$key][$value]");
			}
			if ($bolRes === false) {
				Bingo_Log::warning('fail to invoke talk with cm![input:'.serialize($arrCmInput).']');
			}
			return $bolRes;
	    }
	}
?>
