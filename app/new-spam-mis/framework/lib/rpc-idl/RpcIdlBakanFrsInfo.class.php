<?php
/**
 * ��frsҳ�棬��ȡ�����ðɿ�����Ϣ
 * Χ��cache��http����չ��
 *
 */
class RpcIdlBakanFrsInfo {
	/**
	 * http���󣬻�ȡ�ɿ�frsչʾ��Ϣ
	 * 
	 */
	public static $arrBakanFrs; /*frs bakan info*/
	
	public static $intConnectTimeOut = 1000;
	
	public static $intReadTimeOut = 1000;
	
	public static $ip = 'http://tc-forum-test08.tc.baidu.com';
	
	public static $port = '8088';
	
	public static $__bakanCache = null;
	
	public static $cache_server = 'bakan_frs';
	
	const CACHE_EXPIRE_TIME = 3600;
	
	
	/**
	 * ����frsҳ��İɿ�(�ɿ�ϵͳʹ��)
	 */
	public static function deleteBakanFrsCache($forum_name){
		$cache_key = 'bakan_key_'.$forum_name;
		self::$__bakanCache = new MemCachedWrapper(self::$cache_server);
		self::$__bakanCache->deleteAll(self::$cache_server,$cache_key);
		MyLog::debug("delete the cache all.cache_key[$cache_key]");
		
		return true;
		
	}
	/**
	 * ����http���Ӷ˿ں�ip�ĺ���
	 */
	public static function setHttpIpPort($ip,$port) {
		self::$ip = $ip;
		self::$port = $port;
		return true;
	}
	/**
	 * ��ȡfrsҳ��İɿ���Ϣ
	 */
	public static function getBakanFrsCache($forum_name) {
		$arrFrsBakan = array();
		$cache_key = 'bakan_key_'.$forum_name;
		self::$__bakanCache = new MemCachedWrapper(self::$cache_server);
		$arrFrsBakan = self::getCache($cache_key);
		if (false === $arrFrsBakan) {
			Rpc::putLog('warning',"not find the forum_name[$forum_name] in the cache" ,
                 __FILE__, __LINE__, NULL, 0, NULL);
			$arrFrsBakan = self::getBakanFrsHttp(self::$ip,self::$port,$forum_name);
			$bolRet = self::addCache($cache_key,$arrFrsBakan);
			if (false === $bolRet) {
				Rpc::putLog('warning',"set the forum_name[$forum_name] to cache error." ,
                 __FILE__, __LINE__, NULL, 0, NULL);
			}else {
				Rpc::putLog('debug',"set the forum_name[$forum_name] to cache success." ,
                 __FILE__, __LINE__, NULL, 0, NULL);
			}
			return $arrFrsBakan;
		}else {
			Rpc::putLog('debug',"find the forum_name[$forum_name] in the cache." ,
                 __FILE__, __LINE__, NULL, 0, NULL);
			return $arrFrsBakan;
		}
	}
	
	/**
     * @brief get cache
     **/
	private static function getCache($strKey) {
		$arrIdStr[] = $strKey;
        $arrCache = self::$__bakanCache->get($arrIdStr);
        if ($arrCache !== false) {
            if (array_key_exists($strKey, $arrCache) === true) {
                return $arrCache[$strKey];
            }
        }

		/*
		$arrCache = self::$__bakanCache->get($strKey);
		if ($arrCache !== false) {
			return $arrCache;
		}*/
        return false;
    }
	
    /**
     * @brief add cache
     **/
    private static function addCache($strKey, $value) {
        $bolRet = self::$__bakanCache->set($strKey, $value, false, time()+self::CACHE_EXPIRE_TIME);
        //$bolRet = self::$__bakanCache->set($strKey, $value);
        return $bolRet;
        
    }
	
	protected static function getBakanFrsHttp($ip,$port,$forum_name) {
		
		$req_url = $ip.':'.$port.'/bakan/sys/get_frs_bakan?kw='.$forum_name;
		//echo($req_url);
		
		// ��ʼ��һ�� cURL ����
		$curl = curl_init(); 
		
		// ��������Ҫץȡ��URL
		curl_setopt($curl, CURLOPT_URL, $req_url);
		
		// ����header
		curl_setopt($curl, CURLOPT_HEADER, 0);
		
		// ����cURL ������Ҫ�������浽�ַ����л����������Ļ�ϡ�
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
		
		//�������ӳ�ʱ
		curl_setopt($curl, CURLOPT_CONNECTTIMEOUT_MS, self::$intConnectTimeOut);
		
		//���ö�д��ʱ
		curl_setopt($curl, CURLOPT_TIMEOUT_MS,self::$intReadTimeOut);
		
		// ����cURL��������ҳ
		$data = curl_exec($curl);
		
		// �ر�URL����
		curl_close($curl);
		
		// ��ʾ��õ�����
		//echo($data);
		$arrRet = unserialize($data);
		//var_dump($arrRet);
		if (is_array($arrRet) && isset($arrRet['error_no']) 
			&& intval($arrRet['error_no']) == 0) {
			self::$arrBakanFrs = $arrRet['msg'];
			self::$arrBakanFrs['has_open'] = true;
		}else {
			self::$arrBakanFrs['has_open'] = false;
		}
		//var_dump(self::$arrBakanFrs);
		return self::$arrBakanFrs;
		
	}
	
}

?>
