<?php 
/**
 * buff store rpc idl
 * <AUTHOR> <<EMAIL>>
 * @since 2009-01-19
 * @package buff-logic
 *
 */
class RpcIdlBuffStore
{
    const SERVER_NAME = 'buff_store';
    
    const QUERY_NAME = 'query';   
    
    public static function call($arrInput)
    {
        $boolRs = Rpc::rpcCall(self::SERVER_NAME, self::QUERY_NAME, $arrInput, $arrOutput);
        if (! $boolRs || ! isset($arrOutput) || empty($arrOutput)) {
            Rpc::putLog('warning', 'RpcIdlBuffStore Failure!input:' . serialize($arrInput),
                 __FILE__, __LINE__, NULL, 0, NULL);
            return FALSE;
        }
        return $arrOutput;
    }
}