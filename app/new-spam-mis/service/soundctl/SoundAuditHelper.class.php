<?php
class SoundAuditHelper{
	public static function getSoundPost($arrInput){
		$arrDefaultConfig = array ('size' => 30 );
		$misName = ($arrInput['type'] === 0)?'ȫ�������':(($arrInput['type'] === 1)?'������':'������');
		$arrTplVar = TbMisViewHelper::getDefaultTplVar ();
		$tableTpl = 'sound_audit_check_box_table';
		$arrTplVar = array_merge ( $arrTplVar, array (
				'mis_table' => $tableTpl, 
				'mis_table_template' => $tableTpl, 
				'mis_name' => $misName, 
				'subIndex' => 0 ) );
		$arrResult = TbMisViewHelper::getDefaultResult ();
		$arrResult ['total'] = self::getSearchCount ( $arrInput);
		if ($arrResult ['total'] != 0) {
			$arrResult['data'] = self::getSearchData ( $arrInput);
		}
		$arrResult ['page'] = $arrInput ['page'];		
		TbMisViewHelper::viewProcess ( $arrInput, $arrResult, $arrTplVar, $arrDefaultConfig, array (
				'type', 
				'op_status', 
				'tabledate',
				'from_times',
				'to_times',
				 )
		);
		
	}
	
	protected static function getSearchCount ( $arrInput){
		if($arrInput['type'] === 0){//ȫ��
			$table = 'audio_monitor' . $arrInput['tabledate'];	
			$sql = 'SELECT count(*) as count FROM ' . $table . ' WHERE 1=1 ';		
		}
		if($arrInput['type'] === 1){//100 play_times
			$table = 'sound_post_multi_play';
			$sql = 'SELECT count(*) as count FROM ' . $table . ' WHERE play_times >=100 ';
		}
		if($arrInput['type'] === 2){//400 play_times
			$table = 'sound_post_multi_play';
			$sql = 'SELECT count(*) as count FROM ' . $table . ' WHERE play_times >=400 ';
		}
		$date = date('Y-m-d',strtotime($arrInput['tabledate']));
		$sql .= " and db_time like '%$date%'";
		if(isset($arrInput['fname'])){
				$forum = $arrInput['fname'];
				$sql .= " and forum like '%$forum%'";
			}
			if(isset ( $arrInput ['username'] )){
				$user = $arrInput['username'];
				$sql .= " and user ='$user'";
			}
			if(isset ( $arrInput ['userid'] )){
				$userId = $arrInput['userid'];
				$sql .= " and user_id =$userId";
			}
			if($arrInput ['status'] !== 'all'){
				$status = intval($arrInput['status']);
				if($status===0){//
					$sql .= " and op_status=0";
				}
				if($status===1){//pass
					$sql .= " and (op_status&1=1 or op_status&4=4)";
				}
				if($status===2){//delete
					$sql .= " and (op_status&2=2 or op_status&8=8)";
				}
				
			}
			if(($arrInput['from_times'] == $arrInput['to_times'])&& $arrInput['from_times']>=0){
				$times = $arrInput['to_times'];
				$sql .= " and play_times=$times";
			}
			if($arrInput['from_times'] > $arrInput['to_times']){
				$from = $arrInput['to_times'];
				$to = $arrInput['from_times'];
				$sql .= " and play_times>=$from and play_times<=$to";
			}
			if($arrInput['from_times'] < $arrInput['to_times']){
				$from = $arrInput['from_times'];
				$to = $arrInput['to_times'];
				$sql .= " and play_times<=$to and play_times>=$from";
			}
			$intCount = 0;
			$result = TbMisDbCommon::doQuery ( $sql, 'newonlinemonitor' );
			if ($result === false) {
				return $intCount;
			}
			$row = mysqli_fetch_assoc ( $result );
			if ($row) {
				$intCount = $row ['count'];
			}
			return $intCount;
	}
	
	protected static function getSearchData ( $arrInput){
		if($arrInput['type'] === 0){//ȫ��
			$table = 'audio_monitor' . $arrInput['tabledate'];	
			$sql = 'SELECT * FROM ' . $table . ' WHERE 1=1 ';		
		}
		if($arrInput['type'] === 1){//100 play_times
			$table = 'sound_post_multi_play';
			$sql = 'SELECT * FROM ' . $table . ' WHERE play_times >=100 ';
		}
		if($arrInput['type'] === 2){//400 play_times
			$table = 'sound_post_multi_play';
			$sql = 'SELECT * FROM ' . $table . ' WHERE play_times >=400 ';
		}
		$date = date('Y-m-d',strtotime($arrInput['tabledate']));
		$sql .= " and db_time like '%$date%'";
		if(isset($arrInput['fname'])){
				$forum = $arrInput['fname'];
				$sql .= " and forum like '%$forum%'";
		}
		if(isset ( $arrInput ['username'] )){
			$user = $arrInput['username'];
			$sql .= " and user ='$user'";
		}
		if(isset ( $arrInput ['userid'] )){
			$userId = $arrInput['userid'];
			$sql .= " and user_id =$userId";
		}
		//if($arrInput ['status'] !== 'all'){
		//	$status = intval($arrInput['status']);
		//	$sql .= " and op_status =$status";
		//}
		if($arrInput ['status'] !== 'all'){
				$status = intval($arrInput['status']);
				if($status===0){//
					$sql .= " and op_status=0";
				}
				if($status===1){//pass
					$sql .= " and (op_status&1=1 or op_status&4=4)";
				}
				if($status===2){//delete
					$sql .= " and (op_status&2=2 or op_status&8=8)";
				}
				
			}
		if(($arrInput['from_times'] == $arrInput['to_times'])&& $arrInput['from_times']>=0){
				$times = $arrInput['to_times'];
				$sql .= " and play_times=$times";
			}
			if($arrInput['from_times'] > $arrInput['to_times']){
				$from = $arrInput['to_times'];
				$to = $arrInput['from_times'];
				$sql .= " and play_times>=$from and play_times<=$to";
			}
			if($arrInput['from_times'] < $arrInput['to_times']){
				$from = $arrInput['from_times'];
				$to = $arrInput['to_times'];
				$sql .= " and play_times<=$to and play_times>=$from";
			}
		$sql .= " ORDER BY db_time DESC ";
		$page = ( int ) $arrInput ['page']; // ҳ��
		$size = 30; // ÿҳ����ʾ����
		$offset = ($page - 1) * $size; // ����ƫ����
		if ($page > 0) {
			$sql .= " LIMIT $offset,$size";
		}
		$sqlResult = TbMisDbCommon::doQuery ( $sql, 'newonlinemonitor' );
		if ($sqlResult == false) {
			return false;
		}
		$data = TbMisDbCommon::getAllRows ( $sqlResult );
		$arrResult = array ();
		foreach ($data as $one){
			$arrRow ['username'] = $one ['user'];
			$arrRow ['forum'] = $one ['forum'];
			$arrRow ['fname'] = $one ['forum'];
			$arrRow ['id'] = $one ['postdb_id'];
			$arrRow ['userid'] = $one ['user_id'];
			$arrRow['title'] = $one ['title'];
			$arrRow['content'] = $one ['content'];
			$arrRow['create_time'] = $one ['create_time'];
			$arrRow['op_status'] = $one ['op_status'];
			$arrRow['thread_id'] = $one ['thread_id'];
			$arrRow['forum_id'] = $one ['forum_id'];
			$arrRow['post_id'] = $one ['post_id'];
			$arrRow['forum_id'] = $one ['forum_id'];
			$arrRow ['user_id'] = $one ['user_id'];
			$arrRow ['ori_content'] = $one ['ori_content'];
			$arrRow ['play_times'] = $one ['play_times'];
			$arrRow['sound'] = 'http://mis.tieba.baidu.com/new-spam-mis/soundaudit/getvoice/?tid='.$one ['thread_id'].'&pid='.$one ['post_id'];
			
			$arrResult [] = $arrRow;
		}
		return $arrResult;
	}
	
	public function updateStatus($intId, $arrOpUser, $intStatus,$type, $tabledate) {
		if($type === 0){
			$table = 'audio_monitor' . $tabledate;
		}
		if($type === 1){
			$table = 'sound_post_multi_play';
		}
		if($type===2){
			$table = 'sound_post_multi_play';
			if($intStatus ===1){
				$intStatus = 4;
			}
			if($intStatus === 2){
				$intStatus = 8;
			}
		}
		$opTime = date('Y-m-d H:i:s');
		$op_uid =$arrOpUser ['uid'];
		$op_username = $arrOpUser ['uname']; 
		$postdb_id = $intId ;
		$query = "UPDATE {$table} SET op_time='$opTime',op_uid=$op_uid,op_username='$op_username',op_status=op_status|$intStatus where postdb_id=$postdb_id";
		$result = TbMisDbCommon::doQuery ( $query, 'newonlinemonitor' );
		if ($result === false) {
			Bingo_Log::warning("����monitor��״̬ʧ�ܣ�postdb_id=$intId��op_status=$intStatus");
		}
		return $result;
	}
	
	public static function getSoundPostByPostDbId($type,$tabledate,$oneId){
		if($type === 0){
			$table = 'audio_monitor' . $tabledate;
		}
		if($type>0){
			$table = 'sound_post_multi_play';
		}
		$sql = "select * from $table where postdb_id =$oneId ";
		$result = TbMisDbCommon::doQuery ( $sql, 'newonlinemonitor' );
		if ($result === false) {
			Bingo_Log::warning("doQuery fail ".$sql);
			return false;
		}
		$data = TbMisDbCommon::getAllRows ( $result );
		return $data[0];
	}
	
}
?>
