<?php
/**
 * Ԥ��ƽ̨���������������ݿ�forum_mis_pmc������
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 *         @date 2013-8-19
 * @version 1.0
 */

class PmcService {
	public static function queryrecordsbytime($inputParams) {
		if (! is_numeric ( $inputParams['begintime'] ) || ! is_numeric ( $inputParams['endtime'] ) ){
			return false;
		}
		$begintime = $inputParams['begintime'];
		$endtime = $inputParams['endtime'];
		
		$query ="select apeal_type,apeal_uid from apeal_list where punish_byman = 0 and apeal_time>='$begintime' and apeal_time<='$endtime'";
		$sqlResult = TbMisDbCommon::doQuery ( $query,'pmc-mis' );
		$arrResult = TbMisDbCommon::getAllRows ( $sqlResult );
		
		if (! empty ( $arrResult )) {
			return $arrResult;
		}
		else {
			return null;
		}
	}
	
	
		
		
	
}

?>
