<?php
	require_once ( 'coreUsersConf.php' );
	//获取审核/勋章词表黑名单
	function ch_json_encode($data) {
   	/**
   	 * 将中文编码
   	 * @param array $data
   	 * @return string
   	*/
   		function ch_urlencode($data) {
   		    if (is_array($data) || is_object($data)) {
   		        foreach ($data as $k => $v) {
        		       if (is_scalar($v)) {
                		   if (is_array($data)) {
                       			$data[$k] = urlencode($v);
                  		 } else if (is_object($data)) {
                       		$data->$k = urlencode($v);
                  		 }
              			 } else if (is_array($data)) {
                   		$data[$k] = ch_urlencode($v); //递归调用该函数
               			} else if (is_object($data)) {
                   		$data->$k = ch_urlencode($v);
              		 	}
           		}
       		}
       		return $data;
   	}
   	
   	$ret = ch_urlencode($data);
   	$ret = json_encode($ret);
   	return urldecode($ret);
	}
	//get blakc ids
	$mysqli_black = mysqli_init();
	mysqli_options($mysqli_black,MYSQLI_OPT_CONNECT_TIMEOUT,5);
	
	//$ret_black = mysqli_real_connect($mysqli_black,'st01-forum-kw1-2.st01.baidu.com','mis','forummisFORUMMIS','wordmis',3336);
	$ret_black = mysqli_real_connect ( $mysqli_black, $dbConfig['wordmis']['db_host'],
                                              $dbConfig['wordmis']['db_user'],
                                              $dbConfig['wordmis']['db_password'],
                                              $dbConfig['wordmis']['db_name'],
                                              $dbConfig['wordmis']['db_port']);
	if(!$ret_black)
	{
		//Bingo_Log::warning("mysql_black connect fail");
		exit(0);
	}	
	//sql todo	
	//$sql_black = 'select word from wordsrv_wordlist_audit_and_medal_black where status = \'0\';';
	$sql_black = $sql_black_conf ;
	//echo $sql_black."\n";
	$res_black = $mysqli_black->query ( $sql_black );
	//Bingo_Log::warning ( $sql_black );
	if  ( !$res_black ) {
		//Bingo_Log::warning("select fail");
		exit ( 0 );
	}

	//print_r ( $res_black );
	//echo $res_black."\n";
	$black_names = array ( );
	while ( $row_black = mysqli_fetch_assoc ( $res_black ) ) {
//		print_r( $row_black );
		//echo '$row_black'; 
		$black_names[] = $row_black['word'];//todo
	}
//	print_r ( $black_names );
	
	// get public test unames
	//获取众测词表
	$mysqli = mysqli_init();
	mysqli_options($mysqli,MYSQLI_OPT_CONNECT_TIMEOUT,5);
//	$ret = mysqli_real_connect($mysqli,'cq01-forum-rdtest23.vm.baidu.com','root','root','wordsrv',3306);
	$ret = mysqli_real_connect ( $mysqli, $dbConfig['wordsrv']['db_host'],
					      $dbConfig['wordsrv']['db_user'], 
					      $dbConfig['wordsrv']['db_password'],
					      $dbConfig['wordsrv']['db_name'],
					      $dbConfig['wordsrv']['db_port']);
	if(!$ret)
	{
		//Bingo_Log::warning("mysql connect fail");
		exit(0);
	}	
//	$sql = 'select word1 from wordsrv_store_124 where status = \'2\'';
	$sql = $sql_public_test_conf;
//	$sql = 'select word1 from wordsrv_store_124 order by word_id desc limit 11';
	$res = $mysqli->query ( $sql );
	//Bingo_Log::warning($sql);
	if  ( !$res ) {
		//Bingo_Log::warning("select fail");
		exit ( 0 );
	}	
	//传递众测名单给众测	
	$public_test_names = array ( );
	$count = 0;
	$num_one_curl = $array_num_in_one_curl;
	while ( $row = mysqli_fetch_assoc ( $res ) ) {
		//print_r ( $row['word1'] );
		$arrInput = array ( 'user_name' => array ( $row['word1'] ) );
                //$arrInput = array ('user_name' => array('wh2000292' ));
                //print_r ( $arrInput );
                $id_info = Tieba_Service::call('user','getUidByUnames',$arrInput);
//              $id_test = PuserInfo::getInfoByun( 'abc' , array('uid'));
                //print_r ( $id_info ); 
                $new_cuser_id = $id_info['output']['uids'][0]['user_id'];
        //        print_r ( $new_cuser_id );
		if  ( in_array ( $new_cuser_id, $black_names) )
			continue;
		//echo "---\n";	
		//print_r ( $row['word1'] );
	//	echo "---\n";	
	//	exit();
		$public_test_names[ (int)($count / $num_one_curl)][] =  iconv('gbk','utf-8',$row['word1']);
		$count++;
	}
	$curl_count = (int)($count / $num_one_curl) + 1;
	//while ( $curl_count-- )
	//print_r ( $public_test_names);
	
//	$cuser_ids[0] = mb_check_encoding ( $cuser_ids[0], 'UTF-8' ) ? $cuser_ids[0] : utf8_encode ( $cuser_ids[0] );
//	$cuser_ids[0] = mb_check_encoding ( $cuser_ids[0], 'UTF-8' ) ? $cuser_ids[0] : mb_convert_encoding( $cuser_ids[0], 'UTF-8', 'gbk');
//	echo "------" . $cuser_ids[0] . "----------";	
	//$test_post_url = "http://cq01-testing-sdc06.vm.baidu.com:8373/crowdtest_lixin/crowdtest/n/UserWs/importUser";
	$test_post_url = $public_test_import_url;
		
	while ( $curl_count-- ) {
		$dataJson = json_encode ( array ( 'user' => $public_test_names[$curl_count] ) );
		$token = md5 ( $dataJson . 'tieba_check' );
		$post_params = array (
		        'source' => 'tieba',
        		'data' => $dataJson,
       	 		'token' => $token, );
		$ch = curl_init();
		curl_setopt ( $ch, CURLOPT_URL, $test_post_url );
		curl_setopt ( $ch, CURLOPT_POST, true );
		curl_setopt ( $ch, CURLOPT_RETURNTRANSFER, true );//不直接输出，返回到变量
		curl_setopt ( $ch, CURLOPT_POSTFIELDS, $post_params );
		curl_setopt ( $ch, CURLOPT_CONNECTTIMEOUT, 30 );
		curl_setopt ( $ch, CURLOPT_TIMEOUT, 60 ); 
		$curl_result = curl_exec($ch);
		$result = json_decode( $curl_result );
		print_r($result);
		
		curl_close($ch);
		sleep ( 5 );
	}
?>
