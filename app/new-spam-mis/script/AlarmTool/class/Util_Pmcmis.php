<?php
// require_once 'Bd/Rpc/Camel.php';
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Block
 *
 * <AUTHOR>
 */
class Util_Pmcmis {
    //put your code here
    public static function getBlockInfo($uid)
    {
		$timelastmonth = mktime(date('H'),date('i'),date('s'),date('m')-1,date('d'), date('Y'));
		$act_type = 0;
		$arrParam = array('user_id' => $uid,'start_time'=>$timelastmonth,'act_type'=>$act_type);
		$ret= RpcService::ralCall('userstate','getDealBlockLog',$arrParam);
		if($ret['errno']!==0)
		{
			Bingo_Log::warning('call userstate::getDealBlockLog failed,input:'.  serialize($arrParam)." output:".  serialize($ret));
			return array();
		}
		return $ret['res']['deal_log'];
        
    }
    
    public static function getMaskInfo($uid) {
    	$timelastmonth = mktime(date('H'),date('i'),date('s'),date('m')-1,date('d'), date('Y'));
    	$act_type = 0;
    	$arrParam = array('user_id' => $uid,'start_time'=>$timelastmonth,'act_type'=>$act_type);
    	$ret= RpcService::ralCall('antibrowse','getDealMaskLog',$arrParam);
    	if($ret['errno']!==0)
    	{
    		Bingo_Log::warning('call antibrowser::getDealMaskLog failed,input:'.  serialize($arrParam)." output:".  serialize($ret));
    		return array();
    	}
    	return $ret['res']['mask_info'];
    }
    
    public static function getUserStateHistory($uid)
    {
    	$timelastmonth = mktime(date('H'),date('i'),date('s'),date('m')-1,date('d'), date('Y'));
    	$arrInput=array(
    			'service_type'=>'vcode',
    			'opgroup'=>'system',
    			'key'=>$uid,
    			'forum_id'=>-1,
    			'begin_time'=>$timelastmonth,
    	);
    	$ret=RpcService::ralCall('userstate','queryUserStateHistory',$arrInput);
    	if($ret['errno']!==0)
    	{
    		Bingo_Log::warning("call userstate::queryUserStateHistory, input:".serialize($arrInput).' output:'.serialize($ret));
    		return array();
    	}
    	return $ret['output']['history'];
    }
    
    public static function getUserBlockAndMaskInfo($uid)
    {
    	$timelastmonth = mktime(date('H'),date('i'),date('s'),date('m')-1,date('d'), date('Y'));
    	$act_type = 0;
    	$arrParam = array('user_id' => $uid,'start_time'=>$timelastmonth,'act_type'=>$act_type);
    	$ret= RpcService::ralCall('userstate','getDealBlockAndMaskLog',$arrParam);
    	if($ret['errno']!==0)
    	{
    		Bingo_Log::warning('call userstate::getDealBlockAndMaskLog failed,input:'.  serialize($arrParam)." output:".  serialize($ret));
    		return array();
    	}
    	return $ret['res']['deal_log'];
    	
    
    }
    
    
   
}

?>
