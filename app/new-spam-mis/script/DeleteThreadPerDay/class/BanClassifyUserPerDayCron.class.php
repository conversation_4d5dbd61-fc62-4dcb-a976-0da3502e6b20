<?php
/**
 * 
 * �����ھ�����û�id�õ��ĸ���ά�����ݣ�����ɾ���Ľű����˽ű���ClearHistoryCron�������Ǵ˽ű��е����ݾ������ݿ�ֱ�ӻ�ȡ��DM�������ݱȽ�ȫ
 * <AUTHOR>
 * @since 2012-8-24
 *
 */

require_once '/home/<USER>/forum-php/bin/new-spam-mis/service/helper/Bd_HttpProxy.class.php';

class BanClassifyUserPerDayCron extends TbMisCrontabBasic {
	
	const FLAG = 'ban_classify_user';
	
	private $_userName = '������״���';
	private $_dmTable = 'dm_classify_user';
	
	private $_clearHistoryUrl = 'http://m1-forum-spam07.m1.baidu.com:8666/data/recycle';
	
	protected function _run() {
		$this->_writeLog ( "BanClassifyUserPerDayCron begin run!" );
		$this->_deal ();
		$this->_writeLog ( "BanClassifyUserPerDayCron end !" );
	}
	
	private function _deal() {
		$yesterday = date ( 'Y-m-d', strtotime ( "-1 day" ) );
		$query = "select * from $this->_dmTable where op_status = 0 and dm_date>='$yesterday'";
		$dbResult = TbMisDbCommon::findAllByQuery ( $query, 'newonlinemonitor' );
		$this->_writeLog ( "BanClassifyUserPerDayCron: sql [$query]!" );
		if ($dbResult === false) {
			return false;
		}
		
		$dbResult2 = TbMisDbCommon::findAllByQuery ( "select word from mis_wordlist_white_user where status=0 and (failtime=0 OR failtime>=UNIX_TIMESTAMP(NOW()))", 'wordmis' );
		$whiteUserIds = array ();
		foreach ( $dbResult2 as $record ) {
			$whiteUserIds [] = intval ( $record ['word'] );
		}
		foreach ( $dbResult as $arrRow ) {
			$uname = $arrRow ['user_name'];
			$uid = $arrRow ['user_id'];
			$isVip = $arrRow ['is_vip'];
			$beginTime = $arrRow ['dm_date'];
			
			$today = date ( 'Y-m-d' );
			
			if (! is_numeric ( $uid ) || $uid <= 0) {
				continue;
			}
			
			if (in_array ( intval ( $uid ), $whiteUserIds )) {
				$this->_writeLog ( "BanClassifyUserPerDayCron: $uid is in white user list!" );
				$query = "update $this->_dmTable set op_status = 1, ban_time='$today' where user_id = $uid and dm_date>='$yesterday'";
				$result = TbMisDbCommon::doQuery ( $query, 'newonlinemonitor' );
				continue;
			}
			
			if (empty ( $uname )) {
				continue;
			}
			
			$endTime = date ( 'Y-m-d', strtotime ( $beginTime ) + 86400 );
			
			if ($isVip == 1) {
				RpcService::maskoffUser ( $uid, '*********', $this->_userName);
				//$result = RpcService::addUserToAntiserver ( $uid );
			} else {
				// $inputArr = array (
				// 'method' => 'do_recycle',
				// 'tasktype' => 4,
				// 'main_param' => mb_convert_encoding ( $uname, 'UTF-8', 'GBK'
				// ),
				// 'account' => mb_convert_encoding ( $this->_userName, 'UTF-8',
				// 'GBK' ),
				// 'accountid' => *********,
				// 'starttime' => date ( 'Y-m-d', strtotime ( '-7 days' ) . '
				// 00:00:00' ),
				// 'endtime' => date ( 'Y-m-d' ) . ' 23:59:59' );
				// $httpProxy = Bd_HttpProxy::getInstance ();
				// $result = $httpProxy->post ( $this->_clearHistoryUrl,
				// $inputArr );
				// $this->_writeLog ( "BanClassifyUserPerDayCron: add user to
				// recycle system : " . serialize ( $result ) );
				RpcService::maskoffUser ( $uid, '*********', $this->_userName);
				//$result = RpcService::singleton ()->globalBanUser ( $uname, $uid, '*********', $this->_userName, 2 );
				$result = true;
			}
			
			if ($result === false) {
				$this->_writeLog ( "BanClassifyUserPerDayCron: deal user : $uid, $uname failed!" );
				$query = "update $this->_dmTable set op_status = 2, ban_time='$today' where user_id = $uid and dm_date>='$yesterday'";
			} else {
				$this->_writeLog ( "BanClassifyUserPerDayCron: deal user : $uid, $uname success!" );
				$query = "update $this->_dmTable set op_status = 1, ban_time='$today' where user_id = $uid and dm_date>='$yesterday'";
			}
			$result = TbMisDbCommon::doQuery ( $query, 'newonlinemonitor' );
		}
	}
	
	protected function _writeLog($str) {
		$strFile = $this->_localpath . '/deletethread/liubo03_' . self::FLAG . '.log';
		$strDatetime = date ( 'Y-m-d H:i:s' );
		file_put_contents ( $strFile, "$strDatetime $str\n", FILE_APPEND );
	}
	
	protected function _getFlagName() {
		return self::FLAG;
	}
}
