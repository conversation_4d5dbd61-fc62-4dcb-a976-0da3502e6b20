<?php
/**
 *  reply my friend
 * @version 2014-05-13
 * <AUTHOR>
 *
 */
class replyFriendAction extends Molib_Client_BaseAction {
	
	const FRIEND_SERVICE_FALSE = 3100044;        //调用friend service 失败
	const FRIEND_SERVICE_ERRNO_NOT_ZERO=3100045; //调用friend service 返回错误码不为0
    const YOU_ARE_FRIEND = 3100098;              //已经是好友,不能再回复
    const THIS_FRIEND_DIDNOT_ADD_YOU =3100099;   //只能通过已经申请加你的好友
    const USER_REPLY_TOO_MANY_TIMES = 310013;  //用户回复申请太多次
    const FRIEND_DELETE_STATAUS =3100105;      //朋友被删除
    
    
    protected static $_getMsg = array(
	    self::FRIEND_SERVICE_FALSE          => '系统错误。',
	    self::FRIEND_SERVICE_ERRNO_NOT_ZERO => '系统错误！',
	    self::YOU_ARE_FRIEND                => '您们已经是好友,不能再回复',
	    self::THIS_FRIEND_DIDNOT_ADD_YOU    => '该用户没申请加您好友,不能回复',
	    Tieba_Errcode::ERR_PROPS_IS_BLOCK   => '您已被全局封禁',
	    self::USER_REPLY_TOO_MANY_TIMES     => '您回复已超过限制',
	    self::FRIEND_DELETE_STATAUS         => '回复失败',
    );
    
	protected function _getPrivateInfo() {
		$arrPrivateInfo['ispv'] = 0;
		return $arrPrivateInfo;
	}
	protected function _checkPrivate() {
		return true;
	}
	protected function _execute() {
	   //get param
		$is_short = intval(Bingo_Http_Request::get('is_short', 0));
		//long connect get param
		$friend_id = intval($this->_getInput('friend_id', 0));
		$message = strval($this->_getInput('message', 0));
		
	    if($is_short){
			$friend_id = intval(Bingo_Http_Request::get('friend_id', 0));
			$message = strval(Bingo_Http_Request::get('message', 0));
		}
	    $uid = intval($this->_objRequest->getCommonAttr('user_id', 0));
		if($uid<=0){
			Bingo_Log::warning("user unlogin.user_id[".$uid."]");
			$arrErro = self::buiReturnData(Tieba_Errcode::ERR_USER_NOT_LOGIN,'用户未登录');
			return $this->_objResponse->setOutData($arrErro);
		}
		$arrInput['friend_id'] = intval($friend_id);
		$arrInput['message'] = strval($message);
		$arrInput['user_id'] = $uid;
		$arrOut = Tieba_Service::call('friend','replyFriend',$arrInput,NULL,NULL,'post','php','utf-8');
		
		$is_return = self::checkReturn($arrOut,$arrInput);
		if($is_return !== true){
			return $is_return;
		}
		
		//return data
		$arrReturn = self::buiReturnData(0,'success');
		Tieba_Stlog::addNode('ispv',0);
		return $this->_objResponse->setOutData($arrReturn);
	}
	public function checkReturn($arrOut,$arrInput=array()){
		if(false === $arrOut) {
			Bingo_Log::fatal("call friend replyFriend fail. input=".serialize($arrInput)." output=".serialize($arrOut));
			$arrReturn = self::buiReturnData(self::FRIEND_SERVICE_FALSE,self::$_getMsg[self::FRIEND_SERVICE_FALSE]);
			return $this->_objResponse->setOutData($arrReturn);
			
		}elseif($arrOut['errno'] ==Tieba_Errcode::ERR_PROPS_IS_BLOCK){
			
			$arrReturn = self::buiReturnData(Tieba_Errcode::ERR_PROPS_IS_BLOCK,self::$_getMsg[Tieba_Errcode::ERR_PROPS_IS_BLOCK]);
        	return $this->_objResponse->setOutData($arrReturn);
        	
		}elseif($arrOut['errno'] == self::THIS_FRIEND_DIDNOT_ADD_YOU){
        	$arrReturn = self::buiReturnData(self::THIS_FRIEND_DIDNOT_ADD_YOU,self::$_getMsg[self::THIS_FRIEND_DIDNOT_ADD_YOU]);
        	return $this->_objResponse->setOutData($arrReturn);
        	
        }elseif($arrOut['errno'] == self::YOU_ARE_FRIEND){
        	$arrReturn = self::buiReturnData(self::YOU_ARE_FRIEND,self::$_getMsg[self::YOU_ARE_FRIEND]);
        	return $this->_objResponse->setOutData($arrReturn);
        	
        }elseif($arrOut['errno'] == self::USER_REPLY_TOO_MANY_TIMES){
        	$arrReturn = self::buiReturnData(self::USER_REPLY_TOO_MANY_TIMES,self::$_getMsg[self::USER_REPLY_TOO_MANY_TIMES]);
        	return $this->_objResponse->setOutData($arrReturn);
        	
        }elseif($arrOut['errno'] == self::FRIEND_DELETE_STATAUS){
        	$arrReturn = self::buiReturnData(self::FRIEND_DELETE_STATAUS,self::$_getMsg[self::FRIEND_DELETE_STATAUS]);
        	return $this->_objResponse->setOutData($arrReturn);
        	
        }elseif($arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::fatal("call friend replyFriend fail. input=".serialize($arrInput)." output=".serialize($arrOut));
			$arrReturn = self::buiReturnData(self::FRIEND_SERVICE_ERRNO_NOT_ZERO,self::$_getMsg[self::FRIEND_SERVICE_ERRNO_NOT_ZERO]);
			return $this->_objResponse->setOutData($arrReturn);
		}
		return true;
	}
	
	private function _setResponse(){
		return true;
	}
	private function _stLog(){
		
	}
public function buiReturnData($errno=0,$errmsg='',$usermsg=''){
		return array(
		'errorno'=>intval($errno),
		'errmsg'=>strval($errmsg),
		'usermsg'=>strval($usermsg),
		'error_code'=>intval($errno),
		'error_msg'=>strval($errmsg),
		);
	}
	
}