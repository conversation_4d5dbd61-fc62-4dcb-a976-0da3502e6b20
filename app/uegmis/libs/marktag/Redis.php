<?php

/**
 *
 * @brife  redis相关操作
 *
 */
class Libs_Marktag_Redis {

    const REDIS_NAME = 'yuelaou';
    const DEFAULT_REDIS_EXPIRE = 43200;

    protected static $_redis = null;

    /**
     * @param
     * @return
     */
    private static function getRedis() {
        if (self::$_redis) {
            return self::$_redis;
        }
        Bingo_Timer::start('redis_init');
        self::$_redis = new Bingo_Cache_Redis(self::REDIS_NAME);
        Bingo_Timer::end('redis_init');

        if (!self::$_redis || !self::$_redis->isEnable()) {
            Bingo_Log::warning("init fdynamic redis fail.");
            self::$_redis = null;
            return null;
        }
        return self::$_redis;
    }

    /**
     * 批量获取Redis kv
     * @param
     * @return array ,return false if failed.
     */
    public static function mgetKv($arrKeys) {
        if (!is_array($arrKeys)){
            Bingo_Log::warning("array key is invalid");
            return false;
        }
        foreach ($arrKeys as $key) {
            $key = strval($key);
            $redisInput['reqs'][] = array(
                'key' => $key,
            );
        }
        if (count($redisInput) <= 0) {
            Bingo_Log::warning("no need to del");
            return false;
        }

        $redis = self::getRedis();
        if (!$redis) {
            Bingo_Log::warning('load redis failed');
            return false;
        }

        Bingo_Timer::start('redis_GET');
        $redisOutput = $redis->GET($redisInput);
        Bingo_Timer::end('redis_GET');

        if($redisOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS){
            Bingo_Log::warning(__FUNCTION__ . " call redis failed ,input:". serialize($redisInput) . ", output:" . serialize($redisOutput));
            return false;
        }

        return $redisOutput['ret'];
    }


    /**
     * 批量删除Redis
     * @param
     * @return
     */
    public static function mdel($arrKeys) {
        if (!is_array($arrKeys) || count($arrKeys) === 0) {
            Bingo_Log::warning("array key is invalid");
            return false;
        }
        $redisInput = array(
            'reqs' => $arrKeys,
        );
        $redis = self::getRedis();
        if (!$redis) {
            Bingo_Log::warning('load redis failed');
            return false;
        }

        Bingo_Timer::start('redis_DEL');
        $redisOutput = $redis->DEL($redisInput);
        Bingo_Timer::end('redis_DEL');

        if ($redisOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(__FUNCTION__ . " call redis failed ,input:" . serialize($redisInput) . ", output:" . serialize($redisOutput));
            return false;
        }

        return true;
    }

    /**
     * 批量设置redis数据
     * @param
     * @return
     */
    public static function msetKv($arrInput) {
        if (!is_array($arrInput['reqs']) || count($arrInput['reqs']) === 0) {
            Bingo_Log::warning("array reqs is invalid");
            return false;
        }
        foreach ($arrInput['reqs'] as $key => $val) {
            if (!isset($val['key']) || !isset($val['value'])) {
                Bingo_Log::warning("array reqs field is invalid");
                return false;
            }
            if (!isset($val['seconds'])) {
                $seconds = $arrInput['reqs'][$key]['seconds'] = self::DEFAULT_REDIS_EXPIRE;
            }
        }

        $redis = self::getRedis();
        if (!$redis) {
            Bingo_Log::warning('load redis failed');
            return false;
        }

        Bingo_Timer::start('redis_SETEX');
        $redisOutput = $redis->SETEX($arrInput);
        Bingo_Timer::end('redis_SETEX');

        if ($redisOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(__FUNCTION__ . " call redis failed ,input:" . serialize($arrInput) . ", output:" . serialize($redisOutput));
            return false;
        }
        return true;
    }

    /**
     * 批量添加set 类型数据
     * @param array $arrInput
     * @return boolean
     */
    public static function msetSet($arrInput) {
        if (!is_array($arrInput['reqs']) || count($arrInput['reqs']) === 0) {
            Bingo_Log::warning("array reqs is invalid");
            return false;
        }
        foreach ($arrInput['reqs'] as $key => $val) {
            if (!isset($val['key']) || !isset($val['member'])) {
                Bingo_Log::warning("array reqs field is invalid");
                return false;
            }
            if (!isset($val['seconds'])) {
                $seconds = $arrInput['reqs'][$key]['seconds'] = self::DEFAULT_REDIS_EXPIRE;
            }
        }
        $redis = self::getRedis();
        if (!$redis) {
            Bingo_Log::warning('load redis failed');
            return false;
        }

        Bingo_Timer::start('redis_SADD');
        $redisOutput = $redis->SADD($arrInput);
        Bingo_Timer::end('redis_SADD');

        if ($redisOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(__FUNCTION__ . " call redis failed ,input:" . serialize($arrInput) . ", output:" . serialize($redisOutput));
            return false;
        } else {
            $redis->EXPIRE($arrInput);
        }
        return true;
    }

    /**
     * 获取set类型数据
     * @param array $arrKeys
     * @return boolean|array
     */
    public static function mgetSet($arrKeys) {
        if (!is_array($arrKeys) || count($arrKeys) === 0) {
            Bingo_Log::warning("array key is invalid");
            return false;
        }
        $redisInput = array(
            'reqs' => array(
                $arrKeys,
            ),
        );

        $redis = self::getRedis();
        if (!$redis) {
            Bingo_Log::warning('load redis failed');
            return false;
        }

        Bingo_Timer::start('redis_GET');
        $redisOutput = $redis->SMEMBERS($redisInput);
        Bingo_Timer::end('redis_GET');

        if ($redisOutput['err_no'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning(__FUNCTION__ . " call redis failed ,input:" . serialize($redisInput) . ", output:" . serialize($redisOutput));
            return false;
        }

        return $redisOutput['ret'];
    }

    /**
     * hash
     * @param unknown $key
     * @return boolean
     */
    public static function hgetAll($key) {
        $redis = self::getRedis();
        if (!$redis) {
            Bingo_Log::warning('load redis failed');
            return false;
        }
        $redisInput = array(
            'key' => $key,
        );
        Bingo_Timer::start(__FUNCTION__);
        $redisOut = $redis->HGETALL($redisInput);
        Bingo_Timer::end(__FUNCTION__);
        if ($redisOut['err_no'] !== 0) {
            Bingo_Log::warning(__FUNCTION__ . " call redis failed ,input:" . serialize($redisInput) . ", output:" . serialize($redisOut));
            return false;
        }
        return $redisOut['ret'][$key];
    }

    /**
     *
     * @param unknown $key
     * @param unknown $fields
     * array(
     *     array(
     *        'field' => '9.0.0.1',
     * 'value' => 'test',
     *     ),
     * )
     * @return boolean
     */
    public static function hmset($key, $fields) {
        $redis = self::getRedis();
        if (!$redis) {
            Bingo_Log::warning('load redis failed');
            return false;
        }
        $redisInput = array(
            'key' => $key,
            'fields' => $fields,
        );
        Bingo_Timer::start(__FUNCTION__);
        $redisOut = $redis->HMSET($redisInput);
        Bingo_Timer::end(__FUNCTION__);
        if ($redisOut['err_no'] !== 0) {
            Bingo_Log::warning(__FUNCTION__ . " call redis failed ,input:" . serialize($redisInput) . ", output:" . serialize($redisOut));
            return false;
        }
        return true;
    }

    /**
     * 获取cuid对应的key
     * @param unknown $cuid
     * @return string
     */
    public static function getCuidKey($cuid) {
        return self::REDIS_CUID_PREFIX . crc32(trim($cuid));
    }

    /**
     *
     * @param unknown $key
     * @param unknown $fields
     * array(
     *    '9.0.0.1',
     * )
     * @return boolean
     */
    public static function hdel($key, $fields) {
        $redis = self::getRedis();
        if (!$redis) {
            Bingo_Log::warning('load redis failed');
            return false;
        }
        $redisInput = array(
            'key' => $key,
            'field' => $fields,
        );
        Bingo_Timer::start(__FUNCTION__);
        $redisOut = $redis->HDEL($redisInput);
        Bingo_Timer::end(__FUNCTION__);
        if ($redisOut['err_no'] !== 0) {
            Bingo_Log::warning(__FUNCTION__ . " call redis failed ,input:" . serialize($redisInput) . ", output:" . serialize($redisOut));
            return false;
        }
        return true;
    }
}


