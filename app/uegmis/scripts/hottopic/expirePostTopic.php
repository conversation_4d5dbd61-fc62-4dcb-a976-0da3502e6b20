<?php
/**
 * ===========================================
 * @desc: 判断发帖框配置的话题是否失效
 * @author: fengzhen
 * @date: 2016-6-12
 * ===========================================
 * @version 1.0.0
 * @copyright Copyright (c) www.baidu.com
 */
require_once dirname ( __FILE__ ) . '/../../lib/BaseScript.php';
init_script_env ( 'hottopic' );
class ExpirePostTopic extends Lib_BaseScript {
	const PER_GET_ITEM_NUM = 100;
	public function process(){
		$count = 0;
		$start = 0;
		while(true){
			$arrPara = array(
				'app_name' => Util_Hottopic_Const::POST_TOPIC_WORDLIST_APP,
				'wordlist_name' => Util_Hottopic_Const::POST_TOPIC_WORDLIST_NAME,
				'if_effective' => 0,
				'start' => $start,
				'offset' => self::PER_GET_ITEM_NUM,
			);
			$arrOutput =  Tieba_Service::call('wordlist','queryWLItem',$arrPara);
			$start = $start + self::PER_GET_ITEM_NUM;
			if ( isset($arrOutput['errno']) && (0 == intval($arrOutput['errno'])) ) {
				if(0 == count($arrOutput['ret'])){
					break;//已取出所有数据
				}
				foreach($arrOutput['ret']  as $arrTmp){
					//处理每个词条
					$this->_handlePerItem($arrTmp);
					$count ++;
					usleep(10000);
				}
			} else {
				Bingo_Log::warning(__FUNCTION__." call service wordlist failed : queryWLItem , input =".serialize($arrPara)." output =".serialize($arrOutput));
				continue;
			}
		}
		var_dump("total deal num[$count]");
		return true;
	}
	
	protected function _handlePerItem($arrInput){

		//拿到这条item的信息
		//判断下是否过期，如果过期了就删掉
		//记日志
		$intExpiredTime = $arrInput['expired_time'];
		if(true == $this->_isExpire($intExpiredTime)){
			$del = $this->_delItem($arrInput['key']);
			if($del){
				var_dump("del key success:".$arrInput['key']);
			}else{
				var_dump("del key fail:".$arrInput['key']);
			}
		}
		return false;
	}
	
	protected function _delItem($strKey){
		$arrInputPara = array(
					'key'                 => $strKey,
					'op_username'         => 'fengzhen_script',
					'app_name'            => Util_Hottopic_Const::POST_TOPIC_WORDLIST_APP,
					'wordlist_name'       => Util_Hottopic_Const::POST_TOPIC_WORDLIST_NAME,
					'app_token' 		  => Util_Hottopic_Const::POST_TOPIC_APP_TOKEN,
		);
		$arrInput = array(
			'wl_item_info' => $arrInputPara,
		);
		$arrOutput =  Tieba_Service::call('wordlist','delWLItem',$arrInput);
		if(intval($arrOutput['errno']) !== Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning(__FUNCTION__." call service failed : delWLItem , input =".serialize($arrInput)." output =".serialize($arrOutput));
			return false;
		}
		return true;
	}
	
	protected function _isExpire($intExpiredTime){
		if(0 == $intExpiredTime){
			return false;//没过期
		}
		if($intExpiredTime >= time()){
			return false;//没过期
		}
		return true;
	}
	
}
$obj = new ExpirePostTopic();
$obj -> process();
exit(0);