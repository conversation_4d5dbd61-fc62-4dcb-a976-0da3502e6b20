<?php
/**
 * 
 * 发送ueg核心数据
 * 每天运行一次
 * <AUTHOR>
 */
require_once dirname ( __FILE__ ) . '/../../lib/BaseScript.php';
init_script_env ( 'dataplatform' );
define ( 'ORP_PATH', ROOT_PATH );
if (! defined ( 'UEGMIS_DATA_PATH' )) {
    define ( 'UEGMIS_DATA_PATH', ORP_PATH . '/data/app/uegmis' );
}

define ( 'NAME_SCRIPT', 'Scripts_Dataplatform_SendEmail' );

Bingo_Log::init ( array (
    LOG => array (
        'file' => dirname ( __FILE__ ) . '/../../../../log/app/uegmis/' . NAME_SCRIPT . '.log',
        'level' => 0x04 | 0x02 
    ) 
), LOG );

class Scripts_Dataplatform_SendEmail extends Lib_BaseScript {
    public static function send() {
        $etime = mktime ( 0, 0, 0, date ( "m" ), date ( "d" ), date ( "Y" ) ); // 今天;
        $stime = mktime ( 0, 0, 0, date ( "m" ), date ( "d" ) - 2, date ( "Y" ) ); // 前天;
        $mtime = mktime ( 0, 0, 0, date ( "m" ), date ( "d" ) - 1, date ( "Y" ) );
        $tables = array (
            '删帖' => array (
                'tb_name' => 'tb_delete',
                'col_name' => 'USER所有' 
            ),
	    '封禁' => array('tb_name' => 'tb_lock', 'col_name' => 'LOCK_REASON所有'),
	    '封禁_系统' => array('tb_name' => 'tb_lock', 'col_name' => '策略'),
	    '封禁_吧务' => array('tb_name' => 'tb_lock', 'col_name' => '吧务'),
	    /*
            '封禁' => array (
                'col_name' => 'total' 
            ),
            '封禁_系统' => array (
                'col_name' => '1' 
            ),
            '封禁_吧务' => array (
                'col_name' => '2' 
            ),
	    */
            '验证码量' => array (
                'tb_name' => 'tb_vcode',
                'col_name' => 'CHANNEL所有' 
            ),
            '申诉量' => array (
                'tb_name' => 'tb_appeal',
                'col_name' => '人' 
            ) 
        );
        $tm1 = "昨日(" . date ( "Y-m-d", $mtime ) . ")";
        $tm2 = "前天(" . date ( "Y-m-d", $stime ) . ")";
        $ret = array ();
        $mail_content = "hello~以下为UEG数据平台核心数据<br><br><table border='0' cellpadding='3' cellspacing='1' width='100%' align='center' style='background-color: #b9d8f3;'><tr style='text-align: center;'><td></td><td>$tm2</td><td>$tm1</td><td>同比变化</td></tr>";
//        $lock_thedaybeforeyesterday = self::getLockFromDB ( $stime );
//        $lock_yesterday = self::getLockFromDB ( $mtime );
// 	$appeal_yesterday = self::getAppealFromDB($mtime);
 //       $appeal_thedaybeforeyesterday = self::getAppealFromDB($stime);
//        echo "lock info yesterday:";
 //       var_dump ( $lock_yesterday );
//        echo "lock info thedaybeforeyesterday:";
//        var_dump ( $lock_thedaybeforeyesterday );
        foreach ( $tables as $key => $param ) {
            $select ['stime'] = $stime;
            $select ['etime'] = $etime;
            $select ['tb_name'] = $param ['tb_name'];
            $select ['key_value'] = $param ['col_name'];
        /*    if (strstr ( $key, '封禁' )) {
                $thedaybeforeyesterday = ( int ) $lock_thedaybeforeyesterday [$param ['col_name']];
                $yesterday = ( int ) $lock_yesterday [$param ['col_name']];
            }
            else if(strstr($key, '申诉量'))
            {
                $thedaybeforeyesterday = (int)$appeal_thedaybeforeyesterday;
                $yesterday = (int)$appeal_yesterday;
            } 
	    else {*/
                $data = Dl_Dataplatform_Dataplatform::select ( $select );
                $thedaybeforeyesterday = ( int ) $data [0] ['cnt']; // 前天数据;
                $yesterday = ( int ) $data [1] ['cnt']; // 昨天数据;
        /*    }*/
            $symbal = $yesterday > $thedaybeforeyesterday ? "<font color='red'>+</font>" : ($yesterday == $thedaybeforeyesterday ? '=' : "<font color='green'>-</font>");
            $change = $thedaybeforeyesterday == 0 ? 0 : (abs ( $yesterday - $thedaybeforeyesterday ) / $thedaybeforeyesterday);
            $change = sprintf ( "%.2f", ($change * 100) );
            $change = "<font color='black'>" . $change . "%</font>";
            $cols = "<tr style='text-align: center; COLOR: #0076C8; BACKGROUND-COLOR: white; font-weight: bold'><td>" . $key . "</td><td>" . $thedaybeforeyesterday . "</td><td>" . $yesterday . "</td><td>" . $symbal . $change . "</td></tr>";
            $mail_content .= $cols;
        }
        $mail_content .= "</table>";
        $mail = new Lib_SendMail ();
        $mail->setTo ( array (
                  '<EMAIL>',
	) );
        $mail->setFrom ( "<EMAIL>" );
        $mail->setCharset ( "utf-8" );
        $mail->setSubject ( "UEG数据平台" . " " . date ( "Y-m-d" ) );
        $mail->setHTML ( $mail_content );
        $mail->setText ( $mail_content );
        $ret = $mail->send ();
        if ($ret == false) {
            echo "send mail" . $mail_content . "fail";
        } else {
            echo "send mail ok";
        }
    }
    public static function getLockFromDB($start_time) {
	$con = mysql_connect('cq01-dba-forum-userstate-99.cq01:3100','zengfanhua','3n4ZeQ4BLU');
	if (! $con) {
            echo "connect db fail";
            return;
        }
	mysql_select_db('forum_userstate',$con);
        $res = array ();
        $sum = 0;
	$data_name = array(
        rand(0,10),rand(11,20),rand(21,30),rand(31,40),rand(41,50),rand(51,60),rand(61,70),rand(71,80),rand(81,90),rand(91,100),rand(101,127)
        );
	 $num_array = array('1' => array(),'2' => array(), '3'=>array());
	foreach($data_name as $i){ 
	   $table = 'user_state_log_' . $i;
           $sql = 'select count(distinct(user_id)) ,opgroup from '.$table.' where act_type = 0 and start_time>='.$start_time.' and start_time<='.($start_time + 86400).' group by opgroup;';
        $result = mysql_query($sql,$con);
        if($result==false)
	{
		echo "select fail ".$table;
	}
	else
	{
		  echo "select ok ".$table;
	}
	while($row = mysql_fetch_row($result)){
           	$res[$row[1]] += $row[0];
		array_push($num_array[$row[1]],$row[0]);
	} 

        }
	var_dump($num_array);
	// 去掉最小值和最大值;
	foreach($num_array as $key => $var)
	{
		$max[$key] = max($var);
		$min[$key] = min($var);
	}
	$sum = 0;
	$count = count($data_name) -2;
    	foreach($res as $key => $var)
    	{
        	$res[$key] = ($var-$max[$key]-$min[$key]) / $count * 128;
        	$sum += $res[$key];
    	}
    	$res['total'] = $sum;
        return $res;
    }
    public static function getAppealFromDB($start_time){
        $con =  mysql_connect('yf-dba-forum-light-99.yf01:5550','zengfanhua','3n4ZeQ4BLU');
        if (! $con) {
            echo "connect db fail";
            return;
        }
        echo "connect ok";
        mysql_select_db('forum_mis_pmc',$con);
        $end_time = $start_time + 86400;
        for($i = 0; $i < 128; $i++)
        {
            $TABLE = "user_apeal".$i;
            $sql = "select count(distinct(apeal_uid))  from $TABLE where apeal_time>=$start_time and apeal_time<$end_time";
            $result = mysql_query($sql,$con);
            while($row = mysql_fetch_row($result)){
            var_dump($row);
            $sum += $row[0];
            }
        }
        return $sum;
    }	
    public function process() {
    }
}
Scripts_Dataplatform_SendEmail::send ();
?>

