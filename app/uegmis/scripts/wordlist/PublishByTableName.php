<?php
require_once dirname ( __FILE__ ) . '/../../lib/BaseScript.php';
init_script_env ( 'wordlist' );
define('ORP_PATH', ROOT_PATH);
class Scripts_Wordlist_Publish extends Lib_BaseScript {
	/*
	 * 发送到 贴吧服务器的词表,不支持发送
	 */
	protected static $pushToTiebaServer = array
	(
		'confilter_mis_wordlist_createanti',
		'confilter_mis_wordlist_createcontain',
		'confilter_mis_wordlist_createexact',
		'confilter_search_filter',
		'confilter_search_unfilter',
	);
	protected static $pushConf = array(
		array(
			'dest_path' => '',
			'noah_id' => 20294,
			'token' => 'dcf51e0b4ddb4d40b60300f1422b9e91',
		),
		array(
			'dest_path' => '',
			'noah_id' => 19277,
			'token' => '3dc56702b4874db58ae08684f45c8f3f',
		),
		array(
			'dest_path' => '',
			'noah_id' => 19279,
			'token' => '4336acec6481497fb4305366731d88d5',
		),
		
	);
	const NOAH_TOKEN = '0950c26848c344ceb341ace7e30faf0e';
	const NOAH_ID = 5664;
	const DATA_FORMAT_ONE_ITEM_PER_LINE = 'one_item_per_line';
    const DATA_FORMAT_TWO_ITEM_PER_LINE = 'two_item_per_line';
	protected $tableName = '';//要发送的词表名（5代词表显示的词表名）
	protected $arrPushWordlist = array();
	protected static $appName = 'confilter';
	protected static $pushType = 'noah';
	protected static $opName = 'PublishByTableName';
	const MIN_PUSH_TIME_BETWEEN = 300;//发送的最小时间间隔
	protected static $logFileName = 'push_log';
	public function __construct($tableInfo){
		//$this->tableName = $tableName;
//		if(in_array($this->tableName, self::$pushToTiebaServer)){
//			Bingo_Log::warning("can not publish table[$tableName]");
//			echo "can not publish table[$tableName]";
//			exit(0);
//		}
//$tableInfo   :    词表名:词条类型（单词or双词）cofilter_aaaa:1,confilter_bbb:2
		if(empty($tableInfo)){
			Bingo_Log::warning("tablename is empty");
			echo "tablename is empty";
			exit(255);
		}
		$this->arrPushWordlist = explode(',',$tableInfo);
		return true;
	}
	public function process() {
//		$res = self::_checkIfNeedPublish();
//		if(false === $res){
//			echo "_checkIfNeedPublish false";
//			exit(0);
//		}
		$arrFailWordlist = array();
		$arrFilePath = array();
		foreach($this->arrPushWordlist as $strTable){
			$res = $this->createFile($strTable);
			if(false === $res){
				sleep(10);
				$res = $this->createFile($strTable);
				if(false === $res){
					$arrFailWordlist[] = $strTable;
					continue;
				}
			}
			if(!empty($res['filePath'])){
				$arrFilePath[] = $res['filePath'];
			}
		}
		if(!empty($arrFilePath)){
			$fileDir = dirname($arrFilePath[0]);
            if (!$this->sendFile($fileDir, $arrFilePath)) {
                Bingo_Log::warning('ERR[can not send wordlist file] RES[' . serialize($fileDir) . ']');
            }
		}
		echo "push table[".$this->tableName . "] success!";
		exit(0);
		
	}
	protected function sendFile($fileDir,$files){
			
			$tar = $this->makeFullTarForConfilter($fileDir, $files);
            $filePath = dirname($tar);
            $fileName = basename($tar);
            $fileNameWithFullPath = $filePath . '/' . $fileName;
            Bingo_Log::notice('FILEIN[' . serialize($fileNameWithFullPath) . '] FILEPATH_TAR[' . serialize($filePath) . '] FILENAME_TAR[' . serialize($fileName) . ']');
            $arrScp = array(
                'scpConf' => array(
                    'noah' => array(
                        '0' => array(
                            'noah_id' => self::$pushConf[0]['noah_id'],
                            'token' => self::$pushConf[0]['token'],
                            'dest_path' => '',
                            'after_cmd' => array('ignore_after_cmd'),
                        ),
                    ),
                ),
                'sendInfo' => array(
                    'pusher' => self::$opName,
                    'wordlist_name' => '', // default
                    'app_name' => self::$appName,
                ),
            );

            $storage = new Orp_Storage("yKz3hBQv8DhtpGRFj4v30BNRc8wLBSpv", 'orp');

            $realPath = $storage->getLocalRealPath($fileNameWithFullPath);
            $filePath = dirname($realPath);
            $filePath = '/home/<USER>/../../' . $filePath; // for noah bscp work@xxxx  , avoid matrix@xxx

            $xscp = new Mislib_Scp_Xscp($filePath);
            $inputSend = array(
                '0' => $fileName,
            );
            $outputSend = $xscp->send($inputSend, $arrScp);
            $logFileFullPath = $fileDir . '/' . self::$logFileName;
            if($outputSend['noah'][0]['errno'] > 0){//发布失败
            	$arrScp = array(
	                'scpConf' => array(
	                    'noah' => array(
	                        '0' => array(
	                            'noah_id' => self::$pushConf[1]['noah_id'],
	                            'token' => self::$pushConf[1]['token'],
	                            'dest_path' => '',
	                            'after_cmd' => array('ignore_after_cmd'),
	                        ),
	                    ),
	                ),
	                'sendInfo' => array(
	                    'pusher' => self::$opName,
	                    'wordlist_name' => '', // default
	                    'app_name' => self::$appName,
	                ),
            	);
            	$outputSend = $xscp->send($inputSend, $arrScp);
            }
            $strOut = serialize($outputSend);
			file_put_contents ( $logFileFullPath, "push_out[$strOut]\n", FILE_APPEND );
        	Bingo_Log::notice('ERR[] CALL[xscp->send] RES[' . serialize($outputSend) . '] REQ[' . serialize($inputSend) . '][' . serialize($arrScp) . ']');
        	return true;
	}
	
	protected function makeFullTarForConfilter($fileDirWithFullPath, $filter = array())
    {
        $strDataPath = $fileDirWithFullPath ;
        $tmpDirname = 'tmp/tmp_'.date('YmdHis');
        $confliter_forumDirname = "confilter-forum";
        $confilterDirname    = $tmpDirname."/$confliter_forumDirname/data/confilter";
        $logFullPath = $strDataPath . '/' . self::$logFileName;
        system("mkdir -p $strDataPath/$confilterDirname",$ret);
        if($ret){
            Bingo_log::warning("failed to do cmd:"."mkdir -p $strDataPath/$confilterDirname");
            file_put_contents ( $logFullPath, "makeFullTarForConfilter[failed to do cmd:mkdir -p $strDataPath/$confilterDirname]\n", FILE_APPEND );
            return false;
        }
        if (empty($filter)) {
            system("cp -r $strDataPath/* $strDataPath/$confilterDirname/", $ret);
            if ($ret) {
                Bingo_log::warning("failed to do cmd:" . "cp $strDataPath $strDataPath/$confilterDirname/");
                file_put_contents ( $logFullPath, "makeFullTarForConfilter[failed to do cmd:cp $strDataPath $strDataPath/$confilterDirname/]\n", FILE_APPEND );
                return false;
            }
        }
        else{
            foreach($filter as $luckyOne){
            	$strluckPath = dirname($luckyOne);
                $luckyOne = basename($luckyOne);
                system("cp -r $strluckPath/$luckyOne $strDataPath/$confilterDirname/", $ret);
                if($ret){
                    Bingo_log::warning("failed to do cmd:"."cp $strluckPath/$luckyOne $strDataPath/$confilterDirname/");
                    file_put_contents ( $logFullPath, "makeFullTarForConfilter[failed to do cmd:cp $strluckPath/$luckyOne $strDataPath/$confilterDirname/]\n", FILE_APPEND );
                    return false;
                }
            }
        }
        $strTarName = 'confilter-forum.tar.gz';
        $strTarFilename = "$tmpDirname/$strTarName";
        system("cd $strDataPath/$tmpDirname/ && tar czf $strDataPath/$strTarFilename $confliter_forumDirname/",$ret);
        if($ret){
            Bingo_log::warning("failed to do cmd:"."cd $strDataPath/$tmpDirname/ && tar czf $strDataPath/$strTarFilename $confliter_forumDirname/");
            file_put_contents ( $logFullPath, "makeFullTarForConfilter[failed to do cmd:cd $strDataPath/$tmpDirname/ && tar czf $strDataPath/$strTarFilename $confliter_forumDirname/]\n", FILE_APPEND );
            return false;
        }
        return $strDataPath . '/' . $strTarFilename;
    }
	
	protected function createFile($wordListName){
		$wordlistFilePath = ROOT_PATH.'/data/app/uegmis/wordlist/';
		$newfile = new Mislib_Wordfile_Newfile($wordlistFilePath);
		$arrData = array(
			'app_name' => self::$appName,
	        'wordlist_name' => Util_Wordlist_Word::getWordListFilename($wordListName),
		);
		$res = $newfile->newFile($arrData);
		if($res === false){
			Bingo_Log::warning ( "Mislib_Wordfile_Newfile:: newFile fail. [" . serialize ( $arrData ) . "]");
			return false;
		}
		return $res;
	}
	protected function _checkIfNeedPublish(){
		$arrData = array(
			'query_info' => array(
		        'app_name' => self::$appName,
				'wordlist_name' => Util_Wordlist_Word::getWordListFilename($this->tableName),
				'dept' => Util_Wordlist_Const::DEPT_UEG,
	   	 	),
		);
		$res = Service_Wordlist_Wordlist::getWl($arrData);
		if($res === false){
			Bingo_Log::warning($this->tableName . "getWl failed in _checkIfNeedPublish:");
			echo $this->tableName . "getWl failed in _checkIfNeedPublish";
			exit(255);
		}
		$arrTable = $res['ret'][0];
		if(empty($arrTable)){
			Bingo_Log::warning($this->tableName . "no table");
			echo $this->tableName . "get no table";
			exit(255);
		}
		$fileName = $arrTable['file_name'];
		$last_update_item_time = intval($arrTable['last_update_item_time']);
		$custom = $arrTable['custom'];
		$ifModify = false;
		if(empty($custom)){
			$arrCustom = array();
			Bingo_Log::warning("empty custom,table:".$fileName);
			$arrCustom[self::$pushType][] = array('noah_id'=>self::NOAH_ID,'pushing_time'=>time());
			$ifModify = true;
		}else{
			$arrCustom = unserialize($custom);
			if($arrCustom == false){
				Bingo_Log::warning("unserialize custom fail or arrCustom[noah] unset,table:".$fileName);
				return false;
			}
			if(!isset($arrCustom[self::$pushType]) || empty($arrCustom[self::$pushType])){
				$arrCustom[self::$pushType][] = array('noah_id'=>self::NOAH_ID,'pushing_time'=>time());
				$ifModify = true;
			}
			foreach($arrCustom[self::$pushType] as $key => $conf){
				if(intval($conf['noah_id']) === self::NOAH_ID){
					$lastPushTime = intval($conf['push_time']);
					$pushIngTime = isset($conf['pushing_time'])?intval($conf['pushing_time']) : 0;
					if($lastPushTime >= $last_update_item_time){//最后成功发布时间大于等于 词表最后更新时间，不用再发送
						Bingo_Log::warning("no need publish:".$fileName);
						return false;
					}
					$long = time() - $pushIngTime;
					if($long <= self::MIN_PUSH_TIME_BETWEEN ){
						Bingo_Log::warning("you push [$fileName] too manay times in :".self::MIN_PUSH_TIME_BETWEEN . ",pushingtime:".date('y-m-d H:i:s',$pushIngTime));
						return false;
					}
					$arrCustom[self::$pushType][$key]['pushing_time'] = time();
					$ifModify = true;
					break;
				}		
			}		
		}
		if($ifModify){
			$updateArr = array(
	            'wl_info'=>array(
	                'app_name'=>self::$appName,
	                'wordlist_name'=>Util_Wordlist_Word::getWordListFilename($this->tableName),//no app prefix
	                'op_username'=> self::$opName,
	                'dept'=>Util_Wordlist_Const::DEPT_UEG,
	                'custom'=>serialize($arrCustom),
	                'app_token'=>Util_Wordlist_Word::getAppTokenByAppName(self::$appName),
	            )      
	        );
	        $res = Service_Wordlist_Wordlist::modifyWL($updateArr);
	        if($res === false){
	        	Bingo_Log::warning("update custom failed,input:".serialize($updateArr));
	        	return false;
	        }
	        return true;
		}
		return false;
	}
	protected function _getNoahIdSchedule($arrInput){
		$intNoahId = $arrInput['noah_id'];
		$xscp = new Mislib_Scp_Xscp('');
		$intRetry = 3;//retry 3 times
		for ($i = 1; $i <= $intRetry; $i++) {
			$noah_status = $xscp->getNoahScheduleStatus($intNoahId);
			if($noah_status == Util_Wordlist_Const::PUSH_STATUS_FINISH){
				break;
			}
			sleep(1);
		}		
		if($noah_status === false){
			Bingo_Log::warning("Mislib_Scp_Xscp::getNoahScheduleStatus failed,noahid:".$intNoahId);
			return rawurlencode("get Noah Schedule Status fail $intNoahId");
		}
		switch($noah_status){
			case Util_Wordlist_Const::PUSH_STATUS_UNPUSH:
				return rawurlencode("排队等待发布，请稍等 .....");
				break;
			case Util_Wordlist_Const::PUSH_STATUS_PUSHING:
				return rawurlencode("词表发布中 .....");
				break;
			case Util_Wordlist_Const::PUSH_STATUS_FINISH:
				$res = self::_modifyPushTime($arrInput);
				if(!$res){
					Bingo_Log::warning("_modifyPushTime failed in _getNoahIdSchedule,".serialize($arrInput));
				}
				return rawurlencode("success:发布已完成 .....");
				break;
			default:
				Bingo_Log::warning("unknow status:".$noah_status);
				return rawurlencode("fail:发布未完成，请找rd确认！");
				break;
		}
		
	}
	
}
$scriptTask = new Scripts_Wordlist_Publish ($argv [1]);
$scriptTask->process ();