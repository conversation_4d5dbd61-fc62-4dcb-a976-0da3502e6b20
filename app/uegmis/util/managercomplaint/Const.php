<?php

class Util_Managercomplaint_Const
{
    //吧主是否被撤销
    const PASS_MANAGER = 3;
    const DELETE_MANAGER = 2;
    const UN_PROCESS = 4;
    const ALARM_MANAGER = 5;
    //投诉类型 存db
    const COMPLAINT_TYPE_ELSE = 0;//其他
    CONST COMPLAINT_TYPE_LONG_NO_LOGIN = 1;//长期未登陆
    CONST COMPLAINT_TYPE_ILLEGAL_OPERATE = 2;//违规操作
    CONST COMPLAINT_TYPE_SEX = 3;//色情反动
    CONST COMPLAINT_TYPE_ILLEGAL_DELPOST = 4;
    CONST COMPLAINT_TYPE_ILLEGAL_ADDPOST = 5;
    CONST COMPLAINT_TYPE_ILLEGAL_INTEREST = 6;
    CONST COMPLAINT_TYPE_ILLEGAL_POST_INFO = 7;

    //是否特殊吧主 存db
    CONST IS_SPECIAL_MANAGER_UNCONFIRM = 0;//待确认
    CONST IS_SPECIAL_MANAGER_NOMAL = 1;//普通吧主
    CONST IS_SPECIAL_MANAGER_OFFICIAL = 2;//官方吧主
    CONST IS_SPECIAL_MANAGER_BRAND =3; //品牌吧吧主

    CONST IS_OFFICIAL_MANAGER = 1;//官方吧主
    CONST IS_NOT_OFFICIAL_MANAGER = 0;//非官方吧主（含待确定的）

    //审核状态
    CONST COMPLAINT_STATUS_UN_AUDIT = 0;//待审核  存db
    CONST COMPLAINT_STATUS_DONE = 255;//已审核
    CONST COMPLAINT_STATUS_PASS_MANAGER = 1;//不撤销吧主  存db
    CONST COMPLAINT_STATUS_DELETE_MANAGER = 2;//撤销吧主  存db
    CONST COMPLAINT_STATUS_ALARM = 3;//警告 不撤销吧主 存db


    CONST TPL_TYPE_PASS = 0;//通过类模板 存db
    CONST TPL_TYPE_DELETE = 1;//撤销类模板 存db
    CONST TPL_TYPE_ALARM = 2;//警告类模板 存db

    CONST TPL_STATUS_USED = 0;//可用 存db
    CONST TPL_STATUS_DELETE = 1;//已删除，不可用 存db

    CONST IS_NOT_AUTO_AUDIT = 0;
    CONST IS_AUTO_AUDIT = 1;//自动脚本处理

    public static $opAction = array(
        'jump' => self::COMPLAINT_STATUS_UN_AUDIT,
        'pass' => self::COMPLAINT_STATUS_PASS_MANAGER,
        'delete' => self::COMPLAINT_STATUS_DELETE_MANAGER,
        'alarm' => self::COMPLAINT_STATUS_ALARM,
    );

    public static $officialWordTableInConfilter = 'tousu_special_uo_word';

    public static $deleteOpName = '吧主投诉后台mis';

    public static $tplType = array(
        'pass' => self::TPL_TYPE_PASS,
        'delete' => self::TPL_TYPE_DELETE,
        'alarm' => self::TPL_TYPE_ALARM,
    );
    public static $auditStatus = array(
        'undo',//未处理
        'done',//已处理
        'all',//全部
    );
    public static $complaintType = array(
        'nologin' => self::COMPLAINT_TYPE_LONG_NO_LOGIN,
        'illegal' => self::COMPLAINT_TYPE_ILLEGAL_OPERATE,
        'sex' => self::COMPLAINT_TYPE_SEX,
        'else' => self::COMPLAINT_TYPE_ELSE,
        'illegal_delpost' => self::COMPLAINT_TYPE_ILLEGAL_DELPOST,
        'illegal_addpost'=> self::COMPLAINT_TYPE_ILLEGAL_ADDPOST,
        'illegal_interest'=> self::COMPLAINT_TYPE_ILLEGAL_INTEREST,
        'illegal_post_info' => self::COMPLAINT_TYPE_ILLEGAL_POST_INFO,
    );

    public static $complaintTypeReson = array(
        self::COMPLAINT_TYPE_LONG_NO_LOGIN =>'nologin',
        self::COMPLAINT_TYPE_ILLEGAL_OPERATE => 'illegal',
        self::COMPLAINT_TYPE_SEX => 'sex',
        self::COMPLAINT_TYPE_ELSE => 'else',
        self::COMPLAINT_TYPE_ILLEGAL_DELPOST => 'illegal_delpost',
        self::COMPLAINT_TYPE_ILLEGAL_ADDPOST => 'illegal_addpost',
        self::COMPLAINT_TYPE_ILLEGAL_INTEREST => 'illegal_interest',
        self::COMPLAINT_TYPE_ILLEGAL_POST_INFO => 'illegal_post_info',
    );

    public static $managerType = array(
        'is_forum_assist',
        'is_forum_manager',
    );
    public static $forumRoleNameEngToChs = array(
        'manager' => '吧主',
        'assist' => '小吧主',
        'videoadmin' => '视频小编',
        'picadmin' => '图片小编',
        'publication' => '吧刊小编',
        'publication_editor' => '吧刊主编',
        'fourth_manager' => '第四吧主',
        'voiceadmin' => '语音小编',
        'profession_manager' => '职业吧主',
    );
    public static $specialManager = array(
        self::IS_SPECIAL_MANAGER_UNCONFIRM,
        self::IS_SPECIAL_MANAGER_NOMAL,
        self::IS_SPECIAL_MANAGER_OFFICIAL,
	self::IS_SPECIAL_MANAGER_BRAND,
    );
    //自动处理
    //这个是给投诉人的模板
    public static $tplLongNoLogin = "亲爱的吧友，经百度贴吧管理组核实确认，您所举报的吧主因违反《百度贴吧-吧主协议》相关规定,已被撤销吧主职务。感谢您对百度贴吧的关注与支持。";
    public static $remarkLongNoLogin = "长期未登录";
    public static $remarkPassManager = "自动处理";
    //给被撤吧主的模板
    public static $tplLongNoLogin4Manager = "亲爱的吧友您好，由于近期接到贴吧用户对您的投诉，经百度贴吧管理组核实，您在吧内确实存在违反吧主协议的相关操作或未达到吧主协议的相关标准，现已对您的吧主职务进行撤销处理，详情请见贴吧吧主小管家在吧内发布的撤销公告，感谢您对贴吧的支持，如对此处理有异议，请至贴吧反馈中心进行申诉，链接 http://tieba.baidu.com/hermes/feedback\n百度贴吧管理组";
    public static $sysmsgTitle4Manager    = "吧主投诉撤销通知";
    //在吧里发公告的模板
    //no html in the Text
    public static $title4LongNoLogin = "[公告]关于撤销 %s 吧主管理权限的说明";
    public static $content4LongNoLogin = "接到用户举报，经核实吧主 %s 长期未对贴吧进行管理及发言，无法在建设 %s吧 内容上、言论导向上发挥应有的模范带头作用，严重违反吧主协议，故撤销其吧主管理权限。如对此项决议有异议，请至贴吧反馈中心进行申诉，链接 <a target=\"_blank\" href=\"http://tieba.baidu.com/hermes/feedback\">http://tieba.baidu.com/hermes/feedback</a><br> \n百度贴吧管理组";
    public static $intAnnoucementUid = 167570067;//50007797;//for offline environment
    public static $strAnnoucementUname = "贴吧吧主小管家";//'四九居士'; //for  offline environment
    public static $intAddtopUid =  1565127810;//1050300015;//for offline environment//915253643;//
    public static $strAddtopUname = "贴吧平台化01";//'小猪'; //for  offline environment//"狂三小天使";//

    public static $tplOfficialManager = "亲爱的吧友，经百度贴吧管理组调查，您所举报的吧主在日常管理中确实存在一些不足，我们会在近期内加强对吧主的监督和引导，若吧主未能改善现状，我们将按《吧主协议》规定进行处理。同时，也欢迎您与我们一同对吧主进行监督，感谢您对百度贴吧的关注与支持，谢谢。";
    public static $tplCancelAppeal4Assist = "亲爱的吧友，如您投诉小吧主，请先联系吧主解决问题，吧主有义务对小吧主进行管理，感谢您的配合";
    public static $remarkOfficialManager = "官方吧吧主";

    public static $tplBrandManager = "亲爱的吧友，经百度贴吧管理组核实，您所举报的吧主并未违反《百度贴吧-吧主协议》相关规定，故不予处理。感谢您的反馈，谢谢。";
    public static $remarkBrandManager = "品牌吧吧主";

    //警告 给吧主发消息的模板
    public static $tplAlarm4Manager = "亲爱的吧主，您好！由于近期接到贴吧用户对您的投诉，经百度贴吧管理组核实您或您的吧务团队，在吧内多次出现无故封禁用户或无故删帖行为。鉴于您在贴吧管理中做出的贡献，此次我们对您及吧务团队的行为做出警告，请您在今后的贴吧管理中进行改正，并加强吧务团队管理。如果此后管理员再次接到同类用户投诉并核查属实，将按照规定对您进行相应处理，谢谢！\n百度贴吧管理组";
    public static $sysmsgTitleAlarm4Manager    = "吧主投诉警告";


    const SYSMSG_CATEGORY_ID = 1;
    public static $sysmsgTitle = '吧主投诉处理结果';
    public static $sysmsgCallFrom = 'managercomplaint';

    const PROFESSION_MANAGER_OP_USER_ROLE = 2;
    const PROFESSION_MANAGER_CALL_FROM = 'ueg_managercomplaint';
    const FORUM_FESSIONAL_STATUS_PASS = 2;

    const NOLOGIN_FAIL_TYPE = 'complain_war_nologin';
}
