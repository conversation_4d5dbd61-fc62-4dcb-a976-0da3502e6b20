<?php
/**
 * 
 * 质检包锁定，防止多个管理员同时质检
 * <AUTHOR>
 * @since 2014-11-25
 */
class assignAction extends Lib_BaseAction {
	function init() {
		self::setUiAttr ( self::COMMIT_UI ); //设置UI的类型
		//基类初始化，初始化失败且未设置错误代码设置错误代码为参数设置错误
		if (false === parent::init ()) {
			if (0 === $this->_intErrno) {
				$this->_intErrno	= Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strError	= Tieba_Errcode::$codes [$this->_intErrno];
				Bingo_Log::warning ( $this->_strError );
			}
			$this->_jsonRet();
			return false;
		}
		return true;
	}
	protected function _process() { 
		$taskId = Bingo_Http_Request::getNoXssSafe('task_id');
		$type = Bingo_Http_Request::getNoXssSafe('type');
		if(empty($taskId) || !is_numeric($taskId) || !in_array($type, Util_Multiaudit_Const::$auditType)) {
			$this->_intErrno	= Tieba_Errcode::ERR_MO_PARAM_INVALID;
			$this->_strError	= Tieba_Errcode::$codes[$this->_intErrno];
			return $this->_jsonRet();
		}
		
		
		$arrInput = array(
			'task_id'		=> $taskId,
			'op_uid'    	=> $this->_arrUserInfo[self::USER_ID],
			'op_uname'  	=> $this->_arrUserInfo[self::USER_NAME],
			'op_time'   	=> time(),
			'type' => $type,
		);
		
		$ret = Service_Multiaudit_Quality::assignQuality($arrInput);
		$this->_intErrno	= $ret['errno'];
		$this->_strError	= $ret['errmsg'];
		if($this->_intErrno == 1) {
			$this->_strError	= '已经被质检';
		}
		return $this->_jsonRet();
		
	}
}