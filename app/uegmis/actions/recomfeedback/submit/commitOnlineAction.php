<?php

ini_set('memory_limit', '1024M');

/**
 * Created by PhpStorm.
 * User: zhaoshiya
 * Date: 2017/3/27
 * Time: 下午2:19
 */
class commitOnlineAction extends Lib_BaseAction {
    /**
     * @param empty
     * @return bool
     */
    function init() {
        self::setUiAttr(self::COMMIT_UI);//设置UI的类型
        //基类初始化，初始化失败且未设置错误代码设置错误代码为参数设置错误
        if (false === parent::init()) {
            if (0 === $this->_intErrno) {
                $this->_intErrno = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strError = Tieba_Errcode::$codes [$this->_intErrno];
                Bingo_Log::warning($this->_strError);
            }
        }
        return true;
    }

    /**
     * @param empty
     * @return
     */
    function _process() {
        $arrInput = array();
        $arrInput['op_uid'] = $this->_arrUserInfo[self::USER_ID];
        $arrInput['op_uname']  = $this->_arrUserInfo[self::USER_NAME];
        $ret = Service_Recomfeedback_Recomfeedback::commitOnline($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            $this->setErrno($ret['errno'], $ret['errmsg']);
        }
        return $this->_jsonRet();
    }


    /**
     * @param $errno
     * @param string $errmsg
     * @return
     */
    private function setErrno($errno, $errmsg = '') {
        if ($errmsg === '') {
            $errmsg = Tieba_Error::getErrmsg($errno);
        }
        $this->_intErrno = $errno;
        $this->_strError = $errmsg;
    }
}