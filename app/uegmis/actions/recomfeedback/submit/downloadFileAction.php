<?php

ini_set('memory_limit', '1024M');

/**
 * Created by PhpStorm.
 * User: liujiaxiang01
 * Date: 2017/10/19
 * Time: 17:18
 */
class downloadFileAction extends Lib_BaseAction {
    /**
     * @param empty
     * @return bool
     */
    function init() {
        self::setUiAttr(self::COMMIT_UI);//设置UI的类型
        //基类初始化，初始化失败且未设置错误代码设置错误代码为参数设置错误
        if (false === parent::init()) {
            if (0 === $this->_intErrno) {
                $this->_intErrno = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strError = Tieba_Errcode::$codes [$this->_intErrno];
                Bingo_Log::warning($this->_strError);
            }
        }
        return true;
    }

    /**
     * @param empty
     * @return
     */
    function _process() {

        $status = intval(Bingo_Http_Request::getNoXssSafe('status', -1));
        $contentType = intval(Bingo_Http_Request::getNoXssSafe('content_type', -1));
        $limit = intval(Bingo_Http_Request::getNoXssSafe('limit', Util_Recomfeedback_Const::ONLINE_LIMIT_MAX));

        $arrInput = array();
        $arrInput['limit'] = $limit;
        ($status !== -1) && $arrInput['status'] = $status;
        ($contentType !== -1) && $arrInput['content_type'] = $contentType;

        $arrInput['op_uid'] = $this->_arrUserInfo[self::USER_ID];
        $arrInput['op_uname']  = $this->_arrUserInfo[self::USER_NAME];
        $ret = Service_Recomfeedback_Recomfeedback::downloadOnlineData($arrInput);
        if(Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            $this->setErrno($ret['errno'], $ret['errmsg']);
            return $this->_jsonRet();
        }

        $content = '';
        $fileName = Util_Recomfeedback_Const::SUPPRESS_FORUM;
        $fileType = Bingo_Http_Request::get('file_type', 'excel');
        if ($fileType == 'excel') {
            $field = Bingo_Http_Request::get('field', '');
            $arrField = explode(',', $field);
            $content = Util_Recomfeedback_File::convertExcel($ret['data'], $arrField);
            $fileName .= '.xls';
        } else {
            $content = Util_Recomfeedback_File::formatData($ret['data']);
            $fileName .= '.txt';
        }

        header("Content-type: application/octet-stream");
        header('Content-Disposition: attachment; filename="' . $fileName . '"');
        header("Content-Length: ". strlen($content));
        echo $content;
    }


    /**
     * @param $errno
     * @param string $errmsg
     * @return
     */
    private function setErrno($errno, $errmsg = '') {
        if ($errmsg === '') {
            $errmsg = Tieba_Error::getErrmsg($errno);
        }
        $this->_intErrno = $errno;
        $this->_strError = $errmsg;
    }
}