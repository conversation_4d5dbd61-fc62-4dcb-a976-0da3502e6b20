<?php
/**
 *
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @abstract 群聊审核列表页
 * @date 2013-10-24
 * 
 */

class listAction extends Lib_BaseAction {
	
	protected static $_pageSize = 30;
	
	const PAGE_MODE_ADD		= 'add';
	const PAGE_MODE_UPDATE	= 'update';
	
	public function init() {
		self::setUiAttr(self::BROWSE_UI);

		if(false == parent::init()){
			if(0 === $this->_intErrorNo){
				$this->_intErrno  = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strError = Tieba_Errcode::$codes[$this->_intErrno];
				Bingo_Log::warning($this->_strError);
			}
		}
		$this->_strTemplate = 'groupaudit/groupaudit.php';
		return true;
	}
	
	public function _process() {
		$model	= Bingo_Http_Request::getNoXssSafe('model','add');
		$page	= Bingo_Http_Request::getNoXssSafe('pn',1);
		$ie     = Bingo_Http_Request::getNoXssSafe('ie','');
		$status = Bingo_Http_Request::getNoXssSafe('status', Util_Groupaudit_Const::OP_STATUS_UNDEAL);

		if( !is_numeric($page) || $page < 0 ){
			$page = 1;
		}
		if ( !in_array($model, array(self::PAGE_MODE_ADD, self::PAGE_MODE_UPDATE)) ) {
			$model = self::PAGE_MODE_ADD;
		}
		if (!in_array($status, array(Util_Groupaudit_Const::OP_STATUS_UNDEAL, Util_Groupaudit_Const::OP_STATUS_PASS))) {
			$status = Util_Groupaudit_Const::OP_STATUS_UNDEAL;
		}
		
		$totalSize = 0;
		$condition = array();
		if ( $model == self::PAGE_MODE_ADD ) {
			$condition = array('cmd_no'=>  Util_Groupaudit_Const::REPORT_TYPE_ADD);
		} else {
			$condition = array('cmd_no'=>  Util_Groupaudit_Const::REPORT_TYPE_UPDATE);
		}
		$condition['status'] = $status;
		
		$srvInput = array (
				'input' => Bingo_String::array2json ( $condition, Bingo_Encode::ENCODE_UTF8 ), 
				'format' => 'json' 
		);
		$dlGroupCountRes = Service_Groupaudit_Groupaudit::getGroupCountByCmdNo($srvInput);
		if ($dlGroupCountRes ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			$this->_intErrno  = $dlGroupCountRes ['errno'];
			$this->_strError = Tieba_Error::getErrmsg ( $this->_intErrno );
			$this->_viewRet();
			return false;
		}
		$totalSize = $dlGroupCountRes['ret'];
		$totalPage = ceil($totalSize/self::$_pageSize);
		if ( $page > $totalPage ) {
			$page = $totalPage;
		}
		
		$arrGroupList = array();
		if ($totalSize > 0) {
			$condition['offset'] = ($page - 1) * self::$_pageSize;
			$condition['count']  = self::$_pageSize;
			$srvInput = array (
				'input' => Bingo_String::array2json ( $condition, Bingo_Encode::ENCODE_UTF8 ), 
				'format' => 'json' 
			);
			$dlGroupList = Service_Groupaudit_Groupaudit::getGroupListByCmdNo($srvInput);
			if ($dlGroupList ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
				$this->_intErrno  = $dlGroupList ['errno'];
				$this->_strError = Tieba_Error::getErrmsg ( $this->_intErrno );
				$this->_viewRet();
				return false; 
			}
			$dlResult = $dlGroupList['ret'];
			if ( !empty($dlResult) && is_array($dlResult) ) {
				foreach ($dlResult as $row) {
					$group = array();
					$group['id']		= $row['group_db_id'];
					$group['g_id']		= $row['group_id'];
					$group['g_portrait']= $row['portrait'];
					$group['g_name']	= $row['group_name'];
					$group['g_info']	= $row['intro'];
					$group['c_time']	= date("Y-m-d H:i:s", $row['create_time']);
					$group['c_uname']	= $row['create_uname'];
					array_push($arrGroupList, $group);
				}
			}
		}
		
		$page = array(
					'pn' => intval($page),
					'tn' => intval($totalPage),
					'ps' => self::$_pageSize,
					'ts' => $totalSize
				);
		//Bingo_Page::assign('list', $arrGroupList);
		//Bingo_Page::assign('page', $page);
		$this->_arrTpl['list'] = $arrGroupList;
		$this->_arrTpl['page'] = $page;
		$this->_viewRet();
		return true;
	}
	
	
}


?>