<?php

/**
 * Created by PhpStorm.
 * User: baidu
 * Date: 15/11/2
 * Time: 下午6:50
 */
class getExtraInfoAction extends Lib_BaseAction {
    CONST MODULE = "forumsquare";

    /**
     * @param empty
     * @return bool
     */
    function init() {
        self::setUiAttr(self::BROWSE_UI); //设置UI的类型
        //基类初始化，初始化失败且未设置错误代码设置错误代码为参数设置错误
        if (false === parent::init()) {
            if (0 === $this->_intErrno) {
                $this->_intErrno = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strError = Tieba_Errcode::$codes[$this->_intErrno];
                Bingo_Log::warning($this->_strError);
                return false;
            }
        }
        return true;
    }

    /**
     * @param
     * @return
     */
    function _process() {
        $forumClassId = Bingo_Http_Request::get("forum_class_id", 0);
        if (!$forumClassId) {
            Bingo_Log::warning("forum_class_id is needed!");
            $this->_intErrno = Tieba_Errcode::ERR_PARAM_ERROR;
            $this->_strError = "missing param forum_class_id";
            return $this->_jsonRet();
        }
        $arrInput = array(
            "forum_class_id" => $forumClassId,
        );
        $ret = Tieba_Service::call(self::MODULE, "getForumExtra", $arrInput, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning("failed to call service getForumExtra. input: " . serialize($arrInput)
                . " output: " . serialize($ret) . __CLASS__);
            $this->_intErrno = $ret['errno'];
            $this->_strError = Tieba_Error::getUserMsg($this->_intErrno);
            return $this->_jsonRet();
        }
        $this->_arrTpl['data'] = $ret['ret'];
        return $this->_jsonRet();
    }
}