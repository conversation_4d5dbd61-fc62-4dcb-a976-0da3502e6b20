<?php
/**
 * 下线热帖
 * User: mi<PERSON><PERSON>qi<PERSON>
 * Date: 2017/4/11
 * Time: 上午11:33
 */
class offHotAction extends Lib_BaseAction {

    /**
     * 初始化
     * @param empty
     * @return bool
     */
    function init() {
        self::setUiAttr ( self::COMMIT_UI ); //设置UI的类型
        //基类初始化，初始化失败且未设置错误代码设置错误代码为参数设置错误
        if (false === parent::init()) {
            if (0 === $this->_intErrno) {
                $this->_intErrno = Tieba_Errcode::ERR_PARAM_ERROR;
                $this->_strError = Tieba_Errcode::$codes [$this->_intErrno];
                Bingo_Log::warning ( Util_Hotthread_Const::PRE_LOG . $this->_strError );
            }
            return false;
        }
        return true;
    }

    /**
     * @param
     * @return json
     */
    protected function _process() {
        $pushDate = Bingo_Http_Request::get('push_date', 0);
        $hotId    = Bingo_Http_Request::get('hot_ids', 0);
        $hotIdArr = explode(',', $hotId);

        if (empty($hotIdArr)) {
            $this->error(Tieba_Errcode::ERR_PARAM_ERROR, '请选择需要下线的帖子');
            return $this->_jsonRet();
        }
        if (empty($pushDate)) {
            $this->error(Tieba_Errcode::ERR_PARAM_ERROR, '请按照日期查找后下线');
            return $this->_jsonRet();
        }
        //构建参数
        $opUser = array(
            'user_id'   => $this->_arrUserInfo['user_id'],
            'user_name' => $this->_arrUserInfo['user_name'],
        );
        $arrInput = array(
            'hot_ids'   => $hotIdArr,
            'op_user'   => $opUser,
            'push_date' => $pushDate,
        );

        $retOffline = Service_Hotthread_Hotthread::offlineHotthread($arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $retOffline['errno']) {
            $this->error($retOffline['errno'], $retOffline['errmsg']);
            return $this->_jsonRet();
        }

        $this->_intErrno = Tieba_Errcode::ERR_SUCCESS;
        $this->_strError = '下线成功';
        return $this->_jsonRet();
    }

    /**
     * 设置错误号
     * @param $errno
     * @param string $errmsg
     */
    private function error($errno, $errmsg = '') {
        $this->_intErrno = $errno;
        $this->_strError = !empty($errmsg) ? $errmsg : Tieba_Error::getErrmsg($errno);
    }
}