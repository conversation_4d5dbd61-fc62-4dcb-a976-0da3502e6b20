<?php
/**
 * ===========================================
 * @desc: 
 * @author: fengzhen
 * @date: 2015-9-8
 * ===========================================
 * @version 1.0.0
 * @copyright Copyright (c) www.baidu.com
 */
class getRelateThreadAction extends Lib_BaseAction {
	const PAGE_SIZE = 10;
	function init() {
		self::setUiAttr ( self::BROWSE_UI ); //设置UI的类型
		//基类初始化，初始化失败且未设置错误代码设置错误代码为参数设置错误
		if (false === parent::init ()) {
			if (0 === $this->_intErrno) {
				$this->_intErrno = Tieba_Errcode::ERR_PARAM_ERROR;
				$this->_strError = Tieba_Errcode::$codes [$this->_intErrno];
				Bingo_Log::warning ( $this->_strError );
			}
		}
		return true;
	}
	protected function _process() {
		$page = (int) Bingo_Http_Request::getNoXssSafe ( 'page', 1 );
		$topicId = (int)Bingo_Http_Request::getNoXssSafe('topic_id', 0);
        $threadId = (int)Bingo_Http_Request::getNoXssSafe('thread_id', 0);
        $sortType = (int)Bingo_Http_Request::getNoXssSafe('sort_type', 1);
		if(empty($topicId)){
			Bingo_Log::warning("invalid topic_id");
			$this->_intErrno = Tieba_Errcode::ERR_PARAM_ERROR;
			$this->_strError = 'input topic_id params error!';
			return $this->_jsonRet();
		}
		$arrInput = array(
   			'req' => array(
   				'topic_id' => $topicId,
    			'offset' => $page > 1? ($page-1) * self::PAGE_SIZE : 0,
				'num' => self::PAGE_SIZE + 1,
                'thread_id' => $threadId,
                'sort_type' => $sortType
   			),
   		);
		$ret = Tieba_Service::call('hottopic', 'getTopicRelateThread', $arrInput, null, null, 'post', 'php', 'utf-8');
		if ($ret === false || (isset($ret['errno']) && intval($ret['errno']) !== Tieba_Errcode::ERR_SUCCESS)) {
			Bingo_Log::warning(__FUNCTION__." call service hottopic failed : getTopicRelateThread , input =".serialize($arrInput)." output =".serialize($ret));
			$this->_intErrno = isset($ret['errno'])?$ret['errno']:Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
			$this->_strError = isset($ret['errmsg'])?$ret['errmsg'].implode(',', $ret['ret']):"unknown errno";
			return $this->_jsonRet();
		}
		$res = $ret['ret'];
        if(empty($res)){
        	Bingo_Log::warning($topicId." getTopicRelateThread get no data.");
        	return $this->_jsonRet();
        }
        $arrTids = array();
        $arrPids = array();
        $arrSource = array();
        $arrRes = array();
        foreach($res as $t){
        	$arrTids[] = intval($t['thread_id']);
        	$arrRes[$t['thread_id']] = $t;
        	if(4 != $t['source'] & isset($t['show_post_id']) && $t['show_post_id'] > 0) {
                $arrPids[$t['thread_id']] = $t['show_post_id'];
            }
            $arrSource[$t['thread_id']] = $t['source'];
        }
        $threadInfo = $this->_getThreadList($arrTids);
        $postInfo = $this->_getPostInfo($arrPids);
        if($threadInfo === false){
        	return false;
        }
        $arrRawThreadList = $threadInfo['thread_list'];
        $arrUserId = $this->_getThreadUserId($arrRawThreadList);
        $arrRawUserList = $this->_getUserData($arrUserId);
        foreach($arrRawThreadList as $value){
        	$thread = array();
        	$thread['id'] = $arrRes[$value['thread_id']]['id'];
			$thread['tid'] = $value['thread_id'];
            $thread['abstract'] = $value['abstract'];
            $thread['title'] = $value['title'];
            $thread['post_num'] = $value['post_num'];
            $thread['last_time_int'] = $value['last_modified_time'];
            $thread['create_time'] = $value['create_time'];
            $thread['is_deleted'] = $value['is_deleted'];
            $uname = isset($arrRawUserList[$value['user_id']]['user_name'])?$arrRawUserList[$value['user_id']]['user_name']:'';
            $thread['user_info'] = array(
   									'user_id' => $value['user_id'],
   									'user_name' => $uname,
   									'portrait' => Tieba_Ucrypt::encode($value['user_id'], $uname),
   								);
			if ($value['good_types'] > 0){
				$thread['good_types'] = 1;
			}else{
            	$thread['good_types'] = 0;
            }
            $arrMedia = array();
			if(count($value['media']) > 0) {
				$count = 0;
	            foreach($value['media'] as $media) {
		            if ('pic' == $media['type']){
			            if(!empty($media['cut_pic']) || !empty($media['small_pic'])){
				            $arrMedia[] = $media;
				            $count ++;
				            if($count >= 3){
				            	 break;
				            }
			            }
		            }
	            }
			}
            $thread['media'] = $arrMedia;
            $thread['zan'] = isset($value['zan'])?$value['zan']:'';
            $source = $arrSource[$thread['tid']];
            $thread['source'] = $source;
            if ($thread['source'] == 4) {
                $thread['source_show'] = "用户发的";
            } elseif ($thread['source'] == 1) {
                $thread['source_show'] = "人工加的";
            } else {
                $thread['source_show'] = "策略挖的";
            }
            if ($source != 4 && isset($postInfo[$thread['tid']])) {
                $thread['show_post_info'] = $postInfo[$thread['tid']];
            }
			$goodThreadList[] = $thread;
        }
        $threadList = array_slice($goodThreadList, 0,self::PAGE_SIZE);
        $num = count($arrTids);

        $countRet = Tieba_Service::call('hottopic', 'getTopicRelateThreadCount', $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($countRet === false || (isset($countRet['errno']) && intval($countRet['errno']) !== Tieba_Errcode::ERR_SUCCESS)) {
            Bingo_Log::warning(__FUNCTION__." call service hottopic failed : getTopicRelateThreadCount , input =".serialize($arrInput)." output =".serialize($ret));
            $this->_intErrno = isset($countRet['errno'])?$countRet['errno']:Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
            $this->_strError = isset($countRet['errmsg'])?$countRet['errmsg'].implode(',', $countRet['ret']):"unknown errno";
            return $this->_jsonRet();
        }
        $pageInfo = array(
        	'page_size' => self::PAGE_SIZE,
        	'current_page' => $page,
        	'has_more' => ($num > self::PAGE_SIZE)?1:0,
            'count' => $countRet['ret'],
        );
        $out = array(
        	'list' => $threadList,
        	'page' => $pageInfo,
        );
		$this->_arrTpl['data'] = $out;
		return $this->_jsonRet();
	}
	
	private function _getThreadList($arrThreadId)
    {
        $arrInput = array(
            'thread_ids'      => $arrThreadId,
            'forum_id'        => 0,
            'need_abstract'   => 1,
            'need_photo_pic'  => 0,
            'call_from'       => 'client_frs',
            'need_forum_name' => 0,
            'need_user_data'  => 0, // here we don't get user data, because zan user also need user data, so we do it together
        );

        $arrOutput = Tieba_Service::call('post', 'mgetThread', $arrInput,null,null,'post', 'php', 'utf-8');
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('call post:mgetThread failed.  [sevice_name:post] [method:mgetThread] [input:'.serialize($arrInput).'] [output:'.serialize($arrOutput).']');
            return false;
        }
        return $arrOutput['output'];
    }
	
	private function _getThreadUserId($arrThreadList)
    {
        $arrUserId = array();
        foreach ($arrThreadList as $key => $val) {
            if (isset($val['user_id'])) {
                $arrUserId = array_merge(array(strval($val['user_id'])), $arrUserId);
            }
        }
        return $arrUserId;
    }
	private function _getUserData($arrUserId)
    {
        $arrInput = array(
            'user_id'  => $arrUserId,
        );
        $arrOutput = Tieba_Service::call('user', 'mgetUserData', $arrInput,null,null,'post', 'php', 'utf-8');
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			Bingo_Log::warning('call user:mgetUserData failed.  [sevice_name:user] [method:mgetUserData] [input:'.serialize($arrInput).'] [output:'.serialize($arrOutput).']');
            return false;
        }
        foreach ($arrOutput['user_info'] as $arrOutInfo){
		    if (!empty($arrOutInfo['profession_manager_nick_name'])) {
			    $arrOutput['user_info']['user_nickname'] = $arrOutInfo['profession_manager_nick_name'];
		    }
	    }
        return $arrOutput['user_info'];
    }

    /**
     * @param $arrPids
     * @return array|bool
     */
    private function _getPostInfo($arrPids)
    {
        if (empty($arrPids)) {
            return false;
        }
        $arrPids = array_values($arrPids);
        $arrInput = array(
            'post_ids' => $arrPids,
        );
        $arrOutput = Tieba_Service::call('post', 'getPostInfo', $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($arrOutput['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('call post:mgetThread failed.  [sevice_name:post] [method:getPostInfo] [input:' . serialize($arrInput) . '] [output:' . serialize($arrOutput) . ']');
            return false;
        }
        $outRes = array();
        if (!empty($arrOutput['output'])) {
            foreach ($arrOutput['output'] as $item) {
                $content = strip_tags($item['content']);
                $content = preg_replace("/\s/", "", $content);
                $tid = $item['thread_id'];
                $newItem = array(
                    'post_id' => $item['post_id'],
                    'thread_id' => $item['thread_id'],
                    'user_name' => $item['username'],
                    'user_id'  => $item['user_id'],
                    'create_time' => $item['now_time'],
                    'content' => $content
                );
                $outRes[$tid] = $newItem;
            }
        }
        return $outRes;
    }
}
