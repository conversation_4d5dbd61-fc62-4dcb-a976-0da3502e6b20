<?php
class commitTopicAction extends Lib_BaseAction {
	function init() {
		self::setUiAttr ( self::COMMIT_UI ); //设置UI的类型
		//基类初始化，初始化失败且未设置错误代码设置错误代码为参数设置错误
		if (false === parent::init ()) {
			if (0 === $this->_intErrno) {
				$this->_intErrno = Tieba_Errcode::ERR_PARAM_ERROR;
				$this->_strError = Tieba_Errcode::$codes [$this->_intErrno];
				Bingo_Log::warning ( $this->_strError );
			}
		}
		return true;
	}
	protected function _process() {
		$topicid = (int)Bingo_Http_Request::getNoXssSafe('topic_id', 0);
		$topicname = Bingo_Http_Request::getNoXssSafe('topic_name', '');
		$topicDesc = Bingo_Http_Request::getNoXssSafe('topic_desc', '');
		$post_num_coefficient = Bingo_Http_Request::getNoXssSafe('post_num_coefficient', 1);
		$relate_forum_title = Bingo_Http_Request::getNoXssSafe('relate_forum_title', Util_Hottopic_Const::$defaultRelateForumTitle);
		$magic_title = Bingo_Http_Request::getNoXssSafe('magic_title', '');
		$mc_postid_list = Bingo_Http_Request::getNoXssSafe('mc_postid_list', array());
		$relate_thread_title = Bingo_Http_Request::getNoXssSafe('relate_thread_title', Util_Hottopic_Const::$defaultRelateThreadTitle);
		$opStatus = (int)Bingo_Http_Request::getNoXssSafe('op_status', Util_Hottopic_Const::TOPIC_OP_STATUS_NEW);
		$topicType = (int)Bingo_Http_Request::getNoXssSafe('topic_type', Util_Hottopic_Const::TOPIC_TYPE_NOMAL);
		$relateForums = Bingo_Http_Request::getNoXssSafe('relate_forums', array());
		$headPic = Bingo_Http_Request::getNoXssSafe('head_pic', '');
		$bgImage = Bingo_Http_Request::getNoXssSafe('bg_image', '');
		$headPhotoJumpUrl =  Bingo_Http_Request::getNoXssSafe('head_photo_jump_url', '');
		$pcHeadPic = Bingo_Http_Request::getNoXssSafe('pc_hpic', '');
		$shareTitle = Bingo_Http_Request::getNoXssSafe('share_title', '');
		$sharePic = Bingo_Http_Request::getNoXssSafe('share_pic', '');
		$opType = Bingo_Http_Request::getNoXssSafe('op_type', '');
		$sendSepForums = Bingo_Http_Request::getNoXssSafe('send_sep_forums', '');
		$sendSepKeywords = Bingo_Http_Request::getNoXssSafe('send_sep_keywords', '');
		$tag = (int)Bingo_Http_Request::getNoXssSafe('tag', -1);
		$videoThreadId = (int)Bingo_Http_Request::getNoXssSafe('video_thread', 0);
		$intBaseNum = (int)Bingo_Http_Request::getNoXssSafe('base_num', 0);
		$pmyTopicId = (int)Bingo_Http_Request::getNoXssSafe('pmy_topic_id', 0);
		$hotValue = (int)Bingo_Http_Request::getNoXssSafe('hot_value', 0);
		$topicTid = trim(Bingo_Http_Request::getNoXssSafe("topic_tid", 0));
		$nowTime = time();
		switch ($opType){
			case Util_Hottopic_Const::OP_TYPE_ADD :
				$arrInput = array(			
					'topic_name' => $topicname,
					'topic_desc' => $topicDesc,
					//'post_num_coefficient'=> $post_num_coefficient,
					'relate_forum_title' => $relate_forum_title,
					'magic_title' => empty($magic_title)?$topicname.Util_Hottopic_Const::$defaultMagicPostTitle : $magic_title,
					'forum_names' => $relateForums,
					'relate_thread_title' => $relate_thread_title,
					'op_status' => $opStatus,
					'topic_type' => $topicType,
					'create_time' => $nowTime,
					'op_uid' => $this->_arrUserInfo[self::USER_ID],
					'op_uname' => $this->_arrUserInfo[self::USER_NAME],
					'op_time' => $nowTime,
					'video_thread' => $videoThreadId,
					'send_sep_forums' => $sendSepForums,
					'send_sep_keywords' => $sendSepKeywords,
				);
				if(!empty($hotValue)) {
				    $arrInput['hot_value'] = $hotValue;
				    $arrInput['discuss_num'] = $hotValue;
                }
				$check = $this->_checkParam($arrInput);
				if(is_array($check)){
					$this->_intErrno = $check['errno'];
					$this->_strError = $check['errmsg'];
					return $this->_jsonRet();
				}
				if(!empty($mc_postid_list)){
					$arrInput['mc_postid_list'] = $mc_postid_list;
				}
				if(empty($sharePic)){
					$sharePic = Util_Hottopic_Const::$defaultSharePic;
				}
				if(empty($shareTitle)){
					$shareTitle = "吧友们都在讨论#".$topicname."#，快去参与一起讨论吧！";
				}
				if($tag >= 0){
					$arrInput['topic_tag'] = $tag;
				}
				if($pmyTopicId > 0){
					$arrInput['pmy_topic_id'] = $pmyTopicId;
				}
				$extra = array(
					'head_pic' => $headPic,
					'bg_image' => $bgImage,
					'head_photo_jump_url'  => $headPhotoJumpUrl,
					'share_title' => $shareTitle,
					'share_pic' => $sharePic,
					'pc_hpic' => $pcHeadPic,
                    'topic_tid' => $topicTid
				);
				$arrInput['extra'] = serialize($extra);
				if($videoThreadId > 0){
					$threadList = $this->mgetThread(array($videoThreadId));
					if(!empty($threadList[$videoThreadId]['video_info'])){
						$arrInput['video_info'] = serialize($threadList[$videoThreadId]['video_info']);
					}
				}
				$input = array(
					'req' => $arrInput,
				);
				$ret = Tieba_Service::call('hottopic', 'addTopic', $input, NULL, NULL, 'post', 'php', 'utf-8');
				if ($ret === false || (isset($ret['errno']) && intval($ret['errno']) !== Tieba_Errcode::ERR_SUCCESS)) {
					Bingo_Log::warning(__FUNCTION__." call service hottopic failed : addTopic , input =".serialize($arrInput)." output =".serialize($ret));
					$this->_intErrno = isset($ret['errno'])?$ret['errno']:Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
					$this->_strError = isset($ret['errmsg'])?$ret['errmsg'].implode(',', $ret['ret']):"unknown errno";
					return $this->_jsonRet();
				}
				$topicid = intval($ret['ret']);
				if($topicid > 0){
					$this->importDataFromSep($topicid);
					if(!empty($sendSepKeywords)){
						$this->updateDiscussNum($topicid);
					}
					if (!empty($topicTid)) {
                        if (false == $this->addTopicBindingTid($topicid, $topicTid)) {
                            Bingo_Log::warning(__FUNCTION__." call service hottopic addTopicBindingTid failed : addTopic , topicid =".$topicid." , topicTid =".$topicTid);
                            $this->_intErrno = Tieba_Errcode::ERR_PARAM_ERROR;
                            $this->_strError = "话题绑定tid失败，此话题未在线上前30榜单或该tid可能已经绑定其他话题。";
                            return $this->_jsonRet();
                        }
                    }
				}
				break;
			case Util_Hottopic_Const::OP_TYPE_EDIT :
				$arrInput = array(	
					'topic_id' => $topicid,	
					'topic_type' => $topicType,
					'op_uid' => $this->_arrUserInfo[self::USER_ID],
					'op_uname' => $this->_arrUserInfo[self::USER_NAME],
					'op_time' => $nowTime,
				);
				if(empty($topicid)){
					Bingo_Log::warning("topic_id input params error!".serialize($arrInput));
					$this->_intErrno = Tieba_Errcode::ERR_PARAM_ERROR;
					$this->_strError = 'input params error!';
					return $this->_jsonRet();
				}
				if(empty($headPic)){
					$headPic = Util_Hottopic_Const::$defaultHeadPic;
				}
				if(empty($shareTitle)){
					$shareTitle = "吧友们都在讨论#".$topicname."#，快去参与一起讨论吧！";
				}
				$extra = array(
					'head_pic' => $headPic,
                    'head_photo_jump_url'  => $headPhotoJumpUrl,
					'share_title' => $shareTitle,
					'share_pic' => !empty($sharePic)?$sharePic:Util_Hottopic_Const::$defaultSharePic,
					'pc_hpic' => $pcHeadPic,
                    'bg_image' => $bgImage,
                    'topic_tid' => $topicTid
                );
				$arrInput['extra'] = serialize($extra);
				if(!empty($topicname)){
					$arrInput['topic_name'] = $topicname;
				}
				if(!empty($topicDesc)){
					$arrInput['topic_desc'] = $topicDesc;
				}
				if($post_num_coefficient == 1){
					$arrInput['post_num_coefficient'] = $post_num_coefficient;
				}
				$arrInput['relate_forum_title'] = $relate_forum_title;
				$arrInput['magic_title'] = $magic_title;
				$arrInput['mc_postid_list'] = $mc_postid_list;
				$arrInput['relate_thread_title'] = $relate_thread_title;
				$arrInput['send_sep_forums'] = $sendSepForums;
				$arrInput['send_sep_keywords'] = $sendSepKeywords;
				$arrInput['video_thread'] = $videoThreadId;
                if(!empty($hotValue)) {
                    $arrInput['hot_value'] = $hotValue;
                    $arrInput['discuss_num'] = $hotValue;
                }
				if($tag >= 0){
					$arrInput['topic_tag'] = $tag;
				}
				if($intBaseNum > 0){
					$arrInput['base_num'] = $intBaseNum;
				}
				if($videoThreadId > 0){
					$threadList = $this->mgetThread(array($videoThreadId));
					if(!empty($threadList[$videoThreadId]['video_info'])){
						$arrInput['video_info'] = serialize($threadList[$videoThreadId]['video_info']);
					}
				}else{
					$arrInput['video_info'] = '';
				}
				$input = array(
					'req' => $arrInput,
				);
				$ret = Tieba_Service::call('hottopic', 'updateTopicInfo', $input, NULL, NULL, 'post', 'php', 'utf-8');
				if ($ret === false || (isset($ret['errno']) && intval($ret['errno']) !== Tieba_Errcode::ERR_SUCCESS)) {
					Bingo_Log::warning(__FUNCTION__." call service hottopic failed : updateTopicInfo , input =".serialize($arrInput)." output =".serialize($ret));
					$this->_intErrno = isset($ret['errno'])?$ret['errno']:Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
					$this->_strError = isset($ret['errmsg'])?$ret['errmsg'].implode(',', $ret['ret']):"unknown errno";
					return $this->_jsonRet();
				}
				if(!empty($sendSepKeywords)){
					$this->updateDiscussNum($topicid);
				}
				if ($topicid > 0) {
                    if (false == $this->addTopicBindingTid($topicid, $topicTid)) {
                        Bingo_Log::warning(__FUNCTION__." call service hottopic addTopicBindingTid failed : addTopic , topic id =".$topicid." , topic tid =".$topicTid);
                        $this->_intErrno = Tieba_Errcode::ERR_PARAM_ERROR;
                        $this->_strError = "话题绑定tid失败，此话题未在线上前30榜单或该tid可能已经绑定其他话题。";
                        return $this->_jsonRet();
                    }
                }
				break;
			case Util_Hottopic_Const::OP_TYPE_DELETE :
				$arrInput = array(	
					'topic_id' => $topicid,	
					'op_uid' => $this->_arrUserInfo[self::USER_ID],
					'op_uname' => $this->_arrUserInfo[self::USER_NAME],
					'op_time' => $nowTime,
					'op_status' => Util_Hottopic_Const::TOPIC_OP_STATUS_DELETE,
				);
				if(empty($topicid)){
					Bingo_Log::warning("topic_id input params error!".serialize($arrInput));
					$this->_intErrno = Tieba_Errcode::ERR_PARAM_ERROR;
					$this->_strError = 'input params error!';
					return $this->_jsonRet();
				}
				$input = array(
					'req' => $arrInput,
				);
				$ret = Tieba_Service::call('hottopic', 'deleteTopic', $input, NULL, NULL, 'post', 'php', 'utf-8');
				if ($ret === false || (isset($ret['errno']) && intval($ret['errno']) !== Tieba_Errcode::ERR_SUCCESS)) {
					Bingo_Log::warning(__FUNCTION__." call service hottopic failed : deleteTopic , input =".serialize($arrInput)." output =".serialize($ret));
					$this->_intErrno = isset($ret['errno'])?$ret['errno']:Tieba_Errcode::ERR_CALL_SERVICE_FAIL;
					$this->_strError = isset($ret['errmsg'])?$ret['errmsg'].implode(',', $ret['ret']):"unknown errno";
					return $this->_jsonRet();
				}
				break;
			default :
				Bingo_Log::warning("[$topicid]unknown op_type[$opType]!");
				$this->_intErrno = Tieba_Errcode::ERR_PARAM_ERROR;
				$this->_strError = 'input params error!';
				return $this->_jsonRet();
				break;
		}
		if ($ret ['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
			$this->_intErrno = $ret ['errno'];
			$this->_strError = $ret ['errmsg'];
			return $this->_jsonRet ();
		}
		$this->_arrTpl['data'] = array('topic_id' => $topicid);
		return $this->_jsonRet();
	}
	
	private function _checkParam($arrInput){
		if(empty($arrInput['topic_name']) || empty($arrInput['relate_forum_title'])
		|| empty($arrInput['magic_title']) || empty($arrInput['relate_thread_title'])){
			Bingo_Log::warning("input params error!".serialize($arrInput));
			return array(
				'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
				'errmsg' => "input params error!",
			);
		}
		if(mb_strlen($arrInput['topic_name'], 'UTF8') > Util_Hottopic_Const::MAX_TOPIC_NAME_LENGTH){
			Bingo_Log::warning("topic_name is too long!".serialize($arrInput));
			return array(
				'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
				'errmsg' => "话题名称过长，不要超过".Util_Hottopic_Const::MAX_TOPIC_NAME_LENGTH . "个字。",
			);
		}
		if(mb_strlen($arrInput['topic_desc'], 'UTF8') > Util_Hottopic_Const::MAX_TOPIC_DESC_LENGTH){
			Bingo_Log::warning("topic_desc is too long!".serialize($arrInput));
			return array(
				'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
				'errmsg' => "话题简介过长，不要超过".Util_Hottopic_Const::MAX_TOPIC_DESC_LENGTH . "个字。",
			);
		}
		if(mb_strlen($arrInput['relate_forum_title'], 'UTF8') > Util_Hottopic_Const::MAX_TITLE_LENGTH){
			Bingo_Log::warning("relate_forum_title is too long!".serialize($arrInput));
			return array(
				'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
				'errmsg' => "相关吧标题过长，不要超过".Util_Hottopic_Const::MAX_TITLE_LENGTH . "个字。",
			);
		}
		if(mb_strlen($arrInput['magic_title'], 'UTF8') > Util_Hottopic_Const::MAX_TITLE_LENGTH){
			Bingo_Log::warning("magic_title is too long!".serialize($arrInput));
			return array(
				'errno' => Tieba_Errcode::ERR_PARAM_ERROR,
				'errmsg' => "神回复标题过长，不要超过".Util_Hottopic_Const::MAX_TITLE_LENGTH . "个字。",
			);
		}
		return true;
	}
	
	protected function importDataFromSep($topicid){
		$cmdPre = "pwd";
		$dir = exec($cmdPre,$arr,$ret);
        $appPath = $dir . "/../app/hottopic/service/script";
        $cmdPre = "cd $appPath";
        $res = exec($cmdPre,$arr1,$ret1);
        Bingo_Log::notice("cmd:$cmdPre,ret:$ret1,".serialize($arr1));
        $command = $cmdPre . " && ". $dir. "/../php/bin/php importDataFromSep.php " . $topicid . " > /dev/null &";
        Bingo_Log::notice($command);
        Bingo_Timer::start ("popen_time");
        $retVal = pclose(popen($command,'r'));
        Bingo_Timer::end ("popen_time");
		if ($retVal != 0) {
			Bingo_Log::warning ( 'execute command fail command is ' . $command );
			return false;
		}
		return true;
	}
	protected function updateDiscussNum($topicid){
		$cmdPre = "pwd";
		$dir = exec($cmdPre,$arr,$ret);
        $appPath = $dir . "/../app/hottopic/service/script";
        $cmdPre = "cd $appPath";
        $res = exec($cmdPre,$arr1,$ret1);
        Bingo_Log::notice("cmd:$cmdPre,ret:$ret1,".serialize($arr1));
        $command = $cmdPre . " && ". $dir. "/../php/bin/php getDiscussNum.php " . $topicid . " > /dev/null &";
        Bingo_Log::notice($command);
        Bingo_Timer::start ("popen_time");
        $retVal = pclose(popen($command,'r'));
        Bingo_Timer::end ("popen_time");
		if ($retVal != 0) {
			Bingo_Log::warning ( 'execute command fail command is ' . $command );
			return false;
		}
		return true;
	}
	protected function mgetThread($arrTid,$callFrom = 'client_frs',$ie = 'utf-8'){
		$input = array(
		    "thread_ids" => $arrTid,
		    "need_abstract" => 0,
		    "forum_id" => 0,
		    "need_photo_pic" => 0,
		    "need_user_data" => 0,
		    "icon_size" => 1,
		    "need_forum_name" => 0, //是否获取吧名
		    "call_from" => $callFrom, //上游模块名
		);
		$ret = Tieba_Service::call('post', 'mgetThread', $input, NULL, NULL, 'post', 'php', $ie);
		if ($ret === false || (isset($ret['errno']) && intval($ret['errno']) !== Tieba_Errcode::ERR_SUCCESS)) {
			Bingo_Log::warning(__FUNCTION__." call service post failed : mgetThread , input =".serialize($input)." output =".serialize($ret));
			return false;
		}
		return $ret['output']['thread_list'];
	}

    /**
     * @param $topicId
     * @param $topicTid
     * @return bool
     */
    protected function addTopicBindingTid($topicId, $topicTid)
    {
        $arrInput = array(
            'req' => array(
                'topic_id' => $topicId,
                'topic_tid' => $topicTid
            ),
        );
        $ret = Tieba_Service::call('hottopic', 'addTopicBindingTid', $arrInput, null, null, 'post', 'php', 'utf-8');
        if ($ret === false || (isset($ret['errno']) && intval($ret['errno']) !== Tieba_Errcode::ERR_SUCCESS)) {
            Bingo_Log::warning(__FUNCTION__ . " call service hottopic failed : addTopicBindingTid , input =" . serialize($arrInput) . " output =" . serialize($ret));
            return false;
        }
        return true;
    }
}
?>