<?php

class assignAction extends Lib_BaseAction {
	const PAGE_SIZE = 10;
	function init() {
		self::setUiAttr(self::COMMIT_UI);//设置UI的类型
		//基类初始化，初始化失败且未设置错误代码设置错误代码为参数设置错误
		if (false === parent::init ()) {
			if (0 === $this->_intErrno) {
				$this->_intErrno = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strError = Tieba_Errcode::$codes [$this->_intErrno];
				Bingo_Log::warning ( $this->_strError );
			}
		}	
		return true;
	}
	protected function _process() {
		$free   = (int)Bingo_Http_Request::getNoXssSafe('free', 0);
		$source = Bingo_Http_Request::getNoXssSafe('source');
		if($free === 1){
			$arrInput = array(
					'unite_status' => Util_Postrecover_Const::$uniteStatus['taskpool'],
					'assign_uid'   => $this-> _arrUserInfo[self::USER_ID],
					'is_vip'       => (int)Bingo_Http_Request::getNoXssSafe('is_vip',0),
					'apply_times'  => (int)Bingo_Http_Request::getNoXssSafe('apply_times',1),
					'source'       => $source,	
			);
			$ret = Service_Postrecover_Audit::freeAssign($arrInput);
			if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				$this->_intErrno = $ret['errno'];
				$this->_strError = $ret['errmsg'];				
			} 
			return $this->_jsonRet();
		}
		
		$arrInput = array(
			'unite_status' => Util_Postrecover_Const::$uniteStatus['tobe'],
			'assign_uid'   => $this-> _arrUserInfo[self::USER_ID],
			'is_vip'       => (int)Bingo_Http_Request::getNoXssSafe('is_vip',0),
			'apply_times'  => (int)Bingo_Http_Request::getNoXssSafe('apply_times',1),
			'source'       => $source,
		);
		$ret = Service_Postrecover_Audit::assignRecover($arrInput);
		if(intval($ret['ret']) === 0){
			return $this->_jsonRet();
		}
		//获取一页任务返回
		$arrInput = array(
			'unite_status' => Util_Postrecover_Const::$uniteStatus['taskpool'],
			'assign_uid'   => $this-> _arrUserInfo[self::USER_ID],
			'is_vip'       => (int)Bingo_Http_Request::getNoXssSafe('is_vip',0),
			'apply_times'  => (int)Bingo_Http_Request::getNoXssSafe('apply_times',1),
			'offset'       => 0,
			'count'        => self::PAGE_SIZE,
			'order_by'     => 'apply_time ASC',
		);
		$ret = Service_Postrecover_Audit::getRecoverList($arrInput);
		if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			$this->_intErrno = $ret['errno'];
			$this->_strError = $ret['errmsg'];
			return $this->_jsonRet();
		}
		$this->_arrTpl['data'] = $ret['ret']['data'];
		$this->_arrTpl['page'] = self::_paging(1, $ret['ret']['total'], self::PAGE_SIZE);
		//获取模板返回
		$arrInput = array(				
			'is_vip'       => (int)Bingo_Http_Request::getNoXssSafe('is_vip',0),
			'apply_times'  => (int)Bingo_Http_Request::getNoXssSafe('apply_times',1),			
		);
		$ret = Service_Postrecover_Template::getTmpl($arrInput);
		if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			$this->_intErrno = $ret['errno'];
			$this->_strError = $ret['errmsg'];
			return $this->_jsonRet();
		}
		$this->_arrTpl['template'] = array_values($ret['ret']);
		return $this->_jsonRet();
	}
} 
?>
