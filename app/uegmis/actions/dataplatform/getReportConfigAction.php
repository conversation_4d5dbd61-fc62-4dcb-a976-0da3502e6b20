<?php

class getReportConfigAction extends Lib_BaseAction {
	function init() {
		self::setUiAttr(self::BROWSE_UI);//设置UI的类型
		//基类初始化，初始化失败且未设置错误代码设置错误代码为参数设置错误
		if (false === parent::init ()) {
			if (0 === $this->_intErrno) {
				$this->_intErrno = Tieba_Errcode::ERR_MO_PARAM_INVALID;
				$this->_strError = Tieba_Errcode::$codes [$this->_intErrno];
				Bingo_Log::warning ( $this->_strError );
			}
		}
		return true;
	}
	protected function _process() {	
		$callback = trim(Bingo_Http_Request::getNoXssSafe("callback",''));
		$report_id = trim(Bingo_Http_Request::getNoXssSafe("report_id",''));
		$config = $this->_createConfig($report_id);
		if($callback != '')
		{
			echo $callback."(".$config.")";
		}
	//http://cq01-yd-ueg-frontend05.epc.baidu.com:8204/dataplateform/getReportConfig?report_id=60&report_tpl=6&callback=cb
	}
	protected function _createConfig($report_id) {
		$config=array(
			'status' => 0,
			'msg' => 'ok',
			'data' => array('conditions' => array(),),
		);
		// 生成时间选项;
		array_push($config['data']['conditions'],$this->_createDateConditon(Util_Dataplatform_Const::$MUNU_DATE));
		// 生成二级菜单
		$menu = array(
			'name' => '联动菜单',
			'key' => 'first',
			'type' => 7,
			'items' => array()
		);
		$menuInfo = isset(Util_Dataplatform_Const::$POST_REPORT[$report_id]) ? Util_Dataplatform_Const::$POST_REPORT[$report_id]['menu'] : Util_Dataplatform_Const::$VCODE_REPORT[$report_id]['menu'];
		if($menuInfo == null)
		{
			echo "waht!";
			return null;
		}
		Util_Dataplatform_Data::initDeleteMonitorType(Util_Dataplatform_Const::$TRATE_FULL);
		Util_Dataplatform_Data::initVcodeMonitorType(Util_Dataplatform_Const::$VCODE_MONITOR);
		// 生成菜单项;
		foreach($menuInfo as $name => $menu_data)
		{
			$menu_second = Util_Dataplatform_Const::${$menu_data}; // 二级菜单信息;
			{
				 $condition['name'] = $name;
				 $condition['value'] = $menu_data;
				 $condition['data'] = $this->_getItems($menu_second);
				 array_push($menu['items'], $condition);
			}
		}
		array_push($config['data']['conditions'],$menu);
		return json_encode($config);
	}
	protected function _getItems($condition)
	{
		$itesm = array();
		foreach( $condition as $key => $value )
		{
			array_push($itesm, array('name' => $value, 'value' => $key));
		}
		return $itesm;
	}
	protected function _createDateConditon($report_data) {
		 $condition = array();
		 foreach($report_data as $key => $value)
		 {
		 	 if($key == 'list')
			 {
			 	$condition['list'] = array();
				foreach( $value as $key_date => $value_date)
				{
					$listDate = array('name' => $value_date,'value' => $key_date);
					array_push($condition['list'], $listDate);
				}
			 }
			 else
			 {
			 	$condition[$key] = $value;
			 }
		 }
		 return $condition;
	}

	protected function _createReport($report_data) {	
		$report = array();
		foreach($report_data as $report_id => $name)
		{
			$data = array(
				'name' => $name,
				'id' => $report_id,
				'tpl' => 6,
			);
			array_push($report,$data);
		}
		return $report;
	}
}
?>
