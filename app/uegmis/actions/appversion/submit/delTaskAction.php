<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 16/10/25
 * Time: 下午2:16
 */


class delTaskAction extends Lib_BaseAction {
    /**
     * @param empty
     * @return bool
     */
    function init() {
        self::setUiAttr(self::COMMIT_UI);//设置UI的类型
        //基类初始化，初始化失败且未设置错误代码设置错误代码为参数设置错误
        if (false === parent::init()) {
            if (0 === $this->_intErrno) {
                $this->_intErrno = Tieba_Errcode::ERR_MO_PARAM_INVALID;
                $this->_strError = Tieba_Errcode::$codes [$this->_intErrno];
                Bingo_Log::warning($this->_strError);
            }
        }
        return true;
    }

    protected function _process() {
        $taskId = intval(Bingo_Http_Request::get('task_id', 0));
        $opUid = $this->_arrUserInfo[self::USER_ID];
        $opUname = $this->_arrUserInfo[self::USER_NAME];
        if ($taskId <= 0 || !$opUid || empty($opUname)) {
            Bingo_Log::warning('task id invalid');
            $this->error(Tieba_Errcode::ERR_PARAM_ERROR);
            return $this->_jsonRet();
        }
        $arrInput = array(
            'task_id' => $taskId,
            'op_uid' => $opUid,
            'op_uname' => $opUname,
        );

        $taskInfoRet = Tieba_Service::call('appversion', 'delTask', $arrInput, null, null, 'post', 'php', 'utf-8');
        //$taskInfoRet = Service_Appversion::getOneTaskInfo($arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $taskInfoRet['errno']) {
            $this->error($taskInfoRet['errno']);
            return $this->_jsonRet();
        }
        $this->_arrTpl['data'] = $taskInfoRet['ret'];
        return $this->_jsonRet();

    }
    /**
     * 设置错误号
     * @param $errno
     * @param string $errmsg
     */
    private function error($errno, $errmsg = '') {
        $this->_intErrno = $errno;
        $this->_strError = Tieba_Error::getErrmsg($errno);
    }
}