<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2014
 * @version
 */
class Service_Tongchenghui_Interview {

    const FIELD_PREFIX = 'ti_';
    
    const TIEBA_USERPIC_URL_PRE ='http://tb.himg.baidu.com/sys/portrait/item/';
    /**
     * 返回错误信息
     */
    private static function _errRet($errno) {
        return array (
                'errno'  => $errno,
                'errmsg' => Tieba_Error::getErrmsg ( $errno ),                
        );
    }
    
    /**
     * 返回正确信息
     */
    private static function _succRet($ret) {
        $error = Tieba_Errcode::ERR_SUCCESS;
        return array (
                'errno' => $error,
                'errmsg'=> Tieba_Error::getErrmsg ( $error ),
                'ret'   => $ret,
        );
    }

    /**
     * @param [in] $fields 
     * @return [out]
     */
    private static function _changeFields($fields, $flag=true) {
        if (is_array($fields)) {
            $tmp = array();
            if ($flag) {
                foreach ($fields as $key => $val) {
                    $tmp[self::FIELD_PREFIX.$key] = $val;
                }
            } else {
                foreach ($fields as $key => $val) {
                    $tmp[substr($key, strlen(self::FIELD_PREFIX))] = $val;
                }
            }
            return $tmp;
        } else {
            Bingo_Log::warning(__FUNCTION__ . " fields params invalid. fields =".serialize($fields));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
    }

    public static function _getUserPicUrl($user_id,$user_name){
        return self::TIEBA_USERPIC_URL_PRE.Tieba_Ucrypt::encode($user_id, $user_name);
    }
    
    /**
     * @param [in] $arrInput 查询活动条件
     * @return [out] $result
     */
    public static function getInterviewList($arrInput) {

        $arrValue = array();
        //是否删除
        $conds['is_deleted ='] = 0;
        //开始位置
        if (isset($arrInput['offset'])) {
            $arrValue['offset'] = $arrInput['offset'];
        }
        //总条数
        if (isset($arrInput['num'])) {
            $arrValue['num'] = $arrInput['num'];
        }
        
        $retDB = Dl_Tongchenghui_Interview::getInterviewCount($arrValue);
        if ($retDB['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            return self::_errRet($retDB['errno']);
        }
        $ret = $data = array();
        $ret['total'] = $retDB['ret'];
        if ($ret['total'] > 0) {
            $retDB = Dl_Tongchenghui_Interview::getInterviewList($arrValue);
            if($retDB['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                return self::_errRet($retDB['errno']);
            }
            foreach ($retDB['ret'] as $val) {
                $tmp = self::_changeFields($val, false);
                $tmp['portrait'] = self::_getUserPicUrl($tmp['userid'], $tmp['username']);
                $data[] = $tmp;
            }
        }
        $result['data'] = $data;
        return self::_succRet($result);
    }

    /**
     * 根据ID获取一个访谈信息
     */
    public static function getOneInterview($interview_id) {
        $retDB = Dl_Tongchenghui_Interview::getOneInterviewById($interview_id);
        $retDB['ret'] = self::_changeFields($retDB['ret'], false);
        return $retDB;
    }

    /**
     * 添加访谈
     */
    public static function addInterview($arrInput) {
        $arrInput = self::_changeFields($arrInput, true);
        return Dl_Tongchenghui_Interview::insertInterview($arrInput);
    }
    
    /**
     * 修改访谈主体
     */
    public static function updateInterviewBody($arrInput) {
        $arrInput = self::_changeFields($arrInput, true);
        return Dl_Tongchenghui_Interview::updateInterview($arrInput);
    }
    
    /**
     * 修改访谈顺序
     */
    public static function updateInterviewIndex($strInput) {
        $arrValue = array();
        $arrInput = explode(';', $strInput);
        if (count($arrInput) != 2) {
            return self::_errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        foreach ($arrInput as $val) {
            $val = explode(',', $val);
            $arrValue[] = intval($val[0]);
        }
        $ret = Dl_Tongchenghui_Interview::getInterviewListByIds($arrValue);
        if ( !isset($ret['errno']) || Tieba_Errcode::ERR_SUCCESS != $ret['errno'] ) {
            Bingo_Log::warning('ERR[Dl_Tongchenghui_Interview::getInterviewListByIds] IN['.__FUNCTION__.'] REQ['.serialize($arrValue).'] RES['.serialize($arrValue).']');
            return self::_errRet(Tieba_Errcode::ERR_FSTAR_DB_CALL_FAIL);
        }
        
        $arrValue = array();
        foreach ($ret['ret'] as $val) {
            $arrValue[] = array(
                self::FIELD_PREFIX.'id' => $val[self::FIELD_PREFIX.'id'],
                self::FIELD_PREFIX.'interview_index' => $val[self::FIELD_PREFIX.'interview_index'],
            );
        }
        
        $tmp = $arrValue[0][self::FIELD_PREFIX.'interview_index'];
        $arrValue[0][self::FIELD_PREFIX.'interview_index'] = $arrValue[1][self::FIELD_PREFIX.'interview_index'];
        $arrValue[1][self::FIELD_PREFIX.'interview_index'] = $tmp;
        $flag = true;
        $update_ret = Dl_Tongchenghui_Interview::updateInterview($arrValue[0]);
        $flag = $flag & (Tieba_Errcode::ERR_SUCCESS === $output['errno']);
        $update_ret = Dl_Tongchenghui_Interview::updateInterview($arrValue[1]);
        $flag = $flag & (Tieba_Errcode::ERR_SUCCESS === $output['errno']);
        if ($flag) {
            return self::_succRet('success');
        } else {
            return self::_errRet(Tieba_Errcode::ERROR_MIS_FAILED_TO_SEND_TO_DAL);
        }
        //return Dl_Tongchenghui_Interview::updateMultiInterview($arrValue);
    }
    
    /**
     * 删除访谈
     */
    public static function delInterview($arrInput) {
        if (empty($arrInput) || !is_numeric(str_replace(',', '', $arrInput))) {
            Bingo_Log::warning(__FUNCTION__ . " input params invalid. input =".serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrValue = array(
            self::FIELD_PREFIX.'is_deleted' => 1,
            self::FIELD_PREFIX.'id' => explode(',', $arrInput),
        );

        return Dl_Tongchenghui_Interview::updateInterview($arrValue);
    }
}
