<?php


class Service_Personal_Personal {

    public static $service_ie = 'utf-8';

    /**
     * @param $errno
     * @return array
     */
    private static function _errRet($errno) {
        return array(
            'errno' => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
        );
    }

    /**
     * @param $errno
     * @return array
     */
    private static function _succRet($ret) {
        $error = Tieba_Errcode::ERR_SUCCESS;
        return array(
            'errno' => $error,
            'errmsg' => Tieba_Error::getErrmsg($error),
            'ret' => $ret,
        );
    }

    /**
     * 添加一个个性化吧
     * @param $arrInput
     * @return array|bool|mixed|null|string|void
     */
    public static function addPersonalForum($arrInput) {
        if (!isset($arrInput['forum_id']) || !isset($arrInput['op_uid']) || !isset($arrInput['op_uname'])) {
            Bingo_Log::warning('param error' . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $input = array(
            "forum_id" => $arrInput['forum_id'],
            "attr_name" => "is_personal_forum", //吧属性名
            "attr_value" => "1",
        );
        $ret = self::privateCall('delForumAttr', $input);
        if (false == $ret || Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning('error' . serialize($input) . ' while calling delForumAttr ' . serialize($ret));
            return self::_errRet($ret['errno']);
        }
        $ret = self::privateCall('addForumAttr', $input);
        if (false == $ret || Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning('error' . serialize($input) . ' while calling addForumAttr ' . serialize($ret));

            return self::_errRet($ret['errno']);
        }
        $arrInput = array(
            'input' => array(
                'table_name' => Util_Personal_Const::PERSONAL_FORUM_WORDLIT_NAME,
                'key' => $arrInput['forum_id'],
                'expire' => 0,
                'remark' => 'zhaoshiya_script',
                'value' => $arrInput['forum_name'],
                'create_user' => 'zhaoshiya',
                'create_time' => time(),
            ),
        );
        $ret = self::privateCall('addItem', $arrInput, 'mis');
        return self::_succRet($ret);
    }


    /**
     * 获取一个吧的信息
     * @param $arrInput
     * @return array|bool|mixed|null|string|void
     */
    public static function getOneForumInfo($arrInput) {
        if (!isset($arrInput['forum_id']) || !isset($arrInput['op_uid']) || !isset($arrInput['op_uname'])) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $input = array(
            'forum_id' => $arrInput['forum_id'],
            'data_tpl' => 'forum_personal_tpl',
        );
        $ret = self::privateCall('getBtxInfo', $input);
        return self::_succRet($ret);
    }

    /**
     * 设置个性化吧的信息
     * @param $arrInput
     * @return array|bool|mixed|null|string|void
     */
    public static function setOneForumAttr($arrInput) {
        if (!isset($arrInput['forum_id']) || !isset($arrInput['op_uid']) || !isset($arrInput['op_uname'])) {
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrAttr = $arrInput['attr'];
        $input = array(

            "forum_id" => $arrInput['forum_id'],
            "attr_name" => Util_Personal_Const::PERSONAL_FORUM_ATTR_NAME, //吧属性名
            "attr_value" => serialize($arrAttr),
            'call_from' => 'forum_personal_tpl',
        );

        $ret = self::privateCall('delForumAttr', $input);
        if (false == $ret || Tieba_Errcode::ERR_SUCCESS !== $ret['errno']) {
            Bingo_Log::warning('error' . serialize($input) . ' while calling delForumAttr ' . serialize($ret));

            return self::_errRet($ret['errno']);
        }
        $res = self::privateCall('addForumAttr', $input);
        return self::_succRet($res);
    }

    /**
     * @param $method
     * @param $arrInput
     * @param string $module
     * @return array|bool|mixed|null|string|void
     */
    private static function privateCall($method, $arrInput, $module = 'forum') {
        return Tieba_Service::call($module, $method, $arrInput, null, null, 'post', 'php', 'utf-8');
    }
}