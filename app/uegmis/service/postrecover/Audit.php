<?php
class Service_Postrecover_Audit {	
	private static function _errRet($errno) {
		return array (
				'errno'  => $errno, 
				'errmsg' => Util_Postrecover_Errcode::getErrmsg( $errno ) );
	}
	private static function _succRet($ret) {
		$error = Tieba_Errcode::ERR_SUCCESS;
		return array (
				'errno' => $error, 
				'errmsg'=> Tieba_Error::getErrmsg ( $error ), 
				'ret'   => $ret );
	}

    /**
	 * 查看帖子信息
     * @param $pid
     * @return array
     */
    private static function getPostInfoByPids($pids)
    {
        if (!is_array($pids)) {
            return array();
        }

        $arrInput = array(
            "post_ids" => $pids
        );
        $arrOutput = Tieba_Service::call('post', 'getPostInfo', $arrInput, null, null, 'post', 'php', 'utf-8');

        if ($arrOutput != false && $arrOutput['errno'] == Tieba_Errcode::ERR_SUCCESS) {
            return $arrOutput['output'];
        } else {
            Bingo_Log::warning(__FUNCTION__ . " call [post.getPostInfo] failed input[" . serialize($arrInput) . "] output[" . serialize($arrOutput) . "]");
            return array();
        }
    }


    /**
	 * 获取主题的pid
     * @param $tid
     * @return bool
     */
    private static function getTopPidByTid($tid){
        $arrInput = array(
            "thread_id" => $tid , //帖子id
            "offset" => 0,
            "res_num" => 1
        );
        $arrOutput   = Tieba_Service::call('post', 'getPostList', $arrInput, null, null, 'post', 'php', 'utf-8');

        if($arrOutput != false && $arrOutput['errno'] == Tieba_Errcode::ERR_SUCCESS){
            return $arrOutput['output'][0]['post_ids'][0];
        }else{
            Bingo_Log::warning('ERR[call service failed] IN['.__FUNCTION__
				.'] CALL[post.getPostList] REQ['.serialize($arrInput).'] RES['.serialize($arrOutput).']');
            return false;
        }
    }

    /**
     * 查询用户昵称
     * @param $uid
     * @return bool|string
     */
    public static function getNickName($uid)
    {
        $nickname = '';
        $arrParam = array(
            'user_id' => $uid,
            'get_icon' => 0,
        );
        $arrRet = Tieba_Service::call('user', 'getUserData', $arrParam, null, null, 'post', 'php', 'utf-8');
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning(sprintf("call user getUserData fail. [input = %s] [output = %s]",
                serialize($arrParam), serialize($arrRet)));
            return false;
        }

        if (!empty($arrRet['user_info']) && isset($arrRet['user_info'][0]['user_nickname'])) {
            $nickname = $arrRet['user_info'][0]['user_nickname'];
        }
        return $nickname;
    }

    /**
	 * 获取帖子列表
     * @param $arrInput
     * @return array
     */
	public static function getRecoverList($arrInput){
		$dbCount = Dl_Postrecover_Postrecover::getRecoverCount($arrInput);
		if($dbCount['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			return self::_errRet($dbCount['errno']);
		}
		$result['total'] = intval($dbCount['ret']);
		$result['data']  = array();
		if($dbCount['ret'] > 0){
			$dbData = Dl_Postrecover_Postrecover::getRecoverList($arrInput);
			if($dbData['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				return self::_errRet($dbData['errno']);
			}
			$sids = array();
			$delPost = array();
            $threadIds = array();
			foreach ($dbData['ret'] as $row){
				if(intval($row['post_sid']) > 0){
					$sids[] = intval(intval($row['post_sid']));
				}
				$delPost[] = array('thread_id'=> intval($row['thread_id']), 'post_id'=>intval($row['pcid']));
                $threadIds[] = intval($row['thread_id']);
			}
			$signs = array();
			if(count($sids) > 0){
				$signs = self::_getSignBySignIds($sids);
			}
			$delPostInfo = array();
			if(count($delPost) > 0){
				$delPostInfo = self::_getPostDelInfo($delPost);
			}
            $mgetThread = array();
            if (count($threadIds) > 0){
                $mgetThread = self::_mgetThread($threadIds);
            }
			foreach ($dbData['ret'] as $row){
				$row['op_time']    = intval($row['op_time'] ) === 0 ? '' : date('Y-m-d H:i:s',$row['op_time']);
				$row['apply_time'] = date('Y-m-d H:i:s',$row['apply_time']);
				$row['post_time']  = date('Y-m-d H:i:s',$row['post_time']);
				$row['portrait']     = Lib_Portait::getPortaitUrl($row['user_id'], $row['user_name']);
				$row['nick_name']     =  self::getNickName($row['user_id']);
				$multimedia = Lib_Html::getRichTextInfo($row['post_content']);
				$row['post_imgs']  = isset($multimedia['image']) ? $multimedia['image'] : array();
				$row['post_vedio'] = isset($multimedia['video']) ? $multimedia['video'] : array();
				$row['post_audio'] = isset($multimedia['audio']) ? $multimedia['audio'] : array();
				$row['post_audio_url'] = intval($row['post_type'] ) === 1? Util_Postrecover_Const::AUDIO_PLAYER_URL . '?tid=' . $row['thread_id']
					. '&pid=' . $row['pcid'] : '' ;
				$row['post_type'] = Util_Postrecover_Common::getPostType($row['post_no'], $row['comment_id']);

                // 查询帖子内容
                $content = "";
                $pid = $row['pcid'];
                if ($pid == "") {
                    $pid = self::getTopPidByTid($row['thread_id']);
                }
                if ($pid != false) {
                    $post_info = self::getPostInfoByPids(array($pid));
                    if (count($post_info) > 0) {
                        $content = $post_info[0]['content'];
                    }
                }
                if ($content == "") {
                    $content = $row['post_content'];
                }

                $parse = Lib_Html::ParseHtmlImg($content);
				$row['post_content'] = $parse['text'];
				//hp_parse_html_ex 扩展有bug 有一张图片时可能返回图片为空
				if(count($row['post_imgs']) != count($parse['images'])){
					$row['post_imgs'] = array();
					foreach ($parse['images'] as $img){
						$row['post_imgs'][] = array(
								'src'   => $img,
								'width' => 200,
								'height'=> 200,
						);
					}
				}
				$sid = intval($row['post_sid']);
				$row['signature'] = array_key_exists($sid, $signs) ? $signs[$sid] : '';
				$row['op_status_text'] = Util_Postrecover_Common::GetStatusText($row['op_status']);

                $first_post_id = isset($mgetThread[intval($row['thread_id'])]['first_post_id']) ? intval($mgetThread[intval($row['thread_id'])]['first_post_id']) : 0;
                if($first_post_id == $row['pcid'] && array_key_exists(intval($row['thread_id']), $delPostInfo['thread'])){
                    $row['delpost_info'] = $delPostInfo['thread'][intval($row['thread_id'])];
                }elseif (array_key_exists(intval($row['pcid']), $delPostInfo['post'])){
                    $row['delpost_info'] = $delPostInfo['post'][intval($row['pcid'])];
                }
				$result['data'][]  = $row;
			}
		}
		return self::_succRet($result);
	}
	/**
	 * 帖子恢复处理
	 */
	public static function audit($arrInput, $reply = '', $from = ''){
		$result = $arrInput['result'];
		$source = $arrInput['source'];
		$handle_type = $arrInput['handle_type'];

		//获取举报内容
		$input = array('apply_id' => array_keys($result));
		$recovers = Dl_Postrecover_Postrecover::getRecoverList($input);
        Bingo_Log::notice("getRecoverList prams:".serialize($input)." res:".serialize($recovers));
		if($recovers['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			return self::_errRet($recovers['errno']);
		}
        $applyIdsLog = array();
        foreach ($recovers['ret'] as $k => $v){
            $applyIdsLog[] = $v['apply_id'];
        }
        Bingo_Log::warning ('jump data error result:'.serialize($result).' resultCount:'.count($result).' recoversCount:'.count($recovers['ret']).' recovers:'.serialize($applyIdsLog).' matchCount:'.(count(count($result)==count($recovers['ret']))));
        $recovers = $recovers['ret'];

		//获取答复模板
		$input = array('tmpl_id' => array_values($result));
		$tmpls = Service_Postrecover_Template::getTmpl($input);
		Bingo_Log::notice("getTemplate req param:".serialize($input)." res:".serialize($tmpls));
		if($tmpls['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			return self::_errRet($tmpls['errno']);
		}
		$tmpls = $tmpls['ret'];

        //更新状态为正在审核
        $ret = Dl_Postrecover_Postrecover::updateRecoverStatusAudit($result);
        if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            return self::_errRet($ret['errno']);
        }
        sleep(1);  //解决数据库主从延迟，否则导致重复拉取数据
        //先返回用户，后台继续处理
		if (!$from) {
			fastcgi_finish_request();
            Bingo_Log::notice("fastcgi_finish_request");
		}

		$arrLog  = array();
		$errno   = Tieba_Errcode::ERR_SUCCESS;
		$needIcePostInfo = array();
		$needIceUserInfo = array();
		foreach ($recovers as $recover){
            Bingo_Log::notice("recover:".serialize($recover));
		    // op_status 为 OP_STATUS_AUDIT，已经在审核中记录跳过。
		    if (intval($recover['op_status']) === Util_Postrecover_Const::OP_STATUS_AUDIT) {
                Bingo_Log::notice("op_status == OP_STATUS_AUDIT");
		        continue;
            }

			$applyId = intval($recover['apply_id']);
			if(Util_Postrecover_Const::$source[$source] != $recover['source']) {
                Bingo_Log::notice("source not match ".Util_Postrecover_Const::$source[$source]);
				continue;
			}
			$tmplId  = $result[$applyId];
			if(!array_key_exists($applyId, $result) || !array_key_exists($tmplId, $tmpls)){
				continue;
			}				
			$tmpl   = $tmpls[$tmplId];
			$status = intval($tmpl['op_type']);
			if(intval($recover['op_status']) === $status){
				continue;
			}
			$reply_content = !empty($reply) ? $reply : $tmpl['content'];
//			$deal = self::dealApply($recover, $status, $tmpl['content'], $arrInput['op_uid'], $arrInput['op_uname'], $arrInput['op_ip'],$source);
			$deal = self::dealApply($recover, $status, $reply_content, $arrInput['op_uid'], $arrInput['op_uname'], $arrInput['op_ip'],$source,$handle_type);
			if($deal === false){
				$errno  = Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL;
				break;
			}
			$arrLog[] = array(
					'apply_id' => $applyId,
					'op_status'=> $status,
					'op_reply' => $tmpl['content'],
					'op_uid'   => $arrInput['op_uid'],
					'op_uname' => $arrInput['op_uname'],
					'op_time'  => time(),
			);
			if($status == Util_Postrecover_Const::OP_STATUS_RECOVER){
				$needIcePostInfo[] = array('thread_id'=> intval($recover['thread_id']), 'post_id'=>intval($recover['pcid']));
				$needIceUserInfo[] = array(
					'thread_id' => intval($recover['thread_id']), 
				    'post_id'   => intval($recover['pcid']),
					'post_no'   => intval($recover['post_no']),
					'user_id'   => intval($recover['user_id'])
				);
			}
		}
		if(count($arrLog) > 0){
			$res = Dl_Postrecover_Auditlog::insertAuditlog($arrLog);
		}

		//?? 作用？是否删除
		if(count($needIcePostInfo) > 0){
			$delPostInfo = self::_getPostDelInfo($needIcePostInfo, false);
			$arrUserId = array();
			foreach ($needIceUserInfo as $row){
				if($row['post_no'] > 1 && array_key_exists(intval($row['post_id']), $delPostInfo['post'])){
					$delInfo= $delPostInfo['post'][intval($row['post_id'])];
				}elseif ($row['post_no'] == 1 && array_key_exists(intval($row['thread_id']), $delPostInfo['thread'])){
					$delInfo = $delPostInfo['thread'][intval($row['thread_id'])];
				}
				$monitor = intval($delInfo['monitor_type']);
				/* 
				 * 功能下线
				if(in_array($monitor, Util_Postrecover_Const::$needIceMonitor)){
					$arrUserId[] = intval($row['user_id']);
				}
				*/
			}
			/*
			 * 功能下线
			if(count($arrUserId) > 0){
				Data_Unihandle::setUsersIceTime(array_unique($arrUserId), 86400);
			}
			*/
		}
		return self::_errRet($errno);
	}
	
	public static function dealApply($applyInfo, $opStatus, $reply, $opUid, $opUname, $opIp = 0,$source,$handle_type = 0){
		$preStatus = intval($applyInfo['op_status']);
		$threadId  = intval($applyInfo['thread_id']);
		$postId    = intval($applyInfo['post_no']) === 1 ? 0 :intval($applyInfo['pcid']);
		//上次操作是恢复帖子，本次是不恢复帖子，需要把帖子删除
		if(($preStatus & Util_Postrecover_Const::OP_STATUS_RECOVER) > 0 && ($opStatus & Util_Postrecover_Const::OP_STATUS_NOT_RECOVER) > 0){
			if($handle_type == 0){
			    $del = Data_Unihandle::deletePost($applyInfo['thread_id'], $postId,  $applyInfo['user_id'], $applyInfo['user_name'], $opUid,  $opUname,  $opIp,  Util_Postrecover_Const::CALL_FROM);
			}else{
				$del = Data_Unihandle::addFold($applyInfo['thread_id'], $postId,  $applyInfo['user_id'], $applyInfo['user_name'], $opUid,  $opUname,  $opIp,  Util_Postrecover_Const::CALL_FROM);
			}
			if($del === false){
                //更新状态为未处理
                $ret = Dl_Postrecover_Postrecover::updateRecoverStatusAudit($applyInfo['apply_id']);
                if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                    Bingo_Log::warning("update apply to undo fail, applyid="
                        . $applyInfo['apply_id'] . ", output=" . json_encode($ret));
                }
				return false;
			}
		}
		if(($opStatus & Util_Postrecover_Const::OP_STATUS_RECOVER) > 0){
			if($handle_type == 0){
				$del = Data_Unihandle::recoverPost($applyInfo['thread_id'], $postId,  $applyInfo['user_id'], $applyInfo['user_name'], $opUid,  $opUname,  $opIp,  Util_Postrecover_Const::CALL_FROM);
			}
			else{
				$del = Data_Unihandle::delFold($applyInfo['thread_id'], $postId,  $applyInfo['user_id'], $applyInfo['user_name'], $opUid,  $opUname,  $opIp,  Util_Postrecover_Const::CALL_FROM);
			}
			if($del === false){
                //更新状态为未处理
                $ret = Dl_Postrecover_Postrecover::updateRecoverStatusAudit($applyInfo['apply_id']);
                if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
                    Bingo_Log::warning("update apply to undo fail, applyid="
                        . $applyInfo['apply_id'] . ", output=" . json_encode($ret));
                }
				return false;
			}
		}
		$input = array(
				'apply_id' => $applyInfo['apply_id'],
				'op_status'=> $opStatus,
				'op_reply' => $reply,
				'op_uid'   => $opUid,
				'op_uname' => $opUname,
				'op_time'  => time(),
		);
		$ret = Dl_Postrecover_Postrecover::updateRecoverStatus($input);
		if($ret['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			Bingo_Log::warning('call dl update recover failed');
			return false;
		}
		if($source == Util_Postrecover_Const::SOURCE_ZHONGSHEN) {
			$inputSpec = array(
				'apply_id' => $applyInfo['apply_id'],
				'op_status'=> $opStatus,	
			);
			Tieba_Service::call('uegmis','pmcmisCall',$inputSpec);	
		}
		self::sendMsg($applyInfo, $reply);
		self::sendMsgApp($applyInfo, $opStatus, $handle_type); // 新增发送申诉结果给贴吧端内消息
		return true;
	}
	
	public static function sendMsg($apply, $reply){
		$postUrl  = sprintf(Util_Postrecover_Const::POST_URL_FORMAT, $apply['thread_id'], 
				$apply['post_id'], $apply['comment_id'], $apply['comment_id']);
		$content  = sprintf(Util_Postrecover_Const::REPLAY_CONTENT_FORMAT, $apply['user_name'], $reply, $postUrl,
				$postUrl, $apply['post_title'], date('Y-m-d H:i:s', $apply['post_time']), $apply['apply_reason']);
		return Data_Sysmsg::sendSysmsg(intval($apply['user_id']), 1, Util_Postrecover_Const::REPLAY_TITLE, $content, Util_Postrecover_Const::CALL_FROM);
		//return Data_Mis::sendMessage(intval($apply['user_id']), $content);
	}

	/***
	 * 申诉处理结果发送贴吧站内消息
	 * @param $apply
	 * @param $opStatus 操作状态 1恢复 2拒绝
	 * @return bool
	 */
	public static function sendMsgApp($apply, $opStatus, $handle_type = 0)
	{
		Bingo_Log::notice("call ".__METHOD__." apply = ".serialize($apply)." opStatus = ".$opStatus);
		// format param
		$threadId   = intval($apply['thread_id']);
		$postId     = intval($apply['post_id']);
		$userId     = intval($apply['user_id']);
		$date       = date('Y年m月d日', $apply['apply_time']); // 申诉时间
//		$delPostUrl = 'http://tieba.baidu.com/mo/q/wise-bawu-core/recycle-station#/recycle-post?noshare=1'; // 回收站连接
		$delPostUrl = 'https://tieba.baidu.com/mo/q/pmc#system';
		$textConfig = array();
		if($handle_type == 0){
			$textConfig = array(
				1 => array(
					'msg_thread' => "您##date##提交的申诉通过审核，贴子##thread_content##已经恢复，点击查看详情{$delPostUrl} 。",
					'msg_post'   => "您##date##提交的申诉通过审核，您在##forum_name##主题贴##thread_content##下发布的回复贴##post_content##已经恢复，点击查看详情{$delPostUrl} 。",
				),
				2 => array(
					'msg_thread' => "您##date##提交的删帖申诉未通过审核，贴子##thread_content##依然是被删除状态，如有异议您可以点击链接继续申诉，我们会为您安排其他审核员{$delPostUrl} 。",
					'msg_post'   => "您##date##提交的删帖申诉未通过审核，您在主题贴##thread_content##下发布的回复贴##post_content##依然是被删除状态，如有异议您可以点击链接继续申诉，我们会为您安排其他审核员{$delPostUrl} 。",
				)
			);
		}else{
			$textConfig = array(
				1 => array(
					'msg_thread' => "您##date##提交的申诉通过审核，贴子##thread_content##已经恢复，点击查看详情{$delPostUrl} 。",
					'msg_post'   => "您##date##提交的申诉通过审核，您在##forum_name##主题贴##thread_content##下发布的回复贴##post_content##已经恢复，点击查看详情{$delPostUrl} 。",
				),
				2 => array(
					'msg_thread' => "您##date##提交的折叠申诉未通过审核，贴子##thread_content##依然是被折叠状态，如有异议您可以点击链接继续申诉，我们会为您安排其他审核员{$delPostUrl} 。",
					'msg_post'   => "您##date##提交的折叠申诉未通过审核，您在主题贴##thread_content##下发布的回复贴##post_content##依然是折叠状态，如有异议您可以点击链接继续申诉，我们会为您安排其他审核员{$delPostUrl} 。",
				)
			);
		}
		
//		$textConfig = array(
//			1 => array(
//				'msg_thread' => "您##date##提交的申诉通过审核，贴子##thread_content##已经恢复。",
//				'msg_post'   => "您##date##提交的申诉通过审核，您在##forum_name##主题贴##thread_content##下发布的回复贴##post_content##已经恢复。",
//			),
//			2 => array(
//				'msg_thread' => "您##date##提交的删帖申诉未通过审核，贴子##thread_content##依然是被删除状态，如有异议您可以到我的-我的贴子-删贴管理-系统删贴页面继续申诉，我们会为您安排其他审核员重新审核。",
//				'msg_post'   => "您##date##提交的删帖申诉未通过审核，您在主题贴##thread_content##下发布的回复贴##post_content##依然是被删除状态，如有异议您可以到我的-我的贴子-删贴管理-系统删贴页面继续申诉，我们会为您安排其他审核员重新审核。",
//			)
//		);
		if(!isset($textConfig[$opStatus]))
		{
			Bingo_Log::warning("call ".__METHOD__." opStatus error, not in [1,2]");
			return false;
		}

		// 获取主题回复内容
		$retryNum   = 1;
		$threadInfo = array();
		$postInfo   = array();
		if($postId)
		{
			for ($i = 0; $i <= $retryNum; $i++)
			{
				$input = array(
					"post_ids" => array(
						0 => $postId
					)
				);
				$res = Tieba_Service::call('post', 'getPostInfo', $input, null, null, 'post', 'php', 'utf-8');
				if($res && $res['errno'] == Tieba_Errcode::ERR_SUCCESS && isset($res['output']['0']))
				{
					$postInfo = $res['output']['0'];
					break;
				}
			}
		}
		if($threadId)
		{
			for($j = 0; $j < $retryNum; $j++)
			{
				$threadRet = self::_mgetThread(array($threadId));
				if(isset($threadRet[$threadId]['first_post_id']))
				{
					$threadInfo = $threadRet[$threadId];
					break;
				}
			}
		}
		if(!$threadInfo && !$postInfo)
		{
			Bingo_Log::warning("call ".__METHOD__." get threadInfo and postInfo fail");
			return false;
		}

		// 拼接msg
		$threadContentTitle = '';
		$postContentTitle   = '';
		$forumName          = '';
		$firstPostId        = 0;
		$msgRuleLen         = 20;
		if($threadInfo)
		{
			// 获取贴子内容
			$arrThreadType = Tieba_Type_Thread::getTypeArray($threadInfo['thread_types']);
			if($arrThreadType['is_movideo'] === true && !$threadInfo['title'])
			{
				$threadContentTitle = "【[视频]】";
			}else{
				if($threadInfo['title'])
				{
					$threadContent = $threadInfo['title'];
				}else{
					$threadContent = $threadInfo['post_content'];
				}
				$threadTextRes = Naf_Component_Forum_FormatDelPostMsg::getDelPostMsgNew($threadContent, $msgRuleLen, true);
				if($threadTextRes['length'] > $msgRuleLen)
				{
					$threadContentTitle = "【{$threadTextRes['text']}...】";
				}else{
					if($threadTextRes['text'])
					{
						$threadContentTitle = "【{$threadTextRes['text']}】";
					}
				}
			}
			$firstPostId = intval($threadInfo['first_post_id']);
		}

		if($postInfo)
		{
			// 验证是否主题贴
			if($firstPostId == intval($postInfo['post_id']))
			{
				// 主题贴
				$msg = $textConfig[$opStatus]['msg_thread'];
			}else{
				// 回复贴
				$postTextRes = Naf_Component_Forum_FormatDelPostMsg::getDelPostMsgNew($postInfo['content'], $msgRuleLen, true);
				if($postTextRes['length'] > $msgRuleLen)
				{
					$postContentTitle = "【{$postTextRes['text']}...】";
				}else{
					if($postTextRes['text'])
					{
						$postContentTitle = "【{$postTextRes['text']}】";
					}
				}
				$forumName = $postInfo['word'] ? "{$postInfo['word']}吧" : '';
				$msg       = $textConfig[$opStatus]['msg_post'];
			}
		}else{
			$msg = $textConfig[$opStatus]['msg_thread'];
		}

		// 组装描述
		$msgNew = str_replace(
			array('##date##', '##thread_content##', '##post_content##', '##forum_name##'),
			array($date, $threadContentTitle, $postContentTitle, $forumName),
			$msg
		);
		Bingo_Log::notice("call ".__METHOD__." msg = {$msg}; msgNew = {$msgNew}");

		// 发送系统消息
		return Data_Sysmsg::sendSysmsgPush(array(
			'user_id'       => $userId,
			'category_id'   => 801001117,
			'category_name' => '删贴小助理',
			'title'         => '申诉结果',
			'content'       => $msgNew,
		));
	}

    /**
     * 批量取主题信息
     * @param $arrThreadId
     * @return array
     */
    private static function _mgetThread($arrThreadId){
        $input = array(
            "thread_ids" => $arrThreadId,
            "need_abstract" => 0,
            "forum_id" => 0,
            "need_photo_pic" => 0,
            "need_user_data" => 0,
            "icon_size" => 0,
            "need_forum_name" => 0, //是否获取吧名
			"need_post_content" => 1, // 新增获取贴子内容
            "call_from" => "uegmis_postrecover" //上游模块名
        );
        $ret   = Tieba_Service::call('post', 'mgetThread', $input, null, null, 'post', 'php', 'utf-8');
        if (!$ret || $ret['errno'] != Tieba_Errcode::ERR_SUCCESS
            || !isset($ret['output']['thread_list'])
            || !is_array($ret['output']['thread_list'])
            || count($ret['output']['thread_list']) == 0){
            return array();
        }
        return $ret['output']['thread_list'];
    }

	private static function _getSignBySignIds($arrSignId){
		$ret = Data_User::getSignBySignIds($arrSignId);
		$signs = array();
		foreach ($ret['signatures'] as $sign){
			$signs[intval($sign['sid'])] = $sign['surl'];
		}
		return $signs;
	}
	private static function _getPostDelInfo($arrInput, $needOpUname = true){
		$ret = Data_Post::getDelPostInfo($arrInput);
		$delInfo = array();
		$arrUid  = array();
		foreach ($ret['delpost_res'] as $row){
			if($needOpUname && (intval($row['source']) == 500 || intval($row['source']) == 0)){
				$row['op_uname'] = Data_Mis::getUnameByUserIdAndSource($row['op_uid'], $row['source'], $row['call_from']);
			}//兼容脏数据，source虽然是500存储的是pass账号
			if($needOpUname && empty($row['op_uname'])){
				$arrUid[] = $row['op_uid'];
			}
			$row['op_time']  = date('Y-m-d H:i:s', $row['op_time']);
		    $row['source_text'] = Util_Postrecover_Common::getDelSourceText($row['source'], $row['call_from']);
			$delInfo['post'][intval($row['post_id'])] = $row;
		}
		foreach ($ret['delthread_res'] as $row){
			if($needOpUname && (intval($row['source']) == 500 || intval($row['source']) == 0)){
				$row['op_uname'] = Data_Mis::getUnameByUserIdAndSource($row['op_uid'], $row['source'], $row['call_from']);
			}
			if($needOpUname && empty($row['op_uname'])){
				$arrUid[] = $row['op_uid'];
			}
			$row['op_time']  = date('Y-m-d H:i:s', $row['op_time']);
			$row['source_text'] = Util_Postrecover_Common::getDelSourceText($row['source'], $row['call_from']);
			$delInfo['thread'][intval($row['thread_id'])] = $row;
		}
		if($needOpUname && count($arrUid) > 0){
			$ret = Data_User::getUnameByUids($arrUid);
			$arrUser = array();
			foreach ($ret['unames'] as $row){
				$arrUser[intval($row['user_id'])] = $row['user_name'];
			}
			foreach ($delInfo['post'] as &$row){
				if(empty($row['op_uname']) && array_key_exists(intval($row['op_uid']), $arrUser)){
					$row['op_uname'] = $arrUser[intval($row['op_uid'])];
				}//兼容脏数据，op_uid存储的不是uuap的用户id也不是pass的id
				if(empty($row['op_uname'])){
					$row['op_uname'] = 'uid:' . $row['op_uid'];
				}
			}
			foreach ($delInfo['thread'] as &$row){
				if(empty($row['op_uname']) && array_key_exists(intval($row['op_uid']), $arrUser)){
					$row['op_uname'] = $arrUser[intval($row['op_uid'])];
				}
				if(empty($row['op_uname'])){
					$row['op_uname'] = 'uid:' . $row['op_uid'];
				}
			}
		}
		return $delInfo;
	}
	/**
	 * 获取数量
	 */
	public static function getRecoverCount($arrInput){
		return Dl_Postrecover_Postrecover::getRecoverCount($arrInput);
	}
	
	public static function getAuditlog($arrInput){
		$ret = Dl_Postrecover_Auditlog::getAuditlog($arrInput);
		$result = array();
		foreach ($ret['ret'] as $row){
			$row['op_time'] =  date('Y-m-d H:i:s',$row['op_time']);
			$row['op_status_text'] = Util_Postrecover_Common::GetStatusText($row['op_status']);
			$result[]  = $row;
		}
		return self::_succRet($result);
	}
	
	public static function assignRecover($arrInput){
		return Dl_Postrecover_Postrecover::assignRecover($arrInput);
	}
	public static function freeAssign($arrInput){
		return Dl_Postrecover_Postrecover::freeAssign($arrInput);
	}
	
	public static function exportRecover($arrInput){
        $dbData = Dl_Postrecover_Postrecover::exportRecover($arrInput);
        if($dbData['errno'] !== Tieba_Errcode::ERR_SUCCESS){
            return $dbData;
        }
        $delPost = array();
        foreach ($dbData['ret'] as $key=>$row){
            $delPost[floor($key / 40)][] = array('thread_id'=> intval($row['thread_id']), 'post_id'=>intval($row['pcid']));
        }

        $opUser = $delUser = $delUserInfo = array();
        $delPostInfoList = array('post'=>array(), 'thread'=>array());
        // 分组取删帖信息
        if(count($delPost) > 0){
        	foreach ($delPost as $postids){
                $ret = Data_Post::getDelPostInfo($postids);
                if ($ret){
                	if ($ret['delpost_res']){
                		foreach ($ret['delpost_res'] as $postid=>$value){
                            $delPostInfoList['post'][$postid] = $value;
                            $opUser[$value['op_uid']] = $value['op_uid'];
						}
					}
                    if ($ret['delthread_res']){
                        foreach ($ret['delthread_res'] as $threadid=>$value){
                            $delPostInfoList['thread'][$threadid] = $value;
                            $opUser[$value['op_uid']] = $value['op_uid'];
						}
                    }
				}
			}
        }
        // 分组取用户id
		$key = 0;
		foreach ($opUser as $userid){
            $delUser[floor($key / 40)][] = $userid;
            $key++;
		}
        foreach ($delUser as $ids){
            $input = array(
                "user_id" => $ids
            );
            $res = Tieba_Service::call('user', 'mgetUserData', $input, null, null, 'post', 'php', 'utf-8');
            if ($res && $res['errno'] == 0 && $res['user_info']){
            	foreach ($res['user_info'] as $userid=>$userinfo){
                    $delUserInfo[$userid] = $userinfo['user_name'];
				}
			}
		}

        foreach ($dbData['ret'] as &$row){
            //$row['post_content'] = str_replace('imgsrc.baidu.com', 'cq01-pic-favo11.cq01.baidu.com:8080', $row['post_content']);
            $delpost_info = array();
            if(intval($row['post_no']) > 1 && array_key_exists(intval($row['pcid']), $delPostInfoList['post'])){
                $delpost_info = $delPostInfoList['post'][intval($row['pcid'])];
            }elseif (intval($row['post_no']) === 1 && array_key_exists(intval($row['thread_id']), $delPostInfoList['thread'])){
                $delpost_info = $delPostInfoList['thread'][intval($row['thread_id'])];
            }
            if ($delpost_info){
                $row['del_uid'] = $delpost_info['op_uid'];
                $row['del_uname'] = isset($delUserInfo[$delpost_info['op_uid']]) ? $delUserInfo[$delpost_info['op_uid']] : '';
                $row['del_time'] = $delpost_info['op_time'];
                $row['del_call_from'] = $delpost_info['call_from'];
                $row['del_source_text'] = Util_Postrecover_Common::getDelSourceText($delpost_info['source'], $delpost_info['call_from']);
                $row['del_monitor_type'] = $delpost_info['monitor_type'];
			}
        }
		return $dbData;
	}
	
	public static function checkPostInfo($tid, $pid, $cid){
		$comments = array();
		if($cid > 0){
			$comments[] = array(
				'thread_id' => $tid, 'post_id'   => $pid, 'comment_id'=> $cid,
			);
		}
		$check = Data_Post::getMaskInfo(array($tid), array($pid), $comments);
		if($check === false){
			return false;
		}
		if(!isset($check['threads_mask_status'][$tid]['is_key_deleted']) 
		|| !isset($check['posts_mask_status'][$pid]['is_key_deleted'])){
			Bingo_Log::warning("tid = $tid, pid = $pid get maskinfo empty. output= " .serialize($check));
			return false;
		}
		$threadDelete  = $check['threads_mask_status'][$tid]['is_key_deleted'];
		$postDelete    = $check['posts_mask_status'][$pid]['is_key_deleted'];
		$commentDelete = 0;
		if($cid > 0){
			if(!isset($check['subpost_mask_status'][$cid]['is_key_deleted'])){
				Bingo_Log::warning("tid = $tid, pid = $pid get maskinfo empty. output= " .serialize($check));
				return false;
			}
			$commentDelete = $check['subpost_mask_status'][$cid]['is_key_deleted'];
		}
		return array(
			'thread_delete'  => $threadDelete,
			'post_delete'    => $postDelete,
		    'comment_delete' => $commentDelete
		);
	}	
	
}

?>
