<?php
class Service_Bawusearch_Bawusearch {
	public static $service_ie = 'utf-8';
	private static function _errRet($errno) {
		return array (
				'errno'  => $errno, 
				'errmsg' => Tieba_Error::getErrmsg( $errno ) ,
		);
	}
	private static function _succRet($ret) {
		$error = Tieba_Errcode::ERR_SUCCESS;
		return array (
				'errno' => $error, 
				'errmsg'=> Tieba_Error::getErrmsg ( $error ), 
				'ret'   => $ret );
	}
	
	/**
	 * 查询操作记录
	 * @param array $arrInput
	 */
	public static function search($arrInput){
		$out = array(
			'data' => array(),
			'total' => 0,
		);
		if(isset($arrInput['op_userid'])){//查询操作人记录
			if(isset($arrInput['forum_id'])){
				$resCount = Dl_Bawusearch_Bawumemo::getTotalCount($arrInput);
				if($resCount['errno'] !== Tieba_Errcode::ERR_SUCCESS){
					return $resCount;
				}
				$total = intval($resCount['ret']);
				if($total <= 0){
					return self::_succRet($out);
				}
				$res = Dl_Bawusearch_Bawumemo::getDataList($arrInput);
				if($res['errno'] !== Tieba_Errcode::ERR_SUCCESS){
					return $res;
				}
				$dataList = $res['ret'];
			}else{
				$data = self::getDataByOpUid($arrInput);
				if(false === $data){
					return self::_errRet(Tieba_Errcode::ERR_CALL_USER_FUNC_FAIL);
				}
				$total = $data['total'];
				$dataList = $data['data'];
			}			
		}
		if(isset($arrInput['oo_userid'])){//查询被操作人记录
			$resCount = Dl_Bawusearch_Bawumemo::getTotalCount($arrInput);
			if($resCount['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				return $resCount;
			}
			$total = intval($resCount['ret']);
			if($total <= 0){
				return self::_succRet($out);
			}
			$res = Dl_Bawusearch_Bawumemo::getDataList($arrInput);
			if($res['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				return $res;
			}
			$dataList = $res['ret'];
		}
		if(isset($arrInput['forum_id']) && !isset($arrInput['oo_userid']) && !isset($arrInput['op_userid'])){//查询吧维度操作记录
			$resCount = Dl_Bawusearch_Bawumemo::getTotalCount($arrInput);
			if($resCount['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				return $resCount;
			}
			$total = intval($resCount['ret']);
			if($total <= 0){
				return self::_succRet($out);
			}
			$res = Dl_Bawusearch_Bawumemo::getDataList($arrInput);
			if($res['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				return $res;
			}
			$dataList = $res['ret'];
		}
		$out = array(
			'data' => self::getFormatData($dataList),
			'total' => $total,
		);
		
		return self::_succRet($out);
	}
	
	
	protected static function getFormatData($arrInput){
		foreach($arrInput as $key => $info){
			$arrInput[$key]['forum_name'] = Data_Forum::getFnameByFid(intval($info['forum_id']));
			$arrInput[$key]['op_type'] = Util_Bawusearch_Const::$cmdToChsOpType[intval($info['cmd_core_no'])];
			if($info['oo_postid'] > 0){//对象为帖子
				$arrInput[$key]['op_object'] = self::_getAbstract($info);
			}else{
				$arrInput[$key]['op_object'] = $info['oo_user'];
			}
		}
		return $arrInput;
	}
	
	protected static function _getAbstract($arrInput){
		$abstract = '';
		$title = $arrInput['oo_title'];
		//$title = substr($arrInput['oo_title'], 0,15);
		$tid = $arrInput['oo_threadid'];
		$pid = $arrInput['oo_postid'];
		$abstract = "【标题】$title...\n" . " tid: $tid pid: $pid";
		return $abstract;
	}
	
	public static function getDataByOpUid($arrInput){
		$return = array(
			'total' => 0,
			'data' => array(),
		);
		$res = Dl_Bawusearch_BawumemoUser::getRecordsByOpuid($arrInput);
		if($res['errno'] !== Tieba_Errcode::ERR_SUCCESS){
			return false;
		}
		if(empty($res['ret'])){
			Bingo_Log::warning("getRecordsByOpuid no found:".serialize($arrInput));
			return $return;
		}
		$arrForumIds = array();
		foreach($res['ret'] as $data){
			if(in_array($data['forum_id'],$arrForumIds)){
				continue;
			}
			$arrForumIds[] = $data['forum_id'];
		}
		foreach($arrForumIds as $fid){
			$arrInput['forum_id'] = intval($fid);
			$resCount = Dl_Bawusearch_Bawumemo::getTotalCount($arrInput);
			if($resCount['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				return $resCount;
			}
			$total = intval($resCount['ret']);
			$return['total'] = $return['total'] + $total;
			$res = Dl_Bawusearch_Bawumemo::getDataList($arrInput);
			if($res['errno'] !== Tieba_Errcode::ERR_SUCCESS){
				return false;
			}
			$return['data'] = array_merge($return['data'],$res['ret']);
		}
		return $return;
	}
	
}