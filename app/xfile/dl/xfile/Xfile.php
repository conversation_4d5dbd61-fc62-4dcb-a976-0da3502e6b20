<?php
/**
 * @copyright Copyright (c) www.baidu.com
 * <AUTHOR>
 * @date 2015:02:09 11:29:14
 * @version 
 * @structs & methods(copied from idl.)
*/



define("MODULE","Xfile_dl");
class Dl_Xfile_Xfile{

const SERVICE_NAME = "Dl_Xfile_Xfile";
protected static $_conf = null;
protected static $_use_split_db = false;
const DATABASE_NAME = "forum_ueg_profile";

const UTF8 = 'utf8';
const GBK = 'gbk';

const NUM_MOD = 128;

const DB_TABLE_USER_PRE = 'profile_user_';
const DB_TABLE_FMUSER = 'profile_fmuser';
const DB_TABLE_POST = 'profile_post';
const DB_TABLE_FMPOST = 'profile_fmpost';

const DB_SELECT = 'select';
const DB_INSERT = 'insert';
const DB_MULTI_INSERT = 'multi_insert';
const DB_UPDATE = 'update';
const DB_DELETE = 'delete';
const DB_CREATE_TABLE = 'create';
const DB_DROP_TABLE = 'drop';

/**
 * @brief get mysql obj.
 * @return: obj of Bd_DB, or null if connect fail.

**/		
private static function _getDB(){
    $objTbMysql = Tieba_Mysql::getDB(self::DATABASE_NAME);
    if($objTbMysql && $objTbMysql->isConnected()) {
        return $objTbMysql;
    } else {
    	Bingo_Log::warning("db connect fail.");
        return null;
    }
}

	
/**
 * @brief init
 * @return: true if success. false if fail.

**/		
private static function _init(){
	
	//add init code here. init will be called at every public function beginning.
	//not a good idea to init db or cache here. just call _getDB or _getCache when you really need it.
	//init should be recalled for many times.
	
	if(self::$_conf == null){	
		self::$_conf = Bd_Conf::getConf("/app/xfile/dl_xfile_xfile");
		if(self::$_conf == false){
			Bingo_Log::warning("init get conf fail.");
			return false;
		}
		
	}
	return true; 
}


private static function _errRet($errno){
    return array(
        'errno' => $errno,
        'errmsg' => Tieba_Error::getErrmsg($errno),
    );
}

private static function _succRet($out = array())
{
	return array
	(
		'errno' => Tieba_Errcode::ERR_SUCCESS,
		'errmsg' => '',
		'res' => $out ,
	);
}

public static function preCall($arrInput){
    // pre-call hook
}

public static function postCall($arrInput){
    // post-call hook
}

public static function selectPost($arrInput)
{
	$in = array();
	$in['dbMethod'] = self::DB_SELECT ;
	$in['table'] = self::DB_TABLE_POST ;
	$in['fields'] = array('*');
	$in['conds'] = isset($arrInput['conds']) ? $arrInput['conds'] : null ;
	$in['appends'] = isset($arrInput['appends']) ? $arrInput['appends'] : null ;
	
	$out = self::operateDB($in);
	return $out ;
}

public static function selectCountPost($arrInput)
{
	$in = array();
	$in['dbMethod'] = self::DB_SELECT ;
	$in['table'] = self::DB_TABLE_POST ;
	$in['fields'] = array('COUNT(*)');
	$in['conds'] = isset($arrInput['conds']) ? $arrInput['conds'] : null ;
	$in['appends'] = isset($arrInput['appends']) ? $arrInput['appends'] : null ;
	
	$out = self::operateDB($in);
	return $out ;
}

public static function selectFmpost($arrInput)
{
	$in = array();
	$in['dbMethod'] = self::DB_SELECT ;
	$in['table'] = self::DB_TABLE_FMPOST ;
	$in['fields'] = array('*');
	$in['conds'] = isset($arrInput['conds']) ? $arrInput['conds'] : null ;
	$in['appends'] = isset($arrInput['appends']) ? $arrInput['appends'] : null ;
	
	$out = self::operateDB($in);
	return $out ;
}

public static function selectFmuser($arrInput)
{
	$in = array();
	$in['dbMethod'] = self::DB_SELECT ;
	$in['table'] = self::DB_TABLE_FMUSER ;
	$in['fields'] = array('*');
	$in['conds'] = isset($arrInput['conds']) ? $arrInput['conds'] : null ;
	$in['appends'] = isset($arrInput['appends']) ? $arrInput['appends'] : null ;

	$out = self::operateDB($in);
	return $out ;
}

public static function selectUserByUid($userId)
{
	$in = array();
	$in['dbMethod'] = self::DB_SELECT ;
	$in['table'] = self::DB_TABLE_USER_PRE . intval($userId) % self::NUM_MOD ;
	$in['fields'] = array('*');
	$in['conds'] = array( 'user_id=' => intval($userId));
	$in['appends'] = null ;
	
	$out = self::operateDB($in);
	return $out ;
}

public static function insertUser($arrInput)
{
	$userId = intval( $arrInput['rows']['user_id'] );
	
	$in = array();
	$in['dbMethod'] = self::DB_INSERT ;
	$in['table'] = self::DB_TABLE_USER_PRE . intval($userId) % self::NUM_MOD ;
	$in['rows'] = $arrInput['rows'];
	
	$out = self::operateDB($in);
	return $out ;
}

public static function insertPost($arrInput)
{
	$in = array();
	$in['dbMethod'] = self::DB_INSERT ;
	$in['table'] = self::DB_TABLE_POST ;
	$in['rows'] = $arrInput['rows'];
	
	$out = self::operateDB($in);
	return $out ;
}

public static function insertFmpost($arrInput)
{
	$in = array();
	$in['dbMethod'] = self::DB_INSERT ;
	$in['table'] = self::DB_TABLE_FMPOST ;
	$in['rows'] = $arrInput['rows'];
	
	$out = self::operateDB($in);
	return $out ;
}

public static function insertFmuser($arrInput)
{
	$in = array();
	$in['dbMethod'] = self::DB_INSERT ;
	$in['table'] = self::DB_TABLE_FMUSER ;
	$in['rows'] = $arrInput['rows'];
	
	$out = self::operateDB($in);
	return $out ;
}

private static function operateDB($arrInput)
{
	//Bingo_Log::trace('op input['.var_export($arrInput,1));//qmy

	$db = self::_getDB();
	if( null === $db )
	{
		Util_Const::ueg_warning( array('ERR' => Util_Const::GET_DB_FAIL,'FUNC' => __FUNCTION__ ,'REQ' => $arrInput ) );
		return self::_errRet(Tieba_Errcode::ERR_DB_CONN_FAIL);
	}

	if( ! $db->charset( self::UTF8 ) ){
		Util_Const::ueg_warning( array('ERR'=>'charset fail'));
	}
	
	Bingo_Timer::start('operateDb');
	switch ($arrInput['dbMethod'])
	{
		case self::DB_SELECT:
			$table = $arrInput['table'];
			$fields = $arrInput['fields'];
			$conds = $arrInput['conds'];
			$appends = $arrInput['appends'];
				
			$resDB = $db->select( $table,$fields,$conds,null,$appends );
			break;
				
		case self::DB_INSERT:
			$table = $arrInput['table'];
			$rows = $arrInput['rows'];
				
			$resDB = $db->insert( $table,$rows );
			break;
		
		case self::DB_MULTI_INSERT :
			/*
			 * values 顺序有关，需要保证顺序！
			 */
			$arrTmp = array();
			foreach ($arrInput['values'] as $k => $v){
				foreach ($v as $v1 => $i1){
					
					if( is_string($i1)){
						$arrTmp[$k][$v1] = '\'' . $db->escapeString($i1) . '\'';		// 自己转码
					}
					elseif( is_int($i1)){
						$arrTmp[$k][$v1] = intval($i1) ;
					}
					else{
						$arrTmp[$k][$v1] = '\'' . $i1 . '\'';
					}
				}
			}
			
			foreach ($arrTmp as $k => $v){
				ksort($arrTmp[$k]);
			}
			
			$arrTmpV = array();
			foreach($arrTmp as $k => $v){
				$strV = implode(',', $v );
				$arrTmpV []= ' ( ' . $strV . ' ) ' ;
			}
			$strTmp = implode(',', $arrTmpV ) ;
			
			$strRows = ' ( ' . implode(',' , array_keys($arrTmp[0]) ) . ' ) ' ;	// 排序后以第0个key 为准
			$table = $arrInput['table'] ;
			$sql = 'INSERT INTO ' . $table . $strRows . ' VALUES ' . $strTmp . ' ; ';
			$resDB = $db->query( $sql );
			break;
			
		case self::DB_UPDATE:
			$table = $arrInput['table'];
			$rows = $arrInput['rows'];
			$conds = $arrInput['conds'];
				
			$resDB = $db->update( $table,$rows,$conds );
			break;
		case self::DB_DELETE :
			$table = $arrInput['table'];
			$conds = $arrInput['conds'];
			$appends = $arrInput['appends'];
			$resDB = $db->delete( $table , $conds , null , $appends );
			break;
		case self::DB_CREATE_TABLE :
			$table = $arrInput['table'];
			$appends = $arrInput['appends'];
			$sql = 'CREATE TABLE ' . $table . $appends  . ' ;';
			$resDB = $db->query( $sql );
			break;

		case self::DB_DROP_TABLE :
			$table = $arrInput['table'] ;
			$sql = 'DROP TABLE IF EXISTS ' . $table . ' ;';
			$resDB = $db->query( $sql );
			break;
		
		default :
			return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
			break;
	}

	Bingo_Timer::end('operateDb');
	if( false === $resDB )
	{
		Util_Const::ueg_warning( array('ERR' => Util_Const::QUERY_DB_FAIL , 'SQL' => $db->getLastSQL() , 'ERRNO' => $db->errno() , 'ERRMSG' => $db->error()) );	
		$arrOutput = array();
		$arrOutput['errno'] = $db->errno();
		$arrOutput['errmsg'] = $db->error();
		$arrOutput['res'] = $resDB;
		return $arrOutput;
	}

	//Bingo_Log::trace('lastsql[' . serialize($db->getLastSQL()) );//qmy

	$arrOutput = array();
	$arrOutput['errno'] = Tieba_Errcode::ERR_SUCCESS;
	$arrOutput['errmsg'] = '';
	$arrOutput['res'] = $resDB;
	return $arrOutput;
}

}
