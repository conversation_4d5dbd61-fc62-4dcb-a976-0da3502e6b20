<?php
class indexAction extends Bingo_Action_Abstract {

	protected $_user_info = array();

    /**
     * @return bool
     */
	public function init(){
// 		$this->_getUserInfo();
		return true;
	}

    /**
     * execute
     */
	public function execute(){
		Bingo_Page::setTpl('ota.php');
	}

    /**
     * @return array
     */
	private function _getUserInfo() {
		if($this->_user_info['is_login'] == false) {
			$this->_user_info = UserAuth::getUserInfo();
		}
		return $this->_user_info;
	}
}


