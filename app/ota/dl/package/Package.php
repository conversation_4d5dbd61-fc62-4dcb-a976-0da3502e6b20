<?php

define("MODULE","package_dl");

class Dl_Package_Package extends BaseService{

	const SERVICE_NAME			= "Dl_Package_Package";
	const DATABASE_NAME			= 'forum_automation';
	const TABLE_NAME_APP 		= 'app';
	const TABLE_NAME_PACKAGE 	= 'package';
	const NO_RECORD				= 'no record';
	const PAGESIZE 				= 20;
	
	/**
	 * getPackageInfo
	 * <AUTHOR>
	 * @param unknown $arrInput
	 * @return
	 */
	public static function getPackageDetailInfo($arrInput){
		
		if(!self::_init(self::SERVICE_NAME)){
			return self::_errRet(BaseError::ERR_LOAD_CONFIG_FAIL);
		}
		$db = self::_getDB();
		if (!$db) {
			return self::_errRet(BaseError::ERROR_MYSQL_ACCESS_FAIL);
		}
		$res = $db->select(self::TABLE_NAME_PACKAGE, $arrInput['arrFields'], $arrInput['conditions']);
		if(false === $res){
			Bingo_Log::warning("get record fail! [output:".serialize($res)."error:".$db->error()."sql:".$db->getLastSQL()."]");
			return self::_errRet(BaseError::ERR_MYSQL_QUERY_FAIL);
		}
		if($db->getAffectedRows() == 0){
			$res['data']= self::NO_RECORD;
		}
		$error = BaseError::ERR_SUCCESS;
		$arrOutput = array(
			'errno' => $error,
			'errmsg' => BaseError::getErrmsg($error),
			'data' => $res,
		);

		return $arrOutput;
	}

	/**
	 * getPackageList
	 * <AUTHOR>
	 * @param unknown $arrInput
	 * @return
	 */
	public static function getPackageList($arrInput){
		$arrList = array();
		if(!self::_init(self::SERVICE_NAME)){
			return self::_errRet(BaseError::ERR_LOAD_CONFIG_FAIL);
		}
		$db = self::_getDB();
		if (!$db) {
			return self::_errRet(BaseError::ERROR_MYSQL_ACCESS_FAIL);
		}
		
		$res = $db->select(self::TABLE_NAME_PACKAGE, $arrInput['arrFields'], $arrInput['conditions']);
		
		if(false === $res){
			Bingo_Log::warning("get record fail! [output:".serialize($res)."error:".$db->error()."sql:".$db->getLastSQL()."]");
			return self::_errRet(BaseError::ERR_MYSQL_QUERY_FAIL);
		}
		if($db->getAffectedRows() == 0){
			$res['data']='no record';
		}
		$error = BaseError::ERR_SUCCESS;
		$arrList['list'] = $res;
		$arrOutput = array(
			'errno' => $error,
			'errmsg' => BaseError::getErrmsg($error),
			'data' => $arrList,
		);
	
		return $arrOutput;
	}
	
	/**
	 * updatePackageInfo
	 * <AUTHOR>
	 * @param unknown $arrInput
	 * @return
	 */
	public static function updatePackageInfo($arrInput){
		if(!self::_init(self::SERVICE_NAME)){
			return self::_errRet(BaseError::ERR_LOAD_CONFIG_FAIL);
		}
		$db = self::_getDB();
		if (!$db) {
			return self::_errRet(BaseError::ERROR_MYSQL_ACCESS_FAIL);
		}
        $res = $db->update(self::TABLE_NAME_PACKAGE, $arrInput['arrFields'], $arrInput['conditions']);
       
        if(false === $res){
            Bingo_Log::warning("update record fail! [output:".serialize($res)."error:".$db->error()."sql:".$db->getLastSQL()."]");
            return self::_errRet(BaseError::ERR_MYSQL_QUERY_FAIL);
        }
        

		$error = BaseError::ERR_SUCCESS;
		$arrOutput = array(
			'errno' => $error,
			'errmsg' => BaseError::getErrmsg($error),
			'data' => array(
				'num'=>$db->getAffectedRows(),
			),
		);

		return $arrOutput;
	}
	
	/**
	 * updatePackageInfo
	 * <AUTHOR>
	 * @param unknown $arrInput
	 * @return
	 */
	public static function insertPackageInfo($arrInput){
		Bingo_Log::warning("%%%%%".print_r($arrInput,1));
		if(!self::_init(self::SERVICE_NAME)){
			return self::_errRet(BaseError::ERR_LOAD_CONFIG_FAIL);
		}
		$db = self::_getDB();
		if (!$db) {
			return self::_errRet(BaseError::ERROR_MYSQL_ACCESS_FAIL);
		}
		$res = $db->insert(self::TABLE_NAME_PACKAGE, $arrInput['arrFields']);
		Bingo_Log::warning("sql:".$db->getLastSQL());
		if(false === $res || $db->getAffectedRows() == 0){
			Bingo_Log::warning("insert record fail! [output:".serialize($res)."error:".$db->error()."sql:".$db->getLastSQL()."]");
			return self::_errRet(BaseError::ERR_MYSQL_QUERY_FAIL);
		}
		$error = BaseError::ERR_SUCCESS;
		$arrOutput = array(
			'errno' => $error,
			'errmsg' => BaseError::getErrmsg($error),
			'data' => array(
				'p_id' => $db->getInsertID()
			),
		);
		return $arrOutput;
	}
	
	public static function queryCountPackage($arrInput){
		if(!($arrInput && is_array($arrInput) && isset($arrInput['conditions']))){
			BaseLog::warning( ' input params invalid! Params : ' . serialize($arrInput));
			return self::_errRet(BaseError::ERR_PARAM_ERROR);
		}
		if(!self::_init(self::SERVICE_NAME)){
			return self::_errRet(BaseError::ERR_LOAD_CONFIG_FAIL);
		}
		$db = self::_getDB();
		if (!$db) {
			return self::_errRet(BaseError::ERROR_MYSQL_ACCESS_FAIL);
		}
		
		$res = $db->selectCount(self::TABLE_NAME_PACKAGE, $arrInput['conditions']);
		if(false === $res){
			Bingo_Log::warning("select package count fail! [output:".serialize($res)."error:".$db->error()."sql:".$db->getLastSQL()."]");
			return self::_errRet(BaseError::ERR_MYSQL_QUERY_FAIL);
		}
		
		$error = BaseError::ERR_SUCCESS;
		$arrOutput = array(
			'errno' => $error,
			'errmsg' => BaseError::getErrmsg($error),
			'count' => $res,
		);
		
		return $arrOutput;
	}
	
	
	public static function getPackageVersionAndType($arrInput){
		if(!($arrInput && is_array($arrInput))){
			BaseLog::warning( ' input params invalid! Params : ' . serialize($arrInput));
			return self::_errRet(BaseError::ERR_PARAM_ERROR);
		}
		if(!self::_init(self::SERVICE_NAME)){
			return self::_errRet(BaseError::ERR_LOAD_CONFIG_FAIL);
		}
		$db = self::_getDB();
		if (!$db) {
			return self::_errRet(BaseError::ERROR_MYSQL_ACCESS_FAIL);
		}
		$res = $db->select(self::TABLE_NAME_PACKAGE,$arrInput['arrFields']);
	
		if(false === $res){
			Bingo_Log::warning("select package p_type & p_version fail! [output:".serialize($res)."error:".$db->error()."sql:".$db->getLastSQL()."]");
			return self::_errRet(BaseError::ERR_MYSQL_QUERY_FAIL);
		}
	
		$error = BaseError::ERR_SUCCESS;
		$arrOutput = array(
				'errno' => $error,
				'errmsg' => BaseError::getErrmsg($error),
				'data' => $res,
		);
		
		return $arrOutput;
	}
}

