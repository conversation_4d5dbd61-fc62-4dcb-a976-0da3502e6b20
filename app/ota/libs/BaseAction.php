<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file BaseAction.php
 * <AUTHOR>
 * @date 2015/02/26 14:46:34
 * @brief BaseAction Class
 *
 **/
abstract class BaseAction extends Bingo_Action_Abstract {

    protected $_intErrorNo = 0;
    protected $_strError = 'success';
    protected $_strTemplate = '';
    protected $_arrTpl = array ();
    protected $_isAPI = false;
    protected $_format = 'json';
    protected $_user_info = array();
    protected $_isPost = false;
    protected $_isToken = false;
    protected $_isJson = false;
    protected $_token = '';
    protected $_inputParamArray = array();
    protected $_outputParamArray = array();

    public function init(){
        $this->_getUserInfo();
        return true;
    }

    protected function _tbsCheck($strTbs) {
        if(!Tieba_Tbs::check($strTbs, true)){
            return false;
        }
        return true;
    }

    public function execute() {
        Bingo_Timer::start("cost_time");
        $this->_getPrivateInfo();
        //验证请求合法性
        if($this->_isToken && !TokenAuth::checkToken($this->_inputParamArray, Bingo_Http_Request::get('t_token',''))) {
            BaseLog::warning( get_class($this) . ' the request is not legitimate');
            $this->_intErrorNo = BaseError::ERR_TOKEN_FAIL;
            return $this->_renderJson();
        }
        //验证请求模式
        if($this->_isPost && !Bingo_Http_Request::isPost()) {
            BaseLog::warning( get_class($this) . ' request no support get!');
            $this->_intErrorNo = BaseError::ERR_NO_SUPPORT_GET;
            return $this->_renderJson();
        }
        //验证自定义参数校验
        if($this->_checkPrivate()) {
            $result = $this->_execute();
        }else {
            BaseLog::warning( get_class($this) . ' input params invalid! Params : ' . serialize($this->_inputParamArray));
            $this->_intErrorNo = BaseError::ERR_PARAM_ERROR;
            return $this->_renderJson();
        }
        if($result) $this->_buildData($result);
        return $this->_renderJson();
    }

    protected function _getUserInfo() {
        if(!$this->_isAPI && $this->_user_info['is_login'] == false) {
            $this->_user_info = UserAuth::getUserInfo();
        }
        return $this->_user_info;
    }

    protected function _getTokenInfo() {
        $this->_token = Bingo_Http_Request::get('token','');

    }

    protected function _renderJson() {
        Bingo_Timer::end("cost_time");
        $this->_outputParamArray ? $this->_arrTpl ['data'] = $this->_outputParamArray : array();
        $this->_arrTpl ['data']['user_info'] = $this->_user_info;
	    $this->_arrTpl ['errno']  = $this->_intErrorNo;
	    $this->_arrTpl ['errmsg'] = BaseError::getErrmsg($this->_intErrorNo);
        $this->_arrTpl ['cost_time'] = Bingo_Timer::calculate("cost_time");
	    $this->_arrTpl ['ie'] = 'utf-8';
        Bingo_Http_Response::contextType ('application/json');
        if($this->_isAPI || $this->_isJson) {
            echo Bingo_String::array2json($this->_arrTpl);
        }else {
            Bingo_Page::assign ( $this->_arrTpl );
            Bingo_Page::setOnlyDataType ( "json" );
        }
	    return $this->_intErrorNo == BaseError::ERR_SUCCESS;
    }

    protected function _renderView() {
	    $userInfo = $this->_getUserInfo();
	    $this->_arrTpl = array_merge ( $this->_arrTpl, array ( 'user_info' => $userInfo ) );
	    $this->_arrTpl ['errno']  = $this->_intErrorNo;
	    $this->_arrTpl ['errmsg'] = BaseError::getErrmsg($this->_intErrorNo);
	    Bingo_Page::assign ( $this->_arrTpl );      
	    Bingo_Page::setTpl ( $this->_strTemplate );
	    return $this->_intErrorNo == BaseError::ERR_SUCCESS;
    }

    //主要业务逻辑
    abstract protected function _execute();
    //获取参数
    abstract protected function _getPrivateInfo();
    //参数校验
    abstract protected function _checkPrivate();
    //构造输出
    abstract protected function _buildData($result);
}
