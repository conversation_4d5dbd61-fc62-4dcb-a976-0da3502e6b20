<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file Threadlink.php
 * <AUTHOR>
 * @date 2017/05/16 14:26:36
 * @brief
 *
 **/
class Service_Threadshare_Threadshare
{

    // 单吧贴命令号
    const CMD_THREAD_COMMIT = 1; // 发贴
    const CMD_DEL_THREAD = 12; // 删帖（删单吧贴或删个人中心贴）

    // 多吧贴命令号
    const CMD_PERSON_THREAD_COMMIT = 10013;
    const CMD_TRANS_THREAD_COMMIT = 10008; // 转发贴
    const CMD_DEL_TRANS_THREAD = 10009; // 删除转发贴

    private static $_arrCmd = array(
        self::CMD_THREAD_COMMIT,
        self::CMD_DEL_THREAD,
        self::CMD_PERSON_THREAD_COMMIT,
        self::CMD_TRANS_THREAD_COMMIT,
        self::CMD_DEL_TRANS_THREAD,
    );

    private static $_objMulti = null;
    // 发贴吧id
    private static $_intForumId = 0;
    // 发贴吧名
    private static $_strForumName = '';
    // 发贴用户id
    private static $_intUserId = 0;
    // 转发贴原贴tid
    private static $_intOriginalTid = 0;

    /**
     * 接nmq入口方法
     * @param $arrInput
     * @return array|bool
     */
    public static function dealThreadshareNmq($arrInput)
    {
        $data      = Tieba_Service::getArrayParams($arrInput, 'data');
        $intNmqCmd = isset($data['command_no']) ? intval($data['command_no']) : -1;
        if (!in_array($intNmqCmd, self::$_arrCmd)) {
            Bingo_Log::warning('input error ' . serialize($arrInput));
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        switch ($intNmqCmd) {
            //发帖命令
            case self::CMD_THREAD_COMMIT:
            case self::CMD_PERSON_THREAD_COMMIT:
            case self::CMD_TRANS_THREAD_COMMIT:
                self::_dealCommitThread($data);
                self::_dealRepost2Dynamic($data);
                break;
            // 删贴命令
            case self::CMD_DEL_THREAD:
                $addMask = self::_addMaskShareTid($data);
                if (false == $addMask || $addMask['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('call addMaskUserThread failed');
                    return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
                if ($data['is_thread']) {
                    self::_dealDelThread($data);
                }
                break;
            case self::CMD_DEL_TRANS_THREAD:
                $addMaskMulti = self::_addMaskShareTidMulti($data);
                if (false == $addMaskMulti || $addMaskMulti['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('call addMaskUserThread failed');
                    return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
                self::_setMsg($data);  //9.2 ueg需求，我的贴子页增加 贴子回收站红点
                break;
            default :
                break;
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 获取原贴信息
     * @param $arrInput
     * @return array
     */
    public static function getOriginThreadInfo($arrInput)
    {
        if (empty($arrInput['tids']) || !is_array($arrInput['tids'])) {
            Bingo_Log::warning('param error, input:[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrThreadInfo = Molib_Util_ShareThread::getOriginThreadInfo($arrInput['tids'], $arrInput['client_version'], $arrInput['client_type']);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrThreadInfo);
    }

    /**
     * 获取原贴信息
     * @param $arrInput
     * @return array
     */
    public static function getOriginUgcInfo($arrInput)
    {
        if (empty($arrInput['idInfo']) || !is_array($arrInput['idInfo']) ||
            !isset($arrInput['client_version']) || $arrInput['client_version'] == ''
        ) {
            Bingo_Log::warning('param error, input:[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrThreadInfo = Molib_Util_ShareThread::getOriginUgcInfo($arrInput, $arrInput['client_version'], $arrInput['client_type']);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrThreadInfo);
    }

    /**
     * 处理转发贴子到动态逻辑
     * @param $arrData
     * @return array
     */
    private static function _dealRepost2Dynamic($arrData)
    {
        if ($arrData['ui_trans_params']['is_repost_to_dynamic'] != 1) {
            return false;
        }

        $arrExtAttr = array();
        foreach ($arrData['ext_attr'] as $arrAttr) {
            $arrExtAttr[$arrAttr['key']] = $arrAttr['value'];
        }

        $arrData['forum_name'] = Bingo_Encode::convert($arrData['forum_name'], Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);
        $strForumName = !empty($arrData['forum_name']) ? $arrData['forum_name'] : '贴';
        $strRepostContent = '我在'.$arrData['forum_name'].'吧发布了贴子';
        $strThreadTitle = !empty($arrData['utf8_title']) ? $arrData['utf8_title']: '我发表了贴子，大伙来看看吧~';
        $strThumbpic = '';
        if ($arrData['thread_type'] == 40) {
            // 视频贴取封面
            $strRefType = 'video';
            $strThumbpic = $arrExtAttr['video_info']['thumbnail_url'];
        } else if (!empty($arrData['image_urls'])) {
            // 图文贴取第一张图片
            $strRefType = 'imagetext';
            $strThumbpic = $arrData['image_urls'][0];
        } else {
            $strRefType = 'imagetext';
        }

        // 默认贴吧Logo
        if (empty($strThumbpic)) {
            $strThumbpic = 'https://b.bdstatic.com/searchbox/mappconsole/image/20180614/1528975601-43867.png';
        }

        $ak = '4gpK8OBV9M9vwQokj4TWozh3wXuYTm0h';
        $sk = 'wsysQ7EMTSfkqXT1eTrPp03rSjXTW5ZS';
        $ts = time();
        $arrQuery = array(
            'ak'        =>  $ak,
            'ts'        =>  $ts,
            'tk'        =>  crc32($ak.$ts.$sk),
        );
        $arrParams  = array(
            'ak'        =>  $ak,
            'source'    =>  'tieba',
            'appname'   =>  'tieba',
            'params'    =>  array(
                'content'   =>  $strRepostContent,
                'ext_info'  =>  array(
                    'share_type'    =>  'forward',
                ),
                'forward'   =>  array(
                    'tid'   =>  $arrData['thread_id'],
                    'thumbpic'  =>  $strThumbpic,
                    'title' =>  $strThreadTitle,
                    'channel'   =>  'swan_details_tieba_post_share_collect_comment',
                    'url'   =>  'https://mbd.baidu.com/ma/landingpage?t=smartapp_share&appid=flFqXclepWs7RdugAszy9eERL7G5dS0I&url=/pages/pb/pb?tid='.$arrData['thread_id'],
                    'ref_type'  =>  $strRefType,
                    'account_type'  =>  'swan',
                ),
                'source_from'   =>  'tieba',
                'uid'       =>  $arrData['user_id'],
            ),
        );
        $arrHeader = array(
            'Host' => 'resbox.mbd.baidu.com',
            'pathinfo' => '/resbox/ugc/publish/create',
            'querystring' => http_build_query($arrQuery),
        );
        $ret = ral('resbox_form', 'post', $arrParams, rand(), $arrHeader);
        $arrRet = json_decode($ret, true);
        Bingo_Log::warning('repost thread to dynamic. [header: '.serialize($arrHeader).'] [intput: '.serialize($arrParams).'] [output: '.serialize($arrRet).']');
        if ($ret === false || 0 !== $arrRet['errno']) {
            Bingo_Log::warning('call /resbox/ugc/publish/create failed. [input: '.serialize($arrParams).'] [output: '.serialize($arrRet).']');
            header('HTTP/1.1 500 DIY retry');
            return false;
        }
        return true;
    }

    /**
     * 处理发帖逻辑
     * @param $arrData
     * @return array
     */
    private static function _dealCommitThread($arrData)
    {
        if (!self::$_objMulti) {
            self::$_objMulti = new Tieba_Multi('share_multi');
        }

        self::$_intUserId    = intval($arrData['user_id']);
        self::$_intForumId   = intval($arrData['forum_id']);
        self::$_strForumName = Bingo_Encode::convert($arrData['forum_name'], Bingo_Encode::ENCODE_UTF8, Bingo_Encode::ENCODE_GBK);

        //获取原始帖子id
        foreach ($arrData['ext_attr'] as $ext_attr) {
            if ($ext_attr['key'] == 'original_tid') {
                self::$_intOriginalTid = intval($ext_attr['value']);
            }
        }

        self::_setUserActiveForumAttrPre();
        self::_addThreadSharePre($arrData);

        self::$_objMulti->call();

        self::_setUserActiveForumAttrExec();
        self::_addThreadShareExec($arrData);

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * 处理删帖逻辑
     * @param $arrInput
     * @return array
     */
    private static function _dealDelThread($arrInput)
    {
        self::_setMsg($arrInput);//9.2 ueg需求，我的贴子页增加 贴子回收站红点
        self::_delThreadShare($arrInput);
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }


    /**
     * 设置用户最近活跃吧的扩展属性
     * @return bool
     */
    private static function _setUserActiveForumAttrPre()
    {
        // 吧id<=0 或者 等于me0407吧id 直接返回
        if (self::$_intForumId <= 0 || self::$_intForumId == Molib_Conf_Reversion::$noForumThreadAdd['forum_id']) {
            return true;
        }

        // 获取用户个人信息
        $arrReq   = array(
            'user_id'          => array(self::$_intUserId),
            'need_follow_info' => 0,
            'need_pass_info'   => 1,
            'get_icon'         => 0,
        );
        $arrInput = array(
            'serviceName' => 'user',
            'method'      => 'mgetUserDataEx',
            'input'       => $arrReq,
            'ie'          => 'utf-8',
        );

        self::$_objMulti->register('user:mgetUserDataEx', new Tieba_Service('user'), $arrInput);

        return true;
    }

    /**
     * 插入分享贴记录
     * @param
     * @return bool
     */
    private static function _addThreadSharePre($arrData)
    {
        // 临时使用
        $arrInput = array(
            'serviceName' => 'common',
            'method'      => 'spiderReceiveThreadFromNmq',
            'input'       => $arrData,
            'ie'          => 'gbk',
        );
        self::$_objMulti->register('common::spiderReceiveThreadFromNmq', new Tieba_Service('common'), $arrInput);

        if (self::$_intOriginalTid <= 0) {
            return false;
        }

        $arrInput = array(
            'serviceName' => 'post',
            'method'      => 'mgetThread',
            'input'       => array(
                'thread_ids'      => array(
                    self::$_intOriginalTid,
                ),
                'need_abstract'   => 1,
                'forum_id'        => 0,
                'need_photo_pic'  => 0,
                'need_user_data'  => 0,
                'icon_size'       => 0,
                'need_forum_name' => 0,
                'call_from'       => 'client_frs',
            ),

        );
        self::$_objMulti->register('mgetThread', new Tieba_Service('sharethread'), $arrInput);

        //设置分享数
        $arrReq['input'][] = array(
            'tid'    => self::$_intOriginalTid,
            'fields' => array(
                array(
                    'fkey' => 'share_num',
                    'step' => 1,
                ),
            ),
        );
        $arrInput          = array(
            'serviceName' => 'post',
            'method'      => 'incrByKeyInThreadInfo',
            'input'       => $arrReq,
            'ie'          => 'utf-8',
        );
        self::$_objMulti->register('incrByKeyInThreadInfo_share', new Tieba_Service('post'), $arrInput);
    }

    /**
     * @return bool
     */
    private static function _setUserActiveForumAttrExec()
    {

        // 吧id<=0 或者 等于me0407吧id 直接返回
        if (self::$_intForumId <= 0 || self::$_intForumId == Molib_Conf_Reversion::$noForumThreadAdd['forum_id']) {
            return true;
        }

        $arrRet = self::$_objMulti->getResult('user:mgetUserDataEx');
        if (false === $arrRet || Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning("call user:getUserDataEx failed! input user_id:[ " . self::$_intUserId . "], output:[" . serialize($arrRet) . "].");
            return false;
        }

        // 最新活跃吧
        $arrNewActiveForum = array();
        if (!empty($arrRet['user_info'][self::$_intUserId]['new_active_forum'])) {
            $arrOldActiveForum                     = $arrRet['user_info'][self::$_intUserId]['new_active_forum'];
            $arrNewActiveForum[self::$_intForumId] = array(
                'fid'   => self::$_intForumId,
                'fname' => self::$_strForumName,
            );
            foreach ($arrOldActiveForum as $arrForum) {
                $arrNewActiveForum[$arrForum['fid']] = $arrForum;
                if (count($arrNewActiveForum) == 3) {
                    break;
                }
            }
            $arrNewActiveForum = array_values($arrNewActiveForum);
        } else {
            $arrNewActiveForum[] = array(
                'fid'   => self::$_intForumId,
                'fname' => self::$_strForumName,
            );
        }

        // 设置用户属性
        $arrUserInput = array(
            'user_id'    => self::$_intUserId,
            'attr_name'  => 'new_active_forum',
            'attr_value' => $arrNewActiveForum,
        );
        $arrOut       = Tieba_Service::call('user', 'setUserAttrByArray', $arrUserInput, null, null, 'post', 'php', 'utf-8');

        if (false === $arrOut || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning("call setUserAttrByArray error.inuput=[" . serialize($arrUserInput) . "]output=[" . serialize($arrOut) . "]");
            return false;
        }
        return true;
    }

    /**
     * @param $arrData
     * @return bool
     */
    private static function _addThreadShareExec($arrData)
    {
        if (self::$_intOriginalTid <= 0) {
            return false;
        }

        $arrResult = self::$_objMulti->getResult('incrByKeyInThreadInfo_share');
        if (Tieba_Errcode::ERR_SUCCESS != $arrResult['errno']) {
            Bingo_Log::warning('call incrByKeyInThreadInfo_share failed.');
        }

        //获取原始帖子信息
        $arrResult = self::$_objMulti->getResult('mgetThread');
        if (false === $arrResult || Tieba_Errcode::ERR_SUCCESS !== $arrResult['errno']) {
            Bingo_Log::warning('call post:mgetThread failed. thread_id is ' . self::$_intOriginalTid);
            return false;
        }

        $arrInput = array(
            'share_tid'       => $arrData['thread_id'],
            'share_pid'       => $arrData['post_id'],
            'share_uid'       => $arrData['user_id'],
            'share_fid'       => $arrData['forum_id'],
            'share_time'      => time(),
            'original_tid'    => self::$_intOriginalTid,
            'original_uid'    => $arrResult['output']['thread_list'][self::$_intOriginalTid]['user_id'],
            'original_is_del' => 0,
        );

        //插入db
        $arrRet = self::_insertThreadshareTid($arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning('insert threadshare record failed. input is ' . serialize($arrInput));
            return false;
        }

        return true;
    }

    /**
     * 标记删除分享贴原贴
     * @param $arrInput
     * @return bool
     */
    private static function _delThreadShare($arrInput)
    {
        $intThreadId = intval($arrInput['thread_id']);
        if ($intThreadId <= 0) {
            return false;
        }

        $arrInput = array(
            'original_tid' => $intThreadId,
        );

        //插入db
        $arrRet = self::_updateThreadshareTid($arrInput);

        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning('update threadshare Tid failed. input is ' . serialize($arrInput));
            return false;
        }

        return true;
    }

    /**
     * 插入分享贴记录（tid分表）
     * @param $arrInput
     * @return array
     */
    private static function _insertThreadshareTid($arrInput)
    {
        if (empty($arrInput['original_tid']) || empty($arrInput['share_time']) || empty($arrInput['share_uid'])
            || empty($arrInput['original_tid']) || $arrInput['share_fid'] < 0 || empty($arrInput['original_uid'])
        ) {
            Bingo_Log::warning('param error, input:[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $arrReq = array(
            'function'        => 'insertThreadshareTid',
            'share_tid'       => $arrInput['share_tid'],
            'share_pid'       => $arrInput['share_pid'],
            'share_uid'       => $arrInput['share_uid'],
            'share_fid'       => $arrInput['share_fid'],
            'share_time'      => $arrInput['share_time'],
            'original_tid'    => $arrInput['original_tid'],
            'original_uid'    => $arrInput['original_uid'],
            'original_is_del' => $arrInput['original_is_del'],
        );

        $arrRes = Dl_Db_Threadshare::execSql($arrReq);
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning(__METHOD__ . ' call DB failed! input:[' . serialize($arrReq) . ']' . 'output:[' . serialize($arrRes) . ']');
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);

    }

    /**
     * 插入分享贴记录（uid分表）
     * @param $arrInput
     * @return array
     */
    private static function _insertThreadshareUid($arrInput)
    {
        if (empty($arrInput['original_tid']) || empty($arrInput['share_time']) || empty($arrInput['share_uid'])
            || empty($arrInput['original_tid']) || $arrInput['share_fid'] < 0 || empty($arrInput['original_uid'])
        ) {
            Bingo_Log::warning('param error, input:[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrReq = array(
            'function'     => 'insertThreadshareUid',
            'share_tid'    => $arrInput['share_tid'],
            'share_pid'    => $arrInput['share_pid'],
            'share_uid'    => $arrInput['share_uid'],
            'share_fid'    => $arrInput['share_fid'],
            'share_time'   => $arrInput['share_time'],
            'original_tid' => $arrInput['original_tid'],
            'original_uid' => $arrInput['original_uid'],
        );

        $arrRes = Dl_Db_Threadshare::execSql($arrReq);

        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning(__METHOD__ . ' call DB failed! input:[' . serialize($arrReq) . ']' . 'output:[' . serialize($arrRes) . ']');
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);

    }

    /**
     * 更新分享贴记录（tid分表）
     * @param $arrInput
     * @return array
     */
    private static function _updateThreadshareTid($arrInput)
    {
        if (empty($arrInput['original_tid'])) {
            Bingo_Log::warning('param error, input:[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrReq = array(
            'function'     => 'updateThreadshareTid',
            'original_tid' => $arrInput['original_tid'],
        );
        $arrRes = Dl_Db_Threadshare::execSql($arrReq);

        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning(__METHOD__ . ' call DB failed! input:[' . serialize($arrReq) . ']' . 'output:[' . serialize($arrRes) . ']');
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);

    }

    /**
     * 更新分享贴记录（uid分表）
     * @param $arrInput
     * @return array
     */
    private static function _updateThreadshareUid($arrInput)
    {
        if (empty($arrInput['original_tid'])) {
            Bingo_Log::warning('param error, input:[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }

        $arrReq = array(
            'function'     => 'updateThreadshareUid',
            'original_tid' => $arrInput['original_tid'],
        );

        $arrRes = Dl_Db_Threadshare::execSql($arrReq);

        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning(__METHOD__ . ' call DB failed! input:[' . serialize($arrReq) . ']' . 'output:[' . serialize($arrRes) . ']');
        }

        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);

    }

    /**
     * 获取某个贴子的转发贴回复列表(回复详情)
     * @param $arrInput
     * @return array
     */
    public static function getRepostThreadsByThreadId($arrInput)
    {

        if (empty($arrInput['thread_id'])) {
            Bingo_Log::warning('param error, input:[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intPn     = intval($arrInput['pn']) > 0 ? intval($arrInput['pn']) : 1;
        $intRn     = intval($arrInput['rn']) > 0 ? intval($arrInput['rn']) : 30;
        $intTid    = intval($arrInput['thread_id']);
        $intUserId = intval($arrInput['user_id']);

        // 查询总数
        $arrReq = array(
            'function'     => 'selectCountShareThreadByTid',
            'original_tid' => $intTid,
        );

        $arrRes = Dl_Db_Threadshare::execSql($arrReq);
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning(__METHOD__ . ' call DB failed! input:[' . serialize($arrReq) . ']' . 'output:[' . serialize($arrRes) . ']');
        }
        if (!empty($arrRes['results'][0][0])) {
            $intCount = $arrRes['results'][0][0]['cnt'];
        }

        // 查询分页数据
        $arrReq = array(
            'function'     => 'selectShareThreadByTid',
            'original_tid' => $intTid,
            'offset'       => ($intPn - 1) * $intRn,
            'limit'        => $intRn,
        );
        $arrRes = Dl_Db_Threadshare::execSql($arrReq);

        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning(__METHOD__ . ' call DB failed! input:[' . serialize($arrReq) . ']' . 'output:[' . serialize($arrRes) . ']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = isset($arrRes['results'][0]) ? $arrRes['results'][0] : array();
        if (empty($arrData)) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }

        $arrPostId = array_values(array_column($arrData, 'share_pid'));

        $objLibPost  = new Libs_Post($arrPostId, $intUserId);
        $arrPostData = $objLibPost->getPostData();

        $arrOutData = array_merge($arrPostData,
            array(
                'page' => array(
                    'total_page'   => $intCount > 0 ? ceil($intCount / $intRn) : $intPn,
                    'current_page' => $intPn,
                ),
            )
        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutData);
    }

    /**
     * [_getThreadShareList 获取原帖被删的分享帖]
     * <AUTHOR> <[<email address>]>
     * @param  [type] $arrInput [description]
     * @return [type]           [description]
     */
    public static function getThreadShareList($arrInput)
    {
        if (empty($arrInput['thread_id'])) {
            Bingo_Log::warning('param error, inoput:[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intTid = intval($arrInput['thread_id']);
        //查询分享帖列表
        $arrReq = array(
            'function'     => 'selectAllShareThreadByTid',
            'original_tid' => $intTid,
        );

        $arrRes = Dl_Db_Threadshare::execSql($arrReq);
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning(__METHOD__ . 'call DB failed. input:[' . serialize($arrReq) . ']output:[' . serialize($arrRes) . ']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }
        return $arrRes;
    }

    /**
     * [_addMaskShareThread 单吧删原帖或楼主删原帖，分享贴被屏蔽]
     * <AUTHOR> <[<email address>]>
     * @param [type] $arrInput [description]
     * @return [type] [<description>]
     */
    private static function _addMaskShareTid($arrInput)
    {
        $intThreadId = intval($arrInput['thread_id']);
        if ($intThreadId <= 0) {
            return false;
        }

        self::$_objMulti = new Tieba_Multi('share_multi_mask');
        $arrInput        = array(
            'thread_id' => $intThreadId,
        );

        //查询db
        $arrRet = self::getThreadShareList($arrInput);
        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning('select threadshare tid failed. input:[' . serialize($arrInput));
            return false;
        }

        $arrRetInfo = $arrRet['results'][0];
        if (!empty($arrRetInfo)) {
            foreach ($arrRetInfo as $key => $threadInfo) {
                //设置帖子在frs页隐藏
                $arrInput = array(
                    'serviceName' => 'post',
                    'method'      => 'addMaskUserThread',
                    'input'       => array(
                        'mask_info' => array(
                            array(
                                'forum_id'  => $threadInfo['share_fid'],
                                'thread_id' => $threadInfo['share_tid'],
                            ),
                        ),
                        'call_from' => 'threadshare',
                    ),
                );

                self::$_objMulti->register('addMaskUserThread_' . $key, new Tieba_Service('post'), $arrInput);
            }
            self::$_objMulti->call();
            foreach ($arrRetInfo as $key => $threadInfo) {
                $arrOut = self::$_objMulti->getResult('addMaskUserThread_' . $key);
                if (false === $arrOut || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                    Bingo_Log::warning('call addMaskUserThread failed. output:[' . serialize($arrOut) . ']');
                    return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                }
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * [_addMaskShareThread 多吧吧务删帖，分享贴被屏蔽]
     * <AUTHOR> <[<email address>]>
     * @param [type] $arrInput [description]
     * @return [type] [<description>]
     */
    private static function _addMaskShareTidMulti($arrInput)
    {
        $intThreadId = intval($arrInput['thread_id']);
        $intForumId  = intval($arrInput['forum_id']);
        if ($intThreadId <= 0) {
            return false;
        }

        self::$_objMulti = new Tieba_Multi('share_multi_mask_tid');
        $arrInput        = array(
            'thread_id' => $intThreadId,
        );
        //查询db
        $arrRet = self::getThreadShareList($arrInput);

        if (Tieba_Errcode::ERR_SUCCESS !== $arrRet['errno']) {
            Bingo_Log::warning('select threadshare tid failed. input:[' . serialize($arrInput));
            return false;
        }

        $arrRetInfo = $arrRet['results'][0];
        if (!empty($arrRetInfo)) {
            foreach ($arrRetInfo as $key => $threadInfo) {
                // if ($intForumId > 0 && $intForumId == $threadInfo['share_fid'] ) {
                //设置帖子在frs页隐藏
                $arrInput = array(
                    'serviceName' => 'post',
                    'method'      => 'addMaskUserThread',
                    'input'       => array(
                        'mask_info' => array(
                            array(
                                'forum_id'  => $threadInfo['share_fid'],
                                'thread_id' => $threadInfo['share_tid'],
                            ),
                        ),
                        'call_from' => 'threadshare',
                    ),
                );

                self::$_objMulti->register('addMaskUserThread_' . $key, new Tieba_Service('post'), $arrInput);
                // } 
            }
            self::$_objMulti->call();
            foreach ($arrRetInfo as $key => $threadInfo) {
                if ($intForumId > 0 && $intForumId == $threadInfo['share_fid']) {
                    $arrOut = self::$_objMulti->getResult('addMaskUserThread_' . $key);
                    if (false === $arrOut || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                        Bingo_Log::warning('call addMaskUserThread failed. output:[' . serialize($arrOut) . ']');
                        return self::_errRet(Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
                    }
                }
            }
        }
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
    }

    /**
     * @param $errno
     * @param null $data
     * @return array
     */
    private static function _errRet($errno, $data = null)
    {
        $arrRet = array(
            'errno'  => $errno,
            'errmsg' => Tieba_Error::getErrmsg($errno),
            'data'   => $data,
        );
        return $arrRet;
    }

    /**
     * @param $arrInput
     * @return array
     */
    private static function _setMsg($arrInput)
    {
        $arrParams = array(
            'user_id' => intval($arrInput['user_id']),
            'type'    => Molib_Client_Define::DELETE_THREAD_MESSAGE_TYPE,

        );
        $arrRet    = Tieba_Service::call('messagepool', 'updateMsgByType', $arrParams);
        if (false == $arrRet || $arrRet['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
            Bingo_Log::warning('Call messagepool:updateMsgByType failed. input=]' . serialize($arrParams) . '[Output=]' . serialize($arrRet));
            return false;
        }
        return true;
    }

    /**
     * 获取某个贴子的转发贴
     * @param $arrInput
     * @return array
     */
    public static function getRepostTidsByOriginalTid($arrInput)
    {

        if (empty($arrInput['thread_id'])) {
            Bingo_Log::warning('param error, input:[' . serialize($arrInput) . ']');
            return self::_errRet(Tieba_Errcode::ERR_PARAM_ERROR);
        }
        $intPn     = intval($arrInput['pn']) > 0 ? intval($arrInput['pn']) : 1;
        $intRn     = intval($arrInput['rn']) > 0 ? intval($arrInput['rn']) : 30;
        $intTid    = intval($arrInput['thread_id']);
        $intUserId = intval($arrInput['user_id']);

        // 查询总数
        $arrReq = array(
            'function'     => 'selectCountShareThreadByTid',
            'original_tid' => $intTid,
        );

        $arrRes = Dl_Db_Threadshare::execSql($arrReq);
        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning(__METHOD__ . ' call DB failed! input:[' . serialize($arrReq) . ']' . 'output:[' . serialize($arrRes) . ']');
        }
        if (!empty($arrRes['results'][0][0])) {
            $intCount = $arrRes['results'][0][0]['cnt'];
        }

        // 查询分页数据
        $arrReq = array(
            'function'     => 'selectShareThreadByTid',
            'original_tid' => $intTid,
            'offset'       => ($intPn - 1) * $intRn,
            'limit'        => $intRn,
        );
        $arrRes = Dl_Db_Threadshare::execSql($arrReq);

        if (false === $arrRes || Tieba_Errcode::ERR_SUCCESS !== $arrRes['errno']) {
            Bingo_Log::warning(__METHOD__ . ' call DB failed! input:[' . serialize($arrReq) . ']' . 'output:[' . serialize($arrRes) . ']');
            return self::_errRet(Tieba_Errcode::ERR_DL_CALL_FAIL);
        }

        $arrData = isset($arrRes['results'][0]) ? $arrRes['results'][0] : array();
        if (empty($arrData)) {
            return self::_errRet(Tieba_Errcode::ERR_SUCCESS);
        }
        Bingo_Log::warning('getRepostTidsByOriginalTid' . serialize($arrData));
        foreach ($arrData as $key => $item) {
            if (intval($item['share_tid']) > 0) {
                $arrRetTids[] = intval($item['share_tid']);
            }
        }
        $arrOutData = array(
            'page' => array(
                'total_page'   => $intCount > 0 ? ceil($intCount / $intRn) : $intPn,
                'current_page' => $intPn,
            ),
            'data' => $arrRetTids,

        );
        return self::_errRet(Tieba_Errcode::ERR_SUCCESS, $arrOutData);
    }

}
