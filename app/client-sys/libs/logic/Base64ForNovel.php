<?php

/**
 * 专门用于小说阿拉丁提交的cuid解密功能，这个类对于别的就没有什么用了
 */
class Libs_Logic_Base64ForNovel {
	private static $_encode_str = "qogjOuCRNkfil5p4SQ3LAmxGKZTdesvB6z_YPahMI9t80rJyHW1DEwFbc7nUVX2-";
	
	/**
	 * bd_base64_buffer_need
	 * @param unknown_type $input_size
	 * @return number
	 */
	public static function bd_base64_buffer_need($input_size) 
	{
		return ((floor ( ($input_size + 2) / 3 ) * 4) + 2);
	}
	
	/**
	 * bd_base64_general_xor_seeds
	 * @param unknown_type $base64_seeds
	 * @return string
	 */
	public static function bd_base64_general_xor_seeds($base64_seeds) 
	{
		$base64_key_Rotl = (($base64_seeds << 27) | ($base64_seeds >> 5));
		$xor_seeds_Rotl_lv = ($base64_key_Rotl & 0xF);
		$xor_seeds_Rotl = ((0x2D382324 << $xor_seeds_Rotl_lv) | (0x2D382324 << $xor_seeds_Rotl_lv));
		$xor_seeds = ($base64_key_Rotl ^ $xor_seeds_Rotl);
		
		// 处理xor_seeds为4个char的数组
		$t_xor_seeds = decbin ( $xor_seeds );
		$t_xor_seeds = str_pad ( $t_xor_seeds, 32, "0", STR_PAD_LEFT );
		
		return $t_xor_seeds;
	}
	
	/**
	 * bd_base64_compute_encode_table
	 * @param unknown_type $encode_str
	 * @param unknown_type $base64_seeds
	 * @return multitype:
	 */
	public static function bd_base64_compute_encode_table($encode_str, $base64_seeds) 
	{
		$offset_size = $base64_seeds & 0x3F;
		$t_encode_str = substr ( $encode_str, $offset_size );
		$t_encode_str .= substr ( $encode_str, 0, $offset_size );
		
		$encode_table = str_split ( $t_encode_str );
		return $encode_table;
	}
	
	/**
	 * bd_base64_compute_decode_table
	 * @param unknown_type $encode_str
	 * @param unknown_type $base64_seeds
	 * @return multitype:number
	 */
	public static function bd_base64_compute_decode_table($encode_str, $base64_seeds) 
	{
		$offset_size = $base64_seeds & 0x3F;
		$t_encode_str = substr ( $encode_str, $offset_size );
		$t_encode_str .= substr ( $encode_str, 0, $offset_size );
		
		$encode_table = str_split ( $t_encode_str );
		$i = 0;
		$decode_table = array ();
		while ( $i < 64 ) {
			$index = ord ( $encode_table [$i] );
			$decode_table [$index] = $i;
			$i ++;
		}
		return $decode_table;
	}
	
	/**
	 * XOR_64
	 * @param unknown_type $str1
	 * @param unknown_type $str2
	 * @param unknown_type $len
	 * @return string
	 */
	public static function XOR_64($str1, $str2, $len = 32) 
	{
		$str1 = str_pad ( $str1, 32, "0", STR_PAD_LEFT );
		$str2 = str_pad ( $str2, 32, "0", STR_PAD_LEFT );
		
		$str1_left = substr ( $str1, 0, 16 );
		$str1_right = substr ( $str1, 16, 16 );
		
		$str2_left = substr ( $str2, 0, 16 );
		$str2_right = substr ( $str2, 16, 16 );
		
		$result_left = decbin ( bindec ( $str1_left ) ^ bindec ( $str2_left ) );
		$result_left = str_pad ( $result_left, 16, "0", STR_PAD_LEFT );
		$result_right = decbin ( bindec ( $str1_right ) ^ bindec ( $str2_right ) );
		$result_right = str_pad ( $result_right, 16, "0", STR_PAD_LEFT );
		
		return $result_left . $result_right;
	}
	
	/**
	 * bd_base64_xor_enc
	 * @param unknown_type $xor_seeds_bin
	 * @param unknown_type $input_array
	 * @return string
	 */
	public static function bd_base64_xor_enc($xor_seeds_bin, $input_array) 
	{
		$input_size = count ( $input_array );
		$block_cnt = (($input_size) >> 2);
		$block_idx = 0;
		$out_bin = "";
		while ( $block_idx < $block_cnt ) {
			// 处理循环位移
			$A = decbin ( ord ( $input_array [$block_idx * 4] ) );
			$B = decbin ( ord ( $input_array [$block_idx * 4 + 1] ) );
			$C = decbin ( ord ( $input_array [$block_idx * 4 + 2] ) );
			$D = decbin ( ord ( $input_array [$block_idx * 4 + 3] ) );
			
			$A = str_pad ( $A, 8, "0", STR_PAD_LEFT );
			$B = str_pad ( $B, 8, "0", STR_PAD_LEFT );
			$C = str_pad ( $C, 8, "0", STR_PAD_LEFT );
			$D = str_pad ( $D, 8, "0", STR_PAD_LEFT );
			
			$S = substr ( $A, 5, 3 ) . $D . $C . $B . substr ( $A, 0, 5 ); // 小端机
			
			$S = self::XOR_64 ( $S, $xor_seeds_bin );
			$A = chr ( bindec ( substr ( $S, 24, 8 ) ) );
			$B = chr ( bindec ( substr ( $S, 16, 8 ) ) );
			$C = chr ( bindec ( substr ( $S, 8, 8 ) ) );
			$D = chr ( bindec ( substr ( $S, 0, 8 ) ) );
			$S = $A . $B . $C . $D;
			
			$out_bin .= $S;
			
			$block_idx ++;
		}
		
		$block_cnt = (($input_size + 3) >> 2);
		$last_block = "";
		if ($block_idx < $block_cnt) { // /最后一块数据,不做循环位移
			$t_idx = $block_idx * 4;
			
			if ($t_idx < $input_size) {
				$t_block = decbin ( ord ( $input_array [$t_idx] ) );
				$t_block = str_pad ( $t_block, 8, "0", STR_PAD_LEFT );
				$last_block = $t_block . $last_block;
				$t_idx ++;
				if ($t_idx < $input_size) {
					$t_block = decbin ( ord ( $input_array [$t_idx] ) );
					$t_block = str_pad ( $t_block, 8, "0", STR_PAD_LEFT );
					$last_block = $t_block . $last_block;
					$t_idx ++;
					if ($t_idx < $input_size) {
						$t_block = decbin ( ord ( $input_array [$t_idx] ) );
						$t_block = str_pad ( $t_block, 8, "0", STR_PAD_LEFT );
						$last_block = $t_block . $last_block;
						$t_idx ++;
						if ($t_idx < $input_size) {
							$t_block = decbin ( ord ( $input_array [$t_idx] ) );
							$t_block = str_pad ( $t_block, 8, "0", STR_PAD_LEFT );
							$last_block = $t_block . $last_block;
							$t_idx ++;
						}
					}
				}
				$last_block_size = strlen ( $last_block );
				$t_last_block = str_pad ( $last_block, 32, "0", STR_PAD_LEFT );
				$t_last_block = self::XOR_64 ( $t_last_block, $xor_seeds_bin );
				$A = chr ( bindec ( substr ( $t_last_block, 24, 8 ) ) );
				$B = chr ( bindec ( substr ( $t_last_block, 16, 8 ) ) );
				$C = chr ( bindec ( substr ( $t_last_block, 8, 8 ) ) );
				$D = chr ( bindec ( substr ( $t_last_block, 0, 8 ) ) );
				
				$out_bin .= $A . $B . $C . $D;
			}
		}
		$out_str = $out_bin;
		return $out_str;
	}
	
	/**
	 * bd_base64_block_enc
	 * @param unknown_type $encode_table
	 * @param unknown_type $input_str
	 * @return string
	 */
	public static function bd_base64_block_enc($encode_table, $input_str) 
	{
		$out = array ();
		$input_array = str_split ( $input_str );
		
		$bin_input0 = decbin ( ord ( $input_array [0] ) );
		$bin_input0 = str_pad ( $bin_input0, 8, "0", STR_PAD_LEFT );
		if (count ( $input_array ) >= 2) {
			$bin_input1 = decbin ( ord ( $input_array [1] ) );
			$bin_input1 = str_pad ( $bin_input1, 8, "0", STR_PAD_LEFT );
		} else {
			$bin_input1 = "00000000";
		}
		if (count ( $input_array ) >= 3) {
			$bin_input2 = decbin ( ord ( $input_array [2] ) );
			$bin_input2 = str_pad ( $bin_input2, 8, "0", STR_PAD_LEFT );
		} else {
			$bin_input2 = "00000000";
		}
		
		$out [0] = $encode_table [bindec ( substr ( $bin_input0, 2, 6 ) )];
		$out [1] = $encode_table [bindec ( substr ( $bin_input1, 2, 6 ) )];
		$out [2] = $encode_table [bindec ( substr ( $bin_input2, 2, 6 ) )];
		$out [3] = $encode_table [bindec ( substr ( $bin_input0, 0, 2 ) . substr ( $bin_input1, 0, 2 ) . substr ( $bin_input2, 0, 2 ) )];
		
		$result = $out [0] . $out [1] . $out [2] . $out [3];
		return $result;
	}
	
	/**
	 * bd_base64_encode
	 * @param unknown_type $encode_str
	 * @param unknown_type $input
	 * @param unknown_type $base64_seeds
	 * @return string
	 */
	public static function bd_base64_encode($encode_str, $input, $base64_seeds) {
		if (strlen ( $input ) == 0)
		{
                    return "";
		}

		$encode_table = self::bd_base64_compute_encode_table ( $encode_str, $base64_seeds );
		$xor_seeds_bin = self::bd_base64_general_xor_seeds ( $base64_seeds );
		$input_array = str_split ( $input );
		$input_size = count ( $input_array );
		$out_array = array ();
		$out_str = "";
		
		$tail_len = ($input_size % 3);
		$align_cnt = floor ( $input_size / 3 );
		$len_base = $align_cnt * 4;
		
		$idx = $align_cnt;
		$xor_str = self::bd_base64_xor_enc ( $xor_seeds_bin, $input_array );
		
		if ($tail_len > 0) { // //尾部数据块
			$tmp_str = substr ( $xor_str, $idx * 3, $tail_len );
			$t_out = self::bd_base64_block_enc ( $encode_table, $tmp_str );
			$out_str = $t_out;
			$len_base += 4;
		}
		while ( $idx > 0 ) {
			$idx --;
			$tmp_str = substr ( $xor_str, $idx * 3, 3 );
			$t_out = self::bd_base64_block_enc ( $encode_table, $tmp_str );
			$out_str = $t_out . $out_str;
			$len_base += 4;
		}
		
		$char_length = strlen ( $out_str );
		$out_str .= chr ( ord ( 'A' ) + $tail_len );
		return $out_str;
	}
	
	/**
	 * bd_base64_block_dec
	 * @param unknown_type $decode_table
	 * @param unknown_type $input_str
	 * @return string
	 */
	public static function bd_base64_block_dec($decode_table, $input_str) 
	{
		$input_array = str_split ( $input_str );
		$buff = array ();
		
		$buff [0] = $decode_table [ord ( $input_array [0] )];
		$buff [1] = $decode_table [ord ( $input_array [1] )];
		$buff [2] = $decode_table [ord ( $input_array [2] )];
		$buff [3] = $decode_table [ord ( $input_array [3] )];
		
		$out_array = array ();
		$out_array [0] = chr ( $buff [0] | ((($buff [3] >> 4) << 6) & 0xC0) );
		$out_array [1] = chr ( $buff [1] | ((($buff [3] >> 2) << 6) & 0xC0) );
		$out_array [2] = chr ( $buff [2] | ((($buff [3]) << 6) & 0xC0) );
		
		$result = $out_array [0] . $out_array [1] . $out_array [2];
		return $result;
	}
	
	/**
	 * bd_base64_xor_dec
	 * @param unknown_type $xor_seeds_bin
	 * @param unknown_type $input_array
	 * @return string
	 */
	public static function bd_base64_xor_dec($xor_seeds_bin, $input_array) 
	{
		$input_size = count ( $input_array );
		$block_cnt = (($input_size) >> 2);
		$block_idx = 0;
		$out_bin = "";
		while ( $block_idx < $block_cnt ) {
			// 处理循环位移
			$A = decbin ( ord ( $input_array [$block_idx * 4] ) );
			$B = decbin ( ord ( $input_array [$block_idx * 4 + 1] ) );
			$C = decbin ( ord ( $input_array [$block_idx * 4 + 2] ) );
			$D = decbin ( ord ( $input_array [$block_idx * 4 + 3] ) );
			
			$A = str_pad ( $A, 8, "0", STR_PAD_LEFT );
			$B = str_pad ( $B, 8, "0", STR_PAD_LEFT );
			$C = str_pad ( $C, 8, "0", STR_PAD_LEFT );
			$D = str_pad ( $D, 8, "0", STR_PAD_LEFT );
			
			$S = $D . $C . $B . $A; // 小端机
			$S = self::XOR_64 ( $S, $xor_seeds_bin );
			$t_S = substr ( $S, 3, 29 ) . substr ( $S, 0, 3 );
			$S = $t_S;
			
			$A = chr ( bindec ( substr ( $S, 24, 8 ) ) );
			$B = chr ( bindec ( substr ( $S, 16, 8 ) ) );
			$C = chr ( bindec ( substr ( $S, 8, 8 ) ) );
			$D = chr ( bindec ( substr ( $S, 0, 8 ) ) );
			$S = $A . $B . $C . $D;
			
			$out_bin .= $S;
			$block_idx ++;
		}
		
		$block_cnt = (($input_size + 3) >> 2);
		$last_block = "";
		if ($block_idx < $block_cnt) { // /最后一块数据,不做循环位移
			$t_idx = $block_idx * 4;
			
			if ($t_idx < $input_size) {
				$t_block = decbin ( ord ( $input_array [$t_idx] ) );
				$t_block = str_pad ( $t_block, 8, "0", STR_PAD_LEFT );
				$last_block = $t_block . $last_block;
				$t_idx ++;
				if ($t_idx < $input_size) {
					$t_block = decbin ( ord ( $input_array [$t_idx] ) );
					$t_block = str_pad ( $t_block, 8, "0", STR_PAD_LEFT );
					$last_block = $t_block . $last_block;
					$t_idx ++;
					if ($t_idx < $input_size) {
						$t_block = decbin ( ord ( $input_array [$t_idx] ) );
						$t_block = str_pad ( $t_block, 8, "0", STR_PAD_LEFT );
						$last_block = $t_block . $last_block;
						$t_idx ++;
						if ($t_idx < $input_size) {
							$t_block = decbin ( ord ( $input_array [$t_idx] ) );
							$t_block = str_pad ( $t_block, 8, "0", STR_PAD_LEFT );
							$last_block = $t_block . $last_block;
							$t_idx ++;
						}
					}
				}
				$last_block_size = strlen ( $last_block );
				$t_last_block = str_pad ( $last_block, 32, "0", STR_PAD_LEFT );
				$t_last_block = self::XOR_64 ( $t_last_block, $xor_seeds_bin );
				$A = chr ( bindec ( substr ( $t_last_block, 24, 8 ) ) );
				$B = chr ( bindec ( substr ( $t_last_block, 16, 8 ) ) );
				$C = chr ( bindec ( substr ( $t_last_block, 8, 8 ) ) );
				$D = chr ( bindec ( substr ( $t_last_block, 0, 8 ) ) );
				
				$out_bin .= $A . $B . $C . $D;
			}
		}
		$out_str = substr ( $out_bin, 0, $input_size );
		return $out_str;
	}
	
	/**
	 * bd_base64_decode
	 * @param unknown_type $encode_str
	 * @param unknown_type $input
	 * @param unknown_type $base64_seeds
	 * @return string|number
	 */
	public static function bd_base64_decode($encode_str, $input, $base64_seeds) {
		if (strlen ( $input ) == 0)
		{
                    return "";
		}
		$decode_table = self::bd_base64_compute_decode_table ( $encode_str, $base64_seeds );
		$xor_seeds_bin = self::bd_base64_general_xor_seeds ( $base64_seeds );
		$input_array = str_split ( $input );
		$input_size = count ( $input_array );
		$grout_cnt = 0;
		$out_str = "";
		$tail_len = 0;
		
		while ( 1 ) {
			if (isset ( $input_array [$grout_cnt * 4] ) && isset ( $input_array [$grout_cnt * 4 + 1] ) && isset ( $input_array [$grout_cnt * 4 + 2] ) && isset ( $input_array [$grout_cnt * 4 + 3] )) {
				$input_str = $input_array [$grout_cnt * 4] . $input_array [$grout_cnt * 4 + 1] . $input_array [$grout_cnt * 4 + 2] . $input_array [$grout_cnt * 4 + 3];
				$t_out_str = self::bd_base64_block_dec ( $decode_table, $input_str );
				$out_str .= $t_out_str;
				$grout_cnt ++;
			} else {
				if ($input_array [$grout_cnt * 4] >= 'A' && $input_array [$grout_cnt * 4] <= 'C' && ! isset ( $input_array [$grout_cnt * 4 + 1] )) {
					$tail_len = ord ( $input_array [$grout_cnt * 4] ) - ord ( "A" );
					if ($tail_len > 0 && $tail_len < 3) {
						$tail_len = 3 - $tail_len;
					}
				} else {
					return - 1;
				}
				break;
			}
		}
		$out_str = substr ( $out_str, 0, strlen ( $out_str ) - $tail_len );
		$out_array = str_split ( $out_str );
		$out_str = self::bd_base64_xor_dec ( $xor_seeds_bin, $out_array );
		return $out_str;
	}
	
	/**
	 * ff_base64_decode
	 * @param unknown_type $input
	 * @return Ambigous <string, number, number>
	 */
	public static function ff_base64_decode($input) 
	{
		return self::bd_base64_decode ( self::$_encode_str, $input, 0 );
	}
	
	/**
	 * ff_base64_encode
	 * @param unknown_type $input
	 * @return string
	 */
	public static function ff_base64_encode($input) 
	{
		return self::bd_base64_encode ( self::$_encode_str, $input, 0 );
	}
}

