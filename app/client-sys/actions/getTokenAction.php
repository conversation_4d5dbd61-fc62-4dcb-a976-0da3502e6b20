<?php
/*==================================
*   Copyright (C) 2014 Baidu.com, Inc. All rights reserved.
*   
*   filename	:	getTokenAction.php
*   author		:	he<PERSON><PERSON><PERSON>
*   create_time	:	2018-5-8 11:51:00
*   desc		:
*   http://mtxprod.tieba.otp.baidu.com/c/s/getToken?sign=123456tbclient654321&token=3333
===================================*/

class getTokenAction extends Molib_Client_BaseAction {
    private static $_clientType;
    private static $_clientVersion;
    private static $_token;
    private static $_isfp;
    const OPEN_TIEBA = 1;
    const DU_TOKEN   = 2;
    /**
     * @return 
     */
    public function _getPrivateInfo(){
        $arrPrivateInfo = array();
        $arrPrivateInfo['check_login'] = true; 
        $arrPrivateInfo['need_login']  = false; 
        $arrPrivateInfo['ispv']  = 0; 
        $arrPrivateInfo['token'] = strval($this->_getInput('token', ""));
        $arrPrivateInfo['shoubaicuid'] = strval($this->_getInput('shoubaicuid', ""));
        return $arrPrivateInfo;
    }

    /**
     * @return 
     */
    protected function _checkPrivate() {
        $token = $this->_objRequest->getPrivateAttr("token");
        if(strlen($token) <= 0){
            $this->_error(Tieba_Errcode::ERR_PARAM_ERROR, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_PARAM_ERROR));
            Bingo_Log::warning("err token. [token:".serialize($token)."]");
            return false;
        }
        return true;
    }

    /**
     * @return
     */
    protected function _execute() {
        self::$_clientType = intval($this->_objRequest->getCommonAttr('client_type'));
        self::$_clientVersion = strval($this->_objRequest->getCommonAttr('client_version'));
        $uid = intval(Tieba_Session_Socket::getLoginUid());

        $token = $this->_objRequest->getPrivateAttr("token");
        if((Molib_Client_Define::CLIENT_TYPE_IPHONE == self::$_clientType && Molib_Util_Version::compare('9.7.7', self::$_clientVersion) >= 0)
            || (Molib_Client_Define::CLIENT_TYPE_ANDROID == self::$_clientType && Molib_Util_Version::compare('*******', self::$_clientVersion) >= 0)){
            $token = base64_decode($token);
        }
        self::$_token = $token;
        self::$_isfp = 0;
        preg_match_all('/.*\^[#$a-zA-Z0-9]{10}\^.*/', self::$_token, $matches);

        if (count($matches[0]) <= 0) {
        	//格式兼容_混合口令
            preg_match_all('/\^[a-zA-Z0-9_]{5,}\$/', self::$_token, $matches);
            if (count($matches[0]) <= 0) {
                self::$_isfp = 0;
            } else {
                self::$_isfp = 1;

                $handleWordServer = Wordserver_Wordlist::factory();
                $arrKeys          = array($token);
                //混合类型token处理处理 token中含字符_
                if(strpos($token,'_') !== false){ 
                	Bingo_Log::warning("is mix token type:".$token);
                	$arrSplits = explode('^', $token);
                	$arrSplits = explode('$', $arrSplits[1]);
                	$arrInput = array(
                			'code' => $arrSplits[0],
                			'code_style' => 3,
                	);
                	$arrOut = Molib_Tieba_Service::call('smartapp', 'decryptCode', $arrInput);
                	if ($arrOut==false || $arrOut['errno'] !== Tieba_Errcode::ERR_SUCCESS) {
                		Bingo_Log::warning('call smartapp:decryptCode failed. input: ['. serialize($arrInput) . '], output: ['.serialize($arrOut) .']');
                		$this->_error(Tieba_Errcode::ERR_MO_INTERNAL_ERROR, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_MO_INTERNAL_ERROR));
                		return false;
                	}
                	$_scheme = $arrOut['data']['url'];
                	$parseData = parse_url($_scheme);
                	if(!empty($parseData['query'])){
                		$queryParts = explode('&', $parseData['query']);
                		$params = array();
                		foreach ($queryParts as $param) {
                			$item = explode('=', $param);
                			$params[$item[0]] = urldecode($item[1]);
                		}
                		if(isset($params['extdata'])){
                			$params['extdata'] = Bingo_String::json2array($params['extdata']);
                			if(isset($params['extdata']['activityid'])){
                				$active_id = $params['extdata']['activityid'];
                				Bingo_Log::warning("token active_id:".$active_id);
                				$active_token = 'activity-'.$active_id;
                				$arrKeys          = array($active_token);
                				$wordResult       = $handleWordServer->getValueByKeys($arrKeys, 'tb_wordlist_redis_dutoken_id');
                				if (isset($wordResult[$active_token])) {
                					$arrConfig = unserialize($wordResult[$active_token]);
                					if (((Molib_Client_Define::CLIENT_TYPE_ANDROID == self::$_clientType && Molib_Util_Version::compare($arrConfig[0], self::$_clientVersion) < 0)
                							|| (Molib_Client_Define::CLIENT_TYPE_IPHONE == self::$_clientType && Molib_Util_Version::compare($arrConfig[1], self::$_clientVersion) < 0))) {
                								$data = array(
                										'url' => $arrConfig[2] .'&token='. urlencode(str_replace('^', '$', $matches[0][0])),
                								);
                								$this->_objResponse->setOutData($data);
                								return true;
                							}
                				} else if (((Molib_Client_Define::CLIENT_TYPE_ANDROID == self::$_clientType && Molib_Util_Version::compare('********', self::$_clientVersion) < 0)
                						|| (Molib_Client_Define::CLIENT_TYPE_IPHONE == self::$_clientType && Molib_Util_Version::compare('10.2.4', self::$_clientVersion) < 0))
                						&& $this->_objRequest->getCommonAttr('subapp_type') != 'mini') {
                							 
                							$data = array(
                									'url' => 'https://tieba.baidu.com/mo/q/worldcupredpacket?noshare=1&token=' . base64_encode(str_replace('^', '$', $matches[0][0])),
                							);
                							 
                							$this->_objResponse->setOutData($data);
                							return true;
                						} else {
                							return true;
                						}
                			}
                		}
                	}
                	
                }
                $wordResult       = $handleWordServer->getValueByKeys($arrKeys, 'tb_wordlist_redis_dutoken_id');
                if (isset($wordResult[$token])) {
                    $arrConfig = unserialize($wordResult[$token]);
                    if (((Molib_Client_Define::CLIENT_TYPE_ANDROID == self::$_clientType && Molib_Util_Version::compare($arrConfig[0], self::$_clientVersion) < 0)
                        || (Molib_Client_Define::CLIENT_TYPE_IPHONE == self::$_clientType && Molib_Util_Version::compare($arrConfig[1], self::$_clientVersion) < 0))) {
                        $data = array(
                            'url' => $arrConfig[2] .'&token='. urlencode(str_replace('^', '$', $matches[0][0])),
                        );
                        $this->_objResponse->setOutData($data);
                        return true;
                    }
                } else if (((Molib_Client_Define::CLIENT_TYPE_ANDROID == self::$_clientType && Molib_Util_Version::compare('********', self::$_clientVersion) < 0)
                    || (Molib_Client_Define::CLIENT_TYPE_IPHONE == self::$_clientType && Molib_Util_Version::compare('10.2.4', self::$_clientVersion) < 0))
                    && $this->_objRequest->getCommonAttr('subapp_type') != 'mini') {

                    $data = array(
                        'url' => 'https://tieba.baidu.com/mo/q/worldcupredpacket?noshare=1&token=' . base64_encode(str_replace('^', '$', $matches[0][0])),
                    );

                    $this->_objResponse->setOutData($data);
                    return true;
                } else {
                    return true;
                }
            }
        } else {
            self::$_isfp = 1;
        }
        
        $shoubaicuid = $this->_objRequest->getPrivateAttr("shoubaicuid");
        Bingo_Log::warning('lingbaiwan: uid'.$uid.' cuid:'.$shoubaicuid. ' token:'.$token);
        if ($token == '^sZqulxTVsT$' || $token == '^TBJ1IE#PKL^') {
            $this->_stlog(self::OPEN_TIEBA, $uid, 0, $shoubaicuid);
            if((Molib_Client_Define::CLIENT_TYPE_ANDROID == self::$_clientType && Molib_Util_Version::compare('*******', self::$_clientVersion) < 0)
            || (Molib_Client_Define::CLIENT_TYPE_IPHONE == self::$_clientType && Molib_Util_Version::compare('9.7.7', self::$_clientVersion) < 0)){
                $data = array(
                    'url' => 'https://tieba.baidu.com/mo/q/worldcupredpacket',
                );
                $this->_objResponse->setOutData($data);
                return true;
            }
            if (Molib_Client_Define::CLIENT_TYPE_IPHONE == self::$_clientType && Molib_Util_Version::compare('9.7.7', self::$_clientVersion) < 0) {
                return true;
            }
            if(strlen($shoubaicuid) <= 0){
                $this->_error(Tieba_Errcode::ERR_PARAM_ERROR, Molib_Client_Error::getErrMsg(Tieba_Errcode::ERR_PARAM_ERROR));
                Bingo_Log::warning("err shoubaicuid. [shoubaicuid:".serialize($shoubaicuid)."]");
                return false;
            }
            // 通知手百完成打开贴吧的任务
            ral_set_pathinfo("/act/api/taskservice/internalsignin");
            //向后端发送的数据
            $input = array(
                'id'   => "fucaiTieba",
                'cuid' => $shoubaicuid,
                'token'=> md5('fucaiTieba'.$shoubaicuid.'256'),
            );
            $header = array(
                'Host' => 'eopa.baidu.com',
            );

            //调用ral访问demo服务，获取返回结果
            $ret = ral("fucai", 'post', $input, rand(), $header);
            Bingo_Log::warning('lingbaiwan: uid'.$uid.' cuid:'.$shoubaicuid. ' token:'.$token.' fucairet:'.$ret);
            $ret = json_decode($ret, true);
            if ($ret['errno'] !== 0 || $ret === false) {
                Bingo_Log::warning('call shoubai fucai fail. input:['. serialize($input) .'].output['.serialize($ret).']');
                $this->_error($ret['errno'], $ret['errmsg']);
                return true;
            }

            $this->_stlog(self::OPEN_TIEBA, $uid, 1, $shoubaicuid);
            $retData = array(
                'activity_done' => array(
                    'url'     => $ret['data']['url'],
                    'message' => $ret['data']['message'],
                    'btntext' => '回去领码', //$ret['data']['btnText'], 端上只能显示四个字符，所以pm要求给定文案。。
                    'sharetoken' => $ret['data']['shareToken'],
                ),
            );
            $this->_error(0, Molib_Client_Error::getErrMsg(0));
            $this->_objResponse->setOutData($retData);

            return true;
        }

        $this->_stlog(self::DU_TOKEN, $uid, 0, $shoubaicuid);

        if (self::$_isfp !== 1) {
            $this->_error(0, Molib_Client_Error::getErrMsg(0));
            $this->_objResponse->setOutData(null);
            return true;
        }
        $service = 'resbox_token';

        $pathinfo = "/resbox/token/token/getmeta";
        $params = array(
            'token'  => $token,
            'source' => 'tieba',
        );

        $input = array(
            "params" => json_encode($params),
        );

        $header = array(
            "pathinfo" => $pathinfo,
            "host" => "resbox.mbd.baidu.com",
            "querystring" => http_build_query($input),
        );
        $ret = ral($service,"post",$input,rand(),$header);
        $ret = json_decode($ret, true);
        if ($ret['errno'] !== 0) {
            Bingo_Log::warning('call shoubai resbox fail. input:['. serialize($input) .'].output['.serialize($ret).']');
            $this->_error($ret['errno'], Molib_Client_Error::getErrMsg($ret['errno']));
            return true;
        }

        $this->_stlog(self::DU_TOKEN, $uid, 1, $shoubaicuid, $ret['data']['activityId']);
        $handleWordServer = Wordserver_Wordlist::factory();
        $arrKeys          = array('activityId', 'versionLimit_'.$ret['data']['activityId']);
        $wordResult       = $handleWordServer->getValueByKeys($arrKeys, 'tb_wordlist_redis_dutoken_id');
        $allowId          = unserialize($wordResult['activityId']);

        if (strlen($wordResult['versionLimit_'.$ret['data']['activityId']]) > 0) {
            $versionLimit = json_decode($wordResult['versionLimit_'.$ret['data']['activityId']], true);
            if (!empty($versionLimit['android'])) {
                if (Molib_Client_Define::CLIENT_TYPE_ANDROID == self::$_clientType && Molib_Util_Version::compare($versionLimit['android'], self::$_clientVersion) < 0){
                    $data = array(
                        'url' => 'https://tieba.baidu.com/mo/q/worldcupredpacket',
                    );
                    $this->_objResponse->setOutData($data);
                    return true;
                }
            }
            if (!empty($versionLimit['ios'])) {
                if (Molib_Client_Define::CLIENT_TYPE_IPHONE == self::$_clientType && Molib_Util_Version::compare($versionLimit['ios'], self::$_clientVersion) < 0) {
                    $data = array(
                        'url' => 'https://tieba.baidu.com/mo/q/worldcupredpacket',
                    );
                    $this->_objResponse->setOutData($data);
                    return true;
                }
            }
        }

        if ($allowId == null || $allowId === false || !in_array($ret['data']['activityId'], $allowId)) {
            $this->_objResponse->setOutData(null);
            return true;
        }
        if ($ret['errno'] != 0) {
            Tieba_Stlog::addNode('cuid', '');
            Tieba_Stlog::addNode('uid', 0);
            Bingo_Log::warning('cuid: '.$this->_objRequest->getPrivateAttr('cuid'));
            Bingo_Log::warning('uid:'.$uid);
        } else {
            $this->_objRequest->addCommonAttr('user_id', $uid);
        }
        $this->_objResponse->setOutData($ret['data']);

        return true;
    }

    /**
     * @param $objType
     * @param $uid
     * @param $status
     * @param $shoubaicuid
     * @param int $activityId
     */
    private function _stlog($objType, $uid, $status, $shoubaicuid, $activityId = -1) {
        Tieba_Stlog::addNode('activity_id', $activityId);
        Tieba_Stlog::addNode('obj_id', 1);  // 1代表手百领百万活动
        Tieba_Stlog::addNode('obj_type', $objType);
        Tieba_Stlog::addNode('uid', $uid);
        Tieba_Stlog::addNode('cuid', $this->_objRequest->getCommonAttr('cuid'));
        Tieba_Stlog::addNode('new_cuid', $shoubaicuid);
        Tieba_Stlog::addNode('timestamp', time());
        Tieba_Stlog::addNode('finish_status', $status);
        Tieba_Stlog::addNode('obj_param1', $status);
        Tieba_Stlog::addNode('isfp', self::$_isfp);
        Tieba_Stlog::addNode('obj_tk', md5(self::$_token));
    }
}

