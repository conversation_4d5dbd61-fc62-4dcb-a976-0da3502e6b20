<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file uploadFlutterInfoAction.php
 * <AUTHOR>
 * @date 2019/07/18 15:19:10
 * @brief flutter版本客户端暂不支持长连接，故通过该短连接接口上传设备信息通知server
 *
 **/

class uploadFlutterInfoAction extends Molib_Client_BaseAction
{
    private $arrOutData = array();

    public function _getPrivateInfo()
    {
        return array(
            'check_login'   => false,
            'user_id'       => Bingo_Http_Request::getNoXssSafe('user_id', 0),
            'imei'          => Bingo_Http_Request::getNoXssSafe('imei', ''),
            'cuid'          => Bingo_Http_Request::getNoXssSafe('cuid', ''),
            'ios_token'     => Bingo_Http_Request::getNoXssSafe('ios_token', ''),
            'channel_id'    => Bingo_Http_Request::getNoXssSafe('channel_id', ''),
            'ispv' => 1,
        );
    }

    public function _checkPrivate()
    {
        return true;
    }

    public function _execute()
    {
        $strIosToken        = $this->_objRequest->getPrivateAttr('ios_token', '');
        $strChannelId       = $this->_objRequest->getPrivateAttr('channel_id', '');
        $strImei            = $this->_objRequest->getCommonAttr('phone_imei', '');
        $strCuid            = $this->_objRequest->getCommonAttr('cuid', '');
        $intUserid          = $this->_objRequest->getCommonAttr('user_id', 0);
        $strSubappType      = $this->_objRequest->getCommonAttr('subapp_type', '');
        $strClientVersion   = $this->_objRequest->getCommonAttr('client_version', '0.0.0');
        $intClientType      = intval($this->_objRequest->getCommonAttr('client_type', 0));

        $arrOutput = array(
            'imei'              => $strImei,
            'cuid'              => $strCuid,
            'ios_token'         => $strIosToken,
            'channel_id'        => $strChannelId,
            'user_id'           => $intUserid,
            'strSubappType'     => $strSubappType,
            'client_version'    => $strClientVersion,
            'client_type'       => $intClientType,
        );

        Bingo_Log::notice("uploadFlutterInfo get data [" . serialize($arrOutput) . "]");

        // 暂时先把接口返回置空防止一些关键信息外泄，客户端暂不要求返回什么数据
        $this->arrOutData = array();
        $this->_objResponse->setOutData($this->arrOutData);

        return true;
    }
}


 /* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>