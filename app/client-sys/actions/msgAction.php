<?php
class msgAction extends Molib_Client_BaseAction {
	private $arrMessageCount = array(
		'fans' => 0,
		'replyme' => 0,
		'atme' => 0,
		'agree'	=> 0,
	);
	private $intPletterCount = 0;
	private $intStoreThreadCount = 0;
	
	const STORE_THREAD_RN = 50;
	const STORE_THREAD_OFFSET = 0;
	
	protected function _getPrivateInfo() {
		
		$this->_objRequest->setStrategy(array('check_nonu' => false));
		
		return array(
			'check_login' => false,
			'ispv' => 0,	
			'bookmark' => Bingo_Http_Request::get('bookmark', 0),
		);
	}
	
	protected function _checkPrivate() {
		return true;
	}
	
	protected function _execute() {
		$bolIsLogin = $this->_objRequest->getCommonAttr('login', false);
		
		if ($bolIsLogin) {
            if ( Molib_Util_Version::compare('5.6.0', $this->_objRequest->getCommonAttr('client_version', '')) < 0) {
			    $this->arrMessageCount = $this->_getMessageCount();
			    $this->intPletterCount = $this->_getPletterCount();
            }
            //android 6.4.0需要： 增加回复+at+粉丝提醒
			if ((Molib_Util_Version::compare('6.4.0', $this->_objRequest->getCommonAttr('client_version', '')) >= 0 && $this->_objRequest->getCommonAttr('client_type')  == Molib_Client_Define::CLIENT_TYPE_ANDROID) ||
			(Molib_Util_Version::compare('6.3.0', $this->_objRequest->getCommonAttr('client_version', '')) >= 0 && $this->_objRequest->getCommonAttr('client_type')  == Molib_Client_Define::CLIENT_TYPE_IPHONE)) {
			    $this->arrMessageCount = $this->_getMessageCount();
            }
			if ($this->_objRequest->getPrivateAttr('bookmark', 0)) {
				$this->intStoreThreadCount = $this->_getStoreThreadCount();
			}
		}
		
		$this->_buildResponse();
	}
	
	private function _getMessageCount() {


			//Tieba_Service:call
			$strServiceName = 'messagepool';
			$strMethod = 'getMsg';
		
			$intUserId = $this->_objRequest->getCommonAttr('user_id', 0);

			$arrInput = array(
							  'user_id' => $intUserId,
							  );
		
			// 打印日志 记录传递给Service内容
			Bingo_Log::debug(sprintf('talk to servicename:[%s] method:[%s] input:[%s] ',$strServiceName,$strMethod, serialize($arrInput) ) );
		
			//和service交互
			Bingo_Timer::start("{$strServiceName}_{$strMethod}");
			$arrOutput = Tieba_Service::call($strServiceName,$strMethod,$arrInput,null,null);
			Bingo_Timer::end("{$strServiceName}_{$strMethod}");
		
			if (false === $arrOutput ) {
				Bingo_Log::fatal(sprintf('call messagepool error. Failed to call servicename:[%s] method:[%s][user_name:%s]',	$strServiceName,$strMethod, serialize($arrInput) ));
				return array(
							 'fans' => 0,
							 'replyme' => 0,
							 'atme' => 0,
							 'agree' => 0,
							);
			}
			//check err_no
			if ( isset($arrOutput['errno']) && (0 == intval($arrOutput['errno'])) ) {
				//success nothing to do
				$arrRet = array(
				    'fans' 		=> $arrOutput['data'][1],
				    'replyme'	=> $arrOutput['data'][4],
				    'atme'		=> $arrOutput['data'][9],
				    'agree'		=> $arrOutput['data'][25],
				);
				return $arrRet;
		
			} else {
				//err,print log
				Bingo_Log::warning(sprintf('Err to call servicename:[%s] method:[%s] [input:%s] [output:%s]',$strServiceName,$strMethod,serialize($arrInput),serialize($arrOutput)));
				return array(
							 'fans' => 0,
							 'replyme' => 0,
							 'atme' => 0,
							 'agree' => 0,
							);
			}


		//$strPortrait = $this->_objRequest->getCommonAttr('portrait', '');
		//
		//$strUrl = '/i/msg/get_data';
		//$arrParam = array(
		//	'user' => $strPortrait,
		//);
		//$strOut = Tbapi_Core_Midl_Http::httpcall('message_http', $strUrl, $arrParam);
		//if ($strOut == false || $strOut == ''){

		//	if(false === $strOut){
		//		Bingo_Log::fatal("call message_http error[url:$strUrl][user:$strPortrait]");
		//	}else{
		//		Bingo_Log::warning("call message_http error[url:$strUrl][user:$strPortrait]");
		//	}
		//	return array(
		//		'fans' => 0,
		//		'replyme' => 0,
		//		'atme' => 0,
		//	);
		//}
		//
		//$arrSearch = array('(', ')', '[', ']', 'initItiebaMessage', ';');
		//$strOut = str_replace($arrSearch, '', $strOut);
		//$arrMessage = explode(',', $strOut);
		//$arrMessage = array_map('intval',$arrMessage);
		//
		//$arrRet = array(
		//	'fans' => $arrMessage[0],
		//	'replyme' => $arrMessage[3],
		//	'atme' => $arrMessage[8],
		//);
		//return $arrRet;
		
	}
	
	private function _getPletterCount() {
		$intUserId = $this->_objRequest->getCommonAttr('user_id', 0);
		$arrInput = array(
			'user_id' => $intUserId,
		);
		$arrOut = Molib_Tieba_Service::call('pletter', 'getUnreadNumber', $arrInput);
		if ($arrOut === false || $arrOut['errno'] !== 0 || !isset($arrOut['number'])){
			Bingo_Log::warning('call pletter:getUnreadNumber failed.  [sevice_name:pletter] [method:getUnreadNumber] [input:'.serialize($arrInput).'] [output:'.serialize($arrOutput).']');
			return 0;
		}
		
		return $arrOut['number'];
	}
	
	private function _getStoreThreadCount() {
		$intUserId = $this->_objRequest->getCommonAttr('user_id', 0);
		$arrCookie = $this->_objRequest->getCommonAttr('cookie', array());
		
		$arrInput = array(
        	'user_id' => $intUserId,
        	'offset'  => self::STORE_THREAD_OFFSET,
        	'limit'	  => self::STORE_THREAD_RN,
			'bduss'	  => $arrCookie['BDUSS'],
        );
		
		$arrOut = Molib_Tieba_Service::call('post','queryStoreThreads', $arrInput);
		if ($arrOut === false || $arrOut['errno'] != 0 || !is_array($arrOut['output']['replys'])) {
			Bingo_Log::warning('call post:queryStoreThreads failed.  [sevice_name:post] [method:queryStoreThreads] [input:'.serialize($arrInput).'] [output:'.serialize($arrOutput).']');
			return 0;
		}
		
		$arrStoreThread = $arrOut['output']['replys'];
		$intCount = 0;
		foreach ($arrStoreThread as $arrItem){
			if ($arrItem['count']) {
				$intCount++;
			}
		}
		return $intCount;
	}
	
	private function _buildResponse() {
		
		$arrResponse['message'] = array(
			'fans' => intval($this->arrMessageCount['fans']),
			'replyme' => intval($this->arrMessageCount['replyme']),
			'atme' => intval($this->arrMessageCount['atme']),
			'agree' => intval($this->arrMessageCount['agree']),
			'pletter' => $this->intPletterCount,
			'bookmark' => $this->intStoreThreadCount,
			'count' => intval($this->arrMessageCount['fans']) + intval($this->arrMessageCount['replyme']) + intval($this->arrMessageCount['atme'])
						+ $this->intPletterCount + $this->intStoreThreadCount,
		);
		
		$this-> _objResponse->setOutData($arrResponse);
	}
}
