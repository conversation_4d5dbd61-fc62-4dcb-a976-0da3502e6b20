<?php
/**
 * 实名认证
 */
class checkRealNameAction extends Molib_Client_BaseAction
{
    const REQ_FROM_PB_SHARE = 'pb_share'; // pb分享
    private static $_arrNoNeedCheckSource = array(
        self::REQ_FROM_PB_SHARE,
    );

    private static $_strObjSource = '';

    /**
     * @return void
     */
    public function _getPrivateInfo()
    {
        $arrPrivateInfo['check_login'] = true;
        $arrPrivateInfo['need_login'] = true;
        $arrPrivateInfo['check_real_name'] = true;
        $arrPrivateInfo['ispv'] = 0;

        // pb分享操作去除实名制限制
        self::$_strObjSource = $this->_getInput('obj_source', self::REQ_FROM_PB_SHARE);
        if (in_array(self::$_strObjSource, self::$_arrNoNeedCheckSource)) {
            $arrPrivateInfo['check_real_name'] = false;
        }

        Tieba_Stlog::addNode('obj_source', self::$_strObjSource);
        Tieba_Stlog::addNode('check_real_name', $arrPrivateInfo['check_real_name']);

        return $arrPrivateInfo;
    }

    /**
     * @return 
     */
    public function _checkPrivate()
    {

    	return true;
    }

    /**
     * @return 
     */
    public function _execute()
    {
        return true;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
?>
