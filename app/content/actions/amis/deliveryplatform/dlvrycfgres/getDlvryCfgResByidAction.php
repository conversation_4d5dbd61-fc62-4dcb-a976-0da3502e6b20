<?php
class getDlvryCfgResByidAction extends Util_Action{
	
	/**
	 * (non-PHPdoc)
	 * @see Util_Action::_execute()
	 * @param null
	 * @return null
	 */
	public function _execute(){
		$cfgResid = intval(Bingo_Http_Request::get('cfgres_id',0));
		if($cfgResid <= 0){
			Bingo_Log::warning('getSelectCfgResAction invalid param.cfgResid:['.serialize($cfgResid).']');
			return $this->printOut(Tieba_Errcode::ERR_PARAM_ERROR);
		}
		$arrInput = array(
			'cfgres_id' => $cfgResid,
		);
		$arrOut = Tieba_Service::call('content', 'getDlvryCfgResByCondition', $arrInput, null, null, 'post', 'php', 'utf-8');
		if(false == $arrOut || !isset($arrOut['errno'])
				|| Tieba_Errcode::ERR_SUCCESS != $arrOut['errno']){
			Bingo_Log::warning('call content getDlvryCfgResByCondition fail.input:['.serialize($arrInput).'] out:['.serialize($arrOut).']');
			return $this->printOut(isset($arrOut['errno'])?$arrOut['errno']:Tieba_Errcode::ERR_CALL_SERVICE_FAIL);
		}
		return $this->printOut(Tieba_Errcode::ERR_SUCCESS, '', $arrOut['data'][0]);
	}
	
}