<?php
/**
 * Created by Php<PERSON>torm.
 * User: lilinke
 * Date: 2019/4/1
 * Time: 上午10:57
 */
define ('EASYSCRIPT_DEBUG',false);          //debug 模式
define ('EASYSCRIPT_THROW_EXEPTION',true);  //抛异常模式

define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../../../' );
define ('SCRIPTNAME',basename(__FILE__,".php"));   //定义脚本名
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf");
define ('DATAPATH',BASEPATH."/data");
define ('LOGPATH',"./log/content/script/spider");
define('IS_ORP_RUNTIME', true);
set_include_path(get_include_path() . PATH_SEPARATOR. BASEPATH.'/../../../' . PATH_SEPARATOR. BASEPATH. PATH_SEPARATOR. BASEPATH.'/lib/'. PATH_SEPARATOR.BASEPATH.'/../');

require_once ROOT_PATH . "app/content/script/spider/lib/simple_html_dom.php";
require_once ROOT_PATH . "app/content/script/spider/lib/util.php";
require_once ROOT_PATH . "app/content/script/spider/base/SpiderSite.php";
require_once ROOT_PATH . "app/content/script/spider/douban/ContentPage.php";
require_once ROOT_PATH . "app/content/script/spider/douban/SiteTab.php";

/**
 *
 * @param：null
 * @return：string
 */
function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');


/**
 * 日志行打印
 * @param $strMsg
 * @param bool $bolLineInfo
 * @return null
 */
function outLogLine($strMsg, $type = 'w', $bolEcho = false, $bolLineInfo = true)
{
    $strFileName = '';
    $intLineNo = 0;
    if ($bolLineInfo) {
        if (defined('DEBUG_BACKTRACE_IGNORE_ARGS')) {
            $arrTrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        } else {
            $arrTrace = debug_backtrace();
        }
        if (!empty($arrTrace)) {
            $strFileName = basename($arrTrace[0]['file']);
            $intLineNo = intval($arrTrace[0]['line']);
            //$strMsg = $strFileName . ":Line($intLineNo):" . $strMsg;
        }
    }
    if ('n' == $type) {
        Bingo_Log::notice($strMsg, '', $strFileName, $intLineNo);
    } else if ('w' == $type) {
        Bingo_Log::warning($strMsg, '', $strFileName, $intLineNo);
    } else if ('f' == $type) {
        Bingo_Log::fatal($strMsg, '', $strFileName, $intLineNo);
    } else {
        Bingo_Log::warning($strMsg, '', $strFileName, $intLineNo);
    }
    if ($bolEcho) {
        $strLogPrefix = '';
        if(!empty($strFileName)){
            $strLogPrefix = $strFileName;
        }
        if(0 < $intLineNo){
            $strLogPrefix = $strFileName.'['.$intLineNo.']';
        }
        if(!empty($strLogPrefix)){
            $strMsg = $strLogPrefix.' '.$strMsg;
        }
        echo $strMsg . PHP_EOL;
    }
}



$modCount = 1;
$modBase = 1;
$needGrabPageCnt = 1;
$grabPattern = Lib_Util::GRAB_ALL;
Lib_Util::$CHANNEL = 101;
if (null != $argv && !empty($argv) && is_array($argv) && 3 == count($argv)) {
    $modBase = intval($argv[1]);
    $modCount = intval($argv[2]);
    Lib_Util::outLogLine('getQiCheZhiJiaDataScript  script get input param.argv:['.serialize($argv).']', 'w',ture);
} else {
    Lib_Util::outLogLine('getQiCheZhiJiaDataScript  script get invaild input param.argv:['.serialize($argv).']', 'w',ture);
    exit(1);
}
$strScriptName = 'getQiCheZhiJiaDataScript_' . $modBase . '_' . $modCount;

// $arrSubTab = array();
// if(!empty($siteTab)){
//     if(false !== strpos($siteTab, ',')){
//         $arrSubTab = explode(',', $siteTab);
//         $arrSiteTab = array_filter($arrSiteTab);
//         $newStr = str_replace(',', '_', $siteTab);
//         $strScriptName = $strScriptName.'_'.$newStr;
//     }else{
//         $strScriptName = $strScriptName.'_'.$siteTab;
//         $arrSiteTab[] = $siteTab;
//     }
// }
// $arrSiteTab = array_filter($arrSiteTab);
// $arrSiteTab = array_unique($arrSiteTab);




define ('LOG', 'log');
Bingo_Log::init(array(
    LOG => array(
        'file'  => LOGPATH ."/". $strScriptName. ".log",
        'level' => 0x0f,
    ),
), LOG);


$easyScriptObj  = false;
try{
    outLogLine('getQiCheZhiJiaDataScript ready run.', 'n', true);
    $ScriptConf = array(
        'memory_limit'   => '1024M',
        'data_path'      => DATAPATH,
        'conf_path'      => CONFPATH,
        'lock_file_name' => $strScriptName.'.lock',
        'done_file_name' => $strScriptName.'.done',
        'db_alias'       => array(
            'im'     => 'forum_content',
        ),
        'conf_alias'     => array(
            'main'     => 'main.conf',
        ),
    );

    $easyScriptObj = new Util_EasyScript($ScriptConf);
    //防止脚本重复执行
    if( $easyScriptObj->checkScriptIsRuning() === true ){
        //outLogLine('getPipixiaDatascript is runing.', 'n', true);
        //exit;
    }
    outLogLine('getQiCheZhiJiaDataScript begin runing.', 'n', true);
    $getObj = new getQiCheZhiJiaDataAction();
    $arrSiteTab = getQiCheZhiJiaDataAction::GetSiteTabs($modBase,$modCount);
    $getObj->_execute($arrSiteTab,$needGrabPageCnt,$grabPattern);
    $easyScriptObj->runSuccess();
    outLogLine('getQiCheZhiJiaDataScript run success.', 'n', true);
}catch(Exception $e){
    if($easyScriptObj !== false){
        outLogLine('getQiCheZhiJiaDataScript run fail!'.$easyScriptObj->getErr2String(), 'w');
    }else{
        outLogLine('getQiCheZhiJiaDataScript run fail![null]', 'w');
    }
    outLogLine('getQiCheZhiJiaDataScript fail.'.$easyScriptObj->getErr2String());
    exit;
}
outLogLine('video getQiCheZhiJiaDataScript finish.', 'n', true);


class getQiCheZhiJiaDataAction{
    /**
     * @param $siteTabid
     * @param $needGrabPageCnt
     */
    public static function _execute($arrSiteTab,$needGrabPageCnt,$grabPattern){
        foreach ($arrSiteTab as $siteTab){
            $arrInput = array(
                'site_id' => 101,
                //'sub_tab' => '赘婿',
                //'sub_tab' => '万古神帝',
                'sub_tab' => '',
                'grab_page_count'    => $needGrabPageCnt,
                'grab_pattern'       => $grabPattern,
                'need_continue_grab' => false,
            );
            if('all' != $siteTab){
                $arrInput['sub_tab'] = $siteTab;
            }
            $grabSite = new Base_SpiderSite();
            $grabSite->_executeSpider($arrInput);
        }
    }

    public static function GetSiteTabs($mod, $idx){
        $arrInput = array (
            'function' => 'getGrabSiteBySiteid',
            'site_id' => 101,
        );
        $arrOutput = Tieba_Service::call ( 'common', 'grabResource', $arrInput );
        if ($arrOutput == false || $arrOutput['errno'] != 0){
            return false;
        }
        $arrTabNames = json_decode($arrOutput['data'][0]['site_tabinfo'],true);
        $arrTab = array();
        $id = 0;
        foreach($arrTabNames['tabconfig'] as $tabName => $tabid){
            if ($id % $mod === $idx){
                $arrTab[] = $tabName;
            }
            $id++;
        }
        return $arrTab;
    }
}
