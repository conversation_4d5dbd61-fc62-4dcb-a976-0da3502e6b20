<?php
/**
 * 获取创建任务,如果状态是2 拉取数据,状态是3 重新执行任务
 */


define ('EASYSCRIPT_DEBUG',false);          //debug 模式
define ('EASYSCRIPT_THROW_EXEPTION',true);  //抛异常模式

define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../../../' );
define ('SCRIPTNAME',basename(__FILE__,".php"));   //定义脚本名
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf");
define ('DATAPATH',BASEPATH."/data");
define ('LOGPATH',"./log/content/script/spider");
define('IS_ORP_RUNTIME', true);
set_include_path(get_include_path() . PATH_SEPARATOR. BASEPATH.'/../../../' . PATH_SEPARATOR. BASEPATH. PATH_SEPARATOR. BASEPATH.'/lib/'. PATH_SEPARATOR.BASEPATH.'/../');



/**
 * 获取当前毫秒级时间戳
 * @param：null
 * @return：string
 */
function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');


/**
 * 日志行打印
 * @param $strMsg
 * @param bool $bolLineInfo
 * @return null
 */
function outLogLine($strMsg, $type='w', $bolEcho = false, $bolLineInfo = true){
    $strFileName = '';
    $intLineNo = 0;
    if($bolLineInfo) {
        if (defined('DEBUG_BACKTRACE_IGNORE_ARGS')) {
            $arrTrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        } else {
            $arrTrace = debug_backtrace();
        }
        if(!empty($arrTrace)) {
            $strFileName = basename($arrTrace[0]['file']);
            $intLineNo = intval($arrTrace[0]['line']);
            //$strMsg = $strFileName . ":Line($intLineNo):" . $strMsg;
        }
    }
    if('n' == $type){
        Bingo_Log::notice($strMsg, '', $strFileName, $intLineNo);
    }else if('w' == $type){
        Bingo_Log::warning($strMsg, '', $strFileName, $intLineNo);
    }else if('f' == $type){
        Bingo_Log::fatal($strMsg, '', $strFileName, $intLineNo);
    }else{
        Bingo_Log::warning($strMsg, '', $strFileName, $intLineNo);
    }
    if($bolEcho){
        echo $strMsg.PHP_EOL;
    }
}

if(null != $argv && !empty($argv) && is_array($argv) && 3 == count($argv)){
    $status = intval($argv[1]);
    $dimType=strval($argv[2]);
}else{
    printLog('tiebaDeliveryScript  script get invaild input param.argv:['.serialize($argv).']', 'w');
}

/**
 * @param
 * @return
 */
function statLog($strLog){
	$log_path = ROOT_PATH . "/log/stlog/";
	if(!file_exists($log_path)){
		mkdir($log_path, 0777, true);
	}
	$strLogFileName = $log_path . 'wap' . "." . strftime('%Y%m%d%H');
	$logStr = 'NOTICE: ' . strftime('%m-%d %H:%M:%S') . ':  stat-log' . ' * ' .  posix_getpid() . " " . $strLog . "\n";
	file_put_contents($strLogFileName, $logStr, FILE_APPEND);
}

/**
 * @param
 * @return
 */
function printLog($strLog, $type = 'n', $bolEcho = true){
    if(empty($type)){
        return ;
    }
    if('n' == $type){
        Bingo_Log::notice($strLog);
    }else if('w' == $type){
        Bingo_Log::warning($strLog);
    }else if('f' == $type){
        Bingo_Log::fatal($strLog);
    }
    if($bolEcho){
        echo $strLog."\n";
    }
}

define ('LOG', 'log');
Bingo_Log::init(array(
    LOG => array(
        'file'  => LOGPATH ."/". $strScriptName. ".log",
        'level' => 0x0f,
    ),
), LOG);

$easyScriptObj  = false;
try{
    outLogLine('tieba_kpi_getDataPlatformApi ready run.', 'n', true);
    $ScriptConf = array(
        'memory_limit'   => '1024M',
        'data_path'      => DATAPATH,
        'conf_path'      => CONFPATH,
        'lock_file_name' => $strScriptName.'.lock',
        'done_file_name' => $strScriptName.'.done',
        'db_alias'       => array(
            'im'     => 'forum_content',
        ),
        'conf_alias'     => array(
            'main'     => 'main.conf',
        ),
    );

    $easyScriptObj = new Util_EasyScript($ScriptConf);
    //防止脚本重复执行
    if( $easyScriptObj->checkScriptIsRuning() === true ){
        outLogLine('tieba_kpi_getDataPlatformApi is runing.', 'n', true);
        exit;
    }
    outLogLine('tieba_kpi_getDataPlatformApi begin runing.', 'n', true);

    $lastRunTime = 0;
    $lastRunTime = $easyScriptObj->getLastSaveData('last_run_time', 0);
    $runCount = 0;
    $runCount = $easyScriptObj->getLastSaveData('script_run_count', 0);

    $getObj = new getDataPlatformApiAction();
    $getObj->_execute($lastRunTime, $runCount,$status,$dimType);
    $easyScriptObj->runSuccess();
    outLogLine('tieba_kpi_getDataPlatformApi run success.', 'n', true);
}catch(Exception $e){
    if($easyScriptObj !== false){
        outLogLine('tieba_kpi_getDataPlatformApi run fail!'.$easyScriptObj->getErr2String(), 'w');
    }else{
        outLogLine('tieba_kpi_getDataPlatformApi run fail![null]', 'w');
    }
    outLogLine('tieba_kpi_getDataPlatformApi fail.'.$easyScriptObj->getErr2String());
    exit;
}
outLogLine('data tieba_kpi_getDataPlatformApi finish.', 'n', true);


class getDataPlatformApiAction {

    /**
     * @param $siteTabid
     * @return $needGrabPageCnt
     */
	public static function _execute(&$lastRunTime, &$runCount,$status,$dimType){

	    //根据状态获取数据
        $has_more =1;
        while($has_more){
            $arrInput = array(
                'status' => $status,
                'dim_type' =>$dimType,
            );
            //var_dump($arrInput);
            $arrRet = Tieba_Service::call('content', 'getDataPlatformapiByCondition', $arrInput);
            outLogLine('call service content::getDataPlatformapiByCondition, input:['.serialize($arrInput).']','n', true);
            if($arrRet['errno'] != 0){
                outLogLine('call service content::getDataPlatformapiByCondition fail, input:['.serialize($arrInput).'],output:['.serialize($arrRet).']','n', true);
            }
            $has_more=$arrRet['data']['has_more'];
            if(count($arrRet['data']['list']) ==0)
            {
                return true;
            }
            foreach ($arrRet['data']['list'] as $row){
                $strTaskName=strval($row['taskname']);
                $intTaskId=intval($row['taskid']);
                $strHql =strval($row['hql']);
                $strDimType =strval($row['dim_type']);
                $intId=intval($row['id']);
                var_dump($intTaskId);
                if($status==2){
                    //根据任务名词拉取任务的状态
                    $arrInput = array(
                        'taskName' => $strTaskName,
                        'userName' => "tieba_server_user",
                        'format' => "json",
                        'ie'=>"UTF-8",
                    );
                    $outdata=self::http_post('http://tc.service.tieba.baidu.com/service/dataplatformapi?method=dataCenterRunStatus', $arrInput, 1000);
                    if($outdata['err_no']==1){
                        outLogLine('input dataplatformapi_dataCenterRunStatus outdata error','n',true);
                        continue;
                    }
                    $curStatus=intval($outdata['data']['status']);
                    $fileName=strval($outdata['data']['fileName']);
                    //$intTaskId=strval($outdata['data']['data']['taskid']);

                    if($curStatus==2){
                        continue;
                    }else if($curStatus==0){
                        //获取文件,往小时表写数据

                        $cmd="cd ". DATAPATH . ";wget ".$fileName.' -O '.$strTaskName;

                        exec($cmd);

                        $tmpFile = fopen(DATAPATH .'/'. $strTaskName,'r');
                        $fileCount = 0;
                        if ($tmpFile == false){
                            outLogLine('fopen file fail_filename:['.$fileName.']','n',true);
                            continue;
                        }

                        while (!feof($tmpFile)){
                            $line = trim(fgets($tmpFile));
                            if(empty($line) || $line ==false){
                                continue;
                            }
                            $fileCount++;
                            $arrLine = explode("\t", $line);
                            $day = intval($arrLine[0]);
                            $hour = $arrLine[1];
                            $strCommand = strval($arrLine[2]);
                            $strOS = strval($arrLine[3]);
                            $strAppVersion = strval($arrLine[4]);
                            $strDimType = strval($arrLine[5]);
                            $intUV = intval($arrLine[6]);
                            $intPV = intval($arrLine[7]);
                            //$strDimType = strval($arrLine[5]);
                            /*if($strOS=='all'){
                                $strDimType='na_command_type';
                            }else{
                                $strDimType='na_version_type';
                            }*/


                            if(empty($strCommand)){
                                continue;
                            }
                            //先判断接口天级数据是否已经入库
                            $arrInput = array(
                                'day' => intval($day),
                                'command' => strval($strCommand),
                                'table' => 'dayhour',
                                'os' => $strOS,
                                'app_version' => $strAppVersion,
                                'dim_type' => $strDimType,
                                'hour'=>$hour,
                            );
                            if($strDimType == 'na_open_type' && strval($hour)=='all'){
                                $arrInput['table']='day';
                                unset($arrInput['hour']);
                            }
                            $arrRet = Tieba_Service::call('content', 'getKpiDataByCondition', $arrInput);
                            if($arrRet['errno'] != 0){
                                sleep(2);
                                $arrRet = Tieba_Service::call('content', 'getKpiDataByCondition', $arrInput);
                                if($arrRet['errno'] != 0){
                                    outLogLine('call service content::getKpiDataByCondition fail, input:['.serialize($arrInput).'],output:['.serialize($arrRet).']', 'n', true);
                                    continue;
                                }
                            }
                            if(count($arrRet['data']['list']) >0)
                            {
                                outLogLine('getKpiDataByCondition_is_datalist_number['.count($arrRet['data']['list']).'] input ['.serialize($arrInput).']', 'n', true);
                               // var_dump($arrInput);
                                continue;
                            }

                            $arrInput = array(
                                'day' => intval($day),
                                'hour' => intval($hour),
                                'command' => strval($strCommand),
                                'pv' => intval($intPV),
                                'uv' => intval($intUV),
                                'os' => strval($strOS),
                                'app_version' => strval($strAppVersion),
                                'dim_type' => strval($strDimType),
                            );
                            $strMethod='addKpiMonitorDay';
                            if($strDimType == 'na_open_type' && strval($hour)=='all'){
                                $strMethod='addKpiCommandDay';
                                unset($arrInput['hour']);
                            }
                           $arrRet = Tieba_Service::call('content', $strMethod, $arrInput);
                            if(false === $arrRet || $arrRet['errno'] != 0){
                                sleep(2);
                                $arrRet = Tieba_Service::call('content', 'addKpiMonitorDay', $arrInput);
                                if(false === $arrRet || $arrRet['errno'] != 0) {
                                    outLogLine('call service common::addKpiMonitorDay fail, input:[' . serialize($arrInput) . '],output:[' . serialize($arrRet) . ']', 'n', true);
                                }
                            }

                        }
                        //更新文件名到任务数据库中
                        $taskStatus=0;
                        if($fileCount == 0){
                            $taskStatus=3;
                        }
                        $arrInput = array(
                            'dim_type'=> strval($strDimType),
                            'id' => intval($intId),
                            'taskid' => intval($intTaskId),
                            'filename' => strval($fileName),
                            'taskname' => strval($strTaskName),
                            'status' => $taskStatus,
                        );

                        $arrRet = Tieba_Service::call('content', 'updateDataPlatformapi', $arrInput);
                        if($arrRet['errno'] != 0){
                            outLogLine('call service common::updateDataPlatformapi fail, input:['.serialize($arrInput).'],output:['.serialize($arrRet).']','n',true);
                        }
                        //删除数据文件
                        $tmpfilepath=DATAPATH .'/'.$strTaskName;
                        //var_dump('======='.$tmpfilepath);
                        unlink($tmpfilepath);

                    }else if($curStatus==3){
                        $arrInput = array(
                            'dim_type'=> strval($strDimType),
                            'id' => intval($intId),
                            'taskid' => intval($intTaskId),
                            'filename' => strval($fileName),
                            'taskname' => strval($strTaskName),
                            'status' => 3,
                        );
                        $arrRet = Tieba_Service::call('content', 'updateDataPlatformapi', $arrInput);
                        if($arrRet['errno'] != 0){
                            outLogLine('call service common::updateDataPlatformapi fail, input:['.serialize($arrInput).'],output:['.serialize($arrRet).']','n',true);
                        }
                    }
                }
                if($status==3){
                    //重新执行任务
                    $arrInput = array(
                        'taskId' => $strDimType.'_'.$intTaskId,
                        'userName' => "tieba_server_user",
                        'token'=>'a4bae8a379538cf7',
                        'hql' => $strHql,
                        'format' => "json",
                        'ie'=>"UTF-8",
                        'level'=>4,
                    );
                    $outdata=self::http_post('http://tc.service.tieba.baidu.com/service/dataplatformapi?method=dataCenterHiveQuery', $arrInput, 1000);
                    if($outdata['err_no']==1){
                        outLogLine('input dataplatformapi outdata error','n',true);
                        return true;
                    }
                    //更新任务表中数据
                    $fileName ='';//strval($row['filename']);
                    $arrInput = array(
                        'dim_type'=> strval($strDimType),
                        'id' => intval($intId),
                        'taskid' => intval($intTaskId),
                        'filename' => strval($fileName),
                        'taskname' => strval($outdata['data']['taskName']),
                        'status' => 2,
                    );
                    $arrRet = Tieba_Service::call('content', 'updateDataPlatformapi', $arrInput);
                    if($arrRet['errno'] != 0){
                        outLogLine('call service common::updateDataPlatformapi fail, input:['.serialize($arrInput).'],output:['.serialize($arrRet).']','n',true);
                    }
                }
            }
        }
    }

    /**
     * @param $siteTabid
     * @return $needGrabPageCnt
     */
    public static function  http_post($url, $post, $timeout) {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $post);
        curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Expect:'));
        $output = curl_exec($curl);
        $code  = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $errMsg= curl_multi_getcontent($curl);
        $err = curl_error($curl);
        curl_close($curl);
        /*var_dump($code);
        var_dump($errMsg);
        var_dump($err);
        var_dump($output);*/

        if($code != 200) {
            $arrRet = array(
                'err_no'    => 1,
                'err_msg' => $code,
                'data'    => $errMsg,
            );
            return $arrRet;
        }
        $output=json_decode($output,true);
        $arrRet = array(
            'err_no'    => 0,
            'err_msg' => $code,
            'data'    => $output['data'],
        );
        return $arrRet;

    }


}


