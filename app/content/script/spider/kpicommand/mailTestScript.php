<?php
/**
 * 发送待dau邮件新版v3
 */
define ('EASYSCRIPT_DEBUG',false);          //debug 模式
define ('EASYSCRIPT_THROW_EXEPTION',true);  //抛异常模式

define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../../../' );
define ('SCRIPTNAME',basename(__FILE__,".php"));   //定义脚本名
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf");
define ('DATAPATH',BASEPATH."/data");
define ('LOGPATH',"./log/content/script/spider");
define('IS_ORP_RUNTIME', true);
set_include_path(get_include_path() . PATH_SEPARATOR. BASEPATH.'/../../../' . PATH_SEPARATOR. BASEPATH. PATH_SEPARATOR. BASEPATH.'/lib/'. PATH_SEPARATOR.BASEPATH.'/../');
require_once(dirname(__FILE__) . "/../../dm/Mail_MSGLib/SendMail.php");
require_once(dirname(__FILE__) . "/../../dm/Mail_MSGLib/message.php");
require_once(dirname(__FILE__). "/../lib/util.php");


/**
 * 日志行打印
 * @param $strMsg
 * @param bool $bolLineInfo
 * @return null
 */
function outLogLine($strMsg, $type='w', $bolEcho = false, $bolLineInfo = true){
    $strFileName = '';
    $intLineNo = 0;
    if($bolLineInfo) {
        if (defined('DEBUG_BACKTRACE_IGNORE_ARGS')) {
            $arrTrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
        } else {
            $arrTrace = debug_backtrace();
        }
        if(!empty($arrTrace)) {
            $strFileName = basename($arrTrace[0]['file']);
            $intLineNo = intval($arrTrace[0]['line']);
            //$strMsg = $strFileName . ":Line($intLineNo):" . $strMsg;
        }
    }
    if('n' == $type){
        Bingo_Log::notice($strMsg, '', $strFileName, $intLineNo);
    }else if('w' == $type){
        Bingo_Log::warning($strMsg, '', $strFileName, $intLineNo);
    }else if('f' == $type){
        Bingo_Log::fatal($strMsg, '', $strFileName, $intLineNo);
    }else{
        Bingo_Log::warning($strMsg, '', $strFileName, $intLineNo);
    }
    if($bolEcho){
        echo $strMsg.PHP_EOL;
    }
}

/**
 * @param
 * @return
 */
function statLog($strLog){
	$log_path = ROOT_PATH . "/log/stlog/";
	if(!file_exists($log_path)){
		mkdir($log_path, 0777, true);
	}
	$strLogFileName = $log_path . 'wap' . "." . strftime('%Y%m%d%H');
	$logStr = 'NOTICE: ' . strftime('%m-%d %H:%M:%S') . ':  stat-log' . ' * ' .  posix_getpid() . " " . $strLog . "\n";
	file_put_contents($strLogFileName, $logStr, FILE_APPEND);
}

/**
 * @param
 * @return
 */
function printLog($strLog, $type = 'n', $bolEcho = true){
    if(empty($type)){
        return ;
    }
    if('n' == $type){
        Bingo_Log::notice($strLog);
    }else if('w' == $type){
        Bingo_Log::warning($strLog);
    }else if('f' == $type){
        Bingo_Log::fatal($strLog);
    }
    if($bolEcho){
        echo $strLog."\n";
    }
}

define ('LOG', 'log');
$strScriptName="mailScript";
Bingo_Log::init(array(
    LOG => array(
        'file'  => LOGPATH ."/". $strScriptName. ".log",
        'level' => 0x0f,
    ),
), LOG);

$easyScriptObj  = false;
try{
    outLogLine('mailScript ready run.', 'n', true);
    $ScriptConf = array(
        'memory_limit'   => '1024M',
        'data_path'      => DATAPATH,
        'conf_path'      => CONFPATH,
        'lock_file_name' => $strScriptName.'.lock',
        'done_file_name' => $strScriptName.'.done',
        'db_alias'       => array(
            'im'     => 'forum_content',
        ),
        'conf_alias'     => array(
            'main'     => 'main.conf',
        ),
    );

    $easyScriptObj = new Util_EasyScript($ScriptConf);
    //防止脚本重复执行
    if( $easyScriptObj->checkScriptIsRuning() === true ){
        outLogLine('mailScript is runing.', 'n', true);
        exit;
    }
    outLogLine('mailScript begin runing.', 'n', true);

    $lastRunTime = 0;
    $lastRunTime = $easyScriptObj->getLastSaveData('last_run_time', 0);
    $runCount = 0;
    $runCount = $easyScriptObj->getLastSaveData('script_run_count', 0);

    $getObj = new mailTestScriptAction();
    //查询今天数据
    $getObj->_execute($lastRunTime, $runCount, "today");
    $easyScriptObj->runSuccess();
    outLogLine('mailScript run success.', 'n', true);
}catch(Exception $e){
    if($easyScriptObj !== false){
        outLogLine('mailScript run fail!'.$easyScriptObj->getErr2String(), 'w');
    }else{
        outLogLine('mailScript run fail![null]', 'w');
    }
    outLogLine('mailScript fail.'.$easyScriptObj->getErr2String());
    exit;
}
outLogLine('data mailScript finish.', 'n', true);


class mailTestScriptAction {
	
    /**
     * @param $siteTabid
     * @return $needGrabPageCnt
     */
	public static function _execute(&$lastRunTime, &$runCount ,$day='yesterday'){
		$wordlistConfig = 'tb_wordlist_redis_tb_wordlist_dau_monitor_config';
		$arrPhones = Lib_Util::getConf($wordlistConfig,'phoneList');
		$arrMails = Lib_Util::getConf($wordlistConfig,'maiList');
		$threshold = Lib_Util::getConf($wordlistConfig,'threshold','str');
		$threshold_gradient = Lib_Util::getConf($wordlistConfig,'threshold_gradient','str');
		$openthreshold = Lib_Util::getConf($wordlistConfig,'openthreshold','str');
		$repeatControl = Lib_Util::getConf($wordlistConfig,'repeatControl','str');
		$date = Lib_Util::getConf($wordlistConfig,'date','str');
		$dateControl = Lib_Util::getConf($wordlistConfig,'dateControl','str');
		$ver_threshold = Lib_Util::getConf($wordlistConfig,'ver_threshold','str');
		
		$threshold_ascend_hour = Lib_Util::getConf($wordlistConfig,'threshold_ascend_hour','str');
		$threshold_ascend_uv = Lib_Util::getConf($wordlistConfig,'threshold_ascend_uv','str');
		
		$mailAddress = implode(",", $arrMails);
		//测试账号
		$mailAddress="<EMAIL>";
		
		outLogLine('monitorHour phones:'.implode(",", $arrPhones), 'n', true);
		outLogLine('monitorHour mails:'.$mailAddress, 'n', true);
		outLogLine('monitorHour repeatControl:'.$repeatControl, 'n', true);
		outLogLine('monitorHour dateControl:'.$dateControl, 'n', true);
		outLogLine('monitorHour date:'.$date, 'n', true);
		outLogLine('monitorHour threshold:'.$threshold, 'n', true);
		outLogLine('monitorHour openthreshold:'.$openthreshold, 'n', true);
		//获取昨天日期
		$strYesterday = date('Ymd',time()-24*60*60);
		
		//指定日期
		$strYesterday=$date;
		
		outLogLine('monitorHour begin '.$day.':'.$strYesterday, 'n', true);
		
		$hours = array(0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23);
		
		$msg = new Lib_SendSmsp();
		$mailtitle="dau监控报警test".$strYesterday;
		
		$maiContent="";
		$maiContent.=Util_ReportUtil::getHtmlStart();
		//sql预热
		//组装邮件
		$hasAlarm = false;
		
		//报警类型
		$alarmTypeAscend = "ascend";
		
		
		//如果是节假日需要降级到前溯算法
		$bolHoliday = Util_DauDataProcess::holidayJudge($strYesterday);
		if($bolHoliday){
			outLogLine("is holiday:".$strYesterday, 'n', true);
		}
		
		//获得报警开始时间
		$lastHour = -1;
		foreach ($hours as $hour){
			$hasSendInterface = self::hasSendAlarm($strYesterday, $hour, "interface");
			if($hasSendInterface == 1){
				$lastHour = $hour;
			}
			$hasSendInterface1 = self::hasSendAlarm($strYesterday, $hour, "interface1");
			if($hasSendInterface1 == 1){
				$lastHour = $hour;
			}
			$hasSendAscend = self::hasSendAlarm($strYesterday, $hour, $alarmTypeAscend);
			if($hasSendAscend == 1){
				$lastHour = $hour;
			}
		}
		
		foreach ($hours as $hour){
			if($hour<=$lastHour){
				continue;
			}
			$inferfaceAlarm['data']['rows']=false;
			$inferfaceGradientAlarm['data']['rows']=false;
			$openAlarm['data']['rows']=false;
			$versionAlarm['data']['rows']=false;
			$inferfaceAscendAlarm['data']['rows']=false;
			
			$hasSendInterface = self::hasSendAlarm($strYesterday, $hour, "interface");
			$hasSendInterface1 = self::hasSendAlarm($strYesterday, $hour, "interface1");
			$hasSendOpen = self::hasSendAlarm($strYesterday, $hour, "open");
			$hasSendVersion = self::hasSendAlarm($strYesterday, $hour, "version");
			$hasSendAscend = self::hasSendAlarm($strYesterday, $hour, $alarmTypeAscend);
			
			if(($hasSendInterface1==0)&&$repeatControl=='on'){
				$inferfaceGradientAlarm = Util_DauDataProcess::getAlarmInterfaceByHourAngle($strYesterday, $hour);
				//持续波动接口报警
				outLogLine('hasSendAlarm '.$strYesterday.$hour.':'."interface1=0", 'n', true);
			}else if($repeatControl=='off'){
				$inferfaceGradientAlarm = Util_DauDataProcess::getAlarmInterfaceByHourAngle($strYesterday, $hour);
			}else{
				outLogLine('hasSendAlarm '.$strYesterday.$hour.':'."interface1=1", 'n', true);
			}
			
		    //持续报警
			if(($hasSendAscend==0)&&$repeatControl=='on'){
				//持续波动接口报警
				$inferfaceAscendAlarm = Util_DauDataProcess::getAlarmInterfaceByHourAscend($strYesterday, $hour);
				outLogLine('hasSendAscend '.$strYesterday.$hour.':'."hasSendAscend=0", 'n', true);
			}else if($repeatControl=='off'){
				$inferfaceAscendAlarm = Util_DauDataProcess::getAlarmInterfaceByHourAscend($strYesterday, $hour);
			}else{
				outLogLine('hasSendAscend '.$strYesterday.$hour.':'."hasSendAscend=1", 'n', true);
			}
			//设置报警已发标识
			if(!empty($inferfaceAlarm['data']['rows'])){
				self::setSendAlarm($strYesterday, $hour, "interface");
				outLogLine('setSendAlarm '.$strYesterday.$hour.':'."interface", 'n', true);
			}
			if(!empty($inferfaceGradientAlarm['data']['rows'])){
				self::setSendAlarm($strYesterday, $hour, "interface1");
				outLogLine('setSendAlarm '.$strYesterday.$hour.':'."interface1", 'n', true);
			}
			if(!empty($openAlarm['data']['rows'])){
				self::setSendAlarm($strYesterday, $hour, "open");
				outLogLine('setSendAlarm '.$strYesterday.$hour.':'."open", 'n', true);
			}
			if(!empty($versionAlarm['data']['rows'])){
				self::setSendAlarm($strYesterday, $hour, "version");
				outLogLine('setSendAlarm '.$strYesterday.$hour.':'."version", 'n', true);
			}
			if(!empty($inferfaceAscendAlarm['data']['rows'])){
				self::setSendAlarm($strYesterday, $hour, $alarmTypeAscend);
				outLogLine('setSendAlarm ascend '.$strYesterday.$hour.':'."version", 'n', true);
			}
			
			
			$interfaceDesc="接口pv或uv波动超过阈值".$threshold."%时触发报警";
			$interfaceGradientDesc="uv波动超过阈值".$threshold."%时触发报警";
			$openDesc="启动uv波动超过阈值".$openthreshold."%时触发报警";
			$verDesc="版本pv或uv波动超过阈值".$ver_threshold."%时触发报警";
			$threshold_ascend_hour_show =$threshold_ascend_hour+1;
			$ascendDesc="接口波动超过阈值".$threshold_ascend_uv."%并且持续".$threshold_ascend_hour_show."小时时触发报警";
			if(!$bolHoliday){
				$maiContent.=Util_ReportUtil::getTableContent("dau接口报警".$hour."点", $inferfaceGradientAlarm['data'],$interfaceGradientDesc);
			}else{
				$inferfaceGradientAlarm['data']['rows'] = false;
			}
			
			$maiContent.=Util_ReportUtil::getTableContent("dau接口持续波动报警".$hour."点", $inferfaceAscendAlarm['data'],$ascendDesc);
			if(!empty($inferfaceAlarm['data']['rows'])||!empty($inferfaceGradientAlarm['data']['rows'])||!empty($openAlarm['data']['rows'])||!empty($versionAlarm['data']['rows'])||!empty($inferfaceAscendAlarm['data']['rows'])){
				$hasAlarm=true;
			}
		}
		
		$maiContent.=Util_ReportUtil::getHtmlEnd();
		outLogLine('monitorHour mails:'.$mailAddress, 'n', true);
		
		if($hasAlarm){
			$mailRet = self::sendToMail($mailAddress, $maiContent,'',$mailtitle);
			$msg ->setTo($arrPhones);
			//发送短信
			//$alarm = "贴吧DAU小时级接口报警:".$strYesterday.":".$hour.'小时,共:'.$count."条,详情请看邮件";
			$alarm = "DAU接口小时级报警:".$strYesterday.":详情请看邮件";
			$msg ->setMes(Bingo_Encode::convert($alarm, 'gbk', 'utf-8'));
			$res = $msg ->send();
			outLogLine('text msg ret:'.$res, 'n', true);
			outLogLine('mail msg ret:'.serialize($mailRet), 'n', true);
		}
		
	}
    
	
	/**
	 *
	 * @param unknown $targetRecvMail：支持多个邮箱，以逗号隔开
	 * @param unknown $mailContent
	 * @param string $title
	 * @param string $sender
	 * @return array
	 */
	public static function sendToMail($targetRecvMail = '<EMAIL>', $mailContent,$filePath, $title = 'DAU监控报警', $sender = '<EMAIL>'){
		if(empty($targetRecvMail) || empty($mailContent)){
			Bingo_Log::warning('_sendAlarmsToMail input param invalid,targetRecvMail:['.serialize($targetRecvMail).'] mailContent:['.serialize($mailContent).']');
			return false;
		}
		$mail = new SendMail();
		$mail->setTo($targetRecvMail);
		$mail->setFrom($sender);
		$mail->setCharset("utf-8");
		$mail->setSubject($title);
		$mail->setAttachments($filePath);
		$mail->setText('text');
		$mail->setHTML($mailContent);
		$result = $mail->send();
		if ($result == false) {
			Bingo_Log::warning( "send mail fail.mailcontent:[".$mailContent.']');
			return false;
		}
		return true;
	}
    
	
	/**
	 * 判断是否发过报警
	 * @param string $sender
	 * @return array
	 */
	private static function hasSendAlarm($day,$hour,$type){
		$key =  "redis_dau_monitor_key_".$day.$hour.$type;
		$arrInput =  array(
				'key' => $key,
		);
		$arrOut = Lib_Redis::call("GET", $arrInput);
		$isSend = 0;
		if (!$arrOut || $arrOut['err_no'] != 0) {
			outLogLine('hasSendAlarm fatal ,call redis get fails~ setInput:  '.serialize($arrInput).', arrOutput: '.serialize($arrOut), 'w',true);
		}else{
			$isSend = intval($arrOut['ret'][$key]);
		}
		outLogLine('hasSendAlarm isSend data:'.$isSend ." data:".serialize($arrOut), 'n',true);
		return  $isSend;
	}
    
	/**
	 * 设置发过报警
	 * @param string $sender
	 * @return array
	 */
	private static function setSendAlarm($day,$hour,$type){
		$setInput = array(
				'key' => "redis_dau_monitor_key_".$day.$hour.$type,
				'value' => 1,
				'seconds' => 48*60*60,
		);
		$setOutput = Lib_Redis::call("SETEX", $setInput);
		if (!$setOutput || $setOutput['err_no'] != 0) {
			outLogLine('setSendAlarm fatal exit,call redis set fails~ setInput:  '.serialize($setInput).', arrOutput: '.serialize($setOutput), 'w',true);
		}
	
	}
    
    
}