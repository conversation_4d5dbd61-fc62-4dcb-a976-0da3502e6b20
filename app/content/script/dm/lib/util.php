<?php

class Lib_Util{
	
	const DM_LIB_UTIL_KEY_PREFIX = 'dm_lib_util_';
	
	/**
	 * 参数校验（只校验存在性，不校验具体值）
	 * @param multitype:$arrInput, key1, key2, ...keyn
	 * @return boolean
	 */
	public static function  _checkParam(){
		$args = func_get_args();
		if(null == $args || empty($args) || null == $args[0] || empty($args[0]) || !is_array($args[0])){
			return false;
		}
		$arrInput = $args[0];
		$count = count($args);
		for ($i = 1; $i < $count; $i++){
			if(!isset($arrInput[$args[$i]])){
				return false;
			}
		}
		return true;
	}
	
	/**
	 * 值输出校验
	 * @param unknown $arrOut
	 * @param string $key
	 * @return boolean
	 */
	public static function _checkOutput($arrOut, $key = 'errno', $judgeValue = Tieba_Errcode::ERR_SUCCESS){
		if(null == $arrOut || empty($arrOut) || !isset($arrOut[$key]) || $judgeValue != $arrOut[$key]){
			return false;
		}
		return true;
	}
	
	
	/**
	 *
	 * @param unknown $tab
	 * @param unknown $subTab
	 * @param unknown $arrRunContext
	 * @param string $bolSaveOrRead：true-save，false-read
	 * @param string $scriptName
	 * @return bool
	 */
	public static function proDmRunContext(
			$keyInput, &$arrRunContext,
			$bolSaveOrRead = true, $expireSeconds = 86400){
		if(empty($keyInput)){
			outLogLine('proRunContext call fail.invalid param,key:['.$keyInput.']');
			return false;
		}
		$retryCount = 3;
		$key = self::DM_LIB_UTIL_KEY_PREFIX.$keyInput;
		$arrInput = array();
		if($bolSaveOrRead){
			//写
			$arrInput = array(
				'function' => 'callGrabRedis',
				'call_method' => 'SET',
				'param' => array(
					'key' => $key,
					'value' => serialize($arrRunContext),
				),
			);
			if(0 < $expireSeconds){
				//$expireSeconds = 0为不设置超时时间
				$arrInput['call_method'] = 'SETEX';
				$arrInput['param']['seconds'] = $expireSeconds;
			}
		}else{
			//读
			$arrInput = array(
				'function' => 'callGrabRedis',
				'call_method' => 'GET',
				'param' => array(
					'key' => $key,
				),
			);
		}
		if(empty($arrInput)){
			outLogLine('invalid call redis param.input:['.serialize($arrInput).']');
			return false;
		}
		$ret = array();
		while($retryCount-->0){
			$arrOut = Tieba_Service::call ( 'common', 'grabResource', $arrInput );
			if(!self::_checkOutput($arrOut)){
				outLogLine('call grabResource callGrabRedis fail.input:['.serialize($arrInput).'] arrOut:['.serialize($arrOut).'] retrycount:['.$retryCount.']');
				sleep(1);
				continue;
			}
			$ret = $arrOut['data'];
			if(!self::_checkOutput($ret, 'err_no')){
				outLogLine('call callGrabRedis fail.input:['.serialize($arrInput).'] ret:['.serialize($ret).'] retrycount:['.$retryCount.']');
				sleep(1);
				continue;
			}
			break;
		}
		if($bolSaveOrRead){
			return true;
		}else{
			if(isset($ret['ret'][$key]) && !empty($ret['ret'][$key])){
				$arrRunContext = unserialize($ret['ret'][$key]);
			}
			return true;
		}
		return false;
	}
	
	/**
	 *
	 * @param unknown $arrPassTidsList
	 * @param number $dayUpdateOrHistory
	 * @return boolean|number
	 */
	public static function saveDataToDB($arrPassTidsList){
		if(empty($arrPassTidsList)){
			Bingo_Log::warning('');
			return false;
		}
		$saveSuccess = 0;
		$saveFail = 0;
		$total = count($arrPassTidsList);
		foreach ($arrPassTidsList as $tid => $arrTidInfo){
			if($tid <= 0){
				continue;
			}
			usleep(50000);
			$arrInput = array(
				'thread_id' => $tid,
				'thread_type' => $arrTidInfo['thread_type'],
				'is_good' => intval($arrTidInfo['is_good']),
				'forum_id' => intval($arrTidInfo['fid']),
				'first_dir' => strval($arrTidInfo['first_dir']),
				'second_dir' => strval($arrTidInfo['second_dir']),
				'title' => strval($arrTidInfo['title']),
				'business_type' => intval($arrTidInfo['business_type']),
				'is_hide_del' => 0,
				'post_num' => 0,
				'publish_time' => intval($arrTidInfo['publish_time']),
			);
			$continueLoop = false;
			do{
				$arrOut = Tieba_Service::call ('content', 'addDmResultRecord', $arrInput, null, null, 'post', 'php', 'utf-8');
				if(!self::_checkOutput($arrOut)){
					if(Tieba_Errcode::ERR_METHOD_NOT_FOUND == $arrOut['errno']){
						$continueLoop = true;
						usleep(100000);
						continue;
					}
					$saveFail++;
					Bingo_Log::warning('call content addDmResultRecord fail.input:['.serialize($arrInput).'] out:['.serialize($arrOut).']');
				}
			}while($continueLoop);
			$saveSuccess++;
		}
		if(0 < $saveSuccess){
			Bingo_Log::warning('dm data total:['.$total.'] save success:['.$saveSuccess.']', 'n', true);
		}
		if(0 < $saveFail){
			Bingo_Log::warning('dm data total:['.$total.'] save fail:['.$saveFail.']', 'n', true);
		}
		return $saveSuccess;
	}
	
	/**
	 * @param $arrPassTidsList：key必须是tid
	 * @param $intSourceId
	 * @param $intImportDate
	 * @return bool
	 */
	public static function saveDataToPuGongYing($arrPassTidsList, $intSourceId, $intImportDate, $importDateAddDayNum = 1){
		if(empty($arrPassTidsList) || $intSourceId <= 0 || $intImportDate<= 0){
			Bingo_Log::warning('saveDataToPuGongYing input param error!!! tids:['.serialize($arrPassTidsList).'] source_id['.$intSourceId.'] ImportDate['.$intImportDate.']');
			return false;
		}
		if(0 < $importDateAddDayNum && is_numeric($importDateAddDayNum)){
			$time = strtotime($intImportDate);
			$intImportDate = date('Ymd', $time+$importDateAddDayNum * 86400);
		}
		$bolAddDb = false;
		$arrTidList = array_keys($arrPassTidsList);
		$arrTidList = array_unique($arrTidList);
		$arrTidsChunks = array_chunk($arrTidList,20);
		foreach ($arrTidsChunks as $arrTidsCk) {
			foreach ($arrTidsCk as $tid){
				$tmp = array(
					'resource_id'=>$tid,
					'quality_score'=>10000,
				);
				if(isset($arrPassTidsList[$tid]) 
						&& isset($arrPassTidsList[$tid]['quality_score']) 
						&& 0 < $arrPassTidsList[$tid]['quality_score']){
					$tmp['quality_score'] = intval($arrPassTidsList[$tid]['quality_score']);
				}
				$arrResources[] = $tmp;
			}
			$arrInput = array(
				'source_id' => $intSourceId,
				'resources' => $arrResources,
				'import_date' => $intImportDate,
			);
			$arrRet = Tieba_Service::call('common', 'contentMAddSourceContent', $arrInput);
			if ($arrRet === false || $arrRet['errno'] != 0) {
				Bingo_Log::warning('call service common::contentMAddSourceContent fail, input:[' . serialize($arrInput) . '],output:[' . serialize($arrRet) . ']');
			}else{
				$bolAddDb = true;
			}
			usleep(10000);
		}
		return $bolAddDb;
	}
	
	/**
	 * 获取文本文件的行数
	 * @param unknown $uidsFilePath
	 * @return number
	 */
	public static function getTextFileRowCount($filePath){
		if(empty($filePath) || !file_exists($filePath) || filesize($filePath) <= 0){
			Bingo_Log::warning('file is not exist or invalid.file:['.$filePath.']');
			return 0;
		}
		$retval = 0;
		$cmd = 'wc -l '.$filePath;
		$out = system($cmd, $retval);
		if(false === $out || empty($out)){
			Bingo_Log::warning('exec cmd:['.$cmd.'] fail.errno:['.$retval.']');
			return 0;
		}
		$arrOut = explode(' ', $out);
		return intval($arrOut[0]);
	}
	
	
	/**
	 * 从文件中按行获取数据
	 * @param unknown $filename：待处理的文件
	 * @param number $startLine：读取的起始位置
	 * @param number $endLine：读取的末尾位置
	 * @param unknown $printLogFunction：日志打印函数。
	 *                              注意：如果为静态类成员函数：array('class_name', 'method_name')，
	 *                              如果为定义的不同函数：method_name，并且函数定义的参数和实现符合内部调用输入输出
	 * @param string $method：只读
	 * @return array|boolean
	 */
	public static function getFileDataByLines(
			$filename,
			$startLine = 1,
			$endLine = 50,
			$method = 'rb') {
		if(empty($filename) || $endLine < $startLine){
			Bingo_Log::warning("_getFileDataByLines call fail.invalid param.filename:[$filename] startLine:[$startLine] endLine:[$endLine] method:[$method]");
			return false;
		}
		if(!file_exists($filename) || filesize($filename)<=0){
			Bingo_Log::warning("invalid input file:[$filename]");
			return false;
		}
		$content = array ();
		$count = $endLine - $startLine;
		if (version_compare(PHP_VERSION, '5.1.0', '>=')){ // 判断php版本（因为要用到SplFileObject，PHP>=5.1.0）
			$fp = new SplFileObject ( $filename, $method );
			$fp->seek ( $startLine - 1 ); // 转到第N行, seek方法参数从0开始计数
			for($i = 0; $i <= $count; ++ $i) {
				$content[] = $fp->current(); // current()获取当前行内容
				$fp->next (); // 下一行
			}
		} else { // PHP<5.1
			$fp = fopen ( $filename, $method );
			if (! $fp){
				Bingo_Log::warning("open file $filename fail.method:$method");
				return false;
			}
			for($i = 1; $i < $startLine; ++ $i) { // 跳过前$startLine行
				fgets ( $fp );
			}
			for($i; $i <= $endLine; ++ $i) {
				$content [] = fgets ( $fp ); // 读取文件行内容
			}
			fclose ( $fp );
		}
		return array_filter($content); // array_filter过滤：false,null,''
	}
	
	
}