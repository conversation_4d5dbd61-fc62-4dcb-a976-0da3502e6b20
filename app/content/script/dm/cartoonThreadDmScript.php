<?php

define ('EASYSCRIPT_DEBUG',false);          //debug 模式
define ('EASYSCRIPT_THROW_EXEPTION',true);  //抛异常模式

define ('ROOT_PATH', dirname ( __FILE__ ) . '/../../../../' );
define ('SCRIPTNAME',basename(__FILE__,".php"));   //定义脚本名
define ('BASEPATH',dirname(__FILE__));
define ('CONFPATH',BASEPATH."/conf");
define ('DATAPATH',BASEPATH."/data");
define ('LOGPATH',"./log/content/script/dm");
define('IS_ORP_RUNTIME', true);
set_include_path(get_include_path() . PATH_SEPARATOR. BASEPATH.'/../../' . PATH_SEPARATOR. BASEPATH. PATH_SEPARATOR. BASEPATH.'/lib/'. PATH_SEPARATOR.BASEPATH.'/../');

//require_once ROOT_PATH . "app/content/script/spider/lib/simple_html_dom.php";
//require_once ROOT_PATH . "app/content/script/spider/lib/util.php";
//require_once ROOT_PATH . "app/content/script/spider/base/SpiderSite.php";
//require_once ROOT_PATH . "app/content/script/spider/douban/ContentPage.php";
//require_once ROOT_PATH . "app/content/script/spider/douban/SiteTab.php";
require_once ROOT_PATH . "app/content/script/dm/base/DmFilterHandler.php";
require_once ROOT_PATH . "app/content/script/dm/lib/util.php";

/**
 *
 * @param：null
 * @return：string
 */
function __autoload($strClassName)
{
    require_once str_replace('_', '/', $strClassName) . '.php';
}
spl_autoload_register('__autoload');


/**
 * 日志行打印
 * @param $strMsg
 * @param bool $bolLineInfo
 * @return null
 */
function outLogLine($strMsg, $type = 'w', $bolEcho = false, $bolLineInfo = true)
{
	$strFileName = '';
	$intLineNo = 0;
	if ($bolLineInfo) {
		if (defined('DEBUG_BACKTRACE_IGNORE_ARGS')) {
			$arrTrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1);
		} else {
			$arrTrace = debug_backtrace();
		}
		if (!empty($arrTrace)) {
			$strFileName = basename($arrTrace[0]['file']);
			$intLineNo = intval($arrTrace[0]['line']);
			//$strMsg = $strFileName . ":Line($intLineNo):" . $strMsg;
		}
	}
	if ('n' == $type) {
		Bingo_Log::notice($strMsg, '', $strFileName, $intLineNo);
	} else if ('w' == $type) {
		Bingo_Log::warning($strMsg, '', $strFileName, $intLineNo);
	} else if ('f' == $type) {
		Bingo_Log::fatal($strMsg, '', $strFileName, $intLineNo);
	} else {
		Bingo_Log::warning($strMsg, '', $strFileName, $intLineNo);
	}
	if ($bolEcho) {
		$strLogPrefix = '';
		if(!empty($strFileName)){
			$strLogPrefix = $strFileName;
		}
		if(0 < $intLineNo){
			$strLogPrefix = $strFileName.'['.$intLineNo.']';
		}
		if(!empty($strLogPrefix)){
			$strMsg = $strLogPrefix.' '.$strMsg;
		}
		echo $strMsg . PHP_EOL;
	}
}

$arrConfList = array(
	'key1' => 'cartoon',
	'key2' => 'thread',
	'subkey' => '',
);
$dmType = 'img_txt_by_pv';
$seconddir = '';
$dayUpdateOrHistory = 1;
$startDate = 0;
$endDate = 0;
$beginOffset = 0;
$maxSize = 0;
if(null != $argv && !empty($argv) && is_array($argv) && 2 <= count($argv)){
    $dayUpdateOrHistory = intval($argv[1]);
    if(isset($argv[2]) && !empty($argv[2])){
    	$dmType = strval($argv[2]);
    }
    if(isset($argv[3]) && !empty($argv[3])){
        $beginOffset = intval($argv[3]);
    }
    if(isset($argv[4]) && !empty($argv[4])){
    	$maxSize = intval($argv[4]);
    }
    if(isset($argv[5]) && !empty($argv[5])){
    	$startDate = strval($argv[5]);
    }
    if(isset($argv[6]) && !empty($argv[6])){
    	$endDate = strval($argv[6]);
    }
    /*if(1 == $dayUpdateOrHistory){
    	$arrConfList['subkey'] = 'day';
    }else if(2 == $dayUpdateOrHistory){
    	$arrConfList['subkey'] = 'history';
    }*/
}else{
    outLogLine('cartoonThreadDmScript  script get invaild input param.argv:['.serialize($argv).']', 'w',ture);
    exit;
}
$strScriptName = 'cartoonThreadDmScript';

/*
$arrSubTab = array();
if(!empty($subTab)){
    if(false !== strpos($subTab, ',')){
        $arrSubTab = explode(',', $subTab);
        $newStr = str_replace(',', '_', $subTab);
        //$strScriptName = $strScriptName.'_'.$newStr;
    }else{
        //$strScriptName = $strScriptName.'_'.$subTab;
        $arrSubTab[] = $subTab;
    }
}

$arrSubTab = array_unique($arrSubTab);
*/


define ('LOG', 'log');
Bingo_Log::init(array(
    LOG => array(
        'file'  => LOGPATH ."/". $strScriptName. ".log",
        'level' => 0x0f,
    ),
), LOG);


$easyScriptObj  = false;
try{
    outLogLine('cartoonThreadDmscript ready run.', 'n', true);
    $ScriptConf = array(
        'memory_limit'   => '1024M',
        'data_path'      => DATAPATH,
        'conf_path'      => CONFPATH,
        'lock_file_name' => $strScriptName.'.lock',
        'done_file_name' => $strScriptName.'.done',
        'db_alias'       => array(
            'im'     => 'forum_content',
        ),
        'conf_alias'     => array(
            'main'     => 'main.conf',
        ),
    );

    $easyScriptObj = new Util_EasyScript($ScriptConf);
    //防止脚本重复执行
    if( $easyScriptObj->checkScriptIsRuning() === true ){
        //outLogLine('cartoonThreadDmscript is runing.', 'n', true);
        //exit;
    }
    outLogLine('cartoonThreadDmscript begin runing.', 'n', true);

    $getObj = new cartoonThreadDmAction();
    $getObj->_execute($arrConfList, $dmType, $dayUpdateOrHistory, $startDate, $endDate, $beginOffset, $maxSize);
    $easyScriptObj->runSuccess();
    outLogLine('cartoonThreadDmscript run success.', 'n', true);
}catch(Exception $e){
    if($easyScriptObj !== false){
        outLogLine('cartoonThreadDmscript run fail!'.$easyScriptObj->getErr2String(), 'w');
    }else{
        outLogLine('cartoonThreadDmscript run fail![null]', 'w');
    }
    outLogLine('cartoonThreadDmscript fail.'.$easyScriptObj->getErr2String());
    exit;
}
outLogLine('cartoonThreadDmscript finish.', 'n', true);


/**
 * 回调函数
 * @param unknown $match
 * @return string
 */
function _matchcallback($match){
	return strlen ( $match [0] ) >= 4 ? '' : $match [0];
}

class cartoonThreadDmAction{
	
	const DB_HOST_PORT = 'palo-yqa.baidu.com:9030';
	const DB_USERNAME = 'ns_tieba_off';
	const DB_PWD = 'ns_tieba_off_123';
	
	const HISTORY_DM_BEGIN_DATE = '20150101';
	const HISTORY_DM_END_DATE = '20180717';
	
	const MAIN_THREAD_IMG_COUNT = 1;
	
	const TIEBA_POST_URL_PREFIX = 'https://tieba.baidu.com/p/';
	
	const TIEBA_PGY_SOURCE_ID = 179;
	
	const TRY_COUNT = 300;
	
	const MIN_TXT_COUNT = 50;
	const TXT_COUNT_JUDGE_POST_NUM = 20;
	
	private static $_PV_ACCEPT_LINE = 0;
	
	private static $_arrForumFirstDir = array(
		'动漫',
	);
	private static $_needFilterFids = array(
		//那年那兔那些事儿、宫斗、演绎、女装子、药娘、腐女动漫、贴吧动漫、真夏夜之银梦、假面骑士、假面骑士zio
		3312930,
		715888,
		552743,
		1775019,
		1893541,
		2524665,
		2965400,
		24852052,
		740580,
		26272768,
	);
	
	//2018-09-03优化策略，去除pv限制
	private static $_arrBusinessType = array(
		//business_type：int型，每8位一个分级类型，
		'img_txt' => 0x00000501,
		'img_txt_by_pv' => 0x00000502,
		//'img_txt_by_title_key' => 0x00000303,
	);
	
	//已经保存的tids数据，用于标题去重
	private static $_arrSavedTidsData = array();
	
	
	/**
	 * 参数校验（只校验存在性，不校验具体值）
	 * @param multitype:$arrInput, key1, key2, ...keyn
	 * @return boolean
	 */
	private static function  _checkParam(){
		$args = func_get_args();
		if(null == $args || empty($args) || null == $args[0] || empty($args[0]) || !is_array($args[0])){
			return false;
		}
		$arrInput = $args[0];
		$count = count($args);
		for ($i = 1; $i < $count; $i++){
			if(!isset($arrInput[$args[$i]])){
				return false;
			}
		}
		return true;
	}
	
	/**
	 * 值输出校验
	 * @param unknown $arrOut
	 * @param string $key
	 * @return boolean
	 */
	private static function _checkOutput($arrOut, $key = 'errno', $judgeValue = Tieba_Errcode::ERR_SUCCESS){
		if(null == $arrOut || empty($arrOut) || !isset($arrOut[$key]) || $judgeValue != $arrOut[$key]){
			return false;
		}
		return true;
	}
    /**
     * @param $siteTabid
     * @param $needGrabPageCnt
     * @param $dayUpdateOrHistory：1-日新增，2-历史（2018.1.1至今）
     * @return bool
     */
    public static function _execute($arrConfList, $dmType, $dayUpdateOrHistory=1, 
    		$startDate = '', $endDate = '', 
    		$beginOffset = 0, $maxSize = 0){
    	if(empty($arrConfList)){
    		outLogLine('config key is invalid.conf:['.serialize($arrConfList).']');
    		return false;
    	}
    	if(empty($dmType)){
    		$dmType = 'img_txt';
    	}
    	if(!isset(self::$_arrBusinessType[$dmType]) || empty(self::$_arrBusinessType[$dmType])){
    		outLogLine('invalid dmtype param:['.$dmType.']', 'n', true);
    		return false;
    	}
    	$runInputKey = 'cartoon_'.self::$_arrBusinessType[$dmType];
    	$day = date('Ymd', strtotime('-1 day'));
    	$arrRunContext = array();
    	Lib_Util::proDmRunContext($runInputKey, $arrRunContext, false);
    	if(isset($arrRunContext['dm_date']) && $day == $arrRunContext['dm_date']){
    		outLogLine('date:['.$day.'] data is dm to DB, not need repeat dm!!!!!!', 'n', true);
    		return true;
    	}
    	$saveSuccess = 0;
    	//按一级目录检索法
    	foreach (self::$_arrForumFirstDir as $firstDir){
    		if(empty($firstDir)){
    			continue;
    		}
    		
    		$sql = '';
    		if(1 == $dayUpdateOrHistory){
    			//前一天的数据
    			$startTime = strtotime($day);
    			$endTime = $startTime+86400;
    		}else if(2 == $dayUpdateOrHistory){
    			//历史数据
    			$startTime = empty($startDate) ? strtotime(self::HISTORY_DM_BEGIN_DATE) : strtotime($startDate);
    			$endTime = empty($endDate) ? strtotime(self::HISTORY_DM_END_DATE) : strtotime($endDate);
    		}
    		if($startTime <= 0 || $endTime <= 0 || $endTime < $startTime){
    			outLogLine('query time range is invalid.stime:['.$startTime.'] etime:['.$endTime.']', 'w', true);
    			continue;
    		}
    		$offset = 0;
    		if(0 < $beginOffset){
    			$offset = $beginOffset;
    		}
    		$resNum = 2500;
    		
    		$itryCount = self::TRY_COUNT;
    		$arrReturn = array();
    		$totalSql = 'select count(tid) from tieba_ods_pb_info where day = '.$day.' and create_time >= '.$startTime.' and create_time < '.$endTime.' and is_thread = 1 and first_dir = \''.$firstDir.'\'';
    		outLogLine('>>get data total sql:['.$totalSql.']', 'n', true);
    		$totalCount = self::_getDataFromDb($totalSql, $arrReturn, 0, true);
    		$loopCount = ceil($totalCount/$resNum);
    		outLogLine('>>>current data row count:['.$totalCount.'] loopcount:['.$loopCount.']', 'n', true);
    		 
    		while ($loopCount-->0 && $itryCount){
    			if(0 < $maxSize && $offset > $maxSize){
    				outLogLine('this script run is finish.maxSize:['.$maxSize.'] offset:['.$offset.']');
    				break;
    			}
    			usleep(10000);
    			$sql = 'select tid, first_dir, second_dir, fid from tieba_ods_pb_info where day = '.$day.' and create_time >= '.$startTime.' and create_time < '.$endTime.' and is_thread = 1 and first_dir = \''.$firstDir.'\' order by tid limit '.$offset.', '.$resNum;
    			outLogLine('>>get data sql:['.$sql.'] loopcount:['.$loopCount.']', 'n', true);
    			$arrTidInfoList = array();
    			$bolRet = self::_getDataFromDb($sql, $arrTidInfoList, $resNum);
    			$offset += $resNum;
    			if(!$bolRet || empty($arrTidInfoList)){
    				$itryCount--;
    				outLogLine('db return fail or from db get data is empty.', 'w', true);
    				continue;
    			}
    			outLogLine('>>>from db get tids count:['.count($arrTidInfoList).']', 'n', true);
    			$itryCount = self::TRY_COUNT;
    			//处理帖子过滤
    			//$arrOriginalTids = array_keys($arrTidInfoList);
    			$arrPassTidsList = array();
    			if(self::_mHandleTidsList($arrConfList, $dmType, $arrTidInfoList, $arrPassTidsList, $day) && !empty($arrPassTidsList)){
    				//保存到文件
    				$saveSuccess += self::_saveDataToDB($arrPassTidsList);
    				self::$_arrSavedTidsData = self::$_arrSavedTidsData + $arrPassTidsList;
    				if(!Lib_Util::saveDataToPuGongYing($arrPassTidsList, self::TIEBA_PGY_SOURCE_ID, $day)){
    					outLogLine('need save data import to pugongying fail.fail data date:['.$day.']');
    				}
    				outLogLine('>>>>>>>>>>>>>save tid success count:['.$saveSuccess.']', 'n', true);
    			}
    		}
    	}
    	outLogLine('>>>>>>>>>>>>>save tid success count:['.$saveSuccess.']', 'n', true);
    	if(0 < $saveSuccess){
    		$arrRunContext['dm_date'] = $day;
    		Lib_Util::proDmRunContext($runInputKey, $arrRunContext);
    	}
    }
    
    
    /**
     * 
     * @param unknown $tid
     * @param unknown $title
     * @param unknown $pv
     * @return bool：true-filter tid，false-not filter
     */
    private static function _checkTidTitleRepeatToNeedFilterOrDelDb($tid, $title, $pv){
    	if(empty(self::$_arrSavedTidsData) || empty($title) || $tid <= 0){
    		return false;
    	}
    	$arrNeedDel = array();
    	foreach (self::$_arrSavedTidsData as $saveTid => $arrSaveTidInfo){
    		if(empty($arrSaveTidInfo['title'])){
    			continue;
    		}
    		if($title == $arrSaveTidInfo['title']){
    			if($pv <= intval($arrSaveTidInfo['quality_score'])){
    				//保留最高的pv数据
    				return true;
    			}else{
    				//删除db里的数据
    				if(0 < $arrSaveTidInfo['business_type']){
    					$arrNeedDel[$arrSaveTidInfo['business_type']][] = $saveTid;
    				}
    			}
    		}
    	}
    	if(!empty($arrNeedDel)){
    		//db删除
    		foreach ($arrNeedDel as $busiType => $arrTids){
    			if($busiType <= 0 || empty($arrTids)){
    				continue;
    			}
    			$arrDelInput = array(
    				'business_type' =>$busiType,
    				'thread_id' => $arrTids,
    				'is_drop' => 1,
    			);
    			$arrOut = Tieba_Service::call ('content', 'delDmResultByCondition', $arrDelInput, null, null, 'post', 'php', 'utf-8');
    			if(!self::_checkOutput($arrOut)){
    				outLogLine('delete title repeat fail.input:['.serialize($arrDelInput).'] out:['.serialize($arrOut).']');
    			}else{
    				outLogLine('delete db data is success.input:['.serialize($arrDelInput).']');
    			}
    		}
    	}
    	return false;
    }
    
    /**
     * 
     * @param unknown $arrPassTidsList
     * @param number $dayUpdateOrHistory
     * @return boolean|number
     */
    private static function _saveDataToDB($arrPassTidsList, $dayUpdateOrHistory = 1){
    	if(empty($arrPassTidsList)){
    		return false;
    	}
    	$saveSuccess = 0;
    	$saveFail = 0;
    	$total = count($arrPassTidsList);
    	foreach ($arrPassTidsList as $tid => $arrTidInfo){
    		if($tid <= 0){
    			continue;
    		}
    		usleep(50000);
    		$arrInput = array(
    			'thread_id' => $tid,
    			'thread_type' => $arrTidInfo['thread_type'],
    			'is_good' => intval($arrTidInfo['is_good']),
    			'forum_id' => intval($arrTidInfo['fid']),
    			'first_dir' => strval($arrTidInfo['first_dir']),
    			'second_dir' => strval($arrTidInfo['second_dir']),
    			'title' => strval($arrTidInfo['title']),
    			'business_type' => intval($arrTidInfo['business_type']),
    			'is_hide_del' => 0,
    			'post_num' => 0,
    			'publish_time' => intval($arrTidInfo['publish_time']),
    		);
    		$continueLoop = false;
    		do{
    			$arrOut = Tieba_Service::call ('content', 'addDmResultRecord', $arrInput, null, null, 'post', 'php', 'utf-8');
    			if(!self::_checkOutput($arrOut)){
    				if(Tieba_Errcode::ERR_METHOD_NOT_FOUND == $arrOut['errno']){
    					$continueLoop = true;
    					usleep(100000);
    					continue;
    				}
    				$saveFail++;
    				outLogLine('call content addDmResultRecord fail.input:['.serialize($arrInput).'] out:['.serialize($arrOut).']');
    			}
    		}while($continueLoop);
    		$saveSuccess++;
    	}
    	if(0 < $saveSuccess){
    		outLogLine('dm data total:['.$total.'] save success:['.$saveSuccess.']', 'n', true);
    	}
    	if(0 < $saveFail){
    		outLogLine('dm data total:['.$total.'] save fail:['.$saveFail.']', 'n', true);
    	}
    	return $saveSuccess;
    }
    
    /**
     * 
     * @param unknown $arrConfList
     * @param unknown $firstDir
     * @param unknown $arrPassTidsList
     * @param number $dayUpdateOrHistory
     * @param string $fileFormat
     * @return boolean
     */
    private static function _saveDataToFile($arrConfList, $firstDir, $arrPassTidsList, $dayUpdateOrHistory = 1, $fileFormat = 'txt'){
    	if(empty($arrPassTidsList)){
    		return false;
    	}
    	if(!is_dir(DATAPATH)){
    		mkdir(DATAPATH);
    	}
    	if('excel' == $fileFormat){
    		$filePath = DATAPATH.'/good_thread_'.$firstDir.'.xlsx';
    	}else if('txt' == $fileFormat){
    		$filePath = DATAPATH.'/good_thread_'.$firstDir.'.txt';
    		if(2 == $dayUpdateOrHistory){
    			$filePath = DATAPATH.'/good_thread_history_'.$firstDir.'.txt';
    			$saveData = '';
    			foreach ($arrPassTidsList as $tid => $arrTidInfo){
    				$tmp = $tid."\t".$arrTidInfo['url']."\t".$arrTidInfo['title']."\r\n";
    				$saveData .= $tmp;
    			}
    			if(false === file_put_contents($filePath, $saveData, FILE_APPEND)){
    				outLogLine('save data to file:['.$filePath.'] file');
    				return false;
    			}
    		}else if(1 == $dayUpdateOrHistory){
    			$filePath = DATAPATH.'/good_thread_day_'.$firstDir.'.txt';
    			$arrTid = array_keys($arrPassTidsList);
    			if(empty($arrTid)){
    				return false;
    			}
    			$strTids = join("\r\n", $arrTid);
    			if(empty($strTids)){
    				return false;
    			}
    			$strTids .= "\r\n";
    			if(false === file_put_contents($filePath, $strTids, FILE_APPEND)){
    				outLogLine('save data to file:['.$filePath.'] file');
    				return false;
    			}
    		}
    		return true;
    	}
    	return false;
    }
    
    /**
     * 
     * @param unknown $arrConfList
     * @param unknown $arrOriginalTids
     * @param unknown $arrPassTidsList
     * @return bool
     */
    private static function _mHandleTidsList($arrConfList, $dmType, $arrTidInfoList, &$arrPassTidsList, $day = 0){
    	if(empty($arrConfList) || empty($arrTidInfoList) || empty($dmType)){
    		return false;
    	}
    	$arrOriginalTids = array_keys($arrTidInfoList);
    	$arrOriginalTids = array_filter($arrOriginalTids);
    	$arrOriginalTids = array_unique($arrOriginalTids);
    	
    	$arrTidsChunks = array_chunk($arrOriginalTids, 50);
    	
    	$tidInDBCount = 0;
    	$tidSaveFailCount = 0;
    	foreach ($arrTidsChunks as $arrChkTids){
    		usleep(10000);
    		//批量获取帖子数据
    		$arrTidsList = $arrChkTids;
    		$arrPvNumByTid = array();
    		if('img_txt_by_pv' == $dmType){
    			$arrPvNumByTid = self::_judgeThreadPvByDay($arrTidsList, $day, self::$_PV_ACCEPT_LINE);
    		}
    		$arrThreadDataByTid = self::_mGetThreadData($arrTidsList);
    		$arrThreadMaskInfoByTid = self::_queryThreadMaskInfo($arrThreadDataByTid);
    		foreach ($arrChkTids as $tid){
    			if($tid <= 0){
    				outLogLine('invalid tid:['.$tid.']');
    				continue;
    			}
    			$fid = intval($arrTidInfoList[$tid]['fid']);
    			if(in_array($fid, self::$_needFilterFids)){
    				outLogLine('----tid:['.$tid.'] hit need filter forumid list.');
    				continue;
    			}
    			if('img_txt_by_pv' == $dmType){
    				if(!isset($arrPvNumByTid[$tid]) || $arrPvNumByTid[$tid] < self::$_PV_ACCEPT_LINE){
    					outLogLine('----tid:['.$tid.'] pv by day filter.');
    					continue;
    				}
    			}
    			if(!isset($arrThreadDataByTid[$tid]) || empty($arrThreadDataByTid[$tid])){
    				outLogLine('----tid:['.$tid.'] thread data is delete or invalid.');
    				continue;
    			}
    			
    			if(isset($arrThreadMaskInfoByTid[$tid]) && self::_judgeThreadIsFilter($arrThreadMaskInfoByTid[$tid])){
    				outLogLine('~~~~~tid:['.$tid.'] thread data is delete or filter or user block or partial visible or hide.maskInfo:['.serialize($arrThreadMaskInfoByTid[$tid]).']');
    				continue;
    			}
    			
    			$title = '';
    			$arrThreadData = $arrThreadDataByTid[$tid];
    			/*if(count($arrThreadData['media']) <= self::MAIN_THREAD_IMG_COUNT){
    				outLogLine('~~~tid:['.$tid.'] thread data less than ['.self::MAIN_THREAD_IMG_COUNT.'] pictures,not import DB.');
    				continue;
    			}*/
    			$is_good = 0;
    			$threadType = 0;
    			$arrThreadType = Tieba_Type_Thread::getTypeArray($arrThreadData['thread_types']);
    			if($arrThreadData['good_types'] > 0
    					&& isset($arrThreadType['is_good'])
    					&& $arrThreadType['is_good']){
    				$is_good = 1;
    			}
    			foreach ($arrThreadType as $key => $value){
    				$hitKey = array_search($key, Tieba_Type_Thread::$uniq_type);
    				if(false !== $hitKey){
    					$threadType = $hitKey;
    					break;
    				}
    			}
    			usleep(10000);
    			if(self::_judgeUserIsBlocked($tid, $arrThreadData)){
    				outLogLine('>>>tid:['.$tid.'] thread user is blocked ,not import DB.');
    				continue;
    			}
    			
    			$title = $arrThreadData['title'];
    			//获取楼层内容数据
    			$arrPostInfo = self::getPostInfoByTid($tid, 0, 20);
    			if(null == $arrPostInfo || empty($arrPostInfo)
    					|| empty($arrPostInfo['post_infos'])){
    				outLogLine('----->>>tid:['.$tid.'] thread user is mask or return data is empty ,not import DB.');
    				continue;
    			}
    			//判断每个楼层是否超过给定的字数
    			/*if(!self::_judgePostTxtCountIsPass($arrPostInfo, self::TXT_COUNT_JUDGE_POST_NUM, self::MIN_TXT_COUNT)){
    				outLogLine('~~~~>tid:['.$tid.'] thread befor ['.self::TXT_COUNT_JUDGE_POST_NUM.'] post no pass min text count ['.self::MIN_TXT_COUNT.']');
    				continue;
    			}*/
    			$arrConfList['subkey'] = $dmType;
    			if((isset($arrThreadType['is_video']) && $arrThreadType['is_video'])
    					|| (isset($arrThreadType['is_movideo']) && $arrThreadType['is_movideo'])){
    				//$arrConfList['subkey'] = 'video';
    				//视频贴暂时不挖掘
    				continue;
    			}
    			$arrMainPostInfo = $arrPostInfo;
    			$arrMainPostInfo['post_infos'] = array(
    				$arrPostInfo['post_infos'][0],
    			);
    			
    			if(self::_checkTidTitleRepeatToNeedFilterOrDelDb($tid, $title, $arrPvNumByTid[$tid])){
    				outLogLine('this tid:['.$tid.'] may be title repeat and pv is less than in db data.');
    				continue;
    			}
    			
    			
    			//判断内容是否满足要求
    			$bolPostNeedFilter = false;
    			$bolPostNeedSave = true;
    			$arrReturnReason = array();
    			$arrOriginalThreadData = array(
    				DM_THREAD_POST_AUTHOR => $arrMainPostInfo,
    				DM_THREAD_POST_REPLY => $arrPostInfo,
    			);
    			Base_DmFilterHandler::filterHandle($arrConfList, $arrOriginalThreadData, $bolPostNeedFilter, $bolPostNeedSave, $arrReturnReason);
    			/*if('video' != $arrConfList['subkey'] && ($bolPostNeedFilter || !$bolPostNeedSave)){
    				//换成图集再试一次
    				$arrConfList['subkey'] = 'photos';
    				Base_DmFilterHandler::filterHandle($arrConfList, $arrPostInfo, $bolPostNeedFilter, $bolPostNeedSave);
    			}*/
    			if(!$bolPostNeedFilter || $bolPostNeedSave){
    				//保存满足要求的数据
    				$arrPassTidsList[$tid] = array(
    					'title' => $title,
    					'tid' => $tid,
    					'thread_type' => $threadType,
    					'is_good' => $is_good,
    					'fid' => $arrThreadData['forum_id'],
    					'first_dir' => $arrTidInfoList[$tid]['first_dir'],
    					'second_dir' => $arrTidInfoList[$tid]['second_dir'],
    					'url' => self::TIEBA_POST_URL_PREFIX.$tid,
    					'business_type' => self::$_arrBusinessType[$arrConfList['subkey']],
    					'publish_time' => $arrThreadData['create_time'],
    					'quality_score' => intval($arrPvNumByTid[$tid]),
    				);
    			}else{
    				outLogLine('-----~~~~~~~~~~~~>>>tid:['.$tid.'] thread filter by rules.reason:['.serialize($arrReturnReason).']');
    			}
    		}
    	}
    	return true;
    }
    
    /**
     * 
     * @param unknown $arrPostInfo
     * @param number $postNum
     * @param number $txtCount
     * @return bool
     */
    private static function _judgePostTxtCountIsPass($arrPostInfos, $postNum = 20, $minTxtCount = 50){
    	if(empty($arrPostInfos)){
    		Bingo_Log::warning('_judgePostTxtCountIsPass fail, postinfo is empty.info:['.serialize($arrPostInfos).']');
    		return false;
    	}
    	$bolIsPass = false;
    	foreach ($arrPostInfos['post_infos'] as $arrPostItem){
    		if($postNum <= 0){
    			break;
    		}
    		$postNum--;
    		if(!isset($arrPostItem['content']) || empty($arrPostItem['content'])){
    			continue;
    		}
    		$txtContent = '';
    		foreach ($arrPostItem['content'] as $arrContentItem){
    			if('plainText' == $arrContentItem['tag']){
    				$txtContent .= trim($arrContentItem['value']);
    			}
    		}
    		$txtCount = self::_utf8_strlen($txtContent);
    		if($txtCount > $minTxtCount){
    			$bolIsPass = true;
    			break;
    		}
    	}
    	return $bolIsPass;
    }
    
    /**
     * 
     * @param unknown $arrThreadMaskInfo
     * @return bool
     */
    private static function _judgeThreadIsFilter($arrThreadMaskInfo){
    	if(empty($arrThreadMaskInfo) || !isset($arrThreadMaskInfo['thread_id']) || $arrThreadMaskInfo['thread_id'] <= 0){
    		return false;
    	}
    	if(1 == $arrThreadMaskInfo['is_deleted'] || 0 == $arrThreadMaskInfo['is_exist']
    			|| 1 == $arrThreadMaskInfo['is_user_filtered'] || 1 == $arrThreadMaskInfo['is_user_blocked']
    			|| 1 == $arrThreadMaskInfo['is_partial_visible']){
    		return true;
    	}
    	return false;
    }
    
    /**
     * 
     * @param unknown $arrTidsList
     * @param number $pvLine
     * @return array()
     */
    private static function _judgeThreadPvByDay($arrTidsList, $day = 0, $pvLine = 1000){
    	if(empty($arrTidsList)){
    		return array();
    	}
    	if(0 == $day){
    		$day = date('Ymd', strtotime('-1 day'));
    	}
    	$strTidsList = implode(',', $arrTidsList);
    	$sql = "select tid, pv from tieba_mds_bhv_thread_day where day = $day and tid in ($strTidsList) and client_type = 'mobile_app_na'";
    	$arrKeys = array(
    		0 => 'tid',
    		1 => 'pv',
    	);
    	$arrRetData = self::_queryDataFromDb($sql, $arrKeys);
    	if(null == $arrRetData || empty($arrRetData)){
    		return array();
    	}
    	$arrPvResult = array();
    	foreach ($arrRetData as $arrItem){
    		$tid = intval($arrItem['tid']);
    		$pv = intval($arrItem['pv']);
    		if($tid <= 0 || $pv < $pvLine){
    			continue;
    		}
    		$arrPvResult[$tid] = $pv;
    	}
    	return $arrPvResult;
    }
    
    /**
     * 
     * @param unknown $arrThreadDataByTid
     * @return array
     */
    private static function _queryThreadMaskInfo($arrThreadDataByTid){
    	$arrTidsByFid = array();
    	foreach ($arrThreadDataByTid as $tid => $arrThreadData){
    		if(isset($arrThreadData['is_deleted']) && 1== $arrThreadData['is_deleted']){
    			continue;
    		}
    		$fid = intval($arrThreadData['forum_id']);
    		$arrTidsByFid[$fid][] = $tid;
    	}
    	$arrRetMaskInfoByTid = array();
    	foreach ($arrTidsByFid as $fid => $arrTids){
    		usleep(10000);
    		$input = array(
    			"forum_id" => $fid, //吧id
    			"thread_ids" => $arrTids,
    		);
    		$arrOut = Tieba_Service::call('post', 'getThreadMaskInfo', $input, null, null, 'post', 'php', 'utf-8');
    		if(!self::_checkOutput($arrOut)){
    			outLogLine('call post getThreadMaskInfo fail.input:['.serialize($input).'] out:['.serialize($arrOut).']');
    			continue;
    		}
    		foreach ($arrOut['output']['thread_info'] as $arrItem){
    			if(!isset($arrItem['thread_id']) || $arrItem['thread_id'] <= 0){
    				continue;
    			}
    			$tid = intval($arrItem['thread_id']);
    			$arrRetMaskInfoByTid[$tid] = $arrItem;
    		}
    	}
    	return $arrRetMaskInfoByTid;
    }
    
    /**
     * 
     * @param unknown $tid
     * @param unknown $arrThreadData
     * @return boolean
     */
    private static function _judgeUserIsBlocked($tid, $arrThreadData){
    	//判断用户是否封禁
    	$arrInput = array(
    		'req'=>array(
    			'user_id'=>$arrThreadData['user_id'],
    			'forum_id'=>0,
    		));
    	$arrOut = Tieba_Service::call('anti', 'antiUserBlockQuery', $arrInput, null, null, 'post', 'php', 'utf-8');
    	if ($arrOut['errno']==220012 || $arrOut['errno']==220034){
    		return true;
    	}
    	return false;
    }
    
    /**
     * @param
     * @return boolean
     */
    private static function _getDataFromDb($query_sql, &$arrTidInfoList, $retNum = 5000, $bolIsCount = false){
    	//导入数据组优质数据
    	$conn = mysql_connect(self::DB_HOST_PORT, self::DB_USERNAME, self::DB_PWD);
    	if(null == $conn || !mysql_select_db('tiebadata')){
    		outLogLine('connect db or select db fail.');
    		return false;
    	}
    	//$import_date=date('Ymd',time()-86400*1);
    	//$strDate = date('Ymd', strtotime('-1 day'));
//     	$max_sql = sprintf($max_sql,$import_date);
//     	$result = mysql_unbuffered_query($query_sql, $conn);
//     	$row = mysql_fetch_row($result);
//     	$totalCount = $row[0];
//     	$intMinScore = $row[1];
//     	if ($intMinScore >= $intMaxScore){
//     		$intMaxScore = 1;
//     		$intMinScore = 0;
//     	}

    	if($bolIsCount){
    		$result = mysql_unbuffered_query($query_sql, $conn);
    		$row = mysql_fetch_row($result);
    		$totalCount = $row[0];
    		mysql_close($conn);
    		return $totalCount;
    	}
    	
    	$has_more=1;
    	$intRn=500;
    	$totalCountOnce = 0;
    	do{
    		$sql = sprintf($query_sql,$intRn);
    		$result = mysql_unbuffered_query($sql, $conn);
    		$row = mysql_fetch_row($result);
    		$row_count = 0;
    		$arrResources = array();
    		while($row){
    			$tid = intval($row[0]);
    			//TODO暂存表数据
    			$arrTidInfoList[$tid] = array(
    				'first_dir' => strval($row[1]),
    				'second_dir' => strval($row[2]),
    				'fid' => intval($row[3]),
    			);
    			$row_count++;
    			$totalCountOnce++;
    			$row = mysql_fetch_row($result);
    		}
    		if ($row_count < $intRn){
    			$has_more = 0;
    		}
    		if($totalCountOnce >= $retNum){
    			break;
    		}
    	}while($has_more);
    	mysql_close($conn);
    	return true;
    }
    
    /**
     * @param
     * @return boolean
     */
    private static function _queryDataFromDb($query_sql, $arrKeys){
    	if(empty($query_sql) || empty($arrKeys)){
    		return null;
    	}
    	//导入数据组优质数据
    	$conn = mysql_connect(self::DB_HOST_PORT, self::DB_USERNAME, self::DB_PWD);
    	if(null == $conn || !mysql_select_db('tiebadata')){
    		outLogLine('connect db or select db fail.');
    		return false;
    	}
    	$result = mysql_unbuffered_query($query_sql, $conn);
    	$row = mysql_fetch_row($result);
    	$row_count = 0;
    	$arrRetData = array();
    	while($row){
    		$arrItem = array();
    		foreach ($arrKeys as $index => $keyValue){
    			$arrItem[$keyValue] = $row[$index];
    		}
    		if(!empty($row)){
    			$arrRetData[] = $arrItem;
    		}
    		$row = mysql_fetch_row($result);
    	}
    	mysql_close($conn);
    	return $arrRetData;
    	}
    
    /**
     * 
     * @param unknown $arrTidsList
     * @return array
     */
    private static function _mGetThreadData($arrTidsList){
    	if(empty($arrTidsList)){
    		outLogLine('_mGetThreadData invalid input param,tids:['.serialize($arrTidsList).']');
    		return null;
    	}
    	$arrInput = array(
    		"thread_ids" => $arrTidsList,
    		"need_abstract" => 1,
    		"forum_id" => 0,
    		"need_photo_pic" => 1,
    		"need_user_data" => 0,
    		"icon_size" => 0,
    		"need_forum_name" => 0, //是否获取吧名
    		"call_from" => "client_frs", //上游模块名
    	);
    	$arrOutput   = Tieba_Service::call('post', 'mgetThread', $arrInput, null, null, 'post', 'php', 'utf-8');
    	if (!self::_checkOutput($arrOutput)) {
    		outLogLine("call post mgetThread fail; [input]".serialize($arrInput) . ";[output]".serialize($arrOutput));
    		return null;
    	}
    	$arrRetData = array();
    	$arrThreadList = $arrOutput['output']['thread_list'];
    	foreach ($arrThreadList as $tid => $arrThread){
    		if(1 == $arrThread['is_deleted']){
    			continue;
    		}
    		$arrRetData[$tid] = $arrThread;
    	}
    	return $arrRetData;
    }
    
    /**
     * 获取帖子信息
     * @param unknown $arrInputData
     * @param unknown $serviceName
     * @return NULL|unknown
     */
    private static function getPostInfoByTid($tid, $offset = 0, $resNum = 1, $seeAuthor = 0, $serviceName = 'getPostsByThreadId'){
    	if($tid <= 0){
    		return null;
    	}
    	$arrInput = array (
    		'thread_id' => $tid,
    		'res_num' => $resNum,
    		'offset' => $offset,
    		'see_author' => $seeAuthor,
    		'has_comment' => 0,
    		'has_mask' => 0,
    		'has_ext' => 1,
    		'structured_content' => 1,
    	);
    	$arrOutput = Tieba_Service::call ( 'post', $serviceName, $arrInput, null, null, 'post', 'php', 'utf-8');
    	if (!self::_checkOutput($arrOutput)) {
    		outLogLine( sprintf ( 'service error: %s_%s [%s] [%s]', 'post', $serviceName, serialize ( $arrInput ), serialize ( $arrOutput ) ) );
    		return null;
    	}
    	$arrList = $arrOutput['output']['output'][0];
    	if($arrList['is_thread_deleted'] ==1 || $arrList['user_mask'] ==1
    			|| $arrList['is_thread_mask'] ==1 || $arrList['is_thread_exceed'] ==1){
    		return null;
    	}
    	return $arrList;
    }
    
    /**
     *
     * @param unknown $text
     * @return Ambigous <string, unknown>|unknown
     */
    private static function _filterEmoji($text){
    	$text = preg_replace_callback ( '/./u', "_matchcallback", $text );
    	return $text;
    }
    
    /**
     * @param null $string
     * @return int
     */
    private static function _utf8_strlen($string = null) {
    	// 将字符串分解为单元
    	preg_match_all("/./us", $string, $match);
    	// 返回单元个数
    	return count($match[0]);
    }
    
    
}
