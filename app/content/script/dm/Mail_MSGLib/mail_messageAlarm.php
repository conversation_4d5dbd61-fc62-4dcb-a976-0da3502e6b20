<?php
require_once('message.php');
require_once('MailAlarm.php');

class MailMeaasge {
	/**
	 * [getInput description]
	 * @param  [type] $arrInput [description]
	 * @return [type]           [description]
	 */
	public function getInput($arrInput){
		if(0 === $arrInput['sendType']){
            //发送邮件
			$sendTo = $arrInput['sendTo'];//'<EMAIL>,<EMAIL>';
			$arrSendTo = explode(',',$sendTo);
			$cc = '<EMAIL>,...@baidu.com';
			$arrCc = explode(',',$cc);
			$from = 'tb_autotest';
			$passwd = 'Tb#ForAuto1234';
			$subject = '报警主题'.date('Ymd');
			$body = $arrInput['body'];//"报警内容，把钟翅给的内容重新组织一下即可";

			$obj = new MailAlarm($from,$passwd);
			$obj->setSendTo($arrSendTo);
		    $obj->setCc($arrCc);
		    $obj->setSubject($subject);
			$obj->setBody($body);
			$result = $obj->send();
			return $result;
			
		}
		else if(1 === $arrInput['sendType']){
            //发送短信
			$phoneArr = $arrInput['phone'];
			$message = $arrInput['message'];
			
			$sendmeg = new Lib_SendSmsp();
			$sendmeg->setTo($phoneArr);
			$sendmeg->setMes($message);
			$ret = $sendmeg->send();
			$result = array();
			$result['errno'] = 0;
			$result['errMsg'] = 'seccuss';
		    if(!$ret){
			   $result = array(
			              'errno' => -1,
				          'errMsg' => 'fail',
					      );
			  }
	     	return $result;
		}
	}
	
}	
?>
