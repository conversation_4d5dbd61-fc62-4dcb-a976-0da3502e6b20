<?php namespace Topic\Application\Service;


use Nt\Commander\CommanderTrait;
use Topic\Domain\Command\GetTopicCollectCommand;


class GetTopicFollowService implements ApplicationService {

    use CommanderTrait;
    /**
     * @param $request
     * @return array
     */
    public function execute( $request = null )
    {
        $follow = $this->commandExecute( GetTopicCollectCommand::class, $request);
        $total = $follow['total'];
        $pn = $follow['pn'];
        $rn = $follow['rn'];


        $user_ids = $follow['user_ids'];
        $has_more = (int)($pn * $rn < $total);

        return [
            'user_ids' => $user_ids,
            'page' => [
                'current_pn' => $pn,
                'total' => $total,
                'rn' => $rn,
                'has_more' => $has_more,
            ]
        ];
    }
}