<?php namespace Topic\Application\Service;

use Nt\Commander\CommanderTrait;
//use Topic\Domain\Command\CollectTopicCommand;
use Topic\Domain\Command\CollectTopicCommand;
use Topic\Domain\Command\DealTopicAttrCommand;
use Topic\Domain\Model\Topic\Attribute;

//use Topic\Domain\Command\DealTopicAttrCommand;
//use Topic\Domain\Model\Topic\Attribute;

class PushTopicService implements ApplicationService{

    use CommanderTrait;
    /**
     * @param $request
     * @return mixed
     */
    public function execute( $request = null )
    {
        $request['action'] = 'add';
        $request['value'] = 1;
        $request['attr'] = Attribute::PUSH_NUM;
        $data = $this->commandExecute( DealTopicAttrCommand::class, $request);
//        $this->commandExecute(CollectTopicCommand::class,$request);
        return $data;
    }
}