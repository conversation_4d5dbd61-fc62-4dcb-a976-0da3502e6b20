<?php namespace Topic\Application\Service;

use Nt\Assert\Assert;
use Topic\Domain\Model\Topic\TopicRepository;
use Topic\Domain\Service\TagService;

class GetTopicInfoService implements ApplicationService {


    /**
     * @var TopicRepository
     */
    protected $topicRepository;

    /**
     * @var TagService
     */
    protected $tagService;

    /**
     * MGetTopicDetailService constructor.
     *
     * @param TopicRepository $topicRepository
     * @param TagService      $tagService
     */
    public function __construct( TopicRepository $topicRepository, TagService $tagService )
    {
        $this->topicRepository = $topicRepository;
        $this->tagService      = $tagService;
    }


    /**
     * @param $request
     * @return array
     */
    public function execute( $request = null )
    {
        $topicId = $request['topic_id'];
        Assert::greaterThan($topicId,0);
        $topic_ids = [$topicId];
        $topicInfo =  (new MgetTopicInfoService($this->topicRepository,$this->tagService))->execute(compact('topic_ids'));
        return $topicInfo;
    }
}