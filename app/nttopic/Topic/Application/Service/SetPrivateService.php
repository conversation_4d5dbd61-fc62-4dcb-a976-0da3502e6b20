<?php namespace Topic\Application\Service;

use Nt\Commander\CommanderTrait;
use Topic\Domain\Command\BlockTopicCommand;
use Topic\Domain\Command\SetPrivateCommand;

//use Topic\Domain\Model\Topic\TopicRepository;
//use Topic\Domain\Service\TagService;

class SetPrivateService implements ApplicationService {

    use CommanderTrait;

    /**
     * @param $request
     * @return array
     */
    public function execute( $request = null )
    {
        //TODO: form validator

        // execute command
        $topic = $this->commandExecute( SetPrivateCommand::class, $request );

        return $topic->toArray();
    }
}