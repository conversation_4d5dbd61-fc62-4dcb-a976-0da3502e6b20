<?php namespace Topic\Application\Service;

use Nt\Assert\Assert;
use Nt\Commander\Events\DispatchableTrait;
use Topic\Domain\Model\Topic\TopicRepository;

//use Topic\Domain\Model\Topic\TopicRepository;
//use Topic\Domain\Service\TagService;

class PassTopicService implements ApplicationService {
    use DispatchableTrait;

    /**
     * @var \Topic\Domain\Model\Topic\TopicRepository
     */
    private $topicRepository;

    /**
     * RecommendTopicService constructor.
     *
     * @param \Topic\Domain\Model\Topic\TopicRepository $topicRepository
     */
    public function __construct(TopicRepository $topicRepository)
    {
        $this->topicRepository = $topicRepository;
    }

    /**
     * @param $request
     * @return mixed
     */
    public function execute( $request = null )
    {
        $topic_id = intval($request['topic_id']);
        Assert::greaterThan($topic_id,0);
        // validate topic id
        $topic = $this->topicRepository->ofTopicIdOrFail($topic_id);
        $user_id = $topic->getUserId();
        if($topic->isNormal()) {
            // do nothing
            return compact('user_id');
        }

        $topic->doPass();

        $this->topicRepository->save($topic);

        $this->dispatchEventsFor($topic);
        return compact('user_id');
    }
}