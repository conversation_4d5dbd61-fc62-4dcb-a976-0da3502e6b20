<?php namespace Topic\Application\Service;

use Nt\Commander\CommanderTrait;
use Topic\Domain\Command\AddTopicCommand;
use Topic\Domain\Command\AddTopicReplyCommand;
use Topic\Domain\Model\Topic\Topic;

class AddTopicReplyService implements ApplicationService {

    use CommanderTrait;

    /**
     * @param $request
     * @return array
     */
    public function execute( $request = null )
    {
        //TODO: form validator

        // execute command

        $reply = $this->commandExecute( AddTopicReplyCommand::class, $request );

        return [];
    }
}