<?php namespace Topic\Application\Service;

use Nt\Commander\CommanderTrait;
use Topic\Domain\Command\FollowTopicCommand;

class FollowTopicService implements ApplicationService{

    use CommanderTrait;
    /**
     * @param $request
     * @return mixed
     */
    public function execute( $request = null )
    {
        $this->commandExecute(FollowTopicCommand::class,$request);
        return [];
    }
}