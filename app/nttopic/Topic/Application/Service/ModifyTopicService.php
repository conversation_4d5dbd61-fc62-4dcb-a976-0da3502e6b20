<?php namespace Topic\Application\Service;

use Nt\Commander\CommanderTrait;
use Topic\Domain\Command\AddTopicCommand;
use Topic\Domain\Command\ModifyTopicCommand;
use Topic\Domain\Command\RecoverTopicCommand;
use Topic\Domain\Model\Topic\Topic;
//use Topic\Domain\Model\Topic\TopicRepository;
//use Topic\Domain\Service\TagService;

class ModifyTopicService implements ApplicationService {

    use CommanderTrait;

    /**
     * @param $request
     * @return array
     */
    public function execute( $request = null )
    {

       $this->commandExecute(ModifyTopicCommand::class,$request);
        return [];
    }
}