<?php namespace Topic\Application\Service;

use Nt\Assert\Assert;
use Topic\Domain\Model\Topic\TopicRepository;
use Topic\Domain\Model\Topic\TopicStatus;
use Topic\Domain\Service\TagService;

class MgetTopicInfoService implements ApplicationService {


    /**
     * @var TopicRepository
     */
    protected $topicRepository;

    /**
     * @var TagService
     */
    protected $tagService;

    /**
     * MGetTopicDetailService constructor.
     *
     * @param TopicRepository $topicRepository
     * @param TagService      $tagService
     */
    public function __construct( TopicRepository $topicRepository, TagService $tagService )
    {
        $this->topicRepository = $topicRepository;
        $this->tagService      = $tagService;
    }


    /**
     * @param $request
     * @return array
     */
    public function execute( $request = null )
    {
        $topicIds = $request['topic_ids'];
        Assert::isArray($topicIds);

        $topics = $this->topicRepository->ofTopicIds($topicIds);

        $tagIds = $this->getTagIds($topics);

        $tags = $this->tagService->ofIds($tagIds);

        $this->assignTagName($topics,$tags);
        $ret = [];
        foreach ($topics as $topic) {
            $ret[$topic['topic_id']] = $topic;
        }
        return $ret;
    }

    /**
     * @param $topics
     * @return array
     */
    private function getTagIds( $topics )
    {
        $tagIds = [];
        foreach ($topics as $topic) {
            $tagIds = array_merge($tagIds,(array)$topic['tags']);
        }

        return array_keys(array_count_values($tagIds));
    }

    /**
     * @param $topics
     * @param $tags
     */
    private function assignTagName( &$topics, $tags )
    {
        foreach ($topics as $index => $topic) {
            $tagIds = (array)$topic['tags'];
            $tagNames = [];
            foreach ($tagIds as $tagId) {
                if(array_key_exists($tagId,$tags)) {
                    $tagNames[] = [
                        'tag_id' => $tagId,
                        'tag_name' => $tags[$tagId]['tag_name'],
                    ];
                }
            }
            if(count($tagIds) !== count($tagNames)){
                $topics[$index]['status'] = TopicStatus::BLOCKED;
            }
            $topics[$index]['tags'] = $tagNames;
        }
    }
}