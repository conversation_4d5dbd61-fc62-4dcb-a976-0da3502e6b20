<?php namespace Topic\Application\Service;



use Nt\Commander\CommanderTrait;
use Topic\Domain\Command\GetTopicCollectCommand;


class GetTopicCollectIdsService implements ApplicationService {
    use CommanderTrait;

    /**
     * @param $request
     * @return array
     * @throws \Topic\Domain\Exception\InvalidTopicId
     */
    public function execute( $request = null )
    {
        $request['topic_id'] =  $request['id'];
        $request['order'] = 1;
        $follow = $this->commandExecute( GetTopicCollectCommand::class, $request);

        $user_ids = $follow['user_ids'];

        return [
            'ids' => $user_ids,
        ];
    }
}