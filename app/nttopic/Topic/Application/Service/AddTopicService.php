<?php namespace Topic\Application\Service;

use Nt\Commander\CommanderTrait;
use Topic\Domain\Command\AddTopicCommand;
use Topic\Domain\Model\Topic\Attribute;
use Topic\Domain\Model\Topic\AttrRepository;
use Topic\Domain\Model\Topic\Topic;
//use Topic\Domain\Model\Topic\TopicRepository;
//use Topic\Domain\Service\TagService;

class AddTopicService implements ApplicationService {

    use CommanderTrait;
    /**
     * @var \Topic\Domain\Model\Topic\AttrRepository
     */
    private $attrRepository;

    /**
     * AddTopicService constructor.
     *
     * @param \Topic\Domain\Model\Topic\AttrRepository $attrRepository
     */
    public function __construct(AttrRepository $attrRepository)
    {
        $this->attrRepository = $attrRepository;
    }

    /**
     * @param $request
     * @return array
     */
    public function execute( $request = null )
    {
        //TODO: form validator
//        \Bingo_Log::warning(var_export($request,1));
        // execute command
        /**
         * @var Topic $topic
         */
        $topic = $this->commandExecute( AddTopicCommand::class, $request );

        $topic_id = $topic->getTopicId()->id();

        if (isset($request['attr'])) {
            $attr = $request['attr'];
            $key = $attr['key'];
            $value = $attr['value'];
            $attr = new Attribute($topic_id,$this->attrRepository);
            if (!$attr->setAttribute($key, $value)){
                \Bingo_Log::warning("set topic:$topic_id attr:$attr,value:$value");
            }
        }

        return [
            'topic_id' => $topic->getTopicId()->id(),
        ];
    }
}