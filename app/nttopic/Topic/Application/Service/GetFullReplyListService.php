<?php namespace Topic\Application\Service;

use Nt\Assert\Assert;
use Nt\Commander\CommanderTrait;
use Topic\Domain\Model\Reply\ReplyRepository;

class GetFullReplyListService implements ApplicationService {

    use CommanderTrait;

    /** @var  ReplyRepository */
    protected $repository;

    /**
     * GetTopicReplyListService constructor.
     *
     * @param \Topic\Domain\Model\Reply\ReplyRepository $repository
     */
    public function __construct( ReplyRepository $repository )
    {
        $this->repository = $repository;
    }

    /**
     * @param $request
     * @return array
     */
    public function execute( $request = null )
    {

        // 返回topicInfo和列表
        $topicId = $request['topic_id'];
        Assert::greaterThan($topicId,0);

        $list = $this->repository->getReplyList($topicId);

        return (array)$list;
    }
}





