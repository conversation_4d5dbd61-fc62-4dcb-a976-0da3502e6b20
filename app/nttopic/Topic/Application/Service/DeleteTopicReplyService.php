<?php namespace Topic\Application\Service;

use Nt\Commander\CommanderTrait;
use Topic\Domain\Command\AddTopicCommand;
use Topic\Domain\Command\AddTopicReplyCommand;
use Topic\Domain\Command\DeleteTopicReplyCommand;
use Topic\Domain\Model\Topic\Topic;

class DeleteTopicReplyService implements ApplicationService {

    use CommanderTrait;

    /**
     * @param $request
     * @return array
     */
    public function execute( $request = null )
    {
        //TODO: form validator

        // execute command

        $reply = $this->commandExecute( DeleteTopicReplyCommand::class, $request );

        return [];
    }
}