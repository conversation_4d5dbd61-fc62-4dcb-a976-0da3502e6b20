<?php namespace Topic\Application\Service;

use Nt\Commander\CommanderTrait;
use Topic\Domain\Command\ListTopicCommand;
use Topic\Domain\Model\Topic\Topic;
//use Topic\Domain\Model\Topic\TopicRepository;
//use Topic\Domain\Service\TagService;

class ListTopicService implements ApplicationService {

    use CommanderTrait;

    /**
     * @param $request
     * @return array
     */
    public function execute( $request = null )
    {
        //TODO: form validator

        // execute command

        $info = $this->commandExecute( ListTopicCommand::class, $request );

        $info =  [
            'list' => $info['list'],
            'page' => [
                'current_pn' => $info['pn'],
                'total' => $info['total'],
                'rn' => $info['rn'],
            ]
        ];

        return $info;

    }
}