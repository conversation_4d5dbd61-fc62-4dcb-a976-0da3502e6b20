<?php namespace Topic\Application\Service;

use Nt\Assert\Assert;
use Nt\Commander\CommanderTrait;
//use Topic\Domain\Command\CollectTopicCommand;
use Topic\Domain\Command\CollectTopicCommand;
use Topic\Domain\Command\DealTopicAttrCommand;
use Topic\Domain\Model\Topic\Attribute;
use Topic\Domain\Model\Topic\TopicRepository;

//use Topic\Domain\Command\DealTopicAttrCommand;
//use Topic\Domain\Model\Topic\Attribute;

class PutCoverService implements ApplicationService{
    /**
     * @var \Topic\Domain\Model\Topic\TopicRepository
     */
    private $topicRepository;

    /**
     * PutCoverService constructor.
     *
     * @param \Topic\Domain\Model\Topic\TopicRepository $topicRepository
     */
    public function __construct( TopicRepository $topicRepository )
    {
        $this->topicRepository = $topicRepository;
    }

    /**
     * @param $request
     * @return mixed
     */
    public function execute( $request = null )
    {
        $topic_id = intval($request['topic_id']);
        Assert::greaterThan($topic_id,0);
        // validate topic id
        $topic = $this->topicRepository->ofTopicIdOrFail($topic_id);
        $cover = $request['cover'];
        $topic->putCover($cover);
        $this->topicRepository->save($topic);
//        $this->dispatchEventsFor($topic);
        return $topic->toArray();
    }
}