<?php namespace Topic\Application\Service;

use Nt\Commander\CommanderTrait;
use Topic\Domain\Command\BlockTopicCommand;
use Topic\Domain\Command\DeleteTopicCommand;

//use Topic\Domain\Model\Topic\TopicRepository;
//use Topic\Domain\Service\TagService;

class DeleteTopicService implements ApplicationService {

    use CommanderTrait;

    /**
     * @param $request
     * @return array
     */
    public function execute( $request = null )
    {
        //TODO: form validator

        // execute command
        $this->commandExecute( DeleteTopicCommand::class, $request );

        return [ ];
    }
}