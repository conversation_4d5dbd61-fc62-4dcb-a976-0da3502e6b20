<?php namespace Topic\Application\Service;

use Nt\Assert\Assert;
use Nt\Commander\CommanderTrait;
use Topic\Domain\Command\DealTopicAttrCommand;
use Topic\Domain\Command\GetTopicFromReplyIdCommand;
use Topic\Domain\Model\Reply\ReplyRepository;
use Topic\Domain\Model\Topic\Attribute;

class GetTopicReplyListService implements ApplicationService {

    use CommanderTrait;

    /** @var  ReplyRepository */
    protected $repository;

    /**
     * GetTopicReplyListService constructor.
     *
     * @param \Topic\Domain\Model\Reply\ReplyRepository $repository
     */
    public function __construct( ReplyRepository $repository )
    {
        $this->repository = $repository;
    }

    /**
     * @param $request
     * @return array
     */
    public function execute( $request = null )
    {

        // 返回topicInfo和列表
        $topicId = $request['topic_id'];
        $topic_ids = [$topicId];
        Assert::greaterThan($topicId,0);
        // 增加pv
        if (intval($request['is_pv']) == 1){
            $input['topic_id'] = $topicId;
            $input['action'] = 'add';
            $input['value'] = 1;
            $input['attr'] = Attribute::VIEW_NUM;
            $this->commandExecute( DealTopicAttrCommand::class, $input);
        }
        // 需要第一楼和最后一楼的数据
        $maxReplyId = $this->repository->maxReplyId($topicId);
        $minReplyId = $this->repository->minReplyId($topicId);

        $topicInfo =  app()->make(MgetTopicExtInfoService::class)->execute(compact('topic_ids'));
//        $topicInfo =  app()->make(MgetTopicInfoService::class)->execute(compact('topic_ids'));

//        $request['order'] = 1;
        $order = isset($request['order']) ? intval($request['order']) : 1;
        $replies = $this->commandExecute(GetTopicFromReplyIdCommand::class,$request);

        $list = $replies['list'];
        $list = array_keys($list);
        if($order === 0){
            // 降序
            $list = array_reverse($list);
        }

        if ($minReplyId>0 && $minReplyId!=$list[0]){
            array_unshift($list,$minReplyId);
        }
        if ($maxReplyId>0 && $maxReplyId!=$list[count($list)-1]){
            array_push($list,$maxReplyId);
        }

        $list = array_values(array_unique($list));
        $info =  [
            'topic_info' => (array)$topicInfo[$topicId],
            'list' => $list,
        ];

        return $info;
    }
}





