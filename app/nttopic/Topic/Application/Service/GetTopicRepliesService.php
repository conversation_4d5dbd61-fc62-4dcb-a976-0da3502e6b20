<?php namespace Topic\Application\Service;

use Nt\Assert\Assert;
use Nt\Commander\CommanderTrait;
use Topic\Domain\Command\GetTopicRepliesCommand;
use Topic\Domain\Model\Topic\TopicRepository;
use Topic\Domain\Service\TagService;

class GetTopicRepliesService implements ApplicationService {

    use CommanderTrait;

    /**
     * @param $request
     * @return array
     */
    public function execute( $request = null )
    {
        $replies = $this->commandExecute(GetTopicRepliesCommand::class,$request);

        $total = $replies['total'];
        $pn = $replies['pn'];
        $rn = $replies['rn'];


        $list = $replies['list'];
        $has_more = (int)($pn * $rn < $total);
        
        $info =  [
            'list' => $list,
            'page' => [
                'current_pn' => $pn,
                'total' => $total,
                'rn' => $rn,
                'has_more' => $has_more,
            ]
        ];

        return $info;
    }
}





