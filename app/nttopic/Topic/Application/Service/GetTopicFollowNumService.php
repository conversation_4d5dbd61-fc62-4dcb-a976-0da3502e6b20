<?php namespace Topic\Application\Service;


use Nt\Assert\Assert;
use Nt\Commander\CommanderTrait;

use Topic\Domain\Model\Topic\TopicRepository;

class GetTopicFollowNumService implements ApplicationService {

    /** @type  TopicRepository */
    protected $repo;

    /**
     * GetTopicFollowNumService constructor.
     *
     * @param TopicRepository $repo
     */
    public function __construct( TopicRepository $repo )
    {
        $this->repo = $repo;
    }

    use CommanderTrait;

    /**
     * @param $request
     * @return array
     * @throws \Topic\Domain\Exception\InvalidTopicId
     */
    public function execute( $request = null )
    {

        $topic_id = (int)$request['id'];
        Assert::greaterThan($topic_id,0);

        $topic = $this->repo->ofTopicIdOrFail($topic_id);

        return [
            'total' => $topic->getFollowNum(),
        ];
    }
}