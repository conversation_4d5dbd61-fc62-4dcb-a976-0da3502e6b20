<?php namespace Topic\Application\Service;

use Nt\Commander\CommanderTrait;
use Topic\Domain\Command\GetTopicFromReplyIdCommand;

/**
 * Class GetTopicFromReplyIdService
 * 从reply_id开始返回回复
 * @package Topic\Application\Service
 */
class GetTopicFromReplyIdService implements ApplicationService {

    use CommanderTrait;

    /**
     * @param $request
     * @return array
     */
    public function execute( $request = null )
    {

        $replies = $this->commandExecute(GetTopicFromReplyIdCommand::class,$request);

        $total = $replies['total'];
        $pn = $replies['pn'];
        $rn = $replies['rn'];


        $list = $replies['list'];
        $has_more = (int)($pn * $rn < $total);

        $info =  [
            'list' => $list,
            'page' => [
                'current_pn' => $pn,
                'total' => $total,
                'rn' => $rn,
                'has_more' => $has_more,
            ]
        ];

        return $info;
    }
}





