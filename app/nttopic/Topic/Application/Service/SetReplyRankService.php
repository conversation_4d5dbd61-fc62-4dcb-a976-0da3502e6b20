<?php namespace Topic\Application\Service;

use Nt\Commander\CommanderTrait;
use Topic\Domain\Command\AddTopicCommand;
use Topic\Domain\Command\SetReplyRankCommand;
use Topic\Domain\Model\Topic\Topic;
//use Topic\Domain\Model\Topic\TopicRepository;
//use Topic\Domain\Service\TagService;

class SetReplyRankService implements ApplicationService {

    use CommanderTrait;

    /**
     * @param $request
     * @return array
     */
    public function execute( $request = null )
    {
        //TODO: form validator

        // execute command

        $reply = $this->commandExecute( SetReplyRankCommand::class, $request );

        return [ ];
    }
}