<?php namespace Topic\Infrastructure\Listeners;

use Nt\Commander\Events\EventListener;
use Topic\Domain\Event\TopicWasRecovered;

class SendTopicRecoveredToSe extends EventListener{

    /**
     * @param TopicWasRecovered $event
     */
    public function whenTopicWasRecovered(TopicWasRecovered $event)
    {
        $topic_id = $event->getTopic()->getTopicId()->id();
        $title    = $event->getTopic()->getTitle();
        $se = \Bd_RalRpc::create('Se', array('pid'=>'weifans', 'app'=>'topic'));
        $arrParams = array(
            'id'          => $topic_id,
            'topic_title' => $title,
            'topic_id'    => (string)$topic_id,
        );
        \Bingo_Timer::start('se_insert');
        $res = $se->insert($arrParams);
        \Bingo_Timer::end('se_insert');
        if (null == $res || $res['err_no'] != \Tieba_Errcode::ERR_SUCCESS) {
            // 此处错误不影响主流程
            \Bingo_Log::warning("insert error,app:[tag], error_info: " . serialize($res) . "-" .  $se->get_error());
        }
    }

}