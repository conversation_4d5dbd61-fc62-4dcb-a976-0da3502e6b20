<?php namespace Topic\Infrastructure\Listeners;

use Nt\Commander\Events\EventListener;
use Topic\Domain\Event\TopicSetPrivate;

class SendTopicPrivateNmq extends EventListener{

    /**
     * @param TopicSetPrivate $event
     */
    public function whenTopicSetPrivate(TopicSetPrivate $event)
    {
        $topicInfo = $event->getTopic()->toArray();
        $topicInfo['tags'] =  $event->getTopic()->getTags();
        $topicInfo['private_only']  = 1;
//        \Bingo_Log::warning(var_export($topicInfo,1));
        if ($topicInfo['private'] == 1){// 设置私密
            \Service_Libs_Commit::sendNmq(\Tieba_Cmd_Nttopic::BLOCK_TOPIC,$topicInfo);
        }else { // 公开
            \Service_Libs_Commit::sendNmq(\Tieba_Cmd_Nttopic::RECOVER_TOPIC,$topicInfo);
        }
    }
}