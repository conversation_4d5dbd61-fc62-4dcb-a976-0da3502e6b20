<?php namespace Topic\Infrastructure\Listeners;

use Nt\Commander\Events\EventListener;
use Topic\Domain\Event\TopicWasBlocked;

class SendTopicBlockedToSe extends EventListener{

    /**
     * @param TopicWasBlocked $event
     */
    public function whenTopicWasBlocked(TopicWasBlocked $event)
    {
        $topic_id = $event->getTopic()->getTopicId()->id();
//        $title    = $event->getTopic()->getTitle();
        $se = \Bd_RalRpc::create('Se', array('pid'=>'weifans', 'app'=>'topic'));
        $arrParams = array(
            'id_list'          => [$topic_id],
        );
        \Bingo_Timer::start('se_delete');
        $res = $se->delete($arrParams);
        \Bingo_Timer::end('se_delete');
        if (null == $res || $res['err_no'] != \Tieba_Errcode::ERR_SUCCESS) {
            // 此处错误不影响主流程
            \Bingo_Log::warning("delete error,[topic_id:$topic_id], error_info: " . serialize($res) . "-" .  $se->get_error());
        }
//        $topicInfo['tags'] =  $event->getTopic()->getTags();
//        \Service_Libs_Commit::sendNmq(\Tieba_Cmd_Nttopic::BLOCK_TOPIC,$topicInfo);
    }
}