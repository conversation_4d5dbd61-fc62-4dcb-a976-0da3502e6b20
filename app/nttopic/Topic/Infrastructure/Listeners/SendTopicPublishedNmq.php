<?php namespace Topic\Infrastructure\Listeners;

use Nt\Commander\Events\EventListener;
use Topic\Domain\Event\TopicWasPublished;

class SendTopicPublishedNmq extends EventListener{

    /**
     * @param TopicWasPublished $event
     */
    public function whenTopicWasPublished(TopicWasPublished $event)
    {
        $topic = $event->getTopic();
        $tagInfo  = $topic->getTagInfo();
        $arrParams = $topic->toArray();

        $arrParams['tags'] = $tagInfo;
//        \Bingo_Log::warning( var_export( $arrParams, 1 ) );
        \Service_Libs_Commit::sendNmq('addTopic',$arrParams);
    }
}