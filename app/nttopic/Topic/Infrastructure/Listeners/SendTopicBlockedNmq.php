<?php namespace Topic\Infrastructure\Listeners;

use Nt\Commander\Events\EventListener;
use Topic\Domain\Event\TopicWasBlocked;

class SendTopicBlockedNmq extends EventListener{

    /**
     * @param TopicWasBlocked $event
     */
    public function whenTopicWasBlocked(TopicWasBlocked $event)
    {
        $topicInfo = $event->getTopic()->toArray();
        $topicInfo['tags'] =  $event->getTopic()->getTags();
        \Service_Libs_Commit::sendNmq(\Tieba_Cmd_Nttopic::BLOCK_TOPIC,$topicInfo);
    }
}