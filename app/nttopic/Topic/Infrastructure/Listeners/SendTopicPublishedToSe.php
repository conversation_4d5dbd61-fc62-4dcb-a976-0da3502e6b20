<?php namespace Topic\Infrastructure\Listeners;

use Nt\Commander\Events\EventListener;
use Topic\Domain\Event\TopicWasPublished;

/**
 * Class SendTopicPublishedToSe
 * 发数据到Se模块
 * @package Topic\Infrastructure\Listeners
 */
class SendTopicPublishedToSe extends EventListener{

    /**
     * @param TopicWasPublished $event
     */
    public function whenTopicWasPublished(TopicWasPublished $event)
    {
        $topic_id = $event->getTopic()->getTopicId()->id();
        $title    = $event->getTopic()->getTitle();
        $se = \Bd_RalRpc::create('Se', array('pid'=>'weifans', 'app'=>'topic'));
        \Bingo_Log::warning(var_export($se,1));
        $arrParams = array(
            'id'          => $topic_id,
            'topic_title' => $title,
            'topic_id'    => (string)$topic_id,
        );
        \Bingo_Timer::start('se_insert');
        $res = $se->insert($arrParams);
        \Bingo_Timer::end('se_insert');
        if (null == $res || $res['err_no'] != \Tieba_Errcode::ERR_SUCCESS) {
            // 此处错误不影响主流程
            \Bingo_Log::warning("insert error,app:[tag], error_info: " . serialize($res) . "-" .  $se->get_error());
        }
    }
}