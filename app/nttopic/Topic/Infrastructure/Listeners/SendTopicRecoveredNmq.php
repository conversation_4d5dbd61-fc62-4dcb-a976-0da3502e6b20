<?php namespace Topic\Infrastructure\Listeners;

use Nt\Commander\Events\EventListener;
use Topic\Domain\Event\TopicWasRecovered;

class SendTopicRecoveredNmq extends EventListener{

    /**
     * @param TopicWasRecovered $event
     */
    public function whenTopicWasRecovered(TopicWasRecovered $event)
    {
        $topicInfo = $event->getTopic()->toArray();
        $topicInfo['tags'] =  $event->getTopic()->getTags();
        \Service_Libs_Commit::sendNmq(\Tieba_Cmd_Nttopic::RECOVER_TOPIC,$topicInfo);
    }

}