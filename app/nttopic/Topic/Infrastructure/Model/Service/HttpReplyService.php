<?php namespace Topic\Infrastructure\Model\Service;


use Topic\Domain\Service\ReplyService;


class HttpReplyService implements ReplyService{

    /**
     * @param $replyIds
     * fake数据
     *
     * @return array
     * @throws \BadMethodCallException
     */
    public function ofIds( $replyIds )
    {
//        $replys = [];
//        foreach($replyIds as $reply_id) {
//            $replys[$reply_id] = [
//                'reply_id' => $reply_id,
//                'content' => 'mock数据' . $reply_id,
//            ];
//        }
//        return $replys;
        $replyInfo = \Service_Libs_Call::call('ntreply','mgetReplyInfo',[
            'reply_ids' => $replyIds,
        ]);
        return $replyInfo;
    }

    /**
     * @param $replyIds
     *
     * @return array
     */
    public function ofFloors( $replyIds )
    {
        $replyInfo = \Service_Libs_Call::call('ntreply','getFloorInfo',[
            'reply_ids' => implode(',',$replyIds),
        ]);
        return $replyInfo;
    }
}