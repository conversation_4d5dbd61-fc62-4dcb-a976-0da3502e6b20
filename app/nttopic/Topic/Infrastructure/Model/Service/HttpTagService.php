<?php namespace Topic\Infrastructure\Model\Service;

use Topic\Domain\Exception\InvalidTagId;
use Topic\Domain\Service\TagService;

class HttpTagService implements TagService{

    /**
     * @param $id
     */
    public function ofId( $id )
    {
        throw new \BadMethodCallException('method HttpTagService:ofId not implement');
    }

    /**
     * @param array $ids
     *
     * @return array
     * @throws \BadMethodCallException
     */
    public function ofIds( array $ids )
    {
        if(count($ids) === 0){
            return [];
        }
        $arrInput = [
            'tag_ids' => $ids,
        ];
        $names = \Service_Libs_Call::call('nttag','mgetTagNamesByIds',$arrInput);
        return $names['tags'];

//        $tagInfo = [];
//        foreach ($ids as $id) {
//            $tagInfo[$id] = [
//                'tag_name' => '测试' . $id,
//                'tag_id' => $id,
//            ];
//        }
//        return $tagInfo;
    }

    /**
     * @param array $ids
     * @return bool
     * @throws InvalidTagId
     */
    public function validateTagIds( array $ids )
    {
        return true;
    }
}