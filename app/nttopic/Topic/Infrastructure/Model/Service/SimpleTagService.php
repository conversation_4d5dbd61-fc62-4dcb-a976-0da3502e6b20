<?php namespace Topic\Infrastructure\Model\Service;

use Topic\Domain\Exception\InvalidTagId;
use Topic\Domain\Service\TagService;

class SimpleTagService implements TagService {

    static $tags = [
        0 => [
            'tag_id'   => 0,
            'tag_name' => "其他",
        ],
        1 => [
            'tag_id'   => 1,
            'tag_name' => "生活情感",
        ],
        2 => [
            'tag_id'   => 2,
            'tag_name' => "游戏动漫",
        ],
    ];

    /**
     * @param $id
     *
     * @return mixed
     * @throws \Topic\Domain\Exception\InvalidTagId
     */
    public function ofId( $id )
    {
        $info = self::$tags[$id];
        if (!$info){
            throw new InvalidTagId("$id is not validate tag");
        }
        return $info;
    }

    /**
     * @param array $ids
     *
     * @return array
     */
    public function ofIds( array $ids )
    {
        if ( count( $ids ) === 0 ) {
            return [];
        }
        $tagInfo = [];
        foreach ( $ids as $id ) {
            if (array_key_exists($id, self::$tags)){
                $tagInfo[ $id ] = self::$tags[$id];
            }
        }
        return $tagInfo;
    }

    /**
     * @param array $ids
     *
     * @return bool
     * @throws InvalidTagId
     */
    public function validateTagIds( array $ids )
    {
        return count($ids) == count($this->ofIds($ids));
    }
}