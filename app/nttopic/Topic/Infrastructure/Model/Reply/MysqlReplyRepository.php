<?php namespace Topic\Infrastructure\Model\Reply;

use Topic\Domain\Command\GetTopicFromReplyIdCommand;
use Topic\Domain\Command\GetTopicRepliesCommand;
use Topic\Domain\Model\Reply\Reply;
use Topic\Domain\Model\Reply\ReplyRepository;
use Topic\Infrastructure\Exception\MysqlQueryException;
use Topic\Infrastructure\Model\Topic\MysqlRepositoryTrait;

class MysqlReplyRepository implements ReplyRepository {

    use MysqlRepositoryTrait;



    /**
     * @param $topic_id
     * @param $reply_id
     * @return Reply
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function ofTopicIdAndId( $topic_id, $reply_id )
    {
        $db       = static::getDb();
        $queryRet = $db->select(
            [
                'cond' =>
                    [
                        'topic_id' => $topic_id,
                        'reply_id' => $reply_id,
                    ],
            ]
        );
        $this->assertQueryRet( $queryRet, "select topic_id:$topic_id reply_id:$reply_id error" );

        return Reply::fromData( $queryRet['data'][0] );
    }

    /**
     * @param $topic_id
     * @param $reply_id
     * @throws MysqlQueryException
     */
    public function remove( $topic_id, $reply_id )
    {
        $db = static::getDb()->getMysqlObj();
        $sql = sprintf( 'UPDATE reply set status=2 WHERE topic_id=%d and reply_id=%d' ,
                        $topic_id, $reply_id);
        \Bingo_Log::warning("sql: $sql");
        $res = $db->query($sql );
        if($res === false) {
            throw new MysqlQueryException( "remove topic_id:$topic_id reply_id:$reply_id error ");
        }
    }

    /**
     * @param Reply $reply
     * @return mixed|void
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function save( Reply $reply )
    {
        $db       = static::getDb();
        $field    = $reply->getDataModified();
        $topicId  = $reply->getTopicId();
        $replyId  = $reply->getReplyId();
        $queryRet = $db->update(
            [
                'field' => $field,
                'cond'  => [
                    'topic_id' => $topicId,
                    'reply_id' => $replyId,
                ],
            ]
        );
        $this->assertQueryRet( $queryRet, "save topic_id:$topicId reply_id:$replyId error" );
    }

    /**
     * @return \Dl_Db_Reply
     */
    public static function getDb()
    {
        return new \Dl_Db_Reply();
    }

    /**
     * @param Reply $reply
     * @return mixed|void
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function add( $reply )
    {
        $db  = static::getDb();
        $ret = $db->insert( [ 'field' => $reply->toArray() ] );
        $this->assertQueryRet( $ret, 'add reply error' );
    }

    /**
     * @param GetTopicRepliesCommand $command
     * @return mixed
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function getRank( GetTopicRepliesCommand $command )
    {
        $db  = static::getDb();
        $topic_id = $command->topic_id;
//        $query = 'select * from reply where topic_id = ? and has_resource=1 order by rank, reply_at'
        $query = [
            'cond' => [
                'topic_id' => $command->topic_id,
                'status' => Reply::NORMAL,
            ]
        ];
        if($command->rank){
            $query['cond']['has_resource'] = 1;
        }
//        $query['append'] = 'order by rank ' . $command->toOrder() . ', reply_at ' . $command->toOrder() . ' limit ' . $command->limit() . ',' . $command->rn;
        $query['append'] = 'order by reply_id ' . $command->toOrder() . ' limit ' . $command->limit() . ',' . $command->rn;
        $queryRet = $db->select($query);
        return $this->extractQueryRetData($queryRet,"get topic_id:$topic_id reply error");
    }

    /**
     * @param GetTopicFromReplyIdCommand $command
     *
     * @return mixed
     */
    public function getFromReplyId( GetTopicFromReplyIdCommand $command )
    {
        $db  = static::getDb();
        $topic_id = $command->topic_id;
        //        $query = 'select * from reply where topic_id = ? and status=1 and reply_id>=？ order by reply_at'
        $query = [
            'field' => [
                'reply_id',
            ],
            'cond' => [
                'topic_id' => $command->topic_id,
                'status'   => Reply::NORMAL,
            ]
        ];
        if ($command->toOrder() =='asc'){
            // 升序
            $append = sprintf(" and reply_id >= %d order by reply_id asc limit %d,%d", $command->reply_id,$command->limit(),$command->rn);
        }else {
            // 降序
            $append = sprintf(" and reply_id <= %d order by reply_id desc limit %d,%d", $command->reply_id,$command->limit(),$command->rn);
        }
        $query['append'] = $append;
        $queryRet = $db->select($query);
        return $this->extractQueryRetData($queryRet,"get topic_id:$topic_id reply error");

    }

    /**
     * @param $topic_id
     * @param $reply_id
     *
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function recover( $topic_id, $reply_id )
    {
        $db = static::getDb()->getMysqlObj();
        $sql = sprintf( 'UPDATE reply set status=1 WHERE topic_id=%d and reply_id=%d' ,
                        $topic_id, $reply_id);
        \Bingo_Log::warning("sql: $sql");
        $res = $db->query($sql );
        if($res === false) {
            throw new MysqlQueryException( "recover topic_id:$topic_id reply_id:$reply_id error ");
        }
    }

    /**
     * @param $topic_id
     *
     * @return mixed
     */
    public function maxReplyId( $topic_id )
    {
        $db  = static::getDb();
        $query = [
            'field' => [
                'max(reply_id) as reply_id',
            ],
            'cond' => [
                'topic_id' => $topic_id,
                'status'   => Reply::NORMAL,
            ]
        ];
        $queryRet = $db->select($query);
        $data =  $this->extractQueryRetData($queryRet,"get topic_id:$topic_id max reply error");
        return (int)$data[0]['reply_id'];
    }

    /**
     * @param $topic_id
     *
     * @return mixed
     */
    public function minReplyId( $topic_id )
    {
        $db  = static::getDb();
        $query = [
            'field' => [
                'min(reply_id) as reply_id',
            ],
            'cond' => [
                'topic_id' => $topic_id,
                'status'   => Reply::NORMAL,
            ]
        ];
        $queryRet = $db->select($query);
        $data =  $this->extractQueryRetData($queryRet,"get topic_id:$topic_id min reply error");
        return (int)$data[0]['reply_id'];
    }

    /**
     * @param $topic_id
     *
     * @return mixed
     */
    public function getReplyList( $topic_id )
    {
        $db  = static::getDb();
        $query = [
            'field' => [
                'reply_id',
                'status',
            ],
            'cond' => [
                'topic_id' => $topic_id,
            ],
            'append' => "order by reply_id asc",
        ];
        $queryRet = $db->select($query);
        return $this->extractQueryRetData($queryRet,"get topic_id:$topic_id full reply list error");
    }
}