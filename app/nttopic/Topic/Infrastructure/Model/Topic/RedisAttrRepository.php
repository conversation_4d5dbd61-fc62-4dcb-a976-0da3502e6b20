<?php namespace Topic\Infrastructure\Model\Topic;

use Topic\Domain\Model\Topic\AttrRepository;

class RedisAttrRepository implements AttrRepository {

    /**
     * @param $key
     * @param $field
     * @param $step
     *
     * @return bool
     * @throws \Exception
     */
    public function hincrBy( $key, $field, $step )
    {
        $redisInput = array(
            'key' => $key,
            'field' => $field,
            'step' => $step,
        );
        $arrRes = \Service_Libs_Redis::hincrBy($redisInput);

        if($arrRes === false  ){
            $msg = 'call Util_Redis hincrBy fail , input='.serialize($redisInput);
            throw new \Exception($msg,\Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        return $arrRes;
    }

    /**
     * @param $key
     * @param $field
     *
     * @return int
     * @throws \Exception
     */
    public function hget( $key, $field )
    {
        $redisInput = array(
            'key' => $key,
            'field' => $field,
            'unserial' => 0,
        );
        $arrRes = \Service_Libs_Redis::hgetFromRedis($redisInput);

        if($arrRes === false){
            $msg = 'call Util_Redis hgetFromRedis fail , input='.serialize($redisInput);
            throw new \Exception($msg,\Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        return intval($arrRes);
    }

    /**
     * @param $key
     *
     * @return array|null
     * @throws \Exception
     */
    public function hgetAll( $key )
    {
        $redisInput = array(
            'key' => $key,
        );
        $arrRes = \Service_Libs_Redis::hgetAllFromRedis($redisInput);

        if($arrRes === false){
            $msg = 'call Util_Redis hgetAllFromRedis fail , key='.$key;
            throw new \Exception($msg,\Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
        return $arrRes;
    }

    /**
     * @param $keys
     * @return mixed
     * @throws \Exception
     */
    public function mhgetAll( $keys )
    {
        $redisInput['keys'] = $keys;
        $arrRes = \Service_Libs_Redis::mgetFromRedis($redisInput);
        if($arrRes === false ){
            $msg = 'call Util_Redis mgetFromRedis fail , input='.serialize($redisInput);
            throw new \Exception($msg,\Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }
//        var_dump($arrRes);
        return $arrRes;
    }

    /**
     * @param $key
     * @param $field
     * @param $value
     *
     * @return bool
     * @throws \Exception
     */
    public function hset( $key, $field, $value )
    {
        $redisInput = array(
            "key" => $key,
            "field" => $field,
            "value" => $value,
            "serial" => 0,
        );

        $arrRes = \Service_Libs_Redis::hsetToRedis($redisInput);

        if($arrRes === false){
            $msg = 'call Util_Redis hsetToRedis fail , input='.serialize($redisInput);
            throw new \Exception($msg,\Tieba_Errcode::ERR_REDIS_CALL_FAIL);
        }

        return $arrRes;
    }
}