<?php
namespace Topic\Infrastructure\Model\Topic;

use Topic\Infrastructure\Exception\MysqlQueryException;

trait MysqlRepositoryTrait {

    /**
     * @param        $queryRet
     * @param string $msg
     * @return mixed
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function extractQueryRetData( $queryRet, $msg='' )
    {
        $this->assertQueryRet($queryRet, $msg);
        return $queryRet['data'];
    }

    /**
     * @param $queryRet
     * @param $msg
     * @throws MysqlQueryException
     */
    public function assertQueryRet( $queryRet, $msg = '' )
    {
        if(\Service_Libs_Base::isFail($queryRet)) {
            throw new MysqlQueryException( $msg .  '[error:' . serialize($queryRet) .']' );
        }
    }
}