<?php namespace Topic\Infrastructure\Model\Topic;


use Topic\Domain\Command\ListTopicCommand;
use Topic\Domain\Exception\InvalidTopicId;
use Topic\Domain\Model\Topic\Topic;
use Topic\Domain\Model\Topic\TopicId;
use Topic\Domain\Model\Topic\TopicRepository;
use Topic\Infrastructure\Exception\MysqlQueryException;

class MysqlTopicRepository implements TopicRepository {

    use MysqlRepositoryTrait;

    /**
     * @var IdStrategy
     */
    protected $idStrategy;

    /**
     * MysqlTopicRepository constructor.
     *
     * @param IdStrategy $idStrategy
     */
    public function __construct( IdStrategy $idStrategy )
    {
        $this->idStrategy = $idStrategy;
    }

    /**
     * @return TopicId
     */
    public function nextIdentity()
    {
        return new TopicId( $this->idStrategy->genId() );
    }

    /**
     * @param Topic $topic
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function add( Topic $topic )
    {
        $db  = static::getDb();
        $ret = $db->insert( [ 'field' => $topic->toArray() ] );
        $this->assertQueryRet( $ret, 'add topic error' );
    }

    /**
     * @param  integer[] $ids
     * @return array
     * @throws MysqlQueryException
     */
    public function ofTopicIds( $ids )
    {
        if ( 0 === count( $ids ) ) {
            return [ ];
        }
        $db  = static::getDb();
        $ret = $db->selectIn( 'topic_id', $ids );

        $data =  $this->extractQueryRetData( $ret );
        foreach ($data as &$item) {
            $item['tags'] =  (array)json_decode($item['tags']);
            $cover = (object)json_decode($item['cover']);
            if ($cover->big_url == "undefined" || $cover->small_url == "undefined"){
                $item['cover'] = (object)null;
            }else {
                $item['cover'] = $cover;
            }
            $item['private'] =  (int)$item['private'];
            $item['create_time'] =  $item['publish_at'];
        }
        return $data;
    }

    /**
     * @param $id
     * @return Topic
     * @throws \InvalidArgumentException
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function ofTopicId( $id )
    {
        $db       = static::getDb();
        $queryRet = $db->select(
            [
                'cond' => ['topic_id' => $id],
            ]
        );
        $this->assertQueryRet( $queryRet, 'select topic_id:' . $id . ' error' );

        return Topic::fromData( $queryRet['data'][0] );
    }

    /**
     * @param $id
     * @return Topic
     * @throws \InvalidArgumentException
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     * @throws \Topic\Domain\Exception\InvalidTopicId
     */
    public function ofTopicIdOrFail( $id )
    {
        $db       = static::getDb();
        $queryRet = $db->select(
            [
                'cond' => [
                    'topic_id' => $id,
                ]
            ]
        );
        $this->assertQueryRet( $queryRet, "select topic_id:$id error" );
        $data = $queryRet['data'];
        if ( 0 === count( $data ) ) {
            throw new InvalidTopicId( $id );
        }

        return Topic::fromData( $queryRet['data'][0] );
    }

    /**
     * @param Topic $topic
     * @return mixed
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function save( $topic )
    {
        $db       = static::getDb();
        $field    = $topic->getDataModified();
        $topicId  = $topic->getTopicId()->id();
        $queryRet = $db->update(
            [
                'field' => $field,
                'cond'  => [
                    'topic_id' => $topicId,
                ],
            ]
        );
        $this->assertQueryRet( $queryRet, "save topic_id:$topicId error" );
    }

    /**
     * @return \Dl_Db_Topic
     */
    protected static function getDb()
    {
        return new \Dl_Db_Topic();
    }

    /**
     * @param $topic_id
     * @return mixed|void
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function incReplyNum( $topic_id)
    {
        return $this->inc($topic_id,'reply_num');
    }

    /**
     * @param $topic_id
     * @return mixed|void
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function incFloorNum( $topic_id)
    {
        return $this->inc($topic_id,'floor_num');
    }

    /**
     * @param $topic_id
     * @return mixed|void
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function decReplyNum( $topic_id)
    {
        return $this->dec($topic_id,'reply_num');
    }

    /**
     * @param $topic_id
     * @return mixed|void
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function incCollectNum( $topic_id)
    {
        return $this->inc($topic_id,'collect_num');
    }

    /**
     * @param $topic_id
     * @return mixed|void
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function decCollectNum( $topic_id )
    {
        return $this->dec($topic_id,'collect_num');
    }

    /**
     * @param $topic_id
     * @param $field
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    protected function inc( $topic_id, $field )
    {
        $db = static::getDb();

        $queryRet = $db->update(
            [
                'field' => [
                    $field => [
                        'val' => $field . ' + 1',
                        'quotes' => 0,
                    ]
                ],
                'cond'  => [
                    'topic_id' => $topic_id,
                ],
            ]
        );
        $this->assertQueryRet( $queryRet, "inc topic_id:$topic_id field:$field error" );
    }

    /**
     * @param $topic_id
     * @param $field
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    protected function dec( $topic_id, $field )
    {
        $db = static::getDb();

        $queryRet = $db->update(
            [
                'field' => [
                    $field => [
                        'val' => $field . ' - 1',
                        'quotes' => 0,
                    ]
                ],
                'cond'  => [
                    'topic_id' => $topic_id,
                    $field => [
                        'val' => 0,
                        'opt' => '>'
                    ]
                ],
            ]
        );
        $this->assertQueryRet( $queryRet, "inc topic_id:$topic_id field:$field error" );
    }

    /**
     * @param ListTopicCommand $command
     *
     * @return array
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function listTopic( $command )
    {
        $append = [];
        if ($command->begin > 0){
            $append[] = "publish_at > {$command->begin}";
        }
        if ($command->end > 0 ){
            $append[] = "publish_at <= {$command->end}";
        }
        if ($command->status != ''){
            $append[] = "status = {$command->status}";
        }
        if ($command->is_recommend != ''){
            $append[] = "is_recommend = {$command->is_recommend}";
        }
        if (count($append)>0){
            $append = implode(' and ',$append);
            $append = 'where ' . $append;
        }else {
            $append = '';
        }
        $limit = $command->rn;
        $offset = ($command->pn-1)*$limit;
        $append .= " order by publish_at desc limit $offset,$limit";

        $db = static::getDb();

        $queryRet = $db->select(
            [
//                'cond'  => $cond,
                'append' => $append,
            ]
        );
        return $this->extractQueryRetData( $queryRet, 'list error' );
    }

    /**
     * @param ListTopicCommand $command
     *
     * @return int
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function count( $command )
    {

        $append = [];
        if ($command->begin > 0){
            $append[] = "publish_at > {$command->begin}";
        }
        if ($command->end > 0 ){
            $append[] = "publish_at <= {$command->end}";
        }
        if ($command->status != ''){
            $append[] = "status = {$command->status}";
        }
        if ($command->is_recommend != ''){
            $append[] = "is_recommend = {$command->is_recommend}";
        }
        if (count($append)>0){
            $append = implode(' and ',$append);
            $append = 'where ' . $append;
        }else {
            $append = '';
        }

        $db = static::getDb();

        $query = [
            'field' => [
                'count(1) as cnt'
            ]
        ];
        if ($append){
            $query['append'] = $append;
        }

        $queryRet = $db->select($query);
        $this->assertQueryRet($queryRet,'count topic error');
        return (int)$queryRet['data'][0]['cnt'];
    }

    /**
     * @param $topic_id
     * @param $reply_id
     *
     * @return mixed
     */
    public function setFirstFloor( $topic_id, $reply_id )
    {
        $db       = static::getDb();

        $queryRet = $db->update(
            [
                'field' => [
                    'first_floor' => $reply_id,
                ],
                'cond'  => [
                    'topic_id' => $topic_id,
                ],
            ]
        );
        $this->assertQueryRet( $queryRet, "save topic_id:$topic_id error" );
}
}