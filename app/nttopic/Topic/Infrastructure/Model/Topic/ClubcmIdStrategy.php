<?php namespace Topic\Infrastructure\Model\Topic;

use Topic\Infrastructure\Exception\ClubcmServiceNotAvaliable;

class ClubcmIdStrategy implements IdStrategy {

    const TYPE = 'nt_reply_id';

    /**
     * id服务
     * @return integer
     * @throws ClubcmServiceNotAvaliable
     */
    public function genId()
    {
        $ids = Tieba_Idalloc::alloc(self::TYPE);

        Bingo_Log::notice("[nttopic] nt_reply_id :" .json_encode($ids));

        if (empty($ids[0])) {
            Bingo_Log::warning(sprintf('failed to call Tieba_Idalloc::alloc'));
            throw new ClubcmServiceNotAvaliable();
        }else{
            return intval($ids[0]);
        }
    }
}