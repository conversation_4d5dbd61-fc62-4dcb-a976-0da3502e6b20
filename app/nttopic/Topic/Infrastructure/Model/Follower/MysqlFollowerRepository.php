<?php namespace Topic\Infrastructure\Model\Follower;

use Topic\Domain\Command\GetTopicCollectCommand;
use Topic\Domain\Model\Follower\Follower;
use Topic\Domain\Model\Follower\FollowerRepository;
use Topic\Infrastructure\Exception\MysqlQueryException;
use Topic\Infrastructure\Model\Topic\MysqlRepositoryTrait;

class MysqlFollowerRepository implements FollowerRepository {

    use MysqlRepositoryTrait;

    /**
     * @param $topic_id
     * @param $user_id
     * @return Follower
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function ofTopicIdAndId( $topic_id, $user_id )
    {
        $db       = static::getDb();
        $queryRet = $db->select(
            [
                'cond' =>
                    [
                        'topic_id' => $topic_id,
                        'user_id'  => $user_id,
                    ],
            ]
        );
        $this->assertQueryRet( $queryRet, "select topic_id:$topic_id user_id:$user_id error" );

        return Follower::fromData( $queryRet['data'][0] );
    }

    /**
     * @param Follower $follower
     * @throws \Topic\Infrastructure\Exception\MysqlQueryException
     */
    public function add( $follower )
    {
        $db  = static::getDb();
        $ret = $db->insert( [ 'field' => $follower->toArray() ] );
        $this->assertQueryRet( $ret, 'add follower error' );
    }

    /**
     * @param $topic_id
     * @param $user_id
     * @throws MysqlQueryException
     */
    public function remove( $topic_id, $user_id )
    {
        $db  = static::getDb()->getMysqlObj();
        $sql = sprintf( 'DELETE FROM follower WHERE topic_id=%d and user_id=%d',
                        $topic_id, $user_id );
        $res = $db->query( $sql );
        \Bingo_Log::warning("sql: $sql");
        if ( $res === false ) {
            throw new MysqlQueryException( "remove topic_id:$topic_id user_id:$user_id error " );
        }
    }

    /**
     * @return \Dl_Db_Follower
     */
    protected static function getDb()
    {
        return new \Dl_Db_Follower();
    }

    /**
     * @param GetTopicCollectCommand $command
     * 
*@return mixed
     */
    public function getRanK( $command )
    {
        $db  = static::getDb();
        $topic_id = $command->topic_id;
        //        $query = 'select * from follow where topic_id = ? order by follow_at'
        $query = [
            'field' => [
                'user_id',
            ],
            'cond' => [
                'topic_id' => $command->topic_id,
            ]
        ];
        $query['append'] = 'order by follow_at ' . $command->toOrder() . ' limit ' . $command->limit() . ',' . $command->rn;
        $queryRet = $db->select($query);
        return $this->extractQueryRetData($queryRet,"get topic_id:$topic_id follow error");
    }
}