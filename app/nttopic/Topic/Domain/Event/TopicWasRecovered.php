<?php namespace Topic\Domain\Event;

use Topic\Domain\Model\Topic\Topic;

class TopicWasRecovered {

    /**
     * @var Topic
     */
    protected $topic;



    /**
     * @return Topic
     */
    public function getTopic()
    {
        return $this->topic;
    }

    /**
     * TopicPublished constructor.
     *
     * @param Topic $topic
     */
    public function __construct( Topic $topic )
    {
        $this->topic = $topic;
    }



}