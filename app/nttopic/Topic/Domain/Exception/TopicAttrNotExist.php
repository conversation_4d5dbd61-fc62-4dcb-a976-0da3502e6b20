<?php namespace Topic\Domain\Exception;

class TopicAttrNotExist extends \Exception {

    /**
     * TopicAttrNotExist constructor.
     *
     * @param string $topic_id
     * @param int    $attr
     * @param int    $code
     */
    public function __construct( $topic_id, $attr, $code = 0 )
    {
        parent::__construct( sprintf( 'topic_id:%d attr:%s is invalid', $topic_id, $attr ), $code );
    }
}