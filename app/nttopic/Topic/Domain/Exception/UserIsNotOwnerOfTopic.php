<?php namespace Topic\Domain\Exception;

class UserIsNotOwnerOfTopic extends \Exception {

    /**
     * InvalidTopicId constructor.
     *
     * @param string $topic_id
     * @param int    $user_id
     * @param int    $code
     */
    public function __construct( $topic_id, $user_id, $code = 0 )
    {
        $code = \Tieba_Errcode::ERR_NO_RIGHT;
        parent::__construct( sprintf( 'user:%d is not the owner of topic:%d', $user_id,$topic_id ), $code );
    }
}