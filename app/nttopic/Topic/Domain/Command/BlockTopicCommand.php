<?php namespace Topic\Domain\Command;

use Nt\Assert\Assert;

/**
 * Class BlockTopicCommand
 *
 * @package Topic\Domain\Command
 */
class BlockTopicCommand {

    public $topic_id;


    /**
     * BlockTopicCommand constructor.
     *
     * @param     $topic_id
     */
    public function __construct( $topic_id )
    {
        $this->topic_id = (int)$topic_id;
    }

    public function validate()
    {
        Assert::greaterThan($this->topic_id,0);
    }
}