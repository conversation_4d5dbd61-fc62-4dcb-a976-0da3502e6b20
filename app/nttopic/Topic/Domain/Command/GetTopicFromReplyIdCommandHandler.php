<?php namespace Topic\Domain\Command;

use Nt\Commander\CommandHandler;
use Topic\Domain\Model\Reply\ReplyRepository;
use Topic\Domain\Model\Topic\TopicRepository;
use Topic\Domain\Service\ReplyService;

class GetTopicFromReplyIdCommandHandler implements CommandHandler {
    /**
     * @var TopicRepository
     */
    protected $topicRepository;

    /**
     * @var ReplyRepository
     */
    protected $replyRepository;

    /**
     * @var ReplyService
     */
    protected $replyService;

    /**
     * GetTopicRepliesCommandHandler constructor.
     *
     * @param TopicRepository $topicRepository
     * @param ReplyRepository $replyRepository
     * @param ReplyService    $replyService
     */
    public function __construct( TopicRepository $topicRepository,
                                 ReplyRepository $replyRepository,
                                 ReplyService $replyService)
    {
        $this->topicRepository = $topicRepository;
        $this->replyRepository = $replyRepository;
        $this->replyService = $replyService;
    }

    /**
     * Handle the command.
     *
     * @param  GetTopicFromReplyIdCommand $command
     * @return array
     * @throws \Topic\Domain\Exception\InvalidTopicId
     */
    public function handle( $command )
    {
        $topic = $this->topicRepository->ofTopicIdOrFail($command->topic_id);

        $replies = $this->replyRepository->getFromReplyId($command);
        $replyInfo = [];
//        var_dump($replies);
        foreach ($replies as $reply){
            $replyInfo[$reply['reply_id']] = $reply;
        }
//        var_dump($replyInfo);
//        Bingo_Log::warning( var_export( $replyInfo, 1 ) );
        if(0 === count($replyInfo)){
            $list = [];
        }
        else {
//            if($command->basic){
                $list = $replyInfo;
//            }
//            else {
//                $list = $this->replyService->ofFloors(array_keys($replyInfo));
//            }
        }

        $total = $topic->getReplyNum();
        $pn = $command->pn;
        $rn = $command->rn;

        return compact( 'list', 'total', 'pn', 'rn');
    }
}