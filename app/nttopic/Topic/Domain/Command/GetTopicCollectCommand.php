<?php namespace Topic\Domain\Command;

use Nt\Assert\Assert;

class GetTopicCollectCommand {

    public $topic_id;
    public $order;
    public $pn;
    public $rn;


    /**
     * GetTopicRepliesCommand constructor.
     *
     * @param     $topic_id
     * @param int $order
     * @param int $pn
     * @param int $rn
     */
    public function __construct( $topic_id, $order =  0, $pn = 1, $rn = 10 )
    {
        $this->topic_id     = (int)$topic_id;
        $this->order        = (int)$order;
        $this->pn           = (int)$pn;
        $this->rn           = (int)$rn;
    }

    public function validate()
    {
        Assert::greaterThan($this->topic_id,0);
        Assert::greaterThan($this->pn,0);
        Assert::greaterThan($this->rn,0);
        Assert::oneOf($this->order,[0,1]);
    }

    /**
     * @return string
     */
    public function toOrder()
    {
        if($this->order){
            return 'asc';
        }
        else {
            return 'desc';
        }
    }

    /**
     * @return int
     */
    public function limit()
    {
        return ($this->pn - 1) * $this->rn;
    }


}