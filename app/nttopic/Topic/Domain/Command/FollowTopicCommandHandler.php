<?php namespace Topic\Domain\Command;

use Nt\Commander\CommandHandler;
use Topic\Domain\Model\Follower\Follower;
use Topic\Domain\Model\Follower\FollowerRepository;
use Topic\Domain\Model\Topic\TopicRepository;

class FollowTopicCommandHandler implements CommandHandler{

    /**
     * @var TopicRepository
     */
    protected $topicRepository;

    /**
     * @var FollowerRepository
     */
    protected $followerRepository;

    /**
     * FollowTopicCommandHandler constructor.
     *
     * @param TopicRepository    $topicRepository
     * @param FollowerRepository $followerRepository
     */
    public function __construct( TopicRepository $topicRepository,
                                 FollowerRepository $followerRepository)
    {
        $this->topicRepository = $topicRepository;
        $this->followerRepository = $followerRepository;
    }

    /**
     * Handle the command.
     *
     * @param  FollowTopicCommand $command
     * @return mixed
     * @throws \Topic\Domain\Exception\InvalidTopicId
     */
    public function handle( $command )
    {
        $user_id = $command->user_id;
        $topic_id = $command->topic_id;

        // validate topic_id is ok
        $this->topicRepository->ofTopicIdOrFail($topic_id);

        $follower = $this->followerRepository->ofTopicIdAndId($topic_id,$user_id);
        if($follower){
            return $follower;
        }

        $follower = Follower::followTopic($topic_id,$user_id);
        // insert on duplicate
        $this->followerRepository->add($follower);

        $this->topicRepository->incFollowNum($topic_id);
        return $follower;
    }
}