<?php namespace Topic\Domain\Command;



use Nt\Commander\CommandHandler;
use Nt\Commander\Events\DispatchableTrait;
use Topic\Domain\Exception\InvalidTagId;
use Topic\Domain\Model\Topic\Topic;
use Topic\Domain\Model\Topic\TopicRepository;

class RecoverTopicCommandHandler implements CommandHandler{

    use DispatchableTrait;

    /**
     * @var TopicRepository
     */
    protected $topicRepository;

    /**
     * AddTopicCommandHandler constructor.
     *
     * @param TopicRepository $topicRepository
     */
    public function __construct( TopicRepository $topicRepository )
    {
        $this->topicRepository = $topicRepository;
    }

    /**
     * @param BlockTopicCommand $command
     * @return Topic
     * @throws \Topic\Domain\Exception\InvalidTopicId
     * @throws InvalidTagId
     */
    public function handle($command)
    {
        // validate topic id
        $topic = $this->topicRepository->ofTopicIdOrFail($command->topic_id);

        if($topic->isNormal()) {
            // do nothing
           return $topic;
        }

        $topic->recover();

        $this->topicRepository->save($topic);

        $this->dispatchEventsFor($topic);

        return $topic;
    }
}