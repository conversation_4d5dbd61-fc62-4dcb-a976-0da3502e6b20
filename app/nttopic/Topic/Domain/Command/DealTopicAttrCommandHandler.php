<?php namespace Topic\Domain\Command;

use Nt\Commander\CommandHandler;
use Topic\Domain\Model\Topic\Attribute;
use Topic\Domain\Model\Topic\AttrRepository;
use Topic\Domain\Model\Topic\TopicRepository;

class DealTopicAttrCommandHandler implements CommandHandler {
    /**
     * @var TopicRepository
     */
    protected $topicRepository;
    /**
     * @var \Topic\Domain\Model\Topic\AttrRepository
     */
    private $attrRepository;

    /**
     * DealTopicAttrCommandHandler constructor.
     *
     * @param TopicRepository                          $topicRepository
     * @param \Topic\Domain\Model\Topic\AttrRepository $attrRepository
     */
    public function __construct( TopicRepository $topicRepository,
AttrRepository $attrRepository)
    {
        $this->topicRepository = $topicRepository;
        $this->attrRepository = $attrRepository;
    }

    /**
     * Handle the command.
     *
     * @param DealTopicAttrCommand $command
     *
     * @return mixed
     */
    public function handle( $command )
    {
        $topic_id = $command->topic_id;
        // validate topic_id is ok
        $topic = $this->topicRepository->ofTopicIdOrFail($topic_id);

        $attr = new Attribute($topic_id,$this->attrRepository);
        switch ($command->action){
            case "add":
                $bool = $attr->incrAttrValue($command->attr,$command->value);
                break;
            case "del":
                $bool = $attr->descAttrValue($command->attr,$command->value);
                break;
            case "clc":
                $bool = $attr->clearAttr($command->attr);
                break;
            default:
                $bool = false;
        }
        return ['user_id'=>$topic->getUserId(),'bool'=>$bool];
    }
}