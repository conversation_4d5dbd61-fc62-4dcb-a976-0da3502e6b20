<?php namespace Topic\Domain\Command;

use Nt\Assert\Assert;

class UnCollectTopicCommand {
    public $user_id;
    public $topic_id;

    /**
     * FollowTopicCommand constructor.
     *
     * @param $user_id
     * @param $topic_id
     */
    public function __construct( $user_id, $topic_id )
    {
        $this->user_id  = (int)$user_id;
        $this->topic_id = (int)$topic_id;
    }

    public function validate()
    {
        Assert::greaterThan($this->user_id,0);
        Assert::greaterThan($this->topic_id,0);
    }

}