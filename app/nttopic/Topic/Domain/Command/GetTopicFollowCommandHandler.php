<?php namespace Topic\Domain\Command;

use Nt\Commander\CommandHandler;
use Topic\Domain\Model\Follower\FollowerRepository;
use Topic\Domain\Model\Reply\ReplyRepository;
use Topic\Domain\Model\Topic\TopicRepository;

class GetTopicFollowCommandHandler implements CommandHandler{

    /**
     * @var FollowerRepository
     */
    protected $followRepository;

    /**
     * @var TopicRepository
     */
    protected $topicRepository;

    /**
     * GetTopicFollowCommandHandler constructor.
     *
     * @param FollowerRepository $followRepository
     * @param TopicRepository $topicRepository
     */
    public function __construct( FollowerRepository $followRepository,
                                 TopicRepository $topicRepository)
    {
        $this->followRepository = $followRepository;
        $this->topicRepository = $topicRepository;
    }

    /**
     * Handle the command.
     *
     * @param GetTopicFollowCommand $command
     * @return mixed
     */
    public function handle( $command )
    {
        $topic = $this->topicRepository->ofTopicIdOrFail($command->topic_id);

        $list = $this->followRepository->getRanK($command);

        $user_ids = [];
        foreach ($list as $item){
            $user_ids[] = $item['user_id'];
        }


        $total = $topic->getFollowNum();
        $pn = $command->pn;
        $rn = $command->rn;

        return compact('user_ids','total','pn','rn');
    }
}