<?php namespace Topic\Domain\Command;

use Nt\Commander\CommandHandler;
use Nt\Commander\Events\DispatchableTrait;
use Topic\Domain\Model\Follower\Follower;
use Topic\Domain\Model\Topic\TopicRepository;
use Topic\Domain\Model\Topic\TopicStatus;
use Topic\Domain\Service\TagService;

class ModifyTopic<PERSON>ommandHandler implements CommandHandler{
    use DispatchableTrait;

    /**
     * @var TopicRepository
     */
    protected $topicRepository;
    /**
     * @var \Topic\Domain\Service\TagService
     */
    private $tagService;

    /**
     * FollowTopicCommandHandler constructor.
     *
     * @param TopicRepository                  $topicRepository
     * @param \Topic\Domain\Service\TagService $tagService
     */
    public function __construct( TopicRepository $topicRepository, TagService $tagService)
    {
        $this->topicRepository = $topicRepository;
        $this->tagService = $tagService;
    }

    /**
     * Handle the command.
     *
     * @param  ModifyTopicCommand $command
     * @return mixed
     * @throws \Topic\Domain\Exception\InvalidTopicId
     */
    public function handle( $command )
    {
        $topic_id = $command->topic_id;

        // validate topic_id is ok
        $topic = $this->topicRepository->ofTopicIdOrFail($topic_id);

        if ($command->status == TopicStatus::REVIEWED) {
            $topic->doUnPass();
        }
        if ($command->is_recommend == 0){
            $topic->doUnRecommend();
        }
        if ($command->tags != '' && $this->tagService->ofIds($command->tags)){
            $topic->doSetTags($command->tags);
        }
        $this->topicRepository->save($topic);
        $this->dispatchEventsFor($topic);
        return true;
    }
}