<?php namespace Topic\Domain\Command;

use Nt\Assert\Assert;

class AddTopicCommand {

    public $title;
    public $description;
    public $tags;
    public $user_id;
    public $cover;
    public $create_time;
    public $private;

    /**
     * AddTopicCommand constructor.
     *
     * @param        $title
     * @param        $tags
     * @param        $user_id
     * @param string $description
     * @param string $cover
     * @param int    $create_time
     * @param int    $private
     */
    public function __construct( $title, $tags, $user_id, $description = '', $cover = '',$create_time=0, $private = 0 )
    {
        $this->title       = $title;
        $this->description = $description;
        $this->tags        = $tags;
        $this->user_id     = (int)$user_id;
        $this->cover = ($cover);
        $this->create_time = $create_time;
        $this->private = $private;
    }

    /**
     * 规则
     */
    public function validate()
    {
        Assert::greaterThan($this->user_id,0);
    }

}