<?php namespace Topic\Domain\Command;

use Nt\Assert\Assert;

class DealTopicAttrCommand {
    public $topic_id;
    public $action;
    public $attr;
    public $value;

    /**
     * DealTopicAttrCommand constructor.
     *
     * @param $topic_id
     * @param $action
     * @param $attr
     * @param $value
     */
    public function __construct( $topic_id, $action, $attr, $value = 1)
    {
        $this->topic_id = $topic_id;
        $this->action   = $action;
        $this->attr     = $attr;
        $this->value    = $value;
    }

    /**
     * 规则
     */
    public function validate()
    {
        Assert::greaterThan($this->topic_id,0);
        Assert::oneOf($this->action,['add','del','clc']);
    }

}