<?php namespace Topic\Domain\Command;

use Nt\Assert\Assert;

/**
 * Class SetPrivateCommand
 *
 * @package Topic\Domain\Command
 */
class SetPrivateCommand {

    public $topic_id;
    public $user_id;
    public $private;

    /**
     * BlockTopicCommand constructor.
     *
     * @param     $topic_id
     * @param     $user_id
     * @param     $private
     */
    public function __construct( $topic_id,$user_id,$private )
    {
        $this->topic_id = (int)$topic_id;
        $this->user_id = (int)$user_id;
        $this->private = (int)$private;
    }

    public function validate()
    {
        Assert::greaterThan($this->topic_id,0);
        Assert::greaterThan($this->user_id,0);
        Assert::oneOf($this->private,[0,1]);
    }
}