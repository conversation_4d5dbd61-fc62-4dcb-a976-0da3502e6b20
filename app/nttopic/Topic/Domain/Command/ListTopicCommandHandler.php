<?php namespace Topic\Domain\Command;

use Nt\Commander\CommandHandler;
use Nt\Commander\Events\DispatchableTrait;
use Topic\Domain\Model\Topic\TopicRepository;


class ListTopicCommandHandler implements CommandHandler{

    use DispatchableTrait;

    /**
     * @var TopicRepository
     */
    protected $topicRepository;

    /**
     * AddTopicCommandHandler constructor.
     *
     * @param TopicRepository $topicRepository
     */
    public function __construct( TopicRepository $topicRepository )
    {
        $this->topicRepository = $topicRepository;
    }

    /**
     * @param ListTopicCommand $command
     *
     * @return array
     */
    public function handle($command)
    {
        // select * from topic where publish_at> and publish_at< order by publish_at desc limit 1,10
        $topics = $this->topicRepository->listTopic($command);

        $total = $this->topicRepository->count($command);

        return [
            'list' => $topics,
            'total' => $total,
            'pn' => $command->pn,
            'rn' => $command->rn,
        ];
    }

}