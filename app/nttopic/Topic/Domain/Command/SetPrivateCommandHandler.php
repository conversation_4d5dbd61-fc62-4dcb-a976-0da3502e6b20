<?php namespace Topic\Domain\Command;

use Nt\Commander\CommandHandler;
use Nt\Commander\Events\DispatchableTrait;
use Topic\Domain\Exception\InvalidTagId;
use Topic\Domain\Exception\UserIsNotOwnerOfTopic;
use Topic\Domain\Model\Topic\Topic;
use Topic\Domain\Model\Topic\TopicRepository;
use Topic\Domain\Service\TagService;

class SetPrivateCommandHandler implements CommandHandler{


    use DispatchableTrait;
    /**
     * @var TopicRepository
     */
    protected $topicRepository;

    /**
     * AddTopicCommandHandler constructor.
     *
     * @param TopicRepository $topicRepository
     */
    public function __construct( TopicRepository $topicRepository )
    {
        $this->topicRepository = $topicRepository;
    }

    /**
     * @param SetPrivateCommand $command
     *
     * @return \Topic\Domain\Model\Topic\Topic
     * @throws \Topic\Domain\Exception\UserIsNotOwnerOfTopic
     */
    public function handle($command)
    {
        // validate topic id
        $topic = $this->topicRepository->ofTopicIdOrFail($command->topic_id);

        if ($topic->getUserId() != $command->user_id){
            throw new UserIsNotOwnerOfTopic($command->topic_id,$command->user_id);
        }

//        $topic->doBlock();
        $topic->setPrivate($command->private);

        $this->topicRepository->save($topic);
//        \Bingo_Log::warning("here dispatch");
        $this->dispatchEventsFor($topic);

        return $topic;
    }
}