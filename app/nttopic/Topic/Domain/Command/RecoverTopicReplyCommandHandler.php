<?php namespace Topic\Domain\Command;

use Nt\Commander\CommandHandler;
use Topic\Domain\Exception\InvalidTagId;
use Topic\Domain\Model\Reply\Reply;
use Topic\Domain\Model\Reply\ReplyRepository;
use Topic\Domain\Model\Topic\TopicRepository;


class RecoverTopicReplyCommandHandler implements CommandHandler{

    /**
     * @var TopicRepository
     */
    protected $topicRepository;

    /**
     * @var ReplyRepository
     */
    protected $replyRepository;

    /**
     * AddTopicCommandHandler constructor.
     *
     * @param TopicRepository $topicRepository
     * @param ReplyRepository $replyRepository
     */
    public function __construct(TopicRepository $topicRepository,
                                ReplyRepository $replyRepository)
    {
        $this->topicRepository = $topicRepository;
        $this->replyRepository = $replyRepository;
    }

    /**
     * @param RecoverTopicReplyCommand $command
     * @return mixed
     * @throws \Topic\Domain\Exception\InvalidTopicId
     * @throws \InvalidArgumentException
     * @throws InvalidTagId
     */
    public function handle($command)
    {
        $reply = $this->replyRepository->ofTopicIdAndId($command->topic_id,$command->reply_id);

        if(!$reply){
            return $reply;
        }

        $this->replyRepository->recover($command->topic_id,$command->reply_id);

        $this->topicRepository->incReplyNum($command->topic_id);

        return $reply;
    }
}