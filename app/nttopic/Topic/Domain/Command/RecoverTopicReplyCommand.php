<?php namespace Topic\Domain\Command;



use Nt\Assert\Assert;

class RecoverTopicReplyCommand {

    public $topic_id;
    public $reply_id;

    /**
     * AddTopicReplyCommand constructor.
     *
     * @param $topic_id
     * @param $reply_id
     */
    public function __construct( $topic_id, $reply_id)
    {
        $this->topic_id     = (int)$topic_id;
        $this->reply_id     = (int)$reply_id;
    }


    public function validate()
    {
        Assert::greaterThan($this->topic_id,0);
        Assert::greaterThan($this->reply_id,0);
//        Assert::oneOf($this->has_resource,[0,1]);
    }
}