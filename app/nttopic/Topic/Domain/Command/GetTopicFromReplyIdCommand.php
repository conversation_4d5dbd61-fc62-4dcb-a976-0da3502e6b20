<?php namespace Topic\Domain\Command;

use Nt\Assert\Assert;

class GetTopicFromReplyIdCommand {

    public $topic_id;
    public $reply_id;
    public $pn;
    public $rn;
//    /**
//     * @var int
//     * 只获取基本信息,不做批量操作
//     */
//    public $basic;

    public $order;

    /**
     * GetTopicRepliesCommand constructor.
     *
     * @param int $topic_id
     * @param int $reply_id
     * @param int $order
     * @param int $pn
     * @param int $rn
     */
    public function __construct( $topic_id, $reply_id = 0, $order = 1, $pn = 1, $rn = 10)
    {
        $this->topic_id = (int)$topic_id;
        $this->reply_id = (int)$reply_id;
        $this->pn       = (int)$pn;
        $this->rn       = (int)$rn;
//        $this->basic    = (int)$basic;
        $this->order = $order;
    }

    public function validate()
    {
        Assert::greaterThan( $this->topic_id, 0 );
        Assert::greaterThanEq( $this->pn, 1 );
        Assert::greaterThanEq( $this->rn, 1 );
//        Assert::oneOf( $this->reply_id, [ 0, 1 ] );
//        Assert::oneOf( $this->basic, [ 0, 1 ] );
    }

    /**
     * @return int
     */
    public function limit()
    {
        return ( $this->pn - 1 ) * $this->rn;
    }

    /**
     * @return string
     */
    public function toOrder()
    {
        if ( $this->order ) {
            return 'asc';
        } else {
            return 'desc';
        }
    }
}