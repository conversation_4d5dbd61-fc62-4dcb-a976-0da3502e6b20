<?php namespace Topic\Domain\Command;

use Nt\Assert\Assert;

class ModifyTopicCommand {
    public  $topic_id;
    public $status;
    public $is_recommend;
    public $tags;

    /**
     * FollowTopicCommand constructor.
     *
     * @param $topic_id
     * @param $status
     * @param $is_recommend
     * @param $tag
     */
    public function __construct( $topic_id, $status='', $is_recommend='', $tag='' )
    {
        $this->topic_id = $topic_id;
        $this->status = $status;
        $this->is_recommend = $is_recommend;
        if ($tag !== ''){
            $this->tags = [$tag];
        }else {
            $this->tags = [];
        }
        $this->validate();
    }

    public function validate()
    {
        Assert::greaterThan($this->topic_id,0);
    }

}