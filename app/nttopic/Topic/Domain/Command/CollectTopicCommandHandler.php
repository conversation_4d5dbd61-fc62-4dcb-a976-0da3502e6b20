<?php namespace Topic\Domain\Command;

use Nt\Commander\CommandHandler;
use Topic\Domain\Model\Follower\Follower;
use Topic\Domain\Model\Follower\FollowerRepository;
use Topic\Domain\Model\Topic\TopicRepository;

class CollectTopicCommandHandler implements CommandHandler{

    /**
     * @var TopicRepository
     */
    protected $topicRepository;

    /**
     * @var FollowerRepository
     */
    protected $followerRepository;

    /**
     * FollowTopicCommandHandler constructor.
     *
     * @param TopicRepository    $topicRepository
     * @param FollowerRepository $followerRepository
     */
    public function __construct( TopicRepository $topicRepository,
                                 FollowerRepository $followerRepository)
    {
        $this->topicRepository = $topicRepository;
        $this->followerRepository = $followerRepository;
    }

    /**
     * Handle the command.
     *
     * @param  CollectTopicCommand $command
     * @return mixed
     * @throws \Topic\Domain\Exception\InvalidTopicId
     */
    public function handle( $command )
    {
        $user_id = $command->user_id;
        $topic_id = $command->topic_id;

        // validate topic_id is ok
        $topic = $this->topicRepository->ofTopicIdOrFail($topic_id);

        $follower = $this->followerRepository->ofTopicIdAndId($topic_id,$user_id);
        if($follower){
            return $topic->getUserId();
        }

        $follower = Follower::followTopic($topic_id,$user_id);
        // insert on duplicate
        $this->followerRepository->add($follower);

        $this->topicRepository->incCollectNum( $topic_id);
        return $topic->getUserId();
    }
}