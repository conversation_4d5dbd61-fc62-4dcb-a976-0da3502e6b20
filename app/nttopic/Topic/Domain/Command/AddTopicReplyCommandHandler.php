<?php namespace Topic\Domain\Command;

use Nt\Commander\CommandHandler;
use Topic\Domain\Exception\InvalidTagId;
use Topic\Domain\Model\Reply\Reply;
use Topic\Domain\Model\Reply\ReplyRepository;
use Topic\Domain\Model\Topic\TopicRepository;


class AddTopicReplyCommandHandler implements CommandHandler{

    /**
     * @var TopicRepository
     */
    protected $topicRepository;

    /**
     * @var ReplyRepository
     */
    protected $replyRepository;

    /**
     * AddTopicCommandHandler constructor.
     *
     * @param TopicRepository $topicRepository
     * @param ReplyRepository $replyRepository
     */
    public function __construct(TopicRepository $topicRepository,
                                ReplyRepository $replyRepository)
    {
        $this->topicRepository = $topicRepository;
        $this->replyRepository = $replyRepository;
    }

    /**
     * @param AddTopicReplyCommand $command
     * @return mixed
     * @throws \Topic\Domain\Exception\InvalidTopicId
     * @throws \InvalidArgumentException
     * @throws InvalidTagId
     */
    public function handle($command)
    {
        /*
         * 校验topic_id有效性
         */
        $this->topicRepository->ofTopicIdOrFail($command->topic_id);

        $reply = Reply::reply(
            $command->topic_id,
            $command->reply_id,
            $command->reply_at,
            $command->has_resource
        );

        $this->replyRepository->add($reply);

        $this->topicRepository->incReplyNum($command->topic_id);
        $this->topicRepository->incFloorNum($command->topic_id);

        if ($command->floor_num == 1){
            $this->topicRepository->setFirstFloor($command->topic_id,$command->reply_id);
        }

        return $reply;
    }
}