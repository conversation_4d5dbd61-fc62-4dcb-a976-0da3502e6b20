<?php namespace Topic\Domain\Command;

use Nt\Assert\Assert;

/**
 * Class BlockTopicCommand
 *
 * @package Topic\Domain\Command
 */
class DeleteTopicCommand {

    public $topic_id;
    public $user_id;

    /**
     * BlockTopicCommand constructor.
     *
     * @param     $topic_id
     * @param     $user_id
     */
    public function __construct( $topic_id, $user_id )
    {
        $this->topic_id = (int)$topic_id;
        $this->user_id = (int)$user_id;
    }

    public function validate()
    {
        Assert::greaterThan($this->topic_id,0);
        Assert::greaterThan($this->user_id,0);
    }
}