<?php namespace Topic\Domain\Command;



use Nt\Assert\Assert;

class AddTopicReplyCommand {

    public $topic_id;
    public $reply_id;
    public $reply_at;
    public $has_resource;
    public $floor_num;

    /**
     * AddTopicReplyCommand constructor.
     *
     * @param $topic_id
     * @param $reply_id
     * @param $time
     * @param $floor_num
     *
     */
    public function __construct( $topic_id, $reply_id, $time, $floor_num )
    {
        $this->topic_id     = (int)$topic_id;
        $this->reply_id     = (int)$reply_id;
        $this->reply_at     = (int)$time;
        $this->has_resource = 0;
        $this->floor_num = $floor_num;
    }


    public function validate()
    {
        Assert::greaterThan($this->topic_id,0);
        Assert::greaterThan($this->reply_id,0);
//        Assert::oneOf($this->has_resource,[0,1]);
    }
}