<?php namespace Topic\Domain\Model\Follower;

class FollowStatus {

    const FOLLOWED  = 1;

    const UN_FOLLOWED = 0;

    protected $status;

    /**
     * TopicStatus constructor.
     *
     * @param $status
     */
    private function __construct($status)
    {
        $this->status = $status;
    }

    public static function followed()
    {
        return new self( self::FOLLOWED );
    }

    public static function unFollowed()
    {
        return new self(self::UN_FOLLOWED);
    }


    public function equalsTo( self $aStatus )
    {
        return $this->status === $aStatus->status();
    }

    public function status()
    {
        return $this->status;
    }

    public function __toString()
    {
        return (string)$this->status;
    }

}