<?php namespace Topic\Domain\Model\Follower;

class Follower {
    protected $user_id;
    protected $topic_id;
    protected $followAt;

    /**
     * @var FollowStatus
     */
    protected $status;

    /**
     * Follower constructor.
     *
     * @param $user_id
     * @param $topic_id
     */
    public function __construct( $user_id, $topic_id )
    {
        $this->user_id  = $user_id;
        $this->topic_id = $topic_id;
        $this->followAt = new \DateTimeImmutable();
    }

    /**
     * @param $topic_id
     * @param $user_id
     * @return static
     */
    public static function followTopic( $topic_id, $user_id )
    {
        return new static(
            $user_id,
            $topic_id
        );
    }

    /**
     * @return array
     */
    public function toArray()
    {
        return [
            'topic_id' => $this->topic_id,
            'user_id' => $this->user_id,
            'follow_at' => $this->followAt->getTimestamp(),
        ];
    }

    /**
     * @param $data
     * @return static
     */
    public static function fromData( $data )
    {
        if(!$data){
            return null;
        }

        return new static(
            $data['user_id'],
            $data['topic_id']
        );
    }

}