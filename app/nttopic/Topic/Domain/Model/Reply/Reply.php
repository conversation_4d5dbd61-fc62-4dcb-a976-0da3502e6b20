<?php namespace Topic\Domain\Model\Reply;

class Reply {

    const NORMAL  = 1;

    const DELETED = 2;

    protected $topic_id;
    protected $reply_id;
    protected $reply_at;
    protected $has_resource;
    protected $rank;
    protected $status;

    /**
     * @var array
     */
    protected $_dataModified = [];

    /**
     * Reply constructor.
     *
     * @param     $topic_id
     * @param     $reply_id
     * @param     $reply_at
     * @param     $has_resource
     * @param int $rank
     * @param int $status
     */
    public function __construct( $topic_id, $reply_id, $reply_at, $has_resource, $rank = 0, $status = self::NORMAL )
    {
        $this->topic_id      = $topic_id;
        $this->reply_id      = $reply_id;
        $this->reply_at      = $reply_at;
        $this->has_resource  = $has_resource;
        $this->rank          = $rank;
        $this->status        = $status;
        $this->_dataModified = [];
    }

    /**
     * @return mixed
     */
    public function getTopicId()
    {
        return $this->topic_id;
    }

    /**
     * @param mixed $topic_id
     */
    public function setTopicId( $topic_id )
    {
        $this->topic_id = $topic_id;
    }

    /**
     * @return mixed
     */
    public function getReplyId()
    {
        return $this->reply_id;
    }

    /**
     * @param mixed $reply_id
     */
    public function setReplyId( $reply_id )
    {
        $this->reply_id = $reply_id;
    }

    /**
     * @return array
     */
    public function getDataModified()
    {
        return $this->_dataModified;
    }

    /**
     * @param array $dataModified
     */
    public function setDataModified( $dataModified )
    {
        $this->_dataModified = $dataModified;
    }

    /**
     * @param     $rank
     * @param int $has_resource
     */
    public function setRank( $rank, $has_resource = 0 )
    {
        $this->_dataModified['rank']         = $rank;
        $this->_dataModified['has_resource'] = (int)( $has_resource !== 0 );
    }

    /**
     * @return mixed
     */
    public function getHasResource()
    {
        return $this->has_resource;
    }

    /**
     * @param mixed $has_resource
     */
    public function setHasResource( $has_resource )
    {
        $this->has_resource = $has_resource;
    }

    /**
     * @param $topic_id
     * @param $reply_id
     * @param $reply_at
     * @param $has_resource
     *
     * @return static
     */
    public static function reply( $topic_id, $reply_id, $reply_at, $has_resource )
    {
        return new static(
            $topic_id,
            $reply_id,
            $reply_at,
            $has_resource
        );
    }

    /**
     * @return int
     */
    public function getRank()
    {
        return $this->rank;
    }

    /**
     * @return array
     */
    public function toArray()
    {
        return [
            'topic_id'     => $this->getTopicId(),
            'reply_id'     => $this->getReplyId(),
            'rank'         => $this->getRank(),
            'has_resource' => $this->getHasResource(),
            'reply_at'     => $this->getReplyAt(),
            'status'       => $this->getStatus(),
        ];
    }

    /**
     * @param $data
     *
     * @return static
     */
    public static function fromData( $data )
    {
        if ( !$data ) {
            return null;
        }
        $reply = new static(
            $data['topic_id'],
            $data['reply_id'],
            $data['reply_at'],
            $data['has_resource'],
            $data['rank'],
            $data['status']
        );
        return $reply;
    }

    /**
     * @return mixed
     */
    public function getReplyAt()
    {
        return $this->reply_at;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus( $status )
    {
        $this->status = $status;
    }

}