<?php namespace Topic\Domain\Model\Topic;

use Nt\Assert\Assert;
use Nt\Commander\Events\EventGenerator;
use Topic\Domain\Event\TopicSetPrivate;
use Topic\Domain\Event\TopicWasBlocked;
use Topic\Domain\Event\TopicWasPassed;
use Topic\Domain\Event\TopicWasPublished;
use Topic\Domain\Event\TopicWasRecovered;

class Topic {

    const MIN_TITLE_LENGTH = 5;

    const MAX_TITLE_LENGTH = 30;

    const MIN_DESC_LENGTH  = 0;

    const MAX_DESC_LENGTH  = 100;

    const MIN_TAG_COUNT    = 1;

    const MAX_TAG_COUNT    = 5;

    /**
     * @var integer
     */
    protected $id;

    /**
     * @var TopicId
     */
    protected $topicId;
    /**
     * @var string
     */
    protected $title;
    /**
     * @var string
     */
    protected $desc;
    /**
     * @var integer[]
     */
    protected $tags;
    /**
     * @var integer
     */
    protected $reply_num;
    /**
     * @var integer
     */
    protected $collect_num;

    /**
     * @var TopicStatus
     */
    protected $status;
    /**
     * @var integer
     */
    protected $user_id;

    protected $private;
    /**
     * @var \DateTimeImmutable
     */
    protected $publishAt;
    //    /**
    //     * @var \DateTimeImmutable
    //     */
    //    protected $updateAt;
    /**
     * @var array
     */
    protected $_dataModified = [];

    protected $tagInfo;
    /**
     * @var string
     */
    private $cover;
    /**
     * @var int
     */
    private $floor_num;
    /**
     * @var int
     */
    private $first_floor;

    private $is_recommend;

    /**
     * Topic constructor.
     *
     * @param TopicId    $topicId
     * @param string     $title
     * @param string     $desc
     * @param            $user_id
     * @param \integer[] $tags
     * @param string     $cover
     * @param int        $reply_num
     * @param int        $collect_num
     * @param int        $floor_num
     * @param int        $first_floor
     * @param int        $private
     */
    public function __construct( TopicId $topicId, $title, $desc, $user_id, array $tags, $cover = '', $reply_num = 0, $collect_num = 0, $floor_num = 0, $first_floor = 0, $private = 0 )
    {
        $this->topicId     = $topicId;
        $this->user_id     = $user_id;
        $this->reply_num   = $reply_num;
        $this->collect_num = $collect_num;
        $this->setTitle( $title );
        $this->setDesc( $desc );
        $this->setTags( $tags );
        $this->status      = TopicStatus::reviewed();
        $this->publishAt   = new \DateTimeImmutable();
        $this->cover       = $this->formatCoverPic($cover);
        $this->floor_num   = $floor_num;
        $this->first_floor = $first_floor;
        $this->private     = $private;
    }

    /**
     * @return int
     */
    public function getFloorNum()
    {
        return $this->floor_num;
    }

    /**
     * @param int $floor_num
     */
    public function setFloorNum( $floor_num )
    {
        $this->floor_num = $floor_num;
    }

    /**
     * @return TopicId
     */
    public function getTopicId()
    {
        return $this->topicId;
    }

    /**
     * @param TopicId $topicId
     */
    public function setTopicId( $topicId )
    {
        $this->topicId = $topicId;
    }

    /**
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @param string $title
     */
    public function setTitle( $title )
    {
        $title = trim( $title );
        $title = $this->filterEmoji($title);
        Assert::lengthBetween( $title, self::MIN_TITLE_LENGTH, self::MAX_TITLE_LENGTH );
        $this->title = $title;
    }

    /**
     * @return string
     */
    public function getDesc()
    {
        return $this->desc;
    }

    /**
     * @param string $desc
     */
    public function setDesc( $desc )
    {
        $desc = trim( $desc );
        if ( $desc ) {
            Assert::lengthBetween( $desc, self::MIN_DESC_LENGTH, self::MAX_DESC_LENGTH );
        }
        $this->desc = $desc;
    }

    /**
     * @return \integer[]
     */
    public function getTags()
    {
        return $this->tags;
    }

    /**
     * @param array $tags
     *
     * @throws \InvalidArgumentException
     */
    public function setTags( $tags )
    {
        $tagCount = count( $tags );
        if ( $tagCount < self::MIN_TAG_COUNT || $tagCount > self::MAX_TAG_COUNT ) {
            throw new \InvalidArgumentException(
                sprintf( 'tag:%s need to has size in (%d,%d)',
                         json_encode( $tags ), self::MIN_TAG_COUNT, self::MAX_TAG_COUNT )
            );
        }
        $this->tags = $tags;
    }

    /**
     * @return int
     */
    public function getReplyNum()
    {
        return $this->reply_num;
    }

    /**
     * @param int $reply_num
     */
    public function setReplyNum( $reply_num )
    {
        $this->reply_num = $reply_num;
    }

    /**
     * @return TopicStatus
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param TopicStatus $status
     */
    public function setStatus( $status )
    {
        $this->status = $status;
    }

    /**
     * @return int
     */
    public function getUserId()
    {
        return $this->user_id;
    }

    /**
     * @param int $user_id
     */
    public function setUserId( $user_id )
    {
        $this->user_id = $user_id;
    }

    /**
     * @return \DateTimeImmutable
     */
    public function getPublishAt()
    {
        return $this->publishAt;
    }

    /**
     * @param \DateTimeImmutable $publishAt
     */
    public function setPublishAt( $publishAt )
    {
        $this->publishAt = $publishAt;
    }

    /**
     * @return array
     */
    public function getDataModified()
    {
        return $this->_dataModified;
    }

    use EventGenerator;

    /**
     * 新建话题
     *
     * @param $topicId
     * @param $title
     * @param $desc
     * @param $user_id
     * @param $tags
     * @param $cover
     * @param $private
     *
     * @return static
     */
    public static function publish( $topicId, $title, $desc, $user_id, $tags, $cover, $private )
    {
        $topic = new static(
            $topicId,
            $title,
            $desc,
            $user_id,
            $tags,
            $cover,
            $private
        );
        $topic->raise( new TopicWasPublished( $topic ) );
        return $topic;
    }

    /**
     * 过滤掉emoji表情
     * @param $str
     *
     * @return mixed
     */
    function filterEmoji($str)
    {
        $str = preg_replace_callback(
            '/./u',
            function (array $match) {
                return strlen($match[0]) >= 4 ? '' : $match[0];
            },
            $str);

        return $str;
    }
    /**
     * @param $data
     *
     * @return static
     * @throws \InvalidArgumentException
     */
    public static function fromData( $data )
    {
        if ( !$data ) {
            return null;
        }
        $topic = new static(
            new TopicId( $data['topic_id'] ),
            $data['title'],
            $data['desc'],
            $data['user_id'],
            json_decode( $data['tags'] ),
            (array)json_decode( $data['cover'] ),
            $data['reply_num'],
            $data['collect_num'],
            $data['floor_num'],
            $data['first_floor']
        );
        $topic->setStatus( TopicStatus::from( $data['status'] ) );
        $topic->setIsRecommend((int)$data['is_recommend']);
        $topic->setPublishAt(new \DateTimeImmutable(date('YmdHis',$data['publish_at'])));
        return $topic;
    }

    /**
     * @return bool
     */
    public function isBlocked()
    {
        return $this->status->equalsTo( TopicStatus::blocked() );
    }

    /**
     * @return bool
     */
    public function isDeleted()
    {
        return $this->status->equalsTo( TopicStatus::deleted() );
    }

    /**
     * @return bool
     */
    public function isRecommended()
    {
        return $this->is_recommend == 1;
    }

    /**
     * @return bool
     */
    public function isNormal()
    {
        return $this->status->equalsTo( TopicStatus::normal() );
    }

    /**
     * 封禁
     */
    public function doBlock()
    {
        $this->_dataModified['status'] = TopicStatus::blocked()->status();
        $this->raise( new TopicWasBlocked( $this ) );
    }

    /**
     * 删除
     */
    public function doDelete()
    {
        $this->_dataModified['status'] = TopicStatus::deleted()->status();
        // 发送的nmq和封禁的nmq一致
        $this->raise( new TopicWasBlocked( $this ) );
    }

    /**
     * 通过话题
     */
    public function doPass()
    {
        $this->_dataModified['status'] = TopicStatus::normal()->status();
        $this->raise( new TopicWasPassed( $this ) );
    }

    /**
     * 修改封面
     * @param $cover
     */
    public function putCover( $cover )
    {
        $this->_dataModified['cover'] = $this->formatCoverPic($cover);
    }

    /**
     * 不通过话题
     */
    public function doUnPass()
    {
        $this->_dataModified['status'] = TopicStatus::reviewed()->status();
        $this->raise( new TopicWasBlocked( $this ) );
    }

    /**
     * 推荐
     */
    public function doRecommend()
    {
        $this->_dataModified['is_recommend'] = 1;
    }

    /**
     * 取消推荐
     */
    public function doUnRecommend()
    {
        $this->_dataModified['is_recommend'] = 0;
    }

    /**
     * 取消推荐
     *
     * @param array $tags
     */
    public function doSetTags(array $tags)
    {
        $this->_dataModified['tags'] = json_encode($tags);
    }

    /**
     * 解禁
     */
    public function recover()
    {
        $this->_dataModified['status'] = TopicStatus::normal()->status();
        $this->raise( new TopicWasRecovered( $this ) );
    }

    /**
     * 插入数据库用
     *
     * @return array
     */
    public function toArray()
    {
        $field = [
            'topic_id'    => $this->topicId->id(),
            'user_id'     => $this->user_id,
            'title'       => $this->title,
            'description' => $this->desc,
            'tags'        => json_encode( $this->tags ),
            'status'      => $this->status->status(),
            'publish_at'  => $this->publishAt->getTimestamp(),
            'cover'       => $this->cover,
            'private'     => (int)$this->private,
        ];
        return $field;
    }

    /**
     * @return int
     */
    public function getCollectNum()
    {
        return $this->collect_num;
    }

    /**
     * @param int $collect_num
     */
    public function setCollectNum( $collect_num )
    {
        $this->collect_num = $collect_num;
    }

    /**
     * @return mixed
     */
    public function getTagInfo()
    {
        return $this->tagInfo;
    }

    /**
     * @param mixed $tagInfo
     */
    public function setTagInfo( $tagInfo )
    {
        $this->tagInfo = $tagInfo;
    }

    /**
     * @return mixed
     */
    public function getIsRecommend()
    {
        return $this->is_recommend;
    }

    /**
     * @param mixed $is_recommend
     */
    public function setIsRecommend( $is_recommend )
    {
        $this->is_recommend = $is_recommend;
    }

    /**
     * @param $pic
     *
     * @return string
     */
    public function formatCoverPic($pic){
//        \Bingo_Log::warning(var_export($pic,1));
//        $pattern = '/http:\/\/imgsrc.*?\//i';
//        $urlPrefix = 'https://imgsa.baidu.com/';

        if( empty($pic) ){
            return '{}';
        }
        if( !is_array($pic)){
            $pic = json_decode($pic,1);
            if (!$pic){
                return '{}';
            }
        }

//        if( !empty($pic['big_url']) ){
//            $big_url = preg_replace($pattern, $urlPrefix, $pic['big_url']);
//            $pic['big_url'] = $big_url;
//        }
//        if( !empty($pic['water_url']) ){
//            //$water_url = preg_replace($pattern, $urlPrefix, $pic['water_url']);
//            $pic['water_url'] = '';
//        }
//        if( !empty($pic['small_url']) ){
//            $small_url = preg_replace($pattern, $urlPrefix, $pic['small_url']);
//            $pic['small_url'] = $small_url;
//        }
        return json_encode($pic);
    }

    /**
     * @return int
     */
    public function getPrivate()
    {
        return $this->private;
    }

    /**
     * @param int $private
     */
    public function setPrivate( $private )
    {
        $this->_dataModified['private'] = $private;
        $this->private = $private;
        $this->raise(new TopicSetPrivate($this));
//        if ($private == 1){
//            $this->raise(TopicWasBlocked::class);
//        }else {
//            $this->raise(TopicWasRecovered::class);
//        }
    }

}