<?php
/**
 * Created by PhpStorm.
 * User:
 * Date: 9/15/14
 * Time: 10:33 PM
 */
// 需要兼容原来的接口嘛
abstract class  Dl_Db_Model {
    
    private static $_mysql_operator_white_list = array(
        "+=",
        "-=",
        "|=",
        "&=",
        ">",
        "<",
        ">=",
        "<=",
        "<>",
        "in",
        "like",
        "not in",
    );

    protected $error = null;
    protected $errno = null;
    protected $data  = null;
    /**
     * @type Tieba_Mysql
     */
    protected $db      = null;
    protected $table   = null;
    protected $db_name = null;

    /**
     * Dl_Db_Model constructor.
     *
     * @param null $model
     * @param null $dbname
     */
    public function __construct( $model = null, $dbname = null) {
        if(is_null($model)) {
            $model = $this->getModel();
        }
        if(is_null($dbname)) {
            $dbname = $this->getDbname();
        }

        $this->table   = $model;
        $this->db_name = $dbname;
        
        $this->getObjDB();
    }

    // 插入数据前的回调方法
    /**
     * @return bool
     */
    protected function _before_insert() {
        return true;
    }

    /**
     * @return bool
     */
    protected function _after_insert() {
        return true;
    }

    /**
     * @return bool
     */
    protected function _before_select() {
        return true;
    }

    /**
     * @return bool
     */
    protected function _after_select() {
        return true;
    }

    /**
     * @return bool
     */
    protected function _before_update() {
        return true;
    }

    /**
     * @return bool
     */
    protected function _after_update() {
        return true;
    }

    /**
     * @brief    : dl接口
     * @param array $arrInput
     * @return array : $arrOutput
     */
    public function startTransaction( $arrInput = array()) {
        if ( isset( $arrInput['db'] ) ) {
            $db = $arrInput['db'];
        } else {
            $db = $this->getObjDB();
        }
        if ( is_null( $db ) ) {
            return $this->errRet( Tieba_Errcode::ERR_DB_CONN_FAIL );
        }
        $ret = $db->startTransaction();
        Bingo_Log::pushNotice( 'sql', $db->getLastSQL() );
        if ( $ret <= 0 ) {
            Bingo_Log::warning( "[output:" . serialize( $ret ) . "error:" . $db->error() . "sql:" . $db->getLastSQL() . "]" );

            return $this->errRet( Tieba_Errcode::ERR_DB_QUERY_FAIL, array( 'mysql_errno' => $db->errno() ) );
        }

        return $this->errRet( Tieba_Errcode::ERR_SUCCESS );
    }

    /**
     * @brief  : dl接口
     * @param  : $arrInput
     * @return : $arrOutput
     **/
    public function getAffectedRows( $arrInput ) {
        if ( isset( $arrInput['db'] ) ) {
            $db = $arrInput['db'];
        } else {
            $db = $this->getObjDB();
        }
        if ( is_null( $db ) ) {
            return $this->errRet( Tieba_Errcode::ERR_DB_CONN_FAIL );
        }
        $affected_rows = $db->getAffectedRows();
        
        Bingo_Log::pushNotice('getAffectedRows', $affected_rows);

        return $this->errRet( Tieba_Errcode::ERR_SUCCESS, $affected_rows );
    }

    /**
     * @brief  : dl接口
     * @param  : array $arrInput
     * @return : $arrOutput
     **/
    public function commit( $arrInput=array() ) {
        if ( isset( $arrInput['db'] ) ) {
            $db = $arrInput['db'];
        } else {
            $db = $this->getObjDB();
        }
        if ( is_null( $db ) ) {
            return $this->errRet( Tieba_Errcode::ERR_DB_CONN_FAIL );
        }
        $ret = $db->commit();
        Bingo_Log::pushNotice('sql', 'commit');
        
        if ( $ret <= 0 ) {
            Bingo_Log::warning( "[output:" . serialize( $ret ) . "error:" . $db->error() . "sql:" . $db->getLastSQL() . "]" );

            return $this->errRet( Tieba_Errcode::ERR_DB_QUERY_FAIL, array( 'mysql_errno' => $db->errno() ) );
        }

        return $this->errRet( Tieba_Errcode::ERR_SUCCESS );
    }

    /**
     * @brief  : dl接口
     * @param  : $arrInput
     * @return : $arrOutput
     **/
    public function rollBack( $arrInput = array()) {
        if ( isset( $arrInput['db'] ) ) {
            $db = $arrInput['db'];
        } else {
            $db = $this->getObjDB();
        }
        if ( is_null( $db ) ) {
            return $this->errRet( Tieba_Errcode::ERR_DB_CONN_FAIL );
        }
        $ret = $db->rollback();
        Bingo_Log::pushNotice('sql', 'ROLLBACK');
        
        if ( $ret <= 0 ) {
            Bingo_Log::warning( "[output:" . serialize( $ret ) . "error:" . $db->error() . "sql:" . $db->getLastSQL() . "]" );

            return $this->errRet( Tieba_Errcode::ERR_DB_QUERY_FAIL, array( 'mysql_errno' => $db->errno() ) );
        }

        return $this->errRet( Tieba_Errcode::ERR_SUCCESS );
    }

    /**
     * @brief : dl接口
     * @param : $arrInput
     * @return array : $arrOutput
     */
    public function insert( $arrInput ) {
        if ( false === $this->_before_insert( $arrInput ) ) {
            return $this->errRet();
        }

        if ( !isset( $arrInput['field'] ) || !is_array( $arrInput['field'] ) ) {
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );

            return $this->errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        if ( isset( $arrInput['table'] ) ) {
            $this->table = $arrInput['table'];
        }

        if ( isset( $arrInput['db'] ) ) {
            $db = $arrInput['db'];
        } else {
            $db = $this->getObjDB();
        }
        if ( is_null( $db ) ) {
            return $this->errRet( Tieba_Errcode::ERR_DB_CONN_FAIL );
        }
        $fields   = $arrInput['field'];
        $strOnDup = null;
        if ( isset( $arrInput['onDup'] ) && is_array( $arrInput['onDup'] ) ) {
            foreach ( $arrInput['onDup'] as $key => $value ) {
                if ( $strOnDup !== null ) {
                    $strOnDup .= ",";
                }
                $key = mysql_escape_string( trim( $key ) );
                if ( is_int( $value ) ) {
                    $strOnDup .= "$key=$value ";
                } else if ( is_string( $value ) ) {
                    $value = mysql_escape_string( trim( $value ) );
                    $strOnDup .= "$key='$value' ";
                } else if ( is_array( $value ) ) {
                    $val    = $value['val'];
                    $opt    = in_array( strval( $value['opt'] ),
                                        self::$_mysql_operator_white_list ) ? strval( $value['opt'] ) : '=';
                    $quotes = isset( $value['quotes'] ) ? 0 : 1;

                    if ( is_int( $val ) ) {
                        $strOnDup .= "$key $opt $val ";
                    } else {
                        $val = mysql_escape_string( trim( $val ) );
                        if ( $quotes === 0 ) {
                            $strOnDup .= "$key $opt $val ";
                        } else {
                            $strOnDup .= "$key $opt '$val' ";
                        }
                    }

                }
            }
        }
        $ret = $db->insert( $this->table, $fields, null, $strOnDup );
        Bingo_Log::pushNotice('sql', $db->getLastSQL());
        Bingo_Log::warning('wc === sql: ' . $db->getLastSQL());

        if ( $ret <= 0 ) {
            Bingo_Log::warning( "[output:" . serialize( $ret ) . "error:" . $db->error() . "sql:" . $db->getLastSQL() . "]" );

            return $this->errRet( Tieba_Errcode::ERR_DB_QUERY_FAIL, array( 'mysql_errno' => $db->errno() ) );
        }
        $insertId = $db->getInsertID();
        //$this->errRet(Tieba_Errcode::ERR_SUCCESS,$insertId);
        $this->errno           = Tieba_Errcode::ERR_SUCCESS;
        $this->data[]          = $insertId;
        $arrInput['insert_id'] = $insertId;
        $arrInput['table']     = $this->table;
        $this->_after_insert( $arrInput );

        return $this->errRet( Tieba_Errcode::ERR_SUCCESS, $insertId );
    }

    /**
     * @brief  : dl接口
     * @param  : $arrInput
     * @return : $arrOutput
     **/
    public function select( $arrInput ) {

        if ( !isset( $arrInput['field'] ) ) {
            $arrInput['field'] = static::getFields();
        }

        if ( false === $this->_before_select( $arrInput ) ) {
            return $this->errRet();
        }

        if ( !isset( $arrInput['field'] ) || !is_array( $arrInput['field'] ) ) {
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );

            return $this->errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }

        if ( isset( $arrInput['table'] ) ) {
            $this->table = $arrInput['table'];
        }

        $arrFields = $arrInput['field'];
        $str_cond  = null;
        if ( isset( $arrInput['cond'] ) && is_array( $arrInput['cond'] ) ) {
            foreach ( $arrInput['cond'] as $key => $value ) {
                if ( $str_cond !== null ) {
                    $str_cond .= " and ";
                }

                $key = mysql_escape_string( trim( $key ) );
                if ( is_int( $value ) ) {
                    $str_cond .= "$key=$value ";
                } else if ( is_string( $value ) ) {
                    $value = mysql_escape_string( trim( $value ) );
                    $str_cond .= "$key='$value' ";
                } else if ( is_array( $value ) ) {
                    $val    = $value['val'];
                    $opt    = in_array( strval( $value['opt'] ),
                                        self::$_mysql_operator_white_list ) ? strval( $value['opt'] ) : '=';
                    $quotes = isset( $value['quotes'] ) ? 0 : 1;

                    if ( is_int( $val ) ) {
                        $str_cond .= "$key $opt $val ";
                    } else {
                        $val = mysql_escape_string( trim( $val ) );
                        if ( $quotes === 0 ) {
                            $str_cond .= "$key $opt $val ";
                        } else {
                            $str_cond .= "$key $opt '$val' ";
                        }
                    }

                }

            }
        }

        $str_append = null;
        if ( isset( $arrInput['append'] ) ) {
            $str_append = $arrInput['append'];
        }

        if ( isset( $arrInput['db'] ) ) {
            $db = $arrInput['db'];
        } else {
            $db = $this->getObjDB();
        }
        if ( is_null( $db ) ) {
            return $this->errRet( Tieba_Errcode::ERR_DB_CONN_FAIL );
        }

        $ret = $db->select( $this->table, $arrFields, $str_cond, null, $str_append );
        Bingo_Log::pushNotice('sql', $db->getLastSQL());
        Bingo_Log::warning('wc:' . $db->getLastSQL());
        if ( $ret === false ) {
            Bingo_Log::warning( "[output:" . serialize( $ret ) . "error:" . $db->error() . "sql:" . $db->getLastSQL() . "]" );

            return $this->errRet( Tieba_Errcode::ERR_DB_QUERY_FAIL, array( 'mysql_errno' => $db->errno() ) );
        }
        $this->errno = Tieba_Errcode::ERR_SUCCESS;

        $this->_after_select( $arrInput );
        return $this->errRet( Tieba_Errcode::ERR_SUCCESS, $ret );
    }

    /**
     * @param $key
     * @param $ids
     * @return array
     */
    public function selectIn($key, $ids){

        /*
         * 防止SQL注入,强制转化int型
         */
        foreach($ids as $index => $value){
            $ids[$index] = intval($value);
        }

        return $this->select([
            'cond' => [
                $key => [
                    'val' => '(' . implode(',',$ids) . ')',
                    'opt' => 'in',
                    'quotes' => 0,
                ]
            ]
        ]);
    }

    /**
     * @brief  : dl接口
     * @param  : $arrInput
     * @return : $arrOutput
     **/
    public function update( $arrInput ) {
        if ( false === $this->_before_update( $arrInput ) ) {
            return $this->errRet();
        }
        if ( !isset( $arrInput['field'] ) || !is_array( $arrInput['field'] ) ) {
            Bingo_Log::warning( "input params invalid. [" . serialize( $arrInput ) . "]" );

            return $this->errRet( Tieba_Errcode::ERR_PARAM_ERROR );
        }
        if ( isset( $arrInput['table'] ) ) {
            $this->table = $arrInput['table'];
        }
        $str_field = null;
        foreach ( $arrInput['field'] as $key => $value ) {
            if ( $str_field !== null ) {
                $str_field .= ",";
            }
            $key = mysql_escape_string( trim( $key ) );
            if ( is_int( $value ) ) {
                $str_field .= "$key=$value ";
            } else if ( is_string( $value ) ) {
                $value = mysql_escape_string( trim( $value ) );
                $str_field .= "$key='$value' ";
            } else if ( is_array( $value ) ) {
                $val    = $value['val'];
                $opt    = in_array( strval( $value['opt'] ),
                                    self::$_mysql_operator_white_list ) ? strval( $value['opt'] ) : '=';
                $quotes = isset( $value['quotes'] ) ? 0 : 1; //默认有引号

                if ( is_int( $val ) ) {
                    $str_field .= "$key $opt $val ";
                } else {
                    $val = mysql_escape_string( trim( $val ) );
                    if ( $quotes === 0 ) {
                        $str_field .= "$key $opt $val ";
                    } else {
                        $str_field .= "$key $opt '$val' ";
                    }
                }

            }
        }
        $str_cond = null;
        if ( isset( $arrInput['cond'] ) && is_array( $arrInput['cond'] ) ) {
            foreach ( $arrInput['cond'] as $key => $value ) {
                if ( $str_cond !== null ) {
                    $str_cond .= " and ";
                }
                $key = mysql_escape_string( trim( $key ) );
                if ( is_int( $value ) ) {
                    $str_cond .= "$key=$value ";
                } else if ( is_string( $value ) ) {
                    $value = mysql_escape_string( trim( $value ) );
                    $str_cond .= "$key='$value' ";
                } else if ( is_array( $value ) ) {
                    $val    = $value['val'];
                    $opt    = in_array( strval( $value['opt'] ),
                                        self::$_mysql_operator_white_list ) ? strval( $value['opt'] ) : '=';
                    $quotes = isset( $value['quotes'] ) ? 0 : 1;

                    if ( is_int( $val ) ) {
                        $str_cond .= "$key $opt $val ";
                    } else {
                        $val = mysql_escape_string( trim( $val ) );
                        if ( $quotes === 0 ) {
                            $str_cond .= "$key $opt $val ";
                        } else {
                            $str_cond .= "$key $opt '$val' ";
                        }
                    }

                }
            }
        }

        $str_append = null;
        if ( isset( $arrInput['append'] ) ) {
            $str_append = $arrInput['append'];
        }

        if ( isset( $arrInput['db'] ) ) {
            $db = $arrInput['db'];
        } else {
            $db = $this->getObjDB();
        }
        if ( is_null( $db ) ) {
            return $this->errRet( Tieba_Errcode::ERR_DB_CONN_FAIL );
        }

        $ret = $db->update( $this->table, $str_field, $str_cond );
        Bingo_Log::pushNotice('sql', $db->getLastSQL());
        Bingo_Log::warning('wc===sql' . $db->getLastSQL());
        if ( $ret === false ) {
            Bingo_Log::warning( "[output:" . serialize( $ret ) . "error:" . $db->error() . "sql:" . $db->getLastSQL() . "]" );

            return $this->errRet( Tieba_Errcode::ERR_DB_QUERY_FAIL, array( 'mysql_errno' => $db->errno() ) );
        }
        $affectedRows = $db->getAffectedRows();

        $this->errno  = Tieba_Errcode::ERR_SUCCESS;
        $this->data[] = $affectedRows;

        $this->_after_update( $arrInput );

        return $this->errRet(Tieba_Errcode::ERR_SUCCESS,$affectedRows);
    }

    /**
     * @param null   $errno
     * @param string $data
     * @param null   $error
     * @return array
     */
    protected function errRet( $errno = null, $data = "", $error = null ) {
        if ( !is_null( $errno ) ) {
            $this->errno = $errno;
        }

        if ( is_null( $error ) ) {
            if ( is_null( $this->error ) ) {
                $this->error = Tieba_Error::getErrmsg( $this->errno );
            }
        } else {
            $this->error = $error;
        }

        $arrRet = array(
            'errno'  => $this->errno,
            'errmsg' => $this->error,
        );
        if ( $data !== "" ) {
            $arrRet['data'] = $data;
        }
        Bingo_Log::pushNotice( "errno", $this->errno );

        return $arrRet;
    }

    /**
     * @param string $charset
     * @return Bd_DB|null|Tieba_Mysql
     */
    protected function getObjDB($charset='utf8') {
        if($this->db){
            return $this->db;
        }
        $dbname     = $this->db_name;
        $objTbMysql = Tieba_Mysql::getDB( $dbname );
        $this->db   = $objTbMysql;
        if ( $objTbMysql && $objTbMysql->isConnected() ) {
            $objTbMysql->charset($charset);
            return $objTbMysql;
        } else {
            Bingo_Log::warning( "db connect fail." );

            return null;
        }
    }

    /**
     * @return Tieba_Mysql
     */
    public function getMysqlObj() {
        return $this->db;
    }

    /**
     * @param Tieba_Mysql $db
     * @return $this
     */
    public function setMysqlObj( Tieba_Mysql $db ) {
        $this->db = $db;

        return $this;
    }

    /**
     * @param $dbFunc
     * @param $arrInput
     * @return mixed
     */
    public function __call( $dbFunc, $arrInput ) {
        if ( empty( $arrInput ) ) {
            return $this->db->$dbFunc();
        } else {
            return $this->db->$dbFunc( $arrInput[0] );
        }
    }

    /**
     * @return mixed
     */
    public abstract function getFields();

    /**
     * @return mixed
     */
    public abstract function getModel();

    /**
     * @return mixed
     */
    public abstract function getDbname();
}
