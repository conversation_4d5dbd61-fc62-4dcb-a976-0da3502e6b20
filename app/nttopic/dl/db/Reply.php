<?php

class Dl_Db_Reply extends Dl_Db_Model{

    public static $_db_table = 'reply';
    public static $_db_name = 'forum_nttopic';

    public static $fields = [
        'topic_id',
        'reply_id',
        'rank',
        'has_resource',
        'status',
    ];


    /**
     * @return array
     */
    public function getFields()
    {
        return self::$fields;
    }

    /**
     * @return string
     */
    public function getModel()
    {
        return self::$_db_table;
    }

    /**
     * @return string
     */
    public function getDbname()
    {
        return self::$_db_name;
    }

    /**
     * 所有更新都会修改更新时间
     * @param $arrInput
     * @return mixed
     */
    protected function _before_update( &$arrInput) {
        $arrInput['field']['update_at'] = Bingo_Timer::getNowTime();
        return $arrInput;
    }
}