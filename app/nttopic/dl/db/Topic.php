<?php

class Dl_Db_Topic extends Dl_Db_Model{

    public static $_db_table = 'topic';
    public static $_db_name = 'forum_nttopic';

    public static $fields = [
        'topic_id',
        'user_id',
        'title',
        'description',
        'tags',
        'status',
        'cover',
        'reply_num',
        'collect_num',
        'first_floor', // 第一楼层的回复id
        'is_recommend',// 是否推荐
        'floor_num',
        'publish_at',
        'update_at',
        'private', // 隐私
    ];

    /**
     * @return array
     */
    public function getFields()
    {
        return self::$fields;
    }

    /**
     * @return string
     */
    public function getModel()
    {
        return self::$_db_table;
    }

    /**
     * @return string
     */
    public function getDbname()
    {
        return self::$_db_name;
    }

    /**
     * 所有更新都会修改更新时间
     * @param $arrInput
     * @return mixed
     */
    protected function _before_update( &$arrInput) {
        $arrInput['field']['update_at'] = Bingo_Timer::getNowTime();
        return $arrInput;
    }
}