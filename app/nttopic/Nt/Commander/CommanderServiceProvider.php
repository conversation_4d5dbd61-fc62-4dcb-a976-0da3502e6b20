<?php namespace Nt\Commander;

//use Illuminate\Support\ServiceProvider;


use Nt\Container\Container;
use Nt\Support\ServiceProvider;

class CommanderServiceProvider implements ServiceProvider {

    /**
     * @var Container
     */
    protected $app;

    /**
     * CommanderServiceProvider constructor.
     *
     * @param $app
     */
    public function __construct($app)
    {
        $this->app = $app;
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->registerCommandTranslator();

        $this->registerCommandBus();

    }


    /**
     * Register the command translator binding.
     */
    protected function registerCommandTranslator()
    {
        $this->app->bind('Nt\Commander\CommandTranslator', 'Nt\Commander\BasicCommandTranslator');
    }

    /**
     * Register the command bus implementation.
     */
    protected function registerCommandBus()
    {
        $this->app->bind('Nt\Commander\CommandBus', function($app)
        {
            return $app->make('Nt\Commander\DefaultCommandBus');
        });
    }

}
