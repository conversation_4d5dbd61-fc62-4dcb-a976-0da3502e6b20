<?php namespace Nt\Commander\Events;

use Nt\Commander\Events\Contracts\Dispatcher as DispatcherInterface;
use Nt\Events\Dispatcher;

//use Illuminate\Events\Dispatcher;
//use Illuminate\Log\Writer;


class EventDispatcher implements DispatcherInterface {

    /**
     * The Dispatcher instance.
     *
     * @var Dispatcher
     */
    protected $event;


    /**
     * Create a new EventDispatcher instance.
     *
     * @param Dispatcher $event
     */
    function __construct(Dispatcher $event)
    {
        $this->event = $event;
    }

    /**
     * Dispatch all raised events.
     *
     * @param array $events
     */
    public function dispatch(array $events)
    {
        foreach($events as $event)
        {
            $eventName = $this->getEventName($event);

            $this->event->fire($eventName, $event);
        }
    }

    /**
     * Make the fired event name look more object-oriented.
     *
     * @param $event
     * @return string
     */
    protected function getEventName($event)
    {
        return str_replace('\\', '.', get_class($event));
    }

} 